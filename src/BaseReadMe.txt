V1.0  20160409
	1. 初始发布
V1.0.1  20160507
	1、极光推送增加工具类JPushUtil 修改人：徐涛
V1.4  20160612
	1、极光推送工具类JPushUtil中方法增加返回值 修改人：徐涛
V1.5  
	1、极光推送工具类JPushUtil中区分IOS 产品模式与开发模式（徐涛 20160617）
V1.6 
	1、添加谷歌坐标转换百度
	2、修改GPS转百度的参数	
	3、 增加BASE64A保存文件的功能
V1.7
	1、增加二进制字节数组的Base64字符串保存成文件的功能（周伟伟 20160722）
V1.8 20160803
    1.增加IdcUtils18位身份证验证有效性方法。（徐涛 20160803）
    2.增加ProcessReqUtil处理Servlet参数工具类。（徐涛 20160808）
V1.9 
    1.日期工具类增加计算当前日期所在周的方法。（徐涛 20160811）
V1.10
	1.身份证工具类增加正则表达式验证。（徐涛 20160823）
	2.增加EchartsUtil(任杰20160823)    
V1.11
	1.新增年龄码表6003工具类
V1.13
	1.添加身份证15位验证。（齐然然 20160907）
	2.添加Property模糊搜索功能。（李炜 20160912）
V1.14
	1.身份证工具类新增18位与15位身份证转换方法。（徐涛 20160923）
V1.15
	1.修改日期工具类获取日期的第一天方法（齐然然20161101）
V1.16
	1.新增微信公众号相关工具类（任杰20161108）	
v1.17
	1.StringUtils增加uuid方法（周伟伟20161123）
V1.18
	1.JsfUtil新增request解析ip方法(任杰20161124)	
V1.19
	1.StringUtils添加方法isNumber用来验证是否为小数(李炜20161124)
	2.StringUtils新增验证组织机构代码及ProcessReqUtil新增解析上传压缩Json字符串(徐涛 20161125)
	3.ObjectCopyUtil对象copy赋值。(徐涛 20161126)
V1.20
	1.新增http请求工具类HttpRequestUtil(任杰20161205)
	2.ObjectCopyUtil对象方法修改为copy父类方法(徐涛 20161209)
V1.22 
	1.ObjectCopyUtil对象方法修改为copy父类方法 增加是否空值也拷贝(徐涛 20161210)
V1.23
    1.日期工具类增加取得某天是月中的多少周、 获取当前月的最大天数、 获取当前月的最大天数方法（齐然然20161223） 
    2.xstreamJar包升级，忽略未知节点。(徐涛 20161223)
v1.24
	1.增加jdbc连接工具类JdbcUtil（周伟伟20170103）
	2.FileUtils增加根据文件地址获取文件内容方法getFileContentAbsPath（周伟伟20170103）
v1.25
	1.增加mybatis-mapping的包引入（周伟伟20170106）
	2.增加邮件发送的工具类（齐然然20170106） 
	3.增加邮件工具类jar包（齐然然20170109） 
v1.26
	1.FileUtils增加根据相对路径读取文件（周伟伟20170112）
	2.XstramUtil增加合并xml的功能（周伟伟20170112）
	3.增加ikeexpression的包导入，用于解析计算等表达式（周伟伟20170116）
v.1.27
	1.PostionUtil增加GPS84转经纬度方法（徐琦 20170301）
	2.增加OSS上传附件功能（孙明 20170411）
	3.FileBaseBean修改（孙明 20170418）
	4.FileUtils增加读取文件内容的方法
v.1.33
	合并base2.0内容如下（徐琦20170427）
	1. JSFUTIL增加判断是否存在session (周伟伟 20160520)
   	2. 增加读取xml文件转对象的方法  (周伟伟 20160520)  
	3.添加坐标系转换方法（李炜 20160707） 
	4.JSFUTIL增加获取Web站点根目录方法	getContextPath()(任杰 20161011)
	5.修改日期工具类获取日期的第一天方法（齐然然20161101）
	6.StringUtils添加方法isNumber用来验证是否为小数(李炜20161124)
	7.StringUtils添加生成UUID的公用方法(李炜20170122)
	8.增加身份证校验工具类（朱晓锋20170203）
	9.xstreamJar包升级，忽略未知节点。(李炜 20170206)
	10.XStreamUtils工具类从base迁移到base2。(李炜 20170206)
	11.ZipUtils工具类添加接口方法。(李炜 20170210)
	12.添加工具类HttpRequestUtil。(李炜 20170210)
	13.PropertyUtils添加方法getValuesByStartWith。(李炜 20170220)
	14.添加OSS上传附件工具(孙明 20170324)
	15.更新OSS视频转换工具(孙明 20170328)
	16.添加查询视频转换任务(孙明 20170329)
	17.添加复制对象工具类（徐琦20170410）	
	18.增加社会信用代码、组织机构代码验证（任杰20170410）	
	18.OSS大附件上传优化（孙明 20170516）
V1.36
	1.添加OSS附件key的认证（孙明 20170602）
	2.添加OSS获取文件头部（孙明 20170602）
v1.37
	1.BaiduMapUtil增加两点间距离计算方法（任杰20170710）
	2.OSSUtil添加获取附件方法（孙明20170724）	
v1.38
	1.报表插件增加大字段显示类型Rich。（徐涛20170802）
	2.OSSUtil添加附件上传方法（孙明20170801）
v1.39
	1.去除微信相关工具类，移至单独项目tools-wechat（任杰20170913）
	2.增加一个NumberText工具类，实现数字和中文转换等功能（马小朋20170918）
	3.OSSUtil添加附件上传方法（孙明20170801）
	4.FileUtils 添加上传、预览和压缩图片的方法（孙明20170921）
	5.ImageUtil添加压缩图片的方法（孙明20170921）
	6.OSS添加配置文件读取（孙明20170923）
v1.43
	1.fastReport生成xml修改（朱晓锋20170930）
v1.44
	1.DateUtil添加获取当前日期往后或者往前days的日期方法（20171014）
	2.修改jackson使用版本（李炜20171016）
v1.45
	1.修改javax.mail使用版本（沈佳伟20171018）
V1.46
	1.新增二维码生成工具类Qrcode。（徐涛20171019）
	2.新增DESC对称加密工具类EncryptUtil。（徐涛20171023）
V1.47
	1.修改zoneUtil.zoneselect方法，编码长度不为10位或12位不处理。（李炜20171031）
	2.pom去除重复spring-test,aliyun-java-sdk-sts的Jar包。（徐涛20171031）
V1.48
	1.StringUtils添加验证手机、邮箱的公用方法(齐然然20171101)
    2.DateUtils添加日期前后判断和只获取日期方法（马小朋20171123）
V1.49
    1.JpushUtil极光推送IOS增加声音提示（徐涛20171125）
    2.添加RestTemplate请求工具类（孙明20171207）
    3.修改RestTemplate请求工具类（孙明20171214）
V1.53
	1.新增时间计算方法（朱晓锋20180118）
	2.StringUtils增加截取字符串长度的方法（马小朋20180224）
	3.DateUtils新增查询两个日期直接的日期（齐然然20180303）
V1.55
	1.jsfUtil中新增获取RequestHeader中内容，用于获取请求路径及请求参数的方法（徐琦20180313）
	2.COSUtil开发（马小朋20180316）


__________________________________________________________________________________________________________

研发一部建立代码版本库

V1.0  
	1.研发一部建立代码版本库（齐然然20180328）

V1.1
	1.ProcessReqUtil修改方法process中获取文件流为FileItem对象（齐然然20180426）
	2.获取两个工作日期之间的工作日差值（马小朋20180426）
	4.StringUtils添加邮编验证（齐然然20180426）
V1.2
	1.手机号码与固定电话方法分开（齐然然20180517）
	2.手机号码验证位数（毛旭20180601）
V1.3
	1.DateUtils添加方法，获取传入日期年份第一天，获取传入日期年份最后一天（任晨炯20180611）
	2.清空对象属性的值（齐然然20180620）
	3.加密手机号与身份证号码的公用方法（任晨炯20180714）
V1.4
	1.生成二维码下方显示文本（齐然然20180717）
	2.创建一个新的DES加密方法，需传入加密体和密钥（裔照丹20180723）
	3.修改des加密方式（裔照丹20180727）
V1.5	
	1.修改邮政编码校验规则（任晨炯20180803）
	2.添加下载http文件方法（马小朋20180821）
V1.6
	1.新增图片等比例缩放resizeImgNew方法（齐然然20180903）
	2.http请求超时处理(马小朋20180905)

V1.7
	1.添加sm加密解密（马小朋20181102）
	2.http以raw方式请求（齐然然20181106）
	3.日期工具类中新增 多个日期中获取最大或者最小日期方法（任晨炯20181112）
V1.8
	1.手机号正则表达式第二位不做控制（齐然然20181113）
	2.javaee升级到7（马小朋20181114）
        3.解决javaee7和hibernate3.6jar冲突（马小朋20181115）
  	4.添加日期工具方法（马小朋20181124）

V1.9 
	1、放射检验接口修改加密解密（裔照丹20181214）
	2、pom文件中添加sm加密，javaee6，以及persistence-api
	3、httprequest添加方法（马小朋20190112）
  	4.发起https请求并获取ZIP压缩包结果（齐然然20190114）
	5、添加httprequest方法用于tools-core(马小朋20190114)
	6、propertyUtils返回信息不错报,用于tools-core(马小朋20190114)
V1.10
	7.zip压缩乱码修改（马小朋20190122）
V1.11
	8、身份证号码识别添加jar包（齐然然20190226）

V1.12
	1.DateUtils日期解析增加格式 yyyyMMdd
	2.修改获取两日期之间年月方法(裔照丹20190422)
V1.13
	1、StringUtils新增判断2个集合是否相同方法（齐然然20190621）
	2、aes加密工具类（齐然然20190705）
	3、pom文件中fastjson版本号修改（任晨炯20190712）
V1.14
	1、PropertyUtils中文乱码修改编码（安静20190724）
	2、发起https请求并发送ZIP压缩包结果,并且指定压缩包key名称（任晨炯20190725）
	3、StringUtils中添加string转map的方法（任晨炯20190726）
	4、MySMUtil密钥更新（任晨炯20190807）
V1.15
	1、固定电话校验规则修改（齐然然20191008）
	2、获取某个日期开始，day后的日期（齐然然20191009）
	3、FileUtils工具类增加文件上传格式统一校验方法（任晨炯20191018）
	4、验证上传文件格式（齐然然20191101）
	5、ftp文件下载（毛旭20191127）
	6、DateUtils新增计算年龄方法（安静20191204）
V1.17
	1、DateUtils中calcYearMonthBetweenTwoDate计算2个日期相差的天数修改（齐然然20200113）
	2、调整StringUtils中的获取Ip方法（getRemoteAddr），废弃JsfUtil中的获取Ip方法（getIpAddr）。（安静20200114）
	3、IdcUtils工具类修改身份证校验方式。（任晨炯20200303）
	4、IdcUtils工具类添加身份证校验出生日期方式。（任晨炯20200304）
	5、增加Ip工具类（毛旭20200321）
	6、项目上传（金芳20200326）

V1.18
	1、String类型的数值转成千分位格式。（任晨炯20200401）
	2、新增导出工具类：设置表格样式与边框线的功能（齐然然20200527）
	3、MAVEN仓库地址改为************，测试项目上传（金芳20200605）

V1.19
	1、根据身份证号获取性别（齐然然20200617）
	2、新增图标工具类（齐然然20200619）
	3、图表工具类新增折线图初始化方法（安静20200729）
	4、DateUtils覆写字符串转日期方法，忽略传入格式参数，自动识别转换成日期格式。（徐涛20201113）
	5、新增RSA密钥工具类，签名算法使用sha256withrsa（任晨炯20201207）
	6、加入港澳通行证、护照、军官证、户口簿、台胞证校验规则,并且港澳通行证、护照、军官证、户口簿内字母必须大写（潘伟20210225）
	7、图表-柱状图显示数字，饼图显示数字和百分比（齐然然20210425）
	8、图表公共类中增加横式柱状图公共方法（潘伟20210419）
	9、柱状图坐标轴刻度显示整数（齐然然20210421）
	10、证件号码加密非身份证号处理（齐然然20210515）
	11、增加转汉字日期方法（杨攀华20210719）
	12、增加JWT依赖包，解析token工具类（潘伟20210914）
	13、fastreport支持封装Map（齐然然20210915）
	14、验证社会信用代码isCreditCode按国家社会信用代码验证规则（潘伟20211008）
	15、StringUtils新增获取本地机器的ip（齐然然20211105）
	16、修改BUG：IdcUtils校验身份证报错后未返回错误信息（龚哲20211125）
	17、CollectionUtil新增Objet类型转换List时类型校验方法（陈晨20211202）
	18、ObjectCopyUtil新增使用序列化方法深拷贝List方法（陈晨20211207）
	19、log4j漏洞解决（齐然然20211213）
	20、mybatis-spring升级到兼容mybatis的版本（潘伟20220112）
	21、新增excel导出通用工具类（陈晨20220127）
	22、StringUtils新增list按长度分组方法（潘伟20220228）
	23、ZoneUtil增加通过给定的地区编码以及父级地区编码 获取该父级编码对应的直属子级编码（潘伟20220309）
	24、增加依据范围判断 传入的值 是否在范围内(支持科学记数法)（潘伟20220329）
	25、新增支持合并行列以及多sheet的excel工具类（潘伟20220413）
	26、DateUtils新增方法-两个时间比较大小（侯思杰20220418）
	27、增加计算皮尔逊相关系数方法（潘伟20220509）
	28、fastjson漏洞问题版本从1.2.70升级到1.2.83（徐涛20220524）
	29、增加金额转换成中文工具类（潘伟20220601）
	30、新增对象工具类，包括判空等操作（陈晨20220616）
	31、excel导出通用工具类新增多行列头以及合并单元格功能（陈晨20220709）
	32、Echarts饼图添加新样式（陈晨20220714）
	33、台胞证规则调整（潘伟20220719）
	34、excel导出通用工具类标题与列头行与其它行高度调整一致（陈晨20220802）
	35、log4j升级到2.18.0（潘伟20220804）
	36、DynamicExcelExportUtil增加刷新缓存，避免大量数据时内存溢出（潘伟20220809）
	37、MathUtils增加BigDecimal去除小数点后末尾的0（潘伟20220815）
	38、处理使用完压缩文件但压缩文件无法被删除（潘伟20220827）
	39、AES加密解密新增偏移量参数（侯思杰20220906）
	40、以raw方式实现post请求新增Header参数（侯思杰20220906）
	41、ObjectUtil新增类型转换方法（陈晨20220916）
	42、ZoneUtil新增移除地区全称中的省份方法（陈晨20220926）
	43、当前的文件不为图片时返回空（侯思杰20220930）
	44、StringUtils新增阿拉伯数字转中文数字方法（陈晨20221010）
	45、StringUtils新增字符串逗号分隔加引号方法（侯思杰20221019）
	46、新增IO相关工具封装工具类（陈晨20221103）
	47、ObjectUtil新增Clob字段值转字符串方法（陈晨20221103）
	48、excel导出通用工具类修复第一列宽度未自适应BUG，新增自定义标题、列头高度（陈晨20221108）
	49、excel导出通用工具类新增自定义Sheet名称（陈晨20221109）
	50、上传附件新增ZIP文件验证（齐然然20221119）
	51、HttpRequestUtil新增调用接口上传文件（不压缩）并返回JSON方法（陈晨20221209）
	52、IoUtil新增将流写到流中方法（陈晨20221209）
	53、xstream版本升级（陈晨20230112）
	54、修复XStreamUtils解析方法异常问题（陈晨20230112）
	55、敏感信息数据库加密监听器（侯思杰20230112）
	56、四舍五入保留两位小数，小数点后末尾0去掉（侯思杰20230221）
	57、List按属性分组工具类-新增多级方法名分组（侯思杰20230303）
	58、FastReport Map遍历数据异常处理（潘伟20230306）
	59、ExcelExportUtil工具类导出大量数据优化（闫壮壮20230321）
	60、附件上传新增类型（图片、pdf、zip、dcm）（侯思杰20230504）
	61、CollectionUtil新增list去重方法removeDupByContains（陈晨20230518）
	62、新增比较工具类CompareUtil，比较数字、拼音、编号等（陈晨20230519）
	63、新增删除对应sheet中的行方法（侯思杰20230522）
	64、新增OkHttp工具类（陈晨20230609）
	65、FileUtils文件格式校验 增加pdf和图片和zip、rar（潘伟20230617）
    66、ExcelExportUtil-excel通用导出类性能优化（边框线统一设置）（陈晨20230620）
    67、DateUtils新增计算两个日期相差年份（侯思杰20230620）
    68、DateUtils新增获取固定的日期（侯思杰20230620）
    69、新增SortUtil码表排序工具类（侯思杰20230708）
    70、FileUtils增加zipFileCRC32ByPo与zipFileCRC32，提升压缩文件生成速度（潘伟20230720）
    71、SortUtil新增单个属性Int类型升序排序（侯思杰20230731）
    72、ExcelExportUtil-添加可自动换行属性（侯思杰20230809）
    73、FileUtils-新增支持word、pdf、图片格式（侯思杰20230906）
    74、ExcelExportUtil标题样式增加垂直居中、自动换行（潘伟20230912）
    75、新增国密处理类（从微服务架构copy）（陈晨20231009）
    76、OkHttpUtils-postManyParams新增流的处理（陈晨20231117）
	77、身份证格式通用校验调整；社会信用代码通用验证调整；（拷贝Hutool工具）（闫壮壮20230105）
	78、EchartsUtil bar去除label显示（侯思杰20240110）
	79、Jackson 反序列化漏洞(CVE-2019-14439)升级jackson版本到2.9.9，jackson-databind升级到********（潘伟20240207）
	80、【福建职卫】职业病诊断/鉴定-社会信用代码规则调整（闫壮壮20240223）
	81、增加Set<String>转字符串方法（闫壮壮20240322）
	82、StringUtils新增校验密码方法（陈晨20240218）
	83、【福建职卫】职业病诊断/鉴定-社会信用代码规则调整（闫壮壮20240227）
	84、StringUtils新增随机生成4位数包含两位数字两位字母（字母不分大小写）（侯思杰20240327）
	85、DateUtils根据身份证计算年龄15位计算逻辑修改（侯思杰20240419）
	86、增加随机生成N位随机数字公用的方法（闫壮壮20240422）
	87、FileUtils上传文件-解决PDF文件XSS攻击漏洞（侯思杰20240612）
	88、FileUtils上传文件-新增仅支持word、pdf验证（侯思杰20240627）
	89、ExcelExportUtil新增追加导出功能（陈晨20240815）
	90、EchartsUtil堆叠柱状图增加自定义颜色属性，Y轴线显示属性（闫壮壮20240930）
	91、269469 DateUtils新增获取指定某个日期的某年某月某日后的日期（侯思杰20250210）
	92、262937 StringUtils增加姓名验证方法，pom文件新加验证繁体字依赖（闫壮壮20250214）
	93、282558 EchartsUtil中initPieXmlNew方法饼图的生成支持设置饼图中心位置和半径信息（侯思杰20250327）
	94、313618 附件上传增加类型11，控制只能上传rar、zip格式（闫壮壮20250710）

test/devtest
    1、286224【漏洞】广东数据交换子系统漏洞修复-commons-collections版本升级到3.2.2（侯思杰20250328）