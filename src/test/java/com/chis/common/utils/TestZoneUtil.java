package com.chis.common.utils;

import org.junit.Assert;
import org.junit.Test;


/**
 * Created by SHB on 2016-3-30.
 */
public class TestZoneUtil {
	
	/**
	 * public static int getZoneType(String zoneCode)
	 */
	@Test
	public void getZoneType() {
		/**
		 * 10位
		 */
		Assert.assertEquals(ZoneUtil.getZoneType("0000000000"), 1);
		Assert.assertEquals(ZoneUtil.getZoneType("3200000000"), 2);
		Assert.assertEquals(ZoneUtil.getZoneType("3201000000"), 3);
		Assert.assertEquals(ZoneUtil.getZoneType("3201020000"), 4);
		Assert.assertEquals(ZoneUtil.getZoneType("3201020300"), 5);
		Assert.assertEquals(ZoneUtil.getZoneType("3201020304"), 6);
		
		/**
		 * 12位测试
		 */
		Assert.assertEquals(ZoneUtil.getZoneType("000000000000"), 1);
		Assert.assertEquals(ZoneUtil.getZoneType("320000000000"), 2);
		Assert.assertEquals(ZoneUtil.getZoneType("320100000000"), 3);
		Assert.assertEquals(ZoneUtil.getZoneType("320102000000"), 4);
		Assert.assertEquals(ZoneUtil.getZoneType("320102030000"), 5);
		Assert.assertEquals(ZoneUtil.getZoneType("320102030004"), 6);
	}
	
	/**
	 * public static int getZoneType(String zoneCode)
	 * 异常测试
	 */
	@Test(expected = RuntimeException.class)
	public void getZoneType2() {
		/**
		 * 其它位数编码测试
		 */
		ZoneUtil.getZoneType("32020100");
	}
	
	/**
	 * public static String getParentCode(String zoneCode, int zoneType)
	 */
	@Test
	public void getParentCode() {
		/**
		 * 10位
		 */
		Assert.assertEquals(ZoneUtil.getParentCode("0000000000", 1), null);
		Assert.assertEquals(ZoneUtil.getParentCode("3200000000", 2), "0000000000");
		Assert.assertEquals(ZoneUtil.getParentCode("3201000000", 3), "3200000000");
		Assert.assertEquals(ZoneUtil.getParentCode("3201020000", 4), "3201000000");
		Assert.assertEquals(ZoneUtil.getParentCode("3201020300", 5), "3201020000");
		Assert.assertEquals(ZoneUtil.getParentCode("3201020304", 6), "3201020300");
		
		/**
		 * 12位测试
		 */
		Assert.assertEquals(ZoneUtil.getParentCode("000000000000", 1), null);
		Assert.assertEquals(ZoneUtil.getParentCode("320000000000", 2), "000000000000");
		Assert.assertEquals(ZoneUtil.getParentCode("320100000000", 3), "320000000000");
		Assert.assertEquals(ZoneUtil.getParentCode("320102000000", 4), "320100000000");
		Assert.assertEquals(ZoneUtil.getParentCode("320102030000", 5), "320102000000");
		Assert.assertEquals(ZoneUtil.getParentCode("320102030004", 6), "320102030000");
		
		/**
		 * 其它位数编码测试
		 */
		Assert.assertEquals(ZoneUtil.getParentCode("32020100", 3), null);
	}
	
	/**
	 * public static String getParentCode(String zoneCode, String zoneType)
	 */
	@Test
	public void getParentCode2() {
		/**
		 * 10位
		 */
		Assert.assertEquals(ZoneUtil.getParentCode("0000000000", "1"), null);
		Assert.assertEquals(ZoneUtil.getParentCode("3200000000", "2"), "0000000000");
		Assert.assertEquals(ZoneUtil.getParentCode("3201000000", "3"), "3200000000");
		Assert.assertEquals(ZoneUtil.getParentCode("3201020000", "4"), "3201000000");
		Assert.assertEquals(ZoneUtil.getParentCode("3201020300", "5"), "3201020000");
		Assert.assertEquals(ZoneUtil.getParentCode("3201020304", "6"), "3201020300");
		
		/**
		 * 12位测试
		 */
		Assert.assertEquals(ZoneUtil.getParentCode("000000000000", "1"), null);
		Assert.assertEquals(ZoneUtil.getParentCode("320000000000", "2"), "000000000000");
		Assert.assertEquals(ZoneUtil.getParentCode("320100000000", "3"), "320000000000");
		Assert.assertEquals(ZoneUtil.getParentCode("320102000000", "4"), "320100000000");
		Assert.assertEquals(ZoneUtil.getParentCode("320102030000", "5"), "320102000000");
		Assert.assertEquals(ZoneUtil.getParentCode("320102030004", "6"), "320102030000");
		
		/**
		 * 其它位数编码测试
		 */
		Assert.assertEquals(ZoneUtil.getParentCode("32020100", "3"), null);
	}
	
	/**
	 * public static String getParentCode(String zoneCode)
	 */
	@Test
	public void getParentCode3() {
		/**
		 * 10位
		 */
		Assert.assertEquals(ZoneUtil.getParentCode("0000000000"), null);
		Assert.assertEquals(ZoneUtil.getParentCode("3200000000"), "0000000000");
		Assert.assertEquals(ZoneUtil.getParentCode("3201000000"), "3200000000");
		Assert.assertEquals(ZoneUtil.getParentCode("3201020000"), "3201000000");
		Assert.assertEquals(ZoneUtil.getParentCode("3201020300"), "3201020000");
		Assert.assertEquals(ZoneUtil.getParentCode("3201020304"), "3201020300");
		
		/**
		 * 12位测试
		 */
		Assert.assertEquals(ZoneUtil.getParentCode("000000000000"), null);
		Assert.assertEquals(ZoneUtil.getParentCode("320000000000"), "000000000000");
		Assert.assertEquals(ZoneUtil.getParentCode("320100000000"), "320000000000");
		Assert.assertEquals(ZoneUtil.getParentCode("320102000000"), "320100000000");
		Assert.assertEquals(ZoneUtil.getParentCode("320102030000"), "320102000000");
		Assert.assertEquals(ZoneUtil.getParentCode("320102030004"), "320102030000");
		
	}
	
	/**
	 * public static int getZoneType(String zoneCode)
	 * 异常测试
	 */
	@Test(expected = RuntimeException.class)
	public void getParentCode4() {
		/**
		 * 其它位数编码测试
		 */
		ZoneUtil.getParentCode("32020100");
	}
	
	
    @Test
    public void zoneSelect(){
        //10位，省级测试
        Assert.assertEquals("10位zoneSelect()方法出错，省级", ZoneUtil.zoneSelect("3200000000"),"32");
        //10位，市级测试
        Assert.assertEquals("10位zoneSelect()方法出错，市级", ZoneUtil.zoneSelect("3201000000"),"3201");
        //10位，县区级测试
        Assert.assertEquals("10位zoneSelect()方法出错，县区级", ZoneUtil.zoneSelect("3201100000"),"320110");
        //10位，乡镇街道级测试
        Assert.assertEquals("10位zoneSelect()方法出错，乡镇街道级", ZoneUtil.zoneSelect("3201010100"),"32010101");
        //10位，村级测试
        Assert.assertEquals("10位zoneSelect()方法出错，村级", ZoneUtil.zoneSelect("3201010101"),"3201010101");

        //12位，省级测试
        Assert.assertEquals("12位zoneSelect()方法出错，省级", ZoneUtil.zoneSelect("320000000000"),"32");
        //12位，市级测试
        Assert.assertEquals("12位zoneSelect()方法出错，市级", ZoneUtil.zoneSelect("320100000000"),"3201");
        //12位，县区级测试
        Assert.assertEquals("12位zoneSelect()方法出错，县区级", ZoneUtil.zoneSelect("320110000000"),"320110");
        //12位，乡镇街道级测试
        Assert.assertEquals("12位zoneSelect()方法出错，乡镇街道级", ZoneUtil.zoneSelect("320101010000"),"320101010");
        //12位，村级测试
        Assert.assertEquals("12位zoneSelect()方法出错，村级", ZoneUtil.zoneSelect("320101010001"),"320101010001");
        
        //其它位
//        Assert.assertEquals(ZoneUtil.zoneSelect("32020100"),"320201");

    }
    
	/**
	 * public static String getZoneCodeLevel(String zoneCode)
	 */
	@Test
	public void getZoneCodeLevel() {
		/**
		 * 10位
		 */
		Assert.assertEquals(ZoneUtil.getZoneCodeLevel("0000000000"), "1");
		Assert.assertEquals(ZoneUtil.getZoneCodeLevel("3200000000"), "2");
		Assert.assertEquals(ZoneUtil.getZoneCodeLevel("3201000000"), "3");
		Assert.assertEquals(ZoneUtil.getZoneCodeLevel("3201020000"), "4");
		Assert.assertEquals(ZoneUtil.getZoneCodeLevel("3201020300"), "5");
		Assert.assertEquals(ZoneUtil.getZoneCodeLevel("3201020304"), "6");
		
		/**
		 * 12位测试
		 */
		Assert.assertEquals(ZoneUtil.getZoneCodeLevel("000000000000"), "1");
		Assert.assertEquals(ZoneUtil.getZoneCodeLevel("320000000000"), "2");
		Assert.assertEquals(ZoneUtil.getZoneCodeLevel("320100000000"), "3");
		Assert.assertEquals(ZoneUtil.getZoneCodeLevel("320102000000"), "4");
		Assert.assertEquals(ZoneUtil.getZoneCodeLevel("320102030000"), "5");
		Assert.assertEquals(ZoneUtil.getZoneCodeLevel("320102030004"), "6");
	}
	
	/**
	 * public static String getZoneCodeLevel(String zoneCode)
	 * 异常测试
	 */
	@Test(expected = RuntimeException.class)
	public void getZoneCodeLevel2() {
		/**
		 * 其它位数编码测试
		 */
		ZoneUtil.getZoneCodeLevel("32020100");
	}
}
