package com.chis.common.utils;

import java.lang.reflect.InvocationTargetException;

import org.junit.Assert;
import org.junit.Test;

import com.sun.star.lang.NullPointerException;

public class TestReflections {

	@Test
	public void testInvokeGetter() {
		A a = new A();
		a.setName("A");
		Assert.assertEquals("Reflections.invokeGetter方法报错",
				Reflections.invokeGetter(a, "name"), "A");
		a.setB(new B());
		a.getB().setName("B");
		Assert.assertEquals("Reflections.invokeGetter方法报错",
				Reflections.invokeGetter(a, "b.name"), "B");

	}

	@Test
	public void testInvokeSetter() {
		A a = new A();
		Reflections.invokeSetter(a, "b", new B());
		Reflections.invokeSetter(a, "b.name", "B");
		Assert.assertEquals("Reflections.invokeSetter方法报错",
				Reflections.invokeGetter(a, "b.name"), "B");
	}

	@Test
	public void testGetFieldValue() {
		A a = new A();
		a.setName("A");
		Assert.assertEquals("Reflections.getFieldValue方法报错",
				Reflections.getFieldValue(a, "name"), "A");
	}

	@Test(expected = IllegalArgumentException.class)
	public void testGetFieldValueForError() {
		A a = new A();
		Reflections.getFieldValue(a, "AAAA");
	}

	@Test
	public void testSetFieldValue() {
		A a = new A();
		Reflections.setFieldValue(a, "value", "test");
		Assert.assertEquals("Reflections.setFieldValue方法报错", a.getValue(),
				"test");
	}

	@Test
	public void testInvokeMethod() {
		A a = new A();
		a.setName("A");
		Assert.assertEquals("Reflections.invokeMethod方法报错",
				Reflections.invokeMethod(a, "getName", null, null), "A");
	}

	@Test(expected = RuntimeException.class)
	public void testInvokeMethodForError() {
		A a = new A();
		Reflections.invokeMethod(a, null, null, null);
		Reflections.invokeMethod(a, "getA", null, null);
	}

	@Test
	public void testInvokeMethodByName() {
		A a = new A();
		a.setName("A");
		Assert.assertEquals("Reflections.invokeMethodByName方法报错",
				Reflections.invokeMethodByName(a, "getName", null), "A");
	}

	@Test(expected = RuntimeException.class)
	public void testInvokeMethodByNameForError() {
		A a = new A();
		Reflections.invokeMethodByName(a, null, null);
		Reflections.invokeMethodByName(null, null, null);
	}

	@Test
	public void testGetAccessibleField() {
		A a = new A();
		Assert.assertEquals("Reflections.getAccessibleField方法报错", Reflections
				.getAccessibleField(a, "name").getType(), String.class);
	}

	@Test
	public void testGetAccessibleMethod() {
		A a = new A();
		Assert.assertEquals("Reflections.getAccessibleMethod方法报错", Reflections
				.getAccessibleMethod(a, "setName", String.class).getName(),
				"setName");
		Assert.assertEquals("Reflections.getAccessibleMethod方法报错",
				Reflections.getAccessibleMethod(a, "AAAA", null), null);
	}

	@Test
	public void testGetAccessibleMethodByName() {
		A a = new A();
		Assert.assertEquals("Reflections.getAccessibleMethodByName方法报错",
				Reflections.getAccessibleMethodByName(a, "setName").getName(),
				"setName");
		Assert.assertEquals("Reflections.getAccessibleMethodByName方法报错",
				Reflections.getAccessibleMethodByName(a, "AAAA"), null);
	}

	@Test
	public void testMakeAccessible() {
		A a = new A();
		a.setName("A");
		a.setValue("a");
		try {
			Assert.assertEquals("Reflections.makeAccessible方法报错", Reflections
					.getAccessibleMethod(a, "outPut", null).invoke(a, null),
					"A:a");
			Assert.assertEquals("Reflections.makeAccessible方法报错", Reflections
					.getAccessibleMethod(a, "getName", null).invoke(a, null),
					"A");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Test
	public void testGetClassGenricType() {
		Assert.assertEquals("Reflections.getClassGenricType方法报错",
				Reflections.getClassGenricType(A.class), String.class);
	}

	@Test
	public void testGetUserClass() {
		A a = new A();
		Assert.assertEquals("Reflections.getUserClass方法报错",
				Reflections.getUserClass(a), A.class);
	}

	@Test
	public void testConvertReflectionExceptionToUnchecked() {
		Assert.assertEquals(
				"Reflections.convertReflectionExceptionToUnchecked方法报错",
				Reflections
						.convertReflectionExceptionToUnchecked(new IllegalArgumentException()).getClass(),
				IllegalArgumentException.class);

		Assert.assertEquals(
				"Reflections.convertReflectionExceptionToUnchecked方法报错",
				Reflections
						.convertReflectionExceptionToUnchecked(new InvocationTargetException(
								new NullPointerException())).getClass(),
				RuntimeException.class);
	}

}

class Super<String> {

}

class A extends Super<String> {
	private B b;
	private String name;
	private String value;

	private String outPut() {
		return name + ":" + value;
	}

	public B getB() {
		return b;
	}

	public void setB(B b) {
		this.b = b;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

}

class B {
	private String name;
	private String value;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
}