package com.chis.common.utils;

import org.junit.Assert;
import org.junit.Test;

public class TestEncodes {

	@Test
	public void test() {
		//UrlEncode测试
		Assert.assertNotNull("UrlEncode方法出错",
				Encodes.urlEncode("http://10.88.88.132:60000/redmine/"));
		Assert.assertEquals("UrlEncode方法出错",
				Encodes.urlEncode("http://10.88.88.132:60000/redmine/"),
				"http%3A%2F%2F10.88.88.132%3A60000%2Fredmine%2F");
		//UrlDecode测试
		Assert.assertNotNull("UrlDecode方法出错",
				Encodes.urlDecode("http%3A%2F%2F10.88.88.132%3A60000%2Fredmine%2F"));
		Assert.assertEquals("UrlDecode方法出错",
				Encodes.urlDecode("http%3A%2F%2F10.88.88.132%3A60000%2Fredmine%2F"),
				"http://10.88.88.132:60000/redmine/");
	}
}
