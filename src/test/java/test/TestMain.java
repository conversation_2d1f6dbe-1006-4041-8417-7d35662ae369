//package test;
//
//import java.util.Date;
//
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.transaction.TransactionConfiguration;
//import org.springframework.transaction.annotation.Transactional;
//
//import com.chis.common.utils.DateUtils;
//import com.chis.modules.system.service.SystemModuleServiceImpl;
//
//@Transactional  
//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = true)  
//@RunWith(SpringJUnit4ClassRunner.class)  
//@ContextConfiguration(locations={"classpath:/spring/spring-*.xml"})  
//public class TestMain {
//	
//	@Autowired
//	private SystemModuleServiceImpl serviceImpl;
//
//	@Test
//	public void test() {
//		System.err.println("【】：" + this.serviceImpl.deleteGroup(17));
//	}
//	
//	
//	
//}
