package org.csapi.schema.location;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for DelayTolerance.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * <p>
 * 
 * <pre>
 * &lt;simpleType name="DelayTolerance">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="NoDelay"/>
 *     &lt;enumeration value="LowDelay"/>
 *     &lt;enumeration value="DelayTolerant"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "DelayTolerance")
@XmlEnum
public enum DelayTolerance {

	@XmlEnumValue("NoDelay")
	NO_DELAY("NoDelay"), @XmlEnumValue("LowDelay")
	LOW_DELAY("LowDelay"), @XmlEnumValue("DelayTolerant")
	DELAY_TOLERANT("DelayTolerant");
	private final String value;

	DelayTolerance(String v) {
		value = v;
	}

	public String value() {
		return value;
	}

	public static DelayTolerance fromValue(String v) {
		for (DelayTolerance c : DelayTolerance.values()) {
			if (c.value.equals(v)) {
				return c;
			}
		}
		throw new IllegalArgumentException(v);
	}

}
