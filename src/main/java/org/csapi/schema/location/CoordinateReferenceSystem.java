package org.csapi.schema.location;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for CoordinateReferenceSystem complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="CoordinateReferenceSystem">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="code" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="codeSpace" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="edition" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CoordinateReferenceSystem", propOrder = { "code", "codeSpace",
		"edition" })
public class CoordinateReferenceSystem {

	@XmlElement(required = true)
	protected String code;
	@XmlElement(required = true)
	protected String codeSpace;
	@XmlElement(required = true)
	protected String edition;

	/**
	 * Gets the value of the code property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCode() {
		return code;
	}

	/**
	 * Sets the value of the code property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCode(String value) {
		this.code = value;
	}

	/**
	 * Gets the value of the codeSpace property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCodeSpace() {
		return codeSpace;
	}

	/**
	 * Sets the value of the codeSpace property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCodeSpace(String value) {
		this.codeSpace = value;
	}

	/**
	 * Gets the value of the edition property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getEdition() {
		return edition;
	}

	/**
	 * Sets the value of the edition property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEdition(String value) {
		this.edition = value;
	}

}
