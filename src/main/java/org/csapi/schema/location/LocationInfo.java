package org.csapi.schema.location;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;

/**
 * <p>
 * Java class for LocationInfo complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="LocationInfo">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Latitude" type="{http://www.w3.org/2001/XMLSchema}float"/>
 *         &lt;element name="Longitude" type="{http://www.w3.org/2001/XMLSchema}float"/>
 *         &lt;element name="Altitude" type="{http://www.w3.org/2001/XMLSchema}float"/>
 *         &lt;element name="Accuracy" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="Timestamp" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "LocationInfo", propOrder = { "latitude", "longitude",
		"altitude", "accuracy", "timestamp" })
public class LocationInfo {

	@XmlElement(name = "Latitude")
	protected float latitude;
	@XmlElement(name = "Longitude")
	protected float longitude;
	@XmlElement(name = "Altitude")
	protected float altitude;
	@XmlElement(name = "Accuracy")
	protected int accuracy;
	@XmlElement(name = "Timestamp", required = true, nillable = true)
	@XmlSchemaType(name = "dateTime")
	protected XMLGregorianCalendar timestamp;

	/**
	 * Gets the value of the latitude property.
	 * 
	 */
	public float getLatitude() {
		return latitude;
	}

	/**
	 * Sets the value of the latitude property.
	 * 
	 */
	public void setLatitude(float value) {
		this.latitude = value;
	}

	/**
	 * Gets the value of the longitude property.
	 * 
	 */
	public float getLongitude() {
		return longitude;
	}

	/**
	 * Sets the value of the longitude property.
	 * 
	 */
	public void setLongitude(float value) {
		this.longitude = value;
	}

	/**
	 * Gets the value of the altitude property.
	 * 
	 */
	public float getAltitude() {
		return altitude;
	}

	/**
	 * Sets the value of the altitude property.
	 * 
	 */
	public void setAltitude(float value) {
		this.altitude = value;
	}

	/**
	 * Gets the value of the accuracy property.
	 * 
	 */
	public int getAccuracy() {
		return accuracy;
	}

	/**
	 * Sets the value of the accuracy property.
	 * 
	 */
	public void setAccuracy(int value) {
		this.accuracy = value;
	}

	/**
	 * Gets the value of the timestamp property.
	 * 
	 * @return possible object is {@link XMLGregorianCalendar }
	 * 
	 */
	public XMLGregorianCalendar getTimestamp() {
		return timestamp;
	}

	/**
	 * Sets the value of the timestamp property.
	 * 
	 * @param value
	 *            allowed object is {@link XMLGregorianCalendar }
	 * 
	 */
	public void setTimestamp(XMLGregorianCalendar value) {
		this.timestamp = value;
	}

}
