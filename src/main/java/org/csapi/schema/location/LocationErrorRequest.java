package org.csapi.schema.location;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import org.csapi.schema.common.v2_0.ServiceError;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="correlator" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Address" type="{http://www.w3.org/2001/XMLSchema}anyURI"/>
 *         &lt;element name="Reason" type="{http://www.csapi.org/schema/common/v2_0}ServiceError"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "correlator", "address", "reason" })
@XmlRootElement(name = "LocationErrorRequest")
public class LocationErrorRequest {

	@XmlElement(required = true)
	protected String correlator;
	@XmlElement(name = "Address", required = true, nillable = true)
	@XmlSchemaType(name = "anyURI")
	protected String address;
	@XmlElement(name = "Reason", required = true, nillable = true)
	protected ServiceError reason;

	/**
	 * Gets the value of the correlator property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCorrelator() {
		return correlator;
	}

	/**
	 * Sets the value of the correlator property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCorrelator(String value) {
		this.correlator = value;
	}

	/**
	 * Gets the value of the address property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * Sets the value of the address property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setAddress(String value) {
		this.address = value;
	}

	/**
	 * Gets the value of the reason property.
	 * 
	 * @return possible object is {@link ServiceError }
	 * 
	 */
	public ServiceError getReason() {
		return reason;
	}

	/**
	 * Sets the value of the reason property.
	 * 
	 * @param value
	 *            allowed object is {@link ServiceError }
	 * 
	 */
	public void setReason(ServiceError value) {
		this.reason = value;
	}

}
