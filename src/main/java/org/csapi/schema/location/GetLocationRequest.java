package org.csapi.schema.location;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import org.csapi.schema.common.v2_0.TimeMetric;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Requester" type="{http://www.w3.org/2001/XMLSchema}anyURI"/>
 *         &lt;element name="Address" type="{http://www.w3.org/2001/XMLSchema}anyURI"/>
 *         &lt;element name="RequestedAccuracy" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="AcceptableAccuracy" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="MaximumAge" type="{http://www.csapi.org/schema/common/v2_0}TimeMetric"/>
 *         &lt;element name="ResponseTime" type="{http://www.csapi.org/schema/common/v2_0}TimeMetric"/>
 *         &lt;element name="Tolerance" type="{http://www.csapi.org/schema/location}DelayTolerance"/>
 *         &lt;element name="ApplicationId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="serviceType" type="{http://www.csapi.org/schema/location}ServiceType"/>
 *         &lt;element name="crs" type="{http://www.csapi.org/schema/location}CoordinateReferenceSystem"/>
 *         &lt;element name="locType" type="{http://www.csapi.org/schema/location}LocType"/>
 *         &lt;element name="prio" type="{http://www.csapi.org/schema/location}Priority"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "requester", "address", "requestedAccuracy",
		"acceptableAccuracy", "maximumAge", "responseTime", "tolerance",
		"applicationId", "serviceType", "crs", "locType", "prio" })
@XmlRootElement(name = "getLocationRequest")
public class GetLocationRequest {

	@XmlElement(name = "Requester", required = true, nillable = true)
	@XmlSchemaType(name = "anyURI")
	protected String requester;
	@XmlElement(name = "Address", required = true, nillable = true)
	@XmlSchemaType(name = "anyURI")
	protected String address;
	@XmlElement(name = "RequestedAccuracy")
	protected int requestedAccuracy;
	@XmlElement(name = "AcceptableAccuracy")
	protected int acceptableAccuracy;
	@XmlElement(name = "MaximumAge", required = true, nillable = true)
	protected TimeMetric maximumAge;
	@XmlElement(name = "ResponseTime", required = true, nillable = true)
	protected TimeMetric responseTime;
	@XmlElement(name = "Tolerance", required = true, nillable = true)
	protected DelayTolerance tolerance;
	@XmlElement(name = "ApplicationId", required = true, nillable = true)
	protected String applicationId;
	@XmlElement(required = true)
	protected ServiceType serviceType;
	@XmlElement(required = true)
	protected CoordinateReferenceSystem crs;
	@XmlElement(required = true)
	protected LocType locType;
	@XmlElement(required = true)
	protected Priority prio;

	/**
	 * Gets the value of the requester property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getRequester() {
		return requester;
	}

	/**
	 * Sets the value of the requester property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setRequester(String value) {
		this.requester = value;
	}

	/**
	 * Gets the value of the address property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * Sets the value of the address property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setAddress(String value) {
		this.address = value;
	}

	/**
	 * Gets the value of the requestedAccuracy property.
	 * 
	 */
	public int getRequestedAccuracy() {
		return requestedAccuracy;
	}

	/**
	 * Sets the value of the requestedAccuracy property.
	 * 
	 */
	public void setRequestedAccuracy(int value) {
		this.requestedAccuracy = value;
	}

	/**
	 * Gets the value of the acceptableAccuracy property.
	 * 
	 */
	public int getAcceptableAccuracy() {
		return acceptableAccuracy;
	}

	/**
	 * Sets the value of the acceptableAccuracy property.
	 * 
	 */
	public void setAcceptableAccuracy(int value) {
		this.acceptableAccuracy = value;
	}

	/**
	 * Gets the value of the maximumAge property.
	 * 
	 * @return possible object is {@link TimeMetric }
	 * 
	 */
	public TimeMetric getMaximumAge() {
		return maximumAge;
	}

	/**
	 * Sets the value of the maximumAge property.
	 * 
	 * @param value
	 *            allowed object is {@link TimeMetric }
	 * 
	 */
	public void setMaximumAge(TimeMetric value) {
		this.maximumAge = value;
	}

	/**
	 * Gets the value of the responseTime property.
	 * 
	 * @return possible object is {@link TimeMetric }
	 * 
	 */
	public TimeMetric getResponseTime() {
		return responseTime;
	}

	/**
	 * Sets the value of the responseTime property.
	 * 
	 * @param value
	 *            allowed object is {@link TimeMetric }
	 * 
	 */
	public void setResponseTime(TimeMetric value) {
		this.responseTime = value;
	}

	/**
	 * Gets the value of the tolerance property.
	 * 
	 * @return possible object is {@link DelayTolerance }
	 * 
	 */
	public DelayTolerance getTolerance() {
		return tolerance;
	}

	/**
	 * Sets the value of the tolerance property.
	 * 
	 * @param value
	 *            allowed object is {@link DelayTolerance }
	 * 
	 */
	public void setTolerance(DelayTolerance value) {
		this.tolerance = value;
	}

	/**
	 * Gets the value of the applicationId property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getApplicationId() {
		return applicationId;
	}

	/**
	 * Sets the value of the applicationId property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setApplicationId(String value) {
		this.applicationId = value;
	}

	/**
	 * Gets the value of the serviceType property.
	 * 
	 * @return possible object is {@link ServiceType }
	 * 
	 */
	public ServiceType getServiceType() {
		return serviceType;
	}

	/**
	 * Sets the value of the serviceType property.
	 * 
	 * @param value
	 *            allowed object is {@link ServiceType }
	 * 
	 */
	public void setServiceType(ServiceType value) {
		this.serviceType = value;
	}

	/**
	 * Gets the value of the crs property.
	 * 
	 * @return possible object is {@link CoordinateReferenceSystem }
	 * 
	 */
	public CoordinateReferenceSystem getCrs() {
		return crs;
	}

	/**
	 * Sets the value of the crs property.
	 * 
	 * @param value
	 *            allowed object is {@link CoordinateReferenceSystem }
	 * 
	 */
	public void setCrs(CoordinateReferenceSystem value) {
		this.crs = value;
	}

	/**
	 * Gets the value of the locType property.
	 * 
	 * @return possible object is {@link LocType }
	 * 
	 */
	public LocType getLocType() {
		return locType;
	}

	/**
	 * Sets the value of the locType property.
	 * 
	 * @param value
	 *            allowed object is {@link LocType }
	 * 
	 */
	public void setLocType(LocType value) {
		this.locType = value;
	}

	/**
	 * Gets the value of the prio property.
	 * 
	 * @return possible object is {@link Priority }
	 * 
	 */
	public Priority getPrio() {
		return prio;
	}

	/**
	 * Sets the value of the prio property.
	 * 
	 * @param value
	 *            allowed object is {@link Priority }
	 * 
	 */
	public void setPrio(Priority value) {
		this.prio = value;
	}

}
