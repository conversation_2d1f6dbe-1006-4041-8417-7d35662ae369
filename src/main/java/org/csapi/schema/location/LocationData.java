package org.csapi.schema.location;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import org.csapi.schema.common.v2_0.ServiceError;

/**
 * <p>
 * Java class for LocationData complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="LocationData">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Address" type="{http://www.w3.org/2001/XMLSchema}anyURI"/>
 *         &lt;element name="ReportStatus" type="{http://www.csapi.org/schema/location}RetrievalStatus"/>
 *         &lt;element name="CurrentLocation" type="{http://www.csapi.org/schema/location}LocationInfo"/>
 *         &lt;element name="ErrorInformation" type="{http://www.csapi.org/schema/common/v2_0}ServiceError"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "LocationData", propOrder = { "address", "reportStatus",
		"currentLocation", "errorInformation" })
public class LocationData {

	@XmlElement(name = "Address", required = true, nillable = true)
	@XmlSchemaType(name = "anyURI")
	protected String address;
	@XmlElement(name = "ReportStatus", required = true, nillable = true)
	protected RetrievalStatus reportStatus;
	@XmlElement(name = "CurrentLocation", required = true, nillable = true)
	protected LocationInfo currentLocation;
	@XmlElement(name = "ErrorInformation", required = true, nillable = true)
	protected ServiceError errorInformation;

	/**
	 * Gets the value of the address property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * Sets the value of the address property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setAddress(String value) {
		this.address = value;
	}

	/**
	 * Gets the value of the reportStatus property.
	 * 
	 * @return possible object is {@link RetrievalStatus }
	 * 
	 */
	public RetrievalStatus getReportStatus() {
		return reportStatus;
	}

	/**
	 * Sets the value of the reportStatus property.
	 * 
	 * @param value
	 *            allowed object is {@link RetrievalStatus }
	 * 
	 */
	public void setReportStatus(RetrievalStatus value) {
		this.reportStatus = value;
	}

	/**
	 * Gets the value of the currentLocation property.
	 * 
	 * @return possible object is {@link LocationInfo }
	 * 
	 */
	public LocationInfo getCurrentLocation() {
		return currentLocation;
	}

	/**
	 * Sets the value of the currentLocation property.
	 * 
	 * @param value
	 *            allowed object is {@link LocationInfo }
	 * 
	 */
	public void setCurrentLocation(LocationInfo value) {
		this.currentLocation = value;
	}

	/**
	 * Gets the value of the errorInformation property.
	 * 
	 * @return possible object is {@link ServiceError }
	 * 
	 */
	public ServiceError getErrorInformation() {
		return errorInformation;
	}

	/**
	 * Sets the value of the errorInformation property.
	 * 
	 * @param value
	 *            allowed object is {@link ServiceError }
	 * 
	 */
	public void setErrorInformation(ServiceError value) {
		this.errorInformation = value;
	}

}
