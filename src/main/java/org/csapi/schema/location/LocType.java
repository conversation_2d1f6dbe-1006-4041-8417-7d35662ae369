package org.csapi.schema.location;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for LocType.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * <p>
 * 
 * <pre>
 * &lt;simpleType name="LocType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="CURRENT"/>
 *     &lt;enumeration value="LAST"/>
 *     &lt;enumeration value="CURRENT_OR_LAST"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "LocType")
@XmlEnum
public enum LocType {

	CURRENT, LAST, CURRENT_OR_LAST;

	public String value() {
		return name();
	}

	public static LocType fromValue(String v) {
		return valueOf(v);
	}

}
