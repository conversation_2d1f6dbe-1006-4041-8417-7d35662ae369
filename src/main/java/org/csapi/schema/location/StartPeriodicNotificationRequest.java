package org.csapi.schema.location;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import org.csapi.schema.common.v2_0.TimeMetric;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Requester" type="{http://www.w3.org/2001/XMLSchema}anyURI"/>
 *         &lt;element name="Addresses" type="{http://www.w3.org/2001/XMLSchema}anyURI" maxOccurs="unbounded"/>
 *         &lt;element name="RequestedAccuracy" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="Frequency" type="{http://www.csapi.org/schema/common/v2_0}TimeMetric"/>
 *         &lt;element name="Duration" type="{http://www.csapi.org/schema/common/v2_0}TimeMetric"/>
 *         &lt;element name="ApplicationId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="serviceType" type="{http://www.csapi.org/schema/location}ServiceType"/>
 *         &lt;element name="Crs" type="{http://www.csapi.org/schema/location}CoordinateReferenceSystem"/>
 *         &lt;element name="locType" type="{http://www.csapi.org/schema/location}LocType"/>
 *         &lt;element name="prio" type="{http://www.csapi.org/schema/location}Priority"/>
 *         &lt;element name="eventNotification" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "requester", "addresses",
		"requestedAccuracy", "frequency", "duration", "applicationId",
		"serviceType", "crs", "locType", "prio", "eventNotification" })
@XmlRootElement(name = "startPeriodicNotificationRequest")
public class StartPeriodicNotificationRequest {

	@XmlElement(name = "Requester", required = true, nillable = true)
	@XmlSchemaType(name = "anyURI")
	protected String requester;
	@XmlElement(name = "Addresses", required = true, nillable = true)
	@XmlSchemaType(name = "anyURI")
	protected List<String> addresses;
	@XmlElement(name = "RequestedAccuracy")
	protected int requestedAccuracy;
	@XmlElement(name = "Frequency", required = true, nillable = true)
	protected TimeMetric frequency;
	@XmlElement(name = "Duration", required = true, nillable = true)
	protected TimeMetric duration;
	@XmlElement(name = "ApplicationId", required = true, nillable = true)
	protected String applicationId;
	@XmlElement(required = true)
	protected ServiceType serviceType;
	@XmlElement(name = "Crs", required = true, nillable = true)
	protected CoordinateReferenceSystem crs;
	@XmlElement(required = true)
	protected LocType locType;
	@XmlElement(required = true)
	protected Priority prio;
	@XmlElement(required = true)
	protected String eventNotification;

	/**
	 * Gets the value of the requester property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getRequester() {
		return requester;
	}

	/**
	 * Sets the value of the requester property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setRequester(String value) {
		this.requester = value;
	}

	/**
	 * Gets the value of the addresses property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the addresses property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getAddresses().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link String }
	 * 
	 * 
	 */
	public List<String> getAddresses() {
		if (addresses == null) {
			addresses = new ArrayList<String>();
		}
		return this.addresses;
	}

	/**
	 * Gets the value of the requestedAccuracy property.
	 * 
	 */
	public int getRequestedAccuracy() {
		return requestedAccuracy;
	}

	/**
	 * Sets the value of the requestedAccuracy property.
	 * 
	 */
	public void setRequestedAccuracy(int value) {
		this.requestedAccuracy = value;
	}

	/**
	 * Gets the value of the frequency property.
	 * 
	 * @return possible object is {@link TimeMetric }
	 * 
	 */
	public TimeMetric getFrequency() {
		return frequency;
	}

	/**
	 * Sets the value of the frequency property.
	 * 
	 * @param value
	 *            allowed object is {@link TimeMetric }
	 * 
	 */
	public void setFrequency(TimeMetric value) {
		this.frequency = value;
	}

	/**
	 * Gets the value of the duration property.
	 * 
	 * @return possible object is {@link TimeMetric }
	 * 
	 */
	public TimeMetric getDuration() {
		return duration;
	}

	/**
	 * Sets the value of the duration property.
	 * 
	 * @param value
	 *            allowed object is {@link TimeMetric }
	 * 
	 */
	public void setDuration(TimeMetric value) {
		this.duration = value;
	}

	/**
	 * Gets the value of the applicationId property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getApplicationId() {
		return applicationId;
	}

	/**
	 * Sets the value of the applicationId property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setApplicationId(String value) {
		this.applicationId = value;
	}

	/**
	 * Gets the value of the serviceType property.
	 * 
	 * @return possible object is {@link ServiceType }
	 * 
	 */
	public ServiceType getServiceType() {
		return serviceType;
	}

	/**
	 * Sets the value of the serviceType property.
	 * 
	 * @param value
	 *            allowed object is {@link ServiceType }
	 * 
	 */
	public void setServiceType(ServiceType value) {
		this.serviceType = value;
	}

	/**
	 * Gets the value of the crs property.
	 * 
	 * @return possible object is {@link CoordinateReferenceSystem }
	 * 
	 */
	public CoordinateReferenceSystem getCrs() {
		return crs;
	}

	/**
	 * Sets the value of the crs property.
	 * 
	 * @param value
	 *            allowed object is {@link CoordinateReferenceSystem }
	 * 
	 */
	public void setCrs(CoordinateReferenceSystem value) {
		this.crs = value;
	}

	/**
	 * Gets the value of the locType property.
	 * 
	 * @return possible object is {@link LocType }
	 * 
	 */
	public LocType getLocType() {
		return locType;
	}

	/**
	 * Sets the value of the locType property.
	 * 
	 * @param value
	 *            allowed object is {@link LocType }
	 * 
	 */
	public void setLocType(LocType value) {
		this.locType = value;
	}

	/**
	 * Gets the value of the prio property.
	 * 
	 * @return possible object is {@link Priority }
	 * 
	 */
	public Priority getPrio() {
		return prio;
	}

	/**
	 * Sets the value of the prio property.
	 * 
	 * @param value
	 *            allowed object is {@link Priority }
	 * 
	 */
	public void setPrio(Priority value) {
		this.prio = value;
	}

	/**
	 * Gets the value of the eventNotification property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getEventNotification() {
		return eventNotification;
	}

	/**
	 * Sets the value of the eventNotification property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEventNotification(String value) {
		this.eventNotification = value;
	}

}
