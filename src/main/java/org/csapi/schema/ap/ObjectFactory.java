package org.csapi.schema.ap;

import javax.xml.bind.annotation.XmlRegistry;

/**
 * This object contains factory methods for each Java content interface and Java
 * element interface generated in the org.csapi.schema.ap package.
 * <p>
 * An ObjectFactory allows you to programatically construct new instances of the
 * Java representation for XML content. The Java representation of XML content
 * can consist of schema derived interfaces and classes representing the binding
 * of schema type definitions, element declarations and model groups. Factory
 * methods for each of these are provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

	/**
	 * Create a new ObjectFactory that can be used to create new instances of
	 * schema derived classes for package: org.csapi.schema.ap
	 * 
	 */
	public ObjectFactory() {
	}

	/**
	 * Create an instance of {@link APStatusRepRsp }
	 * 
	 */
	public APStatusRepRsp createAPStatusRepRsp() {
		return new APStatusRepRsp();
	}

	/**
	 * Create an instance of {@link APSvcAuthenticRsp }
	 * 
	 */
	public APSvcAuthenticRsp createAPSvcAuthenticRsp() {
		return new APSvcAuthenticRsp();
	}

	/**
	 * Create an instance of {@link APStatusRepReq }
	 * 
	 */
	public APStatusRepReq createAPStatusRepReq() {
		return new APStatusRepReq();
	}

	/**
	 * Create an instance of {@link APRegistrationReq }
	 * 
	 */
	public APRegistrationReq createAPRegistrationReq() {
		return new APRegistrationReq();
	}

	/**
	 * Create an instance of {@link APLogOutRsp }
	 * 
	 */
	public APLogOutRsp createAPLogOutRsp() {
		return new APLogOutRsp();
	}

	/**
	 * Create an instance of {@link AlarmReq }
	 * 
	 */
	public AlarmReq createAlarmReq() {
		return new AlarmReq();
	}

	/**
	 * Create an instance of {@link APSvcAuthenticReq }
	 * 
	 */
	public APSvcAuthenticReq createAPSvcAuthenticReq() {
		return new APSvcAuthenticReq();
	}

	/**
	 * Create an instance of {@link APSvcPerfCmdReq }
	 * 
	 */
	public APSvcPerfCmdReq createAPSvcPerfCmdReq() {
		return new APSvcPerfCmdReq();
	}

	/**
	 * Create an instance of {@link APLogOutReq }
	 * 
	 */
	public APLogOutReq createAPLogOutReq() {
		return new APLogOutReq();
	}

	/**
	 * Create an instance of {@link APSvcPerfReportReq }
	 * 
	 */
	public APSvcPerfReportReq createAPSvcPerfReportReq() {
		return new APSvcPerfReportReq();
	}

	/**
	 * Create an instance of {@link RecoveryAPReq }
	 * 
	 */
	public RecoveryAPReq createRecoveryAPReq() {
		return new RecoveryAPReq();
	}

	/**
	 * Create an instance of {@link PauseAPRsp }
	 * 
	 */
	public PauseAPRsp createPauseAPRsp() {
		return new PauseAPRsp();
	}

	/**
	 * Create an instance of {@link PauseAPReq }
	 * 
	 */
	public PauseAPReq createPauseAPReq() {
		return new PauseAPReq();
	}

	/**
	 * Create an instance of {@link RecoveryAPRsp }
	 * 
	 */
	public RecoveryAPRsp createRecoveryAPRsp() {
		return new RecoveryAPRsp();
	}

	/**
	 * Create an instance of {@link AlarmRsp }
	 * 
	 */
	public AlarmRsp createAlarmRsp() {
		return new AlarmRsp();
	}

	/**
	 * Create an instance of {@link APRegistrationRsp }
	 * 
	 */
	public APRegistrationRsp createAPRegistrationRsp() {
		return new APRegistrationRsp();
	}

}
