package org.csapi.schema.wap;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ApplicationID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="addresses" type="{http://www.w3.org/2001/XMLSchema}anyURI" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="targetURL" type="{http://www.w3.org/2001/XMLSchema}anyURI"/>
 *         &lt;element name="ExtendCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="subject" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="receiptRequest" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "applicationID", "addresses", "targetURL",
		"extendCode", "subject", "receiptRequest" })
@XmlRootElement(name = "sendPushRequest")
public class SendPushRequest {

	@XmlElement(name = "ApplicationID", required = true, nillable = true)
	protected String applicationID;
	@XmlSchemaType(name = "anyURI")
	protected List<String> addresses;
	@XmlElement(required = true)
	@XmlSchemaType(name = "anyURI")
	protected String targetURL;
	@XmlElement(name = "ExtendCode", required = true, nillable = true)
	protected String extendCode;
	@XmlElement(required = true)
	protected String subject;
	protected boolean receiptRequest;

	/**
	 * Gets the value of the applicationID property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getApplicationID() {
		return applicationID;
	}

	/**
	 * Sets the value of the applicationID property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setApplicationID(String value) {
		this.applicationID = value;
	}

	/**
	 * Gets the value of the addresses property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the addresses property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getAddresses().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link String }
	 * 
	 * 
	 */
	public List<String> getAddresses() {
		if (addresses == null) {
			addresses = new ArrayList<String>();
		}
		return this.addresses;
	}

	/**
	 * Gets the value of the targetURL property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getTargetURL() {
		return targetURL;
	}

	/**
	 * Sets the value of the targetURL property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setTargetURL(String value) {
		this.targetURL = value;
	}

	/**
	 * Gets the value of the extendCode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getExtendCode() {
		return extendCode;
	}

	/**
	 * Sets the value of the extendCode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setExtendCode(String value) {
		this.extendCode = value;
	}

	/**
	 * Gets the value of the subject property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSubject() {
		return subject;
	}

	/**
	 * Sets the value of the subject property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSubject(String value) {
		this.subject = value;
	}

	/**
	 * Gets the value of the receiptRequest property.
	 * 
	 */
	public boolean isReceiptRequest() {
		return receiptRequest;
	}

	/**
	 * Sets the value of the receiptRequest property.
	 * 
	 */
	public void setReceiptRequest(boolean value) {
		this.receiptRequest = value;
	}

}
