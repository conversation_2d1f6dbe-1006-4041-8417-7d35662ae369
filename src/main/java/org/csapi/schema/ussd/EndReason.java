package org.csapi.schema.ussd;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for EndReason.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * <p>
 * 
 * <pre>
 * &lt;simpleType name="EndReason">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="UserEnd"/>
 *     &lt;enumeration value="Busy"/>
 *     &lt;enumeration value="UserAbsent"/>
 *     &lt;enumeration value="IllegalEquipment"/>
 *     &lt;enumeration value="SystemError"/>
 *     &lt;enumeration value="TimeOut"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "EndReason")
@XmlEnum
public enum EndReason {

	@XmlEnumValue("UserEnd")
	USER_END("UserEnd"), @XmlEnumValue("Busy")
	BUSY("Busy"), @XmlEnumValue("UserAbsent")
	USER_ABSENT("UserAbsent"), @XmlEnumValue("IllegalEquipment")
	ILLEGAL_EQUIPMENT("IllegalEquipment"), @XmlEnumValue("SystemError")
	SYSTEM_ERROR("SystemError"), @XmlEnumValue("TimeOut")
	TIME_OUT("TimeOut");
	private final String value;

	EndReason(String v) {
		value = v;
	}

	public String value() {
		return value;
	}

	public static EndReason fromValue(String v) {
		for (EndReason c : EndReason.values()) {
			if (c.value.equals(v)) {
				return c;
			}
		}
		throw new IllegalArgumentException(v);
	}

}
