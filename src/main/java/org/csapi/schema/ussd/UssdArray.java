package org.csapi.schema.ussd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for UssdArray complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="UssdArray">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ussdMessage" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ussdReturnRequest" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UssdArray", propOrder = { "ussdMessage", "ussdReturnRequest" })
public class UssdArray {

	@XmlElement(required = true)
	protected String ussdMessage;
	protected boolean ussdReturnRequest;

	/**
	 * Gets the value of the ussdMessage property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUssdMessage() {
		return ussdMessage;
	}

	/**
	 * Sets the value of the ussdMessage property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUssdMessage(String value) {
		this.ussdMessage = value;
	}

	/**
	 * Gets the value of the ussdReturnRequest property.
	 * 
	 */
	public boolean isUssdReturnRequest() {
		return ussdReturnRequest;
	}

	/**
	 * Sets the value of the ussdReturnRequest property.
	 * 
	 */
	public void setUssdReturnRequest(boolean value) {
		this.ussdReturnRequest = value;
	}

}
