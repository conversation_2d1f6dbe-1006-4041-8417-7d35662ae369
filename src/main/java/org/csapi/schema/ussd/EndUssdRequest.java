package org.csapi.schema.ussd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ApplicationID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="UssdMessage" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="UssdIdentifier" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "applicationID", "ussdMessage",
		"ussdIdentifier" })
@XmlRootElement(name = "endUssdRequest")
public class EndUssdRequest {

	@XmlElement(name = "ApplicationID", required = true, nillable = true)
	protected String applicationID;
	@XmlElement(name = "UssdMessage", required = true, nillable = true)
	protected String ussdMessage;
	@XmlElement(name = "UssdIdentifier", required = true, nillable = true)
	protected String ussdIdentifier;

	/**
	 * Gets the value of the applicationID property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getApplicationID() {
		return applicationID;
	}

	/**
	 * Sets the value of the applicationID property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setApplicationID(String value) {
		this.applicationID = value;
	}

	/**
	 * Gets the value of the ussdMessage property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUssdMessage() {
		return ussdMessage;
	}

	/**
	 * Sets the value of the ussdMessage property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUssdMessage(String value) {
		this.ussdMessage = value;
	}

	/**
	 * Gets the value of the ussdIdentifier property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUssdIdentifier() {
		return ussdIdentifier;
	}

	/**
	 * Sets the value of the ussdIdentifier property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUssdIdentifier(String value) {
		this.ussdIdentifier = value;
	}

}
