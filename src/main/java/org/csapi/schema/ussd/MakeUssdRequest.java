package org.csapi.schema.ussd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ApplicationID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="destinationAddress" type="{http://www.w3.org/2001/XMLSchema}anyURI"/>
 *         &lt;element name="ussdMessage" type="{http://www.csapi.org/schema/ussd}UssdArray"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "applicationID", "destinationAddress",
		"ussdMessage" })
@XmlRootElement(name = "makeUssdRequest")
public class MakeUssdRequest {

	@XmlElement(name = "ApplicationID", required = true, nillable = true)
	protected String applicationID;
	@XmlElement(required = true)
	@XmlSchemaType(name = "anyURI")
	protected String destinationAddress;
	@XmlElement(required = true)
	protected UssdArray ussdMessage;

	/**
	 * Gets the value of the applicationID property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getApplicationID() {
		return applicationID;
	}

	/**
	 * Sets the value of the applicationID property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setApplicationID(String value) {
		this.applicationID = value;
	}

	/**
	 * Gets the value of the destinationAddress property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDestinationAddress() {
		return destinationAddress;
	}

	/**
	 * Sets the value of the destinationAddress property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDestinationAddress(String value) {
		this.destinationAddress = value;
	}

	/**
	 * Gets the value of the ussdMessage property.
	 * 
	 * @return possible object is {@link UssdArray }
	 * 
	 */
	public UssdArray getUssdMessage() {
		return ussdMessage;
	}

	/**
	 * Sets the value of the ussdMessage property.
	 * 
	 * @param value
	 *            allowed object is {@link UssdArray }
	 * 
	 */
	public void setUssdMessage(UssdArray value) {
		this.ussdMessage = value;
	}

}
