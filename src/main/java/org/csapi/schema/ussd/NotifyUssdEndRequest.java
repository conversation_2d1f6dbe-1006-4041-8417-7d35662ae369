package org.csapi.schema.ussd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ussdIdentifier" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="endReason" type="{http://www.csapi.org/schema/ussd}EndReason"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "ussdIdentifier", "endReason" })
@XmlRootElement(name = "notifyUssdEndRequest")
public class NotifyUssdEndRequest {

	@XmlElement(required = true)
	protected String ussdIdentifier;
	@XmlElement(required = true)
	protected EndReason endReason;

	/**
	 * Gets the value of the ussdIdentifier property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUssdIdentifier() {
		return ussdIdentifier;
	}

	/**
	 * Sets the value of the ussdIdentifier property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUssdIdentifier(String value) {
		this.ussdIdentifier = value;
	}

	/**
	 * Gets the value of the endReason property.
	 * 
	 * @return possible object is {@link EndReason }
	 * 
	 */
	public EndReason getEndReason() {
		return endReason;
	}

	/**
	 * Sets the value of the endReason property.
	 * 
	 * @param value
	 *            allowed object is {@link EndReason }
	 * 
	 */
	public void setEndReason(EndReason value) {
		this.endReason = value;
	}

}
