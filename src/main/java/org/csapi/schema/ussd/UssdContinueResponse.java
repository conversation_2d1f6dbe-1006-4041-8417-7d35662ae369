package org.csapi.schema.ussd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="returnMessage" type="{http://www.csapi.org/schema/ussd}UssdArray"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "returnMessage" })
@XmlRootElement(name = "ussdContinueResponse")
public class UssdContinueResponse {

	@XmlElement(required = true)
	protected UssdArray returnMessage;

	/**
	 * Gets the value of the returnMessage property.
	 * 
	 * @return possible object is {@link UssdArray }
	 * 
	 */
	public UssdArray getReturnMessage() {
		return returnMessage;
	}

	/**
	 * Sets the value of the returnMessage property.
	 * 
	 * @param value
	 *            allowed object is {@link UssdArray }
	 * 
	 */
	public void setReturnMessage(UssdArray value) {
		this.returnMessage = value;
	}

}
