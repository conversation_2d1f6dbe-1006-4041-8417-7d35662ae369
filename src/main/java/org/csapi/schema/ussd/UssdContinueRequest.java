package org.csapi.schema.ussd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ussdIdentifier" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ussdMessage" type="{http://www.csapi.org/schema/ussd}UssdArray"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "ussdIdentifier", "ussdMessage" })
@XmlRootElement(name = "ussdContinueRequest")
public class UssdContinueRequest {

	@XmlElement(required = true)
	protected String ussdIdentifier;
	@XmlElement(required = true)
	protected UssdArray ussdMessage;

	/**
	 * Gets the value of the ussdIdentifier property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUssdIdentifier() {
		return ussdIdentifier;
	}

	/**
	 * Sets the value of the ussdIdentifier property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUssdIdentifier(String value) {
		this.ussdIdentifier = value;
	}

	/**
	 * Gets the value of the ussdMessage property.
	 * 
	 * @return possible object is {@link UssdArray }
	 * 
	 */
	public UssdArray getUssdMessage() {
		return ussdMessage;
	}

	/**
	 * Sets the value of the ussdMessage property.
	 * 
	 * @param value
	 *            allowed object is {@link UssdArray }
	 * 
	 */
	public void setUssdMessage(UssdArray value) {
		this.ussdMessage = value;
	}

}
