package org.csapi.schema.ussd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ussdIdentifier" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="senderAddress" type="{http://www.w3.org/2001/XMLSchema}anyURI"/>
 *         &lt;element name="ussdMessage" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "ussdIdentifier", "senderAddress",
		"ussdMessage" })
@XmlRootElement(name = "handleUssdRequest")
public class HandleUssdRequest {

	@XmlElement(required = true)
	protected String ussdIdentifier;
	@XmlElement(required = true)
	@XmlSchemaType(name = "anyURI")
	protected String senderAddress;
	@XmlElement(required = true)
	protected String ussdMessage;

	/**
	 * Gets the value of the ussdIdentifier property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUssdIdentifier() {
		return ussdIdentifier;
	}

	/**
	 * Sets the value of the ussdIdentifier property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUssdIdentifier(String value) {
		this.ussdIdentifier = value;
	}

	/**
	 * Gets the value of the senderAddress property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSenderAddress() {
		return senderAddress;
	}

	/**
	 * Sets the value of the senderAddress property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSenderAddress(String value) {
		this.senderAddress = value;
	}

	/**
	 * Gets the value of the ussdMessage property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUssdMessage() {
		return ussdMessage;
	}

	/**
	 * Sets the value of the ussdMessage property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUssdMessage(String value) {
		this.ussdMessage = value;
	}

}
