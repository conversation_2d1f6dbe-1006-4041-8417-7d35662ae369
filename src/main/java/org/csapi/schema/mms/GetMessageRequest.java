package org.csapi.schema.mms;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ApplicationID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="messageRefIdentifier" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "applicationID", "messageRefIdentifier" })
@XmlRootElement(name = "getMessageRequest")
public class GetMessageRequest {

	@XmlElement(name = "ApplicationID", required = true, nillable = true)
	protected String applicationID;
	@XmlElement(required = true)
	protected String messageRefIdentifier;

	/**
	 * Gets the value of the applicationID property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getApplicationID() {
		return applicationID;
	}

	/**
	 * Sets the value of the applicationID property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setApplicationID(String value) {
		this.applicationID = value;
	}

	/**
	 * Gets the value of the messageRefIdentifier property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getMessageRefIdentifier() {
		return messageRefIdentifier;
	}

	/**
	 * Sets the value of the messageRefIdentifier property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setMessageRefIdentifier(String value) {
		this.messageRefIdentifier = value;
	}

}
