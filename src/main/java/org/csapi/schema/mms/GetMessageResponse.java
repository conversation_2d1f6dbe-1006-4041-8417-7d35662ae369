package org.csapi.schema.mms;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="mmsMessage" type="{http://www.csapi.org/schema/mms}MmsMessage"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "mmsMessage" })
@XmlRootElement(name = "getMessageResponse")
public class GetMessageResponse {

	@XmlElement(required = true)
	protected MmsMessage mmsMessage;

	/**
	 * Gets the value of the mmsMessage property.
	 * 
	 * @return possible object is {@link MmsMessage }
	 * 
	 */
	public MmsMessage getMmsMessage() {
		return mmsMessage;
	}

	/**
	 * Sets the value of the mmsMessage property.
	 * 
	 * @param value
	 *            allowed object is {@link MmsMessage }
	 * 
	 */
	public void setMmsMessage(MmsMessage value) {
		this.mmsMessage = value;
	}

}
