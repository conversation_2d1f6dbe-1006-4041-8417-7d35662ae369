package org.csapi.schema.mms;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;

/**
 * <p>
 * Java class for MessageReference complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="MessageReference">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="messageIdentifier" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="messageServiceActivationNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="senderAddress" type="{http://www.w3.org/2001/XMLSchema}anyURI"/>
 *         &lt;element name="subject" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="priority" type="{http://www.csapi.org/schema/mms}MessagePriority"/>
 *         &lt;element name="message" type="{http://www.w3.org/2001/XMLSchema}base64Binary"/>
 *         &lt;element name="dateTime" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MessageReference", propOrder = { "messageIdentifier",
		"messageServiceActivationNumber", "senderAddress", "subject",
		"priority", "message", "dateTime" })
public class MessageReference {

	@XmlElement(required = true)
	protected String messageIdentifier;
	@XmlElement(required = true)
	protected String messageServiceActivationNumber;
	@XmlElement(required = true)
	@XmlSchemaType(name = "anyURI")
	protected String senderAddress;
	@XmlElement(required = true)
	protected String subject;
	@XmlElement(required = true)
	protected MessagePriority priority;
	@XmlElement(required = true)
	protected byte[] message;
	@XmlElement(required = true)
	@XmlSchemaType(name = "dateTime")
	protected XMLGregorianCalendar dateTime;

	/**
	 * Gets the value of the messageIdentifier property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getMessageIdentifier() {
		return messageIdentifier;
	}

	/**
	 * Sets the value of the messageIdentifier property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setMessageIdentifier(String value) {
		this.messageIdentifier = value;
	}

	/**
	 * Gets the value of the messageServiceActivationNumber property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getMessageServiceActivationNumber() {
		return messageServiceActivationNumber;
	}

	/**
	 * Sets the value of the messageServiceActivationNumber property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setMessageServiceActivationNumber(String value) {
		this.messageServiceActivationNumber = value;
	}

	/**
	 * Gets the value of the senderAddress property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSenderAddress() {
		return senderAddress;
	}

	/**
	 * Sets the value of the senderAddress property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSenderAddress(String value) {
		this.senderAddress = value;
	}

	/**
	 * Gets the value of the subject property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSubject() {
		return subject;
	}

	/**
	 * Sets the value of the subject property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSubject(String value) {
		this.subject = value;
	}

	/**
	 * Gets the value of the priority property.
	 * 
	 * @return possible object is {@link MessagePriority }
	 * 
	 */
	public MessagePriority getPriority() {
		return priority;
	}

	/**
	 * Sets the value of the priority property.
	 * 
	 * @param value
	 *            allowed object is {@link MessagePriority }
	 * 
	 */
	public void setPriority(MessagePriority value) {
		this.priority = value;
	}

	/**
	 * Gets the value of the message property.
	 * 
	 * @return possible object is byte[]
	 */
	public byte[] getMessage() {
		return message;
	}

	/**
	 * Sets the value of the message property.
	 * 
	 * @param value
	 *            allowed object is byte[]
	 */
	public void setMessage(byte[] value) {
		this.message = ((byte[]) value);
	}

	/**
	 * Gets the value of the dateTime property.
	 * 
	 * @return possible object is {@link XMLGregorianCalendar }
	 * 
	 */
	public XMLGregorianCalendar getDateTime() {
		return dateTime;
	}

	/**
	 * Sets the value of the dateTime property.
	 * 
	 * @param value
	 *            allowed object is {@link XMLGregorianCalendar }
	 * 
	 */
	public void setDateTime(XMLGregorianCalendar value) {
		this.dateTime = value;
	}

}
