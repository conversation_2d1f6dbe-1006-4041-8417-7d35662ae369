package org.csapi.schema.sms;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for DeliveryStatus.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * <p>
 * 
 * <pre>
 * &lt;simpleType name="DeliveryStatus">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="Delivered"/>
 *     &lt;enumeration value="DeliveryUncertain"/>
 *     &lt;enumeration value="DeliveryImpossible"/>
 *     &lt;enumeration value="MessageWaiting"/>
 *     &lt;enumeration value="DeliveryToTerminal"/>
 *     &lt;enumeration value="DeliveryNotificationNotSupported"/>
 *     &lt;enumeration value="KeyWordFilterFailed"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "DeliveryStatus")
@XmlEnum
public enum DeliveryStatus {

	@XmlEnumValue("Delivered")
	DELIVERED("Delivered"), @XmlEnumValue("DeliveryUncertain")
	DELIVERY_UNCERTAIN("DeliveryUncertain"), @XmlEnumValue("DeliveryImpossible")
	DELIVERY_IMPOSSIBLE("DeliveryImpossible"), @XmlEnumValue("MessageWaiting")
	MESSAGE_WAITING("MessageWaiting"), @XmlEnumValue("DeliveryToTerminal")
	DELIVERY_TO_TERMINAL("DeliveryToTerminal"), @XmlEnumValue("DeliveryNotificationNotSupported")
	DELIVERY_NOTIFICATION_NOT_SUPPORTED("DeliveryNotificationNotSupported"), @XmlEnumValue("KeyWordFilterFailed")
	KEY_WORD_FILTER_FAILED("KeyWordFilterFailed");
	private final String value;

	DeliveryStatus(String v) {
		value = v;
	}

	public String value() {
		return value;
	}

	public static DeliveryStatus fromValue(String v) {
		for (DeliveryStatus c : DeliveryStatus.values()) {
			if (c.value.equals(v)) {
				return c;
			}
		}
		throw new IllegalArgumentException(v);
	}

}
