package org.csapi.schema.sms;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for DeliveryInformation complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="DeliveryInformation">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Address" type="{http://www.w3.org/2001/XMLSchema}anyURI"/>
 *         &lt;element name="DeliveryStatus" type="{http://www.csapi.org/schema/sms}DeliveryStatus"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DeliveryInformation", propOrder = { "address",
		"deliveryStatus" })
public class DeliveryInformation {

	@XmlElement(name = "Address", required = true, nillable = true)
	@XmlSchemaType(name = "anyURI")
	protected String address;
	@XmlElement(name = "DeliveryStatus", required = true, nillable = true)
	protected DeliveryStatus deliveryStatus;

	/**
	 * Gets the value of the address property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * Sets the value of the address property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setAddress(String value) {
		this.address = value;
	}

	/**
	 * Gets the value of the deliveryStatus property.
	 * 
	 * @return possible object is {@link DeliveryStatus }
	 * 
	 */
	public DeliveryStatus getDeliveryStatus() {
		return deliveryStatus;
	}

	/**
	 * Sets the value of the deliveryStatus property.
	 * 
	 * @param value
	 *            allowed object is {@link DeliveryStatus }
	 * 
	 */
	public void setDeliveryStatus(DeliveryStatus value) {
		this.deliveryStatus = value;
	}

}
