package org.csapi.schema.sms;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for SMSMessage complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="SMSMessage">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Message" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SmsServiceActivationNumber" type="{http://www.w3.org/2001/XMLSchema}anyURI"/>
 *         &lt;element name="SenderAddress" type="{http://www.w3.org/2001/XMLSchema}anyURI"/>
 *         &lt;element name="MessageFormat" type="{http://www.csapi.org/schema/sms}MessageFormat"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SMSMessage", propOrder = { "message",
		"smsServiceActivationNumber", "senderAddress", "messageFormat" })
public class SMSMessage {

	@XmlElement(name = "Message", required = true, nillable = true)
	protected String message;
	@XmlElement(name = "SmsServiceActivationNumber", required = true, nillable = true)
	@XmlSchemaType(name = "anyURI")
	protected String smsServiceActivationNumber;
	@XmlElement(name = "SenderAddress", required = true, nillable = true)
	@XmlSchemaType(name = "anyURI")
	protected String senderAddress;
	@XmlElement(name = "MessageFormat", required = true, nillable = true)
	protected MessageFormat messageFormat;

	/**
	 * Gets the value of the message property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getMessage() {
		return message;
	}

	/**
	 * Sets the value of the message property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setMessage(String value) {
		this.message = value;
	}

	/**
	 * Gets the value of the smsServiceActivationNumber property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSmsServiceActivationNumber() {
		return smsServiceActivationNumber;
	}

	/**
	 * Sets the value of the smsServiceActivationNumber property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSmsServiceActivationNumber(String value) {
		this.smsServiceActivationNumber = value;
	}

	/**
	 * Gets the value of the senderAddress property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSenderAddress() {
		return senderAddress;
	}

	/**
	 * Sets the value of the senderAddress property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSenderAddress(String value) {
		this.senderAddress = value;
	}

	/**
	 * Gets the value of the messageFormat property.
	 * 
	 * @return possible object is {@link MessageFormat }
	 * 
	 */
	public MessageFormat getMessageFormat() {
		return messageFormat;
	}

	/**
	 * Sets the value of the messageFormat property.
	 * 
	 * @param value
	 *            allowed object is {@link MessageFormat }
	 * 
	 */
	public void setMessageFormat(MessageFormat value) {
		this.messageFormat = value;
	}

}
