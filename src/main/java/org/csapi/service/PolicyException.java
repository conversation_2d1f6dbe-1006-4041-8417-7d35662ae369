package org.csapi.service;

import javax.xml.ws.WebFault;

/**
 * This class was generated by the JAX-WS RI. JAX-WS RI 2.1.3-hudson-390-
 * Generated source version: 2.0
 * 
 */
@WebFault(name = "PolicyException", targetNamespace = "http://www.csapi.org/schema/common/v2_0")
public class PolicyException extends Exception {

	/**
	 * Java type that goes as soapenv:Fault detail element.
	 * 
	 */
	private org.csapi.schema.common.v2_0.PolicyException faultInfo;

	/**
	 * 
	 * @param message
	 * @param faultInfo
	 */
	public PolicyException(String message,
			org.csapi.schema.common.v2_0.PolicyException faultInfo) {
		super(message);
		this.faultInfo = faultInfo;
	}

	/**
	 * 
	 * @param message
	 * @param faultInfo
	 * @param cause
	 */
	public PolicyException(String message,
			org.csapi.schema.common.v2_0.PolicyException faultInfo,
			Throwable cause) {
		super(message, cause);
		this.faultInfo = faultInfo;
	}

	/**
	 * 
	 * @return returns fault bean: org.csapi.schema.common.v2_0.PolicyException
	 */
	public org.csapi.schema.common.v2_0.PolicyException getFaultInfo() {
		return faultInfo;
	}

}
