package org.csapi.service;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.logging.Logger;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;

/**
 * This class was generated by the JAX-WS RI. JAX-WS RI 2.1.3-hudson-390-
 * Generated source version: 2.0
 * <p>
 * An example of how this class may be used:
 * 
 * <pre>
 * cmcc_mas_wbs service = new cmcc_mas_wbs();
 * CmccMasWbs portType = service.getCmccMasWbs();
 * portType.apRegistration(...);
 * </pre>
 * 
 * </p>
 * 
 */
@WebServiceClient(name = "cmcc_mas_wbs", targetNamespace = "http://www.csapi.org/service", wsdlLocation = "file:/E:/\u8f6f\u4ef6\u9879\u76ee/010-\u804c\u536b\u9879\u76ee/001%20\u6c5f\u82cf\u7701\u804c\u4e1a\u536b\u751f/1020\u9700\u6c42\u5206\u6790/20211201\u77ed\u4fe1\u9a8c\u8bc1\u7801\u767b\u5f55/cmcc_mas_wbs.wsdl")
public class CmccMasWbs_Service extends Service {

	private final static URL CMCCMASWBS_WSDL_LOCATION;
	private final static Logger logger = Logger
			.getLogger(org.csapi.service.CmccMasWbs_Service.class.getName());

	static {
		URL url = null;
		try {
			URL baseUrl;
			baseUrl = org.csapi.service.CmccMasWbs_Service.class
					.getResource(".");
			url = new URL(
					baseUrl,
					"file:/E:/\u8f6f\u4ef6\u9879\u76ee/010-\u804c\u536b\u9879\u76ee/001%20\u6c5f\u82cf\u7701\u804c\u4e1a\u536b\u751f/1020\u9700\u6c42\u5206\u6790/20211201\u77ed\u4fe1\u9a8c\u8bc1\u7801\u767b\u5f55/cmcc_mas_wbs.wsdl");
		} catch (MalformedURLException e) {
			logger.warning("Failed to create URL for the wsdl Location: 'file:/E:/\u8f6f\u4ef6\u9879\u76ee/010-\u804c\u536b\u9879\u76ee/001%20\u6c5f\u82cf\u7701\u804c\u4e1a\u536b\u751f/1020\u9700\u6c42\u5206\u6790/20211201\u77ed\u4fe1\u9a8c\u8bc1\u7801\u767b\u5f55/cmcc_mas_wbs.wsdl', retrying as a local file");
			logger.warning(e.getMessage());
		}
		CMCCMASWBS_WSDL_LOCATION = url;
	}

	public CmccMasWbs_Service(URL wsdlLocation, QName serviceName) {
		super(wsdlLocation, serviceName);
	}

	public CmccMasWbs_Service() {
		super(CMCCMASWBS_WSDL_LOCATION, new QName(
				"http://www.csapi.org/service", "cmcc_mas_wbs"));
	}

	/**
	 * 
	 * @return returns CmccMasWbs
	 */
	@WebEndpoint(name = "cmcc_mas_wbs")
	public CmccMasWbs getCmccMasWbs() {
		return super.getPort(new QName("http://www.csapi.org/service",
				"cmcc_mas_wbs"), CmccMasWbs.class);
	}

}
