package org.csapi.service;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import org.csapi.schema.ap.APLogOutReq;
import org.csapi.schema.ap.APLogOutRsp;
import org.csapi.schema.ap.APRegistrationReq;
import org.csapi.schema.ap.APRegistrationRsp;
import org.csapi.schema.ap.APStatusRepReq;
import org.csapi.schema.ap.APStatusRepRsp;
import org.csapi.schema.ap.APSvcAuthenticReq;
import org.csapi.schema.ap.APSvcAuthenticRsp;
import org.csapi.schema.ap.APSvcPerfCmdReq;
import org.csapi.schema.ap.APSvcPerfReportReq;
import org.csapi.schema.ap.AlarmReq;
import org.csapi.schema.ap.AlarmRsp;
import org.csapi.schema.ap.PauseAPReq;
import org.csapi.schema.ap.PauseAPRsp;
import org.csapi.schema.ap.RecoveryAPReq;
import org.csapi.schema.ap.RecoveryAPRsp;
import org.csapi.schema.location.EndNotificationRequest;
import org.csapi.schema.location.GetLocationForGroupRequest;
import org.csapi.schema.location.GetLocationForGroupResponse;
import org.csapi.schema.location.GetLocationRequest;
import org.csapi.schema.location.GetLocationResponse;
import org.csapi.schema.location.LocationEndRequest;
import org.csapi.schema.location.LocationErrorRequest;
import org.csapi.schema.location.LocationNotificationRequest;
import org.csapi.schema.location.StartPeriodicNotificationRequest;
import org.csapi.schema.location.StartPeriodicNotificationResponse;
import org.csapi.schema.mms.GetMessageDeliveryStatusRequest;
import org.csapi.schema.mms.GetMessageDeliveryStatusResponse;
import org.csapi.schema.mms.GetMessageRequest;
import org.csapi.schema.mms.GetMessageResponse;
import org.csapi.schema.mms.GetReceivedMessagesRequest;
import org.csapi.schema.mms.GetReceivedMessagesResponse;
import org.csapi.schema.mms.NotifyMessageDeliveryReceiptRequest;
import org.csapi.schema.mms.NotifyMessageReceptionRequest;
import org.csapi.schema.mms.SendMessageRequest;
import org.csapi.schema.mms.SendMessageResponse;
import org.csapi.schema.notification.StartNotificationRequest;
import org.csapi.schema.notification.StopNotificationRequest;
import org.csapi.schema.sms.GetReceivedSmsRequest;
import org.csapi.schema.sms.GetReceivedSmsResponse;
import org.csapi.schema.sms.GetSmsDeliveryStatusRequest;
import org.csapi.schema.sms.GetSmsDeliveryStatusResponse;
import org.csapi.schema.sms.NotifySmsDeliveryStatusRequest;
import org.csapi.schema.sms.NotifySmsReceptionRequest;
import org.csapi.schema.sms.SendSmsRequest;
import org.csapi.schema.sms.SendSmsResponse;
import org.csapi.schema.ussd.EndUssdRequest;
import org.csapi.schema.ussd.HandleUssdRequest;
import org.csapi.schema.ussd.HandleUssdResponse;
import org.csapi.schema.ussd.MakeUssdRequest;
import org.csapi.schema.ussd.MakeUssdResponse;
import org.csapi.schema.ussd.NotifyUssdEndRequest;
import org.csapi.schema.ussd.UssdContinueRequest;
import org.csapi.schema.ussd.UssdContinueResponse;
import org.csapi.schema.wap.GetPushDeliveryStatusRequest;
import org.csapi.schema.wap.GetPushDeliveryStatusResponse;
import org.csapi.schema.wap.NotifyPushDeliveryReceiptRequest;
import org.csapi.schema.wap.SendPushRequest;
import org.csapi.schema.wap.SendPushResponse;

/**
 * This class was generated by the JAX-WS RI. JAX-WS RI 2.1.3-hudson-390-
 * Generated source version: 2.0
 * 
 */
@WebService(name = "cmcc_mas_wbs", targetNamespace = "http://www.csapi.org/service")
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface CmccMasWbs {

	/**
	 * 
	 * @param apRegistrationReq
	 * @return returns org.csapi.schema.ap.APRegistrationRsp
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(operationName = "APRegistration", action = "http://www.csapi.org/service/APRegistration")
	@WebResult(name = "APRegistrationRsp", targetNamespace = "http://www.csapi.org/schema/ap", partName = "APRegistrationRsp")
	public APRegistrationRsp apRegistration(
			@WebParam(name = "APRegistrationReq", targetNamespace = "http://www.csapi.org/schema/ap", partName = "APRegistrationReq") APRegistrationReq apRegistrationReq)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param apStatusRepReq
	 * @return returns org.csapi.schema.ap.APStatusRepRsp
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(operationName = "APStatusRep", action = "http://www.csapi.org/service/APStatusRep")
	@WebResult(name = "APStatusRepRsp", targetNamespace = "http://www.csapi.org/schema/ap", partName = "APStatusRepRsp")
	public APStatusRepRsp apStatusRep(
			@WebParam(name = "APStatusRepReq", targetNamespace = "http://www.csapi.org/schema/ap", partName = "APStatusRepReq") APStatusRepReq apStatusRepReq)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param apLogOutReq
	 * @return returns org.csapi.schema.ap.APLogOutRsp
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(operationName = "APLogOut", action = "http://www.csapi.org/service/APLogOut")
	@WebResult(name = "APLogOutRsp", targetNamespace = "http://www.csapi.org/schema/ap", partName = "APLogOutRsp")
	public APLogOutRsp apLogOut(
			@WebParam(name = "APLogOutReq", targetNamespace = "http://www.csapi.org/schema/ap", partName = "APLogOutReq") APLogOutReq apLogOutReq)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param pauseAPReq
	 * @return returns org.csapi.schema.ap.PauseAPRsp
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(operationName = "PauseAP", action = "http://www.csapi.org/service/PauseAP")
	@WebResult(name = "PauseAPRsp", targetNamespace = "http://www.csapi.org/schema/ap", partName = "PauseAPRsp")
	public PauseAPRsp pauseAP(
			@WebParam(name = "PauseAPReq", targetNamespace = "http://www.csapi.org/schema/ap", partName = "PauseAPReq") PauseAPReq pauseAPReq)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param recoveryAPReq
	 * @return returns org.csapi.schema.ap.RecoveryAPRsp
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(operationName = "RecoveryAP", action = "http://www.csapi.org/service/RecoveryAP")
	@WebResult(name = "RecoveryAPRsp", targetNamespace = "http://www.csapi.org/schema/ap", partName = "RecoveryAPRsp")
	public RecoveryAPRsp recoveryAP(
			@WebParam(name = "RecoveryAPReq", targetNamespace = "http://www.csapi.org/schema/ap", partName = "RecoveryAPReq") RecoveryAPReq recoveryAPReq)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param apSvcAuthenticReq
	 * @return returns org.csapi.schema.ap.APSvcAuthenticRsp
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(operationName = "APSvcAuthentic", action = "http://www.csapi.org/service/APSvcAuthentic")
	@WebResult(name = "APSvcAuthenticRsp", targetNamespace = "http://www.csapi.org/schema/ap", partName = "APSvcAuthenticRsp")
	public APSvcAuthenticRsp apSvcAuthentic(
			@WebParam(name = "APSvcAuthenticReq", targetNamespace = "http://www.csapi.org/schema/ap", partName = "APSvcAuthenticReq") APSvcAuthenticReq apSvcAuthenticReq)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param apSvcPerfCmdReq
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(operationName = "APSvcPerfCmd", action = "http://www.csapi.org/service/APSvcPerfCmd")
	public void apSvcPerfCmd(
			@WebParam(name = "APSvcPerfCmdReq", targetNamespace = "http://www.csapi.org/schema/ap", partName = "APSvcPerfCmdReq") APSvcPerfCmdReq apSvcPerfCmdReq)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param alarmReq
	 * @return returns org.csapi.schema.ap.AlarmRsp
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(operationName = "APSvcAlarm", action = "http://www.csapi.org/service/APSvcAlarm")
	@WebResult(name = "AlarmRsp", targetNamespace = "http://www.csapi.org/schema/ap", partName = "AlarmRsp")
	public AlarmRsp apSvcAlarm(
			@WebParam(name = "AlarmReq", targetNamespace = "http://www.csapi.org/schema/ap", partName = "AlarmReq") AlarmReq alarmReq)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param apSvcPerfReportReq
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(operationName = "APSvcPerfReport", action = "http://www.csapi.org/service/APSvcPerfReport")
	public void apSvcPerfReport(
			@WebParam(name = "APSvcPerfReportReq", targetNamespace = "http://www.csapi.org/schema/ap", partName = "APSvcPerfReportReq") APSvcPerfReportReq apSvcPerfReportReq)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param startNotificationRequest
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/startNotification")
	public void startNotification(
			@WebParam(name = "startNotificationRequest", targetNamespace = "http://www.csapi.org/schema/notification", partName = "startNotificationRequest") StartNotificationRequest startNotificationRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param stopNotificationRequest
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/stopNotification")
	public void stopNotification(
			@WebParam(name = "stopNotificationRequest", targetNamespace = "http://www.csapi.org/schema/notification", partName = "stopNotificationRequest") StopNotificationRequest stopNotificationRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param sendSmsRequest
	 * @return returns org.csapi.schema.sms.SendSmsResponse
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/sendSms")
	@WebResult(name = "sendSmsResponse", targetNamespace = "http://www.csapi.org/schema/sms", partName = "sendSmsResponse")
	public SendSmsResponse sendSms(
			@WebParam(name = "sendSmsRequest", targetNamespace = "http://www.csapi.org/schema/sms", partName = "sendSmsRequest") SendSmsRequest sendSmsRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param notifySmsDeliveryStatusRequest
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(action = "http://www.csapi.org/service/notifySmsDeliveryStatus")
	public void notifySmsDeliveryStatus(
			@WebParam(name = "notifySmsDeliveryStatusRequest", targetNamespace = "http://www.csapi.org/schema/sms", partName = "notifySmsDeliveryStatusRequest") NotifySmsDeliveryStatusRequest notifySmsDeliveryStatusRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param getReceivedSmsRequest
	 * @return returns org.csapi.schema.sms.GetReceivedSmsResponse
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(operationName = "GetReceivedSms", action = "http://www.csapi.org/service/GetReceivedSms")
	@WebResult(name = "GetReceivedSmsResponse", targetNamespace = "http://www.csapi.org/schema/sms", partName = "GetReceivedSmsResponse")
	public GetReceivedSmsResponse getReceivedSms(
			@WebParam(name = "GetReceivedSmsRequest", targetNamespace = "http://www.csapi.org/schema/sms", partName = "GetReceivedSmsRequest") GetReceivedSmsRequest getReceivedSmsRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param getSmsDeliveryStatusRequest
	 * @return returns org.csapi.schema.sms.GetSmsDeliveryStatusResponse
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(operationName = "GetSmsDeliveryStatus", action = "http://www.csapi.org/service/GetSmsDeliveryStatus")
	@WebResult(name = "GetSmsDeliveryStatusResponse", targetNamespace = "http://www.csapi.org/schema/sms", partName = "GetSmsDeliveryStatusResponse")
	public GetSmsDeliveryStatusResponse getSmsDeliveryStatus(
			@WebParam(name = "GetSmsDeliveryStatusRequest", targetNamespace = "http://www.csapi.org/schema/sms", partName = "GetSmsDeliveryStatusRequest") GetSmsDeliveryStatusRequest getSmsDeliveryStatusRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param notifySmsReceptionRequest
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/notifySmsReception")
	public void notifySmsReception(
			@WebParam(name = "notifySmsReceptionRequest", targetNamespace = "http://www.csapi.org/schema/sms", partName = "notifySmsReceptionRequest") NotifySmsReceptionRequest notifySmsReceptionRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param sendMessageRequest
	 * @return returns org.csapi.schema.mms.SendMessageResponse
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/sendMessage")
	@WebResult(name = "sendMessageResponse", targetNamespace = "http://www.csapi.org/schema/mms", partName = "sendMessageResponse")
	public SendMessageResponse sendMessage(
			@WebParam(name = "sendMessageRequest", targetNamespace = "http://www.csapi.org/schema/mms", partName = "sendMessageRequest") SendMessageRequest sendMessageRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param getMessageDeliveryStatusRequest
	 * @return returns org.csapi.schema.mms.GetMessageDeliveryStatusResponse
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/getMessageDeliveryStatus")
	@WebResult(name = "getMessageDeliveryStatusResponse", targetNamespace = "http://www.csapi.org/schema/mms", partName = "getMessageDeliveryStatusResponse")
	public GetMessageDeliveryStatusResponse getMessageDeliveryStatus(
			@WebParam(name = "getMessageDeliveryStatusRequest", targetNamespace = "http://www.csapi.org/schema/mms", partName = "getMessageDeliveryStatusRequest") GetMessageDeliveryStatusRequest getMessageDeliveryStatusRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param getReceivedMessagesRequest
	 * @return returns org.csapi.schema.mms.GetReceivedMessagesResponse
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/getReceivedMessages")
	@WebResult(name = "getReceivedMessagesResponse", targetNamespace = "http://www.csapi.org/schema/mms", partName = "getReceivedMessagesResponse")
	public GetReceivedMessagesResponse getReceivedMessages(
			@WebParam(name = "getReceivedMessagesRequest", targetNamespace = "http://www.csapi.org/schema/mms", partName = "getReceivedMessagesRequest") GetReceivedMessagesRequest getReceivedMessagesRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param getMessageRequest
	 * @return returns org.csapi.schema.mms.GetMessageResponse
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/getMessage")
	@WebResult(name = "getMessageResponse", targetNamespace = "http://www.csapi.org/schema/mms", partName = "getMessageResponse")
	public GetMessageResponse getMessage(
			@WebParam(name = "getMessageRequest", targetNamespace = "http://www.csapi.org/schema/mms", partName = "getMessageRequest") GetMessageRequest getMessageRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param notifyMessageReceptionRequest
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(action = "http://www.csapi.org/service/notifyMessageReception")
	public void notifyMessageReception(
			@WebParam(name = "notifyMessageReceptionRequest", targetNamespace = "http://www.csapi.org/schema/mms", partName = "notifyMessageReceptionRequest") NotifyMessageReceptionRequest notifyMessageReceptionRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param notifyMessageDeliveryReceiptRequest
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/notifyMessageDeliveryReceipt")
	public void notifyMessageDeliveryReceipt(
			@WebParam(name = "notifyMessageDeliveryReceiptRequest", targetNamespace = "http://www.csapi.org/schema/mms", partName = "notifyMessageDeliveryReceiptRequest") NotifyMessageDeliveryReceiptRequest notifyMessageDeliveryReceiptRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param getLocationRequest
	 * @return returns org.csapi.schema.location.GetLocationResponse
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(action = "http://www.csapi.org/service/getLocation")
	@WebResult(name = "getLocationResponse", targetNamespace = "http://www.csapi.org/schema/location", partName = "getLocationResponse")
	public GetLocationResponse getLocation(
			@WebParam(name = "getLocationRequest", targetNamespace = "http://www.csapi.org/schema/location", partName = "getLocationRequest") GetLocationRequest getLocationRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param getLocationForGroupRequest
	 * @return returns org.csapi.schema.location.GetLocationForGroupResponse
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(action = "http://www.csapi.org/service/getLocationForGroup")
	@WebResult(name = "getLocationForGroupResponse", targetNamespace = "http://www.csapi.org/schema/location", partName = "getLocationForGroupResponse")
	public GetLocationForGroupResponse getLocationForGroup(
			@WebParam(name = "getLocationForGroupRequest", targetNamespace = "http://www.csapi.org/schema/location", partName = "getLocationForGroupRequest") GetLocationForGroupRequest getLocationForGroupRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param startPeriodicNotificationRequest
	 * @return returns
	 *         org.csapi.schema.location.StartPeriodicNotificationResponse
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(action = "http://www.csapi.org/service/startPeriodicNotification")
	@WebResult(name = "startPeriodicNotificationResponse", targetNamespace = "http://www.csapi.org/schema/location", partName = "startPeriodicNotificationResponse")
	public StartPeriodicNotificationResponse startPeriodicNotification(
			@WebParam(name = "startPeriodicNotificationRequest", targetNamespace = "http://www.csapi.org/schema/location", partName = "startPeriodicNotificationRequest") StartPeriodicNotificationRequest startPeriodicNotificationRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param endNotificationRequest
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(operationName = "EndNotification", action = "http://www.csapi.org/service/EndNotification")
	public void endNotification(
			@WebParam(name = "EndNotificationRequest", targetNamespace = "http://www.csapi.org/schema/location", partName = "EndNotificationRequest") EndNotificationRequest endNotificationRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param locationNotificationRequest
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(operationName = "LocationNotification", action = "http://www.csapi.org/service/LocationNotification")
	public void locationNotification(
			@WebParam(name = "LocationNotificationRequest", targetNamespace = "http://www.csapi.org/schema/location", partName = "LocationNotificationRequest") LocationNotificationRequest locationNotificationRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param locationErrorRequest
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(operationName = "LocationError", action = "http://www.csapi.org/service/LocationError")
	public void locationError(
			@WebParam(name = "LocationErrorRequest", targetNamespace = "http://www.csapi.org/schema/location", partName = "LocationErrorRequest") LocationErrorRequest locationErrorRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param locationEndRequest
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(operationName = "LocationEnd", action = "http://www.csapi.org/service/LocationEnd")
	public void locationEnd(
			@WebParam(name = "LocationEndRequest", targetNamespace = "http://www.csapi.org/schema/location", partName = "LocationEndRequest") LocationEndRequest locationEndRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param sendPushRequest
	 * @return returns org.csapi.schema.wap.SendPushResponse
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(action = "http://www.csapi.org/service/sendPush")
	@WebResult(name = "sendPushResponse", targetNamespace = "http://www.csapi.org/schema/wap", partName = "sendPushResponse")
	public SendPushResponse sendPush(
			@WebParam(name = "sendPushRequest", targetNamespace = "http://www.csapi.org/schema/wap", partName = "sendPushRequest") SendPushRequest sendPushRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param getPushDeliveryStatusRequest
	 * @return returns org.csapi.schema.wap.GetPushDeliveryStatusResponse
	 * @throws PolicyException
	 * @throws ServiceException
	 */
	@WebMethod(action = "http://www.csapi.org/service/getPushDeliveryStatus")
	@WebResult(name = "getPushDeliveryStatusResponse", targetNamespace = "http://www.csapi.org/schema/wap", partName = "getPushDeliveryStatusResponse")
	public GetPushDeliveryStatusResponse getPushDeliveryStatus(
			@WebParam(name = "getPushDeliveryStatusRequest", targetNamespace = "http://www.csapi.org/schema/wap", partName = "getPushDeliveryStatusRequest") GetPushDeliveryStatusRequest getPushDeliveryStatusRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param notifyPushDeliveryReceiptRequest
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/notifyPushDeliveryReceipt")
	public void notifyPushDeliveryReceipt(
			@WebParam(name = "notifyPushDeliveryReceiptRequest", targetNamespace = "http://www.csapi.org/schema/wap", partName = "notifyPushDeliveryReceiptRequest") NotifyPushDeliveryReceiptRequest notifyPushDeliveryReceiptRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param makeUssdRequest
	 * @return returns org.csapi.schema.ussd.MakeUssdResponse
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/makeUssd")
	@WebResult(name = "makeUssdResponse", targetNamespace = "http://www.csapi.org/schema/ussd", partName = "makeUssdResponse")
	public MakeUssdResponse makeUssd(
			@WebParam(name = "makeUssdRequest", targetNamespace = "http://www.csapi.org/schema/ussd", partName = "makeUssdRequest") MakeUssdRequest makeUssdRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param handleUssdRequest
	 * @return returns org.csapi.schema.ussd.HandleUssdResponse
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/handleUssd")
	@WebResult(name = "handleUssdResponse", targetNamespace = "http://www.csapi.org/schema/ussd", partName = "handleUssdResponse")
	public HandleUssdResponse handleUssd(
			@WebParam(name = "handleUssdRequest", targetNamespace = "http://www.csapi.org/schema/ussd", partName = "handleUssdRequest") HandleUssdRequest handleUssdRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param ussdContinueRequest
	 * @return returns org.csapi.schema.ussd.UssdContinueResponse
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/ussdContinue")
	@WebResult(name = "ussdContinueResponse", targetNamespace = "http://www.csapi.org/schema/ussd", partName = "ussdContinueResponse")
	public UssdContinueResponse ussdContinue(
			@WebParam(name = "ussdContinueRequest", targetNamespace = "http://www.csapi.org/schema/ussd", partName = "ussdContinueRequest") UssdContinueRequest ussdContinueRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param endUssdRequest
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/endUssd")
	public void endUssd(
			@WebParam(name = "endUssdRequest", targetNamespace = "http://www.csapi.org/schema/ussd", partName = "endUssdRequest") EndUssdRequest endUssdRequest)
			throws PolicyException, ServiceException;

	/**
	 * 
	 * @param notifyUssdEndRequest
	 * @throws ServiceException
	 * @throws PolicyException
	 */
	@WebMethod(action = "http://www.csapi.org/service/notifyUssdEnd")
	public void notifyUssdEnd(
			@WebParam(name = "notifyUssdEndRequest", targetNamespace = "http://www.csapi.org/schema/ussd", partName = "notifyUssdEndRequest") NotifyUssdEndRequest notifyUssdEndRequest)
			throws PolicyException, ServiceException;

}
