package customComponent;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.component.UIInput;
import javax.faces.component.UINamingContainer;

import org.primefaces.component.api.UITree;
import org.primefaces.component.inputtext.InputText;
import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeExpandEvent;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;

/**
 * 新版地区单选组件
 * <AUTHOR>
 */
public class ZoneSingleImproveComp extends UINamingContainer implements Serializable {

	private static final long serialVersionUID = -7618305755770492110L;
	private UITree uiTree;
    private TreeNode selectedNode;
    private InputText nameInput;
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    /**地区rid*/
    private UIInput idInput;
    /**地区编码*/
    private UIInput codeInput;
    /**新版地区编码12位*/
    private UIInput newCodeInput;
    /**地区级别*/
    private UIInput levelInput;

    /**
     * 初始化地区
     */
    public void initZoneTree() {
    	TreeNode rootNode = uiTree.getValue();
        if(null != rootNode && rootNode.getChildCount() > 0)
        	return;
        rootNode = new DefaultTreeNode("root", null);
        Map<String, Object> attrs = this.getAttributes();
        if(!StringUtils.isNotBlank((String)attrs.get("userZoneCode")))
        	return;
        List<TsZone> list = this.commService.findZoneListImprove(
        		(Boolean)attrs.get("isAdmin"), (String)attrs.get("userZoneCode"), (String)attrs.get("zoneTypeMin"));
        if(null != list && list.size() > 0) {
            Map<String, TreeNode> map = new HashMap<String, TreeNode>();
            TsZone top = list.get(0);

            Short topLvl = top.getZoneType();
            TreeNode temNode = new DefaultTreeNode(top, rootNode);
            map.put(top.getZoneCode(), temNode);

            if(list.size() > 1) {
                for(int i=1; i<list.size(); i++) {
                    TsZone t = list.get(i);
                    if(topLvl.equals(t.getZoneType())) {
                        TreeNode node = new DefaultTreeNode(t, rootNode);
                        map.put(t.getZoneCode(), node);
                    }else {
                        String parentCode = ZoneUtil.getParentCode(t.getZoneCode());
                        if(StringUtils.isNotBlank(parentCode)) {
                            TreeNode parentNode = map.get(parentCode);
                            if(null != parentNode) {
                                TreeNode node = new DefaultTreeNode(t, parentNode);
                                map.put(t.getZoneCode(), node);
                            }
                        }
                    }
                }
            }
            map.clear();
        }
        uiTree.setValue(rootNode);
        String clientId = this.getClientId();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.update(clientId+ ":zoneTree");
        
    }

    /**
     * 地区选中事件
     * @param event 节点选中事件
     */
    public void onNodeSelect(NodeSelectEvent event) {
        TreeNode node = event.getTreeNode();
        TsZone zone = (TsZone) node.getData();
        nameInput.setValue(zone.getZoneName());
        idInput.setValue(zone.getRid());
        codeInput.setValue(zone.getZoneGb());
        newCodeInput.setValue(zone.getZoneCode());
        levelInput.setValue(zone.getZoneType());
    }
    
    /**
     * 地区展开
     * @param event 节点展开事件
     */
    public void onNodeExpand(NodeExpandEvent event){
    	TreeNode node = event.getTreeNode();
        TsZone zone = (TsZone) node.getData();
        
        Map<String, Object> attrs = this.getAttributes();
        Integer zoneTypeMax = 6;
        try{
        	zoneTypeMax = Integer.valueOf((String)attrs.get("zoneTypeMax"));
        }catch(NumberFormatException e){
        	zoneTypeMax=6;
        }
        if((zone.getZoneType()+2)>zoneTypeMax)
        	return;
    	//获取下两级地区数据
    	List<TsZone> remoteList = this.commService.getNextLvZone(zone.getZoneCode(),zone.getZoneType()+2);
    	if(null == remoteList || remoteList.size()<=0)
    		return;
    	//获取下一级树节点
    	List<TreeNode> nodeList = node.getChildren();
    	//封装树节点到Map
    	Map<String, TreeNode> map = new HashMap<String, TreeNode>();
    	for(TreeNode nodeItem : nodeList)
    		map.put(((TsZone)nodeItem.getData()).getZoneCode(), nodeItem);
    	//迭代子级数据,延续树
    	for(TsZone tzItem : remoteList){
    		String parentCode = ZoneUtil.getParentCode(tzItem.getZoneCode());
            if(StringUtils.isNotBlank(parentCode)) {
                TreeNode parentNode = map.get(parentCode);
                if(null != parentNode) {
                    new DefaultTreeNode(tzItem, parentNode);
                }
            }
    	}
    	
    	//更新树
    	String clientId = this.getClientId();
    	RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.update(clientId+ ":zoneTree");
    }

    public UITree getUiTree() {
        return uiTree;
    }

    public void setUiTree(UITree uiTree) {
        this.uiTree = uiTree;
    }

    public TreeNode getSelectedNode() {
        return selectedNode;
    }

    public void setSelectedNode(TreeNode selectedNode) {
        this.selectedNode = selectedNode;
    }

    public InputText getNameInput() {
        return nameInput;
    }

    public void setNameInput(InputText nameInput) {
        this.nameInput = nameInput;
    }

    public UIInput getIdInput() {
        return idInput;
    }

    public void setIdInput(UIInput idInput) {
        this.idInput = idInput;
    }

	public UIInput getCodeInput() {
		return codeInput;
	}

	public void setCodeInput(UIInput codeInput) {
		this.codeInput = codeInput;
	}

	public UIInput getLevelInput() {
		return levelInput;
	}

	public void setLevelInput(UIInput levelInput) {
		this.levelInput = levelInput;
	}

	public UIInput getNewCodeInput() {
		return newCodeInput;
	}

	public void setNewCodeInput(UIInput newCodeInput) {
		this.newCodeInput = newCodeInput;
	}

	public CommServiceImpl getCommService() {
		return commService;
	}

	public void setCommService(CommServiceImpl commService) {
		this.commService = commService;
	}
}
