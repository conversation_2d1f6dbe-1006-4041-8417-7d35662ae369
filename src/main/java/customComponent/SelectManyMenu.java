package customComponent;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.faces.component.UIInput;
import javax.faces.component.UINamingContainer;
import javax.faces.component.UISelectItems;

import org.primefaces.component.inputtext.InputText;
import org.primefaces.context.RequestContext;

import com.chis.common.utils.MapUtils;
import com.chis.common.utils.StringUtils;


/**
 * 多选下拉组件
 * 只支持数据类型为map, key-String.class value-String.class
 *
 * <AUTHOR>
 * @createDate 2014年9月12日上午9:03:43
 */
public class SelectManyMenu extends UINamingContainer implements Serializable {

	private static final long serialVersionUID = -441884568995488876L;
	/**map keys*/
	private InputText labelInput;
	/**map values*/
    private UIInput valueInput;
    /**多选框*/
    private UIInput selectManyCheckBox;
    /**选中的值*/
    private List<String> selectedList = new ArrayList<String>();
    /**标准库f:selectItems*/
    private UISelectItems selectItems;

    /**
     * 初始化组件
     */
    public void init() {
    	//已选中值不为空，要初始化
    	String value = (String) this.valueInput.getValue();
    	if(StringUtils.isNotBlank(value)) {
    		//标准库map
    		Map<String,String> map = (Map<String, String>) this.selectItems.getValue();
    		if(null != map && map.size() > 0) {
    			String[] split = value.split(",");
    			StringBuilder sb = new StringBuilder();
    			for(String s:split) {
    				this.selectedList.add(s);
    				sb.append(",").append(MapUtils.findKey(map, s));
    			}
    			this.labelInput.setValue(sb.toString().substring(1));
    		}else {
    			throw new RuntimeException(this.getClientId() + "组件有误！");
    		}
    	}
            
        String clientId = this.getClientId();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.update(clientId+ ":manyCheckbox");
        requestContext.update(clientId+ ":dataLabel");
    }
    
    /**
     * 关闭时的选中事件，将选中的key,value设置到相应的控件上
     */
    public void onSelect() {
    	if(null != this.selectedList && this.selectedList.size() > 0) {
    		StringBuilder keyBuilder = new StringBuilder();
    		StringBuilder valueBuilder = new StringBuilder();
			Map<String,String> map = (Map<String, String>) this.selectItems.getValue();
			if(null != map && map.size() > 0) {
				for(String s: this.selectedList) {
	    			valueBuilder.append(",").append(s);
	    			keyBuilder.append(",").append(MapUtils.findKey(map, s));
	    		}
				this.labelInput.setValue(keyBuilder.toString().substring(1));
				this.valueInput.setValue(valueBuilder.toString().substring(1));
			}else {
    			throw new RuntimeException(this.getClientId() + "组件有误！");
    		}
    	}else {
    		this.labelInput.setValue("");
    		this.valueInput.setValue("");
    	}
    	
        String clientId = this.getClientId();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.update(clientId+ ":dataValue");
        requestContext.update(clientId+ ":dataLabel");
    }

	public InputText getLabelInput() {
		return labelInput;
	}

	public void setLabelInput(InputText labelInput) {
		this.labelInput = labelInput;
	}

	public UIInput getValueInput() {
		return valueInput;
	}

	public void setValueInput(UIInput valueInput) {
		this.valueInput = valueInput;
	}

	public UIInput getSelectManyCheckBox() {
		return selectManyCheckBox;
	}

	public void setSelectManyCheckBox(UIInput selectManyCheckBox) {
		this.selectManyCheckBox = selectManyCheckBox;
	}

	public List<String> getSelectedList() {
		return selectedList;
	}

	public void setSelectedList(List<String> selectedList) {
		this.selectedList = selectedList;
	}

	public UISelectItems getSelectItems() {
		return selectItems;
	}

	public void setSelectItems(UISelectItems selectItems) {
		this.selectItems = selectItems;
	}

}
