package customComponent;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.logic.ZoneUnitVO;
import org.primefaces.component.api.UITree;
import org.primefaces.component.inputtext.InputText;
import org.primefaces.context.RequestContext;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.TreeNode;
import org.springframework.util.CollectionUtils;

import javax.faces.component.UIInput;
import javax.faces.component.UINamingContainer;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  <p>类描述：管辖地区本级及下级地区组合机构(系统单位)</p>
 * @ClassAuthor hsj 2022-09-20 11:19
 */
public class ZoneUnitManyComp extends UINamingContainer implements Serializable{

	private static final long serialVersionUID = -7618305755770492110L;
    //树-对象
    private UITree uiTree;
    //单位名称
    private InputText nameInput;
    //已选择的节点rid
    private UIInput unitRidsInput;
    private TreeNode[] selectedExportNodes;
    private List<TreeNode> nodes;

    /**
     * 初始化树
     */
    public void initZoneUnitTree() {
        TreeNode rootNode = uiTree.getValue();
        this.selectedExportNodes = null;
        this.nodes = new ArrayList<>();
        //选中的
        String unitRids = unitRidsInput.getValue() == null ? null : unitRidsInput.getValue().toString();
        List<String> rids = new ArrayList<>();
        if(StringUtils.isNotBlank(unitRids)){
            rids = StringUtils.string2list(unitRids,",");
        }
        List<ZoneUnitVO>  zoneUnitVOs = (List<ZoneUnitVO>) this.getAttributes().get("zoneUnitList");
        if(null == rootNode || rootNode.getChildCount() <=0 ) {
            rootNode = new CheckboxTreeNode("root", null);
            if (!CollectionUtils.isEmpty(zoneUnitVOs)) {
                Map<String, TreeNode> map = new HashMap<String, TreeNode>();
                ZoneUnitVO   zoneUnit = zoneUnitVOs.get(0);
                TreeNode temNode = new CheckboxTreeNode(zoneUnit, rootNode);
                map.put(zoneUnit.getZoneGb(),temNode);
                if(zoneUnitVOs.size() > 1) {
                    for (int i = 1; i < zoneUnitVOs.size(); i++) {
                        ZoneUnitVO   zoneUnitVO = zoneUnitVOs.get(i);
                        if (map.containsKey(zoneUnitVO.getZoneGb())) {
                            TreeNode node = map.get(zoneUnitVO.getZoneGb());
                            TreeNode node1 =  new CheckboxTreeNode(zoneUnitVO, node);
                            if(null != zoneUnitVO.getRid() && !CollectionUtils.isEmpty(rids) && rids.contains(zoneUnitVO.getRid().toString())){
                                node1.setSelected(true);
                                nodes.add(node);
                            }
                        } else {
                            TreeNode node = new CheckboxTreeNode(zoneUnitVO, temNode);
                            map.put(zoneUnitVO.getZoneGb(), node);
                        }

                    }
                }
                map.clear();
                nodesSelected();
            }
            uiTree.setValue(rootNode);
            String clientId = this.getClientId();
            RequestContext requestContext = RequestContext.getCurrentInstance();
            requestContext.update(clientId+ ":zoneUnitTree");
        }else{
            rootNode.setSelected(false);
            clearNodeSelected(rootNode, unitRidsInput);
            nodesSelected();
            String clientId = this.getClientId();
            RequestContext requestContext = RequestContext.getCurrentInstance();
            requestContext.update(clientId+ ":zoneUnitTree");
        }
    }
    /**
     *  <p>方法描述：已选节点初始化</p>
     * @MethodAuthor hsj 2022-09-26 15:14
     */
    private void nodesSelected() {
        this.selectedExportNodes = null;
        if(!CollectionUtils.isEmpty(this.nodes)){
            selectedExportNodes = new TreeNode[(this.nodes.size())];
            for(int i = 0 ; i< nodes.size() ;i++){
                selectedExportNodes[i] = nodes.get(i);
            }
        }
    }
    /**
     *  <p>方法描述：节点是否选中初始化</p>
     * @MethodAuthor hsj 2022-09-26 15:14
     */
    private void clearNodeSelected(TreeNode rootNode, UIInput unitRidsInput) {
        String unitRids = unitRidsInput.getValue() == null ? null : unitRidsInput.getValue().toString();
        List<String> rids = new ArrayList<>();
        if(StringUtils.isNotBlank(unitRids)){
            rids = StringUtils.string2list(unitRids,",");
        }
        if (null != rootNode) {
            List<TreeNode> children = rootNode.getChildren();
            if (null != children && children.size() > 0) {
                for (TreeNode node1 : children) {
                    ZoneUnitVO zoneUnitVO = (ZoneUnitVO) node1.getData();
                    if(null != zoneUnitVO.getRid() && !CollectionUtils.isEmpty(rids) && rids.contains(zoneUnitVO.getRid().toString())){
                        node1.setSelected(true);
                        nodes.add(node1);
                    }else {
                        node1.setSelected(false);
                    }
                    clearNodeSelected(node1, unitRidsInput);
                }
            }
        }

    }


    /**
     *  <p>方法描述：</p>
     * @MethodAuthor hsj 2022-09-21 11:35
     */
    public void hideAction() {
        this.unitRidsInput.setValue("");
        this.nameInput.setValue("");
        if(selectedExportNodes != null &&  selectedExportNodes.length >= 0){
            List<String> nameStr= new ArrayList<>();
            List<String> rids = new ArrayList<>();
            for (TreeNode node : this.selectedExportNodes) {
                ZoneUnitVO zoneUnitVO = (ZoneUnitVO) node.getData();
                if(null != zoneUnitVO.getRid() && !rids.contains(zoneUnitVO.getRid().toString())){
                    nameStr.add(zoneUnitVO.getName());
                    rids.add(zoneUnitVO.getRid().toString());
                }
            }
            if(!CollectionUtils.isEmpty(nameStr)){
                this.nameInput.setValue(StringUtils.join(nameStr, "，"));
            }
            if(!CollectionUtils.isEmpty(nameStr)){
                this.unitRidsInput.setValue(StringUtils.join(rids, ","));
            }
        }
        String clientId = this.getClientId();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.update(clientId + ":unitName");
        requestContext.update(clientId + ":unitRids");
    }

    /**
    *  <p>方法描述：清空所选择的单位信息</p>
    * @MethodAuthor hsj 2022-09-20 11:53
    */
    public void clearCodeName() {
        this.nameInput.setValue("");
        this.unitRidsInput.setValue("");
        this.selectedExportNodes = null;
        TreeNode rootNode = uiTree.getValue();
        if(null != rootNode) {
            List<TreeNode> children = rootNode.getChildren();
            if (null != children && children.size() > 0) {
                for (TreeNode node : children) {
                    node.setSelected(false);
                    List<TreeNode> children2 = node.getChildren();
                    if (null != children2 && children2.size() > 0) {
                        for (TreeNode node2 : children2) {
                            node2.setSelected(false);
                        }
                    }
                }
            }
        }
    }


    public UITree getUiTree() {
        return uiTree;
    }

    public void setUiTree(UITree uiTree) {
        this.uiTree = uiTree;
    }


    public InputText getNameInput() {
        return nameInput;
    }

    public void setNameInput(InputText nameInput) {
        this.nameInput = nameInput;
    }

    public UIInput getUnitRidsInput() {
        return unitRidsInput;
    }

    public void setUnitRidsInput(UIInput unitRidsInput) {
        this.unitRidsInput = unitRidsInput;
    }

    public TreeNode[] getSelectedExportNodes() {
        return selectedExportNodes;
    }

    public void setSelectedExportNodes(TreeNode[] selectedExportNodes) {
        this.selectedExportNodes = selectedExportNodes;
    }

    public List<TreeNode> getNodes() {
        return nodes;
    }

    public void setNodes(List<TreeNode> nodes) {
        this.nodes = nodes;
    }
}
