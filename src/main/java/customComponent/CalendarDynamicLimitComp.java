package customComponent;

import org.primefaces.component.calendar.Calendar;

import javax.faces.component.UINamingContainer;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <p>描述：日期动态限制组件</p>
 *
 *  @MethodAuthor: 龚哲,2021/10/26 19:45,CalendarDynamicLimitComp
 */
public class CalendarDynamicLimitComp extends UINamingContainer implements Serializable {

    private static final long serialVersionUID = -7618305755770492110L;

    /**开始日期*/
    private Calendar beginCalendar;
    /**结束日期*/
    private Calendar endCalendar;

    private Date defaultMaxDate;

    public Calendar getBeginCalendar() {
        return beginCalendar;
    }

    public void setBeginCalendar(Calendar beginCalendar) {
        this.beginCalendar = beginCalendar;
    }

    public Calendar getEndCalendar() {
        return endCalendar;
    }

    public void setEndCalendar(Calendar endCalendar) {
        this.endCalendar = endCalendar;
    }

    public Date getDefaultMaxDate() {
        return new Date();
    }

    public void setDefaultMaxDate(Date defaultMaxDate) {
        this.defaultMaxDate = defaultMaxDate;
    }
}
