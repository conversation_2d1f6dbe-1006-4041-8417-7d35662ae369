package customComponent;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.component.UIInput;
import javax.faces.component.UINamingContainer;

import org.primefaces.component.api.UITree;
import org.primefaces.component.inputtext.InputText;
import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsZone;

/**
 * <AUTHOR>
 */
public class ZoneSingleComp extends UINamingContainer implements Serializable {

    private UITree uiTree;
    private TreeNode selectedNode;
    private InputText nameInput;
    /**地区rid*/
    private UIInput idInput;
    /**地区编码*/
    private UIInput codeInput;
    /**地区级别*/
    private UIInput levelInput;

    /**
     * 初始化地区
     */
    public void initZoneTree() {
        TreeNode rootNode = uiTree.getValue();
        if(null == rootNode || rootNode.getChildCount() <=0) {
            rootNode = new DefaultTreeNode("root", null);

            Map<String, Object> attrs = this.getAttributes();
            List<TsZone> list = (List<TsZone>) attrs.get("zoneList");
            if(null != list && list.size() > 0) {
                Map<String, TreeNode> map = new HashMap<String, TreeNode>();
                TsZone top = list.get(0);

                Short topLvl = top.getZoneType();
                TreeNode temNode = new DefaultTreeNode(top, rootNode);
                map.put(top.getZoneGb(), temNode);

                if(list.size() > 1) {
                    for(int i=1; i<list.size(); i++) {
                        TsZone t = list.get(i);
                        if(topLvl.equals(t.getZoneType())) {
                            TreeNode node = new DefaultTreeNode(t, rootNode);
                            map.put(t.getZoneGb(), node);
                        }else {
                            String parentCode = findParentCode(t.getZoneGb(), t.getZoneType().toString());
                            if(StringUtils.isNotBlank(parentCode)) {
                                TreeNode parentNode = map.get(parentCode);
                                if(null != parentNode) {
                                    TreeNode node = new DefaultTreeNode(t, parentNode);
                                    map.put(t.getZoneGb(), node);
                                }
                            }
                        }
                    }
                }
                map.clear();
            }
            uiTree.setValue(rootNode);
            
            String clientId = this.getClientId();
            RequestContext requestContext = RequestContext.getCurrentInstance();
            requestContext.update(clientId+ ":zoneTree");
        }
    }

    /**
     * 地区选中事件
     * @param event 节点选中事件
     */
    public void onNodeSelect(NodeSelectEvent event) {
        TreeNode node = event.getTreeNode();
        TsZone zone = (TsZone) node.getData();
        nameInput.setValue(zone.getZoneName());
        idInput.setValue(zone.getRid());
        codeInput.setValue(zone.getZoneGb());
        levelInput.setValue(zone.getZoneType());
    }

    /**
     * 根据地区编码和地区级别获取上级地区编码
     * @param zoneCode 地区编码
     * @param zoneType 地区编码对应的地区级别
     * @return 上级地区编码
     */
    private static String findParentCode(String zoneCode, String zoneType) {
        if("2".equals(zoneType)) {
            return "0000000000";
        }else if("3".equals(zoneType)) {
            return zoneCode.substring(0, 2)+"00000000";
        }else if("4".equals(zoneType)) {
            return zoneCode.substring(0, 4)+"000000";
        }else if("5".equals(zoneType)) {
            return zoneCode.substring(0, 6)+"0000";
        }else if("6".equals(zoneType)) {
            return zoneCode.substring(0, 8)+"00";
        }else {
            return "0000000000";
        }
    }

    public UITree getUiTree() {
        return uiTree;
    }

    public void setUiTree(UITree uiTree) {
        this.uiTree = uiTree;
    }

    public TreeNode getSelectedNode() {
        return selectedNode;
    }

    public void setSelectedNode(TreeNode selectedNode) {
        this.selectedNode = selectedNode;
    }

    public InputText getNameInput() {
        return nameInput;
    }

    public void setNameInput(InputText nameInput) {
        this.nameInput = nameInput;
    }

    public UIInput getIdInput() {
        return idInput;
    }

    public void setIdInput(UIInput idInput) {
        this.idInput = idInput;
    }

	public UIInput getCodeInput() {
		return codeInput;
	}

	public void setCodeInput(UIInput codeInput) {
		this.codeInput = codeInput;
	}

	public UIInput getLevelInput() {
		return levelInput;
	}

	public void setLevelInput(UIInput levelInput) {
		this.levelInput = levelInput;
	}


}
