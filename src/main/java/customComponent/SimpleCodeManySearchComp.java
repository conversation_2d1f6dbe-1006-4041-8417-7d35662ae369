package customComponent;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import org.apache.commons.collections.CollectionUtils;
import org.primefaces.component.api.UITree;
import org.primefaces.component.inputtext.InputText;
import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.NodeUnselectEvent;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.TreeNode;

import javax.faces.component.UIInput;
import javax.faces.component.UINamingContainer;
import javax.faces.component.html.HtmlOutputLabel;
import java.util.*;

/**
 * <p>方法描述：码表下拉多选组件-一层时支持搜索</p>
 *
 * @MethodAuthor hsj 2025/7/17 19:39
 */
public class SimpleCodeManySearchComp extends UINamingContainer {

    private InputText nameInput;
    private UIInput idsValue;
    private UITree uiTree;
    private HtmlOutputLabel nameLabel;
    /**
     * 重新加载次数
     **/
    private UIInput resetInput;

    private String searchText;

    /**
     * @Description : 下拉多选树初始化
     * @MethodAuthor: anjing
     * @Date : 2020/1/21 15:46
     **/
    public void init() {
        this.searchText = "";
        TreeNode rootNode = uiTree.getValue();
        Map<String, Object> attrs = this.getAttributes();
        Boolean ifReset = (resetInput == null || resetInput.getValue() == null || "".equals(resetInput.getValue().toString())) ? false : Boolean.valueOf(resetInput.getValue().toString());
        if (null == rootNode || rootNode.getChildCount() <= 0 || ifReset) {
            filterTree();
            String clientId = this.getClientId();
            RequestContext requestContext = RequestContext.getCurrentInstance();
            requestContext.update(clientId + ":dataOverPanel");
            requestContext.update(clientId + ":reset");
        }
    }

    private void addChildNode(String levelNo, Set<String> levelNoSet,
                              Map allMap, TreeNode parentNode) {

        int level = StringUtils.countMatches(levelNo, ".");
        for (String ln : levelNoSet) {
            if (StringUtils.countMatches(ln, ".") == (level + 1)
                    && StringUtils.startsWith(ln, levelNo + ".")) {
                TsSimpleCode t = (TsSimpleCode) allMap.get(ln);
                TreeNode node = new CheckboxTreeNode(t, parentNode);
                this.addChildNode(ln, levelNoSet, allMap, node);
            }
        }
    }

    /**
     * <p>方法描述：</p>
     *
     * @MethodAuthor hsj 2025/7/18 15:20
     */
    public void initData() {
        this.searchText = "";
        filterTree();
        String clientId = this.getClientId();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.update(clientId + ":dataOverPanel");
        requestContext.update(clientId + ":reset");
    }

    /**
     * <p>方法描述：查询条件失焦查询</p>
     *
     * @MethodAuthor hsj 2025/7/18 15:20
     */
    public void filterTree() {
        TreeNode rootNode = uiTree.getValue();
        Map<String, Object> attrs = this.getAttributes();
        String selectedIds = null == this.idsValue.getValue() ? "" : (String) this.idsValue.getValue();
        resetInput.setValue(false);
        rootNode = new CheckboxTreeNode("root", null);
        uiTree.setSelection(null);
        List<TsSimpleCode> list = (List<TsSimpleCode>) attrs.get("simpleCodeList");
        if (CollectionUtils.isEmpty(list)) {
            uiTree.setValue(rootNode);
            return;
        }
        String str = (String) attrs.get("ifTree");
        boolean ifTree = "true".equals(str);
        if (ifTree) {//树型结构
            Set<String> firstLevelNoSet = new LinkedHashSet<>();
            Set<String> levelNoSet = new LinkedHashSet<>();
            Map<String, TsSimpleCode> menuMap = new HashMap<>();
            for (TsSimpleCode t : list) {
                menuMap.put(t.getCodeLevelNo(), t);
            }
            for (TsSimpleCode t : list) {
                if (StringUtils.isNotBlank(t.getCodeLevelNo())) {
                    if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
                        firstLevelNoSet.add(t.getCodeLevelNo());
                    } else {
                        levelNoSet.add(t.getCodeLevelNo());
                    }
                }
            }
            for (String ln : firstLevelNoSet) {
                TreeNode node = new CheckboxTreeNode(menuMap.get(ln), rootNode);
                this.addChildNode(ln, levelNoSet, menuMap, node);
            }
        } else {
            //非树形处理
            if (StringUtils.isNotBlank(selectedIds)) {
                selectedIds = "," + selectedIds + ",";
            }
            for (TsSimpleCode t : list) {
                if (StringUtils.isNotBlank(this.searchText) && !t.getCodeName().contains(this.searchText)) {
                    continue;
                }
                TreeNode node = new CheckboxTreeNode(t, rootNode);
                if (StringUtils.isNotBlank(selectedIds) && selectedIds.contains("," + t.getRid() + ",")) {
                    node.setSelected(true);
                }
            }
        }
        this.uiTree.setValue(rootNode);
    }


    public void onNodeSelect(NodeSelectEvent event) {
        TreeNode selectNode = event.getTreeNode();
        selectNode.setSelected(true);
        // 获得第一级的节点
        if (null != selectNode) {
            String selectedIds = (String) this.idsValue.getValue();
            List<String> list = StringUtils.string2list(selectedIds, ",");
            TsSimpleCode t = (TsSimpleCode) selectNode.getData();

            Map<String, Object> attrs = this.getAttributes();
            String str = (String) attrs.get("ifTree");
            boolean ifTree = "true".equals(str);
            str = (String) attrs.get("ifContantsParent");
            boolean ifContantsParent = "true".equals(str);
            if (ifTree) {//树形
                if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {//父级勾选，全部子级勾选
                    if (ifContantsParent && !list.contains(t.getRid().toString())) {
                        list.add(t.getRid().toString());
                    }
                    List<TsSimpleCode> simpleCodeList = (List<TsSimpleCode>) attrs.get("simpleCodeList");
                    for (TsSimpleCode code : simpleCodeList) {
                        boolean child = code.getCodeLevelNo().startsWith(t.getCodeNo() + ".");
                        if (child && !list.contains(code.getRid().toString())) {
                            list.add(code.getRid().toString());
                        }
                    }
                } else {//子级勾选
                    Integer curRid = t.getRid();
                    list.add(curRid.toString());
                    if (ifContantsParent) {
                        List<TsSimpleCode> simpleCodeList = (List<TsSimpleCode>) attrs.get("simpleCodeList");
                        Map<String, Integer> tmpMap = new HashMap<>();
                        String codeLevelNo = null;
                        for (TsSimpleCode code : simpleCodeList) {
                            tmpMap.put(code.getCodeLevelNo(), code.getRid());
                            if (curRid.compareTo(code.getRid()) == 0) {
                                codeLevelNo = code.getCodeLevelNo();
                            }
                        }
                        if (StringUtils.isNotBlank(codeLevelNo) && codeLevelNo.indexOf(".") != -1) {
                            codeLevelNo = codeLevelNo.split("\\.")[0];
                            Set<String> keySet = tmpMap.keySet();
                            boolean flag = true;
                            for (String key : keySet) {
                                if (key.indexOf(".") != -1 && key.startsWith(codeLevelNo + ".") && !list.contains(tmpMap.get(key).toString())) {
                                    flag = false;
                                }
                            }
                            Integer rid = tmpMap.get(codeLevelNo);
                            if (null != rid && flag && !list.contains(rid)) {
                                list.add(rid.toString());
                            }
                        }
                    }
                }
            } else {
                list.add(t.getRid().toString());
            }
            String val = StringUtils.list2string(list, ",");
            this.idsValue.setValue(val);
            String clientId = this.getClientId();
            RequestContext requestContext = RequestContext.getCurrentInstance();
            requestContext.update(clientId + ":selectedIds");
        }
    }

    public void onNodeNoSelect(NodeUnselectEvent event) {
        TreeNode selectNode = event.getTreeNode();
        selectNode.setSelected(false);
        // 获得第一级的节点
        String selectedIds = (String) this.idsValue.getValue();
        List<String> list = StringUtils.string2list(selectedIds, ",");
        TsSimpleCode t = (TsSimpleCode) selectNode.getData();
        Map<String, Object> attrs = this.getAttributes();
        String str = (String) attrs.get("ifTree");
        boolean ifTree = "true".equals(str);
        str = (String) attrs.get("ifContantsParent");
        boolean ifContantsParent = "true".equals(str);
        //树形
        if (ifTree) {
            //父级去除勾选，子级全部不勾选
            if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
                if (ifContantsParent && list.contains(t.getRid().toString())) {
                    list.remove(t.getRid().toString());
                }
                List<TsSimpleCode> simpleCodeList = (List<TsSimpleCode>) attrs.get("simpleCodeList");
                for (TsSimpleCode code : simpleCodeList) {
                    boolean child = code.getCodeLevelNo().startsWith(t.getCodeNo() + ".");
                    if (child && list.contains(code.getRid().toString())) {
                        list.remove(code.getRid().toString());
                    }
                }
            } else {//子级不勾选
                Integer curRid = t.getRid();
                list.remove(curRid.toString());
                if (ifContantsParent && null != curRid) {
                    // 去除父级勾选
                    List<TsSimpleCode> simpleCodeList = (List<TsSimpleCode>) attrs.get("simpleCodeList");
                    Map<String, Integer> tmpMap = new HashMap<>();
                    String codeLevelNo = null;
                    for (TsSimpleCode code : simpleCodeList) {
                        tmpMap.put(code.getCodeNo(), code.getRid());
                        if (curRid.compareTo(code.getRid()) == 0) {
                            codeLevelNo = code.getCodeLevelNo();
                        }
                    }
                    if (StringUtils.isNotBlank(codeLevelNo) && codeLevelNo.indexOf(".") != -1) {
                        Integer rid = tmpMap.get(codeLevelNo.split("\\.")[0]);
                        if (null != rid) {
                            list.remove(rid.toString());
                        }
                    }
                }
            }
        } else {
            list.remove(t.getRid().toString());
        }
        String val = StringUtils.list2string(list, ",");
        this.idsValue.setValue(val);
        String clientId = this.getClientId();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.update(clientId + ":selectedIds");
    }

    public void hideAction() {
        this.nameInput.setValue("");
        this.nameLabel.setValue("");
        String selectedIds = (String) this.idsValue.getValue();
        List<String> list = StringUtils.string2list(selectedIds, ",");
        if (!CollectionUtils.isEmpty(list)) {
            Map<String, Object> attrs = this.getAttributes();
            String str = (String) attrs.get("ifContantsParent");
            boolean ifContantsParent = "true".equals(str);
            str = (String) attrs.get("ifTree");
            boolean ifTree = "true".equals(str);
            List<TsSimpleCode> simpleCodeList = (List<TsSimpleCode>) attrs.get("simpleCodeList");
            Map<String, TsSimpleCode> map = new HashMap<>();
            Map<String, String> codeMap = new HashMap<>();
            for (TsSimpleCode t : simpleCodeList) {
                map.put(t.getRid().toString(), t);
                String codeNo = t.getCodeNo();
                String codeLevelNo = t.getCodeLevelNo();
                if (StringUtils.isNotBlank(codeLevelNo) && codeNo.equals(codeLevelNo)) {
                    codeMap.put(codeNo, t.getRid().toString());
                }
            }
            // 分类名称
            StringBuilder nameSb = new StringBuilder();
            if (ifTree && ifContantsParent) {
                Set<String> keySet = null == codeMap ? new HashSet<String>() : codeMap.keySet();
                List<String> fatherCodeList = new ArrayList<>();
                for (String rid : list) {
                    TsSimpleCode t = map.get(rid);
                    if (null == t) {
                        continue;
                    }
                    String codeNo = t.getCodeNo();
                    String codeLevelNo = t.getCodeLevelNo();
                    if (StringUtils.isNotBlank(codeNo) && StringUtils.isNotBlank(codeLevelNo) && codeNo.equals(codeLevelNo)) {
                        fatherCodeList.add(codeNo);
                    }
                }
                // 如果选了大类，就不拼接小类名称了
                for (String rid : list) {
                    TsSimpleCode t = map.get(rid);
                    if (null == t) {
                        continue;
                    }
                    if (keySet.contains(t.getCodeNo())) {
                        nameSb.append("，").append(t.getCodeName());
                    }
                    String codeLevelNo = t.getCodeLevelNo();
                    if (StringUtils.isNotBlank(codeLevelNo) && codeLevelNo.indexOf(".") != -1) {
                        String code = codeLevelNo.split("\\.")[0];
                        if (!fatherCodeList.contains(code)) {
                            nameSb.append("，").append(t.getCodeName());
                        }
                    }
                }
            } else {
                for (String rid : list) {
                    TsSimpleCode t = map.get(rid);
                    if (null == t) {
                        continue;
                    }
                    nameSb.append("，").append(t.getCodeName());
                }
            }
            String nameVal = nameSb.length() > 0 ? nameSb.substring(1) : "";
            this.nameInput.setValue(nameVal);
            this.nameLabel.setValue(nameVal);
        }
        String clientId = this.getClientId();
        if (nameInput != null && nameInput.getValue() != null) {
            RequestContext requestContext = RequestContext.getCurrentInstance();
            requestContext.update(clientId + ":codeName");
        }
        if (nameLabel != null && nameLabel.getValue() != null) {
            RequestContext requestContext = RequestContext.getCurrentInstance();
            requestContext.update(clientId + ":codeNameLabel");
        }

    }

    /**
     * @Description : 清除
     * @MethodAuthor: anjing
     * @Date : 2020/1/21 15:47
     **/
    public void clearCodeName() {
        if (nameInput != null) {
            this.nameInput.setValue("");
        }
        if (nameLabel != null) {
            this.nameLabel.setValue("");
        }
        this.idsValue.setValue("");
        TreeNode rootNode = uiTree.getValue();
        if (null != rootNode) {
            List<TreeNode> children = rootNode.getChildren();
            if (null != children && children.size() > 0) {
                for (TreeNode node : children) {
                    node.setSelected(false);
                    List<TreeNode> children2 = node.getChildren();
                    if (null != children2 && children2.size() > 0) {
                        for (TreeNode node2 : children2) {
                            node2.setSelected(false);
                        }
                    }
                }
            }
        }
    }

    public InputText getNameInput() {
        return nameInput;
    }

    public void setNameInput(InputText nameInput) {
        this.nameInput = nameInput;
    }

    public UIInput getIdsValue() {
        return idsValue;
    }

    public void setIdsValue(UIInput idsValue) {
        this.idsValue = idsValue;
    }

    public UITree getUiTree() {
        return uiTree;
    }

    public void setUiTree(UITree uiTree) {
        this.uiTree = uiTree;
    }

    public HtmlOutputLabel getNameLabel() {
        return nameLabel;
    }

    public void setNameLabel(HtmlOutputLabel nameLabel) {
        this.nameLabel = nameLabel;
    }

    public UIInput getResetInput() {
        return resetInput;
    }

    public void setResetInput(UIInput resetInput) {
        this.resetInput = resetInput;
    }

    // 新增：搜索文本的Getter和Setter
    public String getSearchText() {
        return searchText;
    }

    public void setSearchText(String searchText) {
        this.searchText = searchText;
    }

}