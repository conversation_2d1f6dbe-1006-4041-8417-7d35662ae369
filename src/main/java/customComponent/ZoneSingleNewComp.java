package customComponent;

import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsZone;
import org.primefaces.component.api.UITree;
import org.primefaces.component.inputtext.InputText;
import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import javax.faces.component.UIInput;
import javax.faces.component.UINamingContainer;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 新版地区单选组件
 * <AUTHOR>
 * 
 * <p>修订内容：</p>
 *新增参数用于重置list
 *resetList 是否重置list 
 *resetTimes 重置次数
 *setedTimes 当前已重置次数
 * 当 ifReset && resetTimes>setedTimes 时进行重置
 * @ClassReviser xq,2018年1月31日,ZoneSingleNewComp
 */
public class ZoneSingleNewComp extends UINamingContainer implements Serializable {

	private static final long serialVersionUID = -7618305755770492110L;
	private UITree uiTree;
    private TreeNode selectedNode;
    private InputText nameInput;
    /**地区rid*/
    private UIInput idInput;
    /**地区编码*/
    private UIInput codeInput;
    /**新版地区编码12位*/
    private UIInput newCodeInput;
    /**地区级别*/
    private UIInput levelInput;
    /**真实地区级别*/
    private UIInput realLevelInput;
    /**重新加载次数**/
    private UIInput setedTimesInput;
    /**
     * 初始化地区
     */
    public void initZoneTree() {
        TreeNode rootNode = uiTree.getValue();
        Map<String, Object> attrs = this.getAttributes();
//        boolean ifReset= (boolean) attrs.get("resetList");
        String reset= (String) attrs.get("resetList");
        boolean ifReset="true".equals(reset);
        Integer resetTimes=  attrs.get("resetTimes")==null?0:Integer.valueOf(attrs.get("resetTimes").toString());
        Integer setedTimes =(setedTimesInput==null  ||setedTimesInput.getValue()==null || "".equals(setedTimesInput.getValue()))?0:Integer.valueOf(setedTimesInput.getValue().toString());

        Integer idInputVal = (null == idInput || null == idInput.getValue()||StringUtils.isBlank(idInput.getValue().toString())) ? null : Integer.valueOf(idInput.getValue().toString());
        if(null == rootNode || rootNode.getChildCount() <=0 || (ifReset && resetTimes>setedTimes)) {
        	setedTimesInput.setValue(setedTimes+1);
            rootNode = new DefaultTreeNode("root", null);
//            Map<String, Object> attrs = this.getAttributes();
            List<TsZone> list = (List<TsZone>) attrs.get("zoneList");
            if(null != list && list.size() > 0) {
                Map<String, TreeNode> map = new HashMap<String, TreeNode>();
                TsZone top = list.get(0);

                Short topLvl = top.getZoneType();
                TreeNode temNode = new DefaultTreeNode(top, rootNode);
                if(null != idInputVal && idInputVal.intValue() == top.getRid()){
                    this.selectedNode = temNode;
                }
                map.put(top.getZoneCode(), temNode);

                if(list.size() > 1) {
                    for(int i=1; i<list.size(); i++) {
                        TsZone t = list.get(i);
                        if(topLvl.equals(t.getZoneType())) {
                            TreeNode node = new DefaultTreeNode(t, rootNode);
                            if(null != idInputVal && idInputVal.intValue() == t.getRid()){
                                this.selectedNode = node;
                            }
                            map.put(t.getZoneCode(), node);
                        }else {
                            String parentCode = ZoneUtil.getParentCode(t.getZoneCode());
                            if(StringUtils.isNotBlank(parentCode)) {
                                TreeNode parentNode = map.get(parentCode);
                                if(null != parentNode) {
                                    TreeNode node = new DefaultTreeNode(t, parentNode);
                                    if(null != idInputVal && idInputVal.intValue() == t.getRid()){
                                        this.selectedNode = node;
                                    }
                                    map.put(t.getZoneCode(), node);
                                }
                            }
                        }
                    }
                }
                map.clear();
            }
            uiTree.setValue(rootNode);

            if(null != this.selectedNode){
                this.selectedNode.setSelected(true);
            }
            String clientId = this.getClientId();
            RequestContext requestContext = RequestContext.getCurrentInstance();
            requestContext.update(clientId+ ":zoneTree");
        } else {
            rootNode.setSelected(false);
            clearNodeSelected(rootNode, idInputVal);
            String clientId = this.getClientId();
            RequestContext requestContext = RequestContext.getCurrentInstance();
            requestContext.update(clientId+ ":zoneTree");
        }
    }

    /**
     * 地区选中事件
     * @param event 节点选中事件
     */
    public void onNodeSelect(NodeSelectEvent event) {
        TreeNode node = event.getTreeNode();
        TsZone zone = (TsZone) node.getData();

        try {
            int realZoneType = Integer.parseInt(zone.getRealZoneType().toString());
            String errorStr = getErrorStr(realZoneType);
            if (ObjectUtil.isNotEmpty(errorStr)) {
                JsfUtil.addErrorMessage(errorStr);
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        nameInput.setValue(zone.getZoneName());
        idInput.setValue(zone.getRid());
        codeInput.setValue(zone.getZoneGb());
        newCodeInput.setValue(zone.getZoneCode());
        levelInput.setValue(zone.getZoneType());
        realLevelInput.setValue(zone.getRealZoneType());
        TreeNode rootNode = uiTree.getValue();
        rootNode.setSelected(false);
        clearNodeSelected(rootNode, zone.getRid());

        String clientId = this.getClientId();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.execute("PF('"+clientId+":ZonePanel').hide()");
        String onchangeCon = (String)this.getAttributes().get("onchange");
        if(StringUtils.isNotBlank(onchangeCon)){
            requestContext.execute(onchangeCon);
        }
    }

    private String getErrorStr(int realZoneType) {
        String[] zoneTypeStr = {"", "国", "省", "地市", "县区", "街道"};
        StringBuilder sb = new StringBuilder();
        //最高可选择的地区级别
        String choseZoneTypeMin = StringUtils.objectToString(this.getAttributes().get("choseZoneTypeMin"));
        if (ObjectUtil.isNotEmpty(choseZoneTypeMin) && Integer.parseInt(choseZoneTypeMin) < 2) {
            choseZoneTypeMin = "";
        }
        //最低可选择的地区级别
        String choseZoneTypeMax = StringUtils.objectToString(this.getAttributes().get("choseZoneTypeMax"));
        if (ObjectUtil.isNotEmpty(choseZoneTypeMax) && Integer.parseInt(choseZoneTypeMax) > 5) {
            choseZoneTypeMax = "";
        }
        int choseZoneTypeMinInt = ObjectUtil.isEmpty(choseZoneTypeMin) ? 0 : Integer.parseInt(choseZoneTypeMin);
        int choseZoneTypeMaxInt = ObjectUtil.isEmpty(choseZoneTypeMax) ? 0 : Integer.parseInt(choseZoneTypeMax);
        if (ObjectUtil.isNotEmpty(choseZoneTypeMin)) {
            if (ObjectUtil.isNotEmpty(choseZoneTypeMax)) {
                if (realZoneType < choseZoneTypeMinInt && choseZoneTypeMinInt == choseZoneTypeMaxInt) {
                    //最高可选择的地区级别 == 最低可选择的地区级别 提示: 必须选择至xx！
                    return "必须选择至" + zoneTypeStr[choseZoneTypeMinInt] + "！";
                }
                if (realZoneType < choseZoneTypeMinInt || realZoneType > choseZoneTypeMaxInt) {
                    //最高可选择的地区级别 != 最低可选择的地区级别 提示: 必须选择至xx或xx！
                    for (int i = choseZoneTypeMinInt; i <= choseZoneTypeMaxInt; i++) {
                        sb.append("或").append(zoneTypeStr[i]);
                    }
                    return "必须选择至" + sb.substring(1) + "！";
                }
            } else {
                if (realZoneType < choseZoneTypeMinInt) {
                    //无最低可选择的地区级别 提示: 必须选择至xx及以下地区！
                    return "必须选择至" + zoneTypeStr[choseZoneTypeMinInt] + "及以下地区！";
                }
            }
        } else {
            if (ObjectUtil.isNotEmpty(choseZoneTypeMax)) {
                if (realZoneType > choseZoneTypeMaxInt) {
                    //无最高可选择的地区级别 提示: 必须选择至xx及以上地区！
                    return "必须选择至" + zoneTypeStr[choseZoneTypeMaxInt] + "及以上地区！";
                }
            }
        }
        return "";
    }

    /**
     * 递归清除上次选择节点Selected标记
     * @param rootNode 当前节点
     * @param zoneRid 当此选择节点地区RID
     */
    private void clearNodeSelected(TreeNode rootNode, Integer zoneRid) {
        if (null != rootNode) {
            List<TreeNode> children = rootNode.getChildren();
            if (null != children && children.size() > 0) {
                for (TreeNode node1 : children) {
                    TsZone zone1 = (TsZone) node1.getData();
                    if (zoneRid == null || zone1.getRid() == null || (!zoneRid.equals(zone1.getRid()))) {
                        node1.setSelected(false);
                    } else if (zone1.getRid() != null && zoneRid.equals(zone1.getRid())){
                        node1.setSelected(true);
                    }
                    clearNodeSelected(node1, zoneRid);
                }
            }
        }
    }

    /**
     * @Description : 清除
     * @MethodAuthor: anjing
     * @Date : 2020/1/21 15:47
     **/
    public void clearCodeName() {
        this.nameInput.setValue("");
        this.idInput.setValue("");
        this.codeInput.setValue("");
        this.newCodeInput.setValue("");
        this.levelInput.setValue(null);
        this.realLevelInput.setValue(null);
        TreeNode rootNode = uiTree.getValue();
        if(rootNode!=null){
            rootNode.setSelected(false);
            clearNodeSelected(rootNode, null);
        }
    }

    public UITree getUiTree() {
        return uiTree;
    }

    public void setUiTree(UITree uiTree) {
        this.uiTree = uiTree;
    }

    public TreeNode getSelectedNode() {
        return selectedNode;
    }

    public void setSelectedNode(TreeNode selectedNode) {
        this.selectedNode = selectedNode;
    }

    public InputText getNameInput() {
        return nameInput;
    }

    public void setNameInput(InputText nameInput) {
        this.nameInput = nameInput;
    }

    public UIInput getIdInput() {
        return idInput;
    }

    public void setIdInput(UIInput idInput) {
        this.idInput = idInput;
    }

	public UIInput getCodeInput() {
		return codeInput;
	}

	public void setCodeInput(UIInput codeInput) {
		this.codeInput = codeInput;
	}

	public UIInput getLevelInput() {
		return levelInput;
	}

	public void setLevelInput(UIInput levelInput) {
		this.levelInput = levelInput;
	}

	public UIInput getNewCodeInput() {
		return newCodeInput;
	}

	public void setNewCodeInput(UIInput newCodeInput) {
		this.newCodeInput = newCodeInput;
	}

	public UIInput getSetedTimesInput() {
		return setedTimesInput;
	}

	public void setSetedTimesInput(UIInput setedTimesInput) {
		this.setedTimesInput = setedTimesInput;
	}

    public UIInput getRealLevelInput() {
        return realLevelInput;
    }

    public void setRealLevelInput(UIInput realLevelInput) {
        this.realLevelInput = realLevelInput;
    }

}
