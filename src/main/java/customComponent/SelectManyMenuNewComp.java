package customComponent;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import org.primefaces.component.api.UITree;
import org.primefaces.component.inputtext.InputText;
import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.NodeUnselectEvent;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.TreeNode;
import org.springframework.util.CollectionUtils;

import javax.faces.component.UIInput;
import javax.faces.component.UINamingContainer;
import java.util.*;

/**
 * @Description: 下拉多选组件
 * Map<String,String>
 *     参考SimpleCodeManyComp
 * @ClassAuthor pw,2021年12月2日,SelectManyMenuNewComp
 */
public class SelectManyMenuNewComp extends UINamingContainer {

    private InputText nameInput;
    private UIInput idsValue;
    private UITree uiTree;

    /**
     * @Description: 下拉多选树初始化
     *
     * @MethodAuthor pw,2021年12月2日
     */
    public void init() {
        TreeNode rootNode = uiTree.getValue();

        String selectedIds = (String) this.idsValue.getValue();
        List<String> ridList = StringUtils.isBlank(selectedIds) ? Collections.EMPTY_LIST :
                Arrays.asList(selectedIds.split(","));
        if(null == rootNode || rootNode.getChildCount() <=0) {
            rootNode = new CheckboxTreeNode("root", null);
            Map<String, Object> attrs = this.getAttributes();
            Map<String,String> map = (Map<String, String>) attrs.get("dataMap");

            if(!CollectionUtils.isEmpty(map)){
                for(Map.Entry<String,String> mapEntity : map.entrySet()){
                    CheckboxTreeNode treeNode = new CheckboxTreeNode(mapEntity, rootNode);
                    if(ridList.contains(mapEntity.getValue())){
                        treeNode.setSelected(true);
                    }
                }
            }
            uiTree.setValue(rootNode);
        }else{
            List<TreeNode> childNodes = rootNode.getChildren();
            for(TreeNode treeNode : childNodes){
                CheckboxTreeNode checkboxTreeNode = (CheckboxTreeNode)treeNode;
                Map.Entry<String,String> t = (Map.Entry<String,String>)checkboxTreeNode.getData();
                if(ridList.contains(t.getValue())){
                    treeNode.setSelected(true);
                }else{
                    treeNode.setSelected(false);
                }
            }
        }
        String clientId = this.getClientId();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.update(clientId+ ":dataOverPanel");
    }
    
    public void onNodeSelect(NodeSelectEvent event){
        TreeNode selectNode = event.getTreeNode();
        selectNode.setSelected(true);
        // 获得节点
        if (null != selectNode) {
        	String selectedIds = (String) this.idsValue.getValue();
        	List<String> list = StringUtils.string2list(selectedIds, ",");
            Map.Entry<String,String> t = (Map.Entry<String,String>) selectNode.getData();
        	
        	Map<String, Object> attrs = this.getAttributes();
            list.add(t.getValue());
        	String val = StringUtils.list2string(list, ",");
        	this.idsValue.setValue(val);
            String clientId = this.getClientId();
            RequestContext requestContext = RequestContext.getCurrentInstance();
            requestContext.update(clientId + ":selectedIds");
        }
    }

    public void onNodeNoSelect(NodeUnselectEvent event) {
    	TreeNode selectNode = event.getTreeNode();
    	selectNode.setSelected(false);
        // 获得节点
        if (null != selectNode) {
        	String selectedIds = (String) this.idsValue.getValue();
        	List<String> list = StringUtils.string2list(selectedIds, ",");
            Map.Entry<String,String> t = (Map.Entry<String,String>) selectNode.getData();
        	Map<String, Object> attrs = this.getAttributes();
            list.remove(t.getValue());
        	String val = StringUtils.list2string(list, ",");
        	this.idsValue.setValue(val);
        	String clientId = this.getClientId();
            RequestContext requestContext = RequestContext.getCurrentInstance();
            requestContext.update(clientId + ":selectedIds");
        }
    }
    
    public void hideAction() {
    	this.nameInput.setValue("");
    	String selectedIds = (String) this.idsValue.getValue();
    	List<String> list = StringUtils.string2list(selectedIds, ",");
		if (list != null && list.size() > 0) {
			Map<String, Object> attrs = this.getAttributes();
            Map<String,String> rootMap = (Map<String, String>) attrs.get("dataMap");
	        Map<String, Map.Entry<String,String>> map = new HashMap<>();
	        if(!CollectionUtils.isEmpty(rootMap)){
	            for(Map.Entry<String,String> mapEntity : rootMap.entrySet()){
                    map.put(mapEntity.getValue(), mapEntity);
                }
            }
			StringBuilder nameSb = new StringBuilder();
			for (String rid : list) {
                Map.Entry<String,String> mapEntity = map.get(rid);
				nameSb.append("，").append(mapEntity.getKey());
			}
			this.nameInput.setValue(nameSb.substring(1));
		}
		String clientId = this.getClientId();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.update(clientId + ":codeName");
	}

    /**
     * @Description: 清除
     *
     * @MethodAuthor pw,2021年12月2日
     */
    public void clearCodeName() {
        this.nameInput.setValue("");
        this.idsValue.setValue("");
        TreeNode rootNode = uiTree.getValue();
        if(null != rootNode) {
            List<TreeNode> children = rootNode.getChildren();
            if (null != children && children.size() > 0) {
                for (TreeNode node : children) {
                    node.setSelected(false);
                    List<TreeNode> children2 = node.getChildren();
                    if (null != children2 && children2.size() > 0) {
                        for (TreeNode node2 : children2) {
                            node2.setSelected(false);
                        }
                    }
                }
            }
        }
    }

    public InputText getNameInput() {
        return nameInput;
    }

    public void setNameInput(InputText nameInput) {
        this.nameInput = nameInput;
    }

    public UIInput getIdsValue() {
        return idsValue;
    }

    public void setIdsValue(UIInput idsValue) {
        this.idsValue = idsValue;
    }

    public UITree getUiTree() {
        return uiTree;
    }

    public void setUiTree(UITree uiTree) {
        this.uiTree = uiTree;
    }
}
