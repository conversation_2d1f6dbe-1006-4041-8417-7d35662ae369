package com.wondersgroup.sso.webservice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;

/**
 * This class was generated by the JAX-WS RI. JAX-WS RI 2.1.3-hudson-390-
 * Generated source version: 2.0
 * 
 */
@WebService(name = "SsoservicePortType", targetNamespace = "http://webservice.sso.wondersgroup.com/")
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface SsoservicePortType {

	/**
	 * 
	 * @param parameters
	 * @return returns com.wondersgroup.sso.webservice.SetLoginUserRsp
	 */
	@WebMethod(action = "urn:setLoginUser")
	@WebResult(name = "SetLoginUserRsp", targetNamespace = "http://webservice.sso.wondersgroup.com/", partName = "parameters")
	public SetLoginUserRsp setLoginUser(
			@WebParam(name = "SetLoginUserReq", targetNamespace = "http://webservice.sso.wondersgroup.com/", partName = "parameters") SetLoginUserReq parameters);

	/**
	 * 
	 * @param parameters
	 * @return returns com.wondersgroup.sso.webservice.GetLoginNameByTicketRsp
	 */
	@WebMethod(action = "urn:getLoginNameByTicket")
	@WebResult(name = "GetLoginNameByTicketRsp", targetNamespace = "http://webservice.sso.wondersgroup.com/", partName = "parameters")
	public GetLoginNameByTicketRsp getLoginNameByTicket(
			@WebParam(name = "GetLoginNameByTicketReq", targetNamespace = "http://webservice.sso.wondersgroup.com/", partName = "parameters") GetLoginNameByTicketReq parameters);

}
