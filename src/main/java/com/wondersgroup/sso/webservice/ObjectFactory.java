package com.wondersgroup.sso.webservice;

import javax.xml.bind.annotation.XmlRegistry;

/**
 * This object contains factory methods for each Java content interface and Java
 * element interface generated in the com.wondersgroup.sso.webservice package.
 * <p>
 * An ObjectFactory allows you to programatically construct new instances of the
 * Java representation for XML content. The Java representation of XML content
 * can consist of schema derived interfaces and classes representing the binding
 * of schema type definitions, element declarations and model groups. Factory
 * methods for each of these are provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

	/**
	 * Create a new ObjectFactory that can be used to create new instances of
	 * schema derived classes for package: com.wondersgroup.sso.webservice
	 * 
	 */
	public ObjectFactory() {
	}

	/**
	 * Create an instance of {@link SetLoginUserReq }
	 * 
	 */
	public SetLoginUserReq createSetLoginUserReq() {
		return new SetLoginUserReq();
	}

	/**
	 * Create an instance of {@link GetLoginNameByTicketRsp }
	 * 
	 */
	public GetLoginNameByTicketRsp createGetLoginNameByTicketRsp() {
		return new GetLoginNameByTicketRsp();
	}

	/**
	 * Create an instance of {@link GetLoginNameByTicketReq }
	 * 
	 */
	public GetLoginNameByTicketReq createGetLoginNameByTicketReq() {
		return new GetLoginNameByTicketReq();
	}

	/**
	 * Create an instance of {@link SetLoginUserRsp }
	 * 
	 */
	public SetLoginUserRsp createSetLoginUserRsp() {
		return new SetLoginUserRsp();
	}

}
