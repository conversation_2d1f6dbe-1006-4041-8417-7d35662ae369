package com.wondersgroup.sso.webservice;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="msgid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="method" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="service" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="username" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="identity" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="newpassword" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="oldpassword" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "msgid", "method", "service", "username",
		"identity", "newpassword", "oldpassword" })
@XmlRootElement(name = "SetLoginUserReq")
public class SetLoginUserReq {

	@XmlElement(required = true)
	protected String msgid;
	protected int method;
	@XmlElement(required = true)
	protected String service;
	@XmlElement(required = true)
	protected String username;
	protected String identity;
	protected String newpassword;
	protected String oldpassword;

	/**
	 * Gets the value of the msgid property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getMsgid() {
		return msgid;
	}

	/**
	 * Sets the value of the msgid property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setMsgid(String value) {
		this.msgid = value;
	}

	/**
	 * Gets the value of the method property.
	 * 
	 */
	public int getMethod() {
		return method;
	}

	/**
	 * Sets the value of the method property.
	 * 
	 */
	public void setMethod(int value) {
		this.method = value;
	}

	/**
	 * Gets the value of the service property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getService() {
		return service;
	}

	/**
	 * Sets the value of the service property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setService(String value) {
		this.service = value;
	}

	/**
	 * Gets the value of the username property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUsername() {
		return username;
	}

	/**
	 * Sets the value of the username property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUsername(String value) {
		this.username = value;
	}

	/**
	 * Gets the value of the identity property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getIdentity() {
		return identity;
	}

	/**
	 * Sets the value of the identity property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setIdentity(String value) {
		this.identity = value;
	}

	/**
	 * Gets the value of the newpassword property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getNewpassword() {
		return newpassword;
	}

	/**
	 * Sets the value of the newpassword property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setNewpassword(String value) {
		this.newpassword = value;
	}

	/**
	 * Gets the value of the oldpassword property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getOldpassword() {
		return oldpassword;
	}

	/**
	 * Sets the value of the oldpassword property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setOldpassword(String value) {
		this.oldpassword = value;
	}

}
