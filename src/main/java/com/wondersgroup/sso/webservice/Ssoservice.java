package com.wondersgroup.sso.webservice;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.logging.Logger;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;

/**
 * This class was generated by the JAX-WS RI. JAX-WS RI 2.1.3-hudson-390-
 * Generated source version: 2.0
 * <p>
 * An example of how this class may be used:
 * 
 * <pre>
 * Ssoservice service = new Ssoservice();
 * SsoservicePortType portType = service.getSsoserviceHttpSoap12Endpoint();
 * portType.setLoginUser(...);
 * </pre>
 * 
 * </p>
 * 
 */
@WebServiceClient(name = "Ssoservice", targetNamespace = "http://webservice.sso.wondersgroup.com/", wsdlLocation = "file:/D:/MyEclipse%20Professional%202014/web-system1.10/src/main/java/com/chis/modules/system/wsdl/ssoservice.wsdl")
public class Ssoservice extends Service {

	private final static URL SSOSERVICE_WSDL_LOCATION;
	private final static Logger logger = Logger
			.getLogger(com.wondersgroup.sso.webservice.Ssoservice.class
					.getName());

	static {
		URL url = null;
		try {
			URL baseUrl;
			baseUrl = com.wondersgroup.sso.webservice.Ssoservice.class
					.getResource(".");
			url = new URL(
					baseUrl,
					"file:/D:/MyEclipse%20Professional%202014/web-system1.10/src/main/java/com/chis/modules/system/wsdl/ssoservice.wsdl");
		} catch (MalformedURLException e) {
			logger.warning("Failed to create URL for the wsdl Location: 'file:/D:/MyEclipse%20Professional%202014/web-system1.10/src/main/java/com/chis/modules/system/wsdl/ssoservice.wsdl', retrying as a local file");
			logger.warning(e.getMessage());
		}
		SSOSERVICE_WSDL_LOCATION = url;
	}

	public Ssoservice(URL wsdlLocation, QName serviceName) {
		super(wsdlLocation, serviceName);
	}

	public Ssoservice() {
		super(SSOSERVICE_WSDL_LOCATION, new QName(
				"http://webservice.sso.wondersgroup.com/", "Ssoservice"));
	}

	/**
	 * 
	 * @return returns SsoservicePortType
	 */
	@WebEndpoint(name = "SsoserviceHttpSoap12Endpoint")
	public SsoservicePortType getSsoserviceHttpSoap12Endpoint() {
		return super.getPort(new QName(
				"http://webservice.sso.wondersgroup.com/",
				"SsoserviceHttpSoap12Endpoint"), SsoservicePortType.class);
	}

}
