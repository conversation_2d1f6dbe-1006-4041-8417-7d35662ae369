package com.wondersgroup.sso.webservice;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="msgid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ticket" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="service" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="keyword" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "msgid", "ticket", "service", "keyword" })
@XmlRootElement(name = "GetLoginNameByTicketReq")
public class GetLoginNameByTicketReq {

	@XmlElement(required = true)
	protected String msgid;
	@XmlElement(required = true)
	protected String ticket;
	@XmlElement(required = true)
	protected String service;
	@XmlElement(required = true)
	protected String keyword;

	/**
	 * Gets the value of the msgid property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getMsgid() {
		return msgid;
	}

	/**
	 * Sets the value of the msgid property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setMsgid(String value) {
		this.msgid = value;
	}

	/**
	 * Gets the value of the ticket property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getTicket() {
		return ticket;
	}

	/**
	 * Sets the value of the ticket property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setTicket(String value) {
		this.ticket = value;
	}

	/**
	 * Gets the value of the service property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getService() {
		return service;
	}

	/**
	 * Sets the value of the service property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setService(String value) {
		this.service = value;
	}

	/**
	 * Gets the value of the keyword property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getKeyword() {
		return keyword;
	}

	/**
	 * Sets the value of the keyword property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setKeyword(String value) {
		this.keyword = value;
	}

}
