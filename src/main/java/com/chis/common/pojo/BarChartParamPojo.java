package com.chis.common.pojo;

import java.util.List;

import com.github.abel533.echarts.code.X;
import com.github.abel533.echarts.series.Bar;

/**
 * <p>类描述：柱状图图表参数</p>
 * @ClassAuthor qrr,2020年6月19日,BarChartParamPojo
 * */
public class BarChartParamPojo{
	/**标题*/
	private String title;
	/**x轴名称*/
	private String xAxisTitle;
	/**y轴名称*/
	private String yAxisTitle;
	/**图例*/
	private Object[] legend;
	/**图例位置，默认居左*/
	private X xlegend;
	/**x轴数据*/
	private Object[] xdate;
	/**柱状图数据*/
	private List<Bar> bars;
	/**x轴是否滑动*/
	private boolean ifside = false;
	/**x轴是否滑动默认刻度个数占比*/
	private Integer sideNum = 10;
	/**x轴名称是否旋转*/
	private boolean ifrotate = false;
	/**坐标轴最小间隔大小*/
	private Integer minInterval = 1;

	/**颜色*/
	private List<Object> color;

	/**是否显示Y轴线*/
	private boolean ifShowYLine;

	public String getTitle() {
		return title;
	}
	public String getxAxisTitle() {
		return xAxisTitle;
	}
	public String getyAxisTitle() {
		return yAxisTitle;
	}
	public Object[] getLegend() {
		return legend;
	}
	public X getXlegend() {
		return xlegend;
	}
	public Object[] getXdate() {
		return xdate;
	}
	public List<Bar> getBars() {
		return bars;
	}
	public boolean isIfside() {
		return ifside;
	}
	public boolean isIfrotate() {
		return ifrotate;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public void setxAxisTitle(String xAxisTitle) {
		this.xAxisTitle = xAxisTitle;
	}
	public void setyAxisTitle(String yAxisTitle) {
		this.yAxisTitle = yAxisTitle;
	}
	public void setLegend(Object[] legend) {
		this.legend = legend;
	}
	public void setXlegend(X xlegend) {
		this.xlegend = xlegend;
	}
	public void setXdate(Object[] xdate) {
		this.xdate = xdate;
	}
	public void setBars(List<Bar> bars) {
		this.bars = bars;
	}
	public void setIfside(boolean ifside) {
		this.ifside = ifside;
	}
	public void setIfrotate(boolean ifrotate) {
		this.ifrotate = ifrotate;
	}
	public Integer getSideNum() {
		return sideNum;
	}
	public void setSideNum(Integer sideNum) {
		this.sideNum = sideNum;
	}

	public Integer getMinInterval() {
		return minInterval;
	}

	public void setMinInterval(Integer minInterval) {
		this.minInterval = minInterval;
	}

	public List<Object> getColor() {
		return color;
	}
	public void setColor(List<Object> color) {
		this.color = color;
	}

	public boolean getIfShowYLine() {
		return ifShowYLine;
	}
	public void setIfShowYLine(boolean ifShowYLine) {
		this.ifShowYLine = ifShowYLine;
	}
}
