package com.chis.common.pojo;

import com.github.abel533.echarts.code.X;
import com.github.abel533.echarts.series.Line;

import java.util.List;

/**
 * @Description : 折线图图表参数
 * @ClassAuthor : anjing
 * @Date : 2020/7/29 15:11
 **/
public class LineChartParamPojo {

    /**标题*/
    private String title;
    /**x轴名称*/
    private String xAxisTitle;
    /**y轴名称*/
    private String yAxisTitle;
    /**图例*/
    private Object[] legend;
    /**图例位置，默认居左*/
    private X xlegend;
    /**x轴数据*/
    private Object[] xdate;
    /**折线图数据*/
    private List<Line> lines;
    /**x轴是否滑动*/
    private boolean ifside = false;
    /**x轴是否滑动默认刻度个数占比*/
    private Integer sideNum = 10;
    /**x轴名称是否旋转*/
    private boolean ifrotate = false;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getxAxisTitle() {
        return xAxisTitle;
    }

    public void setxAxisTitle(String xAxisTitle) {
        this.xAxisTitle = xAxisTitle;
    }

    public String getyAxisTitle() {
        return yAxisTitle;
    }

    public void setyAxisTitle(String yAxisTitle) {
        this.yAxisTitle = yAxisTitle;
    }

    public Object[] getLegend() {
        return legend;
    }

    public void setLegend(Object[] legend) {
        this.legend = legend;
    }

    public X getXlegend() {
        return xlegend;
    }

    public void setXlegend(X xlegend) {
        this.xlegend = xlegend;
    }

    public Object[] getXdate() {
        return xdate;
    }

    public void setXdate(Object[] xdate) {
        this.xdate = xdate;
    }

    public List<Line> getLines() {
        return lines;
    }

    public void setLines(List<Line> lines) {
        this.lines = lines;
    }

    public boolean isIfside() {
        return ifside;
    }

    public void setIfside(boolean ifside) {
        this.ifside = ifside;
    }

    public Integer getSideNum() {
        return sideNum;
    }

    public void setSideNum(Integer sideNum) {
        this.sideNum = sideNum;
    }

    public boolean isIfrotate() {
        return ifrotate;
    }

    public void setIfrotate(boolean ifrotate) {
        this.ifrotate = ifrotate;
    }
}
