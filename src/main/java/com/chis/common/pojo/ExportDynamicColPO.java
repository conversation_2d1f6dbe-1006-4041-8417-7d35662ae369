package com.chis.common.pojo;

import org.apache.poi.ss.usermodel.CellStyle;
/**
 * @Description: Excel 导出 单元格数据对象
 * 
 * @ClassAuthor pw,2022年04月12日,ExportDynamicColPO
 */
public class ExportDynamicColPO {
    /** 数据对象 */
    private Object colVal;
    /** 数据单元格样式 */
    private CellStyle colStyle;
    /** 合并行数，默认为1 */
    private Integer rowspan = 1;
    /** 合并列数，默认为1 */
    private Integer colspan = 1;
    /** 单元格宽度 默认3000 */
    private Integer colWidth = 3000;

    public Object getColVal() {
        return colVal;
    }

    public void setColVal(Object colVal) {
        this.colVal = colVal;
    }

    public CellStyle getColStyle() {
        return colStyle;
    }

    public void setColStyle(CellStyle colStyle) {
        this.colStyle = colStyle;
    }

    public Integer getRowspan() {
        return rowspan;
    }

    public void setRowspan(Integer rowspan) {
        this.rowspan = rowspan;
    }

    public Integer getColspan() {
        return colspan;
    }

    public void setColspan(Integer colspan) {
        this.colspan = colspan;
    }

    public Integer getColWidth() {
        return colWidth;
    }

    public void setColWidth(Integer colWidth) {
        this.colWidth = colWidth;
    }
}
