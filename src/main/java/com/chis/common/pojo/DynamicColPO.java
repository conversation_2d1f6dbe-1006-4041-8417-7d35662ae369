package com.chis.common.pojo;

import com.chis.common.utils.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;

import java.math.BigDecimal;

/**
 * <p>描述 动态单元格实体</p>
 *
 * @ClassAuthor gongzhe,2022/6/17 18:23,DynamicColPO
 */
public class DynamicColPO {
    /** 值类型 默认文本
     * 1 文本 2 Integer 3 BigDecimal
     * */
    private Integer type = 1;
    /**合并行数，默认为1*/
    private Integer rowspan = 1;
    /**合并列数，默认为1*/
    private Integer colspan = 1;
    /**前端页面样式*/
    private String style;
    /**字体颜色*/
    private String color;
    /**默认居中*/
    private String textAlign = "center";
    /** 文字加粗默认normal 合计需要设置成bold */
    private String fontWeight = "normal";

    /** 文本值 标题名称放文本值内 */
    private String colStrVal;
    /** Integer值 */
    private Integer colIntVal;
    /** BigDecimal值 */
    private BigDecimal colDecimalVal;


    /** 导出时单元格宽度 若不填写 默认3000 */
    private Integer columnWidth;
    /** 导出时单元格内容对齐方式 CellStyle.ALIGN_CENTER*/
    private Short cellAlign = CellStyle.ALIGN_CENTER;

    public DynamicColPO(){}
    public DynamicColPO(Integer type, Integer rowspan, Integer colspan, String style, String color,
                        String textAlign, String fontWeight, String colStrVal,
                        Integer colIntVal, BigDecimal colDecimalVal, Integer columnWidth,
                        Short cellAlign) {
        if(null != type){
            this.type = type;
        }
        if(null != rowspan){
            this.rowspan = rowspan;
        }
        if(null != colspan){
            this.colspan = colspan;
        }
        if(StringUtils.isNotBlank(style)){
            this.style = style;
        }
        if(StringUtils.isNotBlank(color)){
            this.color = color;
        }
        if(StringUtils.isNotBlank(textAlign)){
            this.textAlign = textAlign;
        }
        if(StringUtils.isNotBlank(fontWeight)){
            this.fontWeight = fontWeight;
        }
        if(StringUtils.isNotBlank(colStrVal)){
            this.colStrVal = colStrVal;
        }
        if(null != colIntVal){
            this.colIntVal = colIntVal;
        }
        if(null != colDecimalVal){
            this.colDecimalVal = colDecimalVal;
        }
        if(null != columnWidth){
            this.columnWidth = columnWidth;
        }
        if(null != cellAlign){
            this.cellAlign = cellAlign;
        }
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getRowspan() {
        return rowspan;
    }

    public void setRowspan(Integer rowspan) {
        this.rowspan = rowspan;
    }

    public Integer getColspan() {
        return colspan;
    }

    public void setColspan(Integer colspan) {
        this.colspan = colspan;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getTextAlign() {
        return textAlign;
    }

    public void setTextAlign(String textAlign) {
        this.textAlign = textAlign;
    }

    public String getFontWeight() {
        return fontWeight;
    }

    public void setFontWeight(String fontWeight) {
        this.fontWeight = fontWeight;
    }

    public String getColStrVal() {
        return colStrVal;
    }

    public void setColStrVal(String colStrVal) {
        this.colStrVal = colStrVal;
    }

    public Integer getColIntVal() {
        return colIntVal;
    }

    public void setColIntVal(Integer colIntVal) {
        this.colIntVal = colIntVal;
    }

    public BigDecimal getColDecimalVal() {
        return colDecimalVal;
    }

    public void setColDecimalVal(BigDecimal colDecimalVal) {
        this.colDecimalVal = colDecimalVal;
    }

    public Integer getColumnWidth() {
        return columnWidth;
    }

    public void setColumnWidth(Integer columnWidth) {
        this.columnWidth = columnWidth;
    }

    public Short getCellAlign() {
        return cellAlign;
    }

    public void setCellAlign(Short cellAlign) {
        this.cellAlign = cellAlign;
    }
}
