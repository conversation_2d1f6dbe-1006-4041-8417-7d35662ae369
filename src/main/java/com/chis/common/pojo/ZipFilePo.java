package com.chis.common.pojo;

import java.util.List;

/**
 * <p>类描述： CRC32压缩方式生成压缩文件的数据对象 </p>
 * @ClassAuthor： pw 2023/7/20
 **/
public class ZipFilePo {
    public ZipFilePo(){}
    public ZipFilePo(String name, String path){
        this.name = name;
        this.path = path;
    }
    /** 文件名称 */
    private String name;
    /** 文件路径 文件夹时 ifChangeChildName = true 可不传递*/
    private String path;
    /** 是否需要修改子文件名称
     * 如果false则不会读取childList，而是通过父文件夹listfile子文件
     * 如果true那么直接读取childList，不会通过父文件夹listfile子文件
     * 默认true
     * */
    private Boolean ifChangeChildName = Boolean.TRUE;
    /** 子文件集合
     * ifChangeChildName = true 时传递
     * 注意 子文件夹name需要父文件夹name+文件分隔符+实际name
     * 例如 u01文件夹下有tmp文件夹，tmp文件夹下有个a.jpg文件
     * 那么 tmp的name为u01/tmp    a.jpg的name为u01/tmp/a.jpg
     * */
    private List<ZipFilePo> childList;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Boolean getIfChangeChildName() {
        return ifChangeChildName;
    }

    public void setIfChangeChildName(Boolean ifChangeChildName) {
        this.ifChangeChildName = ifChangeChildName;
    }

    public List<ZipFilePo> getChildList() {
        return childList;
    }

    public void setChildList(List<ZipFilePo> childList) {
        this.childList = childList;
    }
}
