package com.chis.common.pojo;


import java.util.List;

/**
 * @Description: Excel 导出 行数据对象
 * 
 * @ClassAuthor pw,2022年04月12日,ExportDynamicRowPO
 */
public class ExportDynamicRowPO {
    /** 单元格列表 */
    private List<ExportDynamicColPO> cols;
    /** 行高 默认20 建议一级标题30 二级标题25 三级标题或者其他20*/
    private Float heightInPoints = 20f;

    /**是否设置行高自适应*/
    private boolean ifAutoRowHeight= Boolean.FALSE;

    public List<ExportDynamicColPO> getCols() {
        return cols;
    }

    public void setCols(List<ExportDynamicColPO> cols) {
        this.cols = cols;
    }

    public Float getHeightInPoints() {
        return heightInPoints;
    }

    public void setHeightInPoints(Float heightInPoints) {
        this.heightInPoints = heightInPoints;
    }

    public boolean getIfAutoRowHeight() {
        return ifAutoRowHeight;
    }

    public void setIfAutoRowHeight(boolean ifAutoRowHeight) {
        this.ifAutoRowHeight = ifAutoRowHeight;
    }
}
