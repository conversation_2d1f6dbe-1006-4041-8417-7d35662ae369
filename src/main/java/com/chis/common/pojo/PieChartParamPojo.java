package com.chis.common.pojo;

import com.github.abel533.echarts.series.Pie;

/**
 * <p>类描述：柱状图图表参数</p>
 * @ClassAuthor qrr,2020年6月19日,BarChartParamPojo
 * */
public class PieChartParamPojo{
	private Pie data;
	private String title;
	private Object[] legend;
	//饼图在水平方向上的中心位置
	private String centerX;
	//饼图在垂直方向上的中心位置
	private String centerY;
	//饼图的半径
	private String radius;
	private String tooltip = "{a}<br/>{b}：{c} <br/>占比：{d}%"; 
	public Pie getData() {
		return data;
	}
	public String getTitle() {
		return title;
	}
	public Object[] getLegend() {
		return legend;
	}
	public void setData(Pie data) {
		this.data = data;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public void setLegend(Object[] legend) {
		this.legend = legend;
	}
	public String getTooltip() {
		return tooltip;
	}
	public void setTooltip(String tooltip) {
		this.tooltip = tooltip;
	}

	public String getCenterX() {
		return centerX;
	}

	public void setCenterX(String centerX) {
		this.centerX = centerX;
	}

	public String getCenterY() {
		return centerY;
	}

	public void setCenterY(String centerY) {
		this.centerY = centerY;
	}

	public String getRadius() {
		return radius;
	}

	public void setRadius(String radius) {
		this.radius = radius;
	}
}
