package com.chis.common.pojo;

import com.github.abel533.echarts.axis.CategoryAxis;
/**
 * <p>类描述：横坐标，由于原有echarts的jar包中的CategoryAxis类无minInterval属性，故创建此类</p>
 * @ClassAuthor qrr,2021年4月21日,MyCategoryAxis
 * */
public class MyCategoryAxis extends CategoryAxis{
	private static final long serialVersionUID = 1L;
	private Integer minInterval;

	public Integer getMinInterval() {
		return minInterval;
	}

	public void setMinInterval(Integer minInterval) {
		this.minInterval = minInterval;
	}
}
