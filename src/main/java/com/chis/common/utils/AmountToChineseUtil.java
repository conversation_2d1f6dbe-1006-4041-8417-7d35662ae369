package com.chis.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>类描述：金额转换成中文工具类 </p>
 * 参考 https://www.csdn.net/tags/NtTakg1sOTkzNDUtYmxvZwO0O0OO0O0O.html
 * @ClassAuthor pw,2022年06月1日,AmountToChineseUtil
 */
public class AmountToChineseUtil {
    private static final String[] RMB_ARR = { "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖" };
    private static final String[] UNIT_ARR = { "", "拾", "佰", "仟" };
    private static final String[] HIGH_UINIT_ARR = { "", "万", "亿" };
    private static final String[] LOW_UNIT_ARR = { "角", "分" };
    private static final String RMB_UNIT = "元";
    private static final String RMB_INTEGER = "整";
    private static final String ZERO_STR = "0";
    private static final String DOUBLE_ZERO_STR = "00";
    private static final char ZERO_CHR = '0';
    private static final int SCALE = 0;
    /** 限制最大千亿 */
    private static final int LIMIT_LEN = 12;
    /**
     * BigDecimal类型数据取整数并转为字符串
     */
    private static String greaterZeroToInt(BigDecimal amount) {
        if (amount == null) {
            return ZERO_STR;
        }
        return amount.divide(new BigDecimal(1), SCALE, RoundingMode.DOWN).toString();
    }

    /**
     * BigDecimal类型数据取整数
     */
    private static BigDecimal greaterZeroNum(BigDecimal amount) {
        return amount.divide(new BigDecimal(1), SCALE, RoundingMode.DOWN);
    }

    /**
     * BigDecimal类型数据取2位小数转为字符串整数，不足2位十位补0
     */
    private static String lessZeroToInt(BigDecimal amount) {
        String intStr = amount.movePointRight(2).divide(new BigDecimal(1), SCALE, RoundingMode.HALF_UP).toBigInteger()
                .toString();
        if (intStr.length() == 1) {
            return ZERO_STR + intStr;
        }
        return intStr;
    }

    /**
     * 整数转人民币
     */
    private static String itoRmbStr(String numStr) {
        StringBuilder resultBuffer = new StringBuilder();
        if (ZERO_STR.equals(numStr)) {
            return resultBuffer.toString();
        }
        int len = numStr.length();
        //不允许超过千亿
        if (len > LIMIT_LEN) {
            return numStr;
        }
        int col = (len - 1) / 4;
        int row = (len - 1) % 4;
        int index = 0;
        for (int i = 0; i <= col; i++) {
            StringBuilder buffer = new StringBuilder();
            for (int j = 0; j <= row; j++) {
                if (numStr.charAt(index) == ZERO_CHR) {
                    if ((numStr.charAt(index - 1) == ZERO_CHR && j != 0) || j == row) {
                        index++;
                        continue;
                    }
                    buffer.append(RMB_ARR[numStr.charAt(index) - ZERO_CHR]);
                } else {
                    buffer.append(RMB_ARR[numStr.charAt(index) - ZERO_CHR]);
                    buffer.append(UNIT_ARR[row - j]);
                }
                index++;
            }
            String str = buffer.toString();
            if(null != str && str.endsWith("零")){
                str = str.substring(0, str.length()-1);
            }
            if(!"零".equals(buffer.toString())){
                resultBuffer.append(str).append(HIGH_UINIT_ARR[col - i]);
            }
            row = 3;
        }
        /*String str = resultBuffer.toString();
        if(null != str && str.endsWith("零")){
            str = str.substring(0, str.length()-1);
        }*/
        resultBuffer.append(RMB_UNIT);
        return resultBuffer.toString();
    }

    /**
     * 小数转人民币
     */
    private static String ftoRmbStr(String numStr, boolean ifHasFen) {
        String result = "";
        if (numStr.equals(DOUBLE_ZERO_STR) || numStr.equals(ZERO_STR)){
            return RMB_INTEGER;
        }
        result += RMB_ARR[numStr.charAt(0) - ZERO_CHR];
        if (numStr.charAt(0) != ZERO_CHR) {
            result += LOW_UNIT_ARR[0];
        }
        if(ifHasFen){
            if (numStr.charAt(1) != ZERO_CHR) {
                result += RMB_ARR[numStr.charAt(1) - ZERO_CHR];
                result += LOW_UNIT_ARR[1];
            }
        }
        return result;
    }

    /**
     * 数字转人民币
     */
    private static String change(BigDecimal amount,boolean ifHasFen) {
        if(null != amount && amount.compareTo(BigDecimal.ZERO) == 0){
            return "零元整";
        }
        String greaterZeroToInt = greaterZeroToInt(amount);
        String itoRmbStr = itoRmbStr(greaterZeroToInt);
        BigDecimal lessZeroNum = amount.subtract(greaterZeroNum(amount));
        String lessZeroToInt = lessZeroToInt(lessZeroNum);
        String ftoRmbStr = ftoRmbStr(lessZeroToInt, ifHasFen);
        return itoRmbStr + ftoRmbStr;
    }

    /**
     * 格式化人名币数字为￥12,343,171.60类似格式(带小数位)
     */
    public static String formatYuan(BigDecimal amount) {
        return formatNumber(amount, 2, 4);
    }

    /**
     * 格式化人名币数字为￥12,343,171类似格式(不带小数位)
     */
    public static String formatFen(BigDecimal amount) {
        return formatNumber(amount, 0, 0);
    }

    /**
     * 格式化人名币数字为￥12343171类似格式(不带小数位且不带分隔符)
     */
    public static String formatFenNoSymbol(BigDecimal amount) {
        return formatFen(amount).replace(",", "");
    }

    /**
     *  格式化人名币数字
     * @function formatNumber
     * @param amount 金额
     * @param minDigits 设置数的小数部分所允许的最小位数(如果不足后面补0)
     * @param maxDigits 设置数的小数部分所允许的最大位数(如果超过会四舍五入)
     * @return String
     *
     */
    private static String formatNumber(BigDecimal amount, int minDigits, int maxDigits) {
        NumberFormat currency = NumberFormat.getCurrencyInstance();
        currency.setMinimumFractionDigits(minDigits);
        currency.setMaximumFractionDigits(maxDigits);
        return currency.format(amount);
    }

    /**
     * <p>方法描述：金额转换成中文 </p>
     * BigDecimal decimal = new BigDecimal("33.91");
     * System.out.println(convertAmountToChinese(decimal, "（", "）", true)); 返回33.91（叁十叁元玖角壹分）
     * System.out.println(convertAmountToChinese(decimal, "（", "）", false)); 返回33.91（叁十叁元玖角）
     * @param amount 金额
     * @param pre 示例中的左括号 pre以及after全不为空时有效 否则仅返回示例中的 叁十叁元玖角壹分 中文部分
     * @param after 示例中的右括号
     * @param ifHasFen 是否包含分
     * @MethodAuthor pw,2022年06月1日
     */
    public static String convertAmountToChinese(BigDecimal amount, String pre, String after, boolean ifHasFen){
        if(null == amount){
            return "";
        }
        int scale = amount.scale();
        BigDecimal decimal = amount;
        if(scale > 0){
            while((amount.setScale(scale-1, RoundingMode.HALF_UP)).compareTo(amount) == 0){
                decimal = amount.setScale(scale-1, RoundingMode.HALF_UP);
                scale --;
            }
        }
        StringBuffer buffer = new StringBuffer();
        String chinese = change(decimal,ifHasFen);
        if(StringUtils.isNotBlank(pre) && StringUtils.isNotBlank(after)){
            buffer.append(decimal.toPlainString()).append(pre).append(chinese).append(after);
        }else{
            buffer.append(chinese);
        }
        return buffer.toString();
    }

    public static void main(String[] args) {
        List<BigDecimal> list = new ArrayList<>();
        BigDecimal decimal = new BigDecimal("1");
        list.add(decimal);
        decimal = new BigDecimal("10");
        list.add(decimal);
        decimal = new BigDecimal("100");
        list.add(decimal);
        decimal = new BigDecimal("1000");
        list.add(decimal);
        decimal = new BigDecimal("10000");
        list.add(decimal);
        decimal = new BigDecimal("100000");
        list.add(decimal);
        decimal = new BigDecimal("1000000");
        list.add(decimal);
        decimal = new BigDecimal("10000000");
        list.add(decimal);
        decimal = new BigDecimal("100000000");
        list.add(decimal);
        decimal = new BigDecimal("1000000000");
        list.add(decimal);
        decimal = new BigDecimal("10000000000");
        list.add(decimal);
        decimal = new BigDecimal("100000000000");
        list.add(decimal);
        decimal = new BigDecimal("1000000000000");
        list.add(decimal);
        decimal = new BigDecimal("11");
        list.add(decimal);
        decimal = new BigDecimal("101");
        list.add(decimal);
        decimal = new BigDecimal("1001");
        list.add(decimal);
        decimal = new BigDecimal("10001");
        list.add(decimal);
        decimal = new BigDecimal("100001");
        list.add(decimal);
        decimal = new BigDecimal("1000001");
        list.add(decimal);
        decimal = new BigDecimal("10000001");
        list.add(decimal);
        decimal = new BigDecimal("100000001");
        list.add(decimal);
        decimal = new BigDecimal("1000000001");
        list.add(decimal);
        decimal = new BigDecimal("10000000001");
        list.add(decimal);
        decimal = new BigDecimal("100000000001");
        list.add(decimal);
        decimal = new BigDecimal("1000000000001");
        list.add(decimal);
        decimal = new BigDecimal("1.10");
        list.add(decimal);
        decimal = new BigDecimal("11.11");
        list.add(decimal);
        decimal = new BigDecimal("101.21");
        list.add(decimal);
        decimal = new BigDecimal("1001.3");
        list.add(decimal);
        decimal = new BigDecimal("10001.4");
        list.add(decimal);
        decimal = new BigDecimal("100001.0");
        list.add(decimal);
        decimal = new BigDecimal("1000001.1");
        list.add(decimal);
        decimal = new BigDecimal("10000001.8");
        list.add(decimal);
        decimal = new BigDecimal("100000001.1");
        list.add(decimal);
        decimal = new BigDecimal("1000000001.1");
        list.add(decimal);
        decimal = new BigDecimal("10000000001.1");
        list.add(decimal);
        decimal = new BigDecimal("100000000001.1");
        list.add(decimal);
        decimal = new BigDecimal("1000000000001.1");
        list.add(decimal);
        for(BigDecimal bigDecimal : list){
            System.out.println(convertAmountToChinese(bigDecimal, "（", "）", true));
        }
    }
}
