package com.chis.common.utils;

import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.model.OSSObject;
import com.chis.common.pojo.ZipFilePo;
import com.qcloud.cos.model.COSObject;
import org.apache.commons.fileupload.FileItem;
import org.apache.log4j.Logger;
import org.apache.pdfbox.cos.COSBase;
import org.apache.pdfbox.cos.COSDictionary;
import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageTree;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipFile;
import org.apache.tools.zip.ZipOutputStream;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.*;
import java.util.zip.CRC32;
import java.util.zip.CheckedInputStream;

/**
 * 文件操作工具类 实现文件的创建、删除、复制、压缩、解压以及目录的创建、删除、复制、压缩解压等功能<br/>
 * 
 * <AUTHOR>
 * @createTime 2015年11月5日
 */
public class FileUtils extends org.apache.commons.io.FileUtils {

	private static Logger log = Logger.getLogger(FileUtils.class);
	/** 文件上传单次字节长度 */
	private static int FILE_UPLOAD_SINGLE_BYTE = 4096;
	/**
	 * 复制单个文件，如果目标文件存在，则不覆盖
	 * 
	 * @param srcFileName
	 *            待复制的文件名
	 * @param descFileName
	 *            目标文件名
	 * @return 如果复制成功，则返回true，否则返回false
	 */
	public static boolean copyFile(String srcFileName, String descFileName) {
		return FileUtils.copyFileCover(srcFileName, descFileName, false);
	}

	/**
	 * 复制单个文件
	 * 
	 * @param srcFileName
	 *            待复制的文件名
	 * @param descFileName
	 *            目标文件名
	 * @param coverlay
	 *            如果目标文件已存在，是否覆盖
	 * @return 如果复制成功，则返回true，否则返回false
	 */
	public static boolean copyFileCover(String srcFileName, String descFileName, boolean coverlay) {
		File srcFile = new File(srcFileName);
		// 判断源文件是否存在
		if (!srcFile.exists()) {
			log.debug("复制文件失败，源文件 " + srcFileName + " 不存在!");
			return false;
		}
		// 判断源文件是否是合法的文件
		else if (!srcFile.isFile()) {
			log.debug("复制文件失败，" + srcFileName + " 不是一个文件!");
			return false;
		}
		File descFile = new File(descFileName);
		// 判断目标文件是否存在
		if (descFile.exists()) {
			// 如果目标文件存在，并且允许覆盖
			if (coverlay) {
				log.debug("目标文件已存在，准备删除!");
				if (!FileUtils.delFile(descFileName)) {
					log.debug("删除目标文件 " + descFileName + " 失败!");
					return false;
				}
			} else {
				log.debug("复制文件失败，目标文件 " + descFileName + " 已存在!");
				return false;
			}
		} else {
			if (!descFile.getParentFile().exists()) {
				// 如果目标文件所在的目录不存在，则创建目录
				log.debug("目标文件所在的目录不存在，创建目录!");
				// 创建目标文件所在的目录
				if (!descFile.getParentFile().mkdirs()) {
					log.debug("创建目标文件所在的目录失败!");
					return false;
				}
			}
		}

		// 准备复制文件
		// 读取的位数
		int readByte = 0;
		InputStream ins = null;
		OutputStream outs = null;
		try {
			// 打开源文件
			ins = new FileInputStream(srcFile);
			// 打开目标文件的输出流
			outs = new FileOutputStream(descFile);
			byte[] buf = new byte[1024];
			// 一次读取1024个字节，当readByte为-1时表示文件已经读取完毕
			while ((readByte = ins.read(buf)) != -1) {
				// 将读取的字节流写入到输出流
				outs.write(buf, 0, readByte);
			}
			log.debug("复制单个文件 " + srcFileName + " 到" + descFileName + "成功!");
			return true;
		} catch (Exception e) {
			log.debug("复制文件失败：" + e.getMessage());
			return false;
		} finally {
			// 关闭输入输出流，首先关闭输出流，然后再关闭输入流
			if (outs != null) {
				try {
					outs.close();
				} catch (IOException oute) {
					oute.printStackTrace();
				}
			}
			if (ins != null) {
				try {
					ins.close();
				} catch (IOException ine) {
					ine.printStackTrace();
				}
			}
		}
	}

	/**
	 * 复制整个目录的内容，如果目标目录存在，则不覆盖
	 * 
	 * @param srcDirName
	 *            源目录名
	 * @param descDirName
	 *            目标目录名
	 * @return 如果复制成功返回true，否则返回false
	 */
	public static boolean copyDirectory(String srcDirName, String descDirName) {
		return FileUtils.copyDirectoryCover(srcDirName, descDirName, false);
	}

	/**
	 * 复制整个目录的内容
	 * 
	 * @param srcDirName
	 *            源目录名
	 * @param descDirName
	 *            目标目录名
	 * @param coverlay
	 *            如果目标目录存在，是否覆盖
	 * @return 如果复制成功返回true，否则返回false
	 */
	public static boolean copyDirectoryCover(String srcDirName, String descDirName, boolean coverlay) {
		File srcDir = new File(srcDirName);
		// 判断源目录是否存在
		if (!srcDir.exists()) {
			log.debug("复制目录失败，源目录 " + srcDirName + " 不存在!");
			return false;
		}
		// 判断源目录是否是目录
		else if (!srcDir.isDirectory()) {
			log.debug("复制目录失败，" + srcDirName + " 不是一个目录!");
			return false;
		}
		// 如果目标文件夹名不以文件分隔符结尾，自动添加文件分隔符
		String descDirNames = descDirName;
		if (!descDirNames.endsWith(File.separator)) {
			descDirNames = descDirNames + File.separator;
		}
		File descDir = new File(descDirNames);
		// 如果目标文件夹存在
		if (descDir.exists()) {
			if (coverlay) {
				// 允许覆盖目标目录
				log.debug("目标目录已存在，准备删除!");
				if (!FileUtils.delFile(descDirNames)) {
					log.debug("删除目录 " + descDirNames + " 失败!");
					return false;
				}
			} else {
				log.debug("目标目录复制失败，目标目录 " + descDirNames + " 已存在!");
				return false;
			}
		} else {
			// 创建目标目录
			log.debug("目标目录不存在，准备创建!");
			if (!descDir.mkdirs()) {
				log.debug("创建目标目录失败!");
				return false;
			}

		}

		boolean flag = true;
		// 列出源目录下的所有文件名和子目录名
		File[] files = srcDir.listFiles();
		for (int i = 0; i < files.length; i++) {
			// 如果是一个单个文件，则直接复制
			if (files[i].isFile()) {
				flag = FileUtils.copyFile(files[i].getAbsolutePath(), descDirName + files[i].getName());
				// 如果拷贝文件失败，则退出循环
				if (!flag) {
					break;
				}
			}
			// 如果是子目录，则继续复制目录
			if (files[i].isDirectory()) {
				flag = FileUtils.copyDirectory(files[i].getAbsolutePath(), descDirName + files[i].getName());
				// 如果拷贝目录失败，则退出循环
				if (!flag) {
					break;
				}
			}
		}

		if (!flag) {
			log.debug("复制目录 " + srcDirName + " 到 " + descDirName + " 失败!");
			return false;
		}
		log.debug("复制目录 " + srcDirName + " 到 " + descDirName + " 成功!");
		return true;

	}

	/**
	 * 
	 * 删除文件，可以删除单个文件或文件夹
	 * 
	 * @param fileName
	 *            被删除的文件名
	 * @return 如果删除成功，则返回true，否是返回false
	 */
	public static boolean delFile(String fileName) {
		File file = new File(fileName);
		if (!file.exists()) {
			log.debug(fileName + " 文件不存在!");
			return true;
		} else {
			if (file.isFile()) {
				return FileUtils.deleteFile(fileName);
			} else {
				return FileUtils.deleteDirectory(fileName);
			}
		}
	}

	/**
	 * 
	 * 删除单个文件
	 * 
	 * @param fileName
	 *            被删除的文件名
	 * @return 如果删除成功，则返回true，否则返回false
	 */
	public static boolean deleteFile(String fileName) {
		File file = new File(fileName);
		if (file.exists() && file.isFile()) {
			if (file.delete()) {
				log.debug("删除文件 " + fileName + " 成功!");
				return true;
			} else {
				log.debug("删除文件 " + fileName + " 失败!");
				return false;
			}
		} else {
			log.debug(fileName + " 文件不存在!");
			return true;
		}
	}

	/**
	 * 
	 * 删除目录及目录下的文件
	 * 
	 * @param dirName
	 *            被删除的目录所在的文件路径
	 * @return 如果目录删除成功，则返回true，否则返回false
	 */
	public static boolean deleteDirectory(String dirName) {
		String dirNames = dirName;
		if (!dirNames.endsWith(File.separator)) {
			dirNames = dirNames + File.separator;
		}
		File dirFile = new File(dirNames);
		if (!dirFile.exists() || !dirFile.isDirectory()) {
			log.debug(dirNames + " 目录不存在!");
			return true;
		}
		boolean flag = true;
		// 列出全部文件及子目录
		File[] files = dirFile.listFiles();
		for (int i = 0; i < files.length; i++) {
			// 删除子文件
			if (files[i].isFile()) {
				flag = FileUtils.deleteFile(files[i].getAbsolutePath());
				// 如果删除文件失败，则退出循环
				if (!flag) {
					break;
				}
			}
			// 删除子目录
			else if (files[i].isDirectory()) {
				flag = FileUtils.deleteDirectory(files[i].getAbsolutePath());
				// 如果删除子目录失败，则退出循环
				if (!flag) {
					break;
				}
			}
		}

		if (!flag) {
			log.debug("删除目录失败!");
			return false;
		}
		// 删除当前目录
		if (dirFile.delete()) {
			log.debug("删除目录 " + dirName + " 成功!");
			return true;
		} else {
			log.debug("删除目录 " + dirName + " 失败!");
			return false;
		}

	}

	/**
	 * 创建单个文件
	 * 
	 * @param descFileName
	 *            文件名，包含路径
	 * @return 如果创建成功，则返回true，否则返回false
	 */
	public static boolean createFile(String descFileName) {
		File file = new File(descFileName);
		if (file.exists()) {
			log.debug("文件 " + descFileName + " 已存在!");
			return false;
		}
		if (descFileName.endsWith(File.separator)) {
			log.debug(descFileName + " 为目录，不能创建目录!");
			return false;
		}
		if (!file.getParentFile().exists()) {
			// 如果文件所在的目录不存在，则创建目录
			if (!file.getParentFile().mkdirs()) {
				log.debug("创建文件所在的目录失败!");
				return false;
			}
		}

		// 创建文件
		try {
			if (file.createNewFile()) {
				log.debug(descFileName + " 文件创建成功!");
				return true;
			} else {
				log.debug(descFileName + " 文件创建失败!");
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.debug(descFileName + " 文件创建失败!");
			return false;
		}

	}

	/**
	 * 创建目录
	 * 
	 * @param descDirName
	 *            目录名,包含路径
	 * @return 如果创建成功，则返回true，否则返回false
	 */
	public static boolean createDirectory(String descDirName) {
		String descDirNames = descDirName;
		if (!descDirNames.endsWith(File.separator)) {
			descDirNames = descDirNames + File.separator;
		}
		File descDir = new File(descDirNames);
		if (descDir.exists()) {
			log.debug("目录 " + descDirNames + " 已存在!");
			return false;
		}
		// 创建目录
		if (descDir.mkdirs()) {
			log.debug("目录 " + descDirNames + " 创建成功!");
			return true;
		} else {
			log.debug("目录 " + descDirNames + " 创建失败!");
			return false;
		}

	}

	/**
	 * 写入文件
	 * 
	 * @param file
	 *            要写入的文件
	 */
	public static void writeToFile(String fileName, String content, boolean append) {
		try {
			org.apache.commons.io.FileUtils.write(new File(fileName), content, "utf-8", append);
			log.debug("文件 " + fileName + " 写入成功!");
		} catch (IOException e) {
			log.debug("文件 " + fileName + " 写入失败! " + e.getMessage());
		}
	}

	/**
	 * 写入文件
	 * 
	 * @param file
	 *            要写入的文件
	 */
	public static void writeToFile(String fileName, String content, String encoding, boolean append) {
		try {
			org.apache.commons.io.FileUtils.write(new File(fileName), content, encoding, append);
			log.debug("文件 " + fileName + " 写入成功!");
		} catch (IOException e) {
			log.debug("文件 " + fileName + " 写入失败! " + e.getMessage());
		}
	}

	/**
	 * 压缩文件或目录
	 * 
	 * @param srcDirName
	 *            压缩的根目录
	 * @param fileName
	 *            根目录下的待压缩的文件名或文件夹名，其中*或""表示跟目录下的全部文件
	 * @param descFileName
	 *            目标zip文件
	 */
	public static void zipFiles(String srcDirName, String fileName, String descFileName) {
		// 判断目录是否存在
		if (srcDirName == null) {
			log.debug("文件压缩失败，目录 " + srcDirName + " 不存在!");
			return;
		}
		File fileDir = new File(srcDirName);
		if (!fileDir.exists() || !fileDir.isDirectory()) {
			log.debug("文件压缩失败，目录 " + srcDirName + " 不存在!");
			return;
		}
		String dirPath = fileDir.getAbsolutePath();
		File descFile = new File(descFileName);
		try {
			ZipOutputStream zouts = new ZipOutputStream(new FileOutputStream(descFile));
			if ("*".equals(fileName) || "".equals(fileName)) {
				FileUtils.zipDirectoryToZipFile(dirPath, fileDir, zouts);
			} else {
				File file = new File(fileDir, fileName);
				if (file.isFile()) {
					FileUtils.zipFilesToZipFile(dirPath, file, zouts);
				} else {
					FileUtils.zipDirectoryToZipFile(dirPath, file, zouts);
				}
			}
			zouts.close();
			log.debug(descFileName + " 文件压缩成功!");
		} catch (Exception e) {
			log.debug("文件压缩失败：" + e.getMessage());
			e.printStackTrace();
		}

	}

	/**
	 * 解压缩ZIP文件，将ZIP文件里的内容解压到descFileName目录下
	 * 
	 * @param zipFileName
	 *            需要解压的ZIP文件
	 * @param descFileName
	 *            目标文件
	 */
	public static boolean unZipFiles(String zipFileName, String descFileName) {
		String descFileNames = descFileName;
		if (!descFileNames.endsWith(File.separator)) {
			descFileNames = descFileNames + File.separator;
		}
		try {
			// 根据ZIP文件创建ZipFile对象
			ZipFile zipFile = new ZipFile(zipFileName);
			ZipEntry entry = null;
			String entryName = null;
			String descFileDir = null;
			byte[] buf = new byte[4096];
			int readByte = 0;
			// 获取ZIP文件里所有的entry
			@SuppressWarnings("rawtypes")
			Enumeration enums = zipFile.getEntries();
			// 遍历所有entry
			while (enums.hasMoreElements()) {
				entry = (ZipEntry) enums.nextElement();
				// 获得entry的名字
				entryName = entry.getName();
				descFileDir = descFileNames + entryName;
				if (entry.isDirectory()) {
					// 如果entry是一个目录，则创建目录
					new File(descFileDir).mkdirs();
					continue;
				} else {
					// 如果entry是一个文件，则创建父目录
					new File(descFileDir).getParentFile().mkdirs();
				}
				File file = new File(descFileDir);
				// 打开文件输出流
				OutputStream os = new FileOutputStream(file);
				// 从ZipFile对象中打开entry的输入流
				InputStream is = zipFile.getInputStream(entry);
				while ((readByte = is.read(buf)) != -1) {
					os.write(buf, 0, readByte);
				}
				os.close();
				is.close();
			}
			zipFile.close();
			log.debug("文件解压成功!");
			return true;
		} catch (Exception e) {
			log.debug("文件解压失败：" + e.getMessage());
			return false;
		}
	}

	/**
	 * 将目录压缩到ZIP输出流
	 * 
	 * @param dirPath
	 *            目录路径
	 * @param fileDir
	 *            文件信息
	 * @param zouts
	 *            输出流
	 */
	public static void zipDirectoryToZipFile(String dirPath, File fileDir, ZipOutputStream zouts) {
		if (fileDir.isDirectory()) {
			File[] files = fileDir.listFiles();
			// 空的文件夹
			if (files.length == 0) {
				// 目录信息
				ZipEntry entry = new ZipEntry(getEntryName(dirPath, fileDir));
				try {
					zouts.putNextEntry(entry);
					zouts.closeEntry();
				} catch (Exception e) {
					e.printStackTrace();
				}
				return;
			}

			for (int i = 0; i < files.length; i++) {
				if (files[i].isFile()) {
					// 如果是文件，则调用文件压缩方法
					FileUtils.zipFilesToZipFile(dirPath, files[i], zouts);
				} else {
					// 如果是目录，则递归调用
					FileUtils.zipDirectoryToZipFile(dirPath, files[i], zouts);
				}
			}

		}

	}

	/**
	 * 将文件压缩到ZIP输出流
	 * 
	 * @param dirPath
	 *            目录路径
	 * @param file
	 *            文件
	 * @param zouts
	 *            输出流
	 */
	public static void zipFilesToZipFile(String dirPath, File file, ZipOutputStream zouts) {
		FileInputStream fin = null;
		ZipEntry entry = null;
		// 创建复制缓冲区
		byte[] buf = new byte[4096];
		int readByte = 0;
		if (file.isFile()) {
			try {
				// 创建一个文件输入流
				fin = new FileInputStream(file);
				// 创建一个ZipEntry
				entry = new ZipEntry(getEntryName(dirPath, file));
				// 存储信息到压缩文件
				zouts.putNextEntry(entry);
				// 复制字节到压缩文件
				while ((readByte = fin.read(buf)) != -1) {
					zouts.write(buf, 0, readByte);
				}
				zouts.closeEntry();
				fin.close();
				System.out.println("添加文件 " + file.getAbsolutePath() + " 到zip文件中!");
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

	}

	/**
	 * 获取待压缩文件在ZIP文件中entry的名字，即相对于跟目录的相对路径名
	 * 
	 * @param dirPat
	 *            目录名
	 * @param file
	 *            entry文件名
	 * @return
	 */
	private static String getEntryName(String dirPath, File file) {
		String dirPaths = dirPath;
		if (!dirPaths.endsWith(File.separator)) {
			dirPaths = dirPaths + File.separator;
		}
		String filePath = file.getAbsolutePath();
		// 对于目录，必须在entry名字后面加上"/"，表示它将以目录项存储
		if (file.isDirectory()) {
			filePath += "/";
		}
		int index = filePath.indexOf(dirPaths);

		return filePath.substring(index + dirPaths.length());
	}

	/**
	 * 修复路径，将 \\ 或 / 等替换为 File.separator
	 * 
	 * @param path
	 * @return
	 */
	public static String path(String path) {
		String p = StringUtils.replace(path, "\\", "/");
		p = StringUtils.join(StringUtils.split(p, "/"), "/");
		if (!StringUtils.startsWithAny(p, "/") && StringUtils.startsWithAny(path, "\\", "/")) {
			p += "/";
		}
		if (!StringUtils.endsWithAny(p, "/") && StringUtils.endsWithAny(path, "\\", "/")) {
			p = p + "/";
		}
		return p;
	}

	/**
	 * Enhancement of java.io.File#createNewFile() Create the given file. If the
	 * parent directory don't exists, we will create them all.
	 * 
	 * @param file
	 *            the file to be created
	 * @return true if the named file does not exist and was successfully
	 *         created; false if the named file already exists
	 * @see java.io.File#createNewFile
	 * @throws IOException
	 */
	public static boolean createFile(File file) throws IOException {
		if (!file.exists()) {
			makeDir(file.getParentFile());
		}
		return file.createNewFile();
	}

	/**
	 * Enhancement of java.io.File#mkdir() Create the given directory . If the
	 * parent folders don't exists, we will create them all.
	 * 
	 * @see java.io.File#mkdir()
	 * @param dir
	 *            the directory to be created
	 */
	public static void makeDir(File dir) {
		if (!dir.getParentFile().exists()) {
			makeDir(dir.getParentFile());
		}
		dir.mkdir();
	}

	/**
	 * 文件转化为字节数组
	 * 
	 * @param file
	 *            文件
	 * @return 字节数组
	 */
	public static byte[] convertFile2Bytes(File file) {
		byte[] ret = null;
		try {
			if (file == null) {
				return null;
			}
			FileInputStream in = new FileInputStream(file);
			ByteArrayOutputStream out = new ByteArrayOutputStream(4096);
			byte[] b = new byte[4096];
			int n;
			while ((n = in.read(b)) != -1) {
				out.write(b, 0, n);
			}
			in.close();
			out.close();
			ret = out.toByteArray();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return ret;
	}
	
	/**
	 * 根据相对路径读取文件 <br/>
	 * @param relativePath spring环境中classpath下的相对路径，不要以/开头 <br/>
	 * @return
	 */
	public static String getFileContentRelativePath(String relativePath) {
		if (StringUtils.isNotBlank(relativePath)) {
			try {
				//地址根目录
				File file = ResourceUtils.getFile("classpath:" + relativePath);
				InputStream is = new FileInputStream(file);
				return getFileContentStream(is);
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			} 
		}
		return null;
	}
	
	/**
	 * 直接根据路径读取文件
	 * @param absolutePaht
	 * @return
	 */
	public static String getFileContentAbsPath(String absolutePaht) {
		if (StringUtils.isNotBlank(absolutePaht)) {
			try {
				//地址根目录
				File file = new File(absolutePaht);
				InputStream is = new FileInputStream(file);
				return getFileContentStream(is);
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			} 
		}
		return null;
	}
	
	/**
	 * 根据WebRoot下的路径文件名获取文件内容
	 * /home/<USER>/gusuyimiao-7.0.67/webapps/Web//resources/freemarker/system/dynaModelPf.xml
	 * @return
	 */
	public static String getFileContent(String filePathStartFromWebRoot) {
		if (StringUtils.isNotBlank(filePathStartFromWebRoot)) {
			try {
				//地址根目录
				String xmlPath = getWebRootPath() + filePathStartFromWebRoot;
				File file = new File(xmlPath);
				InputStream is = new FileInputStream(file);
				return getFileContentStream(is);
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			} 
		}
		return null;
	}
	
	/**
	 * 根据输入流读取文件内容
	 * @param is
	 * @return
	 */
	public static String getFileContentStream(InputStream is) {
		BufferedReader br = null;
		try {
			//地址根目录
			br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			StringBuilder sb = new StringBuilder();
			while (true) {
				String line = br.readLine();
				if (line == null) {
					break;
				}
				sb.append(line).append("\r\n");
			}
			return sb.toString();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		} finally {
			try {
				if (is != null) {
					is.close();
				}
				if (br != null) {
					br.close();
				}
			} catch (IOException e1) {
				e1.printStackTrace();
			}
		}
	}

	/**
	 * 从网页地址下载文件，返回是否下载成功
	 * 
	 * @param fileUrl 
	 * @param filePath
	 * @return
	 */
	public static boolean downLoadUrlFile(String fileUrl, String filePath) {
		try {
			String ml = filePath.substring(0, filePath.lastIndexOf("/"));
			File file = new File(ml);
			if (!file.exists()) {
				file.mkdirs();
			}
			FileUtils.copyURLToFile(new URL(fileUrl), new File(filePath));
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}
	

	public static HttpServletResponse downLoadFiles(String tempzip, String srcpath, HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		try {
			//压缩文件
			File zipfile = new File(tempzip);
			if (!zipfile.exists()){
				zipfile.createNewFile();
			}
			response.reset();
			//创建文件输出流
			FileOutputStream fous = new FileOutputStream(zipfile);
			java.util.zip.ZipOutputStream zipOut = new java.util.zip.ZipOutputStream(fous);
			File file=new File(srcpath);
			File[] tempList = file.listFiles();
			for(File srcfile : tempList) {
				zipFile(srcfile, zipOut);
			}
			zipOut.close();
			fous.close();
			return downloadZip(zipfile,response);
		}catch (Exception e) {
			e.printStackTrace();
		}
		return response ;
	}

	public static HttpServletResponse downloadZip(File file,HttpServletResponse response) {
		try {
			// 以流的形式下载文件。
			InputStream fis = new BufferedInputStream(new FileInputStream(file.getPath()));
			byte[] buffer = new byte[fis.available()];
			fis.read(buffer);
			fis.close();
			// 清空response
			response.reset();

			OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
			response.setContentType("application/octet-stream");

			//如果输出的是中文名的文件，在此处就要用URLEncoder.encode方法进行处理
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getName(), "UTF-8"));
			toClient.write(buffer);
			toClient.flush();
			toClient.close();
		} catch (IOException ex) {
			ex.printStackTrace();
		}finally{
			try {
				File f = new File(file.getPath());
				f.delete();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return response;
	}

	public static void zipFile(File inputFile, java.util.zip.ZipOutputStream ouputStream) {
		try {
			if(inputFile.exists()) {
				/**如果是目录的话这里是不采取操作的，
				 * 至于目录的打包正在研究中*/
				if (inputFile.isFile()) {
					FileInputStream IN = new FileInputStream(inputFile);
					BufferedInputStream bins = new BufferedInputStream(IN, 512);
					//org.apache.tools.zip.ZipEntry
					java.util.zip.ZipEntry entry = new java.util.zip.ZipEntry(inputFile.getName());
					ouputStream.putNextEntry(entry);
					// 向压缩文件中输出数据
					int nNumber;
					byte[] buffer = new byte[512];
					while ((nNumber = bins.read(buffer)) != -1) {
						ouputStream.write(buffer, 0, nNumber);
					}
					// 关闭创建的流对象
					bins.close();
					IN.close();
				} else {
					try {
						File[] files = inputFile.listFiles();
						for (int i = 0; i < files.length; i++) {
							zipFile(files[i], ouputStream);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 递归删除目录下的所有文件及子目录下所有文件
	 * @param dir 将要删除的文件目录
	 */
	public static boolean deleteDir(File dir) {
		if (dir.isDirectory()) {
			String[] children = dir.list();
			//递归删除目录中的子目录下
			for (int i=0; i<children.length; i++) {
				boolean success = deleteDir(new File(dir, children[i]));
				if (!success) {
					return false;
				}
			}
		}
		// 目录此时为空，可以删除
		return dir.delete();
	}
	
	/**
	 * 获取webRoot根目录
	 * 
	 * @return
	 */
	public static String getWebRootPath() {
		String path = FileUtils.class.getClassLoader().getResource("/").getPath();
		path = path.substring(0, path.indexOf("WEB-INF"));
		return path;
	}
	

	/**
	 * 上传文件，将文件写入指定虚拟路径目录下
	 * @param realFilePath
	 * @param in
	 */
	public static void copyFile(String realFilePath, InputStream in) {
		if(StringUtils.isNotBlank(realFilePath))	{
			//父级目录。首先判断父级目录是否存在，如不存在，则需要新建文件夹
			String parentFilePath = realFilePath.substring(0, realFilePath.lastIndexOf("/"));
			File tempFile = new File(parentFilePath);
			if (!tempFile.exists()) {
				tempFile.mkdirs();
			}
			File outFile = new File(realFilePath);
			FileOutputStream out = null;
			try {
				out = new FileOutputStream(outFile);
				byte[] buffer = new byte[FILE_UPLOAD_SINGLE_BYTE];
				int i = 0;
				while ((i = in.read(buffer)) != -1) {
					out.write(buffer, 0, i);
				}
				out.flush();// 将缓存中的数据写入文件
			} catch (Exception e) {
				e.printStackTrace();
			} finally {// 关闭输入输出流
				try {
					in.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
				try {
					out.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}
	
	/**
	 * Base64字符串保存成图片，格式为Jpg
	 * @param imgStr 图片的二进制字节码转成的Base64字符串
	 * @param imgFilePath 保存到的完整的图片路径
	 */
    public static void base64ToImage(String imgStr, String imgFilePath) {// 对字节数组字符串进行Base64解码并生成图片
        OutputStream out = null;
        BASE64Decoder decoder = new BASE64Decoder();
        try {
            if (StringUtils.isNotBlank(imgStr)) {
                // Base64解码
                byte[] bytes = decoder.decodeBuffer(imgStr);
                for (int i = 0; i < bytes.length; ++i) {
                    if (bytes[i] < 0) {// 调整异常数据
                        bytes[i] += 256;
                    }
                }
                // 生成jpeg图片
                out = new FileOutputStream(imgFilePath);
                out.write(bytes);
                out.flush();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return;
        } finally {
            try {
                if (null != out) {
                    out.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

	public static String convertImageToBase64Str(String imageFileName) {
		ByteArrayOutputStream baos = null;
		BASE64Encoder encoder = new BASE64Encoder();
		try {
			//获取图片类型
			String suffix = imageFileName.substring(imageFileName.lastIndexOf(".") + 1);
			//构建文件
			File imageFile = new File(imageFileName);
			//通过ImageIO把文件读取成BufferedImage对象
			BufferedImage bufferedImage = ImageIO.read(imageFile);
			//构建字节数组输出流
			baos = new ByteArrayOutputStream();
			//写入流
			ImageIO.write(bufferedImage, suffix, baos);
			//通过字节数组流获取字节数组
			byte[] bytes = baos.toByteArray();
			//获取JDK8里的编码器Base64.Encoder转为base64字符
			return encoder.encode(bytes);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (baos != null) {
					baos.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return null;
	}
 /*********************** 附件上传 操作 ****************************/
    
    /**
     * 附件上传
     * @param filePath XXX/XX/XX/+文件名称
     * @param fileItem 文件流
     */
    public static void uploadFile(String filePath, FileItem fileItem) throws Exception{
    	String type = PropertyUtils.getValue("upload.type");
    	if (StringUtils.isBlank(type)) {
			return ;
		}

		if("3".equals(type)){//COS方式上传
			CosUtil.uploadFile(filePath,fileItem);
		}else if ("2".equals(type)) { // OSS 方式上传
			OssUtil.uploadFile(filePath, fileItem);
		} else if ("1".equals(type)){ // 本地存储
			copyFile(JsfUtil.getAbsolutePath() + "/" +filePath, fileItem.getInputStream());
		}
    }
    
    
    /**
     * 附件上传
     * @param filePath XXX/XX/XX/+文件名称
     * @param bucket OSS 存储容器
     * @param fileItem 文件流
     */
    public static void uploadFile(String filePath, InputStream in, String bucket) throws Exception{
    	String type = PropertyUtils.getValue("upload.type");
    	if (StringUtils.isBlank(type)) {
			return ;
		}

		if("3".equals(type)){//COS方式上传
			CosUtil.uploadFile(bucket,filePath,in);
		}else if ("2".equals(type)) { // OSS 方式上传
    		bucket = StringUtils.isBlank(bucket) ? "jswj-input" : bucket;
			OssUtil.uploadFile(filePath, bucket, in);
		} else if ("1".equals(type)){ // 本地存储
			copyFile(JsfUtil.getAbsolutePath() + "/" +filePath, in);
		}
    }
    
    /**
     * 预览附件
     * @param filePath XXX/XX/XX/+文件名称
     * @param bucket OSS 存储容器 默认jswj-input
     * @return 文件路径
     * @throws Exception
     */
    public static String previewFile(String filePath, String bucket){
    	String type = PropertyUtils.getValue("upload.type");
    	if (StringUtils.isBlank(type)) {
			return null;
		}

		if("3".equals(type)){//COS方式上传
			return CosUtil.getUrl(bucket,filePath);
		}else if ("2".equals(type)) { // OSS 方式上传
    		bucket = StringUtils.isBlank(bucket) ? "jswj-input" : bucket;
    		boolean exists = OssUtil.checkKeyExists(filePath, bucket);
    		if(!exists) {
    			return null;
    		}
    		return OssUtil.getUrl(bucket, filePath);
    	} else if ("1".equals(type)){ // 本地存储
			return "/webFile/"+filePath;
		}
    	
		return filePath;
    }
    
    /**
     * 删除文件
     * @param filePath
     * @param bucket
     */
    public static void delFile(String filePath, String bucket) {
    	String type = PropertyUtils.getValue("upload.type");
    	if (StringUtils.isBlank(type)) {
			return ;
		}

		if("3".equals(type)){//COS方式
			CosUtil.deleteFile(bucket,filePath);
		}else if ("2".equals(type)) { // OSS 方式上传
    		bucket = StringUtils.isBlank(bucket) ? "jswj-input" : bucket;
    		OssUtil.delObject(bucket, filePath);
    	} else if ("1".equals(type)){ // 本地存储
    		String realPath = JsfUtil.getAbsolutePath() + "/" + filePath;
    		File file = new File(realPath);
    		if(!file.exists() || !file.isFile()) {
    			return ;
    		}
    		
    		file.delete();
		}
    }
    
    /**
     * 获取文件流
     * @param filePath XXX/XX/XX/+文件名称
     * @param bucket OSS 存储容器 默认jswj-input
     * @return 文件流
     * @throws Exception
     */
    public static InputStream downLoadFile(String filePath, String bucket) throws Exception{
    	String type = PropertyUtils.getValue("upload.type");
    	if (StringUtils.isBlank(type)) {
			return null;
		}

		if("3".equals(type)){//COS方式
			COSObject cosObject = CosUtil.downloadFile(bucket, filePath);
			if(cosObject!=null){
				return cosObject.getObjectContent();
			}
			return null;
		}else if ("2".equals(type)) { // OSS 方式上传
    		bucket = StringUtils.isBlank(bucket) ? "jswj-input" : bucket;
    		OSSObject ossObject = OssUtil.dowloadFile(filePath, bucket);
    		if(ossObject == null) {
    			return null;
    		}
    		return ossObject.getObjectContent();
    	} else if ("1".equals(type)){ // 本地存储
    		File file = new File(JsfUtil.getAbsolutePath() + "/" +filePath);
    		if(!file.exists() || !file.isFile()) {
    			return null;
    		}
			return new FileInputStream(file);
		}
    	return null;
    }
    
    /** 压缩图片 */
    public static String compressImg(String filePath, String bucket, int width, int height, String color) {
    	String type = PropertyUtils.getValue("upload.type");
    	if (StringUtils.isBlank(type)) {
			return null;
		}

		if("3".equals(type)){//COS方式
			return CosUtil.getCompressImgUrl(bucket, filePath, "pad", width, height, null, color);
		}else if ("2".equals(type)) { // OSS 方式上传
    		bucket = StringUtils.isBlank(bucket) ? "jswj-input" : bucket;
    		return OssUtil.getCompressImgUrl(bucket, filePath, "pad", width, height, null, color);
    	} else if ("1".equals(type)){ // 本地存储
    		String url = "/webFile" +  ImageUtil.resizeImgNew(filePath, height, width, "#"+color);
			return url;
		}
    	return null;
    	
    }

	/**
	 * <p>方法描述：下载http文件</p>
	 *
	 * @MethodAuthor mxp, 2018-8-20,httpDownload
	 */
	public static boolean httpDownload(String httpUrl,String outFilePath){
		// 下载网络文件
		URL url;
		try {
			url = new URL(httpUrl);
		} catch (MalformedURLException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
			return false;
		}
		//父级目录。首先判断父级目录是否存在，如不存在，则需要新建文件夹
		String parentFilePath = outFilePath.substring(0, outFilePath.lastIndexOf("/"));
		File tempFile = new File(parentFilePath);
		if (!tempFile.exists()) {
			tempFile.mkdirs();
		}
		InputStream in = null;
		FileOutputStream out = null;
		try {
			URLConnection conn = url.openConnection();
			in = conn.getInputStream();
			out = new FileOutputStream(outFilePath);
			byte[] buffer = new byte[FILE_UPLOAD_SINGLE_BYTE];
			int i = 0;
			while ((i = in.read(buffer)) != -1) {
				out.write(buffer, 0, i);
			}
			out.flush();// 将缓存中的数据写入文件
			return true;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {// 关闭输入输出流
			try {
				if(in!=null){
					in.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
			try {
				if (out != null) {
					out.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

		return false;
	}
	/**
	 * <p>方法描述：校验文件上传的格式是否正确： index为1：只传图片，index为2：只传pdf，index为3：只传pdf和图片</p>
	 *
	 * @MethodAuthor rcj, 2019-10-18,allowPdfOrImageMethod
	 */
	public static boolean  allowPdfOrImageMethod(String contentType,String fileName,String index){
		contentType = contentType.toLowerCase();
		fileName =fileName.toLowerCase();
		Integer dotIndex = fileName.lastIndexOf(".");
		if(null !=dotIndex && "1".equals(index)){
			//只传图片
			if(contentType.startsWith("image")){
				String endName = fileName.substring(dotIndex+1);
				if(null != endName && (endName.equals("gif")|| endName.equals("jpeg")|| endName.equals("jpg")|| endName.equals("png") )){
					return true;
				}
			}
		}else if(null !=dotIndex && "2".equals(index)){
			//只传pdf	
			if(contentType.startsWith("application/pdf")){
				String endName = fileName.substring(dotIndex+1);
				if(null != endName && endName.equals("pdf")){
					return true;
				}
			}
		}else if(null !=dotIndex && "3".equals(index)){
			//只传pdf和图片	
			if(contentType.startsWith("image")||contentType.startsWith("application/pdf")){
				String endName = fileName.substring(dotIndex+1);
				if(null != endName && (endName.equals("gif")|| endName.equals("jpeg")|| endName.equals("jpg")|| endName.equals("png") || endName.equals("pdf") )){
					return true;
				}
			}
		}else if(null !=dotIndex && "4".equals(index)){
			//只传pdf和图片和zip
			if(contentType.startsWith("image")||contentType.startsWith("application/pdf")||contentType.startsWith("application/zip")||contentType.startsWith("application/x-zip-compressed")||contentType.startsWith("application/x-compressed")){
				String endName = fileName.substring(dotIndex+1);
				if(null != endName && (endName.equals("gif")|| endName.equals("jpeg")|| endName.equals("jpg")|| endName.equals("png") || endName.equals("pdf")|| endName.equals("zip") )){
					return true;
				}
			}
		}else if(null !=dotIndex && "5".equals(index)){
			//只能上传excel表格
			if(contentType.startsWith("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") || contentType.startsWith("application/vnd.ms-excel")){
				String endName = fileName.substring(dotIndex+1);
				if(null != endName && (endName.equals("xls")|| endName.equals("xlsx"))){
					return true;
				}
			}
		}else if(null !=dotIndex && "6".equals(index)){
			//只传pdf和图片和zip、dcm
			if(contentType.startsWith("image")||contentType.startsWith("application/pdf")||contentType.startsWith("application/zip")||contentType.startsWith("application/x-zip-compressed")||contentType.startsWith("application/x-compressed") || contentType.startsWith("application/octet-stream")){
				String endName = fileName.substring(dotIndex+1);
				if(null != endName && (endName.equals("gif")|| endName.equals("jpeg")|| endName.equals("jpg")|| endName.equals("png") || endName.equals("pdf")|| endName.equals("zip") || endName.equals("dcm") )){
					return true;
				}
			}
		}else if(null !=dotIndex && "7".equals(index)){
			//只传pdf和图片和zip、rar
			//rar  application/octet-stream
			if(contentType.startsWith("image")||contentType.startsWith("application/pdf")||contentType.startsWith("application/zip")||contentType.startsWith("application/x-zip-compressed")||contentType.startsWith("application/x-compressed") || contentType.startsWith("application/octet-stream")){
				String endName = fileName.substring(dotIndex+1);
				if(null != endName && (endName.equals("gif")|| endName.equals("jpeg")|| endName.equals("jpg")|| endName.equals("png") || endName.equals("pdf")|| endName.equals("zip") || endName.equals("rar") )){
					return true;
				}
			}
		}else if("8".equals(index)){
			//只传zip、rar、dcm
			if(contentType.startsWith("application/zip")||contentType.startsWith("application/x-zip-compressed")||contentType.startsWith("application/x-compressed") || contentType.startsWith("application/octet-stream")){
				String endName = fileName.substring(dotIndex+1);
				if(endName.equals("zip") || endName.equals("rar")|| endName.equals("dcm")){
					return true;
				}
			}
		}else if("9".equals(index)){
			//支持word、pdf、图片格式
			boolean isFlag = contentType.startsWith("image") || contentType.startsWith("application/pdf") || contentType.startsWith("application/vnd.openxmlformats-officedocument.wordprocessingml.document") || contentType.startsWith("application/msword");
			if(isFlag){
				String endName = fileName.substring(dotIndex+1);
				isFlag = null != endName && (endName.equals("gif")|| endName.equals("jpeg")|| endName.equals("jpg")|| endName.equals("png") || endName.equals("pdf") || endName.equals("docx") || endName.equals("doc"));
				if(isFlag){
					return true;
				}
			}
		}else if("10".equals(index)){
			//支持word、pdf
			boolean isFlag =  contentType.startsWith("application/pdf") || contentType.startsWith("application/vnd.openxmlformats-officedocument.wordprocessingml.document") || contentType.startsWith("application/msword");
			if(isFlag){
				String endName = fileName.substring(dotIndex+1);
				isFlag = null != endName && (endName.equals("pdf") || endName.equals("docx") || endName.equals("doc"));
				if(isFlag){
					return true;
				}
			}
		}else if("11".equals(index)){
			//只传zip、rar
			if(contentType.startsWith("application/zip")||contentType.startsWith("application/x-zip-compressed")||contentType.startsWith("application/x-compressed") || contentType.startsWith("application/octet-stream")){
				String endName = fileName.substring(dotIndex+1);
				if(endName.equals("zip") || endName.equals("rar")){
					return true;
				}
			}
		}
		return false;
	} 
	/**
 	 * <p>方法描述：验证上传文件格式，返回
 	 * index为1：只传图片，index为2：只传pdf，index为3：只传pdf和图片
 	 * </p>
 	 * @MethodAuthor qrr,2019年11月1日,veryFormat
	 * */
	public static String veryFormat(String contentType,String fileName,String index) {
		String errorMsg = null;
		if ("1".equals(index)) {
			if (!FileUtils.allowPdfOrImageMethod(contentType, fileName, index)) {
				errorMsg = "仅支持GIF、JPEG、JPG、PNG的图片格式！";
			}
		}else if ("2".equals(index)) {
			if (!FileUtils.allowPdfOrImageMethod(contentType, fileName, index)) {
            	errorMsg = "仅支持PDF的文件格式！";
			}
		}else if ("3".equals(index)) {
			if (!FileUtils.allowPdfOrImageMethod(contentType, fileName, index)) {
            	errorMsg = "仅支持GIF、JPEG、JPG、PNG、PDF的文件格式！";
			}
		}else if ("4".equals(index)) {
			if (!FileUtils.allowPdfOrImageMethod(contentType, fileName, index)) {
            	errorMsg = "仅支持GIF、JPEG、JPG、PNG、PDF、ZIP的文件格式！";
			}
		}else if("5".equals(index)){
			if (!FileUtils.allowPdfOrImageMethod(contentType, fileName, index)) {
				errorMsg = "仅支持xls、xlsx的文件格式！";
			}
		}else if("6".equals(index)){
			if (!FileUtils.allowPdfOrImageMethod(contentType, fileName, index)) {
				errorMsg = "仅支持GIF、JPEG、JPG、PNG、PDF、ZIP、DCM的文件格式！";
			}
		}else if("7".equals(index)){
			if (!FileUtils.allowPdfOrImageMethod(contentType, fileName, index)) {
				errorMsg = "仅支持GIF、JPEG、JPG、PNG、PDF、ZIP、RAR的文件格式！";
			}
		}else if("8".equals(index)){
			if (!FileUtils.allowPdfOrImageMethod(contentType, fileName, index)) {
				errorMsg = "仅支持RAR、ZIP、DCM的文件格式！";
			}
		}else if("9".equals(index)){
			//支持word、pdf、图片格式
			if (!FileUtils.allowPdfOrImageMethod(contentType, fileName, index)) {
				errorMsg = "仅支持GIF、JPEG、JPG、PNG、PDF、DOCX、DOC的文件格式！";
			}
		}else if("10".equals(index)){
			//支持word、pdf
			if (!FileUtils.allowPdfOrImageMethod(contentType, fileName, index)) {
				errorMsg = "仅支持PDF、DOCX、DOC的文件格式！";
			}
		}else if("11".equals(index)){
			//支持RAR、ZIP
			if (!FileUtils.allowPdfOrImageMethod(contentType, fileName, index)) {
				errorMsg = "仅支持RAR、ZIP的文件格式！";
			}
		}
		
		return errorMsg;
	}
	/**
	 *  <p>方法描述：获取虚拟路径某个文件夹下的文件名称</p>
	 * @MethodAuthor hsj
	 */
	public static String getFileName(String jigsawUrl) {
		StringBuffer str = new StringBuffer();
		String path = jigsawUrl; // 路径
		File f = new File(path);
		if (!f.exists()) {
			return null;
		}

		File fa[] = f.listFiles();
		for (int i = 0; i < fa.length; i++) {
			File fs = fa[i];
			if (fs.isDirectory()) {
				System.out.println(fs.getName() + " [目录]");
			} else {
				str.append(",").append(fs.getName());
			}
		}
		if(null != str){
			return  str.toString().substring(1);
		}
		return null;
	}

	public static String getResourceBasePath(String url) {
	// 获取跟目录
		File path = null;
		try {
			// 获取项目根路径
//			path = new File(ResourceUtils.getURL("classpath:").getPath());
			path = new ClassPathResource("").getFile();
		} catch (Exception e) {
			e.printStackTrace();
		}
		if (path == null || !path.exists()) {
			path = new File("");
		}
		String pathStr = path.getAbsolutePath();
    // 如果是在eclipse中运行，则和target同级目录,如果是jar部署到服务器，则默认和jar包同级
		pathStr = pathStr.replace(File.separator+"WEB-INF"+File.separator+"classes", "");
		return getFileName(pathStr+url);
	}

	public static void main(String[] args) {

		getResourceBasePath("/resources/images/jigsaw");
	}

	/**
	 * <p>方法描述：ZipFilePo方式CRC32压缩外部调用方法 </p>
	 * 示例：
	 *    public static void zipFilePoDownload(){
	 *         List<ZipFilePo> zipFilePoList = new ArrayList<>();
	 *         zipFilePoList.add(generate("新test","D:/u01/测试"));
	 *         zipFilePoList.add(generate("新aaa","D:/u01/测试 - 副本 (2)"));
	 *         zipFilePoList.add(generate("新bbb","D:/u01/测试 - 副本 (4)"));
	 *         zipFilePoList.add(generate("新ccc","D:/u01/测试 - 副本 (5)"));
	 *         ZipFilePo filePo = new ZipFilePo("原始1","D:/mnt");
	 *         filePo.setIfChangeChildName(false);
	 *         zipFilePoList.add(filePo);
	 *         filePo = new ZipFilePo("原始2","D:/u01/测试 - 副本 (6) - 副本 - 副本");
	 *         filePo.setIfChangeChildName(false);
	 *         zipFilePoList.add(filePo);
	 *
	 *         zipFileCRC32ByPo(zipFilePoList, "D:/zipFiletest.zip");
	 *     }
	 *
	 *     public static ZipFilePo generate(String name, String pre){
	 *         ZipFilePo filePo = new ZipFilePo();
	 *         filePo.setName(name);
	 *         filePo.setChildList(new ArrayList<>());
	 *         filePo.getChildList().add(new ZipFilePo(filePo.getName()+"/java 程序性能优化-.让你的Java程序更快、更稳定.pdf",pre+"/java 程序性能优化-.让你的Java程序更快、更稳定.pdf"));
	 *         filePo.getChildList().add(new ZipFilePo(filePo.getName()+"/koala.jpeg",pre+"/koala.jpeg"));
	 *         filePo.getChildList().add(new ZipFilePo(filePo.getName()+"/<EMAIL>",pre+"/<EMAIL>"));
	 *         filePo.getChildList().add(new ZipFilePo(filePo.getName()+"/poi-tl-master.zip",pre+"/poi-tl-master.zip"));
	 *         filePo.getChildList().add(new ZipFilePo(filePo.getName()+"/Redis深度历险：核心原理和应用实践.pdf",pre+"/Redis深度历险：核心原理和应用实践.pdf"));
	 *         filePo.getChildList().add(new ZipFilePo(filePo.getName()+"/smsService.jar",pre+"/smsService.jar"));
	 *         return filePo;
	 *     }
	 * 注意：
	 *  参数zipFilePath对应的目录，在调用这个方法前就需要存在，否则new FileOutputStream会出现拒绝访问的异常
	 *  方法里使用zipFile.mkdirs()也可以创建目录，但创建后还是会new FileOutputStream会出现拒绝访问的异常
	 * @MethodAuthor： pw 2023/7/20
	 **/
	public static void zipFileCRC32ByPo(List<ZipFilePo> zipFilePoList, String zipFilePath){
		if(CollectionUtils.isEmpty(zipFilePoList)){
			return;
		}
		File zipFile = new File(zipFilePath);
		try (java.util.zip.ZipOutputStream zipOut = new java.util.zip.ZipOutputStream(new FileOutputStream(zipFile));
			 BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(zipOut))
		{
			long beginTime = System.currentTimeMillis();
			for(ZipFilePo zipFilePo : zipFilePoList){
				allotFillZipEntity(zipFilePo, zipOut, bufferedOutputStream);
			}
			log.debug("CRC32压缩用时"+(System.currentTimeMillis()- beginTime)+"毫秒");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * <p>方法描述： 普通方式CRC32压缩外部调用方法 </p>
	 * @MethodAuthor： pw 2023/7/20
	 **/
	public static void zipFileCRC32(String dirPath, String generateZipPath) {
		File zipFile = new File(generateZipPath);
		File fileModel = new File(dirPath);
		if(!fileModel.exists()){
			return;
		}
		File[] fileConent = fileModel.listFiles();
		try (java.util.zip.ZipOutputStream zipOut = new java.util.zip.ZipOutputStream(new FileOutputStream(zipFile));
			 BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(zipOut))
		{
			//开始时间
			long beginTime = System.currentTimeMillis();
			for (File fc : fileConent) {
				if(fc.isDirectory()){
					zipDirectoryToZipFile(fc, zipOut, bufferedOutputStream, fc.getName()+"/");
				}else if(fc.isFile()){
					zipFilesToZipFile(fc, zipOut, bufferedOutputStream, null);
				}
			}
			log.debug("普通CRC32压缩用时"+(System.currentTimeMillis()- beginTime)+"毫秒");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * <p>方法描述：CRC32压缩  获取CRC32值</p>
	 * @MethodAuthor： pw 2023/7/20
	 **/
	private static long getFileCRCCode(File file) throws Exception {
		BufferedInputStream bufferedInputStream = new BufferedInputStream(new FileInputStream(file));
		CRC32 crc32 = new CRC32();
		//CheckedInputStream一种输入流，它还维护正在读取的数据的校验和。 然后可以使用校验和来验证输入数据的完整性。
		CheckedInputStream checkedinputstream = new CheckedInputStream(bufferedInputStream, crc32);
		while (checkedinputstream.read() != -1) {}
		checkedinputstream.close();
		bufferedInputStream.close();
		return crc32.getValue();
	}

	/**
	 * <p>方法描述：CRC32压缩 文件压缩到压缩包里</p>
	 * @MethodAuthor： pw 2023/7/20
	 **/
	private static void zipFilesToZipFile(File fc, java.util.zip.ZipOutputStream zipOut,
										 BufferedOutputStream bufferedOutputStream, String fatherPath) throws Exception{
		try (BufferedInputStream bufferedInputStream = new BufferedInputStream(new FileInputStream(fc))) {
			log.debug("CRC32压缩文件："+fatherPath+fc.getName());
			java.util.zip.ZipEntry entry=new java.util.zip.ZipEntry(fatherPath+fc.getName());
			//核心，和复制粘贴效果一样，并没有压缩，但速度很快
			entry.setMethod(java.util.zip.ZipEntry.STORED);
			entry.setSize(fc.length());
			entry.setCrc(getFileCRCCode(fc));
			zipOut.putNextEntry(entry);
			int len = 0;
			byte[] data = new byte[8192];
			while ((len = bufferedInputStream.read(data)) != -1) {
				bufferedOutputStream.write(data, 0, len);
			}
			bufferedInputStream.close();
			bufferedOutputStream.flush();
		}
	}

	/**
	 * <p>方法描述：CRC32压缩  文件夹压缩到压缩包</p>
	 * @MethodAuthor： pw 2023/7/20
	 **/
	private static void zipDirectoryToZipFile(File fileDir, java.util.zip.ZipOutputStream zouts,
											 BufferedOutputStream bufferedOutputStream,
											 String fatherPath) throws Exception{
		if (!fileDir.isDirectory()) {
			return;
		}
		File[] files = fileDir.listFiles();
		// 空的文件夹
		if (files.length == 0) {
			log.debug("CRC32压缩文件夹："+fatherPath+fileDir.getName());
			// 目录信息
			java.util.zip.ZipEntry entry = new java.util.zip.ZipEntry(fatherPath+fileDir.getName());
			try {
				zouts.putNextEntry(entry);
				zouts.closeEntry();
			} catch (Exception e) {
				e.printStackTrace();
			}
			return;
		}

		for (int i = 0; i < files.length; i++) {
			if (files[i].isFile()) {
				// 如果是文件，则调用文件压缩方法
				zipFilesToZipFile( files[i], zouts,bufferedOutputStream,fatherPath);
			} else {
				log.debug("CRC32压缩文件夹："+files[i].getName());
				String prePath = StringUtils.isBlank(fatherPath) ? fatherPath :
						(fatherPath.endsWith("/") || fatherPath.endsWith(File.separator) ? fatherPath : fatherPath+"/");
				// 如果是目录，则递归调用
				zipDirectoryToZipFile( files[i], zouts,bufferedOutputStream,prePath+files[i].getName()+"/");
			}
		}
	}

	/**
	 * <p>方法描述：CRC32压缩 ZipFilePo文件压缩到压缩包 </p>
	 * @MethodAuthor： pw 2023/7/20
	 **/
	private static void zipFilesToZipFileByZipFilePo(java.util.zip.ZipOutputStream zipOut,
													BufferedOutputStream bufferedOutputStream, ZipFilePo zipFilePo) throws Exception{
		File fc = new File(zipFilePo.getPath());
		if(!fc.exists() || !fc.isFile()){
			return;
		}
		try (BufferedInputStream bufferedInputStream = new BufferedInputStream(new FileInputStream(fc))) {
			log.debug("CRC32压缩文件："+zipFilePo.getName());
			java.util.zip.ZipEntry entry=new java.util.zip.ZipEntry(zipFilePo.getName());
			//核心，和复制粘贴效果一样，并没有压缩，但速度很快
			entry.setMethod(java.util.zip.ZipEntry.STORED);
			entry.setSize(fc.length());
			entry.setCrc(getFileCRCCode(fc));
			zipOut.putNextEntry(entry);
			int len = 0;
			byte[] data = new byte[8192];
			while ((len = bufferedInputStream.read(data)) != -1) {
				bufferedOutputStream.write(data, 0, len);
			}
			bufferedInputStream.close();
			bufferedOutputStream.flush();
		}
	}

	/**
	 * <p>方法描述：CRC32压缩 ZipFilePo文件夹压缩到压缩包</p>
	 * @MethodAuthor： pw 2023/7/20
	 **/
	private static void zipDirectoryToZipFileByZipFilePo(String name, java.util.zip.ZipOutputStream zipOut,
														BufferedOutputStream bufferedOutputStream, List<ZipFilePo> childList){
		if(null == name || name.length() == 0){
			return;
		}
		int size = null == childList ? 0 : childList.size();
		// 空的文件夹
		if (size == 0) {
			// 目录信息
			log.debug("CRC32压缩空文件夹"+name);
			java.util.zip.ZipEntry entry = new java.util.zip.ZipEntry(name);
			try {
				zipOut.putNextEntry(entry);
				zipOut.closeEntry();
			} catch (Exception e) {
				e.printStackTrace();
			}
			return;
		}

		for (int i = 0; i < size; i++) {
			allotFillZipEntity(childList.get(i), zipOut, bufferedOutputStream);
		}
	}

	/**
	 * <p>方法描述：CRC32压缩 分发压缩 </p>
	 * @MethodAuthor： pw 2023/7/20
	 **/
	private static void allotFillZipEntity(ZipFilePo zipFilePo, java.util.zip.ZipOutputStream zipOut,
										   BufferedOutputStream bufferedOutputStream){
		if(null == zipFilePo){
			return;
		}
		try{
			String name = zipFilePo.getName();
			if(StringUtils.isBlank(name)){
				return;
			}
			String filePath = zipFilePo.getPath();
			if(StringUtils.isBlank(filePath) && !zipFilePo.getIfChangeChildName()){
				return;
			}
			File curFile = null == filePath || filePath.length() == 0 ? null : new File(zipFilePo.getPath());
			if(!zipFilePo.getIfChangeChildName() && (null == curFile || !curFile.exists())){
				return;
			}
			if(null == curFile || curFile.isDirectory()){
				if(zipFilePo.getIfChangeChildName()){
					//读取childList
					zipDirectoryToZipFileByZipFilePo(zipFilePo.getName(), zipOut, bufferedOutputStream, zipFilePo.getChildList());
				}else{
					//fileList
					zipDirectoryToZipFile(curFile, zipOut, bufferedOutputStream, name+"/");
				}
			}else{
				zipFilesToZipFileByZipFilePo(zipOut, bufferedOutputStream, zipFilePo);
			}
		}catch(Exception e){
			e.printStackTrace();
		}
	}


	/**
	 *  <p>方法描述：附件上传验证</p>
	 * @MethodAuthor hsj 2024-06-12 14:23
	 */
	public static String veryFile(InputStream in, String contentType, String fileName, String index){
		String errorMsg = FileUtils.veryFormat(contentType, fileName, index);
		if(StringUtils.isNotBlank(errorMsg)){
			return errorMsg;
		}
		String suffix = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
		if (!StrUtil.equals(suffix, "pdf")) {
			return null;
		}
		try {
			boolean haveJavaScript = containsJavaScript(in);
			if (haveJavaScript) {
				return "文件上传异常！";
			}
		}catch (Exception e){
			e.printStackTrace();
			return "文件上传异常！";
		}
		return null;
	}
	/**
	 *  <p>方法描述：校验pdf文件是否包含js脚本</p>
	 * @MethodAuthor hsj 2024-06-12 17:07
	 */
	public static boolean containsJavaScript(InputStream in) throws IOException {
		PDDocument document = PDDocument.load(in);
		return containsJavaScript(document);
	}
	/**
	 *  <p>方法描述：校验pdf文件是否包含js脚本</p>
	 * @MethodAuthor hsj 2024-06-12 17:07
	 */
	public static boolean containsJavaScript(PDDocument document) {
		PDPageTree pages = document.getPages();
		COSDictionary catalog = document.getDocumentCatalog().getCOSObject();
		if (checkDictionaryForJavaScript(catalog,null)) {
			return Boolean.TRUE;
		}
		ExecutorService executorService = Executors.newFixedThreadPool(5);
		CompletionService completionService = new ExecutorCompletionService<>(executorService);
		for(PDPage page : pages){
			completionService.submit(new VerifyJavaScriptCallable(page.getCOSObject()));
		}
		for(PDPage page : pages){
			try {
				Future<Boolean> future = completionService.take();
				if (future.get()) {
					return Boolean.TRUE;
				}
			} catch (InterruptedException | ExecutionException e) {
				e.printStackTrace();
				Thread.currentThread().interrupt();
				throw new RuntimeException("文件上传异常", e);
			}
		}
		executorService.shutdownNow();
		return Boolean.FALSE;
	}

	private static boolean checkDictionaryForJavaScript(COSDictionary dict, Set<COSDictionary> visited) {
		if (visited == null) {
			visited = new HashSet<>();
		}
		if (visited.contains(dict)) {
			return false;
		}
		visited.add(dict);
		for (COSName key : dict.keySet()) {
			COSBase value = dict.getDictionaryObject(key);
			if (key.equals(COSName.JS) || key.equals(COSName.JAVA_SCRIPT)) {
				return true;
			} else if (value instanceof COSDictionary) {
				if (checkDictionaryForJavaScript((COSDictionary) value, visited)) {
					return true;
				}
			}
		}
		return false;
	}
}