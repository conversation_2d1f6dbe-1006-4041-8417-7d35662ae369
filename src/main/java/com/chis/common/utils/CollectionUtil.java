package com.chis.common.utils;

import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * zww 高效地比较两个集合中不同的元素
 * Created by Administrator on 14-5-15.
 */
public class CollectionUtil {

    private CollectionUtil() {

    }

    /**
     * 获取两个集合的不同元素
     *
     * @param collmax
     * @param collmin
     * @return
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public static Collection getDiffent(Collection collmax, Collection collmin) {
        //使用LinkeList防止差异过大时,元素拷贝
        Collection csReturn = new LinkedList();
        Collection max = collmax;
        Collection min = collmin;
        //先比较大小,这样会减少后续map的if判断次数
        if (collmax.size() < collmin.size()) {
            max = collmin;
            min = collmax;
        }
        //直接指定大小,防止再散列
        Map<Object, Integer> map = new HashMap<Object, Integer>(max.size());
        for (Object object : max) {
            map.put(object, 1);
        }
        for (Object object : min) {
            if (map.get(object) == null) {
                csReturn.add(object);
            } else {
                map.put(object, 2);
            }
        }
        for (Map.Entry<Object, Integer> entry : map.entrySet()) {
            if (entry.getValue() == 1) {
                csReturn.add(entry.getKey());
            }
        }
        return csReturn;
    }
    
    /**
     * 取两个集合中的相同的元素
     * @param collmax 
     * @param collmin
     * @return
     */
    public static Collection getSameMeta(Collection collmax, Collection collmin) {
        //使用LinkeList防止差异过大时,元素拷贝
        Collection csReturn = new LinkedList();
        Collection max = collmax;
        Collection min = collmin;
        //先比较大小,这样会减少后续map的if判断次数
        if (collmax.size() < collmin.size()) {
            max = collmin;
            min = collmax;
        }
        //直接指定大小,防止再散列
        Map<Object, Integer> map = new HashMap<Object, Integer>(max.size());
        for (Object object : max) {
            map.put(object, 1);
        }
        for (Object object : min) {
        	if (null != map.get(object)) {
        		csReturn.add(object);
        	}
        }
        return csReturn;
    }

    /**
     * 获取两个集合的不同元素,去除重复
     *
     * @param collmax
     * @param collmin
     * @return
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public static List getDiffentNoDuplicate(Collection collmax, Collection collmin) {
        return new ArrayList(getDiffent(collmax, collmin));
    }
    /**
 	 * <p>方法描述：集合按照序号排序</p>
 	 * @MethodAuthor qrr,2020年7月13日,collectionSort
	 * */
	public static <T> void collectionSortByNum(List<T> list) {
		if (!CollectionUtils.isEmpty(list)) {
			Collections.sort(list, new Comparator<T>(){
				@Override
				public int compare(T o1,T o2) {
					Object obj1 = Reflections.getFieldValue(o1, "num");
					Object obj2 = Reflections.getFieldValue(o2, "num");
					if (null!=obj1 && null!=obj2) {
						return Integer.valueOf(obj1.toString())-Integer.valueOf(obj2.toString());
					}
					return 0;
				}
				
			});
		}
	}

    /**
     * Objet类型转换List时类型校验,将错误的类型移除
     *
     * @param clazz  List元素的class类型
     * @param object List
     * @return 类型转换校验后的List, 非List对象以及空List返回new ArrayList<>()
     */
    public static <T> List<T> castList(Class<? extends T> clazz, Object object) {
        if (!(object instanceof List<?>)) {
            return new ArrayList<>();
        }
        List<?> rawList = (List<?>) object;
        if (CollectionUtils.isEmpty(rawList)) {
            return new ArrayList<>();
        }
        List<T> result = new ArrayList<>(rawList.size());
        for (Object o : rawList) {
            try {
                result.add(clazz.cast(o));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    public static boolean isArray(Object obj) {
        return null != obj && obj.getClass().isArray();
    }

    /**
     * 使用List集合contains实现List去重(保持原List顺序)
     *
     * @param list 原始List
     * @return 去重后的List(保持原List顺序)
     */
    public static List<String> removeDupByContains(List<String> list) {
        List<String> newList = new ArrayList<>();
        for (String s : list) {
            if (StringUtils.isBlank(s)) {
                continue;
            }
            boolean isContains = newList.contains(s);
            if (!isContains) {
                newList.add(s);
            }
        }
        list.clear();
        list.addAll(newList);
        return list;
    }
}
