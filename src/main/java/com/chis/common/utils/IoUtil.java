package com.chis.common.utils;

import java.io.*;
import java.nio.CharBuffer;

/**
 * IO相关工具封装
 *
 * <AUTHOR>
 * @version 1.0
 */
public class IoUtil {

    /**
     * 默认缓存大小 8192
     */
    public static final int DEFAULT_BUFFER_SIZE = 2 << 12;

    /**
     * 从Reader中读取String，读取完毕后关闭Reader
     *
     * @param reader Reader
     * @return String
     * @throws IOException IO异常
     */
    public static String read(Reader reader) throws IOException {
        return read(reader, true);
    }

    /**
     * 从{@link Reader}中读取String
     *
     * @param reader  {@link Reader}
     * @param isClose 是否关闭{@link Reader}
     * @return String
     * @throws IOException IO异常
     */
    public static String read(Reader reader, boolean isClose) throws IOException {
        final StringBuilder builder = new StringBuilder();
        final CharBuffer buffer = CharBuffer.allocate(DEFAULT_BUFFER_SIZE);
        try {
            while (-1 != reader.read(buffer)) {
                builder.append(buffer.flip());
            }
        } catch (IOException e) {
            throw new IOException(e);
        } finally {
            if (isClose) {
                close(reader);
            }
        }
        return builder.toString();
    }

    /**
     * 将流写到流中
     */
    public static void write(OutputStream out, InputStream in, boolean isCloseOut, boolean isCloseIn) {
        try {
            int bytes = 0;
            byte[] buffer = new byte[1024];
            while ((bytes = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytes);
            }
            out.flush();
        } catch (Exception e) {
        } finally {
            if (isCloseOut) {
                close(out);
            }
            if (isCloseIn) {
                close(in);
            }
        }
    }

    public static String getString(InputStream in, boolean isClose) {
        String read = "";
        try (ByteArrayOutputStream outStream = new ByteArrayOutputStream()) {
            byte[] data = new byte[1024];
            int count;
            while ((count = in.read(data, 0, 1024)) != -1) {
                outStream.write(data, 0, count);
            }
            read = outStream.toString("UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (isClose) {
                close(in);
            }
        }
        return read;
    }

    /**
     * 关闭<br>
     * 关闭失败不会抛出异常
     *
     * @param closeable 被关闭的对象
     */
    public static void close(AutoCloseable closeable) {
        if (null != closeable) {
            try {
                closeable.close();
            } catch (Exception e) {
                // 静默关闭
            }
        }
    }
}
