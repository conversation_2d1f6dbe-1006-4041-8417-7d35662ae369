/** Copyright (c) 2017, <EMAIL> All Rights Reserved.*/ 
package com.chis.common.utils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.converter.xml.MappingJackson2XmlHttpMessageConverter;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;

/**
 * @ClassAuthor sunm,2017年12月4日,RestTempUtil
 */
public class RestTempUtil {
	
	private static List<HttpMessageConverter<?>> messageConverters;
	
	private static RestTemplate restTemplate;
	
	private static SimpleClientHttpRequestFactory requestFactory;
	
	static {
		requestFactory = new SimpleClientHttpRequestFactory();
		requestFactory.setReadTimeout(5000);
		requestFactory.setConnectTimeout(5000);
		// 添加转换器
		messageConverters = new ArrayList<>();
		messageConverters.add(new StringHttpMessageConverter(Charset.forName("UTF-8")));
		messageConverters.add(new FormHttpMessageConverter());
		messageConverters.add(new MappingJackson2XmlHttpMessageConverter());
		messageConverters.add(new MappingJackson2HttpMessageConverter());
		messageConverters.add(new ByteArrayHttpMessageConverter());
		initTemplate();
	}
	
	private static void initTemplate() {
		if (restTemplate != null) {
			return ;
		}
		restTemplate = new RestTemplate(messageConverters);
		restTemplate.setRequestFactory(requestFactory);
		restTemplate.setErrorHandler(new DefaultResponseErrorHandler());
	}
	
	/**
	 * 执行请求操作
	 * @MethodAuthor sunm,2017年12月4日,doExceute
	 * @param httpMethod GET POST, 默认 POST
	 */
	public static String doExecute(String reqUrl, String tokenId, HttpMethod method, Map<String, Object> paramMap){
		String url = reqUrl + "?tokenId=" + tokenId;
		MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
		if(paramMap != null && !paramMap.isEmpty()) {
			for (String key : paramMap.keySet() ) {
				param.add(key, paramMap.get(key));
			}
		}
		return doHttp(method, param, url, null);
	}
	
	/**
	 * 执行请求操作，请求内存包含附件信息
	 * @MethodAuthor sunm,2017年12月4日,doFileExecute
	 */
	public static String doFileExecute(String reqUrl, String tokenId, FileInputStream file, Map<String, Object> paramMap) throws Exception {
		String url = reqUrl + "?tokenId=" + tokenId;
		HttpHeaders header = new HttpHeaders();
		String BOUNDARY = "----------" + System.currentTimeMillis();
		MediaType type = MediaType.parseMediaType("multipart/form-data; boundary=" + BOUNDARY);
		header.setContentType(type);
		MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
		param.add("file", toByte(file));
		if(paramMap != null && !paramMap.isEmpty()) {
			for (String key : paramMap.keySet() ) {
				param.add(key, paramMap.get(key));
			}
		}
		
		return doHttp(HttpMethod.POST, param, url, header);
	}
	
	/**
	 * 执行请求操作，请求内存包含附件信息
	 * @throws FileNotFoundException 
	 * @MethodAuthor sunm,2017年12月4日,doFileExecute
	 */
	public static String doFileExecute(String reqUrl, String tokenId, File file, Map<String, Object> paramMap) throws Exception {
		String url = reqUrl + "?tokenId=" + tokenId;
		HttpHeaders header = new HttpHeaders();
		String BOUNDARY = "----------" + System.currentTimeMillis();
		MediaType type = MediaType.parseMediaType("multipart/form-data; boundary=" + BOUNDARY);
		header.setContentType(type);
		MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
		param.add("file", toByte(new FileInputStream(file)));
		if(paramMap != null && !paramMap.isEmpty()) {
			for (String key : paramMap.keySet() ) {
				param.add(key, paramMap.get(key));
			}
		}
		return doHttp(HttpMethod.POST, param, url, header);
	}
	
	/**
	 * @MethodAuthor sunm,2017年12月4日,doHttp
	 */
	private static String doHttp(HttpMethod method, MultiValueMap<String, Object> param, String url, HttpHeaders header) {
		try {
			HttpEntity<MultiValueMap<String, Object>> httpEntity;
			if (header == null) {
				httpEntity = new HttpEntity<MultiValueMap<String, Object>>(param);
			} else {
				httpEntity = new HttpEntity<MultiValueMap<String, Object>>(param, header);
			}
			ResponseEntity<String> responseEntity = restTemplate.exchange(url, method, httpEntity, String.class);
			return responseEntity.getBody();
		} catch (Exception e) {
			e.printStackTrace();
		} 
		return null;
	}
	
	/** 
	 * 将文件流转换成字节数组
	 * @MethodAuthor sunm,2017年12月4日,toByte
	 */
	private static ByteArrayResource toByte(FileInputStream in) {
		try {
			ByteArrayOutputStream bos = new ByteArrayOutputStream();
			byte[] buffer = new byte[1024];
			while (in.read(buffer) > 0) {
				bos.write(buffer);
			}
			in.close();
			bos.close();
			ByteArrayResource arrayResource = new ByteArrayResource(bos.toByteArray());
			return arrayResource;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
}
