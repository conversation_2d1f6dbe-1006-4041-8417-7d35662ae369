package com.chis.common.utils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

/**
 * map工具类
 *
 * <AUTHOR>
 * @createDate 2014年9月12日上午9:59:03
 */
public class MapUtils {

    private MapUtils() {
    }

    /**
     * 根据value从map中找key
     * @param map map
     * @param value value
     * @return key
     */
    @SuppressWarnings("unchecked")
	public static String findKey(Map map, Object value) {
        if(null != map && map.size() > 0 && null != value) {
        	Set<Entry> set = map.entrySet();
        	for(Entry ent: set) {
        		if(ent.getValue().equals(value)) {
        			return (String) ent.getKey();
        		}
        	}
        }
        return null;
    }
    
    /**
     * 生产Dialog的条件Map，默认modal模式，可拖动，不可修改dialog的大小<br/>
     * @param width dialog的宽度
     * @param contentWidth dialog的内容宽度
     * @param height dialog的高度
     * @param contentHeight dialog的内容高度
     * @return dialog的条件map
     */
    public static Map<String, Object> produceDialogMap(Integer width, Integer contentWidth, Integer height, Integer contentHeight) {
    	Map<String, Object> options = new HashMap<String, Object>();
		options.put("modal", true);
		options.put("draggable", true);
		options.put("resizable", false);
    	if(null != width) {
    		options.put("width", width);
    	}
    	if(null != contentWidth) {
    		options.put("contentWidth", contentWidth);
    	}
    	if(null != height) {
    		options.put("height", height);
    	}
    	if(null != contentHeight) {
    		options.put("contentHeight", contentHeight);
    	}
		return options;
    }
    
    /**
     * 将map中的key对应的value设置到object中字段名为key的字段上
     * @param map key-object对象中的字段名 value-对应的值
     * @param object 普通对象
     */
    public static void initFields(Map<String, Object> map, Object object) {
    	if(null != map && map.size() > 0 && null != object) {
    		Set<String> keySet = map.keySet();
    		for(String key : keySet) {
    			Field field = Reflections.getField(key, object.getClass());
    			if(null != field) {
    				Reflections.setField(field, object, map.get(key));
    			}
    		}
    	}
    }
}
