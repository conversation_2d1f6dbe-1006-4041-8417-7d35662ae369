package com.chis.common.utils;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import org.apache.commons.fileupload.FileItem;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.PartETag;
import com.aliyun.oss.model.UploadPartRequest;
import com.aliyun.oss.model.UploadPartResult;

public class PartUploader implements Runnable {

	private static List<PartETag> partETags;

	private FileItem file;

	private long startPos;

	private long partSize;
	private int partNumber;

	private String bucketName;

	private String key;

	private String uploadId;

	private OSSClient ossClient;

	public PartUploader(FileItem file, long startPos, long partSize,
			int partNumber, String uploadId, String bucketName, String key,
			OSSClient ossClient, List<PartETag> partETags) {
		this.file = file;
		this.startPos = startPos;
		this.partSize = partSize;
		this.partNumber = partNumber;
		this.uploadId = uploadId;
		this.bucketName = bucketName;
		this.key = key;
		this.ossClient = ossClient;
		this.partETags = partETags;
	}

	@Override
	public void run() {
		InputStream input = null;
		try {
			input = file.getInputStream();
			input.skip(this.startPos);
			UploadPartRequest uploadPartRequest = new UploadPartRequest();
			uploadPartRequest.setBucketName(bucketName);
			uploadPartRequest.setKey(key);
			uploadPartRequest.setUploadId(this.uploadId);
			uploadPartRequest.setInputStream(input);
			uploadPartRequest.setPartSize(this.partSize);
			uploadPartRequest.setPartNumber(this.partNumber);

			UploadPartResult uploadPartResult = ossClient
					.uploadPart(uploadPartRequest);
			synchronized (partETags) {
				partETags.add(uploadPartResult.getPartETag());
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (input != null) {
				try {
					input.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

}
