package com.chis.common.utils;

import java.util.List;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.mts.model.v20140618.QueryPipelineListRequest;
import com.aliyuncs.mts.model.v20140618.QueryPipelineListResponse;
import com.aliyuncs.mts.model.v20140618.QueryPipelineListResponse.Pipeline;
import com.aliyuncs.mts.model.v20140618.UpdatePipelineRequest;
import com.aliyuncs.mts.model.v20140618.UpdatePipelineResponse;
import com.chis.common.bean.OSSPipeline;

/**
 * 管道
 * <AUTHOR>
 */
public class PipelineUtil {

	/**
	 * 更新管道
	 * @param client
	 * @param pipelineId 管道
	 * @return 0 管道不存在 1 修改成功  -1 修改失败
	 */
	public static Integer updatePipeline(DefaultAcsClient client, OSSPipeline pipeline) {
		// 判断管道ID是否存在
		Pipeline p = queryPipeline(client, pipeline.getPipelineId());
		if (p == null) {
			return 0;
		}
		try {
			UpdatePipelineResponse response = null;
			UpdatePipelineRequest request = new UpdatePipelineRequest();
			request.setPipelineId(pipeline.getPipelineId());
			request.setName(pipeline.getName());
			request.setState(pipeline.getState());
			request.setNotifyConfig(pipeline.getNotifyConfig());
			request.setRole(pipeline.getRole());
			response = client.getAcsResponse(request);
			System.out.println(response.toString());
			return 1;
		} catch (Exception e) {
			e.printStackTrace();
			return -1;
		}
	}
	
	/**
	 * 查询所有的管道 
	 * @return
	 */
	public static Pipeline queryPipeline(DefaultAcsClient client, String pipelineId) {
		try {
			QueryPipelineListResponse response = null;
			QueryPipelineListRequest request = new QueryPipelineListRequest();
			request.setPipelineIds(pipelineId);
			response = client.getAcsResponse(request);
	        List<Pipeline> data = response.getPipelineList();
	        if (data == null || data.size() == 0) {
	        	return null;
	        } else {
	        	return data.get(0);
	        }
		} catch (Exception e) {
			e.printStackTrace();
		}
        return null;
	}
}
