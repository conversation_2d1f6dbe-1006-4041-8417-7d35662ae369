package com.chis.common.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Description: JWT 解析token工具类
 *
 * @ClassAuthor pw,2021年09月14日,JwtUtils
 */
@Component
public class JwtUtils {

    public static String createToken() throws Exception{
        String key = "zdww-ggws-jwt-key";
        String uuid = "3821";
        String userId = "asdfgh";
        String timestramp = System.currentTimeMillis()+"";
        String ddd = "ssdddfff";
        return JWT.create().withAudience(uuid,userId,timestramp, ddd).sign(Algorithm.HMAC256(key));
    }

    public static void main1(String[] args) throws Exception{
        System.out.println(createToken());
        String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOlsiMTIzNDU2Nzg5IiwiYXNkZmdoIiwiMTYzMTUzMTE3MDk4MiJdfQ.gBAcq9SmUwgL2QLn_InZV8R4BD88lfCQyNC2uJKcn_0";
        token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOlsiMTIzNDU2Nzg5IiwiYXNkZmdoIiwiMTYzMTUzMTkyMzMzMSIsInNzZGRkZmZmIl19.jX7KJj_eQd3Sl_9_wEcDnOu2LyVZyeJzXTFUukOS5to";
        //token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJ7XCJwYXNzd29yZFwiOlwiMTIzNDU2QGFcIixcImFwcGlkXCI6XCJjZGNpY1wiLFwidXNlcm5hbWVcIjpcInRlc3RcIixcInRpbWVzdGFtcFwiOlwiMjAyMC0xMi0yNCAxNzoxMTo0NFwifSJ9.N_HAmuH2ulOfzIDnaSv0CYTr9qaxC1QpQiqlDwSyCsk";

        token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOlsiMzgyMSIsImFzZGZnaCIsIjE2MzE2MDUwMTgwMzciLCJzc2RkZGZmZiJdfQ.aXTJbGfdmWyoaaiWw129PJ5y-J_pokC-vCl2wZwLTx8";
        JWTVerifier verifier = JWT.require(Algorithm.HMAC256("zdww-ggws-jwt-key"))
                .build();
        DecodedJWT jwt = verifier.verify(token);
        Map<String, Claim> claims = jwt.getClaims();
        if(null != claims){
            String[] audArr = claims.get("aud").asArray(String.class);
        }
    }

    public static void main(String[] args) throws Exception{
        String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJ7XCJwYXNzd29yZFwiOlwiMTIzNDU2QGFcIixcImFwcGlkXCI6XCJjZGNpY1wiLFwidXNlcm5hbWVcIjpcInRlc3RcIixcInRpbWVzdGFtcFwiOlwiMjAyMC0xMi0yNCAxNzoxMTo0NFwifSJ9.N_HAmuH2ulOfzIDnaSv0CYTr9qaxC1QpQiqlDwSyCsk";
        System.out.println(verifyStringToken(token,"zdww-ggws-jwt-key"));
        token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOlsiMzgyMSIsImFzZGZnaCIsIjE2MzE2MDUwMTgwMzciLCJzc2RkZGZmZiJdfQ.aXTJbGfdmWyoaaiWw129PJ5y-J_pokC-vCl2wZwLTx8";
        System.out.println(verifyStringToken(token,"zdww-ggws-jwt-key"));
    }

    /**
     * @Description: 解析字符串
     *
     * @MethodAuthor pw,2021年09月14日
     */
    public static String verifyStringToken(String token, String jwtKey) throws Exception {
        if(StringUtils.isBlank(token) || StringUtils.isBlank(jwtKey)){
            return null;
        }
        JWTVerifier verifier = JWT.require(Algorithm.HMAC256(jwtKey))
                .build();
        DecodedJWT jwt = verifier.verify(token);
        List<String> list = jwt.getAudience();
        return null == list ? null : list.get(0);
    }
}
