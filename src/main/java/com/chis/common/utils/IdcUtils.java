package com.chis.common.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.chis.common.enumn.SexEnum;

/**
 * 模块说明：身份证校验工具类
 * 
 * <AUTHOR>
 * 
 * @createDate 2016年8月3日
 */
public class IdcUtils {

	/** 15位身份证年份前缀 */
	public final static String IDC15_YEAR_PREFIX = "19";

	/**
	 * 校验18位身份证校验位 调整成Hutool工具中的验证
	 * @param idCard
	 * @return 错误信息
	 */
	public static String checkIDC(String idCard) {
		if (StrUtil.isBlank(idCard)) {
			return null;
		}
		return IdcardUtil.isValidCard(idCard) ? null : "身份证输入错误！";
	}

	/**
	 * 将15位符合要求的身份证号转换成18位，否则返回空
	 * @param century
	 *            默认赋值19，因为20年以后就没有15位身份证了
	 * @param idCardNo15
	 *            待转换的 15 位身份证号码
	 * @return
	 */
	public static String from15to18(String idCardNo15) {
		String checkIDC = checkIDC(idCardNo15);
		if (StringUtils.isBlank(checkIDC) && idCardNo15.length() == 15) {
			int[] weight = new int[] { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1 };
			// 通过加入世纪码, 变成 17 为的新号码本体.
			String newNoBody = idCardNo15.substring(0, 6) + IDC15_YEAR_PREFIX + idCardNo15.substring(6);

			// 下面算最后一位校验码
			int checkSum = 0;
			for (int i = 0; i < 17; i++) {
				int ai = Integer.parseInt("" + newNoBody.charAt(i)); // 位于 i
																		// 位置的数值
				checkSum = checkSum + ai * weight[i];
			}

			int checkNum = checkSum % 11;
			String checkChar = null;

			switch (checkNum) {
			case 0:
				checkChar = "1";
				break;
			case 1:
				checkChar = "0";
				break;
			case 2:
				checkChar = "X";
				break;
			default:
				checkChar = "" + (12 - checkNum);
			}
			return newNoBody + checkChar;
		}
		return null;
	}

	/**
	 * 将18位符合要求的身份证号转换成15位，否则返回空
	 * @param idCardNo18
	 * @return
	 */
	public static String from18to15(String idCardNo18) {
		String checkIDC = checkIDC(idCardNo18);
		if (StringUtils.isBlank(checkIDC) && idCardNo18.length() == 18) {
			return idCardNo18.substring(0, 6) + idCardNo18.substring(8, 17);
		}
		return null;
	}
	
	/**
	 * 将15位身份证自动转换成18位，18位身份证无需转换
	 * @param idc
	 * @return
	 */
	public static String idcAutoConvertTo18(String idc)	{
		if(StringUtils.isNotBlank(idc))	{
			if(idc.length() == 15){
				return from15to18(idc);
			}else if(idc.length() == 18){
				return idc;
			}
		}
		return null;
	}

	//按十八位身份证校验 十五位的不再支持
	private static boolean isIdc(String idc) {
		//十五位 十八位验证 "(^\\d{6}\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\d|3[01])\\d{3}$|^\\d{6}(18|19|20)?\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|X)$)"
		return idcBasePattern("^\\d{6}(18|19|20)?\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|X)$", idc);
	}
	//户口簿
	public static boolean isHousehold(String idc){
		return idcBasePattern("(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X)$)", idc);
	}
	//护照
	public static boolean isPassPort(String idc){
		return idcBasePattern("^([A-Z]|[0-9]){5,17}$", idc);
	}
	//军官证
	public static boolean isMilitary(String idc){
		return idcBasePattern("^[\\u4E00-\\u9FA5](字第)([0-9A-Z]{4,8})(号?)$", idc);
	}
	//港澳通行证
	public static boolean isHmCard(String idc){
		return idcBasePattern("^[HM]{1}([0-9]{8})$", idc);
	}
	//台胞证
	public static boolean isTaiWanEntry(String idc){
		return idcBasePattern("(^\\d{8}$)|(^[a-zA-Z0-9]{10}$)|(^\\d{18}$)|(^[A-Z]\\d{6}\\([DBA]\\)$)|(^\\d{10}\\(B\\)$)", idc);
	}

	private static boolean idcBasePattern(String pattern, String idc){
		return StringUtils.pattern(pattern, idc);
	}

	// 获得校验位
	private static String getVerify(String eightcardid) {
		int[] wi = { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1 };
		int[] vi = { 1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2 };
		int[] ai = new int[18];
		int remaining = 0;

		if (eightcardid.length() == 18) {
			eightcardid = eightcardid.substring(0, 17);
		}

		if (eightcardid.length() == 17) {
			int sum = 0;
			for (int i = 0; i < 17; i++) {
				String k = eightcardid.substring(i, i + 1);
				ai[i] = Integer.parseInt(k);
			}
			for (int i = 0; i < 17; i++) {
				sum = sum + wi[i] * ai[i];
			}
			remaining = sum % 11;
		}
		return remaining == 2 ? "X" : String.valueOf(vi[remaining]);
	}
	
	/**
	 * 校验身份证是否是合法生日和地区
	 * 
	 * @param idc
	 * @return
	 * <AUTHOR>
	 */
	private static String getVerfyBirth(String idc) {
		String errorInfo = null;
		String strYear = idc.substring(6, 10);// 年份
		String strMonth = idc.substring(10, 12);// 月份
		String strDay = idc.substring(12, 14);// 月份
		if (isDate(strYear + "-" + strMonth + "-" + strDay) == false) {
			errorInfo = "身份证输入错误！";
			return errorInfo;
		}
		GregorianCalendar gc = new GregorianCalendar();
		SimpleDateFormat s = new SimpleDateFormat("yyyy-MM-dd");
		try {
			if (Integer.parseInt(strYear) > gc.get(Calendar.YEAR)
					|| Integer.parseInt(strYear) < 1900
					|| (gc.getTime().getTime() - s.parse(
							strYear + "-" + strMonth + "-" + strDay).getTime()) < 0) {
				errorInfo = "身份证输入错误！";
				return errorInfo;
			}
		} catch (NumberFormatException e) {
			e.printStackTrace();
		} catch (java.text.ParseException e) {
			e.printStackTrace();
		}
		return errorInfo;
	}

	/**
	 * 功能：判断字符串是否为日期格式
	 * 
	 * @param str
	 * @return
	 */
	public static boolean isDate(String strDate) {
		Pattern pattern = Pattern
				.compile("^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$");
		Matcher m = pattern.matcher(strDate);
		if (m.matches()) {
			return true;
		} else {
			return false;
		}
	}

	/**
 	 * <p>方法描述：根据身份证号获取性别，15位最后一位奇数为男，偶数为女；18位倒数第2位奇数为男，偶数为女</p>
 	 * @MethodAuthor qrr,2020年6月17日,getSex
	 * */
	public static Integer getSex(String idc) {
		if (StringUtils.isBlank(idc)) {
			return null;
		}
		String val = null;
		if (idc.length()==15) {
			val = idc.substring(
					idc.length() - 1,
					idc.length());
		}else {
			val = idc.substring(
					idc.length() - 2,
					idc.length() - 1);
		}
        if (Integer.valueOf(val) % 2 == 0) {
        	return SexEnum.FEMALE.getType();
        } else {
        	return SexEnum.MALE.getType();
        }// 性别
	}


	/**
	 * <p>方法描述：根据证件类型验证证件号</p>
	 * @MethodAuthor： yzz
	 * @Date：2022-08-04
	 **/
	public static String validateIdc(String tmpIdc, String codeNo){
		String checkIDC = null;
		boolean flag = false;
		if(StringUtils.isNotBlank(codeNo)){
			if("01".equals(codeNo)){
				// 验证身份证号合法性
				checkIDC = IdcUtils.checkIDC(tmpIdc);
				if(null != checkIDC){
					flag = true;
				}
			}else if("02".equals(codeNo) && !IdcUtils.isHousehold(tmpIdc)){
				// 验证户口簿
				flag = true;
			}else if("03".equals(codeNo) && !IdcUtils.isPassPort(tmpIdc)){
				// 验证护照
				flag = true;
			}else if("04".equals(codeNo) && !IdcUtils.isMilitary(tmpIdc)){
				// 验证军官证
				flag = true;
			}else if("06".equals(codeNo) && !IdcUtils.isHmCard(tmpIdc)){
				// 验证港澳通行证
				flag = true;
			}else if("07".equals(codeNo) && !IdcUtils.isTaiWanEntry(tmpIdc)){
				// 验证台胞证
				flag = true;
			}
		}
		if(flag){
			checkIDC = "证件号码格式错误！";
		}
		return checkIDC;
	}
	/**
	 * @Description : 出生日期的计算
	 * @MethodAuthor: anjing
	 * @Date : 2019/5/16 10:28
	 **/
	public static Date calBirthday(String idc){
		Date brithday = null;
		// 日期转换格式
		SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
		try {
			// 18位身份证号
			if (idc.length() == 18) {
				brithday = df.parse(idc.substring(6, 14));
			} else {
				// 15位身份证号前加上"19"
				brithday = df.parse(new StringBuilder("19").append(idc.substring(6, 12)).toString());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return brithday;
	}

	/**
	 * @Description : 年龄的计算
	 * @MethodAuthor: anjing
	 * @Date : 2019/5/22 15:16
	 **/
	public static Integer calAge(String idc) {
		String year = "";
		String date = "";
		int age = 0;
		if (idc.length() == 18) {
			year = idc.substring(6, 10);
			date = idc.substring(6, 14);
		} else {
			year = "19" + idc.substring(6, 8);
			date = "19" + idc.substring(6, 12);
		}
		try {
			Date today = new Date();
			int yearNow = DateUtils.getYear(today);
			Date birthDate = DateUtils.parseDate(date, "yyyyMMdd");
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(birthDate);
			calendar.set(Calendar.YEAR, DateUtils.getYearInt());
			long millis = calendar.getTimeInMillis();
			long todayMillis = DateUtils.getDateOnly(today).getTime();
			age = yearNow - Integer.valueOf(year);

			if (todayMillis - millis < 0) {
				age -= 1;
			}
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return age;
	}


	  /*********************************** 身份证验证结束 ****************************************/
   public static void main(String[] args) {
	   int[] vi = { 1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2 };
	   for (int i : vi) {
		   String idc = "32028120201310201"+String.valueOf(i);
		   System.out.println("末尾的值："+i+"——"+checkIDC(idc));
	   }
        
//       System.out.println(from18to15("320311197707060018"));
//    	System.out.println(checkIDC("002323199408072323"));
//    	System.out.println(checkIDC("112233445566778899"));
//    	System.out.println(checkIDC("232323232323232322"));
//    	System.out.println(checkIDC("112233445566778896"));
   }

}
