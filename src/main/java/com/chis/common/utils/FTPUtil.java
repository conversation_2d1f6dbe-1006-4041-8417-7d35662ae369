package com.chis.common.utils;


import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.net.SocketException;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;


public class FTPUtil {
	 /**
     * 连接 FTP 服务器
     *
     * @param addr     FTP 服务器 IP 地址
     * @param port     FTP 服务器端口号
     * @param username 登录用户名
     * @param password 登录密码
     * @return
     * @throws Exception
     */
    public static FTPClient connectFtpServer(String addr, int port, String username, String password, String controlEncoding) {
        FTPClient ftpClient = new FTPClient();
        /**设置文件传输的编码*/
		ftpClient.setControlEncoding(controlEncoding);
 
		/**连接 FTP 服务器
		 * 如果连接失败，则此时抛出异常，如ftp服务器服务关闭时，抛出异常：
		 * java.net.ConnectException: Connection refused: connect*/
		try {
			ftpClient.connect(addr, port);
		} catch (SocketException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (java.io.IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		/**登录 FTP 服务器
		 * 1）如果传入的账号为空，则使用匿名登录，此时账号使用 "Anonymous"，密码为空即可*/
		 try {
			ftpClient.login(username, password);
		} catch (java.io.IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
         
 
		/** 设置传输的文件类型
		 * BINARY_FILE_TYPE：二进制文件类型
		 * ASCII_FILE_TYPE：ASCII传输方式，这是默认的方式
		 * ....
		 */
		try {
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
		} catch (java.io.IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
 
		/**
		 * 确认应答状态码是否正确完成响应
		 * 凡是 2开头的 isPositiveCompletion 都会返回 true，因为它底层判断是：
		 * return (reply >= 200 && reply < 300);
		 */
		int reply = ftpClient.getReplyCode();
		if (!FTPReply.isPositiveCompletion(reply)) {
		    /**
		     * 如果 FTP 服务器响应错误 中断传输、断开连接
		     * abort：中断文件正在进行的文件传输，成功时返回 true,否则返回 false
		     * disconnect：断开与服务器的连接，并恢复默认参数值
		     */
		    try {
				ftpClient.abort();
				 ftpClient.disconnect();
			} catch (java.io.IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		   
		} else {
		}
        return ftpClient;
    }
 
    /**
     * 使用完毕，应该及时关闭连接
     * 终止 ftp 传输
     * 断开 ftp 连接
     *
     * @param ftpClient
     * @return
     */
    public static FTPClient closeFTPConnect(FTPClient ftpClient) {
        if (ftpClient != null && ftpClient.isConnected()) {
		    try {
				ftpClient.abort();
				ftpClient.disconnect();
			} catch (java.io.IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		    
		}
        return ftpClient;
    }
    
    
    public static void downloadSingleFile(FTPClient ftpClient, String absoluteLocalDirectory, String relativeRemotePath) {
        /**如果 FTP 连接已经关闭，或者连接无效，则直接返回*/
        if (!ftpClient.isConnected() || !ftpClient.isAvailable()) {
            System.out.println(">>>>>FTP服务器连接已经关闭或者连接无效*********");
            return;
        }
        if (StringUtils.isBlank(absoluteLocalDirectory) || StringUtils.isBlank(relativeRemotePath)) {
            System.out.println(">>>>>下载时遇到本地存储路径或者ftp服务器文件路径为空，放弃...*********");
            return;
        }
        try {
            /**没有对应路径时，FTPFile[] 大小为0，不会为null*/
            FTPFile[] ftpFiles = ftpClient.listFiles(relativeRemotePath);
            FTPFile ftpFile = null;
            if (ftpFiles.length >= 1) {
                ftpFile = ftpFiles[0];
            }
            if (ftpFile != null && ftpFile.isFile()) {
                /** ftpFile.getName():获取的是文件名称，如 123.mp4
                 * 必须保证文件存放的父目录必须存在，否则 retrieveFile 保存文件时报错
                 */
                File localFile = new File(absoluteLocalDirectory, relativeRemotePath);
                if (!localFile.getParentFile().exists()) {
                    localFile.getParentFile().mkdirs();
                }
                OutputStream outputStream;
				try {
						outputStream = new FileOutputStream(localFile);
						
		                String workDir = relativeRemotePath.substring(0, relativeRemotePath.lastIndexOf("/"));
		                if (StringUtils.isBlank(workDir)) {
		                    workDir = "/";
		                }
		                /**文件下载前，FTPClient工作目录必须切换到文件所在的目录，否则下载失败
		                 * "/" 表示用户根目录*/
		                ftpClient.changeWorkingDirectory(workDir);
		                /**下载指定的 FTP 文件 到本地
		                 * 1)注意只能是文件，不能直接下载整个目录
		                 * 2)如果文件本地已经存在，默认会重新下载
		                 * 3)下载文件之前，ftpClient 工作目录必须是下载文件所在的目录
		                 * 4)下载成功返回 true，失败返回 false
		                 */
		                ftpClient.retrieveFile(ftpFile.getName(), outputStream);
		 
		                outputStream.flush();
		                outputStream.close();
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
                System.out.println(">>>>>FTP服务器文件下载完毕*********" + ftpFile.getName());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public static void main(String[] args) throws Exception {
        System.out.println("-----------------------应用启动------------------------");
        FTPClient ftpClient = FTPUtil.connectFtpServer("10.88.88.55", 21, "010", "1", "gbk");
        downloadSingleFile(ftpClient, "E:\\gxg\\ftpDownload", "\\319035445.pdf");
        System.out.println("FTP 连接是否有效：" + ftpClient.isAvailable());
        closeFTPConnect(ftpClient);
        System.out.println("-----------------------应用关闭------------------------");
    }

}
