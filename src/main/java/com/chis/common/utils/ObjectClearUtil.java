package com.chis.common.utils;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

/**
 * <p>类描述：清空实体指定的字段</p>
 * @ClassAuthor qrr,2018年6月20日,ObjectClearUtil
 * */
public class ObjectClearUtil {
	/**
 	 * <p>方法描述：除了指定的属性值全部清空</p>
 	 * @MethodAuthor qrr,2018年6月20日,copyProperties
	 * */
	public static void clearObjectProperties(Object from, String[] includsArray) throws Exception {
		clearProperties(from, includsArray);
	}
	/**
	 * 对象属性值复制，仅复制指定名称的属性值
	 * 
	 * @param from
	 * @param to
	 * @param includsArray
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public static void clearProperties(Object from, String[] includsArray) throws Exception {
		List<String> includesList = null;
		if (includsArray != null && includsArray.length > 0) {
			includesList = Arrays.asList(includsArray); // 构造列表对象
		} else {
			return;
		}
		Method[] fromMethods = from.getClass().getMethods();
		Method fromMethod = null;
		String fromMethodName = null;
		for (int i = 0; i < fromMethods.length; i++) {
			fromMethod = fromMethods[i];
			fromMethodName = fromMethod.getName();
			if (!fromMethodName.startsWith("set"))
				continue;
			// 排除列表检测
			String str = fromMethodName.substring(3);
			if (!includesList.contains(str.substring(0, 1).toLowerCase() + str.substring(1))) {
				fromMethod.invoke(from, new Object[]{null});
			}
		}
	}
}
