package com.chis.common.utils;

import com.baidu.aip.ocr.AipOcr;
import org.json.JSONObject;

import java.util.HashMap;


/***
 * <p>类描述: 百度OCR图像识别工具类 </p>
 *
 * @ClassAuthor mxp , 2018/11/1 , BaiDuOCRUtil
 */
public class BaiDuOCRUtil {

    //设置APPID/AK/SK
    public static final String APP_ID = "14594887";
    public static final String API_KEY = "gsthEAGhWgmWNyfxZpoTa59c";
    public static final String SECRET_KEY = "tOzCMjbUep7l7mMhnMb9h4q60FMik1gF";

    private static AipOcr client;
    static {
        // 初始化一个AipOcr
        client = new AipOcr(APP_ID, API_KEY, SECRET_KEY);

        // 可选：设置网络连接参数
        client.setConnectionTimeoutInMillis(2000);
        client.setSocketTimeoutInMillis(60000);
    }

    public static void sample() {
        // 传入可选参数调用接口
        HashMap<String, String> options = new HashMap<String, String>();
        options.put("detect_direction", "true");
        options.put("detect_risk", "false");
        options.put("id_card_side", "front");

        String idCardSide = "back";

        // 参数为本地图片路径
        String image = "D:\\Download\\UCBrowser\\idcard1.png";
        JSONObject res = client.idcard(image, idCardSide, options);
        System.out.println(res.toString(2));

        // 参数为本地图片二进制数组
//        byte[] file = readImageFile(image);
//        res = client.idcard(file, idCardSide, options);
//        System.out.println(res.toString(2));

    }

    public static void main(String[] args) {
        BaiDuOCRUtil.sample();
    }


}
