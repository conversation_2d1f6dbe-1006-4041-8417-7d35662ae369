package com.chis.common.utils;

import java.math.BigDecimal;
import java.text.Collator;
import java.util.Locale;

/**
 * 比较工具类，比较数字、拼音、编号等
 *
 * <AUTHOR>
 * @version 1.0
 */
public class CompareUtil {

    /**
     * BigDecimal类型比较(空值排在后面)
     *
     * @param b1 比较BigDecimal1
     * @param b2 比较BigDecimal2
     * @return 比较结果
     */
    public static int compareBigDecimal(BigDecimal b1, BigDecimal b2) {
        return compareBigDecimal(b1, b2, false);
    }

    /**
     * BigDecimal类型比较
     *
     * @param b1            比较BigDecimal1
     * @param b2            比较BigDecimal2
     * @param isNullGreater 空值是否排在前面
     * @return 比较结果
     */
    public static int compareBigDecimal(BigDecimal b1, BigDecimal b2, boolean isNullGreater) {
        int res = compareNull(b1, b2, isNullGreater);
        if (res != 0) {
            return res;
        }
        return b1.compareTo(b2);
    }

    /**
     * Integer类型比较(空值排在后面)
     *
     * @param i1 比较Integer1
     * @param i2 比较Integer2
     * @return 比较结果
     */
    public static int compareInteger(Integer i1, Integer i2) {
        return compareInteger(i1, i2, false);
    }

    /**
     * Integer类型比较
     *
     * @param i1            比较Integer1
     * @param i2            比较Integer2
     * @param isNullGreater 空值是否排在前面
     * @return 比较结果
     */
    public static int compareInteger(Integer i1, Integer i2, boolean isNullGreater) {
        int res = compareNull(i1, i2, isNullGreater);
        if (res != 0) {
            return res;
        }
        return Integer.compare(i1, i2);
    }

    /**
     * 汉字按拼音比较(空值排在后面)
     *
     * @param s1 比较字符串1
     * @param s2 比较字符串2
     * @return 比较结果
     */
    public static int comparePinyin(String s1, String s2) {
        return comparePinyin(s1, s2, false);
    }

    /**
     * 汉字按拼音比较
     *
     * @param s1            比较字符串1
     * @param s2            比较字符串2
     * @param isNullGreater 空值是否排在前面
     * @return 比较结果
     */
    public static int comparePinyin(String s1, String s2, boolean isNullGreater) {
        int res = compareNull(s1, s2, isNullGreater);
        if (res != 0) {
            return res;
        }
        return Collator.getInstance(Locale.CHINESE).compare(s1, s2);
    }

    /**
     * 编号比较(空值排在后面)
     *
     * @param s1 比较字符串1
     * @param s2 比较字符串2
     * @return 比较结果
     */
    public static int compareNo(String s1, String s2) {
        return compareNo(s1, s2, false);
    }

    /**
     * 编号比较
     *
     * @param s1            比较字符串1
     * @param s2            比较字符串2
     * @param isNullGreater 空值是否排在前面
     * @return 比较结果
     */
    public static int compareNo(String s1, String s2, boolean isNullGreater) {
        int res = compareNull(s1, s2, isNullGreater);
        if (res != 0) {
            return res;
        }
        // 比较字符串中的每个字符
        char c1;
        char c2;
        // 逐字比较返回结果
        for (int i = 0; i < s1.length(); i++) {
            c1 = s1.charAt(i);
            try {
                c2 = s2.charAt(i);
            } catch (StringIndexOutOfBoundsException e) {
                // 如果在该字符前，两个串都一样，str2更短，则str1较大
                return 1;
            }
            // 如果都是数字的话，则需要考虑多位数的情况，取出完整的数字字符串，转化为数字再进行比较
            if (Character.isDigit(c1) && Character.isDigit(c2)) {
                StringBuilder numStr1 = new StringBuilder();
                StringBuilder numStr2 = new StringBuilder();
                // 获取数字部分字符串
                for (int j = i; j < s1.length(); j++) {
                    c1 = s1.charAt(j);
                    if (!Character.isDigit(c1) && c1 != '.') {
                        // 不是数字则直接退出循环
                        break;
                    }
                    numStr1.append(c1);
                }
                // 考虑可能带小数的情况
                for (int j = i; j < s2.length(); j++) {
                    c2 = s2.charAt(j);
                    if (!Character.isDigit(c2) && c2 != '.') {
                        break;
                    }
                    numStr2.append(c2);
                }
                // 转换成数字数组进行比较 适配 1.25.3.5 这种情况
                String[] numberArray1 = numberStrToNumberArray(numStr1.toString());
                String[] numberArray2 = numberStrToNumberArray(numStr2.toString());
                return compareNumberArray(numberArray1, numberArray2);
            }
            // 不是数字的比较方式
            if (c1 != c2) {
                return c1 - c2;
            }
        }
        return 0;
    }

    /**
     * 数字字符串转数字数组
     * 适配 1.25.3.5 这种情况 ，同时如果不不包含小数点【整数情况】
     *
     * @param numberStr 数字字符串
     * @return 数字数组
     */
    private static String[] numberStrToNumberArray(String numberStr) {
        String[] numberArray = numberStr.split("\\.");
        if (numberArray.length == 0) {
            numberArray = new String[]{numberStr};
        }
        return numberArray;

    }

    /**
     * 比较两个数字数组
     *
     * @param numberArray1 数字数组1
     * @param numberArray2 数字数组2
     * @return 比较结果
     */
    private static int compareNumberArray(String[] numberArray1, String[] numberArray2) {
        for (int i = 0; i < numberArray1.length; i++) {
            if (numberArray2.length < i + 1) {
                return 1;
            }
            int compareResult = Integer.valueOf(numberArray1[i]).compareTo(Integer.valueOf(numberArray2[i]));
            if (compareResult != 0) {
                return compareResult;
            }
        }
        return -1;
    }

    /**
     * 空值(null)比较(空值排在后面)，若存在空值返回结果，否则固定返回0
     *
     * @param o1 比较对象1
     * @param o2 比较对象2
     * @return 若存在空值返回比较结果，否则固定返回0
     */
    public static int compareNull(Object o1, Object o2) {
        return compareNull(o1, o2, false);
    }

    /**
     * 空值(null)比较，若存在空值返回结果，否则固定返回0
     *
     * @param o1            比较对象1
     * @param o2            比较对象2
     * @param isNullGreater 空值是否排在前面
     * @return 若存在空值返回比较结果，否则固定返回0
     */
    public static int compareNull(Object o1, Object o2, boolean isNullGreater) {
        if (ObjectUtil.isEmpty(o1)) {
            return isNullGreater ? 1 : -1;
        }
        if (ObjectUtil.isEmpty(o2)) {
            return isNullGreater ? -1 : 1;
        }
        return 0;
    }
}
