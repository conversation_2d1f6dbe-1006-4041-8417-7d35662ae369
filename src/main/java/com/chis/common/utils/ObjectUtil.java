package com.chis.common.utils;

import java.io.Reader;
import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Clob;
import java.util.Date;
import java.util.Iterator;
import java.util.Map;

/**
 * 对象工具类，包括判空、类型转换等操作
 *
 * <AUTHOR>
 * @version 1.1.1
 */
public class ObjectUtil {

    /**
     * 检查对象是否为null<br>
     * 判断标准为：
     *
     * <pre>
     * 1. == null
     * 2. equals(null)
     * </pre>
     *
     * @param obj 对象
     * @return 是否为null
     */
    public static boolean isNull(Object obj) {
        //noinspection ConstantConditions
        return null == obj || obj.equals(null);
    }

    /**
     * 检查对象是否不为null
     *
     * @param obj 对象
     * @return 是否为null
     */
    public static boolean isNotNull(Object obj) {
        return !isNull(obj);
    }

    /**
     * 判断指定对象是否为空，支持：
     *
     * <pre>
     * 1. CharSequence
     * 2. Map
     * 3. Iterable
     * 4. Iterator
     * 5. Array
     * </pre>
     *
     * @param obj 被判断的对象
     * @return 是否为空，如果类型不支持，返回false
     */
    public static boolean isEmpty(Object obj) {
        if (null == obj) {
            return true;
        }
        if (obj instanceof CharSequence) {
            return StringUtils.isBlank((CharSequence) obj);
        } else if (obj instanceof Map) {
            return isMapEmpty((Map<?, ?>) obj);
        } else if (obj instanceof Iterable) {
            return isIterableEmpty((Iterable<?>) obj);
        } else if (obj instanceof Iterator) {
            return isIteratorEmpty((Iterator<?>) obj);
        } else if (CollectionUtil.isArray(obj)) {
            return isArrayEmpty(obj);
        }
        return false;
    }

    /**
     * 判断指定对象是否为非空，支持：
     *
     * <pre>
     * 1. CharSequence
     * 2. Map
     * 3. Iterable
     * 4. Iterator
     * 5. Array
     * </pre>
     *
     * @param obj 被判断的对象
     * @return 是否为空，如果类型不支持，返回true
     */
    public static boolean isNotEmpty(Object obj) {
        return !isEmpty(obj);
    }

    private static boolean isMapEmpty(Map<?, ?> map) {
        return null == map || map.isEmpty();
    }

    private static boolean isIterableEmpty(Iterable<?> iterable) {
        return null == iterable || isEmpty(iterable.iterator());
    }

    private static boolean isIteratorEmpty(Iterator<?> Iterator) {
        return null == Iterator || !Iterator.hasNext();
    }

    private static boolean isArrayEmpty(Object array) {
        if (array != null) {
            if (CollectionUtil.isArray(array)) {
                return 0 == Array.getLength(array);
            }
            return false;
        }
        return true;
    }

    /**
     * 转换值为指定类型(转换失败返回null)
     *
     * @param <T>   转换的目标类型
     * @param type  类型目标
     * @param value 被转换值
     * @return 转换后的值
     */
    public static <T> T convert(Class<T> type, Object value) {
        return convert(type, value, null);
    }

    /**
     * 转换值为指定类型(转换失败返回默认值)
     *
     * @param <T>          转换的目标类型
     * @param type         类型目标
     * @param value        被转换值
     * @param defaultValue 默认值
     * @return 转换后的值
     */
    @SuppressWarnings("unchecked")
    public static <T> T convert(Class<T> type, Object value, T defaultValue) {
        if (isEmpty(value)) {
            return defaultValue;
        }
        T t = null;
        try {
            if (type == String.class) {
                t = (T) toStr(value);
            } else if (type == Double.class) {
                t = (T) toDouble(value);
            } else if (type == Float.class) {
                t = (T) toFloat(value);
            } else if (type == Long.class) {
                t = (T) toLong(value);
            } else if (type == Short.class) {
                t = (T) toShort(value);
            } else if (type == Integer.class) {
                t = (T) toInteger(value);
            } else if (type == BigDecimal.class) {
                t = (T) toBigDecimal(value);
            } else if (type == Date.class) {
                t = (T) toDate(value);
            } else if (type == Boolean.class) {
                t = (T) toBoolean(value);
            }
        } catch (Exception ignored) {
        }
        if (t == null) {
            return defaultValue;
        } else {
            return t;
        }
    }

    /**
     * 转换为String类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static String toStr(Object value) {
        if (value instanceof Clob) {
            return clobToStr((Clob) value);
        }
        return null == value ? null : value.toString();
    }

    /**
     * 转换为Double类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static Double toDouble(Object value) {
        return Double.valueOf(StringUtils.trim(value.toString()));
    }

    /**
     * 转换为Float类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static Float toFloat(Object value) {
        return Float.valueOf(StringUtils.trim(value.toString()));
    }

    /**
     * 转换为Long类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static Long toLong(Object value) {
        return Long.valueOf(StringUtils.trim(value.toString()));
    }

    /**
     * 转换为Short类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static Short toShort(Object value) {
        return Short.valueOf(StringUtils.trim(value.toString()));
    }

    /**
     * 转换为Integer类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static Integer toInteger(Object value) {
        return Integer.valueOf(StringUtils.trim(value.toString()));
    }

    /**
     * 转换为BigDecimal类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static BigDecimal toBigDecimal(Object value) {
        BigDecimal ret = null;
        if (value != null) {
            if (value instanceof BigDecimal) {
                ret = (BigDecimal) value;
            } else if (value instanceof String) {
                ret = new BigDecimal((String) value);
            } else if (value instanceof BigInteger) {
                ret = new BigDecimal((BigInteger) value);
            } else if (value instanceof Number) {
                ret = BigDecimal.valueOf(((Number) value).doubleValue());
            }
        }
        return ret;
    }

    /**
     * 转换为Date类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static Date toDate(Object value) {
        Date date = null;
        if (value != null) {
            if (value instanceof Date) {
                date = (Date) value;
            } else if (value instanceof Long) {
                date = new Date((Long) value * 1000L);
            } else if (value instanceof String) {
                Long aLong = convert(Long.class, value);
                if (aLong != null) {
                    date = new Date(aLong * 1000L);
                } else {
                    date = new Date((String) value);
                }
            }
        }
        return date;
    }

    /**
     * 转换为Date类型
     *
     * @param value 被转换值
     * @return 转换后的值
     */
    public static Boolean toBoolean(Object value) {
        Boolean data = null;
        if (value != null) {
            if (value instanceof Boolean) {
                data = (Boolean) value;
            } else if (value instanceof String) {
                if ("true".equals(value.toString())) {
                    data = Boolean.TRUE;
                } else if ("false".equals(value.toString())) {
                    data = Boolean.FALSE;
                }
            }
        }
        return data;
    }

    /**
     * Clob字段值转字符串
     *
     * @param clob {@link Clob}
     * @return 字符串
     */
    private static String clobToStr(Clob clob) {
        Reader reader = null;
        try {
            reader = clob.getCharacterStream();
            return IoUtil.read(reader);
        } catch (Exception ignored) {
        } finally {
            IoUtil.close(reader);
        }
        return "";
    }
}
