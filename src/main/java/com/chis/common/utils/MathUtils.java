package com.chis.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 计算类方法
 * 
 * <AUTHOR>
 * 
 */
public class MathUtils {
	
	
	/**
	 * 根据传入的args[]求标准差
	 * 
	 * @param args
	 *            double[]
	 * @return double
	 */
	public static double getStdDiffer(double[] args) {
		if (args == null)
			return 0;
		int length = args.length;
		if (length == 0)
			return 0;
		double sum = 0;
		for (int i = 0; i < length; i++) {
			sum += args[i];
		}
		double averg = sum / (double) length;
		double squareDiffer = 0;
		for (int i = 0; i < length; i++) {
			squareDiffer += Math.pow(averg - args[i], 2);
		}
		squareDiffer = squareDiffer / length;
		squareDiffer = Math.sqrt(squareDiffer);
		return squareDiffer;
	}
	
	/**
	 * 根据传入的args[]求平均值
	 * 
	 * @param args
	 *            double[]
	 * @return double
	 */
	public static double getPJZ(double[] args) {
		if (args == null)
			return 0;
		double sum = 0;
		for (int i = 0; i < args.length; i++) {
			sum += args[i];
		}
		return sum / args.length;
	}
	
	/**
	 * 求标准差
	 * @param list 需要计算的集合
	 * @param scale 返回值的精度，默认2
	 * @return
	 */
	public static BigDecimal deviation(List<BigDecimal> list, Integer scale) {
		if(null != list && list.size() > 0) {
			if(null == scale) {
				scale = 2;
			}
			int n = list.size();
			BigDecimal avg = average(list, scale*2);
			if(null == avg) {
				return null;
			}
			BigDecimal squar = BigDecimal.ZERO;
			for(BigDecimal b : list) {
				squar = squar.add(b.subtract(avg).pow(2));
			}
			
			BigDecimal rst = squar.divide(new BigDecimal(n), scale*2, RoundingMode.HALF_UP);
			rst = new BigDecimal(Math.sqrt(rst.doubleValue())).setScale(scale, RoundingMode.HALF_UP);
			return rst;
		}else {
			return null;
		}
	}
	
	/**
	 * 求平均值
	 * @param list 需要计算的集合
	 * @param scale 返回值的精度，默认2
	 * @return 
	 */
	public static BigDecimal average(List<BigDecimal> list, Integer scale) {
		if(null != list && list.size() > 0) {
			if(null == scale) {
				scale = 2;
			}
			
			BigDecimal total = BigDecimal.ZERO;
			for(BigDecimal b : list) {
				total = total.add(b);
			}
			//缺少RoundingMode.HALF_UP 有可能出现 java.lang.ArithmeticException: Rounding necessary
			return total.divide(new BigDecimal(list.size()), scale, RoundingMode.HALF_UP);
		}else {
			return null;
		}
	}
	
	public static void main(String[] args) {
		System.err.println("【】：" + new BigDecimal(-5).pow(2));
	}
	
	
	
	/**
	 * 计算a*100/b，保留2位有效数字，四舍五入
	 * @param a 除数
	 * @param b 被除数
	 * @return
	 */
	public static BigDecimal percent(BigDecimal a, BigDecimal b) {
		if(BigDecimal.ZERO.equals(b)) {
			return BigDecimal.ZERO;
		}
		return a.multiply(BigDecimal.TEN.pow(2)).divide(b,2,BigDecimal.ROUND_HALF_UP);
	}
	
	/**
	 * 计算a*100/b，保留2位有效数字，四舍五入
	 * @param a 除数
	 * @param b 被除数
	 * @return
	 */
	public static BigDecimal percent(BigDecimal a, BigDecimal b, Integer scale) {
		if(BigDecimal.ZERO.equals(b)) {
			return BigDecimal.ZERO;
		}
		if(null == scale) {
			scale = 2;
		}
		return a.multiply(BigDecimal.TEN.pow(2)).divide(b, scale,BigDecimal.ROUND_HALF_UP);
	}

	/**
	 * @Description: 计算皮尔逊相关系数
	 *  分子 （x原始值-x平均值）乘以（对应的y原始值-y平均值）逐个相加的结果
	 *  分母 （x原始值-x平均值）平方的和开根号的结果  乘以 （（对应的y原始值-y平均值）平方的和开根号的结果）
	 * @MethodAuthor pw,2022年05月9日
	 */
	public static BigDecimal calculatePearson(List<BigDecimal> xList, List<BigDecimal> yList, Integer scale){
		if(null == xList || null == yList || xList.size() == 0 || yList.size() == 0 || xList.size() != yList.size()){
			return BigDecimal.ZERO;
		}
		if(null == scale){
			scale = 5;
		}
		int size = xList.size();
		//保证精度 平均值精度+2
		BigDecimal xAvg = average(xList, scale+2);
		BigDecimal yAvg = average(yList, scale+2);
		//分子
		BigDecimal numerator = BigDecimal.ZERO;
		//x原始值-x平均值平方和
		BigDecimal xQuadraticSum = BigDecimal.ZERO;
		//y原始值-y平均值平方和
		BigDecimal yQuadraticSum = BigDecimal.ZERO;
		for(int i=0;i<size;i++){
			BigDecimal curVal = (xList.get(i).subtract(xAvg))
					.multiply((yList.get(i).subtract(yAvg)));
			numerator = numerator.add(curVal);
			xQuadraticSum = xQuadraticSum.add((xList.get(i).subtract(xAvg)).pow(2));
			yQuadraticSum = yQuadraticSum.add((yList.get(i).subtract(yAvg)).pow(2));
		}

		//（x原始值-x平均值）平方的和开根号的结果
		BigDecimal xPart = new BigDecimal(Math.sqrt(xQuadraticSum.doubleValue()))
				.setScale(scale+2, RoundingMode.HALF_UP);
		//（y原始值-y平均值）平方的和开根号的结果
		BigDecimal yPart = new BigDecimal(Math.sqrt(yQuadraticSum.doubleValue()))
				.setScale(scale+2, RoundingMode.HALF_UP);
		//分母
		BigDecimal denominator = xPart.multiply(yPart);
		if(denominator.compareTo(BigDecimal.ZERO) != 0){
			return numerator.divide(denominator, scale, RoundingMode.HALF_UP);
		}
		return BigDecimal.ZERO;
	}

	/**
	 * <p>方法描述：用于BigDecimal去除小数点后末尾的0 </p>
	 * @MethodAuthor： pw 2022/8/15
	 **/
	public static BigDecimal clearPointEndZero(BigDecimal decimal){
		if(null == decimal){
			return null;
		}
		int scale = decimal.scale();
		if(0 == scale){
			return decimal;
		}
		BigDecimal result = decimal;
		for(int i=1;i<=scale;i++){
			BigDecimal tmp = decimal.setScale(scale-i,BigDecimal.ROUND_DOWN);
			if(tmp.compareTo(decimal) == 0){
				result = tmp;
			}else{
				break;
			}
		}
		return result;
	}
	/**
	 *  <p>方法描述：四舍五入保留两位小数，小数点后末尾0去掉
	 *  b/a</p>
	 * @MethodAuthor hsj 2023-02-21 11:19
	 */
	public static BigDecimal divide(BigDecimal a, BigDecimal b) {
		if(BigDecimal.ZERO.equals(b)) {
			return BigDecimal.ZERO;
		}
		return  MathUtils.clearPointEndZero( a.multiply(BigDecimal.TEN.pow(2)).divide(b,2,BigDecimal.ROUND_HALF_UP));
	}
}
