package com.chis.common.utils;

import okhttp3.*;
import okio.BufferedSink;
import org.apache.log4j.Logger;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * OkHttp工具类
 *
 * <AUTHOR>
 * @version 1.0
 */
public class OkHttpUtils {
    private final static int READ_TIMEOUT = 300;
    private final static int CONNECT_TIMEOUT = 300;
    private final static int WRITE_TIMEOUT = 300;
    private static final Logger log = Logger.getLogger(OkHttpUtils.class);
    private static OkHttpClient okHttpClient;

    public OkHttpUtils(OkHttpClient okHttpClient) {
        OkHttpUtils.okHttpClient = okHttpClient;
    }

    private static void buildOkHttpClient() {
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();
        //设置读取超时时间
        clientBuilder.readTimeout(READ_TIMEOUT, TimeUnit.SECONDS);
        //设置超时连接时间
        clientBuilder.connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS);
        //设置写入超时时间
        clientBuilder.writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS);
        //支持HTTPS请求，跳过证书验证
        //clientBuilder.sslSocketFactory(createSSLSocketFactory());
        //添加拦截器
        //clientBuilder.addInterceptor(new HttpLogInterceptor());
        okHttpClient = clientBuilder.build();
    }

    /**
     * Get请求
     *
     * @param url   请求的url
     * @param param 请求的参数
     * @return response.body().string()
     */
    public static String get(String url, Map<String, String> param) {
        buildOkHttpClient();
        StringBuilder sb = new StringBuilder(url);
        sb.append("?");
        if (!ObjectUtil.isEmpty(param)) {
            for (Map.Entry<String, String> entry : param.entrySet()) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }
        url = sb.substring(0, sb.length() - 1);
        Request request = new Request.Builder().url(url).build();
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.body() != null) {
                String responseStr = response.body().string();
                log.info("okhttp3 get status: " + response.code() + ", url: " + url + ", resp: " + responseStr + ".");
                if (response.isSuccessful()) {
                    return responseStr;
                }
            } else {
                log.info("okhttp3 get status: " + response.code() + ", url: " + url + ".");
            }
        } catch (Exception e) {
            log.error("okhttp3 get exception：", new Throwable(e));
        }
        return "";
    }

    /**
     * Post请求
     *
     * @param url       请求的url
     * @param params    post form 提交的参数
     * @param headerMap headerMap
     * @return response.body().string()
     */
    public static String post(String url, Map<String, String> params, Map<String, String> headerMap) throws Exception {
        buildOkHttpClient();
        FormBody.Builder builder = new FormBody.Builder();
        if (!ObjectUtil.isEmpty(params)) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.add(entry.getKey(), entry.getValue());
            }
        }
        return post(url, builder.build(), headerMap);
    }

    /**
     * Post请求发送JSON数据
     *
     * @param url        请求Url
     * @param jsonParams 请求的JSON
     * @param headerMap  headerMap
     * @return response.body().string()
     */
    public static String postJsonParams(String url, String jsonParams, Map<String, String> headerMap) throws Exception {
        buildOkHttpClient();
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), jsonParams);
        return post(url, requestBody, headerMap);
    }

    /**
     * Post请求发送xml类型数据
     *
     * @param url       请求Url
     * @param xml       请求的xml
     * @param headerMap headerMap
     * @return response.body().string()
     */
    public static String postXmlParams(String url, String xml, Map<String, String> headerMap) throws Exception {
        buildOkHttpClient();
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/xml; charset=utf-8"), xml);
        return post(url, requestBody, headerMap);
    }


    /**
     * Post请求发送string以及file类型数据
     *
     * @param url       请求Url
     * @param paramMap  请求的参数
     * @param headerMap headerMap
     * @return response.body().string()
     */
    public static String postManyParams(String url, Map<String, Object> paramMap, Map<String, String> headerMap) throws Exception {
        buildOkHttpClient();
        MediaType mediaType = MediaType.parse("application/octet-stream");
        MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            String k = entry.getKey();
            Object v = entry.getValue();
            if (v instanceof String) {
                builder.addFormDataPart(k, StringUtils.objectToString(v));
            } else if (v instanceof File) {
                if (!ObjectUtil.isEmpty(((File) v).getName())) {
                    builder.addFormDataPart(k, ((File) v).getName(), RequestBody.create(mediaType, (File) v));
                }
            } else if (v instanceof InputStream) {
                if (!ObjectUtil.isEmpty(k)) {
                    builder.addFormDataPart(k, k, createStreamRequestBody((InputStream) v));
                }
            } else {
                builder.addFormDataPart(k, StringUtils.objectToString(v));
            }
        }
        return post(url, builder.build(), headerMap);
    }

    /**
     * 处理流
     * @param inputStream inputStream
     * @return RequestBody
     */
    private static RequestBody createStreamRequestBody(final InputStream inputStream) {
        return new RequestBody() {
            @Override
            public MediaType contentType() {
                return MediaType.parse("application/octet-stream");
            }
            @Override
            public long contentLength() {
                try {
                    return inputStream.available();
                } catch (IOException e) {
                    return -1;
                }
            }
            @Override
            public void writeTo(BufferedSink sink) throws IOException {
                try {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        sink.write(buffer, 0, bytesRead);
                    }
                } finally {
                    inputStream.close();
                }
            }
        };
    }

    /**
     * post
     *
     * @param url         请求的url
     * @param requestBody requestBody
     * @param headerMap   headerMap
     * @return response.body().string()
     */
    private static String post(String url, RequestBody requestBody, Map<String, String> headerMap) throws Exception {
        Request.Builder builder = new Request.Builder().url(url).post(requestBody);
        if (!ObjectUtil.isEmpty(headerMap)) {
            addHeaders(headerMap, builder);
        }
        Request request = builder.build();
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.body() != null) {
                String responseStr = response.body().string();
                log.info("okhttp3 post status: " + response.code() + ", url: " + url + ", resp: " + responseStr + ".");
                if (response.isSuccessful()) {
                    return responseStr;
                }
            } else {
                log.info("okhttp3 post status: " + response.code() + ", url: " + url + ".");
            }
        } catch (Exception e) {
            log.error("okhttp3 post exception：", new Throwable(e));
            throw e;
        }
        return "";
    }

    /**
     * 添加header信息
     *
     * @param headerMap header
     * @param builder   Request.Builder
     */
    private static void addHeaders(Map<String, String> headerMap, Request.Builder builder) {
        if (!ObjectUtil.isEmpty(headerMap)) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                builder.addHeader(entry.getKey(), entry.getValue());
            }
        }
    }

}
