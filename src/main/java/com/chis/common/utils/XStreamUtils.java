package com.chis.common.utils;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;
import com.thoughtworks.xstream.io.xml.XmlFriendlyNameCoder;
import com.thoughtworks.xstream.io.xml.XppDriver;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

/**
 * 模块说明：xml转换工具类<br/>
 * @XStreamAlias xml转换注解<br/>
 * @XStreamOmitField xml字段不生成注解<br/>
 * @XStreamImplicit 注解在集合上，不生成集合的标签<br/>
 * <AUTHOR>
 * @createDate 2016年12月24日
 */
public class XStreamUtils {

	/**
	 * 转换成xml
	 * 
	 * @param obj
	 * @return
	 */
	public static String toXml(Object obj) {
		if (null == obj)
			return null;
		// 转义--为-
		XStream xStream = new XStream(new XppDriver(new XmlFriendlyNameCoder("_-", "_")));
		xStream.autodetectAnnotations(true);
		String xml = xStream.toXML(obj);
		return xml;
	}

	/**
	 * xml转换对象
	 * 
	 * @param s
	 * @param clazz
	 * @return
	 */
	public static Object fromXml(String s, Class clazz) {
		return fromXml(s, clazz, null);
	}

	/**
	 * xml转换对象
	 *
	 * @param s
	 * @param rootClazz
	 * @param packageArr
	 * @return
	 */
	public static Object fromXml(String s, Class rootClazz, String[] packageArr) {
		if (StringUtils.isBlank(s))
			return null;
		XStream stream = new XStream(new DomDriver());
		if (null == packageArr || packageArr.length == 0) {
			String packageName = rootClazz.getPackage().getName();
			packageArr = new String[]{packageName + ".*"};
		}
		stream.allowTypesByWildcard(packageArr);
		stream.processAnnotations(rootClazz);
		// 忽略多余的xml节点
		stream.ignoreUnknownElements();
		return stream.fromXML(s);
	}
	
	/**
	 * 合并两个xml的内容
	 * @param xml1
	 * @param xml2
	 * @param nodeName1
	 * @param nodeName2
	 * @return
	 */
	public static String uniteXml(String xml1, String xml2, String nodeName1, String nodeName2) throws Exception{
		Document doc_1 = DocumentHelper.parseText(xml1);
		Document doc_2 = DocumentHelper.parseText(xml2);
		
		Element ele1 = (Element)doc_1.selectSingleNode(nodeName1);
		Element ele2 = (Element)doc_2.selectSingleNode(nodeName2);
		
		ele1.appendContent(ele2);
		return doc_1.asXML();
	}
	
	/**
	 * 合并两个xml的内容
	 * @param xml1
	 * @param xml2
	 * @param nodeName1
	 * @param nodeName2
	 * @return
	 */
	public static Document uniteXml(Document doc_1, Document doc_2, String nodeName1, String nodeName2) throws Exception{
		Element ele1 = (Element)doc_1.selectSingleNode(nodeName1);
		Element ele2 = (Element)doc_2.selectSingleNode(nodeName2);
		ele1.appendContent(ele2);
		return doc_1;
	}
	
	/**
	 * 合并多个xml
	 * @param xmls 多个xml以@zwx_split@隔开
	 * @param nodeName
	 * @return
	 * @throws Exception
	 */
	public static String uniteXmls(String xmls, String nodeName) throws Exception{
		String[] arrs = xmls.split("@zwx_split@");
		Document doc_1 = DocumentHelper.parseText(arrs[0]);
		for(int i=1; i<arrs.length; i++) {
			Document doc_i = DocumentHelper.parseText(arrs[i]);
			uniteXml(doc_1, doc_i, nodeName, nodeName);
		}
		return doc_1.asXML();
	}
}
