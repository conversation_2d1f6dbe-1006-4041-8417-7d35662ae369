package com.chis.common.utils;

import com.aliyun.oss.event.ProgressEvent;
import com.aliyun.oss.event.ProgressEventType;
import com.aliyun.oss.event.ProgressListener;

public class FileProgressListener implements ProgressListener {
	private long bytesWritten = 0;
	private long bytesRead = 0;
	private long totalBytes = -1;
	private boolean succeed = false;

	@Override
	public void progressChanged(ProgressEvent progressEvent) {
		long bytes = progressEvent.getBytes();
		ProgressEventType eventType = progressEvent.getEventType();
		
		
		System.out.println(eventType);
		switch (eventType) {
		case TRANSFER_STARTED_EVENT:
			break;
		case REQUEST_CONTENT_LENGTH_EVENT:
			this.totalBytes = bytes;
			break;
		case REQUEST_BYTE_TRANSFER_EVENT: // 上传进度
			this.bytesWritten += bytes;
			if (this.totalBytes != -1) {
				int percent = (int) (this.bytesWritten * 100.0 / this.totalBytes);
				System.out.println(percent);
			} 
			break;
		 case RESPONSE_CONTENT_LENGTH_EVENT:
             this.totalBytes = bytes;
             break;
         case RESPONSE_BYTE_TRANSFER_EVENT: // 下载进度
             this.bytesRead += bytes;
             if (this.totalBytes != -1) {
                 int percent = (int)(this.bytesRead * 100.0 / this.totalBytes);
                 System.out.println(percent);
             }
             break;
		case TRANSFER_COMPLETED_EVENT:
			this.succeed = true;
			break;
		case TRANSFER_FAILED_EVENT:
			break;
		default:
			break;
		}
	}

	public boolean isSucceed() {
		return succeed;
	}
}
