package com.chis.common.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Date;

import org.apache.commons.fileupload.FileItem;

import com.aliyun.oss.ClientConfiguration;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.AppendObjectRequest;
import com.aliyun.oss.model.AppendObjectResult;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.chis.common.bean.InputFileBean;

/**
 * OSS 基础操作
 * 
 * <AUTHOR>
 */
public class OssUtil {

	private static String ACCESS_ID = "";//PropertyUtils.getValue("oss.access.id")

	private static String ACCESS_KEY = "";//PropertyUtils.getValue("oss.access.key")
	// 华东2 访问的域名
	private static String endPoint = "";//PropertyUtils.getValue("oss.endpoint")
	public static String OSS_REGION = "";//PropertyUtils.getValue("oss.region")
	private static String bucketName = "";//PropertyUtils.getValue("oss.bucket")
	
	// 在自己内部定义自己的一个 实例，只供内部调用
	private static OSSClient ossClient = null;
	
	static {
		ClientConfiguration conf = new ClientConfiguration();
		conf.setIdleConnectionTime(1000);
		ossClient = new OSSClient(endPoint, ACCESS_ID, ACCESS_KEY, conf);
	}
	
	/**
	 * 创建存储空间
	 * 
	 * @param bucketName
	 *            Bucket的命名有以下规范： 只能包括小写字母，数字，短横线（-） 必须以小写字母或者数字开头 长度必须在3-63字节之间
	 */
	public static void createNewBucket(String bucketName) {
		try {
			boolean flag = ossClient.doesBucketExist(bucketName);
			// 判断存储空间是否存在
			if (flag) {
				return;
			}
			// 新建一个Bucket
			ossClient.createBucket(bucketName);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 删除存储空间
	 * 
	 * @param bucketName
	 */
	public static void delBucket(String bucketName) {
		try {
			boolean flag = ossClient.doesBucketExist(bucketName);
			// 判断存储空间是否存在
			if (flag) {
				return;
			}
			// 删除
			ossClient.deleteBucket(bucketName);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 设置Bucket权限
	 * 
	 * @param bucketName
	 * @type 1 public-read-write 2 public-read 3 private
	 */
	public static void setPower(String bucketName, Integer type) {
		try {
			boolean flag = ossClient.doesBucketExist(bucketName);
			// 判断存储空间是否存在
			if (!flag) {
				return;
			}
			CannedAccessControlList power = null;
			if (type.intValue() == 1) {
				power = CannedAccessControlList.PublicReadWrite;
			} else if (type.intValue() == 2) {
				power = CannedAccessControlList.PublicRead;
			} else {
				power = CannedAccessControlList.Private;
			}
			ossClient.setBucketAcl(bucketName, power);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 生成签名URL
	 * 
	 * @param bucketName
	 * @param key
	 */
	public static String getUrl(String bucketName, String key) {
		// 设置URL过期时间为1小时
		Date expiration = new Date(new Date().getTime() + 3600 * 1000);
		GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(
				bucketName, key, HttpMethod.GET);
		// 设置过期时间
		request.setExpiration(expiration);
		// 生成URL签名(HTTP PUT请求)
		URL signedUrl = ossClient.generatePresignedUrl(request);
		String path = signedUrl.toString();
		return path;
	}

	public static InputFileBean uploadFile(String key, FileItem content) {
		try {
			// M
			double size = new BigDecimal(content.getInputStream().available()).divide(
					new BigDecimal(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP)
					.doubleValue();
			if (size >= 100) {
				multipartUpload(key, content);
			} else {
				// 创建上传Object的Metadata
				ObjectMetadata meta = new ObjectMetadata();
				// 设置文件编码
				meta.setContentEncoding("utf-8");
				// 必须设置ContentLength
				meta.setContentLength(content.getInputStream().available());
				// 上传Object.
				PutObjectRequest re = new PutObjectRequest(bucketName, key,
						content.getInputStream(), meta);
				OssUtil.ossClient.putObject(re);
			}
			return initOSSFile(bucketName, key);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 上传文件
	 * 
	 * @param bucket
	 *            存储空间名称
	 * @param key
	 *            上传路径及文件名称 如： xxxx/xxx.jpg,若为根目录直接为文件名称 xxx.jpg
	 * @param filePath
	 *            文件的真实路径
	 */
	public static InputFileBean uploadFile(String key, String filePath) {
		try {
			File file = new File(filePath);
			// 判断是不是文件
			if (!file.isFile()) {
				return null;
			}

			long length = file.length();
			// M
			double size = new BigDecimal(length).divide(
					new BigDecimal(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP)
					.doubleValue();
			// 大于100M 分片上传
			if (size >= 100) {
//				multipartUpload(key, new FileInputStream(file));
			} else {
				InputStream content = new FileInputStream(file);
				// 创建上传Object的Metadata
				ObjectMetadata meta = new ObjectMetadata();
				// 设置文件编码
				meta.setContentEncoding("utf-8");
				// 必须设置ContentLength
				meta.setContentLength(file.length());
				// 上传Object.
				PutObjectRequest re = new PutObjectRequest(bucketName, key,
						content, meta)
						.withProgressListener(new FileProgressListener());
				OssUtil.ossClient.putObject(re);
			}
			return initOSSFile(bucketName, key);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 上传文件，只适合小于100的文件上传
	 * 
	 * @param bucket
	 *            存储空间名称
	 * @param key
	 *            上传路径及文件名称 如： xxxx/xxx.jpg,若为根目录直接为文件名称 xxx.jpg
	 * @param content
	 *            文件流
	 */
	public static InputFileBean uploadFile(String key, String bucket, InputStream content) {
		try {
			// M
			double size = new BigDecimal(content.available()).divide(
					new BigDecimal(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP)
					.doubleValue();
			if (size >= 100) {
				return null;
			} else {
				// 创建上传Object的Metadata
				ObjectMetadata meta = new ObjectMetadata();
				// 设置文件编码
				meta.setContentEncoding("utf-8");
				// 必须设置ContentLength
				meta.setContentLength(content.available());
				// 上传Object.
				PutObjectRequest re = new PutObjectRequest(bucketName, key, content, meta);
				OssUtil.ossClient.putObject(re);
			}
			return initOSSFile(bucketName, key);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 上传文件
	 * 
	 * @param bucket
	 *            存储空间名称
	 * @param key
	 *            上传路径及文件名称 如： xxxx/xxx.jpg,若为根目录直接为文件名称 xxx.jpg
	 * @param file
	 */
	public static InputFileBean uploadFile(String key, File file) {
		try {
			long length = file.length();
			// kb
			double size = new BigDecimal(length).divide(
					new BigDecimal(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP)
					.doubleValue();
			// 大于100M 分片上传
			if (size >= 100) {
//				multipartUpload(key, new FileInputStream(file));
			} else {
				InputStream content = new FileInputStream(file);
				// 创建上传Object的Metadata
				ObjectMetadata meta = new ObjectMetadata();
				// 设置文件编码
				meta.setContentEncoding("utf-8");
				// 必须设置ContentLength
				meta.setContentLength(file.length());
				// 上传进度
				PutObjectRequest re = new PutObjectRequest(bucketName, key,
						content, meta)
						.withProgressListener(new FileProgressListener());
				OssUtil.ossClient.putObject(re);
				// 打印ETag
			}

			return initOSSFile(bucketName, key);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return null;
	}

	/**
	 * 分块上传 适合大文件上传
	 * 
	 * @param key
	 * @param partFile
	 * @throws IOException
	 */
	private static void multipartUpload(String key, FileItem input)
			throws Exception {
		try {
			MultipartUpload.upload(key, bucketName, input, ossClient);
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
	}

	/**
	 * 追加上传
	 * 
	 * @param bucketName
	 * @param key
	 * @throws FileNotFoundException
	 */
	public static InputFileBean appendUpload(String key, File partFile)
			throws FileNotFoundException {
		AppendObjectRequest appendObjectRequest = new AppendObjectRequest(
				bucketName, key, new FileInputStream(partFile));
		appendObjectRequest.withProgressListener(new FileProgressListener());
		int partSize = 1024 * 1024 * 5;

		// 计算分块数目
		int partCount = (int) (partFile.length() / partSize);
		if (partFile.length() % partSize != 0) {
			partCount++;
		}

		AppendObjectResult appendObjectResult = null;
		for (int i = 0; i < partCount; i++) {
			if (i == 0) {
				appendObjectRequest.setPosition(0l);
				appendObjectResult = OssUtil.ossClient
						.appendObject(appendObjectRequest);
			} else {
				appendObjectRequest.setPosition(appendObjectResult
						.getNextPosition());
				appendObjectResult = OssUtil.ossClient
						.appendObject(appendObjectRequest);
			}
		}

		return initOSSFile(bucketName, key);
	}
	
	/**
	 * 下载文件
	 * 
	 * @param bucketName
	 * @param key
	 * @throws IOException
	 * @return 1 成功 -1 下载失败 0 文件不存在
	 */
	public static Integer downloadFile(String key, String path) {
		try {
			// 判断文件是否存在
			boolean found = OssUtil.ossClient.doesObjectExist(bucketName, key);
			if (!found) {
				return 0;
			}

			// 下载object到文件
			GetObjectRequest req = new GetObjectRequest(bucketName, key);
			req.withProgressListener(new FileProgressListener());
			OssUtil.ossClient.getObject(req, new File(path));
			return 1;
		} catch (Exception e) {
			e.printStackTrace();
			return -1;
		}
	}
	
	/**
	 * 删除附件
	 * @param key
	 * @param bucket
	 */
	public static void delFile(String key) {
		boolean found = OssUtil.ossClient.doesObjectExist(bucketName, key);
		if (found) {
			OssUtil.ossClient.deleteObject(bucketName, key);
		}
	}

	private static InputFileBean initOSSFile(String bucketName, String key) {
		try {
			String encodedObjectName = URLEncoder.encode(key, "utf-8");
			return new InputFileBean(OssUtil.OSS_REGION, bucketName,
					encodedObjectName);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	/**
	 * 验证key是否存在
	 * @param key 文件key
	 * @param bucket 盘符
	 * @return
	 */
	public static boolean checkKeyExists(String key, String bucket) {
		boolean found = ossClient.doesObjectExist(bucket, key);
		return found;
	}
	
	public static OSSObject dowloadFile(String key, String bucket) {
		// 判断文件是否存在
		boolean found = OssUtil.ossClient.doesObjectExist(bucket, key);
		if (!found) {
			return null;
		}
		// 下载object到文件
		GetObjectRequest req = new GetObjectRequest(bucketName, key);
		OSSObject obj = OssUtil.ossClient.getObject(req);
		return obj;
	}
	
	/**
	 * 获取文件的头部
	 */
	public static String getRequestContentType(String key, String bucket) {
	   ObjectMetadata obj = null;
	   if (checkKeyExists(key, bucket)) {
		   obj = ossClient.getObjectMetadata(bucket, key);
	   }
	   
	   if (obj == null) {
		   return null;
	   }
	   return obj.getContentType();
	}
	
	/**
	 * 缩放图片
	 * @param m 指定缩略的模式 必填
	 *          lfit：等比缩放，限制在设定在指定w与h的矩形内的最大图片。
	 *			mfit：等比缩放，延伸出指定w与h的矩形框外的最小图片。
	 *			fill：固定宽高，将延伸出指定w与h的矩形框外的最小图片进行居中裁剪。
	 *			pad：固定宽高，缩略填充
	 *			fixed：固定宽高，强制缩略
	 * @param width 宽度 1-4096 必填
	 * @param height 高度 1-4096 必填
	 * @param limit 指定当目标缩略图大于原图时是否处理。值是 1 表示不处理；值是 0 表示处理 ，默认是 1
	 * @param color 当缩放模式选择为pad（缩略填充）时， 如00FF00（绿色）
	 */
	public static String getCompressImgUrl(String bucketName, String key, String m, Integer width, Integer height, Integer limit, String color) {
		try {
			StringBuilder sb = new StringBuilder();
			sb.append("image/resize,");
			sb.append("m_").append(m).append(",");
			sb.append("w_").append(width).append(",");
			sb.append("h_").append(height).append(",");
			if (StringUtils.isNotBlank(color)) {
				sb.append("color_").append(color).append(",");
			}
			
			if (limit != null) {
				sb.append("limit_").append(limit);
			}
			
			// 设置URL过期时间为1小时
			Date expiration = new Date(new Date().getTime() + 3600 * 1000);
			GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(
					bucketName, key, HttpMethod.GET);
			// 设置过期时间
			request.setExpiration(expiration);
			request.setProcess(sb.toString());
			// 生成URL签名(HTTP PUT请求)
			URL signedUrl = ossClient.generatePresignedUrl(request);
			String path = signedUrl.toString();
			return path;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 删除文件
	 * @param bucketName
	 * @param key
	 * @return 1 成功 -1 失败 0 文件不存在
	 */
	public static Integer delObject(String bucketName, String key) {
		// 判断文件是否存在
		boolean found = ossClient.doesObjectExist(bucketName, key);
		if (!found) {
			return 0;
		}
		
		try {
			ossClient.deleteObject(bucketName, key);
			return 1;
		} catch (Exception e) {
			e.printStackTrace();
			return -1;
		}
	}
}
