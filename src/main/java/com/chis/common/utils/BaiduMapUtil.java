package com.chis.common.utils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpMethod;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * Gps坐标转换成百度坐标
 * 
 * <AUTHOR>
 * @创建时间 2014-11-19
 */
public class BaiduMapUtil {
	public static double DEF_PI = 3.14159265359; // PI
	public static double DEF_2PI = 6.28318530712; // 2*PI
	public static double DEF_PI180 = 0.01745329252; // PI/180.0
	public static double DEF_R = 6370693.5; // radius of earth

	public static double GetShortDistance(double lon1, double lat1,
			double lon2, double lat2) {
		double ew1, ns1, ew2, ns2;
		double dx, dy, dew;
		double distance;
		// 角度转换为弧度
		ew1 = lon1 * DEF_PI180;
		ns1 = lat1 * DEF_PI180;
		ew2 = lon2 * DEF_PI180;
		ns2 = lat2 * DEF_PI180;
		// 经度差
		dew = ew1 - ew2;
		// 若跨东经和西经180 度，进行调整
		if (dew > DEF_PI)
			dew = DEF_2PI - dew;
		else if (dew < -DEF_PI)
			dew = DEF_2PI + dew;
		dx = DEF_R * Math.cos(ns1) * dew; // 东西方向长度(在纬度圈上的投影长度)
		dy = DEF_R * (ns1 - ns2); // 南北方向长度(在经度圈上的投影长度)
		// 勾股定理求斜边长
		distance = Math.sqrt(dx * dx + dy * dy);
		return distance;
	}

	public static double GetLongDistance(double lon1, double lat1, double lon2,
			double lat2) {
		double ew1, ns1, ew2, ns2;
		double distance;
		// 角度转换为弧度
		ew1 = lon1 * DEF_PI180;
		ns1 = lat1 * DEF_PI180;
		ew2 = lon2 * DEF_PI180;
		ns2 = lat2 * DEF_PI180;
		// 求大圆劣弧与球心所夹的角(弧度)
		distance = Math.sin(ns1) * Math.sin(ns2) + Math.cos(ns1)
				* Math.cos(ns2) * Math.cos(ew1 - ew2);
		// 调整到[-1..1]范围内，避免溢出
		if (distance > 1.0)
			distance = 1.0;
		else if (distance < -1.0)
			distance = -1.0;
		// 求大圆劣弧长度
		distance = DEF_R * Math.acos(distance);
		return distance;
	}

	/**
	 * Gps坐标转换成baidu坐标
	 * 
	 * @param lng
	 *            经度
	 * @param lat
	 *            纬度
	 * @return 返回坐标 lng#@#lat
	 */
	public static String findTransBaiduPoints(String lng, String lat) {
		if (StringUtils.isNotBlank(lng) && StringUtils.isNotBlank(lat)) {
			HttpURLConnection url_con = null;
			StringBuffer urlStr = new StringBuffer();
			// 调用百度接口 from 0 to 4 表示GPS转换成百度坐标，x、y 表示经度、纬度
			urlStr = new StringBuffer(
					"http://api.map.baidu.com/ag/coord/convert?from=");
			urlStr.append("0&to=4&x=").append(lng).append("&y=").append(lat);
			try {
				// 百度地图http请求返回值
				String httpRespStr = null;
				URL url = new URL(urlStr.toString());
				StringBuffer bankXmlBuffer = new StringBuffer();
				// 创建URL连接，提交到数据，获取返回结果
				HttpURLConnection connection = (HttpURLConnection) url
						.openConnection();
				connection.setRequestMethod("POST");
				connection.setDoOutput(true);
				connection.setRequestProperty("User-Agent", "directclient");

				PrintWriter out = new PrintWriter(new OutputStreamWriter(
						connection.getOutputStream(), "GBK"));
				out.println();
				out.close();
				BufferedReader in = new BufferedReader(new InputStreamReader(
						connection.getInputStream(), "GBK"));

				String inputLine;

				while ((inputLine = in.readLine()) != null) {
					bankXmlBuffer.append(inputLine);
				}
				in.close();
				httpRespStr = bankXmlBuffer.toString();
				// 判断返回值是否为空，如为空，则解析json
				if (StringUtils.isNotBlank(httpRespStr)) {
					try {
						JSONObject jsonObj = JSONObject
								.parseObject(httpRespStr);
						// 返回信息是否正确，为0表示正确
						String status = jsonObj.getString("error");
						// result
						if ("0".equals(status)) {
							// 经度
							String x = jsonObj.getString("x");
							// 经度转换后值
							String decodeX = StringUtils.decode(x);
							// 纬度
							String y = jsonObj.getString("y");
							// 纬度转换后值
							String decodeY = StringUtils.decode(y);
							// 返回值
							if (StringUtils.isNotBlank(decodeX)
									&& StringUtils.isNotBlank(decodeY)) {
								StringBuilder xAndy = new StringBuilder(decodeX)
										.append("#@#").append(decodeY);
								return xAndy.toString();
							} else {
								return null;
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else {
					return null;
				}
			} catch (Exception e) {
				System.out.println("发送GET请求出现异常！" + e);
				e.printStackTrace();
				return null;
			} finally {
				if (url_con != null)
					url_con.disconnect();
			}
		}
		return null;
	}

	/**
	 * Gps坐标转换成baidu坐标
	 * 
	 * @param lngAndLats
	 *            经度和纬度的字符串 格式为：lng,lat;lng,lat,lng,lat
	 * @return 返回坐标 集合
	 */
	public static List<String[]> findTransBaiduPointsList(String lngAndLats) {
		List<String[]> lngAndLatList = new ArrayList<String[]>();
		if (StringUtils.isNotBlank(lngAndLats)) {
			HttpURLConnection url_con = null;
			StringBuffer urlStr = new StringBuffer();
			// 调用百度接口 from 1 to 5 表示GPS转换成百度坐标，x、y 表示经度、纬度
			urlStr = new StringBuffer(
					"http://api.map.baidu.com/geoconv/v1/?coords=")
					.append(lngAndLats);
			urlStr.append("&from=1&to=5&ak=247a689f8a4f8552ba7dcee56c641671");
			try {
				// 百度地图http请求返回值
				String httpRespStr = null;
				HttpClient client = new HttpClient();
				HttpMethod method = new GetMethod(urlStr.toString());
				// 参数对象
				HttpMethodParams paramMap = new HttpMethodParams();
				paramMap.setParameter("", "");
				method.setParams(paramMap);

				client.executeMethod(method);
				// 打印服务器返回的状态
				// 打印返回的信息
				httpRespStr = method.getResponseBodyAsString();
				// 释放连接
				method.releaseConnection();

				// 判断返回值是否为空，如为空，则解析json
				if (StringUtils.isNotBlank(httpRespStr)) {
					JSONObject jsonObj = JSONObject.parseObject(httpRespStr);
					// 返回信息是否正确，为0表示正确
					String error = jsonObj.getString("status");
					if ("0".equals(error)) {
						JSONArray jsonArray = jsonObj.getJSONArray("result");
						if (jsonArray.size() > 0) {
							for (Object obj : jsonArray) {
								JSONObject jsonSub = (JSONObject) obj;
								// 经度
								String decodeX = jsonSub.getString("x");
								// 纬度
								String decodeY = jsonSub.getString("y");
								if (StringUtils.isNotBlank(decodeX)
										&& StringUtils.isNotBlank(decodeY)) {
									String[] lngAndLatArr = new String[] {
											decodeX, decodeY };
									lngAndLatList.add(lngAndLatArr);
								} else {
									return null;
								}
							}
						} else {
							return null;
						}
					}
				}
			} catch (Exception e) {
				System.out.println("发送GET请求出现异常！" + e);
				e.printStackTrace();
				return null;
			} finally {
				if (url_con != null)
					url_con.disconnect();
			}
		}
		return lngAndLatList;
	}

	/**
	 * 根据地址获取经纬度
	 * 
	 * @param address
	 *            地址
	 * @return 0-经度 1-维度
	 * @throws Exception
	 * <AUTHOR>
	 */
	public static String[] findLngLat(String address) throws Exception {
		String url = "http://api.map.baidu.com/geocoder/v2/";

		HttpClient client = new HttpClient();
		HttpMethod method = new GetMethod(url);

		NameValuePair pair = new NameValuePair("address", address);
		NameValuePair pair2 = new NameValuePair("ak",
				"247a689f8a4f8552ba7dcee56c641671");
		NameValuePair pair3 = new NameValuePair("output", "json");

		NameValuePair[] pairs = new NameValuePair[] { pair, pair2, pair3 };
		// 设置编码UTF-8
		method.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,
				"UTF-8");
		method.setQueryString(pairs);

		client.executeMethod(method);
		// 打印服务器返回的状态
		// 打印返回的信息
		String rstStr = method.getResponseBodyAsString();
		// 释放连接
		method.releaseConnection();

		try {
			JSONObject object = (JSONObject) JSON.parse(rstStr);
			JSONObject result = object.getJSONObject("result");
			JSONObject location = result.getJSONObject("location");

			return new String[] { location.get("lng").toString(),
					location.get("lat").toString() };
		} catch (Exception e) {
		}

		return null;
	}

}
