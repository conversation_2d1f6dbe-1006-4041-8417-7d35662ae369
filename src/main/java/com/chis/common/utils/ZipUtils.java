package com.chis.common.utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import javax.servlet.http.HttpServletResponse;

public class ZipUtils {

	public static void main(String[] args) throws IOException {
		StringBuilder sb = new StringBuilder();
		sb.append("hello,world,nice to meet you");

		System.err.println("【压缩前】：" + sb.length());
		System.err.println("【gzip压缩后】：" + gzip(sb.toString()));
		System.err.println("【zip压缩后】：" + zip(sb.toString()));
		
		File[] srcFiles = { new File("E:\\1.png"), new File("E:\\2.png"), new File("E:\\1.txt") };
		File zipFile = new File("E:\\ZipFile.zip");
		         // 调用压缩方法
		        /* zipFiles(srcFiles, zipFile);*/
	}

	/**
	 * 
	 * 使用gzip进行压缩
	 */
	public static String gzip(String primStr) {
		if (primStr == null || primStr.length() == 0) {
			return primStr;
		}
		ByteArrayOutputStream out = new ByteArrayOutputStream();
		GZIPOutputStream gzip = null;
		try {
			gzip = new GZIPOutputStream(out);
			gzip.write(primStr.getBytes("UTF-8"));
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (gzip != null) {
				try {
					gzip.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return new sun.misc.BASE64Encoder().encode(out.toByteArray());
	}

	/**
	 * 
	 * <p>
	 * Description:使用gzip进行解压缩
	 * </p>
	 * 
	 * @param compressedStr
	 * @return
	 */
	public static String gunzip(String compressedStr) {
		if (compressedStr == null) {
			return null;
		}

		ByteArrayOutputStream out = new ByteArrayOutputStream();
		ByteArrayInputStream in = null;
		GZIPInputStream ginzip = null;
		byte[] compressed = null;
		String decompressed = null;
		try {
			compressed = new sun.misc.BASE64Decoder().decodeBuffer(compressedStr);
			in = new ByteArrayInputStream(compressed);
			ginzip = new GZIPInputStream(in);

			byte[] buffer = new byte[1024];
			int offset = -1;
			while ((offset = ginzip.read(buffer)) != -1) {
				out.write(buffer, 0, offset);
			}
			decompressed = out.toString("UTF-8");
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (ginzip != null) {
				try {
					ginzip.close();
				} catch (IOException e) {
				}
			}
			if (in != null) {
				try {
					in.close();
				} catch (IOException e) {
				}
			}
			if (out != null) {
				try {
					out.close();
				} catch (IOException e) {
				}
			}
		}

		return decompressed;
	}

	/**
	 * 使用zip进行压缩
	 * 
	 * @param str
	 *            压缩前的文本
	 * @return 返回压缩后的文本
	 */
	public static final String zip(String str) {
		if (str == null)
			return null;
		byte[] compressed;
		ByteArrayOutputStream out = null;
		ZipOutputStream zout = null;
		String compressedStr = null;
		try {
			out = new ByteArrayOutputStream();
			zout = new ZipOutputStream(out);
			zout.putNextEntry(new ZipEntry("0"));
			zout.write(str.getBytes("UTF-8"));
			zout.closeEntry();
			compressed = out.toByteArray();
			compressedStr = new sun.misc.BASE64Encoder().encodeBuffer(compressed);
		} catch (IOException e) {
			compressed = null;
		} finally {
			if (zout != null) {
				try {
					zout.close();
				} catch (IOException e) {
				}
			}
			if (out != null) {
				try {
					out.close();
				} catch (IOException e) {
				}
			}
		}
		return compressedStr;
	}

	/**
	 * 使用zip进行解压缩
	 * 
	 * @param compressed
	 *            压缩后的文本
	 * @return 解压后的字符串
	 */
	public static final String unzip(String compressedStr) {
		if (compressedStr == null) {
			return null;
		}

		ByteArrayOutputStream out = null;
		ByteArrayInputStream in = null;
		ZipInputStream zin = null;
		String decompressed = null;
		try {
			byte[] compressed = new sun.misc.BASE64Decoder().decodeBuffer(compressedStr);
			out = new ByteArrayOutputStream();
			in = new ByteArrayInputStream(compressed);
			zin = new ZipInputStream(in);
			zin.getNextEntry();
			byte[] buffer = new byte[1024];
			int offset = -1;
			while ((offset = zin.read(buffer)) != -1) {
				out.write(buffer, 0, offset);
			}
			decompressed = out.toString("UTF-8");
		} catch (IOException e) {
			decompressed = null;
		} finally {
			if (zin != null) {
				try {
					zin.close();
				} catch (IOException e) {
				}
			}
			if (in != null) {
				try {
					in.close();
				} catch (IOException e) {
				}
			}
			if (out != null) {
				try {
					out.close();
				} catch (IOException e) {
				}
			}
		}
		return decompressed;
	}

	/**
	 * 返回信息，信息json字符串放在data.json文件里面，并且压缩成data.zip的方式进行下载
	 * 
	 * @param resp
	 * @param json
	 */
	public static void sendBackInfo(HttpServletResponse resp, String json) {
		try {
			// 服务端将字符串写到文件中并压缩zip
			resp.setHeader("Content-Disposition", "attachment;filename=data.zip");
			resp.setHeader("Connection", "close");
			resp.setContentType("application/octet-stream");
			ZipOutputStream zip = new ZipOutputStream(resp.getOutputStream());
			zip.putNextEntry(new ZipEntry("data.json"));

			byte[] byt = json.getBytes("UTF-8");
			zip.write(byt, 0, byt.length);
			zip.closeEntry();
			zip.finish();
			zip.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	

	/**
	 * 生成的字符串压缩成zip文件转换到指定文件 编码转换编码为utf-8
	 * 
	 * @param filePath
	 *            文件路径
	 * @param xml
	 *            XML字符串
	 * @return 压缩XML文件路径
	 * @throws java.io.IOException
	 * <AUTHOR>
	 * @createDate 2014-9-5
	 */
	public static String ZipFileToZip(String filePath, String xml) throws IOException {
		FileOutputStream out = null;
		ZipOutputStream zipOut = null;
		try {
			File pathFile = new File(filePath);
			if (!pathFile.exists()) {
				pathFile.mkdirs();
			}

			String uuid = UUID.randomUUID().toString().replaceAll("-", "");
			// xml文件的路径
			String xmlPath = filePath + uuid + ".xml";
			// zip文件路径
			String zipPath = filePath + uuid + ".zip";

			// 文件路径
			out = new FileOutputStream(xmlPath);
			out.write(xml.getBytes("UTF-8"));
			out.close();

			zipOut = new ZipOutputStream(new FileOutputStream(new File(zipPath)));
			// xml文件
			File xmlFile = new File(xmlPath);
			fileToZip(filePath, xmlFile, zipOut);
			// 删除XML文件
			xmlFile.delete();
			return zipPath;
		} catch (IOException e) {
			e.printStackTrace();
			throw e;
		} finally {
			if (null != out) {
				out.close();
			}
			if (null != zipOut) {
				zipOut.close();
			}
		}
	}

	/**
	 * 将文件写入到压缩文件中
	 * @param baseDirPath
	 * @param file
	 * @param out
	 * @throws IOException
	 */
	public static void fileToZip(String baseDirPath, File file, ZipOutputStream out) throws IOException {
		FileInputStream in = null;
		ZipEntry entry = null;
		byte[] buffer = new byte[4096];
		int bytes_read;
		if (file.isFile()) {
			try {
				in = new FileInputStream(file);
				entry = new ZipEntry(getEntryName(baseDirPath, file));
				out.putNextEntry(entry);
				while ((bytes_read = in.read(buffer)) != -1) {
					out.write(buffer, 0, bytes_read);
				}
				out.closeEntry();
				in.close();
			} catch (IOException e) {
				e.printStackTrace();
			} finally {
				// 关闭输入输出流
				if (out != null) {
					out.closeEntry();
				}
				if (in != null) {
					in.close();
				}
			}
		}
	}

	/**
	 * 获取压缩文件内容
	 * @param baseDirPath
	 * @param file
	 * @return
	 */
	private static String getEntryName(String baseDirPath, File file) {
		if (!baseDirPath.endsWith(File.separator)) {
			baseDirPath = baseDirPath + File.separator;
		}
		String filePath = file.getAbsolutePath();
		if (file.isDirectory()) {
			filePath = filePath + "/";
		}
		return filePath.substring(filePath.indexOf(baseDirPath) + baseDirPath.length());
	}
	
	/**
	 * 生成的字符串压缩成zip文件转换到指定文件 编码转换编码为utf-8，压缩包内文件后缀为json
	 * @param filePath
	 * @param msg
	 * @return
	 * @throws IOException
	 */
	public static String ZipJsonFileToZip(String filePath, String msg) throws IOException {
		return ZipFileToZip(filePath,msg,"json");
	}
	
	/**
	 * 生成的字符串压缩成zip文件转换到指定文件 编码转换编码为utf-8
	 * 
	 * @param filePath
	 * @param msg
	 * @param fileType
	 * @return
	 * @throws IOException
	 */
	public static String ZipFileToZip(String filePath, String msg,String fileType) throws IOException {
		FileOutputStream out = null;
		ZipOutputStream zipOut = null;
		try {
			File pathFile = new File(filePath);
			if (!pathFile.exists()) {
				pathFile.mkdirs();
			}

			String uuid = UUID.randomUUID().toString().replaceAll("-", "");
			// xml文件的路径
			String path = filePath + uuid + "."+fileType;
			// zip文件路径
			String zipPath = filePath + uuid + ".zip";

			// 文件路径
			out = new FileOutputStream(path);
			out.write(msg.getBytes("UTF-8"));
			out.close();

			zipOut = new ZipOutputStream(new FileOutputStream(new File(zipPath)));
			// xml文件
			File file = new File(path);
			fileToZip(filePath, file, zipOut);
			// 删除XML文件
			file.delete();
			return zipPath;
		} catch (IOException e) {
			e.printStackTrace();
			throw e;
		} finally {
			if (null != out) {
				out.close();
			}
			if (null != zipOut) {
				zipOut.close();
			}
		}
	}


	/***
	 * <p>方法描述: 将字符串进行zip压缩，并返回压缩后的字节数组</p>
	 *
	 * @MethodAuthor mxp,2019/1/11,string2ZipBytes
	 */
	public static byte[] string2ZipBytes(String json) throws IOException {
		ByteArrayOutputStream out = new ByteArrayOutputStream();
		ZipOutputStream zout = new ZipOutputStream(out);
		zout.putNextEntry(new ZipEntry("data.json"));
		zout.write(json.getBytes(StandardCharsets.UTF_8));
		zout.closeEntry();
		return out.toByteArray();
	}

	public static void zipFiles(List<File> srcFiles, File zipFile) {
		//注意 zip中不能存在相同名称的文件
		//父文件夹不存在时 创建父文件夹
		if(null == zipFile.getParentFile() || !zipFile.getParentFile().exists()){
			zipFile.getParentFile().mkdirs();
		}
		// 创建 FileOutputStream 对象
		FileOutputStream fileOutputStream = null;
		// 创建 ZipOutputStream
		ZipOutputStream zipOutputStream = null;
		// 创建 FileInputStream 对象
		FileInputStream fileInputStream = null;

		try {
			// 实例化 FileOutputStream 对象
			fileOutputStream = new FileOutputStream(zipFile);
			// 实例化 ZipOutputStream 对象
			zipOutputStream = new ZipOutputStream(fileOutputStream);
			// 创建 ZipEntry 对象
			ZipEntry zipEntry = null;
			// 遍历源文件数组
			for (int i = 0; i < srcFiles.size(); i++) {
				// 将源文件数组中的当前文件读入 FileInputStream 流中
				fileInputStream = new FileInputStream(srcFiles.get(i));
				// 实例化 ZipEntry 对象，源文件数组中的当前文件
				zipEntry = new ZipEntry(srcFiles.get(i).getName());
				zipOutputStream.putNextEntry(zipEntry);
				// 该变量记录每次真正读的字节个数
				int len;
				// 定义每次读取的字节数组
				byte[] buffer = new byte[1024];
				while ((len = fileInputStream.read(buffer)) > 0) {
					zipOutputStream.write(buffer, 0, len);
				}
				//不关闭 文件不能删除
				fileInputStream.close();
			}
			zipOutputStream.closeEntry();
			zipOutputStream.close();
			fileOutputStream.flush();
			fileOutputStream.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * <p>方法描述：文件夹打包</p>
	 * @MethodAuthor： yzz
	 * @Date：2022-11-25
	 **/
	public static void zipFileNew(File fileToZip, String fileName, ZipOutputStream zipOut) throws IOException {
		if (fileToZip.isHidden()) {
			return;
		}
		if (fileToZip.isDirectory()) {
			if (fileName.endsWith("/")) {
				zipOut.putNextEntry(new ZipEntry(fileName));
				zipOut.closeEntry();
			} else {
				zipOut.putNextEntry(new ZipEntry(fileName + "/"));
				zipOut.closeEntry();
			}
			File[] children = fileToZip.listFiles();
			for (File childFile : children) {
				zipFileNew(childFile, fileName + "/" + childFile.getName(), zipOut);
			}
			return;
		}
		FileInputStream fis = new FileInputStream(fileToZip);
		ZipEntry zipEntry = new ZipEntry(fileName);
		zipOut.putNextEntry(zipEntry);
		byte[] bytes = new byte[1024];
		int length;
		while ((length = fis.read(bytes)) >= 0) {
			zipOut.write(bytes, 0, length);
		}
		zipOut.closeEntry();
		fis.close();
	}

}