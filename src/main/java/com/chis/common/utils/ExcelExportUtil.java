package com.chis.common.utils;

import com.chis.common.entity.ExcelExportObject;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.util.*;

/**
 * EXCEL poi 公共导出工具类
 *
 * <AUTHOR>
 * @version 2.0
 */
public class ExcelExportUtil {

    /**
     * 默认Sheet名称
     */
    private String sheetName;

    /**
     * 单行标题
     */
    private String title;
    /**
     * 多行标题
     */
    private String[] multiTitle;
    /**
     * 是否多行标题
     */
    private boolean needMultiTitle;
    /**
     * 多行标题居中样式
     */
    private Short[] multiTitleAlignStyle;
    /**
     * 多行标题字体是否加粗
     */
    private boolean[] multiTitleFontBoldStyle;
    /**
     * 是否需要标题行
     */
    private boolean needTitle;

    /**
     * 标题行高度
     */
    private Integer titleHeight;

    /**
     * 列头
     */
    private String[] columnHeaders;

    /**
     * 列宽
     */
    private Integer[] columnWidths;

    /**
     * 列头行高度
     */
    private Integer columnHeight;

    /**
     * 是否需要列头行
     */
    private boolean needColumnHeaders;

    /**
     * 是否多行列头
     */
    private boolean hasMultiLineColumnHeaders;

    /**
     * 多行列头
     */
    private String[][] multiLineColumnHeaders;

    /**
     * 数据
     */
    private List<ExcelExportObject[]> dataList;
    private List<List<ExcelExportObject>> appendDataList;

    /**
     * 冻结窗格行数(默认或0不冻结)
     */
    private int frozenPaneRowsNum;

    /**
     * 合并单元格List
     */
    private List<CellRangeAddress> mergeCellsList;

    /**
     * 表头字体大小
     */
    private int titleFontSize=11;

    /**
     * 内容字体大小
     */
    private int rowsFontSize=11;
    /** 样式缓存 避免创建太多 导致内存溢出 */
    private Map<String,CellStyle> cellStyleMap;

    /**行高度是否自适应(换行),默认不用*/
    private boolean adaptiveHeight;


    /**
     * 初始化EXCEL poi 公共导出工具类
     *
     * @param title    {@link #title 标题}
     * @param rowNames {@link #columnHeaders 列头}
     * @param dataList {@link #dataList 数据}
     */
    public ExcelExportUtil(String title, String[] rowNames, List<ExcelExportObject[]> dataList) {
        this.dataList = dataList;
        this.columnHeaders = rowNames;
        this.title = title;
        this.frozenPaneRowsNum = 0;
        this.needTitle = true;
        this.needColumnHeaders = true;
        this.hasMultiLineColumnHeaders = false;
        this.adaptiveHeight = false;
        this.mergeCellsList = new ArrayList<>();
        this.cellStyleMap = new HashMap<>();
    }

    /**
     * 初始化EXCEL poi 公共导出工具类 多行表头 无标题
     *
     * @param rowNames {@link #columnHeaders 列头}
     * @param dataList {@link #dataList 数据}
     */
    public ExcelExportUtil(String[][] rowNames, List<ExcelExportObject[]> dataList) {
        this.dataList = dataList;
        this.multiLineColumnHeaders = rowNames;
        this.columnHeaders = rowNames[0];
        this.frozenPaneRowsNum = 0;
        this.needTitle = false;
        this.needColumnHeaders = true;
        this.hasMultiLineColumnHeaders = true;
        this.mergeCellsList = new ArrayList<>();
        this.cellStyleMap = new HashMap<>();
    }

    /**
     * 初始化EXCEL poi 公共导出工具类(多行列头)
     *
     * @param title    {@link #title 标题}
     * @param rowNames {@link #columnHeaders 列头}
     * @param dataList {@link #dataList 数据}
     */
    public ExcelExportUtil(String title, String[][] rowNames, List<ExcelExportObject[]> dataList) {
        this.dataList = dataList;
        this.multiLineColumnHeaders = rowNames;
        this.columnHeaders = rowNames[0];
        this.title = title;
        this.frozenPaneRowsNum = 0;
        this.needTitle = true;
        this.needColumnHeaders = true;
        this.hasMultiLineColumnHeaders = true;
        this.mergeCellsList = new ArrayList<>();
        this.cellStyleMap = new HashMap<>();
    }


    /**
     * 初始化EXCEL poi 公共导出工具类(多行表头 多行列头)
     *
     * @param multiTitle    {@link #multiTitle 多行标题}
     * @param rowNames {@link #columnHeaders 列头}
     * @param dataList {@link #dataList 数据}
     */
    public ExcelExportUtil(String[] multiTitle, String[] rowNames, List<ExcelExportObject[]> dataList) {
        this.dataList = dataList;
        this.columnHeaders = rowNames;
        this.multiTitle=multiTitle;
        this.needMultiTitle=true;
        this.multiTitleAlignStyle=new Short[]{2,2};
        this.multiTitleFontBoldStyle=new boolean[]{true,true};
        this.title = multiTitle[0];
        this.frozenPaneRowsNum = 0;
        this.needTitle = true;
        this.needColumnHeaders = true;
        this.hasMultiLineColumnHeaders = true;
        this.mergeCellsList = new ArrayList<>();
        this.cellStyleMap = new HashMap<>();
    }

    /**
     * 自定义单元格内容样式
     *
     * @param wBook              表格
     * @param align              单元格样式-水平
     * @param vertical           单元格样式-垂直
     * @param wrapText           单元格样式-自动换行
     * @param fontHeightInPoints 单元格样式-字体大小
     * @param boldWeightBold     单元格样式-字体是否加粗
     * @param dataFormat         单元格样式-单元格的数据格式
     * @return {@link CellStyle 单元格样式}
     */
    private static CellStyle customDataLeftStyle(Workbook wBook, short align, short vertical, boolean wrapText,
                                                 short fontHeightInPoints, boolean boldWeightBold,
                                                 short dataFormat) {
        CellStyle style =  wBook.createCellStyle();
        style.setAlignment(align);
        style.setVerticalAlignment(vertical);
        if (wrapText) {
            style.setWrapText(true);
        }
        Font font = wBook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints(fontHeightInPoints);
        if (boldWeightBold) {
            font.setBoldweight(Font.BOLDWEIGHT_BOLD);
        }
        style.setFont(font);
        //统一设置边框线
        style.setBorderBottom((short) 1);
        style.setBorderLeft((short) 1);
        style.setBorderRight((short) 1);
        style.setBorderTop((short) 1);
        style.setDataFormat(dataFormat);
        return style;
    }

    /**
     * 根据导出数据模板生成样式
     *
     * @param wBook             wBook
     * @param excelExportObject 导出数据模板
     * @return 样式
     */
    public CellStyle generateCellStyle(Workbook wBook, ExcelExportObject excelExportObject) {
        return generateCellStyle(
                wBook,
                excelExportObject.getAlignStyleType(),
                excelExportObject.getVerticalStyleType(),
                excelExportObject.isWrapText(),
                (short) this.rowsFontSize,
                excelExportObject.isBoldWeightBold(),
                excelExportObject.getDataFormat()
        );
    }

    /**
     * 生成样式
     *
     * @param wBook              wBook
     * @param align              水平样式
     * @param vertical           垂直
     * @param wrapText           是否自动换行
     * @param fontHeightInPoints 字体大小
     * @param boldWeightBold     是否加粗
     * @param dataFormat         单元格的数据格式
     * @return 样式
     */
    private CellStyle generateCellStyle(Workbook wBook, short align, short vertical, boolean wrapText,
                                        short fontHeightInPoints, boolean boldWeightBold,
                                        short dataFormat) {
        StringBuffer keyBuffer = new StringBuffer(wBook.toString());
        keyBuffer.append(";").append(align).append(";").append(vertical).append(";").append(wrapText ? 1 : 0)
                .append(";").append(fontHeightInPoints).append(";").append(boldWeightBold ? 1 : 0)
                .append(";").append(dataFormat);
        if (this.cellStyleMap.containsKey(keyBuffer.toString())) {
            return this.cellStyleMap.get(keyBuffer.toString());
        }
        CellStyle style = customDataLeftStyle(wBook, align, vertical, wrapText, fontHeightInPoints, boldWeightBold, dataFormat);
        this.cellStyleMap.put(keyBuffer.toString(), style);
        return style;
    }


    /**
     * 自定义单元格边框线大小样式
     *
     * @param wBook 表格
     * @param cell  cell
     * @return 单元格边框线样式
     */
    public static CellStyle customBorderSizeStyle(Workbook wBook, Cell cell) {
        CellStyle style;
        if (null != cell) {
            style = cell.getCellStyle();
        } else {
            style = wBook.createCellStyle();
        }
        style.setBorderBottom((short) 1);
        style.setBorderLeft((short) 1);
        style.setBorderRight((short) 1);
        style.setBorderTop((short) 1);
        return style;
    }

    /**
     * <p>方法描述：删除对应sheet中的行 </p>
     * <p style="color:red;">注意：只支持xlsx格式</p>
     * @param workbook                         准备操作的excel工作簿
     * @param sheetIndexWithRemoveIndexListMap 准备处理的sheet下标以及行Map key sheet下标 value 准备删除的行下标集合
     * @MethodAuthor pw, 2022年05月27日
     */
    public static Workbook removeSheetRows(Workbook workbook,
                                           Map<Integer, List<Integer>> sheetIndexWithRemoveIndexListMap) {
        if (CollectionUtils.isEmpty(sheetIndexWithRemoveIndexListMap) || null == workbook) {
            return workbook;
        }
        int sheetCount = workbook.getNumberOfSheets();
        for (Map.Entry<Integer, List<Integer>> mapEntity : sheetIndexWithRemoveIndexListMap.entrySet()) {
            Integer sheetIndex = mapEntity.getKey();
            List<Integer> removeRowIndexList = mapEntity.getValue();
            //无sheet下标 或者 没有处理行下标 或者sheet下标大于等于sheet个数
            if (null == sheetIndex || CollectionUtils.isEmpty(removeRowIndexList) || sheetIndex >= sheetCount) {
                continue;
            }
            //排序
            Collections.sort(removeRowIndexList, new Comparator<Integer>() {
                @Override
                public int compare(Integer o1, Integer o2) {
                    return o1.compareTo(o2);
                }
            });
            //已经处理行数
            int count = 0;
            List<Integer> executeRemoveIndexList = new ArrayList<>();
            for (Integer removeIndex : removeRowIndexList) {
                //过滤掉可能重复的下标
                if (!executeRemoveIndexList.contains(removeIndex)) {
                    executeRemoveIndexList.add(removeIndex);
                }
            }
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            List<Row> rowList = new ArrayList<>();
            for (Integer removeIndex : executeRemoveIndexList) {
                rowList.add(sheet.getRow(removeIndex));
            }
            //删除行 但会留下空行 去掉空行 需要执行下边的转移行
            //注意 一定要执行删除行 否则在数据不全的时候 会有异常
            //（第二行只有第一列有数据，第三行全数据，仅留下第二行时从第二列开始会出现第三行的数据）
            for (Row removeRow : rowList) {
                if (null != removeRow) {
                    sheet.removeRow(removeRow);
                }
            }
            //注意 转移行的时候 从最小下标开始 每转移一行 后边准备转移的下标都需要减去1
            for (Integer removeIndex : executeRemoveIndexList) {
                //https://github.com/apache/poi/blob/REL_3_8_FINAL/src/java/org/apache/poi/ss/usermodel/Sheet.java
                //int startRow 开始移动的行下标（移动时不包含这一行，会将后边移动的行覆盖这一行的内容）
                //int endRow  结束移动的行下标
                //int n 移动行数
                //boolean copyRowHeight 是否在移动的时候 复制行高
                //boolean resetOriginalRowHeight 是否设置原始行高为默认值
                int startRowIndex = removeIndex - count;
                //无操作的行 退出处理
                if (startRowIndex > sheet.getLastRowNum()) {
                    break;
                }
                sheet.shiftRows(startRowIndex, sheet.getLastRowNum(), -1, true, true);
                count++;
            }
        }
        return workbook;
    }
    /**
     *  <p>方法描述：  * <p>方法描述：删除对应sheet中的行 </p>
     *      *
     *      * @param workbook                         准备操作的excel工作簿
     *      * @param sheetIndexWithRemoveIndexListMap 准备处理的sheet下标以及行Map key sheet下标 value 准备删除的行下标集合
     *      固定标题行 从下往上平移</p>
     * @MethodAuthor hsj 2023-05-20 19:20
     */
    public static Workbook removeNextSheetRows(Workbook workbook,
                                           Map<Integer, List<Integer>> sheetIndexWithRemoveIndexListMap) {
        if (CollectionUtils.isEmpty(sheetIndexWithRemoveIndexListMap) || null == workbook) {
            return workbook;
        }
        int sheetCount = workbook.getNumberOfSheets();
        for (Map.Entry<Integer, List<Integer>> mapEntity : sheetIndexWithRemoveIndexListMap.entrySet()) {
            Integer sheetIndex = mapEntity.getKey();
            List<Integer> removeRowIndexList = mapEntity.getValue();
            //无sheet下标 或者 没有处理行下标 或者sheet下标大于等于sheet个数
            if (null == sheetIndex || CollectionUtils.isEmpty(removeRowIndexList) || sheetIndex >= sheetCount) {
                continue;
            }
            //排序
            Collections.sort(removeRowIndexList, new Comparator<Integer>() {
                @Override
                public int compare(Integer o1, Integer o2) {
                    return o1.compareTo(o2);
                }
            });
            //已经处理行数
            List<Integer> executeRemoveIndexList = new ArrayList<>();
            for (Integer removeIndex : removeRowIndexList) {
                //过滤掉可能重复的下标
                if (!executeRemoveIndexList.contains(removeIndex)) {
                    executeRemoveIndexList.add(removeIndex);
                }
            }
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            int count = sheet.getLastRowNum();
            List<Row> rowList = new ArrayList<>();
            for (Integer removeIndex : executeRemoveIndexList) {
                rowList.add(sheet.getRow(removeIndex));
            }
            //删除行 但会留下空行 去掉空行 需要执行下边的转移行
            //注意 一定要执行删除行 否则在数据不全的时候 会有异常
            //（第二行只有第一列有数据，第三行全数据，仅留下第二行时从第二列开始会出现第三行的数据）
            for (Row removeRow : rowList) {
                if (null != removeRow) {
                    sheet.removeRow(removeRow);
                }
            }
            //倒叙向上平移
            Collections.reverse(executeRemoveIndexList);//倒叙(从大到小)
            for(int i = 0;i<executeRemoveIndexList.size();i++){
                int star = executeRemoveIndexList.get(i)+1;
                if(star > count){
                    continue;
                }
                //最后一个空格,整体往上移动
                sheet.shiftRows(star, count, -1, true, true);
            }
        }
        return workbook;
    }

    public static void main(String[] args) {
        File file = new File("E:\\logs\\a.xlsx");
        Workbook wb = null;
        try {
            InputStream inp = new FileInputStream(file);
            wb = WorkbookFactory.create(inp);
            if (null != inp) {
                inp.close();
            }
            Map<Integer, List<Integer>> sheetIndexWithRemoveIndexListMap = new HashMap<>();
            List<Integer> remove1 = new ArrayList<>();
            remove1.add(5);
            remove1.add(9);
            remove1.add(16);
            List<Integer> remove2 = new ArrayList<>();
            remove2.add(5);
            remove2.add(7);
            remove2.add(8);
            remove2.add(9);
            sheetIndexWithRemoveIndexListMap.put(0, remove1);
            sheetIndexWithRemoveIndexListMap.put(1, remove2);
            removeSheetRows(wb, sheetIndexWithRemoveIndexListMap);
            OutputStream outputStream = new FileOutputStream("E:\\logs\\aaa.xlsx");
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 自定义单元格高、宽、边框线样式
     *
     * @param wBook     表格
     * @param sheet     sheet
     * @param totalRows 行数
     * @param columnNum 列数
     */
    public void customBorderLineStyle(Workbook wBook, Sheet sheet, Integer totalRows, int columnNum) {
        CellStyle borderStyle = customBorderSizeStyle(wBook, null);
        int titleLine = this.needTitle ? 1 : 0;
        int headerLine = titleLine + 1;
        boolean isMultiLineColumnHeaders = this.hasMultiLineColumnHeaders && ObjectUtil.isNotEmpty(this.multiLineColumnHeaders);
        if (isMultiLineColumnHeaders) {
            headerLine = titleLine + this.multiLineColumnHeaders.length;
        }
        for (int i = 0; i < totalRows; i++) {
            Row row = sheet.getRow(i);
            if (ObjectUtil.isEmpty(row)) {
                continue;
            }
            if (this.needTitle && i < titleLine && ObjectUtil.isNotEmpty(this.titleHeight)) {
                //标题行
                row.setHeightInPoints(this.titleHeight);
            } else if (this.needColumnHeaders && i < headerLine && ObjectUtil.isNotEmpty(this.columnHeight)) {
                //列头行
                row.setHeightInPoints(this.columnHeight);
            }  else {
                //其他行默认高度20
                if(!this.adaptiveHeight){
                    row.setHeightInPoints(20);
                }
            }
            for (int j = 0; j < columnNum; j++) {
                Cell cell = row.getCell(j);
                if (null == cell) {
                    cell = row.createCell(j);
                    cell.setCellStyle(borderStyle);
                }
            }
        }
        boolean columnWidthsNotEmpty = ObjectUtil.isNotEmpty(this.columnWidths);
        //列宽自动适应
        for (int colNum = 0; colNum < columnNum; colNum++) {
            int columnWidth;
            if (columnWidthsNotEmpty && this.columnWidths.length > colNum && ObjectUtil.isNotEmpty(this.columnWidths[colNum])) {
                columnWidth = this.columnWidths[colNum];
            } else {
                columnWidth = sheet.getColumnWidth(colNum) / 256;
                for (int rowNum = needTitle ? 1 : 0; rowNum <= sheet.getLastRowNum(); rowNum++) {
                    Row currentRow;
                    //当前行未被使用过
                    if (sheet.getRow(rowNum) == null) {
                        currentRow = sheet.createRow(rowNum);
                    } else {
                        currentRow = sheet.getRow(rowNum);
                    }
                    if (currentRow.getCell(colNum) != null) {
                        Cell cell = currentRow.getCell(colNum);
                        int length = cell.getStringCellValue().getBytes().length;
                        if (columnWidth < length) {
                            columnWidth = length;
                        }
                    }
                    if (columnWidth >= 50) {
                        break;
                    }
                }
                //设置列最大宽度为50
                columnWidth = Math.min(columnWidth, 50);
            }
            sheet.setColumnWidth(colNum, (columnWidth + 4) * 256);
        }
    }

    /**
     * 添加合并单元格
     *
     * @param firstRow 起始行
     * @param lastRow  结束行
     * @param firstCol 起始列
     * @param lastCol  结束列
     */
    public void addMergeCells(int firstRow, int lastRow, int firstCol, int lastCol) {
        this.mergeCellsList.add(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
    }

    /**
     * 生成excel文件(xlsx)
     *
     * @return {@link Workbook excel文件Workbook}
     */
    public Workbook exportExcel() {
        return exportExcel("2007");
    }

    /**
     * 生成excel文件
     *
     * @param version 生成EXCEL版本, 2003: 生成2003xls; 其他生成2007xlsx
     * @return {@link Workbook excel文件Workbook}
     */
    public Workbook exportExcel(String version) {
        Workbook wBook;
        if (StringUtils.isNotBlank(version) && "2003".equals(version)) {
            wBook = new HSSFWorkbook();
        } else {
            wBook = new XSSFWorkbook();
        }
        // 定义所需列数
        int columnNum = this.columnHeaders.length;
        try {
            //sheet
            Sheet sheet1 = wBook.createSheet(ObjectUtil.isEmpty(this.sheetName) ? this.title : this.sheetName);

            if (this.frozenPaneRowsNum > 0) {
                sheet1.createFreezePane(0, this.frozenPaneRowsNum);
            }

            //行
            int line = 0;

            if (this.needTitle) {
                if(needMultiTitle){
                    for (int i = 0; i < multiTitle.length; i++) {
                        //标题
                        Row titleRow = sheet1.createRow(line++);
                        //创建标题单元格
                        Cell titleCell = titleRow.createCell(0);
                        //设置标题内容样式
                        titleCell.setCellStyle(generateCellStyle(wBook, this.multiTitleAlignStyle[i], CellStyle.VERTICAL_CENTER, true, (short) 11, this.multiTitleFontBoldStyle[i], (short) 0));
                        titleCell.setCellValue(multiTitle[i]);
                        //设置标题单元格区域
                        CellRangeAddress region = new CellRangeAddress(i, i, 0, columnNum - 1);
                        sheet1.addMergedRegion(region);
                    }
                }else{
                    //标题
                    Row titleRow = sheet1.createRow(line++);
                    //创建标题单元格
                    Cell titleCell = titleRow.createCell(0);
                    //设置标题内容样式
                    titleCell.setCellStyle(generateCellStyle(wBook, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, true, (short) 11, true, (short) 0));
                    titleCell.setCellValue(this.title);
                    //设置标题单元格区域
                    CellRangeAddress region = new CellRangeAddress(0, 0, 0, columnNum - 1);
                    sheet1.addMergedRegion(region);
                }
            }

            if (this.needColumnHeaders) {
                //列头
                int headerLine = 1;
                boolean isMultiLineColumnHeaders = this.hasMultiLineColumnHeaders && ObjectUtil.isNotEmpty(this.multiLineColumnHeaders);
                if (isMultiLineColumnHeaders) {
                    headerLine = this.multiLineColumnHeaders.length;
                }
                for (int i = 0; i < headerLine; i++) {
                    Row titleRow1 = sheet1.createRow(line++);
                    for (int n = 0; n < columnNum; n++) {
                        //创建列头单元格
                        Cell cell = titleRow1.createCell(n);
                        //设置列头单元格内容样式
                        cell.setCellStyle(generateCellStyle(wBook, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, true, (short) titleFontSize, true, (short) 0));
                        //此处缺少设置列头单元格数据类型操作
                        //设置列头单元格值
                        if (isMultiLineColumnHeaders) {
                            cell.setCellValue(this.multiLineColumnHeaders[i][n]);
                        } else {
                            cell.setCellValue(this.columnHeaders[n]);
                        }
                    }
                }
            }

            //数据列数与表头列数不一致跳过
            for (ExcelExportObject[] obj : this.dataList) {
                //数据
                if (obj.length != columnNum) {
                    continue;
                }
                //创建所需的行数
                Row row = sheet1.createRow(line++);
                for (int j = 0; j < obj.length; j++) {
                    Cell cell = row.createCell(j);
                    //设置单元格内容样式
                    cell.setCellStyle(generateCellStyle(wBook, obj[j].getAlignStyleType(), obj[j].getVerticalStyleType(), obj[j].isWrapText(), (short) rowsFontSize, obj[j].isBoldWeightBold(), obj[j].getDataFormat()));
                    //此处缺少设置列头单元格数据类型操作
                    //设置单元格的值
                    cell.setCellValue(StringUtils.objectToString(obj[j].getObject()));
                }
            }

            appendData(wBook, sheet1);

            for (CellRangeAddress cellRangeAddress : this.mergeCellsList) {
                sheet1.addMergedRegion(cellRangeAddress);
            }

            //设置单元格高、宽、边框线样式
            sheet1.autoSizeColumn(columnNum, true);
            int totalRows = line;
            customBorderLineStyle(wBook, sheet1, totalRows, columnNum);

            return wBook;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 追加行
     *
     * @param wBook wBook
     * @param sheet1 sheet1
     */
    public void appendData(Workbook wBook, Sheet sheet1) {
        if (ObjectUtil.isEmpty(this.appendDataList)) {
            return;
        }
        for (List<ExcelExportObject> exportObjectList : this.appendDataList) {
            Row row = sheet1.createRow(sheet1.getLastRowNum() + 1);
            for (int i = 0, exportObjectListSize = exportObjectList.size(); i < exportObjectListSize; i++) {
                ExcelExportObject excelExportObject = exportObjectList.get(i);
                Cell cell = row.createCell(i);
                cell.setCellStyle(generateCellStyle(wBook, excelExportObject));
                cell.setCellValue(StringUtils.objectToString(excelExportObject.getObject()));
            }
        }
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public boolean isNeedTitle() {
        return needTitle;
    }

    public void setNeedTitle(boolean needTitle) {
        this.needTitle = needTitle;
    }

    public Integer getTitleHeight() {
        return titleHeight;
    }

    public void setTitleHeight(Integer titleHeight) {
        this.titleHeight = titleHeight;
    }

    public String[] getColumnHeaders() {
        return columnHeaders;
    }

    public void setColumnHeaders(String[] columnHeaders) {
        this.columnHeaders = columnHeaders;
    }

    public Integer[] getColumnWidths() {
        return columnWidths;
    }

    public void setColumnWidths(Integer[] columnWidths) {
        this.columnWidths = columnWidths;
    }

    public Integer getColumnHeight() {
        return columnHeight;
    }

    public void setColumnHeight(Integer columnHeight) {
        this.columnHeight = columnHeight;
    }

    public boolean isNeedColumnHeaders() {
        return needColumnHeaders;
    }

    public void setNeedColumnHeaders(boolean needColumnHeaders) {
        this.needColumnHeaders = needColumnHeaders;
    }

    public boolean isHasMultiLineColumnHeaders() {
        return hasMultiLineColumnHeaders;
    }

    public void setHasMultiLineColumnHeaders(boolean hasMultiLineColumnHeaders) {
        this.hasMultiLineColumnHeaders = hasMultiLineColumnHeaders;
    }

    public String[][] getMultiLineColumnHeaders() {
        return multiLineColumnHeaders;
    }

    public void setMultiLineColumnHeaders(String[][] multiLineColumnHeaders) {
        this.multiLineColumnHeaders = multiLineColumnHeaders;
    }

    public List<ExcelExportObject[]> getDataList() {
        return dataList;
    }

    public void setDataList(List<ExcelExportObject[]> dataList) {
        this.dataList = dataList;
    }

    public List<List<ExcelExportObject>> getAppendDataList() {
        return appendDataList;
    }

    public void setAppendDataList(List<List<ExcelExportObject>> appendDataList) {
        this.appendDataList = appendDataList;
    }

    public int getFrozenPaneRowsNum() {
        return frozenPaneRowsNum;
    }

    public void setFrozenPaneRowsNum(int frozenPaneRowsNum) {
        this.frozenPaneRowsNum = frozenPaneRowsNum;
    }

    public int getTitleFontSize() {
        return titleFontSize;
    }

    public void setTitleFontSize(int titleFontSize) {
        this.titleFontSize = titleFontSize;
    }

    public int getRowsFontSize() {
        return rowsFontSize;
    }

    public void setRowsFontSize(int rowsFontSize) {
        this.rowsFontSize = rowsFontSize;
    }

    public boolean isAdaptiveHeight() {
        return adaptiveHeight;
    }

    public void setAdaptiveHeight(boolean adaptiveHeight) {
        this.adaptiveHeight = adaptiveHeight;
    }

    public String[] getMultiTitle() {
        return multiTitle;
    }
    public void setMultiTitle(String[] multiTitle) {
        this.multiTitle = multiTitle;
    }

    public boolean isNeedMultiTitle() {
        return needMultiTitle;
    }
    public void setNeedMultiTitle(boolean needMultiTitle) {
        this.needMultiTitle = needMultiTitle;
    }

    public Short[] getMultiTitleAlignStyle() {
        return multiTitleAlignStyle;
    }
    public void setMultiTitleAlignStyle(Short[] multiTitleAlignStyle) {
        this.multiTitleAlignStyle = multiTitleAlignStyle;
    }
    public boolean[] getMultiTitleFontBoldStyle() {
        return multiTitleFontBoldStyle;
    }
    public void setMultiTitleFontBoldStyle(boolean[] multiTitleFontBoldStyle) {
        this.multiTitleFontBoldStyle = multiTitleFontBoldStyle;
    }
}
