package com.chis.common.utils;

import sun.misc.BASE64Decoder;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Map;

public class RSAUtil {
 
	public static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
	public static final String ENCODE_ALGORITHM = "SHA-256";
	public static final String PLAIN_TEXT = "hello world";
 
	public static void main(String[] args) throws UnsupportedEncodingException, SignatureException {
		// 公私钥对
		Map<String, byte[]> keyMap = RSA.generateKeyBytes();
//		PublicKey publicKey = RSA.restorePublicKey(keyMap.get(RSA.PUBLIC_KEY));
//		PrivateKey privateKey = RSA.restorePrivateKey(keyMap.get(RSA.PRIVATE_KEY));
		
		
		PublicKey publicKey = getPubKey();
		PrivateKey privateKey = getPrivateKey("MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCsI+RvdiuhHoGhBQGwtSGNe8PVrthkDinP6c6tCpoFpyJusWj2w6l1r/HreC67HIQKrdxd09U7gu/61dzn3oJ1QJH59N6DQcdLOARCatgA1vy+Ymv/0it0izi1IyZZ3zYyG8Nxuu+ar2wSlfD3P4g42pj9iEol6En6iYUNxrfmKwawSDJhR4AwYBRkbhIz+hXeT35mu2WluBu5TrQYj1aTCSbiqqlHKt8Q37xMK7B+q7OZcDmuYgRp1RrS780H7jqsWPT4iPOeokrSo0igKvFOSF822aG/1U0ZZEyr1pVjWLfBqOAbjUeO0h4X6ze6dLJEIc8oDtr8c+5NYB21VOzBAgMBAAECggEAKA72MuYKAyWJPwJeQVP1aepaSVi17JrLKiIbwXMNIrGhNqc8aOhhgAygvI4sOcjKBX4uIPuRzJaM6A9qp97E9yo+ji9otJzTFvMDMr2QZKmjCV74CQFMsYNQf/6dBx8FiqWhVs8MwS9/c9fGypLTTX+5SI61ypqC1LW569nJo9sPps9gAeAFKqUlmPLe2uXCxo/EUg4kwao8C2mprpBg/gF3WEaT9gBafJezl4jDmfOzP8UdzYGfXfyZzNXfMz+XI3FV7aZnEl4QFxkANQyTp6r5+P6nttiwwT8dHxzONToZvSKUV1a1SvUNZ3nCnFK05ui69jGeniQbed/EuD4tEQKBgQDXzPhalFQHxX1UQc23Uu+tomvuYU6tW0psgDeKoUXOrzMzh5pgX7u4MEu1Dk2Pb4sNmtBySzl/q4cv31Mji+OX52huOjjlEFrUscwHiwIqe3tmPQkDTB6rTrPlw9d09GZzJP0fN7HWs6ZYmvE2+lZsK42qnVKfZ+e2jrcoQZHUNQKBgQDMNNtV/e53rz+9XS8jX1OZQoKcIHEnjNVKjDxByOYQ0mvTMtAGWv/A0hJ49h6IrrjCAJCtx0/ZiPFhnqoj85w2YgeeCtVkGZlRakKlAfF0pyWkH8pldLJs6uc32hUYCjn6OvBQvuCVXu8ANnk4j9H6iGlohpAJ9ys4GPruO+8v3QKBgQCXNX8b8O0EWQQdYtJjTxC0KKfZ64L2g44dgqXoVGKWlTqaM70fJFVjlSunHPOJus3DZaQDQeRzKdxa1e/qa125FaOeANvW3WPXvxFIg8WrwMRRehjzn4Wp4Ua6i0RQJeQXcGaQVjUdTNoVIdQ9AqJFvyrPztaS7Xp194AtC+zDtQKBgH259WkmZNIvhhDy9HjU0kXEG2JA3yo4nYG+gcMHDA9DXruzxzyCfoCbVmA/OGFzIbHgYzN7SYBnviYaaCqmbIFXLdVyWhu9XxYvVET/w0fNOXGpWRXKGajRn/ZVQTOB6FxWOWudeey9fG0nHM9kTSXxIuUGJFzeXy+0wZo4MHXJAoGAeen7nx3OC3/rgPw4grH9qtG92RiP49bet3abUGvXPHgnTfAcV/F1Yb4aIw2TkoW6zeHjNa4I3WLyozc5ZzNj3thPCva4rpHCEPAk4rQ7MzkKf200m66L1PROqov58rZnHx1S++STA/TL00M3hLXZlWDIwWWmbmjGfGT3mLfa6P8=");
//		String pubKeyStr = new String(Base64.encode(publicKey.getEncoded())); //pkcs8格式
//		String priKeyStr = new String(Base64.encode(privateKey.getEncoded())); //pkcs8格式
//		 System.out.println("pubKeyStr:\r"+pubKeyStr);
//	     System.out.println("priKeyStr:\r"+priKeyStr);
		
		
		// 签名
		String sing_byte = sign(privateKey, PLAIN_TEXT);
		System.out.println("SHA256withRSA签名后-----》" + sing_byte);
		// 验签
//		verifySign(publicKey, PLAIN_TEXT, sing_byte);
	}
 
	/**
	 * 签名
	 * 
	 * @param privateKey
	 *            私钥
	 * @param plain_text
	 *            明文
	 * @return
	 * @throws UnsupportedEncodingException 
	 * @throws SignatureException 
	 */
	public static String sign(PrivateKey privateKey, String plain_text) throws UnsupportedEncodingException, SignatureException {
		Signature sign = null;
		try {
			sign = Signature.getInstance(SIGNATURE_ALGORITHM);  
			sign.initSign(privateKey);  
			sign.update(plain_text.getBytes("utf-8")); 
//			System.out.println("SHA256withRSA签名后-----》" + Base64.getEncoder().encodeToString(sign.sign()));
		} catch (NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
			e.printStackTrace();
		}
		return new sun.misc.BASE64Encoder().encode(sign.sign());
	}
 
	/**
	 * 验签
	 * 
	 * @param publicKey
	 *            公钥
	 * @param plain_text
	 *            明文
	 * @param signed
	 *            签名
	 * @throws UnsupportedEncodingException 
	 */
	public static boolean verifySign(PublicKey publicKey, String plain_text, byte[] signed) throws UnsupportedEncodingException {
		Signature sign = null;  
		boolean SignedSuccess=false;
		try {
			sign = Signature.getInstance(SIGNATURE_ALGORITHM);  
			sign.initVerify(publicKey);  
			sign.update(plain_text.getBytes("utf-8")); 
			SignedSuccess = sign.verify(signed);
			System.out.println("验证成功？---" + SignedSuccess);
			
		} catch (NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
			e.printStackTrace();
		}
		return SignedSuccess;
	}
 
	/**
	 * bytes[]换成16进制字符串
	 * 
	 * @param src
	 * @return
	 */
	public static String bytesToHexString(byte[] src) {
		StringBuilder stringBuilder = new StringBuilder("");
		if (src == null || src.length <= 0) {
			return null;
		}
		for (int i = 0; i < src.length; i++) {
			int v = src[i] & 0xFF;
			String hv = Integer.toHexString(v);
			if (hv.length() < 2) {
				stringBuilder.append(0);
			}
			stringBuilder.append(hv);
		}
		return stringBuilder.toString();
	}
	
	
	

	/**
	  * 实例化公钥
	  * 
	  * @return
	  */
	public static PublicKey getPubKey() {
	  PublicKey publicKey = null;
	  try {

	   // 自己的公钥(测试)
	    String pubKey ="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArCPkb3YroR6BoQUBsLUhjXvD1a7YZA4pz+nOrQqaBacibrFo9sOpda/x63guuxyECq3cXdPVO4Lv+tXc596CdUCR+fTeg0HHSzgEQmrYANb8vmJr/9IrdIs4tSMmWd82MhvDcbrvmq9sEpXw9z+IONqY/YhKJehJ+omFDca35isGsEgyYUeAMGAUZG4SM/oV3k9+ZrtlpbgbuU60GI9Wkwkm4qqpRyrfEN+8TCuwfquzmXA5rmIEadUa0u/NB+46rFj0+IjznqJK0qNIoCrxTkhfNtmhv9VNGWRMq9aVY1i3wajgG41HjtIeF+s3unSyRCHPKA7a/HPuTWAdtVTswQIDAQAB";
	      java.security.spec.X509EncodedKeySpec bobPubKeySpec = new java.security.spec.X509EncodedKeySpec(
	     new BASE64Decoder().decodeBuffer(pubKey));
	   // RSA对称加密算法
	   KeyFactory keyFactory;
	   keyFactory = KeyFactory.getInstance("RSA");
	   // 取公钥匙对象
	   publicKey = keyFactory.generatePublic(bobPubKeySpec);
	  } catch (NoSuchAlgorithmException e) {
	   e.printStackTrace();
	  } catch (InvalidKeySpecException e) {
	   e.printStackTrace();
	  } catch (IOException e) {
	   e.printStackTrace();
	  }
	  return publicKey;
	 }

	 

	 


	 /**
	  * 实例化私钥
	  * 
	  * @return
	  */
	 public static PrivateKey getPrivateKey(String s) {
	  PrivateKey privateKey = null;
	  String priKey = s;
	  PKCS8EncodedKeySpec priPKCS8;
	  try {
	   priPKCS8 = new PKCS8EncodedKeySpec(
	     new BASE64Decoder().decodeBuffer(priKey));
	   KeyFactory keyf = KeyFactory.getInstance("RSA");
	   privateKey = keyf.generatePrivate(priPKCS8);
	  } catch (IOException e) {
	   e.printStackTrace();
	  } catch (NoSuchAlgorithmException e) {
	   e.printStackTrace();
	  } catch (InvalidKeySpecException e) {
	   e.printStackTrace();
	  }
	  return privateKey;
	 }
}
