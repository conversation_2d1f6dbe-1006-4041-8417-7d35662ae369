package com.chis.common.utils;

import com.chis.common.bean.InputFileBean;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.*;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.transfer.TransferManager;
import com.qcloud.cos.transfer.Upload;
import com.qcloud.cos.utils.IOUtils;
import org.apache.commons.fileupload.FileItem;

import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <p>类描述：腾讯云对象存储常用操作工具类</p>
 *
 * @ClassAuthor mxp, 2018/3/13, CosUtil
 */
public class CosUtil {

    // 初始化用户身份信息
    private static String APP_ID;//= PropertyUtils.getValue("cos.app.id");
    private static String SECRET_ID;//= PropertyUtils.getValue("cos.secret.id");
    private static String SECRET_KEY;// = PropertyUtils.getValue("cos.secret.key");
    // 设置bucket的区域
    private static String COS_REGION;// = PropertyUtils.getValue("cos.region");
    private static String BUCKET_NAME = "wxwj-input";// = PropertyUtils.getValue("cos.bucket");
    private static String BUCKET_NAME_DEFAULT;

    private static COSCredentials cred = null;
    private static ClientConfig clientConfig = null;
    public static COSClient cosClient;

    static {
        APP_ID = "1255843943";
        SECRET_ID = "AKID19KcqArDQeicKrsTVSWl4StSyJSSoit3";
        SECRET_KEY = "PwYEsLdmkiidyAA0gBUdcVLbieOwJdsv";
        COS_REGION = "ap-beijing";
        BUCKET_NAME_DEFAULT = BUCKET_NAME + "-" + APP_ID;
        // 1 初始化用户身份信息(secretId, secretKey)
        cred = new BasicCOSCredentials(SECRET_ID, SECRET_KEY);
        // 2 设置bucket的区域, COS地域的简称
        clientConfig = new ClientConfig(new Region(COS_REGION));
        // 3 生成cos客户端
        cosClient = new COSClient(cred, clientConfig);
        // 4 生成默认的存储桶
        createDefaultBucket();
    }

    /**
     * <p>方法描述：生成默认的存储桶</p>
     *
     * @MethodAuthor mxp, 2018/3/14,createDefaultBucket
     */
    public static void createDefaultBucket() {
        if (!cosClient.doesBucketExist(BUCKET_NAME_DEFAULT)) {
            CreateBucketRequest createBucketRequest = new CreateBucketRequest(BUCKET_NAME_DEFAULT);
            createBucketRequest.setCannedAcl(CannedAccessControlList.Private);
            cosClient.createBucket(createBucketRequest);
        }
    }


    /**
     * <p>方法描述：生成cos客户端</p>
     *
     * @MethodAuthor mxp, 2018/3/13,getInstance
     */
    public static COSClient getInstance() {
        if (cosClient == null) {
            synchronized (CosUtil.class) {
                if (cosClient == null) {
                    cosClient = new COSClient(cred, clientConfig);
                }
            }
        }
        return cosClient;
    }

    /**
     * <p>方法描述：存储桶名称由自定义字符串和系统生成数字串（APP_ID）组成，两者由中划线连接。</p>
     *
     * @MethodAuthor mxp, 2018/3/13,dealBucketName
     */
    private static String dealBucketName(String buckName) {
        if(StringUtils.isBlank(buckName)){
            return BUCKET_NAME_DEFAULT;
        }
        return StringUtils.trim(buckName) + "-" + APP_ID;
    }

    /**
     * <p>方法描述：判断存储桶是否存在</p>
     *
     * @MethodAuthor mxp, 2018/3/13,dealBucketName
     */
    public static boolean doesBucketExist(String bucketName) {
        bucketName = dealBucketName(bucketName);
        return cosClient.doesBucketExist(bucketName);
    }

    /**
     * <p>方法描述：创建一个存储桶</p>
     * 存储桶名称中，自定义字符串长度不能超过 40 字符。 仅支持小写字母、数字、中划线及其组合，不支持特殊符号及下划线
     *
     * @MethodAuthor mxp, 2018/3/13,createBucket
     */
    public static boolean createBucket(String bucketName) {
        if (!doesBucketExist(bucketName)) {
            bucketName = dealBucketName(bucketName);
            cosClient.createBucket(bucketName);
            return true;
        }
        return false;
    }

    /**
     * <p>方法描述：删除一个存储桶</p>
     *
     * @MethodAuthor mxp, 2018/3/13,deleteBucket
     */
    public static void deleteBucket(String bucketName) {
        if (doesBucketExist(bucketName)) {
            bucketName = dealBucketName(bucketName);
            ObjectListing objectListing = cosClient.listObjects(bucketName);
            List<COSObjectSummary> objectSummaries = objectListing.getObjectSummaries();
            for (COSObjectSummary objectSummary : objectSummaries) {
                cosClient.deleteObject(bucketName,objectSummary.getKey());
            }
            cosClient.deleteBucket(bucketName);
        }
    }

    /**
     * <p>方法描述：设置存储桶读写权限</p>
     * 私有读写：CannedAccessControlList.Private
     * 公有读写：CannedAccessControlList.PublicReadWrite
     * 公有读私有写：CannedAccessControlList.PublicRead
     *
     * @MethodAuthor mxp, 2018/3/13,deleteBucket
     */
    public static boolean setBucketACL(String bucketName, CannedAccessControlList accessControlList) {
        if (doesBucketExist(bucketName)) {
            bucketName = dealBucketName(bucketName);
            cosClient.setBucketAcl(bucketName, accessControlList);
            return true;
        }
        return false;
    }

    /**
     * <p>方法描述：生成预签名下载链接</p>
     *
     * @MethodAuthor mxp, 2018/3/13,getDownloadUrl
     */
    public static String getDownloadUrl(String bucketName, String key) {
        bucketName = dealBucketName(bucketName);
        // 设置URL过期时间为1小时
        Date expiration = new Date(new Date().getTime() + 3600 * 1000);
        URL presignedUrl = cosClient.generatePresignedUrl(bucketName, key, expiration);
        return presignedUrl.toString();
    }

    /**
     * <p>方法描述：生成预签名链接：浏览器支持的文件类型可直接打开</p>
     *
     * @MethodAuthor mxp, 2018/3/13,getUrl
     */
    public static String getUrl(String bucketName, String key) {
        if(!checkKeyExists(bucketName,key)){
            return null;
        }
        bucketName = dealBucketName(bucketName);
        GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(bucketName, key, HttpMethodName.GET);
        ResponseHeaderOverrides responseHeaders = new ResponseHeaderOverrides();
        String responseContentDisposition = "filename=";
        responseHeaders.setContentDisposition(responseContentDisposition);
        req.setResponseHeaders(responseHeaders);
        URL url = cosClient.generatePresignedUrl(req);
        return url.toString();
    }

    /**
     * <p>方法描述：上传文件</p>
     *
     * @MethodAuthor mxp, 2018/3/13,uploadFile
     */
    public static <T> InputFileBean uploadFile(String bucket, String key, T t) {
        return doUploadFile(bucket, key, t, 1);
    }

    /**
     * <p>方法描述：上传文件:默认存储桶</p>
     *
     * @MethodAuthor mxp, 2018/3/13,uploadFile
     */
    public static <T> InputFileBean uploadFile(String key, T t) {
        return doUploadFile(BUCKET_NAME_DEFAULT, key, t, 1);
    }

    /**
     * <p>方法描述：高级 API 文件上传(推荐)</p>
     *
     * @MethodAuthor mxp, 2018/3/13,upload
     */
    public static <T> InputFileBean upload(String bucket, String key, T t) {
        return doUploadFile(bucket, key, t, 2);
    }

    /**
     * <p>方法描述：高级 API 文件上传(推荐):默认存储桶</p>
     *
     * @MethodAuthor mxp, 2018/3/13,upload
     */
    public static <T> InputFileBean upload(String key, T t) {
        return doUploadFile(BUCKET_NAME_DEFAULT, key, t, 2);
    }

    /**
     * <p>方法描述：判断文件流，执行上传</p>
     *
     * @MethodAuthor mxp, 2018/3/13,doUploadFile
     */
    private static <T> InputFileBean doUploadFile(String bucket, String key, T t, int type) {
        try {
            InputStream inputStream;
            if (t instanceof FileItem) {
                inputStream = ((FileItem) t).getInputStream();
            } else if (t instanceof InputStream) {
                inputStream = (InputStream) t;
            } else if (t instanceof File) {
                inputStream = new FileInputStream((File) t);
            } else {
                return null;
            }
            bucket = dealBucketName(bucket);
            if (type == 1) {
                return uploadFile(bucket, key, inputStream);
            } else {
                return doUpload(bucket, key, inputStream);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * <p>方法描述：</p>
     *
     * @MethodAuthor mxp, 2018/3/13,uploadFile
     */
    private static InputFileBean uploadFile(String bucket, String key, InputStream inputStream) throws Exception {
        double size = new BigDecimal(inputStream.available()).divide(
                new BigDecimal(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP)
                .doubleValue();
        if (size >= 100) {
            multipartUpload(bucket, key, inputStream);
        } else {
            // 创建上传Object的Metadata
            ObjectMetadata meta = new ObjectMetadata();
            // 设置文件编码
            meta.setContentEncoding("utf-8");
            // 必须设置ContentLength
            meta.setContentLength(inputStream.available());
            // 上传Object.
            PutObjectRequest re = new PutObjectRequest(bucket, key, inputStream, meta);
            PutObjectResult result = cosClient.putObject(re);
            String eTag = result.getETag();
            System.out.println(eTag);
            inputStream.close();
        }
        return initCOSFile(bucket, key);

    }

    /**
     * <p>方法描述：高级 API 文件上传(推荐)</p>
     * 上传接口根据用户文件的长度自动选择简单上传以及分块上传， 降低用户的使用门槛。用户不用关心分块上传的每个步骤。
     *
     * @MethodAuthor mxp, 2018/3/13,uploadFile
     */
    private static InputFileBean doUpload(String bucket, String key, InputStream inputStream) throws Exception {
        double size = new BigDecimal(inputStream.available()).divide(
                new BigDecimal(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP)
                .doubleValue();
        // 传入一个threadPool, 若不传入线程池, 默认TransferManager中会生成一个单线程的线程池。
        TransferManager transferManager;
        if (size >= 100) {
            transferManager = new TransferManager(cosClient, Executors.newFixedThreadPool(5));
        }else{
            transferManager = new TransferManager(cosClient);
        }
        // 提交上传请求
        ObjectMetadata objectMetadata = new ObjectMetadata();
        // 设置文件编码
//        objectMetadata.setContentEncoding("utf-8");
        // 设置输入流长度
        objectMetadata.setContentLength(inputStream.available());
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, key, inputStream, objectMetadata);
        // 本地文件上传
        Upload upload = transferManager.upload(putObjectRequest);
        // 等待传输结束（如果想同步的等待上传结束，则调用 waitForCompletion）
        UploadResult uploadResult = upload.waitForUploadResult();
        // 关闭 TransferManger
        transferManager.shutdownNow();
        return initCOSFile(bucket, key);
    }

    /**
     * <p>方法描述：</p>
     *
     * @MethodAuthor mxp, 2018/3/14,initCOSFile
     */
    private static InputFileBean initCOSFile(String bucketName, String key) {
        try {
            String encodedObjectName = URLEncoder.encode(key, "utf-8");
            return new InputFileBean(COS_REGION, bucketName, encodedObjectName);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * <p>方法描述：分块上传 适合大文件上传</p>
     *
     * @MethodAuthor mxp, 2018/3/13,multipartUpload
     */
    private static void multipartUpload(String bucket, String key, InputStream inputStream) throws Exception {
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        // 初始化分块
        InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(bucket, key);
        InitiateMultipartUploadResult initResponse = cosClient.initiateMultipartUpload(initRequest);
        String uploadId = initResponse.getUploadId();
        // 上传分块, 最多 1000 个分块, 分块大小支持为 1M * 5G.
        // 分块大小设置为 5M. 如果总计 n 个分块, 则 1~n-1 的分块大小一致, 最后一块小于等于前面的分块大小
        List<PartETag> partETags = Collections.synchronizedList(new ArrayList<PartETag>());
        int partSize = 5 * 1024 * 1024;
        int fileLength = inputStream.available();
        int partCount = fileLength / partSize;
        if (fileLength % partSize != 0) {
            partCount++;
        }
        if (partCount > 10000) {
            throw new RuntimeException("Total parts count should not exceed 10000");
        } else {
            System.out.println("Total parts count " + partCount + "\n");
        }
        // partStream 代表 part 数据的输入流, 流长度为 partSize
        byte[] bytes = IOUtils.toByteArray(inputStream);
        for (int i = 0; i < partCount; i++) {
            long startPos = i * partSize;
            long curPartSize = (i + 1 == partCount) ? (fileLength - startPos) : partSize;
            byte[] dest = new byte[(int) curPartSize];
            System.arraycopy(bytes, (int) startPos, dest, 0, (int) curPartSize);
            executorService.execute(new PartUploaderCOS(new ByteArrayInputStream(dest), startPos,
                    curPartSize, i + 1, uploadId, bucket, key, cosClient, partETags));
        }
        inputStream.close();
        executorService.shutdown();
        while (!executorService.isTerminated()) {
            try {
                executorService.awaitTermination(5, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        if (partETags.size() != partCount) {
            throw new IllegalStateException("Upload multiparts fail due to some parts are not finished yet");
        }
        // complete 完成分块上传.
        CompleteMultipartUploadRequest compRequest = new CompleteMultipartUploadRequest(bucket, key, uploadId, partETags);
        CompleteMultipartUploadResult completeResult = cosClient.completeMultipartUpload(compRequest);

    }

    /**
     * <p>方法描述：下载文件</p>
     *
     * @MethodAuthor mxp, 2018/3/13,downloadFile
     */
    public static COSObject downloadFile(String bucket, String key) {
        bucket = dealBucketName(bucket);
        return cosClient.getObject(bucket, key);
    }

    /**
     * <p>方法描述：删除文件</p>
     *
     * @MethodAuthor mxp, 2018/3/14,deleteFile
     */
    public static void deleteFile(String bucket, String key) {
        if(doesBucketExist(bucket)){
            bucket = dealBucketName(bucket);
            cosClient.deleteObject(bucket, key);
        }

    }

    /**
     * <p>方法描述：判断文件是否存在</p>
     *
     * @MethodAuthor mxp, 2018/3/14,checkKeyExists
     */
    public static boolean checkKeyExists(String bucket, String key) {
        bucket = dealBucketName(bucket);
        return cosClient.doesObjectExist(bucket, key);
    }

    /**
     * <p>方法描述：获取文件的头部</p>
     *
     * @MethodAuthor mxp, 2018/3/14,getRequestContentType
     */
    public static String getRequestContentType(String bucket, String key) {
        ObjectMetadata obj = null;
        if (checkKeyExists(bucket, key)) {
            bucket = dealBucketName(bucket);
            obj = cosClient.getObjectMetadata(bucket, key);
        }
        if (obj == null) {
            return null;
        }
        return obj.getContentType();
    }

    /**
     * 缩放图片
     *
     * @param m      指定缩略的模式 必填
     *               lfit：等比缩放，限制在设定在指定w与h的矩形内的最大图片。
     *               mfit：等比缩放，延伸出指定w与h的矩形框外的最小图片。
     *               fill：固定宽高，将延伸出指定w与h的矩形框外的最小图片进行居中裁剪。
     *               pad：固定宽高，缩略填充
     *               fixed：固定宽高，强制缩略
     * @param width  宽度 1-4096 必填
     * @param height 高度 1-4096 必填
     * @param limit  指定当目标缩略图大于原图时是否处理。值是 1 表示不处理；值是 0 表示处理 ，默认是 1
     * @param color  当缩放模式选择为pad（缩略填充）时， 如00FF00（绿色）
     */
    public static String getCompressImgUrl(String bucketName, String key, String m, Integer width, Integer height, Integer limit, String color) {
        //腾讯云图片缩略图需要开通万象图片，但这个服务超出免费流量后就要收费，所以直接获取图片，不处理。
        return getUrl(bucketName,key);
    }

    public static void main(String[] args) throws Exception {
        long l = System.currentTimeMillis();
        //测试删除
        String bucket = "wxwj-input";
//        deleteBucket(bucket);
//        System.out.println(doesBucketExist("wxwj-input"));
        File file = new File("F:\\03_接收文件\\pdftest.pdf");
        InputStream fileInputStream = new FileInputStream(file);
//        FileInputStream fileInputStream = new FileInputStream("F:\\03_接收文件\\text.txt");
        upload(bucket, "/pdf/pdftest.pdf", fileInputStream);

        long l1 = System.currentTimeMillis();
        System.out.println(l1 - l);
        String url = getUrl(bucket, "pdftest.pdf");
        System.out.println(url);
    }


}
