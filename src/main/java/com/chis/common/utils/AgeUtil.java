package com.chis.common.utils;

/**
 * 年龄工具类
 * 
 * <AUTHOR>
 * @createDate 2016年9月5日
 */
public class AgeUtil {
	/**
	 * 将年龄字符串替换成码表codeName codeNo-6003
	 * 
	 * @param ageStr
	 * @return
	 */
	public static String convertAgeStringToCodeName(String ageStr) {
		if("不详".equals(ageStr)){
			return "不详";
		}
		String age = "";
		for (int i = 0; i < ageStr.length(); i++) {
			if (ageStr.charAt(i) >= 48 && ageStr.charAt(i) <= 57) {
				age += ageStr.charAt(i);
			}
		}
		
		if (StringUtils.isNotBlank(age)) {
			Integer ageInt = new Integer(age);
			if (ageInt >= 85) {
				return "85及以上";
			} else if (ageInt >= 80) {
				return "80~";
			} else if (ageInt >= 75) {
				return "75~";
			} else if (ageInt >= 70) {
				return "70~";
			} else if (ageInt >= 65) {
				return "65~";
			} else if (ageInt >= 60) {
				return "60~";
			} else if (ageInt >= 55) {
				return "55~";
			} else if (ageInt >= 50) {
				return "50~";
			} else if (ageInt >= 45) {
				return "45~";
			} else if (ageInt >= 40) {
				return "40~";
			} else if (ageInt >= 35) {
				return "35~";
			} else if (ageInt >= 30) {
				return "30~";
			} else if (ageInt >= 25) {
				return "25~";
			} else if (ageInt >= 20) {
				return "20~";
			} else if (ageInt >= 15) {
				return "15~";
			} else if (ageInt >= 10) {
				return "10~";
			} else if (ageInt == 9) {
				return "9~";
			} else if (ageInt == 8) {
				return "8~";
			} else if (ageInt == 7) {
				return "7~";
			} else if (ageInt == 6) {
				return "6~";
			} else if (ageInt == 5) {
				return "5~";
			} else if (ageInt == 4) {
				return "4~";
			} else if (ageInt == 3) {
				return "3~";
			} else if (ageInt == 2) {
				return "2~";
			} else if (ageInt == 1) {
				return "1~";
			} else if (ageInt == 0) {
				return "0~";
			}
		}
		return null;
	}
}
