package com.chis.common.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.StringWriter;

/**
 * 关于16进制的工具类，
 * 包含文件流转16进制字符串、根据16进制字符串写文件
 * <AUTHOR> 2015-02-06
 */
public class HexUtil {

	/**
	 * 将文件转换为16进制字符串
	 * @param fis 文件输入流
	 * @return 16进制字符串
	 * @throws IOException IO异常
	 */
	public static String file2HexStr(FileInputStream fis) throws IOException {
		StringWriter sw = new StringWriter();
		int len = 1;
		byte[] temp = new byte[len];

		/* 16进制转化模块 */
		for (; (fis.read(temp, 0, len)) != -1;) {
			if (temp[0] > 0xf && temp[0] <= 0xff) {
				sw.write(Integer.toHexString(temp[0]));
			} else if (temp[0] >= 0x0 && temp[0] <= 0xf) {// 对于只有1位的16进制数前边补“0”
				sw.write("0" + Integer.toHexString(temp[0]));
			} else { // 对于int<0的位转化为16进制的特殊处理，因为Java没有Unsigned
				// int，所以这个int可能为负数
				sw.write(Integer.toHexString(temp[0]).substring(6));
			}
		}
		return sw.toString();
	}
	
	/**
	 * 根据16进制字符串，写文件
	 * @param sos 输出流
	 * @param replaced 16进制字符串
	 */
	public static void writeNew2Binary(OutputStream sos, String replaced) {
		try {
			for (int i = 0; i < replaced.length(); i = i + 2) {
				sos.write(Integer.parseInt(replaced.substring(i, i + 2), 16));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
