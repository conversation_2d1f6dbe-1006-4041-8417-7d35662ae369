package com.chis.common.utils;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.imageio.ImageIO;

/**
 * 图片压缩工具类 提供的方法中可以设定生成的 缩略图片的大小尺寸等
 * 
 * <AUTHOR>
 * @date 2011-01-14
 * @versions 1.0
 */
public class ImageUtil {

	/**
	 * 图片文件读取
	 * 
	 * @param srcImgPath
	 * @return
	 */
	private static BufferedImage InputImage(String srcImgPath) {
		BufferedImage srcImage = null;
		FileInputStream in = null;
		try {
			in = new FileInputStream(srcImgPath);
			srcImage = javax.imageio.ImageIO.read(in);
		} catch (IOException e) {
			System.out.println("读取图片文件出错！" + e.getMessage());
			e.printStackTrace();
		} finally {
			if (null != in) {
				try {
					in.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return srcImage;
	}

	/**
	 * 将图片按照指定的图片尺寸压缩
	 * 
	 * @param srcImgPath
	 *            源图片路径
	 * @param outImgPath
	 *            输出的压缩图片的路径
	 * @param new_w
	 *            压缩后的图片宽
	 * @param new_h
	 *            压缩后的图片高
	 */
	public static void compressImage(String srcImgPath, String outImgPath, int new_w, int new_h) {
		BufferedImage src = InputImage(srcImgPath);
		disposeImage(src, outImgPath, new_w, new_h);
	}

	/**
	 * 指定长或者宽的最大值来压缩图片
	 * 
	 * @param srcImgPath
	 *            源图片路径
	 * @param outImgPath
	 *            输出的压缩图片的路径
	 * @param maxLength
	 *            长或者宽的最大值
	 */
	public static void compressImage(String srcImgPath, String outImgPath, int maxLength) {
		// 得到图片
		BufferedImage src = InputImage(srcImgPath);
		if (null != src) {
			int old_w = src.getWidth();
			// 得到源图宽
			int old_h = src.getHeight();
			// 得到源图长
			int new_w = 0;
			// 新图的宽
			int new_h = 0;
			// 新图的长
			// 根据图片尺寸压缩比得到新图的尺寸
			if (old_w > old_h) {
				// 图片要缩放的比例
				new_w = maxLength;
				new_h = (int) Math.round(old_h * ((float) maxLength / old_w));
			} else {
				new_w = (int) Math.round(old_w * ((float) maxLength / old_h));
				new_h = maxLength;
			}
			disposeImage(src, outImgPath, new_w, new_h);
		}
	}

	/**
	 * 处理图片
	 * 
	 * @param src
	 * @param outImgPath
	 * @param new_w
	 * @param new_h
	 */
	private synchronized static void disposeImage(BufferedImage src, String outImgPath, int new_w, int new_h) {
		// 得到图片
		int old_w = src.getWidth();
		// 得到源图宽
		int old_h = src.getHeight();
		// 得到源图长
		BufferedImage newImg = new BufferedImage(new_w, new_h, BufferedImage.TYPE_INT_RGB);
		Graphics2D g = newImg.createGraphics();
		// 从原图上取颜色绘制新图
		g.drawImage(src, 0, 0, old_w, old_h, null);
		g.dispose();
		// 根据图片尺寸压缩比得到新图的尺寸
		newImg.getGraphics().drawImage(src.getScaledInstance(new_w, new_h, Image.SCALE_SMOOTH), 0, 0, null);
		// 调用方法输出图片文件
		OutImage(outImgPath, newImg);
	}

	/**
	 * 将图片文件输出到指定的路径，并可设定压缩质量
	 * 
	 * @param outImgPath
	 * @param newImg
	 * @param per
	 */
	private static void OutImage(String outImgPath, BufferedImage newImg) {
		// 判断输出的文件夹路径是否存在，不存在则创建
		File file = new File(outImgPath);
		if (!file.getParentFile().exists()) {
			file.getParentFile().mkdirs();
		}// 输出到文件流
		try {
			ImageIO.write(newImg, outImgPath.substring(outImgPath.lastIndexOf(".") + 1), new File(outImgPath));
			newImg.flush();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static Map<Integer, String> readfile(String filepath, Map<Integer, String> pathMap) throws Exception {
		if (pathMap == null) {
			pathMap = new HashMap<Integer, String>();
		}

		File file = new File(filepath);
		// 文件
		if (!file.isDirectory()) {
			pathMap.put(pathMap.size(), file.getPath());

		} else if (file.isDirectory()) { // 如果是目录， 遍历所有子目录取出所有文件名
			String[] filelist = file.list();
			for (int i = 0; i < filelist.length; i++) {
				File readfile = new File(filepath + "/" + filelist[i]);
				if (!readfile.isDirectory()) {
					pathMap.put(pathMap.size(), readfile.getPath());

				} else if (readfile.isDirectory()) { // 子目录的目录
					readfile(filepath + "/" + filelist[i], pathMap);
				}
			}
		}
		return pathMap;
	}
	
	 /**
     * 图片缩放
     * @param filePath 图片路径 
     * @param height 高度
     * @param width 宽度
     * @param color 比例不对时填充颜色值
     */
	public static String resizeImg(String filePath, int height, int width, String color) {
		try {
			String realPath = JsfUtil.getAbsolutePath() + "/" +filePath;
			File f = new File(realPath);
			// 判断传入的路径是否为文件
			if (!f.exists() || !f.isFile()) { 
				return null;
			}
			
			BufferedImage bi = ImageIO.read(f);
			if(bi == null) {
				return null;
			}
			double ratio = 0; //缩放比例  
			Image itemp = bi.getScaledInstance(width, height, BufferedImage.SCALE_SMOOTH);
			// 计算比例
			if ((bi.getHeight() > height) || (bi.getWidth() > width)) {
				if (bi.getHeight() >= bi.getWidth()) {
					ratio = (new Integer(height)).doubleValue() / bi.getHeight();
				} else {
					ratio = (new Integer(width)).doubleValue() / bi.getWidth();
				}
				AffineTransformOp op = new AffineTransformOp(AffineTransform.getScaleInstance(ratio, ratio), null);
				itemp = op.filter(bi, null);
			}
			
			// 判断当前文件是否压缩过
			String fileName = f.getName();
			String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);  
			String ratFileName = fileName.substring(0, fileName.lastIndexOf(".")) + "_scale." + suffix;
			String ratPath = realPath.substring(0, realPath.lastIndexOf("/") +1) + ratFileName;
			File outFile = new File(ratPath);
			if(outFile.exists()) {
				return ratPath.replace(JsfUtil.getAbsolutePath(), "");
			}
			
			/** 填充颜色 */
			if (StringUtils.isNotBlank(color)) {
				BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
				Graphics2D g = image.createGraphics();
				g.setColor(Color.decode(color));
				g.fillRect(0, 0, width, height);
				 
				if (width == itemp.getWidth(null))
					g.drawImage(itemp, 0, (height - itemp.getHeight(null)) / 2,
							itemp.getWidth(null), itemp.getHeight(null),
							Color.decode(color), null);
				else
					g.drawImage(itemp, (width - itemp.getWidth(null)) / 2, 0,
							itemp.getWidth(null), itemp.getHeight(null),
							Color.decode(color), null);
				g.dispose();
				itemp = image;
			}
			ImageIO.write((BufferedImage) itemp, "jpg", outFile);
			return ratPath.replace(JsfUtil.getAbsolutePath(), "");
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
	}
	/**
 	 * <p>方法描述：图片缩放
 	 * @param filePath 图片路径 
     * @param height 高度
     * @param width 宽度
     * @param color 比例不对时填充颜色值
 	 * </p>
 	 * @MethodAuthor qrr,2018年9月3日,resizeImgNew
     */
	public static String resizeImgNew(String filePath, int height, int width, String color) {
		try {
			String realPath = JsfUtil.getAbsolutePath() + "/" +filePath;
			File f = new File(realPath);
			// 判断传入的路径是否为文件
			if (!f.exists() || !f.isFile()) { 
				return null;
			}
			
			// 判断当前的文件是否为图片
			BufferedImage bi = ImageIO.read(f);
			if(bi == null) {
				return null;
			}
			// 判断当前文件是否压缩过
			String fileName = f.getName();
			String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);  
			String ratFileName = fileName.substring(0, fileName.lastIndexOf(".")) + "_scale." + suffix;
			String ratPath = realPath.substring(0, realPath.lastIndexOf("/") +1) + ratFileName;
			File outFile = new File(ratPath);
			if(outFile.exists()) {
				return ratPath.replace(JsfUtil.getAbsolutePath(), "");
			}
			Image itemp = bi.getScaledInstance(width, height, BufferedImage.SCALE_SMOOTH);
			
			/** 填充颜色 */	
			if (StringUtils.isNotBlank(color)) {
				BufferedImage image =new BufferedImage(width, height,BufferedImage.TYPE_INT_RGB);
				Graphics2D g = image.createGraphics();
				g.setColor(Color.decode(color));
				g.fillRect(0, 0, width, height);
				
				double bili = (double)bi.getHeight()/bi.getWidth(); 
				if (bi.getHeight() >= height) {
					bili = height/(double)bi.getHeight();
					int w  = (int)(bi.getWidth()*bili);
					g.drawImage(itemp, (width - w) / 2, 0,
							w, itemp.getHeight(null),
							Color.decode(color), null);
				}else {
					if (bi.getWidth() >= width) {
						bili = width/(double)bi.getWidth();
						int h  = (int)(bi.getHeight()*bili);
						g.drawImage(itemp, 0, (height - h) / 2,
								width, h,
								Color.decode(color), null);
					}else {
						g.drawImage(itemp, (width - bi.getWidth()) / 2, (height - bi.getHeight()) / 2,
								bi.getWidth(), bi.getHeight(),
								Color.decode(color), null);
					}
				}
				g.dispose();
				itemp = image;
			}
			ImageIO.write((BufferedImage) itemp, "jpg", outFile);
			return ratPath.replace(JsfUtil.getAbsolutePath(), "");
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
	}

	public static void main(String args[]) {
		try {
//			resizeImg("/zywszk/22.png", 400, 600, "#1F1F1F");
//			 resizeImgNew("/zywszk/2c7e7abb6ad2474bab4a1007afa1ebe3.png", 400, 600, "#1F1F1F");
//			compressImage(filepath, "E:/webFile/zywszk/fe028cd29cdf46d481f01af0435ff23b31.png", 500,500);
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		System.out.println("ok");
	}

}
