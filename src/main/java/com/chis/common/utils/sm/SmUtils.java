package com.chis.common.utils.sm;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.security.Security;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * 国密处理类
 * author:zyb
 * createtime:2020/06/10
 */
public class SmUtils {
    private static final String SECRET_KEY = "00d11fd69c844272";
    private static final String ALGORITHM = "SM4";
    private static final String ALGORITHM_ECB_PADDING = "SM4/ECB/PKCS7Padding";
    private static final String ALGORITHM_CBC_PADDING = "SM4/CBC/PKCS7Padding";
    private static final int DEFAULT_KEY_SIZE = 128;

    static {
        if (Security.getProvider("BC") != null) {
            Security.removeProvider("BC");
        }
        Security.addProvider(new BouncyCastleProvider());
    }

    /**
     * SM4加密
     * 给数据库字段使用
     * 类似AES加密
     * 对称加密，可逆向解密
     */
    public static String sm4Encrypt(String content) {
        if (content == null) {
            return null;
        }
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM_ECB_PADDING);
            cipher.init(Cipher.ENCRYPT_MODE, getSecretKeySpec());
            return Base64Utils.encodeToString(cipher.doFinal(content.getBytes(UTF_8)));
        } catch (Exception e) {
            return content;
        }
    }

    /**
     * SM4解密
     * 给数据库字段使用
     */
    public static String sm4Decrypt(String content) {
        if (content == null) {
            return null;
        }
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM_ECB_PADDING);
            cipher.init(Cipher.DECRYPT_MODE, getSecretKeySpec());
            return new String(cipher.doFinal(Base64Utils.decodeFromString(content)), UTF_8);
        } catch (Exception e) {
            return content;
        }
    }

    /**
     * 密钥
     */
    private static SecretKeySpec getSecretKeySpec() throws Exception {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(SECRET_KEY.getBytes(UTF_8));
        keyGenerator.init(DEFAULT_KEY_SIZE, secureRandom);
        SecretKey secretKey = keyGenerator.generateKey();
        return new SecretKeySpec(secretKey.getEncoded(), ALGORITHM);
    }
}
