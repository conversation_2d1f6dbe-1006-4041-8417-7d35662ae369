package com.chis.common.utils.sm;


import org.bouncycastle.crypto.InvalidCipherTextException;

import java.io.IOException;

/***
 * <p>类描述: SM2 </p>
 *
 * @ClassAuthor mxp , 2018/11/2 , SM2Utils
 */
public class SM2Utils {
    public static byte[] getFormatSm2(String plainText) {
        StringBuilder sb = new StringBuilder();

        sb.append(plainText);
        byte[] bs = sb.toString().getBytes();
        int len = bs.length;
        int mod = len % 16;
        byte[] tbs = bs;
        if (mod != 0) {
            int append = 16 - mod;
            tbs = new byte[len + append];
            System.arraycopy(bs, 0, tbs, 0, len);
            for (int i = len; i < len + append; i++) {
                tbs[i] = (byte) 0x00;
            }
        }
        return tbs;
    }

    // 生成随机秘钥对
    public static void generateKeyPair() {

    }

    // 数据加密
    public static String encrypt(byte[] publicKey, byte[] data) throws IOException, InvalidCipherTextException {
        if (publicKey == null || publicKey.length == 0) {
            return null;
        }

        if (data == null || data.length == 0) {
            return null;
        }

        byte[] source = new byte[data.length];
        System.arraycopy(data, 0, source, 0, data.length);

        String key = SMUtil.byteToHex(publicKey);
        String content = new String(source);

        return SmUtilNew.sm2Encrypt123(content, key);

    }

    // 数据解密
    public static byte[] decrypt(byte[] privateKey, byte[] encryptedData) throws IOException, InvalidCipherTextException {
        if (privateKey == null || privateKey.length == 0) {
            return null;
        }

        if (encryptedData == null || encryptedData.length == 0) {
            return null;
        }
        String result = SmUtilNew.sm2Decrypt123(new String(encryptedData), SMUtil.byteToHex(privateKey));
        return null == result ? null : result.getBytes();
    }

    public static void main(String[] args) throws Exception {
        // 生成密钥对
        generateKeyPair();

        String plainText = "ererfeiisgod";
        byte[] sourceData = plainText.getBytes();


        // 下面的秘钥可以使用generateKeyPair()生成的秘钥内容
        // 国密规范正式私钥
        String prik = "3690655E33D5EA3D9A4AE1A1ADD766FDEA045CDEAA43A9206FB8C430CEFE0D94";
        // 国密规范正式公钥
        String pubk = "04F6E0C3345AE42B51E06BF50B98834988D54EBC7460FE135A48171BC0629EAE205EEDE253A530608178A98F1E19BB737302813BA39ED3FA3C51639D7A20C7391A";

        System.out.println("加密: ");
        String cipherText = SmUtilNew.sm2Encrypt132(plainText, pubk);
        System.out.println(cipherText);
        System.out.println("解密: ");
        plainText = SmUtilNew.sm2Decrypt132(cipherText, prik);;
        System.out.println(plainText);

    }
}
