package com.chis.common.utils.sm;

import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/***
 * <p>类描述: SM4工具类 </p>
 *
 * @ClassAuthor mxp , 2018/11/2 , SM4Utils
 */
public class SM4Utils {
    private String secretKey = "";
    private String iv = "";
    private boolean hexString = false;

    public SM4Utils(String secretKey, boolean hexString) {
        //只支持32长度的秘钥
        if (secretKey.length() > 32) {
            this.secretKey = secretKey.substring(0, 32);
        } else {
            this.secretKey = secretKey;
        }
        this.hexString = hexString;
    }

    public String encryptDataECB1(String plainText) {
        try {
            SM4Context ctx = new SM4Context();
            ctx.isPadding = true;
            ctx.mode = SM4.SM4_ENCRYPT;

            byte[] keyBytes;
            if (hexString) {
                keyBytes = SMUtil.hexStringToBytes(secretKey);
            } else {
                keyBytes = secretKey.getBytes();
            }

            SM4 sm4 = new SM4();
            sm4.sm4_setkey_enc(ctx, keyBytes);
            byte[] encrypted = sm4.sm4_crypt_ecb(ctx, plainText.getBytes("GBK"));
            String cipherText = SMStringUtil.byteArray2hex(encrypted);
            return cipherText;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public String encryptDataECB1(byte[] bs) {
        try {
            SM4Context ctx = new SM4Context();
            ctx.isPadding = true;
            ctx.mode = SM4.SM4_ENCRYPT;

            byte[] keyBytes;
            if (hexString) {
                keyBytes = SMUtil.hexStringToBytes(secretKey);
            } else {
                keyBytes = secretKey.getBytes();
            }

            SM4 sm4 = new SM4();
            sm4.sm4_setkey_enc(ctx, keyBytes);
            byte[] encrypted = sm4.sm4_crypt_ecb(ctx, bs);
            String cipherText = SMStringUtil.byteArray2hex(encrypted);
            return cipherText;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public String encryptDataECB4UTF81(String plainText) {
        try {
            SM4Context ctx = new SM4Context();
            ctx.isPadding = true;
            ctx.mode = SM4.SM4_ENCRYPT;

            byte[] keyBytes;
            if (hexString) {
                keyBytes = SMUtil.hexStringToBytes(secretKey);
            } else {
                keyBytes = secretKey.getBytes();
            }

            SM4 sm4 = new SM4();
            sm4.sm4_setkey_enc(ctx, keyBytes);
            byte[] encrypted = sm4.sm4_crypt_ecb(ctx, plainText.getBytes());
            String cipherText = SMStringUtil.byteArray2hex(encrypted);
            return cipherText;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public String decryptDataECB1(String cipherText) {
        try {
            SM4Context ctx = new SM4Context();
            ctx.isPadding = true;
            ctx.mode = SM4.SM4_DECRYPT;

            byte[] keyBytes;
            if (hexString) {
                keyBytes = SMUtil.hexStringToBytes(secretKey);
            } else {
                keyBytes = secretKey.getBytes();
            }

            SM4 sm4 = new SM4();
            sm4.sm4_setkey_dec(ctx, keyBytes);
            byte[] decrypted = sm4.sm4_crypt_ecb(ctx, new BASE64Decoder().decodeBuffer(cipherText));
            return new String(decrypted, "GBK");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public String decryptDataECB1(byte[] cipherBs) {
        try {
            SM4Context ctx = new SM4Context();
            ctx.isPadding = true;
            ctx.mode = SM4.SM4_DECRYPT;

            byte[] keyBytes;
            if (hexString) {
                keyBytes = SMUtil.hexStringToBytes(secretKey);
            } else {
                keyBytes = secretKey.getBytes();
            }

            SM4 sm4 = new SM4();
            sm4.sm4_setkey_dec(ctx, keyBytes);
            byte[] decrypted = sm4.sm4_crypt_ecb(ctx, cipherBs);
            return new String(decrypted, "GBK");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public String encryptDataCBC1(String plainText) {
        try {
            SM4Context ctx = new SM4Context();
            ctx.isPadding = true;
            ctx.mode = SM4.SM4_ENCRYPT;

            byte[] keyBytes;
            byte[] ivBytes;
            if (hexString) {
                keyBytes = SMUtil.hexStringToBytes(secretKey);
                ivBytes = SMUtil.hexStringToBytes(iv);
            } else {
                keyBytes = secretKey.getBytes();
                ivBytes = iv.getBytes();
            }

            SM4 sm4 = new SM4();
            sm4.sm4_setkey_enc(ctx, keyBytes);
            byte[] encrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, plainText.getBytes("GBK"));
            String cipherText = new BASE64Encoder().encode(encrypted);
            if (cipherText != null && cipherText.trim().length() > 0) {
                Pattern p = Pattern.compile("\\s*|\t|\r|\n");
                Matcher m = p.matcher(cipherText);
                cipherText = m.replaceAll("");
            }
            return cipherText;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public String decryptDataCBC1(String cipherText) {
        try {
            SM4Context ctx = new SM4Context();
            ctx.isPadding = true;
            ctx.mode = SM4.SM4_DECRYPT;

            byte[] keyBytes;
            byte[] ivBytes;
            if (hexString) {
                keyBytes = SMUtil.hexStringToBytes(secretKey);
                ivBytes = SMUtil.hexStringToBytes(iv);
            } else {
                keyBytes = secretKey.getBytes();
                ivBytes = iv.getBytes();
            }

            SM4 sm4 = new SM4();
            sm4.sm4_setkey_dec(ctx, keyBytes);
            byte[] decrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, new BASE64Decoder().decodeBuffer(cipherText));
            return new String(decrypted, "GBK");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) throws Exception {

        String a = "{\"birthday\":\"20180303\",\"AppVersion\":\"1.0\",\"address\":\"%E8%B4%B5%E5%B7%9E%E7%9C%81%E6%B5%8B%E8%AF%95A\",\"gender\":\"1\",\"CertID\":\"134213175547446544\",\"cellphone\":\"13232323232\",\"CertType\":\"01\",\"imei\":\"0000\",\"Time\":\"20180710093231\",\"AppUserID\":\"1531215151446\",\"Name\":\"%E6%B5%8B%E8%AF%95A\",\"AppPackageName\":\"com.hengbao.vasdemo\"}";
        //System.out.println(ByteUtil.byteArrayToHexStr(a.getBytes()));
        SM4BcUtil sm4 = new SM4BcUtil("03B68A739213EF9C3DB689C068B0E22E");
        String enTxt = SMStringUtil.byteArray2hex(sm4.encrypt(a.getBytes()));
        //SM4Utils sm4 = new SM4Utils("03B68A739213EF9C3DB689C068B0E22E", true);
        //String cipherText = sm4.encryptDataECB1(a.getBytes());
        System.out.println("密文: " + enTxt.toUpperCase());
        //System.out.println(new String(sm4.decrypt(SMStringUtil.hex2byteArray418(enTxt.toUpperCase()))));


		/*String data = "A68FD27B84FF5C596DB313258689BB591BCAFE5A6116FA41F9485F7ACD86DB8F773AAE22579DF84B2A1171565EE7C037842EE7EC9B8C6F481CC6AEF98E177B5DF451072CF66329A39D7758732960B7FF6151CA9D3DAEB43E578C5E9D912392A998E3E8DC4D31550A1EA3603E45221081519899DA64C53BF9E5E3021C0E192E69E776262E88354D083CB0F7304D4F9E1365DFB0E0AD7CED8146BF3F693B062ED0E944D07D6A98769FE969651F3C7207035E7345BB8CBDA90C95FA407AD5C052A9161F701253115C5303D5C46060B1BA7ACFA6E1842659059518D7C4D4AB7E8B5A320E085DCE38F94F6FEE52E071F1A41346302F08ADA7174D1F7EDFCCD1A0E04DD31F0B248614518EB7095D7664088A57D6C250617B601DF7FA31725F0D3C14A9F404B55A7CBAC1A50D5EDDD257055403E3787665AB0FB5AA78CAFBCE96B72A48286090D7CA1717CE8A752D2C9D2BF5DFD331AF57187CE0A97B0D7F81578422283EB882E6E3610F965AB099848689203593A239AE9A5A6B697DF73EB833FF55139C313683A0BFE94D98022FE67CBCA382BAA0295BA66FF5CA10F082A1726B8B022FDC1C27ABC75C12B2AD740CDEFE036448AF6DF3448CC0B19D1530DE27E935F7370BE7DC33A3EFAA7B44A05525F7E050496B461155D6D77DF5C4DAC8C7C196725DA93ADCAA05BCB85AB5732BB17EFD3BC6ACA98B3ABE94C297CD8776B55D327BD169C2368B6062CBC909C2903938E422D4FA55076DE240E16A8A9532CF7561600BE797F7608B942AF6F3CB04380E91EA5ED24D3EFA59FF0C00647C37CBE33D6DD6E2834DB21412C136F922734F90AC826ADD2F0AAEE0B9F5B4205AB6A2AEB22A03AF07F0B5693D5B8E7C066B5846399C0AB3E1F01406CFA03E945EC0F36AD10D06F275ADEC1D82DF9972C96756BA5D1E9AB1D6EF9FDA111076EB7F0D3AB34921D4F4E7F616835C98F9C2C1F6500F8042D272D0E6C79FA6095DCE2BE6C5A0241B";
		SM4BcUtil sm4t = new SM4BcUtil("90C9D5307ABD4CFF869F26A71F2D42F2");
		System.out.println(new String(sm4t.decrypt(SMStringUtil.hex2byteArray418(data))).trim());*/
		/*//String t1 = "ererdafadffseiisg1dddddd2332oddd";
		String t1 = "92CDCE45A16799FD6D3231BA5E986602";
		System.out.println(ByteUtil.byteArrayToHexStr(t1.getBytes()));

		//用户身份认证密钥
		//SM4Utils sm4 = new SM4Utils("0123456789ABCDEFFEDCBA9876543210", true);
		SM4Utils sm4 = new SM4Utils("0123456789ABCDEFFEDCBA9876543210", true);
		System.out.println("ECB模式");
		String cipherText = sm4.encryptDataECB(ByteUtil.hex2byteArray(t1));
		//String cipherText = sm4.encryptDataECB4UTF8(t1);
		if ("86C63180C2806ED1F47B859DE501215B".equals(cipherText.toUpperCase())) {
			System.out.println("密文: ok " + cipherText.toUpperCase());
		}

		//计算主索引ID
		sm4 = new SM4Utils("86C63180C2806ED1F47B859DE501215B", true);
		t1 = "3031313130313030323031373132323530303658000000000000000000000000";
		cipherText = sm4.encryptDataECB(ByteUtil.hex2byteArray(t1));
		if ("98FF9F2C05145CB9305E9D8A57E072BB51180CA49E5BE5E9D41BCE3A9A571928".equals(cipherText.toUpperCase())) {
			System.out.println("健康卡ID密文: ok " + cipherText.toUpperCase());
		}

		//动态二维码有效性信息SM4 加密解密
		sm4 = new SM4Utils("11223344556677888877665544332211", true);
		String expiryTimestamp = "20180117115003";
		expiryTimestamp = SMStringUtil.append(expiryTimestamp, "0", 32, false);
		cipherText = sm4.encryptDataECB(ByteUtil.hex2byteArray(expiryTimestamp));
		if ("92FD69141DFE1553A42251E2B0196148".equals(cipherText.toUpperCase())) {
			System.out.println("有效性信息密文: ok " + cipherText.toUpperCase());
		}

		String tt = sm4.decryptDataECB(SMStringUtil.hex2byteArray418(cipherText));
		System.out.println(SMStringUtil.byteArray2hex(tt.getBytes()));

		//手机号SM4 加密解密
		sm4 = new SM4Utils("11223344556677888877665544332211", true);
		String txt = "13001174100";
		txt = SMStringUtil.append(txt, "0", 32, false);
		cipherText = sm4.encryptDataECB(ByteUtil.hex2byteArray(txt));
		tt = sm4.decryptDataECB(SMStringUtil.hex2byteArray418(cipherText));
		System.out.println(SMStringUtil.byteArray2hex(tt.getBytes()));*/


    }
}
