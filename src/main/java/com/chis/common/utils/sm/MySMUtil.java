package com.chis.common.utils.sm;

import org.bouncycastle.util.encoders.Hex;

/***
 * <p>类描述: SM工具类 </p>
 *
 * @ClassAuthor mxp , 2018/11/2 , MySMUtil
 */
public class MySMUtil {
    //签名、加密秘钥
    public static final String KEY_HEX = "E941713CA44447E6B8CA9D9E81AAFBEC";

    /***
     * <p>方法描述: SM3加密</p>
     *
     * @MethodAuthor mxp, 2018/11/2,genHashString
     */
    public static String sm3Hash(String srcData) {
        byte[] msg = srcData.getBytes();
        SM3Digest sm3 = new SM3Digest();
        sm3.update(msg, 0, msg.length);
        byte[] md = new byte[32];
        sm3.doFinal(md, 0);
        return new String(Hex.encode(md)).toUpperCase();
    }

    /***
     * <p>方法描述: sm4加密，加密结果转大写</p>
     *
     * @MethodAuthor mxp, 2018/11/2,sm4Encrypt
     */
    public static String sm4Encrypt(String srcData) {
        try {
            SM4BcUtil sm4 = new SM4BcUtil(KEY_HEX);
            return SMStringUtil.byteArray2hex(sm4.encrypt(srcData.getBytes())).toUpperCase();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /***
     * <p>方法描述: sm4解密</p>
     *
     * @MethodAuthor mxp, 2018/11/2,sm4Encrypt
     */
    public static String sm4Decrypt(String srcData) {
        try {
            SM4BcUtil sm4t = new SM4BcUtil(KEY_HEX);
            return new String(sm4t.decrypt(SMStringUtil.hex2byteArray418(srcData))).trim();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        String s="app_id=83A6D3CB90E64E98B45FDDD767A5F0AF&biz_content={\"birthday\":\"20180303\",\"AppVersion\":\"1.0\",\"address\":\"%E8%B4%B5%E5%B7%9E%E7%9C%81%E6%B5%8B%E8%AF%95A\",\"gender\":\"1\",\"CertID\":\"134213175547446544\",\"cellphone\":\"13232323232\",\"CertType\":\"01\",\"imei\":\"0000\",\"Time\":\"20180710093231\",\"AppUserID\":\"1531215151446\",\"Name\":\"%E6%B5%8B%E8%AF%95A\",\"AppPackageName\":\"com.hengbao.vasdemo\"}&digest_type=SM3&enc_type=SM4&method=ehc.ehealthcode.register&term_id=0000&timestamp=1531215151446&version=X.M.0.1&key=03B68A739213EF9C3DB689C068B0E22E776826E0DCB1CDA1382D37B53F2DE1C2";
        //AA82A3295E79AC786811B58D04D8A4EA283690B5D64310809E11F5410811820C
        System.out.println(MySMUtil.sm3Hash(s));
    }

}
