package com.chis.common.utils.sm;

import cn.hutool.crypto.symmetric.SM4;
import com.chis.common.utils.StringUtils;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithRandom;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;
import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigInteger;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Locale;

import static java.nio.charset.StandardCharsets.UTF_8;


public class SmUtilNew {
    private static final X9ECParameters x9ECParameters = GMNamedCurves.getByName("sm2p256v1");
    private static final ECDomainParameters ecDomainParameters = new ECDomainParameters(x9ECParameters.getCurve(), x9ECParameters.getG(), x9ECParameters.getN());
    private static final String SECRET_KEY = secretKeyDecode(new Integer[]{0, 1, 15, 4, 5, 20, 19, 13, 17, 21, 18, 15, 16, 15, 21, 17});
    private static final String ALGORITHM = "SM4";
    private static final String ALGORITHM_ECB_PADDING = "SM4/ECB/PKCS7Padding";
    private static final String ALGORITHM_CBC_PADDING = "SM4/CBC/PKCS7Padding";
    private static final int DEFAULT_KEY_SIZE = 128;
    private static final String PRIVATE_KEY = secretKeyDecode(new Integer[]{2, 12, 2, 6, 6, 10, 9, 16, 23, 23, 17, 19, 25, 28, 27, 25, 29, 22, 24, 25, 30, 36, 22, 34, 32, 25, 30, 29, 32, 34, 31, 32, 36, 45, 46, 49, 46, 45, 40, 49, 44, 47, 55, 46, 47, 51, 57, 50, 60, 55, 56, 57, 52, 57, 57, 70, 66, 62, 71, 67, 62, 69, 63, 66});
    private static final String PUBLIC_KEY = secretKeyDecode(new Integer[]{0, 5, 7, 7, 18, 19, 15, 7, 15, 9, 25, 22, 15, 27, 21, 29, 21, 17, 30, 28, 22, 30, 32, 35, 27, 27, 32, 31, 38, 39, 41, 34, 46, 38, 39, 49, 44, 45, 38, 53, 55, 52, 46, 43, 52, 51, 58, 58, 53, 51, 51, 60, 56, 62, 65, 61, 69, 65, 58, 74, 70, 75, 64, 76, 77, 68, 68, 68, 74, 70, 83, 83, 87, 74, 79, 88, 77, 87, 82, 92, 83, 85, 94, 83, 95, 94, 101, 91, 96, 94, 103, 104, 102, 107, 104, 96, 108, 101, 111, 113, 111, 109, 113, 104, 105, 105, 108, 114, 110, 113, 112, 126, 112, 116, 121, 115, 129, 127, 127, 134, 127, 132, 136, 124, 129, 134, 140, 133, 142, 143});
    private static final String SM4_KEY = secretKeyDecode(new Integer[]{15, 14, 4, 13, 4, 18, 13, 8, 21, 21, 17, 12, 13, 14, 20, 21, 24, 27, 21, 34, 25, 23, 30, 25, 30, 34, 31, 41, 34, 40, 43, 45});


    static {
        if (Security.getProvider("BC") != null) {
            Security.removeProvider("BC");
        }
        Security.addProvider(new BouncyCastleProvider());
    }

    /**
     * 密钥解密
     */
    public static String secretKeyDecode(Integer[] secretKeyEncryptArray) {
        if (secretKeyEncryptArray == null || secretKeyEncryptArray.length == 0) return "";

        Integer[] decodedArray = new Integer[secretKeyEncryptArray.length];

        for (int i = 0, length = secretKeyEncryptArray.length; i < length; i++) {
            decodedArray[i] = secretKeyEncryptArray[i] - i;
        }

        StringBuilder decodedStr = new StringBuilder();
        for (Integer item : decodedArray) {
            decodedStr.append(Integer.toString(item, 16));
        }

        return decodedStr.toString();
    }

    /**
     * SM2签名C1C2C3
     * 后私前公
     * PUBLIC_KEY=04+X+Y
     */
    public static String sm2Sign123(String content) throws InvalidCipherTextException {
        return sm2Sign123(content, PUBLIC_KEY);
    }

    /**
     * SM2签名C1C2C3（自定义KEY）
     * 后私前公
     * PUBLIC_KEY=04+X+Y
     */
    public static String sm2Sign123(String content, String publicKey) throws InvalidCipherTextException {
        if (content == null || StringUtils.isBlank(publicKey)) {
            return null;
        }
        byte[] sm3EncryptPrivate = sm3Encrypt(content).getBytes(UTF_8);
        ECPoint ecPoint = ecDomainParameters.getCurve().decodePoint(ByteUtils.fromHexString(publicKey));
        SM2Engine sm2Engine = new SM2Engine();
        sm2Engine.init(true, new ParametersWithRandom(new ECPublicKeyParameters(ecPoint, ecDomainParameters), new SecureRandom()));
        return ByteUtils.toHexString(sm2Engine.processBlock(sm3EncryptPrivate, 0, sm3EncryptPrivate.length));
    }

    /**
     * SM2验签C1C2C3
     * 前私后公
     */
    public static Boolean sm2Check123(String content, String sign) throws InvalidCipherTextException {
        if (content == null || sign == null) {
            return false;
        }
        return isEquals(sm3Encrypt(content), sm2Decrypt123(sign));
    }

    /**
     * 匹配相等
     */
    public static boolean isEquals(Object a, Object b) {
        if (a == null && b == null) {
            return true;
        }
        if (a != null && b == null) {
            return false;
        }
        if (a == null) {
            return false;
        }
        return String.valueOf(a).equals(String.valueOf(b));
    }
    /**
     * SM2签名C1C3C2
     * 后私前公
     * PUBLIC_KEY=04+X+Y
     */
    public static String sm2Sign132(String content) throws Exception {
        if (content == null) {
            return null;
        }
        byte[] sm3EncryptPrivate = sm3Encrypt(content).getBytes(UTF_8);
        ECPoint ecPoint = ecDomainParameters.getCurve().decodePoint(ByteUtils.fromHexString(PUBLIC_KEY));
        SM2Engine sm2Engine = new SM2Engine();
        sm2Engine.init(true, new ParametersWithRandom(new ECPublicKeyParameters(ecPoint, ecDomainParameters), new SecureRandom()));
        return ByteUtils.toHexString(sm2C1C2C3ToC1C3C2(sm2Engine.processBlock(sm3EncryptPrivate, 0, sm3EncryptPrivate.length)));
    }

    /**
     * SM2验签C1C3C2
     * 前私后公
     */
    public static Boolean sm2Check132(String content, String sign) throws Exception {
        if (content == null || sign == null) {
            return false;
        }
        return isEquals(sm3Encrypt(content), sm2Decrypt132(sign));
    }

    /**
     * SM2加密C1C2C3
     * 后私前公
     * PUBLIC_KEY=04+X+Y
     */
    public static String sm2Encrypt123(String content) throws InvalidCipherTextException {
        return sm2Encrypt123(content, PUBLIC_KEY);
    }

    /**
     * SM2解密C1C2C3
     * 前私后公
     */
    public static String sm2Decrypt123(String content) throws InvalidCipherTextException {
        return sm2Decrypt123(content, PRIVATE_KEY);
    }

    /**
     * SM2加密C1C2C3（自定义KEY）
     * 后私前公
     * PUBLIC_KEY=04+X+Y
     */
    public static String sm2Encrypt123(String content, String publicKey) throws InvalidCipherTextException {
        if (content == null || StringUtils.isBlank(publicKey)) {
            return null;
        }
        byte[] contentPrivate = content.getBytes(UTF_8);
        ECPoint ecPoint = ecDomainParameters.getCurve().decodePoint(ByteUtils.fromHexString(publicKey));
        SM2Engine sm2Engine = new SM2Engine();
        sm2Engine.init(true, new ParametersWithRandom(new ECPublicKeyParameters(ecPoint, ecDomainParameters), new SecureRandom()));
        return ByteUtils.toHexString(sm2Engine.processBlock(contentPrivate, 0, contentPrivate.length));
    }

    /**
     * SM2解密C1C2C3（自定义KEY）
     * 前私后公
     */
    public static String sm2Decrypt123(String content, String privateKey) throws InvalidCipherTextException {
        if (content == null || StringUtils.isBlank(privateKey)) {
            return null;
        }
        byte[] signature = ByteUtils.fromHexString(content);
        ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(new BigInteger(privateKey, 16), ecDomainParameters);
        SM2Engine sm2Engine = new SM2Engine();
        sm2Engine.init(false, privateKeyParameters);
        return new String(sm2Engine.processBlock(signature, 0, signature.length), UTF_8);
    }

    /**
     * SM2加密C1C3C2
     * 后私前公
     * PUBLIC_KEY=04+X+Y
     */
    public static String sm2Encrypt132(String content) throws Exception {
        return sm2Encrypt132(content, PUBLIC_KEY);
    }

    /**
     * SM2解密C1C3C2
     * 前私后公
     */
    public static String sm2Decrypt132(String content) throws Exception {
        return sm2Decrypt132(content, PRIVATE_KEY);
    }

    /**
     * SM2加密C1C3C2（自定义KEY）
     * 后私前公
     * PUBLIC_KEY=04+X+Y
     */
    public static String sm2Encrypt132(String content, String publicKey) throws Exception {
        if (content == null || StringUtils.isBlank(publicKey)) {
            return null;
        }
        byte[] contentPrivate = content.getBytes(UTF_8);
        ECPoint ecPoint = ecDomainParameters.getCurve().decodePoint(ByteUtils.fromHexString(publicKey));
        SM2Engine sm2Engine = new SM2Engine();
        sm2Engine.init(true, new ParametersWithRandom(new ECPublicKeyParameters(ecPoint, ecDomainParameters), new SecureRandom()));
        return ByteUtils.toHexString(sm2C1C2C3ToC1C3C2(sm2Engine.processBlock(contentPrivate, 0, contentPrivate.length)));
    }

    /**
     * SM2解密C1C3C2（自定义KEY）
     * 前私后公
     */
    public static String sm2Decrypt132(String content, String privateKey) throws Exception {
        if (content == null || StringUtils.isBlank(privateKey)) {
            return null;
        }
        byte[] signature = sm2C1C3C2ToC1C2C3(ByteUtils.fromHexString(content));
        ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(new BigInteger(privateKey, 16), ecDomainParameters);
        SM2Engine sm2Engine = new SM2Engine();
        sm2Engine.init(false, privateKeyParameters);
        return new String(sm2Engine.processBlock(signature, 0, signature.length), UTF_8);
    }

    /**
     * SM3加密
     * 类似MD5加密
     * 非对称加密，不可逆向解密
     * 64位
     */
    public static String sm3Encrypt(String content) {
        if (content == null) {
            return null;
        }
        SM3Digest sm3Digest = new SM3Digest();
        byte[] source = md5(content).getBytes(UTF_8);
        sm3Digest.update(source, 0, source.length);
        byte[] result = new byte[sm3Digest.getDigestSize()];
        sm3Digest.doFinal(result, 0);
        return ByteUtils.toHexString(result);
    }

    /**
     * md5加密
     */
    public static String md5(Object object) {
        if (object == null) {
            return null;
        }
        return DigestUtils.md5Hex(String.valueOf(object)).toLowerCase(Locale.ROOT);
    }

    /**
     * SM4加密
     * 给数据库字段使用
     * 类似AES加密
     * 对称加密，可逆向解密
     */
    public static String sm4Encrypt(String content) {
        if (content == null) {
            return null;
        }
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM_ECB_PADDING);
            cipher.init(Cipher.ENCRYPT_MODE, getSecretKeySpec());
            return Base64Utils.encodeToString(cipher.doFinal(content.getBytes(UTF_8)));
        } catch (Exception e) {
            return content;
        }
    }

    /**
     * SM4解密
     * 给数据库字段使用
     */
    public static String sm4Decrypt(String content) throws Exception {
        if (content == null) {
            return null;
        }
        Cipher cipher = Cipher.getInstance(ALGORITHM_ECB_PADDING);
        cipher.init(Cipher.DECRYPT_MODE, getSecretKeySpec());
        return new String(cipher.doFinal(Base64Utils.decodeFromString(content)), UTF_8);
    }

    /**
     * SM4加密
     * 与前端SM4加密一致
     * 类似AES加密
     * 对称加密，可逆向解密
     */
    public static String sm4JsEncrypt(String content) throws DecoderException {
        return sm4JsEncrypt(content, SM4_KEY);
    }

    /**
     * SM4解密
     * 与前端SM4解密一致
     */
    public static String sm4JsDecrypt(String content) throws DecoderException {
        return sm4JsDecrypt(content, SM4_KEY);
    }

    /**
     * SM4加密（自定义KEY）
     * 与前端SM4加密一致
     * 类似AES加密
     * 对称加密，可逆向解密
     */
    public static String sm4JsEncrypt(String content, String sm4Key) throws DecoderException {
        if (StringUtils.isBlank(content) || StringUtils.isBlank(sm4Key)) {
            return null;
        }
        return new SM4(Hex.decodeHex(sm4Key)).encryptHex(content);
    }

    /**
     * SM4解密（自定义KEY）
     * 与前端SM4解密一致
     */
    public static String sm4JsDecrypt(String content, String sm4Key) throws DecoderException {
        if (StringUtils.isBlank(content) || StringUtils.isBlank(sm4Key)) {
            return null;
        }
        return new SM4(Hex.decodeHex(sm4Key)).decryptStr(content);
    }

    /**
     * SM2格式转换
     * C1C2C3转C1C3C2
     */
    private static byte[] sm2C1C2C3ToC1C3C2(byte[] c1c2c3) throws Exception {
        int c1Len = (x9ECParameters.getCurve().getFieldSize() + 7) / 8 * 2 + 1;
        int c3Len = 32;
        byte[] result = new byte[c1c2c3.length];
        System.arraycopy(c1c2c3, 0, result, 0, c1Len);
        System.arraycopy(c1c2c3, c1c2c3.length - c3Len, result, c1Len, c3Len);
        System.arraycopy(c1c2c3, c1Len, result, c1Len + c3Len, c1c2c3.length - c1Len - c3Len);
        return result;
    }

    /**
     * SM2格式转换
     * C1C3C2转C1C2C3
     */
    private static byte[] sm2C1C3C2ToC1C2C3(byte[] c1c3c2) throws Exception {
        int c1Len = (x9ECParameters.getCurve().getFieldSize() + 7) / 8 * 2 + 1;
        int c3Len = 32;
        byte[] result = new byte[c1c3c2.length];
        System.arraycopy(c1c3c2, 0, result, 0, c1Len);
        System.arraycopy(c1c3c2, c1Len + c3Len, result, c1Len, c1c3c2.length - c1Len - c3Len);
        System.arraycopy(c1c3c2, c1Len, result, c1c3c2.length - c3Len, c3Len);
        return result;
    }

    /**
     * 密钥
     */
    private static SecretKeySpec getSecretKeySpec() throws Exception {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(SECRET_KEY.getBytes(UTF_8));
        keyGenerator.init(DEFAULT_KEY_SIZE, secureRandom);
        SecretKey secretKey = keyGenerator.generateKey();
        return new SecretKeySpec(secretKey.getEncoded(), ALGORITHM);
    }

    public static void main(String[] args) throws InvalidCipherTextException {
        System.out.println(SmUtilNew.sm2Decrypt123("04c8e2f0649eb11867b1f6148c9afb027d3e8b2c83ccb3f9a7de135acddd280ab3f65550b05d3767450f930c7cab34f112cfe9898492d8dac2dda071be641e6ecc63ef0fe119e9c9f81c7366e6f871898c5b79d6c702511a62584e0895d55a0ef788219ab20764dac9be9b3f02386e6c0f6dc6b5b0f8066ce5c33dd592b449ed8a3aa2612893001b5b4f1f159aeb72516f9161f8b12051534b7fbb16ee7238637d9dabccebe5f9fd6879e6d96b5720691ded53798c970910cd23ee46858b35ca5857e50848121fc4f29cfe972eef180963682dd7dd8bdce99df07d6278390af67407b7b547391c001d0bc72c4ffa8f1b0a917d151bbf4e48a84b039e886e0ad9571956184adb0636f03092e65faee8ffde3e251e855302616cca21701b27dc2c318cf54f0a97682aaf05902733383463f50977431eb0708b74fbdee28b61064f06aa05659bfa149c986bcb6f6f1d6dcb5fcb106d95e64b7fd8290c19db6af37b26d27565e34b4eea2be48bda267e3be0409da4c6db956fd61f9318e8095c2d8165c7e0c617b52cb207dcf10d3265b5ea42c9d5161c133bc35adb51891bc7e672119d37ba930f3bae311e7eedc330d6e881c6557afce0725459a388b6ebd0e502cdab05fc16939e44bf39d9db36e2"));

    }

}
