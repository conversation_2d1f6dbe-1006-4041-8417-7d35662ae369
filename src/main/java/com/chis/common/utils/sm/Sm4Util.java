package com.chis.common.utils.sm;

import com.chis.common.utils.StringUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
/**
 *  <p>类描述：国密4加解密-适合oracle历史数据处理</p>
 * @ClassAuthor hsj 2023-01-14 9:03
 */
public class Sm4Util {
	  private String secretKey = "";
	    private String iv = "";
	    private boolean hexString = false;
	    public Sm4Util() {
	    }
	    private byte[] getkey(String key) {
	        try {
	            byte[] keyBytes = new byte[16];
	            byte[] t = new byte[0];
	            t = key.getBytes("UTF-8");
	            if (t.length < 16) {
	                for (int i = 0; i < t.length; i++) {
	                    keyBytes[i] = t[i];
	                }
	            } else {
	                keyBytes = t;
	            }
	            return keyBytes;
	        } catch (UnsupportedEncodingException e) {
	            e.printStackTrace();
	            return null;
	        }
	    }
	    public String encryptData_ECB(String plainText) {
	        try {
	            SM4Context ctx = new SM4Context();
	            ctx.isPadding = true;
	            ctx.mode = SM4.SM4_ENCRYPT;
	            byte[] keyBytes;
	            if (hexString) {
	                keyBytes = new Util().hexStringToBytes(secretKey);
	            } else {
	                keyBytes = getkey(secretKey);
	            }
	            SM4 sm4 = new SM4();
	            sm4.sm4_setkey_enc(ctx, keyBytes);
	            byte[] encrypted = sm4.sm4_crypt_ecb(ctx, plainText.getBytes("UTF-8"));
	            String cipherText = new BASE64Encoder().encode(encrypted);
	            if (cipherText != null & cipherText.trim().length() > 0) {
	                Pattern p = Pattern.compile("\\s*|\t|\r|\n");
	                Matcher m = p.matcher(cipherText);
	                cipherText = m.replaceAll("");
	            }
	            return cipherText;
	        } catch (Exception e) {
	            e.printStackTrace();
	            return null;
	        }
	    }
	    public String decryptData_ECB(String cipherText) throws Exception {
	            SM4Context ctx = new SM4Context();
	            ctx.isPadding = true;
	            ctx.mode = SM4.SM4_DECRYPT;
	            byte[] keyBytes;
	            if (hexString) {
	                keyBytes = new Util().hexStringToBytes(secretKey);
	            } else {
	                keyBytes = getkey(secretKey);
	            }
	            SM4 sm4 = new SM4();
	            sm4.sm4_setkey_dec(ctx, keyBytes);
	            byte[] decrypted = sm4.sm4_crypt_ecb(ctx, new BASE64Decoder().decodeBuffer(cipherText));
	            return new String(decrypted, "UTF-8");
	    }
	    public String encryptData_CBC(String plainText) {
	        try {
	            SM4Context ctx = new SM4Context();
	            ctx.isPadding = true;
	            ctx.mode = SM4.SM4_ENCRYPT;
	            byte[] keyBytes;
	            byte[] ivBytes;
	            if (hexString) {
	                keyBytes = new Util().hexStringToBytes(secretKey);
	                ivBytes = new Util().hexStringToBytes(iv);
	            } else {
	                keyBytes = getkey(secretKey);
	                ivBytes = iv.getBytes();
	            }
	            SM4 sm4 = new SM4();
	            sm4.sm4_setkey_enc(ctx, keyBytes);
	            byte[] encrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, plainText.getBytes("UTF-8"));
	            String cipherText = new BASE64Encoder().encode(encrypted);
	            if (cipherText != null & cipherText.trim().length() > 0) {
	                Pattern p = Pattern.compile("\\s*|\t|\r|\n");
	                Matcher m = p.matcher(cipherText);
	                cipherText = m.replaceAll("");
	            }
	            return cipherText;
	        } catch (Exception e) {
	            e.printStackTrace();
	            return null;
	        }
	    }
	    public String decryptData_CBC(String cipherText) {
	        try {
	            SM4Context ctx = new SM4Context();
	            ctx.isPadding = true;
	            ctx.mode = SM4.SM4_DECRYPT;
	            byte[] keyBytes;
	            byte[] ivBytes;
	            if (hexString) {
	                keyBytes = new Util().hexStringToBytes(secretKey);
	                ivBytes = new Util().hexStringToBytes(iv);
	            } else {
	                keyBytes = getkey(secretKey);
	                ivBytes = iv.getBytes();
	            }
	            SM4 sm4 = new SM4();
	            sm4.sm4_setkey_dec(ctx, keyBytes);
	            byte[] decrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, new BASE64Decoder().decodeBuffer(cipherText));
	            return new String(decrypted, "UTF-8");
	        } catch (Exception e) {
	            e.printStackTrace();
	            return null;
	        }
	    }
	    /**
	     * ECB 模式 加密
	     * @param value
	     * @param key
	     * @return
	     */
	    /**
	     * ECB 模式 加密
	     * @param value
	     * @param key
	     * @return
	     */
	    public static String strEncode (String value,String key){
			if(StringUtils.isBlank(value)){
				return value;
			}
			try {
				Sm4Util sm4Utils = new Sm4Util();
				sm4Utils.secretKey = key;
				sm4Utils.hexString = false;
				String s = sm4Utils.encryptData_ECB(value);
				s = s.trim().replaceAll("\r|\n","");
				s = s.replace("+","%2B");
				s = s.replace("/","%2F");
				return s;
			}catch (Exception e){
				e.printStackTrace();
				return value;
			}
	    }
	    /**
	     * ECB 模式 解密
	     * @param value
	     * @param key
	     * @return
	     */
	    /**
	     * ECB 模式 解密
	     * @param value
	     * @param key
	     * @return
	     */
	    public static String strDecode (String value,String key) {
	        if(StringUtils.isBlank(value)){
	            return value;
	        }
	        String s ;
	        try{
	            value = value.replace("%2B","+");
	            value = value.replace("%2F","/");
	            Sm4Util sm4Utils = new Sm4Util();
	            sm4Utils.secretKey = key;
	            sm4Utils.hexString = false;
	            s = sm4Utils.decryptData_ECB(value);
	        }catch(Exception e){
	            e.printStackTrace();
//	            StackTraceElement[] trace = e.getStackTrace();
//	            String sOut = "";
//	            for (int i = 0; i < trace.length; i++) {
//	                sOut += trace[i] + "\r\n";
//	            }
//	            return sOut;
				return value;
	        }
	        return s;
	    }
	    public class SM4Context {
	        public int mode;
	        public long[] sk;
	        public boolean isPadding;
	        public SM4Context() {
	            this.mode = 1;
	            this.isPadding = true;
	            this.sk = new long[32];
	        }
	    }
	    static class SM4 {
	        public static final int SM4_ENCRYPT = 1;
	        public static final int SM4_DECRYPT = 0;
	        private int getUlongBe(byte[] b, int i) {
	            long n = (long)(b[i] & 0xff) << 24 | (long)((b[i + 1] & 0xff) << 16) | (long)((b[i + 2] & 0xff) << 8) | (long)(b[i + 3] & 0xff) & 0xffffffffL;
	            return (int)n;
	        }
	        private void putUlongBe(long n, byte[] b, int i) {
	            b[i] = (byte)(int)(0xFF & n >> 24);
	            b[i + 1] = (byte)(int)(0xFF & n >> 16);
	            b[i + 2] = (byte)(int)(0xFF & n >> 8);
	            b[i + 3] = (byte)(int)(0xFF & n);
	        }
	        private long shl (long x, int n) {
	            return (x & 0xFFFFFFFF) << n;
	        }
	        private int rotl (long x, int n) {
	            return (int)(shl(x, n) | x >> (32 - n));
	        }
	        private void swap (long[] sk, int i) {
	            long t = sk[i];
	            sk[i] = sk[(31 - i)];
	            sk[(31 - i)] = t;
	        }
	        public static final byte[] SBOXTABLE = { (byte) 0xd6, (byte) 0x90, (byte) 0xe9, (byte) 0xfe,
	                (byte) 0xcc, (byte) 0xe1, 0x3d, (byte) 0xb7, 0x16, (byte) 0xb6,
	                0x14, (byte) 0xc2, 0x28, (byte) 0xfb, 0x2c, 0x05, 0x2b, 0x67,
	                (byte) 0x9a, 0x76, 0x2a, (byte) 0xbe, 0x04, (byte) 0xc3,
	                (byte) 0xaa, 0x44, 0x13, 0x26, 0x49, (byte) 0x86, 0x06,
	                (byte) 0x99, (byte) 0x9c, 0x42, 0x50, (byte) 0xf4, (byte) 0x91,
	                (byte) 0xef, (byte) 0x98, 0x7a, 0x33, 0x54, 0x0b, 0x43,
	                (byte) 0xed, (byte) 0xcf, (byte) 0xac, 0x62, (byte) 0xe4,
	                (byte) 0xb3, 0x1c, (byte) 0xa9, (byte) 0xc9, 0x08, (byte) 0xe8,
	                (byte) 0x95, (byte) 0x80, (byte) 0xdf, (byte) 0x94, (byte) 0xfa,
	                0x75, (byte) 0x8f, 0x3f, (byte) 0xa6, 0x47, 0x07, (byte) 0xa7,
	                (byte) 0xfc, (byte) 0xf3, 0x73, 0x17, (byte) 0xba, (byte) 0x83,
	                0x59, 0x3c, 0x19, (byte) 0xe6, (byte) 0x85, 0x4f, (byte) 0xa8,
	                0x68, 0x6b, (byte) 0x81, (byte) 0xb2, 0x71, 0x64, (byte) 0xda,
	                (byte) 0x8b, (byte) 0xf8, (byte) 0xeb, 0x0f, 0x4b, 0x70, 0x56,
	                (byte) 0x9d, 0x35, 0x1e, 0x24, 0x0e, 0x5e, 0x63, 0x58, (byte) 0xd1,
	                (byte) 0xa2, 0x25, 0x22, 0x7c, 0x3b, 0x01, 0x21, 0x78, (byte) 0x87,
	                (byte) 0xd4, 0x00, 0x46, 0x57, (byte) 0x9f, (byte) 0xd3, 0x27,
	                0x52, 0x4c, 0x36, 0x02, (byte) 0xe7, (byte) 0xa0, (byte) 0xc4,
	                (byte) 0xc8, (byte) 0x9e, (byte) 0xea, (byte) 0xbf, (byte) 0x8a,
	                (byte) 0xd2, 0x40, (byte) 0xc7, 0x38, (byte) 0xb5, (byte) 0xa3,
	                (byte) 0xf7, (byte) 0xf2, (byte) 0xce, (byte) 0xf9, 0x61, 0x15,
	                (byte) 0xa1, (byte) 0xe0, (byte) 0xae, 0x5d, (byte) 0xa4,
	                (byte) 0x9b, 0x34, 0x1a, 0x55, (byte) 0xad, (byte) 0x93, 0x32,
	                0x30, (byte) 0xf5, (byte) 0x8c, (byte) 0xb1, (byte) 0xe3, 0x1d,
	                (byte) 0xf6, (byte) 0xe2, 0x2e, (byte) 0x82, 0x66, (byte) 0xca,
	                0x60, (byte) 0xc0, 0x29, 0x23, (byte) 0xab, 0x0d, 0x53, 0x4e, 0x6f,
	                (byte) 0xd5, (byte) 0xdb, 0x37, 0x45, (byte) 0xde, (byte) 0xfd,
	                (byte) 0x8e, 0x2f, 0x03, (byte) 0xff, 0x6a, 0x72, 0x6d, 0x6c, 0x5b,
	                0x51, (byte) 0x8d, 0x1b, (byte) 0xaf, (byte) 0x92, (byte) 0xbb,
	                (byte) 0xdd, (byte) 0xbc, 0x7f, 0x11, (byte) 0xd9, 0x5c, 0x41,
	                0x1f, 0x10, 0x5a, (byte) 0xd8, 0x0a, (byte) 0xc1, 0x31,
	                (byte) 0x88, (byte) 0xa5, (byte) 0xcd, 0x7b, (byte) 0xbd, 0x2d,
	                0x74, (byte) 0xd0, 0x12, (byte) 0xb8, (byte) 0xe5, (byte) 0xb4,
	                (byte) 0xb0, (byte) 0x89, 0x69, (byte) 0x97, 0x4a, 0x0c,
	                (byte) 0x96, 0x77, 0x7e, 0x65, (byte) 0xb9, (byte) 0xf1, 0x09,
	                (byte) 0xc5, 0x6e, (byte) 0xc6, (byte) 0x84, 0x18, (byte) 0xf0,
	                0x7d, (byte) 0xec, 0x3a, (byte) 0xdc, 0x4d, 0x20, 0x79,
	                (byte) 0xee, 0x5f, 0x3e, (byte) 0xd7, (byte) 0xcb, 0x39, 0x48 };
	        public static final int[] FK = { 0xa3b1bac6, 0x56aa3350, 0x677d9197, 0xb27022dc };
	        public static final int[] CK = { 0x00070e15,0x1c232a31,0x383f464d,0x545b6269,
	                0x70777e85,0x8c939aa1,0xa8afb6bd,0xc4cbd2d9,
	                0xe0e7eef5,0xfc030a11,0x181f262d,0x343b4249,
	                0x50575e65,0x6c737a81,0x888f969d,0xa4abb2b9,
	                0xc0c7ced5,0xdce3eaf1,0xf8ff060d,0x141b2229,
	                0x30373e45,0x4c535a61,0x686f767d,0x848b9299,
	                0xa0a7aeb5,0xbcc3cad1,0xd8dfe6ed,0xf4fb0209,
	                0x10171e25,0x2c333a41,0x484f565d,0x646b7279 };
	        private byte sm4Sbox(byte inch) {
	            int i = inch & 0xFF;
	            byte retVal = SBOXTABLE[i];
	            return retVal;
	        }
	        private long sm4Lt(long ka) {
	            long bb = 0L;
	            long c = 0L;
	            byte[] a = new byte[4];
	            byte[] b = new byte[4];
	            putUlongBe(ka, a, 0);
	            b[0] = sm4Sbox(a[0]);
	            b[1] = sm4Sbox(a[1]);
	            b[2] = sm4Sbox(a[2]);
	            b[3] = sm4Sbox(a[3]);
	            bb = getUlongBe(b, 0);
	            c = bb ^ rotl(bb, 2) ^ rotl(bb, 10) ^ rotl(bb, 18) ^ rotl(bb, 24);
	            return c;
	        }
	        private long sm4F(long x0, long x1, long x2, long x3, long rk) {
	            return x0 ^ sm4Lt(x1 ^ x2 ^ x3 ^ rk);
	        }
	        private long sm4CalciRK(long ka) {
	            long bb = 0;
	            long rk = 0;
	            byte[] a = new byte[4];
	            byte[] b = new byte[4];
	            putUlongBe(ka, a, 0);
	            b[0] = sm4Sbox(a[0]);
	            b[1] = sm4Sbox(a[1]);
	            b[2] = sm4Sbox(a[2]);
	            b[3] = sm4Sbox(a[3]);
	            bb = getUlongBe(b, 0);
	            rk = bb ^ rotl(bb, 13) ^ rotl(bb, 23);
	            return rk;
	        }
	        private void sm4_setkey(long [] sk, byte [] key) {
	            long[] mk = new long[4];
	            long[] k = new long[36];
	            int i = 0;
	            mk[0] = getUlongBe(key, 0);
	            mk[1] = getUlongBe(key, 4);
	            mk[2] = getUlongBe(key, 8);
	            mk[3] = getUlongBe(key, 12);
	            k[0] = mk[0] ^ (long) FK[0];
	            k[1] = mk[1] ^ (long) FK[1];
	            k[2] = mk[2] ^ (long) FK[2];
	            k[3] = mk[3] ^ (long) FK[3];
	            for (; i < 32; i++) {                           //(long)
	                k[(i + 4)] = (k[i] ^ sm4CalciRK(k[(i + 1)] ^ k[(i + 2)] ^ k[(i + 3)] ^  CK[i]));
	                sk[i] = k[(i + 4)];
	            }
	        }
	        private void sm4_one_round(long[] sk, byte[] input, byte[] output) {
	            int i = 0;
	            long[] ulbuf = new long[36];
	            ulbuf[0] = getUlongBe(input, 0);
	            ulbuf[1] = getUlongBe(input, 4);
	            ulbuf[2] = getUlongBe(input, 8);
	            ulbuf[3] = getUlongBe(input, 12);
	            while (i < 32) {
	                ulbuf[(i + 4)] = sm4F(ulbuf[i], ulbuf[(i + 1)], ulbuf[(i + 2)], ulbuf[(i + 3)], sk[i]);
	                i++;
	            }
	            putUlongBe(ulbuf[35], output, 0);
	            putUlongBe(ulbuf[34], output, 4);
	            putUlongBe(ulbuf[33], output, 8);
	            putUlongBe(ulbuf[32], output, 12);
	        }
	        private byte[] padding(byte [] input, int mode) {
	            if (input == null) {
	                return null;
	            }
	            byte[] ret = (byte[]) null;
	            if (mode == SM4_ENCRYPT) {
	                int p = 16 - input.length % 16;
	                ret = new byte[input.length + p];
	                System.arraycopy(input, 0, ret, 0, input.length);
	                for (int i = 0; i < p; i++) {
	                    ret[input.length + i] = (byte) p;
	                }
	            } else {
	                int p = input[input.length - 1];
	                ret = new byte[input.length - p];
	                System.arraycopy(input, 0, ret, 0, input.length - p);
	            }
	            return ret;
	        }
	        public void sm4_setkey_enc(SM4Context ctx, byte[] key) throws Exception {
	            if (ctx == null) {
	                throw new Exception("ctx is null!");
	            }
	            if (key == null || key.length < 16) {
	                throw new Exception("key error!");
	            }
	            ctx.mode = SM4_ENCRYPT;
	            sm4_setkey(ctx.sk, key);
	        }
	        public void sm4_setkey_dec(SM4Context ctx, byte[] key) throws Exception {
	            if (ctx == null) {
	                throw new Exception("ctx is null!");
	            }
	            if (key == null || key.length < 16) {
	                throw new Exception("key error!");
	            }
	            int i = 0;
	            ctx.mode = SM4_DECRYPT;
	            sm4_setkey(ctx.sk, key);
	            for (i = 0; i < 16; i++) {
	                swap(ctx.sk, i);
	            }
	        }
	        public byte[] sm4_crypt_ecb(SM4Context ctx, byte[] input) throws Exception {
	            if (input == null) {
	                throw new Exception("input is null!");
	            }
	            if ((ctx.isPadding) & (ctx.mode == SM4_ENCRYPT)) {
	                input = padding(input, SM4_ENCRYPT);
	            }
	            int length = input.length;
	            ByteArrayInputStream bins = new ByteArrayInputStream(input);
	            ByteArrayOutputStream bous = new ByteArrayOutputStream();
	            for (; length > 0; length -= 16) {
	                byte[] in = new byte[16];
	                byte[] out = new byte[16];
	                bins.read(in);
	                sm4_one_round(ctx.sk, in, out);
	                bous.write(out);
	            }
	            byte[] output = bous.toByteArray();
	            if (ctx.isPadding & ctx.mode == SM4_DECRYPT) {
	                output = padding(output, SM4_DECRYPT);
	            }
	            bins.close();
	            bous.close();
	            return output;
	        }
	        public byte[] sm4_crypt_cbc(SM4Context ctx, byte[] iv, byte[] input) throws Exception {
	            if (iv == null || iv.length != 16) {
	                throw new Exception("iv error!");
	            }
	            if (input == null) {
	                throw new Exception("input is null!");
	            }
	            if (ctx.isPadding & ctx.mode == SM4_ENCRYPT) {
	                input = padding(input, SM4_ENCRYPT);
	            }
	            int i = 0;
	            int length = input.length;
	            ByteArrayInputStream bins = new ByteArrayInputStream(input);
	            ByteArrayOutputStream bous = new ByteArrayOutputStream();
	            if (ctx.mode == SM4_ENCRYPT) {
	                for (; length > 0; length -= 16) {
	                    byte[] in = new byte[16];
	                    byte[] out = new byte[16];
	                    byte[] out1 = new byte[16];
	                    bins.read(in);
	                    for (i = 0; i < 16; i++) {
	                        out[i] = ((byte) (in[i] ^ iv[i]));
	                    }
	                    sm4_one_round(ctx.sk, out, out1);
	                    System.arraycopy(out1, 0, iv, 0, 16);
	                    bous.write(out1);
	                }
	            } else {
	                byte[] temp = new byte[16];
	                for (; length > 0; length -= 16) {
	                    byte[] in = new byte[16];
	                    byte[] out = new byte[16];
	                    byte[] out1 = new byte[16];
	                    bins.read(in);
	                    System.arraycopy(in, 0, temp, 0, 16);
	                    sm4_one_round(ctx.sk, in, out);
	                    for (i = 0; i < 16; i++) {
	                        out1[i] = ((byte) (out[i] ^ iv[i]));
	                    }
	                    System.arraycopy(temp, 0, iv, 0, 16);
	                    bous.write(out1);
	                }
	            }
	            byte[] output = bous.toByteArray();
	            if (ctx.isPadding & ctx.mode == SM4_DECRYPT) {
	                output = padding(output, SM4_DECRYPT);
	            }
	            bins.close();
	            bous.close();
	            return output;
	        }
	    }
	   static class Util {
	        public String null2String(Object obj){
	            if(obj==null){
	                return "";
	            }
	            return obj.toString();
	        }
	        /**
	         * 整形转换成网络传输的字节流（字节数组）型数据
	         *
	         * @param num 一个整型数据
	         * @return 4个字节的自己数组
	         */
	        public byte[] intToBytes(int num) {
	            byte[] bytes = new byte[4];
	            bytes[0] = (byte) (0xff & (num >> 0));
	            bytes[1] = (byte) (0xff & (num >> 8));
	            bytes[2] = (byte) (0xff & (num >> 16));
	            bytes[3] = (byte) (0xff & (num >> 24));
	            return bytes;
	        }
	        /**
	         * 四个字节的字节数据转换成一个整形数据
	         *
	         * @param bytes 4个字节的字节数组
	         * @return 一个整型数据
	         */
	        public int byteToInt(byte[] bytes) {
	            int num = 0;
	            int temp;
	            temp = (0x000000ff & (bytes[0])) << 0;
	            num = num | temp;
	            temp = (0x000000ff & (bytes[1])) << 8;
	            num = num | temp;
	            temp = (0x000000ff & (bytes[2])) << 16;
	            num = num | temp;
	            temp = (0x000000ff & (bytes[3])) << 24;
	            num = num | temp;
	            return num;
	        }
	        /**
	         * 长整形转换成网络传输的字节流（字节数组）型数据
	         *
	         * @param num 一个长整型数据
	         * @return 4个字节的自己数组
	         */
	        public byte[] longToBytes(long num) {
	            byte[] bytes = new byte[8];
	            for (int i = 0; i < 8; i++) {
	                bytes[i] = (byte) (0xff & (num >> (i * 8)));
	            }
	            return bytes;
	        }
	        /**
	         * 大数字转换字节流（字节数组）型数据
	         *
	         * @param n
	         * @return
	         */
	        public byte[] byteConvert32Bytes(BigInteger n) {
	            byte  [] tmpd = (byte[]) null;
	            if (n == null) {
	                return null;
	            }
	            if (n.toByteArray().length == 33) {
	                tmpd = new byte[32];
	                System.arraycopy(n.toByteArray(), 1, tmpd, 0, 32);
	            } else if (n.toByteArray().length == 32) {
	                tmpd = n.toByteArray();
	            } else {
	                tmpd = new byte[32];
	                for (int i = 0; i < 32 - n.toByteArray().length; i++) {
	                    tmpd[i] = 0;
	                }
	                System.arraycopy(n.toByteArray(), 0, tmpd, 32 - n.toByteArray().length, n.toByteArray().length);
	            }
	            return tmpd;
	        }
	        /**
	         * 换字节流（字节数组）型数据转大数字
	         *
	         * @param b
	         * @return
	         */
	        public BigInteger byteConvertInteger(byte[] b) {
	            if (b[0] < 0) {
	                byte[] temp = new byte[b.length + 1];
	                temp[0] = 0;
	                System.arraycopy(b, 0, temp, 1, b.length);
	                return new BigInteger(temp);
	            }
	            return new BigInteger(b);
	        }
	        /**
	         * 根据字节数组获得值(十六进制数字)
	         *
	         * @param bytes
	         * @return
	         */
	        public String getHexString(byte[] bytes) {
	            return getHexString(bytes, true);
	        }
	        /**
	         * 根据字节数组获得值(十六进制数字)
	         *
	         * @param bytes
	         * @param upperCase
	         * @return
	         */
	        public String getHexString(byte[] bytes, boolean upperCase) {
	            String ret = "";
	            for (int i = 0; i < bytes.length; i++) {
	                ret += Integer.toString((bytes[i] & 0xff) + 0x100, 16).substring(1);
	            }
	            return upperCase ? ret.toUpperCase() : ret;
	        }
	        /**
	         * 打印十六进制字符串
	         *
	         * @param bytes
	         */
	        public void printHexString(byte[] bytes) {
	            for (int i = 0; i < bytes.length; i++) {
	                String hex = Integer.toHexString(bytes[i] & 0xFF);
	                if (hex.length() == 1) {
	                    hex = '0' + hex;
	                }
	            }
	        }
	        /**
	         * Convert hex string to byte[]
	         *
	         * @param hexString the hex string
	         * @return byte[]
	         */
	        public byte[] hexStringToBytes(String hexString) {
	            if (hexString == null || hexString.equals("")) {
	                return null;
	            }
	            hexString = hexString.toUpperCase();
	            int length = hexString.length() / 2;
	            char[] hexChars = hexString.toCharArray();
	            byte[] d = new byte[length];
	            for (int i = 0; i < length; i++) {
	                int pos = i * 2;
	                d[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
	            }
	            return d;
	        }
	        /**
	         * Convert char to byte
	         *
	         * @param c char
	         * @return byte
	         */
	        public byte charToByte(char c) {
	            return (byte) "0123456789ABCDEF".indexOf(c);
	        }
	        /**
	         * 用于建立十六进制字符的输出的小写字符数组
	         */
	        private final char[] DIGITS_LOWER = {'0', '1', '2', '3', '4', '5',
	                '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
	        /**
	         * 用于建立十六进制字符的输出的大写字符数组
	         */
	        private final char[] DIGITS_UPPER = {'0', '1', '2', '3', '4', '5',
	                '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
	        /**
	         * 将字节数组转换为十六进制字符数组
	         *
	         * @param data byte[]
	         * @return 十六进制char[]
	         */
	        public char[] encodeHex(byte[] data) {
	            return encodeHex(data, true);
	        }
	        /**
	         * 将字节数组转换为十六进制字符数组
	         *
	         * @param data        byte[]
	         * @param toLowerCase <code>true</code> 传换成小写格式 ， <code>false</code> 传换成大写格式
	         * @return 十六进制char[]
	         */
	        public char[] encodeHex(byte[] data, boolean toLowerCase) {
	            return encodeHex(data, toLowerCase ? DIGITS_LOWER : DIGITS_UPPER);
	        }
	        /**
	         * 将字节数组转换为十六进制字符数组
	         *
	         * @param data     byte[]
	         * @param toDigits 用于控制输出的char[]
	         * @return 十六进制char[]
	         */
	        protected char[] encodeHex(byte[] data, char[] toDigits) {
	            int l = data.length;
	            char[] out = new char[l << 1];
	            // two characters form the hex value.
	            for (int i = 0, j = 0; i < l; i++) {
	                out[j++] = toDigits[(0xF0 & data[i]) >>> 4];
	                out[j++] = toDigits[0x0F & data[i]];
	            }
	            return out;
	        }
	        /**
	         * 将字节数组转换为十六进制字符串
	         *
	         * @param data byte[]
	         * @return 十六进制String
	         */
	        public String encodeHexString(byte[] data) {
	            return encodeHexString(data, true);
	        }
	        /**
	         * 将字节数组转换为十六进制字符串
	         *
	         * @param data        byte[]
	         * @param toLowerCase <code>true</code> 传换成小写格式 ， <code>false</code> 传换成大写格式
	         * @return 十六进制String
	         */
	        public String encodeHexString(byte[] data, boolean toLowerCase) {
	            return encodeHexString(data, toLowerCase ? DIGITS_LOWER : DIGITS_UPPER);
	        }
	        /**
	         * 将字节数组转换为十六进制字符串
	         *
	         * @param data     byte[]
	         * @param toDigits 用于控制输出的char[]
	         * @return 十六进制String
	         */
	        protected String encodeHexString(byte[] data, char[] toDigits) {
	            return new String(encodeHex(data, toDigits));
	        }
	        /**
	         * 将十六进制字符数组转换为字节数组
	         *
	         * @param data 十六进制char[]
	         * @return byte[]
	         * @throws RuntimeException 如果源十六进制字符数组是一个奇怪的长度，将抛出运行时异常
	         */
	        public byte[] decodeHex(char[] data) {
	            int len = data.length;
	            if ((len & 0x01) != 0) {
	                throw new RuntimeException("Odd number of characters.");
	            }
	            byte[] out = new byte[len >> 1];
	            // two characters form the hex value.
	            for (int i = 0, j = 0; j < len; i++) {
	                int f = toDigit(data[j], j) << 4;
	                j++;
	                f = f | toDigit(data[j], j);
	                j++;
	                out[i] = (byte) (f & 0xFF);
	            }
	            return out;
	        }
	        /**
	         * 将十六进制字符转换成一个整数
	         *
	         * @param ch    十六进制char
	         * @param index 十六进制字符在字符数组中的位置
	         * @return 一个整数
	         * @throws RuntimeException 当ch不是一个合法的十六进制字符时，抛出运行时异常
	         */
	        protected int toDigit(char ch, int index) {
	            int digit = Character.digit(ch, 16);
	            if (digit == -1) {
	                throw new RuntimeException("Illegal hexadecimal character " + ch
	                        + " at index " + index);
	            }
	            return digit;
	        }
	        /**
	         * 数字字符串转ASCII码字符串
	         * @return ASCII字符串
	         */
	        public String stringToAsciiString(String content) {
	            String result = "";
	            int max = content.length();
	            for (int i = 0; i < max; i++) {
	                char c = content.charAt(i);
	                String b = Integer.toHexString(c);
	                result = result + b;
	            }
	            return result;
	        }
	        /**
	         * 十六进制转字符串
	         *
	         * @param hexString  十六进制字符串
	         * @param encodeType 编码类型4：Unicode，2：普通编码
	         * @return 字符串
	         */
	        public String hexStringToString(String hexString, int encodeType) {
	            String result = "";
	            int max = hexString.length() / encodeType;
	            for (int i = 0; i < max; i++) {
	                char c = (char) hexStringToAlgorism(hexString
	                        .substring(i * encodeType, (i + 1) * encodeType));
	                result += c;
	            }
	            return result;
	        }
	        /**
	         * 十六进制字符串装十进制
	         *
	         * @param hex 十六进制字符串
	         * @return 十进制数值
	         */
	        public int hexStringToAlgorism(String hex) {
	            hex = hex.toUpperCase();
	            int max = hex.length();
	            int result = 0;
	            for (int i = max; i > 0; i--) {
	                char c = hex.charAt(i - 1);
	                int algorism = 0;
	                if (c >= '0' & c <= '9') {
	                    algorism = c - '0';
	                } else {
	                    algorism = c - 55;
	                }
	                result += Math.pow(16, max - i) * algorism;
	            }
	            return result;
	        }
	        /**
	         * 十六转二进制
	         *
	         * @param hex 十六进制字符串
	         * @return 二进制字符串
	         */
	        public String hexStringToBinary(String hex) {
	            hex = hex.toUpperCase();
	            String result = "";
	            int max = hex.length();
	            for (int i = 0; i < max; i++) {
	                char c = hex.charAt(i);
	                switch (c) {
	                    case '0':
	                        result += "0000";
	                        break;
	                    case '1':
	                        result += "0001";
	                        break;
	                    case '2':
	                        result += "0010";
	                        break;
	                    case '3':
	                        result += "0011";
	                        break;
	                    case '4':
	                        result += "0100";
	                        break;
	                    case '5':
	                        result += "0101";
	                        break;
	                    case '6':
	                        result += "0110";
	                        break;
	                    case '7':
	                        result += "0111";
	                        break;
	                    case '8':
	                        result += "1000";
	                        break;
	                    case '9':
	                        result += "1001";
	                        break;
	                    case 'A':
	                        result += "1010";
	                        break;
	                    case 'B':
	                        result += "1011";
	                        break;
	                    case 'C':
	                        result += "1100";
	                        break;
	                    case 'D':
	                        result += "1101";
	                        break;
	                    case 'E':
	                        result += "1110";
	                        break;
	                    case 'F':
	                        result += "1111";
	                        break;
	                    default:
	                        break;
	                }
	            }
	            return result;
	        }
	        /**
	         * ASCII码字符串转数字字符串
	         *
	         * @param content ASCII字符串
	         * @return 字符串
	         */
	        public String asciiStringToString(String content) {
	            String result = "";
	            int length = content.length() / 2;
	            for (int i = 0; i < length; i++) {
	                String c = content.substring(i * 2, i * 2 + 2);
	                int a = hexStringToAlgorism(c);
	                char b = (char) a;
	                String d = String.valueOf(b);
	                result += d;
	            }
	            return result;
	        }
	        /**
	         * 将十进制转换为指定长度的十六进制字符串
	         *
	         * @param algorism  int 十进制数字
	         * @param maxLength int 转换后的十六进制字符串长度
	         * @return String 转换后的十六进制字符串
	         */
	        public String algorismToHexString(int algorism, int maxLength) {
	            String result = "";
	            result = Integer.toHexString(algorism);
	            if (result.length() % 2 == 1) {
	                result = "0" + result;
	            }
	            return patchHexString(result.toUpperCase(), maxLength);
	        }
	        /**
	         * 字节数组转为普通字符串（ASCII对应的字符）
	         *
	         * @param bytearray byte[]
	         * @return String
	         */
	        public String byteToString(byte[] bytearray) {
	            String result = "";
	            char temp;
	            int length = bytearray.length;
	            for (int i = 0; i < length; i++) {
	                temp = (char) bytearray[i];
	                result += temp;
	            }
	            return result;
	        }
	        /**
	         * 二进制字符串转十进制
	         *
	         * @param binary 二进制字符串
	         * @return 十进制数值
	         */
	        public int binaryToAlgorism(String binary) {
	            int max = binary.length();
	            int result = 0;
	            for (int i = max; i > 0; i--) {
	                char c = binary.charAt(i - 1);
	                int algorism = c - '0';
	                result += Math.pow(2, max - i) * algorism;
	            }
	            return result;
	        }
	        /**
	         * 十进制转换为十六进制字符串
	         *
	         * @param algorism int 十进制的数字
	         * @return String 对应的十六进制字符串
	         */
	        public String algorismToHEXString(int algorism) {
	            String result = "";
	            result = Integer.toHexString(algorism);
	            if (result.length() % 2 == 1) {
	                result = "0" + result;
	            }
	            result = result.toUpperCase();
	            return result;
	        }
	        /**
	         * HEX字符串前补0，主要用于长度位数不足。
	         *
	         * @param str       String 需要补充长度的十六进制字符串
	         * @param maxLength int 补充后十六进制字符串的长度
	         * @return 补充结果
	         */
	        public String patchHexString(String str, int maxLength) {
	            String temp = "";
	            for (int i = 0; i < maxLength - str.length(); i++) {
	                temp = "0" + temp;
	            }
	            str = (temp + str).substring(0, maxLength);
	            return str;
	        }
	        /**
	         * 将一个字符串转换为int
	         *
	         * @param s          String 要转换的字符串
	         * @param defaultInt int 如果出现异常,默认返回的数字
	         * @param radix      int 要转换的字符串是什么进制的,如16 8 10.
	         * @return int 转换后的数字
	         */
	        public int parseToInt(String s, int defaultInt, int radix) {
	            int i = 0;
	            try {
	                i = Integer.parseInt(s, radix);
	            } catch (NumberFormatException ex) {
	                i = defaultInt;
	            }
	            return i;
	        }
	        /**
	         * 将一个十进制形式的数字字符串转换为int
	         *
	         * @param s          String 要转换的字符串
	         * @param defaultInt int 如果出现异常,默认返回的数字
	         * @return int 转换后的数字
	         */
	        public int parseToInt(String s, int defaultInt) {
	            int i = 0;
	            try {
	                i = Integer.parseInt(s);
	            } catch (NumberFormatException ex) {
	                i = defaultInt;
	            }
	            return i;
	        }
	        /**
	         * 十六进制串转化为byte数组
	         *
	         * @return the array of byte
	         */
	        public byte[] hexToByte(String hex)
	                throws IllegalArgumentException {
	            if (hex.length() % 2 != 0) {
	                throw new IllegalArgumentException();
	            }
	            char[] arr = hex.toCharArray();
	            byte[] b = new byte[hex.length() / 2];
	            for (int i = 0, j = 0, l = hex.length(); i < l; i++, j++) {
	                String swap = "" + arr[i++] + arr[i];
	                int byteint = Integer.parseInt(swap, 16) & 0xFF;
	                b[j] = new Integer(byteint).byteValue();
	            }
	            return b;
	        }
	        /**
	         * 字节数组转换为十六进制字符串
	         *
	         * @param b byte[] 需要转换的字节数组
	         * @return String 十六进制字符串
	         */
	        public String byteToHex(byte [] b) {
	            if (b == null) {
	                throw new IllegalArgumentException(
	                        "Argument b ( byte array ) is null! ");
	            }
	            String hs = "";
	            String stmp = "";
	            for (int n = 0; n < b.length; n++) {
	                stmp = Integer.toHexString(b[n] & 0xff);
	                if (stmp.length() == 1) {
	                    hs = hs + "0" + stmp;
	                } else {
	                    hs = hs + stmp;
	                }
	            }
	            return hs.toUpperCase();
	        }
	        public byte[] subByte(byte[] input, int startIndex, int length) {
	            byte[] bt = new byte[length];
	            for (int i = 0; i < length; i++) {
	                bt[i] = input[i + startIndex];
	            }
	            return bt;
	        }
	    }
	   public static void main(String[] args) {
			//a2Syca7Av9kuc6DbTsgjfa1qjkVqWLr6KOhoT4jLWG4=
		String strEncode = Sm4Util.strEncode("22447777", "00d11fd69c844272");
		System.out.println(strEncode);
		System.out.println(strEncode.length());
	}
}
