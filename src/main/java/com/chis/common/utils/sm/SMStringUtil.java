package com.chis.common.utils.sm;

import java.io.*;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/***
 * <p>类描述: String工具类 </p>
 *
 * @ClassAuthor mxp , 2018/11/2 , SMStringUtil
 */
@SuppressWarnings({"unchecked", "rawtypes"})
public class SMStringUtil {

    private static final int maxBuffer = 1024;

    public static String trimFirstTag(String srcStr, String firstTag) {
        if (srcStr == null || srcStr.equals(""))
            return "";
        if (firstTag == null || firstTag.equals(""))
            return srcStr;
        String trimStr = srcStr.substring(srcStr.indexOf(firstTag)
                + firstTag.length(), srcStr.length());
        return trimStr;

    }

    public static String[] strSplit(String src, String spit) {
        int index = 0;
        Vector vector = new Vector();
        String as[] = new String[1];

        if (src == null)
            return new String[0];
        if (spit == null)
            return null;
        if (src.trim().equals(""))
            return new String[0];

        index = src.indexOf(spit);
        String tem = "";
        while (index != -1) {
            if (index != 0 && src.substring(index - 1, index).equals("\\")) {
                tem = tem + src.substring(0, index - 1) + spit;
                src = src.substring(index + 1);
                index = src.indexOf(spit);
            } else {
                String tem2 = tem.equals("") ? src.substring(0, index).trim()
                        : tem + src.substring(0, index).trim();
                src = src.substring(index + 1);
                vector.addElement(tem2);
                tem = "";
                index = src.indexOf(spit);
            }// end if
        }// end while

        vector.addElement(src.trim());
        as = new String[vector.size()];
        for (int j = 0; j < vector.size(); j++)
            as[j] = (String) vector.elementAt(j);

        return as;
    }

    /* 页面解码%u中文问题 */
    public static String unescape(String src) {
        StringBuffer tmp = new StringBuffer();
        tmp.ensureCapacity(src.length());
        int lastPos = 0, pos = 0;
        char ch;
        while (lastPos < src.length()) {
            pos = src.indexOf("%", lastPos);
            if (pos == lastPos) {
                if (src.charAt(pos + 1) == 'u') {
                    ch = (char) Integer.parseInt(src
                            .substring(pos + 2, pos + 6), 16);
                    tmp.append(ch);
                    lastPos = pos + 6;
                } else {
                    ch = (char) Integer.parseInt(src
                            .substring(pos + 1, pos + 3), 16);
                    tmp.append(ch);
                    lastPos = pos + 3;
                }
            } else {
                if (pos == -1) {
                    tmp.append(src.substring(lastPos));
                    lastPos = src.length();
                } else {
                    tmp.append(src.substring(lastPos, pos));
                    lastPos = pos;
                }
            }
        }
        return tmp.toString();
    }

    private final static DecimalFormat nf = new DecimalFormat("###,##0.00");

    public static String formatToMoney(String s) {
        if (s == null || s.equals("")) {
            return "0.00";
        }
        try {
            return formatToMoney(Double.parseDouble(s));
        } catch (Exception e) {
            return s;
        }
    }

    public static String formatToMoney(Double d) {
        try {
            return nf.format(d);
        } catch (Exception e) {
            return String.valueOf(d);
        }
    }

    public static List<String> fileStream2StrList(String src, String charset) {
        List<String> list = new ArrayList<String>();
        BufferedReader br = null;
        String line = null;
        try {
            br = new BufferedReader(new InputStreamReader(new FileInputStream(src), charset));
            while ((line = br.readLine()) != null) {
                list.add(line);
            }
        } catch (Exception e) {

        } finally {
            try {
                br.close();
            } catch (IOException e) {

            }
        }
        return list;
    }

    public static String stream2Str(InputStream in, String encoding,
                                    int bufferLen) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        String str = null;
        if (bufferLen == -1)
            bufferLen = maxBuffer;
        try {
            byte[] buffer = new byte[bufferLen];
            int bytesRead = -1;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            byte[] obs = out.toByteArray();
            str = new String(obs, encoding);
            return str;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                out.close();
            } catch (IOException ex) {
            }
        }
        return str;
    }


    public static byte[] stream2ByteArray(InputStream in, int bufferLen) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        if (bufferLen == -1)
            bufferLen = maxBuffer;
        try {
            byte[] buffer = new byte[bufferLen];
            int bytesRead = -1;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            byte[] obs = out.toByteArray();
            return obs;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                out.close();
            } catch (IOException ex) {
            }
        }
        return null;
    }

    public static String file2Str(String path) {
        try {
            FileInputStream fis = new FileInputStream(path);
            return SMStringUtil.stream2Str(fis, "utf-8", 1024);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static boolean isEmpty(String str) {
        if (str != null && str.trim().length() > 0) {
            return false;
        }
        return true;
    }

    public static String byte2hex(byte b) {
        return "" + "0123456789abcdef".charAt(0xF & b >> 4)
                + "0123456789abcdef".charAt(b & 0xF);
    }

    public static String byteArray2hex(byte[] bs) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < bs.length; i++) {
            sb.append(byte2hex(bs[i]));
        }
        return sb.toString();
    }

    public static byte hex2byte(String hex) {
        byte r = 0;
        char c = hex.charAt(0);
        byte l = (byte) ((c >= 'A') ? c - 'A' + 10 : c - '0');
        c = hex.charAt(1);
        r = (byte) ((c >= 'A') ? c - 'A' + 10 : c - '0');
        return (byte) ((l & 0xF) << 4 | r & 0xF);
    }

    public static byte[] hex2byteArray416(String hex) {
        hex = hex.toUpperCase();
        if (hex.length() % 2 != 0) {
            return null;
        }
        int len = hex.length() / 2;
        byte[] bs = new byte[len];
        for (int i = 0; i < len; i++) {
            String t = hex.substring(i * 2, 2);
            bs[i] = hex2byte(t);
        }
        return bs;
    }

    public static byte[] hex2byteArray418(String hex) {
        hex = hex.toUpperCase();
        if (hex.length() % 2 != 0) {
            return null;
        }
        int len = hex.length() / 2;
        byte[] bs = new byte[len];
        for (int i = 0; i < len; i++) {
            String t = hex.substring(i * 2, i * 2 + 2);
            bs[i] = hex2byte(t);
        }
        return bs;
    }

    public static String[] collection2Array(Collection c) {
        Iterator it = c.iterator();
        String[] strArr = new String[c.size()];
        int i = 0;
        while (it.hasNext()) {
            String k = it.next().toString();
            strArr[i] = k;
            i++;
        }
        return strArr;
    }

    public static String decoStr(String str, int length, String suffix) {
        String cn = "";
        if (str == null || "".equals(str)) {
            return "";
        }
        if (str.length() > length) {
            cn = str.substring(0, length);
        } else {
            cn = str;
        }
        if (suffix != null && !"".equals(suffix)) {
            cn += suffix;
        }
        return cn;
    }

    public static boolean isEmailFormat(String email) {
        Pattern p = Pattern.compile("^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$");
        Matcher m = p.matcher(email);
        return m.find();
    }

    public static boolean isMobileFormat(String phone) {
        Pattern p = Pattern.compile("^1[0-9]{10}$");
        Matcher m = p.matcher(phone);
        return m.find();
    }

    public static boolean isNumber(String number) {
        Pattern p = Pattern.compile("^\\d+$");
        Matcher m = p.matcher(number);
        return m.find();
    }

    public static String addStarStr(int len, String str) {
        if (len > 5) {
            str += "******";
        } else {
            str += "***";
        }
        return str;
    }

    public static boolean isBirthday(String dateStr) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        if (SMStringUtil.isEmpty(dateStr)) {
            return false;
        }
        if (dateStr.trim().length() != 8) {
            return false;
        }
        try {
            sf.parse(dateStr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static String cleanChars(String str) {
        byte[] bs = {0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x08, 0x0b, 0x0c, 0x0e, 0x10, 0x11,
                0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f};
        StringBuilder sb = new StringBuilder();

        String c = "";
        for (int i = 0; i < str.length(); i++) {
            c = str.substring(i, i + 1);
            byte[] tbs = c.getBytes();
            if (tbs.length == 1) {
                byte tb = tbs[0];
                if (tb == 0x07) {
                    sb.append("·");
                } else {
                    boolean flag = false;
                    for (int j = 0; j < bs.length; j++) {
                        if (tb == bs[j]) {
                            flag = true;
                            break;
                        }
                    }
                    if (!flag)
                        sb.append(c);
                }
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    public static String append(String oriStr, String appendStr, int length, boolean isHead) {
        StringBuilder sb = new StringBuilder();
        if (oriStr == null) {
            return null;
        }
        sb.append(oriStr);
        while (sb.length() < length) {
            if (isHead) {
                oriStr = appendStr + oriStr;
                sb.insert(0, appendStr);
            } else {
                sb.append(appendStr);
            }
        }
        return sb.toString();
    }

    public static byte[] toByteArray(String hexString) {
        hexString = hexString.toLowerCase();
        byte[] byteArray = new byte[hexString.length() / 2];
        int k = 0;
        for (int i = 0; i < byteArray.length; i++) {
            byte high = (byte) (Character.digit(hexString.charAt(k), 16) & 0xFF);
            byte low = (byte) (Character.digit(hexString.charAt(k + 1), 16) & 0xFF);
            byteArray[i] = ((byte) (high << 4 | low));
            k += 2;
        }
        return byteArray;
    }

    public static String toHexString(byte[] byteArray) {
        if ((byteArray == null) || (byteArray.length < 1)) {
            throw new IllegalArgumentException("this byteArray must not be null or empty");
        }
        StringBuilder hexString = new StringBuilder();
        for (int i = 0; i < byteArray.length; i++) {
            if ((byteArray[i] & 0xFF) < 16) {
                hexString.append("0");
            }
            hexString.append(Integer.toHexString(0xFF & byteArray[i]));
        }
        return hexString.toString().toUpperCase();
    }

    public static String strXor(String str1, String str2) {
        byte[] b1 = toByteArray(str1);
        byte[] b2 = toByteArray(str2);
        byte[] shortbytes;
        byte[] longbytes;
        if (b1.length >= b2.length) {
            longbytes = b1;
            shortbytes = b2;
        } else {
            longbytes = b2;
            shortbytes = b1;
        }
        byte[] xorstr = new byte[longbytes.length];
        for (int i = 0; i < shortbytes.length; i++) {
            xorstr[i] = ((byte) (shortbytes[i] ^ longbytes[i]));
        }
        for (int i = 0; i < longbytes.length; i++) {
            xorstr[i] = longbytes[i];
        }
        return new String(toHexString(xorstr));
    }


    public static boolean isIpv4(String ipAddress) {
        String ip = "^(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|[1-9])\\." + "(00?\\d|1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)\\." + "(00?\\d|1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)\\."
                + "(00?\\d|1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)$";
        Pattern pattern = Pattern.compile(ip);
        Matcher matcher = pattern.matcher(ipAddress);
        return matcher.matches();
    }

    public static boolean isTelNo(String telNo) {
        String tel = "^([0-9]{3,4}-)?[0-9]{7,8}(-[0-9]{1,4})?$";
        Pattern pattern = Pattern.compile(tel);
        Matcher matcher = pattern.matcher(telNo);
        return matcher.matches();
    }

    public static boolean isIdCard(String idCard) {
        String tel = "^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
        Pattern pattern = Pattern.compile(tel);
        Matcher matcher = pattern.matcher(idCard);
        return matcher.matches();
    }


    public static boolean isChinese(String str) {
        String reg = "[\\u4e00-\\u9fa5]+";
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

    public static boolean isName(String name) {
        name = name.toLowerCase();
        String chs = "0123456789abcedfghijklmnopqrstuvwxyz";
        for (int i = 0; i < name.length(); i++) {
            String c = String.valueOf(name.charAt(i));
            if (chs.indexOf(c) == -1) {
                if (!isChinese(c)) {
                    return false;
                }
            }
        }
        return true;
    }

    public static void main(String args[]) {
        //System.out.println(SMStringUtil.isIpv4("10.168.2.a"));
        //System.out.println(SMStringUtil.isBirthday("198020a"));
        String str = "和dafa特个1";
        System.out.println(SMStringUtil.isName(str));
    }

}
