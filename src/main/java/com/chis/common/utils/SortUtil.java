package com.chis.common.utils;

import org.apache.commons.collections.CollectionUtils;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
/**
 *  <p>类描述：排序工具类
 *  码表排序 20230617
 *  单个属性排序-Int-升序 20230731</p>
 * @ClassAuthor hsj 2023-07-06 16:45
 */
public class SortUtil {
    /**
     *  <p>方法描述：码表排序</p>
     * @MethodAuthor hsj 2023-06-17 10:34
     */
    public static <V> void sortCodeByField(List<V> list, Class<V> clazz, final String fieldName) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        String[] methodName = {fieldName,"getNum"};
        final Method[] method = getMethodByNames(clazz,methodName);
        String[] methodName1 = {fieldName,"getCodeNo"};
        final Method[] method1 = getMethodByNames(clazz,methodName1);
        Collections.sort(list, new Comparator<V>() {
            @Override
            public int compare(V o1, V o2) {
                Object obj = sortCode(o1,method);
                Integer num1 = obj == null ? null : Integer.parseInt(String.valueOf(obj));
                obj = sortCode(o2,method);
                Integer num2 = obj == null ? null : Integer.parseInt(String.valueOf(obj));
                obj = sortCode(o1,method1);
                String code1 =  obj == null ? null : obj.toString();
                obj = sortCode(o2,method1) ;
                String code2 =  obj == null ? null : obj.toString();
                if (ObjectUtil.isNotNull(num1) && ObjectUtil.isNotNull(num2)) {
                    int i = num1.compareTo(num2);
                    if(i == 0){
                        return code1.compareTo(code2);
                    }
                    return i;
                }else if(ObjectUtil.isNull(num1)  && ObjectUtil.isNull(num2)){
                    return code1.compareTo(code2);
                } else if (ObjectUtil.isNull(num1) ) {
                    return -1;
                } else if (ObjectUtil.isNull(num2)) {
                    return 1;
                }
                return code1.compareTo(code2);
            }
        });
    }
    private static Method[] getMethodByNames(Class<?> clazz, String[] methodName) {
        Method[] method = new Method[methodName.length];
        // 入参不能为空
        if (null == clazz || methodName == null || methodName.length == 0) {
            return method;
        }

        try {
            for(int i = 0;i<methodName.length;i++){
                Method m = clazz.getDeclaredMethod(methodName[i]);
                method[i] =  m;
                clazz = m.getReturnType();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return method;
    }
    //取值
    public static <V> Object sortCode(V t, Method[] method) {
        // 入参非法行校验
        if (null == t ||  null == method) {
            return null;
        }
        Object key = null;
        Object valTmp = t;
        try {

            for(int i = 0;i<method.length;i++){
                key = method[i].invoke(valTmp);
                valTmp = key;
                if(valTmp == null){
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return  valTmp;
    }
    /**
     *  <p>方法描述：单个属性排序-Int-升序</p>
     * @MethodAuthor hsj 2023-07-31 10:18
     */
    public static <V> void sortIntSingleByField(List<V> list, Class<V> clazz, final String fieldName) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        final Method method = getMethodByName(clazz,fieldName);
        Collections.sort(list, new Comparator<V>() {
            @Override
            public int compare(V o1, V o2) {
                Object obj = sortVal(o1,method);
                Integer num1 = obj == null ? null : Integer.parseInt(String.valueOf(obj));
                obj = sortVal(o2,method);
                Integer num2 = obj == null ? null : Integer.parseInt(String.valueOf(obj));
                if(num1 == null && num2 == null){
                    return 0;
                }
                if(num1 == null){
                    return 1;
                }
                if(num2 == null) {
                    return -1;
                }
                return num1.compareTo(num2);
            }
        });
    }
    /**
     * 根据类和方法名，获取方法对象
     *
     * @param clazz
     * @param methodName
     * @return
     */
    private static Method getMethodByName(Class<?> clazz, String methodName) {
        Method method = null;
        // 入参不能为空
        if (null == clazz || StringUtils.isBlank(methodName)) {
            return method;
        }
        try {
            method = clazz.getDeclaredMethod(methodName);
        } catch (Exception e) {
           e.printStackTrace();
        }
        return method;
    }
    //取值
    public static <V> Object sortVal(V t, Method method) {
        // 入参非法行校验
        if (null == t ||  null == method) {
            return null;
        }
        Object valTmp = t;
        try {
            Object  key = method.invoke(valTmp);
            valTmp = key;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return  valTmp;
    }
}
