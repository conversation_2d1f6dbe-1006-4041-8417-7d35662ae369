package com.chis.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

import com.chis.common.bean.GpsBean;

/**
 * 各类地图坐标转换工具类
 * 
 * <AUTHOR>
 * 
 */
public class PostionUtil {
	public static final String BAIDU_LBS_TYPE = "bd09ll";

	public static double pi = 3.1415926535897932384626;
	public static double a = 6378245.0;
	public static double ee = 0.00669342162296594323;

	/**
	 * 84 to 火星坐标系 (GCJ-02) World Geodetic System ==> Mars Geodetic System
	 * 
	 * @param lat
	 * @param lon
	 * @return
	 */
	public static GpsBean gps84_To_Gcj02(double lat, double lon) {
		if (outOfChina(lat, lon)) {
			return null;
		}
		double dLat = transformLat(lon - 105.0, lat - 35.0);
		double dLon = transformLon(lon - 105.0, lat - 35.0);
		double radLat = lat / 180.0 * pi;
		double magic = Math.sin(radLat);
		magic = 1 - ee * magic * magic;
		double sqrtMagic = Math.sqrt(magic);
		dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
		dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
		double mgLat = lat + dLat;
		double mgLon = lon + dLon;
		return new GpsBean(mgLat, mgLon);
	}

	/**
	 * * 火星坐标系 (GCJ-02) to 84 * * @param lon * @param lat * @return
	 * */
	public static GpsBean gcj_To_Gps84(double lat, double lon) {
		GpsBean gps = transform(lat, lon);
		double lontitude = lon * 2 - gps.getWgLon();
		double latitude = lat * 2 - gps.getWgLat();
		return new GpsBean(latitude, lontitude);
	}

	/**
	 * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换算法 将 GCJ-02 坐标转换成 BD-09 坐标
	 * 
	 * @param gg_lat
	 * @param gg_lon
	 */
	public static GpsBean gcj02_To_Bd09(double gg_lat, double gg_lon) {
		double x = gg_lon, y = gg_lat;
		double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * pi);
		double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * pi);
		double bd_lon = z * Math.cos(theta) + 0.0065;
		double bd_lat = z * Math.sin(theta) + 0.006;
		return new GpsBean(bd_lat, bd_lon);
	}

	/**
	 * * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换算法 * * 将 BD-09 坐标转换成GCJ-02 坐标 * * @param
	 * bd_lat * @param bd_lon * @return
	 */
	public static GpsBean bd09_To_Gcj02(double bd_lat, double bd_lon) {
		double x = bd_lon - 0.0065, y = bd_lat - 0.006;
		double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * pi);
		double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * pi);
		double gg_lon = z * Math.cos(theta);
		double gg_lat = z * Math.sin(theta);
		return new GpsBean(gg_lat, gg_lon);
	}

	/**
	 * (BD-09)-->84
	 * 
	 * @param bd_lat
	 * @param bd_lon
	 * @return
	 */
	public static GpsBean bd09_To_Gps84(double bd_lat, double bd_lon) {

		GpsBean gcj02 = bd09_To_Gcj02(bd_lat, bd_lon);
		GpsBean map84 = gcj_To_Gps84(gcj02.getWgLat(), gcj02.getWgLon());
		return map84;

	}

	/**
	 * 
	 * @param lat
	 * @param lon
	 * @return
	 */
	public static boolean outOfChina(double lat, double lon) {
		if (lon < 72.004 || lon > 137.8347)
			return true;
		if (lat < 0.8293 || lat > 55.8271)
			return true;
		return false;
	}

	public static GpsBean transform(double lat, double lon) {
		if (outOfChina(lat, lon)) {
			return new GpsBean(lat, lon);
		}
		double dLat = transformLat(lon - 105.0, lat - 35.0);
		double dLon = transformLon(lon - 105.0, lat - 35.0);
		double radLat = lat / 180.0 * pi;
		double magic = Math.sin(radLat);
		magic = 1 - ee * magic * magic;
		double sqrtMagic = Math.sqrt(magic);
		dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
		dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
		double mgLat = lat + dLat;
		double mgLon = lon + dLon;
		return new GpsBean(mgLat, mgLon);
	}

	public static double transformLat(double x, double y) {
		double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y
				+ 0.2 * Math.sqrt(Math.abs(x));
		ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
		ret += (20.0 * Math.sin(y * pi) + 40.0 * Math.sin(y / 3.0 * pi)) * 2.0 / 3.0;
		ret += (160.0 * Math.sin(y / 12.0 * pi) + 320 * Math.sin(y * pi / 30.0)) * 2.0 / 3.0;
		return ret;
	}

	public static double transformLon(double x, double y) {
		double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1
				* Math.sqrt(Math.abs(x));
		ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
		ret += (20.0 * Math.sin(x * pi) + 40.0 * Math.sin(x / 3.0 * pi)) * 2.0 / 3.0;
		ret += (150.0 * Math.sin(x / 12.0 * pi) + 300.0 * Math.sin(x / 30.0
				* pi)) * 2.0 / 3.0;
		return ret;
	}

	/**
	 * 经纬度转GPS84
	 * 
	 * @param du
	 * @param fen
	 * @param miao
	 * @return
	 */
	public static double jwd_To_Gps84(BigDecimal d, BigDecimal f, BigDecimal m) {
		return d.add(f.divide(new BigDecimal("60"), 6, RoundingMode.HALF_UP))
				.add(m.divide(new BigDecimal("3600"), 6, RoundingMode.HALF_UP))
				.doubleValue();
	}

	/**
	 * GPS84转经纬度
	 * 
	 * @param vals 经度或纬度
	 * @return 度（整数）分（整数）秒（精确到2位小数）
	 */
	public static String[] Gps84_To_jwd(double vals) {
		Integer d = (int) Math.floor(vals);
		Integer f = (int) Math.floor((vals - d) * 60);
		double m = ((vals - d) * 60 - f) * 60;
		BigDecimal b = new BigDecimal(m);
		String sec = b.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
		return new String[] { d.toString(), f.toString(), sec};
	}
	
	
	/**
	 * 经纬度转百度坐标
	 * 
	 * @param latDu
	 * @param latFen
	 * @param latMiao
	 * @param lonDu
	 * @param lonFen
	 * @param lonMiao
	 * @return
	 */
	public static GpsBean jwd_To_Bd09(BigDecimal latDu, BigDecimal latFen,
			BigDecimal latMiao, BigDecimal lonDu, BigDecimal lonFen, BigDecimal lonMiao) {
		GpsBean gpsBean = gps84_To_Gcj02(jwd_To_Gps84(latDu, latFen, latMiao),
				jwd_To_Gps84(lonDu, lonFen, lonMiao));
		if (gpsBean != null) {
			return gcj02_To_Bd09(gpsBean.getWgLat(), gpsBean.getWgLon());
		}
		return null;
	}
	
	
	/**
	 * google坐标转成百度坐标
	 * @param x
	 * @param y
	 * @return
	 */
	public static GpsBean google_To_Bd09(BigDecimal x, BigDecimal y) {
		GpsBean gpsBean  = gps84_To_Gcj02(x.doubleValue(), y.doubleValue());
		if (gpsBean != null) {
			return gcj02_To_Bd09(gpsBean.getWgLat(), gpsBean.getWgLon());
		}
		return null;
	}
	
	
	/**
	 * 经纬度转GPS84
	 * 
	 * @param du
	 * @param fen
	 * @param miao
	 * @return
	 */
	public static double jwd_To_Gps84(Integer du, Integer fen, Integer miao) {
		BigDecimal d = new BigDecimal(du.toString());
		BigDecimal f = new BigDecimal(fen.toString());
		BigDecimal m = new BigDecimal(miao.toString());
		return d.add(f.divide(new BigDecimal("60"), 6, RoundingMode.HALF_UP))
				.add(m.divide(new BigDecimal("3600"), 6, RoundingMode.HALF_UP))
				.doubleValue();
	}

	/**
	 * 经纬度转百度坐标
	 * 
	 * @param latDu
	 * @param latFen
	 * @param latMiao
	 * @param lonDu
	 * @param lonFen
	 * @param lonMiao
	 * @return
	 */
	public static GpsBean jwd_To_Bd09(Integer latDu, Integer latFen,
			Integer latMiao, Integer lonDu, Integer lonFen, Integer lonMiao) {
		GpsBean gpsBean = gps84_To_Gcj02(jwd_To_Gps84(latDu, latFen, latMiao),
				jwd_To_Gps84(lonDu, lonFen, lonMiao));
		if (gpsBean != null) {
			return gcj02_To_Bd09(gpsBean.getWgLat(), gpsBean.getWgLon());
		}
		return null;
	}

	

	
	
}
