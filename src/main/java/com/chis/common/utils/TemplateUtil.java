package com.chis.common.utils;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.mts.model.v20140618.AddTemplateRequest;
import com.aliyuncs.mts.model.v20140618.DeleteTemplateRequest;
import com.aliyuncs.mts.model.v20140618.SearchTemplateRequest;
import com.aliyuncs.mts.model.v20140618.SearchTemplateResponse;
import com.aliyuncs.mts.model.v20140618.SearchTemplateResponse.Template;
import com.aliyuncs.mts.model.v20140618.UpdateTemplateRequest;
import com.chis.common.bean.OSSTemplate;

/**
 * OSS 自定义转码模板操作
 * <AUTHOR>
 */
public class TemplateUtil {

	/** 
     * 查询自定义转码模板
     */
    private static List<Template> searchTemplate(DefaultAcsClient client) {
    	SearchTemplateRequest request = new SearchTemplateRequest();
    	SearchTemplateResponse response = null;
        try {
            response = client.getAcsResponse(request);
            List<Template> data = response.getTemplateList();
            return data;
        } catch (Exception e) {
        	e.printStackTrace();
        } 
        return null;
    }
    
    /**
     * 根据模板ID获取模板
     * @param client
     * @param tmpId
     * @return
     */
    public static Template getTemplateById(DefaultAcsClient client, String tmpId) {
    	List<Template> tmpList = searchTemplate(client);
    	
    	if (tmpList == null ||tmpList.size() == 0) {
    		return null;
    	}
    	
    	for (Template tmp : tmpList) {
    		if (tmp.getId().equals(tmpId)) {
    			return tmp;
    		}
    	}
    	return null;
    }
    
    /**
     * 添加自定义转码模板
     * @param client
     * @param temp
     */
    public static void addTemplate(DefaultAcsClient client, OSSTemplate temp) {
    	try {
	    	AddTemplateRequest request = new AddTemplateRequest();
	    	request.setName(temp.getName());  // 必填
	    	/** 容器，JSON对象 内容格式 JSON */
	    	request.setContainer(temp.getContainer()); // 必填
	    	if (temp.getVideo() != null) {
	    		request.setVideo(JSONObject.toJSONString(temp.getVideo()).toString());
	    	}
	    	if (temp.getAudio() != null) {
	    		request.setAudio(JSONObject.toJSONString(temp.getAudio()).toString());
	    	}
	    	request.setTransConfig(temp.getTransConfig());
	    	request.setMuxConfig(temp.getMuxConfig());
        	client.getAcsResponse(request);
        } catch (Exception e) {
           e.printStackTrace();
        }
    }
    
    /**
     * 更新自定义转码模板
     * @param client
     * @param temp
     * @return 0 模板不存在 1 修改成功  -1 修改失败
     */
    public static Integer updateTemplate(DefaultAcsClient client, OSSTemplate temp) {
    	try {
    		Template tmp = getTemplateById(client, temp.getTemplateId());
        	if (tmp == null) {
        		return 0;
        	} else {
        		UpdateTemplateRequest request = new UpdateTemplateRequest();
    	    	request.setTemplateId(temp.getTemplateId());
    	    	request.setName(temp.getName());  // 必填
    	    	/** 容器，JSON对象 内容格式 JSON */
    	    	request.setContainer(temp.getContainer()); // 必填
    	    	if (temp.getVideo() != null) {
    	    		request.setVideo(JSONObject.toJSONString(temp.getVideo()).toString());
    	    	}
    	    	if (temp.getAudio() != null) {
    	    		request.setAudio(JSONObject.toJSONString(temp.getAudio()).toString());
    	    	}
    	    	request.setTransConfig(temp.getTransConfig());
    	    	request.setMuxConfig(temp.getMuxConfig());
            	client.getAcsResponse(request);
            	return 1;
        	}
        } catch (Exception e) {
           e.printStackTrace();
           return -1;
        }
    }
    
    /**
     * 删除自定义转码模板
     * @param client
     * @param templateId
     * @return 0 模板不存在 1 删除成功  -1 删除失败
     */
    public static Integer delTemplate(DefaultAcsClient client, String templateId) {
        try {
        	Template tmp = getTemplateById(client, templateId);
        	if (tmp == null) {
        		return 0;
        	} else {
        		DeleteTemplateRequest request = new DeleteTemplateRequest();
            	request.setTemplateId(templateId);
                client.getAcsResponse(request);
                return 1;
        	}
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        } 
    }
}
