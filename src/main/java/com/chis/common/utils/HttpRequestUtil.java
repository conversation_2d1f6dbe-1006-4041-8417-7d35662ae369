package com.chis.common.utils;

import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;


public class HttpRequestUtil {
	/**
	 * 发起http请求并获取结果
	 * 
	 * @param requestUrl
	 *            请求地址
	 * @param requestMethod
	 *            请求方式（GET、POST）
	 * @param outputStr
	 *            提交的数据
	 * @return JSONObject(通过JSONObject.get(key)的方式获取json对象的属性值)
	 * @throws IOException
	 */
	public static String httpRequest(String requestUrl, String requestMethod,
			String outputStr) throws IOException {
		StringBuffer buffer = new StringBuffer();
		URL url = new URL(requestUrl);
		HttpURLConnection httpUrlConn = (HttpURLConnection) url
				.openConnection();

		httpUrlConn.setDoOutput(true);
		httpUrlConn.setDoInput(true);
		httpUrlConn.setUseCaches(false);
		// 设置请求方式（GET/POST）
		httpUrlConn.setRequestMethod(requestMethod);

		if ("GET".equalsIgnoreCase(requestMethod))
			httpUrlConn.connect();

		// 当有数据需要提交时
		if (null != outputStr) {
			OutputStream outputStream = httpUrlConn.getOutputStream();
			// 注意编码格式，防止中文乱码
			outputStream.write(outputStr.getBytes("UTF-8"));
			outputStream.close();
		}

		// 将返回的输入流转换成字符串
		InputStream inputStream = httpUrlConn.getInputStream();
		InputStreamReader inputStreamReader = new InputStreamReader(
				inputStream, "utf-8");
		BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

		String str = null;
		while ((str = bufferedReader.readLine()) != null) {
			buffer.append(str);
		}
		bufferedReader.close();
		inputStreamReader.close();
		// 释放资源
		inputStream.close();
		inputStream = null;
		httpUrlConn.disconnect();
		return buffer.toString();
	}
	
	/**
	 * 发起https请求并获取结果
	 * 模拟附件发送
	 * @param requestUrl
	 * @param fis
	 * @return
	 * @throws IOException
	 */
	public static String httpRequest(String requestUrl, FileInputStream fis) throws IOException{
		URL urlObj = new URL(requestUrl);
		// 连接
		HttpURLConnection con = (HttpURLConnection) urlObj.openConnection();
		/**
		 * 设置关键值
		 */
		con.setRequestMethod("POST"); // 以Post方式提交表单，默认get方式
		con.setDoInput(true);
		con.setDoOutput(true);
		con.setUseCaches(false); // post方式不能使用缓存
		// 设置请求头信息
		con.setRequestProperty("Connection", "Keep-Alive");
		con.setRequestProperty("Charset", "UTF-8");
		// 设置边界
		String BOUNDARY = "----------" + System.currentTimeMillis();
		con.setRequestProperty("Content-Type", "multipart/form-data; boundary="
				+ BOUNDARY);
		// 请求正文信息
		StringBuilder sb = new StringBuilder();

		// 附件部分：
		sb.append("--");
		sb.append(BOUNDARY);
		sb.append("\r\n");
		sb.append("Content-Disposition: form-data;name=\"dataFile\";filename=\""
				+ "Data.zip" + "\"\r\n");
		sb.append("Content-Type:application/octet-stream\r\n\r\n");

		byte[] head = sb.toString().getBytes("utf-8");

		// 获得输出流
		OutputStream out = new DataOutputStream(con.getOutputStream());
		// 输出表头
		out.write(head);

		// 文件正文部分
		// 把文件已流文件的方式 推入到url中
		int bytes = 0;
		byte[] bufferOut = new byte[1024];
		while ((bytes = fis.read(bufferOut)) != -1) {
			out.write(bufferOut, 0, bytes);
		}
		fis.close();

		// 结尾部分
		byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义最后数据分隔线

		out.write(foot);
		out.flush();
		out.close();

		// 将返回的输入流转换成字符串
		InputStream inputStream = con.getInputStream();
		InputStreamReader inputStreamReader = new InputStreamReader(
				inputStream, "utf-8");
		BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

		String str = null;
		StringBuffer buffer = new StringBuffer();
		while ((str = bufferedReader.readLine()) != null) {
			buffer.append(str);
		}
		bufferedReader.close();
		inputStreamReader.close();
		// 释放资源
		inputStream.close();
		inputStream = null;
		con.disconnect();
		return buffer.toString();
	}
	/***
	 * <p>方法描述: 发起https请求并获取结果</p>
	 * compressed：压缩的字节数组
	 * @MethodAuthor mxp,2019/1/12,httpRequest
	 */
	public static String httpRequest(String requestUrl, byte[] compressed,boolean ifZip) throws IOException{
		URL urlObj = new URL(requestUrl);
		// 连接
		HttpURLConnection con = (HttpURLConnection) urlObj.openConnection();
		/**
		 * 设置关键值
		 */
		con.setRequestMethod("POST"); // 以Post方式提交表单，默认get方式
		con.setDoInput(true);
		con.setDoOutput(true);
		con.setUseCaches(false); // post方式不能使用缓存
		// 设置请求头信息
		con.setRequestProperty("Connection", "Keep-Alive");
		con.setRequestProperty("Charset", "UTF-8");
		// 设置边界
		String BOUNDARY = "----------" + System.currentTimeMillis();
		con.setRequestProperty("Content-Type", "multipart/form-data; boundary="
				+ BOUNDARY);
		// 请求正文信息
		StringBuilder sb = new StringBuilder();

		// 附件部分：
		sb.append("--");
		sb.append(BOUNDARY);
		sb.append("\r\n");
		sb.append("Content-Disposition: form-data;name=\"dataJson\";filename=\""
				+ "dataJson.zip" + "\"\r\n");
		sb.append("Content-Type:application/octet-stream\r\n\r\n");

		byte[] head = sb.toString().getBytes("utf-8");

		// 获得输出流
		OutputStream out = new DataOutputStream(con.getOutputStream());
		// 输出表头
		out.write(head);

		// 文件正文部分
		out.write(compressed);
		// 结尾部分
		byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义最后数据分隔线

		out.write(foot);
		out.flush();
		out.close();

		// 将返回的输入流转换成字符串
		InputStream inputStream = con.getInputStream();
		StringBuffer buffer = new StringBuffer();
		if(ifZip){
			ZipInputStream zis = new ZipInputStream(inputStream);
			ZipEntry zipEntry = zis.getNextEntry();
			while (zipEntry != null) {
				InputStreamReader isr = new InputStreamReader(zis, "UTF-8");
				BufferedReader br = new BufferedReader(isr);
				String str = null;
				while ((str = br.readLine()) != null) {
					buffer.append(str);
				}
				zipEntry = null;
			}
			zis.close();
		}else{
			InputStreamReader inputStreamReader = new InputStreamReader(
					inputStream, "utf-8");
			BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

			String str = null;

			while ((str = bufferedReader.readLine()) != null) {
				buffer.append(str);
			}
			bufferedReader.close();
			inputStreamReader.close();
		}

		// 释放资源
		inputStream.close();
		inputStream = null;
		con.disconnect();
		return buffer.toString();
	}

	/**
	 * <p>方法描述: 发起http请求并获取结果</p>
	 *
	 * @MethodAuthor mxp, 2018/9/5,httpRequest
	 *
	 * @param requestUrl
	 *            请求地址
	 * @param requestMethod
	 *            请求方式（GET、POST）
	 * @param outputStr
	 *            提交的数据
	 * @param connectionTimeout
	 *            连接超时
	 * @param readTimeOut
	 *            读取超时
	 * @return JSONObject(通过JSONObject.get(key)的方式获取json对象的属性值)
	 */
	public static String httpRequest(String requestUrl, String requestMethod,
									 String outputStr,int connectionTimeout, int readTimeOut) throws IOException {
		StringBuffer buffer = new StringBuffer();
		URL url = new URL(requestUrl);
		HttpURLConnection httpUrlConn = (HttpURLConnection) url
				.openConnection();

		httpUrlConn.setDoOutput(true);
		httpUrlConn.setDoInput(true);
		httpUrlConn.setUseCaches(false);
		httpUrlConn.setConnectTimeout(connectionTimeout);
		httpUrlConn.setReadTimeout(readTimeOut);
		// 设置请求方式（GET/POST）
		httpUrlConn.setRequestMethod(requestMethod);

		if ("GET".equalsIgnoreCase(requestMethod))
			httpUrlConn.connect();

		// 当有数据需要提交时
		if (null != outputStr) {
			OutputStream outputStream = httpUrlConn.getOutputStream();
			// 注意编码格式，防止中文乱码
			outputStream.write(outputStr.getBytes("UTF-8"));
			outputStream.close();
		}

		// 将返回的输入流转换成字符串
		InputStream inputStream = httpUrlConn.getInputStream();
		InputStreamReader inputStreamReader = new InputStreamReader(
				inputStream, "utf-8");
		BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

		String str = null;
		while ((str = bufferedReader.readLine()) != null) {
			buffer.append(str);
		}
		bufferedReader.close();
		inputStreamReader.close();
		// 释放资源
		inputStream.close();
		inputStream = null;
		httpUrlConn.disconnect();
		return buffer.toString();
	}
	/**
 	 * <p>方法描述：以raw方式实现post请求</p>
 	 * @MethodAuthor qrr,2018年11月5日,httpTest
	 * */
	public static String httpRequestByRaw(String url,String msg) throws ClientProtocolException,IOException {
		HttpClient httpClient = new DefaultHttpClient();
		HttpPost post = new HttpPost(url);
		StringEntity postingString = new StringEntity(msg, "UTF-8");// json传递
		postingString.setContentEncoding("UTF-8");
		post.setEntity(postingString);
		post.setHeader("Content-type", "application/json; charset=UTF-8");
		HttpResponse response = httpClient.execute(post);
		String content = EntityUtils.toString(response.getEntity());
		return content;
	}
	/**
	 *  <p>方法描述：以raw方式实现post请求</p>
	 * @MethodAuthor hsj 2022-09-06 9:20
	 */
	public static String httpRequestByRaw(String url, String msg, Header[] headers) throws IOException {
		HttpClient httpClient = HttpClients.createDefault();
		HttpPost post = new HttpPost(url);
		StringEntity postingString = new StringEntity(msg, "UTF-8");// json传递
		postingString.setContentEncoding("UTF-8");
		post.setEntity(postingString);
		post.setHeader("Content-type", "application/json; charset=UTF-8");
		post.setHeaders(headers);
		HttpResponse response = httpClient.execute(post);
		String content = EntityUtils.toString(response.getEntity());
		return content;
	}
	/**
 	 * <p>方法描述：发起https请求并获取ZIP压缩包结果
 	 * @param requestUrl
	 * @param fis
	 * @return
	 * @throws IOException
 	 * </p>
 	 * @MethodAuthor qrr,2019年1月14日,httpRequest
	 */
	public static String httpRequestByZip(String requestUrl, FileInputStream fis) throws IOException{
		URL urlObj = new URL(requestUrl);
		// 连接
		HttpURLConnection con = (HttpURLConnection) urlObj.openConnection();
		/**
		 * 设置关键值
		 */
		con.setRequestMethod("POST"); // 以Post方式提交表单，默认get方式
		con.setDoInput(true);
		con.setDoOutput(true);
		con.setUseCaches(false); // post方式不能使用缓存
		// 设置请求头信息
		con.setRequestProperty("Connection", "Keep-Alive");
		con.setRequestProperty("Charset", "UTF-8");
		// 设置边界
		String BOUNDARY = "----------" + System.currentTimeMillis();
		con.setRequestProperty("Content-Type", "multipart/form-data; boundary="
				+ BOUNDARY);
		// 请求正文信息
		StringBuilder sb = new StringBuilder();

		// 附件部分：
		sb.append("--");
		sb.append(BOUNDARY);
		sb.append("\r\n");
		sb.append("Content-Disposition: form-data;name=\"dataFile\";filename=\""
				+ "Data.zip" + "\"\r\n");
		sb.append("Content-Type:application/octet-stream\r\n\r\n");

		byte[] head = sb.toString().getBytes("utf-8");

		// 获得输出流
		OutputStream out = new DataOutputStream(con.getOutputStream());
		// 输出表头
		out.write(head);

		// 文件正文部分
		// 把文件已流文件的方式 推入到url中
		int bytes = 0;
		byte[] bufferOut = new byte[1024];
		while ((bytes = fis.read(bufferOut)) != -1) {
			out.write(bufferOut, 0, bytes);
		}
		fis.close();

		// 结尾部分
		byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义最后数据分隔线

		out.write(foot);
		out.flush();
		out.close();

		// 将返回的输入流转换成字符串
		InputStream inputStream = con.getInputStream();
		
		ZipInputStream zin = new ZipInputStream(inputStream);

		StringBuffer buffer = new StringBuffer();
		ZipEntry zipEntry = zin.getNextEntry();
		while (zipEntry != null) {
			InputStreamReader isr = new InputStreamReader(zin, "UTF-8");
			BufferedReader br = new BufferedReader(isr);
			String str = null;
			while ((str = br.readLine()) != null) {
				buffer.append(str);
			}
			zipEntry = null;
			zipEntry = zin.getNextEntry();
		}
		
		zin.close();
		// 释放资源
		inputStream.close();
		inputStream = null;
		con.disconnect();
		return buffer.toString();
	}
	/**
	 * <p>方法描述：发起https请求并获取反馈结果结果
	 * @param requestUrl
	 * @param fis
	 * @return
	 * @throws IOException
	 * </p>
	 * @MethodAuthor qrr,2019年1月14日,httpRequest
	 */
	public static String httpRequestByBackJson(String requestUrl, FileInputStream fis) throws IOException{
		URL urlObj = new URL(requestUrl);
		// 连接
		HttpURLConnection con = (HttpURLConnection) urlObj.openConnection();
		/**
		 * 设置关键值
		 */
		con.setRequestMethod("POST"); // 以Post方式提交表单，默认get方式
		con.setDoInput(true);
		con.setDoOutput(true);
		con.setUseCaches(false); // post方式不能使用缓存
		// 设置请求头信息
		con.setRequestProperty("Connection", "Keep-Alive");
		con.setRequestProperty("Charset", "UTF-8");
		// 设置边界
		String BOUNDARY = "----------" + System.currentTimeMillis();
		con.setRequestProperty("Content-Type", "multipart/form-data; boundary="
				+ BOUNDARY);
		// 请求正文信息
		StringBuilder sb = new StringBuilder();
		
		// 附件部分：
		sb.append("--");
		sb.append(BOUNDARY);
		sb.append("\r\n");
		sb.append("Content-Disposition: form-data;name=\"dataFile\";filename=\""
				+ "Data.zip" + "\"\r\n");
		sb.append("Content-Type:application/octet-stream\r\n\r\n");
		
		byte[] head = sb.toString().getBytes("utf-8");
		
		// 获得输出流
		OutputStream out = new DataOutputStream(con.getOutputStream());
		// 输出表头
		out.write(head);
		
		// 文件正文部分
		// 把文件已流文件的方式 推入到url中
		int bytes = 0;
		byte[] bufferOut = new byte[1024];
		while ((bytes = fis.read(bufferOut)) != -1) {
			out.write(bufferOut, 0, bytes);
		}
		fis.close();
		
		// 结尾部分
		byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义最后数据分隔线
		
		out.write(foot);
		out.flush();
		out.close();
		
		// 将返回的输入流转换成字符串
		InputStream inputStream = con.getInputStream();
		StringBuffer buffer = new StringBuffer();
		InputStreamReader inputStreamReader = new InputStreamReader(
				inputStream, "utf-8");
		BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
	
		String str = null;
		while ((str = bufferedReader.readLine()) != null) {
			buffer.append(str);
		}
		bufferedReader.close();
		inputStreamReader.close();
		// 释放资源
		inputStream.close();
		inputStream = null;
		con.disconnect();
		return buffer.toString();
	}
	
	
	
	
	/**
 	 * <p>方法描述：发起https请求并发送ZIP压缩包结果,并且指定压缩包key名称
 	 * @param requestUrl
	 * @param fis
	 * @return
	 * @throws IOException
 	 * </p>
 	 * @MethodAuthor rcj,2019年1月14日,httpRequest
	 */
	public static String httpRequestByZipName(String requestUrl, FileInputStream fis,String zipName) throws IOException{
		URL urlObj = new URL(requestUrl);
		// 连接
		HttpURLConnection con = (HttpURLConnection) urlObj.openConnection();
		/**
		 * 设置关键值
		 */
		con.setRequestMethod("POST"); // 以Post方式提交表单，默认get方式
		con.setDoInput(true);
		con.setDoOutput(true);
		con.setUseCaches(false); // post方式不能使用缓存
		// 设置请求头信息
		con.setRequestProperty("Connection", "Keep-Alive");
		con.setRequestProperty("Charset", "UTF-8");
		// 设置边界
		String BOUNDARY = "----------" + System.currentTimeMillis();
		con.setRequestProperty("Content-Type", "multipart/form-data; boundary="
				+ BOUNDARY);
		// 请求正文信息
		StringBuilder sb = new StringBuilder();

		// 附件部分：
		sb.append("--");
		sb.append(BOUNDARY);
		sb.append("\r\n");
		sb.append("Content-Disposition: form-data;name=\"").append(zipName).append("\";filename=\""
				+ "Data.zip" + "\"\r\n");
		sb.append("Content-Type:application/octet-stream\r\n\r\n");

		byte[] head = sb.toString().getBytes("utf-8");

		// 获得输出流
		OutputStream out = new DataOutputStream(con.getOutputStream());
		// 输出表头
		out.write(head);

		// 文件正文部分
		// 把文件已流文件的方式 推入到url中
		int bytes = 0;
		byte[] bufferOut = new byte[1024];
		while ((bytes = fis.read(bufferOut)) != -1) {
			out.write(bufferOut, 0, bytes);
		}
		fis.close();

		// 结尾部分
		byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义最后数据分隔线

		out.write(foot);
		out.flush();
		out.close();

		StringBuffer buffer = new StringBuffer();
		// 将返回的输入流转换成字符串
		InputStream inputStream = con.getInputStream();
		InputStreamReader inputStreamReader = new InputStreamReader(
				inputStream, "utf-8");
		BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
		
		String str = null;
		while ((str = bufferedReader.readLine()) != null) {
			buffer.append(str);
		}
		bufferedReader.close();
		inputStreamReader.close();
		// 释放资源
		inputStream.close();
		inputStream = null;
		con.disconnect();
		return buffer.toString();
	}

	/**
	 * 调用接口上传文件（不压缩）并返回JSON
	 */
	public static String httpRequestByRaw(String url, InputStream in) throws IOException {
		String content = "";
		HttpClient httpClient = HttpClients.createDefault();
		HttpPost post = new HttpPost(url);
		InputStreamEntity inputStreamEntity = new InputStreamEntity(in, ContentType.MULTIPART_FORM_DATA);
		inputStreamEntity.setContentEncoding("UTF-8");
		post.setEntity(inputStreamEntity);
		post.setHeader("Content-type", "application/json; charset=UTF-8");
		HttpResponse response = httpClient.execute(post);
		content = EntityUtils.toString(response.getEntity());
		return content;
	}

}
