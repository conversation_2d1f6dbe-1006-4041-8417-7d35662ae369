package com.chis.common.utils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

public class JdbcUtil {
	
	

	/**
	 * 批量执行SQL语句
	 * 
	 * @param dataSource
	 *            数据源
	 * @param sqlList
	 *            SQL语句集合
	 */
	public static void insertSql(DataSource dataSource, List<String> sqlList) {
		Connection conn = null;
		Statement stmt = null;
		try {
			conn = dataSource.getConnection();
			stmt = conn.createStatement();
			for (String sql : sqlList) {
				stmt.addBatch(sql);
			}
			stmt.executeBatch();
			stmt.close();
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		} finally {
			if (null != stmt) {
				try {
					stmt.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
	}
	
	/**
	 * 根据sql查询，并返回Map
	 * @param dataSource
	 * @param sql 只查询2个字段，第一个字段放入返回的map的key中，第二个字段放入返回的map的value中
	 * @return
	 */
	public static Map<String, String> selectMap(DataSource dataSource, String sql) {
		Map<String, String> map = new HashMap<String, String>();
		Connection conn = null;
		Statement stmt = null;
		try {
			conn = dataSource.getConnection();
			stmt = conn.createStatement();
			
			ResultSet rs = stmt.executeQuery(sql);
			while(rs.next()) {
				String key = rs.getString(1);
				String value = rs.getString(2);
				map.put(key, value);
			}
			stmt.close();
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		} finally {
			if (null != stmt) {
				try {
					stmt.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
		return map;
	}
	
	

	
	/**
	 * 根据sql查询，并返回Map
	 * @param dataSource
	 * @param sql 只查询2个字段，第一个字段放入返回的map的key中，第二个字段放入返回的map的value中
	 * @return
	 */
	public static List selectList(DataSource dataSource, String sql, List paramList) {
		List rtnList = new ArrayList();
		Connection conn = null;
		PreparedStatement pstmt = null;
		try {
			conn = dataSource.getConnection();
			pstmt = conn.prepareStatement(sql);
			
			if(null != paramList && paramList.size() > 0) {
				for(int i=0; i<paramList.size(); i++) {
					pstmt.setObject(i+1, paramList.get(i));
				}
			}
			
			ResultSet rst = pstmt.executeQuery(sql);
			ResultSetMetaData rsmd = rst.getMetaData();
			while (rst.next()) {
				ArrayList nurList = new ArrayList();
				for (int i = 1; i <= rsmd.getColumnCount(); i++) {
					nurList.add(rst.getObject(i));
				}
				rtnList.add(nurList);
			}
			pstmt.close();
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		} finally {
			if (null != pstmt) {
				try {
					pstmt.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
		return rtnList;
	}
	
	
}
