package com.chis.common.utils;

import cn.hutool.core.util.CreditCodeUtil;
import com.google.common.collect.Lists;
import com.hankcs.hanlp.HanLP;
import org.apache.commons.lang3.StringEscapeUtils;
import org.springframework.util.CollectionUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtils extends org.apache.commons.lang3.StringUtils {
	private static final char SEPARATOR = '_';
	private static final String CHARSET_NAME = "UTF-8";
	public static String[] Chinese = new String[] { "零", "一", "二", "三", "四",
			"五", "六", "七", "八", "九" };
	/**固定的验证正则表达式*/
    public static final String PHONE = "^\\s*$|^(0(([1,2]\\d)|([3-9]\\d{2}))[-]?)?\\d{7,8}$";
    /**手机号的验证正则表达式*/
    public static final String MOBILE_REGEX = "^\\s*$|^[1]\\d{10}";
    /**邮箱的验证正则表达式*/
    public static final String  email= "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$";
    /**邮编的验证正则表达式*/
    public static final String  post= "^[0-9]{6}$";
	/**社会信用代码*/
    public static final String  CreditCode="^([0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\\d{14})$";
	// 定义允许的字符集：仅简体汉字和实心点
	private static final String SIMPLIFIED_CHINESE_CHARACTERS = "[\\u4e00-\\u9fa5]";  // 简体汉字
	private static final String SOLID_DOT = "·";  // 实心点

	// 组合正则表达式，确保姓名只能包含简体汉字和实心点
	private static final String VALID_NAME_REGEX =
			"^(?![·])" +  // 不以实心点开头
					"(?!.*[·]$)" +  // 不以实心点结尾
					"([\\u4e00-\\u9fa5]+(·[\\u4e00-\\u9fa5]+)*)+$";  // 简体汉字部分，允许中间有实心点分隔

	// 定义不允许的字符集
	private static final String SPECIAL_CHARACTERS = "[~@#$%^&*()_=+/?,<>、。;]";  // 特殊字符
	private static final String ENGLISH_CHARACTERS = "[a-zA-Z]";  // 英文字符
	private static final String DIGITS = "\\d";  // 数字
	private static final String FULL_WIDTH_CHARACTERS = "[\\uFF01-\\uFF60\\uFFE8-\\uFFEE]";  // 全角字符


	public static void main(String[] args) {
		
    	/*System.err.println(Pattern.matches(MOBILE_REGEX,"1852149658654564654654"));
    	System.err.println(pattern(MOBILE_REGEX,"1852149658654564654654"));
    	
    	String s="哈哈<sub>-2</sub>3<sup>4</sup>1<sub>2</sub>3<sup>4</sup>";
		String s1 = s.replaceAll("(<sub>.*?</sub>)|(<sup>.*?</sup>)", "a");
		System.out.println(s1);
		System.out.println(s1.length());*/

    	String s="-0.000001";
		System.out.println(s.matches("[-]?(\\d\\.\\d{0,5}[1-9])|([1-9])"));
		System.out.println(s.matches("([-]?[1-9])|([-]?\\d\\.\\d{0,5}[1-9])"));
		/*BigDecimal zf = new BigDecimal("2.4");
		BigDecimal bigDecimal = new BigDecimal(10);
		BigDecimal pow = bigDecimal.pow(10);
		BigDecimal multiply = zf.multiply(pow);
		System.out.println(pow.toString());
		System.out.println(multiply.toString());*/

	/*	System.out.println("张三"+isValidName("张三"));  // true
		System.out.println("李四"+isValidName("李四"));  // true
		System.out.println("赵晓明"+isValidName("赵晓明"));  // true
		System.out.println("阿依古丽"+isValidName("阿依古丽"));  // true
		System.out.println("张三·李四"+isValidName("张三·李四"));  // true
		System.out.println("张三123"+isValidName("张三123"));  // false
		System.out.println("John"+isValidName("John"));  // false
		System.out.println("张三."+isValidName("张三."));  // false
		System.out.println("张三·"+isValidName("张三·"));  // false
		System.out.println("张·三"+isValidName("张·三"));  // true
		System.out.println("张三·李四·王五"+isValidName("张三·李四·王五"));  // true
		System.out.println("張三"+isValidName("張三"));  // false (繁体字)
		System.out.println("李四·王五"+isValidName("李四·王五"));  // true
		System.out.println("張三·李四"+isValidName("張三·李四"));  // false (包含繁体字)
		System.out.println("赵曉明"+isValidName("赵曉明"));  // false (繁体字)*/

	}
    
    
	/**
	 * 转换为字节数组
	 * 
	 * @param str
	 * @return
	 */
	public static byte[] getBytes(String str) {
		if (str != null) {
			try {
				return str.getBytes(CHARSET_NAME);
			} catch (UnsupportedEncodingException e) {
				return null;
			}
		} else {
			return null;
		}
	}

	/**
	 * 按字符编码转换为字节数组
	 * 
	 * @param str
	 * @return
	 */
	public static byte[] getBytes(String str, String encoding) {
		if (str != null) {
			try {
				return str.getBytes(encoding);
			} catch (UnsupportedEncodingException e) {
				return null;
			}
		} else {
			return null;
		}
	}

	/**
	 * 获取字符串的字节数，默认"UTF-8"编码，汉字3个字节
	 * 
	 * @param str
	 */
	public static int getBytesLength(String str) {
		if (StringUtils.isBlank(str)) {
			return 0;
		} else {
			return getBytes(str, CHARSET_NAME).length;
		}
	}

	/**
	 * 按字符编码获取字符串的字节数
	 * 
	 * @param str
	 */
	public static int getBytesLength(String str, String encoding) {
		if (StringUtils.isBlank(str)) {
			return 0;
		} else {
			return getBytes(str, encoding).length;
		}
	}

	/**
	 * 转换为字节数组
	 * 
	 * @param str
	 * @return
	 */
	public static String toString(byte[] bytes) {
		try {
			return new String(bytes, CHARSET_NAME);
		} catch (UnsupportedEncodingException e) {
			return EMPTY;
		}
	}

	/**
	 * 是否包含字符串
	 * 
	 * @param str
	 *            验证字符串
	 * @param strs
	 *            字符串组
	 * @return 包含返回true
	 */
	public static boolean inString(String str, String... strs) {
		if (str != null) {
			for (String s : strs) {
				if (str.equals(trim(s))) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 替换掉HTML标签方法
	 */
	public static String replaceHtml(String html) {
		if (isBlank(html)) {
			return "";
		}
		String regEx = "<.+?>";
		Pattern p = Pattern.compile(regEx);
		Matcher m = p.matcher(html);
		String s = m.replaceAll("");
		return s;
	}

	/**
	 * 替换为手机识别的HTML，去掉样式及属性，保留回车。
	 * 
	 * @param html
	 * @return
	 */
	public static String replaceMobileHtml(String html) {
		if (html == null) {
			return "";
		}
		return html.replaceAll("<([a-z]+?)\\s+?.*?>", "<$1>");
	}

	/**
	 * 替换为手机识别的HTML，去掉样式及属性，保留回车。
	 * 
	 * @param txt
	 * @return
	 */
	public static String toHtml(String txt) {
		if (txt == null) {
			return "";
		}
		return replace(replace(Encodes.escapeHtml(txt), "\n", "<br/>"), "\t",
				"&nbsp; &nbsp; ");
	}

	/**
	 * 缩略字符串（不区分中英文字符）
	 * 
	 * @param str
	 *            目标字符串
	 * @param length
	 *            截取长度
	 * @return
	 */
	public static String abbr(String str, int length) {
		if (str == null) {
			return "";
		}
		try {
			StringBuilder sb = new StringBuilder();
			int currentLength = 0;
			for (char c : replaceHtml(StringEscapeUtils.unescapeHtml4(str))
					.toCharArray()) {
				currentLength += String.valueOf(c).getBytes("GBK").length;
				if (currentLength <= length - 3) {
					sb.append(c);
				} else {
					sb.append("...");
					break;
				}
			}
			return sb.toString();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return "";
	}

	public static String abbr2(String param, int length) {
		if (param == null) {
			return "";
		}
		StringBuffer result = new StringBuffer();
		int n = 0;
		char temp;
		boolean isCode = false; // 是不是HTML代码
		boolean isHTML = false; // 是不是HTML特殊字符,如&nbsp;
		for (int i = 0; i < param.length(); i++) {
			temp = param.charAt(i);
			if (temp == '<') {
				isCode = true;
			} else if (temp == '&') {
				isHTML = true;
			} else if (temp == '>' && isCode) {
				n = n - 1;
				isCode = false;
			} else if (temp == ';' && isHTML) {
				isHTML = false;
			}
			try {
				if (!isCode && !isHTML) {
					n += String.valueOf(temp).getBytes("GBK").length;
				}
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}

			if (n <= length - 3) {
				result.append(temp);
			} else {
				result.append("...");
				break;
			}
		}
		// 取出截取字符串中的HTML标记
		String temp_result = result.toString().replaceAll("(>)[^<>]*(<?)",
				"$1$2");
		// 去掉不需要结素标记的HTML标记
		temp_result = temp_result
				.replaceAll(
						"</?(AREA|BASE|BASEFONT|BODY|BR|COL|COLGROUP|DD|DT|FRAME|HEAD|HR|HTML|IMG|INPUT|ISINDEX|LI|LINK|META|OPTION|P|PARAM|TBODY|TD|TFOOT|TH|THEAD|TR|area|base|basefont|body|br|col|colgroup|dd|dt|frame|head|hr|html|img|input|isindex|li|link|meta|option|p|param|tbody|td|tfoot|th|thead|tr)[^<>]*/?>",
						"");
		// 去掉成对的HTML标记
		temp_result = temp_result.replaceAll("<([a-zA-Z]+)[^<>]*>(.*?)</\\1>",
				"$2");
		// 用正则表达式取出标记
		Pattern p = Pattern.compile("<([a-zA-Z]+)[^<>]*>");
		Matcher m = p.matcher(temp_result);
		List<String> endHTML = Lists.newArrayList();
		while (m.find()) {
			endHTML.add(m.group(1));
		}
		// 补全不成对的HTML标记
		for (int i = endHTML.size() - 1; i >= 0; i--) {
			result.append("</");
			result.append(endHTML.get(i));
			result.append(">");
		}
		return result.toString();
	}

	/**
	 * 转换为Double类型
	 */
	public static Double toDouble(Object val) {
		if (val == null) {
			return 0D;
		}
		try {
			return Double.valueOf(trim(val.toString()));
		} catch (Exception e) {
			return 0D;
		}
	}

	/**
	 * 转换为Float类型
	 */
	public static Float toFloat(Object val) {
		return toDouble(val).floatValue();
	}

	/**
	 * 转换为Long类型
	 */
	public static Long toLong(Object val) {
		return toDouble(val).longValue();
	}

	/**
	 * 转换为Integer类型
	 */
	public static Integer toInteger(Object val) {
		return toLong(val).intValue();
	}

	/**
	 * 转换为BigDecimal类型
	 */
	public static BigDecimal toBigDecimal(Object value) {
		BigDecimal ret = null;
		if( value != null ) {
			if( value instanceof BigDecimal ) {
				ret = (BigDecimal) value;
			} else if( value instanceof String ) {
				ret = new BigDecimal( (String) value );
			} else if( value instanceof BigInteger) {
				ret = new BigDecimal( (BigInteger) value );
			} else if( value instanceof Number ) {
				ret = new BigDecimal( ((Number)value).doubleValue() );
			}
		}
		return ret;
	}


	/**
	 * 获得用户远程地址
	 */
    public static String getRemoteAddr(HttpServletRequest request) {
        String ipAddress = request.getHeader("x-forwarded-for");
        if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
            if(ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")){
                //根据网卡取本机配置的IP
                InetAddress inet=null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                ipAddress= inet.getHostAddress();
            }
        }
        //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if(ipAddress!=null && ipAddress.length()>15){ //"***.***.***.***".length() = 15
            if(ipAddress.indexOf(",")>0){
                ipAddress = ipAddress.substring(0,ipAddress.indexOf(","));
            }
        }
        return ipAddress;
    }

	/**
	 * 驼峰命名法工具
	 * 
	 * @return toCamelCase("hello_world") == "helloWorld"
	 *         toCapitalizeCamelCase("hello_world") == "HelloWorld"
	 *         toUnderScoreCase("helloWorld") = "hello_world"
	 */
	public static String toCamelCase(String s) {
		if (s == null) {
			return null;
		}

		s = s.toLowerCase();

		StringBuilder sb = new StringBuilder(s.length());
		boolean upperCase = false;
		for (int i = 0; i < s.length(); i++) {
			char c = s.charAt(i);

			if (c == SEPARATOR) {
				upperCase = true;
			} else if (upperCase) {
				sb.append(Character.toUpperCase(c));
				upperCase = false;
			} else {
				sb.append(c);
			}
		}

		return sb.toString();
	}

	/**
	 * 驼峰命名法工具
	 * 
	 * @return toCamelCase("hello_world") == "helloWorld"
	 *         toCapitalizeCamelCase("hello_world") == "HelloWorld"
	 *         toUnderScoreCase("helloWorld") = "hello_world"
	 */
	public static String toCapitalizeCamelCase(String s) {
		if (s == null) {
			return null;
		}
		s = toCamelCase(s);
		return s.substring(0, 1).toUpperCase() + s.substring(1);
	}

	/**
	 * 驼峰命名法工具
	 * 
	 * @return toCamelCase("hello_world") == "helloWorld"
	 *         toCapitalizeCamelCase("hello_world") == "HelloWorld"
	 *         toUnderScoreCase("helloWorld") = "hello_world"
	 */
	public static String toUnderScoreCase(String s) {
		if (s == null) {
			return null;
		}

		StringBuilder sb = new StringBuilder();
		boolean upperCase = false;
		for (int i = 0; i < s.length(); i++) {
			char c = s.charAt(i);

			boolean nextUpperCase = true;

			if (i < (s.length() - 1)) {
				nextUpperCase = Character.isUpperCase(s.charAt(i + 1));
			}

			if ((i > 0) && Character.isUpperCase(c)) {
				if (!upperCase || !nextUpperCase) {
					sb.append(SEPARATOR);
				}
				upperCase = true;
			} else {
				upperCase = false;
			}

			sb.append(Character.toLowerCase(c));
		}

		return sb.toString();
	}

	/**
	 * 如果不为空，则设置值
	 * 
	 * @param target
	 * @param source
	 */
	public static void setValueIfNotBlank(String target, String source) {
		if (isNotBlank(source)) {
			target = source;
		}
	}

	/**
	 * 转换为JS获取对象值，生成三目运算返回结果
	 * 
	 * @param objectString
	 *            对象串 例如：row.user.id
	 *            返回：!row?'':!row.user?'':!row.user.id?'':row.user.id
	 */
	public static String jsGetVal(String objectString) {
		StringBuilder result = new StringBuilder();
		StringBuilder val = new StringBuilder();
		String[] vals = split(objectString, ".");
		for (int i = 0; i < vals.length; i++) {
			val.append("." + vals[i]);
			result.append("!" + (val.substring(1)) + "?'':");
		}
		result.append(val.substring(1));
		return result.toString();
	}

	/**
	 * String根据分隔符转List
	 * 
	 * @param value
	 *            String字符串
	 * @param splitStr
	 *            分隔符
	 * @return List
	 */
	public static List<String> string2list(String value, String splitStr) {
		List<String> list = Lists.newArrayList();
		if (StringUtils.isNotBlank(value)) {
			String[] arr = value.split(splitStr);
			for (String s : arr) {
				list.add(s);
			}
		}
		return list;
	}

	/**
	 * List转String
	 * 
	 * @param list
	 *            List
	 * @param splitStr
	 *            分隔符
	 * @return String
	 */
	public static String list2string(List list, String splitStr) {
		if (null != list && list.size() > 0) {
			StringBuilder sb = new StringBuilder();
			for (Object s : list) {
				sb.append(splitStr).append(s);
			}
			return sb.toString().replaceFirst(splitStr, "");
		} else {
			return null;
		}
	}

	/**
	* <p>Description： 适用于list</>和set</p>
	* <p>Author： yzz 2024-03-22 </p>
	*/
	public static String collection2string(Collection list, String splitStr) {
		if (null != list && list.size() > 0) {
			StringBuilder sb = new StringBuilder();
			for (Object s : list) {
				sb.append(splitStr).append(s);
			}
			return sb.toString().replaceFirst(splitStr, "");
		} else {
			return null;
		}
	}

	/**
	 * 字符串如果是NULL，返回空串，否则去空格返回
	 * 
	 * @param str
	 *            字符串
	 * @return 字符串
	 */
	public static String convertNull2Empty(String str) {
		if (null == str) {
			return "";
		} else {
			return str.trim();
		}
	}

	/**
	 * 对象的toString
	 * 
	 * @param object
	 *            对象为null返回空串，否则toString
	 * @return 对象的toString
	 */
	public static String objectToString(Object object) {
		return null == object ? "" : object.toString();
	}

	/**
	 * 解决页面模糊查询时，输入% 或 ％查询数据有误问题
	 * 
	 * @param str
	 *            页面传入的值
	 * @return
	 */
	public static String convertBFH(String str) {
		if (str.indexOf("%") != -1 || str.indexOf("％") != -1) {
			str = str.replaceAll("%", "\\\\%").replaceAll("％", "\\\\％");
		}
		return str;
	}

	/**
	 * base64解码
	 * 
	 * @param data
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("restriction")
	public static String decode(String data) {
		if (isBlank(data)) {
			return "";
		}
		try {
			return toString(new BASE64Decoder().decodeBuffer(data));
		} catch (IOException e) {
			return "";
		}
	}

	/**
	 * base64编码
	 * 
	 * @param data
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("restriction")
	public static String encode(byte[] data) {
		if (data == null) {
			return "";
		}
		return (new BASE64Encoder()).encodeBuffer(data);
	}

	/**
	 * base64编码
	 * 
	 * @param data
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("restriction")
	public static String encode(String source) {
		if (isBlank(source)) {
			return "";
		}
		return (new BASE64Encoder()).encodeBuffer(getBytes(source));
	}

	/**
	 * 解析url中param的值
	 * 
	 * @param url
	 *            url地址
	 * @param param
	 *            参数名
	 * @return 返回url中param的值，如果没有返回NULL
	 */
	public static String parseUrl(String url, String param) {
		if (StringUtils.isNotBlank(url) && StringUtils.isNotBlank(param)) {
			if (url.indexOf("?") != -1 && url.indexOf(param) != -1) {
				url = url.substring(url.indexOf("?") + 1);
				String[] arr = url.split("&");
				for (String s : arr) {
					if (s.startsWith(param)) {
						return s.replaceAll(param, "").replaceAll("=", "");
					}
				}
			}
		}
		return null;
	}

	/**
	 * 将字符串数组转换成按分隔符隔开的字符串如数组"1","2","3"则返回1,2,3
	 * 
	 * @param array
	 *            字符串数组
	 * @param splitStr
	 *            分隔符
	 * @return 按分隔符隔开的字符串
	 */
	public static String array2string(String[] array, String splitStr) {
		if (null != array && array.length > 0) {
			StringBuilder sb = new StringBuilder();
			for (String s : array) {
				if (StringUtils.isNotBlank(s)) {
					sb.append(splitStr).append(s);
				}
			}
			return sb.toString().replaceFirst(splitStr, "");
		} else {
			return null;
		}
	}

	public static final String convertNumToChar(int num) {
		if (num >= 0 && num <= 99) {
			String chiStr = "";
			char[] numStr = String.valueOf(num).toCharArray();
			if (numStr.length == 1) {
				chiStr = Chinese[(new Integer(num)).intValue()];
			} else if (numStr.length == 2) {
				if (numStr[0] != 49) {
					chiStr = Chinese[Integer.valueOf(String.valueOf(numStr[0]))
							.intValue()];
				}

				if (numStr[1] != 48) {
					chiStr = chiStr
							+ "十"
							+ Chinese[Integer
									.valueOf(String.valueOf(numStr[1]))
									.intValue()];
				} else {
					chiStr = chiStr + "十";
				}
			}

			return chiStr;
		} else {
			return null;
		}
	}

	/**
	 * 阿拉伯数字转中文数字（只支持正整数）
	 *
	 * @param number 阿拉伯数字
	 * @return 中文数字
	 */
	public static String convertNumber2chinese(int number) {
		final String[] num = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
		final String[] unit = {"", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千"};
		if (number < 10) {
			return num[number];
		}
		if (number == 10) {
			return "十";
		}
		if (number < 20) {
			return "十" + num[number - 10];
		}
		StringBuilder dst = new StringBuilder();
		int count = 0;
		while (number > 0) {
			dst.insert(0, (num[number % 10] + unit[count]));
			number = number / 10;
			count++;
		}
		return dst.toString().replaceAll("零[千百十]", "零").replaceAll("零+万", "万")
				.replaceAll("零+亿", "亿").replaceAll("亿万", "亿零")
				.replaceAll("零+", "零").replaceAll("零$", "");
	}

	/**
	 * 字符串匹配
	 * 
	 * @param regEx
	 *            正则表达式
	 * @param value
	 *            要匹配的字符串
	 * @return 匹配成功返回true
	 */
	public static boolean pattern(String regEx, String value) {
		Pattern p = Pattern.compile(regEx);
		Matcher m = p.matcher(value);
		return m.find();
	}

	public static String dealZero(String zoneCode) {
		if (zoneCode.length() != 8) {
			return zoneCode;
		} else {
			if (zoneCode.substring(zoneCode.length() - 2).equals("00")) {
				zoneCode = zoneCode.substring(0, 6);
			}

			if (zoneCode.substring(zoneCode.length() - 2).equals("00")) {
				zoneCode = zoneCode.substring(0, 4);
			}

			if (zoneCode.substring(zoneCode.length() - 2).equals("00")) {
				zoneCode = zoneCode.substring(0, 2);
			}

			return zoneCode;
		}
	}

	/**
	 * 获取HTML文件中的图片路径数组
	 * 
	 * <AUTHOR>
	 * @createDate 2015年6月5日
	 */
	public static String[] getImgsPath(String content) {
		String img = "";
		Pattern p_image;
		Matcher m_image;
		String str = "";
		String[] images = null;
		String regEx_img = "(<img.*src\\s*=\\s*(.*?)[^>]*?>)";
		p_image = Pattern.compile(regEx_img, Pattern.CASE_INSENSITIVE);
		m_image = p_image.matcher(content);
		while (m_image.find()) {
			img = m_image.group();
			Matcher m = Pattern.compile("src\\s*=\\s*\"?(.*?)(\"|>|\\s+)")
					.matcher(img);
			while (m.find()) {
				String tempSelected = m.group(1);
				if ("".equals(str)) {
					str = tempSelected;
				} else {
					String temp = tempSelected;
					str = str + "," + temp;
				}
			}
		}
		regEx_img = "(<)(input).*?(type)(=)(\")(image)(\").*?(\\/)(>)";
		p_image = Pattern.compile(regEx_img, Pattern.CASE_INSENSITIVE);
		m_image = p_image.matcher(content);
		while (m_image.find()) {
			img = m_image.group();
			Matcher m = Pattern.compile("src\\s*=\\s*\"?(.*?)(\"|>|\\s+)")
					.matcher(img);
			while (m.find()) {
				String tempSelected = m.group(1);
				if ("".equals(str)) {
					str = tempSelected;
				} else {
					String temp = tempSelected;
					str = str + "," + temp;
				}
			}
		}

		if (!"".equals(str)) {
			images = str.split(",");
		}
		return images;
	}


	/**
	 * 验证是否为小数
	 * 
	 * @return isNumber(null); false isNumber(""); true isNumber("11.5"); true
	 *         isNumber("0.5"); true isNumber("011.5"); false isNumber("115");
	 *         true
	 */
	public static boolean isNumber(String value) {
		if (null == value) {
			return false;
		}
		if (isEmpty(value)) {
			return true;
		}
		if (!value.matches("^(-?\\d+)(\\.\\d+)?$")) {
			return false;
		}
		if ("0".equals(value)) {
			return true;
		}
		if (!value.startsWith("0")) {
			return true;
		}
		return value.startsWith("0.");
	}

	/**
	 * 随机生成大写的uuid
	 * 
	 * @return
	 */
	public static String uuid() {
		return UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
	}

	public static boolean isInteger(String value) {
		if (isEmpty(value)) {
			return true;
		}
		if (!isType(value, "\\d+")) {
			return false;
		}
		return (!value.startsWith("0")) || (value.length() <= 1);
	}

	public static boolean isType(String str, String regex) {
		return str.matches(regex);
	}

	public static boolean is10Date(String date) {
		if (isEmpty(date)) {
			return true;
		}
		if (!isSpecifiedLength(date, "10")) {
			return false;
		}

		return isType(
				date,
				"(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)");
	}

	public static boolean is19Datetime(String datetime) {
		if (isEmpty(datetime)) {
			return true;
		}
		if (!isSpecifiedLength(datetime, "19")) {
			return false;
		}
		return isType(
				datetime,
				"^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\\s((([0-1][0-9])|(2?[0-3]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$");
	}

	public static boolean isSpecifiedLength(String value, String length) {
		if (isEmpty(value)) {
			return true;
		}
		if (isEmpty(length)) {
			return false;
		}
		String[] len = length.split("\\|");
		for (int i = 0; i < len.length; i++) {
			if (getStringLength(value) == Integer.parseInt(len[i])) {
				return true;
			}
		}
		return false;
	}

	private static int getStringLength(String str) {
		int len = str.length();
		int clen = 0;
		for (int i = 0; i < len; i++) {
			if (gbValue(str.charAt(i)) > 0) {
				clen += 2;
			} else {
				clen++;
			}
		}
		return clen;
	}

	private static int gbValue(char ch) {
		String str = new String();
		str = str + ch;
		try {
			byte[] bytes = str.getBytes("GBK");
			if (bytes.length < 2) {
				return 0;
			}
			return (bytes[0] << 8 & 0xFF00) + (bytes[1] & 0xFF);
		} catch (Exception e) {
		}
		return 0;
	}

	/**
	 * 验证组织机构代码是否合法
	 * 
	 * @param code
	 * @return
	 */
	public static boolean isOrganizationCode(String code) {
		if (isEmpty(code))
			return true;

		if (isPdyOrganizationCode(code)) {
			return true;
		}
		return orgCodeValidate(code);
	}

	/**
	 * 验证社会信用代码是否合法
	 *  调整成调用Hutool工具中的方法
	 * @param creditCode
	 * @return
	 */
	public static boolean isCreditCode(String creditCode) {
		return CreditCodeUtil.isCreditCode(creditCode);
	}

		/**
	 * 验证社会信用代码是否合法(老版)
	 *
	 * @param cCode
	 * @return
	 */
	public static boolean isCreditCodeOld(String cCode) {
		if (isEmpty(cCode))
			return true;
		if (cCode.length() != 18)
			return false;
		if(!Pattern.matches(CreditCode, cCode)){
			return false;
		}
		return true;
	}
	/**
	* <p>Description： 验证社会信用代码是否合法</p>
	* <p>Author： yzz 2024-02-22 </p>
	*/
	public static boolean verifyCreditCode(boolean flag,String creditCode){
			return flag ?  isCreditCodeOld(creditCode): isCreditCode(creditCode);
	}



	private static boolean isPdyOrganizationCode(String code) {
		if (code.length() != 10)
			return false;

		if (code.indexOf("-") != 8) {
			return false;
		}

		return (code.toLowerCase().startsWith("pdy"));
	}

	private static boolean orgCodeValidate(String code) {
		if (isEmpty(code)) {
			return false;
		}

		int[] ws = { 3, 7, 9, 10, 5, 8, 4, 2 };
		String str = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
		String regex = "^([0-9A-Z]){8}-[0-9|X]$";

		if (!(code.matches(regex)))
			return false;

		int sum = 0;
		for (int i = 0; i < 8; ++i) {
			sum += str.indexOf(String.valueOf(code.charAt(i))) * ws[i];
		}

		int c9 = 11 - sum % 11;

		String sc9 = String.valueOf(c9);
		if (11 == c9)
			sc9 = "0";
		else if (10 == c9)
			sc9 = "X";

		return sc9.equals(String.valueOf(code.charAt(9)));
	}
	/**邮箱验证
	 * qrr 20171101
	 * */
	public static boolean checkEmail(String value) {
	    if(StringUtils.isNotBlank(value)){
	        if(!Pattern.matches(email, value)){
	            return false;
	        }
	    }
        return true;
	}
	/**邮箱验证
	 * qrr 20171101
	 * */
	public static boolean vertyPhone(String value) {
		if (vertyFixedPhone(value) || vertyMobilePhone(value)) {
			return true;
		}
        return false;
	}

	/**
	 * <p>方法描述：返回最大为maxLength长度的字符串</p>
	 * 中文和中文标点符号算2个长度，英文和英文标点符号算1个长度；
	 * maxLength：为中文算2个的长度和英文算1个的长度加起来的值
	 * defaultEndWith:默认超过maxLength长度后，在末尾拼接的字符串
	 * 默认拼接的字符串，同样计入maxLength的长度
	 * @MethodAuthor mxp, 2018/2/23,getStringByLength
	 */
	public static String getStringByLength(String str, int maxLength, String defaultEndWith) {
		str = StringUtils.trimToEmpty(str);
		if ("".equals(str)) {
			return null;
		}
		//小于等于maxLength直接返回
		int stringLength = getStringLength(str);
		if(stringLength<=maxLength){
			return str;
		}
		int len = str.length();
		defaultEndWith = StringUtils.trimToEmpty(defaultEndWith);
		int cLen = getStringLength(defaultEndWith);
		if (cLen >= maxLength) {
			return null;
		}
		int i;
		for (i = 0; i < len; i++) {
			if (gbValue(str.charAt(i)) > 0) {
				cLen += 2;
			} else {
				cLen++;
			}
			if (cLen == maxLength) {
				i++;
				break;
			} else if (cLen > maxLength) {
				break;
			}
		}
		return i == 0?null:str.substring(0, i) + defaultEndWith;
	}

	/**
	 * <p>方法描述：方法同getStringByLength，默认结尾是英文标点 ...</p>
	 *
	 * @MethodAuthor mxp, 2018/2/23,getStringByLengthWithDefaultEnd
	 */
	public static String getStringByLengthWithDefaultEnd(String str, int maxLength) {
		return getStringByLength(str, maxLength, "...");
	}
	/**邮编验证
	 * qrr 20171101
	 * */
	public static boolean vertyPost(String value) {
		if (!Pattern.matches(post, value)) {
			return false;
		}
        return true;
	}
	/**
 	 * <p>方法描述：手机号码验证</p>
 	 * @MethodAuthor qrr,2018年5月16日,vertyPhone
	 * */
	public static boolean vertyMobilePhone(String value) {
		if (!Pattern.matches(MOBILE_REGEX,
				value)) {
			return false;
		}
        return true;
	}
	/**
 	 * <p>方法描述：固话验证</p>
 	 * @MethodAuthor qrr,2018年5月16日,vertyFixedPhone
	 * */
	public static boolean vertyFixedPhone(String value) {
		if (!Pattern.matches(PHONE, value)) {
			return false;
		}
        return true;
	}

	/**
	 * <p>方法描述：加密身份证,隐藏出生年月日</p>
 	 * 
 	 * @MethodAuthor rcj,2018年7月13日,encryptIdc
	 * @param str
	 * @return
	 */
	public static String encryptIdc(String idcCard){
        if(StringUtils.isNotBlank(idcCard)){
            String trim = idcCard.trim();
            if(idcCard.trim().length()==15){
                idcCard = trim.substring(0,6)+"******"+trim.substring(12);
            }else if(idcCard.trim().length()==18){
                idcCard = trim.substring(0,6)+"********"+trim.substring(14);
            }else if (idcCard.trim().length()>4) {
            	idcCard = idcCard.trim().substring(0,idcCard.trim().length()-4)+"****";
			}else {
				idcCard = "****";
			}
            return idcCard;
        }
		return idcCard;
	}
	
	/**
	 * <p>方法描述：加密电话号码,保留最后四位</p>
 	 * 
 	 * @MethodAuthor rcj,2018年7月13日,encryptPhone
	 * @param phoneNo
	 * @return
	 */
	public static String encryptPhone(String phoneNo){
      	if(StringUtils.isNotBlank(phoneNo)&& phoneNo.length()>4){
      		String num = "****************************************************************************************************".substring(0,phoneNo.length()-4);
      		phoneNo = num.concat(phoneNo.substring(phoneNo.length()-4));
      		return phoneNo;
      	}
      	return phoneNo;
	}
	/**
 	 * <p>方法描述：比较两个List集合是否相等
 	 * 1. 如果一个List的引用为null，或者其包含的元素个数为0，那么该List在本逻辑处理中都算作空；
     * 2. 泛型参数T涉及到对象，所以需要确保正确实现了对应对象的equal()方法。
 	 * </p>
 	 * @MethodAuthor qrr,2019年6月21日,isListEqual
	 * */
	public static <T>boolean isListEqual(List<T> list1, List<T> list2) {
		// 两个list引用相同（包括两者都为空指针的情况）
		if (list1 == list2) {
			return true;
		}
		
		// 两个list都为空（包括空指针、元素个数为0）
		if ((list1 == null && list2 != null && list2.size() == 0)
				|| (list2 == null && list1 != null && list1.size() == 0)) {
			return true;
		}
		//一个list为空，另一个list有值
		if ((list1 == null && list2 != null && list2.size()>0)
				|| (list2 == null && list1 != null && list1.size()>0)) {
			return false;
		}
		// 两个list元素个数不相同
		if (list1.size() != list2.size()) {
			return false;
		}
		
		// 两个list元素个数已经相同，再比较两者内容
		// 采用这种可以忽略list中的元素的顺序
		// 涉及到对象的比较是否相同时，确保实现了equals()方法
		if (!list1.containsAll(list2)) {
			return false;
		}
		
		return true; 
	}
	
	/**
	 * <p>方法描述：String转map</p>
 	 * 
 	 * @MethodAuthor rcj,2019年7月26日,getStringToMap
	 */
	   public static Map<String,Object> getStringToMap(String str){
		   if(null != str && str.contains(",")){
			   
			   //根据逗号截取字符串数组
			   String[] str1 = str.split(",");
			   //创建Map对象
			   Map<String,Object> map = new HashMap<>();
			   //循环加入map集合
			   for (int i = 0; i < str1.length; i++) {
				   //根据":"截取字符串数组
				   String[] str2 = str1[i].split(":");
				   //str2[0]为KEY,str2[1]为值
				   map.put(str2[0],str2[1]);
			   }
			   return map;
		   }else{
			   return new HashMap<>();
		   }
	   }
	   
	   
	   
	/**
	 * <p>
	 * 方法描述：String类型的数值转成千分位格式
	 * </p>
	 * 
	 * @return String 返回类型
	 * @MethodAuthor rcj,2020年4月1日,addThousandSeparator
	 */
	public static String addThousandSeparator(String text) {
		if (text == null) {
			return null;
		}
		int index = text.indexOf(".");
		if (index > 0) {
			String integerPartial = text.substring(0, index);
			String decimalPartial = text.substring(index);
			return addThousandSeparatorForInteger(integerPartial)
					+ decimalPartial;
		} else {
			return addThousandSeparatorForInteger(text);
		}
	}

	// 只给整数加千分位分隔符
	public static String addThousandSeparatorForInteger(String text) {
		int index = text.indexOf(".");
		if (index != -1) {
			return text;
		} else {
			int length = text.length();
			List<String> stringContainer = new ArrayList<String>();
			while (length > 3) {
				stringContainer.add(text.substring(length - 3, length));
				length = length - 3;
			}
			// 将最前面的小于三个数字的也加入到数组去
			stringContainer.add(text.substring(0, length)); 
			StringBuffer buffer = new StringBuffer();
			for (int i = stringContainer.size() - 1; i >= 0; i--) {
				buffer.append(stringContainer.get(i) + ",");
			}
			buffer.deleteCharAt(buffer.length() - 1);
			return buffer.toString();
		}
	}

	/**
	 * <p>方法描述：随机生成8位随机数字</p>
	 * @MethodAuthor： yzz
	 * @Date：2021-09-24
	 **/
	public static String KeyValue8(){
		//定义一个字符串（0-9）即62位；
		String str="1234567890";
		//由Random生成随机数
		Random random=new Random();
		StringBuffer sb=new StringBuffer();
		//长度为几就循环几次
		for(int i=0; i<8; ++i){
			//产生0-61的数字
			int number=random.nextInt(10);
			//将产生的数字通过length次承载到sb中
			sb.append(str.charAt(number));
		}
		//将承载的字符转换成字符串
		return sb.toString();
	}

	/**
	* <p>Description：随机生成N位随机数字(重复/不重复) </p>
	* <p>Author： yzz 2024-04-20 </p>
	 * @Param: ifRepeat:true:可重复  false:不可重复
	*/
	public static String generateRandomNumber(int N,boolean ifRepeat) {
		if (N < 1) {
			throw new IllegalArgumentException("N must be a positive integer");
		}
		Set<Integer> set = new HashSet<>();
		Random random = new Random();
		StringBuilder number = new StringBuilder();
		while (true) {
			// 0到9之间的随机整数
			if (!ifRepeat) {
				set.add(random.nextInt(10));
				if (set.size() == N) {
					break;
				}
			} else {
				number.append(random.nextInt(10));
				if (number.length() == N) {
					break;
				}
			}

		}
		return ifRepeat ? number.toString() : collection2string(set, "");
	}

	/**
	 *  <p>方法描述：随机生成4位数包含两位数字两位字母（字母不分大小写）</p>
	 * @MethodAuthor hsj 2024-03-27 16:53
	 */
	public static String generateRandomNumber() {
		Random random = new Random();
		StringBuilder sb = new StringBuilder(4);

		// 生成两位数字
		for (int i = 0; i < 2; i++) {
			int digit = random.nextInt(10); // 生成0-9之间的随机数字
			sb.append(digit);
		}
		// 生成两位字母（不分大小写）
		String letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
		for (int i = 0; i < 2; i++) {
			int index = random.nextInt(letters.length()); // 生成随机索引
			char letter = letters.charAt(index); // 获取对应索引的字母
			sb.append(letter);
		}
		// 打乱顺序，确保数字和字母随机分布
		for (int i = sb.length() - 1; i > 0; i--) {
			int index = random.nextInt(i + 1);
			char temp = sb.charAt(index);
			sb.setCharAt(index, sb.charAt(i));
			sb.setCharAt(i, temp);
		}
		return sb.toString();
	}
	/**
 	 * <p>方法描述：获取本地机器的IP</p>
 	 * @MethodAuthor qrr,2021年11月5日,getLocalIP
	 * */
	public static String getLocalIP(){     
        InetAddress addr;
		try {
			addr = InetAddress.getLocalHost();
			byte[] ipAddr = addr.getAddress();     
			String ip = "";     
			for (int i = 0; i < ipAddr.length; i++) {     
				if (i > 0) {     
					ip  += ".";     
				}     
				ip += ipAddr[i] & 0xFF;     
			}     
			return ip ;     
		} catch (UnknownHostException e) {
			e.printStackTrace();
		}
		return null;
    }


    /**
     * @Description: list按长度分组
     *
     * @MethodAuthor pw,2022年02月28日
     */
	public static <T> List<List<T>> splitListProxy(List<T> lists, Integer num){
		if(CollectionUtils.isEmpty(lists) || null == num || num < 1){
			return null;
		}
		int capacity = lists.size()/num + 1;
		List<List<T>> result = new ArrayList<>(capacity);
		List<T> list = new ArrayList<>(num);
		for(T t : lists){
			list.add(t);
			if(list.size() == num){
				result.add(list);
				list = new ArrayList<>(num);
			}
		}
		if(null != list && list.size() > 0){
			result.add(list);
		}
		return result;
	}

	/**
	 * @Description: 依据范围判断 传入的值 是否在范围内(支持科学记数法)
	 * @param matchVal 传入的值 2E2
	 * @param rule 规则字符串 1<=x<=3E6
	 * @param splitTag 规则里的分隔符 x
	 * @MethodAuthor pw,2022年03月29日
	 */
	public static boolean ifMatchValueArea(String matchVal, String rule, String splitTag)
			throws NumberFormatException{
		if(StringUtils.isBlank(matchVal) || StringUtils.isBlank(rule) || null == splitTag){
			return false;
		}
		String[] ruleArr = rule.split(splitTag);
		if(ruleArr.length == 0 || ruleArr.length > 2){
			return false;
		}
		return ruleArr.length == 2  ?
				(matchRule(matchVal, ruleArr[0], true) && matchRule(matchVal, ruleArr[1], false)) :
				matchRule(matchVal, ruleArr[0], true);
	}

	/**
	 * @Description: 判断是否匹配 如果规则值为空字符串 是算匹配的
	 *
	 * @MethodAuthor pw,2022年03月29日
	 */
	private static boolean matchRule(String val, String ruleVal, boolean ifLeft) throws NumberFormatException{
		if(StringUtils.isBlank(ruleVal)){
			return true;
		}
		boolean flag = false;
		BigDecimal postDecimal = new BigDecimal(val);
		BigDecimal ruleDecimal = new BigDecimal(ruleVal.replace(">","")
				.replace("<", "")
				.replace("=", ""));
		int result = ifLeft ? ruleDecimal.compareTo(postDecimal) : postDecimal.compareTo(ruleDecimal);
		//1 <=   2 <   3 >=   4 >    5=
		if(ruleVal.contains("<=")){
			flag = result <= 0;
		}else if(ruleVal.contains("<")){
			flag = result < 0;
		}else if(ruleVal.contains(">=")){
			flag = result >= 0;
		}else if(ruleVal.contains(">")){
			flag = result > 0;
		}else if(ruleVal.contains("=")){
			flag = result == 0;
		}
		return flag;
	}

	/**
	 * <p>方法描述：去除字符串中的空格 回车 换行 制表符</p>
	 * @MethodAuthor： yzz
	 * @Date：2022-09-09
	 **/
	public static String replaceBlank(String str){
		String dest = "";
		if (str!=null) {
			Pattern p = Pattern.compile("\\s*|\t|\r|\n");
			Matcher m = p.matcher(str);
			dest = m.replaceAll("");
		}
		return dest;
	}
	/**
	 *  <p>方法描述：字符串逗号分隔加引号</p>
	 * @MethodAuthor hsj 2022-10-18 16:43
	 */
	public static String symbolSpilt(String str) {
		StringBuffer sb = new StringBuffer();
		String[] temp = str.split(",");
		for (int i = 0; i < temp.length; i++) {
			if (!"".equals(temp[i]) && temp[i] != null)
				sb.append("'" + temp[i] + "',");
		}
		String result = sb.toString();
		String tp = result.substring(result.length() - 1, result.length());
		if (",".equals(tp)) {
			return result.substring(0, result.length() - 1);
		} else {
			return result;
		}
	}

	/**
	 * 校验密码，校验规则：密码应为8-16位，必须包含大小写字母、数字和特殊字符、不能包含连续3位顺序或逆序或重复的数字、字母
	 *
	 * @param value 密码
	 * @return 校验结果
	 */
	public static boolean validPassword(String value) {
		if (StringUtils.isBlank(value)) {
			return false;
		}
		if (value.length() < 8 || value.length() > 16) {
			return false;
		}
		boolean bool = Pattern.compile("[A-Z]+").matcher(value).find() && Pattern.compile("[a-z]+").matcher(value).find() && Pattern.compile("[0-9]+").matcher(value).find();
		if (!bool) {
			return false;
		}
		for (int i = 0; i < value.length() - 2; i++) {
			int char1 = StringUtils.isAlphanumericCheck(value.charAt(i));
			int char2 = StringUtils.isAlphanumericCheck(value.charAt(i + 1));
			int char3 = StringUtils.isAlphanumericCheck(value.charAt(i + 2));
			if (char1 > 0 && char2 > 0 && char3 > 0) {
				//连续3位是字母或数字
				if ((char1 + 1 == char2 && char2 + 1 == char3) || (char1 - 1 == char2 && char1 - 2 == char3) || (char1 == char2 && char2 == char3)) {
					return false;
				}
			}
			if (char1 < 0 && char2 < 0 && char3 < 0) {
				//连续3位非字母数字
				if (char1 == char2 && char2 == char3) {
					return false;
				}
			}
		}
		return Pattern.compile("[@#$%^&*()_+!~￥={}:;'\",<>|.【】—、]").matcher(value).find();
	}

	private static int isAlphanumericCheck(char character) {
		int value = (int) character;
		if ((value >= 65 && value <= 90) || (value >= 97 && value <= 122) || (value >= 48 && value <= 57)) {
			return value;
		}
		return -value;
	}

	/**
	* <p>Description：处理rid超过1000，拼接in语句报错的情况, </p>
	 *ridLength：按ridLength个rid分一组临时表
	* <p>Author： yzz 2024-05-11 </p>
	*/
	public static String getGroupRidByrids(String rids){
		if(StringUtils.isBlank(rids)){
			return rids;
		}

		StringBuilder sql=new StringBuilder();
		if(rids.length()<=4000){
			sql.append(" TEMP_TABLE AS ( ");
			sql.append(" SELECT REGEXP_SUBSTR(IDS, '[^,]+', 1, LEVEL, 'i') AS ID ");
			sql.append(" FROM (SELECT '").append(rids).append("' IDS FROM DUAL) ");
			sql.append(" CONNECT BY LEVEL <= LENGTH(IDS) - LENGTH(REGEXP_REPLACE(IDS, ',', '')) + 1 ) ");
		}else{
			List<String> ridList = string2list(rids,",");
			List<List<String>> lists = splitListProxy(ridList, 350);
			sql.append(" TEMP_TABLE AS ( select ID from (");
			for (int i = 0; i < lists.size(); i++) {
				sql.append(" SELECT REGEXP_SUBSTR(IDS, '[^,]+', 1, LEVEL, 'i') AS ID ");
				sql.append(" FROM (SELECT '").append(list2string(lists.get(i),",")).append("' IDS FROM DUAL) ");
				sql.append(" CONNECT BY LEVEL <= LENGTH(IDS) - LENGTH(REGEXP_REPLACE(IDS, ',', '')) + 1  ");
				if(i!=(lists.size()-1)){
					sql.append(" union all ");
				}
			}
			sql.append("))");
		}
		return sql.toString();
	}


	/**
	 * 检查姓名是否有效（仅包含简体汉字和实心点）
	 *
	 * @param name 姓名字符串
	 * @return 如果有效返回 true，否则返回 false
	 */
	public static boolean isValidName(String name) {
		if (name == null || name.trim().isEmpty()) {
			return false;
		}

		// 使用正则表达式检查是否符合规则
		Pattern pattern = Pattern.compile(VALID_NAME_REGEX);
		Matcher matcher = pattern.matcher(name);

		if (!matcher.matches()) {
			return false;
		}

		// 检查是否包含特殊字符、英文字符、数字、全角字符或繁体字
		return !containsInvalidCharacters(name);
	}

	/**
	 * 检查姓名是否包含无效字符（特殊字符、英文字符、数字、全角字符或繁体字）
	 *
	 * @param name 姓名字符串
	 * @return 如果包含无效字符返回 true，否则返回 false
	 */
	private static boolean containsInvalidCharacters(String name) {
		// 检查特殊字符
		Pattern specialPattern = Pattern.compile(SPECIAL_CHARACTERS);
		Matcher specialMatcher = specialPattern.matcher(name);
		if (specialMatcher.find()) {
			return true;
		}

		// 检查英文字符
		Pattern englishPattern = Pattern.compile(ENGLISH_CHARACTERS);
		Matcher englishMatcher = englishPattern.matcher(name);
		if (englishMatcher.find()) {
			return true;
		}

		// 检查数字
		Pattern digitPattern = Pattern.compile(DIGITS);
		Matcher digitMatcher = digitPattern.matcher(name);
		if (digitMatcher.find()) {
			return true;
		}

		// 检查全角字符
		Pattern fullWidthPattern = Pattern.compile(FULL_WIDTH_CHARACTERS);
		Matcher fullWidthMatcher = fullWidthPattern.matcher(name);
		if (fullWidthMatcher.find()) {
			return true;
		}

		// 检查繁体字
		if (!name.equals(HanLP.convertToSimplifiedChinese(name))) {
			return true;
		}

		return false;
	}

}
