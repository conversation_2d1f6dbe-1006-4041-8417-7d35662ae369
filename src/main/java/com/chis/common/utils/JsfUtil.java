package com.chis.common.utils;


import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.URL;
import java.util.Iterator;

import java.util.List;
import java.util.Map;

import javax.faces.application.Application;
import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
/**
 * 
 * <AUTHOR>
 * @date   2015-10-31
 * @JsfUtil.java
 */
public class JsfUtil {

	public static void addErrorMessage(Exception ex, String defaultMsg) {
		String msg = ex.getLocalizedMessage();
		if (msg != null && msg.length() > 0) {
			addErrorMessage(msg);
		} else {
			addErrorMessage(defaultMsg);
		}
	}

	public static void addErrorMessages(List<String> messages) {
		for (String message : messages) {
			addErrorMessage(message);
		}
	}

	public static void addErrorMessage(String msg) {
		FacesMessage facesMsg = new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg);
		FacesContext.getCurrentInstance().addMessage(null, facesMsg);
	}

	public static void addErrorMessage(String clientId, String msg) {
		FacesMessage facesMsg = new FacesMessage(FacesMessage.SEVERITY_ERROR, msg, msg);
		FacesContext.getCurrentInstance().addMessage(clientId, facesMsg);
	}

	public static void addSuccessMessage(String msg) {
		FacesMessage facesMsg = new FacesMessage(FacesMessage.SEVERITY_INFO, msg, msg);
		FacesContext.getCurrentInstance().addMessage("successInfo", facesMsg);
	}

	public static String getRequestParameter(String key) {
		return FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get(key);
	}
	
	/**
	 * <p>方法描述：</p>
	 *获取RequestHeader中内容，用于获取请求路径及请求参数
	 * @MethodAuthor xq,2018年3月13日,getRequestHeader
	 */
	public static String getRequestHeader(String key) {
		return  FacesContext.getCurrentInstance().getExternalContext().getRequestHeaderMap().get(key);
	}

	public static Object getObjectFromRequestParameter(String requestParameterName, Converter converter, UIComponent component) {
		String theId = JsfUtil.getRequestParameter(requestParameterName);
		return converter.getAsObject(FacesContext.getCurrentInstance(), component, theId);
	}
	
	public static FacesContext getFacesContext() {
		return FacesContext.getCurrentInstance();
	}
	
	public static ExternalContext getExternalContext() {
		return getFacesContext().getExternalContext();
	}
	
	public static HttpServletRequest getRequest() {
		return (HttpServletRequest) getExternalContext().getRequest();
	}
	
	public static HttpServletResponse getResponse() {
		return (HttpServletResponse) getExternalContext().getResponse();
	}
	
	public static Map<String, Object> getRequestMap() {
		return getExternalContext().getRequestMap();
	}
	
	public static Map<String, String> getRequestParameterMap() {
		return getExternalContext().getRequestParameterMap();
	}
	
	/**
	 * 获取session范围的Map
	 * @return session范围的Map
	 */
	public static Map<String, Object> getSessionMap() {
		return getExternalContext().getSessionMap();
	}

	/**
	 * 获取session对象
	 * @return session对象
	 */
	public static HttpSession getSession() {
		return getRequest().getSession();
	}

    /**
     * 获取application
     * @return 返回application对象
     */
	public static Application getApplication() {
        return getFacesContext().getApplication();
    }
	
    /**
     * 获取受管Bean
     * @param beanName 受管Bean名称
     * @return 受管Bean对象
     */
	public static Object getBean(String beanName) {
        return getApplication().getVariableResolver().resolveVariable(FacesContext.getCurrentInstance(), beanName);
    }
	
    /**
     * 获取weblogic.xml中配置路径
     * @return
     */
    public static String getAbsolutePath() {
    	return PropertyUtils.getValue("virtual.directory");
    }

    /**
     * 获取访问根路径
     * @return
     */
    public static String getContextPath(){
    	return getRequest().getContextPath();
    }

    /**
     * 是否有session
     * @return
     */
    public static boolean hasSession() {
    	boolean b = false;
    	if(null != getFacesContext() && null != getExternalContext() && null != getRequest()
    			&& null != getSession()) {
    		b = true;
    	}
    	return b;
    }
    

    

	/** 
     * 获取当前网络ip 
     * @param request
     * 此方法已废弃，调整为base中StringUtils工具类中的getRemoteAddr方法
     * @return 
     */
	@Deprecated
    public static String getIpAddr(HttpServletRequest request){  
        String ipAddress = request.getHeader("x-forwarded-for");  
            if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {  
                ipAddress = request.getHeader("Proxy-Client-IP");  
            }  
            if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {  
                ipAddress = request.getHeader("WL-Proxy-Client-IP");  
            }  
            if(ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {  
                ipAddress = request.getRemoteAddr();  
                if(ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")){  
                    //根据网卡取本机配置的IP  
                    InetAddress inet=null;  
                    try {  
                        inet = InetAddress.getLocalHost();  
                    } catch (Exception e) {  
                        e.printStackTrace();  
                    }  
                    ipAddress= inet.getHostAddress();  
                }  
            }  
            //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割  
            if(ipAddress!=null && ipAddress.length()>15){ //"***.***.***.***".length() = 15  
                if(ipAddress.indexOf(",")>0){  
                    ipAddress = ipAddress.substring(0,ipAddress.indexOf(","));  
                }  
            }  
            return ipAddress;   
    }  

}

