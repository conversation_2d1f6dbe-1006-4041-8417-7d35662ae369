package com.chis.common.utils;

import java.awt.Color;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Hashtable;

import javax.imageio.ImageIO;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.Binarizer;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.ChecksumException;
import com.google.zxing.EncodeHintType;
import com.google.zxing.FormatException;
import com.google.zxing.LuminanceSource;
import com.google.zxing.NotFoundException;
import com.google.zxing.Result;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.common.HybridBinarizer;
import com.google.zxing.qrcode.QRCodeReader;
import com.google.zxing.qrcode.QRCodeWriter;

/**
 * 类文件描述：二维码生成工具类
 * 
 * <AUTHOR>
 * @createDate 2017年10月19日
 * 
 */
public class QRCode {

	private static final String FORMAT = "png";
	private static final int BLACK = 0xFF000000;
    private static final int WHITE = 0xFFFFFFFF;

	/**
	 * 生成二维码
	 * 
	 * @param contents
	 *            内容，换行可以用\n
	 * @param dest
	 *            生成二维码图片路径
	 * @param width
	 *            宽度
	 * @param height
	 *            高度
	 * @throws WriterException
	 * @throws FileNotFoundException
	 * @throws IOException
	 */
	public static void encode(String contents, String dest, int width, int height) throws WriterException, FileNotFoundException, IOException {
		encode(contents, dest, width, height, 0);
	}
	
	/**
	 * 生成二维码
	 * 
	 * @param contents
	 *            内容，换行可以用\n
	 * @param dest
	 *            生成二维码图片路径
	 * @param width
	 *            宽度
	 * @param height
	 *            高度
	 * @throws WriterException
	 * @throws FileNotFoundException
	 * @throws IOException
	 */
	public static void encode(String contents, String dest, int width, int height,int margin) throws WriterException, FileNotFoundException, IOException {
		if(StringUtils.isBlank(dest))
			return;
		//父级目录。首先判断父级目录是否存在，如不存在，则需要新建文件夹
		String parentFilePath = dest.substring(0, dest.lastIndexOf("/"));
		File tempFile = new File(parentFilePath);
		if (!tempFile.exists()) {
			tempFile.mkdirs();
		}
		
		contents = new String(contents.getBytes("UTF-8"), "ISO-8859-1");
		QRCodeWriter writer = new QRCodeWriter();

		Hashtable<EncodeHintType, Object> hints = new Hashtable<EncodeHintType, Object>();
		hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
		hints.put(EncodeHintType.MARGIN, margin);

		BitMatrix matrix = writer.encode(contents, BarcodeFormat.QR_CODE, width, height, hints);

		FileOutputStream fileOutputStream = null;
		try {
			fileOutputStream = new FileOutputStream(new File(dest));
			MatrixToImageWriter.writeToStream(matrix, FORMAT, fileOutputStream);
		} catch (Exception e) {
			throw e;
		} finally {
			if (null != fileOutputStream) {
				fileOutputStream.close();
			}
		}
	}
	/**
	 * 生成二维码下放加入文本
	 * 
	 * @param contents
	 *            内容，换行可以用\n
	 * @param dest
	 *            生成二维码图片路径
	 * @param width
	 *            宽度
	 * @param height
	 *            高度
	 * @throws WriterException
	 * @throws FileNotFoundException
	 * @throws IOException
	 */
	public static void encodeAddText(String contents, String dest, int width, int height,int margin,String pressText) throws WriterException, FileNotFoundException, IOException {
		if(StringUtils.isBlank(dest))
			return;
		//父级目录。首先判断父级目录是否存在，如不存在，则需要新建文件夹
		String parentFilePath = dest.substring(0, dest.lastIndexOf("/"));
		File tempFile = new File(parentFilePath);
		if (!tempFile.exists()) {
			tempFile.mkdirs();
		}
		
		contents = new String(contents.getBytes("UTF-8"), "ISO-8859-1");
		QRCodeWriter writer = new QRCodeWriter();

		Hashtable<EncodeHintType, Object> hints = new Hashtable<EncodeHintType, Object>();
		hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
		hints.put(EncodeHintType.MARGIN, margin);

		BitMatrix matrix = writer.encode(contents, BarcodeFormat.QR_CODE, width, height+30, hints);
		
		BufferedImage image = toBufferedImage(matrix);
		pressText(pressText,dest,image,Color.black,width, height);
	}
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2018年7月17日,toBufferedImage
	 * */
	public static BufferedImage toBufferedImage(BitMatrix matrix) {
        int width = matrix.getWidth();
        int height = matrix.getHeight();
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, matrix.get(x, y) ? BLACK : WHITE);
            }
        }
        return image;
	}
	/**
 	 * <p>方法描述：在二维码下方添加文字</p>
 	 * @MethodAuthor qrr,2018年7月17日,pressText
	 * */
	public static void pressText(String pressText, String newImg, BufferedImage image,Color color, int width, int height) {

        try {
            int imageW = image.getWidth();
            int imageH = image.getHeight();
            Graphics g = image.createGraphics();
            g.drawImage(image, 0, 0, imageW, imageH, null);
            g.setColor(color);
            g.setFont(new Font("宋体", Font.PLAIN, 18));
            Font f = g.getFont();
            //计算文字开始的位置
            FontMetrics fm = sun.font.FontDesignMetrics.getMetrics(f); 
            //x开始的位置：（图片宽度-字的宽度）/2
            int startX = (width - fm.stringWidth(pressText))/2;
            //y开始的位置：图片高度-（图片高度-图片宽度）/2
            int startY = height - (height - width) / 2 +20;
            g.drawString(pressText, startX, startY);
            g.dispose();

            FileOutputStream out = new FileOutputStream(newImg);
            ImageIO.write(image, "JPEG", out);
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
	/**
	 * 从一张图片解析出二维码信息
	 * 
	 * @param dest
	 *            目标地址
	 * @return String 二维码信息
	 * @throws IOException
	 * @throws NotFoundException
	 * @throws ChecksumException
	 * @throws FormatException
	 */
	public static String decode(String dest) throws IOException, NotFoundException, ChecksumException, FormatException {
		QRCodeReader reader = new QRCodeReader();
		BufferedImage image = ImageIO.read(new File(dest));
		LuminanceSource source = new BufferedImageLuminanceSource(image);
		Binarizer binarizer = new HybridBinarizer(source);
		BinaryBitmap imageBinaryBitmap = new BinaryBitmap(binarizer);
		Result result = reader.decode(imageBinaryBitmap);
		return result.getText();
	}

	public static void main(String[] args) throws WriterException, IOException, NotFoundException, ChecksumException, FormatException {
		QRCode.encodeAddText("http://www.baidu.com/", "C:/1/2/Desktop/1/Target222.png", 240, 240,0,"345dfgfdg");
//		System.out.println(QRCode.decode("C:/1/2/Desktop/1/Target222.png"));

	}

}