package com.chis.common.utils;

import org.apache.pdfbox.cos.COSBase;
import org.apache.pdfbox.cos.COSDictionary;
import org.apache.pdfbox.cos.COSName;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Callable;

public  class VerifyJavaScriptCallable implements Callable {
    private final COSDictionary pageDictionary;

    public VerifyJavaScriptCallable(COSDictionary pageDictionary) {
        this.pageDictionary = pageDictionary;
    }

    @Override
    public Boolean call() throws Exception {
        return checkDictionaryForJavaScript(this.pageDictionary,null) ;
    }
    private  boolean checkDictionaryForJavaScript(COSDictionary dict, Set<COSDictionary> visited) {
        if (visited == null) {
            visited = new HashSet<>();
        }
        if (visited.contains(dict)) {
            return false;
        }
        visited.add(dict);
        for (COSName key : dict.keySet()) {
            COSBase value = dict.getDictionaryObject(key);
            if (key.equals(COSName.JS) || key.equals(COSName.JAVA_SCRIPT)) {
                return true;
            } else if (value instanceof COSDictionary) {
                if (checkDictionaryForJavaScript((COSDictionary) value, visited)) {
                    return true;
                }
            }
        }
        return false;
    }
}
