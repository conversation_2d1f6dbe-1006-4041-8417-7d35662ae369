package com.chis.common.utils;

import java.util.Map;

import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.PushPayload.Builder;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.AndroidNotification;
import cn.jpush.api.push.model.notification.IosNotification;
import cn.jpush.api.push.model.notification.Notification;

/**
 * 模块说明：极光推送工具类
 * 
 * <AUTHOR>
 * @createDate 2016年5月7日
 */
public class JPushUtil {

	public static void main(String[] args) {
		//移动OA推送测试
		JPushUtil.sendJPush("8ccced08526ac2e739b63d3a", "0695bcb88f1adbac9263a897", "gsjk", "你好", null,false);
	}
	
	/**
	 * 广播推送消息给所有用户
	 * 
	 * @param appKey
	 *            appKey
	 * @param masterSecret
	 *            masterSecret
	 * @param title
	 *            标题名称
	 * @param paramMap
	 *            参数
	 */
	public static boolean sendJPushAll(String appKey, String masterSecret, String title, Map<String, String> paramMap,boolean ifProduce) {
		try {
			JPushClient jpushClient = new JPushClient(masterSecret, appKey);
			PushPayload payload = buildPushObject(null, title, paramMap);
			// 推送是否产品模式，安卓无模式区分  IOS 存在开发。产品。根据证书判断
			payload.resetOptionsApnsProduction(ifProduce);
			PushResult result = jpushClient.sendPush(payload);
			System.err.println(result);
			return true;
		} catch (Exception e) {
			System.err.println(e.getMessage());
			return false;
		}
	}

	/**
	 * 推送消息给指定用户
	 * 
	 * @param appKey
	 *            appKey
	 * @param masterSecret
	 *            masterSecret
	 * @param anotherName
	 *            设备别名
	 * @param title
	 *            标题名称
	 * @param paramMap
	 *            参数
	 */
	public static boolean sendJPush(String appKey, String masterSecret, String anotherName, String title, Map<String, String> paramMap,boolean ifProduce) {
		try {
			JPushClient jpushClient = new JPushClient(masterSecret, appKey);
			PushPayload payload = buildPushObject(anotherName, title, paramMap);
			// 推送是否产品模式
			payload.resetOptionsApnsProduction(ifProduce);
			PushResult result = jpushClient.sendPush(payload);
			System.err.println(result);
			return true;
		} catch (Exception e) {
			System.err.println(e.getMessage());
			return false;
		}
	}

	/**
	 * 设置推送消息对象
	 * 
	 * @param anotherName
	 *            设备别名
	 * @param title
	 *            标题名称
	 * @param paramMap
	 *            参数
	 * @return
	 */
	private static PushPayload buildPushObject(String anotherName, String title, Map<String, String> paramMap) {

		cn.jpush.api.push.model.notification.AndroidNotification.Builder androidBuilder = AndroidNotification.newBuilder();
		cn.jpush.api.push.model.notification.IosNotification.Builder iosBuilder = IosNotification.newBuilder();
		iosBuilder.setSound("defalut");
		if (null != paramMap && paramMap.size() > 0) {
			androidBuilder = androidBuilder.addExtras(paramMap);
			iosBuilder = iosBuilder.addExtras(paramMap);
		}
		AndroidNotification androidBuild = androidBuilder.build();
		IosNotification iosbuild = iosBuilder.build();
		Notification build = Notification.newBuilder().setAlert(title).addPlatformNotification(androidBuild).addPlatformNotification(iosbuild)
				.build();

		Builder setPlatform = PushPayload.newBuilder().setPlatform(Platform.all());
		if (StringUtils.isNotBlank(anotherName)) {
			setPlatform = setPlatform.setAudience(Audience.alias(anotherName));
		} else {
			setPlatform = setPlatform.setAudience(Audience.all());
		}
		PushPayload pushPayload = setPlatform.setNotification(build).build();
		return pushPayload;
	}

}
