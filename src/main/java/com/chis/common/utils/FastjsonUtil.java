package com.chis.common.utils;

import com.alibaba.fastjson.JSON;
import java.util.Comparator;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

public class FastjsonUtil {


    /***
     * <p>方法描述: json字段按照字典排序（只排序第一层）</p>
     *
     * @MethodAuthor mxp, 2018/11/2,sortMap
     */
    public static Map<String, String> sortMap(String jsonStr) {
        Map<String, String> map = JSON.parseObject(jsonStr, Map.class);
        if (map == null || map.isEmpty()) {
            return null;
        }
        Map<String, String> sortMap = new TreeMap<String, String>(new MapComparator());
        sortMap.putAll(map);
        return sortMap;
    }

    /***
     * <p>方法描述: json字段按照字典排序（只排序第一层）</p>
     *
     * @MethodAuthor mxp, 2018/11/2,sortJson
     */
    public static String sortJson(String jsonStr) {
        Map<String, String> resultMap = sortMap(jsonStr);
        return JSON.toJSONString(resultMap);
    }

    /***
     * <p>方法描述: json字段按照字典排序（只排序第一层）</p>
     *
     * @MethodAuthor mxp, 2018/11/2,sortJson
     */
    public static String sortJson(String jsonStr, String... appends) {
        Map<String, String> resultMap = sortMap(jsonStr);
        StringBuilder sb = new StringBuilder();
        if(resultMap!=null&&resultMap.size()>0){
            Set<Map.Entry<String, String>> entries = resultMap.entrySet();
            for (Map.Entry<String, String> entry : entries) {
                sb.append("&").append(entry.getKey()).append("=").append(entry.getValue());
            }
        }
        if (appends != null && appends.length > 0) {
            for (int i = 0; i < appends.length; i++) {
                sb.append("&").append(appends[i]);
            }
        }
        return sb.length()>0?sb.substring(1):null;
    }


}

class MapComparator implements Comparator<String> {

    @Override
    public int compare(String str1, String str2) {
        // TODO Auto-generated method stub
        return str1.compareTo(str2);
    }

}
