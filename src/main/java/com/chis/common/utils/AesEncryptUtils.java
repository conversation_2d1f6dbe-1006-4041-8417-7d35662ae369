package com.chis.common.utils;

import java.nio.charset.StandardCharsets;
import java.util.Random;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;

public class AesEncryptUtils {
	private static final String KEY = "abcdef0123456789";
	private static final String ALGORITHMSTR = "AES/ECB/PKCS5Padding";

	public static String base64Encode(byte[] bytes) {
		return Base64.encodeBase64String(bytes);
	}

	public static byte[] base64Decode(String base64Code) throws Exception {
		return Base64.decodeBase64(base64Code);
	}

	public static byte[] aesEncryptToBytes(String content, String encryptKey) throws Exception {
		KeyGenerator kgen = KeyGenerator.getInstance("AES");
		kgen.init(128);
		Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
		cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(), "AES"));
		return cipher.doFinal(content.getBytes("utf-8"));
	}

	public static String aesEncrypt(String content, String encryptKey) throws Exception {
		return base64Encode(aesEncryptToBytes(content, encryptKey));
	}

	public static String aesDecryptByBytes(byte[] encryptBytes, String decryptKey) throws Exception {
		KeyGenerator kgen = KeyGenerator.getInstance("AES");
		kgen.init(128);
		Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
		cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), "AES"));
		byte[] decryptBytes = cipher.doFinal(encryptBytes);
		return new String(decryptBytes,StandardCharsets.UTF_8);
	}

	public static String aesDecrypt(String encryptStr, String decryptKey) throws Exception {
		return aesDecryptByBytes(base64Decode(encryptStr), decryptKey);
	}

	/**生成16位的密钥**/
	public static String KeyValue16(){
		//定义一个字符串（A-Z，a-z，0-9）即62位；
		String str="zxcvbnmlkjhgfdsaqwertyuiopQWERTYUIOPASDFGHJKLZXCVBNM1234567890";
		//由Random生成随机数
		Random random=new Random();
		StringBuffer sb=new StringBuffer();
		//长度为几就循环几次
		for(int i=0; i<16; ++i){
			//产生0-61的数字
			int number=random.nextInt(62);
			//将产生的数字通过length次承载到sb中
			sb.append(str.charAt(number));
		}
		//将承载的字符转换成字符串
		return sb.toString();
	}



	/**
	 * AES 加密操作
	 *
	 * @param content 待加密内容
	 * @param key     加密密钥
	 * @param vipara     偏移量
	 * @return 返回Base64转码后的加密数据
	 */
	public static String encrypt(String content, String key, String vipara) {
		//偏移量
		byte[] iv = vipara.getBytes();
		try {
			Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
			int blockSize = cipher.getBlockSize();
			byte[] dataBytes = content.getBytes();
			int length = dataBytes.length;
			//计算需填充长度
			if (length % blockSize != 0) {
				length = length + (blockSize - (length % blockSize));
			}
			byte[] plaintext = new byte[length];
			//填充
			System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);
			SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), "AES");
			//设置偏移量参数
			IvParameterSpec ivSpec = new IvParameterSpec(iv);
			cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
			byte[] encryped = cipher.doFinal(plaintext);

			return org.apache.commons.net.util.Base64.encodeBase64String(encryped);

		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * AES 解密操作
	 *
	 * @param content 待解密内容
	 * @param key     加密密钥
	 * @param vipara     偏移量
	 * @return
	 */
	public static String desEncrypt(String content, String key, String vipara) {

		byte[] iv = vipara.getBytes();

		try {
			byte[] encryp = org.apache.commons.net.util.Base64.decodeBase64(content);
			Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
			SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), "AES");
			IvParameterSpec ivSpec = new IvParameterSpec(iv);
			cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
			byte[] original = cipher.doFinal(encryp);
			return new String(original,StandardCharsets.UTF_8);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}


	public static void main(String[] args) throws Exception {
//		String content = "\"'{\"loginName\":\"展示\",\"password\":\"123\"}'\"";
//		String content = "13666036";
//		System.out.println("加密前：" + content);
//
//		String encrypt = aesEncrypt(content, "afmi5pjDeKpSUaLU");
//		System.out.println(encrypt.length() + ":加密后：" + encrypt);
//
//		String decrypt = aesDecrypt(encrypt, "afmi5pjDeKpSUaLU");
//		System.out.println("解密后：" + decrypt);
//		System.out.println(KeyValue16());


//		String  content = "{\"unitCode\":\"njzwx001\",\"password\":\"Zwx@train.321\"}";
//		String key = "mf123nets654@zwx";
//		String vipara = "MF@123.654zwx321";
//		System.out.println("content:" + content);
//		String s1 = encrypt(content, key,vipara);
//		System.out.println("result1:" + s1);
//		String aa ="u5HRaEKrJh9ukMWqN7RXu7qrfRQBYF2E7P4hxOWxpBxV88vTbVhgjt+3o86svTiXz4736edYy6AfxA8TpVFrL2fsVJZUtfkuqFrAVKElLamAh5Wy6d32wfESI1ZJ2gxX";
//		System.out.println("result2:" + desEncrypt(s1, key,vipara));
//		System.out.println("key : " + key);
//		System.out.println("VIPARA:" + vipara);
		
	}
	
}