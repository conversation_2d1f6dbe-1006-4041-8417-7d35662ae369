package com.chis.common.utils;

import java.lang.reflect.Method;

/**
 * 枚举的工具类
 *
 * <AUTHOR>
 * @createDate 2014年9月16日上午9:45:23
 */
public class EnumUtils extends org.apache.commons.lang3.EnumUtils{

    private EnumUtils() {
    }

    /**
     * 根据枚举Class和value返回枚举实例<br/>
     * @param clazz 枚举Class
     * @param value 值
     * @return 枚举实例
     */
    public static Enum findEnum(Class clazz, Object value) {
    	if(null == value || !clazz.isEnum()) {
            throw new RuntimeException("参数传入错误！");
        }
        Object[] obs = clazz.getEnumConstants();
        for(Object o: obs) {
            if(o.toString().equals(value.toString())) {
                return (Enum)o;
            }
        }
        return null;
    }
    
    /**
     * 根据传入的枚举值，以及枚举Class返回中文描述；多个以,隔开<br/>
     * @param clazz 枚举Class
     * @param value 值,多个以,隔开
     * @return 中文描述
     */
    public static String findTypeCN(Class clazz, String value) {
        if(null == value || !clazz.isEnum()) {
            throw new RuntimeException("参数传入错误！");
        }
        String[] splits = value.split(",");
        StringBuilder sb = new StringBuilder();
        for(String s:splits) {
        	Enum eu = findEnum(clazz, s);
        	try {
				Method m = clazz.getDeclaredMethod("getTypeCN");
				if(null != m) {
					sb.append(",").append(m.invoke(eu));
				}else {
					throw new RuntimeException("MethodNotFoundException:getTypeCN");
				}
			} catch (Exception e) {
				e.printStackTrace();
			} 
        }
        return sb.toString().replaceFirst(",", "");
    }
    
}
