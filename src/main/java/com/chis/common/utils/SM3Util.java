package com.chis.common.utils;

import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.Security;

public class SM3Util {

    private static final String ENCODING = "UTF-8";

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    public static void main(String[] args) throws NoSuchProviderException, NoSuchAlgorithmException {
        byte[] message = "123".getBytes();
        MessageDigest digest = MessageDigest.getInstance("SM3", "BC");
        byte[] result = digest.digest(message);
        System.out.println(result);
    }
}
