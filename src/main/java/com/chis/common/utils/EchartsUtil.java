package com.chis.common.utils;

import java.math.BigDecimal;
import java.util.*;

import com.chis.common.pojo.BarChartParamPojo;
import com.chis.common.pojo.LineChartParamPojo;
import com.chis.common.pojo.MyCategoryAxis;
import com.chis.common.pojo.MyValueAxis;
import com.chis.common.pojo.PieChartParamPojo;
import com.chis.common.pojo.PieDataPojo;
import com.github.abel533.echarts.Grid;
import com.github.abel533.echarts.Label;
import com.github.abel533.echarts.Legend;
import com.github.abel533.echarts.Timeline;
import com.github.abel533.echarts.Title;
import com.github.abel533.echarts.Toolbox;
import com.github.abel533.echarts.Tooltip;
import com.github.abel533.echarts.axis.*;
import com.github.abel533.echarts.code.*;
import com.github.abel533.echarts.data.Data;
import com.github.abel533.echarts.feature.MagicType;
import com.github.abel533.echarts.json.GsonOption;
import com.github.abel533.echarts.series.Bar;
import com.github.abel533.echarts.series.Line;
import com.github.abel533.echarts.series.Pie;
import com.github.abel533.echarts.series.Series;
import com.github.abel533.echarts.style.ItemStyle;
import com.github.abel533.echarts.style.LineStyle;
import com.github.abel533.echarts.style.TextStyle;
import org.springframework.util.CollectionUtils;

/**
 * echarts图表生成工具类
 * 
 * <AUTHOR>
 * @createDate 2016年8月19日
 */
public class EchartsUtil {
	/**
	 * 时间轴，每个图表最多仅有一个时间轴控件
	 * 
	 * @param type
	 *            模式是时间类型，时间轴间隔根据时间跨度自动计算，可选为：'number'
	 * @param notMerge
	 *            时间轴上多个option切换时是否进行merge操作，同setOption第二个参数
	 * @param realtime
	 *            拖拽或点击改变时间轴是否实时显示，在不支持Canvas的浏览器中该值自动强制置为false
	 * @param x
	 *            时间轴左上角横坐标，数值单位px，支持百分比（字符串），如'50%'(显示区域横向中心)
	 * @param y
	 *            时间轴左上角纵坐标，数值单位px，支持百分比（字符串），默认无，随y2定位，如'50%'(显示区域纵向中心)
	 * @param x2
	 *            时间轴右下角横坐标，数值单位px，支持百分比（字符串），如'50%'(显示区域横向中心)
	 * @param y2
	 *            时间轴右下角纵坐标，数值单位px，支持百分比（字符串），如'50%'(显示区域纵向中心)
	 * @param width
	 *            时间轴宽度，默认为总宽度 - x - x2，数值单位px，指定width后将忽略x2。见下图。
	 *            支持百分比（字符串），如'50%'(显示区域一半的宽度)
	 * @param height
	 *            时间轴高度，数值单位px，支持百分比（字符串），如'50%'(显示区域一半的高度)
	 * @param backgroundColor
	 *            背景颜色，默认透明。
	 * @param borderColor
	 *            边框颜色
	 * @param borderWidth
	 *            边框线宽
	 * @param padding
	 *            内边距，单位px，默认各方向内边距为5，接受数组分别设定上右下左边距，同css
	 * @param controlPosition
	 *            播放控制器位置，可选为：'left' | 'right' | 'none'
	 * @param autoPlay
	 *            是否自动播放
	 * @param loop
	 *            是否循环播放
	 * @param playInterval
	 *            播放时间间隔，单位ms
	 * @param lineStyle
	 *            时间轴轴线样式
	 * @param label
	 *            时间轴标签文本
	 * @param currentIndex
	 *            当前索引位置，对应options数组，用于指定显示特定系列
	 * @param datas
	 *            时间轴列表，同时也是轴label内容
	 * @return
	 */
	public static Timeline createTimeline(TimeLineType type, Boolean notMerge,
			Boolean realtime, Object x, Object y, Object x2, Object y2,
			Object width, Object height, String backgroundColor,
			String borderColor, Integer borderWidth, Integer padding,
			ControlPosition controlPosition, Boolean autoPlay, Boolean loop,
			Integer playInterval, LineStyle lineStyle, Label label,
			Integer currentIndex, List<Object> datas) {
		Timeline timeline = new Timeline();
		if (null != type) {
			timeline.type(type);
		}
		if (null != notMerge) {
			timeline.notMerge(notMerge);
		}
		if (null != realtime) {
			timeline.realtime(realtime);
		}
		if (null != x) {
			timeline.x(x);
		}
		if (null != y) {
			timeline.y(y);
		}
		if (null != x2) {
			timeline.x2(x2);
		}
		if (null != y2) {
			timeline.y2(y2);
		}
		if (null != width) {
			timeline.width(width);
		}
		if (null != height) {
			timeline.height(height);
		}
		if (StringUtils.isNotBlank(backgroundColor)) {
			timeline.backgroundColor(backgroundColor);
		}
		if (StringUtils.isNotBlank(borderColor)) {
			timeline.borderColor(borderColor);
		}
		if (null != borderWidth) {
			timeline.borderWidth(borderWidth);
		}
		if (null != padding) {
			timeline.padding(padding);
		}
		if (null != controlPosition) {
			timeline.controlPosition(controlPosition);
		}
		if (null != autoPlay) {
			timeline.autoPlay(autoPlay);
		}
		if (null != loop) {
			timeline.loop(loop);
		}
		if (null != playInterval) {
			timeline.playInterval(playInterval);
		}
		if (null != lineStyle) {
			timeline.setLineStyle(lineStyle);
		}
		if (null != label) {
			timeline.setLabel(label);
		}
		if (null != currentIndex) {
			timeline.currentIndex(currentIndex);
		}
		if (null != datas) {
			timeline.data(datas);
		}
		return timeline;
	}

	/**
	 * 
	 * @param text
	 *            主标题文本，'\n'指定换行
	 * @param link
	 *            主标题文本超链接
	 * @param target
	 *            指定窗口打开主标题超链接，支持'self' | 'blank'，不指定等同为'blank'（新窗口）
	 * @param subtext
	 *            副标题文本，'\n'指定换行
	 * @param sublink
	 *            副标题文本超链接
	 * @param subtarget
	 *            指定窗口打开副标题超链接，支持'self' | 'blank'，不指定等同为'blank'（新窗口）
	 * @param x
	 *            水平安放位置，默认为左侧，可选为：'center' | 'left' | 'right' |
	 *            {number}（x坐标，单位px）
	 * @param y
	 *            垂直安放位置，默认为全图顶端，可选为：'top' | 'bottom' | 'center' |
	 *            {number}（y坐标，单位px）
	 * @param textAlign
	 *            水平对齐方式，默认根据x设置自动调整，可选为： left' | 'right' | 'center
	 * @param backgroundColor
	 *            标题背景颜色，默认透明
	 * @param borderColor
	 *            标题边框颜色
	 * @param borderWidth
	 *            标题边框线宽，单位px，默认为0（无边框）
	 * @param itemGap
	 *            主副标题纵向间隔，单位px，默认为10
	 * @param textStyle
	 *            主标题文本样式
	 * @param subtextStyle
	 *            副标题文本样式
	 * @param padding
	 *            标题内边距，单位px，默认各方向内边距为5，接受数组分别设定上右下左边距
	 * @return
	 */
	public static Title createTitle(String text, String link, String target,
			String subtext, String sublink, String subtarget, Object x,
			Object y, X textAlign, String backgroundColor, String borderColor,
			Integer borderWidth, Integer itemGap, TextStyle textStyle,
			TextStyle subtextStyle, Integer... padding) {
		Title title = new Title();
		if (StringUtils.isNotBlank(text)) {
			title.text(text);
		}
		if (StringUtils.isNotBlank(link)) {
			title.link(link);
		}
		if (StringUtils.isNotBlank(target)) {
			title.target(target);
		}
		if (StringUtils.isNotBlank(subtext)) {
			title.subtext(subtext);
		}
		if (StringUtils.isNotBlank(sublink)) {
			title.sublink(sublink);
		}
		if (StringUtils.isNotBlank(subtarget)) {
			title.subtarget(subtarget);
		}
		if (null != x) {
			title.x(x);
		}
		if (null != y) {
			title.y(y);
		}
		if (null != textAlign) {
			title.textAlign(textAlign);
		}
		if (StringUtils.isNotBlank(backgroundColor)) {
			title.backgroundColor(backgroundColor);
		}
		if (StringUtils.isNotBlank(borderColor)) {
			title.borderColor(borderColor);
		}
		if (null != borderWidth) {
			title.borderWidth(borderWidth);
		}
		if (null != padding) {
			title.padding(padding);
		}
		if (null != itemGap) {
			title.itemGap(itemGap);
		}
		if (null != textStyle) {
			title.textStyle(textStyle);
		}
		if (null != subtextStyle) {
			title.subtextStyle(subtextStyle);
		}
		return title;
	}

	/**
	 * 工具箱，每个图表最多仅有一个工具箱
	 * 
	 * @param orient
	 *            布局方式，默认为水平布局，可选为：'horizontal' | 'vertical'
	 * @param x
	 *            水平安放位置，默认为全图居中，可选为：'center' | 'left' | 'right' |
	 *            {number}（x坐标，单位px）
	 * @param y
	 *            垂直安放位置，默认为全图顶端，可选为：'top' | 'bottom' | 'center' |
	 *            {number}（y坐标，单位px）
	 * @param backgroundColor
	 *            工具箱背景颜色，默认透明
	 * @param borderColor
	 *            工具箱边框颜色
	 * @param borderWidth
	 *            工具箱边框线宽，单位px，默认为0（无边框）
	 * @param itemSize
	 *            工具箱icon大小，单位（px）
	 * @param itemGap
	 *            各个item之间的间隔，单位px，默认为10，横向布局时为水平间隔，纵向布局时为纵向间隔
	 * @param color
	 *            工具箱icon颜色序列，循环使用，同时支持在具体feature内指定color
	 * @param feature
	 * @param padding
	 *            工具箱内边距，单位px，默认各方向内边距为5，接受数组分别设定上右下左边距，同css
	 * @return
	 */
	private static Toolbox createToolbox(Orient orient, Object x, Object y,
			String backgroundColor, String borderColor, Integer borderWidth,
			Integer itemSize, Integer itemGap, List<Object> color,
			Object feature, Integer... padding) {
		Toolbox toolbox = new Toolbox();
		if (null != orient) {
			toolbox.orient(orient);
		}
		if (null != x) {
			toolbox.x(x);
		}
		if (null != y) {
			toolbox.y(y);
		}
		if (StringUtils.isNotBlank(backgroundColor)) {
			toolbox.backgroundColor(backgroundColor);
		}
		if (StringUtils.isNotBlank(borderColor)) {
			toolbox.borderColor(borderColor);
		}
		if (null != borderWidth) {
			toolbox.borderWidth(borderWidth);
		}
		if (padding != null) {
			toolbox.padding(padding);
		}
		if (null != itemGap) {
			toolbox.itemGap(itemGap);
		}
		if (null != itemSize) {
			toolbox.itemSize(itemSize);
		}
		if (null != color) {
			toolbox.color(color);
		}
		if (null != feature) {
			toolbox.feature(feature);
		}
		return toolbox;
	}

	/**
	 * 图例，每个图表最多仅有一个图例
	 * 
	 * @param orient
	 *            布局方式，默认为水平布局，可选为：'horizontal' | 'vertical'
	 * @param x
	 *            水平安放位置，默认为全图居中，可选为：'center' | 'left' | 'right' |
	 *            {number}（x坐标，单位px）
	 * @param y
	 *            垂直安放位置，默认为全图顶端，可选为：'top' | 'bottom' | 'center' |
	 *            {number}（y坐标，单位px）
	 * @param backgroundColor
	 *            工具箱背景颜色，默认透明
	 * @param borderColor
	 *            工具箱边框颜色
	 * @param borderWidth
	 *            工具箱边框线宽，单位px，默认为0（无边框）
	 * @param itemGap
	 *            各个item之间的间隔，单位px，默认为10，横向布局时为水平间隔，纵向布局时为纵向间隔
	 * @param itemWidth
	 *            图例图形宽度
	 * @param itemHeight
	 *            图例图形高度
	 * @param textStyle
	 *            默认只设定了图例文字颜色（详见textStyle） ，更个性化的是，要指定文字颜色跟随图例，可设color为'auto'
	 * @param selectedMode
	 *            选择模式，默认开启图例开关，可选single，multiple
	 * @param selected
	 *            配置默认选中状态，可配合LEGEND.SELECTED事件做动态数据载入
	 * @param data
	 *            图例内容数组，数组项通常为{string}，每一项代表一个系列的name，默认布局到达边缘会自动分行（列），传入空字符串
	 *            ''可实现手动分行（列）。使用根据该值索引series中同名系列所用的图表类型和itemStyle，如果索引不到，该item
	 *            将 默 认 为 没 启 用 状 态
	 * @param padding
	 *            图例内边距，单位px，默认各方向内边距为5，接受数组分别设定上右下左边距，同css
	 * @return
	 */
	public static Legend createLegend(Orient orient, Object x, Object y,
			String backgroundColor, String borderColor, Integer borderWidth,
			Integer itemGap, Integer itemWidth, Integer itemHeight,
			TextStyle textStyle, Object selectedMode,
			Map<String, Boolean> selected, List<Object> data,
			Integer... padding) {
		Legend legend = new Legend();
		if (null != orient) {
			legend.orient(orient);
		}
		if (null != x) {
			legend.x(x);
		}
		if (null != y) {
			legend.y(y);
		}
		if (StringUtils.isNotBlank(backgroundColor)) {
			legend.backgroundColor(backgroundColor);
		}
		if (StringUtils.isNotBlank(borderColor)) {
			legend.borderColor(borderColor);
		}
		if (null != borderWidth) {
			legend.borderWidth(borderWidth);
		}
		if (padding != null) {
			legend.padding(padding);
		}
		if (null != itemGap) {
			legend.itemGap(itemGap);
		}
		if (null != itemWidth) {
			legend.itemWidth(itemWidth);
		}
		if (null != itemHeight) {
			legend.itemHeight(itemHeight);
		}
		if (null != textStyle) {
			legend.textStyle(textStyle);
		}
		if (null != selectedMode) {
			legend.selectedMode(selectedMode);
		}
		if (null != selected) {
			legend.setSelected(selected);
		}
		if (null != data) {
			legend.data(data);
		}
		return legend;
	}

	/**
	 * 直角坐标系内绘图网格
	 * 
	 * @param x
	 *            直角坐标系内绘图网格左上角横坐标，数值单位px，支持百分比（字符串），如'50%'(显示区域横向中心)
	 * @param y
	 *            直角坐标系内绘图网格左上角纵坐标，数值单位px，支持百分比（字符串），如'50%'(显示区域纵向中心)
	 * @param x2
	 *            直角坐标系内绘图网格右下角横坐标，数值单位px，支持百分比（字符串），如'50%'(显示区域横向中心)
	 * @param y2
	 *            直角坐标系内绘图网格右下角纵坐标，数值单位px，支持百分比（字符串），如'50%'(显示区域纵向中心)
	 * @param width
	 *            直角坐标系内绘图网格（不含坐标轴）宽度，默认为总宽度 - x - x2，数值单位px，指定width后将忽略x2
	 * @param height
	 *            直角坐标系内绘图网格（不含坐标轴）高度，默认为总高度 - y - y2，数值单位px，指定height后将忽略y2
	 * @param backgroundColor
	 *            背景颜色，默认透明。
	 * @param borderColor
	 *            边框线宽
	 * @param borderWidth
	 *            边框颜色
	 * @return
	 */
	public static Grid createGrid(Object x, Object y, Object x2, Object y2,
			Object width, Object height, String backgroundColor,
			String borderColor, Integer borderWidth) {
		Grid grid = new Grid();
		if (null != x) {
			grid.x(x);
		}
		if (null != y) {
			grid.y(y);
		}
		if (null != x2) {
			grid.x2(x2);
		}
		if (null != y2) {
			grid.y2(y2);
		}
		if (null != width) {
			grid.width(width);
		}
		if (null != height) {
			grid.height(height);
		}
		if (StringUtils.isNotBlank(backgroundColor)) {
			grid.backgroundColor(backgroundColor);
		}
		if (StringUtils.isNotBlank(borderColor)) {
			grid.borderColor(borderColor);
		}
		if (null != borderWidth) {
			grid.borderWidth(borderWidth);
		}
		return grid;
	}

	/**
	 * 值域坐标轴
	 * 
	 * @param name
	 *            坐标轴名称，默认为空
	 * @param label
	 *            坐标轴文本标签
	 * @param style
	 *            线条（线段）样式
	 * @param max
	 *            指定的最大值，eg: 100，默认无，会自动根据具体数值调整，指定后将忽略boundaryGap[1]
	 * @param min
	 *            指定的最小值，eg: 0，默认无，会自动根据具体数值调整，指定后将忽略boundaryGap[0]
	 * @param boundaryGap
	 *            坐标轴两端空白策略，数组内数值代表百分比，[原始数据最小值与最终最小值之间的差额，原始数据最大值与最终最大值之间的差额]
	 * @param scale
	 *            脱离0值比例，放大聚焦到最终_min，_max区间
	 * @param splitNumber
	 *            分割段数，不指定时根据min、max算法调整
	 * @return
	 */
	public static ValueAxis createValueAxis(String name, AxisLabel label,
			LineStyle style, Integer max, Integer min, Double[] boundaryGap,
			Boolean scale, Integer splitNumber) {
		ValueAxis axis = new ValueAxis();
		if (StringUtils.isNotBlank(name)) {
			axis.name(name);
		}
		if (label != null) {
			axis.axisLabel(label);
		}
		if (null != boundaryGap) {
			axis.boundaryGap(boundaryGap);
		}
		if (style != null) {
			axis.axisLine().lineStyle(style);
		}
		// 最大值
		if (null != max) {
			axis.max(max);
		}
		// 最小值
		if (null != min) {
			axis.min(min);
		}
		if (null != scale) {
			axis.scale(scale);
		}
		if (null != splitNumber) {
			axis.splitNumber(splitNumber);
		}
		return axis;
	}

	/**
	 * 类目坐标轴
	 * 
	 * @param name
	 *            坐标轴名称，默认为空
	 * @param label
	 *            坐标轴文本标签
	 * @param linestyle
	 *            线条（线段）样式
	 * @param data
	 *            类目列表，同时也是label内容
	 * @param boundaryGap
	 *            类目起始和结束两端空白策略，默认为true留空，false则顶头
	 * @return
	 */
	public static CategoryAxis createCategoryAxis(String name, AxisLabel label,
			LineStyle linestyle, List data, Boolean boundaryGap) {
		CategoryAxis axis = new CategoryAxis();
		if (StringUtils.isNotBlank(name)) {
			axis.name(name);
		}
		if (label != null) {
			axis.axisLabel(label);
		}
		if (linestyle != null) {
			axis.axisLine().lineStyle(linestyle);
		}
		if (null != data) {
			for (Object obj : data) {
				axis.data().add(obj);
			}
		}
		if (null != boundaryGap) {
			axis.boundaryGap(boundaryGap);
		}
		return axis;
	}

	/**
	 * 线条（线段）样式
	 * 
	 * @param color
	 *            颜色
	 * @param width
	 *            线条样式，可选为：'solid' | 'dotted' | 'dashed'， 树图还可以选：'curve' |
	 *            'broken'
	 * @param type
	 *            线宽
	 * @param shadowColor
	 *            折线主线(IE8+)有效，阴影色彩，支持rgba
	 * @param shadowBlur
	 *            折线主线(IE8+)有效，阴影模糊度，大于0有效
	 * @param shadowOffsetX
	 *            折线主线(IE8+)有效，阴影横向偏移，正值往右，负值往左
	 * @param shadowOffsetY
	 *            折线主线(IE8+)有效，阴影纵向偏移，正值往下，负值往上
	 * @return
	 */
	public static LineStyle createLineStyle(Object color, Integer width,
			LineType type, String shadowColor, Integer shadowBlur,
			Integer shadowOffsetX, Integer shadowOffsetY) {
		LineStyle style = new LineStyle();
		if (null != color) {
			style.color(color);
		}
		if (null != width) {
			style.width(width);
		}
		if (null != type) {
			style.type(type);
		}
		if (StringUtils.isNotBlank(shadowColor)) {
			style.shadowColor(shadowColor);
		}
		if (null != shadowBlur) {
			style.shadowBlur(shadowBlur);
		}
		if (null != shadowOffsetX) {
			style.shadowOffsetX(shadowOffsetX);
		}
		if (null != shadowOffsetY) {
			style.shadowOffsetY(shadowOffsetY);
		}
		return style;
	}

	/**
	 * 坐标轴文本标签选项
	 * 
	 * @param interval
	 *            标签显示挑选间隔，默认为'auto'，可选为： 'auto'（自动隐藏显示不下的） | 0（全部显示） |
	 *            {number}（用户指定选择间隔）
	 *            {function}函数回调，传递参数[index，data[index]]，返回true显示，返回false隐藏
	 * @param rotate
	 *            标签旋转角度，默认为0，不旋转，正值为逆时针，负值为顺时针，可选为：-90 ~ 90
	 * @param margin
	 *            坐标轴文本标签与坐标轴的间距，默认为8，单位px
	 * @param clickable
	 *            坐标轴文本标签是否可点击
	 * @param formatter
	 *            间隔名称格式器：{string}（Template） | {Function}
	 * @param textStyle
	 *            文本样式
	 * @return
	 */
	public static AxisLabel createAxisLabel(Object interval, Integer rotate,
			Integer margin, Boolean clickable, Object formatter,
			TextStyle textStyle) {
		AxisLabel label = new AxisLabel();
		if (null != clickable) {
			label.clickable(clickable);
		}
		if (null != interval) {
			label.interval(interval);
		}
		if (null != rotate) {
			label.rotate(rotate);
		}
		if (null != margin) {
			label.margin(margin);
		}
		if (null != formatter) {
			label.formatter(formatter);
		}
		if (null != textStyle) {
			label.textStyle(textStyle);
		}
		return label;
	}

	/**
	 * 文字样式
	 * 
	 * @param color
	 *            颜色
	 * @param decoration
	 *            修饰，仅对tooltip.textStyle生效 ，默认'none'
	 * @param x
	 *            水平对齐方式，可选为：'left' | 'right' | 'center'
	 * @param baseline
	 *            垂直对齐方式，可选为：'top' | 'bottom' | 'middle'
	 * @param fontFamily
	 *            字体系列
	 * @param fontSize
	 *            字号 ，单位px
	 * @param fontStyle
	 *            样式，可选为：'normal' | 'italic' | 'oblique'， 默认'normal'
	 * @param fontWeight
	 *            粗细，可选为：'normal' | 'bold' | 'bolder' | 'lighter' | 100 | 200
	 *            |... | 900 ，默认'normal'
	 * @return
	 */
	public static TextStyle createTextStyle(String color, String decoration,
			X x, Baseline baseline, String fontFamily, Integer fontSize,
			FontStyle fontStyle, Object fontWeight) {
		TextStyle style = new TextStyle();
		if (StringUtils.isNotBlank(color)) {
			style.color(color);
		}
		if (StringUtils.isNotBlank(decoration)) {
			style.decoration(decoration);
		}
		if (null != x) {
			style.align(x);
		}
		if (null != baseline) {
			style.baseline(baseline);
		}
		if (StringUtils.isNoneBlank(fontFamily)) {
			style.fontFamily(fontFamily);
		}
		if (null != fontSize) {
			style.fontSize(fontSize);
		}
		if (null != fontStyle) {
			style.fontStyle(fontStyle);
		}
		if (null != fontWeight) {
			style.fontWeight(fontWeight);
		}
		return style;
	}

	/**
	 * 
	 * @param type
	 *            类型
	 * @param seriesName
	 *            图表名称,可为空
	 * @param datas
	 *            数据集
	 * @return
	 */
	public static Series<?> createSeries(SeriesType type, String seriesName,
			List datas) {
		Series<?> series = null;
		switch (type) {
		case bar:
			series = new Bar(seriesName);
			break;
		case line:
			series = new Line(seriesName);
			break;
		case pie:
			series = new Pie(seriesName);
			break;
		default:
			break;
		}
		if (null != datas) {
			series.data().addAll(datas);
		}
		return series;
	}

	/**
	 * 
	 * @param title
	 * @param type
	 * @param legend
	 * @param dataMap
	 * @return
	 */
	public static GsonOption createSimpleOption(String title, SeriesType type,
			List legend, Map<String, List<Data>> dataMap, Axis xAxis, Axis yAxis) {
		GsonOption option = new GsonOption();
		option.title(title);
		option.title().setX(X.center);
		option.title().textStyle().color("#3E98C5");
		if (SeriesType.bar.equals(type) || SeriesType.line.equals(type)) {
			option.toolbox()
					.show(true)
					.feature(new MagicType(Magic.line, Magic.bar),
							Tool.restore, Tool.saveAsImage).padding(20)
					.x(X.right).y(Y.center).orient(Orient.vertical);
			Tooltip tooltip = new Tooltip();
			tooltip.setTrigger(Trigger.axis);
			tooltip.axisPointer().setType(PointerType.none);
			option.setTooltip(tooltip);
			if (legend != null && legend.size() > 0) {
				option.legend().x(X.right);
			}
			option.xAxis(xAxis);
			option.yAxis(yAxis);
		} else if (SeriesType.pie.equals(type)) {
			option.tooltip().trigger(Trigger.item)
					.formatter("{a} <br/>{b} : {d}%");
			option.toolbox().show(true).feature(Tool.restore, Tool.saveAsImage)
					.padding(20);
			if (legend != null && legend.size() > 0) {
				option.legend().orient(Orient.vertical);
				option.legend().x(X.left);
				option.legend().y(Y.center);
			}
		}
		if (legend != null && legend.size() > 0) {
			for (Object obj : legend) {
				option.legend().data().add(obj);
			}
		}
		Set<String> e = dataMap.keySet();
		for (String str : e) {
			option.series().add(createSeries(type, str, dataMap.get(str)));
		}
		return option;
	}

	/**
 	 * <p>方法描述：封装图表数据
 	 * title：标题
 	 * xAxisTitle：x轴名称
 	 * yAxisTitle：y轴名称
 	 * xdate:x轴数据
 	 * bars：柱状图数据
 	 * ifdatazoom：x轴是否滑动
 	 * ifrotate:x轴是否旋转
 	 * </p>
 	 * @MethodAuthor qrr,2018年7月10日,buildChartJson
	 * */
	public static String initBarXml(BarChartParamPojo param) {
		boolean flag = false;//x轴文本内容过长，需换行
		Object[] xdate = param.getXdate();
		for (Object xname : xdate) {
			if (null==xname) {
				continue;
			}
			if (xname.toString().length()>10) {
				flag = true;
				break;
			}
		}
		GsonOption op = new GsonOption();
		// 自定义颜色
		if(!CollectionUtils.isEmpty(param.getColor())){
			op.setColor(param.getColor());
		}
		op.tooltip().trigger(Trigger.axis);
		// 定义工具
		op.toolbox()
				.show(true)
				.feature(new MagicType(Magic.line, Magic.bar), Tool.restore,
						Tool.saveAsImage).padding(20).x(X.right).y(Y.top)
				.orient(Orient.horizontal)
				.padding(new Integer[]{30,30,0,0});
		Object[] legend = param.getLegend();
		X xlegend = param.getXlegend();
		if (null!=legend) {
			op.legend().data(legend);
		}
		op.legend().y(Y.top);
		if (null==xlegend) {
			op.legend().x(X.left);
		}else {
			op.legend().x(xlegend);
		}
		op.legend().padding(new Integer[]{30,0,0,0});
		op.grid().y(85);
		op.grid().x(85);
		op.grid().x2(65);
		op.grid().y2("20%");
		if (flag) {
			op.grid().y2("26%");
		}
		op.title().x(X.center);
		op.title(param.getTitle());
		op.title().textStyle().fontFamily("微软雅黑");
		op.title().textStyle().fontWeight("bolder");
		op.title().textStyle().setColor("rgba(30,144,255,0.8)");

		List<Bar> bars = param.getBars();
		if (param.isIfside()) {
			op.dataZoom().show(true);
			op.dataZoom().realtime(true);
			op.dataZoom().start(0);
			Bar bar = bars.get(0);
			List datas = bar.getData();
			BigDecimal one = new BigDecimal(param.getSideNum());
			BigDecimal two = new BigDecimal(datas.size());
			BigDecimal divide = one.multiply(new BigDecimal(100)).divide(
					two, 0);
			op.dataZoom().end(divide.intValue());
		}
		// 横坐标
		CategoryAxis xAxis = new CategoryAxis();
		xAxis.setData(Arrays.asList(xdate));
		xAxis.setType(AxisType.category);
		xAxis.setShow(true);
		xAxis.setName(param.getxAxisTitle());
		AxisLabel xAxisLabel = new AxisLabel();
		xAxisLabel
				.setFormatter("function(value){return (value.length >10 ? (value.slice(0,10)+'\n'+value.slice(10,value.length)) : value )}");
		xAxisLabel.textStyle().color("#000000");
		xAxisLabel.setInterval(0);
		xAxisLabel.margin(15);
		if (param.isIfrotate()) {
			xAxisLabel.setRotate(20);
		}
		xAxis.setAxisLabel(xAxisLabel);
		xAxis.splitLine().show(true);
		op.xAxis().add(xAxis);

		// 纵坐标
		MyValueAxis valueAxis = new MyValueAxis();
		valueAxis.setType(AxisType.value);
		valueAxis.setName(param.getyAxisTitle());
		valueAxis.axisTick().show(false);
		valueAxis.axisLabel().textStyle().color("#000000");
		valueAxis.axisLabel().formatter("{value}");
		if(param.getIfShowYLine()){
			AxisLine axisLine = new AxisLine();
			axisLine.show(true);
			xAxis.setAxisLine(axisLine);
			valueAxis.setAxisLine(axisLine);
		}
		if (null != param.getMinInterval()) {//坐标轴最小间隔大小
			valueAxis.setMinInterval(param.getMinInterval());
		}
		op.yAxis().add(valueAxis);

		op.series().addAll(bars);
		return op.toString();
	}


	/**
	 * @Description: 横式柱状图
	 * 当前应用于TdZwHethOrgAmountAnalyseBean
	 *
	 * @MethodAuthor pw,2021年04月19日
	 */
	public static String initBarXmlRound(BarChartParamPojo param) {
		Object[] xdate = param.getXdate();
		GsonOption op = new GsonOption();
		op.tooltip().trigger(Trigger.axis);
		op.tooltip().axisPointer().type(PointerType.shadow);
		// 定义工具
		op.toolbox()
				.show(true)
				.feature(Tool.saveAsImage).padding(20).x(X.right).y(Y.top)
				.orient(Orient.horizontal)
				.padding(new Integer[]{30,30,0,0});
		op.calculable(true);
		Object[] legend = param.getLegend();
		X xlegend = param.getXlegend();
		if (null!=legend) {
			op.legend().data(legend);
		}
		op.legend().y(Y.top);
		if (null==xlegend) {
			op.legend().x(X.center);
		}else {
			op.legend().x(xlegend);
		}
		op.legend().padding(new Integer[]{30,0,0,0});
		op.title().x(X.center);
		op.title(param.getTitle());
		op.title().textStyle().fontFamily("微软雅黑");
		op.title().textStyle().fontWeight("bolder");
		op.title().textStyle().fontSize(18);
		op.title().textStyle().setColor("rgba(30,144,255,0.8)");
		List<Bar> bars = param.getBars();

		op.calculable(true);
		// 横坐标
		MyCategoryAxis xAxis = new MyCategoryAxis();
		xAxis.setType(AxisType.value);
		xAxis.setShow(true);
		xAxis.setName(param.getxAxisTitle());
		if (null != param.getMinInterval()) {
			xAxis.setMinInterval(param.getMinInterval());
		}
		op.xAxis().add(xAxis);

		// 纵坐标
		ValueAxis valueAxis = new ValueAxis();
		valueAxis.setType(AxisType.category);
		valueAxis.setData(Arrays.asList(xdate));
		valueAxis.axisTick().setShow(true);
		valueAxis.axisLabel().margin(10);
		valueAxis.setName(param.getyAxisTitle());
		op.yAxis().add(valueAxis);

		op.series().addAll(bars);
		return op.toString();
	}

	/**
	 * 饼图数据初始化
	 * @param name 名称
	 * @param value 值
	 * @param color 颜色
	 *   selected 是否选中
	 * @return
	 */
	public static PieDataPojo initPieData(String name, String value, String color) {
		PieDataPojo myData = new PieDataPojo();
		ItemStyle style= new ItemStyle();
		if (StringUtils.isNotBlank(color)) {
			style.normal().color(color);
		}
		myData.itemStyle(style);
		myData.setValue(value);
		myData.setName(name);
		myData.textStyle().color("#7d7d7d");
		return myData;
	}
	/**
	 * 饼图内容初始化
	 * @param data 数据集合
	 * @param title 标题
	 * @param legend 
	 * @return
	 */
	public static String initPieXml(Pie data, String title, Object[] legend) {
		GsonOption op = new GsonOption();
		if (StringUtils.isNotBlank(title)) {
			op.title(title);
			op.title().x(X.center);
			TextStyle t = new TextStyle();
			t.setFontSize(9);
			t.setColor("rgba(30,144,255,0.8)");
			t.setFontFamily2("微软雅黑");
			t.fontSize(18);
			t.fontWeight("bolder");
			op.title().textStyle(t);
		}
		if (null!=legend) {
			op.legend().data(legend);
			op.legend().y(Y.top);
			op.legend().x(X.left);
			op.legend().setPadding(new Object[]{40,0,0,0});
			op.legend().orient(Orient.vertical);
		}
		//异步触发
		op.tooltip().trigger(Trigger.item);
		op.tooltip().formatter("{a}<br/>{b}：{c} <br/>占比：{d}%");
        //工具栏
		op.toolbox().show(false);
		op.series().add(data);
		return op.toString();
	}
	/**
	 * 饼图内容初始化
	 *   data 数据集合
	 *   title 标题
	 *   legend
	 * @return
	 */
	public static String initPieXmlNew(PieChartParamPojo paramPojo) {
		GsonOption op = new GsonOption();
		if (StringUtils.isNotBlank(paramPojo.getTitle())) {
			op.title(paramPojo.getTitle());
			op.title().x(X.center);
			TextStyle t = new TextStyle();
			t.setFontSize(9);
			t.setColor("rgba(30,144,255,0.8)");
			t.setFontFamily2("微软雅黑");
			t.fontSize(18);
			t.fontWeight("bolder");
			op.title().textStyle(t);
		}
		if (null!=paramPojo.getLegend()) {
			op.legend().data(paramPojo.getLegend());
			op.legend().y(Y.top);
			op.legend().x(X.left);
			op.legend().setPadding(new Object[]{40,0,0,0});
			op.legend().orient(Orient.vertical);
		}
		//异步触发
		op.tooltip().trigger(Trigger.item);
		op.tooltip().formatter(paramPojo.getTooltip());
        //工具栏
		op.toolbox().show(false);
		Pie pie = paramPojo.getData();
		// 检查 centerX 和 centerY 是否非空
		if (StringUtils.isNotBlank(paramPojo.getCenterX()) && StringUtils.isNotBlank(paramPojo.getCenterY())) {
			// 设置饼图的中心位置
			pie.center(paramPojo.getCenterX(), paramPojo.getCenterY());
		}
		// 检查 radius 是否非空
		if (StringUtils.isNotBlank(paramPojo.getRadius())) {
			// 设置饼图的半径
			pie.radius(paramPojo.getRadius());
		}
		pie.itemStyle().normal().label().formatter("{b}：{c}（{d}%）");
		op.series().add(pie);
		return op.toString();
	}

	/**
	 * 饼图内容初始化（图例右侧居中）
	 */
	public static String initPieXml1(PieChartParamPojo paramPojo) {
		GsonOption op = new GsonOption();
		if (StringUtils.isNotBlank(paramPojo.getTitle())) {
			op.title(paramPojo.getTitle());
			op.title().x(X.center);
			TextStyle t = new TextStyle();
			t.setColor("rgba(0, 0, 0, 100)");
			t.setFontFamily2("微软雅黑");
			t.fontSize(16);
			t.fontWeight("bold");
			op.title().textStyle(t);
		}
		if (null!=paramPojo.getLegend()) {
			op.legend().data(paramPojo.getLegend());
			op.legend().y("135");
			op.legend().x("725");
			op.legend().itemHeight(18);
			op.legend().itemWidth(18);
			op.legend().setPadding(new Object[]{0,0,0,0});
			op.legend().itemGap(30);
			op.legend().orient(Orient.vertical);
		}
		//异步触发
		op.tooltip().trigger(Trigger.item);
		op.tooltip().formatter(paramPojo.getTooltip());
		//工具栏
		op.toolbox().show(true)
				.feature(Tool.saveAsImage).padding(20).x(X.right).y(Y.top)
				.orient(Orient.horizontal)
				.padding(30, 160, 0, 0);
		Pie pie = paramPojo.getData();
		pie.radius(new String[]{"30%","60%"});
		pie.center(new String[]{"40%","53%"});
		pie.itemStyle().normal().label().formatter("{b}：{c}（{d}%）");
		TextStyle t = new TextStyle();
		t.setFontFamily2("微软雅黑");
		t.fontSize(14);
		pie.itemStyle().normal().label().textStyle(t);
		pie.itemStyle().normal().borderRadius(10);
		pie.itemStyle().normal().borderColor("#fff");
		op.series().add(pie);
		return op.toString();
	}
	public static void main(String[] args) {
		/*
		 * Map<String, List<Data>> dataMap = new HashMap<String, List<Data>>();
		 * List<Data> datas1 = new ArrayList<>(); datas1.add(new
		 * Data().value(1)); datas1.add(new Data().value(5)); datas1.add(new
		 * Data().value(4)); datas1.add(new Data().value(3)); datas1.add(new
		 * Data().value(2)); dataMap.put(null, datas1); List<Data> datas2 = new
		 * ArrayList<>(); datas2.add(new Data().value(2)); datas2.add(new
		 * Data().value(5)); datas2.add(new Data().value(2)); datas2.add(new
		 * Data().value(7)); datas2.add(new Data().value(4)); dataMap.put(null,
		 * datas2);
		 * 
		 * GsonOption op = createSimpleOption("测试", SeriesType.bar, new
		 * ArrayList<>(dataMap.keySet()), dataMap,null,null);
		 * System.out.println(op.toString());
*/
	}

    public static String initLineXml(LineChartParamPojo param) {
        boolean flag = false;//x轴文本内容过长，需换行
        Object[] xdate = param.getXdate();
        for(Object xname : xdate) {
            if(null == xname) {
                continue;
            }
            if(xname.toString().length() > 10) {
                flag = true;
                break;
            }
        }
        GsonOption op = new GsonOption();
        op.tooltip().trigger(Trigger.axis);
        // 定义工具
        op.toolbox()
                .show(true)
                .feature(new MagicType(Magic.line, Magic.bar), Tool.restore,
                        Tool.saveAsImage).padding(20).x(X.right).y(Y.top)
                .orient(Orient.horizontal)
                .padding(new Integer[]{30,30,0,0});
        Object[] legend = param.getLegend();
        X xlegend = param.getXlegend();
        if(null != legend) {
            op.legend().data(legend);
        }
        op.legend().y(Y.top);
        if(null == xlegend) {
            op.legend().x(X.left);
        } else {
            op.legend().x(xlegend);
        }
        op.legend().padding(new Integer[]{30,0,0,0});
        op.grid().y(85);
        op.grid().x(85);
        op.grid().x2(65);
        op.grid().y2("20%");
        if(flag) {
            op.grid().y2("26%");
        }
        op.title().x(X.center);
        op.title(param.getTitle());
        op.title().textStyle().fontFamily("微软雅黑");
        op.title().textStyle().fontWeight("bolder");
        op.title().textStyle().setColor("rgba(30,144,255,0.8)");

        List<Line> lines = param.getLines();
        if (param.isIfside()) {
            op.dataZoom().show(true);
            op.dataZoom().realtime(true);
            op.dataZoom().start(0);
            Line line = lines.get(0);
            List datas = line.getData();
            BigDecimal one = new BigDecimal(param.getSideNum());
            BigDecimal two = new BigDecimal(datas.size());
            BigDecimal divide = one.multiply(new BigDecimal(100)).divide(two, 0);
            op.dataZoom().end(divide.intValue());
        }

        // 横坐标
        CategoryAxis xAxis = new CategoryAxis();
        xAxis.setData(Arrays.asList(xdate));
        xAxis.setType(AxisType.category);
        xAxis.setShow(true);
        xAxis.setName(param.getxAxisTitle());
        AxisLabel xAxisLabel = new AxisLabel();
        xAxisLabel
                .setFormatter("function(value){return (value.length >10 ? (value.slice(0,10)+'\n'+value.slice(10,value.length)) : value )}");
        xAxisLabel.textStyle().color("#000000");
        xAxisLabel.setInterval(0);
        xAxisLabel.margin(15);
        if (param.isIfrotate()) {
            xAxisLabel.setRotate(20);
        }
        xAxis.setAxisLabel(xAxisLabel);
        xAxis.splitLine().show(true);
        op.xAxis().add(xAxis);

        // 纵坐标
        ValueAxis valueAxis = new ValueAxis();
        valueAxis.setType(AxisType.value);
        valueAxis.setName(param.getyAxisTitle());
        valueAxis.axisTick().show(false);
        valueAxis.axisLabel().textStyle().color("#000000");
        valueAxis.axisLabel().formatter("{value}");
        op.yAxis().add(valueAxis);

        op.series().addAll(lines);
        return op.toString();
    }
}
