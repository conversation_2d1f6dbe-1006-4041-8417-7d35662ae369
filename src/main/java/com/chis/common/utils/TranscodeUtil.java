package com.chis.common.utils;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.mts.model.v20140618.ListJobRequest;
import com.aliyuncs.mts.model.v20140618.ListJobResponse;
import com.aliyuncs.mts.model.v20140618.ListJobResponse.Job;
import com.aliyuncs.mts.model.v20140618.QueryAnalysisJobListRequest;
import com.aliyuncs.mts.model.v20140618.QueryAnalysisJobListResponse;
import com.aliyuncs.mts.model.v20140618.QueryAnalysisJobListResponse.AnalysisJob;
import com.aliyuncs.mts.model.v20140618.QueryAnalysisJobListResponse.AnalysisJob.Template;
import com.aliyuncs.mts.model.v20140618.QueryPipelineListResponse.Pipeline;
import com.aliyuncs.mts.model.v20140618.SubmitAnalysisJobRequest;
import com.aliyuncs.mts.model.v20140618.SubmitAnalysisJobResponse;
import com.aliyuncs.mts.model.v20140618.SubmitJobsRequest;
import com.aliyuncs.mts.model.v20140618.SubmitSnapshotJobRequest;
import com.aliyuncs.mts.model.v20140618.SubmitSnapshotJobResponse;
import com.aliyuncs.mts.model.v20140618.SubmitSnapshotJobResponse.SnapshotJob;
import com.aliyuncs.profile.DefaultProfile;
import com.chis.common.bean.InputFileBean;
import com.chis.common.bean.OutputFile;

/**
 * 转码操作
 * 
 * <AUTHOR>
 */
public class TranscodeUtil {

	private static final String MTS_REGION = "cn-shanghai";
    private static final String OSS_REGION = "oss-cn-shanghai";

    private static final String mtsEndpoint = "mts.cn-shanghai.aliyuncs.com";
    private static final String ossEndPoint = "https://oss-cn-shanghai.aliyuncs.com";

    private static String accessKeyId = "LTAIiJ5evZtJS5Cd";
    private static String accessKeySecret = "bHt1e3zK3kC8AJ4gRvBnrKFoYivPbH";
    // 管道ID
    private static String pipelineId = "4de683d0c14e4f72ae3821b469d89dbb";
    // 转码模板
    private static String transcodeTemplateId = "109153c299051f915245c05a5cb4c3ba";
    // 水印模板
    private static String waterMarkTemplateId = "75934a0195bc443891e46592484851ed";

    private static String inputBucket = "jswj-input";
    
    private static String outputBucket = "jswj-output";
    
    private static DefaultAcsClient aliyunClient = null;
    
    private static void getInstance() {
    	if (aliyunClient == null) {
    		 try {
				DefaultProfile.addEndpoint(MTS_REGION, MTS_REGION, "Mts", mtsEndpoint);
			} catch (ClientException e) {
				e.printStackTrace();
			}
        	aliyunClient = new DefaultAcsClient(DefaultProfile.getProfile(MTS_REGION, accessKeyId, accessKeySecret));
    	}
    }
	
	
    public static void transcodeJob(String key) {
		getInstance();
		// 判断管道ID 是否存在
		Pipeline p = PipelineUtil.queryPipeline(aliyunClient, pipelineId);
		if (p == null) {
			return ;
		}
		
		SubmitJobsRequest request = new SubmitJobsRequest();
		
		InputFileBean fileBean = new InputFileBean(OSS_REGION, inputBucket, key);
		OutputFile outFile = new OutputFile(key, transcodeTemplateId, null);
		// 判断模板ID是否为空
		if (TemplateUtil.getTemplateById(aliyunClient, transcodeTemplateId) == null) {
			// 获取系统默认模板
			String jobId = submitAnalysisJob(fileBean);
			AnalysisJob job = waitAnalysisJobComplete(jobId);
			List<String> tmp = getSupportTemplateIds(job);
			outFile.setTemplateId(tmp.get(0));
		}
		
		try {
			String input = JSONObject.toJSONString(fileBean);
			request.setInput(input);
			request.setOutputBucket(outputBucket);
			JSONArray jobConfigArray = new JSONArray();
	        jobConfigArray.add(outFile);
			request.setOutputs(jobConfigArray.toJSONString());
			request.setPipelineId(pipelineId);
			request.setOutputLocation(OSS_REGION);
			aliyunClient.getAcsResponse(request);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private static List<String> getSupportTemplateIds(AnalysisJob analysisJob) {
		List<String> templateIds = new ArrayList<String>(analysisJob
				.getTemplateList().size());
		for (Template template : analysisJob.getTemplateList()) {
			templateIds.add(template.getId());
		}
		return templateIds;
	}

	private static String submitAnalysisJob(InputFileBean file) {
		SubmitAnalysisJobRequest request = new SubmitAnalysisJobRequest();
		request.setInput(JSONObject.toJSONString(file));
		request.setPipelineId(pipelineId);
		SubmitAnalysisJobResponse response = null;
		try {
			response = aliyunClient.getAcsResponse(request);
			return response.getAnalysisJob().getId();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 等待当前job 是否完成
	 * @param client
	 * @param analysisJobId
	 * @return
	 */
	private static AnalysisJob waitAnalysisJobComplete(String analysisJobId) {
		QueryAnalysisJobListRequest request = new QueryAnalysisJobListRequest();
		request.setAnalysisJobIds(analysisJobId);
		QueryAnalysisJobListResponse response = null;
		try {
			while (true) {
				response = aliyunClient.getAcsResponse(request);
				AnalysisJob analysisJob = response.getAnalysisJobList().get(0);
				String status = analysisJob.getState();
				if ("Fail".equals(status)) {
					return null;
				}
				if ("Success".equals(status)) {
					return analysisJob;
				}
				Thread.sleep(5 * 1000);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 视频截图
	 * @param inputFile
	 * @return
	 */
	public static SnapshotJob submitSnapshotJob(String input, String key) {
		InputFileBean outputSnapshotFile = new InputFileBean();
		outputSnapshotFile.setBucket(outputBucket);
		outputSnapshotFile.setLocation(OSS_REGION);
		outputSnapshotFile.setObject(key);
		JSONObject jobConfig = new JSONObject();
		jobConfig.put("OutputFile", JSONObject.toJSONString(outputSnapshotFile));
		jobConfig.put("Time", 1000L); // snapshot time by ms
		SubmitSnapshotJobRequest request = new SubmitSnapshotJobRequest();
		request.setSnapshotConfig(jobConfig.toJSONString());
		InputFileBean inputFile = new InputFileBean();
		inputFile.setBucket(inputBucket);
		inputFile.setLocation(OSS_REGION);
		inputFile.setObject(input);
		request.setInput(JSONObject.toJSONString(inputFile));
		SubmitSnapshotJobResponse response = null;
		try {
			response = aliyunClient.getAcsResponse(request);
			return response.getSnapshotJob();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/** 
	 * 查询当前视频对象是否正在转换
	 */
	public static boolean getJobsIsRun(String object) {
		getInstance();
		ListJobRequest req = new ListJobRequest();
		req.setPipelineId(pipelineId);
		req.setState("Transcoding");
		req.setMaximumPageSize(100L);
		ListJobResponse response = null;
		try {
			response = aliyunClient.getAcsResponse(req);
			List<Job> jobs =  response.getJobList();
			for (Job j : jobs) {
				String obj = j.getOutput().getOutputFile().getObject();
				if (obj.equals(object)) {
					return false;
				}
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}
}
