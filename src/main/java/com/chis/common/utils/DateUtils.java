package com.chis.common.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 日期工具类, 继承org.apache.commons.lang.time.DateUtils类
 * 
 * <AUTHOR>
 * @version 2015-11-10
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
	
	private static String[] parsePatterns = {
			"yyyy-MM-dd HH:mm:ss.SSS", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM-dd", "yyyy-MM",
			"yyyy/MM/dd HH:mm:ss.SSS", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM/dd", "yyyy/MM",
			"yyyy.MM.dd HH:mm:ss.SSS", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM.dd", "yyyy.MM",
			"yyyyMMdd" ,"yyyyMM", "yyyy年MM月dd日", "HH:mm"};

	/**
	 * 得到当前日期字符串 格式（yyyy-MM-dd）
	 */
	public static String getDate() {
		return getDate("yyyy-MM-dd");
	}

	/**
	 * 得到当前日期字符串 格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
	 */
	public static String getDate(String pattern) {
		return DateFormatUtils.format(new Date(), pattern);
	}

	/**
	 * 将日期转成日期字符串格式 <br/>
	 * 
	 * @param date
	 *            java.util.Date <br/>
	 * @return String yyyy年MM月dd日 <br/>
	 */
	public static String getChineseStringDate(Date date) {
		SimpleDateFormat newk = new SimpleDateFormat("yyyy年MM月dd日");
		return newk.format(date);
	}

	/**
	 * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
	 */
	public static String formatDate(Date date, Object... pattern) {
		String formatDate = null;
		if (pattern != null && pattern.length > 0) {
			formatDate = DateFormatUtils.format(date, pattern[0].toString());
		} else {
			formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
		}
		return formatDate;
	}

	/**
	 * 得到日期时间字符串，转换格式（yyyy-MM-dd HH:mm:ss）
	 */
	public static String formatDateTime(Date date) {
		return formatDate(date, "yyyy-MM-dd HH:mm:ss");
	}

	/**
	 * 得到当前时间字符串 格式（HH:mm:ss）
	 */
	public static String getTime() {
		return formatDate(new Date(), "HH:mm:ss");
	}

	/**
	 * 得到当前日期和时间字符串 格式（yyyy-MM-dd HH:mm:ss）
	 */
	public static String getDateTime() {
		return formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
	}

	/**
	 * 得到当前年份字符串 格式（yyyy）
	 */
	public static String getYear() {
		return formatDate(new Date(), "yyyy");
	}

	/**
	 * 得到当前年份字符串 格式（yyyy）
	 */
	public static int getYearInt() {
		return Calendar.getInstance().get(Calendar.YEAR);
	}

	/**
	 * 得到当前月份字符串 格式（MM）
	 */
	public static String getMonth() {
		return formatDate(new Date(), "MM");
	}

	/**
	 * 得到当天字符串 格式（dd）
	 */
	public static String getDay() {
		return formatDate(new Date(), "dd");
	}

	/**
	 * 得到当前星期字符串 格式（E）星期几
	 */
	public static String getWeek() {
		return formatDate(new Date(), "E");
	}

	/**
	 * 返回日期所在一年中的周数
	 * 
	 * @param date
	 *            日期
	 * @return 返回月份
	 */
	public static int getWeek(java.util.Date date) {
		java.util.Calendar c = java.util.Calendar.getInstance();
		c.setTime(date);
		return c.get(java.util.Calendar.WEEK_OF_YEAR);
	}

	/**
	 * 取得某天是一年中的多少周(取得某一年共有多少周时使用) <br/>
	 * 星期一算第一天 <br/>
	 * 
	 * @param date
	 * @return
	 */
	public static int getWeekOfYear(Date date) {
		Calendar c = new GregorianCalendar();
		c.setFirstDayOfWeek(Calendar.MONDAY);
		c.setMinimalDaysInFirstWeek(7);
		c.setTime(date);
		return c.get(Calendar.WEEK_OF_YEAR);
	}

	/**
	 * 取得某一年共有多少周
	 * 
	 * @param year
	 * @return
	 */
	public static int getMaxWeekNumOfYear(int year) {
		Calendar c = new GregorianCalendar();
		c.set(year, Calendar.DECEMBER, 31, 23, 59, 59);
		return getWeekOfYear(c.getTime());
	}

	/**
	 * 得到周的第一天
	 * 
	 * @return
	 * @throws Exception
	 */
	public static Date getWeekFirstDay(int year, int week) throws Exception {
		Calendar calFirstDayOfTheYear = new GregorianCalendar(year,
				Calendar.JANUARY, 1);
		calFirstDayOfTheYear.add(Calendar.DATE, 7 * (week - 1));
		int dayOfWeek = calFirstDayOfTheYear.get(Calendar.DAY_OF_WEEK);
		Calendar calFirstDayInWeek = (Calendar) calFirstDayOfTheYear.clone();
		calFirstDayInWeek.add(Calendar.DATE,
				calFirstDayOfTheYear.getActualMinimum(Calendar.DAY_OF_WEEK)
						- dayOfWeek);
		return calFirstDayInWeek.getTime();
	}

	/**
	 * 得到周的最后一天
	 * 
	 * @return
	 * @throws Exception
	 */
	public static Date getWeekLastDay(int year, int week) throws Exception {
		Calendar calFirstDayOfTheYear = new GregorianCalendar(year,
				Calendar.JANUARY, 1);
		calFirstDayOfTheYear.add(Calendar.DATE, 7 * (week - 1));
		int dayOfWeek = calFirstDayOfTheYear.get(Calendar.DAY_OF_WEEK);
		Calendar calLastDayInWeek = (Calendar) calFirstDayOfTheYear.clone();
		calLastDayInWeek.add(Calendar.DATE,
				calFirstDayOfTheYear.getActualMaximum(Calendar.DAY_OF_WEEK)
						- dayOfWeek);
		return calLastDayInWeek.getTime();
	}

	/**
	 * 日期型字符串转化为日期 格式 { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm",
	 * "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy.MM.dd",
	 * "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm" }
	 */
	public static Date parseDate(Object str) {
		if (str == null) {
			return null;
		}
		try {
			return org.apache.commons.lang3.time.DateUtils.parseDate(str.toString(), parsePatterns);
		} catch (ParseException e) {
			return null;
		}
	}

	/**
	 * 获取过去的天数
	 * 
	 * @param date
	 * @return
	 */
	public static long pastDays(Date date) {
		long t = new Date().getTime() - date.getTime();
		return t / (24 * 60 * 60 * 1000);
	}

	/**
	 * 获取过去的小时
	 * 
	 * @param date
	 * @return
	 */
	public static long pastHour(Date date) {
		long t = new Date().getTime() - date.getTime();
		return t / (60 * 60 * 1000);
	}

	/**
	 * 获取过去的分钟
	 * 
	 * @param date
	 * @return
	 */
	public static long pastMinutes(Date date) {
		long t = new Date().getTime() - date.getTime();
		return t / (60 * 1000);
	}

	/**
	 * 转换为时间（天,时:分:秒.毫秒）
	 * 
	 * @param timeMillis
	 * @return
	 */
	public static String formatDateTime(long timeMillis) {
		long day = timeMillis / (24 * 60 * 60 * 1000);
		long hour = (timeMillis / (60 * 60 * 1000) - day * 24);
		long min = ((timeMillis / (60 * 1000)) - day * 24 * 60 - hour * 60);
		long s = (timeMillis / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
		long sss = (timeMillis - day * 24 * 60 * 60 * 1000 - hour * 60 * 60
				* 1000 - min * 60 * 1000 - s * 1000);
		return (day > 0 ? day + "," : "") + hour + ":" + min + ":" + s + "."
				+ sss;
	}

	/**
	 * 获取两个日期之间的天数
	 * 
	 * @param before
	 * @param after
	 * @return
	 */
	public static double getDistanceOfTwoDate(Date before, Date after) {
		long beforeTime = before.getTime();
		long afterTime = after.getTime();
		return (afterTime - beforeTime) / (1000 * 60 * 60 * 24);
	}

	/**
	 * 获取日期所在月份的第一天
	 * 
	 * @param date
	 * @return yyyy-mm-dd
	 */
	public static Date getMonthFirstDay(Date date) {
		Calendar calendar = new GregorianCalendar();
		calendar.setTime(date);
		calendar.add(Calendar.MONTH, 0);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return calendar.getTime();
	}

	/**
	 * 获取日期所在月份的最后一天
	 * 
	 * @param date
	 * @return yyyy-mm-dd
	 */
	public static Date getMonthLastDay(Date date) {
		Calendar calendar = new GregorianCalendar();
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_MONTH,
				calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		return calendar.getTime();
	}

	/**
	 * @param args
	 * @throws ParseException
	 */
	public static void main1(String[] args) throws ParseException {
		// System.out.println(formatDate(parseDate("2010/3/6")));
		// System.out.println(getDate("yyyy年MM月dd日 E"));
		// long time = new Date().getTime()-parseDate("2012-11-19").getTime();
		// System.out.println(time/(24*60*60*1000));
		// System.out.println(getMonthFirstDay(parseDate("2016-02-01")));
		// System.out.println(parseDate("2016-02-01"));

		// System.out.println(DateUtils.formatDate(new Date(), "yyyy"));

		Calendar theCa = Calendar.getInstance();
		theCa.setTime(new Date());
		theCa.add(theCa.DATE, 30);
		Date date = theCa.getTime();
		System.out.println(DateUtils.formatDate(date));
		Calendar instance = Calendar.getInstance();
		instance.set(Calendar.MONTH,0);
		instance.set(Calendar.DAY_OF_MONTH,1);
		instance.set(Calendar.HOUR_OF_DAY, 0);
		instance.set(Calendar.MINUTE, 0);
		instance.set(Calendar.SECOND, 0);
		instance.set(Calendar.MILLISECOND, 0);
		String s = DateUtils.formatDate(instance.getTime(), "yyyy-MM-dd hh:mm:ss");
		System.out.println(s);
		System.out.println(Calendar.YEAR);
	}

	public static Date getDayLastTime(Date date) {
		Calendar calendar = new GregorianCalendar();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		return calendar.getTime();
	}

	/**
	 * 获取指定年月的天数
	 * 
	 * @param year
	 *            年份
	 * @param month
	 *            月份
	 * @return
	 */
	public static int getDayEachMonth(int year, int month) {
		Calendar c = Calendar.getInstance();
		SimpleDateFormat sdf0 = new SimpleDateFormat("yyyy-MM");
		Date dd = null;
		try {
			dd = sdf0.parse(year + "-" + month);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		c.setTime(dd);
		int cc = c.getActualMaximum(Calendar.DAY_OF_MONTH);
		return cc;
	}

	/**
	 * 根据日期算出是周几
	 * 
	 * @param date
	 * @return
	 * @throws Exception
	 */
	public static int dayForWeek(Date date) throws Exception {
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		int dayForWeek = 0;
		if (c.get(Calendar.DAY_OF_WEEK) == 1) {
			dayForWeek = 7;
		} else {
			dayForWeek = c.get(Calendar.DAY_OF_WEEK) - 1;
		}
		return dayForWeek;
	}

	/**
	 * 根据日期计算距离当前日期最近的周
	 * 
	 * @param date
	 *            当前日期
	 * @param weekC
	 *            周几
	 * @param ifBefore
	 *            是否之前
	 * @return
	 * @throws Exception
	 */
	public static Date findWeekDay(Date date, int weekC, boolean ifBefore)
			throws Exception {
		if (null != date) {
			for (int i = 0; i < 7; i++) {
				int dayForWeek = dayForWeek(date);
				if (dayForWeek == weekC) {
					break;
				}
				date = DateUtils.addDays(date, ifBefore ? -1 : 1);
			}
		}
		return date;
	}

	/**
	 * 根据周数获取对应的定时器的英文
	 * 
	 * @param i
	 * @return
	 */
	public static String getWeekOfInt(int i) {
		String week = null;
		switch (i) {
		case 1:
			week = "MON";
			break;
		case 2:
			week = "TUE";
			break;
		case 3:
			week = "WED";
			break;
		case 4:
			week = "THU";
			break;
		case 5:
			week = "FRI";
			break;
		case 6:
			week = "SAT";
			break;
		case 7:
			week = "SUN";
			break;
		default:
			week = "MON";
		}
		return week;
	}

	/**
	 * 根据周数获取对应的定时器的英文
	 *
	 * @param i
	 * @return
	 */
	public static String getWeekCNOfInt(int i) {
		String week = null;
		switch (i) {
			case 1:
				week = "周一";
				break;
			case 2:
				week = "周二";
				break;
			case 3:
				week = "周三";
				break;
			case 4:
				week = "周四";
				break;
			case 5:
				week = "周五";
				break;
			case 6:
				week = "周六";
				break;
			case 7:
				week = "周天";
				break;
			default:
				week = "周一";
		}
		return week;
	}

	/**
	 * 获取但前月的最大天数
	 * 
	 * @param year
	 * @param month
	 * @return
	 */
	public static Integer getTheMaxDayNumOfMonth(String year, String month) {
		return getTheMaxDayNumOfMonth(Integer.parseInt(year),
				Integer.parseInt(month));
	}

	/**
	 * 获取但前月的最大天数
	 * 
	 * @param year
	 * @param month
	 * @return
	 */
	public static Integer getTheMaxDayNumOfMonth(Integer year, Integer month) {
		Calendar cal = Calendar.getInstance();
		// 年
		cal.set(Calendar.YEAR, year);
		// 月，因为Calendar里的月是从0开始，所以要-1
		cal.set(Calendar.MONTH, month - 1);
		// 当月的最大天数
		return cal.getActualMaximum(Calendar.DAY_OF_MONTH);
	}

	/**
	 * 取得某天是月中的多少周 星期一算第一天
	 * 
	 * @param date
	 * @return
	 */
	public static int getWeekOfMonth(Date date) {
		Calendar c = new GregorianCalendar();
		c.setFirstDayOfWeek(Calendar.MONDAY);
		c.setMinimalDaysInFirstWeek(7);
		c.setTime(date);
		return c.get(Calendar.WEEK_OF_MONTH);
	}

	/**
	 * 获取当前日期往后或者往前days的日期
	 * 
	 * @param days
	 *            -30 往前三十天 30 往后30天
	 * @return date
	 * <AUTHOR>
	 * @createDate 2017/10/11 10:28
	 */
	public static Date getDateByDays(int days) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.add(Calendar.DATE, days);
		return calendar.getTime();
	}
	/**
	 * <p>
	 * 方法描述：beforeDate和afterDate都存在，且beforeDate大于afterDate返回true，其他情况返回false
	 * </p>
	 * 【注：只判断日期，不判断时间】
	 * 
	 * @MethodAuthor mxp,2017/11/23,isBefore
	 */
	public static boolean isDateAfter(Date beforeDate, Date afterDate) {
		if (beforeDate != null && afterDate != null
				&& getDateOnly(beforeDate).after(getDateOnly(afterDate))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * <p>
	 * 方法描述：只获取日期
	 * </p>
	 * 
	 * @MethodAuthor mxp,2017/11/23,getDateOnly
	 */
	public static Date getDateOnly(Date date) {
		Calendar instance = Calendar.getInstance();
		instance.setTime(date);
		instance.set(Calendar.HOUR_OF_DAY, 0);
		instance.set(Calendar.MINUTE, 0);
		instance.set(Calendar.SECOND, 0);
		instance.set(Calendar.MILLISECOND, 0);
		return instance.getTime();
	}

	/**
	 * <p>
	 * 方法描述：beforeDate和afterDate都存在，且beforeDate大于afterDate返回false，其他情况返回true
	 * </p>
	 * 
	 * @MethodAuthor mxp,2017/11/23,isBefore
	 */
	public static boolean isAfter(Date beforeDate, Date afterDate) {
		if (beforeDate != null && afterDate != null
				&& beforeDate.after(afterDate)) {
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}

	/**
	 *  <p>方法描述：两个时间大小比较</p>
	 *  firstDate：第一个时间
	 *  endDate：第二个时间
	 *  symbol：符号
	 * @MethodAuthor hsj 2022/4/18 9:43
	 */
	public static boolean isCompareDate(Date firstDate, String symbol,Date endDate) {
		return isCompareDateTime( firstDate,  symbol, endDate,"yyyy-MM-dd");
	}
	/**
	 *  <p>方法描述：两个时间大小比较</p>
	 *  firstDate：第一个时间
	 *  endDate：第二个时间
	 *  symbol：符号
	 *  pattern:格式(默认为 yyyy-MM-dd HH:mm:ss)
	 * @MethodAuthor hsj 2022/4/16 18:38
	 */
	public static boolean isCompareDateTime(Date firstDate, String symbol,Date endDate,String pattern) {
		if (null == firstDate   || null == endDate   || StringUtils.isBlank(symbol)) {
			return Boolean.FALSE;
		}
		firstDate = getDateByDate(firstDate,pattern);
		endDate = getDateByDate(endDate,pattern);
		switch (symbol){
			case "<=":
				return firstDate.before(endDate) || firstDate.equals(endDate);
			case "<":
				return firstDate.before(endDate) ;
			case ">=":
				return firstDate.after(endDate) || firstDate.equals(endDate);
			case ">":
				return firstDate.after(endDate) ;
			case "=":
				return firstDate.equals(endDate);
		}
		return Boolean.FALSE;
	}
	/**
	 *  <p>方法描述：日期格式转换</p>
	 * @MethodAuthor hsj 2022/4/18 9:10
	 */
	private static Date getDateByDate(Date date, String pattern) {
		if(StringUtils.isBlank(pattern)){
			pattern = "yyyy-MM-dd HH:mm:ss";
		}
		try {
			return DateUtils.parseDate(DateUtils.formatDate(date, pattern), parsePatterns);
		} catch (ParseException e) {
			return date;
		}
	}

	public static void main(String[] args) {
//		System.out.println(DateUtils.parseDate("2022-01-02 22:01:01").before(DateUtils.parseDate("2022-01-02 22:01:02")));
//		System.out.println(DateUtils.isDateAfter(DateUtils.parseDate("2022-01-03 22:01:03"),DateUtils.parseDate("2022-01-02 22:01:02")));
	}
	/**
	 *  	 * <p>方法描述：</p>
 	 * 对时间进行加减，获取最新的时间
 	 * @param1 时间
 	 * @param2 加减的数值
 	 * @param3 加减的类型（年/月/日/时/分/秒）
 	 * @MethodAuthor zxf,2018年1月17日,getNDayAfterTime
	 */
	public static Date getNDayAfterTime(Date time, int n, int field) {
		GregorianCalendar gc = new GregorianCalendar();
		gc.setTime(time);
		gc.add(field, n);
		return gc.getTime();
	}
	/**
	 * 
 	 * <p>方法描述：获取两个日期之间的日期</p>
 	 * 
 	 * @MethodAuthor qrr,2018年3月1日,getDateByDays
	 */
	public static List<Date> getDuringDate(Date startDate,Date endDate){
		List<Date> lDate = new ArrayList<Date>();
		Calendar calBegin = Calendar.getInstance();
		calBegin.setTime(startDate);
		while (endDate.after(calBegin.getTime())) {
			calBegin.add(Calendar.DAY_OF_MONTH, 1);
			lDate.add(calBegin.getTime());
		}
		return lDate;
	}

	/**
	 * <p>方法描述：获取两个工作日之间的差距值</p>
	 *
	 * @MethodAuthor mxp, 2018-04-25,getDistanceOfWorkDay
	 */
	public static int  getDistanceOfWorkDay(Date startDate,Date endDate){
		int i=0;
		if(startDate==null||endDate==null){
			return 0;
		}
		startDate = getDateOnly(startDate);
		endDate = getDateOnly(endDate);
		if(startDate.compareTo(endDate)==0){
			return 0;
		}
		try {
			Calendar calendar = Calendar.getInstance();
			if(startDate.compareTo(endDate)<0){
				calendar.setTime(startDate);//
				while (calendar.getTime().compareTo(endDate)<0){
					if(!checkHoliday(calendar)){
						i++;
					}
					calendar.add(Calendar.DAY_OF_MONTH, 1);
				}
			}else{
				calendar.setTime(endDate);//
				while (calendar.getTime().compareTo(startDate)<0){
					if(!checkHoliday(calendar)){
						i--;
					}
					calendar.add(Calendar.DAY_OF_MONTH, 1);
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		return i;
	}

	/**
	 * <p>方法描述：获取某个日期开始，day个工作日后的日期</p>
	 *
	 * @MethodAuthor mxp, 2018-04-26,addDateByWorkDay
	 */
	public static Date addDateByWorkDay(Date startDate, int day){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(startDate);//设置当前时间
		try {
			for (int i = 0; i < day; i++) {
				calendar.add(Calendar.DAY_OF_MONTH, 1);
				if(checkHoliday(calendar)){
					i--;
				}
			}
			return calendar.getTime();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return calendar.getTime();
	}
	/**
	 * <p>方法描述：获取某个日期开始，day后的日期</p>
	 *
	 * @MethodAuthor mxp, 2018-04-26,addDateByWorkDay
	 */
	public static Date addDateByDay(Date startDate, int day){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(startDate);//设置当前时间
		try {
			for (int i = 0; i < day; i++) {
				calendar.add(Calendar.DAY_OF_MONTH, 1);
			}
			return calendar.getTime();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return calendar.getTime();
	}
	/**
	 * <p>方法描述：判断日期是否是工作日</p>
	 *
	 * @MethodAuthor mxp, 2018-04-26,checkHoliday
	 */
	public static boolean checkHoliday(Calendar calendar) throws Exception{
		//判断日期是否是周六周日
		if(calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY ||
				calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY){
			return true;
		}
		return false;
	}
	
	/**
	 * <p>方法描述：获取传入日期所在年的第一天</p>
 	 * 
 	 * @MethodAuthor rcj,2018年6月11日,getYearFirstDay
	 * @param date
	 * @return
	 */
	public static Date getYearFirstDay(final Date date) {  
	    final Calendar cal = Calendar.getInstance();  
	    cal.setTime(date);  
	    final int first = cal.getActualMinimum(Calendar.DAY_OF_YEAR);  
	    cal.set(Calendar.DAY_OF_YEAR, first);  
	    return cal.getTime();  
	}  
	
	
	/**
	 * <p>方法描述：获取传入日期所在年的最后一天</p>
 	 * 
 	 * @MethodAuthor rcj,2018年6月11日,getYearLastDay
	 * @param date
	 * @return
	 */
	public static Date getYearLastDay(final Date date) {  
	    final Calendar cal = Calendar.getInstance();  
	    cal.setTime(date);  
	    final int last = cal.getActualMaximum(Calendar.DAY_OF_YEAR);  
	    cal.set(Calendar.DAY_OF_YEAR, last);  
	    return cal.getTime();  
	}  
	
	/**
	 *<p>方法描述：多个日期之间比较大小 并且返回一个日期值 (tag == 1 时返回一个日期最大值，否则返回一个日期最小值)</p>
 	 * 
 	 * @MethodAuthor rcj,2018年11月9日,getMaxOrMinResult
	 * @param dateArray String[] dateArray = {"2013-04-01","2013-12-08","2013-04-28","2013-04-08", "2013-03-11"}
	 * @param tag==1 则返回一个最大日期，否则返回一个最小日期
	 * @return 最大日期
	 */
	 public static String getMaxOrMinResult(String[] dateArray,Integer tag) {
		 if(null != dateArray && dateArray.length > 0 ){
			 Map<String, Integer> dateMap = new TreeMap<String, Integer>();
			 int arrayLen;
			 arrayLen = dateArray.length;
			 for(int i = 0; i < arrayLen; i++){
				 String dateKey = dateArray[i];
				 if(dateMap.containsKey(dateKey)){
					 int value = dateMap.get(dateKey) + 1;
					 dateMap.put(dateKey, value);
				 }else{
					 dateMap.put(dateKey, 1);
				 }
			 }
			 Set<String> keySet = dateMap.keySet();
			 String[] sorttedArray = new String[keySet.size()];
			 Iterator<String> iter = keySet.iterator();
			 int index = 0;
			 while (iter.hasNext()) {
				 String key = iter.next();
				 sorttedArray[index++] = key;
			 }
			 int sorttedArrayLen = sorttedArray.length;
			 if(tag == 1){
				 System.out.println("最大日期是：" + sorttedArray[sorttedArrayLen - 1] + "," +
						 " 天数为" + dateMap.get(sorttedArray[sorttedArrayLen - 1]));
				 return sorttedArray[sorttedArrayLen - 1];
			 }else{
				 System.out.println("最小日期是：" + sorttedArray[0] + "," +
						 " 天数为" + dateMap.get(sorttedArray[0]));
				 return sorttedArray[0];
			 }
		 }
		 return null;
	  }


	  /***
	   * <p>方法描述: 计算虚岁</p>
	   *
	   * @MethodAuthor mxp,2018/11/23,calcNominalAgeByBirthDay
	   */
	public  static Integer calcNominalAgeByBirthDay(Date birthday) {
		if(birthday==null){
			return null;
		}
		int age = 0;
		try {
			Calendar now = Calendar.getInstance();
			now.setTime(new Date());// 当前时间

			Calendar birth = Calendar.getInstance();
			birth.setTime(birthday);

			if (birth.after(now)) {//如果传入的时间，在当前时间的后面，返回0岁
				age = 0;
			} else {
				age = now.get(Calendar.YEAR) - birth.get(Calendar.YEAR);
				if (now.get(Calendar.DAY_OF_YEAR) > birth.get(Calendar.DAY_OF_YEAR)) {
					age += 1;
				}
			}
			return age;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}



	/***
	 * <p>方法描述: 计算实岁</p>
	 *
	 * @MethodAuthor mxp,2018/11/23,calcRealAgeByBirthDay
	 */
	public static Integer calcRealAgeByBirthDay(Date  birthday) {
		// 得到当前时间的年、月、日
		if (birthday!=null){
			Calendar cal = Calendar.getInstance();
			int yearNow = cal.get(Calendar.YEAR);
			int monthNow = cal.get(Calendar.MONTH) + 1;
			int dayNow = cal.get(Calendar.DATE);
			//得到输入时间的年，月，日
			cal.setTime(birthday);
			int selectYear = cal.get(Calendar.YEAR);
			int selectMonth = cal.get(Calendar.MONTH) + 1;
			int selectDay =cal.get(Calendar.DATE);
			// 用当前年月日减去生日年月日
			int yearMinus = yearNow - selectYear;
			int monthMinus = monthNow - selectMonth;
			int dayMinus = dayNow - selectDay;
			int age = yearMinus;// 先大致赋值
			if (yearMinus <=0) {
				age = 0;
			}if (monthMinus < 0) {
				age=age-1;
			} else if (monthMinus == 0) {
				if (dayMinus < 0) {
					age=age-1;
				}
			}
			return age;
		}
		return null;
	}

	/***
	 * <p>方法描述: 计算两个日期之前相差多少个月，只金计算月份</p>
	 *
	 * @MethodAuthor mxp,2018/11/23,calcMonthsBetweenTwoDate
	 */
	public static long calcMonthsBetweenTwoDate(Date beforeDate, Date afterDate){
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(beforeDate);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(afterDate);
        int day1= cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if(year1 != year2)   //同一年
        {
            int timeDistance = 0 ;
            for(int i = year1 ; i < year2 ; i ++)
            {
                if(i%4==0 && i%100!=0 || i%400==0)    //闰年
                {
                    timeDistance += 366;
                }
                else    //不是闰年
                {
                    timeDistance += 365;
                }
            }

            return timeDistance + (day2-day1) ;
        }
        else    //不同年
        {
            return day2-day1;
        }
	}
	/**
	 *  <p>方法描述：计算两个日期相差年份</p>
	 * @MethodAuthor hsj 2023-06-16 7:35
	 */
	public static long calcYearBetweenTwoDate(Date beforeDate, Date afterDate){
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(beforeDate);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(afterDate);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
		return year2 - year1;
	}
	/***
	 * <p>方法描述: 计算两个日期之间相差的时间段</p>
	 *
	 * @MethodAuthor mxp,2018/11/23,calcYearMonthBetweenTwoDate
	 */
    public static String calcYearMonthBetweenTwoDate(Date beforeDate,Date afterDate) {
        if (beforeDate == null || afterDate == null) {
            return null;
        }
        int result=0;
        String resultString = null;
        int startYear=getYear(beforeDate);
        int startMonth=getMonth(beforeDate);
        int startDay=getDay(beforeDate);
        int endYear=getYear(afterDate);
        int endMonth=getMonth(afterDate);
        int endDay=getDay(afterDate);
        if (startDay>endDay){
            if (endDay==getDaysOfMonth(getYear(new Date()),endMonth)){   //也满足一月
                result=(endYear-startYear)*12+endMonth-startMonth;
            }else{
                result=(endYear-startYear)*12+endMonth-startMonth-1;
            }
        }else{
            result=(endYear-startYear)*12+endMonth-startMonth;
        }
        if(result > 0 && result<12){
            resultString = result+"个月";
        }else if(result >= 12){
            int i=result%12;
            resultString = (int)Math.floor(result/12) + "年"+ i +"个月";
        }else if(result == 0){
        	double day = getDistanceOfTwoDate(beforeDate, afterDate);
            resultString = Double.valueOf(day).intValue()+1 + "天";//当天算1天
        }
        return resultString;
    }

    public static int getDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DATE);
    }

    /**
     * 返回日期的月份，1-12,即yyyy-MM-dd中的MM
     *
     * @param date
     * @return
     */
    public static int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 返回日期的年,即yyyy-MM-dd中的yyyy
     *
     * @param date
     *            Date
     * @return int
     */
    public static int getYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }

    public static int getDaysOfMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month - 1, 1);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
    * @Description : 根据出生日期计算年龄
    * @MethodAuthor: anjing
    * @Date : 2019/12/4 15:30
    **/
    public static int calAgeByBirtDate(Date birthDate) {
        int age = 0;
        Date today = new Date();
        int year = DateUtils.getYear(birthDate);
        int yearNow = DateUtils.getYear(today);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(birthDate);
        calendar.set(Calendar.YEAR, DateUtils.getYearInt());
        long millis = calendar.getTimeInMillis();
        long todayMillis = DateUtils.getDateOnly(today).getTime();
        age = yearNow - Integer.valueOf(year);

        if (todayMillis - millis < 0) {
            age -= 1;
        }
        return age;
    }

    /**
     * @Description : 根据身份证号计算年龄
     * @MethodAuthor: anjing
     * @Date : 2019/11/25 14:56
     **/
    public static int calAgeByIdc(String idc) {
        String year = "";
        String date = "";
        int age = 0;
        if (idc.length() == 18) {
            year = idc.substring(6, 10);
            date = idc.substring(6, 14);
        } else {
            year = new StringBuilder("19").append(idc.substring(6, 8)).toString();
            date = new StringBuilder("19").append(idc.substring(6, 12)).toString();
        }
        try {
            Date today = new Date();
            int yearNow = DateUtils.getYear(today);
            Date birthDate = DateUtils.parseDate(date, "yyyyMMdd");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(birthDate);
            calendar.set(Calendar.YEAR, DateUtils.getYearInt());
            long millis = calendar.getTimeInMillis();
            long todayMillis = DateUtils.getDateOnly(today).getTime();
            age = yearNow - Integer.valueOf(year);

            if (todayMillis - millis < 0) {
                age -= 1;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return age;
    }

	/**
	 * @Description : 重写DateUtils的日期转换方法，忽略日期格式参数，自动匹配日期格式进行日期转换,
	 * 以后禁止使用此方法，直接调用parseDate(Object str)方法，所有参数格式见parsePatterns常量，缺的话考虑增加
	 * 如需一定要传入日期格式，调用父级包名中该方法
	 * @MethodAuthor: xt
	 * @Date : 2020/11/13 16:34
	 **/
	@Deprecated
	public static Date parseDate(String str, String... parsePatterns) throws ParseException {
		return parseDate(str);
	}

	/**
	 * @param date 时间
	 * @return 汉字年月日
	 */
	public static String formatStr(Date date) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-M-d");
		String format = sdf.format(date);
		String[] split = format.split("-");
		//2020-7-18 ==> 二O二O年七月十八日
		String year = split[0];
		String month = split[1];
		String day = split[2];
		String strYear = getStrYear(year);
		String strMonth = getStrMonth(month);
		String strDay = getStrDay(day);
		String formatStr = strYear + "年" + strMonth + "月" + strDay + "日";
		return formatStr;
	}
	/**
	 * @param day 数字日 13
	 * @return 汉字日 13==>十三
	 */
	public static String getStrDay(String day) {
		int dayLength = day.length();
		String strDay = "";
		if (dayLength < 2) {
			strDay = numToStr(day);
			return strDay;
		} else {
			if (day.startsWith("1")) {
				if (day.equals("10")) {
					return "十";
				} else {
					String substring = day.substring(1);
					strDay = "十" + numToStr(substring);
					return strDay;
				}
			} else if (day.startsWith("2")) {
				if (day.equals("20")) {
					return "二十";
				} else {
					String substring = day.substring(1);
					strDay = "二十" + numToStr(substring);
					return strDay;
				}
			} else if (day.startsWith("3")) {
				if (day.equals("30")) {
					return "三十";
				} else {
					String substring = day.substring(1);
					strDay = "三十" + numToStr(substring);
					return strDay;
				}
			}
		}
		return "";
	}
	/**
	 * @param month 数字月 7
	 * @return 中文月 7==>七
	 */
	public static String getStrMonth(String month) {
		String strMonth = numToStr(month);
		return strMonth;
	}
	/**
	 * @param year 数字年 2020
	 * @return 汉字年 二零二零
	 */
	public static String getStrYear(String year) {
		int length = year.length();
		String strYear = "";
		for (int i = 0; i < length; i++) {
			String substring = year.substring(i, i + 1);
			strYear = strYear + numToStr(substring);
		}
		return strYear;
	}
	/**
	 * @param nun 0~9 ==>零~九
	 * @return
	 */
	public static String numToStr(String nun) {
		if (nun.equals("0")) {
			return "〇";
		} else if (nun.equals("1")) {
			return "一";
		} else if (nun.equals("2")) {
			return "二";
		} else if (nun.equals("3")) {
			return "三";
		} else if (nun.equals("4")) {
			return "四";
		} else if (nun.equals("5")) {
			return "五";
		} else if (nun.equals("6")) {
			return "六";
		} else if (nun.equals("7")) {
			return "七";
		} else if (nun.equals("8")) {
			return "八";
		} else if (nun.equals("9")) {
			return "九";
		} else if (nun.equals("10")) {
			return "十";
		} else if (nun.equals("11")) {
			return "十一";
		} else if (nun.equals("12")) {
			return "十二";
		}
		return "";
	}
	/**
	 *  <p>方法描述：获取固定的日期
	 *  year：距离当年的年份
	 *  mon：月份
	 *  day：日
	 *  </p>
	 * @MethodAuthor hsj 2023-06-20 9:28
	 */
	public static Date getFixedDate(Integer year, Integer month, Integer day) {
		Date now = new Date();
		Calendar c = Calendar.getInstance();
		try {
			c.setTime(now);
			c.add(Calendar.YEAR, year);
			c.set(Calendar.MONTH, month-1);
			c.set(Calendar.DATE, day);
		}catch (Exception e){
			e.printStackTrace();
		}
		return c.getTime();
	}

	/**
	* <p>Description：获取去年的指定日期 </p>
	* <p>Author： yzz 2023-12-19 </p>
	*/
	public static Date getLastYearDate(Date date){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.YEAR, -1);
		return calendar.getTime();
	}


	/**
	 *  <p>方法描述：指定某个日期的某年某月某日后的日期</p>
	 * @MethodAuthor hsj 2025-02-10 14:17
	 * param date 指定日期
	 * param years 年
	 * param months 月
	 * param days 日
	 */
	public static Date getDateByIssuer(Date date,Integer years,Integer months,Integer days){
		if(null == date){
			return date;
		}
		try {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			int yearsToAdd = years == null ? 0 : years;
			int monthsToAdd = months == null ? 0 : months;
			int daysToAdd = days == null ? 0 : days;
			calendar.add(Calendar.YEAR, yearsToAdd);
			calendar.add(Calendar.MONTH, monthsToAdd);
			calendar.add(Calendar.DAY_OF_MONTH, daysToAdd);
			return calendar.getTime();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return date;
	}

}
