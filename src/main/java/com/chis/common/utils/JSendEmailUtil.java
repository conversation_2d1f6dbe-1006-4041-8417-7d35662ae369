package com.chis.common.utils;

import java.util.Date;
import java.util.Enumeration;
import java.util.Properties;
import java.util.Vector;

import javax.mail.Authenticator;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;


public class JSendEmailUtil {
	static Vector file = new Vector();// 附件文件集合
	public JSendEmailUtil(){
		
	}
	public static String transferChinese(String strText) {
		try {
		   strText = MimeUtility.encodeText(new String(strText.getBytes(), "GB2312"), "GB2312", "B");
		} catch (Exception e) {
		   e.printStackTrace();
		}
		  return strText;
		}
	public void attachfile(String fname) {
		  file.addElement(fname);
		}
	public static boolean sendEmail(String toEmail,String content,String subject,String host,final String username,final String password,String url){
		Properties props = System.getProperties();
		props.put("mail.smtp.host", host);
		props.put("mail.smtp.auth", "true");
		Session session = Session.getDefaultInstance(props, new Authenticator() {
		public PasswordAuthentication getPasswordAuthentication() {
			return new PasswordAuthentication(username, password);
		}
	  });
		try {
			// 构造MimeMessage 并设定基本的值
			MimeMessage msg = new MimeMessage(session);
			msg.setFrom(new InternetAddress(url));
			InternetAddress[] address = { new InternetAddress(toEmail) };
			msg.setRecipients(Message.RecipientType.TO, address);
			subject = transferChinese(subject);
			msg.setSubject(subject);
			// 构造Multipart
			Multipart mp = new MimeMultipart();
			// 向Multipart添加正文
			MimeBodyPart mbpContent = new MimeBodyPart();
			mbpContent.setText(content);
			// 向MimeMessage添加（Multipart代表正文）
			mp.addBodyPart(mbpContent);
			// 向Multipart添加附件
			Enumeration efile = file.elements();
			while (efile.hasMoreElements()) {
			MimeBodyPart mbpFile = new MimeBodyPart();
			// 向MimeMessage添加（Multipart代表附件）
			mp.addBodyPart(mbpFile);
			}
			file.removeAllElements();
			// 向Multipart添加MimeMessage
			msg.setContent(mp);
			msg.setSentDate(new Date());
			// 发送邮件
			Transport.send(msg);
			System.out.println("发送成功！");
		} catch (MessagingException mex) {
			mex.printStackTrace();
			Exception ex = null;
			if ((ex = mex.getNextException()) != null) {
			ex.printStackTrace();
		}
		  System.out.println("发送失败" + mex.getMessage());
		  return false;
		}
		 return true;
	}
 
	public static void main(String[] args) {
//		sendEmail("<EMAIL>","做出选择删除","建议：","smtp.163.com","<EMAIL>","Shbjkl001","<EMAIL>");
		}
}
