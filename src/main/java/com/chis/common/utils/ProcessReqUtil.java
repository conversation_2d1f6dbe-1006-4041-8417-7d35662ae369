package com.chis.common.utils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemIterator;
import org.apache.commons.fileupload.FileItemStream;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;

import com.chis.common.bean.FileBaseBean;

/**
 * 处理接收的请求
 * 
 * <AUTHOR>
 * @history 2014-09-22 创建文件
 * 
 */
public class ProcessReqUtil {

	/**
	 * 处理FORM表单形式提交过来的值
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	public static Map<String, Object> process(HttpServletRequest request, HttpServletResponse response) {
		// 接收参数值
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			response.setContentType("text/html;charset=UTF-8");
			request.setCharacterEncoding("UTF-8");
			response.setCharacterEncoding("UTF-8");

			DiskFileItemFactory factory = new DiskFileItemFactory();
			ServletFileUpload upload = new ServletFileUpload(factory);
			// 参数列表
			List<FileItem> items = new ArrayList<FileItem>();
			items = upload.parseRequest(request);
			// 得到所有的文件
			Iterator<FileItem> it = items.iterator();
			while (it.hasNext()) {
				FileItem fItem = (FileItem) it.next();
				String fName = "";
				Object fValue = null;

				// 普通文本框的值
				if (fItem.isFormField()) {
					fName = fItem.getFieldName();
					fValue = fItem.getString("UTF-8");
					map.put(fName, fValue);
					// 获取上传文件
				} else {
					fName = fItem.getFieldName();
					fValue = fItem.getInputStream();
					map.put(fName, fValue);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return map;
	}
	
	/**
	 * 处理FORM表单形式提交过来的值
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	public static Map<String, FileBaseBean> process(HttpServletRequest request) {
		// 接收参数值
		Map<String, FileBaseBean> map = new HashMap<String, FileBaseBean>();
		try {
			request.setCharacterEncoding("UTF-8");
			DiskFileItemFactory factory = new DiskFileItemFactory();
			ServletFileUpload upload = new ServletFileUpload(factory);
			// 参数列表
			List<FileItem> items = new ArrayList<FileItem>();
			items = upload.parseRequest(request);
			// 得到所有的文件
			Iterator<FileItem> it = items.iterator();
			while (it.hasNext()) {
				FileItem fItem = (FileItem) it.next();
				String fName = "";
				FileBaseBean bean = new FileBaseBean();
				// 普通文本框的值
				if (fItem.isFormField()) {
					fName = fItem.getFieldName();
					Object fValue = fItem.getString("UTF-8");
					bean.setFileName(fName);
					bean.setIn(fValue);
					bean.setFormFiled(true);
					map.put(fName, bean);
					// 获取上传文件
				} else {
					fName = fItem.getFieldName();
					bean.setFileName(fItem.getName());
					bean.setIn(fItem);
					bean.setContentType(fItem.getContentType());
					bean.setFormFiled(false);
					map.put(fName, bean);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return map;
	}

	/**
	 * 将http请求中的Map<String,String[]>转换成Map<String,String>
	 * 
	 * @param requestMap
	 * @return
	 */
	public static Map<String, String> processRequestMap(HttpServletRequest request) {
		Map<String, String> map = new HashMap<String, String>();
		if (null != request) {
			// 获取request请求的参数集合
			Map<String, String[]> requestMap = request.getParameterMap();
			// 将request请求集合Map<String,String[]>格式化成Map<String,String>
			if (null != requestMap && requestMap.size() > 0) {
				Iterator<String> it = requestMap.keySet().iterator();
				while (it.hasNext()) {
					String next = it.next();
					String[] strArr = requestMap.get(next);
					StringBuilder val = new StringBuilder();
					if( null != strArr && strArr.length > 0)	{
						for( String s : strArr)	{
							if( StringUtils.isNotBlank(s)){
								val.append(",").append(s);
							}
						}
						if(val.length() > 0 ) val.deleteCharAt(0);
					}
					
					map.put(next,  Encodes.urlDecode(val.toString()));
				}
			}
		}
		return map;
	}

	/**
	 * 设置接口请求编码
	 * 
	 * @param request
	 * @param response
	 */
	public static void setReqResEncoding(HttpServletRequest request, HttpServletResponse response) {
		if (null != request && null != response) {
			try {
				request.setCharacterEncoding("UTF-8");
				response.setCharacterEncoding("UTF-8");
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 设置跨域访问
	 * 
	 * @param response
	 */
	public static void setRespCrossDomain(HttpServletResponse response) {
		if (null != response) {
			response.setHeader("Access-Control-Allow-Origin", "*");
		}
	}
	
	/**
	 * 接收的文件是data.zip-->data.json
	 * 
	 * @return
	 */
	public static String parseInputStream(HttpServletRequest req) {
		try {
			StringBuilder json = new StringBuilder();
			DiskFileItemFactory factory = new DiskFileItemFactory();
			// 指定在内存中缓存数据大小,单位为byte,这里设为1Mb
			factory.setSizeThreshold(4 * 1024 * 1024);
			ServletFileUpload sfu = new ServletFileUpload(factory);
			sfu.setHeaderEncoding("UTF-8");

			FileItemIterator fii = sfu.getItemIterator(req);// 解析request请求
			while (fii.hasNext()) {
				FileItemStream fis = fii.next();// 从集合中获得一个文件流
				ZipInputStream zis = new ZipInputStream(fis.openStream());
				ZipEntry zipEntry = zis.getNextEntry();
				while (zipEntry != null) {
					InputStreamReader isr = new InputStreamReader(zis, "UTF-8");
					BufferedReader br = new BufferedReader(isr);
					String str = null;
					while ((str = br.readLine()) != null) {
						json.append(str);
					}
					zipEntry = null;
					zipEntry = zis.getNextEntry();
				}
			}
			return json.toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public static String parseInputStream(InputStream in) {
		try {
			StringBuilder json = new StringBuilder();
			ZipInputStream zis = new ZipInputStream(in);
			ZipEntry zipEntry = zis.getNextEntry();
			while (zipEntry != null) {
				InputStreamReader isr = new InputStreamReader(zis, "UTF-8");
				BufferedReader br = new BufferedReader(isr);
				String str = null;
				while ((str = br.readLine()) != null) {
					json.append(str);
				}
				zipEntry = null;
				zipEntry = zis.getNextEntry();
			}
			return json.toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
}
