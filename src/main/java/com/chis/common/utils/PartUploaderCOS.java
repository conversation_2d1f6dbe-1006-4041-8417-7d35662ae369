package com.chis.common.utils;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.PartETag;
import com.qcloud.cos.model.UploadPartRequest;
import com.qcloud.cos.model.UploadPartResult;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

public class PartUploaderCOS implements Runnable {

	private static List<PartETag> partETags;

	private InputStream inputStream;

	private long startPos;

	private long partSize;
	private int partNumber;

	private String bucketName;

	private String key;

	private String uploadId;

	private COSClient cosClient;

	public PartUploaderCOS(InputStream inputStream, long startPos, long partSize,
						   int partNumber, String uploadId, String bucketName, String key,
						   COSClient cosClient, List<PartETag> partETags) {
		this.inputStream = inputStream;
		this.startPos = startPos;
		this.partSize = partSize;
		this.partNumber = partNumber;
		this.uploadId = uploadId;
		this.bucketName = bucketName;
		this.key = key;
		this.cosClient = cosClient;
		this.partETags = partETags;
	}

	@Override
	public void run() {
		try {
//			inputStream.skip(this.startPos);
			UploadPartRequest uploadRequest = new UploadPartRequest().withBucketName(bucketName).
					withUploadId(uploadId).withKey(key).withPartNumber(partNumber).
					withInputStream(inputStream).withPartSize(partSize);
			UploadPartResult uploadPartResult = cosClient.uploadPart(uploadRequest);
			synchronized (partETags) {
				partETags.add(uploadPartResult.getPartETag());
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (inputStream != null) {
				try {
					inputStream.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

}
