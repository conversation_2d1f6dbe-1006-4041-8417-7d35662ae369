package com.chis.common.utils;

import java.io.*;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

public class ObjectCopyUtil {
	/**
	 * 利用反射实现对象之间属性复制
	 * 
	 * @param from
	 * @param to
	 */
	public static void copyProperties(Object from, Object to) throws Exception {
		copyPropertiesExclude(from, to, null);
	}
	
	/**
	 * 复制对象属性,默认空值也复制
	 * 
	 * @param from
	 * @param to
	 * @param excludsArray
	 *            排除属性列表
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public static void copyPropertiesExclude(Object from, Object to, String[] excludsArray) throws Exception {
		copyPropertiesExclude(from, to, excludsArray, true);
	}
	
	/**
	 * 对象属性值复制，仅复制指定名称的属性值,是否赋值空值
	 * 
	 * @param from
	 * @param to
	 * @param excludsArray
	 *            排除属性列表
	 * @param ifNullSet
	 *            空值是否还需要设置
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public static void copyPropertiesExclude(Object from, Object to, String[] excludsArray,boolean ifNullSet) throws Exception {
		List<String> excludesList = null;
		if (excludsArray != null && excludsArray.length > 0) {
			excludesList = Arrays.asList(excludsArray); // 构造列表对象
		}
		Method[] fromMethods = from.getClass().getMethods();
		Method[] toMethods = to.getClass().getMethods();
		Method fromMethod = null, toMethod = null;
		String fromMethodName = null, toMethodName = null;
		for (int i = 0; i < fromMethods.length; i++) {
			fromMethod = fromMethods[i];
			fromMethodName = fromMethod.getName();
			if (!fromMethodName.startsWith("get"))
				continue;
			// 排除列表检测
			String str = fromMethodName.substring(3);
			if (excludesList != null && excludesList.contains(str.substring(0, 1).toLowerCase() + str.substring(1))) {//toLowerCase()
				continue;
			}
			toMethodName = "set" + fromMethodName.substring(3);
			toMethod = findMethodByName(toMethods, toMethodName);
			if (toMethod == null)
				continue;
			Object value = fromMethod.invoke(from, new Object[0]);
			if(!ifNullSet)	{
				if (value == null)
					continue;
				// 集合类判空处理
				if (value instanceof Collection) {
					Collection newValue = (Collection) value;
					if (newValue.size() <= 0)
						continue;
				}
			}
			try {
				toMethod.invoke(to, new Object[] { value });
			} catch (Exception e) {
				System.err.println("【fromMethod】:" + fromMethod);
				throw new RuntimeException(e);
			}
			
		}
	}

	/**
	 * 对象属性值复制，仅复制指定名称的属性值,默认控制也赋值
	 * 
	 * @param from
	 * @param to
	 * @param includsArray
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public static void copyPropertiesInclude(Object from, Object to, String[] includsArray) throws Exception {
		copyPropertiesInclude(from, to, includsArray, true);
	}
	
	/**
	 * 对象属性值复制，仅复制指定名称的属性值
	 * 
	 * @param from
	 * @param to
	 * @param includsArray
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public static void copyPropertiesInclude(Object from, Object to, String[] includsArray,boolean ifNullSet) throws Exception {
		List<String> includesList = null;
		if (includsArray != null && includsArray.length > 0) {
			includesList = Arrays.asList(includsArray); // 构造列表对象
		} else {
			return;
		}
		Method[] fromMethods = from.getClass().getMethods();
		Method[] toMethods = to.getClass().getMethods();
		Method fromMethod = null, toMethod = null;
		String fromMethodName = null, toMethodName = null;
		for (int i = 0; i < fromMethods.length; i++) {
			fromMethod = fromMethods[i];
			fromMethodName = fromMethod.getName();
			if (!fromMethodName.startsWith("get"))
				continue;
			// 排除列表检测
			String str = fromMethodName.substring(3);
			if (!includesList.contains(str.substring(0, 1).toLowerCase() + str.substring(1))) {
				continue;
			}
			toMethodName = "set" + fromMethodName.substring(3);
			toMethod = findMethodByName(toMethods, toMethodName);
			if (toMethod == null)
				continue;
			Object value = fromMethod.invoke(from, new Object[0]);
			if( !ifNullSet )	{
				if (value == null)
					continue;
				// 集合类判空处理
				if (value instanceof Collection) {
					Collection newValue = (Collection) value;
					if (newValue.size() <= 0)
						continue;
				}
			}
			toMethod.invoke(to, new Object[] { value });
		}
	}

	/**
	 * 从方法数组中获取指定名称的方法
	 * 
	 * @param methods
	 * @param name
	 * @return
	 */
	public static Method findMethodByName(Method[] methods, String name) {
		for (int j = 0; j < methods.length; j++) {
			if (methods[j].getName().equals(name))
				return methods[j];
		}
		return null;
	}
	
	/**
	 * 利用反射实现对象之间属性复制
	 * 去除了"rid","createDate","createManid","modifyDate","modifyManid"
	 * 
	 * @param from
	 * @param to
	 */
	public static void copyPropertiesWithOutCommParams(Object from, Object to) throws Exception {
		copyPropertiesExclude(from, to, new String[] { "rid", "createDate", "createManid", "modifyDate", "modifyManid" });
	}

	/**
	 * 使用序列化方法深拷贝List<T>
	 * @param src 源List
	 * @return 深拷贝得到的List
	 */
	public static <T> List<T> deepCopy(List<T> src) {
		try (ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
			 ObjectOutputStream out = new ObjectOutputStream(byteOut);) {
			out.writeObject(src);
			try (ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
				 ObjectInputStream in = new ObjectInputStream(byteIn);) {
				@SuppressWarnings("unchecked")
				List<T> dest = (List<T>) in.readObject();
				return dest;
			} catch (Exception e) {
				e.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return new ArrayList<>();
	}
	
}
