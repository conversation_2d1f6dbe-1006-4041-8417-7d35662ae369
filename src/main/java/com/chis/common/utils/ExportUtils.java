package com.chis.common.utils;

import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * <p>类描述：导出功能</p>
 * @ClassAuthor qrr,2020年5月27日,ExportUtils
 * */
public class ExportUtils {
	/**
 	 * <p>方法描述：设置标题样式</p>
 	 * @MethodAuthor qrr,2020年5月27日,setTitleStyle
	 * */
	public static XSSFCellStyle setTitleStyle(XSSFWorkbook wBook) {
		XSSFCellStyle style = wBook.createCellStyle();
		style.setAlignment(XSSFCellStyle.ALIGN_CENTER);
		style.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
		Font font = wBook.createFont();
		font.setBoldweight(Font.BOLDWEIGHT_BOLD);
		font.setFontHeightInPoints((short)20);
		font.setFontName("宋体");
		font.setFontHeightInPoints((short) 12);
		style.setFont(font);
		return style;
	}
	/**
 	 * <p>方法描述：设置表头样式</p>
 	 * @MethodAuthor qrr,2020年5月27日,setHeadStyle
	 * */
	public static XSSFCellStyle setHeadStyle(XSSFWorkbook wBook) {
		XSSFCellStyle style = wBook.createCellStyle();
		style.setAlignment(XSSFCellStyle.ALIGN_CENTER);
		style.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
		Font font = wBook.createFont();
		font.setBoldweight(Font.BOLDWEIGHT_BOLD);
		font.setFontHeightInPoints((short)20);
		font.setFontName("宋体");
		font.setFontHeightInPoints((short) 11);
		style.setFont(font);
		return style;
	}
	/**
 	 * <p>方法描述：设置数据居中样式</p>
 	 * @MethodAuthor qrr,2020年5月27日,setDataCenterStyle
	 * */
	public static XSSFCellStyle setDataCenterStyle(XSSFWorkbook wBook) {
		XSSFCellStyle style = wBook.createCellStyle();
		style.setAlignment(XSSFCellStyle.ALIGN_CENTER);
		style.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
		Font font = wBook.createFont();
		font.setFontHeightInPoints((short)20);
		font.setFontName("宋体");
		font.setFontHeightInPoints((short) 11);
		style.setFont(font);
		return style;
	}
	/**
 	 * <p>方法描述：设置数据居左样式</p>
 	 * @MethodAuthor qrr,2020年5月27日,setDataLeftStyle
	 * */
	public static XSSFCellStyle setDataLeftStyle(XSSFWorkbook wBook) {
		XSSFCellStyle style = wBook.createCellStyle();
		style.setAlignment(XSSFCellStyle.ALIGN_LEFT);
		style.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
		Font font = wBook.createFont();
		font.setFontHeightInPoints((short)20);
		font.setFontName("宋体");
		font.setFontHeightInPoints((short) 11);
		style.setFont(font);
		return style;
	}
	/**
 	 * <p>方法描述：设置合计居左样式</p>
 	 * @MethodAuthor qrr,2020年5月27日,setHjStyle
	 * */
	public static XSSFCellStyle setHjStyle(XSSFWorkbook wBook) {
		XSSFCellStyle style = wBook.createCellStyle();
		style.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
		style.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
		Font font = wBook.createFont();
		font.setBoldweight(Font.BOLDWEIGHT_BOLD);
		font.setFontHeightInPoints((short)20);
		font.setFontName("宋体");
		font.setFontHeightInPoints((short) 11);
		style.setFont(font);
		return style;
	}
	/**
 	 * <p>方法描述：设置表格边框线</p>
 	 * @MethodAuthor qrr,2020年5月27日,setBorderStyle
	 * */
	public static XSSFCellStyle setBorderStyle(XSSFWorkbook wBook) {
		XSSFCellStyle borderStyle = wBook.createCellStyle();
		borderStyle.setBorderBottom((short) 1);
		borderStyle.setBorderLeft((short) 1);
		borderStyle.setBorderRight((short) 1);
		borderStyle.setBorderTop((short) 1);
		return borderStyle;
	}
	/**
 	 * <p>方法描述：设置查询条件样式</p>
 	 * @MethodAuthor qrr,2020年5月27日,setBorderStyle
	 * */
	public static XSSFCellStyle setSearchConditionStyle(XSSFWorkbook wBook) {
		XSSFCellStyle style = wBook.createCellStyle();
		style.setAlignment(XSSFCellStyle.ALIGN_LEFT);
		style.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
        style.setWrapText(true);
        Font font = wBook.createFont();
		font.setBoldweight(Font.BOLDWEIGHT_BOLD);
		font.setFontHeightInPoints((short)20);
		font.setFontName("宋体");
		font.setFontHeightInPoints((short) 11);
		style.setFont(font);
		return style;
	}
	/**
 	 * <p>方法描述：设置边框线</p>
 	 * @MethodAuthor qrr,2020年5月27日,setBorderLine
	 * */
	public static void setBorderLine(XSSFWorkbook wBook,XSSFSheet sheet,Integer totalRows,Integer toatlCells) {
		XSSFCellStyle borderStyle = ExportUtils.setBorderStyle(wBook);
		for (int i = 0; i < totalRows; i++) {
			XSSFRow row = sheet.getRow(i);
			if (i==0) {// 第一行标题，设置行高30
				row.setHeightInPoints(30);
			}else if (i!=1) {// 第二行查询条件，无需设置行高
				row.setHeightInPoints(20);
			}
			for (int j = 0; j < toatlCells; j++) {
				XSSFCell cell = row.getCell(j);
				if (null==cell) {
					cell = row.createCell(j);
					cell.setCellStyle(borderStyle);
				}else {
					XSSFCellStyle cellStyle = cell.getCellStyle();
					cellStyle.setBorderBottom((short) 1);
					cellStyle.setBorderLeft((short) 1);
					cellStyle.setBorderRight((short) 1);
					cellStyle.setBorderTop((short) 1);
				}
			}
		}
	}
}
