package com.chis.common.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.apache.commons.fileupload.FileItem;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.CompleteMultipartUploadRequest;
import com.aliyun.oss.model.InitiateMultipartUploadRequest;
import com.aliyun.oss.model.InitiateMultipartUploadResult;
import com.aliyun.oss.model.PartETag;

public class MultipartUpload {

	private static OSSClient client = null;
	private static ExecutorService executorService;
	private static List<PartETag> partETags;
	
	
	public static void upload(String key, String bucketName, FileItem file, OSSClient ossClient) throws Exception{
		try {
			executorService = Executors.newFixedThreadPool(5);
			partETags = Collections .synchronizedList(new ArrayList<PartETag>());
			client = ossClient;
			String uploadId = claimUploadId(bucketName, key);
			final long partSize = 5 * 1024 * 1024L; // 5MB
			long fileLength = file.getInputStream().available();
			int partCount = (int) (fileLength / partSize);
			if (fileLength % partSize != 0) {
				partCount++;
			}
			if (partCount > 10000) {
				throw new RuntimeException(
						"Total parts count should not exceed 10000");
			} else {
				System.out.println("Total parts count " + partCount + "\n");
			}
			
			for (int i = 0; i < partCount; i++) {
				
				long startPos = i * partSize;
				long curPartSize = (i + 1 == partCount) ? (fileLength - startPos) : partSize;
				executorService.execute(new PartUploader(file, startPos,
						curPartSize, i + 1, uploadId, bucketName, key, client, partETags));
			}
	
			executorService.shutdown();
			while (!executorService.isTerminated()) {
				try {
					executorService.awaitTermination(5, TimeUnit.SECONDS);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
	
			if (partETags.size() != partCount) {
				throw new IllegalStateException(
						"Upload multiparts fail due to some parts are not finished yet");
			}
	
			// 完成上传
			completeMultipartUpload(uploadId, bucketName, key);
		} catch (Exception e) {
			throw e;
        }
	}

	private static String claimUploadId(String bucketName, String key) {
		InitiateMultipartUploadRequest request = new InitiateMultipartUploadRequest(
				bucketName, key);
		InitiateMultipartUploadResult result = client
				.initiateMultipartUpload(request);
		
		return result.getUploadId();
	}

	private static void completeMultipartUpload(String uploadId, String bucketName, String key) {
		Collections.sort(partETags, new Comparator<PartETag>() {
			@Override
			public int compare(PartETag p1, PartETag p2) {
				return p1.getPartNumber() - p2.getPartNumber();
			}
		});

		CompleteMultipartUploadRequest completeMultipartUploadRequest = new CompleteMultipartUploadRequest(
				bucketName, key, uploadId, partETags);
		client.completeMultipartUpload(completeMultipartUploadRequest);
	}
}