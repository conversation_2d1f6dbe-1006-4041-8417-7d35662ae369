package com.chis.common.utils;

import java.lang.reflect.Field;
import java.sql.Clob;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import com.chis.common.bean.FastReportData;
import com.chis.common.bean.FastReportDataRef;
import com.chis.common.bean.ReportOptType;
import com.chis.common.bean.Rich;

/**
 * FastReport报表的工具类
 * <AUTHOR> 2015-2-2
 */
public class FastReportUtil {
	
	/**
	 * 将FastReport数据集转xml
	 * @param rptDataList 数据集
	 * @param defList 关系数据集
	 * @param reportOptType 操作类型
	 * @param filePath 导出PDF路径
	 * @param copies 打印份数
	 * @param pageNumbers 打印的页码 单页：单页打印
	 * @return xml压缩后的字符串
	 */
	public static String generateXml(List<FastReportData> rptDataList, List<FastReportDataRef> defList,
			ReportOptType reportOptType, String filePath, String copies, String pageNumbers) {
		try {
			// 建立document对象
			Document document = DocumentHelper.createDocument();
			// XML根节点
			Element reportElement = document.addElement("report-config");
			// 打印设置toXml
			reportElement.addAttribute("optype", reportOptType.toString());
			reportElement.addAttribute("filepath", filePath);
			reportElement.addAttribute("copies", copies);
			reportElement.addAttribute("pageNumbers", pageNumbers);
			
			// 数据集内容toXml
			Element datapacketElement = reportElement.addElement("datapacket");
			if(null != rptDataList && rptDataList.size() > 0) {
				for(FastReportData frd : rptDataList) {
					Element dataElement = datapacketElement.addElement("data");
					dataElement.addAttribute("datename", frd.getDataname());

					Element fieldsElement = dataElement.addElement("fields");
					
					if (frd.getClazz().equals(Map.class)) {
						Element rowdataElement = null;
						List<Map<String, Object>> dataList = frd.getList();
						if(null != dataList && dataList.size()>0){
							Map<String, Object> fieldMap = dataList.get(0);
							for (Entry<String, Object> entry : fieldMap.entrySet()) {
								Element fieldElement = fieldsElement.addElement("field");
								fieldElement.addAttribute("attrname", entry.getKey());
//								fieldElement.addAttribute("fieldtype", field.getType().getSimpleName());
							}
							for (Map<String, Object> map : dataList) {
								if (null == rowdataElement) {
									rowdataElement = dataElement.addElement("rowdata");
								}
								Element rowElement = rowdataElement.addElement("row");
								for (Entry<String, Object> entry : map.entrySet()) {
									String name = entry.getKey();
									Object value = entry.getValue();
									if (null != value && value instanceof Clob) {
										Clob clob = (Clob) value;
										rowElement.addAttribute(name, ClobTransfer.clobToString(clob));
									} else if (null != value && value instanceof Date) {
										Date date = (Date) value;
										rowElement.addAttribute(name, DateUtils.formatDateTime(date));
									} else if (null != value && value instanceof Rich) {
										Rich rich = (Rich) value;
										rowElement.addAttribute(name, rich.getText());
									} else {
										rowElement.addAttribute(name, StringUtils.objectToString(value));
									}
								}
							}
						}
					}else {
						Field[] fields = frd.getClazz().getDeclaredFields();
						if (null != fields && fields.length > 0) {
							for (Field field : fields) {
								field.setAccessible(true);
								if (!field.getName().toUpperCase().equals("serialVersionUID".toUpperCase())) {
									Element fieldElement = fieldsElement.addElement("field");
									fieldElement.addAttribute("attrname", field.getName());
									System.out.println(field.getName()+"========"+field.getType().getSimpleName());
									fieldElement.addAttribute("fieldtype", field.getType().getSimpleName());
								}
							}
						}
						
						Element rowdataElement = null;
						List dataList = frd.getList();
						if(null != dataList && dataList.size()>0){
							for (Object obj : dataList) {
								if (null == rowdataElement) {
									rowdataElement = dataElement.addElement("rowdata");
								}
								Element rowElement = rowdataElement.addElement("row");
								for (Field field : fields) {
									if (!field.getName().toUpperCase().equals("serialVersionUID".toUpperCase())) {
										Object value = Reflections.invokeGetter(field.getName(), obj);
										if (null != value && value instanceof Clob) {
											Clob clob = (Clob) value;
											rowElement.addAttribute(field.getName(), ClobTransfer.clobToString(clob));
										} else if (null != value && value instanceof Date) {
											Date date = (Date) value;
											rowElement.addAttribute(field.getName(), DateUtils.formatDateTime(date));
										} else if (null != value && value instanceof Rich) {
											Rich rich = (Rich) value;
											rowElement.addAttribute(field.getName(), rich.getText());
										} else {
											System.out.println(field.getName()+"##########"+StringUtils.objectToString(value));
											rowElement.addAttribute(field.getName(), StringUtils.objectToString(value));
										}
									}
								}
							}
						}
						
					}
                }
			}
			
			
			// 数据关系toXml
            Element datarefsElement = reportElement.addElement("datarefs");
            if(null != defList && defList.size() > 0) {
                for(FastReportDataRef ref : defList) {
                    Element datarefElement = datarefsElement.addElement("dataref");
                    datarefElement.addAttribute("masterdatasetname", ref.getMasterdatasetname());
                    datarefElement.addAttribute("masteridfieldname", ref.getMasteridfieldname());
                    datarefElement.addAttribute("subdatasetname", ref.getSubdatasetname());
                    datarefElement.addAttribute("subidfieldname", ref.getSubidfieldname());
                    datarefElement.addAttribute("masterorderstrs", ref.getMasterorderstrs());
                    datarefElement.addAttribute("masterdescorderstrs", ref.getMasterdescorderstrs());
                    datarefElement.addAttribute("suborderstrs", ref.getSuborderstrs());
                    datarefElement.addAttribute("subdescorderstrs", ref.getSubdescorderstrs());
                }
            }

			// 压缩xml字符串
			String rtnStr = ZipUtils.gzip(document.asXML());
            // xml字符串去除回车
			if (StringUtils.isNotBlank(rtnStr)) {
				rtnStr = rtnStr.replaceAll("\r", "");
                rtnStr = rtnStr.replaceAll("\n", "");
			}
			return rtnStr;
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}
}

