package com.chis.common.entity;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;

/**
 * EXCEL 导出数据
 *
 * <AUTHOR>
 * @date 2022/1/27
 */
public class ExcelExportObject {
    /**
     * 数据值
     */
    private Object object;

    /**
     * 单元格样式-水平
     */
    private short alignStyleType;

    /**
     * 单元格样式-垂直
     */
    private short verticalStyleType;

    /**
     * 单元格样式-自动换行
     */
    private boolean wrapText;

    /**
     * 内容是否加粗
     */
    private boolean boldWeightBold;
    /**
     * 设置单元格的数据格式
     * <pre>文本格式</pre>
     * <pre> 0：常规文本格式</pre>
     * <pre> 49：文本格式</pre>
     * <pre>数字格式：</pre>
     * <pre> 1：常规数字格式（通常带有千位分隔符和两位小数）</pre>
     * <pre> 2：货币格式</pre>
     * <pre> 3：百分比格式</pre>
     * <pre> 4：小数格式（两位小数）</pre>
     * <pre> 5：分数格式</pre>
     * <pre>科学记数法</pre>
     * <pre> 11：科学记数法格式（如 "1.23E+05"）</pre>
     * <pre>日期/时间格式：</pre>
     * <pre> 14：日期格式（如 "yyyy-MM-dd"）</pre>
     * <pre> 15：日期时间格式（如 "yyyy-MM-dd HH:mm:ss"）</pre>
     * <pre> 16：时间格式（如 "HH:mm:ss"）</pre>
     */
    private short dataFormat = 0;

    /**
     * 初始化EXCEL导出数据
     * <p>数据值(默认空字符串)
     * <p>单元格样式(默认水平居左垂直分散对齐不自动换行)
     */
    public ExcelExportObject() {
        this.object = "";
        this.alignStyleType = XSSFCellStyle.ALIGN_LEFT;
        this.verticalStyleType = (short) 4;
        this.wrapText = false;
    }

    /**
     * 初始化EXCEL导出数据
     * <p>单元格样式默认水平居左垂直分散对齐不自动换行
     *
     * @param object {@link #object 数据值}
     */
    public ExcelExportObject(Object object) {
        this.object = object;
        this.alignStyleType = XSSFCellStyle.ALIGN_LEFT;
        this.verticalStyleType = (short) 4;
        this.wrapText = false;
    }

    /**
     * 初始化EXCEL导出数据
     * <p>单元格样式默认水平居左垂直分散对齐
     *
     * @param object            {@link #object 数据值}
     * @param wrapText          {@link #wrapText 单元格样式-自动换行}
     */
    public ExcelExportObject(Object object, boolean wrapText) {
        this.object = object;
        this.alignStyleType = XSSFCellStyle.ALIGN_LEFT;
        this.verticalStyleType = (short) 4;
        this.wrapText = wrapText;
    }

    /**
     * 初始化EXCEL导出数据
     * <p>单元格样式默认垂直分散对齐不自动换行
     *
     * @param object         {@link #object 数据值}
     * @param alignStyleType {@link #alignStyleType 单元格水平样式}
     */
    public ExcelExportObject(Object object, short alignStyleType) {
        this.object = object;
        this.alignStyleType = alignStyleType;
        this.verticalStyleType = (short) 4;
        this.wrapText = false;
    }

    /**
     * 初始化EXCEL导出数据
     * <p>单元格样式默认垂直分散对齐不自动换行
     *
     * @param object         {@link #object 数据值}
     * @param alignStyleType {@link #alignStyleType 单元格水平样式}
     * @param boldWeightBold {@link #boldWeightBold 内容是否加粗}
     */
    public ExcelExportObject(Object object, short alignStyleType,boolean boldWeightBold) {
        this.object = object;
        this.alignStyleType = alignStyleType;
        this.verticalStyleType = (short) 4;
        this.wrapText = false;
        this.boldWeightBold=boldWeightBold;
    }


    /**
     * 初始化EXCEL导出数据
     * <p>单元格样式默认不自动换行
     *
     * @param object            {@link #object 数据值}
     * @param alignStyleType    {@link #alignStyleType 单元格水平样式}
     * @param verticalStyleType {@link #verticalStyleType 单元格垂直样式}
     */
    public ExcelExportObject(Object object, short alignStyleType, short verticalStyleType) {
        this.object = object;
        this.alignStyleType = alignStyleType;
        this.verticalStyleType = verticalStyleType;
        this.wrapText = false;
    }

    /**
     * 初始化EXCEL导出数据
     *
     * @param object            {@link #object 数据值}
     * @param alignStyleType    {@link #alignStyleType 单元格样式-水平}
     * @param verticalStyleType {@link #verticalStyleType 单元格样式-垂直}
     * @param wrapText          {@link #wrapText 单元格样式-自动换行}
     */
    public ExcelExportObject(Object object, short alignStyleType, short verticalStyleType, boolean wrapText) {
        this.object = object;
        this.alignStyleType = alignStyleType;
        this.verticalStyleType = verticalStyleType;
        this.wrapText = wrapText;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public short getAlignStyleType() {
        return alignStyleType;
    }

    public void setAlignStyleType(short alignStyleType) {
        this.alignStyleType = alignStyleType;
    }

    public short getVerticalStyleType() {
        return verticalStyleType;
    }

    public void setVerticalStyleType(short verticalStyleType) {
        this.verticalStyleType = verticalStyleType;
    }

    public boolean isWrapText() {
        return wrapText;
    }

    public void setWrapText(boolean wrapText) {
        this.wrapText = wrapText;
    }

    public boolean isBoldWeightBold() {
        return boldWeightBold;
    }

    public void setBoldWeightBold(boolean boldWeightBold) {
        this.boldWeightBold = boldWeightBold;
    }

    public short getDataFormat() {
        return dataFormat;
    }

    public void setDataFormat(short dataFormat) {
        this.dataFormat = dataFormat;
    }
}
