package com.chis.common.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 输出内容 
 * <AUTHOR>
 *
 */
public class OutputFile {

	/** 输出object */
	@JSONField(name = "OutputObject") 
	private String outputObject;
	/** 转码模板ID */
	@JSONField(name = "TemplateId") 
	private String templateId;
	/** 水印 */
	@JSONField(name = "WaterMarks") 
	private WaterMarksBean waterMarks;
	@JSONField(name = "UserData") 
	private String tserData;

	public OutputFile(String outputObject, String templateId,
			WaterMarksBean waterMarks) {
		super();
		this.outputObject = outputObject;
		this.templateId = templateId;
		this.waterMarks = waterMarks;
	}

	public String getOutputObject() {
		return outputObject;
	}

	public void setOutputObject(String outputObject) {
		this.outputObject = outputObject;
	}

	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}

	public WaterMarksBean getWaterMarks() {
		return waterMarks;
	}

	public void setWaterMarks(WaterMarksBean waterMarks) {
		this.waterMarks = waterMarks;
	}

	public String getTserData() {
		return tserData;
	}

	public void setTserData(String tserData) {
		this.tserData = tserData;
	}

	
}
