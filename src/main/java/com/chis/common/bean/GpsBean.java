package com.chis.common.bean;

/**
 * 经纬度坐标实体
 * 
 * <AUTHOR>
 * 
 */
public class GpsBean {
	private double wgLat;
	private double wgLon;

	public GpsBean(double wgLat, double wgLon) {
		setWgLat(wgLat);
		setWgLon(wgLon);
	}

	public double getWgLat() {
		return wgLat;
	}

	public void setWgLat(double wgLat) {
		this.wgLat = wgLat;
	}

	public double getWgLon() {
		return wgLon;
	}

	public void setWgLon(double wgLon) {
		this.wgLon = wgLon;
	}

	@Override
	public String toString() {
		return wgLat + "," + wgLon;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		long temp;
		temp = Double.doubleToLongBits(wgLat);
		result = prime * result + (int) (temp ^ (temp >>> 32));
		temp = Double.doubleToLongBits(wgLon);
		result = prime * result + (int) (temp ^ (temp >>> 32));
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		GpsBean other = (GpsBean) obj;
		if (Double.doubleToLongBits(wgLat) != Double
				.doubleToLongBits(other.wgLat))
			return false;
		if (Double.doubleToLongBits(wgLon) != Double
				.doubleToLongBits(other.wgLon))
			return false;
		return true;
	}
	
	
}
