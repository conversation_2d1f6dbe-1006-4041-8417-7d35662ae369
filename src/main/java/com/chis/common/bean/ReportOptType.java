package com.chis.common.bean;

/**
 * FastReport报表操作类型
 * <AUTHOR>
 */
public enum ReportOptType {

	DESIGN((short)0) {
        public String getTypeCN() { return "设计";}
	},

	VIEW((short)1) {
        public String getTypeCN() { return "预览";}
	},

    PRINT((short)2) {
        public String getTypeCN() { return "打印";}
    },

    EXPORT((short)3) {
        public String getTypeCN() { return "PDF导出";}
    }
    
    ;

	private final Short typeNo;

	ReportOptType(Short typeNo) {
		this.typeNo = typeNo;
	} 
	
	public String toString() {return String.valueOf(typeNo);}
	
	
	public Short getTypeNo() {
		return typeNo;
	}

    public abstract String getTypeCN();

}
