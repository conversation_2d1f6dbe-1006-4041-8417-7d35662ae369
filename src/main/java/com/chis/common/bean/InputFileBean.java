package com.chis.common.bean;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * sunm
 * <AUTHOR>
 */
public class InputFileBean {
	@J<PERSON><PERSON>ield(name = "Location") 
	private String location;
	@<PERSON><PERSON><PERSON>ield(name = "Bucket") 
	private String bucket;
	@J<PERSON>NField(name = "Object") 
	private String object;
	public InputFileBean(){}
	public InputFileBean(String location, String bucket, String object) {
		this.location = location;
		this.bucket = bucket;
		this.object = object;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getBucket() {
		return bucket;
	}

	public void setBucket(String bucket) {
		this.bucket = bucket;
	}

	public String getObject() {
		return object;
	}

	public void setObject(String object) {
		this.object = object;
	}
}
