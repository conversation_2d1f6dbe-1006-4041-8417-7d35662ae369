package com.chis.common.bean;

import java.io.Serializable;
import java.util.List;

/**
 * FastReport数据集对象
 * <AUTHOR> 2015-01-30
 */
public class FastReportData<T> implements Serializable{

	private static final long serialVersionUID = 1498772963167617146L;
	private String dataname;
    private Class<T> clazz;
	private List<T> list;
	
	public FastReportData() {
	}

	public FastReportData(Class<T> clazz,String dataname, List<T> list) {
		this.dataname = dataname;
        this.clazz = clazz;
		this.list = list;
	}

	public List<T> getList() {
		return list;
	}

	public void setList(List<T> list) {
		this.list = list;
	}

	public String getDataname() {
		return dataname;
	}

	public void setDataname(String dataname) {
		this.dataname = dataname;
	}

    public Class<T> getClazz() {
        return clazz;
    }

    public void setClazz(Class<T> clazz) {
        this.clazz = clazz;
    }
}
