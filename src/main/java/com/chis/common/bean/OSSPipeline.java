package com.chis.common.bean;

/**
 * 管道基础bean
 * <AUTHOR>
 */
public class OSSPipeline {
	/** 管道ID 必填项 */
	private String pipelineId;
	/** 管道名称 必填项 最大长度128字节 */
	private String name;
	/**管道状态 必填写 分为Active、Paused
	 * Active:表示管道内的作业会被媒体转码服务调度转码执行；
	 * Paused:表示管道暂停，作业不再会被媒体转码调度转码执行，管道内的所有作业状态维持在已提交状态，已经处于转码中的任务将继续转码，不受影响。
	 * */
	private String state;
	/** MNS配置，例：{"Topic":"mts-topic-1"} */
	private String notifyConfig;
	
	private String role;

	public OSSPipeline(String pipelineId, String name, String state) {
		super();
		this.pipelineId = pipelineId;
		this.name = name;
		this.state = state;
	}

	public String getPipelineId() {
		return pipelineId;
	}

	public void setPipelineId(String pipelineId) {
		this.pipelineId = pipelineId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getNotifyConfig() {
		return notifyConfig;
	}

	public void setNotifyConfig(String notifyConfig) {
		this.notifyConfig = notifyConfig;
	}

	public String getRole() {
		return role;
	}

	public void setRole(String role) {
		this.role = role;
	}
}
