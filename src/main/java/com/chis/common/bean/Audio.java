package com.chis.common.bean;

/**
 * 音频流
 * <AUTHOR>
 */
public class Audio {
	/** 编码格式 AAC、MP3、VORBIS、 FLAC*/
	private String Codec;
	/** 采样率(Hz) */
	private String Samplerate;
	/** 输出码率(Kbps) (8-1000)*/
	private String Bitrate;
	/** 声道数 (1-8)*/
	private String Channels;

	public Audio(String codec, String samplerate, String bitrate,
			String channels) {
		this.Codec = codec;
		this.Samplerate = samplerate;
		this.Bitrate = bitrate;
		this.Channels = channels;
	}

	public String getCodec() {
		return Codec;
	}

	public void setCodec(String codec) {
		Codec = codec;
	}

	public String getSamplerate() {
		return Samplerate;
	}

	public void setSamplerate(String samplerate) {
		Samplerate = samplerate;
	}

	public String getBitrate() {
		return Bitrate;
	}

	public void setBitrate(String bitrate) {
		Bitrate = bitrate;
	}

	public String getChannels() {
		return Channels;
	}

	public void setChannels(String channels) {
		Channels = channels;
	}
	
}
