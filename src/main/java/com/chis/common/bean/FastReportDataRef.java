package com.chis.common.bean;

import java.io.Serializable;

/**
 * FastReport数据集关系对象
 * <AUTHOR> 2015-01-30
 */
public class FastReportDataRef implements Serializable{

	private static final long serialVersionUID = 6308190530687229534L;
	
	private String masterdatasetname;
	private String masteridfieldname;
	private String subdatasetname;
	private String subidfieldname;
	private String masterorderstrs;
	private String masterdescorderstrs;
	private String suborderstrs;
	private String subdescorderstrs;
	
	public FastReportDataRef() {
	}

	public String getMasterdatasetname() {
		return masterdatasetname;
	}

	public void setMasterdatasetname(String masterdatasetname) {
		this.masterdatasetname = masterdatasetname;
	}

	public String getMasteridfieldname() {
		return masteridfieldname;
	}

	public void setMasteridfieldname(String masteridfieldname) {
		this.masteridfieldname = masteridfieldname;
	}

	public String getSubdatasetname() {
		return subdatasetname;
	}

	public void setSubdatasetname(String subdatasetname) {
		this.subdatasetname = subdatasetname;
	}

	public String getSubidfieldname() {
		return subidfieldname;
	}

	public void setSubidfieldname(String subidfieldname) {
		this.subidfieldname = subidfieldname;
	}

	public String getMasterorderstrs() {
		return masterorderstrs;
	}

	public void setMasterorderstrs(String masterorderstrs) {
		this.masterorderstrs = masterorderstrs;
	}

	public String getMasterdescorderstrs() {
		return masterdescorderstrs;
	}

	public void setMasterdescorderstrs(String masterdescorderstrs) {
		this.masterdescorderstrs = masterdescorderstrs;
	}

	public String getSuborderstrs() {
		return suborderstrs;
	}

	public void setSuborderstrs(String suborderstrs) {
		this.suborderstrs = suborderstrs;
	}

	public String getSubdescorderstrs() {
		return subdescorderstrs;
	}

	public void setSubdescorderstrs(String subdescorderstrs) {
		this.subdescorderstrs = subdescorderstrs;
	}

}
