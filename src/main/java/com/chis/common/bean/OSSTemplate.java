package com.chis.common.bean;

/**
 * 转码模板参数
 * 
 * <AUTHOR>
 */
public class OSSTemplate {
	/** 模板ID */
	private String templateId;
	/** 模板名称 */
	private String name;
	/** json 对象 */
	private String container;
	/** 视频流配置 */
	private OssVideo video;
	/** 音频配置 */
	private Audio audio;
	/** 转码通用配置 JSON对象 */
	private String transConfig;
	/** 封包配置 JSON对象 */
	private String muxConfig;
	
	public OSSTemplate(String name, String container) {
		this.name = name;
		this.container = container;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getContainer() {
		return container;
	}

	public void setContainer(String container) {
		this.container = container;
	}

	public OssVideo getVideo() {
		return video;
	}

	public void setVideo(OssVideo video) {
		this.video = video;
	}

	public Audio getAudio() {
		return audio;
	}

	public void setAudio(Audio audio) {
		this.audio = audio;
	}

	public String getTransConfig() {
		return transConfig;
	}

	public void setTransConfig(String transConfig) {
		this.transConfig = transConfig;
	}

	public String getMuxConfig() {
		return muxConfig;
	}

	public void setMuxConfig(String muxConfig) {
		this.muxConfig = muxConfig;
	}

	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}

}
