package com.chis.common.bean;

/** 视频流 */
public class OssVideo {
		
	/** 编码格式 H.264/H.265*/
	private String Codec;
	/** 编码级别  high/main/baseline*/
	private String Profile;
	/** 码率(Kbps) 范围（10-500000）*/
	private String Bitrate;
	/** 质量控制因子 （0-51）*/
	private String Crf;
	/** 视频宽度(px) （128-4096）*/
	private String Width;
	/** 视频高度(px) （128-4096）*/
	private String Height;
	/** 帧率(fps) （0-60）*/
	private String Fps;
	/**　关键帧间最大帧数（1-100000） */
	private String Gop;

	
	public OssVideo(String codec, String profile, String bitrate, String crf,
			String width, String height, String fps, String gop) {
		Codec = codec;
		Profile = profile;
		Bitrate = bitrate;
		Crf = crf;
		Width = width;
		Height = height;
		Fps = fps;
		Gop = gop;
	}

	public String getCodec() {
		return Codec;
	}

	public void setCodec(String codec) {
		Codec = codec;
	}

	public String getProfile() {
		return Profile;
	}

	public void setProfile(String profile) {
		Profile = profile;
	}

	public String getBitrate() {
		return Bitrate;
	}

	public void setBitrate(String bitrate) {
		Bitrate = bitrate;
	}

	public String getCrf() {
		return Crf;
	}

	public void setCrf(String crf) {
		Crf = crf;
	}

	public String getWidth() {
		return Width;
	}

	public void setWidth(String width) {
		Width = width;
	}

	public String getHeight() {
		return Height;
	}

	public void setHeight(String height) {
		Height = height;
	}

	public String getFps() {
		return Fps;
	}

	public void setFps(String fps) {
		Fps = fps;
	}

	public String getGop() {
		return Gop;
	}

	public void setGop(String gop) {
		Gop = gop;
	}
	
}
