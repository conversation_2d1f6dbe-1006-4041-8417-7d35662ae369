package com.chis.common.bean;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 水印
 * 
 * <AUTHOR>
 */
public class WaterMarksBean {
	@JSONField(name="InputFile")
	private InputFileBean inputFile;
	/** 水印模板ID */
	@JSONField(name="WaterMarkTemplateId")
	private String waterMarkTemplateId;
	
	public WaterMarksBean(InputFileBean inputFile, String waterMarkTemplateId) {
		this.inputFile = inputFile;
		this.waterMarkTemplateId = waterMarkTemplateId;
	}

	public InputFileBean getInputFile() {
		return inputFile;
	}

	public void setInputFile(InputFileBean inputFile) {
		this.inputFile = inputFile;
	}

	public String getWaterMarkTemplateId() {
		return waterMarkTemplateId;
	}

	public void setWaterMarkTemplateId(String waterMarkTemplateId) {
		this.waterMarkTemplateId = waterMarkTemplateId;
	}

	
}
