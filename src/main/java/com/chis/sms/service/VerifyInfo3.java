/**
 * VerifyInfo3.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.chis.sms.service;

public class VerifyInfo3  extends com.chis.sms.service.VerifyInfo2  implements java.io.Serializable {
    private java.lang.String sendSms;

    public VerifyInfo3() {
    }

    public VerifyInfo3(
           int agreeCount,
           java.lang.String allowBeginTime,
           java.lang.String allowEndTime,
           java.lang.String canLogin,
           java.lang.String doctorMobile,
           java.lang.String exContent,
           int leftCount,
           java.lang.String linkTel,
           java.lang.String netConfigs,
           int netTag,
           java.lang.String noteEnd,
           int packageSize,
           int specialTag,
           int tagInfo,
           java.lang.String unitCode,
           int useCount,
           java.lang.String useSpField,
           java.lang.String wakeInfo,
           java.lang.String allowNets,
           java.lang.String sendSms) {
        super(
            agreeCount,
            allowBeginTime,
            allowEndTime,
            canLogin,
            doctorMobile,
            exContent,
            leftCount,
            linkTel,
            netConfigs,
            netTag,
            noteEnd,
            packageSize,
            specialTag,
            tagInfo,
            unitCode,
            useCount,
            useSpField,
            wakeInfo,
            allowNets);
        this.sendSms = sendSms;
    }


    /**
     * Gets the sendSms value for this VerifyInfo3.
     * 
     * @return sendSms
     */
    public java.lang.String getSendSms() {
        return sendSms;
    }


    /**
     * Sets the sendSms value for this VerifyInfo3.
     * 
     * @param sendSms
     */
    public void setSendSms(java.lang.String sendSms) {
        this.sendSms = sendSms;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof VerifyInfo3)) return false;
        VerifyInfo3 other = (VerifyInfo3) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.sendSms==null && other.getSendSms()==null) || 
             (this.sendSms!=null &&
              this.sendSms.equals(other.getSendSms())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getSendSms() != null) {
            _hashCode += getSendSms().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(VerifyInfo3.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://service.sms.chis.com/", "verifyInfo3"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sendSms");
        elemField.setXmlName(new javax.xml.namespace.QName("", "sendSms"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
