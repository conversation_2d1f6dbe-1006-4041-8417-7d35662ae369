/**
 * SmsServiceLocator.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.chis.sms.service;

public class SmsServiceLocator extends org.apache.axis.client.Service implements com.chis.sms.service.SmsService {

    public SmsServiceLocator() {
    }


    public SmsServiceLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public SmsServiceLocator(java.lang.String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for SmsServerPort
    private java.lang.String SmsServerPort_address = "http://***********:7002/SmsService/SmsService";

    public java.lang.String getSmsServerPortAddress() {
        return SmsServerPort_address;
    }

    // The WSDD service name defaults to the port name.
    private java.lang.String SmsServerPortWSDDServiceName = "SmsServerPort";

    public java.lang.String getSmsServerPortWSDDServiceName() {
        return SmsServerPortWSDDServiceName;
    }

    public void setSmsServerPortWSDDServiceName(java.lang.String name) {
        SmsServerPortWSDDServiceName = name;
    }

    public com.chis.sms.service.SmsServer getSmsServerPort() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(SmsServerPort_address);
        }
        catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getSmsServerPort(endpoint);
    }

    public com.chis.sms.service.SmsServer getSmsServerPort(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            com.chis.sms.service.SmsServerPortBindingStub _stub = new com.chis.sms.service.SmsServerPortBindingStub(portAddress, this);
            _stub.setPortName(getSmsServerPortWSDDServiceName());
            return _stub;
        }
        catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setSmsServerPortEndpointAddress(java.lang.String address) {
        SmsServerPort_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (com.chis.sms.service.SmsServer.class.isAssignableFrom(serviceEndpointInterface)) {
                com.chis.sms.service.SmsServerPortBindingStub _stub = new com.chis.sms.service.SmsServerPortBindingStub(new java.net.URL(SmsServerPort_address), this);
                _stub.setPortName(getSmsServerPortWSDDServiceName());
                return _stub;
            }
        }
        catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        java.lang.String inputPortName = portName.getLocalPart();
        if ("SmsServerPort".equals(inputPortName)) {
            return getSmsServerPort();
        }
        else  {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://service.sms.chis.com/", "SmsService");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://service.sms.chis.com/", "SmsServerPort"));
        }
        return ports.iterator();
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(java.lang.String portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        
if ("SmsServerPort".equals(portName)) {
            setSmsServerPortEndpointAddress(address);
        }
        else 
{ // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
    * Set the endpoint address for the specified port name.
    */
    public void setEndpointAddress(javax.xml.namespace.QName portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
