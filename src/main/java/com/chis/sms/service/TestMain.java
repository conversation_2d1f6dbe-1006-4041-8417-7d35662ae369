package com.chis.sms.service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.GZIPOutputStream;

import sun.misc.BASE64Encoder;

public class TestMain {

	public static void main(String[] args) {
		System.err.println("-----1---");
		SmsServerProxy proxy = new SmsServerProxy();
		
		try {
			StringBuilder sb = new StringBuilder();
			sb.append("<?xml version='1.0' encoding='GB2312'?>");
			sb.append("<SmsInfoes>");
			sb.append("<SmsInfo>");
			sb.append("<UserCode>A1234</UserCode>");
			sb.append("<TelNum>13921137249</TelNum>");
			sb.append("<Content>fffff </Content>");
			sb.append("<Count>2</Count>");
			sb.append("<InvalidDate>2012-3-2</InvalidDate>");
			sb.append("<SpTag>0</SpTag>");
			sb.append("<NetTag>1</NetTag>");
			sb.append("</SmsInfo>");
			sb.append("</SmsInfoes>");
			
			System.err.println("-----2---");
			int rst = proxy.smsUpLoad(compress(sb.toString()), "cybercdc", "670b14728ad9902aecba32e22fa4f6bd");
			System.err.println("rst:" + rst);
			System.err.println("-----3---");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private static String compress(String s) {
		try {
			ByteArrayInputStream input = new ByteArrayInputStream(s.getBytes("GBK"));
			ByteArrayOutputStream output = new ByteArrayOutputStream(1024);
			GZIPOutputStream gzout = new GZIPOutputStream(output);
			
			byte[] buf = new byte[1024];
			int number;
			
			while ((number = input.read(buf)) != -1) {
				gzout.write(buf, 0, number);
			}
			
			gzout.close();
			input.close();
			
			String result = new BASE64Encoder().encode(output.toByteArray());
			
			output.close();
			
			return result;
		} catch (IOException e) {
			
		}
		return null;
	}
}


/**
<?xml version="1.0" encoding="GB2312"?>
<SmsInfoes>
<SmsInfo>
	<UserCode>A1234</UserCode>
	<TelNum>13921137249</TelNum>
	    <Content>fffff </Content>
	    <Count>2</Count>
	    <InvalidDate>2012-3-2</InvalidDate>
	    <SpTag>1</SpTag>
	    <NetTag>1</NetTag>
</SmsInfo>
  <SmsInfo>
	<UserCode>A1234</UserCode>
	<TelNum>13921137249</TelNum>
	    <Content>fffff </Content>
	    <Count>2</Count>
	    <InvalidDate>2012-3-2</InvalidDate>
	    <SpTag>1</SpTag>
	    <NetTag>1</NetTag>
</SmsInfo>
</SmsInfoes>

*/