/**
 * VerifyInfo.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.chis.sms.service;

public class VerifyInfo  implements java.io.Serializable {
    private int agreeCount;

    private java.lang.String allowBeginTime;

    private java.lang.String allowEndTime;

    private java.lang.String canLogin;

    private java.lang.String doctorMobile;

    private java.lang.String exContent;

    private int leftCount;

    private java.lang.String linkTel;

    private java.lang.String netConfigs;

    private int netTag;

    private java.lang.String noteEnd;

    private int packageSize;

    private int specialTag;

    private int tagInfo;

    private java.lang.String unitCode;

    private int useCount;

    private java.lang.String useSpField;

    private java.lang.String wakeInfo;

    public VerifyInfo() {
    }

    public VerifyInfo(
           int agreeCount,
           java.lang.String allowBeginTime,
           java.lang.String allowEndTime,
           java.lang.String canLogin,
           java.lang.String doctorMobile,
           java.lang.String exContent,
           int leftCount,
           java.lang.String linkTel,
           java.lang.String netConfigs,
           int netTag,
           java.lang.String noteEnd,
           int packageSize,
           int specialTag,
           int tagInfo,
           java.lang.String unitCode,
           int useCount,
           java.lang.String useSpField,
           java.lang.String wakeInfo) {
           this.agreeCount = agreeCount;
           this.allowBeginTime = allowBeginTime;
           this.allowEndTime = allowEndTime;
           this.canLogin = canLogin;
           this.doctorMobile = doctorMobile;
           this.exContent = exContent;
           this.leftCount = leftCount;
           this.linkTel = linkTel;
           this.netConfigs = netConfigs;
           this.netTag = netTag;
           this.noteEnd = noteEnd;
           this.packageSize = packageSize;
           this.specialTag = specialTag;
           this.tagInfo = tagInfo;
           this.unitCode = unitCode;
           this.useCount = useCount;
           this.useSpField = useSpField;
           this.wakeInfo = wakeInfo;
    }


    /**
     * Gets the agreeCount value for this VerifyInfo.
     * 
     * @return agreeCount
     */
    public int getAgreeCount() {
        return agreeCount;
    }


    /**
     * Sets the agreeCount value for this VerifyInfo.
     * 
     * @param agreeCount
     */
    public void setAgreeCount(int agreeCount) {
        this.agreeCount = agreeCount;
    }


    /**
     * Gets the allowBeginTime value for this VerifyInfo.
     * 
     * @return allowBeginTime
     */
    public java.lang.String getAllowBeginTime() {
        return allowBeginTime;
    }


    /**
     * Sets the allowBeginTime value for this VerifyInfo.
     * 
     * @param allowBeginTime
     */
    public void setAllowBeginTime(java.lang.String allowBeginTime) {
        this.allowBeginTime = allowBeginTime;
    }


    /**
     * Gets the allowEndTime value for this VerifyInfo.
     * 
     * @return allowEndTime
     */
    public java.lang.String getAllowEndTime() {
        return allowEndTime;
    }


    /**
     * Sets the allowEndTime value for this VerifyInfo.
     * 
     * @param allowEndTime
     */
    public void setAllowEndTime(java.lang.String allowEndTime) {
        this.allowEndTime = allowEndTime;
    }


    /**
     * Gets the canLogin value for this VerifyInfo.
     * 
     * @return canLogin
     */
    public java.lang.String getCanLogin() {
        return canLogin;
    }


    /**
     * Sets the canLogin value for this VerifyInfo.
     * 
     * @param canLogin
     */
    public void setCanLogin(java.lang.String canLogin) {
        this.canLogin = canLogin;
    }


    /**
     * Gets the doctorMobile value for this VerifyInfo.
     * 
     * @return doctorMobile
     */
    public java.lang.String getDoctorMobile() {
        return doctorMobile;
    }


    /**
     * Sets the doctorMobile value for this VerifyInfo.
     * 
     * @param doctorMobile
     */
    public void setDoctorMobile(java.lang.String doctorMobile) {
        this.doctorMobile = doctorMobile;
    }


    /**
     * Gets the exContent value for this VerifyInfo.
     * 
     * @return exContent
     */
    public java.lang.String getExContent() {
        return exContent;
    }


    /**
     * Sets the exContent value for this VerifyInfo.
     * 
     * @param exContent
     */
    public void setExContent(java.lang.String exContent) {
        this.exContent = exContent;
    }


    /**
     * Gets the leftCount value for this VerifyInfo.
     * 
     * @return leftCount
     */
    public int getLeftCount() {
        return leftCount;
    }


    /**
     * Sets the leftCount value for this VerifyInfo.
     * 
     * @param leftCount
     */
    public void setLeftCount(int leftCount) {
        this.leftCount = leftCount;
    }


    /**
     * Gets the linkTel value for this VerifyInfo.
     * 
     * @return linkTel
     */
    public java.lang.String getLinkTel() {
        return linkTel;
    }


    /**
     * Sets the linkTel value for this VerifyInfo.
     * 
     * @param linkTel
     */
    public void setLinkTel(java.lang.String linkTel) {
        this.linkTel = linkTel;
    }


    /**
     * Gets the netConfigs value for this VerifyInfo.
     * 
     * @return netConfigs
     */
    public java.lang.String getNetConfigs() {
        return netConfigs;
    }


    /**
     * Sets the netConfigs value for this VerifyInfo.
     * 
     * @param netConfigs
     */
    public void setNetConfigs(java.lang.String netConfigs) {
        this.netConfigs = netConfigs;
    }


    /**
     * Gets the netTag value for this VerifyInfo.
     * 
     * @return netTag
     */
    public int getNetTag() {
        return netTag;
    }


    /**
     * Sets the netTag value for this VerifyInfo.
     * 
     * @param netTag
     */
    public void setNetTag(int netTag) {
        this.netTag = netTag;
    }


    /**
     * Gets the noteEnd value for this VerifyInfo.
     * 
     * @return noteEnd
     */
    public java.lang.String getNoteEnd() {
        return noteEnd;
    }


    /**
     * Sets the noteEnd value for this VerifyInfo.
     * 
     * @param noteEnd
     */
    public void setNoteEnd(java.lang.String noteEnd) {
        this.noteEnd = noteEnd;
    }


    /**
     * Gets the packageSize value for this VerifyInfo.
     * 
     * @return packageSize
     */
    public int getPackageSize() {
        return packageSize;
    }


    /**
     * Sets the packageSize value for this VerifyInfo.
     * 
     * @param packageSize
     */
    public void setPackageSize(int packageSize) {
        this.packageSize = packageSize;
    }


    /**
     * Gets the specialTag value for this VerifyInfo.
     * 
     * @return specialTag
     */
    public int getSpecialTag() {
        return specialTag;
    }


    /**
     * Sets the specialTag value for this VerifyInfo.
     * 
     * @param specialTag
     */
    public void setSpecialTag(int specialTag) {
        this.specialTag = specialTag;
    }


    /**
     * Gets the tagInfo value for this VerifyInfo.
     * 
     * @return tagInfo
     */
    public int getTagInfo() {
        return tagInfo;
    }


    /**
     * Sets the tagInfo value for this VerifyInfo.
     * 
     * @param tagInfo
     */
    public void setTagInfo(int tagInfo) {
        this.tagInfo = tagInfo;
    }


    /**
     * Gets the unitCode value for this VerifyInfo.
     * 
     * @return unitCode
     */
    public java.lang.String getUnitCode() {
        return unitCode;
    }


    /**
     * Sets the unitCode value for this VerifyInfo.
     * 
     * @param unitCode
     */
    public void setUnitCode(java.lang.String unitCode) {
        this.unitCode = unitCode;
    }


    /**
     * Gets the useCount value for this VerifyInfo.
     * 
     * @return useCount
     */
    public int getUseCount() {
        return useCount;
    }


    /**
     * Sets the useCount value for this VerifyInfo.
     * 
     * @param useCount
     */
    public void setUseCount(int useCount) {
        this.useCount = useCount;
    }


    /**
     * Gets the useSpField value for this VerifyInfo.
     * 
     * @return useSpField
     */
    public java.lang.String getUseSpField() {
        return useSpField;
    }


    /**
     * Sets the useSpField value for this VerifyInfo.
     * 
     * @param useSpField
     */
    public void setUseSpField(java.lang.String useSpField) {
        this.useSpField = useSpField;
    }


    /**
     * Gets the wakeInfo value for this VerifyInfo.
     * 
     * @return wakeInfo
     */
    public java.lang.String getWakeInfo() {
        return wakeInfo;
    }


    /**
     * Sets the wakeInfo value for this VerifyInfo.
     * 
     * @param wakeInfo
     */
    public void setWakeInfo(java.lang.String wakeInfo) {
        this.wakeInfo = wakeInfo;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof VerifyInfo)) return false;
        VerifyInfo other = (VerifyInfo) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.agreeCount == other.getAgreeCount() &&
            ((this.allowBeginTime==null && other.getAllowBeginTime()==null) || 
             (this.allowBeginTime!=null &&
              this.allowBeginTime.equals(other.getAllowBeginTime()))) &&
            ((this.allowEndTime==null && other.getAllowEndTime()==null) || 
             (this.allowEndTime!=null &&
              this.allowEndTime.equals(other.getAllowEndTime()))) &&
            ((this.canLogin==null && other.getCanLogin()==null) || 
             (this.canLogin!=null &&
              this.canLogin.equals(other.getCanLogin()))) &&
            ((this.doctorMobile==null && other.getDoctorMobile()==null) || 
             (this.doctorMobile!=null &&
              this.doctorMobile.equals(other.getDoctorMobile()))) &&
            ((this.exContent==null && other.getExContent()==null) || 
             (this.exContent!=null &&
              this.exContent.equals(other.getExContent()))) &&
            this.leftCount == other.getLeftCount() &&
            ((this.linkTel==null && other.getLinkTel()==null) || 
             (this.linkTel!=null &&
              this.linkTel.equals(other.getLinkTel()))) &&
            ((this.netConfigs==null && other.getNetConfigs()==null) || 
             (this.netConfigs!=null &&
              this.netConfigs.equals(other.getNetConfigs()))) &&
            this.netTag == other.getNetTag() &&
            ((this.noteEnd==null && other.getNoteEnd()==null) || 
             (this.noteEnd!=null &&
              this.noteEnd.equals(other.getNoteEnd()))) &&
            this.packageSize == other.getPackageSize() &&
            this.specialTag == other.getSpecialTag() &&
            this.tagInfo == other.getTagInfo() &&
            ((this.unitCode==null && other.getUnitCode()==null) || 
             (this.unitCode!=null &&
              this.unitCode.equals(other.getUnitCode()))) &&
            this.useCount == other.getUseCount() &&
            ((this.useSpField==null && other.getUseSpField()==null) || 
             (this.useSpField!=null &&
              this.useSpField.equals(other.getUseSpField()))) &&
            ((this.wakeInfo==null && other.getWakeInfo()==null) || 
             (this.wakeInfo!=null &&
              this.wakeInfo.equals(other.getWakeInfo())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += getAgreeCount();
        if (getAllowBeginTime() != null) {
            _hashCode += getAllowBeginTime().hashCode();
        }
        if (getAllowEndTime() != null) {
            _hashCode += getAllowEndTime().hashCode();
        }
        if (getCanLogin() != null) {
            _hashCode += getCanLogin().hashCode();
        }
        if (getDoctorMobile() != null) {
            _hashCode += getDoctorMobile().hashCode();
        }
        if (getExContent() != null) {
            _hashCode += getExContent().hashCode();
        }
        _hashCode += getLeftCount();
        if (getLinkTel() != null) {
            _hashCode += getLinkTel().hashCode();
        }
        if (getNetConfigs() != null) {
            _hashCode += getNetConfigs().hashCode();
        }
        _hashCode += getNetTag();
        if (getNoteEnd() != null) {
            _hashCode += getNoteEnd().hashCode();
        }
        _hashCode += getPackageSize();
        _hashCode += getSpecialTag();
        _hashCode += getTagInfo();
        if (getUnitCode() != null) {
            _hashCode += getUnitCode().hashCode();
        }
        _hashCode += getUseCount();
        if (getUseSpField() != null) {
            _hashCode += getUseSpField().hashCode();
        }
        if (getWakeInfo() != null) {
            _hashCode += getWakeInfo().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(VerifyInfo.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://service.sms.chis.com/", "verifyInfo"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("agreeCount");
        elemField.setXmlName(new javax.xml.namespace.QName("", "agreeCount"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("allowBeginTime");
        elemField.setXmlName(new javax.xml.namespace.QName("", "allowBeginTime"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("allowEndTime");
        elemField.setXmlName(new javax.xml.namespace.QName("", "allowEndTime"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("canLogin");
        elemField.setXmlName(new javax.xml.namespace.QName("", "canLogin"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("doctorMobile");
        elemField.setXmlName(new javax.xml.namespace.QName("", "doctorMobile"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("exContent");
        elemField.setXmlName(new javax.xml.namespace.QName("", "exContent"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("leftCount");
        elemField.setXmlName(new javax.xml.namespace.QName("", "leftCount"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("linkTel");
        elemField.setXmlName(new javax.xml.namespace.QName("", "linkTel"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("netConfigs");
        elemField.setXmlName(new javax.xml.namespace.QName("", "netConfigs"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("netTag");
        elemField.setXmlName(new javax.xml.namespace.QName("", "netTag"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("noteEnd");
        elemField.setXmlName(new javax.xml.namespace.QName("", "noteEnd"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("packageSize");
        elemField.setXmlName(new javax.xml.namespace.QName("", "packageSize"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("specialTag");
        elemField.setXmlName(new javax.xml.namespace.QName("", "specialTag"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tagInfo");
        elemField.setXmlName(new javax.xml.namespace.QName("", "tagInfo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("unitCode");
        elemField.setXmlName(new javax.xml.namespace.QName("", "unitCode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("useCount");
        elemField.setXmlName(new javax.xml.namespace.QName("", "useCount"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("useSpField");
        elemField.setXmlName(new javax.xml.namespace.QName("", "useSpField"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("wakeInfo");
        elemField.setXmlName(new javax.xml.namespace.QName("", "wakeInfo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
