/**
 * VerifyInfo4.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.chis.sms.service;

public class VerifyInfo4  extends com.chis.sms.service.VerifyInfo3  implements java.io.Serializable {
    private java.lang.String accountMny;

    private java.lang.String prop;

    private java.lang.String replyMess;

    private java.lang.String unitName;

    public VerifyInfo4() {
    }

    public VerifyInfo4(
           int agreeCount,
           java.lang.String allowBeginTime,
           java.lang.String allowEndTime,
           java.lang.String canLogin,
           java.lang.String doctorMobile,
           java.lang.String exContent,
           int leftCount,
           java.lang.String linkTel,
           java.lang.String netConfigs,
           int netTag,
           java.lang.String noteEnd,
           int packageSize,
           int specialTag,
           int tagInfo,
           java.lang.String unitCode,
           int useCount,
           java.lang.String useSpField,
           java.lang.String wakeInfo,
           java.lang.String allowNets,
           java.lang.String sendSms,
           java.lang.String accountMny,
           java.lang.String prop,
           java.lang.String replyMess,
           java.lang.String unitName) {
        super(
            agreeCount,
            allowBeginTime,
            allowEndTime,
            canLogin,
            doctorMobile,
            exContent,
            leftCount,
            linkTel,
            netConfigs,
            netTag,
            noteEnd,
            packageSize,
            specialTag,
            tagInfo,
            unitCode,
            useCount,
            useSpField,
            wakeInfo,
            allowNets,
            sendSms);
        this.accountMny = accountMny;
        this.prop = prop;
        this.replyMess = replyMess;
        this.unitName = unitName;
    }


    /**
     * Gets the accountMny value for this VerifyInfo4.
     * 
     * @return accountMny
     */
    public java.lang.String getAccountMny() {
        return accountMny;
    }


    /**
     * Sets the accountMny value for this VerifyInfo4.
     * 
     * @param accountMny
     */
    public void setAccountMny(java.lang.String accountMny) {
        this.accountMny = accountMny;
    }


    /**
     * Gets the prop value for this VerifyInfo4.
     * 
     * @return prop
     */
    public java.lang.String getProp() {
        return prop;
    }


    /**
     * Sets the prop value for this VerifyInfo4.
     * 
     * @param prop
     */
    public void setProp(java.lang.String prop) {
        this.prop = prop;
    }


    /**
     * Gets the replyMess value for this VerifyInfo4.
     * 
     * @return replyMess
     */
    public java.lang.String getReplyMess() {
        return replyMess;
    }


    /**
     * Sets the replyMess value for this VerifyInfo4.
     * 
     * @param replyMess
     */
    public void setReplyMess(java.lang.String replyMess) {
        this.replyMess = replyMess;
    }


    /**
     * Gets the unitName value for this VerifyInfo4.
     * 
     * @return unitName
     */
    public java.lang.String getUnitName() {
        return unitName;
    }


    /**
     * Sets the unitName value for this VerifyInfo4.
     * 
     * @param unitName
     */
    public void setUnitName(java.lang.String unitName) {
        this.unitName = unitName;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof VerifyInfo4)) return false;
        VerifyInfo4 other = (VerifyInfo4) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.accountMny==null && other.getAccountMny()==null) || 
             (this.accountMny!=null &&
              this.accountMny.equals(other.getAccountMny()))) &&
            ((this.prop==null && other.getProp()==null) || 
             (this.prop!=null &&
              this.prop.equals(other.getProp()))) &&
            ((this.replyMess==null && other.getReplyMess()==null) || 
             (this.replyMess!=null &&
              this.replyMess.equals(other.getReplyMess()))) &&
            ((this.unitName==null && other.getUnitName()==null) || 
             (this.unitName!=null &&
              this.unitName.equals(other.getUnitName())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getAccountMny() != null) {
            _hashCode += getAccountMny().hashCode();
        }
        if (getProp() != null) {
            _hashCode += getProp().hashCode();
        }
        if (getReplyMess() != null) {
            _hashCode += getReplyMess().hashCode();
        }
        if (getUnitName() != null) {
            _hashCode += getUnitName().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(VerifyInfo4.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://service.sms.chis.com/", "verifyInfo4"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("accountMny");
        elemField.setXmlName(new javax.xml.namespace.QName("", "accountMny"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("prop");
        elemField.setXmlName(new javax.xml.namespace.QName("", "prop"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("replyMess");
        elemField.setXmlName(new javax.xml.namespace.QName("", "replyMess"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("unitName");
        elemField.setXmlName(new javax.xml.namespace.QName("", "unitName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
