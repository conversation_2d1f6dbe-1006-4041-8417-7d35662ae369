package com.chis.sms.service;

public class SmsServerProxy implements com.chis.sms.service.SmsServer {
  private String _endpoint = null;
  private com.chis.sms.service.SmsServer smsServer = null;
  
  public SmsServerProxy() {
    _initSmsServerProxy();
  }
  
  public SmsServerProxy(String endpoint) {
    _endpoint = endpoint;
    _initSmsServerProxy();
  }
  
  private void _initSmsServerProxy() {
    try {
      smsServer = (new com.chis.sms.service.SmsServiceLocator()).getSmsServerPort();
      if (smsServer != null) {
        if (_endpoint != null)
          ((javax.xml.rpc.Stub)smsServer)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
        else
          _endpoint = (String)((javax.xml.rpc.Stub)smsServer)._getProperty("javax.xml.rpc.service.endpoint.address");
      }
      
    }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  
  public String getEndpoint() {
    return _endpoint;
  }
  
  public void setEndpoint(String endpoint) {
    _endpoint = endpoint;
    if (smsServer != null)
      ((javax.xml.rpc.Stub)smsServer)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    
  }
  
  public com.chis.sms.service.SmsServer getSmsServer() {
    if (smsServer == null)
      _initSmsServerProxy();
    return smsServer;
  }
  
  public com.chis.sms.service.VerifyInfo smsLogin(java.lang.String userCode, java.lang.String passWord) throws java.rmi.RemoteException{
    if (smsServer == null)
      _initSmsServerProxy();
    return smsServer.smsLogin(userCode, passWord);
  }
  
  public com.chis.sms.service.VerifyInfo2 smsLogin2(java.lang.String userCode, java.lang.String passWord) throws java.rmi.RemoteException{
    if (smsServer == null)
      _initSmsServerProxy();
    return smsServer.smsLogin2(userCode, passWord);
  }
  
  public com.chis.sms.service.VerifyInfo3 smsLogin3(java.lang.String userCode, java.lang.String passWord) throws java.rmi.RemoteException{
    if (smsServer == null)
      _initSmsServerProxy();
    return smsServer.smsLogin3(userCode, passWord);
  }
  
  public com.chis.sms.service.VerifyInfo4 smsLogin4(java.lang.String userCode, java.lang.String passWord) throws java.rmi.RemoteException{
    if (smsServer == null)
      _initSmsServerProxy();
    return smsServer.smsLogin4(userCode, passWord);
  }
  
  public com.chis.sms.service.VerifyInfo4 smsLogin5(java.lang.String userCode, java.lang.String passWord, java.lang.String versionCode) throws java.rmi.RemoteException{
    if (smsServer == null)
      _initSmsServerProxy();
    return smsServer.smsLogin5(userCode, passWord, versionCode);
  }
  
  public int upLoad(java.lang.String upString, java.lang.String userCode, java.lang.String passWord, java.lang.String specialTag) throws java.rmi.RemoteException{
    if (smsServer == null)
      _initSmsServerProxy();
    return smsServer.upLoad(upString, userCode, passWord, specialTag);
  }
  
  public java.lang.String getServerDateTime() throws java.rmi.RemoteException{
    if (smsServer == null)
      _initSmsServerProxy();
    return smsServer.getServerDateTime();
  }
  
  public com.chis.sms.service.VerifyInfo5 smsLogin6(java.lang.String userCode, java.lang.String passWord, java.lang.String versionCode) throws java.rmi.RemoteException{
    if (smsServer == null)
      _initSmsServerProxy();
    return smsServer.smsLogin6(userCode, passWord, versionCode);
  }
  
  public int appUpLoad(java.lang.String upString, java.lang.String userCode, java.lang.String passWord, java.lang.String specialTag) throws java.rmi.RemoteException{
    if (smsServer == null)
      _initSmsServerProxy();
    return smsServer.appUpLoad(upString, userCode, passWord, specialTag);
  }
  
  public int equUpLoad(java.lang.String upString, java.lang.String userCode, java.lang.String passWord) throws java.rmi.RemoteException{
    if (smsServer == null)
      _initSmsServerProxy();
    return smsServer.equUpLoad(upString, userCode, passWord);
  }
  
  public int sendSmsForGn(java.lang.String passw, java.lang.String appid, java.lang.String telNum, java.lang.String msg, java.lang.String intfid, java.lang.String intfname) throws java.rmi.RemoteException{
    if (smsServer == null)
      _initSmsServerProxy();
    return smsServer.sendSmsForGn(passw, appid, telNum, msg, intfid, intfname);
  }
  
  public int smsUpLoad(java.lang.String upString, java.lang.String userCode, java.lang.String passWord) throws java.rmi.RemoteException{
    if (smsServer == null)
      _initSmsServerProxy();
    return smsServer.smsUpLoad(upString, userCode, passWord);
  }
  
  
}