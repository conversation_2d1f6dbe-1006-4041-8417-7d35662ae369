/**
 * SmsServer.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.chis.sms.service;

public interface SmsServer extends java.rmi.Remote {
    public com.chis.sms.service.VerifyInfo smsLogin(java.lang.String userCode, java.lang.String passWord) throws java.rmi.RemoteException;
    public com.chis.sms.service.VerifyInfo2 smsLogin2(java.lang.String userCode, java.lang.String passWord) throws java.rmi.RemoteException;
    public com.chis.sms.service.VerifyInfo3 smsLogin3(java.lang.String userCode, java.lang.String passWord) throws java.rmi.RemoteException;
    public com.chis.sms.service.VerifyInfo4 smsLogin4(java.lang.String userCode, java.lang.String passWord) throws java.rmi.RemoteException;
    public com.chis.sms.service.VerifyInfo4 smsLogin5(java.lang.String userCode, java.lang.String passWord, java.lang.String versionCode) throws java.rmi.RemoteException;
    public int upLoad(java.lang.String upString, java.lang.String userCode, java.lang.String passWord, java.lang.String specialTag) throws java.rmi.RemoteException;
    public java.lang.String getServerDateTime() throws java.rmi.RemoteException;
    public com.chis.sms.service.VerifyInfo5 smsLogin6(java.lang.String userCode, java.lang.String passWord, java.lang.String versionCode) throws java.rmi.RemoteException;
    public int appUpLoad(java.lang.String upString, java.lang.String userCode, java.lang.String passWord, java.lang.String specialTag) throws java.rmi.RemoteException;
    public int equUpLoad(java.lang.String upString, java.lang.String userCode, java.lang.String passWord) throws java.rmi.RemoteException;
    public int sendSmsForGn(java.lang.String passw, java.lang.String appid, java.lang.String telNum, java.lang.String msg, java.lang.String intfid, java.lang.String intfname) throws java.rmi.RemoteException;
    public int smsUpLoad(java.lang.String upString, java.lang.String userCode, java.lang.String passWord) throws java.rmi.RemoteException;
}
