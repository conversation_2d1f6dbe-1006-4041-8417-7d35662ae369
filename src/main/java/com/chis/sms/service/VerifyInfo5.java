/**
 * VerifyInfo5.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.chis.sms.service;

public class VerifyInfo5  extends com.chis.sms.service.VerifyInfo4  implements java.io.Serializable {
    private java.lang.String ifIden;

    public VerifyInfo5() {
    }

    public VerifyInfo5(
           int agreeCount,
           java.lang.String allowBeginTime,
           java.lang.String allowEndTime,
           java.lang.String canLogin,
           java.lang.String doctorMobile,
           java.lang.String exContent,
           int leftCount,
           java.lang.String linkTel,
           java.lang.String netConfigs,
           int netTag,
           java.lang.String noteEnd,
           int packageSize,
           int specialTag,
           int tagInfo,
           java.lang.String unitCode,
           int useCount,
           java.lang.String useSpField,
           java.lang.String wakeInfo,
           java.lang.String allowNets,
           java.lang.String sendSms,
           java.lang.String accountMny,
           java.lang.String prop,
           java.lang.String replyMess,
           java.lang.String unitName,
           java.lang.String ifIden) {
        super(
            agreeCount,
            allowBeginTime,
            allowEndTime,
            canLogin,
            doctorMobile,
            exContent,
            leftCount,
            linkTel,
            netConfigs,
            netTag,
            noteEnd,
            packageSize,
            specialTag,
            tagInfo,
            unitCode,
            useCount,
            useSpField,
            wakeInfo,
            allowNets,
            sendSms,
            accountMny,
            prop,
            replyMess,
            unitName);
        this.ifIden = ifIden;
    }


    /**
     * Gets the ifIden value for this VerifyInfo5.
     * 
     * @return ifIden
     */
    public java.lang.String getIfIden() {
        return ifIden;
    }


    /**
     * Sets the ifIden value for this VerifyInfo5.
     * 
     * @param ifIden
     */
    public void setIfIden(java.lang.String ifIden) {
        this.ifIden = ifIden;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof VerifyInfo5)) return false;
        VerifyInfo5 other = (VerifyInfo5) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.ifIden==null && other.getIfIden()==null) || 
             (this.ifIden!=null &&
              this.ifIden.equals(other.getIfIden())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getIfIden() != null) {
            _hashCode += getIfIden().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(VerifyInfo5.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://service.sms.chis.com/", "verifyInfo5"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ifIden");
        elemField.setXmlName(new javax.xml.namespace.QName("", "ifIden"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
