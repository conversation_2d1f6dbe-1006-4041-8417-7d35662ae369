package com.chis.activiti.script.system;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.script.IScript;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * OA模块使用的脚本工具方法
 * <AUTHOR>
 * @createTime 2015年10月8日
 */
@Component
@Transactional(readOnly = true)
public class ScriptSystem implements IScript, Serializable{
	
	private static final long serialVersionUID = 2177243240698362738L;
	
	@Autowired
	private SystemModuleServiceImpl moduleService;
	@Autowired
	private CommServiceImpl commService;
	@Autowired
	private FlowBusinessServiceImpl flowBusinessService;
	
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	
	/**
	 * 给流程人员分发消息
	 * @param taskId
	 * @param businessId
	 */
	public void sendFfMsgToPsn(String userIds,String taskId,String businessJson)	{
		if(StringUtils.isNotBlank(userIds) && StringUtils.isNotBlank(taskId) && StringUtils.isNotBlank(businessJson) )	{
			this.flowBusinessService.ffMsgInProcess(taskId, userIds, this.sessionData.getUser().getRid(), businessJson);
		}
	}
	
	/**
	 * 根据流程发起者ID获取流程发起者所在的科室名称
	 * @param instanceId 流程实例ID
	 * @return 流程发起者所在的科室名称
	 */
	public String findStarterOfficeName(String startUserId) {
		String officeName = "";
		if(StringUtils.isNotBlank(startUserId) && StringUtils.isNumeric(startUserId)) {
			officeName = moduleService.findOfficeNameByUserId(startUserId);
		}
		return officeName;
	}
	
	/**
	 * 根据流程发起者ID获取流程发起者是否是领导
	 * @param instanceId 流程实例ID
	 * @return 0:否 1：是
	 */
	public String findStarterIfLeader(String startUserId) {
		String isLeader = "";
		if(StringUtils.isNotBlank(startUserId) && StringUtils.isNumeric(startUserId)) {
			isLeader = moduleService.findIsLeaderByUserId(startUserId);
		}
		return isLeader;
	}
	
	/**
	 * 执行INSERT、UPDATE、DELETE语句
	 * @param sql 原生SQL语句
	 */
	@Transactional(readOnly = false)
	public void executeUpdate(String sql) {
		this.commService.executeUpdate(sql);
	}
	
	/**
	 * 执行查询语句，返回List<Object[]>
	 * @param sql 原生SQL语句
	 */
	public List<Object[]> executeQuery(String sql, Map<String, Object> paramMap) {
		return this.commService.findDataBySqlNoPage(sql, paramMap);
	}
	
	
	
	
}
