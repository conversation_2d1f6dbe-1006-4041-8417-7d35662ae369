package com.chis.activiti.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.system.entity.TsUserInfo;

/**
 * TdFlowTrust entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TD_FLOW_TRUST")
@SequenceGenerator(name = "TdFlowTrust", sequenceName = "TD_FLOW_TRUST_SEQ", allocationSize = 1)
public class TdFlowTrust implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = -50396092833509348L;
	private Integer rid;
	private TsUserInfo tsUserInfoByUserId;
	private TsUserInfo tsUserInfoByTrustUserId;
	private Date beginTime;
	private Date endTime;
	private Integer state;
	private List<TdFlowTrustDef> tdFlowTrustDefs = new ArrayList<TdFlowTrustDef>(0);

	// Constructors

	/** default constructor */
	public TdFlowTrust() {
	}

	/** minimal constructor */
	public TdFlowTrust(Integer rid, TsUserInfo tsUserInfoByUserId,
			TsUserInfo tsUserInfoByTrustUserId, Integer state) {
		this.rid = rid;
		this.tsUserInfoByUserId = tsUserInfoByUserId;
		this.tsUserInfoByTrustUserId = tsUserInfoByTrustUserId;
		this.state = state;
	}

	/** full constructor */
	public TdFlowTrust(Integer rid, TsUserInfo tsUserInfoByUserId,
			TsUserInfo tsUserInfoByTrustUserId, Date beginTime, Date endTime,
			Integer state, List<TdFlowTrustDef> tdFlowTrustDefs) {
		this.rid = rid;
		this.tsUserInfoByUserId = tsUserInfoByUserId;
		this.tsUserInfoByTrustUserId = tsUserInfoByTrustUserId;
		this.beginTime = beginTime;
		this.endTime = endTime;
		this.state = state;
		this.tdFlowTrustDefs = tdFlowTrustDefs;
	}

	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowTrust")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "USER_ID" )
	public TsUserInfo getTsUserInfoByUserId() {
		return this.tsUserInfoByUserId;
	}

	public void setTsUserInfoByUserId(TsUserInfo tsUserInfoByUserId) {
		this.tsUserInfoByUserId = tsUserInfoByUserId;
	}

	@ManyToOne
	@JoinColumn(name = "TRUST_USER_ID" )
	public TsUserInfo getTsUserInfoByTrustUserId() {
		return this.tsUserInfoByTrustUserId;
	}

	public void setTsUserInfoByTrustUserId(TsUserInfo tsUserInfoByTrustUserId) {
		this.tsUserInfoByTrustUserId = tsUserInfoByTrustUserId;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "BEGIN_TIME", length = 7)
	public Date getBeginTime() {
		return this.beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "END_TIME", length = 7)
	public Date getEndTime() {
		return this.endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	@Column(name = "STATE" , precision = 1, scale = 0)
	public Integer getState() {
		return this.state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdFlowTrust", orphanRemoval = true)
	public List<TdFlowTrustDef> getTdFlowTrustDefs() {
		return this.tdFlowTrustDefs;
	}

	public void setTdFlowTrustDefs(List<TdFlowTrustDef> tdFlowTrustDefs) {
		this.tdFlowTrustDefs = tdFlowTrustDefs;
	}

}