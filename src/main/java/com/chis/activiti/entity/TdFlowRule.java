package com.chis.activiti.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "TD_FLOW_RULE")
@SequenceGenerator(name = "TdFlowRule", sequenceName = "TD_FLOW_RULE_SEQ", allocationSize = 1)
@NamedQueries({
    @NamedQuery(name = "TdFlowRule.findRuleByCode", query = "SELECT t FROM TdFlowRule t WHERE t.idcode=:idcode "),
    @NamedQuery(name = "TdFlowRule.findRules", query = "SELECT new TdFlowRule(t.idcode, t.ruleDesc) FROM TdFlowRule t ORDER BY t.idcode ")
})
public class TdFlowRule implements java.io.Serializable {

	private static final long serialVersionUID = 8840034881533099838L;

	private Integer rid;
	private String idcode;
	private String impClass;
	private String ruleDesc;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	public TdFlowRule() {
	}

	public TdFlowRule(Integer rid) {
		this.rid = rid;
	}
	
	public TdFlowRule(String idcode, String ruleDesc) {
		this.idcode = idcode;
		this.ruleDesc = ruleDesc;
	}

	public TdFlowRule(String idcode, String impClass, String ruleDesc, Date createDate, Integer createManid) {
		this.idcode = idcode;
		this.impClass = impClass;
		this.ruleDesc = ruleDesc;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowRule")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Column(name = "IDCODE", unique = true , length = 50)
	public String getIdcode() {
		return this.idcode;
	}

	public void setIdcode(String idcode) {
		this.idcode = idcode;
	}

	@Column(name = "IMP_CLASS" , length = 200)
	public String getImpClass() {
		return this.impClass;
	}

	public void setImpClass(String impClass) {
		this.impClass = impClass;
	}

	@Column(name = "RULE_DESC" , length = 200)
	public String getRuleDesc() {
		return this.ruleDesc;
	}

	public void setRuleDesc(String ruleDesc) {
		this.ruleDesc = ruleDesc;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}


}