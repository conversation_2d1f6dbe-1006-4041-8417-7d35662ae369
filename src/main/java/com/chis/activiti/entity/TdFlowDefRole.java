package com.chis.activiti.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "TD_FLOW_DEF_ROLE")
@SequenceGenerator(name = "TdFlowDefRole", sequenceName = "TD_FLOW_DEF_ROLE_SEQ", allocationSize = 1)
public class TdFlowDefRole implements java.io.Serializable {

	private static final long serialVersionUID = -4831504593166558338L;
	private Integer rid;
	private Integer roleId;
	private TdFlowDef tdFlowDef;

	public TdFlowDefRole() {
	}

	public TdFlowDefRole(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowDefRole")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@Column(name = "ROLE_ID")
	public Integer getRoleId() {
		return roleId;
	}

	public void setRoleId(Integer roleId) {
		this.roleId = roleId;
	}

	@ManyToOne
	@JoinColumn(name = "DEF_ID" )
	public TdFlowDef getTdFlowDef() {
		return this.tdFlowDef;
	}

	public void setTdFlowDef(TdFlowDef tdFlowDef) {
		this.tdFlowDef = tdFlowDef;
	}

}