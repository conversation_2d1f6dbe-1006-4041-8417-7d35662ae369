package com.chis.activiti.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.chis.modules.system.entity.TsTxtype;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "TD_FLOW_NODE_TXTYPE")
@SequenceGenerator(name = "TdFlowNodeTxTypeSeq", sequenceName = "TD_FLOW_NODE_TXTYPE_SEQ", allocationSize = 1)
public class TdFlowNodeTxType implements Serializable {

	private static final long serialVersionUID = 7331477482195005403L;
	private Integer rid;
	private TsTxtype tsTxtype;
	private TdFlowNode tdFlowNode;

	public TdFlowNodeTxType() {
	}

	public TdFlowNodeTxType(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowNodeTxTypeSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "FLOW_NODE_ID" )
	public TdFlowNode getTdFlowNode() {
		return tdFlowNode;
	}

	public void setTdFlowNode(TdFlowNode tdFlowNode) {
		this.tdFlowNode = tdFlowNode;
	}

	@ManyToOne
	@JoinColumn(name = "TX_ID" )
	public TsTxtype getTsTxtype() {
		return tsTxtype;
	}

	public void setTsTxtype(TsTxtype tsTxtype) {
		this.tsTxtype = tsTxtype;
	}
}