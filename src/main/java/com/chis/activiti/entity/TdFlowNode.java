package com.chis.activiti.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.chis.activiti.enumn.DealType;
import com.chis.activiti.enumn.FlowInType;
import com.chis.modules.system.entity.TdFormDef;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "TD_FLOW_NODE")
@SequenceGenerator(name = "TdFlowNode", sequenceName = "TD_FLOW_NODE_SEQ", allocationSize = 1)
@NamedQueries({
    @NamedQuery(name = "TdFlowNode.findByTask", query = "SELECT t FROM TdFlowNode t WHERE t.tdFlowDef.actDefId=:actDefId AND t.actNodeId=:actNodeId ")
})
public class TdFlowNode implements java.io.Serializable {

	private static final long serialVersionUID = 7331477482195005403L;
	private Integer rid;
	private TdFlowDef tdFlowDef;
	private String actNodeId;
	private String nodeName;
	private FlowInType flowInType = FlowInType.XZTZ;
	private DealType dealType = DealType.YBJD;
	private Short ifAdv = 0;
	private String jspUrl;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private Integer nums;
	private Short ifNbNode = 0;
	private Integer limitHour;
	private Short fstNodeDisp = 1;
	private Short ffMsg = 1;

	private String selectedManIds;
	private String selectedManNames;
	private Integer mulNodeRule;

	private List<TdFlowNodeBtn> tdFlowNodeBtnList = new ArrayList<TdFlowNodeBtn>(
			0);
	private List<TdFlowNodeScript> tdFlowNodeScriptList = new ArrayList<TdFlowNodeScript>(
			0);
	private List<TdFlowRuleNode> tdFlowRuleNodes = new ArrayList<>();
	private TdFlowNodePage tdFlowNodePage;
	private TdFormDef tdFormDef;

	private String flowKeyName;//流程定义Key
	
	public TdFlowNode() {
	}

	public TdFlowNode(Integer rid) {
		this.rid = rid;
	}

	/**
	 * @param rid
	 *            主键
	 * @param actNodeId
	 *            节点ID
	 * @param nodeName
	 *            节点名称
	 */
	public TdFlowNode(Integer rid, String actNodeId, String nodeName) {
		this.rid = rid;
		this.actNodeId = actNodeId;
		this.nodeName = nodeName;
	}

	/**
	 * @param rid
	 *            主键
	 * @param actNodeId
	 *            节点ID
	 * @param nodeName
	 *            节点名称
	 * @param dealType
	 *            节点处理类型:一般节点还是会签节点
	 */
	public TdFlowNode(Integer rid, String actNodeId, String nodeName,
			DealType dealType) {
		this.rid = rid;
		this.actNodeId = actNodeId;
		this.nodeName = nodeName;
		this.dealType = dealType;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowNode")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "DEF_ID" )
	public TdFlowDef getTdFlowDef() {
		return this.tdFlowDef;
	}

	public void setTdFlowDef(TdFlowDef tdFlowDef) {
		this.tdFlowDef = tdFlowDef;
	}

	@Column(name = "ACT_NODE_ID" )
	public String getActNodeId() {
		return this.actNodeId;
	}

	public void setActNodeId(String actNodeId) {
		this.actNodeId = actNodeId;
	}

	@Column(name = "NODE_NAME" )
	public String getNodeName() {
		return this.nodeName;
	}

	public void setNodeName(String nodeName) {
		this.nodeName = nodeName;
	}

	@Enumerated
	@Column(name = "FLOWIN_TYPE")
	public FlowInType getFlowInType() {
		return flowInType;
	}

	public void setFlowInType(FlowInType flowInType) {
		this.flowInType = flowInType;
	}

	@Enumerated
	@Column(name = "DEAL_TYPE")
	public DealType getDealType() {
		return dealType;
	}

	public void setDealType(DealType dealType) {
		this.dealType = dealType;
	}

	@Column(name = "IF_ADV")
	public Short getIfAdv() {
		return this.ifAdv;
	}

	public void setIfAdv(Short ifAdv) {
		this.ifAdv = ifAdv;
	}

	@Column(name = "JSP_URL", length = 500)
	public String getJspUrl() {
		return this.jspUrl;
	}

	public void setJspUrl(String jspUrl) {
		this.jspUrl = jspUrl;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Column(name = "NUMS")
	public Integer getNums() {
		return nums;
	}

	public void setNums(Integer nums) {
		this.nums = nums;
	}

	@Column(name = "IF_NB_NODE")
	public Short getIfNbNode() {
		return ifNbNode;
	}

	public void setIfNbNode(Short ifNbNode) {
		this.ifNbNode = ifNbNode;
	}

	@Column(name = "LIMIT_HOUR")
	public Integer getLimitHour() {
		return limitHour;
	}

	public void setLimitHour(Integer limitHour) {
		this.limitHour = limitHour;
	}

	@Transient
	public String getSelectedManIds() {
		return selectedManIds;
	}

	public void setSelectedManIds(String selectedManIds) {
		this.selectedManIds = selectedManIds;
	}

	@Transient
	public String getSelectedManNames() {
		return selectedManNames;
	}

	public void setSelectedManNames(String selectedManNames) {
		this.selectedManNames = selectedManNames;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdFlowNode")
	public List<TdFlowNodeBtn> getTdFlowNodeBtnList() {
		return tdFlowNodeBtnList;
	}

	public void setTdFlowNodeBtnList(List<TdFlowNodeBtn> tdFlowNodeBtnList) {
		this.tdFlowNodeBtnList = tdFlowNodeBtnList;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdFlowNode")
	public List<TdFlowNodeScript> getTdFlowNodeScriptList() {
		return tdFlowNodeScriptList;
	}

	public void setTdFlowNodeScriptList(
			List<TdFlowNodeScript> tdFlowNodeScriptList) {
		this.tdFlowNodeScriptList = tdFlowNodeScriptList;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdFlowNode", orphanRemoval = true)
	public List<TdFlowRuleNode> getTdFlowRuleNodes() {
		return tdFlowRuleNodes;
	}

	public void setTdFlowRuleNodes(List<TdFlowRuleNode> tdFlowRuleNodes) {
		this.tdFlowRuleNodes = tdFlowRuleNodes;
	}

	@ManyToOne
	@JoinColumn(name = "PAGE_ID")
	public TdFlowNodePage getTdFlowNodePage() {
		return tdFlowNodePage;
	}

	public void setTdFlowNodePage(TdFlowNodePage tdFlowNodePage) {
		this.tdFlowNodePage = tdFlowNodePage;
	}

	@Column(name = "FST_NODE_DISP")
	public Short getFstNodeDisp() {
		return fstNodeDisp;
	}

	public void setFstNodeDisp(Short fstNodeDisp) {
		this.fstNodeDisp = fstNodeDisp;
	}

	@Column(name = "FF_MSG")
	public Short getFfMsg() {
		return ffMsg;
	}

	public void setFfMsg(Short ffMsg) {
		this.ffMsg = ffMsg;
	}

	@ManyToOne
	@JoinColumn(name = "DYNA_FORM_ID")
	public TdFormDef getTdFormDef() {
		return tdFormDef;
	}

	public void setTdFormDef(TdFormDef tdFormDef) {
		this.tdFormDef = tdFormDef;
	}

	@Transient
	public String getFlowKeyName() {
		return flowKeyName;
	}

	public void setFlowKeyName(String flowKeyName) {
		this.flowKeyName = flowKeyName;
	}

	@Column(name = "MUL_NODE_RULE")
	public Integer getMulNodeRule() {
		return this.mulNodeRule;
	}

	public void setMulNodeRule(Integer mulNodeRule) {
		this.mulNodeRule = mulNodeRule;
	}

}