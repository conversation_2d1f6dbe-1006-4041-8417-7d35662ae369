package com.chis.activiti.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "TD_FLOW_NODE_SCRIPT")
@SequenceGenerator(name = "TdFlowNodeScript", sequenceName = "TD_FLOW_NODE_SCRIPT_SEQ", allocationSize = 1)
@NamedQueries({
    @NamedQuery(name = "TdFlowNodeScript.findByTaskNodeId", 
    		query = "SELECT t FROM TdFlowNodeScript t WHERE t.tdFlowNode.tdFlowDef.actDefId=:defId and t.tdFlowNode.actNodeId=:actNodeId and t.scriptType=:scriptType")
})
public class TdFlowNodeScript implements java.io.Serializable {

	private static final long serialVersionUID = -4676194844700764113L;
	private Integer rid;
	private TdFlowNode tdFlowNode;
	private String script;
	private Integer scriptType;
	
	public TdFlowNodeScript() {
	}

	public TdFlowNodeScript(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowNodeScript")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "FLOW_NODE_ID" )
	public TdFlowNode getTdFlowNode() {
		return tdFlowNode;
	}

	public void setTdFlowNode(TdFlowNode tdFlowNode) {
		this.tdFlowNode = tdFlowNode;
	}

	@Column(name = "SCRIPT")
	public String getScript() {
		return script;
	}

	public void setScript(String script) {
		this.script = script;
	}

	@Column(name = "SCRIPT_TYPE")
	public Integer getScriptType() {
		return scriptType;
	}

	public void setScriptType(Integer scriptType) {
		this.scriptType = scriptType;
	}
}