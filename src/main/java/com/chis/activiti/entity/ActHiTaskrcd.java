package com.chis.activiti.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.activiti.enumn.ActOptType;

/**
 * 流程历史提交记录
 * <AUTHOR>
 */
@Entity
@Table(name = "ACT_HI_TASKRCD")
@SequenceGenerator(name = "ActHiTaskrcd", sequenceName = "ACT_HI_TASKRCD_SEQ", allocationSize = 1)
@NamedQueries({
    @NamedQuery(name = "ActHiTaskrcd.findByInstanceId", query = "SELECT t FROM ActHiTaskrcd t WHERE t.procInstId=:procInstId ORDER BY t.dealTime DESC")
})
public class ActHiTaskrcd implements java.io.Serializable {

	private static final long serialVersionUID = 7191234713906029492L;
	private Integer rid;
	private String taskDefKey;
	private String taskId;
	private String procInstId;
	private Date dealTime = new Date();
	private String dealAdvice;
	private String userId;
	private Integer trustUserId;
	private String actOptType;
	/** 提交给哪些用户 */
	private String nextUserIds;
	
	public ActHiTaskrcd() {
	}

	public ActHiTaskrcd(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ActHiTaskrcd")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@Column(name = "TASK_DEF_KEY")
	public String getTaskDefKey() {
		return taskDefKey;
	}

	public void setTaskDefKey(String taskDefKey) {
		this.taskDefKey = taskDefKey;
	}

	@Column(name = "TASK_ID")
	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	@Column(name = "PROC_INST_ID")
	public String getProcInstId() {
		return procInstId;
	}

	public void setProcInstId(String procInstId) {
		this.procInstId = procInstId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "DEAL_TIME")
	public Date getDealTime() {
		return dealTime;
	}

	public void setDealTime(Date dealTime) {
		this.dealTime = dealTime;
	}

	@Column(name = "DEAL_ADVICE")
	public String getDealAdvice() {
		return dealAdvice;
	}

	public void setDealAdvice(String dealAdvice) {
		this.dealAdvice = dealAdvice;
	}

	@Column(name = "USER_ID")
	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	@Column(name = "TRUST_USER_ID")
	public Integer getTrustUserId() {
		return trustUserId;
	}

	public void setTrustUserId(Integer trustUserId) {
		this.trustUserId = trustUserId;
	}

    @Column(name = "ACT_OPT_TYPE")
	public String getActOptType() {
		return actOptType;
	}

	public void setActOptType(String actOptType) {
		this.actOptType = actOptType;
	}
	
	@Column(name = "NEXT_USER_IDS")
	public String getNextUserIds() {
		return nextUserIds;
	}

	public void setNextUserIds(String nextUserIds) {
		this.nextUserIds = nextUserIds;
	}

}