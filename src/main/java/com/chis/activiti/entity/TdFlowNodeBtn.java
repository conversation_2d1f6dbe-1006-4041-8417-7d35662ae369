package com.chis.activiti.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.chis.activiti.enumn.FlowBtnType;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "TD_FLOW_NODE_BTN")
@SequenceGenerator(name = "TdFlowNodeBtn", sequenceName = "TD_FLOW_NODE_BTN_SEQ", allocationSize = 1)
public class TdFlowNodeBtn implements java.io.Serializable {

	private static final long serialVersionUID = 7331477482195005403L;
	private Integer rid;
	private TdFlowNode tdFlowNode;
	private FlowBtnType flowBtnType;
	private String flowBtnName;
	
	private boolean disp = false;
	
	public TdFlowNodeBtn() {
	}

	public TdFlowNodeBtn(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowNodeBtn")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "FLOW_NODE_ID" )
	public TdFlowNode getTdFlowNode() {
		return tdFlowNode;
	}

	public void setTdFlowNode(TdFlowNode tdFlowNode) {
		this.tdFlowNode = tdFlowNode;
	}

	@Enumerated
	@Column(name = "FLOW_BTN_TYPE")
	public FlowBtnType getFlowBtnType() {
		return flowBtnType;
	}

	public void setFlowBtnType(FlowBtnType flowBtnType) {
		this.flowBtnType = flowBtnType;
	}

	@Column(name = "FLOW_BTN_NAME")
	public String getFlowBtnName() {
		return flowBtnName;
	}

	public void setFlowBtnName(String flowBtnName) {
		this.flowBtnName = flowBtnName;
	}

	@Transient
	public boolean isDisp() {
		return disp;
	}

	public void setDisp(boolean disp) {
		this.disp = disp;
	}
}