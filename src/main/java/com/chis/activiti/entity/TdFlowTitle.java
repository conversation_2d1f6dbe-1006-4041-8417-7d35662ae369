package com.chis.activiti.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * 流程标题模板
 * <AUTHOR>
 */
@Entity
@Table(name = "TD_FLOW_TITLE")
@SequenceGenerator(name = "TdFlowTitle", sequenceName = "TD_FLOW_TITLE_SEQ", allocationSize = 1)
@NamedQueries({
    @NamedQuery(name = "TdFlowTitle.findByDefId", query = "SELECT t FROM TdFlowTitle t WHERE t.tdFlowDef.rid=:defId ORDER BY t.createDate DESC")
})
public class TdFlowTitle implements java.io.Serializable {

	private static final long serialVersionUID = 8840034881533099838L;

	private Integer rid;
	private TdFlowDef tdFlowDef;
	private String templateName;
	private String templateContent;
	private Integer isDefault;
	private Integer state;
	private Date createDate = new Date();
	private Integer createManid;
	/**解析后的语句*/
	private String afterAnalyContent;

	public TdFlowTitle() {
	}

	public TdFlowTitle(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowTitle")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@ManyToOne
	@JoinColumn(name = "DEF_ID")
	public TdFlowDef getTdFlowDef() {
		return tdFlowDef;
	}

	public void setTdFlowDef(TdFlowDef tdFlowDef) {
		this.tdFlowDef = tdFlowDef;
	}

	@Column(name = "TEMPLATE_NAME")
	public String getTemplateName() {
		return templateName;
	}

	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}

	@Column(name = "TEMPLATE_CONTENT")
	public String getTemplateContent() {
		return templateContent;
	}

	public void setTemplateContent(String templateContent) {
		this.templateContent = templateContent;
	}

	@Column(name = "IS_DEFAULT")
	public Integer getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(Integer isDefault) {
		this.isDefault = isDefault;
	}

	@Column(name = "STATE")
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Transient
	public String getAfterAnalyContent() {
		return afterAnalyContent;
	}

	public void setAfterAnalyContent(String afterAnalyContent) {
		this.afterAnalyContent = afterAnalyContent;
	}

}