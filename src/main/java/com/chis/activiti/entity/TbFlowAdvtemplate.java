package com.chis.activiti.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * 流程意见模板
 * <AUTHOR>
 */
@Entity
@Table(name = "TB_FLOW_ADVTEMPLATE")
@SequenceGenerator(name = "TbFlowAdvtemplate", sequenceName = "TB_FLOW_ADVTEMPLATE_SEQ", allocationSize = 1)
@NamedQueries({
    @NamedQuery(name = "TbFlowAdvtemplate.findByNodeId", query = "SELECT t FROM TbFlowAdvtemplate t WHERE t.tdFlowNode.rid=:nodeId ORDER BY t.createDate DESC")
})
public class TbFlowAdvtemplate implements java.io.Serializable {

	private static final long serialVersionUID = 8840034881533099838L;

	private Integer rid;
	private TdFlowNode tdFlowNode;
	private String templateName;
	private String templateContent;
	private Short isDefault = Short.valueOf("1");
	private Short state = Short.valueOf("1");
	private Date createDate = new Date();
	private Integer createManid;
	/**解析后的语句*/
	private String afterAnalyContent;

	public TbFlowAdvtemplate() {
	}

	public TbFlowAdvtemplate(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbFlowAdvtemplate")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@ManyToOne
	@JoinColumn(name = "FLOW_NODEID")
	public TdFlowNode getTdFlowNode() {
		return tdFlowNode;
	}

	public void setTdFlowNode(TdFlowNode tdFlowNode) {
		this.tdFlowNode = tdFlowNode;
	}

	@Column(name = "TEMPLATE_NAME")
	public String getTemplateName() {
		return templateName;
	}

	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}

	@Column(name = "TEMPLATE_CONTENT")
	public String getTemplateContent() {
		return templateContent;
	}

	public void setTemplateContent(String templateContent) {
		this.templateContent = templateContent;
	}

	@Column(name = "IS_DEFAULT")
	public Short getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(Short isDefault) {
		this.isDefault = isDefault;
	}

	@Column(name = "STATE")
	public Short getState() {
		return state;
	}

	public void setState(Short state) {
		this.state = state;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Transient
	public String getAfterAnalyContent() {
		return afterAnalyContent;
	}

	public void setAfterAnalyContent(String afterAnalyContent) {
		this.afterAnalyContent = afterAnalyContent;
	}

}