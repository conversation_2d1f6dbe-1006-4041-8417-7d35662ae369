package com.chis.activiti.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.chis.modules.system.entity.TdFormDef;
import com.chis.modules.system.entity.TdTempmetaType;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "TD_FLOW_DEF")
@SequenceGenerator(name = "TdFlowDef", sequenceName = "TD_FLOW_DEF_SEQ", allocationSize = 1)
public class TdFlowDef implements java.io.Serializable {

	private static final long serialVersionUID = -4360716848028618843L;
	private Integer rid;
	private TdFlowType tdFlowType;
	private String defName;
	private String actDefId;
	private String defDesc;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	private Integer defVersion;
	private Integer nums;
	private String zipPath;
	private String zipName;

	private Short ifDynaForm;
	private TdFormDef tdFormDef;
	private Integer dynaFormId;
	private Integer isDispapp;
	private Integer isDispPro;
	private boolean selected = Boolean.FALSE;
	@OrderBy(value = "nums")
	private List<TdFlowNode> tdFlowNodeList = new ArrayList<TdFlowNode>(0);
	
	public TdFlowDef() {
	}

	public TdFlowDef(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowDef")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "TPYE_ID")
	public TdFlowType getTdFlowType() {
		return this.tdFlowType;
	}

	public void setTdFlowType(TdFlowType tdFlowType) {
		this.tdFlowType = tdFlowType;
	}

	@Column(name = "DEF_NAME" , length = 100)
	public String getDefName() {
		return this.defName;
	}

	public void setDefName(String defName) {
		this.defName = defName;
	}

	@Column(name = "ACT_DEF_ID" , length = 64)
	public String getActDefId() {
		return this.actDefId;
	}

	public void setActDefId(String actDefId) {
		this.actDefId = actDefId;
	}

	@Column(name = "DEF_DESC", length = 200)
	public String getDefDesc() {
		return this.defDesc;
	}

	public void setDefDesc(String defDesc) {
		this.defDesc = defDesc;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdFlowDef")
	public List<TdFlowNode> getTdFlowNodeList() {
		return tdFlowNodeList;
	}

	public void setTdFlowNodeList(List<TdFlowNode> tdFlowNodeList) {
		this.tdFlowNodeList = tdFlowNodeList;
	}

	@Column(name = "DEF_VERSION")
	public Integer getDefVersion() {
		return defVersion;
	}

	public void setDefVersion(Integer defVersion) {
		this.defVersion = defVersion;
	}

	@Column(name = "NUMS")
	public Integer getNums() {
		return nums;
	}

	public void setNums(Integer nums) {
		this.nums = nums;
	}

	@Column(name = "ZIP_PATH")
	public String getZipPath() {
		return zipPath;
	}

	public void setZipPath(String zipPath) {
		this.zipPath = zipPath;
	}

	@Column(name = "ZIP_NAME")
	public String getZipName() {
		return zipName;
	}

	public void setZipName(String zipName) {
		this.zipName = zipName;
	}

	@Column(name = "IF_DYNA_FORM")
	public Short getIfDynaForm() {
		return ifDynaForm;
	}

	public void setIfDynaForm(Short ifDynaForm) {
		this.ifDynaForm = ifDynaForm;
	}

	@ManyToOne
	@JoinColumn(name = "DYNA_FORM_ID")
	public TdFormDef getTdFormDef() {
		return tdFormDef;
	}

	public void setTdFormDef(TdFormDef tdFormDef) {
		this.tdFormDef = tdFormDef;
	}

	@Transient
	public Integer getDynaFormId() {
		return dynaFormId;
	}

	public void setDynaFormId(Integer dynaFormId) {
		this.dynaFormId = dynaFormId;
	}

	@Transient
	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}
	
	@Column(name = "IS_DISPAPP")	
	public Integer getIsDispapp() {
		return isDispapp;
	}

	public void setIsDispapp(Integer isDispapp) {
		this.isDispapp = isDispapp;
	}
	
    @Column(name = "IS_DISP_PRO")    
    public Integer getIsDispPro() {
        return isDispPro;
    }

    public void setIsDispPro(Integer isDispPro) {
        this.isDispPro = isDispPro;
    }

}