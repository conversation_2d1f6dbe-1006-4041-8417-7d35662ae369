package com.chis.activiti.entity;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * TdFlowTrustTask entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TD_FLOW_TRUST_TASK")
@SequenceGenerator(name = "TdFlowTrustTask", sequenceName = "TD_FLOW_TRUST_TASK_SEQ", allocationSize = 1)
public class TdFlowTrustTask implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 4848317493716050137L;
	private Integer rid;
	private TdFlowTrust tdFlowTrust;
	private String taskId;

	// Constructors

	/** default constructor */
	public TdFlowTrustTask() {
	}

	/** full constructor */
	public TdFlowTrustTask(Integer rid, TdFlowTrust tdFlowTrust,
			String taskId) {
		this.rid = rid;
		this.tdFlowTrust = tdFlowTrust;
		this.taskId = taskId;
	}

	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowTrustTask")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "TRUST_ID" )
	public TdFlowTrust getTdFlowTrust() {
		return this.tdFlowTrust;
	}

	public void setTdFlowTrust(TdFlowTrust tdFlowTrust) {
		this.tdFlowTrust = tdFlowTrust;
	}

	@Column(name = "TASK_ID" , length = 64)
	public String getTaskId() {
		return this.taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

}