package com.chis.activiti.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.chis.modules.system.enumn.SystemType;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "TD_FLOW_NODE_PAGE")
@SequenceGenerator(name = "TdFlowNodePageSeq", sequenceName = "TD_FLOW_NODE_PAGE_SEQ", allocationSize = 1)
@NamedQueries({
		@NamedQuery(name = "TdFlowNodePage.findAll", query = "SELECT t FROM TdFlowNodePage t where t.ifReveal = 1 order by t.systemType,t.pageDesc"),
		@NamedQuery(name = "TdFlowNodePage.findByPageCode", query = "SELECT t FROM TdFlowNodePage t WHERE t.pageCode=:pagecode")
})
public class TdFlowNodePage implements java.io.Serializable {

	private static final long serialVersionUID = 7331477482195005403L;
	private Integer rid;
	private SystemType systemType;
	private String pageCode;
	private String pageDesc;
	private String pageUrl;
	private Short ifReveal = 1;
	private Integer num;
    private String pageParm;



	public TdFlowNodePage() {
	}

	public TdFlowNodePage(Integer rid) {
		this.rid = rid;
	}

	public TdFlowNodePage(SystemType systemType,String pageCode, String pageDesc, String pageUrl,Integer num ) {
		this.pageCode = pageCode;
		this.pageDesc = pageDesc;
		this.pageUrl = pageUrl;
		this.systemType = systemType;
		this.num = num;
	}

    public TdFlowNodePage(SystemType systemType, String pageCode, String pageDesc, String pageUrl, Integer num, String pageParm) {
        this.systemType = systemType;
        this.pageCode = pageCode;
        this.pageDesc = pageDesc;
        this.pageUrl = pageUrl;
        this.num = num;
        this.pageParm = pageParm;
    }

    @Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowNodePageSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Enumerated
	@Column(name = "PARAM_TYPE")
	public SystemType getSystemType() {
		return systemType;
	}

	public void setSystemType(SystemType systemType) {
		this.systemType = systemType;
	}

	@Column(name = "PAGE_CODE")
	public String getPageCode() {
		return pageCode;
	}

	public void setPageCode(String pageCode) {
		this.pageCode = pageCode;
	}

	@Column(name = "PAGE_DESC")
	public String getPageDesc() {
		return pageDesc;
	}

	public void setPageDesc(String pageDesc) {
		this.pageDesc = pageDesc;
	}

	@Column(name = "PAGE_URL")
	public String getPageUrl() {
		return pageUrl;
	}

	public void setPageUrl(String pageUrl) {
		this.pageUrl = pageUrl;
	}

	@Column(name = "IF_REVEAL")
	public Short getIfReveal() {
		return ifReveal;
	}

	public void setIfReveal(Short ifReveal) {
		this.ifReveal = ifReveal;
	}

	@Column(name = "NUM")
	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

    @Column(name = "PAGE_PARAM")
    public String getPageParm() {
        return pageParm;
    }

    public void setPageParm(String pageParm) {
        this.pageParm = pageParm;
    }
}