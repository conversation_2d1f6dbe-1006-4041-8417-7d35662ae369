package com.chis.activiti.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Entity
@Table(name = "TD_TEST_VACC")
@SequenceGenerator(name = "TdTestVacc", sequenceName = "TD_TEST_VACC_SEQ", allocationSize = 1)
public class TdTestVacc implements java.io.Serializable, Cloneable{
	
	private static final long serialVersionUID = 6088104911697902948L;
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTestVacc")
	private Integer rid;
	@Column(name = "VACC_DAY")
	private Integer vaccDay;
	@Column(name = "VACC_REASON")
	private String vaccReason;
	@Column(name = "VACC_MAN")
	private String vaccMan;
	
	public TdTestVacc() {
	}
	
	public TdTestVacc(Integer rid) {
		this.rid = rid;
	}
	
	@Override
	public Object clone() throws CloneNotSupportedException {
		return super.clone();
	}

	public Integer getRid() {
		return rid;
	}
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	public Integer getVaccDay() {
		return vaccDay;
	}
	public void setVaccDay(Integer vaccDay) {
		this.vaccDay = vaccDay;
	}
	public String getVaccReason() {
		return vaccReason;
	}
	public void setVaccReason(String vaccReason) {
		this.vaccReason = vaccReason;
	}
	public String getVaccMan() {
		return vaccMan;
	}
	public void setVaccMan(String vaccMan) {
		this.vaccMan = vaccMan;
	}
	
}
