package com.chis.activiti.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "TD_FLOW_RULE_NODE")
@SequenceGenerator(name = "TdFlowRuleNode", sequenceName = "TD_FLOW_RULE_NODE_SEQ", allocationSize = 1)
public class TdFlowRuleNode implements java.io.Serializable {

	private static final long serialVersionUID = -4831504593166558338L;
	private Integer rid;
	private TdFlowRule tdFlowRule;
	private TdFlowNode tdFlowNode;
	private String selectIds;
	private String selectNames;

	public TdFlowRuleNode() {
	}

	public TdFlowRuleNode(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowRuleNode")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@ManyToOne
	@JoinColumn(name = "FLOW_ROLE_ID")
	public TdFlowRule getTdFlowRule() {
		return tdFlowRule;
	}

	public void setTdFlowRule(TdFlowRule tdFlowRule) {
		this.tdFlowRule = tdFlowRule;
	}

	@ManyToOne
	@JoinColumn(name = "FLOW_NODE_ID")
	public TdFlowNode getTdFlowNode() {
		return tdFlowNode;
	}

	public void setTdFlowNode(TdFlowNode tdFlowNode) {
		this.tdFlowNode = tdFlowNode;
	}
	
	@Column(name = "SELECT_IDS")
	public String getSelectIds() {
		return selectIds;
	}

	public void setSelectIds(String selectIds) {
		this.selectIds = selectIds;
	}

	@Column(name = "SELECT_NAMES")
	public String getSelectNames() {
		return selectNames;
	}

	public void setSelectNames(String selectNames) {
		this.selectNames = selectNames;
	}

}