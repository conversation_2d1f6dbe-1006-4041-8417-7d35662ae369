package com.chis.activiti.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * TdFlowTrustDef entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TD_FLOW_TRUST_DEF")
@SequenceGenerator(name = "TdFlowTrustDef", sequenceName = "TD_FLOW_TRUST_DEF_SEQ", allocationSize = 1)
public class TdFlowTrustDef implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = -7954149429735506533L;
	private Integer rid;
	private TdFlowTrust tdFlowTrust;
	private String defId;

	// Constructors

	/** default constructor */
	public TdFlowTrustDef() {
	}

	/** full constructor */
	public TdFlowTrustDef(Integer rid, TdFlowTrust tdFlowTrust, String defId) {
		this.rid = rid;
		this.tdFlowTrust = tdFlowTrust;
		this.defId = defId;
	}

	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowTrustDef")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "TRUST_ID" )
	public TdFlowTrust getTdFlowTrust() {
		return this.tdFlowTrust;
	}

	public void setTdFlowTrust(TdFlowTrust tdFlowTrust) {
		this.tdFlowTrust = tdFlowTrust;
	}

	@Column(name = "DEF_ID" , length = 64)
	public String getDefId() {
		return this.defId;
	}

	public void setDefId(String defId) {
		this.defId = defId;
	}

}