package com.chis.activiti.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "TD_FLOW_ROLE_USER")
@SequenceGenerator(name = "TdFlowRoleUser", sequenceName = "TD_FLOW_ROLE_USER_SEQ", allocationSize = 1)
public class TdFlowRoleUser implements java.io.Serializable {

	private static final long serialVersionUID = -4831504593166558338L;
	private Integer rid;
	private Integer userInfoId;
	private TdFlowRole tdFlowRole;

	public TdFlowRoleUser() {
	}

	public TdFlowRoleUser(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowRoleUser")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@Column(name = "USER_INFO_ID")
	public Integer getUserInfoId() {
		return userInfoId;
	}

	public void setUserInfoId(Integer userInfoId) {
		this.userInfoId = userInfoId;
	}
	
	@ManyToOne
	@JoinColumn(name = "FLOW_ROLE_ID")
	public TdFlowRole getTdFlowRole() {
		return tdFlowRole;
	}

	public void setTdFlowRole(TdFlowRole tdFlowRole) {
		this.tdFlowRole = tdFlowRole;
	}

}