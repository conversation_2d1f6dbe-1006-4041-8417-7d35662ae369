package com.chis.activiti.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import org.hibernate.validator.constraints.NotBlank;

/**
 * 流程类型维护
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "TD_FLOW_TYPE")
@SequenceGenerator(name = "TdFlowType", sequenceName = "TD_FLOW_TYPE_SEQ", allocationSize = 1)
public class TdFlowType implements java.io.Serializable {

	private static final long serialVersionUID = 5614045136338855829L;
	private Integer rid;
	private String typeName;
	private String typeDesc;

	private TdFlowType parent;
	private Integer nums;
	private String picPath;

	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private List<TdFlowDef> tdFlowDefList = new ArrayList<TdFlowDef>(0);
	private List<TdFlowType> tdFlowTypeList = new ArrayList<TdFlowType>(0);
	private boolean selected = Boolean.FALSE;
	private String mobilePicUrl;

	public TdFlowType() {
	}

	public TdFlowType(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowType")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@NotBlank(message = "类型名称不允许为空！")
	@Column(name = "TYPE_NAME" , length = 100)
	public String getTypeName() {
		return this.typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	@Column(name = "TYPE_DESC", length = 200)
	public String getTypeDesc() {
		return this.typeDesc;
	}

	public void setTypeDesc(String typeDesc) {
		this.typeDesc = typeDesc;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdFlowType")
	public List<TdFlowDef> getTdFlowDefList() {
		return tdFlowDefList;
	}

	public void setTdFlowDefList(List<TdFlowDef> tdFlowDefList) {
		this.tdFlowDefList = tdFlowDefList;
	}

	@ManyToOne
	@JoinColumn(name = "PARENT_ID")
	public TdFlowType getParent() {
		return parent;
	}

	public void setParent(TdFlowType parent) {
		this.parent = parent;
	}

	@Column(name = "NUMS")
	public Integer getNums() {
		return nums;
	}

	public void setNums(Integer nums) {
		this.nums = nums;
	}

	@Column(name = "PIC_PATH")
	public String getPicPath() {
		return picPath;
	}

	public void setPicPath(String picPath) {
		this.picPath = picPath;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "parent")
	public List<TdFlowType> getTdFlowTypeList() {
		return tdFlowTypeList;
	}

	public void setTdFlowTypeList(List<TdFlowType> tdFlowTypeList) {
		this.tdFlowTypeList = tdFlowTypeList;
	}

	@Transient
	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}

	@Column(name = "MOBILE_PIC_URL")	
	public String getMobilePicUrl() {
		return mobilePicUrl;
	}

	public void setMobilePicUrl(String mobilePicUrl) {
		this.mobilePicUrl = mobilePicUrl;
	}

}