package com.chis.activiti.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.chis.modules.system.entity.TsTxrpt;

/**
 * 通讯接收事件流转关联表
 *
 * <AUTHOR>
 * @创建时间 2014-11-25
 */
@Entity
@Table(name = "TD_EM_TXRPT_CASE")
@SequenceGenerator(name = "TdEmTxrptCase_Seq", sequenceName = "TD_EM_TXRPT_CASE_SEQ", allocationSize = 1)
public class TdEmTxrptCase implements java.io.Serializable {
	
	private static final long serialVersionUID = 6750629536730180529L;
	private Integer rid;
	//ACTIVITI_TASK_ID
	private Integer activitiTaskId;
	private Integer targetTaskId;
	private TsTxrpt tsTxrpt;
	
	// Constructors
	
	/** default constructor */
	public TdEmTxrptCase() {
	}
	
	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdEmTxrptCase_Seq")
	public Integer getRid() {
		return this.rid;
	}
	
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@Column(name = "ACTIVITI_TASK_ID")
	public Integer getActivitiTaskId() {
		return activitiTaskId;
	}

	public void setActivitiTaskId(Integer activitiTaskId) {
		this.activitiTaskId = activitiTaskId;
	}
	
	@Column(name = "TARGET_TASK_ID")
	public Integer getTargetTaskId() {
		return targetTaskId;
	}

	public void setTargetTaskId(Integer targetTaskId) {
		this.targetTaskId = targetTaskId;
	}

	@ManyToOne
	@JoinColumn(name = "RPT_ID" )
	public TsTxrpt getTsTxrpt() {
		return tsTxrpt;
	}

	public void setTsTxrpt(TsTxrpt tsTxrpt) {
		this.tsTxrpt = tsTxrpt;
	}
	
}