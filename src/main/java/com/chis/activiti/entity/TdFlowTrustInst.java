package com.chis.activiti.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * TdFlowTrustInst entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TD_FLOW_TRUST_INST")
@SequenceGenerator(name = "TdFlowTrustInst", sequenceName = "TD_FLOW_TRUST_INST_SEQ", allocationSize = 1)
public class TdFlowTrustInst implements java.io.Serializable {

	// Fields

	/**
	 * 
	 */
	private static final long serialVersionUID = 1841260053011619881L;
	private Integer rid;
	private TdFlowTrust tdFlowTrust;
	private String procInstId;

	// Constructors

	/** default constructor */
	public TdFlowTrustInst() {
	}

	/** full constructor */
	public TdFlowTrustInst(Integer rid, TdFlowTrust tdFlowTrust,
			String procInstId) {
		this.rid = rid;
		this.tdFlowTrust = tdFlowTrust;
		this.procInstId = procInstId;
	}

	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFlowTrustInst")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "TRUST_ID" )
	public TdFlowTrust getTdFlowTrust() {
		return this.tdFlowTrust;
	}

	public void setTdFlowTrust(TdFlowTrust tdFlowTrust) {
		this.tdFlowTrust = tdFlowTrust;
	}

	@Column(name = "PROC_INST_ID" , length = 64)
	public String getProcInstId() {
		return this.procInstId;
	}

	public void setProcInstId(String procInstId) {
		this.procInstId = procInstId;
	}

}