package com.chis.activiti.javabean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.chis.activiti.entity.TdFlowNode;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 流程多节点处理的各节点的待办人选择的PO
 * <AUTHOR> 2014-12-24
 */
public class MutiNodeDealBean implements Serializable {

	private static final long serialVersionUID = -1022769475897430312L;
	/**节点对象*/
	private TdFlowNode tdFlowNode;
	/**节点的待办人集合*/
	private List<TsUserInfo> userList = new ArrayList<TsUserInfo>(0);
	/**节点的选中的待办人集合*/
	private List<String> selectUserIdsList = new ArrayList<String>(0);
	
	public MutiNodeDealBean() {
	}
	public MutiNodeDealBean(TdFlowNode tdFlowNode) {
		this.tdFlowNode = tdFlowNode;
	}
	public TdFlowNode getTdFlowNode() {
		return tdFlowNode;
	}
	public void setTdFlowNode(TdFlowNode tdFlowNode) {
		this.tdFlowNode = tdFlowNode;
	}
	public List<TsUserInfo> getUserList() {
		return userList;
	}
	public void setUserList(List<TsUserInfo> userList) {
		this.userList = userList;
	}
	public List<String> getSelectUserIdsList() {
		return selectUserIdsList;
	}
	public void setSelectUserIdsList(List<String> selectUserIdsList) {
		this.selectUserIdsList = selectUserIdsList;
	}
}
