package com.chis.activiti.javabean;

import java.io.Serializable;

/**
 * 流程规则的数据字典条件
 * <AUTHOR>
 */
public class DirectoryCondition implements Serializable{

	private static final long serialVersionUID = 622699538603024986L;
	/**当前用户ID*/
	private Integer userId;
	/**当前节点ID*/
	private Integer nodeId;
	
	public DirectoryCondition() {
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public Integer getNodeId() {
		return nodeId;
	}

	public void setNodeId(Integer nodeId) {
		this.nodeId = nodeId;
	}
	
}
