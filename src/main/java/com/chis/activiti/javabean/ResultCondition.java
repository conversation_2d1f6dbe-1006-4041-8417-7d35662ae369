package com.chis.activiti.javabean;

import java.io.Serializable;

/**
 * 流程结果查询条件对象
 * 
 * 说明：当前节点,表示即将要跳转到的节点
 *      上一节点，表示正在处理的节点、正准备跳转的节点
 * 
 * <AUTHOR>
 */
public class ResultCondition implements Serializable, Cloneable{
	
	private static final long serialVersionUID = 6351509061431854280L;
	/**已选的ids，如已选的部门，科室等*/
	private String selectIds;
	/**当前节点ID*/
	private String nodeId;
	/**当前流程实例ID*/
	private String processInstanceId;
	/**当前登录用户ID*/
	private Integer userId;
	/**json形式的查询条件*/
	private String json;
	

	public ResultCondition() {
		
	}
	
	@Override
	public Object clone() throws CloneNotSupportedException {
		return super.clone();
	}

	public String getSelectIds() {
		return selectIds;
	}

	public void setSelectIds(String selectIds) {
		this.selectIds = selectIds;
	}

	public String getNodeId() {
		return nodeId;
	}

	public void setNodeId(String nodeId) {
		this.nodeId = nodeId;
	}

	public String getProcessInstanceId() {
		return processInstanceId;
	}

	public void setProcessInstanceId(String processInstanceId) {
		this.processInstanceId = processInstanceId;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getJson() {
		return json;
	}

	public void setJson(String json) {
		this.json = json;
	}

}
