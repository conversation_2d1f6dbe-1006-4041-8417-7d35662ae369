package com.chis.activiti.javabean;

import java.io.Serializable;

/**
 * 流程中业务模块提供的按钮Bean
 * <AUTHOR>
 */
public class BusinessButton implements Serializable{

	private static final long serialVersionUID = -4016703806040989543L;
	/**按钮的ID*/
	private String btnId;
	/**按钮的名称*/
	private String btnName;
	/**按钮是否显示*/
	private boolean display = true;
	/**按钮对应的Web组件对象*/
	private Object btnObject;
	
	public BusinessButton() {
	}
	public BusinessButton(String btnId, String btnName) {
		this.btnId = btnId;
		this.btnName = btnName;
	}
	public String getBtnId() {
		return btnId;
	}
	public void setBtnId(String btnId) {
		this.btnId = btnId;
	}
	public String getBtnName() {
		return btnName;
	}
	public void setBtnName(String btnName) {
		this.btnName = btnName;
	}
	public Object getBtnObject() {
		return btnObject;
	}
	public void setBtnObject(Object btnObject) {
		this.btnObject = btnObject;
	}
	public boolean isDisplay() {
		return display;
	}
	public void setDisplay(boolean display) {
		this.display = display;
	}
}
