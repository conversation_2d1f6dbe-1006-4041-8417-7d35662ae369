package com.chis.activiti.javabean;

import java.io.Serializable;

/**
 * 用于业务模块存储rid的POJO 
 * <AUTHOR> 2014-12-24
 */
public class BusinessIdBean implements Serializable{
	private static final long serialVersionUID = -9031850877426025439L;
	/**业务主键*/
	private String rid;
	/**需要存储的其他字段*/
	private Object object;
	
	public BusinessIdBean() {
	}
	public BusinessIdBean(String rid) {
		this.rid = rid;
	}
	public String getRid() {
		return rid;
	}
	public void setRid(String rid) {
		this.rid = rid;
	}
	public Object getObject() {
		return object;
	}
	public void setObject(Object object) {
		this.object = object;
	}
}
