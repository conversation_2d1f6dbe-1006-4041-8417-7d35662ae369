package com.chis.activiti.utils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.entity.TdFlowRuleNode;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TbSysEmp;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 根据流程规则查询待办人的工具方法
 *
 * <AUTHOR>
 * @createDate 2014年9月3日下午3:45:00
 */
public class ActivitiRuleUtil {

	private ActivitiRuleUtil() {}
	
	/**
	 * 根据流程实例ID，查询流程发起者
	 * @param instanceId 流程实例ID 
	 * @param em 实体管理器
	 * @return 用户对象
	 */
	public static TsUserInfo findStartUser(String instanceId, EntityManager em) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T1.RID AS USERID, T1.USERNAME, T3.RID AS OFFID, T3.OFFICENAME, T5.RID AS UNITID, T5.UNIT_SIMPNAME ");
		sb.append(" FROM TS_USER_INFO T1 ");
		sb.append(" INNER JOIN TB_SYS_EMP T2 ON T1.EMP_ID = T2.RID ");
		sb.append(" INNER JOIN TS_OFFICE T3 ON T2.DEPT_ID = T3.RID AND T3.IF_REVEAL = '1' ");
		sb.append(" INNER JOIN ACT_HI_PROCINST T4 ON T1.RID = T4.START_USER_ID_ ");
		sb.append(" INNER JOIN TS_UNIT T5 ON T3.UNIT_RID = T5.RID ");
		sb.append(" WHERE T1.IF_REVEAL = '1' AND T4.PROC_INST_ID_ = '").append(instanceId).append("' ");
		
		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if(null != list && list.size() > 0) {
			 Object[] o = list.get(0);
			 TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
			 t.setUsername(o[1]==null?"":o[1].toString());
			 
			 TsOffice office = new TsOffice(Integer.valueOf(o[2].toString()), o[3]==null?"":o[3].toString());
			 TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
			 unit.setUnitSimpname(o[5].toString());
			 
			 office.setTsUnit(unit);
			 t.setTsOffice(office);
			 t.setTsUnit(unit);
			 
			 return t;
		}
		return null;
	}
	
	/**
	 * 根据用户ID，查询用户对象
	 * @param userId 用户ID
	 * @param em 实体管理器
	 * @return 用户对象
	 */
	public static TsUserInfo findUser(Integer userId, EntityManager em) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T1.RID AS USERID, T1.USERNAME, T3.RID AS OFFID, T3.OFFICENAME, T5.RID AS UNITID, T5.UNIT_SIMPNAME ");
		sb.append(" FROM TS_USER_INFO T1 ");
		sb.append(" INNER JOIN TB_SYS_EMP T2 ON T1.EMP_ID = T2.RID ");
		sb.append(" INNER JOIN TS_OFFICE T3 ON T2.DEPT_ID = T3.RID AND T3.IF_REVEAL = '1' ");
		sb.append(" INNER JOIN TS_UNIT T5 ON T3.UNIT_RID = T5.RID ");
		sb.append(" WHERE T1.IF_REVEAL = '1' AND T1.RID = '").append(userId).append("' ");
		
		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if(null != list && list.size() > 0) {
			Object[] o = list.get(0);
			 TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
			 t.setUsername(o[1]==null?"":o[1].toString());

			 TsOffice office = new TsOffice(Integer.valueOf(o[2].toString()), o[3]==null?"":o[3].toString());
			 TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
			 unit.setUnitSimpname(o[5].toString());
			 
			 office.setTsUnit(unit);
			 t.setTsOffice(office);
			 t.setTsUnit(unit);
			 
			 return t;
		}
		return null;
	}
	
	/**
	 * 根据用户ID，查询用户对象
	 * @param empId 职工ID
	 * @param em 实体管理器
	 * @return 用户对象
	 */
	public static TsUserInfo findUserOfEmp(Integer empId, EntityManager em) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T1.RID AS USERID, T1.USERNAME, T3.RID AS OFFID, T3.OFFICENAME, T5.RID AS UNITID, T5.UNIT_SIMPNAME ");
		sb.append(" FROM TS_USER_INFO T1 ");
		sb.append(" INNER JOIN TB_SYS_EMP T2 ON T1.EMP_ID = T2.RID ");
		sb.append(" INNER JOIN TS_OFFICE T3 ON T2.DEPT_ID = T3.RID AND T3.IF_REVEAL = '1' ");
		sb.append(" INNER JOIN TS_UNIT T5 ON T3.UNIT_RID = T5.RID ");
		sb.append(" WHERE T1.IF_REVEAL = '1' AND T2.RID = '").append(empId).append("' ");
		
		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if(null != list && list.size() > 0) {
			Object[] o = list.get(0);
			 TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
			 t.setUsername(o[1]==null?"":o[1].toString());
			 
			 TsOffice office = new TsOffice(Integer.valueOf(o[2].toString()), o[3]==null?"":o[3].toString());
			 TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
			 unit.setUnitSimpname(o[5].toString());
			 
			 office.setTsUnit(unit);
			 t.setTsOffice(office);
			 t.setTsUnit(unit);
			 
			 return t;
		}
		return null;
	}
	
	/**
	 * 根据用户ID，查询其领导，如果是普通员工，找科长，
	 * 如果是科长找分管领导，如果是分管领导，返回Null
	 * @param userId 用户ID
	 * @param em 实体管理器
	 * @return 用户对象
	 */
	public static TsUserInfo findLeaderOfUser(Integer userId, EntityManager em) {
		TsUserInfo user = em.find(TsUserInfo.class, userId);
		TbSysEmp emp = user.getTbSysEmp();
		if(null != emp) {
			TsOffice office = emp.getTsOffice();
			if(null != office) {
				/**
				 * 先找科长和分管领导
				 */
				TbSysEmp kz = office.getTbSysEmpByDeptLeaderId();	//科长
				TbSysEmp leader = office.getTbSysEmpByManageManid();	//分管领导
				
				if(null != leader) {
					/**
					 * 自己就是分管领导，则返回null
					 */
					if(leader.getRid().equals(emp.getRid())) {
						return null;
					}else {
						/**
						 * 没有科长，就返回Null
						 */
						if(null != kz) {
							/**
							 * 自己是科长，就返回分管领导
							 * 自己不是科长，就返回科长
							 */
							if(!kz.getRid().equals(emp.getRid())) {
								return findUserOfEmpJz(kz.getRid(), em);
							}else {
								return findUserOfEmpJz(leader.getRid(), em);
							}
						}else {
							return null;
						}
					}
				}else {
					if(null != kz) {
						/**
						 * 分管领导为空，自己又不是科长，则返回科长;
						 * 			      自己是科长，返回null
						 */
						if(!kz.getRid().equals(emp.getRid())) {
							return findUserOfEmpJz(kz.getRid(), em);
						}
					}
				}
			}
		}
		return null;
	}
	
	/**
	 * 根据用户ID，查询其分管领导,没有就返回Null
	 * @param userId 用户ID
	 * @param em 实体管理器
	 * @return 用户对象
	 */
	public static TsUserInfo findBigLeaderOfUser(Integer userId, EntityManager em) {
		TsUserInfo user = em.find(TsUserInfo.class, userId);
		TbSysEmp emp = user.getTbSysEmp();
		if(null != emp) {
			TsOffice office = emp.getTsOffice();
			if(null != office) {
				TbSysEmp leader = office.getTbSysEmpByManageManid();	//分管领导
				
				if(null != leader) {
					return findUserOfEmpJz(leader.getRid(), em);
				}
			}
		}
		return null;
	}
	
	/**
	 * 查询某个科室里面的用户
	 * @param officeId 科室ID
	 * @param em 实体管理器
	 * @return 用户对象
	 */
	public static List<TsUserInfo> findUsersOfOffice(Integer officeId, EntityManager em) {
		List<TsUserInfo> rtnList = new ArrayList<TsUserInfo>();
		
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T1.RID AS USERID, T1.USERNAME, T3.RID AS OFFID, T3.OFFICENAME, T5.RID AS UNITID, T5.UNIT_SIMPNAME ");
		sb.append(" FROM TS_USER_INFO T1 ");
		sb.append(" INNER JOIN TB_SYS_EMP T2 ON T1.EMP_ID = T2.RID ");
		sb.append(" INNER JOIN TS_OFFICE T3 ON T2.DEPT_ID = T3.RID AND T3.IF_REVEAL = '1' ");
		sb.append(" INNER JOIN TS_UNIT T5 ON T3.UNIT_RID = T5.RID ");
		sb.append(" WHERE T1.IF_REVEAL = '1' AND T3.RID = '").append(officeId).append("' ");
		
		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if(null != list && list.size() > 0) {
			for(Object[] o: list) {
				 TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
				 t.setUsername(o[1]==null?"":o[1].toString());
				 
				 TsOffice office = new TsOffice(Integer.valueOf(o[2].toString()), o[3]==null?"":o[3].toString());
				 TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
				 unit.setUnitSimpname(o[5].toString());
				 
				 office.setTsUnit(unit);
				 t.setTsOffice(office);
				 t.setTsUnit(unit);
				 
				 rtnList.add(t);
			}
		}
		return rtnList;
	}
	
	/**
	 * 查询该流程该节点之前的节点
	 * @param nodeId 科室ID
	 * @param em 实体管理器
	 * @return 流程节点对象集合
	 */
	public static List<TdFlowNode> findNodesBefore(Integer nodeId, EntityManager em) {
		TdFlowNode currentNode = em.find(TdFlowNode.class, nodeId);
		if(null != currentNode) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TdFlowNode t where t.tdFlowDef.rid =");
			sb.append(currentNode.getTdFlowDef().getRid());
			sb.append(" and t.nums < ").append(currentNode.getNums());
			sb.append(" order by t.nums ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}
	
	/**
	 * 查询某个节点配置的用户
	 * @param nodeIds 要查询待办人的节点的ID，可能有多个节点，以','隔开的
	 * @param condition 条件对象
	 * @param em 实体管理器
	 * @return 用户对象
	 */
	public static List<TsUserInfo> findUsersOfNode(String nodeIds, ResultCondition condition, EntityManager em) {
		List<TsUserInfo> rtnList = new ArrayList<TsUserInfo>();
		Map<Integer, TsUserInfo> map = new HashMap<Integer, TsUserInfo>();
		try {
			String[] split = nodeIds.split(",");
			
			//设置查询条件
			ResultCondition con = new ResultCondition();
			con.setProcessInstanceId(condition.getProcessInstanceId());
			con.setUserId(condition.getUserId());
			
			for(String nodeId:split) {
				StringBuilder sb = new StringBuilder(" select t from TdFlowRuleNode t where t.tdFlowNode.rid =");
				sb.append(condition.getNodeId());
				
				con.setNodeId(nodeId);
				List<TdFlowRuleNode> ruleNodeList = em.createQuery(sb.toString()).getResultList();
				if(null != ruleNodeList && ruleNodeList.size() > 0) {
					for(TdFlowRuleNode tfr: ruleNodeList) {
						con.setSelectIds(tfr.getSelectIds());
						if(StringUtils.isNotBlank(tfr.getTdFlowRule().getImpClass())) {
							IActivitiRuleService ruleService = (IActivitiRuleService)SpringContextHolder.getBean(tfr.getTdFlowRule().getImpClass()); 
							List<TsUserInfo> userList = ruleService.findMan(con, em);
							if(null != userList && userList.size() > 0) {
								for(TsUserInfo user: userList) {
									map.put(user.getRid(), user);
								}
							}
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
		
		if(null != map && map.size() > 0) {
			Collection<TsUserInfo> coll = map.values();
			if(null != coll && coll.size() > 0) {
				for(TsUserInfo user: coll) {
					rtnList.add(user);
				}
			}
		}
		return rtnList;
	}
	
	/**
	 * 根据节点ID，获取前一个节点的ID
	 * @param nodeId 节点ID
	 * @param em 实体管理器
	 * @return 前一个节点的ID
	 */
	public static String findLastNodeId(String nodeId, EntityManager em) {
		if(StringUtils.isNotBlank(nodeId)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" SELECT T2.RID ");
			sb.append(" FROM TD_FLOW_NODE T1,TD_FLOW_NODE T2 ");
			sb.append(" WHERE T1.DEF_ID = T2.DEF_ID AND T2.NUMS = (T1.NUMS-1) ");
			sb.append(" AND T1.RID = ").append(nodeId);

			List list = em.createNativeQuery(sb.toString()).getResultList();
			if(null != list && list.size() > 0) {
				return list.get(0).toString();
			}
		}
		return null;
	}
	

/**
 * +根据流程实例ID，查询流程发起者(包括兼职人员)
 * @param instanceId 流程实例ID
 * @param em 实体管理器
 * @return 用户对象
 */
public static TsUserInfo findStartUserJz(String instanceId, EntityManager em) {
	StringBuilder sb = new StringBuilder();
	sb.append("SELECT T1.RID AS USERID, T1.USERNAME, ','||LISTAGG(C.OFFICEID,',') WITHIN GROUP(ORDER BY C.OFFICEID)||',' AS OFFICEIDS ,");
	sb.append(" LISTAGG(C.OFFICENAME,',') WITHIN GROUP(ORDER BY DECODE(C.ZKS, 1, -1, C.NUM)) AS OFFICENAMES");
	sb.append(", T5.RID AS UNITID, T5.UNIT_SIMPNAME FROM TS_USER_INFO T1");
	sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
	sb.append(" WHERE B.IF_REVEAL = '1' UNION ALL SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, ");
	sb.append(" 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
	sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID WHERE C.IF_REVEAL = '1' ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
	sb.append(" INNER JOIN ACT_HI_PROCINST T4 ON T1.RID = T4.START_USER_ID_ ");
	sb.append(" INNER JOIN TS_UNIT T5 ON T1.UNIT_RID = T5.RID ");
	sb.append(" WHERE T1.IF_REVEAL = '1' AND T4.PROC_INST_ID_ = '").append(instanceId).append("' ");
	sb.append(" GROUP BY T1.RID, T1.USERNAME ,C.EMPNUM ,T5.RID,T5.UNIT_SIMPNAME,T5.UNIT_CODE");
	sb.append(" ORDER BY T5.UNIT_CODE, SUM(DECODE(C.ZKS, 1, C.NUM, NULL)) ,C.EMPNUM ");
	
	List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
	if(null != list && list.size() > 0) {
		Object[] o = list.get(0);
		 TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
		 t.setUsername(o[1]==null?"":o[1].toString());
		 
		 TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
		 unit.setUnitSimpname(o[5].toString());
		 
		 t.setOfficeIds(String.valueOf(o[2]));
		 t.setOfficeNames(String.valueOf(o[3]));
		 t.setTsUnit(unit);
		 
		 return t;
	}
	return null;
}

/**
 * +根据用户ID，查询用户对象(包括兼职人员)
 * @param userId 用户ID
 * @param em 实体管理器
 * @return 用户对象
 */
public static TsUserInfo findUserJz(Integer userId, EntityManager em) {
	StringBuilder sb = new StringBuilder();
	sb.append("SELECT T1.RID AS USERID, T1.USERNAME, ','||LISTAGG(C.OFFICEID,',') WITHIN GROUP(ORDER BY C.OFFICEID)||',' AS OFFICEIDS ,");
	sb.append(" LISTAGG(C.OFFICENAME,',') WITHIN GROUP(ORDER BY DECODE(C.ZKS, 1, -1, C.NUM)) AS OFFICENAMES");
	sb.append(", T5.RID AS UNITID, T5.UNIT_SIMPNAME FROM TS_USER_INFO T1");
	sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
	sb.append("  WHERE B.IF_REVEAL = '1' UNION ALL SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, ");
	sb.append(" 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
	sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID WHERE C.IF_REVEAL = '1' ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
	sb.append(" INNER JOIN TS_UNIT T5 ON T1.UNIT_RID = T5.RID ");
	sb.append(" WHERE T1.IF_REVEAL = '1' AND T1.RID = '").append(userId).append("' ");
	sb.append(" GROUP BY T1.RID, T1.USERNAME ,C.EMPNUM ,T5.RID,T5.UNIT_SIMPNAME,T5.UNIT_CODE");
	sb.append(" ORDER BY T5.UNIT_CODE, SUM(DECODE(C.ZKS, 1, C.NUM, NULL)) ,C.EMPNUM ");
	
	List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
	if(null != list && list.size() > 0) {
		Object[] o = list.get(0);
		 TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
		 t.setUsername(o[1]==null?"":o[1].toString());

		 TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
		 unit.setUnitSimpname(o[5].toString());
		 
		 t.setOfficeIds(String.valueOf(o[2]));
		 t.setOfficeNames(String.valueOf(o[3]));
		 t.setTsUnit(unit);
		 
		 return t;
	}
	return null;
}

/**
 * +根据用户ID，查询用户对象(包括兼职人员)
 * @param empId 职工ID
 * @param em 实体管理器
 * @return 用户对象
 */
public static TsUserInfo findUserOfEmpJz(Integer empId, EntityManager em) {
	StringBuilder sb = new StringBuilder();
	sb.append("SELECT T1.RID AS USERID, T1.USERNAME, ','||LISTAGG(C.OFFICEID,',') WITHIN GROUP(ORDER BY C.OFFICEID)||',' AS OFFICEIDS ,");
	sb.append(" LISTAGG(C.OFFICENAME,',') WITHIN GROUP(ORDER BY DECODE(C.ZKS, 1, -1, C.NUM)) AS OFFICENAMES");
	sb.append(", T5.RID AS UNITID, T5.UNIT_SIMPNAME FROM TS_USER_INFO T1");
	sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
	sb.append("  WHERE B.IF_REVEAL = '1' UNION ALL SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, ");
	sb.append(" 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
	sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID WHERE C.IF_REVEAL = '1' ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
	sb.append(" INNER JOIN TS_UNIT T5 ON T1.UNIT_RID = T5.RID ");
	sb.append(" WHERE T1.IF_REVEAL = '1' AND C.RID = '").append(empId).append("' ");
	sb.append(" GROUP BY T1.RID, T1.USERNAME ,C.EMPNUM ,T5.RID,T5.UNIT_SIMPNAME,T5.UNIT_CODE");
	sb.append(" ORDER BY T5.UNIT_CODE, SUM(DECODE(C.ZKS, 1, C.NUM, NULL)) ,C.EMPNUM ");
	
	List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
	if(null != list && list.size() > 0) {
		Object[] o = list.get(0);
		 TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
		 t.setUsername(o[1]==null?"":o[1].toString());
		 
		 TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
		 unit.setUnitSimpname(o[5].toString());
		 
		 t.setOfficeIds(String.valueOf(o[2]));
		 t.setOfficeNames(String.valueOf(o[3]));
		 t.setTsUnit(unit);
		 
		 return t;
	}
	return null;
}


/**
 * +查询某个科室里面的用户(包括兼职人员)
 * @param officeId 科室ID
 * @param em 实体管理器
 * @return 用户对象
 */
public static List<TsUserInfo> findUsersOfOfficeJz(String officeIds, EntityManager em) {
	List<TsUserInfo> rtnList = new ArrayList<TsUserInfo>();
	
	StringBuilder sb = new StringBuilder();
	
	sb.append("SELECT * FROM (");
	sb.append(" SELECT T1.RID AS USERID, T1.USERNAME, ','||LISTAGG(C.OFFICEID,',') WITHIN GROUP(ORDER BY C.OFFICEID)||',' AS OFFICEIDS ,");
	sb.append(" LISTAGG(C.OFFICENAME,',') WITHIN GROUP(ORDER BY DECODE(C.ZKS, 1, -1, C.NUM)) AS OFFICENAMES");
	sb.append(", T5.RID AS UNITID, T5.UNIT_SIMPNAME FROM TS_USER_INFO T1");
	sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
	sb.append("  WHERE B.IF_REVEAL = '1' UNION ALL SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, ");
	sb.append(" 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
	sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID WHERE C.IF_REVEAL = '1' ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
	sb.append(" INNER JOIN TS_UNIT T5 ON T1.UNIT_RID = T5.RID ");
	sb.append(" WHERE T1.IF_REVEAL = '1' ");
	sb.append(" GROUP BY T1.RID, T1.USERNAME ,C.EMPNUM ,T5.RID,T5.UNIT_SIMPNAME,T5.UNIT_CODE");
	sb.append(" ORDER BY T5.UNIT_CODE, SUM(DECODE(C.ZKS, 1, C.NUM, NULL)) ,C.EMPNUM ");
	sb.append(") D WHERE 1=1 ");
	if(StringUtils.isNotBlank(officeIds))	{
		String[] idArr = officeIds.split(",");
		sb.append("AND (");
		StringBuilder officePiece = new StringBuilder();
		for( String id : idArr)	{
			officePiece.append(" OR D.OFFICEIDS LIKE '%,").append(id).append(",%'");
		}
		sb.append(officePiece.substring(3)).append(")");
	}
	List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
	if(null != list && list.size() > 0) {
		for(Object[] o: list) {
			 TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
			 t.setUsername(o[1]==null?"":o[1].toString());
			 
			 TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
			 unit.setUnitSimpname(o[5].toString());
			 
			 t.setOfficeIds(String.valueOf(o[2]));
			 t.setOfficeNames(String.valueOf(o[3]));
			 t.setTsUnit(unit);
			 
			 rtnList.add(t);
		}
	}
	return rtnList;
}


	/**
	 * +查询某个科室里面的领导(包括兼职人员)
	 * @param officeId 科室ID
	 * @param em 实体管理器
	 * @return 用户对象
	 */
	public static List<TsUserInfo> findUsersOfOfficeLeaderJz(String officeIds, EntityManager em) {
		List<TsUserInfo> rtnList = new ArrayList<TsUserInfo>();
		
		StringBuilder sb = new StringBuilder();
		
		sb.append("SELECT * FROM (");
		sb.append(" SELECT T1.RID AS USERID, T1.USERNAME, ','||LISTAGG(C.OFFICEID,',') WITHIN GROUP(ORDER BY C.OFFICEID)||',' AS OFFICEIDS ,");
		sb.append(" LISTAGG(C.OFFICENAME,',') WITHIN GROUP(ORDER BY DECODE(C.ZKS, 1, -1, C.NUM)) AS OFFICENAMES");
		sb.append(", T5.RID AS UNITID, T5.UNIT_SIMPNAME FROM TS_USER_INFO T1");
		sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
		sb.append("  WHERE B.IF_REVEAL = '1' AND A.IS_LEADER='1' UNION ALL SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, ");
		sb.append(" 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID AND A.IS_LEADER='1' ");
		sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID WHERE C.IF_REVEAL = '1' ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
		sb.append(" INNER JOIN TS_UNIT T5 ON T1.UNIT_RID = T5.RID ");
		sb.append(" WHERE T1.IF_REVEAL = '1' ");
		sb.append(" GROUP BY T1.RID, T1.USERNAME ,C.EMPNUM ,T5.RID,T5.UNIT_SIMPNAME,T5.UNIT_CODE");
		sb.append(" ORDER BY T5.UNIT_CODE, SUM(DECODE(C.ZKS, 1, C.NUM, NULL)) ,C.EMPNUM ");
		sb.append(") D WHERE 1=1 ");
		if(StringUtils.isNotBlank(officeIds))	{
			String[] idArr = officeIds.split(",");
			sb.append("AND (");
			StringBuilder officePiece = new StringBuilder();
			for( String id : idArr)	{
				officePiece.append(" OR D.OFFICEIDS LIKE '%,").append(id).append(",%'");
			}
			sb.append(officePiece.substring(3)).append(")");
		}
		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if(null != list && list.size() > 0) {
			for(Object[] o: list) {
				 TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
				 t.setUsername(o[1]==null?"":o[1].toString());
				 
				 TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
				 unit.setUnitSimpname(o[5].toString());
				 
				 t.setOfficeIds(String.valueOf(o[2]));
				 t.setOfficeNames(String.valueOf(o[3]));
				 t.setTsUnit(unit);
				 
				 rtnList.add(t);
			}
		}
		return rtnList;
	}

}

















