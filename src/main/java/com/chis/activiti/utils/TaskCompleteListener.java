package com.chis.activiti.utils;

import java.util.List;
import java.util.Map;

import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.history.HistoricProcessInstance;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.entity.TdFlowNodeScript;
import com.chis.activiti.script.IScript;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.SpringContextHolder;

/**
 * 节点任务执行完成时的方法
 * 
 *  * 流程脚本内置参数：<br/>
 * 	1. 流程发起者ID， startUserId
 * 	2. 业务表单ID，businessId
 * 	3. 流程实例ID，instanceId
 *  4. 任务ID，taskId
 *  5. 操作类型，ACT_OPT_TYPE （S：提交 R：驳回 D：删除 T：终止 F：办结， C：撤销 可能为空	）<BR/>
 *  
 *  
 *  
 *  保存参数，调用service.saveVariable(String taskId, Map<String, Object> map);
 *  
 * <AUTHOR>
 * @createTime 2015年9月30日
 */
@Service(value="TaskCompleteListener")
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class TaskCompleteListener implements TaskListener{

	private static final long serialVersionUID = 8665991902592861181L;
	
	private FlowBusinessServiceImpl service = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);

	@Override
	public void notify(DelegateTask task) {
		System.err.println("【TaskCompleteListener】");
		
		String defId = task.getProcessDefinitionId();
		String actNodeId = task.getTaskDefinitionKey();
		Integer scriptType = 1;
		
		//获取该节点下的脚本
		List<TdFlowNodeScript> scriptList = service.findNodeScriptList(defId, actNodeId, scriptType);
		if(null != scriptList && scriptList.size() > 0) {
			TdFlowNodeScript script = scriptList.get(0);
			
			//执行脚本
			Map<String, Object> vars = task.getVariables();
			vars.put("task", task);
			
			String taskId = task.getId();
			String instanceId = task.getProcessInstanceId();	//流程实例ID
			
			HistoricProcessInstance hpi = service.findInstanceById(instanceId);
			String startUserId = "";
			if(null != hpi) {
				startUserId = hpi.getStartUserId();	//流程发起者ID
			}else {
				//首节点，还没有HistoricProcessInstance
				startUserId = (String) task.getVariable(task.getTaskDefinitionKey() + "_assignees");
			}
			String businessId = "";
			Object businessIdObj = task.getVariable("businessId");
			if(null != businessIdObj) {
				businessId = BusinessIdUtil.parsePK(businessIdObj.toString());
			}
			vars.put("taskId", taskId);
			vars.put("startUserId", startUserId);
			vars.put("businessId", businessId);
			vars.put("instanceId", instanceId);
			vars.put("businessJson", task.getVariable("businessId"));
			
			//准备环境
			Map<String, IScript> beansMap = SpringContextHolder.getBeans(IScript.class); 
			vars.putAll(beansMap);
			
			//执行脚本
			System.err.println("【script】：" + script.getScript());
			GroovyScriptEngine scriptEngine = SpringContextHolder.getBean(GroovyScriptEngine.class); 
			scriptEngine.execute(script.getScript(), vars);
		}
	}

}
