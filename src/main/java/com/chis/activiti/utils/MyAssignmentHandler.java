package com.chis.activiti.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.activiti.engine.HistoryService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.history.HistoricProcessInstance;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.javabean.BusinessIdBean;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TdMsgMain;
import com.chis.modules.system.entity.TdMsgSub;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.MessageType;
import com.chis.modules.system.logic.BackNodeVO;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.protocol.PushMsgToAppImpl;
import com.chis.modules.system.service.CommServiceImpl;

@Service(value="MyAssignmentHandler")
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class MyAssignmentHandler implements TaskListener{

	private static final long serialVersionUID = 1L;
	private FlowBusinessServiceImpl service = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	private HistoryService historyService = SpringContextHolder.getBean("historyService");
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);

	@Override
	public void notify(DelegateTask task) {
		String assignees = (String) task.getVariable(task.getTaskDefinitionKey() + "_assignees");
		System.err.println("【MyAssignmentHandler】：" + assignees + ",【taskId】:" + task.getId());
		if(StringUtils.isNotBlank(assignees)) {
			List<String> userList = Arrays.asList(assignees.split(","));
			/**
			 * 是否退回，0-否 1-是
			 */
			Object ifBack = task.getVariable("if_back");
			if(null != ifBack && "1".equals(ifBack)) {
				task.setDescription("【驳回】");
			}
			
			//设置任务的待办人
			task.addCandidateUsers(userList);
			
			//保存站内消息
			String instanceId = task.getProcessInstanceId();
			HistoricProcessInstance hisProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(instanceId).singleResult();
			if(null != hisProcessInstance) {
				this.saveMsgs(hisProcessInstance.getBusinessKey(), task.getId(), hisProcessInstance.getStartUserId(), userList, ifBack,instanceId,task);
			}
		}
	}
	
	/**
	 * 保存消息主子表
	 */
	private void saveMsgs(String businessKey, String taskId, String starter, List<String> userList, Object ifBack,String instanceId,DelegateTask task) {
		TdMsgMain msgMain = new TdMsgMain();
		msgMain.setMessageType(MessageType.ACTIVITI);
		
		/**
		 * 是否退回，0-否 1-是
		 */
		String infoTitle = "";
		if(null != ifBack && "1".equals(ifBack)) {
			infoTitle = "【驳回】"+ businessKey;
			msgMain.setInfoTitle(infoTitle);
		}else {
			infoTitle = businessKey;
			msgMain.setInfoTitle(businessKey);
		}
		
		msgMain.setNetAdr("/webapp/flow/tdFlowTaskEdit.faces?taskId=" + taskId);
		msgMain.setNetName("待办任务");
		msgMain.setTsUserInfo(new TsUserInfo(new Integer(starter)));
		msgMain.setPublishTime(new Date());
		/********** ADD BY SUNM ****** date:20160803 ****begin*******/
		BusinessIdBean business = JSON.parseObject(task.getVariable("businessId").toString(), BusinessIdBean.class );
		// 更新当前登录人的待办任务状态
//		this.service.updateTdMsgState(Integer.valueOf(business.getRid()), (short)1,this.sessionData.getUser().getRid(),taskId);
		msgMain.setAppendKeys(business.getRid());  // 业务主键ID 
		msgMain.setSubType(0); // 消息类型
		msgMain.setIsTodo((short)1);  // 是否待办
		msgMain.setTodoState((short)0); // 待办状态
		
		// 获取待办任务的菜单ID menu_en=flow_dbrw
		Integer menuId = this.service.getMenuIdByMenuEn("flow_dbrw");
		if (menuId != null) {
			msgMain.setMenuId(menuId);
		}
		/********** ADD BY SUNM ****** date:20160803 ****end*******/
		
		List<TdMsgSub> subList = new ArrayList<TdMsgSub>();
		if(null != userList && userList.size() > 0 ) {
			for(String userId : userList) {
				if(StringUtils.isNotBlank(userId)) {
					TdMsgSub sub = new TdMsgSub();
					sub.setTdMsgMain(msgMain);
					sub.setTsUserInfo(new TsUserInfo(Integer.valueOf(userId)));
					subList.add(sub);
				}
			}
			service.saveTdMsg(msgMain, subList);
			this.sendPushMsg(StringUtils.list2string(userList, ","), infoTitle, Integer.valueOf(taskId),instanceId,msgMain.getRid().toString(), task);
		}
	}
	
	/** 极光推送 */
	public void sendPushMsg(String userIds, String content, Integer rid,String instanceId,String msgRid,DelegateTask task) {
		Map<String, String> paramMap = new HashMap<>();
		boolean ifSend = false;
		TdFlowNode node = service.findNodeByTaskId(task.getProcessDefinitionId(), task.getTaskDefinitionKey());
		//移动APP OA流程推送
		if( null != node && null != node.getTdFlowDef() && null != node.getTdFlowDef().getIsDispapp() && 
				node.getTdFlowDef().getIsDispapp().intValue() == 1)	{
			paramMap.put("type", "2");
			paramMap.put("msgRid", msgRid.toString());
			paramMap.put("taskId", rid.toString());
			paramMap.put("flowId", instanceId);
			ifSend = true;
		}
		//转交接报与值班接报的流程定义key
		String outOrg = commService.findParamValue("EMERG_OUTORG");
		String moveReport =commService.findParamValue("EMERG_MOVE_REPORT");
		String flowKeyName = node.getFlowKeyName();
		if(StringUtils.isNotBlank(flowKeyName) && (flowKeyName.equals(outOrg) || flowKeyName.equals(moveReport)))	{
			paramMap.put("type", "4");
			ifSend = true;
		}
		//是否需要推送消息
		if (ifSend) {
			List<TsUserInfo> userList = this.commService.findUserByUserIds(userIds);
			if (userList != null && userList.size() > 0) {
				for (TsUserInfo user : userList) {
					new PushMsgToAppImpl(content, paramMap, user.getUserNo()).sendJPush();
				}
			}
		}
		
	}
	
	

}
