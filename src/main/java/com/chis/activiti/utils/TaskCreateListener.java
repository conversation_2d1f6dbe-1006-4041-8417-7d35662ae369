package com.chis.activiti.utils;

import java.util.List;
import java.util.Map;

import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.history.HistoricProcessInstance;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.entity.TdFlowNodeScript;
import com.chis.activiti.script.IScript;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;

/**
 * 进入节点执行的方法
 * <AUTHOR>
 * @createTime 2015年9月30日
 */
@Service(value="TaskCreateListener")
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class TaskCreateListener implements TaskListener{

	private static final long serialVersionUID = 2109006210648428278L;
	
	private FlowBusinessServiceImpl service = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);

	@Override
	public void notify(DelegateTask task) {
		System.err.println("【TaskCreateListener】");
		
		String defId = task.getProcessDefinitionId();
		String actNodeId = task.getTaskDefinitionKey();
		Integer scriptType = 0;
		
		//获取该节点下的脚本
		List<TdFlowNodeScript> scriptList = service.findNodeScriptList(defId, actNodeId, scriptType);
		if(null != scriptList && scriptList.size() > 0) {
			TdFlowNodeScript script = scriptList.get(0);
			
			if(StringUtils.isNotBlank(script.getScript())) {
				//执行脚本
				Map<String, Object> vars = task.getVariables();
				vars.put("task", task);
				
				String taskId = task.getId();
				String instanceId = task.getProcessInstanceId();	//流程实例ID
				
				HistoricProcessInstance hpi = service.findInstanceById(instanceId);
				String startUserId = "";
				if(null != hpi) {
					startUserId = hpi.getStartUserId();	//流程发起者ID
				}else {
					//首节点，还没有HistoricProcessInstance
					startUserId = (String) task.getVariable(task.getTaskDefinitionKey() + "_assignees");
				}
				String businessId = "";
				Object businessIdObj = task.getVariable("businessId");
				if(null != businessIdObj) {
					businessId = BusinessIdUtil.parsePK(businessIdObj.toString());
				}
				vars.put("taskId", taskId);
				vars.put("startUserId", startUserId);
				vars.put("businessId", businessId);
				vars.put("instanceId", instanceId);
				vars.put("businessJson", task.getVariable("businessId"));
				
				//准备环境
				Map<String, IScript> beansMap = SpringContextHolder.getBeans(IScript.class); 
				vars.putAll(beansMap);
				
				//执行脚本
				System.err.println("【script】：" + script.getScript());
				GroovyScriptEngine scriptEngine = SpringContextHolder.getBean(GroovyScriptEngine.class); 
				scriptEngine.execute(script.getScript(), vars);
			}
		}			
	}

}
