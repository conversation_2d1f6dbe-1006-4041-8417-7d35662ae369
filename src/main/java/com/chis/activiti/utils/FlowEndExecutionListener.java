package com.chis.activiti.utils;

import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.service.impl.FlowBusinessServiceImpl;

/**
 * 流程结束监听事件
 * 	根据businessKey中的tableName，更改其状态由0变为1
 *  在流程变量中，必须要有变量businessTableName
 * 
 * <AUTHOR>
 * @date 2015年4月29日
 */
@Service
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class FlowEndExecutionListener implements ExecutionListener {

	private static final long serialVersionUID = -2358980118334739332L;
	
	@Autowired
	private FlowBusinessServiceImpl service;

	@Override
	public void notify(DelegateExecution execution) throws Exception {
		Object businessTableName = execution.getVariable("businessTableName");
		if(null != businessTableName && !"".equals(businessTableName.toString().trim())) {
			service.updataBusinessTableState(businessTableName.toString(), "0", "1");
		}
	}

}
