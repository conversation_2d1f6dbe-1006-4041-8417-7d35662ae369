package com.chis.activiti.utils;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.javabean.BusinessIdBean;
import com.chis.common.utils.StringUtils;

/**
 * 流程中businessId与json之间
 * <AUTHOR> 2014-12-24
 */
public class BusinessIdUtil {
	private BusinessIdUtil() {
    }
	
	/**
	 * 根据主键快速生成json字符串
	 * @param rid 业务主键
	 * @return json字符串
	 */
	public static String toJsonString(String rid) {
		if(StringUtils.isNotBlank(rid)) {
			return JSON.toJSONString(new BusinessIdBean(rid));
		}
		return null;
	}
	
	
	/**
	 * 根据json字符串解析获取主键
	 * @param json 字符串
	 * @return 业务主键
	 */
	public static String parsePK(String json) {
		if(StringUtils.isNotBlank(json)) {
			BusinessIdBean bean = JSON.parseObject(json, BusinessIdBean.class);
			return bean.getRid();
		}
		return null;
	}
}
