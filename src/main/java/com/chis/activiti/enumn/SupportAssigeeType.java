package com.chis.activiti.enumn;

/**
 * 流程按钮类型
 * 
 * <AUTHOR>
 * @createDate 2014-12-18
 */
public enum SupportAssigeeType {

	OR("0") {
		public String getTypeCN() {
			return "合并提供的待办人";
		}
	},

	AND("1") {
		public String getTypeCN() {
			return "与提供的待办人比较，取相同的人";
		}
	},

	ONLY("2") {
		public String getTypeCN() {
			return "仅使用提供的待办人";
		}
	}
	;

	private final String typeNo;

	SupportAssigeeType(String typeNo) {
		this.typeNo = typeNo;
	}

	public String toString() {
		return String.valueOf(typeNo);
	}

	public String getTypeNo() {
		return typeNo;
	}

	public abstract String getTypeCN();

}
