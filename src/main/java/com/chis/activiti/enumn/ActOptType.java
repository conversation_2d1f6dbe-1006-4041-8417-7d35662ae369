package com.chis.activiti.enumn;

/**
 * 节点处理类型
 * <AUTHOR>
 */
public enum ActOptType {

	/**
	 * 保存
	 */
	S("S") {
		public String getTypeCN() {
			return "保存";
		}
	},

	/**
	 * 驳回
	 */
	R("R") {
		public String getTypeCN() {
			return "驳回";
		}
	},

	/**
	 * 删除
	 */
	D("D") {
		public String getTypeCN() {
			return "删除";
		}
	},

	/**
	 * 终止
	 */
	T("T") {
		public String getTypeCN() {
			return "终止";
		}
	},
	
	/**
	 * 办结
	 */
	F("F") {
		public String getTypeCN() {
			return "办结";
		}
	},
	
	/**
	 * 完成
	 */
	FS("FS") {
		public String getTypeCN() {
			return "完成";
		}
	},
	
	/**
	 * 撤销
	 */
	C("C") {
		public String getTypeCN() {
			return "撤销";
		}
	}
	
	;

	private final String typeNo;

	ActOptType(String typeNo) {
		this.typeNo = typeNo;
	}

	public String toString() {
		return String.valueOf(typeNo);
	}

	public String getTypeNo() {
		return typeNo;
	}

	public abstract String getTypeCN();

}
