package com.chis.activiti.enumn;

/**
 * 流程按钮类型
 * 
 * <AUTHOR>
 * @createDate 2014-12-18
 */
public enum FlowBtnType {

	SAVE("0") {
		public String getTypeCN() {
			return "保存";
		}
	},

	SUBMIT("1") {
		public String getTypeCN() {
			return "提交";
		}
	},

	REJECT("2") {
		public String getTypeCN() {
			return "驳回";
		}
	},

	DELETE("3") {
		public String getTypeCN() {
			return "删除";
		}
	},

	FLOW_IMG("4") {
		public String getTypeCN() {
			return "流程图";
		}
	},

	BACK("5") {
		public String getTypeCN() {
			return "返回";
		}
	},
	
	PREVIEW("6") {
		public String getTypeCN() {
			return "预览";
		}
	},	
	
	PRINT("7") {
		public String getTypeCN() {
			return "打印";
		}
	},
	
	DESIGN("8") {
		public String getTypeCN() {
			return "设计";
		}
	},
	
	PRINTER("9") {
		public String getTypeCN() {
			return "打印机";
		}
	},
	
	FENFA("10") {
		public String getTypeCN() {
			return "分发";
		}
	},
	
	TERMINATE("11") {
		public String getTypeCN() {
			return "终止";
		}
	},
	
	/**无论流程实例中有多少个任务，都办结掉*/
	FINISH("12") {
		public String getTypeCN() {
			return "办结";
		}
	},
	
	/**只办结自己的待办任务*/
	FINISH_SELF("13") {
		public String getTypeCN() {
			return "完成";
		}
	},
	
	/**新增*/
	ADD_AGAIN("14") {
		public String getTypeCN() {
			return "新增";
		}
	},
	
	/**文档打印*/
	WORD_PRINT("15") {
		public String getTypeCN() {
			return "文档打印";
		}
	},
	
	/**已阅*/
	READ("16") {
		public String getTypeCN() {
			return "已阅";
		}
	}
	
	/*,
	
	CANCEL("13") {
		public String getTypeCN() {
			return "撤销";
		}
	}*/
	
	;

	private final String typeNo;

	FlowBtnType(String typeNo) {
		this.typeNo = typeNo;
	}

	public String toString() {
		return String.valueOf(typeNo);
	}

	public String getTypeNo() {
		return typeNo;
	}

	public abstract String getTypeCN();

}
