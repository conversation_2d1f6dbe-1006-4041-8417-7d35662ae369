package com.chis.activiti.enumn;

/**
 * 节点处理类型
 * <AUTHOR>
 */
public enum DealType {

	YBJD((short)0) {
        public String getTypeCN() { return "一般节点";}
	},

	HQJD((short)1) {
        public String getTypeCN() { return "会签节点";}
	}

    ;

	private final Short typeNo;

	DealType(Short typeNo) {
		this.typeNo = typeNo;
	} 
	
	public String toString() {return String.valueOf(typeNo);}
	
	
	public Short getTypeNo() {
		return typeNo;
	}

    public abstract String getTypeCN();

}
