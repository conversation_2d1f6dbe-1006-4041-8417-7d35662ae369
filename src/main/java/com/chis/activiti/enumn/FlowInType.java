package com.chis.activiti.enumn;

/**
 * 节点流转方式
 * <AUTHOR>
 */
public enum FlowInType {
	/**自动跳转*/
	ZYTZ((short)0) {
        public String getTypeCN() { return "自动跳转";}
	},

	/**选择跳转*/
	XZTZ((short)1) {
        public String getTypeCN() { return "选择跳转";}
	},
	
	/**拟办跳转*/
	NBJD((short)2) {
        public String getTypeCN() { return "拟办跳转";}
	}

    ;

	private final Short typeNo;

	FlowInType(Short typeNo) {
		this.typeNo = typeNo;
	} 
	
	public String toString() {return String.valueOf(typeNo);}
	
	
	public Short getTypeNo() {
		return typeNo;
	}

    public abstract String getTypeCN();

}
