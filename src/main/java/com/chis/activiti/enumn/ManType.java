package com.chis.activiti.enumn;

/**
 * 待办人类型
 * <AUTHOR>
 */
public enum ManType {

	RYTX((short)0) {
        public String getTypeCN() { return "任意挑选";}
	},
	
	ADW((short)1) {
        public String getTypeCN() { return "按单位";}
	},
	
	AKS((short)2) {
        public String getTypeCN() { return "按科室";}
	},

	AGZ((short)3) {
        public String getTypeCN() { return "按规则";}
	}
	
    ;

	private final Short typeNo;

	ManType(Short typeNo) {
		this.typeNo = typeNo;
	} 
	
	public String toString() {return String.valueOf(typeNo);}
	
	
	public Short getTypeNo() {
		return typeNo;
	}

    public abstract String getTypeCN();

}
