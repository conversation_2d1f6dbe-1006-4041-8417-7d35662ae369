package com.chis.activiti.service.impl;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.activiti.engine.ActivitiException;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricVariableInstance;
import org.activiti.engine.impl.cmd.GetDeploymentProcessDefinitionCmd;
import org.activiti.engine.impl.interceptor.Command;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.persistence.entity.ExecutionEntity;
import org.activiti.engine.impl.persistence.entity.HistoricIdentityLinkEntity;
import org.activiti.engine.impl.persistence.entity.HistoricProcessInstanceEntity;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.activiti.engine.impl.pvm.process.ActivityImpl;
import org.activiti.engine.impl.pvm.process.ProcessDefinitionImpl;
import org.apache.commons.lang3.StringUtils;

import com.chis.common.utils.SpringContextHolder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

/**
 * 将已结束的流程，撤回到最后一个提交的节点
 * <AUTHOR>
 * @createTime 2016年9月14日
 */
@Deprecated
public class ReplevyEndedProcessCmd implements Command<Void> {
	/**要撤回的历史流程实例ID*/
	private String histInstId;
	/**最后一步是会签节点的话，可能要添加处理人*/
	private String userIds;
	/** 退回的节点名称 */
	private String backTaskNode;
	
	private ProcessDefinitionEntity processDefinition = null;
	private HistoricActivityInstance lastActInst = null;
	
	private CommandContext commandContext = null;
	private RuntimeService runtimeService;
//	private TaskService taskService;
//	private RepositoryService repositoryService;
	private HistoryService historyService;

	public ReplevyEndedProcessCmd(String histInstId, String userIds, String backTaskNode) {
		this.histInstId = histInstId;
		this.userIds = userIds;
		this.backTaskNode = backTaskNode;
		
		this.runtimeService = SpringContextHolder.getBean("runtimeService");
//		this.taskService = SpringContextHolder.getBean("taskService");
//		this.repositoryService = SpringContextHolder.getBean("repositoryService");
		this.historyService = SpringContextHolder.getBean("historyService");
	}

	public Void execute(CommandContext commandContext) {
		this.commandContext = commandContext;
		
		HistoricProcessInstanceEntity histProcInst = commandContext.getHistoricProcessInstanceEntityManager().findHistoricProcessInstance(histInstId);
		histProcInst.setEndActivityId(null);
		histProcInst.setEndTime(null);
		
		String processDefinitionId = histProcInst.getProcessDefinitionId();
		processDefinition = new GetDeploymentProcessDefinitionCmd(processDefinitionId).execute(commandContext);

		this.createProcessInstance(histProcInst);
		
		return null;
	}

	private ExecutionEntity createProcessInstance(HistoricProcessInstanceEntity histProcInst) {
		ActivityImpl lastAct = this.findLastActivity();

		if (lastAct == null) {
			throw new ActivitiException("Cannot start process instance, initial activity where the process instance should start is null.");
		}
		
		if(this.isParallel(lastAct)) {
			return processDeadMutilInstance(histProcInst, lastAct);
		}else {
			return processDeadSingleInstance(histProcInst, lastAct);
		}
	}
	
	private ExecutionEntity processDeadSingleInstance(HistoricProcessInstanceEntity histProcInst, ActivityImpl lastAct) {
		ExecutionEntity processInstance = new ExecutionEntity(lastAct);
		processInstance.setId(this.histInstId);
		processInstance.insert();
		processInstance.setProcessDefinition(processDefinition);
		processInstance.setProcessInstance(processInstance);
		processInstance.setBusinessKey(histProcInst.getBusinessKey());
		processInstance.initialize();
		
		//保存变量
		this.runtimeService.setVariables(this.histInstId, this.findUsersVaris(this.histInstId));
		
		processInstance.executeActivity(lastAct);
		
		return processInstance;
	}
	
	private ExecutionEntity processDeadMutilInstance(HistoricProcessInstanceEntity histProcInst, ActivityImpl lastAct) {
		ExecutionEntity processInstance = new ExecutionEntity();
		processInstance.setId(this.histInstId);
		processInstance.setParentId(null);
		processInstance.setProcessDefinition(processDefinition);
		processInstance.setProcessInstance(processInstance);
		processInstance.setActive(false);
		processInstance.setConcurrent(false);
		processInstance.setScope(true);
		processInstance.setEventScope(false);
		processInstance.setSuspensionState(1);
		processInstance.setCachedEntityState(0);
		
		commandContext.getExecutionEntityManager().insert(processInstance);
		
		//保存变量
		Map<String, Object> allUsersVaris = this.findUsersVaris(this.histInstId);
		String userKey = lastAct.getId()+"_multiUserList";
		
		Collection<String> oldList = (Collection<String>) allUsersVaris.get(userKey);
		List<String> newList = Lists.newArrayList();
		if(StringUtils.isNotBlank(userIds)) {
			String[] arr = this.userIds.split(",");
			for(String s : arr) {
				newList.add(s);
				oldList.add(s);
			}
		}else {
			String assignee = null;
			
			List<HistoricIdentityLinkEntity> histIdenList = this.commandContext.getHistoricIdentityLinkEntityManager().findHistoricIdentityLinksByTaskId(this.lastActInst.getTaskId());
			if(null != histIdenList && histIdenList.size() > 0) {
				for(HistoricIdentityLinkEntity hil : histIdenList) {
					if("candidate".equals(hil.getType())) {
						assignee = hil.getUserId();
						newList.add(assignee);
						break;
					}
				}
			}
		}
		
		allUsersVaris.put(userKey, newList);
		this.runtimeService.setVariables(this.histInstId, allUsersVaris);
		
		ExecutionEntity parent = new ExecutionEntity();
		parent.setParentId(this.histInstId);
		parent.setProcessDefinition(processDefinition);
		parent.setProcessInstance(processInstance.getProcessInstance());
		parent.setActivity(lastAct);
		parent.setActive(false);
		parent.setConcurrent(false);
		parent.setScope(true);
		parent.setEventScope(false);
		parent.setSuspensionState(1);
		parent.setCachedEntityState(0);

		commandContext.getExecutionEntityManager().insert(parent);
		
		ExecutionEntity childExecution = new ExecutionEntity();
		childExecution.setParentId(parent.getId());
		childExecution.setProcessDefinition(processDefinition);
		childExecution.setProcessInstance(processInstance.getProcessInstance());
		childExecution.setActivity(lastAct);
		childExecution.setActive(true);
		childExecution.setConcurrent(true);
		childExecution.setScope(false);
		childExecution.setEventScope(false);
		childExecution.setSuspensionState(1);
		childExecution.setCachedEntityState(0);
		commandContext.getExecutionEntityManager().insert(childExecution);
		childExecution.executeActivity(lastAct);
		
		
		allUsersVaris.put(userKey, oldList);
		this.runtimeService.setVariables(this.histInstId, allUsersVaris);
		
		return processInstance;
	}
	
	/**
	 * <li>返回当前节点对象
	 */
	protected ActivityImpl findLastActivity() {
		List<HistoricActivityInstance> histActList = historyService.createHistoricActivityInstanceQuery().processInstanceId(this.histInstId).orderByActivityId().asc().list();
		if(null != histActList && histActList.size() > 0) {
			for(HistoricActivityInstance histAct : histActList) {
				if (this.backTaskNode.equals(histAct.getActivityId())) {
					this.lastActInst = histAct;
					String taskDefKey = histAct.getActivityId();
					return this.findActivity(taskDefKey);
				}
				
//				if("userTask".equals(histAct.getActivityType())) {
//					this.lastActInst = histAct;
//					String taskDefKey = histAct.getActivityId();
//					return this.findActivity(taskDefKey);
//				}
			}
		}
		return null;
	}
	
	protected ActivityImpl findActivity(String activityId) {
		return ((ProcessDefinitionImpl) processDefinition).findActivity(activityId);
	}
	
	protected Map<String, Object> findUsersVaris(String procInstId) {
		Map<String, Object> allVariMap = this.getAllVariablesInProcess(procInstId);
		Map<String, Object> usersVariMap = Maps.newHashMap();
		// 设置代办人
		List<ActivityImpl> actList = ((ProcessDefinitionImpl) processDefinition).getActivities();
		if(null != actList && actList.size() > 0) {
			for(ActivityImpl act : actList) {
				if("userTask".equals(act.getProperty("type"))) {
					String key = null;
					if(this.isParallel(act)) {
						key = act.getId() + "_multiUserList";
					}else {
						key = act.getId() + "_assignees";
					}
					if(allVariMap.containsKey(key)) {
						usersVariMap.put(key, allVariMap.get(key));
					}
				}
			}
		}
		
		// 设置businessId
		if(allVariMap.containsKey("businessId")) {
			usersVariMap.put("businessId", allVariMap.get("businessId"));
		}
		
		// nrOfInstances nrOfCompletedInstances nrOfActiveInstances
//		int length = this.userIds.split(",").length;
//		if(allVariMap.containsKey("nrOfInstances")) {
//			usersVariMap.put("nrOfInstances", Integer.valueOf(allVariMap.get("nrOfInstances").toString())+length);
//		}
//		
//		if(allVariMap.containsKey("nrOfCompletedInstances")) {
//			usersVariMap.put("nrOfCompletedInstances", allVariMap.get("nrOfInstances"));
//		}
//		
//		if(allVariMap.containsKey("nrOfActiveInstances")) {
//			usersVariMap.put("nrOfActiveInstances", Integer.valueOf(allVariMap.get("nrOfActiveInstances").toString())+length);
//		}
		
		
		return usersVariMap;
	}
			
	/**
	 * 根据流程实例ID，查询所有的流程变量
	 * 
	 * @param procInstId
	 * @return
	 */
	protected Map<String, Object> getAllVariablesInProcess(String procInstId) {
		Map<String, Object> map = Maps.newHashMap();
		List<HistoricVariableInstance> list = this.historyService.createHistoricVariableInstanceQuery().processInstanceId(procInstId).list();
		if (null != list && list.size() > 0) {
			for (HistoricVariableInstance hvi : list) {
				map.put(hvi.getVariableName(), hvi.getValue());
			}
		}
		return map;
	}
	
	/**
	 * <li>判断节点多实例类型是否是并发
	 */
	protected boolean isParallel(ActivityImpl act) {
		if (null != act.getProperty("multiInstance") && "parallel".equals(act.getProperty("multiInstance"))) {
			return true;
		}
		return false;
	}

}
