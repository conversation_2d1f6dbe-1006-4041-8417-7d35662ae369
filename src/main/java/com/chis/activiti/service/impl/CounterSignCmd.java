package com.chis.activiti.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.activiti.engine.RuntimeService;
import org.activiti.engine.impl.interceptor.Command;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.persistence.entity.ExecutionEntity;
import org.activiti.engine.impl.persistence.entity.HistoricIdentityLinkEntity;
import org.activiti.engine.impl.persistence.entity.IdentityLinkEntity;
import org.activiti.engine.impl.persistence.entity.TaskEntity;
import org.activiti.engine.impl.pvm.process.ActivityImpl;
import org.activiti.engine.impl.pvm.process.ProcessDefinitionImpl;
import org.activiti.engine.runtime.Execution;
import org.activiti.engine.task.IdentityLinkType;

import com.chis.common.utils.SpringContextHolder;

/**
 * 加减签，针对某个节点的某个任务
 * 
 * 注意：加减签分开调，不要同时传入加签和减签的
 * <AUTHOR>
 */
public class CounterSignCmd implements Command<String> {
	/** 当前流程实例ID */
	private String instanceId;
	/** 要加减签的节点ID */
	private String activityId;
	/** 加签的待办人 */
	private List<String> addList;
	/** 减签的待办人 */
	private List<String> subtractList;
	private CommandContext commandContext;
	private RuntimeService runtimeService;

	private CounterSignCmd() {
	}

	public CounterSignCmd(String instanceId, String activityId, List<String> addList, List<String> subtractList) {
		this.instanceId = instanceId;
		this.activityId = activityId;
		this.addList = addList;
		this.subtractList = subtractList;
		
		runtimeService =  SpringContextHolder.getBean("runtimeService");
	}

	public String execute(CommandContext commandContext) {
		this.commandContext = commandContext;

		if (this.isParallel(this.getActivity())) {
			this.dealParallelInstance();
		} else {
			this.dealSequentialInstance();
		}
		return null;
	}

	/**
	 * <li>添加一条并行实例
	 */
	private void dealParallelInstance() {
		ExecutionEntity parentExecutionEntity = new ExecutionEntity();// = this.commandContext.getExecutionEntityManager().findExecutionById(this.instanceId).findExecution(activityId);
		
		List<Execution> data = this.runtimeService.createExecutionQuery().processInstanceId(this.instanceId).activityId(activityId).list();
		if (data == null || data.size() == 0) {
			return ;
		}
		
		for (Execution e : data) {
			ExecutionEntity entity = (ExecutionEntity) e;
			if (entity.isActive() ) {
				parentExecutionEntity = entity.getParent();
				break;
			}
		}
		
		List<ExecutionEntity> childList = parentExecutionEntity.getExecutions();
		ActivityImpl activity = getActivity();
		String assigneeKey = this.activityId+"_multiUserList";
		Collection<String> assigneeProto = (Collection<String>)parentExecutionEntity.getVariable(assigneeKey);
		List<String> assigneeList = new ArrayList<String>();
		for(String s : assigneeProto) {
			assigneeList.add(s);
		}
		
		
		List<ExecutionEntity> addEntityList = new ArrayList<ExecutionEntity>();
		List<ExecutionEntity> subtractEntityList = new ArrayList<ExecutionEntity>();
		
		
		if(null != this.addList && this.addList.size() > 0) {
			for(String s: this.addList) {
				assigneeList.add(s);
				ExecutionEntity execution = parentExecutionEntity.createExecution();
				execution.setActive(true);
				execution.setConcurrent(true);
				execution.setScope(false);
				
				parentExecutionEntity.setVariableLocal("nrOfInstances", (Integer) parentExecutionEntity.getVariableLocal("nrOfInstances") + 1);
				parentExecutionEntity.setVariableLocal("nrOfActiveInstances", (Integer) parentExecutionEntity.getVariableLocal("nrOfActiveInstances") + 1);
				execution.setVariableLocal("loopCounter", parentExecutionEntity.getExecutions().size() + 1);
				execution.setVariableLocal(activityId+"_assignees", s);
				
				addEntityList.add(execution);
			}
		}
		if(null != this.subtractList && this.subtractList.size() > 0) {
			for(String s: this.subtractList) {
				for(ExecutionEntity ee: childList) {
					if(s.equals(ee.getVariableLocal(this.activityId+"_assignees"))) {
						subtractEntityList.add(ee);
						break;
					}
				}
				assigneeList.remove(s);
			}
		}
		
        if(null != subtractEntityList && subtractEntityList.size() > 0) {
        	for(ExecutionEntity ee : subtractEntityList) {
        		ee.remove();
        		ee.getParent().setVariableLocal("nrOfInstances", (Integer) ee.getParent().getVariableLocal("nrOfInstances") - 1);
        		ee.getParent().setVariableLocal("nrOfActiveInstances", (Integer) ee.getParent().getVariableLocal("nrOfActiveInstances") - 1);
        	}
        }
        if(null != addEntityList && addEntityList.size() > 0) {
        	for(ExecutionEntity ee : addEntityList) {
        		ee.executeActivity(activity);
        	}
        }
	}

	/**
	 * <li>给串行实例集合中添加一个审批人
	 * 
	 * 1.更改变量
	 * 2.增加、删除 ACT_HI_IDENTITYLINK, ACT_RU_IDENTITYLINK 数据
	 */
	private void dealSequentialInstance() {
		ExecutionEntity execution = getActivieExecutions().get(0);
		
		//串行就1个任务
		TaskEntity task = execution.getTasks().get(0);
		List<IdentityLinkEntity> ileList = this.commandContext.getIdentityLinkEntityManager().findIdentityLinksByTaskId(task.getId());
		
		List<IdentityLinkEntity> addEntityList = new ArrayList<IdentityLinkEntity>();
		List<HistoricIdentityLinkEntity> addHisEntityList = new ArrayList<HistoricIdentityLinkEntity>();
		List<IdentityLinkEntity> subtractEntityList = new ArrayList<IdentityLinkEntity>();
		/**
		 * key-用户iD, value，新增的IdentityLinkEntity的ID
		 * 为了保持IdentityLinkEntity、HistoricIdentityLinkEntity的ID统一
		 */
		Map<String, String> map = new HashMap<String, String>();
		
		String assigneeKey = this.activityId+"_assignees";
		String assignees = (String)execution.getVariable(assigneeKey);
		if(null != this.addList && this.addList.size() > 0) {
			for(String s: this.addList) {
				assignees = assignees + "," + s;
				
				IdentityLinkEntity entity = new IdentityLinkEntity();
				entity.setType(IdentityLinkType.CANDIDATE);
				entity.setUserId(s);
				entity.setTaskId(task.getId());
				addEntityList.add(entity);
				
				HistoricIdentityLinkEntity hisEntity = new HistoricIdentityLinkEntity();
				hisEntity.setType(IdentityLinkType.CANDIDATE);
				hisEntity.setUserId(s);
				hisEntity.setTaskId(task.getId());
				addHisEntityList.add(hisEntity);
			}
		}
		if(null != this.subtractList && this.subtractList.size() > 0) {
			List<String> asList = string2list(assignees,",");
			List<String> delList = new ArrayList<String>();
			for(String s: this.subtractList) {
				if(asList.contains(s)) {
					delList.add(s);
				}
				
				for(IdentityLinkEntity ile : ileList) {
					if(s.equals(ile.getUserId())) {
						subtractEntityList.add(ile);
						break;
					}
				}
			}
			asList.removeAll(delList);
			assignees = list2string(asList, ",");
		}
        execution.setVariable(assigneeKey, assignees);
        execution.setVariableLocal(assigneeKey, assignees);
        
        if(null != addEntityList && addEntityList.size() > 0) {
        	for(IdentityLinkEntity ile : addEntityList) {
        		this.commandContext.getIdentityLinkEntityManager().insert(ile);
        		System.err.println("ID:" + ile.getId());
        		map.put(ile.getUserId(), ile.getId());
        	}
        }
        if(null != addHisEntityList && addHisEntityList.size() > 0) {
        	for(HistoricIdentityLinkEntity ile : addHisEntityList) {
        		ile.setId(map.get(ile.getUserId()));
        		this.commandContext.getHistoricIdentityLinkEntityManager().insert(ile);
        	}
        }
        if(null != subtractEntityList && subtractEntityList.size() > 0) {
        	for(IdentityLinkEntity ile : subtractEntityList) {
        		this.commandContext.getIdentityLinkEntityManager().deleteIdentityLink(ile, true);
        	}
        }
	}

	/**
	 * <li>获取活动的执行 , 子流程的活动执行是其孩子执行(并行多实例情况下) <li>串行情况下获取的结果数量为1
	 */
	protected List<ExecutionEntity> getActivieExecutions() {
		List<ExecutionEntity> activeExecutions = new ArrayList<ExecutionEntity>();
		ActivityImpl activity = getActivity();
		List<ExecutionEntity> executions = getChildExecutionByProcessInstanceId();

		for (ExecutionEntity execution : executions) {
			if (execution.isActive() && (execution.getActivityId().equals(activityId) || activity.contains(execution.getActivity()))) {
				activeExecutions.add(execution);
			}
		}

		return activeExecutions;
	}

	/**
	 * <li>获取流程实例根的所有子执行
	 */
	protected List<ExecutionEntity> getChildExecutionByProcessInstanceId() {
		return commandContext.getExecutionEntityManager().findChildExecutionsByProcessInstanceId(this.instanceId);
	}

	/**
	 * <li>返回当前节点对象
	 */
	protected ActivityImpl getActivity() {
		return this.getProcessDefinition().findActivity(this.activityId);
	}

	/**
	 * <li>判断节点多实例类型是否是并发
	 */
	protected boolean isParallel(ActivityImpl act) {
		if (null != act.getProperty("multiInstance") && "parallel".equals(act.getProperty("multiInstance"))) {
			return true;
		}
		return false;
	}

	/**
	 * <li>返回流程定义对象
	 */
	protected ProcessDefinitionImpl getProcessDefinition() {
		return this.getProcessInstanceEntity().getProcessDefinition();
	}

	/**
	 * <li>返回流程实例的根执行对象
	 */
	protected ExecutionEntity getProcessInstanceEntity() {
		return commandContext.getExecutionEntityManager().findExecutionById(this.instanceId);
	}
	
    /**
     * List转String
     * @param list List
     * @param splitStr 分隔符
     * @return String
     */
    public static String list2string(List list, String splitStr) {
        if(null != list && list.size() > 0) {
            StringBuilder sb = new StringBuilder();
            for(Object s: list) {
                sb.append(splitStr).append(s);
            }
            return sb.toString().replaceFirst(splitStr, "");
        }else {
            return null;
        }
    }
    
    /**
     * String根据分隔符转List
     * @param value String字符串
     * @param splitStr 分隔符
     * @return List
     */
    public static List<String> string2list(String value, String splitStr) {
        List<String> list = new ArrayList<String>();;
        if(null != value && !"".equals(value.trim())) {
            String[] arr = value.split(splitStr);
            for(String s: arr) {
                list.add(s);
            }
        }
        return list;
    }
}
