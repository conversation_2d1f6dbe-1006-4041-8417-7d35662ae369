package com.chis.activiti.service.impl;

import java.io.InputStream;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.persistence.Query;

import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricVariableInstance;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.activiti.engine.impl.pvm.PvmActivity;
import org.activiti.engine.impl.pvm.process.ActivityImpl;
import org.activiti.engine.impl.task.TaskDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.entity.ActHiTaskrcd;
import com.chis.activiti.entity.TbFlowAdvtemplate;
import com.chis.activiti.entity.TdEmTxrptCase;
import com.chis.activiti.entity.TdFlowDef;
import com.chis.activiti.entity.TdFlowDefRole;
import com.chis.activiti.entity.TdFlowDefUser;
import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.entity.TdFlowNodeBtn;
import com.chis.activiti.entity.TdFlowNodePage;
import com.chis.activiti.entity.TdFlowNodeScript;
import com.chis.activiti.entity.TdFlowNodeTxType;
import com.chis.activiti.entity.TdFlowRule;
import com.chis.activiti.entity.TdFlowRuleNode;
import com.chis.activiti.entity.TdFlowTitle;
import com.chis.activiti.entity.TdFlowTrust;
import com.chis.activiti.entity.TdFlowType;
import com.chis.activiti.enumn.ActOptType;
import com.chis.activiti.enumn.DealType;
import com.chis.activiti.enumn.FlowBtnType;
import com.chis.activiti.enumn.FlowInType;
import com.chis.activiti.javabean.BusinessIdBean;
import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.MutiNodeDealBean;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.script.IScript;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.activiti.utils.BusinessIdUtil;
import com.chis.activiti.utils.GroovyScriptEngine;
import com.chis.common.utils.CollectionUtil;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.Reflections;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TbTempmetaDefine;
import com.chis.modules.system.entity.TdFormDef;
import com.chis.modules.system.entity.TdMsgMain;
import com.chis.modules.system.entity.TdMsgSub;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsTxrpt;
import com.chis.modules.system.entity.TsTxtype;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.MessageType;
import com.chis.modules.system.enumn.TxType;
import com.chis.modules.system.logic.BackNodeVO;
import com.chis.modules.system.logic.MetaCondition;
import com.chis.modules.system.protocol.PushMsgToAppImpl;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.service.CommServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

/**
 * 流程业务session bean 2015 3 31 最后修改------------ 2014-9-2 david 增加方法findTaskData
 * 
 * <AUTHOR>
 * @createDate 2014年9月3日下午6:43:06
 */
@Service
@Transactional(readOnly = false, rollbackFor = Throwable.class)
public class FlowBusinessServiceImpl extends AbstractTemplate implements
		Serializable {

	@Autowired
	private ActivitiServiceImpl activitiService;

	@Autowired
	private CommServiceImpl commService;

	private static final long serialVersionUID = 3576498110849853339L;

	private static String DYNA_FORM_CODE = "sys_dynaform";

	/**
	 * 根据任务ID,查询流程节点以及流程定义Key
	 * 
	 * @param taskId
	 * @return
	 */
	public TdFlowNode findNodeByTaskId(String processDefinitionId,
			String taskDefinitionKey) {
		if (StringUtils.isNotBlank(processDefinitionId)
				&& StringUtils.isNotBlank(taskDefinitionKey)) {

			StringBuilder hql = new StringBuilder();
			hql.append(
					"select t from TdFlowNode t where t.tdFlowDef.actDefId = '")
					.append(processDefinitionId).append("' ");
			hql.append(" and t.actNodeId = '").append(taskDefinitionKey)
					.append("'");
			List<TdFlowNode> resultList = this.em.createQuery(hql.toString())
					.getResultList();
			if (null != resultList && resultList.size() > 0) {
				TdFlowNode tdFlowNode = resultList.get(0);

				StringBuilder sql = new StringBuilder();
				sql.append(
						"SELECT T.KEY_ FROM ACT_RE_PROCDEF T WHERE T.ID_ = '")
						.append(tdFlowNode.getTdFlowDef().getActDefId())
						.append("'");
				List resultList2 = this.em.createNativeQuery(sql.toString())
						.getResultList();
				if (null != resultList2 && resultList2.size() > 0) {
					String key = String.valueOf(resultList2.get(0));
					tdFlowNode.setFlowKeyName(key);
				}
				return tdFlowNode;
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TdFlowType> findFlowTypeList() {
		List<TdFlowType> rtnList = new ArrayList<TdFlowType>(0);
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT A.TYPE_NAME, A.RID, A.PARENT_ID ");
		sb.append(" FROM TD_FLOW_TYPE A ");
		sb.append(" START WITH A.PARENT_ID IS NULL ");
		sb.append(" CONNECT BY PRIOR A.RID = A.PARENT_ID ");
		sb.append(" ORDER SIBLINGS BY A.RID ");
		List<Object[]> list = em.createNativeQuery(sb.toString())
				.getResultList();
		if (null != list && list.size() > 0) {
			for (Object[] o : list) {
				TdFlowType t = new TdFlowType();
				t.setTypeName(String.valueOf(o[0]));
				t.setRid(Integer.valueOf(o[1].toString()));
				if (null != o[2]) {
					t.setParent(new TdFlowType(Integer.valueOf(o[2].toString())));
				}
				rtnList.add(t);
			}
		}
		return rtnList;

	}

	@SuppressWarnings("unchecked")
	public String saveDef(TdFlowDef def) {

		// 部署流程定义
		String actDefId = null;
		try {
			actDefId = this.activitiService.deployProcessDefinition(
					def.getZipPath(), def.getZipName());
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			return "流程包设计有误，请重新上传！";
		}
		List<ActivityImpl> activitiImplList = this.activitiService
				.findTaskNodes(actDefId);

		// 插入TdFlowDef
		if (null == def.getRid()) {
			// 新增
			def.setActDefId(actDefId);
			def.setDefVersion(this.activitiService.findDefVersion(actDefId));
			def = (TdFlowDef) this.saveObj(def);
		}
		
		// 获取上个版本的流程定义
		TdFlowDef defEntity = this.getDefByActNodeId(actDefId);
		// 保存用户分配 TD_FLOW_DEF_USER
		this.saveDefUser(defEntity, def);
		// 保存角色分配  TD_FLOW_DEF_ROLE
		this.saveDefRole(defEntity, def);
		TdFlowNodePage dynaFormUrl = null;
		if (null != def.getIfDynaForm() && def.getIfDynaForm().intValue() == 1) {
			dynaFormUrl = this.getDynaFormUrl(DYNA_FORM_CODE);
		}
		// 插入TdFlowNode
		if (null != activitiImplList && activitiImplList.size() > 0) {
			// 获取上一个版本的节点集合
			List<TdFlowNode> nodeList = this.findNodeListByDefId(defEntity);
			for (int i = 0; i < activitiImplList.size(); i++) {
				ActivityImpl ai = activitiImplList.get(i);
				// 获取与上一个版本的相同节点内容
				TdFlowNode oldNode = this.getFlowNodeByDefId(nodeList, ai.getId());
				
				TdFlowNode node = new TdFlowNode();
				node.setTdFlowDef(def);
				node.setActNodeId(ai.getId());
				node.setNodeName(ai.getProperty("name").toString());
				node.setNums(i + 1);
				FlowInType type = FlowInType.XZTZ;
				if (i == activitiImplList.size()-1) {
					type = FlowInType.ZYTZ;
				}
				
				DealType dealType=  DealType.YBJD;
				short ifAdv = 0;
				short ifNbNode = 0;
				short ffMsg = 1;
				short fstNodeDisp = 1;
				
				// 上一版本的节点不为空
				if (oldNode != null) {
					type = oldNode.getFlowInType();
					dealType = oldNode.getDealType();
					ifAdv = oldNode.getIfAdv();
					ifNbNode = oldNode.getIfNbNode();
					ffMsg = oldNode.getFfMsg();
					fstNodeDisp = oldNode.getFstNodeDisp();
					dynaFormUrl = oldNode.getTdFlowNodePage();
				}
				
				node.setFlowInType(type);
				node.setDealType(dealType);
				node.setIfAdv(ifAdv);
				node.setIfNbNode(ifNbNode);
				node.setFfMsg(ffMsg);
				node.setFstNodeDisp(fstNodeDisp);
				node.setCreateDate(new Date());
				node.setCreateManid(def.getCreateManid());
				node.setJspUrl(dynaFormUrl == null ? null : dynaFormUrl.getPageUrl());
				node.setTdFlowNodePage(dynaFormUrl);
				node = (TdFlowNode) this.saveObj(node);
				
				// 上一版本的节点不为空
				if (oldNode != null) {
					// 保存节点脚本
					this.saveScript(oldNode.getRid(), node.getRid());
					// 保存代办人
					this.saveDbPeo(oldNode.getRid(), node.getRid());
					// 保存按钮
					this.saveBtn(oldNode.getRid(), node.getRid());
					// 保存通讯方式
					this.saveTxtype(oldNode.getRid(), node.getRid());
					// 保存意见模板
					this.saveAdviceTmp(oldNode.getRid(), node.getRid());
				}
			}
		}
		return null;
	}
	
	/**
	 * 保存分配的用户
	 * @param oldDef
	 * @param newDef
	 */
	public void saveDefUser(TdFlowDef oldDef, TdFlowDef newDef) {
		if (oldDef == null || oldDef.getRid() == null) {
			return ;
		}
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TdFlowDefUser t where t.tdFlowDef.rid = ").append(oldDef.getRid());
		List<TdFlowDefUser> data = this.em.createQuery(sb.toString()).getResultList();
		
		if (data != null && data.size() > 0) {
			for (TdFlowDefUser entity : data) {
				TdFlowDefUser newEntity = (TdFlowDefUser) this.copyEntity(entity);
				if (newEntity == null) {
					continue ;
				}
				newEntity.setRid(null);
				newEntity.setTdFlowDef(newDef);
				this.save(newEntity);
			}   
		}
	}
	
	/**
	 * 保存分配的角色
	 */
	public void saveDefRole(TdFlowDef oldDef, TdFlowDef newDef) {
		if (oldDef == null || oldDef.getRid() == null) {
			return ;
		}
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TdFlowDefRole t where t.tdFlowDef.rid = ").append(oldDef.getRid());
		List<TdFlowDefRole> data = this.em.createQuery(sb.toString()).getResultList();
		if (data != null && data.size() > 0) {
			for (TdFlowDefRole entity : data) {
				TdFlowDefRole newEntity = (TdFlowDefRole) this.copyEntity(entity);
				if (newEntity == null) {
					continue ;
				}
				newEntity.setRid(null);
				newEntity.setTdFlowDef(newDef);
				this.save(newEntity);
			}   
		}
	}
	
	
	/**
	 * 保存节点脚本
	 */
	public void saveScript(Integer oldNodeId, Integer newNodeId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TdFlowNodeScript t where t.tdFlowNode.rid = ").append(oldNodeId);
		List<TdFlowNodeScript> data = this.em.createQuery(sb.toString()).getResultList();
		if (data != null && data.size() > 0) {
			TdFlowNodeScript entity = data.get(0);
			TdFlowNodeScript newEntity = (TdFlowNodeScript) this.copyEntity(entity);
			if (newEntity == null) {
				return ;
			}
			newEntity.setRid(null);
			newEntity.setTdFlowNode(new TdFlowNode(newNodeId));
			this.save(newEntity);
		}
	}
	
	
	/**
	 * 保存按钮
	 */
	public void saveBtn(Integer oldNodeId, Integer newNodeId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TdFlowNodeBtn t where t.tdFlowNode.rid = ").append(oldNodeId);
		List<TdFlowNodeBtn> data = this.em.createQuery(sb.toString()).getResultList();
		if (data != null && data.size() > 0) {
			for (TdFlowNodeBtn entity : data) {
				TdFlowNodeBtn newEntity = (TdFlowNodeBtn) this.copyEntity(entity);
				if (newEntity == null) {
					continue ;
				}
				newEntity.setRid(null);
				newEntity.setTdFlowNode(new TdFlowNode(newNodeId));
				this.save(newEntity);
			}   
		}
	}
	
	/**
	 * 保存保存通讯方式
	 */
	public void saveTxtype(Integer oldNodeId, Integer newNodeId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TdFlowNodeTxType t where t.tdFlowNode.rid = ").append(oldNodeId);
		List<TdFlowNodeTxType> data = this.em.createQuery(sb.toString()).getResultList();
		if (data != null && data.size() > 0) {
			for (TdFlowNodeTxType entity : data) {
				TdFlowNodeTxType newEntity = (TdFlowNodeTxType) this.copyEntity(entity);
				if (newEntity == null) {
					continue ;
				}
				newEntity.setRid(null);
				newEntity.setTdFlowNode(new TdFlowNode(newNodeId));
				this.save(newEntity);
			}
		}
	}
	
	/**
	 * 保存代办人
	 */
	public void saveDbPeo(Integer oldNodeId, Integer newNodeId){
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TdFlowRuleNode t where t.tdFlowNode.rid = ").append(oldNodeId);
		List<TdFlowRuleNode> data = this.em.createQuery(sb.toString()).getResultList();
		if (data != null && data.size() > 0) {
			for (TdFlowRuleNode entity : data) {
				TdFlowRuleNode newEntity = (TdFlowRuleNode) this.copyEntity(entity);
				if (newEntity == null) {
					continue ;
				}
				newEntity.setRid(null);
				newEntity.setTdFlowNode(new TdFlowNode(newNodeId));
				this.save(newEntity);
			}
		}
	}
	
	/**
	 * 保存意见模板
	 */
	public void saveAdviceTmp(Integer oldNodeId, Integer newNodeId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TbFlowAdvtemplate t where t.tdFlowNode.rid = ").append(oldNodeId);
		List<TbFlowAdvtemplate> data = this.em.createQuery(sb.toString()).getResultList();
		
		if (data != null && data.size() > 0) {
			for (TbFlowAdvtemplate entity : data) {
				TbFlowAdvtemplate newEntity = (TbFlowAdvtemplate) this.copyEntity(entity);
				if (newEntity == null) {
					continue ;
				}
				newEntity.setRid(null);
				newEntity.setTdFlowNode(new TdFlowNode(newNodeId));
				this.save(newEntity);
			}
		}
	}
	
	/**
	 * 复制实体
	 * @param obj
	 * @return
	 */
	public Object copyEntity(Object obj){
		try {
			Class<?> classType = obj.getClass();
	        // 返回的新对象
	        Object objectCopy = classType.getConstructor(new Class[]{}).newInstance(new Object[]{});
	        //获得对象的所有成员变量
	        Field[] fields = classType.getDeclaredFields();
	        for(Field field : fields) {
	            //获取成员变量的名字
	            String name = field.getName(); 
	            
	            if ("serialVersionUID".equals(name)) {
	            	continue;
	            }

	            Object val = Reflections.getFieldValue(obj, name);
	            Reflections.setFieldValue(objectCopy, name, val);
	        }
	        return objectCopy;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	/**
	 * 根据流程定义的ACTNODEID查找流程定义
	 * @param actNodeId
	 */
	public TdFlowDef getDefByActNodeId(String actDefId) {
		String[] defId = actDefId.split(":");
		String likeActDefId = defId[0] + ":" + (Integer.valueOf(defId[1])-1);
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TdFlowDef t where t.actDefId like '").append(likeActDefId).append("%'");
		List<TdFlowDef> datas = this.em.createQuery(sb.toString()).getResultList();
		if (datas == null || datas.size() == 0) {
			return null;
		}
		return datas.get(0);
	}
	
	/**
	 * 根据流程定义Id查找节点集合
	 * @param defId
	 * @return
	 */
	public List<TdFlowNode> findNodeListByDefId(TdFlowDef def) {
		if (def == null || def.getRid() == null) {
			return new ArrayList<TdFlowNode>();
		}
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TdFlowNode t where t.tdFlowDef.rid = ").append(def.getRid());
		List<TdFlowNode> nodeList = this.em.createQuery(sb.toString()).getResultList();
		return nodeList;
	}
	
	/**
	 * 根据节点定义Id查找节点内容
	 * @param nodeList 节点集合 
	 * @param actNodeId 节点定义ID
	 * @return
	 */
	public TdFlowNode getFlowNodeByDefId(List<TdFlowNode> nodeList, String actNodeId) {
		if (nodeList == null || nodeList.size() == 0) {
			return null;
		}
		
		for (TdFlowNode node : nodeList) {
			if (node.getActNodeId().equals(actNodeId)) {
				return node;
			}
		}
		return null;
	}

	public void updateDef(TdFlowDef def) {
		this.update(def);
		TdFlowDef find = (TdFlowDef) this.find(TdFlowDef.class, def.getRid());
		// 更新表单地址
		// 如果是动态表单，则将其页面更新为动态表单页面，否则清空表单页面
		List<TdFlowNode> tdFlowNodeList = find.getTdFlowNodeList();
		if (null != tdFlowNodeList && tdFlowNodeList.size() > 0) {
			TdFlowNodePage dynaFormUrl = null;
			if (null != def.getIfDynaForm()
					&& def.getIfDynaForm().intValue() == 1) {
				dynaFormUrl = this.getDynaFormUrl(DYNA_FORM_CODE);
			}
			Short ifDynaForm = def.getIfDynaForm();
			// 如果是否动态表单修改，则节点上的页面也需要清空
			// 如果是动态表单，则将其页面更新为动态表单页面，否则清空表单页面
			for (TdFlowNode tdFlowNode : tdFlowNodeList) {
				TdFormDef tdFormDef = tdFlowNode.getTdFormDef();
				if (null != tdFormDef) {// 动态表单
					if (null == ifDynaForm || ifDynaForm.intValue() == 0) {
						tdFlowNode.setJspUrl(null);
						tdFlowNode.setTdFlowNodePage(null);
						tdFlowNode.setTdFormDef(null);
						this.update(tdFlowNode);
					}
				} else {
					if (null != ifDynaForm && ifDynaForm.intValue() == 1) {
						tdFlowNode.setJspUrl(null == dynaFormUrl ? null
								: dynaFormUrl.getPageUrl());
						tdFlowNode.setTdFlowNodePage(null == dynaFormUrl ? null
								: dynaFormUrl);
						tdFlowNode.setTdFormDef(null);
						this.update(tdFlowNode);
					}
				}

				// TdFlowNodePage tdFlowNodePage =
				// tdFlowNode.getTdFlowNodePage();
				// if( null != tdFlowNodePage &&
				// StringUtils.isNotBlank(tdFlowNodePage.getPageCode())){
				// //正常表单
				// if( (null == ifDynaForm || ifDynaForm.intValue() == 0) &&
				// tdFlowNodePage.getPageCode().equals(DYNA_FORM_CODE) ){
				// tdFlowNode.setJspUrl(null);
				// tdFlowNode.setTdFlowNodePage(null);
				// tdFlowNode.setTdFormDef(null);
				// this.update(tdFlowNode);
				// //动态表单
				// }else if(null != ifDynaForm && ifDynaForm.intValue() == 1 &&
				// !tdFlowNodePage.getPageCode().equals(DYNA_FORM_CODE)) {
				// tdFlowNode.setJspUrl( null == dynaFormUrl ? null :
				// dynaFormUrl.getPageUrl() );
				// tdFlowNode.setTdFlowNodePage( null == dynaFormUrl ? null :
				// dynaFormUrl);
				// tdFlowNode.setTdFormDef(null);
				// this.update(tdFlowNode);
				// }
				// }
			}
		}
	}

	@Transactional(readOnly = true)
	public Map<String, Integer> findRoleMapICanSee(Integer userId,
			Integer unitId) {
		Map<String, Integer> map = new LinkedHashMap<String, Integer>();
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT B.ROLE_NAME, B.RID ");
		sb.append(" FROM TS_USER_ROLE A INNER JOIN TS_ROLE B ON A.ROLE_ID = B.RID ");
		sb.append(" WHERE A.USER_INFO_ID = '").append(userId).append("' ");
		sb.append(" UNION ");
		sb.append(" SELECT D.ROLE_NAME, D.RID ");
		sb.append(" FROM TS_UNIT_ROLE C INNER JOIN TS_ROLE D ON C.ROLE_ID = D.RID ");
		sb.append(" WHERE C.UNIT_ID = '").append(unitId).append("' ");
		sb.append(" ORDER BY ROLE_NAME ");
		List<Object[]> list = em.createNativeQuery(sb.toString())
				.getResultList();
		if (null != list && list.size() > 0) {
			for (Object[] o : list) {
				map.put(o[0].toString(), Integer.valueOf(o[1].toString()));
			}
		}
		return map;
	}

	@Transactional(readOnly = true)
	public List<String> findRoleListIHave(Integer defId) {
		List<String> list = new ArrayList<String>(0);
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT A.ROLE_ID FROM TD_FLOW_DEF_ROLE A WHERE A.DEF_ID = '")
				.append(defId).append("'");
		List<Object> temList = em.createNativeQuery(sb.toString())
				.getResultList();
		if (null != temList && temList.size() > 0) {
			for (Object o : temList) {
				list.add(o.toString());
			}
		}
		return list;
	}

	public void grantRolesToDef(Integer defId, List<String> roleList) {
		StringBuilder sb = new StringBuilder();
		sb.append(" DELETE FROM TD_FLOW_DEF_ROLE T WHERE T.DEF_ID = '")
				.append(defId).append("' ");
		em.createNativeQuery(sb.toString()).executeUpdate();

		if (null != roleList && roleList.size() > 0) {
			TdFlowDef def = new TdFlowDef(defId);
			for (String roleId : roleList) {
				TdFlowDefRole tur = new TdFlowDefRole();
				tur.setRoleId(Integer.valueOf(roleId));
				tur.setTdFlowDef(def);
				this.save(tur);
			}
		}
	}

	@Transactional(readOnly = true)
	public List<TsOffice> findOfficeList(Integer unitId) {
		List<TsOffice> rtnList = new ArrayList<TsOffice>(0);
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT A.OFFICENAME, A.RID, A.UP_ID  ");
		sb.append(" FROM TS_OFFICE A  ");
		sb.append(" WHERE A.IF_REVEAL = '1' ");
		if (null != unitId) {
			sb.append(" AND A.UNIT_RID = '").append(unitId).append("' ");
		}
		sb.append(" START WITH A.UP_ID IS NULL ");
		sb.append(" CONNECT BY PRIOR A.RID = A.UP_ID ");
		sb.append(" ORDER SIBLINGS BY A.RID ");

		List<Object[]> list = em.createNativeQuery(sb.toString())
				.getResultList();
		if (null != list && list.size() > 0) {
			for (Object[] o : list) {
				TsOffice t = new TsOffice();
				t.setOfficename(String.valueOf(o[0]));
				t.setRid(Integer.valueOf(o[1].toString()));
				if (null != o[2]) {
					t.setParentOffice(new TsOffice(Integer.valueOf(o[2]
							.toString())));
				}
				rtnList.add(t);
			}
		}
		return rtnList;
	}

	@Transactional(readOnly = true)
	public List<TsUserInfo> findUsers(Integer unitId, Integer officeId,
			String filterIds) {
		List<TsUserInfo> rtnList = new ArrayList<TsUserInfo>();
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT D.RID, D.USERNAME, D.OFFICENAMES,D.OFFICEIDS ");
		sb.append(" FROM ( SELECT T1.RID, T1.USERNAME,LISTAGG(C.OFFICENAME,',') WITHIN GROUP(ORDER BY DECODE(C.ZKS, 1, -1, C.NUM)) AS OFFICENAMES");
		sb.append(" , ','||LISTAGG(C.OFFICEID,',') WITHIN GROUP(ORDER BY C.OFFICEID)||',' AS OFFICEIDS ");
		sb.append(" ,SUM(DECODE(C.ZKS, 1, C.NUM, NULL)) AS ZKSNUM, C.EMPNUM FROM TS_USER_INFO T1 ");

		sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.UNIT_RID,B.RID OFFICEID ,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
		sb.append(" UNION ALL");
		sb.append(" SELECT A.RID,C.OFFICENAME,C.UNIT_RID,C.RID OFFICEID,C.NUM, A.NUM EMPNUM, 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
		sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID ) C ON C.RID = T1.EMP_ID");

		sb.append(" WHERE T1.IF_REVEAL = '1' ");
		if (null != unitId) {
			sb.append(" AND C.UNIT_RID = '").append(unitId).append("' ");
		}

		if (StringUtils.isNotBlank(filterIds)) {
			sb.append(" AND T1.RID NOT IN (").append(filterIds).append(") ");
		}
		sb.append(" GROUP BY T1.RID, T1.USERNAME,C.EMPNUM ) D");
		if (null != officeId) {
			sb.append(" WHERE D.OFFICEIDS LIKE '%,").append(officeId)
					.append(",%'");
		}
		sb.append(" ORDER BY D.ZKSNUM ,D.EMPNUM");

		List<Object[]> temList = em.createNativeQuery(sb.toString())
				.getResultList();
		if (null != temList && temList.size() > 0) {
			for (Object[] o : temList) {
				TsUserInfo tui = new TsUserInfo(
						Integer.valueOf(o[0].toString()));
				tui.setUsername(o[1] == null ? "" : o[1].toString());
				tui.setOfficeName(o[2] == null ? "" : o[2].toString());
				rtnList.add(tui);
			}
		}
		return rtnList;
	}

	@Transactional(readOnly = true)
	public List<TsUserInfo> findUsers(Integer defId) {
		// 2015-3-31 xt 新增过滤兼职科室
		List<TsUserInfo> rtnList = new ArrayList<TsUserInfo>();
		StringBuilder sb = new StringBuilder(
				" SELECT T1.RID, T1.USERNAME,LISTAGG(C.OFFICENAME,',') WITHIN GROUP(ORDER BY DECODE(c.ZKS, 1, -1, C.NUM)) AS OFFICENAMES ");
		sb.append(" FROM  TD_FLOW_DEF_USER T0 ");
		sb.append(" INNER JOIN TS_USER_INFO T1 ON T0.USER_INFO_ID = T1.RID ");
		sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME ,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
		sb.append(" UNION ALL SELECT * FROM ( ");
		sb.append(" SELECT A.RID,C.OFFICENAME ,C.NUM, A.NUM EMPNUM, 0 AS ZKS  FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
		sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");

		sb.append(" WHERE T1.IF_REVEAL = '1' ");
		sb.append(" AND T0.DEF_ID = '").append(defId).append("' ");
		sb.append(" GROUP BY T1.RID, T1.USERNAME ,C.EMPNUM");
		sb.append(" ORDER BY SUM(DECODE(C.ZKS, 1, C.NUM, NULL)),C.EMPNUM");

		List<Object[]> temList = em.createNativeQuery(sb.toString())
				.getResultList();
		if (null != temList && temList.size() > 0) {
			for (Object[] o : temList) {
				TsUserInfo tui = new TsUserInfo(
						Integer.valueOf(o[0].toString()));
				tui.setUsername(o[1] == null ? "" : o[1].toString());
				tui.setOfficeName(o[2] == null ? "" : o[2].toString());
				rtnList.add(tui);
			}
		}
		return rtnList;
	}

	public void yhfpRole(List<TsUserInfo> storedTargetList,
			List<TsUserInfo> targetList, Integer defId) {
		StringBuilder sb = new StringBuilder();
		if (null != storedTargetList && storedTargetList.size() > 0) {
			for (TsUserInfo t : storedTargetList) {
				sb.append(",").append(t.getRid());
			}
			String ids = sb.toString();
			if (StringUtils.isNotBlank(ids)) {
				ids = ids.substring(1);
				sb = new StringBuilder(
						" DELETE FROM TD_FLOW_DEF_USER T WHERE  T.DEF_ID ='");
				sb.append(defId).append("' AND T.USER_INFO_ID IN (")
						.append(ids).append(")");
				em.createNativeQuery(sb.toString()).executeUpdate();
			}
		}

		if (null != targetList && targetList.size() > 0) {
			TdFlowDef def = new TdFlowDef(defId);
			for (TsUserInfo t : targetList) {
				TdFlowDefUser tur = new TdFlowDefUser();
				tur.setUserInfoId(t.getRid());
				tur.setTdFlowDef(def);
				this.save(tur);
			}
		}

	}

	@Transactional(readOnly = true)
	public List<TdFlowNode> findFlowNodes(Integer defId) {
		StringBuilder sb = new StringBuilder(
				" select t from TdFlowNode t where t.tdFlowDef.rid = '");
		sb.append(defId).append("' order by t.nums ");
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public TdFlowNode findTdFlowNode(Integer rid) {
		return (TdFlowNode) this.find(TdFlowNode.class, rid);
	}

	public String updateTdFlowNode(TdFlowNode node, List<String> bnbList) {
		/**
		 * 如果是拟办节点，先检查该流程是否有其他拟办节点，有责返回错误
		 * 
		 */
		TdFlowNode oldNode = (TdFlowNode) this.find(TdFlowNode.class,
				node.getRid());
		if (node.getFlowInType().equals(FlowInType.NBJD)) {
			StringBuilder sb = new StringBuilder();
			sb.append(
					" SELECT T.RID FROM TD_FLOW_NODE T WHERE T.FLOWIN_TYPE = '")
					.append(FlowInType.NBJD).append("' AND T.DEF_ID ='");
			sb.append(oldNode.getTdFlowDef().getRid())
					.append("' AND T.RID <> '").append(oldNode.getRid())
					.append("' ");
			List list = em.createNativeQuery(sb.toString()).getResultList();
			if (null != list && list.size() > 0) {
				return "该流程已有拟办节点，不允许再有其它拟办节点！";
			} else {
				if (null != bnbList && bnbList.size() > 0) {
					// 先将原来的被拟办节点恢复
					sb = new StringBuilder();
					sb.append(
							" UPDATE TD_FLOW_NODE SET IF_NB_NODE = '0', FLOWIN_TYPE='")
							.append(FlowInType.XZTZ)
							.append("'  WHERE DEF_ID = '")
							.append(oldNode.getTdFlowDef().getRid())
							.append("' ");
					em.createNativeQuery(sb.toString()).executeUpdate();

					this.update(node);

					for (String bnbId : bnbList) {
						sb = new StringBuilder();
						sb.append(
								" UPDATE TD_FLOW_NODE SET IF_NB_NODE = '1', FLOWIN_TYPE='")
								.append(FlowInType.ZYTZ)
								.append("' WHERE RID = '").append(bnbId)
								.append("' ");
						em.createNativeQuery(sb.toString()).executeUpdate();
					}
				} else {
					return "请选择被拟办的节点！";
				}
			}
		} else {
			/**
			 * 先判断原来是不是拟办节点，如果是，要将该流程的被拟办节点设为不是被拟办节点
			 */
			if (oldNode.getFlowInType().equals(FlowInType.NBJD)) {
				StringBuilder sb = new StringBuilder();
				sb.append(
						" UPDATE TD_FLOW_NODE SET IF_NB_NODE = '0', FLOWIN_TYPE='")
						.append(FlowInType.XZTZ).append("' WHERE DEF_ID = '")
						.append(oldNode.getTdFlowDef().getRid()).append("' ");
				em.createNativeQuery(sb.toString()).executeUpdate();
			}
			this.update(node);
		}
		return null;
	}

	@Transactional(readOnly = true)
	public Map<String, String> findDirectoryDatas(DirectoryCondition condition,
			String ruleCode) {
		try {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TdFlowRule t where t.idcode = '")
					.append(ruleCode).append("' ");
			List<TdFlowRule> list = em.createQuery(sb.toString())
					.getResultList();
			if (null != list && list.size() > 0) {
				TdFlowRule t = list.get(0);
				IActivitiRuleService service = SpringContextHolder.getBean(t
						.getImpClass());
				return service.selectDirectoryDatas(condition, em);
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
		return new HashMap<String, String>();
	}

	@Transactional(readOnly = true)
	public Map<String, String> findSelectedRules(Integer nodeId) {
		Map<String, String> map = new LinkedHashMap<String, String>();
		StringBuilder sb = new StringBuilder("");
		sb.append(" select t from TdFlowRuleNode t where t.tdFlowNode.rid =")
				.append(nodeId);
		List<TdFlowRuleNode> list = em.createQuery(sb.toString())
				.getResultList();
		if (null != list && list.size() > 0) {
			for (TdFlowRuleNode t : list) {
				String selectIds = t.getSelectIds();
				String selectNames = t.getSelectNames();
				if (StringUtils.isNotBlank(selectIds)) {
					String[] s1 = selectIds.split(",");
					String[] s2 = selectNames.split(",");
					for (int i = 0; i < s1.length; i++) {
						map.put(s2[i], s2[i] + "_" + s1[i] + "_"
								+ t.getTdFlowRule().getIdcode());
					}
				}
			}
		}
		return map;
	}

	public void saveOrUpdateFlowRuleNode(Integer nodeId,
			Collection<String> selectIds) {
		StringBuilder sb = new StringBuilder();
		sb = new StringBuilder(
				" DELETE FROM TD_FLOW_RULE_NODE WHERE FLOW_NODE_ID = '");
		sb.append(nodeId).append("' ");
		em.createNativeQuery(sb.toString()).executeUpdate();

		Map<Integer, String[]> saveMap = new HashMap<Integer, String[]>();
		if (null != selectIds && selectIds.size() > 0) {
			sb = new StringBuilder();
			sb.append(" SELECT T.IDCODE, T.RID FROM TD_FLOW_RULE T ");
			List<Object[]> list = em.createNativeQuery(sb.toString())
					.getResultList();
			Map<String, Integer> ruleMap = new HashMap<String, Integer>();
			if (null != list && list.size() > 0) {
				for (Object[] o : list) {
					ruleMap.put(o[0].toString(),
							Integer.valueOf(o[1].toString()));
				}
			}

			// 字典中文_字典ID_规则编码
			for (String selectId : selectIds) {
				String[] arr = selectId.split("_");
				Integer ruleId = ruleMap.get(arr[2]);

				// 0-ids 1-中文
				String[] values = saveMap.get(ruleId);
				if (null != values && values.length == 2) {
					values[0] = values[0] + "," + arr[1];
					values[1] = values[1] + "," + arr[0];
				} else {
					values = new String[2];
					values[0] = arr[1];
					values[1] = arr[0];
				}
				saveMap.put(ruleId, values);
			}

			Set<Integer> keySet = saveMap.keySet();
			for (Integer key : keySet) {
				// 0-ids 1-中文
				String[] values = saveMap.get(key);
				TdFlowRuleNode t = new TdFlowRuleNode();
				t.setTdFlowNode(new TdFlowNode(nodeId));
				t.setTdFlowRule(new TdFlowRule(key));
				t.setSelectIds(values[0]);
				t.setSelectNames(values[1]);
				this.save(t);
			}
		}
	}

	@Transactional(readOnly = true)
	public List<TdFlowType> searchMyFlowTypes(Integer userId) {
		List<TdFlowType> rtnList = new ArrayList<TdFlowType>();
		StringBuilder sb = new StringBuilder(
				" SELECT  LISTAGG(T.ROLE_ID,',') WITHIN GROUP(ORDER BY T.ROLE_ID) AS ROLS ");
		sb.append(" FROM TS_USER_ROLE T WHERE T.USER_INFO_ID = '")
				.append(userId).append("' ");
		List<Object> list = em.createNativeQuery(sb.toString()).getResultList();
		String roles = null;
		if (null != list && list.size() > 0) {
			Object o = list.get(0);
			if (null != o) {
				roles = o.toString();
			}
		}

		sb = new StringBuilder();
		sb.append("select a.* from (");
		sb.append(" WITH DEF AS (SELECT T4.RID, T4.DEF_NAME, T4.ACT_DEF_ID, T4.CREATE_MANID, T5.RID AS TYPE_ID, T5.TYPE_NAME, T5.PIC_PATH, ");
		sb.append(" T4.IF_DYNA_FORM,T4.DYNA_FORM_ID,T4.nums,T5.nums AS typeNums ");
		sb.append(" FROM ACT_RE_PROCDEF T2  ");
		sb.append(" INNER JOIN (SELECT MAX(T1.VERSION_) AS VERSION_, T1.KEY_ FROM ACT_RE_PROCDEF T1 GROUP BY T1.KEY_) T3 ON T2.VERSION_ = T3.VERSION_ AND T2.KEY_ = T3.KEY_  ");
		sb.append(" INNER JOIN TD_FLOW_DEF T4 ON T4.ACT_DEF_ID = T2.ID_  ");
		sb.append(" INNER JOIN TD_FLOW_TYPE T5 ON T4.TPYE_ID = T5.RID order by t4.nums) ");
		sb.append(" SELECT * FROM DEF WHERE DEF.CREATE_MANID = '")
				.append(userId).append("' ");
		sb.append(" UNION ");
		sb.append(" SELECT D1.* FROM DEF D1 ");
		sb.append(" INNER JOIN TD_FLOW_DEF_USER L1 ON L1.DEF_ID = D1.RID ");
		sb.append(" WHERE L1.USER_INFO_ID = '").append(userId).append("' ");
		if (StringUtils.isNotBlank(roles)) {
			sb.append(" UNION ");
			sb.append(" SELECT D1.* FROM DEF D1 ");
			sb.append(" INNER JOIN TD_FLOW_DEF_ROLE L2 ON L2.DEF_ID = D1.RID ");
			sb.append(" WHERE L2.ROLE_ID IN(").append(roles).append(")	");
		}
		sb.append(")a order by a.typeNums, a.nums");
		List<Object[]> temList = em.createNativeQuery(sb.toString())
				.getResultList();
		if (null != temList && temList.size() > 0) {
			Map<Integer, TdFlowType> map = new LinkedHashMap<Integer, TdFlowType>();

			for (Object[] o : temList) {
				Integer typeId = Integer.valueOf(o[4].toString());
				TdFlowType type = map.get(typeId);
				if (null == type) {
					type = new TdFlowType(typeId);
					type.setTypeName(o[5] == null ? "" : o[5].toString());
					type.setPicPath(o[6] == null ? "" : o[6].toString());
					type.setTdFlowDefList(new ArrayList<TdFlowDef>());
				}

				TdFlowDef def = new TdFlowDef(Integer.valueOf(o[0].toString()));
				def.setDefName(o[1] == null ? "" : o[1].toString());
				def.setActDefId(o[2] == null ? "" : o[2].toString());
				def.setCreateManid(Integer.valueOf(o[3].toString()));
				def.setIfDynaForm(o[7] == null ? null : Short.valueOf(o[7]
						.toString()));
				def.setDynaFormId((o[8] == null ? null : Integer.valueOf(o[8]
						.toString())));
				type.getTdFlowDefList().add(def);

				map.put(typeId, type);
			}
			Collection<TdFlowType> values = map.values();
			for (TdFlowType t : values) {
				rtnList.add(t);
			}
		}

		return rtnList;
	}

	@Deprecated
	public String startProcessInstance(TdFlowDef def, Integer userId,
			String username, String bussinessId, String actNodeKey) {
		Map<String, Object> map = new HashMap<String, Object>();
		// owner是流程定义中的变量
		// map.put("owner", userId.toString());
		map.put(actNodeKey + "_assignees", userId.toString());
		if (StringUtils.isNotBlank(bussinessId)) {
			map.put("businessId", bussinessId);
		}
		StringBuilder sb = new StringBuilder();
		sb.append(username)
				.append("发起的")
				.append(def.getDefName())
				.append("【")
				.append(DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"))
				.append("】");
		return this.activitiService.startProcessInstance(def.getActDefId(),
				map, userId.toString(), sb.toString());
	}

	public String startProcessInstanceNew(TdFlowDef def, Integer userId,
			String businessKey, String bussinessId, String actNodeKey) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put(actNodeKey + "_assignees", userId.toString());
		if (StringUtils.isNotBlank(bussinessId)) {
			map.put("businessId", bussinessId);
		}
		return this.activitiService.startProcessInstance(def.getActDefId(),
				map, userId.toString(), businessKey);
	}

	public void saveVariable(String taskId, Map<String, Object> map) {
		this.activitiService.saveVariable(taskId, map);
	}

	@Transactional(readOnly = true)
	public List<Object[]> findHisTaskRecord(String flowId, int type) {
		List<Object[]> list = new ArrayList<Object[]>();
		// type 1-任务id 2-流程实例ID
		String instanceId = null;
		if (type == 1) {
			instanceId = this.activitiService.findHisTaskById(flowId)
					.getProcessInstanceId();
		} else {
			instanceId = this.activitiService.findInstanceById(flowId).getId();
		}

		if (StringUtils.isNotBlank(instanceId)) {
			StringBuilder sb = new StringBuilder();
			sb.append("WITH M AS (");

			// 首节点未处理的
			sb.append(" SELECT A1.NAME_, T1.USERNAME, '' AS DEAL_TIME, '' AS DEAL_ADVICE, A1.ID_ AS TASKID, T1.RID AS USERID ");
			sb.append(" FROM ACT_RU_TASK A1 ");
			sb.append(" INNER JOIN TS_USER_INFO T1 ON A1.ASSIGNEE_ = T1.RID ");
			sb.append(" WHERE A1.PROC_INST_ID_ = '").append(instanceId)
					.append("' AND A1.ASSIGNEE_ IS NOT NULL ");

			sb.append(" UNION ALL ");

			// 其他节点未处理的
			sb.append(" SELECT A1.NAME_, T1.USERNAME, '' AS DEAL_TIME, '' AS DEAL_ADVICE, A1.ID_ AS TASKID, T1.RID AS USERID ");
			sb.append(" FROM ACT_RU_TASK A1 ");
			sb.append(" INNER JOIN ACT_RU_IDENTITYLINK A2 ON A2.TASK_ID_ = A1.ID_ AND A2.TYPE_ = 'candidate' ");
			sb.append(" INNER JOIN TS_USER_INFO T1 ON A2.USER_ID_ = T1.RID ");
			sb.append(" WHERE A1.PROC_INST_ID_ = '").append(instanceId)
					.append("' AND A1.ASSIGNEE_ IS NULL ");

			sb.append(" UNION ALL ");

			// 已处理的节点
			sb.append(" SELECT W.* FROM (");
			sb.append(" SELECT T2.NAME_, T3.USERNAME, TO_CHAR(T1.DEAL_TIME, 'YYYY-MM-DD HH24:MI:SS'), T1.DEAL_ADVICE, ");
			sb.append(" T2.ID_ AS TASKID, T3.RID AS USERID ");
			sb.append(" FROM ACT_HI_TASKRCD T1 ");
			sb.append(" INNER JOIN ACT_HI_TASKINST T2 ON T1.TASK_ID = T2.ID_ ");
			sb.append(" INNER JOIN TS_USER_INFO T3 ON T1.USER_ID = T3.RID ");
			sb.append(" WHERE T1.PROC_INST_ID = '").append(instanceId)
					.append("' ");
			// sb.append(" ORDER BY T1.DEAL_TIME DESC ");
			sb.append(" ) W ");

			sb.append(")");
			sb.append(" SELECT M.*, H.TYPES, H.SEND_STATUS, H.RECEIVE_STATUS, TO_CHAR(H.RECEIVE_TIME, 'YYYY-MM-DD HH24:MI:SS') AS RECEIVE_TIME, H.MSG_ERR FROM M ");

			sb.append(" LEFT JOIN ");
			sb.append(" ( ");
			sb.append(" SELECT M1.TARGET_TASK_ID, M3.TYPES, M2.SEND_STATUS, M2.RECEIVE_STATUS, M2.RECEIVE_TIME, M2.RID AS MSGID, M2.TAR_USER_ID, M2.MSG_ERR ");
			sb.append(" FROM TD_EM_TXRPT_CASE M1 ");
			sb.append(" INNER JOIN TS_TXRPT M2 ON M1.RPT_ID = M2.RID  ");
			sb.append(" INNER JOIN TS_TXTYPE M3 ON M2.TX_ID = M3.RID ");
			sb.append(" ) H ON  M.TASKID = H.TARGET_TASK_ID AND M.USERID = H.TAR_USER_ID ");

			sb.append(" ORDER BY M.DEAL_TIME, TO_NUMBER(M.TASKID) ");

			list = em.createNativeQuery(sb.toString()).getResultList();
		}
		
		// 根据流程实例和任务ID  查询提交给哪些用户；
		List<Object[]> dataList = new ArrayList<Object[]>();
		if (list != null && list.size() > 0) {
			StringBuilder sb = new StringBuilder();
			for (Object[] obj : list) {
				Object[] map = new Object[13];
				System.arraycopy(obj, 0, map, 0, obj.length);
				sb = new StringBuilder();
				sb.append(" SELECT NEXT_USER_IDS FROM ACT_HI_TASKRCD WHERE PROC_INST_ID = '").append(instanceId).append("'");
				sb.append(" AND TASK_ID = '").append(obj[4]).append("' AND NEXT_USER_IDS IS NOT NULL");
				List<Object> next_user_ids = this.em.createNativeQuery(sb.toString()).getResultList();
				if (next_user_ids != null && next_user_ids.size() > 0) {
					sb = new StringBuilder();
					sb.append(" select wm_concat(username) from ts_user_info a where a.rid in (").append(next_user_ids.get(0)).append(")");
					map[12] = this.em.createNativeQuery(sb.toString()).getSingleResult();
				}
				
				dataList.add(map);
			}
		}
		
		return dataList;
	}

	@Transactional(readOnly = true)
	public Map<TdFlowNode, MutiNodeDealBean> findUsersByNodes(
			ResultCondition condition, String taskId, List<TdFlowNode> nodeList) {
		Map<TdFlowNode, MutiNodeDealBean> rtnMap = new LinkedHashMap<TdFlowNode, MutiNodeDealBean>();
		ProcessInstance pi = this.activitiService
				.findProcessInstanceByTaskId(taskId);
		condition.setProcessInstanceId(pi.getId());

		StringBuilder sb = null;
		for (TdFlowNode node : nodeList) {
			MutiNodeDealBean dealBean = new MutiNodeDealBean(node);

			condition.setNodeId(node.getRid().toString());

			Map<Integer, TsUserInfo> map = new HashMap<Integer, TsUserInfo>();
			List<TsUserInfo> list = new ArrayList<TsUserInfo>();

			sb = new StringBuilder(
					" select t from TdFlowRuleNode t where t.tdFlowNode.rid =");
			sb.append(condition.getNodeId());
			List<TdFlowRuleNode> ruleNodeList = em.createQuery(sb.toString())
					.getResultList();
			if (null != ruleNodeList && ruleNodeList.size() > 0) {

				for (TdFlowRuleNode t : ruleNodeList) {
					// 设置selectIds
					condition.setSelectIds(t.getSelectIds());
					TdFlowRule flowRule = t.getTdFlowRule();
					try {
						IActivitiRuleService ruleService = SpringContextHolder
								.getBean(flowRule.getImpClass());
						List<TsUserInfo> userList = ruleService.findMan(
								condition, em);
						if (null != userList && userList.size() > 0) {
							for (TsUserInfo user : userList) {
								map.put(user.getRid(), user);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
						TransactionAspectSupport.currentTransactionStatus()
								.setRollbackOnly();
					}
				}

				if (null != map && map.size() > 0) {
					Collection<TsUserInfo> coll = map.values();
					if (null != coll && coll.size() > 0) {
						for (TsUserInfo user : coll) {
							list.add(user);
						}
					}

					dealBean.setUserList(list);
				}
			}

			rtnMap.put(node, dealBean);
		}

		return rtnMap;
	}

	@Transactional(readOnly = true)
	public List<TsUserInfo> findUsersByNodeId(ResultCondition condition,
			String taskId) {
		List<TsUserInfo> list = new ArrayList<TsUserInfo>();
		if (StringUtils.isNotBlank(taskId)) {
			// 通过taskId设置processInstanceId
			ProcessInstance pi = this.activitiService
					.findProcessInstanceByTaskId(taskId);
			if (null != pi) {
				condition.setProcessInstanceId(pi.getId());
			} else {
				throw new RuntimeException("该任务已提交！");
			}
			list = this.findUsersByNodeId(condition);
		}
		return list;
	}

	@Transactional(readOnly = true)
	public List<TsUserInfo> findUsersByNodeId(ResultCondition condition) {
		Map<Integer, TsUserInfo> map = new LinkedHashMap<Integer, TsUserInfo>();
		List<TsUserInfo> list = new ArrayList<TsUserInfo>();

		StringBuilder sb = new StringBuilder(
				" select t from TdFlowRuleNode t where t.tdFlowNode.rid =");
		sb.append(condition.getNodeId());
		List<TdFlowRuleNode> ruleNodeList = em.createQuery(sb.toString())
				.getResultList();
		if (null != ruleNodeList && ruleNodeList.size() > 0) {

			for (TdFlowRuleNode t : ruleNodeList) {
				// 设置selectIds
				condition.setSelectIds(t.getSelectIds());
				TdFlowRule flowRule = t.getTdFlowRule();
				try {
					IActivitiRuleService ruleService = SpringContextHolder
							.getBean(flowRule.getImpClass());
					List<TsUserInfo> userList = ruleService.findMan(condition,
							em);
					if (null != userList && userList.size() > 0) {
						for (TsUserInfo user : userList) {
							map.put(user.getRid(), user);
						}
					}
				} catch (Exception e) {
					e.printStackTrace();
					TransactionAspectSupport.currentTransactionStatus()
							.setRollbackOnly();
				}
			}
			if (null != map && map.size() > 0) {
				Collection<TsUserInfo> coll = map.values();
				if (null != coll && coll.size() > 0) {
					for (TsUserInfo user : coll) {
						list.add(user);
					}
				}
			}
		}

		return list;
	}

	@Transactional(readOnly = true)
	public TdFlowNode findNextNode(String taskId, String defId,
			Map<String, Object> variables) {
		// 下一节点
		TdFlowNode node = null;

		ProcessInstance processInstance = this.activitiService
				.findProcessInstanceByTaskId(taskId);
		TaskDefinition task = this.activitiService.nextTaskDefinition(
				processInstance.getId(), variables);
		if (null != task) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TdFlowNode t where t.actNodeId = '")
					.append(task.getKey()).append("' and t.tdFlowDef.rid = '")
					.append(defId).append("' ");
			List<TdFlowNode> list = em.createQuery(sb.toString())
					.getResultList();
			if (null != list && list.size() > 0) {
				node = list.get(0);
			}
		}
		return node;
	}

	@Transactional(readOnly = true)
	public List<TdFlowNode> findNextNodeList(String taskId, String defId,
			Map<String, Object> variables, TdFlowNode currentNode) {
		ProcessInstance processInstance = this.activitiService
				.findProcessInstanceByTaskId(taskId);

		List<HistoricVariableInstance> allHisVariables = this.activitiService
				.findAllHisVariables(processInstance.getId());
		if (null == variables) {
			variables = new HashMap<String, Object>();
		}
		if (null != allHisVariables && allHisVariables.size() > 0) {
			for (HistoricVariableInstance hvi : allHisVariables) {
				if (!variables.containsKey(hvi.getVariableName())) {
					variables.put(hvi.getVariableName(), hvi.getValue());
				}
			}
		}

		List<PvmActivity> taskDefList = this.activitiService.findNextTaskDefs(
				currentNode.getActNodeId(), processInstance.getId(), variables);
		if (null != taskDefList && taskDefList.size() > 0) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TdFlowNode t where t.tdFlowDef.rid = '")
					.append(defId).append("' and t.actNodeId in (");
			for (int i = 0; i < taskDefList.size(); i++) {
				PvmActivity task = taskDefList.get(i);
				if (i == taskDefList.size() - 1) {
					sb.append("'").append(task.getId()).append("') ");
				} else {
					sb.append("'").append(task.getId()).append("', ");
				}
			}
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TdFlowNode> findNbNextNodeList(String taskId, String defId,
			Map<String, Object> variables, TdFlowNode currentNode) {
		ProcessInstance processInstance = this.activitiService
				.findProcessInstanceByTaskId(taskId);
		Map<String, Object> processVariables = this.activitiService
				.findAllVariables(processInstance.getId());
		if (null == processVariables) {
			processVariables = new HashMap<String, Object>();
		}
		if (null != variables) {
			processVariables.putAll(variables);
		}

		List<PvmActivity> taskDefList = this.activitiService.findNextTaskDefs(
				currentNode.getActNodeId(), processInstance.getId(),
				processVariables);

		if (null != taskDefList && taskDefList.size() > 0) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TdFlowNode t where t.tdFlowDef.rid = '")
					.append(defId).append("' and ((t.actNodeId in (");
			for (int i = 0; i < taskDefList.size(); i++) {
				PvmActivity task = taskDefList.get(i);
				if (i == taskDefList.size() - 1) {
					sb.append("'").append(task.getId()).append("')) ");
				} else {
					sb.append("'").append(task.getId()).append("', ");
				}
			}
			sb.append(" or (t.ifNbNode = 1 and t.nums > ")
					.append(currentNode.getNums()).append(") )");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

	public String submitFlow(String taskId, Map<String, Object> variables,
			String advice, Integer userId, Integer trustId) {
		ProcessInstance pi = this.activitiService
				.findProcessInstanceByTaskId(taskId);
		/**
		 * 保存流程历史提交记录
		 */
		List<TsUserInfo> selectedUserList = null;
		if (variables != null) {
			selectedUserList = (List<TsUserInfo>) variables.get("selectedUserList");
			variables.remove("selectedUserList");
		}
		
		this.saveActRcdInfo(advice, pi.getId(), taskId, userId, ActOptType.S.toString(), trustId, selectedUserList);

		/**
		 * 是否退回，0-否 1-是
		 */
		if (null == variables) {
			variables = new HashMap<String, Object>();
		}
		variables.put("ACT_OPT_TYPE", "S");
		variables.put("if_back", "0");
		this.activitiService.commitProcess(taskId, variables, null);
		return pi.getId();
	}

	public String backFlow(String taskId, Map<String, Object> variables,
			String advice, String activitiId, Integer userId, Integer trustId) {
		ProcessInstance pi = this.activitiService
				.findProcessInstanceByTaskId(taskId);
		/**
		 * 保存流程历史提交记录
		 */
		List<TsUserInfo> selectedUserList = null;
		if (variables != null) {
			selectedUserList = (List<TsUserInfo>) variables.get("selectedUserList");
			variables.remove("selectedUserList");
		}
		
		this.saveActRcdInfo(advice, pi.getId(), taskId, userId, ActOptType.R.toString(), trustId, selectedUserList);

		/**
		 * 是否退回，0-否 1-是
		 */
		if (null == variables) {
			variables = new HashMap<String, Object>();
		}
		variables.put("ACT_OPT_TYPE", "R");
		variables.put("if_back", "1");
		this.activitiService.backTask(taskId, variables, activitiId);
		return pi.getId();
	}
	
	public void backFlow(String taskId, String advice, String activitiId, Integer userId, Integer trustId, Map<String, Object> variables) {
		ProcessInstance pi = this.activitiService
				.findProcessInstanceByTaskId(taskId);
		/**
		 * 保存流程历史提交记录
		 */
		List<TsUserInfo> selectedUserList = null;
		if (variables != null) {
			selectedUserList = (List<TsUserInfo>) variables.get("selectedUserList");
			variables.remove("selectedUserList");
		}
		
		this.saveActRcdInfo(advice, pi.getId(), taskId, userId, ActOptType.S.toString(), trustId, selectedUserList);
		
		variables = new HashMap<String, Object>();
		variables.put("ACT_OPT_TYPE", "R");
		variables.put("if_back", "1");
			
		this.activitiService.rebutProcess(taskId, variables, activitiId);
	}

	@Transactional(readOnly = true)
	public Map<String, String> findBackableNodes(String taskId) {
		Map<String, String> map = new LinkedHashMap<String, String>();
		try {
			List<ActivityImpl> list = this.activitiService
					.findBackAvtivity(taskId);
			if (null != list && list.size() > 0) {
				for (ActivityImpl ai : list) {
					map.put((String) ai.getProperty("name"), ai.getId());
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException("查找退回节点失败！");
		}
		return map;
	}

	@Transactional(readOnly = true)
	public InputStream findImageStream(String flowId, int type) {
		return this.activitiService.findImageStream(flowId, type);
	}

	@Transactional(readOnly = true)
	public TdFlowNode findFirstNode(Integer defId) {
		TdFlowNode node = null;
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TdFlowNode t where t.tdFlowDef.rid =")
				.append(defId).append(" and t.nums = 1");
		List<TdFlowNode> list = em.createQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			node = list.get(0);
			node.getTdFlowNodeBtnList().size();
		}
		return node;
	}

	@Transactional(readOnly = true)
	public String findCurrentTaskId(String processInstanceId) {
		String rtnStr = null;
		StringBuilder sb = new StringBuilder();
		sb.append("  SELECT T2.ID_, T2.NAME_  FROM ACT_RU_EXECUTION T1 ");
		sb.append("  INNER JOIN ACT_RU_TASK T2 ON T1.PROC_INST_ID_ = T2.PROC_INST_ID_ AND T1.ACT_ID_ = T2.TASK_DEF_KEY_ ");
		sb.append(" WHERE T1.PROC_INST_ID_ = '").append(processInstanceId)
				.append("' ");
		List<Object[]> list = em.createNativeQuery(sb.toString())
				.getResultList();
		if (null != list && list.size() > 0) {
			Object[] o = list.get(0);
			rtnStr = o[0].toString();
		}
		return rtnStr;
	}

	@Transactional(readOnly = true)
	public TdFlowNode findCurrentNode(String processInstanceId) {
		TdFlowNode node = null;
		// 已结束,找流程的第一个节点
		HistoricProcessInstance pi = this.activitiService
				.findInstanceById(processInstanceId);
		StringBuilder sb = new StringBuilder(
				" select t from TdFlowNode t where t.tdFlowDef.actDefId ='");
		sb.append(pi.getProcessDefinitionId()).append("' and t.nums =1");
		List<TdFlowNode> list = em.createQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			node = list.get(0);
		}
		return node;
	}

	public String cbFlow(String processInstanceId, String taskNodeName,
			String taskName, Integer userId) {
		this.activitiService.updateTaskPriority(processInstanceId, 99);

		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT * FROM (SELECT T1.ASSIGNEE_ AS USERID,T1.ID_ ");
		sb.append(" FROM ACT_RU_TASK T1 ");
		sb.append(" WHERE T1.PROC_INST_ID_ = '").append(processInstanceId)
				.append("' ");
		if (StringUtils.isNotBlank(taskNodeName)) {
			sb.append(" AND T1.NAME_ LIKE '%").append(taskNodeName)
					.append("%' ");
		}
		sb.append(" UNION ");
		sb.append(" SELECT W2.USER_ID_ AS USERID,W1.ID_");
		sb.append(" FROM ACT_RU_TASK W1 ");
		sb.append(" INNER JOIN ACT_RU_IDENTITYLINK W2 ON W2.TASK_ID_ = W1.ID_ ");
		sb.append(" WHERE W1.PROC_INST_ID_ = '").append(processInstanceId)
				.append("' ");
		if (StringUtils.isNotBlank(taskNodeName)) {
			sb.append(" AND W1.NAME_ LIKE '%").append(taskNodeName)
					.append("%' ");
		}
		sb.append(")A WHERE A.USERID IS NOT NULL");
		List<Object[]> resultList = em.createNativeQuery(sb.toString())
				.getResultList();
		if (null != resultList && resultList.size() > 0) {
			sb = new StringBuilder();
			for (Object[] objArr : resultList) {
				if (null != objArr) {
					sb.append(",").append(objArr[0]);
					TdMsgMain msgMain = new TdMsgMain();
					msgMain.setInfoTitle("请尽快办理您的待办任务【" + taskName + "】!");
					msgMain.setMessageType(MessageType.ACTIVITI);
					msgMain.setNetAdr("/webapp/flow/tdFlowTaskEdit.faces?taskId="
							+ objArr[1]);
					msgMain.setNetName("待办任务");
					msgMain.setTsUserInfo(new TsUserInfo(userId));

					msgMain = (TdMsgMain) this.saveObj(msgMain);

					TdMsgSub sub = new TdMsgSub();
					sub.setTdMsgMain(msgMain);
					sub.setTsUserInfo(new TsUserInfo(Integer.valueOf(objArr[0]
							.toString())));
					this.save(sub);

					// 极光推送
					this.sendPushMsg(objArr[0].toString(), "请尽快办理您的待办任务【"
							+ taskName + "】!",
							Integer.valueOf(objArr[1].toString()),
							processInstanceId, msgMain.getRid().toString());
				}
			}
			return sb.substring(1);
		}
		return null;
	}

	@Transactional(readOnly = true)
	public String findVariable(String instanceId, String key) {
		return this.activitiService.findVariable(instanceId, key);
	}

	@Transactional(readOnly = true)
	public TdFlowDef findDef(Integer rid) {
		return (TdFlowDef) this.find(TdFlowDef.class, rid);
	}

	@Transactional(readOnly = true)
	public List<TdFlowNode> findNbNodes(String defId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select new TdFlowNode(t.rid, t.actNodeId, t.nodeName, t.dealType)  from TdFlowNode t where t.tdFlowDef.rid = '");
		sb.append(defId).append("' and t.ifNbNode = '1' order by t.nums ");
		return em.createQuery(sb.toString()).getResultList();
	}

	/**
	 * 保存流程类型，检查类型名称的唯一性,成功保存返回null,否则返回错误信息
	 * 
	 * @param type
	 *            流程类型
	 * @return string
	 * <AUTHOR>
	 */
	public String saveOrUpdateFlowType(TdFlowType type) {
		StringBuilder hql = new StringBuilder("");
		hql.append("select new TdFlowType(t.rid) from TdFlowType t");
		hql.append(" where t.typeName = '").append(type.getTypeName())
				.append("' ");
		if (null != type.getRid()) {
			hql.append(" and t.rid != '").append(type.getRid()).append("' ");
		}
		List<TdFlowType> list = em.createQuery(hql.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return "该类型名称已存在！请重新输入！";
		}

		if (null != type.getRid()) {
			this.update(type);
		} else {
			this.save(type);
		}
		return null;
	}

	/**
	 * 删除流程类型
	 * 
	 * @param rid
	 * @return string
	 * <AUTHOR>
	 */
	public String deleteFlowType(Integer rid) {
		try {
			if (null != rid) {
				StringBuffer sql = new StringBuffer("");
				sql.append("delete TD_FLOW_TYPE where rid = ").append(rid);
				em.createNativeQuery(sql.toString()).executeUpdate();
				return null;
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			return "该数据已被引用！不予删除！";
		}
		return null;
	}

	/**
	 * 过滤rid后所有的流程类型
	 * 
	 * @param rid
	 * @return
	 */
	@Transactional(readOnly = true)
	public TdFlowType findFlowType(Integer rid) {
		return (TdFlowType) this.find(TdFlowType.class, rid);
	}

	/***
	 * 初始化首页面或编辑页面树
	 * 
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TdFlowType> searchInit() {
		StringBuffer hql = new StringBuffer("");
		hql.append("select t from TdFlowType t ");
		hql.append(" order by decode(t.parent.rid,null,0,t.rid) ");
		return em.createQuery(hql.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public Object[] buildMsgTitle(String pid) {
		// 张三发起的请假流程【2014-08-28 17:04:00】
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T2.USERNAME,T3.DEF_NAME,TO_CHAR(T1.START_TIME_, 'YYYY-MM-DD HH24:MI:SS'), T1.START_USER_ID_ ");
		sb.append(" FROM ACT_HI_PROCINST T1 ");
		sb.append(" INNER JOIN TS_USER_INFO T2 ON T1.START_USER_ID_ = T2.RID ");
		sb.append(" INNER JOIN TD_FLOW_DEF T3 ON T1.PROC_DEF_ID_ = T3.ACT_DEF_ID ");
		sb.append(" WHERE T1.PROC_INST_ID_ = '").append(pid).append("' ");
		List<Object[]> list = em.createNativeQuery(sb.toString())
				.getResultList();
		if (null != list && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TdFlowRule> findFlowRules() {
		return em.createNamedQuery("TdFlowRule.findRules").getResultList();
	}

	public String saveOrUpdateFlowTemplate(TbFlowAdvtemplate template) {
		/**
		 * 验证模板名称的唯一性
		 */
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T.RID FROM TB_FLOW_ADVTEMPLATE T WHERE T.TEMPLATE_NAME = '");
		sb.append(template.getTemplateName()).append("' ");
		if (null != template.getRid()) {
			sb.append(" AND T.RID <> '").append(template.getRid()).append("' ");
		}
		sb.append(" AND T.FLOW_NODEID = '")
				.append(template.getTdFlowNode().getRid()).append("' ");
		List list = super.em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return "该模板名称已存在！";
		}

		/**
		 * 如果是新增的话，默认是默认的模板，需要将其他默认的模板改为不默认
		 */
		if (null == template.getRid()) {
			sb = new StringBuilder(
					" UPDATE TB_FLOW_ADVTEMPLATE SET IS_DEFAULT = '0' WHERE FLOW_NODEID = '");
			sb.append(template.getTdFlowNode().getRid()).append("' ");
			super.em.createNativeQuery(sb.toString()).executeUpdate();
		}

		if (null != template.getRid()) {
			// 修改
			this.update(template);
		} else {
			// 新增
			this.save(template);
		}
		return null;
	}

	public String saveOrUpdateTdFlowTitle(TdFlowTitle tdFlowTitle) {
		/**
		 * 验证模板名称的唯一性
		 */
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T.RID FROM TD_FLOW_TITLE T WHERE T.TEMPLATE_NAME = '");
		sb.append(tdFlowTitle.getTemplateName()).append("' ");
		if (null != tdFlowTitle.getRid()) {
			sb.append(" AND T.RID <> '").append(tdFlowTitle.getRid())
					.append("' ");
		}
		sb.append(" AND T.DEF_ID = '")
				.append(tdFlowTitle.getTdFlowDef().getRid()).append("' ");
		List list = super.em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return "该模板名称已存在！";
		}

		/**
		 * 如果是新增的话，默认是默认的模板，需要将其他默认的模板改为不默认
		 */
		if (null == tdFlowTitle.getRid()) {
			sb = new StringBuilder(
					" UPDATE TD_FLOW_TITLE SET IS_DEFAULT = '0' WHERE DEF_ID = '");
			sb.append(tdFlowTitle.getTdFlowDef().getRid()).append("' ");
			super.em.createNativeQuery(sb.toString()).executeUpdate();
		}

		if (null != tdFlowTitle.getRid()) {
			// 修改
			this.update(tdFlowTitle);
		} else {
			// 新增
			this.save(tdFlowTitle);
		}
		return null;
	}

	public void startDefaultFlowTemplate(Integer nodeId, String rid) {
		StringBuilder sb = new StringBuilder();
		sb.append(" UPDATE TB_FLOW_ADVTEMPLATE SET IS_DEFAULT = '0' WHERE FLOW_NODEID = '");
		sb.append(nodeId).append("' ");
		super.em.createNativeQuery(sb.toString()).executeUpdate();

		if (StringUtils.isNotBlank(rid)) {
			sb = new StringBuilder();
			sb.append(" UPDATE TB_FLOW_ADVTEMPLATE SET IS_DEFAULT = '1' WHERE RID = '");
			sb.append(rid).append("' ");
			super.em.createNativeQuery(sb.toString()).executeUpdate();
		}
	}

	public void editDefaultFlowTitleTemplate(Integer nodeId, String rid) {
		StringBuilder sb = new StringBuilder();
		sb.append(" UPDATE TD_FLOW_TITLE SET IS_DEFAULT = '0' WHERE DEF_ID = '");
		sb.append(nodeId).append("' ");
		super.em.createNativeQuery(sb.toString()).executeUpdate();

		if (StringUtils.isNotBlank(rid)) {
			sb = new StringBuilder();
			sb.append(" UPDATE TD_FLOW_TITLE SET IS_DEFAULT = '1' WHERE RID = '");
			sb.append(rid).append("' ");
			super.em.createNativeQuery(sb.toString()).executeUpdate();
		}
	}

	@Transactional(readOnly = true)
	public String settingDefaultFlowAdvice(String nodeId,
			MetaCondition condition) {
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT T.TEMPLATE_CONTENT FROM TB_FLOW_ADVTEMPLATE T WHERE T.FLOW_NODEID = '");
		sb.append(nodeId).append("' AND T.IS_DEFAULT = '1'");
		List list = super.em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return this.commService.resolveTemplatemeta(list.get(0).toString(),
					condition, false);
		}
		return null;
	}

	public String sendMsgAfterSubmit(String taskId, String instanceId,
			Integer submiterId) {
		HistoricProcessInstance hisInstance = this.activitiService
				.findInstanceById(instanceId);

		// 0-待办人ID 1-TASKID
		List<Object[]> manList = this.findMan(instanceId);
		if (null != manList && manList.size() > 0) {
			// 构建代办人IDS
			StringBuilder sb = new StringBuilder();
			for (Object[] o : manList) {
				if (Integer.parseInt(o[1].toString()) > Integer
						.parseInt(taskId)) {
					TdMsgMain msgMain = new TdMsgMain();
					msgMain.setMessageType(MessageType.ACTIVITI);
					msgMain.setInfoTitle(hisInstance.getBusinessKey());
					msgMain.setNetAdr("/webapp/flow/tdFlowTaskEdit.faces?taskId="
							+ o[1]);
					msgMain.setNetName("待办任务");
					msgMain.setTsUserInfo(new TsUserInfo(submiterId));
					msgMain.setPublishTime(new Date());
					Object[] obj = this.findTaskData(o[1].toString());
					if (obj != null) {
						BusinessIdBean bean = JSON.parseObject(obj[1].toString(), BusinessIdBean.class);
						msgMain.setAppendKeys(bean.getRid());
					}
					msgMain.setIsTodo((short)1);
					msgMain.setSubType(1);
					msgMain.setTodoState((short)0);
					// 获取待办任务的菜单ID menu_en=flow_dbrw
					Integer menuId = this.getMenuIdByMenuEn("flow_dbrw");
					if (menuId != null) {
						msgMain.setMenuId(menuId);
					}
					this.save(msgMain);

					// 构建代办人IDS
					sb.append(",").append(o[0]);

					TdMsgSub sub = new TdMsgSub();
					sub.setTdMsgMain(msgMain);
					sub.setTsUserInfo(new TsUserInfo(Integer.valueOf(o[0]
							.toString())));
					this.save(sub);

					// 极光推送
					this.sendPushMsg(o[0].toString(),
							hisInstance.getBusinessKey(),
							Integer.valueOf(o[1].toString()), instanceId,
							msgMain.getRid().toString());
				}

			}
			return sb.toString().replaceFirst(",", "");
		}
		return null;
	}

	public String sendMsgAfterSubmit(String backNodeId, String instanceId,
			Integer submiterId, boolean ifBackOpt) {
		HistoricProcessInstance hisInstance = this.activitiService
				.findInstanceById(instanceId);

		// 0-待办人ID 1-TASKID
		List<Object[]> manList = this.findMan(instanceId);
		if (null != manList && manList.size() > 0) {
			// 构建代办人IDS
			StringBuilder sb = new StringBuilder();
			for (Object[] o : manList) {
				if (o[1].equals(backNodeId)) {
					TdMsgMain msgMain = new TdMsgMain();
					msgMain.setMessageType(MessageType.ACTIVITI);
					msgMain.setInfoTitle(hisInstance.getBusinessKey());
					msgMain.setNetAdr("/webapp/flow/tdFlowTaskEdit.faces?taskId="
							+ o[1]);
					msgMain.setNetName("待办任务");
					msgMain.setTsUserInfo(new TsUserInfo(submiterId));
					msgMain.setPublishTime(new Date());
					Object[] obj = this.findTaskData(o[1].toString());
					if (obj != null) {
						BusinessIdBean bean = JSON.parseObject(obj[1].toString(), BusinessIdBean.class);
						msgMain.setAppendKeys(bean.getRid());
					}
					msgMain.setIsTodo((short)1);
					msgMain.setSubType(1);
					msgMain.setTodoState((short)0);
					// 获取待办任务的菜单ID menu_en=flow_dbrw
					Integer menuId = this.getMenuIdByMenuEn("flow_dbrw");
					if (menuId != null) {
						msgMain.setMenuId(menuId);
					}
					this.save(msgMain);

					// 构建代办人IDS
					sb.append(",").append(o[0]);

					TdMsgSub sub = new TdMsgSub();
					sub.setTdMsgMain(msgMain);
					sub.setTsUserInfo(new TsUserInfo(Integer.valueOf(o[0]
							.toString())));
					this.save(sub);
					// 极光推送
					this.sendPushMsg(o[0].toString(),
							hisInstance.getBusinessKey(),
							Integer.valueOf(o[1].toString()), instanceId,
							msgMain.getRid().toString());
				}

			}
			return sb.toString().replaceFirst(",", "");
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<Object[]> findMan(String instanceId) {
		if (StringUtils.isNotBlank(instanceId)) {
			StringBuilder sb = new StringBuilder(" SELECT T3.USER_ID_, T2.ID_ ");
			sb.append(" FROM ACT_RU_EXECUTION T1  ");
			sb.append(" INNER JOIN ACT_RU_TASK T2 ON T1.PROC_INST_ID_ = T2.PROC_INST_ID_ AND T1.ACT_ID_ = T2.TASK_DEF_KEY_ ");
			sb.append(" INNER JOIN ACT_RU_IDENTITYLINK T3 ON T3.TASK_ID_ = T2.ID_ ");
			sb.append(" WHERE T1.PROC_INST_ID_ = '").append(instanceId)
					.append("' ");

			sb.append(" UNION  ");
			sb.append(" SELECT T2.ASSIGNEE_ , T2.ID_ ");
			sb.append(" FROM ACT_RU_EXECUTION T1  ");
			sb.append(" INNER JOIN ACT_RU_TASK T2 ON T1.PROC_INST_ID_ = T2.PROC_INST_ID_ AND T1.ACT_ID_ = T2.TASK_DEF_KEY_ ");
			sb.append(" WHERE T1.PROC_INST_ID_ = '").append(instanceId)
					.append("' AND T2.ASSIGNEE_ IS NOT NULL ");

			return em.createNativeQuery(sb.toString()).getResultList();
		}
		return null;
	}
	
	
	/**
	 * 查询当前流程最新的参数值
	 * @param flowInstId
	 * @return
	 */
	public String findCurrFlowParam(String flowInstId){
		if(StringUtils.isNotBlank(flowInstId))	{
			StringBuilder sql = new StringBuilder();
			sql.append("SELECT  B.PAGE_PARAM FROM (");
			sql.append(" SELECT A.PAGE_PARAM, ROWNUM RN FROM (");
			sql.append(" SELECT T4.PAGE_PARAM FROM ACT_HI_TASKINST T ");
			sql.append(" INNER JOIN TD_FLOW_DEF T2 ON T2.ACT_DEF_ID = T.PROC_DEF_ID_ ");
			sql.append(" INNER JOIN TD_FLOW_NODE T3 ON T3.DEF_ID = T2.RID AND T3.ACT_NODE_ID = T.TASK_DEF_KEY_ ");
			sql.append(" INNER JOIN TD_FLOW_NODE_PAGE T4 ON T3.PAGE_ID = T4.RID");
			sql.append(" WHERE T.PROC_INST_ID_ = '").append(flowInstId).append("' ORDER BY T.START_TIME_ DESC )  A ) B WHERE B.RN = 1");
		    List<Object> resultList = em.createNativeQuery(sql.toString()).getResultList();
		    if( null != resultList && resultList.size() > 0 )	{
		    	return resultList.get(0) == null ?  null : resultList.get(0).toString();
		    }
		}
		return null;
	}
	
	

	@Transactional(readOnly = true)
	public Object[] findTaskData(String taskId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T3.RID, T4.TEXT_, 1");
		sb.append(" FROM ACT_RU_TASK T1 ");
		sb.append(" INNER JOIN TD_FLOW_DEF T2 ON T2.Act_Def_Id = t1.proc_def_id_ ");
		sb.append(" INNER JOIN TD_FLOW_NODE T3 ON T3.DEF_ID = T2.RID AND T3.ACT_NODE_ID = T1.TASK_DEF_KEY_ ");
		sb.append(" INNER JOIN ACT_RU_VARIABLE T4 ON T1.PROC_INST_ID_ = T4.PROC_INST_ID_ AND T4.NAME_ = 'businessId' ");
		sb.append(" WHERE T1.ID_ = '").append(taskId).append("' ");
		sb.append(" UNION ");
		sb.append(" SELECT T3.RID, T4.TEXT_, 2");
		sb.append(" FROM ACT_HI_TASKINST T1 ");
		sb.append(" INNER JOIN TD_FLOW_DEF T2 ON T2.Act_Def_Id = t1.proc_def_id_ ");
		sb.append(" INNER JOIN TD_FLOW_NODE T3 ON T3.DEF_ID = T2.RID AND T3.ACT_NODE_ID = T1.TASK_DEF_KEY_ ");
		sb.append(" INNER JOIN ACT_HI_VARINST T4 ON T1.PROC_INST_ID_ = T4.PROC_INST_ID_ AND T4.NAME_ = 'businessId' ");
		sb.append(" WHERE T1.ID_ = '").append(taskId).append("' ");

		List<Object[]> list = em.createNativeQuery(sb.toString())
				.getResultList();
		if (null != list && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	public void deleteProcessInstanceTaskId(String taskId) {
		if (StringUtils.isNotBlank(taskId)) {
			StringBuilder sb = new StringBuilder();
			sb.append(
					"SELECT W.PROC_INST_ID_ FROM ACT_HI_TASKINST W WHERE W.ID_ = '")
					.append(taskId).append("'");
			List list = super.em.createNativeQuery(sb.toString())
					.getResultList();
			if (null != list && list.size() > 0) {
				String processId = list.get(0).toString();

				sb = new StringBuilder();
				sb.append(" BEGIN ");
				sb.append(
						" DELETE FROM ACT_RU_IDENTITYLINK WHERE PROC_INST_ID_ = '")
						.append(processId).append("'; ");
				sb.append(
						" DELETE FROM ACT_RU_IDENTITYLINK WHERE TASK_ID_ IN (SELECT T1.ID_ FROM ACT_RU_TASK T1 WHERE T1.PROC_INST_ID_ = '")
						.append(processId).append("'); ");
				sb.append(
						" DELETE FROM ACT_RU_VARIABLE WHERE PROC_INST_ID_ = '")
						.append(processId).append("'; ");
				sb.append(" DELETE FROM ACT_RU_TASK WHERE PROC_INST_ID_ = '")
						.append(processId).append("'; ");
				sb.append(
						" DELETE FROM ACT_RU_EXECUTION WHERE PROC_INST_ID_ = '")
						.append(processId).append("'; ");
				sb.append(" DELETE FROM ACT_HI_ACTINST WHERE PROC_INST_ID_ = '")
						.append(processId).append("'; ");
				sb.append(
						" DELETE FROM ACT_HI_IDENTITYLINK WHERE PROC_INST_ID_ = '")
						.append(processId).append("'; ");
				sb.append(
						" DELETE FROM ACT_HI_IDENTITYLINK WHERE TASK_ID_ IN (SELECT T1.ID_ FROM ACT_HI_TASKINST T1 WHERE T1.PROC_INST_ID_ = '")
						.append(processId).append("'); ");
				sb.append(
						" DELETE FROM ACT_HI_PROCINST WHERE PROC_INST_ID_ = '")
						.append(processId).append("'; ");
				sb.append(
						" DELETE FROM ACT_HI_TASKINST WHERE PROC_INST_ID_ = '")
						.append(processId).append("'; ");
				sb.append(" DELETE FROM ACT_HI_VARINST WHERE PROC_INST_ID_ = '")
						.append(processId).append("'; ");
				sb.append(" DELETE FROM ACT_HI_TASKRCD WHERE PROC_INST_ID = '")
						.append(processId).append("'; ");
				sb.append(" DELETE FROM ACT_HI_COMMENT WHERE PROC_INST_ID_ = '")
						.append(processId).append("'; ");
				sb.append(" END; ");

				super.em.createNativeQuery(sb.toString()).executeUpdate();
			}
		}
	}

	public void saveTxRpt(String taskId, Map<Integer, String> sendRstMap,
			String content, TsTxtype tx) {
		/**
		 * 先根据任务ID（可能是处理过的），查询 该流程的当前的所有待办任务以及相应的处理人
		 */
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT W.USER_ID_, MAX(W.TASKID) FROM ( ");

		sb.append(" SELECT T3.USER_ID_, T2.ID_ AS TASKID ");
		sb.append(" FROM ACT_HI_TASKINST T1  ");
		sb.append(" INNER JOIN ACT_HI_TASKINST T2 ON T1.PROC_INST_ID_ = T2.PROC_INST_ID_ ");
		sb.append(" INNER JOIN ACT_HI_IDENTITYLINK T3 ON T3.TASK_ID_ = T2.ID_ ");
		sb.append(" WHERE T1.ID_ = '")
				.append(taskId)
				.append("' AND T2.ASSIGNEE_ IS NULL AND T3.TYPE_ = 'candidate' ");
		sb.append(" UNION ");
		sb.append(" SELECT T2.ASSIGNEE_  AS USER_ID_, T2.ID_ AS TASKID ");
		sb.append(" FROM ACT_HI_TASKINST T1 ");
		sb.append(" INNER JOIN ACT_HI_TASKINST T2 ON T1.PROC_INST_ID_ = T2.PROC_INST_ID_ ");
		sb.append(" WHERE T1.ID_ = '").append(taskId)
				.append("' AND T2.ASSIGNEE_ IS NOT NULL ");

		sb.append(" ) W GROUP BY  W.USER_ID_ ");
		// 0-用户ID 1-任务ID
		List<Object[]> list = super.em.createNativeQuery(sb.toString())
				.getResultList();

		if (null != list && list.size() > 0) {
			for (Object[] o : list) {
				Integer userId = Integer.valueOf(o[0].toString());
				if (sendRstMap.containsKey(userId)) {
					TsTxrpt rpt = new TsTxrpt();
					rpt.setMsgCont(content);
					rpt.setTarUserId(userId);
					rpt.setMsgId(sendRstMap.get(userId));
					if (StringUtils.isNotBlank(rpt.getMsgId())
							&& StringUtils.isNumeric(rpt.getMsgId())) {
						rpt.setSendStatus(Short.valueOf("1"));
					} else {
						rpt.setSendStatus(Short.valueOf("0"));
					}
					rpt.setTsTxtype(tx);
					this.save(rpt);

					TdEmTxrptCase rptCase = new TdEmTxrptCase();
					rptCase.setActivitiTaskId(Integer.valueOf(taskId));
					rptCase.setTargetTaskId(Integer.valueOf(o[1].toString()));
					rptCase.setTsTxrpt(rpt);
					this.save(rptCase);

				}
			}
		}
	}

	@Transactional(readOnly = true)
	public List<TbFlowAdvtemplate> findAdvTemplateList(Integer nodeId) {
		return super.em.createNamedQuery("TbFlowAdvtemplate.findByNodeId")
				.setParameter("nodeId", nodeId).getResultList();
	}

	@Transactional(readOnly = true)
	public List<TdFlowTitle> findTitleTemplateList(Integer rid) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TdFlowTitle t where t.tdFlowDef.rid=").append(
				rid);
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public List<TbTempmetaDefine> findAllTemplateDefine() {
		return super.em.createNamedQuery("TbTempmetaDefine.findAll")
				.getResultList();
	}

	@Transactional(readOnly = true)
	public List<TsTxtype> findAllUseableTxtype() {
		return super.em.createNamedQuery("TsTxtype.findUsable").getResultList();
	}

	@Transactional(readOnly = true)
	public List<TbFlowAdvtemplate> findAdvTemplateList(Integer nodeId,
			MetaCondition condition) {
		List<TbFlowAdvtemplate> list = super.em
				.createNamedQuery("TbFlowAdvtemplate.findByNodeId")
				.setParameter("nodeId", nodeId).getResultList();
		if (null != list) {
			for (TbFlowAdvtemplate adv : list) {
				if (StringUtils.isNotBlank(adv.getTemplateContent())) {
					adv.setAfterAnalyContent(this.commService
							.resolveTemplatemeta(adv.getTemplateContent(),
									condition, false));
				}
			}
		} else {
			list = new ArrayList<TbFlowAdvtemplate>(0);
		}
		return list;
	}

	@Transactional(readOnly = true)
	public TdFlowNode findTdFlowNodeWithSubList(Integer rid) {
		TdFlowNode node = (TdFlowNode) this.find(TdFlowNode.class, rid);
		node.getTdFlowNodeBtnList().size();
		return node;
	}

	@Transactional(readOnly = true)
	public List<TdFlowNodeBtn> findTdFlowNodeButton(Integer flowNodeRid) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TdFlowNodeBtn t where t.tdFlowNode.rid = ")
				.append(flowNodeRid);
		return em.createQuery(sb.toString()).getResultList();
	}

	public void saveFlowNodeButton(List<TdFlowNodeBtn> list, Integer flowNodeRid) {
		if (flowNodeRid != null) {
			// 删除原有配置
			StringBuilder sb = new StringBuilder();
			sb.append("delete TD_FLOW_NODE_BTN where FLOW_NODE_ID = ").append(
					flowNodeRid);
			em.createNativeQuery(sb.toString()).executeUpdate();
			// 保存新的配置
			if (list != null && list.size() > 0) {
				for (TdFlowNodeBtn t : list) {
					t.setRid(null);
					save(t);
				}
			}
		}
	}

	public void resetButtonCfg(List<TdFlowNode> list) {
		StringBuilder sb = null;
		for (TdFlowNode flowNode : list) {
			sb = new StringBuilder();
			sb.append("select rid from TD_FLOW_NODE_BTN where FLOW_NODE_ID = ")
					.append(flowNode.getRid());
			List<Object> tempList = em.createNativeQuery(sb.toString())
					.getResultList();
			if (tempList == null || tempList.size() == 0) {
				resetNodebuttonCfg(flowNode);
			}
		}
	}

	private void resetNodebuttonCfg(TdFlowNode flowNode) {
		FlowBtnType[] flowBtnTypes = FlowBtnType.values();
		if (flowBtnTypes.length > 0) {
			TdFlowNodeBtn tdFlowNodeBtn = null;
			for (FlowBtnType t : flowBtnTypes) {
				// 只有第一个节点可以配置删除按钮
				if (t.getTypeNo().equals("3") && !flowNode.getNums().equals(1)) {
					continue;
				}
				tdFlowNodeBtn = new TdFlowNodeBtn();
				tdFlowNodeBtn.setFlowBtnType(t);
				tdFlowNodeBtn.setTdFlowNode(flowNode);
				tdFlowNodeBtn.setFlowBtnName(t.getTypeCN());
				save(tdFlowNodeBtn);
			}
		}
	}

	public void resetTxTypeCfg(List<TdFlowNode> list) {
		StringBuilder sb = null;
		for (TdFlowNode flowNode : list) {
			sb = new StringBuilder();
			sb.append(
					"select rid from TD_FLOW_NODE_TXTYPE where FLOW_NODE_ID = ")
					.append(flowNode.getRid());
			List<Object> tempList = em.createNativeQuery(sb.toString())
					.getResultList();
			if (tempList == null || tempList.size() == 0) {
				resetNodeTxTypeCfg(flowNode);
			}
		}
	}

	private void resetNodeTxTypeCfg(TdFlowNode flowNode) {
		List<TsTxtype> list = this.findAllUseableTxtype();
		if (list != null && list.size() > 0) {
			TdFlowNodeTxType flowNodeTxType;
			for (TsTxtype t : list) {
				flowNodeTxType = new TdFlowNodeTxType();
				flowNodeTxType.setTdFlowNode(flowNode);
				flowNodeTxType.setTsTxtype(t);
				save(flowNodeTxType);
			}
		}
	}

	@Transactional(readOnly = true)
	public List<Object> findAlreadSelectTx(Integer rid) {
		StringBuilder sb = new StringBuilder();
		sb.append("select TX_ID from TD_FLOW_NODE_TXTYPE where FLOW_NODE_ID = ")
				.append(rid);
		return em.createNativeQuery(sb.toString()).getResultList();
	}

	public void saveFlowNodeTxType(List<TdFlowNodeTxType> list,
			Integer flowNodeRid) {
		// 删除原有配置
		StringBuilder sb = new StringBuilder();
		sb.append("delete TD_FLOW_NODE_TXTYPE where FLOW_NODE_ID = ").append(
				flowNodeRid);
		em.createNativeQuery(sb.toString()).executeUpdate();
		// 保存新的配置
		if (list != null && list.size() > 0) {
			for (TdFlowNodeTxType t : list) {
				save(t);
			}
		}
	}

	@Transactional(readOnly = true)
	public List<TdFlowNodePage> findAllFlowNodePage() {
		return em.createNamedQuery("TdFlowNodePage.findAll").getResultList();
	}

	@Transactional(readOnly = true)
	public TdFlowDef findFlowDefByActDefId(String actDefId) {
		if (StringUtils.isNotBlank(actDefId)) {
			StringBuilder sb = new StringBuilder();
			sb.append(
					" SELECT T1.ID_, T1.KEY_ FROM ACT_RE_PROCDEF T1 WHERE T1.ID_ = '")
					.append(actDefId).append("' ORDER BY T1.VERSION_ DESC ");
			List<Object[]> list = super.em.createNativeQuery(sb.toString())
					.getResultList();
			if (null != list && list.size() > 0) {
				sb = new StringBuilder();
				sb.append(
						" select t from TdFlowDef t left join fetch t.tdFlowNodeList t2 where t.actDefId = '")
						.append(list.get(0)[0].toString()).append("' ");
				sb.append(" order by t2.nums");
				List<TdFlowDef> rtnList = super.em.createQuery(sb.toString())
						.getResultList();
				if (null != rtnList && rtnList.size() > 0) {
					TdFlowDef def = rtnList.get(0);
					// def.getTdFlowNodeList().size();
					List<TdFlowNode> nodeList = def.getTdFlowNodeList();
					if (null != nodeList && nodeList.size() > 0) {
						for (TdFlowNode node : nodeList) {
							node.getTdFlowNodeBtnList().size();
						}
					}
					return def;
				}
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public TdFlowDef findFlowDefByKey(String processKey) {
		if (StringUtils.isNotBlank(processKey)) {
			StringBuilder sb = new StringBuilder();
			sb.append(
					" SELECT T1.ID_, T1.KEY_ FROM ACT_RE_PROCDEF T1 WHERE T1.KEY_ = '")
					.append(processKey).append("' ORDER BY T1.VERSION_ DESC ");
			List<Object[]> list = super.em.createNativeQuery(sb.toString())
					.getResultList();
			if (null != list && list.size() > 0) {
				sb = new StringBuilder();
				sb.append(
						" select t from TdFlowDef t left join fetch t.tdFlowNodeList t2 where t.actDefId = '")
						.append(list.get(0)[0].toString()).append("' ");
				sb.append(" order by t2.nums");
				List<TdFlowDef> rtnList = super.em.createQuery(sb.toString())
						.getResultList();
				if (null != rtnList && rtnList.size() > 0) {
					TdFlowDef def = rtnList.get(0);
					// def.getTdFlowNodeList().size();
					List<TdFlowNode> nodeList = def.getTdFlowNodeList();
					if (null != nodeList && nodeList.size() > 0) {
						for (TdFlowNode node : nodeList) {
							node.getTdFlowNodeBtnList().size();
						}
					}
					return def;
				}
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TsTxtype> findTxtypeByNodeId(Integer flowNodeId) {
		List<TsTxtype> rtnList = new ArrayList<TsTxtype>();
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T1.RID, T1.TYPES, T1.IMPL_CLASS, T1.XH, T1.STATUS, T1.TYPE_CODE ");
		sb.append(" FROM TS_TXTYPE T1 ");
		sb.append(" INNER JOIN TD_FLOW_NODE_TXTYPE T2 ON T2.TX_ID = T1.RID ");
		sb.append(" WHERE T2.FLOW_NODE_ID = '").append(flowNodeId)
				.append("' AND T1.STATUS='1' ");
		sb.append(" ORDER BY T1.XH ");

		List<Object[]> list = super.em.createNativeQuery(sb.toString())
				.getResultList();
		if (null != list && list.size() > 0) {
			for (Object[] o : list) {
				TsTxtype t = new TsTxtype();
				t.setRid(Integer.valueOf(o[0].toString()));
				if (null != o[1]) {
					t.setTxType((TxType) EnumUtils.findEnum(TxType.class, o[1]));
				}
				if (null != o[2]) {
					t.setImplClass(o[2].toString());
				}
				if (null != o[3]) {
					t.setXh(Integer.valueOf(o[3].toString()));
				}
				if (null != o[4]) {
					t.setStatus(Short.valueOf(o[4].toString()));
				}
				if (null != o[5]) {
					t.setTypeCode(o[5].toString());
				}

				rtnList.add(t);
			}
		}

		return rtnList;
	}

	@Transactional(readOnly = true)
	public List<TsTxtype> findTxtypeByTaskId(String taskId) {
		if (StringUtils.isNotBlank(taskId)) {
			TdFlowNode flowNode = this.findFlowNodeByTaskId(taskId);
			if (null != flowNode && null != flowNode.getRid()) {
				return this.findTxtypeByNodeId(flowNode.getRid());
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public TdFlowNode findFlowNodeByTaskId(String taskId) {
		if (StringUtils.isNotBlank(taskId)) {
			HistoricTaskInstance hisTask = this.activitiService
					.findHisTaskById(taskId);
			ProcessDefinitionEntity definitionEntity = this.activitiService
					.findProcessDefinitionEntityByTaskId(taskId);
			StringBuilder sb = new StringBuilder();
			sb.append(
					" select t from TdFlowNode t where t.tdFlowDef.actDefId='")
					.append(definitionEntity.getId());
			sb.append("' and t.actNodeId = '")
					.append(hisTask.getTaskDefinitionKey()).append("' ");

			List<TdFlowNode> list = super.em.createQuery(sb.toString())
					.getResultList();
			if (null != list && list.size() > 0) {
				TdFlowNode node = list.get(0);
				node.getTdFlowNodeBtnList().size();
				return node;
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TsUserInfo> findAssigeeOfActivitiNodeId(String taskId,
			String activitiNodeId) {
		if (StringUtils.isNotBlank(taskId)
				&& StringUtils.isNotBlank(activitiNodeId)) {
			ProcessInstance processInstance = this.activitiService
					.findProcessInstanceByTaskId(taskId);
			StringBuilder sb = new StringBuilder();
			sb.append(" SELECT T1.NAME_ , T1.TEXT_ FROM ACT_HI_VARINST T1  ");
			sb.append(" WHERE T1.PROC_INST_ID_ = '").append(
					processInstance.getId());
			sb.append("' AND (T1.NAME_  = 'owner' OR T1.NAME_ LIKE '")
					.append(activitiNodeId).append("_%') ");

			String userIds = null;
			// 先根据节点ID找人，找不到就是首节点
			List<Object[]> list0 = super.em.createNativeQuery(sb.toString())
					.getResultList();
			if (null != list0 && list0.size() > 0) {
				String starter = null;
				for (Object[] o : list0) {
					if (o[0].toString().startsWith(activitiNodeId)) {
						userIds = o[1].toString();
						break;
					} else if ("owner".equals(o[0].toString())) {
						starter = o[1].toString();
					}
				}

				if (StringUtils.isBlank(userIds)) {
					userIds = starter;
				}
			}

			return this.findAssigeeByUserIds(userIds);
		}
		return new ArrayList<TsUserInfo>(0);
	}

	@Transactional(readOnly = true)
	public List<TsUserInfo> findAssigeeByUserIds(String userIds) {
		List<TsUserInfo> userList = new ArrayList<TsUserInfo>();
		if (StringUtils.isNotBlank(userIds)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" SELECT T1.RID AS USERID, T1.USERNAME, T3.RID AS OFFID, T3.OFFICENAME, T5.RID AS UNITID, T5.UNIT_SIMPNAME ");
			sb.append(" FROM TS_USER_INFO T1 ");
			sb.append(" INNER JOIN TS_UNIT T5 ON T1.UNIT_RID = T5.RID ");
			sb.append(" INNER JOIN TB_SYS_EMP T2 ON T2.RID = T1.EMP_ID ");
			sb.append(" INNER JOIN TS_OFFICE T3 ON T2.DEPT_ID = T3.RID ");
			sb.append(" WHERE T1.RID IN (").append(userIds).append(") ");

			List<Object[]> list = em.createNativeQuery(sb.toString())
					.getResultList();
			if (null != list && list.size() > 0) {
				for (Object[] o : list) {
					TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0]
							.toString()));
					t.setUsername(o[1] == null ? "" : o[1].toString());

					TsOffice office = new TsOffice(Integer.valueOf(o[2]
							.toString()), o[3] == null ? "" : o[3].toString());
					TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
					unit.setUnitSimpname(o[5].toString());

					office.setTsUnit(unit);
					t.setTsOffice(office);
					t.setTsUnit(unit);

					userList.add(t);
				}
			}
		}
		return userList;
	}

	@Transactional(readOnly = true)
	public Map<String, List<TsUserInfo>> findAssigeeInProcessInstance(
			String taskId) {
		if (StringUtils.isNotBlank(taskId)) {
			ProcessInstance processInstance = this.activitiService
					.findProcessInstanceByTaskId(taskId);
			return findProcessInstanceById(processInstance.getId());
		}
		
		return null;
	}
	
	@Transactional(readOnly = true)
	public Map<String, List<TsUserInfo>> findProcessInstanceById(String instanceId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT * FROM ( ");
		sb.append(" SELECT A1.ID_, A1.NAME_, A1.ASSIGNEE_ ");
		sb.append(" FROM ACT_HI_TASKINST A1 ");
		sb.append(" WHERE A1.PROC_INST_ID_ = '")
				.append(instanceId)
				.append("' AND A1.ASSIGNEE_ IS NOT NULL ");
		sb.append(" UNION ALL ");
		sb.append(" SELECT A1.ID_, A1.NAME_, A3.USER_ID_ ");
		sb.append(" FROM ACT_HI_TASKINST A1 ");
		sb.append(" INNER JOIN ACT_HI_IDENTITYLINK A3 ON A3.TASK_ID_ = A1.ID_ ");
		sb.append(" WHERE A1.PROC_INST_ID_ = '")
				.append(instanceId).append("' ");
		sb.append(" ) ORDER BY ID_ ");

		List<Object[]> list = super.em.createNativeQuery(sb.toString())
				.getResultList();
		if (null != list && list.size() > 0) {
			Map<String, String> temMap = new LinkedHashMap<String, String>();
			Map<String, List<TsUserInfo>> rtnMap = new LinkedHashMap<String, List<TsUserInfo>>();

			for (Object[] o : list) {
				String tempUserIds = temMap.get(o[1].toString());
				if (StringUtils.isNotBlank(tempUserIds)) {
					temMap.put(o[1].toString(),
							tempUserIds + "," + o[2].toString());
				} else {
					temMap.put(o[1].toString(), o[2].toString());
				}
			}

			Set<String> keySet = temMap.keySet();
			for (String nodeName : keySet) {
				rtnMap.put(nodeName,
						this.findAssigeeByUserIds(temMap.get(nodeName)));
			}

			return rtnMap;
		}
		return null;
	}

	@Transactional(readOnly = true)
	public Map<String, TsUserInfo> findProcessInstancePerson(String taskId) {
		if (StringUtils.isNotBlank(taskId)) {
			ProcessInstance processInstance = this.activitiService
					.findProcessInstanceByTaskId(taskId);
			StringBuilder sb = new StringBuilder();
			sb.append("SELECT T1.ID_, T1.NAME_, MAX(T1.ASSIGNEE_), T1.TASK_DEF_KEY_ FROM ( ");
			sb.append(" SELECT A1.ID_, A1.NAME_, A1.ASSIGNEE_, A1.TASK_DEF_KEY_ FROM ACT_HI_TASKINST A1");
			sb.append(" WHERE A1.PROC_INST_ID_ = '")
					.append(processInstance.getId()).append("'");
			sb.append(" AND A1.ASSIGNEE_ IS NOT NULL UNION ALL ");
			sb.append(" SELECT A1.ID_, A1.NAME_, A3.USER_ID_,A1.TASK_DEF_KEY_ FROM ACT_HI_TASKINST A1 ");
			sb.append(" INNER JOIN ACT_HI_IDENTITYLINK A3 ON A3.TASK_ID_ = A1.ID_ ");
			sb.append(" INNER JOIN ACT_HI_TASKRCD A4 ON A4.TASK_ID = A1.ID_ AND A4.USER_ID = A3.USER_ID_");
			sb.append(" WHERE A1.PROC_INST_ID_ = '")
					.append(processInstance.getId()).append("'");
			sb.append(" ) T1 ");
			sb.append(" GROUP BY T1.ID_, T1.NAME_, T1.TASK_DEF_KEY_");
			List<Object[]> list = super.em.createNativeQuery(sb.toString())
					.getResultList();
			if (null != list && list.size() > 0) {
				Map<String, TsUserInfo> rtnMap = new LinkedHashMap<String, TsUserInfo>();
				for (Object[] o : list) {
					if (StringUtils.isNotBlank(o[2].toString())) {
						TsUserInfo tsUserInfo = em.find(TsUserInfo.class,
								Integer.valueOf(o[2].toString()));
						rtnMap.put(o[1].toString(), tsUserInfo);
					}
				}
				return rtnMap;
			}
		}
		return null;
	}

	public void saveMsgInProcess(String taskId, String userIds,
			Integer currentUserId, String buinessId) {
		if (StringUtils.isNotBlank(taskId) && StringUtils.isNotBlank(userIds)) {
			HistoricProcessInstance processInstance = this.activitiService
					.findHisInstanceByTaskId(taskId);

			StringBuilder sb = new StringBuilder();
			sb.append(" SELECT T1.ASSIGNEE_, MAX(T1.ID_) FROM ( ");
			sb.append(" SELECT A1.ID_, A1.NAME_, A1.ASSIGNEE_ ");
			sb.append(" FROM ACT_HI_TASKINST A1  ");
			sb.append(" WHERE A1.PROC_INST_ID_ = '").append(
					processInstance.getId());
			sb.append("' AND A1.ASSIGNEE_ IN (").append(userIds).append(") ");
			sb.append(" UNION ALL ");
			sb.append(" SELECT A1.ID_ AS ASSIGNEE_, A1.NAME_, A3.USER_ID_ ");
			sb.append(" FROM ACT_HI_TASKINST A1 ");
			sb.append(" INNER JOIN ACT_HI_IDENTITYLINK A3 ON A3.TASK_ID_ = A1.ID_ ");
			sb.append(" WHERE A1.PROC_INST_ID_ = '").append(
					processInstance.getId());
			sb.append("' AND A3.USER_ID_ IN (").append(userIds).append(") ");
			sb.append(" ) T1 GROUP BY T1.ASSIGNEE_ ");

			/**
			 * 查询整个流程的过程的参与人参与的最大节点的地址
			 * 
			 */
			List<Object[]> list = super.em.createNativeQuery(sb.toString())
					.getResultList();
			if (null != list && list.size() > 0) {
				/**
				 * 保存发送的消息
				 */
				for (Object[] o : list) {
					// 0-任务处理人 1-任务ID
					TdMsgMain msgMain = new TdMsgMain();
					msgMain.setMessageType(MessageType.DISPATCH);
					msgMain.setInfoTitle(processInstance.getBusinessKey()
							+ "，请注意查阅！");
					msgMain.setNetAdr("/webapp/flow/tdFlowTaskEdit.faces?ff=1&taskId="
							+ o[1]);
					msgMain.setNetName("待办任务");
					msgMain.setTsUserInfo(new TsUserInfo(currentUserId));
					msgMain.setPublishTime(new Date());
					if (StringUtils.isNoneBlank(buinessId)) {
						BusinessIdBean bean = JSON.parseObject(buinessId, BusinessIdBean.class);
						msgMain.setAppendKeys(bean.getRid());
					}
					msgMain.setIsTodo((short)0);
					msgMain.setSubType(1);
					msgMain.setTodoState((short)0);
					// 获取待办任务的菜单ID menu_en=flow_dbrw
					Integer menuId = this.getMenuIdByMenuEn("flow_dbrw");
					if (menuId != null) {
						msgMain.setMenuId(menuId);
					}
					
					this.save(msgMain);

					TdMsgSub sub = new TdMsgSub();
					sub.setTdMsgMain(msgMain);
					sub.setTsUserInfo(new TsUserInfo(Integer.valueOf(o[0]
							.toString())));
					this.save(sub);
					// 极光推送
					this.sendPushMsg(o[0].toString(),
							processInstance.getBusinessKey() + "，请注意查阅！",
							Integer.valueOf(o[1].toString()), processInstance
									.getId(), msgMain.getRid().toString());
				}
			}
		}
	}
	
	/**
	 * 查询流程发起者
	 * @param taskId
	 * @return
	 */
	public Integer findFlowStartUser(String taskId)	{
		if(StringUtils.isNotBlank(taskId))	{
			StringBuilder sql = new StringBuilder();
			sql.append("SELECT INST.START_USER_ID_ FROM ACT_HI_TASKINST TASK INNER JOIN  ACT_HI_PROCINST INST ");
			sql.append(" ON TASK.PROC_INST_ID_ = INST.ID_ WHERE TASK.ID_ = '").append(taskId).append("'");
			List<Object> resultList = this.em.createNativeQuery(sql.toString()).getResultList();
			if( null != resultList && resultList.size() > 0)	{
				return resultList.get(0) == null ? null : new Integer(resultList.get(0).toString());
			}
		}
		return null;
	}

	public void ffMsgInProcess(String taskId, String userIds,
			Integer currentUserId, String buinessId) {
		if (StringUtils.isNotBlank(taskId) && StringUtils.isNotBlank(userIds)) {
			HistoricProcessInstance processInstance = this.activitiService
					.findHisInstanceByTaskId(taskId);

			if (StringUtils.isNotBlank(userIds)) {
				String[] users = userIds.split(",");
				/**
				 * 保存发送的消息
				 */
				TdMsgMain msgMain = new TdMsgMain();
				msgMain.setMessageType(MessageType.DISPATCH);
				msgMain.setInfoTitle(processInstance.getBusinessKey()
						+ "，请注意查阅！");
				msgMain.setNetAdr("/webapp/flow/tdFlowTaskEdit.faces?ff=1&taskId="
						+ taskId);
				msgMain.setNetName("待办任务");
				msgMain.setTsUserInfo(new TsUserInfo(currentUserId));
				msgMain.setPublishTime(new Date());
				if (StringUtils.isNoneBlank(buinessId)) {
					BusinessIdBean bean = JSON.parseObject(buinessId, BusinessIdBean.class);
					msgMain.setAppendKeys(bean.getRid());
				}
				msgMain.setIsTodo((short)0);
				msgMain.setSubType(1);
				msgMain.setTodoState((short)0);
				// 获取待办任务的菜单ID menu_en=flow_dbrw
				Integer menuId = this.getMenuIdByMenuEn("flow_dbrw");
				if (menuId != null) {
					msgMain.setMenuId(menuId);
				}
				
				
				List<TdMsgSub> subList = new ArrayList<TdMsgSub>();
				for (String user : users) {
					// 0-任务处理人 1-任务ID
					TdMsgSub sub = new TdMsgSub();
					sub.setTdMsgMain(msgMain);
					sub.setTsUserInfo(new TsUserInfo(Integer.valueOf(user)));
					subList.add(sub);
				}
				msgMain.setTdMsgSubs(subList);
				msgMain = (TdMsgMain) this.saveObj(msgMain);
				if (msgMain != null) {
					String content = processInstance.getBusinessKey()
							+ "，请注意查阅！";
					this.sendPushMsg(userIds, content, Integer.valueOf(taskId),
							processInstance.getId(), msgMain.getRid()
									.toString());
				}
			}
		}
	}

	/** 极光推送 */
	public void sendPushMsg(String userIds, String content, Integer rid,
			String flowId, String msgRid) {
		List<TsUserInfo> userList = this.commService.findUserByUserIds(userIds);
		Map<String, String> paramMap = new HashMap<>();
		paramMap.put("msgRid", msgRid);
		paramMap.put("taskId", rid.toString());
		paramMap.put("type", "2");
		paramMap.put("flowId", flowId);
		if (userList != null && userList.size() > 0) {
			for (TsUserInfo user : userList) {
				new PushMsgToAppImpl(content, paramMap, user.getUserNo())
						.sendJPush();
			}
		}
	}

	@Transactional(readOnly = true)
	public Map<String, Object> initFlowContextByTask(String taskId) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (StringUtils.isNotBlank(taskId)) {
			HistoricTaskInstance hisTask = this.activitiService
					.findHisTaskById(taskId);
			HistoricProcessInstance historicInstance = this.activitiService
					.findInstanceById(hisTask.getProcessInstanceId());
			map.put("activitiInstanceId", historicInstance.getId());
			map.put("startUserId", historicInstance.getStartUserId());
			map.put("businessKey", historicInstance.getBusinessKey());

			// 流程定义对象
			TdFlowDef tdFlowDef = this.findFlowDefByActDefId(historicInstance
					.getProcessDefinitionId());
			map.put("tdFlowDef", tdFlowDef);

			// 当前流程节点对象
			TdFlowNode tdFlowNode = this.findFlowNodeByTaskId(taskId);
			map.put("tdFlowNode", tdFlowNode);

		}
		return map;
	}

	@Transactional(readOnly = true)
	public Map<String, Object> initFlowContextByProcess(String processId,
			Integer userId) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (StringUtils.isNotBlank(processId)) {
			HistoricProcessInstance historicInstance = this.activitiService
					.findInstanceById(processId);
			map.put("startUserId", historicInstance.getStartUserId());
			map.put("businessKey", historicInstance.getBusinessKey());

			// 流程定义对象
			TdFlowDef tdFlowDef = this.findFlowDefByActDefId(historicInstance
					.getProcessDefinitionId());
			map.put("tdFlowDef", tdFlowDef);

			/**
			 * 当前任务节点的寻找规则： 寻找，当前登录人参与的最后一个节点的表单数据; 找不到，当前流程的第一个节点的表单数据
			 */
			if (null!=userId) {
				StringBuilder sb = new StringBuilder();
				sb.append(" WITH M AS ( ");
				sb.append(" SELECT A1.ID_, A3.TEXT_ ");
				sb.append(" FROM ACT_HI_TASKINST A1 ");
				sb.append(" INNER JOIN ACT_HI_IDENTITYLINK A2 ON A2.TASK_ID_ = A1.ID_ ");
				sb.append(" INNER JOIN ACT_HI_VARINST A3 ON A3.PROC_INST_ID_ = A1.PROC_INST_ID_ AND A3.NAME_ = 'businessId' ");
				sb.append(" WHERE A1.PROC_INST_ID_ = '").append(processId)
						.append("' ");
				sb.append(" AND A2.USER_ID_ = '").append(userId).append("' ");
				sb.append(" UNION ");
				sb.append(" SELECT A1.ID_, A3.TEXT_ ");
				sb.append(" FROM ACT_HI_TASKINST A1 ");
				sb.append(" INNER JOIN ACT_HI_VARINST A3 ON A3.PROC_INST_ID_ = A1.PROC_INST_ID_ AND A3.NAME_ = 'businessId' ");
				sb.append(" WHERE A1.PROC_INST_ID_ = '").append(processId)
						.append("' AND A1.ASSIGNEE_ IS NOT NULL ");
				sb.append(" ) SELECT M.ID_, M.TEXT_ FROM M ORDER BY TO_NUMBER(M.ID_) DESC ");
				List<Object[]> list = super.em.createNativeQuery(sb.toString())
						.getResultList();
				if (null != list && list.size() > 0) {
					Object[] o = list.get(0);
					map.put("activitiTaskId", o[0].toString());
					map.put("businessIdJSON", o[1].toString());

					// 当前流程节点对象
					TdFlowNode tdFlowNode = this.findFlowNodeByTaskId(o[0]
							.toString());
					map.put("tdFlowNode", tdFlowNode);
					if (null != tdFlowNode) {
						map.put("flowNodeId", tdFlowNode.getRid().toString());
					}
				}
			}
			
		}
		return map;
	}

	@Transactional(readOnly = true)
	public List<Object[]> findTaskInfoInProcess(String activitiInstanceId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T1.NAME_, MAX(T1.ASSIGNEE_), T1.TASK_DEF_KEY_, TO_CHAR(T1.END_TIME_, 'YYYY-MM-DD HH24:MI:SS'), ");
		sb.append(" T1.USERNAME,T1.OFFICENAME,T1.UNITNAME,T1.UNIT_SIMPNAME,T1.MB_NUM,T1.PSN_SIGN,T1.DEAL_ADVICE ");
		sb.append(" FROM ( ");
		// 首节点
		sb.append(" SELECT A1.ID_, A1.NAME_, M2.RID AS ASSIGNEE_, A1.TASK_DEF_KEY_, A1.END_TIME_, ");
		sb.append(" M2.USERNAME,M4.OFFICENAME,M5.UNITNAME,M5.UNIT_SIMPNAME,M3.MB_NUM,M3.PSN_SIGN,M6.DEAL_ADVICE ");
		sb.append(" FROM ACT_HI_TASKINST A1 ");
		sb.append(" INNER JOIN TS_USER_INFO M2 ON M2.RID = A1.ASSIGNEE_ ");
		sb.append(" LEFT JOIN TB_SYS_EMP M3 ON M2.EMP_ID = M3.RID ");
		sb.append(" LEFT JOIN TS_OFFICE M4 ON M3.DEPT_ID = M4.RID ");
		sb.append(" LEFT JOIN TS_UNIT M5 ON M2.UNIT_RID = M5.RID ");
		sb.append(" LEFT JOIN ACT_HI_TASKRCD M6 ON M6.TASK_ID = A1.ID_ ");
		sb.append(" WHERE A1.PROC_INST_ID_ = '").append(activitiInstanceId)
				.append("' ");
		sb.append(" AND A1.ASSIGNEE_ IS NOT NULL ");

		sb.append(" UNION ALL ");

		// 除了首节点外，已经处理过的节点
		sb.append(" SELECT A1.ID_, A1.NAME_, M2.RID AS ASSIGNEE_, A1.TASK_DEF_KEY_, A1.END_TIME_, ");
		sb.append(" M2.USERNAME,M4.OFFICENAME,M5.UNITNAME,M5.UNIT_SIMPNAME,M3.MB_NUM,M3.PSN_SIGN,A4.DEAL_ADVICE ");
		sb.append(" FROM ACT_HI_TASKINST A1 ");
		sb.append(" INNER JOIN ACT_HI_IDENTITYLINK A3 ON A3.TASK_ID_ = A1.ID_ ");
		sb.append(" INNER JOIN ACT_HI_TASKRCD A4 ON A4.TASK_ID = A1.ID_ AND A4.USER_ID = A3.USER_ID_ ");
		sb.append(" INNER JOIN TS_USER_INFO M2 ON M2.RID = A3.USER_ID_ ");
		sb.append(" LEFT JOIN TB_SYS_EMP M3 ON M2.EMP_ID = M3.RID ");
		sb.append(" LEFT JOIN TS_OFFICE M4 ON M3.DEPT_ID = M4.RID ");
		sb.append(" LEFT JOIN TS_UNIT M5 ON M2.UNIT_RID = M5.RID ");
		sb.append(" WHERE A1.PROC_INST_ID_ = '").append(activitiInstanceId)
				.append("' ");

		sb.append(" ) T1 ");
		sb.append(" GROUP BY T1.ID_, T1.NAME_, T1.TASK_DEF_KEY_, T1.END_TIME_, ");
		sb.append(" T1.USERNAME,T1.OFFICENAME,T1.UNITNAME,T1.UNIT_SIMPNAME,T1.MB_NUM,T1.PSN_SIGN,T1.DEAL_ADVICE ");
		sb.append(" ORDER BY TO_NUMBER(T1.ID_)");
		return super.em.createNativeQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public boolean processIsEnded(String activitiInstanceId) {
		HistoricProcessInstance historicInstance = this.activitiService
				.findInstanceById(activitiInstanceId);
		return null == historicInstance.getEndTime() ? false : true;
	}

	@Transactional(readOnly = true)
	public List<List<BackNodeVO>> findBackableNodeList(String taskId) {
		List<List<BackNodeVO>> rtnList = new ArrayList<List<BackNodeVO>>();
		if (StringUtils.isNotBlank(taskId)) {
			TdFlowNode currentNode = this.findFlowNodeByTaskId(taskId);
			boolean fstNodeDisp = true;
			if (null != currentNode && null != currentNode.getFstNodeDisp()
					&& currentNode.getFstNodeDisp().intValue() == 0) {
				fstNodeDisp = false;
			}

			Set<List<ActivityImpl>> set = this.activitiService
					.findBackableNodeList(taskId, fstNodeDisp);
			for (List<ActivityImpl> list : set) {
				if (null != list && list.size() > 0) {
					List<BackNodeVO> nodeList = new ArrayList<BackNodeVO>();
					for (ActivityImpl ai : list) {
						List<TsUserInfo> userList = this
								.findAssigeeOfActivitiNodeId(taskId, ai.getId());
						if (null != userList && userList.size() > 0) {
							BackNodeVO vo = new BackNodeVO(
									(String) ai.getProperty("name"), ai.getId());
							vo.setAssigeeList(userList);
							nodeList.add(vo);
						}
					}
					if (null != nodeList && nodeList.size() > 0) {
						rtnList.add(nodeList);
					}
				}
			}
		}
		return rtnList;
	}

	public void saveTdMsg(TdMsgMain msgMain, List<TdMsgSub> subList) {
		msgMain = (TdMsgMain) this.saveObj(msgMain);
		if (null != subList && subList.size() > 0) {
			for (TdMsgSub sub : subList) {
				sub.setTdMsgMain(msgMain);
				this.save(sub);
			}
		}
	}
	
	/** 更新待办任务是否已办 */
	public void updateTdMsgState(Integer rid, short state,Integer userId,String taskId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" update Td_Msg_Main a set TODO_STATE = 1 WHERE APPEND_KEYS = '").append(rid).append("'");
		sb.append(" and exists(select 1 from TD_MSG_SUB b where a.rid= b.main_id");
		sb.append(" and b.PUBLISH_MAN=").append(userId).append(")");
		sb.append(" and a.net_adr like '%").append(taskId).append("%'");
		this.em.createNativeQuery(sb.toString()).executeUpdate();
	}
	
	/** 根据菜单英文名称获取ID */
	public Integer getMenuIdByMenuEn (String menuEn) {
		StringBuilder sb = new StringBuilder();
		sb.append("select rid from ts_menu where menu_en = '").append(menuEn).append("'");
		List<Object> data = this.em.createNativeQuery(sb.toString()).getResultList();
		if (data != null && data.size() > 0) {
			return Integer.valueOf(data.get(0).toString());
		}
		
		return null;
	}

	@Transactional(readOnly = true)
	public Map<TdFlowNode, MutiNodeDealBean> processNbMap(
			Map<TdFlowNode, MutiNodeDealBean> dataMap) {
		if (null != dataMap && dataMap.size() > 0) {
			Set<Entry<TdFlowNode, MutiNodeDealBean>> entrySet = dataMap
					.entrySet();
			StringBuilder sb = new StringBuilder();
			for (Entry<TdFlowNode, MutiNodeDealBean> entry : entrySet) {
				if (null != entry.getValue()
						&& null != entry.getValue().getUserList()
						&& entry.getValue().getUserList().size() > 0) {
					for (TsUserInfo user : entry.getValue().getUserList()) {
						sb.append(",").append(user.getRid());
					}
				}
			}

			Map<Integer, String> map = new HashMap<Integer, String>();
			if (sb.length() > 0) {
				String userIds = sb.toString().replaceFirst(",", "");
				sb = new StringBuilder("SELECT DISTINCT T1.RID, T3.CODE_NAME");
				sb.append(" FROM TS_USER_INFO T1 ");
				sb.append(" INNER JOIN TB_SYS_EMP T2 ON T1.EMP_ID = T2.RID ");
				sb.append(" INNER JOIN TS_SIMPLE_CODE T3 ON T2.DUTY = T3.RID ");
				sb.append(" WHERE 1=1 ");
				sb.append(" AND T1.RID IN(").append(userIds).append(") ");

				List<Object[]> dutyList = super.em.createNativeQuery(
						sb.toString()).getResultList();
				if (null != dutyList && dutyList.size() > 0) {
					for (Object[] o : dutyList) {
						map.put(Integer.valueOf(o[0].toString()),
								o[1] == null ? null : o[1].toString());
					}
				}
			}

			if (null != map && map.size() > 0) {
				for (Entry<TdFlowNode, MutiNodeDealBean> entry : entrySet) {
					if (null != entry.getValue()
							&& null != entry.getValue().getUserList()
							&& entry.getValue().getUserList().size() > 0) {
						for (TsUserInfo user : entry.getValue().getUserList()) {
							user.setDutyName(map.get(user.getRid()));
						}
					}
				}
			}
		}
		return dataMap;
	}

	@Transactional(readOnly = true)
	public List<Object[]> findLastNodeAdvice(String taskId) {
		/**
		 * 当前任务的executionId与开始任务一致的话： 就到ACT_HI_TASKINST倒叙找第一个ID小于当前ID的任务 不一致的话：
		 * 找ACT_HI_TASKINST倒叙找第一个ID小于当前ID，且executionId与startEvent一致的任务
		 */
		StringBuilder sb = new StringBuilder(
				" SELECT A2.ID_, A2.EXECUTION_ID_, A2.ACT_ID_, A2.PROC_INST_ID_");
		sb.append(" FROM ACT_HI_ACTINST A1 ");
		sb.append(
				" INNER JOIN ACT_HI_ACTINST A2 ON A1.PROC_INST_ID_ = A2.PROC_INST_ID_ AND (A2.ACT_ID_ = 'startEvent' or A2.TASK_ID_ = '")
				.append(taskId).append("') ");
		sb.append(" WHERE A1.TASK_ID_ ='").append(taskId)
				.append("' ORDER BY TO_NUMBER(A2.ID_) ");

		List<Object[]> list = super.em.createNativeQuery(sb.toString())
				.getResultList();
		if (null != list && list.size() == 2) {
			String executionId = null; // startEvent的execution
			String procInstId = null;

			Object[] o1 = list.get(0);
			Object[] o2 = list.get(1);
			procInstId = o1[3].toString();
			executionId = o1[1].toString();

			sb = new StringBuilder(" SELECT A1.ID_, A1.EXECUTION_ID_ ");
			sb.append(" FROM ACT_HI_TASKINST A1 ");
			sb.append(" WHERE A1.PROC_INST_ID_ = '").append(procInstId)
					.append("' AND TO_NUMBER(A1.ID_) < '").append(taskId)
					.append("'");
			sb.append(" ORDER BY TO_NUMBER(A1.ID_) DESC ");

			List<Object[]> list2 = super.em.createNativeQuery(sb.toString())
					.getResultList();
			if (null != list2 && list2.size() > 0) {
				String lastTaskId = null;
				if (o1[1].equals(o2[1])) {
					lastTaskId = list2.get(0)[0].toString();
				} else {
					for (Object[] o : list2) {
						if (o[1].equals(executionId)) {
							lastTaskId = o[0].toString();
							break;
						}
					}
				}

				if (StringUtils.isNotBlank(lastTaskId)) {
					// 0-用户名 1-处理时间 2-处理意见
					sb = new StringBuilder(
							" SELECT T2.USERNAME, TO_CHAR(T1.DEAL_TIME, 'YYYY-MM-DD HH24:MI:SS') AS DEAL_TIME, T1.DEAL_ADVICE ");
					sb.append(" FROM ACT_HI_TASKRCD T1 ");
					sb.append(" INNER JOIN TS_USER_INFO T2 ON T1.USER_ID = T2.RID ");
					sb.append(" WHERE T1.TASK_ID IN (").append(lastTaskId)
							.append(") ");
					return super.em.createNativeQuery(sb.toString())
							.getResultList();
				}
			}
		}

		return new ArrayList<Object[]>(0);
	}

	public void terminateProcess(String taskId, Map<String, Object> variables,
			String advice, Integer userId, Integer trustId) {
		ProcessInstance pi = this.activitiService
				.findProcessInstanceByTaskId(taskId);
		/**
		 * 保存流程历史提交记录
		 */
		this.saveActRcdInfo(advice, pi.getId(), taskId, userId, ActOptType.T.toString(), trustId, null);
		if (null == variables) {
			variables = new HashMap<String, Object>();
		}
		variables.put("terminate", "1");
		variables.put("ACT_OPT_TYPE", "T");
		this.exeActScript(taskId, variables);
		this.activitiService.terminateProcess(taskId, variables, false);
	}

	public void finishProcess(String taskId, Map<String, Object> variables,
			String advice, Integer userId, Integer trustId) {
		ProcessInstance pi = this.activitiService
				.findProcessInstanceByTaskId(taskId);
		/**
		 * 保存流程历史提交记录
		 */
		List<TsUserInfo> selectedUserList = null;
		if (variables != null) {
			selectedUserList = (List<TsUserInfo>) variables.get("selectedUserList");
			variables.remove("selectedUserList");
		}
		
		this.saveActRcdInfo(advice, pi.getId(), taskId, userId, ActOptType.F.toString(), trustId, selectedUserList);
		

		if (null == variables) {
			variables = new HashMap<String, Object>();
		}
		variables.put("finish", "1");
		variables.put("ACT_OPT_TYPE", "F");
		this.exeActScript(taskId, variables);
		this.activitiService.terminateProcess(taskId, variables, false);
	}

	public void finishSelfProcess(String taskId, Map<String, Object> variables,
			String advice, Integer userId, Integer trustId) {
		ProcessInstance pi = this.activitiService
				.findProcessInstanceByTaskId(taskId);
		/**
		 * 保存流程历史提交记录
		 */
		List<TsUserInfo> selectedUserList = null;
		if (variables != null) {
			selectedUserList = (List<TsUserInfo>) variables.get("selectedUserList");
			variables.remove("selectedUserList");
		}
		
		this.saveActRcdInfo(advice, pi.getId(), taskId, userId, ActOptType.S.toString(), trustId, selectedUserList);

		if (null == variables) {
			variables = new HashMap<String, Object>();
		}
		variables.put("finishSelf", "1");
		variables.put("ACT_OPT_TYPE", "FS");
		this.exeActScript(taskId, variables);
		this.activitiService.terminateProcess(taskId, variables, true);
	}

	/**
	 * 根据任务id执行该节点下的脚本
	 * 
	 * @param taskId
	 */
	private void exeActScript(String taskId, Map<String, Object> variables) {
		Task task = this.activitiService.findTaskById(taskId);

		String defId = task.getProcessDefinitionId();
		String actNodeId = task.getTaskDefinitionKey();
		Integer scriptType = 1;

		// 获取该节点下的脚本
		List<TdFlowNodeScript> scriptList = this.findNodeScriptList(defId,
				actNodeId, scriptType);
		if (null != scriptList && scriptList.size() > 0) {
			TdFlowNodeScript script = scriptList.get(0);

			Map<String, Object> vars = this.activitiService
					.findAllVariables(task.getProcessInstanceId());
			if (vars == null) {
				vars = Maps.newHashMap();
			}
			vars.putAll(variables);
			// 执行脚本
			vars.put("task", task);

			String instanceId = task.getProcessInstanceId(); // 流程实例ID

			HistoricProcessInstance hpi = this.findInstanceById(instanceId);
			String startUserId = "";
			if (null != hpi) {
				startUserId = hpi.getStartUserId(); // 流程发起者ID
			} else {
				// 首节点，还没有HistoricProcessInstance
				startUserId = (String) vars.get(task.getTaskDefinitionKey()
						+ "_assignees");
			}
			String businessId = "";
			Object businessIdObj = vars.get("businessId");
			if (null != businessIdObj) {
				businessId = BusinessIdUtil.parsePK(businessIdObj.toString());
			}
			vars.put("taskId", taskId);
			vars.put("startUserId", startUserId);
			vars.put("businessId", businessId);
			vars.put("instanceId", instanceId);

			// 准备环境
			Map<String, IScript> beansMap = SpringContextHolder
					.getBeans(IScript.class);
			vars.putAll(beansMap);

			// 执行脚本
			System.err.println("【script】：" + script.getScript());
			GroovyScriptEngine scriptEngine = SpringContextHolder
					.getBean(GroovyScriptEngine.class);
			scriptEngine.execute(script.getScript(), vars);
		}

	}

	@Transactional(readOnly = true)
	public String flowIsEnd(String taskId) {
		if (StringUtils.isNotBlank(taskId)) {
			HistoricProcessInstance hisInstance = this.activitiService
					.findInstanceById(this.activitiService.findHisTaskById(
							taskId).getProcessInstanceId());
			if (null != hisInstance && null != hisInstance.getEndTime()) {
				return hisInstance.getId();
			}
		}
		return null;
	}

	public void saveProcessTitle(String instanceId, String businessKey) {
		StringBuilder sb = new StringBuilder();
		if (StringUtils.isNotBlank(instanceId)
				&& StringUtils.isNotBlank(businessKey)) {
			sb.append("UPDATE ACT_HI_PROCINST SET BUSINESS_KEY_ = '")
					.append(businessKey).append("' WHERE PROC_INST_ID_ = '")
					.append(instanceId).append("'");
			super.em.createNativeQuery(sb.toString()).executeUpdate();

			sb = new StringBuilder();
			sb.append("UPDATE ACT_RU_EXECUTION SET BUSINESS_KEY_ = '")
					.append(businessKey).append("' WHERE PROC_INST_ID_ = '")
					.append(instanceId).append("'");
			super.em.createNativeQuery(sb.toString()).executeUpdate();
		}
	}

	public void updataBusinessTableState(String tableName, String oldState,
			String newState) {
		if (StringUtils.isNotBlank(tableName)
				&& StringUtils.isNotBlank(oldState)
				&& StringUtils.isNotBlank(newState)) {
			String[] splits = tableName.split(",");
			StringBuilder sb = null;
			for (String s : splits) {
				sb = new StringBuilder(" UPDATE ");
				sb.append(s).append(" SET STATE_MARK = '").append(newState);
				sb.append("' WHERE STATE_MARK ='").append(oldState)
						.append("' ");
				super.em.createNativeQuery(sb.toString()).executeUpdate();
			}
		}
	}

	@Transactional(readOnly = true)
	public HistoricProcessInstance findInstanceById(String instanceId) {
		return this.activitiService.findInstanceById(instanceId);
	}

	@Transactional(readOnly = true)
	public List<TdFlowNodeScript> findNodeScriptList(String defId,
			String actNodeId, Integer scriptType) {
		Query query = super.em
				.createNamedQuery("TdFlowNodeScript.findByTaskNodeId");
		query.setParameter("defId", defId);
		query.setParameter("actNodeId", actNodeId);
		query.setParameter("scriptType", scriptType);
		return query.getResultList();
	}

	@Transactional(readOnly = true)
	public TdFlowNodeScript findNodeScriptByNodeid(Integer nodeId) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TdFlowNodeScript t where t.tdFlowNode.rid = ")
				.append(nodeId);
		List<TdFlowNodeScript> list = em.createQuery(sb.toString())
				.getResultList();
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	public void saveOrUpdateTdFlowNodeScript(TdFlowNodeScript tdFlowNodeScript) {
		if (tdFlowNodeScript.getRid() == null) {
			super.save(tdFlowNodeScript);
		} else {
			super.update(tdFlowNodeScript);
		}
	}

	public void deleteTdFlowNodeScript(Integer scriptid) {
		StringBuilder sb = new StringBuilder();
		sb.append("delete TD_FLOW_NODE_SCRIPT t where t.rid = ").append(
				scriptid);
		em.createNativeQuery(sb.toString()).executeUpdate();
	}

	@Transactional(readOnly = true)
	public String getDesc() {
		return "流程引擎服务";
	}

	@Transactional(readOnly = true)
	public String getVersion() {
		return "1.0.1";
	}

	@Transactional(readOnly = true)
	public TdFlowNodePage getDynaFormUrl(String pageCode) {
		if (StringUtils.isNotBlank(pageCode)) {
			StringBuilder sql = new StringBuilder();
			sql.append("select t from TdFlowNodePage t where t.pageCode = '")
					.append(pageCode).append("'");
			List<TdFlowNodePage> resultList = this.em.createQuery(
					sql.toString()).getResultList();
			if (null != resultList && resultList.size() > 0) {
				return null == resultList.get(0) ? null : resultList.get(0);
			}
		}
		return null;
	}

	public List<TdFormDef> findTdFormDefs() {
		List<TdFormDef> resultList = this.em.createNamedQuery(
				"TdFormDef.findAll").getResultList();
		return resultList;
	}

	/**
	 * 查询我所有的委托
	 * 
	 * @param userId
	 *            用户ID
	 * @return 0-委托人ID，委托的流程定义ID
	 */
	public List<Object[]> findMyTrust(Integer userId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT E1.USER_ID, LISTAGG(''''||E3.ACT_DEF_ID||'''', ',') WITHIN GROUP (ORDER BY E3.ACT_DEF_ID) AS  ACT_DEF_ID ");
		sb.append(" FROM TD_FLOW_TRUST E1 ");
		sb.append(" INNER JOIN TD_FLOW_TRUST_DEF E2 ON E2.TRUST_ID = E1.RID ");
		sb.append(" INNER JOIN TD_FLOW_DEF E3 ON E2.DEF_ID = E3.RID ");
		sb.append(" WHERE E1.STATE = '1' ");
		sb.append(" AND E1.TRUST_USER_ID = '").append(userId).append("' ");
		sb.append(" AND SYSDATE >= TO_DATE(TO_CHAR(E1.BEGIN_TIME, 'YYYY-MM-DD') || ' 00:00:00', 'YYYY-MM-DD HH24:MI:SS') ");
		sb.append(" AND SYSDATE <= TO_DATE(TO_CHAR(E1.END_TIME, 'YYYY-MM-DD') || ' 23:59:59', 'YYYY-MM-DD HH24:MI:SS') ");
		sb.append(" GROUP BY E1.USER_ID ");
		return super.em.createNativeQuery(sb.toString()).getResultList();
	}

	/**
	 * 保存流程委托
	 * 
	 * @param tdFlowTrust
	 */
	public void saveOrUpdateFlowTrust(TdFlowTrust tdFlowTrust) {
		if (tdFlowTrust.getRid() == null) {
			save(tdFlowTrust);
		} else {
			update(tdFlowTrust);
		}
	}

	/**
	 * 根据主键查询流程委托实体
	 * 
	 * @param rid
	 * @return
	 */
	@Transactional(readOnly = true)
	public TdFlowTrust findFlowTrustByRid(Integer rid) {
		TdFlowTrust trust = (TdFlowTrust) find(TdFlowTrust.class, rid);
		trust.getTdFlowTrustDefs().size();
		return trust;
	}

	/**
	 * 根据主键删除
	 * 
	 * @param rid
	 */
	public void deleteFlowTrustByRid(Integer rid) {
		delete(TdFlowTrust.class, rid);
	}

	public void updateFlowTrustState(Integer rid, Integer state) {
		StringBuilder sb = new StringBuilder();
		sb.append("UPDATE TD_FLOW_TRUST SET STATE = ").append(state)
				.append("WHERE RID = ").append(rid);
		em.createNativeQuery(sb.toString()).executeUpdate();
	}

	@Transactional(readOnly = true)
	public List<TdFlowDef> findFollowDefs(Integer userId) {
		/** -------查询所有的流程定义-------- **/
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT T4.RID,T4.DEF_NAME,T4.ACT_DEF_ID,T5.RID AS TYPEID,T5.TYPE_NAME");
		sb.append(" FROM ACT_RE_PROCDEF T2  INNER JOIN ");
		sb.append(" (SELECT MAX(T1.VERSION_) AS VERSION_, T1.KEY_ FROM ACT_RE_PROCDEF T1 GROUP BY T1.KEY_) T3 ");
		sb.append(" ON T2.VERSION_ = T3.VERSION_ AND T2.KEY_ = T3.KEY_ ");
		sb.append(" INNER JOIN TD_FLOW_DEF T4 ON T4.ACT_DEF_ID = T2.ID_");
		sb.append(" INNER JOIN TD_FLOW_TYPE T5 ON T4.TPYE_ID = T5.RID ");
		List<Object[]> result = em.createNativeQuery(sb.toString())
				.getResultList();
		List<TdFlowDef> defs = new ArrayList<>(result.size());
		outer: for (Object[] obj : result) {
			TdFlowDef def = new TdFlowDef();
			def.setRid(Integer.valueOf(obj[0].toString()));
			def.setDefName(obj[1].toString());
			def.setActDefId(obj[2].toString());
			TdFlowType flowType = new TdFlowType();
			flowType.setRid(Integer.valueOf(obj[3].toString()));
			flowType.setTypeName(obj[4].toString());
			def.setTdFlowType(flowType);
			// def.setTdFlowNodeList(findFlowNodes(def.getRid()));
			defs.add(def);
			/** -------遍历流程节点过滤第一个流程节点-------- **/
			// for (TdFlowNode node : def.getTdFlowNodeList()) {
			// if (node.getNums() == 1) {
			// continue;
			// }
			// if (null != node.getTdFlowRuleNodes()
			// && node.getTdFlowRuleNodes().size() > 0) {
			// for (TdFlowRuleNode t : node.getTdFlowRuleNodes()) {
			// // 设置selectIds
			// ResultCondition condition = new ResultCondition();
			// condition.setSelectIds(t.getSelectIds());
			// TdFlowRule flowRule = t.getTdFlowRule();
			// IActivitiRuleService ruleService = SpringContextHolder
			// .getBean(flowRule.getImpClass());
			// /** ---------------根据流程规则查询所有该节点的可操作人--------------- */
			// /** ------------若包含当前登陆人添加该流程定义 --------------- **/
			// List<TsUserInfo> userList = ruleService.findMan(
			// condition, em);
			// if (null != userList && userList.size() > 0) {
			// for (TsUserInfo user : userList) {
			// if (user.getRid().intValue() == userId
			// .intValue()) {
			// defs.add(def);
			// continue outer;
			// }
			// }
			// }
			// }
			// }
			// }
		}
		return defs;
	}

	/**
	 * 根据流程实例ID，进行流程撤销操作。 <br/>
	 * 一、可以撤销的条件如下： <br/>
	 * 1. 流程结束，“暂不支持撤销！” <br/>
	 * 1.1 结束节点的前一个节点的人可以撤销。<br/>
	 * 1.2 否则不可以撤销 <br/>
	 * 2. 流程未结束 <br/>
	 * 2.0 当前节点有自己存活的任务，不可以撤销 <br/>
	 * 2.1 当前节点是普通节点 <br/>
	 * 2.1.1 下一个节点是普通节点 <br/>
	 * 2.1.1.1 下一个节点的任务还未处理？可撤销：不可撤销 <br/>
	 * 2.1.2 下一个节点是Gateway <br/>
	 * 2.1.2.1 判断Gateway之前的节点是否有未处理的？可撤销：(判断Gateway之后的第一个节点是否都未处理的？可撤销：不可撤销) <br/>
	 * 2.1.3 下一个节点是会签节点 <br/>
	 * 2.1.3.1 该会签节点对应的任务是否都未处理？可撤销：不可撤销 <br/>
	 * 2.2 当前节点是会签节点 <br/>
	 * 2.2.1 当前节点是否还有未处理的任务？可撤销：（按照2.1.1，2.1.2，2.1.3检查一遍） <br/>
	 * 
	 * 二、撤销： 1. 当前节点是普通节点 2. 当前节点是会签节点 && 当前节点还有未提交的任务 3. 当前节点是会签节点 &&
	 * 已经流转到下一个节点了
	 * 
	 * 
	 * @param processId
	 *            流程实例ID <br/>
	 * @param userId
	 *            用户ID <br/>
	 * @param trustId
	 *            委托人ID <br/>
	 * @return 返回NULL表示成功，否则返回错误信息 <br/>
	 */
	public String cancelProcess(String processId, Integer userId,
			Integer trustId) {
		HistoricProcessInstance hpi = this.activitiService
				.findInstanceById(processId);
		if (null != hpi) {
			if (null != hpi.getEndTime()) {
				// 流程已经结束
				return "您不可以撤销该流程！";
			} else {
				// 流程未结束
				// 获取自己处理过的当前流程的最后一个节点
				String taskId = this.findMyLastTaskId(processId, userId);
				if (StringUtils.isNotBlank(taskId)) {
					// 根据任务ID获取TdFlowNode对象，即当前人处理的最后一个任务节点
					TdFlowNode flowNode = this.findFlowNodeByTaskId(taskId);

					if (DealType.HQJD.equals(flowNode.getDealType())) {
						return "您不可以撤销该流程！";
					}

					// 查找当前节点是否还有我未处理的任务，如果有就不允许撤销
					if (this.findCurNodeAliveTaskOfMe(processId,
							flowNode.getActNodeId(), userId)) {
						return "您不可以撤销该流程！";
					}

					// 获取当前人处理任务所在的节点后的所有第一个节点
					List<ActivityImpl> nextActList = this.activitiService
							.findNextTaskDefs(flowNode.getActNodeId(),
									hpi.getProcessDefinitionId());
					/**
					 * 所有下一个节点对应的任务（ID必须大于taskId，回退的不算），有以下几种情况： 1.有任务未提交 可撤销
					 * 2.有任务已提交 不可撤销 3.无任务 可撤销
					 * 即查找nextActList中对应所有任务(ID大于taskId)中有提交的任务的ID，找到就不可以撤销
					 */
					boolean hasEndedTask = false; // 下一个节点有提交过的任务，就不可撤销，没有就可以
					if (null != nextActList && nextActList.size() > 0) {
						hasEndedTask = this.findHandedTasksAfterMe(taskId,
								processId, nextActList);
					}

					if (hasEndedTask) {
						return "您不可以撤销该流程！";
					} else {
						/**
						 * 满足一切条件后，现在就可以撤销了。 1.删除所有下一个节点的任务和历史任务，（自己节点的任务不删）
						 * 2.恢复期望撤销的任务和历史任务，并删除流转意见
						 */
						this.activitiService.withdrawProcess(flowNode
								.getTdFlowDef().getActDefId(), processId,
								taskId, userId, trustId);
					}
				} else {
					// 没有找到当前操作人参与过的流程
					return "您不可以撤销该流程！";
				}
			}
		} else {
			// 未找到流程实例，可能已经被删掉
			return "请刷新界面！";
		}
		return null;
	}

	/**
	 * 根据流程实例ID和用户ID查询自己处理过的最后一个任务ID
	 * 
	 * @param processId
	 * @param userId
	 * @return
	 */
	private String findMyLastTaskId(String processId, Integer userId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T.TASK_ID FROM ACT_HI_TASKRCD T WHERE T.PROC_INST_ID = '");
		sb.append(processId).append("' AND T.USER_ID = '").append(userId)
				.append("' ORDER BY T.RID DESC ");
		List list = super.em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return list.get(0).toString();
		}
		return null;
	}

	/**
	 * 根据流程实例ID查询最后一个任务ID
	 * 
	 * @param processId
	 * @return
	 */
	public String findLastTaskId(String processId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T.TASK_ID FROM ACT_HI_TASKRCD T WHERE T.PROC_INST_ID = '");
		sb.append(processId).append("' ORDER BY T.RID DESC ");
		List list = super.em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return list.get(0).toString();
		}
		return null;
	}

	/**
	 * 查询当前节点是否还有我的任务未处理
	 * 
	 * @param processId
	 * @param taskDefKey
	 * @param userId
	 * @return
	 */
	private boolean findCurNodeAliveTaskOfMe(String processId,
			String taskDefKey, Integer userId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T1.Id_ ");
		sb.append(" FROM ACT_RU_TASK T1 ");
		sb.append(" INNER JOIN ACT_RU_IDENTITYLINK T2 ON T2.TASK_ID_ = T1.ID_ AND T2.TYPE_ = 'candidate' ");
		sb.append(" WHERE T1.PROC_INST_ID_ = '").append(processId).append("' ");
		sb.append(" AND T1.TASK_DEF_KEY_ = '").append(taskDefKey).append("' ");
		sb.append(" AND T2.USER_ID_ = '").append(userId).append("' ");

		List list = super.em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 查找我处理的后一个节点中所有提交的任务中是否有提交过的
	 * 
	 * @return
	 */
	private boolean findHandedTasksAfterMe(String taskId, String processId,
			List<ActivityImpl> nextActList) {
		StringBuilder s = new StringBuilder();
		for (ActivityImpl ai : nextActList) {
			s.append(",'").append(ai.getId()).append("'");
		}

		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T1.ID_, T1.END_TIME_");
		sb.append(" FROM ACT_HI_TASKINST T1");
		sb.append(" WHERE T1.PROC_INST_ID_ = '").append(processId).append("'");
		sb.append(" AND T1.TASK_DEF_KEY_ IN (")
				.append(s.toString().replaceFirst(",", "")).append(") ");
		sb.append(" AND TO_NUMBER(T1.ID_) > '").append(taskId).append("'");
		sb.append(" AND T1.END_TIME_ IS NOT NULL ");

		List list = super.em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return true;
		}
		return false;
	}

	/**
	 * 查找流程的标题模板，并解析返回
	 * 
	 * @param defId
	 * @param condition
	 * @return
	 */
	@Transactional(readOnly = true)
	public String analyFlowTilteTempl(Integer defId, MetaCondition condition) {
		List<TdFlowTitle> list = super.em
				.createNamedQuery("TdFlowTitle.findByDefId")
				.setParameter("defId", defId).getResultList();
		if (null != list) {
			for (TdFlowTitle t : list) {
				if (null != t.getIsDefault()
						&& t.getIsDefault().intValue() == 1) {
					return this.commService.resolveTemplatemeta(
							t.getTemplateContent(), condition, false);
				}
			}
		}
		return null;
	}

	/**
	 * 追回流程，即在流程未结束的情况下，追回自己的已办任务
	 * 
	 * @param processId
	 * @param userId
	 * @param trustId
	 * @return
	 */
	public String replevyProcess(String processId, Integer userId,
			Integer trustId) {
		HistoricProcessInstance hpi = this.activitiService
				.findInstanceById(processId);
		if (null != hpi) {
			if (null != hpi.getEndTime()) {
				// 流程已经结束
				return "该流程已结束，您不可以撤销该流程！";
			} else {
				// 流程未结束
				// 获取自己处理过的当前流程的最后一个节点
				String taskId = this.findMyLastTaskId(processId, userId);
				if (StringUtils.isNotBlank(taskId)) {
					this.activitiService.replevyProcess(taskId);
				} else {
					// 没有找到当前操作人参与过的流程
					return "您没有参与该流程，不允许撤销！";
				}
			}
		} else {
			// 未找到流程实例，可能已经被删掉
			return "请刷新界面！";
		}
		return null;
	}

	/**
	 * 查询某流程实例的当前所有任务待办人 <br/>
	 * 0-TASK_DEF_KEY_, <br/>
	 * 1-NAME_, <br/>
	 * 2-WM_CONCAT(Q1.USERNAME), <br/>
	 * 3-WM_CONCAT(Q1.USER_ID) <br/>
	 * 
	 * @param proInstId
	 */
	public List<Object[]> findActNodeUsers(String proInstId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT Q1.TASK_DEF_KEY_, Q1.NAME_, TO_CHAR(WM_CONCAT(Q1.USERNAME)),  TO_CHAR(WM_CONCAT(Q1.USER_ID)) ");
		sb.append(" FROM (");
		sb.append(" SELECT T1.TASK_DEF_KEY_, T1.NAME_, TO_NUMBER(T1.ASSIGNEE_) AS USER_ID, U1.USERNAME, ");
		sb.append(" TO_NUMBER(T1.ID_) AS ID_");
		sb.append(" FROM ACT_RU_TASK T1");
		sb.append(" INNER JOIN TS_USER_INFO U1 ON T1.ASSIGNEE_ = U1.RID");
		sb.append(" WHERE 1=1");
		sb.append(" AND T1.PROC_INST_ID_ = '").append(proInstId)
				.append("' AND T1.ASSIGNEE_ IS NOT NULL");
		sb.append(" UNION");
		sb.append(" SELECT T1.TASK_DEF_KEY_, T1.NAME_, TO_NUMBER(I.USER_ID_) AS USER_ID, U1.USERNAME, ");
		sb.append(" TO_NUMBER(T1.ID_) AS ID_");
		sb.append(" FROM ACT_RU_TASK T1 ");
		sb.append(" INNER JOIN ACT_RU_IDENTITYLINK I ON I.TASK_ID_ = T1.ID_ ");
		sb.append(" INNER JOIN TS_USER_INFO U1 ON I.USER_ID_ = U1.RID");
		sb.append(" WHERE 1=1");
		sb.append(" AND T1.PROC_INST_ID_ = '").append(proInstId)
				.append("' AND T1.ASSIGNEE_ IS NULL");
		sb.append(" AND I.TYPE_ = 'candidate' AND I.USER_ID_ IS NOT NULL");
		sb.append(" ) Q1");
		sb.append(" GROUP BY Q1.TASK_DEF_KEY_, Q1.NAME_");
		return super.em.createNativeQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public TdFlowNode findFlowNode(String proDefId, String taskDefKey) {
		Query query = super.em.createNamedQuery("TdFlowNode.findByTask");
		query.setParameter("actDefId", proDefId);
		query.setParameter("actNodeId", taskDefKey);
		
		List<TdFlowNode> list = query.getResultList();
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	/**
	 * 加减签操作
	 * 
	 * @param processId
	 * @param userId
	 * @param trustId
	 * @return key-节点ID， value-加签的人员，用于发送消息
	 */
	public Map<String, List<String>> counterSignProcess(String processId,
			List<Object[]> newProtoList) {
		System.err.println("【processId】：" + processId);
		List<Object[]> oldProtoList = this.findActNodeUsers(processId);
		/** key-流程节点ID, value-人员ID */
		Map<String, List<String>> newMap = Maps.newHashMap();
		Map<String, List<String>> oldMap = Maps.newHashMap();
		Map<String, List<String>> rtnMap = Maps.newHashMap();

		for (Object[] o : newProtoList) {
			newMap.put(o[0].toString(),
					StringUtils.string2list(o[3].toString(), ","));
		}
		for (Object[] o : oldProtoList) {
			oldMap.put(o[0].toString(),
					StringUtils.string2list(o[3].toString(), ","));
		}

		Set<Entry<String, List<String>>> entrySet = newMap.entrySet();
		for (Entry<String, List<String>> entry : entrySet) {
			List<String> newList = newMap.get(entry.getKey());
			List<String> oldList = oldMap.get(entry.getKey());
			List<String> allList = Lists.newArrayList(newList);
			allList.addAll(oldList);

			/**
			 * 要删的集合
			 */
			List<String> delList = CollectionUtil.getDiffentNoDuplicate(
					newList, allList);
			/**
			 * 要加的集合
			 */
			List<String> addList = CollectionUtil.getDiffentNoDuplicate(
					oldList, allList);

			if (null != addList && addList.size() > 0) {
				rtnMap.put(entry.getKey(), addList);
				this.activitiService.counterSignProcess(processId,
						entry.getKey(), addList, null);
			}
			if (null != delList && delList.size() > 0) {
				this.activitiService.counterSignProcess(processId,
						entry.getKey(), null, delList);
			}
		}
		return rtnMap;
	}

	/**
	 * 加减签之后要发送消息
	 * 
	 * @param instanceId
	 * @param map
	 *            key-节点ID， value-加签的人员，用于发送消息
	 * @param submiterId
	 * @return
	 */
	public String sendMsgAfterCounterSign(String instanceId,
			Map<String, List<String>> map, Integer submiterId) {
		HistoricProcessInstance hisInstance = this.activitiService
				.findInstanceById(instanceId);
		Map<String, String> taskMenMap = this.activitiService.findTaskAndMen(
				hisInstance, map);
		Set<Entry<String, String>> entrySet = taskMenMap.entrySet();

		StringBuilder sb = new StringBuilder();
		for (Entry<String, String> entry : entrySet) {
			TdMsgMain msgMain = new TdMsgMain();
			msgMain.setMessageType(MessageType.ACTIVITI);
			msgMain.setInfoTitle(hisInstance.getBusinessKey());
			msgMain.setNetAdr("/webapp/flow/tdFlowTaskEdit.faces?taskId="
					+ entry.getKey());
			msgMain.setNetName("待办任务");
			msgMain.setTsUserInfo(new TsUserInfo(submiterId));
			msgMain.setPublishTime(new Date());
			
			Object[] obj = this.findTaskData(entry.getKey());
			if (obj != null) {
				BusinessIdBean bean = JSON.parseObject(obj[1].toString(), BusinessIdBean.class);
				msgMain.setAppendKeys(bean.getRid());
			}
			msgMain.setIsTodo((short)1);
			msgMain.setSubType(1);
			msgMain.setTodoState((short)0);
			// 获取待办任务的菜单ID menu_en=flow_dbrw
			Integer menuId = this.getMenuIdByMenuEn("flow_dbrw");
			if (menuId != null) {
				msgMain.setMenuId(menuId);
			}
			
			this.save(msgMain);

			// 构建代办人IDS
			sb.append(",").append(entry.getValue());

			for (String s : entry.getValue().split(",")) {
				TdMsgSub sub = new TdMsgSub();
				sub.setTdMsgMain(msgMain);
				sub.setTsUserInfo(new TsUserInfo(Integer.valueOf(s)));
				this.save(sub);
			}
			// 极光推送
			// this.sendPushMsg(o[0].toString(), hisInstance.getBusinessKey(),
			// Integer.valueOf(o[1].toString()),instanceId,msgMain.getRid().toString());
		}
		return sb.toString().replaceFirst(",", "");
	}

	@Transactional(readOnly = true)
	public List<Object[]> findNodeAdvice(String taskId) {
		StringBuilder sb = new StringBuilder();
		// 0-用户名 1-处理时间 2-处理意见
		sb = new StringBuilder(
				" SELECT T2.USERNAME, TO_CHAR(T1.DEAL_TIME, 'YYYY-MM-DD HH24:MI:SS') AS DEAL_TIME, T1.DEAL_ADVICE ");
		sb.append(" FROM ACT_HI_TASKRCD T1 ");
		sb.append(" INNER JOIN TS_USER_INFO T2 ON T1.USER_ID = T2.RID ");
		sb.append(" WHERE T1.TASK_ID IN (").append(taskId).append(") ");
		return super.em.createNativeQuery(sb.toString()).getResultList();
	}
	
	//流程更新消息为已阅
	public void updateMsgState(String taskId,Integer userId){
		StringBuilder sb=new StringBuilder();
		sb.append("update TD_MSG_SUB set ACCEPT_STATE=1 where MAIN_ID in ");
		sb.append("(select rid from TD_MSG_MAIN where NET_ADR like '%").append(taskId).append("%')");
		sb.append(" and PUBLISH_MAN=").append(userId);
		this.em.createNativeQuery(sb.toString()).executeUpdate();
	}
	
	//流程更新待办任务为已办
	public void updateToDoState(String taskId,Integer userId){
		StringBuilder sb = new StringBuilder();
		sb.append(" update Td_Msg_Main a set TODO_STATE = 1 WHERE NET_ADR like '%").append(taskId).append("%'");
		sb.append(" and exists(select 1 from TD_MSG_SUB b where a.rid= b.main_id");
		sb.append(" and b.PUBLISH_MAN=").append(userId).append(")");
		this.em.createNativeQuery(sb.toString()).executeUpdate();
	}
	
	
	/**
	 * 保存流程提交记录
	 * @param advice 审核意见
	 * @param instId 流程实例
	 * @param taskId 任务ID
	 * @param userId 用户iD
	 * @param actOptType 操作类型
	 * @param trustId 托管userId
	 * @param nextUserIds 提交给哪些用户
	 */
	public void saveActRcdInfo(String advice, String instId, String taskId,
			Integer userId, String actOptType, Integer trustId, List<TsUserInfo> nextUserIds) {
		
		ActHiTaskrcd rcd = new ActHiTaskrcd();
		rcd.setDealAdvice(advice);
		rcd.setProcInstId(instId);
		rcd.setTaskId(taskId);
		rcd.setUserId(userId.toString());
		rcd.setActOptType(ActOptType.S.toString());
		if (!trustId.equals(userId)) {
			rcd.setTrustUserId(trustId);
		}
		
		if (nextUserIds != null && nextUserIds.size() > 0) {
			String userIds = "";
			for (TsUserInfo user : nextUserIds) {
				userIds += "," + user.getRid();
			}
			rcd.setNextUserIds(userIds.substring(1));
		}
		this.save(rcd);
	}
	/**
 	 * <p>修订内容：defId:流程定义的ACTIVITI节点ID
 	 * 			 busId：业务主键
 	 *           return:流程实例</p>
	 * 
 	 * @MethodReviser qrr,2017年12月29日,findProInstId
	 * */
	@Transactional(readOnly = true)
	public Object[] findProInstId(String defId,String busId) {
		if (StringUtils.isNotBlank(defId)&&StringUtils.isNotBlank(busId)) {
			StringBuilder sb = new StringBuilder();
			sb = new StringBuilder(" SELECT t1.proc_inst_id_,T4.rid ");
			sb.append(" FROM act_hi_taskinst T1 ");
			sb.append(" LEFT JOIN act_hi_varinst T2 ON T1.proc_inst_id_ = T2.proc_inst_id_ ");
			sb.append(" LEFT JOIN ACT_RE_PROCDEF T3 ON T3.id_ = T1.proc_def_id_ ");
			sb.append(" LEFT JOIN TD_FLOW_DEF T4 ON T4.ACT_DEF_ID = T1.proc_def_id_ ");
			sb.append(" WHERE T3.KEY_ in (").append(defId).append(")");
			sb.append(" and T2.name_ ='businessId'");
			sb.append(" and T2.text_ like '%").append(busId).append("%'");
			List<Object[]> list = super.em.createNativeQuery(sb.toString()).getResultList();
			if (null!=list && list.size()>0) {
				return list.get(0);
			}
		}
		return null;
	}
}
