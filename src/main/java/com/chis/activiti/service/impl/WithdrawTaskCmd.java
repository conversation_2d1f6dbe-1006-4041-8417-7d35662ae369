package com.chis.activiti.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.activiti.engine.TaskService;
import org.activiti.engine.impl.context.Context;
import org.activiti.engine.impl.interceptor.Command;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.persistence.entity.HistoricProcessInstanceEntity;
import org.activiti.engine.impl.persistence.entity.HistoricTaskInstanceEntity;
import org.activiti.engine.impl.pvm.PvmTransition;
import org.activiti.engine.impl.pvm.process.ActivityImpl;
import org.activiti.engine.impl.pvm.process.ProcessDefinitionImpl;
import org.activiti.engine.task.Task;
import org.springframework.jdbc.core.JdbcTemplate;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;


/**
 * 不判断是否可以撤销，执行此操作，默认都可以撤销。 判断是否撤销，是在业务中去判断。 <br/>
 * 
 * 不准备在用的原因是能实现的情况太少，只能普通流程无分支、会签
 * <AUTHOR>
 * @createTime 2016年4月27日
 */
@Deprecated
public class WithdrawTaskCmd implements Command<Integer> {

    private String defId;
    /** 该流程实例ID */
    private String processId;
    /** 该用户参与该流程的最后一个taskId */
    private String taskId;
    /** 用户ID */
    private Integer userId;
    /** 委托用户ID */
    private Integer trustId;

    public WithdrawTaskCmd(String defId, String processId, String taskId, Integer userId, Integer trustId) {
        this.defId = defId;
        this.processId = processId;
        this.taskId = taskId;
        this.userId = userId;
        this.trustId = trustId;
    }


    /**
     * -1：失败（流程结束，暂不支持回退）
     * 1：成功
     */
    @Override
    public Integer execute(CommandContext commandContext) {
        HistoricProcessInstanceEntity processInstance = Context.getCommandContext().getHistoricProcessInstanceEntityManager().findHistoricProcessInstance(processId);
        HistoricTaskInstanceEntity hisTask = Context.getCommandContext().getHistoricTaskInstanceEntityManager().findHistoricTaskInstanceById(taskId);
        TaskService taskService = Context.getProcessEngineConfiguration().getTaskService();
        JdbcTemplate jdbcTemplate = SpringContextHolder.getBean("jdbcTemplate");
        List<String> sqlList = new ArrayList<String>();


        if (null != processInstance.getEndTime()) {
            // 流程结束
        	return -1;
        } else {
            // 流程未结束
            /**
             * 1.删除所有下一个节点的任务和历史任务，（自己节点的任务不删） 2.恢复期望撤销的任务和历史任务
             */
            List<ActivityImpl> nextActList = this.findNextTaskDefs(hisTask.getTaskDefinitionKey(), this.defId);

            // 当前流程的所有待办任务
            List<Task> taskList = taskService.createTaskQuery().processInstanceId(hisTask.getProcessInstanceId()).list();
            // 需要结束掉的待办任务：查询出要退回到的节点后面所有的节点，其中含有待办任务的，就需要结束掉
            Set<Task> dealTaskSet = new HashSet<Task>();
            if (null != nextActList && nextActList.size() > 0) {
                for (ActivityImpl ai : nextActList) {
                    for (Task t : taskList) {
                        if (ai.getId().equals(t.getTaskDefinitionKey())) {
                            dealTaskSet.add(t);
                        }
                    }
                }
            }

            if (null != dealTaskSet && dealTaskSet.size() > 0) {
                for (Task t : dealTaskSet) {
                    sqlList.add("delete from act_hi_actinst where proc_inst_id_ = '"+this.processId+"' and task_id_ = '"+t.getId()+"' ");
                    sqlList.add("delete from ACT_HI_IDENTITYLINK where task_id_ = '"+t.getId()+"' ");
                    sqlList.add("delete from ACT_HI_DETAIL where task_id_ = '"+t.getId()+"' ");
                    sqlList.add("delete from act_hi_taskinst where proc_inst_id_ = '"+this.processId+"' and id_ = '"+t.getId()+"' ");
                    sqlList.add("delete from ACT_RU_IDENTITYLINK where task_id_ = '"+t.getId()+"' ");
                    sqlList.add("delete from act_ru_task where proc_inst_id_ = '"+this.processId+"' and id_ = '"+t.getId()+"' ");
                }
            }

            // 恢复Task
            sqlList.add("update act_hi_actinst set end_time_=null,duration_=null where proc_inst_id_ = '"+this.processId+"' and id_="+this.taskId);
            sqlList.add("update act_hi_taskinst set end_time_=null,duration_=null,delete_reason_=null where proc_inst_id_ = '"+this.processId+"' and id_="+this.taskId);

            StringBuilder sb = new StringBuilder();
            sb.append("insert into act_ru_task(ID_,REV_,EXECUTION_ID_,PROC_INST_ID_,PROC_DEF_ID_,");
            sb.append("NAME_,PARENT_TASK_ID_,DESCRIPTION_,TASK_DEF_KEY_,OWNER_,ASSIGNEE_,DELEGATION_,");
            sb.append("PRIORITY_,CREATE_TIME_,DUE_DATE_,SUSPENSION_STATE_) VALUES ('");
            sb.append(hisTask.getId()).append("',");
            sb.append(1).append(",");
            sb.append("'").append(hisTask.getExecutionId()).append("',");
            sb.append("'").append(hisTask.getProcessInstanceId()).append("',");
            sb.append("'").append(hisTask.getProcessDefinitionId()).append("',");
            sb.append("'").append(hisTask.getName()).append("',");
            if(StringUtils.isNotBlank(hisTask.getParentTaskId())) {
                sb.append("'").append(hisTask.getParentTaskId()).append("',");
            }else {
                sb.append("null,");
            }
            if(StringUtils.isNotBlank(hisTask.getDescription())) {
                sb.append("'").append(hisTask.getDescription()).append("',");
            }else {
                sb.append("null,");
            }
            sb.append("'").append(hisTask.getTaskDefinitionKey()).append("',");
            if(StringUtils.isNotBlank(hisTask.getOwner())) {
                sb.append("'").append(hisTask.getOwner()).append("',");
            }else {
                sb.append("null,");
            }
            if(StringUtils.isNotBlank(hisTask.getAssignee())) {
                sb.append("'").append(hisTask.getAssignee()).append("',");
            }else {
                sb.append("null,");
            }
            sb.append("null,");
            sb.append(hisTask.getPriority()).append(",");
            sb.append(" TO_DATE('").append(DateUtils.formatDateTime(hisTask.getStartTime())).append("','YYYY-MM-DD HH24:MI:SS'),");
            sb.append("null,1)");
            sqlList.add(sb.toString());

            //待办任务的待办人
            sb = new StringBuilder();
            sb.append(" insert into act_ru_identitylink(ID_,REV_,GROUP_ID_,TYPE_,USER_ID_,TASK_ID_,PROC_INST_ID_,PROC_DEF_ID_)");
            sb.append(" select * from (");
            sb.append(" select ID_,1,null as GROUP_ID_,'candidate',USER_ID_,TASK_ID_,null as PROC_INST_ID_,null as PROC_DEF_ID_ from ACT_HI_IDENTITYLINK ");
            sb.append(" where TASK_ID_ ='").append(this.taskId).append("' ");
            sb.append(" ) ");
            sqlList.add(sb.toString());

            sqlList.add("update act_ru_execution set rev_=rev_-1,act_id_='"+hisTask.getTaskDefinitionKey()+"' where id_="+hisTask.getExecutionId());

            if(null != sqlList && sqlList.size() > 0) {
                for(String s : sqlList) {
                	//System.err.println("【撤销SQL】：" + s);
                    jdbcTemplate.update(s);
                }
            }
        }
        return 1;
    }

    private List<ActivityImpl> findNextTaskDefs(String activitiNodeId, String defId) {
        List<ActivityImpl> rtnList = new ArrayList<ActivityImpl>();
        // 获取流程定义的所有节点
        ProcessDefinitionImpl definition = (ProcessDefinitionImpl) Context.getProcessEngineConfiguration().getRepositoryService().getProcessDefinition(defId);
        // 当前节点
        ActivityImpl currentAct = definition.findActivity(activitiNodeId);
        List<PvmTransition> transList = currentAct.getOutgoingTransitions();
        if (null != transList && transList.size() > 0) {
            for (PvmTransition pt : transList) {
                ActivityImpl targetAct = (ActivityImpl) pt.getDestination();
                String type = (String) targetAct.getProperty("type");
                if ("userTask".equals(type)) {
                    rtnList.add(targetAct);
                } else if (type.endsWith("Gateway")) {
                    // 网管节点要找其前后的节点
                    List<PvmTransition> transitionList2 = targetAct.getOutgoingTransitions();
                    if (null != transitionList2 && transitionList2.size() > 0) {
                        for (PvmTransition trans2 : transitionList2) {
                            ActivityImpl targetAct2 = (ActivityImpl) trans2.getDestination();
                            String type2 = (String) targetAct2.getProperty("type");
                            if ("userTask".equals(type2)) {
                                rtnList.add(targetAct2);
                            }
                        }
                    }
                }
            }
        }
        return rtnList;
    }

}
