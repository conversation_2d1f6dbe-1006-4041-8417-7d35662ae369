package com.chis.activiti.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.activiti.engine.ActivitiException;
import org.activiti.engine.TaskService;
import org.activiti.engine.impl.RepositoryServiceImpl;
import org.activiti.engine.impl.interceptor.Command;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.persistence.entity.ExecutionEntity;
import org.activiti.engine.impl.persistence.entity.TaskEntity;
import org.activiti.engine.impl.pvm.PvmTransition;
import org.activiti.engine.impl.pvm.process.ActivityImpl;
import org.activiti.engine.impl.pvm.process.ProcessDefinitionImpl;
import org.activiti.engine.task.Task;

import com.chis.common.utils.SpringContextHolder;

public class TaskCommitCmd implements Command<Void> {

	private String toTaskKey;
	private Map<String, Object> variables;
	private String type;
	private String taskId;
	private TaskService taskService;
	
	public TaskCommitCmd(String taskId, Map<String, Object> variables, String toTaskKey,
			String type) {
		this.toTaskKey = toTaskKey;
		this.type = type;
		this.variables = variables;
		this.taskId = taskId;
		
		taskService = SpringContextHolder.getBean("taskService");
	}
	
	@Override
	public Void execute(CommandContext commandContext) {
		
		if (this.toTaskKey == null || "".equals(this.toTaskKey.trim())) {
			throw new ActivitiException("task commit error : toTaskKey is null!");
		}
		
		// 根据taskId查找任务
		TaskEntity task = (TaskEntity) taskService.createTaskQuery().taskId(taskId).singleResult();
		// 参数
		if (this.variables != null) {
			task.setExecutionVariables(this.variables);
		}
		ExecutionEntity execu = task.getExecution();
		
		// 记录驳回的节点
		List<ActivityImpl> toActivityList = new ArrayList<ActivityImpl>();
		// 获取驳回的节点
		List<String> toTaskKeyList = Arrays.asList(this.toTaskKey.split(","));
		RepositoryServiceImpl repositoryService = (RepositoryServiceImpl) execu
				.getEngineServices().getRepositoryService();
		ProcessDefinitionImpl processDefinitionImpl = (ProcessDefinitionImpl) repositoryService
				.getDeployedProcessDefinition(execu.getProcessDefinitionId());
		// 装载驳回的节点
		for (String taskKey : toTaskKeyList) {
			ActivityImpl ai = processDefinitionImpl.findActivity(taskKey);
			toActivityList.add(ai);
		}
		
		// 查询活动的节点 
		List<Task> taskList = taskService.createTaskQuery().processInstanceId(task.getProcessInstanceId()).list();
		// 记录处理
		List<Task> dealTaskSet = new ArrayList<Task>();

		ActivityImpl endActivityImpl = processDefinitionImpl.findActivity("endEvent");

		if ((toActivityList == null) || (toActivityList.size() == 0)
				|| (endActivityImpl == null)) {
			throw new ActivitiException(this.toTaskKey + " to ActivityImpl is null!");
		}
		
		Map<String, ActivityImpl> allNextActivityMap = new HashMap<String, ActivityImpl>();
		for (ActivityImpl toActivityImpl : toActivityList) {
			iteratorNextNodes(toActivityImpl, allNextActivityMap);
		}
		
		if ((allNextActivityMap != null) && (allNextActivityMap.size() > 0)) {
			for (Task t : taskList) {
				if (allNextActivityMap.get(t.getTaskDefinitionKey()) != null) {
					dealTaskSet.add(t);
				}
			}
		}
		
		/** 任务为驳回且会签节点走此不走步骤， 当会签时，设置原因：驳回在提交会将上次的人员又加入代办 */
		if ("turnBack".equals(type) && task.getVariable(task.getTaskDefinitionKey()+"_multiUserList") != null) {
			task.setVariable(task.getTaskDefinitionKey()+"_multiUserList", new ArrayList<>());
		}
		
		for (Task t : dealTaskSet) {
			TaskEntity te = (TaskEntity) t;
			te.fireEvent("complete");
			commandContext.getTaskEntityManager().deleteTask(task, this.type, false);
			te.getExecution().removeTask(task);
			
			if (t.getId().equals(task.getId()))
				for (int i = 0; i < toActivityList.size(); i++) {
					ActivityImpl toActivityImpl = (ActivityImpl) toActivityList
							.get(i);
					if (i == toActivityList.size() - 1) {
						te.getExecution().executeActivity(toActivityImpl);
					} else {
						ExecutionEntity execution = te.getExecution()
								.createExecution();
						execution.executeActivity(toActivityImpl);
					}
				}
			else {
				te.getExecution().executeActivity(endActivityImpl);
			}
		}

		return null;
	}

	public void iteratorNextNodes(ActivityImpl currentAi,
			Map<String, ActivityImpl> allNextActivityMap) {
		List<PvmTransition> tranList = currentAi.getOutgoingTransitions();
		if ((tranList != null) && (tranList.size() > 0))
			for (PvmTransition tran : tranList) {
				ActivityImpl ai = (ActivityImpl) tran.getDestination();
				
				if (allNextActivityMap.containsKey(ai.getId())) {
					break;
				}
				String type = (String) ai.getProperty("type");
				if ("userTask".equals(type))
					allNextActivityMap.put(ai.getId(), ai);
				else if ("endEvent".equals(type)) {
					break;
				}
				iteratorNextNodes(ai, allNextActivityMap);
			}
	}
}
