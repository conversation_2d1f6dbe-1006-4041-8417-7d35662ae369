package com.chis.activiti.service.impl;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.zip.ZipInputStream;

import org.activiti.bpmn.model.BpmnModel;
import org.activiti.engine.HistoryService;
import org.activiti.engine.IdentityService;
import org.activiti.engine.ManagementService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricVariableInstance;
import org.activiti.engine.impl.RepositoryServiceImpl;
import org.activiti.engine.impl.bpmn.behavior.UserTaskActivityBehavior;
import org.activiti.engine.impl.bpmn.diagram.ProcessDiagramGenerator;
import org.activiti.engine.impl.interceptor.Command;
import org.activiti.engine.impl.javax.el.ExpressionFactory;
import org.activiti.engine.impl.javax.el.ValueExpression;
import org.activiti.engine.impl.juel.ExpressionFactoryImpl;
import org.activiti.engine.impl.juel.SimpleContext;
import org.activiti.engine.impl.persistence.entity.ExecutionEntity;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.activiti.engine.impl.persistence.entity.TaskEntity;
import org.activiti.engine.impl.pvm.PvmActivity;
import org.activiti.engine.impl.pvm.PvmTransition;
import org.activiti.engine.impl.pvm.process.ActivityImpl;
import org.activiti.engine.impl.pvm.process.ProcessDefinitionImpl;
import org.activiti.engine.impl.pvm.process.TransitionImpl;
import org.activiti.engine.impl.task.TaskDefinition;
import org.activiti.engine.repository.Deployment;
import org.activiti.engine.runtime.Execution;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.activiti.spring.ProcessEngineFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chis.common.utils.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

@Service
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class ActivitiServiceImpl implements Serializable {

	private static final long serialVersionUID = 3576498110849853339L;
	
	@Autowired
	private ProcessEngineFactoryBean processEngine;
	@Autowired
	private RuntimeService runtimeService;
	@Autowired
	private TaskService taskService;
	@Autowired
	private HistoryService historyService;
	@Autowired
	private RepositoryService repositoryService;
	@Autowired
	private IdentityService identityService;
	@Autowired
	private ManagementService managementService;
	
	
	public String deployProcessDefinition(String zipPath, String zipName) {
		InputStream is = null;
		ZipInputStream zis = null;
		try {
			is = new FileInputStream(zipPath);
			zis = new ZipInputStream(is);
			Deployment deploy = repositoryService.createDeployment().name(zipName).addZipInputStream(zis).deploy();
			ProcessDefinitionEntity defEntity = (ProcessDefinitionEntity) repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
			return defEntity.getId();
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		} finally {
			try {
				zis.close();
				is.close();
			} catch (IOException e) {
				e.printStackTrace();
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			}
		}
	}

	@Transactional(readOnly=true)
	public List<ActivityImpl> findTaskNodes(String defId) {
		List<ActivityImpl> rtnList = new ArrayList<ActivityImpl>(0);
		ProcessDefinitionEntity defEntity = (ProcessDefinitionEntity) repositoryService.getProcessDefinition(defId);
		if (null != defEntity) {
			List<ActivityImpl> activitiList = defEntity.getActivities();
			if (null != activitiList && activitiList.size() > 0) {
				for (ActivityImpl ai : activitiList) {
					if ("userTask".equals(ai.getProperty("type"))) {
						rtnList.add(ai);
					}
				}
			}
		}
		return rtnList;
	}

	@Transactional(readOnly=true)
	public int findDefVersion(String defId) {
		int version = 0;
		ProcessDefinitionEntity defEntity = (ProcessDefinitionEntity) repositoryService.getProcessDefinition(defId);
		if (null != defEntity) {
			version = defEntity.getVersion();
		}
		return version;
	}

	public String startProcessInstance(String defId, Map<String, Object> map, String userId, String title) {
		identityService.setAuthenticatedUserId(userId);
		ProcessInstance processInstance = runtimeService.startProcessInstanceById(defId, title, map);
		return processInstance.getId();
	}

	public void saveVariable(String taskId, Map<String, Object> map) {
		if (null != map && map.size() > 0) {
			Set<String> keySet = map.keySet();
			for (String key : keySet) {
				taskService.setVariable(taskId, key, map.get(key));
			}
		}
	}

	@Transactional(readOnly=true)
	public ProcessInstance findProcessInstanceByTaskId(String taskId) {
		ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(findTaskById(taskId).getProcessInstanceId()).singleResult();
		return processInstance;
	}

	@Transactional(readOnly=true)
	public TaskEntity findTaskById(String taskId) {
		TaskEntity task = (TaskEntity) taskService.createTaskQuery().taskId(taskId).singleResult();
		return task;
	}
	
	/**
	 * 根据流程定义ID，当前节点ID获取下一个任务节点，不考虑分支上的条件，有分支都认为符合条件
	 * @param activitiNodeId 当前节点的id，如sjTask
	 * @param procInstId 实例编号
	 * @param variables 流程中的变量
	 * @return
	 */
	@Transactional(readOnly=true)
	public List<ActivityImpl> findNextTaskDefs(String activitiNodeId, String defId) {
		List<ActivityImpl> rtnList = Lists.newArrayList();
		// 获取流程定义的所有节点
		ProcessDefinitionImpl definition = (ProcessDefinitionImpl) repositoryService.getProcessDefinition(defId);
		//当前节点
		ActivityImpl currentAct = definition.findActivity(activitiNodeId);
		List<PvmTransition> transList = currentAct.getOutgoingTransitions();
		if(null != transList && transList.size() > 0) {
			for(PvmTransition pt : transList) {
				ActivityImpl targetAct = (ActivityImpl) pt.getDestination();
				String type = (String) targetAct.getProperty("type");
				if("userTask".equals(type)) {
					rtnList.add(targetAct);
				}else if(type.endsWith("Gateway")) {
					//网管节点要找其前后的节点
					List<PvmTransition> transitionList2 = targetAct.getOutgoingTransitions();
					if(null != transitionList2 && transitionList2.size() > 0) {
						for(PvmTransition trans2 : transitionList2) {
							ActivityImpl targetAct2 = (ActivityImpl)trans2.getDestination();
							String type2 = (String) targetAct2.getProperty("type");
							if("userTask".equals(type2)) {
								rtnList.add(targetAct2);
							}
						}
					}
				}
			}
		}
		return rtnList;
	}
	
	/**
	 * 根据实例编号查找下一个任务节点
	 * @param activitiNodeId 当前节点的id，如sjTask
	 * @param procInstId 实例编号
	 * @param variables 流程中的变量
	 * @return
	 */
	@Transactional(readOnly=true)
	public List<PvmActivity> findNextTaskDefs(String activitiNodeId, String procInstId, Map<String, Object> variables) {
		// 流程标示
		String processDefinitionId = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInstId).singleResult().getProcessDefinitionId();
		//流程定义实体对象
		ProcessDefinitionEntity def = (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService).getDeployedProcessDefinition(processDefinitionId);
		// 获得当前任务的所有节点
		List<ActivityImpl> activitiList = def.getActivities();
		String id = null;
		for (ActivityImpl activityImpl : activitiList) {
			id = activityImpl.getId();
			if (activitiNodeId.equals(id)) {
				return nextTaskDefs(activityImpl, variables);
			}
		}
		return null;
	}
	
	/**
	 * 查询下一步要经过的节点
	 * @param activityImpl 当前节点对象
	 * @param variables 流程变量（可能有条件）
	 * @return 任务节点集合
	 */
	private List<PvmActivity> nextTaskDefs(ActivityImpl activityImpl, Map<String, Object> variables) {
		List<PvmActivity> rtnList = new ArrayList<PvmActivity>();
		
		/**
		 * 从当前节点发出去的线
		 */
		List<PvmTransition> outTransitions = activityImpl.getOutgoingTransitions();
		
		for (PvmTransition tr : outTransitions) {
			PvmActivity ac = tr.getDestination(); // 获取线路的终点节点
			
			/**
			 * 节点的类型：
			 * userTask-用户普通任务
			 * exclusiveGateway-网关，符合条件的第一条路
			 * inclusiveGateway-网关，符合条件的就走，可并行
			 * parallelGateway-网关，有多条路，走多少条路
			 * 
			 */
			Object types = ac.getProperty("type");
			System.out.println(types);
			
			if ("exclusiveGateway".equals(types)) {
				List<PvmTransition> outList  = ac.getOutgoingTransitions();
				for(PvmTransition tr1 : outList) {
					String elString = (String) tr1.getProperty("conditionText");
					try {
						if(this.resloveJuel(elString, variables)) {
							if("userTask".equals(tr1.getDestination().getProperty("type"))) {
//								rtnList.add(((UserTaskActivityBehavior) ((ActivityImpl) tr1.getDestination()).getActivityBehavior()).getTaskDefinition()); 
								rtnList.add(tr1.getDestination()); 
								return rtnList;
							}else {
								return this.nextTaskDefs((ActivityImpl)tr1.getDestination(), variables);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
						throw new RuntimeException("EL表达式解析有误！");
					}
				}
			} else if ("userTask".equals(types)) {
				rtnList.add(ac);
			} else if ("parallelGateway".equals(types)) {
				List<PvmTransition> outList  = ac.getOutgoingTransitions();
				for(PvmTransition tr1 : outList) {
					if("userTask".equals(tr1.getDestination().getProperty("type"))) {
						rtnList.add(tr1.getDestination()); 
					}else {
						rtnList.addAll(this.nextTaskDefs((ActivityImpl)tr1.getDestination(), variables));
					}
				}
			}else if ("inclusiveGateway".equals(types)) {
				List<PvmTransition> outList  = ac.getOutgoingTransitions();
				for(PvmTransition tr1 : outList) {
					String elString = (String) tr1.getProperty("conditionText");
					try {
						if(this.resloveJuel(elString, variables)) {
							if("userTask".equals(tr1.getDestination().getProperty("type"))) {
								rtnList.add(tr1.getDestination()); 
							}else {
								rtnList.addAll(this.nextTaskDefs((ActivityImpl)tr1.getDestination(), variables));
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
						throw new RuntimeException("EL表达式解析有误！");
					}
				}
			}
		}
		return rtnList;
	}
	
	/**
	 * 根据实例编号查找下一个任务节点
	 * 
	 * @param procInstId 实例编号
	 * @param variables 流程中的变量
	 * @return
	 */
	@Transactional(readOnly=true)
	public TaskDefinition nextTaskDefinition(String procInstId, Map<String, Object> variables) {
		// 流程标示
		String processDefinitionId = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInstId).singleResult().getProcessDefinitionId();

		ProcessDefinitionEntity def = (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService).getDeployedProcessDefinition(processDefinitionId);
		// 执行实例
		ExecutionEntity execution = (ExecutionEntity) runtimeService.createProcessInstanceQuery().processInstanceId(procInstId).singleResult();
		// 当前实例的执行到哪个节点
		String activitiId = execution.getActivityId();
		// 获得当前任务的所有节点
		List<ActivityImpl> activitiList = def.getActivities();
		String id = null;
		for (ActivityImpl activityImpl : activitiList) {
			id = activityImpl.getId();
			if (activitiId.equals(id)) {
				//System.out.println("当前任务：" + activityImpl.getProperty("name"));
				return nextTaskDefinition(activityImpl, activityImpl.getId(), variables);
				// System.out.println(taskDefinition.getCandidateGroupIdExpressions().toArray()[0]);
				// return taskDefinition;
			}
		}
		return null;
	}
	
	/**
	 * 下一个任务节点
	 * 
	 * @param activityImpl
	 *            当前节点
	 * @param activityId 当前节点ID
	 * @param variables 流程变量
	 * @return
	 */
	private TaskDefinition nextTaskDefinition(ActivityImpl activityImpl, String activityId, Map<String, Object> variables) {
		if ("userTask".equals(activityImpl.getProperty("type")) && !activityId.equals(activityImpl.getId())) {
			TaskDefinition taskDefinition = ((UserTaskActivityBehavior) activityImpl.getActivityBehavior()).getTaskDefinition();
			return taskDefinition;
		} else {
			List<PvmTransition> outTransitions = activityImpl.getOutgoingTransitions();
			List<PvmTransition> outTransitionsTemp = null;
			for (PvmTransition tr : outTransitions) {
				PvmActivity ac = tr.getDestination(); // 获取线路的终点节点
				if ("exclusiveGateway".equals(ac.getProperty("type"))) {
					outTransitionsTemp = ac.getOutgoingTransitions();
					if (outTransitionsTemp.size() == 1) {
						return nextTaskDefinition((ActivityImpl) outTransitionsTemp.get(0).getDestination(), activityId, variables);
					} else if (outTransitionsTemp.size() > 1) {
						for (PvmTransition tr1 : outTransitionsTemp) {
							String elString = (String) tr1.getProperty("conditionText");
							try {
								if(this.resloveJuel(elString, variables)) {
									return nextTaskDefinition((ActivityImpl) tr1.getDestination(), activityId, variables);
								}
							} catch (Exception e) {
								e.printStackTrace();
								throw new RuntimeException("EL表达式解析有误！");
							}
						}
					}
				} else if ("userTask".equals(ac.getProperty("type"))) {
					return ((UserTaskActivityBehavior) ((ActivityImpl) ac).getActivityBehavior()).getTaskDefinition();
				}
			}
			return null;
		}
	}

	
	public void commitProcess(String taskId, Map<String, Object> variables, String activityId) {
		if (variables == null) {
			variables = new HashMap<String, Object>();
		}
		// 跳转节点为空，默认提交操作
		if (StringUtils.isEmpty(activityId)) {
			/**
			 * 需要考虑当前提交是否是在分支合并,
			 * 如果分支合并的话，待办人要合并，不能替代
			 */
			if(null != variables && variables.size() > 0) {
				Map<String, Object> processVarMap = taskService.getVariables(taskId);
				ProcessInstance instance = this.findProcessInstanceByTaskId(taskId);
				Set<String> keySet = variables.keySet();
				for(String key : keySet) {
					if((key.endsWith("_assignees") || key.endsWith("_multiUserList")) && null != processVarMap.get(key)) {
						List<Execution> executionList = runtimeService.createExecutionQuery().processInstanceId(instance.getId()).list();
						if(null != executionList && executionList.size() > 0) {
							for(Execution execu : executionList) {
								if(StringUtils.isBlank(execu.getActivityId()) || execu.getActivityId().endsWith("Gateway")) {
									if(key.endsWith("_multiUserList")) {
										Set sets = new HashSet();
										sets.addAll((Collection)variables.get(key));
										sets.addAll((Collection)processVarMap.get(key));
										variables.put(key, sets);
									}else {
										if(null != variables.get(key)) {
											String addUsers = variables.get(key)+"";
											for(String s : addUsers.split(",")) {
												if(("," + processVarMap.get(key) + ",").indexOf("," + s + ",") < 0) {
													variables.put(key, processVarMap.get(key) + "," + s);
												}
											}
										}
									}
									break;
								}
							}
						}
					}
				}
			}
			taskService.complete(taskId, variables);
		} else {// 流程转向操作
			turnTransition(taskId, activityId, variables);
		}
	}

	
	public void addComment(String taskId, String advice) {
		ProcessInstance processInstance = this.findProcessInstanceByTaskId(taskId);
		taskService.addComment(taskId, processInstance.getId(), advice);
	}

	/**
	 * 根据当前任务ID，查询可以驳回的任务节点
	 * 
	 * @param taskId
	 *            当前任务ID
	 */
	@Transactional(readOnly=true)
	public List<ActivityImpl> findBackAvtivity(String taskId) throws Exception {
		List<ActivityImpl> rtnList = iteratorBackActivity(taskId, findActivitiImpl(taskId, null), new ArrayList<ActivityImpl>(), new ArrayList<ActivityImpl>());
		return rtnList;
	}

	@Transactional(readOnly=true)
	public ProcessDefinitionEntity findProcessDefinitionEntityByTaskId(String taskId) {
		//获取历史任务
		HistoricTaskInstance hisTask = this.findHisTaskById(taskId);
		// 取得流程定义
		ProcessDefinitionEntity processDefinition = (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService).getDeployedProcessDefinition(hisTask.getProcessDefinitionId());
		return processDefinition;
	}

	/**
	 * 反向排序list集合，便于驳回节点按顺序显示
	 * 
	 * @param list
	 * @return
	 */
	private List<ActivityImpl> reverList(List<ActivityImpl> list) {
		List<ActivityImpl> rtnList = new ArrayList<ActivityImpl>();
		// 由于迭代出现重复数据，排除重复
		for (int i = list.size(); i > 0; i--) {
			if (!rtnList.contains(list.get(i - 1)))
				rtnList.add(list.get(i - 1));
		}
		return rtnList;
	}

	/**
	 * 迭代循环流程树结构，查询当前节点可驳回的任务节点
	 * 
	 * @param taskId
	 *            当前任务ID
	 * @param currActivity
	 *            当前活动节点
	 * @param rtnList
	 *            存储回退节点集合
	 * @param tempList
	 *            临时存储节点集合（存储一次迭代过程中的同级userTask节点）
	 * @return 回退节点集合
	 */
	private List<ActivityImpl> iteratorBackActivity(String taskId, ActivityImpl currActivity, List<ActivityImpl> rtnList, List<ActivityImpl> tempList) throws Exception {
		// 查询流程定义，生成流程树结构
		ProcessInstance processInstance = findProcessInstanceByTaskId(taskId);

		// 当前节点的流入来源
		List<PvmTransition> incomingTransitions = currActivity.getIncomingTransitions();
		// 条件分支节点集合，userTask节点遍历完毕，迭代遍历此集合，查询条件分支对应的userTask节点
		List<ActivityImpl> exclusiveGateways = new ArrayList<ActivityImpl>();
		// 并行节点集合，userTask节点遍历完毕，迭代遍历此集合，查询并行节点对应的userTask节点
		List<ActivityImpl> parallelGateways = new ArrayList<ActivityImpl>();
		// 遍历当前节点所有流入路径
		for (PvmTransition pvmTransition : incomingTransitions) {
			TransitionImpl transitionImpl = (TransitionImpl) pvmTransition;
			ActivityImpl activityImpl = transitionImpl.getSource();
			String type = (String) activityImpl.getProperty("type");
			/**
			 * 并行节点配置要求：<br>
			 * 必须成对出现，且要求分别配置节点ID为:XXX_start(开始)，XXX_end(结束)
			 */
			if ("parallelGateway".equals(type)) {// 并行路线
				String gatewayId = activityImpl.getId();
				String gatewayType = gatewayId.substring(gatewayId.lastIndexOf("_") + 1);
				if ("START".equals(gatewayType.toUpperCase())) {// 并行起点，停止递归
					return rtnList;
				} else {// 并行终点，临时存储此节点，本次循环结束，迭代集合，查询对应的userTask节点
					parallelGateways.add(activityImpl);
				}
			} else if ("startEvent".equals(type)) {// 开始节点，停止递归
				return rtnList;
			} else if ("userTask".equals(type)) {// 用户任务
				tempList.add(activityImpl);
			} else if ("exclusiveGateway".equals(type)) {// 分支路线，临时存储此节点，本次循环结束，迭代集合，查询对应的userTask节点
				currActivity = transitionImpl.getSource();
				exclusiveGateways.add(currActivity);
			}
		}

		/**
		 * 迭代条件分支集合，查询对应的userTask节点
		 */
		for (ActivityImpl activityImpl : exclusiveGateways) {
			iteratorBackActivity(taskId, activityImpl, rtnList, tempList);
		}

		/**
		 * 迭代并行集合，查询对应的userTask节点
		 */
		for (ActivityImpl activityImpl : parallelGateways) {
			iteratorBackActivity(taskId, activityImpl, rtnList, tempList);
		}

		/**
		 * 根据同级userTask集合，过滤最近发生的节点
		 */
		currActivity = filterNewestActivity(processInstance, tempList);
		if (currActivity != null) {
			// 查询当前节点的流向是否为并行终点，并获取并行起点ID
			String id = findParallelGatewayId(currActivity);
			if (StringUtils.isEmpty(id)) {// 并行起点ID为空，此节点流向不是并行终点，符合驳回条件，存储此节点
				rtnList.add(currActivity);
			} else {// 根据并行起点ID查询当前节点，然后迭代查询其对应的userTask任务节点
				currActivity = findActivitiImpl(taskId, id);
			}

			// 清空本次迭代临时集合
			tempList.clear();
			// 执行下次迭代
			iteratorBackActivity(taskId, currActivity, rtnList, tempList);
		}
		return rtnList;
	}

	/**
	 * 根据当前节点，查询输出流向是否为并行终点，如果为并行终点，则拼装对应的并行起点ID
	 * 
	 * @param activityImpl
	 *            当前节点
	 * @return
	 */
	private String findParallelGatewayId(ActivityImpl activityImpl) {
		List<PvmTransition> incomingTransitions = activityImpl.getOutgoingTransitions();
		for (PvmTransition pvmTransition : incomingTransitions) {
			TransitionImpl transitionImpl = (TransitionImpl) pvmTransition;
			activityImpl = transitionImpl.getDestination();
			String type = (String) activityImpl.getProperty("type");
			if ("parallelGateway".equals(type)) {// 并行路线
				String gatewayId = activityImpl.getId();
				String gatewayType = gatewayId.substring(gatewayId.lastIndexOf("_") + 1);
				if ("END".equals(gatewayType.toUpperCase())) {
					return gatewayId.substring(0, gatewayId.lastIndexOf("_")) + "_start";
				}
			}
		}
		return null;
	}

	/**
	 * 根据流入任务集合，查询最近一次的流入任务节点
	 * 
	 * @param processInstance
	 *            流程实例
	 * @param tempList
	 *            流入任务集合
	 * @return
	 */
	private ActivityImpl filterNewestActivity(ProcessInstance processInstance, List<ActivityImpl> tempList) {
		while (tempList.size() > 0) {
			ActivityImpl activity_1 = tempList.get(0);
			HistoricActivityInstance activityInstance_1 = findHistoricUserTask(processInstance, activity_1.getId());
			if (activityInstance_1 == null) {
				tempList.remove(activity_1);
				continue;
			}

			if (tempList.size() > 1) {
				ActivityImpl activity_2 = tempList.get(1);
				HistoricActivityInstance activityInstance_2 = findHistoricUserTask(processInstance, activity_2.getId());
				if (activityInstance_2 == null) {
					tempList.remove(activity_2);
					continue;
				}

				if (activityInstance_1.getEndTime().before(activityInstance_2.getEndTime())) {
					tempList.remove(activity_1);
				} else {
					tempList.remove(activity_2);
				}
			} else {
				break;
			}
		}
		if (tempList.size() > 0) {
			return tempList.get(0);
		}
		return null;
	}

	/**
	 * 流程转向操作
	 * 
	 * @param taskId
	 *            当前任务ID
	 * @param activityId
	 *            目标节点任务ID
	 * @param variables
	 *            流程变量
	 * @throws Exception
	 */
	private void turnTransition(String taskId, String activityId, Map<String, Object> variables) {
		try {
			// 当前节点
			ActivityImpl currActivity = findActivitiImpl(taskId, null);
			// 清空当前流向
			List<PvmTransition> oriPvmTransitionList = clearTransition(currActivity);

			// 创建新流向
			TransitionImpl newTransition = currActivity.createOutgoingTransition();
			// 目标节点
			ActivityImpl pointActivity = findActivitiImpl(taskId, activityId);
			// 设置新流向的目标节点
			newTransition.setDestination(pointActivity);

			// 执行转向任务
			taskService.complete(taskId, variables);
			// 删除目标节点新流入
			pointActivity.getIncomingTransitions().remove(newTransition);

			// 还原以前流向
			restoreTransition(currActivity, oriPvmTransitionList);
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}

	/**
	 * 查询指定任务节点的最新记录
	 * 
	 * @param processInstance
	 *            流程实例
	 * @param activityId
	 * @return
	 */
	private HistoricActivityInstance findHistoricUserTask(ProcessInstance processInstance, String activityId) {
		HistoricActivityInstance rtnVal = null;
		// 查询当前流程实例审批结束的历史节点
		List<HistoricActivityInstance> historicActivityInstances = historyService.createHistoricActivityInstanceQuery().activityType("userTask").processInstanceId(processInstance.getId())
				.activityId(activityId).finished().orderByHistoricActivityInstanceEndTime().desc().list();
		if (historicActivityInstances.size() > 0) {
			rtnVal = historicActivityInstances.get(0);
		}

		return rtnVal;
	}

	/**
	 * 根据任务ID和节点ID获取活动节点 <br>
	 * 
	 * @param taskId
	 *            任务ID
	 * @param activityId
	 *            活动节点ID <br>
	 *            如果为null或""，则默认查询当前活动节点 <br>
	 *            如果为"end"，则查询结束节点 <br>
	 * 
	 * @return
	 * @throws Exception
	 */
	private ActivityImpl findActivitiImpl(String taskId, String activityId) throws Exception {
		// 取得流程定义
		ProcessDefinitionEntity processDefinition = findProcessDefinitionEntityByTaskId(taskId);

		// 获取当前活动节点ID
		if (StringUtils.isEmpty(activityId)) {
			activityId = findTaskById(taskId).getTaskDefinitionKey();
		}

		// 根据流程定义，获取该流程实例的结束节点
		if (activityId.toUpperCase().equals("END")) {
			for (ActivityImpl activityImpl : processDefinition.getActivities()) {
				List<PvmTransition> pvmTransitionList = activityImpl.getOutgoingTransitions();
				if (pvmTransitionList.isEmpty()) {
					return activityImpl;
				}
			}
		}

		// 根据节点ID，获取对应的活动节点
		ActivityImpl activityImpl = ((ProcessDefinitionImpl) processDefinition).findActivity(activityId);

		return activityImpl;
	}

	/**
	 * 清空指定活动节点流向
	 * 
	 * @param activityImpl
	 *            活动节点
	 * @return 节点流向集合
	 */
	private List<PvmTransition> clearTransition(ActivityImpl activityImpl) {
		// 存储当前节点所有流向临时变量
		List<PvmTransition> oriPvmTransitionList = new ArrayList<PvmTransition>();
		// 获取当前节点所有流向，存储到临时变量，然后清空
		List<PvmTransition> pvmTransitionList = activityImpl.getOutgoingTransitions();
		for (PvmTransition pvmTransition : pvmTransitionList) {
			oriPvmTransitionList.add(pvmTransition);
		}
		pvmTransitionList.clear();

		return oriPvmTransitionList;
	}

	/**
	 * 还原指定活动节点流向
	 * 
	 * @param activityImpl
	 *            活动节点
	 * @param oriPvmTransitionList
	 *            原有节点流向集合
	 */
	private void restoreTransition(ActivityImpl activityImpl, List<PvmTransition> oriPvmTransitionList) {
		// 清空现有流向
		List<PvmTransition> pvmTransitionList = activityImpl.getOutgoingTransitions();
		pvmTransitionList.clear();
		// 还原以前流向
		for (PvmTransition pvmTransition : oriPvmTransitionList) {
			pvmTransitionList.add(pvmTransition);
		}
	}

	@Transactional(readOnly=true)
	public InputStream findImageStream(String flowId, int type) {
		// type 1-任务id 2-流程实例ID
		String defId = null;
		String instanceId = null;
		if (type == 1) {
			ProcessDefinitionEntity processDefinitionEntity = this.findProcessDefinitionEntityByTaskId(flowId);
			defId = processDefinitionEntity.getId();
			instanceId = this.findHisTaskById(flowId).getProcessInstanceId();
		} else {
			HistoricProcessInstance hp = this.findInstanceById(flowId);
			defId = hp.getProcessDefinitionId();
		}
		BpmnModel bpmnModel = repositoryService.getBpmnModel(defId);
		InputStream is = null;
		if (type == 1) {
			is = ProcessDiagramGenerator.generateDiagram(bpmnModel, "png", runtimeService.getActiveActivityIds(instanceId));
		} else {
			List<ProcessInstance> list = runtimeService.createProcessInstanceQuery().processInstanceId(flowId).list();
			if (null != list && list.size() > 0) {
				is = ProcessDiagramGenerator.generateDiagram(bpmnModel, "png", runtimeService.getActiveActivityIds(flowId));
			} else {
				is = ProcessDiagramGenerator.generatePngDiagram(bpmnModel);
			}
		}
		return is;
	}

	@Transactional(readOnly=true)
	public HistoricProcessInstance findInstanceById(String instanceId) {
		HistoricProcessInstance pi = null;
		List<HistoricProcessInstance> list = historyService.createHistoricProcessInstanceQuery().processInstanceId(instanceId).list();
		if (null != list && list.size() > 0) {
			pi = list.get(0);
		}
		return pi;
	}
	
	@Transactional(readOnly=true)
	public HistoricProcessInstance findHisInstanceByTaskId(String taskId) {
		if(StringUtils.isNotBlank(taskId)) {
			HistoricTaskInstance hisTaskInstance = this.findHisTaskById(taskId);
			if(null != hisTaskInstance && StringUtils.isNotBlank(hisTaskInstance.getProcessInstanceId())) {
				return this.findInstanceById(hisTaskInstance.getProcessInstanceId());
			}
		}
		return null;
	}
	
	@Transactional(readOnly=true)
	public HistoricTaskInstance findHisTaskById(String taskId) {
		HistoricTaskInstance task = null;
		List<HistoricTaskInstance> list = historyService.createHistoricTaskInstanceQuery().taskId(taskId).list();
		if (null != list && list.size() > 0) {
			task = list.get(0);
		}
		return task;
	}

	
	public void updateTaskPriority(String instanceId, int priority) {
		List<Task> list = taskService.createTaskQuery().processInstanceId(instanceId).list();
		if (null != list && list.size() > 0) {
			for (Task t : list) {
				t.setPriority(priority);
				taskService.saveTask(t);
			}
		}
	}

	@Transactional(readOnly=true)
	public String findVariable(String instanceId, String key) {
		String rtnStr = null;
		List<HistoricVariableInstance> list = historyService.createHistoricVariableInstanceQuery().processInstanceId(instanceId).variableName(key).list();
		if (null != list && list.size() > 0) {
			HistoricVariableInstance hv = list.get(0);
			rtnStr = hv.getValue() == null ? "" : hv.getValue().toString();
		}
		return rtnStr;
	}
	
	@Transactional(readOnly=true)
	public List<HistoricVariableInstance> findAllHisVariables(String instanceId) {
		if(StringUtils.isNotBlank(instanceId)) {
			return historyService.createHistoricVariableInstanceQuery().processInstanceId(instanceId).list();
		}
		return null;
	}
	
	/**
	 * JUEL表达式解析，activiti内部的解析方式
	 * @param elString EL表达式
	 * @param variables 流程变量
	 * @return 解析结果
	 */
	private boolean resloveJuel(String elString, Map<String, Object> variables) {
		try {
			if(StringUtils.isNotBlank(elString)) {
				ExpressionFactory factory = new ExpressionFactoryImpl(); 
				SimpleContext simpleContext = new SimpleContext();  
				if(null != variables && variables.size() > 0) {
					Set<String> keySet = variables.keySet();
					for(String s:keySet) {
						Object value = variables.get(s);
						simpleContext.setVariable(s, factory.createValueExpression(value, Object.class));  
					}
				}
				
				ValueExpression e = factory.createValueExpression(simpleContext, elString, boolean.class);  
				return (Boolean) e.getValue(simpleContext);
			}
		} catch (Exception e) {
			System.err.println("【解析表达式有误】");
			e.printStackTrace();
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		}
		return true;
	} 
	
	
	@Transactional(readOnly=true)
	public Set<List<ActivityImpl>> findBackableNodeList(String taskId,boolean fstNodeDisp) {
		Set<List<ActivityImpl>> groupSet = new HashSet<List<ActivityImpl>>();
		if(StringUtils.isNotBlank(taskId)) {
			TaskEntity currentTask = this.findTaskById(taskId);
			String defId = currentTask.getProcessDefinitionId();
			
			// 获取流程定义的所有节点
			ProcessDefinitionImpl definition = (ProcessDefinitionImpl) repositoryService.getProcessDefinition(defId);
			
			//当前节点
			ActivityImpl currentActivity = definition.findActivity(currentTask.getTaskDefinitionKey());
			//开始节点
			ActivityImpl startActivity = definition.getInitial();
			//申请节点
			ActivityImpl startAct = (ActivityImpl)startActivity.getOutgoingTransitions().get(0).getDestination();
			
			
			List<PvmTransition> transitionList = currentActivity.getIncomingTransitions();
			if(null != transitionList && transitionList.size() > 0) {
				List<ActivityImpl> list = new ArrayList<ActivityImpl>();
				for(PvmTransition trans : transitionList) {
					ActivityImpl sourceAct = (ActivityImpl)trans.getSource();
					String type = (String) sourceAct.getProperty("type");
					if("userTask".equals(type)) {
						if(!startAct.getId().equals(sourceAct.getId())) {
							list.add(sourceAct);
						}
					}else if(type.endsWith("Gateway")) {
						//网管节点要找其前后的节点
						List<PvmTransition> transitionList2 = sourceAct.getIncomingTransitions();
						if(null != transitionList2 && transitionList2.size() > 0) {
							for(PvmTransition trans2 : transitionList2) {
								ActivityImpl sourceAct2 = (ActivityImpl)trans2.getSource();
								String type2 = (String) sourceAct2.getProperty("type");
								if("userTask".equals(type2)) {
									if(!startAct.getId().equals(sourceAct2.getId())) {
										list.add(sourceAct2);
									}
								}
							}
						}
					}
				}
				if(null != list && list.size() > 0) {
					groupSet.add(list);
				}
			}
			
			if(fstNodeDisp) {
				//添加申请节点
				List<ActivityImpl> startList = new ArrayList<ActivityImpl>();
				startList.add(startAct);
				groupSet.add(startList);
			}
		}
		return groupSet;
	}
	
	
	public void backTask(String taskId, Map<String, Object> variables, String activityId) {
		taskService.taskJump(taskId, variables, activityId, "turnback");
	}

	
	public void terminateProcess(String taskId, Map<String, Object> variables, boolean onlySelf) {
		//taskService.processTerminate(taskId, variables);
		
		HistoricProcessInstance hpi = this.findHisInstanceByTaskId(taskId);
		List<Task> needEndList = taskService.createTaskQuery().processInstanceId(hpi.getId()).list();
		if(null != needEndList && needEndList.size() > 0) {
			if(onlySelf) {
				for(Task ta : needEndList) {
					if(ta.getId().equals(taskId)) {
						turnTransition(ta.getId(), "endEvent", variables);
						break;
					}
				}
			}else {
				for(Task ta : needEndList) {
					turnTransition(ta.getId(), "endEvent", variables);
				}
			}
		}
	}
	
	@Transactional(readOnly=true)
	public Map<String, Object> findAllVariables(String instanceId) {
		Map<String, Object> rtnMap = new HashMap<String, Object>(); 
		
		List<Execution> list = runtimeService.createExecutionQuery().processInstanceId(instanceId).list();
		for(Execution e: list) {
			Map<String, Object> map = runtimeService.getVariables(e.getId());
			rtnMap.putAll(map);
		}
		return rtnMap;
	}
	
	@Transactional(readOnly=true)
	public Task findByTaskId(String taskId) {
		return taskService.createTaskQuery().taskId(taskId).singleResult();
	}
	
	/**
	 * 查询结束节点前的一个节点，如果有分支，找出分支上所有的前一个节点。
	 * @param defId 
	 * @param endActId
	 * @return
	 */
	@Transactional(readOnly=true)
	public List<ActivityImpl> findNodesBeforeEnd(String defId, String endActId) {
		List<ActivityImpl> rtnList = Lists.newArrayList();
		
		// 获取流程定义的所有节点
		ProcessDefinitionImpl definition = (ProcessDefinitionImpl) repositoryService.getProcessDefinition(defId);
		//结束节点
		ActivityImpl endAct = definition.findActivity(endActId);
		List<PvmTransition> transList = endAct.getIncomingTransitions();
		if(null != transList && transList.size() > 0) {
			for(PvmTransition pt : transList) {
				ActivityImpl sourceAct = (ActivityImpl) pt.getSource();
				String type = (String) sourceAct.getProperty("type");
				if("userTask".equals(type)) {
					rtnList.add(sourceAct);
				}else if(type.endsWith("Gateway")) {
					//网管节点要找其前后的节点
					List<PvmTransition> transitionList2 = sourceAct.getIncomingTransitions();
					if(null != transitionList2 && transitionList2.size() > 0) {
						for(PvmTransition trans2 : transitionList2) {
							ActivityImpl sourceAct2 = (ActivityImpl)trans2.getSource();
							String type2 = (String) sourceAct2.getProperty("type");
							if("userTask".equals(type2)) {
								rtnList.add(sourceAct2);
							}
						}
					}
				}
			}
		}
		return rtnList;
	}
	
	/**
	 * 执行流程的撤销操作
	 * @param defId
	 * @param processId
	 * @param taskId
	 * @param userId
	 * @param trustId
	 * @return
	 */
	@Deprecated
	public Integer withdrawProcess(String defId, String processId, String taskId, Integer userId, Integer trustId) {
		Command<Integer> cmd = new WithdrawTaskCmd(defId, processId, taskId, userId, trustId);
		return managementService.executeCommand(cmd);
	}
	
	/**
	 * 将已结束的流程追回
	 * @return
	 */
	public void replevyFlow(String processId, String userIds, String actNodeId) {
		ReplevyEndedProcessCmd cmd = new ReplevyEndedProcessCmd(processId, userIds, actNodeId);
		managementService.executeCommand(cmd);
	}
	
	/**
	 * 追回流程
	 * @param hisTaskId
	 * @return
	 */
	public String replevyProcess(String hisTaskId) {
		Command<String> cmd = new ReplevyTasksCmd(hisTaskId);
		return managementService.executeCommand(cmd);
	}
	
	/**
	 * 驳回
	 */
	public String rebutProcess(String processId, String actNodeId) {
		RebutTaskCmd cmd = new RebutTaskCmd(processId, actNodeId);
		return this.managementService.executeCommand(cmd);
	}
	
	public void rebutProcess(String taskId, Map<String, Object> variables, String actNodeId) {
//		// turnback
//		
		TaskCommitCmd cmd = new TaskCommitCmd(taskId, variables, actNodeId, "turnback");
		this.managementService.executeCommand(cmd);
	}
	
	/**
	 * 加签、减签
	 * @param instanceId
	 * @param activityId
	 * @param addList
	 * @param subtractList
	 * @return
	 */
	public String counterSignProcess(String instanceId, String activityId, List<String> addList, List<String> subtractList) {
		Command<String> cmd = new CounterSignCmd(instanceId, activityId, addList, subtractList);
		return managementService.executeCommand(cmd);
	}
	
	/**
	 * 加减签后新增人员的消息发送的条件转换
	 * @param hisProInst 历史流程实例
	 * @param map key-节点ID value-该节点上新增的人员
	 * @return key-任务ID value-待办人id，多个以,隔开
	 */
	public Map<String, String> findTaskAndMen(HistoricProcessInstance hisProInst, Map<String, List<String>> map) {
		Map<String, String> rtnMap = Maps.newHashMap();
		List<Task> taskList = taskService.createTaskQuery().processInstanceId(hisProInst.getId()).list();
		// 获取流程定义的所有节点
		ProcessDefinitionImpl definition = (ProcessDefinitionImpl) repositoryService.getProcessDefinition(hisProInst.getProcessDefinitionId());
		for(Task t : taskList) {
			if(map.containsKey(t.getTaskDefinitionKey())) {
				ActivityImpl activity = definition.findActivity(t.getTaskDefinitionKey());
				if(isParallel(activity)) {
					rtnMap.put(t.getId(), t.getAssignee());
				}else {
					rtnMap.put(t.getId(), StringUtils.list2string(map.get(t.getTaskDefinitionKey()), ","));
				}
			}
		}
		return rtnMap;
	}
	
    private boolean isParallel(ActivityImpl act) {
    	if(null != act.getProperty("multiInstance") && "parallel".equals(act.getProperty("multiInstance"))) {
    		return true;
    	}
        return false;
    }
	
}
