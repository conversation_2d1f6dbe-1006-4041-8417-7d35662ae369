package com.chis.activiti.service.impl;

import java.io.Serializable;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.entity.TdTestVacc;
import com.chis.modules.system.service.AbstractTemplate;

@Service
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class TestServiceImpl extends AbstractTemplate implements Serializable {
	
	private static final long serialVersionUID = 3576498110849853339L;

	public TdTestVacc findVacc(Integer rid) {
		return (TdTestVacc) this.find(TdTestVacc.class, rid);
	}

	public TdTestVacc saveOrUpdateVacc(TdTestVacc vacc) {
		TdTestVacc t =null;
		if(null != vacc.getRid()) {
			t = (TdTestVacc) this.updateObj(vacc);
		}else {
			t = (TdTestVacc) this.saveObj(vacc);
		}
		return t;
	}

	
	public String getDesc() {
		return "测试：请假服务";
	}

	public String getVersion() {
		return "1.0.0";
	}

}
















