package com.chis.activiti.service.impl;


import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricVariableInstance;
import org.activiti.engine.impl.RepositoryServiceImpl;
import org.activiti.engine.impl.interceptor.Command;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.persistence.entity.ExecutionEntity;
import org.activiti.engine.impl.persistence.entity.HistoricActivityInstanceEntity;
import org.activiti.engine.impl.persistence.entity.HistoricTaskInstanceEntity;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.activiti.engine.impl.persistence.entity.TaskEntity;
import org.activiti.engine.impl.pvm.PvmTransition;
import org.activiti.engine.impl.pvm.process.ActivityImpl;
import org.activiti.engine.impl.pvm.process.ProcessDefinitionImpl;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

/**
 * 追回任务，即无论当前任务在哪，只要流程没结束， 都可以追回。  <br/>
 *  1.当前节点是会签节点  <br/>
 *  1.1 当前节点存活着 <br/>
 *   1.1.1 有没有自己的待办任务，有则提示无需追回 <br/>
 *   1.1.2 没有自己的待办任务，根据executionEntity.executeActivity(activity)创建任务 <br/>
 * 
 * 1.2 当前节点未存活 1.2.1 查询该节点在ACT_HI_ACTINST中的所有executionId 1.2.2 根据 <br/>
 * 1.2.1找到存活的execution 1.2.3 根据1.2.2中存活的execution,查询存活的子节点的任务 1.2.4 结束1.2.3 中的任务 <br/>
 * 1.2.5 根据executionEntity.executeActivity(activity)创建任务，可能要创建execution <br/>
 * 
 * 
 * 2.当前节点不是会签节点 <br/>
 * 2.1.查询要退回节点的历史任务（该人员最后一次处理过的历史任务）<br/>
 * 2.2.根据2.1的结果中的executionId查询所有当前激活的子任务 <br/>
 * 2.3.结束2.2中的任务  <br/>
 * 2.3.1  判断这些任务所在的节点是否和要追回的节点一致  <br/>
 * 2.3.1.1 如果有一致的话，提示不允许追回。（可能流程之前已经被人追回，并且第二次走到要追回的节点，并且待办人还不是当前人）  <br/>
 * 2.3.1.2 如果没有一致的话，2.2任务要结束掉  <br/>
 * 2.4 根据executionEntity.executeActivity(activity)创建任务 <br/>
 * 
 * <AUTHOR>
 */
@Deprecated
public class RebutTaskCmd implements Command<String> {
	/** 当前流程实例ID */
	private String instanceId;
	/** 要退回的节点ID */
	private String activityId;
	/** 当前流程定义ID */
	private String defId;
	private CommandContext commandContext;

	private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
	private RuntimeService runtimeService;
	private TaskService taskService;
	private RepositoryService repositoryService;
	private HistoryService historyService;

	private RebutTaskCmd() {
	}

	public RebutTaskCmd(String instanceId, String activityId) {
		this.runtimeService = processEngine.getRuntimeService();
		this.taskService = processEngine.getTaskService();
		this.repositoryService = processEngine.getRepositoryService();
		this.historyService = processEngine.getHistoryService();
		this.activityId = activityId;
		this.instanceId = instanceId;
	}

	public String execute(CommandContext commandContext) {
		
		this.commandContext = commandContext;
		HistoricTaskInstanceEntity entity = null;
		List<HistoricTaskInstance> data = historyService.createHistoricTaskInstanceQuery().processInstanceId(instanceId).list();
		for (HistoricTaskInstance i : data) {
			entity = (HistoricTaskInstanceEntity) i;
			if (entity.getTaskDefinitionKey().equals(activityId)) {
				break;
			}
		}
		
		// HistoricActivityInstanceEntity hisEntity = this.commandContext.getHistoricActivityInstanceEntityManager().findHistoricActivityInstance(activityId, instanceId);
		this.defId = entity.getProcessDefinitionId();
		List<String> activeActivityIdList = this.runtimeService.getActiveActivityIds(this.instanceId);

		// 判断是否会签
		if (this.isParallel(this.getActivity())) {
				this.processDeadMutilInstance(entity);
				return null;
		} else {
			// 非会签
			if (null != activeActivityIdList && activeActivityIdList.size() > 0) {
				List<Task> taskList = this.taskService.createTaskQuery().processInstanceId(this.instanceId).list();
				Set<TaskEntity> delTaskList = new HashSet<TaskEntity>();
				if (null != taskList && taskList.size() > 0) {
					for (Task t : taskList) {
						if (this.nodeCanRtn(this.activityId, t.getTaskDefinitionKey())) {
							delTaskList.add((TaskEntity) t);
							continue;
						}
					}
				}

				if (null != delTaskList && delTaskList.size() > 0) {
					for (TaskEntity taskEntity : delTaskList) {
						if (!taskEntity.getExecutionId().equals(entity.getExecutionId())) {
							taskEntity.getExecution().deleteCascade("replevy");
						}
					}
				}
			}

			ExecutionEntity executionEntity = commandContext.getExecutionEntityManager().findExecutionById(entity.getExecutionId());
			if (null == executionEntity) {
				ExecutionEntity instanceExecution = commandContext.getExecutionEntityManager().findExecutionById(this.instanceId);
				ExecutionEntity childExecution = new ExecutionEntity();
				childExecution.setId(entity.getExecutionId());
				childExecution.setParentId(this.instanceId);
				childExecution.setProcessDefinitionId(this.defId);
				childExecution.setProcessInstance(instanceExecution.getProcessInstance());
				childExecution.setActive(true);
				childExecution.setConcurrent(true);
				childExecution.setScope(false);
				childExecution.setEventScope(false);
				childExecution.setSuspensionState(1);
				childExecution.setCachedEntityState(7);
				commandContext.getExecutionEntityManager().insert(childExecution);
				childExecution.executeActivity(this.getActivity());
			} else {
				/** 如果executionEntity上有task，还要删掉 */
				List<TaskEntity> taskList = executionEntity.getTasks();
				if (null != taskList && taskList.size() > 0) {
					for (TaskEntity te : taskList) {
						commandContext.getTaskEntityManager().deleteTask(te, "replevy", true);
						executionEntity.removeTask(te);// 执行规划的线
					}
				}
				executionEntity.setActive(true);
				executionEntity.executeActivity(this.getActivity());
			}
		}
		return null;
	}

	/**
	 * <li>获取活动的执行 , 子流程的活动执行是其孩子执行(并行多实例情况下) <li>串行情况下获取的结果数量为1
	 */
	protected List<ExecutionEntity> getActivieExecutions() {
		List<ExecutionEntity> activeExecutions = new ArrayList<ExecutionEntity>();
		ActivityImpl activity = getActivity();
		List<ExecutionEntity> executions = getChildExecutionByProcessInstanceId();

		for (ExecutionEntity execution : executions) {
			if (execution.isActive() && (execution.getActivityId().equals(activityId) || activity.contains(execution.getActivity()))) {
				activeExecutions.add(execution);
			}
		}

		return activeExecutions;
	}

	private boolean nodeCanRtn(String fromActId, String toActId) {
		ProcessDefinitionEntity processDefinition = this.findProcessDefinitionEntityByDefId();
		ActivityImpl fromAct = ((ProcessDefinitionImpl) processDefinition).findActivity(fromActId);
		ActivityImpl toAct = ((ProcessDefinitionImpl) processDefinition).findActivity(toActId);

		Set<ActivityImpl> sets = new HashSet<ActivityImpl>();
		this.nodeCanRtn(toAct, sets);
		for (ActivityImpl ai : sets) {
			if (ai.getId().equals(fromAct.getId())) {
				return true;
			}
		}
		return false;
	}

	private void nodeCanRtn(ActivityImpl toAct, Set<ActivityImpl> beforeSet) {
		List<PvmTransition> tranList1 = toAct.getIncomingTransitions();
		if (null != tranList1 && tranList1.size() > 0) {
			for (PvmTransition pt1 : tranList1) {
				ActivityImpl fromAct1 = (ActivityImpl) pt1.getSource();
				if (((String) fromAct1.getProperty("type")).endsWith("Task")) {
					if (beforeSet.contains(fromAct1)) {
						break;
					} else {
						beforeSet.add(fromAct1);
					}
				} else if (((String) fromAct1.getProperty("type")).equals("startEvent")) {
					return;
				}
				this.nodeCanRtn(fromAct1, beforeSet);
			}
		}
	}
	
	/**
	 * 回退到已经全部处理过的会签节点任务中的其中一个
	 */
	private void processDeadMutilInstance(HistoricTaskInstanceEntity htie) {
		List<Task> taskList = this.taskService.createTaskQuery().processInstanceId(this.instanceId).list();
		List<TaskEntity> delTaskList = new ArrayList<TaskEntity>();
		if (null != taskList && taskList.size() > 0) {
			for (Task t : taskList) {
				if (this.nodeCanRtn(this.activityId, t.getTaskDefinitionKey())) {
					delTaskList.add((TaskEntity) t);
				}
			}
		}

		ExecutionEntity instExecution = commandContext.getExecutionEntityManager().findExecutionById(this.instanceId);

		if (null != delTaskList && delTaskList.size() > 0) {
			for (TaskEntity taskEntity : delTaskList) {
				/**
				 * 当前待办任务的EXECUTION
				 * ID和流程实例ID一致的话，是不能删除的，只能删除任务，并且更新execution.
				 * 不一致的话就删除（可能也有问题，暂时先这么做，碰到特殊流程报错再改）
				 */
				if (taskEntity.getExecutionId().equals(this.instanceId)) {
					instExecution.setActivity(null);
					instExecution.setActive(false);

					commandContext.getTaskEntityManager().deleteTask(taskEntity, "replevy", true);
					instExecution.forceUpdate();
					
				} else {
					taskEntity.getExecution().deleteCascade("replevy");
				}
			}
		}
		
		Map<String, String> execMap = Maps.newHashMap();
		String parentExecutionId = null;
		
		//查找会签节点中的父execution
		List<HistoricActivityInstance> histActList = historyService.createHistoricActivityInstanceQuery().processInstanceId(this.instanceId).activityId(this.activityId).list();
		if(null != histActList && histActList.size() > 0) {
			for(HistoricActivityInstance histAct : histActList) {
				if(!execMap.containsKey(histAct.getExecutionId())) {
					parentExecutionId = histAct.getExecutionId();
					break;
				}
			}
		}
		
		String variableKey =  this.activityId+"_multiUserList";
		List<String> oldList = (List<String>) this.getAllVariablesInProcess(this.instanceId).get(variableKey);
		
		List<String> newList = Lists.newArrayList();
		newList.add(htie.getAssignee());
		
		this.runtimeService.setVariable(this.instanceId, variableKey, newList);
		
		ExecutionEntity parent = new ExecutionEntity();
		parent.setId(parentExecutionId);
		parent.setParentId(this.instanceId);
		parent.setProcessDefinitionId(defId);
		parent.setProcessInstance(instExecution.getProcessInstance());
		parent.setActivity(this.getActivity());
		parent.setActive(false);
		parent.setConcurrent(false);
		parent.setScope(true);
		parent.setEventScope(false);
		parent.setSuspensionState(1);
		parent.setCachedEntityState(0);

		commandContext.getExecutionEntityManager().insert(parent);
		
		ExecutionEntity childExecution = new ExecutionEntity();
		childExecution.setParentId(parentExecutionId);
		childExecution.setProcessDefinitionId(defId);
		childExecution.setProcessInstance(instExecution.getProcessInstance());
		childExecution.setActivity(this.getActivity());
		childExecution.setActive(true);
		childExecution.setConcurrent(true);
		childExecution.setScope(false);
		childExecution.setEventScope(false);
		childExecution.setSuspensionState(1);
		childExecution.setCachedEntityState(0);
		commandContext.getExecutionEntityManager().insert(childExecution);
		childExecution.executeActivity(this.getActivity());	
		
		this.runtimeService.setVariable(this.instanceId, variableKey, oldList);		
	}
	

	private ProcessDefinitionEntity findProcessDefinitionEntityByDefId() {
		ProcessDefinitionEntity processDefinition = (ProcessDefinitionEntity) ((RepositoryServiceImpl) this.repositoryService).getDeployedProcessDefinition(defId);
		return processDefinition;
	}

	/**
	 * <li>获取流程实例根的所有子执行
	 */
	protected List<ExecutionEntity> getChildExecutionByProcessInstanceId() {
		return commandContext.getExecutionEntityManager().findChildExecutionsByProcessInstanceId(this.instanceId);
	}

	/**
	 * <li>返回当前节点对象
	 */
	protected ActivityImpl getActivity() {
		return this.getProcessDefinition().findActivity(this.activityId);
	}

	/**
	 * <li>判断节点多实例类型是否是并发
	 */
	protected boolean isParallel(ActivityImpl act) {
		if (null != act.getProperty("multiInstance") && "parallel".equals(act.getProperty("multiInstance"))) {
			return true;
		}
		return false;
	}

	/**
	 * <li>返回流程定义对象
	 */
	protected ProcessDefinitionImpl getProcessDefinition() {
		return this.getProcessInstanceEntity().getProcessDefinition();
	}

	/**
	 * <li>返回流程实例的根执行对象
	 */
	protected ExecutionEntity getProcessInstanceEntity() {
		return commandContext.getExecutionEntityManager().findExecutionById(this.instanceId);
	}

	/**
	 * 根据流程实例ID，查询所有的流程变量
	 * 
	 * @param procInstId
	 * @return
	 */
	protected Map<String, Object> getAllVariablesInProcess(String procInstId) {
		Map<String, Object> map = Maps.newHashMap();
		List<HistoricVariableInstance> list = this.historyService.createHistoricVariableInstanceQuery().processInstanceId(procInstId).list();
		if (null != list && list.size() > 0) {
			for (HistoricVariableInstance hvi : list) {
				map.put(hvi.getVariableName(), hvi.getValue());
			}
		}
		return map;
	}

}

