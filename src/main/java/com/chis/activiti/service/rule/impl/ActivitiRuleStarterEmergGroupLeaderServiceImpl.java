package com.chis.activiti.service.rule.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 当前操作人所在单位的各应急处置组的领导 规则编码：1016 ResultCondition必要条件：当前登录人ID
 * DirectoryCondition必要条件：无
 * 
 * <AUTHOR>
 * @createDate 2014年12月3日上午8:43:08
 */
@Component(value="com.chis.activiti.service.rule.impl.ActivitiRuleStarterEmergGroupLeaderServiceImpl")
@Transactional(readOnly = true)
public class ActivitiRuleStarterEmergGroupLeaderServiceImpl implements IActivitiRuleService {

	@Override
	public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em) {
		List<TsUserInfo> userList = new ArrayList<TsUserInfo>();
		if (null != condition.getUserId()) {
			StringBuilder sb = new StringBuilder();
			sb.append(" SELECT T9.RID AS USERID, T9.USERNAME, ");
			sb.append(" ','||LISTAGG(C.OFFICEID,',') WITHIN GROUP(ORDER BY C.OFFICEID)||',' AS OFFICEIDS ,");
			sb.append(" LISTAGG(C.OFFICENAME,',') WITHIN GROUP(ORDER BY DECODE(C.ZKS, 1, -1, C.NUM)) AS OFFICENAMES,");
			sb.append(" T5.RID AS UNITID, T5.UNIT_SIMPNAME ");
			sb.append(" FROM TS_USER_INFO T1 ");
			sb.append(" INNER JOIN TS_UNIT T5 ON T1.UNIT_RID = T5.RID ");
			sb.append(" INNER JOIN TS_USER_INFO T9 ON T5.RID = T9.UNIT_RID");
			sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
			sb.append(" WHERE B.IF_REVEAL = '1' UNION ALL SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, ");
			sb.append(" 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
			sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID WHERE C.IF_REVEAL = '1'  ORDER BY C.NUM)) C ON C.RID = T9.EMP_ID");
			sb.append(" WHERE T9.IF_REVEAL = '1' AND T1.RID = '").append(condition.getUserId()).append("' ");
			sb.append(" AND T9.RID IN ( SELECT T8.PSN_ID  FROM TS_UNIT T5 INNER JOIN TB_EM_RANKS T6");
			sb.append(" ON T6.ORG_ID = T5.RID AND T6.IS_DEAL_GROUP = '1' INNER JOIN TB_EM_TEAM_MEMBERS T7");
			sb.append(" ON T7.TEAM_ID = T6.RID INNER JOIN TB_EM_MEMBERS T8");
			sb.append(" ON T8.RID = T7.MEMBER_ID AND T8.IS_LEADER = '1' AND T8.DUTY_STATE = '1' )");
			sb.append(" GROUP BY T9.RID, T9.USERNAME ,C.EMPNUM ,T5.RID,T5.UNIT_SIMPNAME,T5.UNIT_CODE");
			sb.append(" ORDER BY T5.UNIT_CODE, SUM(DECODE(C.ZKS, 1, C.NUM, NULL)) ,C.EMPNUM");
			List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
			if (null != list && list.size() > 0) {
				for (Object[] o : list) {
					TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
					t.setUsername(o[1] == null ? "" : o[1].toString());

					TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
					unit.setUnitSimpname(o[5].toString());

					t.setTsUnit(unit);
					t.setOfficeIds(String.valueOf(o[2]));
					t.setOfficeNames(String.valueOf(o[3]));
					userList.add(t);
				}
			}
		}
		return userList;
	}

	@Override
	public Map<String, String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em) {
		Map<String, String> map = new HashMap<String, String>();
		map.put("【本单位应急各处置组的领导】", "【本单位应急各处置组的领导】_1_1016");
		return map;
	}

	@Override
	public String desciption() {
		return "本单位应急各处置组的领导";
	}

}
