package com.chis.activiti.service.rule.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.activiti.utils.ActivitiRuleUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 与流程发起者相同部门的人
 * 规则编码：1009
 * 
 * ResultCondition必要条件：流程实例ID
 * DirectoryCondition必要条件：无
 * 
 * <AUTHOR>
 * @createDate 2014年9月3日上午10:42:49
 */
@Component(value="com.chis.activiti.service.rule.impl.ActivitiRuleStarterSameOfficeServiceImpl")
@Transactional(readOnly = true)
public class ActivitiRuleStarterSameOfficeServiceImpl implements IActivitiRuleService{

	@Override
	public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em) {
		List<TsUserInfo> list = new ArrayList<TsUserInfo>();
		if(null != condition && StringUtils.isNotBlank(condition.getProcessInstanceId())) {
			TsUserInfo startUser = ActivitiRuleUtil.findStartUserJz(condition.getProcessInstanceId(), em);
			if(null != startUser) {
				String officesIds = startUser.getOfficeId() == null? null:startUser.getOfficeId().toString();
				if(StringUtils.isBlank(officesIds))	{
					officesIds = startUser.getOfficeIds().substring(1,startUser.getOfficeIds().length()-1);
				}
				list = ActivitiRuleUtil.findUsersOfOfficeJz(officesIds, em);
			}
		}
		return list;
	}

	@Override
	public Map<String, String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em) {
		Map<String, String> map = new HashMap<String, String>();
		map.put("【与流程发起者相同部门的人】", "【与流程发起者相同部门的人】_1_1009");
		return map;
	}

	@Override
	public String desciption() {
		return "与流程发起者相同部门的人";
	}

}
