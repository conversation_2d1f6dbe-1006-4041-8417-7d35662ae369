package com.chis.activiti.service.rule.impl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 科室领导（根据选中的科室中，找到科室负责人） 规则编码：1014 ResultCondition必要条件：选中的ids
 * DirectoryCondition必要条件：当前登录人
 * 
 * <AUTHOR>
 */
@Component(value="com.chis.activiti.service.rule.impl.ActivitiRuleOfficeLeaderServiceImpl")
@Transactional(readOnly = true)
public class ActivitiRuleOfficeLeaderServiceImpl implements IActivitiRuleService {

	@Override
	public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em) {
		List<TsUserInfo> userList = new ArrayList<TsUserInfo>();
		if (StringUtils.isNotBlank(condition.getSelectIds())) {
			StringBuilder sb = new StringBuilder();
			
			sb.append("SELECT * FROM (");
			sb.append(" SELECT T1.RID AS USERID, T1.USERNAME, ','||LISTAGG(C.OFFICEID,',') WITHIN GROUP(ORDER BY C.OFFICEID)||',' AS OFFICEIDS ,");
			sb.append(" LISTAGG(C.OFFICENAME,',') WITHIN GROUP(ORDER BY DECODE(C.ZKS, 1, -1, C.NUM)) AS OFFICENAMES");
			sb.append(", T5.RID AS UNITID, T5.UNIT_SIMPNAME FROM TS_USER_INFO T1");
			sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
			sb.append("  WHERE B.IF_REVEAL = '1' AND A.IS_LEADER = '1' UNION ALL SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, ");
			sb.append(" 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
			sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID WHERE C.IF_REVEAL = '1' AND B.IS_LEADER = '1' ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
			sb.append(" INNER JOIN TS_UNIT T5 ON T1.UNIT_RID = T5.RID ");
			sb.append(" WHERE T1.IF_REVEAL = '1' ");
			sb.append(" GROUP BY T1.RID, T1.USERNAME ,C.EMPNUM ,T5.RID,T5.UNIT_SIMPNAME,T5.UNIT_CODE");
			sb.append(" ORDER BY T5.UNIT_CODE, SUM(DECODE(C.ZKS, 1, C.NUM, NULL)) ,C.EMPNUM ");
			sb.append(") D WHERE 1 = 1 AND (");
			//科室Id
			StringBuilder officeSql = new StringBuilder(); 
			String[] officeIdArr = condition.getSelectIds().split(",");
			for( String officeId : officeIdArr)	{
				officeSql.append(" OR D.OFFICEIDS LIKE '%,").append(officeId).append(",%'");
			}
			sb.append(officeSql.substring(3)).append(")");
			List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
			if(null != list && list.size() > 0) {
				for(Object[] o: list) {
					 TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
					 t.setUsername(o[1]==null?"":o[1].toString());
					 
					 TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
					 unit.setUnitSimpname(o[5].toString());
					 
					 t.setOfficeIds(String.valueOf(o[2]));
					 t.setOfficeNames(String.valueOf(o[3]));
					 t.setTsUnit(unit);
					 userList.add(t);
				}
			}
		}
		return userList;
	}

	@Override
	public Map<String, String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em) {
		Map<String, String> map = new LinkedHashMap<String, String>();
		StringBuilder sb = new StringBuilder();
		sb.append("  SELECT T.UNIT_RID FROM TS_USER_INFO T WHERE T.RID = '");
		sb.append(condition.getUserId()).append("' ");
		List<Object> list = em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			Object obj = list.get(0);

			sb = new StringBuilder();
			sb.append(" SELECT A.OFFICENAME, A.RID, A.UP_ID  ");
			sb.append(" FROM TS_OFFICE A  ");
			sb.append(" WHERE A.IF_REVEAL = '1' ");
			sb.append(" AND A.UNIT_RID = '").append(obj).append("' ");
			sb.append(" START WITH A.UP_ID IS NULL ");
			sb.append(" CONNECT BY PRIOR A.RID = A.UP_ID ");
			sb.append(" ORDER SIBLINGS BY A.RID ");

			List<Object[]> temList = em.createNativeQuery(sb.toString()).getResultList();
			if (null != temList && temList.size() > 0) {
				for (Object[] o : temList) {
					map.put(o[0].toString() + "【科室领导】", o[0].toString() + "【科室领导】_" + o[1] + "_1014");
				}
			}
		}
		return map;
	}

	@Override
	public String desciption() {
		return "科室领导";
	}

}
