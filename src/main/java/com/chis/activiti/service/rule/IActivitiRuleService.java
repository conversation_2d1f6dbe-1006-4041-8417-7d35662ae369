package com.chis.activiti.service.rule;

import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 用于选择待办人规则
 * 
 * 
 * 2014-9-2 david  接口重新设计，入参封装为javabean 
 * 
 * <AUTHOR>
 */
public interface IActivitiRuleService {

    /**
     * 根据查询条件查询待办人
     * @param condition 查询条件
     * @param em 实体管理器
     * @return 用户集合
     */
    public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em);

	/**
	 * 初始化数据字典
	 * @param condition 条件对象
	 * @param em 实体管理器
	 * @return key-显示的中文 value-字典中文_字典ID_规则编码
	 */
	public Map<String,String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em);

    /**
     * @return 描述
     */
    public String desciption();


}
