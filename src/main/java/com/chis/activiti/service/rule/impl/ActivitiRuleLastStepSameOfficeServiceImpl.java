package com.chis.activiti.service.rule.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.activiti.utils.ActivitiRuleUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 与前一步处理者相同部门的人 规则编码：1011
 * 
 * ResultCondition必要条件：上一节点ID DirectoryCondition必要条件：无
 * 
 * <AUTHOR>
 */
@Component(value="com.chis.activiti.service.rule.impl.ActivitiRuleLastStepSameOfficeServiceImpl")
@Transactional(readOnly = true)
public class ActivitiRuleLastStepSameOfficeServiceImpl implements IActivitiRuleService {

	@Override
	public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em) {
		List<TsUserInfo> userList = new ArrayList<TsUserInfo>();
		try {
			if (null != condition && StringUtils.isNotBlank(condition.getNodeId())) {
				//先查询出上一个节点的ID
				String lastNodeId = ActivitiRuleUtil.findLastNodeId(condition.getNodeId(), em);
				
				if(StringUtils.isNotBlank(lastNodeId)) {
					// 将查询条件对象克隆，重新设置当前节点ID
					ResultCondition conClone = (ResultCondition) condition.clone();
					conClone.setNodeId(lastNodeId);

					// 获取上一步的处理者
					List<TsUserInfo> list = ActivitiRuleUtil.findUsersOfNode(lastNodeId, conClone, em);
					
					// 再获取他们的同科室的人
					if (null != list && list.size() > 0) {
						Map<Integer, TsUserInfo> map = new HashMap<Integer, TsUserInfo>();
						for (TsUserInfo user : list) {
							String officesIds = user.getOfficeId() == null? null:user.getOfficeId().toString();
							if(StringUtils.isBlank(officesIds))	{
								officesIds = user.getOfficeIds().substring(1,user.getOfficeIds().length()-1);
							}
							List<TsUserInfo> temList = ActivitiRuleUtil.findUsersOfOfficeJz(officesIds, em);
							if (null != temList && temList.size() > 0) {
								for (TsUserInfo u : temList) {
									map.put(u.getRid(), u);
								}
							}
						}

						if (null != map && map.size() > 0) {
							Collection<TsUserInfo> coll = map.values();
							if (null != coll && coll.size() > 0) {
								for (TsUserInfo user : coll) {
									userList.add(user);
								}
							}
						}
					}
				}

			}

		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException("查询条件clone失败！");
		}
		return userList;
	}

	@Override
	public Map<String, String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em) {
		Map<String, String> map = new HashMap<String, String>();
		map.put("【与前一步处理者相同部门的人】", "【与前一步处理者相同部门的人】_1_1011");
		return map;
	}

	@Override
	public String desciption() {
		return "与前一步处理者相同部门的人";
	}

}
