package com.chis.activiti.service.rule.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.activiti.utils.ActivitiRuleUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 和前一步处理者相同
 * 规则编码：1005
 * 
 * ResultCondition必要条件：上一节点ID
 * DirectoryCondition必要条件：无
 * 
 * <AUTHOR>
 * @createDate 2014年9月2日
 */
@Component(value="com.chis.activiti.service.rule.impl.ActivitiRuleLastStepServiceImpl")
@Transactional(readOnly = true)
public class ActivitiRuleLastStepServiceImpl implements IActivitiRuleService{

	@Override
	public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em) {
		List<TsUserInfo> userList = new ArrayList<TsUserInfo>();
		
		try {
			if(null != condition && StringUtils.isNotBlank(condition.getNodeId())) {
				//先查询出上一个节点的ID
				String lastNodeId = ActivitiRuleUtil.findLastNodeId(condition.getNodeId(), em);
				
				if(StringUtils.isNotBlank(lastNodeId)) {
					//将查询条件对象克隆，重新设置当前节点ID
					ResultCondition conClone = (ResultCondition) condition.clone();
					conClone.setNodeId(lastNodeId);
					
					userList = ActivitiRuleUtil.findUsersOfNode(lastNodeId, conClone, em);
				}

			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException("查询条件clone失败！");
		}

		return userList;
	}

	@Override
	public Map<String, String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em) {
		Map<String, String> map = new HashMap<String, String>();
		map.put("【和前一步处理者相同】", "【和前一步处理者相同】_1_1005");
		return map;
	}

	@Override
	public String desciption() {
		return "和前一步处理者相同";
	}

}
