package com.chis.activiti.service.rule.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.activiti.utils.ActivitiRuleUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 某步处理者的分管领导,只能选择该节点之前的步骤
 * 
 * 规则编码：1017
 * ResultCondition必要条件：选中的ids（选中的节点ID）
 * DirectoryCondition必要条件：当前节点ID(TD_FLOW_NODE的ID)
 * 
 * 这里的流程定义ID是TD_FLOW_DEF的ID
 * <AUTHOR>
 * @createDate 2015年4月20日
 */
@Component(value="com.chis.activiti.service.rule.impl.ActivitiRuleSomeStepBigLeaderServiceImpl")
@Transactional(readOnly = true)
public class ActivitiRuleSomeStepBigLeaderServiceImpl implements IActivitiRuleService{

	@Override
	public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em) {
		List<TsUserInfo> userList = new ArrayList<TsUserInfo>();
		if(null != condition && StringUtils.isNotBlank(condition.getSelectIds())) {
			//获取某些步的处理者
			List<TsUserInfo> list = ActivitiRuleUtil.findUsersOfNode(condition.getSelectIds(), condition, em);
			
			//再获取他们的分管领导
			if(null != list && list.size() > 0) {
				Map<Integer, TsUserInfo> map = new HashMap<Integer, TsUserInfo>();
				for(TsUserInfo user : list) {
					TsUserInfo leader = ActivitiRuleUtil.findBigLeaderOfUser(user.getRid(), em);
					if(null != leader) {
						map.put(leader.getRid(), leader);
					}
				}
				
				if(null != map && map.size() > 0) {
					Collection<TsUserInfo> coll = map.values();
					if(null != coll && coll.size() > 0) {
						for(TsUserInfo user: coll) {
							userList.add(user);
						}
					}
				}
			}
		}
		return userList;
	}

	@Override
	public Map<String, String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em) {
		Map<String,String> map = new LinkedHashMap<String, String>();
		if(null != condition.getNodeId()) {
			List<TdFlowNode> list = ActivitiRuleUtil.findNodesBefore(condition.getNodeId(), em);
			if(null != list && list.size() > 0) {
				for(TdFlowNode node:list) {
					map.put(node.getNodeName()+"【某步处理者的分管领导】", node.getNodeName()+"【某步处理者的分管领导】_"+node.getRid()+"_1017");
				}
			}
		}
		return map;
	}

	@Override
	public String desciption() {
		return "某步处理者的分管领导";
	}
	
}
