package com.chis.activiti.service.rule.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.activiti.utils.ActivitiRuleUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 分管领导（流程中的每一个参与者都归他管，实质上也是发起者的分管领导）
 * 规则编码：1013
 * ResultCondition必要条件：流程实例ID
 * DirectoryCondition必要条件：无
 * <AUTHOR>
 */
@Component(value="com.chis.activiti.service.rule.impl.ActivitiRuleBigLeaderServiceImpl")
@Transactional(readOnly = true)
public class ActivitiRuleBigLeaderServiceImpl implements IActivitiRuleService{
	
	@Override
	public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em) {
		List<TsUserInfo> list = new ArrayList<TsUserInfo>();
		if(null != condition && StringUtils.isNotBlank(condition.getProcessInstanceId())) {
			TsUserInfo startUser = ActivitiRuleUtil.findStartUserJz(condition.getProcessInstanceId(), em);
			if(null != startUser) {
				TsUserInfo leader = ActivitiRuleUtil.findBigLeaderOfUser(startUser.getRid(), em);
				if(null != leader) {
					list.add(leader);
				}
			}
		}
		return list;
	}

	@Override
	public Map<String, String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em) {
		Map<String, String> map = new HashMap<String, String>();
		map.put("【分管领导】", "【分管领导】_1_1013");
		return map;
	}

	@Override
	public String desciption() {
		return "分管领导";
	}

}
