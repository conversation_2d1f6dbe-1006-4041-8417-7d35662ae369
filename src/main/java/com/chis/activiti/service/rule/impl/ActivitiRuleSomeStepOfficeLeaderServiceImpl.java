package com.chis.activiti.service.rule.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.activiti.utils.ActivitiRuleUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 与某步处理者相同部门的领导
 * 
 * 规则编码：1019
 * ResultCondition必要条件：选中的ids（选中的节点ID）
 * DirectoryCondition必要条件：当前节点ID(TD_FLOW_NODE的ID)
 * 
 * <AUTHOR>
 * @createDate 2014年9月3日下午1:52:42
 */
@Component(value="com.chis.activiti.service.rule.impl.ActivitiRuleSomeStepOfficeLeaderServiceImpl")
@Transactional(readOnly = true)
public class ActivitiRuleSomeStepOfficeLeaderServiceImpl implements IActivitiRuleService{

	@Override
	public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em) {
		List<TsUserInfo> userList = new ArrayList<TsUserInfo>();
		if(null != condition && StringUtils.isNotBlank(condition.getSelectIds())) {
			//获取某些步的处理者
			List<TsUserInfo> list = ActivitiRuleUtil.findUsersOfNode(condition.getSelectIds(), condition, em);
			
			//再获取他们的同科室的领导
			if(null != list && list.size() > 0) {
				Map<Integer, TsUserInfo> map = new HashMap<Integer, TsUserInfo>();
				for(TsUserInfo user : list) {
					String officesIds = user.getOfficeId() == null? null:user.getOfficeId().toString();
					if(StringUtils.isBlank(officesIds))	{
						officesIds = user.getOfficeIds().substring(1,user.getOfficeIds().length()-1);
					}
					List<TsUserInfo> temList = ActivitiRuleUtil.findUsersOfOfficeLeaderJz(officesIds, em);
					if(null != temList && temList.size() > 0) {
						for(TsUserInfo u : temList) {
							map.put(u.getRid(), u);
						}
					}
				}
				
				if(null != map && map.size() > 0) {
					Collection<TsUserInfo> coll = map.values();
					if(null != coll && coll.size() > 0) {
						for(TsUserInfo user: coll) {
							userList.add(user);
						}
					}
				}
			}
		}
		return userList;
	}

	@Override
	public Map<String, String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em) {
		Map<String,String> map = new LinkedHashMap<String, String>();
		if(null != condition.getNodeId()) {
			List<TdFlowNode> list = ActivitiRuleUtil.findNodesBefore(condition.getNodeId(), em);
			if(null != list && list.size() > 0) {
				for(TdFlowNode node:list) {
					map.put(node.getNodeName()+"【与某步处理者所在部门的领导】", node.getNodeName()+"【与某步处理者所在部门的领导】_"+node.getRid()+"_1019");
				}
			}
		}
		return map;
	}

	@Override
	public String desciption() {
		return "与某步处理者所在部门的领导";
	}

}
