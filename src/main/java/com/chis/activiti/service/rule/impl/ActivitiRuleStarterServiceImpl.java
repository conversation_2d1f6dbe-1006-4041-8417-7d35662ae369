package com.chis.activiti.service.rule.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.activiti.utils.ActivitiRuleUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;
import com.google.common.collect.Lists;

/**
 * 流程发起者
 * 规则编码：1003
 * ResultCondition必要条件：流程实例ID
 * DirectoryCondition必要条件：无
 *
 * <AUTHOR>
 * @createDate 2014年9月3日上午8:43:08
 */
@Component(value="com.chis.activiti.service.rule.impl.ActivitiRuleStarterServiceImpl")
@Transactional(readOnly = true)
public class ActivitiRuleStarterServiceImpl implements IActivitiRuleService{

	@Override
	public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em) {
		List<TsUserInfo> rtnList = Lists.newArrayList();
		if(StringUtils.isNotBlank(condition.getProcessInstanceId())) {
			TsUserInfo starter = ActivitiRuleUtil.findStartUserJz(condition.getProcessInstanceId(), em);
			if(null != starter) {
				rtnList.add(starter);
			}
		}
		return rtnList;
	}

	@Override
	public Map<String, String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em) {
		Map<String, String> map = new HashMap<String, String>();
		map.put("【流程发起者】", "【流程发起者】_1_1003");
		return map;
	}

	@Override
	public String desciption() {
		return "流程发起者";
	}

}
