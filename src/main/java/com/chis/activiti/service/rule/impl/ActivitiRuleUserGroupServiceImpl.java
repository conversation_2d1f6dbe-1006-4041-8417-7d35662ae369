package com.chis.activiti.service.rule.impl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 按用户组分配 规则编码：1012
 * 
 * ResultCondition必要条件：选中的组 DirectoryCondition必要条件：无
 * 
 * <AUTHOR>
 */
@Component(value="com.chis.activiti.service.rule.impl.ActivitiRuleUserGroupServiceImpl")
@Transactional(readOnly = true)
public class ActivitiRuleUserGroupServiceImpl implements IActivitiRuleService {

	@Override
	public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em) {
		List<TsUserInfo> userList = new ArrayList<TsUserInfo>();

		if (null != condition && StringUtils.isNotBlank(condition.getSelectIds())) {
			StringBuilder sb = new StringBuilder();
			sb.append("SELECT T1.RID AS USERID, T1.USERNAME, ','||LISTAGG(C.OFFICEID,',') WITHIN GROUP(ORDER BY C.OFFICEID)||',' AS OFFICEIDS ,");
			sb.append(" LISTAGG(C.OFFICENAME,',') WITHIN GROUP(ORDER BY DECODE(C.ZKS, 1, -1, C.NUM)) AS OFFICENAMES");
			sb.append(", T5.RID AS UNITID, T5.UNIT_SIMPNAME FROM TS_USER_INFO T1");
			sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
			sb.append("  WHERE B.IF_REVEAL = '1' UNION ALL SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, ");
			sb.append(" 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
			sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID WHERE C.IF_REVEAL = '1' ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
			sb.append(" INNER JOIN TS_UNIT T5 ON T1.UNIT_RID = T5.RID ");
			sb.append(" INNER JOIN TS_USER_GROUP T4 ON T4.USER_INFO_ID = T1.RID");
			sb.append(" WHERE T1.IF_REVEAL = '1'");
			sb.append(" AND T4.GROUP_ID IN (").append(condition.getSelectIds()).append(") ");
			sb.append(" GROUP BY T1.RID, T1.USERNAME ,C.EMPNUM ,T5.RID,T5.UNIT_SIMPNAME,T5.UNIT_CODE");
			sb.append(" ORDER BY T5.UNIT_CODE, SUM(DECODE(C.ZKS, 1, C.NUM, NULL)) ,C.EMPNUM ");
			

			List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
			if (null != list && list.size() > 0) {
				for (Object[] o : list) {
					TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
					t.setUsername(o[1] == null ? "" : o[1].toString());
					TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
					unit.setUnitSimpname(o[5].toString());

					t.setTsUnit(unit);
					t.setOfficeIds(String.valueOf(o[2]));
					t.setOfficeNames(String.valueOf(o[3]));
					userList.add(t);
				}
			}
		}

		return userList;
	}

	@Override
	public Map<String, String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em) {
		Map<String, String> map = new LinkedHashMap<String, String>();
		StringBuilder sb = new StringBuilder();
		sb.append("  SELECT T.UNIT_RID FROM TS_USER_INFO T WHERE T.RID = '");
		sb.append(condition.getUserId()).append("' ");
		List<Object> list0 = em.createNativeQuery(sb.toString()).getResultList();
		if (null != list0 && list0.size() > 0) {
			Object obj = list0.get(0);
			
			sb = new StringBuilder();
			sb.append(" SELECT T1.Group_Name, T1.RID ");
			sb.append(" FROM TS_GROUP T1 ");
			sb.append(" WHERE T1.IF_REVEAL = '1' ");
			sb.append(" AND T1.UNIT_RID = '").append(obj).append("' ");
			sb.append(" ORDER BY T1.XH ");
			List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
			if (null != list && list.size() > 0) {
				for (Object[] o : list) {
					map.put(o[0].toString() + "【按用户组分配】", o[0].toString() + "【按用户组分配】_" + o[1] + "_1012");
				}
			}
		}
		
		return map;
	}

	@Override
	public String desciption() {
		return "按用户组分配";
	}

}
