package com.chis.activiti.service.rule.impl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.CodeType;

/**
 * 返回的对象:TsUserInfo
 * rid-主键
 * username-用户名
 * tsOffice-所在科室  
 * 		rid-科室ID
 * 		officename-科室名称
 * tsUnit-所在单位
 * 		rid-单位ID
 * 		unitSimpname-单位简称
 * 
 * 按职务规则
 * 规则编码：1002
 * ResultCondition必要条件：已选的职务IDS
 * DirectoryCondition必要条件：无
 * 
 * 2014-9-2 david  接口重新设计，入参封装为javabean 
 * 
 * <AUTHOR>
 */
@Component(value="com.chis.activiti.service.rule.impl.ActivitiRuleDutyServiceImpl")
@Transactional(readOnly = true)
public class ActivitiRuleDutyServiceImpl implements IActivitiRuleService{

	@Override
	public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em) {
		List<TsUserInfo> userList = new ArrayList<TsUserInfo>();
		
		if(StringUtils.isNotBlank(condition.getSelectIds())) {
			StringBuilder sb = new StringBuilder();

			sb.append(" SELECT T1.RID AS USERID,T1.USERNAME,");
			sb.append(" ','||LISTAGG(C.OFFICEID,',') WITHIN GROUP(ORDER BY C.OFFICEID)||',' AS OFFICEIDS ,");
			sb.append(" LISTAGG(C.OFFICENAME,',') WITHIN GROUP(ORDER BY DECODE(C.ZKS, 1, -1, C.NUM)) AS OFFICENAMES,");
			sb.append(" T5.RID AS UNITID,T5.UNIT_SIMPNAME FROM TS_USER_INFO T1");
			sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS,B.UNIT_RID,A.DUTY FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
			sb.append(" WHERE B.IF_REVEAL = '1' UNION ALL SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, ");
			sb.append(" 0 AS ZKS,C.UNIT_RID,B.DUTY_ID AS DUTY  FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
			sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID WHERE C.IF_REVEAL = '1'  ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
			sb.append(" INNER JOIN TS_UNIT T5 ON C.UNIT_RID = T5.RID WHERE T1.IF_REVEAL = '1'");
			sb.append(" AND C.DUTY IN (").append(condition.getSelectIds()).append(")");
			sb.append(" GROUP BY T1.RID, T1.USERNAME ,C.EMPNUM ,T5.RID,T5.UNIT_SIMPNAME,T5.UNIT_CODE");
			sb.append(" ORDER BY T5.UNIT_CODE, SUM(DECODE(C.ZKS, 1, C.NUM, NULL)) ,C.EMPNUM ");
			 List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
			 if(null != list && list.size() > 0) {
				 for(Object[] o:list) {
					 TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
					 t.setUsername(o[1]==null?"":o[1].toString());
					 
					 TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
					 unit.setUnitSimpname(o[5].toString());
					 
					 t.setTsUnit(unit);
					 t.setOfficeIds(String.valueOf(o[2]));
					 t.setOfficeNames(String.valueOf(o[3]));
					 userList.add(t);
				 }
			 }
		}
		return userList;
	}

	@Override
	public Map<String,String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em) {
		Map<String,String> map = new LinkedHashMap<String, String>();
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T1.CODE_NAME, T1.RID ");
		sb.append(" FROM TS_SIMPLE_CODE T1 ");
		sb.append(" INNER JOIN TS_CODE_TYPE T2 ON T1.CODE_TYPE_ID = T2.RID ");
		sb.append(" WHERE T2.CODE_TYPE_NAME = '").append(CodeType.ZHW).append("' ");
		sb.append(" ORDER BY T1.CODE_NO ");
		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if(null != list && list.size() > 0) {
			for(Object[] o : list) {
				map.put(o[0].toString()+"【按职务分配】", o[0].toString()+"【按职务分配】_"+o[1]+"_1002");
			}
		}
		return map;
	}

	@Override
	public String desciption() {
		return "按职务分配";
	}

}
