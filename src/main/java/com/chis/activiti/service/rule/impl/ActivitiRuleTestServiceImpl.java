package com.chis.activiti.service.rule.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 测试规则
 * 规则编码：1000
 * ResultCondition必要条件：无
 * DirectoryCondition必要条件：无
 * <AUTHOR>
 */
@Component(value="com.chis.activiti.service.rule.impl.ActivitiRuleTestServiceImpl")
@Transactional(readOnly = true)
public class ActivitiRuleTestServiceImpl implements IActivitiRuleService{
	
	@Override
	public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em) {
		List<TsUserInfo> list = new ArrayList<TsUserInfo>();
		System.err.println("json:" + condition.getJson());
		try {
//		    JSONObject jsonObject = JSONObject.fromObject(condition.getJson()); 
//		    Object bean = JSONObject.toBean(jsonObject);
//			System.out.println(PropertyUtils.getProperty(bean, "david"));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}

	@Override
	public Map<String, String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em) {
		Map<String, String> map = new HashMap<String, String>();
		map.put("【测试规则】", "【测试规则】_1_1000");
		return map;
	}

	@Override
	public String desciption() {
		return "测试规则";
	}

}
