package com.chis.activiti.service.rule.impl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.activiti.utils.ActivitiRuleUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 和某步处理者相同,只能选择该节点之前的步骤
 * 
 * 规则编码：1004
 * ResultCondition必要条件：选中的ids（选中的节点ID）
 * DirectoryCondition必要条件：当前节点ID(TD_FLOW_NODE的ID)
 * 
 * <AUTHOR>
 * @createDate 2014年9月2日
 */
@Component(value="com.chis.activiti.service.rule.impl.ActivitiRuleSomeStepServiceImpl")
@Transactional(readOnly = true)
public class ActivitiRuleSomeStepServiceImpl implements IActivitiRuleService{

	@Override
	public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em) {
		List<TsUserInfo> userList = new ArrayList<TsUserInfo>();
		if(null != condition && StringUtils.isNotBlank(condition.getSelectIds())) {
			userList = ActivitiRuleUtil.findUsersOfNode(condition.getSelectIds(), condition, em);
		}
		return userList;
	}

	@Override
	public Map<String, String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em) {
		Map<String,String> map = new LinkedHashMap<String, String>();
		if(null != condition.getNodeId()) {
			List<TdFlowNode> list = ActivitiRuleUtil.findNodesBefore(condition.getNodeId(), em);
			if(null != list && list.size() > 0) {
				for(TdFlowNode node:list) {
					map.put(node.getNodeName()+"【和某步处理者相同】", node.getNodeName()+"【和某步处理者相同】_"+node.getRid()+"_1004");
				}
			}
		}
		return map;
	}

	@Override
	public String desciption() {
		return "和某步处理者相同";
	}
	
}
