package com.chis.activiti.service.rule.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.rule.IActivitiRuleService;
import com.chis.activiti.utils.ActivitiRuleUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 流程发起者所在部门的领导
 * 规则编码：1018
 * 
 * ResultCondition必要条件：流程实例ID
 * DirectoryCondition必要条件：无
 * 
 * <AUTHOR>
 * @createDate 2014年9月3日上午10:39:06
 */
@Component(value="com.chis.activiti.service.rule.impl.ActivitiRuleStarterOfficeLeaderServiceImpl")
@Transactional(readOnly = true)
public class ActivitiRuleStarterOfficeLeaderServiceImpl implements IActivitiRuleService{

	@Override
	public List<TsUserInfo> findMan(ResultCondition condition, EntityManager em) {
		List<TsUserInfo> list = new ArrayList<TsUserInfo>();
		if(null != condition && StringUtils.isNotBlank(condition.getProcessInstanceId())) {
			TsUserInfo startUser = ActivitiRuleUtil.findStartUserJz(condition.getProcessInstanceId(), em);
			
			if(null != startUser) {
				String officesIds = startUser.getOfficeId() == null? null:startUser.getOfficeId().toString();
				if(StringUtils.isBlank(officesIds))	{
					officesIds = startUser.getOfficeIds().substring(1,startUser.getOfficeIds().length()-1);
				}
				list = ActivitiRuleUtil.findUsersOfOfficeLeaderJz(officesIds, em);
			}
		}
		return list;
	}

	@Override
	public Map<String, String> selectDirectoryDatas(DirectoryCondition condition, EntityManager em) {
		Map<String, String> map = new HashMap<String, String>();
		map.put("【流程发起者所在部门的领导】", "【流程发起者所在部门的领导】_1_1018");
		return map;
	}

	@Override
	public String desciption() {
		return "流程发起者所在部门的领导";
	}

}
