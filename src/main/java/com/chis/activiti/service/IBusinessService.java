package com.chis.activiti.service;

import java.util.Map;

/**
 * 业务表单接口
 * <AUTHOR>
 */
public interface IBusinessService {
	
	/**
	 *  业务表单页面初始化
	 *  @param obj 参数
	 */
	public void init(Object obj);
	
	/**
	 *  业务表单流转的过程中，需要向流程中添加的变量
	 *  @return 返回业务表单中的流程变量
	 */
	public Map<String, Object> addVariables();
	
	/**
	 *  业务表单保存，返回NULL表示业务表单保存失败
	 *  @return 返回业务表单主键
	 */
	public String saveAction();
	
	/**
	 * 保存之后进行的操作
	 */
	public void afterSaveAction();
	
	/**
	 * 业务表单提交操作，返回NULL表示业务表单提交失败
	 * @return 返回业务表单主键
	 */
	public String submitAction();
	
	/**
	 * 提交成功之后进行的操作
	 */
	public void afterSubmitAction();
	
	/**
	 * 业务表单退回操作，返回NULL表示业务表单退回失败
	 */
	public String backAction();
	
	/**
	 * 退回之后进行的操作
	 */
	public void afterBackAction();
	
	/**
	 * 删除流程以及业务数据
	 * @return 返回NULL表示成功，否则返回错误信息
	 */
	public String deleteAction();

	/**
	 * 根据提供的用户IDS，提供移动电话
	 * @param userIds 用户IDS
	 * @return KEY-用户ID VALUE-移动电话
	 */
	public Map<Integer, String> supplyPhoneNum(String userIds); 
	
	/**
	 * 终止流程以及业务数据
	 * @return 返回NULL表示成功，否则返回错误信息
	 */
	public String terminateAction();
	
}



























