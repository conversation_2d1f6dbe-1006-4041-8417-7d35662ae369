package com.chis.modules.heth.base.plugin;

import com.chis.common.utils.JaxbMapper;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.*;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.init.PluginInit;
import com.chis.modules.system.logic.SQLSentence;
import com.chis.modules.system.logic.SQLSentences;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.util.*;

@Component(value = "PluginObjInit_HETH_BASE_101")
public class PluginObjInit extends PluginInit {

    public static String getManyChar(String c, int num) {
        StringBuilder str = new StringBuilder();
        for (int i = 0; i < num; i++) {
            str.append(c);
        }
        return str.toString();
    }

    @Override
    public void run() {
        this.initDataStructureByXml();
        super.run();
    }

    @Override
    public void initDataStructureByXml() {
        for (SystemType systemType : SystemType.values()) {
            initDataStructureByXml(systemType);
        }
    }

    public void initDataStructureByXml(SystemType systemType) {
        Integer systemTypeNo = ObjectUtil.convert(Integer.class, systemType.getTypeNo());
        if (systemTypeNo == null) {
            return;
        }
        //解析XML
        String fileLocal = "/db/new/SQL_" + systemTypeNo + ".xml";
        fileLocal = org.springframework.util.StringUtils.cleanPath(fileLocal);
        if (!new ClassPathResource(fileLocal).exists()) {
            return;
        }
        SQLSentences sqlSentences = JaxbMapper.fileToObject(fileLocal, SQLSentences.class);
        if (null == sqlSentences || CollectionUtils.isEmpty(sqlSentences.getSqlList())) {
            return;
        }
        List<SQLSentence> sqlList = sqlSentences.getSqlList();
        int sqlListSize = sqlList.size();
        //查询当前升级版本
        TsSystemUpdate tsu = this.pluginUpdateService.findSystemUpdateByXml(systemType);
        if (null == tsu) {
            tsu = new TsSystemUpdate();
            tsu.setParamType(systemTypeNo);
        }
        tsu.setCurVersion(ObjectUtil.convert(Integer.class, tsu.getCurVersion(), 1));
        if (tsu.getCurVersion() == 0) {
            tsu.setCurVersion(1);
        }
        this.showSqlWaining(sqlList, fileLocal);
        //升级版本不高于XML版本无需升级
        if (sqlListSize <= tsu.getCurVersion()) {
            return;
        }
        System.out.print("数据库升级中: SQL_" + systemTypeNo + ".xml");
        for (int i = tsu.getCurVersion() - 1; i < sqlListSize; i++) {
            SQLSentence sqlObj = sqlList.get(i);
            if (null == sqlObj) {
                continue;
            }
            String sql = sqlObj.getSql();
            int ver = sqlObj.getVer();
            // 如果sql语句为空时，则不执行
            if (StringUtils.isNotBlank(sql)) {
                //System.out.println("RunSQL(" + ver + "): " + sql);
                System.out.print(getManyChar("\b", 100) + "数据库升级中: SQL_" + systemTypeNo + ".xml, 当前ver: " + ver);
                try {
                    this.pluginUpdateService.executeUpdate(sql, ver, true);
                } catch (Exception e) {
                    tsu.setCurVersion(i);
                    tsu.setUpdateTime(new Date());
                    tsu = this.pluginUpdateService.updateSystemUpdate(tsu);
                    System.out.print(getManyChar("\b", 100) + "SQL_" + systemTypeNo + ".xml ver: " + ver + " 升级失败");
                    System.out.println();
                    return;
                }
            }
            tsu.setCurVersion(i + 1);
            tsu.setUpdateTime(new Date());
            tsu = this.pluginUpdateService.updateSystemUpdate(tsu);
        }
        System.out.print(getManyChar("\b", 100) + "SQL_" + systemTypeNo + ".xml 升级完成");
        System.out.println();
    }

    @Override
    public List<String> buildDataStructurePlugin() {
        return new ArrayList<>();
    }

    @Override
    public Set<TsMenu> buildMenuPlugin() {
        return new HashSet<>();
    }

    @Override
    public Set<TsCodeRule> buildCodeRulePlugin() {
        return new HashSet<>();
    }

    @Override
    public Set<TsSystemParam> buildSystemParamPlugin() {
        return new HashSet<>();
    }

    @Override
    public Set<TsSimpleCode> buildCodePlugin() {
        return new HashSet<>();
    }

    @Override
    public Set<TsCodeType> buildCodeTypePlugin() {
        return new HashSet<>();
    }

    @Override
    public SystemType buildSystemType() {
        return SystemType.COMM_BASE;
    }

    /**
     * <p>方法描述：输出重复语句警告 </p>
     *
     * @MethodAuthor： pw 2022/8/16
     **/
    private void showSqlWaining(List<SQLSentence> sqlList, String fileLocal) {
        //key 1、列 tableName +#+ columnName 2、键 tableName+&+CONSTRAINT_NAME 3、tableName创建表
        //4、SEQUENCE_NAME+@ 5、INDEX_NAME+* 其他情况酌情添加 但注意分隔符不能重复
        Map<String, List<Integer>> sqlMap = new HashMap<>();
        Map<String, List<Integer>> sqlModifyMap = new HashMap<>();
        Set<Integer> verSet = new HashSet<>();
        StringBuffer sb = new StringBuffer();
        int listsize = sqlList.size();
        //检查是否有重复
        for (int i = 0; i < listsize; i++) {
            SQLSentence sqlObj = sqlList.get(i);
            String sql = null;
            int ver = 0;
            if (null != sqlObj) {
                sql = sqlObj.getSql();
                ver = sqlObj.getVer();
            }
            if (StringUtils.isBlank(sql)) {
                continue;
            }
            if (verSet.contains(ver)) {
                sb.append(" ver：").append(ver).append("重复；");
            } else {
                verSet.add(ver);
            }
            String sqlKey = this.generateKey(sql);
            if (StringUtils.isBlank(sqlKey)) {
                sb.append(" ver：").append(ver).append("类型未匹配到；");
            } else {
                //MODIFY或者DROP时候 注意两边各一个空格
                boolean ifModify = sql.toUpperCase().contains(" MODIFY ") || sql.toUpperCase().contains(" DROP ");
                List<Integer> tmpList = ifModify ? sqlModifyMap.get(sqlKey) : sqlMap.get(sqlKey);
                if (null == tmpList) {
                    tmpList = new ArrayList<>();
                }
                tmpList.add(ver);
                if (ifModify) {
                    sqlModifyMap.put(sqlKey, tmpList);
                } else {
                    sqlMap.put(sqlKey, tmpList);
                }
            }
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(sqlMap)) {
            for (Map.Entry<String, List<Integer>> mapEntity : sqlMap.entrySet()) {
                List<Integer> tmpList = mapEntity.getValue();
                if (null != tmpList && tmpList.size() > 1) {
                    sb.append(" ver:").append(StringUtils.list2string(tmpList, ",")).append("语句重复；");
                }
            }
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(sqlModifyMap)) {
            for (Map.Entry<String, List<Integer>> mapEntity : sqlModifyMap.entrySet()) {
                List<Integer> tmpList = mapEntity.getValue();
                if (null != tmpList && tmpList.size() > 1) {
                    sb.append(" ver:").append(StringUtils.list2string(tmpList, ",")).append("语句重复；");
                }
            }
        }
        if (sb.length() > 0) {
            System.out.print("升级语句可能重复异常：");
            System.out.println(fileLocal + sb);
        }
    }

    /**
     * <p>方法描述：用于简单判断语句是否重复 生成key </p>
     *
     * @MethodAuthor： pw 2022/8/16
     **/
    private String generateKey(String sql) {
        StringBuffer keyBuffer = new StringBuffer();
        String sequenceName = this.generateKeyHelper(sql, "SEQUENCE_NAME");
        //优先处理不依赖TABLE_NAME的
        if (StringUtils.isNotBlank(sequenceName)) {
            //注意 分隔符不可以重复 避免出现分隔符一@ 分隔符二@@的情况
            keyBuffer.append(sequenceName).append("@");
        } else {
            String indexName = this.generateKeyHelper(sql, "INDEX_NAME");
            if (StringUtils.isNotBlank(indexName)) {
                //避免出现只有INDEX_NAME的情况
                keyBuffer.append("*").append(indexName).append("*");
            }
            String tableName = this.generateKeyHelper(sql, "TABLE_NAME");
            if (StringUtils.isBlank(tableName)) {
                return keyBuffer.toString();
            }
            keyBuffer.append(tableName);
            String columnName = this.generateKeyHelper(sql, "COLUMN_NAME");
            if (StringUtils.isNotBlank(columnName)) {
                keyBuffer.append("#").append(columnName);
            }
            String constraintName = this.generateKeyHelper(sql, "CONSTRAINT_NAME");
            if (StringUtils.isNotBlank(constraintName)) {
                keyBuffer.append("&").append(constraintName);
            }


        }
        return keyBuffer.toString();
    }

    /**
     * <p>方法描述：For 方法generateKey sql中截取key的值 </p>
     *
     * @MethodAuthor： pw 2022/8/16
     **/
    private String generateKeyHelper(String sql, String key) {
        if (StringUtils.isBlank(sql)) {
            return null;
        }
        String tmpSql = sql.toUpperCase();
        if (tmpSql.indexOf(key) == -1) {
            return null;
        }
        int beginIndex = sql.indexOf("'", tmpSql.indexOf(key) + key.length()) + 1;
        int endIndex = sql.indexOf("'", beginIndex);
        return sql.substring(beginIndex, endIndex);
    }
}
