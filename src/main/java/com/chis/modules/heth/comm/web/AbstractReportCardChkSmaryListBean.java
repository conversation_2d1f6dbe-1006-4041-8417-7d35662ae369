package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TdZwHethChkSmaryComm;
import com.chis.modules.heth.comm.entity.TdZwHethChkSubComm;
import com.chis.modules.heth.comm.rptvo.BadRsnTypeVo;
import com.chis.modules.heth.comm.rptvo.PostVo;
import com.chis.modules.heth.comm.service.TdZwHethChkSmaryServiceImpl;
import com.chis.modules.heth.comm.utils.ZwChkSmaryCommUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <p>类描述：职业性有害因素监测卡详情页基类</p>
 *
 * @ClassAuthor: yzz
 * @date： 2021年10月26日
 **/
public  class AbstractReportCardChkSmaryListBean  extends FacesEditBean implements IProcessData {


    protected CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    protected TdZwHethChkSmaryServiceImpl chkSmaryService = SpringContextHolder.getBean(TdZwHethChkSmaryServiceImpl.class);
    
    protected TdZwHethChkSmaryComm chkSmary = new TdZwHethChkSmaryComm();

    /**记录id*/
    protected Integer rid;
    /**接害合并列*/
    protected Integer colInt;

    /**接害对象*/
    List<BadRsnTypeVo> badRsnTypeVoList ;

    /**是否0审核，1详情*/
    protected Integer ifIsCheckout;

    /**危害因素类型,码表5537*/
    protected List<TsSimpleCode> badrsnTypeList;
    /**岗状态码表5009*/
    protected List<TsSimpleCode> postList;
    public AbstractReportCardChkSmaryListBean(){
        /**码表初始化*/
        this.badrsnTypeList = commService.findSimpleCodesByTypeId("5537");
        String extends1 = "1,2,3";
        this.postList = commService.findSimpleCodesByTypeIdAndExtends1("5009",extends1);
    }

    /**
     * <p>方法描述：附件查看</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-26
     **/
    public void toAnnexView() {
        if (StringUtils.isBlank(chkSmary.getAnnexPath())) {
            JsfUtil.addErrorMessage("附件已经被删除，请重新上传！");
            return;
        }
        RequestContext.getCurrentInstance().execute(
                "window.open('" + FileUtils.previewFile(chkSmary.getAnnexPath(),
                        "") + "')");
    }

    @Override
    public void processData(List<?> list) {

    }

    @Override
    public void addInit() {

    }

    /**
     * <p>方法描述：详情调用</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-26
     **/
    @Override
    public void viewInit() {
        if (null==rid) {
            return;
        }
        chkSmary = chkSmaryService.searchChkSmary(rid);
        //接害情况封装
        packageBadRsnTypeVo();
        ZwChkSmaryCommUtils.initEntity(chkSmary);
    }

    /**
     *  <p>方法描述：接害情况封装</p>
     * @MethodAuthor hsj
     */
    public void packageBadRsnTypeVo() {
        if(null != chkSmary && !CollectionUtils.isEmpty(chkSmary.getChkSubList())){
            badRsnTypeVoList = new ArrayList<>();
            //根据getFkByTchRsnId分组
            Map<TsSimpleCode,List<TdZwHethChkSubComm>> tdZwHethChkSubCommMap = new HashMap<TsSimpleCode, List<TdZwHethChkSubComm>>();
            GroupUtil.listGroup2Map(chkSmary.getChkSubList(),tdZwHethChkSubCommMap,TdZwHethChkSubComm.class,"getFkByTchRsnId");
            if(!CollectionUtils.isEmpty(tdZwHethChkSubCommMap)) {
                //num 排序
                List<Map.Entry<TsSimpleCode,List<TdZwHethChkSubComm>>> list = new ArrayList<>(tdZwHethChkSubCommMap.entrySet());
                Collections.sort(list, new Comparator<Map.Entry<TsSimpleCode,List<TdZwHethChkSubComm>>>() {
                    @Override
                    public int compare(Map.Entry<TsSimpleCode,List<TdZwHethChkSubComm>> o1, Map.Entry<TsSimpleCode,List<TdZwHethChkSubComm>> o2) {
                        return o1.getKey().getNum().compareTo(o2.getKey().getNum());
                    }
                });
                for (Map.Entry<TsSimpleCode, List<TdZwHethChkSubComm>> entry : list) {
                    List<TdZwHethChkSubComm> tdZwHethChkSubComms = entry.getValue();
                    BadRsnTypeVo badRsnTypeVo = new BadRsnTypeVo();
                    List<PostVo> postVoList = new ArrayList<>();
                    if(!CollectionUtils.isEmpty(tdZwHethChkSubComms)){
                        for(TdZwHethChkSubComm t:tdZwHethChkSubComms){
                            badRsnTypeVo.setBadrsnType(t.getFkByTchRsnId());
                            if(null == t.getFkByOnguardStateid() ){
                                badRsnTypeVo.setTouchNum(t.getTouchNum());
                            }else {
                                PostVo postVo =new PostVo();
                                postVo.setPost(t.getFkByOnguardStateid());
                                postVo.setNeedChkNum(t.getNeedChkNum());
                                postVoList.add(postVo);
                            }
                        }
                    }
                    if(!CollectionUtils.isEmpty(postVoList)){
                        //根据num排序ONGUARD_STATEID
                        Collections.sort(postVoList, new Comparator<PostVo>() {
                            @Override
                            public int compare(PostVo o1, PostVo o2) {
                                if(o1.getPost().getNum()!=null&&o2.getPost().getNum()!=null){
                                    return o1.getPost().getNum().compareTo(o2.getPost().getNum());
                                }
                                return 0;
                            }
                        });
                    }
                    badRsnTypeVo.setPostVos(postVoList);
                    badRsnTypeVoList.add(badRsnTypeVo);
                }
            }
        }
    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    @Override
    public String[] buildHqls() {
        return new String[0];
    }

    public TdZwHethChkSmaryComm getChkSmary() {
        return chkSmary;
    }

    public void setChkSmary(TdZwHethChkSmaryComm chkSmary) {
        this.chkSmary = chkSmary;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Integer getColInt() {
        return colInt;
    }

    public void setColInt(Integer colInt) {
        this.colInt = colInt;
    }

    public List<BadRsnTypeVo> getBadRsnTypeVoList() {
        return badRsnTypeVoList;
    }

    public void setBadRsnTypeVoList(List<BadRsnTypeVo> badRsnTypeVoList) {
        this.badRsnTypeVoList = badRsnTypeVoList;
    }

    public Integer getIfIsCheckout() {
        return ifIsCheckout;
    }

    public void setIfIsCheckout(Integer ifIsCheckout) {
        this.ifIsCheckout = ifIsCheckout;
    }

    public List<TsSimpleCode> getBadrsnTypeList() {
        return badrsnTypeList;
    }

    public void setBadrsnTypeList(List<TsSimpleCode> badrsnTypeList) {
        this.badrsnTypeList = badrsnTypeList;
    }

    public List<TsSimpleCode> getPostList() {
        return postList;
    }

    public void setPostList(List<TsSimpleCode> postList) {
        this.postList = postList;
    }
}
