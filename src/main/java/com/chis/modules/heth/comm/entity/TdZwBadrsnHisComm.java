package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>修订内容：新增字段+起止日期20180419</p>
 * @ClassReviser qrr,2018年4月21日,TdZwBadrsnHis
 */
@Entity
@Table(name = "TD_ZW_BADRSN_HIS")
@SequenceGenerator(name = "TdZwBadrsnHis", sequenceName = "TD_ZW_BADRSN_HIS_SEQ", allocationSize = 1)
public class TdZwBadrsnHisComm implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwOccdiscaseComm tdZwOccdiscase;
    private TsSimpleCode tsSimpleCode;
    private String badrsns;
    private BigDecimal touchyears;
    private Date createDate;
    private Integer createManid;
    private String unitName;
    private String postName;
    private String workName;

    private String parentName;
    /*******************新增字段******************/
    //+起止日期20180419
    private String touchTime;
    public TdZwBadrsnHisComm() {
    }

    public TdZwBadrsnHisComm(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwBadrsnHis")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwOccdiscaseComm getTdZwOccdiscase() {
        return this.tdZwOccdiscase;
    }

    public void setTdZwOccdiscase(TdZwOccdiscaseComm tdZwOccdiscase) {
        this.tdZwOccdiscase = tdZwOccdiscase;
    }

    @ManyToOne
    @JoinColumn(name = "BADRSN_ID")
    public TsSimpleCode getTsSimpleCode() {
        return this.tsSimpleCode;
    }

    public void setTsSimpleCode(TsSimpleCode tsSimpleCode) {
        this.tsSimpleCode = tsSimpleCode;
    }

    @Column(name = "BADRSNS")
    public String getBadrsns() {
        return badrsns;
    }

    public void setBadrsns(String badrsns) {
        this.badrsns = badrsns;
    }

    @Column(name = "TOUCHYEARS")
    public BigDecimal getTouchyears() {
        return touchyears;
    }

    public void setTouchyears(BigDecimal touchyears) {
        this.touchyears = touchyears;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Column(name = "UNIT_NAME")
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Column(name = "POST_NAME")
    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    @Transient
    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    @Column(name = "WORK_NAME")
    public String getWorkName() {
        return workName;
    }

    public void setWorkName(String workName) {
        this.workName = workName;
    }
    @Column(name = "TOUCH_TIME")
    public String getTouchTime() {
        return touchTime;
    }

    public void setTouchTime(String touchTime) {
        this.touchTime = touchTime;
    }
}
