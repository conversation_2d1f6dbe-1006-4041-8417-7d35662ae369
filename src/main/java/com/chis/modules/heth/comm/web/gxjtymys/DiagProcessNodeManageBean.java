package com.chis.modules.heth.comm.web.gxjtymys;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.List;

@ManagedBean(name = "diagProcessNodeManageBean")
@ViewScoped
public class DiagProcessNodeManageBean {
    private List<Object[]> dataList;
    private List<Object[]> chemicalDataList;
    private List<Object[]> technicalDataList;
    private String perPageSize = "20,50,100";

    public DiagProcessNodeManageBean(){
        this.init();
        this.initChemicalDataList();
        this.initTechnicalDataList();
    }

    /**
     * <p>方法描述： 初始化数据 </p>
     * @MethodAuthor： pw 2023/4/17
     **/
    private void init(){
        this.dataList = new ArrayList<>();
        String type = "职业病诊断";
        int code = 1000;
        this.dataList.add(this.generateData(1, type, code,"提交申请"));
        this.dataList.add(this.generateData(2, type, code,"申请受理"));
        this.dataList.add(this.generateData(3, type, code,"全国查重"));
        this.dataList.add(this.generateData(4, type, code,"初审反馈"));
        this.dataList.add(this.generateData(5, type, code,"驳回"));
        this.dataList.add(this.generateData(6, type, code,"材料补充"));
        this.dataList.add(this.generateData(7, type, code,"诊断受理"));
        this.dataList.add(this.generateData(8, type, code,"缴费情况"));
        this.dataList.add(this.generateData(9, type, code,"书面通知用人单位"));
        this.dataList.add(this.generateData(10, type,code,"诊断"));
        this.dataList.add(this.generateData(11, type,code,"出具职业病诊断证明书"));
        type = "职业病鉴定";
        this.dataList.add(this.generateData(12, type, 1989,"鉴定申请"));
        this.dataList.add(this.generateData(13, type, 1989,"鉴定受理"));
    }

    /**
     * <p>方法描述： 化学品毒性鉴定报告查询数据初始化 </p>
     * @MethodAuthor： pw 2023/4/20
     **/
    private void initChemicalDataList(){
        this.chemicalDataList = new ArrayList<>();
        this.chemicalDataList.add(new Object[]{1,"西安市_新城区","测试机构","测试人员","职业性苯中毒","待审核"});
    }

    /**
     * <p>方法描述： 职业卫生技术服务统计数据初始化 </p>
     * @MethodAuthor： pw 2023/4/20
     **/
    private void initTechnicalDataList(){
        this.technicalDataList = new ArrayList<>();
        this.technicalDataList.add(new Object[]{"陕西省",900,225,225,225,225});
        this.technicalDataList.add(new Object[]{"宝鸡市",20,5,5,5,5});
        this.technicalDataList.add(new Object[]{"咸阳市",40,10,10,10,10});
        this.technicalDataList.add(new Object[]{"铜川市",60,15,15,15,15});
        this.technicalDataList.add(new Object[]{"渭南市",80,20,20,20,20});
        this.technicalDataList.add(new Object[]{"延安市",100,25,25,25,25});
        this.technicalDataList.add(new Object[]{"榆林市",120,30,30,30,30});
        this.technicalDataList.add(new Object[]{"汉中市",140,35,35,35,35});
        this.technicalDataList.add(new Object[]{"安康市",160,40,40,40,40});
        this.technicalDataList.add(new Object[]{"商洛市",180,45,45,45,45});
    }

    private Object[] generateData(int num, String type, Integer code, String name){
        Object[] objArr = new Object[4];
        objArr[0] = num;
        objArr[1] = type;
        objArr[2] = code+num;
        objArr[3] = name;
        return objArr;
    }

    public List<Object[]> getDataList() {
        return dataList;
    }

    public void setDataList(List<Object[]> dataList) {
        this.dataList = dataList;
    }

    public String getPerPageSize() {
        return perPageSize;
    }

    public void setPerPageSize(String perPageSize) {
        this.perPageSize = perPageSize;
    }

    public List<Object[]> getChemicalDataList() {
        return chemicalDataList;
    }

    public void setChemicalDataList(List<Object[]> chemicalDataList) {
        this.chemicalDataList = chemicalDataList;
    }

    public List<Object[]> getTechnicalDataList() {
        return technicalDataList;
    }

    public void setTechnicalDataList(List<Object[]> technicalDataList) {
        this.technicalDataList = technicalDataList;
    }
}
