package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 职业健康监护标准（GBZ188）版本计数表
 * 
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置
 * 
 * 
 * <AUTHOR>
 * @createDate 2014年9月1日 上午8:56:49
 * @LastModify LuXuekun
 * @ModifyDate 2014年9月1日 上午8:56:49
 */
@Entity
@Table(name = "TB_ZW_GBZ188VER")
@SequenceGenerator(name = "TbZwGbz188verSeq", sequenceName = "TB_ZW_GBZ188VER_SEQ", allocationSize = 1)
public class TbZwGbz188verComm implements java.io.Serializable {

	private static final long serialVersionUID = 782972906770048945L;
	private Integer rid;
	private String stdname;
	private Integer verNum;
	private Date createDate;
	private Integer createManid;

	public TbZwGbz188verComm() {
	}

	public TbZwGbz188verComm(Integer rid, String stdname, Integer verNum,
                             Date createDate, Integer createManid) {
		this.rid = rid;
		this.stdname = stdname;
		this.verNum = verNum;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	@Id
	@GeneratedValue(generator = "TbZwGbz188verSeq", strategy = GenerationType.SEQUENCE)
	@Column(name = "RID", unique = true )
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Column(name = "STDNAME" , length = 100)
	public String getStdname() {
		return this.stdname;
	}

	public void setStdname(String stdname) {
		this.stdname = stdname;
	}

	@Column(name = "VER_NUM" )
	public Integer getVerNum() {
		return this.verNum;
	}

	public void setVerNum(Integer verNum) {
		this.verNum = verNum;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" )
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" )
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

}