package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSONObject;
import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 *  <p>类描述：职业卫生技术服务信息报送卡查询</p>
 * @ClassAuthor hsj 2023-11-27 11:21
 */
@ManagedBean(name = "occHethCardFillInQueryBean")
@ViewScoped
public class OccHethCardFillInQueryBean extends AbstractOccHethCardBean {
    private static final long serialVersionUID = 1L;
    /**
     * 查询条件：报告单位名称
     */
    private String searchOrgName;
    /**
     * 查询条件：技术服务领域名称
     */
    private String selectServiceNames;
    /**
     * 查询条件：技术服务领域rids
     */
    private String selectServiceRids;

    /**导入权限*/
    private Boolean importJurisdiction;

    /**来源*/
    protected List<Integer> searchSourceList;

    /**
     * 删除接口地址
     */
    private String delUrl;
    /**
     * <p>方法描述：本省的zongGb </p>
     * pw 2023/12/2
     **/
    private String curZoneGb;
    /** 导入错误文件路径 */
    private String errFilePath;
    public OccHethCardFillInQueryBean() {
        this.init();
        this.searchAction();
    }

    private void init() {
        //报告单位地区
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if(null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.searchZoneGb = tsZone.getZoneGb();
        this.searchZoneName = tsZone.getZoneName();
        this.searchZoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb(), true, "", "");
        //状态
        this.searchStateList = new ArrayList<>();
        this.searchStateList.add(1);
        this.searchSourceList = new ArrayList<>();
        this.ifSupportCancel = Boolean.FALSE;
        Set<String> btnSet = Global.getBtnSet();
        this.importJurisdiction = CollectionUtils.isEmpty(btnSet) ? false : btnSet.contains("heth_comm_gs_zywsjsfwxxbskcx_imp");
        this.delUrl = PropertyUtils.getValue("delUrl");
        this.curZoneGb = StringUtils.isBlank(this.searchZoneGb) ? null : this.searchZoneGb.substring(0,2);
    }


    @Override
    public void searchAction() {
        super.searchAction();
    }

    @Override
    public String[] buildHqls() {
        StringBuilder baseSql = new StringBuilder();
        baseSql.append("SELECT T.RID, CASE WHEN Z.ZONE_TYPE > 2 THEN SUBSTR(Z.FULL_NAME, INSTR(Z.FULL_NAME, '_') + 1) ELSE Z.FULL_NAME END orgZoneName,T.ORG_NAME,");
        baseSql.append(" CASE WHEN Z1.ZONE_TYPE > 2 THEN SUBSTR(Z1.FULL_NAME, INSTR(Z1.FULL_NAME, '_') + 1) ELSE Z1.FULL_NAME END crptZoneName,T.CRPT_NAME, T.RPT_DATE, T.STATE, Z.ZONE_GB as orgZoneGB,Z1.ZONE_GB as crptZoneGB,nvl(T.SOURCE,0)  ");
        baseSql.append("FROM TD_ZW_OCCHETH_CARD T ");
        baseSql.append("LEFT JOIN TS_UNIT tu  ON T.FILL_UNIT_ID = tu.RID ");
        baseSql.append("LEFT JOIN TS_ZONE Z ON tu.ZONE_ID = Z.RID  ");
        baseSql.append("LEFT JOIN TS_ZONE Z1 ON T.ZONE_ID = Z1.RID ");
        baseSql.append("WHERE NVL(T.DEL_MARK, 0) = 0 ");

        // 报告单位地区
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            baseSql.append(" AND Z.ZONE_GB LIKE :zoneCode escape '\\\' ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneGb)) + "%");
        }
        // 报告单位名称
        if (StringUtils.isNotBlank(this.searchOrgName)) {
            baseSql.append(" AND T.ORG_NAME LIKE :searchOrgName escape '\\\' ");
            this.paramMap.put("searchOrgName", "%" + StringUtils.convertBFH(this.searchOrgName.trim()) + "%");
        }
        // 服务单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            baseSql.append(" AND T.CRPT_NAME LIKE :searchCrptName escape '\\\' ");
            this.paramMap.put("searchCrptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        //技术服务领域
        if (ObjectUtil.isNotEmpty(this.selectServiceRids)) {
            baseSql.append(" AND EXISTS (SELECT 1 FROM TD_ZW_OCCHETH_CARD_SERVICE S WHERE S.MAIN_ID = T.RID AND S.SERVICE_AREA_ID IN (:selectServiceRids))");
            this.paramMap.put("selectServiceRids",StringUtils.string2list(this.selectServiceRids,",") );
        }
        // 出具技术服务报告时间
        if (ObjectUtil.isNotEmpty(this.searchRptBeginDate)) {
            baseSql.append(" AND T.RPT_DATE >= TO_DATE(:searchRptBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchRptBeginDate", DateUtils.formatDate(this.searchRptBeginDate) + " 00:00:00");
        }
        if (ObjectUtil.isNotEmpty(this.searchRptEndDate)) {
            baseSql.append(" AND T.RPT_DATE <= TO_DATE(:searchRptEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchRptEndDate", DateUtils.formatDate(this.searchRptEndDate) + " 23:59:59");
        }
        // 状态
        if (ObjectUtil.isNotEmpty(this.searchStateList)) {
            baseSql.append(" AND T.STATE IN (:searchStateList)");
            this.paramMap.put("searchStateList", this.searchStateList);
        }
        // 来源
        if (ObjectUtil.isNotEmpty(this.searchSourceList)) {
            baseSql.append(" AND nvl(T.SOURCE,0) IN (:searchSourceList)");
            this.paramMap.put("searchSourceList", this.searchSourceList);
        }
        String sql1 = "SELECT * FROM (" + baseSql + ")AA ORDER BY AA.orgZoneGB ,AA.ORG_NAME ,AA.RPT_DATE DESC, AA.crptZoneGB, AA.CRPT_NAME, AA.RID DESC";
        String sql2 = "SELECT COUNT(1) FROM (" + baseSql + ")";
        return new String[]{sql1, sql2};
    }

    /**
     * <p>方法描述：导入模板下载 </p>
     * pw 2023/12/2
     **/
    public StreamedContent getTemplateFile() {
        String fileName = "职业卫生技术服务信息报送卡导入模板.xlsx";
        if (StringUtils.isBlank(fileName)) {
            return null;
        }
        InputStream stream = null;
        try {
            String moudleFilePath = "/resources/template/excel/" + fileName;
            stream = FacesContext.getCurrentInstance().getExternalContext().getResourceAsStream(moudleFilePath);
            return new DefaultStreamedContent(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", URLEncoder.encode(fileName, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("文件下载失败!");
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }
        return null;
    }

    /**
     * <p>方法描述：错误数据下载 </p>
     * pw 2023/12/2
     **/
    public StreamedContent getErrorImportFile() {
        String nameTip = "职业卫生技术服务信息报送卡";
        if (StringUtils.isBlank(this.errFilePath)) {
            return null;
        }
        InputStream stream;
        try {
            stream = new FileInputStream(JsfUtil.getAbsolutePath() + this.errFilePath);
            String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            String fileName = nameTip + "导入错误数据.xlsx";
            if (this.errFilePath.endsWith(".xls")) {
                contentType = "application/vnd.ms-excel";
                fileName = nameTip + "导入错误数据.xls";
            }
            return new DefaultStreamedContent(stream, contentType, URLEncoder.encode(fileName, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("下载失败!");
        }
        return null;
    }

    /**
     * <p>方法描述：调接口导入数据 </p>
     * pw 2023/12/2
     **/
    public void importDataAction(FileUploadEvent event) {
        // 删除历史错误文件
        if (ObjectUtil.isNotEmpty(this.errFilePath)) {
            File errorFile = new File(JsfUtil.getAbsolutePath() + this.errFilePath);
            if (errorFile.exists()) {
                errorFile.delete();
            }
        }
        this.errFilePath = null;
        String updateFormId = "tabView:mainForm:uploadFileDialog";
        if (event == null || event.getFile() == null) {
            RequestContext.getCurrentInstance().update(updateFormId);
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
            JsfUtil.addErrorMessage("文件上传失败！");
            return;
        }
        try {
            UploadedFile file = event.getFile();
            //格式验证
            String fileName = file.getFileName();// 文件名称
            String contentType = file.getContentType().toLowerCase();
            String errorMsg = FileUtils.veryFile(file.getInputstream(),contentType, fileName, "5");
            if (StringUtils.isNotBlank(errorMsg)) {
                JsfUtil.addErrorMessage(errorMsg);
                RequestContext.getCurrentInstance().update(updateFormId);
                RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').show()");
                return;
            }
            String urlStr = this.delUrl + "/easyExcel/zwBskInfoImport?zoneGb="+this.curZoneGb;
            String responseStr = HttpRequestUtil.httpRequestByRaw(urlStr, file.getInputstream());
            if (StringUtils.isNotBlank(responseStr)) {
                JSONObject responseJson = JSONObject.parseObject(responseStr);
                Boolean flag = ObjectUtil.convert(Boolean.class, responseJson.get("flag"), false);
                String msg = StringUtils.objectToString(responseJson.get("msg"));
                String errorFileName = StringUtils.objectToString(responseJson.get("errorFile"));
                if (flag) {
                    JsfUtil.addSuccessMessage(msg);
                } else {
                    JsfUtil.addErrorMessage(ObjectUtil.isEmpty(msg) ? "导入文件异常！" : msg);
                }
                if (ObjectUtil.isNotEmpty(errorFileName)) {
                    this.errFilePath = File.separator + "comm" + File.separator + "temp" + File.separator + errorFileName;
                } else {
                    this.errFilePath = null;
                }
            } else {
                JsfUtil.addErrorMessage("导入文件异常！");
            }
            this.searchAction();
            RequestContext.getCurrentInstance().update("tabView:mainForm");
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
        } catch (Exception e) {
            e.printStackTrace();
            RequestContext.getCurrentInstance().update(updateFormId);
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
            JsfUtil.addErrorMessage("导入文件异常！");
        }
    }


    public String getSearchOrgName() {
        return searchOrgName;
    }

    public void setSearchOrgName(String searchOrgName) {
        this.searchOrgName = searchOrgName;
    }

    public String getSelectServiceNames() {
        return selectServiceNames;
    }

    public void setSelectServiceNames(String selectServiceNames) {
        this.selectServiceNames = selectServiceNames;
    }

    public String getSelectServiceRids() {
        return selectServiceRids;
    }

    public void setSelectServiceRids(String selectServiceRids) {
        this.selectServiceRids = selectServiceRids;
    }

    public Boolean getImportJurisdiction() {
        return importJurisdiction;
    }

    public void setImportJurisdiction(Boolean importJurisdiction) {
        this.importJurisdiction = importJurisdiction;
    }

    public List<Integer> getSearchSourceList() {
        return searchSourceList;
    }

    public void setSearchSourceList(List<Integer> searchSourceList) {
        this.searchSourceList = searchSourceList;
    }

    public String getErrFilePath() {
        return errFilePath;
    }

    public void setErrFilePath(String errFilePath) {
        this.errFilePath = errFilePath;
    }
}