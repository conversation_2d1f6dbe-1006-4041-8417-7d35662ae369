package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.rptvo.SelectManyOrgVo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 职业健康检查机构 弹框 多选
 */
@ManagedBean(name = "selectManyOrgListBean")
@ViewScoped
public class SelectManyOrgListBean extends FacesBean {

    private static final long serialVersionUID = -738445454536625063L;

    /**
     * 名称 或 拼音码
     */
    private String searchName;
    /**
     * 查询列集合
     */
    private List<SelectManyOrgVo> displayList;
    /**
     * 所有集合
     */
    private List<SelectManyOrgVo> allList;
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
            .getBean(SystemModuleServiceImpl.class);
    /**
     * 查询条件-地区
     */
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;

    /**
     * 类型 <pre>1: 职业健康检查机构</pre><pre>2: 职业病诊断机构</pre><pre>3: 放射卫生技术服务机构</pre>
     */
    private String checkType;

    /**
     * 弹框标题名称
     */
    private String title;

    /**
     * 弹框单位查询条件和列表行的名称
     */
    private String searchUnitName;
    /**
     * 是否显示资质机构名称
     */
    private Boolean zzOrgName;

    /**是否隐藏被选中的记录*/
    private boolean ifHideSelected;

    /**
     * <p>方法描述：初始化数据</p>
     *
     * @MethodAuthor qrr, 2019年12月3日, SelectTjOrgListBean
     */
    public SelectManyOrgListBean() {
        //已选择的码表rid，以逗号隔开
        String selectIds = JsfUtil.getRequest().getParameter("selectIds");
        //已选择的码表rid，以逗号隔开
        String zoneCode = JsfUtil.getRequest().getParameter("searchZoneCode");
        //类型
        this.checkType = JsfUtil.getRequest().getParameter("checkType");
        //弹框标题名称
        this.title = JsfUtil.getRequest().getParameter("title");
        //弹框单位查询条件和列表行的名称
        this.searchUnitName = JsfUtil.getRequest().getParameter("searchUnitName");
        //是否显示资质机构名称
        this.zzOrgName = "1".equals(JsfUtil.getRequest().getParameter("zzOrgName"));
        //是否隐藏被选中的记录
        this.ifHideSelected = "1".equals(JsfUtil.getRequest().getParameter("ifHideSelected"));
        this.initZone(zoneCode);
        this.init(selectIds);
    }

    /**
     * <p>方法描述：查询所有体检机构</p>
     *
     * @MethodAuthor qrr, 2019年12月3日, init
     */
    private void init(String selectIds) {
        this.displayList = new ArrayList<>();
        this.allList = new ArrayList<>();
        Map<String, Object> paramMap = new HashMap<>();
        List<Object[]> list;
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT T1.RID,CASE WHEN T2.ZONE_TYPE >2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME,'_')+1) ELSE T2.FULL_NAME END ZONE_NAME," +
                "T1.UNITNAME,T2.RID as zoneId,T2.ZONE_GB,T2.ZONE_TYPE,T2.ZONE_NAME,T.ORG_NAME,T.rid as mainRid ");

        if ("1".equals(checkType)) {
            sql.append(" FROM TD_ZW_TJORGINFO T ");
        }else if ("2".equals(checkType)) {
            sql.append(" FROM TD_ZW_DIAGORGINFO T ");
        }else if ("3".equals(checkType)) {
            sql.append(" FROM TD_ZW_SRVORGINFO T ");
        }
        sql.append(" LEFT JOIN TS_UNIT T1 ON T1.RID = T.ORG_ID ");
        sql.append(" LEFT JOIN TS_ZONE T2 ON T2.RID = T1.ZONE_ID ");
        sql.append(" WHERE T.STATE = 1 ");
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sql.append(" AND T2.ZONE_GB LIKE :zoneCode escape '\\\'");
            paramMap.put("zoneCode", ZoneUtil.zoneSelect(this.searchZoneCode) + "%");
        }
        if (StringUtils.isNotBlank(searchName)) {
            sql.append(" AND T1.UNITNAME LIKE :searchName escape '\\\'");
            paramMap.put("searchName", "%" + StringUtils.convertBFH(this.searchName.trim()) + "%");
        }
        sql.append(" ORDER BY T2.ZONE_GB,T1.UNITNAME");
        list = this.commService.findDataBySqlNoPage(sql.toString(), paramMap);
        // 第一次出现勾选的当前页的第一行数
        int row = -1;
        if (!CollectionUtils.isEmpty(list)) {
            int i = 0;
            List<String> stringList = StringUtils.string2list(selectIds, ",");
            List<SelectManyOrgVo> manyOrgVos=new ArrayList<>();
            for (Object[] obj : list) {
                SelectManyOrgVo manyOrgVo=new SelectManyOrgVo();
                manyOrgVo.setRid(Integer.parseInt(obj[0].toString()));
                manyOrgVo.setZoneFullName(obj[1].toString());
                if (this.zzOrgName) {
                    manyOrgVo.setUnitName(StringUtils.objectToString(obj[7]));
                } else {
                    manyOrgVo.setUnitName(StringUtils.objectToString(obj[2]));
                }
                manyOrgVo.setZoneRid(Integer.parseInt(obj[3].toString()));
                manyOrgVo.setZobeGb(obj[4].toString());
                manyOrgVo.setZoneType(Integer.parseInt(obj[5].toString()));
                manyOrgVo.setMainRid(Integer.parseInt(obj[8].toString()));
                manyOrgVo.setIfSelected(false);
                if (ObjectUtil.isNotEmpty(stringList)
                        && stringList.contains(manyOrgVo.getRid().toString())) {
                    if(this.ifHideSelected){
                        continue;
                    }else{
                        manyOrgVo.setIfSelected(true);
                    }
                    if (row == -1) {
                        row = i - i % 10;
                    }
                }
                manyOrgVos.add(manyOrgVo);
                i++;
            }
            this.allList.addAll(manyOrgVos);
            this.displayList.addAll(manyOrgVos);
        }
        //初始化选择当前页的第一行数
        if (row > -1) {
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("codeForm:selectedTable");
            dataTable.setFirst(row);
        }
    }

    /**
     * <p>方法描述：初始化地区</p>
     *
     * @MethodAuthor qrr, 2020年5月7日, initZone
     */
    private void initZone(String zoneCode) {
        if (StringUtils.isBlank(zoneCode)) {
            zoneCode = Global.getUser().getTsUnit().getTsZone().getZoneGb().substring(0, 2);
        }
        // 地区初始化
        this.zoneList = this.commService.findZoneListByGbAndType(zoneCode, true, "", "");
        this.searchZoneCode = this.zoneList.get(0).getZoneGb();
        this.searchZoneName = this.zoneList.get(0).getZoneName();
    }

    /**
     * <p>方法描述：根据名称、拼音码过滤数据</p>
     *
     * @MethodAuthor qrr, 2018年4月9日, searchAction
     */
    public void searchAction() {
        //初始化展示页面的数据集
        this.displayList = new ArrayList<>();
        if (null != allList && allList.size() > 0) {
            if (StringUtils.isNotBlank(searchName)) {
                for (SelectManyOrgVo t : allList) {
                    String unitName = t.getUnitName() == null ? "" : t.getUnitName();
                    //如果模糊匹配上，则增加
                    if (unitName.contains(searchName)) {
                        //地区
                        String zoneGb = t.getZobeGb();
                        if (zoneGb.startsWith(ZoneUtil.zoneSelect(this.searchZoneCode))) {
                            this.displayList.add(t);
                        }
                    }
                }
            } else {
                for (SelectManyOrgVo t : allList) {
                    String zoneGb = t.getZobeGb();
                    if (zoneGb.startsWith(ZoneUtil.zoneSelect(this.searchZoneCode))) {
                        this.displayList.add(t);
                    }
                }
            }
        }
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("codeForm:selectedTable");
        dataTable.setFirst(0);
    }

    /**
     * <p>方法描述：提交</p>
     *
     * @MethodAuthor qrr, 2019年12月2日, submitAction
     */
    public void submitAction() {
        if (null != allList && allList.size() > 0) {
            List<SelectManyOrgVo> results = new ArrayList<>();
            for (SelectManyOrgVo t : allList) {
                if (t.getIfSelected()) {
                    results.add(t);
                }
            }
            if (results.size() == 0) {
                JsfUtil.addErrorMessage("请选择数据！");
                return;
            } else {
                if (results.size() > 1000) {
                    JsfUtil.addErrorMessage("最多可选择1000条数据！");
                    return;
                }
            }
            Map<String, List<SelectManyOrgVo>> map = new HashMap<String, List<SelectManyOrgVo>>();
            map.put("selectPros", results);
            RequestContext.getCurrentInstance().closeDialog(map);
        }
    }

    /**
     * <p>方法描述：关闭</p>
     *
     * @MethodAuthor qrr, 2018年4月9日, dialogClose
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

    public String getSearchName() {
        return searchName;
    }

    public List<SelectManyOrgVo> getDisplayList() {
        return displayList;
    }

    public void setSearchName(String searchName) {
        this.searchName = searchName;
    }

    public void setDisplayList(List<SelectManyOrgVo> displayList) {
        this.displayList = displayList;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getCheckType() {
        return checkType;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public boolean getIfHideSelected() {
        return ifHideSelected;
    }
    public void setIfHideSelected(boolean ifHideSelected) {
        this.ifHideSelected = ifHideSelected;
    }
}
