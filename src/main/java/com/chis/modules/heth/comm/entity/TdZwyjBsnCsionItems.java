package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-10-29
 */
@Entity
@Table(name = "TD_ZWYJ_BSN_CSION_ITEMS")
@SequenceGenerator(name = "TdZwyjBsnCsionItems", sequenceName = "TD_ZWYJ_BSN_CSION_ITEMS_SEQ", allocationSize = 1)
public class TdZwyjBsnCsionItems implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwyjBsnCsionItemSub fkByMainId;
	private TbTjItems fkByItemId;
	private Integer sex;
	private Integer jdgptn;
	private TsSimpleCode fkByMsruntId;
	private BigDecimal ge;
	private BigDecimal gt;
	private BigDecimal le;
	private BigDecimal lt;
	private Integer hgFlag;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjBsnCsionItems() {
	}

	public TdZwyjBsnCsionItems(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjBsnCsionItems")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwyjBsnCsionItemSub getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwyjBsnCsionItemSub fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_ID")			
	public TbTjItems getFkByItemId() {
		return fkByItemId;
	}

	public void setFkByItemId(TbTjItems fkByItemId) {
		this.fkByItemId = fkByItemId;
	}	
			
	@Column(name = "SEX")	
	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}	
			
	@Column(name = "JDGPTN")	
	public Integer getJdgptn() {
		return jdgptn;
	}

	public void setJdgptn(Integer jdgptn) {
		this.jdgptn = jdgptn;
	}	
			
	@ManyToOne
	@JoinColumn(name = "MSRUNT_ID")			
	public TsSimpleCode getFkByMsruntId() {
		return fkByMsruntId;
	}

	public void setFkByMsruntId(TsSimpleCode fkByMsruntId) {
		this.fkByMsruntId = fkByMsruntId;
	}	
			
	@Column(name = "GE")	
	public BigDecimal getGe() {
		return ge;
	}

	public void setGe(BigDecimal ge) {
		this.ge = ge;
	}	
			
	@Column(name = "GT")	
	public BigDecimal getGt() {
		return gt;
	}

	public void setGt(BigDecimal gt) {
		this.gt = gt;
	}	
			
	@Column(name = "LE")	
	public BigDecimal getLe() {
		return le;
	}

	public void setLe(BigDecimal le) {
		this.le = le;
	}	
			
	@Column(name = "LT")	
	public BigDecimal getLt() {
		return lt;
	}

	public void setLt(BigDecimal lt) {
		this.lt = lt;
	}	
			
	@Column(name = "HG_FLAG")
	public Integer getHgFlag() {
		return hgFlag;
	}

	public void setHgFlag(Integer hgFlag) {
		this.hgFlag = hgFlag;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}