package com.chis.modules.heth.comm.enumn;

/**
 * <p>类描述：季度对应开始与结束的月、日</p>
 * @ClassAuthor qrr,2019年10月9日,QuarterDateEnum
 * */
public enum QuarterDateCommEnum {

    Quarter_1("01-01","03-31",1,"第一季度"),
    Quarter_2("04-01","06-30",2,"第二季度"),
    Quarter_3("07-01","09-30",3,"第三季度"),
    Quarter_4("10-01","12-31",4,"第四季度");

    private String sdate;
    private String edate;
    private Integer quarter;
    private String quarterName;

    private QuarterDateCommEnum(String sdate,String edate,Integer quarter,String quarterName) {
        this.sdate = sdate;
        this.edate = edate;
        this.quarter = quarter;
        this.quarterName = quarterName;
    }

    public String getSdate() {
        return sdate;
    }

    public String getEdate() {
        return edate;
    }

    public void setSdate(String sdate) {
        this.sdate = sdate;
    }

    public void setEdate(String edate) {
        this.edate = edate;
    }

    public Integer getQuarter() {
        return quarter;
    }

    public void setQuarter(Integer quarter) {
        this.quarter = quarter;
    }

    public String getQuarterName() {
        return quarterName;
    }

    public void setQuarterName(String quarterName) {
        this.quarterName = quarterName;
    }
}
