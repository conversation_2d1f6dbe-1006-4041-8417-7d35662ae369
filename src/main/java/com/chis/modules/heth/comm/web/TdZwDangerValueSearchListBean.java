package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.javabean.TdTjBhkInfoBean;
import com.chis.modules.heth.comm.service.HethBaseCommServiceImpl;
import com.chis.modules.heth.comm.service.HethStaQueryCommServiceImpl;
import com.chis.modules.heth.comm.service.TdZdzybCommServiceImpl;
import com.chis.modules.heth.comm.service.ZwReportCardCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.poi.ss.formula.functions.T;
import org.primefaces.component.column.Column;
import org.primefaces.component.fieldset.Fieldset;
import org.primefaces.component.outputpanel.OutputPanel;
import org.primefaces.component.panelgrid.PanelGrid;
import org.primefaces.component.row.Row;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.TreeNode;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.component.html.HtmlOutputText;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * @Description : 危急值查询
 * @ClassAuthor : rcj
 * @Date : 2020/11/17 10:19
 **/
@ManagedBean(name = "tdZwDangerValueSearchListBean")
@ViewScoped
public class TdZwDangerValueSearchListBean extends FacesEditBean implements IProcessData {

    /**查询条件：地区名称*/
    private String searchZoneName;
    /**查询条件：地区编码*/
    private String searchZoneCode;
    /**查询条件：地区集合*/
    private List<TsZone> zoneList;
    /**查询条件：检查机构Id*/
    private String searchUnitId;
	/**查询条件-体检机构名称*/
	private String searchUnitName;
    /**查询条件：用人单位地区名称*/
    private String searchCrptZoneCode;
    /**查询条件：用人单位地区编码*/
    private String searchCrptZoneName;
    /**查询条件：用人单位名称*/
    private String searchCrptName;
    /**查询条件：姓名*/
    private String searchPersonName;
    /**查询条件：身份证*/
    private String searchPersonIdc;
    /**查询条件：体检日期查询-开始日期*/
    private Date searchBhkBdate;
    /**查询条件：体检日期查询-结束日期*/
    private Date searchBhkEdate;
	/**查询条件-在岗状态*/
	private List<TsSimpleCode> onGuardList;
	private String selectOnGuardNames;
	/**查询条件-在岗状态*/
	private String selectOnGuardIds;
	/**查询条件-危害因素*/
	private List<TsSimpleCode> badRsnList;
	private String selectBadRsnNames;
	/**查询条件-危害因素*/
	private String selectBadRsnIds;
	/**查询条件-结论是否正常*/
	private String[] concluStates;
	/**查询条件-已选择的年龄*/
	private TreeNode itemSortTree;
	private TreeNode[] selectItems;
	private String selectItemName;
	private String selectItemIds;

    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private HethBaseCommServiceImpl baseServiceImpl = SpringContextHolder.getBean(HethBaseCommServiceImpl.class);
	private ZwReportCardCommServiceImpl cardServiceImpl = SpringContextHolder.getBean(ZwReportCardCommServiceImpl.class);
	private HethStaQueryCommServiceImpl hethStaQueryServiceImpl = SpringContextHolder.getBean(HethStaQueryCommServiceImpl.class);
	private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);

	/** 体检项目的布局表格 */
	private Integer rid ;
    /**
	 * 体检项目的布局表格
	 */
	private OutputPanel archivePanel = (OutputPanel) JsfUtil.getApplication().createComponent(
			OutputPanel.COMPONENT_TYPE);
	/**体检记录Id*/
	private Integer bhkId;
	private TdZwyjDangerBhk tdZwyjDangerBhk;
	/**
	 * 体检显示页面的实例
	 */
	private TdTjBhk tdTjBhkShow;
	/**
	 * 问诊
	 */
	private TdTjExmsdata tdTjExmsdata = new TdTjExmsdata();
	/**
	 * 职业史集合
	 */
	private List<TdTjEmhistory> tdTjEmhistoryList;
	/**
	 * 放射史集合
	 */
	private List<TdTjEmhistory> tdTjEmhistoryList2;
	// 系统参数
	private String tzParam;
	private String tsParam;
	private String hyParam;


	/**体检记录表是否存在*/
	private boolean ifBhkInfoExist;

    private TdTjBhkInfoBean tjBhkInfoBean = new TdTjBhkInfoBean();

	public TdZwDangerValueSearchListBean() {
        this.initParam();
        this.ifSQL = true;
		this.initItemSortTree();
        this.searchAction();
    }

    /**
    * @Description : 查询参数初始化
    * @MethodAuthor: rcj
    * @Date : 2020/11/17 10:23
    **/
    private void initParam() {
    	TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
		if (null==tsZone) {
			tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
		}
        this.searchZoneCode = tsZone.getZoneCode();
        this.searchZoneName = tsZone.getZoneName();
		this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb(), true, "", "");
        // 默认：前日期往前推90天
        this.searchBhkBdate = DateUtils.getYearFirstDay(new Date());
        this.searchBhkEdate = new Date();


		this.onGuardList = commService.findSimpleCodesByTypeId("5009");

		this.badRsnList = commService.findSimpleCodesByTypeId("5007");

	}

	/**
	 * <p>方法描述：初始化项目</p>
	 * @MethodAuthor rcj,2020年4月22日,initWorkSortTree
	 * */
	private void initItemSortTree() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select t from TdZwyjDangerItems t where t.ifReveal=1 order by t.fkByItemId.num,t.fkByItemId.itemCode");
		List<TdZwyjDangerItems> list = cardServiceImpl.findByHql(sql.toString(), TdZwyjDangerItems.class);
		this.itemSortTree = new CheckboxTreeNode("root", null);
		this.initSortTree(list, this.itemSortTree);
	}

	/**
	 * 初始化树
	 */
	private void initSortTree(List<TdZwyjDangerItems> list,TreeNode sortTree) {
		if (null != list && list.size() > 0) {
			for (TdZwyjDangerItems detail : list) {
				new CheckboxTreeNode(detail, sortTree);
			}
		}

	}

    /**
    * @Description : 执行查询
    * @MethodAuthor: rcj
    * @Date : 2020/11/17 10:23
    **/
    @Override
    public void searchAction() {
		if(DateUtils.isDateAfter(searchBhkBdate, searchBhkEdate)){
			JsfUtil.addErrorMessage("体检开始日期应小于等于体检结束日期！");
			return;
		}
        super.searchAction();
    }

    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder();
		sb.append("select T.RID,");
		sb.append("       T5.FULL_NAME,");
		sb.append("       T6.CRPT_NAME,");
		sb.append("       T.PERSON_NAME,");
		sb.append("       DECODE(LENGTH(T.IDC),");
		sb.append("              15,");
		sb.append("              SUBSTR(T.IDC, 1, 6) || '******' || SUBSTR(T.IDC, 13),");
		sb.append("              18,");
		sb.append("              SUBSTR(T.IDC, 1, 6) || '********' || SUBSTR(T.IDC, 15),T.IDC) AS IDC,");
		sb.append("       T8.CODE_NAME,");
		sb.append("       A.badrsn,");
		sb.append("       B.dangerItem,");
		sb.append("       T.IF_RHK,");
		sb.append("       T.BHK_DATE,");
		sb.append("       T2.UNIT_NAME,");
		sb.append("       T5.ZONE_GB");
		sb.append("  FROM TD_ZWYJ_DANGER_BHK T");
		sb.append("  LEFT JOIN TB_TJ_SRVORG T2");
		sb.append("    ON T.BHKORG_ID = T2.RID");
		sb.append("  LEFT JOIN TB_TJ_CRPT T6");
		sb.append("    ON T.ENTRUST_CRPT_ID = T6.RID");
		sb.append("  LEFT JOIN TS_ZONE T5");
		sb.append("    ON T6.ZONE_ID = T5.RID");
		sb.append("  LEFT JOIN TS_SIMPLE_CODE T8");
		sb.append("    ON T8.RID = T.ONGUARD_STATEID");
		sb.append("  LEFT JOIN (SELECT T.RID,");
		sb.append("                    LISTAGG(T4.CODE_NAME, '，') WITHIN GROUP(ORDER BY T4.CODE_LEVEL_NO) as ");
		sb.append("badrsn");
		sb.append("               FROM TD_ZWYJ_DANGER_BHK T");
		sb.append("               LEFT JOIN TD_ZWYJ_DANGER_BSN T3");
		sb.append("                 ON T3.BHK_ID = T.RID");
		sb.append("               LEFT JOIN TS_SIMPLE_CODE T4");
		sb.append("                 ON T4.RID = T3.BADRSN_ID");
		sb.append("              group by T.RID) A");
		sb.append("    ON A.RID = T.RID");
		sb.append("  LEFT JOIN (SELECT T.RID,");
		sb.append("                    LISTAGG(T3.ITEM_NAME, '，') WITHIN GROUP(ORDER BY T3.NUM) as ");
		sb.append("dangerItem");
		sb.append("               FROM TD_ZWYJ_DANGER_BHK T");
		sb.append("               LEFT JOIN TD_ZWYJ_DANGER_RESULT T1");
		sb.append("                 ON T1.MAIN_ID = T.RID");
		sb.append("               LEFT JOIN TD_ZWYJ_DANGER_ITEMS T2");
		sb.append("                 ON T2.RID = T1.DANGER_ID");
		sb.append("               LEFT JOIN TB_TJ_ITEMS T3");
		sb.append("                 ON T3.RID = T2.ITEM_ID");
		sb.append("              group by T.RID) B");
		sb.append("    ON B.RID = T.RID");
		sb.append(" WHERE 1=1 ");
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sb.append(" AND T5.ZONE_GB LIKE :zonecode escape '\\\'");
            this.paramMap.put("zonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        // 体检机构
        if (null != this.searchUnitId) {
            sb.append(" AND T2.RID in ( ").append(this.searchUnitId).append(")");
        }
        //企业名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sb.append(" AND T6.CRPT_NAME LIKE :crptName  escape '\\\'");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
		//人员姓名
		if (StringUtils.isNotBlank(this.searchPersonName)) {
			sb.append(" AND T.PERSON_NAME LIKE :personName  escape '\\\'");
			this.paramMap.put("personName", "%" + StringUtils.convertBFH(this.searchPersonName.trim()) + "%");
		}
        //人员身份证
        if (StringUtils.isNotBlank(this.searchPersonIdc)) {
            sb.append(" AND T.IDC  = :personIdc ");
            this.paramMap.put("personIdc", StringUtils.convertBFH(this.searchPersonIdc.trim()));
        }
		// 体检日期
		if (null != this.searchBhkBdate) {
			sb.append(" AND T.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchBhkBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
		}
		if (null != this.searchBhkEdate) {
			sb.append(" AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchBhkEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
		}
		//在岗状态
		if(StringUtils.isNotBlank(selectOnGuardIds)){
			sb.append(" AND T8.RID in (").append(this.selectOnGuardIds).append(")");
		}
		//危害因素
		if(StringUtils.isNotBlank(selectBadRsnIds)){
			sb.append(" AND EXISTS  ( SELECT C.RID FROM TD_ZWYJ_DANGER_BSN C   WHERE C.BHK_ID = T.RID  AND  C.BADRSN_ID IN (").append(this.selectBadRsnIds).append(") )");
		}
		//项目
		if(StringUtils.isNotBlank(selectItemIds)){
			sb.append(" AND EXISTS  ( SELECT D.RID FROM TD_ZWYJ_DANGER_RESULT D LEFT JOIN TD_ZWYJ_DANGER_ITEMS E ON E.RID = D.DANGER_ID  WHERE D.MAIN_ID = T.RID  AND  E.ITEM_ID IN (").append(this.selectItemIds).append(") )");
		}
        String h2 = "SELECT COUNT(*) FROM (" + sb.toString() + ")";
        String h1 = "SELECT * FROM (" + sb.toString() + ")AA ORDER BY AA.ZONE_GB, AA.CRPT_NAME, AA.BHK_DATE DESC,AA.PERSON_NAME";
        return new String[] { h1, h2 };
    }

    @Override
    public void processData(List<?> list) {

    	if(!CollectionUtils.isEmpty(list)){
			List<Object[]> objList = (List<Object[]>)list;
			for (Object[] obj:objList) {
				if(null != obj[1]){
					int i = obj[1].toString().indexOf("_");
					String substring = obj[1].toString().substring(i + 1);
					obj[1] = substring;
				}
				if(null !=obj[4] && !obj[4].toString().contains("****") && obj[4].toString().length()>=4){
					obj[4] = obj[4].toString().substring(0,obj[4].toString().length()-4)+"****";
				}
			}
		}

	}

	/**
	 * <p>方法描述：查询体检机构</p>
	 * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
	 * */
	public void selUnitAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null,625,null,500);

		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		List<String> paramList = new ArrayList<String>();
		paramList.add(searchUnitId);
		paramMap.put("selectIds", paramList);
		List<String> paramList2 = new ArrayList<String>();
		paramList2.add(searchZoneCode.substring(0,2));
		paramMap.put("searchZoneCode", paramList2);
		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
	}
	/**
	 * <p>方法描述：处理选择的体检机构</p>
	 * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
	 * */
	public void onSelectUnitAction(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event
				.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			List<TbTjSrvorg> list = (List<TbTjSrvorg>) selectedMap.get("selectPros");
			if (null!=list && list.size()>0) {
				StringBuffer names = new StringBuffer();
				StringBuffer ids = new StringBuffer();
				for (TbTjSrvorg t : list) {
					names.append("，").append(t.getUnitName());
					ids.append(",").append(t.getRid());
				}
				searchUnitId=ids.substring(1);
				this.searchUnitName = names.substring(1);
			}
		}
	}
	/**
	 * <p>方法描述：清空单位</p>
	 * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
	 * */
	public void clearUnit() {
		searchUnitId = null;
		this.searchUnitName = null;
	}

    @Override
    public void addInit() {

    }


	@Override
    public void viewInit() {
    }

    @Override
    public void modInit() {
		tdZwyjDangerBhk = cardServiceImpl.findTdZwyjDangerBhkById(rid);
		bhkId =cardServiceImpl.findBhkId(tdZwyjDangerBhk.getFkByBhkorgId().getRid(),tdZwyjDangerBhk.getBhkCode());
		this.getBhkInfo();
		RequestContext.getCurrentInstance().scrollTo("tabView:editForm:editTitleGrid");
    }


    /**
 	 * <p>方法描述：获取体检详情信息</p>
 	 * @MethodAuthor rcj,2020年11月12日,getBhkInfo
	 * */
	private void getBhkInfo() {
        tjBhkInfoBean = new TdTjBhkInfoBean();
		if(null != bhkId){
			tjBhkInfoBean.setIfShowBaseOnly(false);
			ifBhkInfoExist = true;
            tjBhkInfoBean.setRid(bhkId);
            tjBhkInfoBean.setArchivePanel(archivePanel);
            tjBhkInfoBean.setIfManagedOrg(true);
            tjBhkInfoBean.initBhkInfo();
		}else{
			tjBhkInfoBean.setIfShowBaseOnly(true);
			ifBhkInfoExist = false;
			tdTjBhkShow = new TdTjBhk();
			tdTjBhkShow.setBhkCode(tdZwyjDangerBhk.getBhkCode());
			tdTjBhkShow.setTbTjSrvorg(tdZwyjDangerBhk.getFkByBhkorgId());
			tdTjBhkShow.setTbTjCrpt(tdZwyjDangerBhk.getFkByCrptId());
			tdTjBhkShow.setCrptName(tdZwyjDangerBhk.getCrptName());
			tdTjBhkShow.setPersonName(tdZwyjDangerBhk.getPersonName());
			tdTjBhkShow.setSex(tdZwyjDangerBhk.getSex());
			tdTjBhkShow.setFkByCardTypeId(tdZwyjDangerBhk.getFkByCardTypeId());
			tdTjBhkShow.setIdc(tdZwyjDangerBhk.getIdc());
			tdTjBhkShow.setPsnType(tdZwyjDangerBhk.getPsnType());
			tdTjBhkShow.setBrth(tdZwyjDangerBhk.getBrth());
			tdTjBhkShow.setAge(tdZwyjDangerBhk.getAge());
			tdTjBhkShow.setIsxmrd(tdZwyjDangerBhk.getIsxmrd());
			tdTjBhkShow.setDpt(tdZwyjDangerBhk.getDpt());
			tdTjBhkShow.setTsSimpleCode(tdZwyjDangerBhk.getFkByOnguardStateid());
			tdTjBhkShow.setBhkType(tdZwyjDangerBhk.getBhkType());
			tdTjBhkShow.setBhkDate(tdZwyjDangerBhk.getBhkDate());
			tdTjBhkShow.setWrklnt(null==tdZwyjDangerBhk.getWrklnt()?null:Double.valueOf(tdZwyjDangerBhk.getWrklnt().toString()));
			tdTjBhkShow.setWrklntmonth(tdZwyjDangerBhk.getWrklntmonth());
			tdTjBhkShow.setTchbadrsntim(null==tdZwyjDangerBhk.getTchbadrsntim()?null:Double.valueOf(tdZwyjDangerBhk.getTchbadrsntim().toString()));
			tdTjBhkShow.setTchbadrsnmonth(tdZwyjDangerBhk.getTchbadrsnmonth());
			tdTjBhkShow.setWorkName(tdZwyjDangerBhk.getWorkName());
			tdTjBhkShow.setBadrsn(tdZwyjDangerBhk.getBadrsn());
			tdTjBhkShow.setBhkrst(tdZwyjDangerBhk.getBhkrst());
			tdTjBhkShow.setMhkadv(tdZwyjDangerBhk.getMhkadv());
            List<TdTjMhkrst> tdTjMhkrsts = new ArrayList<>();
            TdTjMhkrst mhkrst = new TdTjMhkrst();
			TsSimpleCode code = new TsSimpleCode();
			code.setCodeDesc(tdZwyjDangerBhk.getOcpBhkrstdes());
            mhkrst.setTsSimpleCode(code);
            tdTjMhkrsts.add(mhkrst);
            tdTjBhkShow.setTdTjMhkrsts(tdTjMhkrsts);
			// 隐藏身份证号和出生日期
			if(StringUtils.isNotBlank(tdTjBhkShow.getIdc())) {
				tdTjBhkShow.setIdc(StringUtils.encryptIdc(tdTjBhkShow.getIdc()));
			}
			//用人单位
			if(tdZwyjDangerBhk.getFkByCrptId()!=null&& tdZwyjDangerBhk.getFkByCrptId().getRid()!=null
				&&tdZwyjDangerBhk.getFkByBhkorgId()!=null && tdZwyjDangerBhk.getFkByBhkorgId().getTsUnit()!=null && tdZwyjDangerBhk.getFkByBhkorgId().getTsUnit().getRid()!=null){
				TbTjCrptIndepend tbTjCrptIndepend = this.hethStaQueryServiceImpl.findTbTjCrptIndependByBusType(1, tdZwyjDangerBhk.getFkByCrptId().getRid(), tdZwyjDangerBhk.getFkByBhkorgId().getTsUnit().getRid());
				if(tbTjCrptIndepend!=null){
					tbTjCrptIndepend.setLinkphone2(StringUtils.encryptPhone(tbTjCrptIndepend.getLinkphone2()));

					tjBhkInfoBean.setTbTjCrptIndepend(tbTjCrptIndepend);
				}
			}
			//用工单位
			if(tdZwyjDangerBhk.getFkByEntrusTCrptId()!=null && tdZwyjDangerBhk.getFkByEntrusTCrptId().getRid()!=null){
				tdTjBhkShow.setFkByEntrustCrptId(tdZwyjDangerBhk.getFkByEntrusTCrptId());
				if(tdZwyjDangerBhk.getFkByBhkorgId()!=null && tdZwyjDangerBhk.getFkByBhkorgId().getTsUnit()!=null && tdZwyjDangerBhk.getFkByBhkorgId().getTsUnit().getRid()!=null){
					TbTjCrptIndepend tbTjCrptIndependEntrust = this.hethStaQueryServiceImpl.findTbTjCrptIndependByBusType(1, tdZwyjDangerBhk.getFkByEntrusTCrptId().getRid(), tdZwyjDangerBhk.getFkByBhkorgId().getTsUnit().getRid());
					if(tbTjCrptIndependEntrust!=null){
						tbTjCrptIndependEntrust.setLinkphone2(StringUtils.encryptPhone(tbTjCrptIndependEntrust.getLinkphone2()));
						tjBhkInfoBean.setTbTjCrptIndependEntrust(tbTjCrptIndependEntrust);
					}
				}
			}
            tjBhkInfoBean.setTdTjBhkShow(tdTjBhkShow);
			FacesContext.getCurrentInstance().renderResponse();
		}
	}
    
	/**
	 * 构建outputPanel，依次页面结构层次是：outputpanel, panel, panelGrid, row, column,控件
	 * 
	 * @param dataMap
	 *            组件数据集合
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private void createOutPanel(Map<TsSimpleCode, List<Object[]>> dataMap) {
		archivePanel.getChildren().clear();
		if (null != dataMap && dataMap.size() > 0) {
			Set<TsSimpleCode> tsSimpleCodeSet = dataMap.keySet();
			// 遍历数据集合，当集合中出现一级分类时，就创建一个Fieldset
			for (TsSimpleCode t : tsSimpleCodeSet) {
				if (t.getCodeLevelNo().split("\\.").length == 1) {
					createPanel(t, dataMap);
				}
			}
		}
	}
	/**
	 * 创建Fieldset，并加入到archivePanel中 创建步骤： 1、创建一个新的Fieldset，设置样式和内容
	 * 2、遍历数据集合，每找到一个该一级分类下的二级分类，则为该二级分类建一个新的PanelGrid
	 * 3、给新创建的PanelGrid设置表格属性，表头内容，并调用相应方法创建表体 4、将新建的PanelGrid加入到Fieldset中
	 * 5、继续循环，将集合中该一级分类下所有的二级分类中的数据都做成PanelGrid，添加到Fieldset
	 * 
	 * @param ts
	 *            一级分类的码表实例
	 * @param dataMap
	 *            数据集合
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private void createPanel(TsSimpleCode ts, Map<TsSimpleCode, List<Object[]>> dataMap) {
		// 第一步，创建Fieldset
		final Fieldset fieldset = createFieldset(ts.getCodeName(), "margin-top: 5px;margin-bottom: 5px;");
		// 第二步，遍历集合，创建Fieldset下的PanelGrid
		Set<TsSimpleCode> tsSimpleCodeSet = dataMap.keySet();
		for (TsSimpleCode t : tsSimpleCodeSet) {
			String[] str = t.getCodeLevelNo().split("\\.");
			// 第三步,判断此二级分类是否在该一级分类下，若是则创建PanelGrid
			if (str.length == 2 && str[0].equals(ts.getCodeLevelNo())) {
				List<Object[]> list = dataMap.get(t);
				final PanelGrid panelGrid = createPanelGrid("width:100%;margin-top:8px;");
				final Row headRow = createRow();
				final Column headColumn = createColumn(8, "ui-widget-header", "height:22px;");
				final HtmlOutputText headText = createOutputtext(t.getCodeName());
				headColumn.getChildren().add(headText);
				headRow.getChildren().add(headColumn);
				panelGrid.getChildren().add(headRow);
				// 调用相应方法创建表体，根据系统参数值，调用对应的方法创建表体
				boolean bool = true;
				if (str[0].equals(tsParam)) {
					bool = this.createPanelGrid1(panelGrid, list);
				} else if (str[0].equals(tzParam)) {
					bool = this.createPanelGrid2(panelGrid, list);
				} else if (str[0].equals(hyParam)) {
					bool = this.createPanelGrid3(panelGrid, list);
				} else {
					bool = this.createPanelGrid1(panelGrid, list);
				}
				// 第四步，如果表体内有组件，则将该表添加到Fieldse
				if (!bool) {
					fieldset.getChildren().add(panelGrid);
				}
			}
		}
		// 第五步，将Fieldse添加到outpanel中
		if (fieldset.getChildren().size() != 0) {
			archivePanel.getChildren().add(fieldset);
		}
	}
	/**
	 * 创建Fieldset
	 * 
	 * @param legend
	 *            标题
	 * @param style
	 *            样式
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private Fieldset createFieldset(String legend, String style) {
		Fieldset fieldset = (Fieldset) JsfUtil.getApplication().createComponent(Fieldset.COMPONENT_TYPE);
		fieldset.setLegend(legend);
		fieldset.setStyle(style);
		fieldset.setToggleable(true);
		fieldset.setToggleSpeed(500);
		return fieldset;
	}
	/**
	 * 创建PanelGrid
	 * 
	 * @param style
	 *            样式
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private PanelGrid createPanelGrid(String style) {
		PanelGrid panelGrid = (PanelGrid) JsfUtil.getApplication().createComponent(PanelGrid.COMPONENT_TYPE);
		if (null != style) {
			panelGrid.setStyle(style);
		}
		return panelGrid;
	}
	/**
	 * 创建Row
	 * 
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private Row createRow() {
		Row row = (Row) JsfUtil.getApplication().createComponent(Row.COMPONENT_TYPE);
		return row;
	}
	/**
	 * 创建列
	 * 
	 * @param colspan
	 *            合并列数
	 * @param styleClass
	 *            样式类
	 * @param style
	 *            样式
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private Column createColumn(Integer colspan, String styleClass, String style) {
		Column column = (Column) JsfUtil.getApplication().createComponent(Column.COMPONENT_TYPE);
		if (null != colspan) {
			column.setColspan(colspan);
		}
		if (null != styleClass) {
			column.setStyleClass(styleClass);
		}
		if (null != style) {
			column.setStyle(style);
		}
		return column;
	}

	/**
	 * 创建HtmlOutputText
	 * 
	 * @param value
	 *            值
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private HtmlOutputText createOutputtext(Object value) {
		HtmlOutputText htmlOutputText = (HtmlOutputText) JsfUtil.getApplication().createComponent(
				HtmlOutputText.COMPONENT_TYPE);
		if (null != value) {
			htmlOutputText.setValue(value);
		}
		return htmlOutputText;
	}
	/**
	 * 该方法用于创建功能性及特殊检查的panelgrid 1、设置表格的第一行内容 2、遍历该分类下的数据集合，将集合数据添加到表格中 3、返回创建结果
	 * 
	 * @param panelGrid
	 *            需要创建内容的panelGrid
	 * @param list
	 *            该分类下的数据list集合
	 * @return 表格有内容返回true，否则返回false
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private boolean createPanelGrid1(PanelGrid panelGrid, List<Object[]> list) {
		boolean isBlank = true;
		// 标题行
		final Row titleRow = createRow();
		// 标题列
		final Column titleColumn1 = createColumn(null, "ui-state-default",
				"width:250px;text-align: center;height:20px;");
		final HtmlOutputText titleText1 = createOutputtext("项目");
		titleColumn1.getChildren().add(titleText1);
		final Column titleColumn2 = createColumn(null, "ui-state-default", "text-align: center;");
		final HtmlOutputText titleText2 = createOutputtext("结果");
		titleColumn2.getChildren().add(titleText2);
		// 组件关联
		titleRow.getChildren().add(titleColumn1);
		titleRow.getChildren().add(titleColumn2);
		panelGrid.getChildren().add(titleRow);
		// 遍历数据集合
		for (Object[] obj : list) {
			final Row contentRow = createRow();
			// 某条数据记录的第一列，设置内容和样式，数据为空，显示"—"
			final Column contentColumn1 = createColumn(null, null, "text-align: left;height:20px;");
			final HtmlOutputText contentText1 = createOutputtext(obj[0] == null ? "—" : obj[0]);
			contentColumn1.getChildren().add(contentText1);
			// 某条数据记录的第二列，设置内容和样式，数据为空，显示"—"
			final Column contentColumn2 = createColumn(null, null, "text-align: left;");
			final HtmlOutputText contentText2 = createOutputtext(obj[1] == null ? "—" : obj[1]);
			contentColumn2.getChildren().add(contentText2);
			// 组件关联
			contentRow.getChildren().add(contentColumn1);
			contentRow.getChildren().add(contentColumn2);
			panelGrid.getChildren().add(contentRow);
			isBlank = false;
		}
		return isBlank;
	}

	/**
	 * 该方法用于创建体征panelgrid，体征需要分栏显示，因此，需要在createPanelGrid1基础增加循环
	 * 1、设置表格的第一行内容，内容需要循环一次，做成分栏效果 2、遍历该分类下的数据集合，将集合数据添加到表格中，每行中添加两条记录，循环实现
	 * 3、返回创建结果
	 * 
	 * @param panelGrid
	 *            需要创建内容的panelGrid
	 * @param list
	 *            该分类下的数据list数据集合
	 * @return 表格有内容返回true，否则返回false
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private boolean createPanelGrid2(PanelGrid panelGrid, List<Object[]> list) {
		boolean isBlank = true;
		// 1.创建标题行，分栏需要循环创建两次，四列
		final Row titleRow = createRow();
		for (int i = 0; i < 2; i++) {
			final Column titleColumn1 = createColumn(null, "ui-state-default",
					"width:150px;text-align: center;height:20px;");
			final HtmlOutputText titleText1 = createOutputtext("项目");
			titleColumn1.getChildren().add(titleText1);
			final Column titleColumn2 = createColumn(null, "ui-state-default", "width:300px;text-align: center;");
			final HtmlOutputText titleText2 = createOutputtext("结果");
			titleColumn2.getChildren().add(titleText2);
			titleRow.getChildren().add(titleColumn1);
			titleRow.getChildren().add(titleColumn2);
		}
		panelGrid.getChildren().add(titleRow);
		// 2.创建内容行
		int count = 0;
		int length = list.size();
		Row contentRow = null;
		// 3.遍历集合，添加数据，分栏添加
		for (int i = 0; i < length; i++) {
			Object[] obj = list.get(i);
			// 如果该行已有两列数据则创建一个新行
			if (count == 0) {
				contentRow = createRow();
			}
			// 给该行添加数据列
			final Column contentColumn1 = createColumn(null, null, "text-align: left;height:20px;");
			final HtmlOutputText contentText1 = createOutputtext(obj[0] == null ? "—" : obj[0]);
			contentColumn1.getChildren().add(contentText1);
			final Column contentColumn2 = createColumn(null, null, "text-align: left;");
			// 如果是最后一个且该行只有一条记录则合并列3列数据
			if (i == (length - 1) && count == 0) {
				contentColumn2.setColspan(3);
			}
			final HtmlOutputText contentText2 = createOutputtext(obj[1] == null ? "—" : obj[1]);
			// 关联组件
			contentColumn2.getChildren().add(contentText2);
			contentRow.getChildren().add(contentColumn1);
			contentRow.getChildren().add(contentColumn2);
			count++;
			// 如果该行已有两条数据记录，则将该行添加到表格中，并创建一个新行
			if (count == 2) {
				panelGrid.getChildren().add(contentRow);
				isBlank = false;
				count = 0;
			}
		}
		// 添加最后一行的数据记录
		if (count == 1) {
			panelGrid.getChildren().add(contentRow);
			isBlank = false;
		}
		return isBlank;
	}

	/**
	 * 该方法用于创建化验检查检查panelgrid，化验需要添加新的数据列，因此，需要在createPanelGrid1基础增加新列
	 * 1、设置表格的第一行内容 2、遍历该分类下的数据集合，将集合数据添加到表格中 3、返回创建结果
	 * 
	 * @param panelGrid
	 *            需要创建内容的panelGrid
	 * @param list
	 *            该分类下的数据list数据集合
	 * @return 表格有内容返回true，否则返回false
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private boolean createPanelGrid3(PanelGrid panelGrid, List<Object[]> list) {
		boolean isBlank = true;
		// 第一步.创建标题行，分栏需要循环创建两次，八列
		final Row titleRow = createRow();
		for (int i = 0; i < 2; i++) {
			// 添加项目列
			final Column titleColumn1 = createColumn(null, "ui-state-default",
					"width:120px;text-align: center;height:20px;");
			final HtmlOutputText titleText1 = createOutputtext("项目");
			titleColumn1.getChildren().add(titleText1);
			// 添加结果列
			final Column titleColumn2 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
			final HtmlOutputText titleText2 = createOutputtext("结果");
			titleColumn2.getChildren().add(titleText2);
			// 添加参考值列
			final Column titleColumn3 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
			final HtmlOutputText titleText3 = createOutputtext("参考值");
			titleColumn3.getChildren().add(titleText3);
			// 添加计量单位列
			final Column titleColumn4 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
			final HtmlOutputText titleText4 = createOutputtext("计量单位");
			titleColumn4.getChildren().add(titleText4);
			// 组件关联
			titleRow.getChildren().add(titleColumn1);
			titleRow.getChildren().add(titleColumn2);
			titleRow.getChildren().add(titleColumn3);
			titleRow.getChildren().add(titleColumn4);
		}
		panelGrid.getChildren().add(titleRow);
		// 第二步，创建内容行，遍历集合，添加数据，分栏添加
		int count = 0;
		int length = list.size();
		Row contentRow = null;
		for (int i = 0; i < length; i++) {
			Object[] obj = list.get(i);
			// 1.如果该行已有两条数据则创建一个新行
			if (count == 0) {
				contentRow = createRow();
			}
			// 2.添加项目名称内容
			final Column contentColumn1 = createColumn(null, null, "text-align: left;height:20px;");
			final HtmlOutputText contentText1 = createOutputtext(obj[0] == null ? "—" : obj[0]);
			contentColumn1.getChildren().add(contentText1);
			// 3.添加体检结果内容
			final Column contentColumn2 = createColumn(null, null, null);
			final HtmlOutputText contentText2 = createOutputtext(null);
			this.setPanelgridValue(contentColumn2, contentText2, obj);
			contentColumn2.getChildren().add(contentText2);
			// 4.添加参考值列的内容
			final Column contentColumn3 = createColumn(null, null, "text-align: left");
			final HtmlOutputText contentText3 = createOutputtext(obj[3] == null ? "—" : obj[3]);
			contentColumn3.getChildren().add(contentText3);
			// 5.添加计量单位的列的内容
			final Column contentColumn4 = createColumn(null, null, "text-align: left;");
			// 如果是最后一个且该行只有一条记录则合并列3列数据
			if (i == (length - 1) && count == 0) {
				contentColumn4.setColspan(5);
			}
			final HtmlOutputText contentText4 = createOutputtext(obj[4] == null ? "—" : obj[4]);
			// 6.组件关联
			contentColumn4.getChildren().add(contentText4);
			contentRow.getChildren().add(contentColumn1);
			contentRow.getChildren().add(contentColumn2);
			contentRow.getChildren().add(contentColumn3);
			contentRow.getChildren().add(contentColumn4);
			count++;
			// 如果该行已有两条数据记录，则将该行添加到表格中，并创建一个新行
			if (count == 2) {
				panelGrid.getChildren().add(contentRow);
				isBlank = false;
				count = 0;
			}
		}
		// 添加最后一行的数据记录
		if (count == 1) {
			panelGrid.getChildren().add(contentRow);
			isBlank = false;
		}
		return isBlank;
	}
	/**
	 * 设置化验结果列的样式和值
	 * 
	 * @param contentColumn
	 * @param contentText
	 * @param obj
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private void setPanelgridValue(Column contentColumn, HtmlOutputText contentText, Object[] obj) {
		// 定量且结果偏低或偏高则结果加标志，并设置颜色
		Integer result = null;
		if (null != obj[2]) {
			result = Integer.valueOf(obj[2].toString());
		}
		if (null != obj[1]) {
			if (null != result) {
				if (result == 2) {
					// 如果偏高，则添加向上箭头，并设置颜色为红色
					contentColumn.setStyle("text-align: left;color: red");
					String string = obj[1].toString() + "↑";
					contentText.setValue(string);
				} else if (result == 1) {
					// 如果偏低，则添加向下箭头，并设置颜色为蓝色
					contentColumn.setStyle("text-align: left;color: blue");
					String string = obj[1].toString() + "↓";
					contentText.setValue(string);
				} else {
					// 否则，原样显示
					contentColumn.setStyle("text-align: left;");
					contentText.setValue(obj[1]);
				}
			} else {
				contentColumn.setStyle("text-align: left;");
				contentText.setValue(obj[1]);
			}
		} else {
			// 内容为空，则显示为"—"
			contentColumn.setStyle("text-align: left;");
			contentText.setValue("—");
		}
	}
    
    @Override
    public void saveAction() {

    }

	/**
	 * <p>方法描述：清空选择的年龄龄</p>
	 * @MethodAuthor rcj,2020年11月17日,clearSelectItem
	 * */
	public void clearSelectItem() {
		this.selectItemName = null;
		this.selectItems = null;
		this.selectItemIds = null;
		List<TreeNode> children = this.itemSortTree.getChildren();
		if (null!=children && children.size()>0) {
			for (TreeNode node : children) {
				node.setSelected(false);
				List<TreeNode> children2 = node.getChildren();
				if (null!=children2 && children2.size()>0) {
					for (TreeNode node2 : children2) {
						node2.setSelected(false);
					}
				}
			}
		}
	}

	/**
	 * <p>方法描述：处理选择的项目</p>
	 * @MethodAuthor rcj,2020年11月17日,hideItemAction
	 * */
	public void hideItemAction() {
		// 遍历选择的危害因素，获取选择的id与名称
		this.selectItemName = null;
		this.selectItemIds = null;
		if (this.selectItems != null && selectItems.length > 0) {
			StringBuffer sb = new StringBuffer();
			StringBuilder nameSb = new StringBuilder(); // 分类名称
			for (TreeNode treeNode : selectItems) {
				TdZwyjDangerItems detail = (TdZwyjDangerItems) treeNode.getData();
				nameSb.append("，").append(
						String.valueOf(detail.getFkByItemId().getItemName()));
				sb.append(",").append(detail.getFkByItemId().getRid());
			}
			this.selectItemIds =sb.substring(1);
			this.selectItemName = nameSb.toString().substring(1);
		}
	}




    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

	public String getSearchUnitId() {
		return searchUnitId;
	}

	public void setSearchUnitId(String searchUnitId) {
		this.searchUnitId = searchUnitId;
	}

	public String getSearchUnitName() {
		return searchUnitName;
	}

	public void setSearchUnitName(String searchUnitName) {
		this.searchUnitName = searchUnitName;
	}

	public String getSearchCrptZoneCode() {
        return searchCrptZoneCode;
    }

    public void setSearchCrptZoneCode(String searchCrptZoneCode) {
        this.searchCrptZoneCode = searchCrptZoneCode;
    }

    public String getSearchCrptZoneName() {
        return searchCrptZoneName;
    }

    public void setSearchCrptZoneName(String searchCrptZoneName) {
        this.searchCrptZoneName = searchCrptZoneName;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchPersonName() {
        return searchPersonName;
    }

    public void setSearchPersonName(String searchPersonName) {
        this.searchPersonName = searchPersonName;
    }


	public OutputPanel getArchivePanel() {
		return archivePanel;
	}

	public void setArchivePanel(OutputPanel archivePanel) {
		this.archivePanel = archivePanel;
	}

	public SessionData getSessionData() {
		return sessionData;
	}

	public void setSessionData(SessionData sessionData) {
		this.sessionData = sessionData;
	}

	public CommServiceImpl getCommService() {
		return commService;
	}

	public void setCommService(CommServiceImpl commService) {
		this.commService = commService;
	}


	public Integer getBhkId() {
		return bhkId;
	}

	public void setBhkId(Integer bhkId) {
		this.bhkId = bhkId;
	}

	public TdZwyjDangerBhk getTdZwyjDangerBhk() {
		return tdZwyjDangerBhk;
	}

	public void setTdZwyjDangerBhk(TdZwyjDangerBhk tdZwyjDangerBhk) {
		this.tdZwyjDangerBhk = tdZwyjDangerBhk;
	}

	public TdTjBhk getTdTjBhkShow() {
		return tdTjBhkShow;
	}

	public void setTdTjBhkShow(TdTjBhk tdTjBhkShow) {
		this.tdTjBhkShow = tdTjBhkShow;
	}

	public TdTjExmsdata getTdTjExmsdata() {
		return tdTjExmsdata;
	}

	public void setTdTjExmsdata(TdTjExmsdata tdTjExmsdata) {
		this.tdTjExmsdata = tdTjExmsdata;
	}

	public List<TdTjEmhistory> getTdTjEmhistoryList() {
		return tdTjEmhistoryList;
	}

	public void setTdTjEmhistoryList(List<TdTjEmhistory> tdTjEmhistoryList) {
		this.tdTjEmhistoryList = tdTjEmhistoryList;
	}

	public List<TdTjEmhistory> getTdTjEmhistoryList2() {
		return tdTjEmhistoryList2;
	}

	public void setTdTjEmhistoryList2(List<TdTjEmhistory> tdTjEmhistoryList2) {
		this.tdTjEmhistoryList2 = tdTjEmhistoryList2;
	}

	public String getTzParam() {
		return tzParam;
	}

	public void setTzParam(String tzParam) {
		this.tzParam = tzParam;
	}

	public String getTsParam() {
		return tsParam;
	}

	public void setTsParam(String tsParam) {
		this.tsParam = tsParam;
	}

	public String getHyParam() {
		return hyParam;
	}

	public void setHyParam(String hyParam) {
		this.hyParam = hyParam;
	}

	public List<TsSimpleCode> getOnGuardList() {
		return onGuardList;
	}

	public void setOnGuardList(List<TsSimpleCode> onGuardList) {
		this.onGuardList = onGuardList;
	}

	public String getSelectOnGuardNames() {
		return selectOnGuardNames;
	}

	public void setSelectOnGuardNames(String selectOnGuardNames) {
		this.selectOnGuardNames = selectOnGuardNames;
	}

	public String getSelectOnGuardIds() {
		return selectOnGuardIds;
	}

	public void setSelectOnGuardIds(String selectOnGuardIds) {
		this.selectOnGuardIds = selectOnGuardIds;
	}

	public List<TsSimpleCode> getBadRsnList() {
		return badRsnList;
	}

	public void setBadRsnList(List<TsSimpleCode> badRsnList) {
		this.badRsnList = badRsnList;
	}

	public String getSelectBadRsnNames() {
		return selectBadRsnNames;
	}

	public void setSelectBadRsnNames(String selectBadRsnNames) {
		this.selectBadRsnNames = selectBadRsnNames;
	}

	public String getSelectBadRsnIds() {
		return selectBadRsnIds;
	}

	public void setSelectBadRsnIds(String selectBadRsnIds) {
		this.selectBadRsnIds = selectBadRsnIds;
	}

	public String getSearchPersonIdc() {
		return searchPersonIdc;
	}

	public void setSearchPersonIdc(String searchPersonIdc) {
		this.searchPersonIdc = searchPersonIdc;
	}

	public Date getSearchBhkBdate() {
		return searchBhkBdate;
	}

	public void setSearchBhkBdate(Date searchBhkBdate) {
		this.searchBhkBdate = searchBhkBdate;
	}

	public Date getSearchBhkEdate() {
		return searchBhkEdate;
	}

	public void setSearchBhkEdate(Date searchBhkEdate) {
		this.searchBhkEdate = searchBhkEdate;
	}

	public String[] getConcluStates() {
		return concluStates;
	}

	public void setConcluStates(String[] concluStates) {
		this.concluStates = concluStates;
	}

	public HethBaseCommServiceImpl getBaseServiceImpl() {
		return baseServiceImpl;
	}

	public void setBaseServiceImpl(HethBaseCommServiceImpl baseServiceImpl) {
		this.baseServiceImpl = baseServiceImpl;
	}

	public ZwReportCardCommServiceImpl getCardServiceImpl() {
		return cardServiceImpl;
	}

	public void setCardServiceImpl(ZwReportCardCommServiceImpl cardServiceImpl) {
		this.cardServiceImpl = cardServiceImpl;
	}

	public HethStaQueryCommServiceImpl getHethStaQueryServiceImpl() {
		return hethStaQueryServiceImpl;
	}

	public void setHethStaQueryServiceImpl(HethStaQueryCommServiceImpl hethStaQueryServiceImpl) {
		this.hethStaQueryServiceImpl = hethStaQueryServiceImpl;
	}

	public SystemModuleServiceImpl getSystemModuleService() {
		return systemModuleService;
	}

	public void setSystemModuleService(SystemModuleServiceImpl systemModuleService) {
		this.systemModuleService = systemModuleService;
	}

	public boolean isIfBhkInfoExist() {
		return ifBhkInfoExist;
	}

	public void setIfBhkInfoExist(boolean ifBhkInfoExist) {
		this.ifBhkInfoExist = ifBhkInfoExist;
	}

    public TdTjBhkInfoBean getTjBhkInfoBean() {
        return tjBhkInfoBean;
    }

    public void setTjBhkInfoBean(TdTjBhkInfoBean tjBhkInfoBean) {
        this.tjBhkInfoBean = tjBhkInfoBean;
    }

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public TreeNode getItemSortTree() {
		return itemSortTree;
	}

	public void setItemSortTree(TreeNode itemSortTree) {
		this.itemSortTree = itemSortTree;
	}

	public TreeNode[] getSelectItems() {
		return selectItems;
	}

	public void setSelectItems(TreeNode[] selectItems) {
		this.selectItems = selectItems;
	}

	public String getSelectItemName() {
		return selectItemName;
	}

	public void setSelectItemName(String selectItemName) {
		this.selectItemName = selectItemName;
	}

	public String getSelectItemIds() {
		return selectItemIds;
	}

	public void setSelectItemIds(String selectItemIds) {
		this.selectItemIds = selectItemIds;
	}
}
