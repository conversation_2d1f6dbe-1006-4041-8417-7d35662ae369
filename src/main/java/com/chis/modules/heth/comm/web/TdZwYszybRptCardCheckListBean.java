package com.chis.modules.heth.comm.web;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TdZwBgkFlow;
import com.chis.modules.heth.comm.entity.TdZwYszybTchBadrsn;
import org.primefaces.context.RequestContext;

import com.chis.modules.heth.comm.entity.TdZwYszybRpt;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import org.springframework.util.CollectionUtils;

/**
 * @Description : 疑似职业病报告卡审核-查询
 * @ClassAuthor : anjing
 * @Date : 2019/9/16 17:47
 **/
@ManagedBean(name = "tdZwYszybRptCardCheckListBean")
@ViewScoped
public class TdZwYszybRptCardCheckListBean extends AbstractReportCardCheckNewBean implements IProcessData {

    /**查询条件：检查机构地区集合*/
    private List<TsZone> zoneList;
    /**查询条件：用工单位地区名称*/
    private String searchZoneName;
    /**查询条件：用工单位地区编码*/
    private String searchZoneCode;
    /**查询条件：用工单位名称*/
    private String searchCrptName;
    /**查询条件：社会信用代码*/
    private String searchCreditCode;
    /**查询条件：证件号码*/
    private String searchIdc;
    /**查询条件：疑似职业病名称*/
    private String searchdiseName;
    /**查询条件：人员姓名*/
    private String searchPersonnelName;
    /**查询条件：接收日期查询-开始日期*/
    private Date searchRcvBdate;
    /**查询条件：接收日期查询-结束日期*/
    private Date searchRcvEdate;
    /**出生日期是否脱敏*/
    private boolean birthIfShow;

    private TdZwYszybRpt tdZwYszybRpt;
    //是否化学
    private boolean ifChemical;

    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    public TdZwYszybRptCardCheckListBean() {
        this.initParam();
        this.ifSQL = true;
        this.searchAction();
    }

    @Override
    public Integer getCardType() {
        return 2;
    }

    /**
     * @Description : 查询参数初始化
     * @MethodAuthor: anjing
     * @Date : 2019/8/27 8:49
     **/
    private void initParam() {
    	TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
		if (null==tsZone) {
			tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
		}
        this.searchZoneCode = tsZone.getZoneCode();
        this.searchZoneName = tsZone.getZoneName();
        this.zoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "");
    }

    /**
     * @Description : 执行查询
     * @MethodAuthor: anjing
     * @Date : 2019/8/27 8:49
     **/
    @Override
    public void searchAction() {
        if(DateUtils.isDateAfter(searchRcvBdate, searchRcvEdate)){
            JsfUtil.addErrorMessage("结束日期大于等于开始日期！");
            return;
        }
        super.searchAction();
    }

    @Override
    public String[] buildHqls() {
        String rcvDateFieldName;
        if (this.zoneType > 3) {
            rcvDateFieldName = "COUNTY_RCV_DATE";
        } else if (this.zoneType < 3) {
            rcvDateFieldName = "PRO_RCV_DATE";
        } else if ("3".equals(this.checkLevel)) {
            rcvDateFieldName = "CITY_RCV_DATE";
        } else {
            rcvDateFieldName = "PRO_RCV_DATE";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT T.RID, T4.ZONE_GB, CASE WHEN T4.ZONE_TYPE > 2 THEN SUBSTR( T4.FULL_NAME, INSTR( T4.FULL_NAME, '_' ) + 1 ) ELSE T4.FULL_NAME END ZONE_NAME, " +
                "T.EMP_CRPT_NAME, T.PERSONNEL_NAME, T.IDC, T2.CODE_NAME as YSZYB_NAME,T1.UNITNAME as ORG_NAME");
        sb.append(", T5.").append(rcvDateFieldName).append(" AS RCV_DATE");
        sb.append(", T5.STATE, 1 AS LIMIT_DAY, 1 AS IFCHECK");
        sb.append(" FROM TD_ZW_YSZYB_RPT T ");
        sb.append(" LEFT JOIN TS_SIMPLE_CODE T2 on T.OCC_DISEID=T2.RID ");
        sb.append(" LEFT JOIN TS_UNIT T1 ON T.RPT_UNIT_ID = T1.RID ");
        sb.append(" LEFT JOIN TS_ZONE T4 ON T.EMP_ZONE_ID = T4.RID ");
        sb.append(" LEFT JOIN TD_ZW_BGK_LAST_STA T5 ON T5.BUS_ID = T.RID ");
        sb.append(" WHERE NVL(T.DEL_MARK, 0) = 0 ");
        sb.append(" AND T5.CART_TYPE = 2 ");
        // 用工单位地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sb.append(" AND T4.ZONE_GB LIKE :zonecode escape '\\\'");
            this.paramMap.put("zonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sb.append(" AND T.EMP_CRPT_NAME LIKE :crptName escape '\\\'");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        if (StringUtils.isNotBlank(this.searchPersonnelName)) {
            sb.append(" AND T.PERSONNEL_NAME LIKE :personnelName escape '\\\'");
            this.paramMap.put("personnelName", "%" + StringUtils.convertBFH(this.searchPersonnelName.trim()) + "%");
        }
        if (null != this.searchRcvBdate) {
            sb.append(" AND T5.").append(rcvDateFieldName).append(" >= TO_DATE('").append(DateUtils.formatDate(this.searchRcvBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchRcvEdate) {
            sb.append(" AND T5.").append(rcvDateFieldName).append(" <= TO_DATE('").append(DateUtils.formatDate(this.searchRcvEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if (ObjectUtil.isNotEmpty(this.states)) {
            this.paramMap.put("stateList", Arrays.asList(this.states));
            sb.append(" AND T5.STATE IN (:stateList)");
        }
        if (StringUtils.isNotBlank(searchCreditCode)) {
        	sb.append(" AND T.EMP_CREDIT_CODE =:searchCreditCode");
            this.paramMap.put("searchCreditCode", StringUtils.convertBFH(searchCreditCode.trim()));
		}
        if (StringUtils.isNotBlank(searchIdc)) {
        	sb.append(" AND T.IDC =:searchIdc");
            this.paramMap.put("searchIdc", StringUtils.convertBFH(searchIdc.trim()));
		}
        if (StringUtils.isNotBlank(searchdiseName)) {
        	sb.append(" AND T2.CODE_NAME LIKE :searchdiseName escape '\\\'");
            this.paramMap.put("searchdiseName", "%"+StringUtils.convertBFH(searchdiseName.trim())+"%");
		}
        String h2 = "SELECT COUNT(*) FROM (" + sb.toString() + ")";
        String h1 = "SELECT * FROM (" + sb.toString() + ")AA ORDER BY AA.ZONE_GB, AA.EMP_CRPT_NAME, AA.RCV_DATE, AA.PERSONNEL_NAME";
        return new String[] { h1, h2 };
    }

    @Override
    public void processData(List<?> list) {
        try {
            if (StringUtils.isBlank(limitTime) || CollectionUtils.isEmpty(list)) {
                return;
            }
            boolean checkLevel2 = "2".equals(this.checkLevel);
            boolean zoneType2 = new Integer(2).equals(this.zoneType);
            boolean zoneType3 = new Integer(3).equals(this.zoneType);
            List<Object[]> result = CollectionUtil.castList(Object[].class, list);
            for (Object[] obj : result) {
                //证件号码脱敏
                obj[5] = StringUtils.encryptIdc(StringUtils.objectToString(obj[5]));
                Integer state = ObjectUtil.convert(Integer.class, obj[9], null);
                obj[11] = "0";
                if (state == null) {
                    continue;
                }
                Integer calLimitTime = null;
                if (obj[8] != null) {
                    calLimitTime = calLimitTime(DateUtils.parseDate(obj[8].toString(), "yyyy-MM-dd"));
                }
                if (zoneType2) {
                    // 省级
                    if (state == 5) {
                        obj[10] = calLimitTime;
                        obj[11] = "1";
                    }
                } else if (zoneType3) {
                    // 市级
                    if (state == 3 || (checkLevel2 && state == 5)) {
                        obj[10] = calLimitTime;
                        obj[11] = "1";
                    }
                } else {
                    //区县级
                    if (state == 1) {
                        obj[10] = calLimitTime;
                        obj[11] = "1";
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {
        this.modInit();
    }

    @Override
    public void modInit() {
        this.tdZwYszybRpt =this.cardCheckServiceImpl.findYszybRpt(this.rid);
        if(!CollectionUtils.isEmpty(tdZwYszybRpt.getBadrsnList())){
            StringBuilder s = new StringBuilder();
            for(TdZwYszybTchBadrsn tchBadrsn : tdZwYszybRpt.getBadrsnList()){
                s.append("，").append(tchBadrsn.getFkByBadrsnId().getCodeName());
            }
            tdZwYszybRpt.setTchBadrsns(s.substring(1,s.length()));
        }
        String ifCityDirect = this.tdZwYszybRpt.getFkByEmpZoneId().getIfCityDirect();
        String ifProvDirectStr = this.tdZwYszybRpt.getFkByEmpZoneId().getIfProvDirect();
        this.ifCityDirect = "1".equals(ifCityDirect);
        //审核意见
        initFlow();
        boolean platVersion1 = "1".equals(this.platVersion);
        pakAuditTypeStr(this.ifCityDirect, "1".equals(ifProvDirectStr), platVersion1);
        this.fillCheckResult();
        ifChemical = false;
        if(null != tdZwYszybRpt.getFkByOccDiseid()){
        	if("1".equals(tdZwYszybRpt.getFkByOccDiseid().getExtendS1())){
                ifChemical = true;
            }

        }
    }

    /**
     * <p>方法描述：赋值checkResult
     * 审核与详情页需要
     * </p>
     * pw 2024/4/9
     **/
    private void fillCheckResult(){
        this.checkResult = "";
        if (ifCheck) {
            this.checkResult = "1";
            return;
        }
        if (CollectionUtils.isEmpty(this.bgkFlows)) {
            return;
        }
        for (TdZwBgkFlow bgkFlow : this.bgkFlows) {
            Integer operFlag = bgkFlow.getOperFlag();
            if (null == operFlag) {
                continue;
            }
            boolean ifPass = (this.level == 3 && 42 == operFlag) ||
                    (this.level == 1 && (("3".equals(this.checkLevel) && 31 == operFlag) || ("2".equals(this.checkLevel) && 43 == operFlag))) ||
                    (this.level == 2 && 41 == operFlag);
            if (ifPass) {
                this.checkResult = "1";
                return;
            }

            ifPass = (this.level == 3 && 32 == operFlag) || (this.level == 1 && 11 == operFlag) ||
                    (this.level == 2 && (13 == operFlag || 22 == operFlag));
            if (ifPass) {
                this.checkResult = "0";
                return;
            }
        }
    }

    /**
     * 生成审核类型（二级审核&省直属）
     *
     * @param ifCityDirect 是否市直属
     * @param ifProvDirect 是否省直属
     * @param platVersion1 是否市级平台
     */
    private void pakAuditTypeStr(boolean ifCityDirect, boolean ifProvDirect, boolean platVersion1) {
        for (TdZwBgkFlow bgkFlow : this.getBgkFlows()) {
            Integer operFlag = bgkFlow.getOperFlag();
            String auditTypeStr = "";
            if ("3".equals(this.checkLevel)) {
                if (ifCityDirect) {
                    auditTypeStr = getAuditTypeStrByCheckLevel3AndCityDirect(operFlag);
                } else {
                    auditTypeStr = getAuditTypeStrByCheckLevel3AndNotCityDirect(operFlag);
                }
            } else if ("2".equals(this.checkLevel)) {
                auditTypeStr = getAuditTypeStrByCheckLevel2AndProvDirect(operFlag, platVersion1, ifProvDirect);
            }
            bgkFlow.setAuditTypeStr(auditTypeStr);
        }
    }

    /**
     * 获取审核类型（二级审核&省直属）
     *
     * @param operFlag     操作类型
     * @param platVersion1 是否市级平台
     * @param ifProvDirect 是否省直属
     * @return 审核类型
     */
    private String getAuditTypeStrByCheckLevel2AndProvDirect(Integer operFlag, boolean platVersion1, boolean ifProvDirect) {
        if (operFlag == 42) {
            return platVersion1 ? "市级审核通过" : "省级审核通过";
        } else if (operFlag == 32) {
            return platVersion1 ? "市级审核退回" : "省级审核退回";
        }
        if (ifProvDirect) {
            return "";
        }
        if (operFlag == 43) {
            return "区县级审核通过";
        } else if (operFlag == 11) {
            return "区县级审核退回";
        }
        return "";
    }

    /**
     * 获取审核类型（三级审核&市直属）
     *
     * @param operFlag 操作类型
     * @return 审核类型
     */
    private String getAuditTypeStrByCheckLevel3AndCityDirect(Integer operFlag) {
        switch (operFlag) {
            case 41:
                return "市级审核通过";
            case 13:
                return "市级审核退回";
            case 42:
                return "省级审核通过";
            case 32:
                return "省级审核退回";
            default:
                return "";
        }
    }

    /**
     * 获取审核类型（三级审核&非市直属）
     *
     * @param operFlag 操作类型
     * @return 审核类型
     */
    private String getAuditTypeStrByCheckLevel3AndNotCityDirect(Integer operFlag) {
        switch (operFlag) {
            case 31:
                return "区县级审核通过";
            case 11:
                return "区县级审核退回";
            case 41:
                return "市级审核通过";
            case 22:
                return "市级审核退回";
            case 42:
                return "省级审核通过";
            case 32:
                return "省级审核退回";
            default:
                return "";
        }
    }

    /**
     *  <p>方法描述：提交</p>
     * @MethodAuthor hsj 2022-06-25 14:37
     */
    @Override
    public void saveAction() {
    	boolean flag = false;
    	if (StringUtils.isBlank(this.checkResult)) {
			JsfUtil.addErrorMessage("审核结果不能为空！");
			flag = true;
		}
    	if (veryCheckInfo()) {
    		flag = true;
		}
    	if (flag) {
    		return;
		}
    	if ("1".equals(this.checkResult)) {
            //审核通过
    		this.reviewHolidaysAction();
		}else {//退回
			this.returnAction(this.tdZwYszybRpt.getFkByEmpZoneId());
		}
    }

    /**
     * <p>方法描述：附件查看</p>
     *
     * @MethodAuthor rcj, 2019年9月5日, showReportAction
     */
    public void toAnnexView() {
        if (StringUtils.isBlank(tdZwYszybRpt.getAnnexPath())) {
            JsfUtil.addErrorMessage("附件已经被删除，请重新上传！");
            return;
        }
        RequestContext.getCurrentInstance().execute(
                "window.open('" + FileUtils.previewFile(tdZwYszybRpt.getAnnexPath(),
                        "") + "')");
    }

    /**
     * @Description : 检查机构地区清空
     * @MethodAuthor: anjing
     * @Date : 2019/9/16 13:50
     **/
    public void clearSearchZone() {
        this.searchZoneCode = null;
        this.searchZoneName = null;
    }
    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchPersonnelName() {
        return searchPersonnelName;
    }

    public void setSearchPersonnelName(String searchPersonnelName) {
        this.searchPersonnelName = searchPersonnelName;
    }

    public Date getSearchRcvBdate() {
        return searchRcvBdate;
    }

    public void setSearchRcvBdate(Date searchRcvBdate) {
        this.searchRcvBdate = searchRcvBdate;
    }

    public Date getSearchRcvEdate() {
        return searchRcvEdate;
    }

    public void setSearchRcvEdate(Date searchRcvEdate) {
        this.searchRcvEdate = searchRcvEdate;
    }

    public void setTdZwYszybRpt(TdZwYszybRpt tdZwYszybRpt) {
        this.tdZwYszybRpt = tdZwYszybRpt;
    }

    public TdZwYszybRpt getTdZwYszybRpt() {
        return tdZwYszybRpt;
    }

	public String getSearchCreditCode() {
		return searchCreditCode;
	}

	public void setSearchCreditCode(String searchCreditCode) {
		this.searchCreditCode = searchCreditCode;
	}

	public String getSearchdiseName() {
		return searchdiseName;
	}

	public void setSearchdiseName(String searchdiseName) {
		this.searchdiseName = searchdiseName;
	}

	public String getSearchIdc() {
		return searchIdc;
	}

	public void setSearchIdc(String searchIdc) {
		this.searchIdc = searchIdc;
	}

	public boolean isIfChemical() {
		return ifChemical;
	}

	public void setIfChemical(boolean ifChemical) {
		this.ifChemical = ifChemical;
	}

    public boolean isBirthIfShow() {
        return birthIfShow;
    }

    public void setBirthIfShow(boolean birthIfShow) {
        this.birthIfShow = birthIfShow;
    }
}
