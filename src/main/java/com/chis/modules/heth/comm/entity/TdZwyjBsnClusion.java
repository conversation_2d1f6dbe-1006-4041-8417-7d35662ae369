package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-10-29
 */
@Entity
@Table(name = "TD_ZWYJ_BSN_CLUSION")
@SequenceGenerator(name = "TdZwyjBsnClusion", sequenceName = "TD_ZWYJ_BSN_CLUSION_SEQ", allocationSize = 1)
public class TdZwyjBsnClusion implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String badrsns;
	private TsSimpleCode fkByOnguardStateid;
	private String itemDesc;
	private Integer deterWay;
	private Integer xh;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjBsnClusion() {
	}

	public TdZwyjBsnClusion(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjBsnClusion")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "BADRSNS")	
	public String getBadrsns() {
		return badrsns;
	}

	public void setBadrsns(String badrsns) {
		this.badrsns = badrsns;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ONGUARD_STATEID")			
	public TsSimpleCode getFkByOnguardStateid() {
		return fkByOnguardStateid;
	}

	public void setFkByOnguardStateid(TsSimpleCode fkByOnguardStateid) {
		this.fkByOnguardStateid = fkByOnguardStateid;
	}	
			
	@Column(name = "ITEM_DESC")	
	public String getItemDesc() {
		return itemDesc;
	}

	public void setItemDesc(String itemDesc) {
		this.itemDesc = itemDesc;
	}	
			
	@Column(name = "DETER_WAY")	
	public Integer getDeterWay() {
		return deterWay;
	}

	public void setDeterWay(Integer deterWay) {
		this.deterWay = deterWay;
	}	
			
	@Column(name = "XH")	
	public Integer getXh() {
		return xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}