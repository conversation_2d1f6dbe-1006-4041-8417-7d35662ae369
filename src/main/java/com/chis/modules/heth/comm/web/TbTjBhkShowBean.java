package com.chis.modules.heth.comm.web;

import javax.faces.bean.ManagedBean;

import org.primefaces.component.outputpanel.OutputPanel;

import com.chis.common.utils.JsfUtil;
import com.chis.modules.heth.comm.javabean.TdTjBhkInfoBean;


@ManagedBean(name="tbTjBhkShowBean")
public class TbTjBhkShowBean {
	private TdTjBhkInfoBean tjBhkInfoBean;
	private OutputPanel archivePanel = (OutputPanel) JsfUtil.getApplication().createComponent(
			OutputPanel.COMPONENT_TYPE);
	private Integer pageParam;
	
	public TbTjBhkShowBean() {
		pageParam = Integer.parseInt(JsfUtil.getRequest().getParameter("bhkrid"));
		tjBhkInfoBean = new TdTjBhkInfoBean();
		tjBhkInfoBean.setRid(pageParam);
		tjBhkInfoBean.setArchivePanel(archivePanel);
		tjBhkInfoBean.setIfManagedOrg(true);
		tjBhkInfoBean.initBhkInfo();
		
		
	}
	public TdTjBhkInfoBean getTjBhkInfoBean() {
		return tjBhkInfoBean;
	}

	public void setTjBhkInfoBean(TdTjBhkInfoBean tjBhkInfoBean) {
		this.tjBhkInfoBean = tjBhkInfoBean;
	}
	public OutputPanel getArchivePanel() {
		return archivePanel;
	}
	public void setArchivePanel(OutputPanel archivePanel) {
		this.archivePanel = archivePanel;
	}
	public Integer getPageParam() {
		return pageParam;
	}
	public void setPageParam(Integer pageParam) {
		this.pageParam = pageParam;
	}
}
