package com.chis.modules.heth.comm.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * 职业健康监护档案上传记录
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置<br/>
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 */
@Entity
@Table(name = "TD_ZWTJ_UPLOADLOG")
@SequenceGenerator(name = "TdZwtjUploadlog_Seq", sequenceName = "TD_ZWTJ_UPLOADLOG_SEQ", allocationSize = 1)
public class TdZwtjUploadlog implements java.io.Serializable {

    private static final long serialVersionUID = -3540647283150296178L;
    private Integer rid;
    private TbTjSrvorg tbTjSrvorg;
    private Integer succCount;
    private Integer failCount;
    private Date uploadDate;
    private Date createDate;
    private Integer createManid;

    public TdZwtjUploadlog() {
    }

    /** full constructor */
    public TdZwtjUploadlog(Integer rid, TbTjSrvorg tbTjSrvorg,
                           Integer succCount, Integer failCount, Date uploadDate,
                           Date createDate, Integer createManid) {
        this.rid = rid;
        this.tbTjSrvorg = tbTjSrvorg;
        this.succCount = succCount;
        this.failCount = failCount;
        this.uploadDate = uploadDate;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwtjUploadlog_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "UPLOAD_UNITID" )
    public TbTjSrvorg getTbTjSrvorg() {
        return this.tbTjSrvorg;
    }

    public void setTbTjSrvorg(TbTjSrvorg tbTjSrvorg) {
        this.tbTjSrvorg = tbTjSrvorg;
    }

    @Column(name = "SUCC_COUNT" )
    public Integer getSuccCount() {
        return this.succCount;
    }

    public void setSuccCount(Integer succCount) {
        this.succCount = succCount;
    }

    @Column(name = "FAIL_COUNT" )
    public Integer getFailCount() {
        return this.failCount;
    }

    public void setFailCount(Integer failCount) {
        this.failCount = failCount;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPLOAD_DATE" , length = 7)
    public Date getUploadDate() {
        return this.uploadDate;
    }

    public void setUploadDate(Date uploadDate) {
        this.uploadDate = uploadDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }
}
