package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdZwBgkFlow;
import com.chis.modules.heth.comm.entity.TdZwBgkLastSta;
import com.chis.modules.heth.comm.entity.TdZwHethChkSmaryComm;
import com.chis.modules.heth.comm.entity.TdZwjdArchivesCard;
import com.chis.modules.heth.comm.service.TdZwHethAppraisalRptCardServiceImpl;
import com.chis.modules.heth.comm.service.TdZwHethChkSmaryServiceImpl;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>类描述： 职业病鉴定报告卡审核-基类</p>
 *
 * @ClassAuthor: yzz
 * @date： 2021年11月09日
 **/
public abstract class AbstractArchivesCardCheckListBean extends FacesEditBean implements IProcessData {

    protected CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    protected SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    protected TdZwHethChkSmaryServiceImpl chkSmaryService = SpringContextHolder.getBean(TdZwHethChkSmaryServiceImpl.class);
    protected TdZwHethAppraisalRptCardServiceImpl appraisalRptCardService = SpringContextHolder.getBean(TdZwHethAppraisalRptCardServiceImpl.class);
    /**记录id*/
    protected Integer rid;
    /** 选择的结果集 */
    protected List<Object[]> selectEntitys;
    /**审核等级*/
    protected String checkLevel;
    /** 审核期限 */
    protected String limitTime;
    /**默认审核意见*/
    protected String defaultAuditAdv;
    /**批量审核提示信息*/
    protected String tipInfo;
    /**审核级别*/
    protected Integer level;
    /**地区级别*/
    protected Integer zoneType;
    /**地区*/
    protected TsZone tsZone;
    /**查询条件：选中状态*/
    protected String[] states;
    /**查询条件：状态*/
    protected List<SelectItem> stateList = new ArrayList<>();
    /**不同审核级别的全部状态*/
    protected String allStates;
    /**审核结果*/
    protected String checkResult;
    /**报告卡最新状态*/
    protected TdZwBgkLastSta newFlow = new TdZwBgkLastSta();
    /**是否0审核，1详情*/
    protected Integer ifIsCheckout;
    /**编辑页面*/
    protected TdZwjdArchivesCard archivesCard = new TdZwjdArchivesCard();
    /**职业病鉴定结果是否显示 默认显示*/
    protected Boolean ifAgainRst = Boolean.TRUE;
    /**市级平台/省级平台*/
    protected String platVersion;

    public AbstractArchivesCardCheckListBean() {
        tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        if(null==tsZone.getRealZoneType()){
            this.zoneType = tsZone.getZoneType().intValue();
        }else {
            this.zoneType = tsZone.getRealZoneType().intValue();
        }
        /*审核等级*/
        checkLevel=PropertyUtils.getValueWithoutException("checkLevel");
        /*审核期限*/
        limitTime = PropertyUtils.getValueWithoutException("limitTime");
        /**批量审核默认审核意见*/
        defaultAuditAdv = PropertyUtils.getValueWithoutException("defaultAuditAdv");
        /*批量审核提示信息*/
        this.tipInfo = "批量审核：若存在审核意见为空的数据则默认置为【"+defaultAuditAdv+"】；审核人默认为当前登录人。";
        //市级平台/省级平台
        platVersion=PropertyUtils.getValueWithoutException("platVersion");
        initState();
    }

    /**
     * <p>方法描述：初始化状态</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-21
     **/
    public void initState(){
        level = 0;//1：初审2：复审3：终审
        if("2".equals(checkLevel)){//2级审核
            if (zoneType <= 3) {//终审
                level = 3;
            }else {//初审
                level = 1;
            }
        }else if ("3".equals(checkLevel)) {//3级审核
            if (zoneType == 2) {//终审
                level = 3;
            }else if (zoneType == 3){//复审
                level = 2;
            }else {//初审
                level = 1;
            }
        }
        if("3".equals(checkLevel)){
            stateList.add(new SelectItem("0","待提交") );
            stateList.add(new SelectItem("1","区县级待审") );
            stateList.add(new SelectItem("2","区县级退回") );
            stateList.add(new SelectItem("3","市级待审") );
            stateList.add(new SelectItem("4","市级退回") );
            stateList.add(new SelectItem("5","省级待审") );
            stateList.add(new SelectItem("6","省级退回") );
            stateList.add(new SelectItem("7","省级通过") );
            if(this.zoneType==4){
                states=new String[]{"1"};
            }else if(this.zoneType==3){
                states=new String[]{"3"};
            }else if(this.zoneType==2){
                states=new String[]{"5"};
            }
        }else if("2".equals(checkLevel)){
            if("1".equals(platVersion)){
                stateList.add(new SelectItem("0","待提交") );
                stateList.add(new SelectItem("1","区县级待审") );
                stateList.add(new SelectItem("2","区县级退回") );
                stateList.add(new SelectItem("5","市级待审") );
                stateList.add(new SelectItem("6","市级退回") );
                stateList.add(new SelectItem("7","市级通过") );
                if(this.zoneType==4){
                    states=new String[]{"1"};
                }else if(this.zoneType==3 || this.zoneType==2){
                    states=new String[]{"5"};
                }
            }else if("2".equals(platVersion)){
                stateList.add(new SelectItem("0","待提交") );
                stateList.add(new SelectItem("1","区县级待审") );
                stateList.add(new SelectItem("2","区县级退回") );
                stateList.add(new SelectItem("5","省级待审") );
                stateList.add(new SelectItem("6","省级退回") );
                stateList.add(new SelectItem("7","省级通过") );
                if(this.zoneType==4){
                    states=new String[]{"1"};
                }else if(this.zoneType==2 || this.zoneType==3){
                    states=new String[]{"5"};
                }
            }
        }
    }


    /**
     * <p>方法描述：批量审核</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-22
     **/
    public void openReviewConfirmDialog() {
        if(this.selectEntitys == null || this.selectEntitys.size()==0){
            JsfUtil.addErrorMessage("请选择需要审批的数据！");
            return ;
        }
        RequestContext.getCurrentInstance().execute("PF('ReviewConfirmDialog').show()");
    }

    /**
     * <p>方法描述：批量审核</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-22
     **/
    public void reviewBatchAction() {

        for(Object[] obj : this.selectEntitys) {
            TdZwBgkLastSta tdZwBgkLastSta= chkSmaryService.findTdZwBgkLastSta(Integer.valueOf(obj[0].toString()),getCartType());
            // 报告卡审批流程
            TdZwBgkFlow tdZwBgkFlow=new TdZwBgkFlow();
            tdZwBgkFlow.setRcvDate(new Date());
            tdZwBgkFlow.setFkBySmtPsnId(new TsUserInfo(Global.getUser().getRid()));
            tdZwBgkFlow.setAuditAdv(defaultAuditAdv);
            tdZwBgkFlow.setAuditMan(Global.getUser().getUsername());
            tdZwBgkFlow.setBusId(Integer.valueOf(obj[0].toString()));
            tdZwBgkFlow.setCartType(getCartType());
            if(tdZwBgkLastSta!=null){
                if("2".equals(checkLevel)){//2级审核
                    if("1".equals(obj[12].toString())){
                        //报告卡最新状态
                        tdZwBgkLastSta.setState(5);
                        tdZwBgkLastSta.setCountyChkPsn(Global.getUser().getUsername());
                        tdZwBgkLastSta.setCountAuditAdv(defaultAuditAdv);
                        tdZwBgkLastSta.setProRcvDate(new Date());
                        //报告卡审批流程  状态
                        tdZwBgkFlow.setOperFlag(43);

                    }else if("5".equals(obj[12].toString())){
                        tdZwBgkLastSta.setState(7);
                        tdZwBgkLastSta.setProChkPsn(Global.getUser().getUsername());
                        tdZwBgkLastSta.setProAuditAdv(defaultAuditAdv);
                        //报告卡审批流程  状态
                        tdZwBgkFlow.setOperFlag(42);
                    }
                }else if ("3".equals(checkLevel)) {//3级审核
                    if("1".equals(obj[12].toString())){
                        tdZwBgkLastSta.setState(3);
                        tdZwBgkLastSta.setCountyChkPsn(Global.getUser().getUsername());
                        tdZwBgkLastSta.setCountAuditAdv(defaultAuditAdv);
                        tdZwBgkLastSta.setCityRcvDate(new Date());
                        //报告卡审批流程  状态
                        tdZwBgkFlow.setOperFlag(31);

                    }else if("3".equals(obj[12].toString())){
                        tdZwBgkLastSta.setState(5);
                        tdZwBgkLastSta.setCityChkPsn(Global.getUser().getUsername());
                        tdZwBgkLastSta.setCityAuditAdv(defaultAuditAdv);
                        tdZwBgkLastSta.setProRcvDate(new Date());
                        //报告卡审批流程  状态
                        tdZwBgkFlow.setOperFlag(41);
                    }else if("5".equals(obj[12].toString())){
                        tdZwBgkLastSta.setState(7);
                        tdZwBgkLastSta.setProChkPsn(Global.getUser().getUsername());
                        tdZwBgkLastSta.setProAuditAdv(defaultAuditAdv);
                        //报告卡审批流程  状态
                        tdZwBgkFlow.setOperFlag(42);
                        tdZwBgkLastSta.setProSmtDate(new Date());
                    }
                }
                chkSmaryService.updateInsLastRecordTwo(tdZwBgkLastSta,tdZwBgkFlow,limitTime,obj[17]!=null?Integer.parseInt(obj[17].toString()):null);
            }
        }
        JsfUtil.addSuccessMessage("审核成功！");
        this.searchAction();
    }




    /**
     *  <p>方法描述：提交审核验证</p>
     * @MethodAuthor hsj
     */
    public boolean veryData() {
        boolean flag = false;
        if(StringUtils.isBlank(checkResult)){
            JsfUtil.addErrorMessage("审核结果不能为空！");
            flag = true;
        }
        if(3 == level ){
            if(StringUtils.isBlank(newFlow.getProAuditAdv())){
                JsfUtil.addErrorMessage("审核意见不能为空！");
                flag = true;
            }else if(newFlow.getProAuditAdv().length()>100){
                JsfUtil.addErrorMessage("审核意见长度不能超过100！");
                flag = true;
            }
            if(StringUtils.isBlank(newFlow.getProChkPsn())){
                JsfUtil.addErrorMessage("审核人不能为空！");
                flag = true;
            }

        }
        if(2 == level  ){
            if( StringUtils.isBlank(newFlow.getCityAuditAdv())){
                JsfUtil.addErrorMessage("审核意见不能为空！");
                flag = true;
            }else if(newFlow.getCityAuditAdv().length()>100){
                JsfUtil.addErrorMessage("审核意见长度不能超过100！");
                flag = true;
            }
            if(StringUtils.isBlank(newFlow.getCityChkPsn())){
                JsfUtil.addErrorMessage("审核人不能为空！");
                flag = true;
            }
        }
        if(1 == level ){
            if( StringUtils.isBlank(newFlow.getCountAuditAdv())){
                JsfUtil.addErrorMessage("审核意见不能为空！");
                flag = true;
            }else if(newFlow.getCountAuditAdv().length()>100){
                JsfUtil.addErrorMessage("审核意见长度不能超过100！");
                flag = true;
            }
            if(StringUtils.isBlank(newFlow.getCountyChkPsn())){
                JsfUtil.addErrorMessage("审核人不能为空！");
                flag = true;
            }
        }
        return flag;
    }


    /**
     *  <p>方法描述：更新报告卡最新状态</p>
     * @MethodAuthor hsj
     */
    public void saveOrUpdateNewFlowOrBgkFlow() {
        TdZwBgkFlow tdZwBgkFlow=new TdZwBgkFlow();
        tdZwBgkFlow.setCartType(getCartType());
        tdZwBgkFlow.setBusId(rid);
        tdZwBgkFlow.setRcvDate(new Date());
        tdZwBgkFlow.setFkBySmtPsnId(new TsUserInfo(Global.getUser().getRid()));
        TsZone zone=  null;
        //根据“鉴定机构地区”
        if(archivesCard.getFkByJdUnitId()!=null){
            zone=  archivesCard.getFkByJdUnitId().getTsZone();
        }
        if("2".equals(checkLevel)){
            //二级审核
            switch (level){
                case 1:
                    if("1".equals(checkResult)){
                        newFlow.setProRcvDate(new Date());
                        newFlow.setState(5);
                        tdZwBgkFlow.setOperFlag(43);
                    }else {
                        newFlow.setState(2);
                        tdZwBgkFlow.setOperFlag(11);
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getCountAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getCountyChkPsn());
                    break;
                case 3:
                    if("1".equals(checkResult)){
                        newFlow.setState(7);
                        newFlow.setProSmtDate(new Date());
                        tdZwBgkFlow.setOperFlag(42);
                    }else {
                        newFlow.setState(6);
                        tdZwBgkFlow.setOperFlag(32);
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getProAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getProChkPsn());
                    break;
                default:
                    break;
            }

        }else if("3".equals(checkLevel)){
            //三级审核
            switch (level){
                case 1:
                    if("1".equals(checkResult)){
                        newFlow.setCityRcvDate(new Date());
                        newFlow.setState(3);
                        tdZwBgkFlow.setOperFlag(31);
                    }else {
                        newFlow.setState(2);
                        tdZwBgkFlow.setOperFlag(11);
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getCountAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getCountyChkPsn());
                    break;
                case 2:
                    if("1".equals(checkResult)){
                        newFlow.setProRcvDate(new Date());
                        newFlow.setState(5);
                        tdZwBgkFlow.setOperFlag(41);
                    }else {
                        newFlow.setState(4);
                        if (zone!=null&&"1".equals(zone.getIfCityDirect())) {//市直属
                            tdZwBgkFlow.setOperFlag(13);
                        }else{
                            tdZwBgkFlow.setOperFlag(22);
                        }
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getCityAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getCityChkPsn());
                    break;
                case 3:
                    if("1".equals(checkResult)){
                        newFlow.setState(7);
                        newFlow.setProSmtDate(new Date());
                        tdZwBgkFlow.setOperFlag(42);
                    }else {
                        newFlow.setState(6);
                        tdZwBgkFlow.setOperFlag(32);
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getProAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getProChkPsn());
                    break;
                default:
                    break;
            }
        }
        chkSmaryService.updateInsLastRecordTwo(newFlow,tdZwBgkFlow,limitTime,null == archivesCard.getFkByMainId() ? null : archivesCard.getFkByMainId().getRid());
    }



    public abstract Integer getCartType();




    @Override
    public void processData(List<?> list) {

    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    @Override
    public String[] buildHqls() {
        return new String[0];
    }


    public List<Object[]> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<Object[]> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }

    public String getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(String checkLevel) {
        this.checkLevel = checkLevel;
    }

    public String getLimitTime() {
        return limitTime;
    }

    public void setLimitTime(String limitTime) {
        this.limitTime = limitTime;
    }

    public String getDefaultAuditAdv() {
        return defaultAuditAdv;
    }

    public void setDefaultAuditAdv(String defaultAuditAdv) {
        this.defaultAuditAdv = defaultAuditAdv;
    }

    public String getTipInfo() {
        return tipInfo;
    }

    public void setTipInfo(String tipInfo) {
        this.tipInfo = tipInfo;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getZoneType() {
        return zoneType;
    }

    public void setZoneType(Integer zoneType) {
        this.zoneType = zoneType;
    }

    public TsZone getTsZone() {
        return tsZone;
    }

    public void setTsZone(TsZone tsZone) {
        this.tsZone = tsZone;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public String getAllStates() {
        return allStates;
    }

    public void setAllStates(String allStates) {
        this.allStates = allStates;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult;
    }

    public TdZwBgkLastSta getNewFlow() {
        return newFlow;
    }

    public void setNewFlow(TdZwBgkLastSta newFlow) {
        this.newFlow = newFlow;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Integer getIfIsCheckout() {
        return ifIsCheckout;
    }

    public void setIfIsCheckout(Integer ifIsCheckout) {
        this.ifIsCheckout = ifIsCheckout;
    }

    public TdZwjdArchivesCard getArchivesCard() {
        return archivesCard;
    }

    public void setArchivesCard(TdZwjdArchivesCard archivesCard) {
        this.archivesCard = archivesCard;
    }

    public Boolean getIfAgainRst() {
        return ifAgainRst;
    }

    public void setIfAgainRst(Boolean ifAgainRst) {
        this.ifAgainRst = ifAgainRst;
    }

    public String getPlatVersion() {
        return platVersion;
    }

    public void setPlatVersion(String platVersion) {
        this.platVersion = platVersion;
    }
}
