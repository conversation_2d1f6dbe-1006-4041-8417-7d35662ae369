package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-8-25
 */
@Entity
@Table(name = "TD_TJ_BHK_ABNOMAL")
@SequenceGenerator(name = "TdTjBhkAbnomal", sequenceName = "TD_TJ_BHK_ABNOMAL_SEQ", allocationSize = 1)
public class TdTjBhkAbnomal implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer bhkId;
	private Integer type;
	private String abnomalInfo;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdTjBhkAbnomal() {
	}

	public TdTjBhkAbnomal(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjBhkAbnomal")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "BHK_ID")	
	public Integer getBhkId() {
		return bhkId;
	}

	public void setBhkId(Integer bhkId) {
		this.bhkId = bhkId;
	}	
			
	@Column(name = "TYPE")	
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}	
			
	@Column(name = "ABNOMAL_INFO")	
	public String getAbnomalInfo() {
		return abnomalInfo;
	}

	public void setAbnomalInfo(String abnomalInfo) {
		this.abnomalInfo = abnomalInfo;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}