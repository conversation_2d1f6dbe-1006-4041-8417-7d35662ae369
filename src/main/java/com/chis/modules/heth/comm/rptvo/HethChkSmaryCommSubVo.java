package com.chis.modules.heth.comm.rptvo;

import com.chis.modules.system.rptvo.ZwxFieldDoc;

/***
 *  <p>类描述：职业性有害因素检测情况[职业性有害因素监测卡]</p>
 *
 * @ClassAuthor maox,2019年9月5日,HethChkSmaryVo
 * <AUTHOR>
 *
 */
public class HethChkSmaryCommSubVo {
    @ZwxFieldDoc(value = "职业性有害因素")
    private String badrsnName;

    @ZwxFieldDoc(value = "工作场所")
    private String workPlace;

    @ZwxFieldDoc(value = "岗位/工种")
    private String workTypeName;

    @ZwxFieldDoc(value = "浓度（强度）类型")
    private String thickTypeName;

    @ZwxFieldDoc(value = "检测位（最小值）")
    private String jcValMin;

    @ZwxFieldDoc(value = "检测位（最大值）")
    private String jcValMax;

    @ZwxFieldDoc(value = "检测日期")
    private String jcTime;

    @ZwxFieldDoc(value = "合格情况")
    private String hgFlagName;

    public String getBadrsnName() {
        return badrsnName;
    }

    public void setBadrsnName(String badrsnName) {
        this.badrsnName = badrsnName;
    }

    public String getWorkPlace() {
        return workPlace;
    }

    public void setWorkPlace(String workPlace) {
        this.workPlace = workPlace;
    }

    public String getWorkTypeName() {
        return workTypeName;
    }

    public void setWorkTypeName(String workTypeName) {
        this.workTypeName = workTypeName;
    }

    public String getThickTypeName() {
        return thickTypeName;
    }

    public void setThickTypeName(String thickTypeName) {
        this.thickTypeName = thickTypeName;
    }

    public String getJcValMin() {
        return jcValMin;
    }

    public void setJcValMin(String jcValMin) {
        this.jcValMin = jcValMin;
    }

    public String getJcValMax() {
        return jcValMax;
    }

    public void setJcValMax(String jcValMax) {
        this.jcValMax = jcValMax;
    }

    public String getJcTime() {
        return jcTime;
    }

    public void setJcTime(String jcTime) {
        this.jcTime = jcTime;
    }

    public String getHgFlagName() {
        return hgFlagName;
    }

    public void setHgFlagName(String hgFlagName) {
        this.hgFlagName = hgFlagName;
    }
}
