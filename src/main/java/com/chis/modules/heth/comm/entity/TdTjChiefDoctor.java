package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-3-25
 */
@Entity
@Table(name = "TD_TJ_CHIEF_DOCTOR")
@SequenceGenerator(name = "TdTjChiefDoctor", sequenceName = "TD_TJ_CHIEF_DOCTOR_SEQ", allocationSize = 1)
public class TdTjChiefDoctor implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdTjBhkClt fkByBhkId;
	private TdZwPsninfoComm fkByMhkdctId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdTjChiefDoctor() {
	}

	public TdTjChiefDoctor(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjChiefDoctor")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "BHK_ID")			
	public TdTjBhkClt getFkByBhkId() {
		return fkByBhkId;
	}

	public void setFkByBhkId(TdTjBhkClt fkByBhkId) {
		this.fkByBhkId = fkByBhkId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "MHKDCT_ID")			
	public TdZwPsninfoComm getFkByMhkdctId() {
		return fkByMhkdctId;
	}

	public void setFkByMhkdctId(TdZwPsninfoComm fkByMhkdctId) {
		this.fkByMhkdctId = fkByMhkdctId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}