package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-4-20
 */
@Entity
@Table(name = "TD_ZW_JC_CRPT_SET")
@SequenceGenerator(name = "TdZwJcCrptSet", sequenceName = "TD_ZW_JC_CRPT_SET_SEQ", allocationSize = 1)
public class TdZwJcCrptSet implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String creditCode;
	private TsSimpleCode fkByBadrsnId;
	private String workPlace;
	private String workType;
	private Date jcTime;
	private String jcValMax;
	private String jcValMin;
	private TsSimpleCode fkByThickTypeId;
	private String jcUnitName;
	private String jcUnitCharge;
	private Integer hgFlag;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwJcCrptSet() {
	}

	public TdZwJcCrptSet(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwJcCrptSet")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "CREDIT_CODE")	
	public String getCreditCode() {
		return creditCode;
	}

	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BADRSN_ID")			
	public TsSimpleCode getFkByBadrsnId() {
		return fkByBadrsnId;
	}

	public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
		this.fkByBadrsnId = fkByBadrsnId;
	}	
			
	@Column(name = "WORK_PLACE")	
	public String getWorkPlace() {
		return workPlace;
	}

	public void setWorkPlace(String workPlace) {
		this.workPlace = workPlace;
	}	
			
	@Column(name = "WORK_TYPE")	
	public String getWorkType() {
		return workType;
	}

	public void setWorkType(String workType) {
		this.workType = workType;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "JC_TIME")			
	public Date getJcTime() {
		return jcTime;
	}

	public void setJcTime(Date jcTime) {
		this.jcTime = jcTime;
	}	
			
	@Column(name = "JC_VAL_MAX")	
	public String getJcValMax() {
		return jcValMax;
	}

	public void setJcValMax(String jcValMax) {
		this.jcValMax = jcValMax;
	}	
			
	@Column(name = "JC_VAL_MIN")	
	public String getJcValMin() {
		return jcValMin;
	}

	public void setJcValMin(String jcValMin) {
		this.jcValMin = jcValMin;
	}	
			
	@ManyToOne
	@JoinColumn(name = "THICK_TYPE_ID")			
	public TsSimpleCode getFkByThickTypeId() {
		return fkByThickTypeId;
	}

	public void setFkByThickTypeId(TsSimpleCode fkByThickTypeId) {
		this.fkByThickTypeId = fkByThickTypeId;
	}	
			
	@Column(name = "JC_UNIT_NAME")	
	public String getJcUnitName() {
		return jcUnitName;
	}

	public void setJcUnitName(String jcUnitName) {
		this.jcUnitName = jcUnitName;
	}	
			
	@Column(name = "JC_UNIT_CHARGE")	
	public String getJcUnitCharge() {
		return jcUnitCharge;
	}

	public void setJcUnitCharge(String jcUnitCharge) {
		this.jcUnitCharge = jcUnitCharge;
	}	
			
	@Column(name = "HG_FLAG")	
	public Integer getHgFlag() {
		return hgFlag;
	}

	public void setHgFlag(Integer hgFlag) {
		this.hgFlag = hgFlag;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}