package com.chis.modules.heth.comm.service;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TdZwMonthBadrsns;
import com.chis.modules.heth.comm.entity.TdZwMonthBhk;
import com.chis.modules.heth.comm.entity.TdZwMonthCrpt;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

@Service
@Transactional(readOnly = false)
public class RoutineMonitoringSummarizeService extends AbstractTemplate {

    /**
     * 根据系统单位id获取体检机构id
     *
     * @param unitId 系统单位id
     * @return Integer 体检机构id
     */
    @Transactional(readOnly = true)
    public Integer findBhkOrgRidByUnitRid(Integer unitId) {
        String sql = "SELECT S.RID FROM TB_TJ_SRVORG S WHERE S.REG_ORGID = " + unitId;
        List<BigDecimal> resultList = CollectionUtil.castList(BigDecimal.class, this.findDataBySqlNoPage(sql, null));
        if (resultList.isEmpty()) {
            return null;
        }
        return ObjectUtil.convert(Integer.class, resultList.get(0));
    }

    /**
     * 查找同体检机构同报告出具日期结束日期填报数
     *
     * @param bhkOrgId 体检机构id
     * @param endDate  报告出具日期结束日期
     * @return Integer 填报数
     */
    @Transactional(readOnly = true)
    public Integer findMonthBhkCountByEndDate(Integer bhkOrgId, Date endDate) {
        if (bhkOrgId == null || endDate == null) {
            return 0;
        }
        String endDateStr = DateUtils.formatDate(endDate, "yyyy-MM-dd");
        String sql = "SELECT COUNT(1) FROM TD_ZW_MONTH_BHK B WHERE NVL(B.DEL_MARK, 0) = 0 AND B.ORG_ID = " + bhkOrgId +
                " AND B.END_BHK_DATE = TO_DATE('" + endDateStr + "', 'yyyy-MM-dd') ";
        return this.findCountBySql(sql);
    }

    /**
     * 查找体检机构在报告出具周期内的体检用工单位
     *
     * @param bhkOrgId  体检机构id
     * @param startDate 报告出具日期开始日期
     * @param endDate   报告出具日期结束日期
     * @param crptId    用工单位ID
     * @return List 用工单位
     */
    @Transactional(readOnly = true)
    public List<Integer> findBhkEntrustCrptByBhkOrgCycle(Integer bhkOrgId, Date startDate, Date endDate,
                                                         Integer crptId) {
        if (bhkOrgId == null || startDate == null || endDate == null) {
            return new ArrayList<>();
        }
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "WITH BARRSNS AS (SELECT DISTINCT ADS.ANALY_ITEM_ID " +
                "                 FROM TD_ZDZYB_ANALY_TYPE T " +
                "                          INNER JOIN TD_ZDZYB_ANALY_DETAIL AD ON T.RID = AD.MAIN_ID " +
                "                          INNER JOIN TD_ZDZYB_ANALY_DETAIL_SUB ADS ON AD.RID = ADS.MAIN_ID" +
                "                 WHERE T.BUS_TYPE = 1 AND T.ANALY_TYPE = 4) " +
                "SELECT DISTINCT B.ENTRUST_CRPT_ID " +
                "FROM TD_TJ_BHK B " +
                "WHERE B.JC_TYPE = 1 " +
                "  AND B.BHKORG_ID = :bhkOrgId ";
        if (crptId != null) {
            sql += "  AND B.ENTRUST_CRPT_ID = :crptId ";
            paramMap.put("crptId", crptId);
        }
        sql += "  AND B.RPT_PRINT_DATE >= TO_DATE(:startDateStr, 'yyyy-MM-dd') " +
                "  AND B.RPT_PRINT_DATE <= TO_DATE(:endDateStr, 'yyyy-MM-dd') " +
                "  AND EXISTS(SELECT 1 " +
                "             FROM TD_TJ_BADRSNS BR " +
                "                      INNER JOIN BARRSNS BRS ON BR.BADRSN_ID = BRS.ANALY_ITEM_ID " +
                "             WHERE B.RID = BR.BHK_ID)";
        paramMap.put("bhkOrgId", bhkOrgId);
        String startDateStr = DateUtils.formatDate(startDate, "yyyy-MM-dd");
        paramMap.put("startDateStr", startDateStr);
        String endDateStr = DateUtils.formatDate(endDate, "yyyy-MM-dd");
        paramMap.put("endDateStr", endDateStr);
        List<BigDecimal> bigDecimalList = CollectionUtil.castList(BigDecimal.class, this.findDataBySqlNoPage(sql, paramMap));
        List<Integer> dataList = new ArrayList<>();
        for (BigDecimal bigDecimal : bigDecimalList) {
            Integer data = ObjectUtil.convert(Integer.class, bigDecimal);
            if (data == null) {
                continue;
            }
            dataList.add(data);
        }
        return dataList;
    }

    /**
     * 根据填报rid查询用人单位
     *
     * @param rid 填报rid
     * @return List 用人单位
     */
    @Transactional(readOnly = true)
    public List<TdZwMonthCrpt> findCrptByBhkRid(Integer rid) {
        if (rid == null) {
            return new ArrayList<>();
        }
        List<TdZwMonthCrpt> crptList = this.findEntityListByMainId(TdZwMonthCrpt.class, rid);
        for (TdZwMonthCrpt crpt : crptList) {
            if (new Integer(0).equals(crpt.getState())) {
                crpt.setBadrsnsList(this.findEntityListByMainId(TdZwMonthBadrsns.class, crpt.getRid()));
            }
        }
        return crptList;
    }

    /**
     * 根据体检机构、用工单位、周期汇总
     *
     * @param bhkOrgId  体检机构Id
     * @param crptId    用工单位Id
     * @param startDate 周期开始日期
     * @param endDate   周期结束日期
     * @return List 汇总数据
     */
    @Transactional(readOnly = true)
    public List<Object[]> findSummarizeDataByCrptIdAndBhkOrgCycle(Integer bhkOrgId, Integer crptId,
                                                                  Date startDate, Date endDate) {
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "WITH BARRSNS1 AS (SELECT AD.ANALY_ITEM_ID AS ANALY_ITEM_ID1, ADS.ANALY_ITEM_ID AS ANALY_ITEM_ID2 " +
                "                  FROM TD_ZDZYB_ANALY_TYPE T " +
                "                           INNER JOIN TD_ZDZYB_ANALY_DETAIL AD ON T.RID = AD.MAIN_ID " +
                "                           INNER JOIN TD_ZDZYB_ANALY_DETAIL_SUB ADS ON AD.RID = ADS.MAIN_ID" +
                "                  WHERE T.BUS_TYPE = 1 AND T.ANALY_TYPE = 4), " +
                "     BHK_DATA1 AS (SELECT B.ENTRUST_CRPT_ID, " +
                "                          BRS.ANALY_ITEM_ID1, " +
                "                          B.PERSON_ID, " +
                "                          SUM(NVL(SC1.EXTENDS2, 0)) AS YSZYB, " +
                "                          SUM(NVL(SC2.EXTENDS2, 0)) AS ZYJJZ " +
                "                   FROM TD_TJ_BHK B " +
                "                            INNER JOIN TD_TJ_BADRSNS BR ON B.RID = BR.BHK_ID " +
                "                            INNER JOIN BARRSNS1 BRS ON BR.BADRSN_ID = BRS.ANALY_ITEM_ID2 " +
                "                            LEFT JOIN TS_SIMPLE_CODE SC1 ON BR.EXAM_CONCLUSION_ID = SC1.RID AND SC1.EXTENDS2 = 5 " +
                "                            LEFT JOIN TS_SIMPLE_CODE SC2 ON BR.EXAM_CONCLUSION_ID = SC2.RID AND SC2.EXTENDS2 = 4 " +
                "                   WHERE B.JC_TYPE = 1 " +
                "                     AND B.BHKORG_ID = :bhkOrgId ";
        if (crptId != null) {
            sql += "                     AND B.ENTRUST_CRPT_ID = :crptId ";
            paramMap.put("crptId", crptId);
        }
        sql += "                     AND B.RPT_PRINT_DATE >= TO_DATE(:startDateStr, 'YYYY-MM-DD') " +
                "                     AND B.RPT_PRINT_DATE <= TO_DATE(:endDateStr, 'YYYY-MM-DD') " +
                "                     AND EXISTS(SELECT 1 " +
                "                                FROM TD_TJ_BADRSNS BR " +
                "                                         LEFT JOIN BARRSNS1 BRS ON BR.BADRSN_ID = BRS.ANALY_ITEM_ID2 " +
                "                                WHERE B.RID = BR.BHK_ID) " +
                "                   GROUP BY B.ENTRUST_CRPT_ID, BRS.ANALY_ITEM_ID1, B.PERSON_ID), " +
                "     BHK_DATA2 AS (SELECT ENTRUST_CRPT_ID, ANALY_ITEM_ID1, COUNT(1) AS NUM " +
                "                   FROM BHK_DATA1 " +
                "                   GROUP BY ENTRUST_CRPT_ID, ANALY_ITEM_ID1), " +
                "     BHK_DATA3 AS (SELECT ENTRUST_CRPT_ID, ANALY_ITEM_ID1, COUNT(1) AS NUM " +
                "                   FROM BHK_DATA1 " +
                "                   WHERE YSZYB > 0 " +
                "                   GROUP BY ENTRUST_CRPT_ID, ANALY_ITEM_ID1), " +
                "     BHK_DATA4 AS (SELECT ENTRUST_CRPT_ID, ANALY_ITEM_ID1, COUNT(1) AS NUM " +
                "                   FROM BHK_DATA1 " +
                "                   WHERE ZYJJZ > 0 " +
                "                   GROUP BY ENTRUST_CRPT_ID, ANALY_ITEM_ID1) " +
                "SELECT B2.ENTRUST_CRPT_ID, SC.RID, NVL(B2.NUM, 0) AS PSN_NUM, NVL(B3.NUM, 0) AS YSZYB_NUM, NVL(B4.NUM, 0) AS ZYJJZ_NUM " +
                "FROM TS_SIMPLE_CODE SC " +
                "    INNER JOIN TS_CODE_TYPE CT ON SC.CODE_TYPE_ID = CT.RID AND CT.CODE_TYPE_NAME = '5547' " +
                "    LEFT JOIN BHK_DATA2 B2 ON SC.RID = B2.ANALY_ITEM_ID1 " +
                "    LEFT JOIN BHK_DATA3 B3 ON SC.RID = B3.ANALY_ITEM_ID1 AND B2.ENTRUST_CRPT_ID = B3.ENTRUST_CRPT_ID " +
                "    LEFT JOIN BHK_DATA4 B4 ON SC.RID = B4.ANALY_ITEM_ID1 AND B2.ENTRUST_CRPT_ID = B4.ENTRUST_CRPT_ID " +
                "WHERE SC.IF_REVEAL = 1 ";
        paramMap.put("bhkOrgId", bhkOrgId);
        String startDateStr = DateUtils.formatDate(startDate, "yyyy-MM-dd");
        paramMap.put("startDateStr", startDateStr);
        String endDateStr = DateUtils.formatDate(endDate, "yyyy-MM-dd");
        paramMap.put("endDateStr", endDateStr);
        return CollectionUtil.castList(Object[].class, this.findDataBySqlNoPage(sql, paramMap));
    }

    /**
     * 存储所有汇总数据，包括更新填报主表、插入或更新或删除用人单位表及汇总表
     *
     * @param analyBadRsnMap        统计危害因素映射
     * @param monthBhk              填报主表
     * @param needFillMonthCrptList 用人单位表及汇总表
     * @param delCrptList           需要删除的用人单位表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAllSummarizeData(Map<Integer, TsSimpleCode> analyBadRsnMap,
                                     TdZwMonthBhk monthBhk,
                                     List<TdZwMonthCrpt> needFillMonthCrptList,
                                     List<Integer> delCrptList) {
        List<TdZwMonthCrpt> needFillMonthCrptInsertList = new ArrayList<>();
        List<TdZwMonthCrpt> needFillMonthCrptUpdateList = new ArrayList<>();
        for (TdZwMonthCrpt crpt : needFillMonthCrptList) {
            this.preEntity(crpt);
            if (crpt.getRid() == null) {
                needFillMonthCrptInsertList.add(crpt);
            } else {
                needFillMonthCrptUpdateList.add(crpt);
            }
        }
        if (ObjectUtil.isNotEmpty(needFillMonthCrptInsertList)) {
            this.saveBatchObjs(needFillMonthCrptInsertList);
        }
        if (ObjectUtil.isNotEmpty(needFillMonthCrptUpdateList)) {
            this.updateBatchObjs(needFillMonthCrptUpdateList);
        }
        String sql = "SELECT RID, CRPT_ID FROM TD_ZW_MONTH_CRPT WHERE MAIN_ID = " + monthBhk.getRid();
        List<Object[]> crptList = CollectionUtil.castList(Object[].class, this.findDataBySqlNoPage(sql, null));
        Map<Integer, Integer> crptIdMap = new HashMap<>();
        for (Object[] crpt : crptList) {
            Integer rid = ObjectUtil.convert(Integer.class, crpt[0]);
            Integer crptId = ObjectUtil.convert(Integer.class, crpt[1]);
            crptIdMap.put(crptId, rid);
        }
        List<TdZwMonthBadrsns> insertBadrsnsList = new ArrayList<>();
        List<TdZwMonthBadrsns> updateBadrsnsList = new ArrayList<>();
        List<Integer> deleteBadrsnsRidList = new ArrayList<>();
        for (TdZwMonthCrpt crpt : needFillMonthCrptList) {
            if (crpt.getRid() == null) {
                crpt.setRid(crptIdMap.get(crpt.getFkByCrptId().getRid()));
            }
            for (TdZwMonthBadrsns badrsns : crpt.getBadrsnsList()) {
                this.preEntity(badrsns);
                if (!analyBadRsnMap.containsKey(badrsns.getFkByBadrsnId().getRid())) {
                    deleteBadrsnsRidList.add(badrsns.getRid());
                }
                if (badrsns.getRid() == null) {
                    insertBadrsnsList.add(badrsns);
                } else {
                    updateBadrsnsList.add(badrsns);
                }
            }
        }
        if (ObjectUtil.isNotEmpty(insertBadrsnsList)) {
            this.saveBatchObjs(insertBadrsnsList);
        }
        if (ObjectUtil.isNotEmpty(updateBadrsnsList)) {
            this.updateBatchObjs(updateBadrsnsList);
        }
        delCrptByRid(deleteBadrsnsRidList, 3);
        delCrptByRid(delCrptList, 2);
        updateCrptNum(monthBhk.getRid());
    }

    /**
     * 删除用人单位、用人单位汇总数据，更新填报用人单位数
     *
     * @param rid     填报rid
     * @param crptRid 用人单位rid
     */
    public void delCrptAndUpdateCrptNumByRid(Integer rid, Integer crptRid) {
        List<Integer> crptRidList = new ArrayList<>();
        crptRidList.add(crptRid);
        delCrptByRid(crptRidList, 2);
        updateCrptNum(rid);
    }

    /**
     * 删除用人单位、用人单位汇总数据
     *
     * @param delCrptList 用人单位rid集合
     * @param type        <pre>1: 仅删除用人单位汇总数据</pre><pre>2: 无数据的未填报的用人单位</pre><pre>3: 多余的汇总数据</pre>
     */
    public void delCrptByRid(List<Integer> delCrptList, int type) {
        List<List<Integer>> lists = StringUtils.splitListProxy(delCrptList, 999);
        if (ObjectUtil.isEmpty(lists)) {
            return;
        }
        String badrsnsField = "MAIN_ID";
        if (type == 3) {
            badrsnsField = "RID";
        }
        String sql1 = "DELETE TD_ZW_MONTH_BADRSNS WHERE " + badrsnsField + " IN (:delCrptList)";
        String sql2 = "DELETE TD_ZW_MONTH_CRPT WHERE RID IN (:delCrptList)";
        for (List<Integer> list : lists) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("delCrptList", list);
            this.executeSql(sql1, paramMap);
            if (type != 2) {
                continue;
            }
            this.executeSql(sql2, paramMap);
        }
    }

    /**
     * 更新填报用人单位数
     *
     * @param rid 填报rid
     */
    public void updateCrptNum(Integer rid) {
        String sql = "SELECT COUNT(1) FROM TD_ZW_MONTH_CRPT C WHERE MAIN_ID = " + rid;
        int count1 = this.findCountBySql(sql);
        sql = "SELECT COUNT(1) FROM TD_ZW_MONTH_CRPT C WHERE STATE = 1 AND MAIN_ID = " + rid;
        int count2 = this.findCountBySql(sql);
        sql = "UPDATE TD_ZW_MONTH_BHK SET CRPT_NUM = :count1, CRPT_SUBMIT_NUM = :count2, MODIFY_DATE = :modifyDate, MODIFY_MANID = :modifyManid WHERE RID = :rid";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("count1", count1);
        paramMap.put("count2", count2);
        paramMap.put("modifyDate", new Date());
        paramMap.put("modifyManid", Global.getUser().getRid());
        paramMap.put("rid", rid);
        this.executeSql(sql, paramMap);
    }

    /**
     * 标删填报主表
     *
     * @param rid 主表RID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delTdZwMonthBhk(Integer rid) {
        String sql = "UPDATE TD_ZW_MONTH_BHK SET DEL_MARK = 1, MODIFY_DATE = :modifyDate, MODIFY_MANID = :modifyManid WHERE RID = :rid";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("modifyDate", new Date());
        paramMap.put("modifyManid", Global.getUser().getRid());
        paramMap.put("rid", rid);
        this.executeSql(sql, paramMap);
    }

    /**
    * <p>Description：通过主表rid查询所有用人单位 </p>
    * <p>Author： yzz 2024-08-09 </p>
    */
    public List<Object[]> findMonthCrptByMainId(Integer rid,String crptName,String[] status) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", rid);

        StringBuffer sql = new StringBuffer();
        sql.append(" select T.RID,T1.CRPT_NAME,T.STATE,T.CRPT_ID ");
        sql.append(" from TD_ZW_MONTH_CRPT T ");
        sql.append(" INNER JOIN TB_TJ_CRPT T1 on T.CRPT_ID=T1.RID ");
        sql.append(" LEFT  JOIN TS_ZONE T2 on T1.ZONE_ID = T2.RID ");
        sql.append(" where T.MAIN_ID= :rid");

        if (StringUtils.isNotBlank(crptName)) {
            sql.append(" and T1.CRPT_NAME like :crptName ");
            paramMap.put("crptName", "%"+crptName+"%");
        }
        if (status != null && status.length > 0) {
            List<Integer> statusList = new ArrayList<>();
            for (String s : status) {
                statusList.add(Integer.parseInt(s));
            }
            sql.append(" and T.STATE in (:status) ");
            paramMap.put("status", statusList);
        }
        sql.append(" order by T2.ZONE_GB,T1.CRPT_NAME ");
        return this.findDataBySqlNoPage(sql.toString(), paramMap);
    }

    /**
    * <p>Description：根据用工单位rid获取各危害因素统计信息 </p>
    * <p>Author： yzz 2024-08-09 </p>
    */
    public List<Object[]> findBadrsnByMainId(int rid) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select T.rid, ");
        sql.append("         T1.CODE_NAME as BADRSN_NAME, ");
        sql.append("         T.HOLD_CARD_NUM, ");
        sql.append("         T.BHK_NUM, ");
        sql.append("         T.SUSPECTED_NUM, ");
        sql.append("         T.CONTRAINDLIST_NUM, ");
        sql.append("         T.SYS_BHK_NUM, ");
        sql.append("         T.SYS_SUSPECTED_NUM, ");
        sql.append("         T.SYS_CONTRAINDLIST_NUM, ");
        sql.append("         T1.RID as BADRSNS_ID ");
        sql.append(" from TD_ZW_MONTH_BADRSNS T ");
        sql.append(" inner join TS_SIMPLE_CODE T1 on T.BADRSN_ID = T1.RID ");
        sql.append(" where T1.IF_REVEAL=1 and T.MAIN_ID = ").append(rid);
        sql.append(" order by T1.NUM,T1.CODE_NO ");
        return this.findDataBySqlNoPage(sql.toString(), null);
    }
    /**
    * <p>Description：用人单位各危害因素汇总保存  ifRequired：是否验证必填</p>
    * <p>Author： yzz 2024-08-12 </p>
    */
    public void saveMonthBadrsn(List<Object[]> monthBadrsnList,Integer rid,Integer mainRid,boolean ifTip) {
        if (CollectionUtils.isEmpty(monthBadrsnList)) {
            return;
        }
        StringBuilder sql;
        // 保存各危害因素汇总数据
        for (Object[] obj : monthBadrsnList) {
            sql = new StringBuilder();
            sql.append(" update TD_ZW_MONTH_BADRSNS set ");
            sql.append(" HOLD_CARD_NUM= ").append("".equals(obj[2]) || obj[2]==null ? "null " : obj[2]);
            sql.append(",BHK_NUM=").append("".equals(obj[3]) || obj[3] == null ? "null " : obj[3]);
            sql.append(",SUSPECTED_NUM=").append("".equals(obj[4]) || obj[4] == null ? "null " : obj[4]);
            sql.append(",CONTRAINDLIST_NUM=").append("".equals(obj[5]) || obj[5] == null ? "null " : obj[5]);
            sql.append(",SYS_BHK_NUM=").append("".equals(obj[6]) || obj[6] == null ? "null " : obj[6]);
            sql.append(",SYS_SUSPECTED_NUM=").append("".equals(obj[7]) || obj[7] == null ? "null " : obj[7]);
            sql.append(",SYS_CONTRAINDLIST_NUM=").append("".equals(obj[8]) || obj[8] == null ? "null " : obj[8]);
            sql.append(",MODIFY_DATE=sysdate");
            sql.append(",MODIFY_MANID=").append(Global.getUser().getRid());
            sql.append(" where RID=").append(obj[0]);
            this.executeSql(sql.toString(), null);
        }
        // 行切换只更新数据记录
        if (!ifTip) {
            return;
        }
        // 更新用人单位状态为已填报
        sql = new StringBuilder();
        sql.append(" update TD_ZW_MONTH_CRPT set STATE=1 where RID=").append(rid);
        this.executeSql(sql.toString(), null);

        // 更新主表用人单位数和用人单位已填报数
        updateCrptNum(mainRid);
    }

    /**
    * <p>Description：更新主表状态 </p>
    * <p>Author： yzz 2024-08-12 </p>
    */
    public void updateMainState(Integer rid, Integer statue) {
        this.executeSql(" update TD_ZW_MONTH_BHK set STATE= " + statue + " where rid= " + rid, null);
    }


    /**
    * <p>Description：导出数据查询 </p>
    * <p>Author： yzz 2024-08-13 </p>
    */
    public List<Object[]> findExportDataList(Integer rid, String crptName, String[] status){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", rid);

        StringBuilder sql = new StringBuilder();
        sql.append(" select  T3.CRPT_NAME, ");
        sql.append("         T1.CODE_NAME as BADRSN_NAME, ");
        sql.append("         T.HOLD_CARD_NUM, ");
        sql.append("         T.BHK_NUM, ");
        sql.append("         T.SUSPECTED_NUM, ");
        sql.append("         T.CONTRAINDLIST_NUM ");
        sql.append(" from TD_ZW_MONTH_BADRSNS T ");
        sql.append(" inner join TS_SIMPLE_CODE T1 on T.BADRSN_ID = T1.RID ");
        sql.append(" inner join TD_ZW_MONTH_CRPT T2 on T.MAIN_ID=T2.RID ");
        sql.append(" inner join TB_TJ_CRPT T3 on T2.CRPT_ID=T3.RID ");
        sql.append(" inner join TS_ZONE T4 on T3.ZONE_ID=T4.RID ");
        sql.append(" where T1.IF_REVEAL=1 and T2.MAIN_ID = :rid ");
        if (StringUtils.isNotBlank(crptName)) {
            sql.append(" and T3.CRPT_NAME like :crptName ");
            paramMap.put("crptName", "%"+crptName+"%");
        }
        if (status != null && status.length > 0) {
            List<Integer> statusList = new ArrayList<>();
            for (String s : status) {
                statusList.add(Integer.parseInt(s));
            }
            sql.append(" and T2.STATE in (:status) ");
            paramMap.put("status", statusList);
        }
        sql.append(" order by T4.ZONE_GB,T3.CRPT_NAME,T1.NUM, T1.CODE_NO ");
        return this.findDataBySqlNoPage(sql.toString(), paramMap);
    }

    /**
    * <p>Description：提交 </p>
    * <p>Author： yzz 2024-09-12 </p>
    */
    public void submitMonthBhk(Integer rid,List<Object[]> monthBadrsnList, int unitRid, Integer bhkRid, boolean bool) {
        //更新主表状态
        updateMainState(rid,1);
        //保存当前单位
        saveMonthBadrsn(monthBadrsnList,unitRid,bhkRid,bool);
    }

    /**
     * <p>方法描述： 查询当前报告出具周期省级或市级是否已经汇总 </p>
     * pw 2024/9/12
     **/
    @Transactional(readOnly = true)
    public Integer checkIfCollectByManageOrg(String zoneGb, Date start, Date end) {
        if (StringUtils.isBlank(zoneGb) || null == start || null == end) {
            return null;
        }
        StringBuffer buffer = new StringBuffer();
        buffer.append(" SELECT COUNT(1) FROM TD_ZW_MONTH_BHK_PROV T ")
                .append(" INNER JOIN TS_UNIT T1 ON T.ORG_ID = T1.RID ")
                .append(" INNER JOIN TS_ZONE T2 ON T1.MANAGE_ZONE_ID = T2.RID ")
                .append(" WHERE NVL(T.DEL_MARK,0)=0 ")
                .append(" AND T.STATE=1 ")
                .append(" AND T.START_BHK_DATE>=TO_DATE('").append(DateUtils.formatDate(start)).append("','YYYY-MM-DD') ")
                .append(" AND T.END_BHK_DATE<=TO_DATE('").append(DateUtils.formatDate(end)).append("','YYYY-MM-DD') ")
                .append(" AND (  ")
                .append("       T2.ZONE_GB = '").append(ZoneUtil.getParentCode(zoneGb, 3)).append("' ")
                .append("       OR")
                .append("       T2.ZONE_GB = '").append(ZoneUtil.getParentCode(zoneGb, 4)).append("' ")
                .append("      )");
        return this.findCountBySql(buffer.toString(), null);
    }
}
