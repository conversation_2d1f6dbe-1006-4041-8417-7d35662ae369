package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-10-29
 */
@Entity
@Table(name = "TD_ZWYJ_BSN_ADV_CSION")
@SequenceGenerator(name = "TdZwyjBsnAdvCsion", sequenceName = "TD_ZWYJ_BSN_ADV_CSION_SEQ", allocationSize = 1)
public class TdZwyjBsnAdvCsion implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwyjBsnBhk fkByBhkId;
	private TdZwyjBsnCsionItems fkByAdvId;
	private String itemRst;
	private String msrunt;
	private String itemStdvalue;
	private Integer jdgptn;
	private BigDecimal maxval;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjBsnAdvCsion() {
	}

	public TdZwyjBsnAdvCsion(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjBsnAdvCsion")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "BHK_ID")			
	public TdZwyjBsnBhk getFkByBhkId() {
		return fkByBhkId;
	}

	public void setFkByBhkId(TdZwyjBsnBhk fkByBhkId) {
		this.fkByBhkId = fkByBhkId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ADV_ID")			
	public TdZwyjBsnCsionItems getFkByAdvId() {
		return fkByAdvId;
	}

	public void setFkByAdvId(TdZwyjBsnCsionItems fkByAdvId) {
		this.fkByAdvId = fkByAdvId;
	}	
			
	@Column(name = "ITEM_RST")	
	public String getItemRst() {
		return itemRst;
	}

	public void setItemRst(String itemRst) {
		this.itemRst = itemRst;
	}	
			
	@Column(name = "MSRUNT")	
	public String getMsrunt() {
		return msrunt;
	}

	public void setMsrunt(String msrunt) {
		this.msrunt = msrunt;
	}	
			
	@Column(name = "ITEM_STDVALUE")	
	public String getItemStdvalue() {
		return itemStdvalue;
	}

	public void setItemStdvalue(String itemStdvalue) {
		this.itemStdvalue = itemStdvalue;
	}	
			
	@Column(name = "JDGPTN")	
	public Integer getJdgptn() {
		return jdgptn;
	}

	public void setJdgptn(Integer jdgptn) {
		this.jdgptn = jdgptn;
	}	
			
	@Column(name = "MAXVAL")	
	public BigDecimal getMaxval() {
		return maxval;
	}

	public void setMaxval(BigDecimal maxval) {
		this.maxval = maxval;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}