package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * @Description : GBZ188不规范数据上报[职卫报告卡]
 * <AUTHOR>
 * @createTime 2019-10-9
 */
@Entity
@Table(name = "TD_ZW_GBZ188_NOSTD")
@SequenceGenerator(name = "TdZwGbz188Nostd", sequenceName = "TD_ZW_GBZ188_NOSTD_SEQ", allocationSize = 1)
public class TdZwGbz188NostdComm implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdTjBhk fkByMainId;
    private String nostdDesc;
    private Integer chkStdFlag;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    public TdZwGbz188NostdComm() {
    }

    public TdZwGbz188NostdComm(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwGbz188Nostd")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdTjBhk getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdTjBhk fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @Column(name = "NOSTD_DESC")
    public String getNostdDesc() {
        return nostdDesc;
    }

    public void setNostdDesc(String nostdDesc) {
        this.nostdDesc = nostdDesc;
    }

    @Column(name = "CHK_STD_FLAG")
    public Integer getChkStdFlag() {
        return chkStdFlag;
    }

    public void setChkStdFlag(Integer chkStdFlag) {
        this.chkStdFlag = chkStdFlag;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }
}
