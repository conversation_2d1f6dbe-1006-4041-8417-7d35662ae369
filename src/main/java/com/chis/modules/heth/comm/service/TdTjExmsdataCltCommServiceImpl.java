package com.chis.modules.heth.comm.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.modules.heth.comm.entity.TdTjExmsdataClt;
import com.chis.modules.system.service.AbstractTemplate;

/**
 * @Description : 问诊项目（数据录入）-业务层
 * @ClassAuthor : anjing
 * @Date : 2019/5/18 18:49
 **/
@Service
@Transactional(readOnly = true)
public class TdTjExmsdataCltCommServiceImpl extends AbstractTemplate {

    /**
     * @Description : 根据主表Id获取问诊项目信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 18:51
     **/
    public List<TdTjExmsdataClt> selectExmsdataCltByBhkId(Integer bhkId) {
        if (null != bhkId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdTjExmsdataClt t ");
            sb.append(" where t.fkByBhkId.rid = ").append(bhkId);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }
}
