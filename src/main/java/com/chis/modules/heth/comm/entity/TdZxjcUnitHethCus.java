package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-4
 */
@Entity
@Table(name = "TD_ZXJC_UNIT_HETH_CUS")
@SequenceGenerator(name = "TdZxjcUnitHethCus", sequenceName = "TD_ZXJC_UNIT_HETH_CUS_SEQ", allocationSize = 1)
public class TdZxjcUnitHethCus implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZxjcUnitbasicinfo fkByMainId;
	private Integer ifhea;
	private Integer checkTotalPeoples;
	private Integer ifheaDust;
	private Integer ifheaChemistry;
	private Integer ifheaPhysics;
	private Integer ifheaEmit;
	private Integer ifheaBiological;
	private Integer ifheaOther;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZxjcUnitHethCus() {
	}

	public TdZxjcUnitHethCus(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZxjcUnitHethCus")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZxjcUnitbasicinfo getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZxjcUnitbasicinfo fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "IFHEA")	
	public Integer getIfhea() {
		return ifhea;
	}

	public void setIfhea(Integer ifhea) {
		this.ifhea = ifhea;
	}	
			
	@Column(name = "CHECK_TOTAL_PEOPLES")	
	public Integer getCheckTotalPeoples() {
		return checkTotalPeoples;
	}

	public void setCheckTotalPeoples(Integer checkTotalPeoples) {
		this.checkTotalPeoples = checkTotalPeoples;
	}	
			
	@Column(name = "IFHEA_DUST")	
	public Integer getIfheaDust() {
		return ifheaDust;
	}

	public void setIfheaDust(Integer ifheaDust) {
		this.ifheaDust = ifheaDust;
	}	
			
	@Column(name = "IFHEA_CHEMISTRY")	
	public Integer getIfheaChemistry() {
		return ifheaChemistry;
	}

	public void setIfheaChemistry(Integer ifheaChemistry) {
		this.ifheaChemistry = ifheaChemistry;
	}	
			
	@Column(name = "IFHEA_PHYSICS")	
	public Integer getIfheaPhysics() {
		return ifheaPhysics;
	}

	public void setIfheaPhysics(Integer ifheaPhysics) {
		this.ifheaPhysics = ifheaPhysics;
	}	
			
	@Column(name = "IFHEA_EMIT")	
	public Integer getIfheaEmit() {
		return ifheaEmit;
	}

	public void setIfheaEmit(Integer ifheaEmit) {
		this.ifheaEmit = ifheaEmit;
	}	
			
	@Column(name = "IFHEA_BIOLOGICAL")	
	public Integer getIfheaBiological() {
		return ifheaBiological;
	}

	public void setIfheaBiological(Integer ifheaBiological) {
		this.ifheaBiological = ifheaBiological;
	}	
			
	@Column(name = "IFHEA_OTHER")	
	public Integer getIfheaOther() {
		return ifheaOther;
	}

	public void setIfheaOther(Integer ifheaOther) {
		this.ifheaOther = ifheaOther;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}