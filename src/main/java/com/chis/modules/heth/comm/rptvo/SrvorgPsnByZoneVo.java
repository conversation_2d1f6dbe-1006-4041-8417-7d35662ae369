package com.chis.modules.heth.comm.rptvo;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 */
public class SrvorgPsnByZoneVo implements java.io.Serializable {
    private Integer rid;

    private Integer unitRid;
    private String zoneName;
    private Integer zoneId;
    private String unitName;
    private String empName;
    private String idcCard;
    private String mobileNo;
    private String sex;
    private String titleName;
    private Integer titleRid;
    private String titleCentPath;

    public Integer getRid() {
        return rid;
    }
    public void setRid(Integer rid) {
        this.rid = rid;
    }
    public String getEmpName() {
        return empName;
    }
    public void setEmpName(String empName) {
        this.empName = empName;
    }
    public String getSex() {
        return sex;
    }
    public void setSex(String sex) {
        this.sex = sex;
    }
    public String getZoneName() {
        return zoneName;
    }
    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }
    public String getUnitName() {
        return unitName;
    }
    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getZoneId() {
        return zoneId;
    }
    public void setZoneId(Integer zoneId) {
        this.zoneId = zoneId;
    }
    public String getIdcCard() {
        return idcCard;
    }
    public void setIdcCard(String idcCard) {
        this.idcCard = idcCard;
    }
    public String getMobileNo() {
        return mobileNo;
    }
    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }
    public String getTitleName() {
        return titleName;
    }
    public void setTitleName(String titleName) {
        this.titleName = titleName;
    }
    public Integer getTitleRid() {
        return titleRid;
    }
    public void setTitleRid(Integer titleRid) {
        this.titleRid = titleRid;
    }
    public String getTitleCentPath() {
        return titleCentPath;
    }
    public void setTitleCentPath(String titleCentPath) {
        this.titleCentPath = titleCentPath;
    }

    public Integer getUnitRid() {
        return unitRid;
    }
    public void setUnitRid(Integer unitRid) {
        this.unitRid = unitRid;
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SrvorgPsnByZoneVo that = (SrvorgPsnByZoneVo) o;
        return Objects.equals(rid, that.rid);
    }
}
