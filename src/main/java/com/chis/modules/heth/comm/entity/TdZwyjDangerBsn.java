package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-11-17
 */
@Entity
@Table(name = "TD_ZWYJ_DANGER_BSN")
@SequenceGenerator(name = "TdZwyjDangerBsn", sequenceName = "TD_ZWYJ_DANGER_BSN_SEQ", allocationSize = 1)
public class TdZwyjDangerBsn implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwyjDangerBhk fkByBhkId;
	private Integer badrsnId;
	private TsSimpleCode fkByExamConclusionId;
	private String qtjbName;
	private Date createDate;
	private Integer createManid;
	
	public TdZwyjDangerBsn() {
	}

	public TdZwyjDangerBsn(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjDangerBsn")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "BHK_ID")			
	public TdZwyjDangerBhk getFkByBhkId() {
		return fkByBhkId;
	}

	public void setFkByBhkId(TdZwyjDangerBhk fkByBhkId) {
		this.fkByBhkId = fkByBhkId;
	}	
			
	@Column(name = "BADRSN_ID")	
	public Integer getBadrsnId() {
		return badrsnId;
	}

	public void setBadrsnId(Integer badrsnId) {
		this.badrsnId = badrsnId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EXAM_CONCLUSION_ID")			
	public TsSimpleCode getFkByExamConclusionId() {
		return fkByExamConclusionId;
	}

	public void setFkByExamConclusionId(TsSimpleCode fkByExamConclusionId) {
		this.fkByExamConclusionId = fkByExamConclusionId;
	}	
			
	@Column(name = "QTJB_NAME")	
	public String getQtjbName() {
		return qtjbName;
	}

	public void setQtjbName(String qtjbName) {
		this.qtjbName = qtjbName;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
}