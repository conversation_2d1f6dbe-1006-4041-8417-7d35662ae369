package com.chis.modules.heth.comm.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdZwyjBsnBhk;
import com.chis.modules.heth.comm.entity.TdZwyjBsnCheck;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;

/**
 * @Description : 重点危害因素检查情况审核
 * @ClassAuthor : rcj
 * @Date : 2020/10/29 10:19
 **/
@ManagedBean(name = "tdZwZdBadRsnInspectCheckListBean")
@ViewScoped
public class TdZwZdBadRsnInspectCheckListBean extends AbstractZdBadRsnCheckBean implements IProcessData{
	private static final long serialVersionUID = 1L;
	/**查询条件：接收日期查询-开始日期*/
    private Date searchRcvBdate;
    /**查询条件：接收日期查询-结束日期*/
    private Date searchRcvEdate;

	 /**是否显示提示信息*/
	private boolean ifShowTipInfo = false;
	/**显示提示信息*/
	private String tipInfo;
	/**默认审核意见*/
	private String defaultAuditAdv;
	private Boolean ifCheck = true;//是否审核界面
	protected Integer zoneType;//2:省级3：市级4：区县级


	public TdZwZdBadRsnInspectCheckListBean() {
    	/**批量审核默认审核意见*/
    	defaultAuditAdv = PropertyUtils.getValue("zdzybdefaultAuditAdv");
    	// 默认：前日期往前推90天
        this.searchRcvBdate = DateUtils.getYearFirstDay(new Date());
        this.searchRcvEdate = new Date();
        //显示提示信息
		this.tipInfo = "批量审核：若存在审核意见为空的数据则默认置为【"+defaultAuditAdv+"】。";
		ifShowTipInfo = true;
		
		TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
		if (null==tsZone) {
			tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
		}
		zoneType = tsZone.getZoneType().intValue();
		
		initSearchStates();
        this.searchAction();
    }
	/**
	 * <p>
	 * 方法描述：初始化状态
	 * </p>
	 * 
	 * @MethodAuthor rcj,2020年10月16日,initSearchStates
	 * */
	private void initSearchStates() {
		// 1、区县审核，状态为：待审核、待复审、复审退回、复审通过、终审通过，默认（待审核、复审退回）
		// 2、市级审核，状态为：待审核、已退回、待终审、终审退回、终审通过（待审核、终审退回）
		// 3、省级审核，状态为：待审核、已退回、审核通过

		this.stateList = new ArrayList<>();
		if (zoneType == 2) {
			this.states = new String[] {"5"};
			this.stateList.add(new SelectItem("5", "待审核"));
			this.stateList.add(new SelectItem("4", "已退回"));
			this.stateList.add(new SelectItem("6", "审核通过"));
		} else if (zoneType == 3) {
			this.states = new String[] {"3","4"};
			this.stateList.add(new SelectItem("3", "待审核"));
			this.stateList.add(new SelectItem("2", "已退回"));
			this.stateList.add(new SelectItem("5", "待终审"));
			this.stateList.add(new SelectItem("4", "终审退回"));
			this.stateList.add(new SelectItem("6", "终审通过"));
		} else {
			this.states = new String[] {"1","2"};
			this.stateList.add(new SelectItem("1", "待审核"));
			this.stateList.add(new SelectItem("3,4", "待复审"));
			this.stateList.add(new SelectItem("2", "复审退回"));
			this.stateList.add(new SelectItem("5", "复审通过"));
			this.stateList.add(new SelectItem("6", "终审通过"));
		}
	}
	
	 /**
     * @Description : 批量审核
     * @MethodAuthor: rcj
     * @Date : 2020/9/17 17:56
     **/
    public void reviewBatchAction() {
        for(Object[] obj : this.selectEntitys) {
            if(null == obj || null == obj[0]) {
                continue;
            }
			TdZwyjBsnCheck newFlow = cardCheckServiceImpl.searchTdZwyjBsnCheckByMainId(Integer.parseInt(obj[0].toString()));
            if(null == newFlow){
                newFlow = new TdZwyjBsnCheck();
                newFlow.setCreateManid(sessionData.getUser().getRid());
                newFlow.setCreateDate(new Date());
                newFlow.setFkByMainId(new TdZwyjBsnBhk(Integer.parseInt(obj[0].toString())));
            }
            initAuditAdvice(newFlow);
			cardCheckServiceImpl.upsertEntity(newFlow);
        }
        JsfUtil.addSuccessMessage("审核成功！");
        this.searchAction();
    }
    

    /**
 	 * <p>方法描述：批量非区级审核时，初始化审核意见</p>
 	 * @MethodAuthor rcj,2019年12月7日,initAuditAdvice
     * */
    private void initAuditAdvice(TdZwyjBsnCheck newFlow) {
    	if (zoneType != 4) {
    		String defaultAuditAdv = PropertyUtils.getValue("zdzybdefaultAuditAdv");
    		if (zoneType == 2 ) {
				// 省级审核意见
				if(StringUtils.isBlank(newFlow.getProAuditAdv())){
					newFlow.setProAuditAdv(defaultAuditAdv);
				}
				newFlow.setProSmtDate(new Date());
				newFlow.setFkByProChkOrgid(sessionData.getUser().getTsUnit());
				newFlow.setCityRst2(1);
				newFlow.setState(6);
			} else if (zoneType == 3  ){
				// 市级审核意见
				if(StringUtils.isBlank(newFlow.getCityAuditAdv())){
					newFlow.setCityAuditAdv(defaultAuditAdv);
				}
				newFlow.setCityRst2(null);
				newFlow.setProAuditAdv(null);
				newFlow.setCitySmtDate(new Date());
				newFlow.setFkByCiytChkOrgid(sessionData.getUser().getTsUnit());
				newFlow.setCityRst(1);
				newFlow.setState(5);
			}
		}
	}
    
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2021年1月6日,validateSearch
	 * */
    @Override
	public boolean  validateSearch(){
		if(DateUtils.isDateAfter(searchRcvBdate, searchRcvEdate)){
            JsfUtil.addErrorMessage("接收开始日期应小于等于接收结束日期！");
            return false;
        }
		return true;
    }

    /**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2021年1月5日,getSearchCondition
     * */
    @Override
    public String getOtherSearchCondition(){
    	StringBuilder sb = new StringBuilder();
    	// 接收日期
        if (null != this.searchRcvBdate) {
			sb.append(" AND CASE WHEN (T1.STATE = 5 or T1.STATE = 2) and t1.city_rst is not null THEN T1.CITY_SMT_DATE ");
			sb.append(" WHEN (T1.STATE = 3 and t5.if_city_direct is null) or(T1.STATE = 5 and t1.city_rst is  null) THEN T1.COUNTY_SMT_DATE WHEN T1.STATE = 1 OR T1.STATE IS NULL or (T1.STATE = 3 and t5.if_city_direct = 1) THEN nvl(T.DATA_UP_DATE,T.CREATE_DATE) ELSE T1.PRO_SMT_DATE END >= TO_DATE('").append(DateUtils.formatDate(this.searchRcvBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchRcvEdate) {
			sb.append(" AND  CASE WHEN (T1.STATE = 5 or T1.STATE = 2) and t1.city_rst is not null THEN T1.CITY_SMT_DATE ");
			sb.append(" WHEN (T1.STATE = 3 and t5.if_city_direct is null) or(T1.STATE = 5 and t1.city_rst is  null) THEN T1.COUNTY_SMT_DATE WHEN T1.STATE = 1 OR T1.STATE IS NULL or (T1.STATE = 3 and t5.if_city_direct = 1) THEN nvl(T.DATA_UP_DATE,T.CREATE_DATE) ELSE T1.PRO_SMT_DATE END <= TO_DATE('").append(DateUtils.formatDate(this.searchRcvEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        // 状态
 		StringBuffer stateSb = new StringBuffer();
         if (null != states && states.length > 0) {
             for (String state : states) {
                 stateSb.append(",").append(state);
             }
         } else {
             if (null != stateList && stateList.size() > 0) {
                 for (SelectItem item : stateList) {
                     stateSb.append(",").append(item.getValue());
                 }
             }
         }
         sb.append(" AND (T1.STATE IN (").append(stateSb.substring(1)).append(") ");
         if(stateSb.toString().contains("1")){
         	sb.append(" or T1.STATE IS NULL");
         }
         if (zoneType == 3 && stateSb.toString().contains("3")) {//市级审核-待审核（3+市直属状态为空）
        	 sb.append(" or (t5.if_city_direct=1 and t1.STATE is null)");
         }
         sb.append(")");
    	return sb.toString();
    }

    /**
	 * <p>方法描述：切换审核结果</p>
	 * @MethodAuthor rcj,2020年4月22日,changeSubmitRst
	 * */
	public void changeSubmitRst(){

		if(zoneType == 3){
			// 通过且意见为空则默认通过
			if(newFlow.getCityRst() == 1){
				if(StringUtils.isBlank(newFlow.getCityAuditAdv())){
					newFlow.setCityAuditAdv(defaultAuditAdv);
				}
			}

		}else if(zoneType == 2){
			// 通过且意见为空则默认通过
			if(newFlow.getCityRst2() == 1){
				if(StringUtils.isBlank(newFlow.getProAuditAdv())){
					newFlow.setProAuditAdv(defaultAuditAdv);
				}
			}
		}
	}
    
    @Override
    public void saveAction() {
		try{
			baseServiceImpl.upsertTdZwyjBsnBhkEntity(newFlow,zoneType,1,defaultAuditAdv,sessionData.getUser().getTsUnit(),ifCityDirect);
			JsfUtil.addSuccessMessage("保存成功！");
		}catch (Exception e ){
			JsfUtil.addErrorMessage("保存失败！");
			e.printStackTrace();
		}

    }

	/**
	 * @Description : 审核撤销
	 * @MethodAuthor: rcj
	 * @Date : 2020/10/30 11:13
	 **/
    public void revokeAction() {
    	try {
			baseServiceImpl.upsertTdZwyjBsnBhkEntity(newFlow,zoneType,3,defaultAuditAdv,sessionData.getUser().getTsUnit(),ifCityDirect);
			ifCheck = true;
			modInitAction();
			JsfUtil.addSuccessMessage("撤销成功！");
		}catch (Exception e ){
			JsfUtil.addErrorMessage("撤销失败！");
    		e.printStackTrace();
		}

    }

	/**
	 * @Description : 审核提交
	 * @MethodAuthor: rcj
	 * @Date : 2020/10/30 11:13
	 **/
    public void submitAction(){
		boolean flag = false;
		if (newFlow!= null  && this.ifCheck) {
			if((ifCityDirect || zoneType == 4) && newFlow.getIfNormal()==null){
				JsfUtil.addErrorMessage("结论是否正常不能为空！");
				flag = true;
			}
			if ((zoneType ==2 && newFlow.getCityRst2() == null)||(!ifCityDirect && zoneType ==3 && newFlow.getCityRst() == null) ) {
				JsfUtil.addErrorMessage("审核结果不能为空！");
				flag = true;
			}
			if((ifCityDirect || zoneType == 4) && newFlow.getIfNormal()!=null &&  newFlow.getIfNormal()==1 && StringUtils.isBlank(newFlow.getCountyAuditAdv())){
				JsfUtil.addErrorMessage("结论是否正常为是时的审核意见不能为空！");
				flag = true;
			}

			if (zoneType == 3 && !ifCityDirect  && null != newFlow.getCityRst() && newFlow.getCityRst() == 2 &&  StringUtils.isBlank(newFlow.getCityAuditAdv())) {
				JsfUtil.addErrorMessage("审核结果为退回时的审核意见不能为空！");
				flag = true;
			}
			if (zoneType == 2  && null != newFlow.getCityRst2() && newFlow.getCityRst2() == 2 && StringUtils.isBlank(newFlow.getProAuditAdv())) {
				JsfUtil.addErrorMessage("审核结果为退回时的审核意见不能为空！");
				flag = true;
			}
		}
		if (flag) {
			return;
		}
		try{
			baseServiceImpl.upsertTdZwyjBsnBhkEntity(newFlow,zoneType,2,defaultAuditAdv,sessionData.getUser().getTsUnit(),ifCityDirect);
			JsfUtil.addSuccessMessage("提交成功！");
			this.searchAction();
			this.backAction();
			RequestContext context = RequestContext.getCurrentInstance();
			context.update("tabView");
		}catch (Exception e ){
			JsfUtil.addErrorMessage("提交失败！");
			e.printStackTrace();
		}

	}
    
    @Override
    public void processData(List<?> list) {
        try {
            if (StringUtils.isBlank(limitTime)) {
                return;
            }
            if (null!=list && list.size()>0) {
                List<Object[]> result = (List<Object[]>) list;
                for (Object[] obj : result) {

                    if (null == obj[13]) {
                        continue;
                    }
                    // 初审
                    if(null == obj[11] && (zoneType == 4 || zoneType == 3)){
						obj[13] = calLimitTime(DateUtils.parseDate(obj[10].toString(), "yyyy-MM-dd"));
						obj[14]="1";//审核
						continue;
					}
                    if (zoneType == 2) {//省级
                        //已退回
                    	if(Integer.valueOf(obj[11].toString()) == 4){
                            obj[14]="0";//详情
						}else if(Integer.valueOf(obj[11].toString())==5){
                            //待审核
                    		obj[13] = calLimitTime(DateUtils.parseDate(obj[10].toString(), "yyyy-MM-dd"));
							obj[14]="1";//审核
						}else if(Integer.valueOf(obj[11].toString()) == 6){
							obj[14]="0";//详情
						}
                    } else if (zoneType == 3) {//市级
						//待审核
						if(Integer.valueOf(obj[11].toString()) == 3 || Integer.valueOf(obj[11].toString()) == 1){
							obj[13] = calLimitTime(DateUtils.parseDate(obj[10].toString(), "yyyy-MM-dd"));
							obj[14]="1";//审核
						}else if(Integer.valueOf(obj[11].toString())==2){
							obj[14]="0";//详情
						}else if(Integer.valueOf(obj[11].toString()) == 5){
							obj[14]="0";//详情
						}else if(Integer.valueOf(obj[11].toString()) == 4){
							//已退回
							obj[13] = calLimitTime(DateUtils.parseDate(obj[10].toString(), "yyyy-MM-dd"));
							obj[14]="1";//审核
						}else if(Integer.valueOf(obj[11].toString()) == 6){
							obj[14]="0";//详情
						}
                    } else {//区县级
						//待审核
						if(Integer.valueOf(obj[11].toString()) == 1){
							obj[13] = calLimitTime(DateUtils.parseDate(obj[10].toString(), "yyyy-MM-dd"));
							obj[14]="1";//审核
						}else if(Integer.valueOf(obj[11].toString())==3 || Integer.valueOf(obj[11].toString())==4){
							obj[14]="0";//详情
						}else if(Integer.valueOf(obj[11].toString()) == 2){
							//已退回
							obj[13] = calLimitTime(DateUtils.parseDate(obj[10].toString(), "yyyy-MM-dd"));
							obj[14]="1";//审核
						}else if(Integer.valueOf(obj[11].toString()) == 5){
							obj[14]="0";//详情
						}else if(Integer.valueOf(obj[11].toString()) == 6){
							obj[14]="0";//详情
						}
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    @Override
	public String getType() {
		return "0";
	}
    public Date getSearchRcvBdate() {
        return searchRcvBdate;
    }

    public void setSearchRcvBdate(Date searchRcvBdate) {
        this.searchRcvBdate = searchRcvBdate;
    }

    public Date getSearchRcvEdate() {
        return searchRcvEdate;
    }

    public void setSearchRcvEdate(Date searchRcvEdate) {
        this.searchRcvEdate = searchRcvEdate;
    }

	public String getTipInfo() {
		return tipInfo;
	}

	public void setTipInfo(String tipInfo) {
		this.tipInfo = tipInfo;
	}

	public boolean isIfShowTipInfo() {
		return ifShowTipInfo;
	}

	public void setIfShowTipInfo(boolean ifShowTipInfo) {
		this.ifShowTipInfo = ifShowTipInfo;
	}
	public Integer getZoneType() {
		return zoneType;
	}
	public void setZoneType(Integer zoneType) {
		this.zoneType = zoneType;
	}
	public Boolean getIfCheck() {
		return ifCheck;
	}
	public void setIfCheck(Boolean ifCheck) {
		this.ifCheck = ifCheck;
	}
	
}
