package com.chis.modules.heth.comm.web;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdZyTrainSituation;
import com.chis.modules.heth.comm.entity.TdZyUnitbasicinfo;
import com.chis.modules.heth.comm.entity.TdZyUnitfactorcrowd;
import com.chis.modules.heth.comm.entity.TdZyUnitharmfactorcheck;
import com.chis.modules.heth.comm.entity.TdZyUnithealthcustody;
import com.chis.modules.heth.comm.entity.TdZyUnitmainprod;
import com.chis.modules.heth.comm.logic.ShowColumnPO;
import com.chis.modules.heth.comm.logic.ShowRowPO;
import com.chis.modules.heth.comm.service.TdZyUnitbasicinfoService;
import com.chis.modules.system.service.CommServiceImpl;
import org.springframework.util.CollectionUtils;

/**
 * <p>类描述：企业申报信息</p>
 * @ClassAuthor qrr,2020年12月9日,TdZyUnitBasicInfoViewBean
 * */
@ManagedBean(name="tdZyUnitBasicInfoViewBean")
@ViewScoped
public class TdZyUnitBasicInfoViewBean {
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private TdZyUnitbasicinfoService basicinfoService = SpringContextHolder.getBean(TdZyUnitbasicinfoService.class);
	private TdZyUnitbasicinfo unitbasicinfo;
	/**培训情况*/
	private TdZyTrainSituation trainSituation;
	/**主要产品*/
	private List<TdZyUnitmainprod> proList;
	/**职业病危害因素种类及接触人数*/
	private TdZyUnitfactorcrowd factorcrowd;
	/**危害因素检测情况*/
	private TdZyUnitharmfactorcheck factorcheck;
	/**职业健康监护开展情况*/
	private TdZyUnithealthcustody healthcustody;
	/** 危害因素接触人数明细 */
	private List<ShowRowPO> factorcDetailList;
	/** 危害因素检测情况明细 */
	private List<ShowRowPO> harmfactDetailList;
	/** 监护开展情况明细 */
	private List<ShowRowPO> healthDetailList;

	public TdZyUnitBasicInfoViewBean(){
		String rid = JsfUtil.getRequestParameter("rid");
		if (StringUtils.isNotBlank(rid)) {
			this.searchAction(new Integer(rid));
		}
	}
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2020年12月9日,searchAction
	 * */
	private void searchAction(Integer rid) {
		this.unitbasicinfo = commService.find(TdZyUnitbasicinfo.class, rid);
		//电话脱敏
		if(null != this.unitbasicinfo){
			//填报人联系电话脱敏
			if(StringUtils.isNotBlank(this.unitbasicinfo.getFillPhone())){
				this.unitbasicinfo.setFillPhone(StringUtils.encryptPhone(this.unitbasicinfo.getFillPhone()));
			}
			//法定代表人联系人电话脱敏
			if(StringUtils.isNotBlank(this.unitbasicinfo.getLegalPersonPhone())){
				this.unitbasicinfo.setLegalPersonPhone(StringUtils.encryptPhone(this.unitbasicinfo.getLegalPersonPhone()));
			}
			//职业卫生管理联系人电话脱敏
			if(StringUtils.isNotBlank(this.unitbasicinfo.getLinkPhone())){
				this.unitbasicinfo.setLinkPhone(StringUtils.encryptPhone(this.unitbasicinfo.getLinkPhone()));
			}
		}
		this.trainSituation = commService.findEntityByMainId(TdZyTrainSituation.class, rid);
		this.proList = commService.findEntityListByMainId(TdZyUnitmainprod.class, rid);
		this.factorcrowd = commService.findEntityByMainId(TdZyUnitfactorcrowd.class, rid);
		this.factorcheck = commService.findEntityByMainId(TdZyUnitharmfactorcheck.class, rid);
		this.healthcustody = commService.findEntityByMainId(TdZyUnithealthcustody.class, rid);

		//样式集合
		List<String> styleList = new ArrayList<>();
		styleList.add("text-align:right;padding-right:3px;width:217px;");
		styleList.add("text-align:left;padding-left:10px;width:220px;");
		styleList.add("text-align:right;padding-right:3px;width:217px;");
		styleList.add("text-align:left;padding-left:10px;width:220px;");
		styleList.add("text-align:right;padding-right:3px;width:230px;");
		styleList.add("text-align:left;padding-left:10px;");

		this.factorcDetailList = new ArrayList<>();
		//危害因素接触情况明细
		Map<Integer,List<Object[]>> badRsnCountTypeMap = this.basicinfoService.findBadRsnCountInfoByTypeAndMainRid(rid, 1);
		if(null == this.factorcrowd){
			this.factorcrowd = new TdZyUnitfactorcrowd();
		}
		String unit = "人";
		this.generateFactorcDetailRow(styleList, "粉尘",
				this.factorcrowd.getIfhfDust(), this.factorcrowd.getHfDustPeoples(),
				this.generateFactorcDetailColumn(null == badRsnCountTypeMap ? null : badRsnCountTypeMap.get(1), unit), unit);
		this.generateFactorcDetailRow(styleList, "化学物质",
				this.factorcrowd.getIfhfChemistry(), this.factorcrowd.getHfChemistryPeoples(),
				this.generateFactorcDetailColumn(null == badRsnCountTypeMap ? null : badRsnCountTypeMap.get(2), unit), unit);
		this.generateFactorcDetailRow(styleList, "物理因素",
				this.factorcrowd.getIfhfPhysics(), this.factorcrowd.getHfPhysicsPeoples(),
				this.generateFactorcDetailColumn(null == badRsnCountTypeMap ? null : badRsnCountTypeMap.get(3), unit), unit);
		this.generateFactorcDetailRow(styleList, "放射性因素",
				this.factorcrowd.getIfhfRadioactivity(), this.factorcrowd.getHfRadioactivityPeoples(),
				this.generateFactorcDetailColumn(null == badRsnCountTypeMap ? null : badRsnCountTypeMap.get(4), unit), unit);
		this.generateFactorcDetailRow(styleList, "生物因素",
				this.factorcrowd.getIfhfBiology(), this.factorcrowd.getHfBiologyPeoples(),
				this.generateFactorcDetailColumn(null == badRsnCountTypeMap ? null : badRsnCountTypeMap.get(5), unit), unit);
		this.generateFactorcDetailRow(styleList, "其他因素",
				this.factorcrowd.getIfhfOther(), this.factorcrowd.getHfOtherPeoples(),
				this.generateFactorcDetailColumn(null == badRsnCountTypeMap ? null : badRsnCountTypeMap.get(6), unit), unit);

		//检测情况明细
		this.harmfactDetailList = new ArrayList<>();
		badRsnCountTypeMap = this.basicinfoService.findBadRsnCountInfoByTypeAndMainRid(rid, 2);
		if(null == this.factorcheck){
			this.factorcheck = new TdZyUnitharmfactorcheck();
		}
		Map<Integer,List<Object[]>> orgInfoMap = this.basicinfoService.findOrgInfoByMainRid(rid);
		//检测机构名称与检测报告编号
		List<Object[]> orgInfoList = null == orgInfoMap ? null : orgInfoMap.get(1);
		if(!CollectionUtils.isEmpty(orgInfoList)){
			Object[] objArr = orgInfoList.get(0);
			this.factorcheck.setTestUnitNames(null == objArr[1] ? null : objArr[1].toString());
			this.factorcheck.setTestReportNos(null == objArr[2] ? null : objArr[2].toString());
		}
		this.generateHarmfactDetailRow(styleList, "粉尘",
				this.factorcheck.getIfatDust(),
				this.factorcheck.getIfatDustAllChecknum(),
				this.factorcheck.getIfatDustAllExcessnum(),
				this.generateHarmfactDetailColumn(null == badRsnCountTypeMap ? null : badRsnCountTypeMap.get(1)));
		this.generateHarmfactDetailRow(styleList, "化学物质",
				this.factorcheck.getIfatChemistry(),
				this.factorcheck.getIfatChemistryAllChecknum(),
				this.factorcheck.getIfatChemistryAllExcessnum(),
				this.generateHarmfactDetailColumn(null == badRsnCountTypeMap ? null : badRsnCountTypeMap.get(2)));
		this.generateHarmfactDetailRow(styleList, "物理因素",
				this.factorcheck.getIfatPhysics(),
				this.factorcheck.getIfatPhysicsAllChecknum(),
				this.factorcheck.getIfatPhysicsAllExcessnum(),
				this.generateHarmfactDetailColumn(null == badRsnCountTypeMap ? null : badRsnCountTypeMap.get(3)));
		this.generateHarmfactDetailRow(styleList, "放射性物质",
				this.factorcheck.getIfatRadioactivity(),
				this.factorcheck.getIfatRadioactivityChecknum(),
				this.factorcheck.getIfatRadioactivityExcessnum(),
				this.generateHarmfactDetailColumn(null == badRsnCountTypeMap ? null : badRsnCountTypeMap.get(4)));
		//生物和其他一起
		List<Object[]> tmpBadRsnCountList = new ArrayList<>();
		if(null != badRsnCountTypeMap){
			if(!CollectionUtils.isEmpty(badRsnCountTypeMap.get(5))){
				tmpBadRsnCountList.addAll(badRsnCountTypeMap.get(5));
			}
			if(!CollectionUtils.isEmpty(badRsnCountTypeMap.get(6))){
				tmpBadRsnCountList.addAll(badRsnCountTypeMap.get(6));
			}
		}
		this.generateHarmfactDetailRow(styleList, "生物和其他因素",
				this.factorcheck.getIfatBiologyother(),
				this.factorcheck.getIfatBiologyotherChecknum(),
				this.factorcheck.getIfatBiologyotherExcessnum(),
				this.generateHarmfactDetailColumn(tmpBadRsnCountList));

		//职业健康监护开展情况明细
		this.healthDetailList = new ArrayList<>();
		badRsnCountTypeMap = this.basicinfoService.findBadRsnCountInfoByTypeAndMainRid(rid, 3);
		if(null == this.healthcustody){
			this.healthcustody = new TdZyUnithealthcustody();
		}
		//检查机构名称与检查总结报告编号
		orgInfoList = null == orgInfoMap ? null : orgInfoMap.get(2);
		if(!CollectionUtils.isEmpty(orgInfoList)){
			Object[] objArr = orgInfoList.get(0);
			this.healthcustody.setCheckUnitNames(null == objArr[1] ? null : objArr[1].toString());
			this.healthcustody.setCheckReportNos(null == objArr[2] ? null : objArr[2].toString());
		}
		this.generateHealthDetailRow(styleList, "粉尘",
				this.healthcustody.getIfheaDust(), this.healthcustody.getHeaDustPeoples(),
				this.generateHealthDetailColumn(null == badRsnCountTypeMap ? null : badRsnCountTypeMap.get(1), unit), unit);
		this.generateHealthDetailRow(styleList, "化学物质",
				this.healthcustody.getIfheaChemistry(), this.healthcustody.getHeaChemistryPeoples(),
				this.generateHealthDetailColumn(null == badRsnCountTypeMap ? null : badRsnCountTypeMap.get(2), unit), unit);
		this.generateHealthDetailRow(styleList, "物理因素",
				this.healthcustody.getIfheaPhysics(), this.healthcustody.getHeaPhysicsPeoples(),
				this.generateHealthDetailColumn(null == badRsnCountTypeMap ? null : badRsnCountTypeMap.get(3), unit), unit);
		this.generateHealthDetailRow(styleList, "放射性因素",
				this.healthcustody.getIfheaRadioactivity(), this.healthcustody.getHeaRadioactivityPeoples(),
				this.generateHealthDetailColumn(null == badRsnCountTypeMap ? null : badRsnCountTypeMap.get(4), unit), unit);
	}

	/**
	 * <p>方法描述：单危害因素职业健康监护开展情况行 </p>
	 * @MethodAuthor： pw 2022/9/9
	 **/
	private void generateHealthDetailRow(List<String> styleList, String name, Integer ifHas, Integer nums,
										 List<ShowColumnPO> columnList, String unit){
		if(null == unit){
			unit = "";
		}
		List<ShowColumnPO> showColumnList = new ArrayList<>();
		ShowColumnPO columnPO = new ShowColumnPO();
		columnPO.setColVal(null == name ? "" : name+"：");
		showColumnList.add(columnPO);
		columnPO = new ShowColumnPO();
		columnPO.setColVal(null != ifHas && 1 == ifHas ? "有" : (null != ifHas && 0 == ifHas ? "无" : null));
		showColumnList.add(columnPO);
		columnPO = new ShowColumnPO();
		columnPO.setColVal("体检人数：");
		showColumnList.add(columnPO);
		columnPO = new ShowColumnPO();
		columnPO.setColVal(null == nums ? null : nums.toString()+unit);
		showColumnList.add(columnPO);
		if(!CollectionUtils.isEmpty(columnList)){
			showColumnList.addAll(columnList);
		}
		this.healthDetailList.addAll(this.generateNormalRow(showColumnList, styleList, 6));
	}

	/**
	 * <p>方法描述：单危害因素职业病危害因素检测情况行 </p>
	 * @MethodAuthor： pw 2022/9/9
	 **/
	private void generateHarmfactDetailRow(List<String> styleList, String name, Integer ifJc,
										   Integer jcPoint, Integer overPoint, List<ShowColumnPO> columnList){
		List<ShowColumnPO> showColumnList = new ArrayList<>();
		ShowColumnPO columnPO = new ShowColumnPO();
		columnPO.setColVal("检测点数：");
		showColumnList.add(columnPO);
		columnPO = new ShowColumnPO();
		columnPO.setColVal(null == jcPoint ? null : jcPoint.toString());
		showColumnList.add(columnPO);
		columnPO = new ShowColumnPO();
		columnPO.setColVal("超标点数：");
		showColumnList.add(columnPO);
		columnPO = new ShowColumnPO();
		columnPO.setColVal(null == overPoint ? null : overPoint.toString());
		showColumnList.add(columnPO);
		if(!CollectionUtils.isEmpty(columnList)){
			showColumnList.addAll(columnList);
		}
		List<String> tmpStyleList = new ArrayList<>();
		int styleSize = styleList.size();
		for(int i=2;i<styleSize; i++){
			tmpStyleList.add(styleList.get(i));
		}
		List<ShowRowPO> showRowPOList = this.generateNormalRow(showColumnList, tmpStyleList, 4);
		int rowspan = showRowPOList.size();
		//名称与是否检测列加入
		columnPO = new ShowColumnPO();
		columnPO.setColVal(null == name ? "" : name+"：");
		columnPO.setRowspan(rowspan);
		columnPO.setStyle(styleList.get(0));
		showRowPOList.get(0).getColList().add(0,columnPO);
		columnPO = new ShowColumnPO();
		columnPO.setColVal(null != ifJc && 1 == ifJc ? "检测" : (null != ifJc && 0 == ifJc ? "未检测" : null));
		columnPO.setRowspan(rowspan);
		columnPO.setStyle(styleList.get(1));
		showRowPOList.get(0).getColList().add(1,columnPO);
		this.harmfactDetailList.addAll(showRowPOList);
	}

	/**
	 * <p>方法描述：单危害因素职业病危害因素种类行 </p>
	 * @MethodAuthor： pw 2022/9/9
	 **/
	private void generateFactorcDetailRow(List<String> styleList, String name, Integer ifHas, Integer nums,
													 List<ShowColumnPO> columnList, String unit){
		if(null == unit){
			unit = "";
		}
		List<ShowColumnPO> showColumnList = new ArrayList<>();
		ShowColumnPO columnPO = new ShowColumnPO();
		columnPO.setColVal(null == name ? "" : name+"：");
		showColumnList.add(columnPO);
		columnPO = new ShowColumnPO();
		columnPO.setColVal(null == ifHas ? "" : (1 == ifHas ? "有" : "无"));
		showColumnList.add(columnPO);
		columnPO = new ShowColumnPO();
		columnPO.setColVal("接触总人数：");
		showColumnList.add(columnPO);
		columnPO = new ShowColumnPO();
		columnPO.setColVal(null == nums ? null : nums.toString()+unit);
		showColumnList.add(columnPO);
		if(!CollectionUtils.isEmpty(columnList)){
			showColumnList.addAll(columnList);
		}
		this.factorcDetailList.addAll(this.generateNormalRow(showColumnList, styleList, 6));
	}

	/**
	 * <p>方法描述：生成普通行数据 </p>
	 * @MethodAuthor： pw 2022/9/9
	 **/
	private List<ShowRowPO> generateNormalRow(List<ShowColumnPO> showColumnList, List<String> styleList, int columnNum){
		if(CollectionUtils.isEmpty(styleList) || styleList.size() != columnNum){
			return null;
		}
		List<List<ShowColumnPO>> groupList = StringUtils.splitListProxy(showColumnList, columnNum);
		int size = groupList.size();
		List<ShowRowPO> resultList = new ArrayList<>();
		for(int i=0; i<size; i++){
			ShowRowPO showRow = new ShowRowPO();
			List<ShowColumnPO> tmpList = groupList.get(i);
			//加入样式
			int innerSize = tmpList.size();
			for(int j=0; j<innerSize; j++){
				String style = styleList.get(j);
				//最后一行 取最后一个样式 避免一行数据 满数据6个单元格 但实际仅四个单元格的情况
				if(j == innerSize-1){
					style = styleList.get(columnNum-1);
				}
				tmpList.get(j).setStyle(style);
			}
			if(innerSize < columnNum){
				//最后一个单元格 设置colspan
				tmpList.get(innerSize-1).setColspan(columnNum-innerSize+1);
			}
			showRow.setColList(tmpList);
			resultList.add(showRow);
		}
		//加入通用样式 比如行高
		for(ShowRowPO rowPO : resultList){
			List<ShowColumnPO> colList = rowPO.getColList();
			String style = colList.get(0).getStyle();
			StringBuffer styleBuffer = new StringBuffer(null == style ? "" : style);
			//第一个单元格设置高度
			styleBuffer.append("height: 38px;");
			colList.get(0).setStyle(styleBuffer.toString());
		}
		return resultList;
	}

	/**
	 * <p>方法描述：职业健康监护开展情况明细单危害因素单元格生成 </p>
	 * @MethodAuthor： pw 2022/9/9
	 **/
	private List<ShowColumnPO> generateHealthDetailColumn(List<Object[]> list, String unit){
		if(CollectionUtils.isEmpty(list)){
			return null;
		}
		if(null == unit){
			unit = "";
		}
		List<ShowColumnPO> resultList = new ArrayList<>();
		for(Object[] objArr : list){
			String hazardsName = null == objArr[1] ? null : objArr[1].toString();
			if(StringUtils.isBlank(hazardsName)){
				continue;
			}
			String peNum = null == objArr[2] ? null : objArr[2].toString()+unit;
			ShowColumnPO columnPO = new ShowColumnPO();
			columnPO.setColVal("接触"+hazardsName+"体检人数：");
			resultList.add(columnPO);
			columnPO = new ShowColumnPO();
			columnPO.setColVal(peNum);
			resultList.add(columnPO);
		}
		return resultList;
	}

	/**
	 * <p>方法描述：检测情况明细单危害因素单元格生成 </p>
	 * @MethodAuthor： pw 2022/9/9
	 **/
	private List<ShowColumnPO> generateHarmfactDetailColumn(List<Object[]> list){
		if(CollectionUtils.isEmpty(list)){
			return null;
		}
		List<ShowColumnPO> resultList = new ArrayList<>();
		for(Object[] objArr : list){
			String hazardsName = null == objArr[1] ? null : objArr[1].toString();
			if(StringUtils.isBlank(hazardsName)){
				continue;
			}
			String checkPoints = null == objArr[2] ? null : objArr[2].toString();
			String overproofPoints = null == objArr[3] ? null : objArr[3].toString();
			ShowColumnPO columnPO = new ShowColumnPO();
			columnPO.setColVal(hazardsName+"检测点数：");
			resultList.add(columnPO);
			columnPO = new ShowColumnPO();
			columnPO.setColVal(checkPoints);
			resultList.add(columnPO);
			columnPO = new ShowColumnPO();
			columnPO.setColVal(hazardsName+"超标点数：");
			resultList.add(columnPO);
			columnPO = new ShowColumnPO();
			columnPO.setColVal(overproofPoints);
			resultList.add(columnPO);
		}
		return resultList;
	}

	/**
	 * <p>方法描述：危害因素接触情况明细单危害因素单元格生成 </p>
	 * @MethodAuthor： pw 2022/9/9
	 **/
	private List<ShowColumnPO> generateFactorcDetailColumn(List<Object[]> list, String unit){
		if(CollectionUtils.isEmpty(list)){
			return null;
		}
		if(null == unit){
			unit = "";
		}
		List<ShowColumnPO> resultList = new ArrayList<>();
		for(Object[] objArr : list){
			String hazardsName = null == objArr[1] ? null : objArr[1].toString();
			if(StringUtils.isBlank(hazardsName)){
				continue;
			}
			String contactNumber = null == objArr[2] ? null : objArr[2].toString() +unit;
			ShowColumnPO columnPO = new ShowColumnPO();
			columnPO.setColVal("接触"+hazardsName+"人数：");
			resultList.add(columnPO);
			columnPO = new ShowColumnPO();
			columnPO.setColVal(contactNumber);
			resultList.add(columnPO);
		}
		return resultList;
	}

	public TdZyUnitbasicinfo getUnitbasicinfo() {
		return unitbasicinfo;
	}
	public void setUnitbasicinfo(TdZyUnitbasicinfo unitbasicinfo) {
		this.unitbasicinfo = unitbasicinfo;
	}
	public TdZyTrainSituation getTrainSituation() {
		return trainSituation;
	}
	public void setTrainSituation(TdZyTrainSituation trainSituation) {
		this.trainSituation = trainSituation;
	}
	public List<TdZyUnitmainprod> getProList() {
		return proList;
	}
	public void setProList(List<TdZyUnitmainprod> proList) {
		this.proList = proList;
	}
	public TdZyUnitfactorcrowd getFactorcrowd() {
		return factorcrowd;
	}
	public void setFactorcrowd(TdZyUnitfactorcrowd factorcrowd) {
		this.factorcrowd = factorcrowd;
	}
	public TdZyUnitharmfactorcheck getFactorcheck() {
		return factorcheck;
	}
	public void setFactorcheck(TdZyUnitharmfactorcheck factorcheck) {
		this.factorcheck = factorcheck;
	}
	public TdZyUnithealthcustody getHealthcustody() {
		return healthcustody;
	}
	public void setHealthcustody(TdZyUnithealthcustody healthcustody) {
		this.healthcustody = healthcustody;
	}

	public List<ShowRowPO> getFactorcDetailList() {
		return factorcDetailList;
	}

	public void setFactorcDetailList(List<ShowRowPO> factorcDetailList) {
		this.factorcDetailList = factorcDetailList;
	}

	public List<ShowRowPO> getHarmfactDetailList() {
		return harmfactDetailList;
	}

	public void setHarmfactDetailList(List<ShowRowPO> harmfactDetailList) {
		this.harmfactDetailList = harmfactDetailList;
	}

	public List<ShowRowPO> getHealthDetailList() {
		return healthDetailList;
	}

	public void setHealthDetailList(List<ShowRowPO> healthDetailList) {
		this.healthDetailList = healthDetailList;
	}
}
