package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @createTime 2018-7-2
 */
@Entity
@Table(name = "TD_ZDZYB_ANALY_TYPE")
@SequenceGenerator(name = "TdZdzybAnalyTypeAnaly", sequenceName = "TD_ZDZYB_ANALY_TYPE_SEQ", allocationSize = 1)
public class TdZdzybAnalyTypeComm {

    private Integer rid;
    private Integer busType;
    private Integer analyType;
    private String rmk;
    private Date modifyDate;
    private Integer modifyManid;
    private Date createDate;
    private Integer createManid;
    private List<TdZdzybAnalyDetailComm> tdZdzybAnalyDetailList;

    public TdZdzybAnalyTypeComm() {
    }

    public TdZdzybAnalyTypeComm(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZdzybAnalyTypeAnaly")
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Column(name = "BUS_TYPE")
    public Integer getBusType() {
        return busType;
    }

    public void setBusType(Integer busType) {
        this.busType = busType;
    }

    @Column(name = "ANALY_TYPE")
    public Integer getAnalyType() {
        return analyType;
    }

    public void setAnalyType(Integer analyType) {
        this.analyType = analyType;
    }

    @Column(name = "RMK")
    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdZdzybAnalyType")
    public List<TdZdzybAnalyDetailComm> getTdZdzybAnalyDetailList() {
        return tdZdzybAnalyDetailList;
    }

    public void setTdZdzybAnalyDetailList(List<TdZdzybAnalyDetailComm> tdZdzybAnalyDetailList) {
        this.tdZdzybAnalyDetailList = tdZdzybAnalyDetailList;
    }
}
