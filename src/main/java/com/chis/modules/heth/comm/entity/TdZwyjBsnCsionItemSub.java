package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-10-29
 */
@Entity
@Table(name = "TD_ZWYJ_BSN_CSION_ITEM_SUB")
@SequenceGenerator(name = "TdZwyjBsnCsionItemSub", sequenceName = "TD_ZWYJ_BSN_CSION_ITEM_SUB_SEQ", allocationSize = 1)
public class TdZwyjBsnCsionItemSub implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwyjBsnClusion fkByMainId;
	private String itemDesc;
	private String pdCsionDesc;
	private Integer xh;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjBsnCsionItemSub() {
	}

	public TdZwyjBsnCsionItemSub(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjBsnCsionItemSub")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwyjBsnClusion getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwyjBsnClusion fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "ITEM_DESC")	
	public String getItemDesc() {
		return itemDesc;
	}

	public void setItemDesc(String itemDesc) {
		this.itemDesc = itemDesc;
	}	
			
	@Column(name = "PD_CSION_DESC")	
	public String getPdCsionDesc() {
		return pdCsionDesc;
	}

	public void setPdCsionDesc(String pdCsionDesc) {
		this.pdCsionDesc = pdCsionDesc;
	}	
			
	@Column(name = "XH")	
	public Integer getXh() {
		return xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}