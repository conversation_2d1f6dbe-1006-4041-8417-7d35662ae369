package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-4
 */
@Entity
@Table(name = "TD_ZXJC_EQUIMENT_DIS")
@SequenceGenerator(name = "TdZxjcEquimentDis", sequenceName = "TD_ZXJC_EQUIMENT_DIS_SEQ", allocationSize = 1)
public class TdZxjcEquimentDis implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZxjcUnitbasicinfo fkByMainId;
	private Integer fckzDistributionSituation;
	private Integer fckzWearSituation;
	private Integer fdkzDistributionSituation;
	private Integer fdkzWearSituation;
	private Integer fzyesDistributionSituation;
	private Integer fzyesWearSituation;
	private Integer signDust;
	private Integer signChemistry;
	private Integer signPhysics;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZxjcEquimentDis() {
	}

	public TdZxjcEquimentDis(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZxjcEquimentDis")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZxjcUnitbasicinfo getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZxjcUnitbasicinfo fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "FCKZ_DISTRIBUTION_SITUATION")	
	public Integer getFckzDistributionSituation() {
		return fckzDistributionSituation;
	}

	public void setFckzDistributionSituation(Integer fckzDistributionSituation) {
		this.fckzDistributionSituation = fckzDistributionSituation;
	}	
			
	@Column(name = "FCKZ_WEAR_SITUATION")	
	public Integer getFckzWearSituation() {
		return fckzWearSituation;
	}

	public void setFckzWearSituation(Integer fckzWearSituation) {
		this.fckzWearSituation = fckzWearSituation;
	}	
			
	@Column(name = "FDKZ_DISTRIBUTION_SITUATION")	
	public Integer getFdkzDistributionSituation() {
		return fdkzDistributionSituation;
	}

	public void setFdkzDistributionSituation(Integer fdkzDistributionSituation) {
		this.fdkzDistributionSituation = fdkzDistributionSituation;
	}	
			
	@Column(name = "FDKZ_WEAR_SITUATION")	
	public Integer getFdkzWearSituation() {
		return fdkzWearSituation;
	}

	public void setFdkzWearSituation(Integer fdkzWearSituation) {
		this.fdkzWearSituation = fdkzWearSituation;
	}	
			
	@Column(name = "FZYES_DISTRIBUTION_SITUATION")	
	public Integer getFzyesDistributionSituation() {
		return fzyesDistributionSituation;
	}

	public void setFzyesDistributionSituation(Integer fzyesDistributionSituation) {
		this.fzyesDistributionSituation = fzyesDistributionSituation;
	}	
			
	@Column(name = "FZYES_WEAR_SITUATION")	
	public Integer getFzyesWearSituation() {
		return fzyesWearSituation;
	}

	public void setFzyesWearSituation(Integer fzyesWearSituation) {
		this.fzyesWearSituation = fzyesWearSituation;
	}	
			
	@Column(name = "SIGN_DUST")	
	public Integer getSignDust() {
		return signDust;
	}

	public void setSignDust(Integer signDust) {
		this.signDust = signDust;
	}	
			
	@Column(name = "SIGN_CHEMISTRY")	
	public Integer getSignChemistry() {
		return signChemistry;
	}

	public void setSignChemistry(Integer signChemistry) {
		this.signChemistry = signChemistry;
	}	
			
	@Column(name = "SIGN_PHYSICS")	
	public Integer getSignPhysics() {
		return signPhysics;
	}

	public void setSignPhysics(Integer signPhysics) {
		this.signPhysics = signPhysics;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}