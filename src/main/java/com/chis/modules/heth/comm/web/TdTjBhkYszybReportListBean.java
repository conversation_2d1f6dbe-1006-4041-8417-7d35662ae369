package com.chis.modules.heth.comm.web;

import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.heth.comm.service.TdTjBhkYszybReportServiceImpl;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;

/**
 * <p>描述 体检疑似职业病报告情况列表</p>
 *
 * @ClassAuthor gongzhe, 2022/6/24 14:22,TdTjBhkYszybReportListBean
 */
@ManagedBean(name = "tdTjBhkYszybReportListBean")
@ViewScoped
public class TdTjBhkYszybReportListBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;
    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private final TdTjBhkYszybReportServiceImpl reportService = SpringContextHolder.getBean(TdTjBhkYszybReportServiceImpl.class);

    /***************查询条件开始************************/
    /**
     * 检查机构地区
     */
    private Integer searchZoneId;
    private String searchZoneCode;
    private String searchZoneName;
    private List<TsZone> searchZoneList;
    /**
     * 职业健康检查机构
     */
    private String searchUnitId;
    private String searchUnitName;
    /**
     * 体检日期
     */
    private Date searchDateStart;
    private Date searchDateEnd;
    private TsZone tsZone;
    /***************查询条件结束************************/
    /**
     * 导出数据
     */
    private List<Object[]> returnedDataList;

    public TdTjBhkYszybReportListBean() {
        super.ifSQL = true;
        init();
        this.searchAction();
    }

    private void init() {
        tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.searchZoneCode = tsZone.getZoneCode();
        this.searchZoneName = tsZone.getZoneName();
        this.searchZoneId = tsZone.getRid();
        this.searchZoneList = this.systemModuleService.findZoneListICanSee(false, tsZone.getZoneCode());
        //当天-1年
        searchDateStart = DateUtils.addYears(new Date(), -1);
        searchDateEnd = new Date();
    }

    @Override
    public void searchAction() {
        if (searchDateStart == null || searchDateEnd == null) {
            JsfUtil.addErrorMessage("体检日期不能为空！");
            return;
        }
        super.searchAction();
    }

    public void selUnitAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 625, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add(searchUnitId);
        paramMap.put("selectIds", paramList);
        List<String> paramList2 = new ArrayList<>();
        paramList2.add(searchZoneCode);
        paramMap.put("searchZoneCode", paramList2);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
    }

    /**
     * <p>方法描述：处理选择的体检机构</p>
     *
     * @MethodAuthor qrr, 2020年4月22日, clearSelectWorkAge
     */
    public void onSelectUnitAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TbTjSrvorg> list = (List<TbTjSrvorg>) selectedMap.get("selectPros");
            if (null != list && list.size() > 0) {
                StringBuffer names = new StringBuffer();
                StringBuffer ids = new StringBuffer();
                for (TbTjSrvorg t : list) {
                    names.append("，").append(t.getUnitName());
                    ids.append(",").append(t.getTsUnit().getRid());
                }
                searchUnitId = ids.substring(1);
                this.searchUnitName = names.substring(1);
            }
        }
    }

    /**
     * <p>方法描述：清空单位</p>
     *
     * @MethodAuthor qrr, 2020年4月22日, clearSelectWorkAge
     */
    public void clearUnit() {
        searchUnitId = null;
        this.searchUnitName = null;
    }

    /**
     * <p>描述 地区移除省</p>
     *
     * @param fullname
     * @return java.lang.String
     * @MethodAuthor gongzhe, 2022/6/22 16:59,removeProv
     */
    private String removeProv(String fullname) {
        if (StringUtils.isNotBlank(fullname)) {
            int index = fullname.indexOf("_");
            if (index != -1) {
                return fullname.substring(index + 1);
            }
        }
        return fullname;
    }

    @Override
    public void processData(List<?> list) {
        if (!CollectionUtils.isEmpty(list)) {
            List<Object[]> result = (List<Object[]>) list;
            for (Object[] o : result) {
                //地区去除省
                o[0] = removeProv((String) o[0]);
                //总数
                o[2] = o[2] == null ? new BigDecimal(0) : o[2];
                BigDecimal total = (BigDecimal) o[2];
                //报告数
                o[4] = o[4] == null ? new BigDecimal(0) : o[4];
                BigDecimal rptTotal = (BigDecimal) o[4];
                //不及时数
                BigDecimal outTime = o[6] == null ? new BigDecimal(0) : (BigDecimal) o[6];
                //及时数 = 报告数 - 不及时数
                BigDecimal inTime = rptTotal.subtract(outTime);
                //未报告数 = 总数-报告数
                o[3] = total.subtract(rptTotal);
                //报告率 = 报告数/疑似职业病例数*100%，四舍五入保留2位小数
                if (rptTotal.intValue() == 0) {
                    o[5] = 0;
                } else if (rptTotal.equals(total)) {
                    o[5] = 100;
                } else {
                    o[5] = (rptTotal.multiply(new BigDecimal(100)).divide(total, 2, BigDecimal.ROUND_HALF_UP)).stripTrailingZeros().toPlainString();
                }
                //报告及时率 = “是否及时”字段都为“是”的疑似职业病报告数/ 疑似职业病报告数 *100%，四舍五入保留2位小数
                if (inTime.intValue() == 0) {
                    o[7] = 0;
                } else if (inTime.equals(rptTotal)) {
                    o[7] = 100;
                } else {
                    o[7] = (inTime.multiply(new BigDecimal(100)).divide(rptTotal, 2, BigDecimal.ROUND_HALF_UP)).stripTrailingZeros().toPlainString();
                }
            }
        }
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder();
        sb.append(" WITH RPT_TABLE AS(");
        sb.append("    SELECT TT.RID,TT.REL_BHK_ID,TT.RPT_UNIT_ID AS UNIT_ID FROM TD_ZW_YSZYB_RPT TT ");
        sb.append("     LEFT JOIN TD_TJ_BHK T ON TT.REL_BHK_ID = T.RID");
        sb.append("    INNER JOIN TS_UNIT T2 ON TT.RPT_UNIT_ID = T2.RID");
        sb.append("    LEFT JOIN TS_ZONE T5 ON T2.ZONE_ID = T5.RID");
        sb.append("    INNER JOIN TD_ZW_BGK_LAST_STA TT4 ON TT4.BUS_ID = TT.RID AND TT4.CART_TYPE = 2 AND TT4.STATE != 0");
        sb.append("    INNER JOIN TS_SIMPLE_CODE TT3 ON TT3.RID = TT.SOURCE_ID AND  TT3.EXTENDS1 = 1");
        sb.append("    WHERE NVL(TT.DEL_MARK, 0) = 0 ");
        sb.append(fillHqlParam());
        sb.append("),OUTTIME_TABLE AS(");
        sb.append("    SELECT BUS_ID,REL_BHK_ID,UNIT_ID FROM (");
        sb.append("    SELECT F.BUS_ID,T.REL_BHK_ID,UNIT_ID, COUNT(1) AS OUTTIME FROM  ");
        sb.append(" TD_ZW_BGK_FLOW F INNER JOIN  RPT_TABLE T ON F.BUS_ID = T.RID");
        sb.append("     WHERE F.CART_TYPE = 2 AND F.IF_IN_TIME = 0 ");
        sb.append("    GROUP BY F.BUS_ID, T.REL_BHK_ID, T.UNIT_ID) WHERE OUTTIME > 0 ");
        sb.append(" ),OCC_TABLE AS (");
        sb.append(" SELECT BHKORG_ID,COUNT(1) AS total FROM (");
        sb.append(" SELECT ST.BHK_ID, ST.OCC_DISEID,T.BHKORG_ID ");
        sb.append(" FROM TD_TJ_SUPOCCDISELIST ST LEFT JOIN TD_TJ_BHK T ON ST.BHK_ID = T.RID");
        sb.append(" LEFT JOIN TB_TJ_CRPT CT ON CT.RID = T.CRPT_ID ");
        sb.append(" INNER JOIN TB_TJ_SRVORG T1 ON T.BHKORG_ID = T1.RID");
        sb.append(" INNER JOIN TS_UNIT T2 ON T1.REG_ORGID = T2.RID");
        sb.append(" LEFT JOIN TS_ZONE T5 ON T2.ZONE_ID = T5.RID");
        sb.append(" WHERE 1 = 1 AND CT.INTER_PRC_TAG = 1 ");
        sb.append(fillHqlParam());
        sb.append(" GROUP BY ST.BHK_ID, ST.OCC_DISEID, T.BHKORG_ID)");
        sb.append(" GROUP BY BHKORG_ID");
        sb.append(" ),RPT_COUNT_TABLE AS (");
        sb.append(" SELECT UNIT_ID,COUNT(1) AS rptTotal FROM RPT_TABLE GROUP BY UNIT_ID ");
        sb.append(" ),OUTTIME_COUNT_TABLE AS (");
        sb.append(" SELECT UNIT_ID,COUNT(1) AS outTimeTotal FROM OUTTIME_TABLE GROUP BY UNIT_ID ");
        sb.append(") SELECT T5.FULL_NAME, T1.UNIT_NAME,B1.total, R1.rptTotal, R2.outTimeTotal FROM OCC_TABLE B1 ");
        sb.append(" INNER JOIN TB_TJ_SRVORG T1 ON B1.BHKORG_ID = T1.RID");
        sb.append(" INNER JOIN TS_UNIT T2 ON T1.REG_ORGID = T2.RID");
        sb.append(" LEFT JOIN TS_ZONE T5 ON T2.ZONE_ID = T5.RID");
        sb.append(" LEFT JOIN RPT_COUNT_TABLE R1 ON R1.UNIT_ID = T2.RID ");
        sb.append(" LEFT JOIN OUTTIME_COUNT_TABLE R2 ON R2.UNIT_ID = T2.RID ");
        sb.append(" ORDER BY T5.ZONE_GB, T1.UNIT_NAME ");
        String h1 = "SELECT FULL_NAME,UNIT_NAME,TOTAL ,'' AS noRpt,rptTotal,'' AS rptRate,outTimeTotal,'' as inTimeRate FROM (" + sb + ")";
        String h2 = "SELECT COUNT(*) FROM (" + sb + ")";
        return new String[]{h1, h2};
    }

    private String fillHqlParam() {
        StringBuilder sb = new StringBuilder(" AND T.BHK_TYPE IN (3, 4)");
        if (this.searchDateStart != null) {
            sb.append(" AND T.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(searchDateStart, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
        }
        if (this.searchDateEnd != null) {
            sb.append(" AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(searchDateEnd, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
        }
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sb.append(" AND T5.ZONE_GB LIKE '" + ZoneUtil.zoneSelect(this.searchZoneCode) + "%' ");
        }
        if (this.searchUnitId != null) {
            sb.append(" AND T2.RID in ( ").append(searchUnitId).append(")");
        }
        return sb.toString();
    }

    /**
     * <p>方法描述：导出验证</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-06-27
     **/
    public void exportReturnDataAction() {

        if (searchDateStart == null || searchDateEnd == null) {
            JsfUtil.addErrorMessage("体检日期不能为空！");
            return;
        }
        //无数据导出提示
        returnedDataList = getReturnedDataList(searchZoneCode, searchUnitId, searchDateStart, searchDateEnd);
        if (CollectionUtils.isEmpty(returnedDataList)) {
            JsfUtil.addErrorMessage("无数据导出！");
            return;
        }

        RequestContext.getCurrentInstance().execute("getDownloadFileClick()");
    }

    /**
     * <p>方法描述：导出方法</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-06-27
     **/
    public DefaultStreamedContent export() {
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("showStatus();");
        Integer[] columnWidths = new Integer[]{36, 36, 10, 15, 20, null, null, null, 36, 36, null};
        ExcelExportUtil excelExportUtil = new ExcelExportUtil("体检疑似职业病漏报名单", getReturnedDataExcelHeaders(), pakExcelExportDataList(returnedDataList));
        excelExportUtil.setFrozenPaneRowsNum(2);
        excelExportUtil.setColumnWidths(columnWidths);
        Workbook wb = excelExportUtil.exportExcel("");
        if (wb != null) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                String fileName = "体检疑似职业病漏报名单.xlsx";
                fileName = URLEncoder.encode(fileName, "UTF-8");
                wb.write(baos);
                baos.flush();
                byte[] aa = baos.toByteArray();
                context.execute("hideStatus();");
                return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
            } catch (Exception e) {
                JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            }
        }
        context.execute("hideStatus();");
        return null;
    }


    /**
     * <p>方法描述：pakExcelExportDataList</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-06-27
     **/
    private List<ExcelExportObject[]> pakExcelExportDataList(List<Object[]> returnedDataList) {
        List<ExcelExportObject[]> dataList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(returnedDataList)) {
            for (Object[] obj : returnedDataList) {
                ExcelExportObject[] objects = new ExcelExportObject[11];
                objects[0] = new ExcelExportObject(obj[3], XSSFCellStyle.ALIGN_LEFT);
                objects[1] = new ExcelExportObject(obj[4], XSSFCellStyle.ALIGN_LEFT);
                objects[2] = new ExcelExportObject(obj[5], XSSFCellStyle.ALIGN_CENTER);
                objects[3] = new ExcelExportObject(obj[6], XSSFCellStyle.ALIGN_CENTER);
                objects[4] = new ExcelExportObject(obj[7], XSSFCellStyle.ALIGN_CENTER);
                objects[5] = new ExcelExportObject(obj[8], XSSFCellStyle.ALIGN_CENTER);
                objects[6] = new ExcelExportObject(obj[9], XSSFCellStyle.ALIGN_CENTER);
                objects[7] = new ExcelExportObject(obj[10], XSSFCellStyle.ALIGN_CENTER);
                objects[8] = new ExcelExportObject(obj[11], XSSFCellStyle.ALIGN_LEFT);
                objects[9] = new ExcelExportObject(obj[12], XSSFCellStyle.ALIGN_LEFT);
                objects[10] = new ExcelExportObject(obj[13], XSSFCellStyle.ALIGN_CENTER);
                dataList.add(objects);
            }
        }
        return dataList;
    }


    /**
     * <p>方法描述：导出-表头</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-06-27
     **/
    private String[] getReturnedDataExcelHeaders() {
        return new String[]{"检查机构地区", "职业健康检查机构", "姓名", "证件类型", "证件号码", "疑似职业病名称", "体检编号", "体检日期", "用人单位地区", "用人单位名称", "社会信用代码"};
    }

    /**
     * <p>方法描述：最终导出数据</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-06-27
     **/
    public List<Object[]> getReturnedDataList(String searchZoneCode, String searchUnitId, Date searchDateStart, Date searchDateEnd) {
        return reportService.getReturnedDataList(searchZoneCode, searchUnitId, searchDateStart, searchDateEnd);
    }

    public Integer getSearchZoneId() {
        return searchZoneId;
    }

    public void setSearchZoneId(Integer searchZoneId) {
        this.searchZoneId = searchZoneId;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public String getSearchUnitId() {
        return searchUnitId;
    }

    public void setSearchUnitId(String searchUnitId) {
        this.searchUnitId = searchUnitId;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public Date getSearchDateStart() {
        return searchDateStart;
    }

    public void setSearchDateStart(Date searchDateStart) {
        this.searchDateStart = searchDateStart;
    }

    public Date getSearchDateEnd() {
        return searchDateEnd;
    }

    public void setSearchDateEnd(Date searchDateEnd) {
        this.searchDateEnd = searchDateEnd;
    }
}
