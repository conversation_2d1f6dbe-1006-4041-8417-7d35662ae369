package com.chis.modules.heth.comm.service;

import com.chis.common.utils.CollectionUtil;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <p>类描述： </p>
 *
 * @ClassAuthor: yzz
 * @date： 2022年07月29日
 **/
@Service
@Transactional(readOnly = false)
public class CrptCheckServiceImpl extends AbstractTemplate {



    /**
     * <p>方法描述：保存流程信息</p>
     * @MethodAuthor： yzz
     * @Date：2022-07-29
     * @param tbTjCrpt 用人单位
     *        checkLevel  审核级别
     *        zoneType  当前登录人的地区级别
     **/
    public void saveProcessInfo(TbTjCrpt tbTjCrpt,String checkLevel,String zoneType,Integer checkState,String checkRst) {
        Integer statue=null;
        if("2".equals(checkLevel)){
            //省直属
            if(StringUtils.isNotBlank(tbTjCrpt.getTsZoneByZoneId().getIfProvDirect())
                    &&"1".equals(tbTjCrpt.getTsZoneByZoneId().getIfProvDirect())){
                if(StringUtils.isNotBlank(zoneType)&&"3".equals(zoneType)){
                    //市级  通过
                    if(checkState==1){
                        statue=3;
                    }
                }else if(StringUtils.isNotBlank(zoneType)&&"2".equals(zoneType)){
                    //省级
                    if(checkState==1) {
                        statue=5;
                    }
                }
            }else{
                //非省直属
                if(StringUtils.isNotBlank(zoneType)&&"4".equals(zoneType)){
                    //区县级
                    if(checkState==1) {
                        //通过
                        statue=1;
                    }
                }
            }
        }else if("3".equals(checkLevel)){
            //市直属
            if(StringUtils.isNotBlank(tbTjCrpt.getTsZoneByZoneId().getIfCityDirect())
                    &&"1".equals(tbTjCrpt.getTsZoneByZoneId().getIfCityDirect())){
                //审核机构时市级
                if(StringUtils.isNotBlank(zoneType)&&"3".equals(zoneType)){
                    //市级通过
                    if(checkState==1){
                        statue=3;
                    }
                }
            }else{
                //非市直属  区县
                if(StringUtils.isNotBlank(zoneType)&&"4".equals(zoneType)){
                    //区县级 通过
                    if(checkState==1) {
                        statue=1;
                    }
                }
            }
        }
        saveLastSta(tbTjCrpt.getRid(), statue, checkRst,checkState, zoneType,false,checkLevel);
        this.updateObj(tbTjCrpt);
    }

    /**
     * <p>方法描述：根据用人单位rid获取历史审核意见</p>
     * @MethodAuthor： yzz
     * @Date：2022-07-29
     **/
    public List<Object[]> findHisotryListByCrpt(Integer rid) {
        StringBuilder sql = new StringBuilder();
        sql.append("select T.CART_TYPE,T.AUDIT_ADV,T.AUDIT_MAN,T.CREATE_DATE,T1.CRPT_NAME,T3.IF_CITY_DIRECT,T3.IF_PROV_DIRECT,T.OPER_FLAG ");
        sql.append(" from TD_ZW_BGK_FLOW T ");
        sql.append(" LEFT JOIN TB_TJ_CRPT T1 on T.BUS_ID=T1.RID ");
        sql.append(" LEFT JOIN TS_ZONE T3 ON T1.ZONE_ID = T3.RID " );
        sql.append(" where T.CART_TYPE=10 and T1.RID= ").append(rid);
        sql.append(" ORDER BY T.CREATE_DATE desc ");
        return this.findSqlResultList(sql.toString());
    }

    /**
     * <p>方法描述：submitProcessInfo</p>
     * @MethodAuthor： yzz
     * @Date：2022-07-30
     **/
    public void submitProcessInfo(TbTjCrpt tbTjCrpt, String checkLevel, String zoneType,String platVersion,Integer checkState,String checkRst) {
        Integer state=null;
        Integer flag=null;
        if("2".equals(checkLevel)){
            //省直属
            if(StringUtils.isNotBlank(tbTjCrpt.getTsZoneByZoneId().getIfProvDirect())
                    &&"1".equals(tbTjCrpt.getTsZoneByZoneId().getIfProvDirect())){
                if(StringUtils.isNotBlank(zoneType)&&"3".equals(zoneType)){
                    //市级
                    if(checkState==1){
                        state=5;
                        flag=33;
                    }
                }else if(StringUtils.isNotBlank(zoneType)&&"2".equals(zoneType)){
                    //省级
                    if(checkState==1) {
                        state=7;
                        flag=42;
                    }
                }
            }else{
                //非省直属
                if(StringUtils.isNotBlank(zoneType)&&"4".equals(zoneType)){
                    //区县  市级平台
                    if(StringUtils.isNotBlank(platVersion)&&"1".equals(platVersion)){
                        if(checkState==1) {
                            state=3;
                            flag=31;
                        }
                    }else if(StringUtils.isNotBlank(platVersion)&&"2".equals(platVersion)){
                        //区县  省级平台
                        if(checkState==1) {
                            state=5;
                            flag=43;
                        }
                    }
                }else if(StringUtils.isNotBlank(zoneType)&&"3".equals(zoneType)){
                    //市级 通过
                    if(checkState==1) {
                        state=5;
                        flag=33;
                    }else if(checkState==2){
                        //退回
                        state=4;
                        flag=21;
                    }
                }else if(StringUtils.isNotBlank(zoneType)&&"2".equals(zoneType)){
                    //省级 通过
                    if(checkState==1) {
                        state=7;
                        flag=42;
                    }else if(checkState==2){
                        //退回
                        state=6;
                        flag=22;
                    }
                }
            }
        }else if("3".equals(checkLevel)){
            //市直属
            if(StringUtils.isNotBlank(tbTjCrpt.getTsZoneByZoneId().getIfCityDirect())
                    &&"1".equals(tbTjCrpt.getTsZoneByZoneId().getIfCityDirect())){
                    //审核机构时市级
                if(StringUtils.isNotBlank(zoneType)&&"3".equals(zoneType)){
                    if(checkState==1) {
                        state=5;
                        flag=41;
                    }
                }else if(StringUtils.isNotBlank(zoneType)&&"2".equals(zoneType)){
                    //省级 通过
                    if(checkState==1) {
                        state=7;
                        flag=42;
                    }else if(checkState==2){
                        //退回
                        state=6;
                        flag=32;
                    }
                }
            }else{
                //非市直属  区县
                if(StringUtils.isNotBlank(zoneType)&&"4".equals(zoneType)){
                    if(checkState==1) {
                        state=3;
                        flag=31;
                    }
                }else if(StringUtils.isNotBlank(zoneType)&&"3".equals(zoneType)){
                    //市级
                    if(checkState==1) {
                        state=5;
                        flag=41;
                    }else if(checkState==2){
                        //退回
                        state=4;
                        flag=21;
                    }
                }else if(StringUtils.isNotBlank(zoneType)&&"2".equals(zoneType)){
                    //省级
                    if(checkState==1) {
                        state=7;
                        flag=42;
                    }else if(checkState==2){
                        //退回
                        state=6;
                        flag=32;
                    }
                }
            }
        }
        if(state!=null&&flag!=null){
            saveLastSta(tbTjCrpt.getRid(),state,checkRst,checkState,zoneType,true,checkLevel);
            saveFlow(tbTjCrpt.getRid(),flag,checkRst);
            this.updateObj(tbTjCrpt);
        }

    }

    /**
     * <p>方法描述：存储 最新状态表</p>
     * @MethodAuthor： yzz
     * @Date：2022-07-30
     **/
    public void saveLastSta(Integer crptRid,Integer statue,String checkRst,Integer checkState,String zoneType,boolean ifSubmit,String checkLevel){
        //查询数据库是否已存数据
        TdZwBgkLastSta lastSta=this.findOneByHql("select T from TdZwBgkLastSta T where T.cartType=10 and T.busId="+crptRid,TdZwBgkLastSta.class);
        if(lastSta==null){
            lastSta=new TdZwBgkLastSta();
        }
        if("4".equals(zoneType)){
            //初审
            lastSta.setCountyRcvDate(new Date());
            lastSta.setCountAuditAdv(checkRst);
            lastSta.setCountyChkPsn(Global.getUser().getUsername());
            lastSta.setCountyRst(checkState);
            if(ifSubmit){
                lastSta.setCityRst(null);
                lastSta.setCityAuditAdv(null);
                lastSta.setProAuditAdv(null);
                lastSta.setProRst(null);
            }
        }else if("3".equals(zoneType)){
            //复审
            lastSta.setCityRcvDate(new Date());
            lastSta.setCityAuditAdv(checkRst);
            lastSta.setCityChkPsn(Global.getUser().getUsername());
            lastSta.setCityRst(checkState);
            if(ifSubmit){
                if(checkState==1){
                    lastSta.setProAuditAdv(null);
                    lastSta.setProRst(null);
                }else{
                    lastSta.setCountAuditAdv(null);
                    lastSta.setCountyRst(null);
                }
            }
        }else if("2".equals(zoneType)){
            //终审
            lastSta.setProRcvDate(new Date());
            lastSta.setProAuditAdv(checkRst);
            lastSta.setProChkPsn(Global.getUser().getUsername());
            lastSta.setProRst(checkState);
            if(ifSubmit&&checkState==2){
                if("2".equals(checkLevel)){
                    lastSta.setCountAuditAdv(null);
                    lastSta.setCountyRst(null);
                }else{
                    lastSta.setCityRst(null);
                    lastSta.setCityAuditAdv(null);
                }
            }
        }
        if(statue!=null){
            lastSta.setState(statue);
        }
        if(lastSta.getRid()==null){
            lastSta.setCartType(10);
            lastSta.setBusId(crptRid);
            lastSta.setCreateDate(new Date());
            lastSta.setCreateManid(Global.getUser().getRid());
            this.save(lastSta);
        }
    }

    /**
     * <p>方法描述：保存 历史流程表</p>
     * @MethodAuthor： yzz
     * @Date：2022-07-30
     **/
    public void saveFlow(Integer crptRid,Integer flag,String checkRst){
        TdZwBgkFlow tdZwBgkFlow=new TdZwBgkFlow();
        tdZwBgkFlow.setCartType(10);
        tdZwBgkFlow.setBusId(crptRid);
        tdZwBgkFlow.setRcvDate(new Date());
        tdZwBgkFlow.setFkBySmtPsnId(Global.getUser());
        tdZwBgkFlow.setOperFlag(flag);
        tdZwBgkFlow.setAuditAdv(checkRst);
        tdZwBgkFlow.setAuditMan(Global.getUser().getUsername());
        tdZwBgkFlow.setCreateDate(new Date());
        tdZwBgkFlow.setCreateManid(Global.getUser().getRid());
        this.save(tdZwBgkFlow);
    }


    /**
     * <p>方法描述：查询最新状态表</p>
     * @MethodAuthor： yzz
     * @Date：2022-07-29
     **/
    public List<Object[]> findLastStas(Integer busId){
        StringBuilder sql = new StringBuilder();
        sql.append("select T.RID,T.CART_TYPE,T.BUS_ID, " );
        sql.append(" T.COUNTY_RCV_DATE,T.CITY_RCV_DATE,T.PRO_RCV_DATE, ");
        sql.append(" T.COUNTY_AUDIT_ADV,T.CITY_AUDIT_ADV,T.PRO_AUDIT_ADV, ");
        sql.append(" T.COUNTY_CHK_PSN,T.CITY_CHK_PSN,T.PRO_CHK_PSN,T.STATE,  ");
        sql.append(" T.COUNTY_RST,T.CITY_RST,T.PRO_RST ");
        sql.append(" from TD_ZW_BGK_LAST_STA T ");
        sql.append(" where T.BUS_ID= ").append(busId);
        sql.append(" and T.CART_TYPE=10 ");
        sql.append(" ORDER BY T.CREATE_DATE desc ");
        return this.findSqlResultList(sql.toString());
    }

    /**
     * <p>方法描述：通过社会信用代码获取企业部分属性信息 </p>
     * @MethodAuthor： pw 2022/7/30
     **/
    @Transactional(readOnly = true)
    public List<Object[]> findRelationUnitListByInstitutionCode(String institutionCode){
        if(StringUtils.isBlank(institutionCode)){
            return Collections.EMPTY_LIST;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        //rid 地区 单位名称 社会信用代码 是否分支机构 上级单位名称 单位地址
        sqlBuffer.append(" SELECT T.RID,T2.FULL_NAME,T.CRPT_NAME,T.INSTITUTION_CODE,T.IF_SUB_ORG, ")
                .append(" T1.CRPT_NAME AS UPPERCRPTNAME,T.ADDRESS ").append(" FROM TB_TJ_CRPT T ")
                .append(" LEFT JOIN TB_TJ_CRPT T1 ON T.UPPER_UNIT_ID = T1.RID ")
                .append(" LEFT JOIN TS_ZONE T2 ON T.ZONE_ID = T2.RID ")
                .append(" WHERE T.INSTITUTION_CODE='").append(institutionCode).append("' ")
                .append(" ORDER BY T.IF_SUB_ORG,T.CRPT_NAME ");
        return this.findSqlResultList(sqlBuffer.toString());
    }

    /**
     * <p>方法描述：将分支机构改为主体机构</p>
     * @param editUpperUnitId 准备改为主体机构的分支机构rid
     * @param subOrgRidList 准备修改是否分支机构以及上级机构rid的机构rid列表
     * @MethodAuthor： pw 2022/7/30
     **/
    public void changeUpperUnit(Integer editUpperUnitId, List<Integer> subOrgRidList){
        //修改成分支机构
        String updateSubOrgSql = "UPDATE TB_TJ_CRPT SET UPPER_UNIT_ID=:upperUnitId ,IF_SUB_ORG=1 WHERE RID IN (:subOrgRidList)";
        Map<String,Object> updateMap = new HashMap<>();
        updateMap.put("upperUnitId",editUpperUnitId);
        updateMap.put("subOrgRidList",subOrgRidList);
        this.executeSql(updateSubOrgSql, updateMap);
        //修改成主体机构
        updateSubOrgSql = "UPDATE TB_TJ_CRPT SET UPPER_UNIT_ID=NULL,IF_SUB_ORG=0 WHERE RID="+editUpperUnitId;
        this.executeSql(updateSubOrgSql, null);
    }

    /**
     * <p>方法描述：验证是否同社会信用代码同名称有重复 </p>
     * @param crptName 查询的企业名称
     * @param institutionCode 查询的企业社会信用代码
     * @param rid 当前企业的rid
     * @MethodAuthor： pw 2022/8/1
     **/
    @Transactional(readOnly = true)
    public boolean checkIfNameAndInstitutionCodeExist(String crptName, String institutionCode, Integer rid){
        StringBuffer querySqlBuffer = new StringBuffer();
        querySqlBuffer.append(" SELECT COUNT(1) FROM TB_TJ_CRPT T WHERE 1=1 ");
        Map<String,Object> paramMap = new HashMap<>();
        if(StringUtils.isNotBlank(crptName)){
            querySqlBuffer.append(" AND T.CRPT_NAME=:crptName ");
            paramMap.put("crptName",crptName);
        }
        if(StringUtils.isNotBlank(institutionCode)){
            querySqlBuffer.append(" AND T.INSTITUTION_CODE=:institutionCode ");
            paramMap.put("institutionCode",institutionCode);
        }
        if(null != rid){
            querySqlBuffer.append(" AND T.RID<>:rid ");
            paramMap.put("rid",rid);
        }
        return this.findCountBySql(querySqlBuffer.toString(), paramMap) > 0;
    }

    /**
     * <p>方法描述：撤销审核 </p>
     * @MethodAuthor： pw 2022/8/2
     **/
    public void cancelCrptCheck(TdZwBgkLastSta bgkLastSta, Integer bgkFlowState){
        Integer busId = bgkLastSta.getBusId();
        Integer cartType = bgkLastSta.getCartType();
        if(null == busId || null == cartType || null == bgkFlowState){
            return;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT T.RID FROM TD_ZW_BGK_FLOW T ").append(" WHERE T.BUS_ID=").append(busId)
                .append(" AND T.CART_TYPE=").append(cartType).append(" AND T.OPER_FLAG=").append(bgkFlowState)
                .append(" ORDER BY T.CREATE_DATE DESC ");
        List<Object> ridList = this.findSqlResultList(sqlBuffer.toString());
        if(CollectionUtils.isEmpty(ridList)){
            return;
        }
        Integer rid = Integer.parseInt(ridList.get(0).toString());
        this.delete(TdZwBgkFlow.class, rid);
        this.upsertEntity(bgkLastSta);
    }

    /**
     * <p>描述 保存变更地区</p>
     *
     * @param tbTjCrpt
     * @param checkLevel
     * @param zoneType
     * @param checkState
     * @param checkRst
     * @MethodAuthor gongzhe,2022/8/2 17:48,saveChangeZone
     * @return void
     */
    public void saveChangeZone(TbTjCrpt tbTjCrpt,String checkLevel,String platVersion,String zoneType,Integer checkState,String checkRst){
        //根据变更后地区更新状态
        //根据当前审核机构地区保存操作标识
        Integer flag=null;
        Integer statue=null;
        if ("2".equals(checkLevel)) {
            //省直属
            if("1".equals(tbTjCrpt.getTsZoneByZoneId().getIfProvDirect())){
                if ("1".equals(platVersion)) {
                    statue = 3;
                } else if ("2".equals(platVersion)) {
                    //省平台
                    statue = 5;
                }
            }else{
                //非省直属
                //区县级
                statue=1;
            }
            //区县提交
            if (StringUtils.isNotBlank(zoneType) && "4".equals(zoneType)) {
                //区县  市级平台
                if ("1".equals(platVersion)) {
                    flag = 31;
                } else if ("2".equals(platVersion)) {
                    //省平台
                    flag = 43;
                }
            }
        } else if ("3".equals(checkLevel)) {
            //市直属
            if("1".equals(tbTjCrpt.getTsZoneByZoneId().getIfCityDirect())){
                statue=3;
            }else{
                //非市直属
                statue=1;
            }
            // 区县
            if (StringUtils.isNotBlank(zoneType) && "4".equals(zoneType)) {
                flag = 31;
            } else if (StringUtils.isNotBlank(zoneType) && "3".equals(zoneType)) {
                //市级
                flag = 41;
            }
        }
        if(flag!=null){
            saveLastSta(tbTjCrpt.getRid(), statue, "",checkState, zoneType,true, checkLevel);
            //更新地区
            String sql = " UPDATE TB_TJ_CRPT SET ZONE_ID = "+tbTjCrpt.getTsZoneByZoneId().getRid()+" WHERE RID = "+tbTjCrpt.getRid();
            em.createNativeQuery(sql).executeUpdate();
            saveFlow(tbTjCrpt.getRid(),flag,checkRst);
        }
    }

    /**
     * 批量处理最新状态表及历史审核流程表
     *
     * @param dataMap key: 状态: sa(新增)/se+状态(更新);操作标识: opa+操作标识(新增) value:rid List
     * @return 成功条数
     */
    public int batchCrptCheck(Map<String, List<Integer>> dataMap, String accountLevel, Integer checkState, String checkRst, boolean checkLevel2) {
        int count = 0;
        String latestStateStr = pakLatestState(accountLevel, checkState, checkLevel2);
        //最新状态表操作(新增)
        if (dataMap.containsKey("sa")) {
            batchInsertLatestState(dataMap.get("sa"));
        }
        for (String key : dataMap.keySet()) {
            List<Integer> ridList = dataMap.get(key);
            if (key.startsWith("se") && key.length() > 2) {
                //最新状态表操作(更新)
                String state = StringUtils.objectToString(key.substring(2));
                batchUpdateLatestState(ridList, checkRst, checkState, state, latestStateStr);
            } else if (key.startsWith("opa") && key.length() > 3) {
                //历史审核流程表操作(新增)
                String opFlag = StringUtils.objectToString(key.substring(3));
                batchInsertCheckFlow(ridList, opFlag, checkRst);
                count += ridList.size();
            }
        }
        return count;
    }

    private String pakLatestState(String accountLevel, Integer checkState, boolean checkLevel2) {
        String sql = "";
        if ("4".equals(accountLevel)) {
            sql += ", COUNTY_RCV_DATE = TO_DATE(:nowDate, 'YYYY-MM-DD HH24:MI:SS')";
            sql += ", COUNTY_CHK_PSN = :userName";
            sql += ", COUNTY_AUDIT_ADV = :checkRst";
            sql += ", COUNTY_RST = :checkState";
            sql += ", CITY_AUDIT_ADV = NULL";
            sql += ", CITY_RST = NULL";
            sql += ", PRO_AUDIT_ADV = NULL";
            sql += ", PRO_RST = NULL";
        } else if ("3".equals(accountLevel)) {
            sql += ", CITY_RCV_DATE = TO_DATE(:nowDate, 'YYYY-MM-DD HH24:MI:SS')";
            sql += ", CITY_CHK_PSN = :userName";
            sql += ", CITY_AUDIT_ADV = :checkRst";
            sql += ", CITY_RST = :checkState";
            if (checkState == 1) {
                sql += ", PRO_AUDIT_ADV = NULL";
                sql += ", PRO_RST = NULL";
            } else {
                sql += ", COUNTY_AUDIT_ADV = NULL";
                sql += ", COUNTY_RST = NULL";
            }
        } else if ("2".equals(accountLevel)) {
            sql += ", PRO_RCV_DATE = TO_DATE(:nowDate, 'YYYY-MM-DD HH24:MI:SS')";
            sql += ", PRO_CHK_PSN = :userName";
            sql += ", PRO_AUDIT_ADV = :checkRst";
            sql += ", PRO_RST = :checkState";
            if (checkState == 2) {
                if (checkLevel2) {
                    sql += ", COUNTY_AUDIT_ADV = NULL";
                    sql += ", COUNTY_RST = NULL";
                } else {
                    sql += ", CITY_AUDIT_ADV = NULL";
                    sql += ", CITY_RST = NULL";
                }
            }
        }
        return sql.substring(2);
    }

    private void batchInsertLatestState(List<Integer> ridList) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("nowDate", DateUtils.getDate("yyyy-MM-dd HH:mm:ss"));
        paramMap.put("userRid", Global.getUser().getRid());
        StringBuilder baseSql = new StringBuilder();
        baseSql.append("INSERT INTO TD_ZW_BGK_LAST_STA (RID, CART_TYPE, BUS_ID, CREATE_DATE, CREATE_MANID) ");
        baseSql.append("SELECT TD_ZW_BGK_LAST_STA_SEQ.NEXTVAL, T.* FROM ( ");
        int length = ridList.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = ridList.subList(i * 1000, endIndex);
            StringBuilder sql = new StringBuilder(baseSql.toString());
            for (Integer rid : subList) {
                sql.append("SELECT ");
                sql.append("10 t1, ");
                sql.append(rid).append(" t2, ");
                sql.append("TO_DATE(:nowDate, 'YYYY-MM-DD HH24:MI:SS') t3, ");
                sql.append(":userRid t4 ");
                sql.append("FROM DUAL UNION ALL ");
            }
            this.executeSql(sql.substring(0, sql.length() - 10) + " ) T ", paramMap);
        }
    }

    private void batchUpdateLatestState(List<Integer> ridList, String checkRst, Integer checkState, String state, String latestStateStr) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("nowDate", DateUtils.getDate("yyyy-MM-dd HH:mm:ss"));
        paramMap.put("userName", Global.getUser().getUsername());
        paramMap.put("checkRst", checkRst);
        paramMap.put("checkState", checkState);
        paramMap.put("state", state);
        StringBuilder baseSql = new StringBuilder();
        baseSql.append("UPDATE TD_ZW_BGK_LAST_STA T SET ");
        baseSql.append("STATE = :state, ");
        baseSql.append(latestStateStr).append(" ");
        baseSql.append("WHERE T.CART_TYPE = 10 AND T.BUS_ID IN ( ");
        int length = ridList.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = ridList.subList(i * 1000, endIndex);
            String sql = baseSql + StringUtils.list2string(subList, ",") + ")";
            this.executeSql(sql, paramMap);
        }
    }

    private void batchInsertCheckFlow(List<Integer> ridList, String opFlag, String checkRst) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("nowDate", DateUtils.getDate("yyyy-MM-dd HH:mm:ss"));
        paramMap.put("userName", Global.getUser().getUsername());
        paramMap.put("userRid", Global.getUser().getRid());
        paramMap.put("opFlag", opFlag);
        paramMap.put("checkRst", checkRst);
        StringBuilder baseSql = new StringBuilder();
        baseSql.append("INSERT INTO TD_ZW_BGK_FLOW (RID, CART_TYPE, BUS_ID, OPER_FLAG, RCV_DATE, SMT_PSN_ID, AUDIT_ADV, AUDIT_MAN, CREATE_DATE, CREATE_MANID) ");
        baseSql.append("SELECT TD_ZW_BGK_FLOW_SEQ.NEXTVAL, T.* FROM ( ");
        int length = ridList.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = ridList.subList(i * 1000, endIndex);
            StringBuilder sql = new StringBuilder(baseSql.toString());
            for (Integer rid : subList) {
                sql.append("SELECT ");
                sql.append("10 t1, ");
                sql.append(rid).append(" t2, ");
                sql.append(":opFlag t3, ");
                sql.append("TO_DATE(:nowDate, 'YYYY-MM-DD HH24:MI:SS') t4, ");
                sql.append(":userRid t5, ");
                sql.append(":checkRst t6, ");
                sql.append(":userName t7, ");
                sql.append("TO_DATE(:nowDate, 'YYYY-MM-DD HH24:MI:SS') t8, ");
                sql.append(":userRid t9 ");
                sql.append("FROM DUAL UNION ALL ");
            }
            this.executeSql(sql.substring(0, sql.length() - 10) + " ) T ", paramMap);
        }
    }

    /**
     * 读取企业关系表【TB_TJ_CRPT_INDEPEND】的业务类型【BUS_TYPE】为1的最新填报机构【UNIT_ID】对应检查机构申报记录【TD_ZW_TJORGINFO】
     *
     * @param crptId 企业RID
     * @return 检查机构申报记录
     */
    public TdZwTjorginfoNew findOrgInfoByUnitLatest(Integer crptId) {
        if (ObjectUtil.isEmpty(crptId)) {
            return new TdZwTjorginfoNew();
        }
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "SELECT T.RID, T.ORG_NAME, T.LINK_MAN, T.LINK_TEL " +
                "FROM TB_TJ_CRPT_INDEPEND CI " +
                "LEFT JOIN TD_ZW_TJORGINFO T ON T.ORG_ID = CI.UNIT_ID " +
                "WHERE CI.BUS_TYPE = '1' AND CI.CRPT_ID = :crptId " +
                "ORDER BY CI.RID DESC";
        paramMap.put("crptId", crptId);
        List<Object[]> dataList = CollectionUtil.castList(Object[].class, this.findDataBySqlNoPage(sql, paramMap));
        TdZwTjorginfoNew orgInfo = new TdZwTjorginfoNew();
        if (ObjectUtil.isNotEmpty(dataList)) {
            orgInfo.setRid(ObjectUtil.convert(Integer.class, dataList.get(0)[0]));
            orgInfo.setOrgName(StringUtils.objectToString(dataList.get(0)[1]));
            orgInfo.setLinkMan(StringUtils.objectToString(dataList.get(0)[2]));
            orgInfo.setLinkTel(StringUtils.objectToString(dataList.get(0)[3]));
        }
        return orgInfo;
    }
}
