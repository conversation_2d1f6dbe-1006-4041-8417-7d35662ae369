package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2018-7-6
 */
@Entity
@Table(name = "TD_ZDZYB_SPECIAL_ANALY")
@SequenceGenerator(name = "TdZdzybSpecialAnalyComm", sequenceName = "TD_ZDZYB_SPECIAL_ANALY_SEQ", allocationSize = 1)
public class TdZdzybSpecialAnalyComm implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer analyType;
	private TsSimpleCode fkByBadrsnId;
	private String analyName;
	private String itemStdvalue;
	private Integer xh;
	private String rmk;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZdzybSpecialAnalyComm() {
	}

	public TdZdzybSpecialAnalyComm(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZdzybSpecialAnalyComm")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "ANALY_TYPE")	
	public Integer getAnalyType() {
		return analyType;
	}

	public void setAnalyType(Integer analyType) {
		this.analyType = analyType;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BADRSN_ID")			
	public TsSimpleCode getFkByBadrsnId() {
		return fkByBadrsnId;
	}

	public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
		this.fkByBadrsnId = fkByBadrsnId;
	}	
			
	@Column(name = "ANALY_NAME")	
	public String getAnalyName() {
		return analyName;
	}

	public void setAnalyName(String analyName) {
		this.analyName = analyName;
	}	
			
	@Column(name = "ITEM_STDVALUE")	
	public String getItemStdvalue() {
		return itemStdvalue;
	}

	public void setItemStdvalue(String itemStdvalue) {
		this.itemStdvalue = itemStdvalue;
	}	
			
	@Column(name = "XH")	
	public Integer getXh() {
		return xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}	
			
	@Column(name = "RMK")	
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}