package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-11-16
 */
@Entity
@Table(name = "TD_ZWYJ_OTR_CRPT_LIST")
@SequenceGenerator(name = "TdZwyjOtrCrptList", sequenceName = "TD_ZWYJ_OTR_CRPT_LIST_SEQ", allocationSize = 1)
public class TdZwyjOtrCrptList implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwyjOtrWarn fkByMainId;
	private TbTjCrpt fkByCrptId;
	private TsZone fkByCrptZoneId;
	private String crptName;
	private String creditCode;
	private Date beginBhkDate;
	private Date endBhkDate;
	private Integer otrPsns;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	/**超范围服务项目集合*/
	private List<TdZwyjOtrSerItemCrpt> tdZwyjOtrSerItemCrptList;
	/**超范围服务项目*/
	private String items;
	
	public TdZwyjOtrCrptList() {
	}

	public TdZwyjOtrCrptList(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjOtrCrptList")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwyjOtrWarn getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwyjOtrWarn fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_ID")			
	public TbTjCrpt getFkByCrptId() {
		return fkByCrptId;
	}

	public void setFkByCrptId(TbTjCrpt fkByCrptId) {
		this.fkByCrptId = fkByCrptId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_ZONE_ID")			
	public TsZone getFkByCrptZoneId() {
		return fkByCrptZoneId;
	}

	public void setFkByCrptZoneId(TsZone fkByCrptZoneId) {
		this.fkByCrptZoneId = fkByCrptZoneId;
	}	
			
	@Column(name = "CRPT_NAME")	
	public String getCrptName() {
		return crptName;
	}

	public void setCrptName(String crptName) {
		this.crptName = crptName;
	}	
			
	@Column(name = "CREDIT_CODE")	
	public String getCreditCode() {
		return creditCode;
	}

	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "BEGIN_BHK_DATE")			
	public Date getBeginBhkDate() {
		return beginBhkDate;
	}

	public void setBeginBhkDate(Date beginBhkDate) {
		this.beginBhkDate = beginBhkDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "END_BHK_DATE")			
	public Date getEndBhkDate() {
		return endBhkDate;
	}

	public void setEndBhkDate(Date endBhkDate) {
		this.endBhkDate = endBhkDate;
	}	
			
	@Column(name = "OTR_PSNS")	
	public Integer getOtrPsns() {
		return otrPsns;
	}

	public void setOtrPsns(Integer otrPsns) {
		this.otrPsns = otrPsns;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId")
	public List<TdZwyjOtrSerItemCrpt> getTdZwyjOtrSerItemCrptList() {
		return tdZwyjOtrSerItemCrptList;
	}

	public void setTdZwyjOtrSerItemCrptList(List<TdZwyjOtrSerItemCrpt> tdZwyjOtrSerItemCrptList) {
		this.tdZwyjOtrSerItemCrptList = tdZwyjOtrSerItemCrptList;
	}

	@Transient
	public String getItems() {
		return items;
	}

	public void setItems(String items) {
		this.items = items;
	}
}