package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-11-17
 */
@Entity
@Table(name = "TD_ZWYJ_DANGER_BHK")
@SequenceGenerator(name = "TdZwyjDangerBhk", sequenceName = "TD_ZWYJ_DANGER_BHK_SEQ", allocationSize = 1)
public class TdZwyjDangerBhk implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String bhkCode;
	private TbTjSrvorg fkByBhkorgId;
	private TbTjCrpt fkByCrptId;
	private String crptName;
	private String personName;
	private String sex;
	private TsSimpleCode fkByCardTypeId;
	private String idc;
	private Integer psnType;
	private Date brth;
	private Integer age;
	private String isxmrd;
	private String lnktel;
	private String dpt;
	private String wrknum;
	private BigDecimal wrklnt;
	private Integer wrklntmonth;
	private BigDecimal tchbadrsntim;
	private Integer tchbadrsnmonth;
	private String workName;
	private Integer bhkType;
	private TsSimpleCode fkByOnguardStateid;
	private Date bhkDate;
	private String bhkrst;
	private String mhkadv;
	private String ocpBhkrstdes;
	private Date jdgdat;
	private String badrsn;
	private Date rptPrintDate;
	private Integer ifRhk;
	private String lastBhkCode;
	private String lastFstBhkCode;
	private String uuid;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	/***危急值项目结果[危急值查询]*/
	private List<TdZwyjDangerResult> tdZwyjDangerResultList = new ArrayList<>();
	/**用工单位*/
	private TbTjCrpt fkByEntrusTCrptId;
	
	public TdZwyjDangerBhk() {
	}

	public TdZwyjDangerBhk(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjDangerBhk")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "BHK_CODE")	
	public String getBhkCode() {
		return bhkCode;
	}

	public void setBhkCode(String bhkCode) {
		this.bhkCode = bhkCode;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BHKORG_ID")			
	public TbTjSrvorg getFkByBhkorgId() {
		return fkByBhkorgId;
	}

	public void setFkByBhkorgId(TbTjSrvorg fkByBhkorgId) {
		this.fkByBhkorgId = fkByBhkorgId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_ID")			
	public TbTjCrpt getFkByCrptId() {
		return fkByCrptId;
	}

	public void setFkByCrptId(TbTjCrpt fkByCrptId) {
		this.fkByCrptId = fkByCrptId;
	}	
			
	@Column(name = "CRPT_NAME")	
	public String getCrptName() {
		return crptName;
	}

	public void setCrptName(String crptName) {
		this.crptName = crptName;
	}	
			
	@Column(name = "PERSON_NAME")	
	public String getPersonName() {
		return personName;
	}

	public void setPersonName(String personName) {
		this.personName = personName;
	}	
			
	@Column(name = "SEX")	
	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CARD_TYPE_ID")			
	public TsSimpleCode getFkByCardTypeId() {
		return fkByCardTypeId;
	}

	public void setFkByCardTypeId(TsSimpleCode fkByCardTypeId) {
		this.fkByCardTypeId = fkByCardTypeId;
	}	
			
	@Column(name = "IDC")	
	public String getIdc() {
		return idc;
	}

	public void setIdc(String idc) {
		this.idc = idc;
	}	
			
	@Column(name = "PSN_TYPE")	
	public Integer getPsnType() {
		return psnType;
	}

	public void setPsnType(Integer psnType) {
		this.psnType = psnType;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "BRTH")			
	public Date getBrth() {
		return brth;
	}

	public void setBrth(Date brth) {
		this.brth = brth;
	}	
			
	@Column(name = "AGE")	
	public Integer getAge() {
		return age;
	}

	public void setAge(Integer age) {
		this.age = age;
	}	
			
	@Column(name = "ISXMRD")	
	public String getIsxmrd() {
		return isxmrd;
	}

	public void setIsxmrd(String isxmrd) {
		this.isxmrd = isxmrd;
	}	
			
	@Column(name = "LNKTEL")	
	public String getLnktel() {
		return lnktel;
	}

	public void setLnktel(String lnktel) {
		this.lnktel = lnktel;
	}	
			
	@Column(name = "DPT")	
	public String getDpt() {
		return dpt;
	}

	public void setDpt(String dpt) {
		this.dpt = dpt;
	}	
			
	@Column(name = "WRKNUM")	
	public String getWrknum() {
		return wrknum;
	}

	public void setWrknum(String wrknum) {
		this.wrknum = wrknum;
	}	
			
	@Column(name = "WRKLNT")	
	public BigDecimal getWrklnt() {
		return wrklnt;
	}

	public void setWrklnt(BigDecimal wrklnt) {
		this.wrklnt = wrklnt;
	}	
			
	@Column(name = "WRKLNTMONTH")	
	public Integer getWrklntmonth() {
		return wrklntmonth;
	}

	public void setWrklntmonth(Integer wrklntmonth) {
		this.wrklntmonth = wrklntmonth;
	}	
			
	@Column(name = "TCHBADRSNTIM")	
	public BigDecimal getTchbadrsntim() {
		return tchbadrsntim;
	}

	public void setTchbadrsntim(BigDecimal tchbadrsntim) {
		this.tchbadrsntim = tchbadrsntim;
	}	
			
	@Column(name = "TCHBADRSNMONTH")	
	public Integer getTchbadrsnmonth() {
		return tchbadrsnmonth;
	}

	public void setTchbadrsnmonth(Integer tchbadrsnmonth) {
		this.tchbadrsnmonth = tchbadrsnmonth;
	}	
			
	@Column(name = "WORK_NAME")	
	public String getWorkName() {
		return workName;
	}

	public void setWorkName(String workName) {
		this.workName = workName;
	}	
			
	@Column(name = "BHK_TYPE")	
	public Integer getBhkType() {
		return bhkType;
	}

	public void setBhkType(Integer bhkType) {
		this.bhkType = bhkType;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ONGUARD_STATEID")			
	public TsSimpleCode getFkByOnguardStateid() {
		return fkByOnguardStateid;
	}

	public void setFkByOnguardStateid(TsSimpleCode fkByOnguardStateid) {
		this.fkByOnguardStateid = fkByOnguardStateid;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "BHK_DATE")			
	public Date getBhkDate() {
		return bhkDate;
	}

	public void setBhkDate(Date bhkDate) {
		this.bhkDate = bhkDate;
	}	
			
	@Column(name = "BHKRST")	
	public String getBhkrst() {
		return bhkrst;
	}

	public void setBhkrst(String bhkrst) {
		this.bhkrst = bhkrst;
	}	
			
	@Column(name = "MHKADV")	
	public String getMhkadv() {
		return mhkadv;
	}

	public void setMhkadv(String mhkadv) {
		this.mhkadv = mhkadv;
	}	
			
	@Column(name = "OCP_BHKRSTDES")	
	public String getOcpBhkrstdes() {
		return ocpBhkrstdes;
	}

	public void setOcpBhkrstdes(String ocpBhkrstdes) {
		this.ocpBhkrstdes = ocpBhkrstdes;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "JDGDAT")			
	public Date getJdgdat() {
		return jdgdat;
	}

	public void setJdgdat(Date jdgdat) {
		this.jdgdat = jdgdat;
	}	
			
	@Column(name = "BADRSN")	
	public String getBadrsn() {
		return badrsn;
	}

	public void setBadrsn(String badrsn) {
		this.badrsn = badrsn;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "RPT_PRINT_DATE")			
	public Date getRptPrintDate() {
		return rptPrintDate;
	}

	public void setRptPrintDate(Date rptPrintDate) {
		this.rptPrintDate = rptPrintDate;
	}	
			
	@Column(name = "IF_RHK")	
	public Integer getIfRhk() {
		return ifRhk;
	}

	public void setIfRhk(Integer ifRhk) {
		this.ifRhk = ifRhk;
	}	
			
	@Column(name = "LAST_BHK_CODE")	
	public String getLastBhkCode() {
		return lastBhkCode;
	}

	public void setLastBhkCode(String lastBhkCode) {
		this.lastBhkCode = lastBhkCode;
	}	
			
	@Column(name = "LAST_FST_BHK_CODE")	
	public String getLastFstBhkCode() {
		return lastFstBhkCode;
	}

	public void setLastFstBhkCode(String lastFstBhkCode) {
		this.lastFstBhkCode = lastFstBhkCode;
	}	
			
	@Column(name = "UUID")	
	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId")
	public List<TdZwyjDangerResult> getTdZwyjDangerResultList() {
		return tdZwyjDangerResultList;
	}

	public void setTdZwyjDangerResultList(List<TdZwyjDangerResult> tdZwyjDangerResultList) {
		this.tdZwyjDangerResultList = tdZwyjDangerResultList;
	}

	@ManyToOne
	@JoinColumn(name = "ENTRUST_CRPT_ID")
	public TbTjCrpt getFkByEntrusTCrptId() {
		return fkByEntrusTCrptId;
	}

	public void setFkByEntrusTCrptId(TbTjCrpt fkByEntrusTCrptId) {
		this.fkByEntrusTCrptId = fkByEntrusTCrptId;
	}
}