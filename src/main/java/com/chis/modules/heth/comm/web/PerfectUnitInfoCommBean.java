package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUnitAttr;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.web.FacesEditBean;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
* <p>类描述：完善单位信息模块</p>
 *
 * @ClassAuthor rcj,2018年4月18日,PerfectUnitInfoCommBean
* 
*/
@ManagedBean(name="perfectUnitInfoCommBean")
@ViewScoped
public class PerfectUnitInfoCommBean extends FacesEditBean {

	private static final long serialVersionUID = 1L;
	/**存在session中的对象*/
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);

	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	/**单位id*/
	private Integer unitId;
	/**单位*/
	private TsUnit tsUnit ;
	/**地区名称*/
    private String editZoneName;
    /**地区id*/
    private Integer editZoneId;
    /**地区编码*/
    private String editZoneCode;
    /**地区集合*/
    private List<TsZone> zoneList;
    //江苏地区编码
    private String zoneCode;
    /**是否管理员*/
    private boolean ifAdmin = Boolean.TRUE;
    /**添加页面选中的属性*/
    private List<String> selectSortList = new ArrayList<String>(0);
	/**
	 * 是否显示职业病诊断文书编号
	 */
	private Boolean ifShowDiagWritNo;

	public PerfectUnitInfoCommBean(){
		this.unitId = this.sessionData.getUser().getTsUnit().getRid();
        TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
        }
		this.zoneCode = tsZone.getZoneCode().substring(0, 2);
		if (!this.sessionData.getUser().getUserNo().equals(Constants.ADMIN)) {
			ifAdmin = Boolean.FALSE;
		}

		//是否显示职业病诊断文书编号
		String ifShowDiagWritNoString = "";
		try {
			ifShowDiagWritNoString = PropertyUtils.getValue("ifShowDiagWritNo");
		} catch (Exception ignored) {
		}
		this.ifShowDiagWritNo = "1".equals(ifShowDiagWritNoString);

		init();
		initZone();
	}
	
	/**
	 * <p>方法描述：信息初始化</p>
 	 * 
 	 * @MethodAuthor rcj,2018年4月18日,init
	 */
	public void init() {
		this.tsUnit = this.systemModuleService.findUnitWithSort(this.unitId);
		Set<TsUnitAttr> attrSet = this.tsUnit.getTsUnitAttrs();
        if(null != attrSet && attrSet.size() > 0) {
            for(TsUnitAttr attr: attrSet) {
                this.selectSortList.add(attr.getTsBsSort().getRid().toString());
            }
        }
	}
	
	
	/**
	 * <p>方法描述：初始化地区到街道</p>
 	 * @MethodAuthor rcj,2018年4月18日,initZone
 	 * 
	 * */
	private void initZone() {
		if(null == this.zoneList || this.zoneList.size()<= 0){
			this.zoneList = this.commService.findZoneListNew(false, zoneCode, "2", "5");
		}
		this.editZoneId = tsUnit.getTsZone().getRid();
		TsZone zone = systemModuleService.find(TsZone.class,editZoneId);
		this.editZoneCode=zone.getZoneCode();
		this.editZoneName = zone.getZoneName();
		
	}


	@Override
	public void addInit() {
	
		
	}

	@Override
	public void viewInit() {
	
		
	}

	@Override
	public void modInit() {

		
	}

	@Override
	public void saveAction() {
        String orgTel = StringUtils.objectToString(this.tsUnit.getOrgTel());
        if ("".equals(orgTel) || !StringUtils.vertyPhone(orgTel)) {
            JsfUtil.addErrorMessage("请输入正确的联系人电话！");
            return;
        }

		TsZone zone = systemModuleService.find(TsZone.class,editZoneId);
		this.tsUnit.setTsZone(zone);
		this.tsUnit.setIfComplete(1);
		systemModuleService.saveOrUpdateUnit(tsUnit, selectSortList);
		init();
		JsfUtil.addSuccessMessage("保存成功！");
	}

	@Override
	public String[] buildHqls() {
		
		return null;
	}



	public SessionData getSessionData() {
		return sessionData;
	}



	public void setSessionData(SessionData sessionData) {
		this.sessionData = sessionData;
	}



	public TsUnit getTsUnit() {
		return tsUnit;
	}



	public void setTsUnit(TsUnit tsUnit) {
		this.tsUnit = tsUnit;
	}



	public String getEditZoneName() {
		return editZoneName;
	}



	public void setEditZoneName(String editZoneName) {
		this.editZoneName = editZoneName;
	}


	public String getEditZoneCode() {
		return editZoneCode;
	}



	public void setEditZoneCode(String editZoneCode) {
		this.editZoneCode = editZoneCode;
	}



	public List<TsZone> getZoneList() {
		return zoneList;
	}



	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}



	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public CommServiceImpl getCommService() {
		return commService;
	}

	public void setCommService(CommServiceImpl commService) {
		this.commService = commService;
	}

	public Integer getEditZoneId() {
		return editZoneId;
	}

	public void setEditZoneId(Integer editZoneId) {
		this.editZoneId = editZoneId;
	}

	public String getZoneCode() {
		return zoneCode;
	}

	public void setZoneCode(String zoneCode) {
		this.zoneCode = zoneCode;
	}

	public boolean isIfAdmin() {
		return ifAdmin;
	}

	public void setIfAdmin(boolean ifAdmin) {
		this.ifAdmin = ifAdmin;
	}

	public SystemModuleServiceImpl getSystemModuleService() {
		return systemModuleService;
	}

	public void setSystemModuleService(SystemModuleServiceImpl systemModuleService) {
		this.systemModuleService = systemModuleService;
	}

	public List<String> getSelectSortList() {
		return selectSortList;
	}

	public void setSelectSortList(List<String> selectSortList) {
		this.selectSortList = selectSortList;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public Boolean getIfShowDiagWritNo() {
		return ifShowDiagWritNo;
	}

	public void setIfShowDiagWritNo(Boolean ifShowDiagWritNo) {
		this.ifShowDiagWritNo = ifShowDiagWritNo;
	}
}
