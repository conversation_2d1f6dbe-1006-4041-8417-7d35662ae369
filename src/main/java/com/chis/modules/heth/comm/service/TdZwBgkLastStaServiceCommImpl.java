package com.chis.modules.heth.comm.service;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdZwBgkLastSta;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 报告卡最新状态表 相关方法
 */
@Service
@Transactional(readOnly = false)
public class TdZwBgkLastStaServiceCommImpl extends AbstractTemplate {

    /**
     * 批量更新最新状态表
     */
    public void batchUpdateLatestSta(List<TdZwBgkLastSta> bgkLastStaList) {
        TdZwBgkLastSta bgkLastSta = bgkLastStaList.get(0);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("cartType", bgkLastSta.getCartType());
        StringBuilder baseSql = new StringBuilder();
        baseSql.append("UPDATE TD_ZW_BGK_LAST_STA T SET ");
        if (bgkLastSta.getState() != null) {
            baseSql.append(" T.STATE=:statue ");
            paramMap.put("statue", bgkLastSta.getState());
        }
        if (bgkLastSta.getCityRcvDate() != null) {
            baseSql.append(" ,T.CITY_RCV_DATE=").append("TO_DATE(:cityRcvDate, 'YYYY-MM-DD HH24:MI:SS') ");
            paramMap.put("cityRcvDate", DateUtils.formatDate(bgkLastSta.getCityRcvDate(), "yyyy-MM-dd HH:mm:ss"));
        }
        if (StringUtils.isNotBlank(bgkLastSta.getCountAuditAdv())) {
            baseSql.append(" ,T.COUNTY_AUDIT_ADV= :countAuditAdv");
            paramMap.put("countAuditAdv", bgkLastSta.getCountAuditAdv());
        }
        if (bgkLastSta.getProRcvDate() != null) {
            baseSql.append(" ,T.PRO_RCV_DATE=").append("TO_DATE(:proRcvDate, 'YYYY-MM-DD HH24:MI:SS') ");
            paramMap.put("proRcvDate", DateUtils.formatDate(bgkLastSta.getProRcvDate(), "yyyy-MM-dd HH:mm:ss"));
        }
        if (StringUtils.isNotBlank(bgkLastSta.getCityAuditAdv())) {
            baseSql.append(" ,T.CITY_AUDIT_ADV= :cityAuditAdv");
            paramMap.put("cityAuditAdv", bgkLastSta.getCityAuditAdv());
        }
        if (StringUtils.isNotBlank(bgkLastSta.getCityChkPsn())) {
            baseSql.append(" ,T.CITY_CHK_PSN= :cityChkPsn");
            paramMap.put("cityChkPsn", bgkLastSta.getCityChkPsn());
        }
        if (StringUtils.isNotBlank(bgkLastSta.getCountyChkPsn())) {
            baseSql.append(" ,T.COUNTY_CHK_PSN= :countyChkPsn");
            paramMap.put("countyChkPsn", bgkLastSta.getCountyChkPsn());
        }
        if (bgkLastSta.getCountyRst()!=null) {
            baseSql.append(" ,T.COUNTY_RST= :countyRst");
            paramMap.put("countyRst", bgkLastSta.getCountyRst());
        }
        if (bgkLastSta.getCityRst()!=null) {
            baseSql.append(" ,T.CITY_RST= :cityRst");
            paramMap.put("cityRst", bgkLastSta.getCityRst());
        }
        if (bgkLastSta.getProAuditAdv()!=null) {
            baseSql.append(" ,T.PRO_AUDIT_ADV= :proAuditAdv");
            paramMap.put("proAuditAdv", bgkLastSta.getProAuditAdv());
        }
        if (bgkLastSta.getProChkPsn()!=null) {
            baseSql.append(" ,T.PRO_CHK_PSN= :proChkPsn");
            paramMap.put("proChkPsn", bgkLastSta.getProChkPsn());
        }
        if (bgkLastSta.getProRst()!=null) {
            baseSql.append(" ,T.PRO_RST= :proRst");
            paramMap.put("proRst", bgkLastSta.getProRst());
        }
        if(bgkLastSta.getState()==2 || bgkLastSta.getState()==4 || bgkLastSta.getState()==6){
            baseSql.append(" ,T.COUNTY_RCV_DATE=null,T.CITY_RCV_DATE=null,T.PRO_RCV_DATE=null ");
        }

        baseSql.append(" where  T.CART_TYPE=:cartType AND T.BUS_ID IN (:list)");
        int length = bgkLastStaList.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<TdZwBgkLastSta> subList = bgkLastStaList.subList(i * 1000, endIndex);
            List<Integer> rids = new ArrayList<>();
            for (TdZwBgkLastSta lastSta : subList) {
                rids.add(lastSta.getBusId());
            }
            paramMap.put("list", rids);
            this.executeSql(baseSql.toString(), paramMap);
        }
    }


}
