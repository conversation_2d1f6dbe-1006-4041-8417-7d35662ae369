package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.DefaultLazyDataModel;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  <p>类描述：机构单位弹出框通用：显示字段：地区，机构名称;查询条件：地区，机构名称</p>
 * @ClassAuthor hsj 2021/11/8 15:31
 */
@ManagedBean(name="orgSelectListBean")
@ViewScoped
public class OrgSelectListBean {
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);

	/**查询条件：地区：默认为省*/
	private List<TsZone> zoneList;
	private String searchZoneCode;
	private String searchZoneName;
	private String searchOrgName;
	/**查询条件：机构名称*/
	private String  orgName;
	/**弹出框标题*/
	private String titleName;
	/**当前页面机构类型*/
	private String orgType;
	private Object[] selectPro;

	private DefaultLazyDataModel sendFsDataModel;
	protected Map<String, Object> sendFsParamMap = new HashMap<String, Object>();
	private String[] sendFsHql;


	public OrgSelectListBean() {
		this.orgType= JsfUtil.getRequestParameter("orgType");
		TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		if(null==tsZone){
			tsZone = Global.getUser().getTsUnit().getTsZone();
		}
		this.searchZoneCode = tsZone.getZoneCode();
		this.searchZoneName = tsZone.getZoneName();
		String zoneGb = tsZone.getZoneGb().substring(0, 2)+ "00000000";
		//查询地区放入缓存中（默认为省级）
		this.zoneList = this.commService.findZoneListCache(false, zoneGb,"2","4");
		if(!CollectionUtils.isEmpty(zoneList)) {
			this.searchZoneCode = zoneList.get(0).getZoneCode();
			this.searchZoneName = zoneList.get(0).getZoneName();
		}

		//标题初始化
		if(StringUtils.isNotBlank(orgType)){
			switch (orgType){
				case "1":
					//诊断机构
					titleName ="诊断机构选择";
					break;
				default:
					titleName = null;
					break;
			}

		}
		this.searchAction();
	}
	/**
	 *  <p>方法描述：查询</p>
	 * @MethodAuthor hsj
	 */
	public void searchAction() {
		this.sendFsParamMap.clear();
		this.sendFsHql = this.buildSendFsHqls();
		this.sendFsDataModel = new DefaultLazyDataModel(this.sendFsHql[0], this.sendFsHql[1], this.sendFsParamMap, null, "tabView:editForm:sampTabview:sendCheckFsDataTable", true);
	}

	/**
	 *  <p>方法描述：查询机构信息</p>
	 * @MethodAuthor hsj
	 */
	private String[] buildSendFsHqls() {
		StringBuilder sb = new StringBuilder();
		String sql = "";
		if(StringUtils.isNotBlank(orgType)){
			if("1".equals(orgType)){
				sb.append(" FROM TD_ZW_DIAGORGINFO T");
			}
			sb.append(" LEFT JOIN  TS_UNIT T1 ON T.ORG_ID = T1.RID");
			sb.append(" LEFT JOIN TS_ZONE T2 ON T2.RID = T1.ZONE_ID");
			sb.append( " WHERE T.STATE = 1 ");
			// 地区
			if(StringUtils.isNotBlank(this.searchZoneCode)){
				sb.append(" AND T2.ZONE_CODE LIKE '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%' ");
			}
			// 单位名称
			if(StringUtils.isNotBlank(this.orgName)) {
				sb.append(" AND T.ORG_NAME LIKE :orgName  escape '\\\'");
				this.sendFsParamMap.put("orgName", "%" + StringUtils.convertBFH(this.orgName)  + "%");
			}
		}
		if(StringUtils.isNotBlank(sb)){
			sql ="SELECT T1.RID,CASE WHEN T2.ZONE_TYPE > 2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME, '_') + 1) ELSE T2.FULL_NAME END ZONE_NAME,T.ORG_NAME";
			return new String[]{sql + sb.toString(), "SELECT COUNT(*)" + sb.toString()};
		}
		return null;
	}
	/**
	 *  <p>方法描述：选择事件</p>
	 * @MethodAuthor hsj
	 */
	public void selectOrgAction() {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("selectOrg", this.selectPro);
		RequestContext.getCurrentInstance().closeDialog(map);
	}

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public String getSearchZoneCode() {
		return searchZoneCode;
	}

	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}

	public String getSearchZoneName() {
		return searchZoneName;
	}

	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}

	public String getSearchOrgName() {
		return searchOrgName;
	}

	public void setSearchOrgName(String searchOrgName) {
		this.searchOrgName = searchOrgName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getTitleName() {
		return titleName;
	}

	public void setTitleName(String titleName) {
		this.titleName = titleName;
	}

	public String getOrgType() {
		return orgType;
	}

	public void setOrgType(String orgType) {
		this.orgType = orgType;
	}

	public DefaultLazyDataModel getSendFsDataModel() {
		return sendFsDataModel;
	}

	public void setSendFsDataModel(DefaultLazyDataModel sendFsDataModel) {
		this.sendFsDataModel = sendFsDataModel;
	}

	public Map<String, Object> getSendFsParamMap() {
		return sendFsParamMap;
	}

	public void setSendFsParamMap(Map<String, Object> sendFsParamMap) {
		this.sendFsParamMap = sendFsParamMap;
	}

	public String[] getSendFsHql() {
		return sendFsHql;
	}

	public void setSendFsHql(String[] sendFsHql) {
		this.sendFsHql = sendFsHql;
	}

	public Object[] getSelectPro() {
		return selectPro;
	}

	public void setSelectPro(Object[] selectPro) {
		this.selectPro = selectPro;
	}
}
