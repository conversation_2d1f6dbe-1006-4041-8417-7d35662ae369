package com.chis.modules.heth.comm.web;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.enumn.ReturnType;
import com.chis.modules.heth.comm.json.CrptDialogParamJson;
import com.chis.modules.heth.comm.rptvo.SrvorgPsnVo;
import com.chis.modules.heth.comm.rptvo.TbYsjcLimitValVO;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.enumn.SystemMessageEnum;
import com.chis.modules.system.logic.WordReportDTO;
import com.chis.modules.system.logic.WordReportJson;
import com.chis.modules.system.utils.Global;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.text.Collator;
import java.util.*;

/**
 * <p>类描述：职业卫生技术服务信息报送卡填报-标配</p>
 *
 * @ClassAuthor hsj 2025-05-12 16:58
 */
@ManagedBean(name = "occHethCardListBean")
@ViewScoped
public class OccHethCardListBean extends OccHethCardBaseBean {
    private static final long serialVersionUID = 1L;

    /**
     * 编辑页面：技术服务地址地区
     */
    private List<TsZone> editZoneList = new ArrayList<>();
    private Map<Integer, TsZone> editZoneMap = new HashMap<>(16);
    /**
     * 承担的服务事项码表
     */
    private List<TsSimpleCode> servicesUndertakenList = new ArrayList<>();
    /**
     * 预览文书文件地址
     */
    private String reportFilePath;

    /******************职业病危害因素检测***************************/
    /**
     * 危害因素大类 码表
     */
    private List<TsSimpleCode> firsBadRsntList;
    /**
     * 危害因素列表
     */
    private List<TbYsjcLimitValVO> limitVals;
    /**
     * 选中的危害因素
     */
    private Object[] selectBadRsn;
    /**
     * 危害因素选择框--查询条件 危害因素大类rid
     */
    private Integer firstBadRsnId;
    /**
     * 危害因素选择框--查询条件 名称/拼音码
     */
    private String searchNamOrPy;
    /**
     * 超标检测项目 下拉
     */
    private List<Object[]> itemList2;
    /**
     * 计量单位特殊值
     */
    private TreeNode msruntTree;
    /**
     * 危害因素弹出框-已选择的危害因素
     */
    private TbYsjcLimitValVO selBadRsn;

    /**检测任务rid*/
    private Integer contractId;
    // 是否详情页
    private Integer ifViewPage;

    /**是否需要验证需要现场打卡记录*/
    private boolean ifNeedOnSiteCheckIn;

    /**校验开始日期*/
    private Date checkStartDate;

    public OccHethCardListBean() {
        this.init();
        this.initSimpleCode();
        String id = JsfUtil.getRequest().getParameter("rid");
        if (ObjectUtil.isNotEmpty(id)) {
            this.mainRid = Integer.valueOf(id);
            this.viewInit();
            this.ifViewPage = 2;
            this.forwardOtherPage();
        } else {
            this.searchAction();
        }

        // 是否需要验证需要现场打卡记录
        String ifNeedOnSiteCheckInTemp = PropertyUtils.getValueWithoutException("ifNeedOnSiteCheckIn");
        if ("true".equals(ifNeedOnSiteCheckInTemp)) {
            ifNeedOnSiteCheckIn = true;
        } else {
            ifNeedOnSiteCheckIn = false;
        }
        if (ifNeedOnSiteCheckIn) {
            // 验证开始日期
            String checkStartDate = PropertyUtils.getValueWithoutException("checkStartDate");
            if (StringUtils.isNotBlank(checkStartDate)) {
                this.checkStartDate = DateUtils.parseDate(checkStartDate);
            }
        }
    }


    private void init() {
        //服务单位地区
        this.searchZoneList = this.commService.findZoneListByGbAndTypeNoNation("", true, "", "");
        //技术服务地址地区
        this.editZoneList = this.commService.findZoneListByGbAndTypeNoNation("", true, "", "4");
        for (TsZone zone : this.editZoneList) {
            this.editZoneMap.put(zone.getRid(), zone);
        }
        //状态
        this.searchStateList = new ArrayList<>();
        this.searchStateList.add(0);
        this.ifSupportCancel = Boolean.TRUE;
    }

    private void initSimpleCode() {
        //承担的服务事项
        this.servicesUndertakenList = this.commService.findNumSimpleCodesByTypeId("5577");
        addSimpleCodeMapByList(this.servicesUndertakenList);
    }

    @Override
    public void searchAction() {
        super.searchAction();
    }

    @Override
    public String[] buildHqls() {
        Integer unitId = Global.getUser().getTsUnit().getRid();
        StringBuilder baseSql = new StringBuilder();
        baseSql.append("SELECT T.RID, Z.FULL_NAME, T.CRPT_NAME, T.RPT_DATE, T.STATE, Z.ZONE_GB, nvl(T.SOURCE,0),T.CONTRACT_ID ");
        baseSql.append("FROM TD_ZW_OCCHETH_CARD T ");
        baseSql.append("LEFT JOIN TS_ZONE Z ON T.ZONE_ID = Z.RID ");
        baseSql.append("WHERE NVL(T.DEL_MARK, 0) = 0 ");
        // 当前单位填报
        baseSql.append(" AND T.FILL_UNIT_ID = :fillUnitId ");
        this.paramMap.put("fillUnitId", unitId);
        // 服务单位地区
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            baseSql.append(" AND Z.ZONE_GB LIKE :zoneCode escape '\\\' ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneGb)) + "%");
        }
        // 服务单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            baseSql.append(" AND T.CRPT_NAME LIKE :searchCrptName escape '\\\' ");
            this.paramMap.put("searchCrptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        // 出具技术服务报告时间
        if (ObjectUtil.isNotEmpty(this.searchRptBeginDate)) {
            baseSql.append(" AND T.RPT_DATE >= TO_DATE(:searchRptBeginDate, 'YYYY-MM-DD HH24:MI:SS') ");
            this.paramMap.put("searchRptBeginDate", DateUtils.formatDate(this.searchRptBeginDate) + " 00:00:00");
        }
        if (ObjectUtil.isNotEmpty(this.searchRptEndDate)) {
            baseSql.append(" AND T.RPT_DATE <= TO_DATE(:searchRptEndDate, 'YYYY-MM-DD HH24:MI:SS') ");
            this.paramMap.put("searchRptEndDate", DateUtils.formatDate(this.searchRptEndDate) + " 23:59:59");
        }
        // 状态
        if (ObjectUtil.isNotEmpty(this.searchStateList)) {
            baseSql.append(" AND T.STATE IN (:searchStateList)");
            this.paramMap.put("searchStateList", this.searchStateList);
        }
        String sql1 = "SELECT * FROM (" + baseSql + ")AA ORDER BY AA.STATE,AA.RPT_DATE DESC, AA.ZONE_GB, AA.CRPT_NAME, AA.RID DESC";
        String sql2 = "SELECT COUNT(1) FROM (" + baseSql + ")";
        return new String[]{sql1, sql2};
    }

    public void deleteAction() {
        if (this.mainRid == null) {
            return;
        }
        try {
            this.occhethCardService.deleteOcchethCardByRid(this.mainRid);
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
        this.searchAction();
    }

    @Override
    public void addInit() {

    }

    @Override
    public void modInit() {
        this.occhethCard = this.occhethCardService.findTdZwOcchethCardByRid(this.mainRid);
        //初0始化-机构信息
        Object[] occhethBaseInfo = this.occhethCardService.findTdZwOcchethBaseInfo(Global.getUser().getTsUnit().getRid());
        String occhethInfoRid;
        if (ObjectUtil.isNotEmpty(occhethBaseInfo)) {
            occhethInfoRid = StringUtils.objectToString(occhethBaseInfo[0]);
            //初始化-机构信息
            this.occhethCard.setOrgId(occhethInfoRid);
        }
        //处理-参与人员信息
        if(new Integer("2").equals(this.occhethCard.getSource())){
            initOccPsn();
        }else{
            for (TdZwOcchethCardPsn occhethCardPsn : this.occhethCard.getOcchethCardPsnList()) {
                if (ObjectUtil.isEmpty(occhethCardPsn.getFkByPsnId()) || ObjectUtil.isEmpty(occhethCardPsn.getFkByPsnId().getRid())) {
                    continue;
                }
                this.occhethCard.getOcchethCardPsnRidList().add(occhethCardPsn.getFkByPsnId().getRid().toString());
                for (TdZwOcchethCardItem occhethCardItem : occhethCardPsn.getOcchethCardItemList()) {
                    if (ObjectUtil.isEmpty(occhethCardItem.getFkByItemId())) {
                        continue;
                    }
                    occhethCardPsn.getOcchethCardItemSimpleCodeList().add(occhethCardItem.getFkByItemId().getRid().toString());
                }
            }
        }
        //按rid排序
        occhethCardPsnSort();

        //处理-服务的用人单位信息-技术服务地址
        for (TdZwOcchethCardZone occhethCardZone : this.occhethCard.getOcchethCardZoneList()) {
            if (ObjectUtil.isEmpty(occhethCardZone.getFkByZoneId())) {
                continue;
            }
            TsZone zone = occhethCardZone.getFkByZoneId();
            occhethCardZone.setZoneRid(zone.getRid());
            occhethCardZone.setZoneName(zone.getZoneName());
            occhethCardZone.setZoneGb(zone.getZoneGb());
        }
        //处理-技术服务信息-技术服务领域
        this.occhethCard.setOcchethCardServiceSimpleCodeList(new ArrayList<String>());
        for (TdZwOcchethCardService occhethCardService : this.occhethCard.getOcchethCardServiceList()) {
            if (ObjectUtil.isEmpty(occhethCardService.getFkByServiceAreaId())) {
                continue;
            }
            this.occhethCard.getOcchethCardServiceSimpleCodeList().add(occhethCardService.getFkByServiceAreaId().getRid().toString());
        }
        packServiceResults();

        if(new Integer("2").equals(this.occhethCard.getSource())) {
            //初始化现场调查时间、现场采样/检测时间
            initDate();
            //初始化报告信息
            if(this.occhethCard.getRptDate()==null){
                //出具技术服务报告时间  给默认值
                this.occhethCard.setRptDate(new Date());
                this.occhethCard.setFillFormPsn(Global.getUser().getUsername());
                this.occhethCard.setFillLink(Global.getUser().getMbNum());
                this.occhethCard.setFillDate(new Date());
            }
        }

        if (0 == this.occhethCard.getState() && StringUtils.isNotBlank(this.occhethCard.getAnnexPath())) {
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("disabledInput('true','occHethCard')");
        }
        this.ifViewPage = 0;
    }

    /**
    * <p>Description：初始化现场调查时间、现场采样/检测时间 </p>
    * <p>Author： yzz 2025/5/20 </p>
    */
    public void initDate() {
        try{
            List<Object[]> appDates = this.occhethCardService.findAppDate(this.contractId);
            if(!CollectionUtils.isEmpty(appDates)){
                for (Object[] appDate : appDates) {
                    //1 现场调查
                    if(appDate[2]!=null && "1".equals(appDate[2].toString()) && this.occhethCard.getInvestStartDate()==null && this.occhethCard.getInvestEndDate()==null){
                        if(appDate[0]!=null){
                            this.occhethCard.setInvestStartDate(DateUtils.parseDate(appDate[0].toString(),"yyyy-MM-dd"));
                        }
                        if(appDate[1]!=null){
                            this.occhethCard.setInvestEndDate(DateUtils.parseDate(appDate[1].toString(),"yyyy-MM-dd"));
                        }
                    }
                    //2现场测量
                    if(appDate[2]!=null && "2".equals(appDate[2].toString()) &&this.occhethCard.getJcStartDate()==null && this.occhethCard.getJcEndDate()==null){
                        if(appDate[0]!=null){
                            this.occhethCard.setJcStartDate(DateUtils.parseDate(appDate[0].toString(),"yyyy-MM-dd"));
                        }
                        if(appDate[1]!=null){
                            this.occhethCard.setJcEndDate(DateUtils.parseDate(appDate[1].toString(),"yyyy-MM-dd"));
                        }
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
    * <p>Description：初始化参与人员信息 </p>
    * <p>Author： yzz 2025/5/20 </p>
    */
    public void initOccPsn() {
        this.occhethCard.setOcchethCardPsnRidList(new ArrayList<String>());
        // 手机端关联的绑定的人员
        List<Object[]> occhethCardPsnList = new ArrayList<>();
        //《职业卫生技术服务信息报送卡》 报送卡上传后不再加载手机端的人员
        if (StringUtils.isBlank(this.occhethCard.getAnnexPath())) {
            occhethCardPsnList = this.occhethCardService.findPsnByContractId(this.contractId);
        }
        Map<Integer, Object[]> psnMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(occhethCardPsnList)) {
            for (Object[] obj : occhethCardPsnList) {
                psnMap.put(Integer.parseInt(obj[0].toString()), obj);
            }
        }
        List<Object[]> psnList = new ArrayList<>();
        // 已保存的人员
        if (!CollectionUtils.isEmpty(this.occhethCard.getOcchethCardPsnList())) {
            for (TdZwOcchethCardPsn occhethCardPsn : this.occhethCard.getOcchethCardPsnList()) {
                if (ObjectUtil.isEmpty(occhethCardPsn.getFkByPsnId()) || ObjectUtil.isEmpty(occhethCardPsn.getFkByPsnId().getRid())) {
                    continue;
                }
                this.occhethCard.getOcchethCardPsnRidList().add(occhethCardPsn.getFkByPsnId().getRid().toString());
                if (psnMap.containsKey(occhethCardPsn.getFkByPsnId().getRid())) {
                    occhethCardPsn.setIsApp(true);
                    psnList.add(psnMap.get(occhethCardPsn.getFkByPsnId().getRid()));
                }
                for (TdZwOcchethCardItem occhethCardItem : occhethCardPsn.getOcchethCardItemList()) {
                    if (ObjectUtil.isEmpty(occhethCardItem.getFkByItemId())) {
                        continue;
                    }
                    occhethCardPsn.getOcchethCardItemSimpleCodeList().add(occhethCardItem.getFkByItemId().getRid().toString());
                }
            }
        }
        // 移除已经添加的人员
        if (!CollectionUtils.isEmpty(psnList)) {
            occhethCardPsnList.removeAll(psnList);
        }
        // 其余人员加载上
        if (!CollectionUtils.isEmpty(occhethCardPsnList)) {
            for (Object[] obj : occhethCardPsnList) {
                TdZwOcchethCardPsn occhethCardPsn = new TdZwOcchethCardPsn();
                occhethCardPsn.setFkByMainId(this.occhethCard);
                occhethCardPsn.setFkByPsnId(new TdZwPsninfoComm(Integer.parseInt(obj[0].toString())));
                occhethCardPsn.setPsnName(obj[1].toString());
                this.occhethCard.getOcchethCardPsnRidList().add(obj[0].toString());
                if (psnMap.containsKey(occhethCardPsn.getFkByPsnId().getRid())) {
                    occhethCardPsn.setIsApp(true);
                }
                this.occhethCardService.preEntity(occhethCardPsn);
                this.occhethCard.getOcchethCardPsnList().add(occhethCardPsn);
            }
        }
    }


    @Override
    public void viewInit() {
        if (null != this.mainRid) {
            this.ifSupportCancel = this.occhethCardService.queryRcdCountByParams(12, this.mainRid, 3) == 0;
        }
        this.ifViewPage = 1;
        super.viewInit();
    }


    @Override
    public void saveAction() {
        if (verifyFailed(false) || verifyCheckIn()) {
            return;
        }
        try {
            saveOcchethCard(0, false);
            JsfUtil.addSuccessMessage("暂存成功！");
            if (ObjectUtil.isNotEmpty(this.occhethCard.getAnnexPath())) {
                RequestContext.getCurrentInstance().execute("disabledInput('true','occHethCard')");
            }
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("暂存失败！");
        }
    }

    /**
    * <p>Description：验证是否需要现场打卡记录 </p>
    * <p>Author： yzz 2025/7/15 </p>
    */
    public boolean verifyCheckIn(){
        // 如果配置未开启，直接跳过验证
        if(!this.ifNeedOnSiteCheckIn){
            return false;
        }
        // 如果签约日期在2025-07-08之前，跳过验证
        if(this.occhethCard.getFkByContractId()!=null && this.occhethCard.getFkByContractId().getOrderDate()!=null && this.checkStartDate!=null && this.occhethCard.getFkByContractId().getOrderDate().before(this.checkStartDate)){
            return false;
        }
        if(this.occhethCard==null || this.occhethCard.getFkByContractId()==null || this.occhethCard.getFkByContractId().getRid()==null){
            return true;
        }
        Integer count = this.occhethCardService.findCountCheckIn(this.occhethCard.getFkByContractId().getRid());
        if(count==0){
            JsfUtil.addErrorMessage("这条检测任务没有查询到拍照打卡记录，请完成后再操作！");
            return true;
        }
        return false;
    }

    public void checkSubmitAction() {
        if (verifyFailed(true) || verifyCheckIn()) {
            return;
        }
        boolean flag = false;
        if (StringUtils.isBlank(this.occhethCard.getAnnexPath())) {
            JsfUtil.addErrorMessage("请先上传《职业卫生技术服务信息报送卡》文书！");
            flag = true;
        }
        if (StringUtils.isBlank(this.occhethCard.getSignAddress())) {
            JsfUtil.addErrorMessage("请先上传《职业卫生技术服务信息报告首页（含质控号）、签发页》（盖章）文书！");
            flag = true;
        }
        if (flag) {
            return;
        }
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("PF('ConfirmDialog').show();");
    }

    public void submitAction() {
        try {
            saveOcchethCard(1, false);
            JsfUtil.addSuccessMessage("提交成功！");
        } catch (Exception e) {
            this.occhethCard.setState(0);
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
        try {
            this.viewInitAction();
            RequestContext.getCurrentInstance().update("tabView");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("跳转详情页面失败！");
        }
    }

    /**
     * 保存主表
     *
     * @param state 主表状态
     */
    public boolean saveOcchethCard(int state, boolean ifGenQrCode) {
        saveOcchethCardBefore();
        this.occhethCard.setState(state);
        if (ifGenQrCode) {
            String qrCode = generateQrCode();
            if (null == qrCode) {
                return false;
            }
            this.occhethCard.setQrCodePath(qrCode);
        }
        this.occhethCardService.saveOcchethCard(this.occhethCard);
        this.mainRid = this.occhethCard.getRid();
        if(ObjectUtil.isNotNull(state) &&  new Integer(0).equals(state)){
            //按rid排序
            this.occhethCardPsnSort();
            //存储成功之后重新加载技术服务结果中的职业病危害因素检测结果
            this.occhethCard.setBpBadrsnList(this.occhethCardService.findOcchethCardResult(this.mainRid));
            this.processOccupationalHazardFactors();
        }
        return true;
    }

    /**
     * 保存/提交验证是否不通过
     *
     * @param isSubmit 是否提交操作
     * @return boolean true 验证不通过;false 验证通过
     */
    private boolean verifyFailed(boolean isSubmit) {
        boolean verifyFailed = false;
        if (StringUtils.isBlank(this.selServiceResultExtends3)) {
            JsfUtil.addErrorMessage("技术服务结果未关联编号规则，请联系管理员！");
            verifyFailed = true;
        }
        //项目负责人
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getProFz())) {
            JsfUtil.addErrorMessage("项目负责人不能为空");
            verifyFailed = true;
        }
        //联系电话
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getProLinktel())) {
            JsfUtil.addErrorMessage("联系电话不能为空");
            verifyFailed = true;
        }
        if (ObjectUtil.isNotEmpty(this.occhethCard.getProLinktel())
                && !StringUtils.vertyPhone(this.occhethCard.getProLinktel())) {
            JsfUtil.addErrorMessage("联系电话格式错误！");
            verifyFailed = true;
        }
        //参与人员信息
        if (isSubmit) {
            if (ObjectUtil.isEmpty(this.occhethCard.getOcchethCardPsnList())) {
                JsfUtil.addErrorMessage("请至少添加一条参与人员信息！");
                verifyFailed = true;
            } else {
                //承担的服务事项
                for (TdZwOcchethCardPsn occhethCardPsn : this.occhethCard.getOcchethCardPsnList()) {
                    if (ObjectUtil.isEmpty(occhethCardPsn.getOcchethCardItemSimpleCodeList())) {
                        JsfUtil.addErrorMessage(occhethCardPsn.getPsnName() + "承担的服务事项不能为空！");
                        verifyFailed = true;
                    }
                }
            }
        }
        //服务的用人单位
        if (ObjectUtil.isEmpty(this.occhethCard.getFkByCrptId())) {
            JsfUtil.addErrorMessage("请选择服务的用人单位！");
            verifyFailed = true;
        }
        //技术服务地址
        if (isSubmit) {
            List<TdZwOcchethCardZone> occhethCardZoneList = this.occhethCard.getOcchethCardZoneList();
            for (int i = 0; i < occhethCardZoneList.size(); i++) {
                TdZwOcchethCardZone occhethCardZone = occhethCardZoneList.get(i);
                if (ObjectUtil.isEmpty(occhethCardZone.getZoneRid())) {
                    JsfUtil.addErrorMessage("第" + (i + 1) + "行技术服务地址不能为空！");
                    verifyFailed = true;
                }
            }
        }
        //技术服务领域
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getOcchethCardServiceSimpleCodeList())) {
            JsfUtil.addErrorMessage("请选择技术服务领域！");
            verifyFailed = true;
        }
        //现场调查时间
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getInvestStartDate())) {
            JsfUtil.addErrorMessage("请选择现场调查开始时间！");
            verifyFailed = true;
        }
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getInvestEndDate())) {
            JsfUtil.addErrorMessage("请选择现场调查结束时间！");
            verifyFailed = true;
        }
        //现场采样/检测时间
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getJcStartDate())) {
            JsfUtil.addErrorMessage("请选择现场采样/检测开始时间！");
            verifyFailed = true;
        }
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getJcEndDate())) {
            JsfUtil.addErrorMessage("请选择现场采样/检测结束时间！");
            verifyFailed = true;
        }
        //出具技术服务报告时间
        if (ObjectUtil.isEmpty(this.occhethCard.getRptDate())) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("出具技术服务报告时间");
            verifyFailed = true;
        } else {
            if (ObjectUtil.isNotEmpty(this.occhethCard.getInvestEndDate())
                    && DateUtils.isCompareDate(this.occhethCard.getRptDate(), "<", this.occhethCard.getInvestEndDate())) {
                JsfUtil.addErrorMessage("出具技术服务报告时间应大于等于现场调查结束时间！");
                verifyFailed = true;
            }
            if (ObjectUtil.isNotEmpty(this.occhethCard.getJcEndDate())
                    && DateUtils.isCompareDate(this.occhethCard.getRptDate(), "<", this.occhethCard.getJcEndDate())) {
                JsfUtil.addErrorMessage("出具技术服务报告时间应大于等于现场采样/检测结束日期！");
                verifyFailed = true;
            }
        }
        //技术服务报告编号
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getRptNo())) {
            JsfUtil.addErrorMessage("技术服务报告编号不能为空！");
            verifyFailed = true;
        }

        boolean existServiceResult = "1".equals(this.selServiceResultExtends1) || "2".equals(this.selServiceResultExtends1);
        // 职业病危害因素
        if (existServiceResult && isSubmit) {
            if (ObjectUtil.isEmpty(this.occhethCard.getSelBadrsnList())) {
                JsfUtil.addErrorMessage("职业病危害因素至少选择一项！");
                verifyFailed = true;
            } else {
                Set<String> badrsnSet = new HashSet<>();
                if (!CollectionUtils.isEmpty(this.badrsnSubList)) {
                    for (TdZwOcchethBpSub badrsn : this.badrsnSubList) {
                        badrsnSet.add(Convert.toStr(badrsn.getFkByBadrsnId().getFkByBadrsnId().getRid()));
                    }
                }
                for (String badrsnId : this.occhethCard.getSelBadrsnList()) {
                    if (!badrsnSet.contains(badrsnId)) {
                        JsfUtil.addErrorMessage(this.simpleCodeMap.get(badrsnId).getCodeName() + "职业病危害因素检测结果至少有一条记录！");
                        verifyFailed = true;
                    }
                }
            }
        }
        // 技术服务结果选择“防护设施和个体防护用品效果评价”时 “开展职业病防护设备设施防护效果检测”与“开展职业病防护用品防护效果检测”
        // 至少勾选一项
        if (isSubmit && "3".equals(this.selServiceResultExtends1)
                && !this.occhethCard.getHasJcInst() && !this.occhethCard.getHasJcUse()) {
            JsfUtil.addErrorMessage("开展职业病防护设备设施防护效果检测与开展职业病防护用品防护效果检测至少选择一项！");
            verifyFailed = true;
        }
        // 开展职业病防护设备设施防护效果检测
        if (this.occhethCard.getHasJcInst()) {
            boolean emptyJcInstNum = ObjectUtil.isEmpty(this.occhethCard.getJcInstNum());
            boolean emptyJcNotHgInstNum = ObjectUtil.isEmpty(this.occhethCard.getJcNotHgInstNum());
            if (isSubmit && emptyJcInstNum) {
                JsfUtil.addErrorMessage("检测设备设施数量不能为空！");
                verifyFailed = true;
            }
            if (!emptyJcInstNum && this.occhethCard.getJcInstNum() <= 0) {
                JsfUtil.addErrorMessage("检测设备设施数量应大于0！");
                verifyFailed = true;
            }
            if (isSubmit && emptyJcNotHgInstNum) {
                JsfUtil.addErrorMessage("检测结果不合格的设备设施数量不能为空！");
                verifyFailed = true;
            }
            if (!emptyJcNotHgInstNum && this.occhethCard.getJcNotHgInstNum() < 0) {
                JsfUtil.addErrorMessage("检测结果不合格的设备设施数量应大于等于0！");
                verifyFailed = true;
            }
            if (!emptyJcInstNum && !emptyJcNotHgInstNum) {
                if (this.occhethCard.getJcNotHgInstNum() > this.occhethCard.getJcInstNum()) {
                    JsfUtil.addErrorMessage("检测结果不合格的设备设施数量应小于等于检测设备设施数量！");
                    verifyFailed = true;
                }
                if (isSubmit && this.occhethCard.getJcNotHgInstNum() > 0
                        && ObjectUtil.isEmpty(this.occhethCard.getNotHgInstName())) {
                    JsfUtil.addErrorMessage("不合格的设备设施名称不能为空！");
                    verifyFailed = true;
                }
            }
        }
        // 开展职业病防护用品防护效果检测
        if (this.occhethCard.getHasJcUse()) {
            boolean emptyJcUseNum = ObjectUtil.isEmpty(this.occhethCard.getJcUseNum());
            boolean emptyJcNotHgUseNum = ObjectUtil.isEmpty(this.occhethCard.getJcNotHgUseNum());
            if (isSubmit && emptyJcUseNum) {
                JsfUtil.addErrorMessage("检测防护用品数量不能为空！");
                verifyFailed = true;
            }
            if (!emptyJcUseNum && this.occhethCard.getJcUseNum() <= 0) {
                JsfUtil.addErrorMessage("检测防护用品数量应大于0！");
                verifyFailed = true;
            }
            if (isSubmit && emptyJcNotHgUseNum) {
                JsfUtil.addErrorMessage("结果不合格的防护用品数量不能为空！");
                verifyFailed = true;
            }
            if (!emptyJcNotHgUseNum && this.occhethCard.getJcNotHgUseNum() < 0) {
                JsfUtil.addErrorMessage("结果不合格的防护用品数量应大于等于0！");
                verifyFailed = true;
            }
            if (!emptyJcUseNum && !emptyJcNotHgUseNum) {
                if (this.occhethCard.getJcNotHgUseNum() > this.occhethCard.getJcUseNum()) {
                    JsfUtil.addErrorMessage("结果不合格的防护用品数量应小于等于检测防护用品数量！");
                    verifyFailed = true;
                }
                if (isSubmit && this.occhethCard.getJcNotHgUseNum() > 0
                        && ObjectUtil.isEmpty(this.occhethCard.getNotHgUseName())) {
                    JsfUtil.addErrorMessage("不合格的防护用品名称不能为空！");
                    verifyFailed = true;
                }
            }
        }

        //单位负责人
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getOrgFzMan())) {
            JsfUtil.addErrorMessage("单位负责人不能为空！");
            verifyFailed = true;
        }
        //填表人
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getFillFormPsn())) {
            JsfUtil.addErrorMessage("填表人不能为空！");
            verifyFailed = true;
        }
        //填表人联系电话
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getFillLink())) {
            JsfUtil.addErrorMessage("填表人联系电话不能为空！");
            verifyFailed = true;
        }
        if (ObjectUtil.isNotEmpty(this.occhethCard.getFillLink())
                && !StringUtils.vertyPhone(this.occhethCard.getFillLink())) {
            JsfUtil.addErrorMessage("填表人联系电话格式错误！");
            verifyFailed = true;
        }
        //填表日期
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getFillDate())) {
            JsfUtil.addErrorMessage("请选择填表日期！");
            verifyFailed = true;
        }
        if (ObjectUtil.isNotEmpty(this.occhethCard.getFillDate()) && ObjectUtil.isNotEmpty(this.occhethCard.getRptDate())
                && DateUtils.isCompareDate(this.occhethCard.getFillDate(), "<", this.occhethCard.getRptDate())) {
            JsfUtil.addErrorMessage("填表日期应大于等于出具技术服务报告时间！");
            verifyFailed = true;
        }
        return verifyFailed;
    }

    /**
     * 保存前处理报送卡实体操作
     */
    private void saveOcchethCardBefore() {
        //报告卡编码
        //ZYWSJC+职业卫生技术服务机构地区编码10位+年月日+5位流水
        if (ObjectUtil.isEmpty(this.occhethCard.getCardNo())) {
            String aa = this.commService.getAutoCode("OCCHETH_CARD_CODE", null);
            String cardNo = "ZYWSJC" + this.occhethCard.getOrgZoneGb() + DateUtils.formatDate(new Date(), "yyyyMMdd") + aa;
            this.occhethCard.setCardNo(cardNo);
        }
        if (ObjectUtil.isEmpty(this.occhethCard.getCheckNo())) {
            String idcode = this.selServiceResultExtends3;
            /*if ("1".equals(this.selServiceResultExtends1)) {
                idcode = "ZYWS_TY_DQJC_CODE";
            } else if ("2".equals(this.selServiceResultExtends1)) {
                idcode = "ZYWS_TY_XZPJ_CODE";
            } else if ("3".equals(this.selServiceResultExtends1)) {
                idcode = "ZYWS_TY_XGPJ_CODE";
            }*/
            if (ObjectUtil.isNotEmpty(idcode)) {
                this.occhethCard.setCheckNo(StringUtils.objectToString(this.commService.getAutoCode(idcode, null)));
            }
        }
        //处理-参与人员信息
        List<TdZwOcchethCardPsn> occhethCardPsnList = this.occhethCard.getOcchethCardPsnList();
        //姓名排序
        Collections.sort(occhethCardPsnList, new Comparator<TdZwOcchethCardPsn>() {
            @Override
            public int compare(TdZwOcchethCardPsn p1, TdZwOcchethCardPsn p2) {
                Collator com = Collator.getInstance(Locale.CHINA);
                return com.compare(p1.getPsnName(), p2.getPsnName());
            }
        });
        for (TdZwOcchethCardPsn occhethCardPsn : occhethCardPsnList) {
            occhethCardPsn.setOcchethCardItemList(new ArrayList<TdZwOcchethCardItem>());
            for (String simpleCodeRid : occhethCardPsn.getOcchethCardItemSimpleCodeList()) {
                if (this.simpleCodeMap.containsKey(simpleCodeRid)) {
                    TdZwOcchethCardItem occhethCardItem = new TdZwOcchethCardItem();
                    occhethCardItem.setFkByMainId(occhethCardPsn);
                    occhethCardItem.setFkByItemId(this.simpleCodeMap.get(simpleCodeRid));
                    occhethCardItem.setCreateDate(new Date());
                    occhethCardItem.setCreateManid(Global.getUser().getRid());
                    occhethCardPsn.getOcchethCardItemList().add(occhethCardItem);
                }
            }
        }
        //处理-服务的用人单位信息-技术服务地址
        //地区排序
        List<TdZwOcchethCardZone> occhethCardZoneList = this.occhethCard.getOcchethCardZoneList();
        for (TdZwOcchethCardZone occhethCardZone : occhethCardZoneList) {
            if (ObjectUtil.isEmpty(occhethCardZone.getFkByZoneId()) || ObjectUtil.isEmpty(occhethCardZone.getZoneRid())) {
                occhethCardZone.setFkByZoneId(null);
            }
        }
        Collections.sort(occhethCardZoneList, new Comparator<TdZwOcchethCardZone>() {
            @Override
            public int compare(TdZwOcchethCardZone p1, TdZwOcchethCardZone p2) {
                Collator com = Collator.getInstance(Locale.CHINA);
                if (ObjectUtil.isEmpty(p1.getZoneGb())) {
                    return 1;
                } else if (ObjectUtil.isEmpty(p2.getZoneGb())) {
                    return -1;
                }
                return com.compare(p1.getZoneGb(), p2.getZoneGb());
            }
        });

        //处理-技术服务信息-技术服务领域
        this.occhethCard.setOcchethCardServiceList(new ArrayList<TdZwOcchethCardService>());
        for (String simpleCodeRid : this.occhethCard.getOcchethCardServiceSimpleCodeList()) {
            if (this.simpleCodeMap.containsKey(simpleCodeRid)) {
                TdZwOcchethCardService occhethCardService = new TdZwOcchethCardService();
                occhethCardService.setFkByMainId(this.occhethCard);
                occhethCardService.setFkByServiceAreaId(this.simpleCodeMap.get(simpleCodeRid));
                occhethCardService.setCreateDate(new Date());
                occhethCardService.setCreateManid(Global.getUser().getRid());
                this.occhethCard.getOcchethCardServiceList().add(occhethCardService);
            }
        }

        //处理-技术服务结果
        this.occhethCard.setIfBadrsnJc("1".equals(selServiceResultExtends1) ? 1 : 0);
        this.occhethCard.setIfStatusPj("2".equals(selServiceResultExtends1) ? 1 : 0);

        // 是否选择任一 开展职业病防护设备设施防护效果检测 or 开展职业病防护用品防护效果检测
        boolean hasInstUsePj = this.occhethCard.getHasJcInst() || this.occhethCard.getHasJcUse();
        if (hasInstUsePj) {
            this.occhethCard.setIfInstUsePj(1);
        } else {
            this.occhethCard.setIfInstUsePj(0);
        }
        // 职业病危害因素检测结果
        boolean existServiceResult = "1".equals(this.selServiceResultExtends1) || "2".equals(this.selServiceResultExtends1);
        if (existServiceResult) {
            serviceOcchethCardBefore();
        }
        // 开展职业病防护设备设施防护效果检测
        if (this.occhethCard.getHasJcInst()) {
            this.occhethCard.setIfJcInst(1);
            if (this.occhethCard.getJcNotHgInstNum() == null || this.occhethCard.getJcNotHgInstNum() == 0) {
                this.occhethCard.setNotHgInstName(null);
            }
        } else {
            this.occhethCard.setIfJcInst(0);
            this.occhethCard.setJcInstNum(null);
            this.occhethCard.setJcNotHgInstNum(null);
            this.occhethCard.setNotHgInstName(null);
        }
        // 开展职业病防护用品防护效果检测
        if (this.occhethCard.getHasJcUse()) {
            this.occhethCard.setIfJcUse(1);
            if (this.occhethCard.getJcNotHgUseNum() == null || this.occhethCard.getJcNotHgUseNum() == 0) {
                this.occhethCard.setNotHgUseName(null);
            }
        } else {
            this.occhethCard.setIfJcUse(0);
            this.occhethCard.setJcUseNum(null);
            this.occhethCard.setJcNotHgUseNum(null);
            this.occhethCard.setNotHgUseName(null);
        }
    }

    /**
     *  <p>方法描述：存储前技术服务结果相关信息点处理</p>
     * @MethodAuthor hsj 2025-05-21 14:38
     */
    private void serviceOcchethCardBefore() {
        this.occhethCard.setOcchethJcBadrsns(new ArrayList<TdZwOcchethJcBadrsn>());
        this.occhethCard.setOcchethPjBadrsns(new ArrayList<TdZwOcchethPjBadrsn>());
        this.occhethCard.setBpBadrsnList(new ArrayList<TdZwOcchethBpBadrsn>());
        initOcchethBadrsnType();
        //key为危害因素大类
        Map<Integer, List<TdZwOcchethBpSub>> badrsnSubMap = new HashMap<>();
        //职业病危害因素检测结果
        if (!CollectionUtils.isEmpty(this.badrsnSubList)) {
            for (TdZwOcchethBpSub badrsnSub : this.badrsnSubList) {
                List<TdZwOcchethBadrsnItem> badrsnItems = badrsnSub.getBadrsnItems();
                Integer key = badrsnSub.getFkByBadrsnId().getFkByBadrsnId().getRid();
                if (!badrsnSubMap.containsKey(key)) {
                    badrsnSubMap.put(key, new ArrayList<TdZwOcchethBpSub>());
                }
                badrsnSub.setBadrsnItems(new ArrayList<TdZwOcchethBadrsnItem>());
                if (!CollectionUtils.isEmpty(badrsnItems)) {
                    for (TdZwOcchethBadrsnItem badrsnItem : badrsnItems) {
                        if (badrsnItem.getIfFirst() != null && badrsnItem.getIfFirst()) {
                            continue;
                        }
                        badrsnItem.setRid(null);
                        badrsnItem.setCreateDate(new Date());
                        badrsnItem.setCreateManid(Global.getUser().getRid());
                        badrsnItem.setFkByMainId(badrsnSub);
                        badrsnSub.getBadrsnItems().add(badrsnItem);
                    }
                }
                badrsnSubMap.get(key).add(badrsnSub);
            }
        }

        //职业病危害因素
        this.occhethCard.setBpBadrsnList(new ArrayList<TdZwOcchethBpBadrsn>());
        for (String badrsn : this.occhethCard.getSelBadrsnList()) {
            TdZwOcchethBpBadrsn badrsnObj = new TdZwOcchethBpBadrsn();
            badrsnObj.setFkByMainId(this.occhethCard);
            badrsnObj.setFkByBadrsnId(new TsSimpleCode(Convert.toInt(badrsn)));
            badrsnObj.setCreateDate(new Date());
            badrsnObj.setCreateManid(Global.getUser().getRid());
            if (badrsnSubMap.containsKey(Convert.toInt(badrsn))) {
                List<TdZwOcchethBpSub> badrsnSubList = badrsnSubMap.get(Convert.toInt(badrsn));
                for (TdZwOcchethBpSub badrsnSub : badrsnSubList) {
                    badrsnSub.setRid(null);
                    badrsnSub.setCreateDate(new Date());
                    badrsnSub.setCreateManid(Global.getUser().getRid());
                    badrsnSub.setFkByMainId(badrsnObj);
                }
                badrsnObj.setBadrsnSubList(badrsnSubList);
            }
            this.occhethCard.getBpBadrsnList().add(badrsnObj);
        }
        //超标危害因素类型
        if (!CollectionUtils.isEmpty(this.occhethCard.getSelectedBadrsnIds())) {
            for (Integer badrsnId : this.occhethCard.getSelectedBadrsnIds()) {
                if("1".equals(this.selServiceResultExtends1)){
                    TdZwOcchethJcBadrsn badrsn = new TdZwOcchethJcBadrsn();
                    badrsn.setFkByMainId(this.occhethCard);
                    badrsn.setFkByBadrsnId(new TsSimpleCode(Convert.toInt(badrsnId)));
                    badrsn.setCreateDate(new Date());
                    badrsn.setCreateManid(Global.getUser().getRid());
                    this.occhethCard.getOcchethJcBadrsns().add(badrsn);
                }else if("2".equals(this.selServiceResultExtends1)){
                    TdZwOcchethPjBadrsn badrsn = new TdZwOcchethPjBadrsn();
                    badrsn.setFkByMainId(this.occhethCard);
                    badrsn.setFkByBadrsnId(new TsSimpleCode(Convert.toInt(badrsnId)));
                    badrsn.setCreateDate(new Date());
                    badrsn.setCreateManid(Global.getUser().getRid());
                    this.occhethCard.getOcchethPjBadrsns().add(badrsn);
                }
            }
        }
    }

    /**
     * 撤销操作
     */
    public void revokeAction() {
        if (this.mainRid == null) {
            return;
        }
        try {
            this.occhethCardService.revokeOcchethCardByRid(this.mainRid);
            JsfUtil.addSuccessMessage("撤销成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
        try {
            this.modInitAction();
            RequestContext.getCurrentInstance().update("tabView");
            if (ObjectUtil.isNotEmpty(this.occhethCard.getAnnexPath())) {
                RequestContext.getCurrentInstance().execute("disabledInput('true','occHethCard')");
            }
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("跳转编辑页面失败！");
        }
    }

    /**
     * 制作文书
     */
    public void buildWritReport() {
        if (verifyFailed(true) || verifyCheckIn()) {
            return;
        }
        this.reportFilePath = null;
        String returnJson = "";
        try {
            saveOcchethCard(0, false);
            WordReportJson reportJson = new WordReportJson();
            reportJson.setRid(this.occhethCard.getRid());
            reportJson.setRptCode("HETH_RPT_1002");
            reportJson.setIfPdf(1);
            String requestJson = JSON.toJSONString(reportJson);
            String debug = PropertyUtils.getValue("encrypt.debug");
            String encryptKey = PropertyUtils.getValue("encrypt.key");
            //JSON是否需要加密
            String encodeJson = "true".equals(debug) ? requestJson : AesEncryptUtils.aesEncrypt(requestJson, encryptKey);
            //调用接口
            String delUrl = PropertyUtils.getValue("delUrl");
            String reposeJson = HttpRequestUtil.httpRequestByRaw(delUrl + "/word/generate", encodeJson);
            //JSON是否需要解密
            returnJson = "true".equals(debug) ? reposeJson : AesEncryptUtils.aesDecrypt(reposeJson, encryptKey);
        } catch (Exception e) {
            JsfUtil.addErrorMessage("生成文书失败！");
            e.printStackTrace();
            return;
        }
        if (StringUtils.isNotBlank(returnJson)) {
            WordReportDTO returnDTO = JSON.parseObject(returnJson, WordReportDTO.class);
            if (null == returnDTO || !ReturnType.SUCCESS_PROCESS.getTypeNo().equals(returnDTO.getType())) {
                logger.error(null == returnDTO ? "" : returnDTO.getMess());
                JsfUtil.addErrorMessage("生成文书失败！");
                return;
            } else {
                reportFilePath = returnDTO.getFilePath();
                if (StringUtils.isNotBlank(reportFilePath)) {
                    RequestContext.getCurrentInstance().execute("openPDFClick()");
                } else {
                    JsfUtil.addErrorMessage("生成文书失败！");
                    return;
                }
            }
        }
        RequestContext.getCurrentInstance().update("tabView:editForm:editPanel");
    }

    /**
     * 文书上传前验证
     */
    public void beforeUpload() {
        if (verifyFailed(true) || verifyCheckIn()) {
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('FileDialog').show();");
        RequestContext.getCurrentInstance().update("fileDialog");
    }

    /**
     * 文书上传
     */
    public void fileUpload(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            try {
                // 文件名称
                String fileName = file.getFileName();
                String errorMsg = FileUtils.veryFile(file.getInputstream(), file.getContentType(), fileName, "2");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    return;
                }
                String uuid = UUID.randomUUID().toString().replaceAll("-", "");
                String path = JsfUtil.getAbsolutePath();
                String relativePath = "heth/comm/reportcard/" + uuid + fileName.substring(fileName.lastIndexOf("."));
                // 文件路径
                String filePath = path + relativePath;
                this.occhethCard.setAnnexPath(relativePath);
                FileUtils.copyFile(filePath, file.getInputstream());
                saveOcchethCard(0, false);
                RequestContext currentInstance = RequestContext.getCurrentInstance();
                currentInstance.execute("PF('FileDialog').hide();");
                currentInstance.update("tabView:editForm:editPanel");
                currentInstance.execute("disabledInput('true','occHethCard')");
                JsfUtil.addSuccessMessage("上传成功！");
                this.searchAction();
            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("上传失败！");
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 删除文书
     */
    public void delMadedwrit() {
        try {
            this.occhethCardService.deleteOcchethCardAnnexPathByRid(this.occhethCard.getRid());
            JsfUtil.addSuccessMessage("删除成功！");
            modInit();
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.update("tabView:editForm:editPanel");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * 新增参与人员信息
     */
    public void addOcchethCardPsn() {
        Map<String, Object> options = new HashMap<>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("height", 495);
        options.put("width", 660);
        options.put("contentWidth", 630);
        options.put("contentHeight", 490);

        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add(StringUtils.list2string(this.occhethCard.getOcchethCardPsnRidList(), ","));
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<>();
        paramList.add("2");
        paramMap.put("type", paramList);
        paramList = new ArrayList<>();
        paramList.add(this.occhethCard.getOrgId());
        paramMap.put("orgId", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/dialog/selectOrgPsnList.xhtml", options, paramMap);
    }

    /**
     * 选择人员后操作
     *
     * @param event 选择的人员
     */
    public void onOcchethCardPsnSel(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (ObjectUtil.isNotEmpty(selectedMap)) {
            List<SrvorgPsnVo> list = CollectionUtil.castList(SrvorgPsnVo.class, selectedMap.get("selectPros"));
            if (ObjectUtil.isNotEmpty(list)) {
                for (SrvorgPsnVo t : list) {
                    if (this.occhethCard.getOcchethCardPsnRidList().contains(t.getRid().toString())) {
                        continue;
                    }
                    TdZwOcchethCardPsn occhethCardPsn = new TdZwOcchethCardPsn();
                    occhethCardPsn.setFkByMainId(this.occhethCard);
                    occhethCardPsn.setFkByPsnId(new TdZwPsninfoComm(t.getRid()));
                    occhethCardPsn.setPsnName(t.getEmpName());
                    occhethCardPsn.setCreateDate(new Date());
                    occhethCardPsn.setCreateManid(Global.getUser().getRid());
                    this.occhethCard.getOcchethCardPsnList().add(occhethCardPsn);
                    this.occhethCard.getOcchethCardPsnRidList().add(t.getRid().toString());
                }
            }
        }
    }

    /**
     * 删除参与人员信息
     *
     * @param occhethCardPsn 参与人员信息
     */
    public void delOcchethCardPsn(TdZwOcchethCardPsn occhethCardPsn) {
        this.occhethCard.getOcchethCardPsnList().remove(occhethCardPsn);
        for (int i = this.occhethCard.getOcchethCardPsnRidList().size() - 1; i >= 0; i--) {
            if (this.occhethCard.getOcchethCardPsnRidList().get(i).equals(occhethCardPsn.getFkByPsnId().getRid().toString())) {
                this.occhethCard.getOcchethCardPsnRidList().remove(i);
                break;
            }
        }
    }

    /**
     * 用人单位选择
     */
    public void selectCrptList() {
        Map<String, Object> options = MapUtils.produceDialogMap(1077, 1050, 520, 505);
        Map<String, List<String>> paramMap = new HashMap<>();
        CrptDialogParamJson json = new CrptDialogParamJson();
        json.setBusType("4");
        json.setIfShowOtherZone(true);
        json.setIfSearchZoneDefault(false);
        if (ObjectUtil.isNotEmpty(this.occhethCard.getCrptName())) {
            json.setSearchCrptName(this.occhethCard.getCrptName());
        }
        List<String> paramList = new ArrayList<>();
        paramList.add(JSON.toJSONString(json));
        paramMap.put("jsonParam", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        String useNewCrptSelect = this.commService.findParamValue("USE_NEW_CRPT_SELECT");
        String crptSelectPath = "/webapp/heth/comm/crptCommSelectList";
        if ("1".equals(useNewCrptSelect)) {
            crptSelectPath = "/webapp/heth/comm/crptCommSelectListNew";
        }
        requestContext.openDialog(crptSelectPath, options, paramMap);
    }

    /**
     * 选择用人单位后
     *
     * @param event 选择的用人单位
     */
    public void onCrptSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            TbTjCrptIndepend tbTjCrptIndepend = (TbTjCrptIndepend) selectedMap.get("selectCrptIndepend");
            TbTjCrpt tbTjCrpt = tbTjCrptIndepend.getFkByCrptId();
            this.occhethCard.setFkByCrptId(tbTjCrpt);
            this.occhethCard.setFkByZoneId(tbTjCrpt.getTsZoneByZoneId());
            this.occhethCard.setCrptName(tbTjCrpt.getCrptName());
            this.occhethCard.setAddress(tbTjCrpt.getAddress());
            this.occhethCard.setSafeposition(tbTjCrptIndepend.getLinkman2());
            this.occhethCard.setSafephone(tbTjCrptIndepend.getLinkphone2());
            this.occhethCard.setCreditCode(tbTjCrpt.getInstitutionCode());
            this.occhethCard.setFkByCrptSizeId(tbTjCrpt.getTsSimpleCodeByCrptSizeId());
        }
    }

    /**
     * 新增技术服务地址
     */
    public void addOcchethCardZone() {
        TdZwOcchethCardZone occhethCardZone = new TdZwOcchethCardZone();
        occhethCardZone.setFkByMainId(this.occhethCard);
        occhethCardZone.setFkByZoneId(new TsZone());
        occhethCardZone.setCreateDate(new Date());
        occhethCardZone.setCreateManid(Global.getUser().getRid());
        this.occhethCard.getOcchethCardZoneList().add(occhethCardZone);
    }

    /**
     * 选择技术服务地址
     *
     * @param occhethCardZone 当前选择的技术服务地址
     */
    public void onOcchethCardZoneSelect(TdZwOcchethCardZone occhethCardZone) {
        if (ObjectUtil.isNotEmpty(occhethCardZone.getZoneRid())
                && this.editZoneMap.containsKey(occhethCardZone.getZoneRid())) {
            TsZone zone = this.editZoneMap.get(occhethCardZone.getZoneRid());
            occhethCardZone.setFkByZoneId(zone);
            occhethCardZone.setFullName(zone.getFullName());
        }
    }

    /**
     * 删除技术服务地址
     *
     * @param occhethCardZone 当前选择的技术服务地址
     */
    public void delOcchethCardZone(TdZwOcchethCardZone occhethCardZone) {
        this.occhethCard.getOcchethCardZoneList().remove(occhethCardZone);
    }

    /**
     * <p>方法描述：添加职业病危害因素和对应的检测</p>
     *
     * @MethodAuthor hsj 2025-05-17 10:05
     */
    public void openBadrsnAddJcDialog() {
        initBadRsn();
        showAddBadrsnDialog();
    }

    /**
     *  <p>方法描述：添加职业病危害因素和对应的检测</p>
     * @MethodAuthor hsj 2025-05-22 15:54
     */
    private void showAddBadrsnDialog() {
        this.occhethBpBadrsnSub = new TdZwOcchethBpSub();
        RequestContext.getCurrentInstance().execute("PF('AddBadrsnJcDialog').show()");
        RequestContext.getCurrentInstance().execute("disabledInputReturn()");
        RequestContext.getCurrentInstance().update("tabView:editForm:addBadrsnJcDialog");
    }

    /**
     * <p>方法描述：修改职业病危害因素和对应的检测</p>
     *
     * @MethodAuthor hsj 2025-05-17 10:05
     */
    public void openBadrsnUpdateJcDialog() {
        List<TdZwOcchethBadrsnItem> itemList = this.occhethBpBadrsnSub.getBadrsnItems();
        List<TdZwOcchethBadrsnItem> itemNew = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemList)) {
            for (TdZwOcchethBadrsnItem item : itemList) {
                if (item.getIfFirst() != null && item.getIfFirst()) {
                    continue;
                }
                itemNew.add(item);
            }
        }
        this.occhethBpBadrsnSub.setBulletBadrsnItems(itemNew);
        initItem(this.occhethBpBadrsnSub.getFkByBadrsnId().getRid());
        initBadRsn();
        this.occhethBpBadrsnSub.setState("1");
        this.occhethBpBadrsnSub.setOldBadrsnId(this.occhethBpBadrsnSub.getFkByBadrsnId().getRid());
        this.occhethBpBadrsnSub.setBulletBadrsnId(this.occhethBpBadrsnSub.getFkByBadrsnId());
        this.occhethBpBadrsnSub.setBadrsnName(this.occhethBpBadrsnSub.getFkByBadrsnId().getRsnCnName());
        this.occhethBpBadrsnSub.setBulletBadrsnNum(this.occhethBpBadrsnSub.getBadrsnNum());
        this.occhethBpBadrsnSub.setBulletPostNum(this.occhethBpBadrsnSub.getPostNum());
        this.occhethBpBadrsnSub.setBulletOverPostNum(this.occhethBpBadrsnSub.getOverPostNum());
        RequestContext.getCurrentInstance().execute("PF('AddBadrsnJcDialog').show()");
        RequestContext.getCurrentInstance().execute("disabledInputReturn()");
    }
    /**
     *  <p>方法描述：已选择的危害因素大类</p>
     * @MethodAuthor hsj 2025-05-20 16:48
     */
    private void initBadRsn() {
        this.firsBadRsntList = new ArrayList<>();
        //已选择的危害因素大类
        List<String> selBadrsnList = this.occhethCard.getSelBadrsnList();
        if (!CollectionUtils.isEmpty(selBadrsnList)) {
            for (String badrsn : selBadrsnList) {
                if (!this.simpleCodeMap.containsKey(badrsn)) {
                    continue;
                }
                this.firsBadRsntList.add(this.simpleCodeMap.get(badrsn));
            }
            sortSimpleCodeList(this.firsBadRsntList);
        }
    }

    /**
     * <p>方法描述：初始化检测项目</p>
     *
     * @MethodAuthor hsj 2025-05-19 15:40
     */
    public void initItem(Integer badRsnId) {
        itemList2 = new ArrayList<>();
        List<Object[]> itemObjList = new ArrayList<>();
        itemObjList = occhethCardService.findItemByBadrsnId(badRsnId, null);
        if (!CollectionUtils.isEmpty(itemObjList)) {
            itemList2.addAll(itemObjList);
            for (Object[] objects : itemObjList) {
                TsSimpleCode newitem = new TsSimpleCode();
                newitem.setRid(Integer.parseInt(objects[0].toString()));
                newitem.setCodeName(objects[1].toString());
                newitem.setNum(objects[7] != null ? Integer.parseInt(objects[7].toString()) : null);
                objects[8] = objects[1].toString().replaceAll("(<sub>)|(</sub>)|(<sup>)|(</sup>)", "");
                if (!itemMap.containsKey(newitem.getRid())) {
                    itemMap.put(newitem.getRid(), newitem);
                }
            }
        }
    }

    /**
     * <p>方法描述：职业病危害因素弹出框-提交 职业病危害因素检测</p>
     *
     * @MethodAuthor hsj 2025-05-17 13:54
     */
    public void addBadrsnItemAction() {
        if (validateBadRsnItem()) {
            return;
        }
        handleBadrsnSub();
        SystemMessageEnum.SAVE_SUCCESS.showMessage();
        RequestContext.getCurrentInstance().execute("PF('AddBadrsnJcDialog').hide()");
    }
    private void handleBadrsnSub() {
        // 添加
        if ("0".equals(occhethBpBadrsnSub.getState())) {
            this.badrsnSubList.add(this.occhethBpBadrsnSub);
        }
        // 超标危害因素类型回显
        this.initOcchethBadrsnType();
        // 计算行合并
        OcchethCardBadrsnItemSort(true);
        // 更新页面组件
        RequestContext.getCurrentInstance().update("tabView:editForm:servicesResultPanel");
        RequestContext.getCurrentInstance().update("tabView:editForm:addBadrsnBtn");
        RequestContext.getCurrentInstance().update("tabView:editForm:badrsnJcPanel");
        RequestContext.getCurrentInstance().update("tabView:editForm:searchBadrsnPanel");
    }
    /**
     *  <p>方法描述：保存验证</p>
     * @MethodAuthor hsj 2025-05-22 15:53
     */
    private boolean validateBadRsnItem() {
        boolean flag = false;
        if (ObjectUtil.isNull(this.occhethBpBadrsnSub.getBulletBadrsnId()) || ObjectUtil.isNull(this.occhethBpBadrsnSub.getBulletBadrsnId().getRid())) {
            SystemMessageEnum.PLEASE_SELECT.formatMessage("职业病危害因素");
            flag = true;
        }
        if(ObjectUtil.isNotNull(this.occhethBpBadrsnSub.getBulletBadrsnNum()) && new Integer(0).equals(this.occhethBpBadrsnSub.getBulletBadrsnNum())){
            SystemMessageEnum.GT_FOR_NUMBER.formatMessage("接触危害人数","0");
            flag = true;
        }
        if (ObjectUtil.isNull(this.occhethBpBadrsnSub.getBulletPostNum())) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("岗位/工种数");
            flag = true;
        }else if(new Integer(0).equals(this.occhethBpBadrsnSub.getBulletPostNum())){
            SystemMessageEnum.GT_FOR_NUMBER.formatMessage("岗位/工种数","0");
            flag = true;
        }
        if (ObjectUtil.isNull(this.occhethBpBadrsnSub.getBulletOverPostNum())) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("超标岗位/工种数");
            flag = true;
        }
        //超标岗位/工种数 小于等于 “岗位/工种数”
        if (ObjectUtil.isNotNull(this.occhethBpBadrsnSub.getBulletPostNum()) && ObjectUtil.isNotNull(this.occhethBpBadrsnSub.getBulletOverPostNum())
                && this.occhethBpBadrsnSub.getBulletPostNum() < this.occhethBpBadrsnSub.getBulletOverPostNum()) {
            SystemMessageEnum.GE_FOR_NUMBER.formatMessage("岗位/工种数", "超标岗位/工种数");
            flag = true;
        }
        //是否为化学、粉尘、物理
        boolean isFlag = "1".equals(this.occhethBpBadrsnSub.getBadrsnEx1()) || "2".equals(this.occhethBpBadrsnSub.getBadrsnEx1()) || "3".equals(this.occhethBpBadrsnSub.getBadrsnEx1());
        List<TdZwOcchethBadrsnItem> badrsnItems = this.occhethBpBadrsnSub.getBulletBadrsnItems();
        if(isFlag){
            //超标岗位/工种数等于0且存在明细
            boolean f = ObjectUtil.isNotNull(this.occhethBpBadrsnSub.getBulletOverPostNum())
                    && this.occhethBpBadrsnSub.getBulletOverPostNum() == 0
                    && !CollectionUtils.isEmpty(badrsnItems);
            if(f){
                JsfUtil.addErrorMessage("超标岗位/工种数为0时，超标岗位/工种明细应为空！");
                flag = true;
            }
            //超标岗位/工种数大于0且不存在明细
            f = ObjectUtil.isNotNull(this.occhethBpBadrsnSub.getBulletOverPostNum())
                    && this.occhethBpBadrsnSub.getBulletOverPostNum() > 0
                    && CollectionUtils.isEmpty(badrsnItems);
            if(f){
                JsfUtil.addErrorMessage("超标岗位/工种数大于0时，超标岗位/工种明细至少有一条记录！");
                flag = true;
            }
            //超标岗位/工种数大于0且存在明细
            f = ObjectUtil.isNotNull(this.occhethBpBadrsnSub.getBulletOverPostNum())
                    && this.occhethBpBadrsnSub.getBulletOverPostNum() > 0
                    && !CollectionUtils.isEmpty(badrsnItems);
            if(f){
                boolean ifExist = false;
                int count = 1;
                Set<String> allSet = new HashSet<>();//超标岗位/工种名称+超标检测项目+超标检测指标
                Set<String> repeatSet = new HashSet<>();//重复的
                Set<String> postName = new HashSet<>();//
                for (TdZwOcchethBadrsnItem badrsnItem : badrsnItems) {
                    if (StringUtils.isBlank(badrsnItem.getPostName())) {
                        SystemMessageEnum.DYNAMIC_HEAD_NOT_EMPTY.formatMessage(count, "超标岗位/工种名称");
                        flag = true;
                        ifExist = true;
                    }else{
                        postName.add(badrsnItem.getPostName());
                    }
                    if (badrsnItem.getItemId() == null) {
                        SystemMessageEnum.DYNAMIC_HEAD_PLEASE_SELECT.formatMessage(count, "超标检测项目");
                        flag = true;
                        ifExist = true;
                    }
                    if (badrsnItem.getIndexRid() == null) {
                        SystemMessageEnum.DYNAMIC_HEAD_PLEASE_SELECT.formatMessage(count, "超标检测指标");
                        flag = true;
                        ifExist = true;
                    }
                    if (badrsnItem.getRst() == null) {
                        SystemMessageEnum.DYNAMIC_HEAD_NOT_EMPTY.formatMessage(count, "超标检测值");
                        flag = true;
                        ifExist = true;
                    }
                    count++;
                    //超标岗位/工种名称 +超标检测项目 +超标检测指标 要唯一
                    if(StringUtils.isNotBlank(badrsnItem.getPostName()) && badrsnItem.getItemId() != null && badrsnItem.getIndexRid() != null){
                        String key = badrsnItem.getPostName() +"&"+ badrsnItem.getItemId()+ badrsnItem.getIndexRid();
                        if(allSet.contains(key)){
                            String name = badrsnItem.getPostName()+"的超标检测项目（"+this.itemMap.get(badrsnItem.getItemId()).getCodeName()+"）和超标检测指标（"+this.indexMap.get(badrsnItem.getIndexRid()).getCodeName()+"）";
                            repeatSet.add(name);
                        }else{
                            allSet.add(key);
                        }
                    }
                }
                //存在重复 超标岗位/工种名称 +超标检测项目 +超标检测指标
                if(!CollectionUtils.isEmpty(repeatSet)){
                    for (String key : repeatSet) {
                        SystemMessageEnum.NOT_REPEAT.formatMessage(key);
                        flag = true;
                        ifExist = true;
                    }
                }
                //表格中信息点全部验证通过后判断数量是否匹配成功
                if(!ifExist){
                    if(!this.occhethBpBadrsnSub.getBulletOverPostNum().equals(postName.size())){
                        JsfUtil.addErrorMessage("超标岗位/工种数与超标岗位/工种明细记录数不一致！");
                        flag = true;
                    }
                }
            }
        }else{
            this.occhethBpBadrsnSub.setBadrsnItems(new ArrayList<TdZwOcchethBadrsnItem>());
        }
        if(!flag){
            this.occhethBpBadrsnSub.setBadrsnItems(badrsnItems);
            this.occhethBpBadrsnSub.setFkByBadrsnId(this.occhethBpBadrsnSub.getBulletBadrsnId());
            this.occhethBpBadrsnSub.setBadrsnEx1(this.occhethBpBadrsnSub.getBulletBadrsnId().getFkByBadrsnId().getExtendS1());
            this.occhethBpBadrsnSub.setBadrsnNum(this.occhethBpBadrsnSub.getBulletBadrsnNum());
            this.occhethBpBadrsnSub.setPostNum(this.occhethBpBadrsnSub.getBulletPostNum());
            this.occhethBpBadrsnSub.setOverPostNum(this.occhethBpBadrsnSub.getBulletOverPostNum());
        }
        return flag;
    }

    /**
     *  <p>方法描述：职业病危害因素弹出框-下一个</p>
     * @MethodAuthor hsj 2025-05-20 15:10
     */
    public void nextBadrsnItemAction(){
        if (validateBadRsnItem()) {
            return;
        }
        handleBadrsnSub();
        this.occhethBpBadrsnSub = new TdZwOcchethBpSub();
        RequestContext.getCurrentInstance().execute("disabledInputReturn()");
        JsfUtil.addSuccessMessage("连续保存成功！");
    }

    /**
     *  <p>方法描述：危害因素的选择</p>
     * @MethodAuthor hsj 2025-05-20 14:08
     */
    public void chooseBadrsnAction(){
        if(CollectionUtils.isEmpty(this.badrsnSubList)){
            return;
        }
        List<String> list = this.occhethCard.getSelBadrsnList();
        if(CollectionUtils.isEmpty(list)){
            this.badrsnSubList = new ArrayList<>();
            initOcchethBadrsnType();
            return;
        }
        List<TdZwOcchethBpSub> removeList = new ArrayList<>();
        for (TdZwOcchethBpSub bpSub : this.badrsnSubList){
            String badrsnId = Convert.toStr(bpSub.getFkByBadrsnId().getFkByBadrsnId().getRid());
            if(!list.contains(badrsnId)){
                removeList.add(bpSub);
            }
        }
        if(!CollectionUtils.isEmpty(removeList)){
            this.badrsnSubList.removeAll(removeList);
        }
        initOcchethBadrsnType();
    }

    /**
     * <p>方法描述：添加超标的在岗状态与检测指标</p>
     *
     * @MethodAuthor hsj 2025-05-17 13:41
     */
    public void addOcchethCardBadrsnItem() {
        TdZwOcchethBadrsnItem occhethBadrsnItem = new TdZwOcchethBadrsnItem();
        occhethBadrsnItem.setFkByMainId(this.occhethBpBadrsnSub);
        if (!CollectionUtils.isEmpty(itemList2) && itemList2.size() == 1) {
            //检测项目存在且只有一条记录时 默认第一条
            TbYsjcRsnRelItemComm itemComm = new TbYsjcRsnRelItemComm();
            itemComm.setRid(Integer.parseInt(itemList2.get(0)[0].toString()));
            itemComm.setNum(Integer.parseInt(itemList2.get(0)[7].toString()));
            occhethBadrsnItem.setFkByItemId(itemComm);
            occhethBadrsnItem.setItemId(itemComm.getRid());
            changeItemAction(occhethBadrsnItem, true);
        } else {
            //检测项目不存在或存在多条记录时 清空
            occhethBadrsnItem.setFkByItemId(null);
            occhethBadrsnItem.setItemId(null);
            occhethBadrsnItem.setMsruntTree(new DefaultTreeNode());
            occhethBadrsnItem.setFkByMsruntId(null);
            occhethBadrsnItem.setMsruntName(null);
        }
        occhethBadrsnItem.setIndexRid(null);
        occhethBadrsnItem.setCreateDate(new Date());
        occhethBadrsnItem.setCreateManid(Global.getUser().getRid());
        if (this.occhethBpBadrsnSub.getBulletBadrsnItems() == null) {
            this.occhethBpBadrsnSub.setBulletBadrsnItems(new ArrayList<TdZwOcchethBadrsnItem>());
        }
        this.occhethBpBadrsnSub.getBulletBadrsnItems().add(occhethBadrsnItem);
        RequestContext.getCurrentInstance().execute("disabledInputReturn();");
    }


    /**
     * <p>方法描述：职业病危害因素弹出框 </p>
     *
     * @MethodAuthor hsj 2025-05-19 10:21
     */
    public void openBadrsnDialog() {
        //危害因素列表初始化
        this.firstBadRsnId = null;
        this.searchNamOrPy = null;
        this.selBadRsn = null;
        badRsnSearch();
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:selectedBadRsnTable");
        dataTable.setFirst(0);
        dataTable.setRows(10);
        RequestContext.getCurrentInstance().execute("PF('BadRsnDialog').show()");
        RequestContext.getCurrentInstance().update("tabView:editForm:badRsnDialog");
        RequestContext.getCurrentInstance().update("tabView:editForm:searchPanel");
        RequestContext.getCurrentInstance().update("tabView:editForm:selectedBadRsnTable");
        RequestContext.getCurrentInstance().execute("disabledInputReturn();");
    }

    /**
     * <p>方法描述：确定切换相关危害因素</p>
     *
     * @MethodAuthor hsj 2025-05-19 11:16
     */
    public void changeBadRsn() {
        TsSimpleCode simpleCode = !this.simpleCodeMap.containsKey(Convert.toStr(this.selBadRsn.getParentId())) ? null : this.simpleCodeMap.get(Convert.toStr(this.selBadRsn.getParentId()));
        TbYsjcLimitValComm badRsnLimitValComm = new TbYsjcLimitValComm();
        badRsnLimitValComm.setRid(this.selBadRsn.getBadRsnId());
        badRsnLimitValComm.setRsnCnName(this.selBadRsn.getBadRsnName());
        badRsnLimitValComm.setFkByBadrsnId(simpleCode);
        this.occhethBpBadrsnSub.setBulletBadrsnId(badRsnLimitValComm);
        this.occhethBpBadrsnSub.setBadrsnName(this.selBadRsn.getBadRsnName());
        this.occhethBpBadrsnSub.setBulletBadrsnItems(new ArrayList<TdZwOcchethBadrsnItem>());
        this.occhethBpBadrsnSub.setBadrsnEx1(simpleCode.getExtendS1());
        initItem(this.selBadRsn.getBadRsnId());
        RequestContext.getCurrentInstance().update("tabView:editForm:badrsnJcPanel");
        RequestContext.getCurrentInstance().update("tabView:editForm:searchBadrsnPanel");
        RequestContext.getCurrentInstance().update("tabView:editForm:badrsnAddBtn");
        RequestContext.getCurrentInstance().execute("disabledInputReturn();");
    }

    /**
     * <p>方法描述：危害因素选择</p>
     *
     * @MethodAuthor hsj 2025-05-19 11:51
     */
    public void chooseBadRsn() {
        if (CollectionUtils.isEmpty(this.occhethBpBadrsnSub.getBulletBadrsnItems())) {
            changeBadRsn();
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmDelDialog').show()");
    }

    /**
     * <p>方法描述：危害因素弹出框查询</p>
     *
     * @MethodAuthor hsj 2025-05-19 10:23
     */
    public void badRsnSearch() {
        List<String> selBadrsnList = this.occhethCard.getSelBadrsnList();
        //当前已选择的
        Integer badRsnId = this.occhethBpBadrsnSub.getBulletBadrsnId() == null ? null : this.occhethBpBadrsnSub.getBulletBadrsnId().getRid();
        //之前已选择的
        Integer oldBadRsnId = this.occhethBpBadrsnSub.getOldBadrsnId();
        List<Integer> badRsnIds = new ArrayList<>();
        //表格中已选择的
        if(!CollectionUtils.isEmpty(this.badrsnSubList)){
            for(TdZwOcchethBpSub bpSub : this.badrsnSubList){
                Integer id = bpSub.getFkByBadrsnId() == null ? null : bpSub.getFkByBadrsnId().getRid();
                if(ObjectUtil.isNull(id)){
                    continue;
                }
                if(ObjectUtil.isNotNull(oldBadRsnId) && id.equals(oldBadRsnId)){
                    continue;
                }
                badRsnIds.add(id);
            }
        }
        if(ObjectUtil.isNotNull(badRsnId)){
            badRsnIds.add(badRsnId);
        }
        this.limitVals = this.occhethCardService.findBadrsnByBadrsnIds(selBadrsnList, badRsnIds, firstBadRsnId, searchNamOrPy);
    }
    /**
     *  <p>方法描述：清空危害因素</p>
     * @MethodAuthor hsj 2025-05-20 17:22
     */
    public void clearBpBadrsn(){
        this.occhethBpBadrsnSub.setBulletBadrsnId(null);
        this.occhethBpBadrsnSub.setBadrsnName(null);
        this.occhethBpBadrsnSub.setBadrsnEx1(null);
        this.occhethBpBadrsnSub.setBulletBadrsnItems(new ArrayList<TdZwOcchethBadrsnItem>());
    }
    /**
     * <p>方法描述：删除 职业病危害因素检测</p>
     *
     * @MethodAuthor hsj 2025-05-17 15:18
     */
    public void delOcchethBpBadrsn() {
        this.badrsnSubList.remove(occhethBpBadrsnSub);
        //超标危害因素类型回显
        this.initOcchethBadrsnType();
        // 计算行合并
        OcchethCardBadrsnItemSort(true);
    }

    /**
     * <p>方法描述：删除 职业病危害因素检测</p>
     *
     * @MethodAuthor hsj 2025-05-17 13:37
     */
    public void delBadrsnItemAction() {
        this.occhethBpBadrsnSub.getBulletBadrsnItems().remove(this.occhethBadrsnItem);
    }

    /**
     * <p>方法描述：检测项目 change 事件
     * 刷新计量单位</p>
     *
     * @MethodAuthor hsj 2025-05-17 11:53
     */
    public void changeItemAction(TdZwOcchethBadrsnItem item, Boolean flag) {
        if (item.getItemId() == null) {
            item.setFkByItemId(null);
            item.setMsruntTree(new DefaultTreeNode());
            item.setMsruntName(null);
            item.setFkByMsruntId(null);
            return;
        }
        item.setFkByItemId(new TbYsjcRsnRelItemComm(item.getItemId()));
        if (!CollectionUtils.isEmpty(itemList2)) {
            List<TsSimpleCode> msruntList = new ArrayList<>();
            for (Object[] objects : itemList2) {
                if (item.getItemId() == Integer.parseInt(objects[0].toString())) {
                    if (objects[3] != null && objects[4] != null) {
                        msruntList.add(new TsSimpleCode(Integer.parseInt(objects[3].toString()), objects[4].toString(), null));
                    }
                    if (objects[5] != null && objects[6] != null) {
                        msruntList.add(new TsSimpleCode(Integer.parseInt(objects[5].toString()), objects[6].toString(), null));
                    }
                    break;
                }
            }
            item.setMsruntTree(initMsruntTree(msruntList));
            if (flag) {
                //计量单位是一个时，默认选中
                if (!CollectionUtils.isEmpty(msruntList) && msruntList.size() == 1) {
                    item.setMsruntName(msruntList.get(0).getCodeName());
                    item.setFkByMsruntId(msruntList.get(0));
                } else {
                    item.setMsruntName(null);
                    item.setFkByMsruntId(null);
                }
            }
        }
    }


    /**
     * <p>方法描述：初始化计量单位特殊值码表</p>
     *
     * @MethodAuthor hsj 2025-05-20 11:00
     */
    private TreeNode initMsruntTree(List<TsSimpleCode> msruntList) {
        this.msruntTree = new DefaultTreeNode("root", null);
        if (!CollectionUtils.isEmpty(msruntList)) {
            for (TsSimpleCode t : msruntList) {
                new DefaultTreeNode(t, this.msruntTree);
            }
        }
        return msruntTree;
    }

    /**
     * <p>方法描述：选择计量单位特殊值</p>
     *
     * @MethodAuthor hsj 2025-05-17 13:28
     */
    public void onMsruntSelect(NodeSelectEvent event) {
        TreeNode selectNode = event.getTreeNode();
        if (null != selectNode) {
            TsSimpleCode t = (TsSimpleCode) selectNode.getData();
            this.occhethBadrsnItem.setFkByMsruntId(t);
            this.occhethBadrsnItem.setMsruntName(t.getCodeName());
        }
    }

    @Override
    public boolean updateQrCodeInfo() {
        boolean flag = true;
        if (!validateQrCodeInfo(true)) {
            flag = false;
        }
        if (verifyFailed(false) || verifyCheckIn()) {
            flag = false;
        }
        try {
            if (flag && !this.saveOcchethCard(0, true)) {
                SystemMessageEnum.DOWNLOAD_FAIL.showMessage();
                flag = false;
            }
        } catch(Exception e) {
            SystemMessageEnum.DOWNLOAD_FAIL.showMessage();
            e.printStackTrace();
            return false;
        }
        if (!flag) {
            return flag;
        }
        this.searchAction();
        return flag;
    }

    @Override
    public void exeDownloadQrCode() {
        String exe = "getDownloadFileClick()";
        if (null != this.ifViewPage && 1 == this.ifViewPage) {
            exe = "getViewDownloadFileClick()";
        }
        if (null != this.ifViewPage && 2 == this.ifViewPage) {
            exe = "getOtDownloadFileClick()";
        }
        RequestContext.getCurrentInstance().execute(exe);
    }

    public List<TsZone> getEditZoneList() {
        return editZoneList;
    }

    public void setEditZoneList(List<TsZone> editZoneList) {
        this.editZoneList = editZoneList;
    }

    public Map<Integer, TsZone> getEditZoneMap() {
        return editZoneMap;
    }

    public void setEditZoneMap(Map<Integer, TsZone> editZoneMap) {
        this.editZoneMap = editZoneMap;
    }

    public List<TsSimpleCode> getServicesUndertakenList() {
        return servicesUndertakenList;
    }

    public void setServicesUndertakenList(List<TsSimpleCode> servicesUndertakenList) {
        this.servicesUndertakenList = servicesUndertakenList;
    }

    public String getReportFilePath() {
        return reportFilePath;
    }

    public void setReportFilePath(String reportFilePath) {
        this.reportFilePath = reportFilePath;
    }


    public List<TsSimpleCode> getFirsBadRsntList() {
        return firsBadRsntList;
    }

    public void setFirsBadRsntList(List<TsSimpleCode> firsBadRsntList) {
        this.firsBadRsntList = firsBadRsntList;
    }

    public List<TbYsjcLimitValVO> getLimitVals() {
        return limitVals;
    }

    public void setLimitVals(List<TbYsjcLimitValVO> limitVals) {
        this.limitVals = limitVals;
    }

    public Object[] getSelectBadRsn() {
        return selectBadRsn;
    }

    public void setSelectBadRsn(Object[] selectBadRsn) {
        this.selectBadRsn = selectBadRsn;
    }

    public Integer getFirstBadRsnId() {
        return firstBadRsnId;
    }

    public void setFirstBadRsnId(Integer firstBadRsnId) {
        this.firstBadRsnId = firstBadRsnId;
    }

    public String getSearchNamOrPy() {
        return searchNamOrPy;
    }

    public void setSearchNamOrPy(String searchNamOrPy) {
        this.searchNamOrPy = searchNamOrPy;
    }

    public List<Object[]> getItemList2() {
        return itemList2;
    }

    public void setItemList2(List<Object[]> itemList2) {
        this.itemList2 = itemList2;
    }

    public TreeNode getMsruntTree() {
        return msruntTree;
    }

    public void setMsruntTree(TreeNode msruntTree) {
        this.msruntTree = msruntTree;
    }

    public TbYsjcLimitValVO getSelBadRsn() {
        return selBadRsn;
    }

    public void setSelBadRsn(TbYsjcLimitValVO selBadRsn) {
        this.selBadRsn = selBadRsn;
    }

    public Integer getContractId() {
        return contractId;
    }
    public void setContractId(Integer contractId) {
        this.contractId = contractId;
    }
}