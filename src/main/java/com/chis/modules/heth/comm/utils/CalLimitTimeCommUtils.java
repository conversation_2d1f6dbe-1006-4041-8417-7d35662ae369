package com.chis.modules.heth.comm.utils;

import com.chis.common.utils.DateUtils;
import com.chis.modules.heth.comm.enumn.QuarterDateCommEnum;

import java.text.ParseException;
import java.util.Date;

public class CalLimitTimeCommUtils {
    /**
     * <p>方法描述：计算超期
     * ifwork：期限是否工作日</p>
     * @MethodAuthor qrr,2019年9月16日,calLimitTime
     * */
    public static int calLimitTime(Date sDate, Integer due, boolean ifwork) {
        if (null==sDate) {
            return 0;
        }
        Date date = null;
        if (ifwork) {
            date = DateUtils.addDateByWorkDay(sDate, due+1);
        }else {
            date = DateUtils.addDateByDay(sDate, due+1);//接收日期+1后开始计算
        }
        if (null!=date && date.after(new Date())) {
            //剩余多少天
            int val = 0;
            if (ifwork) {
                val = DateUtils.getDistanceOfWorkDay(new Date(), date);
                if(val == 0) {
                    return -1;
                }
            }else {
                double days = DateUtils.getDistanceOfTwoDate(DateUtils.getDateOnly(new Date()), DateUtils.getDateOnly(date));
                val = Double.valueOf(days).intValue();

            }
            return val>due?due:val;
        }else {
            //超期
            return -1;
        }
    }
    /**
     * <p>方法描述：计算及时性
     * type:计算方式1:根据天数计算2：根据季度计算
     * ifwork:是否工作日
     * year：根据季度计算的年份
     * quarter：第几季度
     * </p>
     * @MethodAuthor qrr,2019年10月22日,calIfInTime
     * */
    public static Integer calIfInTime(Integer type, Date busDate, Integer due,
                                      boolean ifwork, Integer year, Integer quarter) {
        if (null==type) {
            return null;
        }
        Integer ifInTime = null;
        if (1==type) {
            if (null==busDate) {
                return null;
            }
            //是否及时
            if (calLimitTime(busDate, due,ifwork) < 0) {
                ifInTime = 0;
            } else {
                ifInTime = 1;
            }
        } else if (2==type) {
            if (null == year || null == quarter) {
                return null;
            }
            QuarterDateCommEnum[] dateEnums = QuarterDateCommEnum.values();
            for (QuarterDateCommEnum date : dateEnums) {
                if (null != quarter && null != date.getQuarter()
                        && quarter.intValue() == date.getQuarter().intValue()) {
                    Date now = DateUtils.getDateOnly(new Date());
                    try {
                        Date sdate = DateUtils.parseDate(
                                year + "-" + date.getSdate(), "yyyy-MM-dd");
                        Date edate = DateUtils.parseDate(
                                year + "-" + date.getEdate(), "yyyy-MM-dd");
                        if (now.before(sdate) || now.after(edate)) {
                            ifInTime = 0;
                        } else {
                            ifInTime = 1;
                        }
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    break;
                }
            }
        }
        return ifInTime;
    }
}
