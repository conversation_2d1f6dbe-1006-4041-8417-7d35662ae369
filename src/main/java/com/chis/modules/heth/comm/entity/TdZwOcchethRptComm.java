package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2023-7-6
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_RPT")
@SequenceGenerator(name = "TdZwOcchethRpt", sequenceName = "TD_ZW_OCCHETH_RPT_SEQ", allocationSize = 1)
public class TdZwOcchethRptComm implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwOcchethInfoComm fkByOrgId;
    private TbTjCrpt fkByCrptId;
    private String crptName;
    private TsZone fkByZoneId;
    private String creditCode;
    private String address;
    private TsSimpleCode fkByIndusTypeId;
    private TsSimpleCode fkByEconomyId;
    private String linkMan;
    private String linkPhone;
    private TsSimpleCode fkBySortId;
    private Date rptDate;
    private String rptNo;
    private String manageNo;
    private String fileName;
    private String filePath;
    private Integer state;
    private Integer delMark;
    private Integer createManid;
    private Date createDate;
    private Date modifyDate;
    private Integer modifyManid;
    private Integer chkWayType;
    private TsUnit fkByUnitId;
    private TsSimpleCode fkByCrptSizeId;
    private Integer zybRiskId;
    private String projectName;
    private Date bhkBeginDate;
    private Date bhkEndDate;
    private Integer bhkPsn;
    private String entrustFilePath;

    public TdZwOcchethRptComm() {
    }

    public TdZwOcchethRptComm(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethRpt")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "ORG_ID")
    public TdZwOcchethInfoComm getFkByOrgId() {
        return fkByOrgId;
    }

    public void setFkByOrgId(TdZwOcchethInfoComm fkByOrgId) {
        this.fkByOrgId = fkByOrgId;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_ID")
    public TbTjCrpt getFkByCrptId() {
        return fkByCrptId;
    }

    public void setFkByCrptId(TbTjCrpt fkByCrptId) {
        this.fkByCrptId = fkByCrptId;
    }

    @Column(name = "CRPT_NAME")
    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    @ManyToOne
    @JoinColumn(name = "ZONE_ID")
    public TsZone getFkByZoneId() {
        return fkByZoneId;
    }

    public void setFkByZoneId(TsZone fkByZoneId) {
        this.fkByZoneId = fkByZoneId;
    }

    @Column(name = "CREDIT_CODE")
    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    @Column(name = "ADDRESS")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @ManyToOne
    @JoinColumn(name = "INDUS_TYPE_ID")
    public TsSimpleCode getFkByIndusTypeId() {
        return fkByIndusTypeId;
    }

    public void setFkByIndusTypeId(TsSimpleCode fkByIndusTypeId) {
        this.fkByIndusTypeId = fkByIndusTypeId;
    }

    @ManyToOne
    @JoinColumn(name = "ECONOMY_ID")
    public TsSimpleCode getFkByEconomyId() {
        return fkByEconomyId;
    }

    public void setFkByEconomyId(TsSimpleCode fkByEconomyId) {
        this.fkByEconomyId = fkByEconomyId;
    }

    @Column(name = "LINK_MAN")
    public String getLinkMan() {
        return linkMan;
    }

    public void setLinkMan(String linkMan) {
        this.linkMan = linkMan;
    }

    @Column(name = "LINK_PHONE")
    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }

    @ManyToOne
    @JoinColumn(name = "SORT_ID")
    public TsSimpleCode getFkBySortId() {
        return fkBySortId;
    }

    public void setFkBySortId(TsSimpleCode fkBySortId) {
        this.fkBySortId = fkBySortId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "RPT_DATE")
    public Date getRptDate() {
        return rptDate;
    }

    public void setRptDate(Date rptDate) {
        this.rptDate = rptDate;
    }

    @Column(name = "RPT_NO")
    public String getRptNo() {
        return rptNo;
    }

    public void setRptNo(String rptNo) {
        this.rptNo = rptNo;
    }

    @Column(name = "MANAGE_NO")
    public String getManageNo() {
        return manageNo;
    }

    public void setManageNo(String manageNo) {
        this.manageNo = manageNo;
    }

    @Column(name = "FILE_NAME")
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Column(name = "FILE_PATH")
    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Column(name = "STATE")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Column(name = "DEL_MARK")
    public Integer getDelMark() {
        return delMark;
    }

    public void setDelMark(Integer delMark) {
        this.delMark = delMark;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Column(name = "CHK_WAY_TYPE")
    public Integer getChkWayType() {
        return chkWayType;
    }

    public void setChkWayType(Integer chkWayType) {
        this.chkWayType = chkWayType;
    }

    @ManyToOne
    @JoinColumn(name = "UNIT_ID")
    public TsUnit getFkByUnitId() {
        return fkByUnitId;
    }

    public void setFkByUnitId(TsUnit fkByUnitId) {
        this.fkByUnitId = fkByUnitId;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_SIZE_ID")
    public TsSimpleCode getFkByCrptSizeId() {
        return fkByCrptSizeId;
    }

    public void setFkByCrptSizeId(TsSimpleCode fkByCrptSizeId) {
        this.fkByCrptSizeId = fkByCrptSizeId;
    }

    @Column(name = "ZYB_RISK_ID")
    public Integer getZybRiskId() {
        return zybRiskId;
    }

    public void setZybRiskId(Integer zybRiskId) {
        this.zybRiskId = zybRiskId;
    }

    @Column(name = "PROJECT_NAME")
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BHK_BEGIN_DATE")
    public Date getBhkBeginDate() {
        return bhkBeginDate;
    }

    public void setBhkBeginDate(Date bhkBeginDate) {
        this.bhkBeginDate = bhkBeginDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BHK_END_DATE")
    public Date getBhkEndDate() {
        return bhkEndDate;
    }

    public void setBhkEndDate(Date bhkEndDate) {
        this.bhkEndDate = bhkEndDate;
    }

    @Column(name = "BHK_PSN")
    public Integer getBhkPsn() {
        return bhkPsn;
    }

    public void setBhkPsn(Integer bhkPsn) {
        this.bhkPsn = bhkPsn;
    }

    @Column(name = "ENTRUST_FILE_PATH")
    public String getEntrustFilePath() {
        return entrustFilePath;
    }

    public void setEntrustFilePath(String entrustFilePath) {
        this.entrustFilePath = entrustFilePath;
    }

}