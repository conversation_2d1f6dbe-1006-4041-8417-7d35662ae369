package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUserInfo;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2023-9-26
 */
@Entity
@Table(name = "TB_TJ_CRPT_WARN")
@SequenceGenerator(name = "TbTjCrptWarn", sequenceName = "TB_TJ_CRPT_WARN_SEQ", allocationSize = 1)
public class TbTjCrptWarnComm implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbTjCrpt fkByCrptId;
	private TsSimpleCode fkByWarnId;
	private Date warnDate;
	private TsSimpleCode fkByResultId;
	private Date dealDate;
	private TsUserInfo fkByDealPsnId;
	private String rmk;
	private Integer state;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	/**是否超期*/
	private boolean ifOvertime;
	/**处理结果rid*/
	private Integer resultId;
	
	public TbTjCrptWarnComm() {
	}

	public TbTjCrptWarnComm(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjCrptWarn")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "CRPT_ID")			
	public TbTjCrpt getFkByCrptId() {
		return fkByCrptId;
	}

	public void setFkByCrptId(TbTjCrpt fkByCrptId) {
		this.fkByCrptId = fkByCrptId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "WARN_ID")			
	public TsSimpleCode getFkByWarnId() {
		return fkByWarnId;
	}

	public void setFkByWarnId(TsSimpleCode fkByWarnId) {
		this.fkByWarnId = fkByWarnId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "WARN_DATE")			
	public Date getWarnDate() {
		return warnDate;
	}

	public void setWarnDate(Date warnDate) {
		this.warnDate = warnDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "RESULT_ID")			
	public TsSimpleCode getFkByResultId() {
		return fkByResultId;
	}

	public void setFkByResultId(TsSimpleCode fkByResultId) {
		this.fkByResultId = fkByResultId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "DEAL_DATE")			
	public Date getDealDate() {
		return dealDate;
	}

	public void setDealDate(Date dealDate) {
		this.dealDate = dealDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "DEAL_PSN_ID")			
	public TsUserInfo getFkByDealPsnId() {
		return fkByDealPsnId;
	}

	public void setFkByDealPsnId(TsUserInfo fkByDealPsnId) {
		this.fkByDealPsnId = fkByDealPsnId;
	}	
			
	@Column(name = "RMK")	
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Transient
	public boolean getIfOvertime() {
		return ifOvertime;
	}

	public void setIfOvertime(boolean ifOvertime) {
		this.ifOvertime = ifOvertime;
	}
	@Transient
	public Integer getResultId() {
		return resultId;
	}

	public void setResultId(Integer resultId) {
		this.resultId = resultId;
	}
}