package com.chis.modules.heth.comm.entity;

/**
 * 职业健康检查审核情况(体检机构)-退回数据导出
 *
 * <AUTHOR>
 * @date 2022/1/27
 */
public class TdTjBhkCheckOrgReturnedDataExportEntity {
    /**
     * 地区
     */
    private String zoneName;
    /**
     * 用人单位名称
     */
    private String employerName;
    /**
     * 姓名
     */
    private String personnelName;
    /**
     * 体检编号
     */
    private String bhkCode;
    /**
     * 在岗状态
     */
    private String jobStatus;
    /**
     * 监测类别
     */
    private String monitoringTypes;
    /**
     * 体检日期
     */
    private String bhkDate;
    /**
     * 报告出具日期
     */
    private String rptPrintDate;
    /**
     * 是否复检
     */
    private String recheck;
    /**
     * 是否异常
     */
    private String abnormal;
    /**
     * 异常情况
     */
    private String abnormalSituation;
    /**
     * 状态
     */
    private String status;
    /**
     * 退回原因
     */
    private String backRsn;

    public TdTjBhkCheckOrgReturnedDataExportEntity() {
    }

    public TdTjBhkCheckOrgReturnedDataExportEntity(String zoneName, String employerName, String personnelName, String bhkCode, String jobStatus, String monitoringTypes, String bhkDate, String recheck, String abnormal, String abnormalSituation, String status, String backRsn) {
        this.zoneName = zoneName;
        this.employerName = employerName;
        this.personnelName = personnelName;
        this.bhkCode = bhkCode;
        this.jobStatus = jobStatus;
        this.monitoringTypes = monitoringTypes;
        this.bhkDate = bhkDate;
        this.recheck = recheck;
        this.abnormal = abnormal;
        this.abnormalSituation = abnormalSituation;
        this.status = status;
        this.backRsn = backRsn;
    }

    /**
     * 设置部分参数
     *
     * @param zoneName      {@link #zoneName 地区}
     * @param employerName  {@link #employerName 用人单位名称}
     * @param personnelName {@link #personnelName 姓名}
     * @param bhkCode       {@link #bhkCode 体检编号}
     * @param jobStatus     {@link #jobStatus 在岗状态}
     */
    public void setPropertyA(String zoneName, String employerName, String personnelName, String bhkCode, String jobStatus) {
        this.zoneName = zoneName;
        this.employerName = employerName;
        this.personnelName = personnelName;
        this.bhkCode = bhkCode;
        this.jobStatus = jobStatus;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getEmployerName() {
        return employerName;
    }

    public void setEmployerName(String employerName) {
        this.employerName = employerName;
    }

    public String getPersonnelName() {
        return personnelName;
    }

    public void setPersonnelName(String personnelName) {
        this.personnelName = personnelName;
    }

    public String getBhkCode() {
        return bhkCode;
    }

    public void setBhkCode(String bhkCode) {
        this.bhkCode = bhkCode;
    }

    public String getJobStatus() {
        return jobStatus;
    }

    public void setJobStatus(String jobStatus) {
        this.jobStatus = jobStatus;
    }

    public String getMonitoringTypes() {
        return monitoringTypes;
    }

    public void setMonitoringTypes(String monitoringTypes) {
        this.monitoringTypes = monitoringTypes;
    }

    public String getBhkDate() {
        return bhkDate;
    }

    public void setBhkDate(String bhkDate) {
        this.bhkDate = bhkDate;
    }

    public String getRptPrintDate() {
        return rptPrintDate;
    }

    public void setRptPrintDate(String rptPrintDate) {
        this.rptPrintDate = rptPrintDate;
    }

    public String getRecheck() {
        return recheck;
    }

    public void setRecheck(String recheck) {
        this.recheck = recheck;
    }

    public String getAbnormal() {
        return abnormal;
    }

    public void setAbnormal(String abnormal) {
        this.abnormal = abnormal;
    }

    public String getAbnormalSituation() {
        return abnormalSituation;
    }

    public void setAbnormalSituation(String abnormalSituation) {
        this.abnormalSituation = abnormalSituation;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getBackRsn() {
        return backRsn;
    }

    public void setBackRsn(String backRsn) {
        this.backRsn = backRsn;
    }
}
