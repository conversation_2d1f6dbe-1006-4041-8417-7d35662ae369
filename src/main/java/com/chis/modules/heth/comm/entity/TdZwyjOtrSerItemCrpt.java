package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-11-16
 */
@Entity
@Table(name = "TD_ZWYJ_OTR_SER_ITEM_CRPT")
@SequenceGenerator(name = "TdZwyjOtrSerItemCrpt", sequenceName = "TD_ZWYJ_OTR_SER_ITEM_CRPT_SEQ", allocationSize = 1)
public class TdZwyjOtrSerItemCrpt implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwyjOtrCrptList fkByMainId;
	private TsSimpleCode fkByServiceId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjOtrSerItemCrpt() {
	}

	public TdZwyjOtrSerItemCrpt(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjOtrSerItemCrpt")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwyjOtrCrptList getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwyjOtrCrptList fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "SERVICE_ID")			
	public TsSimpleCode getFkByServiceId() {
		return fkByServiceId;
	}

	public void setFkByServiceId(TsSimpleCode fkByServiceId) {
		this.fkByServiceId = fkByServiceId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}