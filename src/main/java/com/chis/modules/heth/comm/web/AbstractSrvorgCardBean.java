package com.chis.modules.heth.comm.web;

import com.chis.common.utils.CacheUtils;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdZwSrvorgCard;
import com.chis.modules.heth.comm.logic.SimpleCodeTreePO;
import com.chis.modules.heth.comm.rptvo.SrvorginfoVo;
import com.chis.modules.heth.comm.service.TdZwSrvorgCardServiceImpl;
import com.chis.modules.heth.comm.utils.ZwSrvorgCardUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 *  <p>方法描述：放射卫生技术服务信息报送卡</p>
 * @MethodAuthor hsj 2023-11-25 19:26
 */
public class AbstractSrvorgCardBean extends FacesEditBean implements IProcessData {
    protected CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    protected TdZwSrvorgCardServiceImpl srvorgCardService = SpringContextHolder.getBean(TdZwSrvorgCardServiceImpl.class);
    //服务单位地区
    protected List<TsZone> zoneList;
    protected List<TsZone> jsZoneList;
    protected String searchZoneName;
    protected String searchZoneGb;
    //服务单位名称
    protected String searchUnitName;
    //出具技术服务报告时间
    protected Date searchRptSDate;
    protected Date searchRptEDate;
    //状态
    protected String[] states;
    //修改时rid
    protected Integer rid;
    //报告卡
    protected  TdZwSrvorgCard srvorgCard;
    //放射资质信息
    protected SrvorginfoVo srvorginfo;
    //码表
    protected Map<String,List<TsSimpleCode>> tsSimpleCodeMap ;
    //技术服务
    protected Map<Integer,TsSimpleCode> jsfwSimpleCodeMap ;
    //地区key:gb value:
    protected Map<String,TsZone> zoneMap ;


    //现场调查时间是否必填
    protected boolean ifDate =false;
    //放射卫生防护检测
    protected Boolean ifFswsFhJc;
    //是否开展放射诊疗工作场所放射防护检测
    protected Boolean ifFhJc;
    //开展放射诊疗设备质量控制检测
    protected Boolean ifInstZkJc;
    //放射诊疗建设项目评价
    protected Boolean ifItemPj;
    //预评价
    protected Boolean ifItemYpj;
    //控制效果评价
    protected Boolean ifItemXgpj;
    //个人剂量监测
    protected Boolean ifDoseMonit;
    //放射防护器材和含放射性产品检测
    protected Boolean ifFhInstJc;
    //开展放射防护器材检测
    protected Boolean ifJcInst;
    //开展含放射性产品检测
    protected Boolean ifJcUse;
    /** 是否支持撤销 当上传日志表 状态为3（已提交） 不可撤销 */
    protected Boolean ifSupportCancel;
    /**技术服务*/
    protected List<TsSimpleCode> badServiceList ;

    /**文件类型1：报送卡，2，签发页*/
    protected Integer fileType;
    // 技术服务领域 编辑页显示用
    protected List<SimpleCodeTreePO> showServiceList;
    // 技术服务领域Map key 大类codeNo value 子集
    protected Map<String, List<SimpleCodeTreePO>> serviceTreeMap;
    private String brTagStr = "<br/>";

    public AbstractSrvorgCardBean(){
        this.init();
        this.initSimpleCode();

    }

    private void init() {
        this.ifSQL=true;
        this.ifSupportCancel = Boolean.FALSE;
        //出具技术服务报告时间：
        //开始日期默认当年1月1日
        this.searchRptSDate = DateUtils.getYearFirstDay(new Date());
        //结束日期默认当前日期
        this.searchRptEDate = new Date();
        //状态默认为待提交
        this.states = new String[]{"0"};
    }

    /**
     *  <p>方法描述：码表初始化</p>
     * @MethodAuthor hsj 2022-08-20 14:21
     */
    private void initSimpleCode() {
        this.tsSimpleCodeMap = new HashMap<>();
        this.badServiceList = new ArrayList<>();
        this.jsfwSimpleCodeMap = new HashMap<>();
        this.showServiceList = new ArrayList<>();
        this.serviceTreeMap = new HashMap<>();
        //技术服务领域
        List<TsSimpleCode> simpleCodes = commService.findallSimpleCodesByTypeIdAndExtends2("5580",null);
        if(!CollectionUtils.isEmpty(simpleCodes)){
            Map<String, TsSimpleCode> fatherMap = new HashMap<>();
            this.badServiceList.addAll(simpleCodes);
            for(TsSimpleCode simpleCode : simpleCodes){
                this.jsfwSimpleCodeMap.put(simpleCode.getRid(),simpleCode);
                String codeNo = simpleCode.getCodeNo();
                String codeLevelNo = simpleCode.getCodeLevelNo();
                if (StringUtils.isBlank(codeNo) || StringUtils.isBlank(codeLevelNo)) {
                    continue;
                }
                if (codeNo.equals(codeLevelNo)) {
                    fatherMap.put(codeNo, simpleCode);
                }
                SimpleCodeTreePO treePO = new SimpleCodeTreePO();
                treePO.setSimpleCode(simpleCode);
                treePO.setIfHasChild(Boolean.FALSE);
                treePO.setIfSelected(Boolean.FALSE);
                treePO.setIfDisabled(Boolean.FALSE);
                this.showServiceList.add(treePO);
                if (codeLevelNo.indexOf(".") == -1) {
                    continue;
                }
                String fatherCode = codeLevelNo.split("\\.")[0];
                List<SimpleCodeTreePO> childList = this.serviceTreeMap.get(fatherCode);
                if (null == childList) {
                    childList = new ArrayList<>();
                    this.serviceTreeMap.put(fatherCode, childList);
                }
                treePO.setIfDisabled(Boolean.TRUE);
                childList.add(treePO);
            }
        }
        if (!CollectionUtils.isEmpty(this.showServiceList) && !CollectionUtils.isEmpty(this.serviceTreeMap)) {
            for (SimpleCodeTreePO treePO : this.showServiceList) {
                TsSimpleCode simpleCode = treePO.getSimpleCode();
                String codeNo = simpleCode.getCodeNo();
                List<SimpleCodeTreePO> childList = this.serviceTreeMap.get(codeNo);
                if (CollectionUtils.isEmpty(childList)) {
                    continue;
                }
                treePO.setIfHasChild(Boolean.TRUE);
                for (SimpleCodeTreePO child : childList) {
                    child.setFatherSimple(simpleCode);
                }
                Integer ext2 = simpleCode.getExtendS2();
                if (null != ext2 && 2 == ext2) {
                    SimpleCodeTreePO child = childList.get(0);
                    child.setPreTip("（放射诊疗类型：");
                    child = childList.get(childList.size()-1);
                    child.setAfterTip("）");
                } else {
                    SimpleCodeTreePO child = childList.get(0);
                    child.setPreTip("（");
                    child = childList.get(childList.size()-1);
                    child.setAfterTip("）");
                }
            }
        }
        //承担的服务事项
        addSimpleCode("5577");
        //服务单位类型
        addSimpleCode("5578");
        //超标点位放射性危害类型
        addSimpleCode("5579");
        // 检测类型
        addSimpleCode("5646");
    }

    private void addSimpleCode(String type) {
        List<TsSimpleCode> simpleCodes = commService.findLevelSimpleCodesByTypeId(type);
        if(CollectionUtils.isEmpty(simpleCodes)){
           return;
        }
        if ("5577".equals(type)) {
            List<TsSimpleCode> simpleCodeList =new ArrayList<>();
            for(TsSimpleCode tsSimpleCode :simpleCodes){
                if(!"1".equals(tsSimpleCode.getExtendS1())){
                    simpleCodeList.add(tsSimpleCode);
                }
            }
            if(!tsSimpleCodeMap.containsKey("5577")){
                tsSimpleCodeMap.put("5577",simpleCodeList);
            }
            return;
        }
        if(!tsSimpleCodeMap.containsKey(type)){
            tsSimpleCodeMap.put(type,simpleCodes);
        }
        //放射卫生防护检测 - 特殊处理
        if("5579".equals(type)){
            specialSimpleCode(simpleCodes);
            return;
        }
        //技术服务领域
        if("5580".equals(type)){

        }
    }
    /**
     *  <p>方法描述：放射卫生防护检测 - 特殊处理
     *   //排除“放射性气溶胶”选项，扩展字段1为1</p>
     * @MethodAuthor hsj 2022-08-27 16:22
     */
    private void specialSimpleCode(List<TsSimpleCode> simpleCodes) {
        if(!CollectionUtils.isEmpty(simpleCodes)){
            List<TsSimpleCode> simpleCodeList =new ArrayList<>();
            for(TsSimpleCode tsSimpleCode :simpleCodes){
                if(!"1".equals(tsSimpleCode.getExtendS1())){
                    simpleCodeList.add(tsSimpleCode);
                }
            }
            if(!tsSimpleCodeMap.containsKey("5579-1")){
                tsSimpleCodeMap.put("5579-1",simpleCodeList);
            }
        }
    }


    /**
     *  <p>方法描述：地区清空</p>
     * @MethodAuthor hsj 2022-08-19 14:07
     */
    public void clearSearchZone(){
        this.searchZoneGb = null;
        this.searchZoneName = null;
    }
    /**
     *  <p>方法描述：添加
     *  验证【放射卫生技术服务机构资质申报】已提交
     *  </p>
     * @MethodAuthor hsj 2022-08-19 15:35
     */
    @Override
    public void addInit() {
    }
    /**
     *  <p>方法描述：技术服务结果</p>
     * @MethodAuthor hsj 2022-08-24 10:44
     */
    public void initResult() {
        if(null == srvorgCard.getIfFswsFhJc() || srvorgCard.getIfFswsFhJc() == 0){
            ifFswsFhJc = false;
            srvorgCard.setIfFswsFhJc(0);
        }else {
            ifFswsFhJc = true;
        }
        if(null == srvorgCard.getIfFhJc() || srvorgCard.getIfFhJc() == 0){
            srvorgCard.setIfFhJc(0);
            ifFhJc = false;
        }else {
            ifFhJc = true;
        }
        if(null == srvorgCard.getIfInstZkJc() || srvorgCard.getIfInstZkJc() == 0){
            srvorgCard.setIfInstZkJc(0);
            ifInstZkJc = false;
        }else {
            ifInstZkJc = true;
        }
        if(null == srvorgCard.getIfItemPj() || srvorgCard.getIfItemPj() == 0){
            srvorgCard.setIfItemPj(0);
            ifItemPj = false;
        }else {
            ifItemPj = true;
        }
        if(null == srvorgCard.getIfItemYpj() || srvorgCard.getIfItemYpj() == 0){
            srvorgCard.setIfItemYpj(0);
            ifItemYpj = false;
        }else {
            ifItemYpj = true;
        }
        if(null == srvorgCard.getIfItemXgpj() || srvorgCard.getIfItemXgpj() == 0){
            srvorgCard.setIfItemXgpj(0);
            ifItemXgpj = false;
        }else {
            ifItemXgpj = true;
        }
        if(null == srvorgCard.getIfDoseMonit() || srvorgCard.getIfDoseMonit() == 0){
            srvorgCard.setIfDoseMonit(0);
            ifDoseMonit = false;
        }else {
            ifDoseMonit = true;
        }

        if(null == srvorgCard.getIfFhInstJc() || srvorgCard.getIfFhInstJc() == 0){
            srvorgCard.setIfFhInstJc(0);
            ifFhInstJc = false;
        }else {
            ifFhInstJc = true;
        }
        if(null == srvorgCard.getIfJcInst() || srvorgCard.getIfJcInst() == 0){
            srvorgCard.setIfJcInst(0);
            ifJcInst = false;
        }else {
            ifJcInst = true;
        }
        if(null == srvorgCard.getIfJcUse() || srvorgCard.getIfJcUse() == 0){
            srvorgCard.setIfJcUse(0);
            ifJcUse = false;
        }else {
            ifJcUse = true;
        }

    }

    /**
     *  <p>方法描述：</p>
     * @MethodAuthor hsj 2022-08-22 9:57
     */
    public List<TsSimpleCode> getTsSimpleCode(String type){
            return tsSimpleCodeMap.get(type);
    }
    @Override
    public void viewInit() {
        this.dealSrvorgCard();
    }
    /**
     *  <p>方法描述：修改初始化</p>
     */
    @Override
    public void modInit() {
        if (null==rid) {
            return;
        }
        this.dealSrvorgCard();
        if(0 == srvorgCard.getState() && StringUtils.isNotBlank(srvorgCard.getAnnexPath())){
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("disabledInput('true','srvorgCardDiv')");
        }
    }

    private void dealSrvorgCard() {
        srvorginfo = srvorgCardService.getSrvorginfo(false);
        srvorgCard = srvorgCardService.searchCardService(rid);
        ZwSrvorgCardUtils.initEntity(srvorgCard);
        this.fillDataShowServiceList();
        //现场调查时间 是否必填
        this.changeServicesAction();
        //服务技术结果
        this.initResult();
    }

    /**
     *  <p>方法描述：技术服务领域选择</p>
     * @MethodAuthor hsj 2022-08-23 14:33
     */
    public void  changeServicesAction(){
        this.ifDate = false;
        if (CollectionUtils.isEmpty(this.showServiceList)) {
            return;
        }
        //存在勾选的技术服务领域 当存在非1时必填；有且只有1时非必填
        for (SimpleCodeTreePO treePO : this.showServiceList) {
            TsSimpleCode simpleCode = treePO.getSimpleCode();
            //当“技术服务领域”5580中只有“个人剂量检测”（扩展字段1为1）时非必填，否则必填
            if (treePO.getIfSelected() && !treePO.getIfDisabled() && !"1".equals(simpleCode.getExtendS1())) {
                this.ifDate = true;
            }
        }
    }
  /**
   *  <p>方法描述：放射卫生技术服务报告首页、签发页</p>
   * @MethodAuthor hsj 2024-08-27 16:06
   */
    public void signAddressUpload(){
        RequestContext.getCurrentInstance().execute("PF('FileDialog').show()");
        RequestContext.getCurrentInstance().update("fileDialog");
    }
    /**
     *  <p>方法描述：暂存</p>
     * @MethodAuthor hsj 2022-08-19 13:37
     */
    @Override
    public void saveAction() {
    }

    @Override
    public String[] buildHqls() {
        return new String[0];
    }

    @Override
    public void processData(List<?> list) {

    }

    /**
     * <p>方法描述：填充技术服务领域 </p>
     * pw 2025/5/9
     **/
    public void fillDataShowServiceList() {
        if (CollectionUtils.isEmpty(this.showServiceList)) {
            return;
        }
        List<String> serviceList = new ArrayList<>();
        String[] servicesArr = srvorgCard.getServices();
        if (null != servicesArr && servicesArr.length > 0) {
            for (String service : servicesArr) {
                serviceList.add(service);
            }
        }
        for (SimpleCodeTreePO treePO : this.showServiceList) {
            String simpleRid = treePO.getSimpleCode().getRid().toString();
            treePO.setIfSelected(serviceList.contains(simpleRid));
            if (!treePO.getIfHasChild()) {
                continue;
            }
            List<SimpleCodeTreePO> childList = this.serviceTreeMap.get(treePO.getSimpleCode().getCodeNo());
            if (CollectionUtils.isEmpty(childList)) {
                continue;
            }
            for (SimpleCodeTreePO child : childList) {
                child.setIfDisabled(!treePO.getIfSelected());
            }
        }
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public List<TsZone> getJsZoneList() {
        return jsZoneList;
    }

    public void setJsZoneList(List<TsZone> jsZoneList) {
        this.jsZoneList = jsZoneList;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneGb() {
        return searchZoneGb;
    }

    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public Date getSearchRptSDate() {
        return searchRptSDate;
    }

    public void setSearchRptSDate(Date searchRptSDate) {
        this.searchRptSDate = searchRptSDate;
    }

    public Date getSearchRptEDate() {
        return searchRptEDate;
    }

    public void setSearchRptEDate(Date searchRptEDate) {
        this.searchRptEDate = searchRptEDate;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwSrvorgCard getSrvorgCard() {
        return srvorgCard;
    }

    public void setSrvorgCard(TdZwSrvorgCard srvorgCard) {
        this.srvorgCard = srvorgCard;
    }

    public SrvorginfoVo getSrvorginfo() {
        return srvorginfo;
    }

    public void setSrvorginfo(SrvorginfoVo srvorginfo) {
        this.srvorginfo = srvorginfo;
    }

    public Map<String, List<TsSimpleCode>> getTsSimpleCodeMap() {
        return tsSimpleCodeMap;
    }

    public void setTsSimpleCodeMap(Map<String, List<TsSimpleCode>> tsSimpleCodeMap) {
        this.tsSimpleCodeMap = tsSimpleCodeMap;
    }

    public Map<Integer, TsSimpleCode> getJsfwSimpleCodeMap() {
        return jsfwSimpleCodeMap;
    }

    public void setJsfwSimpleCodeMap(Map<Integer, TsSimpleCode> jsfwSimpleCodeMap) {
        this.jsfwSimpleCodeMap = jsfwSimpleCodeMap;
    }

    public Map<String, TsZone> getZoneMap() {
        return zoneMap;
    }

    public void setZoneMap(Map<String, TsZone> zoneMap) {
        this.zoneMap = zoneMap;
    }

    public boolean isIfDate() {
        return ifDate;
    }

    public void setIfDate(boolean ifDate) {
        this.ifDate = ifDate;
    }

    public Boolean getIfFswsFhJc() {
        return ifFswsFhJc;
    }

    public void setIfFswsFhJc(Boolean ifFswsFhJc) {
        this.ifFswsFhJc = ifFswsFhJc;
    }

    public Boolean getIfFhJc() {
        return ifFhJc;
    }

    public void setIfFhJc(Boolean ifFhJc) {
        this.ifFhJc = ifFhJc;
    }

    public Boolean getIfInstZkJc() {
        return ifInstZkJc;
    }

    public void setIfInstZkJc(Boolean ifInstZkJc) {
        this.ifInstZkJc = ifInstZkJc;
    }

    public Boolean getIfItemPj() {
        return ifItemPj;
    }

    public void setIfItemPj(Boolean ifItemPj) {
        this.ifItemPj = ifItemPj;
    }

    public Boolean getIfItemYpj() {
        return ifItemYpj;
    }

    public void setIfItemYpj(Boolean ifItemYpj) {
        this.ifItemYpj = ifItemYpj;
    }

    public Boolean getIfItemXgpj() {
        return ifItemXgpj;
    }

    public void setIfItemXgpj(Boolean ifItemXgpj) {
        this.ifItemXgpj = ifItemXgpj;
    }

    public Boolean getIfDoseMonit() {
        return ifDoseMonit;
    }

    public void setIfDoseMonit(Boolean ifDoseMonit) {
        this.ifDoseMonit = ifDoseMonit;
    }

    public Boolean getIfFhInstJc() {
        return ifFhInstJc;
    }

    public void setIfFhInstJc(Boolean ifFhInstJc) {
        this.ifFhInstJc = ifFhInstJc;
    }

    public Boolean getIfJcInst() {
        return ifJcInst;
    }

    public void setIfJcInst(Boolean ifJcInst) {
        this.ifJcInst = ifJcInst;
    }

    public Boolean getIfJcUse() {
        return ifJcUse;
    }

    public void setIfJcUse(Boolean ifJcUse) {
        this.ifJcUse = ifJcUse;
    }

    public Boolean getIfSupportCancel() {
        return ifSupportCancel;
    }

    public void setIfSupportCancel(Boolean ifSupportCancel) {
        this.ifSupportCancel = ifSupportCancel;
    }

    public List<TsSimpleCode> getBadServiceList() {
        return badServiceList;
    }

    public void setBadServiceList(List<TsSimpleCode> badServiceList) {
        this.badServiceList = badServiceList;
    }

    public Integer getFileType() {
        return fileType;
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }

    public List<SimpleCodeTreePO> getShowServiceList() {
        return showServiceList;
    }

    public void setShowServiceList(List<SimpleCodeTreePO> showServiceList) {
        this.showServiceList = showServiceList;
    }

    public Map<String, List<SimpleCodeTreePO>> getServiceTreeMap() {
        return serviceTreeMap;
    }

    public void setServiceTreeMap(Map<String, List<SimpleCodeTreePO>> serviceTreeMap) {
        this.serviceTreeMap = serviceTreeMap;
    }

    public String getBrTagStr() {
        return brTagStr;
    }

    public void setBrTagStr(String brTagStr) {
        this.brTagStr = brTagStr;
    }
}
