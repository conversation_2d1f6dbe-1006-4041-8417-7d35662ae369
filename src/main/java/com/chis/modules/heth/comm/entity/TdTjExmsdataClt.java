package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * @Description : 问诊项目（数据录入）
 * @ClassAuthor: anjing
 * @Date : 2019/5/14 10:19
 **/
@Entity
@Table(name = "TD_TJ_EXMSDATA_CLT")
@SequenceGenerator(name = "TdTjExmsdataClt_Seq", sequenceName = "TD_TJ_EXMSDATA_CLT_SEQ", allocationSize = 1)
public class TdTjExmsdataClt implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /**主键Id*/
    private Integer rid;
    /**关联：体检主表-数据录入（体检主表Id）*/
    private TdTjBhkClt fkByBhkId;
    /**月经史初潮年龄*/
    private Integer mnrage;
    /**月经史经期*/
    private String mns;
    /**月经史周期*/
    private String cyc;
    /**月经史停经年龄*/
    private Integer mnlage;
    /**月经史经期是否*/
    private Integer isxmns;
    /**生育史子女人数*/
    private Integer chldqty;
    /**生育史流产次数*/
    private Integer abrqty;
    /**生育史早产次数*/
    private Integer slnkqty;
    /**生育史死产次*/
    private Integer stlqty;
    /**生育史畸胎次*/
    private Integer trsqty;
    /**生育史子女健康状况*/
    private String chldhthcnd;
    /**婚姻史结婚日期*/
    private String mrydat;
    /**配偶接触放射线情况*/
    private String cplrdtcnd;
    /**配偶职业及健康状况*/
    private String cplprfhthcnd;
    /**烟酒史吸烟情况*/
    private Integer smksta;
    /**平均每天吸烟量*/
    private String smkdayble;
    /**吸烟史-年*/
    private String smkyerqty;
    /**吸烟史-月*/
    private Integer smkmthqty;
    /**烟酒史饮酒情况*/
    private Integer winsta;
    /**烟酒史酒量*/
    private String windaymlx;
    /**烟酒史酒龄*/
    private String winyerqty;
    /**家族史*/
    private String jzs;
    /**个人史*/
    private String grs;
    /**其他*/
    private String oth;
    /**创建日期*/
    private Date createDate;
    /**创建人*/
    private Integer createManid;
    /**修改日期*/
    private Date modifyDate;
    /**修改人*/
    private Integer modifyManid;

    /**目前吸烟情况*/
    private TsSimpleCode fkBySmkstaId;

    public TdTjExmsdataClt() {
    }

    public TdTjExmsdataClt(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjExmsdataClt_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "BHK_ID" )
    public TdTjBhkClt getFkByBhkId() {
        return fkByBhkId;
    }

    public void setFkByBhkId(TdTjBhkClt fkByBhkId) {
        this.fkByBhkId = fkByBhkId;
    }

    @Column(name = "MNRAGE")
    public Integer getMnrage() {
        return mnrage;
    }

    public void setMnrage(Integer mnrage) {
        this.mnrage = mnrage;
    }

    @Column(name = "MNS", length = 10)
    public String getMns() {
        return mns;
    }

    public void setMns(String mns) {
        this.mns = mns;
    }

    @Column(name = "CYC", length = 10)
    public String getCyc() {
        return cyc;
    }

    public void setCyc(String cyc) {
        this.cyc = cyc;
    }

    @Column(name = "MNLAGE")
    public Integer getMnlage() {
        return mnlage;
    }

    public void setMnlage(Integer mnlage) {
        this.mnlage = mnlage;
    }

    @Column(name = "ISXMNS")
    public Integer getIsxmns() {
        return isxmns;
    }

    public void setIsxmns(Integer isxmns) {
        this.isxmns = isxmns;
    }

    @Column(name = "CHLDQTY")
    public Integer getChldqty() {
        return chldqty;
    }

    public void setChldqty(Integer chldqty) {
        this.chldqty = chldqty;
    }

    @Column(name = "ABRQTY")
    public Integer getAbrqty() {
        return abrqty;
    }

    public void setAbrqty(Integer abrqty) {
        this.abrqty = abrqty;
    }

    @Column(name = "SLNKQTY")
    public Integer getSlnkqty() {
        return slnkqty;
    }

    public void setSlnkqty(Integer slnkqty) {
        this.slnkqty = slnkqty;
    }

    @Column(name = "STLQTY")
    public Integer getStlqty() {
        return stlqty;
    }

    public void setStlqty(Integer stlqty) {
        this.stlqty = stlqty;
    }

    @Column(name = "TRSQTY")
    public Integer getTrsqty() {
        return trsqty;
    }

    public void setTrsqty(Integer trsqty) {
        this.trsqty = trsqty;
    }

    @Column(name = "CHLDHTHCND", length = 250)
    public String getChldhthcnd() {
        return chldhthcnd;
    }

    public void setChldhthcnd(String chldhthcnd) {
        this.chldhthcnd = chldhthcnd;
    }

    @Column(name = "MRYDAT", length = 50)
    public String getMrydat() {
        return mrydat;
    }

    public void setMrydat(String mrydat) {
        this.mrydat = mrydat;
    }

    @Column(name = "CPLRDTCND", length = 100)
    public String getCplrdtcnd() {
        return cplrdtcnd;
    }

    public void setCplrdtcnd(String cplrdtcnd) {
        this.cplrdtcnd = cplrdtcnd;
    }

    @Column(name = "CPLPRFHTHCND", length = 100)
    public String getCplprfhthcnd() {
        return cplprfhthcnd;
    }

    public void setCplprfhthcnd(String cplprfhthcnd) {
        this.cplprfhthcnd = cplprfhthcnd;
    }

    @Column(name = "SMKSTA")
    public Integer getSmksta() {
        return smksta;
    }

    public void setSmksta(Integer smksta) {
        this.smksta = smksta;
    }

    @Column(name = "SMKDAYBLE", length = 10)
    public String getSmkdayble() {
        return smkdayble;
    }

    public void setSmkdayble(String smkdayble) {
        this.smkdayble = smkdayble;
    }

    @Column(name = "SMKYERQTY", length = 10)
    public String getSmkyerqty() {
        return smkyerqty;
    }

    public void setSmkyerqty(String smkyerqty) {
        this.smkyerqty = smkyerqty;
    }

    @Column(name = "WINSTA")
    public Integer getWinsta() {
        return winsta;
    }

    public void setWinsta(Integer winsta) {
        this.winsta = winsta;
    }

    @Column(name = "WINDAYMLX", length = 10)
    public String getWindaymlx() {
        return windaymlx;
    }

    public void setWindaymlx(String windaymlx) {
        this.windaymlx = windaymlx;
    }

    @Column(name = "WINYERQTY", length = 10)
    public String getWinyerqty() {
        return winyerqty;
    }

    public void setWinyerqty(String winyerqty) {
        this.winyerqty = winyerqty;
    }

    @Column(name = "JZS")
    public String getJzs() {
        return jzs;
    }

    public void setJzs(String jzs) {
        this.jzs = jzs;
    }

    @Column(name = "GRS")
    public String getGrs() {
        return grs;
    }

    public void setGrs(String grs) {
        this.grs = grs;
    }

    @Column(name = "OTH")
    public String getOth() {
        return oth;
    }

    public void setOth(String oth) {
        this.oth = oth;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Column(name = "SMKMTHQTY" )
    public Integer getSmkmthqty() {
        return smkmthqty;
    }

    public void setSmkmthqty(Integer smkmthqty) {
        this.smkmthqty = smkmthqty;
    }

    @ManyToOne
    @JoinColumn(name = "SMKSTA_ID" )
    public TsSimpleCode getFkBySmkstaId() {
        return fkBySmkstaId;
    }

    public void setFkBySmkstaId(TsSimpleCode fkBySmkstaId) {
        this.fkBySmkstaId = fkBySmkstaId;
    }
}
