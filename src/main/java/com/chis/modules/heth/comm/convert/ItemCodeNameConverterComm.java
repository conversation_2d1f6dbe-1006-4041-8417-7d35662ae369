package com.chis.modules.heth.comm.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.chis.common.utils.StringUtils;

/**
 * 根据码表的层级编码与名称进行缩进转换
 * 
 * <AUTHOR>
 * @createDate 2014年9月17日 上午11:00:59
 * @LastModify LuXuekun
 * @ModifyDate 2014年9月17日 上午11:00:59
 */
@FacesConverter("heth.ItemCodeNameConverterComm")
public class ItemCodeNameConverterComm implements Converter {
	
	@Override
	public Object getAsObject(FacesContext context, UIComponent component,
			String str) {
		return null;
	}

	@Override
	public String getAsString(FacesContext context, UIComponent component,
			Object obj) {
		if (null != obj) {
			// 转入的数据格式为：[名称],[层级编码]
			String objStr = String.valueOf(obj);
			String[] split = objStr.split("@@@");
			// 必须有名称与层级编码组成
			if (split.length > 1) {
				String levelCode = split[1];
				// 判断层级编码的长度
				if (StringUtils.isNotBlank(levelCode)) {
					StringBuilder spces = new StringBuilder();
					// 加中文全角空格
					for (int i = 0; i < (levelCode.split("\\.").length) - 1; i++) {
						spces.append("　");
					}
					// 中文全角加名称
					return spces.append(split[0]).toString();
				}
			} else {
				return split[0];
			}
		}
		return "";
	}

}
