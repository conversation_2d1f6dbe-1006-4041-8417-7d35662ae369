package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-11-16
 */
@Entity
@Table(name = "TD_ZWYJ_OTR_BHK")
@SequenceGenerator(name = "TdZwyjOtrBhk", sequenceName = "TD_ZWYJ_OTR_BHK_SEQ", allocationSize = 1)
public class TdZwyjOtrBhk implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String bhkCode;
	private TbTjSrvorg fkByBhkorgId;
	private TsZone fkByCrptZoneId;
	private TbTjCrpt fkByCrptId;
	private String personName;
	private TsSimpleCode fkByCardTypeId;
	private String idc;
	private Date bhkDate;
	private String badrsn;
	private Integer otrType;
	private Date happenDate;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjOtrBhk() {
	}

	public TdZwyjOtrBhk(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjOtrBhk")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "BHK_CODE")	
	public String getBhkCode() {
		return bhkCode;
	}

	public void setBhkCode(String bhkCode) {
		this.bhkCode = bhkCode;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BHKORG_ID")			
	public TbTjSrvorg getFkByBhkorgId() {
		return fkByBhkorgId;
	}

	public void setFkByBhkorgId(TbTjSrvorg fkByBhkorgId) {
		this.fkByBhkorgId = fkByBhkorgId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_ZONE_ID")			
	public TsZone getFkByCrptZoneId() {
		return fkByCrptZoneId;
	}

	public void setFkByCrptZoneId(TsZone fkByCrptZoneId) {
		this.fkByCrptZoneId = fkByCrptZoneId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_ID")			
	public TbTjCrpt getFkByCrptId() {
		return fkByCrptId;
	}

	public void setFkByCrptId(TbTjCrpt fkByCrptId) {
		this.fkByCrptId = fkByCrptId;
	}	
			
	@Column(name = "PERSON_NAME")	
	public String getPersonName() {
		return personName;
	}

	public void setPersonName(String personName) {
		this.personName = personName;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CARD_TYPE_ID")			
	public TsSimpleCode getFkByCardTypeId() {
		return fkByCardTypeId;
	}

	public void setFkByCardTypeId(TsSimpleCode fkByCardTypeId) {
		this.fkByCardTypeId = fkByCardTypeId;
	}	
			
	@Column(name = "IDC")	
	public String getIdc() {
		return idc;
	}

	public void setIdc(String idc) {
		this.idc = idc;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "BHK_DATE")			
	public Date getBhkDate() {
		return bhkDate;
	}

	public void setBhkDate(Date bhkDate) {
		this.bhkDate = bhkDate;
	}	
			
	@Column(name = "BADRSN")	
	public String getBadrsn() {
		return badrsn;
	}

	public void setBadrsn(String badrsn) {
		this.badrsn = badrsn;
	}	
			
	@Column(name = "OTR_TYPE")	
	public Integer getOtrType() {
		return otrType;
	}

	public void setOtrType(Integer otrType) {
		this.otrType = otrType;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "HAPPEN_DATE")			
	public Date getHappenDate() {
		return happenDate;
	}

	public void setHappenDate(Date happenDate) {
		this.happenDate = happenDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}