package com.chis.modules.heth.comm.rptvo;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.rptvo.ZwxFieldDoc;

/**
 *  <p>类描述：岗位</p>
 * @ClassAuthor hsj 2021/10/22 18:52
 */
public class PostVo {
    @ZwxFieldDoc(value = "岗位")
    private TsSimpleCode post;

    @ZwxFieldDoc(value = "应拣人数")
    private Integer needChkNum;

    public TsSimpleCode getPost() {
        return post;
    }

    public void setPost(TsSimpleCode post) {
        this.post = post;
    }

    public Integer getNeedChkNum() {
        return needChkNum;
    }

    public void setNeedChkNum(Integer needChkNum) {
        this.needChkNum = needChkNum;
    }
}
