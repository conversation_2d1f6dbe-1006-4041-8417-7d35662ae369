package com.chis.modules.heth.comm.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-2-18
 */
@Entity
@Table(name = "TD_ZW_OCCDIS_CARD_BADRSN")
@SequenceGenerator(name = "TdZwOccdisCardBadrsn", sequenceName = "TD_ZW_OCCDIS_CARD_BADRSN_SEQ", allocationSize = 1)
public class TdZwOccdisCardBadrsn implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwOccdisCardNew fkByMainId;
	private TsSimpleCode fkByBadrsnId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	
	public TdZwOccdisCardBadrsn() {
	}

	public TdZwOccdisCardBadrsn(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOccdisCardBadrsn")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwOccdisCardNew getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwOccdisCardNew fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BADRSN_ID")			
	public TsSimpleCode getFkByBadrsnId() {
		return fkByBadrsnId;
	}

	public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
		this.fkByBadrsnId = fkByBadrsnId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
}