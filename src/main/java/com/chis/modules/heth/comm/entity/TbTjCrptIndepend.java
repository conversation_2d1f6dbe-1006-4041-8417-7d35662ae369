package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsUnit;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-4-13
 */
@Entity
@Table(name = "TB_TJ_CRPT_INDEPEND")
@SequenceGenerator(name = "TbTjCrptIndepend", sequenceName = "TB_TJ_CRPT_INDEPEND_SEQ", allocationSize = 1)
public class TbTjCrptIndepend implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbTjCrpt fkByCrptId;
	private TsUnit fkByUnitId;
	/**
	 * 业务类型
	 * <p>1：职业健康检查
	 * <p>2：职业病诊断
	 * <p>3：职业病鉴定
	 * <p>4：职业病危害因素检测
	 * <p>5：技术服务申报
	 * <p>6：在线申报
	 * <p>7：工作场所监测
	 * <p>8：现状调查
	 */
	private Integer busType;
	private String linkman2;
	private String linkphone2;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TbTjCrptIndepend() {
	}

	public TbTjCrptIndepend(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjCrptIndepend")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "CRPT_ID")			
	public TbTjCrpt getFkByCrptId() {
		return fkByCrptId;
	}

	public void setFkByCrptId(TbTjCrpt fkByCrptId) {
		this.fkByCrptId = fkByCrptId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "UNIT_ID")			
	public TsUnit getFkByUnitId() {
		return fkByUnitId;
	}

	public void setFkByUnitId(TsUnit fkByUnitId) {
		this.fkByUnitId = fkByUnitId;
	}

	@Column(name = "BUS_TYPE")
	public Integer getBusType() {
		return busType;
	}

	public void setBusType(Integer busType) {
		this.busType = busType;
	}

	@Column(name = "LINKMAN2")
	public String getLinkman2() {
		return linkman2;
	}

	public void setLinkman2(String linkman2) {
		this.linkman2 = linkman2;
	}	
			
	@Column(name = "LINKPHONE2")	
	public String getLinkphone2() {
		return linkphone2;
	}

	public void setLinkphone2(String linkphone2) {
		this.linkphone2 = linkphone2;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}