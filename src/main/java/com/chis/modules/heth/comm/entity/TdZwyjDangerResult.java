package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-11-17
 */
@Entity
@Table(name = "TD_ZWYJ_DANGER_RESULT")
@SequenceGenerator(name = "TdZwyjDangerResult", sequenceName = "TD_ZWYJ_DANGER_RESULT_SEQ", allocationSize = 1)
public class TdZwyjDangerResult implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwyjDangerBhk fkByMainId;
	private TdZwyjDangerItems fkByDangerId;
	private String itemRst;
	private String msrunt;
	private String itemStdvalue;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjDangerResult() {
	}

	public TdZwyjDangerResult(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjDangerResult")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwyjDangerBhk getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwyjDangerBhk fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "DANGER_ID")			
	public TdZwyjDangerItems getFkByDangerId() {
		return fkByDangerId;
	}

	public void setFkByDangerId(TdZwyjDangerItems fkByDangerId) {
		this.fkByDangerId = fkByDangerId;
	}	
			
	@Column(name = "ITEM_RST")	
	public String getItemRst() {
		return itemRst;
	}

	public void setItemRst(String itemRst) {
		this.itemRst = itemRst;
	}	
			
	@Column(name = "MSRUNT")	
	public String getMsrunt() {
		return msrunt;
	}

	public void setMsrunt(String msrunt) {
		this.msrunt = msrunt;
	}	
			
	@Column(name = "ITEM_STDVALUE")	
	public String getItemStdvalue() {
		return itemStdvalue;
	}

	public void setItemStdvalue(String itemStdvalue) {
		this.itemStdvalue = itemStdvalue;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}