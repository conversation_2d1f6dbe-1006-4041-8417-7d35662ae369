package com.chis.modules.heth.comm.service;

import com.chis.common.utils.ObjectUtil;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 *  <p>类描述：GBZ188维护service</p>
 * @ClassAuthor hsj 2023-07-18 9:05
 */
@Service
public class Gbz188PageCommServiceImpl extends AbstractTemplate {

    /**
     *  <p>方法描述：查询-职业健康监护方案标准（GBZ-188）
     *  根据危害因素，在岗状态codeno排序
     *  showStop:是否显示停用</p>
     * @MethodAuthor hsj 2023-07-18 9:08
     */
    @Transactional(readOnly = true)
    public List<TbZwtjMainstd> getZwtjMainstd(boolean showStop) {
        StringBuilder sql = new StringBuilder();
        // 查询标准列表，按有因素第一序列在岗状态第二序列排序
        sql.append("from TbZwtjMainstd t where 1=1 ");
        if (!showStop) {
            sql.append(" and t.stopTag=1 ");
        }
        sql.append(" order by t.tsSimpleCodeByBadrsnId.codeNo,t.tsSimpleCodeByWorkStateid.codeNo");
        return this.findHqlResultList(sql.toString());
    }

    /**
     *  <p>方法描述：根据职业健康监护方案标准（GBZ-188）rid查询标准项目组合选项卡
     *  按照 是否必检 排序 </p>
     * @MethodAuthor hsj 2023-07-18 9:31
     */
    @Transactional(readOnly = true)
    public List<TbZwtjSchemeItems> getSchemeitemses(Integer schemeId) {
        if(null == schemeId){
            return null;
        }
        StringBuilder sql = new StringBuilder();
        sql.append("from TbZwtjSchemeItems t where t.tbZwtjMainstd.rid=");
        sql.append(schemeId);
        sql.append(" order by t.isMust desc");
        return this.findHqlResultList(sql.toString());
    }

    /**
     *  <p>方法描述：根据职业健康监护方案标准（GBZ-188）rid查询职业健康监护标准禁忌症（GBZ-188）
     *  按照方案标准码表codeno排序</p>
     * @MethodAuthor hsj 2023-07-18 9:34
     */
    @Transactional(readOnly = true)
    public List<TbZwtjJjzs> getTbZwtjjjzsList(Integer schemeId) {
        if(null == schemeId){
            return null;
        }
        StringBuilder sql = new StringBuilder();
        sql.append("from TbZwtjJjzs t where t.tbZwtjMainstd.rid=");
        sql.append(schemeId);
        sql.append(" order by t.tsSimpleCode.codeNo");
        return this.findHqlResultList(sql.toString());
    }

    /**
     *  <p>方法描述：根据职业健康监护方案标准（GBZ-188）rid查询职业健康监护标准疑似职业病（GBZ-188）</p>
     * @MethodAuthor hsj 2023-07-18 9:34
     */
    @Transactional(readOnly = true)
    public List<TbZwtjOccdises> getTbZwtjoccList(Integer schemeId) {
        if(null == schemeId){
            return null;
        }
        StringBuilder sql = new StringBuilder();
        sql.append("from TbZwtjOccdises t where t.tbZwtjMainstd.rid=");
        sql.append(schemeId);
        sql.append(" order by t.tsSimpleCode.codeLevelNo");
        return this.findHqlResultList(sql.toString());
    }

    /**
     *  <p>方法描述：根据职业健康监护方案标准（GBZ-188）rid查询职业健康监护标准询问症状（GBZ-188）</p>
     * @MethodAuthor hsj 2023-07-18 9:34
     */
    @Transactional(readOnly = true)
    public List<TbZwtjAskitems> getTbAskItemList(Integer schemeId) {
        if(null == schemeId){
            return null;
        }
        StringBuilder sql = new StringBuilder();
        sql.append("from TbZwtjAskitems t where t.tbZwtjMainstd.rid=");
        sql.append(schemeId);
        sql.append(" order by t.tsSimpleCode.codeNo");
        return this.findHqlResultList(sql.toString());
    }

    /**
     *  <p>方法描述：保存标准维护</p>
     * @MethodAuthor hsj 2023-07-18 11:43
     */
    @Transactional(readOnly = false)
    public TbZwtjMainstd saveTbZwtjmainstd(TbZwtjMainstd tbMainstd) {
        if (null == tbMainstd) {
            return null;
        }
        this.upsertEntity(tbMainstd);
        // 保存修改记录日志
        this.saveGbzLog("TB_ZWTJ_MAINSTD", tbMainstd.getRid(), tbMainstd.getStopTag(), tbMainstd.getModifyManid());
        return tbMainstd;
    }

    /**
     *  <p>方法描述：保存GBZ 188修改记录</p>
     * @MethodAuthor hsj 2023-07-18 11:44
     */
    @Transactional(readOnly = false)
    private void saveGbzLog(String tableName, Integer tableRid, short stopTag, Integer userId) {
        // 判断表名与记录表ID不能为空
        if (null == tableName || "".equals(tableName)) {
            throw new RuntimeException("修改记录表名不能为空！");
        }
        if (null == tableRid) {
            throw new RuntimeException("修改记录表ID不能为空！");
        }

        // 查询版本记录
        StringBuilder sql = new StringBuilder();
        sql.append("select t from TbZwGbz188verComm t");
        List<TbZwGbz188verComm> resultList = this.findHqlResultList(sql.toString());
        TbZwGbz188verComm tbZwGbz188ver = new TbZwGbz188verComm();
        // 判断版本记录表是否为空
        if (null != resultList && resultList.size() > 0) {
            tbZwGbz188ver = resultList.get(0);
        } else {
            // 如果版本记录表为空则插入一条新的记录
            tbZwGbz188ver.setStdname(Constants.GBZ188NAME);
            tbZwGbz188ver.setVerNum(0);
            tbZwGbz188ver.setCreateDate(new Date());
            tbZwGbz188ver.setCreateManid(userId);
            this.saveObj(tbZwGbz188ver);
        }

        // 保存GBZ 188 修订记录
        TdZwGbzlogComm tdZwGbzlog = new TdZwGbzlogComm();
        tdZwGbzlog.setCreateDate(new Date());
        tdZwGbzlog.setCreateManid(userId);
        // 删除标记
        tdZwGbzlog.setDelFlag(stopTag == 0 ? (short) 1 : (short) 0);
        // 记录ID
        tdZwGbzlog.setRecId(tableRid);
        // 表名
        tdZwGbzlog.setTabName(tableName);
        // 最新版本号+1
        tdZwGbzlog.setVerNum(tbZwGbz188ver.getVerNum() + 1);

        // 保存修改记录同时新增版本号
        this.upsertEntity(tdZwGbzlog);
        // 版本号+1
        tbZwGbz188ver.setVerNum(tbZwGbz188ver.getVerNum() + 1);
        //无修改信息
        this.updateObj(tbZwGbz188ver);
    }

    /**
     *  <p>方法描述：根据标准主表ID查询还没有维护的项目组合</p>
     * @MethodAuthor hsj 2023-07-18 13:46
     */
    @Transactional(readOnly = true)
    public List<Object[]> getSelectItemList(Integer mainstdId) {
        if(null == mainstdId){
            return null;
        }
        StringBuilder sql = new StringBuilder();
        // 查询出所有未添加项目合列表
        sql.append("SELECT T.RID,T.CODE_NO,T.CODE_NAME,T.CODE_LEVEL_NO,T.SPLSHT FROM TS_SIMPLE_CODE T");
        sql.append("  INNER JOIN TS_CODE_TYPE A ON T.CODE_TYPE_ID=A.RID");
        sql.append("  WHERE A.CODE_TYPE_NAME='5012' AND T.RID NOT IN(");
        sql.append("  SELECT ITEM_CMBID FROM TB_ZWTJ_SCHEME_ITEMS WHERE SCHEME_ID ='");
        sql.append(mainstdId);
        sql.append("' ) AND T.IF_REVEAL='1' ORDER BY T.CODE_LEVEL_NO");
        return this.findSqlResultList(sql.toString());

    }
    
    /**
     *  <p>方法描述：保存标准项目组合列表</p>
     * @MethodAuthor hsj 2023-07-18 14:35
     */
    @Transactional(readOnly = false)
    public void saveItemList(List<TbZwtjSchemeItems> selectedItemList) {
        if (null != selectedItemList && selectedItemList.size() > 0) {
            for (TbZwtjSchemeItems tbZwtjSchemeItems : selectedItemList) {
                // 保存项目组合
                this.upsertEntity(tbZwtjSchemeItems);
                // 记录项目组合业务提醒
                this.saveGbzLog("TB_ZWTJ_SCHEME_ITEMS", tbZwtjSchemeItems.getRid(), tbZwtjSchemeItems.getStopTag(),
                        tbZwtjSchemeItems.getModifyManid());
            }
        }
    }

    /**
     *  <p>方法描述：停用GBZ 188标准，根据操作子表的名称和RID
     *  tableName：表名
     *  tableRid：rid
     *  userId:用户rid</p>
     * @MethodAuthor hsj 2023-07-18 14:40
     */
    @Transactional(readOnly = false)
    public void stopGbz(String tableName, Integer tableRid, Integer userId) {
        if (null != tableName && !"".equals(tableName.trim()) && null != tableRid) {
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE ");
            sql.append(tableName);
            sql.append(" SET STOP_TAG=0 WHERE RID ='");
            sql.append(tableRid);
            sql.append("'");
            this.executeSql(sql.toString(),null);

            // 停用修改记录表
            this.saveGbzLog(tableName, tableRid, (short) 0, userId);
            // 如果是停用项目组合表，则同进停用项目组合业务提醒
            if ("TB_ZWTJ_SCHEME_ITEMS".equals(tableName)) {
                sql = new StringBuilder();
                sql.append("SELECT T.SCHEME_ID,T.ITEM_CMBID FROM TB_ZWTJ_SCHEME_ITEMS T WHERE T.RID='");
                sql.append(tableRid);
                sql.append("'");
                List<Object[]> resultList = this.findSqlResultList(sql.toString());
                // 停用业务提醒表
                if (null != resultList && resultList.size() > 0) {
                    Object[] objects = resultList.get(0);
                    sql = new StringBuilder();
                    sql.append("UPDATE TB_ZWTJ_BSWAKE SET STOP_TAG=0 WHERE ITEM_CMBID=");
                    sql.append(ObjectUtil.toStr(objects[1]));
                    sql.append(" AND SCHEME_ID=");
                    sql.append(ObjectUtil.toStr(objects[0]));
                    this.executeSql(sql.toString(),null);
                    // 停用业务提醒修改记录表
                    sql = new StringBuilder();
                    sql.append("SELECT RID FROM  TB_ZWTJ_BSWAKE WHERE ITEM_CMBID=");
                    sql.append(ObjectUtil.toStr(objects[1]));
                    sql.append(" AND SCHEME_ID=");
                    sql.append(ObjectUtil.toStr(objects[0]));
                    // 查询出业务醒表的RID
                    List resultList2 = this.findSqlResultList(sql.toString());
                    if (null != resultList2 && resultList2.size() > 0) {
                        Object object = resultList2.get(0);
                        this.saveGbzLog("TB_ZWTJ_BSWAKE", Integer.valueOf(String.valueOf(object)), (short) 0, userId);
                    }
                }
            }
        }
    }

    /**
     *  <p>方法描述：删除GBZ 188标准，根据操作子表的名称和RID
     *  tableName：表名
     *  tableRid：rid
     *  userId:用户rid</p>
     * @MethodAuthor hsj 2023-07-18 14:40
     */
    @Transactional(readOnly = false)
    public void delGbz(String tableName, Integer tableRid) {
        if(null == tableName || "".equals(tableName.trim()) || null == tableRid){
            return;
        }
        // 删除项目组合表
        StringBuilder sql = new StringBuilder();
        sql.append("DELETE ");
        sql.append(tableName);
        sql.append(" WHERE RID ='");
        sql.append(tableRid);
        sql.append("'");
        this.executeSql(sql.toString(),null);
        // 如果是删除项目组合表，则同进删除项目组合业务提醒
        if ("TB_ZWTJ_SCHEME_ITEMS".equals(tableName)) {
            sql = new StringBuilder();
            sql.append("SELECT T.SCHEME_ID,T.ITEM_CMBID FROM TB_ZWTJ_SCHEME_ITEMS T WHERE T.RID='");
            sql.append(tableRid);
            sql.append("'");
            List<Object[]> resultList =this.findSqlResultList(sql.toString());
            // 删除业务提醒表
            if (null != resultList && resultList.size() > 0) {
                Object[] objects = resultList.get(0);
                sql = new StringBuilder();
                sql.append("DELETE TB_ZWTJ_BSWAKE WHERE ITEM_CMBID=");
                sql.append(ObjectUtil.toStr(objects[1]));
                sql.append(" AND SCHEME_ID=");
                sql.append(ObjectUtil.toStr(objects[0]));
                this.executeSql(sql.toString(),null);
            }
        }

        // 删除修改记录表
        sql = new StringBuilder();
        sql.append("DELETE TD_ZW_GBZLOG WHERE REC_ID=");
        sql.append(tableRid);
        sql.append(" AND TAB_NAME ='");
        sql.append(tableName);
        sql.append("'");
        this.executeSql(sql.toString(),null);
    }

    /**
     *  <p>方法描述：保存业务提醒</p>
     * @MethodAuthor hsj 2023-07-18 15:11
     */
    @Transactional(readOnly = false)
    public void saveBsWake(String editBsWake, Integer schemeItemID, Integer mainstdId,
                          TbZwtjSchemeItems tbZwtjSchemeItems) {
        if (null == schemeItemID || null == mainstdId) {
            return;
        }
        // 首先查询有没有业务提醒
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT T.RID FROM TB_ZWTJ_BSWAKE T WHERE T.ITEM_CMBID='");
        sql.append(schemeItemID);
        sql.append("' AND T.SCHEME_ID='");
        sql.append(mainstdId);
        sql.append("'");
        List<Object> resultList2 = this.findSqlResultList(sql.toString());
        // 如果有业务提醒，则更新业务提醒表
        if (null != resultList2 && resultList2.size() > 0) {
            // 更新业务提醒表
            sql = new StringBuilder();
            sql.append("UPDATE TB_ZWTJ_BSWAKE SET WAKE_INFO= :wakeInfo ");
            sql.append(" WHERE ITEM_CMBID='");
            sql.append(schemeItemID);
            sql.append("' AND SCHEME_ID='");
            sql.append(mainstdId);
            sql.append("'");
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("wakeInfo", editBsWake == null ? "" : editBsWake.trim());
            this.executeSql(sql.toString(),paramMap);

            // 查询业务提醒表数据
            sql = new StringBuilder();
            sql.append("SELECT RID,STOP_TAG,MODIFY_MANID FROM TB_ZWTJ_BSWAKE T");
            sql.append(" WHERE T.ITEM_CMBID='");
            sql.append(schemeItemID);
            sql.append("' AND T.SCHEME_ID='");
            sql.append(mainstdId);
            sql.append("'");
            List<Object[]> resultList = this.findSqlResultList(sql.toString());
            // 记录修订日志记录表
            if (null != resultList && resultList.size() > 0) {
                Object[] objects = resultList.get(0);

                this.saveGbzLog("TB_ZWTJ_BSWAKE", Integer.valueOf(String.valueOf(objects[0])),
                        Short.valueOf(String.valueOf(objects[1])), Integer.valueOf(String.valueOf(objects[2])));
            }
            // 如果没有业务提醒，就新增一个业务提醒
        } else {
            // 保存业务提醒表
            TbZwtjBswake tbZwtjBswake = new TbZwtjBswake();
            // 创建人,修改人,创建时间，修改时间
            tbZwtjBswake.setCreateDate(new Date());
            tbZwtjBswake.setCreateManid(tbZwtjSchemeItems.getCreateManid());
            tbZwtjBswake.setModifyDate(new Date());
            tbZwtjBswake.setModifyManid(tbZwtjSchemeItems.getCreateManid());
            // 发布状态
            tbZwtjBswake.setPublishTag((short) 0);
            // 停用标记
            tbZwtjBswake.setStopTag((short) 1);
            // 主表
            tbZwtjBswake.setTbZwtjMainstd(tbZwtjSchemeItems.getTbZwtjMainstd());
            // 项目组合ID
            tbZwtjBswake.setTsSimpleCode(tbZwtjSchemeItems.getTsSimpleCode());
            // 业务提醒，默认为空
            tbZwtjBswake.setWakeInfo(editBsWake);
            this.save(tbZwtjBswake);
            // 记录业务提醒修订记录
            this.saveGbzLog("TB_ZWTJ_BSWAKE", tbZwtjBswake.getRid(), tbZwtjBswake.getStopTag(),
                    tbZwtjBswake.getCreateManid());
        }
    }

    /**
     *  <p>方法描述：清除业务提醒表内容</p>
     * @MethodAuthor hsj 2023-07-18 15:11
     */
    @Transactional(readOnly = false)
    public void cleanBsWork(Integer schemeItemID, Integer mainstdId) {
        // 判断项目组合ID 与主表ID不为空
        if (null == schemeItemID || null == mainstdId) {
            return;
        }
        // 修改记录版本
        // 先查询出业务提醒表的rid
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT T.RID FROM TB_ZWTJ_BSWAKE T WHERE T.ITEM_CMBID='");
        sql.append(schemeItemID);
        sql.append("' AND T.SCHEME_ID='");
        sql.append(mainstdId);
        sql.append("'");
        List<Object[]> resultList = this.findSqlResultList(sql.toString());

        // 删除业务提醒
        sql = new StringBuilder();
        sql.append("DELETE TB_ZWTJ_BSWAKE T WHERE T.ITEM_CMBID='");
        sql.append(schemeItemID);
        sql.append("' AND T.SCHEME_ID='");
        sql.append(mainstdId);
        sql.append("'");
        this.executeSql(sql.toString(),null);

        // 再根据RID删除修改记录表
        if (null != resultList && resultList.size() > 0) {
            sql = new StringBuilder();
            sql.append("DELETE TD_ZW_GBZLOG WHERE REC_ID=");
            sql.append(ObjectUtil.toStr(resultList.get(0)));
            sql.append(" AND TAB_NAME ='TB_ZWTJ_BSWAKE'");
            this.executeSql(sql.toString(),null);
        }
    }

    /**
     *  <p>方法描述：查询未添加的职业禁忌证</p>
     * @MethodAuthor hsj 2023-07-18 15:42
     */
    @Transactional(readOnly = true)
    public List<Object[]> findJjzList(Integer mainId) {
        if (null == mainId) {
            return null;
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT A.RID,A.CODE_NO,A.CODE_NAME,A.SPLSHT FROM TS_SIMPLE_CODE A ");
        sql.append(" INNER JOIN TS_CODE_TYPE B ON A.CODE_TYPE_ID=B.RID");
        sql.append(" WHERE B.CODE_TYPE_NAME='5011' AND A.RID NOT IN(");
        sql.append(" SELECT CONTRAIND_ID FROM TB_ZWTJ_JJZS WHERE SCHEME_ID ='");
        sql.append(mainId);
        sql.append("') AND A.IF_REVEAL='1' ORDER BY A.CODE_NO");
        return this.findSqlResultList(sql.toString());
    }

    /**
     *  <p>方法描述：保存选择的职业禁忌证</p>
     * @MethodAuthor hsj 2023-07-18 15:45
     */
    @Transactional(readOnly = false)
    public void saveJjzList(List<TbZwtjJjzs> tbZwtjJjzsList) {
        if (CollectionUtils.isEmpty(tbZwtjJjzsList)) {
            return;
        }
        for (TbZwtjJjzs tbZwtjJjzs : tbZwtjJjzsList) {
            // 保存职业禁忌证
            this.save(tbZwtjJjzs);
            // 记录职业禁忌证修改记录
            this.saveGbzLog("TB_ZWTJ_JJZS", tbZwtjJjzs.getRid(), tbZwtjJjzs.getStopTag(),
                    tbZwtjJjzs.getModifyManid());
        }
    }

    /**
     *  <p>方法描述：查询未添加的疑似职业病 列表</p>
     * @MethodAuthor hsj 2023-07-18 15:50
     */
    @Transactional(readOnly = true)
    public List<Object[]> findOccList(Integer mainId) {
        if (null == mainId) {
            return null;
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT A.RID,A.CODE_NO,A.CODE_NAME,A.CODE_LEVEL_NO,A.SPLSHT FROM TS_SIMPLE_CODE A ");
        sql.append(" INNER JOIN TS_CODE_TYPE B ON A.CODE_TYPE_ID=B.RID");
        sql.append(" WHERE B.CODE_TYPE_NAME='5010' AND A.RID NOT IN(");
        sql.append("SELECT OCC_DISEID FROM TB_ZWTJ_OCCDISES WHERE SCHEME_ID ='");
        sql.append(mainId);
        sql.append("') AND A.IF_REVEAL='1' ORDER BY A.CODE_LEVEL_NO");
        return this.findSqlResultList(sql.toString());
    }

    /**
     *  <p>方法描述：保存选择的疑似职业病 列表</p>
     * @MethodAuthor hsj 2023-07-18 15:52
     */
    @Transactional(readOnly = false)
    public void saveOccList(List<TbZwtjOccdises> tbZwtjOccdisesList) {
        if (CollectionUtils.isEmpty(tbZwtjOccdisesList)) {
            return;
        }
        for (TbZwtjOccdises tbZwtjOccdises : tbZwtjOccdisesList) {
            // 保存疑似职业病
            this.save(tbZwtjOccdises);
            // 记录疑似职业病修改记录
            this.saveGbzLog("TB_ZWTJ_OCCDISES", tbZwtjOccdises.getRid(), tbZwtjOccdises.getStopTag(),
                    tbZwtjOccdises.getModifyManid());
        }
    }

    /**
     *  <p>方法描述：查询未添加的询问症关 列表</p>
     * @MethodAuthor hsj 2023-07-18 15:52
     */
    @Transactional(readOnly = true)
    public List<Object[]> findAskItemList(Integer mainId) {
        if (null == mainId) {
           return null;
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT A.RID,A.CODE_NO,A.CODE_NAME,A.CODE_LEVEL_NO,A.SPLSHT FROM TS_SIMPLE_CODE A ");
        sql.append(" INNER JOIN TS_CODE_TYPE B ON A.CODE_TYPE_ID=B.RID");
        sql.append(" WHERE B.CODE_TYPE_NAME='5006' AND A.RID NOT IN(");
        sql.append("SELECT SYM_ID FROM TB_ZWTJ_ASKITEMS WHERE SCHEME_ID ='");
        sql.append(mainId);
        sql.append("') AND A.IF_REVEAL='1' ORDER BY A.CODE_NO");
        return this.findSqlResultList(sql.toString());
    }

    /**
     *  <p>方法描述：验证标准方案是否存在</p>
     * @MethodAuthor hsj 2023-07-18 15:56
     */
    @Transactional(readOnly = true)
    public boolean verfiyAk(Integer badRsn, Integer jobState, Integer mainstdId) {
        // 判断危害因素和在岗状态是否为空
        if (null == badRsn || null == jobState) {
            return false;
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) FROM TB_ZWTJ_MAINSTD T WHERE T.BADRSN_ID='");
        sql.append(badRsn);
        sql.append("' AND T.WORK_STATEID='");
        sql.append(jobState);
        sql.append("'");
        // 修改时不状态自己
        if (null != mainstdId) {
            sql.append(" AND RID!='");
            sql.append(mainstdId);
            sql.append("'");
        }
        Object singleResult = em.createNativeQuery(sql.toString()).getSingleResult();
        // 不为空并且不为0
        return null != singleResult && !"0".equals(singleResult.toString());
    }

    /**
     *  <p>方法描述：据项目组合ID和主表ID查询业务提醒</p>
     * @MethodAuthor hsj 2023-07-18 15:57
     */
    @Transactional(readOnly = true)
    public String getWake(Integer codeRid, Integer mainstdId) {
        if (null == codeRid || null == mainstdId) {
            return "";
        }
        // 根据项目组合ID与标准方案主表ID查询业务提醒
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT T.WAKE_INFO FROM TB_ZWTJ_BSWAKE T WHERE T.ITEM_CMBID ='");
        sql.append(codeRid);
        sql.append("' AND T.SCHEME_ID='");
        sql.append(mainstdId);
        sql.append("'");
        // 获取业务提醒
        List<Object> resutlList = this.findSqlResultList(sql.toString());
        if (null != resutlList && resutlList.size() > 0) {
            return String.valueOf(resutlList.get(0));
        }else {
            return "";
        }
    }

    /**
     *  <p>方法描述：保存选择的询问症状 列表</p>
     * @MethodAuthor hsj 2023-07-18 15:59
     */
    @Transactional(readOnly = false)
    public void saveAskItemList(List<TbZwtjAskitems> tbZwtjAskitemsList) {
        if (CollectionUtils.isEmpty(tbZwtjAskitemsList)) {
            return;
        }
        for (TbZwtjAskitems tbZwtjAskitems : tbZwtjAskitemsList) {
            // 保存询问症状
            this.save(tbZwtjAskitems);
            // 记录询问症状修改记录
            this.saveGbzLog("TB_ZWTJ_ASKITEMS", tbZwtjAskitems.getRid(), tbZwtjAskitems.getStopTag(),
                    tbZwtjAskitems.getModifyManid());
        }
    }

    /**
     *  <p>方法描述：启用GBZ 188标准，根据操作子表的名称和RID</p>
     * @MethodAuthor hsj 2023-07-18 16:00
     */
    @Transactional(readOnly = false)
    public void powerGbz(String tableName, Integer tableRid, Integer userId) {
        if (null == tableName || "".equals(tableName.trim()) || null == tableRid) {
            return;
        }
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ");
        sql.append(tableName);
        sql.append(" SET STOP_TAG=1 WHERE RID ='");
        sql.append(tableRid);
        sql.append("'");
        this.executeSql(sql.toString(),null);

        // 新增修改记录表
        this.saveGbzLog(tableName, tableRid, (short) 1, userId);
        // 如果是启用用项目组合表，则同时启用项目组合业务提醒
        if ("TB_ZWTJ_SCHEME_ITEMS".equals(tableName)) {
            sql = new StringBuilder();
            sql.append("SELECT T.SCHEME_ID,T.ITEM_CMBID FROM TB_ZWTJ_SCHEME_ITEMS T WHERE T.RID='");
            sql.append(tableRid);
            sql.append("'");
            List<Object[]> resultList = this.findSqlResultList(sql.toString());
            // 启用业务提醒表
            if (null != resultList && resultList.size() > 0) {
                Object[] objects = resultList.get(0);
                sql = new StringBuilder();
                sql.append("UPDATE TB_ZWTJ_BSWAKE SET STOP_TAG=1 WHERE ITEM_CMBID=");
                sql.append(ObjectUtil.toStr(objects[1]));
                sql.append(" AND SCHEME_ID=");
                sql.append(ObjectUtil.toStr(objects[0]));
                this.executeSql(sql.toString(),null);

                // 新增业务提醒修改记录表
                sql = new StringBuilder();
                sql.append("SELECT RID FROM  TB_ZWTJ_BSWAKE WHERE ITEM_CMBID=");
                sql.append(ObjectUtil.toStr(objects[1]));
                sql.append(" AND SCHEME_ID=");
                sql.append(ObjectUtil.toStr(objects[0]));
                // 查询出业务醒表的RID
                List resultList2 = this.findSqlResultList(sql.toString());
                if (null != resultList2 && resultList2.size() > 0) {
                    Object object = resultList2.get(0);
                    this.saveGbzLog("TB_ZWTJ_BSWAKE", Integer.valueOf(String.valueOf(object)), (short) 1, userId);
                }
            }
        }
    }

}
