package com.chis.modules.heth.comm.json;

import java.io.Serializable;
import java.util.List;

/**
 * <p>类描述： 场所监测 导入 返回对象 </p>
 * @ClassAuthor： pw 2023/6/21
 **/
public class CrptUploadRepDTO implements Serializable {
    private static final long serialVersionUID = -5221206287458234775L;
    private String type;
    private String mess;
    private List<CrptUploadRepSingleDTO> ridList;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMess() {
        return mess;
    }

    public void setMess(String mess) {
        this.mess = mess;
    }

    public List<CrptUploadRepSingleDTO> getRidList() {
        return ridList;
    }

    public void setRidList(List<CrptUploadRepSingleDTO> ridList) {
        this.ridList = ridList;
    }
}
