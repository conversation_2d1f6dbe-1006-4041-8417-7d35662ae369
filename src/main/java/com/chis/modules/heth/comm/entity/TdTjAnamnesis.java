package com.chis.modules.heth.comm.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * 问诊既往病史表
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 */
@Entity
@Table(name = "TD_TJ_ANAMNESIS")
@SequenceGenerator(name = "TdTjAnamnesis_Seq", sequenceName = "TD_TJ_ANAMNESIS_SEQ", allocationSize = 1)
public class TdTjAnamnesis implements java.io.Serializable {

    private static final long serialVersionUID = -4911841803216586520L;
    private Integer rid;
    private TdTjBhk tdTjBhk;
    private String hstnam;
    private String hstdat;
    private String hstunt;
    private String hstcruprc;
    private String hstlps;
    private Date createDate;
    private Integer createManid;

    public TdTjAnamnesis() {
    }

    public TdTjAnamnesis(Integer rid, TdTjBhk tdTjBhk, String hstnam,
                         Date createDate, Integer createManid) {
        this.rid = rid;
        this.tdTjBhk = tdTjBhk;
        this.hstnam = hstnam;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    public TdTjAnamnesis(Integer rid, TdTjBhk tdTjBhk, String hstnam,
                         String hstdat, String hstunt, String hstcruprc, String hstlps,
                         Date createDate, Integer createManid) {
        this.rid = rid;
        this.tdTjBhk = tdTjBhk;
        this.hstnam = hstnam;
        this.hstdat = hstdat;
        this.hstunt = hstunt;
        this.hstcruprc = hstcruprc;
        this.hstlps = hstlps;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjAnamnesis_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "BHK_ID" )
    public TdTjBhk getTdTjBhk() {
        return this.tdTjBhk;
    }

    public void setTdTjBhk(TdTjBhk tdTjBhk) {
        this.tdTjBhk = tdTjBhk;
    }

    @Column(name = "HSTNAM" , length = 100)
    public String getHstnam() {
        return this.hstnam;
    }

    public void setHstnam(String hstnam) {
        this.hstnam = hstnam;
    }

    @Column(name = "HSTDAT", length = 100)
    public String getHstdat() {
        return this.hstdat;
    }

    public void setHstdat(String hstdat) {
        this.hstdat = hstdat;
    }

    @Column(name = "HSTUNT", length = 100)
    public String getHstunt() {
        return this.hstunt;
    }

    public void setHstunt(String hstunt) {
        this.hstunt = hstunt;
    }

    @Column(name = "HSTCRUPRC")
    public String getHstcruprc() {
        return this.hstcruprc;
    }

    public void setHstcruprc(String hstcruprc) {
        this.hstcruprc = hstcruprc;
    }

    @Column(name = "HSTLPS", length = 50)
    public String getHstlps() {
        return this.hstlps;
    }

    public void setHstlps(String hstlps) {
        this.hstlps = hstlps;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }
}
