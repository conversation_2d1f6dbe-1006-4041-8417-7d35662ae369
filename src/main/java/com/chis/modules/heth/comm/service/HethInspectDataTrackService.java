package com.chis.modules.heth.comm.service;

import com.chis.common.utils.CollectionUtil;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 职业健康检查数据上传情况跟踪Service
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(readOnly = false)
public class HethInspectDataTrackService extends AbstractTemplate {

    public Object[] findUpCountryStatusByBusId(Integer busId) {
        String sql = "SELECT UC.BUS_ID, UC.RID, UC.STATE, UC.ERR_MSG FROM TD_ZYWS_UP_COUNTRY UC WHERE UC.BUS_TYPE = 2 AND UC.BUS_ID = :busId ORDER BY UC.RID DESC";
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("busId", busId);
        List<Object[]> list = CollectionUtil.castList(Object[].class, findDataBySqlNoPage(sql, paramMap));
        if (ObjectUtil.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }


    /**
     * <p>方法描述：根据rid批量更新所有记录状态值</p>
     * @MethodAuthor： yzz
     * @Date：2022-09-23
     **/
    public void batchUpdateAllState(List<Integer> state3RidList, List<Integer> state5RidList, List<Integer> stateUpRidList,Boolean ifNeedBhkAudit) {
        if (!CollectionUtils.isEmpty(state3RidList)) {
            batchUpdateUpcountry(state3RidList, 0);
        }
        if (!CollectionUtils.isEmpty(state5RidList)) {
            batchUpdateUpcountry(state5RidList, 9);
        }
        if (!CollectionUtils.isEmpty(stateUpRidList) && ifNeedBhkAudit) {
            batchUpdateBhk(stateUpRidList, 6);
        }
    }


    /**
     * <p>方法描述：批量更新上传国家日志记录</p>
     * @MethodAuthor： yzz
     * @Date：2022-09-23
     **/
    public void batchUpdateUpcountry(List<Integer> rids,Integer state) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("state", state);

        StringBuilder baseSql = new StringBuilder();
        baseSql.append("UPDATE TD_ZYWS_UP_COUNTRY T SET ");
        baseSql.append(" T.STATE = :state ");
        baseSql.append(" ,T.ERR_MSG = null ");
        baseSql.append("WHERE T.BUS_TYPE = 2 and T.RID IN ( ");
        int length = rids.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = rids.subList(i * 1000, endIndex);
            String sql = baseSql + StringUtils.list2string(subList, ",") + ")";
            this.executeSql(sql, paramMap);
        }
    }

    /**
     * <p>方法描述：批量更新体检主表状态</p>
     * @MethodAuthor： yzz
     * @Date：2022-09-23
     **/
    private void batchUpdateBhk(List<Integer> rids,Integer state) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("state", state);

        StringBuilder baseSql = new StringBuilder();
        baseSql.append("UPDATE TD_TJ_BHK T SET ");
        baseSql.append(" T.STATE = :state ");
        baseSql.append(" ,T.ERR_MSG = null ");
        baseSql.append("WHERE T.RID IN ( ");
        int length = rids.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = rids.subList(i * 1000, endIndex);
            String sql = baseSql + StringUtils.list2string(subList, ",") + ")";
            this.executeSql(sql, paramMap);
        }
    }
}
