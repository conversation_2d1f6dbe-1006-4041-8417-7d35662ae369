package com.chis.modules.heth.comm.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.modules.heth.comm.entity.TdTjEmhistoryClt;
import com.chis.modules.system.service.AbstractTemplate;

/**
 * @Description : 问诊(非)放射职业史信息表（数据录入）-业务层
 * @ClassAuthor : anjing
 * @Date : 2019/5/18 9:36
 **/
@Service
@Transactional(readOnly = true)
public class TdTjEmhistoryCltCommServiceImpl extends AbstractTemplate {

    /**
     * @Description : 根据主表Id、类型获取放射史列表
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 9:53
     **/
    public List<TdTjEmhistoryClt> selectEmhistoryCltListByBhkIdAndHisType(Integer bhkId, Integer hisType) {
        if (null != bhkId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdTjEmhistoryClt t ");
            sb.append(" where t.fkByBhkId.rid = ").append(bhkId);
            sb.append(" and t.hisType = ").append(hisType);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }
}
