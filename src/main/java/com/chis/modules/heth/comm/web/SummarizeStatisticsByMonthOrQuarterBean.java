package com.chis.modules.heth.comm.web;

import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.*;

@ManagedBean(name = "summarizeStatisticsByMonthOrQuarterBean")
@ViewScoped
public class SummarizeStatisticsByMonthOrQuarterBean extends FacesEditBean implements IProcessData {
    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
     * 查询地区集合
     */
    private List<TsZone> searchZoneList;
    /**
     * 查询条件 地区编码
     */
    private String searchCheckOrgZoneGb;
    /**
     * 查询条件 地区名称
     */
    private String searchCheckOrgZoneName;

    /**
     * 查询条件 职业健康检查机构
     */
    private String searchCheckOrgId;
    private String searchCheckOrgName;
    /**
     * 查询条件 统计类型
     */
    private Integer analyType;
    /**
     * 查询条件 报告出具周期 年 集合
     */
    private List<Integer> searchYearList;
    /**
     * 查询条件 报告出具周期 年
     */
    private Integer searchYear;
    /**
     * 查询条件 月份或者季度 集合
     */
    private List<Integer> searchOtList;
    /**
     * 查询条件 月份或者季度
     */
    private Integer searchOt;
    /**
     * 标题
     */
    private String tableTitle;
    /**
     * 标题赋值用
     */
    private String[] titleBaseArr;
    /**
     * 危害因素集合
     */
    private List<Integer> badrsnList;
    /** 页面数据倍数 */
    private Integer[] pageSizeArr = new Integer[]{5, 10};
    /** 缓存的页面数据条数 */
    private Integer cachePageSize;
    /** 缓存统计类型 */
    private Integer cacheAnalyType;
    /** 导出查询的危害因素集合 */
    private List<Integer> exportBadrsns;

    public SummarizeStatisticsByMonthOrQuarterBean() {
        this.ifSQL = true;
        this.initZone();
        this.analyType = 0;
        this.initSearchYear();
        this.titleBaseArr = new String[]{"各职业健康检查机构职业健康检查月度开展情况", "各职业健康检查机构职业健康检查季度开展情况"};
        this.tableTitle = this.titleBaseArr[0];
        this.resetPageSize(14);
    }

    @Override
    public void searchAction() {
        super.searchAction();
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<Object[]> result = CollectionUtil.castList(Object[].class, list);
        for (Object[] obj : result) {
            //地区去除省份
            String fullName = StringUtils.objectToString(obj[0]);
            obj[0] = fullName.substring(fullName.indexOf("_") + 1);
        }
    }

    @Override
    public String[] buildHqls() {
        this.tableTitle = this.fillStartAndEndDateStr(this.paramMap);
        String querySql = this.generateQuerySql(this.paramMap, this.badrsnList);
        String countSql = "SELECT COUNT(*) FROM (" + querySql + ") ";
        return new String[]{querySql, countSql};
    }

    /**
     * <p>方法描述：查询数据 </p>
     * pw 2024/8/9
     **/
    private String generateQuerySql(Map<String, Object> curMap, List<Integer> badRsns) {
        StringBuffer buffer = new StringBuffer();
        buffer.append("  SELECT T3.FULL_NAME, ")
                .append("       T2.UNIT_NAME, ")
                .append("       K.CODE_NAME, ")
                .append("       SUM(NVL(T4.HOLD_CARD_NUM, 0)) AS HOLD_CARD_NUM, ")
                .append("       SUM(NVL(T4.BHK_NUM, 0)) AS BHK_NUM, ")
                .append("       SUM(NVL(T4.CONTRAINDLIST_NUM, 0)) AS CONTRAINDLIST_NUM, ")
                .append("       SUM(NVL(T4.SUSPECTED_NUM, 0)) AS SUSPECTED_NUM, ")
                .append("       T3.REAL_ZONE_TYPE, ")
                .append("       T3.ZONE_TYPE ")
                .append(" FROM TD_ZW_MONTH_BHK_PROV T ")
                .append(" INNER JOIN TS_UNIT M ON T.ORG_ID = M.RID ")
                .append(" INNER JOIN TS_ZONE M1 ON M.MANAGE_ZONE_ID = M1.RID ")
                .append(" INNER JOIN TD_ZW_MONTH_ORG T1 ON T1.MAIN_ID = T.RID ")
                .append(" INNER JOIN TB_TJ_SRVORG T2 ON T2.RID = T1.ORG_ID ")
                .append(" INNER JOIN TS_ZONE T3 ON T2.ZONE_ID = T3.RID ");
        if (!CollectionUtils.isEmpty(badRsns)) {
            buffer.append(" LEFT JOIN TS_SIMPLE_CODE K ON K.RID IN(:badrsnList) ");
            curMap.put("badrsnList", badRsns);
        } else {
            buffer.append(" LEFT JOIN TS_SIMPLE_CODE K ON K.RID = 0 ");
        }
        buffer.append(" LEFT JOIN TD_ZW_MONTH_ORG_BADRSNS T4 ON T4.MAIN_ID = T1.RID AND T4.BADRSN_ID = K.RID ")
                .append(" WHERE M1.ZONE_TYPE = 2 AND T.STATE = 1 AND NVL(T.DEL_MARK,0)=0 ");
        if (CollectionUtils.isEmpty(badRsns)) {
            buffer.append(" AND 1=2 ");
        }
        if (StringUtils.isNotBlank(this.searchCheckOrgZoneGb)) {
            buffer.append(" AND T3.ZONE_GB LIKE :searchCheckOrgZoneGb escape '\\\'");
            curMap.put("searchCheckOrgZoneGb", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchCheckOrgZoneGb).trim()) + "%");
        }
        List<Integer> orgIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(this.searchCheckOrgId)) {
            for (String str : this.searchCheckOrgId.split(",")) {
                if (StringUtils.isBlank(str)) {
                    continue;
                }
                orgIdList.add(Integer.parseInt(str));
            }
        }
        if (!CollectionUtils.isEmpty(orgIdList)) {
            buffer.append(" AND T1.ORG_ID IN (:orgIdList) ");
            curMap.put("orgIdList", orgIdList);
        }

        buffer.append(" AND ((T.START_BHK_DATE>=TO_DATE(:startDate, 'YYYY-MM-DD HH24:MI:SS') AND T.START_BHK_DATE<=TO_DATE(:endDate, 'YYYY-MM-DD HH24:MI:SS')) OR (T.END_BHK_DATE>=TO_DATE(:startDate, 'YYYY-MM-DD HH24:MI:SS') AND T.END_BHK_DATE <=TO_DATE(:endDate, 'YYYY-MM-DD HH24:MI:SS'))) ");

        buffer.append(" GROUP BY T2.RID,T2.UNIT_NAME, T3.FULL_NAME,T3.ZONE_GB,K.RID,K.CODE_NAME,K.NUM,T3.REAL_ZONE_TYPE,T3.ZONE_TYPE ")
                .append(" ORDER BY T3.ZONE_GB,T2.UNIT_NAME,K.NUM ");
        return buffer.toString();
    }


    /**
     * <p>方法描述：获取危害因素sql </p>
     * pw 2024/8/12
     **/
    private String generateQueryBadRsnIdSql(Map<String, Object> curMap) {
        StringBuffer buffer = new StringBuffer();
        buffer.append("  SELECT DISTINCT T4.BADRSN_ID ")
                .append(" FROM TD_ZW_MONTH_BHK_PROV T ")
                .append(" INNER JOIN TS_UNIT M ON T.ORG_ID = M.RID ")
                .append(" INNER JOIN TS_ZONE M1 ON M.MANAGE_ZONE_ID = M1.RID ")
                .append(" INNER JOIN TD_ZW_MONTH_ORG T1 ON T1.MAIN_ID = T.RID ")
                .append(" INNER JOIN TB_TJ_SRVORG T2 ON T2.RID = T1.ORG_ID ")
                .append(" INNER JOIN TS_ZONE T3 ON T2.ZONE_ID = T3.RID ");
        buffer.append(" LEFT JOIN TD_ZW_MONTH_ORG_BADRSNS T4 ON T4.MAIN_ID = T1.RID ")
                .append(" WHERE M1.ZONE_TYPE = 2 AND T.STATE = 1 AND NVL(T.DEL_MARK,0)=0 ");
        if (StringUtils.isNotBlank(this.searchCheckOrgZoneGb)) {
            buffer.append(" AND T3.ZONE_GB LIKE :searchCheckOrgZoneGb escape '\\\'");
            curMap.put("searchCheckOrgZoneGb", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchCheckOrgZoneGb).trim()) + "%");
        }
        List<Integer> orgIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(this.searchCheckOrgId)) {
            for (String str : this.searchCheckOrgId.split(",")) {
                if (StringUtils.isBlank(str)) {
                    continue;
                }
                orgIdList.add(Integer.parseInt(str));
            }
        }
        if (!CollectionUtils.isEmpty(orgIdList)) {
            buffer.append(" AND T1.ORG_ID IN (:orgIdList) ");
            curMap.put("orgIdList", orgIdList);
        }

        buffer.append(" AND ((T.START_BHK_DATE>=TO_DATE(:startDate, 'YYYY-MM-DD HH24:MI:SS') AND T.START_BHK_DATE<=TO_DATE(:endDate, 'YYYY-MM-DD HH24:MI:SS')) OR (T.END_BHK_DATE>=TO_DATE(:startDate, 'YYYY-MM-DD HH24:MI:SS') AND T.END_BHK_DATE <=TO_DATE(:endDate, 'YYYY-MM-DD HH24:MI:SS'))) ");
        return buffer.toString();
    }

    /**
     * <p>方法描述：查询 校验 调整每页数据条数 </p>
     * pw 2024/8/12
     **/
    public void preSearchAction() {
        if (!this.validateQuery()) {
            return;
        }
        this.badrsnList = this.queryBadrsnList();
        Integer curPageSize = CollectionUtils.isEmpty(this.badrsnList) ? null : this.badrsnList.size();
        if (null == this.cacheAnalyType) {
            this.cacheAnalyType = this.analyType;
        }
        boolean ifMarch = null != curPageSize &&
                (curPageSize != this.cachePageSize || this.cacheAnalyType.compareTo(this.analyType) != 0);
        if (ifMarch) {
            this.cacheAnalyType = this.analyType;
            this.resetPageSize(curPageSize);
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("tabView:mainForm:dataTable");
            dataTable.reset();
            dataTable.setRows(curPageSize);
            dataTable.setRowsPerPageTemplate(this.getPerPageSize());
        }
        RequestContext.getCurrentInstance().execute("exeSearch()");
    }

    /**
     * <p>方法描述：职业健康检查机构弹出框 </p>
     * pw 2024/8/9
     **/
    public void selCheckOrgAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 625, null, 500);

        Map<String, List<String>> paramMap = new HashMap<>(16);
        List<String> paramList = new ArrayList<>();
        paramList.add(this.searchCheckOrgId);
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<>();
        paramList.add(this.searchCheckOrgZoneGb);
        paramMap.put("searchZoneCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
    }

    /**
     * <p>方法描述：选择职业健康检查机构 </p>
     * pw 2024/8/9
     **/
    public void onSelectCheckOrgAction(SelectEvent event) {
        Map<String, Object> selectedMap = new HashMap<>();
        try {
            selectedMap = (Map<String, Object>) event.getObject();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (CollectionUtils.isEmpty(selectedMap)) {
            return;
        }
        this.searchCheckOrgId = null;
        this.searchCheckOrgName = null;
        List<TbTjSrvorg> list = CollectionUtil.castList(TbTjSrvorg.class, selectedMap.get("selectPros"));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        StringBuilder names = new StringBuilder();
        StringBuilder ids = new StringBuilder();
        for (TbTjSrvorg t : list) {
            names.append("，").append(t.getUnitName());
            ids.append(",").append(t.getRid());
        }
        this.searchCheckOrgId = ids.substring(1);
        this.searchCheckOrgName = names.substring(1);
    }

    /**
     * <p>方法描述：清空选择的职业健康检查机构 </p>
     * pw 2024/8/9
     **/
    public void clearCheckOrg() {
        this.searchCheckOrgId = null;
        this.searchCheckOrgName = null;
        RequestContext.getCurrentInstance().update("tabView:mainForm:checkOrgName");
    }

    /**
     * <p>方法描述：切换统计类型 </p>
     * pw 2024/8/9
     **/
    public void changeAnalyType() {
        this.initSearchOtList(true);
    }

    /**
     * <p>方法描述：切换报告出具周期的年 </p>
     * pw 2024/8/9
     **/
    public void changeYear() {
        this.initYearList();
        this.initSearchOtList(false);
    }

    /**
     * <p>方法描述：导出前校验 </p>
     * pw 2024/8/12
     **/
    public void preDownLoadFile() {
        if (!this.validateQuery()) {
            return;
        }
        this.exportBadrsns = this.queryBadrsnList();
        if (CollectionUtils.isEmpty(this.exportBadrsns)) {
            JsfUtil.addErrorMessage("无可导出的数据！");
            return;
        }
        RequestContext.getCurrentInstance().execute("generateClick()");
    }

    /**
     * <p>方法描述：导出 </p>
     * pw 2024/8/12
     **/
    public DefaultStreamedContent getDownloadFile() {
        Map<String, Object> curMap = new HashMap<>();
        String title = this.fillStartAndEndDateStr(curMap);
        List<Object[]> exportDataList = generateExportData(curMap);
        this.processData(exportDataList);
        try(ByteArrayOutputStream baos =  new ByteArrayOutputStream()) {
            //生成包含sheet以及对应sheet中标题行的Workbook
            Workbook wBook = this.singleSheetWorkbook(exportDataList, title);
            wBook.write(baos);
            baos.flush();
            byte[] aa = baos.toByteArray();
            String tmpName = title+".xlsx";
            String fileName = URLEncoder.encode(tmpName, "UTF-8");
            return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
        } catch(Exception e) {
            JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    /**
     * <p>方法描述：生成导出的Workbook </p>
     * pw 2024/8/12
     **/
    private Workbook singleSheetWorkbook(List<Object[]> expList, String titleName) {
        String[][] multiLineColumnHeaders = new String[][]{{titleName,"","","","","",""},
                {"地区","职业健康检查机构","危害因素","接触职业病危害因素人数","职业健康检查人数","发现职业禁忌证人数","发现疑似职业病数量"}};
        List<ExcelExportObject[]> expDataList = this.pakExcelExportDataList(expList);
        ExcelExportUtil excelExportUtil = new ExcelExportUtil(multiLineColumnHeaders,  expDataList);
        // sheetName 去除时间范围
        excelExportUtil.setSheetName(titleName.split("（")[0]);
        //合并单元格
        excelExportUtil.addMergeCells(0, 0, 0, 6);
        excelExportUtil.setColumnHeight(26);
        excelExportUtil.setFrozenPaneRowsNum(2);
        Integer[] columnWidths = new Integer[]{28,30,20,30,30,20,15};
        excelExportUtil.setColumnWidths(columnWidths);
        excelExportUtil.setTitle(titleName);
        return excelExportUtil.exportExcel();
    }

    /**
     * <p>方法描述：封装导出数据 </p>
     * pw 2024/8/12
     **/
    private List<ExcelExportObject[]> pakExcelExportDataList(List<Object[]> expList) {
        List<ExcelExportObject[]> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(expList)) {
            return resultList;
        }
        List<ExcelExportObject> curList;
        for (Object[] arr : expList) {
            curList = new ArrayList<>();
            // 地区
            ExcelExportObject obj = new ExcelExportObject(arr[0]);
            curList.add(obj);
            // 职业健康检查机构
            obj = new ExcelExportObject(arr[1]);
            curList.add(obj);
            // 危害因素
            obj = new ExcelExportObject(arr[2]);
            curList.add(obj);
            // 接触职业病危害因素人数
            obj = new ExcelExportObject(arr[3]);
            obj.setAlignStyleType(XSSFCellStyle.ALIGN_CENTER);
            curList.add(obj);
            // 职业健康检查人数
            obj = new ExcelExportObject(arr[4]);
            obj.setAlignStyleType(XSSFCellStyle.ALIGN_CENTER);
            curList.add(obj);
            // 发现职业禁忌证人数
            obj = new ExcelExportObject(arr[5]);
            obj.setAlignStyleType(XSSFCellStyle.ALIGN_CENTER);
            curList.add(obj);
            // 发现疑似职业病数量
            obj = new ExcelExportObject(arr[6]);
            obj.setAlignStyleType(XSSFCellStyle.ALIGN_CENTER);
            curList.add(obj);
            resultList.add(this.exchangeExpObjListToArr(curList));
        }
        return resultList;
    }

    /**
     * <p>方法描述：ExcelExportObject集合转数组 </p>
     * pw 2024/8/12
     **/
    private ExcelExportObject[] exchangeExpObjListToArr(List<ExcelExportObject> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        int size = list.size();
        ExcelExportObject[] objArr = new ExcelExportObject[size];
        for (int i=0; i<size; i++) {
            objArr[i] = list.get(i);
        }
        return objArr;
    }

    /**
     * <p>方法描述：获取导出的数据 </p>
     * pw 2024/8/12
     **/
    private List<Object[]> generateExportData(Map<String, Object> curMap) {
        String querySql = this.generateQuerySql(curMap, this.exportBadrsns);
        return this.commService.findDataBySqlNoPage(querySql, curMap);
    }

    /**
     * <p>方法描述：组装日期范围  加入查询条件</p>
     * pw 2024/8/9
     **/
    private String fillStartAndEndDateStr(Map<String, Object> curMap) {
        String startDate = null;
        String endDate = null;
        String title = "";
        if (0 == this.analyType) {
            if (1 == this.searchOt) {
                startDate = (this.searchYear - 1) + "-12-16 00:00:00";
            } else {
                startDate = this.searchYear + "-" + (this.searchOt - 1) + "-16 00:00:00";
            }
            endDate = this.searchYear + "-" + this.searchOt + "-15 23:59:59";
            title = this.titleBaseArr[0];
        }
        if (1 == this.analyType) {
            switch (this.searchOt) {
                case 1:
                    startDate = (this.searchYear - 1) + "-11-16 00:00:00";
                    endDate = this.searchYear + "-2-15 23:59:59";
                    break;
                case 2:
                    startDate = this.searchYear + "-02-16 00:00:00";
                    endDate = this.searchYear + "-05-15 23:59:59";
                    break;
                case 3:
                    startDate = this.searchYear + "-05-16 00:00:00";
                    endDate = this.searchYear + "-08-15 23:59:59";
                    break;
                case 4:
                    startDate = this.searchYear + "-08-16 00:00:00";
                    endDate = this.searchYear + "-11-15 23:59:59";
                    break;
                default:
                    break;
            }
            title = this.titleBaseArr[1];
        }
        curMap.put("startDate", startDate);
        curMap.put("endDate", endDate);
        Date start = DateUtils.parseDate(startDate);
        Date end = DateUtils.parseDate(endDate);
        return title + "（" + (null == start ? "" : DateUtils.formatDate(start)) + "~" + (null == end ? "" : DateUtils.formatDate(end)) + "）";
    }

    /**
     * <p>方法描述：查询 导出 校验 </p>
     * pw 2024/8/9
     **/
    private boolean validateQuery() {
        boolean flag = true;
        if (null == this.searchOt) {
            JsfUtil.addErrorMessage("请选择报告出具日期的" + (0 == this.analyType ? "月！" : "季度！"));
            flag = false;
        }
        return flag;
    }

    /**
     * <p>方法描述：查询危害因素 </p>
     * pw 2024/8/9
     **/
    private List<Integer> queryBadrsnList() {
        Map<String, Object> curMap = new HashMap<>();
        String querySql = this.generateQueryBadRsnIdSql(curMap);
        this.fillStartAndEndDateStr(curMap);
        List<Object> resultList = this.commService.findDataBySqlNoPage(querySql, curMap);
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }
        List<Integer> list = new ArrayList<>();
        for (Object o : resultList) {
            if (null == o) {
                continue;
            }
            list.add(Integer.parseInt(o.toString()));
        }
        return list;
    }

    /**
     * <p>方法描述：重置每页显示的数据条数 </p>
     * pw 2024/8/9
     **/
    private void resetPageSize(int size) {
        this.cachePageSize = size;
        this.setPageSize(size);
        StringBuffer buffer = new StringBuffer();
        buffer.append(size).append(",").append(size * this.pageSizeArr[0]).append(",").append(size * this.pageSizeArr[1]);
        this.setPerPageSize(buffer.toString());
    }

    /**
     * <p>方法描述：重置报告出具周期的月或者季度列表 </p>
     * pw 2024/8/9
     **/
    private void initSearchOtList(boolean ifChangeAnalyType) {
        if (null == this.analyType) {
            return;
        }
        if (null == this.searchOtList) {
            this.searchOtList = new ArrayList<>();
        }
        this.searchOtList.clear();
        int max = 12;
        boolean ifMarch = 0 == this.analyType && DateUtils.getYearInt() == this.searchYear;
        if (ifMarch) {
            max = DateUtils.getMonth(new Date());
        }
        if (1 == this.analyType) {
            max = 4;
        }
        ifMarch = 1 == this.analyType && DateUtils.getYearInt() == this.searchYear;
        if (ifMarch) {
            max = this.generateMaxQAuarter();
        }
        for (int i = 1; i <= max; i++) {
            this.searchOtList.add(i);
        }
        Integer lastest = this.searchOtList.get(this.searchOtList.size() - 1);
        ifChangeAnalyType = ifChangeAnalyType || null != this.searchOt && this.searchOt.compareTo(lastest) > 0;
        if (ifChangeAnalyType) {
            this.searchOt = null;
        }
    }

    /**
     * <p>方法描述：获取当年的最大可选季度 </p>
     * pw 2024/8/9
     **/
    private int generateMaxQAuarter() {
        Date curDate = new Date();
        Date a = DateUtils.parseDate(this.searchYear + "-2-16");
        Date b = DateUtils.parseDate(this.searchYear + "-5-16");
        Date c = DateUtils.parseDate(this.searchYear + "-8-16");
        if (a.compareTo(curDate) > 0) {
            return 1;
        } else if (b.compareTo(curDate) > 0) {
            return 2;
        } else if (c.compareTo(curDate) > 0) {
            return 3;
        } else {
            return 4;
        }
    }

    /**
     * <p>方法描述：初始化报告出具周期的年集合 </p>
     * pw 2024/8/9
     **/
    private void initYearList() {
        if (null == this.searchYearList) {
            this.searchYearList = new ArrayList<>();
        }
        if (!CollectionUtils.isEmpty(this.searchYearList)) {
            List<Integer> removeList = new ArrayList<>();
            for (Integer a : this.searchYearList) {
                if (a.compareTo(this.searchYear) > 0) {
                    continue;
                }
                removeList.add(a);
            }
            if (!CollectionUtils.isEmpty(removeList)) {
                this.searchYearList.removeAll(removeList);
            }
        }

        int rangeYear = 5;
        // 从大到小
        for (int i = 0; i < rangeYear; i++) {
            this.searchYearList.add(this.searchYear - i);
        }
    }

    /**
     * <p>方法描述：初始化报告出具周期的年 </p>
     * pw 2024/8/9
     **/
    private void initSearchYear() {
        this.searchYear = DateUtils.getYearInt();
        this.initYearList();
        this.initSearchOtList(true);
    }

    /**
     * <p>方法描述：初始化地区 </p>
     * pw 2024/8/9
     **/
    private void initZone() {
        //查询条件：管辖范围内地区
        TsZone zone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        this.searchCheckOrgZoneGb = null == zone ? null : zone.getZoneGb();
        this.searchCheckOrgZoneName = null == zone ? null : zone.getZoneName();
        this.searchZoneList = null == this.searchCheckOrgZoneGb ? new ArrayList<TsZone>() :
                this.commService.findZoneListByGbAndType(this.searchCheckOrgZoneGb, true, "", "");
    }

    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public String getSearchCheckOrgZoneGb() {
        return searchCheckOrgZoneGb;
    }

    public void setSearchCheckOrgZoneGb(String searchCheckOrgZoneGb) {
        this.searchCheckOrgZoneGb = searchCheckOrgZoneGb;
    }

    public String getSearchCheckOrgZoneName() {
        return searchCheckOrgZoneName;
    }

    public void setSearchCheckOrgZoneName(String searchCheckOrgZoneName) {
        this.searchCheckOrgZoneName = searchCheckOrgZoneName;
    }

    public String getSearchCheckOrgId() {
        return searchCheckOrgId;
    }

    public void setSearchCheckOrgId(String searchCheckOrgId) {
        this.searchCheckOrgId = searchCheckOrgId;
    }

    public String getSearchCheckOrgName() {
        return searchCheckOrgName;
    }

    public void setSearchCheckOrgName(String searchCheckOrgName) {
        this.searchCheckOrgName = searchCheckOrgName;
    }

    public Integer getAnalyType() {
        return analyType;
    }

    public void setAnalyType(Integer analyType) {
        this.analyType = analyType;
    }

    public List<Integer> getSearchYearList() {
        return searchYearList;
    }

    public void setSearchYearList(List<Integer> searchYearList) {
        this.searchYearList = searchYearList;
    }

    public Integer getSearchYear() {
        return searchYear;
    }

    public void setSearchYear(Integer searchYear) {
        this.searchYear = searchYear;
    }

    public List<Integer> getSearchOtList() {
        return searchOtList;
    }

    public void setSearchOtList(List<Integer> searchOtList) {
        this.searchOtList = searchOtList;
    }

    public Integer getSearchOt() {
        return searchOt;
    }

    public void setSearchOt(Integer searchOt) {
        this.searchOt = searchOt;
    }

    public String getTableTitle() {
        return tableTitle;
    }

    public void setTableTitle(String tableTitle) {
        this.tableTitle = tableTitle;
    }

    public String[] getTitleBaseArr() {
        return titleBaseArr;
    }

    public void setTitleBaseArr(String[] titleBaseArr) {
        this.titleBaseArr = titleBaseArr;
    }

    public List<Integer> getBadrsnList() {
        return badrsnList;
    }

    public void setBadrsnList(List<Integer> badrsnList) {
        this.badrsnList = badrsnList;
    }
}
