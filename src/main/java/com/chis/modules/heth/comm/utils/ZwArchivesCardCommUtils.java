package com.chis.modules.heth.comm.utils;

import com.chis.modules.heth.comm.entity.TdZwjdArchivesCard;
import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Map;

/**
 *  <p>类描述：职业病鉴定报告卡工具类</p>
 * @ClassAuthor hsj 2021/11/9 10:14
 */
public class ZwArchivesCardCommUtils {
	/**
	 *  <p>方法描述：关联实体初始化</p>
	 * @MethodAuthor hsj
	 */
	public static void initEntity(TdZwjdArchivesCard archivesCard) {
		//证件类型
		if (null == archivesCard.getFkByCardTypeId()) {
			archivesCard.setFkByCardTypeId(new TsSimpleCode());
		}
		//职业病名称
		if (null == archivesCard.getFkByZybTypeId()) {
			archivesCard.setFkByZybTypeId(new TsSimpleCode());
		}
		if (null == archivesCard.getFkByZybDisTypeId()) {
			archivesCard.setFkByZybDisTypeId(new TsSimpleCode());
		}
		//病种
		if (null == archivesCard.getFkByRptTypeId()) {
			archivesCard.setFkByRptTypeId(new TsSimpleCode());
		}
		//鉴定类型5543
		if (null == archivesCard.getFkByJdTypeId()) {
			archivesCard.setFkByJdTypeId(new TsSimpleCode());
		}
		//首次鉴定结论
		if (null == archivesCard.getFkByJdRstId()) {
			archivesCard.setFkByJdRstId(new TsSimpleCode());
		}
		//再次鉴定结论
		if (null == archivesCard.getFkByJdAgainRstId()) {
			archivesCard.setFkByJdAgainRstId(new TsSimpleCode());
		}
		//fkByJdZybTypeId
		if (null == archivesCard.getFkByJdZybTypeId()) {
			archivesCard.setFkByJdZybTypeId(new TsSimpleCode());
		}
		//fkByJdZybDisTypeId
		if (null == archivesCard.getFkByJdZybDisTypeId()) {
			archivesCard.setFkByJdZybDisTypeId(new TsSimpleCode());
		}
		//fkByJdRptTypeId
		if (null == archivesCard.getFkByJdRptTypeId()) {
			archivesCard.setFkByJdRptTypeId(new TsSimpleCode());
		}
	}
	/**
	 *  <p>方法描述：清空</p>
	 * @MethodAuthor hsj
	 */
	public  static void clearArchivesCardEntity(TdZwjdArchivesCard archivesCard) {
		//证件类型
		if (null == archivesCard.getFkByCardTypeId() || null == archivesCard.getFkByCardTypeId().getRid())  {
			archivesCard.setFkByCardTypeId(null);
		}
		//职业病名称
		if (null == archivesCard.getFkByZybTypeId() || null == archivesCard.getFkByZybTypeId().getRid())  {
			archivesCard.setFkByZybTypeId(null);
		}
		if (null == archivesCard.getFkByZybDisTypeId() || null == archivesCard.getFkByZybDisTypeId().getRid())  {
			archivesCard.setFkByZybDisTypeId(null);
		}
		//病种
		if (null == archivesCard.getFkByRptTypeId() || null == archivesCard.getFkByRptTypeId().getRid())  {
			archivesCard.setFkByRptTypeId(null);
		}
		//鉴定类型5543
		if (null == archivesCard.getFkByJdTypeId() || null == archivesCard.getFkByJdTypeId().getRid())  {
			archivesCard.setFkByJdTypeId(null);
		}
		//首次鉴定结论
		if (null == archivesCard.getFkByJdRstId() || null == archivesCard.getFkByJdRstId().getRid())  {
			archivesCard.setFkByJdRstId(null);
		}
		//再次鉴定结论
		if (null == archivesCard.getFkByJdAgainRstId() || null == archivesCard.getFkByJdAgainRstId().getRid())  {
			archivesCard.setFkByJdAgainRstId(null);
		}
		//fkByJdZybTypeId
		if (null == archivesCard.getFkByJdZybTypeId() || null == archivesCard.getFkByJdZybTypeId().getRid())  {
			archivesCard.setFkByJdZybTypeId(null);
		}
		//fkByJdZybDisTypeId
		if (null == archivesCard.getFkByJdZybDisTypeId() || null == archivesCard.getFkByJdZybDisTypeId().getRid())  {
			archivesCard.setFkByJdZybDisTypeId(null);
		}
		//fkByJdRptTypeId
		if (null == archivesCard.getFkByJdRptTypeId() || null == archivesCard.getFkByJdRptTypeId().getRid())  {
			archivesCard.setFkByJdRptTypeId(null);
		}

	}
}
