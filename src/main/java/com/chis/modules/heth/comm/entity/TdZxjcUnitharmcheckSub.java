package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-4
 */
@Entity
@Table(name = "TD_ZXJC_UNITHARMCHECK_SUB")
@SequenceGenerator(name = "TdZxjcUnitharmcheckSub", sequenceName = "TD_ZXJC_UNITHARMCHECK_SUB_SEQ", allocationSize = 1)
public class TdZxjcUnitharmcheckSub implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZxjcUnitharmChk fkByMainId;
	private Integer type;
	private TsSimpleCode fkByFactorId;
	private Integer ifatFactor;
	private Integer checkNum;
	private Integer excessNum;
	private Integer workNum;
	private Integer workExcessNum;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private String factorName;
	
	public TdZxjcUnitharmcheckSub() {
	}

	public TdZxjcUnitharmcheckSub(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZxjcUnitharmcheckSub")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZxjcUnitharmChk getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZxjcUnitharmChk fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "TYPE")	
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}	
			
	@ManyToOne
	@JoinColumn(name = "FACTOR_ID")			
	public TsSimpleCode getFkByFactorId() {
		return fkByFactorId;
	}

	public void setFkByFactorId(TsSimpleCode fkByFactorId) {
		this.fkByFactorId = fkByFactorId;
	}	
			
	@Column(name = "IFAT_FACTOR")	
	public Integer getIfatFactor() {
		return ifatFactor;
	}

	public void setIfatFactor(Integer ifatFactor) {
		this.ifatFactor = ifatFactor;
	}	
			
	@Column(name = "CHECK_NUM")	
	public Integer getCheckNum() {
		return checkNum;
	}

	public void setCheckNum(Integer checkNum) {
		this.checkNum = checkNum;
	}	
			
	@Column(name = "EXCESS_NUM")	
	public Integer getExcessNum() {
		return excessNum;
	}

	public void setExcessNum(Integer excessNum) {
		this.excessNum = excessNum;
	}	
			
	@Column(name = "WORK_NUM")	
	public Integer getWorkNum() {
		return workNum;
	}

	public void setWorkNum(Integer workNum) {
		this.workNum = workNum;
	}	
			
	@Column(name = "WORK_EXCESS_NUM")	
	public Integer getWorkExcessNum() {
		return workExcessNum;
	}

	public void setWorkExcessNum(Integer workExcessNum) {
		this.workExcessNum = workExcessNum;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
	@Column(name = "FACTOR_NAME")	
	public String getFactorName() {
		return factorName;
	}

	public void setFactorName(String factorName) {
		this.factorName = factorName;
	}	
			
}