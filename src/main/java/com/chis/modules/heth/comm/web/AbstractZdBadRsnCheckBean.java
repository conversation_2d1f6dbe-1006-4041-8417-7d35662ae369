package com.chis.modules.heth.comm.web;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;

import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.service.HethStaQueryCommServiceImpl;
import org.primefaces.component.outputpanel.OutputPanel;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MapUtils;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.heth.comm.javabean.TdTjBhkInfoBean;
import com.chis.modules.heth.comm.service.HethBaseCommServiceImpl;
import com.chis.modules.heth.comm.service.ZwReportCardCheckCommServiceImpl;
import com.chis.modules.heth.comm.service.ZwReportCardCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.HolidayUtil;
import com.chis.modules.system.web.FacesEditBean;

/**
 * <p>类描述：重点危害因素检查情况审核基类</p>
 * @ClassAuthor rcj,2020年10月16日,AbstractReportCardCheckBean
 * */
/**
 * <p>修订内容：查询和审核模块基类</p>
 * @ClassReviser qrr,2021年1月6日,AbstractZdBadRsnCheckBean
 * */
public abstract class AbstractZdBadRsnCheckBean extends FacesEditBean{
	private static final long serialVersionUID = 1L;
	protected SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	protected ZwReportCardCheckCommServiceImpl cardCheckServiceImpl = SpringContextHolder.getBean(ZwReportCardCheckCommServiceImpl.class);

    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    protected HethBaseCommServiceImpl baseServiceImpl = SpringContextHolder.getBean(HethBaseCommServiceImpl.class);
	private ZwReportCardCommServiceImpl cardServiceImpl = SpringContextHolder.getBean(ZwReportCardCommServiceImpl.class);
	private HethStaQueryCommServiceImpl hethStaQueryServiceImpl = SpringContextHolder.getBean(HethStaQueryCommServiceImpl.class);


	/**查询条件：地区名称*/
    private String searchZoneName;
    /**查询条件：地区编码*/
    private String searchZoneCode;
    /**查询条件：地区集合*/
    private List<TsZone> zoneList;
    /**查询条件：检查机构Id*/
    private String searchUnitId;
    /**查询条件：用人单位地区名称*/
    private String searchCrptZoneCode;
    /**查询条件：用人单位地区编码*/
    private String searchCrptZoneName;
    /**查询条件：用人单位地区集合*/
    private List<TsZone> crptZoneList;
    /**查询条件：用人单位名称*/
    private String searchCrptName;
    /**查询条件：姓名*/
    private String searchPersonName;
    /**查询条件：身份证*/
    private String searchPersonIdc;
    /**查询条件：体检日期查询-开始日期*/
    protected Date searchBhkBdate;
    /**查询条件：体检日期查询-结束日期*/
    protected Date searchBhkEdate;
	/**查询条件-在岗状态*/
	private List<TsSimpleCode> onGuardList;
	private String selectOnGuardNames;
	/**查询条件-在岗状态*/
	private String selectOnGuardIds;
	/**查询条件-危害因素*/
	private List<TsSimpleCode> badRsnList;
	private String selectBadRsnNames;
	/**查询条件-危害因素*/
	private String selectBadRsnIds;
	/**查询条件-结论是否正常*/
	private String[] concluStates;
	/**查询条件-体检机构名称*/
	private String searchUnitName;
	
	private TdZwyjBsnBhk tdZwyjBsnBhk;
	/**重点职业病危害因素项目列表*/
	private List<ZdzybBadRsnInfo> zdzybBadRsnInfoList;

	/**是否有其他异常情况*/
	private boolean ifAbnormal;
	private String abnormalInfo;

	/**体检记录表是否存在*/
	private boolean ifBhkInfoExist;

    private TdTjBhkInfoBean tjBhkInfoBean = new TdTjBhkInfoBean();
    /*** 体检项目的布局表格*/
	private OutputPanel archivePanel = (OutputPanel) JsfUtil.getApplication().createComponent(
			OutputPanel.COMPONENT_TYPE);
	
	protected Integer rid;//业务主键
	/** 审核期限 */
	protected String limitTime;
	/** 退回原因 */
	private String backRsn;
	/** 退回原因只读 */
	private Boolean readOnly = false;
	/** 选中的状态 */
	protected String[] states;
	/** 所有状态 */
	protected List<SelectItem> stateList = new ArrayList<>();
	
	protected TdZwyjBsnCheck newFlow = new TdZwyjBsnCheck();
    /** 选择的结果集 */
    protected List<Object[]> selectEntitys;
    //是否显示提醒
    protected Boolean ifShowWarn = false;

	//是否市直属
	protected boolean ifCityDirect;

	public AbstractZdBadRsnCheckBean(){
		this.ifSQL = true;
		limitTime = PropertyUtils.getValue("zdBadRsnlimitTime");
		initParam();
	}
	/**
    * @Description : 查询参数初始化
    * @MethodAuthor: rcj
    * @Date : 2020/10/30 10:23
    **/
    private void initParam() {
    	TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
		if (null==tsZone) {
			tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
		}
        this.searchZoneCode = tsZone.getZoneCode();
        this.searchZoneName = tsZone.getZoneName();
        this.zoneList = this.commService.findZoneList(false, tsZone.getZoneGb(), null, null);

        this.crptZoneList = this.commService.findZoneList(true, null, null, null);

		// 检查机构初始化
        this.onZoneSelect();

		this.onGuardList = commService.findSimpleCodesByTypeId("5009");
		StringBuffer hql = new StringBuffer();
		hql.append(" select t.fkByBadrsnId from TdZwyjBsnClusionRsn t order by  t.fkByBadrsnId.num,t.fkByBadrsnId.codeNo");
		this.badRsnList = new ArrayList<>();
		List<TsSimpleCode> list = commService.findByHql(hql.toString(), TsSimpleCode.class);
		List<TsSimpleCode> allList = commService.findSimpleCodesByTypeId("5007");
		Map<String,TsSimpleCode> map = new HashMap<>();
		if(!CollectionUtils.isEmpty(list)){
			for (TsSimpleCode code:list) {
				int i = code.getCodeLevelNo().indexOf(".");
				if(i>0){
					String substring = code.getCodeLevelNo().substring(0, i);
					if(!CollectionUtils.isEmpty(allList)) {
						for (TsSimpleCode all : allList) {
							if(map.get(substring)!=null){
								continue;
							}else
							if(null != all.getCodeLevelNo() && all.getCodeLevelNo().equals(substring)){
								map.put(all.getCodeLevelNo(),all);
							}
						}
					}
				}
			}
		}
		for (String key:map.keySet()) {
			TsSimpleCode value = map.get(key);
			badRsnList.add(value);
		}
		if(!CollectionUtils.isEmpty(list)){
			for (TsSimpleCode code:list ) {
				badRsnList.add(code);
			}
		}

	}
    /**
     * @Description : 级联刷新检查机构
     * @MethodAuthor: rcj
     * @Date : 2020/10/30 11:13
     **/
     public void onZoneSelect() {
         if (StringUtils.isBlank(this.searchZoneCode)) {
             return;
         }
 		searchUnitName = null;
         searchUnitId = null;
     }
     /**
      * @Description : 清空查询条件-用人单位地区
      * @MethodAuthor: rcj
      * @Date : 2020/10/30 11:15
      **/
      public void clearSearchCrptZone() {
          this.searchCrptZoneCode = null;
          this.searchCrptZoneName = null;
      }

  	/**
  	 * <p>方法描述：查询体检机构</p>
  	 * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
  	 * */
  	public void selUnitAction() {
  		Map<String, Object> options = MapUtils.produceDialogMap(null,625,null,500);

  		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
  		List<String> paramList = new ArrayList<String>();
  		paramList.add(searchUnitId);
  		paramMap.put("selectIds", paramList);
  		List<String> paramList2 = new ArrayList<String>();
  		paramList2.add(searchZoneCode);
  		paramMap.put("searchZoneCode", paramList2);
  		RequestContext requestContext = RequestContext.getCurrentInstance();
  		requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
  	}
  	/**
  	 * <p>方法描述：处理选择的体检机构</p>
  	 * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
  	 * */
  	public void onSelectUnitAction(SelectEvent event) {
  		Map<String, Object> selectedMap = (Map<String, Object>) event
  				.getObject();
  		if (null != selectedMap && selectedMap.size() > 0) {
  			List<TbTjSrvorg> list = (List<TbTjSrvorg>) selectedMap.get("selectPros");
  			if (null!=list && list.size()>0) {
  				StringBuffer names = new StringBuffer();
  				StringBuffer ids = new StringBuffer();
  				for (TbTjSrvorg t : list) {
  					names.append("，").append(t.getUnitName());
  					ids.append(",").append(t.getRid());
  				}
  				searchUnitId=ids.substring(1);
  				this.searchUnitName = names.substring(1);
  			}
  		}
  	}
  	/**
  	 * <p>方法描述：清空单位</p>
  	 * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
  	 * */
  	public void clearUnit() {
  		searchUnitId = null;
  		this.searchUnitName = null;
  	}
//	/**
//	 * <p>
//	 * 方法描述：初始化状态
//	 * </p>
//	 * 
//	 * @MethodAuthor rcj,2020年10月16日,initSearchStates
//	 * */
//	private void initSearchStates() {
//		// 1、区县审核，状态为：待审核、待复审、复审退回、复审通过、终审通过，默认（待审核、复审退回）
//		// 2、市级审核，状态为：待审核、已退回、待终审、终审退回、终审通过（待审核、终审退回）
//		// 3、省级审核，状态为：待审核、已退回、审核通过
//
//		this.stateList = new ArrayList<>();
//		if (zoneType == 2) {
//			this.states = new String[] {"5"};
//			this.stateList.add(new SelectItem("5", "待审核"));
//			this.stateList.add(new SelectItem("4", "已退回"));
//			this.stateList.add(new SelectItem("6", "审核通过"));
//		} else if (zoneType == 3) {
//			this.states = new String[] {"3","4"};
//			this.stateList.add(new SelectItem("3", "待审核"));
//			this.stateList.add(new SelectItem("2", "已退回"));
//			this.stateList.add(new SelectItem("5", "待终审"));
//			this.stateList.add(new SelectItem("4", "终审退回"));
//			this.stateList.add(new SelectItem("6", "终审通过"));
//		} else {
//			this.states = new String[] {"1","2"};
//			this.stateList.add(new SelectItem("1", "待审核"));
//			this.stateList.add(new SelectItem("3", "待复审"));
//			this.stateList.add(new SelectItem("2", "复审退回"));
//			this.stateList.add(new SelectItem("5", "复审通过"));
//			this.stateList.add(new SelectItem("6", "终审通过"));
//		}
//	}
	/**
 	 * <p>方法描述：计算超期</p>
 	 * @MethodAuthor rcj,2020年10月16日,calLimitTime
	 * */
	protected int calLimitTime(Date sDate) {
		if (null==sDate) {
			return -1;
		}
		//剩余天数
		int day = HolidayUtil.calRemainingDate(sDate, new Date(),limitTime);
		if (day == 0) {
			return -1;
		}
		return day;
	}


    /**
    * @Description : 打开批量审核确认框,判断是否存在未填写规范性审核的数据
    * @MethodAuthor: rcj
    * @Date : 2019/9/19 17:03
    **/
    public void openReviewConfirmDialog() {
        if(this.selectEntitys == null || this.selectEntitys.size()==0){
            JsfUtil.addErrorMessage("请选择需要审批的数据！");
            return ;
        }
        RequestContext.getCurrentInstance().execute("PF('ReviewConfirmDialog').show()");
    }

   

    /**
     * @Description : 执行查询
     * @MethodAuthor: rcj
     * @Date : 2020/10/30 10:23
     **/
     @Override
     public void searchAction() {
		if (!validateSearch()) {
			return;
		}
 		if(DateUtils.isDateAfter(searchBhkBdate, searchBhkEdate)){
 			JsfUtil.addErrorMessage("体检开始日期应小于等于体检结束日期！");
 			return;
 		}
        super.searchAction();
     }
    /**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2021年1月6日,validateSearch
     * */
     public boolean  validateSearch(){
    	 return true;
     }
    
    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder();
        sb.append(" select T.RID,  CASE WHEN T5.ZONE_TYPE > 2 THEN SUBSTR(T5.FULL_NAME, INSTR(T5.FULL_NAME, '_') + 1) ELSE T5.FULL_NAME END ZONE_NAME,");
        sb.append(" T2.UNIT_NAME, T.PERSON_NAME, T6.Crpt_Name, T8.CODE_NAME, listagg(T10.CODE_NAME, '，') within group( order by T10.CODE_NAME) as badrsn, T1.IF_NORMAL, to_char(T.BHK_DATE,'yyyy-mm-dd'),  nvl(to_char(T.DATA_UP_DATE, 'yyyy-mm-dd'), to_char(T.CREATE_DATE, 'yyyy-mm-dd'))");
		sb.append(" ,to_char(CASE WHEN(T1.STATE = 5 or T1.STATE = 2) and t1.city_rst is not null  THEN T1.CITY_SMT_DATE ");
		sb.append(" WHEN (T1.STATE = 3 and t5.if_city_direct is null) or(T1.STATE = 5 and t1.city_rst is  null) THEN T1.COUNTY_SMT_DATE WHEN T1.STATE = 1 OR T1.STATE IS NULL or (T1.STATE = 3 and t5.if_city_direct = 1) THEN nvl(T.DATA_UP_DATE,T.CREATE_DATE) ELSE T1.PRO_SMT_DATE END ,'yyyy-mm-dd') AS RCV_DATE");
        sb.append(", T1.STATE, T5.zone_gb,1 AS LIMIT_DAY,1 AS IFCHECK  ,t5.if_city_direct ");
        sb.append(" FROM TD_ZWYJ_BSN_BHK T ");
        sb.append(" LEFT JOIN TD_ZWYJ_BSN_CHECK T1 ON T1.MAIN_ID = T.RID ");
        sb.append(" LEFT JOIN TB_TJ_SRVORG T2 ON T.BHKORG_ID = T2.RID ");
        sb.append(" LEFT JOIN TS_ZONE T5 ON T2.ZONE_ID = T5.RID ");
        sb.append(" LEFT JOIN TB_TJ_CRPT T6 ON T.ENTRUST_CRPT_ID = T6.RID ");
        sb.append(" LEFT JOIN TS_SIMPLE_CODE T8 ON T8.RID = T.ONGUARD_STATEID ");
        sb.append(" LEFT JOIN TD_ZWYJ_BSN_BADRSNS T9 ON T9.BHK_ID = T.RID ");
        sb.append(" LEFT JOIN TS_SIMPLE_CODE T10 ON T10.RID = T9.BADRSN_ID ");

        sb.append(" WHERE 1=1 ");
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sb.append(" AND T5.ZONE_GB LIKE :zonecode escape '\\\'");
            this.paramMap.put("zonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        // 体检机构
        if (null != this.searchUnitId) {
            sb.append(" AND T2.RID in ( ").append(this.searchUnitId).append(")");
        }
        //企业名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sb.append(" AND T6.CRPT_NAME LIKE :crptName  escape '\\\'");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
		//人员姓名
		if (StringUtils.isNotBlank(this.searchPersonName)) {
			sb.append(" AND T.PERSON_NAME LIKE :personName  escape '\\\'");
			this.paramMap.put("personName", "%" + StringUtils.convertBFH(this.searchPersonName.trim()) + "%");
		}
        //人员身份证
        if (StringUtils.isNotBlank(this.searchPersonIdc)) {
            sb.append(" AND T.IDC  = :personIdc ");
            this.paramMap.put("personIdc", StringUtils.convertBFH(this.searchPersonIdc.trim()));
        }
        
		// 体检日期
		if (null != this.searchBhkBdate) {
			sb.append(" AND T.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchBhkBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
		}
		if (null != this.searchBhkEdate) {
			sb.append(" AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchBhkEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
		}
		//在岗状态
		if(StringUtils.isNotBlank(selectOnGuardIds)){
			sb.append(" AND T8.RID in (").append(this.selectOnGuardIds).append(")");
		}
		//危害因素
		if(StringUtils.isNotBlank(selectBadRsnIds)){
			sb.append(" AND T10.RID in (").append(this.selectBadRsnIds).append(")");
		}
		// 是否结论异常
		if(null != concluStates && concluStates.length > 0){
			sb.append(" AND T1.IF_NORMAL in (").append(Arrays.toString(this.concluStates).replace("[","").replace("]","")).append(")");
		}
        sb.append(getOtherSearchCondition());
        sb.append(" group by T.RID,T5.zone_gb,CASE WHEN T5.ZONE_TYPE > 2 THEN SUBSTR(T5.FULL_NAME, INSTR(T5.FULL_NAME, '_') + 1) ELSE T5.FULL_NAME END ,T2.UNIT_NAME,T.PERSON_NAME,T6.Crpt_Name,T8.CODE_NAME,T1.IF_NORMAL,to_char(T.BHK_DATE,'yyyy-mm-dd'),nvl(to_char(T.DATA_UP_DATE, 'yyyy-mm-dd'),to_char(T.CREATE_DATE, 'yyyy-mm-dd')),T1.STATE, 1,1,'','' ");
		sb.append(" , to_char(CASE WHEN (T1.STATE = 5 or T1.STATE = 2) and t1.city_rst is not null THEN T1.CITY_SMT_DATE ");
		sb.append(" WHEN (T1.STATE = 3 and t5.if_city_direct is null) or(T1.STATE = 5 and t1.city_rst is  null) THEN T1.COUNTY_SMT_DATE WHEN T1.STATE = 1 OR T1.STATE IS NULL or (T1.STATE = 3 and t5.if_city_direct = 1) THEN nvl(T.DATA_UP_DATE,T.CREATE_DATE) ELSE T1.PRO_SMT_DATE END ,'yyyy-mm-dd') ,t5.if_city_direct ");
        String h2 = "SELECT COUNT(*) FROM (" + sb.toString() + ")";
        String h1 = "SELECT * FROM (" + sb.toString() + ")AA  ORDER BY "+("0".equals(getType())?"AA.RCV_DATE,":"")+"AA.ZONE_GB, AA.UNIT_NAME, AA.PERSON_NAME";
        if ("0".equals(getType())) {
			
		}
        return new String[] { h1, h2 };
    }
    /**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2021年1月5日,getSearchCondition
     * */
    public String getOtherSearchCondition(){
    	return "";
    }

    @Override
    public void modInit() {
		tdZwyjBsnBhk = cardServiceImpl.find(TdZwyjBsnBhk.class,rid);
		if (null==tdZwyjBsnBhk) {
			tdZwyjBsnBhk = new TdZwyjBsnBhk();
		}
		TsZone zone = tdZwyjBsnBhk.getFkByBhkorgId().getTsZone();
		if("1".equals(zone.getIfCityDirect())){
			ifCityDirect = true;
		}else{
			ifCityDirect = false;
		}
		this.getBhkInfo();
		
		newFlow = cardCheckServiceImpl.searchTdZwyjBsnCheckByMainId(rid);
		if(null == newFlow){
			newFlow = new TdZwyjBsnCheck();
			newFlow.setFkByMainId(tdZwyjBsnBhk);
		}

		initZdzybInfo();
		initAbnormal();
		
		RequestContext.getCurrentInstance().scrollTo("tabView:viewForm:editTitleGrid");
//		modSubInit();
    }
//    /**
// 	 * <p>方法描述：子级特性修改</p>
// 	 * @MethodAuthor qrr,2021年1月6日,modSubInit
//     * */
//    public void modSubInit(){
//    	
//    }
    
    /**
 	 * <p>方法描述：获取体检详情信息</p>
 	 * @MethodAuthor rcj,2019年10月12日,getBhkInfo
	 * */
	private void getBhkInfo() {
		Integer bhkId =cardServiceImpl.findBhkId(tdZwyjBsnBhk.getFkByBhkorgId().getRid(),tdZwyjBsnBhk.getBhkCode());
        tjBhkInfoBean = new TdTjBhkInfoBean();

		if(null != bhkId){
			tjBhkInfoBean.setIfShowBaseOnly(false);
			ifBhkInfoExist = true;
            tjBhkInfoBean.setRid(bhkId);
            tjBhkInfoBean.setArchivePanel(archivePanel);
            tjBhkInfoBean.setIfManagedOrg(true);
            tjBhkInfoBean.initBhkInfo();
		}else{
			tjBhkInfoBean.setIfShowBaseOnly(true);
			ifBhkInfoExist = false;
			TdTjBhk tdTjBhkShow = new TdTjBhk();
			tdTjBhkShow.setBhkCode(tdZwyjBsnBhk.getBhkCode());
			tdTjBhkShow.setTbTjSrvorg(tdZwyjBsnBhk.getFkByBhkorgId());
			tdTjBhkShow.setTbTjCrpt(tdZwyjBsnBhk.getFkByCrptId());
			tdTjBhkShow.setFkByEntrustCrptId(tdZwyjBsnBhk.getFkByEntrustCrptId());
			tdTjBhkShow.setCrptName(tdZwyjBsnBhk.getCrptName());
			tdTjBhkShow.setPersonName(tdZwyjBsnBhk.getPersonName());
			tdTjBhkShow.setSex(tdZwyjBsnBhk.getSex());
			tdTjBhkShow.setFkByCardTypeId(tdZwyjBsnBhk.getFkByCardTypeId());
			tdTjBhkShow.setIdc(tdZwyjBsnBhk.getIdc());
			tdTjBhkShow.setPsnType(tdZwyjBsnBhk.getPsnType());
			tdTjBhkShow.setBrth(tdZwyjBsnBhk.getBrth());
			tdTjBhkShow.setAge(tdZwyjBsnBhk.getAge());
			tdTjBhkShow.setIsxmrd(tdZwyjBsnBhk.getIsxmrd());
			tdTjBhkShow.setDpt(tdZwyjBsnBhk.getDpt());
			tdTjBhkShow.setTsSimpleCode(tdZwyjBsnBhk.getFkByOnguardStateid());
			tdTjBhkShow.setBhkType(tdZwyjBsnBhk.getBhkType());
			tdTjBhkShow.setBhkDate(tdZwyjBsnBhk.getBhkDate());
			tdTjBhkShow.setWrklnt(null==tdZwyjBsnBhk.getWrklnt()?null:Double.valueOf(tdZwyjBsnBhk.getWrklnt().toString()));
			tdTjBhkShow.setWrklntmonth(tdZwyjBsnBhk.getWrklntmonth());
			tdTjBhkShow.setTchbadrsntim(null==tdZwyjBsnBhk.getTchbadrsntim()?null:Double.valueOf(tdZwyjBsnBhk.getTchbadrsntim().toString()));
			tdTjBhkShow.setTchbadrsnmonth(tdZwyjBsnBhk.getTchbadrsnmonth());
			tdTjBhkShow.setWorkName(tdZwyjBsnBhk.getWorkName());
			tdTjBhkShow.setBadrsn(tdZwyjBsnBhk.getBadrsn());
			tdTjBhkShow.setBhkrst(tdZwyjBsnBhk.getBhkrst());
			tdTjBhkShow.setMhkadv(tdZwyjBsnBhk.getMhkadv());
            List<TdTjMhkrst> tdTjMhkrsts = new ArrayList<>();
            TdTjMhkrst mhkrst = new TdTjMhkrst();
			TsSimpleCode code = new TsSimpleCode();
			code.setCodeDesc(tdZwyjBsnBhk.getOcpBhkrstdes());
            mhkrst.setTsSimpleCode(code);
            tdTjMhkrsts.add(mhkrst);
            tdTjBhkShow.setTdTjMhkrsts(tdTjMhkrsts);
			// 隐藏身份证号和出生日期
			if(StringUtils.isNotBlank(tdTjBhkShow.getIdc())) {
				tdTjBhkShow.setIdc(StringUtils.encryptIdc(tdTjBhkShow.getIdc()));
			}
		//用人单位
			boolean flag = tdZwyjBsnBhk.getFkByCrptId()!=null&& tdZwyjBsnBhk.getFkByCrptId().getRid()!=null
					&&tdZwyjBsnBhk.getFkByBhkorgId()!=null && tdZwyjBsnBhk.getFkByBhkorgId().getTsUnit()!=null
					&& tdZwyjBsnBhk.getFkByBhkorgId().getTsUnit().getRid()!=null;
		if(flag){
			TbTjCrptIndepend tbTjCrptIndepend = this.hethStaQueryServiceImpl.findTbTjCrptIndependByBusType(1, tdZwyjBsnBhk.getFkByCrptId().getRid(), tdZwyjBsnBhk.getFkByBhkorgId().getTsUnit().getRid());
			if(tbTjCrptIndepend!=null){
				tbTjCrptIndepend.setLinkphone2(StringUtils.encryptPhone(tbTjCrptIndepend.getLinkphone2()));
				tjBhkInfoBean.setTbTjCrptIndepend(tbTjCrptIndepend);
			}
		}
		//用工单位
			flag = tdZwyjBsnBhk.getFkByEntrustCrptId()!=null && tdZwyjBsnBhk.getFkByEntrustCrptId().getRid()!=null
			&& tdZwyjBsnBhk.getFkByBhkorgId()!=null && tdZwyjBsnBhk.getFkByBhkorgId().getTsUnit()!=null
					&& tdZwyjBsnBhk.getFkByBhkorgId().getTsUnit().getRid()!=null;
		if(flag){
			TbTjCrptIndepend tbTjCrptIndependEntrust = this.hethStaQueryServiceImpl.findTbTjCrptIndependByBusType(1, tdZwyjBsnBhk.getFkByEntrustCrptId().getRid(), tdZwyjBsnBhk.getFkByBhkorgId().getTsUnit().getRid());
			if(tbTjCrptIndependEntrust!=null){
				tbTjCrptIndependEntrust.setLinkphone2(StringUtils.encryptPhone(tbTjCrptIndependEntrust.getLinkphone2()));
				tjBhkInfoBean.setTbTjCrptIndependEntrust(tbTjCrptIndependEntrust);
			}
		}
            tjBhkInfoBean.setTdTjBhkShow(tdTjBhkShow);
			FacesContext.getCurrentInstance().renderResponse();
		}
	}

    /**
     * @MethodName: initZdzybInfo
     * @Description: 封装重点职业病信息
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-11-02
    **/
    private void initZdzybInfo(){
		zdzybBadRsnInfoList = new ArrayList<>();
		Map<Integer,List<Object[]>> map = new HashMap<>();
		Map<Integer,Integer> rowsMap = new HashMap<>();//跨行
		List<Object[]> list =cardServiceImpl.getSelectItemList(rid);
		if(!CollectionUtils.isEmpty(list)){
			for(Object[] orr:list){
				Integer badRid = Integer.parseInt(null==orr[0]?"":orr[0].toString());
				Integer pdWay = Integer.parseInt(null==orr[8]?"":orr[8].toString());
				if(pdWay==1){
					if(rowsMap.containsKey(badRid)){
						Integer a = rowsMap.get(badRid);
						a++;
						rowsMap.put(badRid,a);
					}else{
						rowsMap.put(badRid,1);
					}
				}
				if(map.containsKey(badRid)){
					List<Object[]> list1 = map.get(badRid);
					list1.add(orr);
				}else{
					List<Object[]> list1 = new ArrayList<>();
					list1.add(orr);
					map.put(badRid,list1);
				}
			}

			//不同危害因素封装
			for(Integer badRid:map.keySet()){
				List<Object[]> list1 = map.get(badRid);
				ZdzybBadRsnInfo badRsnInfo = new ZdzybBadRsnInfo();
				badRsnInfo.setBadName(null==list1.get(0)[1]?"":list1.get(0)[1].toString());
				badRsnInfo.setFinalConslusion(null==list1.get(0)[9]?"":list1.get(0)[9].toString());

				List<ZdzybItemRst> zdzybItemRstList = new ArrayList<>();
				for(Object[] orr:list1){
					ZdzybItemRst itemRst = new ZdzybItemRst();
					itemRst.setItemName(null==orr[2]?"":orr[2].toString());
					itemRst.setResult(null==orr[3]?"":orr[3].toString());
					itemRst.setStdValue(null==orr[4]?"":orr[4].toString());
					itemRst.setMsrunt(null==orr[5]?"":orr[5].toString());
					itemRst.setPdStd(null==orr[6]?"":orr[6].toString());
					itemRst.setConslusion(null==orr[7]?"":orr[7].toString());
					Integer pdWay = Integer.parseInt(null==orr[8]?"":orr[8].toString());
					if(pdWay ==1){
						itemRst.setIndex(rowsMap.get(badRid));
						rowsMap.put(badRid,0);
					}else{
						itemRst.setIndex(1);
					}
					zdzybItemRstList.add(itemRst);
				}
				badRsnInfo.setZdzybItemRstList(zdzybItemRstList);
				zdzybBadRsnInfoList.add(badRsnInfo);
			}
		}
		initAbnormal();
	}

	/**是否存在异常情况*/
    private void initAbnormal(){
		List<Object[]> list =cardServiceImpl.getAbnormalInfoList(rid);
		if(!CollectionUtils.isEmpty(list)){
			ifAbnormal = true;
			abnormalInfo ="在岗状态为"+tdZwyjBsnBhk.getFkByOnguardStateid().getCodeName()+"，检查结论不应该出现";
			for(Object[] orr:list){
				if(null != orr[1]){
					abnormalInfo +=orr[1].toString()+"、";
				}
			}
			abnormalInfo = abnormalInfo.substring(0,abnormalInfo.length()-1)+"。";

		}else{
			ifAbnormal = false;
		}
	}
    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {
    	
    }
    @Override
	public void saveAction() {
		// TODO Auto-generated method stub
		
	}
    /**
 	 * <p>方法描述：获取类型，0审核1查询</p>
 	 * @MethodAuthor qrr,2021年1月7日,getType
     * */
    public abstract String getType();

	public String getBackRsn() {
		return backRsn;
	}
	public void setBackRsn(String backRsn) {
		this.backRsn = backRsn;
	}
	public Integer getRid() {
		return rid;
	}
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	public Boolean getReadOnly() {
		return readOnly;
	}
	public void setReadOnly(Boolean readOnly) {
		this.readOnly = readOnly;
	}

	public String[] getStates() {
		return states;
	}

	public void setStates(String[] states) {
		this.states = states;
	}

	public List<SelectItem> getStateList() {
		return stateList;
	}

	public void setStateList(List<SelectItem> stateList) {
		this.stateList = stateList;
	}

    public List<Object[]> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<Object[]> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }
	public Boolean getIfShowWarn() {
		return ifShowWarn;
	}
	public void setIfShowWarn(Boolean ifShowWarn) {
		this.ifShowWarn = ifShowWarn;
	}

	public TdZwyjBsnCheck getNewFlow() {
		return newFlow;
	}

	public void setNewFlow(TdZwyjBsnCheck newFlow) {
		this.newFlow = newFlow;
	}

	public boolean isIfCityDirect() {
		return ifCityDirect;
	}

	public void setIfCityDirect(boolean ifCityDirect) {
		this.ifCityDirect = ifCityDirect;
	}
	public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

	public String getSearchUnitId() {
		return searchUnitId;
	}

	public void setSearchUnitId(String searchUnitId) {
		this.searchUnitId = searchUnitId;
	}

	public String getSearchUnitName() {
		return searchUnitName;
	}

	public void setSearchUnitName(String searchUnitName) {
		this.searchUnitName = searchUnitName;
	}


	public String getSearchCrptZoneCode() {
        return searchCrptZoneCode;
    }

    public void setSearchCrptZoneCode(String searchCrptZoneCode) {
        this.searchCrptZoneCode = searchCrptZoneCode;
    }

    public String getSearchCrptZoneName() {
        return searchCrptZoneName;
    }

    public void setSearchCrptZoneName(String searchCrptZoneName) {
        this.searchCrptZoneName = searchCrptZoneName;
    }

    public List<TsZone> getCrptZoneList() {
        return crptZoneList;
    }

    public void setCrptZoneList(List<TsZone> crptZoneList) {
        this.crptZoneList = crptZoneList;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchPersonName() {
        return searchPersonName;
    }

    public void setSearchPersonName(String searchPersonName) {
        this.searchPersonName = searchPersonName;
    }
    public TdZwyjBsnBhk getTdZwyjBsnBhk() {
		return tdZwyjBsnBhk;
	}

	public void setTdZwyjBsnBhk(TdZwyjBsnBhk tdZwyjBsnBhk) {
		this.tdZwyjBsnBhk = tdZwyjBsnBhk;
	}
	public List<TsSimpleCode> getOnGuardList() {
		return onGuardList;
	}

	public void setOnGuardList(List<TsSimpleCode> onGuardList) {
		this.onGuardList = onGuardList;
	}

	public String getSelectOnGuardNames() {
		return selectOnGuardNames;
	}

	public void setSelectOnGuardNames(String selectOnGuardNames) {
		this.selectOnGuardNames = selectOnGuardNames;
	}

	public String getSelectOnGuardIds() {
		return selectOnGuardIds;
	}

	public void setSelectOnGuardIds(String selectOnGuardIds) {
		this.selectOnGuardIds = selectOnGuardIds;
	}

	public List<TsSimpleCode> getBadRsnList() {
		return badRsnList;
	}

	public void setBadRsnList(List<TsSimpleCode> badRsnList) {
		this.badRsnList = badRsnList;
	}

	public String getSelectBadRsnNames() {
		return selectBadRsnNames;
	}

	public void setSelectBadRsnNames(String selectBadRsnNames) {
		this.selectBadRsnNames = selectBadRsnNames;
	}

	public String getSelectBadRsnIds() {
		return selectBadRsnIds;
	}

	public void setSelectBadRsnIds(String selectBadRsnIds) {
		this.selectBadRsnIds = selectBadRsnIds;
	}

	public String getSearchPersonIdc() {
		return searchPersonIdc;
	}

	public void setSearchPersonIdc(String searchPersonIdc) {
		this.searchPersonIdc = searchPersonIdc;
	}

	public Date getSearchBhkBdate() {
		return searchBhkBdate;
	}

	public void setSearchBhkBdate(Date searchBhkBdate) {
		this.searchBhkBdate = searchBhkBdate;
	}

	public Date getSearchBhkEdate() {
		return searchBhkEdate;
	}

	public void setSearchBhkEdate(Date searchBhkEdate) {
		this.searchBhkEdate = searchBhkEdate;
	}

	public String[] getConcluStates() {
		return concluStates;
	}

	public void setConcluStates(String[] concluStates) {
		this.concluStates = concluStates;
	}
	public List<ZdzybBadRsnInfo> getZdzybBadRsnInfoList() {
		return zdzybBadRsnInfoList;
	}

	public void setZdzybBadRsnInfoList(List<ZdzybBadRsnInfo> zdzybBadRsnInfoList) {
		this.zdzybBadRsnInfoList = zdzybBadRsnInfoList;
	}


	public boolean isIfAbnormal() {
		return ifAbnormal;
	}

	public void setIfAbnormal(boolean ifAbnormal) {
		this.ifAbnormal = ifAbnormal;
	}

	public String getAbnormalInfo() {
		return abnormalInfo;
	}

	public void setAbnormalInfo(String abnormalInfo) {
		this.abnormalInfo = abnormalInfo;
	}

	public boolean isIfBhkInfoExist() {
		return ifBhkInfoExist;
	}

	public void setIfBhkInfoExist(boolean ifBhkInfoExist) {
		this.ifBhkInfoExist = ifBhkInfoExist;
	}

    public TdTjBhkInfoBean getTjBhkInfoBean() {
        return tjBhkInfoBean;
    }

    public void setTjBhkInfoBean(TdTjBhkInfoBean tjBhkInfoBean) {
        this.tjBhkInfoBean = tjBhkInfoBean;
    }
    public OutputPanel getArchivePanel() {
		return archivePanel;
	}
	public void setArchivePanel(OutputPanel archivePanel) {
		this.archivePanel = archivePanel;
	}
}
