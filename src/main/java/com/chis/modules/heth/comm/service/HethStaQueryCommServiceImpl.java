package com.chis.modules.heth.comm.service;

import java.text.SimpleDateFormat;
import java.util.*;

import javax.persistence.Query;
import javax.persistence.TypedQuery;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.system.entity.TdTjExport;
import com.chis.modules.system.utils.Global;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.modules.heth.comm.logic.TjPersonSearchConditionPO;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.service.CommServiceImpl;

/**
 * <p>
 * 类描述：职业病查询统计统一业务层
 * </p>
 *
 * @ClassAuthor rcj, 2019-11-23, HethAnalyStaQueryServiceImpl
 */
@Service
@Transactional(readOnly = true)
public class HethStaQueryCommServiceImpl extends AbstractTemplate {
	@Autowired
	private CommServiceImpl commService;
    /**
     * <p>
     * 方法描述：根据sql查询
     * </p>
     *
     * @MethodAuthor mxp, 2018-05-02,findBySql
     */
    public List findBySql(String sql) {
        return em.createNativeQuery(sql).getResultList();
    }

	@Transactional(readOnly = false)
    public <T> void updateEntityBatch(List<T> list){
    	this.updateBatchObjs(list);
	}

    /**
     * <p>
     * 方法描述：根据查询条件查询第一条数据
     * </p>
     *
     * @MethodAuthor mxp, 2018-04-27,findOneByHql
     */
    @Override
	public <T> T findOneByHql(String hql, Class<T> t) {
        TypedQuery<T> query = em.createQuery(hql, t).setMaxResults(1);
        List<T> resultList = query.getResultList();
        if (resultList != null && resultList.size() > 0) {
            return resultList.get(0);
        }
        return null;
    }


    /**
     * <p>
     * 方法描述：保存
     * </p>
     *
     * @MethodAuthor mxp, 2018-04-27,saveEntity
     */
    @Transactional(readOnly = false)
    public <T> void saveEntity(T t) {
        super.save(t);
    }

    /**
     * <p>
     * 方法描述：更新
     * </p>
     *
     * @MethodAuthor mxp, 2018-04-27,updateEntity
     */
    @Transactional(readOnly = false)
    public <T> void updateEntity(T t) {
        super.update(t);
    }

    /**
     * <p>
     * 方法描述：删除
     * </p>
     *
     * @MethodAuthor mxp, 2018-04-27,deleteEntity
     */
    @Override
	@Transactional(readOnly = false)
    public <T> void deleteEntity(T t) {
        super.delete(t);
    }

    /**
     * <p>
     * 方法描述：删除
     * </p>
     *
     * @MethodAuthor mxp, 2018-04-27,deleteEntity
     */
    @Override
	@Transactional(readOnly = false)
    public <T> void deleteEntity(Class<T> entityClass, Integer rid) {
        super.delete(entityClass, rid);
    }





    /**
     * <p>
     * 方法描述：获取数量
     * </p>
     *
     * @MethodAuthor mxp, 2018-04-28,findCountBySql
     */
    @Override
	public int findCountBySql(String sql) {
        List<Object> resultList = super.em.createNativeQuery(sql)
                .getResultList();
        if (resultList != null && resultList.size() > 0) {
            return Integer.valueOf(resultList.get(0).toString());
        }

        return 0;

    }
	
	
	    /**
	 	 * <p>方法描述：获取实体对象</p>
	 	 * @MethodAuthor qrr,2018年5月4日,findEntityByMainId
	     */
	    @Override
		@Transactional(readOnly = true)
	    public <T> T  findEntityByMainId(Class<T> t,Integer mainId){
	    	StringBuffer sb = new StringBuffer();
	    	sb.append(" SELECT T FROM ").append(t.getSimpleName()).append(" T WHERE T.fkByMainId.rid = '").append(mainId).append("'");
	    	return findOneByHql(sb.toString(), t);
	    }
	    /**
	 	 * <p>方法描述：获取实体对象集合</p>
	     * @param <T>
	 	 * @MethodAuthor qrr,2018年5月4日,findEntityByMainId
	     */
	    @Override
		@Transactional(readOnly = true)
	    public <T> List<T> findEntityListByMainId(Class<T> t,Integer mainId){
	    	StringBuffer sb = new StringBuffer();
	    	sb.append(" SELECT T FROM ").append(t.getSimpleName()).append(" T WHERE T.fkByMainId.rid = '").append(mainId).append("'");
	    	return	findByHql(sb.toString(), t);
	    }
	    
    /**
     * <p>方法描述：查询经济类型</p>
 	 * 
 	 * @MethodAuthor rcj,2018年5月10日,findTbTjCrptByMainId
     * @return
     */
    public TsSimpleCode findTsSimpleCode(Integer crptId , String codeTypeName) {
		StringBuffer sb = new StringBuffer();
		sb.append("SELECT t FROM TbTjCrpt t INNER JOIN t.tsSimpleCodeByEconomyId t1  where t.rid = '").append(crptId).append("'");
    	sb.append(" and t1.tsCodeType.codeTypeName = ' ").append(codeTypeName).append("'");
    	List<TsSimpleCode> resultList = findByHql(sb.toString(), TsSimpleCode.class);
    	if(null != resultList && resultList.size()>0){
    		return resultList.get(0);
    	}
    	return null;
	}
   
    /**
	 * 查找职业健康检查结论按地区分析定时统计表中最小和最大年份
	 * 
	 * @return 最小和最大年份
	 * <AUTHOR>
	 * @createDate 2014-9-25
	 */

	public List<Object[]> findMinYearOfConclusin() {
		String sql = "SELECT MIN(T.BHK_YEAR),MAX(T.BHK_YEAR) FROM TD_CLUSION_ZONE_TIME T";
		return em.createNativeQuery(sql).getResultList();
	}
    
	/**
	 * 职业病危害分布分析查询sql（小类）
	 *
	 * @param zoneCode
	 *            地区编码
	 * @param bigHarmCode
	 *            危害因素编码
	 * @param onguardCode
	 *            在岗状态编码
	 * @param analyseType
	 * @param bigHarmType
	 * @param firstLevelNoSet
	 * @return 各个地区的数据信息
	 * <AUTHOR>
	 * @history 2019年11月29日
	 */
	public List<Object[]> searchTdOccBadInfo(String zoneCode, Date searchStartDate, Date searchEndDate, String bigHarmCode, String onguardCode, Integer analyseType, boolean bigHarmType, Set<String> firstLevelNoSet) {
		if (StringUtils.isNotBlank(zoneCode)) {
			if(bigHarmType){
				return searchTdOccBadInfoByBigHarmType(zoneCode,
						searchStartDate,searchEndDate,bigHarmCode, onguardCode,analyseType,bigHarmType,firstLevelNoSet);
			}
			StringBuffer sql1 = new StringBuffer();
			sql1.append(" T.BHK_DATE >=to_date('").append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
			sql1.append(" AND T.BHK_DATE <=to_date('").append(DateUtils.formatDate(searchEndDate, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
			sql1.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
			if (StringUtils.isNotBlank(onguardCode)) {
				sql1.append(" AND T.ONGUARD_STATEID IN (").append(onguardCode).append(") ");
			}
			Integer zoneCodeLevel = Integer.valueOf(ZoneUtil.getZoneType(zoneCode));

			//区级统计本级和下级
			if(zoneCodeLevel == 4){
				zoneCodeLevel = 3;
			}
			
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT A.ZONE_NAME,C.*");
			if (StringUtils.isNotBlank(bigHarmCode)) {
				String[] split = bigHarmCode.split(",");
				for (int i = 0; i < split.length; i++) {
					sql.append(",B.S").append(i).append("1,B.S").append(i).append("2");
				}
			}
			sql.append(" FROM ( ");
			sql.append(" SELECT SUBSTR(T.ZONE_GB,0,").append(zoneCodeLevel * 2).append(") AS ZONE_GB,T.ZONE_NAME FROM TS_ZONE T ");
			sql.append(" WHERE T.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
			if(zoneCodeLevel == 2){
				sql.append(" AND T.ZONE_TYPE = 3 ");
			}else if(zoneCodeLevel >2) {
				sql.append(" AND T.ZONE_TYPE IN　(4)");
			}
			sql.append(" AND T.IF_REVEAL = 1 ");

			sql.append(" ORDER BY T.ZONE_GB ");
			sql.append(" ) A ");
			//C
			sql.append(" LEFT JOIN ( SELECT SUBSTR(T2.ZONE_GB,0,").append(zoneCodeLevel * 2).append(") ZONE_GB,'' PH,COUNT(DISTINCT T1.rid) SJSUM1");
			if(analyseType!=null&&analyseType ==1){//人数
				sql.append(", count(distinct T.PERSON_ID) SJSUM2 FROM TD_TJ_BHK T");
			}else{
				sql.append(",count(DISTINCT T.RID) SJSUM2 FROM TD_TJ_BHK T");
			}
			sql.append(" LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.CRPT_ID");
			sql.append(" LEFT JOIN TS_ZONE T2 ON T2.RID = T1.ZONE_ID");
			sql.append(" LEFT JOIN TD_TJ_BADRSNS T3 ON T3.BHK_ID = T.RID");
			sql.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T4.RID = T3.BADRSN_ID");
			sql.append(" WHERE ");
			sql.append(sql1);
			if(StringUtils.isNotBlank(bigHarmCode)){
				sql.append(" AND T4.RID  IN (").append(bigHarmCode).append(") ");
			}
			sql.append(" GROUP BY SUBSTR(T2.ZONE_GB,0,").append(zoneCodeLevel * 2).append(")) C ON C.ZONE_GB = A.ZONE_GB");

			sql.append(" LEFT JOIN (  ");
			sql.append(" SELECT SUBSTR(M.ZONE_GB,0,").append(zoneCodeLevel * 2).append(") AS ZONE_GB");

			if (StringUtils.isNotBlank(bigHarmCode)) {
				String[] split = bigHarmCode.split(",");
				for (int i = 0; i < split.length; i++) {
					sql.append("  ,SUM(DECODE(M.RID,'").append(split[i]).append("',M.CNUM,0)) S").append(i).append("1");
					sql.append("  ,SUM(DECODE(M.RID,'").append(split[i]).append("',M.PERNUM,0)) S").append(i).append("2");
				}
			}
			sql.append(" FROM (SELECT SUBSTR(T2.ZONE_GB,0,").append(zoneCodeLevel * 2).append(") AS ZONE_GB,T4.RID,count(DISTINCT T1.RID) CNUM");
			if(analyseType != null && analyseType == 1){//按人数
				sql.append(",count(distinct T.PERSON_ID) PERNUM  ");
			}else{//按人次数
				sql.append(",count(DISTINCT T.RID) PERNUM  ");
			}
			
			sql.append(" FROM TD_TJ_BHK T LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.CRPT_ID");
			sql.append(" LEFT JOIN TS_ZONE T2 ON T2.RID = T1.ZONE_ID");
			sql.append(" LEFT JOIN TD_TJ_BADRSNS T3 ON T3.BHK_ID = T.RID");
			sql.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T4.RID = T3.BADRSN_ID");
			sql.append(" WHERE ");
			sql.append(sql1);
			sql.append(" GROUP BY  SUBSTR(T2.ZONE_GB,0,").append(zoneCodeLevel * 2).append("),T4.RID ) M GROUP BY SUBSTR(M.ZONE_GB,0,").append(zoneCodeLevel * 2).append(") ");
			sql.append(" ) B ON A.ZONE_GB = B.ZONE_GB ");
			return em.createNativeQuery(sql.toString()).getResultList();
		}
		return null;
	}
	
	
	/**
	 * 职业病危害分布分析查询sql（大类）
	 *
	 * @param zoneCode
	 *            地区编码
	 * @param bigHarmCode
	 *            危害因素编码
	 * @param onguardCode
	 *            在岗状态编码
	 * @param analyseType
	 * @param bigHarmType
	 * @param firstLevelNoSet
	 * @return 各个地区的数据信息
	 * <AUTHOR>
	 * @history 2019年11月29日
	 */
	private List<Object[]> searchTdOccBadInfoByBigHarmType(String zoneCode, Date searchStartDate, Date searchEndDate, String bigHarmCode, String onguardCode, Integer analyseType, boolean bigHarmType, Set<String> firstLevelNoSet) {
		if(firstLevelNoSet==null&&firstLevelNoSet.size()==0){
			return null;
		}
		StringBuffer sql1 = new StringBuffer();
		sql1.append(" T.BHK_DATE >=to_date('").append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
		sql1.append(" AND T.BHK_DATE <=to_date('").append(DateUtils.formatDate(searchEndDate, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
		sql1.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		if (StringUtils.isNotBlank(onguardCode)) {
			sql1.append(" AND T.ONGUARD_STATEID IN (").append(onguardCode).append(") ");
		}
		Integer zoneCodeLevel = Integer.valueOf(ZoneUtil.getZoneType(zoneCode));
		List<String> bigHarmCodes =  new ArrayList<>(firstLevelNoSet);

		//区级统计本级和下级
		if(zoneCodeLevel == 4){
			zoneCodeLevel = 3;
		}
		
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT A.ZONE_NAME,B.* FROM ( ");
		sql.append(" SELECT SUBSTR(T.ZONE_GB,0,").append(zoneCodeLevel * 2).append(") AS ZONE_GB ,T.ZONE_NAME FROM TS_ZONE T ");
		sql.append(" WHERE T.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");

		if(zoneCodeLevel == 2){
			sql.append(" AND T.ZONE_TYPE = 3 ");
		}else if(zoneCodeLevel >2) {
			sql.append(" AND T.ZONE_TYPE IN　(4)");
		}
		
		sql.append(" AND T.IF_REVEAL = 1 ");
		sql.append(" ORDER BY T.ZONE_GB ) A ");
		sql.append(" LEFT JOIN  ");
		sql.append(" (SELECT R0.ZONE_GB,'' PH,SJSUM1,SJSUM2");
		for (int i = 0; i < bigHarmCodes.size(); i++) {
			sql.append(",S").append(i).append("1,S").append(i).append("2");
		}
		sql.append(" FROM (  ");
		sql.append(" SELECT distinct SUBSTR(T2.ZONE_GB,0,").append(zoneCodeLevel * 2).append(") ZONE_GB,COUNT(DISTINCT T1.rid) SJSUM1");
		if(analyseType!=null&&analyseType ==1){//人数
			sql.append(", count(distinct T.PERSON_ID) SJSUM2 FROM TD_TJ_BHK T");
		}else{
			sql.append(",count(distinct T.RID) SJSUM2 FROM TD_TJ_BHK T");
		}
		sql.append(" LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.CRPT_ID");
		sql.append(" LEFT JOIN TS_ZONE T2 ON T2.RID = T1.ZONE_ID");
		sql.append(" LEFT JOIN TD_TJ_BADRSNS T3 ON T3.BHK_ID = T.RID");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T4.RID = T3.BADRSN_ID");
		sql.append(" WHERE ");
		sql.append(sql1);
		sql.append("   GROUP BY SUBSTR(T2.ZONE_GB,0,").append(zoneCodeLevel * 2).append(")) R0");

		for (int i = 0; i < bigHarmCodes.size(); i++) {
			sql.append(" LEFT JOIN ");
			sql.append(" (SELECT SUBSTR(T2.ZONE_GB,0,").append(zoneCodeLevel * 2).append(") AS ZONE_GB,COUNT(DISTINCT T1.rid) S").append(i);
			if(analyseType!=null&&analyseType ==1){//人数
				sql.append("1,count(distinct T.PERSON_ID) S"+i+"2");
			}else{
				sql.append("1,count(distinct T.RID) S"+i+"2");
			}
			sql.append(" FROM TD_TJ_BHK T");
			sql.append(" LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.CRPT_ID");
			sql.append(" LEFT JOIN TS_ZONE T2 ON T2.RID = T1.ZONE_ID");
			sql.append(" LEFT JOIN TD_TJ_BADRSNS T3 ON T3.BHK_ID = T.RID");
			sql.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T4.RID = T3.BADRSN_ID");
			sql.append(" WHERE");
			sql.append(sql1);
			sql.append(" AND T4.CODE_LEVEL_NO LIKE '").append(bigHarmCodes.get(i)).append(".%'");
			sql.append(" GROUP BY SUBSTR(T2.ZONE_GB,0,").append(zoneCodeLevel * 2).append(") ) R").append(i+1);
			sql.append(" ON R0.ZONE_GB = R"+(i+1)+".ZONE_GB ");
		}
		sql.append(" ) B ON A .ZONE_GB = B.ZONE_GB");

		return em.createNativeQuery(sql.toString()).getResultList();
	}
	/***
	 *  <p>方法描述：</p>
     *
     * @MethodAuthor maox,2019年7月11日,findMinYearOfConclusin
	 * @return
	 */
	public List<Object[]> findMinYearOfConclusinToNowYear() {
		String sql = "SELECT MIN(T.BHK_YEAR),extract(year from sysdate) FROM TD_CLUSION_ZONE_TIME T";
		return em.createNativeQuery(sql).getResultList();
	}
	/**
	 * 获取职业病大类信息
	 *
	 * @return findBigHarmInfo
	 * <AUTHOR>
	 * @history 2019年11月23日
	 */
	public List<Object[]> findBigHarmInfo() {
		StringBuffer sql = new StringBuffer("");
		sql.append(" SELECT T.CODE_LEVEL_NO,T.CODE_NAME FROM TS_SIMPLE_CODE T  ");
		sql.append(" INNER JOIN TS_CODE_TYPE T1 ON T.CODE_TYPE_ID = T1.RID ");
		sql.append(" WHERE T1.CODE_TYPE_NAME = '5026' AND T.EXTENDS1 = '1' ");
		sql.append(" AND T.IF_REVEAL = '1' AND INSTR(T.CODE_LEVEL_NO,'.','1','1') = 0 ");
		sql.append(" ORDER BY T.NUM,T.CODE_LEVEL_NO ");
		return em.createNativeQuery(sql.toString()).getResultList();
	}
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findDiseaseyTypeId(String typeNo,boolean ifAllUse) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.extendS1 = '1' and t.tsCodeType.codeTypeName in (").append(typeNo)
					.append(") ");
			if (ifAllUse) {
				sb.append(" and t.ifReveal= 1");
			}
			sb.append(" order by t.num,t.codeLevelNo ,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}
	
	
	/**
 	 * <p>方法描述：获取企业所在地区结果分析数据
 	 * dimension:1用人单位地区2检查机构
 	 * analyseType:1人数2人次数
 	 * selectBigHarmRids：选择的危害因素rid，以逗号隔开
 	 * </p>
 	 * @MethodAuthor qrr,2019年11月21日,getResultAnalyseDatas
	 * */
	public List<Object[]> getResultAnalyseDatas(Integer dimension,
			Integer analyseType, Integer analyseTerms,
			List<TsSimpleCode> headList, String zoneGb, Date sdate, Date edate,
			String selectBigHarmIds) {
		if (null == dimension || null == analyseType || null == analyseTerms) {
			return null;
		}
		int zoneType = ZoneUtil.getZoneType(zoneGb);
		StringBuffer sql = new StringBuffer();
		sql.append("select tt.ZONE_NAME ");
		if (1==dimension) {
			sql.append(",tt.zone_gb,aa.*,1 as mergeRows,1 as ifshow");
			sql.append(" from ts_zone tt ");
		}else {
			sql.append(",aa.* ");
			sql.append(" from ts_zone tt ");
			sql.append(" left join( ");
			sql.append("select ");
			if (2==zoneType) {
				sql.append("substr(tt3.zone_gb,0,4)zone_gb ");
			}else if (3==zoneType) {
				sql.append("substr(tt3.zone_gb,0,6)zone_gb ");
			}else if (4==zoneType) {
				sql.append("substr(tt3.zone_gb,0,8)zone_gb ");
			}else {
				sql.append("tt3.zone_gb ");
			}
			sql.append(",tt2.UNIT_NAME,org.*,1 as mergeRows,1 as ifshow");
			sql.append(" from TB_TJ_SRVORG tt2 ");
			sql.append(" left join ts_zone tt3 on tt3.rid = tt2.ZONE_ID ");
		}
		sql.append(" left join (");
		sql.append("select ");
		
		if (1 == dimension) {
			if (2 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,4)||'000000' as zone_gb ");
			} else if (3 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,6)||'0000' as zone_gb ");
			} else if (4 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,8)||'00' as zone_gb ");
			} else {
				sql.append(" t3.zone_gb ");
			}
		}else {
			sql.append(" tt1.rid");
		}
		sql.append(",count(distinct t1.rid) as crpts");
		if (1 == analyseType) {
			sql.append(",count(distinct t.person_id) as totals,count(distinct case when t.sex='男' then  t.person_id else null end) as mans");
			sql.append(",count(distinct case when t.sex='女' then  t.person_id else null end) as womans");
			if (null != headList && headList.size() > 0) {
				for (TsSimpleCode t : headList) {
					if (1==analyseTerms) {//在岗状态
						sql.append(",count(distinct case when t.ONGUARD_STATEID="
								+ t.getRid() + " then  t.person_id else null end)");
					}else if (2==analyseTerms) {
						sql.append(",count(distinct case when t5.code_level_no like '"
								+ t.getCodeLevelNo()
								+ (t.getCodeLevelNo().equals(t.getCodeNo()) ? "."
										: "")
								+ "%' then  t.person_id else null end)");
					}else {
						sql.append(",count(distinct case when t5.BHKRST_ID="
								+ t.getRid() + " then  t.person_id else null end)");
					}
				}
			}
		} else {
			sql.append(",count(distinct t.rid)  as totals,count(distinct case when t.sex='男' then t.rid else null end) as mans");
			sql.append(",count(distinct case when t.sex='女' then t.rid else null end) as womans");
			if (null != headList && headList.size() > 0) {
				for (TsSimpleCode t : headList) {
					if (1==analyseTerms) {//在岗状态
						sql.append(",count(distinct case when t.ONGUARD_STATEID="
								+ t.getRid() + " then t.rid else null end)");
					}else if (2==analyseTerms) {
						sql.append(",count(distinct case when t5.code_level_no like '"
								+ t.getCodeLevelNo()
								+ (t.getCodeLevelNo().equals(t.getCodeNo()) ? "."
										: "") + "%' then t.rid else null end)");
					}else {
						sql.append(",count(distinct case when t5.BHKRST_ID="
								+ t.getRid() + " then t.rid else null end)");
					}
					
				}
			}
		}
		sql.append(" from TD_TJ_BHK t ");
		sql.append(" left join TB_TJ_CRPT t1  on t1.rid = t.crpt_id");
		if (1== dimension) {
			sql.append(" left join ts_zone t3 on t3.rid = t1.zone_id ");
		}else {
			sql.append(" left join TB_TJ_SRVORG tt1  on tt1.rid = t.BHKORG_ID");
		}
		if (2==analyseTerms) {
			//危害因素
			sql.append(" left join TD_TJ_BADRSNS t4 on t4.BHK_ID = t.rid");
			sql.append(" left join TS_SIMPLE_CODE t5 on t4.BADRSN_ID = t5.rid");
		}
		if (3==analyseTerms) {
			//体检结论
			sql.append(" left join TD_TJ_MHKRST t5 on t5.BHK_ID = t.rid");
		}
		sql.append(" where t.BHK_TYPE in (3,4)");
		sql.append(" and t1.INTER_PRC_TAG = 1");
		if (null != sdate) {
			sql.append(" and t.BHK_DATE >=to_date('")
					.append(DateUtils.formatDate(sdate, "yyyy-MM-dd"))
					.append("','yyyy-MM-dd')");
		}
		if (null != edate) {
			sql.append(" and t.BHK_DATE <=to_date('")
					.append(DateUtils.formatDate(edate, "yyyy-MM-dd"))
					.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
		}
		if (2==analyseTerms) {
			if (StringUtils.isNotBlank(selectBigHarmIds)) {
				sql.append(" and t5.rid in (").append(selectBigHarmIds).append(")");
			}
		}
		sql.append(" group by ");
		if (1 == dimension) {
			if (2 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,4)");
			} else if (3 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,6)");
			} else if (4 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,8)");
			} else {
				sql.append(" t3.zone_gb ");
			}
			sql.append(")aa on aa.zone_gb = tt.zone_gb where 1=1");
		}else {
			sql.append(" tt1.rid)org on org.rid = tt2.rid where 1=1 and tt2.STOP_TAG = 1");
			sql.append(")aa on aa.zone_gb=");
			if (2==zoneType) {
				sql.append("substr(tt.zone_gb,0,4)");
			}else if (3==zoneType) {
				sql.append("substr(tt.zone_gb,0,6)");
			}else if (4==zoneType) {
				sql.append("substr(tt.zone_gb,0,8)");
			}else {
				sql.append("tt.zone_gb");
			}
			sql.append(" where UNIT_NAME is not null");
		}
		if (StringUtils.isNotBlank(zoneGb)) {
			sql.append(" and tt.zone_gb like '")
					.append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
			sql.append(" and tt.zone_type" + (zoneType >= 4 ? ">=" : ">"))
			.append(zoneType).append(" and tt.zone_type<=")
			.append(zoneType + 1);
		}
		sql.append(" order by tt.zone_gb"
				+ (1 != dimension ? ",UNIT_NAME" : ""));
		return this.findSqlResultList(sql.toString());
	}
	/**
 	 * <p>方法描述：查询用人单位所在地区或体检机构同期几年的人数或人次数</p>
 	 * @MethodAuthor qrr,2019年11月22日,getDuringYearResultDatas
	 * */
	public List<Object[]> getDuringYearResultDatas(Integer dimension,
			Integer analyseType, String zoneGb, Integer orgId, Integer syear,
			Integer eyear,String selectBigHarmIds) {
		if (null == dimension || null == analyseType) {
			return null;
		}
		StringBuffer sql = new StringBuffer();
		sql.append("select ");
		if (1 == analyseType) {// 人数
			sql.append(" count(distinct t.person_id) as totals");
		} else {// 人次数
			sql.append(" count(distinct t.rid) as totals");
		}
		sql.append(",to_number(to_char(t.bhk_date,'yyyy'))");
		sql.append(" from td_tj_bhk t ");
		sql.append(" left join TB_TJ_CRPT t1  on t1.rid = t.crpt_id");
		if (1 == dimension) {
			sql.append(" left join ts_zone t3 on t3.rid = t1.zone_id where 1=1");
			if (StringUtils.isNotBlank(zoneGb)) {
				sql.append(" and t3.zone_gb like '")
						.append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
			}
		}else {
			sql.append(" left join TB_TJ_SRVORG tt1  on tt1.rid = t.BHKORG_ID where 1=1");
			if (null != orgId) {
				sql.append(" and tt1.rid =").append(orgId);
				sql.append(" and tt1.STOP_TAG = 1");
			}
		}
		sql.append(" and t1.INTER_PRC_TAG = 1");
		sql.append(" and t.BHK_TYPE in (3,4)");
		if (null != syear) {
			sql.append(" and to_number(to_char(t.bhk_date,'yyyy')) >=").append(
					syear);
		}
		if (null != eyear) {
			sql.append(" and to_number(to_char(t.bhk_date,'yyyy')) <=").append(
					eyear);
		}
		if (StringUtils.isNotBlank(selectBigHarmIds)) {
			sql.append(" and exists(select 1 from TD_TJ_BADRSNS badRsn where badRsn.BHK_ID = t.rid");
			sql.append(" and badRsn.BADRSN_ID in (").append(selectBigHarmIds).append(")").append(")");
		}
		sql.append(" group by to_char(t.bhk_date,'yyyy')");
		sql.append(" order by to_char(t.bhk_date,'yyyy')");
		return this.findSqlResultList(sql.toString());
	}
	/**
 	 * <p>方法描述：获取企业所在地区结论分析数据
 	 * analyseType:1地区2机构
 	 * </p>
 	 * @MethodAuthor qrr,2019年11月21日,getConclusionAnalyseDatas
	 * */
	public List<Object[]> getConclusionAnalyseDatas(Integer analyseType,
			String zoneGb, Date sdate, Date edate, List<TsSimpleCode> headList) {
		if (null == analyseType) {
			return null;
		}
		int zoneType = ZoneUtil.getZoneType(zoneGb);
		StringBuffer sql = new StringBuffer();
		sql.append("select ");
		if (0 == analyseType) {
			sql.append(" tt.ZONE_NAME,tt.zone_Gb,aa.*,1,1  ");
		} else {
			sql.append(" tt.ZONE_NAME,B.* ");
		}
		if (0 == analyseType) {
			sql.append(" from ts_zone tt ");
		} else {
			sql.append(" from ts_zone tt ");
			sql.append(" LEFT JOIN (SELECT ");
			if (2==zoneType) {
				sql.append(" substr( tt.zone_gb, 0, 4 )zone_gb");
			}else if (3==zoneType) {
				sql.append(" substr( tt.zone_gb, 0, 6 )zone_gb");
			}else if (4==zoneType) {
				sql.append(" substr( tt.zone_gb, 0, 8 )zone_gb");
			}else {
				sql.append(" tt.zone_gb");
			}
			sql.append(",org.UNIT_NAME,aa.*,1 AS mergeRows,1 AS ifshow ");
			sql.append(" from TB_TJ_SRVORG org ");
			sql.append(" left join ts_zone tt on tt.rid = org.ZONE_ID ");
		}
		sql.append(" left join (");
		sql.append("select ");
		
		if (0 == analyseType) {
			if (2 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,4)||'000000' as zone_gb ");
			} else if (3 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,6)||'0000' as zone_gb ");
			} else if (4 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,8)||'00' as zone_gb ");
			} else {
				sql.append(" t3.zone_gb ");
			}
		} else {
			sql.append(" tt1.rid");
		}
		sql.append(",count(t.rid) as totals");
		if (null != headList && headList.size() > 0) {
			for (TsSimpleCode t : headList) {
				sql.append(",count(case when t5.BHKRST_ID=" + t.getRid()
						+ " then  t.rid else null end)");
			}
		}
		sql.append(" from TD_TJ_BHK t ");
		if (0 == analyseType) {
			sql.append(" left join TB_TJ_CRPT t1  on t1.rid = t.crpt_id");
			sql.append(" left join ts_zone t3 on t3.rid = t1.zone_id ");
		} else {
			sql.append(" left join TB_TJ_SRVORG tt1  on tt1.rid = t.BHKORG_ID");
		}
		// 体检结论
		sql.append(" left join TD_TJ_MHKRST t5 on t5.BHK_ID = t.rid");

		sql.append(" where t.BHK_TYPE in (3,4)");
		if (0 == analyseType) {
			sql.append(" and t1.INTER_PRC_TAG = 1");

		} else {
			sql.append(" and tt1.STOP_TAG = 1");
		}
		if (null != sdate) {
			sql.append(" and t.BHK_DATE >=to_date('")
					.append(DateUtils.formatDate(sdate, "yyyy-MM-dd"))
					.append("','yyyy-MM-dd')");
		}
		if (null != edate) {
			sql.append(" and t.BHK_DATE <=to_date('")
					.append(DateUtils.formatDate(edate, "yyyy-MM-dd"))
					.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
		}
		sql.append(" group by ");
		if (0 == analyseType) {
			if (2 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,4)");
			} else if (3 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,6)");
			} else if (4 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,8)");
			} else {
				sql.append(" t3.zone_gb ");
			}
			sql.append(")aa on aa.zone_gb = tt.zone_gb where 1=1");
		} else {
			sql.append(" tt1.rid)aa on aa.rid = org.rid where 1=1 and org.STOP_TAG = 1");
			sql.append(")B ON  B.zone_gb = ");
			if (2==zoneType) {
				sql.append("substr( tt.zone_gb, 0, 4 )");
			}else if (3==zoneType) {
				sql.append("substr( tt.zone_gb, 0, 6 )");
			}else if (4==zoneType) {
				sql.append("substr( tt.zone_gb, 0, 8 )");
			}else {
				sql.append("tt.zone_gb");
			}
			sql.append(" WHERE UNIT_NAME IS NOT NULL");
		}

		if (StringUtils.isNotBlank(zoneGb)) {
			sql.append(" and tt.zone_gb like '")
					.append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
			sql.append(" and tt.zone_type" + (zoneType >= 4 ? ">=" : ">"))
					.append(zoneType).append(" and tt.zone_type<=")
					.append(zoneType + 1);
		}
		sql.append(" order by tt.zone_gb");
		if (0 != analyseType) {
			sql.append(",UNIT_NAME");
		}
		return this.findSqlResultList(sql.toString());
	}
	
	

	/**
 	 * <p>方法描述：获取目标疾病检出情况分析数据
 	 * analyseType:1职业禁忌证2疑似职业病
 	 * </p>
 	 * @MethodAuthor qrr,2019年11月21日,getConclusionAnalyseDatas
	 * */
	public List<Object[]> getTargetDiseaseAnalyseDatas(Integer analyseType,
			String zoneGb, Date sdate, Date edate,String badRsnIds) {
		if (null == analyseType) {
			return new  ArrayList<>();
		}
		StringBuffer sql = new StringBuffer();
		sql.append("select tt.ZONE_NAME ");
		sql.append(",B.*,A.persons ");
		sql.append(",case when nvl(b.totalPesons,0)=0 or nvl(A.persons,0)=0  then '0.00' else to_char(round(A.persons*100/b.totalPesons,2), 'FM9990.00') end");
		sql.append(" from ts_zone tt ");
		sql.append(" left join (")
				.append(getJjzAndYsZybTjPersonsSqlByCrptZone(analyseType,
						zoneGb, sdate, edate, badRsnIds))
				.append(")A on A.zone_gb = tt.zone_gb");
		sql.append(" left join (");
		sql.append("select ");
		int zoneType = ZoneUtil.getZoneType(zoneGb);
		if (2 == zoneType) {
			sql.append(" substr(t3.zone_gb,0,4)||'000000' as zone_gb ");
		} else if (3 == zoneType) {
			sql.append(" substr(t3.zone_gb,0,6)||'0000' as zone_gb ");
		} else if (4 == zoneType) {
			sql.append(" substr(t3.zone_gb,0,8)||'00' as zone_gb ");
		} else {
			sql.append(" t3.zone_gb ");
		}
		sql.append(",count(distinct t1.rid) as crpts");//企业数
		sql.append(",count(t.rid) as totals");//检查总人次数
		sql.append(",count(distinct t.PERSON_ID) as totalPesons");//检查总人数
		sql.append(" from TD_TJ_BHK t ");
		sql.append(" left join TB_TJ_CRPT t1  on t1.rid = t.crpt_id");
		sql.append(" left join ts_zone t3 on t3.rid = t1.zone_id ");
		sql.append(" where t.BHK_TYPE in (3,4)");
		sql.append(" and t1.INTER_PRC_TAG = 1");
		if (null != sdate) {
			sql.append(" and t.BHK_DATE >=to_date('")
					.append(DateUtils.formatDate(sdate, "yyyy-MM-dd"))
					.append("','yyyy-MM-dd')");
		}
		if (null != edate) {
			sql.append(" and t.BHK_DATE <=to_date('")
					.append(DateUtils.formatDate(edate, "yyyy-MM-dd"))
					.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
		}
		if (StringUtils.isNotBlank(badRsnIds)) {
			sql.append(" and exists(select 1 from TD_TJ_BADRSNS t4 where t.rid = t4.BHK_ID and t4.BADRSN_ID in (").append(badRsnIds).append("))");
		}
		sql.append(" group by ");
		if (2 == zoneType) {
			sql.append(" substr(t3.zone_gb,0,4)");
		} else if (3 == zoneType) {
			sql.append(" substr(t3.zone_gb,0,6)");
		} else if (4 == zoneType) {
			sql.append(" substr(t3.zone_gb,0,8)");
		} else {
			sql.append(" t3.zone_gb ");
		}
		sql.append(")B on B.zone_gb = tt.zone_gb where 1=1");

		if (StringUtils.isNotBlank(zoneGb)) {
			sql.append(" and tt.zone_gb like '")
					.append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
			sql.append(" and tt.zone_type" + (zoneType >= 4 ? ">=" : ">"))
					.append(zoneType).append(" and tt.zone_type<=")
					.append(zoneType + 1);
		}
		sql.append(" order by tt.zone_gb");
		return this.findSqlResultList(sql.toString());
	}
	/**
 	 * <p>方法描述：获取职业禁忌证或疑似职业病企业地区体检人数</p>
 	 * @MethodAuthor qrr,2019年11月25日,getSql
	 * */
	private String getJjzAndYsZybTjPersonsSqlByCrptZone(Integer analyseType,
			String zoneGb, Date sdate, Date edate, String badRsnIds) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select ");
		int zoneType = ZoneUtil.getZoneType(zoneGb);
		if (2 == zoneType) {
			sql.append(" substr(t3.zone_gb,0,4)||'000000' as zone_gb ");
		} else if (3 == zoneType) {
			sql.append(" substr(t3.zone_gb,0,6)||'0000' as zone_gb ");
		} else if (4 == zoneType) {
			sql.append(" substr(t3.zone_gb,0,8)||'00' as zone_gb ");
		} else {
			sql.append(" t3.zone_gb ");
		}
		sql.append(",count(distinct tt.PERSON_ID) as persons");
		sql.append(" from "
				+ (1 == analyseType ? "TD_TJ_CONTRAINDLIST"
						: "TD_TJ_SUPOCCDISELIST") + " tt1 ");
		sql.append(" inner join TD_TJ_BHK tt on tt.rid = tt1.BHK_ID ");
		sql.append(" left join TB_TJ_CRPT t1  on t1.rid = tt.crpt_id");
		sql.append(" left join ts_zone t3 on t3.rid = t1.zone_id ");
		sql.append(" where tt.BHK_TYPE in (3,4) and t1.INTER_PRC_TAG = 1");
		if (null != sdate) {
			sql.append(" and tt.BHK_DATE >=to_date('")
					.append(DateUtils.formatDate(sdate, "yyyy-MM-dd"))
					.append("','yyyy-MM-dd')");
		}
		if (null != edate) {
			sql.append(" and tt.BHK_DATE <=to_date('")
					.append(DateUtils.formatDate(edate, "yyyy-MM-dd"))
					.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
		}
		if (StringUtils.isNotBlank(badRsnIds)) {
			sql.append(" and tt1.BADRSN_ID in (").append(badRsnIds).append(")");
		}
		sql.append(" GROUP BY ");
		if (2 == zoneType) {
			sql.append(" substr(t3.zone_gb,0,4)");
		} else if (3 == zoneType) {
			sql.append(" substr(t3.zone_gb,0,6)");
		} else if (4 == zoneType) {
			sql.append(" substr(t3.zone_gb,0,8)");
		} else {
			sql.append(" t3.zone_gb ");
		}
		return sql.toString();
	}
	/**
	 * 职业健康检查工作量查询
	 *
	 * @param searchZoneType
	 *            地区级别
	 * @param searchZoneCode
	 *            地区编码
	 * @param searchStartTime
	 *            开始时间
	 * @param searchEndTime
	 *            结束时间
	 * @param list
	 *            查询结果
	 * @return 查询结果
	 * <AUTHOR>
	 * @createDate 2014-9-28
	 */

	public List<Object[]> getWorkAmountByArea(String searchZoneType, String searchZoneCode, Date searchStartTime,
			Date searchEndTime, List<Integer> list) {
		StringBuilder sb = new StringBuilder();
		// 截取长度
		int sublength = Integer.valueOf(searchZoneType).intValue() * 2;
		// 地区编码去零
		String code = ZoneUtil.zoneSelect(searchZoneCode);
		// 日期转换
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String start = sdf.format(searchStartTime);
		String end = sdf.format(searchEndTime);
		sb.append("SELECT SUBSTR(T2.ZONE_GB, 0, ");
		sb.append(sublength);
		sb.append(") R0,COUNT(T.RID) R1,SUM(DECODE(T.SEX, '男', 1, 0)) R2,SUM(DECODE(T.SEX, '女', 1, 0)) R3,");
		sb.append("SUM(DECODE(T.BHK_TYPE, '3', 1, 0)) R4,SUM(DECODE(T.BHK_TYPE, '4', 1, 0)) R5,SUM(DECODE(T.IF_RHK, '1', 1, 0)) R6,'1' AS R7,");
		sb.append("SUM(DECODE(T.IF_WRKTABU, '1', 1, 0)) R8,'1' AS R9,SUM(DECODE(T.IF_TARGETDIS, '1', 1, 0)) R10,'1' AS R11");
		for (Integer i : list) {
			sb.append(",SUM(DECODE(T.ONGUARD_STATEID ,'");
			sb.append(i);
			sb.append("', 1, 0))");
		}
		sb.append(" FROM TD_TJ_BHK T");
		sb.append(" INNER JOIN TB_TJ_SRVORG T1 ON T1.RID = T.BHKORG_ID");
		sb.append(" INNER JOIN TS_ZONE T2 ON T2.RID = T1.ZONE_ID");
		sb.append(" WHERE T2.ZONE_GB LIKE '");
		sb.append(code);
		sb.append("%'");
		sb.append(" AND T.BHK_DATE >= to_date('");
		sb.append(start);
		sb.append("','yyyy-MM-dd')");
		sb.append(" AND T.BHK_DATE <= to_date('");
		sb.append(end);
		sb.append("','yyyy-MM-dd')");
		sb.append(" GROUP BY CUBE(SUBSTR(T2.ZONE_GB,0,");
		sb.append(sublength);
		sb.append(")) ORDER BY SUBSTR(T2.ZONE_GB,0,");
		sb.append(sublength).append(")");
		return em.createNativeQuery(sb.toString()).getResultList();
	}
	/**
	 * @param sql
	 *            执行的sql语句
	 * @return List<Object>
	 * <AUTHOR>
	 * @createDate 2014-9-18
	 */

	public List<Object[]> executeSQL(String sql) {
		if (sql == null) {
			return null;
		}
		return (List<Object[]>) this.em.createNativeQuery(sql).getResultList();
	}
	/**
	 * 按机构查询统计工作量
	 *
	 * @param searchZoneCode
	 *            地区编码
	 * @param searchStartTime
	 *            开始时间
	 * @param searchEndTime
	 *            结束时间
	 * @param list
	 *            在岗状态集合
	 * @return 查询结果
	 * <AUTHOR>
	 * @param sessionData 
	 * @createDate 2014-9-28
	 */

	public List<Object[]> getWorkAmountBySrvorg(String searchZoneCode, Date searchStartTime, Date searchEndTime,
			List<Integer> list,Integer flagUnitId, SessionData sessionData) {
		String code = ZoneUtil.zoneSelect(searchZoneCode);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String start = sdf.format(searchStartTime);
		String end = sdf.format(searchEndTime);
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT W3.ZONE_GB,W2.UNIT_NAME,W2.UNIT_CODE,NVL(W1.R1,0),NVL(W1.R2,0),NVL(W1.R3,0),NVL(W1.R4,0),NVL(W1.R5,0),NVL(W1.R6,0),NVL(W1.R61,0),NVL(W1.R7,0),NVL(W1.R71,0),NVL(W1.R8,0),NVL(W1.R81,0)");
		for (int i = 0; i < list.size(); i++) {
			sb.append(",NVL(R1").append(i).append(",0)");
		}
		sb.append(" FROM TB_TJ_SRVORG W2");
		sb.append(" LEFT JOIN TS_ZONE W3 ON W3.RID = W2.ZONE_ID");
		sb.append(" LEFT JOIN(");
		sb.append("SELECT T1.UNIT_CODE R0");
		sb.append(",COUNT(T.RID) R1");
		sb.append(",SUM(DECODE(T.SEX, '男', 1, 0)) R2");
		sb.append(",SUM(DECODE(T.SEX, '女', 1, 0)) R3");
		sb.append(",SUM(DECODE(T.BHK_TYPE, '3', 1, 0)) R4");
		sb.append(",SUM(DECODE(T.BHK_TYPE, '4', 1, 0)) R5");
		sb.append(",SUM(DECODE(T.IF_RHK, '1', 1, 0)) R6,'1' AS R61");
		sb.append(",SUM(DECODE(T.IF_WRKTABU, '1', 1, 0)) R7,'1' AS R71");
		sb.append(",SUM(DECODE(T.IF_TARGETDIS, '1', 1, 0)) R8,'1' AS R81");
		for (int i = 0; i < list.size(); i++) {
			sb.append(",SUM(DECODE(T.ONGUARD_STATEID ,'");
			sb.append(list.get(i));
			sb.append("', 1, 0)) R1").append(i);
		}
		sb.append(" FROM TD_TJ_BHK T");
		sb.append(" INNER JOIN TB_TJ_SRVORG T1 ON T1.RID = T.BHKORG_ID");
		sb.append(" AND T.BHK_DATE >= to_date('");
		sb.append(start);
		sb.append("','yyyy-MM-dd')");
		sb.append(" AND T.BHK_DATE <= to_date('");
		sb.append(end);
		sb.append("','yyyy-MM-dd')");
		sb.append(" GROUP BY ROLLUP(T1.UNIT_CODE)) W1 ON W1.R0=W2.UNIT_CODE WHERE 1=1 AND W2.STOP_TAG =1");
		sb.append(" AND W3.ZONE_GB LIKE '").append(code).append("%'");
		sb.append(" and W3.zone_type>2 ");
		//是否疾控
		if (null != flagUnitId) {
			sb.append(" AND W2.REG_ORGID = ").append(flagUnitId);
		}
		sb.append(" ORDER BY W3.ZONE_GB,W2.UNIT_CODE");
		return em.createNativeQuery(sb.toString()).getResultList();
	}
	/**
	 * 页面数字点击查询
	 *
	 * @param condition1
	 *            条件一
	 * @param condition2
	 *            条件一
	 * @return 查询结果
	 * <AUTHOR>
	 * @createDate 2014-9-28
	 */

	public List<Object[]> getBHKDataByRC(String condition1, String condition2, Date searchStartTime, Date searchEndTime) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String start = sdf.format(searchStartTime);
		String end = sdf.format(searchEndTime);
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT T.RID,T.PERSON_NAME,T.SEX,T.IDC,T.BHK_DATE,T1.UNIT_NAME,T2.CRPT_NAME");
		sb.append("  FROM TD_TJ_BHK T");
		sb.append(" INNER JOIN TB_TJ_SRVORG T1 ON T1.RID = T.BHKORG_ID");
		sb.append(" INNER JOIN TB_TJ_CRPT T2 ON T2.RID = T.CRPT_ID");
		sb.append(" INNER JOIN TS_ZONE T3 ON T3.RID = T1.ZONE_ID");
		sb.append(" where 1 = 1");
		sb.append(" AND ");
		sb.append(condition1);
		sb.append(" AND ");
		sb.append(condition2);
		sb.append(" AND T.BHK_DATE >= to_date('");
		sb.append(start);
		sb.append(" ','yyyy-MM-dd')");
		sb.append("  AND T.BHK_DATE <= to_date('");
		sb.append(end);
		sb.append(" ','yyyy-MM-dd')");
		return em.createNativeQuery(sb.toString()).getResultList();
	}
	//qrr
	
	
	//aj
    /**
    * @Description : 根据编码获取大类
    * @MethodAuthor: anjing
    * @Date : 2019/11/27 10:39
    **/
    public List<TsSimpleCode> findBigHeadList(String codeTypeName, boolean ifZybDisease) {
        List<TsSimpleCode> list = new ArrayList<>();
        StringBuffer sql = new StringBuffer("");
        sql.append(" SELECT T.RID,T.CODE_NO,T.CODE_NAME,T.CODE_LEVEL_NO FROM TS_SIMPLE_CODE T  ");
        sql.append(" INNER JOIN TS_CODE_TYPE T1 ON T.CODE_TYPE_ID = T1.RID ");
        sql.append(" WHERE T1.CODE_TYPE_NAME = '").append(codeTypeName).append("' ");
        if(ifZybDisease) {
            sql.append(" AND T.EXTENDS1 = '1' ");
        }
        sql.append(" AND T.IF_REVEAL = '1' AND INSTR(T.CODE_LEVEL_NO,'.','1','1') = 0 ");
        sql.append(" ORDER BY T.NUM,T.CODE_LEVEL_NO ");

        List<Object[]> resultList = em.createNativeQuery(sql.toString()).getResultList();
        if(CollectionUtils.isEmpty(resultList)) {
            return null;
        }
        for(Object[] obj : resultList) {
            TsSimpleCode tsSimpleCode = new TsSimpleCode();
            tsSimpleCode.setRid(Integer.valueOf(obj[0].toString()));
            tsSimpleCode.setCodeNo(obj[1]==null?"":String.valueOf(obj[1]));
            tsSimpleCode.setCodeName(obj[2]==null?"":String.valueOf(obj[2]));
            tsSimpleCode.setCodeLevelNo(obj[3]==null?"":String.valueOf(obj[3]));
            list.add(tsSimpleCode);
        }
        return list;
    }

    /**
    * @Description : 获取用人单位职业病基本情况统计数据
    * @MethodAuthor: anjing
    * @Date : 2019/11/26 16:31
    **/
	public List<Object[]> getCrptStatisticsDatasByZone(String zoneGb, Date tlBdate,
                                                 Date tlEdate, List<TsSimpleCode> zybDiseaseHeadList, boolean zybDiseaseSmall) {
        StringBuffer sb = new StringBuffer();
	    if(null == zoneGb || CollectionUtils.isEmpty(zybDiseaseHeadList)) {
            return null;
        }
        int zoneType = ZoneUtil.getZoneType(zoneGb);
        sb.append(" SELECT A.ZONE_NAME,B.* ");
        sb.append(" FROM ( ");
        sb.append(" SELECT ");
        if(2 == zoneType) {
            sb.append(" SUBSTR(T.ZONE_GB, 0, 4) || '000000' AS ZONE_GB ");
        } else if(3 == zoneType) {
            sb.append(" SUBSTR(T.ZONE_GB, 0, 6) || '0000' AS ZONE_GB ");
        } else if(4 == zoneType) {
            sb.append(" SUBSTR(T.ZONE_GB, 0, 8) || '00' AS ZONE_GB ");
        } else {
            sb.append(" T.ZONE_GB,  ");
        }
        sb.append(" ,T.ZONE_NAME FROM TS_ZONE T ");
        sb.append(" WHERE T.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zoneGb)).append("%' ");
        sb.append(" AND T.ZONE_TYPE "+ (zoneType >= 4 ? ">=" : ">"))
                .append(zoneType).append(" AND T.ZONE_TYPE <= ")
                .append(zoneType + 1);
        sb.append(" AND T.IF_REVEAL = 1 ");
        sb.append(" ORDER BY T.ZONE_GB ) A ");
        sb.append(" LEFT JOIN ( ");
        if(2 == zoneType) {
            sb.append(" SELECT DISTINCT SUBSTR(T2.ZONE_GB, 0, 4) || '000000' AS ZONE_GB ");
        } else if(3 == zoneType) {
            sb.append(" SELECT DISTINCT SUBSTR(T2.ZONE_GB, 0, 6) || '0000' AS ZONE_GB ");
        } else if(4 == zoneType) {
            sb.append(" SELECT DISTINCT SUBSTR(T2.ZONE_GB, 0, 8) || '00' AS ZONE_GB ");
        } else {
            sb.append(" SELECT T2.ZONE_GB,  ");
        }
        for (TsSimpleCode t : zybDiseaseHeadList) {
            sb.append(", COUNT(CASE WHEN T3.CODE_LEVEL_NO LIKE '")
                    .append(t.getCodeLevelNo())
                    //.append(t.getCodeLevelNo().equals(t.getCodeNo())?".":"")
                    .append("%' THEN T.RID ELSE NULL END) ");
        }
        sb.append(" FROM TD_ZW_OCCDISCASE T ");
        sb.append(" LEFT JOIN TB_TJ_CRPT T1 ON T.CRPT_ID = T1.RID ");
        sb.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID ");
        if(!zybDiseaseSmall) {
            sb.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.OCCDISE_ID = T3.RID ");
        } else {
            sb.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.CFM_ZYB_SMALL_ID = T3.RID ");
        }
        sb.append(" WHERE NVL( T.DEL_MARK, 0 ) = 0 ");
        sb.append(" AND T.STATE_MARK > 4 ");
        sb.append(" AND T.IS_DIS = 1 ");
        sb.append(" AND T1.INTER_PRC_TAG = 1 ");
        if (null != tlBdate) {
            sb.append(" AND T.TL_DATE >= TO_DATE('").append(DateUtils.formatDate(tlBdate, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
        }
        if (null != tlEdate) {
            sb.append(" AND T.TL_DATE <= TO_DATE('").append(DateUtils.formatDate(tlEdate, "yyyy-MM-dd")).append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
        }
        sb.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zoneGb)).append("%' ");
        if(2 == zoneType) {
            sb.append(" GROUP BY SUBSTR(T2.ZONE_GB, 0, 4) ");
        } else if(3 == zoneType) {
            sb.append(" GROUP BY SUBSTR(T2.ZONE_GB, 0, 6) ");
        } else if(4 == zoneType) {
            sb.append(" GROUP BY SUBSTR(T2.ZONE_GB, 0, 8) ");
        } else {
            sb.append(" GROUP BY T2.ZONE_GB,  ");
        }
        sb.append(" ORDER BY T2.ZONE_CODE ");
        sb.append(" ) B ON A.ZONE_GB = B.ZONE_GB ");
        return this.findSqlResultList(sb.toString());
    }

    /**
     * @Description : 获取用人单位职业病基本情况统计数据
     * @MethodAuthor: anjing
     * @Date : 2019/11/26 16:31
     **/
    public List<Object[]> getCrptStatisticsDatasByIndus(Integer type, String zoneGb, Date tlBdate,
                                                       Date tlEdate, List<TsSimpleCode> zybDiseaseHeadList,
                                                       List<TsSimpleCode> indusTypeHeadList, List<TsSimpleCode> economyHeadList,
                                                       List<TsSimpleCode> crptsizwList, boolean zybDiseaseSmall, boolean indusSmall, boolean economySmall) {
        StringBuffer sb = new StringBuffer();
        StringBuffer sb1 = new StringBuffer();
        StringBuffer sb2 = new StringBuffer();
        StringBuffer sb3 = new StringBuffer();
        StringBuffer sb4 = new StringBuffer();
        StringBuffer sb5 = new StringBuffer();
        StringBuffer ids = null;
        if(null == zoneGb || CollectionUtils.isEmpty(zybDiseaseHeadList)) {
            return null;
        }
        sb.append(" SELECT TT.CODE_NAME, A.* ");
        sb.append(" FROM TS_SIMPLE_CODE TT ");
        sb.append(" LEFT JOIN TS_CODE_TYPE TT1 ON TT.CODE_TYPE_ID = TT1.RID ");
        String codeTypeName = "";
        if(type == 2) {
            codeTypeName = "5002";
            sb2.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T1.INDUS_TYPE_ID = T4.RID ");
            if(indusSmall) {
                sb1.append(" SELECT T4.RID AS INDUS_TYPE_ID ");
                sb3.append(" GROUP BY T4.RID, T4.CODE_NAME ");
                sb4.append(" ) A ON A.INDUS_TYPE_ID = TT.RID ");
            } else {
                Integer num = indusTypeHeadList.get(0).getCodeLevelNo().length();
                sb1.append(" SELECT SUBSTR(T4.CODE_LEVEL_NO, 0, " + num + ") AS CODE_LEVEL_NO ");
                sb3.append(" GROUP BY SUBSTR(T4.CODE_LEVEL_NO, 0, " + num + ") ");
                sb4.append(" ) A ON A.CODE_LEVEL_NO = TT.CODE_LEVEL_NO ");
            }
            if(!CollectionUtils.isEmpty(indusTypeHeadList)) {
                ids = new StringBuffer();
                for (TsSimpleCode s : indusTypeHeadList) {
                    ids.append(",").append(s.getRid());
                }
                sb5.append(" AND TT.RID IN ( ").append(ids.deleteCharAt(0)).append(" )");
            }
        } else if(type == 3) {
            codeTypeName = "5003";
            sb2.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T1.ECONOMY_ID = T4.RID ");
            if(economySmall) {
                sb1.append(" SELECT T4.RID AS ECONOMY_ID ");
                sb3.append(" GROUP BY T4.RID, T4.CODE_NAME ");
                sb4.append(" ) A ON A.ECONOMY_ID = TT.RID ");
            } else {
                Integer num = economyHeadList.get(0).getCodeLevelNo().length();
                sb1.append(" SELECT SUBSTR(T4.CODE_LEVEL_NO, 0, " + num + ") AS CODE_LEVEL_NO ");
                sb3.append(" GROUP BY SUBSTR(T4.CODE_LEVEL_NO, 0, " + num + ") ");
                sb4.append(" ) A ON A.CODE_LEVEL_NO = TT.CODE_LEVEL_NO ");
            }
            if(!CollectionUtils.isEmpty(economyHeadList)) {
                ids = new StringBuffer();
                for (TsSimpleCode s : economyHeadList) {
                    ids.append(",").append(s.getRid());
                }
                sb5.append(" AND TT.RID IN ( ").append(ids.deleteCharAt(0)).append(" )");
            }
        } else if(type == 4) {
            codeTypeName = "5004";
            sb1.append(" SELECT T4.RID AS CRPT_SIZE_ID ");
            sb2.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T1.CRPT_SIZE_ID = T4.RID ");
            sb3.append(" GROUP BY T4.RID, T4.CODE_NAME ");
            sb4.append(" ) A ON A.CRPT_SIZE_ID = TT.RID ");
            if(!CollectionUtils.isEmpty(crptsizwList)) {
                ids = new StringBuffer();
                for (TsSimpleCode s : crptsizwList) {
                    ids.append(",").append(s.getRid());
                }
                sb5.append(" AND TT.RID IN ( ").append(ids.deleteCharAt(0)).append(" )");
            }
        }
        sb.append(" LEFT JOIN ( ");
        sb.append(sb1);
        for (TsSimpleCode t : zybDiseaseHeadList) {
            sb.append(", COUNT(CASE WHEN T3.CODE_LEVEL_NO LIKE '")
                    .append(t.getCodeLevelNo())
                    //.append(t.getCodeLevelNo().equals(t.getCodeNo())?".":"")
                    .append("%' THEN T.RID ELSE NULL END) ");
        }
        sb.append(" FROM TD_ZW_OCCDISCASE T ");
        sb.append(" LEFT JOIN TB_TJ_CRPT T1 ON T.CRPT_ID = T1.RID ");
        sb.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID ");
        if(!zybDiseaseSmall) {
            sb.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.OCCDISE_ID = T3.RID ");
        } else {
            sb.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.CFM_ZYB_SMALL_ID = T3.RID ");
        }
        sb.append(sb2);
        sb.append(" WHERE NVL(T.DEL_MARK, 0) = 0 ");
        sb.append(" AND T.STATE_MARK > 4 ");
        sb.append(" AND T.IS_DIS = 1 ");
        sb.append(" AND T1.INTER_PRC_TAG = 1 ");
        if (null != tlBdate) {
            sb.append(" AND T.TL_DATE >= TO_DATE('").append(DateUtils.formatDate(tlBdate, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
        }
        if (null != tlEdate) {
            sb.append(" AND T.TL_DATE <= TO_DATE('").append(DateUtils.formatDate(tlEdate, "yyyy-MM-dd")).append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
        }
        sb.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zoneGb)).append("%' ");
        sb.append(sb3);
        sb.append(sb4);
        sb.append(" WHERE 1=1 ");

        sb.append(" AND TT1.CODE_TYPE_NAME = '").append(codeTypeName).append("'");
        sb.append(sb5);
        sb.append(" ORDER BY TT.NUM, TT.CODE_LEVEL_NO, TT.CODE_NO ");
        return this.findSqlResultList(sb.toString());
    }

    /**
    * @Description : 根据行业类别/经济类型/企业规模大类统计数据
    * @MethodAuthor: anjing
    * @Date : 2020/1/3 10:36
    **/
    public List<Object[]> getCrptStatisticsDatasByBigClass(Integer type, String zoneGb, Date tlBdate,
                                                        Date tlEdate, List<TsSimpleCode> zybDiseaseHeadList,
                                                        List<TsSimpleCode> indusTypeHeadList, List<TsSimpleCode> economyHeadList,
                                                        List<TsSimpleCode> crptsizwList, boolean zybDiseaseSmall) {
        StringBuffer sb = new StringBuffer();
        StringBuffer sb1 = new StringBuffer();
        StringBuffer sb2 = new StringBuffer();
        StringBuffer sb3 = new StringBuffer();
        StringBuffer sb4 = new StringBuffer();
        StringBuffer sb5 = new StringBuffer();
        StringBuffer ids = null;
        if(null == zoneGb || CollectionUtils.isEmpty(zybDiseaseHeadList)) {
            return null;
        }
        sb.append(" SELECT TT.CODE_NAME, A.* ");
        sb.append(" FROM TS_SIMPLE_CODE TT ");
        sb.append(" LEFT JOIN TS_CODE_TYPE TT1 ON TT.CODE_TYPE_ID = TT1.RID ");
        String codeTypeName = "";
        if(type == 2) {
            codeTypeName = "5002";
            sb2.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T1.INDUS_TYPE_ID = T4.RID ");

            Integer num = indusTypeHeadList.get(0).getCodeLevelNo().length();
            sb1.append(" SELECT SUBSTR(T4.CODE_LEVEL_NO, 0, " + num + ") AS CODE_LEVEL_NO ");
            sb3.append(" GROUP BY SUBSTR(T4.CODE_LEVEL_NO, 0, " + num + ") ");
            sb4.append(" ) A ON A.CODE_LEVEL_NO = TT.CODE_LEVEL_NO ");
            if(!CollectionUtils.isEmpty(indusTypeHeadList)) {
                ids = new StringBuffer();
                for (TsSimpleCode s : indusTypeHeadList) {
                    ids.append(",").append(s.getRid());
                }
                sb5.append(" AND TT.RID IN ( ").append(ids.deleteCharAt(0)).append(" )");
            }
        } else if(type == 3) {
            codeTypeName = "5003";
            sb2.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T1.ECONOMY_ID = T4.RID ");

            Integer num = economyHeadList.get(0).getCodeLevelNo().length();
            sb1.append(" SELECT SUBSTR(T4.CODE_LEVEL_NO, 0, " + num + ") AS CODE_LEVEL_NO ");
            sb3.append(" GROUP BY SUBSTR(T4.CODE_LEVEL_NO, 0, " + num + ") ");
            sb4.append(" ) A ON A.CODE_LEVEL_NO = TT.CODE_LEVEL_NO ");
            if(!CollectionUtils.isEmpty(economyHeadList)) {
                ids = new StringBuffer();
                for (TsSimpleCode s : economyHeadList) {
                    ids.append(",").append(s.getRid());
                }
                sb5.append(" AND TT.RID IN ( ").append(ids.deleteCharAt(0)).append(" )");
            }
        } else if(type == 4) {
            codeTypeName = "5004";
            sb1.append(" SELECT T4.RID AS CRPT_SIZE_ID ");
            sb2.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T1.CRPT_SIZE_ID = T4.RID ");
            sb3.append(" GROUP BY T4.RID, T4.CODE_NAME ");
            sb4.append(" ) A ON A.CRPT_SIZE_ID = TT.RID ");
            if(!CollectionUtils.isEmpty(crptsizwList)) {
                ids = new StringBuffer();
                for (TsSimpleCode s : crptsizwList) {
                    ids.append(",").append(s.getRid());
                }
                sb5.append(" AND TT.RID IN ( ").append(ids.deleteCharAt(0)).append(" )");
            }
        }
        sb.append(" LEFT JOIN ( ");
        sb.append(sb1);
        for (TsSimpleCode t : zybDiseaseHeadList) {
            sb.append(", COUNT(CASE WHEN T3.CODE_LEVEL_NO LIKE '")
                    .append(t.getCodeLevelNo())
                    .append("%' THEN T.RID ELSE NULL END) ");
        }
        sb.append(" FROM TD_ZW_OCCDISCASE T ");
        sb.append(" LEFT JOIN TB_TJ_CRPT T1 ON T.CRPT_ID = T1.RID ");
        sb.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID ");
        if(!zybDiseaseSmall) {
            sb.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.OCCDISE_ID = T3.RID ");
        } else {
            sb.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.CFM_ZYB_SMALL_ID = T3.RID ");
        }
        sb.append(sb2);
        sb.append(" WHERE NVL(T.DEL_MARK, 0) = 0 ");
        sb.append(" AND T.STATE_MARK > 4 ");
        sb.append(" AND T.IS_DIS = 1 ");
        sb.append(" AND T1.INTER_PRC_TAG = 1 ");
        if (null != tlBdate) {
            sb.append(" AND T.TL_DATE >= TO_DATE('").append(DateUtils.formatDate(tlBdate, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
        }
        if (null != tlEdate) {
            sb.append(" AND T.TL_DATE <= TO_DATE('").append(DateUtils.formatDate(tlEdate, "yyyy-MM-dd")).append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
        }
        sb.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zoneGb)).append("%' ");
        sb.append(sb3);
        sb.append(sb4);
        sb.append(" WHERE 1=1 ");

        sb.append(" AND TT1.CODE_TYPE_NAME = '").append(codeTypeName).append("'");
        sb.append(sb5);
        sb.append(" ORDER BY TT.NUM, TT.CODE_LEVEL_NO, TT.CODE_NO ");
        return this.findSqlResultList(sb.toString());
    }

    /**
    * @Description : 根据行业类别/经济类型小类统计数据
    * @MethodAuthor: anjing
    * @Date : 2020/1/3 10:43
    **/
    public List<Object[]> getCrptStatisticsDatasBySmallClass(Integer type, String zoneGb, Date tlBdate,
                                                           Date tlEdate, List<TsSimpleCode> zybDiseaseHeadList,
                                                           List<TsSimpleCode> indusTypeHeadList, List<TsSimpleCode> economyHeadList,
                                                           List<TsSimpleCode> crptsizwList, boolean zybDiseaseSmall) {
        StringBuffer sb = new StringBuffer();
        StringBuffer sb1 = new StringBuffer();
        StringBuffer sb2 = new StringBuffer();
        StringBuffer sb3 = new StringBuffer();
        StringBuffer sb4 = new StringBuffer();
        if(null == zoneGb || CollectionUtils.isEmpty(zybDiseaseHeadList)) {
            return null;
        }
        if(type == 2) {
            sb1.append(" SELECT T4.RID AS INDUS_TYPE_ID ");
            sb2.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T1.INDUS_TYPE_ID = T4.RID ");
            sb3.append(" GROUP BY T4.RID, T4.CODE_NAME ");
        } else if(type == 3) {
            sb1.append(" SELECT T4.RID AS ECONOMY_ID ");
            sb2.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T1.ECONOMY_ID = T4.RID ");
            sb3.append(" GROUP BY T4.RID, T4.CODE_NAME ");
        }
        sb.append(sb1);
        for (TsSimpleCode t : zybDiseaseHeadList) {
            sb.append(", COUNT(CASE WHEN T3.CODE_LEVEL_NO LIKE '")
                    .append(t.getCodeLevelNo())
                    .append("%' THEN T.RID ELSE NULL END) ");
        }
        sb.append(" FROM TD_ZW_OCCDISCASE T ");
        sb.append(" LEFT JOIN TB_TJ_CRPT T1 ON T.CRPT_ID = T1.RID ");
        sb.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID ");
        if(!zybDiseaseSmall) {
            sb.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.OCCDISE_ID = T3.RID ");
        } else {
            sb.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.CFM_ZYB_SMALL_ID = T3.RID ");
        }
        sb.append(sb2);
        sb.append(" WHERE NVL(T.DEL_MARK, 0) = 0 ");
        sb.append(" AND T.STATE_MARK > 4 ");
        sb.append(" AND T.IS_DIS = 1 ");
        sb.append(" AND T1.INTER_PRC_TAG = 1 ");
        if (null != tlBdate) {
            sb.append(" AND T.TL_DATE >= TO_DATE('").append(DateUtils.formatDate(tlBdate, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
        }
        if (null != tlEdate) {
            sb.append(" AND T.TL_DATE <= TO_DATE('").append(DateUtils.formatDate(tlEdate, "yyyy-MM-dd")).append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
        }
        sb.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zoneGb)).append("%' ");
        sb.append(sb3);
        sb.append(sb4);
        return this.findSqlResultList(sb.toString());
    }

    @Transactional(readOnly = true)
    public List<TsSimpleCode> findTsSimpleCodeListByTypeNos(String typeNo,boolean ifZybDisease) {
        if (StringUtils.isNotBlank(typeNo)) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TsSimpleCode t ");
            sb.append(" where t.ifReveal= 1 and t.tsCodeType.codeTypeName in (").append(typeNo)
                    .append(") ");
            if (ifZybDisease) {
                sb.append(" and t.extendS1 = '1' ");
            }
            sb.append(" order by t.num,t.codeLevelNo ,t.codeNo ");
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }
	//mx
	/***
	 *  <p>方法描述：是否码表最末级</p>
     *
     * @MethodAuthor maox,2019年11月26日,findSimpleSubLevelCode
	 * @param typeNo
	 * @param levelCodeNo
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleSubLevelCode(String typeNo,String levelCodeNo) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (").append(typeNo)
					.append(") ");
			sb.append(" and t.ifReveal= 1");

			if (StringUtils.isNotBlank(levelCodeNo)) {
				sb.append(" and (");
				sb.append("  t.codeLevelNo like '").append(levelCodeNo).append(".").append("%'");
				sb.append(" )");
			}
			sb.append(" order by t.codeLevelNo ,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
	}
	
	/***
	 *  <p>方法描述：诊断机构职业病数据</p>
     *
     * @MethodAuthor maox,2019年11月26日,getOrgDiseaseData
	 * @param type
	 * @param zoneGb
	 * @param diseaseList
	 * @param searchStartTime
	 * @param searchEndTime
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<Object[]> getOrgDiseaseData(Integer type, String zoneGb,List<TsSimpleCode> diseaseList, Date searchStartTime, Date searchEndTime) {
		int zoneType = ZoneUtil.getZoneType(zoneGb);
		StringBuilder sb = new StringBuilder();
		String diseaseIds = "";
		if(null != diseaseList && diseaseList.size()>0){
			for(int i = 0;i<diseaseList.size();i++){
				TsSimpleCode code  = diseaseList.get(i);
				diseaseIds += code.getRid()+",";
			}
			diseaseIds = diseaseIds.substring(0, diseaseIds.length()-1);
		}
		sb.append("SELECT tt.rid,");
		if (2 == zoneType) {
			sb.append(" substr(zz.zone_gb,0,4)||'000000' as zone_gb ");
		} else if (3 == zoneType) {
			sb.append(" substr(zz.zone_gb,0,6)||'0000' as zone_gb ");
		} else if (4 == zoneType) {
			sb.append(" substr(zz.zone_gb,0,8)||'00' as zone_gb ");
		} else {
			sb.append(" zz.zone_gb ");
		}
		sb.append(",tt.ORG_NAME,nvl(aa.total,0)");
		if(null != diseaseList && diseaseList.size()>0){
			for(int i = 0;i<diseaseList.size();i++){
				sb.append(", nvl(aa.s").append(i).append(",0) , aa.ss").append(i);
			}
		}
		sb.append(" ,'s' from TD_ZW_DIAGORGINFO tt  left join ( SELECT T2.RID ,");
		
		if (2 == zoneType) {
			sb.append(" substr(t4.zone_gb,0,4)||'000000' as zone_gb ");
		} else if (3 == zoneType) {
			sb.append(" substr(t4.zone_gb,0,6)||'0000' as zone_gb ");
		} else if (4 == zoneType) {
			sb.append(" substr(t4.zone_gb,0,8)||'00' as zone_gb ");
		} else {
			sb.append(" t4.zone_gb ");
		}
		sb.append(",T2.ORG_NAME ");
		if(type == 1){
			sb.append(" ,sum(case when t.CFM_ZYB_SMALL_ID in (").append(diseaseIds).append(")then 1 else 0 end) AS total");
		}else{
			sb.append(" ,sum(case when t.OCCDISE_ID in (").append(diseaseIds).append(") then 1 else 0 end) AS total");
		}
		if(null != diseaseList && diseaseList.size()>0){
			for(int i = 0;i<diseaseList.size();i++){
				TsSimpleCode code  = diseaseList.get(i);
				if(type == 1){
					sb.append(" ,sum(case when t.CFM_ZYB_SMALL_ID =");
				}else{
					sb.append(" ,sum(case when t.OCCDISE_ID =");
				}
				sb.append(code.getRid()).append(" then 1 else 0 end) s").append(i);
				sb.append(",'s' ss").append(i);
			}
		}
		
		sb.append(" FROM  TD_ZW_DIAGORGINFO T2");
		sb.append(" LEFT JOIN TS_UNIT T1 ON T2.ORG_ID = T1.RID");
		sb.append(" LEFT JOIN TD_ZW_OCCDISCASE T  ON T.ACPTORG_ID = T1.RID ");
		if(type == 1){
			sb.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T3.RID = T.CFM_ZYB_SMALL_ID ");
		}else{
			sb.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T3.RID = T.OCCDISE_ID ");
		}
		sb.append(" LEFT JOIN TS_ZONE T4 ON T1.ZONE_ID = T4.RID ");
		sb.append(" WHERE 1 =1 AND T2.STATE = 1 AND T.STATE_MARK > 4 AND NVL(T.DEL_MARK,0)=0 AND T.IS_DIS = 1");
		sb.append(" AND T.TL_DATE >= to_date('");
		sb.append(DateUtils.formatDate(searchStartTime, "yyyy-MM-dd"));
		sb.append(" ','yyyy-MM-dd')");
		sb.append("  AND T.TL_DATE <= to_date('");
		sb.append(DateUtils.formatDate(searchEndTime, "yyyy-MM-dd"));
		sb.append(" ','yyyy-MM-dd')");
		sb.append(" and t4.zone_gb like '").append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
		sb.append(" GROUP BY T2.RID,T2.ORG_NAME,");
		if (2 == zoneType) {
			sb.append(" substr(t4.zone_gb,0,4)");
		} else if (3 == zoneType) {
			sb.append(" substr(t4.zone_gb,0,6)");
		} else if (4 == zoneType) {
			sb.append(" substr(t4.zone_gb,0,8)");
		} else {
			sb.append(" t4.zone_gb ");
		}
		sb.append(" ) aa on tt.rid = aa.rid ");
		sb.append(" LEFT JOIN TS_UNIT uu ON tt.ORG_ID = uu.RID");
		sb.append(" LEFT JOIN TS_ZONE zz ON uu.ZONE_ID = zz.RID ");
		sb.append(" where tt.STATE =1");
		sb.append(" and zz.zone_gb like '").append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
		sb.append(" order by 2,1");
		List<Object[]> result = em.createNativeQuery(sb.toString()).getResultList();
		return result;
	}
	
	
	
	/***
	 *  <p>方法描述：检查质量分析</p>
     *
     * @MethodAuthor maox,2019年12月7日,getQuality
	 * @param type
	 * @param zoneGb
	 * @param sdate
	 * @param edate
	 * @param searchBadRsnIds
	 * @return
	 */
	public List<Object[]> getQuality(short type, String zoneGb,Date sdate, Date edate,String searchBadRsnIds,boolean ifShowCheck){
		StringBuilder sql = new StringBuilder();
		int zoneType = ZoneUtil.getZoneType(zoneGb);
		
		if (1==type) {
			sql.append("select tt.ZONE_NAME ");
			sql.append(",tt.zone_gb,nvl(aa.total,0),nvl(aa.rhkTotal,0),nvl(aa.itemnum,0),aa.s1,nvl(aa.checknum+aa.itemnum,0),aa.s2,nvl(aa.jjznum,0),aa.s3,nvl(aa.ysjybnum,0),aa.s4,'s',nvl(aa.psntotal,0) ");
			sql.append(" from ts_zone tt ");
		}else{
			sql.append("select  ");
			if (2 == zoneType) {
				sql.append(" substr(tt.zone_gb,0,4)||'000000' as zone_gb ");
			} else if (3 == zoneType) {
				sql.append(" substr(tt.zone_gb,0,6)||'0000' as zone_gb ");
			} else if (4 == zoneType) {
				sql.append(" substr(tt.zone_gb,0,8)||'00' as zone_gb ");
			} else {
				sql.append(" tt.zone_gb ");
			}
			sql.append(" ,tt2.UNIT_NAME,nvl(aa.total,0),nvl(aa.rhkTotal,0),nvl(aa.itemnum,0),aa.s1,nvl(aa.checknum+aa.itemnum,0),aa.s2,nvl(aa.jjznum,0),aa.s3,nvl(aa.ysjybnum,0),aa.s4,'s',nvl(aa.psntotal,0) ");
			sql.append(" from TB_TJ_SRVORG tt2 ");
			sql.append(" left join ts_zone tt on tt.rid = tt2.ZONE_ID ");
		}
		sql.append("left join (select ");
		if (1 == type) {
			if (2 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,4)||'000000' as zone_gb ");
			} else if (3 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,6)||'0000' as zone_gb ");
			} else if (4 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,8)||'00' as zone_gb ");
			} else {
				sql.append(" t3.zone_gb ");
			}
		}else{
			sql.append(" tt1.rid");
		}
		sql.append(" ,count(distinct t.rid) as total,sum(t.IF_RHK) as rhkTotal ");
		sql.append(" ,sum(case when t.IF_INTEITM_LACK=0 and t.IF_RHK=0 then 1 else 0 end) itemnum ,'a' s1, ");
		if(ifShowCheck){
			sql.append(" sum(case when t5.rid is not null then 1 else 0 end) as checknum ,'a' s2");
		}else{
			sql.append(" 0  as checknum ,'a' s2 ");
		}
		sql.append(" ,sum(case when s1.bhk_id is not null and  t.IF_WRKTABU=1 then 1 else 0 end) as jjznum,'a' s3");
		sql.append(" ,sum(case when s.bhk_id is not null and  t.IF_TARGETDIS=1 then 1 else 0 end) as ysjybnum,'a' s4");
		sql.append(" ,count(distinct T.PERSON_ID) as psntotal");
		sql.append(" FROM TD_TJ_BHK T ");
		sql.append(" LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.CRPT_ID ");
		sql.append(" LEFT JOIN TB_TJ_SRVORG TT1  ON TT1.RID = T.BHKORG_ID ");
		sql.append(" LEFT JOIN TS_ZONE T3 ON T3.RID = TT1.ZONE_ID ");
		
		sql.append(" LEFT JOIN  (select a.bhk_id from TD_TJ_CONTRAINDLIST a  ");
		if(null != searchBadRsnIds ){
			sql.append(" where a.BADRSN_ID in (").append(searchBadRsnIds).append(" )");
		}
		sql.append(" group by a.bhk_id) s1 on s1.bhk_id=t.rid");
		
		sql.append(" LEFT JOIN  (select a.bhk_id from TD_TJ_SUPOCCDISELIST a  ");
		if(null != searchBadRsnIds ){
			sql.append(" where a.BADRSN_ID in (").append(searchBadRsnIds).append(" )");
		}
		sql.append(" group by a.bhk_id) s on s.bhk_id=t.rid");
		
		sql.append(" LEFT JOIN  (select a.bhk_id from TD_TJ_BADRSNS a  ");
		if(null != searchBadRsnIds ){
			sql.append(" where a.BADRSN_ID in (").append(searchBadRsnIds).append(" )");
		}
		sql.append(" group by a.bhk_id) s2 on s2.bhk_id=t.rid");
		
		if(ifShowCheck){
			sql.append(" LEFT JOIN TD_ZW_GBZ188_NOSTD T4 ON T.RID = T4.MAIN_ID AND T4.CHK_STD_FLAG=1 ");
			sql.append(" LEFT JOIN TD_ZW_BGK_LAST_STA T5 ON T4.RID = T5.BUS_ID AND T5.CART_TYPE = 5 AND T5.STATE = 7 ");
		}
		
		sql.append(" where t.BHK_TYPE in (3, 4) ");
		sql.append(" and t1.INTER_PRC_TAG = 1");
		sql.append(" and s2.bhk_id is not null ");
		if (null != sdate) {
			sql.append(" and t.BHK_DATE >=to_date('")
					.append(DateUtils.formatDate(sdate, "yyyy-MM-dd"))
					.append("','yyyy-MM-dd')");
		}
		if (null != edate) {
			sql.append(" and t.BHK_DATE <=to_date('")
					.append(DateUtils.formatDate(edate, "yyyy-MM-dd"))
					.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
		}
		sql.append(" and t3.zone_gb like '").append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
		
		sql.append(" group by ");
		if (1 == type) {
			if (2 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,4)");
			} else if (3 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,6)");
			} else if (4 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,8)");
			} else {
				sql.append(" t3.zone_gb ");
			}
			sql.append(")aa on aa.zone_gb = tt.zone_gb where 1=1 ");
		}else{
			sql.append(" tt1.rid)aa on aa.rid = tt2.rid where 1=1 and tt2.STOP_TAG = 1 ");
		}
		if (StringUtils.isNotBlank(zoneGb)) {
			sql.append(" and tt.zone_gb like '")
					.append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
			if (1 == type) {
				sql.append(" and tt.zone_type" + (zoneType >= 4 ? ">=" : ">"))
						.append(zoneType).append(" and tt.zone_type<=")
						.append(zoneType + 1);
			}
		}
		sql.append(" order by tt.zone_gb");
		List<Object[]> result = em.createNativeQuery(sql.toString()).getResultList();
		return result;
		
	}
	
	public Integer getTotalPerson(short type, String zoneGb,Date sdate, Date edate,String searchBadRsnIds,boolean ifShowCheck){
		StringBuilder sql = new StringBuilder();
		int zoneType = ZoneUtil.getZoneType(zoneGb);
		sql.append("select ");
		sql.append(" count(distinct T.PERSON_ID) as psntotal");
		sql.append(" FROM TD_TJ_BHK T ");
		sql.append(" LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.CRPT_ID ");
		sql.append(" LEFT JOIN TB_TJ_SRVORG TT1  ON TT1.RID = T.BHKORG_ID ");
		sql.append(" LEFT JOIN TS_ZONE T3 ON T3.RID = TT1.ZONE_ID ");
		
		sql.append(" LEFT JOIN  (select a.bhk_id from TD_TJ_CONTRAINDLIST a  ");
		if(null != searchBadRsnIds ){
			sql.append(" where a.BADRSN_ID in (").append(searchBadRsnIds).append(" )");
		}
		sql.append(" group by a.bhk_id) s1 on s1.bhk_id=t.rid");
		
		sql.append(" LEFT JOIN  (select a.bhk_id from TD_TJ_SUPOCCDISELIST a  ");
		if(null != searchBadRsnIds ){
			sql.append(" where a.BADRSN_ID in (").append(searchBadRsnIds).append(" )");
		}
		sql.append(" group by a.bhk_id) s on s.bhk_id=t.rid");
		
		sql.append(" LEFT JOIN  (select a.bhk_id from TD_TJ_BADRSNS a  ");
		if(null != searchBadRsnIds ){
			sql.append(" where a.BADRSN_ID in (").append(searchBadRsnIds).append(" )");
		}
		sql.append(" group by a.bhk_id) s2 on s2.bhk_id=t.rid");
		
		if(ifShowCheck){
			sql.append(" LEFT JOIN TD_ZW_GBZ188_NOSTD T4 ON T.RID = T4.MAIN_ID AND T4.CHK_STD_FLAG=1 ");
			sql.append(" LEFT JOIN TD_ZW_BGK_LAST_STA T5 ON T4.RID = T5.BUS_ID AND T5.CART_TYPE = 5 AND T5.STATE = 7 ");
		}
		
		sql.append(" where t.BHK_TYPE in (3, 4) ");
		sql.append(" and t1.INTER_PRC_TAG = 1");
		sql.append(" and s2.bhk_id is not null ");
		if (null != sdate) {
			sql.append(" and t.BHK_DATE >=to_date('")
					.append(DateUtils.formatDate(sdate, "yyyy-MM-dd"))
					.append("','yyyy-MM-dd')");
		}
		if (null != edate) {
			sql.append(" and t.BHK_DATE <=to_date('")
					.append(DateUtils.formatDate(edate, "yyyy-MM-dd"))
					.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
		}
		sql.append(" and t3.zone_gb like '").append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
		sql.append(" and t3.zone_type >").append(zoneType);
	
		return super.findCountBySql(sql.toString());
		
	}
	
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findZybByTypeId(String typeNo,boolean ifAllUse) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (").append(typeNo)
					.append(") ");
			if (ifAllUse) {
				sb.append(" and t.ifReveal= 1");
			}
			sb.append(" and t.extendS1 =1");
			sb.append(" order by t.num,t.codeLevelNo ,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}
	
	/***
	 *  <p>方法描述：疾病趋势</p>
     *
     * @MethodAuthor maox,2019年12月5日,getDuringYearDiseaseDatas
	 * @param dimension
	 * @param zoneGb
	 * @param orgName
	 * @param syear
	 * @param eyear
	 * @param selectBigHarmIds
	 * @return
	 */
	public List<Object[]> getDuringYearDiseaseDatas(Integer dimension,
			 String zoneGb, String orgName, Integer syear,
			Integer eyear,String selectBigHarmIds,String code) {
		if (null == dimension) {
			return null;
		}
		StringBuffer sql = new StringBuffer();
		sql.append("select ");
		sql.append(" count(distinct t.rid) as totals");
		sql.append(",to_number(to_char(t.bhk_date,'yyyy'))");
		sql.append(" from td_tj_bhk t ");
		sql.append(" left join TB_TJ_CRPT t1  on t1.rid = t.crpt_id");
		sql.append(" left join TB_TJ_SRVORG tt1  on tt1.rid = t.BHKORG_ID ");
		if(2 == dimension){
			sql.append(" left join ts_zone t3 on t3.rid = tt1.zone_id ");
		}else{
			sql.append(" left join ts_zone t3 on t3.rid = t1.zone_id ");
		}
		sql.append(" left join ts_simple_code t4 on t1.INDUS_TYPE_ID = t4.rid");
		sql.append(" left join ts_simple_code t5 on t1.ECONOMY_ID = t5.rid ");
		sql.append(" left join ts_simple_code t6 on t1.CRPT_SIZE_ID = t6.rid ");
		sql.append(" where t1.INTER_PRC_TAG = 1");
		sql.append(" and t.BHK_TYPE in (3,4) and IF_TARGETDIS=1");
		if (null != syear) {
			sql.append(" and to_number(to_char(t.bhk_date,'yyyy')) >=").append(
					syear);
		}
		if (null != eyear) {
			sql.append(" and to_number(to_char(t.bhk_date,'yyyy')) <=").append(
					eyear);
		}
		if (StringUtils.isNotBlank(zoneGb)) {
			sql.append(" and t3.zone_gb like '")
					.append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
		}
		if(2 == dimension){
			if (null != orgName) {
				sql.append(" and tt1.UNIT_NAME ='").append(orgName);
				sql.append("' and tt1.STOP_TAG = 1");
			}
		}
		if(3 == dimension){
			sql.append(" and t4.code_level_no like '").append(code).append("%'");
		}else if(4 == dimension){
			sql.append(" and t5.code_level_no like '").append(code).append("%'");
		}else if(5 == dimension){
			sql.append(" and t6.code_level_no like '").append(code).append("%'");
		}
		if (StringUtils.isNotBlank(selectBigHarmIds)) {
			sql.append(" and exists(select 1 from TD_TJ_BADRSNS badRsn where badRsn.BHK_ID = t.rid");
			sql.append(" and badRsn.BADRSN_ID in (").append(selectBigHarmIds).append(")").append(")");
		}
		sql.append(" group by to_char(t.bhk_date,'yyyy')");
		sql.append(" order by to_char(t.bhk_date,'yyyy')");
		return this.findSqlResultList(sql.toString());
	}
	
	/***
	 *  <p>方法描述：健康检查疾病分析</p>
     *
     * @MethodAuthor maox,2019年12月3日,getcheckAnalyseDatas
	 * @param dimension 分析维度
	 * @param analyseTerms 统计项
	 * @param headList 表格右边表头
	 * @param zoneGb
	 * @param sdate
	 * @param edate
	 * @param selectBigHarmIds 选择的危害因素
	 * @return
	 */
	public List<Object[]> getcheckAnalyseDatas(Integer dimension,Integer analyseTerms,
			List<TsSimpleCode> headList, String zoneGb, Date sdate, Date edate,
			String selectBigHarmIds){
		if (null == dimension || null == analyseTerms) {
			return null;
		}
		StringBuffer sql = new StringBuffer();
		int zoneType = ZoneUtil.getZoneType(zoneGb);
		if (1==dimension) {
			sql.append("select tt.ZONE_NAME ");
			sql.append(",tt.zone_gb,aa.*");
			sql.append(" from ts_zone tt ");
		}else if(2==dimension){
			sql.append("select ");
			if (2 == zoneType) {
				sql.append(" substr(tt.zone_gb,0,4)||'000000' as zone_gb ");
			} else if (3 == zoneType) {
				sql.append(" substr(tt.zone_gb,0,6)||'0000' as zone_gb ");
			} else if (4 == zoneType) {
				sql.append(" substr(tt.zone_gb,0,8)||'00' as zone_gb ");
			} else {
				sql.append(" tt.zone_gb ");
			}
			sql.append(" ,tt2.UNIT_NAME,aa.*");
			sql.append(" from TB_TJ_SRVORG tt2 ");
			sql.append(" left join ts_zone tt on tt.rid = tt2.ZONE_ID ");
		}else{
			sql.append(" select a.code_name ,a.code_level_no,aa.*from ts_simple_code a left join ts_code_type b on a.code_type_id =  b.rid");
			
		}
		sql.append(" left join (");
		sql.append("select ");
		
		if (1 == dimension) {
			if (2 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,4)||'000000' as zone_gb ");
			} else if (3 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,6)||'0000' as zone_gb ");
			} else if (4 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,8)||'00' as zone_gb ");
			} else {
				sql.append(" t3.zone_gb ");
			}
		}else if(2 == dimension){
			sql.append(" tt1.rid");
		}else if (3 == dimension) {
			sql.append(" SUBSTR (t4.code_level_no, 1, INSTR (t4.code_level_no, '.', 1, 1) - 1) AS codeNo");
		}else if (4 == dimension) {
			sql.append(" SUBSTR (t5.code_level_no, 1, INSTR (t5.code_level_no, '.', 1, 1) - 1) AS codeNo ");
		}else if (5 == dimension) {
			sql.append(" t6.code_no AS codeNo");
		}
		sql.append("  ,count(distinct t.rid) as totals");
		sql.append(" ,count(distinct case when t.sex = '男' then t.rid else null end) as mans, ");
		sql.append(" count(distinct case when t.sex = '女' then t.rid else null end) as womans, ");
		sql.append(" case when (sum(case when t.TCHBADRSNTIM is not null and t.TCHBADRSNMONTH is not null then 1 else 0 end)) >0 then  max(nvl(t.TCHBADRSNTIM, 0) + nvl(t.TCHBADRSNMONTH, 0) / 12) else -1 end as maxbad,");
		sql.append(" case when (sum(case when t.TCHBADRSNTIM is not null and t.TCHBADRSNMONTH is not null then 1 else 0 end)) >0 then   min(nvl(t.TCHBADRSNTIM, 100) + nvl(t.TCHBADRSNMONTH, 0) / 12) else -1 end as minbad, ");
		sql.append(" case when (sum(case when t.TCHBADRSNTIM is not null and t.TCHBADRSNMONTH is not null then 1 else 0 end)) >0 then  ");
		sql.append("	sum(nvl(t.TCHBADRSNTIM, 0) + nvl(t.TCHBADRSNMONTH, 0) / 12) /nvl(sum(case when t.TCHBADRSNTIM is not null and t.TCHBADRSNMONTH is not null then 1 else 0 end),1) else -1 end,");
		sql.append(" case when (sum(case when t.WRKLNT is not null and t.WRKLNTMONTH is not null then 1 else 0 end)) >0 then  	");
		sql.append(" sum(nvl(t.WRKLNT, 0) + nvl(t.WRKLNTMONTH, 0) / 12) /nvl(sum(case when t.WRKLNT is not null and t.WRKLNTMONTH is not null then 1 else 0 end),1) else -1 end ");
		if (null != headList && headList.size() > 0) {
			for (TsSimpleCode t : headList) {
				if (1==analyseTerms) {//在岗状态
					sql.append(",count(distinct case when t.ONGUARD_STATEID="
							+ t.getRid() + " then  t.rid else null end)");
				}else if (2==analyseTerms) {
					sql.append(",count(distinct case when t7.code_level_no like '"
							+ t.getCodeLevelNo()+ (t.getCodeLevelNo().equals(t.getCodeNo()) ? ".": "")+ "%' ");
					sql.append(" or  t7.code_level_no like '%,"+ t.getCodeLevelNo()+ (t.getCodeLevelNo().equals(t.getCodeNo()) ? ".": "")+ "%' ");
					sql.append(" then  t.person_id else null end)");
				}
			}
		}
		sql.append(",sum(nvl(TCHBADRSNTIM,0)+nvl(TCHBADRSNMONTH,0)/12),sum(nvl(WRKLNT,0)+nvl(WRKLNTMONTH,0)/12)");
		sql.append(" ,nvl(sum(case when t.TCHBADRSNTIM is not null and t.TCHBADRSNMONTH is not null then 1 else 0 end),0) badpsnnums");
		sql.append(" ,nvl(sum(case when t.WRKLNT is not null and t.WRKLNTMONTH is not null then 1 else 0 end),0) workpsnnums");
		sql.append(" from TD_TJ_BHK t ");
		sql.append(" left join TB_TJ_CRPT t1 on t1.rid = t.crpt_id ");
		sql.append(" left join TB_TJ_SRVORG tt1  on tt1.rid = t.BHKORG_ID ");
		if(2 == dimension){
			sql.append(" left join ts_zone t3 on t3.rid = tt1.zone_id ");
		}else{
			sql.append(" left join ts_zone t3 on t3.rid = t1.zone_id ");
		}
		
		sql.append(" left join ts_simple_code t4 on t1.INDUS_TYPE_ID = t4.rid");
		sql.append(" left join ts_simple_code t5 on t1.ECONOMY_ID = t5.rid ");
		sql.append(" left join ts_simple_code t6 on t1.CRPT_SIZE_ID = t6.rid ");
		sql.append(" inner join (select mm.bhk_id ,wm_concat(nn.code_level_no) as code_level_no from TD_TJ_BADRSNS mm ");
		sql.append(" left join ts_simple_code nn on mm.badrsn_id = nn.rid ");
		if (2==analyseTerms) {
			if (StringUtils.isNotBlank(selectBigHarmIds)) {
				sql.append(" where mm.BADRSN_ID in (").append(selectBigHarmIds).append(")");
			}
		}
		sql.append(" group by mm.bhk_id) t7 on t7.BHK_ID = t.rid");
		sql.append(" where t.BHK_TYPE in (3,4)");
		sql.append(" and t1.INTER_PRC_TAG = 1 and t.IF_TARGETDIS=1");
		if (null != sdate) {
			sql.append(" and t.BHK_DATE >=to_date('")
					.append(DateUtils.formatDate(sdate, "yyyy-MM-dd"))
					.append("','yyyy-MM-dd')");
		}
		if (null != edate) {
			sql.append(" and t.BHK_DATE <=to_date('")
					.append(DateUtils.formatDate(edate, "yyyy-MM-dd"))
					.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
		}
		sql.append(" and t3.zone_gb like '").append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
		
		sql.append(" group by ");
		if (1 == dimension) {
			if (2 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,4)");
			} else if (3 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,6)");
			} else if (4 == zoneType) {
				sql.append(" substr(t3.zone_gb,0,8)");
			} else {
				sql.append(" t3.zone_gb ");
			}
			sql.append(")aa on aa.zone_gb = tt.zone_gb where 1=1");
		}else if (2 == dimension) {
			sql.append(" tt1.rid)aa on aa.rid = tt2.rid where 1=1 and tt2.STOP_TAG = 1");
		}else if (3 == dimension) {
			sql.append(" SUBSTR (t4.code_level_no, 1, INSTR (t4.code_level_no, '.', 1, 1) - 1)");
			sql.append(" )aa on aa.codeNo = a.code_level_no");
		}else if (4 == dimension) {
			sql.append(" SUBSTR (t5.code_level_no, 1, INSTR (t5.code_level_no, '.', 1, 1) - 1)");
			sql.append(" )aa on aa.codeNo = a.code_level_no");
		}else if (5 == dimension) {
			sql.append(" t6.code_no");
			sql.append(" )aa on aa.codeNo = a.code_level_no");
		}
		if(1 == dimension || 2 == dimension){
			if (StringUtils.isNotBlank(zoneGb)) {
				sql.append(" and tt.zone_gb like '")
						.append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
				if (1 == dimension) {
					sql.append(" and tt.zone_type" + (zoneType >= 4 ? ">=" : ">"))
							.append(zoneType).append(" and tt.zone_type<=")
							.append(zoneType + 1);
				}
			}
			sql.append(" order by tt.zone_gb"
					+ (1 != dimension ? ",aa.rid, tt2.rid" : ""));
		}else{
			sql.append(" where b.code_type_name =  ");
			if(3==dimension){
				sql.append(5002);
			}else if(4==dimension){
				sql.append(5003);
			}else{
				sql.append(5004);
			}
			sql.append(" and a.code_level_no not like '%.%'");
			sql.append(" order by a.num,a.code_no");
		}
		
		List<Object[]> result = em.createNativeQuery(sql.toString()).getResultList();
		return result;
		
	}
	
	/***
	 *  <p>方法描述：诊断机构上报情况</p>
     *
     * @MethodAuthor maox,2019年12月24日,getTdOrgReport
	 * @param dimension
	 * @param zoneGb
	 * @param searchStartDate
	 * @param searchEndDate
	 * @return
	 */
	public List<Object[]> getTdOrgReport(Integer dimension,String zoneGb,Date searchStartDate,Date searchEndDate ){
		StringBuffer sql = new StringBuffer();
		int zoneType = ZoneUtil.getZoneType(zoneGb);
		
		sql.append("select aa.zone_name,zz.* from  (");
		sql.append("select ");
		if (2 == zoneType) {
			sql.append(" substr(z.zone_gb,0,4)||'000000' as zone_gb ");
		} else if (3 == zoneType || 4 == zoneType || 5 == zoneType) {
			sql.append(" substr(z.zone_gb,0,6)||'0000' as zone_gb ");
		}
		/*else if (4 == zoneType) {
			sql.append(" substr(z.zone_gb,0,8)||'00' as zone_gb ");
		} else {
			sql.append(" tt.zone_gb ");
		}*/
		sql.append(",t.ORG_NAME");
		if(dimension ==3){
			sql.append("  ,to_number(to_char( to_date('").append(DateUtils.formatDate(searchEndDate, "yyyy-MM-dd")).append("','yyyy-MM-dd'),'yyyy')||");
			sql.append(" to_char( to_date('").append(DateUtils.formatDate(searchEndDate, "yyyy-MM-dd")).append("','yyyy-MM-dd'),'q'))");
			sql.append(" - to_number(to_char( to_date('").append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd")).append("','yyyy-MM-dd'),'yyyy')||");
			sql.append(" to_char( to_date('").append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd")).append("','yyyy-MM-dd'),'q'))+1 as totalnum");
		}else{
			sql.append(",nvl(t8.totalnum,0)");
		}
		sql.append(" ,nvl(t8.sbnum,0), case when t8.totalnum >0 then  round(t8.sbnum * 100 / t8.totalnum, 2) else 0 end as sbpercent");
		sql.append(" ,nvl(t8.sbwcs,0) , case when t8.totalnum >0 then round(t8.sbwcs * 100 / t8.totalnum, 2) else 0 end as wcspercent");
		sql.append("  ,nvl(t8.sbend,0) , case when t8.sbnum >0 then round(t8.sbend * 100 / t8.sbnum, 2) else 0 end as endpercent ");
		sql.append(" ,nvl(t8.notintime,0) ,  case when t8.sbnum >0 then round(t8.notintime * 100 / t8.sbnum, 2)  else 0 end as notintimepercent");
		sql.append(" , nvl(t8.backnum ,0), case when t8.sbnum > 0 then  round(t8.backnum * 100 / t8.sbnum, 2)else 0 end  as backpercent ");
		sql.append(" from TD_ZW_DIAGORGINFO t");
		sql.append(" left join ts_unit u on t.ORG_ID = u.rid");
		sql.append(" left join ts_zone z on u.zone_id = z.rid ");
		sql.append(" left join (");
		sql.append("   select  t3.rid orgid,");
		if( dimension ==1){
			sql.append(" sum(case when s1.extends4=1 then 1 else 0 end ) totalnum,");
		}else if( dimension ==2){
			sql.append(" sum(case when s1.extends4 is null then 1 else 0 end ) totalnum, ");	
		}else{
			sql.append("  to_number(to_char( to_date('").append(DateUtils.formatDate(searchEndDate, "yyyy-MM-dd")).append("','yyyy-MM-dd'),'yyyy')||");
			sql.append(" to_char( to_date('").append(DateUtils.formatDate(searchEndDate, "yyyy-MM-dd")).append("','yyyy-MM-dd'),'q'))");
			sql.append(" - to_number(to_char( to_date('").append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd")).append("','yyyy-MM-dd'),'yyyy')||");
			sql.append(" to_char( to_date('").append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd")).append("','yyyy-MM-dd'),'q'))+1 as totalnum,");
		}
		sql.append("			sum(case when t4.bus_id is not null then 1 else 0 end) sbnum, ");	
		sql.append("			sum (case when t5.state >0 and ((s.IF_CITY_DIRECT=1 and t5.state != 4) or(s.IF_CITY_DIRECT is null and t5.state != 2) ) then 1 else 0 end) sbwcs, ");	
		sql.append("			sum (case when t5.state =7 then 1 else 0 end) sbend, ");	
		sql.append("			sum(case when t6.bus_id is not null then 1 else 0 end) notintime, ");	
		sql.append("			sum(case when t7.bus_id is not null then 1 else 0 end) backnum ");	
		if( dimension ==1){
			sql.append("		from TD_ZW_OCCDISCASE t1");
			sql.append("		left join TD_ZW_PNEUMSIS_CARD tt on tt.main_id = t1.rid");	
		}else if( dimension ==2){
			sql.append("		from TD_ZW_OCCDISCASE t1");
			sql.append("		left join TD_ZW_OCC_DIS_CARD tt on tt.main_id = t1.rid");	
		}else{
			sql.append("		from TD_ZW_ZD_RPT tt ");	
		}
		if(dimension <3){
			sql.append("		left join ts_simple_code s1 on t1.CFM_ZYB_SMALL_ID = s1.rid ");
			sql.append("		left join ts_unit t2 on t1.ACPTORG_ID = t2.rid ");	
			sql.append("		left join ts_zone s on t2.zone_id = s.rid ");
			sql.append("		left join TD_ZW_DIAGORGINFO t3 on t3.org_id = t2.rid ");
		}else{
			sql.append("  		left join TD_ZW_DIAGORGINFO t3 on t3.rid = tt.FILL_UNIT_ID ");
			sql.append("		left join ts_unit t2 on t3.org_id = t2.rid ");
			sql.append("		left join ts_zone s on t2.zone_id = s.rid ");
		}
			
		sql.append("		left join  (select a.bus_id from TD_ZW_BGK_FLOW a where  a.OPER_FLAG = 12   and a.CART_TYPE = ");	
		if( dimension ==1){
			sql.append(3);	
		}else if( dimension ==2){
			sql.append(4);	
		}else{
			sql.append(7);	
		}
		sql.append("		group by a.bus_id)t4 on  tt.rid = t4.BUS_ID  left join TD_ZW_BGK_LAST_STA t5 on t5.cart_type =  ");	
		if( dimension ==1){
			sql.append(3);	
		}else if( dimension ==2){
			sql.append(4);	
		}else{
			sql.append(7);	
		}
		sql.append("		and tt.rid = t5.bus_id and t5.state >0 left join  (select a.bus_id from TD_ZW_BGK_FLOW a where  a.OPER_FLAG in(11,12,13) and a.IF_IN_TIME= 0 and a.CART_TYPE =   ");
		if( dimension ==1){
			sql.append(3);	
		}else if( dimension ==2){
			sql.append(4);	
		}else{
			sql.append(7);	
		}
		sql.append("	group by a.bus_id) t6 on   tt.rid = t6.BUS_ID left join  (select a.bus_id from TD_ZW_BGK_FLOW a where  a.OPER_FLAG in(11,13) and  a.CART_TYPE =   ");
		if( dimension ==1){
			sql.append(3);	
		}else if( dimension ==2){
			sql.append(4);	
		}else{
			sql.append(7);	
		}
		sql.append("	group by a.bus_id) t7 on  tt.rid = t7.BUS_ID   where 1 =1");	
		if(dimension <3){
			sql.append("	and t1.IS_DIS=1 	and t1.state_mark >4 ");	
			sql.append("		and (t1.del_mark is null or t1.del_mark =0) ");
			//日期
			if (null != searchStartDate) {
				sql.append(" and t1.TL_DATE >=to_date('")
						.append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd"))
						.append("','yyyy-MM-dd')");
			}
			if (null != searchEndDate) {
				sql.append(" and t1.TL_DATE <=to_date('")
						.append(DateUtils.formatDate(searchEndDate, "yyyy-MM-dd"))
						.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
			}
		}else{
			//季度日期
			if (null != searchStartDate) {
				sql.append(" and to_number(to_char( to_date('").append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd")).append("','yyyy-MM-dd'),'yyyy')||");
				sql.append(" to_char( to_date('").append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd")).append("','yyyy-MM-dd'),'q'))");
				sql.append(" <= to_number(to_char(tt.RPT_YEAR)||to_char(tt.RPT_QUARTER))");
			}
			if (null != searchEndDate) {
				sql.append(" and to_number(to_char( to_date('").append(DateUtils.formatDate(searchEndDate, "yyyy-MM-dd")).append("','yyyy-MM-dd'),'yyyy')||");
				sql.append(" to_char( to_date('").append(DateUtils.formatDate(searchEndDate, "yyyy-MM-dd")).append("','yyyy-MM-dd'),'q'))");
				sql.append(" >= to_number(to_char(tt.RPT_YEAR)||to_char(tt.RPT_QUARTER))");
			}
		}
		
		sql.append("		 group by t3.rid ) t8 on t8.orgid = t.rid");	
		sql.append(" where t.state = 1 ) zz");
		sql.append(" left join  ts_zone aa  on aa.zone_gb = zz.zone_gb ");
		if (StringUtils.isNotBlank(zoneGb)) {
			sql.append(" where aa.zone_gb like '").append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
				sql.append(" and aa.zone_type" + (zoneType == 4 ? "=" : ">"))
						.append(zoneType).append(" and aa.zone_type<=")
						.append(zoneType + 1);
		}
		sql.append(" order by 2");
		List<Object[]> result = em.createNativeQuery(sql.toString()).getResultList();
		return result;
	}
	
	public Map<String,String> getZoneMap(){
		Map<String,String> zoneMap = new HashMap<String, String>();
		StringBuffer sql = new StringBuffer();
		sql.append(" select z.zone_gb,case  when z.ZONE_TYPE > 3 then  substr(z.FULL_NAME, instr(z.full_name, '_', 1, z.ZONE_TYPE - 3) + 1) else z.zone_name end");
		sql.append("   from ts_zone z");
		List<Object[]> result = this.findSqlResultList(sql.toString());
		if(null != result && result.size()>0){
			for(Object[] arr:result){
				zoneMap.put(arr[0].toString(), arr[1].toString());
			}
		}
		return zoneMap;
	}
	
	public Map<String,String> getZoneNameMap(){
		Map<String,String> zoneMap = new HashMap<String, String>();
		StringBuffer sql = new StringBuffer();
		sql.append(" select z.zone_gb,z.zone_name");
		sql.append("   from ts_zone z");
		List<Object[]> result = this.findSqlResultList(sql.toString());
		if(null != result && result.size()>0){
			for(Object[] arr:result){
				zoneMap.put(arr[0].toString(), arr[1].toString());
			}
		}
		return zoneMap;
	}
	
	/***
	 *  <p>方法描述：质量分析线表</p>
     *
     * @MethodAuthor maox,2019年12月10日,getDuringYearAnalys
	 * @param type
	 * @param zoneGb
	 * @param syear
	 * @param eyear
	 * @param selectBigHarmIds
	 * @param ifCheck
	 * @return
	 */
	public List<Object[]> getDuringYearAnalys(short type,
			 String zoneGb, Integer syear,Integer eyear,String selectBigHarmIds,boolean ifCheck) {
		StringBuffer sql = new StringBuffer();
		int zoneType = ZoneUtil.getZoneType(zoneGb);
		sql.append("");
		sql.append("select ");
		sql.append("to_number(to_char(t.bhk_date,'yyyy'))");
		sql.append(" ,count(distinct t.rid) as total,sum(t.IF_RHK) as rhkTotal");
		sql.append(" ,sum(case when t.IF_INTEITM_LACK=0 and t.IF_RHK=0 then 1 else 0 end) itemnum");
		if(ifCheck){
			sql.append(" ,(sum(case when t5.rid is not null then 1 else 0 end)+sum(case when t.IF_INTEITM_LACK=0 and t.IF_RHK=0 then 1 else 0 end)) as checknum ");
		}else{
			sql.append(" ,'a' ");
		}
		sql.append(" ,sum(case when t.IF_WRKTABU=1 and s1.bhk_id is not null  then 1 else 0 end) as jjznum");
		sql.append(" ,sum(case when t.IF_TARGETDIS=1 and s.bhk_id is not null then 1 else 0 end) as ysjybnum");
		sql.append(" from td_tj_bhk t ");
		sql.append(" left join TB_TJ_CRPT t1  on t1.rid = t.crpt_id");
		sql.append(" left join TB_TJ_SRVORG tt1  on tt1.rid = t.BHKORG_ID ");
			sql.append(" left join ts_zone t3 on t3.rid = tt1.zone_id ");
		if(ifCheck){
			sql.append(" LEFT JOIN TD_ZW_GBZ188_NOSTD T4 ON T.RID = T4.MAIN_ID AND T4.CHK_STD_FLAG = 1");
			sql.append(" LEFT JOIN TD_ZW_BGK_LAST_STA T5 ON T4.RID = T5.BUS_ID AND T5.CART_TYPE = 5 AND T5.STATE = 7");	
		}
		sql.append(" LEFT JOIN  (select a.bhk_id from TD_TJ_CONTRAINDLIST a  ");
		if(null != selectBigHarmIds ){
			sql.append(" where a.BADRSN_ID in (").append(selectBigHarmIds).append(" )");
		}
		sql.append(" group by a.bhk_id) s1 on s1.bhk_id=t.rid");
		
		sql.append(" LEFT JOIN  (select a.bhk_id from TD_TJ_SUPOCCDISELIST a  ");
		if(null != selectBigHarmIds ){
			sql.append(" where a.BADRSN_ID in (").append(selectBigHarmIds).append(" )");
		}
		sql.append(" group by a.bhk_id) s on s.bhk_id=t.rid");
		
		sql.append(" where t1.INTER_PRC_TAG = 1");
		sql.append(" and t.BHK_TYPE in (3,4)");
		if (null != syear) {
			sql.append(" and to_number(to_char(t.bhk_date,'yyyy')) >=").append(
					syear);
		}
		if (null != eyear) {
			sql.append(" and to_number(to_char(t.bhk_date,'yyyy')) <=").append(
					eyear);
		}
		if (StringUtils.isNotBlank(zoneGb)) {
			sql.append(" and t3.zone_gb like '")
					.append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
			if(type != 0){
				sql.append(" and t3.zone_type >").append(zoneType);
			}
			
		}
		
		if (StringUtils.isNotBlank(selectBigHarmIds)) {
			sql.append(" and exists(select 1 from TD_TJ_BADRSNS badRsn where badRsn.BHK_ID = t.rid");
			sql.append(" and badRsn.BADRSN_ID in (").append(selectBigHarmIds).append(")").append(")");
		}
		sql.append(" group by to_char(t.bhk_date,'yyyy')");
		sql.append(" order by to_char(t.bhk_date,'yyyy')");
		return this.findSqlResultList(sql.toString());
	}
	
	@Transactional(readOnly = true)
	public List<TsZone> findZoneListUpStreet(String zoneCode) {
		StringBuilder sb = new StringBuilder(" select new TsZone(t.rid, t.zoneGb, t.zoneName, t.zoneType,t.realZoneType) from TsZone t where t.ifReveal='1' ");
		sb.append(" and t.zoneGb like '").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		sb.append(" and t.zoneType < 5");
		sb.append(" order by t.zoneGb ");
		return em.createQuery(sb.toString()).getResultList();
	}
	
	/**
 	 * <p>方法描述：获取企业各地区体检情况</p>
 	 * @MethodAuthor qrr,2019年12月16日,findCrptAnaly
	 * */
	public List<Object[]> findCrptBhkDataAnaly(String searchZoneCode,
			Date searchStartDate, Date searchEndDate,
			String searchIndusTypeIds, String searchEconomyIds,
			String searchCitySizeIds, String searchOnguardIds,
			String selectBigHarmIds) {
		if (StringUtils.isBlank(searchZoneCode)) {
			return new ArrayList<>();
		}
		StringBuffer sql = new StringBuffer();
		sql.append(" select zone.ZONE_NAME");
		sql.append(",NVL(A.crpts,0) AS crpts,NVL(A.persons,0) AS persons,NVL(A.abnormals,0) AS abnormals,NVL(A.rechecks,0) AS rechecks,NVL(A.jjzs,0) AS jjzs,NVL(A.zybs,0) AS zybs,zone.ZONE_GB");
		sql.append(" from ts_zone zone left join(");
		sql.append("select count(distinct t.CRPT_ID) as crpts,count(distinct t.PERSON_ID) as persons");
		sql.append(",count(distinct case when t4.EXTENDS2=2 OR T.IF_WRKTABU=1 OR T.IF_TARGETDIS = 1 THEN t.PERSON_ID ELSE null END) as abnormals");
		sql.append(",count(distinct case when t4.EXTENDS2=2 THEN t.PERSON_ID ELSE null END) AS rechecks");
		sql.append(",count(distinct case when T.IF_WRKTABU=1 THEN t.PERSON_ID ELSE null END) AS jjzs");
		sql.append(",count(case when T.IF_TARGETDIS = 1 THEN t.PERSON_ID ELSE null END) AS zybs");
		int zoneType = ZoneUtil.getZoneType(searchZoneCode);
		if (2 == zoneType) {
			sql.append(",concat(substr(t2.zone_gb, 0, 4), '000000') zone_gb");
		} else if (3 == zoneType) {
			sql.append(",concat(substr(t2.zone_gb, 0, 6), '0000') zone_gb");
		} else if (4 == zoneType) {
			sql.append(",concat(substr(t2.zone_gb, 0, 8), '00') zone_gb");
		} else {
			sql.append(",t2.zone_gb");
		}
		sql.append(" from td_tj_bhk t");
		sql.append(" left join tb_tj_crpt t1 on t1.rid = t.crpt_id");
		sql.append(" left join ts_zone t2 on t2.rid = t1.zone_id");
		sql.append(" left join TD_TJ_MHKRST t3 on t.rid = t3.BHK_ID");
		sql.append(" left join TS_SIMPLE_CODE t4 on t4.rid = t3.BHKRST_ID");
		sql.append(" where t.bhk_type in (3,4) and t1.inter_prc_tag = 1 ");
		if (null != searchStartDate) {
			sql.append(" and t.bhk_date >=to_date('")
					.append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd"))
					.append("','yyyy-MM-dd')");
		}
		if (null != searchEndDate) {
			sql.append(" and t.bhk_date <=to_date('")
					.append(DateUtils.formatDate(searchEndDate, "yyyy-MM-dd"))
					.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
		}
		if (StringUtils.isNotBlank(searchIndusTypeIds)) {
			sql.append(" and t1.INDUS_TYPE_ID in (").append(searchIndusTypeIds)
					.append(")");
		}
		if (StringUtils.isNotBlank(searchEconomyIds)) {
			sql.append(" and t1.ECONOMY_ID in (").append(searchEconomyIds)
					.append(")");
		}
		if (StringUtils.isNotBlank(searchCitySizeIds)) {
			sql.append(" and t1.CRPT_SIZE_ID in (").append(searchCitySizeIds)
					.append(")");
		}
		if (StringUtils.isNotBlank(searchOnguardIds)) {
			sql.append(" and t.ONGUARD_STATEID in (").append(searchOnguardIds)
					.append(")");
		}
		if (StringUtils.isNotBlank(selectBigHarmIds)) {
			sql.append(
					" and exists (select 1 from TD_TJ_BADRSNS tt where tt.BHK_ID = t.rid and tt.BADRSN_ID in (")
					.append(selectBigHarmIds).append(")").append(")");
		}
		sql.append(" group by ");
		if (2 == zoneType) {
			sql.append(" concat(substr(t2.zone_gb, 0, 4), '000000')");
		} else if (3 == zoneType) {
			sql.append(" concat(substr(t2.zone_gb, 0, 6), '0000')");
		} else if (4 == zoneType) {
			sql.append(" concat(substr(t2.zone_gb, 0, 8), '00')");
		} else {
			sql.append(" t2.zone_gb");
		}
		sql.append(")A ON A.zone_gb = ZONE.zone_gb WHERE 1=1");
		if (StringUtils.isNotBlank(searchZoneCode)) {
			sql.append(" and ZONE.zone_gb like '")
					.append(ZoneUtil.zoneSelect(searchZoneCode)).append("%'");
			sql.append(" and ZONE.zone_type" + (zoneType >= 4 ? ">=" : ">"))
					.append(zoneType).append(" and ZONE.zone_type<=")
					.append(zoneType + 1);
		}
		sql.append(" order by abnormals desc,zone_gb");
		return this.findBySql(sql.toString());
	}
	/**
 	 * <p>方法描述：获取企业各地区体检情况</p>
 	 * @MethodAuthor qrr,2019年12月16日,findCrptAnaly
	 * */
	public List<Object[]> findHjCrptBhkData(String searchZoneCode,
			Date searchStartDate, Date searchEndDate,
			String searchIndusTypeIds, String searchEconomyIds,
			String searchCitySizeIds, String searchOnguardIds,
			String selectBigHarmIds) {
		if (StringUtils.isBlank(searchZoneCode)) {
			return new ArrayList<>();
		}
		StringBuffer sql = new StringBuffer();
		sql.append("select '合计',count(distinct t.CRPT_ID) as crpts,count(distinct t.PERSON_ID) as persons");
		sql.append(",count(distinct case when t4.EXTENDS2=2 OR T.IF_WRKTABU=1 OR T.IF_TARGETDIS = 1 THEN t.PERSON_ID ELSE null END) as abnormals");
		sql.append(",count(distinct case when t4.EXTENDS2=2 THEN t.PERSON_ID ELSE null END) AS rechecks");
		sql.append(",count(distinct case when T.IF_WRKTABU=1 THEN t.PERSON_ID ELSE null END) AS jjzs");
		sql.append(",count(case when T.IF_TARGETDIS = 1 THEN t.PERSON_ID ELSE null END) AS zybs");
		sql.append(" from td_tj_bhk t");
		sql.append(" left join tb_tj_crpt t1 on t1.rid = t.crpt_id");
		sql.append(" left join ts_zone t2 on t2.rid = t1.zone_id");
		sql.append(" left join TD_TJ_MHKRST t3 on t.rid = t3.BHK_ID");
		sql.append(" left join TS_SIMPLE_CODE t4 on t4.rid = t3.BHKRST_ID");
		sql.append(" where t.bhk_type in (3,4) and t1.inter_prc_tag = 1 ");
		if (null != searchStartDate) {
			sql.append(" and t.bhk_date >=to_date('")
					.append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd"))
					.append("','yyyy-MM-dd')");
		}
		if (null != searchEndDate) {
			sql.append(" and t.bhk_date <=to_date('")
					.append(DateUtils.formatDate(searchEndDate, "yyyy-MM-dd"))
					.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
		}
		if (StringUtils.isNotBlank(searchIndusTypeIds)) {
			sql.append(" and t1.INDUS_TYPE_ID in (").append(searchIndusTypeIds)
					.append(")");
		}
		if (StringUtils.isNotBlank(searchEconomyIds)) {
			sql.append(" and t1.ECONOMY_ID in (").append(searchEconomyIds)
					.append(")");
		}
		if (StringUtils.isNotBlank(searchCitySizeIds)) {
			sql.append(" and t1.CRPT_SIZE_ID in (").append(searchCitySizeIds)
					.append(")");
		}
		if (StringUtils.isNotBlank(searchOnguardIds)) {
			sql.append(" and t.ONGUARD_STATEID in (").append(searchOnguardIds)
					.append(")");
		}
		if (StringUtils.isNotBlank(selectBigHarmIds)) {
			sql.append(
					" and exists (select 1 from TD_TJ_BADRSNS tt where tt.BHK_ID = t.rid and tt.BADRSN_ID in (")
					.append(selectBigHarmIds).append(")").append(")");
		}
		int zoneType = ZoneUtil.getZoneType(searchZoneCode);
		if (StringUtils.isNotBlank(searchZoneCode)) {
			sql.append(" and t2.zone_gb like '")
					.append(ZoneUtil.zoneSelect(searchZoneCode)).append("%'");
			sql.append(" and t2.zone_type" + (zoneType >= 4 ? ">=" : ">")).append(zoneType);
		}
		return this.findBySql(sql.toString());
	}
	//rcj


    /***************************************** aj *************************************/
    /**
     * @Description : 获取汇总总人数、异常人数
     * @MethodAuthor: anjing
     * @Date : 2019/12/24 13:43
     **/
    public List<Object[]> findHzZrsAndYcrs(String zoneCode, Date startDate, Date endDate, String indusTypeId,
                                           String economyId, String citySizeIds, String unitId, String harmIds,
                                           String onguardIds, String itemId) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT COUNT(DISTINCT T.PERSON_ID) AS ZRS");
        sb.append(", COUNT(DISTINCT CASE WHEN T3.RGLTAG = 0 THEN T.PERSON_ID ELSE NULL END) AS YCRS");
        sb.append(" FROM TD_TJ_BHK T ");
        sb.append(" LEFT JOIN TB_TJ_CRPT T1 ON T.CRPT_ID = T1.RID ");
        sb.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID ");
        sb.append(" LEFT JOIN TD_TJ_BHKSUB T3 ON T3.BHK_ID = T.RID ");
        sb.append(" LEFT JOIN TB_TJ_ITEMS T4 ON T3.ITEM_ID = T4.RID ");
        sb.append(" WHERE 1=1 ");
        sb.append(" AND T.BHK_TYPE IN (3, 4) ");
        sb.append(" AND T1.INTER_PRC_TAG = 1 ");
        // 地区
        if(StringUtils.isNotBlank(zoneCode)) {
            sb.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zoneCode)).append("%'");
        }
        // 检查周期
        if(null != startDate) {
            sb.append(" AND T.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(startDate, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
        }
        if(null != endDate) {
            sb.append(" AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(endDate, "yyyy-MM-dd")).append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
        }
        // 行业类别
        if(StringUtils.isNotBlank(indusTypeId)) {
            sb.append(" AND T1.INDUS_TYPE_ID IN (").append(indusTypeId).append(")");
        }
        // 经济类型
        if(StringUtils.isNotBlank(economyId)) {
            sb.append(" AND T1.ECONOMY_ID IN (").append(economyId).append(")");
        }
        // 企业规模
        if(StringUtils.isNotBlank(citySizeIds)) {
            sb.append(" AND T1.CRPT_SIZE_ID IN (").append(citySizeIds).append(")");
        }
        // 检查机构
        if(StringUtils.isNotBlank(unitId)) {
            sb.append(" AND T.BHKORG_ID IN (").append(unitId).append(")");
        }
        // 危害因素
        if(StringUtils.isNotBlank(harmIds)) {
            sb.append(" AND EXISTS (SELECT 1 FROM TD_TJ_BADRSNS TT WHERE TT.BHK_ID = T.RID AND TT.BADRSN_ID IN (")
                    .append(harmIds).append(")").append(")");
        }
        // 在岗状态
        if(StringUtils.isNotBlank(onguardIds)) {
            sb.append(" AND T.ONGUARD_STATEID IN (").append(onguardIds).append(")");
        }
        // 体检项目
        if(StringUtils.isNotBlank(itemId)) {
            sb.append(" AND T3.ITEM_ID IN (").append(itemId).append(")");
        }
        sb.append(" ORDER BY T2.ZONE_GB, T1.CRPT_NAME, T4.ITEM_NAME ");
        List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
        return list;
    }
    

    /***
	 *  <p>方法描述：检查机构上报情况</p>
     *
     * @MethodAuthor rcj,2019年12月24日,getTdOrgReport
	 * @param dimension
	 * @param zoneGb
	 * @param searchStartDate
	 * @param searchEndDate
	 * @return
	 */
	public List<Object[]> getTjOrgReport(Integer dimension,String zoneGb,Date searchStartDate,Date searchEndDate ){
		StringBuffer sql = new StringBuffer();
		int zoneType = ZoneUtil.getZoneType(zoneGb);
		String bhkBeginDate = PropertyUtils.getValueWithoutException("reportCard.bhkBeginDate");
		Date beginDate = DateUtils.parseDate(bhkBeginDate);
		
		if(dimension.intValue() == 1){
			sql.append("select aa.zone_name,zz.* from  (");
			sql.append("select ");
			if (2 == zoneType) {
				sql.append(" substr(z.zone_gb,0,4)||'000000' as zone_gb ");
			} else if (3 == zoneType) {
				sql.append(" substr(z.zone_gb,0,6)||'0000' as zone_gb ");
			} else {
				sql.append(" z.zone_gb ");
			}
			sql.append(" ,t.ORG_NAME,nvl(B.totalnum,0),nvl(t8.sbnum,0), case when B.totalnum >0 then  round(t8.sbnum * 100 / B.totalnum, 2) else 0 end as sbpercent");
			sql.append(" ,nvl(t8.sbwcs,0) , case when B.totalnum >0 then round(t8.sbwcs * 100 / B.totalnum, 2) else 0 end as wcspercent");
			sql.append("  ,nvl(t8.sbend,0) , case when t8.sbwcs >0 then round(t8.sbend * 100 / t8.sbnum, 2) else 0 end as endpercent ");
			sql.append(" ,nvl(t8.notintime,0) ,  case when t8.sbwcs >0 then round(t8.notintime * 100 / t8.sbnum, 2)  else 0 end as notintimepercent");
			sql.append(" , nvl(t8.backnum ,0), case when t8.sbwcs > 0 then  round(t8.backnum * 100 / t8.sbnum, 2)else 0 end  as backpercent ");
			sql.append(" from TD_ZW_TJORGINFO t");
			sql.append(" left join ts_unit u on t.ORG_ID = u.rid");
			sql.append(" left join ts_zone z on u.zone_id = z.rid ");
			
			sql.append(" left join  (select count(A.crptid) as totalnum, A.orgid");
			sql.append(" from (select  t4.reg_orgid as orgid,max(t1.RPT_PRINT_DATE),tc.rid as crptid ");
			sql.append(" from TD_TJ_BHK t1");
			sql.append(" left join TB_TJ_CRPT tc on tc.rid = t1.crpt_id");
			sql.append(" left join TB_TJ_SRVORG t4 on t4.rid = t1.BHKORG_ID");
			sql.append(" left join TS_UNIT t2 on t2.rid = t4.reg_orgid");
			sql.append(" left join ts_zone t3 on t3.rid = t2.zone_id");
			sql.append(" where 1 = 1 and T1.BHK_TYPE in (3, 4)");
			if(null != beginDate){
				sql.append(" and t1.RPT_PRINT_DATE >=to_date('")
				.append(DateUtils.formatDate(beginDate, "yyyy-MM-dd"))
				.append("','yyyy-MM-dd')");
			}
			
			if (null != searchStartDate) {
				sql.append(" AND T1.BHK_DATE >= TO_DATE('")
						.append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd"))
						.append("','yyyy-MM-dd')");
			}
			if (null != searchEndDate) {
				sql.append(" AND T1.BHK_DATE <= TO_DATE('")
						.append(DateUtils.formatDate(searchEndDate, "yyyy-MM-dd"))
						.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
			}
			sql.append(" and not exists (select 1 from TD_ZW_HETH_CHK_SMARY tt left join TD_ZW_TJORGINFO tt1 on tt1.rid = tt.fill_unit_id");
			sql.append(" where  tt.crpt_id = t1.crpt_id and nvl(tt.DEL_MARK, 0) = 0 and tt.bgn_bhk_date <= t1.bhk_date and tt.end_bhk_date >= t1.bhk_date)");
			
			sql.append(" group by  t4.reg_orgid,tc.rid union all select t4.ORG_ID as orgid,t6.ORG_RCV_DATE,tcc.rid as crptid   from TD_ZW_HETH_CHK_SMARY t1 ");
			sql.append(" left join TB_TJ_CRPT tcc on tcc.rid = t1.crpt_id");
			sql.append(" left join TD_ZW_TJORGINFO t4 on t4.rid = t1.fill_unit_id");
			sql.append(" left join TS_UNIT t2 on t2.rid = t4.ORG_ID");
			sql.append(" left join ts_zone t3 on t3.rid = t1.zone_id");
			sql.append(" left join TD_ZW_BGK_LAST_STA t6 on t6.BUS_ID = t1.rid");
			sql.append(" where 1 = 1 and t6.CART_TYPE = 1");
			
			if (null != searchStartDate) {
				sql.append(" AND t1.END_BHK_DATE >= TO_DATE('")
						.append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd"))
						.append("','yyyy-MM-dd')");
			}
			if (null != searchEndDate) {
				sql.append(" AND t1.BGN_BHK_DATE <= TO_DATE('")
						.append(DateUtils.formatDate(searchEndDate, "yyyy-MM-dd"))
						.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
			}
			sql.append("  and nvl(t1.DEL_MARK, 0) = 0)A group by A.orgid)B on B.orgid = t.ORG_ID");
			
			sql.append(" left join (");
			sql.append("   select  tt2.rid orgid,");
			sql.append("			sum(case when t4.bus_id is not null then 1 else 0 end) sbnum, ");	
			sql.append("			sum (case when t5.state >0 and ((s.IF_CITY_DIRECT=1 and t5.state != 4) or(s.IF_CITY_DIRECT is null and t5.state != 2) ) then 1 else 0 end) sbwcs, ");	
			sql.append("			sum (case when t5.state =7 then 1 else 0 end) sbend, ");	
			sql.append("			sum(case when t6.bus_id is not null then 1 else 0 end) notintime, ");	
			sql.append("			sum(case when t7.bus_id is not null then 1 else 0 end) backnum ");	
			sql.append("		from TD_ZW_HETH_CHK_SMARY tt ");	
			sql.append("		left join TD_ZW_TJORGINFO tt2  on tt2.rid = tt.FILL_UNIT_ID ");	
			sql.append("		left join ts_unit t2  on tt2.ORG_ID = t2.rid ");	
			sql.append("		left join ts_zone s on t2.zone_id = s.rid ");
			sql.append("		left join  (select a.CART_TYPE,a.bus_id from TD_ZW_BGK_FLOW a where  a.OPER_FLAG = 12 group by a.cart_type,a.bus_id)t4 on t4.CART_TYPE =   ");	
			sql.append(dimension);	
			sql.append("		and tt.rid = t4.BUS_ID  left join TD_ZW_BGK_LAST_STA t5 on t5.cart_type =  ");	
			sql.append(dimension);	
			sql.append("		and tt.rid = t5.bus_id and t5.state >0 left join  (select a.CART_TYPE,a.bus_id from TD_ZW_BGK_FLOW a where  a.OPER_FLAG in(11,12,13) and a.IF_IN_TIME= 0 group by a.cart_type,a.bus_id) t6 on t6.CART_TYPE =   ");
			sql.append(dimension);	
			sql.append("		and tt.rid = t6.BUS_ID left join  (select a.CART_TYPE,a.bus_id from TD_ZW_BGK_FLOW a where  a.OPER_FLAG in(11,13)  group by a.cart_type,a.bus_id) t7 on t7.CART_TYPE =   ");
			sql.append(dimension);	
			sql.append("	and tt.rid = t7.BUS_ID   where 1 =1 ");
			if (null != searchStartDate) {
				sql.append(" and ( (tt.BGN_BHK_DATE <=to_date('")
				.append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd"))
				.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
				sql.append(" and tt.END_BHK_DATE >=to_date('")
				.append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd"))
				.append("','yyyy-MM-dd') )");
				sql.append(" or tt.BGN_BHK_DATE >=to_date('")
						.append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd"))
						.append("','yyyy-MM-dd'))");
			}
			
			sql.append("		 group by tt2.rid ) t8 on t8.orgid = t.rid");	
			sql.append(" where t.state = 1 ) zz");
			sql.append(" left join  ts_zone aa  on aa.zone_gb = zz.zone_gb ");
			if (StringUtils.isNotBlank(zoneGb)) {
				sql.append(" where aa.zone_gb like '").append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
					sql.append(" and aa.zone_type" + (zoneType == 4 ? "=" : ">"))
							.append(zoneType).append(" and aa.zone_type<=")
							.append(zoneType + 1);
			}
			sql.append(" order by 2 ");
		}else{
			sql.append("select aa.zone_name,zz.* from  (");
			sql.append("select ");
			if (2 == zoneType) {
				sql.append(" substr(z.zone_gb,0,4)||'000000' as zone_gb ");
			} else if (3 == zoneType) {
				sql.append(" substr(z.zone_gb,0,6)||'0000' as zone_gb ");
			} else {
				sql.append(" z.zone_gb ");
			}
			sql.append(" ,t.ORG_NAME,nvl(t8.totalnum,0),nvl(t8.sbnum,0), case when t8.totalnum >0 then  round(t8.sbnum * 100 / t8.totalnum, 2) else 0 end as sbpercent");
			sql.append(" ,nvl(t8.sbwcs,0) , case when t8.totalnum >0 then round(t8.sbwcs * 100 / t8.totalnum, 2) else 0 end as wcspercent");
			sql.append("  ,nvl(t8.sbend,0) , case when t8.sbwcs >0 then round(t8.sbend * 100 / t8.sbnum, 2) else 0 end as endpercent ");
			sql.append(" ,nvl(t8.notintime,0) ,  case when t8.sbwcs >0 then round(t8.notintime * 100 / t8.sbnum, 2)  else 0 end as notintimepercent");
			sql.append(" , nvl(t8.backnum ,0), case when t8.sbwcs > 0 then  round(t8.backnum * 100 / t8.sbnum, 2)else 0 end  as backpercent ");
			sql.append(" from TD_ZW_TJORGINFO t");
			sql.append(" left join ts_unit u on t.ORG_ID = u.rid");
			sql.append(" left join ts_zone z on u.zone_id = z.rid ");
			sql.append(" left join (");
			sql.append("   select  tt2.rid orgid,count( tt1.rid) totalnum,");
			sql.append("			sum(case when t4.bus_id is not null then 1 else 0 end) sbnum, ");	
			sql.append("			sum (case when t5.state >0 and ((s.IF_CITY_DIRECT=1 and t5.state != 4) or(s.IF_CITY_DIRECT is null and t5.state != 2) ) then 1 else 0 end) sbwcs, ");	
			sql.append("			sum (case when t5.state =7 then 1 else 0 end) sbend, ");	
			sql.append("			sum(case when t6.bus_id is not null then 1 else 0 end) notintime, ");	
			sql.append("			sum(case when t7.bus_id is not null then 1 else 0 end) backnum ");	
			if( dimension ==5){
				sql.append("		from TD_TJ_BHK tt1 ");	
				sql.append("		left join TD_ZW_GBZ188_NOSTD tt on tt.main_id = tt1.rid");	
				sql.append("		left join TB_TJ_SRVORG t3 on tt1.BHKORG_ID = t3.rid ");	
				sql.append("		left join TD_ZW_TJORGINFO tt2 on tt2.ORG_ID = t3.REG_ORGID ");
				sql.append("		left join ts_unit t2 on tt2.ORG_ID = t2.rid   ");	
				sql.append("		left join ts_zone s on t2.zone_id = s.rid ");
			}else if( dimension ==6){
				sql.append("		from TD_TJ_BHK tt1 ");	
				sql.append("		left join TD_ZW_ZYBANDJJZ_NOTICE tt on tt.main_id = tt1.rid  ");	
				sql.append("		left join TB_TJ_SRVORG t3 on tt1.BHKORG_ID = t3.rid ");	
				sql.append("		left join TD_ZW_TJORGINFO tt2 on tt2.ORG_ID = t3.REG_ORGID ");
				sql.append("		left join ts_unit t2 on tt2.ORG_ID = t2.rid   ");	
				sql.append("		left join ts_zone s on t2.zone_id = s.rid ");
			}else if( dimension ==2){
				sql.append("		from TD_TJ_BHK tt1");	
				sql.append("		INNER JOIN TB_TJ_SRVORG T1  ON tt1.BHKORG_ID = T1.RID ");	
				sql.append("		INNER JOIN TS_UNIT T2 ON T1.REG_ORGID = T2.RID");	
				sql.append("		left join TD_ZW_TJORGINFO tt2 on tt2.org_id = T2.rid");	
				sql.append("		left join ts_zone s  on t2.zone_id = s.rid ");	
				sql.append("		left join TD_ZW_YSZYB_RPT tt  on tt.REL_BHK_ID = tt1.rid ");	
			}
			sql.append("		left join  (select a.CART_TYPE,a.bus_id from TD_ZW_BGK_FLOW a where  a.OPER_FLAG = 12 group by a.cart_type,a.bus_id)t4 on t4.CART_TYPE =   ");	
			sql.append(dimension);	
			sql.append("		and tt.rid = t4.BUS_ID  left join TD_ZW_BGK_LAST_STA t5 on t5.cart_type =  ");	
			sql.append(dimension);	
			sql.append("		and tt.rid = t5.bus_id and t5.state >0 left join  (select a.CART_TYPE,a.bus_id from TD_ZW_BGK_FLOW a where  a.OPER_FLAG in(11,12,13) and a.IF_IN_TIME= 0 group by a.cart_type,a.bus_id) t6 on t6.CART_TYPE =   ");
			sql.append(dimension);	
			sql.append("		and tt.rid = t6.BUS_ID left join  (select a.CART_TYPE,a.bus_id from TD_ZW_BGK_FLOW a where  a.OPER_FLAG in(11,13)  group by a.cart_type,a.bus_id) t7 on t7.CART_TYPE =   ");
			sql.append(dimension);	
			sql.append("	and tt.rid = t7.BUS_ID   where 1 =1  AND tt1.BHK_TYPE IN (3, 4)");
			if(dimension ==6){
				sql.append("	and (tt1.IF_TARGETDIS = 1 or tt1.IF_WRKTABU = 1)   ");
			}
			if (null != searchStartDate) {
				sql.append(" and tt1.RPT_PRINT_DATE >=to_date('")
						.append(DateUtils.formatDate(searchStartDate, "yyyy-MM-dd"))
						.append("','yyyy-MM-dd')");
			}
			if (null != searchEndDate) {
				sql.append(" and tt1.RPT_PRINT_DATE <=to_date('")
						.append(DateUtils.formatDate(searchEndDate, "yyyy-MM-dd"))
						.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
			}
			
			sql.append("		 group by tt2.rid ) t8 on t8.orgid = t.rid");	
			sql.append(" where t.state = 1 ) zz");
			sql.append(" left join  ts_zone aa  on aa.zone_gb = zz.zone_gb ");
			if (StringUtils.isNotBlank(zoneGb)) {
				sql.append(" where aa.zone_gb like '").append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
					sql.append(" and aa.zone_type" + (zoneType == 4 ? "=" : ">"))
							.append(zoneType).append(" and aa.zone_type<=")
							.append(zoneType + 1);
			}
			sql.append(" order by 2 ");
		}
		
		List<Object[]> result = em.createNativeQuery(sql.toString()).getResultList();
		return result;
	}
	@Transactional(readOnly = true)
	public List<TbTjSrvorg> findUnitByZoneId(Integer unitId, String zoneCode, String zoneLevel) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TbTjSrvorgAnaly t where t.stopTag=1 ");
		/**
		 * 选中的地区是当前地区要考虑是否是超管，超管能看到所有单位，普通用户只能看到自己
		 */
			sb.append(" and t.tsZone.zoneGb like'")
					.append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
			if (StringUtils.isNotBlank(zoneLevel)) {
				sb.append(" and (t.tsZone.zoneType = '").append(zoneLevel)
						.append("' or t.tsZone.zoneType = '");
				sb.append((Integer.parseInt(zoneLevel) + 1)).append("') ");
			}
		sb.append(" order by t.tsZone.zoneGb ");
		return em.createQuery(sb.toString()).getResultList();
	}
	public List<Object[]> findBhkExportList(String searchZoneCode, String searchPersonName, String searchIDC, String searchOrganization,
			String searchHisBadRsn, String searchUnitId, Date searchStartTime, Date searchEndTime, String filterItems,Integer startInd,Integer endInd,Integer flagUnitId,String bhk_type_sql, String searchBhkrstCode) {
		StringBuilder sb = new StringBuilder("");
		sb.append(" SELECT B.* FROM (");
		sb.append(" SELECT A.*, ROWNUM RN FROM ( SELECT DISTINCT * FROM (");
		sb.append(" SELECT ");
		sb.append(" TRANSLATE(T1.PERSON_NAME,CHR(9)||CHR(13)||CHR(10)||' ' ,',') AS PSNNAME,T1.BHK_CODE,T1.SEX,T1.AGE,")
				.append("DECODE (LENGTH (T1.IDC),15,SUBSTR (T1.IDC, 1, 6) || '******' || SUBSTR (T1.IDC, 13),18,SUBSTR (T1.IDC, 1, 6) || '********' || SUBSTR (T1.IDC, 15)) AS IDC,")
				.append("T3.UNIT_NAME,B.CRPT_NAME,D.CODE_NAME AS ZZGT,T1.DPT,T1.WORK_NAME,");
		sb.append(" DECODE ( T1.WRKLNT ,NULL,'',T1.WRKLNT ||'年')||DECODE ( T1.WRKLNTMONTH ,NULL,'',T1.WRKLNTMONTH ||'月') AS WORDAT ,");
		sb.append(" DECODE ( T1.TCHBADRSNTIM ,NULL,'',T1.TCHBADRSNTIM ||'年')||DECODE ( T1.TCHBADRSNMONTH ,NULL,'',T1.TCHBADRSNMONTH ||'月') AS BADDAT  ,T1.BADRSN,TO_CHAR(T1.BHK_DATE,'YYYY-MM-DD'),TO_CHAR(T1.MHKADV) ,T1.RID");
		sb.append(" FROM TD_TJ_BHK T1");
		sb.append(" LEFT JOIN TB_TJ_CRPT B ON B.RID=T1.CRPT_ID");
		sb.append(" LEFT JOIN TS_SIMPLE_CODE D ON T1.ONGUARD_STATEID=D.RID");
		sb.append(" INNER JOIN TD_TJ_PERSON T2 ON T2.RID = T1.PERSON_ID");
		sb.append(" INNER JOIN TB_TJ_SRVORG T3 ON T3.RID = T1.BHKORG_ID");
		sb.append(" INNER JOIN TS_ZONE T4 ON T4.RID = T3.ZONE_ID");
		sb.append(" LEFT JOIN TD_TJ_EMHISTORY A ON A.BHK_ID=T1.RID");

		sb.append(" WHERE ").append(bhk_type_sql).append(" T4.ZONE_GB LIKE '");
		sb.append(ZoneUtil.zoneSelect(searchZoneCode.trim())).append("%'");
		if (StringUtils.isNotBlank(searchPersonName)) {
			sb.append(" AND TRANSLATE(T2.PERSON_NAME,CHR(9)||CHR(13)||CHR(10)||' ' ,',')  = '");
			sb.append(searchPersonName.trim()).append("'");
		}
		if (StringUtils.isNotBlank(searchIDC)) {
			sb.append(" AND T2.IDC = '");
			sb.append(searchIDC.trim()).append("'");
		}
		if (StringUtils.isNotBlank(searchOrganization)) {
			sb.append(" AND T3.UNIT_NAME LIKE '%");
			sb.append(StringUtils.convertBFH(searchOrganization.trim())).append("%'");
		}
		
		//是否疾控单位
		if ( null != flagUnitId) {
			sb.append(" AND T3.REG_ORGID = ").append(flagUnitId);
		}
		
		if (StringUtils.isNotBlank(searchUnitId)) {
			sb.append(" AND T1.BHKORG_ID =").append(searchUnitId);
		}
		if (StringUtils.isNotBlank(searchHisBadRsn)) {
			sb.append(" AND A.PRFRAYSRT LIKE '%");
			sb.append(StringUtils.convertBFH(searchHisBadRsn.trim())).append("%'");
		}
		if (null != searchStartTime) {
			sb.append(" AND T1.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(searchStartTime, "yyyy-MM-dd")).append("','YYYY-MM-DD')");
		}
		if (null != searchEndTime) {
			sb.append(" AND T1.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(searchEndTime, "yyyy-MM-dd"))
			.append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
		}
		if (StringUtils.isNotBlank(filterItems)) {
			sb.append(" AND EXISTS ( SELECT 1 FROM TD_TJ_BHKSUB TSUB WHERE TSUB.BHK_ID=T1.RID AND ");
			sb.append(" TSUB.ITEM_ID IN (").append(filterItems).append(") )");
		}
        if (StringUtils.isNotBlank(searchBhkrstCode)) {
            sb.append(" AND EXISTS (SELECT 1 FROM TD_TJ_BHK TT " +
                    "LEFT JOIN TD_TJ_MHKRST TT1 ON TT1.BHK_ID = TT.RID " +
                    "LEFT JOIN TS_SIMPLE_CODE TT2 ON TT1.BHKRST_ID = TT2.RID " +
                    "WHERE TT2.RID IN ( ").append(searchBhkrstCode).append(") AND T1.RID = TT.RID)");
        }
		sb.append("ORDER BY T2.PERSON_NAME,T1.PERSON_ID,T1.BHK_DATE");
		sb.append(") )A ");
		sb.append(") B WHERE RN > ").append(startInd).append(" AND RN <= ").append(endInd).append("");
																																  
		List<Object[]> resultList = this.em.createNativeQuery(sb.toString()).getResultList();
		return resultList;
	}
	public List<Object[]> findBhkItemsRstList(List<String> bhkIdsList) {
		StringBuilder sb = new StringBuilder("");
		sb.append("SELECT T.BHK_ID,T.ITEM_ID,TO_CHAR(T.ITEM_RST),T.MSRUNT,  ");
		sb.append("(case when T.RGLTAG = 1 then '合格' else '不合格' end)as RGLTAG,");
		sb.append("DECODE(T.RST_FLAG,0,'未见异常',1,'尘肺样改变',2,'其他异常',3,'未检查','') as RST_FLAG,");
		sb.append("T2.ITEM_TAG");
		sb.append(" FROM TD_TJ_BHKSUB T  ");
		sb.append(" LEFT JOIN TB_TJ_ITEMS T2 on T.ITEM_ID = T2.RID");
		sb.append(" WHERE 1=1 ");
		if (!CollectionUtils.isEmpty(bhkIdsList)) {
			sb.append(" AND (");
			StringBuffer sql = new StringBuffer();
			for (String bhkIds : bhkIdsList) {
				sql.append(" OR T.BHK_ID IN ( ").append(bhkIds).append(" )");
			}
			sb.append(sql.substring(3));
			sb.append(" )");
		}
		List<Object[]> resultList = this.em.createNativeQuery(sb.toString()).getResultList();
		return resultList;
	}
	public List<Object[]> findBhkJbsList(String bhkIds,String jbs) {
		StringBuilder sb = new StringBuilder("");
		sb.append("SELECT H.STASTP_DATE,H.UNIT_NAME,H.DEPARTMENT,H.DEFEND_STEP,A.RID FROM TD_TJ_BHK A ");
		sb.append(" INNER JOIN TD_TJ_EMHISTORY H ON H.BHK_ID=A.RID WHERE H.BHK_ID IN ( ").append(bhkIds).append("  )");
		if(StringUtils.isNotBlank(jbs))	{
			sb.append("  AND H.PRFRAYSRT LIKE '%").append(jbs).append("%'");
		}
		
		sb.append(" ORDER BY H.CHKDAT");
		List<Object[]> resultList = this.em.createNativeQuery(sb.toString()).getResultList();
		return resultList;
	}
	public List<Object[]> findBhkJwbsList(String bhkIds) {
		StringBuilder sb = new StringBuilder("SELECT T.HSTNAM,T.HSTDAT,T.HSTUNT,T.HSTCRUPRC,T.HSTLPS,A.RID FROM TD_TJ_BHK A");
		sb.append(" INNER JOIN TD_TJ_ANAMNESIS T ON A.RID = T.BHK_ID WHERE T.BHK_ID IN ( ");
		sb.append(bhkIds).append("  ) ORDER BY T.CREATE_DATE");
		List<Object[]> resultList = this.em.createNativeQuery(sb.toString()).getResultList();
		return resultList;
	}
	public List<Object[]> findBhkQtList(List<String> bhkIdsList) {
		StringBuilder sb = new StringBuilder("");
		sb.append(" SELECT WM_CONCAT(DISTINCT B.JZS), ");
		sb.append(" WM_CONCAT(DISTINCT B.GRS),");
		sb.append(" WM_CONCAT(DISTINCT B.OTH),");
		sb.append(" WM_CONCAT(DISTINCT D.CODE_NAME),");
		sb.append(" WM_CONCAT(DISTINCT C.OTHSYM),A.RID");
		sb.append("  FROM TD_TJ_BHK A LEFT JOIN  TD_TJ_EXMSDATA  B ON A.RID = B.BHK_ID");
		sb.append(" LEFT JOIN TD_TJ_SYMPTOM C ON C.BHK_ID = A.RID ");
		sb.append(" LEFT JOIN TS_SIMPLE_CODE D ON C.SYM_ID = D.RID WHERE 1=1 ");
		if (!CollectionUtils.isEmpty(bhkIdsList)) {
			sb.append(" AND (");
			StringBuffer sql = new StringBuffer();
			for (String bhkIds : bhkIdsList) {
				sql.append(" OR  A.RID IN ( ").append(bhkIds).append(" )");
			}
			sb.append(sql.substring(3));
			sb.append(" )");
		}
		sb.append("  GROUP BY A.RID");
		List<Object[]> resultList = this.em.createNativeQuery(sb.toString()).getResultList();
		return resultList;
	}
	/**
	 * 查找体检主表集合
	 * 
	 * @param Rid
	 *            根据id查主表信息
	 * @param zoneCode
	 *            地区编码
	 * @param srvorgName
	 *            体检机构名称
	 * @return 主表集合
	 * <AUTHOR>
	 * @createDate 2014-9-14
	 */
	public List<TdTjBhk> findTdTjBhkListByPerson(Integer Rid, String zoneCode, String srvorgName,String searchHisBadRsn,Date searchStartTime,Date searchEndTime, String filterItems) {
		List<TdTjBhk> retList = null;
		if (null != Rid) {
			StringBuilder sb = new StringBuilder();
			sb.append("select t from TdTjBhkAnaly t ");
			
			if (StringUtils.isNotBlank(filterItems)) {
				sb.append(" inner join fetch t.tdTjBhksubs as c ");
			}
			sb.append(" where t.tdTjPerson.rid = ");
			sb.append(Rid).append(" ");
			
			if (StringUtils.isNotBlank(searchHisBadRsn)) {
				sb.append(" and t.rid in ( select s.tdTjBhk.rid from TdTjEmhistoryAnaly s where s.tdTjBhk.tdTjPerson.rid = ");
				sb.append(Rid).append(" and s.prfraysrt like '%").append(searchHisBadRsn.trim()).append("%' ) ");
			}
			
			if (StringUtils.isNotBlank(zoneCode)) {
				sb.append(" and t.tbTjSrvorg.tsZone.zoneGb like '");
				sb.append(ZoneUtil.zoneSelect(zoneCode.trim()));
				sb.append("%'");
			}
			if (StringUtils.isNotBlank(srvorgName)) {
				sb.append(" and t.tbTjSrvorg.unitName like '%");
				sb.append(srvorgName);
				sb.append("%'");
			}
			
			if (null != searchStartTime) {
				sb.append(" AND t.bhkDate >= TO_DATE('").append(DateUtils.formatDate(searchStartTime, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
			}
			if (null != searchEndTime) {
				sb.append(" AND t.bhkDate <= TO_DATE('").append(DateUtils.formatDate(searchEndTime, "yyyy-MM-dd"))
						.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
			}
			if (StringUtils.isNotBlank(filterItems)) {
				sb.append(" AND c.tbTjItems.rid in (").append(filterItems).append(") ");
			}
			sb.append(" order by t.bhkDate desc");
			List<TdTjBhk> list = em.createQuery(sb.toString()).getResultList();
			//处理查询出来的重复数据
			retList = new ArrayList<>();
			Set<Integer> bhkSets = new HashSet<Integer>();
			if (null != list && list.size() > 0) {
				for (TdTjBhk tdTjBhk : list) {
					if(!bhkSets.contains(tdTjBhk.getRid())){
						List<TdTjSupoccdiselist> tdTjSupoccdiselists = tdTjBhk.getTdTjSupoccdiselists();
						if (null != tdTjSupoccdiselists) {
							tdTjSupoccdiselists.size();
						}
						List<TdTjContraindlist> tdTjContraindlists = tdTjBhk.getTdTjContraindlists();
						if (null != tdTjContraindlists) {
							tdTjContraindlists.size();
						}
						retList.add(tdTjBhk);
						bhkSets.add(tdTjBhk.getRid());
					}
				}
			}
		}
		return retList;
	}
	/**
	 * 查历次职业病
	 * 
	 * @param personId
	 *            人员rid
	 * @return 职业病集合
	 * <AUTHOR>
	 * @createDate 2014-9-18
	 */

	public List<TdTjSupoccdiselist> findSupoccdiselist(Integer personId,String orgId) {
		if (null != personId) {
			StringBuilder sb = new StringBuilder();
			sb.append("from TdTjSupoccdiselist t where t.tdTjBhk.bhkType IN (3,4) AND t.tdTjBhk.tdTjPerson.rid =");
			sb.append(personId);
			sb.append(" and t.tdTjBhk.tbTjCrpt.interPrcTag =1");
			if (StringUtils.isNotBlank(orgId)) {
				sb.append(" AND T.tdTjBhk.tbTjSrvorg.rid =").append(orgId);
			}
			return em.createQuery(sb.toString()).getResultList();
		} else {
			return null;
		}
	}
	/**
	 * 查历次禁忌症
	 * 
	 * @param personId
	 *            人员rid
	 * @return 禁忌症集合
	 * <AUTHOR>
	 * @createDate 2014-9-18
	 */

	public List<TdTjContraindlist> findContraindlist(Integer personId,String orgId) {
		if (null != personId) {
			StringBuilder sb = new StringBuilder();
			sb.append("from TdTjContraindlist t where t.tdTjBhk.bhkType IN (3,4) AND t.tdTjBhk.tdTjPerson.rid =");
			sb.append(personId);
			sb.append(" and t.tdTjBhk.tbTjCrpt.interPrcTag =1");
			if (StringUtils.isNotBlank(orgId)) {
				sb.append(" AND T.tdTjBhk.tbTjSrvorg.rid =").append(orgId);
			}
			return em.createQuery(sb.toString()).getResultList();
		} else {
			return null;
		}
	}

	/**
	 * 查本次职业病
	 * 
	 * @param bhkId
	 *            体检主表rid
	 * @return 职业病集合
	 * <AUTHOR>
	 * @createDate 2014-9-18
	 */

	public List<TdTjSupoccdiselist> findBSupoccdiselist(Integer bhkId) {
		if (null != bhkId) {
			StringBuilder sb = new StringBuilder();
			sb.append("from TdTjSupoccdiselist t where t.tdTjBhk.rid =");
			sb.append(bhkId);
			return em.createQuery(sb.toString()).getResultList();
		} else {
			return null;
		}
	}
	/**
	 * 查本次禁忌症
	 * 
	 * @param bhkId
	 *            体检主表rid
	 * @return 禁忌症集合
	 * <AUTHOR>
	 * @createDate 2014-9-18
	 */

	public List<TdTjContraindlist> findBContraindlist(Integer bhkId) {
		if (null != bhkId) {
			StringBuilder sb = new StringBuilder();
			sb.append("from TdTjContraindlist t where t.tdTjBhk.rid =");
			sb.append(bhkId);
			return em.createQuery(sb.toString()).getResultList();
		} else {
			return null;
		}
	}
	/**
	 * 根据rid查找主表信息
	 * 
	 * @param rid
	 *            主表rid
	 * @return 返回主表信息
	 * <AUTHOR>
	 * @createDate 2014-9-14
	 */

	public TdTjBhk findTdTjBhkById(Integer rid) {
		TdTjBhk tdTjBhk = null;
		if (null != rid) {
			tdTjBhk = this.find(TdTjBhk.class, rid);
			tdTjBhk.getTdTjEmhistories().size();
			tdTjBhk.getTdTjAnamnesises().size();
			tdTjBhk.getTdTjExmsdatas().size();
			tdTjBhk.getTdTjSymptoms().size();
			tdTjBhk.getTdTjMhkrsts().size();
			tdTjBhk.getTdTjBadrsnses().size();
			tdTjBhk.getTdTjContraindlists().size();
			tdTjBhk.getTdTjSupoccdiselists().size();
			tdTjBhk.getTdTjTchBadrsns().size();
		}
		return tdTjBhk;
	}


	public List<TdTjBhk> findTdTjBhkListByIds(String ids) {
		List<TdTjBhk> retList = null;
		if(StringUtils.isNotBlank(ids)){
			String[] idsArr = ids.split(",");
			int length = idsArr.length;
			StringBuilder sb = new StringBuilder();
			sb.append("select t from TdTjBhk t  WHERE t.rid in( ");
			if(length<=1000){
				sb.append(ids).append(")");
			}else{
				List<String> list = Arrays.asList(idsArr);
				List<String> firstList = list.subList(0,1000);
				sb.append(StringUtils.list2string(firstList,",")).append(")");
				int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
				for(int i=1;i<allDataCount;i++){
					int endIndex = Math.min((i+1)*1000,length);
					List<String> subList = list.subList(i*1000,endIndex);
					sb.append(" or t.rid in( ").append(StringUtils.list2string(subList,",")).append(")");
				}
			}
			retList = em.createQuery(sb.toString()).getResultList();
		}
		return retList;
	}
	public void updateTdTjBhkListByIds(List<Integer> ids,Map<String,Object> valuesMap){
		if(!CollectionUtils.isEmpty(ids) && MapUtils.isNotEmpty(valuesMap)){
			StringBuilder sb = new StringBuilder("UPDATE TD_TJ_BHK T SET ");
			StringBuilder valStr = new StringBuilder();
			for(String field:valuesMap.keySet()){
				Object value = valuesMap.get(field);
				if(value != null){
					valStr.append("," + field + " = ");
					if(value instanceof Integer){
						valStr.append(value);
					}else if(value instanceof String){
						valStr.append("'" + value + "'");
					}else if(value instanceof Date){
						valStr.append(" TO_DATE('").append(DateUtils.formatDate((Date)value));
						valStr.append(" 23:59:59', 'YYYY-MM-DD HH24:MI:SS') ");
					}
				}else{
					valStr.append("," + field + " = null ");
				}
			}
			sb.append(valStr.substring(1));
			int length = ids.size();
			sb.append(" WHERE T.RID IN (");
			if(length<=1000){
				sb.append(StringUtils.list2string(ids,",")).append(")");
			}else{
				List<Integer> firstList = ids.subList(0,1000);
				sb.append(StringUtils.list2string(firstList,",")).append(")");
				int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
				for(int i=1;i<allDataCount;i++){
					int endIndex = Math.min((i+1)*1000,length);
					List<Integer> subList = ids.subList(i*1000,endIndex);
					sb.append(" OR T.RID IN( ").append(StringUtils.list2string(subList,",")).append(")");
				}
			}
		}
	}




	/**
	 * 根据主表id查找子表集合
	 * 
	 * @param rid
	 *            主表rid
	 * @return 数据模型集合
	 * <AUTHOR>
	 * @createDate 2014-9-18
	 * 
	 *             修改人：wlj 修改时间：2014-9-28
	 *             修改内容：不建议在for循环中使用SQL语句，现改为一次性从数据库查询所有数据，再进行数据处理
     * 此方法已废弃，以后请调用findProjectInfoNew(Integer rid, boolean ifRmkSubCover)
	 */

	@Deprecated
	public Map<TsSimpleCode, List<Object[]>> findProjectInfo(Integer rid) {
        TdTjBhk tdTjBhk = this.find(TdTjBhk.class, rid);
	    Map<TsSimpleCode, List<Object[]>> dataMap = new LinkedHashMap<TsSimpleCode, List<Object[]>>();
		List<TsSimpleCode> list = commService.findNumSimpleCodesByTypeId("5008");
		StringBuilder sb = null;
		if (null != list && list.size() > 0) {
			sb = new StringBuilder();
			List<TsSimpleCode> firstList = new ArrayList<TsSimpleCode>();
			Map<String, List<TsSimpleCode>> map = new HashMap<String, List<TsSimpleCode>>();
			// 获取二级分类id集合
			for (TsSimpleCode t : list) {
				if (null != t.getCodeLevelNo()) {
					if (t.getCodeLevelNo().split("\\.").length == 1) {
						firstList.add(t);
					} else if (t.getCodeLevelNo().split("\\.").length == 2) {
						String parentCode = t.getCodeLevelNo().split("\\.")[0];
						if (map.containsKey(parentCode)) {
							map.get(parentCode).add(t);
						} else {
							List<TsSimpleCode> ls = new ArrayList<TsSimpleCode>();
							ls.add(t);
							map.put(parentCode, ls);
						}
					}
				}
			}
			List<TsSimpleCode> newList = new ArrayList<TsSimpleCode>();
			for (TsSimpleCode t : firstList) {
				List<TsSimpleCode> listMap = map.get(t.getCodeNo());
				newList.add(t);
				if (null != listMap) {
					for (TsSimpleCode sortCode : listMap) {
						sb.append(",");
						sb.append(sortCode.getRid());
					}
                    newList.addAll(listMap);
				}
			}
			if (sb.toString().length() > 0) {
				String ids = sb.toString().substring(1);
				// 根据主表id查询体检子表中所有体检记录
				sb = new StringBuilder();
				sb.append("SELECT T2.ITEM_NAME,TO_CHAR(T1.ITEM_RST),T1.RST_DESC,T1.ITEM_STDVALUE,T1.MSRUNT,T2.ITEM_SORTID,T1.ITEM_ID");
				sb.append(" FROM TD_TJ_BHKSUB T1");
				sb.append(" INNER JOIN TB_TJ_ITEMS T2 ON T2.RID = T1.ITEM_ID");
//				sb.append(" INNER JOIN TS_SIMPLE_CODE T3 ON T3.RID =T2.ITEM_SORTID");
				sb.append(" WHERE T1.BHK_ID = ");
				sb.append(rid);
				sb.append(" AND T2.ITEM_SORTID IN (");
				sb.append(ids).append(") ORDER BY T2.NUM,T2.ITEM_CODE");
				List<Object[]> list2 = em.createNativeQuery(sb.toString()).getResultList();

				// 数据处理，将查询到的数据进行分类，建立数据模型
				for (TsSimpleCode t : newList) {
					if (null != t.getCodeLevelNo()) {
						if (t.getCodeLevelNo().split("\\.").length == 1) {
							dataMap.put(t, null);
						} else if (t.getCodeLevelNo().split("\\.").length == 2) {
							dataMap.put(t, getReqSortList(t, list2));
						}
					}
				}
			}
		}
		return dataMap;
	}
	/**
	 * 获取该分类的记录集合
	 * 
	 * @param t
	 *            码表分类
	 * @param list
	 *            所有的数据集合
	 * @return 该分类的数据集合
	 * 
	 * <AUTHOR>
	 * @createDate 2014-9-28
	 */
	private List<Object[]> getReqSortList(TsSimpleCode t, List<Object[]> list) {
		List<Object[]> relist = new LinkedList<Object[]>();
		for (Object[] obj : list) {
			if (Integer.valueOf(obj[5].toString()).equals(t.getRid())) {
				relist.add(obj);
			}
		}
		return relist;
	}
	public Integer findBhkExportCount(String searchZoneCode, String searchPersonName, String searchIDC, String searchOrganization,
			String searchHisBadRsn, String searchUnitId, Date searchStartTime, Date searchEndTime, String filterItems,Integer flagUnitId, String searchBhkrstCode) {
		StringBuilder sb = new StringBuilder("");
		sb.append(" FROM TD_TJ_PERSON T");
		sb.append(" INNER JOIN (");
		sb.append(" SELECT T1.PERSON_ID,COUNT(DISTINCT T1.RID) IDCS FROM TD_TJ_BHK T1");
		sb.append(" INNER JOIN TD_TJ_PERSON T2 ON T2.RID = T1.PERSON_ID");
		sb.append(" INNER JOIN TB_TJ_SRVORG T3 ON T3.RID = T1.BHKORG_ID");
		sb.append(" INNER JOIN TS_ZONE T4 ON T4.RID = T3.ZONE_ID");
		sb.append(" LEFT JOIN TD_TJ_EMHISTORY A ON A.BHK_ID=T1.RID");
		sb.append(" WHERE T1.BHK_TYPE IN (2, 3,4) AND T4.ZONE_GB LIKE '");
		sb.append(ZoneUtil.zoneSelect(searchZoneCode.trim())).append("%'");
		if (StringUtils.isNotBlank(searchPersonName)) {
			sb.append(" AND TRANSLATE(T2.PERSON_NAME,CHR(9)||CHR(13)||CHR(10)||' ' ,',')  = '");
			sb.append(searchPersonName.trim()).append("'");
		}
		if (StringUtils.isNotBlank(searchIDC)) {
			sb.append(" AND T2.IDC = '");
			sb.append(searchIDC.trim()).append("'");
		}
		if (StringUtils.isNotBlank(searchOrganization)) {
			sb.append(" AND T3.UNIT_NAME LIKE '%");
			sb.append(StringUtils.convertBFH(searchOrganization.trim())).append("%'");
		}
		if (StringUtils.isNotBlank(searchUnitId)) {
			sb.append(" AND T1.BHKORG_ID =").append(searchUnitId);
		}
		if ( null != flagUnitId) {
			sb.append(" AND T3.REG_ORGID =").append(flagUnitId);
		}
		if (StringUtils.isNotBlank(searchHisBadRsn)) {
			sb.append(" AND A.PRFRAYSRT LIKE '%");
			sb.append(StringUtils.convertBFH(searchHisBadRsn.trim())).append("%'");
		}
		if (null != searchStartTime) {
			sb.append(" AND T1.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(searchStartTime, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
		}
		if (null != searchEndTime) {
			sb.append(" AND T1.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(searchEndTime, "yyyy-MM-dd"))
					.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
		}
		if (StringUtils.isNotBlank(filterItems)) {
			sb.append(" AND EXISTS ( SELECT 1 from TD_TJ_BHKSUB TSUB where TSUB.BHK_ID=T1.RID AND ");
			sb.append(" TSUB.ITEM_ID IN (").append(filterItems).append(") )");
		}
        if (StringUtils.isNotBlank(searchBhkrstCode)) {
            sb.append(" AND EXISTS (SELECT 1 FROM TD_TJ_BHK TT " +
                    "LEFT JOIN TD_TJ_MHKRST TT1 ON TT1.BHK_ID = TT.RID " +
                    "LEFT JOIN TS_SIMPLE_CODE TT2 ON TT1.BHKRST_ID = TT2.RID " +
                    "WHERE TT2.RID IN ( ").append(searchBhkrstCode).append(") AND T1.RID = TT.RID)");
        }
		sb.append(" GROUP BY T1.PERSON_ID");
		sb.append(") T5 ON T5.PERSON_ID = T.RID");
		StringBuilder h2 = new StringBuilder();
		// StringBuilder h1 = new StringBuilder();
		h2.append("SELECT COUNT(T.RID)");
		h2.append(sb.toString());
		List<Object> resultList = this.em.createNativeQuery(h2.toString()).getResultList();
		if (null != resultList && resultList.size() > 0) {
			Integer count = resultList.get(0) != null ? Integer.valueOf(resultList.get(0).toString()) : 0;
			return count;
		}
		return 0;
	}
	@Transactional(readOnly = true)
	public List<TbTjItems> findTjItemList(String searchItemCode, String searchItemName, Short searchState,
			Short searchPanDuanState, Integer ywId) {
		StringBuilder sb = new StringBuilder("from TbTjItems t  where 1=1 ");
		// 查询项目编码字段
		if (StringUtils.isNotBlank(searchItemCode)) {
			sb.append(" and t.itemCode like :itemCode ");
		}
		// 查询项目名称或者拼音码
		if (StringUtils.isNotBlank(searchItemName)) {
			sb.append(" and (t.itemName like :itemName or UPPER(t.pyxnam) like :itemPyName  )");
		}
		// 查询状态 1为启用 0为停用 STOP_TAG
		if (null != searchState) {
			sb.append(" and t.stopTag =").append(searchState);
		}
		// 判断模式 1为定性 0为定量
		if (null != searchPanDuanState) {
			sb.append(" and t.jdgptn =").append(searchPanDuanState);
		}
		// 业务分类 关联码表查询
		if (null != ywId) {
			sb.append(" and t.tsSimpleCode.rid = ").append(ywId);
		}
		// 按业务分类 和项目编码升序排列 codeName
		sb.append(" order by t.tsSimpleCode.num,t.tsSimpleCode.codeLevelNo,t.num, t.itemCode ");
		Query query = this.em.createQuery(sb.toString());
		// 查询项目编码字段
		if (StringUtils.isNotBlank(searchItemCode)) {
			query.setParameter("itemCode", "%" + StringUtils.convertBFH(searchItemCode) + "%");
		}
		// 查询项目名称或者拼音码
		if (StringUtils.isNotBlank(searchItemName)) {
			query.setParameter("itemName", "%" + StringUtils.convertBFH(searchItemName) + "%");
			query.setParameter("itemPyName", "%" + StringUtils.convertBFH(searchItemName.toUpperCase()) + "%");
		}
		List<TbTjItems> resultList = query.getResultList();
		return resultList;
	}
	/**
	 * <p>方法描述：内部导出无需隐藏身份证</p>
 	 * 
 	 * @MethodAuthor rcj,2019年4月11日,findBhkExportList
	 * @param searchZoneCode
	 * @param searchPersonName
	 * @param searchIDC
	 * @param searchOrganization
	 * @param searchHisBadRsn
	 * @param searchUnitId
	 * @param searchStartTime
	 * @param searchEndTime
	 * @param filterItems
	 * @param startInd
	 * @param endInd
	 * @param flagUnitId
	 * @param bhk_type_sql
	 * @return
	 */
	public List<Object[]> findBhkExportNbList(String searchZoneCode, String searchPersonName, String searchIDC, String searchOrganization,
			String searchHisBadRsn, String searchUnitId, Date searchStartTime, Date searchEndTime, String filterItems,Integer startInd,Integer endInd,Integer flagUnitId,String bhk_type_sql, String searchBhkrstCode) {
		StringBuilder sb = new StringBuilder("");
		sb.append(" SELECT B.* FROM (");
		sb.append(" SELECT A.*, ROWNUM RN FROM ( SELECT DISTINCT * FROM (");
		sb.append(" SELECT ");
		sb.append(" TRANSLATE(T1.PERSON_NAME,CHR(9)||CHR(13)||CHR(10)||' ' ,',') AS PSNNAME,T1.BHK_CODE,T1.SEX,T1.AGE,")
		.append(" T1.IDC AS IDC,")
		.append("T3.UNIT_NAME,B.CRPT_NAME,D.CODE_NAME AS ZZGT,T1.DPT,T1.WORK_NAME,");
		sb.append(" DECODE ( T1.WRKLNT ,NULL,'',T1.WRKLNT ||'年')||DECODE ( T1.WRKLNTMONTH ,NULL,'',T1.WRKLNTMONTH ||'月') AS WORDAT ,");
		sb.append(" DECODE ( T1.TCHBADRSNTIM ,NULL,'',T1.TCHBADRSNTIM ||'年')||DECODE ( T1.TCHBADRSNMONTH ,NULL,'',T1.TCHBADRSNMONTH ||'月') AS BADDAT  ,T1.BADRSN,TO_CHAR(T1.BHK_DATE,'YYYY-MM-DD'),TO_CHAR(T1.MHKADV) ,T1.RID");
		sb.append(" FROM TD_TJ_BHK T1");
		sb.append(" LEFT JOIN TB_TJ_CRPT B ON B.RID=T1.CRPT_ID");
		sb.append(" LEFT JOIN TS_SIMPLE_CODE D ON T1.ONGUARD_STATEID=D.RID");
		sb.append(" INNER JOIN TD_TJ_PERSON T2 ON T2.RID = T1.PERSON_ID");
		sb.append(" INNER JOIN TB_TJ_SRVORG T3 ON T3.RID = T1.BHKORG_ID");
		sb.append(" INNER JOIN TS_ZONE T4 ON T4.RID = T3.ZONE_ID");
		sb.append(" LEFT JOIN TD_TJ_EMHISTORY A ON A.BHK_ID=T1.RID");
		
		sb.append(" WHERE ").append(bhk_type_sql).append(" T4.ZONE_GB LIKE '");
		sb.append(ZoneUtil.zoneSelect(searchZoneCode.trim())).append("%'");
		if (StringUtils.isNotBlank(searchPersonName)) {
			sb.append(" AND TRANSLATE(T2.PERSON_NAME,CHR(9)||CHR(13)||CHR(10)||' ' ,',')  = '");
			sb.append(searchPersonName.trim()).append("'");
		}
		if (StringUtils.isNotBlank(searchIDC)) {
			sb.append(" AND T2.IDC = '");
			sb.append(searchIDC.trim()).append("'");
		}
		if (StringUtils.isNotBlank(searchOrganization)) {
			sb.append(" AND T3.UNIT_NAME LIKE '%");
			sb.append(StringUtils.convertBFH(searchOrganization.trim())).append("%'");
		}
		
		//是否疾控单位
		if ( null != flagUnitId) {
			sb.append(" AND T3.REG_ORGID = ").append(flagUnitId);
		}
		
		if (StringUtils.isNotBlank(searchUnitId)) {
			sb.append(" AND T1.BHKORG_ID =").append(searchUnitId);
		}
		if (StringUtils.isNotBlank(searchHisBadRsn)) {
			sb.append(" AND A.PRFRAYSRT LIKE '%");
			sb.append(StringUtils.convertBFH(searchHisBadRsn.trim())).append("%'");
		}
		if (null != searchStartTime) {
			sb.append(" AND T1.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(searchStartTime, "yyyy-MM-dd")).append("','YYYY-MM-DD')");
		}
		if (null != searchEndTime) {
			sb.append(" AND T1.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(searchEndTime, "yyyy-MM-dd"))
			.append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
		}
		if (StringUtils.isNotBlank(filterItems)) {
			sb.append(" AND EXISTS ( SELECT 1 FROM TD_TJ_BHKSUB TSUB WHERE TSUB.BHK_ID=T1.RID AND ");
			sb.append(" TSUB.ITEM_ID IN (").append(filterItems).append(") )");
		}
        if (StringUtils.isNotBlank(searchBhkrstCode)) {
            sb.append(" AND EXISTS (SELECT 1 FROM TD_TJ_BHK TT " +
                    "LEFT JOIN TD_TJ_MHKRST TT1 ON TT1.BHK_ID = TT.RID " +
                    "LEFT JOIN TS_SIMPLE_CODE TT2 ON TT1.BHKRST_ID = TT2.RID " +
                    "WHERE TT2.RID IN ( ").append(searchBhkrstCode).append(") AND T1.RID = TT.RID)");
        }
		sb.append("ORDER BY T2.PERSON_NAME,T1.PERSON_ID,T1.BHK_DATE");
		sb.append(") )A ");
		sb.append(") B WHERE RN > ").append(startInd).append(" AND RN <= ").append(endInd).append("");
		
		List<Object[]> resultList = this.em.createNativeQuery(sb.toString()).getResultList();
		return resultList;
	}
	 /**
	 * 获取职业病大类信息
	 *
	 * @return findBigHarmInfo
	 * <AUTHOR>
	 * @history 2019年11月23日
	 */
	public List<Object[]> findFirstLevelSimpleCode(String codeTypeNo,String extends1) {
		if (StringUtils.isBlank(codeTypeNo)) {
			return new ArrayList<>();
		}
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT T.RID,T.CODE_NAME FROM TS_SIMPLE_CODE T  ");
		sql.append(" INNER JOIN TS_CODE_TYPE T1 ON T.CODE_TYPE_ID = T1.RID ");
		sql.append(" WHERE T1.CODE_TYPE_NAME = ").append(codeTypeNo);
		if (StringUtils.isNotBlank(extends1)) {
			sql.append(" AND T.EXTENDS1 = ").append(extends1);
		}
		sql.append(" AND T.IF_REVEAL = '1' AND INSTR(T.CODE_LEVEL_NO,'.','1','1') = 0 ");
		sql.append(" ORDER BY T.NUM,T.CODE_LEVEL_NO,T.CODE_NO ");
		return em.createNativeQuery(sql.toString()).getResultList();
	}
	/**
 	 * <p>方法描述：查询体检机构资质注册</p>
 	 * @MethodAuthor qrr,2020年4月22日,findTjSvorgByRegUnitId
	 * */
	public List<Object[]> findTjSvorgByRegUnitId(Integer regUnitId) {
		if (null==regUnitId) {
			return null;
		}
		StringBuffer sql = new StringBuffer();
		sql.append("select rid,UNIT_NAME from TB_TJ_SRVORG where REG_ORGID =").append(regUnitId);
		return em.createNativeQuery(sql.toString()).getResultList();
	}
	/**
	 * 查找体检主表集合
	 * 
	 * @param Rid
	 *            根据id查主表信息
	 * @param orgId
	 *            体检机构名称
	 * @return 主表集合
	 * <AUTHOR>
	 * @createDate 2014-9-14
	 */
	public List<TdTjBhk> findTdTjBhkAllListByPerson(Integer Rid,String orgId) {
		List<TdTjBhk> retList = null;
		if (null != Rid) {
			StringBuilder sb = new StringBuilder();
			sb.append("select t from TdTjBhk t ");
			sb.append(" where t.bhkType IN (3,4) AND t.tdTjPerson.rid = ").append(Rid);
			sb.append(" and t.tbTjCrpt.interPrcTag =1 ");
			if (StringUtils.isNotBlank(orgId)) {
				sb.append(" and t.tbTjSrvorg.rid =").append(orgId);
			}
			sb.append(" order by t.bhkDate desc");
			List<TdTjBhk> list = em.createQuery(sb.toString()).getResultList();
			//处理查询出来的重复数据
			retList = new ArrayList<>();
			Set<Integer> bhkSets = new HashSet<Integer>();
			if (null != list && list.size() > 0) {
				for (TdTjBhk tdTjBhk : list) {
					//体检危害因素
					getBhkBadrsnName(tdTjBhk);
					if(!bhkSets.contains(tdTjBhk.getRid())){
						List<TdTjSupoccdiselist> tdTjSupoccdiselists = tdTjBhk.getTdTjSupoccdiselists();
						if (null != tdTjSupoccdiselists) {
							tdTjSupoccdiselists.size();
						}
						List<TdTjContraindlist> tdTjContraindlists = tdTjBhk.getTdTjContraindlists();
						if (null != tdTjContraindlists) {
							tdTjContraindlists.size();
						}
						List<TdTjMhkrst> mhkrstList = tdTjBhk.getTdTjMhkrsts();
						if (null != mhkrstList) {
							mhkrstList.size();
						}
						retList.add(tdTjBhk);
						bhkSets.add(tdTjBhk.getRid());
					}
				}
			}
		}
		return retList;
	}
	/**
	 *  <p>方法描述：体检危害因数的处理</p>
	 * @MethodAuthor hsj 2024-05-28 15:57
	 */
	private void getBhkBadrsnName(TdTjBhk tdTjBhk ) {
		List<TdTjBadrsns> badrsnsList = tdTjBhk.getTdTjBadrsnses();
		if(CollectionUtils.isEmpty(badrsnsList)) {
			return;
		}
		List<String> names = new ArrayList<>();
		SortUtil.sortCodeByField(badrsnsList,TdTjBadrsns.class,"getTsSimpleCode");
		for(TdTjBadrsns bads:badrsnsList){
			names.add(bads.getTsSimpleCode().getCodeName());
		}
		tdTjBhk.setBhkBadrsn(StringUtils.list2string(names,"，"));
	}
	/**
 	 * <p>方法描述：初始化个案导出查询条件</p>
 	 * @MethodAuthor qrr,2020年5月21日,initSearchConditionSql
	 * */
	private String initExportSearchConditionSql(
			TjPersonSearchConditionPO conditionPO, boolean ifAdmin,
			Map<String, Object> paramMap) {
		StringBuffer sb = new StringBuffer();
		sb.append(" FROM TD_TJ_BHK T1 ");
		sb.append(" INNER JOIN TD_TJ_PERSON T2 ON T2.RID = T1.PERSON_ID");
		sb.append(" INNER JOIN TB_TJ_CRPT crpt ON crpt.RID = T1.CRPT_ID");
		sb.append(" INNER JOIN TS_ZONE T6 ON T6.RID = crpt.ZONE_ID");
		sb.append(" WHERE 1=1 AND crpt.INTER_PRC_TAG = 1");
		sb.append(" AND T1.BHK_TYPE IN (3,4)");
		String searchPersonName = conditionPO.getSearchPersonName();
		if (StringUtils.isNotBlank(searchPersonName)) {
			sb.append(" AND T2.PERSON_NAME LIKE :searchPersonName escape '\\\'");
			paramMap.put("searchPersonName", "%"+StringUtils.convertBFH(searchPersonName.trim())+"%");
		}
		String searchPsnType = conditionPO.getSearchPsnType();
		if (StringUtils.isNotBlank(searchPsnType)) {
			sb.append(" AND T2.CARD_TYPE_ID =").append(searchPsnType);
		}
		String searchIDC = conditionPO.getSearchIDC();
		if (StringUtils.isNotBlank(searchIDC)) {
			if (!"3".equals(searchPsnType)) {
				sb.append(" AND T2.IDC = :searchIDC");
				paramMap.put("searchIDC", StringUtils.convertBFH(searchIDC.trim()));
			}
		}
		String searchUnitId = conditionPO.getSearchUnitId();
		//是否管理员
		if (!ifAdmin) {
			sb.append(" AND T1.BHKORG_ID = ").append(searchUnitId);//本机构
		}else {
			//体检机构
			if (StringUtils.isNotBlank(searchUnitId) ) {
				sb.append(" AND T1.BHKORG_ID IN (").append(searchUnitId).append(")");
			}
		}
		//年龄
		List<TdZdzybAnalyDetailComm> selectAgeAnalyDetails = conditionPO.getSelectAgeAnalyDetails();
		if (null!=selectAgeAnalyDetails && selectAgeAnalyDetails.size()>0) {
			StringBuffer detail = new StringBuffer();
			for (TdZdzybAnalyDetailComm detailAnaly : selectAgeAnalyDetails) {
				//0-10 20-30
				detail.append(" or (");
				Integer geNum = detailAnaly.getGeNum();
				if (null!=geNum) {
					detail.append("T1.AGE >=").append(geNum);
				}
				Integer gtNum = detailAnaly.getGtNum();
				if (null!=gtNum) {
					if (null!=geNum) {
						detail.append(" AND ");
					}
					detail.append(" T1.AGE >").append(gtNum);
				}
				Integer leNum = detailAnaly.getLeNum();
				if (null!=leNum) {
					if (null!=geNum||null!=gtNum) {
						detail.append(" AND ");
					}
					detail.append(" T1.AGE <=").append(leNum);
				}
				Integer ltNum = detailAnaly.getLtNum();
				if (null!=ltNum) {
					if (null!=geNum||null!=gtNum||null!=leNum) {
						detail.append(" AND ");
					}
					detail.append(" T1.AGE <").append(ltNum);
				}
				detail.append(")");
			}
			sb.append(" AND (").append(detail.substring(3)).append(")");
		}
		//工龄
		List<TdZdzybAnalyDetailComm> selectWorkAnalyDetails = conditionPO.getSelectWorkAnalyDetails();
		if (null!=selectWorkAnalyDetails && selectWorkAnalyDetails.size()>0) {
			StringBuffer detail = new StringBuffer();
			for (TdZdzybAnalyDetailComm detailAnaly : selectWorkAnalyDetails) {
				detail.append(" or (");
				Integer geNum = detailAnaly.getGeNum();
				if (null!=geNum) {
					detail.append(" T1.TCHBADRSNTIM >=").append(geNum);
				}
				Integer gtNum = detailAnaly.getGtNum();
				if (null!=gtNum) {
					if (null!=geNum) {
						detail.append(" AND ");
					}
					detail.append(" T1.TCHBADRSNTIM >").append(gtNum);
				}
				Integer leNum = detailAnaly.getLeNum();
				if (null!=leNum) {
					if (null!=geNum||null!=gtNum) {
						detail.append(" AND ");
					}
					detail.append(" T1.TCHBADRSNTIM <=").append(leNum);
				}
				Integer ltNum = detailAnaly.getLtNum();
				if (null!=ltNum) {
					if (null!=geNum||null!=gtNum||null!=leNum) {
						detail.append(" AND ");
					}
					detail.append(" T1.TCHBADRSNTIM <").append(ltNum);
				}
				detail.append(")");
			}
			sb.append(" AND (").append(detail.substring(3)).append(")");
		}
		//用人单位地区
		String searchZoneCode = conditionPO.getSearchZoneCode();
		if (StringUtils.isNotBlank(searchZoneCode)) {
			sb.append(" AND T6.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(searchZoneCode)).append("%'");
		}
		//用人单位名称
		String searchCrptName = conditionPO.getSearchCrptName();
		if (StringUtils.isNotBlank(searchCrptName)) {
			sb.append(" AND T1.CRPT_NAME LIKE :searchCrptName escape '\\\'");
			paramMap.put("searchCrptName", "%"+StringUtils.convertBFH(searchCrptName.trim())+"%");
		}
		//体检类型
		String[] searchBhkType = conditionPO.getSearchBhkType();
		if (null!=searchBhkType && searchBhkType.length>0) {
			StringBuffer bhkType = new StringBuffer();
			for (String s : searchBhkType) {
				bhkType.append(",").append(s);
			}
			sb.append(" AND T1.BHK_TYPE IN (").append(bhkType.substring(1)).append(")");
		}
		//体检日期
		Date searchStartTime = conditionPO.getSearchStartTime();
		if ( null != searchStartTime) {
			sb.append(" AND T1.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(searchStartTime, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
		}
		Date searchEndTime = conditionPO.getSearchEndTime();
		if ( null != searchEndTime) {
			sb.append(" AND T1.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(searchEndTime, "yyyy-MM-dd")).append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
		}
		//在岗状态
		String selectOnGuardIds = conditionPO.getSelectOnGuardIds();
		if (StringUtils.isNotBlank(selectOnGuardIds)) {
			sb.append(" AND T1.ONGUARD_STATEID IN (").append(selectOnGuardIds).append(")");
		}
		//接触危害因素
		String selectBadRsnIds = conditionPO.getSelectBadRsnIds();
		if (StringUtils.isNotBlank(selectBadRsnIds)) {
			sb.append(" AND EXISTS ( SELECT 1 from TD_TJ_BADRSNS badRsn where badRsn.BHK_ID=T1.RID AND ");
			sb.append(" badRsn.BADRSN_ID IN (").append(selectBadRsnIds).append(") )");
		}
		//体检结论
		String searchSelBhkrstIds = conditionPO.getSearchSelBhkrstIds();
		if (StringUtils.isNotBlank(searchSelBhkrstIds)) {
			sb.append(" AND EXISTS ( SELECT 1 from TD_TJ_MHKRST rst where rst.BHK_ID=T1.RID AND ");
			sb.append(" rst.BHKRST_ID IN (").append(searchSelBhkrstIds).append(") )");
		}
		String searchItemIds = conditionPO.getSearchItemIds();
		if(StringUtils.isNotBlank(searchItemIds))	{
			sb.append(" AND EXISTS ( SELECT 1 from TD_TJ_BHKSUB TSUB where TSUB.BHK_ID=T1.RID ");
			String[] split = searchItemIds.split(",");
			sb.append(" AND (");
			StringBuffer buffer = new StringBuffer();
			for (String s : split) {
				String[] split2 = s.split("@@");
				buffer.append(" OR (TSUB.ITEM_ID =").append(split2[0]);
				if (split2.length>1) {//不合格
					buffer.append(" AND TSUB.RGLTAG = ").append(split2[1]);
				}
				buffer.append(")");
			}
			sb.append(buffer.substring(3)).append(")").append(")");
		}
		//监测类型
		String[] searchJcType = conditionPO.getSearchJcType();
		if (null!=searchJcType && searchJcType.length>0) {
			StringBuffer type = new StringBuffer();
			for (String s : searchJcType) {
				type.append(",").append(s);
			}
			sb.append(" AND T1.JC_TYPE IN (").append(type.substring(1)).append(")");
		}
		return sb.toString();
	}
	/**
 	 * <p>方法描述：非质控员：分页查询体检人员ridSql</p>
 	 * @MethodAuthor qrr,2020年5月21日,initSelectTjPersonIdSql
	 * */
	private String initSelectTjPersonIdSql(String conditionSql,
			String searchBhkNum, boolean ifAdmin, String searchUnitId,
			Integer firstNum, Integer endNum) {
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT T.PERSON_ID");
		sql.append(" FROM (");
		sql.append("SELECT T1.PERSON_ID,T2.PERSON_NAME");
		sql.append(conditionSql);
		sql.append(" GROUP BY T1.PERSON_ID,T2.PERSON_NAME");
		sql.append(")T");
		
		if (StringUtils.isNotBlank(searchBhkNum)) {
			// 非质控员关联体检记录，查询档案数
			sql.append(" INNER JOIN(");
			sql.append("SELECT /*+index(T1 INDEX_CRPTID)*/T1.PERSON_ID,COUNT(T1.RID) IDCS");
			sql.append(",MIN(T1.BHK_DATE) AS CREATE_DATE,MAX(T1.BHK_DATE) AS UPDATE_DATE");
			sql.append(",SUM(T1.IF_TARGETDIS) AS ZYB");
			sql.append(",SUM(T1.IF_WRKTABU) AS JJZ");
			sql.append(" FROM TD_TJ_BHK T1 ");
			sql.append(" INNER JOIN TB_TJ_CRPT crpt ON crpt.RID = T1.CRPT_ID");
			sql.append(" WHERE  T1.BHK_TYPE IN (3,4)  AND crpt.INTER_PRC_TAG = 1");
			sql.append(" AND T1.BHKORG_ID = ").append(searchUnitId);//本机构
			sql.append(" GROUP BY T1.PERSON_ID");
			sql.append(")A ON A.PERSON_ID =T.PERSON_ID");
			sql.append(" WHERE 1=1 ");
			sql.append(" AND A.IDCS >=").append(searchBhkNum);
		}
		sql.append(" ORDER BY T.PERSON_NAME,T.PERSON_ID");
		StringBuffer sb = new StringBuffer();
		sb.append(" SELECT * FROM (");
		sb.append(" SELECT KKK0.*, ROWNUM AS RN FROM (").append(sql);
		sb.append(" ) KKK0 ) WHERE RN > '").append(firstNum)
				.append("' AND RN <= '").append(endNum)
				.append("' ");
		return sb.toString();
	}

	public List<Object[]> findBhkExportNewList(
			TjPersonSearchConditionPO conditionPO, boolean ifAdmin,
			Integer exportFlag, Integer firstNum, Integer endNum) {
		String searchUnitId = conditionPO.getSearchUnitId();
		String searchBhkNum = conditionPO.getSearchBhkNum();
		Map<String, Object> paramMap = new HashMap<String, Object>();
		String conditionSql = initExportSearchConditionSql(conditionPO,
				ifAdmin, paramMap);
		
		StringBuffer sb = new StringBuffer();
		sb.append("SELECT T.RID,");
		sb.append("       T.PERSON_NAME,");
		sb.append("       T.BHK_CODE,");
		sb.append("       T.SEX,");
		sb.append("       T.AGE,");
		sb.append("       T11.CODE_NAME AS idcType,");
		if(exportFlag == 2){
			sb.append("    T.IDC,");
		}else{
			sb.append(" DECODE (LENGTH(T.IDC),15,SUBSTR(T.IDC,1,6) || '******' || SUBSTR (T.IDC,13),18,SUBSTR(T.IDC, 1,6) || '********' || SUBSTR(T.IDC,15),CASE WHEN T.IDC IS NOT NULL THEN SUBSTR(T.IDC, 1,LENGTH(T.IDC)-4) || '****' END) AS IDC,");
		}
		if(exportFlag == 2){
			sb.append("    T.LNKTEL,");
		}else{
			sb.append("       (CASE WHEN T.LNKTEL IS NOT NULL THEN SUBSTR ('************', 0, LENGTH(T .LNKTEL)-4) || SUBSTR (T.LNKTEL, LENGTH(T .LNKTEL)-3, LENGTH(T .LNKTEL))    END) as LNKTEL,");
		}
		sb.append("       T.CRPT_NAME,");
		sb.append("       T.LINKMAN2,");
		sb.append("       T.LINKPHONE2,");
		sb.append("       T.FULL_NAME,");
		sb.append("       T.ADDRESS,");
		sb.append("       T.INSTITUTION_CODE,");
		sb.append("       T3.CODE_NAME AS DWGM,");
		sb.append("       T4.CODE_NAME AS JJLX,");
		sb.append("       T5.CODE_NAME AS HYLB,");
		sb.append("       T6.CODE_NAME AS ZGZT,");
		sb.append("       T.DPT,");
		sb.append("       T.WORK_NAME,");
		sb.append("       DECODE(T.WRKLNT, NULL, '', T.WRKLNT || '年') ||");
		sb.append("       DECODE(T.WRKLNTMONTH, NULL, '', T.WRKLNTMONTH || '月') AS WORDAT,");
		sb.append("       DECODE(T.TCHBADRSNTIM, NULL, '', T.TCHBADRSNTIM || '年') ||");
		sb.append("       DECODE(T.TCHBADRSNMONTH, NULL, '', T.TCHBADRSNMONTH || '月') AS BADDAT,");
		sb.append("       A.BADNAME,");
		sb.append("       T10.CODE_NAME,");
		sb.append("       T8.UNIT_NAME,");
		sb.append("       TO_CHAR(T.BHK_DATE,'yyyy-MM-dd'),");
		sb.append("       TO_CHAR(T.RPT_PRINT_DATE,'yyyy-MM-dd')");
		sb.append(" FROM (");
		sb.append("SELECT T1.RID,T2.PERSON_NAME,T1.BHK_CODE,T1.SEX,T1.AGE,T1.CRPT_NAME,T1.DPT,T1.WORK_NAME,T1.WRKLNT,T1.WRKLNTMONTH,T1.TCHBADRSNTIM,T1.TCHBADRSNMONTH,T1.BHK_DATE,T1.BHK_TYPE ");
		sb.append(",crpt.CRPT_SIZE_ID,crpt.ECONOMY_ID,crpt.INDUS_TYPE_ID,T1.ONGUARD_STATEID,T1.BHKORG_ID,crpt.LINKPHONE2");
		sb.append(",crpt.ADDRESS,crpt.INSTITUTION_CODE,T6.FULL_NAME,T2.IDC,T1.PERSON_ID,T2.ARCH_NUM,T2.CARD_TYPE_ID,T1.LNKTEL,crpt.LINKMAN2,T1.RPT_PRINT_DATE");
		sb.append(conditionSql);
		sb.append(")T");
		sb.append(" LEFT JOIN TS_SIMPLE_CODE T3");
		sb.append("    ON T3.RID = T.CRPT_SIZE_ID");
		sb.append(" LEFT JOIN TS_SIMPLE_CODE T4");
		sb.append("    ON T4.RID = T.ECONOMY_ID");
		sb.append(" LEFT JOIN TS_SIMPLE_CODE T5");
		sb.append("    ON T5.RID = T.INDUS_TYPE_ID");
		sb.append(" LEFT JOIN TS_SIMPLE_CODE T6");
		sb.append("    ON T6.RID = T.ONGUARD_STATEID");
		sb.append("    LEFT JOIN TS_SIMPLE_CODE T11 ON T11.RID = T .CARD_TYPE_ID");
		sb.append("  LEFT JOIN TB_TJ_SRVORG T8");
		sb.append("    ON T8.RID = T.BHKORG_ID");
		sb.append("  LEFT JOIN TD_TJ_MHKRST T9");
		sb.append("    ON T9.BHK_ID = T.RID");
		sb.append("  LEFT JOIN TS_SIMPLE_CODE T10");
		sb.append("    ON T9.BHKRST_ID = T10.RID");
		sb.append(" LEFT JOIN (");
		sb.append(" SELECT wm_concat(T2.code_name) BADNAME, T.RID FROM TD_TJ_BHK T");
		sb.append(" LEFT JOIN TD_TJ_BADRSNS T1 ON T.RID = T1.BHK_ID");
		sb.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T2.RID = T1.BADRSN_ID");
		sb.append(" GROUP BY T.RID");
		sb.append(" )A ON A.RID =T.RID WHERE 1=1 ");
		
		if (!ifAdmin && StringUtils.isNotBlank(searchBhkNum)) {// 非质控员且查询条件档案数不为空，分页查询满足总档案数的体检人员，获取所有体检人员的体检记录
			String personIdSql = initSelectTjPersonIdSql(conditionSql,
					searchBhkNum, ifAdmin, searchUnitId, firstNum, endNum);
			sb.append(" AND EXISTS (SELECT 1 FROM (").append(personIdSql).append(")A WHERE A.PERSON_ID = T.PERSON_ID").append(")");
			sb.append(" ORDER BY T.PERSON_NAME,T.PERSON_ID,T.BHK_DATE");
			List<Object[]> result = this.findDataBySqlNoPage(sb.toString(),paramMap);
			return result;
		}else {// 1、质控员2、非质控员且非质控员查询条件档案数为空，则分页查询体检记录
			if (StringUtils.isNotBlank(searchBhkNum)) {
				sb.append(" AND T.ARCH_NUM >=").append(searchBhkNum);
			}
			sb.append(" ORDER BY T.PERSON_NAME,T.PERSON_ID,T.BHK_DATE");
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT * FROM (");
			sql.append(" SELECT KKK0.*, ROWNUM AS RN FROM (").append(sb);
			sql.append(" ) KKK0 ) WHERE RN > '").append(firstNum)
					.append("' AND RN <= '").append(endNum)
					.append("' ");
			List<Object[]> result = this.findDataBySqlNoPage(sql.toString(),paramMap);
			return result;
		}
	}
	
	

	public Integer findBhkNewExportCount(TjPersonSearchConditionPO conditionPO,
			boolean ifAdmin) {
		String searchUnitId = conditionPO.getSearchUnitId();
		String searchBhkNum = conditionPO.getSearchBhkNum();
		Map<String, Object> paramMap = new HashMap<String, Object>();
		String conditionSql = initExportSearchConditionSql(conditionPO,
				ifAdmin, paramMap);
		StringBuffer sql = new StringBuffer();
		if (!ifAdmin && StringUtils.isNotBlank(searchBhkNum)) {// 非质控员：满足查询条件总档案数的体检人员
			sql.append(" SELECT COUNT(1) FROM (");
			sql.append(" SELECT T1.PERSON_ID");// /*+index(T1 INDEX_CRPTID)*/
			sql.append(conditionSql);
			sql.append(" GROUP BY T1.PERSON_ID");
			sql.append(")T ");
			sql.append(" INNER JOIN(");
			sql.append("SELECT /*+index(T1 INDEX_CRPTID)*/T1.PERSON_ID,COUNT(T1.RID) IDCS");
			sql.append(" FROM TD_TJ_BHK T1 ");
			sql.append(" INNER JOIN TB_TJ_CRPT crpt ON crpt.RID = T1.CRPT_ID");
			sql.append(" WHERE  T1.BHK_TYPE IN (3,4)  AND crpt.INTER_PRC_TAG = 1");
			sql.append(" AND T1.BHKORG_ID = ").append(searchUnitId);//本机构
			sql.append(" GROUP BY T1.PERSON_ID");
			sql.append(")A ON A.PERSON_ID =T.PERSON_ID");
			sql.append(" where 1=1");
			sql.append(" AND A.IDCS >=").append(searchBhkNum);
		}else {
			sql.append(" SELECT COUNT(1) ");
			sql.append(conditionSql);
			if (StringUtils.isNotBlank(searchBhkNum)) {
				sql.append(" AND T2.ARCH_NUM >=").append(searchBhkNum);
			}
		}
		List<Object> resultList = findDataBySqlNoPage(sql.toString(), paramMap);
		if (null != resultList && resultList.size() > 0) {
			Integer count = resultList.get(0) != null ? Integer.valueOf(resultList.get(0).toString()) : 0;
			return count;
		}
		return 0;
	}
	
	/**
	 * <p>方法描述：查询人员职业史,检查日期正序排序</p>
 	 * 
 	 * @MethodAuthor rcj,2020年4月24日,findBhkJbsNewList
	 * @return
	 */
	public List<Object[]> findBhkJbsNewList(List<String> bhkIdsList) {
		StringBuilder sb = new StringBuilder("");
		sb.append("SELECT H.STASTP_DATE,H.UNIT_NAME,H.DEPARTMENT,H.DEFEND_STEP,A.RID FROM TD_TJ_BHK A ");
		sb.append(" INNER JOIN TD_TJ_EMHISTORY H ON H.BHK_ID=A.RID WHERE H.HIS_TYPE = 2 ");
		if (!CollectionUtils.isEmpty(bhkIdsList)) {
			sb.append(" AND (");
			StringBuffer sql = new StringBuffer();
			for (String bhkIds : bhkIdsList) {
				sql.append(" OR  H.BHK_ID IN ( ").append(bhkIds).append(" )");
			}
			sb.append(sql.substring(3));
			sb.append(" )");
		}
		sb.append(" ORDER BY H.CHKDAT,H.RID");
		List<Object[]> resultList = this.em.createNativeQuery(sb.toString()).getResultList();
		return resultList;
	}
	/**
	 * <p>方法描述：查询人员既往病史</p>
 	 * 
 	 * @MethodAuthor rcj,2020年4月24日,findBhkJbsNewList
	 * @return
	 */
	public List<Object[]> findBhkJwbsNewList(List<String> bhkIdsList) {
		StringBuilder sb = new StringBuilder("SELECT T.HSTNAM,T.HSTDAT,T.HSTUNT,T.HSTCRUPRC,T.HSTLPS,A.RID FROM TD_TJ_BHK A");
		sb.append(" INNER JOIN TD_TJ_ANAMNESIS T ON A.RID = T.BHK_ID WHERE 1=1 ");
		if (!CollectionUtils.isEmpty(bhkIdsList)) {
			sb.append(" AND (");
			StringBuffer sql = new StringBuffer();
			for (String bhkIds : bhkIdsList) {
				sql.append(" OR  T.BHK_ID IN ( ").append(bhkIds).append(" )");
			}
			sb.append(sql.substring(3));
			sb.append(" )");
		}
		sb.append(" ORDER BY T.HSTDAT,T.RID");
		List<Object[]> resultList = this.em.createNativeQuery(sb.toString()).getResultList();
		return resultList;
	}
	
	
	/**
	 * <p>方法描述：查询放射史</p>
 	 * 
 	 * @MethodAuthor rcj,2020年4月24日,findBhkFssNewList
	 * @return
	 */
	public List<Object[]> findBhkFssNewList(List<String> bhkIdsList) {
		StringBuilder sb = new StringBuilder("");
		sb.append("SELECT H.STASTP_DATE,H.UNIT_NAME,H.PRFWRKLOD,H.PRFSHNVLU,H.PRFEXCSHN,H.PRFRAYSRT2,H.FSSZL,A.RID FROM TD_TJ_BHK A ");
		sb.append(" INNER JOIN TD_TJ_EMHISTORY H ON H.BHK_ID=A.RID WHERE H.HIS_TYPE = 1 ");
		if (!CollectionUtils.isEmpty(bhkIdsList)) {
			sb.append(" AND (");
			StringBuffer sql = new StringBuffer();
			for (String bhkIds : bhkIdsList) {
				sql.append(" OR  H.BHK_ID IN ( ").append(bhkIds).append(" )");
			}
			sb.append(sql.substring(3));
			sb.append(" )");
		}
		sb.append(" ORDER BY H.CHKDAT,H.RID ");
		List<Object[]> resultList = this.em.createNativeQuery(sb.toString()).getResultList();
		return resultList;
	}

	/**
	* @Description : 根据主表id查找子表集合
	* @MethodAuthor: anjing
	* @Date : 2020/7/21 15:57
	**/
    public Map<TsSimpleCode, List<Object[]>> findProjectInfoNew(Integer rid, boolean ifRmkSubCover) {
        TdTjBhk tdTjBhk = this.find(TdTjBhk.class, rid);
        Map<TsSimpleCode, List<Object[]>> dataMap = new LinkedHashMap<TsSimpleCode, List<Object[]>>();
        List<TsSimpleCode> list = commService.findNumSimpleCodesByTypeId("5008");
        StringBuilder sb = null;
        if (null != list && list.size() > 0) {
            sb = new StringBuilder();
            List<TsSimpleCode> firstList = new ArrayList<TsSimpleCode>();
            Map<String, List<TsSimpleCode>> map = new HashMap<String, List<TsSimpleCode>>();
            // 获取二级分类id集合
            for (TsSimpleCode t : list) {
                if (null != t.getCodeLevelNo()) {
                    if (t.getCodeLevelNo().split("\\.").length == 1) {
                        firstList.add(t);
                    } else if (t.getCodeLevelNo().split("\\.").length == 2) {
                        String parentCode = t.getCodeLevelNo().split("\\.")[0];
                        if (map.containsKey(parentCode)) {
                            map.get(parentCode).add(t);
                        } else {
                            List<TsSimpleCode> ls = new ArrayList<TsSimpleCode>();
                            ls.add(t);
                            map.put(parentCode, ls);
                        }
                    }
                }
            }
            List<TsSimpleCode> newList = new ArrayList<TsSimpleCode>();
            for (TsSimpleCode t : firstList) {
                List<TsSimpleCode> listMap = map.get(t.getCodeNo());
                newList.add(t);
                if (null != listMap) {
                    for (TsSimpleCode sortCode : listMap) {
                        sb.append(",");
                        sb.append(sortCode.getRid());
                    }
                    newList.addAll(listMap);
                }
            }
            if (sb.toString().length() > 0) {
                String ids = sb.toString().substring(1);
                // 根据主表id查询体检子表中所有体检记录
                sb = new StringBuilder();
                sb.append("SELECT T2.ITEM_NAME,TO_CHAR(T1.ITEM_RST),T1.RST_DESC,T1.ITEM_STDVALUE,T1.MSRUNT,T2.ITEM_SORTID,T1.ITEM_ID,T1.RGLTAG,T1.RST_FLAG,T1.IF_LACK,T2.ITEM_TAG");
                sb.append(" FROM TD_TJ_BHKSUB T1");
                sb.append(" INNER JOIN TB_TJ_ITEMS T2 ON T2.RID = T1.ITEM_ID");
//				sb.append(" INNER JOIN TS_SIMPLE_CODE T3 ON T3.RID =T2.ITEM_SORTID");
                sb.append(" WHERE T1.BHK_ID = ");
                sb.append(rid);
                sb.append(" AND T2.ITEM_SORTID IN (");
                sb.append(ids).append(") ORDER BY T2.NUM,T2.ITEM_CODE");
                List<Object[]> list2 = em.createNativeQuery(sb.toString()).getResultList();

                List<Object[]> newSubList = new ArrayList<>();
                if(ifRmkSubCover) {
                    // 覆盖复检记录的体检子表
                    StringBuilder sb3 = new StringBuilder();
                    sb3.append("SELECT T2.ITEM_NAME,TO_CHAR(T1.ITEM_RST),T1.RST_DESC,T1.ITEM_STDVALUE,T1.MSRUNT,T2.ITEM_SORTID,T1.ITEM_ID,T1.RGLTAG,T1.RST_FLAG,T1.IF_LACK,T2.ITEM_TAG");
                    sb3.append(" FROM TD_TJ_BHKSUB T1");
                    sb3.append(" INNER JOIN TB_TJ_ITEMS T2 ON T2.RID = T1.ITEM_ID");
                    sb3.append(" INNER JOIN TD_TJ_BHK T3 ON T1.BHK_ID = T3.RID ");
                    sb3.append(" WHERE T3.BHKORG_ID = ").append(tdTjBhk.getTbTjSrvorg().getRid());
                    sb3.append(" AND T3.LAST_FST_BHK_CODE = '").append(tdTjBhk.getBhkCode()).append("' ");
                    sb3.append(" AND T1.IF_LACK = 0 ");
                    sb3.append(" AND T2.ITEM_SORTID IN (");
                    sb3.append(ids).append(") ORDER BY T3.BHK_DATE,T2.NUM,T2.ITEM_CODE");
                    List<Object[]> list3 = em.createNativeQuery(sb3.toString()).getResultList();

                    List<Object[]> subList = new ArrayList<>();
                    subList.addAll(list2);
                    subList.addAll(list3);
                    Map<String, Object[]> subMap = new LinkedHashMap<>();
                    for (Object[] obj : subList) {
                        subMap.put(obj[6].toString(), obj);
                    }


                    for (String subName : subMap.keySet()) {
                        newSubList.add(subMap.get(subName));
                    }
                } else {
                    if(!CollectionUtils.isEmpty(list2)) {
                        newSubList.addAll(list2);
                    }
                }

                // 数据处理，将查询到的数据进行分类，建立数据模型
                for (TsSimpleCode t : newList) {
                    if (null != t.getCodeLevelNo()) {
                        if (t.getCodeLevelNo().split("\\.").length == 1) {
                            dataMap.put(t, null);
                        } else if (t.getCodeLevelNo().split("\\.").length == 2) {
                            dataMap.put(t, getReqSortList(t, newSubList));
                        }
                    }
                }
            }
        }
        return dataMap;
    }






	/**
	 * @Description : 根据主表id查找子表集合
	 * @MethodAuthor: anjing
	 * @Date : 2020/7/21 15:57
	 **/
	public Map<TsSimpleCode, List<Object[]>> findProjectInfoNewBHK(Integer rid) {
		TdTjBhk tdTjBhk = this.find(TdTjBhk.class, rid);
		Map<TsSimpleCode, List<Object[]>> dataMap = new LinkedHashMap<TsSimpleCode, List<Object[]>>();
		List<TsSimpleCode> list = commService.findNumSimpleCodesByTypeId("5008");
		StringBuilder sb = null;
		if (null != list && list.size() > 0) {
			sb = new StringBuilder();
			List<TsSimpleCode> firstList = new ArrayList<TsSimpleCode>();
			Map<String, List<TsSimpleCode>> map = new HashMap<String, List<TsSimpleCode>>();
			// 获取二级分类id集合
			for (TsSimpleCode t : list) {
				if (null != t.getCodeLevelNo()) {
					if (t.getCodeLevelNo().split("\\.").length == 1) {
						firstList.add(t);
					} else if (t.getCodeLevelNo().split("\\.").length == 2) {
						String parentCode = t.getCodeLevelNo().split("\\.")[0];
						if (map.containsKey(parentCode)) {
							map.get(parentCode).add(t);
						} else {
							List<TsSimpleCode> ls = new ArrayList<TsSimpleCode>();
							ls.add(t);
							map.put(parentCode, ls);
						}
					}
				}
			}
			List<TsSimpleCode> newList = new ArrayList<TsSimpleCode>();
			for (TsSimpleCode t : firstList) {
				List<TsSimpleCode> listMap = map.get(t.getCodeNo());
				newList.add(t);
				if (null != listMap) {
					for (TsSimpleCode sortCode : listMap) {
						sb.append(",");
						sb.append(sortCode.getRid());
					}
					newList.addAll(listMap);
				}
			}
			if (sb.toString().length() > 0) {
				String ids = sb.toString().substring(1);
				// 根据主表id查询体检子表中所有体检记录
				sb = new StringBuilder();
				sb.append("SELECT T2.ITEM_NAME,TO_CHAR(T1.ITEM_RST),T1.RST_DESC,T1.ITEM_STDVALUE,T1.MSRUNT,T2.ITEM_SORTID,T1.ITEM_ID,T1.RGLTAG,T1.RST_FLAG,T1.IF_LACK,T2.ITEM_TAG");
				sb.append(" FROM TD_TJ_BHKSUB T1");
				sb.append(" INNER JOIN TB_TJ_ITEMS T2 ON T2.RID = T1.ITEM_ID");
//				sb.append(" INNER JOIN TS_SIMPLE_CODE T3 ON T3.RID =T2.ITEM_SORTID");
				sb.append(" WHERE T1.BHK_ID = ");
				sb.append(rid);
				sb.append(" AND T2.ITEM_SORTID IN (");
				sb.append(ids).append(") ORDER BY T2.NUM,T2.ITEM_CODE");
				List<Object[]> list2 = em.createNativeQuery(sb.toString()).getResultList();

				List<Object[]> newSubList = new ArrayList<>();

				if(!CollectionUtils.isEmpty(list2)) {
					newSubList.addAll(list2);
				}

				// 数据处理，将查询到的数据进行分类，建立数据模型
				for (TsSimpleCode t : newList) {
					if (null != t.getCodeLevelNo()) {
						if (t.getCodeLevelNo().split("\\.").length == 1) {
							dataMap.put(t, null);
						} else if (t.getCodeLevelNo().split("\\.").length == 2) {
							dataMap.put(t, getReqSortList(t, newSubList));
						}
					}
				}
			}
		}
		return dataMap;
	}


	/**
	 * <p>方法描述： 根据id更新体检主表状态   </p>
	 * @MethodAuthor  yzz，2021-05-14，
	 **/
	@Transactional(readOnly = false)
	public void updateTdTjBhkStateByRid(Integer state,Integer id,Integer operFlag,Integer lastOperFlag){
		//更新体检记录
		StringBuffer sb=new StringBuffer();
		sb.append("update TD_TJ_BHK set state=");
		sb.append(state);
		sb.append(" where rid=");
		sb.append(id);
		this.em.createNativeQuery(sb.toString()).executeUpdate();

		String selectDelSql="select * from TD_ZW_BGK_FLOW where CART_TYPE=9 and BUS_ID="+id+" and OPER_FLAG="+lastOperFlag+" order by CREATE_DATE desc";
		List<Object[]> selectRes= this.em.createNativeQuery(selectDelSql).getResultList();
		if(!CollectionUtils.isEmpty(selectRes)){
			if(lastOperFlag!=null&&(21==lastOperFlag||33==lastOperFlag||44==lastOperFlag)){
				//删除初审时新增的记录21,33,44
				String lastDelSql=" delete from TD_ZW_BGK_FLOW where rid="+Integer.valueOf(selectRes.get(0)[0].toString());
				this.em.createNativeQuery(lastDelSql).executeUpdate();
			}else{
				//把提交的修改的字段更新成null
				String lastUpdateSql=" update TD_ZW_BGK_FLOW set OPER_DATE=null,OPER_PSN_ID=null,IF_IN_TIME=null where rid="+Integer.valueOf( selectRes.get(0)[0].toString());;
				this.em.createNativeQuery(lastUpdateSql).executeUpdate();
			}
		}

		//删除报告卡审批流程
		StringBuffer sql=new StringBuffer();
		sql.append(" select T.* from TD_ZW_BGK_FLOW T ");
		sql.append(" where T.CART_TYPE=9 and T.BUS_ID=").append(id);
		sql.append(" and T.OPER_FLAG=").append(operFlag);
		sql.append(" order by  T.CREATE_DATE desc ");
		List<Object[]> list= this.em.createNativeQuery(sql.toString()).getResultList();
		if(!CollectionUtils.isEmpty(list)){
			StringBuffer delSql=new StringBuffer();
			delSql.append(" delete from  TD_ZW_BGK_FLOW where RID=").append(Integer.valueOf(list.get(0)[0].toString()));
			this.em.createNativeQuery(delSql.toString()).executeUpdate();
		}
	}

/**
 * <p>
 *     方法描述：获取体检体检机构联系人和联系电话
 * </p>
 *
 * @MethodAuthor yph,2021年05月19日
 */
	@Transactional(readOnly = true)
	public TbTjCrptIndepend findTbTjCrptIndepend(Integer crptRid,Integer unitId){
		if(crptRid == null || unitId == null){
			return null;
		}
		StringBuilder sb = new StringBuilder();
		sb.append("	SELECT T FROM TbTjCrptIndepend T WHERE T.fkByCrptId.rid=").append(crptRid).append(" and T.fkByUnitId.rid = ").append(unitId);
		List<TbTjCrptIndepend> list = em.createQuery(sb.toString()).getResultList();
		if (null!=list && list.size()>0) {
			return list.get(0);
		}
		return null;
	}

	/**
	 * 用人/工单位的联系人和联系电话
	 *
	 * @param busType 业务类型
	 * @param crptRid 企业RID
	 * @param unitId  填报机构单位RID
	 * @return 企业信息—各单位独立信息
	 */
	@Transactional(readOnly = true)
	public TbTjCrptIndepend findTbTjCrptIndependByBusType(Integer busType, Integer crptRid, Integer unitId) {
		if (ObjectUtil.isEmpty(busType) || ObjectUtil.isEmpty(crptRid) || ObjectUtil.isEmpty(unitId)) {
			return new TbTjCrptIndepend();
		}
		String hql = "select t from TbTjCrptIndepend t" +
				" where t.busType =" + busType +
				" and t.fkByCrptId.rid=" + crptRid +
				" and t.fkByUnitId.rid=" + unitId;
		TbTjCrptIndepend crptIndepend = this.commService.findOneByHql(hql, TbTjCrptIndepend.class);
		if (ObjectUtil.isEmpty(crptIndepend)) {
			return new TbTjCrptIndepend();
		}
		return crptIndepend;
	}

	/**
	 *  <p>方法描述：根据操作人查询正在导出的条数</p>
	 * @MethodAuthor hsj
	 */
	@Transactional(readOnly = true)
	public  Integer findTdTjExportByOperPsnId(String typeId){
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT COUNT(T.RID) FROM TD_TJ_EXPORT T WHERE T.STATE = 0 AND T.OPER_PSN_ID =  ").append(Global.getUser().getRid()).append(" AND BUS_TYPE_ID = ").append(typeId);
		int count = commService.findCountBySql(sql.toString());
		return  count;
	};

	/**
	 *  <p>方法描述：异步导出保存</p>
	 * @MethodAuthor hsj
	 */
	@Transactional(readOnly = false)
    public void saveTdTjExport(TdTjExport tdTjExport) {
		this.upsertEntity(tdTjExport);
    }
}
