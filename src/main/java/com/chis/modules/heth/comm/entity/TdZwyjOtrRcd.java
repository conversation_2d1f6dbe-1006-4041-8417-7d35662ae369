package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-11-16
 */
@Entity
@Table(name = "TD_ZWYJ_OTR_RCD")
@SequenceGenerator(name = "TdZwyjOtrRcd", sequenceName = "TD_ZWYJ_OTR_RCD_SEQ", allocationSize = 1)
public class TdZwyjOtrRcd implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String bhkCode;
	private TbTjSrvorg fkByBhkorgId;
	private Integer ifOutRange;
	private String proMsg;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjOtrRcd() {
	}

	public TdZwyjOtrRcd(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjOtrRcd")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "BHK_CODE")	
	public String getBhkCode() {
		return bhkCode;
	}

	public void setBhkCode(String bhkCode) {
		this.bhkCode = bhkCode;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BHKORG_ID")			
	public TbTjSrvorg getFkByBhkorgId() {
		return fkByBhkorgId;
	}

	public void setFkByBhkorgId(TbTjSrvorg fkByBhkorgId) {
		this.fkByBhkorgId = fkByBhkorgId;
	}	
			
	@Column(name = "IF_OUT_RANGE")	
	public Integer getIfOutRange() {
		return ifOutRange;
	}

	public void setIfOutRange(Integer ifOutRange) {
		this.ifOutRange = ifOutRange;
	}	
			
	@Column(name = "PRO_MSG")	
	public String getProMsg() {
		return proMsg;
	}

	public void setProMsg(String proMsg) {
		this.proMsg = proMsg;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}