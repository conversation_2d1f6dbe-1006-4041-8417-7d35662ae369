package com.chis.modules.heth.comm.logic;

import java.io.Serializable;

/**
 * <p>类描述：动态单元格样式 </p>
 * @ClassAuthor： pw 2022/9/9
 **/
public class ShowColumnPO implements Serializable {
    private static final long serialVersionUID = -4037985074664808850L;
    /**合并行数，默认为1*/
    private Integer rowspan = 1;
    /**合并列数，默认为1*/
    private Integer colspan = 1;
    /**前端页面样式*/
    private String style;
    /** 单元格显示值 */
    private String colVal;

    public Integer getRowspan() {
        return rowspan;
    }

    public void setRowspan(Integer rowspan) {
        this.rowspan = rowspan;
    }

    public Integer getColspan() {
        return colspan;
    }

    public void setColspan(Integer colspan) {
        this.colspan = colspan;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public String getColVal() {
        return colVal;
    }

    public void setColVal(String colVal) {
        this.colVal = colVal;
    }
}
