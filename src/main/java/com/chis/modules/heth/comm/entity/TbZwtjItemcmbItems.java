package com.chis.modules.heth.comm.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 项目组合与项目关系（GBZ-188）
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置
 *
 * <AUTHOR>
 * @createDate 2014年9月1日 上午9:05:53
 * @LastModify LuXuekun
 * @ModifyDate 2014年9月1日 上午9:05:53 修改人：lqq 修改时间：2014-09-10 修改内容：新增方法
 *
 */
@Entity
@Table(name = "TB_ZWTJ_ITEMCMB_ITEMS", uniqueConstraints = @UniqueConstraint(columnNames = {
        "ITEM_CMBID", "ITEM_ID" }))
@SequenceGenerator(name = "TbZwtjItemcmbItemsSeq", sequenceName = "TB_ZWTJ_ITEMCMB_ITEMS_SEQ", allocationSize = 1)
public class TbZwtjItemcmbItems implements java.io.Serializable {

    private static final long serialVersionUID = 1914156279621317190L;
    private Integer rid;
    private TbTjItems tbTjItems;// 项目组合ID
    private TsSimpleCode tsSimpleCode;// 体检项目ID
    private short stopTag;// 停用标记
    private Date createDate;// 创建日期
    private Integer createManid;// 创建人
    private Date modifyDate;// 修改日期
    private Integer modifyManid;// 修改人
    //+必检项目判定方式20220224
    private  Integer deterWay;


    public TbZwtjItemcmbItems() {
    }

    public TbZwtjItemcmbItems(Integer rid) {
        this.rid = rid;
    }

    public TbZwtjItemcmbItems(Integer rid, TbTjItems tbTjItems,
                              TsSimpleCode tsSimpleCode, short stopTag, Date createDate,
                              Integer createManid) {
        this.rid = rid;
        this.tbTjItems = tbTjItems;
        this.tsSimpleCode = tsSimpleCode;
        this.stopTag = stopTag;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    public TbZwtjItemcmbItems(Integer rid, TbTjItems tbTjItems,
                              TsSimpleCode tsSimpleCode, short stopTag, Date createDate,
                              Integer createManid, Date modifyDate, Integer modifyManid,Integer deterWay) {
        this.rid = rid;
        this.tbTjItems = tbTjItems;
        this.tsSimpleCode = tsSimpleCode;
        this.stopTag = stopTag;
        this.createDate = createDate;
        this.createManid = createManid;
        this.modifyDate = modifyDate;
        this.modifyManid = modifyManid;
        this.deterWay = deterWay;
    }

    @Id
    @GeneratedValue(generator = "TbZwtjItemcmbItemsSeq", strategy = GenerationType.SEQUENCE)
    @Column(name = "RID", unique = true )
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "ITEM_ID" )
    public TbTjItems getTbTjItems() {
        return this.tbTjItems;
    }

    public void setTbTjItems(TbTjItems tbTjItems) {
        this.tbTjItems = tbTjItems;
    }

    @ManyToOne
    @JoinColumn(name = "ITEM_CMBID" )
    public TsSimpleCode getTsSimpleCode() {
        return this.tsSimpleCode;
    }

    public void setTsSimpleCode(TsSimpleCode tsSimpleCode) {
        this.tsSimpleCode = tsSimpleCode;
    }

    @Column(name = "STOP_TAG" )
    public short getStopTag() {
        return this.stopTag;
    }

    public void setStopTag(short stopTag) {
        this.stopTag = stopTag;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Column(name = "DETER_WAY")
    public Integer getDeterWay() {
        return deterWay;
    }

    public void setDeterWay(Integer deterWay) {
        this.deterWay = deterWay;
    }
}
