package com.chis.modules.heth.comm.web;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.json.CrptDialogParamJson;
import com.chis.modules.system.entity.TsUnit;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import com.chis.common.bean.FastReportData;
import com.chis.common.bean.FastReportDataRef;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.GroupUtil;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MapUtils;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.heth.comm.rptvo.BadRsnTypeVo;
import com.chis.modules.heth.comm.rptvo.HethChkSmaryCommPsnSubVo;
import com.chis.modules.heth.comm.rptvo.HethChkSmaryCommSubVo;
import com.chis.modules.heth.comm.rptvo.HethChkSmaryCommVo;
import com.chis.modules.heth.comm.rptvo.PostVo;
import com.chis.modules.heth.comm.utils.ZwChkSmaryCommUtils;
import com.chis.modules.system.entity.TbZwWritsort;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IFastReport;
import com.chis.modules.system.logic.SysReturnPojo;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FastReportBean;

/**
 * <p>类描述：职业性有害因素监测卡填报  beeen </p>
 *
 * @ClassAuthor: yzz
 * @date： 2021年10月20日
 **/

@ManagedBean(name = "tdZwHethChkSmaryListBean")
@ViewScoped
public class TdZwHethChkSmaryListBean extends AbstractReportCardChkSmaryListBean implements IFastReport {

    /**查询条件：报告卡编码*/
    private String searchRptNo;

    /**查询条件：用工单位地区集合*/
    private List<TsZone> zoneList;
    /**查询条件：用工单位地区名称*/
    private String searchZoneName;
    /**查询条件：用工单位地区编码*/
    private String searchZoneCode;
    /**查询条件：用工单位名称*/
    private String searchUnitName;
    /**查询条件：用人单位名称*/
    private String searchCrptName;
    /**查询条件：报告日期查询-开始日期*/
    private Date searchRcvBdate;
    /**查询条件：报告日期查询-结束日期*/
    private Date searchRcvEdate;
    /**查询条件：状态*/
    protected List<SelectItem> stateList = new ArrayList<>();
    /**查询条件：选中状态*/
    private String[] states;
    /**审核等级*/
    private String checkLevel;

    private TdZwTjorginfoNew tjorginfo;
    private String crpyType = null;
    private Boolean selEmpCrpt = Boolean.FALSE;
    /** 检测情况-危害因素对象下标 */
    private Integer badrsnIndex;
    /**文书*/
    private boolean ifShowDesign = false;
    /**系统时间*/
    private Date nowDate;
    /**文书类型*/
    private TbZwWritsort writsort;

    /**浓度,码表5532*/
    private List<TsSimpleCode> fkByThickTypeList;
    /**浓度*/
    Map<Integer,TsSimpleCode> fkByThickTypeMap;
    /**最新流程状态*/
    private TdZwBgkLastSta tdZwBgkLastSta;

    /**文书报表*/
    private FastReportBean fastReportBean;

    /**是否显示撤销按钮*/
    private boolean ifShowCancel;

    public TdZwHethChkSmaryListBean(){
        String value = PropertyUtils.getValueWithoutException("rpt.ifDesign");
        if("1".equals(value)){
            ifShowDesign = true;
        }else{
            ifShowDesign = false;
        }
        nowDate=new Date();
        this.ifSQL = true;
        checkLevel=PropertyUtils.getValue("checkLevel");
        initParam();
        initState();
        this.ifShowCancel=false;
        this.searchAction();
    }
    

    /**
     * <p>方法描述：初始化状态</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-21
     **/
    public void initState(){
        if(StringUtils.isNotBlank(checkLevel)&&"3".equals(checkLevel)){
            stateList.add(new SelectItem("0","待提交") );
            stateList.add(new SelectItem("1","待初审") );
            stateList.add(new SelectItem("2","已退回") );
            stateList.add(new SelectItem("3","待复审") );
            stateList.add(new SelectItem("5","待终审") );
            stateList.add(new SelectItem("7","终审通过") );
        }
        if(StringUtils.isNotBlank(checkLevel)&&"2".equals(checkLevel)){
            stateList.add(new SelectItem("0","待提交") );
            stateList.add(new SelectItem("1","待初审") );
            stateList.add(new SelectItem("2","已退回") );
            stateList.add(new SelectItem("5","待终审") );
            stateList.add(new SelectItem("7","终审通过") );
        }
        states=new String[]{"0","2"};
    }


    /**
     * <p>方法描述：初始化方法</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-21
     **/
    public void initParam(){
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.searchZoneCode = tsZone.getZoneCode().substring(0,2)+"0000000000";

        this.searchZoneName = tsZone.getFullName().split("_")[0];
        /*** 地区初始化 */
        this.zoneList = this.commService.findZoneList(false, searchZoneCode, null, null);
        // 默认：当年1号
        this.searchRcvBdate = DateUtils.getYearFirstDay(new Date());
        // 默认：今天
        this.searchRcvEdate =new Date();

        fkByThickTypeList = commService.findSimpleCodesByTypeId("'5532'");
        if(!CollectionUtils.isEmpty(fkByThickTypeList)){
            fkByThickTypeMap = new HashMap<>();
            for(TsSimpleCode t:fkByThickTypeList){
                fkByThickTypeMap.put(t.getRid(),t);
            }
        }
        colInt = postList.size();

    }
    /**
     *  <p>方法描述：初始化添加页面</p>
     * @MethodAuthor hsj
     */
    @Override
    public void addInit() {
        chkSmary = new TdZwHethChkSmaryComm();
        //年份
        chkSmary.setRptYear(Integer.valueOf(DateUtils.getYear()));
        //填表人信息，默认为当前登录人
        chkSmary.setFillFormPsn( Global.getUser().getUsername());
        chkSmary.setFillLink(Global.getUser().getMbNum());
        chkSmary.setFillDate(new Date());
        chkSmary.setFkByFillSysUnitId(Global.getUser().getTsUnit());
        //报告人信息
        chkSmary.setRptPsn(Global.getUser().getUsername());
        chkSmary.setRptLink(Global.getUser().getMbNum());
        chkSmary.setRptDate(new Date());
        chkSmary.setDelMark(0);
        //职业性有害因素检测情况默认选中“是”
        chkSmary.setIfOpenOneWeek(1);

        //接害
        badRsnTypeVoList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(badrsnTypeList) && !CollectionUtils.isEmpty(postList)){
            for(TsSimpleCode t : badrsnTypeList){
                BadRsnTypeVo badRsnTypeVo = new BadRsnTypeVo();
                badRsnTypeVo.setBadrsnType(t);
                List<PostVo> postVoList = new ArrayList<>();
                for(TsSimpleCode t1:postList){
                    PostVo postVo =new PostVo();
                    postVo.setPost(t1);
                    postVoList.add(postVo);
                }
                badRsnTypeVo.setPostVos(postVoList);
                badRsnTypeVoList.add(badRsnTypeVo);
            }
        }
        ZwChkSmaryCommUtils.initEntity(chkSmary);
        this.tdZwBgkLastSta = new TdZwBgkLastSta();
        this.tdZwBgkLastSta.setCartType(1);
        this.tdZwBgkLastSta.setState(0);
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:jcSubList");
        if(null != dataTable){
            dataTable.setRows(10);
            dataTable.setFirst(0);
        }
    }

    /**
     *  <p>方法描述：修改初始化</p>
     * @MethodAuthor hsj
     */
    @Override
    public void modInit() {
        if (null==rid) {
            return;
        }
        chkSmary = chkSmaryService.searchChkSmary(rid);
        if (chkSmary.getIfOpenOneWeek() == null) {
            chkSmary.setIfOpenOneWeek(1);
        }
        this.tdZwBgkLastSta = this.chkSmaryService.findTdZwBgkLastSta(this.rid, 1);
        //接害情况封装
        packageBadRsnTypeVo();
        ZwChkSmaryCommUtils.initEntity(chkSmary);
        if(StringUtils.isNotBlank(chkSmary.getAnnexPath())){
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.update("tabView:editForm");
            currentInstance.execute("disabledInput('true','chkSmaryDiv')");
        }
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:jcSubList");
        if(null != dataTable){
            dataTable.setRows(10);
            dataTable.setFirst(0);
        }
    }

    @Override
    public void viewInit() {
        super.viewInit();
        TdZwBgkLastSta tdZwBgkLastSta = this.chkSmaryService.findTdZwBgkLastSta(this.rid, 1);
        this.ifShowCancel=false;
        TsZone zone = chkSmary.getFkByEmpZoneId();
        //市直属
        if ("1".equals(zone.getIfCityDirect()) && new Integer("3").equals(tdZwBgkLastSta.getState())) {
            this.ifShowCancel=true;
        } else if ("1".equals(zone.getIfProvDirect()) && new Integer("5").equals(tdZwBgkLastSta.getState())){
            //省直属
            this.ifShowCancel=true;
        }else if(new Integer("1").equals(tdZwBgkLastSta.getState())) {
            this.ifShowCancel=true;
        }
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:viewForm:jcSubList");
        if(null != dataTable){
            dataTable.setRows(10);
            dataTable.setFirst(0);
        }
    }

    /**
     *  <p>方法描述：暂存</p>
     * @MethodAuthor hsj
     */
    @Override
    public void saveAction() {
        try {
            // 若已制作，不走逻辑
            if (StringUtils.isNotBlank(chkSmary.getAnnexPath())) {
                JsfUtil.addSuccessMessage("保存成功！");
                return;
            }
            if (veryData()) {
                return;
            }
            this.chkSmaryService.saveTdZwHethChkSmary(chkSmary,this.tdZwBgkLastSta);
            ZwChkSmaryCommUtils.initEntity(chkSmary);
            JsfUtil.addSuccessMessage("保存成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }

    }

    /**
     *  <p>方法描述：验证数据</p>
     * @MethodAuthor hsj
     */
    private boolean veryData() {
        boolean flag = false;

        TsUnit userUnit = Global.getUser().getTsUnit();
        String comZoneGb2 = "";
        if (userUnit != null && userUnit.getTsZone() != null
                && StringUtils.isNotBlank(userUnit.getTsZone().getZoneGb())
                && userUnit.getTsZone().getZoneGb().length() >= 2) {
            comZoneGb2 = userUnit.getTsZone().getZoneGb().substring(0, 2);
        }
        //用人单位信息
        if (StringUtils.isBlank(chkSmary.getCrptName())) {
            JsfUtil.addErrorMessage("用人单位名称不能为空！");
            flag = true;
        } else if (this.chkSmary.getFkByIndusTypeId() == null
                || !"2".equals(this.chkSmary.getFkByIndusTypeId().getExtendS1())) {
            String zoneGb = "";
            if (this.chkSmary.getFkByZoneId() != null
                    && StringUtils.isNotBlank(this.chkSmary.getFkByZoneId().getZoneGb())) {
                zoneGb = this.chkSmary.getFkByZoneId().getZoneGb();
            }
            if (StringUtils.isBlank(zoneGb) || StringUtils.isBlank(comZoneGb2) || !zoneGb.startsWith(comZoneGb2)) {
                JsfUtil.addErrorMessage("用人单位行业类别不为“人力资源服务”行业时，仅可选择本省范围内的单位！");
                flag = true;
            }
        } else if (this.chkSmary.getFkByIndusTypeId() != null
                && "2".equals(this.chkSmary.getFkByIndusTypeId().getExtendS1())) {
            if (this.chkSmary.getFkByCrptId() != null && this.chkSmary.getFkByEmpCrptId() != null
                    && this.chkSmary.getFkByCrptId().getRid() != null
                    && this.chkSmary.getFkByCrptId().getRid().equals(this.chkSmary.getFkByEmpCrptId().getRid())) {
                JsfUtil.addErrorMessage("当用人单位行业类别是人力资源时，用人单位和用工单位不能相同！");
                flag = true;
            }
        }
        //用工单位信息
        if (StringUtils.isBlank(chkSmary.getEmpCrptName())) {
            JsfUtil.addErrorMessage("用工单位名称不能为空！");
            flag = true;
        } else {
            String zoneGb = "";
            if (this.chkSmary.getFkByEmpZoneId() != null
                    && StringUtils.isNotBlank(this.chkSmary.getFkByEmpZoneId().getZoneGb())) {
                zoneGb = this.chkSmary.getFkByEmpZoneId().getZoneGb();
            }
            if (StringUtils.isBlank(zoneGb) || StringUtils.isBlank(comZoneGb2) || !zoneGb.startsWith(comZoneGb2)) {
                JsfUtil.addErrorMessage("用工单位仅可选择本省范围内的单位！");
                flag = true;
            }
            if (this.chkSmary.getFkByEmpIndusTypeId() != null
                    && "2".equals(this.chkSmary.getFkByEmpIndusTypeId().getExtendS1())) {
                JsfUtil.addErrorMessage("用工单位的行业类别不能为“人力资源服务”行业！");
                flag = true;
            }
        }
        // 验证电话、邮编、填表人联系点话
        if (StringUtils.isNotBlank(chkSmary.getSafephone())) {
            if (!StringUtils.vertyPhone(chkSmary.getSafephone())) {
                JsfUtil.addErrorMessage("用人单位联系人电话格式不正确！");
                flag = true;
            }
        }
        if (StringUtils.isNotBlank(chkSmary.getFillLink())) {
            if (!StringUtils.vertyPhone(chkSmary.getFillLink())) {
                JsfUtil.addErrorMessage("填表人联系电话格式不正确！");
                flag = true;
            }
        }
        if (StringUtils.isNotBlank(chkSmary.getRptLink())) {
            if (!StringUtils.vertyPhone(chkSmary.getRptLink())) {
                JsfUtil.addErrorMessage("报告人联系电话格式不正确！");
                flag = true;
            }
        }
        if (StringUtils.isNotBlank(chkSmary.getPostcode())) {
            if (!StringUtils.vertyPost(chkSmary.getPostcode())) {
                JsfUtil.addErrorMessage("邮编格式不正确！");
                flag = true;
            }
        }
        //接害情况
        //当年接触职业性有害因素作业人数不能为空
        Integer tchBadrsnNum = chkSmary.getTchBadrsnNum() == null ? 0 : chkSmary.getTchBadrsnNum();
        List<TdZwHethChkSubComm> tdZwHethChkSubCommList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(badRsnTypeVoList)){
            for(BadRsnTypeVo badRsnTypeVo:badRsnTypeVoList){
                Integer touchNum = badRsnTypeVo.getTouchNum() == null ? 0 :badRsnTypeVo.getTouchNum();
                TdZwHethChkSubComm tdZwHethChkSubComm = new TdZwHethChkSubComm();
                tdZwHethChkSubComm.setFkByTchRsnId(badRsnTypeVo.getBadrsnType());
                tdZwHethChkSubComm.setTouchNum(badRsnTypeVo.getTouchNum());
                tdZwHethChkSubCommList.add(tdZwHethChkSubComm);
                if(touchNum > tchBadrsnNum){
                    JsfUtil.addErrorMessage(badRsnTypeVo.getBadrsnType().getCodeName()+"不能大于当年接触职业性有害因素作业人数！");
                    flag = true;
                }
                if(!CollectionUtils.isEmpty(badRsnTypeVo.getPostVos())){
                    for(PostVo postVo : badRsnTypeVo.getPostVos()){
                        Integer needChkNum =postVo.getNeedChkNum() == null ? 0 : postVo.getNeedChkNum();
                        tdZwHethChkSubComm = new TdZwHethChkSubComm();
                        tdZwHethChkSubComm.setFkByTchRsnId(badRsnTypeVo.getBadrsnType());
                        tdZwHethChkSubComm.setFkByOnguardStateid(postVo.getPost());
                        tdZwHethChkSubComm.setNeedChkNum(postVo.getNeedChkNum());
                        tdZwHethChkSubCommList.add(tdZwHethChkSubComm);
                        if(needChkNum > touchNum){
                            JsfUtil.addErrorMessage(badRsnTypeVo.getBadrsnType().getCodeName() + postVo.getPost().getCodeName()+"不能大于"+badRsnTypeVo.getBadrsnType().getCodeName() +"！");
                            flag = true;
                        }
                    }
                }
            }
        }
        chkSmary.setChkSubList(tdZwHethChkSubCommList);
        List<Date> dateList =new ArrayList<>();
        //职业性有害因素检测情况
        if (new Integer(1).equals(this.chkSmary.getIfOpenOneWeek())
                && !CollectionUtils.isEmpty(this.chkSmary.getJcSubList())) {
            List<TdZwHethJcSubComm> jcSubList = this.chkSmary.getJcSubList();
            for (int i = 0; i < jcSubList.size(); i++) {
                TdZwHethJcSubComm jcSub = jcSubList.get(i);
                //浓强度
                if (null != jcSub.getThickTypeId()) {
                    jcSub.setFkByThickTypeId(fkByThickTypeMap.get(jcSub.getThickTypeId()));
                } else {
                    jcSub.setFkByThickTypeId(new TsSimpleCode());
                }
                if (null != jcSub.getJcTime()) {
                    dateList.add(jcSub.getJcTime());
                    if (jcSub.getJcTime().after(new Date())) {
                        String msg = "第" + (i + 1) + "行检测日期应小于等于当前日期！";
                        JsfUtil.addErrorMessage(msg);
                        flag = true;
                    }

                }
            }
        }
        //最大选择到当天，大于等于有害因素检测中最大的检测日期
        if(null != chkSmary.getFillDate() ){
            if(chkSmary.getFillDate().after(new Date())){
                JsfUtil.addErrorMessage("填表日期应小于等于当前日期！");
                flag = true;
            }
            if(!CollectionUtils.isEmpty(dateList) ){
                Date d = Collections.max(dateList);
                if(d.after(chkSmary.getFillDate())){
                    JsfUtil.addErrorMessage("填表日期应大于等于有害因素检测中最大的检测日期！");
                    flag = true;
                }
            }
        }

        //报告日期大于等于填报日期
        if(null != chkSmary.getFillDate() && null != chkSmary.getRptDate() && chkSmary.getFillDate().after(chkSmary.getRptDate())){
            JsfUtil.addErrorMessage("填表日期应小于等于报告日期！");
            flag = true;
        }

        //备注大小判断
        if(StringUtils.isNotBlank(chkSmary.getRmk())&&chkSmary.getRmk().length()>500){
            JsfUtil.addErrorMessage("备注长度不能超过500！");
            flag = true;
        }
        return flag;
    }
    public void beforeSubmit() {
        try{
            //为空验证
            if (veryNullData()) {
                return;
            }
            if (veryData()) {
                return;
            }
            // 若已制作，不可提交
            if (StringUtils.isBlank(chkSmary.getAnnexPath())) {
                JsfUtil.addErrorMessage("请先制作文书！");
                return;
            }
            RequestContext context = RequestContext.getCurrentInstance();
            context.execute("PF('ConfirmDialog').show()");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }
    /**
     *  <p>方法描述：为空验证</p>
     * @MethodAuthor hsj
     */
    private boolean veryNullData() {
        boolean flag = false;
        //基本信息
        if(null == chkSmary.getFkByCrptId()){
            JsfUtil.addErrorMessage("用人单位不能为空！");
            flag = true;
        }
        if(null == chkSmary.getFkByEmpCrptId()){
            JsfUtil.addErrorMessage("用工单位不能为空！");
            flag = true;
        }
        //接害情况
        //当年接触职业性有害因素作业人数不能为空
        if(null == chkSmary.getTchBadrsnNum()){
            JsfUtil.addErrorMessage("当年接触职业性有害因素作业人数不能为空！");
            flag = true;
        }
        if(!CollectionUtils.isEmpty(badRsnTypeVoList)){
            for(BadRsnTypeVo badRsnTypeVo:badRsnTypeVoList){
                if(null == badRsnTypeVo.getTouchNum()){
                    JsfUtil.addErrorMessage(badRsnTypeVo.getBadrsnType().getCodeName()+"不能为空！");
                    flag = true;
                }
                if(!CollectionUtils.isEmpty(badRsnTypeVo.getPostVos())){
                    for(PostVo postVo : badRsnTypeVo.getPostVos()){
                        if(null == postVo.getNeedChkNum()){
                            JsfUtil.addErrorMessage(badRsnTypeVo.getBadrsnType().getCodeName() + postVo.getPost().getCodeName()+"不能为空！");
                            flag = true;
                        }
                    }
                }
            }
        }
        //职业性有害因素检测情况
        if (new Integer(1).equals(this.chkSmary.getIfOpenOneWeek())) {
            List<TdZwHethJcSubComm> jcSubList = this.chkSmary.getJcSubList();
            if (CollectionUtils.isEmpty(jcSubList)) {
                JsfUtil.addErrorMessage("职业性有害因素检测情况区域必须有一条检测记录！");
                flag = true;
            } else {
                for (int i = 0; i < jcSubList.size(); i++) {
                    TdZwHethJcSubComm jcSub = jcSubList.get(i);
                    if (null == jcSub.getFkByBadrsnId().getRid()) {
                        String msg = "第" + (i + 1) + "行职业性有害因素不能为空！";
                        JsfUtil.addErrorMessage(msg);
                        flag = true;
                    }
                    if (StringUtils.isBlank(jcSub.getWorkPlace())) {
                        String msg = "第" + (i + 1) + "行工作场所不能为空！";
                        JsfUtil.addErrorMessage(msg);
                        flag = true;
                    }
                    if (StringUtils.isBlank(jcSub.getWorkType())) {
                        String msg = "第" + (i + 1) + "行岗位/工种不能为空！";
                        JsfUtil.addErrorMessage(msg);
                        flag = true;
                    }
                    if (null == jcSub.getThickTypeId()) {
                        jcSub.setFkByThickTypeId(new TsSimpleCode());
                        String msg = "第" + (i + 1) + "行浓/强度类型不能为空！";
                        JsfUtil.addErrorMessage(msg);
                        flag = true;
                    } else {
                        jcSub.setFkByThickTypeId(fkByThickTypeMap.get(jcSub.getThickTypeId()));
                    }
                    if (StringUtils.isBlank(jcSub.getJcValMin())) {
                        String msg = "第" + (i + 1) + "行检测值（最小值）不能为空！";
                        JsfUtil.addErrorMessage(msg);
                        flag = true;
                    }
                    if (StringUtils.isBlank(jcSub.getJcValMax())) {
                        String msg = "第" + (i + 1) + "行检测值（最大值）不能为空！";
                        JsfUtil.addErrorMessage(msg);
                        flag = true;
                    }
                    if (null == jcSub.getJcTime()) {
                        String msg = "第" + (i + 1) + "行检测日期不能为空！";
                        JsfUtil.addErrorMessage(msg);
                        flag = true;
                    }
                    if (null == jcSub.getHgFlag()) {
                        String msg = "第" + (i + 1) + "行合格情况不能为空！";
                        JsfUtil.addErrorMessage(msg);
                        flag = true;
                    }
                }
            }
            if (StringUtils.isBlank(this.chkSmary.getJcUnitName())) {
                JsfUtil.addErrorMessage("检测单位不能为空！");
                flag = true;
            }
            if (StringUtils.isBlank(this.chkSmary.getJcUnitCharge())) {
                JsfUtil.addErrorMessage("检测单位负责人不能为空！");
                flag = true;
            }
        }

        if (StringUtils.isBlank(chkSmary.getFillFormPsn())) {
            JsfUtil.addErrorMessage("填表人不能为空！");
            flag = true;
        }
        if (StringUtils.isBlank(chkSmary.getFillLink())) {
            JsfUtil.addErrorMessage("填表人联系电话不能为空！");
            flag = true;
        }
        if (null == chkSmary.getFillDate()) {
            JsfUtil.addErrorMessage("填表日期不能为空！");
            flag = true;
        }
        if (null == chkSmary.getFkByFillSysUnitId()) {
            JsfUtil.addErrorMessage("填表单位/报告单位不能为空！");
            flag = true;
        }
        if (StringUtils.isBlank(chkSmary.getRptPsn())) {
            JsfUtil.addErrorMessage("报告人不能为空！");
            flag = true;
        }
        if (StringUtils.isBlank(chkSmary.getRptLink())) {
            JsfUtil.addErrorMessage("报告人联系电话不能为空！");
            flag = true;
        }
        return flag;
    }

    /**
     *  <p>方法描述：提交</p>
     * @MethodAuthor hsj
     */
    public void submitAction() {
    	try {
    		this.delBgkLastSta();
			this.chkSmaryService.submitTdZwHethChkSmary(chkSmary, this.tdZwBgkLastSta);
            ZwChkSmaryCommUtils.initEntity(chkSmary);
    		JsfUtil.addSuccessMessage("提交成功！");
    		this.backAction();
    		this.searchAction();
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("提交失败！");
		}
    }
    /**
     *  <p>方法描述：职业性有害因素检测添加</p>
     * @MethodAuthor hsj
     */
    public void addJcSub() {
        TdZwHethJcSubComm jcSub = new TdZwHethJcSubComm();
        jcSub.setFkByBadrsnId(new TsSimpleCode());
        chkSmary.getJcSubList().add(jcSub);
    }
    /**
     *  <p>方法描述：职业性有害因素检测删除</p>
     * @MethodAuthor hsj
     */
    public void delCurBadAction(TdZwHethJcSubComm jcSub) {
        if (null != jcSub) {
            chkSmary.getJcSubList().remove(jcSub);
        }
    }



    /**
     * <p>方法描述：文书制作</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-23
     **/
    public void buildWritReport() {
        // 提交验证  文书验证
        if(veryNullData() || !veryWritsort()||veryData()){
            return;
        }
       //调用保存方法
        this.chkSmaryService.saveTdZwHethChkSmary(chkSmary,this.tdZwBgkLastSta);
        ZwChkSmaryCommUtils.initEntity(chkSmary);
        showShadeTip();
        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update("tabView:editForm");
        currentInstance.execute("disabledInput('true','chkSmaryDiv')");

    }
    /**
     * <p>方法描述：制作文书</p>
     * @MethodAuthor rcj,2018年12月24日,tobuildWritReport
     * */
    public void tobuildWritReport(){
        //根据模板生成附件1、文书编号2、封装签章工具2、上传到本地
        this.fastReportBean = new FastReportBean(this, "HETH_2037");
        SysReturnPojo returnPojo = fastReportBean.showPDFReportAction("");
        String type = returnPojo.getType();
        String mess = returnPojo.getMess();
        if (!"00".equals(type)) {
            System.out.println("type："+type+"！！！！！mess："+mess);
            JsfUtil.addErrorMessage("生成文书失败，请稍后尝试！");
            return;
        }
        //附件地址
        chkSmary.setAnnexPath(mess);
        //保存已制作、文书附件
        this.chkSmaryService.saveChkSmaryAnnexPath(this.chkSmary);
        JsfUtil.addSuccessMessage("制作成功！");
        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.execute("disabledInput('true','chkSmaryDiv')");
    }
    /**
     *
     * <p>描述：文书封装逻辑</p>
     *
     *  @MethodAuthor: 龚哲,2021/10/22 18:09,packageDatas
     */
    private List<FastReportData> packageDatas() {
        List<FastReportData> rptDataList = new ArrayList<FastReportData>();
        //查询主表
        if(null==chkSmary.getRid()){
            return rptDataList;
        }
        TdZwHethChkSmaryComm chkSmaryComm = chkSmaryService.searchChkSmary(chkSmary.getRid());
        if(chkSmaryComm == null){
            return rptDataList;
        }
        //封装主表数据集
        List<HethChkSmaryCommVo> smaryVos = new ArrayList<>();
        HethChkSmaryCommVo smaryVo = new HethChkSmaryCommVo();
        smaryVo.setRptYear(StringUtils.objectToString(chkSmaryComm.getRptYear()));
        smaryVo.setCrptName(chkSmaryComm.getCrptName());
        smaryVo.setCreditCode(chkSmaryComm.getCreditCode());
        smaryVo.setEconomyName(chkSmaryComm.getFkByEconomyId().getCodeName());
        smaryVo.setIndusTypeName(chkSmaryComm.getFkByIndusTypeId().getCodeName());
        smaryVo.setCrptSizeName(chkSmaryComm.getFkByCrptSizeId().getCodeName());
        smaryVo.setZoneName(StringUtils.objectToString(chkSmaryComm.getFkByZoneId().getFullName()).replaceAll("_",""));
        smaryVo.setAddress(chkSmaryComm.getAddress());
        smaryVo.setPostCode(chkSmaryComm.getPostcode());
        smaryVo.setSafePosition(chkSmaryComm.getSafeposition());
        smaryVo.setSafePhone(chkSmaryComm.getSafephone());
        smaryVo.setStaffNum(StringUtils.objectToString(chkSmaryComm.getStaffNum()));
        smaryVo.setWorkNum(StringUtils.objectToString(chkSmaryComm.getWorkNum()));
        smaryVo.setEmpCrptName(chkSmaryComm.getEmpCrptName());
        smaryVo.setEmpCreditCode(chkSmaryComm.getEmpCreditCode());
        smaryVo.setEmpEconomyName(chkSmaryComm.getFkByEmpEconomyId().getCodeName());
        smaryVo.setEmpIndusTypeName(chkSmaryComm.getFkByEmpIndusTypeId().getCodeName());
        smaryVo.setEmpCrptSizeName(chkSmaryComm.getFkByEmpCrptSizeId().getCodeName());
        smaryVo.setEmpZoneName(StringUtils.objectToString(chkSmaryComm.getFkByEmpZoneId().getFullName()).replaceAll("_",""));
        smaryVo.setTchBadrsnNum((StringUtils.objectToString(chkSmaryComm.getTchBadrsnNum())));
        smaryVo.setJcUnitName(chkSmaryComm.getJcUnitName());
        smaryVo.setJcUnitCharge(chkSmaryComm.getJcUnitCharge());
        smaryVo.setFillUnitName(chkSmaryComm.getFkByFillSysUnitId().getUnitname());//填表单位
        smaryVo.setFillFormPsn(chkSmaryComm.getFillFormPsn());//填表人
        smaryVo.setFillLink(chkSmaryComm.getFillLink());
        smaryVo.setFillDate(DateUtils.formatDate(chkSmaryComm.getFillDate()));
        smaryVo.setRptUnitName(chkSmaryComm.getFkByFillSysUnitId().getUnitname());//报告单位
        smaryVo.setRptPsn(chkSmaryComm.getRptPsn());//报告人
        smaryVo.setRptLink(chkSmaryComm.getRptLink());
        smaryVo.setRptDate(DateUtils.formatDate(chkSmaryComm.getRptDate()));
        smaryVo.setRmk(chkSmaryComm.getRmk());
        smaryVos.add(smaryVo);
        rptDataList.add(new FastReportData<>(HethChkSmaryCommVo.class,
                "smaryVo", smaryVos));

        //封装接害情况数据集
        List<HethChkSmaryCommPsnSubVo> smaryCommPsnSubVos = new ArrayList<>();
        if(!CollectionUtils.isEmpty(chkSmaryComm.getChkSubList())){
            //根据getFkByBadrsnId分组
            Map<TsSimpleCode,List<TdZwHethChkSubComm>> tdZwHethChkSubCommMap = new HashMap<TsSimpleCode, List<TdZwHethChkSubComm>>();
            GroupUtil.listGroup2Map(chkSmaryComm.getChkSubList(),tdZwHethChkSubCommMap,TdZwHethChkSubComm.class,"getFkByTchRsnId");
            if(!CollectionUtils.isEmpty(tdZwHethChkSubCommMap)) {
                //codeNo 排序
                List<Map.Entry<TsSimpleCode,List<TdZwHethChkSubComm>>> list = new ArrayList<>(tdZwHethChkSubCommMap.entrySet());

                Collections.sort(list, new Comparator<Map.Entry<TsSimpleCode,List<TdZwHethChkSubComm>>>() {
                    @Override
                    public int compare(Map.Entry<TsSimpleCode,List<TdZwHethChkSubComm>> o1, Map.Entry<TsSimpleCode,List<TdZwHethChkSubComm>> o2) {
                        return o1.getKey().getNum().compareTo(o2.getKey().getNum());
                    }
                });
                for (Map.Entry<TsSimpleCode, List<TdZwHethChkSubComm>> entry : list) {
                    HethChkSmaryCommPsnSubVo smaryCommPsnSubVo = new HethChkSmaryCommPsnSubVo();
                    List<TdZwHethChkSubComm> tdZwHethChkSubComms = entry.getValue();
                    TsSimpleCode tchRsn = entry.getKey();//有害因素
                    smaryCommPsnSubVo.setTchRsnName(tchRsn.getCodeName());//有害因素名称
                    if(!CollectionUtils.isEmpty(tdZwHethChkSubComms)){
                        for(TdZwHethChkSubComm t:tdZwHethChkSubComms){
                            TsSimpleCode onguardState = t.getFkByOnguardStateid();//岗位状态
                            String tNum = StringUtils.objectToString(t.getTouchNum());//接触人次
                            String needChkNum = StringUtils.objectToString(t.getNeedChkNum());//应检人次
                            if(StringUtils.isBlank(tNum)){
                                tNum = "/";
                            }
                            if(StringUtils.isBlank(needChkNum)){
                                needChkNum = "/";
                            }
                            if(null == onguardState){
                                smaryCommPsnSubVo.setTouchNum(tNum);
                            }else if("1".equals(onguardState.getExtendS1())){//扩展字段1，extends1：1：上岗前,2：在岗期间,3：离岗时,4：离岗后,5：应急健康检查
                                smaryCommPsnSubVo.setNeedChkNumBefore(needChkNum);
                            }else if("2".equals(onguardState.getExtendS1())){
                                smaryCommPsnSubVo.setNeedChkNumDuring(needChkNum);
                            }else if("3".equals(onguardState.getExtendS1())){
                                smaryCommPsnSubVo.setNeedChkNumAfter(needChkNum);
                            }
                        }
                    }
                    smaryCommPsnSubVos.add(smaryCommPsnSubVo);
                }
            }
        }
        rptDataList.add(new FastReportData<>(HethChkSmaryCommPsnSubVo.class,
                "smaryCommPsnSubVo", smaryCommPsnSubVos));

        //封装有害因素检测情况数据集
        List<HethChkSmaryCommSubVo> smaryCommSubVos = new ArrayList<>();
        List<TdZwHethJcSubComm> jcSubList = chkSmaryComm.getJcSubList();
        for (TdZwHethJcSubComm jcSubComm:jcSubList){
            HethChkSmaryCommSubVo smaryCommSubVo = new HethChkSmaryCommSubVo();
            smaryCommSubVo.setBadrsnName(jcSubComm.getFkByBadrsnId().getCodeName());
            smaryCommSubVo.setWorkPlace(jcSubComm.getWorkPlace());
            smaryCommSubVo.setWorkTypeName(jcSubComm.getWorkType());
            smaryCommSubVo.setThickTypeName(jcSubComm.getFkByThickTypeId().getCodeName());
            smaryCommSubVo.setJcValMin(StringUtils.objectToString(jcSubComm.getJcValMin()));
            smaryCommSubVo.setJcValMax(StringUtils.objectToString(jcSubComm.getJcValMax()));
            smaryCommSubVo.setJcTime(DateUtils.formatDate(jcSubComm.getJcTime()));
            if(jcSubComm.getHgFlag()==0){
                smaryCommSubVo.setHgFlagName("不合格");
            }else if(jcSubComm.getHgFlag()==1){
                smaryCommSubVo.setHgFlagName("合格");
            }
            smaryCommSubVos.add(smaryCommSubVo);
        }
        rptDataList.add(new FastReportData<>(HethChkSmaryCommSubVo.class,
                "smaryCommSubVo", smaryCommSubVos));
        return rptDataList;
    }
    /**
     * <p>方法描述：设计文书</p>
     * @MethodAuthor rcj,2018年12月25日,buildWritReport
     * */
    public void designWritReport() {
        if(!veryWritsort()){
            return;
        }
        this.fastReportBean = new FastReportBean(this, "HETH_2037");
        fastReportBean.designAction();
        RequestContext.getCurrentInstance().execute("frpt_design();");
    }
    /**
     * <p>方法描述：制作文书加载</p>
     * @MethodAuthor rcj,2019年9月5日,showShadeTip
     * */
    public void showShadeTip(){
        RequestContext.getCurrentInstance().execute("showShadeTip()");
    }

    /**
     * <p>方法描述：当前文书验证</p>
     * @MethodAuthor qrr,2018年4月26日,veryWritsort
     * */
    private boolean veryWritsort(){
        boolean flag = true;
        writsort = chkSmaryService.getWritsort("2037");
        //文书未维护
        if (null==writsort) {
            JsfUtil.addErrorMessage("请在文书类型管理中维护该文书的相关信息！");
            return false;
        }
        if (null==writsort.getFkByRptTemplId()) {
            JsfUtil.addErrorMessage("请在文书类型管理中维护该文书的报表模板！");
            return false;
        }
        return flag;
    }

    /**
     * <p>方法描述：文书删除-假删除-因为验证里有封装数据，有坑</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-23
     **/
    public void delMadedwrit() {
        try {
            chkSmary.setAnnexPath(null);
            JsfUtil.addSuccessMessage("删除成功！");
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("disabledInput('false','chkSmaryDiv')");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }
    @Override
    public void processData(List<?> list) {

    }

    @Override
    public void searchAction() {

        super.searchAction();
    }

    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder();
        sb.append(" FROM TD_ZW_HETH_CHK_SMARY T ");
        sb.append(" INNER JOIN TS_ZONE T1 on T.EMP_ZONE_ID=T1.RID ");
        sb.append(" INNER JOIN TD_ZW_BGK_LAST_STA T2 on T.rid=T2.BUS_ID and T2.CART_TYPE=1 ");
        sb.append(" INNER JOIN TS_ZONE T3 on T.EMP_ZONE_ID=T3.RID ");
        sb.append(" where T.FILL_SYS_UNIT_ID=").append(Global.getUser().getTsUnit().getRid());
        //报告卡编号
        if (StringUtils.isNotBlank(this.searchRptNo)) {
            sb.append(" AND T.RPT_NO LIKE :rptNo escape '\\\'");
            this.paramMap.put("rptNo", "%" + StringUtils.convertBFH(this.searchRptNo.trim()) + "%");
        }
        //用工单位地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sb.append(" AND T1.ZONE_GB LIKE :zonecode escape '\\\'");
            this.paramMap.put("zonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        //用工单位名称
        if (StringUtils.isNotBlank(this.searchUnitName)) {
            sb.append(" AND T.EMP_CRPT_NAME LIKE :unitName escape '\\\'");
            this.paramMap.put("unitName", "%" + StringUtils.convertBFH(this.searchUnitName.trim()) + "%");
        }
        //用人单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sb.append(" AND T.CRPT_NAME LIKE :crptName escape '\\\'");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        if (null != this.searchRcvBdate) {
            sb.append(" AND T.RPT_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRcvBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchRcvEdate) {
            sb.append(" AND T.RPT_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRcvEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != states && states.length > 0) {
            StringBuffer stateSb = new StringBuffer();
            for (String state : states) {
                stateSb.append(",").append(state);
            }
            StringBuffer ssb=new StringBuffer();

            if ("3".equals(checkLevel)) {
                if (stateSb.indexOf(",1")!=-1) {//待初审
                    stateSb.delete(stateSb.indexOf(",1"),stateSb.indexOf(",1")+2);
                    if(ssb.length()>0){
                        ssb.append(" or ");
                    }
                    ssb.append(" T2.STATE=1 or (nvl(T3.IF_CITY_DIRECT,0)=1 and T2.STATE=3) ");
                }
                if(stateSb.indexOf(",2")!=-1){//已退回
                    stateSb.delete(stateSb.indexOf(",2"),stateSb.indexOf(",2")+2);
                    if(ssb.length()>0){
                        ssb.append(" or ");
                    }
                    ssb.append("  T2.STATE IN(2,4,6) ");
                }
                if(stateSb.indexOf(",3")!=-1){//待复审
                    stateSb.delete(stateSb.indexOf(",3"),stateSb.indexOf(",3")+2);
                    if(ssb.length()>0){
                        ssb.append(" or ");
                    }
                    ssb.append(" (nvl(T3.IF_CITY_DIRECT,0)=0 and T2.STATE =3)");
                }
            }else if("2".equals(checkLevel)){
                //待初审
                if(stateSb.indexOf(",2")!=-1){//已退回
                    stateSb.delete(stateSb.indexOf(",2"),stateSb.indexOf(",2")+2);
                    if(ssb.length()>0){
                        ssb.append(" or ");
                    }
                    ssb.append(" T2.STATE IN(2,6) ");
                }
            }

            if(stateSb.length()>0){
                if(ssb.length()>0){
                    ssb.append(" or ");
                }
                ssb.append(" (T2.STATE in(").append(stateSb.substring(1)).append("))");
            }
            if(ssb.length()>0){
                sb.append(" and (").append(ssb).append(")");
            }
        }
        sb.append(" AND T.DEL_MARK !=1 ");
        String h2 = "SELECT COUNT(*) " + sb;
        String h1 ="select T.rid, CASE WHEN T1.ZONE_TYPE > 2 THEN SUBSTR( T1.FULL_NAME, INSTR( T1.FULL_NAME, '_' ) + 1 ) ELSE T1.FULL_NAME END ZONE_NAME," +
                "T.EMP_CRPT_NAME,T.EMP_CREDIT_CODE,T.CRPT_NAME,to_char(T.RPT_DATE, 'yyyy-mm-dd'),T2.STATE, " +
                "nvl(T3.IF_CITY_DIRECT,0),nvl(T3.IF_PROV_DIRECT,0),T.RPT_NO "
        + sb.append(" ORDER BY T.RPT_DATE DESC, T1.ZONE_CODE,T.EMP_CRPT_NAME,T.CRPT_NAME");
        return new String[] { h1, h2 };
    }

    /**
     * <p>方法描述：删除记录  标删</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-21
     **/
    public void delAction(){
        try {
            chkSmaryService.delChkSmary(rid);
            this.searchAction();
            JsfUtil.addSuccessMessage("删除成功！");
        }catch (Exception e){
            JsfUtil.addErrorMessage("删除失败！");
        }

    }
/**
 *  <p>方法描述：初始化企业信息</p>
 * @MethodAuthor hsj
 */
    public void selectCrptList() {
        Map<String, Object> options = MapUtils.produceDialogMap(1080,1050,520,505);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<String>();
        paramList = new ArrayList<String>();
        CrptDialogParamJson json = new CrptDialogParamJson();
        json.setBusType("1");
        json.setSource("2");
        json.setIfShowOtherZone(true);
        json.setIfSearchZoneDefault(false);
        if("1".equals(crpyType)){
            json.setSearchCrptName(StringUtils.objectToString(this.chkSmary.getCrptName()));
            json.setAreaValidate(1);
        }else if("2".equals(crpyType)){
            json.setSearchCrptName(StringUtils.objectToString(this.chkSmary.getEmpCrptName()));
            json.setAreaValidate(2);
            if (this.chkSmary.getFkByCrptId() != null) {
                json.setExcludeRids(String.valueOf(this.chkSmary.getFkByCrptId().getRid()));
            }
        }
        paramList.add(JSON.toJSONString(json));
        paramMap.put("jsonParam", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        String useNewCrptSelect = this.commService.findParamValue("USE_NEW_CRPT_SELECT");
        String crptSelectPath = "/webapp/heth/comm/crptCommSelectList";
        if ("1".equals(useNewCrptSelect)) {
            crptSelectPath = "/webapp/heth/comm/crptCommSelectListNew";
        }
        requestContext.openDialog(crptSelectPath, options, paramMap);
    }
/**
 *  <p>方法描述：选择企业后</p>
 * @MethodAuthor hsj
 */
    public void onCrptSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
            TbTjCrptIndepend tbTjCrptIndepend = (TbTjCrptIndepend) selectedMap.get("selectCrptIndepend");
            TbTjCrpt tbTjCrpt = tbTjCrptIndepend.getFkByCrptId();
            if("1".equals(crpyType)){
                //用人单位
                chkSmary.setFkByCrptId(tbTjCrpt);
                chkSmary.setCrptName(tbTjCrpt.getCrptName());
                chkSmary.setCreditCode(tbTjCrpt.getInstitutionCode());
                chkSmary.setFkByEconomyId(tbTjCrpt.getTsSimpleCodeByEconomyId());
                chkSmary.setFkByIndusTypeId(tbTjCrpt.getTsSimpleCodeByIndusTypeId());
                chkSmary.setFkByCrptSizeId(tbTjCrpt.getTsSimpleCodeByCrptSizeId());
                chkSmary.setFkByZoneId(tbTjCrpt.getTsZoneByZoneId());
                chkSmary.setAddress(tbTjCrpt.getAddress());
                chkSmary.setPostcode(tbTjCrpt.getPostCode());
                chkSmary.setSafeposition(tbTjCrptIndepend.getLinkman2());
                chkSmary.setSafephone(tbTjCrptIndepend.getLinkphone2());
                chkSmary.setStaffNum(tbTjCrpt.getWorkForce());
                chkSmary.setWorkNum(tbTjCrpt.getWorkmanNum());
                //根据选择的社会信用代码自动加载检测信息
                initTdZwJcCrptSet();
                this.selEmpCrpt = this.chkSmary.getFkByIndusTypeId() != null
                        && "2".equals(this.chkSmary.getFkByIndusTypeId().getExtendS1());
                if (!this.selEmpCrpt) {
                    pakEmpCrptInfo(tbTjCrpt);
                } else if (tbTjCrpt.getRid() != null && this.chkSmary.getFkByEmpCrptId() != null
                        && tbTjCrpt.getRid().equals(this.chkSmary.getFkByEmpCrptId().getRid())) {
                    this.chkSmary.setFkByEmpCrptId(null);
                    this.chkSmary.setEmpCrptName(null);
                    this.chkSmary.setEmpCreditCode(null);
                    this.chkSmary.setFkByEmpEconomyId(null);
                    this.chkSmary.setFkByEmpIndusTypeId(null);
                    this.chkSmary.setFkByEmpCrptSizeId(null);
                    this.chkSmary.setFkByEmpZoneId(null);
                }
            }else if("2".equals(crpyType)){
                pakEmpCrptInfo(tbTjCrpt);
            }
        }
    }

    private void pakEmpCrptInfo(TbTjCrpt tbTjCrpt) {
        this.chkSmary.setFkByEmpCrptId(tbTjCrpt);
        this.chkSmary.setEmpCrptName(tbTjCrpt.getCrptName());
        this.chkSmary.setEmpCreditCode(tbTjCrpt.getInstitutionCode());
        this.chkSmary.setFkByEmpEconomyId(tbTjCrpt.getTsSimpleCodeByEconomyId());
        this.chkSmary.setFkByEmpIndusTypeId(tbTjCrpt.getTsSimpleCodeByIndusTypeId());
        this.chkSmary.setFkByEmpCrptSizeId(tbTjCrpt.getTsSimpleCodeByCrptSizeId());
        this.chkSmary.setFkByEmpZoneId(tbTjCrpt.getTsZoneByZoneId());
    }

    /**
     *  <p>方法描述：根据选择的社会信用代码自动加载检测信息</p>
     * @MethodAuthor hsj 2022/4/20 10:07
     */
    private void initTdZwJcCrptSet() {
        String hql = " SELECT t FROM TdZwJcCrptSet t WHERE t.creditCode = '"+chkSmary.getCreditCode()+"' ORDER BY RID";
        List<TdZwJcCrptSet> tdZwJcCrptSetList =this.commService.findByHql(hql, TdZwJcCrptSet.class);
        if(!CollectionUtils.isEmpty(tdZwJcCrptSetList)){
            for (TdZwJcCrptSet tdZwJcCrptSet:tdZwJcCrptSetList){
                TdZwHethJcSubComm tdZwHethJcSubComm = new TdZwHethJcSubComm();
                tdZwHethJcSubComm.setFkByMainId(chkSmary);
                tdZwHethJcSubComm.setFkByMainId(chkSmary);
                if(null !=tdZwJcCrptSet.getFkByBadrsnId()){
                    tdZwHethJcSubComm.setFkByBadrsnId(tdZwJcCrptSet.getFkByBadrsnId());
                }else {
                    tdZwHethJcSubComm.setFkByBadrsnId(new TsSimpleCode());
                }
                tdZwHethJcSubComm.setWorkPlace(tdZwJcCrptSet.getWorkPlace());
                tdZwHethJcSubComm.setWorkType(tdZwJcCrptSet.getWorkType());
                tdZwHethJcSubComm.setJcTime(tdZwJcCrptSet.getJcTime());
                tdZwHethJcSubComm.setJcValMax(tdZwJcCrptSet.getJcValMax());
                tdZwHethJcSubComm.setJcValMin(tdZwJcCrptSet.getJcValMin());
                if(null != tdZwJcCrptSet.getFkByThickTypeId()){
                    tdZwHethJcSubComm.setFkByThickTypeId(tdZwJcCrptSet.getFkByThickTypeId());
                    tdZwHethJcSubComm.setThickTypeId(tdZwJcCrptSet.getFkByThickTypeId().getRid());
                }
                tdZwHethJcSubComm.setHgFlag(tdZwJcCrptSet.getHgFlag());
                chkSmary.getJcSubList().add(tdZwHethJcSubComm);
            }
            //其中检测单位、检测人取配置表中的第一条
            chkSmary.setJcUnitName(tdZwJcCrptSetList.get(0).getJcUnitName());
            chkSmary.setJcUnitCharge(tdZwJcCrptSetList.get(0).getJcUnitCharge());
        }
    }

    /**
   *  <p>方法描述：初始化危害因素</p>
   * @MethodAuthor hsj
   */
    public void selectBadtree() {
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("width", 680);
        options.put("contentWidth", 625);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add("危害因素");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add("5007");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        paramList.add("false");
        paramMap.put("ifAllSelect", paramList);
        paramList = new ArrayList<String>();
        paramList.add("true");
        paramMap.put("ifShowFirstCode", paramList);
        paramList = new ArrayList<String>();
        paramList.add("true");
        paramMap.put("ifSelf", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectBadRsnComm", options,
                paramMap);
    }

    /**
     *  <p>方法描述：选择危害因素</p>
     * @MethodAuthor hsj
     */
    public void onBadtreeSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            TsSimpleCode selectPro = (TsSimpleCode) selectedMap
                    .get("selectPro");
            if (null != selectPro) {
                TdZwHethJcSubComm sub = chkSmary.getJcSubList().get(badrsnIndex);
                if (null != sub) {
                    sub.setFkByBadrsnId(selectPro);
                }
            }
        }
    }
    
    /**
 	 * <p>方法描述：处理最新流程状态</p>
 	 * @MethodAuthor qrr,2021年10月26日,dealBgkLastSta
     * */
    private void delBgkLastSta() {
    	TsZone zone = chkSmary.getFkByEmpZoneId();
    	if ("1".equals(zone.getIfCityDirect())) {//市直属
            this.tdZwBgkLastSta.setCityRcvDate(new Date());
            this.tdZwBgkLastSta.setState(3);
        } else if ("1".equals(zone.getIfProvDirect())){//省直属
        	this.tdZwBgkLastSta.setProRcvDate(new Date());
            this.tdZwBgkLastSta.setState(5);
        }else {
            this.tdZwBgkLastSta.setCountyRcvDate(new Date());
	        this.tdZwBgkLastSta.setState(1);
		}
	}
    
    /**
    * <p>Description：撤销 </p>
    * <p>Author： yzz 2024-05-29 </p>
    */
    public void cancelAction(){
        try{
            String operFlag;
            TsZone zone = chkSmary.getFkByEmpZoneId();
            if ("1".equals(zone.getIfCityDirect())) {//市直属
                operFlag="33";
            } else if ("1".equals(zone.getIfProvDirect())){//省直属
                operFlag="44";
            } else {
                operFlag="21";
            }
            chkSmaryService.cancelChkSmary(this.rid, operFlag);
            JsfUtil.addSuccessMessage("撤销成功！");
            this.modInitAction();
            this.searchAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public Date getSearchRcvBdate() {
        return searchRcvBdate;
    }

    public void setSearchRcvBdate(Date searchRcvBdate) {
        this.searchRcvBdate = searchRcvBdate;
    }

    public Date getSearchRcvEdate() {
        return searchRcvEdate;
    }

    public void setSearchRcvEdate(Date searchRcvEdate) {
        this.searchRcvEdate = searchRcvEdate;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }


    public String getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(String checkLevel) {
        this.checkLevel = checkLevel;
    }

    public Date getNowDate() {
        return nowDate;
    }

    public void setNowDate(Date nowDate) {
        this.nowDate = nowDate;
    }


    public boolean isIfShowDesign() {
        return ifShowDesign;
    }

    public void setIfShowDesign(boolean ifShowDesign) {
        this.ifShowDesign = ifShowDesign;
    }

    public TdZwTjorginfoNew getTjorginfo() {
        return tjorginfo;
    }

    public void setTjorginfo(TdZwTjorginfoNew tjorginfo) {
        this.tjorginfo = tjorginfo;
    }

    public String getCrpyType() {
        return crpyType;
    }

    public void setCrpyType(String crpyType) {
        this.crpyType = crpyType;
    }

    public Boolean getSelEmpCrpt() {
        return selEmpCrpt;
    }

    public void setSelEmpCrpt(Boolean selEmpCrpt) {
        this.selEmpCrpt = selEmpCrpt;
    }

    @Override
    public FastReportBean getFastReportBean() {
        return fastReportBean;
    }
    @SuppressWarnings("rawtypes")
    @Override
    public List<FastReportData> supportFastReportDataSet() {
        return packageDatas();
    }
    @Override
    public List<FastReportDataRef> supportFastReportDataRef() {
        return null;
    }

    public TbZwWritsort getWritsort() {
        return writsort;
    }

    public void setWritsort(TbZwWritsort writsort) {
        this.writsort = writsort;
    }


    public void setFastReportBean(FastReportBean fastReportBean) {
        this.fastReportBean = fastReportBean;
    }

    public Integer getBadrsnIndex() {
        return badrsnIndex;
    }

    public void setBadrsnIndex(Integer badrsnIndex) {
        this.badrsnIndex = badrsnIndex;
    }


    public List<TsSimpleCode> getFkByThickTypeList() {
        return fkByThickTypeList;
    }

    public void setFkByThickTypeList(List<TsSimpleCode> fkByThickTypeList) {
        this.fkByThickTypeList = fkByThickTypeList;
    }


    public Map<Integer, TsSimpleCode> getFkByThickTypeMap() {
        return fkByThickTypeMap;
    }

    public void setFkByThickTypeMap(Map<Integer, TsSimpleCode> fkByThickTypeMap) {
        this.fkByThickTypeMap = fkByThickTypeMap;
    }


	public TdZwBgkLastSta getTdZwBgkLastSta() {
		return tdZwBgkLastSta;
	}


	public void setTdZwBgkLastSta(TdZwBgkLastSta tdZwBgkLastSta) {
		this.tdZwBgkLastSta = tdZwBgkLastSta;
	}

    public String getSearchRptNo() {
        return searchRptNo;
    }

    public void setSearchRptNo(String searchRptNo) {
        this.searchRptNo = searchRptNo;
    }

    public boolean getIfShowCancel() {
        return ifShowCancel;
    }

    public void setIfShowCancel(boolean ifShowCancel) {
        this.ifShowCancel = ifShowCancel;
    }
}
