package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2024-8-8
 */
@Entity
@Table(name = "TD_ZW_MONTH_BHK")
@SequenceGenerator(name = "TdZwMonthBhk", sequenceName = "TD_ZW_MONTH_BHK_SEQ", allocationSize = 1)
public class TdZwMonthBhk implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TbTjSrvorg fkByOrgId;
    private Date startBhkDate;
    private Date endBhkDate;
    private Integer crptNum;
    private Integer crptSubmitNum;
    private Date fillDate;
    private Integer state;
    private Integer delMark;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    /**
     * 添加: 年
     */
    private Integer selYear;
    /**
     * 添加: 月
     */
    private Integer selMonth;

    public TdZwMonthBhk() {
    }

    public TdZwMonthBhk(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwMonthBhk")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "ORG_ID")
    public TbTjSrvorg getFkByOrgId() {
        return fkByOrgId;
    }

    public void setFkByOrgId(TbTjSrvorg fkByOrgId) {
        this.fkByOrgId = fkByOrgId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "START_BHK_DATE")
    public Date getStartBhkDate() {
        return startBhkDate;
    }

    public void setStartBhkDate(Date startBhkDate) {
        this.startBhkDate = startBhkDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "END_BHK_DATE")
    public Date getEndBhkDate() {
        return endBhkDate;
    }

    public void setEndBhkDate(Date endBhkDate) {
        this.endBhkDate = endBhkDate;
    }

    @Column(name = "CRPT_NUM")
    public Integer getCrptNum() {
        return crptNum;
    }

    public void setCrptNum(Integer crptNum) {
        this.crptNum = crptNum;
    }

    @Column(name = "CRPT_SUBMIT_NUM")
    public Integer getCrptSubmitNum() {
        return crptSubmitNum;
    }

    public void setCrptSubmitNum(Integer crptSubmitNum) {
        this.crptSubmitNum = crptSubmitNum;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "FILL_DATE")
    public Date getFillDate() {
        return fillDate;
    }

    public void setFillDate(Date fillDate) {
        this.fillDate = fillDate;
    }

    @Column(name = "STATE")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Column(name = "DEL_MARK")
    public Integer getDelMark() {
        return delMark;
    }

    public void setDelMark(Integer delMark) {
        this.delMark = delMark;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Transient
    public Integer getSelYear() {
        return selYear;
    }

    public void setSelYear(Integer selYear) {
        this.selYear = selYear;
    }

    @Transient
    public Integer getSelMonth() {
        return selMonth;
    }

    public void setSelMonth(Integer selMonth) {
        this.selMonth = selMonth;
    }
}