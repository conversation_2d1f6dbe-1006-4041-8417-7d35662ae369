package com.chis.modules.heth.comm.logic;

import java.util.List;

import com.chis.modules.heth.comm.entity.TdTjBhksubClt;

/**
 * @Description : 项目分类
 * @ClassAuthor : anjing
 * @Date : 2019/5/20 15:21
 **/
public class TbTjItemSort {

    private Integer rid;
    private String codeName;
    private String extendS1;
    private List<TdTjBhksubClt> bhksubCltList;
    /**是否显示结果判定列*/
    private boolean ifShowRstFlag;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getCodeName() {
        return codeName;
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    public List<TdTjBhksubClt> getBhksubCltList() {
        return bhksubCltList;
    }

    public void setBhksubCltList(List<TdTjBhksubClt> bhksubCltList) {
        this.bhksubCltList = bhksubCltList;
    }

	public String getExtendS1() {
		return extendS1;
	}

	public void setExtendS1(String extendS1) {
		this.extendS1 = extendS1;
	}

    public boolean isIfShowRstFlag() {
        return ifShowRstFlag;
    }

    public void setIfShowRstFlag(boolean ifShowRstFlag) {
        this.ifShowRstFlag = ifShowRstFlag;
    }
}
