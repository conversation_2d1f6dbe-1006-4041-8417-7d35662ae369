package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2019-9-4
 */
@Entity
@Table(name = "TD_ZW_HETH_JC_SUB")
@SequenceGenerator(name = "TdZwHethJcSub", sequenceName = "TD_ZW_HETH_JC_SUB_SEQ", allocationSize = 1)
public class TdZwHethJcSubComm implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwHethChkSmaryComm fkByMainId;
	private TsSimpleCode fkByBadrsnId;
	private String workPlace;
	private String workType;
	private String thickType;
	private String thickRange;
	private Date jcTime;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	/**+检测值（最大值）20210803*/
	private String jcValMax;
	/**+检测值（最小值）20210803*/
	private String jcValMin;
	/**+浓度（强度）类型ID20210803*/
	private TsSimpleCode fkByThickTypeId;
	/**+合格情况20210803*/
	private Integer hgFlag;

	private Integer thickTypeId;

	public TdZwHethJcSubComm() {
	}

	public TdZwHethJcSubComm(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwHethJcSub")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwHethChkSmaryComm getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwHethChkSmaryComm fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BADRSN_ID")			
	public TsSimpleCode getFkByBadrsnId() {
		return fkByBadrsnId;
	}

	public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
		this.fkByBadrsnId = fkByBadrsnId;
	}	
			
	@Column(name = "WORK_PLACE")	
	public String getWorkPlace() {
		return workPlace;
	}

	public void setWorkPlace(String workPlace) {
		this.workPlace = workPlace;
	}	
			
	@Column(name = "WORK_TYPE")	
	public String getWorkType() {
		return workType;
	}

	public void setWorkType(String workType) {
		this.workType = workType;
	}	
			
	@Column(name = "THICK_TYPE")	
	public String getThickType() {
		return thickType;
	}

	public void setThickType(String thickType) {
		this.thickType = thickType;
	}	
			
	@Column(name = "THICK_RANGE")	
	public String getThickRange() {
		return thickRange;
	}

	public void setThickRange(String thickRange) {
		this.thickRange = thickRange;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "JC_TIME")			
	public Date getJcTime() {
		return jcTime;
	}

	public void setJcTime(Date jcTime) {
		this.jcTime = jcTime;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	@Column(name = "JC_VAL_MAX")
	public String getJcValMax() {
		return jcValMax;
	}

	public void setJcValMax(String jcValMax) {
		this.jcValMax = jcValMax;
	}
	@Column(name = "JC_VAL_MIN")
	public String getJcValMin() {
		return jcValMin;
	}

	public void setJcValMin(String jcValMin) {
		this.jcValMin = jcValMin;
	}
	@ManyToOne
	@JoinColumn(name = "THICK_TYPE_ID")
	public TsSimpleCode getFkByThickTypeId() {
		return fkByThickTypeId;
	}

	public void setFkByThickTypeId(TsSimpleCode fkByThickTypeId) {
		this.fkByThickTypeId = fkByThickTypeId;
	}
	@Column(name = "HG_FLAG")
	public Integer getHgFlag() {
		return hgFlag;
	}

	public void setHgFlag(Integer hgFlag) {
		this.hgFlag = hgFlag;
	}

	@Transient
	public Integer getThickTypeId() {
		return thickTypeId;
	}

	public void setThickTypeId(Integer thickTypeId) {
		this.thickTypeId = thickTypeId;
	}
}