package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbTjItems;
import com.chis.modules.heth.comm.entity.TbZwtjItemcmbItems;
import com.chis.modules.heth.comm.service.HethBaseCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 *  <p>类描述：项目组合与项目关系维护</p>
 * @ClassAuthor hsj 2022/3/4 10:52
 */
@ManagedBean(name = "tbZwtjItemCommBean")
@ViewScoped
public class TbZwtjItemCommBean extends FacesBean {
	/**
	 * 存在session中的对象
	 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/**
	 * ejb session bean
	 */
	private HethBaseCommServiceImpl hethBaseService = (HethBaseCommServiceImpl) SpringContextHolder
			.getBean(HethBaseCommServiceImpl.class);
	/**
	 * 公用会话Bean服务
	 */
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	/** 名称/拼音码 */
	private String searchName;
	/** 是否启用 */
	private Boolean searchIfReveal;
	/** 选择的项目组合 */
	private TsSimpleCode selectCode;
	/** 项目组合list */
	private List<TsSimpleCode> searchItemsList;
	/** 显示的项目组合list */
	private List<TsSimpleCode> showItemsList;
	/** 项目组合名称 */
	private String itemsName;
	/** 添加初始化的体检项目list */
	private List<TbTjItems> addTjItemsList;
	/** 显示的体检项目list */
	private List<TbTjItems> showTjItemsList;
	/** 保存选择的体检项目list */
	private List<TbTjItems> saveTjItemsList;
	/** 页面显示的项目项目list */
	private List<TbZwtjItemcmbItems> showZwtjItemsList;
	/** 状态 */
	private String state;
	/** 项目关系rid */
	private Integer rid;
	/** 选择的体检项目 */
	private TbTjItems selectItems;
	/** 编辑页面的拼音码 */
	private String editItemsName;
	/**已选择的判定方式*/
	private Map<Integer,Integer> itemsDeterWayMap;

	public TbZwtjItemCommBean() {
		init();
	}

	/**
	 * 选择的项目组合
	 * hsj
	 * @param event
	 *            选择事件
	 */
	public void onRowSelect(SelectEvent event) {
		// 选择的项目组合
		this.selectCode = (TsSimpleCode) event.getObject();
		// 项目组合名称
		this.itemsName = this.selectCode.getCodeName();
		// 根据项目组合，刷新项目组合关系列表
		searchZwtjItems();
	}

	/**
	 * 选择体检项目
	 * hsj
	 * <AUTHOR>
	 * @history 2014年9月10日
	 */
	public void selectItems() {
		if (null != this.selectItems) {
			// 将表中的数据删除
			this.showTjItemsList.remove(this.selectItems);
			// 添加到保存list里
			this.saveTjItemsList.add(this.selectItems);
		}
	}
	/**
	 *  <p>方法描述：根据项目组合，刷新项目组合关系列表</p>
	 * @MethodAuthor hsj 2022/3/4 10:09
	 */
	private void searchZwtjItems() {
		if (null != this.selectCode) {
			// 根据项目组合，刷新项目组合关系列表
			this.showZwtjItemsList = this.hethBaseService.initZwtijItemListById(this.selectCode);
			itemsDeterWayMap = new HashMap<>();
			if(!CollectionUtils.isEmpty(showZwtjItemsList)){
				for(TbZwtjItemcmbItems list: showZwtjItemsList){
					itemsDeterWayMap.put(list.getRid(),list.getDeterWay());
				}
			}
		}
	}

	/**
	 * 查询项目组合
	 * hsj
	 */
	public void searchAction() {
		this.saveTjItemsList = new LinkedList<TbTjItems>();
		this.addTjItemsList = new LinkedList<TbTjItems>();
		this.showZwtjItemsList = new LinkedList<TbZwtjItemcmbItems>();
		this.selectCode = null;
		this.itemsName = "";
		// 项目组合list
		this.showItemsList = new LinkedList<TsSimpleCode>();
		for (TsSimpleCode t : this.searchItemsList) {
			if (this.searchIfReveal) {// 选择显示停用
				if (StringUtils.isNotBlank(this.searchName)) {// 查询名称或拼音码
					// 找到匹配的拼音码或名称
					if ((StringUtils.isNotBlank(t.getCodeName()) && t.getCodeName().indexOf(this.searchName) != -1)
							|| (StringUtils.isNotBlank(t.getSplsht()) && t.getSplsht().toUpperCase()
									.indexOf(this.searchName.toUpperCase()) != -1)) {
						this.showItemsList.add(t);
					}
				} else {
					this.showItemsList.add(t);
				}
			} else {
				if (1 == t.getIfReveal()) {// 启用状态
					if (StringUtils.isNotBlank(this.searchName)) {// 查询名称或拼音码
						// 找到匹配的拼音码或名称
						if ((StringUtils.isNotBlank(t.getCodeName()) && t.getCodeName().indexOf(this.searchName) != -1)
								|| (StringUtils.isNotBlank(t.getSplsht()) && t.getSplsht().toUpperCase()
										.indexOf(this.searchName.toUpperCase()) != -1)) {
							this.showItemsList.add(t);
						}
					} else {
						this.showItemsList.add(t);
					}
				}
			}
		}
	}

	/**
	 * 添加初始化
	 * hsj
	 * <AUTHOR>
	 * @history 2014年9月10日
	 */
	public void addInitAction() {
		this.editItemsName = "";
		this.saveTjItemsList = new LinkedList<TbTjItems>();
		this.showTjItemsList = new LinkedList<TbTjItems>();
		if (null == this.selectCode) {
			JsfUtil.addErrorMessage("请选择项目组合！");
			return;
		} else {
			this.addTjItemsList = this.hethBaseService.initTjItemsList(this.selectCode.getRid());
			for (TbTjItems t : this.addTjItemsList) {
				this.showTjItemsList.add(t);
			}
			// 关闭弹出框
			RequestContext requestContext = RequestContext.getCurrentInstance();
			requestContext.execute("EditDialog.show()");
		}
	}

	/**
	 * 添加页面，查询体检项目
	 * hsj
	 * <AUTHOR>
	 * @history 2014年9月10日
	 */
	public void addSearchAction() {
		this.showTjItemsList = new LinkedList<TbTjItems>();
		// int i = 0;// 是否已添加
		Set<Integer> addSet = new HashSet<Integer>();
		if (null != saveTjItemsList) {
			for (TbTjItems t1 : this.saveTjItemsList) {
				addSet.add(t1.getRid());
			}
		}
		for (TbTjItems t : this.addTjItemsList) {
			// 遍历已选择的项目，如果存在，则不再显示在页面上
			if (addSet.contains(t.getRid())) {
				continue;
			}
			if (StringUtils.isNotBlank(this.editItemsName)) {// 查询名称或拼音码
				System.err.println("项目名称：" + t.getItemName());
				// 找到匹配的拼音码或名称
				if ((StringUtils.isNotBlank(t.getItemName()) && t.getItemName().indexOf(this.editItemsName) != -1)
						|| (StringUtils.isNotBlank(t.getPyxnam()) && t.getPyxnam().toUpperCase()
								.indexOf(this.editItemsName.toUpperCase()) != -1)) {

					// 不存在已选择list中，则显示页面上
					this.showTjItemsList.add(t);
				}
			} else {
				// 不存在已选择list中，则显示页面上
				this.showTjItemsList.add(t);
			}
		}
	}

	/**
	 * 保存
	 * hsj
	 * <AUTHOR>
	 * @history 2014年9月10日
	 */
	public void saveAction() {
		// 选择有体检项目
		if (null != this.saveTjItemsList) {
			this.hethBaseService.saveZwtjItem(this.saveTjItemsList, this.selectCode, sessionData.getUser().getRid());
			RequestContext requestContext = RequestContext.getCurrentInstance();
			requestContext.execute("EditDialog.hide()");
			// 根据项目组合，刷新项目组合关系列表
			searchZwtjItems();
		}
	}

	/**
	 * 停用、启用
	 * hsj
	 * <AUTHOR>
	 * @history 2014年9月10日
	 */
	public void startOrStopAction() {
		// 停用启用
		this.hethBaseService.startOfStopZwtjItemByRid(this.rid, this.state, sessionData.getUser().getRid());
		// 根据项目组合，刷新项目组合关系列表
		searchZwtjItems();
	}

	/**
	 * 删除
	 * hsj
	 * <AUTHOR>
	 * @history 2014年9月10日
	 */
	public void deleteAction() {
		this.hethBaseService.deleteZwtjItemByRid(this.rid);
		// 根据项目组合，刷新项目组合关系列表
		searchZwtjItems();
	}

	/**
	 * 页面初始化 hsj
	 */
	private void init() {
		this.state = null;
		this.rid = null;
		this.selectCode = null;
		this.searchName = "";
		this.searchIfReveal = false;
		this.searchItemsList = new LinkedList<TsSimpleCode>();
		this.showItemsList = new LinkedList<TsSimpleCode>();
		this.showZwtjItemsList = new LinkedList<TbZwtjItemcmbItems>();
		this.itemsName = "";
		this.searchItemsList = this.commService.findAllSimpleCodes("5012");
		// 遍历查询的项目组合，赋值到显示的list中
		for (TsSimpleCode t : this.searchItemsList) {
			if (1 == t.getIfReveal()) {
				this.showItemsList.add(t);
			}
		}
	}
	/**
	 *  <p>方法描述：判断方式的选择</p>
	 * @MethodAuthor hsj 2022/3/4 16:04
	 */
	public void updateDeterWay(TbZwtjItemcmbItems tbZwtjItemcmbItems){
		if(tbZwtjItemcmbItems != null && tbZwtjItemcmbItems.getRid() != null){
			itemsDeterWayMap.put(tbZwtjItemcmbItems.getRid(),tbZwtjItemcmbItems.getDeterWay());
		}
	}
	/**
	 *  <p>方法描述：保存已选择的判断方式</p>
	 * @MethodAuthor hsj 2022/3/4 16:29
	 */
	public void saveDeterWayAction(){
		if (null == this.selectCode) {
			JsfUtil.addErrorMessage("请选择项目组合！");
			return;
		}
		if(null == itemsDeterWayMap){
			JsfUtil.addSuccessMessage("保存成功！");
			return;
		}
		//key:判断方式；value:rids
		Map<Integer,List<Integer>> map = new HashMap<>();
		List<Integer> ways = new ArrayList<>();
		//遍历已操作的
		for(Integer rid : itemsDeterWayMap.keySet()){
			Integer way = itemsDeterWayMap.get(rid);
			//未选择
			if(null == way){
				way = 0;
			}else{
				//记录已选择的判断方式
				if(!ways.contains(way)){
					ways.add(way);
				}
			}
			if(!map.containsKey(way)){
				List<Integer> l= new ArrayList<>();
				l.add(rid);
				map.put(way,l);
			}else{
				map.get(way).add(rid);
			}
		}
		//已选择的记录方式有两种
		if(!CollectionUtils.isEmpty(ways) && ways.size() > 1){
			JsfUtil.addErrorMessage("只能选择一种判定方式！");
			return;
		}
		//保存判断方式
		for(Integer way : map.keySet()){
			List<Integer> rids = map.get(way);
			if(way == 0){
				way = null;
			}
			this.hethBaseService.updateDeterWay(way,rids, sessionData.getUser().getRid());
		}
		JsfUtil.addSuccessMessage("保存成功！");
		// 根据项目组合，刷新项目组合关系列表
		searchZwtjItems();
	}
	public String getSearchName() {
		return searchName;
	}

	public void setSearchName(String searchName) {
		this.searchName = searchName;
	}

	public Boolean getSearchIfReveal() {
		return searchIfReveal;
	}

	public void setSearchIfReveal(Boolean searchIfReveal) {
		this.searchIfReveal = searchIfReveal;
	}

	public List<TsSimpleCode> getSearchItemsList() {
		return searchItemsList;
	}

	public void setSearchItemsList(List<TsSimpleCode> searchItemsList) {
		this.searchItemsList = searchItemsList;
	}

	public String getItemsName() {
		return itemsName;
	}

	public void setItemsName(String itemsName) {
		this.itemsName = itemsName;
	}

	public TsSimpleCode getSelectCode() {
		return selectCode;
	}

	public void setSelectCode(TsSimpleCode selectCode) {
		this.selectCode = selectCode;
	}

	public List<TsSimpleCode> getShowItemsList() {
		return showItemsList;
	}

	public void setShowItemsList(List<TsSimpleCode> showItemsList) {
		this.showItemsList = showItemsList;
	}

	public List<TbTjItems> getAddTjItemsList() {
		return addTjItemsList;
	}

	public void setAddTjItemsList(List<TbTjItems> addTjItemsList) {
		this.addTjItemsList = addTjItemsList;
	}

	public List<TbTjItems> getSaveTjItemsList() {
		return saveTjItemsList;
	}

	public void setSaveTjItemsList(List<TbTjItems> saveTjItemsList) {
		this.saveTjItemsList = saveTjItemsList;
	}

	public List<TbZwtjItemcmbItems> getShowZwtjItemsList() {
		return showZwtjItemsList;
	}

	public void setShowZwtjItemsList(List<TbZwtjItemcmbItems> showZwtjItemsList) {
		this.showZwtjItemsList = showZwtjItemsList;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public TbTjItems getSelectItems() {
		return selectItems;
	}

	public void setSelectItems(TbTjItems selectItems) {
		this.selectItems = selectItems;
	}

	public String getEditItemsName() {
		return editItemsName;
	}

	public void setEditItemsName(String editItemsName) {
		this.editItemsName = editItemsName;
	}

	public List<TbTjItems> getShowTjItemsList() {
		return showTjItemsList;
	}

	public void setShowTjItemsList(List<TbTjItems> showTjItemsList) {
		this.showTjItemsList = showTjItemsList;
	}

}
