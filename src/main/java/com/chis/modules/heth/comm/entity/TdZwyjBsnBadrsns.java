package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-10-29
 */
@Entity
@Table(name = "TD_ZWYJ_BSN_BADRSNS")
@SequenceGenerator(name = "TdZwyjBsnBadrsns", sequenceName = "TD_ZWYJ_BSN_BADRSNS_SEQ", allocationSize = 1)
public class TdZwyjBsnBadrsns implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwyjBsnBhk fkByBhkId;
	private TsSimpleCode fkByBadrsnId;
	private TsSimpleCode fkByExamConclusionId;
	private String qtjbName;
	private Date createDate;
	private Integer createManid;
	
	public TdZwyjBsnBadrsns() {
	}

	public TdZwyjBsnBadrsns(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjBsnBadrsns")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "BHK_ID")			
	public TdZwyjBsnBhk getFkByBhkId() {
		return fkByBhkId;
	}

	public void setFkByBhkId(TdZwyjBsnBhk fkByBhkId) {
		this.fkByBhkId = fkByBhkId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BADRSN_ID")			
	public TsSimpleCode getFkByBadrsnId() {
		return fkByBadrsnId;
	}

	public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
		this.fkByBadrsnId = fkByBadrsnId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EXAM_CONCLUSION_ID")			
	public TsSimpleCode getFkByExamConclusionId() {
		return fkByExamConclusionId;
	}

	public void setFkByExamConclusionId(TsSimpleCode fkByExamConclusionId) {
		this.fkByExamConclusionId = fkByExamConclusionId;
	}	
			
	@Column(name = "QTJB_NAME")	
	public String getQtjbName() {
		return qtjbName;
	}

	public void setQtjbName(String qtjbName) {
		this.qtjbName = qtjbName;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
}