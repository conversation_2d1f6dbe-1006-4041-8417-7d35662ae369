package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-6
 */
@Entity
@Table(name = "TD_ZY_UNITMAINPROD")
@SequenceGenerator(name = "TdZyUnitmainprod", sequenceName = "TD_ZY_UNITMAINPROD_SEQ", allocationSize = 1)
public class TdZyUnitmainprod implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZyUnitbasicinfo fkByMainId;
	private String prodName;
	private BigDecimal annualOutput;
	private String unitName;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZyUnitmainprod() {
	}

	public TdZyUnitmainprod(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZyUnitmainprod")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZyUnitbasicinfo getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZyUnitbasicinfo fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "PROD_NAME")	
	public String getProdName() {
		return prodName;
	}

	public void setProdName(String prodName) {
		this.prodName = prodName;
	}	
			
	@Column(name = "ANNUAL_OUTPUT")	
	public BigDecimal getAnnualOutput() {
		return annualOutput;
	}

	public void setAnnualOutput(BigDecimal annualOutput) {
		this.annualOutput = annualOutput;
	}	
			
	@Column(name = "UNIT_NAME")	
	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}