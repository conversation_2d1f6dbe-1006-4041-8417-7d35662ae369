package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2022-9-6
 */
@Entity
@Table(name = "TD_ZY_UNITBASICINFO")
@SequenceGenerator(name = "TdZyUnitbasicinfo", sequenceName = "TD_ZY_UNITBASICINFO_SEQ", allocationSize = 1)
public class TdZyUnitbasicinfo implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private String uuid;
    private String recordUuid;
    private Integer declareYear;
    private Integer declareType;
    private Integer declareStatus;
    private Date declareDate;
    private Date approveDate;
    private TsSimpleCode fkByReasonId;
    private String remark;
    private TsZone fkByZoneId;
    private String unitName;
    private String creditCode;
    private String regAddr;
    private String workAddr;
    private TsSimpleCode fkByEnterpriseScaleId;
    private TsSimpleCode fkByIndustryCateId;
    private TsSimpleCode fkByEconomicId;
    private String fillMan;
    private String fillPhone;
    private String legalPerson;
    private String legalPersonPhone;
    private String linkManager;
    private String linkPhone;
    private Integer empNum;
    private Integer externalNum;
    private Integer victimsNum;
    private Integer occupationalDiseasesNum;
    private Integer ifBranch;
    private String parentUnitUuid;
    private Integer ifWarProduct;
    private Integer operationStatus;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private TbTjCrpt fkByCrpt;
    /**
     * 培训情况
     */
    private TdZyTrainSituation trainSituation;
    /**
     * 主要产品
     */
    private String proStr;
    /**
     * 检查机构
     */
    private String hethOrgStr;
    /**
     * 检测机构
     */
    private String jcOrgStr;
    /**
     * 职业病危害因素种类及接触人数
     */
    private TdZyUnitfactorcrowd unitfactorcrowd;
    /**
     * 职业健康监护开展情况
     */
    private TdZyUnithealthcustody unithealthcustody;
    /**
     * 职业病危害因素检测情况
     */
    private TdZyUnitharmfactorcheck unitharmfactorcheck;
    private List<List<List<String>>> detailList;

    public TdZyUnitbasicinfo() {
    }

    public TdZyUnitbasicinfo(Integer rid) {
        this.rid = rid;
    }

    public TdZyUnitbasicinfo(Integer rid, Date declareDate) {
        this.rid = rid;
        this.declareDate = declareDate;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZyUnitbasicinfo")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Column(name = "UUID")
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Column(name = "RECORD_UUID")
    public String getRecordUuid() {
        return recordUuid;
    }

    public void setRecordUuid(String recordUuid) {
        this.recordUuid = recordUuid;
    }

    @Column(name = "DECLARE_YEAR")
    public Integer getDeclareYear() {
        return declareYear;
    }

    public void setDeclareYear(Integer declareYear) {
        this.declareYear = declareYear;
    }

    @Column(name = "DECLARE_TYPE")
    public Integer getDeclareType() {
        return declareType;
    }

    public void setDeclareType(Integer declareType) {
        this.declareType = declareType;
    }

    @Column(name = "DECLARE_STATUS")
    public Integer getDeclareStatus() {
        return declareStatus;
    }

    public void setDeclareStatus(Integer declareStatus) {
        this.declareStatus = declareStatus;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "DECLARE_DATE")
    public Date getDeclareDate() {
        return declareDate;
    }

    public void setDeclareDate(Date declareDate) {
        this.declareDate = declareDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "APPROVE_DATE")
    public Date getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(Date approveDate) {
        this.approveDate = approveDate;
    }

    @ManyToOne
    @JoinColumn(name = "REASON_ID")
    public TsSimpleCode getFkByReasonId() {
        return fkByReasonId;
    }

    public void setFkByReasonId(TsSimpleCode fkByReasonId) {
        this.fkByReasonId = fkByReasonId;
    }

    @Column(name = "REMARK")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @ManyToOne
    @JoinColumn(name = "ZONE_ID")
    public TsZone getFkByZoneId() {
        return fkByZoneId;
    }

    public void setFkByZoneId(TsZone fkByZoneId) {
        this.fkByZoneId = fkByZoneId;
    }

    @Column(name = "UNIT_NAME")
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Column(name = "CREDIT_CODE")
    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    @Column(name = "REG_ADDR")
    public String getRegAddr() {
        return regAddr;
    }

    public void setRegAddr(String regAddr) {
        this.regAddr = regAddr;
    }

    @Column(name = "WORK_ADDR")
    public String getWorkAddr() {
        return workAddr;
    }

    public void setWorkAddr(String workAddr) {
        this.workAddr = workAddr;
    }

    @ManyToOne
    @JoinColumn(name = "ENTERPRISE_SCALE_ID")
    public TsSimpleCode getFkByEnterpriseScaleId() {
        return fkByEnterpriseScaleId;
    }

    public void setFkByEnterpriseScaleId(TsSimpleCode fkByEnterpriseScaleId) {
        this.fkByEnterpriseScaleId = fkByEnterpriseScaleId;
    }

    @ManyToOne
    @JoinColumn(name = "INDUSTRY_CATE_ID")
    public TsSimpleCode getFkByIndustryCateId() {
        return fkByIndustryCateId;
    }

    public void setFkByIndustryCateId(TsSimpleCode fkByIndustryCateId) {
        this.fkByIndustryCateId = fkByIndustryCateId;
    }

    @ManyToOne
    @JoinColumn(name = "ECONOMIC_ID")
    public TsSimpleCode getFkByEconomicId() {
        return fkByEconomicId;
    }

    public void setFkByEconomicId(TsSimpleCode fkByEconomicId) {
        this.fkByEconomicId = fkByEconomicId;
    }

    @Column(name = "FILL_MAN")
    public String getFillMan() {
        return fillMan;
    }

    public void setFillMan(String fillMan) {
        this.fillMan = fillMan;
    }

    @Column(name = "FILL_PHONE")
    public String getFillPhone() {
        return fillPhone;
    }

    public void setFillPhone(String fillPhone) {
        this.fillPhone = fillPhone;
    }

    @Column(name = "LEGAL_PERSON")
    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    @Column(name = "LEGAL_PERSON_PHONE")
    public String getLegalPersonPhone() {
        return legalPersonPhone;
    }

    public void setLegalPersonPhone(String legalPersonPhone) {
        this.legalPersonPhone = legalPersonPhone;
    }

    @Column(name = "LINK_MANAGER")
    public String getLinkManager() {
        return linkManager;
    }

    public void setLinkManager(String linkManager) {
        this.linkManager = linkManager;
    }

    @Column(name = "LINK_PHONE")
    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }

    @Column(name = "EMP_NUM")
    public Integer getEmpNum() {
        return empNum;
    }

    public void setEmpNum(Integer empNum) {
        this.empNum = empNum;
    }

    @Column(name = "EXTERNAL_NUM")
    public Integer getExternalNum() {
        return externalNum;
    }

    public void setExternalNum(Integer externalNum) {
        this.externalNum = externalNum;
    }

    @Column(name = "VICTIMS_NUM")
    public Integer getVictimsNum() {
        return victimsNum;
    }

    public void setVictimsNum(Integer victimsNum) {
        this.victimsNum = victimsNum;
    }

    @Column(name = "OCCUPATIONAL_DISEASES_NUM")
    public Integer getOccupationalDiseasesNum() {
        return occupationalDiseasesNum;
    }

    public void setOccupationalDiseasesNum(Integer occupationalDiseasesNum) {
        this.occupationalDiseasesNum = occupationalDiseasesNum;
    }

    @Column(name = "IF_BRANCH")
    public Integer getIfBranch() {
        return ifBranch;
    }

    public void setIfBranch(Integer ifBranch) {
        this.ifBranch = ifBranch;
    }

    @Column(name = "PARENT_UNIT_UUID")
    public String getParentUnitUuid() {
        return parentUnitUuid;
    }

    public void setParentUnitUuid(String parentUnitUuid) {
        this.parentUnitUuid = parentUnitUuid;
    }

    @Column(name = "IF_WAR_PRODUCT")
    public Integer getIfWarProduct() {
        return ifWarProduct;
    }

    public void setIfWarProduct(Integer ifWarProduct) {
        this.ifWarProduct = ifWarProduct;
    }

    @Column(name = "OPERATION_STATUS")
    public Integer getOperationStatus() {
        return operationStatus;
    }

    public void setOperationStatus(Integer operationStatus) {
        this.operationStatus = operationStatus;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_ID")
    public TbTjCrpt getFkByCrpt() {
        return fkByCrpt;
    }

    public void setFkByCrpt(TbTjCrpt fkByCrpt) {
        this.fkByCrpt = fkByCrpt;
    }

    @Transient
    public TdZyTrainSituation getTrainSituation() {
        return trainSituation;
    }

    public void setTrainSituation(TdZyTrainSituation trainSituation) {
        this.trainSituation = trainSituation;
    }

    @Transient
    public String getProStr() {
        return proStr;
    }

    public void setProStr(String proStr) {
        this.proStr = proStr;
    }

    @Transient
    public String getHethOrgStr() {
        return hethOrgStr;
    }

    public void setHethOrgStr(String hethOrgStr) {
        this.hethOrgStr = hethOrgStr;
    }

    @Transient
    public String getJcOrgStr() {
        return jcOrgStr;
    }

    public void setJcOrgStr(String jcOrgStr) {
        this.jcOrgStr = jcOrgStr;
    }

    @Transient
    public TdZyUnitfactorcrowd getUnitfactorcrowd() {
        return unitfactorcrowd;
    }

    public void setUnitfactorcrowd(TdZyUnitfactorcrowd unitfactorcrowd) {
        this.unitfactorcrowd = unitfactorcrowd;
    }

    @Transient
    public TdZyUnithealthcustody getUnithealthcustody() {
        return unithealthcustody;
    }

    public void setUnithealthcustody(TdZyUnithealthcustody unithealthcustody) {
        this.unithealthcustody = unithealthcustody;
    }

    @Transient
    public TdZyUnitharmfactorcheck getUnitharmfactorcheck() {
        return unitharmfactorcheck;
    }

    public void setUnitharmfactorcheck(TdZyUnitharmfactorcheck unitharmfactorcheck) {
        this.unitharmfactorcheck = unitharmfactorcheck;
    }

    @Transient
    public List<List<List<String>>> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<List<List<String>>> detailList) {
        this.detailList = detailList;
    }
}