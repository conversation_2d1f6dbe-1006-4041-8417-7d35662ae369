package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSONObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.json.UnitDialogParamJson;
import com.chis.modules.heth.comm.service.TbTjCrptIndependCommServiceImpl;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.DefaultLazyDataModel;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesBean;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * <AUTHOR>
 * @description:
 */

@ManagedBean(name = "unitCommSelectListBean")
@ViewScoped
public class UnitCommSelectListBean extends FacesBean {
    private static final long serialVersionUID = 1L;
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private TbTjCrptIndependCommServiceImpl service = SpringContextHolder.getBean(TbTjCrptIndependCommServiceImpl.class);
    private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);

    /**
     * 表格数据
     */
    private DefaultLazyDataModel<TsUnit> unitCommList;
    private final String TABLE_ID = "selectForm:unitCommTable";
    /**
     * 查询条件：地区
     */
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
    private Integer searchZoneId;

    private List<TsZone> manageZoneList;
    /**
     * 查询条件：单位名称
     */
    private String searchUnitName;
    /**
     * 查询条件：社会信用代码
     */
    private String searchCreditCode;
    private TsUnit selectUnitComm;

    /**
     * 修改页面：单位
     */
    private TsUnit editUnitComm;
    private Integer unitCommId;

    /**
     * 添加/修改页面：行政区划所属地区
     */
    private List<TsZone> editZoneList;
    private Integer editZoneId;
    private String editZoneName;
    private String editZoneCode;
    /**
     * 添加/修改页面： /**添加/修改页面：行政区划所属地区
     */
    private Integer editManageZoneId;
    private String editManageZoneName;
    private String editManageZoneCode;

    /**
     * 1：职业健康
     */
    private String type;

    private Map<String, TsZone> zoneMap = new HashMap<String, TsZone>();
    private Map<String, TsZone> manageZoneMap = new HashMap<String, TsZone>();

    /**
     * 社会信用代码是否使用老规则校验
     */
    private boolean ifWeakVerify = Boolean.FALSE;

    private String message;

    /**
     * 是否分支机构
     */
    private boolean ifHasSubOrg;

    public UnitCommSelectListBean() {
        this.initReqParams();
        this.initZone();
        this.initUnitComm();
        this.searchAction();
        // 是否存在分支机构 IF_SUB_ORG
        this.ifHasSubOrg = "1".equals(commService.findParamValue("IF_SUB_ORG"));
    }

    /**
     * <p>描述 初始化请求参数</p>
     *
     * @return void
     * @MethodAuthor gongzhe, 2022/7/20 19:02,initReqParams
     */
    private void initReqParams() {
        String jsonParamStr = JsfUtil.getRequest().getParameter("jsonParam");
        UnitDialogParamJson json = JSONObject.parseObject(jsonParamStr, UnitDialogParamJson.class);
        this.type = json.getType();
    }

    /**
     * @Description : 地区初始化
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 15:16
     **/
    private void initZone() {
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.editZoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb(), true, "", "");
        String jsZone = Global.getUser().getTsUnit().getTsZone().getZoneGb().substring(0, 2) + "00000000";
        this.manageZoneList=this.commService.findZoneListByGbAndTypeNoNation(jsZone, true, "", "");

        this.zoneList = editZoneList;
        if (!CollectionUtils.isEmpty(this.editZoneList)) {
            for (TsZone zone : this.editZoneList) {
                if (null != zone.getZoneGb() && zone.getZoneGb().trim().equals(tsZone.getZoneGb())) {
                    this.searchZoneCode = tsZone.getZoneGb();
                    this.searchZoneName = zone.getZoneName();
                    this.searchZoneId = zone.getRid();
                }
                this.zoneMap.put(zone.getRid().toString(), zone);
            }
        }
        if(!CollectionUtils.isEmpty(this.manageZoneList)){
            for (TsZone zone : this.manageZoneList) {
                manageZoneMap.put(zone.getRid().toString(), zone);
            }
        }
    }

    /**
     * @Description : 初始化码表数据
     * @MethodAuthor: anjing
     * @Date : 2021/4/14 10:48
     **/
    private void initSimpleCode() {
        if (this.editUnitComm == null) {
            editUnitComm = new TsUnit();
            editUnitComm.setIfSubOrg("0");
        }
    }

    /**
     * <p>Description：初始化弹框中的行政区划所属地区、业务管辖地区 </p>
     * <p>Author： yzz 2025/4/10 </p>
     */
    private void initUnitComm() {
        this.initSimpleCode();
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.editZoneId = tsZone.getRid();
        this.editZoneName = tsZone.getZoneName();
        this.editZoneCode = tsZone.getZoneGb();

        this.editManageZoneId = tsZone.getRid();
        this.editManageZoneName = tsZone.getZoneName();
        this.editManageZoneCode = tsZone.getZoneGb();
    }


    /**
     * @Description : 执行查询
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 14:41
     **/
    public void searchAction() {
        Map<String, Object> paramMap = new HashMap<>();
        StringBuilder sb = new StringBuilder();
        StringBuilder sbCount = new StringBuilder();
        StringBuilder sql = new StringBuilder();
        sql.append(" FROM TsUnit T ");
        // 查询未标删的
        sql.append(" WHERE T.ifReveal = 1 ");
        // 行政区划所属地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sql.append(" AND T.tsZone.zoneGb LIKE '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%' ");
        }

        // 单位名称
        if (StringUtils.isNotBlank(this.searchUnitName)) {
            sql.append(" AND T.unitname LIKE :searchUnitName ");
            paramMap.put("searchUnitName", "%" + this.searchUnitName + "%");
        }
        // 社会信用代码
        if (StringUtils.isNotBlank(this.searchCreditCode)) {
            sql.append(" AND T.creditCode LIKE :searchCreditCode ");
            paramMap.put("searchCreditCode", "%" + this.searchCreditCode + "%");
        }
        sb.append("SELECT T ").append(sql).append(" ORDER BY T.tsZone.zoneGb,T.fkByManagedZoneId.zoneGb,T.unitCode ");
        sbCount.append("SELECT COUNT(T.rid) ").append(sql);
        String[] hql = new String[]{sb.toString(), sbCount.toString()};
        this.unitCommList = new DefaultLazyDataModel(hql[0], hql[1], paramMap, TABLE_ID);
    }


    /**
     * @Description : 添加企业信息—各单位独立信息
     * @MethodAuthor: anjing
     * @Date : 2021/4/14 10:34
     **/
    public void addUnitCommAction() {
        this.editUnitComm = null;
        initUnitComm();
        RequestContext.getCurrentInstance().update("selectForm:addUnitCommDialog");
        RequestContext.getCurrentInstance().execute("PF('addUnitComm').show();");
    }

    /**
     * <p>Description：保存单位信息验证 </p>
     * <p>Author： yzz 2025/4/9 </p>
     */
    private boolean veryTsUnitComm() {
        boolean flag = true;

        // 所属地区
        if (null == this.editZoneId) {
            JsfUtil.addErrorMessage("请选择行政区划所属地区！");
            flag = false;
        } else {
            if (null == this.zoneMap.get(this.editZoneId.toString())) {
                JsfUtil.addErrorMessage("行政区划所属地区已停用，请重新选择！");
                flag = false;
            }
            Short realZoneType = (null == this.editZoneId || null == this.zoneMap.get(this.editZoneId.toString())) ? null
                    : this.zoneMap.get(this.editZoneId.toString()).getRealZoneType();
            if (null == realZoneType || realZoneType.intValue() < 4) {
                JsfUtil.addErrorMessage("行政区划所属地区必须选择到区县及以下！");
                flag = false;
            }
        }
        // 管辖地区
        if (null == this.editManageZoneId) {
            JsfUtil.addErrorMessage("请选择业务管辖地区！");
            flag = false;
        } else {
            if (null == this.manageZoneMap.get(editManageZoneId.toString())) {
                JsfUtil.addErrorMessage("业务管辖地区已停用，请重新选择！");
                flag = false;
            }
        }
        // 企业名称
        if (StringUtils.isBlank(this.editUnitComm.getUnitname())) {
            JsfUtil.addErrorMessage("单位名称不能为空！");
            flag = false;
        }

        // 社会信用代码
        if (StringUtils.isBlank(this.editUnitComm.getCreditCode())) {
            JsfUtil.addErrorMessage("社会信用代码不能为空！");
            flag = false;
        } else if (!StringUtils.isCreditCode(this.editUnitComm.getCreditCode())) {
            JsfUtil.addErrorMessage("社会信用代码不符合规范！");
            flag = false;
        } else {
            if (ifHasSubOrg) {// 配置分支机构
                if (StringUtils.isBlank(this.editUnitComm.getIfSubOrg())) {
                    JsfUtil.addErrorMessage("是否分支机构不能为空！");
                    flag = false;
                } else {
                    if ("1".equals(this.editUnitComm.getIfSubOrg())) {// 是分支机构
                        // 主体机构必存在
                        Integer count = systemModuleService.findUnitIfExists(this.editUnitComm.getCreditCode(), this.editUnitComm.getRid(), null, "0");
                        if (null == count || count.intValue() == 0) {
                            JsfUtil.addErrorMessage("同社会信用代码的主体机构不存在！");
                            flag = false;
                        }
                        // 社会信用代码和单位名称联合唯一
                        if (StringUtils.isNotBlank(this.editUnitComm.getUnitname())) {
                            Integer subCount = systemModuleService.findUnitIfExists(this.editUnitComm.getCreditCode(), this.editUnitComm.getRid(), this.editUnitComm.getUnitname(), null);
                            if (null != subCount && subCount.intValue() > 0) {
                                JsfUtil.addErrorMessage("社会信用代码和单位名称已存在！");
                                flag = false;
                            }
                        }
                    } else {// 主体机构
                        // 社会信用代码唯一
                        Integer count = systemModuleService.findUnitIfExists(this.editUnitComm.getCreditCode(), this.editUnitComm.getRid(), null, "0");
                        if (null != count && count.intValue() > 0) {
                            JsfUtil.addErrorMessage("社会信用代码已存在！");
                            flag = false;
                        }
                    }
                }
            } else {
                // 社会信用代码唯一
                Integer count = systemModuleService.findUnitIfExists(this.editUnitComm.getCreditCode(), this.editUnitComm.getRid(), null, null);
                if (null != count && count.intValue() > 0) {
                    JsfUtil.addErrorMessage("社会信用代码已存在！");
                    flag = false;
                }
            }
        }
        // 单位简称
        if (StringUtils.isBlank(this.editUnitComm.getUnitSimpname())) {
            JsfUtil.addErrorMessage("单位简称不能为空！");
            flag = false;
        }
        // 单位地址
        if (StringUtils.isBlank(this.editUnitComm.getUnitaddr())) {
            JsfUtil.addErrorMessage("单位地址不能为空！");
            flag = false;
        }
        if (this.ifHasSubOrg && this.editUnitComm.getIfSubOrg() == null) {
            JsfUtil.addErrorMessage("是否分支机构不能为空！");
            flag = false;
        }

        // 联系电话
        if (StringUtils.isNotBlank(this.editUnitComm.getUnittel())) {
            if (!StringUtils.vertyPhone(this.editUnitComm.getUnittel())) {
                JsfUtil.addErrorMessage("联系电话格式不正确！");
                flag = false;
            }
        }

        // 联系人电话
        if (StringUtils.isNotBlank(this.editUnitComm.getOrgTel())) {
            if (!StringUtils.vertyPhone(this.editUnitComm.getOrgTel())) {
                JsfUtil.addErrorMessage("联系人电话格式不正确！");
                flag = false;
            }
        }
        return flag;
    }

    /**
     * <p>Description：选择单位信息 </p>
     * <p>Author： yzz 2025/4/9 </p>
     */
    public void selectUnitCommAction() {
        if (this.selectUnitComm == null) {
            return;
        }
        StringBuilder sql = new StringBuilder();
        // 资质查询职业健康检查机构
        if ("1".equals(this.type)) {
            sql.append(" select count(1) from TD_ZW_TJORGINFO where ORG_ID=").append(this.selectUnitComm.getRid());
            if (this.commService.findCountBySql(sql.toString()) > 0) {
                JsfUtil.addErrorMessage("职业健康检查机构已经存在！");
                return;
            }
        }

        Map<String, Object> map = new HashMap<>();
        map.put("selectUnit", this.selectUnitComm);
        RequestContext.getCurrentInstance().closeDialog(map);
    }

    /**
     * @Description : 修改企业信息—各单位独立信息
     * @MethodAuthor: anjing
     * @Date : 2021/4/14 11:05
     **/
    public void modUnitCommAction() {
        if (null == this.unitCommId) {
            return;
        }
        this.editUnitComm = this.service.find(TsUnit.class, unitCommId);
        if (this.editUnitComm != null) {
            // 地区赋值
            TsZone zone = this.editUnitComm.getTsZone();
            if (null != zone) {
                this.editZoneId = zone.getRid();
                this.editZoneName = zone.getZoneName();
                this.editZoneCode = zone.getZoneGb();
            } else {
                this.editZoneId = null;
                this.editZoneName = null;
                this.editZoneCode = null;
            }
            //管辖地区
            TsZone managedZone = this.editUnitComm.getFkByManagedZoneId();
            if(managedZone!=null){
                this.editManageZoneId = managedZone.getRid();
                this.editManageZoneName = managedZone.getZoneName();
                this.editManageZoneCode = managedZone.getZoneGb();
            }else{
                this.editManageZoneId = null;
                this.editManageZoneName = null;
                this.editManageZoneCode = null;
            }

        }
        RequestContext.getCurrentInstance().update("selectForm:addUnitCommDialog");
        RequestContext.getCurrentInstance().execute("PF('addUnitComm').show();");
    }

    /**
     * <p>Description：保存单位信息 </p>
     * <p>Author： yzz 2025/4/10 </p>
     */
    public void saveUnitCommAction() {
        try {
            if (null == this.editUnitComm) {
                return;
            }
            if (!veryTsUnitComm()) {
                return;
            }
            if (!ZoneUtil.zoneSelect(this.editManageZoneCode).contains(ZoneUtil.zoneSelect(this.editZoneCode)) && !ZoneUtil.zoneSelect(this.editZoneCode).contains(ZoneUtil.zoneSelect(this.editManageZoneCode))) {
                message = this.editZoneName + "不在" + this.editManageZoneName + "管辖范围内，确定保存吗？";
                RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show()");
                RequestContext.getCurrentInstance().update("selectForm:confirmDialog");
            } else {
                saveAction();
            }
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }


    /**
     * <p>Description：保存 </p>
     * <p>Author： yzz 2025/4/9 </p>
     */
    public void saveAction() {
        this.editUnitComm.setTsZone(new TsZone(this.editZoneId));
        this.editUnitComm.setFkByManagedZoneId(new TsZone(this.editManageZoneId));
        this.editUnitComm.setIfReveal(Short.valueOf("1"));
        this.commService.upsertEntity(this.editUnitComm);
        JsfUtil.addSuccessMessage("保存成功！");
        RequestContext.getCurrentInstance().execute("PF('addUnitComm').hide()");
        this.searchAction();
        RequestContext.getCurrentInstance().update("selectForm:unitCommTable");
    }


    /**
     * <p>描述 单位名称和社会信用代码失焦验证 重复性</p>
     *
     * @return void
     * @MethodAuthor gongzhe, 2022/7/21 11:31,checkCptNameAndCode
     */
    public void checkCptNameAndCode() {
        if(StringUtils.isBlank(this.editUnitComm.getCreditCode())){
            return;
        }
        if (!StringUtils.verifyCreditCode(ifWeakVerify, this.editUnitComm.getCreditCode())) {
            JsfUtil.addErrorMessage("社会信用代码不符合规范！");
        }
    }


    public List<TsZone> getZoneList() {
        return zoneList;
    }
    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }
    public String getSearchZoneCode() {
        return searchZoneCode;
    }
    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }
    public String getSearchZoneName() {
        return searchZoneName;
    }
    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }
    public List<TsZone> getEditZoneList() {
        return editZoneList;
    }
    public void setEditZoneList(List<TsZone> editZoneList) {
        this.editZoneList = editZoneList;
    }
    public Integer getEditZoneId() {
        return editZoneId;
    }
    public void setEditZoneId(Integer editZoneId) {
        this.editZoneId = editZoneId;
    }
    public String getEditZoneName() {
        return editZoneName;
    }
    public void setEditZoneName(String editZoneName) {
        this.editZoneName = editZoneName;
    }
    public Integer getSearchZoneId() {
        return searchZoneId;
    }
    public void setSearchZoneId(Integer searchZoneId) {
        this.searchZoneId = searchZoneId;
    }
    @Override
    public int getPageSize() {
        return pageSize;
    }
    @Override
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
    public Map<String, TsZone> getZoneMap() {
        return zoneMap;
    }
    public void setZoneMap(Map<String, TsZone> zoneMap) {
        this.zoneMap = zoneMap;
    }
    public boolean isIfWeakVerify() {
        return ifWeakVerify;
    }
    public void setIfWeakVerify(boolean ifWeakVerify) {
        this.ifWeakVerify = ifWeakVerify;
    }
    public String getSearchUnitName() {
        return searchUnitName;
    }
    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }
    public String getSearchCreditCode() {
        return searchCreditCode;
    }
    public void setSearchCreditCode(String searchCreditCode) {
        this.searchCreditCode = searchCreditCode;
    }
    public DefaultLazyDataModel<TsUnit> getUnitCommList() {
        return unitCommList;
    }
    public void setUnitCommList(DefaultLazyDataModel<TsUnit> unitCommList) {
        this.unitCommList = unitCommList;
    }
    public TsUnit getSelectUnitComm() {
        return selectUnitComm;
    }
    public void setSelectUnitComm(TsUnit selectUnitComm) {
        this.selectUnitComm = selectUnitComm;
    }
    public TsUnit getEditUnitComm() {
        return editUnitComm;
    }
    public void setEditUnitComm(TsUnit editUnitComm) {
        this.editUnitComm = editUnitComm;
    }
    public Integer getUnitCommId() {
        return unitCommId;
    }
    public void setUnitCommId(Integer unitCommId) {
        this.unitCommId = unitCommId;
    }
    public Integer getEditManageZoneId() {
        return editManageZoneId;
    }
    public void setEditManageZoneId(Integer editManageZoneId) {
        this.editManageZoneId = editManageZoneId;
    }
    public String getEditManageZoneName() {
        return editManageZoneName;
    }
    public void setEditManageZoneName(String editManageZoneName) {
        this.editManageZoneName = editManageZoneName;
    }
    public String getMessage() {
        return message;
    }
    public void setMessage(String message) {
        this.message = message;
    }
    public String getEditZoneCode() {
        return editZoneCode;
    }
    public void setEditZoneCode(String editZoneCode) {
        this.editZoneCode = editZoneCode;
    }
    public String getEditManageZoneCode() {
        return editManageZoneCode;
    }
    public void setEditManageZoneCode(String editManageZoneCode) {
        this.editManageZoneCode = editManageZoneCode;
    }
    public boolean getIfHasSubOrg() {
        return ifHasSubOrg;
    }
    public void setIfHasSubOrg(boolean ifHasSubOrg) {
        this.ifHasSubOrg = ifHasSubOrg;
    }

    public List<TsZone> getManageZoneList() {
        return manageZoneList;
    }
    public void setManageZoneList(List<TsZone> manageZoneList) {
        this.manageZoneList = manageZoneList;
    }
}
