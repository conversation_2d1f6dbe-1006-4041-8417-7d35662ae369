package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-16
 */
@Entity
@Table(name = "TB_TJ_ITEMS_GJ")
@SequenceGenerator(name = "TbTjItemsGj", sequenceName = "TB_TJ_ITEMS_GJ_SEQ", allocationSize = 1)
public class TbTjItemsGj implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbTjItems fkByItemId;
	private Integer type;
	private Integer sex;
	private TsSimpleCode fkByMsruntId;
	private BigDecimal minval;
	private BigDecimal maxval;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TbTjItemsGj() {
	}

	public TbTjItemsGj(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjItemsGj")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ITEM_ID")			
	public TbTjItems getFkByItemId() {
		return fkByItemId;
	}

	public void setFkByItemId(TbTjItems fkByItemId) {
		this.fkByItemId = fkByItemId;
	}	
			
	@Column(name = "TYPE")	
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}	
			
	@Column(name = "SEX")	
	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}	
			
	@ManyToOne
	@JoinColumn(name = "MSRUNT_ID")			
	public TsSimpleCode getFkByMsruntId() {
		return fkByMsruntId;
	}

	public void setFkByMsruntId(TsSimpleCode fkByMsruntId) {
		this.fkByMsruntId = fkByMsruntId;
	}	
			
	@Column(name = "MINVAL")	
	public BigDecimal getMinval() {
		return minval;
	}

	public void setMinval(BigDecimal minval) {
		this.minval = minval;
	}	
			
	@Column(name = "MAXVAL")	
	public BigDecimal getMaxval() {
		return maxval;
	}

	public void setMaxval(BigDecimal maxval) {
		this.maxval = maxval;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}