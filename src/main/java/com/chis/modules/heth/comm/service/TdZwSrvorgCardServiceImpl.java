package com.chis.modules.heth.comm.service;

import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.rptvo.SrvorginfoVo;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.Collator;
import java.util.*;

@Service
@Transactional(readOnly = true)
public class TdZwSrvorgCardServiceImpl extends AbstractTemplate {

    /**
     *  <p>方法描述：报告卡标删</p>
     * @MethodAuthor hsj 2022-08-19 14:37
     */
    public void deleteSrvorgCard(Integer rid) {
        if (null != rid) {
            StringBuilder sb = new StringBuilder();
            sb.append(" update  TD_ZW_SRVORG_CARD t set t.DEL_MARK = 1");
            sb.append(" where t.rid = ").append(rid);
            this.executeSql(sb.toString(),null);
        }
    }
    /**
     *  <p>方法描述：获取的放射资质</p>
     * @MethodAuthor hsj 2022-08-19 15:49
     */
    public SrvorginfoVo getSrvorginfo(boolean flag) {
        SrvorginfoVo srvorginfoVo = new SrvorginfoVo();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT T.RID,T.ORG_NAME,T.ORG_FZ,T.ORG_ADDR ,T.CERT_NO ,T2.ZONE_GB,T.LINK_MAN ");
        sb.append(" FROM  TD_ZW_SRVORGINFO T ");
        sb.append(" LEFT JOIN TS_UNIT T1 ON T1.RID = T.ORG_ID ");
        sb.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID  = T2.RID  ");
        sb.append(" WHERE  T.ORG_ID =  ").append(Global.getUser().getTsUnit().getRid());
        if(flag){
            sb.append("  AND  T.STATE  =1  ");
        }
        List<Object[]> o = this.findSqlResultList(sb.toString());
        if(!CollectionUtils.isEmpty(o)){
            Object[] objects = o.get(0);
            srvorginfoVo.setRid(objects[0] == null ? null : Integer.valueOf(objects[0].toString()));
            srvorginfoVo.setOrgName(objects[1] == null ? "" : objects[1].toString());
            srvorginfoVo.setOrgFz(objects[2] == null ? "" : objects[2].toString());
            srvorginfoVo.setOrgAddr(objects[3] == null ? "" : objects[3].toString());
            srvorginfoVo.setCertNo(objects[4] == null ? "" : objects[4].toString());
            srvorginfoVo.setZoneGb(objects[5] == null ? "" : objects[5].toString());
            srvorginfoVo.setLinkMan(objects[6] == null ? "" : objects[6].toString());
        }
        return srvorginfoVo;
    }
    /**
     *  <p>方法描述：放射卫生技术服务机构中资质人员的人员属性为技术负责人（5308的extend3的值为5）的</p>
     * @MethodAuthor hsj 2022-08-19 16:13
     */
    public Object[] getProInfo(Integer rid) {
        Object[] object = null;
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT T2.EMP_NAME  ,T2.MOBILE_NO  FROM TD_ZW_PSN_TYPE  T ");
        sb.append(" LEFT JOIN TS_SIMPLE_CODE  T1 ON T.PSN_TYPE = T1.RID ");
        sb.append(" LEFT  JOIN TD_ZW_PSNINFO T2 ON T.MAIN_ID = T2.RID ");
        sb.append(" LEFT JOIN TD_ZW_SRVORGPSNS T3 ON  T3.EMP_ID = T2.RID ");
        sb.append(" WHERE T1.EXTENDS3  = 5 AND T.ZZJG_TYPE = 1 AND T3.ON_DUTY = 1 ");
        sb.append(" AND T3.ORG_ID  =  ").append(rid);
        List<Object[]> o = this.findSqlResultList(sb.toString());
        if(!CollectionUtils.isEmpty(o)){
            object = o.get(0);
        }
        return object;
    }
    /**
     *  <p>方法描述：资质业务范围</p>
     * @MethodAuthor hsj 2022-08-20 13:50
     */
    public void getTdZwSrvorgCardItems(TdZwSrvorgCard srvorgCard, Integer rid) {
        List<TdZwSrvorgCardItems> srvorgCardItems = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT T1.RID ,T1.CODE_NAME FROM  TD_ZW_SRVORGITEMS T  ");
        sb.append(" LEFT JOIN  TS_SIMPLE_CODE  T1 ON T.ITEM_CODE  = T1.CODE_NO ");
        sb.append(" LEFT JOIN  TS_CODE_TYPE T2 ON T1.CODE_TYPE_ID  = T2.RID ");
        sb.append(" WHERE T2.CODE_TYPE_NAME ='5019' ");
        sb.append(" AND  T.ORG_ID   =  ").append(rid);
        List<Object[]> o = this.findSqlResultList(sb.toString());
        List<String> str =new ArrayList<>();
        if(!CollectionUtils.isEmpty(o)){
            for(Object[] objects :o){
                TdZwSrvorgCardItems srvorgCardItem = new TdZwSrvorgCardItems();
                TsSimpleCode tsSimpleCode = new TsSimpleCode();
                tsSimpleCode.setRid(Integer.valueOf(objects[0].toString()));
                tsSimpleCode.setCodeName(objects[1] == null ? null : objects[1].toString());
                srvorgCardItem.setFkByItemId(tsSimpleCode);
                str.add(objects[1] == null ? null : objects[1].toString());
                srvorgCardItems.add(srvorgCardItem);
            }
        }
        srvorgCard.setItemsName(StringUtils.join(str,"，"));
        srvorgCard.getSrvorgCardItemsList().clear();
        srvorgCard.setSrvorgCardItemsList(srvorgCardItems);
    }
    /**
     *  <p>方法描述：报告卡相关信息保存</p>
     * @MethodAuthor hsj 2022-08-22 14:37
     */
    public void saveSrvorgCard(TdZwSrvorgCard srvorgCard) {
        if (null == srvorgCard) {
            return;
        }
        Integer srvorgCardId = srvorgCard.getRid();
        StringBuffer sqlBuffer = new StringBuffer();
        //主表rid不为空 删除子表信息
        if(null != srvorgCardId){
            //资质业务范围
            sqlBuffer.setLength(0);
            sqlBuffer.append("DELETE FROM TD_ZW_SRVORG_CARD_ITEMS WHERE MAIN_ID = ").append(srvorgCardId);
            this.executeSql(sqlBuffer.toString(), null);
            //参与人员（承担的服务事项）
            sqlBuffer.setLength(0);
            sqlBuffer.append("DELETE FROM TD_ZW_SRVORG_CARD_ITEM I WHERE EXISTS(SELECT 1 FROM TD_ZW_SRVORG_CARD_PSN P WHERE I.MAIN_ID = P.RID AND P.MAIN_ID = " ).append(srvorgCardId).append(") ");
            this.executeSql(sqlBuffer.toString(), null);
            //参与人员
            sqlBuffer.setLength(0);
            sqlBuffer.append("DELETE FROM TD_ZW_SRVORG_CARD_PSN WHERE MAIN_ID = ").append(srvorgCardId);
            this.executeSql(sqlBuffer.toString(), null);
            //技术服务领域
            sqlBuffer.setLength(0);
            sqlBuffer.append("DELETE FROM TD_ZW_SRVORG_CARD_SERVICE WHERE MAIN_ID = ").append(srvorgCardId);
            this.executeSql(sqlBuffer.toString(), null);
            //服务地址
            sqlBuffer.setLength(0);
            sqlBuffer.append("DELETE FROM TD_ZW_SRVORG_CARD_ZONE WHERE MAIN_ID = ").append(srvorgCardId);
            this.executeSql(sqlBuffer.toString(), null);
            //放射防护检测超标危害类型
            sqlBuffer.setLength(0);
            sqlBuffer.append("DELETE FROM TD_ZW_SRVORG_CARD_FHJC WHERE MAIN_ID = ").append(srvorgCardId);
            this.executeSql(sqlBuffer.toString(), null);
            //预评价超标危害类型
            sqlBuffer.setLength(0);
            sqlBuffer.append("DELETE FROM TD_ZW_SRVORG_CARD_YPJ WHERE MAIN_ID = ").append(srvorgCardId);
            this.executeSql(sqlBuffer.toString(), null);
            //效果评价超标危害类型
            sqlBuffer.setLength(0);
            sqlBuffer.append("DELETE FROM TD_ZW_SRVORG_CARD_XGPJ WHERE MAIN_ID = ").append(srvorgCardId);
            this.executeSql(sqlBuffer.toString(), null);
            //个人剂量监测
            sqlBuffer.setLength(0);
            sqlBuffer.append("DELETE FROM TD_ZW_SRVORG_CARD_DOSE WHERE MAIN_ID = ").append(srvorgCardId);
            this.executeSql(sqlBuffer.toString(), null);
        }
        //资质业务范围
        List<TdZwSrvorgCardItems> srvorgCardItems = srvorgCard.getSrvorgCardItemsList();
        if (!CollectionUtils.isEmpty(srvorgCardItems)) {
            for (TdZwSrvorgCardItems zwSrvorgCardItems : srvorgCardItems) {
                zwSrvorgCardItems.setFkByMainId(srvorgCard);
                preInsert(zwSrvorgCardItems);
            }
        }
        //参与人员信息不能为空
        List<TdZwSrvorgCardPsn> srvorgCardPsns = srvorgCard.getSrvorgCardPsnList();
        if (!CollectionUtils.isEmpty(srvorgCardPsns)) {
            for (TdZwSrvorgCardPsn srvorgCardPsn : srvorgCardPsns) {
                srvorgCardPsn.setFkByMainId(srvorgCard);
                //参与人员（承担的服务事项） TdZwSrvorgCardItem
                List<TdZwSrvorgCardItem> srvorgCardItemList = srvorgCardPsn.getTdZwSrvorgCardItemList();
                if(!CollectionUtils.isEmpty(srvorgCardItemList)){
                    for(TdZwSrvorgCardItem srvorgCardItem :srvorgCardItemList){
                        preInsert(srvorgCardItem);
                    }
                }
                preInsert(srvorgCardPsn);
            }
        }

        //技术服务地址
        List<TdZwSrvorgCardZone> srvorgCardZoneList = srvorgCard.getSrvorgCardZoneList();
        if (!CollectionUtils.isEmpty(srvorgCardZoneList)) {
            for (TdZwSrvorgCardZone srvorgCardZone : srvorgCardZoneList) {
                srvorgCardZone.setFkByMainId(srvorgCard);
                preInsert(srvorgCardZone);
            }
        }
        //技术服务领域
        List<TdZwSrvorgCardService> srvorgCardServices = srvorgCard.getSrvorgCardServices();
        if (!CollectionUtils.isEmpty(srvorgCardServices)) {
            for (TdZwSrvorgCardService srvorgCardService : srvorgCardServices) {
                srvorgCardService.setFkByMainId(srvorgCard);
                preInsert(srvorgCardService);
            }
        }
        //超标点位放射性危害类型
        List<TdZwSrvorgCardFhjc> srvorgCardFhjcList = srvorgCard.getSrvorgCardFhjcList();
        if (!CollectionUtils.isEmpty(srvorgCardFhjcList)) {
            for (TdZwSrvorgCardFhjc srvorgCardFhjc : srvorgCardFhjcList) {
                srvorgCardFhjc.setFkByMainId(srvorgCard);
                preInsert(srvorgCardFhjc);
            }
        }
        //超标点位放射性危害类型-预评价
        List<TdZwSrvorgCardYpj> srvorgCardYpjList = srvorgCard.getSrvorgCardYpjList();
        if (!CollectionUtils.isEmpty(srvorgCardYpjList)) {
            for (TdZwSrvorgCardYpj srvorgCardYpj : srvorgCardYpjList) {
                srvorgCardYpj.setFkByMainId(srvorgCard);
                preInsert(srvorgCardYpj);
            }
        }

        //超标点位放射性危害类型-控制效果评价
        List<TdZwSrvorgCardXgpj> srvorgCardXgpjList = srvorgCard.getSrvorgCardXgpjList();
        if (!CollectionUtils.isEmpty(srvorgCardXgpjList)) {
            for (TdZwSrvorgCardXgpj srvorgCardXgpj : srvorgCardXgpjList) {
                srvorgCardXgpj.setFkByMainId(srvorgCard);
                preInsert(srvorgCardXgpj);
            }
        }
        //个人剂量监测
        List<TdZwSrvorgCardDose> srvorgCardDoseList = srvorgCard.getSrvorgCardDoseList();
        if (!CollectionUtils.isEmpty(srvorgCardDoseList)) {
            for (TdZwSrvorgCardDose srvorgCardDose : srvorgCardDoseList) {
                srvorgCardDose.setFkByMainId(srvorgCard);
                preInsert(srvorgCardDose);
            }
        }
        super.upsertEntity(srvorgCard);
        //提交时 更新上传主表日志记录状态为0 报告卡未存储过 不需要更新上传主表日志
        if(null != srvorgCardId && null != srvorgCard.getState() && 1 == srvorgCard.getState()){
            sqlBuffer.setLength(0);
            sqlBuffer.append(" UPDATE TD_ZYWS_CARD_RCD SET STATE = 0 , ")
                    .append(" MODIFY_DATE=SYSDATE , ")
                    .append(" MODIFY_MANID=").append(Global.getUser().getRid())
                    .append(" WHERE BUS_TYPE=22 ").append(" AND BUS_ID= ").append(srvorgCard.getRid());
            this.executeSql(sqlBuffer.toString(),null);
        }
    }
    /**
     *  <p>方法描述：根据rid 查询</p>
     * @MethodAuthor hsj 2022-08-24 10:20
     */
    public TdZwSrvorgCard searchCardService(Integer rid) {
        if (null==rid) {
            return null;
        }
        TdZwSrvorgCard srvorgCard = super.find(TdZwSrvorgCard.class, rid);
        srvorgCard.getSrvorgCardItemsList().size();
        //资质业务范围
        if(srvorgCard.getSrvorgCardItemsList().size() > 0){
            List<String> str = new ArrayList();
            if(!CollectionUtils.isEmpty(srvorgCard.getSrvorgCardItemsList())){
                for(TdZwSrvorgCardItems srvorgCardItems :srvorgCard.getSrvorgCardItemsList()){
                    str.add(srvorgCardItems.getFkByItemId().getCodeName());
                }
                srvorgCard.setItemsName(StringUtils.join(str,"，"));
            }
        }
        //已选择的人员初始化
        srvorgCard.setChoosePsnIds(new HashSet<String>());
        //按照姓名排序
        if(srvorgCard.getSrvorgCardPsnList().size() > 0){
            List<TdZwSrvorgCardPsn> list = srvorgCard.getSrvorgCardPsnList();
            Collections.sort(list, new Comparator<TdZwSrvorgCardPsn>() {
                @Override
                public int compare(TdZwSrvorgCardPsn o1, TdZwSrvorgCardPsn o2) {
                    Collator com = Collator.getInstance(Locale.CHINA);
                    return com.compare(o1.getPsnName(), o2.getPsnName());
                }
            });
            //已选择的人员
            for(TdZwSrvorgCardPsn tdZwSrvorgCardPsn :list){
                if(tdZwSrvorgCardPsn.getTdZwSrvorgCardItemList().size() > 0){
                    List<String> l = new ArrayList();
                    List<String> names = new ArrayList();
                    for(TdZwSrvorgCardItem tdZwSrvorgCardItem :tdZwSrvorgCardPsn.getTdZwSrvorgCardItemList()){
                        l.add(tdZwSrvorgCardItem.getFkByItemId().getRid().toString());
                        names.add(tdZwSrvorgCardItem.getFkByItemId().getCodeName());
                    }
                    tdZwSrvorgCardPsn.setSrvorgCardItemIds(l.toArray(new String[l.size()]));
                    tdZwSrvorgCardPsn.setSrvorgCardItemNames(StringUtils.join(names,"，"));
                }
                srvorgCard.getChoosePsnIds().add(tdZwSrvorgCardPsn.getFkByPsnId().getRid().toString());
            }
        }
        //技术服务地址 按照地区编码排序
        if( srvorgCard.getSrvorgCardZoneList().size() > 0){
            List<TdZwSrvorgCardZone> list = srvorgCard.getSrvorgCardZoneList();
            Collections.sort(list, new Comparator<TdZwSrvorgCardZone>() {
                @Override
                public int compare(TdZwSrvorgCardZone o1, TdZwSrvorgCardZone o2) {
                    if(null == o1.getFkByZoneId() || null == o1.getFkByZoneId().getZoneGb()){
                        return 1;
                    }
                    if(null == o2.getFkByZoneId() || null == o2.getFkByZoneId().getZoneGb()){
                        return -1;
                    }
                    return o1.getFkByZoneId().getZoneGb().compareTo(o2.getFkByZoneId().getZoneGb());
                }
            });
            for(TdZwSrvorgCardZone tdZwSrvorgCardZone:list){
                if(null != tdZwSrvorgCardZone.getFkByZoneId() && null != tdZwSrvorgCardZone.getFkByZoneId().getRid()){
                    tdZwSrvorgCardZone.setZoneGb(tdZwSrvorgCardZone.getFkByZoneId().getZoneGb());
                    tdZwSrvorgCardZone.setZoneName(tdZwSrvorgCardZone.getFkByZoneId().getZoneName());
                }
            }
        }
        //技术服务领域
        if(srvorgCard.getSrvorgCardServices().size() > 0){
            List<TdZwSrvorgCardService> list = srvorgCard.getSrvorgCardServices();
            //想按照码表num code 排序
            Collections.sort(list, new Comparator<TdZwSrvorgCardService>() {
                @Override
                public int compare(TdZwSrvorgCardService o1, TdZwSrvorgCardService o2) {
                    int i = o1.getFkByServiceAreaId().getNum().compareTo(o2.getFkByServiceAreaId().getNum());
                    if(i == 0){
                        String codeLevel1 = o1.getFkByServiceAreaId().getCodeLevelNo();
                        String codeLevel2 = o2.getFkByServiceAreaId().getCodeLevelNo();
                        if (StringUtils.isBlank(codeLevel1) && StringUtils.isBlank(codeLevel2)) {
                            return 0;
                        }
                        if (StringUtils.isBlank(codeLevel1)) {
                            return -1;
                        }
                        if (StringUtils.isBlank(codeLevel2)) {
                            return 1;
                        }
                        return codeLevel1.compareTo(codeLevel2);
                    }
                    return i;
                }
            });
            List<String> l = new ArrayList();
            List<String> names = new ArrayList();
            // key 大类rid value 小类名称
            Map<Integer, List<String>> curMap = new HashMap<>();
            // key 大类rid value 大类名称
            Map<Integer, String> fatherMap = new LinkedHashMap<>();
            for(TdZwSrvorgCardService service :list){
                TsSimpleCode serviceArea = service.getFkByServiceAreaId();
                TsSimpleCode fatherArea = service.getFkByFatherAreaId();
                if (null == fatherArea) {
                    fatherArea = serviceArea;
                }
                Integer serviceId = serviceArea.getRid();
                Integer fatherId = fatherArea.getRid();
                if (serviceId.compareTo(fatherId) == 0) {
                    fatherMap.put(fatherId, fatherArea.getCodeName());
                    l.add(fatherId.toString());
                    continue;
                }
                Integer ext2 = fatherArea.getExtendS2();
                List<String> curList = curMap.get(fatherId);
                String ot = "";
                if (null == curList) {
                    curList = new ArrayList<>();
                    curMap.put(fatherId, curList);
                    ot = null != ext2 && 2 == ext2 ? "放射诊疗类型：" : "";
                }
                curList.add(ot+serviceArea.getCodeName());
                l.add(serviceId.toString());
                // names.add(service.getFkByServiceAreaId().getCodeName());
            }
            Set<Integer> keySet = fatherMap.keySet();
            for (Integer key : keySet) {
                String father = fatherMap.get(key);
                List<String> curList = curMap.get(key);
                if (CollectionUtils.isEmpty(curList)) {
                    names.add(father);
                    continue;
                }
                names.add(father+"（"+StringUtils.list2string(curList,"、")+"）");
            }
            srvorgCard.setServices(l.toArray(new String[l.size()]));
            srvorgCard.setServicesName(StringUtils.join(names,"，"));
        }
        //超标点位放射性危害类型
        if(srvorgCard.getSrvorgCardFhjcList().size() > 0){
            List<TdZwSrvorgCardFhjc> list = srvorgCard.getSrvorgCardFhjcList();
            //想按照码表num code 排序
            Collections.sort(list, new Comparator<TdZwSrvorgCardFhjc>() {
                @Override
                public int compare(TdZwSrvorgCardFhjc o1, TdZwSrvorgCardFhjc o2) {
                    int i = o1.getFkByTypeId().getNum().compareTo(o2.getFkByTypeId().getNum());
                    if(i == 0){
                        return o1.getFkByTypeId().getCodeNo().compareTo(o2.getFkByTypeId().getCodeNo());
                    }
                    return i;
                }
            });
            List<String> l = new ArrayList();
            List<String> names = new ArrayList();
            for(TdZwSrvorgCardFhjc service :list){
                l.add(service.getFkByTypeId().getRid().toString());
                names.add(service.getFkByTypeId().getCodeName());
            }
            srvorgCard.setSrvorgCardFhjcs(l.toArray(new String[l.size()]));
            srvorgCard.setSrvorgCardFhjcName(StringUtils.join(names,"，"));
        }
        //超标点位放射性危害类型-预评价
        if(srvorgCard.getSrvorgCardYpjList().size() > 0){
            List<TdZwSrvorgCardYpj> list = srvorgCard.getSrvorgCardYpjList();
            //想按照码表num code 排序
            Collections.sort(list, new Comparator<TdZwSrvorgCardYpj>() {
                @Override
                public int compare(TdZwSrvorgCardYpj o1, TdZwSrvorgCardYpj o2) {
                    int i = o1.getFkByTypeId().getNum().compareTo(o2.getFkByTypeId().getNum());
                    if(i == 0){
                        return o1.getFkByTypeId().getCodeNo().compareTo(o2.getFkByTypeId().getCodeNo());
                    }
                    return i;
                }
            });
            List<String> l = new ArrayList();
            List<String> names = new ArrayList();
            for(TdZwSrvorgCardYpj service :list){
                l.add(service.getFkByTypeId().getRid().toString());
                names.add(service.getFkByTypeId().getCodeName());
            }
            srvorgCard.setSrvorgCardYpjs(l.toArray(new String[l.size()]));
            srvorgCard.setSrvorgCardYpjName(StringUtils.join(names,"，"));
        }
        //超标点位放射性危害类型-控制效果评价
        if(srvorgCard.getSrvorgCardXgpjList().size() > 0){
            List<TdZwSrvorgCardXgpj> list = srvorgCard.getSrvorgCardXgpjList();
            //想按照码表num code 排序
            Collections.sort(list, new Comparator<TdZwSrvorgCardXgpj>() {
                @Override
                public int compare(TdZwSrvorgCardXgpj o1, TdZwSrvorgCardXgpj o2) {
                    int i = o1.getFkByTypeId().getNum().compareTo(o2.getFkByTypeId().getNum());
                    if(i == 0){
                        return o1.getFkByTypeId().getCodeNo().compareTo(o2.getFkByTypeId().getCodeNo());
                    }
                    return i;
                }
            });
            List<String> l = new ArrayList();
            List<String> names = new ArrayList();
            for(TdZwSrvorgCardXgpj service :list){
                l.add(service.getFkByTypeId().getRid().toString());
                names.add(service.getFkByTypeId().getCodeName());
            }
            srvorgCard.setSrvorgCardXgpjs(l.toArray(new String[l.size()]));
            srvorgCard.setSrvorgCardXgpjName(StringUtils.join(names,"，"));
        }
        //按照rid排序
        if(srvorgCard.getSrvorgCardDoseList().size() > 0){
            List<TdZwSrvorgCardDose> list = srvorgCard.getSrvorgCardDoseList();
            //想按照码表num code 排序
            Collections.sort(list, new Comparator<TdZwSrvorgCardDose>() {
                @Override
                public int compare(TdZwSrvorgCardDose o1, TdZwSrvorgCardDose o2) {
                    return o1.getRid().compareTo(o2.getRid());
                }
            });
        }
        return srvorgCard;
    }

    /**
     * <p>方法描述： 获取对应业务类型、业务主键、状态的上传日志表数量 </p>
     * @MethodAuthor： pw 2022/12/14
     **/
    public int queryRcdCountByParams(Integer busType,Integer busId, Integer state){
        if(null == busType || null == busId || null == state){
            return 0;
        }
        StringBuffer queryBuffer = new StringBuffer();
        queryBuffer.append(" SELECT COUNT(1) FROM TD_ZYWS_CARD_RCD T WHERE T.BUS_TYPE= ")
                .append(busType).append(" AND T.BUS_ID=")
                .append(busId).append(" AND T.STATE=")
                .append(state);
        return this.findCountBySql(queryBuffer.toString());

    }

    /**
     *  <p>方法描述：删除附件</p>
     * @MethodAuthor hsj 2024-09-02 9:14
     */
    @Transactional(readOnly = false)
    public void deleteAnnexPathByRid(Integer rid) {
        if(rid == null){
            return;
        }
        String sql = "UPDATE  TD_ZW_SRVORG_CARD SET ANNEX_PATH = null  where RID="+rid;
        this.executeSql(sql);
    }
}
