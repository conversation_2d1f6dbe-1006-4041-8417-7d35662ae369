package com.chis.modules.heth.comm.logic;

public class BhkItemTableInfoDTO {
    //项目
    private String itemName;
    //结果
    private String result;
    //结果显示颜色
    private String resultColour;
    //参考值
    private String referenceValue;
    //计量单位
    private String unit;
    //合格
    private String pass;
    private String passColour;
    //未检
    private String unchecked;
    private String uncheckedColour;
    //结果判定
    private String resultJudge;
    private String resultJudgeColor;

    public String getItemName() {
        return this.itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getResult() {
        return this.result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getReferenceValue() {
        return this.referenceValue;
    }

    public void setReferenceValue(String referenceValue) {
        this.referenceValue = referenceValue;
    }

    public String getUnit() {
        return this.unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getPass() {
        return this.pass;
    }

    public void setPass(String pass) {
        this.pass = pass;
    }

    public String getUnchecked() {
        return this.unchecked;
    }

    public void setUnchecked(String unchecked) {
        this.unchecked = unchecked;
    }

    public String getResultJudge() {
        return this.resultJudge;
    }

    public void setResultJudge(String resultJudge) {
        this.resultJudge = resultJudge;
    }

    public String getResultColour() {
        return this.resultColour;
    }

    public void setResultColour(String resultColour) {
        this.resultColour = resultColour;
    }

    public String getPassColour() {
        return this.passColour;
    }

    public void setPassColour(String passColour) {
        this.passColour = passColour;
    }

    public String getUncheckedColour() {
        return this.uncheckedColour;
    }

    public void setUncheckedColour(String uncheckedColour) {
        this.uncheckedColour = uncheckedColour;
    }

    public String getResultJudgeColor() {
        return this.resultJudgeColor;
    }

    public void setResultJudgeColor(String resultJudgeColor) {
        this.resultJudgeColor = resultJudgeColor;
    }
}
