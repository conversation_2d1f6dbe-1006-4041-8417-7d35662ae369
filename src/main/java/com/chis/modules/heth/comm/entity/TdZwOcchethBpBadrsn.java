package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2024-10-11
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_BP_BADRSN")
@SequenceGenerator(name = "TdZwOcchethBpBadrsn", sequenceName = "TD_ZW_OCCHETH_BP_BADRSN_SEQ", allocationSize = 1)
public class TdZwOcchethBpBadrsn implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwOcchethCard fkByMainId;
    private TsSimpleCode fkByBadrsnId;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private List<TdZwOcchethBpSub> badrsnSubList = new ArrayList<>();
    private Boolean show;
    private List<Integer> selBadrsnIdList;

    public TdZwOcchethBpBadrsn() {
    }

    public TdZwOcchethBpBadrsn(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethBpBadrsn")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwOcchethCard getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwOcchethCard fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "BADRSN_ID")
    public TsSimpleCode getFkByBadrsnId() {
        return fkByBadrsnId;
    }

    public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
        this.fkByBadrsnId = fkByBadrsnId;
    }


    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OrderBy("rid")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwOcchethBpSub> getBadrsnSubList() {
        return badrsnSubList;
    }

    public void setBadrsnSubList(List<TdZwOcchethBpSub> badrsnSubList) {
        this.badrsnSubList = badrsnSubList;
    }

    @Transient
    public Boolean getShow() {
        return show;
    }

    public void setShow(Boolean show) {
        this.show = show;
    }

    @Transient
    public List<Integer> getSelBadrsnIdList() {
        return selBadrsnIdList;
    }

    public void setSelBadrsnIdList(List<Integer> selBadrsnIdList) {
        this.selBadrsnIdList = selBadrsnIdList;
    }
}