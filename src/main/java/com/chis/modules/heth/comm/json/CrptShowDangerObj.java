package com.chis.modules.heth.comm.json;

import java.io.Serializable;

/**
 * <p>
 *     类描述：企业综合展示 详情 危害因素分析 表格数据对象 图标数据对象
 * </p>
 *
 * @ClassAuthor pw,2020年12月10日,CrptShowDangerObj
 */
public class CrptShowDangerObj implements Serializable {
    private static final long serialVersionUID = 6694128945985876061L;
    /** 危害因素类别 */
    private String dangerType;
    /** 检查人数（体检机构） */
    private Integer checkNumber;
    /** 接害人数（企业申报） */
    private Integer reciveNumber;
    /** 检查人数（企业申报） */
    private Integer crptCheckNumber;
    /** 检查人数和接害人数是否相等 用于标红 */
    private boolean unMatch;
    public CrptShowDangerObj(){}
    public CrptShowDangerObj(String dangerType){
        this.dangerType = dangerType;
    }
    public CrptShowDangerObj(Integer checkNumber){
        this.checkNumber = checkNumber;
    }
    public CrptShowDangerObj(String dangerType, Integer checkNumber, Integer reciveNumber) {
        this.dangerType = dangerType;
        this.checkNumber = checkNumber;
        this.reciveNumber = reciveNumber;
    }

    public String getDangerType() {
        return dangerType;
    }

    public void setDangerType(String dangerType) {
        this.dangerType = dangerType;
    }

    public Integer getCheckNumber() {
        return checkNumber;
    }

    public void setCheckNumber(Integer checkNumber) {
        this.checkNumber = checkNumber;
    }

    public Integer getReciveNumber() {
        return reciveNumber;
    }

    public void setReciveNumber(Integer reciveNumber) {
        this.reciveNumber = reciveNumber;
    }

    public Integer getCrptCheckNumber() {
        return crptCheckNumber;
    }

    public void setCrptCheckNumber(Integer crptCheckNumber) {
        this.crptCheckNumber = crptCheckNumber;
    }

    public boolean isUnMatch() {
        if(null == checkNumber && null == reciveNumber){
            return false;
        }else if((null != checkNumber && null == reciveNumber) || (null == checkNumber && null != reciveNumber) ||
                checkNumber.compareTo(reciveNumber) != 0){
            return true;
        }
        return false;
    }

    public void setUnMatch(boolean unMatch) {
        this.unMatch = unMatch;
    }
}
