package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-6
 */
@Entity
@Table(name = "TD_ZY_TRAIN_SITUATION")
@SequenceGenerator(name = "TdZyTrainSituation", sequenceName = "TD_ZY_TRAIN_SITUATION_SEQ", allocationSize = 1)
public class TdZyTrainSituation implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZyUnitbasicinfo fkByMainId;
	private Integer ifLeadersTrain;
	private Integer ifManagersTrain;
	private Integer trainSum;
	private Integer trainSituation;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZyTrainSituation() {
	}

	public TdZyTrainSituation(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZyTrainSituation")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZyUnitbasicinfo getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZyUnitbasicinfo fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "IF_LEADERS_TRAIN")	
	public Integer getIfLeadersTrain() {
		return ifLeadersTrain;
	}

	public void setIfLeadersTrain(Integer ifLeadersTrain) {
		this.ifLeadersTrain = ifLeadersTrain;
	}	
			
	@Column(name = "IF_MANAGERS_TRAIN")	
	public Integer getIfManagersTrain() {
		return ifManagersTrain;
	}

	public void setIfManagersTrain(Integer ifManagersTrain) {
		this.ifManagersTrain = ifManagersTrain;
	}	
			
	@Column(name = "TRAIN_SUM")	
	public Integer getTrainSum() {
		return trainSum;
	}

	public void setTrainSum(Integer trainSum) {
		this.trainSum = trainSum;
	}	
			
	@Column(name = "TRAIN_SITUATION")	
	public Integer getTrainSituation() {
		return trainSituation;
	}

	public void setTrainSituation(Integer trainSituation) {
		this.trainSituation = trainSituation;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}