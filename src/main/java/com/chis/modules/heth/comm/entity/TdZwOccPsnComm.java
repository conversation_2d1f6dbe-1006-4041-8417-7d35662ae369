package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @createTime 2017-5-25
 */
@Entity
@Table(name = "TD_ZW_OCC_PSN")
@SequenceGenerator(name = "TdZwOccPsn", sequenceName = "TD_ZW_OCC_PSN_SEQ", allocationSize = 1)
public class TdZwOccPsnComm implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwOccdiscaseComm fkByMainId;
    private TdZwPsninfoComm fkByPsnId;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    public TdZwOccPsnComm() {
    }

    public TdZwOccPsnComm(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOccPsn")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwOccdiscaseComm getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwOccdiscaseComm fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "PSN_ID")
    public TdZwPsninfoComm getFkByPsnId() {
        return fkByPsnId;
    }

    public void setFkByPsnId(TdZwPsninfoComm fkByPsnId) {
        this.fkByPsnId = fkByPsnId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }
}
