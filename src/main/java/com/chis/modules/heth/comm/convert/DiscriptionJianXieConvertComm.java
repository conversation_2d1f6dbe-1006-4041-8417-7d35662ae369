package com.chis.modules.heth.comm.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.enumn.SystemType;

/**
 * 用于角色维护模块中
 * Created by zww on 2014-5-7.
 */
@FacesConverter("heth.DiscriptionJianXieConvertComm")
public class DiscriptionJianXieConvertComm implements Converter {
    @Override
    public Object getAsObject(FacesContext facesContext, UIComponent component, String submittedValue) {
        if(StringUtils.isNotBlank(submittedValue)) {
            String[] arr = submittedValue.split(":");
            TsCodeType t = new TsCodeType(Integer.valueOf(arr[0]));
            t.setCodeTypeName(arr[1]);
            t.setCodeTypeDesc(arr[2]);
            t.setSystemType((SystemType) EnumUtils.findEnum(SystemType.class, arr[3]));
            return t;
        }
        return null;
    }

    /**
     *
     * @param facesContext
     * @param component
     * @param value 很长的字符 之间写前50个字符
     * @return
     */
    @Override
    public String getAsString(FacesContext facesContext, UIComponent component, Object value) {
        if (value == null) {
            return "";
        } else {
            String t = (String)value;
            if(t!=null&&t.length()>50){
                return t.substring(0,50).concat("...") ;
            }
            return t;
        }
    }

}
