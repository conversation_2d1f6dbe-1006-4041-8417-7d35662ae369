package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjCrptInvest;
import com.chis.modules.heth.comm.entity.TbTjCrptInvstMining;
import com.chis.modules.heth.comm.service.TbTjCrptInvestServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

@ManagedBean(name="tbTjCrptInvestViewListBean")
@ViewScoped
public class TbTjCrptInvestViewListBean extends FacesEditBean  implements IProcessData {
    private static final long serialVersionUID = -4318968484928583113L;
    /**查询条件：地区集合*/
    private List<TsZone> searchZoneList;
    /**查询条件： 地区Id */
    private Integer searchZoneId;
    /**查询条件： 地区编码 */
    private String searchZoneCode;
    /**查询条件： 地区名称 */
    private String searchZoneName;
    /**查询条件： 企业名称 */
    private String searchUnitName;
    /**查询条件： 社会信用代码 */
    private String searchCreditCode;

    /**查询条件： 行业类别*/
    private String selectIndusTypeNames;
    private String selectIndusTypeIds;
    /**查询条件： 经济类型*/
    private String selectEconomyNames;
    private String selectEconomyIds;
    /**查询条件： 企业规模*/
    private List<TsSimpleCode> crptSizeList;
    private String selectCrptSizes;
    private String selectCrptSizeIds;
    /**查询条件： 非正常生产情况*/
    private List<TsSimpleCode> unproducteList;
    private String selectUnpros;
    private String selectUnproIds;

    /**查询条件： 开采方式*/
    private List<TsSimpleCode> miningList;
    private String selectMinings;
    private String selectMiningIds;

    /**查询条件： 状态 */
    private List<String> stateArray;

    /**查询条件： 存在情况 */
    private List<String> existArray;

    /**查询条件： 生产情况 */
    private List<String> productArray;

    /**查询条件： 含铀情况 */
    private List<String> uraniumArray;

    private String simpleCodeType;
    private Integer rid;
    private TbTjCrptInvest crptInvest;

    /**开采方式*/
    private String selectMiningNames;
    private String zoneName;
    private String orginZoneName;


    protected StreamedContent downloadFile;

    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private TbTjCrptInvestServiceImpl tbTjCrptInvestService = SpringContextHolder.getBean(TbTjCrptInvestServiceImpl.class);

    public TbTjCrptInvestViewListBean(){
        this.ifSQL = true;
        stateArray = new ArrayList<>();
        stateArray.add("3");
        existArray = new ArrayList<>();
        productArray = new ArrayList<>();
        uraniumArray = new ArrayList<>();
        initZone();
        initCrptSize();
        this.searchAction();
    }
    @Override
    public void addInit() {}

    @Override
    public void viewInit() {
        this.crptInvest = commService.find(TbTjCrptInvest.class, rid);
        if(null!=this.crptInvest.getFkByZoneId()) {
            Short zoneType = this.crptInvest.getFkByZoneId().getZoneType();
            String fullName = this.crptInvest.getFkByZoneId().getFullName();
            if (zoneType.intValue()>2) {
                int start = fullName.indexOf("_")+1;
                this.zoneName = fullName.substring(start).replace("_", "");
            }else {
                this.zoneName = fullName;
            }
        }
        if(null!=this.crptInvest.getFkByOrginZoneId()) {
            Short zoneType = this.crptInvest.getFkByOrginZoneId().getZoneType();
            String fullName = this.crptInvest.getFkByOrginZoneId().getFullName();
            if (zoneType.intValue()>2) {
                int start = fullName.indexOf("_")+1;
                this.orginZoneName = fullName.substring(start).replace("_", "");
            }else {
                this.orginZoneName = fullName;
            }
        }

        // 开采方式赋值
        this.selectMiningNames = null;
        //mgrbean.crptInvest.fkByIndusTypeId.extendS1==1  crptInvest.existState==1
        if(null != crptInvest && null != crptInvest.getExistState() && 1 == crptInvest.getExistState() &&
                null != crptInvest.getFkByIndusTypeId() && null != crptInvest.getFkByIndusTypeId().getExtendS1() &&
                "1".equals(crptInvest.getFkByIndusTypeId().getExtendS1())){
            List<TbTjCrptInvstMining> miningList = commService.findEntityListByMainId(TbTjCrptInvstMining.class, this.rid);
            if(!CollectionUtils.isEmpty(miningList)){
                StringBuffer miningBuffer = new StringBuffer();
                for(TbTjCrptInvstMining t : miningList){
                    if(null != t.getFkByMiningId() && null != t.getFkByMiningId().getCodeName()){
                        miningBuffer.append("，").append(t.getFkByMiningId().getCodeName());
                    }
                }
                if(miningBuffer.length() > 0){
                    this.selectMiningNames = miningBuffer.substring(1);
                }
            }
        }
    }

    @Override
    public void modInit() {}

    @Override
    public void saveAction() {}

    @Override
    public String[] buildHqls() {
        //组合查询条件
        StringBuffer conditionBuffer = new StringBuffer();
        conditionBuffer.append("WHERE 1=1 ");
        if(StringUtils.isNotBlank(this.searchZoneCode)){
            conditionBuffer.append("AND T1.ZONE_GB LIKE :zoneCode  ");
            this.paramMap.put("zoneCode", ZoneUtil.zoneSelect(this.searchZoneCode)+"%");
        }else{//地区为空 应该查询不到数据
            conditionBuffer.append("AND 1=2 ");
        }
        if(StringUtils.isNotBlank(this.searchUnitName)){
            conditionBuffer.append("AND T.CRPT_NAME LIKE :crptName  escape '\\\'  ");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchUnitName.trim()) + "%");
        }
        if(StringUtils.isNotBlank(this.searchCreditCode)){
            conditionBuffer.append("AND T.INSTITUTION_CODE LIKE :creditCode  escape '\\\'  ");
            this.paramMap.put("creditCode", "%" + StringUtils.convertBFH(this.searchCreditCode.trim()) + "%");
        }
        if(StringUtils.isNotBlank(this.selectIndusTypeIds)){
            conditionBuffer.append("AND T.INDUS_TYPE_ID IN (").append(this.selectIndusTypeIds).append(")  ");
        }
        if(StringUtils.isNotBlank(this.selectEconomyIds)){
            conditionBuffer.append("AND T.ECONOMY_ID IN (").append(this.selectEconomyIds).append(") ");
        }
        if(StringUtils.isNotBlank(this.selectCrptSizeIds)){
            conditionBuffer.append("AND T.CRPT_SIZE_ID IN (").append(this.selectCrptSizeIds).append(") ");
        }
        //开采方式
        if(StringUtils.isNotBlank(this.selectMiningIds)){
            conditionBuffer.append("AND T.RID IN (").append(" SELECT DISTINCT TTIM.MAIN_ID FROM TB_TJ_CRPT_INVST_MINING TTIM WHERE TTIM.MINING_ID IN( ").append(this.selectMiningIds).append(")").append(") ");
        }
        //非正常生产情况
        if(StringUtils.isNotBlank(this.selectUnproIds)){
            conditionBuffer.append("AND T.NO_PRODUCE_STATE_ID IN (").append(this.selectUnproIds).append(") ");
        }

        //状态
        if(!CollectionUtils.isEmpty(this.stateArray)){
            StringBuffer stateBuffer = new StringBuffer();
            for(String state : stateArray){
                stateBuffer.append(",").append(state);
            }
            conditionBuffer.append("AND T.STATE_MARK IN (").append(stateBuffer.substring(1)).append(") ");
        }
        //存在情况
        if(!CollectionUtils.isEmpty(existArray)){
            StringBuffer stateBuffer = new StringBuffer();
            for(String state : existArray){
                stateBuffer.append(",").append(state);
            }
            conditionBuffer.append("AND T.EXIST_STATE IN (").append(stateBuffer.substring(1)).append(") ");
        }
        //生产情况
        if(!CollectionUtils.isEmpty(productArray)){
            StringBuffer stateBuffer = new StringBuffer();
            for(String state : productArray){
                stateBuffer.append(",").append(state);
            }
            conditionBuffer.append("AND T.PRODUCE_STATE IN (").append(stateBuffer.substring(1)).append(") ");
        }
        //含铀情况
        if(!CollectionUtils.isEmpty(uraniumArray)){
            StringBuffer stateBuffer = new StringBuffer();
            for(String state : uraniumArray){
                stateBuffer.append(",").append(state);
            }
            conditionBuffer.append("AND T.IF_HAS_URANIUM IN (").append(stateBuffer.substring(1)).append(") ");
        }
        // 主查询
        StringBuffer querySqlBuffer = new StringBuffer();
        querySqlBuffer.append("SELECT ")
                .append("T1.FULL_NAME AS ZONEFULLNAME, ") //0 地区全称 需要二次处理 去掉第一个下划线前的内容
                .append("T .CRPT_NAME AS CRPTNAME, ")  //1 企业名称
                .append("T .INSTITUTION_CODE AS INSTITUTIONCODE, ")//2 社会信用代码
                .append("T2.CODE_NAME AS INDUSTYPENAME, ")//3 行业类别
                .append("T3.CODE_NAME AS ECONOMYNAME, ")//4 经济类型
                .append("T4.CODE_NAME AS CRPTSIZENAME, ")//5 企业规模
                .append("T .LINKMAN2 AS LINKMAN2, ")//6 联系人
                .append("T .LINKPHONE2 AS LINKPHONE2, ")//7 联系电话
                .append("T .EXIST_STATE AS EXISTSTATE, ")//8 存在情况
                .append("T5.UNITNAME AS FILLUNITNAME, ")//9 填报单位
                .append("T .INVEST_DATE AS INVESTDATE, ")//10 填报日期
                .append("T .STATE_MARK AS STATEMARK, ")//11 状态
                .append("T.RID AS RID, ")//12 调查表 RID
                //辅助用其他信息 start 新增的信息 都加在这下边
                //辅助用其他信息 end
                //空字段 不使用 避免出现查询中多一个逗号导致查询异常的情况
                .append("'' AS ENDSTR  ");
        querySqlBuffer.append("FROM TB_TJ_CRPT_INVEST T ")
                .append("LEFT JOIN TS_ZONE T1 ON T .ZONE_ID = T1.RID ")
                .append("LEFT JOIN TS_SIMPLE_CODE T2 ON T .INDUS_TYPE_ID = T2.RID ")
                .append("LEFT JOIN TS_SIMPLE_CODE T3 ON T .ECONOMY_ID = T3.RID ")
                .append("LEFT JOIN TS_SIMPLE_CODE T4 ON T .CRPT_SIZE_ID = T4.RID ")
                .append("LEFT JOIN TS_UNIT T5 ON T.FILL_UNIT_ID = T5.RID ");
        querySqlBuffer.append(conditionBuffer.toString()).append(" ");// 加入查询条件
        querySqlBuffer.append("ORDER BY T .ZONE_ID, T .CRPT_NAME "); // 排序

        //count 查询
        String countSql = "SELECT COUNT(*) FROM TB_TJ_CRPT_INVEST T LEFT JOIN TS_ZONE T1 ON T .ZONE_ID = T1.RID " + conditionBuffer.toString();

        return new String[]{querySqlBuffer.toString(), countSql};
    }

    @Override
    public void processData(List<?> list) {
        if(!CollectionUtils.isEmpty(list)){
            List<Object[]> result = (List<Object[]>) list;
            for(Object[] object : result){
                // 处理地区
                String zoneFullName = null == object[0] ? null : object[0].toString();
                if(StringUtils.isNotBlank(zoneFullName)){
                    object[0] = (-1 == zoneFullName.indexOf("_")) ? zoneFullName : zoneFullName.substring(zoneFullName.indexOf("_")+1);
                }
            }
        }
    }

    /** 初始化地区 */
    public void initZone(){
        TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
        }
        this.searchZoneCode = tsZone.getZoneCode();
        this.searchZoneName = tsZone.getZoneName();
        this.searchZoneId = tsZone.getRid();
        this.searchZoneList = this.systemModuleService.findZoneListICanSee(false, tsZone.getZoneCode());
    }

    /** 初始化企业规模 */
    public void initCrptSize(){
        this.crptSizeList = this.commService.findSimpleCodesByTypeId("5004");
        this.unproducteList = this.commService.findSimpleCodesByTypeId("5513");
        this.miningList = this.commService.findSimpleCodesByTypeId("5514");
    }

    /** 行业类别与经济类型弹出框 */
    public void selSimpleCodeAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add("5002".equals(this.simpleCodeType) ? "行业类别" : "经济类型");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add(this.simpleCodeType);
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        if ("5002".equals(this.simpleCodeType)) {
            paramList.add(this.selectIndusTypeIds);
        } else if ("5003".equals(this.simpleCodeType)) {
            paramList.add(this.selectEconomyIds);
        }
        paramMap.put("selectIds", paramList);
        if ("5002".equals(this.simpleCodeType)||"5003".equals(this.simpleCodeType)) {
            paramList = new ArrayList<String>();
            paramList.add("true");
            paramMap.put("ifShowFirstCode", paramList);
        }
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList", options, paramMap);
    }
    /** 选择行业类别与经济类型后赋值 */
    public void onSimpleCodeAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
            if (null!=list && list.size()>0) {
                StringBuffer names = new StringBuffer();
                StringBuffer ids = new StringBuffer();
                for (TsSimpleCode t : list) {
                    names.append("，").append(t.getCodeName());
                    ids.append(",").append(t.getRid());
                }
                if ("5002".equals(simpleCodeType)) {
                    //行业类别
                    this.selectIndusTypeIds = ids.substring(1);
                    this.selectIndusTypeNames = names.substring(1);
                }else if ("5003".equals(simpleCodeType)){
                    //经济类型
                    this.selectEconomyIds = ids.substring(1);
                    this.selectEconomyNames = names.substring(1);
                }
            }
        }
    }
    /**
     * <p>方法描述：清空行业类别</p>
     * */
    public void clearSimpleCode() {
        if ("5002".equals(simpleCodeType)) {
            //行业类别
            this.selectIndusTypeNames = null;
            this.selectIndusTypeIds = null;
        }else if ("5003".equals(simpleCodeType)){
            //经济类型
            this.selectEconomyNames = null;
            this.selectEconomyIds = null;
        }
    }


    /**
     * <p>
     *     方法描述：导出
     * </p>
     *
     * @MethodAuthor yph,2021年08月2日
     */
    public DefaultStreamedContent export(){
        StringBuffer sb= new StringBuffer();
        sb.append(DateUtils.getYear()).append("年").append(searchZoneName).append("用人单位信息");
        // 初始化标题
        HSSFWorkbook wb = initTitle(sb.toString(), 14);
        // 初始化第二行标题
        this.initTitle2(wb);
        // 组装数据导出
        boolean b = initExportData(wb);
        if(!b){
            return null;
        }
        return export1(sb.toString(),wb);

    }

    /***
     * @Description : 导出数据
     * @MethodAuthor: mxp
     * @Date : 2019/5/20 16:57
     * @return
     */
    protected boolean initExportData(HSSFWorkbook wb){
        Font font = wb.createFont();
        font.setFontHeightInPoints((short)10);
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);
        HSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 设置水平居中
        cellStyle.setFont(font);
        cellStyle.setBorderLeft(CellStyle.BORDER_THIN); // 左边框
        cellStyle.setBorderRight(CellStyle.BORDER_THIN); // 右边框
        cellStyle.setBorderTop(CellStyle.BORDER_THIN); // 上边框
        cellStyle.setBorderBottom(CellStyle.BORDER_THIN); // 下边框
        cellStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中

        HSSFCellStyle cellStyle3 = wb.createCellStyle();
        cellStyle3.setAlignment(HSSFCellStyle.ALIGN_LEFT);// 设置水平居中
        cellStyle3.setFont(font);
        cellStyle3.setBorderLeft(CellStyle.BORDER_THIN); // 左边框
        cellStyle3.setBorderRight(CellStyle.BORDER_THIN); // 右边框
        cellStyle3.setBorderTop(CellStyle.BORDER_THIN); // 上边框
        cellStyle3.setBorderBottom(CellStyle.BORDER_THIN); // 下边框
        cellStyle3.setVerticalAlignment(CellStyle.VERTICAL_CENTER);//

        //先查总共有多少条数据
        Integer count  = tbTjCrptInvestService.findEntrustCrptExportCount(this.searchZoneCode, this.searchUnitName, this.searchCreditCode, this.selectIndusTypeIds, this.selectEconomyIds,
                this.selectCrptSizeIds, this.selectMiningIds, this.selectUnproIds, this.stateArray, this.existArray, this.productArray, this.uraniumArray);
        if(count>1000000){
            JsfUtil.addErrorMessage("导出数据大于1百万，无法导出！");
            return false;
        }
        HSSFSheet sheet = wb.getSheetAt(0);
        //每次查询10000条
        int  size = 1000;
        long num = count/size;
        if(count%size!=0){
            num++;
        }
        int rowNum=1;
        //一次查询1000条，进行写入EXCEL
        if( null != count && count > 0 )	{
            //按1000条查询 处理次数
            int allDataCount = count%1000==0?count/1000:((count/1000)+1);
            System.err.println("数据总条数："+count+"，每次导出1000条数据切分次数："+allDataCount+"。");
            for( int i = 0 ; i < allDataCount ; i++ )	{
                List<Object[]> findBhkExportList = tbTjCrptInvestService.findEntrustCrptExportList(this.searchZoneCode, this.searchUnitName, this.searchCreditCode, this.selectIndusTypeIds, this.selectEconomyIds,
                        this.selectCrptSizeIds, this.selectMiningIds, this.selectUnproIds, this.stateArray, this.existArray, this.productArray, this.uraniumArray,i * 1000, (i + 1) * 1000);
                if( null != findBhkExportList && findBhkExportList.size() > 0 )	{
                    System.err.println("本次导出序号："+i);
                    rowNum = (i)*size;
                    //导出人员
                    for( Object[] objArr : findBhkExportList )	{
                        HSSFRow row2 = sheet.createRow(rowNum+2);
                        row2.setHeightInPoints((short) (16));
                        /**用人单位信息*/
                        HSSFCell cell0 = row2.createCell(0);
                        cell0.setCellValue(objArr[0]==null? "": objArr[0].toString());
                        cell0.setCellStyle(cellStyle3);

                        HSSFCell cell1 = row2.createCell(1);
                        cell1.setCellValue(objArr[1]==null? "":objArr[1].toString());
                        cell1.setCellStyle(cellStyle3);

                        HSSFCell cell2 = row2.createCell(2);
                        cell2.setCellValue(objArr[2]==null? "":objArr[2].toString());
                        cell2.setCellStyle(cellStyle3);

                        HSSFCell cell3 = row2.createCell(3);
                        cell3.setCellValue(objArr[3]==null? "":(Integer.parseInt(objArr[3].toString())==1?"是":"否"));
                        cell3.setCellStyle(cellStyle);

                        HSSFCell cell4 = row2.createCell(4);
                        cell4.setCellValue(objArr[4]==null? "":objArr[4].toString());
                        cell4.setCellStyle(cellStyle3);


                        HSSFCell cell5 = row2.createCell(5);
                        cell5.setCellValue(objArr[5]==null? "":objArr[5].toString());
                        cell5.setCellStyle(cellStyle3);

                        HSSFCell cell6 = row2.createCell(6);
                        cell6.setCellValue(objArr[6]==null? "":objArr[6].toString());
                        cell6.setCellStyle(cellStyle3);

                        HSSFCell cell7 = row2.createCell(7);
                        cell7.setCellValue(objArr[7]==null? "":objArr[7].toString());
                        cell7.setCellStyle(cellStyle);

                        HSSFCell cell8 = row2.createCell(8);
                        cell8.setCellValue(objArr[8]==null? "":objArr[8].toString());
                        cell8.setCellStyle(cellStyle);

                        HSSFCell cell9 = row2.createCell(9);
                        cell9.setCellValue(objArr[9]==null? "":objArr[9].toString());
                        cell9.setCellStyle(cellStyle);

                        HSSFCell cell10 = row2.createCell(10);
                        cell10.setCellValue(objArr[10]==null? "":objArr[10].toString());
                        cell10.setCellStyle(cellStyle);

                        HSSFCell cell11 = row2.createCell(11);
                        cell11.setCellValue(objArr[11]==null? "":objArr[11].toString());
                        cell11.setCellStyle(cellStyle);

                        HSSFCell cell12 = row2.createCell(12);
                        cell12.setCellValue(objArr[12]==null? "":objArr[12].toString());
                        cell12.setCellStyle(cellStyle);

                        HSSFCell cell13 = row2.createCell(13);
                        cell13.setCellValue(objArr[13]==null? "":objArr[13].toString());
                        cell13.setCellStyle(cellStyle);

                        /**调查情况*/
                        HSSFCell cell14 = row2.createCell(14);
                        String cz = objArr[14]==null? "":(Integer.valueOf(objArr[14].toString())==1?"存在":"不存在");
                        cell14.setCellValue(cz);
                        cell14.setCellStyle(cellStyle);

                        HSSFCell cell15 = row2.createCell(15);
                        String investState = objArr[15]==null? "":(Integer.valueOf(objArr[15].toString())==1?"正常生产":"非正常生产");
                        cell15.setCellValue(investState);
                        cell15.setCellStyle(cellStyle);

                        HSSFCell cell16 = row2.createCell(16);
                        cell16.setCellValue(objArr[16]==null? "":objArr[16].toString());
                        cell16.setCellStyle(cellStyle);

                        HSSFCell cell17 = row2.createCell(17);
                        cell17.setCellStyle(cellStyle3);
                        cell17.setCellValue(objArr[17]==null? "":objArr[17].toString());

                        //开采方式
                        HSSFCell cell18 = row2.createCell(18);
                        cell18.setCellStyle(cellStyle);
                        cell18.setCellValue(objArr[18]==null? "":objArr[18].toString());

                        HSSFCell cell19 = row2.createCell(19);
                        String hy = objArr[19]==null? "":(Integer.valueOf(objArr[19].toString())==1?"含铀":"不含铀");
                        cell19.setCellStyle(cellStyle);
                        cell19.setCellValue(hy);

                        HSSFCell cell20 = row2.createCell(20);
                        cell20.setCellValue(objArr[20]==null? "":objArr[20].toString());
                        cell20.setCellStyle(cellStyle3);

                        HSSFCell cell21 = row2.createCell(21);
                        cell21.setCellValue(objArr[21]==null? "":objArr[21].toString().substring(0,10));
                        cell21.setCellStyle(cellStyle);

                        /**审核情况*/
                        HSSFCell cell22 = row2.createCell(22);
                        String shenHe = "";
                        if(objArr[22]!=null && StringUtils.isNotBlank(objArr[22].toString())){
                            if(Integer.parseInt(objArr[22].toString())==0 || Integer.parseInt(objArr[22].toString())==5){
                                shenHe = "待填报";
                            }
                            if(Integer.parseInt(objArr[22].toString())==1){
                                shenHe = "待审核";
                            }
                            if(Integer.parseInt(objArr[22].toString())==2){
                                shenHe = "已退回";
                            }
                            if(Integer.parseInt(objArr[22].toString())==3){
                                shenHe = "审核完成";
                            }
                            if(Integer.parseInt(objArr[22].toString())==4){
                                shenHe = "地区变更";
                            }

                        }
                        cell22.setCellValue(shenHe);
                        cell22.setCellStyle(cellStyle);

                        HSSFCell cell23 = row2.createCell(23);
                        HSSFCell cell24 = row2.createCell(24);
                        String shenheUnitName = "";
                        String shenheDate = "";
                        if(objArr[22]!=null && (Integer.parseInt(objArr[22].toString())==2 || Integer.parseInt(objArr[22].toString())==3 )){
                            shenheUnitName = objArr[23]==null? "":objArr[23].toString();
                            shenheDate = objArr[24]==null? "":objArr[24].toString().substring(0,10);
                        }
                        cell23.setCellValue(shenheUnitName);
                        cell23.setCellStyle(cellStyle3);
                        cell24.setCellValue(shenheDate);
                        cell24.setCellStyle(cellStyle);
                        rowNum++;
                    }
                }
            }

        }
        return true;
    }

    /***
     * @Description : 导出
     * @MethodAuthor: mxp
     * @Date : 2019/5/20 16:47
     */
    protected DefaultStreamedContent export1(String fileName, HSSFWorkbook wb) {
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("showStatus();");
        ByteArrayOutputStream baos = null;
        try {
            fileName = new String((fileName + ".xlsx").getBytes("UTF-8"), "ISO-8859-1");
            baos = new ByteArrayOutputStream();
            wb.write(baos);
            baos.flush();
            byte[] aa = baos.toByteArray();
            context.execute("hideStatus();");
            return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
        } catch (IOException e) {
            JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
        } finally {
            if (baos != null) {
                try {
                    baos.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        context.execute("hideStatus();");
        return null;

    }

    protected HSSFWorkbook initTitle(String fileName, int lastCol) {
        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet();
        HSSFFont font = wb.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);// 字体大小
        HSSFCellStyle cellStyle = wb.createCellStyle();
        // 文字居中
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        cellStyle.setFont(font);
        cellStyle.setBorderLeft(CellStyle.BORDER_THIN); // 左边框
        cellStyle.setBorderRight(CellStyle.BORDER_THIN); // 右边框
        cellStyle.setBorderTop(CellStyle.BORDER_THIN); // 上边框
        cellStyle.setBorderBottom(CellStyle.BORDER_THIN); // 下边框
        cellStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中


        HSSFRow row = sheet.createRow(0);
        row.setHeightInPoints((short) (32));
        /*用人单位情况*/
        HSSFCell cell = row.createCell(0);
        cell.setCellStyle(cellStyle);
        cell.setCellValue("用人单位情况");
        CellRangeAddress region = new CellRangeAddress(0, 0, 0, 13);
        sheet.addMergedRegion(region);

        HSSFCell cel2 = row.createCell(14);
        cel2.setCellStyle(cellStyle);
        cel2.setCellValue("调查情况");
        CellRangeAddress region1 = new CellRangeAddress(0, 0, 14, 21);
        sheet.addMergedRegion(region1);

        HSSFCell cel3 = row.createCell(22);
        cel3.setCellStyle(cellStyle);
        cel3.setCellValue("审核情况");
        CellRangeAddress region2 = new CellRangeAddress(0, 0, 22, 24);
        sheet.addMergedRegion(region2);

        HSSFCell cel4 = row.createCell(24);
        cel4.setCellStyle(cellStyle);
        CellRangeAddress region3 = new CellRangeAddress(0, 0, 24, 24);
        sheet.addMergedRegion(region3);

        //初始化第二行
        initTitle2(wb);
        return wb;
    }

    public void initTitle2(HSSFWorkbook wb) {
        HSSFSheet sheet = wb.getSheetAt(0);
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        cellStyle.setBorderLeft(CellStyle.BORDER_THIN); // 左边框
        cellStyle.setBorderRight(CellStyle.BORDER_THIN); // 右边框
        cellStyle.setBorderTop(CellStyle.BORDER_THIN); // 上边框
        cellStyle.setBorderBottom(CellStyle.BORDER_THIN); // 下边框
        cellStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        cellStyle.setAlignment(CellStyle.ALIGN_CENTER);// 左右居中
        Font font = wb.createFont();
        font.setFontHeightInPoints((short)10);
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);
        cellStyle.setFont(font);
        // 表列名称
        HSSFRow row2 = sheet.createRow(1);
        row2.setHeightInPoints((short) (32));
        /**用人单位情况*/
        HSSFCell cell0 = row2.createCell(0);
        cell0.setCellValue("所属地区");
        cell0.setCellStyle(cellStyle);
        sheet.setColumnWidth(0, 7000);

        HSSFCell cell1 = row2.createCell(1);
        cell1.setCellValue("用人单位名称");
        cell1.setCellStyle(cellStyle);
        sheet.setColumnWidth(1, 7000);

        HSSFCell cell2 = row2.createCell(2);
        cell2.setCellValue("社会信用代码");
        cell2.setCellStyle(cellStyle);
        sheet.setColumnWidth(2, 5500);

        HSSFCell cell3 = row2.createCell(3);
        cell3.setCellValue("是否分支机构");
        cell3.setCellStyle(cellStyle);
        sheet.setColumnWidth(3, 4000);

        HSSFCell cell4 = row2.createCell(4);
        cell4.setCellValue("单位地址");
        cell4.setCellStyle(cellStyle);
        sheet.setColumnWidth(4, 8000);


        HSSFCell cell5 = row2.createCell(5);
        cell5.setCellValue("行业类别");
        cell5.setCellStyle(cellStyle);
        sheet.setColumnWidth(5, 5500);

        HSSFCell cell6 = row2.createCell(6);
        cell6.setCellValue("经济类型");
        cell6.setCellStyle(cellStyle);
        sheet.setColumnWidth(6, 4000);

        HSSFCell cell7 = row2.createCell(7);
        cell7.setCellValue("企业规模");
        cell7.setCellStyle(cellStyle);
        sheet.setColumnWidth(7, 4000);

        HSSFCell cell8 = row2.createCell(8);
        cell8.setCellValue("法人");
        cell8.setCellStyle(cellStyle);
        sheet.setColumnWidth(8, 4000);

        HSSFCell cell9 = row2.createCell(9);
        cell9.setCellValue("法人联系电话");
        cell9.setCellStyle(cellStyle);
        sheet.setColumnWidth(9, 4000);

        HSSFCell cell10 = row2.createCell(10);
        cell10.setCellValue("联系人");
        cell10.setCellStyle(cellStyle);
        sheet.setColumnWidth(10, 4000);

        HSSFCell cell11 = row2.createCell(11);
        cell11.setCellValue("联系人电话");
        cell11.setCellStyle(cellStyle);
        sheet.setColumnWidth(11, 4000);

        HSSFCell cell12 = row2.createCell(12);
        cell12.setCellValue("职工总人数");
        cell12.setCellStyle(cellStyle);
        sheet.setColumnWidth(12, 4000);

        HSSFCell cell13 = row2.createCell(13);
        cell13.setCellValue("接害总人数");
        cell13.setCellStyle(cellStyle);
        sheet.setColumnWidth(13, 4000);

        /**调查情况*/
        HSSFCell cell14 = row2.createCell(14);
        cell14.setCellValue("存在情况");
        cell14.setCellStyle(cellStyle);
        sheet.setColumnWidth(14, 4000);

        HSSFCell cell15 = row2.createCell(15);
        cell15.setCellValue("生产情况");
        cell15.setCellStyle(cellStyle);
        sheet.setColumnWidth(15, 4000);

        HSSFCell cell16 = row2.createCell(16);
        cell16.setCellValue("非正常生产情况");
        cell16.setCellStyle(cellStyle);
        sheet.setColumnWidth(16, 4000);

        HSSFCell cell17 = row2.createCell(17);
        cell17.setCellValue("其他说明");
        cell17.setCellStyle(cellStyle);
        sheet.setColumnWidth(17, 4000);

        HSSFCell cell18 = row2.createCell(18);
        cell18.setCellValue("开采方式");
        cell18.setCellStyle(cellStyle);
        sheet.setColumnWidth(18, 4000);

        HSSFCell cell19 = row2.createCell(19);
        cell19.setCellValue("含铀情况");
        cell19.setCellStyle(cellStyle);
        sheet.setColumnWidth(19, 4000);

        HSSFCell cell20 = row2.createCell(20);
        cell20.setCellValue("调查机构");
        cell20.setCellStyle(cellStyle);
        sheet.setColumnWidth(20, 4000);

        HSSFCell cell21 = row2.createCell(21);
        cell21.setCellValue("调查日期");
        cell21.setCellStyle(cellStyle);
        sheet.setColumnWidth(21, 4000);

        /**审核情况*/
        HSSFCell cell22 = row2.createCell(22);
        cell22.setCellValue("审核状态");
        cell22.setCellStyle(cellStyle);
        sheet.setColumnWidth(22, 4000);

        HSSFCell cell23 = row2.createCell(23);
        cell23.setCellValue("审核机构");
        cell23.setCellStyle(cellStyle);
        sheet.setColumnWidth(23, 4000);

        HSSFCell cell24 = row2.createCell(24);
        cell24.setCellValue("审核日期");
        cell24.setCellStyle(cellStyle);
        sheet.setColumnWidth(24, 4000);

    }


    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public Integer getSearchZoneId() {
        return searchZoneId;
    }

    public void setSearchZoneId(Integer searchZoneId) {
        this.searchZoneId = searchZoneId;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public String getSearchCreditCode() {
        return searchCreditCode;
    }

    public void setSearchCreditCode(String searchCreditCode) {
        this.searchCreditCode = searchCreditCode;
    }

    public String getSelectIndusTypeNames() {
        return selectIndusTypeNames;
    }

    public void setSelectIndusTypeNames(String selectIndusTypeNames) {
        this.selectIndusTypeNames = selectIndusTypeNames;
    }

    public String getSelectIndusTypeIds() {
        return selectIndusTypeIds;
    }

    public void setSelectIndusTypeIds(String selectIndusTypeIds) {
        this.selectIndusTypeIds = selectIndusTypeIds;
    }

    public String getSelectEconomyNames() {
        return selectEconomyNames;
    }

    public void setSelectEconomyNames(String selectEconomyNames) {
        this.selectEconomyNames = selectEconomyNames;
    }

    public String getSelectEconomyIds() {
        return selectEconomyIds;
    }

    public void setSelectEconomyIds(String selectEconomyIds) {
        this.selectEconomyIds = selectEconomyIds;
    }

    public List<TsSimpleCode> getCrptSizeList() {
        return crptSizeList;
    }

    public void setCrptSizeList(List<TsSimpleCode> crptSizeList) {
        this.crptSizeList = crptSizeList;
    }

    public String getSelectCrptSizes() {
        return selectCrptSizes;
    }

    public void setSelectCrptSizes(String selectCrptSizes) {
        this.selectCrptSizes = selectCrptSizes;
    }

    public String getSelectCrptSizeIds() {
        return selectCrptSizeIds;
    }

    public void setSelectCrptSizeIds(String selectCrptSizeIds) {
        this.selectCrptSizeIds = selectCrptSizeIds;
    }

    public List<String> getStateArray() {
        return stateArray;
    }

    public void setStateArray(List<String> stateArray) {
        this.stateArray = stateArray;
    }

    public String getSimpleCodeType() {
        return simpleCodeType;
    }

    public void setSimpleCodeType(String simpleCodeType) {
        this.simpleCodeType = simpleCodeType;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TbTjCrptInvest getCrptInvest() {
        return crptInvest;
    }

    public void setCrptInvest(TbTjCrptInvest crptInvest) {
        this.crptInvest = crptInvest;
    }

    public String getSelectMiningNames() {
        return selectMiningNames;
    }

    public void setSelectMiningNames(String selectMiningNames) {
        this.selectMiningNames = selectMiningNames;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getOrginZoneName() {
        return orginZoneName;
    }

    public void setOrginZoneName(String orginZoneName) {
        this.orginZoneName = orginZoneName;
    }

    public List<TsSimpleCode> getUnproducteList() {
        return unproducteList;
    }

    public void setUnproducteList(List<TsSimpleCode> unproducteList) {
        this.unproducteList = unproducteList;
    }

    public String getSelectUnpros() {
        return selectUnpros;
    }

    public void setSelectUnpros(String selectUnpros) {
        this.selectUnpros = selectUnpros;
    }

    public String getSelectUnproIds() {
        return selectUnproIds;
    }

    public void setSelectUnproIds(String selectUnproIds) {
        this.selectUnproIds = selectUnproIds;
    }

    public List<String> getExistArray() {
        return existArray;
    }

    public void setExistArray(List<String> existArray) {
        this.existArray = existArray;
    }

    public List<String> getProductArray() {
        return productArray;
    }

    public void setProductArray(List<String> productArray) {
        this.productArray = productArray;
    }

    public List<String> getUraniumArray() {
        return uraniumArray;
    }

    public void setUraniumArray(List<String> uraniumArray) {
        this.uraniumArray = uraniumArray;
    }

    public List<TsSimpleCode> getMiningList() {
        return miningList;
    }

    public void setMiningList(List<TsSimpleCode> miningList) {
        this.miningList = miningList;
    }

    public String getSelectMinings() {
        return selectMinings;
    }

    public void setSelectMinings(String selectMinings) {
        this.selectMinings = selectMinings;
    }

    public String getSelectMiningIds() {
        return selectMiningIds;
    }

    public void setSelectMiningIds(String selectMiningIds) {
        this.selectMiningIds = selectMiningIds;
    }

    public StreamedContent getDownloadFile() {
        return export();
    }

    public void setDownloadFile(StreamedContent downloadFile) {
        this.downloadFile = downloadFile;
    }

}
