package com.chis.modules.heth.comm.web;

import com.chis.common.bean.FastReportData;
import com.chis.common.bean.FastReportDataRef;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.PropertyUtils;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IFastReport;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.web.FacesEditBean;
import com.chis.modules.system.web.FastReportBean;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description : 报告卡上报基类
 * @ClassAuthor : anjing
 * @Date : 2020/7/21 17:00
 * 
 * 
 * <p>修订内容：GBZ188不规范数据填报基类不再使用</p>
 * @ClassReviser qrr,2022年6月27日,AbstractReportCardCommNewBean
 **/
@Deprecated
public class AbstractReportCardCommNewBean extends FacesEditBean implements IProcessData, IFastReport {

    /**配置文件中报告打印日期之后的数据*/
    protected String bhkBeginDate;
    /**查询条件：状态*/
    protected String[] states;
    protected List<SelectItem> stateList;
    protected  TsZone zone;
    protected SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    /**是否报告打印日期 receiveDate=1：接收日期，查询匹配报告卡打印日期；2：显示“体检日期”，查询、显示匹配BHK_DATE；*/
    protected Boolean ifRptDate;
    /**是否显示处理期限*/
    protected Boolean ifshowdeadline;
    /**退回原因*/
    protected String backRsn;
    /**退回原因只读*/
    protected Boolean readOnly = false;

    public AbstractReportCardCommNewBean() {
        zone = this.sessionData.getUser().getTsUnit().getTsZone();
        /**获取配置文件：上报审核数据开始日期*/
        this.bhkBeginDate = PropertyUtils.getValue("reportCard.bhkBeginDate");
        /**获取配置文件：是否报告打印日期*/
        String value = PropertyUtils.getValueWithoutException("receiveDate");
        if("1".equals(value)){
            ifRptDate=true;
        }else{
            ifRptDate=false;
        }
        /**获取配置文件：是否显示处理期限*/
        String value2 = PropertyUtils.getValueWithoutException("ifshowdeadline");
        if("true".equals(value2)){
            ifshowdeadline=true;
        }else{
            ifshowdeadline=false;
        }
        // 状态初始化
        this.initState();
    }

    /**
    * @Description : 状态初始化
    * @MethodAuthor: anjing
    * @Date : 2020/7/21 17:36
    **/
    private void initState() {
        // 默认：终审完成
        states = new String[]{"7"};
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem("0.5", "待填报"));
        this.stateList.add(new SelectItem("0", "待提交"));
        this.stateList.add(new SelectItem("2,4,6", "已退回"));
        this.stateList.add(new SelectItem("1", "待初审"));
        this.stateList.add(new SelectItem("3", "待复审"));
        this.stateList.add(new SelectItem("5", "待终审"));
        this.stateList.add(new SelectItem("7", "终审完成"));

        this.states = new String[] { "0.5", "0", "2,4,6"};
    }



    @Override
    public FastReportBean getFastReportBean() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public List<FastReportData> supportFastReportDataSet() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public List<FastReportDataRef> supportFastReportDataRef() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public void processData(List<?> list) {
        // TODO Auto-generated method stub
    }

    @Override
    public void addInit() {
        // TODO Auto-generated method stub
    }

    @Override
    public void viewInit() {
        // TODO Auto-generated method stub
    }

    @Override
    public void modInit() {
        // TODO Auto-generated method stub
    }

    @Override
    public void saveAction() {
        // TODO Auto-generated method stub
    }

    @Override
    public String[] buildHqls() {
        // TODO Auto-generated method stub
        return new String[0];
    }

    public String getBhkBeginDate() {
        return bhkBeginDate;
    }

    public void setBhkBeginDate(String bhkBeginDate) {
        this.bhkBeginDate = bhkBeginDate;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public TsZone getZone() {
        return zone;
    }
    public void setZone(TsZone zone) {
        this.zone = zone;
    }

    public Boolean getIfRptDate() {
        return ifRptDate;
    }

    public void setIfRptDate(Boolean ifRptDate) {
        this.ifRptDate = ifRptDate;
    }

    public Boolean getIfshowdeadline() {
        return ifshowdeadline;
    }

    public void setIfshowdeadline(Boolean ifshowdeadline) {
        this.ifshowdeadline = ifshowdeadline;
    }

    public String getBackRsn() {
        return backRsn;
    }

    public void setBackRsn(String backRsn) {
        this.backRsn = backRsn;
    }

    public Boolean getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(Boolean readOnly) {
        this.readOnly = readOnly;
    }
}
