package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-11-17
 */
@Entity
@Table(name = "TD_ZWYJ_DANGER_ITEMS")
@SequenceGenerator(name = "TdZwyjDangerItems", sequenceName = "TD_ZWYJ_DANGER_ITEMS_SEQ", allocationSize = 1)
public class TdZwyjDangerItems implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbTjItems fkByItemId;
	private String dangerName;
	private TsSimpleCode fkByMsruntId;
	private BigDecimal geVal;
	private BigDecimal gtVal;
	private BigDecimal leVal;
	private BigDecimal ltVal;
	private Integer ifReveal;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	//展示最低值
	private String gVal;
	//展示最高值
	private String lVal;
	
	public TdZwyjDangerItems() {
	}

	public TdZwyjDangerItems(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjDangerItems")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ITEM_ID")			
	public TbTjItems getFkByItemId() {
		return fkByItemId;
	}

	public void setFkByItemId(TbTjItems fkByItemId) {
		this.fkByItemId = fkByItemId;
	}	
			
	@Column(name = "DANGER_NAME")	
	public String getDangerName() {
		return dangerName;
	}

	public void setDangerName(String dangerName) {
		this.dangerName = dangerName;
	}	
			
	@ManyToOne
	@JoinColumn(name = "MSRUNT_ID")			
	public TsSimpleCode getFkByMsruntId() {
		return fkByMsruntId;
	}

	public void setFkByMsruntId(TsSimpleCode fkByMsruntId) {
		this.fkByMsruntId = fkByMsruntId;
	}	
			
	@Column(name = "GE_VAL")	
	public BigDecimal getGeVal() {
		return geVal;
	}

	public void setGeVal(BigDecimal geVal) {
		this.geVal = geVal;
	}	
			
	@Column(name = "GT_VAL")	
	public BigDecimal getGtVal() {
		return gtVal;
	}

	public void setGtVal(BigDecimal gtVal) {
		this.gtVal = gtVal;
	}	
			
	@Column(name = "LE_VAL")	
	public BigDecimal getLeVal() {
		return leVal;
	}

	public void setLeVal(BigDecimal leVal) {
		this.leVal = leVal;
	}	
			
	@Column(name = "LT_VAL")	
	public BigDecimal getLtVal() {
		return ltVal;
	}

	public void setLtVal(BigDecimal ltVal) {
		this.ltVal = ltVal;
	}	
			
	@Column(name = "IF_REVEAL")	
	public Integer getIfReveal() {
		return ifReveal;
	}

	public void setIfReveal(Integer ifReveal) {
		this.ifReveal = ifReveal;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Transient
	public String getgVal() {
		return gVal;
	}

	public void setgVal(String gVal) {
		this.gVal = gVal;
	}

	@Transient
	public String getlVal() {
		return lVal;
	}

	public void setlVal(String lVal) {
		this.lVal = lVal;
	}
}