package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;

import javax.persistence.*;

/**
 * 人员信息表
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置<br/>
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 */
@Entity
@Table(name = "TD_TJ_PERSON")
@SequenceGenerator(name = "TdTjPerson_Seq", sequenceName = "TD_TJ_PERSON_SEQ", allocationSize = 1)
public class TdTjPerson implements java.io.Serializable {

    private static final long serialVersionUID = -6784462044622257238L;
    private Integer rid;
    private String personName;
    private String sex;
    private String idc;
    private Date brth;
    private Integer age;
    private String isxmrd;
    private String pyxnam;
    private String brplace;
    private String job;
    private String nativeplace;
    private String pstn;
    private String hmxads;
    private String mlc;
    private String folk;
    private String edcdgr;
    private String pct;
    private String tel;
    private String dept;
    private String jobcod;
    private String ntnlty;
    private Date createDate;
    private Integer createManid;
    private TsSimpleCode fkByCardTypeId;
    private String upTag;
    private String proRsn;
    private String vuid;
    private String empi;
    private String kh;
    private String klx;
    private String sfzs;
    private String ecContent;
    // Constructors


    public TdTjPerson() {
    }

    public TdTjPerson(Integer rid, String personName, String sex, String pyxnam, Date createDate, Integer createManid) {
        this.rid = rid;
        this.personName = personName;
        this.sex = sex;
        this.pyxnam = pyxnam;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjPerson_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Column(name = "PERSON_NAME" , length = 200)
    public String getPersonName() {
        return this.personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    @Column(name = "SEX" , length = 10)
    public String getSex() {
        return this.sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    @Column(name = "IDC", length = 50)
    public String getIdc() {
        return this.idc;
    }

    public void setIdc(String idc) {
        this.idc = idc;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BRTH", length = 7)
    public Date getBrth() {
        return this.brth;
    }

    public void setBrth(Date brth) {
        this.brth = brth;
    }

    @Column(name = "AGE")
    public Integer getAge() {
        return this.age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    @Column(name = "ISXMRD", length = 10)
    public String getIsxmrd() {
        return this.isxmrd;
    }

    public void setIsxmrd(String isxmrd) {
        this.isxmrd = isxmrd;
    }

    @Column(name = "PYXNAM" , length = 200)
    public String getPyxnam() {
        return this.pyxnam;
    }

    public void setPyxnam(String pyxnam) {
        this.pyxnam = pyxnam;
    }

    @Column(name = "BRPLACE", length = 50)
    public String getBrplace() {
        return this.brplace;
    }

    public void setBrplace(String brplace) {
        this.brplace = brplace;
    }

    @Column(name = "JOB", length = 50)
    public String getJob() {
        return this.job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    @Column(name = "NATIVEPLACE", length = 50)
    public String getNativeplace() {
        return this.nativeplace;
    }

    public void setNativeplace(String nativeplace) {
        this.nativeplace = nativeplace;
    }

    @Column(name = "PSTN", length = 50)
    public String getPstn() {
        return this.pstn;
    }

    public void setPstn(String pstn) {
        this.pstn = pstn;
    }

    @Column(name = "HMXADS", length = 200)
    public String getHmxads() {
        return this.hmxads;
    }

    public void setHmxads(String hmxads) {
        this.hmxads = hmxads;
    }

    @Column(name = "MLC", length = 10)
    public String getMlc() {
        return this.mlc;
    }

    public void setMlc(String mlc) {
        this.mlc = mlc;
    }

    @Column(name = "FOLK", length = 50)
    public String getFolk() {
        return this.folk;
    }

    public void setFolk(String folk) {
        this.folk = folk;
    }

    @Column(name = "EDCDGR", length = 50)
    public String getEdcdgr() {
        return this.edcdgr;
    }

    public void setEdcdgr(String edcdgr) {
        this.edcdgr = edcdgr;
    }

    @Column(name = "PCT", length = 200)
    public String getPct() {
        return this.pct;
    }

    public void setPct(String pct) {
        this.pct = pct;
    }

    @Column(name = "TEL", length = 50)
    public String getTel() {
        return this.tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    @Column(name = "DEPT", length = 50)
    public String getDept() {
        return this.dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    @Column(name = "JOBCOD", length = 50)
    public String getJobcod() {
        return this.jobcod;
    }

    public void setJobcod(String jobcod) {
        this.jobcod = jobcod;
    }

    @Column(name = "NTNLTY", length = 50)
    public String getNtnlty() {
        return this.ntnlty;
    }

    public void setNtnlty(String ntnlty) {
        this.ntnlty = ntnlty;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @ManyToOne
    @JoinColumn(name = "CARD_TYPE_ID")
    public TsSimpleCode getFkByCardTypeId() {
        return fkByCardTypeId;
    }

    public void setFkByCardTypeId(TsSimpleCode fkByCardTypeId) {
        this.fkByCardTypeId = fkByCardTypeId;
    }
    @Column(name = "UP_TAG" )
	public String getUpTag() {
		return upTag;
	}

	public void setUpTag(String upTag) {
		this.upTag = upTag;
	}
	@Column(name = "PRO_RSN" )
	public String getProRsn() {
		return proRsn;
	}

	public void setProRsn(String proRsn) {
		this.proRsn = proRsn;
	}
	@Column(name = "VUID" )
	public String getVuid() {
		return vuid;
	}

	public void setVuid(String vuid) {
		this.vuid = vuid;
	}
	@Column(name = "EMPI" )
	public String getEmpi() {
		return empi;
	}

	public void setEmpi(String empi) {
		this.empi = empi;
	}
	@Column(name = "KH" )
	public String getKh() {
		return kh;
	}

	public void setKh(String kh) {
		this.kh = kh;
	}
	@Column(name = "KLX" )
	public String getKlx() {
		return klx;
	}

	public void setKlx(String klx) {
		this.klx = klx;
	}
	@Column(name = "SFZS" )
	public String getSfzs() {
		return sfzs;
	}

	public void setSfzs(String sfzs) {
		this.sfzs = sfzs;
	}
	@Column(name = "ECCONTENT" )
	public String getEcContent() {
		return ecContent;
	}

	public void setEcContent(String ecContent) {
		this.ecContent = ecContent;
	}
}
