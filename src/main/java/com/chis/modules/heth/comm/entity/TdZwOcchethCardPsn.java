package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 职业卫生技术服务信息报送卡—参与人员
 *
 * <AUTHOR>
 * @createTime 2022-8-22
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_CARD_PSN")
@SequenceGenerator(name = "TdZwOcchethCardPsn", sequenceName = "TD_ZW_OCCHETH_CARD_PSN_SEQ", allocationSize = 1)
public class TdZwOcchethCardPsn implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwOcchethCard fkByMainId;
    private TdZwPsninfoComm fkByPsnId;
    private String psnName;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    /**
     * 参与人员（承担的服务事项）
     */
    private List<TdZwOcchethCardItem> occhethCardItemList = new ArrayList<>();
    private List<String> occhethCardItemSimpleCodeList = new ArrayList<>();
    private String occhethCardItemStr;

    /**是否来源手机端*/
    private boolean isApp;

    public TdZwOcchethCardPsn() {
    }

    public TdZwOcchethCardPsn(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethCardPsn")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwOcchethCard getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwOcchethCard fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "PSN_ID")
    public TdZwPsninfoComm getFkByPsnId() {
        return fkByPsnId;
    }

    public void setFkByPsnId(TdZwPsninfoComm fkByPsnId) {
        this.fkByPsnId = fkByPsnId;
    }

    @Column(name = "PSN_NAME")
    public String getPsnName() {
        return psnName;
    }

    public void setPsnName(String psnName) {
        this.psnName = psnName;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwOcchethCardItem> getOcchethCardItemList() {
        return occhethCardItemList;
    }

    public void setOcchethCardItemList(List<TdZwOcchethCardItem> occhethCardItemList) {
        this.occhethCardItemList = occhethCardItemList;
    }

    @Transient
    public List<String> getOcchethCardItemSimpleCodeList() {
        return occhethCardItemSimpleCodeList;
    }

    public void setOcchethCardItemSimpleCodeList(List<String> occhethCardItemSimpleCodeList) {
        this.occhethCardItemSimpleCodeList = occhethCardItemSimpleCodeList;
    }

    @Transient
    public String getOcchethCardItemStr() {
        return occhethCardItemStr;
    }

    public void setOcchethCardItemStr(String occhethCardItemStr) {
        this.occhethCardItemStr = occhethCardItemStr;
    }

    @Transient
    public boolean getIsApp() {
        return isApp;
    }
    public void setIsApp(boolean isApp) {
        this.isApp = isApp;
    }
}