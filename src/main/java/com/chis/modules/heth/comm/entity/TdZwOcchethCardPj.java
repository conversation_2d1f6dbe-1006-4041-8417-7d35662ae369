package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 职业卫生技术服务信息报送卡—职业病危害现状评价
 *
 * <AUTHOR>
 * @createTime 2022-8-22
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_CARD_PJ")
@SequenceGenerator(name = "TdZwOcchethCardPj", sequenceName = "TD_ZW_OCCHETH_CARD_PJ_SEQ", allocationSize = 1)
public class TdZwOcchethCardPj implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwOcchethCard fkByMainId;
    private TbYsjcLimitValComm fkByBadrsnId;
    private Integer postNum;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    /**超标岗位数*/
    private Integer num;
    /**
     * 职业病危害现状评价（超标岗位）
     */
    private List<TdZwOcchethCardPjSub> occhethCardPjSubList = new ArrayList<>();

    public TdZwOcchethCardPj() {
    }

    public TdZwOcchethCardPj(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethCardPj")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwOcchethCard getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwOcchethCard fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "BADRSN_ID")
    public TbYsjcLimitValComm getFkByBadrsnId() {
        return fkByBadrsnId;
    }

    public void setFkByBadrsnId(TbYsjcLimitValComm fkByBadrsnId) {
        this.fkByBadrsnId = fkByBadrsnId;
    }

    @Column(name = "POST_NUM")
    public Integer getPostNum() {
        return postNum;
    }

    public void setPostNum(Integer postNum) {
        this.postNum = postNum;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwOcchethCardPjSub> getOcchethCardPjSubList() {
        return occhethCardPjSubList;
    }

    public void setOcchethCardPjSubList(List<TdZwOcchethCardPjSub> occhethCardPjSubList) {
        this.occhethCardPjSubList = occhethCardPjSubList;
    }
    @Column(name = "NUM")
    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }
}