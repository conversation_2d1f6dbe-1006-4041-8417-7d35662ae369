package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2023-8-14
 */
@Entity
@Table(name = "TD_TJ_CRPT_TASK_SUB")
@SequenceGenerator(name = "TdTjCrptTaskSub", sequenceName = "TD_TJ_CRPT_TASK_SUB_SEQ", allocationSize = 1)
public class TdTjCrptTaskSub implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdTjCrptTask fkByMainId;
    private TbTjCrpt fkByStopCrptId;
    private String crptName;
    private String creditCode;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    public TdTjCrptTaskSub() {
    }

    public TdTjCrptTaskSub(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjCrptTaskSub")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdTjCrptTask getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdTjCrptTask fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "STOP_CRPT_ID")
    public TbTjCrpt getFkByStopCrptId() {
        return fkByStopCrptId;
    }

    public void setFkByStopCrptId(TbTjCrpt fkByStopCrptId) {
        this.fkByStopCrptId = fkByStopCrptId;
    }

    @Column(name = "CRPT_NAME")
    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    @Column(name = "CREDIT_CODE")
    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

}