package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-8-22
 */
@Entity
@Table(name = "TB_YSJC_JC_STD")
@SequenceGenerator(name = "TbYsjcJcStd", sequenceName = "TB_YSJC_JC_STD_SEQ", allocationSize = 1)
public class TbYsjcJcStdComm implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String stdNo;
	private String stdName;
	private Date pubDate;
	private Date stopDate;
	private Date implDate;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TbYsjcJcStdComm() {
	}

	public TbYsjcJcStdComm(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbYsjcJcStd")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "STD_NO")	
	public String getStdNo() {
		return stdNo;
	}

	public void setStdNo(String stdNo) {
		this.stdNo = stdNo;
	}	
			
	@Column(name = "STD_NAME")	
	public String getStdName() {
		return stdName;
	}

	public void setStdName(String stdName) {
		this.stdName = stdName;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "PUB_DATE")			
	public Date getPubDate() {
		return pubDate;
	}

	public void setPubDate(Date pubDate) {
		this.pubDate = pubDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "STOP_DATE")			
	public Date getStopDate() {
		return stopDate;
	}

	public void setStopDate(Date stopDate) {
		this.stopDate = stopDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "IMPL_DATE")			
	public Date getImplDate() {
		return implDate;
	}

	public void setImplDate(Date implDate) {
		this.implDate = implDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}