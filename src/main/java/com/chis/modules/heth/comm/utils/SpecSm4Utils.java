package com.chis.modules.heth.comm.utils;

import cn.hutool.crypto.symmetric.SM4;
import com.chis.common.utils.StringUtils;
import org.apache.commons.codec.binary.Hex;

/**
 * <p>类描述：职业/放射 卫生技术服务机构报送卡 生成质控二维码 特定的加密方式 </p>
 * pw 2025/5/22
 **/
public class SpecSm4Utils {
    private final static String SM4_KEY = secretKeyDecode(new Integer[]{15, 14, 4, 13, 4, 18, 13, 8, 21, 21, 17, 12, 13, 14, 20, 21, 24, 27, 21, 34, 25, 23, 30, 25, 30, 34, 31, 41, 34, 40, 43, 45});
    /**
     * <p>方法描述：特定加密 不适合通用 </p>
     * pw 2025/5/22
     **/
    public static String sm4JsEncrypt(String content, String sm4Key) {
        if (StringUtils.isBlank(content) || StringUtils.isBlank(sm4Key)) {
            return null;
        }
        try {
            return new SM4(Hex.decodeHex(sm4Key)).encryptHex(content);
        } catch (Exception e) {
            return content;
        }
    }

    /**
     * <p>方法描述：内置key 加密 </p>
     * pw 2025/5/22
     **/
    public static String sm4JsEncrypt(String content) {
        return sm4JsEncrypt(content, SM4_KEY);
    }

    /**
     * <p>方法描述：特殊的生成key方式</p>
     * pw 2025/5/22
     **/
    public static String secretKeyDecode(Integer[] secretKeyEncryptArray) {
        if (secretKeyEncryptArray == null || secretKeyEncryptArray.length == 0) return "";

        Integer[] decodedArray = new Integer[secretKeyEncryptArray.length];

        for (int i = 0, length = secretKeyEncryptArray.length; i < length; i++) {
            decodedArray[i] = secretKeyEncryptArray[i] - i;
        }

        StringBuilder decodedStr = new StringBuilder();
        for (Integer item : decodedArray) {
            decodedStr.append(Integer.toString(item, 16));
        }

        return decodedStr.toString();
    }
}
