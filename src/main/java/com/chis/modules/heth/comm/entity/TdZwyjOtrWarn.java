package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-11-16
 */
@Entity
@Table(name = "TD_ZWYJ_OTR_WARN")
@SequenceGenerator(name = "TdZwyjOtrWarn", sequenceName = "TD_ZWYJ_OTR_WARN_SEQ", allocationSize = 1)
public class TdZwyjOtrWarn implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsZone fkByCityZoneId;
	private Integer warnType;
	private TsUnit fkByBhkorgId;
	private TsZone fkByBhkorgZoneId;
	private String warnCont;
	private Date happenDate;
	private Integer otrPsns;
	private TsUnit fkByDealOrgId;
	private Integer ifOutRange;
	private String dealCont;
	private Date dealDate;
	private TsUnit fkByCheckOrgId;
	private Integer checkRst;
	private String checkCont;
	private Date checkDate;
	private Integer stateMark;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjOtrWarn() {
	}

	public TdZwyjOtrWarn(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjOtrWarn")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "CITY_ZONE_ID")			
	public TsZone getFkByCityZoneId() {
		return fkByCityZoneId;
	}

	public void setFkByCityZoneId(TsZone fkByCityZoneId) {
		this.fkByCityZoneId = fkByCityZoneId;
	}	
			
	@Column(name = "WARN_TYPE")	
	public Integer getWarnType() {
		return warnType;
	}

	public void setWarnType(Integer warnType) {
		this.warnType = warnType;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BHKORG_ID")			
	public TsUnit getFkByBhkorgId() {
		return fkByBhkorgId;
	}

	public void setFkByBhkorgId(TsUnit fkByBhkorgId) {
		this.fkByBhkorgId = fkByBhkorgId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BHKORG_ZONE_ID")			
	public TsZone getFkByBhkorgZoneId() {
		return fkByBhkorgZoneId;
	}

	public void setFkByBhkorgZoneId(TsZone fkByBhkorgZoneId) {
		this.fkByBhkorgZoneId = fkByBhkorgZoneId;
	}	
			
	@Column(name = "WARN_CONT")	
	public String getWarnCont() {
		return warnCont;
	}

	public void setWarnCont(String warnCont) {
		this.warnCont = warnCont;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "HAPPEN_DATE")			
	public Date getHappenDate() {
		return happenDate;
	}

	public void setHappenDate(Date happenDate) {
		this.happenDate = happenDate;
	}	
			
	@Column(name = "OTR_PSNS")	
	public Integer getOtrPsns() {
		return otrPsns;
	}

	public void setOtrPsns(Integer otrPsns) {
		this.otrPsns = otrPsns;
	}	
			
	@ManyToOne
	@JoinColumn(name = "DEAL_ORG_ID")			
	public TsUnit getFkByDealOrgId() {
		return fkByDealOrgId;
	}

	public void setFkByDealOrgId(TsUnit fkByDealOrgId) {
		this.fkByDealOrgId = fkByDealOrgId;
	}	
			
	@Column(name = "IF_OUT_RANGE")	
	public Integer getIfOutRange() {
		return ifOutRange;
	}

	public void setIfOutRange(Integer ifOutRange) {
		this.ifOutRange = ifOutRange;
	}	
			
	@Column(name = "DEAL_CONT")	
	public String getDealCont() {
		return dealCont;
	}

	public void setDealCont(String dealCont) {
		this.dealCont = dealCont;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DEAL_DATE")			
	public Date getDealDate() {
		return dealDate;
	}

	public void setDealDate(Date dealDate) {
		this.dealDate = dealDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CHECK_ORG_ID")			
	public TsUnit getFkByCheckOrgId() {
		return fkByCheckOrgId;
	}

	public void setFkByCheckOrgId(TsUnit fkByCheckOrgId) {
		this.fkByCheckOrgId = fkByCheckOrgId;
	}	
			
	@Column(name = "CHECK_RST")	
	public Integer getCheckRst() {
		return checkRst;
	}

	public void setCheckRst(Integer checkRst) {
		this.checkRst = checkRst;
	}	
			
	@Column(name = "CHECK_CONT")	
	public String getCheckCont() {
		return checkCont;
	}

	public void setCheckCont(String checkCont) {
		this.checkCont = checkCont;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "CHECK_DATE")			
	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}	
			
	@Column(name = "STATE_MARK")	
	public Integer getStateMark() {
		return stateMark;
	}

	public void setStateMark(Integer stateMark) {
		this.stateMark = stateMark;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}