package com.chis.modules.heth.comm.web;

import com.chis.common.bean.FastReportData;
import com.chis.common.bean.FastReportDataRef;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.PropertyUtils;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IFastReport;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.web.FacesEditBean;
import com.chis.modules.system.web.FastReportBean;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description : 报告卡上报基类
 * @ClassAuthor : anjing
 * @Date : 2020/7/21 17:00
 **/
public class AbstractReportCardCommBean extends FacesEditBean implements IProcessData, IFastReport {

    /**配置文件中报告打印日期之后的数据*/
    protected String bhkBeginDate;
    /**查询条件：状态*/
    protected String[] states;
    protected List<SelectItem> stateList;
    protected  TsZone zone;
    protected SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    /**审核级别*/
    protected String checkLevel;

    public AbstractReportCardCommBean() {
        zone = this.sessionData.getUser().getTsUnit().getTsZone();
        /**获取配置文件：上报审核数据开始日期*/
        this.bhkBeginDate = PropertyUtils.getValue("reportCard.bhkBeginDate");
        /*审核等级*/
        checkLevel=PropertyUtils.getValue("checkLevel");
        // 状态初始化
        this.initState();
    }

    /**
     * @Description : 状态初始化
     * @MethodAuthor: anjing
     * @Date : 2020/7/21 17:36
     **/
    private void initState() {
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem("0.5", "待填报"));
        this.stateList.add(new SelectItem("0", "待提交"));
        this.stateList.add(new SelectItem("2,8", "已退回"));

        this.stateList.add(new SelectItem("1,3,9,5,6,7", "已提交"));

        this.states = new String[] { "0.5", "0", "2,8"};
    }

    @Override
    public FastReportBean getFastReportBean() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public List<FastReportData> supportFastReportDataSet() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public List<FastReportDataRef> supportFastReportDataRef() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public void processData(List<?> list) {
        // TODO Auto-generated method stub
    }

    @Override
    public void addInit() {
        // TODO Auto-generated method stub
    }

    @Override
    public void viewInit() {
        // TODO Auto-generated method stub
    }

    @Override
    public void modInit() {
        // TODO Auto-generated method stub
    }

    @Override
    public void saveAction() {
        // TODO Auto-generated method stub
    }

    @Override
    public String[] buildHqls() {
        // TODO Auto-generated method stub
        return new String[0];
    }

    public String getBhkBeginDate() {
        return bhkBeginDate;
    }

    public void setBhkBeginDate(String bhkBeginDate) {
        this.bhkBeginDate = bhkBeginDate;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public TsZone getZone() {
        return zone;
    }
    public void setZone(TsZone zone) {
        this.zone = zone;
    }

    public String getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(String checkLevel) {
        this.checkLevel = checkLevel;
    }
}
