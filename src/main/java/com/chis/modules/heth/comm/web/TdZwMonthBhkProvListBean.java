package com.chis.modules.heth.comm.web;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.heth.comm.entity.TdZwMonthBhkProv;
import com.chis.modules.heth.comm.entity.TdZwMonthOrg;
import com.chis.modules.heth.comm.entity.TdZwMonthOrgBadrsns;
import com.chis.modules.heth.comm.service.TdZwMonthBhkProvServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.*;

/**
 * <p>方法描述：职业健康检查常规监测月度汇总</p>
 *
 * @MethodAuthor hsj 2024-08-09 8:59
 */
@ManagedBean(name = "tdZwMonthBhkProvListBean")
@ViewScoped
public class TdZwMonthBhkProvListBean extends FacesEditBean implements IProcessData {

    private static final long serialVersionUID = 3495004620646343505L;
    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final TdZwMonthBhkProvServiceImpl tdZwMonthBhkProvService = SpringContextHolder.getBean(TdZwMonthBhkProvServiceImpl.class);

    private TsZone zone;
    private List<TsSimpleCode> analyBadRsnList;
    /******************查询条件****************************/
    //地区
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
    //职业健康检查机构
    private String searchUnitId;
    private String searchUnitName;

    //报告出具日期
    private Date startBhkDate;
    private Date endBhkDate;
    //状态
    private Integer[] searchState;
    private List<SelectItem> stateList;

    /******************查询条件****************************/

    //业务主表id

    private Integer rid;
    private String startDate;
    private String endDate;
    //业务主表对象
    private TdZwMonthBhkProv monthBhkProv;
    /***********************添加****************************/
    private List<SelectItem> selYearList;
    private List<SelectItem> selMonthList;

    /***********************添加****************************/
    /***********************修改****************************/
    //机构对象
    private List<Object[]> orgList;
    private Object[] orgObj;
    //地区
    private String editZoneCode;
    private String editZoneName;
    //职业健康检查机构
    private String editUnitId;
    private String editUnitName;
    //状态
    private Integer[] editState;
    private List<SelectItem> editStateList;
    //各危害因素汇总表格
    private List<TdZwMonthOrgBadrsns> orgBadrsns;
    private String orgName;

    /***********************详情****************************/
    // 导出数据
    private List<Object[]> exportDataList;

    /**
     * <p>方法描述：初始化</p>
     *
     * @MethodAuthor hsj 2024-08-08 11:40
     */

    public TdZwMonthBhkProvListBean() {
        this.ifSQL = Boolean.TRUE;
        this.monthBhkProv = new TdZwMonthBhkProv();
        this.initZone();
        this.initDate();
        this.initState();
        this.initSimple();
        this.initYear();
        this.searchAction();
    }

    /**
     * <p>方法描述：初始化年份</p>
     *
     * @MethodAuthor hsj 2024-08-09 14:24
     */
    private void initYear() {
        // 添加-年选择 近5年
        this.selYearList = new ArrayList<>();
        int nowYear = DateUtil.year(new Date());
        for (int i = 0; i < 5; i++) {
            int year = nowYear - i;
            this.selYearList.add(new SelectItem(year, year + "年"));
        }
        this.selMonthList = new ArrayList<>();
    }

    /**
     * <p>方法描述：初始化码表</p>
     *
     * @MethodAuthor hsj 2024-08-09 14:24
     */
    private void initSimple() {
        this.analyBadRsnList = this.commService.findSimpleCodeListOrderByNumNo("5547");
    }

    /**
     * <p>方法描述：报告出具日期默认上一年度11月16号</p>
     *
     * @MethodAuthor hsj 2024-08-08 11:44
     */
    private void initDate() {
        this.startBhkDate = DateUtils.getFixedDate(-1, 11, 16);
    }

    /**
     * <p>方法描述：初始化区域方法：登录者所在的管辖地区</p>
     *
     * @MethodAuthor hsj 2024-08-08 11:40
     */
    private void initZone() {
        this.zone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == this.zone || null == this.zone.getRid()) {
            this.zone = Global.getUser().getTsUnit().getTsZone();
        }
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(this.zone.getZoneGb(), true, "", "");
    }


    /**
     * <p>方法描述：状态初始化</p>
     *
     * @MethodAuthor hsj 2024-08-08 11:41
     */
    private void initState() {
        //状态, 默认待提交
        this.searchState = new Integer[]{0};
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem(0, "待提交"));
        this.stateList.add(new SelectItem(1, "已提交"));
        this.editStateList = new ArrayList<>();
        this.editStateList.add(new SelectItem(0, "未提交"));
        this.editStateList.add(new SelectItem(1, "已提交"));
    }

    /**
     * <p>方法描述：查询页面选择机构弹出框</p>
     *
     * @MethodAuthor hsj 2024-08-08 13:30
     */
    public void selUnitAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 625, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add(this.searchUnitId);
        paramMap.put("selectIds", paramList);
        List<String> paramList2 = new ArrayList<>();
        //若地区为空时，默认为管辖地区
        String zoneGb = StringUtils.isNotBlank(this.searchZoneCode) ? this.searchZoneCode : this.zone.getZoneCode();
        paramList2.add(zoneGb);
        paramMap.put("searchZoneCode", paramList2);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
    }

    /**
     * <p>方法描述：修改详情-查询页面选择机构弹出框</p>
     *
     * @MethodAuthor hsj 2024-08-08 13:30
     */
    public void selEditUnitAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 625, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add(this.editUnitId);
        paramMap.put("selectIds", paramList);
        List<String> paramList2 = new ArrayList<>();
        paramList2.add(this.editZoneCode);
        paramMap.put("searchZoneCode", paramList2);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
    }

    /**
     * <p>方法描述：选择机构</p>
     *
     * @MethodAuthor hsj 2024-08-08 13:31
     */
    public void onSelectUnitAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null == selectedMap || selectedMap.size() == 0) {
            return;
        }
        List<TbTjSrvorg> list = (List<TbTjSrvorg>) selectedMap.get("selectPros");
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> names = new ArrayList<>();
        List<String> ids = new ArrayList<>();
        for (TbTjSrvorg t : list) {
            names.add(t.getUnitName());
            ids.add(Convert.toStr(t.getRid()));
        }
        this.searchUnitName = StringUtils.list2string(names, "，");
        this.searchUnitId = StringUtils.list2string(ids, ",");
    }

    /**
     * <p>方法描述：修改/详情-选择机构</p>
     *
     * @MethodAuthor hsj 2024-08-08 13:31
     */
    public void onEditSelectUnitAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null == selectedMap || selectedMap.size() == 0) {
            return;
        }
        List<TbTjSrvorg> list = (List<TbTjSrvorg>) selectedMap.get("selectPros");
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> names = new ArrayList<>();
        List<String> ids = new ArrayList<>();
        for (TbTjSrvorg t : list) {
            names.add(t.getUnitName());
            ids.add(Convert.toStr(t.getRid()));
        }
        this.editUnitName = StringUtils.list2string(names, "，");
        this.editUnitId = StringUtils.list2string(ids, ",");
    }

    /**
     * <p>方法描述：机构清除</p>
     *
     * @MethodAuthor hsj 2024-08-08 13:34
     */
    public void clearUnit() {
        this.searchUnitName = null;
        this.searchUnitId = null;
    }

    /**
     * <p>方法描述：修改页面-机构清除</p>
     *
     * @MethodAuthor hsj 2024-08-08 13:34
     */
    public void clearEditUnit() {
        this.editUnitName = null;
        this.editUnitId = null;
    }

    /**
     * @param list
     * <AUTHOR>
     */
    @Override
    public void processData(List<?> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Object[]> result = (List<Object[]>) list;
        List<String> startDateStrList = new ArrayList<>();
        for (Object[] o : result) {
            if ("1".equals(Convert.toStr(o[6])) || null == o[1]) {
                continue;
            }
            startDateStrList.add(Convert.toStr(o[1]));
        }
        //key: 报告出具日期开始时间+&+报告出具日期结束时间，value: 统计数量
        Map<String, Object[]> map = this.tdZwMonthBhkProvService.findOrgNumBySatrtDate(startDateStrList, this.zone);
        for (Object[] o : result) {
            if ("1".equals(Convert.toStr(o[6]))) {
                continue;
            }
            String key = Convert.toStr(o[1]) + "&" + Convert.toStr(o[2]);
            if (!map.containsKey(key)) {
                o[3] = 0;
                o[4] = 0;
                continue;
            }
            Object[] objMap = map.get(key);
            o[3] = objMap[2];
            o[4] = objMap[3];
        }
    }

    /**
     * <AUTHOR>
     */
    @Override
    public void addInit() {
        RequestContext context = RequestContext.getCurrentInstance();
        if (this.monthBhkProv.getSelYear() == null) {
            JsfUtil.addErrorMessage("请选择报告出具周期年！");
            return;
        }
        if (this.monthBhkProv.getSelMonth() == null) {
            JsfUtil.addErrorMessage("请选择报告出具周期月！");
            return;
        }
        Integer count = this.tdZwMonthBhkProvService.findCountByStartDate(this.monthBhkProv.getStartBhkDate(), this.monthBhkProv.getEndBhkDate());
        if (count > 0) {
            JsfUtil.addErrorMessage("相同报告出具日期已汇总，不可重复汇总！");
            return;
        }
        context.execute("PF('AddDialog').hide();");
        try {
            this.tdZwMonthBhkProvService.upsertEntity(this.monthBhkProv);
            this.rid = this.monthBhkProv.getRid();
            this.startDate = DateUtil.format(this.monthBhkProv.getStartBhkDate(), "yyyy-MM-dd");
            this.endDate = DateUtil.format(this.monthBhkProv.getEndBhkDate(), "yyyy-MM-dd");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("汇总失败！");
            return;
        }
        this.searchAction();
        this.modInitAction();
        context.update("tabView");
    }

    public void addBeforAction() {
        this.monthBhkProv = new TdZwMonthBhkProv();
        this.monthBhkProv.setState(0);
        this.monthBhkProv.setFillDate(new Date());
        this.monthBhkProv.setDelMark(0);
        this.monthBhkProv.setFkByOrgId(Global.getUser().getTsUnit());
        this.monthBhkProv.setSelYear(DateUtil.year(new Date()));
        this.onAddDialogYearChange();
        RequestContext.getCurrentInstance().execute("PF('AddDialog').show();");
    }

    /**
     * 添加-年change事件
     */
    public void onAddDialogYearChange() {
        this.selMonthList = new ArrayList<>();
        Date date = new Date();
        Integer year = DateUtil.year(date);
        // 往年可选12个月
        int maxMonth = 12;
        if (year.equals(this.monthBhkProv.getSelYear())) {
            int month = DateUtil.month(date) + 1;
            // 当年且当天大于15号可选1月-当月 否则可选1月-上月，若1月则不可选
            if (DateUtil.dayOfMonth(date) > 15) {
                maxMonth = month;
            } else {
                maxMonth = month - 1;
            }
        }
        for (int i = 1; i < maxMonth + 1; i++) {
            this.selMonthList.add(new SelectItem(i, i + "月"));
        }
        this.monthBhkProv.setSelMonth(null);
        this.onAddDialogMonthChange();
    }

    /**
     * 添加-月份change事件
     */
    public void onAddDialogMonthChange() {
        if (this.monthBhkProv.getSelMonth() == null) {
            this.monthBhkProv.setStartBhkDate(null);
            this.monthBhkProv.setEndBhkDate(null);
            return;
        }
        int year = this.monthBhkProv.getSelYear();
        int month = this.monthBhkProv.getSelMonth();
        String startDateStr;
        if (month == 1) {
            startDateStr = (year - 1) + "-12-16";
        } else {
            startDateStr = year + "-" + (month - 1) + "-16";
        }
        String endDateStr = year + "-" + month + "-15";
        this.monthBhkProv.setStartBhkDate(DateUtil.parse(startDateStr, "yyyy-MM-dd"));
        this.monthBhkProv.setEndBhkDate(DateUtil.parse(endDateStr, "yyyy-MM-dd"));
    }


    /**
     * <AUTHOR>
     */
    @Override
    public void viewInit() {
        //表格数据要还原
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("tabView:viewForm:orgTable");
        dataTable.setFirst(0);
        dataTable.setRows(10);
        //查询条件要还原
        this.restoreCondition();
        this.searchViewAction();
    }

    @Override
    public void modInit() {
        //表格数据要还原
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("tabView:editForm:orgTable");
        dataTable.setFirst(0);
        dataTable.setRows(10);
        //查询条件要还原
        this.restoreCondition();
        this.searchEditAction();
    }

    /**
     * <p>方法描述：二级页面查询条件还原</p>
     *
     * @MethodAuthor hsj 2024-08-12 9:00
     */
    private void restoreCondition() {
        this.editZoneCode = this.zone.getZoneGb();
        this.editZoneName = this.zone.getZoneName();
        this.editUnitId = null;
        this.editUnitName = null;
        this.editState = null;
        this.orgObj = null;//默认选中的值需清空
    }

    /**
     * <p>方法描述：修改页面查询</p>
     *
     * @MethodAuthor hsj 2024-08-09 17:27
     */
    public void searchEditAction() {
        this.orgName = null;
        this.orgBadrsns = new LinkedList<>();
        this.orgList = this.tdZwMonthBhkProvService.findTdZwMonthOrgListByDate(this.startDate, this.endDate, this.editZoneCode, this.editUnitId, this.editState);
        if (CollectionUtils.isEmpty(this.orgList)) {
            return;
        }
        this.initSelectOrgObj();
        this.initOrgBadrsns("1");
    }

    /**
     * <p>方法描述：初始化选择的对象
     * 该方法用于从列表中选择一个对象作为当前操作的对象
     * 如果当前的orgObj为空，则直接选择列表中的第一个元素
     * 如果当前的orgObj不为空，则遍历列表，寻找与当前orgObj的第一个元素值相同的对象
     * 如果找到，则将其设置为当前操作的对象，否则仍选择列表中的第一个元素</p>
     *
     * @MethodAuthor hsj 2024-08-13 14:26
     */
    private void initSelectOrgObj() {
        if (null == this.orgObj) {
            this.orgObj = this.orgList.get(0);
            return;
        }
        for (Object[] obj : this.orgList) {
            if (Convert.toStr(this.orgObj[0]).equals(Convert.toStr(obj[0]))) {
                this.orgObj = obj;
                return;
            }
        }
        this.orgObj = this.orgList.get(0);
    }

    /**
     * <p>方法描述：详情页面查询</p>
     *
     * @MethodAuthor hsj 2024-08-09 17:28
     */
    public void searchViewAction() {
        this.orgName = null;
        this.orgBadrsns = new LinkedList<>();
        this.orgList = this.tdZwMonthBhkProvService.findTdZwMonthOrgByMainId(this.rid, this.editZoneCode, this.editUnitId);
        if (CollectionUtils.isEmpty(this.orgList)) {
            return;
        }
        this.initSelectOrgObj();
        this.initOrgBadrsns("2");
    }

    /**
     * 导出数据前的处理函数。
     * 该函数用于在导出数据前检查查询条件职业卫生技术服务机构是否为空以及是否有符合查询条件的数据。
     * 如果没有数据，则提示用户；如果有数据，则触发导出文件的下载。
     */
    public void exportBefore() {
        RequestContext context = RequestContext.getCurrentInstance();
        this.exportDataList = this.executeExportSql();
        if (CollectionUtils.isEmpty(this.exportDataList)) {
            context.execute("zwx_loading_stop()");
            JsfUtil.addErrorMessage("无可导出的数据！");
            return;
        }
        context.execute("getDownloadFileClick();");
    }

    public String buildExportSqls(int type, Map<String, Object> paramMap) {
        StringBuilder sb = new StringBuilder();
        if (type == 1) {
            sb.append(" SELECT COUNT(1) ");
        } else {
            sb.append(" SELECT T1.FULL_NAME,T.UNIT_NAME,T2.STATE,T.RID ");
        }
        sb.append("       FROM TB_TJ_SRVORG T ")
                .append(" INNER JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID ")
                .append(" LEFT JOIN TD_ZW_MONTH_BHK T2 ON T2.ORG_ID = T.RID AND TO_CHAR(T2.START_BHK_DATE,'YYYY-MM-DD') = :startDate AND TO_CHAR(T2.END_BHK_DATE,'YYYY-MM-DD') = :endDate AND NVL(T2.DEL_MARK,0) = 0 ")
                .append(" WHERE T.STOP_TAG=1 AND NVL(T2.STATE,0) = 0 ")
                .append(" AND T1.ZONE_GB LIKE :zone ");
        paramMap.put("zone", ZoneUtil.zoneSelect(this.zone.getZoneGb()) + "%");
        paramMap.put("startDate", this.startDate);
        paramMap.put("endDate", this.endDate);

        if (2 == type) {
            sb.append(" GROUP BY T1.FULL_NAME,T.UNIT_NAME,T2.STATE,T.RID,T1.ZONE_GB ");
            sb.append(" ORDER BY T1.ZONE_GB, T.UNIT_NAME ");
        }
        return sb.toString();
    }

    /**
     * 导出体检报告抽取数据为Excel文件。
     *
     * @return 返回DefaultStreamedContent对象，用于浏览器下载Excel文件。返回null表示导出失败。
     * 文件名为"体检报告抽取.xlsx"，编码为UTF-8，内容包含体检报告抽取相关数据。
     */
    public DefaultStreamedContent export() {
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("showStatus();");
        String excelTitle = "未报告职业健康检查机构名单（" + this.startDate + "~" + this.endDate + "）";
        String[] excelHeaders = new String[]{"地区", "职业健康检查机构名称","状态"};

        Integer[] columnWidths = new Integer[]{20, 50};
        List<ExcelExportObject[]> excelExportObjects = new ArrayList<>();
        ExcelExportUtil excelExportUtil = new ExcelExportUtil(excelTitle, excelHeaders, excelExportObjects);
        excelExportUtil.setFrozenPaneRowsNum(2);
        excelExportUtil.setColumnWidths(columnWidths);
        excelExportUtil.setSheetName("未报告职业健康检查机构名单");
        try {
            this.pakExcelExportDataList(excelExportObjects, this.exportDataList, excelHeaders.length);
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("文件导出错误！");
            return null;
        }
        Workbook wb = excelExportUtil.exportExcel();
        if (wb != null) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                String fileName = excelTitle + ".xlsx";
                fileName = URLEncoder.encode(fileName, "UTF-8");
                wb.write(baos);
                baos.flush();
                byte[] aa = baos.toByteArray();
                context.execute("hideStatus();");
                return new DefaultStreamedContent(
                        new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName
                );
            } catch (Exception e) {
                JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            }
        }
        context.execute("hideStatus();");
        return null;
    }

    /**
     * 执行导出SQL查询，获取并处理数据，用于报表生成。
     *
     * @return 返回一个包含查询结果的Object数组列表。
     */
    private List<Object[]> executeExportSql() {
        Map<String, Object> paramMap = new HashMap<>();
        String sql = this.buildExportSqls(2, paramMap);
        List<Object[]> list =
                CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql, paramMap));
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        List<Integer> ridList = new ArrayList<>();
        for (Object[] objects : list) {
            String fullName = StringUtils.objectToString(objects[0]);
            objects[0] = ZoneUtil.removeProvByFullName(fullName);
            Integer state = null == objects[2] ? null : Integer.parseInt(objects[2].toString());
            Integer rid = null == objects[3] ? null : Integer.parseInt(objects[3].toString());
            objects[2] = null != state && 0 == state ? "未提交" : "未填报";
            if (null == state && null != rid) {
                ridList.add(rid);
            }
        }
        if (CollectionUtils.isEmpty(ridList)) {
            return list;
        }
        List<Integer> filterList = this.filterExportSrvorgHasData(ridList);
        List<Object[]> removeList = new ArrayList<>();
        for (Object[] objects : list) {
            Integer rid = null == objects[3] ? null : Integer.parseInt(objects[3].toString());
            boolean flag = null == rid || !ridList.contains(rid) || filterList.contains(rid);
            if (flag) {
                continue;
            }
            removeList.add(objects);
        }
        if (!CollectionUtils.isEmpty(removeList)) {
            list.removeAll(removeList);
        }
        return list;
    }

    /**
     * 将数据列表打包为Excel导出对象列表。
     *
     * @param excelExportObjectList 导出对象列表。
     * @param dataList              数据列表，每个元素是一个对象数组，代表一行数据。
     * @param headerSize            Excel表格的头部大小，用于确定每个数据行中导出对象的数量。
     */
    private void pakExcelExportDataList(List<ExcelExportObject[]> excelExportObjectList,
                                        List<Object[]> dataList, int headerSize) {
        // 检查数据列表和头部大小是否有效，如果无效则返回空列表
        if (ObjectUtil.isEmpty(dataList) || headerSize <= 0) {
            return;
        }

        for (Object[] data : dataList) {
            ExcelExportObject[] objects = new ExcelExportObject[headerSize];
            objects[0] = new ExcelExportObject(StringUtils.objectToString(data[0]));
            objects[1] = new ExcelExportObject(StringUtils.objectToString(data[1]));
            objects[2] = new ExcelExportObject(StringUtils.objectToString(data[2]));
            excelExportObjectList.add(objects);
        }
    }

    @Override
    public void saveAction() {

    }

    /**
     * <p>方法描述：列表查询</p>
     *
     * @MethodAuthor hsj 2024-08-08 13:42
     */
    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder();
        String state = "2";//默认查询所有
        if (ObjectUtil.isNotEmpty(this.searchState) && this.searchState.length == 1) {
            state = Convert.toStr(this.searchState[0]);
        }
        sb.append(" FROM  TD_ZW_MONTH_BHK_PROV T WHERE  NVL(T.DEL_MARK,0) =0 ");
        sb.append(" AND T.ORG_ID = ").append(Global.getUser().getTsUnit().getRid());
        if (null != this.endBhkDate) {
            sb.append(" AND T.START_BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(this.endBhkDate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.startBhkDate) {
            sb.append(" AND T.END_BHK_DATE  >=  TO_DATE('").append(DateUtils.formatDate(this.startBhkDate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (!"2".equals(state)) {
            sb.append(" AND T.STATE =  ").append(state);
        }
        //地区为空时，无需关联查询
        if (StringUtils.isNotBlank(this.searchZoneCode) || StringUtils.isNotBlank(this.searchUnitId)) {
            sb.append(" AND ( ");
            if ("2".equals(state) || "1".equals(state)) {
                sb.append(" EXISTS (SELECT 1 FROM  TD_ZW_MONTH_ORG T1 LEFT JOIN TB_TJ_SRVORG T2 ON T1.ORG_ID =T2.RID  ");
                sb.append(" LEFT JOIN TS_ZONE T3 ON T2.ZONE_ID = T3.RID  WHERE T1.MAIN_ID = T.RID AND T.STATE = 1 ");
                this.getConditionSql(sb);
                sb.append(" )  ");
            }
            if ("2".equals(state)) {
                sb.append(" OR ");
            }
            if ("2".equals(state) || "0".equals(state)) {
                sb.append(" EXISTS (SELECT 1 FROM  TD_ZW_MONTH_BHK T1 LEFT JOIN TB_TJ_SRVORG T2 ON T1.ORG_ID =T2.RID  ");
                sb.append(" LEFT JOIN TS_ZONE T3 ON T2.ZONE_ID = T3.RID WHERE T1.START_BHK_DATE = T.START_BHK_DATE AND T1.END_BHK_DATE = T.END_BHK_DATE ");
                sb.append(" AND T.STATE = 0 AND NVL(T1.DEL_MARK,0)  = 0  ");
                this.getConditionSql(sb);
                sb.append(" ) ");
            }
            sb.append(" ) ");
        }
        String h1 = " SELECT T.rid,TO_CHAR(T.START_BHK_DATE,'yyyy-MM-dd'),TO_CHAR(T.END_BHK_DATE,'yyyy-MM-dd'),T.ORG_NUM,T.ORG_SUBMIT_NUM,T.FILL_DATE,NVL(T.STATE,0)"
                + sb + "ORDER BY T.START_BHK_DATE DESC ";
        String h2 = " SELECT COUNT(*) " + sb;
        return new String[]{h1, h2};
    }

    /**
     * <p>方法描述：地区和机构查询条件抽取</p>
     *
     * @MethodAuthor hsj 2024-08-12 9:05
     */
    private void getConditionSql(StringBuilder sb) {
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sb.append("AND T3.ZONE_GB LIKE :zoneCode  ");
            this.paramMap.put("zoneCode", ZoneUtil.zoneSelect(this.searchZoneCode) + "%");
        }
        if (StringUtils.isNotBlank(this.searchUnitId)) {
            sb.append("AND T1.ORG_ID IN (:unitId) ");
            this.paramMap.put("unitId", StringUtils.string2list(this.searchUnitId, ","));
        }
    }

    /**
     * <p>方法描述：删除前验证</p>
     *
     * @MethodAuthor hsj 2024-08-09 11:39
     */
    public void deleteBeforeAction() {
        if (null == this.rid) {
            return;
        }
        String state = this.tdZwMonthBhkProvService.findTdZwMonthBhkProvState(this.rid);
        if (!"0".equals(state)) {
            JsfUtil.addErrorMessage("当前状态不可删除，请刷新页面！");
        }
        RequestContext.getCurrentInstance().execute("PF('DeleteDialog').show();");
    }

    /**
     * <p>方法描述：标删</p>
     *
     * @MethodAuthor hsj 2024-08-09 11:43
     */
    public void deleteAction() {
        try {
            this.tdZwMonthBhkProvService.deleteTdZwMonthBhkProv(this.rid);
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("删除失败！");
            e.printStackTrace();
        }
        this.searchAction();
    }

    /**
     * <p>方法描述：修改页面行点击</p>
     *
     * @MethodAuthor hsj 2024-08-09 10:49
     */
    public void onRowEditSelect(SelectEvent event) {
        this.orgName = null;
        this.orgObj = (Object[]) event.getObject();
        if (CollectionUtils.isEmpty(this.orgBadrsns)) {
            return;
        }
        this.initOrgBadrsns("1");

    }

    /**
     * <p>方法描述：危害因素统计查询</p>
     *
     * @MethodAuthor hsj 2024-08-09 17:49
     */
    private void initOrgBadrsns(String type) {
        String mianId = Convert.toStr(this.orgObj[0]);
        String name = Convert.toStr(this.orgObj[2]);
        this.orgBadrsns = new LinkedList<>();
        if (CollectionUtils.isEmpty(this.orgList)) {
            return;
        }
        this.orgName = name;
        switch (type) {
            case "1":
                // 初始化选中
                DataTable editDataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("tabView:editForm:orgTable");
                editDataTable.setSelection(this.orgObj);
                if (CollectionUtils.isEmpty(this.analyBadRsnList)) {
                    return;
                }
                Map<Integer, Object[]> orgBadrsnObj = this.tdZwMonthBhkProvService.findOrgBadrsnObjByMainId(mianId);
                for (TsSimpleCode t : this.analyBadRsnList) {
                    if (!orgBadrsnObj.containsKey(t.getRid())) {
                        continue;
                    }
                    TdZwMonthOrgBadrsns tdZwMonthOrgBadrsns = new TdZwMonthOrgBadrsns();
                    tdZwMonthOrgBadrsns.setFkByBadrsnId(t);
                    Object[] obj = orgBadrsnObj.get(t.getRid());
                    tdZwMonthOrgBadrsns.setHoldCardNum(Convert.toInt(obj[1]));
                    tdZwMonthOrgBadrsns.setBhkNum(Convert.toInt(obj[2]));
                    tdZwMonthOrgBadrsns.setSuspectedNum(Convert.toInt(obj[3]));
                    tdZwMonthOrgBadrsns.setContraindlistNum(Convert.toInt(obj[4]));
                    this.orgBadrsns.add(tdZwMonthOrgBadrsns);
                }
                RequestContext.getCurrentInstance().update("tabView:editForm:orgName");
                RequestContext.getCurrentInstance().update("tabView:editForm:orgBadrsnsTable");
                break;
            case "2":
                // 初始化选中
                DataTable viewDataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("tabView:viewForm:orgTable");
                viewDataTable.setSelection(this.orgObj);
                this.orgBadrsns = this.tdZwMonthBhkProvService.findTdZwMonthOrgBadrsnsByMainId(Convert.toInt(mianId));
                RequestContext.getCurrentInstance().update("tabView:viewForm:orgName");
                RequestContext.getCurrentInstance().update("tabView:viewForm:orgBadrsnsTable");
                break;
            default:
                break;
        }

    }

    /**
     * <p>方法描述：详情页面行点击</p>
     *
     * @MethodAuthor hsj 2024-08-09 10:49
     */
    public void onRowViewSelect(SelectEvent event) {
        this.orgName = null;
        this.orgObj = (Object[]) event.getObject();
        this.initOrgBadrsns("2");
    }

    /**
     * <p>方法描述：提交前验证</p>
     *
     * @MethodAuthor hsj 2024-08-09 11:49
     */
    public void beforeSubmit() {
        String state = this.tdZwMonthBhkProvService.findTdZwMonthBhkProvState(this.rid);
        if (!"0".equals(state)) {
            JsfUtil.addErrorMessage("当前状态不可提交，请刷新页面！");
        }
        List<Object[]> monthBhks = this.tdZwMonthBhkProvService.findTdZwMonthOrgListByDate(this.startDate, this.endDate, this.zone.getZoneGb(), null, null);
        if (CollectionUtils.isEmpty(monthBhks)) {
            JsfUtil.addErrorMessage("至少有一家职业健康检查机构！");
            return;
        }
        List<Integer> orgIds = new ArrayList<>();
        //是否存在未提交的数据
        boolean isSubmit = false;
        for (Object[] t : monthBhks) {
            if ("0".equals(Convert.toStr(t[3]))) {
                isSubmit = true;
                break;
            }
            orgIds.add(Convert.toInt(t[0]));
        }
        if (isSubmit) {
            JsfUtil.addErrorMessage("存在未提交的记录，请核实！");
            return;
        }
        Map<Integer, Map<Integer, Object[]>> orgBadrsnObj = this.tdZwMonthBhkProvService.findOrgBadrsnObjByMainIds(orgIds);
        this.monthBhkProv = this.tdZwMonthBhkProvService.findTdZwMonthBhkProvByMainId(this.rid);
        this.monthBhkProv.setOrgNum(monthBhks.size());
        this.monthBhkProv.setOrgSubmitNum(monthBhks.size());
        this.monthBhkProv.setSelOrgIds(orgIds);
        this.monthBhkProv.setTdZwMonthOrgs(new ArrayList<TdZwMonthOrg>());
        for (Object[] t : monthBhks) {
            TdZwMonthOrg tdZwMonthOrg = new TdZwMonthOrg();
            tdZwMonthOrg.setFkByMainId(this.monthBhkProv);
            TbTjSrvorg org = new TbTjSrvorg();
            org.setRid(Convert.toInt(t[4]));
            tdZwMonthOrg.setFkByOrgId(org);
            tdZwMonthOrg.setCreateDate(new Date());
            tdZwMonthOrg.setCreateManid(Global.getUser().getRid());
            tdZwMonthOrg.setTdZwMonthOrgBadrsns(new ArrayList<TdZwMonthOrgBadrsns>());
            Map<Integer, Object[]> badrsnMap = !orgBadrsnObj.containsKey(Convert.toInt(t[0])) ? new HashMap<Integer, Object[]>() : orgBadrsnObj.get(Convert.toInt(t[0]));
            for (TsSimpleCode code : this.analyBadRsnList) {
                if (!badrsnMap.containsKey(code.getRid())) {
                    continue;
                }
                TdZwMonthOrgBadrsns badrsn = new TdZwMonthOrgBadrsns();
                badrsn.setFkByMainId(tdZwMonthOrg);
                badrsn.setFkByBadrsnId(code);
                badrsn.setCreateDate(new Date());
                badrsn.setCreateManid(Global.getUser().getRid());
                Object[] badrsnObj = badrsnMap.get(code.getRid());
                badrsn.setHoldCardNum(Convert.toInt(badrsnObj[2]));
                badrsn.setBhkNum(Convert.toInt(badrsnObj[3]));
                badrsn.setSuspectedNum(Convert.toInt(badrsnObj[4]));
                badrsn.setContraindlistNum(Convert.toInt(badrsnObj[5]));
                tdZwMonthOrg.getTdZwMonthOrgBadrsns().add(badrsn);
            }
            this.monthBhkProv.getTdZwMonthOrgs().add(tdZwMonthOrg);
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show()");

    }

    /**
     * <p>方法描述：提交</p>
     *
     * @MethodAuthor hsj 2024-08-09 13:51
     */
    public void submitAction() {
        try {
            this.tdZwMonthBhkProvService.updateTdZwMonthBhkProv(this.monthBhkProv);
            this.searchAction();
            this.viewInitAction();
            JsfUtil.addSuccessMessage("提交成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("提交失败！");
            this.searchAction();
            e.printStackTrace();
        }

    }

    /**
     * <p>方法描述：撤销前验证</p>
     *
     * @MethodAuthor hsj 2024-08-09 13:54
     */
    public void beforeCancel() {
        this.monthBhkProv = this.tdZwMonthBhkProvService.findTdZwMonthBhkProvByMainId(this.rid);
        if (!"1".equals(Convert.toStr(this.monthBhkProv.getState()))) {
            JsfUtil.addErrorMessage("当前状态不可撤销，请刷新页面！");
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmCancelDialog').show()");
    }

    /**
     * <p>方法描述：撤销</p>
     *
     * @MethodAuthor hsj 2024-08-09 13:54
     */
    public void cancelAction() {
        try {
            this.tdZwMonthBhkProvService.cancelTdZwMonthBhkProv(this.monthBhkProv, this.zone);
            this.searchAction();
            this.modInitAction();
            JsfUtil.addSuccessMessage("撤销成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("撤销失败！");
            this.searchAction();
            e.printStackTrace();
        }
    }

    /**
     * <p>方法描述：导出时 加入的其他条件 </p>
     * pw 2024/9/12
     **/
    private void exportfillOtherParams(StringBuilder sb, Map<String, Object> paramMap) {
        sb.append(" AND EXISTS(SELECT 1 FROM TD_TJ_BHK K ")
                .append(" WHERE K.JC_TYPE=1 ")
                .append(" AND NVL(K.IF_ONLY_FS,0) = 0 ")
                .append(" AND K.BHKORG_ID=T.RID ")
                .append(" AND K.RPT_PRINT_DATE>=TO_DATE(:sDate,'YYYY-MM-DD') ")
                .append(" AND K.RPT_PRINT_DATE<=TO_DATE(:eDate,'YYYY-MM-DD') ")
                .append(") ");
        paramMap.put("sDate", this.startDate);
        paramMap.put("eDate", this.endDate);
    }

    /**
     * <p>方法描述：查询有数据的体检机构 </p>
     * pw 2024/9/20
     **/
    private List<Integer> filterExportSrvorgHasData(List<Integer> srvRidList) {
        if (CollectionUtils.isEmpty(srvRidList)) {
            return Collections.emptyList();
        }
        StringBuilder sb = new StringBuilder();
        Map<String, Object> paramMap = new HashMap<>();
        sb.append(" SELECT T.RID ");
        sb.append("       FROM TB_TJ_SRVORG T ")
                .append(" WHERE T.RID IN(:srvorgIdList) ");
        paramMap.put("srvorgIdList", srvRidList);
        this.exportfillOtherParams(sb, paramMap);
        List<Object> resultList = this.commService.findDataBySqlNoPage(sb.toString(), paramMap);
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }
        List<Integer> ridList = new ArrayList<>();
        for (Object o : resultList) {
            ridList.add(Integer.parseInt(o.toString()));
        }
        return ridList;
    }

    public TsZone getZone() {
        return this.zone;
    }

    public void setZone(TsZone zone) {
        this.zone = zone;
    }

    public List<TsZone> getZoneList() {
        return this.zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return this.searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return this.searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchUnitId() {
        return this.searchUnitId;
    }

    public void setSearchUnitId(String searchUnitId) {
        this.searchUnitId = searchUnitId;
    }

    public String getSearchUnitName() {
        return this.searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public Date getStartBhkDate() {
        return this.startBhkDate;
    }

    public void setStartBhkDate(Date startBhkDate) {
        this.startBhkDate = startBhkDate;
    }

    public Date getEndBhkDate() {
        return this.endBhkDate;
    }

    public void setEndBhkDate(Date endBhkDate) {
        this.endBhkDate = endBhkDate;
    }

    public Integer[] getSearchState() {
        return this.searchState;
    }

    public void setSearchState(Integer[] searchState) {
        this.searchState = searchState;
    }

    public List<SelectItem> getStateList() {
        return this.stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getStartDate() {
        return this.startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return this.endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public List<Object[]> getOrgList() {
        return this.orgList;
    }

    public void setOrgList(List<Object[]> orgList) {
        this.orgList = orgList;
    }

    public Object[] getOrgObj() {
        return this.orgObj;
    }

    public void setOrgObj(Object[] orgObj) {
        this.orgObj = orgObj;
    }

    public String getEditZoneCode() {
        return this.editZoneCode;
    }

    public void setEditZoneCode(String editZoneCode) {
        this.editZoneCode = editZoneCode;
    }

    public String getEditZoneName() {
        return this.editZoneName;
    }

    public void setEditZoneName(String editZoneName) {
        this.editZoneName = editZoneName;
    }

    public String getEditUnitId() {
        return this.editUnitId;
    }

    public void setEditUnitId(String editUnitId) {
        this.editUnitId = editUnitId;
    }

    public String getEditUnitName() {
        return this.editUnitName;
    }

    public void setEditUnitName(String editUnitName) {
        this.editUnitName = editUnitName;
    }

    public Integer[] getEditState() {
        return this.editState;
    }

    public void setEditState(Integer[] editState) {
        this.editState = editState;
    }

    public List<TsSimpleCode> getAnalyBadRsnList() {
        return this.analyBadRsnList;
    }

    public void setAnalyBadRsnList(List<TsSimpleCode> analyBadRsnList) {
        this.analyBadRsnList = analyBadRsnList;
    }

    public List<TdZwMonthOrgBadrsns> getOrgBadrsns() {
        return this.orgBadrsns;
    }

    public void setOrgBadrsns(List<TdZwMonthOrgBadrsns> orgBadrsns) {
        this.orgBadrsns = orgBadrsns;
    }


    public TdZwMonthBhkProv getMonthBhkProv() {
        return this.monthBhkProv;
    }

    public void setMonthBhkProv(TdZwMonthBhkProv monthBhkProv) {
        this.monthBhkProv = monthBhkProv;
    }

    public List<SelectItem> getSelYearList() {
        return this.selYearList;
    }

    public void setSelYearList(List<SelectItem> selYearList) {
        this.selYearList = selYearList;
    }

    public List<SelectItem> getSelMonthList() {
        return this.selMonthList;
    }

    public void setSelMonthList(List<SelectItem> selMonthList) {
        this.selMonthList = selMonthList;
    }

    public String getOrgName() {
        return this.orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public List<SelectItem> getEditStateList() {
        return editStateList;
    }

    public void setEditStateList(List<SelectItem> editStateList) {
        this.editStateList = editStateList;
    }
}
