package com.chis.modules.heth.comm.entity;

/**
 * 存储体检数据
 *
 * <AUTHOR>
 * @date 2022/1/29
 */
public class TdTjBhkCheckBatchDTO {
    /**
     * 体检记录RID
     */
    private Integer bhkRid;
    /**
     * 是否市直属
     */
    private Boolean cityDirect;
    /**
     * 是否省直属
     */
    private Boolean provinceDirect;
    /**
     * 上一条流程记录标识
     */
    private Integer lastMark;
    /**
     * 当前流程记录标识
     */
    private Integer currentMark;
    /**
     * 是否及时
     */
    private Boolean timely;

    public Integer getBhkRid() {
        return bhkRid;
    }

    public void setBhkRid(Integer bhkRid) {
        this.bhkRid = bhkRid;
    }

    public Boolean getCityDirect() {
        return cityDirect;
    }

    public void setCityDirect(Boolean cityDirect) {
        this.cityDirect = cityDirect;
    }

    public Boolean getProvinceDirect() {
        return provinceDirect;
    }

    public void setProvinceDirect(Boolean provinceDirect) {
        this.provinceDirect = provinceDirect;
    }

    public Integer getLastMark() {
        return lastMark;
    }

    public void setLastMark(Integer lastMark) {
        this.lastMark = lastMark;
    }

    public Integer getCurrentMark() {
        return currentMark;
    }

    public void setCurrentMark(Integer currentMark) {
        this.currentMark = currentMark;
    }

    public Boolean getTimely() {
        return timely;
    }

    public void setTimely(Boolean timely) {
        this.timely = timely;
    }
}
