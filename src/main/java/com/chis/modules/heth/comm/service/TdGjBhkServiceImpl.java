package com.chis.modules.heth.comm.service;

import com.chis.common.utils.GroupUtil;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.SortUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdGjBadrsns;
import com.chis.modules.heth.comm.entity.TdGjBhk;
import com.chis.modules.heth.comm.entity.TdGjBhksub;
import com.chis.modules.heth.comm.entity.TdGjTchBadrsns;
import com.chis.modules.system.service.AbstractTemplate;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <p>类描述：国家体检数据导入服务</p>
 *
 * @ClassAuthor hsj 2024-08-15 13:34
 */
@Service
@Transactional(readOnly = true)
public class TdGjBhkServiceImpl extends AbstractTemplate {


    /**
     * <p>方法描述：根据主表rid查询危害因素</p>
     *
     * @MethodAuthor hsj 2024-08-15 13:43
     */
    public Map<Integer, String> getTdGjBadrsnsMap(List<Integer> ridList) {
        Map<Integer, String> map = new HashMap<>();
        if (CollectionUtils.isEmpty(ridList)) {
            return map;
        }
        String hql = " select t from  TdGjBadrsns t where  t.fkByMainId.rid in (:ridList)";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("ridList", ridList);
        List<TdGjBadrsns> list = this.findDataByHqlNoPage(hql, paramMap);
        if (CollectionUtils.isEmpty(list)) {
            return map;
        }
        Map<Integer, List<TdGjBadrsns>> listMap = new HashMap<>();
        String[] methodName = new String[]{"getFkByMainId", "getRid"};
        GroupUtil.listsGroup2Map(list, listMap, TdGjBadrsns.class, methodName);
        for (Map.Entry<Integer, List<TdGjBadrsns>> entry : listMap.entrySet()) {
            List<TdGjBadrsns> tdGjBadrsnsList = entry.getValue();
            SortUtil.sortCodeByField(tdGjBadrsnsList, TdGjBadrsns.class, "getFkByBadrsnId");
            List<String> badrsnList = new LinkedList<>();
            for (TdGjBadrsns tdGjBadrsns : tdGjBadrsnsList) {
                badrsnList.add(tdGjBadrsns.getFkByBadrsnId().getCodeName() + "_" + tdGjBadrsns.getFkByConclusionId().getCodeName());
            }
            map.put(entry.getKey(), StringUtils.list2string(badrsnList, "，"));
        }
        return map;
    }

    public TdGjBhk findTdGjBhkByRid(Integer rid) {
        if (rid == null) {
            return new TdGjBhk();
        }
        TdGjBhk tdGjBhk = this.find(TdGjBhk.class, rid);
        if (tdGjBhk == null || tdGjBhk.getRid() == null) {
            return new TdGjBhk();
        }
        tdGjBhk.getTdGjTchBadrsns().size();
        tdGjBhk.getTdGjBadrsns().size();
        tdGjBhk.getTdGjBhksub().size();
        //证件号码加密
        tdGjBhk.setIdc(StringUtils.encryptIdc(tdGjBhk.getIdc()));
        //接触危害因素
        List<TdGjTchBadrsns> tdGjTchBadrsnsList = tdGjBhk.getTdGjTchBadrsns();
        if (CollectionUtils.isNotEmpty(tdGjTchBadrsnsList)) {
            SortUtil.sortCodeByField(tdGjTchBadrsnsList, TdGjTchBadrsns.class, "getFkByBadrsnId");
            List<String> names = new LinkedList<>();
            for (TdGjTchBadrsns tdGjTchBadrsns : tdGjTchBadrsnsList) {
                names.add(tdGjTchBadrsns.getFkByBadrsnId().getCodeName());
            }
            tdGjBhk.setTchBadrsns(StringUtils.list2string(names, "，"));
        }
        //体检结论
        List<TdGjBadrsns> tdGjBadrsnsList = tdGjBhk.getTdGjBadrsns();
        if (CollectionUtils.isNotEmpty(tdGjBadrsnsList)) {
            SortUtil.sortCodeByField(tdGjBadrsnsList, TdGjBadrsns.class, "getFkByBadrsnId");
            List<String> badrsnList = new LinkedList<>();
            for (TdGjBadrsns tdGjBadrsns : tdGjBadrsnsList) {
                badrsnList.add(tdGjBadrsns.getFkByBadrsnId().getCodeName() + "_" + tdGjBadrsns.getFkByConclusionId().getCodeName());
            }
            tdGjBhk.setTjConclusions(StringUtils.list2string(badrsnList, "，"));
        }
        List<TdGjBhksub> tdGjBhksubList = tdGjBhk.getTdGjBhksub();
        //根据项目序号，code排序
        Collections.sort(tdGjBhksubList, new Comparator<TdGjBhksub>() {
            @Override
            public int compare(TdGjBhksub o1, TdGjBhksub o2) {
                if (ObjectUtil.isNotNull(o1.getFkByItemId().getNum()) && ObjectUtil.isNotNull(o2.getFkByItemId().getNum())) {
                    int i = o1.getFkByItemId().getNum().compareTo(o2.getFkByItemId().getNum());
                    if (i == 0) {
                        return o1.getFkByItemId().getItemCode().compareTo(o2.getFkByItemId().getItemCode());
                    }
                    return i;
                } else if (ObjectUtil.isNotNull(o1.getFkByItemId().getNum())) {
                    return -1;
                } else if (ObjectUtil.isNotNull(o2.getFkByItemId().getNum())) {
                    return 1;
                } else {
                    return o1.getFkByItemId().getItemCode().compareTo(o2.getFkByItemId().getItemCode());
                }
            }
        });
        return tdGjBhk;
    }
}
