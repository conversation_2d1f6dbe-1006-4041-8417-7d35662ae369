package com.chis.modules.heth.comm.web;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.modules.heth.comm.entity.*;

import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;
import org.springframework.util.CollectionUtils;

import com.chis.common.utils.GB2Alpha;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.service.HethBaseCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;

/**
 * 体检项目维护
 * 
 * <AUTHOR>
 * @createDate 2014年9月12日 下午3:45
 * @LastModify wjh
 * @ModifyDate 2014年9月12日 下午3:45
 */
@ManagedBean(name = "tbTjItemsCommBean")
@ViewScoped
public class TbTjItemsCommBean extends FacesEditBean implements IProcessData {

	private static final long serialVersionUID = -738445454536625063L;
	/** 存在session中的对象 * */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private HethBaseCommServiceImpl iHethBaseService = (HethBaseCommServiceImpl) SpringContextHolder
			.getBean(HethBaseCommServiceImpl.class);

	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	// 页面体检项目对象
	private TbTjItems tbTjItems = null;
	// 页面体检项目临时子表集合
	private List<TbTjRstdesc> tempTbTjRstdescs = null;
	// 页面添加和修改定性项目描述
	private TbTjRstdesc tbTjRstdesc = null;
	/** 体检项目对象rid */
	private Integer rid;
	/** 查询条件：业务分类名称 */
	private String searchYeWuName;
	/** 查询条件：码表id */
	private Integer searchYeWuRid;
	/** 查询条件：码表id */
	private String searchYeWuLc;

	// 查询条件： 项目编码
	private String searchItemCode;
	// 查询条件：项目名称/拼音码
	private String searchItemName;
	// 查询条件：状态
	 private String[] searchState;
	// 查询条件：判断模式
	private String[] searchPanDuanState;
	/** 添加页面：业务分类名称 */
	private String editYeWuName;
	/** 添加页面：码表id */
	private Integer editYeWuRid;
	/** 业务分类树 */
	private TreeNode badRsnTree;
	/** 对现有序号就行重新排序 */
	private List<TbTjItems> orderList = new ArrayList<TbTjItems>();
	/** 在岗状态列表 */
	private List<TsSimpleCode> jobStateList;
	/** 计量单位列表 */
	private List<TsSimpleCode> itemUnitList;
	/** 危害因素码表树形选择框*/
	private TreeNode sortTree;
	/**列表特殊标准集合*/
	private List<TbTjItemsSpe> showItemsSpeList;
	/**列表计量单位集合*/
	private List<TbTjItemUnitRel> showTbTjItemUnitRelList;
	/**特殊标准实体*/
	private TbTjItemsSpe editTbTjItemsSpe;
	/**计量单位实体*/
	private TbTjItemUnitRel tbTjItemUnitRel;
	/**是否是添加操作*/
	private boolean ifAdd;
	/**特殊项目*/
	private Integer speIndex;
	/**计量单位*/
	private Integer unitIndex;
	/**体检项目标记码表维护20210507*/
	private List<TsSimpleCode> tjMarkList;
	/**封装*/
	
	//删除特殊实体
	private TbTjItemsSpe tbTjSpe;
	//删除计量单位实体
	private TbTjItemUnitRel tbTjItemUnit;
	/** 国家接口标准集合 */
	private List<TbTjItemsGj> showTbtjItemsGjList;
	/** 国家接口标准实体 */
	private TbTjItemsGj editTbtjItemsGj;
	/** 删除的国家接口标准实体 */
	private TbTjItemsGj tbTjItemsGj;

	/** 编辑用中间变量 特殊标准在岗状态ID */
	private Integer editOnguardStateRid;
	/** 编辑用中间变量 特殊标准危害因素ID */
	private Integer editBadRsnRid;
	/** 编辑用中间变量 特殊标准危害因素名称 */
	private String editBadRsnName;
	/** 编辑用中间变量 特殊标准性别 */
	private String editSexStr;
	/** 编辑用中间变量 特殊标准计量单位 */
	private String editMsrunt;
	/** 编辑用中间变量 最小值 */
	private BigDecimal editMinval;
	/** 编辑用中间变量 最大值 */
	private BigDecimal editMaxVal;
	/** 编辑用中间变量 计量单位参考值 */
	private String editItemStdvalue;
	/** 编辑用中间变量 计量单位是否默认 */
	private Integer editIfDefault;
	/** 编辑用中间变量 国家接口标准类型  */
	private Integer editType;
	/** 编辑用中间变量 国家接口标准性别  */
	private Integer editSex;
	/** 编辑用中间变量 国家接口标准计量单位ID 计量单位的计量单位ID  */
	private Integer editMsruntId;
	/** 删除国家接口标准rid集合 */
	private List<Integer> removeGjRidList;

	/** 国家计量单位码表集合 */
	private List<TsSimpleCode> gjItemUnitList;
	/** 计量单位包含停用的码表Map */
	private Map<Integer, TsSimpleCode> itemUnitMap;
	/** 在岗状态包含停用的码表Map */
	private Map<Integer, TsSimpleCode> onguardStateMap;
	/** 危害因素包含停用的码表Map */
	private Map<Integer, TsSimpleCode> badRsnMap;
	/** 定性项目描述 排列顺序 */
	private Integer rstDescNum;
	/** 定性项目描述 是否合格 */
	private Short egbTag;
	/** 定性项目描述 结果描述 */
	private String rstDesc;

	@PostConstruct
	public void init() {
		this.initSearchCondition();
		this.searchAction();
		this.pageInit();
		this.initSortTree();
	}

	
	/**
	 * <p>方法描述：初始化危害因素</p>
 	 * 
 	 * @MethodAuthor rcj,2019年5月24日,initSortTree
	 */
	private void initSortTree() {
		this.badRsnMap = new HashMap<>();
		List<TsSimpleCode> simpleCodeList = commService.findallSimpleCodesByTypeId("5007");
		if(!CollectionUtils.isEmpty(simpleCodeList)){
			for(TsSimpleCode simpleCode : simpleCodeList){
				this.badRsnMap.put(simpleCode.getRid(), simpleCode);
			}
		}
		this.sortTree = new DefaultTreeNode("root", null);
		List<TsSimpleCode> list = null;
		list = commService.findScAllOrNoByTypeId("5007", true);
		if (null != list && list.size() > 0) {
			Set<String> firstLevelNoSet = new LinkedHashSet<String>(); // 只有第一层
			Set<String> levelNoSet = new LinkedHashSet<String>(); // 没有第一层
			Map<String, TsSimpleCode> menuMap = new HashMap<String, TsSimpleCode>(); // 所有类别

			for (TsSimpleCode t : list) {
				menuMap.put(t.getCodeLevelNo(), t);
				/**
				 * 若层级编码不为空，并且不包含“.”,放在第一层 firstLevelNoSet存储所有层级编码
				 */
				if (StringUtils.isNotBlank(t.getCodeLevelNo())) {
					if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
						firstLevelNoSet.add(t.getCodeLevelNo());
					} else {
						/**
						 * 层级编码存在“.”
						 */
						levelNoSet.add(t.getCodeLevelNo());
					}
				}
			}
			/**
	             * 
	             */
			for (String ln : firstLevelNoSet) {
				TsSimpleCode t = menuMap.get(ln);
				TreeNode node = new DefaultTreeNode(t, this.sortTree);
				this.addChildNode(ln, levelNoSet, menuMap, node);
			}

			menuMap.clear();
		}

	}
	
	
	
	/**
	 * <p>方法描述：初始化在岗状态/计量单位</p>
 	 * 
 	 * @MethodAuthor rcj,2019年5月24日,pageInit
	 */
	private void pageInit() {
		// 在岗状态下拉列表初始化
		jobStateList = commService.findSimpleCodesByTypeId("5009");
		List<TsSimpleCode> simpleCodeList = commService.findallSimpleCodesByTypeId("5009");
		this.onguardStateMap = new HashMap<>();
		if(!CollectionUtils.isEmpty(simpleCodeList)){
			for(TsSimpleCode simpleCode : simpleCodeList){
				this.onguardStateMap.put(simpleCode.getRid(), simpleCode);
			}
		}
		// 组织下拉列表
		if (null == jobStateList ) {
			jobStateList = new ArrayList<>();
		}

		//计量单位全部码表
		simpleCodeList = this.commService.findallSimpleCodesByTypeId("5501");
		this.itemUnitMap = new HashMap<>();
		if(!CollectionUtils.isEmpty(simpleCodeList)){
			for(TsSimpleCode simpleCode : simpleCodeList){
				this.itemUnitMap.put(simpleCode.getRid(), simpleCode);
			}
		}
		// 计量单位下拉列表初始化
		itemUnitList = commService.findSimpleCodesByTypeId("5501");
		// 组织下拉列表
		if (null == itemUnitList ) {
			itemUnitList = new ArrayList<>();
		}

	}
	
	/**
	 * <p>方法描述：危害因素选择</p>
 	 * 
 	 * @MethodAuthor rcj,2019年5月27日,onsortNodeSelect
	 * @param event
	 */
	public void onsortNodeSelect(NodeSelectEvent event) {
		this.editBadRsnRid = null;
		this.editBadRsnName = null;
		TreeNode selectNode = event.getTreeNode();
		/** 获得第一级的节点 */
		if (null != selectNode) {
			List<TreeNode> childs = selectNode.getChildren();
			/**
			 * 得到子级节点 无子级节点（最后一级）刷新输入框，并关闭弹出框
			 * */
			if (childs == null || childs.size() == 0) {
				TsSimpleCode t = (TsSimpleCode) selectNode.getData();
				this.editBadRsnRid = t.getRid();
				this.editBadRsnName = t.getCodeName();
				RequestContext context = RequestContext.getCurrentInstance();
				context.execute("PF('OveralPanel').hide()");
				context.update("tabView:editForm:searchBadrsns");
			}
		}
	}


	
	/**
	 * 排序方法
	 */
	public void orderAction() {
		orderList = new ArrayList<TbTjItems>();
		if (null != tbTjItems) {
			List<TbTjItems> findTjItemList = this.iHethBaseService.findTjItemList(searchItemCode, searchItemName,
					searchState, searchPanDuanState, tbTjItems.getTsSimpleCode().getRid());
			if (null != findTjItemList && findTjItemList.size() > 0) {
				orderList.addAll(findTjItemList);
			} else {
				JsfUtil.addErrorMessage("请先查询出可供排序的数据！");
			}
		}
	}

	/**
	 * 保存排序
	 */
	public void saveOrder() {
		if (null != orderList && orderList.size() > 0) {
			this.iHethBaseService.updateTjItemsCodeNum(orderList);
			JsfUtil.addSuccessMessage("更新排序成功！");
			this.searchAction();
		}
	}

	/**
	 * 下移方法
	 */
	public void downPlaceAction() {
		if (null != tbTjItems) {
			List<TbTjItems> findTjItemList = this.iHethBaseService.findTjItemList(searchItemCode, searchItemName,
					searchState, searchPanDuanState, tbTjItems.getTsSimpleCode().getRid());
			if (null != findTjItemList && findTjItemList.size() > 1) {
				for (int i = 0; i < findTjItemList.size(); i++) {
					TbTjItems tbTjItems = findTjItemList.get(i);
					if (tbTjItems.getRid().intValue() == this.tbTjItems.getRid().intValue()) {
						if (i != findTjItemList.size() - 1) {
							TbTjItems upTbTjItems = findTjItemList.get(i + 1);
							this.iHethBaseService.changeTwoTbTjItems(upTbTjItems, tbTjItems);
							JsfUtil.addSuccessMessage("下移成功！");
							this.searchAction();
						} else {
							JsfUtil.addErrorMessage("当前项目处于业务分类最下层，无法下移！");
						}
						break;
					}
				}
			} else {
				JsfUtil.addErrorMessage("当前项目无法下移！");
			}
		}
	}

	@Override
	public void processData(List<?> list) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < list.size(); i++) {
			TbTjItems tbTjItems = (TbTjItems) list.get(i);
			TsSimpleCode tsSimpleCode = tbTjItems.getTsSimpleCode();
			if (null != tsSimpleCode && StringUtils.isNotBlank(tsSimpleCode.getCodeLevelNo())
					&& tsSimpleCode.getCodeLevelNo().contains(".")) {
				String[] splitT = tsSimpleCode.getCodeLevelNo().split("[.]");
				for (String s : splitT) {
					sb.append(",'").append(s).append("'");
				}
			}
		}
		if (sb.length() > 0) {
			Map<String, String> codeMap = this.iHethBaseService.findItemCodeNames(sb.substring(1), "5008");
			if (null != codeMap) {
				for (Object obj : list) {
					TbTjItems tbTjItems = (TbTjItems) obj;
					TsSimpleCode tsSimpleCode = tbTjItems.getTsSimpleCode();
					tsSimpleCode.setCodeName(null);
					if (StringUtils.isNotBlank(tsSimpleCode.getCodeLevelNo())
							&& tsSimpleCode.getCodeLevelNo().contains(".")) {
						String[] split = tsSimpleCode.getCodeLevelNo().split("[.]");
						for (String s : split) {
							String name = codeMap.get(s);
							if (StringUtils.isNotBlank(name)) {
								String codeName = tsSimpleCode.getCodeName();
								if (StringUtils.isBlank(codeName)) {
									tsSimpleCode.setCodeName(name);
								} else {
									tsSimpleCode.setCodeName(tsSimpleCode.getCodeName() + "　" + name);
								}
							}
						}
					}
				}
			}
		}

	}

	// 初始化查询条件
	private void initSearchCondition() {
		// 初始化业务分类
		this.badRsnTree = new DefaultTreeNode("root", null);
		this.searchState = new String[]{"1"};
		List<TsSimpleCode> simpleCodesByType_5008 = this.commService.findLevelSimpleCodesByTypeId("5008");

		if (null != simpleCodesByType_5008 && simpleCodesByType_5008.size() > 0) {
			// 只有第一层
			Set<String> firstLevelNoSet = new LinkedHashSet<String>();
			// 没有第一层
			Set<String> levelNoSet = new LinkedHashSet<String>();
			// 所有类别
			Map<String, TsSimpleCode> menuMap = new HashMap<String, TsSimpleCode>();
			for (TsSimpleCode t : simpleCodesByType_5008) {
				menuMap.put(t.getCodeLevelNo(), t);
				if (StringUtils.isNotBlank(t.getCodeLevelNo())) {
					if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
						firstLevelNoSet.add(t.getCodeLevelNo());
					} else {
						levelNoSet.add(t.getCodeLevelNo());
					}
				}
			}
			// 由第一层开始遍历
			for (String ln : firstLevelNoSet) {
				TreeNode node = new DefaultTreeNode(menuMap.get(ln), this.badRsnTree);
				this.addChildNode(ln, levelNoSet, menuMap, node);
			}
			menuMap.clear();
		}
	}

	/**
	 * 构建类别树
	 * 
	 * @param levelNo
	 *            类别层级编码
	 * @param levelNoSet
	 *            二级以及以上的菜单的类别编码集合
	 * @param menuMap
	 *            类别map
	 * @param parentNode
	 *            上级树节点
	 */
	private void addChildNode(String levelNo, Set<String> levelNoSet, Map<String, TsSimpleCode> menuMap,
			TreeNode parentNode) {
		int level = StringUtils.countMatches(levelNo, ".");
		for (String ln : levelNoSet) {
			if (StringUtils.countMatches(ln, ".") == (level + 1) && StringUtils.startsWith(ln, levelNo + ".")) {
				TreeNode node = new DefaultTreeNode(menuMap.get(ln), parentNode);
				this.addChildNode(ln, levelNoSet, menuMap, node);
			}
		}
	}

	/**
	 * 业务分类树的选择事件
	 * 
	 * @param event
	 *            事件对象
	 */
	public void onNodeSelect(NodeSelectEvent event) {
		this.searchYeWuRid = null;
		this.searchYeWuName = null;
		this.searchYeWuLc = null;
		TreeNode selectNode = event.getTreeNode();
		if (null != selectNode) {
			TsSimpleCode t = (TsSimpleCode) selectNode.getData();
			this.searchYeWuRid = t.getRid();
			this.searchYeWuName = t.getCodeName();
			this.searchYeWuLc = t.getCodeLevelNo();
		}
	}

	/**
	 * 业务分类树的编辑和保存选择事件
	 * 
	 * @param event
	 *            事件对象
	 */
	public void onAddNodeSelect(NodeSelectEvent event) {
		
		TreeNode selectNode = event.getTreeNode();
		/** 获得第一级的节点 */
		if (null != selectNode) {
			List<TreeNode> childs = selectNode.getChildren();
			/**
			 * 得到子级节点 无子级节点（最后一级）刷新输入框，并关闭弹出框
			 * */
			if (childs == null || childs.size() == 0) {
				TsSimpleCode t = (TsSimpleCode) selectNode.getData();
				this.editYeWuRid = t.getRid();
				this.editYeWuName = t.getCodeName();
				RequestContext context = RequestContext.getCurrentInstance();
				context.execute("PF('overalPanel2').hide()");
			}
		}
	}

	/**
	 * 清空业务分类树
	 */
	public void delSearchYeWuName(){
		this.searchYeWuRid = null;
		this.searchYeWuName = null;
		this.searchYeWuLc = null;
	}
	
	/**
	 * 主界面初始化
	 */
	@Override
	public void addInit() {
		this.rid = null;
		// 业务分类初始化
		this.editYeWuName = null;
		this.editYeWuRid = null;
		// 子表临时集合初始化
		this.tempTbTjRstdescs = new ArrayList<TbTjRstdesc>();
		// 临时字表初始化
		this.tbTjRstdesc = new TbTjRstdesc();
		this.tbTjRstdesc.setEgbTag((short) 1);
		// 初始化体检项目对象
		this.tbTjItems = new TbTjItems();
		this.tbTjItems.setSex(3);
		// 初始化状态为启用
		this.tbTjItems.setStopTag((short) 0);
		// 初始化判断模式为定性 2定量 1:定性
		this.tbTjItems.setJdgptn((short) 1);
		// 初始化主表中子表信息
		this.tbTjItems.setTbTjRstdescs(tempTbTjRstdescs);
		// 初始化特殊标准列表
		this.showItemsSpeList = new ArrayList<>();
		// 初始化特殊标准对象
		this.editTbTjItemsSpe = new TbTjItemsSpe();
		this.editTbTjItemsSpe.setFkByOnguardStateid(new TsSimpleCode());
		this.editTbTjItemsSpe.setFkByBadRsnId(new TsSimpleCode());
		this.showTbTjItemUnitRelList= new ArrayList<>();
		// 初始化计量单位对象
		this.tbTjItemUnitRel = new TbTjItemUnitRel();
		this.tbTjItemUnitRel.setFkByMsruntId(new TsSimpleCode());
		this.tbTjItemUnitRel.setIfDefaut(0);
		//国家接口标准初始化
		this.showTbtjItemsGjList = new ArrayList<>();
		this.removeGjRidList = new ArrayList<>();
		//初始化体检标记列表
		this.initSimpleCodeList();
	}

	/**
	 * 保存/更新
	 */
	public void saveAction() {
		// 后台判断资质类别不为空
		if (this.editYeWuRid == null || this.editYeWuRid == 0) {
			JsfUtil.addErrorMessage("业务分类没有选择！");
			return;
		}
		
		if(StringUtils.isBlank(tbTjItems.getItemCode())){
			JsfUtil.addErrorMessage("体检项目编码不能为空！");
			return;
		}
		
		if(StringUtils.isBlank(tbTjItems.getItemName())){
			JsfUtil.addErrorMessage("体检项目名称不能为空！");
			return;
		}
		
		StringBuilder sql = new StringBuilder("");
        sql.append(" select t from TbTjItems t where t.itemCode = '");
        sql.append(tbTjItems.getItemCode()).append("' ");
        if(tbTjItems.getRid() != null){
        	sql.append(" and t.rid != ").append(tbTjItems.getRid());
        }
        List<TbTjItems> ttts = iHethBaseService.findByHql(sql.toString(), TbTjItems.class);
        if(!CollectionUtils.isEmpty(ttts)){
        	JsfUtil.addErrorMessage("项目编码已被使用！");
			return;
        }

        //默认计量的单位
		String murst = null;
		if(tbTjItems.getJdgptn()==2){
			TsSimpleCode defaultMsrunt = null;
			List<Integer> unitRelMsruntList = new ArrayList<>();
			if(CollectionUtils.isEmpty(showTbTjItemUnitRelList)){
				JsfUtil.addErrorMessage("请先配置默认计量单位！");
				return;
			}else{
				int i=0;
				for (TbTjItemUnitRel unit : showTbTjItemUnitRelList) {
					boolean flag = false;
					if(null == unit.getFkByMsruntId() || unit.getFkByMsruntId().getRid()== null){
						JsfUtil.addErrorMessage("计量单位的计量单位不能为空！");
						flag = true;
					}

					if(null == unit.getMinval()){
						JsfUtil.addErrorMessage("计量单位最小值不能为空！");
						flag = true;
					}
					if(null == unit.getMaxval()){
						JsfUtil.addErrorMessage("计量单位最大值不能为空！");
						flag = true;
					}
					if(null != unit.getMinval() && null != unit.getMaxval() && unit.getMaxval().compareTo(unit.getMinval()) == -1){
						JsfUtil.addErrorMessage("计量单位最大值应大于等于最小值！");
						flag = true;
					}
					if(flag){
						return;
					}
					if(unit.getIfDefaut().intValue() == 1){
						murst = unit.getFkByMsruntId().getCodeName();
						defaultMsrunt = unit.getFkByMsruntId();
						i+=1;
					}
					Integer curMsruntId = null == unit.getFkByMsruntId() ? null : unit.getFkByMsruntId().getRid();
					if(null != curMsruntId){
						unitRelMsruntList.add(curMsruntId);
					}
				}
				if(i == 0){
					JsfUtil.addErrorMessage("请先配置默认计量单位！");
					return;
				}else if(i > 1){
					JsfUtil.addErrorMessage("只能有一条默认计量单位！");
					return;
				}
			}
//			tbTjItems.getTbTjRstdescs().clear();
			//特殊标准的在岗状态与危害因素为空的性别集合
			List<Integer> speSexEmptyOnguardList = new ArrayList<>();
			//特殊标准的在岗状态与危害因素不为空的性别集合
			List<Integer> speSexUnEmptyOnguardList = new ArrayList<>();
			if(!CollectionUtils.isEmpty(showItemsSpeList)){
				// 判断新规则
				for (TbTjItemsSpe spe:showItemsSpeList) {

					// 在岗状态和危害因素，要么同时有值，要么同时为空
					if(((null == spe.getFkByBadRsnId() || null == spe.getFkByBadRsnId().getRid())&& null != spe.getFkByOnguardStateid() && spe.getFkByOnguardStateid().getRid() != null)||((null == spe.getFkByOnguardStateid() || null == spe.getFkByOnguardStateid().getRid())&& null != spe.getFkByBadRsnId() && spe.getFkByBadRsnId().getRid() != null)){
						JsfUtil.addErrorMessage("特殊标准中在岗状态和危害因素必须同时有值或同时为空！");
						return;
					}else
					//基本信息中选择固定某一性别时，特殊标准也只能选择某一性别
					if(tbTjItems.getSex()==1  && !"男".equals(spe.getSex())){
						JsfUtil.addErrorMessage("基本信息中性别选择男时，特殊标准中的性别只能选择男！");
						return;
					}else if(tbTjItems.getSex()==2 && !"女".equals(spe.getSex())){
						JsfUtil.addErrorMessage("基本信息中性别选择女时，特殊标准中的性别只能选择女！");
						return;
					}else if ((tbTjItems.getSex()==1 || tbTjItems.getSex()==2) && (null == spe.getFkByOnguardStateid() || null == spe.getFkByBadRsnId() || (null != spe.getFkByOnguardStateid() && spe.getFkByOnguardStateid().getRid()==null) ||( null != spe.getFkByBadRsnId() && spe.getFkByBadRsnId().getRid() == null)) ){
						//基本信息中选择固定某一性别时，特殊标准中不允许维护“在岗状态”和“危害因素”为空的数据
						JsfUtil.addErrorMessage("基本信息中性别不为通用时，特殊标准中“在岗状态”和“危害因素”不能为空！");
						return;
					}else if(((null == spe.getFkByOnguardStateid() && null == spe.getFkByBadRsnId()) || (null != spe.getFkByOnguardStateid() && spe.getFkByOnguardStateid().getRid()==null && null != spe.getFkByBadRsnId() && spe.getFkByBadRsnId().getRid() == null)) &&  null != spe.getSex() && "通用".equals(spe.getSex())){
						//当特殊标准中“在岗状态”和“危害因素”都为空的时候，特殊标准中的性别不能选择“通用”
						JsfUtil.addErrorMessage("基本信息中性别为通用时，特殊标准中“在岗状态”和“危害因素”都未选择时，特殊标准中的性别不能选择“通用”！");
						return;
					}else if(((null != spe.getFkByOnguardStateid() && spe.getFkByOnguardStateid().getRid()!=null) || (null != spe.getFkByBadRsnId() && spe.getFkByBadRsnId().getRid() != null)) && showTbTjItemUnitRelList.size()>1){
						JsfUtil.addErrorMessage("特殊标准中“在岗状态”或“危害因素”不为空时，计量单位只能维护一条！");
						return;
					}
					Integer sexId = "男".equals(spe.getSex()) ? 1 : ("女".equals(spe.getSex()) ? 2 : 3);
					//在岗状态与危害因素必须同时空 这里只判断一个即可
					if(!speSexEmptyOnguardList.contains(sexId) && (null == spe.getFkByOnguardStateid() || spe.getFkByOnguardStateid().getRid()==null)){
						speSexEmptyOnguardList.add(sexId);
					}
					if(!speSexUnEmptyOnguardList.contains(sexId) && null != spe.getFkByOnguardStateid() && null != spe.getFkByOnguardStateid().getRid()){
						speSexUnEmptyOnguardList.add(sexId);
					}
				}
			}

			//国家接口标准相关验证
			if(!CollectionUtils.isEmpty(this.showTbtjItemsGjList)){
				//基本信息性别是否通用
				boolean ifTy = 3 == this.tbTjItems.getSex();
				List<Integer> curGjSexList = new ArrayList<>();
				//参考值的性别集合
				List<Integer> curResultGjSexList = new ArrayList<>();
				for(TbTjItemsGj itemsGj : this.showTbtjItemsGjList){
					Integer curGjSex = null == itemsGj.getSex() ? 3 : itemsGj.getSex();
					if(!ifTy){
						if(3 == curGjSex || this.tbTjItems.getSex().compareTo(curGjSex) != 0){
							String curSexStr = 1 == this.tbTjItems.getSex() ? "男" : "女";
							JsfUtil.addErrorMessage("基本信息中性别选择"+curSexStr+"时，国家接口标准中的性别只能选择"+curSexStr+"！");
							return;
						}
					}
					if(!curGjSexList.contains(curGjSex)){
						curGjSexList.add(curGjSex);
					}
					if(null == itemsGj.getFkByMsruntId() || !unitRelMsruntList.contains(itemsGj.getFkByMsruntId().getRid())){
						JsfUtil.addErrorMessage("国家接口标准中的“计量单位”在【计量单位】区域内不存在！");
						return;
					}
					if(1 == itemsGj.getType() && !curResultGjSexList.contains(curGjSex)){
						curResultGjSexList.add(curGjSex);
					}
				}
				if(ifTy && curGjSexList.size() > 1 && curGjSexList.contains(3)){
					JsfUtil.addErrorMessage("基本信息中性别为“通用”时，国家接口标准中不能同时存在性别为“通用”和性别为“男”或“女”的记录！");
					return;
				}

				//基本信息通用 有参考值时判断
				if(ifTy && !CollectionUtils.isEmpty(curResultGjSexList)){
					//存在在岗状态与危害因素不为空 并且特殊标准性别与国家接口标准性别不一致
					boolean ifMarch = (!CollectionUtils.isEmpty(speSexUnEmptyOnguardList) &&
							!curResultGjSexList.containsAll(speSexUnEmptyOnguardList));
					if(ifMarch){
						JsfUtil.addErrorMessage("特殊标准中在岗状态与危害因素不为空时性别与国家接口标准参考值的性别必须保持一致！");
						return;
					}

					if(!curResultGjSexList.contains(3)){
						//基本信息性别为通用，国家接口标准中性别不为“通用”时，特殊标准内必须有相同性别的非GBZ188标准的记录
						if(CollectionUtils.isEmpty(speSexEmptyOnguardList) || speSexEmptyOnguardList.size() != curResultGjSexList.size() ||
								!curResultGjSexList.containsAll(speSexEmptyOnguardList)){
							JsfUtil.addErrorMessage("特殊标准中至少存在一条在岗状态和危害因素为空且性别与国家接口标准参考值的性别保持一致的数据！");
							return;
						}
					}else{
						//国家接口标准参考值性别通用
						//存在在岗状态与危害因素为空的特殊标准
						if(!CollectionUtils.isEmpty(speSexEmptyOnguardList)){
							JsfUtil.addErrorMessage("国家接口标准参考值的性别为“通用”，特殊标准中不能存在“在岗状态”和“危害因素”都为空的数据！");
							return;
						}
					}
				}
				//参考值时比较大小
				for(TbTjItemsGj itemsGj : this.showTbtjItemsGjList){
					if(1 != itemsGj.getType()){
						continue;
					}
					BigDecimal curMinVal = itemsGj.getMinval();
					BigDecimal curMaxVal = itemsGj.getMaxval();
					Integer curSex = null == itemsGj.getSex() ? 3 : itemsGj.getSex();
					Integer curMsruntId = null == itemsGj.getFkByMsruntId() ? null : itemsGj.getFkByMsruntId().getRid();
					if(null == curMaxVal || null == curMinVal || null == curMsruntId){
						continue;
					}
					List<String> errMsgList = new ArrayList<>();
					String errMsg = null;
					//基本信息性别男或者女 或者国家接口标准通用
					if((!ifTy || 3 == curSex) && !CollectionUtils.isEmpty(this.showTbTjItemUnitRelList)){
						for(TbTjItemUnitRel unitRel : this.showTbTjItemUnitRelList){
							if(null == unitRel.getFkByMsruntId() ||
									unitRel.getFkByMsruntId().getRid().compareTo(curMsruntId) != 0){
								continue;
							}
							BigDecimal unitMinVal = unitRel.getMinval();
							BigDecimal unitMaxVal = unitRel.getMaxval();
							if(null == unitMinVal || null == unitMaxVal){
								continue;
							}
							if(unitMinVal.compareTo(curMinVal) < 0){
								errMsg = "计量单位的最小值不能超出国家接口标准同计量单位的参考值！";
							}
							if(StringUtils.isNotBlank(errMsg) && !errMsgList.contains(errMsg)){
								errMsgList.add(errMsg);
							}
							if(unitMaxVal.compareTo(curMaxVal) > 0){
								errMsg = "计量单位的最大值不能超出国家接口标准同计量单位的参考值！";
							}
							if(StringUtils.isNotBlank(errMsg) && !errMsgList.contains(errMsg)){
								errMsgList.add(errMsg);
							}
						}
					}
					if(!CollectionUtils.isEmpty(this.showItemsSpeList)){
						//计量单位不同 跳过
						if(null == defaultMsrunt || null == defaultMsrunt.getRid() ||
								curMsruntId.compareTo(defaultMsrunt.getRid()) != 0){
							continue;
						}
						for(TbTjItemsSpe itemsSpe : this.showItemsSpeList){
							BigDecimal unitMinVal = itemsSpe.getMinval();
							BigDecimal unitMaxVal = itemsSpe.getMaxval();
							Integer speSex = "男".equals(itemsSpe.getSex()) ? 1 : ("女".equals(itemsSpe.getSex()) ? 2 : ("通用".equals(itemsSpe.getSex()) ? 3 : null));
							//最大值最小值为空或者性别与国家接口标准不一致 跳过
							if(null == unitMinVal || null == unitMaxVal || null == speSex || null == curSex ||
									curSex.compareTo(speSex) != 0){
								continue;
							}
							if(unitMinVal.compareTo(curMinVal) < 0){
								errMsg = "特殊标准的最小值不能超出国家接口标准同性别同计量单位的参考值！";
							}
							if(StringUtils.isNotBlank(errMsg) && !errMsgList.contains(errMsg)){
								errMsgList.add(errMsg);
							}
							if(unitMaxVal.compareTo(curMaxVal) > 0){
								errMsg = "特殊标准的最大值不能超出国家接口标准同性别同计量单位的参考值！";
							}
							if(StringUtils.isNotBlank(errMsg) && !errMsgList.contains(errMsg)){
								errMsgList.add(errMsg);
							}
						}
					}
					if(!CollectionUtils.isEmpty(errMsgList)){
						for(String msg : errMsgList){
							JsfUtil.addErrorMessage(msg);
						}
						return;
					}
				}
			}

			//全部验证通过后才能赋值
			if(!CollectionUtils.isEmpty(showItemsSpeList)){
				for (TbTjItemsSpe spe : showItemsSpeList) {
					if(StringUtils.isNoneBlank(spe.getSex()) && "通用".equals(spe.getSex())){
						spe.setSex(null);
					}
					if(null != spe.getFkByBadRsnId() && spe.getFkByBadRsnId().getRid() == null){
						spe.setFkByBadRsnId(null);
					}
					if(null != spe.getFkByOnguardStateid() && spe.getFkByOnguardStateid().getRid() == null){
						spe.setFkByOnguardStateid(null);
					}
					spe.setMsrunt(murst);
					spe.setCreateDate(new Date());
					spe.setCreateManid(Global.getUser().getRid());
					spe.setFkByItemId(tbTjItems);
				}
				tbTjItems.setTbTjItemsSpeList(showItemsSpeList);
			}

			if(!CollectionUtils.isEmpty(showTbTjItemUnitRelList)){
				for (TbTjItemUnitRel unit : showTbTjItemUnitRelList) {
					unit.setCreateDate(new Date());
					unit.setCreateManid(Global.getUser().getRid());
					unit.setFkByItemId(tbTjItems);
					//默认计量单位赋值给基础表
					if(unit.getIfDefaut().intValue() == 1){
						tbTjItems.setMinval(unit.getMinval());
						tbTjItems.setMsrunt(unit.getFkByMsruntId().getCodeName());
						tbTjItems.setMaxval(unit.getMaxval());
						tbTjItems.setMsruntId(unit.getFkByMsruntId().getRid());
					}
				}
				tbTjItems.setTbTjItemsUnitList(showTbTjItemUnitRelList);
			}
			tbTjItems.setItemStdvalue(null);
		}
		if(tbTjItems.getJdgptn()==1){
			showItemsSpeList.clear();
			showTbTjItemUnitRelList.clear();
			tbTjItems.setTbTjItemsSpeList(new ArrayList<TbTjItemsSpe>());
			tbTjItems.setTbTjItemsUnitList(new ArrayList<TbTjItemUnitRel>());
		}
		if(3 ==tbTjItems.getSex()){
			tbTjItems.setSex(null);
		}


		if(StringUtils.isNotBlank(this.tbTjItems.getItemTagNew())){
			String[] split = this.tbTjItems.getItemTagNew().split("-");
			//获取码表拓展字段3的值
			this.tbTjItems.setItemTag(Integer.parseInt(split[1]));
			//保存码表rid
			for (TsSimpleCode simpleCode:this.tjMarkList
				 ) {
				if(simpleCode.getRid()==Integer.parseInt(split[0])){
					this.tbTjItems.setItemTagId(simpleCode);
				}
			}
		}else {
			this.tbTjItems.setItemTag(null);
			this.tbTjItems.setItemTagId(null);
		}
		tbTjItems.setPyxnam(new GB2Alpha().String2Alpha(this.tbTjItems.getItemName()));
		tbTjItems.setTsSimpleCode(new TsSimpleCode(this.editYeWuRid));
		try{
			this.iHethBaseService.saveOrUpdateTbTjItems(this.tbTjItems, this.showTbtjItemsGjList, this.removeGjRidList);
			JsfUtil.addSuccessMessage("保存成功！");
			this.searchAction();
			this.backAction();
		}catch(Exception e){
			e.printStackTrace();
			JsfUtil.addErrorMessage("保存失败！");
		}
		if(!CollectionUtils.isEmpty(tbTjItems.getTbTjItemsSpeList())){
			for (TbTjItemsSpe spe:tbTjItems.getTbTjItemsSpeList()) {
				if( spe.getFkByBadRsnId()==null){
					spe.setFkByBadRsnId(new TsSimpleCode());
				}
				if( spe.getFkByOnguardStateid()==null){
					spe.setFkByOnguardStateid(new TsSimpleCode());
				}
			}
		}
	}

	public void clearItemCode(){
		this.editBadRsnRid = null;
		this.editBadRsnName = null;
	}

	/**
	 * 修改主表初始化
	 */
	@Override
	public void modInit() {
		this.tbTjItems = this.iHethBaseService.findTbTjItems(this.rid);
		if(null ==tbTjItems.getSex()){
			tbTjItems.setSex(3);
		}
		if (this.tbTjItems == null) {
			JsfUtil.addErrorMessage("系统有误!");
			forwardEditPage();
			return;
		}
		initSimpleCodeList();
		//体检项目维护修改和回显
		if(tbTjItems.getItemTagId()!=null){
			tbTjItems.setItemTagNew(tbTjItems.getItemTagId().getRid()+"-"+tbTjItems.getItemTag());
		}
		this.editYeWuRid = this.tbTjItems.getTsSimpleCode().getRid();
		this.editYeWuName = this.tbTjItems.getTsSimpleCode().getCodeName();
		this.tbTjRstdesc = new TbTjRstdesc();
		this.tbTjRstdesc.setEgbTag((short) 1);
		showItemsSpeList = new ArrayList<>();
		if(tbTjItems.getJdgptn() == 2){
			showItemsSpeList = tbTjItems.getTbTjItemsSpeList();
			if(null != showItemsSpeList && showItemsSpeList.size()>0){
				for(TbTjItemsSpe spe:showItemsSpeList){
					if(StringUtils.isBlank(spe.getSex())){
						spe.setSex("通用");
					}
					spe.setOnguardStateid(null==spe.getFkByOnguardStateid()?null:spe.getFkByOnguardStateid().getRid());
				}
				
			}
		}
		showTbTjItemUnitRelList = tbTjItems.getTbTjItemsUnitList();
		if(null == showTbTjItemUnitRelList){
			showTbTjItemUnitRelList = new ArrayList<>();
		}
		this.editTbTjItemsSpe = new TbTjItemsSpe();
		this.editTbTjItemsSpe.setFkByOnguardStateid(new TsSimpleCode());
		this.editTbTjItemsSpe.setFkByBadRsnId(new TsSimpleCode());
		// 初始化计量单位对象
		this.tbTjItemUnitRel = new TbTjItemUnitRel();
		this.tbTjItemUnitRel.setFkByMsruntId(new TsSimpleCode());
		this.tbTjItemUnitRel.setIfDefaut(0);

		this.showTbtjItemsGjList =  this.iHethBaseService.findTbTjItemsGjByMainRid(this.rid);
		this.removeGjRidList = new ArrayList<>();
	}

	/**
	 * 修改/查看主子表返回
	 */
	public void modTbTjRstdescBackAction() {
		this.searchAction();
		this.backAction();
	}

	/**
	 * 修改字表信息初始化 传递子表对象
	 */
	public void modTbTjRstdescInitAction() {
		this.rstDescNum = this.tbTjRstdesc.getNum();
		this.egbTag = this.tbTjRstdesc.getEgbTag();
		this.rstDesc = this.tbTjRstdesc.getRstDesc();
	}

	/**
	 * 删除子表信息
	 */
	public void deleteTbTjRstdescAction() {
		if (null != this.tbTjItems.getTbTjRstdescs() && this.tbTjRstdesc != null) {
			this.tbTjItems.getTbTjRstdescs().remove(this.tbTjRstdesc);
			JsfUtil.addSuccessMessage("删除成功！");
		} else {
			JsfUtil.addErrorMessage("删除失败！");

		}
	}

	/**
	 * 定性项目描述 添加字表初始化
	 */
	public void addTjRstdesctAction() {
//		this.editYeWuName = null;
//		this.editYeWuRid = null;
		this.tbTjRstdesc = new TbTjRstdesc();
		this.rstDescNum = null;
		this.egbTag = (short) 1;
		this.rstDesc = null;
	}

	/**
	 * 定性项目描述 字表添加/修改到字表集合
	 */
	public void saveTjRstdesctAction() {
		if(StringUtils.isBlank(this.rstDesc)){
			JsfUtil.addErrorMessage("结果描述不能为空！");
			return;
		}
		this.tbTjRstdesc.setNum(this.rstDescNum);
		this.tbTjRstdesc.setEgbTag(this.egbTag);
		this.tbTjRstdesc.setRstDesc(this.rstDesc);
		boolean ifModify = false;
		if(CollectionUtils.isEmpty(this.tbTjItems.getTbTjRstdescs())){
			this.tbTjItems.setTbTjRstdescs(new ArrayList<TbTjRstdesc>());
		}else{
			for (TbTjRstdesc ttr : this.tbTjItems.getTbTjRstdescs()) {
				if (ttr.equals(this.tbTjRstdesc)){
					ifModify = true;
					JsfUtil.addSuccessMessage("修改成功！");
					break;
				}
			}
		}

		if(!ifModify){
			this.tbTjRstdesc.setTbTjItems(this.tbTjItems);
			this.tbTjItems.getTbTjRstdescs().add(this.tbTjRstdesc);
			JsfUtil.addSuccessMessage("添加成功！");
		}
		initTTR();
		RequestContext.getCurrentInstance().execute("PF('dig').hide()");
	}

	/**
	 * <p>方法描述：添加特殊标准</p>
 	 * 
 	 * @MethodAuthor rcj,2019年5月23日,addSpecialAction
	 */
	public void addSpecialAction(){
		if(!CollectionUtils.isEmpty(showTbTjItemUnitRelList)){
			int i=0;
			for (TbTjItemUnitRel unit : showTbTjItemUnitRelList) {
				if(unit.getIfDefaut().intValue() == 1){
					i+=1;
				}
			}
			if(i == 0){
				JsfUtil.addErrorMessage("请先配置默认计量单位！");
				return;
			}else if(i > 1){
				JsfUtil.addErrorMessage("只能有一条默认计量单位！");
				return;
			}
		}else{
			JsfUtil.addErrorMessage("请先配置默认计量单位！");
			return;
		}
		// 初始化特殊标准对象
		this.editTbTjItemsSpe = new TbTjItemsSpe();
		this.editOnguardStateRid = null;
		this.editBadRsnRid = null;
		this.editBadRsnName = null;
		this.editSexStr = "通用";
		this.editMsrunt = null;
		this.editMinval = null;
		this.editMaxVal = null;
		for (TbTjItemUnitRel unit : showTbTjItemUnitRelList) {
			if(unit.getIfDefaut().intValue() == 1){
				this.editMsrunt = unit.getFkByMsruntId().getCodeName();
			}
		}
		
		RequestContext.getCurrentInstance().execute("PF('dig2').show()");
		RequestContext.getCurrentInstance().update("tabView:editForm:dig2");
	}
	
	
	/**
	 * <p>方法描述：保存(定量)特殊标准维护</p>
 	 * 
 	 * @MethodAuthor rcj,2019年5月23日,saveTjSpecialAction
	 */
	public void saveTjSpecialAction(){
		boolean flag = false;
		if(StringUtils.isBlank(this.editSexStr)){
			JsfUtil.addErrorMessage("性别不能为空！");
			flag = true;
		}
		if(StringUtils.isBlank(this.editMsrunt)){
			JsfUtil.addErrorMessage("请先配置默认计量单位！");
			flag = true;
		}
		
		if(null == this.editMinval){
			JsfUtil.addErrorMessage("最小值不能为空！");
			flag = true;
		}
		if(null == this.editMaxVal){
			JsfUtil.addErrorMessage("最大值不能为空！");
			flag = true;
		}

		if(null != this.editMinval && null != this.editMaxVal && this.editMaxVal.compareTo(this.editMinval) < 0){
			JsfUtil.addErrorMessage("最大值应大于等于最小值！");
			flag = true;
		}

		if(flag){
			return;
		}

		if(null == showItemsSpeList){
			showItemsSpeList = new ArrayList<>();
		}
		this.fillTbtjItemsSpe();
		if(ifAdd){
			this.editTbTjItemsSpe.setFkByItemId(this.tbTjItems);
			this.editTbTjItemsSpe.setCreateDate(new Date());
			this.editTbTjItemsSpe.setCreateManid(Global.getUser().getRid());
			showItemsSpeList.add(editTbTjItemsSpe);
		}else{
			this.editTbTjItemsSpe.setModifyDate(new Date());
			this.editTbTjItemsSpe.setModifyManid(Global.getUser().getRid());
		}
		RequestContext.getCurrentInstance().execute("PF('dig2').hide()");
		RequestContext.getCurrentInstance().update("tabView:editForm:mainGrid2");
	}

	/**
	 * <p>方法描述：特殊标准</p>
	 * @MethodAuthor： pw 2022/9/20
	 **/
	private void fillTbtjItemsSpe(){
		this.editTbTjItemsSpe.setFkByOnguardStateid(null == this.editOnguardStateRid ? null : this.onguardStateMap.get(this.editOnguardStateRid));
		this.editTbTjItemsSpe.setFkByBadRsnId(null == this.editBadRsnRid ? null : this.badRsnMap.get(this.editBadRsnRid));
		this.editTbTjItemsSpe.setSex(this.editSexStr);
		this.editTbTjItemsSpe.setMsrunt(this.editMsrunt);
		this.editTbTjItemsSpe.setMinval(this.editMinval);
		this.editTbTjItemsSpe.setMaxval(this.editMaxVal);
	}
	
	/**
	 * <p>方法描述：修改特殊标准</p>
 	 * 
 	 * @MethodAuthor rcj,2019年5月27日,modTbTjRstSpeInitAction
	 */
	public void modTbTjRstSpeInitAction(){
		if(!CollectionUtils.isEmpty(showTbTjItemUnitRelList)){
			int i=0;
			for (TbTjItemUnitRel unit : showTbTjItemUnitRelList) {
				if(unit.getIfDefaut().intValue() == 1){
					i+=1;
				}
			}
			if(i == 0){
				JsfUtil.addErrorMessage("请先配置默认计量单位！");
				return;
			}else if(i > 1){
				JsfUtil.addErrorMessage("只能有一条默认计量单位！");
				return;
			}
		}else{
			JsfUtil.addErrorMessage("请先配置默认计量单位！");
			return;
		}
		this.initTbtjItemsSpeParams();
		//修改位置的索引
		speIndex = showItemsSpeList.indexOf(editTbTjItemsSpe);
		RequestContext.getCurrentInstance().execute("PF('dig2').show()");
		RequestContext.getCurrentInstance().update("tabView:editForm:dig2");
	}

	/**
	 * <p>方法描述：修改特殊标准初始化参数 </p>
	 * @MethodAuthor： pw 2022/9/20
	 **/
	private void initTbtjItemsSpeParams(){
		this.editOnguardStateRid = null == this.editTbTjItemsSpe.getFkByOnguardStateid() ? null : this.editTbTjItemsSpe.getFkByOnguardStateid().getRid();
		this.editBadRsnRid = null == this.editTbTjItemsSpe.getFkByBadRsnId() ? null : this.editTbTjItemsSpe.getFkByBadRsnId().getRid();
		this.editBadRsnName = null == this.editTbTjItemsSpe.getFkByBadRsnId() ? null : this.editTbTjItemsSpe.getFkByBadRsnId().getCodeName();
		this.editSexStr = this.editTbTjItemsSpe.getSex();
		this.editMsrunt = null;
		for (TbTjItemUnitRel unit : showTbTjItemUnitRelList) {
			if(unit.getIfDefaut().intValue() == 1){
				this.editMsrunt = unit.getFkByMsruntId().getCodeName();
			}
		}
		this.editMinval = this.editTbTjItemsSpe.getMinval();
		this.editMaxVal = this.editTbTjItemsSpe.getMaxval();
	}
	
	/**
	 * <p>方法描述：删除特殊标准</p>
 	 * 
 	 * @MethodAuthor rcj,2019年5月27日,deleteTbTjSpeAction
	 */
	public void deleteTbTjSpeAction(){
		if (!CollectionUtils.isEmpty(showItemsSpeList)) {
			this.showItemsSpeList.remove(this.tbTjSpe);
			JsfUtil.addSuccessMessage("删除成功！");
		} else {
			JsfUtil.addErrorMessage("删除失败！");

		}
	}
	
	
	/**
	 * <p>方法描述：添加计量单位</p>
 	 * 
 	 * @MethodAuthor rcj,2020年6月23日,addItemUnitAction
	 */
	public void addItemUnitAction(){
		// 初始化特殊标准对象
		this.tbTjItemUnitRel = new TbTjItemUnitRel();
		this.editMinval = null;
		this.editMaxVal = null;
		this.editMsruntId = null;
		this.editIfDefault = 0;
	}
	
	
	/**
	 * <p>方法描述：保存(定量)计量单位维护</p>
 	 * 
 	 * @MethodAuthor rcj,2020年6月24日,saveTjItemUnitAction
	 */
	public void saveTjItemUnitAction(){
		boolean flag = false;
		if(null == this.editMsruntId){
			JsfUtil.addErrorMessage("计量单位不能为空！");
			flag = true;
		}
		
		if(null == this.editMinval){
			JsfUtil.addErrorMessage("最小值不能为空！");
			flag = true;
		}
		if(null == this.editMaxVal){
			JsfUtil.addErrorMessage("最大值不能为空！");
			flag = true;
		}
		if(null != this.editMaxVal && null != this.editMinval && this.editMaxVal.compareTo(this.editMinval) < 0){
			JsfUtil.addErrorMessage("最大值应大于等于最小值！");
			flag = true;
		}
		if(flag){
			return;
		}

		if(null == showTbTjItemUnitRelList){
			showTbTjItemUnitRelList = new ArrayList<>();
		}
		
		this.fillTbtjItemUnitRel();
		
		if(ifAdd){
			this.tbTjItemUnitRel.setFkByItemId(this.tbTjItems);
			this.tbTjItemUnitRel.setCreateDate(new Date());
			this.tbTjItemUnitRel.setCreateManid(Global.getUser().getRid());
			showTbTjItemUnitRelList.add(tbTjItemUnitRel);
		}else{
			this.tbTjItemUnitRel.setModifyDate(new Date());
			this.tbTjItemUnitRel.setModifyManid(Global.getUser().getRid());
		}
		RequestContext.getCurrentInstance().execute("PF('dig0').hide()");
		RequestContext.getCurrentInstance().update("tabView:editForm:mainGrid0");
	}
	
	/**
	 * <p>方法描述：修改计量单位</p>
 	 * 
 	 * @MethodAuthor rcj,2020年6月24日,modTbTjItemUnitAction
	 */
	public void modTbTjItemUnitAction(){
		//修改位置的索引
		unitIndex = showTbTjItemUnitRelList.indexOf(tbTjItemUnitRel);
		this.initTbtjItemUnitRelParams();
		RequestContext.getCurrentInstance().execute("PF('dig0').show()");
		RequestContext.getCurrentInstance().update("tabView:editForm:dig0");
	}

	/**
	 * <p>方法描述：计量单位修改初始化 </p>
	 * @MethodAuthor： pw 2022/9/20
	 **/
	private void initTbtjItemUnitRelParams(){
		this.editIfDefault = this.tbTjItemUnitRel.getIfDefaut();
		this.editMinval = this.tbTjItemUnitRel.getMinval();
		this.editMaxVal = this.tbTjItemUnitRel.getMaxval();
		this.editMsruntId = null == this.tbTjItemUnitRel.getFkByMsruntId() ? null :
				this.tbTjItemUnitRel.getFkByMsruntId().getRid();
	}
	/**
	 * <p>方法描述：计量单位保存前赋值 </p>
	 * @MethodAuthor： pw 2022/9/20
	 **/
	private void fillTbtjItemUnitRel(){
		this.tbTjItemUnitRel.setIfDefaut(this.editIfDefault);
		this.tbTjItemUnitRel.setMinval(this.editMinval);
		this.tbTjItemUnitRel.setMaxval(this.editMaxVal);
		this.tbTjItemUnitRel.setFkByMsruntId(this.itemUnitMap.get(this.editMsruntId));
	}
	
	/**TB_TJ_itemUnit_REL
	 * <p>方法描述：删除计量单位</p>
 	 * 
 	 * @MethodAuthor rcj,2020年6月24日,deleteTbTjItemUnitAction
	 */
	public void deleteTbTjItemUnitAction(){
		if (!CollectionUtils.isEmpty(showTbTjItemUnitRelList)) {
			this.showTbTjItemUnitRelList.remove(this.tbTjItemUnit);
			JsfUtil.addSuccessMessage("删除成功！");
		} else {
			JsfUtil.addErrorMessage("删除失败！");

		}
	}
	
	
	
	
	
	/**
	 * 默认设置 日期 修改人 修改时间 创建人 创建时间的设置
	 */
	private void initTTR() {
		if (this.tbTjRstdesc.getCreateDate() == null) {
			this.tbTjRstdesc.setCreateDate(new Date());
			this.tbTjRstdesc.setCreateManid(this.sessionData.getUser().getRid());
		}
		this.tbTjRstdesc.setModifyDate(new Date());
		this.tbTjRstdesc.setModifyManid(this.sessionData.getUser().getRid());

	}

	/**
	 * 查看信息
	 */
	@Override
	public void viewInit() {
		if (this.rid == null) {
			JsfUtil.addErrorMessage("您当前修改的信息不存在！");
			return;
		}
		this.modInit();
	}

	/**
	 * 删除
	 */
	public void deleteAction() {
		try {
			String deleteTbTjItems = this.iHethBaseService.deleteTbTjItems(this.rid);
			if(null != deleteTbTjItems && deleteTbTjItems.contains("成功")){
				JsfUtil.addSuccessMessage(deleteTbTjItems);
			}else{
				JsfUtil.addErrorMessage(deleteTbTjItems);
			}
			this.searchAction();
			// 初始化 还原rid
			this.rid = null;
			return;
		} catch (Exception e) {
		e.printStackTrace();
		JsfUtil.addErrorMessage("该体检项目已被引用，不允许删除！");
		}
	}

	/**
	 * 体检项目 停用/启用
	 */
	public void stopOrStartAction() {
		if (this.tbTjItems != null) {
			this.tbTjItems.setStopTag((short) (this.tbTjItems.getStopTag() == 0 ? 1 : 0));
			this.tbTjItems.setModifyDate(new Date());
			this.tbTjItems.setModifyManid(this.sessionData.getUser().getRid());
			this.iHethBaseService.updateTbTjItems(this.tbTjItems);
			JsfUtil.addSuccessMessage(this.tbTjItems.getStopTag() == 0 ? "停用成功！" : "启用成功！");
			this.searchAction();
			return;
		}
		JsfUtil.addErrorMessage(this.tbTjItems.getStopTag() == 0 ? "停用失败！" : "启用失败！");
	}

	/**
	 * 王吉会 初始化查询结果列表
	 * 
	 * @return
	 */
	@Override
	public String[] buildHqls() {
		StringBuffer countSB = new StringBuffer("select count(t.rid) from TbTjItems t  where 1=1 ");
		StringBuilder sb = new StringBuilder("from TbTjItems t  where 1=1 ");
		// 查询项目编码字段
		if (StringUtils.isNotBlank(this.searchItemCode)) {
			sb.append(" and t.itemCode like :itemCode escape '\\\'");
			countSB.append(" and t.itemCode like :itemCode escape '\\\'");
			paramMap.put("itemCode", "%" + StringUtils.convertBFH(this.searchItemCode) + "%");
		}
		// 查询项目名称或者拼音码
		if (StringUtils.isNotBlank(this.searchItemName)) {
			sb.append(" and (t.itemName like :itemName escape '\\\' or UPPER(t.pyxnam) like :pyitemName escape '\\\')");
			countSB.append(" and (t.itemName like :itemName escape '\\\' or UPPER(t.pyxnam) like :pyitemName escape '\\\')");
			paramMap.put("itemName", "%" + StringUtils.convertBFH(this.searchItemName) + "%");
			paramMap.put("pyitemName", "%" + StringUtils.convertBFH(this.searchItemName.toUpperCase()) + "%");
		}
		// 查询状态 1为启用 0为停用 STOP_TAG
		if (null != this.searchState && searchState.length ==1) {
			sb.append(" and t.stopTag =").append(this.searchState[0]);
			countSB.append(" and t.stopTag =").append(this.searchState[0]);
		}
		// 判断模式 1为定性 0为定量
		if (null != this.searchPanDuanState && searchPanDuanState.length ==1) {
			sb.append(" and t.jdgptn =").append(this.searchPanDuanState[0]);
			countSB.append(" and t.jdgptn =").append(this.searchPanDuanState[0]);
		}
		// 业务分类 关联码表查询
		if (StringUtils.isNotBlank(this.searchYeWuLc)) {
			sb.append(" and t.tsSimpleCode.codeLevelNo like '").append(this.searchYeWuLc).append("%'");
			countSB.append(" and t.tsSimpleCode.codeLevelNo like '").append(this.searchYeWuLc).append("%'");
		}
		// 按业务分类 和项目编码升序排列 codeName
		sb.append(" order by t.tsSimpleCode.codeLevelNo,t.num, t.itemCode ");
		return new String[] { sb.toString(), countSB.toString() };
	}

	/**
	 * <p>
	 *     方法描述：初始化体检标记列表
	 * </p>
	 *
	 * @MethodAuthor yph,2021年05月7日
	 */

	/**
	 * @Description : 企业规模码表初始化
	 * @MethodAuthor: anjing
	 * @Date : 2021/4/13 17:21
	 **/
	private void initSimpleCodeList() {
		this.tjMarkList = this.commService.findLevelSimpleCodesByTypeId("5515");

	}

	/**
	 * <p>方法描述：添加国家接口标准 </p>
	 * @MethodAuthor： pw 2022/9/17
	 **/
	public void addItemGjAction(){
		this.fillGjItemUnitList();
		if(CollectionUtils.isEmpty(gjItemUnitList)){
			JsfUtil.addErrorMessage("请先配置计量单位！");
			return;
		}
		this.editTbtjItemsGj = new TbTjItemsGj();
		this.editTbtjItemsGj.setCreateDate(new Date());
		this.editTbtjItemsGj.setCreateManid(Global.getUser().getRid());
		this.editTbtjItemsGj.setFkByItemId(this.tbTjItems);
		//默认参考值
		this.editTbtjItemsGj.setType(1);
		this.initTbtjItemsGjParams();
		RequestContext.getCurrentInstance().execute("PF('dig3').show()");
		RequestContext.getCurrentInstance().update("tabView:editForm:dig3");
	}

	/**
	 * <p>方法描述：修改国家接口标准 </p>
	 * @MethodAuthor： pw 2022/9/17
	 **/
	public void modTbTjItemsGjAction(){
		this.fillGjItemUnitList();
		if(CollectionUtils.isEmpty(this.gjItemUnitList)){
			JsfUtil.addErrorMessage("请先配置计量单位！");
			return;
		}
		this.initTbtjItemsGjParams();
		RequestContext.getCurrentInstance().execute("PF('dig3').show()");
		RequestContext.getCurrentInstance().update("tabView:editForm:dig3");
	}

	/**
	 * <p>方法描述：删除国家接口标准 </p>
	 * @MethodAuthor： pw 2022/9/17
	 **/
	public void deleteTbTjItemsGjAction(){
		if (!CollectionUtils.isEmpty(this.showTbtjItemsGjList)) {
			this.showTbtjItemsGjList.remove(this.tbTjItemsGj);
			if(null != this.tbTjItemsGj.getRid()){
				this.removeGjRidList.add(this.tbTjItemsGj.getRid());
			}
			JsfUtil.addSuccessMessage("删除成功！");
		} else {
			JsfUtil.addErrorMessage("删除失败！");

		}
	}

	/**
	 * <p>方法描述：添加或者修改国家接口标准 </p>
	 * @MethodAuthor： pw 2022/9/17
	 **/
	public void saveOrUpdateTbTjItemsGjAction(){
		boolean flag = false;
		// 验证 类型与性别有默认值 不会出现空的情况
		if(null == this.editMsruntId){
			flag = true;
			JsfUtil.addErrorMessage("请选择计量单位！");
		}
		if(null == this.editMinval){
			flag = true;
			JsfUtil.addErrorMessage("最小值不能为空！");
		}
		if(null == this.editMaxVal){
			flag = true;
			JsfUtil.addErrorMessage("最大值不能为空！");
		}
		if(null != this.editMinval && null != this.editMaxVal && this.editMinval.compareTo(this.editMaxVal) > 0){
			flag = true;
			JsfUtil.addErrorMessage("最大值应大于等于最小值！");
		}
		//同性别 同计量单位 同类型 不允许重复
		if(null != this.editMsruntId && !CollectionUtils.isEmpty(this.showTbtjItemsGjList)){
			boolean ifRepeat = false;
			for(TbTjItemsGj itemsGj : this.showTbtjItemsGjList){
				if(itemsGj.equals(this.editTbtjItemsGj)){
					continue;
				}
				Integer curType = itemsGj.getType();
				Integer curSex = null == itemsGj.getSex() ? 3 : itemsGj.getSex();
				Integer curMsruntId = itemsGj.getFkByMsruntId().getRid();
				if(this.editType.compareTo(curType) == 0 && this.editSex.compareTo(curSex) == 0 &&
						this.editMsruntId.compareTo(curMsruntId) == 0){
					ifRepeat = true;
				}
			}
			if(ifRepeat){
				JsfUtil.addErrorMessage("国家接口标准中同类型、同性别、同计量单位的记录不允许重复！");
				flag = true;
			}
		}
		if(flag){
			return;
		}
		if(null == this.showTbtjItemsGjList){
			this.showTbtjItemsGjList = new ArrayList<>();
		}
		//给editTbtjItemsGj赋值
		this.fillParamsToTbtjItemsGj();
		if(ifAdd){
			this.showTbtjItemsGjList.add(this.editTbtjItemsGj);
		}
		RequestContext.getCurrentInstance().execute("PF('dig3').hide()");
		RequestContext.getCurrentInstance().update("tabView:editForm:mainGrid3");
	}

	/**
	 * <p>方法描述：初始化国家接口标准中间变量 </p>
	 * @MethodAuthor： pw 2022/9/17
	 **/
	private void initTbtjItemsGjParams(){
		this.editType = this.editTbtjItemsGj.getType();
		this.editSex = this.editTbtjItemsGj.getSex();
		//通用赋值3
		if(null == this.editSex){
			this.editSex = 3;
		}
		this.editMsruntId = null == this.editTbtjItemsGj.getFkByMsruntId() ? null :
				this.editTbtjItemsGj.getFkByMsruntId().getRid();
		this.editMinval = this.editTbtjItemsGj.getMinval();
		this.editMaxVal = this.editTbtjItemsGj.getMaxval();
	}

	/**
	 * <p>方法描述：将中间变量的值赋值给国家接口标准 </p>
	 * @MethodAuthor： pw 2022/9/17
	 **/
	private void fillParamsToTbtjItemsGj(){
		this.editTbtjItemsGj.setType(this.editType);
		this.editTbtjItemsGj.setSex(3 == this.editSex ? null : this.editSex);
		this.editTbtjItemsGj.setFkByMsruntId(this.itemUnitMap.get(this.editMsruntId));
		this.editTbtjItemsGj.setMinval(this.editMinval);
		this.editTbtjItemsGj.setMaxval(this.editMaxVal);
	}

	/**
	 * <p>方法描述：国家接口标准填充可选择的单位 </p>
	 * @MethodAuthor： pw 2022/9/19
	 **/
	private void fillGjItemUnitList(){
		this.gjItemUnitList = new ArrayList<>();
		if(CollectionUtils.isEmpty(this.showTbTjItemUnitRelList)){
			return;
		}
		for(TbTjItemUnitRel unitRel : this.showTbTjItemUnitRelList){
			Integer msruntId = unitRel.getFkByMsruntId().getRid();
			if(null == msruntId){
				continue;
			}
			TsSimpleCode simpleCode = this.itemUnitMap.get(msruntId);
			if(null != simpleCode){
				this.gjItemUnitList.add(simpleCode);
			}
		}
	}


	// getters and setters
	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}


	public TbTjItems getTbTjItems() {
		return tbTjItems;
	}

	public void setTbTjItems(TbTjItems tbTjItems) {
		this.tbTjItems = tbTjItems;
	}

	public List<TbTjRstdesc> getTempTbTjRstdescs() {
		return tempTbTjRstdescs;
	}

	public void setTempTbTjRstdesc(List<TbTjRstdesc> tempTbTjRstdescs) {
		this.tempTbTjRstdescs = tempTbTjRstdescs;
	}

	public String getSearchItemCode() {
		return searchItemCode;
	}

	public void setSearchItemCode(String searchItemCode) {
		this.searchItemCode = searchItemCode;
	}

	public String getSearchItemName() {
		return searchItemName;
	}

	public void setSearchItemName(String searchItemName) {
		this.searchItemName = searchItemName;
	}

	public String[] getSearchPanDuanState() {
		return searchPanDuanState;
	}


	public void setSearchPanDuanState(String[] searchPanDuanState) {
		this.searchPanDuanState = searchPanDuanState;
	}


	public String getEditYeWuName() {
		return editYeWuName;
	}

	public void setEditYeWuName(String editYeWuName) {
		this.editYeWuName = editYeWuName;
	}

	public Integer getEditYeWuRid() {
		return editYeWuRid;
	}

	public void setEditYeWuRid(Integer editYeWuRid) {
		this.editYeWuRid = editYeWuRid;
	}

	public TbTjRstdesc getTbTjRstdesc() {
		return tbTjRstdesc;
	}

	public void setTbTjRstdesc(TbTjRstdesc tbTjRstdesc) {
		this.tbTjRstdesc = tbTjRstdesc;
	}

	public TreeNode getBadRsnTree() {
		return badRsnTree;
	}

	public void setBadRsnTree(TreeNode badRsnTree) {
		this.badRsnTree = badRsnTree;
	}

	public Integer getSearchYeWuRid() {
		return searchYeWuRid;
	}

	public void setSearchYeWuRid(Integer searchYeWuRid) {
		this.searchYeWuRid = searchYeWuRid;
	}

	public String getSearchYeWuName() {
		return searchYeWuName;
	}

	public void setSearchYeWuName(String searchYeWuName) {
		this.searchYeWuName = searchYeWuName;
	}

	public String getSearchYeWuLc() {
		return searchYeWuLc;
	}

	public void setSearchYeWuLc(String searchYeWuLc) {
		this.searchYeWuLc = searchYeWuLc;
	}

	public List<TbTjItems> getOrderList() {
		return orderList;
	}

	public void setOrderList(List<TbTjItems> orderList) {
		this.orderList = orderList;
	}



	public List<TsSimpleCode> getJobStateList() {
		return jobStateList;
	}


	public void setJobStateList(List<TsSimpleCode> jobStateList) {
		this.jobStateList = jobStateList;
	}


	public TreeNode getSortTree() {
		return sortTree;
	}


	public void setSortTree(TreeNode sortTree) {
		this.sortTree = sortTree;
	}




	public List<TbTjItemsSpe> getShowItemsSpeList() {
		return showItemsSpeList;
	}


	public void setShowItemsSpeList(List<TbTjItemsSpe> showItemsSpeList) {
		this.showItemsSpeList = showItemsSpeList;
	}


	public void setTempTbTjRstdescs(List<TbTjRstdesc> tempTbTjRstdescs) {
		this.tempTbTjRstdescs = tempTbTjRstdescs;
	}


	public TbTjItemsSpe getEditTbTjItemsSpe() {
		return editTbTjItemsSpe;
	}


	public void setEditTbTjItemsSpe(TbTjItemsSpe editTbTjItemsSpe) {
		this.editTbTjItemsSpe = editTbTjItemsSpe;
	}


	public TbTjItemsSpe getTbTjSpe() {
		return tbTjSpe;
	}


	public void setTbTjSpe(TbTjItemsSpe tbTjSpe) {
		this.tbTjSpe = tbTjSpe;
	}


	public boolean isIfAdd() {
		return ifAdd;
	}


	public void setIfAdd(boolean ifAdd) {
		this.ifAdd = ifAdd;
	}


	public String[] getSearchState() {
		return searchState;
	}


	public void setSearchState(String[] searchState) {
		this.searchState = searchState;
	}


	public Integer getUnitIndex() {
		return unitIndex;
	}


	public void setUnitIndex(Integer unitIndex) {
		this.unitIndex = unitIndex;
	}


	public TbTjItemUnitRel getTbTjItemUnitRel() {
		return tbTjItemUnitRel;
	}


	public void setTbTjItemUnitRel(TbTjItemUnitRel tbTjItemUnitRel) {
		this.tbTjItemUnitRel = tbTjItemUnitRel;
	}


	public TbTjItemUnitRel getTbTjItemUnit() {
		return tbTjItemUnit;
	}


	public void setTbTjItemUnit(TbTjItemUnitRel tbTjItemUnit) {
		this.tbTjItemUnit = tbTjItemUnit;
	}


	public List<TbTjItemUnitRel> getShowTbTjItemUnitRelList() {
		return showTbTjItemUnitRelList;
	}


	public void setShowTbTjItemUnitRelList(
			List<TbTjItemUnitRel> showTbTjItemUnitRelList) {
		this.showTbTjItemUnitRelList = showTbTjItemUnitRelList;
	}


	public List<TsSimpleCode> getItemUnitList() {
		return itemUnitList;
	}


	public void setItemUnitList(List<TsSimpleCode> itemUnitList) {
		this.itemUnitList = itemUnitList;
	}

	public List<TsSimpleCode> getTjMarkList() {
		return tjMarkList;
	}

	public void setTjMarkList(List<TsSimpleCode> tjMarkList) {
		this.tjMarkList = tjMarkList;
	}

	public List<TbTjItemsGj> getShowTbtjItemsGjList() {
		return showTbtjItemsGjList;
	}

	public void setShowTbtjItemsGjList(List<TbTjItemsGj> showTbtjItemsGjList) {
		this.showTbtjItemsGjList = showTbtjItemsGjList;
	}

	public TbTjItemsGj getEditTbtjItemsGj() {
		return editTbtjItemsGj;
	}

	public void setEditTbtjItemsGj(TbTjItemsGj editTbtjItemsGj) {
		this.editTbtjItemsGj = editTbtjItemsGj;
	}

	public TbTjItemsGj getTbTjItemsGj() {
		return tbTjItemsGj;
	}

	public void setTbTjItemsGj(TbTjItemsGj tbTjItemsGj) {
		this.tbTjItemsGj = tbTjItemsGj;
	}

	public Integer getEditOnguardStateRid() {
		return editOnguardStateRid;
	}

	public void setEditOnguardStateRid(Integer editOnguardStateRid) {
		this.editOnguardStateRid = editOnguardStateRid;
	}

	public Integer getEditBadRsnRid() {
		return editBadRsnRid;
	}

	public void setEditBadRsnRid(Integer editBadRsnRid) {
		this.editBadRsnRid = editBadRsnRid;
	}

	public String getEditSexStr() {
		return editSexStr;
	}

	public void setEditSexStr(String editSexStr) {
		this.editSexStr = editSexStr;
	}

	public String getEditMsrunt() {
		return editMsrunt;
	}

	public void setEditMsrunt(String editMsrunt) {
		this.editMsrunt = editMsrunt;
	}

	public BigDecimal getEditMinval() {
		return editMinval;
	}

	public void setEditMinval(BigDecimal editMinval) {
		this.editMinval = editMinval;
	}

	public BigDecimal getEditMaxVal() {
		return editMaxVal;
	}

	public void setEditMaxVal(BigDecimal editMaxVal) {
		this.editMaxVal = editMaxVal;
	}

	public String getEditItemStdvalue() {
		return editItemStdvalue;
	}

	public void setEditItemStdvalue(String editItemStdvalue) {
		this.editItemStdvalue = editItemStdvalue;
	}

	public Integer getEditIfDefault() {
		return editIfDefault;
	}

	public void setEditIfDefault(Integer editIfDefault) {
		this.editIfDefault = editIfDefault;
	}

	public Integer getEditType() {
		return editType;
	}

	public void setEditType(Integer editType) {
		this.editType = editType;
	}

	public Integer getEditSex() {
		return editSex;
	}

	public void setEditSex(Integer editSex) {
		this.editSex = editSex;
	}

	public Integer getEditMsruntId() {
		return editMsruntId;
	}

	public void setEditMsruntId(Integer editMsruntId) {
		this.editMsruntId = editMsruntId;
	}

	public List<TsSimpleCode> getGjItemUnitList() {
		return gjItemUnitList;
	}

	public void setGjItemUnitList(List<TsSimpleCode> gjItemUnitList) {
		this.gjItemUnitList = gjItemUnitList;
	}

	public Map<Integer, TsSimpleCode> getItemUnitMap() {
		return itemUnitMap;
	}

	public void setItemUnitMap(Map<Integer, TsSimpleCode> itemUnitMap) {
		this.itemUnitMap = itemUnitMap;
	}

	public String getEditBadRsnName() {
		return editBadRsnName;
	}

	public void setEditBadRsnName(String editBadRsnName) {
		this.editBadRsnName = editBadRsnName;
	}

	public List<Integer> getRemoveGjRidList() {
		return removeGjRidList;
	}

	public void setRemoveGjRidList(List<Integer> removeGjRidList) {
		this.removeGjRidList = removeGjRidList;
	}

	public Map<Integer, TsSimpleCode> getOnguardStateMap() {
		return onguardStateMap;
	}

	public void setOnguardStateMap(Map<Integer, TsSimpleCode> onguardStateMap) {
		this.onguardStateMap = onguardStateMap;
	}

	public Map<Integer, TsSimpleCode> getBadRsnMap() {
		return badRsnMap;
	}

	public void setBadRsnMap(Map<Integer, TsSimpleCode> badRsnMap) {
		this.badRsnMap = badRsnMap;
	}

	public Integer getRstDescNum() {
		return rstDescNum;
	}

	public void setRstDescNum(Integer rstDescNum) {
		this.rstDescNum = rstDescNum;
	}

	public Short getEgbTag() {
		return egbTag;
	}

	public void setEgbTag(Short egbTag) {
		this.egbTag = egbTag;
	}

	public String getRstDesc() {
		return rstDesc;
	}

	public void setRstDesc(String rstDesc) {
		this.rstDesc = rstDesc;
	}
}
