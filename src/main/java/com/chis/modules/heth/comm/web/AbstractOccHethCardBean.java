package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.service.OcchethCardService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 *  <p>类描述：职业卫生技术服务信息报送卡基类</p>
 * @ClassAuthor hsj 2023-11-25 18:43
 */
public class AbstractOccHethCardBean extends FacesEditBean {
    private static final long serialVersionUID = 1L;

    protected final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    protected final OcchethCardService occhethCardService = SpringContextHolder.getBean(OcchethCardService.class);

    protected Integer mainRid;

    protected TdZwOcchethCard occhethCard;

    /**
     * 查询条件：服务单位地区集合
     */
    protected List<TsZone> searchZoneList = new ArrayList<>();
    /**
     * 查询条件：服务单位地区编码
     */
    protected String searchZoneGb;
    /**
     * 查询条件：服务单位地区名称
     */
    protected String searchZoneName;
    /**
     * 查询条件：服务单位名称
     */
    protected String searchCrptName;
    /**
     * 查询条件：出具技术服务报告时间-开始日期
     */
    protected Date searchRptBeginDate;
    /**
     * 查询条件：出具技术服务报告时间-结束日期
     */
    protected Date searchRptEndDate;
    /**
     * 查询条件：状态
     */
    protected List<Integer> searchStateList;
    /**
     * 码表Map key：码表RID
     */
    protected Map<String, TsSimpleCode> simpleCodeMap;
    /**
     * 业务范围码表5320
     */
    protected List<TsSimpleCode> businessScopeList = new ArrayList<>();
    /** 是否支持撤销 当上传日志表 状态为3（已提交） 不可撤销 */
    protected Boolean ifSupportCancel;
    /**
     * 职业病危害因素检测 子表
     */
    protected TdZwOcchethCardJc tdZwOcchethCardJc;
    /**
     * 超标检测项目 key:码表rid value: 码表
     */
    protected Map<Integer, TsSimpleCode> itemMap = new HashMap<>();
    /**
     * 职业病危害因素检测 子表
     */
    protected TdZwOcchethCardPj tdZwOcchethCardPj;
    /**
     * 超标检测指标 key:码表rid value: 码表
     */
    protected Map<Integer, TsSimpleCode> indexMap = new HashMap<>();
    /**
     * 超标检测指标 码表
     */
    protected List<TsSimpleCode> indexList;

    public AbstractOccHethCardBean() {
        this.init();
        this.initSimpleCode();
        this.ifSQL = true;
        this.ifSupportCancel =Boolean.FALSE;
    }


    private void init() {
        //出具技术服务报告时间
        this.searchRptEndDate = new Date();
        this.searchRptBeginDate = DateUtils.getYearFirstDay(this.searchRptEndDate);
        this.occhethCard = new TdZwOcchethCard();
    }

    private void initSimpleCode() {
        this.simpleCodeMap = new HashMap<>();
        //业务范围-取最末级
        this.businessScopeList = this.commService.findNumSimpleCodesByTypeId("5320");
        int index = 0;
        for (TsSimpleCode simpleCode : this.businessScopeList) {
            if (ObjectUtil.isEmpty(simpleCode.getCodeLevelNo())) {
                continue;
            }
            int lastIndex = simpleCode.getCodeLevelNo().lastIndexOf(".");
            if (lastIndex > index) {
                index = lastIndex;
            }
        }
        for (int i = this.businessScopeList.size() - 1; i >= 0; i--) {
            TsSimpleCode simpleCode = this.businessScopeList.get(i);
            if (ObjectUtil.isEmpty(simpleCode.getCodeLevelNo()) || index != simpleCode.getCodeLevelNo().lastIndexOf(".")) {
                this.businessScopeList.remove(i);
            }
        }
        addSimpleCodeMapByList(this.businessScopeList);
        //超标检测指标
        indexList = this.commService.findNumSimpleCodesByTypeId("5532");
        if (!CollectionUtils.isEmpty(indexList)) {
            for (TsSimpleCode tsSimpleCode : indexList) {
                indexMap.put(tsSimpleCode.getRid(), tsSimpleCode);
            }
        }
    }
    public void addSimpleCodeMapByList(List<TsSimpleCode> simpleCodeList) {
        if(CollectionUtils.isEmpty(simpleCodeList)){
            return;
        }
        for (TsSimpleCode simpleCode : simpleCodeList) {
            this.simpleCodeMap.put(simpleCode.getRid().toString(), simpleCode);
        }
    }


    @Override
    public String[] buildHqls() {
        return new String[0];
    }


    @Override
    public void addInit() {
    }

    @Override
    public void modInit() {
    }

    @Override
    public void viewInit() {
        this.occhethCard = this.occhethCardService.findTdZwOcchethCardByRid(this.mainRid);

        //处理-参与人员信息
        for (TdZwOcchethCardPsn occhethCardPsn : this.occhethCard.getOcchethCardPsnList()) {
            if (ObjectUtil.isEmpty(occhethCardPsn)) {
                continue;
            }
            List<String> occhethCardItemCodeNameStrList = new ArrayList<>();
            for (TdZwOcchethCardItem occhethCardItem : occhethCardPsn.getOcchethCardItemList()) {
                if (ObjectUtil.isEmpty(occhethCardItem.getFkByItemId())) {
                    continue;
                }
                occhethCardItemCodeNameStrList.add(occhethCardItem.getFkByItemId().getCodeName());
            }
            occhethCardPsn.setOcchethCardItemStr(StringUtils.list2string(occhethCardItemCodeNameStrList, "，"));
        }
        //处理-技术服务信息-技术服务领域
        List<String> occhethCardServiceStrList = new ArrayList<>();
        for (TdZwOcchethCardService occhethCardService : this.occhethCard.getOcchethCardServiceList()) {
            if (ObjectUtil.isEmpty(occhethCardService.getFkByServiceAreaId())) {
                continue;
            }
            occhethCardServiceStrList.add(occhethCardService.getFkByServiceAreaId().getCodeName());
        }
        this.occhethCard.setOcchethCardServiceStr(StringUtils.list2string(occhethCardServiceStrList, "，"));

        dealTechnicalServiceResults();
        publicInit();
    }

    /**
     * 处理技术服务结果
     */
    public void dealTechnicalServiceResults() {
        //职业病危害因素检测
        tdZwOcchethCardJc = new TdZwOcchethCardJc();
        List<TdZwOcchethCardJc> jcList = occhethCardService.packageCardJc(occhethCard.getRid());
        occhethCard.setOcchethCardJcList(jcList);
        //itemList = new ArrayList<>();
        itemMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(occhethCard.getOcchethCardJcList())) {
            for (TdZwOcchethCardJc cardJc : occhethCard.getOcchethCardJcList()) {
                if (!CollectionUtils.isEmpty(cardJc.getOcchethCardJcSubList())) {
                    for (TdZwOcchethCardJcSub jcSub : cardJc.getOcchethCardJcSubList()) {
                        if (StringUtils.isNotBlank(jcSub.getPostName())) {
                            jcSub.setIfFirst(false);
                            if(jcSub.getFkByIndexId()!=null&&jcSub.getFkByIndexId().getRid()!=null){
                                jcSub.setIndexRid(jcSub.getFkByIndexId().getRid());
                            }
                            TsSimpleCode t = new TsSimpleCode();
                            t.setRid(jcSub.getFkByItemId().getRid());
                            if (StringUtils.isNotBlank(jcSub.getFkByItemId().getItemDesc())) {
                                t.setCodeName(jcSub.getFkByItemId().getFkByItemId().getCodeName() + "（" + jcSub.getFkByItemId().getItemDesc() + "）");
                            } else {
                                t.setCodeName(jcSub.getFkByItemId().getFkByItemId().getCodeName());
                            }
                            t.setNum(jcSub.getFkByItemId().getNum());
                            itemMap.put(t.getRid(), t);
                        } else {
                            jcSub.setIfFirst(true);
                            jcSub.setPostNum(0);
                        }
                    }
                }
            }
        }
        OcchethCardJcSort();
        computeRowsSpan();
        //职业病危害现状评价
        tdZwOcchethCardPj = new TdZwOcchethCardPj();
        List<TdZwOcchethCardPj> pjList = occhethCardService.packageCardPj(occhethCard.getRid());
        occhethCard.setOcchethCardPjList(pjList);
        if (!CollectionUtils.isEmpty(occhethCard.getOcchethCardPjList())) {
            for (TdZwOcchethCardPj cardPj : occhethCard.getOcchethCardPjList()) {
                if (!CollectionUtils.isEmpty(cardPj.getOcchethCardPjSubList())) {
                    for (TdZwOcchethCardPjSub pjSub : cardPj.getOcchethCardPjSubList()) {
                        if (StringUtils.isNotBlank(pjSub.getPostName())) {
                            pjSub.setIfFirst(false);
                            if(pjSub.getFkByIndexId()!=null&&pjSub.getFkByIndexId().getRid()!=null){
                                pjSub.setIndexRid(pjSub.getFkByIndexId().getRid());
                            }
                            TsSimpleCode t = new TsSimpleCode();
                            t.setRid(pjSub.getFkByItemId().getRid());
                            if (StringUtils.isNotBlank(pjSub.getFkByItemId().getItemDesc())) {
                                t.setCodeName(pjSub.getFkByItemId().getFkByItemId().getCodeName() + "（" + pjSub.getFkByItemId().getItemDesc() + "）");
                            } else {
                                t.setCodeName(pjSub.getFkByItemId().getFkByItemId().getCodeName());
                            }
                            t.setNum(pjSub.getFkByItemId().getNum());
                            //itemList.add(t);
                            itemMap.put(t.getRid(), t);
                        } else {
                            pjSub.setIfFirst(true);
                            pjSub.setPostNum(0);
                        }
                    }
                }
            }
        }
        OcchethCardPjSort();
        computePjRowsSpan();
    }

    /**
     * 添加/编辑/详情公用初始化操作
     */
    public void publicInit() {
        //拼接资质业务范围
        pakOcchethCardItemsStr();
        //初始化-技术服务结果
        this.occhethCard.setHasBadrsnJc(new Integer(1).equals(this.occhethCard.getIfBadrsnJc()));
        this.occhethCard.setHasStatusPj(new Integer(1).equals(this.occhethCard.getIfStatusPj()));
        this.occhethCard.setHasInstUsePj(new Integer(1).equals(this.occhethCard.getIfInstUsePj()));
        this.occhethCard.setHasJcInst(new Integer(1).equals(this.occhethCard.getIfJcInst()));
        this.occhethCard.setHasJcUse(new Integer(1).equals(this.occhethCard.getIfJcUse()));
    }
    /**
     * <p>方法描述：列表  排序</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-30
     **/
    public void OcchethCardJcSort() {
        if (!CollectionUtils.isEmpty(occhethCard.getOcchethCardJcList())) {
            //危害因素 排序
            Collections.sort(occhethCard.getOcchethCardJcList(), new Comparator<TdZwOcchethCardJc>() {
                @Override
                public int compare(TdZwOcchethCardJc o1, TdZwOcchethCardJc o2) {
                    Integer num1 = o1.getFkByBadrsnId().getNum();
                    Integer num2 = o2.getFkByBadrsnId().getNum();
                    if (null != num1 && null != num2) {
                        return num1.compareTo(num2);
                    }
                    return 0;
                }
            });
            for (TdZwOcchethCardJc cardJc : occhethCard.getOcchethCardJcList()) {
                if (!CollectionUtils.isEmpty(cardJc.getOcchethCardJcSubList())) {
                    for (TdZwOcchethCardJcSub tdZwOcchethCardJcSub : cardJc.getOcchethCardJcSubList()) {
                        if (null == tdZwOcchethCardJcSub.getFkByItemId() || null == tdZwOcchethCardJcSub.getFkByItemId().getRid()) {
                            tdZwOcchethCardJcSub.setItemNumber(-1);
                        } else if (itemMap.containsKey(tdZwOcchethCardJcSub.getFkByItemId().getRid())) {
                            tdZwOcchethCardJcSub.setItemNumber(itemMap.get(tdZwOcchethCardJcSub.getFkByItemId().getRid()).getNum());
                        }
                        if (null == tdZwOcchethCardJcSub.getIndexRid()) {
                            tdZwOcchethCardJcSub.setIndexNumber(-1);
                        } else if (indexMap.containsKey(tdZwOcchethCardJcSub.getIndexRid())) {
                            tdZwOcchethCardJcSub.setIndexNumber(indexMap.get(tdZwOcchethCardJcSub.getIndexRid()).getNum());
                        }
                    }
                    Collections.sort(cardJc.getOcchethCardJcSubList(), new Comparator<TdZwOcchethCardJcSub>() {
                        @Override
                        public int compare(TdZwOcchethCardJcSub o1, TdZwOcchethCardJcSub o2) {
                            String postName1 = StringUtils.objectToString(o1.getPostName());
                            String postName2 = StringUtils.objectToString(o2.getPostName());

                            String itemNum1 = StringUtils.objectToString(o1.getItemNumber());
                            String itemNum2 = StringUtils.objectToString(o2.getItemNumber());

                            String indexNum1 = StringUtils.objectToString(o1.getIndexNumber());
                            String indexNum2 = StringUtils.objectToString(o2.getIndexNumber());

                            if (!postName1.equals(postName2)) {
                                return postName1.compareTo(postName2);
                            } else if (!itemNum1.equals(itemNum2)) {
                                return itemNum1.compareTo(itemNum2);
                            } else if (!indexNum1.equals(indexNum2)) {
                                return indexNum1.compareTo(indexNum2);
                            }
                            return 0;
                        }
                    });
                }
            }
        }
    }

    /**
     * 拼接资质业务范围
     */
    public void pakOcchethCardItemsStr() {
        List<String> occhethItemsCodeNameList = new ArrayList<>();
        for (TdZwOcchethCardItems occhethCardItems : this.occhethCard.getOcchethCardItemsList()) {
            occhethItemsCodeNameList.add(occhethCardItems.getFkByItemId().getCodeName());
        }
        this.occhethCard.setOcchethCardItemsStr(StringUtils.list2string(occhethItemsCodeNameList, "，"));
    }
    /**
     * <p>方法描述：计算行合并</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-30
     **/
    public void computeRowsSpan() {
        if (!CollectionUtils.isEmpty(occhethCard.getOcchethCardJcList())) {
            Map<String, Integer> countRidMap = new HashMap<>();
            Map<String, Integer> countPostMap = new HashMap<>();
            Map<String, Integer> countItemMap = new HashMap<>();
            Set<String> ridSet = new HashSet<>();
            for (TdZwOcchethCardJc cardJc : occhethCard.getOcchethCardJcList()) {
                if (!CollectionUtils.isEmpty(cardJc.getOcchethCardJcSubList()) && cardJc.getOcchethCardJcSubList().size() > 1) {
                    for (TdZwOcchethCardJcSub jcSub : cardJc.getOcchethCardJcSubList()) {
                        if (countRidMap.get(cardJc.getFkByBadrsnId().getRid().toString()) == null) {
                            countRidMap.put(cardJc.getFkByBadrsnId().getRid().toString(), 1);
                        } else {
                            countRidMap.put(cardJc.getFkByBadrsnId().getRid().toString(), countRidMap.get(cardJc.getFkByBadrsnId().getRid().toString()) + 1);
                        }
                        if (countPostMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName()) == null) {
                            countPostMap.put(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName(), 1);
                        } else {
                            countPostMap.put(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName(), countPostMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName()) + 1);
                        }
                        if (countItemMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid()) == null) {
                            countItemMap.put(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid(), 1);
                        } else {
                            countItemMap.put(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid(), countItemMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid()) + 1);
                        }
                        if (ridSet.add(cardJc.getFkByBadrsnId().getRid().toString())) {
                            jcSub.setBadrsnName(cardJc.getFkByBadrsnId().getRsnCnName());
                            jcSub.setNum(cardJc.getNum());
                            jcSub.setPostNum(cardJc.getPostNum());
                            jcSub.setBadrsnRowspan(cardJc.getOcchethCardJcSubList().size());
                        } else {
                            jcSub.setBadrsnName(null);
                            jcSub.setPostNum(null);
                            jcSub.setNum(null);
                        }
                        if (StringUtils.isNotBlank(jcSub.getPostName()) && jcSub.getFkByItemId() != null && jcSub.getFkByItemId().getRid() != null) {
                            if (ridSet.add(cardJc.getFkByBadrsnId().getRid().toString() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid())) {
                                jcSub.setItermName(itemMap.get(jcSub.getFkByItemId().getRid()).getCodeName());
                                jcSub.getFkByItemId().setFkByItemId(new TsSimpleCode("", itemMap.get(jcSub.getFkByItemId().getRid()).getCodeName()));
                            } else {
                                jcSub.setItermName(null);
                            }
                        } else {
                            jcSub.setItermName(null);
                        }
                        jcSub.setFkByIndexId(indexMap.get(jcSub.getIndexRid()));
                    }
                } else {
                    if (CollectionUtils.isEmpty(cardJc.getOcchethCardJcSubList())) {
                        TdZwOcchethCardJcSub occhethCardJcSub = new TdZwOcchethCardJcSub();
                        occhethCardJcSub.setBadrsnName(cardJc.getFkByBadrsnId().getRsnCnName());
                        occhethCardJcSub.setIfFirst(true);
                        occhethCardJcSub.setFkByMainId(cardJc);
                        occhethCardJcSub.setFkByItemId(new TbYsjcRsnRelItemComm());
                        occhethCardJcSub.setFkByIndexId(new TsSimpleCode());
                        occhethCardJcSub.setFkByMsruntId(new TsSimpleCode());
                        List<TdZwOcchethCardJcSub> subList = new ArrayList<>();
                        subList.add(occhethCardJcSub);
                        cardJc.setOcchethCardJcSubList(subList);
                    }
                    if(cardJc.getPostNum()!=null){
                        cardJc.getOcchethCardJcSubList().get(0).setPostNum(cardJc.getPostNum());
                    }
                    cardJc.getOcchethCardJcSubList().get(0).setBadrsnName(cardJc.getFkByBadrsnId().getRsnCnName());
                    if (StringUtils.isNotBlank(cardJc.getOcchethCardJcSubList().get(0).getPostName())) {
                        cardJc.getOcchethCardJcSubList().get(0).setNum(cardJc.getNum());
                        cardJc.getOcchethCardJcSubList().get(0).getFkByItemId().setFkByItemId(new TsSimpleCode("", itemMap.get(cardJc.getOcchethCardJcSubList().get(0).getFkByItemId().getRid()).getCodeName()));
                        cardJc.getOcchethCardJcSubList().get(0).setItermName(itemMap.get(cardJc.getOcchethCardJcSubList().get(0).getFkByItemId().getRid()).getCodeName());
                        cardJc.getOcchethCardJcSubList().get(0).setFkByIndexId(indexMap.get(cardJc.getOcchethCardJcSubList().get(0).getIndexRid()));
                    } else {
                        cardJc.getOcchethCardJcSubList().get(0).setItermName("");
                        cardJc.getOcchethCardJcSubList().get(0).setNum(cardJc.getNum());
                        cardJc.getOcchethCardJcSubList().get(0).setPostNameShow("");
                    }
                    cardJc.getOcchethCardJcSubList().get(0).setBadrsnRowspan(1);
                    cardJc.getOcchethCardJcSubList().get(0).setItemsRowspan(1);
                    cardJc.getOcchethCardJcSubList().get(0).setPostRowspan(1);
                }
            }
            Set<String> ridsSet = new HashSet<>();
            for (TdZwOcchethCardJc cardJc : occhethCard.getOcchethCardJcList()) {
                if (!CollectionUtils.isEmpty(cardJc.getOcchethCardJcSubList()) && cardJc.getOcchethCardJcSubList().size() > 1) {
                    for (TdZwOcchethCardJcSub jcSub : cardJc.getOcchethCardJcSubList()) {
                        if (!countItemMap.isEmpty() && countItemMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid()) != null && ridsSet.add(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid())) {
                            jcSub.setItemsRowspan(countItemMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid()));
                        } else {
                            jcSub.setItermName(null);
                        }
                        if (!countPostMap.isEmpty() && countPostMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName()) != null && ridsSet.add(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName())) {
                            jcSub.setPostRowspan(countPostMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName()));
                        } else {
                            jcSub.setPostNameShow(null);
                        }
                    }
                }
            }

        }
    }
    /**
     * <p>方法描述：列表  排序</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-30
     **/
    public void OcchethCardPjSort() {
        if (!CollectionUtils.isEmpty(occhethCard.getOcchethCardPjList())) {
            //危害因素 排序
            Collections.sort(occhethCard.getOcchethCardPjList(), new Comparator<TdZwOcchethCardPj>() {
                @Override
                public int compare(TdZwOcchethCardPj o1, TdZwOcchethCardPj o2) {
                    Integer num1 = o1.getFkByBadrsnId().getNum();
                    Integer num2 = o2.getFkByBadrsnId().getNum();
                    if (null != num1 && null != num2) {
                        return num1.compareTo(num2);
                    }
                    return 0;
                }
            });
            for (TdZwOcchethCardPj cardPj : occhethCard.getOcchethCardPjList()) {
                if (!CollectionUtils.isEmpty(cardPj.getOcchethCardPjSubList())) {
                    for (TdZwOcchethCardPjSub tdZwOcchethCardPjSub : cardPj.getOcchethCardPjSubList()) {

                        if (null == tdZwOcchethCardPjSub.getFkByItemId() || null == tdZwOcchethCardPjSub.getFkByItemId().getRid()) {
                            tdZwOcchethCardPjSub.setItemNumber(-1);
                        } else if (itemMap.containsKey(tdZwOcchethCardPjSub.getFkByItemId().getRid())) {
                            tdZwOcchethCardPjSub.setItemNumber(itemMap.get(tdZwOcchethCardPjSub.getFkByItemId().getRid()).getNum());
                        }
                        if (null == tdZwOcchethCardPjSub.getIndexRid()) {
                            tdZwOcchethCardPjSub.setIndexNumber(-1);
                        } else if (indexMap.containsKey(tdZwOcchethCardPjSub.getIndexRid())) {
                            tdZwOcchethCardPjSub.setIndexNumber(indexMap.get(tdZwOcchethCardPjSub.getIndexRid()).getNum());
                        }
                    }
                    Collections.sort(cardPj.getOcchethCardPjSubList(), new Comparator<TdZwOcchethCardPjSub>() {
                        @Override
                        public int compare(TdZwOcchethCardPjSub o1, TdZwOcchethCardPjSub o2) {
                            String postName1 = StringUtils.objectToString(o1.getPostName());
                            String postName2 = StringUtils.objectToString(o2.getPostName());

                            String itemNum1 = StringUtils.objectToString(o1.getItemNumber());
                            String itemNum2 = StringUtils.objectToString(o2.getItemNumber());

                            String indexNum1 = StringUtils.objectToString(o1.getIndexNumber());
                            String indexNum2 = StringUtils.objectToString(o2.getIndexNumber());

                            if (!postName1.equals(postName2)) {
                                return postName1.compareTo(postName2);
                            } else if (!itemNum1.equals(itemNum2)) {
                                return itemNum1.compareTo(itemNum2);
                            } else if (!indexNum1.equals(indexNum2)) {
                                return indexNum1.compareTo(indexNum2);
                            }
                            return 0;
                        }
                    });
                }
            }
        }
    }

    @Override
    public void saveAction() {
    }
    /**
     * <p>方法描述：计算行合并</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-30
     **/
    public void computePjRowsSpan() {
        if (!CollectionUtils.isEmpty(occhethCard.getOcchethCardPjList())) {
            Map<String, Integer> countRidMap = new HashMap<>();
            Map<String, Integer> countPostMap = new HashMap<>();
            Map<String, Integer> countItemMap = new HashMap<>();
            Set<String> ridSet = new HashSet<>();
            for (TdZwOcchethCardPj cardPj : occhethCard.getOcchethCardPjList()) {
                if (!CollectionUtils.isEmpty(cardPj.getOcchethCardPjSubList()) && cardPj.getOcchethCardPjSubList().size() > 1) {
                    for (TdZwOcchethCardPjSub pjSub : cardPj.getOcchethCardPjSubList()) {
                        if (countRidMap.get(cardPj.getFkByBadrsnId().getRid().toString()) == null) {
                            countRidMap.put(cardPj.getFkByBadrsnId().getRid().toString(), 1);
                        } else {
                            countRidMap.put(cardPj.getFkByBadrsnId().getRid().toString(), countRidMap.get(cardPj.getFkByBadrsnId().getRid().toString()) + 1);
                        }
                        if (countPostMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName()) == null) {
                            countPostMap.put(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName(), 1);
                        } else {
                            countPostMap.put(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName(), countPostMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName()) + 1);
                        }
                        if (countItemMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid()) == null) {
                            countItemMap.put(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid(), 1);
                        } else {
                            countItemMap.put(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid(), countItemMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid()) + 1);
                        }
                        if (ridSet.add(cardPj.getFkByBadrsnId().getRid().toString())) {
                            pjSub.setBadrsnName(cardPj.getFkByBadrsnId().getRsnCnName());
                            pjSub.setNum(cardPj.getNum());
                            pjSub.setPostNum(cardPj.getPostNum());
                            pjSub.setBadrsnRowspan(cardPj.getOcchethCardPjSubList().size());
                        } else {
                            pjSub.setBadrsnName(null);
                            pjSub.setPostNum(null);
                            pjSub.setNum(null);
                        }
                        if (StringUtils.isNotBlank(pjSub.getPostName()) && pjSub.getFkByItemId() != null && pjSub.getFkByItemId().getRid() != null) {
                            if (ridSet.add(cardPj.getFkByBadrsnId().getRid().toString() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid())) {
                                pjSub.setItermName(itemMap.get(pjSub.getFkByItemId().getRid()).getCodeName());
                                pjSub.getFkByItemId().setFkByItemId(new TsSimpleCode("", itemMap.get(pjSub.getFkByItemId().getRid()).getCodeName()));
                            } else {
                                pjSub.setItermName(null);
                            }
                        } else {
                            pjSub.setItermName(null);
                        }
                        pjSub.setFkByIndexId(indexMap.get(pjSub.getIndexRid()));
                    }
                } else {
                    if (CollectionUtils.isEmpty(cardPj.getOcchethCardPjSubList())) {
                        TdZwOcchethCardPjSub occhethCardPjSub = new TdZwOcchethCardPjSub();
                        occhethCardPjSub.setBadrsnName(cardPj.getFkByBadrsnId().getRsnCnName());
                        occhethCardPjSub.setIfFirst(true);
                        occhethCardPjSub.setFkByMainId(cardPj);
                        occhethCardPjSub.setFkByItemId(new TbYsjcRsnRelItemComm());
                        occhethCardPjSub.setFkByIndexId(new TsSimpleCode());
                        occhethCardPjSub.setFkByMsruntId(new TsSimpleCode());
                        List<TdZwOcchethCardPjSub> subList = new ArrayList<>();
                        subList.add(occhethCardPjSub);
                        cardPj.setOcchethCardPjSubList(subList);
                    }
                    if(cardPj.getPostNum()!=null){
                        cardPj.getOcchethCardPjSubList().get(0).setPostNum(cardPj.getPostNum());
                    }
                    cardPj.getOcchethCardPjSubList().get(0).setBadrsnName(cardPj.getFkByBadrsnId().getRsnCnName());
                    if (StringUtils.isNotBlank(cardPj.getOcchethCardPjSubList().get(0).getPostName())) {
                        cardPj.getOcchethCardPjSubList().get(0).setNum(cardPj.getNum());
                        cardPj.getOcchethCardPjSubList().get(0).getFkByItemId().setFkByItemId(new TsSimpleCode("", itemMap.get(cardPj.getOcchethCardPjSubList().get(0).getFkByItemId().getRid()).getCodeName()));
                        cardPj.getOcchethCardPjSubList().get(0).setItermName(itemMap.get(cardPj.getOcchethCardPjSubList().get(0).getFkByItemId().getRid()).getCodeName());
                        cardPj.getOcchethCardPjSubList().get(0).setFkByIndexId(indexMap.get(cardPj.getOcchethCardPjSubList().get(0).getIndexRid()));
                    } else {
                        cardPj.getOcchethCardPjSubList().get(0).setItermName("");
                        cardPj.getOcchethCardPjSubList().get(0).setNum(cardPj.getNum());
                        cardPj.getOcchethCardPjSubList().get(0).setPostNameShow("");
                    }
                    cardPj.getOcchethCardPjSubList().get(0).setBadrsnRowspan(1);
                    cardPj.getOcchethCardPjSubList().get(0).setItemsRowspan(1);
                    cardPj.getOcchethCardPjSubList().get(0).setPostRowspan(1);
                }
            }
            Set<String> ridsSet = new HashSet<>();
            for (TdZwOcchethCardPj cardPj : occhethCard.getOcchethCardPjList()) {
                if (!CollectionUtils.isEmpty(cardPj.getOcchethCardPjSubList()) && cardPj.getOcchethCardPjSubList().size() > 1) {
                    for (TdZwOcchethCardPjSub pjSub : cardPj.getOcchethCardPjSubList()) {
                        if (!countItemMap.isEmpty() && countItemMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid()) != null && ridsSet.add(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid())) {
                            pjSub.setItemsRowspan(countItemMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid()));
                        } else {
                            pjSub.setItermName(null);
                        }
                        if (!countPostMap.isEmpty() && countPostMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName()) != null && ridsSet.add(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName())) {
                            pjSub.setPostRowspan(countPostMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName()));
                        } else {
                            pjSub.setPostNameShow(null);
                        }
                    }
                }
            }
        }
    }
    /**
     *  <p>方法描述：签发页文书上传前验证</p>
     * @MethodAuthor hsj 2024-08-27 16:01
     */
    public void beforeSignUpload() {
        RequestContext.getCurrentInstance().execute("PF('FileSignDialog').show();");
        RequestContext.getCurrentInstance().update("tabView:editForm:writeSortPanel");
    }
    /**
     *  <p>方法描述：签发页文书上传</p>
     * @MethodAuthor hsj 2024-08-27 16:01
     */
    public void fileSingUpload(FileUploadEvent event) {
        if (null == event) {
           return;
        }
        UploadedFile file = event.getFile();
        try {
            String fileName = file.getFileName();
            String errorMsg = FileUtils.veryFile(file.getInputstream(),file.getContentType(),fileName, "2");
            if (StringUtils.isNotBlank(errorMsg)) {
                JsfUtil.addErrorMessage(errorMsg);
                return;
            }
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            String path = JsfUtil.getAbsolutePath();
            String relativePath = "heth/comm/reportcard/" + uuid + fileName.substring(fileName.lastIndexOf("."));
            // 文件路径
            String filePath = path + relativePath;
            this.occhethCard.setSignAddress(relativePath);
            FileUtils.copyFile(filePath, file.getInputstream());
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("PF('FileSignDialog').hide();");
            currentInstance.update("tabView:editForm:writeSortPanel");
            JsfUtil.addSuccessMessage("上传成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("上传失败！");
            throw new RuntimeException(e);
        }
    }
    /**
     *  <p>方法描述：删除签发页文书</p>
     * @MethodAuthor hsj 2024-08-27 16:02
     */
    public void delSignwrit() {
        try {
            this.occhethCard.setSignAddress(null);
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }
    public Integer getMainRid() {
        return mainRid;
    }

    public void setMainRid(Integer mainRid) {
        this.mainRid = mainRid;
    }

    public TdZwOcchethCard getOcchethCard() {
        return occhethCard;
    }

    public void setOcchethCard(TdZwOcchethCard occhethCard) {
        this.occhethCard = occhethCard;
    }

    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public String getSearchZoneGb() {
        return searchZoneGb;
    }

    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public Date getSearchRptBeginDate() {
        return searchRptBeginDate;
    }

    public void setSearchRptBeginDate(Date searchRptBeginDate) {
        this.searchRptBeginDate = searchRptBeginDate;
    }

    public Date getSearchRptEndDate() {
        return searchRptEndDate;
    }

    public void setSearchRptEndDate(Date searchRptEndDate) {
        this.searchRptEndDate = searchRptEndDate;
    }

    public List<Integer> getSearchStateList() {
        return searchStateList;
    }

    public void setSearchStateList(List<Integer> searchStateList) {
        this.searchStateList = searchStateList;
    }

    public Map<String, TsSimpleCode> getSimpleCodeMap() {
        return simpleCodeMap;
    }

    public void setSimpleCodeMap(Map<String, TsSimpleCode> simpleCodeMap) {
        this.simpleCodeMap = simpleCodeMap;
    }

    public List<TsSimpleCode> getBusinessScopeList() {
        return businessScopeList;
    }

    public void setBusinessScopeList(List<TsSimpleCode> businessScopeList) {
        this.businessScopeList = businessScopeList;
    }

    public Boolean getIfSupportCancel() {
        return ifSupportCancel;
    }

    public void setIfSupportCancel(Boolean ifSupportCancel) {
        this.ifSupportCancel = ifSupportCancel;
    }

    public TdZwOcchethCardJc getTdZwOcchethCardJc() {
        return tdZwOcchethCardJc;
    }

    public void setTdZwOcchethCardJc(TdZwOcchethCardJc tdZwOcchethCardJc) {
        this.tdZwOcchethCardJc = tdZwOcchethCardJc;
    }

    public Map<Integer, TsSimpleCode> getItemMap() {
        return itemMap;
    }

    public void setItemMap(Map<Integer, TsSimpleCode> itemMap) {
        this.itemMap = itemMap;
    }

    public TdZwOcchethCardPj getTdZwOcchethCardPj() {
        return tdZwOcchethCardPj;
    }

    public void setTdZwOcchethCardPj(TdZwOcchethCardPj tdZwOcchethCardPj) {
        this.tdZwOcchethCardPj = tdZwOcchethCardPj;
    }

    public Map<Integer, TsSimpleCode> getIndexMap() {
        return indexMap;
    }

    public void setIndexMap(Map<Integer, TsSimpleCode> indexMap) {
        this.indexMap = indexMap;
    }

    public List<TsSimpleCode> getIndexList() {
        return indexList;
    }

    public void setIndexList(List<TsSimpleCode> indexList) {
        this.indexList = indexList;
    }

}