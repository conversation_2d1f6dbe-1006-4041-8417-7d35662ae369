package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-4
 */
@Entity
@Table(name = "TD_ZXJC_UNITFACTORCROWD")
@SequenceGenerator(name = "TdZxjcUnitfactorcrowd", sequenceName = "TD_ZXJC_UNITFACTORCROWD_SEQ", allocationSize = 1)
public class TdZxjcUnitfactorcrowd implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZxjcUnitbasicinfo fkByMainId;
	private Integer contactTotalPeoples;
	private Integer ifhfDust;
	private Integer hfDustPeoples;
	private Integer ifhfChemistry;
	private Integer hfChemistryPeoples;
	private Integer ifhfPhysics;
	private Integer hfPhysicsPeoples;
	private Integer ifEmit;
	private Integer ifBiological;
	private Integer ifOther;
	private Integer hfBiologyPeoples;
	private Integer hfEmitPeoples;
	private Integer hfOtherPeoples;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZxjcUnitfactorcrowd() {
	}

	public TdZxjcUnitfactorcrowd(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZxjcUnitfactorcrowd")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZxjcUnitbasicinfo getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZxjcUnitbasicinfo fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "CONTACT_TOTAL_PEOPLES")	
	public Integer getContactTotalPeoples() {
		return contactTotalPeoples;
	}

	public void setContactTotalPeoples(Integer contactTotalPeoples) {
		this.contactTotalPeoples = contactTotalPeoples;
	}	
			
	@Column(name = "IFHF_DUST")	
	public Integer getIfhfDust() {
		return ifhfDust;
	}

	public void setIfhfDust(Integer ifhfDust) {
		this.ifhfDust = ifhfDust;
	}	
			
	@Column(name = "HF_DUST_PEOPLES")	
	public Integer getHfDustPeoples() {
		return hfDustPeoples;
	}

	public void setHfDustPeoples(Integer hfDustPeoples) {
		this.hfDustPeoples = hfDustPeoples;
	}	
			
	@Column(name = "IFHF_CHEMISTRY")	
	public Integer getIfhfChemistry() {
		return ifhfChemistry;
	}

	public void setIfhfChemistry(Integer ifhfChemistry) {
		this.ifhfChemistry = ifhfChemistry;
	}	
			
	@Column(name = "HF_CHEMISTRY_PEOPLES")	
	public Integer getHfChemistryPeoples() {
		return hfChemistryPeoples;
	}

	public void setHfChemistryPeoples(Integer hfChemistryPeoples) {
		this.hfChemistryPeoples = hfChemistryPeoples;
	}	
			
	@Column(name = "IFHF_PHYSICS")	
	public Integer getIfhfPhysics() {
		return ifhfPhysics;
	}

	public void setIfhfPhysics(Integer ifhfPhysics) {
		this.ifhfPhysics = ifhfPhysics;
	}	
			
	@Column(name = "HF_PHYSICS_PEOPLES")	
	public Integer getHfPhysicsPeoples() {
		return hfPhysicsPeoples;
	}

	public void setHfPhysicsPeoples(Integer hfPhysicsPeoples) {
		this.hfPhysicsPeoples = hfPhysicsPeoples;
	}	
			
	@Column(name = "IF_EMIT")	
	public Integer getIfEmit() {
		return ifEmit;
	}

	public void setIfEmit(Integer ifEmit) {
		this.ifEmit = ifEmit;
	}	
			
	@Column(name = "IF_BIOLOGICAL")	
	public Integer getIfBiological() {
		return ifBiological;
	}

	public void setIfBiological(Integer ifBiological) {
		this.ifBiological = ifBiological;
	}	
			
	@Column(name = "IF_OTHER")	
	public Integer getIfOther() {
		return ifOther;
	}

	public void setIfOther(Integer ifOther) {
		this.ifOther = ifOther;
	}	
			
	@Column(name = "HF_BIOLOGY_PEOPLES")	
	public Integer getHfBiologyPeoples() {
		return hfBiologyPeoples;
	}

	public void setHfBiologyPeoples(Integer hfBiologyPeoples) {
		this.hfBiologyPeoples = hfBiologyPeoples;
	}	
			
	@Column(name = "HF_EMIT_PEOPLES")	
	public Integer getHfEmitPeoples() {
		return hfEmitPeoples;
	}

	public void setHfEmitPeoples(Integer hfEmitPeoples) {
		this.hfEmitPeoples = hfEmitPeoples;
	}	
			
	@Column(name = "HF_OTHER_PEOPLES")	
	public Integer getHfOtherPeoples() {
		return hfOtherPeoples;
	}

	public void setHfOtherPeoples(Integer hfOtherPeoples) {
		this.hfOtherPeoples = hfOtherPeoples;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}