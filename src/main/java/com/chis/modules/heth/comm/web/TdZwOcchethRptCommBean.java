package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.json.CrptDialogParamJson;
import com.chis.modules.heth.comm.service.ZwIntellReportCommServiceImpl;
import com.chis.modules.heth.comm.utils.QRCodeUtil;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.TreeNode;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

@ManagedBean(name = "tdZwOcchethRptCommBean")
@ViewScoped
public class TdZwOcchethRptCommBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;

    // 查询条件start
    /**
     * 检测（评价）日期 开始时间
     * 开始日期默认当前年1月1日
     */
    private Date rptDateSearchStart;
    /**
     * 检测（评价）日期 结束日期
     * 结束日期默认当前日期
     */
    private Date rptDateSearchEnd;
    /**
     * 单位名称
     * 模糊查询
     */
    private String crptNameSearch;
    /**
     * 管理编号
     * 模糊查询
     */
    private String manageNoSearch;
    /**
     * 技术服务类别
     */
    private List<Integer> sortIdsSearch = new ArrayList<>();
    /**
     * 状态 0：待提交1：已提交
     * 默认 0
     */
    private String[] stateSearch;
    // 查询条件end

    /**
     * 当前职业卫生技术服务机构
     */
    private TdZwOcchethInfoComm tdZwOcchethInfo;
    /**
     * 所有技术服务类别
     */
    private List<SelectItem> jsfwlbList;
    /**
     * 技术服务类别树
     */
    private TreeNode jsfwlbTree;
    /**
     * 技术服务类别的编码
     */
    private Set<String> firstLevelNoSet;
    /**
     * 技术服务类别对象集合
     */
    private List<TsSimpleCode> sortObjList;
    /**
     * 技术服务类别名称
     */
    private String jsfwTreeName;
    /**
     * 技术服务类别编码
     */
    private String jsfwTreeCode;
    /**
     * 查询条件：已选择的技术服务类别树
     */
    private TreeNode[] searchSelJsfw;
    /**
     * 全部的技术服务类别编码
     */
    private String jsfwCodes;

    /**
     * 当前职业卫生技术服务机构申报实体rid
     */
    private Integer rid;
    /**
     * 技术服务类别id
     */
    private Integer sortId;

    /**
     * 当前职业卫生技术服务机构申报实体
     * 用于添加 修改 删除 以及详情
     */
    private TdZwOcchethRptComm tdZwOcchethRpt;

    /**
     * 当前单位
     */
    private TbTjCrpt curCrpt;

    /**
     * 是否申报界面
     */
    private boolean resetFlag = true;

    private String uploadFileName;
    private String uploadFilePath;
    private String uploadEntrustFilePath;
    private String tmpArea;//临时存储所属地区
    /**
     * 技术服务类别资质类型
     */
    private List<Integer> typeList;
    /**
     * 所有技术服务类别
     */
    private List<TsSimpleCode> dataBySqlNoPage;
    /**
     * 用于“技术服务类别”，当选择“职业健康检查总结报告”时 即extend1为1 时候 加载"体检开展方式"
     */
    private List<Integer> tmpExtend1TypeList;
    /**
     * 用于“技术服务类别”，extend1 为2的时候 加载职业病危害风险分类"
     */
    private List<Integer> zybRiskExtend1TypeList;
    /**
     * 用于“技术服务类别”，extend2 为1的时候 判断项目名称是否必填"
     */
    private List<Integer> tmpExtend2TypeList;
    /**
     * 用于“技术服务类别”，extend4 为2的时候 判断项目名称是否必填"
     */
    private List<Integer> tmpExtend4TypeList;
    private List<String> tjExeTypeList;
    private String typeListStr;//详情界面 体检开展方式 文本
    /**
     * 职业病危害风险分类
     */
    private List<TsSimpleCode> zybRiskList;
    /**
     * 详情里的职业病危害风险分类名称
     */
    private String zybRiskStr;
    /**
     * 详情页是否显示二维码
     */
    private String ifShowQRCode;
    /**
     * 二维码显示内容
     */
    private String qrContent;
    /**
     * 生成二维码图片路径
     */
    private String qrPicPath;
    /**
     * 二维码图片名称
     */
    private String qrPicName;
    /**
     * 详情页标识 0：技术服务申报 1：技术服务档案查询
     */
    private String ifView;
    /**
     * 技术服务类别”，extend4 为2
     */
    private Integer ifExtent4Eq2;
    /**
     * 标识 1：委托协议附件上传  2：总结报告附件上传
     */
    private Integer flag;

    private ZwIntellReportCommServiceImpl intellReportServiceImpl = SpringContextHolder.getBean(ZwIntellReportCommServiceImpl.class);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    TsUserInfo user = sessionData.getUser();

    public TdZwOcchethRptCommBean() {
        this.ifSQL = true; //sql 查询
        ifShowQRCode = PropertyUtils.getValueWithoutException("ifShowQRCode");
        if (StringUtils.isNotBlank(ifShowQRCode) && "1".equals(ifShowQRCode)) {
            ifShowQRCode = "1";
        } else {
            ifShowQRCode = "0";
        }
        stateSearch = new String[]{"0"};
        rptDateSearchEnd = DateUtils.getDateOnly(new Date());
        rptDateSearchStart = DateUtils.getYearFirstDay(rptDateSearchEnd);
        tdZwOcchethRpt = new TdZwOcchethRptComm();
        tmpExtend1TypeList = new ArrayList<>();
        zybRiskExtend1TypeList = new ArrayList<>();
        tmpExtend2TypeList = new ArrayList<>();
        typeList = new ArrayList<>();
        zybRiskList = new ArrayList<>();
        validateUnit();// 这个是给 typeList 赋值
        initZybRisk();
        initJsfwLB();
        searchAction();
    }

    private void initZybRisk() {
        StringBuilder sb = new StringBuilder();
        sb.append(" select t from TsSimpleCode t ");
        sb.append(" where t.tsCodeType.codeTypeName in (").append("5511")
                .append(") ");
        sb.append(" and t.ifReveal= 1");
        sb.append(" order by t.num,t.codeLevelNo ,t.codeNo ");//t.num,t.codeLevelNo ,t.codeNo 排序
        zybRiskList = commService.findData(sb.toString(), null);
    }

    /**
     * 技术服务类别初始化
     */
    private void initJsfwLB() {
        sortObjList = new ArrayList<>();
        jsfwlbList = new ArrayList<>();
        this.jsfwlbTree = new CheckboxTreeNode("root", null);
        if (null == dataBySqlNoPage) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TsSimpleCode t ");
            sb.append(" where t.tsCodeType.codeTypeName in (").append("5506")
                    .append(") ");
            sb.append(" and t.ifReveal= 1");
            sb.append(" order by t.num,t.codeLevelNo ,t.codeNo ");//t.num,t.codeLevelNo ,t.codeNo 排序
            dataBySqlNoPage = commService.findData(sb.toString(), null);
        }
        List<TsSimpleCode> tsSimpleCodeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataBySqlNoPage)) {
            tmpExtend1TypeList = new ArrayList<>();
            zybRiskExtend1TypeList = new ArrayList<>();
            tmpExtend2TypeList = new ArrayList<>();
            tmpExtend4TypeList = new ArrayList<>();
            for (TsSimpleCode simpleCode : dataBySqlNoPage) {
                if (StringUtils.isNotBlank(simpleCode.getExtendS1())) {
                    if (!tmpExtend1TypeList.contains(simpleCode.getRid()) &&
                            simpleCode.getExtendS1().trim().equals("1")) {
                        tmpExtend1TypeList.add(simpleCode.getRid());
                    }
                    if (!zybRiskExtend1TypeList.contains(simpleCode.getRid()) &&
                            simpleCode.getExtendS1().trim().equals("2")) {
                        zybRiskExtend1TypeList.add(simpleCode.getRid());
                    }
                }
                if (null != simpleCode.getExtendS2() && 1 == simpleCode.getExtendS2()
                        && !tmpExtend2TypeList.contains(simpleCode.getRid())) {
                    tmpExtend2TypeList.add(simpleCode.getRid());
                }
                if (null != simpleCode.getExtendS4() && "2".equals(simpleCode.getExtendS4())
                        && !tmpExtend4TypeList.contains(simpleCode.getRid())) {
                    tmpExtend4TypeList.add(simpleCode.getRid());
                }

                if (!CollectionUtils.isEmpty(typeList)) {
                    boolean flag = false;
                    for (Integer type : typeList) {
                        if (StringUtils.isNotBlank(simpleCode.getCodeDesc()) &&
                                simpleCode.getCodeDesc().contains(type.toString())) {
                            flag = true;
                            break;
                        }
                    }
                    if (!flag) {
                        continue;
                    }
                }
                tsSimpleCodeList.add(simpleCode);
            }
        }

        if (tsSimpleCodeList != null && tsSimpleCodeList.size() > 0) {
            // 只有第一层
            firstLevelNoSet = new LinkedHashSet<>();
            // 没有第一层
            Set<String> levelNoSet = new LinkedHashSet<>();
            // 所有类别
            Map<String, TsSimpleCode> menuMap = new HashMap<>();
            for (TsSimpleCode objects : tsSimpleCodeList) {
                if (objects.getRid() != null && objects.getCodeName() != null) {
                    jsfwlbList.add(new SelectItem(objects.getRid(), objects.getCodeName()));
                    sortObjList.add(objects);

                    menuMap.put(objects.getRid() + "", objects);
                    /**
                     * 若层级编码不为空，并且不包含“.”,放在第一层 firstLevelNoSet存储所有层级编码
                     */
                    if (StringUtils.containsNone(objects.getRid() + "", ".")) {
                        firstLevelNoSet.add(objects.getRid() + "");
                    } else {
                        /**
                         * 层级编码存在“.”
                         */
                        levelNoSet.add(objects.getRid() + "");
                    }
                }
                for (String ln : firstLevelNoSet) {
                    TsSimpleCode t = menuMap.get(ln);
                    if (null != t) {
                        TreeNode node = new CheckboxTreeNode(t, this.jsfwlbTree);
                        this.addChildNode(ln, levelNoSet, menuMap, node);
                    }
                }

                menuMap.clear();
            }
        }
    }

    @Override
    public void searchAction() {
        // 确认不提示
        if (validateUnit()) {
            //JsfUtil.addErrorMessage("请完善并提交【职业卫生技术服务机构资质】信息！");
            return;
        }
        if (validateSearch()) {
            return;
        }
        super.searchAction();
    }

    @Override
    public void processData(List<?> list) {
        if (null != list && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                Object[] objArr = (Object[]) list.get(i);
                String areaStr = objArr[3] == null ? "" : objArr[3].toString();//所属区域
                if (areaStr.indexOf("_") > 0) {
                    objArr[3] = areaStr.substring(areaStr.indexOf("_") + 1);
                }
            }
        }
    }

    public void addPre() {
        if (validateUnit()) {
            JsfUtil.addErrorMessage("请先至少保存并提交一类机构资质申报信息！");
            return;
        }
        this.addInitAction();
    }

    @Override
    public void addInit() {
        tdZwOcchethRpt = new TdZwOcchethRptComm();
        tdZwOcchethRpt.setState(0);
        tdZwOcchethRpt.setRptDate(new Date());
        curCrpt = new TbTjCrpt();
        this.uploadFileName = null;
        this.uploadFilePath = null;
        uploadEntrustFilePath = null;
        sortId = null;
        tjExeTypeList = new ArrayList<>();
        tmpArea = null;
        this.ifExtent4Eq2 = 0;
    }

    public void preView() {
        resetFlag = true;//其他地方会用 这里需要赋值
        this.viewInitAction();
    }

    /**
     * 弹出不带撤销按钮的页面
     * 需要传递rid参数调用
     */
    public void preViewNoResert() {
        resetFlag = false;
        this.viewInitAction();
    }

    @Override
    public void viewInit() {
        tdZwOcchethRpt = new TdZwOcchethRptComm();
        getTdZwOcchethRptById();
        sortId = tdZwOcchethRpt.getFkBySortId().getRid();
        if (!CollectionUtils.isEmpty(tmpExtend4TypeList) && tmpExtend4TypeList.contains(this.sortId)) {
            this.ifExtent4Eq2 = 1;
        } else {
            this.ifExtent4Eq2 = 0;
        }
        if (null != tdZwOcchethRpt.getChkWayType()) {
            tjExeTypeList = new ArrayList<>();
            StringBuilder builder = new StringBuilder();
            if (3 == tdZwOcchethRpt.getChkWayType()) {
                tjExeTypeList.add("1");
                tjExeTypeList.add("2");
            } else {
                tjExeTypeList.add(tdZwOcchethRpt.getChkWayType().toString());
            }
            if (tjExeTypeList.size() == 2) {
                builder.append("院内体检").append("，").append("外出体检");
            } else if (tjExeTypeList.get(0).equals("1")) {
                builder.append("院内体检");
            } else if (tjExeTypeList.get(0).equals("2")) {
                builder.append("外出体检");
            }
            typeListStr = builder.toString();
        }
    }

    @Override
    public void modInit() {
        tdZwOcchethRpt = new TdZwOcchethRptComm();
        getTdZwOcchethRptById();
        sortId = tdZwOcchethRpt.getFkBySortId().getRid();
        if (!CollectionUtils.isEmpty(tmpExtend4TypeList) && tmpExtend4TypeList.contains(this.sortId)) {
            this.ifExtent4Eq2 = 1;
        } else {
            this.ifExtent4Eq2 = 0;
        }
        if (null != tdZwOcchethRpt.getChkWayType()) {
            tjExeTypeList = new ArrayList<>();
            if (3 == tdZwOcchethRpt.getChkWayType()) {
                tjExeTypeList.add("1");
                tjExeTypeList.add("2");
            } else {
                tjExeTypeList.add(tdZwOcchethRpt.getChkWayType().toString());
            }
        }
    }

    /**
     * 管理编号生成规则
     * 固定前缀+（四位年份）+六位流水号+号
     * 渝职评字（2020）000001号
     */
    @Override
    public void saveAction() {
        if (null == tdZwOcchethRpt.getRid()) {
            tdZwOcchethRpt = (TdZwOcchethRptComm) this.commService.saveObj(tdZwOcchethRpt);
        } else {
            this.commService.update(tdZwOcchethRpt);
        }
    }

    @Override
    public String[] buildHqls() {
        // 获取非标删数据 delMark 为null 或者 0
        StringBuilder builder = new StringBuilder(" from TD_ZW_OCCHETH_RPT T ");
        builder.append(" LEFT JOIN TS_ZONE T1 on T.ZONE_ID = T1.RID ");
        builder.append(" LEFT JOIN TS_SIMPLE_CODE T2 on T.SORT_ID = T2.RID where 1=1 ");
        if (null != rptDateSearchStart) {
            builder.append(" and T.RPT_DATE >= to_date(:rptDateSearchStart,'YYYY-MM-DD HH24:MI:SS') ");
            this.paramMap.put("rptDateSearchStart", DateUtils.formatDate(this.rptDateSearchStart) + " 00:00:00");
        }
        if (null != rptDateSearchEnd) {
            builder.append(" and T.RPT_DATE <= to_date(:rptDateSearchEnd,'YYYY-MM-DD HH24:MI:SS') ");
            this.paramMap.put("rptDateSearchEnd", DateUtils.formatDate(this.rptDateSearchEnd) + " 23:59:59");
        }
        //builder.append("and (((T.DEL_MARK = 0 or T.DEL_MARK is null) and T.STATE = 0) or T.STATE = 1) ");//已经提交 或者未提交未标删的
        builder.append(" and (T.DEL_MARK = 0 or T.DEL_MARK is null) ");//未标删的
        builder.append(" and T.UNIT_ID = :unitId ");
        this.paramMap.put("unitId", Global.getUser().getTsUnit().getRid());
        if (StringUtils.isNotEmpty(crptNameSearch)) {
            builder.append(" and T.CRPT_NAME like :crptName ");
            this.paramMap.put("crptName", "%" + this.crptNameSearch.trim() + "%");
        }
        if (StringUtils.isNotEmpty(manageNoSearch)) {
            builder.append(" and T.MANAGE_NO like :manageNo ");
            this.paramMap.put("manageNo", "%" + this.manageNoSearch.trim() + "%");
        }
        //技术服务类别
        if (StringUtils.isNotEmpty(jsfwTreeCode)) {
            builder.append(" and T.SORT_ID in (");
            builder.append(jsfwTreeCode);
            builder.append(") ");
        }
        if (null != stateSearch && stateSearch.length > 0) {
            builder.append(" and T.STATE in (");
            builder.append(StringUtils.list2string(Arrays.asList(stateSearch), ","));
            builder.append(") ");
        }
        builder.append(" order by T.MANAGE_NO desc "); // T.MANAGE_NO 管理编号自动生成自增 但包含中文 这里可以用rid排序
        String sql = " select T.RID as rid, T.MANAGE_NO as manageNo, T.CRPT_NAME as crptName, T1.FULL_NAME as zongName, T.LINK_MAN as linkMan, T.LINK_PHONE as linkPhone, T.RPT_DATE as rptDate, T2.CODE_NAME as sortName, T.STATE as state, T.FILE_PATH as filePath, T.DEL_MARK as delMark,T.SORT_ID as sortId, T.ZONE_ID as zoneId  " + builder.toString();
        String countSql = " select count(*) " + builder.toString();
        return new String[]{sql, countSql};
    }

    @Override
    public void backAction() {
        setActiveTab(0);
        this.searchAction();
    }

    /**
     * 暂存
     */
    public void saveTmpAction() {
        fillInfo();
        if (saveTmpValidate()) {
            return;
        }
        if (verifyAutoCode()) {
            return;
        }
        saveAction();
        JsfUtil.addSuccessMessage("暂存成功！");
    }

    /**
     * <p>方法描述：流水号的验证</p>
     *
     * @MethodAuthor hsj 2024-09-02 10:58
     */
    private boolean verifyAutoCode() {
        boolean flag = false;
        if (StringUtils.isEmpty(tdZwOcchethRpt.getManageNo())) {
            flag = true;
        } else {
            TdZwOcchethRptComm tdZwOcchethRptBeforeUpdate = this.commService.find(TdZwOcchethRptComm.class, tdZwOcchethRpt.getRid());
            //和原先数据比对 是否重新生成
            if (tdZwOcchethRpt.getFkBySortId().getRid() != tdZwOcchethRptBeforeUpdate.getFkBySortId().getRid().intValue()) {
                flag = true;
            }
        }
        if (flag) {
            // 自动生成编号填充 前缀取规则里的前缀 这里pfx填任意值即可
            try {
                String code = commService.getAutoCode(tdZwOcchethRpt.getFkBySortId().getExtendS3(), null);
                tdZwOcchethRpt.setManageNo(code);
            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("请先维护质控编号的流水号！");
                return true;
            }
        }
        return false;
    }

    /**
     * 提交
     */
    public void submitAction() {
        fillInfo();
        if (submitValidate()) {
            return;
        }
        if (verifyAutoCode()) {
            return;
        }
        tdZwOcchethRpt.setState(1);
        saveAction();
        rid = tdZwOcchethRpt.getRid();
        preView();
        JsfUtil.addSuccessMessage("提交成功！");
    }

    /**
     * 标删
     */
    public void deleteAction() {
        tdZwOcchethRpt = new TdZwOcchethRptComm();
        getTdZwOcchethRptById();
        tdZwOcchethRpt.setDelMark(1);
        if (null != tdZwOcchethRpt.getRid()) {
            commService.update(tdZwOcchethRpt);
        }
    }

    /**
     * 切换技术服务类别的时候 清空体检开展方式 项目名称 以及 职业病危害风险分类
     */
    public void resetTypeList() {
        tjExeTypeList = new ArrayList<>();
        tdZwOcchethRpt.setZybRiskId(null);
        tdZwOcchethRpt.setBhkBeginDate(null);
        tdZwOcchethRpt.setBhkEndDate(null);
        tdZwOcchethRpt.setBhkPsn(null);
        if (!CollectionUtils.isEmpty(tmpExtend4TypeList) && tmpExtend4TypeList.contains(this.sortId)) {
            this.ifExtent4Eq2 = 1;
        } else {
            this.ifExtent4Eq2 = 0;
            tdZwOcchethRpt.setEntrustFilePath(null);
        }
    }

    private void getTdZwOcchethRptById() {
        if (null != rid) {
            tdZwOcchethRpt = this.commService.find(TdZwOcchethRptComm.class, rid);
            curCrpt = tdZwOcchethRpt.getFkByCrptId();
            String hql = "select t from TbTjCrptIndepend t where t.fkByCrptId.institutionCode ='" + curCrpt.getInstitutionCode() + "' "
                    + "and t.fkByUnitId.rid = '" + Global.getUser().getTsUnit().getRid().toString() + "' ";
            List<TbTjCrptIndepend> list = commService.findData(hql, null);
            if (CollectionUtils.isEmpty(list)) {
                curCrpt = new TbTjCrpt();
                if (null == curCrpt.getTsZoneByZoneId()) {
                    curCrpt.setTsZoneByZoneId(new TsZone());
                }
                if (null == curCrpt.getTsSimpleCodeByEconomyId()) {
                    this.curCrpt.setTsSimpleCodeByEconomyId(new TsSimpleCode());
                }
                if (null == curCrpt.getTsSimpleCodeByIndusTypeId()) {
                    curCrpt.setTsSimpleCodeByIndusTypeId(new TsSimpleCode());
                }
                if (null == curCrpt.getTsSimpleCodeByCrptSizeId()) {
                    curCrpt.setTsSimpleCodeByCrptSizeId(new TsSimpleCode());
                }
            }
            this.uploadFileName = tdZwOcchethRpt.getFileName();
            this.uploadFilePath = tdZwOcchethRpt.getFilePath();
            this.uploadEntrustFilePath = tdZwOcchethRpt.getEntrustFilePath();
            if (null != tdZwOcchethRpt.getFkByZoneId()) {
                tmpArea = tdZwOcchethRpt.getFkByZoneId().getFullName();
            }
            if (StringUtils.isNotEmpty(tmpArea)) {
                tmpArea = tmpArea.substring(tmpArea.indexOf("_") + 1);
            }
        }
    }

    /**
     * @Description : 打开用人单位选择框
     * @MethodAuthor: anjing
     * @Date : 2020/2/17 16:48
     **/
    public void selectCrptList() {
        Map<String, Object> options = MapUtils.produceDialogMap(1100, 1070, 520,
                505);
        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        CrptDialogParamJson json = new CrptDialogParamJson();
        json.setIfShowOtherZone(true);
        json.setIfSearchZoneDefault(false);
        json.setBusType("5");
        paramList.add(JSON.toJSONString(json));
        paramMap.put("jsonParam", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        String useNewCrptSelect = this.commService.findParamValue("USE_NEW_CRPT_SELECT");
        String crptSelectPath = "/webapp/heth/comm/crptCommSelectList";
        if ("1".equals(useNewCrptSelect)) {
            crptSelectPath = "/webapp/heth/comm/crptCommSelectListNew";
        }
        requestContext.openDialog(crptSelectPath, options, paramMap);
    }

    /**
     * @Description : 选择用人单位
     * @MethodAuthor: anjing
     * @Date : 2020/2/17 16:49
     **/
    public void onUnitSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            curCrpt = (TbTjCrpt) selectedMap.get("selectCrpt");
            TbTjCrptIndepend crptIndepend = (TbTjCrptIndepend) selectedMap.get("selectCrptIndepend");
            //重新赋值
            this.tdZwOcchethRpt.setFkByCrptId(curCrpt);
            this.tdZwOcchethRpt.setCrptName(curCrpt.getCrptName());
            this.tdZwOcchethRpt.setFkByZoneId(curCrpt.getTsZoneByZoneId());
            this.tdZwOcchethRpt.setCreditCode(curCrpt.getInstitutionCode());
            this.tdZwOcchethRpt.setFkByIndusTypeId(curCrpt.getTsSimpleCodeByIndusTypeId());
            this.tdZwOcchethRpt.setFkByEconomyId(curCrpt.getTsSimpleCodeByEconomyId());
            if (crptIndepend != null) {
                this.tdZwOcchethRpt.setLinkMan(crptIndepend.getLinkman2());
                this.tdZwOcchethRpt.setLinkPhone(crptIndepend.getLinkphone2());
            }
            this.tdZwOcchethRpt.setAddress(curCrpt.getAddress());
            this.tdZwOcchethRpt.setFkByCrptSizeId(curCrpt.getTsSimpleCodeByCrptSizeId());

            if (null != tdZwOcchethRpt.getFkByZoneId()) {
                tmpArea = tdZwOcchethRpt.getFkByZoneId().getFullName();
            }
            if (StringUtils.isNotEmpty(tmpArea)) {
                tmpArea = tmpArea.substring(tmpArea.indexOf("_") + 1);
            }
        }
    }

    /**
     * 暂存验证
     * 1.单位名称不能为空
     * 2.技术服务类别
     * 3.检测（评价）日期
     *
     * @return
     */
    private boolean saveTmpValidate() {
        boolean flag = false;
        if (null == curCrpt || null == curCrpt.getRid()) {
            JsfUtil.addErrorMessage("单位名称不能为空！");
            flag = true;
        }
        if (ifExtent4Eq2 != null && this.ifExtent4Eq2 == 1 && tdZwOcchethRpt.getEntrustFilePath() == null) {
            JsfUtil.addErrorMessage("请上传委托协议附件！");
            flag = true;
        }
        if (null == sortId) {
            JsfUtil.addErrorMessage("请选择技术服务类别！");
            flag = true;
        }
        if (null == tdZwOcchethRpt.getRptDate()) {
            JsfUtil.addErrorMessage("报告日期不能为空！");
            flag = true;
        }
        if (StringUtils.isNotEmpty(tdZwOcchethRpt.getRptNo())) {
            if (flag || rptNoValidate()) {
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 数据填充
     */
    private void fillInfo() {
        TdZwOcchethRptComm tdZwOcchethRptBeforeUpdate = null;
        if (null != tdZwOcchethRpt.getRid()) {
            tdZwOcchethRptBeforeUpdate = this.commService.find(TdZwOcchethRptComm.class, tdZwOcchethRpt.getRid());
        }
        boolean flag = true;

        //未重新选择单位 不修改数据
        if (null != curCrpt && null != curCrpt.getRid()) {
            if (null != tdZwOcchethRptBeforeUpdate &&
                    tdZwOcchethRptBeforeUpdate.getFkByCrptId().getRid() == curCrpt.getRid().intValue()) {
                flag = false;
            } else {
                flag = true;
            }
        } else {
            flag = false;
        }

        if (flag) {
            String hql = "select t from TbTjCrptIndepend t where t.fkByCrptId.rid ='" + curCrpt.getRid() + "' "
                    + "and t.fkByUnitId.rid = '" + Global.getUser().getTsUnit().getRid().toString() + "' and t.busType=5 ";
            List<TbTjCrptIndepend> list = commService.findData(hql, null);
            if (null != list && list.size() > 0) {
                TbTjCrptIndepend tbTjCrptIndepend = list.get(0);
                this.tdZwOcchethRpt.setLinkMan(tbTjCrptIndepend.getLinkman2());
                this.tdZwOcchethRpt.setLinkPhone(tbTjCrptIndepend.getLinkphone2());
            }
            this.tdZwOcchethRpt.setFkByUnitId(Global.getUser().getTsUnit());
            this.tdZwOcchethRpt.setFkByCrptId(curCrpt);
            this.tdZwOcchethRpt.setCrptName(curCrpt.getCrptName());
            this.tdZwOcchethRpt.setFkByZoneId(curCrpt.getTsZoneByZoneId());
            this.tdZwOcchethRpt.setCreditCode(curCrpt.getInstitutionCode());
            this.tdZwOcchethRpt.setFkByIndusTypeId(curCrpt.getTsSimpleCodeByIndusTypeId());
            this.tdZwOcchethRpt.setFkByEconomyId(curCrpt.getTsSimpleCodeByEconomyId());
            this.tdZwOcchethRpt.setAddress(curCrpt.getAddress());
            if (null != tdZwOcchethRpt.getFkByZoneId()) {
                tmpArea = tdZwOcchethRpt.getFkByZoneId().getFullName();
            }
            if (StringUtils.isNotEmpty(tmpArea)) {
                tmpArea = tmpArea.substring(tmpArea.indexOf("_") + 1);
            }
        }

        // 体检开展方式/职业病危害风险分类清空
        if (null != sortId) {
            if (!CollectionUtils.isEmpty(tmpExtend1TypeList) && tmpExtend1TypeList.contains(sortId)) {
                // 清空职业病危害风险分类清空
                tdZwOcchethRpt.setZybRiskId(null);
            }
            if (!CollectionUtils.isEmpty(zybRiskExtend1TypeList) && zybRiskExtend1TypeList.contains(sortId)) {
                // 清空体检开展方式
                tjExeTypeList = new ArrayList<>();
                this.tdZwOcchethRpt.setChkWayType(null);
            }
        }

        if (null != sortId && null != sortObjList && sortObjList.size() > 0) {
            for (TsSimpleCode tmpTs : sortObjList) {
                if (tmpTs.getRid() == sortId.intValue()) {
                    this.tdZwOcchethRpt.setFkBySortId(tmpTs);//技术服务类别
                    break;
                }
            }
            if (tmpExtend1TypeList.contains(sortId) && !CollectionUtils.isEmpty(tjExeTypeList)) {
                if (tjExeTypeList.size() == 2) {
                    this.tdZwOcchethRpt.setChkWayType(3);
                } else if (tjExeTypeList.get(0).equals("1")) {
                    this.tdZwOcchethRpt.setChkWayType(1);
                } else if (tjExeTypeList.get(0).equals("2")) {
                    this.tdZwOcchethRpt.setChkWayType(2);
                }
            }
        }

        if (StringUtils.isNotEmpty(this.uploadFileName)) {
            this.tdZwOcchethRpt.setFileName(uploadFileName);
        }
        if (StringUtils.isNotEmpty(this.uploadFilePath)) {
            this.tdZwOcchethRpt.setFilePath(uploadFilePath);
        }
        if (StringUtils.isNotEmpty(this.uploadEntrustFilePath)) {
            this.tdZwOcchethRpt.setEntrustFilePath(uploadEntrustFilePath);
        }
        if (null == tdZwOcchethRpt.getRid()) {
            tdZwOcchethRpt.setCreateManid(user.getRid());
            tdZwOcchethRpt.setCreateDate(new Date());
        } else {
            tdZwOcchethRpt.setModifyManid(user.getRid());
            tdZwOcchethRpt.setModifyDate(new Date());
        }
    }

    /**
     * 提交验证
     * 1.暂存验证
     * 2.报告编号（唯一性）
     * 3.附件
     *
     * @return
     */
    private boolean submitValidate() {
        boolean flag = saveTmpValidate();
        if (StringUtils.isEmpty(tdZwOcchethRpt.getRptNo())) {
            JsfUtil.addErrorMessage("报告编号不可以为空！");
            flag = true;
        }
        if (this.ifExtent4Eq2 == 1 && tdZwOcchethRpt.getFilePath() == null) {
            JsfUtil.addErrorMessage("请上传总结报告附件！");
            flag = true;
        }
        if (tmpExtend1TypeList.contains(sortId) && CollectionUtils.isEmpty(tjExeTypeList)) {
            JsfUtil.addErrorMessage("体检开展方式不能为空！");
            flag = true;
        }
        if (this.ifExtent4Eq2 == 1 && (null == tdZwOcchethRpt.getBhkBeginDate() || null == tdZwOcchethRpt.getBhkEndDate())) {
            JsfUtil.addErrorMessage("体检日期不能为空！");
            flag = true;
        }
        if (this.ifExtent4Eq2 == 1 && tdZwOcchethRpt.getBhkPsn() == null) {
            JsfUtil.addErrorMessage("体检人数不能为空！");
            flag = true;
        }
        if (zybRiskExtend1TypeList.contains(sortId) && null == tdZwOcchethRpt.getZybRiskId()) {
            JsfUtil.addErrorMessage("职业病危害风险分类不能为空！");
            flag = true;
        }

        if (tmpExtend2TypeList.contains(sortId) && StringUtils.isEmpty(tdZwOcchethRpt.getProjectName())) {
            JsfUtil.addErrorMessage("项目名称不能为空！");
            flag = true;
        }
        return flag;
    }

    /**
     * 报告编号验证
     *
     * @return
     */
    private boolean rptNoValidate() {
        boolean flag = false;
        StringBuilder countSqlBulider = new StringBuilder(" select count(*) from TD_ZW_OCCHETH_RPT t ");
        countSqlBulider.append(" where t.UNIT_ID='" + Global.getUser().getTsUnit().getRid().intValue() + "' ");
        Map<String, Object> paramMap = new HashMap<>();
        countSqlBulider.append("and t.RPT_NO=:rptNo ");
        paramMap.put("rptNo", tdZwOcchethRpt.getRptNo());
        if (null != tdZwOcchethRpt.getRid()) {
            countSqlBulider.append("and t.RID <> '" + tdZwOcchethRpt.getRid().intValue() + "'");
        }

        if (commService.findTotalNumBySQL(countSqlBulider.toString(), paramMap) > 0) {
            JsfUtil.addErrorMessage("报告编号重复！");
            flag = true;
        }
        return flag;
    }

    public void resetOcchethRpt() {
        if (null == tdZwOcchethRpt || null == tdZwOcchethRpt.getRid()) {
            JsfUtil.addErrorMessage("撤销失败！");
            return;
        }
        if (null != tdZwOcchethRpt.getState() && tdZwOcchethRpt.getState() == 1) {
            tdZwOcchethRpt.setState(0);
            this.commService.update(tdZwOcchethRpt);
        }
        //隐藏撤销按钮
        resetFlag = false;
        rid = tdZwOcchethRpt.getRid();
        this.modInitAction();
        JsfUtil.addSuccessMessage("撤销成功！");
    }

    /**
     * 删除附件
     */
    public void delFilePath() {
        if (this.tdZwOcchethRpt != null) {
            if (this.flag == 1) {
                tdZwOcchethRpt.setEntrustFilePath(null);
            } else if (this.flag == 2) {
                tdZwOcchethRpt.setFileName(null);
                tdZwOcchethRpt.setFilePath(null);
            }
        }
        this.uploadFileName = null;
        this.uploadFilePath = null;
        this.uploadEntrustFilePath = null;
    }

    /**
     * 附件上传
     *
     * @param event
     */
    public void fileUpload(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            try {
                String contentType = file.getContentType().toLowerCase();
                String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, file.getFileName(), "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    return;
                }
                String fileName = file.getFileName();// 文件名称
                String uuid = java.util.UUID.randomUUID().toString()
                        .replaceAll("-", "");
                String path = JsfUtil.getAbsolutePath();
                //rpt文件夹
                String relativePath = new StringBuffer("occheth/")
                        .append(uuid)
                        .append(fileName.substring(fileName
                                .lastIndexOf("."))).toString();
                // 文件路径
                String filePath = new StringBuilder(path).append(
                        relativePath).toString();

                FileUtils.copyFile(filePath, file.getInputstream());
                RequestContext.getCurrentInstance().execute(
                        "PF('FileDialog').hide()");
                if (this.flag == 2) {
                    this.uploadFileName = fileName;
                    this.uploadFilePath = relativePath;
                    this.tdZwOcchethRpt.setFilePath(uploadFilePath);
                    this.tdZwOcchethRpt.setFileName(uploadFileName);
                    RequestContext.getCurrentInstance().update("tabView:editForm:btnUpload");
                } else if (this.flag == 1) {
                    this.uploadEntrustFilePath = relativePath;
                    tdZwOcchethRpt.setEntrustFilePath(uploadEntrustFilePath);
                    RequestContext.getCurrentInstance().update("tabView:editForm:entrustBtnUpload");
                }
                JsfUtil.addSuccessMessage("上传成功！");
                RequestContext.getCurrentInstance().update("tabView:editForm:fileDialog");

            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("上传失败！");
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 查询参数验证
     *
     * @return
     */
    private boolean validateSearch() {
        if (null != rptDateSearchEnd && null != rptDateSearchStart && DateUtils.isDateAfter(rptDateSearchStart, rptDateSearchEnd)) {
            JsfUtil.addErrorMessage("结束日期需要大于等于开始日期！");
            return true;
        }
        return false;
    }

    /**
     * 验证是否具有职业卫生技术服务机构资质
     *
     * @return
     */
    private boolean validateUnit() {
        boolean flag = false;
        typeList = new ArrayList<>();
        List<TdZwOcchethInfoComm> list = intellReportServiceImpl.
                selectTdZwOcchethInfoByOrgIdAndState(Global.getUser().getTsUnit().getRid(), 1);
        List<TdZwDiagorginfoComm> diagorgList = intellReportServiceImpl.
                selectTdZwDiagorginfoByOrgIdAndState(Global.getUser().getTsUnit().getRid(), 1);
        List<TdZwSrvorginfoComm> srvorgList = intellReportServiceImpl.
                selectTdZwSrvorginfoByOrgIdAndState(Global.getUser().getTsUnit().getRid(), 1);
        List<TdZwTjorginfoComm> tjorgList = intellReportServiceImpl.
                selectTdZwTjorginfoByOrgIdAndState(Global.getUser().getTsUnit().getRid(), 1);
        if (!CollectionUtils.isEmpty(tjorgList)) {
            typeList.add(1);
        }
        if (!CollectionUtils.isEmpty(srvorgList)) {
            typeList.add(3);
        }
        if (!CollectionUtils.isEmpty(diagorgList)) {
            typeList.add(2);
        }
        if (!CollectionUtils.isEmpty(list)) {
            typeList.add(4);
        }
        initJsfwLB();
        if (CollectionUtils.isEmpty(typeList)) {
            flag = true;
        } else if (!CollectionUtils.isEmpty(list)) {
            this.tdZwOcchethInfo = null;
        }
        return flag;
    }

    /**
     * 构建树
     *
     * @param levelNo    层级编码
     * @param levelNoSet 二级以及以上的层级编码集合
     * @param allMap     所有数据级map
     * @param parentNode 上级树节点
     */
    private void addChildNode(String levelNo, Set<String> levelNoSet, Map allMap, TreeNode parentNode) {
        int level = StringUtils.countMatches(levelNo, ".");
        for (String ln : levelNoSet) {
            if (StringUtils.countMatches(ln, ".") == (level + 1) && StringUtils.startsWith(ln, levelNo + ".")) {
                TreeNode node = new CheckboxTreeNode(allMap.get(ln), parentNode);
                this.addChildNode(ln, levelNoSet, allMap, node);
            }
        }
    }

    /**
     * 清空已经选择的技术服务类别
     */
    public void clearJsfwlb() {
        initJsfwLB();//初始化 是否有必要
        List<TreeNode> children = this.jsfwlbTree.getChildren();
        //第一层级
        if (null != children && children.size() > 0) {
            for (TreeNode node : children) {
                node.setSelected(false);
                node.setPartialSelected(false);
                List<TreeNode> children2 = node.getChildren();
                //第二层级
                if (null != children2 && children2.size() > 0) {
                    for (TreeNode node2 : children2) {
                        node2.setSelected(false);
                        node.setPartialSelected(false);
                    }
                }
            }
        }
        searchSelJsfw = new TreeNode[]{};
        this.jsfwTreeName = null;
        this.jsfwTreeCode = null;
    }

    public void selectJsfwlbAction() {
        this.jsfwTreeName = null;
        this.jsfwTreeCode = null;
        // 遍历选择的技术服务类别，获取选择的id与名称
        if (this.searchSelJsfw != null && searchSelJsfw.length > 0) {

            StringBuilder nameSb = new StringBuilder(); // 分类名称
            StringBuilder ridSb = new StringBuilder(); // 分类Id
            for (TreeNode treeNode : searchSelJsfw) {
                TsSimpleCode simpleCode = (TsSimpleCode) treeNode.getData();
                nameSb.append(",").append(String.valueOf(simpleCode.getCodeName()));
                ridSb.append(",").append(simpleCode.getRid());
            }
            if (nameSb.toString().length() > 0) {
                this.jsfwTreeName = nameSb.toString().substring(1);
                this.jsfwTreeCode = ridSb.toString().substring(1);
                return;
            }
        }
    }

    /**
     * <p>方法描述：二维码弹框</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-07-07
     **/
    public void openQrCodeDiag() {
        if (tdZwOcchethRpt == null) {
            return;
        }
        //拼接二维码显示内容
        this.qrContent = "服务单位：" + tdZwOcchethRpt.getCrptName() + "\n报告单位：" + Global.getUser().getTsUnit().getUnitname();
        if (tdZwOcchethRpt.getFkBySortId() != null && tdZwOcchethRpt.getFkBySortId().getExtendS4() != null && "2".equals(tdZwOcchethRpt.getFkBySortId().getExtendS4())) {
            qrContent += "\n体检日期：" + (tdZwOcchethRpt.getBhkBeginDate() == null ? "" : DateUtils.formatDate(tdZwOcchethRpt.getBhkBeginDate(), "yyyy-MM-dd")) + (tdZwOcchethRpt.getBhkBeginDate() != null && tdZwOcchethRpt.getBhkEndDate() != null ? "~" : "") + (tdZwOcchethRpt.getBhkEndDate() == null ? "" : DateUtils.formatDate(tdZwOcchethRpt.getBhkEndDate(), "yyyy-MM-dd")) + "\n体检人数：" + (tdZwOcchethRpt.getBhkPsn() == null ? "" : tdZwOcchethRpt.getBhkPsn());
        }
        String uuid = java.util.UUID.randomUUID().toString().replaceAll("-", "");
        this.qrPicName = uuid + ".jpg";
        try {
            //判断文件夹路径是否存在
            QRCodeUtil.mkdirs(PropertyUtils.getValue("virtual.directory") + "temp/");
            qrPicPath = "temp/" + qrPicName;
            //生成二维码图片
            QRCodeUtil.encode(qrContent, PropertyUtils.getValue("virtual.directory") + qrPicPath);
            RequestContext.getCurrentInstance().execute("PF('QrCodeDialog').show();");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public Date getRptDateSearchStart() {
        return rptDateSearchStart;
    }

    public void setRptDateSearchStart(Date rptDateSearchStart) {
        this.rptDateSearchStart = rptDateSearchStart;
    }

    public Date getRptDateSearchEnd() {
        return rptDateSearchEnd;
    }

    public void setRptDateSearchEnd(Date rptDateSearchEnd) {
        this.rptDateSearchEnd = rptDateSearchEnd;
    }

    public String getCrptNameSearch() {
        return crptNameSearch;
    }

    public void setCrptNameSearch(String crptNameSearch) {
        this.crptNameSearch = crptNameSearch;
    }

    public String getManageNoSearch() {
        return manageNoSearch;
    }

    public void setManageNoSearch(String manageNoSearch) {
        this.manageNoSearch = manageNoSearch;
    }

    public List<Integer> getSortIdsSearch() {
        return sortIdsSearch;
    }

    public void setSortIdsSearch(List<Integer> sortIdsSearch) {
        this.sortIdsSearch = sortIdsSearch;
    }

    public String[] getStateSearch() {
        return stateSearch;
    }

    public void setStateSearch(String[] stateSearch) {
        this.stateSearch = stateSearch;
    }

    public TdZwOcchethInfoComm getTdZwOcchethInfo() {
        return tdZwOcchethInfo;
    }

    public void setTdZwOcchethInfo(TdZwOcchethInfoComm tdZwOcchethInfo) {
        this.tdZwOcchethInfo = tdZwOcchethInfo;
    }

    public List<SelectItem> getJsfwlbList() {
        return jsfwlbList;
    }

    public void setJsfwlbList(List<SelectItem> jsfwlbList) {
        this.jsfwlbList = jsfwlbList;
    }

    public TreeNode getJsfwlbTree() {
        return jsfwlbTree;
    }

    public void setJsfwlbTree(TreeNode jsfwlbTree) {
        this.jsfwlbTree = jsfwlbTree;
    }

    public Set<String> getFirstLevelNoSet() {
        return firstLevelNoSet;
    }

    public void setFirstLevelNoSet(Set<String> firstLevelNoSet) {
        this.firstLevelNoSet = firstLevelNoSet;
    }

    public List<TsSimpleCode> getSortObjList() {
        return sortObjList;
    }

    public void setSortObjList(List<TsSimpleCode> sortObjList) {
        this.sortObjList = sortObjList;
    }

    public String getJsfwTreeName() {
        return jsfwTreeName;
    }

    public void setJsfwTreeName(String jsfwTreeName) {
        this.jsfwTreeName = jsfwTreeName;
    }

    public String getJsfwTreeCode() {
        return jsfwTreeCode;
    }

    public void setJsfwTreeCode(String jsfwTreeCode) {
        this.jsfwTreeCode = jsfwTreeCode;
    }

    public TreeNode[] getSearchSelJsfw() {
        return searchSelJsfw;
    }

    public void setSearchSelJsfw(TreeNode[] searchSelJsfw) {
        this.searchSelJsfw = searchSelJsfw;
    }

    public String getJsfwCodes() {
        return jsfwCodes;
    }

    public void setJsfwCodes(String jsfwCodes) {
        this.jsfwCodes = jsfwCodes;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Integer getSortId() {
        return sortId;
    }

    public void setSortId(Integer sortId) {
        this.sortId = sortId;
    }

    public TdZwOcchethRptComm getTdZwOcchethRpt() {
        return tdZwOcchethRpt;
    }

    public void setTdZwOcchethRpt(TdZwOcchethRptComm tdZwOcchethRpt) {
        this.tdZwOcchethRpt = tdZwOcchethRpt;
    }

    public TbTjCrpt getCurCrpt() {
        return curCrpt;
    }

    public void setCurCrpt(TbTjCrpt curCrpt) {
        this.curCrpt = curCrpt;
    }

    public String getUploadFileName() {
        return uploadFileName;
    }

    public void setUploadFileName(String uploadFileName) {
        this.uploadFileName = uploadFileName;
    }

    public String getUploadFilePath() {
        return uploadFilePath;
    }

    public void setUploadFilePath(String uploadFilePath) {
        this.uploadFilePath = uploadFilePath;
    }


    public CommServiceImpl getCommService() {
        return commService;
    }

    public void setCommService(CommServiceImpl commService) {
        this.commService = commService;
    }

    public boolean isResetFlag() {
        return resetFlag;
    }

    public void setResetFlag(boolean resetFlag) {
        this.resetFlag = resetFlag;
    }

    public String getTmpArea() {
        return tmpArea;
    }

    public void setTmpArea(String tmpArea) {
        this.tmpArea = tmpArea;
    }

    public List<Integer> getTypeList() {
        return typeList;
    }

    public void setTypeList(List<Integer> typeList) {
        this.typeList = typeList;
    }

    public List<TsSimpleCode> getDataBySqlNoPage() {
        return dataBySqlNoPage;
    }

    public void setDataBySqlNoPage(List<TsSimpleCode> dataBySqlNoPage) {
        this.dataBySqlNoPage = dataBySqlNoPage;
    }

    public List<Integer> getTmpExtend1TypeList() {
        return tmpExtend1TypeList;
    }

    public void setTmpExtend1TypeList(List<Integer> tmpExtend1TypeList) {
        this.tmpExtend1TypeList = tmpExtend1TypeList;
    }

    public List<String> getTjExeTypeList() {
        return tjExeTypeList;
    }

    public void setTjExeTypeList(List<String> tjExeTypeList) {
        this.tjExeTypeList = tjExeTypeList;
    }

    public String getTypeListStr() {
        return typeListStr;
    }

    public void setTypeListStr(String typeListStr) {
        this.typeListStr = typeListStr;
    }

    public List<Integer> getZybRiskExtend1TypeList() {
        return zybRiskExtend1TypeList;
    }

    public void setZybRiskExtend1TypeList(List<Integer> zybRiskExtend1TypeList) {
        this.zybRiskExtend1TypeList = zybRiskExtend1TypeList;
    }

    public List<Integer> getTmpExtend2TypeList() {
        return tmpExtend2TypeList;
    }

    public void setTmpExtend2TypeList(List<Integer> tmpExtend2TypeList) {
        this.tmpExtend2TypeList = tmpExtend2TypeList;
    }

    public List<TsSimpleCode> getZybRiskList() {
        return zybRiskList;
    }

    public void setZybRiskList(List<TsSimpleCode> zybRiskList) {
        this.zybRiskList = zybRiskList;
    }

    public String getZybRiskStr() {
        if (null != tdZwOcchethRpt && null != tdZwOcchethRpt.getZybRiskId() && !CollectionUtils.isEmpty(zybRiskList)) {
            for (TsSimpleCode simpleCode : zybRiskList) {
                if (simpleCode.getRid().intValue() == tdZwOcchethRpt.getZybRiskId()) {
                    return simpleCode.getCodeName();
                }
            }
        }
        return "";
    }

    public void setZybRiskStr(String zybRiskStr) {
        this.zybRiskStr = zybRiskStr;
    }

    public String getIfShowQRCode() {
        return ifShowQRCode;
    }

    public void setIfShowQRCode(String ifShowQRCode) {
        this.ifShowQRCode = ifShowQRCode;
    }

    public String getQrContent() {
        return qrContent;
    }

    public void setQrContent(String qrContent) {
        this.qrContent = qrContent;
    }

    public String getQrPicPath() {
        return qrPicPath;
    }

    public void setQrPicPath(String qrPicPath) {
        this.qrPicPath = qrPicPath;
    }

    public String getQrPicName() {
        return qrPicName;
    }

    public void setQrPicName(String qrPicName) {
        this.qrPicName = qrPicName;
    }

    public String getIfView() {
        return ifView;
    }

    public void setIfView(String ifView) {
        this.ifView = ifView;
    }

    public List<Integer> getTmpExtend4TypeList() {
        return tmpExtend4TypeList;
    }

    public void setTmpExtend4TypeList(List<Integer> tmpExtend4TypeList) {
        this.tmpExtend4TypeList = tmpExtend4TypeList;
    }

    public Integer getIfExtent4Eq2() {
        return ifExtent4Eq2;
    }

    public void setIfExtent4Eq2(Integer ifExtent4Eq2) {
        this.ifExtent4Eq2 = ifExtent4Eq2;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public String getUploadEntrustFilePath() {
        return uploadEntrustFilePath;
    }

    public void setUploadEntrustFilePath(String uploadEntrustFilePath) {
        this.uploadEntrustFilePath = uploadEntrustFilePath;
    }
}
