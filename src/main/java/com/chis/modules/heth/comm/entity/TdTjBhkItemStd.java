package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-5-12
 */
@Entity
@Table(name = "TD_TJ_BHK_ITEM_STD")
@SequenceGenerator(name = "TdTjBhkItemStd", sequenceName = "TD_TJ_BHK_ITEM_STD_SEQ", allocationSize = 1)
public class TdTjBhkItemStd implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdTjBhk fkByBhkId;
	private TdZwBadrsnItemComm fkByItemStdId;
	private Integer nostdFlag;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdTjBhkItemStd() {
	}

	public TdTjBhkItemStd(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjBhkItemStd")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "BHK_ID")			
	public TdTjBhk getFkByBhkId() {
		return fkByBhkId;
	}

	public void setFkByBhkId(TdTjBhk fkByBhkId) {
		this.fkByBhkId = fkByBhkId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_STD_ID")			
	public TdZwBadrsnItemComm getFkByItemStdId() {
		return fkByItemStdId;
	}

	public void setFkByItemStdId(TdZwBadrsnItemComm fkByItemStdId) {
		this.fkByItemStdId = fkByItemStdId;
	}	
			
	@Column(name = "NOSTD_FLAG")	
	public Integer getNostdFlag() {
		return nostdFlag;
	}

	public void setNostdFlag(Integer nostdFlag) {
		this.nostdFlag = nostdFlag;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}