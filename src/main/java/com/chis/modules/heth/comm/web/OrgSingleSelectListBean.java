package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 机构单位单选弹出框通用
 * <pre>查询条件：地区，机构名称</pre>
 * <pre>查询结果：地区，机构名称</pre>
 */
@ManagedBean(name = "orgSingleSelectListBean")
@ViewScoped
public class OrgSingleSelectListBean {
    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
     * 查询条件：地区：默认为省
     */
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
    private String searchOrgName;
    /**
     * 查询条件：机构名称
     */
    private String orgName;
    /**
     * 弹出框标题
     */
    private String titleName;
    /**
     * 机构类型
     * <pre>0：职业健康检查机构</pre>
     * <pre>1：放射卫生服务机构</pre>
     * <pre>2：职业病诊断机构</pre>
     * <pre>3：职业卫生技术服务机构</pre>
     */
    private String orgType;
    /**
     * 排除的机构id
     */
    private List<String> excludedOrgIdList;
    private List<String> excludedUnitIdList;
    private List<Object[]> allDataList;
    private List<Object[]> showDataList;
    private Object[] selectData;

    public OrgSingleSelectListBean() {
        String zongGb = JsfUtil.getRequestParameter("zongGb");
        initZone(zongGb);
        this.orgType = StringUtils.objectToString(JsfUtil.getRequestParameter("orgType"));
        switch (this.orgType) {
            case "0":
                this.titleName = "职业健康检查机构选择";
                break;
            case "1":
                this.titleName = "放射卫生服务机构选择";
                break;
            case "2":
                this.titleName = "职业病诊断机构选择";
                break;
            case "3":
                this.titleName = "职业卫生技术服务机构选择";
                break;
            default:
                this.titleName = "机构选择";
                break;
        }
        String excludedOrgIds = StringUtils.objectToString(JsfUtil.getRequestParameter("excludedOrgIds"));
        this.excludedOrgIdList = StringUtils.string2list(excludedOrgIds, ",");
        String excludedUnitIds = StringUtils.objectToString(JsfUtil.getRequestParameter("excludedUnitIds"));
        this.excludedUnitIdList = StringUtils.string2list(excludedUnitIds, ",");
        initAllData();
        searchAction();
    }

    private void initZone(String zoneCode) {
        if (StringUtils.isBlank(zoneCode)) {
            zoneCode = Global.getUser().getTsUnit().getTsZone().getZoneGb().substring(0, 2);
        }
        // 地区初始化
        this.zoneList = this.commService.findZoneListByGbAndType(zoneCode, true, "", "");
        this.searchZoneCode = this.zoneList.get(0).getZoneGb();
        this.searchZoneName = this.zoneList.get(0).getZoneName();
    }

    public void initAllData() {
        this.allDataList = new ArrayList<>();
        String sql = "SELECT T.RID AS ORG_ID, U.RID AS UNIT_ID, Z.FULL_NAME, T.ORG_NAME, Z.ZONE_GB FROM ";
        switch (this.orgType) {
            case "0":
                sql += " TD_ZW_TJORGINFO ";
                break;
            case "1":
                sql += " TD_ZW_SRVORGINFO ";
                break;
            case "2":
                sql += " TD_ZW_DIAGORGINFO ";
                break;
            case "3":
                sql += " TD_ZW_OCCHETH_INFO ";
                break;
            default:
                return;
        }
        sql += " T ";
        sql += "    LEFT JOIN TS_UNIT U ON T.ORG_ID = U.RID ";
        sql += "    LEFT JOIN TS_ZONE Z ON U.ZONE_ID = Z.RID ";
        sql += " WHERE T.STATE = 1 ";
        // 地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sql += "AND Z.ZONE_GB LIKE '" + ZoneUtil.zoneSelect(this.searchZoneCode) + "%' ";
        }
        sql += " ORDER BY Z.ZONE_GB, T.ORG_NAME ";
        List<Object[]> dataList = CollectionUtil.castList(
                Object[].class, this.commService.findDataBySqlNoPage(sql, null)
        );
        for (Object[] data : dataList) {
            String orgId = StringUtils.objectToString(data[0]);
            String unitId = StringUtils.objectToString(data[1]);
            if (this.excludedOrgIdList.contains(orgId) || this.excludedUnitIdList.contains(unitId)) {
                continue;
            }
            String fullName = StringUtils.objectToString(data[2]);
            data[2] = ZoneUtil.removeProvByFullName(fullName);
            this.allDataList.add(data);
        }
    }

    public void searchAction() {
        this.showDataList = new ArrayList<>();
        for (Object[] data : this.allDataList) {
            String orgNameStr = StringUtils.objectToString(data[3]);
            if (StringUtils.isNotBlank(this.orgName) && !orgNameStr.contains(this.orgName)) {
                continue;
            }
            String zoneGbStr = StringUtils.objectToString(data[4]);
            if (!zoneGbStr.startsWith(ZoneUtil.zoneSelect(this.searchZoneCode))) {
                continue;
            }
            this.showDataList.add(data);
        }
        this.selectData = null;
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                .findComponent("orgForm:dataTable");
        dataTable.setFirst(0);
    }

    public void selectOrgAction() {
        Map<String, Object> map = new HashMap<>();
        map.put("selectOrg", this.selectData);
        RequestContext.getCurrentInstance().closeDialog(map);
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchOrgName() {
        return searchOrgName;
    }

    public void setSearchOrgName(String searchOrgName) {
        this.searchOrgName = searchOrgName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getTitleName() {
        return titleName;
    }

    public void setTitleName(String titleName) {
        this.titleName = titleName;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public List<String> getExcludedOrgIdList() {
        return excludedOrgIdList;
    }

    public void setExcludedOrgIdList(List<String> excludedOrgIdList) {
        this.excludedOrgIdList = excludedOrgIdList;
    }

    public List<String> getExcludedUnitIdList() {
        return excludedUnitIdList;
    }

    public void setExcludedUnitIdList(List<String> excludedUnitIdList) {
        this.excludedUnitIdList = excludedUnitIdList;
    }

    public List<Object[]> getAllDataList() {
        return allDataList;
    }

    public void setAllDataList(List<Object[]> allDataList) {
        this.allDataList = allDataList;
    }

    public List<Object[]> getShowDataList() {
        return showDataList;
    }

    public void setShowDataList(List<Object[]> showDataList) {
        this.showDataList = showDataList;
    }

    public Object[] getSelectData() {
        return selectData;
    }

    public void setSelectData(Object[] selectData) {
        this.selectData = selectData;
    }
}
