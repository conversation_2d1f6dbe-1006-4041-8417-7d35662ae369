package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.math.BigInteger;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-7-21
 */
@Entity
@Table(name = "TD_TJ_TCH_BADRSNS")
@SequenceGenerator(name = "TdTjTchBadrsns", sequenceName = "TD_TJ_TCH_BADRSNS_SEQ", allocationSize = 1)
public class TdTjTchBadrsns implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private BigInteger rid;
	private TdTjBhk fkByBhkId;
	private TsSimpleCode fkByBadrsnId;
	private Date createDate;
	private Integer createManid;
	
	public TdTjTchBadrsns() {
	}

	public TdTjTchBadrsns(BigInteger rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjTchBadrsns")
	public BigInteger getRid() {
		return this.rid;
	}

	public void setRid(BigInteger rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "BHK_ID")			
	public TdTjBhk getFkByBhkId() {
		return fkByBhkId;
	}

	public void setFkByBhkId(TdTjBhk fkByBhkId) {
		this.fkByBhkId = fkByBhkId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BADRSN_ID")			
	public TsSimpleCode getFkByBadrsnId() {
		return fkByBadrsnId;
	}

	public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
		this.fkByBadrsnId = fkByBadrsnId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
}