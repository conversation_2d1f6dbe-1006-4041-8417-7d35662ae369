package com.chis.modules.heth.comm.web.gxjtymys;

import org.primefaces.event.TabChangeEvent;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.List;

@ManagedBean(name = "chemicalToxicityAssessOrgBean")
@ViewScoped
public class ChemicalToxicityAssessOrgBean {
    private String perPageSize = "20,50,100";
    private String tabTitle;

    public ChemicalToxicityAssessOrgBean(){
    }
    /**
     * <p>方法描述： 切换tab </p>
     * @MethodAuthor： pw 2023/4/19
     **/
    public void tableChange(TabChangeEvent event) {
        this.tabTitle = event.getTab().getTitle();
    }

    public String getPerPageSize() {
        return perPageSize;
    }

    public void setPerPageSize(String perPageSize) {
        this.perPageSize = perPageSize;
    }

    public String getTabTitle() {
        return tabTitle;
    }

    public void setTabTitle(String tabTitle) {
        this.tabTitle = tabTitle;
    }
}
