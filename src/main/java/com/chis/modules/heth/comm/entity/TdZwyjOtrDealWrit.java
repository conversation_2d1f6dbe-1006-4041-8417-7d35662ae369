package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-11-16
 */
@Entity
@Table(name = "TD_ZWYJ_OTR_DEAL_WRIT")
@SequenceGenerator(name = "TdZwyjOtrDealWrit", sequenceName = "TD_ZWYJ_OTR_DEAL_WRIT_SEQ", allocationSize = 1)
public class TdZwyjOtrDealWrit implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwyjOtrWarn fkByMainId;
	private String noticeUnit;
	private String writContent;
	private String dealUnit;
	private String writAnnexPath;
	private Date writDate;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjOtrDealWrit() {
	}

	public TdZwyjOtrDealWrit(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjOtrDealWrit")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwyjOtrWarn getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwyjOtrWarn fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "NOTICE_UNIT")	
	public String getNoticeUnit() {
		return noticeUnit;
	}

	public void setNoticeUnit(String noticeUnit) {
		this.noticeUnit = noticeUnit;
	}	
			
	@Column(name = "WRIT_CONTENT")	
	public String getWritContent() {
		return writContent;
	}

	public void setWritContent(String writContent) {
		this.writContent = writContent;
	}	
			
	@Column(name = "DEAL_UNIT")	
	public String getDealUnit() {
		return dealUnit;
	}

	public void setDealUnit(String dealUnit) {
		this.dealUnit = dealUnit;
	}	
			
	@Column(name = "WRIT_ANNEX_PATH")	
	public String getWritAnnexPath() {
		return writAnnexPath;
	}

	public void setWritAnnexPath(String writAnnexPath) {
		this.writAnnexPath = writAnnexPath;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "WRIT_DATE")			
	public Date getWritDate() {
		return writDate;
	}

	public void setWritDate(Date writDate) {
		this.writDate = writDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}