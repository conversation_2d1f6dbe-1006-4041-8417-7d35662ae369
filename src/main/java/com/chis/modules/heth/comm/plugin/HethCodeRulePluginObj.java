package com.chis.modules.heth.comm.plugin;

import com.chis.modules.system.entity.TsCodeRule;
import com.chis.modules.system.enumn.SystemType;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 职卫平台自动编号规则的插件
 * <AUTHOR>
 * @createDate 2014年8月29日 上午11:06:07
 * @LastModify LuXuekun
 * @ModifyDate 2014年8月29日 上午11:06:07
 */
public class HethCodeRulePluginObj {

	public static Set<TsCodeRule> ruleSet;

	/**
	 * 初始化
	 */
	static {
		ruleSet = new HashSet<TsCodeRule>();
		
		ruleSet.add(new TsCodeRule("HETH_YSZYB_RPT_CODE", SystemType.ZYWS_TY,"疑似职业病报告卡流水编号",null,"",null,(short)5,"com.chis.ejb.service.autocode.impl.CodeLiquidServiceImpl", new Date(), 1));
		ruleSet.add(new TsCodeRule("HETH_ZYB_RPT_CODE", SystemType.ZYWS_TY,"职业病报告卡流水编号",null,"",null,(short)5,"com.chis.ejb.service.autocode.impl.CodeLiquidServiceImpl", new Date(), 1));
		ruleSet.add(new TsCodeRule("HETH_SRVORG_CARD_CODE", SystemType.ZYWS_TY,"放射卫生技术服务信息报送卡流水编号",null,"",null,(short)5,"com.chis.ejb.service.autocode.impl.CodeLiquidServiceImpl", new Date(), 1));
		ruleSet.add(new TsCodeRule("OCCHETH_CARD_CODE", SystemType.ZYWS_TY,"职业卫生技术服务信息报送卡流水编号",null,"",null,(short)5,"com.chis.ejb.service.autocode.impl.CodeLiquidServiceImpl", new Date(), 1));
		ruleSet.add(new TsCodeRule("SMARY_CARD_CODE", SystemType.ZYWS_TY,"职业性有害因素监测卡流水编号",null,"",null,(short)5,"com.chis.ejb.service.autocode.impl.CodeLiquidServiceImpl", new Date(), 1));
		//技术服务类别生成流水号
		ruleSet.add(new TsCodeRule("ZYWS_TY_YPJ_CODE", SystemType.ZYWS_TY,"职业病危害预评价报告流水编号","苏质控（建预）字","","号",(short)5,"com.chis.ejb.service.autocode.impl.CodeDynamicBeforeDateServiceImpl", new Date(), 1));
		ruleSet.add(new TsCodeRule("ZYWS_TY_SJ_CODE", SystemType.ZYWS_TY,"职业病防护设施设计专篇流水编号","苏质控（建专）字","","号",(short)5,"com.chis.ejb.service.autocode.impl.CodeDynamicBeforeDateServiceImpl", new Date(), 1));
		ruleSet.add(new TsCodeRule("ZYWS_TY_KZXGPJ_CODE", SystemType.ZYWS_TY,"职业病危害控制效果评价报告流水编号","苏质控（建控）字","","号",(short)5,"com.chis.ejb.service.autocode.impl.CodeDynamicBeforeDateServiceImpl", new Date(), 1));
		ruleSet.add(new TsCodeRule("ZYWS_TY_XZPJ_CODE", SystemType.ZYWS_TY,"职业病危害现状评价流水编号","苏质控（现状）字","","号",(short)5,"com.chis.ejb.service.autocode.impl.CodeDynamicBeforeDateServiceImpl", new Date(), 1));
		ruleSet.add(new TsCodeRule("ZYWS_TY_DQJC_CODE", SystemType.ZYWS_TY,"定期检测报告流水编号","苏质控（定期）字","","号",(short)5,"com.chis.ejb.service.autocode.impl.CodeDynamicBeforeDateServiceImpl", new Date(), 1));
		ruleSet.add(new TsCodeRule("ZYWS_TY_YPXGPJ_CODE", SystemType.ZYWS_TY,"个体防护用品效果评价流水编号","苏质控（个防）字","","号",(short)5,"com.chis.ejb.service.autocode.impl.CodeDynamicBeforeDateServiceImpl", new Date(), 1));
		ruleSet.add(new TsCodeRule("ZYWS_TY_SSXGPJ_CODE", SystemType.ZYWS_TY,"职业病防护设施效果评价流水编号","苏质控（设防）字","","号",(short)5,"com.chis.ejb.service.autocode.impl.CodeDynamicBeforeDateServiceImpl", new Date(), 1));
		ruleSet.add(new TsCodeRule("ZYWS_TY_XGPJ_CODE", SystemType.ZYWS_TY,"职业病防护设备设施与防护用品的效果评价流水编号","苏职质控（效果）","","号",(short)5,"com.chis.ejb.service.autocode.impl.CodeDynamicBeforeDateServiceImpl", new Date(), 1));

		ruleSet.add(new TsCodeRule("ZYWS_VIRTUAL_CREDIT_CODE", SystemType.ZYWS_TY, "虚拟社会信用代码", null, null, null, (short) 3,
				"com.chis.ejb.service.autocode.impl.CodeDynamicDateServiceImpl", new Date(), 1));
	}

    public Set<TsCodeRule> getRuleSet() {
        return ruleSet;
    }
}
