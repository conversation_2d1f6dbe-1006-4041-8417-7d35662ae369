package com.chis.modules.heth.comm.plugin;

import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.enumn.SystemType;

import java.util.HashSet;
import java.util.Set;

/**
 * 码表的插件 码表编码是写死的
 *
 * <AUTHOR>
 * @createDate 2014年8月29日 上午11:07:02
 * @LastModify LuXuekun
 * @ModifyDate 2014年8月29日 上午11:07:02
 */
public class HethTsCodePluginObj {

    public static Set<TsCodeType> codeTypeSet;
    public static Set<TsSimpleCode> simpleCodeSet;

    static {
        codeTypeSet = new HashSet<TsCodeType>();
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5501", "计量单位", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5502", "工种", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5503", "证件类型", (short) 0, (short) 0));
        //在线申报企业
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5508", "变更原因", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5316", "信息来源", (short) 0, (short) 0));
        //企业信息来源
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5512", "企业信息来源", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5513", "非正常生产情况", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5514", "开采方式", (short) 0, (short) 0));
        //体检项目标记
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5515", "体检项目标记", (short) 0, (short) 0));
        //职业性有害因素检测卡统计危害因素
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5537", "职业性有害因素检测卡统计危害因素", (short) 0, (short) 0));
        //浓度（强度）类型（职业健康检查汇总卡）
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5532", "浓度（强度）类型（职业健康检查汇总卡）", (short) 0, (short) 0));
        
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5543", "鉴定类型", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5544", "首次鉴定结论", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5545", "再次鉴定结论", (short) 0, (short) 0));
        //用人单位综合展示-异常信息
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5549", "异常信息", (short) 0, (short) 0));
        //项目组合
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5012", "项目组合", (short) 1, (short) 0));
        //符号（体检结果子表）
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5576", "符号（体检结果子表）", (short) 0, (short) 0));
        //承担的服务事项
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5577", "承担的服务事项", (short) 0, (short) 0));
        //服务单位类型
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5578", "服务单位类型", (short) 0, (short) 0));
        //超标点位放射性危害类型
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5579", "超标点位放射性危害类型", (short) 0, (short) 0));
        //技术服务领域
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5580", "技术服务领域", (short) 0, (short) 0));
        //检测项目（场所检测国家系统字典）
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5581", "检测项目（场所检测国家系统字典）", (short) 0, (short) 0));
        //用人单位-健康企业建设情况
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5589", "健康企业建设情况", (short) 0, (short) 0));
        //主动监测
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5595", "重点岗位/环节", (short) 0, (short) 0));
        //目前吸烟情况
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5611", "目前吸烟情况", (short) 0, (short) 0));
        //企业预警信息
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5602", "企业预警信息", (short) 0, (short) 0));
        //预警处置结果
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5603", "预警处置结果", (short) 0, (short) 0));
        //防护用品佩戴情况
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5618", "防护用品佩戴情况", (short) 0, (short) 0));
        //听力检测异常类型
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5627", "听力检测异常类型", (short) 0, (short) 0));
        //听力检测结论
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5622", "听力检测结论", (short) 0, (short) 0));

        //技术服务类别
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5506", "技术服务类别", (short) 0, (short) 0));
        //职业病危害风险分类
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5511", "职业病危害风险分类", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZYWS_TY, "5646", "开展放射诊疗设备质量控制检测-检测类型", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.BADRSN_CHECK, "5633", "技术服务结果", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.BADRSN_CHECK, "5647", "关键不符合项", (short) 0, (short) 0));
    }
}
