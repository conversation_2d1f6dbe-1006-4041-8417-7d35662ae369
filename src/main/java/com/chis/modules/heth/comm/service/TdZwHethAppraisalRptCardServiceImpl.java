package com.chis.modules.heth.comm.service;

import com.chis.modules.heth.comm.entity.TdZwHethChkSmaryComm;
import com.chis.modules.heth.comm.entity.TdZwHethJcSubComm;
import com.chis.modules.heth.comm.entity.TdZwjdArchivesCard;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.utils.ZwArchivesCardCommUtils;
import com.chis.modules.heth.comm.utils.ZwChkSmaryCommUtils;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import java.util.Date;
import java.util.List;

/**
 * <p>类描述： 职业病鉴定报告卡 服务实现类</p>
 * @ClassAuthor: yzz
 * @date： 2021年11月09日
 **/
@Service
@Transactional(readOnly = true)
public class TdZwHethAppraisalRptCardServiceImpl extends AbstractTemplate {


    /**
     *  <p>方法描述：保存</p>
     * @MethodAuthor hsj
     */
    @Transactional(readOnly = false)
    public void saveArchivesCard(TdZwjdArchivesCard archivesCard, TdZwBgkLastSta tdZwBgkLastSta) {
        if (null == archivesCard) {
            return;
        }
        ZwArchivesCardCommUtils.clearArchivesCardEntity(archivesCard);
        super.upsertEntity(archivesCard);
        // 最新流程
        if (null != tdZwBgkLastSta) {
            tdZwBgkLastSta.setBusId(archivesCard.getRid());
            this.upsertEntity(tdZwBgkLastSta);
        }
    }

    /**
     *  <p>方法描述：查询职业病鉴定报告卡 信息</p>
     * @MethodAuthor yzz
     */
    public TdZwjdArchivesCard searchAppraisalRptCard(Integer rid) {
        if (null==rid) {
            return null;
        }
        TdZwjdArchivesCard archivesCard = super.find(TdZwjdArchivesCard.class, rid);

        return archivesCard;
    }

    public void submitArchivesCard(TdZwjdArchivesCard archivesCard, TdZwBgkLastSta tdZwBgkLastSta) {
        saveArchivesCard(archivesCard,tdZwBgkLastSta);
        //流程记录
        saveOrgTdZwBgkFlow(archivesCard.getRid(), 8, Global.getUser().getTsUnit().getTsZone());
    }
    /**
     *  <p>方法描述：机构填报流程记录</p>
     * @MethodAuthor hsj
     */
    @Transactional(readOnly = false)
    private void saveOrgTdZwBgkFlow(Integer busId, Integer cardType, TsZone zone) {
        if (null == busId || null == cardType) {
            return;
        }
        // 新增流程记录
        TdZwBgkFlow bgkFlow = new TdZwBgkFlow();
        bgkFlow.setBusId(busId);
        bgkFlow.setCartType(cardType);
        bgkFlow.setRcvDate(new Date());
        bgkFlow.setFkBySmtPsnId(Global.getUser());
        if ("1".equals(zone.getIfCityDirect())) {//市直属
            bgkFlow.setOperFlag(33);
        } else if ("1".equals(zone.getIfProvDirect())){//省直属
            bgkFlow.setOperFlag(44);
        } else {
            bgkFlow.setOperFlag(21);
        }
        preInsert(bgkFlow);
        this.save(bgkFlow);
    }




    /**
     * @Description: 查询历次审核意见
     *
     * @MethodAuthor pw,2021年11月10日
     */
    public List<Object[]>  findHisotryList(Integer rid,Integer cartType){
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(" SELECT ")
                .append(" T1.CART_TYPE,")
                .append(" T1.AUDIT_ADV,")
                .append(" T1.AUDIT_MAN,")
                .append(" T1.CREATE_DATE,")
                .append(" T2.CRPT_NAME,")
                .append(" T3.IF_CITY_DIRECT,")
                .append(" T3.IF_PROV_DIRECT,")
                .append(" T1.OPER_FLAG").append(" FROM TD_ZW_BGK_FLOW T1 ");
        sqlBuilder.append(" LEFT JOIN TD_ZWJD_ARCHIVES_CARD T2 ON T1.BUS_ID = T2.RID ")
                .append(" LEFT JOIN TS_UNIT T4 ON T2.FILL_UNIT_ID = T4.RID ")
                .append(" LEFT JOIN TS_ZONE T3 ON T4.ZONE_ID = T3.RID ");
        sqlBuilder.append(" WHERE T1.BUS_ID = ").append(rid).append(" AND T1.CART_TYPE =  ").append(cartType);
        sqlBuilder.append(" and T1.AUDIT_ADV is not null and T1.AUDIT_MAN is not null ");
        sqlBuilder.append(" ORDER BY T1.CREATE_DATE DESC ");
        List<Object[]> sqlResultList = this.findSqlResultList(sqlBuilder.toString());
        return sqlResultList;
    }

}
