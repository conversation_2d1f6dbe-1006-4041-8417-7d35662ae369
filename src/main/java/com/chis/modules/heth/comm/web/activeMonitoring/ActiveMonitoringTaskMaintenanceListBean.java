package com.chis.modules.heth.comm.web.activeMonitoring;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.json.CrptDialogParamJson;
import com.chis.modules.heth.comm.service.ActiveMonitoringTaskServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * 主动监测任务维护Bean
 *
 * <AUTHOR>
 * @version 1.0
 */
@ManagedBean(name = "activeMonitoringTaskMaintenanceListBean")
@ViewScoped
public class ActiveMonitoringTaskMaintenanceListBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final ActiveMonitoringTaskServiceImpl activeMonitoringTaskService =
            SpringContextHolder.getBean(ActiveMonitoringTaskServiceImpl.class);
    /**
     * 当前登录人单位RID
     */
    private Integer unitRid;
    /**
     * 主动监测任务RID
     */
    private Integer rid;
    /**
     * 主动监测任务
     */
    private TbTjJcTask jcTask;
    /**
     * 查询条件: 年份
     */
    private Integer searchYear;
    private List<Integer> searchYearList;
    /**
     * 查询条件: 单位名称
     */
    private String searchCrptName;
    /**
     * 查询条件: 社会信用代码
     */
    private String searchInstitutionCode;
    /**
     * 查询条件: 行业类别
     */
    private String selectIndusTypeIds;
    private String selectIndusTypeNames;
    /**
     * 查询条件: 经济类型
     */
    private String selectEconomyIds;
    private String selectEconomyNames;
    /**
     * 查询条件: 企业规模
     */
    private String selectCrptSizeIds;
    private List<TsSimpleCode> crptSizeList;
    /**
     * 查询条件: 姓名
     */
    private String searchPsnName;
    /**
     * 查询条件: 证件号码
     */
    private String searchIdc;
    /**
     * 查询条件: 状态
     */
    private Integer[] searchState;
    private List<SelectItem> stateList;
    /**
     * 当前操作码表类型
     */
    private String simpleCodeOpType;
    /**
     * 当前操作码表特殊标记
     */
    private Integer simpleCodeOpTag;
    /**
     * 在岗状态页面选择
     * 在岗状态-在岗期间
     */
    private Map<String, Integer> editOnguadrStateMap;

    /**
     * 在岗状态-在岗期间-RID
     */
    private Integer whileOnDutyRid;
    /**
     * 证件号码-身份证件号类型-RID
     */
    private Integer idcTypeRid;
    /**
     * 主动监测因素是否多个
     */
    private Boolean ifMultipleFactors;
    /**
     * 当前行业类别主动监测岗位
     */
    private Set<Integer> postSet;
    private Map<Integer,Set<Integer>> postMap;
    /**
     * 当前行业类别主动监测因素
     */
    private Map<Integer,Set<String>> rsnMap;
    private Map<String,String> rsnNameMap;
    /**企业信息*/
    private TbTjCrptIndepend crptIndepend;
    /** 证件类型码表 去除暂未获取 */
    private List<TsSimpleCode> cardTypeSimpleCodeList;
    /** 证件类型Map */
    private Map<Integer, TsSimpleCode> cardTypeMap;
    /**防护用品佩戴情况*/
    private List<TsSimpleCode> protectEquList;

    public ActiveMonitoringTaskMaintenanceListBean() {
        initSimpleCode();
        otherInit();
        super.searchAction();
    }

    /**
     * 初始化码表相关
     */
    private void initSimpleCode() {
        //企业规模
        if (CollectionUtils.isEmpty(this.crptSizeList)) {
            this.crptSizeList = this.commService.findLevelSimpleCodesByTypeId("5004");
        }
        //在岗状态
        this.editOnguadrStateMap = new LinkedHashMap<>();
        this.whileOnDutyRid = null;
        List<TsSimpleCode> simpleCodeList = this.commService.findLevelSimpleCodesByTypeId("5009");
        for (TsSimpleCode simpleCode : simpleCodeList) {
            if(!"1".equals(simpleCode.getExtendS3())){
                continue;
            }
            Integer simpleCodeRid = simpleCode.getRid();
            this.editOnguadrStateMap.put(simpleCode.getCodeName(), simpleCodeRid);
            //在岗期间
            if ("2".equals(simpleCode.getExtendS1())) {
                this.whileOnDutyRid = simpleCode.getRid();
            }
        }
        if (this.idcTypeRid == null) {
          this.cardTypeSimpleCodeList = new ArrayList<>();
          this.cardTypeMap = new HashMap<>();
          simpleCodeList = this.commService.findLevelSimpleCodesByTypeId("5503");
            for (TsSimpleCode simpleCode : simpleCodeList) {
                String codeNo = simpleCode.getCodeNo();
                //身份证件号
                if ("01".equals(codeNo)) {
                    this.idcTypeRid = simpleCode.getRid();
                }
                //非暂未获取可选择
                if (!"88".equals(codeNo)) {
                    this.cardTypeSimpleCodeList.add(simpleCode);
                }
                this.cardTypeMap.put(simpleCode.getRid(), simpleCode);
            }
        }
        this.protectEquList =  this.commService.findLevelSimpleCodesByTypeId("5618");
    }

    /**
     * 加载当前行业类别主动监测岗位和主动监测因素
     */
    private void initPostAndRsnSet(Integer indusTypeId) {
        String sql = genSql(2,indusTypeId);
        pakPost(sql);
        sql = genSql(3,indusTypeId);
        pakRsn(sql);
    }

    private void pakPost( String sql) {
        this.postSet = new HashSet<>();
        this.postMap = new HashMap<>();
        List<Object[]> list = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql, null));
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        for (Object[] objects : list) {
            Integer rid = ObjectUtil.convert(Integer.class, objects[0]);
            Integer mainId = ObjectUtil.convert(Integer.class, objects[1]);
            if (rid == null || mainId == null) {
                continue;
            }
            if(this.postMap.containsKey(rid)){
                this.postMap.get(rid).add(mainId);
            }else {
                Set<Integer> set = new HashSet<>();
                set.add(mainId);
                this.postMap.put(rid,set);
            }
            this.postSet.add(rid);
        }
    }
    /**
     *  <p>方法描述：rsnMap : key ：岗位rid value : 主动监测因素 </p>
     * @MethodAuthor hsj 2023-08-16 11:36
     */
    private void pakRsn( String sql) {
        this.rsnMap = new HashMap<>();
        this.rsnNameMap = new HashMap<>();
        Map<Integer,Set<String>> rsnPostMap = new HashMap<>();
         if(this.postMap == null || this.postMap.size() ==0){
             return;
            }
        List<Object[]> list = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql, null));
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        for (Object[] objects : list) {
            Integer rid = ObjectUtil.convert(Integer.class, objects[0]);
            Integer mainId = ObjectUtil.convert(Integer.class, objects[1]);
            String name = ObjectUtil.toStr(objects[2]);
            if (rid == null || mainId == null) {
                continue;
            }
            if(rsnPostMap.containsKey(mainId)){
                rsnPostMap.get(mainId).add(rid.toString());
            }else {
                Set<String> set = new HashSet<>();
                set.add(rid.toString());
                rsnPostMap.put(mainId,set);
            }
            if(!this.rsnNameMap.containsKey(rid.toString())){
                this.rsnNameMap.put(rid.toString(),name);
            }
        }
        for(Map.Entry<Integer,Set<Integer>> entry :this.postMap.entrySet()){
            Set<Integer> value = entry.getValue();
            for(Integer mainId : value){
                if(!rsnPostMap.containsKey(mainId)){
                    continue;
                }
                if(this.rsnMap.containsKey(entry.getKey())){
                    this.rsnMap.get(entry.getKey()).addAll(rsnPostMap.get(mainId));
                }else {
                    this.rsnMap.put(entry.getKey(),rsnPostMap.get(mainId));
                }
            }
        }
    }

    /**
     * 其它初始化
     */
    private void otherInit() {
        this.ifSQL = true;
        //操作人所在单位
        if (Global.getUser().getTsUnit() == null || Global.getUser().getTsUnit().getRid() == null) {
            this.unitRid = -1;
        } else {
            this.unitRid = Global.getUser().getTsUnit().getRid();
        }
        //年份, 最近10年
        this.searchYearList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            this.searchYearList.add(DateUtils.getYearInt() - i);
        }
        //默认当年
        this.searchYear = this.searchYearList.get(0);
        //状态, 默认待提交、已退回
        this.searchState = new Integer[]{0, 3};
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem(0, "待提交"));
        this.stateList.add(new SelectItem(1, "待审核"));
        this.stateList.add(new SelectItem(3, "已退回"));
        this.stateList.add(new SelectItem(2, "审核通过"));
        this.ifMultipleFactors = "1".equals(this.commService.findParamValue("IF_MULTIPLE_FACTORS"));
    }

    @Override
    public String[] buildHqls() {
        String dataSql = "SELECT T.RID, " +
                "       T.YEAR, " +
                "       Z.FULL_NAME   AS ZONE_NAME, " +
                "       T.CRPT_NAME, " +
                "       T.CREDIT_CODE, " +
                "       SC1.CODE_NAME AS INDUS_TYPE, " +
                "       SC2.CODE_NAME AS ECONOMY, " +
                "       SC3.CODE_NAME AS CRPT_SIZE, " +
                "       '', " +
                "       T.BACK_RSN, " +
                "       T.STATE ,'true'";
        String sql = "FROM TB_TJ_JC_TASK T " +
                "         LEFT JOIN TS_ZONE Z ON T.ZONE_ID = Z.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC1 ON T.INDUS_TYPE_ID = SC1.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC2 ON T.ECONOMY_ID = SC2.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC3 ON T.CRPT_SIZE_ID = SC3.RID " +
                "WHERE NVL(T.DEL_MARK, 0) = 0 AND T.ORG_ID = :unitRid ";
        //仅查询本单位
        this.paramMap.put("unitRid", this.unitRid);
        //年份
        if (ObjectUtil.isNotEmpty(this.searchYear)) {
            this.paramMap.put("searchYear", this.searchYear);
            sql += "AND T.YEAR = :searchYear ";
        }
        //单位名称
        if (ObjectUtil.isNotEmpty(this.searchCrptName)) {
            this.paramMap.put("searchCrptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
            sql += "AND T.CRPT_NAME LIKE :searchCrptName escape '\\' ";
        }
        //社会信用代码
        if (ObjectUtil.isNotEmpty(this.searchInstitutionCode)) {
            this.paramMap.put("searchInstitutionCode", "%" + StringUtils.convertBFH(this.searchInstitutionCode.trim()) + "%");
            sql += "AND T.CREDIT_CODE LIKE :searchInstitutionCode escape '\\' ";
        }
        //行业类别
        if (StringUtils.isNotBlank(this.selectIndusTypeIds)) {
            List<String> indusTypeIdList = StringUtils.string2list(this.selectIndusTypeIds, ",");
            this.paramMap.put("indusTypeIdList", indusTypeIdList);
            sql += "AND T.INDUS_TYPE_ID IN (:indusTypeIdList) ";
        }
        //经济类型
        if (StringUtils.isNotBlank(this.selectEconomyIds)) {
            List<String> economyIdList = StringUtils.string2list(this.selectEconomyIds, ",");
            this.paramMap.put("economyIdList", economyIdList);
            sql += "AND T.ECONOMY_ID IN (:economyIdList) ";
        }
        //企业规模
        if (StringUtils.isNotBlank(this.selectCrptSizeIds)) {
            List<String> crptSizeIdList = StringUtils.string2list(this.selectCrptSizeIds, ",");
            this.paramMap.put("crptSizeIdList", crptSizeIdList);
            sql += "AND T.CRPT_SIZE_ID IN (:crptSizeIdList) ";
        }
        //姓名&证件号码
        if (ObjectUtil.isNotEmpty(this.searchPsnName) && ObjectUtil.isNotEmpty(this.searchIdc)) {
            this.paramMap.put("searchPsnName", "%" + StringUtils.convertBFH(this.searchPsnName.trim()) + "%");
            this.paramMap.put("searchIdc", this.searchIdc);
            sql += "AND EXISTS(SELECT 1 FROM TB_TJ_JC_TASK_PSN P WHERE T.RID = P.MAIN_ID AND P.PSN_NAME LIKE :searchPsnName escape '\\' AND P.IDC = :searchIdc) ";
        } else if (ObjectUtil.isNotEmpty(this.searchPsnName)) {
            this.paramMap.put("searchPsnName", "%" + StringUtils.convertBFH(this.searchPsnName.trim()) + "%");
            sql += "AND EXISTS(SELECT 1 FROM TB_TJ_JC_TASK_PSN P WHERE T.RID = P.MAIN_ID AND P.PSN_NAME LIKE :searchPsnName escape '\\') ";
        } else if (ObjectUtil.isNotEmpty(this.searchIdc)) {
            this.paramMap.put("searchIdc", this.searchIdc);
            sql += "AND EXISTS(SELECT 1 FROM TB_TJ_JC_TASK_PSN P WHERE T.RID = P.MAIN_ID AND P.IDC = :searchIdc) ";
        }
        //状态
        if (ObjectUtil.isNotEmpty(this.searchState)) {
            List<Integer> searchStateList = new ArrayList<>(Arrays.asList(searchState));
            this.paramMap.put("searchStateList", searchStateList);
            sql += "AND T.STATE IN (:searchStateList) ";
        }
        String countSql = "SELECT COUNT(1) " + sql;
        dataSql += sql + "ORDER BY T.YEAR DESC, Z.ZONE_GB, T.CRPT_NAME, T.CREDIT_CODE";
        return new String[]{dataSql, countSql};
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<String> ridList = new ArrayList<>();
        List<Object[]> result = CollectionUtil.castList(Object[].class, list);
        for (Object[] obj : result) {
            ridList.add(ObjectUtil.toStr(obj[0]));
            //地区去除省份
            String fullName = StringUtils.objectToString(obj[2]);
            obj[2] = fullName.substring(fullName.indexOf("_") + 1);
            //处理页面显示状态
            obj[8] = "";
            String state = StringUtils.objectToString(obj[10]);
            switch (state) {
                case "0":
                    obj[8] = "待提交";
                    break;
                case "1":
                    obj[8] = "待审核";
                    break;
                case "2":
                    obj[8] = "审核通过";
                    break;
                case "3":
                    obj[8] = "已退回";
                    break;
            }
            //仅已退回显示退回原因
            if (!"3".equals(state)) {
                obj[9] = "";
            }
        }
        List<String> rids =activeMonitoringTaskService.getIfBhkCodeByRidList(ridList);
        if(!CollectionUtils.isEmpty(rids)){
            for (Object[] obj : result) {
                if(rids.contains(ObjectUtil.toStr(obj[0]))){
                    obj[11] = "false";
                }
            }
        }
    }

    /**
     * 选择码表弹框页面
     */
    public void selSimpleCodeAction() {
        String titleName = "";
        String selectIds = "";
        String dialogUrl = "";
        String ifAllSelect = "";
        String type = "";
        Integer indusTypeId = null;
        Integer postId = null;
        Integer width = null;
        Integer contentWidth = null;
        Integer height = null;
        Integer contentHeight = null;
        boolean ifShowFirstCode = false;
        switch (this.simpleCodeOpType) {
            case "5002":
                titleName = "行业类别";
                selectIds = this.selectIndusTypeIds;
                contentWidth = 650;
                contentHeight = 500;
                type = "1";
                dialogUrl = "/webapp/heth/comm/dialog/codeMulitySelectTaskList";
                break;
            case "5003":
                titleName = "经济类型";
                selectIds = this.selectEconomyIds;
                contentWidth = 650;
                contentHeight = 500;
                ifShowFirstCode = true;
                dialogUrl = "/webapp/system/codeMulitySelectList";
                break;
            case "5595":
                TbTjJcTaskPsn jcTaskPsn1 = findJcTaskPsnBySimpleCodeOpTag();
                titleName = "岗位";
                selectIds = StringUtils.objectToString(jcTaskPsn1.getPostId());
                contentWidth = 550;
                contentHeight = 430;
                ifAllSelect = "true";
                type = "2";
                indusTypeId = this.jcTask.getFkByIndusTypeId().getRid();
                dialogUrl = "/webapp/heth/comm/dialog/codeRadioSelectTaskList";
                break;
            case "5007":
                TbTjJcTaskPsn jcTaskPsn2 = findJcTaskPsnBySimpleCodeOpTag();
                 //监测岗位名称
                if(jcTaskPsn2 == null || jcTaskPsn2.getPostId() == null){
                    JsfUtil.addErrorMessage("请先选择监测岗位名称！");
                    return;
                }
                postId =jcTaskPsn2.getPostId();
                indusTypeId = this.jcTask.getFkByIndusTypeId().getRid();
                titleName = "职业病主动监测因素";
                selectIds = jcTaskPsn2.getRsnId();
                if (this.ifMultipleFactors) {
                    contentWidth = 650;
                    contentHeight = 500;
                    type = "4";
                    dialogUrl = "/webapp/heth/comm/dialog/codeMulitySelectTaskList";
                } else {
                    contentWidth = 550;
                    contentHeight = 430;
                    ifAllSelect = "true";
                    type = "3";
                    dialogUrl = "/webapp/heth/comm/dialog/codeRadioSelectTaskList";
                }
                break;
            default:
                break;
        }
        Map<String, Object> options = MapUtils.produceDialogMap(width, contentWidth, height, contentHeight);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add(titleName);
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>();
        paramList.add(this.simpleCodeOpType);
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<>();
        paramList.add(selectIds);
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<>();
        paramList.add(StringUtils.objectToString(ifShowFirstCode));
        paramMap.put("ifShowFirstCode", paramList);
        paramList = new ArrayList<>();
        paramList.add(ifAllSelect);
        paramMap.put("ifAllSelect", paramList);
        paramList = new ArrayList<>();
        paramList.add(type);
        paramMap.put("type", paramList);
        if(ObjectUtil.isNotNull(indusTypeId)){
            paramList = new ArrayList<>();
            paramList.add(String.valueOf(indusTypeId));
            paramMap.put("indusTypeId", paramList);
        }
        if(ObjectUtil.isNotNull(postId)){
            paramList = new ArrayList<>();
            paramList.add(String.valueOf(postId));
            paramMap.put("postId", paramList);
        }
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog(dialogUrl, options, paramMap);
    }

    /**
     * 生成SQL
     *
     * @param type      <pre>1: 查询重点行业的行业类别</pre><pre>2: 查询行业类别下监测岗位</pre>
     *                  <pre>3: 查询行业类别下职业病主动监测因素(单选)</pre><pre>4: 查询行业类别下职业病主动监测因素(多选)</pre>
     * @return SQL
     */
    private String genSql(int type,Integer indusTypeId) {
        if (type == 2) {
            return "SELECT SC.RID,DS.RID as mainId " +
                    "FROM TD_ZDZYB_ANALY_DETAIL_SUB DS " +
                    "         LEFT JOIN TS_SIMPLE_CODE SC1 ON DS.ANALY_ITEM_ID = SC1.RID " +
                    "         LEFT JOIN TD_ZDZYB_ANALY_DETAIL D ON DS.MAIN_ID = D.RID " +
                    "         LEFT JOIN TD_ZDZYB_ANALY_TYPE T ON D.MAIN_ID = T.RID " +
                    "         LEFT JOIN TD_ZDZYB_DETAIL_SUB_REL DSR ON DS.RID = DSR.MAIN_ID " +
                    "         LEFT JOIN TS_SIMPLE_CODE SC ON DSR.ANALY_ITEM_ID = SC.RID " +
                    "WHERE T.BUS_TYPE = 4 AND T.ANALY_TYPE = 3 AND DSR.TYPE = 1 AND SC1.RID = " + indusTypeId ;
        } else if (type == 3) {
            return "SELECT SC.RID,DS.RID as mainId ,SC.CODE_NAME " +
                    "FROM TD_ZDZYB_ANALY_DETAIL_SUB DS " +
                    "         LEFT JOIN TS_SIMPLE_CODE SC1 ON DS.ANALY_ITEM_ID = SC1.RID " +
                    "         LEFT JOIN TD_ZDZYB_ANALY_DETAIL D ON DS.MAIN_ID = D.RID " +
                    "         LEFT JOIN TD_ZDZYB_ANALY_TYPE T ON D.MAIN_ID = T.RID " +
                    "         LEFT JOIN TD_ZDZYB_DETAIL_SUB_REL DSR ON DS.RID = DSR.MAIN_ID " +
                    "         LEFT JOIN TS_SIMPLE_CODE SC ON DSR.ANALY_ITEM_ID = SC.RID " +
                    "WHERE T.BUS_TYPE = 4 AND T.ANALY_TYPE = 3 AND DSR.TYPE = 2 AND SC1.RID = " + indusTypeId;
        }
        return "";
    }

    /**
     * 选择码表后操作
     *
     * @param event 选择项
     */
    public void onSimpleCodeAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (ObjectUtil.isEmpty(selectedMap)) {
            return;
        }
        List<String> idAndNameList;
        if (selectedMap.get("selectPro") instanceof TsSimpleCode) {
            idAndNameList = pakIdAndNameForRadio((TsSimpleCode) selectedMap.get("selectPro"));
        } else {
            idAndNameList = pakIdAndNameForMulity(CollectionUtil.castList(TsSimpleCode.class, selectedMap.get("selectPros")));
        }
        switch (this.simpleCodeOpType) {
            case "5002":
                this.selectIndusTypeIds = idAndNameList.get(0);
                this.selectIndusTypeNames = idAndNameList.get(1);
                break;
            case "5003":
                this.selectEconomyIds = idAndNameList.get(0);
                this.selectEconomyNames = idAndNameList.get(1);
                break;
            case "5595":
                TbTjJcTaskPsn jcTaskPsn1 = findJcTaskPsnBySimpleCodeOpTag();
                jcTaskPsn1.setPostId(ObjectUtil.convert(Integer.class, idAndNameList.get(0)));
                jcTaskPsn1.setPostName(idAndNameList.get(1));
                String extendS1Str = StringUtils.objectToString(idAndNameList.get(2));
                jcTaskPsn1.setNeedWorkOther("1".equals(extendS1Str));
                //清空已选择的【职业病主动监测因素】
                jcTaskPsn1.setRsnId(null);
                jcTaskPsn1.setRsnName(null);
                break;
            case "5007":
                TbTjJcTaskPsn jcTaskPsn2 = findJcTaskPsnBySimpleCodeOpTag();
                jcTaskPsn2.setRsnId(idAndNameList.get(0));
                jcTaskPsn2.setRsnName(idAndNameList.get(1));
                break;
            default:
                break;
        }
    }

    /**
     * 单选弹出框返回数据处理
     *
     * @param simpleCode 码表
     * @return 处理后数据
     */
    private List<String> pakIdAndNameForRadio(TsSimpleCode simpleCode) {
        List<String> idAndNameList = new ArrayList<>();
        if (simpleCode == null || simpleCode.getRid() == null) {
            idAndNameList.add("");
            idAndNameList.add("");
            idAndNameList.add("");
            return idAndNameList;
        }
        idAndNameList.add(StringUtils.objectToString(simpleCode.getRid()));
        idAndNameList.add(StringUtils.objectToString(simpleCode.getCodeName()));
        idAndNameList.add(StringUtils.objectToString(simpleCode.getExtendS1()));
        return idAndNameList;
    }

    /**
     * 多选弹出框返回数据处理
     *
     * @param simpleCodeList 码表
     * @return 处理后数据
     */
    private List<String> pakIdAndNameForMulity(List<TsSimpleCode> simpleCodeList) {
        List<String> idAndNameList = new ArrayList<>();
        if (CollectionUtils.isEmpty(simpleCodeList)) {
            idAndNameList.add("");
            idAndNameList.add("");
            return idAndNameList;
        }
        List<String> idList = new ArrayList<>();
        List<String> nameList = new ArrayList<>();
        for (TsSimpleCode simpleCode : simpleCodeList) {
            if (simpleCode == null || simpleCode.getRid() == null) {
                continue;
            }
            idList.add(StringUtils.objectToString(simpleCode.getRid()));
            nameList.add(StringUtils.objectToString(simpleCode.getCodeName()));
        }
        idAndNameList.add(StringUtils.list2string(idList, ","));
        idAndNameList.add(StringUtils.list2string(nameList, "，"));
        return idAndNameList;
    }

    /**
     * 清除页面选择码表
     */
    public void clearSimpleCode() {
        switch (this.simpleCodeOpType) {
            case "5002":
                this.selectIndusTypeNames = null;
                this.selectIndusTypeIds = null;
                break;
            case "5003":
                this.selectEconomyNames = null;
                this.selectEconomyIds = null;
                break;
            default:
                break;
        }
    }

    public TbTjJcTaskPsn findJcTaskPsnBySimpleCodeOpTag() {
        if (this.simpleCodeOpTag == null || CollectionUtils.isEmpty(this.jcTask.getJcTaskPsnList())
                || this.jcTask.getJcTaskPsnList().size() <= this.simpleCodeOpTag) {
            return null;
        }
        return this.jcTask.getJcTaskPsnList().get(this.simpleCodeOpTag);
    }

    @Override
    public void addInit() {
        this.jcTask = new TbTjJcTask();
        this.commService.preEntity(this.jcTask);
        //当前年
        this.jcTask.setYear(DateUtils.getYearInt());
        //操作人系统单位
        this.jcTask.setFkByOrgId(new TsUnit(this.unitRid));
        this.jcTask.setState(0);
        this.jcTask.setDelMark(0);
        this.jcTask.setIfWhBhkCode(Boolean.TRUE);
        if (CollectionUtils.isEmpty(this.jcTask.getJcTaskPsnList())) {
            this.jcTask.setJcTaskPsnList(new ArrayList<TbTjJcTaskPsn>());
        }
    }

    @Override
    public void viewInit() {
        this.jcTask = this.activeMonitoringTaskService.findTbTjJcTaskByRid(this.rid);
        this.fillCardTypeId();
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                .findComponent("tabView:viewForm:jcTaskPsnTable");
        if (dataTable == null) {
            return;
        }
        dataTable.setFirst(0);
        dataTable.setRows(20);
    }

    @Override
    public void modInit() {
        this.jcTask = this.activeMonitoringTaskService.findTbTjJcTaskByRid(this.rid);
        this.fillCardTypeId();
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:jcTaskPsnTable");
        if (dataTable == null) {
            return;
        }
        dataTable.setFirst(0);
        dataTable.setRows(20);
    }

    @Override
    public void saveAction() {
        try {
            if (verifyCrptInfoFailed()) {
                return;
            }
            if (verifyLogic()) {
                return;
            }
            this.fillFkByCardTypeId();
            this.activeMonitoringTaskService.upsertTbTjJcTask(this.jcTask);
            this.rid = this.jcTask.getRid();
            this.jcTask = this.activeMonitoringTaskService.findTbTjJcTaskByRid(this.rid);
            this.fillCardTypeId();
            JsfUtil.addSuccessMessage("保存成功！");
            searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    public void submitAction() {
        try {
            this.fillFkByCardTypeId();
            this.jcTask.setState(1);
            this.jcTask.setBackRsn("");
            this.activeMonitoringTaskService.upsertTbTjJcTask(this.jcTask);
            this.rid = this.jcTask.getRid();
            JsfUtil.addSuccessMessage("提交成功！");
            viewInitAction();
            searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    public void beforeSubmitAction() {
        if (verifySubmitFailed()) {
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show()");
    }

    public void deleteAction() {
        try {
            if (this.rid == null) {
                return;
            }
            this.activeMonitoringTaskService.updateTbTjJcTaskParamByRid(this.rid, "DEL_MARK", 1);
            JsfUtil.addSuccessMessage("删除成功！");
            searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    public void quashAction() {
        try {
            if (this.rid == null) {
                return;
            }
            this.activeMonitoringTaskService.updateTbTjJcTaskParamByRid(this.rid, "STATE", 0);
            JsfUtil.addSuccessMessage("撤销成功！");
            modInitAction();
            searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
    }

    /**
     * 验证单位信息(暂存)
     *
     * @return 成功返回false, 失败返回true
     */
    public boolean verifyCrptInfoFailed() {
        if (this.jcTask.getFkByCrptId() == null || this.jcTask.getFkByCrptId().getRid() == null) {
            JsfUtil.addErrorMessage("请先选择单位名称！");
            return true;
        }
        return false;
    }

    /**
     * 验证证件类型、证件号码唯一和格式是否正确
     *
     * @return 成功返回false, 失败返回true
     */
    public boolean verifyLogic() {
        boolean flag = false;
        List<TbTjJcTaskPsn> jcTaskPsnList = this.jcTask.getJcTaskPsnList();
        if (CollectionUtils.isEmpty(jcTaskPsnList)) {
            return false;
        }
        initPostAndRsnSet(this.jcTask.getFkByIndusTypeId().getRid());
        Map<String, List<String>> idcMap = new HashMap<>();
        for (int i = 0, jcTaskPsnListSize = jcTaskPsnList.size(); i < jcTaskPsnListSize; i++) {
            TbTjJcTaskPsn jcTaskPsn = jcTaskPsnList.get(i);
            Integer cardTypeId = jcTaskPsn.getCardTypeId();
            TsSimpleCode cardTypeSimpleCode = null == cardTypeId ? null : this.cardTypeMap.get(cardTypeId);
            String idc = jcTaskPsn.getIdc();
            if (null != cardTypeSimpleCode && StringUtils.isNotBlank(idc)) {
                String codeNo = cardTypeSimpleCode.getCodeNo();
                if (StringUtils.isNotBlank(IdcUtils.validateIdc(idc, codeNo))) {
                    JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息证件号码输入错误！");
                    flag = true;
                }
                String key = codeNo+"&"+idc;
                if (!idcMap.containsKey(key)) {
                    idcMap.put(key, new ArrayList<String>());
                }
                idcMap.get(key).add(StringUtils.objectToString(i + 1));
            }
            if (jcTaskPsn.getPostId() != null && !this.postSet.contains(jcTaskPsn.getPostId())) {
                JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息监测岗位名称必须在主动监测重点行业范围内！");
                flag = true;
            }
            if (StringUtils.isNotBlank(jcTaskPsn.getRsnId())) {
                if(ObjectUtil.isNotNull(jcTaskPsn.getPostId())){
                    if (verifyRsnFailed(jcTaskPsn.getRsnId(),jcTaskPsn.getPostId())) {
                        JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息职业病主动监测因素必须在主动监测重点行业范围内！");
                        flag = true;
                    }
                }else {
                    JsfUtil.addErrorMessage("请选择第" + (i + 1) + "条劳动者信息监测岗位名称！");
                    flag = true;
                }

            }
        }
        if (verifySole(idcMap)) {
            flag = true;
        }
        return flag;
    }

    /**
     * 提交验证
     *
     * @return 成功返回false, 失败返回true
     */
    public boolean verifySubmitFailed() {
        boolean flag = verifyCrptInfoFailed();
        List<TbTjJcTaskPsn> jcTaskPsnList = this.jcTask.getJcTaskPsnList();
        if (CollectionUtils.isEmpty(jcTaskPsnList)) {
            JsfUtil.addErrorMessage("请添加劳动者信息！");
            return true;
        }
        initPostAndRsnSet(this.jcTask.getFkByIndusTypeId().getRid());
        Map<String, List<String>> idcMap = new HashMap<>();
        for (int i = 0, jcTaskPsnListSize = jcTaskPsnList.size(); i < jcTaskPsnListSize; i++) {
            TbTjJcTaskPsn jcTaskPsn = jcTaskPsnList.get(i);
            if (StringUtils.isBlank(jcTaskPsn.getPsnName())) {
                JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息姓名不能为空！");
                flag = true;
            }
            Integer cardTypeId = jcTaskPsn.getCardTypeId();
            TsSimpleCode cardTypeSimpleCode = null == cardTypeId ? null : this.cardTypeMap.get(cardTypeId);
            if (null == cardTypeSimpleCode) {
                JsfUtil.addErrorMessage("请选择第" + (i + 1) + "条劳动者信息证件类型！");
                flag = true;
            }
            String idc = jcTaskPsn.getIdc();
            if (StringUtils.isBlank(idc)) {
                JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息证件号码不能为空！");
                flag = true;
            } else if (null != cardTypeSimpleCode) {
                String codeNo = cardTypeSimpleCode.getCodeNo();
                // 证件号码格式校验
                if (StringUtils.isNotBlank(IdcUtils.validateIdc(idc, codeNo))) {
                    JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息证件号码输入错误！");
                    flag = true;
                }
                String key = codeNo+"&"+idc;
                if (!idcMap.containsKey(key)) {
                    idcMap.put(key, new ArrayList<String>());
                }
                idcMap.get(key).add(StringUtils.objectToString(i + 1));
            }
            if (jcTaskPsn.getPostId() == null) {
                JsfUtil.addErrorMessage("请选择第" + (i + 1) + "条劳动者信息监测岗位名称！");
                flag = true;
            } else if (!this.postSet.contains(jcTaskPsn.getPostId())) {
                JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息监测岗位名称必须在主动监测重点行业范围内！");
                flag = true;
            } else if (jcTaskPsn.getNeedWorkOther() && StringUtils.isBlank(jcTaskPsn.getWorkOther())) {
                JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息监测岗位为“其他”时，其他名称不能为空！");
                flag = true;
            }
            if (jcTaskPsn.getProtectEquId() == null) {
                JsfUtil.addErrorMessage("请选择第" + (i + 1) + "条劳动者信息防护用品佩戴情况！");
                flag = true;
            }
            if (jcTaskPsn.getOnguadrStateId() == null) {
                JsfUtil.addErrorMessage("请选择第" + (i + 1) + "条劳动者信息在岗状态！");
                flag = true;
            }
            if (StringUtils.isBlank(jcTaskPsn.getRsnId())) {
                JsfUtil.addErrorMessage("请选择第" + (i + 1) + "条劳动者信息职业病主动监测因素！");
                flag = true;
            } else if (verifyRsnFailed(jcTaskPsn.getRsnId(),jcTaskPsn.getPostId())) {
                JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息职业病主动监测因素必须在主动监测重点行业范围内！");
                flag = true;
            }
        }
        if (verifySole(idcMap)) {
            flag = true;
        }
        return flag;
    }

    /**
     * 验证危害因素是否在主动监测重点行业范围内
     *
     * @param rsnIds 危害因素id
     * @return 不在返回true
     */
    private boolean verifyRsnFailed(String rsnIds,Integer postId) {
        List<String> rsnIdList = StringUtils.string2list(rsnIds, ",");
        if(!this.rsnMap.containsKey(postId)){
            return true;
        }
        Set<String> rsnSet = this.rsnMap.get(postId);
        for (String rsnId : rsnIdList) {
            if (!rsnSet.contains(rsnId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证证件类型、证件号码唯一
     *
     * @param idcMap 证件类型、证件号码Map
     * @return 存在重复的返回true
     */
    private boolean verifySole(Map<String, List<String>> idcMap) {
        boolean flag = false;
        for (Map.Entry<String, List<String>> entry : idcMap.entrySet()) {
            if (entry.getValue().size() <= 1) {
                continue;
            }
            JsfUtil.addErrorMessage("第" + StringUtils.list2string(entry.getValue(), "、") + "条劳动者信息证件类型与证件号码重复！");
            flag = true;
        }
        return flag;
    }

    /**
     * 选择单位
     */
    public void selectCrptList() {
        Map<String, Object> options = MapUtils.produceDialogMap(1080, 1050, 520, 505);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        CrptDialogParamJson json = new CrptDialogParamJson();
        json.setBusType("1");
        json.setIfActiveMonitoringTask(Boolean.TRUE);
        json.setActiveMonitoringTaskRid(this.jcTask.getRid());
        json.setIfShowOtherZone(true);
        json.setIfSearchZoneDefault(false);
        json.setSearchCrptName(StringUtils.objectToString(this.jcTask.getCrptName()));
        paramList.add(JSON.toJSONString(json));
        paramMap.put("jsonParam", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        String useNewCrptSelect = this.commService.findParamValue("USE_NEW_CRPT_SELECT");
        String crptSelectPath = "/webapp/heth/comm/crptCommSelectList";
        if ("1".equals(useNewCrptSelect)) {
            crptSelectPath = "/webapp/heth/comm/crptCommSelectListNew";
        }
        requestContext.openDialog(crptSelectPath, options, paramMap);
    }


    /**
     * 选择单位后
     */
    public void onCrptSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
           this.crptIndepend  = (TbTjCrptIndepend) selectedMap.get("selectCrptIndepend");
           //新增或者修改时行业类别不变或者花名册没有数据
           boolean isFlag = this.jcTask.getFkByCrptId() == null || this.jcTask.getFkByCrptId().getRid() == null ||
                   (this.jcTask.getFkByCrptId().getTsSimpleCodeByIndusTypeId()  != null && this.crptIndepend.getFkByCrptId().getTsSimpleCodeByIndusTypeId() != null
                           && this.jcTask.getFkByCrptId().getTsSimpleCodeByIndusTypeId().getRid().compareTo(this.crptIndepend.getFkByCrptId().getTsSimpleCodeByIndusTypeId().getRid()) == 0)
                   || CollectionUtils.isEmpty(this.jcTask.getJcTaskPsnList());
           if(isFlag){
               dealTbTjCrpt();
               return;
           }
           //行业类别变化且花名册有数据
           isFlag = this.jcTask.getFkByCrptId().getTsSimpleCodeByIndusTypeId()  != null && this.crptIndepend.getFkByCrptId().getTsSimpleCodeByIndusTypeId() != null
                            && this.jcTask.getFkByCrptId().getTsSimpleCodeByIndusTypeId().getRid().compareTo(this.crptIndepend.getFkByCrptId().getTsSimpleCodeByIndusTypeId().getRid()) != 0
           && !CollectionUtils.isEmpty(this.jcTask.getJcTaskPsnList());
           if(isFlag && verifyTaskPsnList()){
               dealTbTjCrpt();
               return;
           }
            RequestContext.getCurrentInstance().execute("PF('CrptConfirmDialog').show()");
        }
    }
    /**
     *  <p>方法描述：花名册比较</p>
     * @MethodAuthor hsj 2023-08-16 15:41
     */
    private boolean verifyTaskPsnList() {
        boolean isFlag = Boolean.TRUE;
        if(CollectionUtils.isEmpty(this.jcTask.getJcTaskPsnList())){
            return isFlag;
        }
        initPostAndRsnSet(this.crptIndepend.getFkByCrptId().getTsSimpleCodeByIndusTypeId().getRid());
        for (TbTjJcTaskPsn jcTaskPsn : this.jcTask.getJcTaskPsnList()){
            if (jcTaskPsn.getPostId() != null && !this.postSet.contains(jcTaskPsn.getPostId())) {
                isFlag = Boolean.FALSE;
                break;
            }
            boolean flag = StringUtils.isNotBlank(jcTaskPsn.getRsnId()) && ObjectUtil.isNotNull(jcTaskPsn.getPostId()) && verifyRsnFailed(jcTaskPsn.getRsnId(),jcTaskPsn.getPostId());
            if (flag) {
                isFlag = Boolean.FALSE;
                break;
            }
        }
        return isFlag;

    }

    /**
     *  <p>方法描述：用人单位信息处理</p>
     * @MethodAuthor hsj 2023-08-15 13:39
     */
    public void dealTbTjCrpt(){
        TbTjCrpt tbTjCrpt = this.crptIndepend.getFkByCrptId();
        this.jcTask.setFkByCrptId(tbTjCrpt);
        this.jcTask.setCrptName(tbTjCrpt.getCrptName());
        this.jcTask.setFkByZoneId(tbTjCrpt.getTsZoneByZoneId());
        this.jcTask.setCreditCode(tbTjCrpt.getInstitutionCode());
        this.jcTask.setAddress(tbTjCrpt.getAddress());
        this.jcTask.setFkByIndusTypeId(tbTjCrpt.getTsSimpleCodeByIndusTypeId());
        this.jcTask.setFkByEconomyId(tbTjCrpt.getTsSimpleCodeByEconomyId());
        this.jcTask.setFkByCrptSizeId(tbTjCrpt.getTsSimpleCodeByCrptSizeId());
        this.jcTask.setLinkMan(tbTjCrpt.getLinkman2());
        this.jcTask.setLinkPhone(tbTjCrpt.getLinkphone2());
    }

    /**
     *  <p>方法描述：切换用人单位信息行业类别更换之后</p>
     * @MethodAuthor hsj 2023-08-15 13:46
     */
    public void  crptChangeAction(){
        dealTbTjCrpt();
        if(CollectionUtils.isEmpty(this.jcTask.getJcTaskPsnList())){
            return;
        }
        for(TbTjJcTaskPsn jcTaskPsn: this.jcTask.getJcTaskPsnList()){
            if (jcTaskPsn.getPostId() != null && !this.postSet.contains(jcTaskPsn.getPostId())) {
                jcTaskPsn.setPostId(null);
                jcTaskPsn.setPostName(null);
                jcTaskPsn.setRsnId(null);
                jcTaskPsn.setRsnName(null);
                continue;
            }

            if(StringUtils.isNotBlank(jcTaskPsn.getRsnId()) && ObjectUtil.isNotNull(jcTaskPsn.getPostId())){
                List<String> rsnIdList = StringUtils.string2list(jcTaskPsn.getRsnId(), ",");
                List<String> rsnIdNewList = new LinkedList<>();
                if(!this.rsnMap.containsKey(jcTaskPsn.getPostId())){
                    //清空已选择的【职业病主动监测因素】
                    jcTaskPsn.setRsnId(null);
                    jcTaskPsn.setRsnName(null);
                    continue;
                }
                Set<String> rsnSet = this.rsnMap.get(jcTaskPsn.getPostId());
                for (String rsnId : rsnIdList) {
                    if (rsnSet.contains(rsnId)) {
                        rsnIdNewList.add(rsnId);
                    }
                }
                if(CollectionUtils.isEmpty(rsnIdNewList)){
                    jcTaskPsn.setRsnId(null);
                    jcTaskPsn.setRsnName(null);
                    continue;
                }
                jcTaskPsn.setRsnId(StringUtils.list2string(rsnIdNewList,","));
                List<String> nameList = new LinkedList<>();
                for(String str : rsnIdNewList){
                    nameList.add(this.rsnNameMap.get(str));
                }
                jcTaskPsn.setRsnName(StringUtils.list2string(nameList,"，"));
            }
        }
    }
    /**
     * 劳动者花名册-添加
     */
    public void addJcTaskPsnAction() {
        if (verifyCrptInfoFailed()) {
            return;
        }
        TbTjJcTaskPsn jcTaskPsn = new TbTjJcTaskPsn();
        jcTaskPsn.setOnguadrStateId(this.whileOnDutyRid);
        jcTaskPsn.setFkByMainId(this.jcTask);
        //默认身份证
        jcTaskPsn.setCardTypeId(this.idcTypeRid);
        jcTaskPsn.setJcTaskBadrsnList(new ArrayList<TbTjJcTaskBadrsn>());
        List<TbTjJcTaskPsn> jcTaskPsnList = jcTask.getJcTaskPsnList();
        boolean f = !CollectionUtils.isEmpty(jcTaskPsnList) && jcTaskPsnList.get(jcTaskPsnList.size()-1).getProtectEquId() != null;
        if(f){
            //防护用品佩戴情况 默认显示上一条数据防护用品佩戴情况的值
            jcTaskPsn.setProtectEquId(jcTaskPsnList.get(jcTaskPsnList.size()-1).getProtectEquId());
        }
        this.commService.preEntity(jcTaskPsn);
        this.jcTask.getJcTaskPsnList().add(jcTaskPsn);
    }

    /**
     * 劳动者花名册-删除
     *
     * @param index 下标
     */
    public void delJcTaskPsnAction(Integer index) {
        if (index == null) {
            return;
        }
        this.jcTask.getJcTaskPsnList().remove((int) index);
    }

    /**
     * <p>方法描述：存储前赋值相关码表 </p>
     * pw 2023/11/16
     **/
    private void fillFkByCardTypeId () {
        List<TbTjJcTaskPsn> jcTaskPsnList = this.jcTask.getJcTaskPsnList();
        if (CollectionUtils.isEmpty(jcTaskPsnList)) {
            return;
        }
        for (TbTjJcTaskPsn jcTaskPsn : jcTaskPsnList) {
            Integer cardTypeId = jcTaskPsn.getCardTypeId();
            if (null != cardTypeId) {
                jcTaskPsn.setFkByCardTypeId(new TsSimpleCode(cardTypeId));
            } else {
                jcTaskPsn.setFkByCardTypeId(null);
            }
            Integer protectEquId = jcTaskPsn.getProtectEquId();
            if(ObjectUtil.isNull(protectEquId)){
                jcTaskPsn.setFkByProtectEquId(null);
            }else {
                jcTaskPsn.setFkByProtectEquId(new TsSimpleCode(protectEquId));
            }
        }
    }

    /**
     * <p>方法描述：查询或修改后 初始化相关码表 </p>
     * pw 2023/11/16
     **/
    private void fillCardTypeId () {
        List<TbTjJcTaskPsn> jcTaskPsnList = null == this.jcTask ? null : this.jcTask.getJcTaskPsnList();
        if (CollectionUtils.isEmpty(jcTaskPsnList)) {
            return;
        }
        for (TbTjJcTaskPsn jcTaskPsn : jcTaskPsnList) {
            Integer cardTypeId = null == jcTaskPsn.getFkByCardTypeId() ? null : jcTaskPsn.getFkByCardTypeId().getRid();
            jcTaskPsn.setCardTypeId(cardTypeId);
            Integer protectEquId = null == jcTaskPsn.getFkByProtectEquId() ? null : jcTaskPsn.getFkByProtectEquId().getRid();
            jcTaskPsn.setProtectEquId(protectEquId);
        }
    }

    public CommServiceImpl getCommService() {
        return commService;
    }

    public ActiveMonitoringTaskServiceImpl getActiveMonitoringTaskService() {
        return activeMonitoringTaskService;
    }

    public Integer getUnitRid() {
        return unitRid;
    }

    public void setUnitRid(Integer unitRid) {
        this.unitRid = unitRid;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TbTjJcTask getJcTask() {
        return jcTask;
    }

    public void setJcTask(TbTjJcTask jcTask) {
        this.jcTask = jcTask;
    }

    public Integer getSearchYear() {
        return searchYear;
    }

    public void setSearchYear(Integer searchYear) {
        this.searchYear = searchYear;
    }

    public List<Integer> getSearchYearList() {
        return searchYearList;
    }

    public void setSearchYearList(List<Integer> searchYearList) {
        this.searchYearList = searchYearList;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchInstitutionCode() {
        return searchInstitutionCode;
    }

    public void setSearchInstitutionCode(String searchInstitutionCode) {
        this.searchInstitutionCode = searchInstitutionCode;
    }

    public String getSelectIndusTypeIds() {
        return selectIndusTypeIds;
    }

    public void setSelectIndusTypeIds(String selectIndusTypeIds) {
        this.selectIndusTypeIds = selectIndusTypeIds;
    }

    public String getSelectIndusTypeNames() {
        return selectIndusTypeNames;
    }

    public void setSelectIndusTypeNames(String selectIndusTypeNames) {
        this.selectIndusTypeNames = selectIndusTypeNames;
    }

    public String getSelectEconomyIds() {
        return selectEconomyIds;
    }

    public void setSelectEconomyIds(String selectEconomyIds) {
        this.selectEconomyIds = selectEconomyIds;
    }

    public String getSelectEconomyNames() {
        return selectEconomyNames;
    }

    public void setSelectEconomyNames(String selectEconomyNames) {
        this.selectEconomyNames = selectEconomyNames;
    }

    public String getSelectCrptSizeIds() {
        return selectCrptSizeIds;
    }

    public void setSelectCrptSizeIds(String selectCrptSizeIds) {
        this.selectCrptSizeIds = selectCrptSizeIds;
    }

    public List<TsSimpleCode> getCrptSizeList() {
        return crptSizeList;
    }

    public void setCrptSizeList(List<TsSimpleCode> crptSizeList) {
        this.crptSizeList = crptSizeList;
    }

    public String getSearchPsnName() {
        return searchPsnName;
    }

    public void setSearchPsnName(String searchPsnName) {
        this.searchPsnName = searchPsnName;
    }

    public String getSearchIdc() {
        return searchIdc;
    }

    public void setSearchIdc(String searchIdc) {
        this.searchIdc = searchIdc;
    }

    public Integer[] getSearchState() {
        return searchState;
    }

    public void setSearchState(Integer[] searchState) {
        this.searchState = searchState;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public String getSimpleCodeOpType() {
        return simpleCodeOpType;
    }

    public void setSimpleCodeOpType(String simpleCodeOpType) {
        this.simpleCodeOpType = simpleCodeOpType;
    }

    public Integer getSimpleCodeOpTag() {
        return simpleCodeOpTag;
    }

    public void setSimpleCodeOpTag(Integer simpleCodeOpTag) {
        this.simpleCodeOpTag = simpleCodeOpTag;
    }


    public Integer getIdcTypeRid() {
        return idcTypeRid;
    }

    public void setIdcTypeRid(Integer idcTypeRid) {
        this.idcTypeRid = idcTypeRid;
    }

    public Boolean getIfMultipleFactors() {
        return ifMultipleFactors;
    }

    public void setIfMultipleFactors(Boolean ifMultipleFactors) {
        this.ifMultipleFactors = ifMultipleFactors;
    }
    public Map<String, Integer> getEditOnguadrStateMap() {
        return editOnguadrStateMap;
    }
    public void setEditOnguadrStateMap(Map<String, Integer> editOnguadrStateMap) {
        this.editOnguadrStateMap = editOnguadrStateMap;
    }
    public Integer getWhileOnDutyRid() {
        return whileOnDutyRid;
    }
    public void setWhileOnDutyRid(Integer whileOnDutyRid) {
        this.whileOnDutyRid = whileOnDutyRid;
    }

    public List<TsSimpleCode> getCardTypeSimpleCodeList() {
        return cardTypeSimpleCodeList;
    }

    public void setCardTypeSimpleCodeList(List<TsSimpleCode> cardTypeSimpleCodeList) {
        this.cardTypeSimpleCodeList = cardTypeSimpleCodeList;
    }

    public Map<Integer, TsSimpleCode> getCardTypeMap() {
        return cardTypeMap;
    }

    public void setCardTypeMap(Map<Integer, TsSimpleCode> cardTypeMap) {
        this.cardTypeMap = cardTypeMap;
    }

    public List<TsSimpleCode> getProtectEquList() {
        return protectEquList;
    }

    public void setProtectEquList(List<TsSimpleCode> protectEquList) {
        this.protectEquList = protectEquList;
    }
}
