package com.chis.modules.heth.comm.plugin;

import com.chis.modules.system.entity.TsSystemParam;
import com.chis.modules.system.enumn.DictType;
import com.chis.modules.system.enumn.FieldType;
import com.chis.modules.system.enumn.SystemType;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * <p>类描述： </p>
 *
 * @ClassAuthor: yzz
 * @date： 2022年06月24日
 **/
public class HethSystemParamPluginObj {
    public static Set<TsSystemParam> paramSet;

    static {
        paramSet = new HashSet<TsSystemParam>();

        paramSet.add(new TsSystemParam(new Integer(SystemType.ZYWS_TY.getTypeNo()), "RHK_EMHISTORY_REQUIR", "0",
                "是否开启初检且非岗前职业史必填（0：否，1：是）", Short.valueOf("1"),"rhk_emhistory_requir", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
        paramSet.add(new TsSystemParam(Integer.valueOf(SystemType.ZYWS_TY.getTypeNo()), "IF_CHK_ANAMNESIS", "0",
                "是否开启初检时既往史必填（0：否，1：是）", Short.valueOf("1"),"tjlrjws_many_select", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
        paramSet.add(new TsSystemParam(Integer.valueOf(SystemType.ZYWS_TY.getTypeNo()), "IF_CHK_JZS", "0",
                "是否开启初检时家族史必填（0：否，1：是）", Short.valueOf("1"),"tjlrjzs_many_select", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
        paramSet.add(new TsSystemParam(Integer.valueOf(SystemType.ZYWS_TY.getTypeNo()), "IF_CHK_RAD_HIS", "0",
                "是否开启初检时放射史必填（危害因素包含放射类时）（0：否，1：是）", Short.valueOf("1"),"tjlrfss_many_select", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
        paramSet.add(new TsSystemParam(Integer.valueOf(SystemType.ZYWS_TY.getTypeNo()), "IF_NEED_BHK_AUDIT", "1",
                "是否有个案审核业务（0：否，1：是）", Short.valueOf("1"), "sfygashyw", new Date(), 1, FieldType.SELECT_ONE_MENU, DictType.SELF_DEFINE,
                "否@@0##是@@1"));
        paramSet.add(new TsSystemParam(Integer.valueOf(SystemType.ZYWS_TY.getTypeNo()), "IF_ZYS_TIME_RANGE", "0",
                "体检录入职业史起止时间是否显示时间段（0：否，1：是）", Short.valueOf("1"),"tjlrzys_many_select", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
        paramSet.add(new TsSystemParam(Integer.valueOf(SystemType.ZYWS_TY.getTypeNo()), "IF_UPDATE_BHK", "1",
                "是否允许更新终审通过的体检数据（0：否，1：是）", Short.valueOf("1"),"sfyxgxshtgdtjsj", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
        paramSet.add(new TsSystemParam(Integer.valueOf(SystemType.ZYWS_TY.getTypeNo()), "IF_SHOW_CONSTRUCT_CRPT", "0",
                "体检用人单位是否显示健康企业情况（0：否，1：是）", Short.valueOf("1"),"tjyrdwsfxsjkqyqk", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
        paramSet.add(new TsSystemParam(Integer.valueOf(SystemType.ZYWS_TY.getTypeNo()), "IF_MULTIPLE_FACTORS", "0",
                "主动监测因素是否多个（0：否，1：是）", Short.valueOf("1"),"zdjcyssfdg", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
        paramSet.add(new TsSystemParam(Integer.valueOf(SystemType.ZYWS_TY.getTypeNo()), "REVISE_BHK_IN_ZDZYB", "1",
                "是否允许上传本年度重点职业病监测周期内的数据（1：允许，0：不允许）", Short.valueOf("1"),"sfyxscbndzdzybjczqndsj", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "允许@@1##不允许@@0"));
        paramSet.add(new TsSystemParam(new Integer(SystemType.ZYWS_TY.getTypeNo()), "REVISE_BHK_IN_ZDZYB_YEAR", "2024",
                				"不允许新增或修改重点职业病监测周期体检数据的年份（多个英文逗号分隔）", Short.valueOf("1"),"xykzzdzybjczqnf", new Date(), 1, FieldType.STRINGS,
                				DictType.NO_NEED, null));
        paramSet.add(new TsSystemParam(Integer.valueOf(SystemType.ZYWS_TY.getTypeNo()), "BHK_MISSING_MUST_ITEMS_ARE_ALLOWED", "0",
                "是否允许上传缺项的必检项目（1：允许，0：不允许）", Short.valueOf("1"),"sfyxscqxdbjxm", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "允许@@1##不允许@@0"));
        paramSet.add(new TsSystemParam(Integer.valueOf(SystemType.ZYWS_TY.getTypeNo()), "USE_NEW_CRPT_SELECT", "0",
                "是否使用天眼查通用用人单位弹出框（0：否，1：是）", Short.valueOf("1"),"sfsytyctyyrdwtck", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
        paramSet.add(new TsSystemParam(Integer.valueOf(SystemType.ZYWS_TY.getTypeNo()), "COMPANY_SEARCH_URL", "/api-system/tyc/company/search",
                "天眼查查询企业接口地址", Short.valueOf("1"),"cxqyjkdz", new Date(), 1, FieldType.STRINGS,
                DictType.NO_NEED, null));
    }
}
