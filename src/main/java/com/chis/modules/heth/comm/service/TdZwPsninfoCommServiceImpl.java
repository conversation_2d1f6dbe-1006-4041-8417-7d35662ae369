package com.chis.modules.heth.comm.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;

/**
 * @Description : 资质人员信息-业务层
 * @ClassAuthor : anjing
 * @Date : 2019/5/19 17:45
 **/
@Service
@Transactional(readOnly = true)
public class TdZwPsninfoCommServiceImpl extends AbstractTemplate {

    /**
     * @Description : 根据人员属性获取资质人员信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 17:52
     **/
    public List<Object[]> selectPsninfoListByPsnType(String entends1) {
        StringBuilder sb = new StringBuilder();
        TsUnit tsUnit = Global.getUser().getTsUnit();
        /*sb.append(" SELECT T1.RID, T1.EMP_NAME ");
        sb.append(" FROM TD_ZW_PSN_TYPE T ");
        sb.append(" INNER JOIN TD_ZW_PSNINFO T1 ON T.MAIN_ID = T1.RID ");
        sb.append(" INNER JOIN TS_SIMPLE_CODE T2 ON T.PSN_TYPE = T2.RID ");
        sb.append(" WHERE T2.EXTENDS3 IN (").append(entends1).append(")");
        // 显示本单位检查医生
        sb.append(" AND T1.ORG_ID =  ").append(tsUnit.getRid());
        sb.append(" GROUP BY T1.RID, T1.EMP_NAME ");*/

        sb.append(" SELECT T.RID, T.EMP_NAME ");
        sb.append(" FROM TD_ZW_PSNINFO T ");
        sb.append(" INNER JOIN TD_ZW_TJORGPSNS T1 ON T1.EMP_ID = T.RID ");
        sb.append(" INNER JOIN TD_ZW_TJORGINFO T2 ON T1.ORG_ID = T2.RID ");
        sb.append(" INNER JOIN TD_ZW_PSN_TYPE T3 ON T3.MAIN_ID = T.RID ");
        sb.append(" INNER JOIN TS_SIMPLE_CODE T4 ON T3.PSN_TYPE = T4.RID ");
        sb.append(" WHERE 1=1 ");
        sb.append(" AND T2.STATE = 1 ");
        // 显示本单位检查医生
        sb.append(" AND T2.ORG_ID = ").append(tsUnit.getRid());
        sb.append(" AND T1.ON_DUTY = 1 ");
        sb.append(" AND T4.EXTENDS3 IN (").append(entends1).append(")");
        sb.append(" GROUP BY T.RID, T.EMP_NAME ");
        return  super.em.createNativeQuery(sb.toString()).getResultList();
    }
}
