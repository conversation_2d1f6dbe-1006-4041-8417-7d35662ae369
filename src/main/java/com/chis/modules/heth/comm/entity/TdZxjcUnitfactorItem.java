package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-4
 */
@Entity
@Table(name = "TD_ZXJC_UNITFACTOR_ITEM")
@SequenceGenerator(name = "TdZxjcUnitfactorItem", sequenceName = "TD_ZXJC_UNITFACTOR_ITEM_SEQ", allocationSize = 1)
public class TdZxjcUnitfactorItem implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZxjcUnitfactorcrowd fkByMainId;
	private Integer type;
	private TsSimpleCode fkByFactorId;
	private Integer contactNum;
	private Integer dataType;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private String factorName;
	
	public TdZxjcUnitfactorItem() {
	}

	public TdZxjcUnitfactorItem(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZxjcUnitfactorItem")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZxjcUnitfactorcrowd getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZxjcUnitfactorcrowd fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "TYPE")	
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}	
			
	@ManyToOne
	@JoinColumn(name = "FACTOR_ID")			
	public TsSimpleCode getFkByFactorId() {
		return fkByFactorId;
	}

	public void setFkByFactorId(TsSimpleCode fkByFactorId) {
		this.fkByFactorId = fkByFactorId;
	}	
			
	@Column(name = "CONTACT_NUM")	
	public Integer getContactNum() {
		return contactNum;
	}

	public void setContactNum(Integer contactNum) {
		this.contactNum = contactNum;
	}	
			
	@Column(name = "DATA_TYPE")	
	public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
	@Column(name = "FACTOR_NAME")	
	public String getFactorName() {
		return factorName;
	}

	public void setFactorName(String factorName) {
		this.factorName = factorName;
	}	
			
}