package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2024-8-8
 */
@Entity
@Table(name = "TD_ZW_MONTH_ORG")
@SequenceGenerator(name = "TdZwMonthOrg", sequenceName = "TD_ZW_MONTH_ORG_SEQ", allocationSize = 1)
public class TdZwMonthOrg implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwMonthBhkProv fkByMainId;
    private TbTjSrvorg fkByOrgId;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private List<TdZwMonthOrgBadrsns> tdZwMonthOrgBadrsns;

    public TdZwMonthOrg() {
    }

    public TdZwMonthOrg(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwMonthOrg")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwMonthBhkProv getFkByMainId() {
        return this.fkByMainId;
    }

    public void setFkByMainId(TdZwMonthBhkProv fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "ORG_ID")
    public TbTjSrvorg getFkByOrgId() {
        return this.fkByOrgId;
    }

    public void setFkByOrgId(TbTjSrvorg fkByOrgId) {
        this.fkByOrgId = fkByOrgId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwMonthOrgBadrsns> getTdZwMonthOrgBadrsns() {
        return this.tdZwMonthOrgBadrsns;
    }

    public void setTdZwMonthOrgBadrsns(List<TdZwMonthOrgBadrsns> tdZwMonthOrgBadrsns) {
        this.tdZwMonthOrgBadrsns = tdZwMonthOrgBadrsns;
    }

}