package com.chis.modules.heth.comm.logic;

import com.chis.modules.system.entity.TsSimpleCode;


/**
 * <p>类描述：体检结论、病种对象</p>
 * @ClassAuthor qrr,2021年8月13日,TjBhkConClusionDTO
 * */
public class TjBhkConClusionDTO {
	/**体检结论*/
	private TsSimpleCode fkByConclusionId;
	/**病种*/
	private TsSimpleCode fkByDiseId;
	/**危害因素*/
	private String badRsns;
	/**病种*/
	private String diseNames;
	/**单位*/
	private String unitNames;
	/**其他疾病或异常描述*/
	private String qtjbName;

	public TsSimpleCode getFkByConclusionId() {
		return fkByConclusionId;
	}
	public void setFkByConclusionId(TsSimpleCode fkByConclusionId) {
		this.fkByConclusionId = fkByConclusionId;
	}
	public TsSimpleCode getFkByDiseId() {
		return fkByDiseId;
	}
	public void setFkByDiseId(TsSimpleCode fkByDiseId) {
		this.fkByDiseId = fkByDiseId;
	}
	public String getBadRsns() {
		return badRsns;
	}
	public void setBadRsns(String badRsns) {
		this.badRsns = badRsns;
	}

	public String getDiseNames() {
		return diseNames;
	}

	public void setDiseNames(String diseNames) {
		this.diseNames = diseNames;
	}

	public String getUnitNames() {
		return unitNames;
	}

	public void setUnitNames(String unitNames) {
		this.unitNames = unitNames;
	}

	public String getQtjbName() {
		return qtjbName;
	}

	public void setQtjbName(String qtjbName) {
		this.qtjbName = qtjbName;
	}
}
