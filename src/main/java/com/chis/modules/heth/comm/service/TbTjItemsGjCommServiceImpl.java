package com.chis.modules.heth.comm.service;

import com.chis.modules.heth.comm.entity.TbTjItemsGj;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>类描述： </p>
 *
 * @ClassAuthor: yzz
 * @date： 2022年09月17日
 **/
@Service
@Transactional
public class TbTjItemsGjCommServiceImpl extends AbstractTemplate {


    /**
     * <p>方法描述：findItemsGjByHql</p>
     * @MethodAuthor： yzz
     * @Date：2022-09-17
     **/
    public List<TbTjItemsGj> findItemsGjByHql(){
        StringBuilder hql=new StringBuilder();
        hql.append(" select T from TbTjItemsGj T ");
        return  this.findByHql(hql.toString(),TbTjItemsGj.class);
        
    }

}
