package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesSimpleBean;
import org.primefaces.context.RequestContext;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 *  <p>类描述：
 *  系统单位选择</p>
 * @ClassAuthor hsj 2022-08-20 16:34
 */
@ManagedBean(name="selectOrgUnitListBean")
@ViewScoped
public class SelectOrgUnitListBean extends FacesSimpleBean implements IProcessData{
	private static final long serialVersionUID = 1L;
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	/**查询条件：地区*/
	private List<TsZone> zoneList;
	private String searchZoneGb;
	private String searchZoneName;
	/**查询条件：单位名称*/
	private String searchName;
	/**查询条件：社会信用代码*/
	private String searchCode;

    private Object[] object;
    private Integer rid;
	private String zoneName;
	private String zoneGb;
	private Integer zoneRid;

	//单位信息
	/**单位信息*/
	private TsUnit tsUnit;

	/**是否可修改*/
	private Boolean ifCheck;
	/** 控制显示是否有分支机构（依据系统参数IF_SUB_ORG，甘肃没有分支机构） */
	private Boolean ifHasSubOrg;
	/**当前登录人的省份*/
	private TsZone  nowZone;

    public SelectOrgUnitListBean() {
    	this.ifSQL = true;
		 initZone();
		this.tsUnit = new TsUnit();
		this.zoneName = nowZone.getZoneName();
		this.zoneGb = nowZone.getZoneGb();
		this.zoneRid = nowZone.getRid();
		this.ifCheck = true;
		//是否存在分支机构 IF_SUB_ORG
		this.ifHasSubOrg = "1".equals(commService.findParamValue("IF_SUB_ORG"));
    	this.searchAction();
	}

	private void initZone() {
		//当前登录人的省份
		String jsZone = Global.getUser().getTsUnit().getTsZone().getZoneGb().substring(0, 2) + "00000000";
		StringBuffer buffer = new StringBuffer();
		nowZone = new TsZone();
		buffer.append(" select t from TsZone t where t.zoneGb =").append(jsZone);
		this.nowZone =  commService.findOneByHql(buffer.toString(), TsZone.class);
		//非医疗机构默认空，下拉可选全国，可清空
		this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(null, true, null, null);
		searchZoneGb = null;
		searchZoneName = null;
	}

	@Override
	public String[] buildHqls() {
    	StringBuffer sql = new StringBuffer();
		sql.append("SELECT T1.RID, CASE WHEN T2.ZONE_TYPE >2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME,'_')+1) ELSE T2.FULL_NAME END ZONE_NAME   ,T1.CREDIT_CODE");
		sql.append(" ,T1.UNITNAME  ,T1.UNITADDR,T1.LINK_MAN,T1.ORG_TEL,T1.ZONE_ID");
		sql.append(" FROM  TS_UNIT T1   ");
		sql.append("  LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID  = T2.RID   ");
		sql.append(" WHERE T1.IF_REVEAL = 1 ");
		//地区
		if (StringUtils.isNotBlank(searchZoneGb)) {
			sql.append(" and T2.zone_gb like :searchZoneGb ");
			this.paramMap.put("searchZoneGb", ZoneUtil.zoneSelect(searchZoneGb)+"%");
		}
		//社会信用代码
		if(StringUtils.isNotBlank(searchCode)){
			sql.append(" and T1.CREDIT_CODE like :searchCode escape '\\\'");
			this.paramMap.put("searchCode", "%"+StringUtils.convertBFH(searchCode.trim())+"%");
		}
		if (StringUtils.isNotBlank(searchName)) {
			sql.append(" and T1.UNITNAME like :searchName escape '\\\'");
			this.paramMap.put("searchName", "%"+StringUtils.convertBFH(searchName.trim())+"%");
		}
		sql.append(" ORDER BY T2.ZONE_GB ,T1.UNITNAME ");
		String h2 = "SELECT COUNT(*) FROM (" + sql.toString() + ")";
        String h1 = "SELECT * FROM (" + sql.toString() + ")";
        return new String[]{h1,h2};
	}
	/**
	 *  <p>方法描述：清空地区</p>
	 * @MethodAuthor hsj 2022-08-22 15:44
	 */
	public void clearSearchZone(){
		this.searchZoneGb = null;
		this.searchZoneName = null;
	}
	@Override
	public void processData(List<?> list) {
	}
	public void selectAction() {
		if (null!=object) {
			//需验证单位地址、联系人、联系电话不能为空，若有为空则提示“请先完善单位信息！”
			if(object == null || object[4] == null || object[5] == null || object[6] == null){
				JsfUtil.addErrorMessage("请先完善单位信息！");
				return;
			}
			if (object[6] != null && !StringUtils.vertyPhone(object[6].toString())) {
				JsfUtil.addErrorMessage("请先完善单位信息！");
				return;
			}
			Map<String, Object[]> map = new HashMap<String, Object[]>();
			map.put("selectPros", object);
			RequestContext.getCurrentInstance().closeDialog(map);
		}else {
			JsfUtil.addErrorMessage("请选择数据！");
			return;
		}
	}
	/**
	 *  <p>方法描述：添加单位</p>
	 * @MethodAuthor hsj 2022-08-22 11:10
	 */
	public void addUnit(){
		//地区初始化
		//当前登录人所在的身份
		this.tsUnit = new TsUnit();
		//系统参数无分支机构的地区 都作为主体机构
		if(!this.ifHasSubOrg){
			//是否分支机构 默认否
			this.tsUnit.setIfSubOrg("0");
		}
		this.zoneName = nowZone.getZoneName();
		this.zoneGb = nowZone.getZoneGb();
		this.zoneRid = nowZone.getRid();
		this.ifCheck = true;
		RequestContext.getCurrentInstance().update("mainForm:addUnitDialog");
		RequestContext.getCurrentInstance().execute("PF('AddUnitDialog').show();");
	}
	/**
	 *  <p>方法描述：单位添加</p>
	 * @MethodAuthor hsj 2022-08-22 11:34
	 */
	public void submitUnitAction(){
		try {
			boolean flag = true;
			TsZone tsZone = commService.find(TsZone.class,zoneRid);
			if(tsZone.getRealZoneType()<4){
				JsfUtil.addErrorMessage("地区只能选择区县及以下！");
				flag = false;
			}else {
				tsUnit.setTsZone(tsZone);
			}

			if(StringUtils.isBlank(tsUnit.getCreditCode())){
				JsfUtil.addErrorMessage("社会信用代码不能为空！");
				flag = false;
			}else if(!StringUtils.isCreditCode(tsUnit.getCreditCode())){
				JsfUtil.addErrorMessage("社会信用代码格式不正确！");
				flag = false;
			}else{
				if (StringUtils.isNotBlank(tsUnit.getIfSubOrg())) {
					if ("1".equals(this.tsUnit.getIfSubOrg())) {//是分支机构
						//主体机构必存在
						Integer count = systemModuleService.findUnitIfExists(tsUnit.getCreditCode(), tsUnit.getRid(), null,"0");
						if (null==count || count.intValue()==0) {
							JsfUtil.addErrorMessage("同社会信用代码的主体机构不存在！");
							flag = false;
						}
						//社会信用代码和单位名称联合唯一
						if (StringUtils.isNotBlank(tsUnit.getUnitname())) {
							Integer subCount = systemModuleService.findUnitIfExists(tsUnit.getCreditCode(), tsUnit.getRid(), tsUnit.getUnitname(),null);
							if (null!=subCount && subCount.intValue()>0) {
								JsfUtil.addErrorMessage("社会信用代码和单位名称已存在！");
								flag = false;
							}
						}
					}else {//主体机构
						//社会信用代码唯一
						Integer count = systemModuleService.findUnitIfExists(tsUnit.getCreditCode(), tsUnit.getRid(), null,"0");
						if (null!=count && count.intValue()>0) {
							JsfUtil.addErrorMessage("社会信用代码已存在！");
							flag = false;
						}
					}
				}
			}
			if(StringUtils.isBlank(tsUnit.getUnitname())){
				JsfUtil.addErrorMessage("单位名称不能为空！");
				flag = false;
			}
			if(StringUtils.isBlank(tsUnit.getUnitSimpname())){
				JsfUtil.addErrorMessage("单位简称不能为空！");
				flag = false;
			}
			if(StringUtils.isBlank(tsUnit.getUnitaddr())){
				JsfUtil.addErrorMessage("单位地址不能为空！");
				flag = false;
			}
			if(this.ifHasSubOrg && StringUtils.isBlank(tsUnit.getIfSubOrg())) {
				JsfUtil.addErrorMessage("是否分支机构不能为空！");
				flag = false;
			}
			if (StringUtils.isBlank(tsUnit.getLinkMan())) {
				JsfUtil.addErrorMessage("联系人不能为空！");
				flag = false;
			}
			if (StringUtils.isBlank(tsUnit.getOrgTel())) {
				JsfUtil.addErrorMessage("联系电话不能为空！");
				flag = false;
			}else {
				if (!StringUtils.vertyPhone(tsUnit.getOrgTel())) {
					JsfUtil.addErrorMessage("联系电话格式不正确！");
					flag = false;
				}
			}
			if(!flag){
				return;
			}
			tsUnit.setIfReveal((short) 1);
			//保存时更新该系统单位的‘管辖地区’和‘行政区划’为选择的地区。
			tsUnit.setFkByManagedZoneId(tsZone);
			tsUnit.setUpdateTag(0);
			if(tsUnit.getRid() == null){
				tsUnit.setCreateDate(new Date());
				tsUnit.setCreateManid(Global.getUser().getRid());
			}
			this.commService.upsertEntity(tsUnit);
			JsfUtil.addSuccessMessage("保存成功！");
			RequestContext.getCurrentInstance().execute("PF('AddUnitDialog').hide()");
			searchAction();
			//处理修改后双击失效的问题
//			RequestContext.getCurrentInstance().execute("bindDbclick()");
		} catch (Exception e) {
			JsfUtil.addErrorMessage("保存失败！");
			e.printStackTrace();
		}
	}
	/**
	 *  <p>方法描述 单位修改</p>
	 * @MethodAuthor hsj 2022-08-22 12:01
	 */
	public void updateUnit(){
		if(rid == null){
			tsUnit = new TsUnit();
			return;
		}
		this.tsUnit = commService.find(TsUnit.class, rid);
		this.zoneName = tsUnit.getTsZone().getZoneName();
		this.zoneGb = tsUnit.getTsZone().getZoneGb();
		this.zoneRid = tsUnit.getTsZone().getRid();
		this.ifCheck = false;
		if(null != tsUnit.getCreateManid() && Global.getUser().getRid().toString().equals(tsUnit.getCreateManid().toString())){
			this.ifCheck = true;
		}
		RequestContext.getCurrentInstance().update("mainForm:addUnitDialog");
		RequestContext.getCurrentInstance().execute("PF('AddUnitDialog').show();");
	}
    /**
 	 * <p>方法描述：关闭</p>
 	 * @MethodAuthor qrr,2018年4月9日,dialogClose
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

	public String getSearchZoneGb() {
		return searchZoneGb;
	}

	public void setSearchZoneGb(String searchZoneGb) {
		this.searchZoneGb = searchZoneGb;
	}

	public String getSearchZoneName() {
		return searchZoneName;
	}

	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}

	public String getSearchName() {
		return searchName;
	}

	public void setSearchName(String searchName) {
		this.searchName = searchName;
	}

	public String getSearchCode() {
		return searchCode;
	}

	public void setSearchCode(String searchCode) {
		this.searchCode = searchCode;
	}



	public Object[] getObject() {
		return object;
	}

	public void setObject(Object[] object) {
		this.object = object;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public boolean isIfCheck() {
		return ifCheck;
	}

	public void setIfCheck(boolean ifCheck) {
		this.ifCheck = ifCheck;
	}

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public TsUnit getTsUnit() {
		return tsUnit;
	}

	public void setTsUnit(TsUnit tsUnit) {
		this.tsUnit = tsUnit;
	}

	public TsZone getNowZone() {
		return nowZone;
	}

	public void setNowZone(TsZone nowZone) {
		this.nowZone = nowZone;
	}

	public String getZoneName() {
		return zoneName;
	}

	public void setZoneName(String zoneName) {
		this.zoneName = zoneName;
	}

	public String getZoneGb() {
		return zoneGb;
	}

	public void setZoneGb(String zoneGb) {
		this.zoneGb = zoneGb;
	}

	public Integer getZoneRid() {
		return zoneRid;
	}

	public void setZoneRid(Integer zoneRid) {
		this.zoneRid = zoneRid;
	}

	public Boolean getIfCheck() {
		return ifCheck;
	}

	public void setIfCheck(Boolean ifCheck) {
		this.ifCheck = ifCheck;
	}

	public Boolean getIfHasSubOrg() {
		return ifHasSubOrg;
	}

	public void setIfHasSubOrg(Boolean ifHasSubOrg) {
		this.ifHasSubOrg = ifHasSubOrg;
	}
}
