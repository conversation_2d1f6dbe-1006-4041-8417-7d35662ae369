package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-8-19
 */
@Entity
@Table(name = "TD_ZW_SRVORG_CARD_DOSE")
@SequenceGenerator(name = "TdZwSrvorgCardDose", sequenceName = "TD_ZW_SRVORG_CARD_DOSE_SEQ", allocationSize = 1)
public class TdZwSrvorgCardDose implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwSrvorgCard fkByMainId;
	private String psnName;
	private String idc;
	private Date jcDate;
	private BigDecimal doseCal;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwSrvorgCardDose() {
	}

	public TdZwSrvorgCardDose(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSrvorgCardDose")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwSrvorgCard getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwSrvorgCard fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "PSN_NAME")	
	public String getPsnName() {
		return psnName;
	}

	public void setPsnName(String psnName) {
		this.psnName = psnName;
	}	
			
	@Column(name = "IDC")	
	public String getIdc() {
		return idc;
	}

	public void setIdc(String idc) {
		this.idc = idc;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "JC_DATE")			
	public Date getJcDate() {
		return jcDate;
	}

	public void setJcDate(Date jcDate) {
		this.jcDate = jcDate;
	}	
			
	@Column(name = "DOSE_CAL")	
	public BigDecimal getDoseCal() {
		return doseCal;
	}

	public void setDoseCal(BigDecimal doseCal) {
		this.doseCal = doseCal;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}