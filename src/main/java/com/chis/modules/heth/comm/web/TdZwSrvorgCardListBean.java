package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.enumn.ReturnType;
import com.chis.modules.heth.comm.logic.SimpleCodeTreePO;
import com.chis.modules.heth.comm.rptvo.SrvorgPsnVo;
import com.chis.modules.heth.comm.utils.ZwSrvorgCardUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.enumn.SystemMessageEnum;
import com.chis.modules.system.logic.WordReportDTO;
import com.chis.modules.system.logic.WordReportJson;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.math.BigDecimal;
import java.util.*;

/**
 *  <p>类描述：放射卫生技术服务信息报送卡填报</p>
 * @ClassAuthor hsj 2022-08-19 13:34
 */
@ManagedBean(name = "tdZwSrvorgCardListBean")
@ViewScoped
public class TdZwSrvorgCardListBean   extends AbstractSrvorgCardBean {
    private String redStar;
    //文件
    private String reportFilePath;



    public TdZwSrvorgCardListBean(){
        this.ifSupportCancel = Boolean.TRUE;
        this.redStar = "<font color='red'>*</font>";
        this.initZone();
        String id =JsfUtil.getRequest().getParameter("rid");
        if(ObjectUtil.isNotEmpty(id)){
            this.rid = Integer.valueOf(id);
            this.viewInit();
            this.forwardOtherPage();
        }else{
            this.searchAction();
        }

    }
    /**
     *  <p>方法描述：地区初始化</p>
     * @MethodAuthor hsj 2022-08-19 14:01
     */
    private void initZone() {
        clearSearchZone();
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(null, true, null, null);
        //技术服务地址
        this.jsZoneList = this.commService.findZoneListByGbAndTypeNoNation(null, true,null , "4");
        zoneMap =new HashMap<>();
        for (TsZone zone:jsZoneList){
            zoneMap.put(zone.getZoneGb(),zone);
        }
    }
    /**
     *  <p>方法描述：添加
     *  验证【放射卫生技术服务机构资质申报】已提交
     *  </p>
     * @MethodAuthor hsj 2022-08-19 15:35
     */
    @Override
    public void addInit() {
        ifDate = false ;
        srvorgCard = new TdZwSrvorgCard();
        this.fillDataShowServiceList();
        //获取已提交的放射资质
        srvorginfo = srvorgCardService.getSrvorginfo(true);
        if(null == srvorginfo || null == srvorginfo.getRid()){
            JsfUtil.addErrorMessage("请先填报并提交放射卫生技术服务机构资质信息！");
            return;
        }
        //报告卡信息初始化
        initCard();
        srvorgCard.setChoosePsnIds(new HashSet<String>());
        //服务单位类型
        srvorgCard.setFkBySutId(new TsSimpleCode());
        // 检测类型
        srvorgCard.setFkByJcTypeId(new TsSimpleCode());
        srvorgCard.setUnitType(2);
        this.forwardEditPage();
    }
    /**
     *  <p>方法描述：报告卡信息初始化</p>
     * @MethodAuthor hsj 2022-08-19 16:00
     */
    private void initCard() {
        //机构信息
        initOrg();
        //服务的用人单位信息
        //单位类型 默认医疗机构，切换单位类型弹出框提示将清空单位信息
        srvorgCard.setUnitType(1);
        //出具技术服务报告时间
        srvorgCard.setRptDate(new Date());
        srvorgCard.setFkByFillUnitId(Global.getUser().getTsUnit());
        srvorgCard.setDelMark(0);
        srvorgCard.setState(0);
        //服务技术结果
        initResult();
        //gt20msvBhkNum
        srvorgCard.setGt20msvBhkNum(0);
        //填表信息
        //填报单位只读，放射卫生技术服务机构资质的单位名称
        srvorgCard.setFillUnitName(srvorginfo.getOrgName());
        //单位负责人 默认放射卫生技术服务机构资质的联系人
        srvorgCard.setOrgFzMan(srvorginfo.getLinkMan());
        //填表人默认当年登录人
        srvorgCard.setFillFormPsn(Global.getUser().getUsername());
        //填表人联系电话
        srvorgCard.setFillLink(Global.getUser().getMbNum());
        //填表日期
        srvorgCard.setFillDate(new Date());
    }

    /**
     *  <p>方法描述：机构信息初始化</p>
     * @MethodAuthor hsj 2022-08-19 16:01
     */
    private void initOrg() {
        //机构名称-放射卫生技术服务机构资质的单位名称
        srvorgCard.setOrgName(srvorginfo.getOrgName());
        //法定代表人-放射卫生技术服务机构资质的法定代表人
        srvorgCard.setOrgFz(srvorginfo.getOrgFz());
        //注册地址-放射卫生技术服务机构资质的单位地址
        srvorgCard.setOrgAddr(srvorginfo.getOrgAddr());
        //机构资质证书编号-放射卫生技术服务机构资质的资质证书编号
        srvorgCard.setCertNo(srvorginfo.getCertNo());
        //项目负责人 -默认取值放射卫生技术服务机构中资质人员的人员属性为技术负责人（5308的extend3的值为5）的姓名，有多个取第一个，可编辑
        Object[] proInfo = srvorgCardService.getProInfo(srvorginfo.getRid());
        if(null != proInfo){
            srvorgCard.setProFz(proInfo[0] == null ? null :proInfo[0].toString());
            srvorgCard.setProLinktel(proInfo[1] == null ? null :proInfo[1].toString());
        }
        //资质业务范围--放射卫生技术服务机构资质的业务范围大类，去重，多个中文逗号隔开
        srvorgCardService.getTdZwSrvorgCardItems(srvorgCard,srvorginfo.getRid());

    }
    /**
     *  <p>方法描述：添加人员信息</p>
     * @MethodAuthor hsj 2022-08-20 14:15
     */
    public void addsrvorgCardPsn(){

        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("height", 505);
        options.put("width", 620);
        options.put("contentWidth", 585);
        options.put("contentHeight", 500);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        String psnIds = "";
        if(srvorgCard.getChoosePsnIds() != null && srvorgCard.getChoosePsnIds().size() > 0){
            String psnId = "";
            for(String str:srvorgCard.getChoosePsnIds()){
                psnId += ","+str;
            }
            if(StringUtils.isNotBlank(psnId)){
                psnIds = psnId.substring(1,psnId.length());
            }
        }
        paramList.add(psnIds);
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<String>();
        paramList.add("1");
        paramMap.put("type", paramList);
        paramList = new ArrayList<String>();
        paramList.add(srvorginfo.getRid().toString());
        paramMap.put("orgId", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/dialog/selectOrgPsnList", options, paramMap);
    }
    /**
     *  <p>方法描述：选择人员</p>
     * @MethodAuthor hsj 2022-08-11 14:34
     */
    public void onSrvorgCardPsn(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<SrvorgPsnVo> list = (List<SrvorgPsnVo>) selectedMap.get("selectPros");
            if (null!=list && list.size()>0) {
                for (SrvorgPsnVo t : list) {
                    TdZwSrvorgCardPsn tdZwSrvorgCardPsn = new TdZwSrvorgCardPsn();
                    tdZwSrvorgCardPsn.setFkByPsnId(new TdZwPsninfoComm(t.getRid()));
                    tdZwSrvorgCardPsn.setPsnName(t.getEmpName());
                    srvorgCard.getSrvorgCardPsnList().add(tdZwSrvorgCardPsn);
                    srvorgCard.getChoosePsnIds().add(t.getRid().toString());
                }
            }
        }
    }
    /**
     *  <p>方法描述：删除人员信息</p>
     * @MethodAuthor hsj 2022-08-20 14:48
     */
    public void  delSrvorgCardPsnAction(TdZwSrvorgCardPsn tdZwSrvorgCardPsn){
        if (null != tdZwSrvorgCardPsn) {
            srvorgCard.getChoosePsnIds().remove(tdZwSrvorgCardPsn.getFkByPsnId().getRid().toString());
            srvorgCard.getSrvorgCardPsnList().remove(tdZwSrvorgCardPsn);

        }
    }
    /**
     *  <p>方法描述：单位弹框</p>
     * @MethodAuthor hsj 2022-08-22 9:57
     */
    public void selectUnitList() {
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("height", 505);
        options.put("width", 870);
        options.put("contentWidth", 835);
        options.put("contentHeight", 500);

        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<String>();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        if("1".equals(srvorgCard.getUnitType().toString())){
            if(null != srvorgCard.getFkByUnitId() && null != srvorgCard.getFkByUnitId().getRid()){
                paramList = new ArrayList<String>();
                paramList.add(srvorgCard.getFkByUnitId().getRid().toString());
                paramMap.put("selectIds", paramList);
            }
            requestContext.openDialog("/webapp/heth/comm/dialog/selectRadHethList", options, paramMap);
        }else {
            requestContext.openDialog("/webapp/heth/comm/dialog/selectOrgUnitList", options, paramMap);
        }

    }
    /**
     *  <p>方法描述：单位选择后</p>
     * @MethodAuthor hsj 2022-08-22 13:50
     */
    public void onCrptSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            Object[] selectOrgs = (Object[]) selectedMap.get("selectPros");
            if (null!=selectOrgs ) {
                srvorgCard.setFkByUnitId(new TsUnit(Integer.valueOf(selectOrgs[0].toString())));
                srvorgCard.setUnitName(selectOrgs[3] == null ? null : selectOrgs[3].toString());
                srvorgCard.setAddress(selectOrgs[4] == null ? null : selectOrgs[4].toString());
                srvorgCard.setLinkMan(selectOrgs[5] == null ? null : selectOrgs[5].toString());
                srvorgCard.setLinkPhone(selectOrgs[6] == null ? null : selectOrgs[6].toString());
                srvorgCard.setFkByZoneId(new TsZone(Integer.valueOf(selectOrgs[7].toString())));
                srvorgCard.setCreditCode(selectOrgs[2] == null ? null : selectOrgs[2].toString());
            }
        }
    }
    /**
     *  <p>方法描述：单位类型的切换</p>
     * @MethodAuthor hsj 2022-08-22 14:04
     */
    public  void changeUnitType(){
        srvorgCard.setFkByUnitId(new TsUnit());
        srvorgCard.setUnitName("");
        srvorgCard.setAddress("");
        srvorgCard.setLinkMan("");
        srvorgCard.setLinkPhone("");
    }
    /**
     *  <p>方法描述：</p>
     * @MethodAuthor hsj 2022-08-22 9:57
     */
    public List<TsSimpleCode> getTsSimpleCode(String type){
            return tsSimpleCodeMap.get(type);
    }
    @Override
    public void viewInit() {
        if(null != this.rid){
            this.ifSupportCancel = this.srvorgCardService.queryRcdCountByParams(22,this.rid, 3) == 0;
        }
        super.viewInit();
    }
    /**
     *  <p>方法描述：修改初始化</p>
     */
    @Override
    public void modInit() {
       super.modInit();
    }
    /**
     *  <p>方法描述：暂存</p>
     * @MethodAuthor hsj 2022-08-19 13:37
     */
    @Override
    public void saveAction() {
        try {
            if (veryData()) {
                return;
            }
            saveOrUpdateSrvorgCard();
            if(0 == srvorgCard.getState() && StringUtils.isNotBlank(srvorgCard.getAnnexPath())){
                RequestContext currentInstance = RequestContext.getCurrentInstance();
                currentInstance.execute("disabledInput('true','srvorgCardDiv')");
            }
            JsfUtil.addSuccessMessage("暂存成功！");

        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("暂存失败！");
        }
    }
    /**
     *  <p>方法描述：报告卡保存并且重新查询排序且页面重新查询</p>
     * @MethodAuthor hsj 2022-09-01 16:22
     */
    public void saveOrUpdateSrvorgCard(){
        //技术服务结果处理
        srvorgCardResult();
        ZwSrvorgCardUtils.clearJcSubCommEntity(srvorgCard);
        this.srvorgCardService.saveSrvorgCard(srvorgCard);
        this.srvorgCard =  this.srvorgCardService.searchCardService(srvorgCard.getRid());
        ZwSrvorgCardUtils.initEntity(srvorgCard);
        this.searchAction();
    }
    /**
     *  <p>方法描述：逻辑验证-保存</p>
     * @MethodAuthor hsj 2022-08-20 15:02
     */
    private boolean veryData() {
        boolean flag = false;
        if(null == srvorgCard.getFkByUnitId() || null == srvorgCard.getFkByUnitId().getRid()){
            JsfUtil.addErrorMessage("单位名称不能为空！");
            flag = true;
        }
        //出具技术服务报告时间
        if(null == srvorgCard.getRptDate()){
            JsfUtil.addErrorMessage("出具技术服务报告时间不能为空！");
            flag = true;
        }
        // 验证联系点话
        if (StringUtils.isNotBlank(srvorgCard.getProLinktel()) && !StringUtils.vertyPhone(srvorgCard.getProLinktel())) {
            JsfUtil.addErrorMessage("机构信息联系电话格式不正确！");
            flag = true;
        }
        if (StringUtils.isNotBlank(srvorgCard.getLinkPhone()) && !StringUtils.vertyPhone(srvorgCard.getLinkPhone())) {
            JsfUtil.addErrorMessage("服务的用人单位信息联系电话格式不正确！");
            flag = true;
        }
        if(StringUtils.isBlank(srvorgCard.getCardNo())){
            //新增设置报告卡编号 FSWS+放射卫生技术服务机构地区编码10位+年月日+5位流水
            String aa = commService.getAutoCode("HETH_SRVORG_CARD_CODE", null);
            srvorgCard.setCardNo("FSWS"+srvorginfo.getZoneGb()+DateUtils.formatDate(new Date(),"yyyyMMdd")+aa);
        }
        //参与人员
        if(!CollectionUtils.isEmpty(srvorgCard.getSrvorgCardPsnList())){
            for (TdZwSrvorgCardPsn srvorgCardPsn : srvorgCard.getSrvorgCardPsnList()){
                List<TdZwSrvorgCardItem> items = new ArrayList<>();
                srvorgCardPsn.setTdZwSrvorgCardItemList(items);
                if(null != srvorgCardPsn.getSrvorgCardItemIds() && srvorgCardPsn.getSrvorgCardItemIds().length > 0){
                    for(String str : srvorgCardPsn.getSrvorgCardItemIds()){
                        TdZwSrvorgCardItem item = new TdZwSrvorgCardItem();
                        item.setFkByMainId(srvorgCardPsn);
                        item.setFkByItemId(new TsSimpleCode(Integer.valueOf(str)));
                        items.add(item);
                    }
                    srvorgCardPsn.setTdZwSrvorgCardItemList(items);
                }
            }
        }
        //技术服务领域
        srvorgCard.getSrvorgCardServices().clear();
        if (!CollectionUtils.isEmpty(this.showServiceList)) {
            for (SimpleCodeTreePO treePO : this.showServiceList) {
                if (treePO.getIfDisabled()) {
                    treePO.setIfSelected(Boolean.FALSE);
                }
                if (!treePO.getIfSelected() || treePO.getIfDisabled()) {
                    continue;
                }
                TdZwSrvorgCardService tdZwSrvorgCardService = new TdZwSrvorgCardService();
                tdZwSrvorgCardService.setFkByMainId(srvorgCard);
                tdZwSrvorgCardService.setFkByServiceAreaId(treePO.getSimpleCode());
                tdZwSrvorgCardService.setFkByFatherAreaId(null == treePO.getFatherSimple() ? treePO.getSimpleCode() : treePO.getFatherSimple());
                srvorgCard.getSrvorgCardServices().add(tdZwSrvorgCardService);
            }
        }
        //出具技术服务报告时间
        if(null != srvorgCard.getRptDate()) {
            //当现场采样时间结束日期不为空时，则验证出具技术服务报告时间大于现场采样时间结束日期；
            if (null != srvorgCard.getInvestEndDate() && DateUtils.isCompareDate(srvorgCard.getRptDate(), "<", srvorgCard.getInvestEndDate())) {
                JsfUtil.addErrorMessage("出具技术服务报告时间应大于等于现场采样结束日期！");
                flag = true;
            }
            //当现场采样时间结束日期为空时，现场采样时间开始日期不为空时，则验证出具技术服务报告时间大于现场采样时间开始日期；
            if (null == srvorgCard.getInvestEndDate() && null != srvorgCard.getInvestStartDate() && DateUtils.isCompareDate(srvorgCard.getRptDate(), "<", srvorgCard.getInvestStartDate())) {
                JsfUtil.addErrorMessage("出具技术服务报告时间应大于等于现场采样开始日期！");
                flag = true;
            }
            //当现场检测时间结束日期不为空时，则验证出具技术服务报告时间大于现场检测时间结束日期；
            if (null != srvorgCard.getJcEndDate() && DateUtils.isCompareDate(srvorgCard.getRptDate(), "<", srvorgCard.getJcEndDate())) {
                JsfUtil.addErrorMessage("出具技术服务报告时间应大于等于现场检测结束日期！");
                flag = true;
            }
            //当现场检测时间结束日期为空，现场检测时间开始日期不为空时，则验证出具技术服务报告时间大于现场检测时间开始日期；
            if (null == srvorgCard.getJcEndDate() && null != srvorgCard.getJcStartDate() && DateUtils.isCompareDate(srvorgCard.getRptDate(), "<", srvorgCard.getJcStartDate())) {
                JsfUtil.addErrorMessage("出具技术服务报告时间应大于等于现场检测开始日期！");
                flag = true;
            }
        }
        //放射卫生防护检测 ifFswsFhJc
        if(1 == srvorgCard.getIfFswsFhJc()){
            //开展放射诊疗工作场所放射防护检测 ifFhJc
            if(1 == srvorgCard.getIfFhJc()){
                if(null != srvorgCard.getFhJcPoint() && srvorgCard.getFhJcPoint() == 0){
                    JsfUtil.addErrorMessage("开展放射诊疗工作场所放射防护检测的检测点位要大于0！");
                    flag = true;
                }
                if(null != srvorgCard.getFhJcPoint() && null != srvorgCard.getNotHgFhJcPoint() && srvorgCard.getNotHgFhJcPoint() > srvorgCard.getFhJcPoint()){
                    JsfUtil.addErrorMessage("开展放射诊疗工作场所放射防护检测的超标点位应小于等于检测点位！");
                    flag = true;
                }
                srvorgCard.getSrvorgCardFhjcList().clear();
                if(null != srvorgCard.getSrvorgCardFhjcs() && srvorgCard.getSrvorgCardFhjcs().length > 0){
                    //TD_ZW_SRVORG_CARD_FHJC
                    for(String s : srvorgCard.getSrvorgCardFhjcs()){
                        TdZwSrvorgCardFhjc tdZwSrvorgCardFhjc =new TdZwSrvorgCardFhjc();
                        tdZwSrvorgCardFhjc.setFkByMainId(srvorgCard);
                        tdZwSrvorgCardFhjc.setFkByTypeId(new TsSimpleCode(Integer.valueOf(s)));
                        srvorgCard.getSrvorgCardFhjcList().add(tdZwSrvorgCardFhjc);
                    }
                }
            }
            //开展放射诊疗设备质量控制检测 ifInstZkJc
            if(1 == srvorgCard.getIfInstZkJc()){
                if(null != srvorgCard.getZkJcInstNum() && srvorgCard.getZkJcInstNum() == 0){
                    JsfUtil.addErrorMessage("开展放射诊疗设备质量控制检测的检测设备要大于0！");
                    flag = true;
                }
                if(null != srvorgCard.getZkJcInstNum() && null != srvorgCard.getNotHgZkJcInstNum() && srvorgCard.getNotHgZkJcInstNum() > srvorgCard.getZkJcInstNum()){
                    JsfUtil.addErrorMessage("开展放射诊疗设备质量控制检测的不合格的设备应小于等于检测设备！");
                    flag = true;
                }
            }

        }
        //放射诊疗建设项目评价 ifItemPj
        if(1 == srvorgCard.getIfItemPj()){
            //预评价 ifItemYpj
            if(1 == srvorgCard.getIfItemYpj()){
                // TdZwSrvorgCardYpj
                srvorgCard.getSrvorgCardYpjList().clear();
                if(null != srvorgCard.getSrvorgCardYpjs() && srvorgCard.getSrvorgCardYpjs().length > 0){
                    //TD_ZW_SRVORG_CARD_YPJ
                    for(String s : srvorgCard.getSrvorgCardYpjs()){
                        TdZwSrvorgCardYpj tdZwSrvorgCardYpj =new TdZwSrvorgCardYpj();
                        tdZwSrvorgCardYpj.setFkByMainId(srvorgCard);
                        tdZwSrvorgCardYpj.setFkByTypeId(new TsSimpleCode(Integer.valueOf(s)));
                        srvorgCard.getSrvorgCardYpjList().add(tdZwSrvorgCardYpj);
                    }
                }
            }
            //控制效果评价 ifItemXgpj
            if(1 == srvorgCard.getIfItemXgpj()){
                //XGPJ_JC_POINT
                if(null != srvorgCard.getXgpjJcPoint() && srvorgCard.getXgpjJcPoint() == 0){
                    JsfUtil.addErrorMessage("控制效果评价的检测点位要大于0！");
                    flag = true;
                }
                //NOT_HG_XGPJ_JC_POINT 大于等于0，小于等于检测点位数
                if(null != srvorgCard.getXgpjJcPoint() && null != srvorgCard.getNotHgXgpjJcPoint() && srvorgCard.getNotHgXgpjJcPoint() > srvorgCard.getXgpjJcPoint()){
                    JsfUtil.addErrorMessage("控制效果评价的超标点位应小于等于检测点位！");
                    flag = true;
                }
                // TdZwSrvorgCardXgpj srvorgCardXgpjs
                srvorgCard.getSrvorgCardXgpjList().clear();
                if(null != srvorgCard.getSrvorgCardXgpjs() && srvorgCard.getSrvorgCardXgpjs().length > 0){
                    //TD_ZW_SRVORG_CARD_YPJ
                    for(String s : srvorgCard.getSrvorgCardXgpjs()){
                        TdZwSrvorgCardXgpj tdZwSrvorgCardXgpj =new TdZwSrvorgCardXgpj();
                        tdZwSrvorgCardXgpj.setFkByMainId(srvorgCard);
                        tdZwSrvorgCardXgpj.setFkByTypeId(new TsSimpleCode(Integer.valueOf(s)));
                        srvorgCard.getSrvorgCardXgpjList().add(tdZwSrvorgCardXgpj);
                    }
                }
            }
        }
        //个人剂量监测 ifDoseMonit
        if(1 == srvorgCard.getIfDoseMonit()){
            //监测人数（个人剂量监测）DOSE_MONIT_NUM
            if(null != srvorgCard.getDoseMonitNum() && srvorgCard.getDoseMonitNum() == 0){
                JsfUtil.addErrorMessage("个人剂量监测的监测人数要大于0！");
                flag = true;
            }
            //个人剂量监测的超过20mSv人数应小于等于监测人数
            if(null != srvorgCard.getDoseMonitNum()){
                if(null == srvorgCard.getGt5msv() && null != srvorgCard.getGt20msvBhkNum() && srvorgCard.getDoseMonitNum().compareTo(srvorgCard.getGt20msvBhkNum()) == -1 ){
                    JsfUtil.addErrorMessage("个人剂量监测的5~20mSv人数+超过20mSv人数应小于等于监测人数！");
                    flag = true;
                }else if(null == srvorgCard.getGt20msvBhkNum() && null != srvorgCard.getGt5msv() && srvorgCard.getDoseMonitNum().compareTo(srvorgCard.getGt5msv()) == -1){
                    JsfUtil.addErrorMessage("个人剂量监测的5~20mSv人数+超过20mSv人数应小于等于监测人数！");
                    flag = true;
                }else if(null != srvorgCard.getGt20msvBhkNum() && null != srvorgCard.getGt5msv() ){
                    Integer num = srvorgCard.getGt5msv() +  srvorgCard.getGt20msvBhkNum();
                    if(srvorgCard.getDoseMonitNum().compareTo(num) == -1){
                        JsfUtil.addErrorMessage("个人剂量监测的5~20mSv人数+超过20mSv人数应小于等于监测人数！");
                        flag = true;
                    }
                }
            }
            if(!CollectionUtils.isEmpty(srvorgCard.getSrvorgCardDoseList())){
                for (int i = 0;i< srvorgCard.getSrvorgCardDoseList().size() ;i++){
                    TdZwSrvorgCardDose cardDose = srvorgCard.getSrvorgCardDoseList().get(i);
                    //身份证号
                    if(StringUtils.isNotBlank(cardDose.getIdc()) && StringUtils.isNoneBlank(IdcUtils.checkIDC(cardDose.getIdc()))){
                        JsfUtil.addErrorMessage("个人剂量监测第"+(i+1)+"行身份证格式不正确！");
                        flag = true;
                    }
                    //检测日期 --小于等于出具技术服务报告时间
                    if(null != cardDose.getJcDate() && null != srvorgCard.getRptDate() && DateUtils.isCompareDate(srvorgCard.getRptDate(),"<",cardDose.getJcDate())){
                        JsfUtil.addErrorMessage("个人剂量监测第"+(i+1)+"行检测日期应小于等于出具技术服务报告时间！");
                        flag = true;
                    }
                    //X、γ外照射个人剂量当量项目值 大于20
                    if(null != cardDose.getDoseCal() && cardDose.getDoseCal().compareTo(new BigDecimal(20)) <= 0){
                        JsfUtil.addErrorMessage("个人剂量监测第"+(i+1)+"行X、γ外照射个人剂量当量项目值应大于20！");
                        flag = true;
                    }
                }
            }
        }
        //放射防护器材和含放射性产品检测 ifFhInstJc
        if(1 == srvorgCard.getIfFhInstJc()){
            //开展放射防护器材检测 ifJcInst
            if(1 == srvorgCard.getIfJcInst()){
                if(null != srvorgCard.getJcInstNum() && srvorgCard.getJcInstNum() == 0){
                    JsfUtil.addErrorMessage("开展放射防护器材检测的共检测样品数量要大于0！");
                    flag = true;
                }
                if(null != srvorgCard.getJcInstNum() && null != srvorgCard.getJcNotHgInstNum() && srvorgCard.getJcNotHgInstNum() > srvorgCard.getJcInstNum()){
                    JsfUtil.addErrorMessage("开展放射防护器材检测的超标样品数量应小于等于检测样品数量！");
                    flag = true;
                }
            }
            //开展含放射性产品检测 ifJcUse
            if(1 == srvorgCard.getIfJcUse()){
                if(null != srvorgCard.getJcUseNum() && srvorgCard.getJcUseNum() == 0){
                    JsfUtil.addErrorMessage("开展含放射性产品检测的共检测样品数量要大于0！");
                    flag = true;
                }
                if(null != srvorgCard.getJcUseNum() && null != srvorgCard.getJcNotHgUseNum() && srvorgCard.getJcNotHgUseNum() > srvorgCard.getJcUseNum()){
                    JsfUtil.addErrorMessage("开展含放射性产品检测的超标样品数量应小于等于检测样品数量！");
                    flag = true;
                }
            }
        }
        //填表人联系电话
        if(StringUtils.isNotBlank(srvorgCard.getFillLink()) &&  !StringUtils.vertyPhone(srvorgCard.getFillLink())){
            JsfUtil.addErrorMessage("填表人联系电话格式不正确！");
            flag = true;
        }
        //填表日期 大于等于出具报告日期，小于等于当前日期
        if(null != srvorgCard.getFillDate()){
            if(DateUtils.isCompareDate(srvorgCard.getFillDate() ,">",new Date())){
                JsfUtil.addErrorMessage("填表日期应小于等于当前日期！");
                flag = true;
            }
            if(null != srvorgCard.getRptDate() && DateUtils.isCompareDate(srvorgCard.getFillDate() ,"<",srvorgCard.getRptDate())){
                JsfUtil.addErrorMessage("填表日期应大于等于出具技术服务报告时间！");
                flag = true;
            }

        }
        return flag;
    }
    /**
     *  <p>方法描述：技术服务结果处理</p>
     * @MethodAuthor hsj 2022-08-24 16:18
     */
    private void srvorgCardResult() {
        //放射卫生防护检测
        //IF_FSWS_FH_JC
        if(0 == srvorgCard.getIfFswsFhJc()){
            //是否开展放射诊疗工作场所放射防护检测 -IF_FH_JC
            srvorgCard.setIfFhJc(0);
            //检测点位数 FH_JC_POINT
            srvorgCard.setFhJcPoint(null);
            //检测点位数 超标点位数NOT_HG_FH_JC_POINT
            srvorgCard.setNotHgFhJcPoint(null);
            //超标点位放射性危害类型 TD_ZW_SRVORG_CARD_FHJC
            srvorgCard.setSrvorgCardFhjcs(null);
            srvorgCard.getSrvorgCardFhjcList().clear();

            //开展放射诊疗设备质量控制检测 IF_INST_ZK_JC
            srvorgCard.setIfInstZkJc(0);
            //检测类别
            srvorgCard.setFkByJcTypeId(null);
            //检测设备数 ZK_JC_INST_NUM
            srvorgCard.setZkJcInstNum(null);
            //检测结果有一项以上指标不合格的设备数 NOT_HG_ZK_JC_INST_NUM
            srvorgCard.setNotHgZkJcInstNum(null);
            //不合格的设备名称 NOT_HG_ZK_JC_INST_NAME
            srvorgCard.setNotHgZkJcInstName(null);
        }
        if(0 == srvorgCard.getIfFhJc()){
            //检测点位数 FH_JC_POINT
            srvorgCard.setFhJcPoint(null);
            //检测点位数 超标点位数NOT_HG_FH_JC_POINT
            srvorgCard.setNotHgFhJcPoint(null);
            //超标点位放射性危害类型 TD_ZW_SRVORG_CARD_FHJC
            srvorgCard.setSrvorgCardFhjcs(null);
            srvorgCard.getSrvorgCardFhjcList().clear();
        }else {
            //检测点位数 为空或者为0时
            if(null ==  srvorgCard.getNotHgFhJcPoint() || 0 == srvorgCard.getNotHgFhJcPoint()){
                //超标点位放射性危害类型 TD_ZW_SRVORG_CARD_FHJC
                srvorgCard.setSrvorgCardFhjcs(null);
                srvorgCard.getSrvorgCardFhjcList().clear();
            }
        }
        if(0 == srvorgCard.getIfInstZkJc()){
            //检测类别
            srvorgCard.setFkByJcTypeId(null);
            //检测设备数 ZK_JC_INST_NUM
            srvorgCard.setZkJcInstNum(null);
            //检测结果有一项以上指标不合格的设备数 NOT_HG_ZK_JC_INST_NUM
            srvorgCard.setNotHgZkJcInstNum(null);
            //不合格的设备名称 NOT_HG_ZK_JC_INST_NAME
            srvorgCard.setNotHgZkJcInstName(null);
        }else{
            //检测结果有一项以上指标不合格的设备数 为空时
            if(null ==  srvorgCard.getNotHgZkJcInstNum() || 0 == srvorgCard.getNotHgZkJcInstNum()){
                //不合格的设备名称 NOT_HG_ZK_JC_INST_NAME
                srvorgCard.setNotHgZkJcInstName(null);
            }
        }
        //放射诊疗建设项目评价 IF_ITEM_PJ
        if(0 == srvorgCard.getIfItemPj()){
            //预评价 IF_ITEM_YPJ
            srvorgCard.setIfItemYpj(0);
            //剂量估算超标点位数 YPJ_POINT
            srvorgCard.setYpjPoint(null);
            //超标点位放射性危害类型 TD_ZW_SRVORG_CARD_YPJ
            srvorgCard.setSrvorgCardYpjs(null);
            srvorgCard.getSrvorgCardYpjList().clear();

            //控制效果评价 IF_ITEM_XGPJ
            srvorgCard.setIfItemXgpj(0);
            //检测点位数（控制效果评价） XGPJ_JC_POINT
            srvorgCard.setXgpjJcPoint(null);
            //超标点位数（控制效果评价）NOT_HG_XGPJ_JC_POINT
            srvorgCard.setNotHgXgpjJcPoint(null);
            //超标点位放射性危害类型 TD_ZW_SRVORG_CARD_XGPJ
            srvorgCard.setSrvorgCardXgpjs(null);
            srvorgCard.getSrvorgCardXgpjList().clear();
        }
        if(0 == srvorgCard.getIfItemYpj()){
            //剂量估算超标点位数 YPJ_POINT
            srvorgCard.setYpjPoint(null);
            //超标点位放射性危害类型 TD_ZW_SRVORG_CARD_YPJ
            srvorgCard.setSrvorgCardYpjs(null);
            srvorgCard.getSrvorgCardYpjList().clear();
        }else {
            //剂量估算超标点位数 为空时
            if(null ==  srvorgCard.getYpjPoint() || 0 == srvorgCard.getYpjPoint()){
                //超标点位放射性危害类型 TD_ZW_SRVORG_CARD_YPJ
                srvorgCard.setSrvorgCardYpjs(null);
                srvorgCard.getSrvorgCardYpjList().clear();
            }
        }
        if(0 == srvorgCard.getIfItemXgpj()){
            //检测点位数（控制效果评价） XGPJ_JC_POINT
            srvorgCard.setXgpjJcPoint(null);
            //超标点位数（控制效果评价）NOT_HG_XGPJ_JC_POINT
            srvorgCard.setNotHgXgpjJcPoint(null);
            //超标点位放射性危害类型 TD_ZW_SRVORG_CARD_XGPJ
            srvorgCard.setSrvorgCardXgpjs(null);
            srvorgCard.getSrvorgCardXgpjList().clear();
        }else {
            if(null ==  srvorgCard.getNotHgXgpjJcPoint() || 0 == srvorgCard.getNotHgXgpjJcPoint()){
                //超标点位放射性危害类型 TD_ZW_SRVORG_CARD_XGPJ
                srvorgCard.setSrvorgCardXgpjs(null);
                srvorgCard.getSrvorgCardXgpjList().clear();
            }
        }
        //个人剂量监测 IF_DOSE_MONIT
        if(0 == srvorgCard.getIfDoseMonit()){
            //监测人数（个人剂量监测）DOSE_MONIT_NUM
            srvorgCard.setDoseMonitNum(null);
            //5~20MSV人数（个人剂量监测） GT_5MSV
            srvorgCard.setGt5msv(null);
            //超过20MSV人数（个人剂量监测） GT_20MSV_BHK_NUM
            srvorgCard.setGt20msvBhkNum(0);
            //个人剂量监测 TD_ZW_SRVORG_CARD_DOSE
            srvorgCard.getSrvorgCardDoseList().clear();
        }
        if(0 == srvorgCard.getIfFhInstJc()){
            srvorgCard.setIfJcInst(0);
            srvorgCard.setJcInstNum(null);
            srvorgCard.setJcNotHgInstNum(null);
            srvorgCard.setNotHgInstName(null);

            srvorgCard.setIfJcUse(0);
            srvorgCard.setJcUseNum(null);
            srvorgCard.setJcNotHgUseNum(null);
            srvorgCard.setNotHgUseName(null);
        }
        if(0 == srvorgCard.getIfJcInst()){
            srvorgCard.setJcInstNum(null);
            srvorgCard.setJcNotHgInstNum(null);
            srvorgCard.setNotHgInstName(null);
        }else {
            if(null ==  srvorgCard.getJcNotHgInstNum() || 0 == srvorgCard.getJcNotHgInstNum()){
                srvorgCard.setNotHgInstName(null);
            }
        }
        if(0 == srvorgCard.getIfJcUse()){
            srvorgCard.setJcUseNum(null);
            srvorgCard.setJcNotHgUseNum(null);
            srvorgCard.setNotHgUseName(null);
        }else {
            if(null ==  srvorgCard.getJcNotHgUseNum() || 0 == srvorgCard.getJcNotHgUseNum()){
                srvorgCard.setNotHgUseName(null);
            }
        }
        initResult();
    }

    /**
     *  <p>方法描述：为空验证-提交</p>
     * @MethodAuthor hsj 2022-08-20 15:31
     */
    private boolean veryNullData() {
        boolean flag = false;
        //机构信息
        //项目负责人
        if (StringUtils.isBlank(srvorgCard.getProFz())) {
            JsfUtil.addErrorMessage("项目负责人不能为空！");
            flag = true;
        }
        //联系电话
        if (StringUtils.isBlank(srvorgCard.getProLinktel())) {
            JsfUtil.addErrorMessage("机构信息联系电话不能为空！");
            flag = true;
        }
        //参与人员不能为空
        if(CollectionUtils.isEmpty(srvorgCard.getSrvorgCardPsnList())){
            JsfUtil.addErrorMessage("参与人员信息不能为空！");
            flag = true;
        }else{
            for (TdZwSrvorgCardPsn srvorgCardPsn : srvorgCard.getSrvorgCardPsnList()){
                List<TdZwSrvorgCardItem> items = new ArrayList<>();
                if(null == srvorgCardPsn.getSrvorgCardItemIds() || srvorgCardPsn.getSrvorgCardItemIds().length == 0){
                    JsfUtil.addErrorMessage(srvorgCardPsn.getPsnName() +"的承担的服务事项不能为空！");
                    flag = true;
                }
            }
        }
        //单位类型不能为空
        if(null == srvorgCard.getUnitType()){
            JsfUtil.addErrorMessage("单位类型不能为空！");
            flag = true;
        }
        if (StringUtils.isBlank(srvorgCard.getLinkMan())) {
            JsfUtil.addErrorMessage("联系人不能为空！");
            flag = true;
        }
        //联系电话
        if (StringUtils.isBlank(srvorgCard.getLinkPhone())) {
            JsfUtil.addErrorMessage("服务的用人单位信息联系电话不能为空！");
            flag = true;
        }

        //服务单位类型
        if(null == srvorgCard.getFkBySutId() || null == srvorgCard.getFkBySutId().getRid()){
            JsfUtil.addErrorMessage("服务单位类型不能为空！");
            flag = true;
        }
        //技术服务地址
        if(!CollectionUtils.isEmpty(srvorgCard.getSrvorgCardZoneList())){
            for (int i = 0;i< srvorgCard.getSrvorgCardZoneList().size() ;i++){
                TdZwSrvorgCardZone zone = srvorgCard.getSrvorgCardZoneList().get(i);
                if(null == zone.getFkByZoneId() || null == zone.getFkByZoneId().getRid()){
                    JsfUtil.addErrorMessage("第"+(i+1)+"行技术服务地址不能为空！");
                    flag = true;
                }
            }
        }
        //技术服务领域
        boolean ifServiceEmpty = true;
        if (!CollectionUtils.isEmpty(this.showServiceList)) {
            for (SimpleCodeTreePO treePO : this.showServiceList) {
                if (!treePO.getIfSelected() || treePO.getIfDisabled()) {
                    continue;
                }
                ifServiceEmpty = false;
                TsSimpleCode simpleCode = treePO.getSimpleCode();
                if (!treePO.getIfHasChild()) {
                    continue;
                }
                List<SimpleCodeTreePO> childList = this.serviceTreeMap.get(simpleCode.getCodeNo());
                if (CollectionUtils.isEmpty(childList)) {
                    continue;
                }
                boolean curFlag = false;
                for (SimpleCodeTreePO child : childList) {
                    curFlag = child.getIfSelected() && !child.getIfDisabled();
                    if (curFlag) {
                        break;
                    }
                }
                if (!curFlag) {
                    Integer ext2 = simpleCode.getExtendS2();
                    JsfUtil.addErrorMessage("请选择技术服务领域"+simpleCode.getCodeName()+(null == ext2 || 2 != ext2 ? "" : "的放射诊疗类型")+"！");
                    flag = true;
                }
            }
        }
        if(ifServiceEmpty){
            JsfUtil.addErrorMessage("技术服务领域不能为空！");
            flag = true;
        }
        //现场采样时间
        if(ifDate){
            if(null == srvorgCard.getInvestStartDate()){
                JsfUtil.addErrorMessage("现场采样开始时间不能为空！");
                flag = true;
            }
            if(null == srvorgCard.getInvestEndDate()){
                JsfUtil.addErrorMessage("现场采样结束时间不能为空！");
                flag = true;
            }
        }

        //技术服务报告编号
        if(StringUtils.isBlank(srvorgCard.getRptNo())){
            JsfUtil.addErrorMessage("技术服务报告编号不能为空！");
            flag = true;
        }
        //技术服务结果至少选择一项
        boolean ifFlag = true;
        //ifFswsFhJc 放射卫生防护检测
        if(1 == srvorgCard.getIfFswsFhJc()){
            ifFlag = false;
            //放射卫生防护检测至少选择一项！
            // 是否开展放射诊疗工作场所放射防护检测 IF_FH_JC
            boolean ifFh = true;
            if(1 == srvorgCard.getIfFhJc()){
                ifFh = false;
                //检测点位数 FH_JC_POINT
                if(null == srvorgCard.getFhJcPoint() ){
                    JsfUtil.addErrorMessage("开展放射诊疗工作场所放射防护检测的检测点位不能为空！");
                    flag = true;
                }
                //超标点位数 NOT_HG_FH_JC_POINT
                if(null == srvorgCard.getNotHgFhJcPoint()){
                    JsfUtil.addErrorMessage("开展放射诊疗工作场所放射防护检测的超标点位不能为空！");
                    flag = true;
                }else {
                    //超标点位放射性危害类型 -当超标点位大于0时必填
                    //srvorgCardFhjcs
                    if(srvorgCard.getNotHgFhJcPoint() > 0 && (null == srvorgCard.getSrvorgCardFhjcs() || srvorgCard.getSrvorgCardFhjcs().length == 0 )){
                        JsfUtil.addErrorMessage("开展放射诊疗工作场所放射防护检测的超标点位放射性危害类型不能为空！");
                        flag = true;
                    }
                }
            }
            //开展放射诊疗设备质量控制检测 IF_INST_ZK_JC
            if(1 == srvorgCard.getIfInstZkJc()){
                ifFh = false;
                if (null == srvorgCard.getFkByJcTypeId() || null == srvorgCard.getFkByJcTypeId().getRid()) {
                    JsfUtil.addErrorMessage("请选择开展放射诊疗设备质量控制检测的检测类型！");
                    flag = true;
                }
                //检测设备数 ZK_JC_INST_NUM
                if(null == srvorgCard.getZkJcInstNum() ){
                    JsfUtil.addErrorMessage("开展放射诊疗设备质量控制检测的检测设备不能为空！");
                    flag = true;
                }
                //检测结果有一项以上指标不合格的设备数 NOT_HG_ZK_JC_INST_NUM
                if(null == srvorgCard.getNotHgZkJcInstNum()){
                    JsfUtil.addErrorMessage("开展放射诊疗设备质量控制检测的不合格的设备不能为空！");
                    flag = true;
                }else {
                    //不合格的设备名称 -当超标点位大于0时必填
                    //NOT_HG_ZK_JC_INST_NAME
                    if(srvorgCard.getNotHgZkJcInstNum() > 0 && StringUtils.isBlank(srvorgCard.getNotHgZkJcInstName())){
                        JsfUtil.addErrorMessage("开展放射诊疗设备质量控制检测的不合格设备名称不能为空！");
                        flag = true;
                    }
                }
            }

            if(ifFh){
                JsfUtil.addErrorMessage("放射卫生防护检测至少选择一项！");
                flag = true;
            }
        }
        //放射诊疗建设项目评价 IF_ITEM_PJ
        if(1 == srvorgCard.getIfItemPj()){
            ifFlag = false;
            //放射诊疗建设项目评价至少选择一项
            boolean ifFhzl = true;
            //预评价 IF_ITEM_YPJ
            if(1 == srvorgCard.getIfItemYpj()){
                ifFhzl = false;
                //剂量估算超标点位数  YPJ_POINT
                if(null == srvorgCard.getYpjPoint() ){
                    JsfUtil.addErrorMessage("预评价的剂量估算超标点位不能为空！");
                    flag = true;
                }else {
                    //超标点位放射性危害类型  srvorgCardYpjs
                    if(srvorgCard.getYpjPoint() > 0 && (null == srvorgCard.getSrvorgCardYpjs() || srvorgCard.getSrvorgCardYpjs().length == 0 )){
                        JsfUtil.addErrorMessage("预评价的超标点位放射性危害类型不能为空！");
                        flag = true;
                    }
                }
            }
            //控制效果评价 IF_ITEM_XGPJ
            if(1 == srvorgCard.getIfItemXgpj()){
                ifFhzl = false;
                //检测点位数（控制效果评价） XGPJ_JC_POINT
                if(null == srvorgCard.getXgpjJcPoint() ){
                    JsfUtil.addErrorMessage("控制效果评价的检测点位不能为空！");
                    flag = true;
                }
                //超标点位数（控制效果评价）NOT_HG_XGPJ_JC_POINT
                if(null == srvorgCard.getNotHgXgpjJcPoint() ){
                    JsfUtil.addErrorMessage("控制效果评价的超标点位不能为空！");
                    flag = true;
                }else {
                    //超标点位放射性危害类型  srvorgCardXgpjs
                    if(srvorgCard.getNotHgXgpjJcPoint() > 0 && (null == srvorgCard.getSrvorgCardXgpjs() || srvorgCard.getSrvorgCardXgpjs().length == 0 )){
                        JsfUtil.addErrorMessage("控制效果评价的超标点位放射性危害类型不能为空！");
                        flag = true;
                    }
                }
            }
            if(ifFhzl){
                JsfUtil.addErrorMessage("放射诊疗建设项目评价至少选择一项！");
                flag = true;
            }
        }
        //个人剂量监测IF_DOSE_MONIT
        //个人剂量监测人数、5~20mSv人数不能为空
        if(1 == srvorgCard.getIfDoseMonit()){
            ifFlag = false;
            //监测人数（个人剂量监测）DOSE_MONIT_NUM
            if(null == srvorgCard.getDoseMonitNum() ){
                JsfUtil.addErrorMessage("个人剂量监测的监测人数不能为空！");
                flag = true;
            }
            //5~20MSV人数（个人剂量监测）GT_5MSV
            if(null == srvorgCard.getGt5msv() ){
                JsfUtil.addErrorMessage("个人剂量监测的5~20mSv人数不能为空！");
                flag = true;
            }
            if(!CollectionUtils.isEmpty(srvorgCard.getSrvorgCardDoseList())){
                for (int i = 0;i< srvorgCard.getSrvorgCardDoseList().size() ;i++){
                    TdZwSrvorgCardDose cardDose = srvorgCard.getSrvorgCardDoseList().get(i);
                    //姓名
                    if(StringUtils.isBlank(cardDose.getPsnName())){
                        JsfUtil.addErrorMessage("个人剂量监测第"+(i+1)+"行姓名不能为空！");
                        flag = true;
                    }
                    //身份证号
                    if(StringUtils.isBlank(cardDose.getIdc())){
                        JsfUtil.addErrorMessage("个人剂量监测第"+(i+1)+"行身份证号不能为空！");
                        flag = true;
                    }
                    //检测日期
                    if(null == cardDose.getJcDate()){
                        JsfUtil.addErrorMessage("个人剂量监测第"+(i+1)+"行检测日期不能为空！");
                        flag = true;
                    }
                    //X、γ外照射个人剂量当量项目值
                    if(null == cardDose.getDoseCal()){
                        JsfUtil.addErrorMessage("个人剂量监测第"+(i+1)+"行X、γ外照射个人剂量当量项目值不能为空！");
                        flag = true;
                    }
                }
            }

        }
        //放射防护器材和含放射性产品检测 IF_FH_INST_JC
        if(1 == srvorgCard.getIfFhInstJc()){
            ifFlag = false;
            //放射防护器材和含放射性产品检测至少选择一项！
            boolean ifFhqc = true;
            // 开展放射防护器材检测 IF_JC_INST
            if(1 == srvorgCard.getIfJcInst()){
                ifFhqc = false;
                //共检测样品数量 JC_INST_NUM
                if(null == srvorgCard.getJcInstNum() ){
                    JsfUtil.addErrorMessage("开展放射防护器材检测的检测样品数量不能为空！");
                    flag = true;
                }
                //超标样品数量 JC_NOT_HG_INST_NUM
                if(null == srvorgCard.getJcNotHgInstNum() ){
                    JsfUtil.addErrorMessage("开展放射防护器材检测的超标样品数量不能为空！");
                    flag = true;
                }else {
                    //超标样品名称  NOT_HG_INST_NAME
                    if(srvorgCard.getJcNotHgInstNum() > 0 && StringUtils.isBlank(srvorgCard.getNotHgInstName())){
                        JsfUtil.addErrorMessage("开展放射防护器材检测的超标样品名称不能为空！");
                        flag = true;
                    }
                }
            }
            //开展含放射性产品检测 IF_JC_USE
            if(1 == srvorgCard.getIfJcUse()){
                ifFhqc = false;
                //共检测样品数量 JC_USE_NUM
                if(null == srvorgCard.getJcUseNum() ){
                    JsfUtil.addErrorMessage("开展含放射性产品检测的检测样品数量不能为空！");
                    flag = true;
                }
                //超标样品数量 JC_NOT_HG_USE_NUM
                if(null == srvorgCard.getJcNotHgUseNum() ){
                    JsfUtil.addErrorMessage("开展含放射性产品检测的超标样品数量不能为空！");
                    flag = true;
                }else {
                    //超标样品名称  NOT_HG_USE_NAME
                    if(srvorgCard.getJcNotHgUseNum() > 0 && StringUtils.isBlank(srvorgCard.getNotHgUseName())){
                        JsfUtil.addErrorMessage("开展含放射性产品检测的超标样品名称不能为空！");
                        flag = true;
                    }
                }
            }
            if(ifFhqc){
                JsfUtil.addErrorMessage("放射防护器材和含放射性产品检测至少选择一项！");
                flag = true;
            }
        }
        if(ifFlag){
            JsfUtil.addErrorMessage("技术服务结果至少选择一项！");
            flag = true;
        }
        //报告信息
        //单位负责人
        if(StringUtils.isBlank(srvorgCard.getOrgFzMan())){
            JsfUtil.addErrorMessage("单位负责人不能为空！");
            flag = true;
        }
        //填表人
        if(StringUtils.isBlank(srvorgCard.getFillFormPsn())){
            JsfUtil.addErrorMessage("填表人不能为空！");
            flag = true;
        }
        //填表人联系电话
        if(StringUtils.isBlank(srvorgCard.getFillLink())){
            JsfUtil.addErrorMessage("填表人联系电话不能为空！");
            flag = true;
        }
        //填表日期
        if(null == srvorgCard.getFillDate()){
            JsfUtil.addErrorMessage("填表日期不能为空！");
            flag = true;
        }
        return flag;
    }

    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();
        sql.append(" FROM  TD_ZW_SRVORG_CARD  T  ");
        sql.append(" LEFT JOIN TS_ZONE T1  ON T.ZONE_ID = T1.RID   ");
        sql.append(" WHERE T.DEL_MARK = 0  ");
        sql.append(" AND T.FILL_UNIT_ID =  ").append(Global.getUser().getTsUnit().getRid());
        if (StringUtils.isNotBlank(searchZoneGb)) {
            sql.append(" AND T1.ZONE_GB LIKE :searchZoneGb escape '\\\'");
            paramMap.put("searchZoneGb", StringUtils.convertBFH(ZoneUtil.zoneSelect(searchZoneGb).trim()) + "%");
        }
        if (StringUtils.isNotBlank(searchUnitName)) {
            sql.append(" and T.UNIT_NAME like :searchUnitName escape '\\\'");
            this.paramMap.put("searchUnitName", "%"+StringUtils.convertBFH(searchUnitName.trim())+"%");
        }
        if (null != searchRptSDate) {
            sql.append(" AND T.RPT_DATE >= TO_DATE('")
                    .append(DateUtils.formatDate(searchRptSDate, "yyyy-MM-dd"))
                    .append("','yyyy-MM-dd')");
        }
        if (null != searchRptEDate) {
            sql.append(" AND T.RPT_DATE  <= TO_DATE('")
                    .append(DateUtils.formatDate(searchRptEDate, "yyyy-MM-dd"))
                    .append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
        }
        if(null != states && states.length == 1){
            sql.append(" AND T.STATE = ").append(states[0]);
        }

        String h2 = "SELECT COUNT(*) " + sql.toString() ;
        String h1 = "SELECT T.RID , CASE WHEN T1.ZONE_TYPE >2 THEN SUBSTR(T1.FULL_NAME, INSTR(T1.FULL_NAME,'_')+1) ELSE T1.FULL_NAME END ZONE_NAME  ,T.UNIT_NAME,T.RPT_DATE,T.STATE " + sql.toString();
        h1 += " ORDER BY T.RPT_DATE DESC,T1.ZONE_GB ,T.UNIT_NAME ";
        return new String[]{h1,h2};
    }
    /**
     *  <p>方法描述：标删</p>
     * @MethodAuthor hsj 2022-08-19 14:36
     */
    public void delAction(){
        try {
            this.srvorgCardService.deleteSrvorgCard(this.rid);
            this.searchAction();
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }
    /**
     *  <p>方法描述：新增服务地址</p>
     * @MethodAuthor hsj 2022-08-23 13:48
     */
    public void addSrvorgCardZone(){
        TdZwSrvorgCardZone zone = new TdZwSrvorgCardZone();
        zone.setFkByZoneId(new TsZone());
        srvorgCard.getSrvorgCardZoneList().add(zone);
    }
    /**
     *  <p>方法描述：服务地址删除</p>
     * @MethodAuthor hsj 2022-08-23 13:54
     */
    public void delSrvorgCardZoneAction(TdZwSrvorgCardZone srvorgCardZone){
        if (null != srvorgCardZone) {
            srvorgCard.getSrvorgCardZoneList().remove(srvorgCardZone);
        }
    }
    /**
     *  <p>方法描述：服务地址选择</p>
     * @MethodAuthor hsj 2022-08-23 15:36
     */
    public void searchSrvorgCardZone(TdZwSrvorgCardZone srvorgCardZone){
        //选择时必须选择至县区，选择后刷新详细地址
        if(null == srvorgCardZone.getZoneGb()){
            JsfUtil.addErrorMessage("请选择服务地址！");
            return;
        }
        TsZone tsZone = zoneMap.get(srvorgCardZone.getZoneGb());
        srvorgCardZone.setFkByZoneId(tsZone);
        srvorgCardZone.setFullName(tsZone.getFullName());
    }

    public void changeSelectServicesAction() {
        if (CollectionUtils.isEmpty(this.showServiceList)) {
            return;
        }
        for (SimpleCodeTreePO treePO : this.showServiceList) {
            if (null == treePO.getIfHasChild() || !treePO.getIfHasChild()) {
                continue;
            }
            TsSimpleCode simpleCode = treePO.getSimpleCode();
            String codeNo = simpleCode.getCodeNo();
            List<SimpleCodeTreePO> childList = this.serviceTreeMap.get(codeNo);
            if (CollectionUtils.isEmpty(childList)) {
                continue;
            }
            for (SimpleCodeTreePO child : childList) {
                child.setIfDisabled(!treePO.getIfSelected());
            }
        }
        this.changeServicesAction();
    }
    public void changeResultStateAction(){
        if(this.ifFswsFhJc){
            this.srvorgCard.setIfFswsFhJc(1);
        }else {
            srvorgCard.setIfFswsFhJc(0);
        }
        if(this.ifFhJc){
            this.srvorgCard.setIfFhJc(1);
        }else {
            srvorgCard.setIfFhJc(0);
        }
        if(this.ifInstZkJc){
            this.srvorgCard.setIfInstZkJc(1);
        }else {
            srvorgCard.setIfInstZkJc(0);
        }
        if(this.ifItemPj){
            this.srvorgCard.setIfItemPj(1);
        }else {
            srvorgCard.setIfItemPj(0);
        }
        if(this.ifItemYpj){
            this.srvorgCard.setIfItemYpj(1);
        }else {
            srvorgCard.setIfItemYpj(0);
        }
        if(this.ifItemXgpj){
            this.srvorgCard.setIfItemXgpj(1);
        }else {
            srvorgCard.setIfItemXgpj(0);
        }
        if(this.ifDoseMonit){
            this.srvorgCard.setIfDoseMonit(1);
        }else {
            srvorgCard.setIfDoseMonit(0);
        }
        if(this.ifFhInstJc){
            this.srvorgCard.setIfFhInstJc(1);
        }else {
            srvorgCard.setIfFhInstJc(0);
        }
        if(this.ifJcInst){
            this.srvorgCard.setIfJcInst(1);
        }else {
            srvorgCard.setIfJcInst(0);
        }
        if(this.ifJcUse){
            this.srvorgCard.setIfJcUse(1);
        }else {
            srvorgCard.setIfJcUse(0);
        }
    }
    /**
     *  <p>方法描述：个人剂量监测添加</p>
     * @MethodAuthor hsj 2022-08-24 15:51
     */
    public void addSrvorgCardDose(){
        TdZwSrvorgCardDose dose = new TdZwSrvorgCardDose();
        srvorgCard.getSrvorgCardDoseList().add(dose);
        srvorgCard.setGt20msvBhkNum( srvorgCard.getSrvorgCardDoseList().size());
    }
    /**
     *  <p>方法描述：个人剂量监测删除</p>
     * @MethodAuthor hsj 2022-08-24 15:52
     */
    public void delSrvorgCardDoseAction(TdZwSrvorgCardDose dose){
        if (null != dose) {
            srvorgCard.getSrvorgCardDoseList().remove(dose);
            srvorgCard.setGt20msvBhkNum( srvorgCard.getSrvorgCardDoseList().size());
        }
    }
    /**
     *  <p>方法描述：提交前验证</p>
     * @MethodAuthor hsj 2022-08-19 15:28
     */
    public void beforeSubmit(){
        try{
            //为空验证
            boolean flag = false;
            if (veryNullData()) {
                flag = true;
            }
            if (veryData()) {
                flag = true;
            }
            if(flag){
              return;
            }
            // 若已制作，不可提交
            if (StringUtils.isBlank(srvorgCard.getAnnexPath())) {
                JsfUtil.addErrorMessage("请先上传《放射卫生技术服务信息报送卡》文书！");
                flag = true;
            }
            //放射卫生技术服务报告首页、签发页
            if(StringUtils.isBlank(this.srvorgCard.getSignAddress())){
                JsfUtil.addErrorMessage("请先上传《放射卫生技术服务报告首页、签发页》文书！");
                flag = true;
            }
            if(flag){
                return;
            }
            RequestContext context = RequestContext.getCurrentInstance();
            context.execute("PF('ConfirmDialog').show()");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }

    }
    /**
     *  <p>方法描述：确定提交</p>
     * @MethodAuthor hsj 2022-08-19 15:29
     */
    public void submitAction(){
        try {
            srvorgCard.setState(1);
            //技术服务结果处理
            srvorgCardResult();
            ZwSrvorgCardUtils.clearJcSubCommEntity(srvorgCard);
            this.srvorgCardService.saveSrvorgCard(srvorgCard);
            ZwSrvorgCardUtils.initEntity(srvorgCard);
            JsfUtil.addSuccessMessage("提交成功！");
            this.rid = srvorgCard.getRid();
            this.searchAction();
            this.viewInitAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }

    }
    /**
     *  <p>方法描述：撤销</p>
     * @MethodAuthor hsj 2022-08-25 14:24
     */
    public void revokeAction(){
        try {
            String sql = "update TD_ZW_SRVORG_CARD set state=0,MODIFY_DATE=sysdate,MODIFY_MANID="+Global.getUser().getRid()+" where rid="+srvorgCard.getRid();
            this.commService.executeSql(sql, null);
            SystemMessageEnum.CANCEL_SUCCESS.showMessage();
            this.searchAction();
            //跳转编辑页
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("disabledInput('true','srvorgCardDiv')");
            this.modInit();
            this.forwardEditPage();
        } catch(Exception e) {
            e.printStackTrace();
            SystemMessageEnum.CANCEL_FAIL.showMessage();
        }
    }
    /**
     *  <p>方法描述：文书预览前验证</p>
     * @MethodAuthor hsj 2022-08-19 15:32
     */
    public void buildWritReport() {
        boolean flag = false;
        if (veryNullData()) {
            flag = true;
        }
        if (veryData()) {
            flag = true;
        }
        if(flag){
            return;
        }
        //保存
        saveOrUpdateSrvorgCard();
        reportFilePath = null;
        String returnJson = null;
        try {
            WordReportJson reportJson = new WordReportJson();
            reportJson.setRid(this.srvorgCard.getRid());
            reportJson.setRptCode("HETH_RPT_1001");
            reportJson.setIfPdf(1);
            String requestJson = JSON.toJSONString(reportJson);
            String encodeJson = null;
            String debug = PropertyUtils.getValue("encrypt.debug");
            String encryptKey = PropertyUtils.getValue("encrypt.key");
            if ("true".equals(debug)) {
                encodeJson = requestJson;
            }else {
                //加密
                encodeJson = AesEncryptUtils.aesEncrypt(requestJson, encryptKey);
            }
            //调用接口
            String delUrl = PropertyUtils.getValue("delUrl");
            String reposeJson = HttpRequestUtil.httpRequestByRaw(delUrl+"/word/generate",encodeJson);
            //解密
            if ("true".equals(debug)) {
                returnJson = reposeJson;
            }else {
                returnJson = AesEncryptUtils.aesDecrypt(reposeJson, encryptKey);
            }
        }catch (Exception e){
            JsfUtil.addErrorMessage("生成文书失败！");
            e.printStackTrace();
            return;
        }
        if(StringUtils.isNotBlank(returnJson)){
            WordReportDTO returnDTO = JSON.parseObject(returnJson, WordReportDTO.class);
            if(null == returnDTO || !ReturnType.SUCCESS_PROCESS.getTypeNo().equals(returnDTO.getType())){
                logger.error(returnDTO.getMess());
                JsfUtil.addErrorMessage("生成文书失败！");
                return;
            }else{
                reportFilePath = returnDTO.getFilePath();
                if(StringUtils.isNotBlank(reportFilePath)){
                    RequestContext.getCurrentInstance().execute("openPDFClick()");
                }else {
                    JsfUtil.addErrorMessage("生成文书失败！");
                    return;
                }
            }
        }

    }
    /**
     *  <p>方法描述：文书上传前验证
     *  验证必填+逻辑-保存</p>
     * @MethodAuthor hsj 2022-08-25 9:28
     */
    public void beforeUpload(){
        boolean flag = false;
        if (veryNullData()) {
            flag = true;
        }
        if (veryData()) {
            flag = true;
        }
        if(flag){
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('FileDialog').show()");
        RequestContext.getCurrentInstance().update("fileDialog");
    }
    /**
     *  <p>方法描述：上传附件</p>
     * @MethodAuthor hsj 2022-08-25 9:33
     */
    public void fileUpload(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            try {
                String errorMsg = FileUtils.veryFile(file.getInputstream(),file.getContentType(), file.getFileName(), "2");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    return;
                }
                String fileName = file.getFileName();// 文件名称
                String uuid = java.util.UUID.randomUUID().toString()
                        .replaceAll("-", "");
                String path = JsfUtil.getAbsolutePath();
                String relativePath = new StringBuffer("heth/comm/reportcard/")
                        .append(uuid)
                        .append(fileName.substring(fileName
                                .lastIndexOf("."))).toString();
                // 文件路径
                String filePath = new StringBuilder(path).append(
                        relativePath).toString();
                if(this.fileType != null && this.fileType == 2){
                    srvorgCard.setSignAddress(relativePath);
                    FileUtils.copyFile(filePath, file.getInputstream());
                    RequestContext.getCurrentInstance().update(
                            "tabView:editForm:writeSortPanel");
                }else {
                    srvorgCard.setAnnexPath(relativePath);
                    FileUtils.copyFile(filePath, file.getInputstream());
                    saveOrUpdateSrvorgCard();
                    RequestContext.getCurrentInstance().update(
                            "tabView:editForm:tdZwSrvorgCardCon");
                    RequestContext.getCurrentInstance().execute("disabledInput('true','srvorgCardDiv')");
                }
                RequestContext.getCurrentInstance().execute(
                        "PF('FileDialog').hide()");
                JsfUtil.addSuccessMessage("上传成功！");
            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("上传失败！");
                throw new RuntimeException(e);
            }
        }
    }
    /**
     *  <p>方法描述：文书删除</p>
     * @MethodAuthor hsj 2022-08-19 15:33
     */
    public void delMadedwrit() {
        try {
            if(this.fileType != null && this.fileType == 2){
                srvorgCard.setSignAddress(null);
            }else {
                this.srvorgCardService.deleteAnnexPathByRid(srvorgCard.getRid());
                srvorgCard.setAnnexPath(null);
                RequestContext.getCurrentInstance().execute("disabledInput('false','srvorgCardDiv')");
            }
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    public String getRedStar() {
        return redStar;
    }

    public void setRedStar(String redStar) {
        this.redStar = redStar;
    }

    public String getReportFilePath() {
        return reportFilePath;
    }

    public void setReportFilePath(String reportFilePath) {
        this.reportFilePath = reportFilePath;
    }
}
