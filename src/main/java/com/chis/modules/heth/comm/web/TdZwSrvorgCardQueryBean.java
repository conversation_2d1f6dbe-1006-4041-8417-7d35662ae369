package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSONObject;
import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 *  <p>类描述：放射卫生技术服务信息报送卡查询</p>
 * @ClassAuthor hsj 2023-11-27 15:28
 */
@ManagedBean(name = "tdZwSrvorgCardQueryBean")
@ViewScoped
public class TdZwSrvorgCardQueryBean extends AbstractSrvorgCardBean {
    private static final long serialVersionUID = 1L;


    /**
     * 查询条件：报告单位名称
     */
    private String searchOrgName;
    /**
     * 查询条件：技术服务领域名称
     */
    private String selectServiceNames;
    /**
     * 查询条件：技术服务领域rids
     */
    private String selectServiceRids;

    /**导入权限*/
    private Boolean importJurisdiction;

    /**来源*/
    protected List<Integer> searchSourceList;

    /**
     * 导入模板下载
     */
    private StreamedContent dosModelFile;
    /**错误文件路径*/
    private String importErrFilePath;
    /**删除接口路径*/
    private String delUrl;

    /**错误数据下载*/
    private StreamedContent errorImportFile;

    public TdZwSrvorgCardQueryBean() {
        this.init();
        this.searchAction();
    }

    private void init() {
        //报告单位地区
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if(null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.searchZoneGb = tsZone.getZoneGb();
        this.searchZoneName = tsZone.getZoneName();
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb(), true, "", "");
        this.states = new String[]{"1"};
        this.ifSupportCancel = Boolean.FALSE;
        this.searchSourceList = new ArrayList<>();
        this.importJurisdiction = Boolean.FALSE;
        Set<String> btnSet= Global.getBtnSet();
        if (!CollectionUtils.isEmpty(btnSet)) {
            for (String code : btnSet) {
                if ("heth_comm_gs_fswsjsfwxxbscx_imp".equals(code)) {
                    importJurisdiction=true;
                }
            }
        }
        //删除接口路径
        delUrl = PropertyUtils.getValueWithoutException("delUrl");
    }

    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();
        sql.append(" FROM  TD_ZW_SRVORG_CARD  T  ");
        sql.append("LEFT JOIN TS_UNIT tu  ON T.FILL_UNIT_ID = tu.RID ");
        sql.append("LEFT JOIN TS_ZONE Z ON tu.ZONE_ID = Z.RID  ");
        sql.append("LEFT JOIN TS_ZONE Z1 ON T.ZONE_ID = Z1.RID ");
        sql.append(" WHERE T.DEL_MARK = 0  ");

        // 报告单位地区
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            sql.append(" AND Z.ZONE_GB LIKE :zoneCode escape '\\\' ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneGb)) + "%");
        }
        // 报告单位名称
        if (StringUtils.isNotBlank(this.searchOrgName)) {
            sql.append(" AND T.ORG_NAME LIKE :searchOrgName escape '\\\' ");
            this.paramMap.put("searchOrgName", "%" + StringUtils.convertBFH(this.searchOrgName.trim()) + "%");
        }
        if (StringUtils.isNotBlank(searchUnitName)) {
            sql.append(" and T.UNIT_NAME like :searchUnitName escape '\\\'");
            this.paramMap.put("searchUnitName", "%"+StringUtils.convertBFH(searchUnitName.trim())+"%");
        }
        //技术服务领域
        if (ObjectUtil.isNotEmpty(this.selectServiceRids)) {
            sql.append(" AND EXISTS (SELECT 1 FROM TD_ZW_SRVORG_CARD_SERVICE S WHERE S.MAIN_ID = T.RID AND S.SERVICE_AREA_ID IN (:selectServiceRids))");
            this.paramMap.put("selectServiceRids",StringUtils.string2list(this.selectServiceRids,",") );
        }
        if (null != searchRptSDate) {
            sql.append(" AND T.RPT_DATE >= TO_DATE('")
                    .append(DateUtils.formatDate(searchRptSDate, "yyyy-MM-dd"))
                    .append("','yyyy-MM-dd')");
        }
        if (null != searchRptEDate) {
            sql.append(" AND T.RPT_DATE  <= TO_DATE('")
                    .append(DateUtils.formatDate(searchRptEDate, "yyyy-MM-dd"))
                    .append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
        }
        if(null != states && states.length == 1){
            sql.append(" AND T.STATE = ").append(states[0]);
        }
        // 来源
        if (ObjectUtil.isNotEmpty(this.searchSourceList)) {
            sql.append(" AND nvl(T.SOURCE,0) IN (:searchSourceList)");
            this.paramMap.put("searchSourceList", this.searchSourceList);
        }
        String h2 = "SELECT COUNT(*) " + sql.toString() ;
        String h1 = "SELECT T.RID, CASE WHEN Z.ZONE_TYPE > 2 THEN SUBSTR(Z.FULL_NAME, INSTR(Z.FULL_NAME, '_') + 1) ELSE Z.FULL_NAME END orgZoneName,T.ORG_NAME, CASE WHEN Z1.ZONE_TYPE > 2 THEN SUBSTR(Z1.FULL_NAME, INSTR(Z1.FULL_NAME, '_') + 1) ELSE Z1.FULL_NAME END crptZoneName,T.UNIT_NAME,T.RPT_DATE,T.STATE,nvl(T.SOURCE,0) " + sql.toString();
        h1 += " ORDER BY Z.ZONE_GB,T.ORG_NAME,T.RPT_DATE DESC,Z1.ZONE_GB ,T.UNIT_NAME ,T.RID DESC";
        return new String[]{h1,h2};
    }


    /**
    * <p>Description：导出模板 </p>
    * <p>Author： yzz 2023-11-29 </p>
    */
    public StreamedContent getDosModelFile() {
        InputStream stream = null;
        try {
            String moudleFilePath = "/resources/template/excel/放射卫生技术服务信息报送卡导入模板.xls";
            stream = FacesContext.getCurrentInstance().getExternalContext().getResourceAsStream(moudleFilePath);
            this.dosModelFile = new DefaultStreamedContent(stream, "application/xls", URLEncoder.encode("放射卫生技术服务信息报送卡导入模板.xls", "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("文件下载失败!");
        } finally{
            if(stream != null){
                try {
                    stream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return dosModelFile;
    }

    /**
     * <p>Description：打开 </p>
     * <p>Author： yzz 2023-11-14 </p>
     */
    public void openImportSrvorgCardDialog() {
        RequestContext.getCurrentInstance().execute("PF('ImportSrvorgCardDialog').show()");
    }

    public StreamedContent getErrorImportFile() {
        if (StringUtils.isBlank(this.importErrFilePath)) {
            return null;
        }
        InputStream stream;
        try {
            stream = Files.newInputStream(Paths.get(JsfUtil.getAbsolutePath() + this.importErrFilePath));
            String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            String fileName = "导入错误数据.xlsx";
            if (this.importErrFilePath.endsWith(".xls")) {
                contentType = "application/vnd.ms-excel";
                fileName = "导入错误数据.xls";
            }
            return new DefaultStreamedContent(stream, contentType, URLEncoder.encode(fileName, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("下载失败!");
        }
        return null;
    }

    /**
     * <p>Description：导入模板 </p>
     * <p>Author： yzz 2023-11-14 </p>
     */
    public void importSrvorgCardAction(FileUploadEvent event) {
        // 删除历史错误文件
        if (ObjectUtil.isNotEmpty(this.importErrFilePath)) {
            File errorFile = new File(JsfUtil.getAbsolutePath() + this.importErrFilePath);
            if (errorFile.exists()) {
                boolean ignore = errorFile.delete();
            }
        }
        this.importErrFilePath = null;
        String updateFormId = "tabView:mainForm:importSrvorgCardDialog";
        if (event == null || event.getFile() == null) {
            RequestContext.getCurrentInstance().update(updateFormId);
            RequestContext.getCurrentInstance().execute("PF('ImportSrvorgCardDialog').hide()");
            JsfUtil.addErrorMessage("文件上传失败！");
            return;
        }
        UploadedFile file = event.getFile();
        try {
            //格式验证
            String fileName = file.getFileName();// 文件名称
            String contentType = file.getContentType().toLowerCase();
            String errorMsg = FileUtils.veryFile(file.getInputstream(),contentType, fileName, "5");
            if (StringUtils.isNotBlank(errorMsg)) {
                JsfUtil.addErrorMessage(errorMsg);
                RequestContext.getCurrentInstance().update(updateFormId);
                RequestContext.getCurrentInstance().execute("PF('ImportSrvorgCardDialog').show()");
                return;
            }
            TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
            if(null == tsZone) {
                tsZone = Global.getUser().getTsUnit().getTsZone();
            }
            String zoneCode=tsZone.getZoneGb().substring(0,2);
            String urlStr = delUrl + "/easyExcel/srvorgCardUpload?zoneCode="+zoneCode;

            String responseStr =HttpRequestUtil.httpRequestByRaw(urlStr, file.getInputstream());
            if (StringUtils.isNotBlank(responseStr)) {
                JSONObject responseJson = JSONObject.parseObject(responseStr);
                Boolean flag = ObjectUtil.convert(Boolean.class, responseJson.get("flag"), false);
                String msg = StringUtils.objectToString(responseJson.get("msg"));
                String errorFileName = StringUtils.objectToString(responseJson.get("errorFile"));
                if (flag) {
                    JsfUtil.addSuccessMessage(msg);
                } else {
                    if (ObjectUtil.isEmpty(msg)) {
                        msg = "导入文件异常！";
                    }
                    JsfUtil.addErrorMessage(msg);
                }
                if (ObjectUtil.isNotEmpty(errorFileName)) {
                    this.importErrFilePath = File.separator + "comm" + File.separator + "temp" + File.separator + errorFileName;
                } else {
                    this.importErrFilePath = "";
                }
            } else {
                JsfUtil.addErrorMessage("导入文件异常！");
            }
            searchAction();
            RequestContext.getCurrentInstance().update("tabView:mainForm");
            RequestContext.getCurrentInstance().execute("PF('ImportSrvorgCardDialog').hide()");
        } catch (Exception e) {
            e.printStackTrace();
            RequestContext.getCurrentInstance().update(updateFormId);
            RequestContext.getCurrentInstance().execute("PF('ImportSrvorgCardDialog').hide()");
            JsfUtil.addErrorMessage("导入文件异常！");
        }
    }

    public void setErrorImportFile(StreamedContent errorImportFile) {
        this.errorImportFile = errorImportFile;
    }

    public String getSearchOrgName() {
        return searchOrgName;
    }

    public void setSearchOrgName(String searchOrgName) {
        this.searchOrgName = searchOrgName;
    }

    public String getSelectServiceNames() {
        return selectServiceNames;
    }

    public void setSelectServiceNames(String selectServiceNames) {
        this.selectServiceNames = selectServiceNames;
    }

    public String getSelectServiceRids() {
        return selectServiceRids;
    }

    public void setSelectServiceRids(String selectServiceRids) {
        this.selectServiceRids = selectServiceRids;
    }

    public Boolean getImportJurisdiction() {
        return importJurisdiction;
    }

    public void setImportJurisdiction(Boolean importJurisdiction) {
        this.importJurisdiction = importJurisdiction;
    }

    public List<Integer> getSearchSourceList() {
        return searchSourceList;
    }

    public void setSearchSourceList(List<Integer> searchSourceList) {
        this.searchSourceList = searchSourceList;
    }



    public void setDosModelFile(StreamedContent dosModelFile) {
        this.dosModelFile = dosModelFile;
    }

    public String getImportErrFilePath() {
        return importErrFilePath;
    }

    public void setImportErrFilePath(String importErrFilePath) {
        this.importErrFilePath = importErrFilePath;
    }

    public String getDelUrl() {
        return delUrl;
    }

    public void setDelUrl(String delUrl) {
        this.delUrl = delUrl;
    }
}