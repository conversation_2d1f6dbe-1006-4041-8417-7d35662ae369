package com.chis.modules.heth.comm.web;

import com.chis.common.utils.CollectionUtil;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesTableBean;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用人单位综合展示-职业健康检查数据-弹出框数据
 *
 * <AUTHOR>
 * @version 1.1
 */
@ManagedBean(name = "crptIntegratedShowBhkDataBean")
@ViewScoped
public class GsCrptIntegratedShowBhkDataBean extends FacesTableBean implements IProcessData {
    private static final long serialVersionUID = 1L;
    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    /**
     * 年份
     */
    private String year;
    /**
     * 企业id
     */
    private Integer crptId;
    /**
     * table id
     */
    private String tableId;

    public GsCrptIntegratedShowBhkDataBean() {
        init();
    }

    public GsCrptIntegratedShowBhkDataBean(String year, Integer crptId, String tableId) {
        this.year = year;
        this.crptId = crptId;
        this.tableId = tableId;
        init();
    }

    @Override
    public String getTableId() {
        return this.tableId;
    }

    public void setTableId(String tableId) {
        this.tableId = tableId;
    }

    public void init() {
        this.ifSQL = true;
    }

    @Override
    public String[] buildHqls() {
        this.paramMap.put("crptId", this.crptId);
        this.paramMap.put("year", this.year);
        String sqlBase = "FROM TD_TJ_BHK B  " +
                "WHERE B.ENTRUST_CRPT_ID = :crptId  " +
                "  AND EXTRACT(YEAR FROM B.BHK_DATE) = :year  " +
                "ORDER BY B.BHK_DATE DESC, B.RID DESC";
        String sql1 = "SELECT B.BHK_CODE,  " +
                "       B.PERSON_NAME,  " +
                "       B.IDC,  " +
                "       B.SEX,  " +
                "       B.WORK_NAME,  " +
                "       '',  " +
                "       B.BHK_DATE,  " +
                "       B.RID,  " +
                "       B.OTHER_BADRSN  " +
                sqlBase;
        String sql2 = "SELECT COUNT(1)  " + sqlBase;
        return new String[]{sql1, sql2};
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }

        List<Object[]> result = CollectionUtil.castList(Object[].class, list);
        List<String> ridList = new ArrayList<>();
        for (Object[] objects : result) {
            ridList.add(StringUtils.objectToString(objects[7]));
        }
        Map<String, List<String>> badrsnMap = pakBadrsnMap(ridList);
        for (Object[] obj : result) {
            String rid = StringUtils.objectToString(obj[7]);
            String otherBadrsn = StringUtils.objectToString(obj[8]);
            obj[2] = StringUtils.encryptIdc(StringUtils.objectToString(obj[2]));
            obj[5] = StringUtils.list2string(badrsnMap.get(rid), "，");
            if (StringUtils.isNotBlank(otherBadrsn)) {
                obj[5] = obj[5] + "（" + otherBadrsn + "）";
            }
        }
    }

    /**
     * 封装体检危害因素
     *
     * @param ridList 体检记录RID
     * @return 体检对应体检危害因素
     */
    public Map<String, List<String>> pakBadrsnMap(List<String> ridList) {
        Map<String, List<String>> badrsnMap = new HashMap<>();
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "SELECT BR.BHK_ID, SC.CODE_NAME " +
                "FROM TD_TJ_BADRSNS BR " +
                "         LEFT JOIN TS_SIMPLE_CODE SC ON BR.BADRSN_ID = SC.RID " +
                "WHERE BR.BHK_ID IS NOT NULL AND SC.CODE_NAME IS NOT NULL AND BR.BHK_ID IN (:bhkIdList) " +
                "GROUP BY BR.BHK_ID, SC.CODE_NAME, SC.CODE_LEVEL_NO, SC.NUM " +
                "ORDER BY SC.CODE_LEVEL_NO, SC.NUM";
        paramMap.put("bhkIdList", ridList);
        List<Object[]> dataList = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql, paramMap));
        for (Object[] data : dataList) {
            String key = StringUtils.objectToString(data[0]);
            if (!badrsnMap.containsKey(key)) {
                badrsnMap.put(key, new ArrayList<String>());
            }
            badrsnMap.get(key).add(StringUtils.objectToString(data[1]));
        }
        return badrsnMap;
    }

    public CommServiceImpl getCommService() {
        return commService;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public Integer getCrptId() {
        return crptId;
    }

    public void setCrptId(Integer crptId) {
        this.crptId = crptId;
    }
}
