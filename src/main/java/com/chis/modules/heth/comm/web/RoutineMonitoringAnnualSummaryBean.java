package com.chis.modules.heth.comm.web;

import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.model.DefaultStreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.*;

@ManagedBean(name = "routineMonitoringAnnualSummaryBean")
@ViewScoped
public class RoutineMonitoringAnnualSummaryBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = -1182028505602137259L;
    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
     * 查询条件：地区
     */
    private List<TsZone> zoneList;
    private String searchZoneGb;
    private String searchZoneName;
    private Map<String, TsZone> zoneMap;
    /**
     * 查询条件：报告出具周期 年 集合
     */
    private List<Integer> searchYearList;
    /**
     * 查询条件：报告出具周期 年
     */
    private Integer searchYear;
    /**
     * 查询条件：统计类型<pre>1: 按合计</pre><pre>2: 按辖区</pre>
     */
    private Integer summaryType;
    /**
     * 标题
     */
    private String tableTitle;
    /**
     * 5547 统计危害因素
     */
    private List<Integer> badrsnList;
    private Integer lastPageSize;

    public RoutineMonitoringAnnualSummaryBean() {
        init();
    }

    public void init() {
        this.ifSQL = true;
        initZone();
        initSearchYear();
        this.summaryType = 1;
        this.tableTitle = getTitlePre();
        this.lastPageSize = 14;
        resetPageSize(this.lastPageSize);
        preSearchAction();
        searchAction();
    }

    /**
     * 初始化地区
     */
    private void initZone() {
        //查询条件：管辖范围内地区
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb(), true, "", "4");
        this.searchZoneGb = this.zoneList.get(0).getZoneGb();
        this.searchZoneName = this.zoneList.get(0).getZoneName();
        this.zoneMap = new HashMap<>();
        for (TsZone zone : this.zoneList) {
            this.zoneMap.put(zone.getZoneGb(), zone);
        }
    }

    /**
     * 初始化报告出具周期的年
     */
    private void initSearchYear() {
        // 报告出具周期 默认当年
        this.searchYear = DateUtils.getYearInt();
        if (ObjectUtil.isEmpty(this.searchYearList)) {
            this.searchYearList = new ArrayList<>();
            for (int i = 0; i < 5; i++) {
                this.searchYearList.add(this.searchYear - i);
            }
        }
        changeSearchYear();
    }

    /**
     * 重置每页显示的数据条数
     */
    private void resetPageSize(int size) {
        this.setPageSize(size);
        this.setPerPageSize(size + "," + size * 5 + "," + size * 10);
    }

    /**
     * 初始化报告出具周期的年
     */
    public void changeSearchYear() {
        int minYear = this.searchYear - 4;
        Integer lastYear = this.searchYearList.get(this.searchYearList.size() - 1);
        if (lastYear == minYear) {
            return;
        }
        while (lastYear < minYear) {
            this.searchYearList.remove(lastYear++);
        }
        while (lastYear > minYear) {
            this.searchYearList.add(--lastYear);
        }
    }

    public String getTitlePre() {
        return "各地区职业健康指标常规监测汇总表";
    }

    public String getTitle() {
        if (this.searchYear == null) {
            return getTitlePre();
        }
        String startDate = (this.searchYear - 1) + "-11-16";
        String endDate = this.searchYear + "-11-15";
        return getTitlePre() + "（" + startDate + "~" + endDate + "）";
    }

    @Override
    public void searchAction() {
        super.searchAction();
    }

    public void preSearchAction() {
        if (this.validateQuery()) {
            return;
        }
        this.tableTitle = getTitle();
        Map<String, Object> paramMap = new HashMap<>();
        String querySql = this.generateQueryBadRsnIdSql(paramMap);
        this.badrsnList = this.commService.findIntegerListBySql(querySql, paramMap);
        if (CollectionUtils.isEmpty(this.badrsnList)) {
            RequestContext.getCurrentInstance().execute("exeSearch()");
            return;
        }
        int curPageSize = this.badrsnList.size();
        if (curPageSize != this.lastPageSize) {
            this.resetPageSize(curPageSize);
            DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                    .findComponent("mainForm:dataTable");
            dataTable.reset();
            dataTable.setRows(curPageSize);
            dataTable.setRowsPerPageTemplate(this.getPerPageSize());
            this.lastPageSize = curPageSize;
        }
        RequestContext.getCurrentInstance().execute("exeSearch()");
    }

    /**
     * 查询、导出校验
     *
     * @return Boolean 校验是否失败
     */
    private boolean validateQuery() {
        boolean flag = false;
        if (this.searchYear == null) {
            JsfUtil.addErrorMessage("请选择报告出具周期！");
            flag = true;
        }
        return flag;
    }

    /**
     * 封装查询当前危害因素的SQL
     *
     * @param curMap SQL参数
     * @return String SQL
     */
    private String generateQueryBadRsnIdSql(Map<String, Object> curMap) {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT DISTINCT T4.BADRSN_ID ");
        sql.append(" FROM TD_ZW_MONTH_BHK_PROV T ");
        sql.append("   INNER JOIN TS_UNIT M ON T.ORG_ID = M.RID ");
        sql.append("   INNER JOIN TS_ZONE M1 ON M.MANAGE_ZONE_ID = M1.RID ");
        sql.append("   INNER JOIN TD_ZW_MONTH_ORG T1 ON T1.MAIN_ID = T.RID ");
        sql.append("   INNER JOIN TB_TJ_SRVORG T2 ON T2.RID = T1.ORG_ID ");
        sql.append("   INNER JOIN TS_ZONE T3 ON T2.ZONE_ID = T3.RID ");
        sql.append("   LEFT JOIN TD_ZW_MONTH_ORG_BADRSNS T4 ON T4.MAIN_ID = T1.RID ");
        sql.append(" WHERE M1.ZONE_TYPE = 2 AND T.STATE = 1 AND NVL(T.DEL_MARK, 0) = 0 ");
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            sql.append(" AND T3.ZONE_GB LIKE :searchCheckOrgZoneGb escape '\\\'");
            curMap.put("searchCheckOrgZoneGb", ZoneUtil.zoneSelect(this.searchZoneGb.trim()) + "%");
        }
        sql.append(" AND T.END_BHK_DATE >= TO_DATE(:searchStartDate, 'YYYY-MM-DD') ");
        curMap.put("searchStartDate", (this.searchYear - 1) + "-11-16");
        sql.append(" AND T.END_BHK_DATE <= TO_DATE(:searchEndDate, 'YYYY-MM-DD HH24:MI:SS') ");
        curMap.put("searchEndDate", this.searchYear + "-11-15 23:59:59");
        return sql.toString();
    }

    @Override
    public String[] buildHqls() {
        String baseSql = this.generateQuerySql(this.paramMap);
        String querySql = baseSql + " ORDER BY T3.ZONE_GB, K.NUM ";
        String countSql = "SELECT COUNT(*) FROM (" + baseSql + ") ";
        return new String[]{querySql, countSql};
    }

    private String generateQuerySql(Map<String, Object> curMap) {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT T3.FULL_NAME, ");
        sql.append("     K.CODE_NAME, ");
        sql.append("     SUM(NVL(T4.HOLD_CARD_NUM, 0)) AS HOLD_CARD_NUM, ");
        sql.append("     SUM(NVL(T4.BHK_NUM, 0)) AS BHK_NUM, ");
        sql.append("     SUM(NVL(T4.SUSPECTED_NUM, 0)) AS SUSPECTED_NUM, ");
        sql.append("     SUM(NVL(T4.CONTRAINDLIST_NUM, 0)) AS CONTRAINDLIST_NUM ");
        sql.append(" FROM TD_ZW_MONTH_BHK_PROV T ");
        sql.append("     INNER JOIN TS_UNIT M ON T.ORG_ID = M.RID ");
        sql.append("     INNER JOIN TS_ZONE M1 ON M.MANAGE_ZONE_ID = M1.RID ");
        sql.append("     INNER JOIN TD_ZW_MONTH_ORG T1 ON T1.MAIN_ID = T.RID ");
        sql.append("     INNER JOIN TB_TJ_SRVORG T2 ON T2.RID = T1.ORG_ID ");
        sql.append("     INNER JOIN TS_ZONE T5 ON T2.ZONE_ID = T5.RID ");
        String zoneStr;
        TsZone zone = this.zoneMap.get(this.searchZoneGb.trim());
        Integer zoneType = ObjectUtil.convert(Integer.class, zone.getZoneType(), 0);
        Integer realZoneType = ObjectUtil.convert(Integer.class, zone.getRealZoneType(), 0);
        if (new Integer(2).equals(this.summaryType) && realZoneType < 4) {
            zoneType++;
        }
        if (new Integer("2").equals(zoneType)) {
            zoneStr = "SUBSTR(T5.ZONE_GB, 0, 2) || '00000000'";
        } else if (new Integer("3").equals(zoneType)) {
            zoneStr = "SUBSTR(T5.ZONE_GB, 0, 4) || '000000'";
        } else {
            zoneStr = "SUBSTR(T5.ZONE_GB, 0, 6) || '0000'";
        }
        sql.append("     INNER JOIN TS_ZONE T3 ON T3.ZONE_GB = ").append(zoneStr).append(" ");
        if (CollectionUtils.isEmpty(this.badrsnList)) {
            sql.append(" LEFT JOIN TS_SIMPLE_CODE K ON K.RID = 0 ");
        } else {
            sql.append(" LEFT JOIN TS_SIMPLE_CODE K ON K.RID IN(:badrsnList) ");
            curMap.put("badrsnList", this.badrsnList);
        }
        sql.append("     LEFT JOIN TD_ZW_MONTH_ORG_BADRSNS T4 ON T4.MAIN_ID = T1.RID AND T4.BADRSN_ID = K.RID ");
        sql.append(" WHERE M1.ZONE_TYPE = 2 AND T.STATE = 1 AND NVL(T.DEL_MARK, 0) = 0 ");
        if (CollectionUtils.isEmpty(this.badrsnList)) {
            sql.append(" AND 1 = 2 ");
        }
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            sql.append(" AND T3.ZONE_GB LIKE :searchCheckOrgZoneGb escape '\\\' ");
            curMap.put("searchCheckOrgZoneGb", ZoneUtil.zoneSelect(this.searchZoneGb.trim()) + "%");
        }
        sql.append(" AND T.END_BHK_DATE >= TO_DATE(:searchStartDate, 'YYYY-MM-DD') ");
        curMap.put("searchStartDate", (this.searchYear - 1) + "-11-16");
        sql.append(" AND T.END_BHK_DATE <= TO_DATE(:searchEndDate, 'YYYY-MM-DD HH24:MI:SS') ");
        curMap.put("searchEndDate", this.searchYear + "-11-15 23:59:59");
        sql.append(" GROUP BY T3.FULL_NAME, T3.ZONE_GB, K.RID, K.CODE_NAME, K.NUM, T3.REAL_ZONE_TYPE, T3.ZONE_TYPE ");
        return sql.toString();
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<Object[]> result = CollectionUtil.castList(Object[].class, list);
        for (Object[] obj : result) {
            //地区去除省份
            String fullName = StringUtils.objectToString(obj[0]);
            obj[0] = ZoneUtil.removeProvByFullName(fullName);
        }
    }

    /**
     * 导出前校验
     */
    public void preDownLoadFile() {
        if (this.validateQuery()) {
            return;
        }
        Map<String, Object> paramMap = new HashMap<>();
        String querySql = this.generateQueryBadRsnIdSql(paramMap);
        List<Integer> badrsnList = this.commService.findIntegerListBySql(querySql, paramMap);
        if (CollectionUtils.isEmpty(badrsnList)) {
            JsfUtil.addErrorMessage("无可导出的数据！");
            return;
        }
        paramMap = new HashMap<>();
        querySql = this.generateQueryBadRsnIdSql(paramMap);
        this.badrsnList = this.commService.findIntegerListBySql(querySql, paramMap);
        RequestContext.getCurrentInstance().execute("generateClick()");
    }

    /**
     * 导出
     */
    public DefaultStreamedContent getDownloadFile() {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            String title = getTitle();
            String[] columnHeaders = new String[]{"地区", "危害因素", "接触职业病危害因素劳动者（人）", "实际接受职业健康检查劳动者（人）", "检出的疑似职业病（人）", "职业禁忌证（人）"};
            Integer[] columnWidths = new Integer[]{30, 40, null, null, null, null};
            List<ExcelExportObject[]> excelExportObjects = new ArrayList<>();

            ExcelExportUtil excelExportUtil = new ExcelExportUtil(title, columnHeaders, excelExportObjects);
            pakExcelExportDataList(excelExportObjects, generateExportData(), columnHeaders.length);
            excelExportUtil.setSheetName(getTitlePre());
            excelExportUtil.setFrozenPaneRowsNum(2);
            excelExportUtil.setColumnWidths(columnWidths);
            if (new Integer(1).equals(this.summaryType)) {
                excelExportUtil.addMergeCells(2, 1 + excelExportObjects.size(), 0, 0);
            }
            Workbook wBook = excelExportUtil.exportExcel();

            wBook.write(baos);
            baos.flush();
            byte[] aa = baos.toByteArray();
            String tmpName = title + ".xlsx";
            String fileName = URLEncoder.encode(tmpName, "UTF-8");

            return new DefaultStreamedContent(
                    new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName
            );
        } catch (Exception e) {
            JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取导出的数据
     */
    private List<Object[]> generateExportData() {
        Map<String, Object> paramMap = new HashMap<>();
        String querySql = this.generateQuerySql(paramMap) + " ORDER BY T3.ZONE_GB, K.NUM ";
        List<Object[]> list =
                CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(querySql, paramMap));
        processData(list);
        return list;
    }

    /**
     * 将数据列表打包为Excel导出对象列表。
     *
     * @param excelExportObjectList 导出对象列表。
     * @param dataList              数据列表，每个元素是一个对象数组，代表一行数据。
     * @param headerSize            Excel表格的头部大小，用于确定每个数据行中导出对象的数量。
     */
    private void pakExcelExportDataList(List<ExcelExportObject[]> excelExportObjectList,
                                        List<Object[]> dataList, int headerSize) {
        // 检查数据列表和头部大小是否有效，如果无效则返回空列表
        if (ObjectUtil.isEmpty(dataList) || headerSize <= 0) {
            return;
        }

        for (Object[] data : dataList) {
            ExcelExportObject[] objects = new ExcelExportObject[headerSize];
            if (new Integer(1).equals(this.summaryType)) {
                objects[0] = new ExcelExportObject(StringUtils.objectToString(data[0]), XSSFCellStyle.ALIGN_CENTER);
            } else {
                objects[0] = new ExcelExportObject(StringUtils.objectToString(data[0]));
            }
            objects[1] = new ExcelExportObject(StringUtils.objectToString(data[1]));
            objects[2] = new ExcelExportObject(StringUtils.objectToString(data[2]), XSSFCellStyle.ALIGN_CENTER);
            objects[3] = new ExcelExportObject(StringUtils.objectToString(data[3]), XSSFCellStyle.ALIGN_CENTER);
            objects[4] = new ExcelExportObject(StringUtils.objectToString(data[4]), XSSFCellStyle.ALIGN_CENTER);
            objects[5] = new ExcelExportObject(StringUtils.objectToString(data[5]), XSSFCellStyle.ALIGN_CENTER);
            excelExportObjectList.add(objects);
        }
    }


    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneGb() {
        return searchZoneGb;
    }

    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public List<Integer> getSearchYearList() {
        return searchYearList;
    }

    public void setSearchYearList(List<Integer> searchYearList) {
        this.searchYearList = searchYearList;
    }

    public Integer getSearchYear() {
        return searchYear;
    }

    public void setSearchYear(Integer searchYear) {
        this.searchYear = searchYear;
    }

    public Integer getSummaryType() {
        return summaryType;
    }

    public void setSummaryType(Integer summaryType) {
        this.summaryType = summaryType;
    }

    public String getTableTitle() {
        return tableTitle;
    }

    public void setTableTitle(String tableTitle) {
        this.tableTitle = tableTitle;
    }

    public List<Integer> getBadrsnList() {
        return badrsnList;
    }

    public void setBadrsnList(List<Integer> badrsnList) {
        this.badrsnList = badrsnList;
    }
}
