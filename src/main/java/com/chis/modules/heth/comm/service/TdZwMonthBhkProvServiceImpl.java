package com.chis.modules.heth.comm.service;

import cn.hutool.core.convert.Convert;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.heth.comm.entity.TdZwMonthBhkProv;
import com.chis.modules.heth.comm.entity.TdZwMonthOrgBadrsns;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <p>方法描述：职业健康检查常规监测月度汇总 -服务</p>
 *
 * @MethodAuthor hsj 2024-08-09 8:59
 */
@Service
@Transactional(readOnly = true)
public class TdZwMonthBhkProvServiceImpl extends AbstractTemplate {


    /**
     * @param startDateStrList 开始日期字符串列表
     * @param zone             区域对象
     * @return {@link Map }<{@link String 日期范围（开始日期和结束日期）}, {@link Object[] 数量}>
     * <AUTHOR>
     */
    public Map<String, Object[]> findOrgNumBySatrtDate(List<String> startDateStrList, TsZone zone) {
        Map<String, Object[]> map = new HashMap<>();
        if (CollectionUtils.isEmpty(startDateStrList)) {
            return map;
        }
        StringBuilder sb = new StringBuilder();
        Map<String, Object> paramMap = new HashMap<>();
        sb.append(" SELECT  TO_CHAR(T.START_BHK_DATE,'yyyy-MM-dd'),TO_CHAR(T.END_BHK_DATE,'yyyy-MM-dd') ,COUNT(T.RID)");
        sb.append(" ,SUM(CASE WHEN T.STATE IN (1,2) THEN 1 ELSE 0 end)");
        sb.append(" FROM TD_ZW_MONTH_BHK T LEFT JOIN TB_TJ_SRVORG T1 ON T.ORG_ID  = T1.RID");
        sb.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID =T2.RID ");
        sb.append(" WHERE Nvl(T.DEL_MARK,0)  = 0 ");
        sb.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zone.getZoneGb())).append("%'");
        sb.append(" AND TO_CHAR(T.START_BHK_DATE,'yyyy-MM-dd') in (:startDateStrList)");
        paramMap.put("startDateStrList", startDateStrList);
        sb.append(" GROUP BY T.START_BHK_DATE ,T.END_BHK_DATE");
        List<Object[]> list = this.findDataBySqlNoPage(sb.toString(), paramMap);
        if (CollectionUtils.isEmpty(list)) {
            return map;
        }
        for (Object[] obj : list) {
            String key = Convert.toStr(obj[0]) + "&" + Convert.toStr(obj[1]);
            map.put(key, obj);
        }
        return map;
    }

    /**
     * <p>方法描述：查询这个周期和地区下所有填报的机构</p>
     *
     * @MethodAuthor hsj 2024-08-09 16:50
     */
    public List<Object[]> findTdZwMonthOrgListByDate(String startDate, String endDate, String editZoneCode, String editUnitId, Integer[] editState) {

        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return new ArrayList<>();
        }
        StringBuilder sb = new StringBuilder();
        Map<String, Object> paramMap = new HashMap<>();
        sb.append(" SELECT  T.RID ,CASE WHEN T2.ZONE_TYPE > 2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME, '_') + 1)  ELSE T2.FULL_NAME END ZONE_NAME,");
        sb.append(" T1.UNIT_NAME,T.STATE, T.ORG_ID ");
        sb.append(" FROM TD_ZW_MONTH_BHK T LEFT JOIN TB_TJ_SRVORG T1 ON T.ORG_ID  = T1.RID");
        sb.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID =T2.RID ");
        sb.append(" WHERE NVL(T.DEL_MARK,0)  = 0 ");
        sb.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(editZoneCode)).append("%'");
        sb.append(" AND TO_CHAR(T.START_BHK_DATE,'yyyy-MM-dd') =:startDate");
        paramMap.put("startDate", startDate);
        sb.append(" AND TO_CHAR(T.END_BHK_DATE,'yyyy-MM-dd') =:endDate");
        paramMap.put("endDate", endDate);
        if (StringUtils.isNotBlank(editUnitId)) {
            sb.append(" AND T.ORG_ID IN (:unitId) ");
            paramMap.put("unitId", StringUtils.string2list(editUnitId, ","));
        }
        if (editState != null && editState.length == 1) {
            if ("0".equals(Convert.toStr(editState[0]))) {
                sb.append(" AND T.STATE = 0");
            } else {
                sb.append(" AND T.STATE in (1,2)");
            }
        }
        sb.append(" ORDER BY T2.ZONE_GB,T1.UNIT_NAME,T.STATE");
        return this.findDataBySqlNoPage(sb.toString(), paramMap);
    }

    /**
     * <p>方法描述：根据主表获取危害因素对应的数</p>
     *
     * @MethodAuthor hsj 2024-08-09 11:17
     */
    public Map<Integer, Object[]> findOrgBadrsnObjByMainId(String mainId) {
        Map<Integer, Object[]> map = new HashMap<>();
        if (StringUtils.isBlank(mainId)) {
            return map;
        }
        StringBuilder sb = new StringBuilder();
        Map<String, Object> paramMap = new HashMap<>();
        sb.append(" SELECT T.BADRSN_ID,NVL(SUM(T.HOLD_CARD_NUM),0),NVL(SUM(T.BHK_NUM),0),NVL(SUM(T.SUSPECTED_NUM),0),NVL(SUM(T.CONTRAINDLIST_NUM),0)  ");
        sb.append(" FROM  TD_ZW_MONTH_BADRSNS T LEFT JOIN TD_ZW_MONTH_CRPT T1 ON  T.MAIN_ID =T1.RID  ");
        sb.append(" WHERE T1.MAIN_ID =").append(mainId);
        sb.append(" GROUP BY T.BADRSN_ID ");
        List<Object[]> list = this.findDataBySqlNoPage(sb.toString(), paramMap);
        if (CollectionUtils.isEmpty(list)) {
            return map;
        }
        for (Object[] obj : list) {
            map.put(Convert.toInt(obj[0]), obj);
        }
        return map;
    }

    /**
     * <p>方法描述：根据主表ids获取危害因素对应的数</p>
     *
     * @MethodAuthor hsj 2024-08-09 11:17
     */
    public Map<Integer, Map<Integer, Object[]>> findOrgBadrsnObjByMainIds(List<Integer> rids) {
        Map<Integer, Map<Integer, Object[]>> map = new HashMap<>();
        if (CollectionUtils.isEmpty(rids)) {
            return map;
        }
        StringBuilder sb = new StringBuilder();
        Map<String, Object> paramMap = new HashMap<>();
        sb.append(" SELECT T1.MAIN_ID,T.BADRSN_ID,NVL(SUM(T.HOLD_CARD_NUM),0),NVL(SUM(T.BHK_NUM),0),NVL(SUM(T.SUSPECTED_NUM),0),NVL(SUM(T.CONTRAINDLIST_NUM),0)  ");
        sb.append(" FROM  TD_ZW_MONTH_BADRSNS T LEFT JOIN TD_ZW_MONTH_CRPT T1 ON  T.MAIN_ID =T1.RID  ");
        sb.append(" WHERE T1.MAIN_ID in (:rids)");
        sb.append(" GROUP BY T1.MAIN_ID,T.BADRSN_ID ");
        paramMap.put("rids", rids);
        List<Object[]> list = this.findDataBySqlNoPage(sb.toString(), paramMap);
        if (CollectionUtils.isEmpty(list)) {
            return map;
        }
        for (Object[] obj : list) {
            if (!map.containsKey(Convert.toInt(obj[0]))) {
                Map<Integer, Object[]> objMap = new HashMap<>();
                objMap.put(Convert.toInt(obj[1]), obj);
                map.put(Convert.toInt(obj[0]), objMap);
            } else {
                map.get(Convert.toInt(obj[0])).put(Convert.toInt(obj[1]), obj);
            }
        }
        return map;
    }

    /**
     * <p>方法描述：根据mainId获取TdZwMonthOrg</p>
     *
     * @MethodAuthor hsj 2024-08-09 11:27
     */
    public List<TdZwMonthOrgBadrsns> findTdZwMonthOrgBadrsnsByMainId(Integer mainId) {
        String hql = "SELECT T FROM TdZwMonthOrgBadrsns T WHERE T.fkByMainId.rid =:mainId order by T.fkByBadrsnId.num,T.fkByBadrsnId.codeNo";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mainId", mainId);
        return this.findDataByHqlNoPage(hql, paramMap);
    }

    /**
     * <p>方法描述：根据查询条件查询TdZwMonthOrg</p>
     *
     * @MethodAuthor hsj 2024-08-09 11:33
     */
    public List<Object[]> findTdZwMonthOrgByMainId(Integer rid, String editZoneCode, String editUnitId) {
        StringBuilder sb = new StringBuilder();
        Map<String, Object> paramMap = new HashMap<>();
        sb.append(" SELECT  T.RID ,CASE WHEN T2.ZONE_TYPE > 2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME, '_') + 1)  ELSE T2.FULL_NAME END ZONE_NAME,");
        sb.append(" T1.UNIT_NAME ");
        sb.append(" FROM TD_ZW_MONTH_ORG T LEFT JOIN TB_TJ_SRVORG T1 ON T.ORG_ID  = T1.RID");
        sb.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID =T2.RID ");
        sb.append(" WHERE  T.MAIN_ID=").append(rid);
        sb.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(editZoneCode)).append("%'");
        if (StringUtils.isNotBlank(editUnitId)) {
            sb.append(" AND T.ORG_ID IN (:unitId) ");
            paramMap.put("unitId", StringUtils.string2list(editUnitId, ","));
        }
        sb.append(" ORDER BY T2.ZONE_GB,T1.UNIT_NAME");
        return this.findDataBySqlNoPage(sb.toString(), paramMap);
    }

    /**
     * <p>方法描述：根据主表rid查询状态</p>
     *
     * @MethodAuthor hsj 2024-08-09 11:40
     */
    public String findTdZwMonthBhkProvState(Integer rid) {
        String sql = "SELECT T.RID,T.state FROM TD_ZW_MONTH_BHK_PROV T WHERE T.rid = " + rid;
        List<Object[]> list = this.findDataBySqlNoPage(sql, null);
        return CollectionUtils.isEmpty(list) ? null : Convert.toStr(list.get(0)[1]);
    }

    /**
     * <p>方法描述：标删</p>
     *
     * @MethodAuthor hsj 2024-08-09 11:44
     */
    @Transactional(readOnly = false)
    public void deleteTdZwMonthBhkProv(Integer rid) {
        String sql = "UPDATE  TD_ZW_MONTH_BHK_PROV T SET T.DEL_MARK = 1 ,T.MODIFY_DATE = sysdate,T.MODIFY_MANID =:userId  WHERE T.rid =:rid ";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", rid);
        paramMap.put("userId", Global.getUser().getRid());
        this.executeSql(sql, paramMap);
    }


    public TdZwMonthBhkProv findTdZwMonthBhkProvByMainId(Integer rid) {
        return this.find(TdZwMonthBhkProv.class, rid);
    }

    /**
     * <p>方法描述：省级月度汇总表更新</p>
     *
     * @MethodAuthor hsj 2024-08-09 13:49
     */
    @Transactional(readOnly = false)
    public void updateTdZwMonthBhkProv(TdZwMonthBhkProv monthBhkProv) {
        List<Integer> selOrgIds = monthBhkProv.getSelOrgIds();
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "DELETE TD_ZW_MONTH_ORG_BADRSNS T WHERE T.MAIN_ID IN (SELECT T1.RID FROM  TD_ZW_MONTH_ORG T1 WHERE T1.MAIN_ID =:maindId)";
        paramMap.put("maindId", monthBhkProv.getRid());
        this.executeSql(sql, paramMap);
        paramMap = new HashMap<>();
        sql = "DELETE TD_ZW_MONTH_ORG T WHERE T.MAIN_ID =:maindId";
        paramMap.put("maindId", monthBhkProv.getRid());
        this.executeSql(sql, paramMap);
        monthBhkProv.setState(1);
        this.upsertEntity(monthBhkProv);
        if (CollectionUtils.isEmpty(selOrgIds)) {
            return;
        }
        paramMap = new HashMap<>();
        sql = "UPDATE TD_ZW_MONTH_BHK T SET T.STATE = 2  WHERE T.STATE =  1  AND T.rid in (:rids)";
        paramMap.put("rids", selOrgIds);
        this.executeSql(sql, paramMap);
    }

    /**
     * <p>方法描述：撤销逻辑</p>
     *
     * @MethodAuthor hsj 2024-08-09 13:57
     */
    @Transactional(readOnly = false)
    public void cancelTdZwMonthBhkProv(TdZwMonthBhkProv monthBhkProv, TsZone zone) {
        String sql = "UPDATE  TD_ZW_MONTH_BHK_PROV T SET T.STATE = 0 ,T.MODIFY_DATE = sysdate,T.MODIFY_MANID =:userId  WHERE  T.rid =:rid ";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", monthBhkProv.getRid());
        paramMap.put("userId", Global.getUser().getRid());
        this.executeSql(sql, paramMap);
        //更新TD_ZW_MONTH_BHK
        StringBuilder sb = new StringBuilder();
        paramMap = new HashMap<>();
        sb.append(" SELECT T.RID FROM TD_ZW_MONTH_BHK T");
        sb.append(" LEFT JOIN TB_TJ_SRVORG T1 ON T1.RID = T.ORG_ID");
        sb.append(" LEFT JOIN TS_ZONE T2 ON T2.RID = T1.ZONE_ID");
        sb.append(" WHERE NVL(T.DEL_MARK,0)  = 0 ");
        sb.append(" AND TO_CHAR(T.START_BHK_DATE,'yyyy-MM-dd') =:startDate");
        paramMap.put("startDate", DateUtils.formatDate(monthBhkProv.getStartBhkDate(), "yyyy-MM-dd"));
        sb.append(" AND TO_CHAR(T.END_BHK_DATE,'yyyy-MM-dd') =:endDate");
        paramMap.put("endDate", DateUtils.formatDate(monthBhkProv.getEndBhkDate(), "yyyy-MM-dd"));
        sb.append(" AND T.ORG_ID IN ( SELECT DISTINCT O.ORG_ID   FROM TD_ZW_MONTH_ORG O   WHERE O.MAIN_ID =:mainId");
        paramMap.put("mainId", monthBhkProv.getRid());
        sb.append("  AND NOT EXISTS (  ");
        sb.append("  SELECT 1   FROM TD_ZW_MONTH_ORG O1   LEFT JOIN TD_ZW_MONTH_BHK_PROV P ON P.rid = O1.MAIN_ID  ");
        sb.append(" WHERE O1.ORG_ID = O.ORG_ID   AND P.RID !=:mainId   AND P.STATE = 1 AND NVL(P.DEL_MARK,0)  = 0 ");
        sb.append(" AND TO_CHAR(P.START_BHK_DATE,'yyyy-MM-dd') =:startDate");
        paramMap.put("startDate", DateUtils.formatDate(monthBhkProv.getStartBhkDate(), "yyyy-MM-dd"));
        sb.append(" AND TO_CHAR(P.END_BHK_DATE,'yyyy-MM-dd') =:endDate");
        paramMap.put("endDate", DateUtils.formatDate(monthBhkProv.getEndBhkDate(), "yyyy-MM-dd"));
        sb.append(" ))");
        List<Object> list = this.findDataBySqlNoPage(sb.toString(), paramMap);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Integer> rids = new ArrayList<>();
        for (Object objects : list) {
            rids.add(Convert.toInt(objects));
        }
        List<List<Integer>> listList = StringUtils.splitListProxy(rids, 1000);
        for (List<Integer> ridList : listList) {
            paramMap = new HashMap<>();
            sql = " UPDATE  TD_ZW_MONTH_BHK T SET T.STATE = 1   WHERE T.STATE =  2  AND T.RID in (:ridList)";
            paramMap.put("ridList", ridList);
            this.executeSql(sql, paramMap);
        }
    }

    /**
     * <p>方法描述：选择的报告出具日期区间在该填报单位是否已存在（排除标删）</p>
     *
     * @MethodAuthor hsj 2024-08-09 14:32
     */
    public Integer findCountByStartDate(Date startBhkDate, Date endBhkDate) {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT COUNT(1) FROM TD_ZW_MONTH_BHK_PROV T");
        sb.append(" WHERE TO_CHAR(T.START_BHK_DATE,'yyyy-MM-dd') =:startDate");
        sb.append(" AND TO_CHAR(T.END_BHK_DATE,'yyyy-MM-dd') =:endDate ");
        sb.append(" AND NVL(T.DEL_MARK,0)  = 0");
        sb.append(" AND T.ORG_ID =:orgId");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("startDate", DateUtils.formatDate(startBhkDate, "yyyy-MM-dd"));
        paramMap.put("endDate", DateUtils.formatDate(endBhkDate, "yyyy-MM-dd"));
        paramMap.put("orgId", Global.getUser().getTsUnit().getRid());
        return this.findCountBySql(sb.toString(), paramMap);
    }
}
