package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.ToggleSelectEvent;
import org.primefaces.event.UnselectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>类描述：检查机构弹出框</p>
 * @ClassAuthor qrr,2019年12月3日,SelectTjOrgListBean
 */
@ManagedBean(name = "selectTjOrgCommListBean")
@ViewScoped
public class SelectTjOrgListBean extends FacesBean {

    private static final long serialVersionUID = -738445454536625063L;

    /** 名称 或 拼音码*/
    private String searchName;
	/** 查询列集合*/
	private List<TbTjSrvorg> displayList;
	/** 所有集合*/
	private List<TbTjSrvorg> allList;
	/**选中的集合*/
	private List<TbTjSrvorg> selectedList;
	/**所有选中的集合*/
	private List<TbTjSrvorg> allSelectedList;
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
    /** 查询条件-地区*/
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
	/**是否显示全部选择按钮*/
	private Boolean showAllSel;
    /**
 	 * <p>方法描述：初始化数据</p>
 	 * @MethodAuthor qrr,2019年12月3日,SelectTjOrgListBean
     * */
    public SelectTjOrgListBean() {
    	String selectIds = JsfUtil.getRequest().getParameter("selectIds");//已选择的码表rid，以逗号隔开
		String zoneCode = JsfUtil.getRequest().getParameter("searchZoneCode");//已选择的码表rid，以逗号隔开
		//是否显示选择全部按钮
		String showAllSelStr = JsfUtil.getRequest().getParameter("showAllSel");
		this.showAllSel = "true".equals(showAllSelStr);
		this.initZone(zoneCode);
		this.init(selectIds,this.searchZoneCode);
    }
    /**
 	 * <p>方法描述：查询所有体检机构</p>
 	 * @MethodAuthor qrr,2019年12月3日,init
     * */
    private void init(String selectIds,String zoneCode)   {
		this.displayList = new ArrayList<>();
		this.allList = new ArrayList<>();
		this.selectedList = new ArrayList<>();
		this.allSelectedList = new ArrayList<>();
		List<TbTjSrvorg> list = new ArrayList<>();
		if(StringUtils.isNotBlank(zoneCode)){
			list = commService.findByHql(
					"select t from TbTjSrvorg t where t.stopTag = 1 and t.tsZone.zoneGb like '"+ ZoneUtil.zoneSelect(zoneCode)+"%' order by t.tsZone.zoneGb,t.unitName", TbTjSrvorg.class);
		}else{
			list = commService.findByHql(
					"select t from TbTjSrvorg t where t.stopTag = 1  order by t.tsZone.zoneGb,t.unitName", TbTjSrvorg.class);
		}
		int row = -1;// 第一次出现勾选的当前页的第一行数
		if (null != list && list.size() > 0) {
			int  i = 0;
			List<String> stringList = StringUtils.string2list(selectIds, ",");
			for (TbTjSrvorg obj : list) {
				if (ObjectUtil.isNotEmpty(stringList)
						&& stringList.contains(obj.getRid().toString())) {
					if (row == -1) {
						row = i - i % 10;
					}
					this.selectedList.add(obj);
				}
        		i++;
			}
			this.allList.addAll(list);
			this.displayList.addAll(list);
			this.allSelectedList.addAll(selectedList);
		}
		 //初始化选择当前页的第一行数
        if (row>-1) {
        	DataTable dataTable = (DataTable) FacesContext
        			.getCurrentInstance().getViewRoot()
        			.findComponent("codeForm:selectedTable");
        	dataTable.setFirst(row);
        }	
    }
    /**
 	 * <p>方法描述：初始化地区</p>
 	 * @MethodAuthor qrr,2020年5月7日,initZone
     * */
    private void initZone(String zoneCode) {
		if(StringUtils.isBlank(zoneCode)){
			zoneCode = Global.getUser().getTsUnit().getTsZone().getZoneGb().substring(0, 2);
		}
		// 地区初始化
		this.zoneList = this.commService.findZoneListByGbAndType(zoneCode, true, "", "");
		this.searchZoneCode = this.zoneList.get(0).getZoneGb();
		this.searchZoneName = this.zoneList.get(0).getZoneName();
	}
    /**
 	 * <p>方法描述：根据名称、拼音码过滤数据</p>
 	 * @MethodAuthor qrr,2018年4月9日,searchAction
     */
    public void searchAction() {
        //初始化展示页面的疫苗数据集
        this.displayList = new ArrayList<TbTjSrvorg>();
        if( null != allList && allList.size() > 0)    {
    		if(StringUtils.isNotBlank(searchName))    {
    			for(TbTjSrvorg t :allList)   {
    				//疫苗名称
    				String unitName = t.getUnitName()==null?"":t.getUnitName();
    				//如果模糊匹配上，则增加
    				if (unitName.indexOf(searchName) != -1) {
    					//地区
    					String zoneGb = t.getTsZone().getZoneGb();
    					if (zoneGb.startsWith(ZoneUtil.zoneSelect(this.searchZoneCode))) {
    						this.displayList.add(t);
						}
    				}
    			}
    		}else{
    			for(TbTjSrvorg t :allList)   {
    				String zoneGb = t.getTsZone().getZoneGb();
					if (zoneGb.startsWith(ZoneUtil.zoneSelect(this.searchZoneCode))) {
						this.displayList.add(t);
					}
    			}
    		}
    	}
		//选中的单位
		this.selectedList.clear();
		this.selectedList.addAll(this.allSelectedList);
		DataTable dataTable = (DataTable) FacesContext
				.getCurrentInstance().getViewRoot()
				.findComponent("codeForm:selectedTable");
		dataTable.setFirst(0);
    }
    /**
 	 * <p>方法描述：提交</p>
 	 * @MethodAuthor qrr,2019年12月2日,submitAction
     * */
    public void submitAction() {
		if (CollectionUtils.isEmpty(this.allList)) {
			JsfUtil.addErrorMessage("请选择数据！");
			return;
		}
		if(CollectionUtils.isEmpty(this.allSelectedList)){
			JsfUtil.addErrorMessage("请选择数据！");
			return;
		}else if (this.allSelectedList.size()>1000) {
			JsfUtil.addErrorMessage("最多可选择1000条数据！");
			return;
		}
		Map<String, List<TbTjSrvorg>> map = new HashMap<>();
		map.put("selectPros", this.allSelectedList);
		RequestContext.getCurrentInstance().closeDialog(map);
	}
	/**
	 *  <p>方法描述：行选中监听，添加元素</p>
	 * @MethodAuthor hsj 2024-07-03 10:43
	 */
	public void rowSelectListener(SelectEvent event){
		TbTjSrvorg srvorg = (TbTjSrvorg)event.getObject();
		this.allSelectedList.add(srvorg);
	}
	/**
	 *  <p>方法描述：行取消，移除元素</p>
	 * @MethodAuthor hsj 2024-07-03 10:44
	 */
	public void rowUnselectListener(UnselectEvent event){
		TbTjSrvorg srvorg = (TbTjSrvorg)event.getObject();
		this.allSelectedList.remove(srvorg);
	}
	/**
	 *  <p>方法描述：全选或取消全选</p>
	 * @MethodAuthor hsj 2024-07-03 10:44
	 */
	public void toggleSelectListener(ToggleSelectEvent event){
		DataTable dataTable = (DataTable) FacesContext
				.getCurrentInstance().getViewRoot()
				.findComponent("codeForm:selectedTable");
		if(this.displayList.size()==0){//表格无数据
			return;
		}
		if(event.isSelected()){
			List<TbTjSrvorg> list = (List)dataTable.getSelection();//当前datatable选中的项
			if(!CollectionUtils.isEmpty(list)){
				for(TbTjSrvorg o: list){
					if(!this.allSelectedList.contains(o)){
						this.allSelectedList.add(o);
					}
				}
			}
		}else{//取消全选，需要移除当前页的所有元素
			int current = dataTable.getPage();//获取当前页码
			int rows = dataTable.getRows();//每页条数
			//遍历当前表格数据，将当前页的数据移除
			int curFirst = current * rows;//当前页第一个元素下标
			int curLast = Math.min(curFirst + rows - 1, this.displayList.size() - 1);//当前页最后一个元素的下标（取相对小的）
			for (int i = curFirst; i <= curLast; i++) {
				this.allSelectedList.remove(this.displayList.get(i));
			}
		}
	}
	/**
	 *  <p>方法描述：全部选择</p>
	 * @MethodAuthor hsj 2024-12-20 11:46
	 */
	public void showAllSelAction(){
		if(CollectionUtils.isEmpty(this.displayList)){
			return;
		}
		for(TbTjSrvorg srvorg:this.displayList){
			if(!this.selectedList.contains(srvorg)){
				this.selectedList.add(srvorg);
			}
			if(!this.allSelectedList.contains(srvorg)){
				this.allSelectedList.add(srvorg);
			}
		}
	}
    /**
 	 * <p>方法描述：关闭</p>
 	 * @MethodAuthor qrr,2018年4月9日,dialogClose
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }
	public String getSearchName() {
		return searchName;
	}
	public List<TbTjSrvorg> getDisplayList() {
		return displayList;
	}
	public void setSearchName(String searchName) {
		this.searchName = searchName;
	}
	public void setDisplayList(List<TbTjSrvorg> displayList) {
		this.displayList = displayList;
	}
	public List<TsZone> getZoneList() {
		return zoneList;
	}
	public String getSearchZoneCode() {
		return searchZoneCode;
	}
	public String getSearchZoneName() {
		return searchZoneName;
	}
	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}
	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}
	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}

	public List<TbTjSrvorg> getAllList() {
		return allList;
	}

	public void setAllList(List<TbTjSrvorg> allList) {
		this.allList = allList;
	}

	public List<TbTjSrvorg> getSelectedList() {
		return selectedList;
	}

	public void setSelectedList(List<TbTjSrvorg> selectedList) {
		this.selectedList = selectedList;
	}

	public List<TbTjSrvorg> getAllSelectedList() {
		return allSelectedList;
	}

	public void setAllSelectedList(List<TbTjSrvorg> allSelectedList) {
		this.allSelectedList = allSelectedList;
	}

	public Boolean getShowAllSel() {
		return showAllSel;
	}

	public void setShowAllSel(Boolean showAllSel) {
		this.showAllSel = showAllSel;
	}
}
