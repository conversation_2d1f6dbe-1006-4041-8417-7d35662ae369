package com.chis.modules.heth.comm.web;

import com.chis.common.bean.FastReportData;
import com.chis.common.bean.FastReportDataRef;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.service.HethStaQueryCommServiceImpl;
import com.chis.modules.heth.comm.service.TdZwYszybRptServiceCommImpl;
import com.chis.modules.heth.comm.service.ZwReportCardCommServiceImpl;
import com.chis.modules.heth.comm.utils.CalLimitTimeCommUtils;
import com.chis.modules.heth.comm.utils.RptCardTempCommUtil;
import com.chis.modules.system.entity.TbZwWritsort;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.logic.SysReturnPojo;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.utils.HolidayUtil;
import com.chis.modules.system.web.FastReportBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * @Description : 疑似职业病报告卡-业务层
 * @ClassAuthor : anjing
 * @Date : 2019/9/3 10:50
 **/
@ManagedBean(name="tdZwYszybRptListCommBean")
@ViewScoped
public class TdZwYszybRptListCommBean extends AbstractReportCardCommBean {

    /**查询条件：地区集合*/
    private List<TsZone> zoneList;
    /**查询条件：地区名称*/
    private String searchZoneName;
    /**查询条件：地区编码*/
    private String searchZoneCode;
    /**查询条件：随访查询-用人单位名称*/
    private String searchCrptName;
    /**查询条件：随访查询-人员姓名*/
    private String searchPsnName;
    /**查询条件：随访查询-身份证号*/
    private String searchIdc;
    /**查询条件：随访查询-疑似职业病名称*/
    private String searchYszybName;
    /**查询条件：发现日期-查询开始日期*/
    private Date searchFindBdate;
    /**查询条件：发现日期-查询结束日期*/
    private Date searchFindEdate;
    private TdZwTjorginfoComm tjorginfo;

    private Integer rid;
    private Integer relBhkId;
    private Integer occdiseId;//疑似职业病Id
    private TdZwYszybRpt tdZwYszybRpt;
    private int currentYear;
    private Integer otherSourceDisable;
    /**是否设计*/
    private String ifDesign;

    /**true：处理期限，false：无处理期限*/
    private boolean ifshowdeadline;
    //是否化学
    private boolean ifChemical;
    //是否工种其他
    private boolean ifOtherWork;

    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private TdZwYszybRptServiceCommImpl service = SpringContextHolder.getBean(TdZwYszybRptServiceCommImpl.class);
    private ZwReportCardCommServiceImpl zwReportCardService = SpringContextHolder.getBean(ZwReportCardCommServiceImpl.class);
    private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
            .getBean(SystemModuleServiceImpl.class);
    private ZwReportCardCommServiceImpl cardServiceImpl = SpringContextHolder.getBean(ZwReportCardCommServiceImpl.class);
    private HethStaQueryCommServiceImpl hethStaQueryServiceImpl = SpringContextHolder.getBean(HethStaQueryCommServiceImpl.class);

    /**文书报表*/
    private FastReportBean fastReportBean;
    /***/
    private TbZwWritsort writsort;
    private TdZwBgkLastSta tdZwBgkLastSta;

    private String yszybNostdTime;
    //页面标识
    private Integer receiveDate;
    private Integer tag;
    private String dateName;
    //是否市直属
    private String cityDirectCode;
    private TsZone rptZone;
    /**是否市级直属*/
    private boolean ifCityDirect;
    /** 审核级别 */
    private String checkLevel;
    /** 二级审核*/
    private Boolean twoLevelAudit;
    /** 三级审核*/
    private Boolean threeLevelAudit;

    public TdZwYszybRptListCommBean() {
        checkLevel = PropertyUtils.getValueWithoutException("checkLevel");
        twoLevelAudit = "2".equals(checkLevel);
        threeLevelAudit = "3".equals(checkLevel);
        this.initParam();
        super.ifSQL = true;

        String val2 = JsfUtil.getRequestParameter("tag");
        String val = PropertyUtils.getValue("receiveDate");
        if(StringUtils.isNotBlank(val)){
            receiveDate = Integer.valueOf(val);
        }
        if(StringUtils.isNotBlank(val2)){
            tag = Integer.valueOf(val2);
        }
        if(1==receiveDate){
            dateName ="接收日期";
        }else{
            dateName ="体检日期";
        }
        if(!this.verfyBySearch()) {
            return;
        }
        String sDeadline = PropertyUtils.getValue("ifshowdeadline");
        if("true".equals(sDeadline)){
            ifshowdeadline = true;
        }else{
            ifshowdeadline = false;
        }
        this.searchAction();
    }

    private void initState() {
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem("0.5", "待填报"));
        this.stateList.add(new SelectItem("0", "待提交"));
        this.stateList.add(new SelectItem("2,4,6", "已退回"));

        this.stateList.add(new SelectItem("1", "待初审"));
        this.stateList.add(new SelectItem("3", "待复审"));
        this.stateList.add(new SelectItem("5", "待终审"));
        this.stateList.add(new SelectItem("7", "终审通过"));
        this.states = new String[] { "0.5", "0", "2,4,6"};
    }

    /**
     * <p>描述 1.1、审核级别（CHECK_LEVEL=3）：待填报、待提交、已退回、待初审、待复审、待终审、终审通过。
     *        1.2、审核级别（CHECK_LEVEL=2）：待填报、待提交、已退回、待初审、待终审、终审通过。</p>
     *
     * @MethodAuthor gongzhe,2022/4/24 11:35,initStateNew
     * @return void
     */
    private void initStateNew() {
        this.stateList = new ArrayList<>();
        if(twoLevelAudit){
            this.stateList.add(new SelectItem("0.5", "待填报"));
            this.stateList.add(new SelectItem("0", "待提交"));
            this.stateList.add(new SelectItem("2,6", "已退回"));
            this.stateList.add(new SelectItem("1", "待初审"));
            this.stateList.add(new SelectItem("5", "待终审"));
            this.stateList.add(new SelectItem("7", "终审通过"));
            this.states = new String[] { "0.5", "0", "2,6"};
        }else if(threeLevelAudit){
            this.stateList.add(new SelectItem("0.5", "待填报"));
            this.stateList.add(new SelectItem("0", "待提交"));
            this.stateList.add(new SelectItem("2,4,6", "已退回"));
            this.stateList.add(new SelectItem("1", "待初审"));
            this.stateList.add(new SelectItem("3", "待复审"));
            this.stateList.add(new SelectItem("5", "待终审"));
            this.stateList.add(new SelectItem("7", "终审通过"));
            this.states = new String[] { "0.5", "0", "2,4,6"};
        }

    }

    /**
     * @Description : 查询参数初始化
     * @MethodAuthor: anjing
     * @Date : 2019/9/3 10:51
     **/
    private void initParam() {
        /*** 地区初始化 */
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        // 地区初始化
        if (null == this.zoneList || this.zoneList.size() <= 0) {
            this.searchZoneCode=tsZone.getZoneGb().substring(0,2)+"00000000";
            this.searchZoneName =cardServiceImpl.findProByZoneGb(this.searchZoneCode).getZoneName();
            if(null == this.zoneList || this.zoneList.size() ==0) {
                this.zoneList = this.commService.findZoneList(false, this.searchZoneCode, null, null);
            }
        }

        Date now = new Date();
        // 默认：当前日期往前推90天
        /*this.searchFindBdate = DateUtils.getDateByDays(-90);*/
        // 默认：今天
        this.searchFindEdate = now;

        // 当前年份初始化
        this.currentYear = DateUtils.getYear(now);

        this.yszybNostdTime = PropertyUtils.getValue("yszybNostdTime");
        ifDesign = PropertyUtils.getValue("rpt.ifDesign");
        initStateNew();
    }

    /**
     * @Description : 查询前校验
     * @MethodAuthor: anjing
     * @Date : 2019/9/4 16:17
     **/
    private boolean verfyBySearch() {
        boolean flag = true;
        List<TdZwTjorginfoComm> tdZwTjorginfoList = this.service.selectOrgListByOrgId(this.sessionData.getUser().getTsUnit().getRid());
        if(CollectionUtils.isEmpty(tdZwTjorginfoList)) {
            JsfUtil.addErrorMessage("您单位暂无职业健康检查资质，请先在系统内填报资质信息！");
            flag = false;
        } else {
            this.tjorginfo = tdZwTjorginfoList.get(0);
        }
        /*if(null == this.searchFindBdate){
            JsfUtil.addErrorMessage("开始"+dateName+"不能为空！");
            flag = false;
        }*/
        if(null == this.searchFindEdate){
            JsfUtil.addErrorMessage("结束"+dateName+"不能为空！");
            flag = false;
        }
        if(DateUtils.isDateAfter(this.searchFindBdate, this.searchFindEdate)){
            JsfUtil.addErrorMessage("结束"+dateName+"大于等于"+"开始"+dateName+"！");
            flag = false;
        }
        return flag;
    }

    /**
     * @Description : 执行查询
     * @MethodAuthor: anjing
     * @Date : 2019/9/3 10:52
     **/
    @Override
    public void searchAction() {
        if(!this.verfyBySearch()) {
            return;
        }
        super.searchAction();
    }

    @Override
    public String[] buildHqls() {
        // 状态位是否包含待填报
        boolean ifFill = false;
        StringBuilder stateSb = new StringBuilder();
        if (null != states && states.length > 0) {
            for (String state : states) {
                stateSb.append(",").append(state);
                if ("0.5".equals(state)) {
                    ifFill = true;
                }
            }
        }

        StringBuilder sb = new StringBuilder();
        if (ifFill || stateSb.length() == 0) {
            sb.append(" SELECT distinct NULL AS RID, T.RID AS REL_BHK_ID, T5.ZONE_GB AS ZONE_GB, ");
            sb.append("CASE WHEN T5.ZONE_TYPE >2 THEN SUBSTR(T5.FULL_NAME, INSTR(T5.FULL_NAME,'_')+1) ELSE T5.FULL_NAME END ZONE_NAME, ");
            sb.append("T4.CRPT_NAME AS CRPT_NAME, T.PERSON_NAME AS PERSON_NAME, T.IDC AS IDC, T9.code_name AS YSZYB_NAME,  ");
            if(receiveDate ==1){
                sb.append(" T.RPT_PRINT_DATE AS RCV_DATE,");
            }else{
                sb.append(" T.BHK_DATE AS RCV_DATE,");
            }
            sb.append("NULL AS FILL_DATE, 0.5 AS STATE, 1 AS IF_FILL, 1 AS LIMIT_DAY ,t9.rid occdiseId,nvl(t5.IF_CITY_DIRECT,0) cityDirect,nvl(t5.IF_PROV_DIRECT,0) provDirect");
            sb.append(" FROM TD_TJ_BHK T ");
            sb.append(" INNER JOIN TB_TJ_SRVORG T1 ON T.BHKORG_ID = T1.RID ");
            sb.append(" INNER JOIN TS_UNIT T2 ON T1.REG_ORGID = T2.RID ");
            sb.append(" LEFT JOIN TB_TJ_CRPT T4 ON T.ENTRUST_CRPT_ID = T4.RID ");
            sb.append(" LEFT JOIN TS_ZONE T5 ON T4.ZONE_ID = T5.RID ");
            sb.append(" LEFT JOIN TD_TJ_MHKRST T6 ON T6.BHK_ID = T.RID ");
            sb.append(" LEFT JOIN TS_SIMPLE_CODE T7 ON T6.BHKRST_ID = T7.RID ");
            sb.append(" Inner JOIN TD_TJ_SUPOCCDISELIST t8 on t8.BHK_ID =t.rid ");
            sb.append(" LEFT JOIN TS_SIMPLE_CODE T9 ON T9.RID = T8.OCC_DISEID ");
            sb.append(" WHERE 1=1 ");
            sb.append(" AND T.BHK_TYPE IN (3, 4) ");
            sb.append("  AND T4.INTER_PRC_TAG = 1 ");
            sb.append(" AND T7.EXTENDS2 = 5 ");
            sb.append(" AND NOT EXISTS ( ");
            sb.append(" SELECT 1 FROM TD_ZW_YSZYB_RPT TT INNER JOIN TD_ZW_BGK_LAST_STA TT1 ON TT1.BUS_ID = TT.RID " +
                    " INNER JOIN TS_SIMPLE_CODE TT2 ON TT2.RID = TT.SOURCE_ID " +
                    "WHERE NVL(TT.DEL_MARK, 0) = 0 AND TT.REL_BHK_ID = T.RID and tt.OCC_DISEID =T8.OCC_DISEID " +
                    " AND TT2.EXTENDS1 = 1  AND TT1.CART_TYPE = 2 ");
            sb.append(" ) ");
            if (null != sessionData.getUser() && null != sessionData.getUser().getTsUnit()) {
                sb.append(" AND T2.RID = ").append(sessionData.getUser().getTsUnit().getRid());
            }
            if(receiveDate ==1){
                if (StringUtils.isNotBlank(bhkBeginDate)) {
                    sb.append(" AND T.RPT_PRINT_DATE >=to_date('").append(bhkBeginDate).append("','yyyy-MM-dd')");
                }
                if (null != this.searchFindBdate) {
                    sb.append(" AND T.RPT_PRINT_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchFindBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
                }
                if (null != this.searchFindEdate) {
                    sb.append(" AND T.RPT_PRINT_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchFindEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
                }
            }else{
                if (StringUtils.isNotBlank(bhkBeginDate)) {
                    sb.append(" AND T.BHK_DATE >=to_date('").append(bhkBeginDate).append("','yyyy-MM-dd')");
                }
                if (null != this.searchFindBdate) {
                    sb.append(" AND T.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchFindBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
                }
                if (null != this.searchFindEdate) {
                    sb.append(" AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchFindEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
                }
            }

            if (StringUtils.isNotBlank(this.searchZoneCode)) {
                sb.append(" AND T5.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%' ");
            }
            if (StringUtils.isNotBlank(this.searchCrptName)) {
                sb.append(" AND T4.CRPT_NAME LIKE :crptName escape '\\\'");
                this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
            }
            if (StringUtils.isNotBlank(this.searchPsnName)) {
                sb.append(" AND T.PERSON_NAME LIKE :psnName escape '\\\'");
                this.paramMap.put("psnName", "%" + StringUtils.convertBFH(this.searchPsnName.trim()) + "%");
            }
            if (StringUtils.isNotBlank(this.searchIdc)) {
                sb.append(" AND T.IDC LIKE :idc escape '\\\'");
                this.paramMap.put("idc",StringUtils.convertBFH(this.searchIdc.trim()));
            }
            if (StringUtils.isNotBlank(this.searchYszybName)) {
                sb.append(" AND t9.code_name LIKE :yszybName escape '\\\'");
                this.paramMap.put("yszybName", "%" + StringUtils.convertBFH(this.searchYszybName.trim()) + "%");
            }

            sb.append(" UNION ALL ");
        }

        sb.append(" SELECT T.RID, T.REL_BHK_ID AS REL_BHK_ID, T3.ZONE_GB AS ZONE_GB, " +
                "CASE WHEN T3.ZONE_TYPE >2 THEN SUBSTR(T3.FULL_NAME, INSTR(T3.FULL_NAME,'_')+1) ELSE T3.FULL_NAME END ZONE_NAME, " +
                "to_char(T.EMP_CRPT_NAME)  AS CRPT_NAME, T.PERSONNEL_NAME AS PERSON_NAME, T.IDC, t5.CODE_NAME, T4.ORG_RCV_DATE AS RCV_DATE, T.FILL_DATE, " +
                "case when (T3.IF_CITY_DIRECT =1 and T4.STATE =3) then 1 else T4.STATE end as state, 1 AS IF_FILL, 1 AS LIMIT_DAY," +
                "t.OCC_DISEID occdiseId,nvl(T3.IF_CITY_DIRECT,0) cityDirect,nvl(T3.IF_PROV_DIRECT,0) provDirect");
        sb.append(" FROM TD_ZW_YSZYB_RPT T ");
        sb.append(" INNER JOIN TS_UNIT T2 ON T.RPT_UNIT_ID = T2.RID ");
        sb.append(" LEFT JOIN TS_ZONE T3 ON T.EMP_ZONE_ID = T3.RID ");
        sb.append(" INNER JOIN TD_ZW_BGK_LAST_STA T4 ON T4.BUS_ID = T.RID ");
        sb.append(" LEFT join TS_SIMPLE_CODE t5 on t.OCC_DISEID = t5.rid");
        sb.append(" INNER JOIN TS_SIMPLE_CODE TT2 ON TT2.RID = T.SOURCE_ID ");
        sb.append(" WHERE NVL(T.DEL_MARK, 0) = 0 ");
        sb.append(" AND T4.CART_TYPE = 2 ");
        sb.append(" AND TT2.EXTENDS1 = 1 ");
        if(null != sessionData.getUser() && null != sessionData.getUser().getTsUnit()) {
            sb.append(" AND T2.RID = ").append(sessionData.getUser().getTsUnit().getRid());
        }
        if(StringUtils.isNotBlank(this.searchZoneCode)) {
            sb.append(" AND T3.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%' ");
        }
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sb.append(" AND T.EMP_CRPT_NAME LIKE :crptName escape '\\\'");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        if (StringUtils.isNotBlank(this.searchPsnName)) {
            sb.append(" AND T.PERSONNEL_NAME LIKE :psnName escape '\\\'");
            this.paramMap.put("psnName", "%" + StringUtils.convertBFH(this.searchPsnName.trim()) + "%");
        }
        if(StringUtils.isNotBlank(this.searchIdc)) {
            sb.append(" AND T.IDC LIKE :idc escape '\\\'");
            this.paramMap.put("idc",StringUtils.convertBFH(this.searchIdc.trim()));
        }
        if (null != this.searchFindBdate) {
            sb.append(" AND T4.ORG_RCV_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchFindBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchFindEdate) {
            sb.append(" AND T4.ORG_RCV_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchFindEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if (StringUtils.isNotBlank(this.searchYszybName)) {
            sb.append(" AND (T.YSZYB_NAME LIKE :yszybName escape '\\\' or T5.Code_name LIKE :yszybName escape '\\\')");
            this.paramMap.put("yszybName", "%" + StringUtils.convertBFH(this.searchYszybName.trim()) + "%");
        }
        if (stateSb.length() > 0) {
            if(threeLevelAudit){
                sb.append(" AND (case when T3.IF_CITY_DIRECT =1 and T4.STATE =3 then 1 else T4.STATE end) IN (").append(stateSb.substring(1)).append(")");
            }else if(twoLevelAudit){
                sb.append(" AND T4.STATE IN (").append(stateSb.substring(1)).append(")");
            }
        }

        String h2 = "SELECT COUNT(*) FROM (" + sb+")";
        String h1 = "SELECT * FROM (" + sb+") M ORDER BY (CASE  WHEN M.STATE =6 THEN -1  WHEN M.STATE =4 THEN -2  WHEN M.STATE =2 THEN -3 WHEN M.STATE = 0 OR M.STATE = 0.5 THEN M.STATE ELSE 1 END), M.ZONE_GB, M.CRPT_NAME, M.PERSON_NAME, M.RCV_DATE";
        return new String[] { h1, h2 };
    }

    @Override
    public void processData(List<?> list) {
        try {
            if (null != list && list.size() > 0) {
                List<Object[]> result = (List<Object[]>) list;
                for (Object[] obj : result) {
                    if (null == obj[8] || null == obj[10]) {
                        continue;
                    }
                    String state = obj[10].toString();
                    Date date = (Date) obj[8];
                    if ("0".equals(state) || "0.5".equals(state) || "2".equals(state) || "4".equals(state)|| "6".equals(state)) {
                        obj[11] = "1";
                        obj[12] = calLimitTime(date);
                    } else {
                        obj[11] = "0";
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 计算是否超期，并返回剩余天数
     *
     * @param sDate 日期
     * @return 剩余天数
     */
    protected int calLimitTime(Date sDate) {
        if (null == sDate) {
            return -1;
        }
        //剩余天数
        int day = HolidayUtil.calRemainingDate(sDate, new Date(), yszybNostdTime);
        if (day == 0) {
            return -1;
        }
        return day;
    }
    @Override
    public void addInit() {
        this.tdZwYszybRpt = new TdZwYszybRpt();
        this.tdZwBgkLastSta = new TdZwBgkLastSta();

        TdTjBhk tdTjBhk = this.commService.find(TdTjBhk.class, this.relBhkId);
        if(null != tdTjBhk) {
            this.tdZwYszybRpt.setRelBhkId(tdTjBhk.getRid());
            this.tdZwYszybRpt.setPersonnelName(StringUtils.objectToString(tdTjBhk.getPersonName()));
            this.tdZwYszybRpt.setIdc(StringUtils.objectToString(tdTjBhk.getIdc()));
            this.tdZwYszybRpt.setLinktel(StringUtils.objectToString(tdTjBhk.getLnktel()));
            this.tdZwYszybRpt.setCrptName(tdTjBhk.getCrptName());
            //证件类型
            this.tdZwYszybRpt.setFkByCardTypeId(tdTjBhk.getFkByCardTypeId());
            if(null != tdTjBhk.getTbTjCrpt() && null != tdTjBhk.getTbTjCrpt().getRid()) {

                this.tdZwYszybRpt.setFkByCrptId(tdTjBhk.getTbTjCrpt());
                Integer crptRid = tdTjBhk.getTbTjCrpt().getRid();
                Integer unitRid = tdTjBhk.getTbTjSrvorg().getTsUnit().getRid();
                TbTjCrptIndepend crptIndepend = this.hethStaQueryServiceImpl.findTbTjCrptIndependByBusType(1, crptRid, unitRid);
                if (ObjectUtil.isNotEmpty(crptIndepend)) {
                    this.tdZwYszybRpt.setSafeposition(crptIndepend.getLinkman2());
                    this.tdZwYszybRpt.setSafephone(crptIndepend.getLinkphone2());
                }

                this.tdZwYszybRpt.setCreditCode(tdTjBhk.getTbTjCrpt().getInstitutionCode());
                this.tdZwYszybRpt.setAddress(tdTjBhk.getTbTjCrpt().getAddress());
                this.tdZwYszybRpt.setPostcode(tdTjBhk.getTbTjCrpt().getPostCode());
                if(null != tdTjBhk.getTbTjCrpt().getTsSimpleCodeByEconomyId() && null != tdTjBhk.getTbTjCrpt().getTsSimpleCodeByEconomyId().getRid()) {
                    this.tdZwYszybRpt.setFkByEconomyId(tdTjBhk.getTbTjCrpt().getTsSimpleCodeByEconomyId());
                }
                if(null != tdTjBhk.getTbTjCrpt().getTsSimpleCodeByIndusTypeId() && null != tdTjBhk.getTbTjCrpt().getTsSimpleCodeByIndusTypeId().getRid()) {
                    this.tdZwYszybRpt.setFkByIndusTypeId(tdTjBhk.getTbTjCrpt().getTsSimpleCodeByIndusTypeId());
                }
                if(null != tdTjBhk.getTbTjCrpt().getTsSimpleCodeByCrptSizeId() && null != tdTjBhk.getTbTjCrpt().getTsSimpleCodeByCrptSizeId().getRid()) {
                    this.tdZwYszybRpt.setFkByCrptSizeId(tdTjBhk.getTbTjCrpt().getTsSimpleCodeByCrptSizeId());
                }
                if(null != tdTjBhk.getTbTjCrpt().getTsZoneByZoneId() && null != tdTjBhk.getTbTjCrpt().getTsZoneByZoneId().getRid()) {
                    this.tdZwYszybRpt.setFkByZoneId(tdTjBhk.getTbTjCrpt().getTsZoneByZoneId());
                    //新增设置报告卡编号
                    String aa = commService.getAutoCode("HETH_YSZYB_RPT_CODE", null);
                    this.tdZwYszybRpt.setRptNo("YS"+tdTjBhk.getTbTjCrpt().getTsZoneByZoneId().getZoneGb()+DateUtils.formatDate(new Date(),"yyyyMMdd")+aa);
                }
            }
            if(tdTjBhk.getFkByEntrustCrptId()!=null && tdTjBhk.getFkByEntrustCrptId().getRid()!=null){
                TbTjCrpt empCrpt = tdTjBhk.getFkByEntrustCrptId();
                this.tdZwYszybRpt.setFkByEmpCrptId(empCrpt);
                this.tdZwYszybRpt.setEmpCrptName(empCrpt.getCrptName());
                this.tdZwYszybRpt.setEmpCreditCode(empCrpt.getInstitutionCode());
                if(empCrpt.getTsSimpleCodeByCrptSizeId()!=null && empCrpt.getTsSimpleCodeByCrptSizeId().getRid()!=null){
                    this.tdZwYszybRpt.setFkByEmpCrptSizeId(empCrpt.getTsSimpleCodeByCrptSizeId());
                }
                if(empCrpt.getTsSimpleCodeByIndusTypeId()!=null && empCrpt.getTsSimpleCodeByIndusTypeId().getRid()!=null){
                    this.tdZwYszybRpt.setFkByEmpIndusTypeId(empCrpt.getTsSimpleCodeByIndusTypeId());
                }
                if(empCrpt.getTsZoneByZoneId()!=null && empCrpt.getTsZoneByZoneId().getRid()!=null){
                    rptZone =empCrpt.getTsZoneByZoneId();
                    this.tdZwYszybRpt.setFkByEmpZoneId(rptZone);
                }
                if(empCrpt.getTsSimpleCodeByEconomyId()!=null && empCrpt.getTsSimpleCodeByEconomyId().getRid()!=null){
                    this.tdZwYszybRpt.setFkByEmpEconomyId(empCrpt.getTsSimpleCodeByEconomyId());
                }
            }
            if(StringUtils.isNotBlank(tdTjBhk.getSex())) {
                if("男".equals(tdTjBhk.getSex())) {
                    this.tdZwYszybRpt.setSex(1);
                } else {
                    this.tdZwYszybRpt.setSex(2);
                }
            }
            this.tdZwYszybRpt.setHarmStartDate(tdTjBhk.getHarmStartDate());
            //发现单位
            tdZwYszybRpt.setDiscoveryUnit(tjorginfo.getOrgName());
            tdZwYszybRpt.setDiscoveryRespPsn(tjorginfo.getLinkMan());
            if(null != tdTjBhk.getBrth()) {
                this.tdZwYszybRpt.setBirthday(tdTjBhk.getBrth());
            }

            //是否化学
            ifChemical = false;
            if(null != occdiseId){
                TsSimpleCode code =commService.findTsSimpleCodeByRid(occdiseId);
                if(null != code){
                    tdZwYszybRpt.setFkByOccDiseid(code);
                    tdZwYszybRpt.setYszybName(code.getCodeName());
                    tdZwYszybRpt.setYszybTypeName(code.getCodeDesc());
                    if("1".equals(code.getExtendS1())){
                        ifChemical = true;
                    }
                }

            }

            //工种
            ifOtherWork =false;
            if(null != tdTjBhk.getFkByWorkTypeId()){
                if("1".equals(tdTjBhk.getFkByWorkTypeId().getExtendS1())){
                    ifOtherWork =true;
                }
                tdZwYszybRpt.setFkByWorkTypeId(tdTjBhk.getFkByWorkTypeId());
                tdZwYszybRpt.setWorkOther(tdTjBhk.getWorkOther());
            }else{
                tdZwYszybRpt.setFkByWorkTypeId(new TsSimpleCode());
            }


            List<TdTjSupoccdiselist> tdTjSupoccdiselistList = this.service.findSupoccdiselistListByBhkId(this.relBhkId,occdiseId);
            StringBuilder sb1 = new StringBuilder();
            StringBuilder sb2 = new StringBuilder();
            List<TdZwYszybTchBadrsn> badrsns = new ArrayList<>();
            for(TdTjSupoccdiselist tdTjSupoccdiselist : tdTjSupoccdiselistList) {
                if(null == tdTjSupoccdiselist.getTsSimpleCodeByOccDiseid() || null == tdTjSupoccdiselist.getTsSimpleCodeByOccDiseid().getRid()
                        || null == tdTjSupoccdiselist.getTsSimpleCodeByBadrsnId() || null == tdTjSupoccdiselist.getTsSimpleCodeByBadrsnId().getRid()) {
                    continue;
                }
                TdZwYszybTchBadrsn badrsn = new TdZwYszybTchBadrsn();
                badrsn.setFkByMainId(tdZwYszybRpt);
                badrsn.setFkByBadrsnId(tdTjSupoccdiselist.getTsSimpleCodeByBadrsnId());
                badrsn.setCreateDate(new Date());
                badrsn.setCreateManid(sessionData.getUser().getRid());
                badrsns.add(badrsn);
                sb1.append(",").append(tdTjSupoccdiselist.getTsSimpleCodeByOccDiseid().getCodeName());
                sb2.append(",").append(tdTjSupoccdiselist.getTsSimpleCodeByBadrsnId().getCodeName());
            }
            tdZwYszybRpt.setBadrsnList(badrsns);

            if(StringUtils.isNotBlank(sb2.toString())) {
                this.tdZwYszybRpt.setTchBadrsns(sb2.toString().substring(1));
            }
            this.tdZwYszybRpt.setAnalyWork(StringUtils.objectToString(tdTjBhk.getWorkName()));
            if(null != tdTjBhk.getTchbadrsntim()) {
                this.tdZwYszybRpt.setTchWorkYear(tdTjBhk.getTchbadrsntim().intValue());
            }
            if(null != tdTjBhk.getTchbadrsnmonth()) {
                this.tdZwYszybRpt.setTchWorkMonth(tdTjBhk.getTchbadrsnmonth().intValue());
            }
            if(null != tdTjBhk.getJdgdat()) {
                this.tdZwYszybRpt.setFindDate(tdTjBhk.getJdgdat());
            }


        }
        TsSimpleCode tsSimpleCode = this.service.findSimpleCodeByCodeTypeNameAndExtendS1("5316", "1");
        if(null != tsSimpleCode) {
            this.tdZwYszybRpt.setFkBySourceId(tsSimpleCode);
        }

        // this.tdZwYszybRpt.setFkByFillUnitId(this.tjorginfo);
        this.tdZwYszybRpt.setFkRptUnitId(Global.getUser().getTsUnit());
        this.tdZwYszybRpt.setUnitRespPsn(StringUtils.objectToString(this.tjorginfo.getOrgFz()));
        this.tdZwYszybRpt.setFillFormPsn(this.sessionData.getUser().getUsername());
        this.tdZwYszybRpt.setFillLink(this.sessionData.getUser().getMbNum());
        this.tdZwYszybRpt.setFillDate(new Date());
        this.tdZwYszybRpt.setRptYear(this.currentYear);
        this.tdZwYszybRpt.setDelMark(0);
        this.tdZwYszybRpt.setCreateDate(new Date());
        this.tdZwYszybRpt.setCreateManid(this.sessionData.getUser().getRid());

        //报告信息
        this.tdZwYszybRpt.setRptPsn(this.sessionData.getUser().getUsername());
        this.tdZwYszybRpt.setRptLink(this.sessionData.getUser().getMbNum());
        this.tdZwYszybRpt.setRptDate(new Date());

        this.otherSourceDisable = 0;

        this.tdZwBgkLastSta.setCartType(2);
        this.tdZwBgkLastSta.setState(0);
        if(receiveDate ==1){
            this.tdZwBgkLastSta.setOrgRcvDate(tdTjBhk.getRptPrintDate());
        }else{
            this.tdZwBgkLastSta.setOrgRcvDate(tdTjBhk.getBhkDate());
        }

        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.execute("disabledInput('false','writ_yszyb')");
    }

    public void selectWorkTypeAction(){
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("width", 680);
        options.put("contentWidth", 625);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add("工种");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add("5502");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        paramList.add("true");
        paramMap.put("ifAllSelect", paramList);
        paramList = new ArrayList<String>();
        paramList.add("false");
        paramMap.put("ifShowFirstCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeRadioSelectListNew", options, paramMap);
    }

    /**
     * @MethodName: onWorkTypeSearch
     * @Description:
     * @Param: [event]
     * @Return: void
     * @Author: maox
     * @Date: 2020-07-08
     **/
    public void onWorkTypeSearch(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            TsSimpleCode code = selectedMap.get("selectPro") == null ? null
                    : (TsSimpleCode) selectedMap.get("selectPro");
            code = commService.findTsSimpleCodeByRid(code.getRid());
            tdZwYszybRpt.setFkByWorkTypeId(code);
            if("1".equals(code.getExtendS1())){
                ifOtherWork = true;
            }else{
                ifOtherWork = false;
                this.tdZwYszybRpt.setWorkName(null);
            }
        }
        /*RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update(":tabView:editForm:work')");*/
    }

    /**
     * @MethodName: clearWorkType
     * @Description: 清除工种
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-07-08
     **/
    public void clearWorkType(){
        tdZwYszybRpt.setFkByWorkTypeId(new TsSimpleCode());
        ifOtherWork = false;
        tdZwYszybRpt.setWorkOther(null);
         RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update(":tabView:editForm:work')");
    }

    @Override
    public void viewInit() {
        this.modInit();
    }

    @Override
    public void modInit() {
        this.otherSourceDisable = 0;
        this.tdZwYszybRpt = this.commService.find(TdZwYszybRpt.class, this.rid);
        this.tdZwBgkLastSta = this.service.findTdZwBgkLastStaInfoByBusIdAndCardType(this.rid, 2);

        this.tdZwYszybRpt.setModifyDate(new Date());
        this.tdZwYszybRpt.setModifyManid(this.sessionData.getUser().getRid());
        if(null != this.tdZwYszybRpt.getFkBySourceId() && null != this.tdZwYszybRpt.getFkBySourceId().getRid()) {
            if(this.tdZwYszybRpt.getFkBySourceId().getExtendS1().equals("9")) {
                this.otherSourceDisable = 1;
            }
        }

        if(null != tdZwYszybRpt.getFkByEmpCrptId() && null !=tdZwYszybRpt.getFkByEmpCrptId().getTsZoneByZoneId()){
            rptZone = tdZwYszybRpt.getFkByEmpZoneId();
            ifCityDirect = false;
            if("1".equals(rptZone.getIfCityDirect())){
                ifCityDirect = true;
            }
        }

        ifChemical = false;
        if(null != occdiseId){
            TsSimpleCode code =commService.findTsSimpleCodeByRid(occdiseId);
            if(null != code){
                tdZwYszybRpt.setYszybName(code.getCodeName());
                tdZwYszybRpt.setYszybTypeName(code.getCodeDesc());
                if("1".equals(code.getExtendS1())){
                    ifChemical = true;
                }
            }

        }

        //工种
        ifOtherWork =false;
        String workName="";
        if(null != tdZwYszybRpt.getFkByWorkTypeId()){
            workName = tdZwYszybRpt.getFkByWorkTypeId().getCodeName();
            if("1".equals(tdZwYszybRpt.getFkByWorkTypeId().getExtendS1())){
                ifOtherWork =true;
                workName=workName+"（"+tdZwYszybRpt.getWorkOther()+")";
            }
        }else{
            tdZwYszybRpt.setFkByWorkTypeId(new TsSimpleCode());
        }
        tdZwYszybRpt.setWorkName(workName);

        if(StringUtils.isNotBlank(tdZwYszybRpt.getAnnexPath())){
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("disabledInput('true','writ_yszyb')");
        }else{
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("disabledInput('false','writ_yszyb')");
        }

        this.tdZwBgkLastSta.setModifyDate(new Date());
        this.tdZwBgkLastSta.setModifyManid(this.sessionData.getUser().getRid());
    }

    /**
     * @Description : 暂存
     * @MethodAuthor: anjing
     * @Date : 2019/9/5 9:48
     **/
    @Override
    public void saveAction() {
        try {
            boolean flag = false;
            if(null != this.tdZwYszybRpt.getBirthday() && null != this.tdZwYszybRpt.getHarmStartDate()) {
                if(DateUtils.isAfter(this.tdZwYszybRpt.getHarmStartDate(),this.tdZwYszybRpt.getBirthday())){
                    JsfUtil.addErrorMessage("接害开始日期应大于出生日期！");
                    flag = true;
                }
            }
            if(null!= this.tdZwYszybRpt.getTchWorkMonth() && this.tdZwYszybRpt.getTchWorkMonth()>11){
                JsfUtil.addErrorMessage("专业工龄（月份）格式不正确！");
                flag = true;
            }
            if(null!= this.tdZwYszybRpt.getTchWorkDay() && this.tdZwYszybRpt.getTchWorkDay()>31){
                JsfUtil.addErrorMessage("专业工龄（日）格式不正确！");
                flag = true;
            }
            if(null!= this.tdZwYszybRpt.getTchWorkHour() && this.tdZwYszybRpt.getTchWorkHour()>23){
                JsfUtil.addErrorMessage("专业工龄（时）格式不正确！");
                flag = true;
            }
            if(null!= this.tdZwYszybRpt.getTchWorkMinute() && this.tdZwYszybRpt.getTchWorkMinute()>59){
                JsfUtil.addErrorMessage("专业工龄（分）格式不正确！");
                flag = true;
            }
            if(null != this.tdZwYszybRpt.getHarmStartDate() && null != this.tdZwYszybRpt.getFindDate()) {
                if(DateUtils.isDateAfter(this.tdZwYszybRpt.getHarmStartDate(), this.tdZwYszybRpt.getFindDate())){
                    JsfUtil.addErrorMessage("发现日期应大于等于接害开始日期！");
                    flag = true;
                }
            }
            if(null != this.tdZwYszybRpt.getFillDate() && null != this.tdZwYszybRpt.getFindDate()) {
                if(DateUtils.isDateAfter(this.tdZwYszybRpt.getFindDate(), this.tdZwYszybRpt.getFillDate())){
                    JsfUtil.addErrorMessage("填表日期应大于等于发现日期！");
                    flag = true;
                }
            }
            if(null != this.tdZwYszybRpt.getRptDate() && null != this.tdZwYszybRpt.getFillDate()) {
                if(DateUtils.isDateAfter(this.tdZwYszybRpt.getFillDate(), this.tdZwYszybRpt.getRptDate())){
                    JsfUtil.addErrorMessage("报告日期应大于等于填表日期！");
                    flag = true;
                }
            }
            if(flag){
                return;
            }
            if(null ==tdZwYszybRpt.getFkByWorkTypeId() || null ==tdZwYszybRpt.getFkByWorkTypeId().getRid()){
                tdZwYszybRpt.setFkByWorkTypeId(null);
            }
            this.zwReportCardService.saveOrUpdateYszybRptInfo(this.tdZwYszybRpt,
                    null==this.tdZwBgkLastSta.getRid()?this.tdZwBgkLastSta:null,
                    false,
                    StringUtils.isNotBlank(this.yszybNostdTime)? Integer.valueOf(this.yszybNostdTime) : null,rptZone);
            JsfUtil.addSuccessMessage("暂存成功！");
            if(null ==tdZwYszybRpt.getFkByWorkTypeId() || null ==tdZwYszybRpt.getFkByWorkTypeId().getRid()){
                tdZwYszybRpt.setFkByWorkTypeId(new TsSimpleCode());
            }
            this.searchAction();
        } catch (Exception e) {
            JsfUtil.addErrorMessage("保存失败！");
            e.printStackTrace();
        }
    }

    /**
     * @Description : 提交前必填项校验
     * @MethodAuthor: anjing
     * @Date : 2019/9/5 9:31
     **/
    private boolean verfyBySubmit() {
        boolean flag = true;

        if(ifChemical){
            if(null == this.tdZwYszybRpt.getZyPoisonType()){
                JsfUtil.addErrorMessage("当疑似职业病种类为职业性化学中毒时，请选择职业性化学中毒分类！");
                flag = false;
            }
        }
        if(null == this.tdZwYszybRpt.getHarmStartDate()){
            JsfUtil.addErrorMessage("接害开始日期不能为空！");
            flag = false;
        }
        if(null != this.tdZwYszybRpt.getBirthday() && null != this.tdZwYszybRpt.getHarmStartDate()) {
            if(DateUtils.isAfter(this.tdZwYszybRpt.getHarmStartDate(),this.tdZwYszybRpt.getBirthday())){
                JsfUtil.addErrorMessage("接害开始日期应大于出生日期！");
                flag = false;
            }
        }
        if(null == this.tdZwYszybRpt.getTchWorkYear()||null == this.tdZwYszybRpt.getTchWorkMonth()){
            JsfUtil.addErrorMessage("专业工龄的年月都不能为空！");
            flag = false;
        }
        if(null!= this.tdZwYszybRpt.getTchWorkMonth() && this.tdZwYszybRpt.getTchWorkMonth()>11){
            JsfUtil.addErrorMessage("专业工龄（月份）格式不正确！");
            flag = false;
        }
        if(null!= this.tdZwYszybRpt.getTchWorkDay() && this.tdZwYszybRpt.getTchWorkDay()>31){
            JsfUtil.addErrorMessage("专业工龄（日）格式不正确！");
            flag = false;
        }
        if(null!= this.tdZwYszybRpt.getTchWorkHour() && this.tdZwYszybRpt.getTchWorkHour()>23){
            JsfUtil.addErrorMessage("专业工龄（时）格式不正确！");
            flag = false;
        }
        if(null!= this.tdZwYszybRpt.getTchWorkMinute() && this.tdZwYszybRpt.getTchWorkMinute()>59){
            JsfUtil.addErrorMessage("专业工龄（分）格式不正确！");
            flag = false;
        }
        if(null == this.tdZwYszybRpt.getFkByWorkTypeId() || null ==this.tdZwYszybRpt.getFkByWorkTypeId().getRid()){
            JsfUtil.addErrorMessage("统计工种不能为空！");
            flag = false;
        }else if(ifOtherWork && StringUtils.isBlank(this.tdZwYszybRpt.getWorkOther())){
            JsfUtil.addErrorMessage("工种其他不能为空！");
            flag = false;
        }
        if(StringUtils.isBlank(this.tdZwYszybRpt.getDiscoveryRespPsn())){
            JsfUtil.addErrorMessage("发现单位负责人不能为空！");
            flag = false;
        }

        if(StringUtils.isBlank(this.tdZwYszybRpt.getFillFormPsn())) {
            JsfUtil.addErrorMessage("填表人不能为空！");
            flag = false;
        }
        if(StringUtils.isBlank(this.tdZwYszybRpt.getFillLink())) {
            JsfUtil.addErrorMessage("填表人联系电话不能为空！");
            flag = false;
        }
        if(StringUtils.isNotBlank(this.tdZwYszybRpt.getFillLink())) {
            if(!this.tdZwYszybRpt.getFillLink().matches(Constants.PHONE_MOBILE_REGEX)){
                JsfUtil.addErrorMessage("填表人联系电话格式错误！");
                flag = false;
            }
        }
        if(null == this.tdZwYszybRpt.getFillDate()) {
            JsfUtil.addErrorMessage("填表日期不能为空！");
            flag = false;
        }
        if(null != this.tdZwYszybRpt.getHarmStartDate() && null != this.tdZwYszybRpt.getFindDate()) {
            if(DateUtils.isDateAfter(this.tdZwYszybRpt.getHarmStartDate(), this.tdZwYszybRpt.getFindDate())){
                JsfUtil.addErrorMessage("发现日期应大于等于接害开始日期！");
                flag = false;
            }
        }
        if(null != this.tdZwYszybRpt.getFillDate() && null != this.tdZwYszybRpt.getFindDate()) {
            if(DateUtils.isDateAfter(this.tdZwYszybRpt.getFindDate(), this.tdZwYszybRpt.getFillDate())){
                JsfUtil.addErrorMessage("填表日期应大于等于发现日期！");
                flag = false;
            }
        }

        if(StringUtils.isBlank(this.tdZwYszybRpt.getRptPsn())) {
            JsfUtil.addErrorMessage("报告人不能为空！");
            flag = false;
        }
        if(StringUtils.isBlank(this.tdZwYszybRpt.getRptLink())) {
            JsfUtil.addErrorMessage("报告人联系电话不能为空！");
            flag = false;
        }
        if(StringUtils.isNotBlank(this.tdZwYszybRpt.getRptLink())) {
            if(!this.tdZwYszybRpt.getRptLink().matches(Constants.PHONE_MOBILE_REGEX)){
                JsfUtil.addErrorMessage("报告人联系电话格式错误！");
                flag = false;
            }
        }
        if(null == this.tdZwYszybRpt.getRptDate()) {
            JsfUtil.addErrorMessage("报告日期不能为空！");
            flag = false;
        }
        if(null != this.tdZwYszybRpt.getRptDate() && null != this.tdZwYszybRpt.getFillDate()) {
            if(DateUtils.isDateAfter(this.tdZwYszybRpt.getFillDate(), this.tdZwYszybRpt.getRptDate())){
                JsfUtil.addErrorMessage("报告日期应大于等于填表日期！");
                flag = false;
            }
        }
        return flag;
    }

    /**
     * <p>方法描述：设计文书</p>
     * @MethodAuthor rcj,2019年9月5日,designWritReport
     * */
    public void designWritReport() {
        if(!verfyBySubmit() || !veryWritsort()){
            return;
        }
        this.service.upsertEntity(this.tdZwYszybRpt);
        this.fastReportBean = new FastReportBean(this, "HETH_2031");
        fastReportBean.designAction();
        RequestContext.getCurrentInstance().execute("frpt_design();");
    }


    /**
     * <p>方法描述：制作文书</p>
     * @MethodAuthor rcj,2019年9月5日,buildWritReport
     * */
    public void buildWritReport() {
        if(!verfyBySubmit() || !veryWritsort()){
            return;
        }
        this.zwReportCardService.saveOrUpdateYszybRptInfo(this.tdZwYszybRpt,
                null==this.tdZwBgkLastSta.getRid()?this.tdZwBgkLastSta:null,
                false,
                StringUtils.isNotBlank(this.yszybNostdTime)? Integer.valueOf(this.yszybNostdTime) : null,rptZone);
        showShadeTip();
        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update("tabView:editForm:writ_yszyb_panel");
        currentInstance.execute("windowScrollTop()");
    }

    /**
     * <p>方法描述：制作文书加载</p>
     * @MethodAuthor rcj,2019年9月5日,buildWritReport
     * */
    public void showShadeTip(){
        RequestContext.getCurrentInstance().execute("showShadeTip()");
    }

    /**
     * <p>方法描述：当前文书验证</p>
     * @MethodAuthor qrr,2018年4月26日,veryWritsort
     * */
    private boolean veryWritsort(){
        boolean flag = true;
        writsort = getWritsort("2031");
        //文书未维护
        if (null==writsort) {
            JsfUtil.addErrorMessage("请在文书类型管理中维护该文书的相关信息！");
            return false;
        }
        if (null==writsort.getFkByRptTemplId()) {
            JsfUtil.addErrorMessage("请在文书类型管理中维护该文书的报表模板！");
            return false;
        }
        return flag;
    }

    public  TbZwWritsort getWritsort(String writCode){
        if (StringUtils.isNotBlank(writCode)) {
            String hql = "select t from TbZwWritsort t where t.writCode = "
                    + writCode;
            TbZwWritsort writsort = service.findOneByHql(hql,
                    TbZwWritsort.class);
            return writsort;
        }
        return null;

    }

    /**
     * <p>方法描述：制作文书</p>
     * @MethodAuthor rcj,2018年12月24日,tobuildWritReport
     * */
    public void tobuildWritReport(){
        //根据模板生成附件1、文书编号2、封装签章工具2、上传到本地
        this.fastReportBean = new FastReportBean(this, "HETH_2031");
        SysReturnPojo returnPojo = fastReportBean.showPDFReportAction("");
        String type = returnPojo.getType();
        String mess = returnPojo.getMess();

        if (!"00".equals(type)) {
            System.out.println("type："+type+"！！！！！mess："+mess);
            JsfUtil.addErrorMessage("生成文书失败，请稍后尝试！");
            return;
        }
        //附件地址
        tdZwYszybRpt.setAnnexPath(mess);
        //保存已制作、文书附件
        this.service.upsertEntity(this.tdZwYszybRpt);
        JsfUtil.addSuccessMessage("制作成功！");
        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.execute("disabledInput('true','writ_yszyb')");
    }

    /**
     * <p>方法描述：</p>
     *
     *
     * @MethodAuthor rcj, 2019-9-5,delMadedwrit
     */
    public void delMadedwrit() {
        try {
            tdZwYszybRpt.setAnnexPath(null);
            service.upsertEntity(tdZwYszybRpt);
            JsfUtil.addSuccessMessage("删除成功！");
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("disabledInput('false','writ_yszyb')");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }


    /**
     * <p>方法描述：附件查看</p>
     *
     * @MethodAuthor rcj, 2019年9月5日, showReportAction
     */
    public void toAnnexView() {
        if (StringUtils.isBlank(tdZwYszybRpt.getAnnexPath())) {
            JsfUtil.addErrorMessage("附件已经被删除，请重新上传！");
            return;
        }
        RequestContext.getCurrentInstance().execute(
                "window.open('" + FileUtils.previewFile(tdZwYszybRpt.getAnnexPath(),
                        "") + "')");
    }

    /**
     * @Description : 清空地区
     * @MethodAuthor: anjing
     * @Date : 2019/9/16 13:40
     **/
    public void clearSearchZone() {
        this.searchZoneCode = null;
        this.searchZoneName = null;
    }

    @Override
    public FastReportBean getFastReportBean() {
        return fastReportBean;
    }

    @Override
    public List<FastReportData> supportFastReportDataSet() {
        return RptCardTempCommUtil.packageDatas("HETH_2031", tdZwYszybRpt.getRid());
    }

    @Override
    public List<FastReportDataRef> supportFastReportDataRef() {
        return null;
    }
    /**
     * @Description : 提交
     * @MethodAuthor: anjing
     * @Date : 2019/9/5 9:48
     **/
    public void submitAction() {
        try {
            if(!verfyBySubmit()) {
                return;
            }
            if (StringUtils.isBlank(this.tdZwYszybRpt.getAnnexPath())) {
                JsfUtil.addErrorMessage("请先制作文书！");
                return;
            }
            if(twoLevelAudit){
                //
                if("1".equals(rptZone.getIfProvDirect())){
                    //省直属
                    this.tdZwBgkLastSta.setProRcvDate(new Date());
                    this.tdZwBgkLastSta.setState(5);
                }else{
                    //非省直属
                    this.tdZwBgkLastSta.setCountyRcvDate(new Date());
                    this.tdZwBgkLastSta.setState(1);
                }
            }else if(threeLevelAudit){
                if ("1".equals(rptZone.getIfCityDirect())) {
                    this.tdZwBgkLastSta.setCityRcvDate(new Date());
                    this.tdZwBgkLastSta.setState(3);
                } else {
                    this.tdZwBgkLastSta.setCountyRcvDate(new Date());
                    this.tdZwBgkLastSta.setState(1);
                }
            }
            this.zwReportCardService.saveOrUpdateYszybRptInfo(this.tdZwYszybRpt, this.tdZwBgkLastSta, true,
                    StringUtils.isNotBlank(this.yszybNostdTime)? Integer.valueOf(yszybNostdTime):null,rptZone);
            this.backAction();
            this.searchAction();
            RequestContext.getCurrentInstance().update("tabView");
            JsfUtil.addSuccessMessage("提交成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("提交失败！");
            e.printStackTrace();
        }
    }

    /**
     * @Description : 标删
     * @MethodAuthor: anjing
     * @Date : 2019/9/5 9:51
     **/
    public void deleteAction() {
        try {
            TdZwYszybRpt tdZwYszybRpt = this.service.find(TdZwYszybRpt.class, this.rid);
            if(null != tdZwYszybRpt) {
                tdZwYszybRpt.setDelMark(1);
                this.service.upsertEntity(tdZwYszybRpt);
            }
            JsfUtil.addSuccessMessage("删除成功！");
            this.searchAction();
        } catch (Exception e) {
            JsfUtil.addErrorMessage("删除失败！");
            e.printStackTrace();
        }
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchPsnName() {
        return searchPsnName;
    }

    public void setSearchPsnName(String searchPsnName) {
        this.searchPsnName = searchPsnName;
    }

    public String getSearchIdc() {
        return searchIdc;
    }

    public void setSearchIdc(String searchIdc) {
        this.searchIdc = searchIdc;
    }

    public String getSearchYszybName() {
        return searchYszybName;
    }

    public void setSearchYszybName(String searchYszybName) {
        this.searchYszybName = searchYszybName;
    }

    public Date getSearchFindBdate() {
        return searchFindBdate;
    }

    public void setSearchFindBdate(Date searchFindBdate) {
        this.searchFindBdate = searchFindBdate;
    }

    public Date getSearchFindEdate() {
        return searchFindEdate;
    }

    public void setSearchFindEdate(Date searchFindEdate) {
        this.searchFindEdate = searchFindEdate;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Integer getRelBhkId() {
        return relBhkId;
    }

    public void setRelBhkId(Integer relBhkId) {
        this.relBhkId = relBhkId;
    }

    public TdZwYszybRpt getTdZwYszybRpt() {
        return tdZwYszybRpt;
    }

    public void setTdZwYszybRpt(TdZwYszybRpt tdZwYszybRpt) {
        this.tdZwYszybRpt = tdZwYszybRpt;
    }

    public int getCurrentYear() {
        return currentYear;
    }

    public void setCurrentYear(int currentYear) {
        this.currentYear = currentYear;
    }

    public Integer getOtherSourceDisable() {
        return otherSourceDisable;
    }

    public void setOtherSourceDisable(Integer otherSourceDisable) {
        this.otherSourceDisable = otherSourceDisable;
    }

    public TdZwTjorginfoComm getTjorginfo() {
        return tjorginfo;
    }

    public void setTjorginfo(TdZwTjorginfoComm tjorginfo) {
        this.tjorginfo = tjorginfo;
    }

    public TdZwBgkLastSta getTdZwBgkLastSta() {
        return tdZwBgkLastSta;
    }

    public void setTdZwBgkLastSta(TdZwBgkLastSta tdZwBgkLastSta) {
        this.tdZwBgkLastSta = tdZwBgkLastSta;
    }

    public String getYszybNostdTime() {
        return yszybNostdTime;
    }

    public void setYszybNostdTime(String yszybNostdTime) {
        this.yszybNostdTime = yszybNostdTime;
    }

    public String getIfDesign() {
        return ifDesign;
    }

    public void setIfDesign(String ifDesign) {
        this.ifDesign = ifDesign;
    }

    public Integer getTag() {
        return tag;
    }

    public void setTag(Integer tag) {
        this.tag = tag;
    }

    public boolean isIfshowdeadline() {
        return ifshowdeadline;
    }

    public void setIfshowdeadline(boolean ifshowdeadline) {
        this.ifshowdeadline = ifshowdeadline;
    }

    public String getDateName() {
        return dateName;
    }

    public void setDateName(String dateName) {
        this.dateName = dateName;
    }

    public Integer getOccdiseId() {
        return occdiseId;
    }

    public void setOccdiseId(Integer occdiseId) {
        this.occdiseId = occdiseId;
    }

    public boolean isIfChemical() {
        return ifChemical;
    }

    public void setIfChemical(boolean ifChemical) {
        this.ifChemical = ifChemical;
    }

    public boolean isIfOtherWork() {
        return ifOtherWork;
    }

    public void setIfOtherWork(boolean ifOtherWork) {
        this.ifOtherWork = ifOtherWork;
    }

    public Integer getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Integer receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getCityDirectCode() {
        return cityDirectCode;
    }

    public void setCityDirectCode(String cityDirectCode) {
        this.cityDirectCode = cityDirectCode;
    }

    public TsZone getRptZone() {
        return rptZone;
    }

    public void setRptZone(TsZone rptZone) {
        this.rptZone = rptZone;
    }

    public boolean isIfCityDirect() {
        return ifCityDirect;
    }

    public void setIfCityDirect(boolean ifCityDirect) {
        this.ifCityDirect = ifCityDirect;
    }
}
