package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-8-19
 */
@Entity
@Table(name = "TD_ZW_SRVORG_CARD_FHJC")
@SequenceGenerator(name = "TdZwSrvorgCardFhjc", sequenceName = "TD_ZW_SRVORG_CARD_FHJC_SEQ", allocationSize = 1)
public class TdZwSrvorgCardFhjc implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwSrvorgCard fkByMainId;
	private TsSimpleCode fkByTypeId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwSrvorgCardFhjc() {
	}

	public TdZwSrvorgCardFhjc(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSrvorgCardFhjc")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwSrvorgCard getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwSrvorgCard fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "TYPE_ID")			
	public TsSimpleCode getFkByTypeId() {
		return fkByTypeId;
	}

	public void setFkByTypeId(TsSimpleCode fkByTypeId) {
		this.fkByTypeId = fkByTypeId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}