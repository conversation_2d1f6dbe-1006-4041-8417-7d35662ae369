package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2023-8-14
 */
@Entity
@Table(name = "TD_TJ_CRPT_TASK")
@SequenceGenerator(name = "TdTjCrptTask", sequenceName = "TD_TJ_CRPT_TASK_SEQ", allocationSize = 1)
public class TdTjCrptTask implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TbTjCrpt fkByNewestCrptId;
    private String crptName;
    private TsZone fkByZoneId;
    private String creditCode;
    private String address;
    private String enrolAddress;
    private TsSimpleCode fkByIndusTypeId;
    private TsSimpleCode fkByEconomyId;
    private TsSimpleCode fkByCrptSizeId;
    private String linkMan;
    private String linkPhone;
    private Integer state;
    private TsUserInfo fkByOperPsnId;
    private Date operDate;
    private String errorMsg;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private List<TdTjCrptTaskSub> crptTaskSubList;

    public TdTjCrptTask() {
    }

    public TdTjCrptTask(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjCrptTask")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "NEWEST_CRPT_ID")
    public TbTjCrpt getFkByNewestCrptId() {
        return fkByNewestCrptId;
    }

    public void setFkByNewestCrptId(TbTjCrpt fkByNewestCrptId) {
        this.fkByNewestCrptId = fkByNewestCrptId;
    }

    @Column(name = "CRPT_NAME")
    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    @ManyToOne
    @JoinColumn(name = "ZONE_ID")
    public TsZone getFkByZoneId() {
        return fkByZoneId;
    }

    public void setFkByZoneId(TsZone fkByZoneId) {
        this.fkByZoneId = fkByZoneId;
    }

    @Column(name = "CREDIT_CODE")
    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    @Column(name = "ADDRESS")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Column(name = "ENROL_ADDRESS")
    public String getEnrolAddress() {
        return enrolAddress;
    }

    public void setEnrolAddress(String enrolAddress) {
        this.enrolAddress = enrolAddress;
    }

    @ManyToOne
    @JoinColumn(name = "INDUS_TYPE_ID")
    public TsSimpleCode getFkByIndusTypeId() {
        return fkByIndusTypeId;
    }

    public void setFkByIndusTypeId(TsSimpleCode fkByIndusTypeId) {
        this.fkByIndusTypeId = fkByIndusTypeId;
    }

    @ManyToOne
    @JoinColumn(name = "ECONOMY_ID")
    public TsSimpleCode getFkByEconomyId() {
        return fkByEconomyId;
    }

    public void setFkByEconomyId(TsSimpleCode fkByEconomyId) {
        this.fkByEconomyId = fkByEconomyId;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_SIZE_ID")
    public TsSimpleCode getFkByCrptSizeId() {
        return fkByCrptSizeId;
    }

    public void setFkByCrptSizeId(TsSimpleCode fkByCrptSizeId) {
        this.fkByCrptSizeId = fkByCrptSizeId;
    }

    @Column(name = "LINK_MAN")
    public String getLinkMan() {
        return linkMan;
    }

    public void setLinkMan(String linkMan) {
        this.linkMan = linkMan;
    }

    @Column(name = "LINK_PHONE")
    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }

    @Column(name = "STATE")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @ManyToOne
    @JoinColumn(name = "OPER_PSN_ID")
    public TsUserInfo getFkByOperPsnId() {
        return fkByOperPsnId;
    }

    public void setFkByOperPsnId(TsUserInfo fkByOperPsnId) {
        this.fkByOperPsnId = fkByOperPsnId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "OPER_DATE")
    public Date getOperDate() {
        return operDate;
    }

    public void setOperDate(Date operDate) {
        this.operDate = operDate;
    }

    @Column(name = "ERROR_MSG")
    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdTjCrptTaskSub> getCrptTaskSubList() {
        return this.crptTaskSubList;
    }

    public void setCrptTaskSubList(List<TdTjCrptTaskSub> crptTaskSubList) {
        this.crptTaskSubList = crptTaskSubList;
    }
}