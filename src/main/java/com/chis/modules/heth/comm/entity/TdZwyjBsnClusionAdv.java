package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-10-29
 */
@Entity
@Table(name = "TD_ZWYJ_BSN_CLUSION_ADV")
@SequenceGenerator(name = "TdZwyjBsnClusionAdv", sequenceName = "TD_ZWYJ_BSN_CLUSION_ADV_SEQ", allocationSize = 1)
public class TdZwyjBsnClusionAdv implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwyjBsnClusion fkByMainId;
	private TsSimpleCode fkByBhkrstId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjBsnClusionAdv() {
	}

	public TdZwyjBsnClusionAdv(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjBsnClusionAdv")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwyjBsnClusion getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwyjBsnClusion fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BHKRST_ID")			
	public TsSimpleCode getFkByBhkrstId() {
		return fkByBhkrstId;
	}

	public void setFkByBhkrstId(TsSimpleCode fkByBhkrstId) {
		this.fkByBhkrstId = fkByBhkrstId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}