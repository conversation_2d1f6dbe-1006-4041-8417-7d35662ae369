package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import org.primefaces.model.TreeNode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 职业卫生技术服务信息报送卡—职业病危害因素检测（超标岗位）
 *
 * <AUTHOR>
 * @createTime 2022-8-22
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_BADRSN_ITEM")
@SequenceGenerator(name = "TdZwOcchethBadrsnItem", sequenceName = "TD_ZW_OCCHETH_BADRSN_ITEM_SEQ", allocationSize = 1)
public class TdZwOcchethBadrsnItem implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwOcchethBpSub fkByMainId;
    private String postName;
    private String postNameShow;
    private TbYsjcRsnRelItemComm fkByItemId;

    private TsSimpleCode fkByIndexId;
    private BigDecimal rst;

    private TsSimpleCode fkByMsruntId;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    /**
     * 检测项目
     */
    private Integer itemId;
    /**
     * 检测指标
     */
    private Integer indexRid;
    /**
     * 计量单位
     */
    private String msruntName;

    /**
     * 计量单位特殊值
     */
    private TreeNode msruntTree;
    /**
     * 检测项目 名称
     */
    private String itermName;

    /**
     * 检测项目 码表序号
     */
    private Integer itemNumber;
    /**
     * 检测指标 码表序号
     */
    private Integer indexNumber;

    /**
     * 岗位/工种数
     */
    private Integer postNum;
    /**
     * 超标岗位数
     */
    private Integer num;
    /**
     * 接触人数
     */
    private Integer badrsnNum;
    /**
     * 危害因素名称
     */
    private String badrsnName;
    /**
     * 危害因素 合并行数
     */
    private Integer badrsnRowspan;
    /**
     * 危害因素+岗位 合并行数
     */
    private Integer postRowspan;
    /**
     * 危害因素+岗位+检测项目 合并行数
     */
    private Integer itemsRowspan;
    /**
     * 是否选择危害因素时 添加
     */
    private Boolean ifFirst;

    public TdZwOcchethBadrsnItem() {
    }

    public TdZwOcchethBadrsnItem(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethBadrsnItem")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwOcchethBpSub getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwOcchethBpSub fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @Column(name = "POST_NAME")
    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        setPostNameShow(postName);
        this.postName = postName;
    }

    @ManyToOne
    @JoinColumn(name = "ITEM_ID")
    public TbYsjcRsnRelItemComm getFkByItemId() {
        return fkByItemId;
    }

    public void setFkByItemId(TbYsjcRsnRelItemComm fkByItemId) {
        this.fkByItemId = fkByItemId;
    }

    @ManyToOne
    @JoinColumn(name = "INDEX_ID")
    public TsSimpleCode getFkByIndexId() {
        return fkByIndexId;
    }

    public void setFkByIndexId(TsSimpleCode fkByIndexId) {
        this.fkByIndexId = fkByIndexId;
    }

    @Column(name = "RST")
    public BigDecimal getRst() {
        return rst;
    }

    public void setRst(BigDecimal rst) {
        this.rst = rst;
    }

    @ManyToOne
    @JoinColumn(name = "MSRUNT_ID")
    public TsSimpleCode getFkByMsruntId() {
        return fkByMsruntId;
    }

    public void setFkByMsruntId(TsSimpleCode fkByMsruntId) {
        this.fkByMsruntId = fkByMsruntId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Transient
    public TreeNode getMsruntTree() {
        return msruntTree;
    }

    public void setMsruntTree(TreeNode msruntTree) {
        this.msruntTree = msruntTree;
    }

    @Transient
    public Integer getIndexRid() {
        return indexRid;
    }

    public void setIndexRid(Integer indexRid) {
        this.indexRid = indexRid;
    }

    @Transient
    public Integer getItemId() {
        return itemId;
    }

    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }

    @Transient
    public String getMsruntName() {
        return msruntName;
    }

    public void setMsruntName(String msruntName) {
        this.msruntName = msruntName;
    }

    @Transient
    public Integer getBadrsnRowspan() {
        return badrsnRowspan;
    }

    public void setBadrsnRowspan(Integer badrsnRowspan) {
        this.badrsnRowspan = badrsnRowspan;
    }

    @Transient
    public Integer getPostRowspan() {
        return postRowspan;
    }

    public void setPostRowspan(Integer postRowspan) {
        this.postRowspan = postRowspan;
    }

    @Transient
    public Integer getItemsRowspan() {
        return itemsRowspan;
    }

    public void setItemsRowspan(Integer itemsRowspan) {
        this.itemsRowspan = itemsRowspan;
    }

    @Transient
    public String getBadrsnName() {
        return badrsnName;
    }

    public void setBadrsnName(String badrsnName) {
        this.badrsnName = badrsnName;
    }

    @Transient
    public Integer getPostNum() {
        return postNum;
    }

    public void setPostNum(Integer postNum) {
        this.postNum = postNum;
    }

    @Transient
    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    @Transient
    public Integer getItemNumber() {
        return itemNumber;
    }

    public void setItemNumber(Integer itemNumber) {
        this.itemNumber = itemNumber;
    }

    @Transient
    public Integer getIndexNumber() {
        return indexNumber;
    }

    public void setIndexNumber(Integer indexNumber) {
        this.indexNumber = indexNumber;
    }

    @Transient
    public String getItermName() {
        return itermName;
    }

    public void setItermName(String itermName) {
        this.itermName = itermName;
    }

    @Transient
    public String getPostNameShow() {
        return postNameShow;
    }

    public void setPostNameShow(String postNameShow) {
        this.postNameShow = postNameShow;
    }

    @Transient
    public Boolean getIfFirst() {
        return ifFirst;
    }

    public void setIfFirst(Boolean ifFirst) {
        this.ifFirst = ifFirst;
    }

    @Transient
    public Integer getBadrsnNum() {
        return badrsnNum;
    }

    public void setBadrsnNum(Integer badrsnNum) {
        this.badrsnNum = badrsnNum;
    }
}