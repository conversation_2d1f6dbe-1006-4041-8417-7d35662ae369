package com.chis.modules.heth.comm.web;

import java.util.ArrayList;
import java.util.Date;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;

import com.chis.common.utils.DateUtils;

/**
 * <p>类描述：</p>
 * @ClassAuthor qrr,2021年1月5日,TdZwZdBadRsnInspectSearchListBean
 * */
@ManagedBean(name = "tdZwZdBadRsnInspectSearchListBean")
@ViewScoped
public class TdZwZdBadRsnInspectSearchListBean extends AbstractZdBadRsnCheckBean{
	private static final long serialVersionUID = 1L;
	
	public TdZwZdBadRsnInspectSearchListBean(){
		this.searchBhkBdate = DateUtils.getYearFirstDay(new Date());
		this.searchBhkEdate = DateUtils.getDateOnly(new Date());
		initSearchStates();
		this.searchAction();
	}
	/**
 	 * <p>方法描述：初始化查询状态</p>
 	 * @MethodAuthor qrr,2021年1月6日,initSearchStates
	 * */
	private void initSearchStates() {
		this.states = new String[]{"6"};
		this.stateList = new ArrayList<>();
		this.stateList.add(new SelectItem("1,7", "待初审"));//7代表市直属-待初审
		this.stateList.add(new SelectItem("0", "待复审"));//0代表非市直属-待复审
		this.stateList.add(new SelectItem("2", "复审退回"));
		this.stateList.add(new SelectItem("5", "待终审"));
		this.stateList.add(new SelectItem("4", "终审退回"));
		this.stateList.add(new SelectItem("6", "终审完成"));
	}
	@Override
	public String getOtherSearchCondition() {
		StringBuffer sb = new StringBuffer();
		// 状态
		StringBuffer stateSb = new StringBuffer();
        if (null != states && states.length > 0) {
            for (String state : states) {
                stateSb.append(",").append(state);
            }
        } else {
            if (null != stateList && stateList.size() > 0) {
                for (SelectItem item : stateList) {
                    stateSb.append(",").append(item.getValue());
                }
            }
        }
        sb.append(" AND (T1.STATE IN (").append(stateSb.substring(1)).append(") ");
        if(stateSb.toString().contains("1")){
        	sb.append(" or T1.STATE IS NULL");
        }
        if(stateSb.toString().contains("7")){//市直属-待初审
        	sb.append(" or (t5.if_city_direct=1 and (t1.STATE=3 or t1.STATE is null))");
        }
        if(stateSb.toString().contains("0")){//非市直属-待复审
        	sb.append(" or (nvl(t5.if_city_direct,0)=0 and t1.STATE=3)");
        }
        sb.append(")");
        return sb.toString();
	}
	@Override
	public String getType() {
		return "1";
	}
}
