package com.chis.modules.heth.comm.entity;

import org.hibernate.annotations.Sort;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 职业卫生技术服务信息报送卡—职业病危害因素检测
 *
 * <AUTHOR>
 * @createTime 2022-8-22
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_CARD_JC")
@SequenceGenerator(name = "TdZwOcchethCardJc", sequenceName = "TD_ZW_OCCHETH_CARD_JC_SEQ", allocationSize = 1)
public class TdZwOcchethCardJc implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwOcchethCard fkByMainId;
    private TbYsjcLimitValComm fkByBadrsnId;
    /**岗位/工种数*/
    private Integer postNum;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    /**超标岗位数*/
    private Integer num;
    /**
     * 职业病危害因素检测（超标岗位）
     */
    private List<TdZwOcchethCardJcSub> occhethCardJcSubList = new ArrayList<>();

    public TdZwOcchethCardJc() {
    }

    public TdZwOcchethCardJc(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethCardJc")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwOcchethCard getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwOcchethCard fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "BADRSN_ID")
    public TbYsjcLimitValComm getFkByBadrsnId() {
        return fkByBadrsnId;
    }

    public void setFkByBadrsnId(TbYsjcLimitValComm fkByBadrsnId) {
        this.fkByBadrsnId = fkByBadrsnId;
    }

    @Column(name = "POST_NUM")
    public Integer getPostNum() {
        return postNum;
    }

    public void setPostNum(Integer postNum) {
        this.postNum = postNum;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OrderBy("rid")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwOcchethCardJcSub> getOcchethCardJcSubList() {
        return occhethCardJcSubList;
    }

    public void setOcchethCardJcSubList(List<TdZwOcchethCardJcSub> occhethCardJcSubList) {
        this.occhethCardJcSubList = occhethCardJcSubList;
    }

    @Column(name = "NUM")
    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }
}