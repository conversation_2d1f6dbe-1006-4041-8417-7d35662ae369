package com.chis.modules.heth.comm.service;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import javax.persistence.Query;
import java.util.*;

/**
 * 职业卫生平台会话Bean
 * <p/>
 * 修改人：WJH 修改时间 ：2014-09-11<br/>
 * 修改内容：资质服务机构功能添加
 * <p/>
 * 修改人：LXK 修改时间 ：2014-09-03<br/>
 * 修改内容：项目组合功能添加
 * <p/>
 * 修改人：LXK 修改时间：2014-09-04<br/>
 * 修改内容：新增维护职业禁忌证标准，疑似职业病和询问症状
 * <p/>
 * 修改人：xt 修改时间：2014-09-05<br/>
 * 修改内容：升级包制作代码增加注释
 * <p/>
 * 修改人：lxk 修改时间：2014-09-09<br/>
 * 修改内容：新增清除业务提醒功能
 * <p/>
 * 修改人：lqq 修改时间：2014-09-10<br/>
 * 修改内容：新增项目组合与项目关系维护模块
 * <p/>
 * 修改人：xt 修改时间：2014-09-12<br/>
 * 修改内容：增量包去除重复数据
 *
 * 修改人：xt 修改时间：2014-09-15<br/>
 * 修改内容：升级包需要更新ID太多bug修正
 *
 * 修改人：LXK 修改时间：2014-09-16<br/>
 * 修改内容：查询项目组合，职业禁忌证，职业病，询问症状时过滤停用
 *
 * 修改人：LXK 修改时间：2014-09-17<br/>
 * 修改内容： 1、查询方法加无事务注解<br/>
 * 2、StringBuffer 替换成 StringBuilder<br/>
 * 3、层级缩进功能使用转换器<br/>
 *
 * 修改人：LXK 修改时间：2014-09-25<br/>
 * 修改内容：新增职业病诊断功能
 *
 * 修改人：LXK 修改时间：2014-09-28<br/>
 * 修改内容：新增职业病档案检索功能
 *
 * 修改人：lxk 修改时间：2014-09-30 修改内容：查询危害因素码表时去掉停用的
 *
 * <AUTHOR>
 * @createDate 2014年8月29日 上午11:17:27
 * @LastModify LuXuekun
 * @ModifyDate 2014年8月29日 上午11:17:27
 */
@Service
@Transactional(readOnly = false)
public class HethBaseCommServiceImpl extends AbstractTemplate {

    @Autowired
    private CommServiceImpl commService;

    @Transactional(readOnly = true)
    public TbTjCrpt findTbTjCrpt(Integer crptId){
        if( null !=  crptId){
            TbTjCrpt find = (TbTjCrpt) this.find(TbTjCrpt.class, crptId);
            return find;
        }
        return null;
    }

    /**
     * 验证标准方案是否存在
     *
     * @param badRsn
     *            危害因素
     * @param jobState
     *            在岗状态
     * @param mainstdId
     *            主表ID
     * @return 如果有重复数据则返回TRUE 否则返回flase
     * <AUTHOR>
     * @createDate 2014年9月2日 下午7:10:12
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月2日 下午7:10:12
     */
    @Transactional(readOnly = true)
    public boolean verfiyAk(Integer badRsn, Integer jobState, Integer mainstdId) {
        // 判断危害因素和在岗状态是否为空
        if (null != badRsn && null != jobState) {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT COUNT(*) FROM TB_ZWTJ_MAINSTD T WHERE T.BADRSN_ID='");
            sql.append(badRsn);
            sql.append("' AND T.WORK_STATEID='");
            sql.append(jobState);
            sql.append("'");
            // 修改时不状态自己
            if (null != mainstdId) {
                sql.append(" AND RID!='");
                sql.append(mainstdId);
                sql.append("'");
            }
            Object singleResult = em.createNativeQuery(sql.toString()).getSingleResult();
            // 不为空并且不为0
            if (null != singleResult && !"0".equals(singleResult.toString())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据标准主表ID查询还没有维护的项目组合
     *
     * @param mainstdId
     *            标准主表ID
     * @return 返回查询结果
     * <AUTHOR>
     * @createDate 2014年9月3日 下午3:50:58
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月3日 下午3:50:58
     */
    @Transactional(readOnly = true)
    public List<Object[]> getSelectItemList(Integer mainstdId) {
        if (null != mainstdId) {
            StringBuilder sql = new StringBuilder();
            // 查询出所有未添加项目合列表
            sql.append("SELECT T.RID,T.CODE_NO,T.CODE_NAME,T.CODE_LEVEL_NO,T.SPLSHT FROM TS_SIMPLE_CODE T");
            sql.append("  INNER JOIN TS_CODE_TYPE A ON T.CODE_TYPE_ID=A.RID");
            sql.append("  WHERE A.CODE_TYPE_NAME='5012' AND T.RID NOT IN(");
            sql.append("  SELECT ITEM_CMBID FROM TB_ZWTJ_SCHEME_ITEMS WHERE SCHEME_ID ='");
            sql.append(mainstdId);
            sql.append("' ) AND T.IF_REVEAL='1' ORDER BY T.CODE_LEVEL_NO");
            List<Object[]> resultList = em.createNativeQuery(sql.toString()).getResultList();
            return resultList;
        }
        return null;
    }

    /**
     * 删除子表记录，根据子表名和子表主键
     *
     * @param tableName
     *            子表名称
     * @param tableRid
     *            子表主键
     * <AUTHOR>
     * @createDate 2014年9月3日 下午8:01:21
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月3日 下午8:01:21
     */
    public void delGbz(String tableName, Integer tableRid) {
        if (null != tableName && !"".equals(tableName.trim()) && null != tableRid) {
            // 删除项目组合表
            StringBuilder sql = new StringBuilder();
            sql.append("DELETE ");
            sql.append(tableName);
            sql.append(" WHERE RID ='");
            sql.append(tableRid);
            sql.append("'");
            em.createNativeQuery(sql.toString()).executeUpdate();
            // 如果是删除项目组合表，则同进删除项目组合业务提醒
            if ("TB_ZWTJ_SCHEME_ITEMS".equals(tableName)) {
                sql = new StringBuilder();
                sql.append("SELECT T.SCHEME_ID,T.ITEM_CMBID FROM TB_ZWTJ_SCHEME_ITEMS T WHERE T.RID='");
                sql.append(tableRid);
                sql.append("'");
                List<Object[]> resultList = em.createNativeQuery(sql.toString()).getResultList();
                // 删除业务提醒表
                if (null != resultList && resultList.size() > 0) {
                    Object[] objects = resultList.get(0);
                    sql = new StringBuilder();
                    sql.append("DELETE TB_ZWTJ_BSWAKE WHERE ITEM_CMBID=");
                    sql.append(String.valueOf(objects[1]));
                    sql.append(" AND SCHEME_ID=");
                    sql.append(String.valueOf(objects[0]));
                    em.createNativeQuery(sql.toString()).executeUpdate();
                }
            }

            // 删除修改记录表
            sql = new StringBuilder();
            sql.append("DELETE TD_ZW_GBZLOG WHERE REC_ID=");
            sql.append(tableRid);
            sql.append(" AND TAB_NAME ='");
            sql.append(tableName);
            sql.append("'");
            em.createNativeQuery(sql.toString()).executeUpdate();
        }
    }

    /**
     * 根据项目组合ID和主表ID查询业务提醒
     *
     * @param codeRid
     *            项目组合ID
     * @param mainstdId
     *            主表ID
     * @return
     * <AUTHOR>
     * @createDate 2014年9月3日 下午8:51:09
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月3日 下午8:51:09
     */
    @Transactional(readOnly = true)
    public String getWake(Integer codeRid, Integer mainstdId) {
        if (null != codeRid && null != mainstdId) {
            // 根据项目组合ID与标准方案主表ID查询业务提醒
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT T.WAKE_INFO FROM TB_ZWTJ_BSWAKE T WHERE T.ITEM_CMBID ='");
            sql.append(codeRid);
            sql.append("' AND T.SCHEME_ID='");
            sql.append(mainstdId);
            sql.append("'");

            // 获取业务提醒
            List<Object> resutlList = em.createNativeQuery(sql.toString()).getResultList();
            if (null != resutlList && resutlList.size() > 0) {
                return String.valueOf(resutlList.get(0));
            }
        }
        return "";
    }

    /**
     * 清除业务提醒
     *
     * @param schemeItemID
     *            项目组合ID
     * @param mainstdId
     *            主表ID
     * <AUTHOR>
     * @createDate 2014年9月9日 下午8:25:57
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月9日 下午8:25:57
     */
    public void cleanBsWork(Integer schemeItemID, Integer mainstdId) {
        // 判断项目组合ID 与主表ID不为空
        if (null != schemeItemID && null != mainstdId) {
            // 修改记录版本
            // 先查询出业务提醒表的rid
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT T.RID FROM TB_ZWTJ_BSWAKE T WHERE T.ITEM_CMBID='");
            sql.append(schemeItemID);
            sql.append("' AND T.SCHEME_ID='");
            sql.append(mainstdId);
            sql.append("'");
            List<Object[]> resultList = em.createNativeQuery(sql.toString()).getResultList();

            // 删除业务提醒
            sql = new StringBuilder();
            sql.append("DELETE TB_ZWTJ_BSWAKE T WHERE T.ITEM_CMBID='");
            sql.append(schemeItemID);
            sql.append("' AND T.SCHEME_ID='");
            sql.append(mainstdId);
            sql.append("'");
            em.createNativeQuery(sql.toString()).executeUpdate();

            // 再根据RID删除修改记录表
            if (null != resultList && resultList.size() > 0) {
                sql = new StringBuilder();
                sql.append("DELETE TD_ZW_GBZLOG WHERE REC_ID=");
                sql.append(String.valueOf(resultList.get(0)));
                sql.append(" AND TAB_NAME ='TB_ZWTJ_BSWAKE'");
                em.createNativeQuery(sql.toString()).executeUpdate();
            }
        }
    }

    /**
     * 查询未添加的职业禁忌证
     *
     * @param mainId
     *            标准方案主表的ID
     * @return 返回查询结果
     * <AUTHOR>
     * @createDate 2014年9月4日 下午2:19:49
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月4日 下午2:19:49
     */
    @Transactional(readOnly = true)
    public List<Object[]> findJjzList(Integer mainId) {
        if (null != mainId) {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT A.RID,A.CODE_NO,A.CODE_NAME,A.SPLSHT FROM TS_SIMPLE_CODE A ");
            sql.append(" INNER JOIN TS_CODE_TYPE B ON A.CODE_TYPE_ID=B.RID");
            sql.append(" WHERE B.CODE_TYPE_NAME='5011' AND A.RID NOT IN(");
            sql.append(" SELECT CONTRAIND_ID FROM TB_ZWTJ_JJZS WHERE SCHEME_ID ='");
            sql.append(mainId);
            sql.append("') AND A.IF_REVEAL='1' ORDER BY A.CODE_NO");

            List resultList = em.createNativeQuery(sql.toString()).getResultList();
            return resultList;
        }
        return null;
    }

    /**
     * 查询未添加的疑似职业病 列表
     *
     * @param mainId
     *            标准方案主表的ID
     * @return 返回查询结果
     * <AUTHOR>
     * @createDate 2014年9月4日 下午2:19:49
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月4日 下午2:19:49
     */
    @Transactional(readOnly = true)
    public List<Object[]> findOccList(Integer mainId) {
        if (null != mainId) {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT A.RID,A.CODE_NO,A.CODE_NAME,A.CODE_LEVEL_NO,A.SPLSHT FROM TS_SIMPLE_CODE A ");
            sql.append(" INNER JOIN TS_CODE_TYPE B ON A.CODE_TYPE_ID=B.RID");
            sql.append(" WHERE B.CODE_TYPE_NAME='5010' AND A.RID NOT IN(");
            sql.append("SELECT OCC_DISEID FROM TB_ZWTJ_OCCDISES WHERE SCHEME_ID ='");
            sql.append(mainId);
            sql.append("') AND A.IF_REVEAL='1' ORDER BY A.CODE_LEVEL_NO");
            List<Object[]> resultList = em.createNativeQuery(sql.toString()).getResultList();
            return resultList;
        }
        return null;
    }

    /**
     * 查询未添加的询问症关 列表
     *
     * @param mainId
     *            标准方案主表的ID
     * @return 返回查询结果
     * <AUTHOR>
     * @createDate 2014年9月4日 下午2:19:49
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月4日 下午2:19:49
     */
    @Transactional(readOnly = true)
    public List<Object[]> findAskItemList(Integer mainId) {
        if (null != mainId) {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT A.RID,A.CODE_NO,A.CODE_NAME,A.CODE_LEVEL_NO,A.SPLSHT FROM TS_SIMPLE_CODE A ");
            sql.append(" INNER JOIN TS_CODE_TYPE B ON A.CODE_TYPE_ID=B.RID");
            sql.append(" WHERE B.CODE_TYPE_NAME='5006' AND A.RID NOT IN(");
            sql.append("SELECT SYM_ID FROM TB_ZWTJ_ASKITEMS WHERE SCHEME_ID ='");
            sql.append(mainId);
            sql.append("') AND A.IF_REVEAL='1' ORDER BY A.CODE_NO");
            List resultList = em.createNativeQuery(sql.toString()).getResultList();
            return resultList;
        }
        return null;
    }

    /*************************************** Gbz188升级包制作 *********************************/
    /**
     * 更新升级包的状态
     *
     * @param rid
     *            升级包主表ID
     * @param stopTag
     *            停用，启用标记
     * <AUTHOR>
     * @createDate 2014-9-1
     */
    public void updatePackageState(Integer rid, String stopTag) {
        if (null != rid && StringUtils.isNotBlank(stopTag)) {
            StringBuilder tUpSql = new StringBuilder();
            tUpSql.append("UPDATE TD_ZW_GBZFILES T SET T.STOP_TAG = ").append(stopTag);
            tUpSql.append(" WHERE T.RID = ").append(rid);
            this.em.createNativeQuery(tUpSql.toString()).executeUpdate();
        }
    }

    /**
     * 获取相对增量升级包的启用完整升级包集合
     *
     * @return 完整升级包集合
     * <AUTHOR>
     * @createDate 2014-9-1
     */
    @Transactional(readOnly = true)
    public List<Object[]> findTotalPackageList() {
        List<Object[]> returnList = null;
        StringBuilder tTotalSql = new StringBuilder();
        tTotalSql.append("SELECT T.RID,T.PACKAGE_NAME FROM TD_ZW_GBZFILES T ");
        tTotalSql.append(" WHERE T.PACKAGE_TYPE = 0 AND T.STOP_TAG = 1 ORDER BY T.PACKAGE_DATE,T.END_VER");
        returnList = this.em.createNativeQuery(tTotalSql.toString()).getResultList();
        return returnList;
    }

    /**
     * 获取完整升级包的基础资料的数据集合
     *
     * @return 基础资料集合
     * <AUTHOR>
     * @createDate 2014-9-1
     */
    @Transactional(readOnly = true)
    public List<Object[]> findBaseInfoList() {
        StringBuilder tsql = new StringBuilder();
        tsql.append("SELECT T1.CODE_TYPE_NAME,T.CODE_NO,T.CODE_NAME,T.CODE_LEVEL_NO,T.RID FROM TS_SIMPLE_CODE T ");
        tsql.append(" INNER JOIN TS_CODE_TYPE T1 ON T.CODE_TYPE_ID = T1.RID WHERE T1.CODE_TYPE_NAME IN (");
        tsql.append("'5013','5012','5011','5010','5009','5007','5006') AND T.IF_REVEAL = '1'");
        List<Object[]> resultList = this.em.createNativeQuery(tsql.toString()).getResultList();
        return resultList;
    }

    /**
     * 获取大于某版本的基础资料集合
     *
     * @param endVer
     *            起始版本号
     * @return 基础资料集合
     * <AUTHOR>
     * @createDate 2014-9-1
     */
    @Transactional(readOnly = true)
    public List<Object[]> findPackageList(Integer endVer) {
        if (null != endVer) {
            StringBuilder tsql = new StringBuilder();
            tsql.append("SELECT T.TAB_NAME,T.REC_ID,T.DEL_FLAG FROM TD_ZW_GBZLOG T");
            tsql.append(" WHERE T.TAB_NAME IN ('TS_SIMPLE_CODE','TB_ZW_OCCDIGSTD','TB_ZW_BADRSN_DIGSTD')");
            tsql.append(" AND T.VER_NUM > '").append(endVer).append("' ORDER BY T.VER_NUM");
            List<Object[]> resultList = this.em.createNativeQuery(tsql.toString()).getResultList();
            return resultList;
        }
        return null;
    }

    /**
     * 根据码表ID获取码表数据集合
     *
     * @param rids
     *            基础码表的rid字符串
     * @return
     * <AUTHOR>
     * @createDate 2014-9-1
     */
    @Transactional(readOnly = true)
    public List<Object[]> findBaseInfoStateList(String rids) {
        StringBuilder tsql = new StringBuilder();
        tsql.append("SELECT T1.CODE_TYPE_NAME,T.CODE_NO,T.CODE_NAME，T.CODE_LEVEL_NO,T.RID FROM TS_SIMPLE_CODE T ");
        tsql.append(" INNER JOIN TS_CODE_TYPE T1 ON T.CODE_TYPE_ID = T1.RID WHERE T.RID IN (");
        tsql.append(rids).append(")");
        List<Object[]> resultList = this.em.createNativeQuery(tsql.toString()).getResultList();
        return resultList;
    }

    /**
     * 获取大于版本号的职业健康监护方案标准
     *
     * @param endVer
     *            版本号
     * @param rids
     *            职业健康监护方案标准的id字符串
     * @return
     * <AUTHOR>
     * @createDate 2014-9-3
     */
    @Transactional(readOnly = true)
    public List<Object[]> getMainStdsAdd(int endVer, String rids) {
        StringBuilder tsql = new StringBuilder();
        tsql.append("SELECT A.RID,A.DEL_FLAG,A.BADRSN,A.WORDCODE FROM (");
        tsql.append("SELECT T.RID,T1.DEL_FLAG ,COD.CODE_NO AS BADRSN,COD2.CODE_NO AS WORDCODE FROM TB_ZWTJ_MAINSTD T ");
        tsql.append(" INNER JOIN TS_SIMPLE_CODE COD ON T.BADRSN_ID = COD.RID");
        tsql.append(" INNER JOIN TS_SIMPLE_CODE COD2 ON COD2.RID = T.WORK_STATEID");
        tsql.append(" INNER JOIN TD_ZW_GBZLOG T1 ON T.RID= T1.REC_ID WHERE T1.TAB_NAME ='TB_ZWTJ_MAINSTD' ");
        tsql.append(" AND T1.VER_NUM > '").append(endVer).append("' ORDER BY T1.VER_NUM) A");
        if (StringUtils.isNotBlank(rids)) {
            tsql.append(" UNION SELECT T.RID,0,COD.CODE_NO AS BADRSN,COD2.CODE_NO AS WORDCODE FROM TB_ZWTJ_MAINSTD T INNER JOIN TS_SIMPLE_CODE COD ON T.BADRSN_ID = COD.RID");
            tsql.append(" INNER JOIN TS_SIMPLE_CODE COD2 ON COD2.RID = T.WORK_STATEID WHERE T.RID IN (").append(rids)
                    .append(")");
        }
        List<Object[]> resultList = this.em.createNativeQuery(tsql.toString()).getResultList();
        return resultList;
    }

    /**
     * 获取大于版本号的职卫体检方案标准项目
     *
     * @param endVer
     *            版本号
     * @return
     * <AUTHOR>
     * @createDate 2014-9-3
     */
    @Transactional(readOnly = true)
    public List<Object[]> getItemsAdd(int endVer) {
        StringBuilder tsql = new StringBuilder();
        tsql.append("SELECT T.SCHEME_ID,T1.DEL_FLAG,T.RID,COD.CODE_NO,T.IS_MUST,T.IS_TARGETITEM FROM TB_ZWTJ_SCHEME_ITEMS T ");
        tsql.append(" INNER JOIN TS_SIMPLE_CODE COD ON  T.ITEM_CMBID = COD.RID");
        tsql.append(" INNER JOIN TD_ZW_GBZLOG T1 ON T.RID = T1.REC_ID WHERE T1.TAB_NAME = 'TB_ZWTJ_SCHEME_ITEMS'");
        tsql.append(" AND T1.VER_NUM > '").append(endVer).append("' ORDER BY T1.VER_NUM");
        List<Object[]> resultList = this.em.createNativeQuery(tsql.toString()).getResultList();
        return resultList;
    }

    /**
     * 获取大于版本号的职业健康监护标准禁忌症
     *
     * @param endVer
     *            版本号
     * @return
     * <AUTHOR>
     * @createDate 2014-9-3
     */
    @Transactional(readOnly = true)
    public List<Object[]> getJjzsAdd(int endVer) {
        StringBuilder tsql = new StringBuilder();
        tsql.append("SELECT T.SCHEME_ID,T1.DEL_FLAG,T.RID,COD.CODE_NO FROM TB_ZWTJ_JJZS T INNER JOIN TS_SIMPLE_CODE COD ");
        tsql.append(" ON T.CONTRAIND_ID = COD.RID INNER JOIN TD_ZW_GBZLOG T1 ON T.RID = T1.REC_ID ");
        tsql.append(" WHERE T1.TAB_NAME = 'TB_ZWTJ_JJZS'");
        tsql.append(" AND T1.VER_NUM > '").append(endVer).append("' ORDER BY T1.VER_NUM");
        List<Object[]> resultList = this.em.createNativeQuery(tsql.toString()).getResultList();
        return resultList;
    }

    /**
     * 获取大于版本号的职业健康监护标准疑似职业病
     *
     * @param endVer
     *            版本号
     * @return
     * <AUTHOR>
     * @createDate 2014-9-3
     */
    @Transactional(readOnly = true)
    public List<Object[]> getOccsAdd(int endVer) {
        StringBuilder tsql = new StringBuilder();
        tsql.append("SELECT T.SCHEME_ID,T1.DEL_FLAG,T.RID,COD.CODE_NO FROM TB_ZWTJ_OCCDISES T ");
        tsql.append(" INNER JOIN TS_SIMPLE_CODE COD ON  T.OCC_DISEID = COD.RID");
        tsql.append(" INNER JOIN TD_ZW_GBZLOG T1 ON T.RID = T1.REC_ID WHERE T1.TAB_NAME = 'TB_ZWTJ_OCCDISES'");
        tsql.append(" AND T1.VER_NUM > '").append(endVer).append("' ORDER BY T1.VER_NUM");
        List<Object[]> resultList = this.em.createNativeQuery(tsql.toString()).getResultList();
        return resultList;
    }

    /**
     * 获取大于版本号的职业健康监护标准询问症状
     *
     * @param endVer
     *            版本号
     * @return
     * <AUTHOR>
     * @createDate 2014-9-3
     */
    @Transactional(readOnly = true)
    public List<Object[]> getAsksAdd(int endVer) {
        StringBuilder tsql = new StringBuilder();
        tsql.append("SELECT T.SCHEME_ID,T1.DEL_FLAG,T.RID,COD.CODE_NO FROM TB_ZWTJ_ASKITEMS T ");
        tsql.append(" INNER JOIN TS_SIMPLE_CODE COD ON  T.SYM_ID = COD.RID");
        tsql.append(" INNER JOIN TD_ZW_GBZLOG T1 ON T.RID = T1.REC_ID WHERE T1.TAB_NAME = 'TB_ZWTJ_ASKITEMS'");
        tsql.append(" AND T1.VER_NUM > '").append(endVer).append("' ORDER BY T1.VER_NUM");
        List<Object[]> resultList = this.em.createNativeQuery(tsql.toString()).getResultList();
        return resultList;
    }

    /**
     * 获取大于版本号的职业健康监护标准业务提醒
     *
     * @param endVer
     *            版本号
     * @return
     * <AUTHOR>
     * @createDate 2014-9-3
     */
    @Transactional(readOnly = true)
    public List<Object[]> getWakesAdd(int endVer) {
        StringBuilder tsql = new StringBuilder();
        tsql.append("SELECT T.SCHEME_ID,T1.DEL_FLAG,T.RID,COD.CODE_NO,T.WAKE_INFO FROM TB_ZWTJ_BSWAKE T ");
        tsql.append(" INNER JOIN TS_SIMPLE_CODE COD ON  T.ITEM_CMBID = COD.RID");
        tsql.append(" INNER JOIN TD_ZW_GBZLOG T1 ON T.RID = T1.REC_ID WHERE T1.TAB_NAME = 'TB_ZWTJ_BSWAKE'");
        tsql.append(" AND T1.VER_NUM > '").append(endVer).append("' ORDER BY T1.VER_NUM");
        List<Object[]> resultList = this.em.createNativeQuery(tsql.toString()).getResultList();
        return resultList;
    }

    /**
     * 根据传入的完整包ID，获取当前完整包下的制作的升级包最大版本号的版本
     *
     * @param packId
     *            升级包id
     * @return 返回当前完整包下的最大版本号
     * <AUTHOR>
     * @createDate 2014-9-9
     */
    @Transactional(readOnly = true)
    public int findPackageMaxVer(Integer packId) {
        StringBuilder tsql = new StringBuilder();
        tsql.append("SELECT A.END_VER FROM (");
        tsql.append("SELECT T.END_VER,T.VER,T.PACKAGE_TYPE FROM TD_ZW_GBZFILES T LEFT JOIN TD_ZW_GBZFILES T1 ");
        tsql.append("ON T.RID = T1.FULLPKG_ID  WHERE T.FULLPKG_ID = '").append(packId).append("' or t.rid ='")
                .append(packId).append("' ");
        tsql.append(" ORDER BY T.END_VER DESC  ) A WHERE ROWNUM =1 ");
        List<Object> resultList = this.em.createNativeQuery(tsql.toString()).getResultList();
        if (null != resultList && resultList.size() > 0) {
            Object obj = resultList.get(0);
            return obj == null ? 0 : Integer.valueOf(String.valueOf(obj));
        }
        return 0;
    }

    /**
     * 删除项目组合关系
     *
     * @param rid
     *            项目组合关系rid
     * <AUTHOR>
     * @history 2014年9月10日
     */
    public void deleteZwtjItemByRid(Integer rid) {
        if (null != rid) {
            StringBuilder sql = new StringBuilder("");
            sql.append("DELETE TB_ZWTJ_ITEMCMB_ITEMS WHERE RID = ").append(rid);
            em.createNativeQuery(sql.toString()).executeUpdate();
        }
    }

    /**
     * 停用启用项目组合关系
     *
     * @param rid
     *            项目组合关系rid
     * @param state
     *            状态 0 停用 1启用
     * @param userRid
     *            修改用户rid
     * <AUTHOR>
     * @history 2014年9月10日
     */
    public void startOfStopZwtjItemByRid(Integer rid, String state, Integer userRid) {
        if (null != rid && null != userRid && StringUtils.isNotBlank(state)) {
            StringBuilder sql = new StringBuilder("");
            sql.append(" UPDATE TB_ZWTJ_ITEMCMB_ITEMS SET STOP_TAG = '").append(state);
            sql.append("', MODIFY_MANID = ").append(userRid);
            sql.append(" WHERE RID = ").append(rid);
            em.createNativeQuery(sql.toString()).executeUpdate();
        }
    }

    /**
     * 资质服务机构保存/更新
     *
     * @param tbTjSrvorg
     *            要添加的资质服务机构对象
     * @return
     * <AUTHOR>
     * @createDate 2014-9-10
     */

    public String saveTbTjSrvorg(TbTjSrvorg tbTjSrvorg) {
        if (tbTjSrvorg == null)
            return "数据为空！";
        StringBuilder sql = new StringBuilder("");
        sql.append("from TbTjSrvorg t where t.unitCode = '");
        sql.append(tbTjSrvorg.getUnitCode());
        sql.append("' ");
        if (tbTjSrvorg.getRid() != null) {
            sql.append(" and t.rid != ");
            sql.append(tbTjSrvorg.getRid());
        }
        boolean flag = this.em.createQuery(sql.toString()).getResultList().size() == 0;
        if (!flag)
            return "机构编码重复，请更改！";
        if (tbTjSrvorg.getRid() == null) {
            this.save(tbTjSrvorg);
        } else {
            this.update(tbTjSrvorg);
        }
        return null;
    }

    /**
     * 资质服务机构删除
     *
     * @param rid
     *            要删除的对象的rid
     * @return 为null说明删除成功，否则不成功并且提示原因
     * <AUTHOR>
     * @createDate 2014-9-10
     */

    public String deleteTbTjSrvorgByRid(Integer rid) {
        try {
            this.delete(TbTjSrvorg.class, rid);
            return null;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return "该资质服务机构已被引用，无法删除！";
        }
    }

    /**
     * 资质服务机构查询
     *
     * @param rid
     *            需要查询资质服务机构的id
     * @return 资质服务机构、对象TbTjSrvorg
     * <AUTHOR>
     * @createDate 2014-9-10
     */
    @Transactional(readOnly = true)
    public TbTjSrvorg findTbTjSrvorgById(Integer rid) {
        return (TbTjSrvorg) this.find(TbTjSrvorg.class, rid);
    }

    /**
     * 体检项目添加
     *
     * @param tbTjItems
     *            editYeWuRid 业务分类ID userRid 登录人id pyxnam 体检项目的首字母大写
     * @return
     * <AUTHOR>
     * @createDate 2014-9-12
     */
    @Deprecated
    public String saveTbTjItems(TbTjItems tbTjItems, Integer editYeWuRid, Integer userRid, String pyxnam) {
        tbTjItems.getTsSimpleCode().setRid(editYeWuRid);
        tbTjItems.setModifyDate(new Date());
        tbTjItems.setModifyManid(userRid);
        tbTjItems.setPyxnam(pyxnam); // 体检项目名简写
        StringBuilder sql = new StringBuilder("");
        sql.append("from TbTjItems t where t.itemCode = '");
        sql.append(tbTjItems.getItemCode());
        sql.append("' ");
        List<TbTjItems> ttts = (List<TbTjItems>) this.em.createQuery(sql.toString()).getResultList();
        boolean flag = ttts.size() == 0;
        // 存在项目编码
        if (!flag) {
            // 判断rid存在，并且相等
            if (tbTjItems.getRid() != null && tbTjItems.getRid().intValue() == ttts.get(0).getRid().intValue()) {
                this.update(tbTjItems);
            } 
            return null;
        } else {
            if (tbTjItems.getRid() != null) {
                this.update(tbTjItems);
                return null;
            } else {
                tbTjItems.setCreateDate(new Date());
                tbTjItems.setCreateManid(userRid);
                this.save(tbTjItems);
                return null;
            }
        }
    }

    /**
     * 体检项目删除
     *
     * @param rid
     * @return
     * <AUTHOR>
     * @createDate 2014-9-12
     */

    public String  deleteTbTjItems(Integer rid) {
        try {
            if(rid!=null){
                StringBuffer sb =new StringBuffer();
                sb.append(" BEGIN " );
                sb.append(" delete from TB_TJ_RSTDESC t where t.ITEM_ID=").append(rid).append("; ");
                sb.append(" delete from TB_TJ_ITEM_UNIT_REL t where t.ITEM_ID=").append(rid).append("; ");
                sb.append(" delete from TB_TJ_ITEMS_SPE t where t.ITEM_ID=").append(rid).append("; ");
                //删除国家接口标准
                sb.append(" delete from TB_TJ_ITEMS_GJ t where t.ITEM_ID=").append(rid).append("; ");
                sb.append(" delete from TB_TJ_ITEMS t where t.rid=").append(rid).append("; ");
                sb.append(" END; " );
                em.createNativeQuery(sb.toString()).executeUpdate();
            }
            return "体检项目删除成功！";
        } catch (Exception e) {
            e.printStackTrace();
            return "该体检项目已被引用，不允许删除！";
        }
    }

    /**
     * 体检项目更新
     *
     * @param tbTjItems
     * @return
     * <AUTHOR>
     * @createDate 2014-9-12
     */

    public void updateTbTjItems(TbTjItems tbTjItems) {
        this.update(tbTjItems);
    }

    /**
     * 体检项目查找
     *
     * @param rid
     * @return TbTjItems 体检项目对象
     * <AUTHOR>
     * @createDate 2014-9-12
     */
    @Transactional(readOnly = true)
    public TbTjItems findTbTjItems(Integer rid) {
        TbTjItems tbTjItems = (TbTjItems) this.find(TbTjItems.class, rid);
        // 获取子表信息 不然转到修改试图有 no session or session was closed异常
        if (tbTjItems != null)
            tbTjItems.getTbTjRstdescs().size();
        tbTjItems.getTbTjItemsSpeList().size();
        tbTjItems.getTbTjItemsUnitList().size();
        return tbTjItems;
    }

    // ===========================职业病诊断标准===================================

    /**
     * 查找码表中类别为5007的记录
     *
     * @return 危险因素集合
     * <AUTHOR>
     * @createDate 2014-9-10
     *
     *             修改人: wlj 修改时间：2014-9-17 修改内容：方法整改
     */

    @Transactional(readOnly = true)
    public List<TsSimpleCode> findBadrsnList() {
        StringBuilder sb = new StringBuilder();
        sb.append("from TsSimpleCode t where t.tsCodeType.codeTypeName = '5007'");
        sb.append(" and t.ifReveal = 1 order by t.codeLevelNo");
        return em.createQuery(sb.toString()).getResultList();
    }

    /**
     * @Description : 资质人员信息详情
     * @Date :2017/5/27 9:10
     */
    @Transactional(readOnly = true)
    public TdZwPsninfoComm findTdZwPsninfoByRid(Integer rid) {
    	TdZwPsninfoComm tdZwPsInfo = (TdZwPsninfoComm) find(TdZwPsninfoComm.class, rid);

        return tdZwPsInfo;
    }

    /**
     * 删除主表
     *
     * @param rid
     *            主表rid
     * <AUTHOR>
     * @createDate 2014-9-10
     */

    public void deleteTbZwOccdigstd(Integer rid) {
        if (null != rid) {
            StringBuilder sql = new StringBuilder();
            sql.append("DELETE TB_ZW_BADRSN_DIGSTD WHERE DIGSTD_ID = ");
            sql.append(rid);
            em.createNativeQuery(sql.toString()).executeUpdate();
            sql = new StringBuilder();
            sql.append("DELETE TB_ZW_OCCDIGSTD WHERE RID = ");
            sql.append(rid);
            em.createNativeQuery(sql.toString()).executeUpdate();
            // 保存日志
            recDeleStdByMainid(rid);
        }
    }

    /**
     * 保存删除的日志
     *
     * @param rid
     *            主表rid
     * <AUTHOR>
     * @createDate 2014-9-17
     */
    private void recDeleStdByMainid(Integer rid) {
        // 删子表记录
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT T.RID FROM TB_ZW_BADRSN_DIGSTD T WHERE T.DIGSTD_ID =");
        sb.append(rid);
        List<Object> list = em.createNativeQuery(sb.toString()).getResultList();
        if (null != list && list.size() > 0) {
            for (Object obj : list) {
                this.delGbz("TB_ZW_BADRSN_DIGSTD", Integer.valueOf(obj.toString()));
            }
        }
        // 删主表日志
        this.delGbz("TB_ZW_OCCDIGSTD", rid);
    }

    /**
     * 查询企业列表
     *
     * <AUTHOR>
     * @createDate 2014年9月19日
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月19日
     * @return 返回查询出来的企业表ID，企业名称，企业组织机构号
     */

    @Transactional(readOnly = true)
    public List<Object[]> getCrptList(Integer crptId) {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT T.RID,T.CRPT_NAME,T.INSTITUTION_CODE,T4.ZONE_NAME,T.ADDRESS");
        sql.append(" ,T2.CODE_NAME AS C1,T1.CODE_NAME AS C2,T3.CODE_NAME AS C3,T.SAFETY_PRINCIPAL,T.SAFEPHONE FROM TB_TJ_CRPT T");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T1 ON T1.RID = T.ECONOMY_ID");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T2.RID = T.INDUS_TYPE_ID");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T3.RID = T.CRPT_SIZE_ID");
        sql.append(" LEFT JOIN TS_ZONE T4 ON T4.RID = T.ZONE_ID WHERE 1=1");
        if( null != crptId)	{
            sql.append(" AND T.RID = ").append(crptId);
        }
        sql.append("  ORDER BY T4.ZONE_GB,T.CRPT_NAME ");
        return em.createNativeQuery(sql.toString()).getResultList();
    }

    /**
     * 查询系统码表集合根据 码表类型ID和，和要过滤掉的RID
     *
     * @param typeId
     *            类型ID
     * @param cleanRids
     *            要去掉的RID
     * @return
     * <AUTHOR>
     * @createDate 2014年9月22日
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月22日
     */
    @Transactional(readOnly = true)
    public List<TsSimpleCode> getSimpleCodeById(String typeId, String cleanRids) {
        if (StringUtils.isNotBlank(typeId)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select t from TsSimpleCode t where t.tsCodeType.codeTypeName='");
            sql.append(typeId);
            sql.append("' and t.ifReveal=1");
            if (StringUtils.isNotBlank(cleanRids)) {
                sql.append(" and t.rid not in(");
                sql.append(cleanRids);
                sql.append(")");
            }
            List<TsSimpleCode> resultList = em.createQuery(sql.toString()).getResultList();
            return resultList;
        }
        return null;
    }

    /**
     * 根据身份证号查询体检人员
     *
     * @param idc
     *            身份证号
     * @return
     * <AUTHOR>
     * @createDate 2014年9月23日
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月23日
     */
    @Transactional(readOnly = true)
    public TdTjPerson getPerson(String idc) {
        if (StringUtils.isNotBlank(idc)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select t from TdTjPerson t where t.idc ='");
            sql.append(idc);
            sql.append("'");
            List<TdTjPerson> resultList = em.createQuery(sql.toString()).getResultList();
            if (null != resultList && resultList.size() > 0) {
                return resultList.get(0);
            }
        }
        return null;
    }

    /**
     * 根据身份证号查询人员的历史受理记录,不包含当前编辑记录
     *
     * @param idc
     *            身份证号
     * @param rid
     *            当前编辑记录rid
     * @return
     * <AUTHOR>
     * @createDate 2014年9月23日
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月23日
     */
    @Transactional(readOnly = true)
    public List<Object[]> getOccHistory(String idc, Integer rid) {
        if (StringUtils.isNotBlank(idc)) {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT T.RID,T.ARCH_CODE,T.PERSONNEL_NAME,T.SEX,");
            sql.append(" B.CODE_NAME,T.ACCEPT_DATE,T.IS_DIS,C.CODE_NAME");
            sql.append(" FROM TD_ZW_OCCDISCASE T");
            sql.append(" LEFT JOIN TS_SIMPLE_CODE B ON T.OCCDISE_APPLYID = B.RID");
            sql.append(" LEFT JOIN TS_SIMPLE_CODE C ON T.OCCDISE_ID = C.RID");
            sql.append(" WHERE T.IDC='");
            sql.append(idc);
            sql.append("'");
            if (null != rid) {
                sql.append(" AND T.RID !=");
                sql.append(rid);
            }
            List<Object[]> resultList = em.createNativeQuery(sql.toString()).getResultList();
            return resultList;
        }
        return null;
    }

    /**
     * 保存新增企业
     *
     * @param tbTjCrpt
     *            新增企业
     * @return
     * <AUTHOR>
     * @createDate 2014年9月24日
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月24日
     */
    public TbTjCrpt saveCrpt(TbTjCrpt tbTjCrpt) {
        if (null != tbTjCrpt) {
            if( null != tbTjCrpt.getRid())	{
                tbTjCrpt = (TbTjCrpt) this.updateObj(tbTjCrpt);
            }else{
                tbTjCrpt = (TbTjCrpt) this.saveObj(tbTjCrpt);
            }
            return tbTjCrpt;
        }
        return null;
    }

    /**
     * 验证输入的档案编号是否重复
     *
     * @param archCode
     *            档案编号
     * @param rid
     *            主表RID
     * @return
     * <AUTHOR>
     * @createDate 2014年9月25日
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月25日
     */
    @Transactional(readOnly = true)
    public boolean verifyArchCode(String archCode, Integer rid) {
        if (StringUtils.isNotBlank(archCode)) {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT COUNT(*) FROM TD_ZW_OCCDISCASE T");
            sql.append(" WHERE T.ARCH_CODE='");
            sql.append(archCode);
            sql.append("'");
            // 如果是修改保存，不包含自己
            if (null != rid) {
                sql.append(" AND T.RID!=");
                sql.append(rid);
            }

            Object singleResult = em.createNativeQuery(sql.toString()).getSingleResult();
            // 如果查询的结果大于0则有重复档案编号
            if (null != singleResult && !"0".equals(singleResult.toString())) {
                return true;
            }
        }
        return false;
    }

    public void updateTbTjLawState(Integer rid, int state) {
        StringBuilder sb = new StringBuilder();
        sb.append(" UPDATE TB_TJ_LAW T SET T.STATE_MARK=").append(state);
        sb.append(" WHERE T.RID = '").append(rid).append("'");
        em.createNativeQuery(sb.toString()).executeUpdate();
    }

    @Transactional(readOnly = true)
    public Integer checkTbTjLawCode(String code, Integer rid) {
        StringBuilder sb = new StringBuilder();
        sb.append("select COUNT(*) from TbTjLaw t where 1=1 ");
        if (StringUtils.isNotBlank(code)) {
            sb.append(" and t.lawCode = :lawCode");
        }

        if (rid != null) {
            sb.append(" and t.rid != ").append(rid);
        }

        Query query = em.createQuery(sb.toString());
        query.setParameter("lawCode", code);

        Object countObj = query.getSingleResult();
        return Integer.parseInt(countObj == null ? "0" : countObj.toString());
    }

    public void updateTbTjChlShowState(Integer rid, int state) {
        StringBuilder sb = new StringBuilder();
        sb.append(" UPDATE TB_TJ_CHL_SHOW T SET T.STATE_MARK=").append(state);
        sb.append(" WHERE T.RID = '").append(rid).append("'");
        em.createNativeQuery(sb.toString()).executeUpdate();
    }

    @Transactional(readOnly = true)
    public Integer checkTbTjChlShowCode(String code, Integer rid) {
        StringBuilder sb = new StringBuilder();
        sb.append("select COUNT(*) from TbTjChlShow t where 1=1 ");
        if (StringUtils.isNotBlank(code)) {
            sb.append(" and t.updateCode = :updateCode");
        }

        if (rid != null) {
            sb.append(" and t.rid != ").append(rid);
        }

        Query query = em.createQuery(sb.toString());
        query.setParameter("updateCode", code);

        Object countObj = query.getSingleResult();
        return Integer.parseInt(countObj == null ? "0" : countObj.toString());
    }

    public void updateTbTjChlKnowState(Integer rid, int state) {
        StringBuilder sb = new StringBuilder();
        sb.append(" UPDATE TB_TJ_CHL_KNOW T SET T.STATE_MARK=").append(state);
        sb.append(" WHERE T.RID = '").append(rid).append("'");
        em.createNativeQuery(sb.toString()).executeUpdate();
    }

    @Transactional(readOnly = true)
    public Integer checkTbTjChlKnowCode(String code, Integer rid) {
        StringBuilder sb = new StringBuilder();
        sb.append("select COUNT(*) from TbTjChlKnow t where 1=1 ");
        if (StringUtils.isNotBlank(code)) {
            sb.append(" and t.updateCode = :updateCode");
        }

        if (rid != null) {
            sb.append(" and t.rid != ").append(rid);
        }

        Query query = em.createQuery(sb.toString());
        query.setParameter("updateCode", code);

        Object countObj = query.getSingleResult();
        return Integer.parseInt(countObj == null ? "0" : countObj.toString());
    }

    public void updateTbTjChlHelpState(Integer rid, int state) {
        StringBuilder sb = new StringBuilder();
        sb.append(" UPDATE TB_TJ_CHL_HELP T SET T.STATE_MARK=").append(state);
        sb.append(" WHERE T.RID = '").append(rid).append("'");
        em.createNativeQuery(sb.toString()).executeUpdate();
    }

    @Transactional(readOnly = true)
    public Integer checkTbTjChlHelpCode(String code, Integer rid) {
        StringBuilder sb = new StringBuilder();
        sb.append("select COUNT(*) from TbTjChlHelp t where 1=1 ");
        if (StringUtils.isNotBlank(code)) {
            sb.append(" and t.updateCode = :updateCode");
        }

        if (rid != null) {
            sb.append(" and t.rid != ").append(rid);
        }

        Query query = em.createQuery(sb.toString());
        query.setParameter("updateCode", code);

        Object countObj = query.getSingleResult();
        return Integer.parseInt(countObj == null ? "0" : countObj.toString());
    }

    @Transactional(readOnly = true)
    public Map<String, String> findItemCodeNames(String codes, String codeType) {
        if (StringUtils.isNotBlank(codes) && StringUtils.isNotBlank(codeType)) {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT T.CODE_NO,T.CODE_NAME FROM TS_SIMPLE_CODE T INNER JOIN TS_CODE_TYPE T1 ");
            sql.append("ON T.CODE_TYPE_ID = T1.RID WHERE T1.CODE_TYPE_NAME = '");
            sql.append(codeType).append("' AND T.CODE_NO IN (").append(codes).append(")");
            List<Object[]> resultList = this.em.createNativeQuery(sql.toString()).getResultList();
            if (null != resultList) {
                Map<String, String> map = new HashMap<String, String>();
                for (Object[] objArr : resultList) {
                    map.put(String.valueOf(objArr[0]), String.valueOf(objArr[1]));
                }
                return map;
            }
        }
        return null;
    }

    @Transactional(readOnly = true)
    public List<TbTjItems> findTjItemList(String searchItemCode, String searchItemName,  String[] searchState,
    		String[] searchPanDuanState, Integer ywId) {
        StringBuilder sb = new StringBuilder("from TbTjItems t  where 1=1 ");
        // 查询项目编码字段
        if (StringUtils.isNotBlank(searchItemCode)) {
            sb.append(" and t.itemCode like :itemCode ");
        }
        // 查询项目名称或者拼音码
        if (StringUtils.isNotBlank(searchItemName)) {
            sb.append(" and (t.itemName like :itemName or UPPER(t.pyxnam) like :itemPyName  )");
        }
        // 查询状态 1为启用 0为停用 STOP_TAG
        if (null != searchState && searchState.length == 1) {
            sb.append(" and t.stopTag in (").append(searchState[0]).append(")");
        }
        // 判断模式 1为定性 0为定量
        if (null != searchPanDuanState && searchPanDuanState.length == 1) {
            sb.append(" and t.jdgptn in (").append(searchPanDuanState).append(")");
        }
        // 业务分类 关联码表查询
        if (null != ywId) {
            sb.append(" and t.tsSimpleCode.rid = ").append(ywId);
        }
        // 按业务分类 和项目编码升序排列 codeName
        sb.append(" order by t.tsSimpleCode.codeLevelNo,t.num, t.itemCode ");
        Query query = this.em.createQuery(sb.toString());
        // 查询项目编码字段
        if (StringUtils.isNotBlank(searchItemCode)) {
            query.setParameter("itemCode", "%" + StringUtils.convertBFH(searchItemCode) + "%");
        }
        // 查询项目名称或者拼音码
        if (StringUtils.isNotBlank(searchItemName)) {
            query.setParameter("itemName", "%" + StringUtils.convertBFH(searchItemName) + "%");
            query.setParameter("itemPyName", "%" + StringUtils.convertBFH(searchItemName.toUpperCase()) + "%");
        }
        List<TbTjItems> resultList = query.getResultList();
        return resultList;
    }

    public void changeTwoTbTjItems(TbTjItems t1, TbTjItems t2) {
        if (null != t1 && null != t2) {
            StringBuilder sql = new StringBuilder();
            sql.append(" BEGIN");
            sql.append(" UPDATE TB_TJ_ITEMS T SET T.NUM = ? WHERE T.RID = ?;");
            sql.append(" UPDATE TB_TJ_ITEMS T SET T.NUM = ? WHERE T.RID = ?;");
            sql.append(" END; ");
            Query query = this.em.createNativeQuery(sql.toString());
            query.setParameter(1, t1.getNum());
            query.setParameter(2, t2.getRid());
            query.setParameter(3, t2.getNum());
            query.setParameter(4, t1.getRid());
            query.executeUpdate();
        }
    }

    /**
     * 传入体检项目集合更新体检项目的序号
     *
     * @param orderList
     */
    public void updateTjItemsCodeNum(List<TbTjItems> orderList) {
        if (null != orderList && orderList.size() > 0) {
            StringBuilder sql = new StringBuilder();
            sql.append(" BEGIN");
            for (int i = 0; i < orderList.size(); i++) {
                TbTjItems tempCode = orderList.get(i);
                sql.append(" UPDATE TB_TJ_ITEMS T SET T.NUM = '").append(i + 1).append("' ");
                sql.append(" WHERE T.RID = '").append(tempCode.getRid()).append("';");
            }
            sql.append(" END;");
            this.em.createNativeQuery(sql.toString()).executeUpdate();
        }
    }

    public List<TsSimpleCode> getlawTypeList(){
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TsSimpleCode t where 1=1 and t.tsCodeType.codeTypeName ='5016'");
        List<TsSimpleCode> list=em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size()>0){
            return list;
        }
        return null;
    }

    public TsSimpleCode getTsSimpleCode(Integer rid){
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TsSimpleCode t where 1=1 and t.rid=").append(rid);
        List<TsSimpleCode> list=em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size()>0){
            for(TsSimpleCode t:list){
                return t;
            }
        }
        return null;
    }
    //	@Transactional(readOnly = true)
//	public Integer findTdZwOccdiscaseMaxFlow(Integer id){
//		if( null !=  id){
//			StringBuilder sb=new StringBuilder();
//			sb.append("select max(t.FLOW_TYPE) from TD_ZW_OCCDISCASE_FLOW t where 1=1 and t.MAIN_ID =").append(id);
//			List<Object> list=em.createNativeQuery(sb.toString()).getResultList();
//			if(null != list && list.size()>0){
//				for (Object object : list) {
//					if (null!=object) {
//						return Integer.valueOf(object.toString());
//					}
//				}
//			}
//		}
//		return null;
//	}
//	@Transactional(readOnly = true)
//	public List<TdZwOccdiscaseFlow> findTdZwOccdiscaseFlow(Integer id){
//		if( null !=  id){
//			StringBuilder sb=new StringBuilder();
//			sb.append("select t from TdZwOccdiscaseFlow t where 1=1 and t.fkByMainId.rid =").append(id);
//			sb.append(" order by t.flowType");
//			List<TdZwOccdiscaseFlow> list= em.createQuery(sb.toString()).getResultList();
//			if(null != list && list.size()>0){
//				return list;
//			}
//		}
//		return null;
//	}

    public List<Object[]> findPsnList(Integer unitId){
        if(null != unitId){
            StringBuilder sb=new StringBuilder();
            sb.append(" select distinct t1.rid,t1.PSN_NAME,nvl(t1.IDC_CARD,' '),t3.code_name, ");
            sb.append(" t1.DOSE_NO,t2.psn_id ,t1.ON_DUTY,t1.PSN_SEX,1,'false','false'  from TD_TJ_RADHETH_PSN t1");
            sb.append(" left join TD_TJ_RAD_CHECK_SUB t2 on t1.rid=t2.PSN_ID");
            sb.append(" left join ts_simple_code t3 on t1.WORK_TYPE_ID=t3.rid");
            sb.append(" where t1.UNIT_ID=").append(unitId);
            return em.createNativeQuery(sb.toString()).getResultList();
        }
        return null;
    }

    /**
     * <p>方法描述：系统内在职的身份证号不允许重复。</p>
     *
     * @MethodAuthor rcj,2018年9月29日,findPsnByIdc
     * @param idc
     * @param rid
     * @return
     */
    @Transactional(readOnly = true)
    public Integer findPsnByIdc(String idc,Integer rid) {
        StringBuilder sql = new StringBuilder("");
        sql.append("select t.rid from TD_TJ_RADHETH_PSN t WHERE  t.ON_DUTY ='1'    and  upper(t.IDC_CARD) = upper('").append(idc).append("')");
        if (null!=rid) {
            sql.append(" and t.rid != ").append(rid);
        }
        List<Object> list = em.createNativeQuery(sql.toString()).getResultList();
        if (null!=list && list.size()>0) {
            return new Integer(list.get(0).toString());
        }
        return null;
    }

    /**
     * 根据身份证查询体检人员
     * @param idc
     *
     * @return
     */
    @Transactional(readOnly = true)
    public List<TdTjBhk> findTdTjPsnByIdc(String idc){
        StringBuilder sb = new StringBuilder();
        sb.append("	SELECT T FROM TdTjBhk T WHERE 1 = 1 ");
        if(StringUtils.isNotBlank(idc)){
            sb.append(" AND  T.idc = '").append(idc).append("'");
        }
        List<TdTjBhk> findByHql = findByHql(sb.toString(),TdTjBhk.class);
        return findByHql;
    }

    @Transactional(readOnly = true)
    public TbTjSrvorg findTbTjSorg(Integer rid){
        StringBuilder sb = new StringBuilder();
        sb.append("	SELECT T FROM TbTjSrvorg T WHERE T.rid=").append(rid);
        List<TbTjSrvorg> list = em.createQuery(sb.toString()).getResultList();
        if (null!=list && list.size()>0) {
            return list.get(0);
        }
        return null;
    }
    /**
     * <p>方法描述：查询设备检测情况</p>
     * @MethodAuthor qrr,2018年11月28日,findInstCheckInfo
     * */
    public boolean findInstCheckInfo(Integer instId,String year) {
        boolean flag = false;
        StringBuilder sql = new StringBuilder();
        sql.append("select t.RECHECK,t.IF_CJ_HG,t.IF_FJ_HG from TD_TJ_RAD_CHECK_SUB t ");
        sql.append(" left join TB_TJ_RAD_CHECK_MAIN t1 on t1.rid = t.MAIN_ID");
        sql.append(" where 1=1 ");
        if (null!=instId) {
            sql.append(" and t.INST_ID =").append(instId);
        }
        sql.append(" and t1.state = '1'");
        if (StringUtils.isNotBlank(year)) {
            sql.append(" and to_char(t.CHECK_DATE,'yyyy') = '").append(year).append("'");
        }
        List<Object[]> list = this.findSqlResultList(sql.toString());
        if (null==list || list.size()==0) {
            flag = true;
        }else {
            boolean hgflag = false;
            for (Object[] obj : list) {
                if (null!=obj[0]&& "1".equals(obj[0].toString())) {//复检
                    if (null!=obj[2] && "0".equals(obj[2].toString())) {
                        hgflag = true;
                    }
                }else {
                    if (null!=obj[1]&& "0".equals(obj[1].toString())) {
                        hgflag = true;
                    }
                }
            }
            if (hgflag) {
                flag = true;
            }
        }
        return flag;
    }
    /**
     * <p>方法描述：查询人员检测情况
     * durYear:间隔年份</p>
     * @MethodAuthor qrr,2018年11月28日,findInstCheckInfo
     * */
    public boolean findPsnTjInfo(String idc,Integer durYear) {
        boolean flag = false;
        StringBuilder sql = new StringBuilder();
        sql.append("select 1 from TD_TJ_BHK t ");
        sql.append(" where 1=1 ");
        if (StringUtils.isNotBlank(idc)) {
            sql.append(" and t.IDC ='").append(idc).append("'");
        }
        if (null!=durYear) {
            Calendar instance = Calendar.getInstance();
            instance.add(Calendar.YEAR, -durYear);
            sql.append(" and t.BHK_DATE >=to_date('").append(DateUtils.formatDate(instance.getTime(), "yyyy-MM-dd")).append("','yyyy-MM-dd')");
        }
        int count = this.findCountBySql(sql.toString());
        if (count==0) {
            flag = true;
        }
        return flag;
    }
    /**
     * <p>方法描述：查询人员检测情况
     * durYear:间隔年份</p>
     * @MethodAuthor qrr,2018年11月28日,findInstCheckInfo
     * */
    public boolean findPsnCheckInfo(Integer psnId,String year) {
        boolean flag = false;
        StringBuilder sql = new StringBuilder();
        sql.append("select 1 from TD_TJ_RAD_CHECK_SUB t ");
        sql.append(" left join TB_TJ_RAD_CHECK_MAIN t1 on t1.rid = t.MAIN_ID");
        sql.append(" where 1=1 ");
        if (null!=psnId) {
            sql.append(" and t.PSN_ID =").append(psnId);
        }
        sql.append(" and t1.state = '1'");
        if (StringUtils.isNotBlank(year)) {
            sql.append(" and to_char(t.CHECK_DATE,'yyyy') = '").append(year).append("'");
        }
        int count = this.findCountBySql(sql.toString());
        if (count==0) {
            flag = true;
        }
        return flag;
    }

    /***
     *  <p>方法描述：根据sql获取关联id</p>
     *
     * @MethodAuthor maox,2019年9月16日,getIdBySql
     * @param sql
     * @return
     */
    public Integer getIdBySql(String sql) {
        List<Object[]> list = em.createNativeQuery(sql).getResultList();
        if (null != list && list.size() > 0) {
            Object obj = list.get(0);
            Integer id = Integer.valueOf(obj.toString());
            return id;
        }
        return 0;
    }

    @Transactional(readOnly = true)
    public List<TsSimpleCode> findCFTsSimpleCodesByTypeNoAndLevelCode(
            String typeNo, boolean ifAllUse, String levelCodeNo) {
        if (StringUtils.isNotBlank(typeNo)) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TsSimpleCode t ");
            sb.append(" where t.tsCodeType.codeTypeName in (").append(typeNo)
                    .append(") ");
            sb.append(" and t.extendS4 = '1'");
            if (ifAllUse) {
                sb.append(" and t.ifReveal= 1");
            }
            if (StringUtils.isNotBlank(levelCodeNo)) {
                sb.append("and (t.codeLevelNo like '").append(levelCodeNo)
                        .append(".%'");
                sb.append(" or t.codeLevelNo ='").append(levelCodeNo).append("')");
            }
            sb.append(" order by t.codeLevelNo ,t.codeNo ");
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    @Transactional(readOnly = true)
    public TsZone findZone(String zoneCode) {
        TsZone zone = null;
        StringBuilder sb = new StringBuilder();
        sb.append(" select t ");
        sb.append(" from TsZone t where t.ifReveal = 1 and t.zoneGb = '")
                .append(zoneCode).append("' ");

        List<TsZone> list = em.createQuery(sb.toString()).getResultList();
        if (null != list && list.size() > 0) {
            zone = list.get(0);
        }
        return zone;
    }

    @Transactional(readOnly = true)
    public Integer findBusId(String sql) {
        List<Object> list = em.createNativeQuery(sql).getResultList();
        if (null!=list && list.size()>0) {
            return new Integer(list.get(0).toString());
        }
        return null;
    }
    
    /***
     *  <p>方法描述：项目配置</p>
     *
     * @MethodAuthor maox,2020年4月26日,getStadItemList
     * @param unitId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Object[]> getStadItemList(Integer unitId,String itemIds) {
        if (null != unitId) {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT T1.CODE_NAME, T.ITEM_NAME, T.MSRUNT,case when T2.rid is null then  t.minval else t2.minval end");
            sql.append("	,case when T2.rid is null then  t.maxval else t2.maxval end,T2.RID,T1.RID AS CODEID,1,t.rid as itemId ");
            sql.append("	 FROM TB_TJ_ITEMS t");
            sql.append("	 LEFT JOIN TS_SIMPLE_CODE T1 ON T.ITEM_SORTID = T1.Rid");
            sql.append("	  LEFT JOIN  ");
            sql.append("				(SELECT A.RID,A.ITEM_ID,A.MINVAL,A.MAXVAL FROM TB_TJ_STAD_ITEMS a ");
            sql.append("				 LEFT JOIN TB_TJ_STAD_ITEMS_STATUS B ON A.MAIN_ID =B.RID");
            sql.append("				WHERE B.ORG_ID =").append(unitId).append(") t2 on t.rid = t2.ITEM_ID");
            sql.append("	WHERE 1 =1   AND T.JDGPTN = 2 AND T.STOP_TAG = 1 ");
            if(StringUtils.isNotBlank(itemIds)){
            	 sql.append("  AND	T.RID IN(").append(itemIds).append(")");
            }
           
            sql.append(" ORDER by t1.num,t1.rid, t.NUM ");
            
            List<Object[]> resultList = em.createNativeQuery(sql.toString()).getResultList();
            return resultList;
        }
        return null;
    }

    /**
     * 获取标准值配置
     * rid
     * 项目id ITEM_ID
     * 最小值 MINVAL
     * 最大值 MAXVAL
     * 判断模式 JDGPTN
     * 性别 SEX
     * 计量单位id MSRUNT_ID
     * @param unitId
     * @param itemIds
     * @return
     */
    @Transactional(readOnly = true)
    public List<Object[]> findStadItemList(Integer unitId,String itemIds){
        if(null != unitId){
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT A.RID,A.ITEM_ID,A.MINVAL,A.MAXVAL,A.JDGPTN,A.SEX,A.MSRUNT_ID ");
            sql.append(" FROM TB_TJ_STAD_ITEMS A ");
            sql.append(" LEFT JOIN TB_TJ_STAD_ITEMS_STATUS B ON A.MAIN_ID =B.RID ");
            sql.append(" WHERE B.ORG_ID = ").append(unitId);
            if(StringUtils.isNotBlank(itemIds)){
                sql.append(" AND A.ITEM_ID IN(").append(itemIds).append(")");
            }
            List<Object[]> resultList = em.createNativeQuery(sql.toString()).getResultList();
            return resultList;
        }
        return null;
    }
    
    @Transactional(readOnly = true)
    public List<TbTjItems> findTjItemsList() {
        StringBuilder sb = new StringBuilder();
        sb.append(" select t from TbTjItems t where t.stopTag = 1 ");
        sb.append(" and t.jdgptn = 2");
        sb.append(" order by t.tsSimpleCode.num, t.num ");
        return em.createQuery(sb.toString()).getResultList();
    }


    /** 获取定量的未删除的体检项目标准库 并加载体检项目特殊标准和项目与计量单位关系配置 */
    @Transactional(readOnly = true)
    public List<TbTjItems> findItemListWithUnitAndSpe(){
        StringBuilder sb = new StringBuilder();
        sb.append(" select t from TbTjItems t where t.stopTag = 1 ");
        sb.append(" and t.jdgptn = 2");
        sb.append(" order by t.tsSimpleCode.num, t.num ");
        List<TbTjItems> list = em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0){
            for(TbTjItems t : list){
                //加载 体检项目特殊标准和项目与计量单位关系配置 以及定性项目描述
                if(null != t.getTbTjItemsUnitList()){
                    t.getTbTjItemsUnitList().size();
                }
                if(null != t.getTbTjItemsSpeList()){
                    t.getTbTjItemsSpeList().size();
                }
                if(null != t.getTbTjRstdescs()){
                    t.getTbTjRstdescs().size();
                }
            }
        }
        return list;
    }

    /**
     * 获取定量的未删除的体检项目标准库
     * 注意 不可改变字段顺序 新增字段需加在最后
     * RID
     * 性别1男 2女 SEX
     * 项目编码ITEM_CODE
     * 项目名称ITEM_NAME
     * 计量单位id MSRUNT_ID
     * 业务分类id ITEM_SORTID
     * 业务分类名称 CODE_NAME
     * 最小值 MINVAL
     * 最大值 MAXVAL
     * @return
     */
    @Transactional(readOnly = true)
    public List<Object[]> findTbTjBaseItemsSQLList(){
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT T .RID,T .SEX,T .ITEM_CODE,T .ITEM_NAME,T .MSRUNT_ID,T .ITEM_SORTID,T2.CODE_NAME,T.MINVAL,T.MAXVAL,T2.EXTENDS1 ");
        sql.append(" FROM TB_TJ_ITEMS T ");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T .ITEM_SORTID = T2.RID ");
        sql.append(" WHERE T .JDGPTN = 2 AND STOP_TAG = 1 ");
        sql.append(" ORDER BY T2.NUM,T.NUM ");
        return em.createNativeQuery(sql.toString()).getResultList();
    }

    /**
     * 获取特定项目的体检项目特殊标准
     * 注意 不可改变字段顺序 新增字段需加在最后
     * 这里是为了获取 通用性别项目的体检项目特殊标准 参数itemIds为通用性别项目的rid+英文逗号相隔
     * 项目rid ITEM_ID
     * 最小值MINVAL
     * 最大值MAXVAL
     * 性别SEX（已经转换成男1 女2 通用null）
     * @param itemIds
     * @return
     */
    @Transactional(readOnly = true)
    public List<Object[]> findItemsSpeSQLList(String itemIds){
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT T .ITEM_ID,T .MINVAL,T .MAXVAL, ");
        sql.append(" ( CASE T .SEX WHEN '男' THEN 1 WHEN '1' THEN 1 WHEN '女' THEN 2 WHEN '2' THEN 2 ELSE NULL END ) SEX ");
        sql.append(" FROM TB_TJ_ITEMS_SPE T ");
        sql.append(" WHERE T .ONGUARD_STATEID IS NULL AND T .BAD_RSN_ID IS NULL ");
        if(StringUtils.isNotBlank(itemIds)){
            sql.append(" AND T .ITEM_ID IN (");
            sql.append(itemIds);
            sql.append(")");
        }
        return em.createNativeQuery(sql.toString()).getResultList();
    }

    /**
     * 获取项目与计量单位关系配置
     * 注意 不可改变字段顺序 新增字段需加在最后
     * 项目rid ITEM_ID
     * 计量单位rid MSRUNT_ID
     * 最小值MINVAL
     * 最大值MAXVAL
     * 是否默认IF_DEFAUT 0否 1是
     * @return
     */
    @Transactional(readOnly = true)
    public List<Object[]> findUnitRelSQLList(){
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT T .ITEM_ID,T .MSRUNT_ID,T .MINVAL,T .MAXVAL,T .IF_DEFAUT ");
        sql.append(" FROM TB_TJ_ITEM_UNIT_REL T ");
        return em.createNativeQuery(sql.toString()).getResultList();
    }

    /**
     * 查询定性项目描述中的项目rid
     * 去重后的项目rid ITEM_ID
     * @return
     */
    @Transactional(readOnly = true)
    public List<Object> findRstDescItemList(){
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT DISTINCT T .ITEM_ID FROM  TB_TJ_RSTDESC T ");
        return em.createNativeQuery(sql.toString()).getResultList();
    }

    @Transactional(readOnly = true)
    public TbTjStadItemsStatus findTbTjStadItemsStatus(Integer rid){
        StringBuilder sb = new StringBuilder();
        sb.append("	SELECT T FROM TbTjStadItemsStatus T WHERE T.fkByOrgId.rid=").append(rid);
        List<TbTjStadItemsStatus> list = em.createQuery(sb.toString()).getResultList();
        if (null!=list && list.size()>0) {
            return list.get(0);
        }
        return null;
    }
    
    @Transactional(readOnly = true)
    public List<Object[]> findDlItemAll(Integer unitId) {
        if (null != unitId) {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT T1.CODE_NAME, T.ITEM_NAME, T.MSRUNT,case when T2.rid is null then  t.minval else t2.minval end");
            sql.append("	,case when T2.rid is null then  t.maxval else t2.maxval end,T2.RID,T1.RID AS CODEID,1,t.rid as itemId ");
            sql.append("	 FROM TB_TJ_ITEMS t");
            sql.append("	 LEFT JOIN TS_SIMPLE_CODE T1 ON T.ITEM_SORTID = T1.Rid");
            sql.append("	  LEFT JOIN  ");
            sql.append("				(SELECT A.RID,A.ITEM_ID,A.MINVAL,A.MAXVAL FROM TB_TJ_STAD_ITEMS a ");
            sql.append("				 LEFT JOIN TB_TJ_STAD_ITEMS_STATUS B ON A.MAIN_ID =B.RID");
            sql.append("				WHERE B.ORG_ID =").append(unitId).append(") t2 on t.rid = t2.ITEM_ID");
            sql.append("	WHERE 1 =1   AND T.JDGPTN = 2 AND T.STOP_TAG = 1 ");
           
            sql.append(" ORDER by t1.rid,t.NUM ");
            
            List<Object[]> resultList = em.createNativeQuery(sql.toString()).getResultList();
            return resultList;
        }
        return null;
    }
    
    /***
     *  <p>方法描述：更新</p>
     *
     * @MethodAuthor maox,2020年4月26日,saveStadItemStatus
     * @param tbTjStadItemsStatus
     */
    @Transactional(readOnly = false)
    public void saveStadItemStatus(TbTjStadItemsStatus tbTjStadItemsStatus) {
    	
        if (null != tbTjStadItemsStatus) {
            if( null != tbTjStadItemsStatus.getRid())	{
            	//先删除子表
            	StringBuilder sql = new StringBuilder("");
                sql.append("DELETE TB_TJ_STAD_ITEMS a where a.MAIN_ID = ").append(tbTjStadItemsStatus.getRid());
            	em.createNativeQuery(sql.toString()).executeUpdate();
            	tbTjStadItemsStatus = (TbTjStadItemsStatus) this.updateObj(tbTjStadItemsStatus);
            }else{
            	tbTjStadItemsStatus = (TbTjStadItemsStatus) this.saveObj(tbTjStadItemsStatus);
            }
        }
    }
    
    /***
     *  <p>方法描述：撤销操作</p>
     *
     * @MethodAuthor maox,2020年4月26日,cancleStadItemStatus
     * @param tbTjStadItemsStatus
     */
    @Transactional(readOnly = false)
    public void cancleStadItemStatus(TbTjStadItemsStatus tbTjStadItemsStatus) {
    	StringBuilder sql = new StringBuilder("");
        sql.append("update TB_TJ_STAD_ITEMS_STATUS a set a.state =0  where a.rid = ").append(tbTjStadItemsStatus.getRid());
    	em.createNativeQuery(sql.toString()).executeUpdate();
    }
    
    @Transactional(readOnly = true)
	public List<TsSimpleCode> findAllSimpleCodes(String typeNo) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (").append(typeNo)
					.append(") order by t.num ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}
    
    @Transactional(readOnly = true)
    public List<TbTjItems> findSelectTjItemsList(String itemIds) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select t from TbTjItems t where t.stopTag = 1 ");
        sb.append(" and t.jdgptn = 2");
        if(StringUtils.isNotBlank(itemIds)){
        	sb.append(" and t.rid in(").append(itemIds).append(")");
        }
        sb.append(" order by t.tsSimpleCode.num, t.num ");
        return em.createQuery(sb.toString()).getResultList();
    }

    /**
     * <p>方法描述：查询检查健康机构</p>
     * @MethodAuthor qrr,2019年9月4日,searchTjOrgInfo
     * */
    public List<TdZwTjorginfoComm> searchTjOrgInfoList(String zoneCode) {
        StringBuffer sql = new StringBuffer();
        sql.append("select t.rid,t.ORG_NAME from TD_ZW_TJORGINFO t ");
        sql.append(" left join ts_unit t1 on t1.rid = t.org_id ");
        sql.append(" left join ts_zone t2 on t2.rid = t1.zone_id where 1=1");
        if (StringUtils.isNotBlank(zoneCode)) {
            sql.append(" and t2.zone_gb like '").append(ZoneUtil.zoneSelect(zoneCode)).append("%'");
            sql.append(" and t2.zone_type>=").append(ZoneUtil.getZoneType(zoneCode));
            sql.append(" and t2.zone_type<=").append(ZoneUtil.getZoneType(zoneCode)+1);
        }
        sql.append(" and t.STATE = 1");
        List<Object[]> list = super.findSqlResultList(sql.toString());
        List<TdZwTjorginfoComm> result = new ArrayList<>();
        if (null!=list && list.size()>0) {
            for (Object[] obj : list) {
                TdZwTjorginfoComm tjorginfo = new TdZwTjorginfoComm();
                tjorginfo.setRid(Integer.valueOf(obj[0].toString()));
                tjorginfo.setOrgName(null!=obj[1]?obj[1].toString():"");
                result.add(tjorginfo);
            }
        }
        return result;
    }

    /**
     * <p>方法描述：保存重点危害因素审核流程</p>
     * @MethodAuthor rcj,2020年11月3日,searchTjOrgInfo
     * action 1=保存 ； 2=提交; 3=撤销
     * */
    @Transactional(readOnly = false)
    public void upsertTdZwyjBsnBhkEntity(TdZwyjBsnCheck tdZwyjBsnCheck, Integer zoneType, Integer action, String defaultAuditAdv, TsUnit tsunit,boolean ifCityDirect){
        // 保存
        if(action == 1 ){
            //初审保存
            if(zoneType == 4 && tdZwyjBsnCheck.getState()==null){
                tdZwyjBsnCheck.setState(1);
            }
            if(zoneType == 3 && ifCityDirect){
                tdZwyjBsnCheck.setState(3);
            }
            upsertEntity(tdZwyjBsnCheck);
        }else if(action == 2){
            //初审提交
            if(zoneType == 4 || (zoneType == 3 &&ifCityDirect)){
                if(ifCityDirect){
                    tdZwyjBsnCheck.setState(5);
                    tdZwyjBsnCheck.setCityRst2(null);
                    tdZwyjBsnCheck.setProAuditAdv(null);
                }else{
                    tdZwyjBsnCheck.setState(3);
                }

                tdZwyjBsnCheck.setFkByCountyChkOrgid(tsunit);
                tdZwyjBsnCheck.setCountySmtDate(new Date());
                tdZwyjBsnCheck.setCityRst(null);
                tdZwyjBsnCheck.setCityAuditAdv(null);
            }else if(zoneType == 3 ){
                if(tdZwyjBsnCheck.getCityRst()==1){
                    tdZwyjBsnCheck.setState(5);
                }else{
                    tdZwyjBsnCheck.setState(2);
                }
                tdZwyjBsnCheck.setFkByCiytChkOrgid(tsunit);
                tdZwyjBsnCheck.setCitySmtDate(new Date());
                tdZwyjBsnCheck.setCityRst2(null);
                tdZwyjBsnCheck.setProAuditAdv(null);
            }else if(zoneType == 2 ){
                if(tdZwyjBsnCheck.getCityRst2()==1){
                    tdZwyjBsnCheck.setState(6);
                }else{
                    tdZwyjBsnCheck.setState(4);
                }
                tdZwyjBsnCheck.setFkByProChkOrgid(tsunit);
                tdZwyjBsnCheck.setProSmtDate(new Date());
            }
            //审核意见:选择“通过”且审核意见无值时加载配置的意见
            if(ifCityDirect && zoneType ==3 && null != tdZwyjBsnCheck.getCityRst() && tdZwyjBsnCheck.getCityRst() == 1 &&  StringUtils.isBlank(tdZwyjBsnCheck.getCityAuditAdv())){
                tdZwyjBsnCheck.setCityAuditAdv(defaultAuditAdv);
            }
            //审核意见:选择“通过”且审核意见无值时加载配置的意见
            if(zoneType ==2 && null != tdZwyjBsnCheck.getCityRst2() && tdZwyjBsnCheck.getCityRst2() == 1 &&  StringUtils.isBlank(tdZwyjBsnCheck.getProAuditAdv())){
                tdZwyjBsnCheck.setProAuditAdv(defaultAuditAdv);
            }
            upsertEntity(tdZwyjBsnCheck);
        }else if(action == 3){
            //初审撤销
            if(zoneType == 4 ){
                tdZwyjBsnCheck.setState(1);
            }else if(zoneType == 3 ){
                tdZwyjBsnCheck.setState(3);
            }else if(zoneType == 2 ){
                if(ifCityDirect){
                    tdZwyjBsnCheck.setState(3);
                }else{
                    tdZwyjBsnCheck.setState(5);
                }

            }
            upsertEntity(tdZwyjBsnCheck);
        }
    }

    /**
     * <p>
     *     方法描述：初始化异常情况
     * </p>
     *
     * @MethodAuthor yph,2021年05月13日
     */
    public List<String> initUnAbnormalsList(TdTjBhk tdTjBhk){
        List<String> unAbnormalsList = new ArrayList<>();
        if(tdTjBhk.getIfAbnomal()!=null && tdTjBhk.getIfAbnomal()==1){
            StringBuilder sql=new StringBuilder();
            sql.append(" select T.rid,T.TYPE,T.ABNOMAL_INFO from TD_TJ_BHK_ABNOMAL T where BHK_ID= ").append(tdTjBhk.getRid());
            sql.append(" order by T.TYPE ");
            List<Object[]> objs = this.findSqlResultList(sql.toString());
            if(!CollectionUtils.isEmpty(objs)){
                for (Object[] obj : objs) {
                    if(obj[2]==null){
                        continue;
                    }
                    unAbnormalsList.add(obj[2].toString());
                }
            }
            //GBZ188不规范说明
            if(null!=tdTjBhk.getIfInteitmLack()&&tdTjBhk.getIfInteitmLack()==1&&StringUtils.isNotBlank(tdTjBhk.getLackMsg())){
                //GBZ188不规范说明
                unAbnormalsList.add(tdTjBhk.getLackMsg());
            }
        }
        return unAbnormalsList;

    }

    /**
     * @Description: 修改情况说明
     *
     * @MethodAuthor pw,2022年01月26日
     */
    @Transactional(readOnly = false)
    public void updateInfoMsg(Map<Integer, List<Integer>> stateWithRidListMap , String infoMsg){
        if(CollectionUtils.isEmpty(stateWithRidListMap) || StringUtils.isBlank(infoMsg)){
            return;
        }
        StringBuffer buffer = new StringBuffer();
        Map<String, Object> paramMap = new HashMap<>();
        List<Integer> ridList = new ArrayList<>();
        buffer.append("UPDATE TD_TJ_BHK SET ORG_STATE_DESC='")
                .append(infoMsg)
                .append("',MODIFY_DATE=SYSDATE,DEAL_COMPLETE_DATE=SYSDATE,COUNTY_SMT_DATE=NULL,")
                .append("COUNTY_RST=NULL,COUNTY_AUDIT_ADV=NULL,COUNTY_CHK_ORGID=NULL,CITY_SMT_DATE=NULL,CITY_RST=NULL,")
                .append("CITY_AUDIT_ADV=NULL,CIYT_CHK_ORGID=NULL,PRO_SMT_DATE=NULL,CITY_RST2=NULL,PRO_CHK_ORGID=NULL,")
                .append("PRO_AUDIT_ADV=NULL,PRO_CHK_PSNID=NULL,COUNTY_CHECK_WAY=NULL,CITY_CHECK_WAY=NULL,PRO_CHECK_WAY=NULL,")
                .append("MODIFY_MANID=")
                .append(Global.getUser().getRid())
                .append(" ,STATE=:state ")
                .append(" WHERE RID IN (:ridList) ");
        List<Integer> allBhkRidList = new ArrayList<>();
        for(Map.Entry<Integer, List<Integer>> mapEntity : stateWithRidListMap.entrySet()){
            Integer state = mapEntity.getKey();
            List<Integer> bhkRidList = mapEntity.getValue();
            if(null == state || CollectionUtils.isEmpty(bhkRidList)){
                continue;
            }
            allBhkRidList.addAll(bhkRidList);
            paramMap.put("state", state);
            for(Integer bhkRid : bhkRidList){
                ridList.add(bhkRid);
                if(ridList.size() == 1000){
                    paramMap.put("ridList", ridList);
                    this.executeSql(buffer.toString(),paramMap);
                    ridList.clear();
                }
            }
            if(!CollectionUtils.isEmpty(ridList)){
                paramMap.put("ridList", ridList);
                this.executeSql(buffer.toString(),paramMap);
            }
        }
    }

    /**
     *  <p>方法描述：获取历次审核</p>
     * @MethodAuthor hsj   
     */
    @Transactional(readOnly=false)
    public List<Object[]>  findHisotryList(Integer rid,Integer type){
        if(null == rid){
            return Collections.EMPTY_LIST;
        }
        //查出体检主表 获取体检机构rid与体检编号
        String bhkSql = "SELECT T.BHK_CODE,T.BHKORG_ID,'' FROM TD_TJ_BHK T WHERE T.RID="+rid;
        List<Object[]> queryBhkInfoList = this.findSqlResultList(bhkSql);
        String bhkCode = null;
        Integer bhkOrgId = null;
        if(!CollectionUtils.isEmpty(queryBhkInfoList)){
            Object[] objArr = queryBhkInfoList.get(0);
            bhkCode = null == objArr[0] ? null : objArr[0].toString();
            bhkOrgId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
        }
        if(null == bhkCode || null == bhkOrgId){
            return Collections.EMPTY_LIST;
        }
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select T1.CART_TYPE,T1.AUDIT_ADV,T1.AUDIT_MAN,T1.CREATE_DATE,T2.CRPT_NAME,T3.IF_CITY_DIRECT,T3.IF_PROV_DIRECT,T1.OPER_FLAG from TD_ZW_BGK_FLOW T1 ");
        sqlBuilder.append(" LEFT JOIN TD_TJ_BHK T2 on T1.BHK_CODE = T2.BHK_CODE and T1.BHKORG_ID=T2.BHKORG_ID ");
        sqlBuilder.append("   LEFT JOIN TB_TJ_CRPT T4 ON T2.ENTRUST_CRPT_ID = T4.RID ");
        sqlBuilder.append(" LEFT JOIN TS_ZONE T3 ON T4.ZONE_ID = T3.RID " );
        //用体检机构rid与体检编号查询 避免体检主表被删除 之前的审核意见看不见
        sqlBuilder.append(" where T1.BHK_CODE= ").append(" '").append(bhkCode).append("' ")
                .append(" and T1.BHKORG_ID= ").append(bhkOrgId)
                .append(" and T1.CART_TYPE =  ").append(type);
        sqlBuilder.append(" ORDER BY T1.CREATE_DATE desc ");
        List<Object[]> sqlResultList = this.findSqlResultList(sqlBuilder.toString());
        return sqlResultList;
    }

    /**
     * <p>方法描述：历次审核 </p>
     * pw 2024/1/30
     **/
    @Transactional(readOnly=false)
    public List<Object[]>  findHisotryList(String bhkCode, Integer bhkOrgId,Integer type){
        if(null == bhkCode || null == bhkOrgId){
            return Collections.EMPTY_LIST;
        }
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select T1.CART_TYPE,T1.AUDIT_ADV,T1.AUDIT_MAN,T1.CREATE_DATE,T2.CRPT_NAME,T3.IF_CITY_DIRECT,T3.IF_PROV_DIRECT,T1.OPER_FLAG from TD_ZW_BGK_FLOW T1 ");
        sqlBuilder.append(" LEFT JOIN TD_TJ_BHK T2 on T1.BHK_CODE = T2.BHK_CODE and T1.BHKORG_ID=T2.BHKORG_ID ");
        sqlBuilder.append("   LEFT JOIN TB_TJ_CRPT T4 ON T2.ENTRUST_CRPT_ID = T4.RID ");
        sqlBuilder.append(" LEFT JOIN TS_ZONE T3 ON T4.ZONE_ID = T3.RID " );
        //用体检机构rid与体检编号查询 避免体检主表被删除 之前的审核意见看不见
        sqlBuilder.append(" where T1.BHK_CODE= ").append(" '").append(bhkCode).append("' ")
                .append(" and T1.BHKORG_ID= ").append(bhkOrgId)
                .append(" and T1.CART_TYPE =  ").append(type);
        sqlBuilder.append(" ORDER BY T1.CREATE_DATE desc ");
        List<Object[]> sqlResultList = this.findSqlResultList(sqlBuilder.toString());
        return sqlResultList;
    }
    /**********************************项目组合*****************************************/

    /**
     * 查找没有与项目组合关联的体检项目
     * @return 体检项目集合
     * <AUTHOR>
     * @history 2014年9月10日
     */
    @Transactional(readOnly = true)
    public List<TbTjItems> initTjItemsList(Integer combId) {
        if (null != combId) {
            StringBuilder sql = new StringBuilder("");
            sql.append(" select t from TbTjItems t where");
            sql.append(" t.rid  not in (select a.tbTjItems.rid ");
            sql.append(" from TbZwtjItemcmbItems a where a.tsSimpleCode.rid in (").append(combId).append(") ) ");
            sql.append(" and t.stopTag = 1 order by t.tsSimpleCode.codeLevelNo,t.num,t.itemCode ");
            return em.createQuery(sql.toString()).getResultList();
        }
        return null;
    }
    /**
     * 保存项目组合关系
     * @param list
     *            体检项目list
     * @param selItem
     *            项目组合
     * @param userRid
     *            修改人员rid
     * <AUTHOR>
     * @history 2014年9月10日
     */
    @Transactional(readOnly = false)
    public void saveZwtjItem(List<TbTjItems> list, TsSimpleCode selItem, Integer userRid) {
        if (null != userRid && null != selItem) {
            TbZwtjItemcmbItems item = null;
            // 遍历选择的体检项目，保存至关系表中
            for (TbTjItems t : list) {
                StringBuilder sb = new StringBuilder();
                sb.append(" SELECT T.RID FROM TB_ZWTJ_ITEMCMB_ITEMS T WHERE T.ITEM_ID =? AND T.ITEM_CMBID =?");
                Query query = this.em.createNativeQuery(sb.toString());
                query.setParameter(1, t.getRid());
                query.setParameter(2, selItem.getRid());
                List resultList = query.getResultList();
                if (null == resultList || resultList.size() == 0) {
                    item = new TbZwtjItemcmbItems();
                    item.setTbTjItems(t);
                    item.setTsSimpleCode(selItem);
                    item.setStopTag((short) 1);
                    item.setCreateDate(new Date());
                    item.setCreateManid(userRid);
                    item.setModifyDate(new Date());
                    item.setModifyManid(userRid);
                    this.save(item);
                }
            }
        }
    }

    /**
     * 根据选择的项目组合id获取项目关系表中的内容
     * @param selItem
     *            项目组合
     * @return 项目关系表list
     * <AUTHOR>
     * @history 2014年9月10日
     */
    @Transactional(readOnly = true)
    public List<TbZwtjItemcmbItems> initZwtijItemListById(TsSimpleCode selItem) {
        if (null != selItem) {
            StringBuilder sql = new StringBuilder("");
            sql.append(" select t from TbZwtjItemcmbItems t where t.tsSimpleCode.rid = ").append(selItem.getRid());
            sql.append(" order by t.tbTjItems.tsSimpleCode.codeLevelNo,t.tbTjItems.num,t.tbTjItems.itemCode ");
            return em.createQuery(sql.toString()).getResultList();
        }
        return null;
    }
    /**
     *  <p>方法描述：修改项目组合中 判定方式</p>
     * @MethodAuthor hsj 2022/3/5 14:58
     */
    @Transactional(readOnly = false)
    public void updateDeterWay(Integer way, List<Integer> rids, Integer userRid) {
        if ( null != userRid && !CollectionUtils.isEmpty(rids)) {
            StringBuilder sql = new StringBuilder("");
            sql.append(" UPDATE TB_ZWTJ_ITEMCMB_ITEMS SET DETER_WAY = ").append(way);
            sql.append(", MODIFY_MANID = ").append(userRid);
            sql.append(", MODIFY_DATE =  SYSDATE ");
            sql.append(" WHERE RID in (").append(StringUtils.list2string(rids,",")).append(")");
            em.createNativeQuery(sql.toString()).executeUpdate();
        }
    }

    /**
     * <p>方法描述：根据体检rid集合 获取 主动监测异常信息</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-26
     **/
    public Map<Integer,List<Object[]>> findBhkAbnomalsByBhkRids(List<Integer> bhkRidList) {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT t.rid,t.TYPE,t.ABNOMAL_INFO,t.BHK_ID ");
        sql.append(" from  TD_TJ_BHK_ABNOMAL t ");
        sql.append(" left join TD_TJ_BHK T1 on t.BHK_ID=T1.RID ");
        sql.append(" WHERE t.BHK_ID IN (");
        sql.append(StringUtils.list2string(bhkRidList,","));
        sql.append(")");
        sql.append(" order by t.TYPE ");
        List<Object[]> objlist= em.createNativeQuery(sql.toString()).getResultList();
        Map<Integer,List<Object[]>>  abnomalMap=new HashMap<>();
        if(!CollectionUtils.isEmpty(bhkRidList)){
            for (Object[] objects : objlist) {
                if(abnomalMap.get(Integer.parseInt(objects[3].toString()))!=null){
                    abnomalMap.get(Integer.parseInt(objects[3].toString())).add(objects);
                }else{
                    List<Object[]> objs=new ArrayList<>();
                    objs.add(objects);
                    abnomalMap.put(Integer.parseInt(objects[3].toString()),objs);
                }
            }
        }
        return abnomalMap;
    }

    /**
     * <p>方法描述：通过体检项目标准库TB_TJ_ITEMS的rid获取对应的国家接口标准 </p>
     * @MethodAuthor： pw 2022/9/19
     **/
    public List<TbTjItemsGj> findTbTjItemsGjByMainRid(Integer mainRid){
        if(null == mainRid){
            return Collections.EMPTY_LIST;
        }
        String hql = "select t from TbTjItemsGj t where t.fkByItemId.rid="+mainRid+" order by t.type,t.sex,t.fkByMsruntId.num ";
        return this.findByHql(hql, TbTjItemsGj.class);
    }

    /**
     * <p>方法描述：添加或修改体检项目 </p>
     * @MethodAuthor： pw 2022/9/19
     **/
    public void saveOrUpdateTbTjItems(TbTjItems tbTjItems, List<TbTjItemsGj> showTbtjItemsGjList,
                                      List<Integer> removeGjRidList) {
        if(null == tbTjItems){
            return;
        }
        List<Object> saveObjectList = new ArrayList<>();
        List<Object> updateObjectList = new ArrayList<>();
        if(null == tbTjItems.getRid()){
            preInsert(tbTjItems);
            saveObjectList.add(tbTjItems);
        }else{
            preUpdate(tbTjItems);
            updateObjectList.add(tbTjItems);
        }
        if(tbTjItems.getJdgptn()==2 && !CollectionUtils.isEmpty(showTbtjItemsGjList)){
            for(TbTjItemsGj itemsGj : showTbtjItemsGjList){
                if(null == itemsGj.getRid()){
                    preInsert(itemsGj);
                    saveObjectList.add(itemsGj);
                }else{
                    preUpdate(itemsGj);
                    updateObjectList.add(itemsGj);
                }
            }
        }
        if(!CollectionUtils.isEmpty(removeGjRidList)){
            String deleteSql = " DELETE FROM TB_TJ_ITEMS_GJ T WHERE T.RID IN(:deleteRidList) ";
            Map<String,Object> paramsMap = new HashMap<>();
            List<List<Integer>> removeRidGroup = StringUtils.splitListProxy(removeGjRidList, 1000);
            for(List<Integer> deleteRidList : removeRidGroup){
                paramsMap.put("deleteRidList", deleteRidList);
                this.executeSql(deleteSql, paramsMap);
            }
        }
        //非定量 删除国家接口标准
        if(null != tbTjItems.getRid() && tbTjItems.getJdgptn()!=2){
            String deleteSql = " DELETE FROM TB_TJ_ITEMS_GJ T WHERE T.ITEM_ID = "+tbTjItems.getRid();
            this.executeSql(deleteSql, null);
        }
        if(!CollectionUtils.isEmpty(saveObjectList)){
            this.saveBatchObjs(saveObjectList);
        }
        if(!CollectionUtils.isEmpty(updateObjectList)){
            this.updateBatchObjs(updateObjectList);
        }
    }

    /**
     * <p>方法描述：通过体检主表rid获取异常信息 </p>
     * pw 2023/12/20
     **/
    @Transactional(readOnly = true)
    public Map<Integer, List<String>> findAbnormalInfoByBhkRids(List<Integer> bhkRidList) {
        if (CollectionUtils.isEmpty(bhkRidList)) {
            return Collections.emptyMap();
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT T.RID ,T.IF_INTEITM_LACK ,T.LACK_MSG ,T1.ABNOMAL_INFO ")
                .append("  FROM TD_TJ_BHK T  ")
                .append("  LEFT JOIN TD_TJ_BHK_ABNOMAL T1 ON T1.BHK_ID = T.RID ")
                .append("  WHERE T.IF_ABNOMAL=1 AND T.RID IN(:bhkRidList) order by T1.TYPE ");
        String querySql = sqlBuffer.toString();
        Map<String,Object> paramMap = new HashMap<>();
        List<List<Integer>> groupList = StringUtils.splitListProxy(bhkRidList, 1000);
        List<Object[]> allResultList = new ArrayList<>();
        for (List<Integer> curList : groupList) {
            paramMap.put("bhkRidList", curList);
            List<Object[]> queryResultList = this.findDataBySqlNoPage(querySql, paramMap);
            if (CollectionUtils.isEmpty(queryResultList)) {
                continue;
            }
            allResultList.addAll(queryResultList);
        }
        if (CollectionUtils.isEmpty(allResultList)) {
            return Collections.emptyMap();
        }
        Map<Integer,List<String>> middleMap = new HashMap<>();
        Map<Integer,String> lackMap = new HashMap<>();
        for (Object[] objArr : allResultList) {
            Integer bhkRid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            if (null == bhkRid) {
                continue;
            }
            Integer ifInteiemLack = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
            String lackMsg = StringUtils.objectToString(objArr[2]);
            String abnomalInfo = StringUtils.objectToString(objArr[3]);
            List<String> tmpList = middleMap.get(bhkRid);
            if (null == tmpList) {
                tmpList = new ArrayList<>();
            }
            if (null != ifInteiemLack && 1 == ifInteiemLack && StringUtils.isNotBlank(lackMsg) ) {
                lackMap.put(bhkRid, lackMsg);
            }
            if (StringUtils.isNotBlank(abnomalInfo) && !tmpList.contains(abnomalInfo)) {
                tmpList.add(abnomalInfo);
            }
            middleMap.put(bhkRid, tmpList);
        }
        if (!CollectionUtils.isEmpty(lackMap)) {
            for (Integer key : lackMap.keySet()) {
                List<String> tmpList = middleMap.get(key);
                if (null == tmpList) {
                    tmpList = new ArrayList<>();
                }
                tmpList.add(lackMap.get(key));
                middleMap.put(key, tmpList);
            }
        }
        return middleMap;
    }

    /**
     * <p>方法描述：获取人员不在主动监测名单中的数据 </p>
     * pw 2023/12/19
     **/
    public List<Object[]> unExistJcTaskPsn(List<Integer> bhkRidList) {
        if (CollectionUtils.isEmpty(bhkRidList)) {
            return Collections.emptyList();
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT T.PERSON_NAME,T.RID FROM TD_TJ_BHK T  ")
                .append("  LEFT JOIN TB_TJ_SRVORG T1 ON T.BHKORG_ID = T1.RID ")
                .append("  WHERE T.JC_TYPE = 2 AND NVL(T.IF_RHK,0)=0 ")
                .append("  AND NOT EXISTS (")
                .append("    SELECT 1 FROM TB_TJ_JC_TASK K ")
                .append("    INNER JOIN TB_TJ_JC_TASK_PSN K1 ON K1.MAIN_ID = K.RID ")
                .append("    INNER JOIN TB_TJ_JC_TASK_BADRSN K2 ON K2.MAIN_ID = K1.RID ")
                .append("    WHERE K.ORG_ID = T1.REG_ORGID AND K.CRPT_ID = T.ENTRUST_CRPT_ID ")
                .append("    AND T.CARD_TYPE_ID = K1.CARD_TYPE_ID AND T.IDC = K1.IDC  ")
                .append("    AND EXTRACT (YEAR FROM T.BHK_DATE)=K.YEAR ")
                .append("    AND K.STATE=2 ")
                .append("    AND T.ONGUARD_STATEID=K1.ONGUARD_STATEID ")
                .append("    AND K2.BADRSN_ID IN (SELECT M.BADRSN_ID FROM TD_TJ_BADRSNS M WHERE M.IF_ZD_JC=1 AND M.BHK_ID=T.RID) ")
                .append("  ) AND T.RID IN (:bhkRidList) ");
        String querySql = sqlBuffer.toString();
        List<Object[]> resultList = new ArrayList<>();
        List<List<Integer>> groupList = StringUtils.splitListProxy(bhkRidList, 1000);
        Map<String,Object> paramMap = new HashMap<>();
        for (List<Integer> curList : groupList) {
            paramMap.put("bhkRidList",curList);
            List<Object[]> queryResultList = this.findDataBySqlNoPage(querySql,paramMap);
            if (CollectionUtils.isEmpty(queryResultList)) {
                continue;
            }
            resultList.addAll(queryResultList);
        }
        return resultList;
    }

    /**
     * <p>方法描述：获取已经绑定了主动监测数据的数据 </p>
     * pw 2023/12/19
     **/
    public List<Object[]> findJcTaskPsnBhkCodeNotEmpty(List<Integer> bhkRidList) {
        if (CollectionUtils.isEmpty(bhkRidList)) {
            return Collections.emptyList();
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT T.PERSON_NAME,T.RID FROM TD_TJ_BHK T  ")
                .append("  LEFT JOIN TB_TJ_SRVORG T1 ON T.BHKORG_ID = T1.RID ")
                .append("  WHERE T.JC_TYPE = 2 AND NVL(T.IF_RHK,0)=0 ")
                .append("  AND EXISTS (")
                .append("    SELECT 1 FROM TB_TJ_JC_TASK K ")
                .append("    INNER JOIN TB_TJ_JC_TASK_PSN K1 ON K1.MAIN_ID = K.RID ")
                .append("    INNER JOIN TB_TJ_JC_TASK_BADRSN K2 ON K2.MAIN_ID = K1.RID ")
                .append("    WHERE K.ORG_ID = T1.REG_ORGID AND K.CRPT_ID = T.ENTRUST_CRPT_ID ")
                .append("    AND T.CARD_TYPE_ID = K1.CARD_TYPE_ID AND T.IDC = K1.IDC  ")
                .append("    AND EXTRACT (YEAR FROM T.BHK_DATE)=K.YEAR ")
                .append("    AND K1.BHK_CODE IS NOT NULL ")
                .append("    AND K1.BHK_CODE != T.BHK_CODE ")
                .append("    AND K.STATE=2 ")
                .append("    AND T.ONGUARD_STATEID=K1.ONGUARD_STATEID ")
                .append("    AND K2.BADRSN_ID IN (SELECT M.BADRSN_ID FROM TD_TJ_BADRSNS M WHERE M.IF_ZD_JC=1 AND M.BHK_ID=T.RID) ")
                .append("  ) AND T.RID IN (:bhkRidList) ");
        String querySql = sqlBuffer.toString();
        List<Object[]> resultList = new ArrayList<>();
        List<List<Integer>> groupList = StringUtils.splitListProxy(bhkRidList, 1000);
        Map<String,Object> paramMap = new HashMap<>();
        for (List<Integer> curList : groupList) {
            paramMap.put("bhkRidList",curList);
            List<Object[]> queryResultList = this.findDataBySqlNoPage(querySql,paramMap);
            if (CollectionUtils.isEmpty(queryResultList)) {
                continue;
            }
            resultList.addAll(queryResultList);
        }
        return resultList;
    }

    @Transactional(readOnly = false)
    public void fillJcTaskPsnBhkCode(List<Integer> allBhkRidList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(allBhkRidList)) {
            return;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" MERGE INTO TB_TJ_JC_TASK_PSN A ")
                .append("  USING (")
                .append("           SELECT T.BHK_CODE AS BHK_CODE,K1.RID AS RID FROM TD_TJ_BHK T ")
                .append("           INNER JOIN TB_TJ_SRVORG T1 ON T.BHKORG_ID = T1.RID ")
                .append("           INNER JOIN TB_TJ_JC_TASK K ON K.ORG_ID = T1.REG_ORGID AND K.CRPT_ID = T.ENTRUST_CRPT_ID AND EXTRACT (YEAR FROM T.BHK_DATE)=K.YEAR ")
                .append("           INNER JOIN TB_TJ_JC_TASK_PSN K1 ON K1.MAIN_ID = K.RID AND T.CARD_TYPE_ID = K1.CARD_TYPE_ID AND T.IDC = K1.IDC ")
                .append("           WHERE T.JC_TYPE = 2 AND NVL(T.IF_RHK,0)=0 ")
                .append("           AND K.STATE=2 ")
                .append("           AND T.ONGUARD_STATEID=K1.ONGUARD_STATEID ")
                .append("           AND EXISTS (SELECT 1 FROM TD_TJ_BADRSNS M INNER JOIN TB_TJ_JC_TASK_BADRSN K2 ON K2.BADRSN_ID =M.BADRSN_ID  WHERE M.IF_ZD_JC=1 AND M.BHK_ID=T.RID AND K2.MAIN_ID =K1.RID) ")
                .append("           AND T.RID IN (:bhkRidList) ")
                .append("  ) B ON (A.RID = B.RID) ")
                .append("  WHEN MATCHED ")
                .append("  THEN UPDATE SET A.BHK_CODE = B.BHK_CODE ");
        String updateSql = sqlBuffer.toString();
        Map<String,Object> paramMap = new HashMap<>();
        List<List<Integer>> groupList = StringUtils.splitListProxy(allBhkRidList, 1000);
        for (List<Integer> bhkRidList : groupList) {
            paramMap.put("bhkRidList", bhkRidList);
            this.executeSql(updateSql, paramMap);
        }
    }
    /**
     *  <p>方法描述：查询当前登录人最新记录,按照审核时间倒叙</p>
     * @MethodAuthor hsj 2025-03-28 11:51
     */
    @Transactional(readOnly = true)
    public Object[] findErrFilePath() {
        String sql = "SELECT T.RID ,T.ERROR_MSG,T.ERROR_FILE_PATH   FROM  TD_TJ_CHECK_TASK T WHERE  T.TASK_TYPE = 1 AND T.CHECK_RSN_ID = "+
                Global.getUser().getRid()+" ORDER BY T.CHECK_DATE DESC  ";
        List<Object[]> list = this.findDataBySqlNoPage(sql, null);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }
}
