package com.chis.modules.heth.comm.javabean;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemHeaders;
import org.primefaces.model.UploadedFile;

public class FileItemBean implements FileItem{

	private UploadedFile file;
	
	public FileItemBean(UploadedFile file) {
		this.file = file;
	}
	
	@Override
	public FileItemHeaders getHeaders() {
		return null;
	}

	@Override
	public void setHeaders(FileItemHeaders headers) {
		
	}

	@Override
	public InputStream getInputStream() throws IOException {
		return file.getInputstream();
	}

	@Override
	public String getContentType() {
		return file.getContentType();
	}

	@Override
	public String getName() {
		return file.getFileName();
	}

	@Override
	public boolean isInMemory() {
		return false;
	}

	@Override
	public long getSize() {
		return file.getSize();
	}

	@Override
	public byte[] get() {
		return null;
	}

	@Override
	public String getString(String encoding)
			throws UnsupportedEncodingException {
		return null;
	}

	@Override
	public String getString() {
		return null;
	}

	@Override
	public void write(File file) throws Exception {
		
	}

	@Override
	public void delete() {
		
	}

	@Override
	public String getFieldName() {
		return null;
	}

	@Override
	public void setFieldName(String name) {
		
	}

	@Override
	public boolean isFormField() {
		return false;
	}

	@Override
	public void setFormField(boolean state) {
		
	}

	@Override
	public OutputStream getOutputStream() throws IOException {
		return null;
	}

}
