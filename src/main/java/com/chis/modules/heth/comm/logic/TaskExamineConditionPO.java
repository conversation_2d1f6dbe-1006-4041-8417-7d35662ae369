package com.chis.modules.heth.comm.logic;


/**
 *  <p>类描述：主动监测任务审核查询条件</p>
 * @ClassAuthor hsj 2023-07-20 14:08
 */
public class TaskExamineConditionPO {

    //地区
    private Integer zoneId;
    private String zoneCode;
    private String zoneName;
    //检查机构
    private String orgName;
    //年份
    private Integer year;
    //单位名称
    private String crptName;
    //社会信用代码
    private String creditCode;
    //行业类别
    private String indusTypeIds;
    private String indusTypeNames;
   //经济类型
    private String economyIds;
    private String economyNames;
   //企业规模
    private String crptSizeIds;
    //姓名
    private String psnName;
    //身份证号
    private String idc;
    // 状态
    private Integer[] state;

    public Integer getZoneId() {
        return zoneId;
    }

    public void setZoneId(Integer zoneId) {
        this.zoneId = zoneId;
    }

    public String getZoneCode() {
        return zoneCode;
    }

    public void setZoneCode(String zoneCode) {
        this.zoneCode = zoneCode;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getIndusTypeIds() {
        return indusTypeIds;
    }

    public void setIndusTypeIds(String indusTypeIds) {
        this.indusTypeIds = indusTypeIds;
    }

    public String getIndusTypeNames() {
        return indusTypeNames;
    }

    public void setIndusTypeNames(String indusTypeNames) {
        this.indusTypeNames = indusTypeNames;
    }

    public String getEconomyIds() {
        return economyIds;
    }

    public void setEconomyIds(String economyIds) {
        this.economyIds = economyIds;
    }

    public String getEconomyNames() {
        return economyNames;
    }

    public void setEconomyNames(String economyNames) {
        this.economyNames = economyNames;
    }

    public String getCrptSizeIds() {
        return crptSizeIds;
    }

    public void setCrptSizeIds(String crptSizeIds) {
        this.crptSizeIds = crptSizeIds;
    }

    public String getPsnName() {
        return psnName;
    }

    public void setPsnName(String psnName) {
        this.psnName = psnName;
    }

    public String getIdc() {
        return idc;
    }

    public void setIdc(String idc) {
        this.idc = idc;
    }

    public Integer[] getState() {
        return state;
    }

    public void setState(Integer[] state) {
        this.state = state;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }
}
