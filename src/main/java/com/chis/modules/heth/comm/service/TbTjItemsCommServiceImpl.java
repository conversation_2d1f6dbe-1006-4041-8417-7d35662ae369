package com.chis.modules.heth.comm.service;

import java.util.ArrayList;
import java.util.List;

import com.chis.common.utils.CollectionUtil;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.service.AbstractTemplate;

/**
 * @Description : 体检项目标准库（GBZ-188）-业务层
 * @ClassAuthor : anjing
 * @Date : 2019/5/20 15:50
 **/
@Service
@Transactional(readOnly = true)
public class TbTjItemsCommServiceImpl extends AbstractTemplate {

    public List<Object[]> selectItemListByItemCmbId(String itemCmbIds, String sex) {
        if(StringUtils.isNotBlank(itemCmbIds)) {
            StringBuilder sb = new StringBuilder();
            sb.append(" SELECT T.RID, T.ITEM_CODE, T.ITEM_NAME, T.<PERSON>UN<PERSON>, T.J<PERSON>, T.<PERSON>, <PERSON><PERSON>, T.ITEM_STDVALUE,");
            sb.append(" T.DFLT, T.ITEM_SORTID, T2.CODE_NAME, T1.ITEM_CMBID, T.SEX, T.ITEM_TAG,T2.EXTENDS1, T.MSRUNT_ID,T.item_tag_id,T3.EXTENDS3,'',T1.DETER_WAY ");
            sb.append(" FROM TB_TJ_ITEMS T ");
            sb.append(" INNER JOIN TB_ZWTJ_ITEMCMB_ITEMS T1 ON T1.ITEM_ID = T.RID ");
            sb.append(" INNER JOIN TS_SIMPLE_CODE T2 ON T.ITEM_SORTID = T2.RID ");
            sb.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T3.RID = T.item_tag_id ");
            sb.append(" WHERE T1.ITEM_CMBID IN ( ").append(itemCmbIds).append(")");
            sb.append(" AND T.STOP_TAG = 1 ");
            if("男".equals(sex)) {
                sb.append(" AND NVL(T.SEX, 0) IN (0, 1)");
            } else {
                sb.append(" AND NVL(T.SEX, 0) IN (0, 2)");
            }
            sb.append(" ORDER BY T2.NUM ASC, T2.CODE_NO, T.NUM, T.ITEM_CODE ");
            return em.createNativeQuery(sb.toString()).getResultList();
        }
        return null;
    }

    public List<Object[]> selectItemListByItemCmbIdByRid(String rid, String sex) {
        if (StringUtils.isNotBlank(rid)) {
            StringBuilder sb = new StringBuilder();
            sb.append(" SELECT T.RID, T.ITEM_CODE, T.ITEM_NAME, T.MSRUNT, T.JDGPTN, T.MINVAL, T.MAXVAL, T.ITEM_STDVALUE,");
            sb.append(" T.DFLT, T.ITEM_SORTID, T2.CODE_NAME, '', T.SEX, T.ITEM_TAG,T2.EXTENDS1, T.MSRUNT_ID ");
            sb.append(" FROM TB_TJ_ITEMS T ");
            sb.append(" INNER JOIN TS_SIMPLE_CODE T2 ON T.ITEM_SORTID = T2.RID ");
            sb.append(" WHERE T.RID IN ( ").append(rid).append(")");
            sb.append(" AND T.STOP_TAG = 1 ");
            if ("男".equals(sex)) {
                sb.append(" AND NVL(T.SEX, 0) IN (0, 1)");
            } else {
                sb.append(" AND NVL(T.SEX, 0) IN (0, 2)");
            }
            sb.append(" ORDER BY T2.NUM ASC, T2.CODE_NO, T.NUM, T.ITEM_CODE ");
            return CollectionUtil.castList(Object[].class, em.createNativeQuery(sb.toString()).getResultList());
        }
        return new ArrayList<>();
    }
}
