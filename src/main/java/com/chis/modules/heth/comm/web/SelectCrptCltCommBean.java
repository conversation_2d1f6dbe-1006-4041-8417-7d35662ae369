package com.chis.modules.heth.comm.web;

import java.util.*;
import java.util.regex.Pattern;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;

import com.chis.modules.heth.comm.service.TbTjCrptCommServiceImpl;
import com.chis.modules.system.web.IndusTypeCodePanelBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.web.FacesBean;
@Deprecated
@ManagedBean(name="selectCrptCltCommBean")
@ViewScoped
public class SelectCrptCltCommBean extends FacesBean {

    private static final long serialVersionUID = 1L;
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
            .getBean(SystemModuleServiceImpl.class);
    private TbTjCrptCommServiceImpl service = SpringContextHolder.getBean(TbTjCrptCommServiceImpl.class);

    //查询条件--用人单位名称
    private String searchCrptName;
    //查询条件--社会信用代码
    private String searchCrptCode;

    /** 过滤选择的企业列表 */
    private List<TbTjCrpt> crptList;
    //选择企业
    private TbTjCrpt selectCrpt;
    //企业rid
    private Integer crptId;
    private List<TsZone> zoneList;
    private Integer areaZoneId;
    private String areaZoneName;
    private Short areaZoneType;
    //添加、修改企业实体
    private TbTjCrpt editCrpt;
    //企业规模
    private List<TsSimpleCode> crptsizwList;


    private String searchNamOrPy;

    private List<TsSimpleCode> displayList;

    private List<TsSimpleCode> allList;

    private TsSimpleCode selectPro;

    private String selCodeName;

    /**码表大类*/
    private String firstCodeNo;
    private List<TsSimpleCode> firstList;
    private Map<String, TsZone> zoneMap = new HashMap<String, TsZone>();
    private List<Integer> economyIds;
    private List<Integer> indusTypeIds;
    private String ifTjlrCrpt;

    /** 行业类别弹框Bean */
    private IndusTypeCodePanelBean codePanelBean;
    public SelectCrptCltCommBean(){
        FacesContext facesCon = FacesContext.getCurrentInstance();
        ExternalContext externalContext = null == facesCon ? null : facesCon.getExternalContext();
        Map<String,String> parentParams = null == externalContext ? null : externalContext.getRequestParameterMap();
        if(null != parentParams && null != parentParams.get("ifTjlrCrpt")){
            this.setIfTjlrCrpt("1");
        }
        initEconomyAndIndusType();
        initZone();
        initSimpleCodeList();
        initCrpt();
        this.codePanelBean = new IndusTypeCodePanelBean();
        this.searchAction();
    }

    /** 初始化最后一级经济性质与行业类别rid的集合 */
    private void initEconomyAndIndusType(){
        List<TsSimpleCode> econList = commService.findNumSimpleCodesByTypeId("5003");
        List<TsSimpleCode> indusList = commService.findNumSimpleCodesByTypeId("5002");
        economyIds = ridListByList(econList);
        indusTypeIds = ridListByList(indusList);
    }

    /** 查找经济性质与行业类别最后一级的rid集合 */
    private List<Integer> ridListByList(List<TsSimpleCode> resList){
        List<Integer> resultList = null;
        if(null != resList && resList.size() > 0){
            Map<String,String[]> map = new HashMap<>();
            for(TsSimpleCode t : resList){
                map.put(t.getCodeNo(),new String[]{t.getRid().toString(),t.getCodeLevelNo()});
            }

            if(null != map && !map.isEmpty()){
                resultList = new ArrayList<>();
                List<String[]> list = new ArrayList<>();
                list.addAll(map.values());
                Iterator<String> keyIter = map.keySet().iterator();
                while(keyIter.hasNext()){
                    String key = keyIter.next();
                    boolean flag = true;
                    for(String[] arr : list){
                        String codeLevelNo = arr[1].trim();
                        //判断是否有下级标签
                        if(codeLevelNo.startsWith(key.trim()+".") || codeLevelNo.contains("."+key.trim()+".")){
                            flag = false;
                            break;
                        }
                    }
                    if(flag){
                        resultList.add(Integer.parseInt(map.get(key)[0]));
                    }
                }
            }
        }
        return resultList;
    }
    /**
     * <p>方法描述：初始化地区</p>
     * @MethodAuthor qrr,2018年4月27日,initZone
     * */
    private void initZone() {
        String jsZone = sessionData.getUser().getTsUnit().getTsZone()
                .getZoneGb().substring(0, 2)
                + "00000000";
        this.zoneList = this.systemModuleService.findZoneListICanSee(false,
                jsZone);
        for(TsZone tsZone : this.zoneList) {
            this.zoneMap.put(tsZone.getRid().toString(), tsZone);
        }
    }
    /**
     * <p>方法描述：初始化码表信息</p>
     * @MethodAuthor qrr,2018年4月27日,initSimpleCodeList
     * */
    private void initSimpleCodeList() {
        crptsizwList = commService.findLevelSimpleCodesByTypeId("5004");
    }
    /**
     * <p>方法描述：初始化企业信息</p>
     * @MethodAuthor qrr,2018年4月23日,initCrpt
     * */
    private void initCrpt(){
        editCrpt = new TbTjCrpt();
        editCrpt.setTsZoneByZoneId(new TsZone());
        editCrpt.setTsSimpleCodeByCrptSizeId(new TsSimpleCode());
        editCrpt.setTsSimpleCodeByEconomyId(new TsSimpleCode());
        editCrpt.setTsSimpleCodeByIndusTypeId(new TsSimpleCode());
        //地区
        this.areaZoneId = sessionData.getUser().getTsUnit().getTsZone().getRid();
        this.areaZoneName = sessionData.getUser().getTsUnit().getTsZone().getZoneName();
        this.areaZoneType = sessionData.getUser().getTsUnit().getTsZone().getZoneType();
    }
    /**
     * <p>方法描述：用人单位查询</p>
     * @MethodAuthor qrr,2018年4月23日,searchAction
     * */
    public void searchAction() {
        crptList = new ArrayList<>();
        Map<String, Object> paramMap = new HashMap<>();
        StringBuffer buffer = new StringBuffer();
        buffer.append("SELECT T.RID, T.LINKMAN2, T.LINKPHONE2, T.ADDRESS, T.POSTCODE, T1.ZONE_NAME, T.CRPT_NAME, T.INSTITUTION_CODE, " +
                "T1.ZONE_TYPE, T.INDUS_TYPE_ID, T.ECONOMY_ID, T.CRPT_SIZE_ID, T1.REAL_ZONE_TYPE, T.WORK_FORCE, T.HOLD_CARD_MAN, T1.RID as ZONE_ID ");
        buffer.append(" FROM TB_TJ_CRPT T ");
        buffer.append(" LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID ");
        buffer.append(" WHERE 1 =1 ");
        // 单位名称
        if (StringUtils.isNotBlank(searchCrptName)) {
            buffer.append(" AND T.CRPT_NAME LIKE :crptName");
            paramMap.put("crptName", "%" + this.searchCrptName.trim() + "%");
        }
        // 社会信用代码
        if (StringUtils.isNotBlank(searchCrptCode)) {
            buffer.append(" AND T.INSTITUTION_CODE LIKE :institutionCode");
            paramMap.put("institutionCode", "%" + this.searchCrptCode.trim() + "%");
        }
        buffer.append(" and rownum<=50 order by t1.zone_Code");
        List<Object[]> list = service.findDataBySqlNoPage(buffer.toString(), paramMap);
        if (null != list && list.size() > 0) {
            for (Object[] obj : list) {
                TbTjCrpt crpt = new TbTjCrpt();
                crpt.setRid(Integer.valueOf(obj[0].toString()));
                crpt.setLinkman2(null!=obj[1]?obj[1].toString():"");
                crpt.setLinkphone2(null!=obj[2]?obj[2].toString():"");
                crpt.setAddress(null!=obj[3]?obj[3].toString():"");
                crpt.setPostCode(null!=obj[4]?obj[4].toString():"");
                TsZone tsZone = new TsZone();
                tsZone.setZoneName(null!=obj[5]?obj[5].toString():"");
                tsZone.setZoneType(null!=obj[8]?Short.valueOf(obj[8].toString()):null);
                tsZone.setRealZoneType(null!=obj[12]?Short.valueOf(obj[12].toString()):null);
                tsZone.setRid(null != obj[15] ? Integer.parseInt(obj[15].toString()) : null);
                crpt.setTsZoneByZoneId(tsZone);
                crpt.setCrptName(null!=obj[6]?obj[6].toString():"");
                crpt.setInstitutionCode(null!=obj[7]?obj[7].toString():"");
                TsSimpleCode tsSimpleCodeByIndusTypeId = new TsSimpleCode();
                if(null != obj[9]) { tsSimpleCodeByIndusTypeId.setRid(Integer.parseInt(obj[9].toString()));}
                crpt.setTsSimpleCodeByIndusTypeId(tsSimpleCodeByIndusTypeId);
                TsSimpleCode tsSimpleCodeByEconomyId = new TsSimpleCode();
                if(null != obj[10]) { tsSimpleCodeByEconomyId.setRid(Integer.parseInt(obj[10].toString()));}
                crpt.setTsSimpleCodeByEconomyId(tsSimpleCodeByEconomyId);
                TsSimpleCode tsSimpleCodeByCrptSizeId = new TsSimpleCode();
                if(null != obj[11]) { tsSimpleCodeByCrptSizeId.setRid(Integer.parseInt(obj[11].toString()));}
                crpt.setTsSimpleCodeByCrptSizeId(tsSimpleCodeByCrptSizeId);
                crpt.setWorkForce(null == obj[13] ? null : Integer.parseInt(obj[13].toString()));
                crpt.setHoldCardMan(null == obj[14] ? null : Integer.parseInt(obj[14].toString()));
                crptList.add(crpt);
            }
        }
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("selectForm:crptTable");
        dataTable.setFirst(0);
    }
    /**
     * <p>类描述：添加用人单位</p>
     * @MethodAuthor qrr,2018年4月23日,TdBhkDiagAcceptEditNewBean
     * */
    public void addCrptAction(){
        initCrpt();
        RequestContext.getCurrentInstance().execute("PF('addCrpt').show();");
    }
    /**
     * <p>方法描述：选择用人单位</p>
     * @MethodAuthor qrr,2018年4月23日,selectCrptAction
     * */
    public void selectCrptAction(){
        if (StringUtils.isBlank(selectCrpt.getLinkman2())
                || StringUtils.isBlank(selectCrpt.getLinkphone2())
                || null == selectCrpt.getTsZoneByZoneId().getRealZoneType()
                || (null!= selectCrpt.getTsZoneByZoneId().getRealZoneType() && selectCrpt.getTsZoneByZoneId().getRealZoneType().intValue()<5)
                || StringUtils.isBlank(selectCrpt.getCrptName())
                || StringUtils.isBlank(selectCrpt.getInstitutionCode())
                || StringUtils.isBlank(selectCrpt.getAddress())
                || null == selectCrpt.getTsSimpleCodeByIndusTypeId()
                || null == selectCrpt.getTsSimpleCodeByEconomyId()
                || null== selectCrpt.getTsSimpleCodeByCrptSizeId()
                || null == economyIds
                || null == indusTypeIds
                || !economyIds.contains(selectCrpt.getTsSimpleCodeByEconomyId().getRid())
                || !indusTypeIds.contains(selectCrpt.getTsSimpleCodeByIndusTypeId().getRid())
                || null == selectCrpt.getTsZoneByZoneId().getRid()
                || null == zoneMap.get(selectCrpt.getTsZoneByZoneId().getRid().toString())) {
            JsfUtil.addErrorMessage("请先完善所选单位信息！");
            return;
        }
        //从体检录入过来的 需要判断职工人数和接触危害因素人数是否为空
        if(null != this.ifTjlrCrpt && ifTjlrCrpt.equals("1") && (null == selectCrpt.getWorkForce() || null == selectCrpt.getHoldCardMan())){
            JsfUtil.addErrorMessage("请先完善所选单位信息！");
            return;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("selectCrpt", selectCrpt);
        RequestContext.getCurrentInstance().closeDialog(map);
    }
    /**
     * <p>方法描述：修改企业信息</p>
     * @MethodAuthor qrr,2018年4月23日,modCrptAction
     * */
    public void modCrptAction(){
        if (null==crptId) {
            return;
        }
        String hql = "from TbTjCrpt t where 1=1 and t.rid ="+crptId;
        editCrpt = service.findOneByHql(hql, TbTjCrpt.class);
        if (null==editCrpt) {
            editCrpt = new TbTjCrpt();
        }else {
            //地区赋值
            TsZone zone = editCrpt.getTsZoneByZoneId();
            if (null!=zone) {
                this.areaZoneId = zone.getRid();
                this.areaZoneName = zone.getZoneName();
                this.areaZoneType = zone.getRealZoneType();
            }
        }
        initSimpleCode();
    }
    /**
     * <p>方法描述：初始化码表数据</p>
     * @MethodAuthor qrr,2018年6月6日,initSimpleCode
     * */
    private void initSimpleCode(){
        if (null==editCrpt.getTsSimpleCodeByCrptSizeId()) {
            editCrpt.setTsSimpleCodeByCrptSizeId(new TsSimpleCode());
        }
        if (null==editCrpt.getTsSimpleCodeByEconomyId()) {
            editCrpt.setTsSimpleCodeByEconomyId(new TsSimpleCode());
        }
        if (null==editCrpt.getTsSimpleCodeByIndusTypeId()) {
            editCrpt.setTsSimpleCodeByIndusTypeId(new TsSimpleCode());
        }
    }
    /**
     * <p>方法描述：保存企业信息</p>
     * @MethodAuthor qrr,2018年4月23日,saveCrptAction
     * */
    public void saveCrptAction(){
        if (null==editCrpt) {
            return;
        }
        boolean flag = true;
        if(null == this.areaZoneId){
            JsfUtil.addErrorMessage("所属地区未选择！");
            flag = false;
        }else if(null == this.zoneMap.get(this.areaZoneId.toString())){
            JsfUtil.addErrorMessage("所属地区已停用，请重新选择！");
            flag = false;
        }
        this.areaZoneType = (null == this.areaZoneId || null == this.zoneMap.get(this.areaZoneId.toString())) ? null
                : this.zoneMap.get(this.areaZoneId.toString()).getRealZoneType();
        TsZone tsZone = new TsZone();
        tsZone.setRid(areaZoneId);
        tsZone.setZoneName(areaZoneName);
        //tsZone.setZoneType(areaZoneType);
        tsZone.setRealZoneType(this.areaZoneType);
        editCrpt.setTsZoneByZoneId(tsZone);
        editCrpt.setInterPrcTag((short) 1);
        if (veryTbTjCrpt() && flag) {
            service.saveTbTjCrpt(editCrpt);
            JsfUtil.addSuccessMessage("保存成功！");
            RequestContext.getCurrentInstance().update("selectForm:crptTable");
            RequestContext.getCurrentInstance().execute("PF('addCrpt').hide();");
            initSimpleCode();
            this.searchAction();
        }
    }
    private boolean veryTbTjCrpt(){
        boolean flag = true;
        //地区
        if(null == editCrpt.getTsZoneByZoneId()
                || null == editCrpt.getTsZoneByZoneId().getRid()) {
            JsfUtil.addErrorMessage("请选择所属地区！");
            flag = false;
        }else {
            if (null != areaZoneType && (null!=areaZoneType && areaZoneType.intValue()<5)) {
                JsfUtil.addErrorMessage("所属地区必须选择至街道！");
                flag = false;
            }
        }
        //企业名称
        if(StringUtils.isBlank(editCrpt.getCrptName())) {
            JsfUtil.addErrorMessage("单位名称不能为空！");
            flag = false;
        }
        //社会信用代码
        if(StringUtils.isBlank(editCrpt.getInstitutionCode())) {
            JsfUtil.addErrorMessage("社会信用代码不能为空！");
            flag = false;
        }
        if(StringUtils.isNotBlank(editCrpt.getInstitutionCode())) {
            //社会信用证代码合法与重复性
            if (!StringUtils.isCreditCode(editCrpt.getInstitutionCode())) {
                JsfUtil.addErrorMessage("社会信用代码不合法！");
                flag = false;
            }
            List<TbTjCrpt> creditCodes = service.findTbTjCrptByCreditCode(
                    editCrpt.getInstitutionCode(), editCrpt.getRid());
            if (null != creditCodes && creditCodes.size() > 0) {
                JsfUtil.addErrorMessage("社会信用代码重复！");
                flag = false;
            }
        }
        //单位地址
        if(StringUtils.isBlank(editCrpt.getAddress())) {
            JsfUtil.addErrorMessage("单位地址不能为空！");
            flag = false;
        }
        // 行业类别
        if(null == editCrpt.getTsSimpleCodeByIndusTypeId() || StringUtils.isBlank(editCrpt.getTsSimpleCodeByIndusTypeId().getCodeName())) {
            JsfUtil.addErrorMessage("行业类别不能为空！");
            flag = false;
        }else if(null == indusTypeIds || indusTypeIds.size() == 0
                || !indusTypeIds.contains(editCrpt.getTsSimpleCodeByIndusTypeId().getRid())){
            JsfUtil.addErrorMessage("行业类别必须选择至最末级！");
            flag = false;
        }
        // 经济类型
        if(null == editCrpt.getTsSimpleCodeByEconomyId() || StringUtils.isBlank(editCrpt.getTsSimpleCodeByEconomyId().getCodeName())) {
            JsfUtil.addErrorMessage("经济类型不能为空！");
            flag = false;
        }else if(null == economyIds || economyIds.size() == 0 ||
                !economyIds.contains(editCrpt.getTsSimpleCodeByEconomyId().getRid())){
            JsfUtil.addErrorMessage("经济类型必须选择至最末级！");
            flag = false;
        }

        // 企业规模
        if(null== editCrpt.getTsSimpleCodeByCrptSizeId() || null == editCrpt.getTsSimpleCodeByCrptSizeId().getRid()) {
            JsfUtil.addErrorMessage("企业规模不能为空！");
            flag = false;
        }
        //联系人
        if (StringUtils.isBlank(editCrpt.getLinkman2())) {
            JsfUtil.addErrorMessage("联系人不能为空！");
            flag = false;
        }
        //联系电话
        if (StringUtils.isBlank(editCrpt.getLinkphone2())) {
            JsfUtil.addErrorMessage("联系电话不能为空！");
            flag = false;
        }else {
            if (!veryMobile(editCrpt.getLinkphone2())) {
                JsfUtil.addErrorMessage("联系电话格式不正确！");
                flag = false;
            }
        }
        // 电话
        if (StringUtils.isNotBlank(editCrpt.getPhone())
                && !veryMobile(editCrpt.getPhone())) {
            JsfUtil.addErrorMessage("法人联系电话格式不正确！");
            flag = false;
        }
        // 邮政编码
        if (StringUtils.isNotBlank(editCrpt.getPostCode())) {
            if (!StringUtils.vertyPost(editCrpt.getPostCode())) {
                JsfUtil.addErrorMessage("邮政编码格式不正确！");
                flag = false;
            }
        }
        if(null != ifTjlrCrpt && ifTjlrCrpt.equals("1")){
            if(null == editCrpt.getWorkForce()){
                JsfUtil.addErrorMessage("职工人数不允许为空！");
                flag = false;
            }
            if(null == editCrpt.getHoldCardMan()){
                JsfUtil.addErrorMessage("接触职业病危害因素人数不允许为空！");
                flag = false;
            }
            if(null != editCrpt.getWorkForce() && null != editCrpt.getHoldCardMan()
                    && editCrpt.getWorkForce().compareTo(editCrpt.getHoldCardMan()) < 0){
                JsfUtil.addErrorMessage("接触职业病危害因素人数必须小于等于职工人数！");
                flag = false;
            }
        }
        return flag;
    }
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }
    /**
     * <p>方法描述：验证联系电话（手机号+固定电话）</p>
     * @MethodAuthor qrr,2018年4月8日,veryMobile
     * */
    private boolean veryMobile(String mobile){
        boolean flag = true;
        if (StringUtils.isNotBlank(mobile)
                && !Pattern.matches(Constants.PHONE, mobile)&&!Pattern.matches(Constants.MOBILE_REGEX, mobile)) {
            flag = false;
        }
        return flag;
    }
    /**
     * <p>方法描述：选择经济类型</p>
     * @MethodAuthor qrr,2018年5月29日,selEconomyAction
     * */
    public void selCodeTypeAction(){
        this.displayList = new ArrayList<TsSimpleCode>();
        this.allList = new ArrayList<TsSimpleCode>();
        this.firstList = new ArrayList<TsSimpleCode>();
        this.searchNamOrPy = null;
        if ("经济类型".equals(selCodeName)) {
            allList = commService.findNumSimpleCodesByTypeId("5003");
            if( null != allList && allList.size() > 0){
                for(TsSimpleCode t : allList){
                    if(null != economyIds && !economyIds.contains(t.getRid().intValue())){
                        t.setCreateManid(-1);
                    }
                }
            }
        }else if ("行业类别".equals(selCodeName)) {
            allList = commService.findNumSimpleCodesByTypeId("5002");
            if( null != allList && allList.size() > 0){
                for(TsSimpleCode t : allList){
                    if(null != indusTypeIds && !indusTypeIds.contains(t.getRid().intValue())){
                        t.setCreateManid(-1);
                    }
                }
            }
        }
        dealCodelevel(allList);
        if( null != allList && allList.size() > 0)    {
            this.displayList.addAll(allList);

            for (TsSimpleCode t : allList) {
                if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
                    firstList.add(t);
                }
            }
        }
        if("行业类别".equals(this.selCodeName)) {
            this.codePanelBean.partInit();
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("selectForm:selectedIndusCodeTable");
            dataTable.setFirst(0);
            dataTable.setRows(10);
        }else{
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("selectForm:selectedIndusTable");
            dataTable.setFirst(0);
        }
    }
    /**
     * <p>方法描述：处理码表层级关系</p>
     * @MethodAuthor qrr,2018年4月8日,dealCodelevel
     * */
    private void dealCodelevel(List<TsSimpleCode> list){
        if (list != null && list.size() > 0) {
            for (TsSimpleCode code : list) {
                code.setLevelIndex(StringUtils.countMatches(
                        code.getCodeLevelNo(), ".")
                        + "");
            }
        }
    }

    public void searchCodeAction(){
        //初始化展示页面的疫苗数据集
        this.displayList = new ArrayList<TsSimpleCode>();
        List<TsSimpleCode> list = new ArrayList<TsSimpleCode>();
        if(null != allList && allList.size() > 0 ){
            if(StringUtils.isNotBlank(firstCodeNo)) {
                for(TsSimpleCode t :allList)   {
                    if (t.getCodeLevelNo().startsWith(firstCodeNo)) {
                        list.add(t);
                    }
                }
                if(StringUtils.isNotBlank(searchNamOrPy)){
                    for(TsSimpleCode t :list)   {
                        //疫苗名称
                        String codeName = t.getCodeName()==null?"":t.getCodeName().toString();
                        //疫苗拼音码
                        String codePym = t.getSplsht()==null?"":t.getSplsht().toString();
                        //如果模糊匹配上，则增加
                        if (codeName.indexOf(searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(searchNamOrPy.toUpperCase()) != -1) {
                            this.displayList.add(t);
                        }
                    }
                }else{
                    this.displayList.addAll(list);
                }
            }else if(StringUtils.isNotBlank(searchNamOrPy)){
                for(TsSimpleCode t :allList)   {
                    //疫苗名称
                    String codeName = t.getCodeName()==null?"":t.getCodeName().toString();
                    //疫苗拼音码
                    String codePym = t.getSplsht()==null?"":t.getSplsht().toString();
                    //如果模糊匹配上，则增加
                    if (codeName.indexOf(searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(searchNamOrPy.toUpperCase()) != -1) {
                        this.displayList.add(t);
                    }
                }
            }else{
                this.displayList.addAll(allList);
            }

        }
    }

    public void selectAction(){
        if ("经济类型".equals(selCodeName)) {
            editCrpt.setTsSimpleCodeByEconomyId(selectPro);
            RequestContext.getCurrentInstance().update("selectForm:economyName");
        }else if ("行业类别".equals(selCodeName)) {
            editCrpt.setTsSimpleCodeByIndusTypeId(selectPro);
            RequestContext.getCurrentInstance().update("selectForm:indusTypeName");
        }
        RequestContext.getCurrentInstance().execute("PF('selDialog').hide()");
    }

    /**
     * @Description: 行业类别选择
     *
     * @MethodAuthor pw,2022年02月7日
     */
    public void selectIndusTypeAction(){
        this.editCrpt.setTsSimpleCodeByIndusTypeId(this.selectPro);
        RequestContext.getCurrentInstance().update("selectForm:indusTypeName");
        RequestContext.getCurrentInstance().execute("PF('selIndusTypeDialog').hide()");
    }

    /**
     * <p>方法描述：清空</p>
     * @MethodAuthor qrr,2018年6月5日,delCodeName
     * */
    public void delCodeName(){
        if ("经济类型".equals(selCodeName)) {
            editCrpt.setTsSimpleCodeByEconomyId(new TsSimpleCode());
            RequestContext.getCurrentInstance().update("selectForm:economyName");
        }else if ("行业类别".equals(selCodeName)) {
            editCrpt.setTsSimpleCodeByIndusTypeId(new TsSimpleCode());
            RequestContext.getCurrentInstance().update("selectForm:indusTypeName");
        }
    }
    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchCrptCode() {
        return searchCrptCode;
    }

    public void setSearchCrptCode(String searchCrptCode) {
        this.searchCrptCode = searchCrptCode;
    }

    public TbTjCrpt getSelectCrpt() {
        return selectCrpt;
    }

    public void setSelectCrpt(TbTjCrpt selectCrpt) {
        this.selectCrpt = selectCrpt;
    }

    public Integer getCrptId() {
        return crptId;
    }

    public void setCrptId(Integer crptId) {
        this.crptId = crptId;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public Integer getAreaZoneId() {
        return areaZoneId;
    }

    public void setAreaZoneId(Integer areaZoneId) {
        this.areaZoneId = areaZoneId;
    }

    public String getAreaZoneName() {
        return areaZoneName;
    }

    public void setAreaZoneName(String areaZoneName) {
        this.areaZoneName = areaZoneName;
    }

    public TbTjCrpt getEditCrpt() {
        return editCrpt;
    }

    public void setEditCrpt(TbTjCrpt editCrpt) {
        this.editCrpt = editCrpt;
    }

    public List<TsSimpleCode> getCrptsizwList() {
        return crptsizwList;
    }

    public void setCrptsizwList(List<TsSimpleCode> crptsizwList) {
        this.crptsizwList = crptsizwList;
    }
    public List<TbTjCrpt> getCrptList() {
        return crptList;
    }
    public void setCrptList(List<TbTjCrpt> crptList) {
        this.crptList = crptList;
    }
    public Short getAreaZoneType() {
        return areaZoneType;
    }
    public void setAreaZoneType(Short areaZoneType) {
        this.areaZoneType = areaZoneType;
    }
    public String getSearchNamOrPy() {
        return searchNamOrPy;
    }
    public void setSearchNamOrPy(String searchNamOrPy) {
        this.searchNamOrPy = searchNamOrPy;
    }
    public List<TsSimpleCode> getDisplayList() {
        return displayList;
    }
    public void setDisplayList(List<TsSimpleCode> displayList) {
        this.displayList = displayList;
    }
    public TsSimpleCode getSelectPro() {
        return selectPro;
    }
    public void setSelectPro(TsSimpleCode selectPro) {
        this.selectPro = selectPro;
    }
    public String getSelCodeName() {
        return selCodeName;
    }
    public void setSelCodeName(String selCodeName) {
        this.selCodeName = selCodeName;
    }

    public String getFirstCodeNo() {
        return firstCodeNo;
    }

    public void setFirstCodeNo(String firstCodeNo) {
        this.firstCodeNo = firstCodeNo;
    }

    public List<TsSimpleCode> getFirstList() {
        return firstList;
    }

    public void setFirstList(List<TsSimpleCode> firstList) {
        this.firstList = firstList;
    }

    public List<Integer> getEconomyIds() {
        return economyIds;
    }

    public void setEconomyIds(List<Integer> economyIds) {
        this.economyIds = economyIds;
    }

    public List<Integer> getIndusTypeIds() {
        return indusTypeIds;
    }

    public void setIndusTypeIds(List<Integer> indusTypeIds) {
        this.indusTypeIds = indusTypeIds;
    }

    public String getIfTjlrCrpt() {
        return ifTjlrCrpt;
    }

    public void setIfTjlrCrpt(String ifTjlrCrpt) {
        this.ifTjlrCrpt = ifTjlrCrpt;
    }

    public IndusTypeCodePanelBean getCodePanelBean() {
        return codePanelBean;
    }

    public void setCodePanelBean(IndusTypeCodePanelBean codePanelBean) {
        this.codePanelBean = codePanelBean;
    }
}
