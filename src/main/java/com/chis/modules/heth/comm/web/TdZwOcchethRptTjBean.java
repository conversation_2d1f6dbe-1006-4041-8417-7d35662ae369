package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesSearchBean;
import org.apache.commons.collections.CollectionUtils;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * <p>描述：</p>
 *
 *  @Author: 龚哲,2022/1/4 15:57,TdZwOcchethRptTjBean
 */
@ManagedBean(name = "tdZwOcchethRptTjBean")
@ViewScoped
public class TdZwOcchethRptTjBean extends FacesSearchBean {

    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    /**查询条件：地区集合*/
    private List<TsZone> zoneList;
    private List<TsZone> orgZoneList;
    /**查询条件：用人地区名称*/
    private String searchCrptZoneName;
    /**查询条件：用人地区编码*/
    private String searchCrptZoneCode;
    /**查询条件：用人单位名称*/
    private List<Integer> searchCrptId;
    private String selectCrptNames;
    /**查询条件：行业类别*/
    private List<Integer> searchIndusType;
    private String selectIndusTypeNames;
    /**查询条件：经济性质*/
    private List<Integer> searchEconomy;
    private String selectEconomyNames;
    /**查询条件：企业规模*/
    private String selectCrptSizeIds;
    /**查询条件：职业病危害风险分类*/
    private String selectZybRiskIds;
    /**查询条件：技术服务机构地区名称*/
    private String searchOccZoneName;
    /**查询条件：技术服务机构地区编码*/
    private String searchOccZoneCode;
    /**查询条件：技术服务机构名称*/
    private List<Integer> searchOccId;
    private String selectOccNames;
    /**查询条件：报告开始日期*/
    private Date searchStartDate;
    /**查询条件：报告结束日期*/
    private Date searchEndDate;
    /**查询条件：统计维度*/
    private String searchDimension;
    /**行业类别码表*/
    private List<TsSimpleCode> indusTypeList;
    /**经济性质码表*/
    private List<TsSimpleCode> economyList;
    /**企业规模码表*/
    private List<TsSimpleCode> crptSizeList;
    /**职业病危害风险分类码表*/
    private List<TsSimpleCode> zybRiskList;

    private String simpleCodeType;
    private TsZone tsZone;

    /**
     * 技术服务类别码表
     */
    private Map<String, TsSimpleCode> simpleCodeMap = new LinkedHashMap<>();
    /**
     * 表头
     */
    private List<String> headerValueList;
    /**
     * 表数据
     */
    private List<Object[]> dataList;

    public TdZwOcchethRptTjBean(){
        init();
    }

    private void init(){
        searchEndDate = new Date();
        searchStartDate = DateUtils.addYears(searchEndDate,-1);
        searchDimension = "1";
        //管辖地区
        tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if(null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.zoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "1", "4");
        if(!CollectionUtils.isEmpty(zoneList) && StringUtils.isBlank(searchCrptZoneCode)){
            this.searchCrptZoneCode = zoneList.get(0).getZoneGb();
            this.searchCrptZoneName = zoneList.get(0).getZoneName();
        }
        String zoneGb = new Short("2").equals(tsZone.getRealZoneType()) ? "" : tsZone.getZoneGb();
        this.orgZoneList = this.commService.findZoneListByGbAndType(zoneGb, true, "", "");
        //码表
        indusTypeList = this.commService.findSimpleCodesByTypeId("5002");
        economyList = this.commService.findSimpleCodesByTypeId("5003");
        crptSizeList = this.commService.findSimpleCodesByTypeId("5004");
        zybRiskList = this.commService.findSimpleCodesByTypeId("5511");

        this.headerValueList = new ArrayList<>();
        getSimpleCodeMap5506();
        checkSearchCond();
    }

    /**
     * <p>描述：清空用人单位地区</p>
     * @param
     *
     * @return
     * @Author: 龚哲,2022/1/5 9:25,clearCrptZoneCode
     */
    public void clearCrptZoneCode(){
        this.searchCrptZoneCode = null;
        this.searchCrptZoneName = null;
        System.out.println(searchOccZoneName);
        System.out.println(searchCrptZoneName);
    }

    /**
     * <p>描述：用人单位名称点击动作</p>
     * @param
     *
     * @return
     * @Author: 龚哲,2022/1/5 14:20,selCrptAction
     */
    public void selCrptAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null,625,null,500);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        if(CollectionUtils.isNotEmpty(searchCrptId)){
            paramList.add(","+StringUtils.list2string(searchCrptId,",")+",");
            paramMap.put("selectIds", paramList);
        }
        List<String> paramList2 = new ArrayList<String>();
        paramList2.add(searchCrptZoneCode==null?tsZone.getZoneCode():searchCrptZoneCode);
        paramMap.put("searchZoneCode", paramList2);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectYrOrgList", options, paramMap);
    }

    /**
     * <p>描述：选中单位动作</p>
     * @param event
     *
     * @return
     * @Author: 龚哲,2022/1/5 14:21,onSelectCrptAction
     */
    public void onSelectCrptAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TbTjCrpt> list = (List<TbTjCrpt>) selectedMap.get("selectPros");
            if (null!=list && list.size()>0) {
                StringBuffer names = new StringBuffer();
                StringBuffer ids = new StringBuffer();
                searchCrptId = new ArrayList<>();
                for (TbTjCrpt t : list) {
                    names.append("，").append(t.getCrptName());
                    searchCrptId.add(t.getRid());
                }
                this.selectCrptNames = names.substring(1);
            }
        }
    }
    /**
     * <p>描述：清空用人单位名称</p>
     * @param
     *
     * @return
     * @Author: 龚哲,2022/1/5 9:39,clearCrptNames
     */
    public void clearCrptNames(){
        selectCrptNames = null;
        if(CollectionUtils.isNotEmpty(searchCrptId)){
            searchCrptId.clear();
        }
    }

    /**
     * <p>描述：行业类别、经济类型点击动作</p>
     * @param
     *
     * @return
     * @Author: 龚哲,2022/1/5 14:21,selSimpleCodeAction
     */
    public void selSimpleCodeAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add("5002".equals(this.simpleCodeType) ? "行业类别" : "经济类型");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add(this.simpleCodeType);
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        if ("5002".equals(this.simpleCodeType)) {
            if(CollectionUtils.isNotEmpty(searchIndusType)){
                paramList.add(StringUtils.list2string(searchIndusType,","));
            }
        } else if ("5003".equals(this.simpleCodeType)) {
            if(CollectionUtils.isNotEmpty(searchEconomy)){
                paramList.add(StringUtils.list2string(searchEconomy,","));
            }
        }
        paramMap.put("selectIds", paramList);
        if ("5002".equals(this.simpleCodeType)||"5003".equals(this.simpleCodeType)) {
            paramList = new ArrayList<String>();
            paramList.add("true");
            paramMap.put("ifShowFirstCode", paramList);
        }
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList", options, paramMap);
    }

    /**
     * <p>描述：行业类别、经济类型选中动作</p>
     * @param event
     *
     * @return
     * @Author: 龚哲,2022/1/5 14:48,onSimpleCodeAction
     */
    public void onSimpleCodeAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
            if (null!=list && list.size()>0) {
                StringBuffer names = new StringBuffer();
                StringBuffer ids = new StringBuffer();
                if ("5002".equals(simpleCodeType)) {
                    this.searchIndusType = new ArrayList<>();
                }else if ("5003".equals(simpleCodeType)){
                    this.searchEconomy = new ArrayList<>();
                }
                for (TsSimpleCode t : list) {
                    names.append("，").append(t.getCodeName());
                    ids.append(",").append(t.getRid());
                    if ("5002".equals(simpleCodeType)) {
                        this.searchIndusType.add(t.getRid());
                    }else if ("5003".equals(simpleCodeType)){
                        this.searchEconomy.add(t.getRid());
                    }
                }
                if ("5002".equals(simpleCodeType)) {
                    //行业类别
                    this.selectIndusTypeNames = names.substring(1);
                }else if ("5003".equals(simpleCodeType)){
                    //经济类型
                    this.selectEconomyNames = names.substring(1);
                }
            }
        }
    }
    /**
     * <p>描述：清空行业类别、经济类型</p>
     * @param
     *
     * @return
     * @Author: 龚哲,2022/1/5 14:23,clearSimpleCode
     */
    public void clearSimpleCode() {
        if ("5002".equals(simpleCodeType)) {
            //行业类别
            this.selectIndusTypeNames = null;
            if(CollectionUtils.isNotEmpty(searchIndusType)){
                this.searchIndusType.clear();
            }
            this.indusTypeList = commService.findUpCodeList("5002");
        }else if ("5003".equals(simpleCodeType)){
            //经济类型
            this.selectEconomyNames = null;
            if(CollectionUtils.isNotEmpty(searchEconomy)){
                this.searchEconomy.clear();
            }
            this.economyList = commService.findUpCodeList("5003");
        }
    }

    /**
     * <p>描述：技术服务机构单位名称点击动作</p>
     * @param
     *
     * @return
     * @Author: 龚哲,2022/1/5 14:20,selOccAction
     */
    public void selOccAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null,625,null,500);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        if(CollectionUtils.isNotEmpty(searchOccId)){
            paramList.add(","+StringUtils.list2string(searchOccId,",")+",");
            paramMap.put("selectIds", paramList);
        }
        List<String> paramList2 = new ArrayList<String>();
        String zoneCode = this.searchOccZoneCode;
        if (StringUtils.isBlank(zoneCode)) {
            zoneCode = this.orgZoneList.get(0).getZoneGb();
        }
        paramList2.add(zoneCode);
        paramMap.put("searchZoneCode", paramList2);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectJsfwOrgList", options, paramMap);
    }

    /**
     * <p>描述：选中技术服务机构单位动作</p>
     * @param event
     *
     * @return
     * @Author: 龚哲,2022/1/5 14:21,onSelectOccAction
     */
    public void onSelectOccAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<Object[]> list = (List<Object[]>) selectedMap.get("selectPros");
            if (null!=list && list.size()>0) {
                StringBuffer names = new StringBuffer();
                StringBuffer ids = new StringBuffer();
                searchOccId = new ArrayList<>();
                for (Object[] t : list) {
                    names.append("，").append(t[1]);
                    searchOccId.add(Integer.valueOf(t[0].toString()));
                }
                this.selectOccNames = names.substring(1);
            }
        }
    }
    /**
     * <p>描述：清空技术服务机构单位名称</p>
     * @param
     *
     * @return
     * @Author: 龚哲,2022/1/5 9:39,clearOccNames
     */
    public void clearOccNames(){
        selectOccNames = null;
        if(CollectionUtils.isNotEmpty(searchOccId)){
            searchOccId.clear();
        }
    }
    /**
     * <p>描述：清空技术服务机构地区</p>
     * @param 
     * 
     * @return 
     * @Author: 龚哲,2022/1/5 9:26,clearOccZoneCode
     */ 
    public void clearOccZoneCode(){

        this.searchOccZoneCode = null;
        this.searchOccZoneName = null;
    }

    public void searchAction() {
        checkSearchCond();
    }

    /**
     * 检查查询条件
     */
    private void checkSearchCond() {
        if (StringUtils.isBlank(this.searchCrptZoneCode) && StringUtils.isBlank(this.searchOccZoneCode)) {
            JsfUtil.addSuccessMessage("请选择用人单位地区或技术服务机构地区！");
            return;
        }
        if (StringUtils.isBlank(this.searchDimension)) {
            JsfUtil.addSuccessMessage("请选择统计维度！");
            return;
        }
        String[] sortFields = getSortFields();
        String[] sortNameFields = getSortNameFields();
        if (StringUtils.isBlank(sortFields[0]) || sortNameFields == null) {
            return;
        }
        sortNameFields[2] = sortFields[2] + sortNameFields[2];
        this.headerValueList = StringUtils.string2list(sortNameFields[2], ",");
        this.dataList = getData(sortFields, sortNameFields);
        RequestContext.getCurrentInstance().update(":mainForm:dataTablePanel");
    }

    /**
     * 查询统计结果
     * @param sortFields 统计维度数组
     * @param sortNameFields 统计项数组
     * @return 统计结果
     */
    public List<Object[]> getData(String[] sortFields, String[] sortNameFields) {
        String sql = "";
        sql += "WITH TEMP_TABLE AS ( ";
        sql += "    SELECT NVL(" + sortFields[3] + ", 9999999999999) AS IDX, NVL(" + sortFields[0] + ", 'null') AS NAME, TZOR.SORT_ID AS SORT_ID, COUNT(1) AS NUM ";
        sql += "    FROM TD_ZW_OCCHETH_RPT TZOR ";
        sql += "        LEFT JOIN TS_ZONE TZ12 ON TZ12.RID = TZOR.ZONE_ID AND TZ12.IF_REVEAL = 1 ";
        if ("1".equals(this.searchDimension)) {
            sql += "        LEFT JOIN TS_ZONE TZ11 ON TZ11.IF_REVEAL = 1 AND TZ11.ZONE_GB = SUBSTR(TZ12.ZONE_GB, 1, " + sortFields[5] + ") || '" + sortFields[4] + "' ";
        }
        sql += "        LEFT JOIN TS_UNIT TU ON TU.RID = TZOR.UNIT_ID ";
        sql += "        LEFT JOIN TS_ZONE TZ22 ON TZ22.RID = TU.ZONE_ID AND TZ22.IF_REVEAL = 1 ";
        if ("6".equals(this.searchDimension)) {
            sql += "        LEFT JOIN TS_ZONE TZ21 ON TZ21.IF_REVEAL = 1 AND TZ21.ZONE_GB = SUBSTR(TZ22.ZONE_GB, 1, " + sortFields[5] + ") || '" + sortFields[4] + "' ";
        }
        if (StringUtils.isNotBlank(sortFields[1])) {
            sql += "        LEFT JOIN TS_SIMPLE_CODE TSC ON TSC.IF_REVEAL = 1 AND TSC.RID =" + sortFields[1] + " ";
        }
        sql += "    WHERE TZOR.STATE = 1 AND NVL(TZOR.DEL_MARK, 0) <> 1 AND NVL(" + sortFields[0] + ", 'null') <> 'null'";
        //用人单位地区
        if (StringUtils.isNotBlank(this.searchCrptZoneCode)) {
            sql += "        AND TZ12.ZONE_GB LIKE '" + ZoneUtil.zoneSelect(this.searchCrptZoneCode) + "%' ";
        }
        //用人单位名称
        if (!CollectionUtils.isEmpty(this.searchCrptId)) {
            sql += "        AND TZOR.CRPT_ID IN (" + StringUtils.list2string(this.searchCrptId, ",") + ") ";
        }
        //行业类别
        if (!CollectionUtils.isEmpty(this.searchIndusType)) {
            sql += "        AND TZOR.INDUS_TYPE_ID IN (" + StringUtils.list2string(this.searchIndusType, ",") + ") ";
        }
        //经济性质
        if (!CollectionUtils.isEmpty(this.searchEconomy)) {
            sql += "        AND TZOR.ECONOMY_ID IN (" + StringUtils.list2string(this.searchEconomy, ",") + ") ";
        }
        //企业规模
        if (StringUtils.isNotBlank(this.selectCrptSizeIds)) {
            sql += "        AND TZOR.CRPT_SIZE_ID IN (" + this.selectCrptSizeIds + ") ";
        }
        //职业病危害风险分类
        if (StringUtils.isNotBlank(this.selectZybRiskIds)) {
            sql += "        AND TZOR.ZYB_RISK_ID IN (" + this.selectZybRiskIds + ") ";
        }
        //技术服务机构地区
        if (StringUtils.isNotBlank(this.searchOccZoneCode)) {
            sql += "        AND TZ22.ZONE_GB LIKE '" + ZoneUtil.zoneSelect(this.searchOccZoneCode) + "%' ";
        }
        //技术服务机构名称
        if (!CollectionUtils.isEmpty(this.searchOccId)) {
            sql += "        AND TZOR.UNIT_ID IN (" + StringUtils.list2string(this.searchOccId, ",") + ") ";
        }
        //报告日期
        if (this.searchStartDate != null) {
            sql += "        AND TZOR.RPT_DATE >= TO_DATE('" + DateUtils.formatDate(this.searchStartDate) + "','YYYY-MM-DD') ";
        }
        if (this.searchEndDate != null) {
            sql += "        AND TZOR.RPT_DATE <= TO_DATE('" + DateUtils.formatDate(this.searchEndDate) + "','YYYY-MM-DD') ";
        }
        sql += "    GROUP BY " + sortFields[3] + "," + sortFields[0] + ", TZOR.SORT_ID ";
        sql += "),TEMP_TABLE1 AS ( ";
        sql += "    SELECT * FROM TEMP_TABLE PIVOT (MAX(NUM) FOR SORT_ID IN (" + sortNameFields[0] + ")) ORDER BY IDX ";
        sql += ") SELECT * FROM (";
        sql += "SELECT DECODE(NAME, NULL, '合计', NAME) AS NAME, " + sortNameFields[1] + ", IDX AS IDX FROM TEMP_TABLE1 GROUP BY ROLLUP(NAME, IDX) ORDER BY IDX ";
        sql += ") WHERE (NVL(IDX || '', 'NULL') <> 'NULL') OR (NAME = '合计')";
        @SuppressWarnings("unchecked")
        List<Object[]> dataList = this.commService.findDataBySqlNoPage(sql, null);
        return dataList;
    }

    /**
     * 获取5506码表
     */
    private void getSimpleCodeMap5506() {
        //获取技术服务类别码表
        List<TsSimpleCode> simpleCodeList = commService.findSimpleCodesByTypeId("5506");
        for (int i = 0; i < simpleCodeList.size(); i++) {
            TsSimpleCode simpleCode = simpleCodeList.get(i);
            this.simpleCodeMap.put("A" + (i + 1), simpleCode);
        }
    }

    /**
     * 根据统计维度获取统计相关信息
     *
     * @return 统计维度数组
     * <p>sortFields[0]：统计维度字段名
     * <p>sortFields[1]：统计维度为xxxx码表时使用：统计维度对应码表字段名
     * <p>sortFields[2]：统计维度名称
     * <p>sortFields[3]：统计维度排序字段名
     * <p>sortFields[4]：统计维度为地区时使用：统计维度地区级别(取页面选择地区下级,页面未选择地区取账号级别,最低到区级)
     * <p>sortFields[5]：统计维度为地区时使用：统计维度地区GB去尾0长度(取页面选择地区下级,页面未选择地区取账号级别,最低到区级)
     */
    private String[] getSortFields() {
        String[] sortFields = new String[6];
        sortFields[0] = "";
        switch (this.searchDimension) {
            case "1":
                if (StringUtils.isBlank(this.searchCrptZoneCode)) {
                    JsfUtil.addSuccessMessage("当统计维度为用人单位地区时，用人单位地区必填！");
                    break;
                }
                sortFields[0] = "TZ11.ZONE_NAME";
                sortFields[2] = "用人单位地区";
                sortFields[3] = "TZ11.ZONE_GB";
                if (ZoneUtil.zoneSelect(this.searchCrptZoneCode).length() == 0){
                    sortFields[4] = "00000000";
                    sortFields[5] = "2";
                } else if (ZoneUtil.zoneSelect(this.searchCrptZoneCode).length() == 2){
                    sortFields[4] = "000000";
                    sortFields[5] = "4";
                } else {
                    sortFields[4] = "0000";
                    sortFields[5] = "6";
                }
                break;
            case "2":
                if (CollectionUtils.isEmpty(this.searchCrptId)) {
                    JsfUtil.addSuccessMessage("请选择用人单位名称！");
                    break;
                }
                sortFields[0] = "TZOR.CRPT_NAME";
                sortFields[2] = "用人单位名称";
                sortFields[3] = "TZOR.CRPT_NAME";
                break;
            case "3":
                sortFields[0] = "TSC.CODE_NAME";
                sortFields[1] = "TZOR.INDUS_TYPE_ID";
                sortFields[2] = "行业类别";
                sortFields[3] = "TSC.CODE_LEVEL_NO";
                break;
            case "4":
                sortFields[0] = "TSC.CODE_NAME";
                sortFields[1] = "TZOR.ECONOMY_ID";
                sortFields[2] = "经济性质";
                sortFields[3] = "TSC.CODE_LEVEL_NO";
                break;
            case "5":
                sortFields[0] = "TSC.CODE_NAME";
                sortFields[1] = "TZOR.CRPT_SIZE_ID";
                sortFields[2] = "企业规模";
                sortFields[3] = "TSC.NUM";
                break;
            case "6":
                if (StringUtils.isBlank(this.searchOccZoneCode)) {
                    JsfUtil.addSuccessMessage("当统计维度为技术服务机构地区时，技术服务机构地区必填！");
                    break;
                }
                sortFields[0] = "TZ21.ZONE_NAME";
                sortFields[2] = "技术服务机构地区";
                sortFields[3] = "TZ21.ZONE_GB";
                if (ZoneUtil.zoneSelect(this.searchOccZoneCode).length() == 0){
                    sortFields[4] = "00000000";
                    sortFields[5] = "2";
                } else if (ZoneUtil.zoneSelect(this.searchOccZoneCode).length() == 2){
                    sortFields[4] = "000000";
                    sortFields[5] = "4";
                } else {
                    sortFields[4] = "0000";
                    sortFields[5] = "6";
                }
                break;
            case "7":
                if (CollectionUtils.isEmpty(this.searchOccId)) {
                    JsfUtil.addSuccessMessage("请选择技术服务机构名称！");
                    break;
                }
                sortFields[0] = "TU.UNITNAME";
                sortFields[2] = "技术服务机构名称";
                sortFields[3] = "TU.UNITNAME";
                break;
            default:
        }
        return sortFields;
    }

    /**
     * 获取统计项数组
     * @return 统计项数组 sortNameFields[2]：统计项名称
     */
    private String[] getSortNameFields() {
        String[] sortFields = new String[3];
        sortFields[0] = "";
        sortFields[1] = "";
        sortFields[2] = "";
        for (Map.Entry<String, TsSimpleCode> tscEntry : this.simpleCodeMap.entrySet()) {
            sortFields[0] += ", '" + tscEntry.getValue().getRid() + "' AS " + tscEntry.getKey();
            sortFields[1] += ", NVL(SUM(" + tscEntry.getKey() + "), 0) AS " + tscEntry.getKey();
            sortFields[2] += "," + tscEntry.getValue().getCodeName();
        }
        if (StringUtils.isNotBlank(sortFields[0]) ||
                StringUtils.isNotBlank(sortFields[1]) ||
                StringUtils.isNotBlank(sortFields[2])) {
            sortFields[0] = sortFields[0].substring(1);
            sortFields[1] = sortFields[1].substring(1);
        } else {
            return null;
        }
        return sortFields;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public List<TsZone> getOrgZoneList() {
        return orgZoneList;
    }

    public void setOrgZoneList(List<TsZone> orgZoneList) {
        this.orgZoneList = orgZoneList;
    }

    public String getSearchCrptZoneName() {
        return searchCrptZoneName;
    }

    public void setSearchCrptZoneName(String searchCrptZoneName) {
        this.searchCrptZoneName = searchCrptZoneName;
    }

    public String getSearchCrptZoneCode() {
        return searchCrptZoneCode;
    }

    public void setSearchCrptZoneCode(String searchCrptZoneCode) {
        this.searchCrptZoneCode = searchCrptZoneCode;
    }

    public List<Integer> getSearchCrptId() {
        return searchCrptId;
    }

    public void setSearchCrptId(List<Integer> searchCrptId) {
        this.searchCrptId = searchCrptId;
    }

    public List<Integer> getSearchIndusType() {
        return searchIndusType;
    }

    public void setSearchIndusType(List<Integer> searchIndusType) {
        this.searchIndusType = searchIndusType;
    }

    public List<Integer> getSearchEconomy() {
        return searchEconomy;
    }

    public void setSearchEconomy(List<Integer> searchEconomy) {
        this.searchEconomy = searchEconomy;
    }

    public String getSearchOccZoneName() {
        return searchOccZoneName;
    }

    public void setSearchOccZoneName(String searchOccZoneName) {
        this.searchOccZoneName = searchOccZoneName;
    }

    public String getSearchOccZoneCode() {
        return searchOccZoneCode;
    }

    public void setSearchOccZoneCode(String searchOccZoneCode) {
        this.searchOccZoneCode = searchOccZoneCode;
    }

    public List<Integer> getSearchOccId() {
        return searchOccId;
    }

    public void setSearchOccId(List<Integer> searchOccId) {
        this.searchOccId = searchOccId;
    }

    public Date getSearchStartDate() {
        return searchStartDate;
    }

    public void setSearchStartDate(Date searchStartDate) {
        this.searchStartDate = searchStartDate;
    }

    public Date getSearchEndDate() {
        return searchEndDate;
    }

    public void setSearchEndDate(Date searchEndDate) {
        this.searchEndDate = searchEndDate;
    }

    public String getSearchDimension() {
        return searchDimension;
    }

    public void setSearchDimension(String searchDimension) {
        this.searchDimension = searchDimension;
    }

    public List<TsSimpleCode> getIndusTypeList() {
        return indusTypeList;
    }

    public void setIndusTypeList(List<TsSimpleCode> indusTypeList) {
        this.indusTypeList = indusTypeList;
    }

    public List<TsSimpleCode> getEconomyList() {
        return economyList;
    }

    public void setEconomyList(List<TsSimpleCode> economyList) {
        this.economyList = economyList;
    }

    public List<TsSimpleCode> getCrptSizeList() {
        return crptSizeList;
    }

    public void setCrptSizeList(List<TsSimpleCode> crptSizeList) {
        this.crptSizeList = crptSizeList;
    }

    public List<TsSimpleCode> getZybRiskList() {
        return zybRiskList;
    }

    public void setZybRiskList(List<TsSimpleCode> zybRiskList) {
        this.zybRiskList = zybRiskList;
    }

    public String getSelectCrptNames() {
        return selectCrptNames;
    }

    public void setSelectCrptNames(String selectCrptNames) {
        this.selectCrptNames = selectCrptNames;
    }

    public String getSelectEconomyNames() {
        return selectEconomyNames;
    }

    public void setSelectEconomyNames(String selectEconomyNames) {
        this.selectEconomyNames = selectEconomyNames;
    }

    public String getSelectCrptSizeIds() {
        return selectCrptSizeIds;
    }

    public void setSelectCrptSizeIds(String selectCrptSizeIds) {
        this.selectCrptSizeIds = selectCrptSizeIds;
    }

    public String getSelectZybRiskIds() {
        return selectZybRiskIds;
    }

    public void setSelectZybRiskIds(String selectZybRiskIds) {
        this.selectZybRiskIds = selectZybRiskIds;
    }

    public String getSelectIndusTypeNames() {
        return selectIndusTypeNames;
    }

    public void setSelectIndusTypeNames(String selectIndusTypeNames) {
        this.selectIndusTypeNames = selectIndusTypeNames;
    }

    public String getSimpleCodeType() {
        return simpleCodeType;
    }

    public void setSimpleCodeType(String simpleCodeType) {
        this.simpleCodeType = simpleCodeType;
    }

    public String getSelectOccNames() {
        return selectOccNames;
    }

    public void setSelectOccNames(String selectOccNames) {
        this.selectOccNames = selectOccNames;
    }

    public TsZone getTsZone() {
        return tsZone;
    }

    public void setTsZone(TsZone tsZone) {
        this.tsZone = tsZone;
    }

    public Map<String, TsSimpleCode> getSimpleCodeMap() {
        return simpleCodeMap;
    }

    public void setSimpleCodeMap(Map<String, TsSimpleCode> simpleCodeMap) {
        this.simpleCodeMap = simpleCodeMap;
    }

    public List<String> getHeaderValueList() {
        return headerValueList;
    }

    public void setHeaderValueList(List<String> headerValueList) {
        this.headerValueList = headerValueList;
    }

    public List<Object[]> getDataList() {
        return dataList;
    }

    public void setDataList(List<Object[]> dataList) {
        this.dataList = dataList;
    }
}
