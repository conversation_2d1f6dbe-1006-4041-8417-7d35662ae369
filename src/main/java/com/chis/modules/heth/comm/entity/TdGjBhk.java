package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2024-8-15
 */
@Entity
@Table(name = "TD_GJ_BHK")
@SequenceGenerator(name = "TdGjBhk", sequenceName = "TD_GJ_BHK_SEQ", allocationSize = 1)
public class TdGjBhk implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private String bhkCode;
    private TbTjSrvorg fkByBhkorgId;
    private TbTjCrpt fkByEntrustCrptId;
    private TdTjPerson fkByPersonId;
    private String personName;
    private String sex;
    private TsSimpleCode fkByCardTypeId;
    private String idc;
    private Date brth;
    private Integer age;
    private Integer tchbadrsntim;
    private Integer tchbadrsnmonth;
    private TsSimpleCode fkByOnguardStateid;
    private Date bhkDate;
    private Integer ifRhk;
    private Date rptPrintDate;
    private TsSimpleCode fkByWorkTypeId;
    private String workOther;
    private Integer jcType;
    private Integer chestResult;
    private TsSimpleCode fkByHearingRstId;
    private Date importDate;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    private List<TdGjTchBadrsns> tdGjTchBadrsns;
    private List<TdGjBadrsns> tdGjBadrsns;
    private List<TdGjBhksub> tdGjBhksub;
    //接触的危害因素
    private String tchBadrsns;
    //体检结论
    private String tjConclusions;


    public TdGjBhk() {
    }

    public TdGjBhk(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdGjBhk")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Column(name = "BHK_CODE")
    public String getBhkCode() {
        return this.bhkCode;
    }

    public void setBhkCode(String bhkCode) {
        this.bhkCode = bhkCode;
    }

    @ManyToOne
    @JoinColumn(name = "BHKORG_ID")
    public TbTjSrvorg getFkByBhkorgId() {
        return this.fkByBhkorgId;
    }

    public void setFkByBhkorgId(TbTjSrvorg fkByBhkorgId) {
        this.fkByBhkorgId = fkByBhkorgId;
    }

    @ManyToOne
    @JoinColumn(name = "ENTRUST_CRPT_ID")
    public TbTjCrpt getFkByEntrustCrptId() {
        return this.fkByEntrustCrptId;
    }

    public void setFkByEntrustCrptId(TbTjCrpt fkByEntrustCrptId) {
        this.fkByEntrustCrptId = fkByEntrustCrptId;
    }

    @ManyToOne
    @JoinColumn(name = "PERSON_ID")
    public TdTjPerson getFkByPersonId() {
        return this.fkByPersonId;
    }

    public void setFkByPersonId(TdTjPerson fkByPersonId) {
        this.fkByPersonId = fkByPersonId;
    }

    @Column(name = "PERSON_NAME")
    public String getPersonName() {
        return this.personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    @Column(name = "SEX")
    public String getSex() {
        return this.sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    @ManyToOne
    @JoinColumn(name = "CARD_TYPE_ID")
    public TsSimpleCode getFkByCardTypeId() {
        return this.fkByCardTypeId;
    }

    public void setFkByCardTypeId(TsSimpleCode fkByCardTypeId) {
        this.fkByCardTypeId = fkByCardTypeId;
    }

    @Column(name = "IDC")
    public String getIdc() {
        return this.idc;
    }

    public void setIdc(String idc) {
        this.idc = idc;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BRTH")
    public Date getBrth() {
        return this.brth;
    }

    public void setBrth(Date brth) {
        this.brth = brth;
    }

    @Column(name = "AGE")
    public Integer getAge() {
        return this.age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    @Column(name = "TCHBADRSNTIM")
    public Integer getTchbadrsntim() {
        return this.tchbadrsntim;
    }

    public void setTchbadrsntim(Integer tchbadrsntim) {
        this.tchbadrsntim = tchbadrsntim;
    }

    @Column(name = "TCHBADRSNMONTH")
    public Integer getTchbadrsnmonth() {
        return this.tchbadrsnmonth;
    }

    public void setTchbadrsnmonth(Integer tchbadrsnmonth) {
        this.tchbadrsnmonth = tchbadrsnmonth;
    }

    @ManyToOne
    @JoinColumn(name = "ONGUARD_STATEID")
    public TsSimpleCode getFkByOnguardStateid() {
        return this.fkByOnguardStateid;
    }

    public void setFkByOnguardStateid(TsSimpleCode fkByOnguardStateid) {
        this.fkByOnguardStateid = fkByOnguardStateid;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BHK_DATE")
    public Date getBhkDate() {
        return this.bhkDate;
    }

    public void setBhkDate(Date bhkDate) {
        this.bhkDate = bhkDate;
    }

    @Column(name = "IF_RHK")
    public Integer getIfRhk() {
        return this.ifRhk;
    }

    public void setIfRhk(Integer ifRhk) {
        this.ifRhk = ifRhk;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "RPT_PRINT_DATE")
    public Date getRptPrintDate() {
        return this.rptPrintDate;
    }

    public void setRptPrintDate(Date rptPrintDate) {
        this.rptPrintDate = rptPrintDate;
    }

    @ManyToOne
    @JoinColumn(name = "WORK_TYPE_ID")
    public TsSimpleCode getFkByWorkTypeId() {
        return this.fkByWorkTypeId;
    }

    public void setFkByWorkTypeId(TsSimpleCode fkByWorkTypeId) {
        this.fkByWorkTypeId = fkByWorkTypeId;
    }

    @Column(name = "WORK_OTHER")
    public String getWorkOther() {
        return this.workOther;
    }

    public void setWorkOther(String workOther) {
        this.workOther = workOther;
    }

    @Column(name = "JC_TYPE")
    public Integer getJcType() {
        return this.jcType;
    }

    public void setJcType(Integer jcType) {
        this.jcType = jcType;
    }

    @Column(name = "CHEST_RESULT")
    public Integer getChestResult() {
        return this.chestResult;
    }

    public void setChestResult(Integer chestResult) {
        this.chestResult = chestResult;
    }

    @ManyToOne
    @JoinColumn(name = "HEARING_RST_ID")
    public TsSimpleCode getFkByHearingRstId() {
        return this.fkByHearingRstId;
    }

    public void setFkByHearingRstId(TsSimpleCode fkByHearingRstId) {
        this.fkByHearingRstId = fkByHearingRstId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Column(name = "IMPORT_DATE")
    public Date getImportDate() {
        return this.importDate;
    }

    public void setImportDate(Date importDate) {
        this.importDate = importDate;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdGjTchBadrsns> getTdGjTchBadrsns() {
        return this.tdGjTchBadrsns;
    }

    public void setTdGjTchBadrsns(List<TdGjTchBadrsns> tdGjTchBadrsns) {
        this.tdGjTchBadrsns = tdGjTchBadrsns;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdGjBadrsns> getTdGjBadrsns() {
        return this.tdGjBadrsns;
    }

    public void setTdGjBadrsns(List<TdGjBadrsns> tdGjBadrsns) {
        this.tdGjBadrsns = tdGjBadrsns;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdGjBhksub> getTdGjBhksub() {
        return this.tdGjBhksub;
    }

    public void setTdGjBhksub(List<TdGjBhksub> tdGjBhksub) {
        this.tdGjBhksub = tdGjBhksub;
    }

    @Transient
    public String getTchBadrsns() {
        return this.tchBadrsns;
    }

    public void setTchBadrsns(String tchBadrsns) {
        this.tchBadrsns = tchBadrsns;
    }

    @Transient
    public String getTjConclusions() {
        return this.tjConclusions;
    }

    public void setTjConclusions(String tjConclusions) {
        this.tjConclusions = tjConclusions;
    }
}