package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import java.math.BigInteger;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * 问诊项目
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置<br/>
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 */
@Entity
@Table(name = "TD_TJ_EXMSDATA")
@SequenceGenerator(name = "TdTjExmsdata_Seq", sequenceName = "TD_TJ_EXMSDATA_SEQ", allocationSize = 1)
public class TdTjExmsdata implements java.io.Serializable {

    private static final long serialVersionUID = 3667559813978295725L;
    private BigInteger rid;
    private TdTjBhk tdTjBhk;
    private Integer mnrage;
    private String mns;
    private String cyc;
    private Integer mnlage;
    private Integer isxmns;
    private Integer chldqty;
    private Integer abrqty;
    private Integer slnkqty;
    private Integer stlqty;
    private Integer trsqty;
    private String chldhthcnd;
    private String mrydat;
    private String cplrdtcnd;
    private String cplprfhthcnd;
    private Integer smksta;
    private String smkdayble;
    private String smkyerqty;
    private Integer winsta;
    private String windaymlx;
    private String winyerqty;
    private String jzs;
    private String grs;
    private String oth;
    private Date createDate;
    private Integer createManid;

    /**吸烟史-月*/
    private Integer smkmthqty;

    /**目前吸烟情况*/
    private TsSimpleCode fkBySmkstaId;

    // Constructors

    /** default constructor */
    public TdTjExmsdata() {
    }

    /** minimal constructor */
    public TdTjExmsdata(BigInteger rid, TdTjBhk tdTjBhk, Date createDate,
                        Integer createManid) {
        this.rid = rid;
        this.tdTjBhk = tdTjBhk;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    /** full constructor */
    public TdTjExmsdata(BigInteger rid, TdTjBhk tdTjBhk, Integer mnrage,
                        String mns, String cyc, Integer mnlage, Integer isxmns,
                        Integer chldqty, Integer abrqty, Integer slnkqty, Integer stlqty,
                        Integer trsqty, String mrydat, String cplrdtcnd, String cplprfhthcnd,
                        Integer smksta, String smkdayble, String smkyerqty, Integer winsta,
                        String windaymlx, String winyerqty, String jzs, String grs,
                        String oth, Date createDate, Integer createManid) {
        this.rid = rid;
        this.tdTjBhk = tdTjBhk;
        this.mnrage = mnrage;
        this.mns = mns;
        this.cyc = cyc;
        this.mnlage = mnlage;
        this.isxmns = isxmns;
        this.chldqty = chldqty;
        this.abrqty = abrqty;
        this.slnkqty = slnkqty;
        this.stlqty = stlqty;
        this.trsqty = trsqty;
        this.mrydat = mrydat;
        this.cplrdtcnd = cplrdtcnd;
        this.cplprfhthcnd = cplprfhthcnd;
        this.smksta = smksta;
        this.smkdayble = smkdayble;
        this.smkyerqty = smkyerqty;
        this.winsta = winsta;
        this.windaymlx = windaymlx;
        this.winyerqty = winyerqty;
        this.jzs = jzs;
        this.grs = grs;
        this.oth = oth;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjExmsdata_Seq")
    public BigInteger getRid() {
        return this.rid;
    }

    public void setRid(BigInteger rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "BHK_ID" )
    public TdTjBhk getTdTjBhk() {
        return this.tdTjBhk;
    }

    public void setTdTjBhk(TdTjBhk tdTjBhk) {
        this.tdTjBhk = tdTjBhk;
    }

    @Column(name = "MNRAGE")
    public Integer getMnrage() {
        return this.mnrage;
    }

    public void setMnrage(Integer mnrage) {
        this.mnrage = mnrage;
    }

    @Column(name = "MNS", length = 10)
    public String getMns() {
        return this.mns;
    }

    public void setMns(String mns) {
        this.mns = mns;
    }

    @Column(name = "CYC", length = 10)
    public String getCyc() {
        return this.cyc;
    }

    public void setCyc(String cyc) {
        this.cyc = cyc;
    }

    @Column(name = "MNLAGE")
    public Integer getMnlage() {
        return this.mnlage;
    }

    public void setMnlage(Integer mnlage) {
        this.mnlage = mnlage;
    }

    @Column(name = "ISXMNS")
    public Integer getIsxmns() {
        return this.isxmns;
    }

    public void setIsxmns(Integer isxmns) {
        this.isxmns = isxmns;
    }

    @Column(name = "CHLDQTY")
    public Integer getChldqty() {
        return this.chldqty;
    }

    public void setChldqty(Integer chldqty) {
        this.chldqty = chldqty;
    }

    @Column(name = "ABRQTY")
    public Integer getAbrqty() {
        return this.abrqty;
    }

    public void setAbrqty(Integer abrqty) {
        this.abrqty = abrqty;
    }

    @Column(name = "SLNKQTY")
    public Integer getSlnkqty() {
        return this.slnkqty;
    }

    public void setSlnkqty(Integer slnkqty) {
        this.slnkqty = slnkqty;
    }

    @Column(name = "STLQTY")
    public Integer getStlqty() {
        return this.stlqty;
    }

    public void setStlqty(Integer stlqty) {
        this.stlqty = stlqty;
    }

    @Column(name = "TRSQTY")
    public Integer getTrsqty() {
        return this.trsqty;
    }

    public void setTrsqty(Integer trsqty) {
        this.trsqty = trsqty;
    }

    @Column(name = "CHLDHTHCND")
    public String getChldhthcnd() {
        return chldhthcnd;
    }
    public void setChldhthcnd(String chldhthcnd) {
        this.chldhthcnd = chldhthcnd;
    }

    @Column(name = "MRYDAT")
    public String getMrydat() {
        return this.mrydat;
    }

    public void setMrydat(String mrydat) {
        this.mrydat = mrydat;
    }

    @Column(name = "CPLRDTCND", length = 100)
    public String getCplrdtcnd() {
        return this.cplrdtcnd;
    }

    public void setCplrdtcnd(String cplrdtcnd) {
        this.cplrdtcnd = cplrdtcnd;
    }

    @Column(name = "CPLPRFHTHCND", length = 100)
    public String getCplprfhthcnd() {
        return this.cplprfhthcnd;
    }

    public void setCplprfhthcnd(String cplprfhthcnd) {
        this.cplprfhthcnd = cplprfhthcnd;
    }

    @Column(name = "SMKSTA")
    public Integer getSmksta() {
        return this.smksta;
    }

    public void setSmksta(Integer smksta) {
        this.smksta = smksta;
    }

    @Column(name = "SMKDAYBLE", length = 10)
    public String getSmkdayble() {
        return this.smkdayble;
    }

    public void setSmkdayble(String smkdayble) {
        this.smkdayble = smkdayble;
    }

    @Column(name = "SMKYERQTY", length = 10)
    public String getSmkyerqty() {
        return this.smkyerqty;
    }

    public void setSmkyerqty(String smkyerqty) {
        this.smkyerqty = smkyerqty;
    }

    @Column(name = "WINSTA")
    public Integer getWinsta() {
        return this.winsta;
    }

    public void setWinsta(Integer winsta) {
        this.winsta = winsta;
    }

    @Column(name = "WINDAYMLX", length = 10)
    public String getWindaymlx() {
        return this.windaymlx;
    }

    public void setWindaymlx(String windaymlx) {
        this.windaymlx = windaymlx;
    }

    @Column(name = "WINYERQTY", length = 10)
    public String getWinyerqty() {
        return this.winyerqty;
    }

    public void setWinyerqty(String winyerqty) {
        this.winyerqty = winyerqty;
    }

    @Column(name = "JZS")
    public String getJzs() {
        return this.jzs;
    }

    public void setJzs(String jzs) {
        this.jzs = jzs;
    }

    @Column(name = "GRS")
    public String getGrs() {
        return this.grs;
    }

    public void setGrs(String grs) {
        this.grs = grs;
    }

    @Column(name = "OTH")
    public String getOth() {
        return this.oth;
    }

    public void setOth(String oth) {
        this.oth = oth;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Column(name = "SMKMTHQTY" )
    public Integer getSmkmthqty() {
        return smkmthqty;
    }

    public void setSmkmthqty(Integer smkmthqty) {
        this.smkmthqty = smkmthqty;
    }

    @ManyToOne
    @JoinColumn(name = "SMKSTA_ID" )
    public TsSimpleCode getFkBySmkstaId() {
        return fkBySmkstaId;
    }

    public void setFkBySmkstaId(TsSimpleCode fkBySmkstaId) {
        this.fkBySmkstaId = fkBySmkstaId;
    }

}
