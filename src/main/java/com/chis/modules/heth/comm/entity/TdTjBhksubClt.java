package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * @Description : 体检子表（数据录入）
 * @ClassAuthor: anjing
 * @Date : 2019/5/14 10:30
 **/
@Entity
@Table(name = "TD_TJ_BHKSUB_CLT")
@SequenceGenerator(name = "TdTjBhksubClt_Seq", sequenceName = "TD_TJ_BHKSUB_CLT_SEQ", allocationSize = 1)
public class TdTjBhksubClt implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /**主键Id*/
    private Integer rid;
    /**关联：体检主表-录入数据（体检主表Id）*/
    private TdTjBhkClt fkByBhkId;
    /**关联：体检项目标准库（GBZ-188）*/
    private TbTjItems fkByItemId;
    /**计量单位*/
    private String msrunt;
    /**参考值*/
    private String itemStdvalue;
    /**体检结果*/
    private String itemRst;
    /**合格标记*/
    private Integer rgltag;
    /**偏高偏低*/
    private Integer rstDesc;
    /**是否缺项*/
    private Integer ifLack;
    /**检查日期*/
    private Date chkdat;
    /**关联：人员信息（检查医生Id）*/
    private TdZwPsninfoComm fkByChkdoctId;
    /**判断模式*/
    private Short jdgptn;
    /**最小值*/
    private BigDecimal minval;
    /**最大值*/
    private BigDecimal maxval;
    /**诊断结论*/
    private String diagRest;
    /**创建日期*/
    private Date createDate;
    /**创建人*/
    private Integer createManid;
    /**修改日期*/
    private Date modifyDate;
    /**修改人*/
    private Integer modifyManid;
    /**是否必检:0 非必检 1 必检*/
    private Integer isMust;

    private boolean ifrgltag;
    private boolean lack;

    /**原始值*/
    private String itemRstOri;
    /**+结果判定标记20210504*/
    private Integer rstFlag;
    /**
     * 符号（正常结果）ID 20220705
     */
    private TsSimpleCode fkByDataVersionId;
    /**
     * 符号（电测听-原始值）ID 20220704
     */
    private TsSimpleCode fkByDataVersionOriId;

    /************** 非实体字段 ***************/
    /**定性项目描述*/
    List<TbTjRstdesc> tbTjRstdescList = new ArrayList<>();
    private Integer chkdoctId;

    private TsSimpleCode fkByMsruntId;
    /**
     * 符号（正常结果）ID
     */
    private Integer dataVersionId;
    /**
     * 符号（电测听-原始值）ID
     */
    private Integer dataVersionOriId;


    public TdTjBhksubClt() {
    }

    public TdTjBhksubClt(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjBhksubClt_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "BHK_ID")
    public TdTjBhkClt getFkByBhkId() {
        return fkByBhkId;
    }

    public void setFkByBhkId(TdTjBhkClt fkByBhkId) {
        this.fkByBhkId = fkByBhkId;
    }

    @ManyToOne
    @JoinColumn(name = "ITEM_ID" )
    public TbTjItems getFkByItemId() {
        return fkByItemId;
    }

    public void setFkByItemId(TbTjItems fkByItemId) {
        this.fkByItemId = fkByItemId;
    }

    @Column(name = "MSRUNT", length = 50)
    public String getMsrunt() {
        return msrunt;
    }

    public void setMsrunt(String msrunt) {
        this.msrunt = msrunt;
    }

    @Column(name = "ITEM_STDVALUE", length = 100)
    public String getItemStdvalue() {
        return itemStdvalue;
    }

    public void setItemStdvalue(String itemStdvalue) {
        this.itemStdvalue = itemStdvalue;
    }

    @Lob
    @Column(name = "ITEM_RST" )
    public String getItemRst() {
        return itemRst;
    }

    public void setItemRst(String itemRst) {
        this.itemRst = itemRst;
    }

    @Column(name = "RGLTAG" )
    public Integer getRgltag() {
        return rgltag;
    }

    public void setRgltag(Integer rgltag) {
        this.rgltag = rgltag;
    }

    @Column(name = "RST_DESC", length = 10)
    public Integer getRstDesc() {
        return rstDesc;
    }

    public void setRstDesc(Integer rstDesc) {
        this.rstDesc = rstDesc;
    }

    @Column(name = "IF_LACK" )
    public Integer getIfLack() {
        return ifLack;
    }

    public void setIfLack(Integer ifLack) {
        this.ifLack = ifLack;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "CHKDAT", length = 7)
    public Date getChkdat() {
        return chkdat;
    }

    public void setChkdat(Date chkdat) {
        this.chkdat = chkdat;
    }

    @ManyToOne
    @JoinColumn(name = "CHKDOCT_ID")
    public TdZwPsninfoComm getFkByChkdoctId() {
        return fkByChkdoctId;
    }

    public void setFkByChkdoctId(TdZwPsninfoComm fkByChkdoctId) {
        this.fkByChkdoctId = fkByChkdoctId;
    }

    @Column(name = "JDGPTN" )
    public Short getJdgptn() {
        return jdgptn;
    }

    public void setJdgptn(Short jdgptn) {
        this.jdgptn = jdgptn;
    }

    @Column(name = "MINVAL")
    public BigDecimal getMinval() {
        return minval;
    }

    public void setMinval(BigDecimal minval) {
        this.minval = minval;
    }

    @Column(name = "MAXVAL")
    public BigDecimal getMaxval() {
        return maxval;
    }

    public void setMaxval(BigDecimal maxval) {
        this.maxval = maxval;
    }

    @Column(name = "DIAG_REST")
    public String getDiagRest() {
        return diagRest;
    }

    public void setDiagRest(String diagRest) {
        this.diagRest = diagRest;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Column(name = "IS_MUST" )
    public Integer getIsMust() {
        return isMust;
    }

    public void setIsMust(Integer isMust) {
        this.isMust = isMust;
    }

    @Transient
    public List<TbTjRstdesc> getTbTjRstdescList() {
        return tbTjRstdescList;
    }

    public void setTbTjRstdescList(List<TbTjRstdesc> tbTjRstdescList) {
        this.tbTjRstdescList = tbTjRstdescList;
    }

    @Transient
    public boolean isLack() {
        return lack;
    }

    public void setLack(boolean lack) {
        this.lack = lack;
    }

    @Transient
    public boolean isIfrgltag() {
        return ifrgltag;
    }

    public void setIfrgltag(boolean ifrgltag) {
        this.ifrgltag = ifrgltag;
    }

    @Transient
    public Integer getChkdoctId() {
        return chkdoctId;
    }

    public void setChkdoctId(Integer chkdoctId) {
        this.chkdoctId = chkdoctId;
    }

    @ManyToOne
    @JoinColumn(name = "MSRUNT_ID")
    public TsSimpleCode getFkByMsruntId() {
        return fkByMsruntId;
    }

    public void setFkByMsruntId(TsSimpleCode fkByMsruntId) {
        this.fkByMsruntId = fkByMsruntId;
    }
    @Lob
    @Column(name = "ITEM_RST_ORI" )
	public String getItemRstOri() {
		return itemRstOri;
	}

	public void setItemRstOri(String itemRstOri) {
		this.itemRstOri = itemRstOri;
	}

    @Column(name = "RST_FLAG")
    public Integer getRstFlag() {
        return rstFlag;
    }

    public void setRstFlag(Integer rstFlag) {
        this.rstFlag = rstFlag;
    }

    @ManyToOne
    @JoinColumn(name = "DATA_VERSION_ID")
    public TsSimpleCode getFkByDataVersionId() {
        return fkByDataVersionId;
    }

    public void setFkByDataVersionId(TsSimpleCode fkByDataVersionId) {
        this.fkByDataVersionId = fkByDataVersionId;
    }

    @ManyToOne
    @JoinColumn(name = "DATA_VERSION_ORI_ID")
    public TsSimpleCode getFkByDataVersionOriId() {
        return fkByDataVersionOriId;
    }

    public void setFkByDataVersionOriId(TsSimpleCode fkByDataVersionOriId) {
        this.fkByDataVersionOriId = fkByDataVersionOriId;
    }

    @Transient
    public Integer getDataVersionId() {
        return dataVersionId;
    }

    public void setDataVersionId(Integer dataVersionId) {
        this.dataVersionId = dataVersionId;
    }

    @Transient
    public Integer getDataVersionOriId() {
        return dataVersionOriId;
    }

    public void setDataVersionOriId(Integer dataVersionOriId) {
        this.dataVersionOriId = dataVersionOriId;
    }
}
