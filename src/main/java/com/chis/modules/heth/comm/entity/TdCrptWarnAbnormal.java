package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-4-20
 */
@Entity
@Table(name = "TD_CRPT_WARN_ABNORMAL")
@SequenceGenerator(name = "TdCrptWarnAbnormal", sequenceName = "TD_CRPT_WARN_ABNORMAL_SEQ", allocationSize = 1)
public class TdCrptWarnAbnormal implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String creditCode;
	private String badrsnName;
	private String checkNhgCheck;
	private String checkNhgRpt;
	private String postNhgRpt;
	private String postNhgJc;
	private Integer num;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdCrptWarnAbnormal() {
	}

	public TdCrptWarnAbnormal(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdCrptWarnAbnormal")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "CREDIT_CODE")	
	public String getCreditCode() {
		return creditCode;
	}

	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}	
			
	@Column(name = "BADRSN_NAME")	
	public String getBadrsnName() {
		return badrsnName;
	}

	public void setBadrsnName(String badrsnName) {
		this.badrsnName = badrsnName;
	}	
			
	@Column(name = "CHECK_NHG_CHECK")	
	public String getCheckNhgCheck() {
		return checkNhgCheck;
	}

	public void setCheckNhgCheck(String checkNhgCheck) {
		this.checkNhgCheck = checkNhgCheck;
	}	
			
	@Column(name = "CHECK_NHG_RPT")	
	public String getCheckNhgRpt() {
		return checkNhgRpt;
	}

	public void setCheckNhgRpt(String checkNhgRpt) {
		this.checkNhgRpt = checkNhgRpt;
	}	
			
	@Column(name = "POST_NHG_RPT")	
	public String getPostNhgRpt() {
		return postNhgRpt;
	}

	public void setPostNhgRpt(String postNhgRpt) {
		this.postNhgRpt = postNhgRpt;
	}	
			
	@Column(name = "POST_NHG_JC")	
	public String getPostNhgJc() {
		return postNhgJc;
	}

	public void setPostNhgJc(String postNhgJc) {
		this.postNhgJc = postNhgJc;
	}	
			
	@Column(name = "NUM")	
	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}