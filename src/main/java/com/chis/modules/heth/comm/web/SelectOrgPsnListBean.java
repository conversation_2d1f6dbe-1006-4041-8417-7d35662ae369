package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.rptvo.SrvorgPsnVo;
import com.chis.modules.system.interfaces.IProcessPackData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.PackLazyDataModel;
import com.chis.modules.system.web.FacesSimpleBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.ToggleSelectEvent;
import org.primefaces.event.UnselectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.math.BigDecimal;
import java.util.*;

/**
 *  <p>类描述：
 *  资质人员选择
 *  选择的不显示</p>
 * @ClassAuthor hsj 2022-08-20 16:34
 */
@ManagedBean(name="selectOrgPsnListBean")
@ViewScoped
public class SelectOrgPsnListBean extends FacesSimpleBean implements IProcessPackData {
	private static final long serialVersionUID = 1L;
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

	/** 名称 或 拼音码*/
    private String searchName;
	/**页面已选择的*/
	private Set<String> selectIds;
	/**
	 * 资质类型 <pre>1：放射卫生技术服务<pre>2：职业卫生技术服务
	 */
    private String type;
	/**资质rid*/
    private String orgId;
	/**当前页面已选择*/
    private List<SrvorgPsnVo> selectedDatas;
	/**所有页面已选择*/
    private List<SrvorgPsnVo> allSelectedDatas;
	/**真分页模型*/
	private PackLazyDataModel extractDataModel;
	/**表格的ID*/
	private static final String EXTRACT_TABLE_ID = "mainForm:dataTable";

    public SelectOrgPsnListBean() {
    	this.ifSQL = true;
    	String selectIds = JsfUtil.getRequestParameter("selectIds");
		this.selectIds = new HashSet<>();
		this.selectedDatas = new ArrayList<>();
		this.allSelectedDatas = new ArrayList<>();
		if (StringUtils.isNotBlank(selectIds)) {
			String[] split = selectIds.split(",");
			for (String s : split) {
				this.selectIds.add(s);
			}
		}
		this.type = JsfUtil.getRequestParameter("type");
		this.orgId = JsfUtil.getRequestParameter("orgId");
		RequestContext.getCurrentInstance().update("mainForm");
		//置为第一页
		DataTable dataTable = (DataTable) FacesContext
				.getCurrentInstance().getViewRoot()
				.findComponent(EXTRACT_TABLE_ID);
		dataTable.setFirst(0);
		this.searchAction();
	}
	@Override
	public void searchAction() {
		this.paramMap.clear();
		String[] hql = this.buildHqls();
		this.extractDataModel = new PackLazyDataModel<SrvorgPsnVo>(hql[0], hql[1], this.paramMap, this , EXTRACT_TABLE_ID, this.ifSQL);
		selectedDatas.clear();
		selectedDatas.addAll(allSelectedDatas);
	}
    @Override
	public String[] buildHqls() {
    	StringBuffer sql = new StringBuffer();
		sql.append("SELECT T1.RID,T1.EMP_NAME,T1.SEX , T2.CODE_NAME  FROM  ");
		if ("1".equals(type)) {
			//放射
			sql.append(" TD_ZW_SRVORGPSNS T ");
		} else if ("2".equals(type)) {
			sql.append(" TD_ZW_OCCHETH_PSNS T ");
		}

		sql.append("  LEFT JOIN TD_ZW_PSNINFO T1 ON T.EMP_ID = T1.RID   ");
		sql.append("  LEFT JOIN TS_SIMPLE_CODE T2 ON T1.TITLE_ID = T2.RID    ");
		sql.append("   WHERE T.ON_DUTY  = 1   ");
		if(StringUtils.isNotBlank(orgId)){
			sql.append("   AND T.ORG_ID = ").append(orgId);
		} else {
			sql.append("   AND 1 = 2 ");
		}
		if(selectIds != null && selectIds.size() >0){
			StringBuffer buffer = new StringBuffer();
			for (String id : selectIds) {
				buffer.append(",").append(id);
			}
			sql.append("  AND T1.RID not in  ("+buffer.substring(1)+") ");
		}
		if (StringUtils.isNotBlank(searchName)) {
			sql.append(" and T1.EMP_NAME like :searchName escape '\\\'");
			this.paramMap.put("searchName", "%"+StringUtils.convertBFH(searchName.trim())+"%");
		}
		sql.append(" order by nlssort(T1.EMP_NAME,'NLS_SORT=SCHINESE_PINYIN_M')");
		String h2 = "SELECT COUNT(*) FROM (" + sql.toString() + ")";
        String h1 = "SELECT * FROM (" + sql.toString() + ")";
        return new String[]{h1,h2};
	}
	/**
	 *  <p>方法描述：翻页监听</p>
	 * @MethodAuthor hsj 2022-08-26 11:13
	 */
	public void pageListener(){
		selectedDatas.clear();
		selectedDatas.addAll(allSelectedDatas);
		RequestContext.getCurrentInstance().update(EXTRACT_TABLE_ID);
	}
	@Override
	public List processPackData(List list) {
		List<Object[]> lists =(List<Object[]>)list;
		List<SrvorgPsnVo> data = new ArrayList<>();
		if(!CollectionUtils.isEmpty(lists)){
			for(Object[] obj:lists){
				SrvorgPsnVo srvorgPsnVo = new SrvorgPsnVo();
				srvorgPsnVo.setRid(obj[0] == null ? null : ((BigDecimal) obj[0]).intValue());
				srvorgPsnVo.setEmpName(obj[1] == null ? null : obj[1].toString());
				srvorgPsnVo.setSex(obj[2] == null ? null : obj[2].toString());
				srvorgPsnVo.setCodeName(obj[3] == null ? null : obj[3].toString());
				data.add(srvorgPsnVo);
			}
		}
		return data;
	}
/**
 *  <p>方法描述：行选中 监听事件</p>
 * @MethodAuthor hsj 2022-08-26 9:52
 */
	public void rowSelectListener(SelectEvent event){
		SrvorgPsnVo dto = (SrvorgPsnVo) event.getObject();
		if(!allSelectedDatas.contains(dto)){
			allSelectedDatas.add(dto);
		}
	}
/**
 *  <p>方法描述：行取消 监听事件</p>
 * @MethodAuthor hsj 2022-08-26 9:53
 */
	public void rowUnselectListener(UnselectEvent event){
		SrvorgPsnVo dto = (SrvorgPsnVo) event.getObject();
		allSelectedDatas.remove(dto);
	}
	/**
	 *  <p>方法描述：全选中/取消  监听事件</p>
	 * @MethodAuthor hsj 2022-08-26 9:54
	 */
	public void toggleSelectListener(ToggleSelectEvent event){
		DataTable dataTable = (DataTable) FacesContext
				.getCurrentInstance().getViewRoot()
				.findComponent(EXTRACT_TABLE_ID);
		List<SrvorgPsnVo> displayList = this.extractDataModel.getPackData();
		if(CollectionUtils.isEmpty(displayList)){
			//表格无数据
			return;
		}
		if(event.isSelected()){
			//当前datatable选中的项
			List<SrvorgPsnVo> list = (List<SrvorgPsnVo>)dataTable.getSelection();
			if(!CollectionUtils.isEmpty(list)){
				for(SrvorgPsnVo o: list){
					if(!allSelectedDatas.contains(o)){
						allSelectedDatas.add(o);
					}
				}
			}

		}else{//取消全选，需要移除当前页的所有元素
			allSelectedDatas.removeAll(displayList);
		}
	}


    public void submitAction() {
    	if (null==allSelectedDatas || allSelectedDatas.size()==0) {
    		JsfUtil.addErrorMessage("请选择数据！");
			return;
    	}
		Map<String, List<SrvorgPsnVo>> map = new HashMap<String, List<SrvorgPsnVo>>();
		map.put("selectPros", allSelectedDatas);
		RequestContext.getCurrentInstance().closeDialog(map);
	}
    /**
 	 * <p>方法描述：关闭</p>
 	 * @MethodAuthor qrr,2018年4月9日,dialogClose
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

	public String getSearchName() {
		return searchName;
	}
	public void setSearchName(String searchName) {
		this.searchName = searchName;
	}


	public Set<String> getSelectIds() {
		return selectIds;
	}

	public void setSelectIds(Set<String> selectIds) {
		this.selectIds = selectIds;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}


	public List<SrvorgPsnVo> getSelectedDatas() {
		return selectedDatas;
	}

	public void setSelectedDatas(List<SrvorgPsnVo> selectedDatas) {
		this.selectedDatas = selectedDatas;
	}

	public List<SrvorgPsnVo> getAllSelectedDatas() {
		return allSelectedDatas;
	}

	public void setAllSelectedDatas(List<SrvorgPsnVo> allSelectedDatas) {
		this.allSelectedDatas = allSelectedDatas;
	}


	public PackLazyDataModel getExtractDataModel() {
		return extractDataModel;
	}

	public void setExtractDataModel(PackLazyDataModel extractDataModel) {
		this.extractDataModel = extractDataModel;
	}



}
