package com.chis.modules.heth.comm.web.dialog;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.service.ActiveMonitoringTaskServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.web.FacesBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.*;

/**
 *  <p>类描述：码表弹出框
 *  主动监测任务维护 模块专用</p>
 * @ClassAuthor hsj 2023-08-14 9:41
 */
@ManagedBean(name = "codeMulitySelectTaskListBean")
@ViewScoped
public class CodeMulitySelectTaskListBean extends FacesBean {

	private static final long serialVersionUID = -738445454536625063L;
	private final ActiveMonitoringTaskServiceImpl activeMonitoringTaskService =SpringContextHolder.getBean(ActiveMonitoringTaskServiceImpl.class);

	/** 名称 或 拼音码*/
	private String searchNamOrPy;
	/** 标题*/
	private String titleName;
	/**码表大类*/
	private String selectIds;
	private List<TsSimpleCode> firstList;
	private Map<String,String> firstMap;
	/**查询条件大类是否显示*/
	private Boolean ifShowFirstCode = false;
	/** 查询列集合*/
	private List<TsSimpleCode> displayList;
	/** 所有集合*/
	private List<TsSimpleCode> allList;
	/**为1时,提交时只选择最末级*/
	private String selectLast;
	/**为1时,选择时只选择最末级*/
	private String selLast;
	private Map<String, List<TsSimpleCode>> levelMap;
	/**所有码表map，key:层级编码，val:TsSimpleCode*/
	private Map<String, TsSimpleCode> allCodeMap;
	/**只可选择同一层级的选项，选择一个级别后，非此级别的灰掉不可选择*/
	private boolean selectSameLevel = false;
	/**查询条件的名称*/
	private String searchName;
	/**类型*/
	private String type;
	/**行业类别*/
	private String indusTypeId;
	private String postId;

	/**
	 *  <p>方法描述：初始化数据</p>
	 * @MethodAuthor hsj 2023-08-14 14:32
	 */
	public CodeMulitySelectTaskListBean() {
		this.titleName = JsfUtil.getRequest().getParameter("titleName");
		this.selectIds = JsfUtil.getRequest().getParameter("selectIds");//已选择的码表rid，以逗号隔开
		this.selectLast = JsfUtil.getRequest().getParameter("selectLast");
		this.selLast = JsfUtil.getRequest().getParameter("selLast");
		this.searchName=JsfUtil.getRequest().getParameter("searchName");
		this.type=JsfUtil.getRequest().getParameter("type");
		this.indusTypeId=JsfUtil.getRequest().getParameter("indusTypeId");
		this.postId=JsfUtil.getRequest().getParameter("postId");
		String ifShowFirstCode = JsfUtil.getRequest().getParameter("ifShowFirstCode");
		if (StringUtils.isNotBlank(ifShowFirstCode)) {
			if ("true".equals(ifShowFirstCode)) {
				this.ifShowFirstCode = true;
			}else if ("false".equals(ifShowFirstCode)) {
				this.ifShowFirstCode = false;
			}
		}
		this.init();
	}
	/**
	 *  <p>方法描述：查询码表数据</p>
	 * @MethodAuthor hsj 14:24
	 */
	private void init()   {
		this.displayList = new ArrayList<>();
		this.allList = new ArrayList<>();
		this.firstList = new ArrayList<>();
		this.firstMap = new HashMap<>();
		List<Object[]> list ;
		try {
			list = activeMonitoringTaskService.findSimpleByParamTask(this.selectIds,this.type,this.indusTypeId,this.postId);
		}catch (Exception e){
			e.printStackTrace();
			list = new ArrayList<>();
		}
		if(CollectionUtils.isEmpty(list)){
			return;
		}
		int row = -1;// 第一次出现勾选的当前页的第一行数
		TsSimpleCode selectLevelCode = null;
		int  i = 0;
		//已选中的码表id
		List<String> resList = new ArrayList();
		if(StringUtils.isNotBlank(this.selectIds)){
			if(this.selectIds.contains(",")){
				String[] split = this.selectIds.split(",");
				for(String str:split){
					resList.add(str);
				}
			}else {
				resList.add(this.selectIds);
			}
		}

		for (Object[] obj : list) {
			if (null==obj[0]) {
				continue;
			}
			TsSimpleCode code = new TsSimpleCode();
			code.setRid(null!=obj[0]?Integer.valueOf(obj[0].toString()):null);
			code.setCodeName(null!=obj[1]?obj[1].toString():null);
			code.setCodeLevelNo(null!=obj[3]?obj[3].toString():null);
			code.setCodeNo(null!=obj[2]?obj[2].toString():null);
			code.setSplsht(null!=obj[4]?obj[4].toString():null);
			code.setExtendS1(null!=obj[5]?obj[5].toString():null);
			code.setExtendS2(null!=obj[6]?Integer.valueOf(obj[6].toString()):null);
			code.setExtendS3(StringUtils.objectToString(obj[7]));
			code.setExtendS4(StringUtils.objectToString(obj[8]));
			code.setExtendS5(StringUtils.objectToString(obj[9]));
			code.setLevelIndex(StringUtils.countMatches(code.getCodeLevelNo(), ".")+ "");
			code.setCodePath(StringUtils.objectToString(obj[10]));
			if (StringUtils.isNotBlank(this.selectIds) && resList.contains(code.getRid().toString())) {
				code.setIfSelected(true);
				if (row == -1) {
					row = i - i % 10;
				}
				//已选择的级别
				if (null==selectLevelCode) {
					selectLevelCode = code;
				}
			}
			if (StringUtils.containsNone(code.getCodeLevelNo(), ".")) {
				firstList.add(code);
				firstMap.put(code.getRid().toString(),code.getCodeNo());
			}
			allList.add(code);
			i++;
		}
		//需要显示的码表list
		if( null != allList && allList.size() > 0)    {
			this.displayList.addAll(allList);
			//初始化选择当前页的第一行数
			if (row>-1) {
				DataTable dataTable = (DataTable) FacesContext
						.getCurrentInstance().getViewRoot()
						.findComponent("codeForm:selectedIndusTable");
				dataTable.setFirst(row);
			}
			if (selectSameLevel && null!=selectLevelCode) {
				dealIfDisabled(selectLevelCode);
			}
		}
		dealLevelMap(allList);
		if ("1".equals(selectLast)) {//子级全部勾选，父级勾选
			initSelectParent();
		}
		//只有最末级可选择
		if ("1".equals(selLast)) {
			lastSelectParent();
		}
	}

	/**
	 *  <p>方法描述：只有最后以及可选择</p>
	 * @MethodAuthor hsj
	 */
	private void lastSelectParent() {
		if(CollectionUtils.isEmpty(displayList)){
			return;
		}
		for(TsSimpleCode simpleCode :displayList){
			List<TsSimpleCode> childs = this.levelMap.get(simpleCode.getCodeLevelNo());
			if (CollectionUtils.isEmpty(childs)) {
				simpleCode.setIfDisabled(false);
			}else{
				simpleCode.setIfDisabled(true);
			}
		}
	}

	/**
	 * <p>方法描述：已选择的子级全部勾选，则父级勾选</p>
	 * @MethodAuthor qrr,2021年3月24日,initSelectParent
	 * */
	private void initSelectParent(){
		List<TsSimpleCode> parents = new ArrayList<>();
		for (Map.Entry<String, List<TsSimpleCode>> entry : this.levelMap.entrySet()) {
			String key = entry.getKey();
			List<TsSimpleCode> childs = entry.getValue();
			if (CollectionUtils.isEmpty(childs)) {
				continue;
			}
			TsSimpleCode parent = this.allCodeMap.get(key);
			parents.add(parent);
		}
		if(CollectionUtils.isEmpty(parents)){
			return;
		}
		//按照层级排序，层级高的在前面，若有3级，当只能选择最后一级时，最后一级全部勾选，第一、二级也勾选
		Collections.sort(parents, new Comparator<TsSimpleCode>() {
			@Override
			public int compare(TsSimpleCode o1, TsSimpleCode o2) {
				String codeLevelNo1 = o1.getCodeLevelNo();
				String codeLevelNo2 = o2.getCodeLevelNo();
				if (StringUtils.isNotBlank(codeLevelNo1)
						&& StringUtils.isNotBlank(codeLevelNo2)) {
					String[] split1 = codeLevelNo1.split("\\.");
					String[] split2 = codeLevelNo2.split("\\.");
					return split2.length - split1.length;
				}
				return 0;
			}
		});
		for (TsSimpleCode t : parents) {
			String codeLevelNo = t.getCodeLevelNo();
			if (StringUtils.isBlank(codeLevelNo)) {
				continue;
			}
			List<TsSimpleCode> childs = this.levelMap.get(codeLevelNo);
			selectParent(codeLevelNo, childs);
		}
	}

	/**
	 *  <p>方法描述：子级全部勾选，父级勾选</p>
	 * @MethodAuthor hsj 2023-08-14 14:27
	 */
	private void selectParent(String parentCode,List<TsSimpleCode> childs) {
		if(CollectionUtils.isEmpty(childs)){
			return;
		}
		boolean ifAllChildSelect = true;
		for (TsSimpleCode t : childs) {
			if (!t.isIfSelected()) {
				ifAllChildSelect = false;
				break;
			}
		}
		if (ifAllChildSelect) {//子级全部勾选，父级勾选
			TsSimpleCode parent = this.allCodeMap.get(parentCode);
			parent.setIfSelected(true);
		}
	}

	/**
	 *  <p>方法描述：处理码表层级关系</p>
	 * @MethodAuthor hsj 2023-08-14 14:27
	 */
	private void dealLevelMap(List<TsSimpleCode> list){
		if(CollectionUtils.isEmpty(list)){
			return;
		}
		this.levelMap = new HashMap<>();
		this.allCodeMap = new HashMap<>();
		for (TsSimpleCode obj : list) {
			String codeLevelNo = obj.getCodeLevelNo();
			if (StringUtils.isBlank(codeLevelNo)) {
				continue;
			}
			this.allCodeMap.put(codeLevelNo, obj);
			this.levelMap.put(codeLevelNo, new ArrayList<TsSimpleCode>());
			if(!StringUtils.contains(codeLevelNo, ".")){
				continue;
			}
			String[] split = codeLevelNo.split("\\.");
			StringBuffer parentCodeSb = new StringBuffer();
			for (int i = 0; i < split.length-1; i++) {//仅找出父级
				parentCodeSb.append(".").append(split[i]);
				String parentCode = parentCodeSb.substring(1);
				List<TsSimpleCode> childs = this.levelMap.get(parentCode);
				if (null==childs) {
					childs = new ArrayList<>();
					this.levelMap.put(parentCode, childs);
				}
				childs.add(obj);
			}
		}
	}
	/**
	 * <p>方法描述：勾选大类，则小类默认全部选择</p>
	 * @MethodAuthor qrr,2019年12月2日,selectAction
	 * */
	public void selectAction(TsSimpleCode code){
		if("1".equals(selLast)){
			return;
		}
		if (!selectSameLevel) {
			dealIfSelected(code);
		}else {
			dealIfDisabled(code);
		}
	}
	/**
	 * <p>方法描述：处理是否选中</p>
	 * @MethodAuthor qrr,2020年6月5日,dealIfSelected
	 * */
	private void dealIfSelected(TsSimpleCode code) {
		String codeLevelNo = code.getCodeLevelNo();
		if (StringUtils.isBlank(codeLevelNo)) {
			return;
		}
		if (code.isIfSelected()){//大类勾选，小类全部选择
			for (TsSimpleCode t : allList) {
				String levelNo = t.getCodeLevelNo();
				if (StringUtils.isBlank(levelNo)) {
					continue;
				}
				if (levelNo.startsWith(codeLevelNo + ".")) {//下级全部勾选
					t.setIfSelected(true);
				}
			}
			if (StringUtils.contains(codeLevelNo, ".")) {//子级勾选
				int endIndex = codeLevelNo.lastIndexOf(".");
				String parentCode = codeLevelNo.substring(0, endIndex);
				List<TsSimpleCode> childList = this.levelMap.get(parentCode);//上级下一级的所有子级
				selectParent(parentCode, childList);//子级全部勾选，父级勾选
			}
		}else {//大类不勾选，小类不选择
			for (TsSimpleCode t : allList) {
				String levelNo = t.getCodeLevelNo();
				if (StringUtils.isBlank(levelNo)) {
					continue;
				}
				if (codeLevelNo.startsWith(levelNo + ".")) {//上级不勾选
					t.setIfSelected(false);
				}
				if (levelNo.startsWith(codeLevelNo + ".")) {//下级不勾选
					t.setIfSelected(false);
				}
			}
		}
	}

	/**
	 *  <p>方法描述：处理是否允许选择</p>
	 * @MethodAuthor hsj 2023-08-14 14:34
	 */
	private void dealIfDisabled(TsSimpleCode code) {
		if (code.isIfSelected()){
			if (StringUtils.isBlank(code.getCodeLevelNo())) {
				return;
			}
			String[] selectLevel = code.getCodeLevelNo().split("\\.");
			//非同级disabled
			for (TsSimpleCode t : allList) {
				if (StringUtils.isBlank(t.getCodeLevelNo())) {
					continue;
				}
				String[] level = t.getCodeLevelNo().split("\\.");
				if (selectLevel.length!=level.length) {
					t.setIfDisabled(true);
				}else {
					t.setIfDisabled(false);
				}
			}
		}else {
			//所有码表都未选中，所有码表允许选择
			for (TsSimpleCode t : allList) {
				if (t.isIfSelected()) {
					return;
				}
			}
			for (TsSimpleCode t : allList) {
				t.setIfDisabled(false);
			}
		}
	}

	/**
	 *  <p>方法描述：根据名称、拼音码过滤数据</p>
	 * @MethodAuthor hsj 2023-08-14 14:29
	 */
	public void searchAction() {
		DataTable dataTable = (DataTable) FacesContext
				.getCurrentInstance().getViewRoot()
				.findComponent("codeForm:selectedIndusTable");
		dataTable.setFirst(0);
		if(CollectionUtils.isEmpty(allList)){
			return;
		}
		this.displayList = new ArrayList<>();
		if(StringUtils.isNotBlank(searchNamOrPy)){
			for(TsSimpleCode t :allList)   {
				String codeName = t.getCodeName()==null?"":t.getCodeName().toString();
				String codePym = t.getSplsht()==null?"":t.getSplsht().toString();
				if (codeName.indexOf(searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(searchNamOrPy.toUpperCase()) != -1) {
					this.displayList.add(t);
				}
			}
		}else{
			this.displayList.addAll(allList);
		}
	}

	/**
	 *  <p>方法描述：提交</p>
	 * @MethodAuthor hsj 2023-08-14 14:30
	 */
	public void submitAction() {
		if(CollectionUtils.isEmpty(allList)){
			JsfUtil.addErrorMessage("请选择数据！");
			return;
		}
		List<TsSimpleCode> results = new ArrayList<>();
		for (TsSimpleCode t : allList) {
			if(!t.isIfSelected()){
				continue;
			}
			if ("1".equals(selectLast)) {
				List<TsSimpleCode> childs = this.levelMap.get(t.getCodeLevelNo());
				if (null==childs||childs.size()==0) {//最末级，无子级
					results.add(t);
				}
			}else {
				results.add(t);
			}
		}
		if (null==results ||results.size()==0) {
			JsfUtil.addErrorMessage("请选择数据！");
			return;
		}
		Map<String, List<TsSimpleCode>> map = new HashMap<>();
		map.put("selectPros", results);
		RequestContext.getCurrentInstance().closeDialog(map);
	}
	/**
	 * <p>方法描述：关闭</p>
	 * @MethodAuthor qrr,2018年4月9日,dialogClose
	 */
	public void dialogClose() {
		RequestContext.getCurrentInstance().closeDialog(null);
	}


	public String getSearchNamOrPy() {
		return searchNamOrPy;
	}

	public void setSearchNamOrPy(String searchNamOrPy) {
		this.searchNamOrPy = searchNamOrPy;
	}

	public String getTitleName() {
		return titleName;
	}

	public void setTitleName(String titleName) {
		this.titleName = titleName;
	}

	public List<TsSimpleCode> getDisplayList() {
		return displayList;
	}

	public void setDisplayList(List<TsSimpleCode> displayList) {
		this.displayList = displayList;
	}


	public List<TsSimpleCode> getFirstList() {
		return firstList;
	}

	public void setFirstList(List<TsSimpleCode> firstList) {
		this.firstList = firstList;
	}

	public Boolean getIfShowFirstCode() {
		return ifShowFirstCode;
	}

	public void setIfShowFirstCode(Boolean ifShowFirstCode) {
		this.ifShowFirstCode = ifShowFirstCode;
	}
	public String getSelectLast() {
		return selectLast;
	}
	public void setSelectLast(String selectLast) {
		this.selectLast = selectLast;
	}
	public boolean isSelectSameLevel() {
		return selectSameLevel;
	}
	public void setSelectSameLevel(boolean selectSameLevel) {
		this.selectSameLevel = selectSameLevel;
	}

	public String getSelLast() {
		return selLast;
	}

	public void setSelLast(String selLast) {
		this.selLast = selLast;
	}


	public List<TsSimpleCode> getAllList() {
		return allList;
	}

	public void setAllList(List<TsSimpleCode> allList) {
		this.allList = allList;
	}


	public String getSelectIds() {
		return selectIds;
	}

	public void setSelectIds(String selectIds) {
		this.selectIds = selectIds;
	}

	public String getSearchName() {
		return searchName;
	}

	public void setSearchName(String searchName) {
		this.searchName = searchName;
	}



}
