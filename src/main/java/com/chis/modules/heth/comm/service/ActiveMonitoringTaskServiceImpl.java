package com.chis.modules.heth.comm.service;

import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbTjJcTask;
import com.chis.modules.heth.comm.entity.TbTjJcTaskBadrsn;
import com.chis.modules.heth.comm.entity.TbTjJcTaskPsn;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 主动监测任务用Service
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(readOnly = false)
public class ActiveMonitoringTaskServiceImpl extends AbstractTemplate {

    /**
     * 根据RID获取主动监测任务实体
     *
     * @param rid 主动监测任务RID
     * @return 主动监测任务实体
     */
    public TbTjJcTask findTbTjJcTaskByRid(Integer rid) {
        TbTjJcTask jcTask;
        try {
            if (rid == null) {
                jcTask = new TbTjJcTask();
            } else {
                jcTask = super.find(TbTjJcTask.class, rid);
                jcTask.getJcTaskPsnList().size();
            }
        } catch (Exception e) {
            e.printStackTrace();
            jcTask = new TbTjJcTask();
        }
        Boolean ifBhkCode = Boolean.FALSE;
        Boolean ifWhBhkCode = Boolean.TRUE;
        for (TbTjJcTaskPsn jcTaskPsn : jcTask.getJcTaskPsnList()) {
            if(StringUtils.isBlank(jcTaskPsn.getBhkCode())){
                ifBhkCode = Boolean.TRUE;
            }
            if(StringUtils.isNotBlank(jcTaskPsn.getBhkCode())){
                ifWhBhkCode = Boolean.FALSE;
            }
            //在岗状态
            if (jcTaskPsn.getFkByOnguardStateid() != null && jcTaskPsn.getFkByOnguardStateid().getRid() != null) {
                jcTaskPsn.setOnguadrStateId(jcTaskPsn.getFkByOnguardStateid().getRid());
            }
            //监测岗位
            if (jcTaskPsn.getFkByPostId() != null && jcTaskPsn.getFkByPostId().getRid() != null) {
                jcTaskPsn.setPostId(jcTaskPsn.getFkByPostId().getRid());
                jcTaskPsn.setPostName(jcTaskPsn.getFkByPostId().getCodeName());
                jcTaskPsn.setNeedWorkOther("1".equals(jcTaskPsn.getFkByPostId().getExtendS1()));
            }
            //职业病主动监测因素
            jcTaskPsn.getJcTaskBadrsnList().size();
            List<String> jcTaskBadrsnRidList = new ArrayList<>();
            List<String> jcTaskBadrsnNameList = new ArrayList<>();
            for (TbTjJcTaskBadrsn jcTaskBadrsn : jcTaskPsn.getJcTaskBadrsnList()) {
                if (jcTaskBadrsn.getFkByBadrsnId() == null || jcTaskBadrsn.getFkByBadrsnId().getRid() == null) {
                    continue;
                }
                jcTaskBadrsnRidList.add(StringUtils.objectToString(jcTaskBadrsn.getFkByBadrsnId().getRid()));
                jcTaskBadrsnNameList.add(StringUtils.objectToString(jcTaskBadrsn.getFkByBadrsnId().getCodeName()));
            }
            jcTaskPsn.setRsnId(StringUtils.list2string(jcTaskBadrsnRidList, ","));
            jcTaskPsn.setRsnName(StringUtils.list2string(jcTaskBadrsnNameList, "，"));
            //防护用品佩戴情况
            if (jcTaskPsn.getFkByProtectEquId() != null && jcTaskPsn.getFkByProtectEquId().getRid() != null) {
                jcTaskPsn.setProtectEquId(jcTaskPsn.getFkByProtectEquId().getRid());
            }
        }
        jcTask.setIfBhkCode(ifBhkCode);
        jcTask.setIfWhBhkCode(ifWhBhkCode);
        return jcTask;
    }

    /**
     * 更新主动监测任务实体
     *
     * @param jcTask 主动监测任务实体
     */
    public void upsertTbTjJcTask(TbTjJcTask jcTask) {
        if (!CollectionUtils.isEmpty(jcTask.getJcTaskPsnList())) {
            for (TbTjJcTaskPsn jcTaskPsn : jcTask.getJcTaskPsnList()) {
                if (jcTaskPsn.getOnguadrStateId() == null) {
                    jcTaskPsn.setFkByOnguardStateid(null);
                } else {
                    jcTaskPsn.setFkByOnguardStateid(new TsSimpleCode(jcTaskPsn.getOnguadrStateId()));
                }
                if (jcTaskPsn.getPostId() == null) {
                    jcTaskPsn.setFkByPostId(null);
                } else {
                    jcTaskPsn.setFkByPostId(new TsSimpleCode(jcTaskPsn.getPostId()));
                }
                if (null == jcTaskPsn.getNeedWorkOther() || !jcTaskPsn.getNeedWorkOther()) {
                    jcTaskPsn.setWorkOther("");
                }
                if (!CollectionUtils.isEmpty(jcTaskPsn.getJcTaskBadrsnList())) {
                    jcTaskPsn.getJcTaskBadrsnList().clear();
                } else {
                    jcTaskPsn.setJcTaskBadrsnList(new ArrayList<TbTjJcTaskBadrsn>());
                }
                if (StringUtils.isBlank(jcTaskPsn.getRsnId())) {
                    continue;
                }
                List<String> rsnIdList = StringUtils.string2list(jcTaskPsn.getRsnId(), ",");
                for (String rsnIdStr : rsnIdList) {
                    Integer rsnId = ObjectUtil.convert(Integer.class, rsnIdStr);
                    if (rsnId == null) {
                        continue;
                    }
                    TbTjJcTaskBadrsn jcTaskBadrsn = new TbTjJcTaskBadrsn();
                    jcTaskBadrsn.setFkByMainId(jcTaskPsn);
                    jcTaskBadrsn.setFkByBadrsnId(new TsSimpleCode(rsnId));
                    super.preEntity(jcTaskBadrsn);
                    jcTaskPsn.getJcTaskBadrsnList().add(jcTaskBadrsn);
                }
            }
        }
        super.upsertEntity(jcTask);
    }

    /**
     * 更新主动监测任务字段
     *
     * @param rid   主动监测任务RID
     * @param param 字段名称
     * @param value 字段值
     */
    public void updateTbTjJcTaskParamByRid(Integer rid, String param, Object value) {
        if (rid == null) {
            return;
        }
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "UPDATE TB_TJ_JC_TASK SET " + param + " = :" + param + ", MODIFY_DATE = :nowDate, MODIFY_MANID = :manId WHERE RID = :rid ";
        paramMap.put("rid", rid);
        paramMap.put(param, value);
        paramMap.put("nowDate", new Date());
        paramMap.put("manId", Global.getUser().getRid());
        super.executeSql(sql, paramMap);
    }

    /**
     *  <p>方法描述：弹出框</p>
     * @MethodAuthor hsj 2023-08-14 10:34
     */
    @Transactional(readOnly = true)
    public List<Object[]> findSimpleByParamTask( String selectIds, String type,String indusTypeId,String postId) {
        Map<String, Object> paramMap = new HashMap<>();
        String sql = genSql(type,selectIds,indusTypeId,postId,paramMap);
        List<Object[]> sqlResultList = this.findDataBySqlNoPage(sql,paramMap);
        return sqlResultList;
    }
    private String genSql(String type, String excludeId,String indusTypeId,String postId,Map<String, Object> paramMap) {
        StringBuffer sql = new StringBuffer();
        if ("1".equals(type)) {
            sql.append(" SELECT SC.RID,SC.CODE_NAME,SC.CODE_NO,'' AS CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2,");
            sql.append(" SC.EXTENDS3,SC.EXTENDS4,SC.EXTENDS5,SC.CODE_PATH ");
            sql.append(" FROM TD_ZDZYB_ANALY_DETAIL_SUB DS ");
            sql.append(" LEFT JOIN TS_SIMPLE_CODE SC ON DS.ANALY_ITEM_ID = SC.RID ");
            sql.append(" LEFT JOIN TD_ZDZYB_ANALY_DETAIL D ON DS.MAIN_ID = D.RID ");
            sql.append(" LEFT JOIN TD_ZDZYB_ANALY_TYPE T ON D.MAIN_ID = T.RID ");
            sql.append(" WHERE T.BUS_TYPE = 4 AND T.ANALY_TYPE = 3 ");
            sql.append(" GROUP BY SC.RID,SC.CODE_NAME,SC.CODE_NO,SC.CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2, ");
            sql.append(" SC.EXTENDS3,SC.EXTENDS4,SC.EXTENDS5,SC.CODE_PATH,SC.NUM ");
            sql.append(" ORDER BY SC.NUM,SC.CODE_LEVEL_NO,SC.CODE_NO ");

        } else if ("2".equals(type)) {
            if(ObjectUtil.isNull(indusTypeId)){
                return  "";
            }
            if(StringUtils.isNotBlank(excludeId)){
                List<String> list = StringUtils.string2list(excludeId,",");
                List<Integer> selectList = new ArrayList<>();
                for (String s : list) {
                    selectList.add(Integer.parseInt(s));
                }
                paramMap.put("excludeId",excludeId);
            }
            if(StringUtils.isNotBlank(indusTypeId)){
                paramMap.put("indusTypeId",indusTypeId);
            }
            sql.append(" SELECT SC.RID,SC.CODE_NAME,SC.CODE_NO,'' AS CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2,");
            sql.append(" SC.EXTENDS3,SC.EXTENDS4,SC.CODE_DESC");
            sql.append(" FROM TD_ZDZYB_ANALY_DETAIL_SUB DS");
            sql.append(" LEFT JOIN TS_SIMPLE_CODE SC1 ON DS.ANALY_ITEM_ID = SC1.RID");
            sql.append(" LEFT JOIN TD_ZDZYB_ANALY_DETAIL D ON DS.MAIN_ID = D.RID");
            sql.append(" LEFT JOIN TD_ZDZYB_ANALY_TYPE T ON D.MAIN_ID = T.RID ");
            sql.append(" LEFT JOIN TD_ZDZYB_DETAIL_SUB_REL DSR ON DS.RID = DSR.MAIN_ID ");
            sql.append("  LEFT JOIN TS_SIMPLE_CODE SC ON DSR.ANALY_ITEM_ID = SC.RID ");
            sql.append("  WHERE T.BUS_TYPE = 4 AND T.ANALY_TYPE = 3 AND DSR.TYPE = 1  ");
            sql.append("  AND SC1.RID =:indusTypeId");
            if(StringUtils.isNotBlank(excludeId)){
                sql.append(" AND SC.RID NOT IN (:excludeId)");
            }
            sql.append("  GROUP BY SC.RID,SC.CODE_NAME,SC.CODE_NO,SC.CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2,");
            sql.append("  SC.EXTENDS3,SC.EXTENDS4,SC.CODE_DESC,SC.NUM");
            sql.append("  ORDER BY SC.NUM,SC.CODE_LEVEL_NO,SC.CODE_NO");
        } else if ("3".equals(type)) {
            if(StringUtils.isBlank(indusTypeId) || StringUtils.isBlank(postId)){
                return  "";
            }
            if(StringUtils.isNotBlank(excludeId)){
                List<String> list = StringUtils.string2list(excludeId,",");
                List<Integer> selectList = new ArrayList<>();
                for (String s : list) {
                    selectList.add(Integer.parseInt(s));
                }
                paramMap.put("excludeId",excludeId);
            }
            if(StringUtils.isNotBlank(indusTypeId)){
                paramMap.put("indusTypeId",indusTypeId);
            }
            if(StringUtils.isNotBlank(postId)){
                paramMap.put("postId",postId);
            }
            sql.append(" SELECT SC.RID,SC.CODE_NAME,SC.CODE_NO,'' AS CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2,");
            sql.append(" SC.EXTENDS3,SC.EXTENDS4,SC.CODE_DESC");
            sql.append(" FROM TD_ZDZYB_ANALY_DETAIL_SUB DS ");
            sql.append(" LEFT JOIN TS_SIMPLE_CODE SC1 ON DS.ANALY_ITEM_ID = SC1.RID");
            sql.append(" LEFT JOIN TD_ZDZYB_ANALY_DETAIL D ON DS.MAIN_ID = D.RID");
            sql.append(" LEFT JOIN TD_ZDZYB_ANALY_TYPE T ON D.MAIN_ID = T.RID");
            sql.append(" LEFT JOIN TD_ZDZYB_DETAIL_SUB_REL DSR ON DS.RID = DSR.MAIN_ID ");
            sql.append(" LEFT JOIN TS_SIMPLE_CODE SC ON DSR.ANALY_ITEM_ID = SC.RID");
            sql.append(" WHERE T.BUS_TYPE = 4 AND T.ANALY_TYPE = 3 AND DSR.TYPE = 2 AND SC1.RID =:indusTypeId");
            sql.append(" AND DS.RID in (SELECT sub.MAIN_ID FROM  TD_ZDZYB_DETAIL_SUB_REL sub WHERE sub.ANALY_ITEM_ID =:postId) ");
            if(StringUtils.isNotBlank(excludeId)){
                sql.append(" AND SC.RID NOT IN (:excludeId)");
            }
            sql.append(" GROUP BY SC.RID,SC.CODE_NAME,SC.CODE_NO,SC.CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2,");
            sql.append(" SC.EXTENDS3,SC.EXTENDS4,SC.CODE_DESC,SC.NUM");
            sql.append(" ORDER BY SC.NUM,SC.CODE_LEVEL_NO,SC.CODE_NO");
        } else if ("4".equals(type)) {
            if(StringUtils.isBlank(indusTypeId) || StringUtils.isBlank(postId)){
                return  "";
            }
            if(StringUtils.isNotBlank(indusTypeId)){
                paramMap.put("indusTypeId",indusTypeId);
            }
            if(StringUtils.isNotBlank(postId)){
                paramMap.put("postId",postId);
            }
            sql.append(" SELECT SC.RID,SC.CODE_NAME,SC.CODE_NO,'' AS CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2,");
            sql.append(" SC.EXTENDS3,SC.EXTENDS4,SC.EXTENDS5,SC.CODE_PATH ");
            sql.append(" FROM TD_ZDZYB_ANALY_DETAIL_SUB DS");
            sql.append(" LEFT JOIN TS_SIMPLE_CODE SC1 ON DS.ANALY_ITEM_ID = SC1.RID");
            sql.append(" LEFT JOIN TD_ZDZYB_ANALY_DETAIL D ON DS.MAIN_ID = D.RID");
            sql.append(" LEFT JOIN TD_ZDZYB_ANALY_TYPE T ON D.MAIN_ID = T.RID");
            sql.append(" LEFT JOIN TD_ZDZYB_DETAIL_SUB_REL DSR ON DS.RID = DSR.MAIN_ID");
            sql.append(" LEFT JOIN TS_SIMPLE_CODE SC ON DSR.ANALY_ITEM_ID = SC.RID");
            sql.append(" WHERE T.BUS_TYPE = 4 AND T.ANALY_TYPE = 3 AND DSR.TYPE = 2 AND SC1.RID =:indusTypeId ");
            sql.append(" AND DS.RID in (SELECT sub.MAIN_ID FROM  TD_ZDZYB_DETAIL_SUB_REL sub WHERE sub.ANALY_ITEM_ID =:postId) ");
            sql.append(" GROUP BY SC.RID,SC.CODE_NAME,SC.CODE_NO,SC.CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2,");
            sql.append(" SC.EXTENDS3,SC.EXTENDS4,SC.EXTENDS5,SC.CODE_PATH,SC.NUM ");
            sql.append(" ORDER BY SC.NUM,SC.CODE_LEVEL_NO,SC.CODE_NO");
        }
        return sql.toString();
    }

    /**
     *  <p>方法描述：是否存在不为空的</p>
     * @MethodAuthor hsj 2023-09-27 10:22
     */
    public List<String> getIfBhkCodeByRidList(List<String> ridList) {
        List<String> list = new ArrayList<>();
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramMap = new HashMap<>();
        sql.append(" SELECT T.MAIN_ID ,COUNT(T.MAIN_ID) FROM  TB_TJ_JC_TASK_PSN T");
        sql.append(" WHERE T.BHK_CODE IS NOT NULL");
        sql.append(" AND T.MAIN_ID in (:rids)");
        sql.append(" GROUP BY T.MAIN_ID");
        paramMap.put("rids",ridList);
        List<Object[]> sqlResultList = this.findDataBySqlNoPage(sql.toString(),paramMap);
        if(CollectionUtils.isEmpty(sqlResultList)){
            return list;
        }
        for(Object[] obj: sqlResultList){
            list.add(ObjectUtil.toStr(obj[0]));
        }
        return list;
    }
}
