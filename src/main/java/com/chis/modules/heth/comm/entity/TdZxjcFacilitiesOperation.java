package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-4
 */
@Entity
@Table(name = "TD_ZXJC_FACILITIES_OPERATION")
@SequenceGenerator(name = "TdZxjcFacilitiesOperation", sequenceName = "TD_ZXJC_FACILITIES_OP_SEQ", allocationSize = 1)
public class TdZxjcFacilitiesOperation implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZxjcUnitbasicinfo fkByMainId;
	private Integer fcssSituation;
	private Integer fcssEffect;
	private Integer fdssSituation;
	private Integer fdssEffect;
	private Integer fzsssSituation;
	private Integer fzsssEffect;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZxjcFacilitiesOperation() {
	}

	public TdZxjcFacilitiesOperation(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZxjcFacilitiesOperation")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZxjcUnitbasicinfo getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZxjcUnitbasicinfo fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "FCSS_SITUATION")	
	public Integer getFcssSituation() {
		return fcssSituation;
	}

	public void setFcssSituation(Integer fcssSituation) {
		this.fcssSituation = fcssSituation;
	}	
			
	@Column(name = "FCSS_EFFECT")	
	public Integer getFcssEffect() {
		return fcssEffect;
	}

	public void setFcssEffect(Integer fcssEffect) {
		this.fcssEffect = fcssEffect;
	}	
			
	@Column(name = "FDSS_SITUATION")	
	public Integer getFdssSituation() {
		return fdssSituation;
	}

	public void setFdssSituation(Integer fdssSituation) {
		this.fdssSituation = fdssSituation;
	}	
			
	@Column(name = "FDSS_EFFECT")	
	public Integer getFdssEffect() {
		return fdssEffect;
	}

	public void setFdssEffect(Integer fdssEffect) {
		this.fdssEffect = fdssEffect;
	}	
			
	@Column(name = "FZSSS_SITUATION")	
	public Integer getFzsssSituation() {
		return fzsssSituation;
	}

	public void setFzsssSituation(Integer fzsssSituation) {
		this.fzsssSituation = fzsssSituation;
	}	
			
	@Column(name = "FZSSS_EFFECT")	
	public Integer getFzsssEffect() {
		return fzsssEffect;
	}

	public void setFzsssEffect(Integer fzsssEffect) {
		this.fzsssEffect = fzsssEffect;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}