package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.logic.ShowColumnPO;
import com.chis.modules.heth.comm.logic.ShowRowPO;
import com.chis.modules.heth.comm.rptvo.ResultProVo;
import com.chis.modules.heth.comm.service.TdZxjcUnitBasicInfoService;
import com.chis.modules.heth.comm.service.TdZyUnitbasicinfoService;
import com.chis.modules.system.service.CommServiceImpl;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * <p>类描述：企业申报信息</p>
 *
 * @ClassAuthor qrr, 2020年12月9日, TdZyUnitBasicInfoViewBean
 */
@ManagedBean(name = "tdZxjcUnitBasicInfoViewBean")
@ViewScoped
public class TdZxjcUnitBasicInfoViewBean {
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private TdZxjcUnitBasicInfoService basicinfoService = SpringContextHolder.getBean(TdZxjcUnitBasicInfoService.class);

    /**
     * 在线监测-单位基本信息
     */
    private TdZxjcUnitbasicinfo jcUnitBasicInfo;
    /**
     * 职业病危害因素种类及接触人数
     */
    private Object[] unitFactorcrowd;
    /**
     * 接触因素及人数明细 key:类型  value:结果集
     */
    private Map<String, List<List<Object[]>>> unitFactorItemMap = new HashMap<>();
    /**
     * 职业病危害因素检测情况
     */
    private Object[] unitharmChk;
    /**
     * 危害因素检测情况-危害因素明细
     */
    List<Object[]> unitharmcheckSub = new ArrayList<>();
    /**
     * 职业健康检查情况
     */
    private Object[] unitHethCus;
    /**
     * 危害因素检测情况-危害因素明细
     */
    List<Object[]> unitHealthItem = new ArrayList<>();
    /**
     * 在线监测-职业病防护设施设置及运行情况
     */
    private Object[] facilitiesOperation;
    /**
     * 在线监测-职业病防护用品配备及发放情况
     */
    private Object[] equimentDis;
    /**
     * 检测地点下的检测项目
     */
    private List<ResultProVo> resultPro;
    /**
     * 职业病危害因素检测结果登记  表头含下标名称
     */
    private List<String> titleNames;

    public TdZxjcUnitBasicInfoViewBean() {
        String rid = JsfUtil.getRequestParameter("rid");
        if (StringUtils.isNotBlank(rid)) {
            this.searchAction(new Integer(rid));
        }
        titleNames = new ArrayList<>();
        titleNames.add("游离sio<sub>2</sub>含量");
        titleNames.add("C<sub>STE</sub>");
        titleNames.add("C<sub>TWA</sub>");
        titleNames.add("L<sub>EX,8h</sub>/L<sub>EX,40h</sub>");
    }

    /**
     * <p>方法描述：</p>
     *
     * @MethodAuthor qrr, 2020年12月9日, searchAction
     */
    private void searchAction(Integer rid) {
        //单位基本信息
        this.jcUnitBasicInfo = commService.find(TdZxjcUnitbasicinfo.class, rid);
        //处理当前工作阶段
        if(StringUtils.isNotBlank(jcUnitBasicInfo.getWorkNode())){
            List<String> workNodes = StringUtils.string2list(jcUnitBasicInfo.getWorkNode(), ",");
            if(!CollectionUtils.isEmpty(workNodes)){
                String nodes="";
                for (String workNode : workNodes) {
                    nodes+="，"+("1".equals(workNode)?"可研阶段" :"2".equals(workNode)?"初步设计":"3".equals(workNode)?"建设阶段":"4".equals(workNode)?"竣工阶段":"");
                }
                if(StringUtils.isNotBlank(nodes)){
                    jcUnitBasicInfo.setWorkNode(nodes.substring(1));
                }
            }

        }

        //职业病危害因素种类及接触人数
        List<Object[]> unitFactorcrowdList = basicinfoService.findUnitfactorcrowd(rid);
        if (!CollectionUtils.isEmpty(unitFactorcrowdList)) {
            unitFactorcrowd = unitFactorcrowdList.get(0);
        }
        //接触因素及人数明细
        Map<String, List<Object[]>> unitFactorItemTempMap = new HashMap<>();
        List<Object[]> unitFactorItem = basicinfoService.findUnitFactorItem(rid);

        if (!CollectionUtils.isEmpty(unitFactorItem)) {
            for (Object[] objects : unitFactorItem) {
                if (objects[7] != null) {
                    if (unitFactorItemTempMap.containsKey(objects[7].toString())) {
                        unitFactorItemTempMap.get(objects[7].toString()).add(objects);
                    } else {
                        List<Object[]> temp = new ArrayList<>();
                        temp.add(objects);
                        unitFactorItemTempMap.put(objects[7].toString(), temp);
                    }
                }
            }
        }
        if (!unitFactorItemTempMap.isEmpty()) {
            for (Map.Entry<String, List<Object[]>> integerListEntry : unitFactorItemTempMap.entrySet()) {
                if (!CollectionUtils.isEmpty(integerListEntry.getValue())) {
                    List<List<Object[]>> unitFactorItemList = new ArrayList<>();
                    int listSize = integerListEntry.getValue().size();
                    int toIndex = 3;
                    //3个一组
                    for (int i = 0; i < integerListEntry.getValue().size(); i += 3) {
                        if (i + 3 > listSize) {
                            toIndex = listSize - i;
                        }
                        List<Object[]> temp = integerListEntry.getValue().subList(i, i + toIndex);
                        unitFactorItemList.add(temp);
                    }
                    unitFactorItemMap.put(integerListEntry.getKey(), unitFactorItemList);
                }
            }
        }
        //职业病危害因素检测情况
        List<Object[]> unitharmChkList = basicinfoService.findUnitharmChk(rid);
        if (!CollectionUtils.isEmpty(unitharmChkList)) {
            unitharmChk = unitharmChkList.get(0);
        }
        //危害因素检测情况-危害因素明细
        unitharmcheckSub = basicinfoService.findUnitharmcheckSub(rid);

        //职业病危害因素检测情况
        List<Object[]> unitHethCusList = basicinfoService.findUnitHethCus(rid);
        if (!CollectionUtils.isEmpty(unitHethCusList)) {
            unitHethCus = unitHethCusList.get(0);
        }
        //在线监测-职业健康检查情况-危害因素明细
        unitHealthItem = basicinfoService.findUnitHealthItem(rid);

        //在线监测-职业病防护设施设置及运行情况
        List<Object[]> facilitiesOperationList = basicinfoService.findFacilitiesOperation(rid);
        if (!CollectionUtils.isEmpty(facilitiesOperationList)) {
            facilitiesOperation = facilitiesOperationList.get(0);
        }
        //在线监测-职业病防护用品配备及发放情况
        List<Object[]> equimentDisList = basicinfoService.findEquimentDis(rid);
        if (!CollectionUtils.isEmpty(equimentDisList)) {
            equimentDis = equimentDisList.get(0);
        }
        //检测地点下的检测项目
        List<Object[]> resultProList = basicinfoService.findResultPro(rid);
        Map<String,List<Object[]>> resultProMap=new LinkedHashMap<>();
        if(!CollectionUtils.isEmpty(resultProList)){
            for (Object[] objects : resultProList) {
                if(objects[1]!=null && objects[27]!=null){
                    if( resultProMap.containsKey(objects[1]+"&"+objects[27])){
                        resultProMap.get(objects[1]+"&"+objects[27]).add(objects);
                    }else{
                        List<Object[]> temp=new ArrayList<>();
                        temp.add(objects);
                        resultProMap.put(objects[1]+"&"+objects[27],temp);
                    }
                }
            }
        }
        if(!resultProMap.isEmpty()){
            resultPro=new ArrayList<>();
            for (Map.Entry<String, List<Object[]>> entry : resultProMap.entrySet()) {
                ResultProVo vo=new ResultProVo();
                vo.setFactorName(entry.getKey().split("&")[0]);
                vo.setFlag(Integer.parseInt(entry.getKey().split("&")[1]));
                vo.setResultPro(entry.getValue());
                resultPro.add(vo);
            }
        }
        System.out.println(resultPro);
    }


    public TdZxjcUnitbasicinfo getJcUnitBasicInfo() {
        return jcUnitBasicInfo;
    }

    public void setJcUnitBasicInfo(TdZxjcUnitbasicinfo jcUnitBasicInfo) {
        this.jcUnitBasicInfo = jcUnitBasicInfo;
    }

    public Object[] getUnitFactorcrowd() {
        return unitFactorcrowd;
    }

    public void setUnitFactorcrowd(Object[] unitFactorcrowd) {
        this.unitFactorcrowd = unitFactorcrowd;
    }

    public Map<String, List<List<Object[]>>> getUnitFactorItemMap() {
        return unitFactorItemMap;
    }

    public void setUnitFactorItemMap(Map<String, List<List<Object[]>>> unitFactorItemMap) {
        this.unitFactorItemMap = unitFactorItemMap;
    }

    public Object[] getUnitharmChk() {
        return unitharmChk;
    }

    public void setUnitharmChk(Object[] unitharmChk) {
        this.unitharmChk = unitharmChk;
    }

    public List<Object[]> getUnitharmcheckSub() {
        return unitharmcheckSub;
    }

    public void setUnitharmcheckSub(List<Object[]> unitharmcheckSub) {
        this.unitharmcheckSub = unitharmcheckSub;
    }

    public Object[] getUnitHethCus() {
        return unitHethCus;
    }

    public void setUnitHethCus(Object[] unitHethCus) {
        this.unitHethCus = unitHethCus;
    }

    public List<Object[]> getUnitHealthItem() {
        return unitHealthItem;
    }

    public void setUnitHealthItem(List<Object[]> unitHealthItem) {
        this.unitHealthItem = unitHealthItem;
    }

    public Object[] getFacilitiesOperation() {
        return facilitiesOperation;
    }

    public void setFacilitiesOperation(Object[] facilitiesOperation) {
        this.facilitiesOperation = facilitiesOperation;
    }

    public Object[] getEquimentDis() {
        return equimentDis;
    }

    public void setEquimentDis(Object[] equimentDis) {
        this.equimentDis = equimentDis;
    }

    public List<ResultProVo> getResultPro() {
        return resultPro;
    }

    public void setResultPro(List<ResultProVo> resultPro) {
        this.resultPro = resultPro;
    }

    public List<String> getTitleNames() {
        return titleNames;
    }

    public void setTitleNames(List<String> titleNames) {
        this.titleNames = titleNames;
    }
}
