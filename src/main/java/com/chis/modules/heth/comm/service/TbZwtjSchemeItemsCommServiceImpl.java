package com.chis.modules.heth.comm.service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbZwtjSchemeItems;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;

/**
 * @Description : 职卫体检方案标准项目（GBZ-188）-业务层
 * @ClassAuthor : anjing
 * @Date : 2019/5/22 14:45
 **/
@Service
@Transactional(readOnly = true)
public class TbZwtjSchemeItemsCommServiceImpl extends AbstractTemplate {

    /**
     * 获取所有项目组合与项目关系对应的项目组合
     * @return
     */
    public List<TbZwtjSchemeItems> selectSchemeItemsByCombIds3(String codeNos){
        List<TbZwtjSchemeItems> list = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        Set<Integer> cmbIdSet = new HashSet<>();
        sb.append(" SELECT T.ITEM_CMBID, T1.CODE_NAME, T1.SPLSHT");
        sb.append(" FROM TB_ZWTJ_ITEMCMB_ITEMS T  ");
        sb.append(" INNER JOIN TS_SIMPLE_CODE T1 ON T.ITEM_CMBID = T1.RID ");
        sb.append(" WHERE T1.IF_REVEAL = 1 ");
        sb.append(" AND T.STOP_TAG = 1 ");
        if(StringUtils.isNotBlank(codeNos)){
            sb.append(" AND T1.CODE_NO NOT IN (").append(StringUtils.symbolSpilt(codeNos)).append(") ");
        }
        sb.append(" GROUP BY T.ITEM_CMBID, T1.CODE_NAME, T1.SPLSHT, T1.NUM, T1.CODE_NO ");
        sb.append(" ORDER BY T1.NUM ASC, T1.CODE_NO ");
        List<Object[]> schemeItemList = em.createNativeQuery(sb.toString()).getResultList();
        for(Object[] obj : schemeItemList) {
            Integer rid = Integer.parseInt(obj[0].toString());
            if(cmbIdSet.contains(rid)){
                continue;
            }
            cmbIdSet.add(rid);
            TbZwtjSchemeItems schemeItems = new TbZwtjSchemeItems();
            TsSimpleCode tsSimpleCode = new TsSimpleCode();
            tsSimpleCode.setRid(rid);
            tsSimpleCode.setCodeName(StringUtils.objectToString(obj[1]));
            tsSimpleCode.setSplsht(StringUtils.objectToString(obj[2]));
            schemeItems.setTsSimpleCode(tsSimpleCode);
            list.add(schemeItems);
        }
        return list;
    }

    public List<TbZwtjSchemeItems> selectSchemeItemsByCombIds2(String combids) {
        List<TbZwtjSchemeItems> list = new ArrayList<>();
        if (StringUtils.isNotBlank(combids)) {
            StringBuilder sb = new StringBuilder();
            sb.append(" SELECT T.ITEM_CMBID, T1.CODE_NAME, T1.SPLSHT");
            sb.append(" FROM TB_ZWTJ_SCHEME_ITEMS T  ");
            sb.append(" INNER JOIN TS_SIMPLE_CODE T1 ON T.ITEM_CMBID = T1.RID ");
            sb.append(" INNER JOIN TB_ZWTJ_MAINSTD T2 ON T.SCHEME_ID = T2.RID ");
            sb.append(" WHERE T.ITEM_CMBID IN (").append(combids).append(")");
            sb.append(" AND T.STOP_TAG = 1 ");
            sb.append(" AND T2.STOP_TAG = 1 ");
            sb.append(" GROUP BY T.ITEM_CMBID, T1.CODE_NAME, T1.SPLSHT, T1.NUM, T1.CODE_NO ");
            sb.append(" ORDER BY T1.NUM ASC, T1.CODE_NO ");
            List<Object[]> schemeItemList = em.createNativeQuery(sb.toString()).getResultList();
            for(Object[] obj : schemeItemList) {
                TbZwtjSchemeItems schemeItems = new TbZwtjSchemeItems();
                TsSimpleCode tsSimpleCode = new TsSimpleCode();
                tsSimpleCode.setRid(Integer.parseInt(obj[0].toString()));
                tsSimpleCode.setCodeName(StringUtils.objectToString(obj[1]));
                tsSimpleCode.setSplsht(StringUtils.objectToString(obj[2]));
                schemeItems.setTsSimpleCode(tsSimpleCode);
                list.add(schemeItems);
            }
        }
        return list;
    }
}
