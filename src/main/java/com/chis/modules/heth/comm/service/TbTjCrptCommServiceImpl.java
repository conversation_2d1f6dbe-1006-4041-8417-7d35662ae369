package com.chis.modules.heth.comm.service;

import cn.hutool.core.convert.ConvertException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.json.CompanySearchItemVO;
import com.chis.modules.system.enumn.SystemMessageEnum;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.service.CommServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description : 企业信息业务层
 * @ClassAuthor : anjing
 * @Date : 2020/4/24 8:59
 **/
@Service
@Transactional(readOnly = true)
public class TbTjCrptCommServiceImpl extends AbstractTemplate {

    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
    * @Description : 查询企业是否存在相同的社会信用代码
    * @MethodAuthor: anjing
    * @Date : 2020/4/24 9:04
    **/
    public List<TbTjCrpt> findTbTjCrptByCreditCode(String creditCode, Integer rid) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select new TbTjCrpt(t.rid) from TbTjCrpt t where 1=1");
        if (StringUtils.isNotBlank(creditCode)) {
            sql.append(" and t.institutionCode = '").append(creditCode)
                    .append("'");
        }
        if (null != rid) {
            sql.append(" and t.rid !=").append(rid);
        }
        return em.createQuery(sql.toString()).getResultList();
    }

    /**
    * @Description : 保存企业信息
    * @MethodAuthor: anjing
    * @Date : 2020/4/24 9:04
    **/
    @Transactional(readOnly = false)
    public String saveTbTjCrpt(TbTjCrpt t) {
        if (null == t ) {
            return null;
        }
        if (null==t.getTsSimpleCodeByEconomyId().getRid()) {
            t.setTsSimpleCodeByEconomyId(null);
        }
        if (null==t.getTsSimpleCodeByIndusTypeId().getRid()) {
            t.setTsSimpleCodeByIndusTypeId(null);
        }
        if (null==t.getTsSimpleCodeByCrptSizeId().getRid()) {
            t.setTsSimpleCodeByCrptSizeId(null);
        }
        if (t.getRid() == null) {
            preInsert(t);
            this.save(t);
        } else {
            this.update(t);
        }
        return null;
    }

    /**
     * 生成虚拟社会信用代码
     *
     * @param pfx 虚拟社会信用代码前缀(W+地区编码前6位)
     * @return 虚拟社会信用代码
     */
    public synchronized String genVirtualCreditCode(String pfx) {
        String creditCode = this.commService.getAutoCode("ZYWS_VIRTUAL_CREDIT_CODE", pfx);
        if (this.countByCreditCode(creditCode) > 0) {
            return genVirtualCreditCode(pfx);
        }
        return creditCode;
    }

    /**
     * 根据社会信用代码查询企业数量
     */
    public int countByCreditCode(String creditCode) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("creditCode", creditCode);
        return this.findCountBySql("SELECT COUNT(1) FROM TB_TJ_CRPT WHERE INSTITUTION_CODE = :creditCode", paramMap);
    }

    /**
     * 通过天眼查查询企业信息（带页面错误信息）
     */
    public List<CompanySearchItemVO> searchCompanyByTyc(String searchCompanyWord) {
        if (ObjectUtil.isEmpty(searchCompanyWord)) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("单位名称或社会信用代码");
            throw new RuntimeException("调用天眼查接口失败：单位名称或社会信用代码不能为空！");
        }
        if (searchCompanyWord.length() < 4) {
            JsfUtil.addErrorMessage("单位名称或社会信用代码长度不能小于4！");
            throw new RuntimeException("调用天眼查接口失败：单位名称或社会信用代码长度不能小于4！");
        }
        if (searchCompanyWord.length() > 100) {
            SystemMessageEnum.LENGTH_VERIFY.formatMessage("单位名称或社会信用代码", 100);
            throw new RuntimeException("调用天眼查接口失败：单位名称或社会信用代码长度不能超过100！");
        }
        String companySearchUrl = this.commService.findParamValue("COMPANY_SEARCH_URL");
        if (ObjectUtil.isEmpty(companySearchUrl)) {
            SystemMessageEnum.MAINTENANCE_EMPTY.formatMessage("查询企业接口地址");
            throw new RuntimeException("调用天眼查接口失败：查询企业接口地址未维护！");
        }
        JSONObject entries = new JSONObject();
        entries.set("word", searchCompanyWord);
        String responseStr = "";
        System.out.println("调用天眼查接口，URL：" + companySearchUrl + "，请求参数：" + entries);
        try (HttpResponse response = HttpRequest.post(companySearchUrl)
                .body(entries.toString(), "application/json")
                .execute()) {
            System.out.println("调用天眼查接口，状态码：" + response.getStatus() + "，响应：" + response.body());
            if (response.getStatus() != 200) {
                throw new RuntimeException("调用天眼查接口失败！");
            }
            if (!org.springframework.util.StringUtils.hasText(response.body())) {
                throw new RuntimeException("调用天眼查接口失败！");
            }
            responseStr = response.body();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("调用天眼查接口失败！");
            throw new RuntimeException("调用天眼查接口失败！");
        }
        try {
            JSONObject response = JSONUtil.toBean(responseStr, JSONObject.class);

            // 返回状态码
            String code = response.get("code", String.class);
            // 请求无数据
            if (!"200".equals(code)) {
                throw new RuntimeException("调用天眼查接口失败！");
            }
            JSONObject data = response.get("data", JSONObject.class);
            if (ObjectUtil.isEmpty(data)) {
                throw new RuntimeException("调用天眼查接口失败！");
            }
            // 企业信息集合
            return data.getBeanList("rows", CompanySearchItemVO.class);
        } catch (ConvertException e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("调用天眼查接口失败！");
            throw new RuntimeException("调用天眼查接口失败！");
        }
    }
}
