package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-8-19
 */
@Entity
@Table(name = "TD_ZW_SRVORG_CARD")
@SequenceGenerator(name = "TdZwSrvorgCard", sequenceName = "TD_ZW_SRVORG_CARD_SEQ", allocationSize = 1)
public class TdZwSrvorgCard implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String cardNo;
	private String orgName;
	private String orgFz;
	private String orgAddr;
	private String certNo;
	private String proFz;
	private String proLinktel;
	private Integer unitType;
	private TsUnit fkByUnitId;
	private TsZone fkByZoneId;
	private String unitName;
	private String address;
	private String linkMan;
	private String linkPhone;
	private TsSimpleCode fkBySutId;
	private Date investStartDate;
	private Date investEndDate;
	private Date jcStartDate;
	private Date jcEndDate;
	private Date rptDate;
	private String rptNo;
	private Integer ifFswsFhJc;
	private Integer ifFhJc;
	private Integer fhJcPoint;
	private Integer notHgFhJcPoint;
	private Integer ifInstZkJc;
	private Integer zkJcInstNum;
	private Integer notHgZkJcInstNum;
	private String notHgZkJcInstName;
	private Integer ifItemPj;
	private Integer ifItemYpj;
	private Integer ypjPoint;
	private Integer ifItemXgpj;
	private Integer xgpjJcPoint;
	private Integer notHgXgpjJcPoint;
	private Integer ifDoseMonit;
	private Integer doseMonitNum;
	private Integer gt5msv;
	private Integer gt20msvBhkNum;
	private Integer ifFhInstJc;
	private Integer ifJcInst;
	private Integer jcInstNum;
	private Integer jcNotHgInstNum;
	private String notHgInstName;
	private Integer ifJcUse;
	private Integer jcUseNum;
	private Integer jcNotHgUseNum;
	private String notHgUseName;
	private String orgFzMan;
	private String fillFormPsn;
	private String fillLink;
	private Date fillDate;
	private String fillUnitName;
	private String annexPath;
	private TsUnit fkByFillUnitId;
	private Integer state;
	private Integer delMark;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private List<TdZwSrvorgCardItems> srvorgCardItemsList = new ArrayList<>();
	private List<TdZwSrvorgCardPsn> srvorgCardPsnList = new ArrayList<>();
	private List<TdZwSrvorgCardZone> srvorgCardZoneList = new ArrayList<>();
	private List<TdZwSrvorgCardService> srvorgCardServices = new ArrayList<>();
	private List<TdZwSrvorgCardFhjc> srvorgCardFhjcList = new ArrayList<>();
	private List<TdZwSrvorgCardYpj> srvorgCardYpjList = new ArrayList<>();
	private List<TdZwSrvorgCardXgpj> srvorgCardXgpjList = new ArrayList<>();
	private List<TdZwSrvorgCardDose> srvorgCardDoseList = new ArrayList<>();

	//资质业务范围
	private String itemsName;
	//技术服务领域
	private String[] services;
	private String servicesName;
	//放射防护检测超标危害类型
	private String[] srvorgCardFhjcs;
	private String srvorgCardFhjcName;
	//已选择的人员
	private Set<String> choosePsnIds;
	//超标点位放射性危害类型
	private String[] srvorgCardYpjs;
	private String srvorgCardYpjName;
	//超标点位放射性危害类型-控制效果评价
	private String[] srvorgCardXgpjs;
	private String srvorgCardXgpjName;
	/**來源*/
	private Integer source;
	private String signAddress;

	// 检测类型
	private TsSimpleCode fkByJcTypeId;
	// 统一社会信用代码
	private String creditCode;


	public TdZwSrvorgCard() {
	}

	public TdZwSrvorgCard(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSrvorgCard")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}
	@Column(name = "CARD_NO")
	public String getCardNo() {
		return cardNo;
	}

	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}

	@Column(name = "ORG_NAME")
	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}	
			
	@Column(name = "ORG_FZ")	
	public String getOrgFz() {
		return orgFz;
	}

	public void setOrgFz(String orgFz) {
		this.orgFz = orgFz;
	}	
			
	@Column(name = "ORG_ADDR")	
	public String getOrgAddr() {
		return orgAddr;
	}

	public void setOrgAddr(String orgAddr) {
		this.orgAddr = orgAddr;
	}	
			
	@Column(name = "CERT_NO")	
	public String getCertNo() {
		return certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}	
			
	@Column(name = "PRO_FZ")	
	public String getProFz() {
		return proFz;
	}

	public void setProFz(String proFz) {
		this.proFz = proFz;
	}	
			
	@Column(name = "PRO_LINKTEL")	
	public String getProLinktel() {
		return proLinktel;
	}

	public void setProLinktel(String proLinktel) {
		this.proLinktel = proLinktel;
	}	
			
	@Column(name = "UNIT_TYPE")	
	public Integer getUnitType() {
		return unitType;
	}

	public void setUnitType(Integer unitType) {
		this.unitType = unitType;
	}	
			
	@ManyToOne
	@JoinColumn(name = "UNIT_ID")			
	public TsUnit getFkByUnitId() {
		return fkByUnitId;
	}

	public void setFkByUnitId(TsUnit fkByUnitId) {
		this.fkByUnitId = fkByUnitId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ZONE_ID")			
	public TsZone getFkByZoneId() {
		return fkByZoneId;
	}

	public void setFkByZoneId(TsZone fkByZoneId) {
		this.fkByZoneId = fkByZoneId;
	}	
			
	@Column(name = "UNIT_NAME")	
	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}	
			
	@Column(name = "ADDRESS")	
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}	
			
	@Column(name = "LINK_MAN")	
	public String getLinkMan() {
		return linkMan;
	}

	public void setLinkMan(String linkMan) {
		this.linkMan = linkMan;
	}	
			
	@Column(name = "LINK_PHONE")	
	public String getLinkPhone() {
		return linkPhone;
	}

	public void setLinkPhone(String linkPhone) {
		this.linkPhone = linkPhone;
	}	
			
	@ManyToOne
	@JoinColumn(name = "SUT_ID")			
	public TsSimpleCode getFkBySutId() {
		return fkBySutId;
	}

	public void setFkBySutId(TsSimpleCode fkBySutId) {
		this.fkBySutId = fkBySutId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "INVEST_START_DATE")			
	public Date getInvestStartDate() {
		return investStartDate;
	}

	public void setInvestStartDate(Date investStartDate) {
		this.investStartDate = investStartDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "INVEST_END_DATE")			
	public Date getInvestEndDate() {
		return investEndDate;
	}

	public void setInvestEndDate(Date investEndDate) {
		this.investEndDate = investEndDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "JC_START_DATE")			
	public Date getJcStartDate() {
		return jcStartDate;
	}

	public void setJcStartDate(Date jcStartDate) {
		this.jcStartDate = jcStartDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "JC_END_DATE")			
	public Date getJcEndDate() {
		return jcEndDate;
	}

	public void setJcEndDate(Date jcEndDate) {
		this.jcEndDate = jcEndDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "RPT_DATE")			
	public Date getRptDate() {
		return rptDate;
	}

	public void setRptDate(Date rptDate) {
		this.rptDate = rptDate;
	}	
			
	@Column(name = "RPT_NO")	
	public String getRptNo() {
		return rptNo;
	}

	public void setRptNo(String rptNo) {
		this.rptNo = rptNo;
	}	
			
	@Column(name = "IF_FSWS_FH_JC")	
	public Integer getIfFswsFhJc() {
		return ifFswsFhJc;
	}

	public void setIfFswsFhJc(Integer ifFswsFhJc) {
		this.ifFswsFhJc = ifFswsFhJc;
	}	
			
	@Column(name = "IF_FH_JC")	
	public Integer getIfFhJc() {
		return ifFhJc;
	}

	public void setIfFhJc(Integer ifFhJc) {
		this.ifFhJc = ifFhJc;
	}	
			
	@Column(name = "FH_JC_POINT")	
	public Integer getFhJcPoint() {
		return fhJcPoint;
	}

	public void setFhJcPoint(Integer fhJcPoint) {
		this.fhJcPoint = fhJcPoint;
	}	
			
	@Column(name = "NOT_HG_FH_JC_POINT")	
	public Integer getNotHgFhJcPoint() {
		return notHgFhJcPoint;
	}

	public void setNotHgFhJcPoint(Integer notHgFhJcPoint) {
		this.notHgFhJcPoint = notHgFhJcPoint;
	}	
			
	@Column(name = "IF_INST_ZK_JC")	
	public Integer getIfInstZkJc() {
		return ifInstZkJc;
	}

	public void setIfInstZkJc(Integer ifInstZkJc) {
		this.ifInstZkJc = ifInstZkJc;
	}	
			
	@Column(name = "ZK_JC_INST_NUM")	
	public Integer getZkJcInstNum() {
		return zkJcInstNum;
	}

	public void setZkJcInstNum(Integer zkJcInstNum) {
		this.zkJcInstNum = zkJcInstNum;
	}	
			
	@Column(name = "NOT_HG_ZK_JC_INST_NUM")	
	public Integer getNotHgZkJcInstNum() {
		return notHgZkJcInstNum;
	}

	public void setNotHgZkJcInstNum(Integer notHgZkJcInstNum) {
		this.notHgZkJcInstNum = notHgZkJcInstNum;
	}	
			
	@Column(name = "NOT_HG_ZK_JC_INST_NAME")	
	public String getNotHgZkJcInstName() {
		return notHgZkJcInstName;
	}

	public void setNotHgZkJcInstName(String notHgZkJcInstName) {
		this.notHgZkJcInstName = notHgZkJcInstName;
	}	
			
	@Column(name = "IF_ITEM_PJ")	
	public Integer getIfItemPj() {
		return ifItemPj;
	}

	public void setIfItemPj(Integer ifItemPj) {
		this.ifItemPj = ifItemPj;
	}	
			
	@Column(name = "IF_ITEM_YPJ")	
	public Integer getIfItemYpj() {
		return ifItemYpj;
	}

	public void setIfItemYpj(Integer ifItemYpj) {
		this.ifItemYpj = ifItemYpj;
	}	
			
	@Column(name = "YPJ_POINT")	
	public Integer getYpjPoint() {
		return ypjPoint;
	}

	public void setYpjPoint(Integer ypjPoint) {
		this.ypjPoint = ypjPoint;
	}	
			
	@Column(name = "IF_ITEM_XGPJ")	
	public Integer getIfItemXgpj() {
		return ifItemXgpj;
	}

	public void setIfItemXgpj(Integer ifItemXgpj) {
		this.ifItemXgpj = ifItemXgpj;
	}	
			
	@Column(name = "XGPJ_JC_POINT")	
	public Integer getXgpjJcPoint() {
		return xgpjJcPoint;
	}

	public void setXgpjJcPoint(Integer xgpjJcPoint) {
		this.xgpjJcPoint = xgpjJcPoint;
	}	
			
	@Column(name = "NOT_HG_XGPJ_JC_POINT")	
	public Integer getNotHgXgpjJcPoint() {
		return notHgXgpjJcPoint;
	}

	public void setNotHgXgpjJcPoint(Integer notHgXgpjJcPoint) {
		this.notHgXgpjJcPoint = notHgXgpjJcPoint;
	}	
			
	@Column(name = "IF_DOSE_MONIT")	
	public Integer getIfDoseMonit() {
		return ifDoseMonit;
	}

	public void setIfDoseMonit(Integer ifDoseMonit) {
		this.ifDoseMonit = ifDoseMonit;
	}	
			
	@Column(name = "DOSE_MONIT_NUM")	
	public Integer getDoseMonitNum() {
		return doseMonitNum;
	}

	public void setDoseMonitNum(Integer doseMonitNum) {
		this.doseMonitNum = doseMonitNum;
	}	
			
	@Column(name = "GT_5MSV")	
	public Integer getGt5msv() {
		return gt5msv;
	}

	public void setGt5msv(Integer gt5msv) {
		this.gt5msv = gt5msv;
	}	
			
	@Column(name = "GT_20MSV_BHK_NUM")	
	public Integer getGt20msvBhkNum() {
		return gt20msvBhkNum;
	}

	public void setGt20msvBhkNum(Integer gt20msvBhkNum) {
		this.gt20msvBhkNum = gt20msvBhkNum;
	}	
			
	@Column(name = "IF_FH_INST_JC")	
	public Integer getIfFhInstJc() {
		return ifFhInstJc;
	}

	public void setIfFhInstJc(Integer ifFhInstJc) {
		this.ifFhInstJc = ifFhInstJc;
	}	
			
	@Column(name = "IF_JC_INST")	
	public Integer getIfJcInst() {
		return ifJcInst;
	}

	public void setIfJcInst(Integer ifJcInst) {
		this.ifJcInst = ifJcInst;
	}	
			
	@Column(name = "JC_INST_NUM")	
	public Integer getJcInstNum() {
		return jcInstNum;
	}

	public void setJcInstNum(Integer jcInstNum) {
		this.jcInstNum = jcInstNum;
	}	
			
	@Column(name = "JC_NOT_HG_INST_NUM")	
	public Integer getJcNotHgInstNum() {
		return jcNotHgInstNum;
	}

	public void setJcNotHgInstNum(Integer jcNotHgInstNum) {
		this.jcNotHgInstNum = jcNotHgInstNum;
	}	
			
	@Column(name = "NOT_HG_INST_NAME")	
	public String getNotHgInstName() {
		return notHgInstName;
	}

	public void setNotHgInstName(String notHgInstName) {
		this.notHgInstName = notHgInstName;
	}	
			
	@Column(name = "IF_JC_USE")	
	public Integer getIfJcUse() {
		return ifJcUse;
	}

	public void setIfJcUse(Integer ifJcUse) {
		this.ifJcUse = ifJcUse;
	}	
			
	@Column(name = "JC_USE_NUM")	
	public Integer getJcUseNum() {
		return jcUseNum;
	}

	public void setJcUseNum(Integer jcUseNum) {
		this.jcUseNum = jcUseNum;
	}	
			
	@Column(name = "JC_NOT_HG_USE_NUM")	
	public Integer getJcNotHgUseNum() {
		return jcNotHgUseNum;
	}

	public void setJcNotHgUseNum(Integer jcNotHgUseNum) {
		this.jcNotHgUseNum = jcNotHgUseNum;
	}	
			
	@Column(name = "NOT_HG_USE_NAME")	
	public String getNotHgUseName() {
		return notHgUseName;
	}

	public void setNotHgUseName(String notHgUseName) {
		this.notHgUseName = notHgUseName;
	}	
			
	@Column(name = "ORG_FZ_MAN")	
	public String getOrgFzMan() {
		return orgFzMan;
	}

	public void setOrgFzMan(String orgFzMan) {
		this.orgFzMan = orgFzMan;
	}	
			
	@Column(name = "FILL_FORM_PSN")	
	public String getFillFormPsn() {
		return fillFormPsn;
	}

	public void setFillFormPsn(String fillFormPsn) {
		this.fillFormPsn = fillFormPsn;
	}	
			
	@Column(name = "FILL_LINK")	
	public String getFillLink() {
		return fillLink;
	}

	public void setFillLink(String fillLink) {
		this.fillLink = fillLink;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "FILL_DATE")			
	public Date getFillDate() {
		return fillDate;
	}

	public void setFillDate(Date fillDate) {
		this.fillDate = fillDate;
	}	
			
	@Column(name = "FILL_UNIT_NAME")	
	public String getFillUnitName() {
		return fillUnitName;
	}

	public void setFillUnitName(String fillUnitName) {
		this.fillUnitName = fillUnitName;
	}	
			
	@Column(name = "ANNEX_PATH")	
	public String getAnnexPath() {
		return annexPath;
	}

	public void setAnnexPath(String annexPath) {
		this.annexPath = annexPath;
	}	
			
	@ManyToOne
	@JoinColumn(name = "FILL_UNIT_ID")			
	public TsUnit getFkByFillUnitId() {
		return fkByFillUnitId;
	}

	public void setFkByFillUnitId(TsUnit fkByFillUnitId) {
		this.fkByFillUnitId = fkByFillUnitId;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Column(name = "DEL_MARK")	
	public Integer getDelMark() {
		return delMark;
	}

	public void setDelMark(Integer delMark) {
		this.delMark = delMark;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwSrvorgCardItems> getSrvorgCardItemsList() {
		return srvorgCardItemsList;
	}

	public void setSrvorgCardItemsList(List<TdZwSrvorgCardItems> srvorgCardItemsList) {
		this.srvorgCardItemsList = srvorgCardItemsList;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwSrvorgCardPsn> getSrvorgCardPsnList() {
		return srvorgCardPsnList;
	}

	public void setSrvorgCardPsnList(List<TdZwSrvorgCardPsn> srvorgCardPsnList) {
		this.srvorgCardPsnList = srvorgCardPsnList;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwSrvorgCardZone> getSrvorgCardZoneList() {
		return srvorgCardZoneList;
	}

	public void setSrvorgCardZoneList(List<TdZwSrvorgCardZone> srvorgCardZoneList) {
		this.srvorgCardZoneList = srvorgCardZoneList;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwSrvorgCardService> getSrvorgCardServices() {
		return srvorgCardServices;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public void setSrvorgCardServices(List<TdZwSrvorgCardService> srvorgCardServices) {
		this.srvorgCardServices = srvorgCardServices;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwSrvorgCardFhjc> getSrvorgCardFhjcList() {
		return srvorgCardFhjcList;
	}

	public void setSrvorgCardFhjcList(List<TdZwSrvorgCardFhjc> srvorgCardFhjcList) {
		this.srvorgCardFhjcList = srvorgCardFhjcList;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwSrvorgCardYpj> getSrvorgCardYpjList() {
		return srvorgCardYpjList;
	}

	public void setSrvorgCardYpjList(List<TdZwSrvorgCardYpj> srvorgCardYpjList) {
		this.srvorgCardYpjList = srvorgCardYpjList;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwSrvorgCardXgpj> getSrvorgCardXgpjList() {
		return srvorgCardXgpjList;
	}

	public void setSrvorgCardXgpjList(List<TdZwSrvorgCardXgpj> srvorgCardXgpjList) {
		this.srvorgCardXgpjList = srvorgCardXgpjList;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwSrvorgCardDose> getSrvorgCardDoseList() {
		return srvorgCardDoseList;
	}

	public void setSrvorgCardDoseList(List<TdZwSrvorgCardDose> srvorgCardDoseList) {
		this.srvorgCardDoseList = srvorgCardDoseList;
	}

	@Transient
	public String getItemsName() {
		return itemsName;
	}

	public void setItemsName(String itemsName) {
		this.itemsName = itemsName;
	}
	@Transient
	public String[] getServices() {
		return services;
	}

	public void setServices(String[] services) {
		this.services = services;
	}

	@Transient
	public String[] getSrvorgCardFhjcs() {
		return srvorgCardFhjcs;
	}
	public void setSrvorgCardFhjcs(String[] srvorgCardFhjcs) {
		this.srvorgCardFhjcs = srvorgCardFhjcs;
	}
	@Transient
	public Set<String> getChoosePsnIds() {
		return choosePsnIds;
	}

	public void setChoosePsnIds(Set<String> choosePsnIds) {
		this.choosePsnIds = choosePsnIds;
	}
	@Transient
	public String[] getSrvorgCardYpjs() {
		return srvorgCardYpjs;
	}

	public void setSrvorgCardYpjs(String[] srvorgCardYpjs) {
		this.srvorgCardYpjs = srvorgCardYpjs;
	}
	@Transient
	public String[] getSrvorgCardXgpjs() {
		return srvorgCardXgpjs;
	}

	public void setSrvorgCardXgpjs(String[] srvorgCardXgpjs) {
		this.srvorgCardXgpjs = srvorgCardXgpjs;
	}
	@Transient
	public String getServicesName() {
		return servicesName;
	}

	public void setServicesName(String servicesName) {
		this.servicesName = servicesName;
	}
	@Transient
	public String getSrvorgCardFhjcName() {
		return srvorgCardFhjcName;
	}

	public void setSrvorgCardFhjcName(String srvorgCardFhjcName) {
		this.srvorgCardFhjcName = srvorgCardFhjcName;
	}
	@Transient
	public String getSrvorgCardYpjName() {
		return srvorgCardYpjName;
	}

	public void setSrvorgCardYpjName(String srvorgCardYpjName) {
		this.srvorgCardYpjName = srvorgCardYpjName;
	}
	@Transient
	public String getSrvorgCardXgpjName() {
		return srvorgCardXgpjName;
	}

	public void setSrvorgCardXgpjName(String srvorgCardXgpjName) {
		this.srvorgCardXgpjName = srvorgCardXgpjName;
	}
	@Column(name = "SOURCE")
	public Integer getSource() {
		return source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}
	@Column(name = "SIGN_ADDRESS")
	public String getSignAddress() {
		return signAddress;
	}

	public void setSignAddress(String signAddress) {
		this.signAddress = signAddress;
	}

	@ManyToOne
	@JoinColumn(name = "JC_TYPE_ID")
	public TsSimpleCode getFkByJcTypeId() {
		return fkByJcTypeId;
	}

	public void setFkByJcTypeId(TsSimpleCode fkByJcTypeId) {
		this.fkByJcTypeId = fkByJcTypeId;
	}

	@Column(name = "CREDIT_CODE")
	public String getCreditCode() {
		return creditCode;
	}

	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}
}