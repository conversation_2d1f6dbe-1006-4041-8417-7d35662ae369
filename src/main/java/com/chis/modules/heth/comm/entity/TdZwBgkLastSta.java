package com.chis.modules.heth.comm.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * @Description : 报告卡最新状态[职卫报告卡]
 * <AUTHOR>
 * @createTime 2019-9-16
 */
@Entity
@Table(name = "TD_ZW_BGK_LAST_STA")
@SequenceGenerator(name = "TdZwBgkLastSta", sequenceName = "TD_ZW_BGK_LAST_STA_SEQ", allocationSize = 1)
public class TdZwBgkLastSta implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private Integer cartType;
    private Integer busId;
    private Date orgRcvDate;
    private Date countyRcvDate;
    private Date countySmtDate;
    private Date cityRcvDate;
    private Date proRcvDate;
    private Date proSmtDate;
    private String countyBackRsn;
    private String cityBackRsn;
    private String proBackRsn;
    private Integer state;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private Date orgSmtDate;
    /**区级审核意见*/
    private String countAuditAdv;
    /**市级审核意见*/
    private String cityAuditAdv;

    /**+省级审核意见（最新）20200713*/
    private String proAuditAdv;
    /**+省级审核人20200713*/
    private String proChkPsn;
    /**+市级审核人20200713*/
    private String countyChkPsn;
    /**+区级审核人20200713*/
    private String cityChkPsn;
    /**区级审核结果*/
    private Integer countyRst;
    /**市级审核结果*/
    private Integer cityRst;
    /**省级审核结果*/
    private Integer proRst;

    public TdZwBgkLastSta() {
    }

    public TdZwBgkLastSta(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwBgkLastSta")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Column(name = "CART_TYPE")
    public Integer getCartType() {
        return cartType;
    }

    public void setCartType(Integer cartType) {
        this.cartType = cartType;
    }

    @Column(name = "BUS_ID")
    public Integer getBusId() {
        return busId;
    }

    public void setBusId(Integer busId) {
        this.busId = busId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "ORG_RCV_DATE")
    public Date getOrgRcvDate() {
        return orgRcvDate;
    }

    public void setOrgRcvDate(Date orgRcvDate) {
        this.orgRcvDate = orgRcvDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "COUNTY_RCV_DATE")
    public Date getCountyRcvDate() {
        return countyRcvDate;
    }

    public void setCountyRcvDate(Date countyRcvDate) {
        this.countyRcvDate = countyRcvDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "CITY_RCV_DATE")
    public Date getCityRcvDate() {
        return cityRcvDate;
    }

    public void setCityRcvDate(Date cityRcvDate) {
        this.cityRcvDate = cityRcvDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "PRO_RCV_DATE")
    public Date getProRcvDate() {
        return proRcvDate;
    }

    public void setProRcvDate(Date proRcvDate) {
        this.proRcvDate = proRcvDate;
    }

    @Column(name = "COUNTY_BACK_RSN")
    public String getCountyBackRsn() {
        return countyBackRsn;
    }

    public void setCountyBackRsn(String countyBackRsn) {
        this.countyBackRsn = countyBackRsn;
    }

    @Column(name = "CITY_BACK_RSN")
    public String getCityBackRsn() {
        return cityBackRsn;
    }

    public void setCityBackRsn(String cityBackRsn) {
        this.cityBackRsn = cityBackRsn;
    }

    @Column(name = "PRO_BACK_RSN")
    public String getProBackRsn() {
        return proBackRsn;
    }

    public void setProBackRsn(String proBackRsn) {
        this.proBackRsn = proBackRsn;
    }

    @Column(name = "STATE")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "PRO_SMT_DATE")
    public Date getProSmtDate() {
        return proSmtDate;
    }

    public void setProSmtDate(Date proSmtDate) {
        this.proSmtDate = proSmtDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "COUNTY_SMT_DATE")
    public Date getCountySmtDate() {
        return countySmtDate;
    }

    public void setCountySmtDate(Date countySmtDate) {
        this.countySmtDate = countySmtDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "ORG_SMT_DATE")
    public Date getOrgSmtDate() {
        return orgSmtDate;
    }

    public void setOrgSmtDate(Date orgSmtDate) {
        this.orgSmtDate = orgSmtDate;
    }
    @Column(name = "COUNTY_AUDIT_ADV")
    public String getCountAuditAdv() {
        return countAuditAdv;
    }
    public void setCountAuditAdv(String countAuditAdv) {
        this.countAuditAdv = countAuditAdv;
    }

    @Column(name = "CITY_AUDIT_ADV")
    public String getCityAuditAdv() {
        return cityAuditAdv;
    }
    public void setCityAuditAdv(String cityAuditAdv) {
        this.cityAuditAdv = cityAuditAdv;
    }

    @Column(name = "PRO_AUDIT_ADV")
    public String getProAuditAdv() {
        return proAuditAdv;
    }

    public void setProAuditAdv(String proAuditAdv) {
        this.proAuditAdv = proAuditAdv;
    }

    @Column(name = "PRO_CHK_PSN")
    public String getProChkPsn() {
        return proChkPsn;
    }

    public void setProChkPsn(String proChkPsn) {
        this.proChkPsn = proChkPsn;
    }

    @Column(name = "COUNTY_CHK_PSN")
    public String getCountyChkPsn() {
        return countyChkPsn;
    }

    public void setCountyChkPsn(String countyChkPsn) {
        this.countyChkPsn = countyChkPsn;
    }

    @Column(name = "CITY_CHK_PSN")
    public String getCityChkPsn() {
        return cityChkPsn;
    }

    public void setCityChkPsn(String cityChkPsn) {
        this.cityChkPsn = cityChkPsn;
    }

    @Column(name = "COUNTY_RST")
    public Integer getCountyRst() {
        return countyRst;
    }

    public void setCountyRst(Integer countyRst) {
        this.countyRst = countyRst;
    }

    @Column(name = "CITY_RST")
    public Integer getCityRst() {
        return cityRst;
    }

    public void setCityRst(Integer cityRst) {
        this.cityRst = cityRst;
    }

    @Column(name = "PRO_RST")
    public Integer getProRst() {
        return proRst;
    }

    public void setProRst(Integer proRst) {
        this.proRst = proRst;
    }
}
