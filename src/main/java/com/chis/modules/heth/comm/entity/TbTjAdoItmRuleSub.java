package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-4-10
 */
@Entity
@Table(name = "TB_TJ_ADO_ITM_RULE_SUB")
@SequenceGenerator(name = "TbTjAdoItmRuleSub", sequenceName = "TB_TJ_ADO_ITM_RULE_SUB_SEQ", allocationSize = 1)
public class TbTjAdoItmRuleSub implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbTjAudioItemRule fkByMainId;
	private Integer ageGe;
	private Integer ageGt;
	private Integer ageLe;
	private Integer ageLt;
	private Integer medVal;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TbTjAdoItmRuleSub() {
	}

	public TbTjAdoItmRuleSub(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjAdoItmRuleSub")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TbTjAudioItemRule getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TbTjAudioItemRule fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "AGE_GE")	
	public Integer getAgeGe() {
		return ageGe;
	}

	public void setAgeGe(Integer ageGe) {
		this.ageGe = ageGe;
	}	
			
	@Column(name = "AGE_GT")	
	public Integer getAgeGt() {
		return ageGt;
	}

	public void setAgeGt(Integer ageGt) {
		this.ageGt = ageGt;
	}	
			
	@Column(name = "AGE_LE")	
	public Integer getAgeLe() {
		return ageLe;
	}

	public void setAgeLe(Integer ageLe) {
		this.ageLe = ageLe;
	}	
			
	@Column(name = "AGE_LT")	
	public Integer getAgeLt() {
		return ageLt;
	}

	public void setAgeLt(Integer ageLt) {
		this.ageLt = ageLt;
	}	
			
	@Column(name = "MED_VAL")	
	public Integer getMedVal() {
		return medVal;
	}

	public void setMedVal(Integer medVal) {
		this.medVal = medVal;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}