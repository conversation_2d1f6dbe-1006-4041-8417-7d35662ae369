package com.chis.modules.heth.comm.entity;

import java.io.Serializable;

/**
 * 用人单位审核BO
 *
 * <AUTHOR>
 * @version 1.0
 */
public class CrptCheckBO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用人单位RID
     */
    private Integer crptRid;
    /**
     * 用人单位名称
     */
    private String crptName;
    /**
     * 用人单位社会信用代码
     */
    private String institutionCode;
    /**
     * 用人单位地区RID
     */
    private Integer zoneRid;
    /**
     * 用人单位地区真实级别
     */
    private Integer zoneRealType;
    /**
     * 用人单位地区是否市直属
     */
    private Boolean ifCityDirect;
    /**
     * 用人单位地区是否省直属
     */
    private Boolean ifProvDirect;
    /**
     * 用人单位行业类别
     */
    private Integer indusTypeRid;
    /**
     * 用人单位经济类型
     */
    private Integer economyRid;
    /**
     * 用人单位企业规模
     */
    private Integer crptSizeRid;
    /**
     * 用人单位邮政编码
     */
    private String postCard;
    /**
     * 用人单位法人联系电话
     */
    private String corporatePhone;
    /**
     * 用人单位联系人电话
     */
    private String linkPhone2;
    /**
     * 用人单位职工人数
     */
    private Integer workForce;
    /**
     * 用人单位接触职业病危害因素人数
     */
    private Integer holdCardMan;
    /**
     * 用人单位生产工人数
     */
    private Integer workmanNum;
    /**
     * 用人单位外委人员数
     */
    private Integer outsourceNum;
    /**
     * 用人单位审核状态
     */
    private Integer state;
    /**
     * 用人单位是否无状态表记录
     */
    private Boolean stateNew;

    public CrptCheckBO() {
    }

    public Integer getCrptRid() {
        return crptRid;
    }

    public void setCrptRid(Integer crptRid) {
        this.crptRid = crptRid;
    }

    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    public String getInstitutionCode() {
        return institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    public Integer getZoneRid() {
        return zoneRid;
    }

    public void setZoneRid(Integer zoneRid) {
        this.zoneRid = zoneRid;
    }

    public Integer getZoneRealType() {
        return zoneRealType;
    }

    public void setZoneRealType(Integer zoneRealType) {
        this.zoneRealType = zoneRealType;
    }

    public Boolean getIfCityDirect() {
        return ifCityDirect;
    }

    public void setIfCityDirect(Boolean ifCityDirect) {
        this.ifCityDirect = ifCityDirect;
    }

    public Boolean getIfProvDirect() {
        return ifProvDirect;
    }

    public void setIfProvDirect(Boolean ifProvDirect) {
        this.ifProvDirect = ifProvDirect;
    }

    public Integer getIndusTypeRid() {
        return indusTypeRid;
    }

    public void setIndusTypeRid(Integer indusTypeRid) {
        this.indusTypeRid = indusTypeRid;
    }

    public Integer getEconomyRid() {
        return economyRid;
    }

    public void setEconomyRid(Integer economyRid) {
        this.economyRid = economyRid;
    }

    public Integer getCrptSizeRid() {
        return crptSizeRid;
    }

    public void setCrptSizeRid(Integer crptSizeRid) {
        this.crptSizeRid = crptSizeRid;
    }

    public String getPostCard() {
        return postCard;
    }

    public void setPostCard(String postCard) {
        this.postCard = postCard;
    }

    public String getCorporatePhone() {
        return corporatePhone;
    }

    public void setCorporatePhone(String corporatePhone) {
        this.corporatePhone = corporatePhone;
    }

    public String getLinkPhone2() {
        return linkPhone2;
    }

    public void setLinkPhone2(String linkPhone2) {
        this.linkPhone2 = linkPhone2;
    }

    public Integer getWorkForce() {
        return workForce;
    }

    public void setWorkForce(Integer workForce) {
        this.workForce = workForce;
    }

    public Integer getHoldCardMan() {
        return holdCardMan;
    }

    public void setHoldCardMan(Integer holdCardMan) {
        this.holdCardMan = holdCardMan;
    }

    public Integer getWorkmanNum() {
        return workmanNum;
    }

    public void setWorkmanNum(Integer workmanNum) {
        this.workmanNum = workmanNum;
    }

    public Integer getOutsourceNum() {
        return outsourceNum;
    }

    public void setOutsourceNum(Integer outsourceNum) {
        this.outsourceNum = outsourceNum;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Boolean getStateNew() {
        return stateNew;
    }

    public void setStateNew(Boolean stateNew) {
        this.stateNew = stateNew;
    }
}
