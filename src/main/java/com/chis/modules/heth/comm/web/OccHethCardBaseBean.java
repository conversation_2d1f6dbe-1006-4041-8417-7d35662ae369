package com.chis.modules.heth.comm.web;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.service.OcchethCardGeneralService;
import com.chis.modules.heth.comm.utils.SpecSm4Utils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.javabean.FilePojoNew;
import com.chis.modules.system.enumn.SystemMessageEnum;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;

/**
 * <p>类描述：职业卫生技术服务信息报送卡-标准版基类</p>
 *
 * @ClassAuthor hsj 2025-05-12 17:14
 */
public class OccHethCardBaseBean extends FacesEditBean {
    private static final long serialVersionUID = 1L;

    protected final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    protected final OcchethCardGeneralService occhethCardService = SpringContextHolder.getBean(OcchethCardGeneralService.class);

    protected Integer mainRid;

    protected TdZwOcchethCard occhethCard;

    /**
     * 查询条件：服务单位地区集合
     */
    protected List<TsZone> searchZoneList = new ArrayList<>();
    /**
     * 查询条件：服务单位地区编码
     */
    protected String searchZoneGb;
    /**
     * 查询条件：服务单位地区名称
     */
    protected String searchZoneName;
    /**
     * 查询条件：服务单位名称
     */
    protected String searchCrptName;
    /**
     * 查询条件：出具技术服务报告时间-开始日期
     */
    protected Date searchRptBeginDate;
    /**
     * 查询条件：出具技术服务报告时间-结束日期
     */
    protected Date searchRptEndDate;
    /**
     * 查询条件：状态
     */
    protected List<Integer> searchStateList;
    /**
     * 码表Map key：码表RID
     */
    protected Map<String, TsSimpleCode> simpleCodeMap;
    /**
     * 业务范围码表5320
     */
    protected List<TsSimpleCode> businessScopeList = new ArrayList<>();
    /**
     * 是否支持撤销 当上传日志表 状态为3（已提交） 不可撤销
     */
    protected Boolean ifSupportCancel;
    /**
     * 超标检测项目 key:码表rid value: 码表
     */
    protected Map<Integer, TsSimpleCode> itemMap = new HashMap<>();
    /**
     * 超标检测指标 key:码表rid value: 码表
     */
    protected Map<Integer, TsSimpleCode> indexMap = new HashMap<>();
    protected Map<Integer, TsSimpleCode> indexAllMap = new HashMap<>();
    /**
     * 超标检测指标 码表
     */
    protected List<TsSimpleCode> indexList;

    /**
     * 危害因素-码表5517
     */
    protected List<TsSimpleCode> parentBadRsnList;
    /**
     * 技术服务结果扩展字段1
     */
    protected String selServiceResultExtends1;
    // 技术服务结果扩展字段3
    protected String selServiceResultExtends3;
    /**
     * 码表map，key扩展字段1
     */
    protected Map<String, TsSimpleCode> simpleCodeEx1Map;

    /**
     * 职业病危害因素明细
     */
    protected TdZwOcchethBpSub occhethBpBadrsnSub;
    /**
     * 职业病危害因素检测（超标岗位和检测项目）
     */
    protected TdZwOcchethBadrsnItem occhethBadrsnItem;
    /**
     * 职业病危害因素检测结果-表格
     */
    protected List<TdZwOcchethBpSub> badrsnSubList;
    /**打卡记录集合*/
    protected List<Object[]> appClockList;

    /**当前查看的附件下标*/
    protected Integer annexViewIndex;
    /**附件类*/
    protected Object[] contractAnnex;
    /**文件预览html代码*/
    protected FilePojoNew fileTemp;

    /**是否详情页*/
    protected boolean ifView;

    /**技术服务结果显示*/
    protected String serviceResultMsg;

    public OccHethCardBaseBean() {
        this.init();
        this.initSimpleCode();
        this.ifSQL = true;
        this.ifSupportCancel = Boolean.FALSE;
    }


    private void init() {
        this.occhethCard = new TdZwOcchethCard();
    }

    private void initSimpleCode() {
        this.simpleCodeMap = new HashMap<>();
        this.simpleCodeEx1Map = new HashMap<>();
        //业务范围-取最末级
        this.businessScopeList = this.commService.findNumSimpleCodesByTypeId("5320");
        int index = 0;
        for (TsSimpleCode simpleCode : this.businessScopeList) {
            if (ObjectUtil.isEmpty(simpleCode.getCodeLevelNo())) {
                continue;
            }
            int lastIndex = simpleCode.getCodeLevelNo().lastIndexOf(".");
            if (lastIndex > index) {
                index = lastIndex;
            }
        }
        for (int i = this.businessScopeList.size() - 1; i >= 0; i--) {
            TsSimpleCode simpleCode = this.businessScopeList.get(i);
            if (ObjectUtil.isEmpty(simpleCode.getCodeLevelNo()) || index != simpleCode.getCodeLevelNo().lastIndexOf(".")) {
                this.businessScopeList.remove(i);
            }
        }
        addSimpleCodeMapByList(this.businessScopeList);
        //超标检测指标
        this.indexList=new ArrayList<>();
        this.indexAllMap=new HashMap<>();
        List<TsSimpleCode> indexAllList = this.commService.findallSimpleCodesByTypeIdOrderByNum("5532");
        if (!CollectionUtils.isEmpty(indexAllList)) {
            for (TsSimpleCode tsSimpleCode : indexAllList) {
                if(new Short("1").equals(tsSimpleCode.getIfReveal())){
                    this.indexList.add(tsSimpleCode);
                    indexMap.put(tsSimpleCode.getRid(), tsSimpleCode);
                }
                this.indexAllMap.put(tsSimpleCode.getRid(), tsSimpleCode);
            }
        }
        //码表5517
        this.parentBadRsnList = this.commService.findNumSimpleCodesByTypeId("5517");
        addSimpleCodeEx1MapByList(parentBadRsnList);
        addSimpleCodeMapByList(this.parentBadRsnList);
    }

    /**
     * <p>方法描述：码表map封装
     * key:ex1;value:码表</p>
     *
     * @MethodAuthor hsj 2025-05-16 17:54
     */
    private void addSimpleCodeEx1MapByList(List<TsSimpleCode> simpleCodeList) {
        if (CollectionUtils.isEmpty(simpleCodeList)) {
            return;
        }
        for (TsSimpleCode simpleCode : simpleCodeList) {
            String ex1 = simpleCode.getExtendS1();
            if (StringUtils.isBlank(ex1)) {
                continue;
            }
            this.simpleCodeEx1Map.put(ex1, simpleCode);
        }
    }

    public void addSimpleCodeMapByList(List<TsSimpleCode> simpleCodeList) {
        if (CollectionUtils.isEmpty(simpleCodeList)) {
            return;
        }
        for (TsSimpleCode simpleCode : simpleCodeList) {
            this.simpleCodeMap.put(simpleCode.getRid().toString(), simpleCode);
        }
    }


    @Override
    public String[] buildHqls() {
        return new String[0];
    }


    @Override
    public void addInit() {
    }

    @Override
    public void modInit() {
    }

    @Override
    public void viewInit() {
        this.serviceResultMsg=null;
        this.appClockList=new ArrayList<>();
        this.occhethCard = this.occhethCardService.findTdZwOcchethCardByRid(this.mainRid);

        //处理-参与人员信息
        for (TdZwOcchethCardPsn occhethCardPsn : this.occhethCard.getOcchethCardPsnList()) {
            if (ObjectUtil.isEmpty(occhethCardPsn)) {
                continue;
            }
            List<String> occhethCardItemCodeNameStrList = new ArrayList<>();
            for (TdZwOcchethCardItem occhethCardItem : occhethCardPsn.getOcchethCardItemList()) {
                if (ObjectUtil.isEmpty(occhethCardItem.getFkByItemId())) {
                    continue;
                }
                occhethCardItemCodeNameStrList.add(occhethCardItem.getFkByItemId().getCodeName());
            }
            occhethCardPsn.setOcchethCardItemStr(StringUtils.list2string(occhethCardItemCodeNameStrList, "，"));
        }
        //按rid排序
        occhethCardPsnSort();
        //处理-技术服务信息-技术服务领域
        List<String> occhethCardServiceStrList = new ArrayList<>();
        for (TdZwOcchethCardService occhethCardService : this.occhethCard.getOcchethCardServiceList()) {
            if (ObjectUtil.isEmpty(occhethCardService.getFkByServiceAreaId())) {
                continue;
            }
            occhethCardServiceStrList.add(occhethCardService.getFkByServiceAreaId().getCodeName());
        }
        this.occhethCard.setOcchethCardServiceStr(StringUtils.list2string(occhethCardServiceStrList, "，"));

        publicInit();
        //详情页面 服务结果初始化
        viewServiceResultInit();
        //技术服务结果显示
        if(this.occhethCard.getHasBadrsnJc() || "1".equals(selServiceResultExtends1)){
            this.serviceResultMsg="职业病危害因素检测";
        }else if(this.occhethCard.getHasStatusPj() || "2".equals(selServiceResultExtends1)){
            this.serviceResultMsg="职业病危害现状评价";
        }else if((this.occhethCard.getHasJcUse() || this.occhethCard.getHasJcInst() || "3".equals(selServiceResultExtends1) && !(this.occhethCard.getHasBadrsnJc() || "1".equals(selServiceResultExtends1)) && !(this.occhethCard.getHasStatusPj() || "2".equals(selServiceResultExtends1)))){
            this.serviceResultMsg="职业病防护设备设施与防护用品的效果评价";
        }
    }


    /**
     * <p>Description：详情页技术服务结果初始化 </p>
     * <p>Author： yzz 2025/5/20 </p>
     */
    public void viewServiceResultInit(){
        itemMap = new HashMap<>();
        this.occhethCard.setSelectedBadrsnNames(null);
        List<String> badrsnNames = new ArrayList<>();
        if((!this.ifView && "1".equals(this.selServiceResultExtends1))
                || (this.ifView && this.occhethCard.getHasBadrsnJc())){
            List<TdZwOcchethJcBadrsn> occhethJcBadrsns = this.occhethCard.getOcchethJcBadrsns();
            if (!CollectionUtils.isEmpty(occhethJcBadrsns)) {
                //按照码表排序
                SortUtil.sortCodeByField(occhethJcBadrsns, TdZwOcchethJcBadrsn.class, "getFkByBadrsnId");
                for (TdZwOcchethJcBadrsn badrsnType : occhethJcBadrsns) {
                    badrsnNames.add(badrsnType.getFkByBadrsnId().getCodeName());
                }
            }
        }else if ((!this.ifView &&  "2".equals(this.selServiceResultExtends1))
                || (this.ifView && this.occhethCard.getHasStatusPj())){
            List<TdZwOcchethPjBadrsn> occhethPjBadrsns = this.occhethCard.getOcchethPjBadrsns();
            if (!CollectionUtils.isEmpty(occhethPjBadrsns)) {
                //按照码表排序
                SortUtil.sortCodeByField(occhethPjBadrsns, TdZwOcchethPjBadrsn.class, "getFkByBadrsnId");
                for (TdZwOcchethPjBadrsn badrsnType : occhethPjBadrsns) {
                    badrsnNames.add(badrsnType.getFkByBadrsnId().getCodeName());
                }
            }
        }

        if (!CollectionUtils.isEmpty(badrsnNames)) {
            this.occhethCard.setSelectedBadrsnNames(StringUtils.list2string(badrsnNames, "，"));
        }

        //技术服务结果-职业病危害因素  selBadrsnListStr
        //已选择的职业病危害因素
        this.badrsnSubList=new ArrayList<>();
        StringBuilder selBadrsnListStr=new StringBuilder();
        List<TdZwOcchethBpBadrsn> badrsnList = this.occhethCard.getBpBadrsnList();
        if (!CollectionUtils.isEmpty(badrsnList)) {
            for (TdZwOcchethBpBadrsn badrsn : badrsnList) {
                if (badrsn.getFkByBadrsnId() == null || badrsn.getFkByBadrsnId().getRid() == null) {
                    continue;
                }
                selBadrsnListStr.append("，"+badrsn.getFkByBadrsnId().getCodeName());
                if (CollectionUtils.isEmpty(badrsn.getBadrsnSubList())) {
                    continue;
                }
                this.badrsnSubList.addAll(badrsn.getBadrsnSubList());
                for (TdZwOcchethBpSub bpSub : badrsn.getBadrsnSubList()) {
                    bpSub.setBadrsnName(bpSub.getFkByBadrsnId().getRsnCnName());
                    bpSub.setBadrsnEx1(bpSub.getFkByBadrsnId().getFkByBadrsnId().getExtendS1());
                    List<TdZwOcchethBadrsnItem> badrsnItems = bpSub.getBadrsnItems();
                    if (CollectionUtils.isEmpty(badrsnItems)) {
                        continue;
                    }
                    for (TdZwOcchethBadrsnItem badrsnItem : badrsnItems) {
                        if (badrsnItem.getFkByIndexId() != null && badrsnItem.getFkByIndexId().getRid() != null) {
                            badrsnItem.setIndexRid(badrsnItem.getFkByIndexId().getRid());
                        }
                        if (badrsnItem.getFkByItemId() != null && badrsnItem.getFkByItemId().getRid() != null) {
                            badrsnItem.setItemId(badrsnItem.getFkByItemId().getRid());
                        }
                        if (badrsnItem.getFkByMsruntId() != null && badrsnItem.getFkByMsruntId().getRid() != null) {
                            badrsnItem.setMsruntName(badrsnItem.getFkByMsruntId().getCodeName());
                        }
                        TsSimpleCode t = new TsSimpleCode();
                        t.setRid(badrsnItem.getFkByItemId().getRid());
                        if (StringUtils.isNotBlank(badrsnItem.getFkByItemId().getItemDesc())) {
                            t.setCodeName(badrsnItem.getFkByItemId().getFkByItemId().getCodeName() + "（" + badrsnItem.getFkByItemId().getItemDesc() + "）");
                        } else {
                            t.setCodeName(badrsnItem.getFkByItemId().getFkByItemId().getCodeName());
                        }
                        t.setNum(badrsnItem.getFkByItemId().getNum());
                        itemMap.put(t.getRid(), t);
                    }
                }
            }
        }
        //职业病危害因素检测结果表格数据处理
        OcchethCardBadrsnItemSort(false);

        if(selBadrsnListStr.length()>0){
            this.occhethCard.setSelBadrsnListStr(selBadrsnListStr.substring(1));
        }
    }


    /**
     * <p>Description：参与人员信息排序 </p>
     * <p>Author： yzz 2025/5/20 </p>
     */
    public void occhethCardPsnSort(){
        if(CollectionUtils.isEmpty(this.occhethCard.getOcchethCardPsnList())){
            return;
        }

        Collections.sort(this.occhethCard.getOcchethCardPsnList(), new Comparator<TdZwOcchethCardPsn>() {
            @Override
            public int compare(TdZwOcchethCardPsn o1, TdZwOcchethCardPsn o2) {
                Integer age1= o1.getFkByPsnId().getRid();
                Integer age2= o2.getFkByPsnId().getRid();
                return  age1.compareTo(age2);
            }
        });
    }
    /**
     * <p>方法描述：编辑/详情页面初始化</p>
     *
     * @MethodAuthor hsj 2025-05-20 9:21
     */
    public void publicInit() {
        //拼接资质业务范围
        pakOcchethCardItemsStr();
        this.occhethBpBadrsnSub = new TdZwOcchethBpSub();
        //初始化-技术服务结果
        this.selServiceResultExtends1 = this.occhethCard.getFkByServiceResultId() == null ? null : this.occhethCard.getFkByServiceResultId().getExtendS1();
        this.selServiceResultExtends3 = this.occhethCard.getFkByServiceResultId() == null ? null : this.occhethCard.getFkByServiceResultId().getExtendS3();

        this.occhethCard.setHasBadrsnJc(new Integer(1).equals(this.occhethCard.getIfBadrsnJc()));
        this.occhethCard.setHasStatusPj(new Integer(1).equals(this.occhethCard.getIfStatusPj()));
        this.occhethCard.setHasInstUsePj(new Integer(1).equals(this.occhethCard.getIfInstUsePj()));
        this.occhethCard.setHasJcInst(new Integer(1).equals(this.occhethCard.getIfJcInst()));
        this.occhethCard.setHasJcUse(new Integer(1).equals(this.occhethCard.getIfJcUse()));
        if((!this.ifView && "1".equals(this.selServiceResultExtends1))
                || (this.ifView && this.occhethCard.getHasBadrsnJc())){
            this.occhethCard.setJcPostNum(ObjectUtil.convert(Integer.class, this.occhethCard.getJcPostNum(), 0));
            this.occhethCard.setJcOverNum(ObjectUtil.convert(Integer.class, this.occhethCard.getJcOverNum(), 0));
        }
        if((!this.ifView && "2".equals(this.selServiceResultExtends1))
                || (this.ifView && this.occhethCard.getHasStatusPj())){
            this.occhethCard.setPjPostNum(ObjectUtil.convert(Integer.class, this.occhethCard.getPjPostNum(), 0));
            this.occhethCard.setPjOverNum(ObjectUtil.convert(Integer.class, this.occhethCard.getPjOverNum(), 0));
        }
    }

    /**
     * <p>方法描述：编辑页面技术服务结果处理</p>
     *
     * @MethodAuthor hsj 2025-05-20 11:05
     */
    public void packServiceResults() {
        publicInit();
        packBadRsn();
    }

    /**
     * <p>方法描述：编辑页面-技术服务结果初始化-启用</p>
     *
     * @MethodAuthor hsj 2025-05-20 9:47
     */
    public void packBadRsn() {
        //超标危害因素类型
        this.occhethCard.setSelectedBadrsnIds(null);
        this.occhethCard.setSelectedBadrsnNames(null);
        List<Integer> badrsnIds = new ArrayList<>();
        List<String> badrsnNames = new ArrayList<>();
        if("1".equals(this.selServiceResultExtends1)){
            List<TdZwOcchethJcBadrsn> occhethJcBadrsns = this.occhethCard.getOcchethJcBadrsns();
            if (!CollectionUtils.isEmpty(occhethJcBadrsns)) {
                //按照码表排序
                SortUtil.sortCodeByField(occhethJcBadrsns, TdZwOcchethJcBadrsn.class, "getFkByBadrsnId");
                for (TdZwOcchethJcBadrsn badrsnType : occhethJcBadrsns) {
                    badrsnNames.add(badrsnType.getFkByBadrsnId().getCodeName());
                    Integer badrsnId = badrsnType.getFkByBadrsnId().getRid();
                    if (!this.simpleCodeMap.containsKey(Convert.toStr(badrsnId))) {
                        continue;
                    }
                    badrsnIds.add(badrsnId);
                }
            }
        }else if ("2".equals(this.selServiceResultExtends1)){
            List<TdZwOcchethPjBadrsn> occhethPjBadrsns = this.occhethCard.getOcchethPjBadrsns();
            if (!CollectionUtils.isEmpty(occhethPjBadrsns)) {
                //按照码表排序
                SortUtil.sortCodeByField(occhethPjBadrsns, TdZwOcchethPjBadrsn.class, "getFkByBadrsnId");
                for (TdZwOcchethPjBadrsn badrsnType : occhethPjBadrsns) {
                    badrsnNames.add(badrsnType.getFkByBadrsnId().getCodeName());
                    Integer badrsnId = badrsnType.getFkByBadrsnId().getRid();
                    if (!this.simpleCodeMap.containsKey(Convert.toStr(badrsnId))) {
                        continue;
                    }
                    badrsnIds.add(badrsnId);
                }
            }
        }
        if (!CollectionUtils.isEmpty(badrsnIds)) {
            this.occhethCard.setSelectedBadrsnIds(badrsnIds);
        }
        if (!CollectionUtils.isEmpty(badrsnNames)) {
            this.occhethCard.setSelectedBadrsnNames(StringUtils.list2string(badrsnNames, "，"));
        }
        processOccupationalHazardFactors();
    }
    /**
     *  <p>方法描述：职业病危害因素检测结果的处理</p>
     * @MethodAuthor hsj 2025-05-23 14:15
     */
    public void processOccupationalHazardFactors() {
        this.itemMap = new HashMap<>();
        //已选择的职业病危害因素
        this.occhethCard.setSelBadrsnList(new ArrayList<String>());
        this.badrsnSubList = new ArrayList<>();
        List<TdZwOcchethBpBadrsn> badrsnList = this.occhethCard.getBpBadrsnList();
        if (CollectionUtils.isEmpty(badrsnList)) {
            return;
        }
        for (TdZwOcchethBpBadrsn badrsn : this.occhethCard.getBpBadrsnList()) {
            if (badrsn.getFkByBadrsnId() == null || badrsn.getFkByBadrsnId().getRid() == null) {
                continue;
            }
            if (!this.simpleCodeMap.containsKey(Convert.toStr(badrsn.getFkByBadrsnId().getRid()))) {
                continue;
            }
            this.occhethCard.getSelBadrsnList().add(Convert.toStr(badrsn.getFkByBadrsnId().getRid()));
            if (CollectionUtils.isEmpty(badrsn.getBadrsnSubList())) {
                continue;
            }
            this.badrsnSubList.addAll(badrsn.getBadrsnSubList());
            for (TdZwOcchethBpSub bpSub : badrsn.getBadrsnSubList()) {
                bpSub.setBadrsnName(bpSub.getFkByBadrsnId().getRsnCnName());
                bpSub.setBadrsnEx1(bpSub.getFkByBadrsnId().getFkByBadrsnId().getExtendS1());
                List<TdZwOcchethBadrsnItem> badrsnItems = bpSub.getBadrsnItems();
                if (CollectionUtils.isEmpty(badrsnItems)) {
                    continue;
                }
                for (TdZwOcchethBadrsnItem badrsnItem : badrsnItems) {
                    if (badrsnItem.getFkByIndexId() != null && badrsnItem.getFkByIndexId().getRid() != null) {
                        badrsnItem.setIndexRid(badrsnItem.getFkByIndexId().getRid());
                    }
                    if (badrsnItem.getFkByItemId() != null && badrsnItem.getFkByItemId().getRid() != null) {
                        badrsnItem.setItemId(badrsnItem.getFkByItemId().getRid());
                    }
                    if (badrsnItem.getFkByMsruntId() != null && badrsnItem.getFkByMsruntId().getRid() != null) {
                        badrsnItem.setMsruntName(badrsnItem.getFkByMsruntId().getCodeName());
                    }
                    TsSimpleCode t = new TsSimpleCode();
                    t.setRid(badrsnItem.getFkByItemId().getRid());
                    if (StringUtils.isNotBlank(badrsnItem.getFkByItemId().getItemDesc())) {
                        t.setCodeName(badrsnItem.getFkByItemId().getFkByItemId().getCodeName() + "（" + badrsnItem.getFkByItemId().getItemDesc() + "）");
                    } else {
                        t.setCodeName(badrsnItem.getFkByItemId().getFkByItemId().getCodeName());
                    }
                    t.setNum(badrsnItem.getFkByItemId().getNum());
                    itemMap.put(t.getRid(), t);
                }
            }
        }
        //职业病危害因素检测结果表格数据处理
        OcchethCardBadrsnItemSort(true);
    }
    /**
     * <p>方法描述：超标危害因素类型与--启用的（一定要在计算合并行之前调用）</p>
     *
     * @MethodAuthor hsj 2025-05-20 8:34
     */
    public void initOcchethBadrsnType() {
        this.occhethCard.setSelectedBadrsnNames(null);
        this.occhethCard.setSelectedBadrsnIds(null);
        Integer postNum = 0;
        Integer overNum = 0;
        if (CollectionUtils.isEmpty(this.badrsnSubList)) {
            this.setPostAndOverNumbers(postNum,overNum);
            return;
        }
        Set<Integer> badrsnIdList = new HashSet<>();
        for (TdZwOcchethBpSub bpSub : this.badrsnSubList) {
            if(ObjectUtil.isNotNull(bpSub.getOverPostNum()) && bpSub.getOverPostNum() > 0){
                badrsnIdList.add(bpSub.getFkByBadrsnId().getFkByBadrsnId().getRid());
            }
            postNum += Convert.toInt(bpSub.getPostNum(),0);
            overNum += Convert.toInt(bpSub.getOverPostNum(),0);
        }
        this.setPostAndOverNumbers(postNum,overNum);
        List<TsSimpleCode> badrsnTypeList = new ArrayList<>();
        for (Integer rid : badrsnIdList) {
            if (!this.simpleCodeMap.containsKey(Convert.toStr(rid))) {
                continue;
            }
            badrsnTypeList.add(this.simpleCodeMap.get(Convert.toStr(rid)));
        }
        if (CollectionUtils.isEmpty(badrsnTypeList)) {
            return;
        }
        sortSimpleCodeList(badrsnTypeList);
        List<Integer> selectedBadrsnIds = new ArrayList<>();
        List<String> selectedBadrsnNames = new ArrayList<>();
        for (TsSimpleCode tsSimpleCode : badrsnTypeList) {
            selectedBadrsnIds.add(tsSimpleCode.getRid());
            selectedBadrsnNames.add(tsSimpleCode.getCodeName());
        }
        this.occhethCard.setSelectedBadrsnNames(StringUtils.list2string(selectedBadrsnNames, "，"));
        this.occhethCard.setSelectedBadrsnIds(selectedBadrsnIds);
    }
    /**
     *  <p>方法描述：报送卡主表中-检测岗位数与超标检测岗位数赋值</p>
     * @MethodAuthor hsj 2025-05-23 10:06
     */
    private void setPostAndOverNumbers(Integer postNum, Integer overNum) {
        this.occhethCard.setJcPostNum(null);
        this.occhethCard.setJcOverNum(null);
        this.occhethCard.setPjPostNum(null);
        this.occhethCard.setPjOverNum(null);
        if ("1".equals(selServiceResultExtends1)) {
            this.occhethCard.setJcPostNum(postNum);
            this.occhethCard.setJcOverNum(overNum);
        } else if ("2".equals(selServiceResultExtends1)) {
            this.occhethCard.setPjPostNum(postNum);
            this.occhethCard.setPjOverNum(overNum);
        }
    }
    /**
     * <p>方法描述：码表排序</p>
     *
     * @MethodAuthor hsj 2025-05-19 10:57
     */
    public void sortSimpleCodeList(List<TsSimpleCode> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Collections.sort(list, new Comparator<TsSimpleCode>() {
            @Override
            public int compare(TsSimpleCode o1, TsSimpleCode o2) {
                if (ObjectUtil.isNotNull(o1.getNum()) && ObjectUtil.isNotNull(o2.getNum())) {
                    int i = o1.getNum().compareTo(o2.getNum());
                    if (i == 0) {
                        return o1.getCodeNo().compareTo(o2.getCodeNo());
                    }
                    return i;
                } else if (ObjectUtil.isNotNull(o1.getNum())) {
                    return -1;
                } else if (ObjectUtil.isNotNull(o2.getNum())) {
                    return 1;
                } else {
                    return o1.getCodeNo().compareTo(o2.getCodeNo());
                }
            }
        });
    }

    /**
     * <p>方法描述：职业病危害因素检测结果表格数据处理</p>
     *
     * @MethodAuthor hsj 2025-05-17 14:18
     */
    public void OcchethCardBadrsnItemSort(boolean flag) {
        if (CollectionUtils.isEmpty(this.badrsnSubList)) {
            return;
        }
        //按照危害因素大类码表后危害因素维护的序号排序
        Collections.sort(this.badrsnSubList, new Comparator<TdZwOcchethBpSub>() {
            @Override
            public int compare(TdZwOcchethBpSub o1, TdZwOcchethBpSub o2) {
                Integer num1 = o1.getFkByBadrsnId().getNum();
                Integer num2 = o2.getFkByBadrsnId().getNum();
                if (null != num1 && null != num2) {
                    return num1.compareTo(num2);
                }
                return 0;
            }
        });
        for (TdZwOcchethBpSub badrsnSub : this.badrsnSubList) {
            List<TdZwOcchethBadrsnItem> badrsnItems = badrsnSub.getBadrsnItems();
            if (CollectionUtils.isEmpty(badrsnItems)) {
                continue;
            }
            for (TdZwOcchethBadrsnItem bpBadrsnItem : badrsnSub.getBadrsnItems()) {
                if (null == bpBadrsnItem.getFkByItemId() || null == bpBadrsnItem.getFkByItemId().getRid()) {
                    bpBadrsnItem.setItemNumber(-1);
                } else if (itemMap.containsKey(bpBadrsnItem.getFkByItemId().getRid())) {
                    bpBadrsnItem.setItemNumber(itemMap.get(bpBadrsnItem.getFkByItemId().getRid()).getNum());
                }
                if (null == bpBadrsnItem.getIndexRid()) {
                    bpBadrsnItem.setIndexNumber(-1);
                } else if (indexMap.containsKey(bpBadrsnItem.getIndexRid())) {
                    bpBadrsnItem.setIndexNumber(indexMap.get(bpBadrsnItem.getIndexRid()).getNum());
                }
            }
            Collections.sort(badrsnItems, new Comparator<TdZwOcchethBadrsnItem>() {
                @Override
                public int compare(TdZwOcchethBadrsnItem o1, TdZwOcchethBadrsnItem o2) {
                    String postName1 = StringUtils.objectToString(o1.getPostName());
                    String postName2 = StringUtils.objectToString(o2.getPostName());

                    String itemNum1 = StringUtils.objectToString(o1.getItemNumber());
                    String itemNum2 = StringUtils.objectToString(o2.getItemNumber());

                    String indexNum1 = StringUtils.objectToString(o1.getIndexNumber());
                    String indexNum2 = StringUtils.objectToString(o2.getIndexNumber());

                    if (!postName1.equals(postName2)) {
                        return postName1.compareTo(postName2);
                    } else if (!itemNum1.equals(itemNum2)) {
                        return itemNum1.compareTo(itemNum2);
                    } else if (!indexNum1.equals(indexNum2)) {
                        return indexNum1.compareTo(indexNum2);
                    }
                    return 0;
                }
            });
        }
        //合并行的计算
        Map<String, Integer> countRidMap = new HashMap<>();
        Map<String, Integer> countPostMap = new HashMap<>();
        Map<String, Integer> countItemMap = new HashMap<>();
        Set<String> ridSet = new HashSet<>();
        for (TdZwOcchethBpSub badrsnSub : this.badrsnSubList) {
            if (!CollectionUtils.isEmpty(badrsnSub.getBadrsnItems()) && badrsnSub.getBadrsnItems().size() > 1) {
                //存在数据且大于1
                for (TdZwOcchethBadrsnItem badgeItem : badrsnSub.getBadrsnItems()) {
                    if (countRidMap.get(badrsnSub.getFkByBadrsnId().getRid().toString()) == null) {
                        countRidMap.put(badrsnSub.getFkByBadrsnId().getRid().toString(), 1);
                    } else {
                        countRidMap.put(badrsnSub.getFkByBadrsnId().getRid().toString(), countRidMap.get(badrsnSub.getFkByBadrsnId().getRid().toString()) + 1);
                    }
                    if (countPostMap.get(badrsnSub.getFkByBadrsnId().getRid() + "&" + badgeItem.getPostName()) == null) {
                        countPostMap.put(badrsnSub.getFkByBadrsnId().getRid() + "&" + badgeItem.getPostName(), 1);
                    } else {
                        countPostMap.put(badrsnSub.getFkByBadrsnId().getRid() + "&" + badgeItem.getPostName(), countPostMap.get(badrsnSub.getFkByBadrsnId().getRid() + "&" + badgeItem.getPostName()) + 1);
                    }
                    if (countItemMap.get(badrsnSub.getFkByBadrsnId().getRid() + "&" + badgeItem.getPostName() + "&" + badgeItem.getFkByItemId().getRid()) == null) {
                        countItemMap.put(badrsnSub.getFkByBadrsnId().getRid() + "&" + badgeItem.getPostName() + "&" + badgeItem.getFkByItemId().getRid(), 1);
                    } else {
                        countItemMap.put(badrsnSub.getFkByBadrsnId().getRid() + "&" + badgeItem.getPostName() + "&" + badgeItem.getFkByItemId().getRid(), countItemMap.get(badrsnSub.getFkByBadrsnId().getRid() + "&" + badgeItem.getPostName() + "&" + badgeItem.getFkByItemId().getRid()) + 1);
                    }
                    if (ridSet.add(badrsnSub.getFkByBadrsnId().getRid().toString())) {
                        badgeItem.setBadrsnName(badrsnSub.getFkByBadrsnId().getRsnCnName());
                        badgeItem.setBadrsnNum(badrsnSub.getBadrsnNum());
                        badgeItem.setNum(badrsnSub.getOverPostNum());
                        badgeItem.setPostNum(badrsnSub.getPostNum());
                        badgeItem.setBadrsnRowspan(badrsnSub.getBadrsnItems().size());
                    } else {
                        badgeItem.setBadrsnName(null);
                        badgeItem.setPostNum(null);
                        badgeItem.setNum(null);
                        badgeItem.setBadrsnNum(null);
                    }
                    if (StringUtils.isNotBlank(badgeItem.getPostName()) && badgeItem.getFkByItemId() != null && badgeItem.getFkByItemId().getRid() != null) {
                        if (ridSet.add(badrsnSub.getFkByBadrsnId().getRid().toString() + "&" + badgeItem.getPostName() + "&" + badgeItem.getFkByItemId().getRid())) {
                            badgeItem.setItermName(itemMap.get(badgeItem.getFkByItemId().getRid()).getCodeName());
                            badgeItem.getFkByItemId().setFkByItemId(new TsSimpleCode("", itemMap.get(badgeItem.getFkByItemId().getRid()).getCodeName()));
                        } else {
                            badgeItem.setItermName(null);
                        }
                    } else {
                        badgeItem.setItermName(null);
                    }
                    if(flag){
                        badgeItem.setFkByIndexId(indexMap.get(badgeItem.getIndexRid()));
                    }else{
                        badgeItem.setFkByIndexId(indexAllMap.get(badgeItem.getIndexRid()));
                    }
                }
            } else {
                if (CollectionUtils.isEmpty(badrsnSub.getBadrsnItems())) {
                    TdZwOcchethBadrsnItem badrsnItem = new TdZwOcchethBadrsnItem();
                    badrsnItem.setIfFirst(true);
                    badrsnItem.setBadrsnName(badrsnSub.getFkByBadrsnId().getRsnCnName());
                    badrsnItem.setFkByMainId(badrsnSub);
                    badrsnItem.setFkByItemId(new TbYsjcRsnRelItemComm());
                    badrsnItem.setFkByIndexId(new TsSimpleCode());
                    badrsnItem.setFkByMsruntId(new TsSimpleCode());
                    List<TdZwOcchethBadrsnItem> badrsnSubsns = new ArrayList<>();
                    badrsnSubsns.add(badrsnItem);
                    badrsnSub.setBadrsnItems(badrsnSubsns);
                }
                if (badrsnSub.getPostNum() != null) {
                    badrsnSub.getBadrsnItems().get(0).setPostNum(badrsnSub.getPostNum());
                }
                badrsnSub.getBadrsnItems().get(0).setBadrsnName(badrsnSub.getFkByBadrsnId().getRsnCnName());
                if (StringUtils.isNotBlank(badrsnSub.getBadrsnItems().get(0).getPostName())) {
                    badrsnSub.getBadrsnItems().get(0).setNum(badrsnSub.getOverPostNum());
                    badrsnSub.getBadrsnItems().get(0).setBadrsnNum(badrsnSub.getBadrsnNum());
                    badrsnSub.getBadrsnItems().get(0).getFkByItemId().setFkByItemId(new TsSimpleCode("", itemMap.get(badrsnSub.getBadrsnItems().get(0).getFkByItemId().getRid()).getCodeName()));
                    badrsnSub.getBadrsnItems().get(0).setItermName(itemMap.get(badrsnSub.getBadrsnItems().get(0).getFkByItemId().getRid()).getCodeName());
                    if(flag){
                        badrsnSub.getBadrsnItems().get(0).setFkByIndexId(indexMap.get(badrsnSub.getBadrsnItems().get(0).getIndexRid()));
                    }else{
                        badrsnSub.getBadrsnItems().get(0).setFkByIndexId(indexAllMap.get(badrsnSub.getBadrsnItems().get(0).getIndexRid()));
                    }
                } else {
                    badrsnSub.getBadrsnItems().get(0).setItermName("");
                    badrsnSub.getBadrsnItems().get(0).setNum(badrsnSub.getOverPostNum());
                    badrsnSub.getBadrsnItems().get(0).setBadrsnNum(badrsnSub.getBadrsnNum());
                    badrsnSub.getBadrsnItems().get(0).setPostNameShow("");
                }
                badrsnSub.getBadrsnItems().get(0).setBadrsnRowspan(1);
                badrsnSub.getBadrsnItems().get(0).setItemsRowspan(1);
                badrsnSub.getBadrsnItems().get(0).setPostRowspan(1);
            }
        }
        Set<String> ridsSet = new HashSet<>();
        for (TdZwOcchethBpSub badrsnSub : this.badrsnSubList) {
            if (!CollectionUtils.isEmpty(badrsnSub.getBadrsnItems()) && badrsnSub.getBadrsnItems().size() > 1) {
                for (TdZwOcchethBadrsnItem badrsnItem : badrsnSub.getBadrsnItems()) {
                    if (!countItemMap.isEmpty() && countItemMap.get(badrsnSub.getFkByBadrsnId().getRid() + "&" + badrsnItem.getPostName() + "&" + badrsnItem.getFkByItemId().getRid()) != null && ridsSet.add(badrsnSub.getFkByBadrsnId().getRid() + "&" + badrsnItem.getPostName() + "&" + badrsnItem.getFkByItemId().getRid())) {
                        badrsnItem.setItemsRowspan(countItemMap.get(badrsnSub.getFkByBadrsnId().getRid() + "&" + badrsnItem.getPostName() + "&" + badrsnItem.getFkByItemId().getRid()));
                    } else {
                        badrsnItem.setItermName(null);
                    }
                    if (!countPostMap.isEmpty() && countPostMap.get(badrsnSub.getFkByBadrsnId().getRid() + "&" + badrsnItem.getPostName()) != null && ridsSet.add(badrsnSub.getFkByBadrsnId().getRid() + "&" + badrsnItem.getPostName())) {
                        badrsnItem.setPostRowspan(countPostMap.get(badrsnSub.getFkByBadrsnId().getRid() + "&" + badrsnItem.getPostName()));
                    } else {
                        badrsnItem.setPostNameShow(null);
                    }
                }
            }
        }
    }

    ;


    /**
     * 拼接资质业务范围
     */
    public void pakOcchethCardItemsStr() {
        List<String> occhethItemsCodeNameList = new ArrayList<>();
        for (TdZwOcchethCardItems occhethCardItems : this.occhethCard.getOcchethCardItemsList()) {
            occhethItemsCodeNameList.add(occhethCardItems.getFkByItemId().getCodeName());
        }
        this.occhethCard.setOcchethCardItemsStr(StringUtils.list2string(occhethItemsCodeNameList, "，"));
    }



    @Override
    public void saveAction() {
    }


    /**
     * <p>方法描述：签发页文书上传前验证</p>
     *
     * @MethodAuthor hsj 2024-08-27 16:01
     */
    public void beforeSignUpload() {
        RequestContext.getCurrentInstance().execute("PF('FileSignDialog').show();");
        RequestContext.getCurrentInstance().update("tabView:editForm:writeSortPanel");
    }

    /**
     * <p>方法描述：签发页文书上传</p>
     *
     * @MethodAuthor hsj 2024-08-27 16:01
     */
    public void fileSingUpload(FileUploadEvent event) {
        if (null == event) {
            return;
        }
        UploadedFile file = event.getFile();
        try {
            String fileName = file.getFileName();
            String errorMsg = FileUtils.veryFile(file.getInputstream(), file.getContentType(), fileName, "2");
            if (StringUtils.isNotBlank(errorMsg)) {
                JsfUtil.addErrorMessage(errorMsg);
                return;
            }
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            String path = JsfUtil.getAbsolutePath();
            String relativePath = "heth/comm/reportcard/" + uuid + fileName.substring(fileName.lastIndexOf("."));
            // 文件路径
            String filePath = path + relativePath;
            this.occhethCard.setSignAddress(relativePath);
            FileUtils.copyFile(filePath, file.getInputstream());
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("PF('FileSignDialog').hide();");
            currentInstance.update("tabView:editForm:writeSortPanel");
            JsfUtil.addSuccessMessage("上传成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("上传失败！");
            throw new RuntimeException(e);
        }
    }

    /**
     * <p>方法描述：删除签发页文书</p>
     *
     * @MethodAuthor hsj 2024-08-27 16:02
     */
    public void delSignwrit() {
        try {
            this.occhethCard.setSignAddress(null);
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * <p>方法描述：下载二维码前校验及生成 </p>
     * pw 2025/5/22
     **/
    public void beforeDownloadQrCode() {
        if (!this.generateQrCodeInfo()) {
            return;
        }
        if(StringUtils.isBlank(this.occhethCard.getQrCodePath())) {
            SystemMessageEnum.DOWNLOAD_FAIL.showMessage();
            return;
        }
        this.exeDownloadQrCode();
        if (0 == this.occhethCard.getState() && StringUtils.isNotBlank(this.occhethCard.getAnnexPath())) {
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("disabledInput('true','occHethCard')");
        }
    }

    /**
     * <p>方法描述：执行下载二维码 子类需重写 </p>
     * pw 2025/5/23
     **/
    public void exeDownloadQrCode() {}

    /**
     * <p>方法描述：下载二维码 </p>
     * pw 2025/5/22
     **/
    public StreamedContent getQRCodeStreamedContent() {
        String filePath = this.occhethCard.getQrCodePath();
        String fileName = (null == this.occhethCard.getCrptName() ? "" : this.occhethCard.getCrptName()+"-")+(null == this.occhethCard.getCheckNo() ? "" : this.occhethCard.getCheckNo())+".png";
        try {
            String xnPath = JsfUtil.getAbsolutePath();
            String path="";
            if(filePath.indexOf(xnPath) != -1){
                path = filePath;
            }else{
                path = xnPath + filePath;
            }
            InputStream stream = new FileInputStream(path);
            return new DefaultStreamedContent(stream, "image/png", URLEncoder.encode(fileName, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            SystemMessageEnum.FILE_NOT_EXIST.showMessage();
        }
        return null;
    }

    /**
     * <p>方法描述：编辑页重写的方法 </p>
     * pw 2025/5/23
     **/
    public boolean updateQrCodeInfo() {
        return true;
    }

    /**
     * <p>方法描述：生成二维码校验 </p>
     * pw 2025/5/22
     **/
    public boolean validateQrCodeInfo(boolean ifSave) {
        boolean flag = true;
        if (null == this.occhethCard.getFkByCrptId() || null == this.occhethCard.getFkByCrptId().getRid() ||
                StringUtils.isBlank(this.occhethCard.getCrptName())) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("用人单位信息");
            flag = false;
        }
        if (null == this.occhethCard.getRptDate()) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("出具技术服务报告时间");
            flag = false;
        }
        if (StringUtils.isBlank(this.occhethCard.getRptNo())) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("技术服务报告编号");
            flag = false;
        }
        // 质控编号 存储前会自动生成
        if (!ifSave && StringUtils.isBlank(this.occhethCard.getCheckNo())) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("质控编号");
            flag = false;
        }
        return flag;
    }

    /**
     * <p>方法描述：生成二维码文件 </p>
     * pw 2025/5/22
     **/
    public String generateQrCode() {
        String orgName = this.occhethCard.getOrgName();
        String crptName = this.occhethCard.getCrptName();
        String rptNo = this.occhethCard.getRptNo();
        Date rptDate = this.occhethCard.getRptDate();
        String serviceResultName = null == this.occhethCard.getFkByServiceResultId() ? null : this.occhethCard.getFkByServiceResultId().getCodeName();
        String checkNo = this.occhethCard.getCheckNo();
        Map<String, String> entityMap = new HashMap<>();
        entityMap.put("orgName", null == orgName ? "" : orgName);
        entityMap.put("crptName", null == crptName ? "" : crptName);
        entityMap.put("rptNo", null == rptNo ? "" : rptNo);
        entityMap.put("rptDate",null == rptDate ? "" : DateUtils.formatDate(rptDate));
        entityMap.put("serviceResultName", null == serviceResultName ? "" : serviceResultName);
        entityMap.put("checkNo", null == checkNo ? "" : checkNo);
        try {
            String jsonStr = SpecSm4Utils.sm4JsEncrypt(JSONObject.toJSONString(entityMap));
            String qrPicPath = "heth/comm/reportcard/qrCode/";
            qrPicPath = qrPicPath+StringUtils.uuid()+".png";
            QRCode.encode(jsonStr, JsfUtil.getAbsolutePath()+qrPicPath, 200, 200, 1);
            return qrPicPath;
        } catch(Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * <p>方法描述：详情页时存储二维码 </p>
     * pw 2025/5/23
     **/
    private boolean generateAndSaveQrCode() {
        String qrCode = this.generateQrCode();
        if (StringUtils.isBlank(qrCode)) {
            return false;
        }
        try {
            this.occhethCardService.updateOccHethCardQrCodePath(qrCode, this.occhethCard.getRid());
            this.occhethCard.setQrCodePath(qrCode);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * <p>方法描述：校验、生成二维码 </p>
     * pw 2025/5/22
     **/
    public boolean generateQrCodeInfo() {
        Integer state = this.occhethCard.getState();
        Integer source = this.occhethCard.getSource();
        if (null == source || 2 != source) {
            return false;
        }
        //  已经提交的 没有二维码 生成
        if (null != state && 1 == state && StringUtils.isBlank(this.occhethCard.getQrCodePath())) {
            if (validateQrCodeInfo(true)) {
                if (!this.generateAndSaveQrCode()) {
                    SystemMessageEnum.DOWNLOAD_FAIL.showMessage();
                    return false;
                }
            }
        }
        // 已经提交的 有二维码
        if (null != state && 1 == state && StringUtils.isNotBlank(this.occhethCard.getQrCodePath())) {
            return true;
        }
        // 没有提交的 编辑页 每次都生成
        if (!this.updateQrCodeInfo()) {
            return false;
        }
        return true;
    }

    public Integer getMainRid() {
        return mainRid;
    }

    public void setMainRid(Integer mainRid) {
        this.mainRid = mainRid;
    }

    public TdZwOcchethCard getOcchethCard() {
        return occhethCard;
    }

    public void setOcchethCard(TdZwOcchethCard occhethCard) {
        this.occhethCard = occhethCard;
    }

    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public String getSearchZoneGb() {
        return searchZoneGb;
    }

    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public Date getSearchRptBeginDate() {
        return searchRptBeginDate;
    }

    public void setSearchRptBeginDate(Date searchRptBeginDate) {
        this.searchRptBeginDate = searchRptBeginDate;
    }

    public Date getSearchRptEndDate() {
        return searchRptEndDate;
    }

    public void setSearchRptEndDate(Date searchRptEndDate) {
        this.searchRptEndDate = searchRptEndDate;
    }

    public List<Integer> getSearchStateList() {
        return searchStateList;
    }

    public void setSearchStateList(List<Integer> searchStateList) {
        this.searchStateList = searchStateList;
    }

    public Map<String, TsSimpleCode> getSimpleCodeMap() {
        return simpleCodeMap;
    }

    public void setSimpleCodeMap(Map<String, TsSimpleCode> simpleCodeMap) {
        this.simpleCodeMap = simpleCodeMap;
    }

    public List<TsSimpleCode> getBusinessScopeList() {
        return businessScopeList;
    }

    public void setBusinessScopeList(List<TsSimpleCode> businessScopeList) {
        this.businessScopeList = businessScopeList;
    }

    public Boolean getIfSupportCancel() {
        return ifSupportCancel;
    }

    public void setIfSupportCancel(Boolean ifSupportCancel) {
        this.ifSupportCancel = ifSupportCancel;
    }


    public Map<Integer, TsSimpleCode> getItemMap() {
        return itemMap;
    }

    public void setItemMap(Map<Integer, TsSimpleCode> itemMap) {
        this.itemMap = itemMap;
    }


    public Map<Integer, TsSimpleCode> getIndexMap() {
        return indexMap;
    }

    public void setIndexMap(Map<Integer, TsSimpleCode> indexMap) {
        this.indexMap = indexMap;
    }

    public List<TsSimpleCode> getIndexList() {
        return indexList;
    }

    public void setIndexList(List<TsSimpleCode> indexList) {
        this.indexList = indexList;
    }

    public List<TsSimpleCode> getParentBadRsnList() {
        return parentBadRsnList;
    }

    public void setParentBadRsnList(List<TsSimpleCode> parentBadRsnList) {
        this.parentBadRsnList = parentBadRsnList;
    }

    public String getSelServiceResultExtends1() {
        return selServiceResultExtends1;
    }

    public void setSelServiceResultExtends1(String selServiceResultExtends1) {
        this.selServiceResultExtends1 = selServiceResultExtends1;
    }

    public Map<String, TsSimpleCode> getSimpleCodeEx1Map() {
        return simpleCodeEx1Map;
    }

    public void setSimpleCodeEx1Map(Map<String, TsSimpleCode> simpleCodeEx1Map) {
        this.simpleCodeEx1Map = simpleCodeEx1Map;
    }

    public TdZwOcchethBpSub getOcchethBpBadrsnSub() {
        return occhethBpBadrsnSub;
    }

    public void setOcchethBpBadrsnSub(TdZwOcchethBpSub occhethBpBadrsnSub) {
        this.occhethBpBadrsnSub = occhethBpBadrsnSub;
    }

    public TdZwOcchethBadrsnItem getOcchethBadrsnItem() {
        return occhethBadrsnItem;
    }

    public void setOcchethBadrsnItem(TdZwOcchethBadrsnItem occhethBadrsnItem) {
        this.occhethBadrsnItem = occhethBadrsnItem;
    }

    public List<TdZwOcchethBpSub> getBadrsnSubList() {
        return badrsnSubList;
    }

    public void setBadrsnSubList(List<TdZwOcchethBpSub> badrsnSubList) {
        this.badrsnSubList = badrsnSubList;
    }
    public List<Object[]> getAppClockList() {
        return appClockList;
    }
    public void setAppClockList(List<Object[]> appClockList) {
        this.appClockList = appClockList;
    }

    public Integer getAnnexViewIndex() {
        return annexViewIndex;
    }
    public void setAnnexViewIndex(Integer annexViewIndex) {
        this.annexViewIndex = annexViewIndex;
    }
    public Object[] getContractAnnex() {
        return contractAnnex;
    }
    public void setContractAnnex(Object[] contractAnnex) {
        this.contractAnnex = contractAnnex;
    }
    public FilePojoNew getFileTemp() {
        return fileTemp;
    }
    public void setFileTemp(FilePojoNew fileTemp) {
        this.fileTemp = fileTemp;
    }

    public boolean getIfView() {
        return ifView;
    }
    public void setIfView(boolean ifView) {
        this.ifView = ifView;
    }

    public String getServiceResultMsg() {
        return serviceResultMsg;
    }
    public void setServiceResultMsg(String serviceResultMsg) {
        this.serviceResultMsg = serviceResultMsg;
    }

    public String getSelServiceResultExtends3() {
        return selServiceResultExtends3;
    }

    public void setSelServiceResultExtends3(String selServiceResultExtends3) {
        this.selServiceResultExtends3 = selServiceResultExtends3;
    }
}