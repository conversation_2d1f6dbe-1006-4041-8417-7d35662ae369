package com.chis.modules.heth.comm.web;

import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.rptvo.PlaceInfo;
import com.chis.modules.heth.comm.rptvo.UnitbasicInfo;
import com.chis.modules.heth.comm.service.GsCrptIntegratedShowService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.utils.HolidayUtil;
import com.chis.modules.system.web.FacesEditBean;
import com.github.abel533.echarts.Grid;
import com.github.abel533.echarts.axis.CategoryAxis;
import com.github.abel533.echarts.axis.SplitLine;
import com.github.abel533.echarts.axis.ValueAxis;
import com.github.abel533.echarts.code.*;
import com.github.abel533.echarts.data.Data;
import com.github.abel533.echarts.json.GsonOption;
import com.github.abel533.echarts.series.Bar;
import com.github.abel533.echarts.series.Pie;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.*;

/**
 * 用人单位综合展示
 *
 * <AUTHOR>
 * @version 1.1
 */
@ManagedBean(name = "gsCrptIntegratedShowListBean")
@ViewScoped
public class GsCrptIntegratedShowListBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final GsCrptIntegratedShowService crptIntegratedShowService =
            SpringContextHolder.getBean(GsCrptIntegratedShowService.class);
    /**
     * 用人单位RID
     */
    private Integer rid;
    /**
     * 选择的结果集
     */
    private List<Object[]> selectEntitys;
    /**
     * 查询条件：地区
     */
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
    /**
     * 查询条件：单位名称
     */
    private String searchCrptName;
    /**
     * 查询条件：社会信用代码
     */
    private String searchInstitutionCode;
    /**
     * 查询条件：经济类型
     */
    private String selectEconomyIds;
    private String selectEconomyNames;
    private List<TsSimpleCode> economyList;
    /**
     * 最末级经济类型码表rid集合
     */
    private List<Integer> validateEconomyIds;
    /**
     * 查询条件：企业规模
     */
    private String selectCrptSizeIds;
    private List<TsSimpleCode> crptSizeList;
    /**
     * 当前操作码表类型
     */
    private String simpleCodeOpType;
    /**
     * 查询条件：行业类别
     */
    private String selectIndusTypeIds;
    private String selectIndusTypeNames;
    private List<TsSimpleCode> indusList;
    /**
     * 最末级行业类别码表rid集合
     */
    private List<Integer> validateIndusTypeIds;
    /**
     * 查询条件：职业病危害因素
     */
    private String selectBadRsnIds;
    private String selectBadRsnNames;
    private List<TsSimpleCode> badRsnList;
    /**
     * 查询条件：启用状态
     */
    private String[] enabledState;
    /**
     * 查询条件：修订状态
     */
    private String[] fixState;

    /**
     * 查询条件：异常信息
     */
    private String selectAbnormalInfoIds;
    private List<TsSimpleCode> abnormalInfoList;
    /**
     * 是否有修订权限
     */
    private Boolean hasFix;
    private String stopRsn;
    /**
     * 修订的企业
     */
    private List<TbTjCrptVO> fixCrptList;
    /**
     * 修订类型
     */
    private Integer changeType;
    /**
     * 修订下标
     */
    private Integer changeIndex;
    /**
     * 详情页面：基本信息
     */
    private Object[] crptBaseInfo;
    /**
     * 职业风险分类码表拓展字段1对应码表名称
     */
    private Map<String, String> occRiskClassMap;
    /**
     * 职业病危害因素弹出框数据
     */
    private List<Object[]> occRiskClassList;
    /**
     * 展示职业健康管理档案详情<pre>1:在线申报档案</pre><pre>2:场所监测档案</pre><pre>其他:不展示</pre>
     */
    private Integer showArchives;
    /**
     * 是否当年无申报数据
     */
    private Boolean hasDeclareDataForTheYear;
    /**
     * 最新申报日期
     */
    private Date declareData;
    /**
     * 历次在线申报档案
     */
    private List<TdZyUnitbasicinfo> unitbasicinfoSelList;
    /**
     * 在线申报档案rid
     */
    private Integer unitbasicinfoRid;
    /**
     * 在线申报档案
     */
    private TdZyUnitbasicinfo unitbasicinfo;
    /*****************************场所监测**************************************/
    /**
     * 场所监测年份
     */
    private Integer placeDate;
    private List<Integer> placeDateList;
    /**
     * 是否当年无场所监测
     */
    private Boolean placeThatYear;
    /**
     * 基本情况概况
     */
    private UnitbasicInfo unitbasic;
    /***/
    private List<PlaceInfo> unitharmChkInfos;
    private List<PlaceInfo> unitHethCusInfos;
    private List<PlaceInfo> resultProInfos;
    /*****************************场所监测**************************************/
    /******************************** 统计图 *********************************/
    /**
     * 统计图表类型 1 地区分布 2 行业类别 3 经济类型 4 企业规模  5 高毒 6 致癌
     */
    private Integer chartType;
    /**
     * 统计图json
     */
    private String chartJson;
    /**
     * 缓存对应类型的统计图json
     */
    private Map<Integer, String> chartJsonMap;
    /**
     * 数据条数 用于判断柱状图是否自动滚动
     */
    private Integer chartDataSize;
    /**
     * 缓存数据条数 柱状图自动滚动用
     */
    private Map<Integer, Integer> chartDataSizeMap;
    /**
     * 地区缓存 key zoneGb
     */
    private Map<String, TsZone> chartZoneMap;
    /**
     * 码表缓存 key codeNameDesc subKey codeNo
     */
    private Map<String, Map<String, TsSimpleCode>> chartSimpleCodeMap;

    /******************************** 统计图 *********************************/

    /**用人单位预警处置期限*/
    private String limitTime;

    /**监督管理线索信息提醒*/
    private List<TbTjCrptWarnComm> tbTjCrptWarnCommList;
    /**职业卫生技术服务报告卡数据*/
    private List<Object[]> occhethCardList;
    /**放射卫生技术服务报告卡数据*/
    private List<Object[]> srvorgCardList;
    private Integer cardRid ;
    /**
     * 职业健康检查数据
     */
    private List<Object[]> bhkDataList;
    /**
     * 职业健康检查数据-弹出框数据
     */
    private GsCrptIntegratedShowBhkDataBean crptIntegratedShowBhkDataBean;
    /**
     * 职业病诊断数据
     */
    private List<List<String>> diagDataList;
    private List<String> diagDataHeaderList;
    private List<Object[]> diagYearDataList;

    /**处理结果码表*/
    private List<TsSimpleCode> warnResults;
    private Map<Integer,TsSimpleCode> warnResultMap;
    /**表头上得必填标记*/
    private String fontStr="<font color='red'>*</font>";

    /**预警信息实体类*/
    private TbTjCrptWarnComm crptWarnComm;

    /**申报单位基本信息*/
    private List<Object[]> unitBasicInfoList;

    /**申报表rid*/
    private Integer unitBasicInfoRid;

    /**场所检测*/
    private List<Object[]> unitBasicInfoJcList;

    /**申报表rid*/
    private Integer unitBasicInfoJcRid;

    public GsCrptIntegratedShowListBean() {
        initSimpleCode();
        otherInit();
        searchAction();
    }

    private void initSimpleCode() {
        this.chartSimpleCodeMap = new HashMap<>();
        //经济类型
        if (CollectionUtils.isEmpty(this.economyList)) {
            this.economyList = this.commService.findNumSimpleCodesByTypeId("5003");
        }
        if (!CollectionUtils.isEmpty(this.economyList)) {
            Map<String, TsSimpleCode> fillMap = new HashMap<>();
            for (TsSimpleCode simpleCode : this.economyList) {
                fillMap.put(simpleCode.getCodeNo(), simpleCode);
            }
            this.chartSimpleCodeMap.put("5003", fillMap);
        }
        //企业规模
        if (CollectionUtils.isEmpty(this.crptSizeList)) {
            this.crptSizeList = this.commService.findLevelSimpleCodesByTypeId("5004");
        }
        if (!CollectionUtils.isEmpty(this.crptSizeList)) {
            Map<String, TsSimpleCode> fillMap = new HashMap<>();
            for (TsSimpleCode simpleCode : this.crptSizeList) {
                fillMap.put(simpleCode.getCodeNo(), simpleCode);
            }
            this.chartSimpleCodeMap.put("5004", fillMap);
        }
        //行业类别
        if (CollectionUtils.isEmpty(this.indusList)) {
            this.indusList = this.commService.findNumSimpleCodesByTypeId("5002");
        }
        if (!CollectionUtils.isEmpty(this.indusList)) {
            Map<String, TsSimpleCode> fillMap = new HashMap<>();
            for (TsSimpleCode simpleCode : this.indusList) {
                fillMap.put(simpleCode.getCodeNo(), simpleCode);
            }
            this.chartSimpleCodeMap.put("5002", fillMap);
        }
        //职业病危害因素
        if (CollectionUtils.isEmpty(this.badRsnList)) {
            this.badRsnList = this.commService.findSimpleCodesByTypeId("5007");
        }
        if (!CollectionUtils.isEmpty(this.badRsnList)) {
            Map<String, TsSimpleCode> fillMap = new HashMap<>();
            for (TsSimpleCode simpleCode : this.badRsnList) {
                fillMap.put(simpleCode.getCodeNo(), simpleCode);
            }
            this.chartSimpleCodeMap.put("5007", fillMap);
        }

        //异常信息
        if (CollectionUtils.isEmpty(this.abnormalInfoList)) {
            this.abnormalInfoList = this.commService.findSimpleCodesByTypeId("5602");
        }
        //预警结果
        if (CollectionUtils.isEmpty(this.warnResults)) {
            List<TsSimpleCode> warnResultList = this.commService.findLevelSimpleCodesByTypeId("5603");
            this.warnResults = new ArrayList<>();
            if(!CollectionUtils.isEmpty(warnResultList)){
                for (TsSimpleCode simpleCode : warnResultList) {
                    if(!"4".equals(simpleCode.getExtendS1())){
                        warnResults.add(simpleCode);
                    }
                }
            }
        }
        this.warnResultMap=new HashMap<>();
        if (!CollectionUtils.isEmpty(this.warnResults)) {
            for (TsSimpleCode simpleCode : this.warnResults) {
                warnResultMap.put(simpleCode.getRid(), simpleCode);
            }
        }
        this.validateIndusTypeIds = ridListByList(this.indusList);
        this.validateEconomyIds = ridListByList(this.economyList);
    }

    /**
     * 查找经济性质与行业类别最后一级的rid集合
     **/
    private List<Integer> ridListByList(List<TsSimpleCode> resList) {
        List<Integer> resultList = new ArrayList<>();
        if (resList != null && resList.size() > 0) {
            Map<String, List<TsSimpleCode>> levelMap = new HashMap<>();
            for (TsSimpleCode obj : resList) {
                String codeLevelNo = obj.getCodeLevelNo();
                if (StringUtils.isBlank(codeLevelNo)) {
                    continue;
                }
                levelMap.put(codeLevelNo, new ArrayList<TsSimpleCode>());
                if (StringUtils.contains(codeLevelNo, ".")) {
                    String[] split = codeLevelNo.split("\\.");
                    StringBuffer parentCodeSb = new StringBuffer();
                    for (int i = 0; i < split.length - 1; i++) {//仅找出父级
                        parentCodeSb.append(".").append(split[i]);
                        String parentCode = parentCodeSb.substring(1);
                        List<TsSimpleCode> childs = levelMap.get(parentCode);
                        if (null == childs) {
                            childs = new ArrayList<TsSimpleCode>();
                            levelMap.put(parentCode, childs);
                        }
                        childs.add(obj);
                    }
                }
            }
            for (TsSimpleCode t : resList) {
                t.setLevelIndex(StringUtils.countMatches(t.getCodeLevelNo(), ".") + "");
                // 默认不可选择
                t.setIfSelected(false);
                List<TsSimpleCode> childs = levelMap.get(t.getCodeLevelNo());
                if (CollectionUtils.isEmpty(childs)) {
                    resultList.add(t.getRid());
                    // 最末级支持选择
                    t.setIfSelected(true);
                }
            }
        }
        return resultList;
    }

    private void otherInit() {
        this.ifSQL = true;
        //查询条件：地区
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        this.chartZoneMap = new HashMap<>();
        if (CollectionUtils.isEmpty(this.zoneList)) {
            this.zoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "");
            this.searchZoneCode = this.zoneList.get(0).getZoneCode();
            this.searchZoneName = this.zoneList.get(0).getZoneName();
        }
        if (!CollectionUtils.isEmpty(this.zoneList)) {
            for (TsZone zone : this.zoneList) {
                this.chartZoneMap.put(zone.getZoneGb(), zone);
            }
        }
        //查询条件：启用状态
        this.enabledState = new String[]{"0"};
        //修订权限
        this.hasFix = Global.getBtnSet().contains("heth_comm_crpt_gs_integrated_show_fix");

        //导入数据中空值的判断值
        limitTime = PropertyUtils.getValueWithoutException("warningLimitTime");

        this.initChart();
    }

    @Override
    public void searchAction() {
        //过滤职业病危害因素RID，防止SQL注入
        if (StringUtils.isNotBlank(this.selectBadRsnIds)) {
            List<String> selectBadRsnIdList = StringUtils.string2list(this.selectBadRsnIds, ",");
            for (int i = selectBadRsnIdList.size() - 1; i >= 0; i--) {
                if (ObjectUtil.convert(Integer.class, selectBadRsnIdList.get(i)) == null) {
                    selectBadRsnIdList.remove(i);
                }
            }
            this.selectBadRsnIds = StringUtils.list2string(selectBadRsnIdList, ",");
        }
        super.searchAction();
    }

    /**
     * 生成SQL
     *
     * @param type <pre>1：查询</pre><pre>2：查询count</pre><pre>3：导出</pre>
     * @return SQL
     */
    public String buildSql(int type) {
        this.paramMap.clear();
        StringBuilder sql = new StringBuilder();
        if (StringUtils.isNotBlank(this.selectBadRsnIds)) {
            sql.append(" WITH TEMP_TABLE AS ( ");
            sql.append(" SELECT REGEXP_SUBSTR(IDS, '[^,]+', 1, LEVEL, 'i') AS ID ");
            sql.append(" FROM (SELECT '").append(this.selectBadRsnIds).append("' IDS FROM DUAL) ");
            sql.append(" CONNECT BY LEVEL <= LENGTH(IDS) - LENGTH(REGEXP_REPLACE(IDS, ',', '')) + 1 ) ");
        }
        sql.append("SELECT ");
        if (type == 2) {
            sql.append(" COUNT(*) from( SELECT ");
        }
        if (type == 1 || type == 2) {
            sql.append(" C.RID, ");
            sql.append(" Z.FULL_NAME, ");
            sql.append(" C.CRPT_NAME, ");
            sql.append(" C.INSTITUTION_CODE, ");
            sql.append(" SC1.CODE_NAME ECONOMY, ");
            sql.append(" SC2.CODE_NAME CRPT_SIZE, ");
            sql.append(" SC3.CODE_NAME INDUS_TYPE, ");
            sql.append(" C.WORK_FORCE, ");
            sql.append(" C.HOLD_CARD_MAN, ");
            sql.append(" C.IF_SUB_ORG, ");
            sql.append(" Z.ZONE_GB, ");
            sql.append(" '' DEL_MARK_STR, ");
            sql.append(" C.DEL_MARK, ");
            sql.append(" '' UPDATE_STATE_STR, ");
            sql.append(" C.UPDATE_STATE, ");
            sql.append(" C.STOP_RSN, ");
            sql.append(" 0 as wcq, ");
            sql.append(" sum(case when T.STATE = 1 and (T2.EXTENDS1 = 1 or T2.EXTENDS1 = 2 or T2.EXTENDS1 = 4) then 1 else 0 end)               yhs, ");
            sql.append(" sum(case when T.STATE = 1 and T2.EXTENDS1 = 3 then 1 else 0 end)                                    zzzg, ");
            sql.append(" 0 as cq ");
        }
        if (type == 3) {
            sql.append("        T.RID, ");
            sql.append("        T.FULL_NAME, ");
            sql.append("        T.CRPT_NAME, ");
            sql.append("        T.INSTITUTION_CODE, ");
            sql.append("        T.INDUS_TYPE, ");
            sql.append("        T.ECONOMY, ");
            sql.append("        T.CRPT_SIZE, ");
            sql.append("        T.LINKMAN2, ");
            sql.append("        T.LINKPHONE2, ");
            sql.append("        T.WORK_FORCE, ");
            sql.append("        T.HOLD_CARD_MAN, ");
            sql.append("        T.ZONE_GB, ");
            sql.append("        LISTAGG(BADRSN_NAME, '、') WITHIN GROUP ( ORDER BY BADRSN_NO, BADRSN_NUM ) ");
            sql.append(" FROM (SELECT C.RID, ");
            sql.append("              Z.FULL_NAME, ");
            sql.append("              C.CRPT_NAME, ");
            sql.append("              C.INSTITUTION_CODE, ");
            sql.append("              SC3.CODE_PATH INDUS_TYPE, ");
            sql.append("              SC1.CODE_PATH ECONOMY, ");
            sql.append("              SC2.CODE_NAME CRPT_SIZE, ");
            sql.append("              C.LINKMAN2, ");
            sql.append("              C.LINKPHONE2, ");
            sql.append("              C.WORK_FORCE, ");
            sql.append("              C.HOLD_CARD_MAN, ");
            sql.append("              SC5.CODE_NAME BADRSN_NAME, ");
            sql.append("              SC5.CODE_NO   BADRSN_NO, ");
            sql.append("              SC5.NUM       BADRSN_NUM, ");
            sql.append("              Z.ZONE_GB ");
        }
        sql.append(" FROM TB_TJ_CRPT C ");
        sql.append("         LEFT JOIN TS_ZONE Z ON C.ZONE_ID = Z.RID ");
        sql.append("         LEFT JOIN TS_SIMPLE_CODE SC1 ON C.ECONOMY_ID = SC1.RID ");
        sql.append("         LEFT JOIN TS_SIMPLE_CODE SC2 ON C.CRPT_SIZE_ID = SC2.RID ");
        sql.append("         LEFT JOIN TS_SIMPLE_CODE SC3 ON C.INDUS_TYPE_ID = SC3.RID ");
        sql.append("         LEFT JOIN TB_TJ_CRPT_WARN T on T.CRPT_ID=C.RID ");
        sql.append("         LEFT JOIN TS_SIMPLE_CODE T1 on T.WARN_ID=T1.RID ");
        sql.append("         LEFT JOIN TS_SIMPLE_CODE T2 on T.RESULT_ID = T2.RID ");
        if (type == 3) {
            sql.append("         LEFT JOIN TB_TJ_CRPT_BADRSN BR ON C.RID = BR.CRPT_ID ");
            sql.append("         LEFT JOIN TS_SIMPLE_CODE SC4 ON BR.BADRSN_ID = SC4.RID ");
            sql.append("         LEFT JOIN TS_SIMPLE_CODE SC5 ON SC4.CODE_LEVEL_NO LIKE SC5.CODE_NO || '.%' AND SC5.CODE_NO = SC5.CODE_LEVEL_NO AND  SC5.CODE_TYPE_ID = SC4.CODE_TYPE_ID ");
        }
        sql.append("WHERE NVL(C.UPDATE_STOP, 0) = 0 ");
        //地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sql.append(" AND Z.ZONE_GB LIKE :zoneCode ");
            this.paramMap.put("zoneCode", ZoneUtil.zoneSelect(this.searchZoneCode) + "%");
        }
        //用人单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sql.append(" AND C.CRPT_NAME LIKE :searchCrptName escape '\\' ");
            this.paramMap.put("searchCrptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        //社会信用代码
        if (StringUtils.isNotBlank(this.searchInstitutionCode)) {
            sql.append(" AND C.INSTITUTION_CODE LIKE :searchInstitutionCode escape '\\' ");
            this.paramMap.put("searchInstitutionCode", "%" + StringUtils.convertBFH(this.searchInstitutionCode.trim()) + "%");
        }
        // 经济类型
        if (StringUtils.isNotBlank(this.selectEconomyIds)) {
            sql.append(" AND C.ECONOMY_ID IN (:economyIdList) ");
            List<String> economyIdList = StringUtils.string2list(this.selectEconomyIds, ",");
            this.paramMap.put("economyIdList", economyIdList);
        }
        // 企业规模
        if (StringUtils.isNotBlank(this.selectCrptSizeIds)) {
            sql.append(" AND C.CRPT_SIZE_ID IN (:crptSizeIdList) ");
            List<String> crptSizeIdList = StringUtils.string2list(this.selectCrptSizeIds, ",");
            this.paramMap.put("crptSizeIdList", crptSizeIdList);
        }
        // 行业类别
        if (StringUtils.isNotBlank(this.selectIndusTypeIds)) {
            sql.append(" AND C.INDUS_TYPE_ID IN (:indusTypeIdList) ");
            List<String> indusTypeIdList = StringUtils.string2list(this.selectIndusTypeIds, ",");
            this.paramMap.put("indusTypeIdList", indusTypeIdList);
        }
        //职业病危害因素
        if (StringUtils.isNotBlank(this.selectBadRsnIds)) {
            sql.append(" AND EXISTS ( SELECT 1 FROM TB_TJ_CRPT_BADRSN BR WHERE BR.CRPT_ID = C.RID ");
            sql.append(" AND EXISTS(SELECT 1 FROM TEMP_TABLE TEMP WHERE BR.BADRSN_ID = TEMP.ID) ) ");
        }
        //启用状态
        if (ObjectUtil.isNotEmpty(this.enabledState) && this.enabledState.length == 1) {
            sql.append(" AND NVL(C.DEL_MARK, 0) = :enabledStateStr ");
            this.paramMap.put("enabledStateStr", StringUtils.objectToString(this.enabledState[0]));
        }
        //修订状态
        if (ObjectUtil.isNotEmpty(this.fixState)) {
            sql.append(" AND C.UPDATE_STATE IN (:fixStateList) ");
            this.paramMap.put("fixStateList", Arrays.asList(this.fixState));
        }
        //异常信息
        if(StringUtils.isNotBlank(selectAbnormalInfoIds)){
            sql.append(" AND T1.RID in(:abnormalInfoList) ");
            this.paramMap.put("abnormalInfoList", StringUtils.string2list(selectAbnormalInfoIds,","));
        }
        if(type == 1 || type == 2){
            sql.append(" group by C.RID,Z.FULL_NAME,C.CRPT_NAME,C.INSTITUTION_CODE,SC1.CODE_NAME ,SC2.CODE_NAME ,SC3.CODE_NAME, ");
            sql.append("        C.WORK_FORCE,C.HOLD_CARD_MAN,C.IF_SUB_ORG,Z.ZONE_GB,C.DEL_MARK,C.UPDATE_STATE,C.STOP_RSN ");
        }
        if (type == 2) {
            sql.append(")");
        }
        if (type == 1) {
            sql.append(" ORDER BY Z.ZONE_GB, C.CRPT_NAME ");
        }
        if (type == 3) {
            sql.append(" GROUP BY C.RID, Z.FULL_NAME, C.CRPT_NAME, C.INSTITUTION_CODE, SC3.CODE_PATH, SC1.CODE_PATH, SC2.CODE_NAME, C.LINKMAN2, C.LINKPHONE2, C.WORK_FORCE, C.HOLD_CARD_MAN, SC5.CODE_NAME, SC5.CODE_NO, SC5.NUM, Z.ZONE_GB ");
            sql.append(") T ");
            sql.append("GROUP BY RID, FULL_NAME, CRPT_NAME, INSTITUTION_CODE, INDUS_TYPE, ECONOMY, CRPT_SIZE, LINKMAN2, LINKPHONE2, WORK_FORCE, HOLD_CARD_MAN, ZONE_GB ");
            sql.append("ORDER BY ZONE_GB, CRPT_NAME ");
        }
        return sql.toString();
    }

    @Override
    public String[] buildHqls() {
        return new String[]{buildSql(1), buildSql(2)};
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<Object[]> result = CollectionUtil.castList(Object[].class, list);
        List<Integer> rids=new ArrayList<>();
        for (Object[] objects : result) {
            rids.add(Integer.parseInt(objects[0].toString()));
        }
        Map<Integer,Integer> countNoCpMap=new HashMap<>();
        Map<Integer,Integer> countCpMap=new HashMap<>();
        countWarningNum(rids, countNoCpMap, countCpMap);
        for (Object[] obj : result) {
            //地区去除省份
            String fullName = StringUtils.objectToString(obj[1]);
            obj[1] = fullName.substring(fullName.indexOf("_") + 1);
            //启用状态
            obj[11] = "1".equals(StringUtils.objectToString(obj[12])) ? "停用" : "启用";
            //修订状态
            String fixState = StringUtils.objectToString(obj[14]);
            if ("1".equals(fixState)) {
                obj[13] = "修订中";
            } else if ("2".equals(fixState)) {
                obj[13] = "已修订";
            } else if ("3".equals(fixState)) {
                obj[13] = "修订失败";
            }
            //预警数
            if(countNoCpMap.containsKey(Integer.parseInt(obj[0].toString()))){
                obj[16]=countNoCpMap.get(Integer.parseInt(obj[0].toString()));
            }
            if(countCpMap.containsKey(Integer.parseInt(obj[0].toString()))){
                obj[19]=countCpMap.get(Integer.parseInt(obj[0].toString()));
            }
        }
    }

    /**
    * @description: 统计 预警数
    * <AUTHOR>
    */
    private void countWarningNum(List<Integer> rids, Map<Integer, Integer> countNoCpMap, Map<Integer, Integer> countCpMap) {
        if (CollectionUtils.isEmpty(rids)) {
            return;
        }
        List<Object[]> crptWarnList = crptIntegratedShowService.findBatchCrptWarnByRids(rids);
        if (CollectionUtils.isEmpty(crptWarnList)) {
            return;
        }
        Map<String,Integer> warnDataMap=new HashMap<>();
        for (Object[] objects : crptWarnList) {
            if(objects[2]!=null && !warnDataMap.containsKey(objects[2].toString())){
                if (HolidayUtil.calRemainingDate(DateUtils.parseDate(objects[2]), new Date(), this.limitTime) > 0) {
                    warnDataMap.put(objects[2].toString(),1);
                }else{
                    warnDataMap.put(objects[2].toString(),0);
                }
            }
        }
        if(warnDataMap.isEmpty()){
            return;
        }

        for (Object[] objects : crptWarnList) {
            //待处置 判断有无超期
            boolean bool = objects[1] != null && "0".equals(objects[1].toString()) && objects[2] != null;
            if (!bool) {
                continue;
            }
            if (warnDataMap.containsKey(objects[2].toString()) && warnDataMap.get(objects[2].toString())==1) {
                if (!countNoCpMap.containsKey(Integer.parseInt(objects[0].toString()))) {
                    countNoCpMap.put(Integer.parseInt(objects[0].toString()), 1);
                } else {
                    countNoCpMap.put(Integer.parseInt(objects[0].toString()), countNoCpMap.get(Integer.parseInt(objects[0].toString())) + 1);
                }
            } else {
                if (!countCpMap.containsKey(Integer.parseInt(objects[0].toString()))) {
                    countCpMap.put(Integer.parseInt(objects[0].toString()), 1);
                } else {
                    countCpMap.put(Integer.parseInt(objects[0].toString()), countCpMap.get(Integer.parseInt(objects[0].toString())) + 1);
                }
            }

        }
    }

    /**
     * 选择码表弹框页面
     */
    public void selSimpleCodeAction() {
        String titleName = "";
        String selectIds = "";
        String dialogUrl = "";
        Integer width = null;
        Integer contentWidth = null;
        Integer height = null;
        Integer contentHeight = null;
        switch (this.simpleCodeOpType) {
            case "5002":
                titleName = "行业类别";
                selectIds = this.selectIndusTypeIds;
                dialogUrl = "/webapp/system/indusTypeCodeMulitySelectList";
                contentWidth = 800;
                contentHeight = 500;
                break;
            case "5003":
                titleName = "经济类型";
                selectIds = this.selectEconomyIds;
                dialogUrl = "/webapp/system/codeMulitySelectList";
                contentWidth = 700;
                contentHeight = 500;
                break;
            case "5007":
                titleName = "危害因素";
                selectIds = this.selectBadRsnIds;
                dialogUrl = "/webapp/system/codeMulitySelectList";
                contentWidth = 700;
                contentHeight = 500;
                break;
            default:
                break;
        }
        Map<String, Object> options = MapUtils.produceDialogMap(width, contentWidth, height, contentHeight);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add(titleName);
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>();
        paramList.add(this.simpleCodeOpType);
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<>();
        paramList.add(selectIds);
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<>();
        paramList.add("true");
        paramMap.put("ifShowFirstCode", paramList);

        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog(dialogUrl, options, paramMap);
    }

    /**
     * 选择码表后操作
     *
     * @param event 选择项
     */
    public void onSimpleCodeAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = CollectionUtil.castList(TsSimpleCode.class, selectedMap.get("selectPros"));
            if (ObjectUtil.isNotEmpty(list)) {
                StringBuilder names = new StringBuilder();
                StringBuilder ids = new StringBuilder();
                for (TsSimpleCode t : list) {
                    names.append("，").append(t.getCodeName());
                    ids.append(",").append(t.getRid());
                }
                switch (this.simpleCodeOpType) {
                    case "5002":
                        this.selectIndusTypeIds = ids.substring(1);
                        this.selectIndusTypeNames = names.substring(1);
                        break;
                    case "5003":
                        this.selectEconomyIds = ids.substring(1);
                        this.selectEconomyNames = names.substring(1);
                        break;
                    case "5007":
                        this.selectBadRsnIds = ids.substring(1);
                        this.selectBadRsnNames = names.substring(1);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    /**
     * 清除页面选择码表
     */
    public void clearSimpleCode() {
        switch (this.simpleCodeOpType) {
            case "5002":
                this.selectIndusTypeNames = null;
                this.selectIndusTypeIds = null;
                break;
            case "5003":
                this.selectEconomyNames = null;
                this.selectEconomyIds = null;
                break;
            case "5007":
                this.selectBadRsnIds = null;
                this.selectBadRsnNames = null;
                break;
            default:
                break;
        }
    }

    public void exportBefore() {
        RequestContext context = RequestContext.getCurrentInstance();
        int count = this.commService.findCountBySql(buildSql(2), this.paramMap);
        if (count == 0) {
            JsfUtil.addErrorMessage("无可导出的数据！");
            return;
        }
        context.execute("getDownloadFileClick();");
    }

    /**
     * 导出操作
     */
    public DefaultStreamedContent export() {
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("showStatus();");
        ExcelExportUtil excelExportUtil;
        String[] excelHeaders = new String[]{"地区", "用人单位名称", "社会信用代码", "行业类别", "经济类型", "企业规模", "联系人", "联系电话", "职工人数", "接害人数", "职业病危害因素种类"};
        excelExportUtil = new ExcelExportUtil("用人单位综合展示", excelHeaders, pakExcelExportDataList(executeExportSql(), excelHeaders.length));
        excelExportUtil.setColumnWidths(new Integer[]{null, null, 18, null, null, 6, 9, 12, 6, 6, null});
        excelExportUtil.setNeedTitle(false);
        excelExportUtil.setFrozenPaneRowsNum(1);
        Workbook wb = excelExportUtil.exportExcel();
        if (wb != null) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                String fileName = URLEncoder.encode("用人单位综合展示.xlsx", "UTF-8");
                wb.write(baos);
                baos.flush();
                byte[] aa = baos.toByteArray();
                context.execute("hideStatus();");
                return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
            } catch (Exception e) {
                JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            }
        }
        context.execute("hideStatus();");
        return null;
    }

    /**
     * 查询导出数据
     *
     * @return 导出数据
     */
    private List<Object[]> executeExportSql() {
        return CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(buildSql(3), this.paramMap));
    }

    private List<ExcelExportObject[]> pakExcelExportDataList(List<Object[]> dataList, int headerSize) {
        if (ObjectUtil.isEmpty(dataList) || headerSize <= 0) {
            return new ArrayList<>();
        }
        List<ExcelExportObject[]> excelExportObjectList = new ArrayList<>();
        for (Object[] data : dataList) {
            ExcelExportObject[] objects = new ExcelExportObject[headerSize];
            //电话脱敏
            data[8] = StringUtils.encryptPhone(StringUtils.objectToString(data[8]));

            int index = 0;
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]));
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]));
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]));
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]));
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index + 2]));
            excelExportObjectList.add(objects);
        }
        return excelExportObjectList;
    }

    /**
     * 启用企业
     */
    public void enableCrpt() {
        try {
            this.crptIntegratedShowService.updateCrptDelMarkByRid(this.rid, 0, "");
            JsfUtil.addSuccessMessage("启用成功！");
            searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("启用失败！");
        }
    }

    /**
     * 停用企业
     */
    public void stopCrpt() {
        try {
            if (StringUtils.isBlank(this.stopRsn)) {
                JsfUtil.addErrorMessage("停用原因不能为空！");
                return;
            }
            if (StringUtils.objectToString(this.stopRsn).length() > 200) {
                JsfUtil.addErrorMessage("停用原因长度不能大于200！");
                return;
            }
            this.crptIntegratedShowService.updateCrptDelMarkByRid(this.rid, 1, this.stopRsn);
            JsfUtil.addSuccessMessage("停用成功！");
            searchAction();
            RequestContext.getCurrentInstance().update("tabView:mainForm:dataTable");
            RequestContext.getCurrentInstance().execute("PF('StopRsnDialog').hide()");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("停用失败！");
        }
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {
        this.showArchives = null;
        initBaseInfo();
        //监督管理线索信息提醒
        initCrptWarn();
        //职业病危害项目申报数据
        initUnitBasicInfo();
        //场所检测
        initUnitBasicInfoJc();
        //职业卫生技术服务报告卡数据
        initOcchethCard();
        //放射卫生技术服务报告卡数据
        initSrvorgCard();
        //职业健康检查数据
        DataTable dataTable1 = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                .findComponent("tabView:viewForm:bhkDataTable");
        if (dataTable1 != null) {
            dataTable1.setRows(10);
            dataTable1.setFirst(0);
        }
        this.bhkDataList = this.crptIntegratedShowService.selectBhkDataYearListByCrptId(this.rid);
        //职业病诊断数据
        DataTable dataTable2 = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                .findComponent("tabView:viewForm:diagDataTable");
        if (dataTable2 != null) {
            dataTable2.setRows(10);
            dataTable2.setFirst(0);
        }
        this.diagDataHeaderList = new ArrayList<>();
        this.diagDataList = this.crptIntegratedShowService.selectDiagDataList(this.rid, this.diagDataHeaderList);
        this.crptIntegratedShowBhkDataBean = new GsCrptIntegratedShowBhkDataBean();
    }

    /**
    * @description: 职业病危害项目申报数据
    * <AUTHOR>
    */
    public void initUnitBasicInfo(){
        unitBasicInfoList=new ArrayList<>();
        unitBasicInfoList=this.crptIntegratedShowService.findUnitBasicInfoByRid(this.rid);
        //table 页面初始化
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:viewForm:zybwhTable");//dataTable id 可以定位到dataTable
        if(null != dataTable){
            dataTable.setRows(10);
            dataTable.setFirst(0);
        }
    }
    /**
    * @description: 场所检测
    * <AUTHOR>
    */
    public void initUnitBasicInfoJc(){
        unitBasicInfoJcList=new ArrayList<>();
        unitBasicInfoJcList=this.crptIntegratedShowService.findUnitBasicInfoJcByRid(this.rid);
        //table 页面初始化
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:viewForm:csjcTable");//dataTable id 可以定位到dataTable
        if(null != dataTable){
            dataTable.setRows(10);
            dataTable.setFirst(0);
        }
    }

    /**
     *  <p>方法描述：职业卫生技术服务报告卡数据</p>
     * @MethodAuthor hsj 2023-09-26 15:41
     */
    private void initOcchethCard() {
        this.occhethCardList = this.crptIntegratedShowService.initOcchethCard(this.rid);
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:viewForm:occhethCard");
        dataTable.setFirst(0);
        dataTable.setRows(10);
    }
    /**
     *  <p>方法描述：放射卫生技术服务报告卡数据</p>
     * @MethodAuthor hsj 2023-09-26 16:24
     */
    private void initSrvorgCard() {
        this.srvorgCardList = this.crptIntegratedShowService.initSrvorgCard(this.rid);
        if(!CollectionUtils.isEmpty(this.srvorgCardList )){
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("tabView:viewForm:srvorgCard");
            dataTable.setFirst(0);
            dataTable.setRows(10);
        }
    }
    /**
     *  <p>方法描述：页面跳转</p>
     * @MethodAuthor hsj 2023-09-26 15:59
     */
    public void viewOcchethCardAction(){
        if (null==this.cardRid) {
            return;
        }
        StringBuffer url = new StringBuffer();
        url.append("/webapp/heth/comm/crptshow/viewPage/occHethCardFillInOnlyView.faces?rid=").append(this.cardRid);
        RequestContext.getCurrentInstance().execute("top.ShortcutMenuClick('','职业卫生技术服务信息报送卡详情','"+url+"','');");
    }
    /**
     *  <p>方法描述：页面跳转</p>
     * @MethodAuthor hsj 2023-09-26 15:59
     */
    public void viewSrvorgCardAction(){
        if (null==this.cardRid) {
            return;
        }
        StringBuffer url = new StringBuffer();
        url.append("/webapp/heth/comm/crptshow/viewPage/tdZwSrvorgCardOnlyView.faces?rid=").append(this.cardRid);
        RequestContext.getCurrentInstance().execute("top.ShortcutMenuClick('','放射卫生技术服务信息报送卡详情','"+url+"','');");
    }
    /**
    * @description:  监督管理线索信息提醒
    * <AUTHOR>
    */
    public void initCrptWarn(){
        this.crptWarnComm=null;
        this.tbTjCrptWarnCommList=new ArrayList<>();
        tbTjCrptWarnCommList = crptIntegratedShowService.findCrptWarnByRid(this.rid);
        for (TbTjCrptWarnComm tbTjCrptWarnComm : tbTjCrptWarnCommList) {
            if(tbTjCrptWarnComm.getFkByResultId()==null){
                tbTjCrptWarnComm.setFkByResultId(new TsSimpleCode());
            }else{
                tbTjCrptWarnComm.setResultId(tbTjCrptWarnComm.getFkByResultId().getRid());
            }
            //判断是否超期
            if(!new Integer("0").equals(tbTjCrptWarnComm.getState())){
                continue;
            }
            if(HolidayUtil.calRemainingDate(tbTjCrptWarnComm.getWarnDate(), new Date(), this.limitTime) > 0){
                tbTjCrptWarnComm.setIfOvertime(false);
            }else{
                tbTjCrptWarnComm.setIfOvertime(true);
            }
        }
        //table 页面初始化
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:viewForm:jdglxsTable");//dataTable id 可以定位到dataTable
        if(null != dataTable){
            dataTable.setRows(10);
            dataTable.setFirst(0);
        }
    }

    /**
    * @description: 预警信息提交
    * <AUTHOR>
    */
    public void warnSubmitAction(){
        try{
            if(!this.warnResultMap.isEmpty() && warnResultMap.containsKey(crptWarnComm.getResultId())){
                crptWarnComm.setFkByResultId(warnResultMap.get(crptWarnComm.getResultId()));
            }
            crptWarnComm.setIfOvertime(false);
            crptWarnComm.setFkByDealPsnId(Global.getUser());
            crptWarnComm.setDealDate(new Date());
            updateCrptWarn(crptWarnComm,1);
            JsfUtil.addSuccessMessage("提交成功！");
        }catch (Exception e){
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
    * @description: 预警信息撤销
    * <AUTHOR>
    */
    public void warnCancelAction(){
        try{
            crptWarnComm.setResultId(crptWarnComm.getFkByResultId().getRid());
            updateCrptWarn(crptWarnComm,0);
            JsfUtil.addSuccessMessage("撤销成功！");
        }catch (Exception e){
            JsfUtil.addErrorMessage("撤销失败！");
        }
    }


    /**
    * @description: 更新预警信息 方法抽取
    * <AUTHOR>
    */
    private void updateCrptWarn(TbTjCrptWarnComm crptWarnComm,Integer statue) {
        crptWarnComm.setState(statue);
        this.crptIntegratedShowService.upsertEntity(crptWarnComm);
        //判断是否超期
        if(!new Integer("0").equals(crptWarnComm.getState())){
            return;
        }
        if(HolidayUtil.calRemainingDate(crptWarnComm.getWarnDate(), new Date(), this.limitTime) > 0){
            crptWarnComm.setIfOvertime(false);
        }else{
            crptWarnComm.setIfOvertime(true);
        }
    }

    /**
    * @description: 提交弹框
    * <AUTHOR>
    */
    public void openWarnDialog(){
        if(crptWarnComm==null){
            return;
        }
        if(crptWarnComm.getResultId()==null){
            JsfUtil.addErrorMessage("请选择处理结果！");
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show();");
    }


    private void initBaseInfo() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", this.rid);
        String sql = "SELECT Z.FULL_NAME, " +
                "       C.CRPT_NAME, " +
                "       C.INSTITUTION_CODE, " +
                "       C.ADDRESS, " +
                "       C.ENROL_ADDRESS, " +
                "       SC3.CODE_NAME INDUS_TYPE, " +
                "       SC1.CODE_NAME ECONOMY, " +
                "       SC2.CODE_NAME CRPT_SIZE, " +
                "       C.LINKMAN2, " +
                "       C.LINKPHONE2, " +
                "       SC3.CODE_DESC OCC_RISK_CLASS, " +
                "       ''            BADRSN " +
                "FROM TB_TJ_CRPT C " +
                "         LEFT JOIN TS_ZONE Z ON C.ZONE_ID = Z.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC1 ON C.ECONOMY_ID = SC1.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC2 ON C.CRPT_SIZE_ID = SC2.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC3 ON C.INDUS_TYPE_ID = SC3.RID " +
                "         LEFT JOIN TB_TJ_CRPT_BADRSN BR ON C.RID = BR.CRPT_ID " +
                "WHERE C.RID = :rid";
        List<Object[]> dataList = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql, paramMap));
        if (CollectionUtils.isEmpty(dataList)) {
            this.crptBaseInfo = new Object[11];
            return;
        }
        this.crptBaseInfo = dataList.get(0);
        //地区去除省份
        String fullName = StringUtils.objectToString(this.crptBaseInfo[0]);
        this.crptBaseInfo[0] = fullName.substring(fullName.indexOf("_") + 1);
        if (ObjectUtil.isEmpty(this.occRiskClassMap)) {
            this.occRiskClassMap = new HashMap<>();
            List<TsSimpleCode> occRiskClassStrList = this.commService.findNumSimpleCodesByTypeId("5559");
            for (TsSimpleCode simpleCode : occRiskClassStrList) {
                this.occRiskClassMap.put(simpleCode.getExtendS1(), simpleCode.getCodeName());
            }
        }
        this.crptBaseInfo[10] = this.occRiskClassMap.get(StringUtils.objectToString(this.crptBaseInfo[10]));
        sql = "SELECT LISTAGG(BADRSN_NAME || '：' || BADRSN_NUM || '个', ' | ') WITHIN GROUP ( ORDER BY BADRSN_NUM DESC, BADRSN_NAME ) " +
                "FROM (SELECT RID, BADRSN_NAME, COUNT(1) BADRSN_NUM " +
                "      FROM (SELECT C.RID, " +
                "                   SC5.CODE_NAME BADRSN_NAME " +
                "            FROM TB_TJ_CRPT C " +
                "                     LEFT JOIN TB_TJ_CRPT_BADRSN BR ON C.RID = BR.CRPT_ID " +
                "                     LEFT JOIN TS_SIMPLE_CODE SC4 ON BR.BADRSN_ID = SC4.RID " +
                "                     LEFT JOIN TS_SIMPLE_CODE SC5 ON SC4.CODE_LEVEL_NO LIKE SC5.CODE_NO || '.%' AND SC5.CODE_NO = SC5.CODE_LEVEL_NO AND SC5.CODE_TYPE_ID = SC4.CODE_TYPE_ID " +
                "            WHERE SC5.CODE_NAME IS NOT NULL AND C.RID = :rid " +
                "            GROUP BY C.RID, BR.BADRSN_ID, SC5.CODE_NAME, SC5.CODE_NO) " +
                "      GROUP BY RID, BADRSN_NAME) T " +
                "GROUP BY RID";
        List<String> badrsnDataList = CollectionUtil.castList(String.class, this.commService.findDataBySqlNoPage(sql, paramMap));
        if (CollectionUtils.isEmpty(badrsnDataList)) {
            return;
        }
        this.crptBaseInfo[11] = badrsnDataList.get(0);
    }

    public void openBadrsnDialog() {
        //重置页面弹出框当前页码
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:viewForm:occRiskClassTable");
        if (dataTable != null) {
            dataTable.setFirst(0);
            dataTable.setRows(10);
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", this.rid);
        String sql = "SELECT SC2.CODE_NAME B, SC1.CODE_NAME S, SC1.EXTENDS7, '', '' " +
                "FROM TB_TJ_CRPT C " +
                "         LEFT JOIN TB_TJ_CRPT_BADRSN BR ON C.RID = BR.CRPT_ID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC1 ON BR.BADRSN_ID = SC1.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC2 ON SC1.CODE_LEVEL_NO LIKE SC2.CODE_NO || '.%' AND SC2.CODE_NO = SC2.CODE_LEVEL_NO AND SC2.CODE_TYPE_ID = SC1.CODE_TYPE_ID " +
                "WHERE SC1.CODE_NAME IS NOT NULL AND C.RID = :rid " +
                "GROUP BY SC2.CODE_NAME, SC1.CODE_NAME, SC1.EXTENDS7, SC1.NUM, SC1.CODE_NO " +
                "ORDER BY SC1.NUM, SC1.CODE_NO";
        this.occRiskClassList = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql, paramMap));
        for (Object[] occRiskClass : this.occRiskClassList) {
            String extends7 = StringUtils.objectToString(occRiskClass[2]);
            if ("1".equals(extends7)) {
                occRiskClass[3] = 1;
            } else if ("2".equals(extends7)) {
                occRiskClass[4] = 1;
            } else if ("1,2".equals(extends7)) {
                occRiskClass[3] = 1;
                occRiskClass[4] = 1;
            }
        }
        RequestContext.getCurrentInstance().execute("PF('OccRiskClassDialog').show()");
    }

    /**
     * 跳转申报信息页面
     */
    public void viewUnitBasicInfo() {
        if (this.unitBasicInfoRid == null) {
            return;
        }
        RequestContext.getCurrentInstance().execute("top.ShortcutMenuClick('','在线申报档案','" + "/webapp/heth/comm/crptshow/tdZyUnitBasicInfoView.faces?rid=" + unitBasicInfoRid + "','');");
    }

    /**
     * <p>方法描述：跳转场所监测档案详情</p>
     *
     * @MethodAuthor hsj 2023-06-27 10:33
     */
    public void csjcViewAction() {
        if (null == unitBasicInfoJcRid) {
            return;
        }
        RequestContext.getCurrentInstance().execute("top.ShortcutMenuClick('','场所监测档案','" + "/webapp/heth/comm/crptshow/TdZxjcUnitBasicInfoView.faces?rid=" + unitBasicInfoJcRid + "','');");
    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    /**
     * 打开修订页面
     */
    public void openFixPageAction() {
        if (ObjectUtil.isEmpty(this.selectEntitys) || this.selectEntitys.size() < 2 || this.selectEntitys.size() > 5) {
            JsfUtil.addErrorMessage("请选择2~5条数据进行比对！");
            RequestContext.getCurrentInstance().execute("autoMove()");
            return;
        }
        String ifSubOrg = StringUtils.objectToString(this.selectEntitys.get(0)[9]);
        for (Object[] selectEntity : this.selectEntitys) {
            if (!ifSubOrg.equals(StringUtils.objectToString(selectEntity[9]))) {
                JsfUtil.addErrorMessage("修订的单位必须是同一级别！");
                RequestContext.getCurrentInstance().execute("autoMove()");
                return;
            }
        }
        openFixPage();
    }

    /**
     * 修订页面初始化
     */
    public void openFixPage() {
        //查询需要修订的企业
        this.fixCrptList = new ArrayList<>();
        List<String> crptRidStrList = new ArrayList<>();
        for (Object[] selectEntity : this.selectEntitys) {
            Integer crptRid = ObjectUtil.convert(Integer.class, selectEntity[0]);
            if (crptRid == null) {
                continue;
            }
            crptRidStrList.add(crptRid + "");
        }
        List<TbTjCrpt> crptList = this.crptIntegratedShowService.findCrptListByRids(StringUtils.list2string(crptRidStrList, ","));
        if (CollectionUtils.isEmpty(crptList)) {
            JsfUtil.addErrorMessage("请选择2~5条数据进行比对！");
            RequestContext.getCurrentInstance().execute("autoMove()");
            return;
        }
        for (TbTjCrpt crpt : crptList) {
            if (crpt.getTsZoneByZoneId() == null) {
                crpt.setTsZoneByZoneId(new TsZone());
            }
            if (crpt.getTsSimpleCodeByIndusTypeId() == null) {
                crpt.setTsSimpleCodeByIndusTypeId(new TsSimpleCode());
            }
            if (crpt.getTsSimpleCodeByEconomyId() == null) {
                crpt.setTsSimpleCodeByEconomyId(new TsSimpleCode());
            }
            if (crpt.getTsSimpleCodeByCrptSizeId() == null) {
                crpt.setTsSimpleCodeByCrptSizeId(new TsSimpleCode());
            }
            this.fixCrptList.add(new TbTjCrptVO(crpt));
        }
        //处理企业是否可选
        TbTjCrptVO crptVOTemp = new TbTjCrptVO();
        TbTjCrpt crptTemp = crptList.get(0);
        for (TbTjCrptVO crptVO : this.fixCrptList) {
            TbTjCrpt voCrpt = crptVO.getCrpt();
            if (isNeedSel(voCrpt.getTsZoneByZoneId().getRid(), crptTemp.getTsZoneByZoneId().getRid())) {
                crptVOTemp.setZoneSel(false);
            }
            if (isNeedSel(voCrpt.getCrptName(), crptTemp.getCrptName())) {
                crptVOTemp.setNameSel(false);
            }
            if (isNeedSel(voCrpt.getInstitutionCode(), crptTemp.getInstitutionCode())) {
                crptVOTemp.setInstitutionCodeSel(false);
            }
            if (isNeedSel(voCrpt.getAddress(), crptTemp.getAddress())) {
                crptVOTemp.setAddressSel(false);
            }
            if (isNeedSel(voCrpt.getEnrolAddress(), crptTemp.getEnrolAddress())) {
                crptVOTemp.setEnrolAddressSel(false);
            }
            if (isNeedSel(voCrpt.getTsSimpleCodeByIndusTypeId().getRid(), crptTemp.getTsSimpleCodeByIndusTypeId().getRid())) {
                crptVOTemp.setIndusTypeSel(false);
            }
            if (isNeedSel(voCrpt.getTsSimpleCodeByEconomyId().getRid(), crptTemp.getTsSimpleCodeByEconomyId().getRid())) {
                crptVOTemp.setEconomySel(false);
            }
            if (isNeedSel(voCrpt.getTsSimpleCodeByCrptSizeId().getRid(), crptTemp.getTsSimpleCodeByCrptSizeId().getRid())) {
                crptVOTemp.setCrptSizeSel(false);
            }
            if (isNeedSel(voCrpt.getLinkman2(), crptTemp.getLinkman2())) {
                crptVOTemp.setLinkMan2Sel(false);
            }
            if (isNeedSel(voCrpt.getLinkphone2(), crptTemp.getLinkphone2())) {
                crptVOTemp.setLinkPhone2Sel(false);
            }
        }
        for (TbTjCrptVO crptVO : this.fixCrptList) {
            TbTjCrpt voCrpt = crptVO.getCrpt();
            crptVO.setCrptSel(false);
            boolean isZoneNull = ObjectUtil.isNotEmpty(voCrpt.getTsZoneByZoneId())
                    && ObjectUtil.isNotEmpty(voCrpt.getTsZoneByZoneId().getRid());
            if (isZoneNull && Boolean.FALSE.equals(crptVOTemp.getZoneSel())) {
                crptVO.setZoneSel(false);
            }
            if (ObjectUtil.isNotEmpty(voCrpt.getCrptName()) && Boolean.FALSE.equals(crptVOTemp.getNameSel())) {
                crptVO.setNameSel(false);
            }
            if (ObjectUtil.isNotEmpty(voCrpt.getInstitutionCode()) && Boolean.FALSE.equals(crptVOTemp.getInstitutionCodeSel())) {
                crptVO.setInstitutionCodeSel(false);
            }
            if (ObjectUtil.isNotEmpty(voCrpt.getAddress()) && Boolean.FALSE.equals(crptVOTemp.getAddressSel())) {
                crptVO.setAddressSel(false);
            }
            if (ObjectUtil.isNotEmpty(voCrpt.getEnrolAddress()) && Boolean.FALSE.equals(crptVOTemp.getEnrolAddressSel())) {
                crptVO.setEnrolAddressSel(false);
            }
            boolean isIndusTypeNull = ObjectUtil.isNotEmpty(voCrpt.getTsSimpleCodeByIndusTypeId())
                    && ObjectUtil.isNotEmpty(voCrpt.getTsSimpleCodeByIndusTypeId().getRid());
            if (isIndusTypeNull && Boolean.FALSE.equals(crptVOTemp.getIndusTypeSel())) {
                crptVO.setIndusTypeSel(false);
            }
            boolean isEconomyNull = ObjectUtil.isNotEmpty(voCrpt.getTsSimpleCodeByEconomyId())
                    && ObjectUtil.isNotEmpty(voCrpt.getTsSimpleCodeByEconomyId().getRid());
            if (isEconomyNull && Boolean.FALSE.equals(crptVOTemp.getEconomySel())) {
                crptVO.setEconomySel(false);
            }
            boolean isCrptSizeNull = ObjectUtil.isNotEmpty(voCrpt.getTsSimpleCodeByCrptSizeId())
                    && ObjectUtil.isNotEmpty(voCrpt.getTsSimpleCodeByCrptSizeId().getRid());
            if (isCrptSizeNull && Boolean.FALSE.equals(crptVOTemp.getCrptSizeSel())) {
                crptVO.setCrptSizeSel(false);
            }
            if (ObjectUtil.isNotEmpty(voCrpt.getLinkman2()) && Boolean.FALSE.equals(crptVOTemp.getLinkMan2Sel())) {
                crptVO.setLinkMan2Sel(false);
            }
            if (ObjectUtil.isNotEmpty(voCrpt.getLinkphone2()) && Boolean.FALSE.equals(crptVOTemp.getLinkPhone2Sel())) {
                crptVO.setLinkPhone2Sel(false);
            }
        }
        super.forwardOtherPage();
        RequestContext.getCurrentInstance().update("page_view");
    }

    /**
     * 其中一个为空或者有不一样的值时需要选择
     *
     * @param o1 值1
     * @param o2 值2
     * @return 是否需要选择
     */
    private boolean isNeedSel(Object o1, Object o2) {
        return ObjectUtil.isEmpty(o1) || ObjectUtil.isEmpty(o2) || !o1.equals(o2);
    }

    /**
     * 页面切换选择时需要把其他单位选择清除
     *
     * @param changeType  切换类型
     * @param changeIndex 切换企业下标
     */
    public void fixChange(Integer changeType, Integer changeIndex) {
        if (changeType == null || changeIndex == null) {
            return;
        }
        List<TbTjCrptVO> crptList = this.fixCrptList;
        switch (changeType) {
            case 0:
                for (int i = 0, crptListSize = crptList.size(); i < crptListSize; i++) {
                    TbTjCrptVO crptVO = crptList.get(i);
                    if (crptVO.getCrptSel() == null) {
                        continue;
                    }
                    if (changeIndex != i) {
                        crptVO.setCrptSel(false);
                    }
                }
                break;
            case 1:
                for (int i = 0, crptListSize = crptList.size(); i < crptListSize; i++) {
                    TbTjCrptVO crptVO = crptList.get(i);
                    if (crptVO.getZoneSel() == null) {
                        continue;
                    }
                    if (changeIndex != i) {
                        crptVO.setZoneSel(false);
                    }
                }
                break;
            case 2:
                for (int i = 0, crptListSize = crptList.size(); i < crptListSize; i++) {
                    TbTjCrptVO crptVO = crptList.get(i);
                    if (crptVO.getNameSel() == null) {
                        continue;
                    }
                    if (changeIndex != i) {
                        crptVO.setNameSel(false);
                    }
                }
                break;
            case 3:
                for (int i = 0, crptListSize = crptList.size(); i < crptListSize; i++) {
                    TbTjCrptVO crptVO = crptList.get(i);
                    if (crptVO.getInstitutionCodeSel() == null) {
                        continue;
                    }
                    if (changeIndex != i) {
                        crptVO.setInstitutionCodeSel(false);
                    }
                }
                break;
            case 4:
                for (int i = 0, crptListSize = crptList.size(); i < crptListSize; i++) {
                    TbTjCrptVO crptVO = crptList.get(i);
                    if (crptVO.getAddressSel() == null) {
                        continue;
                    }
                    if (changeIndex != i) {
                        crptVO.setAddressSel(false);
                    }
                }
                break;
            case 5:
                for (int i = 0, crptListSize = crptList.size(); i < crptListSize; i++) {
                    TbTjCrptVO crptVO = crptList.get(i);
                    if (crptVO.getEnrolAddressSel() == null) {
                        continue;
                    }
                    if (changeIndex != i) {
                        crptVO.setEnrolAddressSel(false);
                    }
                }
                break;
            case 6:
                for (int i = 0, crptListSize = crptList.size(); i < crptListSize; i++) {
                    TbTjCrptVO crptVO = crptList.get(i);
                    if (crptVO.getIndusTypeSel() == null) {
                        continue;
                    }
                    if (changeIndex != i) {
                        crptVO.setIndusTypeSel(false);
                    }
                }
                break;
            case 7:
                for (int i = 0, crptListSize = crptList.size(); i < crptListSize; i++) {
                    TbTjCrptVO crptVO = crptList.get(i);
                    if (crptVO.getEconomySel() == null) {
                        continue;
                    }
                    if (changeIndex != i) {
                        crptVO.setEconomySel(false);
                    }
                }
                break;
            case 8:
                for (int i = 0, crptListSize = crptList.size(); i < crptListSize; i++) {
                    TbTjCrptVO crptVO = crptList.get(i);
                    if (crptVO.getCrptSizeSel() == null) {
                        continue;
                    }
                    if (changeIndex != i) {
                        crptVO.setCrptSizeSel(false);
                    }
                }
                break;
            case 9:
                for (int i = 0, crptListSize = crptList.size(); i < crptListSize; i++) {
                    TbTjCrptVO crptVO = crptList.get(i);
                    if (crptVO.getLinkMan2Sel() == null) {
                        continue;
                    }
                    if (changeIndex != i) {
                        crptVO.setLinkMan2Sel(false);
                    }
                }
                break;
            case 10:
                for (int i = 0, crptListSize = crptList.size(); i < crptListSize; i++) {
                    TbTjCrptVO crptVO = crptList.get(i);
                    if (crptVO.getLinkPhone2Sel() == null) {
                        continue;
                    }
                    if (changeIndex != i) {
                        crptVO.setLinkPhone2Sel(false);
                    }
                }
                break;
        }
    }

    public void beforeFixAction() {
        if (verifyFixFailed()) {
            return;
        }
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("PF('FixConfirmDialog').show()");
    }

    /**
     * 修订操作
     */
    public void fixAction() {
        if (verifyFixFailed()) {
            return;
        }
        List<Integer> crptRidList = new ArrayList<>();
        for (TbTjCrptVO crptVO : this.fixCrptList) {
            crptRidList.add(crptVO.getCrpt().getRid());
        }
        try {
            this.crptIntegratedShowService.createCrptFixTask(pakTdTjCrptTask(), crptRidList);
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("修订失败！");
        }
        searchAction();
        super.backAction();
        RequestContext.getCurrentInstance().update("page_view");
        RequestContext.getCurrentInstance().execute("datatableOffClick();");
        JsfUtil.addSuccessMessage("单位信息修订任务进行中，请耐心等待！");
    }

    /**
     * 封装修订任务
     *
     * @return 修订任务
     */
    public TdTjCrptTask pakTdTjCrptTask() {
        TdTjCrptTask crptTask = new TdTjCrptTask();
        crptTask.setCrptTaskSubList(new ArrayList<TdTjCrptTaskSub>());
        for (TbTjCrptVO crptVO : this.fixCrptList) {
            TbTjCrpt voCrpt = crptVO.getCrpt();
            if (ObjectUtil.convert(Boolean.class, crptVO.getCrptSel(), false)) {
                crptTask.setFkByNewestCrptId(new TbTjCrpt(voCrpt.getRid()));
                crptTask.setState(0);
                crptTask.setFkByOperPsnId(new TsUserInfo(Global.getUser().getRid()));
                crptTask.setOperDate(new Date());
            } else {
                TdTjCrptTaskSub crptTaskSub = new TdTjCrptTaskSub();
                crptTaskSub.setFkByMainId(crptTask);
                crptTaskSub.setFkByStopCrptId(new TbTjCrpt(voCrpt.getRid()));
                crptTaskSub.setCrptName(voCrpt.getCrptName());
                crptTaskSub.setCreditCode(voCrpt.getInstitutionCode());
                this.commService.preEntity(crptTaskSub);
                crptTask.getCrptTaskSubList().add(crptTaskSub);
            }
            pakCrptTaskForCrpt(crptTask, crptVO, false);
        }
        return crptTask;
    }

    /**
     * 封装修订任务主表企业信息，只封装已选择的
     *
     * @param crptTask 修订任务主表
     * @param crptVO   企业信息
     */
    public void pakCrptTaskForCrpt(TdTjCrptTask crptTask, TbTjCrptVO crptVO, boolean saveFull) {
        TbTjCrpt voCrpt = crptVO.getCrpt();
        if (ObjectUtil.convert(Boolean.class, crptVO.getNameSel(), false)) {
            crptTask.setCrptName(voCrpt.getCrptName());
        }
        if (ObjectUtil.convert(Boolean.class, crptVO.getZoneSel(), false)) {
            if (saveFull) {
                crptTask.setFkByZoneId(voCrpt.getTsZoneByZoneId());
            } else {
                crptTask.setFkByZoneId(new TsZone(voCrpt.getTsZoneByZoneId().getRid()));
            }
        }
        if (ObjectUtil.convert(Boolean.class, crptVO.getInstitutionCodeSel(), false)) {
            crptTask.setCreditCode(voCrpt.getInstitutionCode());
        }
        if (ObjectUtil.convert(Boolean.class, crptVO.getAddressSel(), false)) {
            crptTask.setAddress(voCrpt.getAddress());
        }
        if (ObjectUtil.convert(Boolean.class, crptVO.getEnrolAddressSel(), false)) {
            crptTask.setEnrolAddress(voCrpt.getEnrolAddress());
        }
        if (ObjectUtil.convert(Boolean.class, crptVO.getIndusTypeSel(), false)) {
            TsSimpleCode simpleCode;
            if (saveFull) {
                simpleCode = voCrpt.getTsSimpleCodeByIndusTypeId();
            } else {
                simpleCode = new TsSimpleCode(voCrpt.getTsSimpleCodeByIndusTypeId().getRid());
            }
            crptTask.setFkByIndusTypeId(simpleCode);
        }
        if (ObjectUtil.convert(Boolean.class, crptVO.getEconomySel(), false)) {
            TsSimpleCode simpleCode;
            if (saveFull) {
                simpleCode = voCrpt.getTsSimpleCodeByEconomyId();
            } else {
                simpleCode = new TsSimpleCode(voCrpt.getTsSimpleCodeByEconomyId().getRid());
            }
            crptTask.setFkByEconomyId(simpleCode);
        }
        if (ObjectUtil.convert(Boolean.class, crptVO.getCrptSizeSel(), false)) {
            TsSimpleCode simpleCode;
            if (saveFull) {
                simpleCode = voCrpt.getTsSimpleCodeByCrptSizeId();
            } else {
                simpleCode = new TsSimpleCode(voCrpt.getTsSimpleCodeByCrptSizeId().getRid());
            }
            crptTask.setFkByCrptSizeId(simpleCode);
        }
        if (ObjectUtil.convert(Boolean.class, crptVO.getLinkMan2Sel(), false)) {
            crptTask.setLinkMan(voCrpt.getLinkman2());
        }
        if (ObjectUtil.convert(Boolean.class, crptVO.getLinkPhone2Sel(), false)) {
            crptTask.setLinkPhone(voCrpt.getLinkphone2());
        }
    }

    /**
     * 修订前验证
     *
     * @return 验证失败返回true
     */
    private boolean verifyFixFailed() {
        boolean flag = false;
        TbTjCrpt selCrpt = null;
        for (TbTjCrptVO crptVO : this.fixCrptList) {
            if (ObjectUtil.convert(Boolean.class, crptVO.getCrptSel(), false)) {
                selCrpt = crptVO.getCrpt();
                break;
            }
        }
        if (selCrpt == null) {
            JsfUtil.addErrorMessage("必须选择一个企业记录！");
            return true;
        }
        //页面企业RID
        List<Integer> crptRidList = new ArrayList<>();
        TdTjCrptTask crptTask = new TdTjCrptTask();
        crptTask.setCrptTaskSubList(new ArrayList<TdTjCrptTaskSub>());
        crptTask.setCrptName(selCrpt.getCrptName());
        crptTask.setFkByZoneId(selCrpt.getTsZoneByZoneId());
        crptTask.setCreditCode(selCrpt.getInstitutionCode());
        crptTask.setAddress(selCrpt.getAddress());
        crptTask.setEnrolAddress(selCrpt.getEnrolAddress());
        crptTask.setFkByIndusTypeId(selCrpt.getTsSimpleCodeByIndusTypeId());
        crptTask.setFkByEconomyId(selCrpt.getTsSimpleCodeByEconomyId());
        crptTask.setFkByCrptSizeId(selCrpt.getTsSimpleCodeByCrptSizeId());
        crptTask.setLinkMan(selCrpt.getLinkman2());
        crptTask.setLinkPhone(selCrpt.getLinkphone2());

        for (TbTjCrptVO crptVO : this.fixCrptList) {
            TbTjCrpt voCrpt = crptVO.getCrpt();
            crptRidList.add(voCrpt.getRid());
            if (ObjectUtil.convert(Boolean.class, crptVO.getCrptSel(), false)) {
                crptTask.setFkByNewestCrptId(new TbTjCrpt(voCrpt.getRid()));
                crptTask.setState(0);
                crptTask.setFkByOperPsnId(new TsUserInfo(Global.getUser().getRid()));
                crptTask.setOperDate(new Date());
            } else {
                TdTjCrptTaskSub crptTaskSub = new TdTjCrptTaskSub();
                crptTaskSub.setFkByMainId(crptTask);
                crptTaskSub.setFkByStopCrptId(new TbTjCrpt(voCrpt.getRid()));
                crptTaskSub.setCrptName(voCrpt.getCrptName());
                crptTaskSub.setCreditCode(voCrpt.getInstitutionCode());
                this.commService.preEntity(crptTaskSub);
                crptTask.getCrptTaskSubList().add(crptTaskSub);
            }
            pakCrptTaskForCrpt(crptTask, crptVO, true);
        }

        boolean isStreet = crptTask.getFkByZoneId() != null && crptTask.getFkByZoneId().getRealZoneType() != null
                && crptTask.getFkByZoneId().getRealZoneType() < 5;
        if (isStreet) {
            JsfUtil.addErrorMessage("地区必须选择至街道！");
            flag = true;
        }
        if (StringUtils.isNotBlank(crptTask.getCreditCode()) && !StringUtils.isCreditCode(crptTask.getCreditCode())) {
            JsfUtil.addErrorMessage("社会信用代码格式不正确！");
            flag = true;
        }
        boolean isIndusTypeEmpty = ObjectUtil.isEmpty(crptTask.getFkByIndusTypeId())
                || ObjectUtil.isEmpty(crptTask.getFkByIndusTypeId().getRid());
        if (isIndusTypeEmpty) {
            JsfUtil.addErrorMessage("行业类别不能为空！");
            flag = true;
        } else if (!this.validateIndusTypeIds.contains(crptTask.getFkByIndusTypeId().getRid())) {
            JsfUtil.addErrorMessage("行业类别必须选择最末级！");
            flag = true;
        }
        boolean isEconomyEmpty = ObjectUtil.isEmpty(crptTask.getFkByEconomyId())
                || ObjectUtil.isEmpty(crptTask.getFkByEconomyId().getRid());
        if (isEconomyEmpty) {
            JsfUtil.addErrorMessage("经济类型不能为空！");
            flag = true;
        } else if (!this.validateEconomyIds.contains(crptTask.getFkByEconomyId().getRid())) {
            JsfUtil.addErrorMessage("经济类型必须选择最末级！");
            flag = true;
        }
        boolean isCrptSizeEmpty = ObjectUtil.isEmpty(crptTask.getFkByCrptSizeId())
                || ObjectUtil.isEmpty(crptTask.getFkByCrptSizeId().getRid());
        if (isCrptSizeEmpty) {
            JsfUtil.addErrorMessage("企业规模不能为空！");
            flag = true;
        }
        if (StringUtils.isNotBlank(crptTask.getLinkPhone()) && !StringUtils.vertyPhone(crptTask.getLinkPhone())) {
            JsfUtil.addErrorMessage("联系电话格式不正确！");
            flag = true;
        }
        if (new Short((short) 0).equals(selCrpt.getIfSubOrg())) {
            if (this.crptIntegratedShowService.hasSameCreditCode(crptTask.getCreditCode(), crptRidList)) {
                JsfUtil.addErrorMessage("主体机构社会信用代码唯一！");
                flag = true;
            }
            if (this.crptIntegratedShowService.hasSameNameForSubOrg(crptTask.getCrptName(), crptRidList)) {
                JsfUtil.addErrorMessage("当前选择的单位名称与所有分支机构的单位名称不能重复！");
                flag = true;
            }
        } else {
            if (this.crptIntegratedShowService.hasSameNameCreditCode(crptTask.getCrptName(), crptTask.getCreditCode(), crptRidList)) {
                JsfUtil.addErrorMessage("单位名称与社会信用代码在系统内唯一！");
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 打开职业健康检查数据弹出框
     *
     * @param year 年份
     */
    public void openBhkDataDialogAction(String year) {
        this.crptIntegratedShowBhkDataBean = new GsCrptIntegratedShowBhkDataBean(year, this.rid, "tabView:viewForm:bhkPsnDataTable");
        this.crptIntegratedShowBhkDataBean.searchAction();
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                .findComponent("tabView:viewForm:bhkPsnDataTable");
        if (dataTable != null) {
            dataTable.setRows(10);
            dataTable.setFirst(0);
        }
        RequestContext.getCurrentInstance().execute("PF('BhkDataDialog').show();");
        RequestContext.getCurrentInstance().update("tabView:viewForm:bhkPsnDataTable");
    }

    /**
     * 打开职业病诊断数据弹出框
     *
     * @param year 年份
     */
    public void openDiagDataDialogAction(String year) {
        this.diagYearDataList = this.crptIntegratedShowService.selectDiagYearDataList(year, this.rid);
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                .findComponent("tabView:viewForm:diagPsnDataTable");
        if (dataTable != null) {
            dataTable.setRows(10);
            dataTable.setFirst(0);
        }
        RequestContext.getCurrentInstance().execute("PF('DiagDataDialog').show();");
        RequestContext.getCurrentInstance().update("tabView:viewForm:diagPsnDataTable");
    }

    /**
     * <p>方法描述：切换统计图类型 </p>
     *
     * @MethodAuthor： pw 2023/6/27
     **/
    public void changeChartType() {
        this.chartJson = this.chartJsonMap.get(this.chartType);
        this.chartDataSize = this.chartDataSizeMap.get(this.chartType);
    }

    /**
     * <p>方法描述： 统计图初始化 </p>
     *
     * @MethodAuthor： pw 2023/6/27
     **/
    private void initChart() {
        this.chartType = 1;
        this.chartJsonMap = new HashMap<>();
        this.chartDataSize = 0;
        this.chartDataSizeMap = new HashMap<>();
        this.initChartJsonMap();
        this.changeChartType();
    }

    /**
     * <p>方法描述：缓存各类型统计图 </p>
     *
     * @MethodAuthor： pw 2023/6/27
     **/
    private void initChartJsonMap() {
        TsZone manageZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        this.generateZoneChart(manageZone);
        this.generateIndusTypeChart(manageZone);
        this.generateEconomyChart(manageZone);
        this.generateCrptSizeChart(manageZone);
        this.generateDrugChart(manageZone);
        this.generateCaChart(manageZone);
    }

    /**
     * <p>方法描述：生成地区的统计图 </p>
     *
     * @MethodAuthor： pw 2023/6/27
     **/
    private void generateZoneChart(TsZone manageZone) {
        this.chartJsonMap.put(1, "");
        this.chartDataSizeMap.put(1, 0);
        if (null == manageZone) {
            return;
        }
        String zoneGb = manageZone.getZoneGb();
        //截取后的地区编码
        String subZoneGb = ZoneUtil.zoneSelect(zoneGb);
        Integer subZoneSize = subZoneGb.length();
        //非 省市区
        if (subZoneSize != 2 && subZoneSize != 4 && subZoneSize != 6) {
            return;
        }
        String sqlBuffer = " SELECT  M.* FROM ( SELECT" +
                (subZoneSize == 2 ? " SUBSTR(T1.ZONE_GB, 0, 4) || '000000'  " : " SUBSTR(T1.ZONE_GB, 0,6)||'0000' ") +
                " AS ZONEGB, COUNT(T.RID) AS CURNUM " +
                " FROM TB_TJ_CRPT T " +
                " LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID " +
                " WHERE T.DEL_MARK = 0 AND NVL(T.OPERATION_STATUS,1)=1 " + " AND T1.ZONE_GB LIKE :zoneGb " +
                " GROUP BY  " +
                (subZoneSize == 2 ? " SUBSTR(T1.ZONE_GB, 0, 4) || '000000' " : " SUBSTR(T1.ZONE_GB, 0,6)||'0000' ") +
                " ) M ORDER BY M.CURNUM DESC, M.ZONEGB";
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("zoneGb", subZoneGb + "%");
        List<Object[]> queryResult = this.commService.findDataBySqlNoPage(sqlBuffer, paramsMap);
        if (CollectionUtils.isEmpty(queryResult)) {
            return;
        }
        List<Object[]> fillList = new ArrayList<>();
        Map<String, Object> tmpMap = new HashMap<>();
        for (Object[] objArr : queryResult) {
            String curGb = null == objArr[0] ? null : objArr[0].toString();
            if (StringUtils.isBlank(curGb)) {
                continue;
            }
            //省、市 对应本省或者本市 丢弃
            boolean flag = (subZoneSize == 2 || subZoneSize == 4) && curGb.equals(zoneGb);
            if (flag) {
                continue;
            }
            TsZone tsZone = this.chartZoneMap.get(curGb);
            //可能出现停用的地区
            if (null == tsZone) {
                tmpMap.put("zoneGb", curGb);
                List<TsZone> tmpList = this.commService.findDataByHqlNoPage(" select t from TsZone t where t.zoneGb=:zoneGb order by t.ifReveal desc ", tmpMap);
                if (CollectionUtils.isEmpty(tmpList)) {
                    continue;
                }
                tsZone = tmpList.get(0);
                this.chartZoneMap.put(curGb, tsZone);
            }
            Object[] fillArr = new Object[2];
            fillArr[0] = null == tsZone.getZoneShortName() ? "" : tsZone.getZoneShortName();
            fillArr[1] = objArr[1];
            fillList.add(fillArr);
        }
        this.generateLineChart(fillList, "地区", 1);
    }

    /**
     * <p>方法描述：生成行业类别的统计图 </p>
     *
     * @MethodAuthor： pw 2023/6/27
     **/
    private void generateIndusTypeChart(TsZone manageZone) {
        this.chartJsonMap.put(2, "");
        this.chartDataSizeMap.put(2, 0);
        if (null == manageZone) {
            return;
        }
        String zoneGb = manageZone.getZoneGb();
        //截取后的地区编码
        String subZoneGb = ZoneUtil.zoneSelect(zoneGb);
        //行业类别大类
        String sqlBuffer = " SELECT M.CODENO, M.CURNUM FROM ( " +
                " SELECT CASE WHEN INSTR(T2.CODE_LEVEL_NO, '.') = 0 THEN T2.CODE_LEVEL_NO ELSE SUBSTR(T2.CODE_LEVEL_NO, 0, INSTR(T2.CODE_LEVEL_NO, '.') - 1) END AS CODENO, " +
                " COUNT(T.RID) AS CURNUM " +
                " FROM TB_TJ_CRPT T " +
                " LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID " +
                " LEFT JOIN TS_SIMPLE_CODE T2 ON T.INDUS_TYPE_ID = T2.RID " +
                " WHERE T.DEL_MARK = 0 AND NVL(T.OPERATION_STATUS, 1) = 1 " +
                "   AND T1.ZONE_GB LIKE :zoneGb " +
                " GROUP BY CASE WHEN INSTR(T2.CODE_LEVEL_NO, '.') = 0 THEN T2.CODE_LEVEL_NO ELSE SUBSTR(T2.CODE_LEVEL_NO, 0, INSTR(T2.CODE_LEVEL_NO, '.') - 1) END " +
                " ) M " +
                " ORDER BY M.CURNUM DESC, M.CODENO ";

        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("zoneGb", subZoneGb + "%");
        List<Object[]> queryResult = this.commService.findDataBySqlNoPage(sqlBuffer, paramsMap);
        if (CollectionUtils.isEmpty(queryResult)) {
            return;
        }
        List<Object[]> fillList = new ArrayList<>();
        for (Object[] objArr : queryResult) {
            String codeNo = null == objArr[0] ? null : objArr[0].toString();
            TsSimpleCode simpleCode = this.findChartSimpleCode("5002", codeNo);
            if (null == simpleCode) {
                continue;
            }
            Object[] fillArr = new Object[2];
            fillArr[0] = null == simpleCode.getCodeName() ? "" : simpleCode.getCodeName();
            fillArr[1] = objArr[1];
            fillList.add(fillArr);
        }
        this.generateLineChart(fillList, "行业类别", 2);
    }

    /**
     * <p>方法描述： 生成经济类型的统计图 </p>
     *
     * @MethodAuthor： pw 2023/6/27
     **/
    private void generateEconomyChart(TsZone manageZone) {
        this.chartJsonMap.put(3, "");
        this.chartDataSizeMap.put(3, 0);
        if (null == manageZone) {
            return;
        }
        String zoneGb = manageZone.getZoneGb();
        //截取后的地区编码
        String subZoneGb = ZoneUtil.zoneSelect(zoneGb);
        //二级类别
        String sqlBuffer = " SELECT M.CODENO ,M.CURNUM FROM ( " +
                " SELECT CASE WHEN INSTR(SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1), '.')=0 THEN " +
                " SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1) ELSE " +
                " SUBSTR(SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1),0,INSTR(SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1), '.')-1) END AS CODENO, " +
                " COUNT(T.RID) AS CURNUM  " +
                " FROM TB_TJ_CRPT T " +
                " LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID " +
                " LEFT JOIN TS_SIMPLE_CODE T2 ON T.ECONOMY_ID = T2.RID " +
                " WHERE T.DEL_MARK = 0 AND NVL(T.OPERATION_STATUS, 1) = 1 AND T1.ZONE_GB LIKE :zoneGb AND INSTR( T2.CODE_LEVEL_NO, '.') > 0  " +
                " GROUP BY CASE WHEN INSTR(SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1), '.')=0 THEN " +
                "   SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1) ELSE " +
                "  SUBSTR(SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1),0,INSTR(SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1), '.')-1) END " +
                " ) M " +
                " ORDER BY M.CURNUM DESC,M.CODENO  ";
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("zoneGb", subZoneGb + "%");
        List<Object[]> queryResult = this.commService.findDataBySqlNoPage(sqlBuffer, paramsMap);
        if (CollectionUtils.isEmpty(queryResult)) {
            return;
        }
        List<Object[]> fillList = new ArrayList<>();
        for (Object[] objArr : queryResult) {
            String codeNo = null == objArr[0] ? null : objArr[0].toString();
            TsSimpleCode simpleCode = this.findChartSimpleCode("5003", codeNo);
            if (null == simpleCode) {
                continue;
            }
            Object[] fillArr = new Object[2];
            fillArr[0] = null == simpleCode.getCodeName() ? "" : simpleCode.getCodeName();
            fillArr[1] = objArr[1];
            fillList.add(fillArr);
        }
        this.generateLineChart(fillList, "经济类型", 3);
    }

    /**
     * <p>方法描述： 生成企业规模的统计图 </p>
     *
     * @MethodAuthor： pw 2023/6/27
     **/
    private void generateCrptSizeChart(TsZone manageZone) {
        this.chartJsonMap.put(4, "");
        this.chartDataSizeMap.put(4, 0);
        if (null == manageZone) {
            return;
        }
        String zoneGb = manageZone.getZoneGb();
        //截取后的地区编码
        String subZoneGb = ZoneUtil.zoneSelect(zoneGb);
        String sqlBuffer = " SELECT M1.CODE_NAME, NVL(M.CURNUM, 0) " +
                "   FROM TS_SIMPLE_CODE M1 " +
                "   INNER JOIN TS_CODE_TYPE M2 ON M1.CODE_TYPE_ID=M2.RID " +
                "   LEFT JOIN (" +
                "    SELECT T2.RID,COUNT(T.RID) AS CURNUM " +
                "    FROM TS_SIMPLE_CODE T2 " +
                "    LEFT JOIN TB_TJ_CRPT T ON T.CRPT_SIZE_ID = T2.RID " +
                "    LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID " +
                "    WHERE T.DEL_MARK = 0 AND NVL(T.OPERATION_STATUS, 1) = 1 AND T1.ZONE_GB LIKE :zoneGb " +
                "   GROUP BY T2.RID) M ON M1.RID = M.RID" +
                "  WHERE M2.CODE_TYPE_NAME='5004' AND (M1.IF_REVEAL = '1' OR M.CURNUM > 0) " +
                "  ORDER BY M1.NUM ";
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("zoneGb", subZoneGb + "%");
        List<Object[]> queryResult = this.commService.findDataBySqlNoPage(sqlBuffer, paramsMap);
        if (CollectionUtils.isEmpty(queryResult)) {
            return;
        }
        this.generatePieChart(queryResult, 4);
    }

    /**
     * <p>方法描述：生成高毒的统计图 </p>
     *
     * @MethodAuthor： pw 2023/6/27
     **/
    private void generateDrugChart(TsZone manageZone) {
        this.chartJsonMap.put(5, "");
        this.chartDataSizeMap.put(5, 0);
        if (null == manageZone) {
            return;
        }
        String zoneGb = manageZone.getZoneGb();
        this.executeDrugAndCaData(zoneGb, 5);
    }

    /**
     * <p>方法描述： 生成致癌的统计图</p>
     *
     * @MethodAuthor： pw 2023/6/28
     **/
    private void generateCaChart(TsZone manageZone) {
        this.chartJsonMap.put(6, "");
        this.chartDataSizeMap.put(6, 0);
        if (null == manageZone) {
            return;
        }
        String zoneGb = manageZone.getZoneGb();
        this.executeDrugAndCaData(zoneGb, 6);
    }

    /**
     * <p>方法描述： 高毒、致癌通用处理 </p>
     *
     * @MethodAuthor： pw 2023/6/28
     **/
    private void executeDrugAndCaData(String zoneGb, Integer curChartType) {
        //截取后的地区编码
        String subZoneGb = ZoneUtil.zoneSelect(zoneGb);
        Integer subZoneSize = subZoneGb.length();
        //非 省市区
        if (subZoneSize != 2 && subZoneSize != 4 && subZoneSize != 6) {
            return;
        }
        String sqlBuffer = " SELECT M.ZONEGB,M.CURNUM FROM ( SELECT" +
                (subZoneSize == 2 ? " SUBSTR(T1.ZONE_GB, 0, 4) || '000000'  " : " SUBSTR(T1.ZONE_GB, 0,6)||'0000' ") +
                " AS ZONEGB, COUNT(T.RID) AS CURNUM " +
                " FROM TB_TJ_CRPT T " +
                " LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID " +
                " LEFT JOIN TB_TJ_CRPT_BADRSN T2 ON T2.CRPT_ID = T.RID " +
                " LEFT JOIN TS_SIMPLE_CODE T3 ON T2.BADRSN_ID = T3.RID " +
                " WHERE T.DEL_MARK = 0 " +
                " AND NVL(T.OPERATION_STATUS, 1) = 1 " +
                " AND T3.EXTENDS7 like :ext7 " +
                " AND T1.ZONE_GB LIKE :zoneGb " +
                " GROUP BY " +
                (subZoneSize == 2 ? " SUBSTR(T1.ZONE_GB, 0, 4) || '000000' " : " SUBSTR(T1.ZONE_GB, 0,6)||'0000' ") +
                "  ) M " +
                "  ORDER BY M.CURNUM DESC, M.ZONEGB ";

        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("zoneGb", subZoneGb + "%");
        paramsMap.put("ext7", "%" + (5 == curChartType ? "1" : "2") + "%");
        List<Object[]> queryResult = this.commService.findDataBySqlNoPage(sqlBuffer, paramsMap);
        if (CollectionUtils.isEmpty(queryResult)) {
            return;
        }
        List<Object[]> fillList = new ArrayList<>();
        Map<String, Object> tmpMap = new HashMap<>();
        for (Object[] objArr : queryResult) {
            String curGb = null == objArr[0] ? null : objArr[0].toString();
            if (StringUtils.isBlank(curGb)) {
                continue;
            }
            //省、市 对应本省或者本市 丢弃
            boolean flag = (subZoneSize == 2 || subZoneSize == 4) && curGb.equals(zoneGb);
            if (flag) {
                continue;
            }
            TsZone tsZone = this.chartZoneMap.get(curGb);
            //可能出现停用的地区
            if (null == tsZone) {
                tmpMap.put("zoneGb", curGb);
                List<TsZone> tmpList = this.commService.findDataByHqlNoPage(" select t from TsZone t where t.zoneGb=:zoneGb order by t.ifReveal desc ", tmpMap);
                if (CollectionUtils.isEmpty(tmpList)) {
                    continue;
                }
                tsZone = tmpList.get(0);
                this.chartZoneMap.put(curGb, tsZone);
            }
            Object[] fillArr = new Object[2];
            fillArr[0] = null == tsZone.getZoneShortName() ? "" : tsZone.getZoneShortName();
            fillArr[1] = objArr[1];
            fillList.add(fillArr);
        }
        this.generateLineChart(fillList, "地区", curChartType);
    }

    /**
     * <p>方法描述： 通过类型编码与码表编码获取码表对象 </p>
     *
     * @MethodAuthor： pw 2023/6/28
     **/
    private TsSimpleCode findChartSimpleCode(String codeTypeName, String codeNo) {
        if (StringUtils.isBlank(codeTypeName) || StringUtils.isBlank(codeNo)) {
            return null;
        }
        Map<String, TsSimpleCode> fillMap = this.chartSimpleCodeMap.get(codeTypeName);
        TsSimpleCode simpleCode = null == fillMap ? null : fillMap.get(codeNo);
        if (null == simpleCode) {
            Map<String, Object> tmpParamMap = new HashMap<>();
            tmpParamMap.put("codeTypeName", codeTypeName);
            tmpParamMap.put("codeNo", codeNo);
            List<TsSimpleCode> tmpList = this.commService.findDataByHqlNoPage(" select t from TsSimpleCode t where t.tsCodeType.codeTypeName=:codeTypeName and t.codeNo=:codeNo order by t.ifReveal desc ", tmpParamMap);
            if (CollectionUtils.isEmpty(tmpList)) {
                return null;
            }
            simpleCode = tmpList.get(0);
            if (null == fillMap) {
                fillMap = new HashMap<>();
            }
            fillMap.put(codeNo, simpleCode);
            this.chartSimpleCodeMap.put(codeTypeName, fillMap);
        }
        return simpleCode;
    }

    /**
     * <p>方法描述：生成柱状图 </p>
     *
     * @MethodAuthor： pw 2023/6/27
     **/
    private void generateLineChart(List<Object[]> dataList, String xName, Integer curChartType) {
        GsonOption option = new GsonOption();
        Grid grid = new Grid();
        grid.setShow(false);
        grid.setHeight("150");
        //设置距离上边与左边的距离
        grid.setY(30);
        //x轴距左边距离
        grid.setX(60);
        //x轴距右边距离
        grid.setX2(80);
        option.setGrid(grid);
        //标题不显示
        option.title().setShow(false);

        //标注
        option.legend().data();
        option.legend().y(35);
        option.legend().x(X.center);

        //移动到柱状图 显示对应数量
        option.tooltip().trigger(Trigger.item);
        option.tooltip().axisPointer().type(PointerType.none);

        //定义工具
        option.toolbox().show(false);
        option.calculable(true);

        //纵坐标
        ValueAxis v = new ValueAxis();
        v.show(true);
        v.splitLine().show(false);
        v.splitArea().show(false);
        v.axisTick().show(false);
        v.setName("单位数量");
        v.min(0);

        //横坐标
        CategoryAxis xAxis = new CategoryAxis();
        xAxis.setType(AxisType.category);
        xAxis.setShow(true);
        xAxis.axisLabel().formatter("function(value){return (value.length >5 ? (value.slice(0,4)+'...') : value )}");
        //interval(0) 避免缩小分辨率导致x轴文本不显示
        //xAxis.axisLabel().interval(0).formatter("function(value){ if(null == value || undefined == value || value.length <=5){ return value } var len = (value.length%10 > 0 ? 1 : 0) + parseInt(value.length); var result = ''; for(var i=0;i<len;i++){ result = result.concat(value.slice(i*5,(i+1)*5)); if(i != len-1){ result = result.concat('\n') }} return result}");
        //去除栏栅
        SplitLine splitLine = new SplitLine();
        splitLine.setShow(false);
        xAxis.setSplitLine(splitLine);
        xAxis.setName(xName);
        option.xAxis(xAxis);

        List<Object> data1 = new ArrayList<>();

        int max = 0;
        if (!CollectionUtils.isEmpty(dataList)) {
            List<Object> xAxisDataList = new ArrayList<>();
            for (Object[] obj : dataList) {
                xAxisDataList.add(null == obj[0] ? "" : obj[0].toString());
                data1.add(null == obj[1] ? "" : obj[1].toString());
                Integer num = null == obj[1] ? null : Integer.parseInt(obj[1].toString());
                if (null != num && num > max) {
                    max = num;
                }
            }
            xAxis.data(xAxisDataList.toArray());
        }
        //设置y轴数值固定
        //v.max(max+10 - max%10);
        v.max(createMaxVal(max));
        option.yAxis(v);

        Bar hwBar1 = new Bar();
        hwBar1.stack("单位数量");
        hwBar1.setData(data1);
        hwBar1.setBarWidth(30);
        hwBar1.itemStyle().normal().label().show(true).position(Position.top).formatter("{c}");
        option.series(hwBar1);
        String result = option.toString();
        if (StringUtils.isNotBlank(result)) {
            this.chartJsonMap.put(curChartType, result);
            this.chartDataSizeMap.put(curChartType, CollectionUtils.isEmpty(dataList) ? 0 : dataList.size());
        }
    }

    /**
     * <p>方法描述： 避免最大2650 y轴最大2500 </p>
     *
     * @MethodAuthor： pw 2023/7/1
     **/
    private Integer createMaxVal(int max) {
        int size = String.valueOf(max).length() - 1;
        int base = 1;
        for (int i = 0; i < size; i++) {
            base = base * 10;
        }
        if (max % base != 0) {
            return max - (max % base) + base;
        } else {
            return max;
        }
    }


    /**
     * <p>方法描述： 生成饼图 </p>
     *
     * @MethodAuthor： pw 2023/6/28
     **/
    private void generatePieChart(List<Object[]> dataList, Integer curChartType) {
        GsonOption option = new GsonOption();
        //标题不显示
        option.title().setShow(false);

        //异步触发
        option.tooltip().trigger(Trigger.item);
        option.tooltip().formatter("{b}<br/>数量：{c} <br/>占比：{d}%");
        //工具栏
        option.toolbox().show(false);
        //option.legend()
        //数据
        Pie pie2 = new Pie();
        pie2.itemStyle().normal().label().show(true);
        //pie2.itemStyle().normal().label().formatter("{b} : {d}%");
        pie2.itemStyle().normal().label().formatter("{b} : {c} ({d}%)");
        pie2.itemStyle().normal().label().position(Position.outer);
        //pie2.itemStyle().normal().label().setInterval(0);
        pie2.itemStyle().normal().labelLine().show(true);
        pie2.itemStyle().normal().labelLine().setLength(2);
        //设置饼图大小 设置一个百分比 实心饼图 比如80% 设置数组那么前后差形成空心饼图 注意 后边的一定要比前边的大 因为标线是从后边的边框开始的
        //pie2.radius(new String[]{"60%","30%"});
        pie2.radius(new String[]{"30%", "60%"});
        //pie2.radius("80%");
        //前 距离左边距离 后边调整上下距离
        pie2.center(new String[]{"50%", "45%"});
        int dataSize = 0;
        List<String> legendDataList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            for (Object[] object : dataList) {
                Data data = new Data();
                String tip = object[0].toString();
                data.name(tip);
                data.value(object[1].toString());
                legendDataList.add(tip);
                pie2.data().add(data);
            }
            dataSize = dataList.size();
        }
        option.legend().orient(Orient.vertical).x("30%").y("13%").data(legendDataList);
        option.series(pie2);
        String result = option.toString();
        if (StringUtils.isNotBlank(result)) {
            this.chartJsonMap.put(curChartType, result);
            this.chartDataSizeMap.put(curChartType, dataSize);
        }
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public List<Object[]> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<Object[]> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchInstitutionCode() {
        return searchInstitutionCode;
    }

    public void setSearchInstitutionCode(String searchInstitutionCode) {
        this.searchInstitutionCode = searchInstitutionCode;
    }

    public String getSelectEconomyIds() {
        return selectEconomyIds;
    }

    public void setSelectEconomyIds(String selectEconomyIds) {
        this.selectEconomyIds = selectEconomyIds;
    }

    public String getSelectEconomyNames() {
        return selectEconomyNames;
    }

    public void setSelectEconomyNames(String selectEconomyNames) {
        this.selectEconomyNames = selectEconomyNames;
    }

    public List<TsSimpleCode> getEconomyList() {
        return economyList;
    }

    public void setEconomyList(List<TsSimpleCode> economyList) {
        this.economyList = economyList;
    }

    public List<Integer> getValidateEconomyIds() {
        return validateEconomyIds;
    }

    public void setValidateEconomyIds(List<Integer> validateEconomyIds) {
        this.validateEconomyIds = validateEconomyIds;
    }

    public String getSelectCrptSizeIds() {
        return selectCrptSizeIds;
    }

    public void setSelectCrptSizeIds(String selectCrptSizeIds) {
        this.selectCrptSizeIds = selectCrptSizeIds;
    }

    public List<TsSimpleCode> getCrptSizeList() {
        return crptSizeList;
    }

    public void setCrptSizeList(List<TsSimpleCode> crptSizeList) {
        this.crptSizeList = crptSizeList;
    }

    public String getSimpleCodeOpType() {
        return simpleCodeOpType;
    }

    public void setSimpleCodeOpType(String simpleCodeOpType) {
        this.simpleCodeOpType = simpleCodeOpType;
    }

    public List<Integer> getValidateIndusTypeIds() {
        return validateIndusTypeIds;
    }

    public void setValidateIndusTypeIds(List<Integer> validateIndusTypeIds) {
        this.validateIndusTypeIds = validateIndusTypeIds;
    }

    public String getSelectIndusTypeIds() {
        return selectIndusTypeIds;
    }

    public void setSelectIndusTypeIds(String selectIndusTypeIds) {
        this.selectIndusTypeIds = selectIndusTypeIds;
    }

    public String getSelectIndusTypeNames() {
        return selectIndusTypeNames;
    }

    public void setSelectIndusTypeNames(String selectIndusTypeNames) {
        this.selectIndusTypeNames = selectIndusTypeNames;
    }

    public List<TsSimpleCode> getIndusList() {
        return indusList;
    }

    public void setIndusList(List<TsSimpleCode> indusList) {
        this.indusList = indusList;
    }

    public String getSelectBadRsnIds() {
        return selectBadRsnIds;
    }

    public void setSelectBadRsnIds(String selectBadRsnIds) {
        this.selectBadRsnIds = selectBadRsnIds;
    }

    public String getSelectBadRsnNames() {
        return selectBadRsnNames;
    }

    public void setSelectBadRsnNames(String selectBadRsnNames) {
        this.selectBadRsnNames = selectBadRsnNames;
    }

    public List<TsSimpleCode> getBadRsnList() {
        return badRsnList;
    }

    public void setBadRsnList(List<TsSimpleCode> badRsnList) {
        this.badRsnList = badRsnList;
    }

    public String[] getEnabledState() {
        return enabledState;
    }

    public void setEnabledState(String[] enabledState) {
        this.enabledState = enabledState;
    }

    public String[] getFixState() {
        return fixState;
    }

    public void setFixState(String[] fixState) {
        this.fixState = fixState;
    }

    public Boolean getHasFix() {
        return hasFix;
    }

    public void setHasFix(Boolean hasFix) {
        this.hasFix = hasFix;
    }

    public String getStopRsn() {
        return stopRsn;
    }

    public void setStopRsn(String stopRsn) {
        this.stopRsn = stopRsn;
    }

    public List<TbTjCrptVO> getFixCrptList() {
        return fixCrptList;
    }

    public void setFixCrptList(List<TbTjCrptVO> fixCrptList) {
        this.fixCrptList = fixCrptList;
    }

    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    public Integer getChangeIndex() {
        return changeIndex;
    }

    public void setChangeIndex(Integer changeIndex) {
        this.changeIndex = changeIndex;
    }

    public Object[] getCrptBaseInfo() {
        return crptBaseInfo;
    }

    public void setCrptBaseInfo(Object[] crptBaseInfo) {
        this.crptBaseInfo = crptBaseInfo;
    }

    public List<Object[]> getOccRiskClassList() {
        return occRiskClassList;
    }

    public void setOccRiskClassList(List<Object[]> occRiskClassList) {
        this.occRiskClassList = occRiskClassList;
    }

    public Integer getShowArchives() {
        return showArchives;
    }

    public void setShowArchives(Integer showArchives) {
        this.showArchives = showArchives;
    }

    public Boolean getHasDeclareDataForTheYear() {
        return hasDeclareDataForTheYear;
    }

    public void setHasDeclareDataForTheYear(Boolean hasDeclareDataForTheYear) {
        this.hasDeclareDataForTheYear = hasDeclareDataForTheYear;
    }

    public Date getDeclareData() {
        return declareData;
    }

    public void setDeclareData(Date declareData) {
        this.declareData = declareData;
    }


    public List<TdZyUnitbasicinfo> getUnitbasicinfoSelList() {
        return unitbasicinfoSelList;
    }

    public void setUnitbasicinfoSelList(List<TdZyUnitbasicinfo> unitbasicinfoSelList) {
        this.unitbasicinfoSelList = unitbasicinfoSelList;
    }

    public Integer getUnitbasicinfoRid() {
        return unitbasicinfoRid;
    }

    public void setUnitbasicinfoRid(Integer unitbasicinfoRid) {
        this.unitbasicinfoRid = unitbasicinfoRid;
    }

    public TdZyUnitbasicinfo getUnitbasicinfo() {
        return unitbasicinfo;
    }

    public void setUnitbasicinfo(TdZyUnitbasicinfo unitbasicinfo) {
        this.unitbasicinfo = unitbasicinfo;
    }

    public Integer getPlaceDate() {
        return placeDate;
    }

    public void setPlaceDate(Integer placeDate) {
        this.placeDate = placeDate;
    }

    public Boolean getPlaceThatYear() {
        return placeThatYear;
    }

    public void setPlaceThatYear(Boolean placeThatYear) {
        this.placeThatYear = placeThatYear;
    }

    public UnitbasicInfo getUnitbasic() {
        return unitbasic;
    }

    public void setUnitbasic(UnitbasicInfo unitbasic) {
        this.unitbasic = unitbasic;
    }

    public List<PlaceInfo> getUnitharmChkInfos() {
        return unitharmChkInfos;
    }

    public void setUnitharmChkInfos(List<PlaceInfo> unitharmChkInfos) {
        this.unitharmChkInfos = unitharmChkInfos;
    }

    public List<PlaceInfo> getUnitHethCusInfos() {
        return unitHethCusInfos;
    }

    public void setUnitHethCusInfos(List<PlaceInfo> unitHethCusInfos) {
        this.unitHethCusInfos = unitHethCusInfos;
    }

    public List<PlaceInfo> getResultProInfos() {
        return resultProInfos;
    }

    public void setResultProInfos(List<PlaceInfo> resultProInfos) {
        this.resultProInfos = resultProInfos;
    }

    public List<Integer> getPlaceDateList() {
        return placeDateList;
    }

    public void setPlaceDateList(List<Integer> placeDateList) {
        this.placeDateList = placeDateList;
    }

    public Integer getChartType() {
        return chartType;
    }

    public void setChartType(Integer chartType) {
        this.chartType = chartType;
    }

    public String getChartJson() {
        return chartJson;
    }

    public void setChartJson(String chartJson) {
        this.chartJson = chartJson;
    }

    public Map<Integer, String> getChartJsonMap() {
        return chartJsonMap;
    }

    public void setChartJsonMap(Map<Integer, String> chartJsonMap) {
        this.chartJsonMap = chartJsonMap;
    }

    public Integer getChartDataSize() {
        return chartDataSize;
    }

    public void setChartDataSize(Integer chartDataSize) {
        this.chartDataSize = chartDataSize;
    }

    public Map<Integer, Integer> getChartDataSizeMap() {
        return chartDataSizeMap;
    }

    public void setChartDataSizeMap(Map<Integer, Integer> chartDataSizeMap) {
        this.chartDataSizeMap = chartDataSizeMap;
    }

    public String getSelectAbnormalInfoIds() {
        return selectAbnormalInfoIds;
    }

    public void setSelectAbnormalInfoIds(String selectAbnormalInfoIds) {
        this.selectAbnormalInfoIds = selectAbnormalInfoIds;
    }

    public List<TsSimpleCode> getAbnormalInfoList() {
        return abnormalInfoList;
    }

    public void setAbnormalInfoList(List<TsSimpleCode> abnormalInfoList) {
        this.abnormalInfoList = abnormalInfoList;
    }

    public String getLimitTime() {
        return limitTime;
    }

    public void setLimitTime(String limitTime) {
        this.limitTime = limitTime;
    }

    public List<TbTjCrptWarnComm> getTbTjCrptWarnCommList() {
        return tbTjCrptWarnCommList;
    }

    public void setTbTjCrptWarnCommList(List<TbTjCrptWarnComm> tbTjCrptWarnCommList) {
        this.tbTjCrptWarnCommList = tbTjCrptWarnCommList;
    }

    public List<Object[]> getOcchethCardList() {
        return occhethCardList;
    }

    public void setOcchethCardList(List<Object[]> occhethCardList) {
        this.occhethCardList = occhethCardList;
    }

    public List<Object[]> getSrvorgCardList() {
        return srvorgCardList;
    }

    public void setSrvorgCardList(List<Object[]> srvorgCardList) {
        this.srvorgCardList = srvorgCardList;
    }

    public Integer getCardRid() {
        return cardRid;
    }

    public void setCardRid(Integer cardRid) {
        this.cardRid = cardRid;
    }
    public List<TsSimpleCode> getWarnResults() {
        return warnResults;
    }

    public void setWarnResults(List<TsSimpleCode> warnResults) {
        this.warnResults = warnResults;
    }

    public String getFontStr() {
        return fontStr;
    }

    public void setFontStr(String fontStr) {
        this.fontStr = fontStr;
    }

    public TbTjCrptWarnComm getCrptWarnComm() {
        return crptWarnComm;
    }

    public void setCrptWarnComm(TbTjCrptWarnComm crptWarnComm) {
        this.crptWarnComm = crptWarnComm;
    }

    public List<Object[]> getBhkDataList() {
        return bhkDataList;
    }

    public void setBhkDataList(List<Object[]> bhkDataList) {
        this.bhkDataList = bhkDataList;
    }

    public List<List<String>> getDiagDataList() {
        return diagDataList;
    }

    public void setDiagDataList(List<List<String>> diagDataList) {
        this.diagDataList = diagDataList;
    }

    public List<String> getDiagDataHeaderList() {
        return diagDataHeaderList;
    }

    public void setDiagDataHeaderList(List<String> diagDataHeaderList) {
        this.diagDataHeaderList = diagDataHeaderList;
    }

    public List<Object[]> getDiagYearDataList() {
        return diagYearDataList;
    }

    public void setDiagYearDataList(List<Object[]> diagYearDataList) {
        this.diagYearDataList = diagYearDataList;
    }

    public GsCrptIntegratedShowBhkDataBean getCrptIntegratedShowBhkDataBean() {
        return crptIntegratedShowBhkDataBean;
    }

    public void setCrptIntegratedShowBhkDataBean(GsCrptIntegratedShowBhkDataBean crptIntegratedShowBhkDataBean) {
        this.crptIntegratedShowBhkDataBean = crptIntegratedShowBhkDataBean;
    }
    public List<Object[]> getUnitBasicInfoList() {
        return unitBasicInfoList;
    }

    public void setUnitBasicInfoList(List<Object[]> unitBasicInfoList) {
        this.unitBasicInfoList = unitBasicInfoList;
    }

    public Integer getUnitBasicInfoRid() {
        return unitBasicInfoRid;
    }

    public void setUnitBasicInfoRid(Integer unitBasicInfoRid) {
        this.unitBasicInfoRid = unitBasicInfoRid;
    }

    public List<Object[]> getUnitBasicInfoJcList() {
        return unitBasicInfoJcList;
    }

    public void setUnitBasicInfoJcList(List<Object[]> unitBasicInfoJcList) {
        this.unitBasicInfoJcList = unitBasicInfoJcList;
    }

    public Integer getUnitBasicInfoJcRid() {
        return unitBasicInfoJcRid;
    }

    public void setUnitBasicInfoJcRid(Integer unitBasicInfoJcRid) {
        this.unitBasicInfoJcRid = unitBasicInfoJcRid;
    }
}
