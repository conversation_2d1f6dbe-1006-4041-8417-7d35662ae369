package com.chis.modules.heth.comm.rptvo;

import com.chis.modules.system.rptvo.ZwxFieldDoc;

/**
 * @ClassName OverRangeRecordVo
 * @Description: 超备案地区使用
 * <AUTHOR>
 * @Date 2020-11-20
 * @Version V1.0
 **/
public class OverRangeRecordVoForZone {
    @ZwxFieldDoc(value = "用人单位地区")
    private String zoneName;

    @ZwxFieldDoc(value = "用人单位")
    private String unitName;

    @ZwxFieldDoc(value = "社会信用代码")
    private String creitCode;

    @ZwxFieldDoc(value = "超范围服务人数")
    private String psnNum;

    @ZwxFieldDoc(value = "体检日期")
    private String bhkDate;

    @ZwxFieldDoc(value = "超服务项目")
    private String itemNames;

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getCreitCode() {
        return creitCode;
    }

    public void setCreitCode(String creitCode) {
        this.creitCode = creitCode;
    }

    public String getPsnNum() {
        return psnNum;
    }

    public void setPsnNum(String psnNum) {
        this.psnNum = psnNum;
    }

    public String getBhkDate() {
        return bhkDate;
    }

    public void setBhkDate(String bhkDate) {
        this.bhkDate = bhkDate;
    }

    public String getItemNames() {
        return itemNames;
    }

    public void setItemNames(String itemNames) {
        this.itemNames = itemNames;
    }
}
