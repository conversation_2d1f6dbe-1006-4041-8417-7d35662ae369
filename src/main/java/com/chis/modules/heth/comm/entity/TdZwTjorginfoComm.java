package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 * <p>修订内容：添加属性清空资质人员的所属机构的人员Id</p>
 * @ClassReviser qrr,2018年6月25日,TdZwDiagorginfo
 */
@Entity
@Table(name = "TD_ZW_TJORGINFO")
@SequenceGenerator(name = "TdZwTjorginfoSeq", sequenceName = "TD_ZW_TJORGINFO_SEQ", allocationSize = 1)
public class TdZwTjorginfoComm implements Serializable {

    private static final long serialVersionUID = -7313213008212321579L;

    private Integer rid;
    private TsUnit tsUnit;
    private String orgName;
    private String orgAddr;
    private String orgFz;
    private String orgFzzw;
    private String linkMan;
    private String linkMb;
    private String linkTel;
    private String fax;
    private String zipcode;
    private String email;
    private String certNo;
    private Date firstGetday;
    private Date createDate;
    private Integer createManid;
    private Short state;

    private String jcItems;

    private Integer cancelState;
    private Date cancelDate;
    private Date validDate;
    //添加属性清空资质人员的所属机构的人员Id
    private String delPsnIds;
    //社会信用代码
    private String creditCode;
    /**+外出开展职业健康检查工作能力20200221*/
    private Integer outWorkPower;
    //+最新提交日期20201107
    private Date lastSmtDate;


    public TdZwTjorginfoComm() {
    }

    public TdZwTjorginfoComm(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID", unique = true , precision = 22, scale = 0)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjorginfoSeq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "ORG_ID" )
    public TsUnit getTsUnit() {
        return this.tsUnit;
    }

    public void setTsUnit(TsUnit tsUnit) {
        this.tsUnit = tsUnit;
    }

    @Column(name = "ORG_NAME", length = 100)
    public String getOrgName() {
        return this.orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    @Column(name = "ORG_ADDR", length = 200)
    public String getOrgAddr() {
        return this.orgAddr;
    }

    public void setOrgAddr(String orgAddr) {
        this.orgAddr = orgAddr;
    }

    @Column(name = "ORG_FZ", length = 50)
    public String getOrgFz() {
        return this.orgFz;
    }

    public void setOrgFz(String orgFz) {
        this.orgFz = orgFz;
    }

    @Column(name = "ORG_FZZW", length = 50)
    public String getOrgFzzw() {
        return this.orgFzzw;
    }

    public void setOrgFzzw(String orgFzzw) {
        this.orgFzzw = orgFzzw;
    }

    @Column(name = "LINK_MAN", length = 50)
    public String getLinkMan() {
        return this.linkMan;
    }

    public void setLinkMan(String linkMan) {
        this.linkMan = linkMan;
    }

    @Column(name = "LINK_MB", length = 50)
    public String getLinkMb() {
        return this.linkMb;
    }

    public void setLinkMb(String linkMb) {
        this.linkMb = linkMb;
    }

    @Column(name = "LINK_TEL", length = 50)
    public String getLinkTel() {
        return this.linkTel;
    }

    public void setLinkTel(String linkTel) {
        this.linkTel = linkTel;
    }

    @Column(name = "FAX", length = 50)
    public String getFax() {
        return this.fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    @Column(name = "ZIPCODE", length = 10)
    public String getZipcode() {
        return this.zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    @Column(name = "EMAIL", length = 50)
    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Column(name = "CERT_NO", length = 50)
    public String getCertNo() {
        return this.certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "FIRST_GETDAY", length = 7)
    public Date getFirstGetday() {
        return this.firstGetday;
    }

    public void setFirstGetday(Date firstGetday) {
        this.firstGetday = firstGetday;
    }

    @Column(name = "CREATE_DATE" )
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Transient
    public String getJcItems() {
        return this.jcItems;
    }

    public void setJcItems(String jcItems) {
        this.jcItems = jcItems;
    }

    @Column(name = "STATE")
    public Short getState() {
        return state;
    }

    public void setState(Short state) {
        this.state = state;
    }

    @Column(name = "CANCEL_STATE")
    public Integer getCancelState() {
        return cancelState;
    }

    public void setCancelState(Integer cancelState) {
        this.cancelState = cancelState;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "CANCEL_DATE")
    public Date getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(Date cancelDate) {
        this.cancelDate = cancelDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "VALID_DATE")
    public Date getValidDate() {
        return validDate;
    }

    public void setValidDate(Date validDate) {
        this.validDate = validDate;
    }
    @Transient
    public String getDelPsnIds() {
        return delPsnIds;
    }

    public void setDelPsnIds(String delPsnIds) {
        this.delPsnIds = delPsnIds;
    }
    @Column(name = "CREDIT_CODE")
    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    @Transient
    public TsSimpleCode getFkByLevelId() {
        return null;
    }

    public void setFkByLevelId(TsSimpleCode fkByLevelId) {

    }

    @Column(name = "OUT_WORK_POWER")
    public Integer getOutWorkPower() {
        return outWorkPower;
    }

    public void setOutWorkPower(Integer outWorkPower) {
        this.outWorkPower = outWorkPower;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "LAST_SMT_DATE")
    public Date getLastSmtDate() {
        return lastSmtDate;
    }

    public void setLastSmtDate(Date lastSmtDate) {
        this.lastSmtDate = lastSmtDate;
    }

}
