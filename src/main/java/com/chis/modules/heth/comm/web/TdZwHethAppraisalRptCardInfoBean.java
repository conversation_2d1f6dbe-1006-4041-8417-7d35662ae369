package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdZwjdArchivesCard;
import com.chis.modules.heth.comm.utils.ZwArchivesCardCommUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

/**
 * <p>方法描述：职业病鉴定报告卡详情</p>
 *
 * @MethodAuthor hsj 2025-02-10 17:50
 */
@ManagedBean(name = "tdZwHethAppraisalRptCardInfoBean")
@ViewScoped
public class TdZwHethAppraisalRptCardInfoBean {
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
     * 是否生日脱敏 1 脱敏
     */
    private Integer ifBirthEncrypt;

    /**
     * 职业病鉴定
     */
    private TsSimpleCode zybjdCode;
    /**
     * 职业病结果
     */
    private TsSimpleCode zybjgCode;
    /**
     * 职业病鉴定结果是否显示 默认显示
     */
    private Boolean ifAgainRst;
    /**
     * 职业病鉴定报告卡rid
     */
    protected Integer rid;
    private TdZwjdArchivesCard archivesCard = new TdZwjdArchivesCard();

    public TdZwHethAppraisalRptCardInfoBean() {
        this.rid = Integer.parseInt(JsfUtil.getRequest().getParameter("cardRid"));
        this.ifBirthEncrypt = 1;
        this.initCardInfo();
    }

    private void initCardInfo() {
        if (ObjectUtil.isNull(this.rid)) {
            return;
        }
        /**鉴定结果默认显示*/
        this.ifAgainRst = Boolean.TRUE;
        //职业病鉴定初始化
        this.zybjdCode = new TsSimpleCode();
        this.zybjgCode = new TsSimpleCode();
        this.archivesCard = commService.find(TdZwjdArchivesCard.class, rid);
        ZwArchivesCardCommUtils.initEntity(archivesCard);
        this.zybjdCode = archivesCard.getFkByZybTypeId();
        this.zybjgCode = archivesCard.getFkByJdZybTypeId();
        if (null != this.archivesCard.getIfZyb() && 0 == this.archivesCard.getIfZyb()) {
            if (null == this.archivesCard.getFkByZybDisTypeId() || null == this.archivesCard.getFkByZybDisTypeId().getRid()) {
                this.archivesCard.setFkByZybDisTypeId(new TsSimpleCode("0", "无"));
            }
            if (null == this.archivesCard.getFkByZybTypeId() || null == this.archivesCard.getFkByZybTypeId().getRid()) {
                this.archivesCard.setFkByZybTypeId(new TsSimpleCode("0", "无"));
            }
        }
        if (null != this.archivesCard.getIfJdZyb() && 0 == this.archivesCard.getIfJdZyb()) {
            if (null == this.archivesCard.getFkByJdZybDisTypeId() || null == this.archivesCard.getFkByJdZybDisTypeId().getRid()) {
                this.archivesCard.setFkByJdZybDisTypeId(new TsSimpleCode("0", "无"));
            }
            if (null == this.archivesCard.getFkByJdZybTypeId() || null == this.archivesCard.getFkByJdZybTypeId().getRid()) {
                this.archivesCard.setFkByJdZybTypeId(new TsSimpleCode("0", "无"));
            }
        }
        if ("1".equals(this.archivesCard.getFkByJdTypeId().getExtendS1()) && "1".equals(this.archivesCard.getFkByJdRstId().getExtendS1())) {
            this.ifAgainRst = Boolean.FALSE;
        } else if ("2".equals(this.archivesCard.getFkByJdTypeId().getExtendS1()) && "1".equals(this.archivesCard.getFkByJdRstId().getExtendS1()) && this.archivesCard.getFkByJdAgainRstId() != null && "1".equals(archivesCard.getFkByJdAgainRstId().getExtendS1())) {
            this.ifAgainRst = Boolean.FALSE;
        } else {
            this.ifAgainRst = Boolean.TRUE;
        }
        if (StringUtils.isNotBlank(this.archivesCard.getIdc())) {
            this.archivesCard.setIdc(StringUtils.encryptIdc(this.archivesCard.getIdc()));
        }
        if (StringUtils.isNotBlank(this.archivesCard.getLinktel())) {
            this.archivesCard.setLinktel(StringUtils.encryptPhone(this.archivesCard.getLinktel()));
        }
        if (StringUtils.isNotBlank(this.archivesCard.getSafephone())) {
            this.archivesCard.setSafephone(StringUtils.encryptPhone(this.archivesCard.getSafephone()));
        }
        if (StringUtils.isNotBlank(this.archivesCard.getFillLink())) {
            this.archivesCard.setFillLink(StringUtils.encryptPhone(this.archivesCard.getFillLink()));
        }
        if (StringUtils.isNotBlank(this.archivesCard.getRptLink())) {
            this.archivesCard.setRptLink(StringUtils.encryptPhone(this.archivesCard.getRptLink()));
        }
    }

    public TsSimpleCode getZybjdCode() {
        return zybjdCode;
    }

    public void setZybjdCode(TsSimpleCode zybjdCode) {
        this.zybjdCode = zybjdCode;
    }

    public TsSimpleCode getZybjgCode() {
        return zybjgCode;
    }

    public void setZybjgCode(TsSimpleCode zybjgCode) {
        this.zybjgCode = zybjgCode;
    }

    public Boolean getIfAgainRst() {
        return ifAgainRst;
    }

    public void setIfAgainRst(Boolean ifAgainRst) {
        this.ifAgainRst = ifAgainRst;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwjdArchivesCard getArchivesCard() {
        return archivesCard;
    }

    public void setArchivesCard(TdZwjdArchivesCard archivesCard) {
        this.archivesCard = archivesCard;
    }

    public Integer getIfBirthEncrypt() {
        return ifBirthEncrypt;
    }

    public void setIfBirthEncrypt(Integer ifBirthEncrypt) {
        this.ifBirthEncrypt = ifBirthEncrypt;
    }
}
