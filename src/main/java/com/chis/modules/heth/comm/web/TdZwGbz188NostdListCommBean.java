package com.chis.modules.heth.comm.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.component.html.HtmlOutputText;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.javabean.TdTjBhkInfoBean;
import com.chis.modules.heth.comm.utils.CalLimitTimeCommUtils;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import org.primefaces.component.column.Column;
import org.primefaces.component.fieldset.Fieldset;
import org.primefaces.component.outputpanel.OutputPanel;
import org.primefaces.component.panelgrid.PanelGrid;
import org.primefaces.component.row.Row;
import org.primefaces.context.RequestContext;

import com.chis.modules.heth.comm.entity.TdTjBhk;
import com.chis.modules.heth.comm.entity.TdTjEmhistory;
import com.chis.modules.heth.comm.entity.TdTjExmsdata;
import com.chis.modules.heth.comm.entity.TdZwBgkLastSta;
import com.chis.modules.heth.comm.entity.TdZwGbz188NostdComm;
import com.chis.modules.heth.comm.service.HethStaQueryCommServiceImpl;
import com.chis.modules.heth.comm.service.ZwReportCardCheckCommServiceImpl;
import com.chis.modules.heth.comm.service.ZwReportCardCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;

/**
 * @Description : GBZ188不规范数据上报（COMM）
 * @ClassAuthor : anjing
 * @Date : 2020/7/21 15:06
 * 
 * 
 * <p>修订内容：该模块不再使用</p>
 * @ClassReviser qrr,2022年6月27日,TdZwGbz188NostdListCommBean
 **/
@Deprecated
@ManagedBean(name="tdZwGbz188NostdListCommBean")
@ViewScoped
public class TdZwGbz188NostdListCommBean extends AbstractReportCardCommNewBean {

    /**查询条件：用人单位地区*/
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
    /**查询条件：用人单位名称*/
    private String searchUnitName;
    /**查询条件：姓名*/
    private String searchName;
    /**查询条件：体检日期*/
    private Date searchBdate;
    private Date searchEdate;

    /**系统参数*/
    private String tzParam;
    private String tsParam;
    private String hyParam;
    /**上报/修改页面：体检主表Id*/
    private Integer bhkId;
    /**上报/修改：GBZ188不规范数据上报实体*/
    private TdZwGbz188NostdComm gbz188Nostd;
    /**上报/修改：报告卡最新状态实体*/
    private TdZwBgkLastSta newFlow;
    /**处理期限*/
    private String yszybNostdTime;
    /**GBZ188表的rid*/
    private Integer rid;
    /**是否市级直属*/
    private Integer ifCityDirect;
    /**是否能修改（用于非详情）*/
    private Boolean ifModify;

    private TdTjBhkInfoBean tjBhkInfoBean = new TdTjBhkInfoBean();
    /**体检项目的布局表格*/
    private OutputPanel archivePanel = (OutputPanel) JsfUtil.getApplication().createComponent(OutputPanel.COMPONENT_TYPE);



    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private HethStaQueryCommServiceImpl hethStaQueryServiceImpl = SpringContextHolder.getBean(HethStaQueryCommServiceImpl.class);
    private ZwReportCardCommServiceImpl cardServiceImpl = SpringContextHolder.getBean(ZwReportCardCommServiceImpl.class);
    private ZwReportCardCheckCommServiceImpl checkServiceImpl = SpringContextHolder.getBean(ZwReportCardCheckCommServiceImpl.class);
    private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
            .getBean(SystemModuleServiceImpl.class);


    public TdZwGbz188NostdListCommBean() {
        this.ifSQL = true;
        // 获取项目编码参数
        tzParam = commService.findParamValue("HETH_ARCHIVE_TZ");
        tsParam = commService.findParamValue("HETH_ARCHIVE_GNJTSJC");
        hyParam = commService.findParamValue("HETH_ARCHIVE_HYJC");
        this.yszybNostdTime = PropertyUtils.getValue("yszybNostdTime");
        this.init();
        this.searchAction();
    }

    /**
    * @Description : 参数初始化
    * @MethodAuthor: anjing
    * @Date : 2020/7/21 17:28
    **/
    private void init() {
        TsZone tsZone = sessionData.getUser().getTsUnit().getFkByManagedZoneId();
        if(null == tsZone || null == tsZone.getRid()){
            tsZone= sessionData.getUser().getTsUnit().getTsZone();
        }
        this.searchZoneCode=tsZone.getZoneGb().substring(0,2)+"00000000";
        this.searchZoneName =cardServiceImpl.findProByZoneGb(this.searchZoneCode).getZoneName();
        if(null == this.zoneList || this.zoneList.size() ==0) {
            this.zoneList = this.commService.findZoneList(false, this.searchZoneCode, null, null);

        }
        searchEdate = new Date();
    }

    @Override
    public String[] buildHqls() {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT * FROM (");
        sb.append(" SELECT T.RID, T1.PERSON_NAME,T1.IDC,NVL(T1.LNKTEL, '——') AS LinkTel, T6.CRPT_NAME,T1.BHK_CODE , T1.RPT_PRINT_DATE,T1.LACK_MSG,T.NOSTD_DESC, T.CHK_STD_FLAG");
        sb.append(" ,T8.ORG_RCV_DATE AS RCV_DATE");
        sb.append(", T8.STATE, 1 AS IF_FILL,1 AS LIMIT_DAY, 0 AS IFCHECK, '' AS LACK_MSG_STR, '' AS NOSTD_DESC_STR,T1.RID AS bhkId,nvl(T7.IF_CITY_DIRECT,0) as IF_CITY_DIRECT, ");
        sb.append(" case when T8.ORG_RCV_DATE is not null then t8.ORG_RCV_DATE else T1.RPT_PRINT_DATE end as rcvDate,T1.BHK_DATE ");
        sb.append(" FROM TD_ZW_GBZ188_NOSTD T ");
        sb.append(" LEFT JOIN TD_TJ_BHK T1 ON T.MAIN_ID = T1.RID ");
        sb.append(" LEFT JOIN TB_TJ_SRVORG T2 ON T1.BHKORG_ID = T2.RID ");
        sb.append(" LEFT JOIN TD_ZW_TJORGINFO T3 ON T2.REG_ORGID = T3.ORG_ID ");
        sb.append(" LEFT JOIN TS_UNIT T4 ON T3.ORG_ID = T4.RID ");
        sb.append(" LEFT JOIN TS_ZONE T5 ON T4.ZONE_ID = T5.RID ");
        sb.append(" LEFT JOIN TB_TJ_CRPT T6 ON T1.CRPT_ID = T6.RID ");
        sb.append(" LEFT JOIN TS_ZONE T7 ON T6.ZONE_ID = T7.RID ");
        sb.append(" LEFT JOIN TD_ZW_BGK_LAST_STA T8 ON T8.BUS_ID = T.RID AND T8.CART_TYPE = 5 ");
        sb.append(" WHERE 1=1 ");
        sb.append(" AND T2.REG_ORGID = ").append(this.sessionData.getUser().getTsUnit().getRid());
        // 用人单位地区
        if(StringUtils.isNotBlank(this.searchZoneCode)) {
            sb.append(" AND T7.ZONE_GB LIKE :crptZoneCode escape '\\\'");
            this.paramMap.put("crptZoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        // 用人单位名称
        if(StringUtils.isNotBlank(this.searchUnitName)) {
            sb.append(" AND T6.CRPT_NAME LIKE :crptName  escape '\\\'");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchUnitName.trim()) + "%");
        }
        // 姓名
        if(StringUtils.isNotBlank(this.searchName)) {
            sb.append(" AND T1.PERSON_NAME   = :personName ");
            this.paramMap.put("personName", new StringBuilder(StringUtils.convertBFH(this.searchName.trim())).toString());
        }
        // 体检日期
        if(null != this.searchBdate) {
            if(ifRptDate){
                sb.append(" AND T8.ORG_RCV_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
            }else{
                sb.append(" AND T1.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
            }
        }
        if(null != this.searchEdate) {
            if(ifRptDate){
                sb.append(" AND T8.ORG_RCV_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
            }else{
                sb.append(" AND T1.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
            }
        }


        //是否带填报
        boolean ifBeforeRpt =false;
        // 状态
        if(null != states && states.length > 0) {
            boolean contais3 =false;
            boolean contais1 =false;
            StringBuffer stateSb = new StringBuffer();
            for (String state : states) {
                if("3".equals(state)){
                    contais3 = true;
                }else if("1".equals(state)){
                    contais1 = true;
                }else{
                    stateSb.append(",").append(state);
                }
            }
            if(stateSb.toString().contains("0.5")){
                ifBeforeRpt =true;

            }else{
                ifBeforeRpt =false;
            }

            sb.append(" AND (T8.STATE IN (").append(stateSb.length()>0?stateSb.substring(1):"-1").append(")");

            if(contais1){
                sb.append(" or (nvl(T7.IF_CITY_DIRECT,0)=0 and T8.STATE=1) or (nvl(T7.IF_CITY_DIRECT,0)=1 and T8.STATE=3 )");
            }
            if(contais3){
                sb.append("or (nvl(T7.IF_CITY_DIRECT,0)=0 and T8.STATE=3 )");
            }

            sb.append(")");
        } else {
            if(null != stateList && stateList.size() > 0) {
                StringBuffer stateSb = new StringBuffer();
                for (SelectItem item : stateList) {
                    stateSb.append(",").append(item.getValue());
                }
                sb.append(" AND T8.STATE IN (").append(stateSb.substring(1)).append(")");
            }
            ifBeforeRpt =true;
        }

        //待填报查询
        if(ifBeforeRpt){
            sb.append(" union all ");
            sb.append("select null as RID,");
            sb.append("              t.person_name , T.IDC as idc, NVL(T.LNKTEL, '——') AS LinkTel, t1.crpt_name , T.BHK_CODE ,t.RPT_PRINT_DATE, t.LACK_MSG, T4.NOSTD_DESC,");
            sb.append("               T4.CHK_STD_FLAG,");
            if(ifRptDate){
                sb.append(" T.RPT_PRINT_DATE AS RCV_DATE,");
            }else{
                sb.append(" T.BHK_DATE AS RCV_DATE,");
            }
            sb.append("               0.5 AS STATE,");
            sb.append("               1 AS IF_FILL,");
            sb.append("               1 AS LIMIT_DAY,");
            sb.append("               0 AS IFCHECK,");
            sb.append("               '' AS LACK_MSG_STR,");
            sb.append("               '' AS NOSTD_DESC_STR,");
            sb.append("               T.RID AS bhkId,");
            sb.append("               nvl(T2.IF_CITY_DIRECT, 0) as IF_CITY_DIRECT,");
            sb.append("               case");
            sb.append("                 when t3.ORG_RCV_DATE is not null then");
            sb.append("                  t3.ORG_RCV_DATE");
            sb.append("                 else");
            sb.append("                  t.RPT_PRINT_DATE");
            sb.append("               end as rcvDate,T.BHK_DATE");
            sb.append(" from td_tj_bhk t ");
            sb.append(" left join tb_tj_crpt t1 on t1.rid = t.crpt_id ");
            sb.append(" left join ts_zone t2 on t2.rid = t1.zone_id ");
            sb.append(" left join TD_ZW_GBZ188_NOSTD t4 on t4.MAIN_ID = t.rid ");
            sb.append(" left join TD_ZW_BGK_LAST_STA t3 on t3.BUS_ID = t4.rid and t3.CART_TYPE = 5 ");
            sb.append(" left join TB_TJ_SRVORG t5 on t5.rid = t.BHKORG_ID ");
            sb.append(" left join TD_ZW_TJORGINFO t6 on t6.ORG_ID = t5.REG_ORGID ");
            sb.append(" where t.PROCESS_LACK = 1 and t.IF_INTEITM_LACK = 1 and t.IF_RHK = 0 ");
            sb.append(" AND T5.REG_ORGID = ").append(this.sessionData.getUser().getTsUnit().getRid());
            sb.append(" and T.BHK_TYPE in (3,4) and t1.INTER_PRC_TAG = 1 and t6.state = 1");
            if (StringUtils.isNotBlank(this.searchZoneCode)) {
                sb.append(" AND t2.ZONE_GB LIKE '");
                sb.append(ZoneUtil.zoneSelect(this.searchZoneCode.trim())).append(
                        "%'");
            }
            if (StringUtils.isNotBlank(bhkBeginDate)) {
                sb.append(" and t.RPT_PRINT_DATE >=to_date('").append(bhkBeginDate)
                        .append("','yyyy-MM-dd')");
            }
            if (StringUtils.isNotBlank(this.searchUnitName)) {
                sb.append(" AND t1.crpt_name like :searchCrptName  escape '\\\'");
                this.paramMap.put("searchCrptName",
                        "%" + StringUtils.convertBFH(this.searchUnitName.trim())
                                + "%");
            }
            if (StringUtils.isNotBlank(this.searchName)) {
                sb.append(" AND t.person_name =:searchName");
                this.paramMap.put("searchName",StringUtils.convertBFH(this.searchName.trim()));
            }

            // 体检日期
            if(null != searchBdate) {
                if(ifRptDate){
                    sb.append(" AND T.RPT_PRINT_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
                }else{
                    sb.append(" AND T.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(searchBdate, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
                }
            }
            if(null != searchEdate) {
                if(ifRptDate){
                    sb.append(" AND T.RPT_PRINT_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
                }else{
                    sb.append(" AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(searchEdate, "yyyy-MM-dd")).append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
                }
            }


            // 状态
            sb.append(" AND ( T3.STATE IS NULL)");

        }



        sb.append(")AA WHERE 1=1 ");

        String h2 = " SELECT COUNT(*) FROM (" + sb.toString() + ")";
        String h1 = sb.toString()+" ORDER BY (CASE WHEN AA.STATE = 2 OR AA.STATE = 4 OR  AA.STATE = 6 THEN -1 WHEN AA.STATE=0 OR AA.STATE=0.5 THEN AA.STATE ELSE 1 END),AA.RCVDATE,AA.PERSON_NAME ";
        return new String[] { h1, h2 };
    }



    @Override
    public void processData(List<?> list) {
        try {
            if (null != list && list.size() > 0) {
                List<Object[]> result = (List<Object[]>) list;
                for (Object[] obj : result) {
                    if (null == obj[10] || null == obj[11]) {
                        continue;
                    }
                    String state = obj[11].toString();
                    Date date = (Date) obj[10];
                    if ("1".equals(obj[18].toString())) {// 市直属
                        if ("0".equals(state) || "0.5".equals(state) || "4".equals(state) || "2".equals(state)|| "6".equals(state)) {
                            obj[12] = "1";
                            obj[13] = CalLimitTimeCommUtils.calLimitTime(date, StringUtils.isNotBlank(this.yszybNostdTime) ? Integer.valueOf(this.yszybNostdTime) : 15,true);
                        } else {
                            obj[12] = "0";
                        }
                    } else {
                        if ("0".equals(state) || "0.5".equals(state) || "2".equals(state)|| "4".equals(state)|| "6".equals(state)) {
                            obj[12] = "1";
                            obj[13] = CalLimitTimeCommUtils.calLimitTime(date, StringUtils.isNotBlank(this.yszybNostdTime) ? Integer.valueOf(this.yszybNostdTime) : 15,true);
                        } else {
                            obj[12] = "0";
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * @Description : 打开退回弹出框
     * @MethodAuthor: rcj
     * @Date : 2021/1/14 15:23
     **/
    public void initDialog() {
        if(this.readOnly) {
            initBackRsn();
        } else {
            this.backRsn = null;
        }
    }

    /**
    * @Description : 执行查询
    * @MethodAuthor: anjing
    * @Date : 2020/7/22 8:38
    **/
    @Override
    public void searchAction(){
        if(null != this.searchBdate && null != this.searchEdate) {
            if(this.searchEdate.before(this.searchBdate)) {
                if(ifRptDate){
                    JsfUtil.addErrorMessage("接收开始日期应小于等于结束日期！");
                }else{
                    JsfUtil.addErrorMessage("体检开始日期应小于等于结束日期！");
                }
                return;
            }
        }
        super.searchAction();
    }

    /**
    * @Description : 清空查询条件用人单位地区
    * @MethodAuthor: anjing
    * @Date : 2020/7/22 8:48
    **/
    public void clearZoneAction() {
        this.searchZoneCode = null;
        this.searchZoneName = null;
    }

    @Override
    public void addInit() {
        this.gbz188Nostd = new TdZwGbz188NostdComm();
        TdTjBhk tdTjBhk = new TdTjBhk();
        tdTjBhk.setRid(bhkId);
        this.gbz188Nostd.setFkByMainId(tdTjBhk);
        this.getBhkInfo();
        this.newFlow = new TdZwBgkLastSta();
        this.newFlow.setCartType(5);
        this.newFlow.setState(0);
        this.newFlow.setOrgRcvDate(tjBhkInfoBean.getTdTjBhkShow().getRptPrintDate());
    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {
        this.gbz188Nostd = cardServiceImpl.findEntityByMainId(TdZwGbz188NostdComm.class, bhkId);
        if(null == this.gbz188Nostd) {
            this.gbz188Nostd = new TdZwGbz188NostdComm();
            TdTjBhk tdTjBhk = new TdTjBhk();
            tdTjBhk.setRid(bhkId);
            this.gbz188Nostd.setFkByMainId(tdTjBhk);
        }
        this.getBhkInfo();
        // 判断是否二级审核，根据用人单位地区
        if(null != this.gbz188Nostd && null != this.gbz188Nostd.getFkByMainId().getTbTjCrpt()
                && null != this.gbz188Nostd.getFkByMainId().getTbTjCrpt().getTsZoneByZoneId()) {
            if("1".equals(this.gbz188Nostd.getFkByMainId().getTbTjCrpt().getTsZoneByZoneId().getIfCityDirect())) {
                this.ifCityDirect = 1;
            } else {
                this.ifCityDirect = 0;
            }
        }
        initBackRsn();
    }

    /**
    * @Description : 执行保存
    * @MethodAuthor: anjing
    * @Date : 2020/7/22 11:20
    **/
    @Override
    public void saveAction() {
        try {
            this.cardServiceImpl.saveOrUpdateGbz188Nostd(this.gbz188Nostd, null==this.newFlow.getRid()?this.newFlow:null, false,
                    "1".equals(tjBhkInfoBean.getTdTjBhkShow().getTbTjCrpt().getTsZoneByZoneId().getIfCityDirect()), null);
            JsfUtil.addSuccessMessage("保存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
    * @Description : 提交
    * @MethodAuthor: anjing
    * @Date : 2020/7/22 13:33
    **/
    public void submitAction() {
        if(StringUtils.isBlank(gbz188Nostd.getNostdDesc())) {
            JsfUtil.addErrorMessage("不规范原因说明不能为空！");
            return;
        }
        try {
            //初次提交清空后面流程信息
            newFlow.setCountAuditAdv(null);
            newFlow.setCountyChkPsn(null);
            newFlow.setCountySmtDate(null);
            newFlow.setCityAuditAdv(null);
            newFlow.setCityChkPsn(null);
            newFlow.setProAuditAdv(null);
            newFlow.setProChkPsn(null);
            // 是否市直属的判断也要根据用人单位地区
            if("1".equals(tjBhkInfoBean.getTdTjBhkShow().getTbTjCrpt().getTsZoneByZoneId().getIfCityDirect())) {
                newFlow.setCityRcvDate(new Date());
                newFlow.setState(3);
            } else {
                newFlow.setCountyRcvDate(new Date());
                newFlow.setState(1);
            }
            gbz188Nostd.setChkStdFlag(null);
            cardServiceImpl.saveOrUpdateGbz188Nostd(gbz188Nostd, newFlow, true,
                    "1".equals(tjBhkInfoBean.getTdTjBhkShow().getTbTjCrpt().getTsZoneByZoneId().getIfCityDirect()), null);

            JsfUtil.addSuccessMessage("提交成功！");
            this.backAction();
            this.searchAction();
            RequestContext context = RequestContext.getCurrentInstance();
            context.update("tabView");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }


    /**
     * @Description : 初始化退回原因
     * @MethodAuthor: rcj
     * @Date : 2021/1/14 14:16
     **/
    protected void initBackRsn() {
        this.newFlow = this.checkServiceImpl.searchNewFlow(rid, 5);
        if(null == this.newFlow) {
            this.newFlow = new TdZwBgkLastSta();
        }
        if(null == this.newFlow.getState()) {
            return;
        }

        //根据状态加载退回原因
        if(this.newFlow.getState() == 2){
            this.backRsn = this.newFlow.getCountyBackRsn();
        }else if(this.newFlow.getState() == 4){
            this.backRsn = this.newFlow.getCityBackRsn();
        }else if(this.newFlow.getState() == 6){
            this.backRsn = this.newFlow.getProBackRsn();
        }

    }

    /**
    * @Description : 获取体检详情信息
    * @MethodAuthor: anjing
    * @Date : 2020/7/22 10:11
    **/
    private void getBhkInfo() {
        tjBhkInfoBean.setRid(bhkId);
        tjBhkInfoBean.setArchivePanel(archivePanel);
        tjBhkInfoBean.setIfManagedOrg(false);
        tjBhkInfoBean.initBhkInfo();
    }

    /**
     * 构建outputPanel，依次页面结构层次是：outputpanel, panel, panelGrid, row, column,控件
     *
     * @param dataMap
     *            组件数据集合
     * @createDate 2014-9-20
     * <AUTHOR>
     */
    private void createOutPanel(Map<TsSimpleCode, List<Object[]>> dataMap) {
        this.archivePanel.getChildren().clear();
        if(null != dataMap && dataMap.size() > 0) {
            Set<TsSimpleCode> tsSimpleCodeSet = dataMap.keySet();
            // 遍历数据集合，当集合中出现一级分类时，就创建一个Fieldset
            for(TsSimpleCode t : tsSimpleCodeSet) {
                if(t.getCodeLevelNo().split("\\.").length == 1) {
                    createPanel(t, dataMap);
                }
            }
        }
    }

    /**
     * 创建Fieldset，并加入到archivePanel中 创建步骤： 1、创建一个新的Fieldset，设置样式和内容
     * 2、遍历数据集合，每找到一个该一级分类下的二级分类，则为该二级分类建一个新的PanelGrid
     * 3、给新创建的PanelGrid设置表格属性，表头内容，并调用相应方法创建表体 4、将新建的PanelGrid加入到Fieldset中
     * 5、继续循环，将集合中该一级分类下所有的二级分类中的数据都做成PanelGrid，添加到Fieldset
     *
     * @param ts
     *            一级分类的码表实例
     * @param dataMap
     *            数据集合
     * @createDate 2014-9-20
     * <AUTHOR>
     */
    private void createPanel(TsSimpleCode ts, Map<TsSimpleCode, List<Object[]>> dataMap) {
        // 第一步，创建Fieldset
        final Fieldset fieldset = this.createFieldset(ts.getCodeName(), "margin-top: 5px;margin-bottom: 5px;");
        // 第二步，遍历集合，创建Fieldset下的PanelGrid
        Set<TsSimpleCode> tsSimpleCodeSet = dataMap.keySet();
        for(TsSimpleCode t : tsSimpleCodeSet) {
            String[] str = t.getCodeLevelNo().split("\\.");
            // 第三步,判断此二级分类是否在该一级分类下，若是则创建PanelGrid
            if(str.length == 2 && str[0].equals(ts.getCodeLevelNo())) {
                List<Object[]> list = dataMap.get(t);
                final PanelGrid panelGrid = this.createPanelGrid("width:100%;margin-top:8px;");
                final Row headRow = this.createRow();
                final Column headColumn = this.createColumn(8, "ui-widget-header", "height:22px;");
                final HtmlOutputText headText = this.createOutputtext(t.getCodeName());
                headColumn.getChildren().add(headText);
                headRow.getChildren().add(headColumn);
                panelGrid.getChildren().add(headRow);
                // 调用相应方法创建表体，根据系统参数值，调用对应的方法创建表体
                boolean bool = true;
                if(str[0].equals(tsParam)) {
                    bool = this.createPanelGrid1(panelGrid, list);
                } else if (str[0].equals(tzParam)) {
                    bool = this.createPanelGrid2(panelGrid, list);
                } else if (str[0].equals(hyParam)) {
                    bool = this.createPanelGrid3(panelGrid, list);
                } else {
                    bool = this.createPanelGrid1(panelGrid, list);
                }
                // 第四步，如果表体内有组件，则将该表添加到Fieldse
                if(!bool) {
                    fieldset.getChildren().add(panelGrid);
                }
            }
        }
        // 第五步，将Fieldse添加到outpanel中
        if(fieldset.getChildren().size() != 0) {
            this.archivePanel.getChildren().add(fieldset);
        }
    }

    /**
     * 创建Fieldset
     *
     * @param legend
     *            标题
     * @param style
     *            样式
     * @return
     * @createDate 2014-9-20
     * <AUTHOR>
     */
    private Fieldset createFieldset(String legend, String style) {
        Fieldset fieldset = (Fieldset) JsfUtil.getApplication().createComponent(Fieldset.COMPONENT_TYPE);
        fieldset.setLegend(legend);
        fieldset.setStyle(style);
        fieldset.setToggleable(true);
        fieldset.setToggleSpeed(500);
        return fieldset;
    }

    /**
     * 创建PanelGrid
     *
     * @param style
     *            样式
     * @return
     * @createDate 2014-9-20
     * <AUTHOR>
     */
    private PanelGrid createPanelGrid(String style) {
        PanelGrid panelGrid = (PanelGrid) JsfUtil.getApplication().createComponent(PanelGrid.COMPONENT_TYPE);
        if (null != style) {
            panelGrid.setStyle(style);
        }
        return panelGrid;
    }

    /**
     * 创建Row
     *
     * @return
     * @createDate 2014-9-20
     * <AUTHOR>
     */
    private Row createRow() {
        Row row = (Row) JsfUtil.getApplication().createComponent(Row.COMPONENT_TYPE);
        return row;
    }

    /**
     * 创建列
     *
     * @param colspan
     *            合并列数
     * @param styleClass
     *            样式类
     * @param style
     *            样式
     * @return
     * @createDate 2014-9-20
     * <AUTHOR>
     */
    private Column createColumn(Integer colspan, String styleClass, String style) {
        Column column = (Column) JsfUtil.getApplication().createComponent(Column.COMPONENT_TYPE);
        if (null != colspan) {
            column.setColspan(colspan);
        }
        if (null != styleClass) {
            column.setStyleClass(styleClass);
        }
        if (null != style) {
            column.setStyle(style);
        }
        return column;
    }

    /**
     * 创建HtmlOutputText
     *
     * @param value
     *            值
     * @return
     * @createDate 2014-9-20
     * <AUTHOR>
     */
    private HtmlOutputText createOutputtext(Object value) {
        HtmlOutputText htmlOutputText = (HtmlOutputText) JsfUtil.getApplication().createComponent(
                HtmlOutputText.COMPONENT_TYPE);
        if (null != value) {
            htmlOutputText.setValue(value);
        }
        return htmlOutputText;
    }

    /**
     * 该方法用于创建功能性及特殊检查的panelgrid 1、设置表格的第一行内容 2、遍历该分类下的数据集合，将集合数据添加到表格中 3、返回创建结果
     *
     * @param panelGrid
     *            需要创建内容的panelGrid
     * @param list
     *            该分类下的数据list集合
     * @return 表格有内容返回true，否则返回false
     * @createDate 2014-9-20
     * <AUTHOR>
     */
    private boolean createPanelGrid1(PanelGrid panelGrid, List<Object[]> list) {
        boolean isBlank = true;
        // 标题行
        final Row titleRow = createRow();
        // 标题列
        final Column titleColumn1 = createColumn(null, "ui-state-default",
                "width:250px;text-align: center;height:20px;");
        final HtmlOutputText titleText1 = createOutputtext("项目");
        titleColumn1.getChildren().add(titleText1);
        final Column titleColumn2 = createColumn(null, "ui-state-default", "text-align: center;");
        final HtmlOutputText titleText2 = createOutputtext("结果");
        titleColumn2.getChildren().add(titleText2);
        // 组件关联
        titleRow.getChildren().add(titleColumn1);
        titleRow.getChildren().add(titleColumn2);
        panelGrid.getChildren().add(titleRow);
        // 遍历数据集合
        for (Object[] obj : list) {
            final Row contentRow = createRow();
            // 某条数据记录的第一列，设置内容和样式，数据为空，显示"—"
            final Column contentColumn1 = createColumn(null, null, "text-align: left;height:20px;");
            final HtmlOutputText contentText1 = createOutputtext(obj[0] == null ? "—" : obj[0]);
            contentColumn1.getChildren().add(contentText1);
            // 某条数据记录的第二列，设置内容和样式，数据为空，显示"—"
            final Column contentColumn2 = createColumn(null, null, "text-align: left;");
            final HtmlOutputText contentText2 = createOutputtext(obj[1] == null ? "—" : obj[1]);
            contentColumn2.getChildren().add(contentText2);
            // 组件关联
            contentRow.getChildren().add(contentColumn1);
            contentRow.getChildren().add(contentColumn2);
            panelGrid.getChildren().add(contentRow);
            isBlank = false;
        }
        return isBlank;
    }

    /**
     * 该方法用于创建体征panelgrid，体征需要分栏显示，因此，需要在createPanelGrid1基础增加循环
     * 1、设置表格的第一行内容，内容需要循环一次，做成分栏效果 2、遍历该分类下的数据集合，将集合数据添加到表格中，每行中添加两条记录，循环实现
     * 3、返回创建结果
     *
     * @param panelGrid
     *            需要创建内容的panelGrid
     * @param list
     *            该分类下的数据list数据集合
     * @return 表格有内容返回true，否则返回false
     * @createDate 2014-9-20
     * <AUTHOR>
     */
    private boolean createPanelGrid2(PanelGrid panelGrid, List<Object[]> list) {
        boolean isBlank = true;
        // 1.创建标题行，分栏需要循环创建两次，四列
        final Row titleRow = createRow();
        for (int i = 0; i < 2; i++) {
            final Column titleColumn1 = createColumn(null, "ui-state-default",
                    "width:150px;text-align: center;height:20px;");
            final HtmlOutputText titleText1 = createOutputtext("项目");
            titleColumn1.getChildren().add(titleText1);
            final Column titleColumn2 = createColumn(null, "ui-state-default", "width:300px;text-align: center;");
            final HtmlOutputText titleText2 = createOutputtext("结果");
            titleColumn2.getChildren().add(titleText2);
            titleRow.getChildren().add(titleColumn1);
            titleRow.getChildren().add(titleColumn2);
        }
        panelGrid.getChildren().add(titleRow);
        // 2.创建内容行
        int count = 0;
        int length = list.size();
        Row contentRow = null;
        // 3.遍历集合，添加数据，分栏添加
        for (int i = 0; i < length; i++) {
            Object[] obj = list.get(i);
            // 如果该行已有两列数据则创建一个新行
            if (count == 0) {
                contentRow = createRow();
            }
            // 给该行添加数据列
            final Column contentColumn1 = createColumn(null, null, "text-align: left;height:20px;");
            final HtmlOutputText contentText1 = createOutputtext(obj[0] == null ? "—" : obj[0]);
            contentColumn1.getChildren().add(contentText1);
            final Column contentColumn2 = createColumn(null, null, "text-align: left;");
            // 如果是最后一个且该行只有一条记录则合并列3列数据
            if (i == (length - 1) && count == 0) {
                contentColumn2.setColspan(3);
            }
            final HtmlOutputText contentText2 = createOutputtext(obj[1] == null ? "—" : obj[1]);
            // 关联组件
            contentColumn2.getChildren().add(contentText2);
            contentRow.getChildren().add(contentColumn1);
            contentRow.getChildren().add(contentColumn2);
            count++;
            // 如果该行已有两条数据记录，则将该行添加到表格中，并创建一个新行
            if (count == 2) {
                panelGrid.getChildren().add(contentRow);
                isBlank = false;
                count = 0;
            }
        }
        // 添加最后一行的数据记录
        if (count == 1) {
            panelGrid.getChildren().add(contentRow);
            isBlank = false;
        }
        return isBlank;
    }

    /**
     * 该方法用于创建化验检查检查panelgrid，化验需要添加新的数据列，因此，需要在createPanelGrid1基础增加新列
     * 1、设置表格的第一行内容 2、遍历该分类下的数据集合，将集合数据添加到表格中 3、返回创建结果
     *
     * @param panelGrid
     *            需要创建内容的panelGrid
     * @param list
     *            该分类下的数据list数据集合
     * @return 表格有内容返回true，否则返回false
     * @createDate 2014-9-20
     * <AUTHOR>
     */
    private boolean createPanelGrid3(PanelGrid panelGrid, List<Object[]> list) {
        boolean isBlank = true;
        // 第一步.创建标题行，分栏需要循环创建两次，八列
        final Row titleRow = createRow();
        for (int i = 0; i < 2; i++) {
            // 添加项目列
            final Column titleColumn1 = createColumn(null, "ui-state-default",
                    "width:120px;text-align: center;height:20px;");
            final HtmlOutputText titleText1 = createOutputtext("项目");
            titleColumn1.getChildren().add(titleText1);
            // 添加结果列
            final Column titleColumn2 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
            final HtmlOutputText titleText2 = createOutputtext("结果");
            titleColumn2.getChildren().add(titleText2);
            // 添加参考值列
            final Column titleColumn3 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
            final HtmlOutputText titleText3 = createOutputtext("参考值");
            titleColumn3.getChildren().add(titleText3);
            // 添加计量单位列
            final Column titleColumn4 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
            final HtmlOutputText titleText4 = createOutputtext("计量单位");
            titleColumn4.getChildren().add(titleText4);
            // 组件关联
            titleRow.getChildren().add(titleColumn1);
            titleRow.getChildren().add(titleColumn2);
            titleRow.getChildren().add(titleColumn3);
            titleRow.getChildren().add(titleColumn4);
        }
        panelGrid.getChildren().add(titleRow);
        // 第二步，创建内容行，遍历集合，添加数据，分栏添加
        int count = 0;
        int length = list.size();
        Row contentRow = null;
        for (int i = 0; i < length; i++) {
            Object[] obj = list.get(i);
            // 1.如果该行已有两条数据则创建一个新行
            if (count == 0) {
                contentRow = createRow();
            }
            // 2.添加项目名称内容
            final Column contentColumn1 = createColumn(null, null, "text-align: left;height:20px;");
            final HtmlOutputText contentText1 = createOutputtext(obj[0] == null ? "—" : obj[0]);
            contentColumn1.getChildren().add(contentText1);
            // 3.添加体检结果内容
            final Column contentColumn2 = createColumn(null, null, null);
            final HtmlOutputText contentText2 = createOutputtext(null);
            this.setPanelgridValue(contentColumn2, contentText2, obj);
            contentColumn2.getChildren().add(contentText2);
            // 4.添加参考值列的内容
            final Column contentColumn3 = createColumn(null, null, "text-align: left");
            final HtmlOutputText contentText3 = createOutputtext(obj[3] == null ? "—" : obj[3]);
            contentColumn3.getChildren().add(contentText3);
            // 5.添加计量单位的列的内容
            final Column contentColumn4 = createColumn(null, null, "text-align: left;");
            // 如果是最后一个且该行只有一条记录则合并列3列数据
            if (i == (length - 1) && count == 0) {
                contentColumn4.setColspan(5);
            }
            final HtmlOutputText contentText4 = createOutputtext(obj[4] == null ? "—" : obj[4]);
            // 6.组件关联
            contentColumn4.getChildren().add(contentText4);
            contentRow.getChildren().add(contentColumn1);
            contentRow.getChildren().add(contentColumn2);
            contentRow.getChildren().add(contentColumn3);
            contentRow.getChildren().add(contentColumn4);
            count++;
            // 如果该行已有两条数据记录，则将该行添加到表格中，并创建一个新行
            if (count == 2) {
                panelGrid.getChildren().add(contentRow);
                isBlank = false;
                count = 0;
            }
        }
        // 添加最后一行的数据记录
        if (count == 1) {
            panelGrid.getChildren().add(contentRow);
            isBlank = false;
        }
        return isBlank;
    }

    /**
     * 设置化验结果列的样式和值
     *
     * @param contentColumn
     * @param contentText
     * @param obj
     * @createDate 2014-9-20
     * <AUTHOR>
     */
    private void setPanelgridValue(Column contentColumn, HtmlOutputText contentText, Object[] obj) {
        // 定量且结果偏低或偏高则结果加标志，并设置颜色
        Integer result = null;
        if (null != obj[2]) {
            result = Integer.valueOf(obj[2].toString());
        }
        if (null != obj[1]) {
            if (null != result) {
                if (result == 2) {
                    // 如果偏高，则添加向上箭头，并设置颜色为红色
                    contentColumn.setStyle("text-align: left;color: red");
                    String string = obj[1].toString() + "↑";
                    contentText.setValue(string);
                } else if (result == 1) {
                    // 如果偏低，则添加向下箭头，并设置颜色为蓝色
                    contentColumn.setStyle("text-align: left;color: blue");
                    String string = obj[1].toString() + "↓";
                    contentText.setValue(string);
                } else {
                    // 否则，原样显示
                    contentColumn.setStyle("text-align: left;");
                    contentText.setValue(obj[1]);
                }
            } else {
                contentColumn.setStyle("text-align: left;");
                contentText.setValue(obj[1]);
            }
        } else {
            // 内容为空，则显示为"—"
            contentColumn.setStyle("text-align: left;");
            contentText.setValue("—");
        }
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public String getSearchName() {
        return searchName;
    }

    public void setSearchName(String searchName) {
        this.searchName = searchName;
    }

    public Date getSearchBdate() {
        return searchBdate;
    }

    public void setSearchBdate(Date searchBdate) {
        this.searchBdate = searchBdate;
    }

    public Date getSearchEdate() {
        return searchEdate;
    }

    public void setSearchEdate(Date searchEdate) {
        this.searchEdate = searchEdate;
    }


    public OutputPanel getArchivePanel() {
        return archivePanel;
    }

    public void setArchivePanel(OutputPanel archivePanel) {
        this.archivePanel = archivePanel;
    }

    public String getTzParam() {
        return tzParam;
    }

    public void setTzParam(String tzParam) {
        this.tzParam = tzParam;
    }

    public String getTsParam() {
        return tsParam;
    }

    public void setTsParam(String tsParam) {
        this.tsParam = tsParam;
    }

    public String getHyParam() {
        return hyParam;
    }

    public void setHyParam(String hyParam) {
        this.hyParam = hyParam;
    }

    public Integer getBhkId() {
        return bhkId;
    }

    public void setBhkId(Integer bhkId) {
        this.bhkId = bhkId;
    }

    public TdZwGbz188NostdComm getGbz188Nostd() {
        return gbz188Nostd;
    }

    public void setGbz188Nostd(TdZwGbz188NostdComm gbz188Nostd) {
        this.gbz188Nostd = gbz188Nostd;
    }

    public TdZwBgkLastSta getNewFlow() {
        return newFlow;
    }

    public void setNewFlow(TdZwBgkLastSta newFlow) {
        this.newFlow = newFlow;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Integer getIfCityDirect() {
        return ifCityDirect;
    }

    public void setIfCityDirect(Integer ifCityDirect) {
        this.ifCityDirect = ifCityDirect;
    }

    public Boolean getIfModify() {
        return ifModify;
    }

    public void setIfModify(Boolean ifModify) {
        this.ifModify = ifModify;
    }

    public TdTjBhkInfoBean getTjBhkInfoBean() {
        return tjBhkInfoBean;
    }

    public void setTjBhkInfoBean(TdTjBhkInfoBean tjBhkInfoBean) {
        this.tjBhkInfoBean = tjBhkInfoBean;
    }
}
