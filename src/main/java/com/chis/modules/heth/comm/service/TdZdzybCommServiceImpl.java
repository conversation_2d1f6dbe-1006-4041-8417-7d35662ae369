package com.chis.modules.heth.comm.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.modules.heth.comm.entity.TdZdzybAnalyDetailComm;
import com.chis.modules.heth.comm.entity.TdZdzybAnalyTypeComm;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;

/**
 * @Description : 重点职业病统一业务层
 * @ClassAuthor : anjing
 * @Date : 2020/1/13 14:20
 **/
@Service
@Transactional(readOnly = true)
public class TdZdzybCommServiceImpl extends AbstractTemplate {

/**mx**/
    /***
     *  <p>方法描述：获取弹出框统计项目列表</p>
     *
     * @MethodAuthor maox,2018年7月3日,getTsSimpleList
     * @return
     */
    @Transactional(readOnly = true)
    public List<TsSimpleCode> initTsSimpleList(String codeName) {
        StringBuilder sql = new StringBuilder("");
        sql.append(" select t from TsSimpleCode t where t.ifReveal =1 and t.tsCodeType.codeTypeName ='");
        sql.append(codeName).append("'");

        return em.createQuery(sql.toString()).getResultList();
    }

    /***
     *  <p>方法描述：保存重点职业病统计分类</p>
     *
     * @MethodAuthor maox,2018年7月5日,saveTdZdzybAnaly
     * @param td
     */
    @Transactional(readOnly = false)
    public void saveTdZdzybAnaly(TdZdzybAnalyTypeComm td){
        if(null == td.getRid()){
            this.save(td);
        }else{
            this.update(td);
        }
    }

    /***
     *  <p>方法描述：获取统计类别实体</p>
     *
     * @MethodAuthor maox,2018年7月5日,findTdZdzybAnalyType
     * @param rid
     * @return
     */
    @Transactional(readOnly = false)
    public TdZdzybAnalyTypeComm findTdZdzybAnalyType(Integer rid){
        TdZdzybAnalyTypeComm td = new TdZdzybAnalyTypeComm();
        td = this.find(TdZdzybAnalyTypeComm.class, rid);
        td.getTdZdzybAnalyDetailList().size();

        return td;
    }

    /***
     *  <p>方法描述：获取统计明细集合</p>
     *
     * @MethodAuthor maox,2018年7月7日,findDetailList
     * @param rid
     * @return
     */
    @Transactional(readOnly = true)
    public List<TdZdzybAnalyDetailComm> findDetailList(Integer rid){
        StringBuilder sql = new StringBuilder("");
        sql.append(" select t from TdZdzybAnalyDetailComm t where t.tdZdzybAnalyType.rid =");
        sql.append(rid).append(" order by t.xh");

        return em.createQuery(sql.toString()).getResultList();
    }

    /***
     *  <p>方法描述：删除类别明细</p>
     *
     * @MethodAuthor maox,2018年7月9日,delAnalyDetail
     * @param rid
     */
    @Transactional(readOnly = false)
    public void delAnalyDetail(Integer rid){
        StringBuilder sql = new StringBuilder("");
        sql.append("DELETE FROM TD_ZDZYB_ANALY_DETAIL WHERE MAIN_ID =").append(rid);
        em.createNativeQuery(sql.toString()).executeUpdate();
    }
    /**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2020年4月22日,findDetailListByAnalyType
     * */
    @Transactional(readOnly = true)
    public List<TdZdzybAnalyDetailComm> findDetailListByAnalyType(Integer busType,Integer analyType){
        StringBuilder sql = new StringBuilder("");
        sql.append(" select t from TdZdzybAnalyDetailComm t where t.tdZdzybAnalyType.busType =").append(busType);
        sql.append(" and t.tdZdzybAnalyType.analyType=").append(analyType).append(" order by t.xh");
        return em.createQuery(sql.toString()).getResultList();
    }

}
