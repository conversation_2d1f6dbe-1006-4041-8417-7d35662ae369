package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.entity.TbTjCrptIndepend;
import com.chis.modules.heth.comm.service.TbTjCrptIndependCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesBean;
import com.chis.modules.system.web.IndusTypeCodePanelBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.*;
import java.util.regex.Pattern;

/**
 * @Description : 用人单位选择
 * @ClassAuthor : anjing
 * @Date : 2021/4/13 14:06
 **/
@Deprecated
@ManagedBean(name="crptIndependSelectListBean")
@ViewScoped
public class CrptIndependSelectListBean extends FacesBean {

    /**填报机构Id*/
    private String createUnitId;
    /**表格数据*/
    private List<TbTjCrptIndepend> crptIndependList;
    /**查询条件：地区*/
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
    private Integer searchZoneId;
    /**查询条件：单位名称*/
    private String searchCrptName;
    /**查询条件：社会信用代码*/
    private String searchCrptCode;
    private TbTjCrptIndepend selectCrptIndepend;
    private Integer crptIndependId;
    /**修改页面：企业*/
    private TbTjCrptIndepend editCrptIndepend;
    /**修改页面：地区*/
    private List<TsZone> editZoneList;
    private Integer editZoneId;
    private String editZoneName;
    private Short editZoneType;
    /**企业规模下拉*/
    private List<TsSimpleCode> crptsizwList;
    private List<Integer> economyIds;
    private List<Integer> indusTypeIds;
    private List<TsSimpleCode> econList;
    private List<TsSimpleCode> indusList;
    /**行业类别/经济类型：所有码表数据*/
    private List<TsSimpleCode> allList;
    /**行业类别/经济类型：展示码表数据*/
    private List<TsSimpleCode> displayList;
    /**行业类别/经济类型弹出框*/
    private String selCodeName;
    private String firstCodeNo;
    private List<TsSimpleCode> firstList;
    private String searchNamOrPy;
    private TsSimpleCode selectPro;
    /**是否体检录入模块：1 是 0 否*/
    private String ifTjlrCrpt;
    /**是否从职业性有害因素监测卡填报进来 1：是；*/
    private String ifJcCrpt;
    /*是否显示 合同信息相关字段*/
    private String isShowContract;
    /** 是否显示本省外的其他省份地区 1显示 空或者其他字符不显示 */
    private String ifShowOtherZone;
    /**标题名称*/
    private String  crpyType;
    private String  crpyName;
    /**默认显示地区*/
    private String defaultZone;
    private Map<String, TsZone> zoneMap = new HashMap<String, TsZone>();
    private static final String All_ZONELIST_CACHE = "allZoneListCache";
    private static final String CUR_ZONELIST_CACHE = "currentZoneListCache";

    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private TbTjCrptIndependCommServiceImpl service = SpringContextHolder.getBean(TbTjCrptIndependCommServiceImpl.class);

    /** 行业类别弹框Bean */
    private IndusTypeCodePanelBean codePanelBean;
    public CrptIndependSelectListBean() {
        this.defaultZone = JsfUtil.getRequest().getParameter("defaultZone");
        this.createUnitId = JsfUtil.getRequest().getParameter("createUnitId");
        //从体检录入进来
        this.ifTjlrCrpt = JsfUtil.getRequest().getParameter("ifTjlrCrpt");
        this.ifJcCrpt = JsfUtil.getRequest().getParameter("ifJcCrpt");
        this.isShowContract = JsfUtil.getRequest().getParameter("isShowContract");
        this.ifShowOtherZone = JsfUtil.getRequest().getParameter("ifShowOtherZone");
        //标题名称
        this.crpyType = JsfUtil.getRequest().getParameter("crpyType");
        if("2".equals(crpyType)){
            crpyName ="用工单位";
        }else{
            crpyName ="用人单位";
        }
        this.initZone();
        this.initCrptIndepend();
        this.initSimpleCodeList();
        this.indusTypeIds = ridListByList(this.indusList);
        this.economyIds = ridListByList(this.econList);
        this.codePanelBean = new IndusTypeCodePanelBean();
        this.searchAction();
    }

    /**
     * @Description : 地区初始化
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 15:16
     **/
    private void initZone() {
        if(null == this.zoneList || this.zoneList.size() ==0) {
           //this.zoneList = this.systemModuleService.findZoneListOrderByUserZone(Global.getUser().getTsUnit().getTsZone().getZoneCode());
        }
        String jsZone = Global.getUser().getTsUnit().getTsZone().getZoneGb().substring(0, 2) + "00000000";
        if(StringUtils.isNotBlank(ifShowOtherZone) && ifShowOtherZone.trim().equals("1")){
            //获取所有地区
            this.editZoneList = (List<TsZone>)CacheUtils.get(All_ZONELIST_CACHE, jsZone);
            if(CollectionUtils.isEmpty(editZoneList)){
                //此处应在findZoneListOrderByUserZone中调整 然后调用findZoneListOrderByUserZone方法 待web-system版本明确后 调整过去
                List<Integer> tmpZoneRidList = new ArrayList<>();
                this.editZoneList = this.systemModuleService.findZoneListICanSee(false, jsZone);
                if(!CollectionUtils.isEmpty(editZoneList)){
                    for(TsZone tsZone : editZoneList){
                        if(null != tsZone.getRid()){
                            tmpZoneRidList.add(tsZone.getRid().intValue());
                        }
                    }
                }

                List<TsZone> tmpTotalZoneList = this.systemModuleService.findZoneListICanSee(true, null);
                if(!CollectionUtils.isEmpty(tmpTotalZoneList)){
                    for(TsZone tsZone : tmpTotalZoneList){
                        //加入非本省地区
                        if(null != tsZone.getRid() && !tmpZoneRidList.contains(tsZone.getRid().intValue())){
                            if(null == this.editZoneList){
                                this.editZoneList = new ArrayList<>();
                            }
                            this.editZoneList.add(tsZone);
                        }
                    }
                }
                if(!CollectionUtils.isEmpty(editZoneList)){
                    CacheUtils.put(All_ZONELIST_CACHE, jsZone, editZoneList);
                }
            }
        }else{
            this.editZoneList = (List<TsZone>)CacheUtils.get(CUR_ZONELIST_CACHE, jsZone);
            if(CollectionUtils.isEmpty(editZoneList)){
                this.editZoneList = this.systemModuleService.findZoneListICanSee(false, jsZone);
                if(!CollectionUtils.isEmpty(editZoneList)){
                    CacheUtils.put(CUR_ZONELIST_CACHE, jsZone, editZoneList);
                }
            }
        }
        zoneList= editZoneList;
        if(!CollectionUtils.isEmpty(editZoneList)){
            for(TsZone tsZone : editZoneList) {
                if((StringUtils.isBlank(ifShowOtherZone) || !ifShowOtherZone.trim().equals("1")) &&
                        null != tsZone.getZoneGb() && tsZone.getZoneGb().trim().equals(jsZone) || "1".equals(defaultZone) &&
                        null != tsZone.getZoneGb() && tsZone.getZoneGb().trim().equals(jsZone)){
                    this.searchZoneCode = jsZone;
                    this.searchZoneName = tsZone.getZoneName();
                    this.searchZoneId = tsZone.getRid();
                }
                this.zoneMap.put(tsZone.getRid().toString(), tsZone);
            }
        }
    }

    /**
     * @Description : 初始化码表数据
     * @MethodAuthor: anjing
     * @Date : 2021/4/14 10:48
     **/
    private void initSimpleCode() {
        if(null == this.editCrptIndepend.getFkByCrptId().getTsSimpleCodeByEconomyId()) {
            this.editCrptIndepend.getFkByCrptId().setTsSimpleCodeByEconomyId(new TsSimpleCode());
        }
        if(null == this.editCrptIndepend.getFkByCrptId().getTsSimpleCodeByIndusTypeId()) {
            this.editCrptIndepend.getFkByCrptId().setTsSimpleCodeByIndusTypeId(new TsSimpleCode());
        }
        if(null == this.editCrptIndepend.getFkByCrptId().getTsSimpleCodeByCrptSizeId()) {
            this.editCrptIndepend.getFkByCrptId().setTsSimpleCodeByCrptSizeId(new TsSimpleCode());
        }
    }

    /**
     * @Description : 企业信息—各单位独立信息初始化
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 16:13
     **/
    private void initCrptIndepend() {
        this.editCrptIndepend = new TbTjCrptIndepend();
        this.editCrptIndepend.setCreateDate(new Date());
        this.editCrptIndepend.setCreateManid(Global.getUser().getRid());
        if(StringUtils.isNotBlank(this.createUnitId)) {
            this.editCrptIndepend.setFkByUnitId(new TsUnit(Integer.valueOf(this.createUnitId)));
        }
        this.editCrptIndepend.setFkByCrptId(new TbTjCrpt());
        this.initSimpleCode();

        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if(null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.editZoneId = tsZone.getRid();
        this.editZoneName = tsZone.getZoneName();
        this.editZoneType = tsZone.getZoneType();
    }

    /**
     * @Description : 企业规模码表初始化
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 17:21
     **/
    private void initSimpleCodeList() {
        this.indusList = this.commService.findNumSimpleCodesByTypeId("5002");
        this.econList = this.commService.findNumSimpleCodesByTypeId("5003");
        this.crptsizwList = this.commService.findLevelSimpleCodesByTypeId("5004");
    }

    /**
    * @Description : 查找经济性质与行业类别最后一级的rid集合
    * @MethodAuthor: anjing
    * @Date : 2021/4/15 13:42
    **/
    private List<Integer> ridListByList(List<TsSimpleCode> resList) {
        List<Integer> resultList = new ArrayList<>();
        if (resList != null && resList.size() > 0) {
            Map<String, List<TsSimpleCode>> levelMap = new HashMap<>();
            for (TsSimpleCode obj : resList) {
                String codeLevelNo = obj.getCodeLevelNo();
                if (StringUtils.isBlank(codeLevelNo)) {
                    continue;
                }
                levelMap.put(codeLevelNo, new ArrayList<TsSimpleCode>());
                if (StringUtils.contains(codeLevelNo, ".")) {
                    String[] split = codeLevelNo.split("\\.");
                    StringBuffer parentCodeSb = new StringBuffer();
                    for (int i = 0; i < split.length-1; i++) {//仅找出父级
                        parentCodeSb.append(".").append(split[i]);
                        String parentCode = parentCodeSb.substring(1);
                        List<TsSimpleCode> childs = levelMap.get(parentCode);
                        if (null==childs) {
                            childs = new ArrayList<TsSimpleCode>();
                            levelMap.put(parentCode, childs);
                        }
                        childs.add(obj);
                    }
                }
            }
            for (TsSimpleCode t : resList) {
                t.setLevelIndex(StringUtils.countMatches(t.getCodeLevelNo(), ".")+ "");
                // 默认不可选择
                t.setIfSelected(false);
                List<TsSimpleCode> childs = levelMap.get(t.getCodeLevelNo());
                if (CollectionUtils.isEmpty(childs)) {
                    resultList.add(t.getRid());
                    // 最末级支持选择
                    t.setIfSelected(true);
                }
            }
        }
        return resultList;
    }

    /**
     * @Description : 执行查询
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 14:41
     **/
    public void searchAction() {
        this.crptIndependList = new ArrayList<>();
        Map<String, Object> paramMap = new HashMap<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT T.RID, T2.RID AS CRPT_RID, T3.ZONE_NAME, T3.ZONE_CODE, T3.ZONE_TYPE, T2.CRPT_NAME, ");
        sql.append(" T2.INSTITUTION_CODE, T.LINKMAN2, T.LINKPHONE2, T2.ADDRESS, T2.POSTCODE, T3.REAL_ZONE_TYPE, ");
        sql.append(" T2.ECONOMY_ID, T2.INDUS_TYPE_ID, T2.CRPT_SIZE_ID, T2.LINKMAN2 AS CRPT_LINKMAN2, T2.LINKPHONE2 AS CRPT_LINKPHONE2, ");
        sql.append(" T2.WORK_FORCE, T2.WORKMAN_NUM ,T2.OUTSOURCE_NUM, T2.ZONE_ID,T3.FULL_NAME,T3.IF_CITY_DIRECT,T3.IF_PROV_DIRECT ,T2.HOLD_CARD_MAN,T3.ZONE_GB");
        sql.append(" FROM TB_TJ_CRPT_INDEPEND T ");
        sql.append(" LEFT JOIN TS_UNIT T1 ON T.UNIT_ID = T1.RID ");
        sql.append(" LEFT JOIN TB_TJ_CRPT T2 ON T.CRPT_ID = T2.RID ");
        sql.append(" LEFT JOIN TS_ZONE T3 ON T2.ZONE_ID = T3.RID ");
        sql.append(" WHERE 1=1 ");
        // 填报机构Id
        if(StringUtils.isNotBlank(this.createUnitId)) {
            sql.append(" AND T.UNIT_ID = '").append(this.createUnitId).append("' ");
        }
        // 地区
        if(StringUtils.isNotBlank(this.searchZoneCode)){
            sql.append(" AND T3.ZONE_CODE LIKE '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%' ");
        }
        // 单位名称
        if(StringUtils.isNotBlank(this.searchCrptName)) {
            sql.append(" AND T2.CRPT_NAME LIKE :searchCrptName ");
            paramMap.put("searchCrptName", "%" + this.searchCrptName + "%");
        }
        // 社会信用代码
        if(StringUtils.isNotBlank(this.searchCrptCode)) {
            sql.append(" AND T2.INSTITUTION_CODE LIKE :searchCrptCode ");
            paramMap.put("searchCrptCode", "%" + this.searchCrptCode + "%");
        }
        sql.append(" AND ROWNUM <=50 ORDER BY T3.ZONE_CODE");
        List<Object[]> list = service.findDataBySqlNoPage(sql.toString(), paramMap);
        if(!CollectionUtils.isEmpty(list)) {
            for(Object[] obj : list) {
                TbTjCrptIndepend tbTjCrptIndepend = new TbTjCrptIndepend();
                tbTjCrptIndepend.setRid(Integer.valueOf(obj[0].toString()));
                TbTjCrpt tbTjCrpt = new TbTjCrpt();
                tbTjCrpt.setRid(Integer.valueOf(obj[1].toString()));
                tbTjCrpt.setLinkman2(StringUtils.objectToString(obj[15]));
                tbTjCrpt.setLinkphone2(StringUtils.objectToString(16));
                tbTjCrpt.setAddress(StringUtils.objectToString(obj[9]));
                tbTjCrpt.setPostCode(StringUtils.objectToString(obj[10]));
                tbTjCrpt.setWorkForce(null!=obj[17]?Integer.valueOf(obj[17].toString()):null);
                tbTjCrpt.setWorkmanNum(null!=obj[18]?Integer.valueOf(obj[18].toString()):null);
                tbTjCrpt.setHoldCardMan(null!=obj[24]?Integer.valueOf(obj[24].toString()):null);
                tbTjCrpt.setOutsourceNum(null!=obj[19]?Integer.valueOf(obj[19].toString()):null);
                TsZone tsZone = new TsZone();
                tsZone.setFullName(StringUtils.objectToString(obj[21]));
                tsZone.setRid(Integer.valueOf(obj[20].toString()));
                tsZone.setZoneName(StringUtils.objectToString(obj[2]));
                tsZone.setZoneType(null!=obj[4]?Short.valueOf(obj[4].toString()):null);
                tsZone.setZoneCode(StringUtils.objectToString(obj[3]));
                tsZone.setRealZoneType(null!=obj[11]?Short.valueOf(obj[11].toString()):null);
                tsZone.setIfCityDirect(StringUtils.objectToString(obj[22]));
                tsZone.setIfProvDirect(StringUtils.objectToString(obj[23]));
                tsZone.setZoneGb(StringUtils.objectToString(obj[25]));
                tbTjCrpt.setTsZoneByZoneId(tsZone);
                tbTjCrpt.setCrptName(StringUtils.objectToString(obj[5]));
                tbTjCrpt.setInstitutionCode(StringUtils.objectToString(obj[6]));
                if(obj[12] != null) {
                    TsSimpleCode a = this.commService.findTsSimpleCodeByRid(Integer.parseInt(obj[12].toString()));
                    tbTjCrpt.setTsSimpleCodeByEconomyId(a);
                }
                if(obj[13] != null) {
                    TsSimpleCode a = this.commService.findTsSimpleCodeByRid(Integer.parseInt(obj[13].toString()));
                    tbTjCrpt.setTsSimpleCodeByIndusTypeId(a);
                }
                if(obj[14] != null){
                    TsSimpleCode a = this.commService.findTsSimpleCodeByRid(Integer.parseInt(obj[14].toString()));
                    tbTjCrpt.setTsSimpleCodeByCrptSizeId(a);
                }
                tbTjCrptIndepend.setFkByCrptId(tbTjCrpt);
                if(StringUtils.isNotBlank(this.createUnitId)) {
                    tbTjCrptIndepend.setFkByUnitId(new TsUnit(Integer.valueOf(this.createUnitId)));
                }
                tbTjCrptIndepend.setLinkman2(StringUtils.objectToString(obj[7]));
                tbTjCrptIndepend.setLinkphone2(StringUtils.objectToString(obj[8]));
                this.crptIndependList.add(tbTjCrptIndepend);
            }
        }
    }

    /**
     * @Description : 添加企业信息—各单位独立信息
     * @MethodAuthor: anjing
     * @Date : 2021/4/14 10:34
     **/
    public void addCrptIndependAction() {
        initCrptIndepend();
        RequestContext.getCurrentInstance().execute("PF('addCrptIndepend').show();");
    }

    /**
     * @Description : 关闭弹出框
     * @MethodAuthor: anjing
     * @Date : 2021/4/14 10:35
     **/
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

    /**
     * @Description : 企业检索
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 15:29
     **/
    public void searchCrptByInstitutionCodeAction() {
        if(StringUtils.isBlank(this.editCrptIndepend.getFkByCrptId().getInstitutionCode())) {
            JsfUtil.addErrorMessage("社会信用代码不能为空！");
            return;
        }
        if(!StringUtils.isCreditCode(this.editCrptIndepend.getFkByCrptId().getInstitutionCode())) {
            JsfUtil.addErrorMessage("社会信用代码不符合规范！");
            return;
        }
        TbTjCrpt tbTjCrpt = this.service.findTbTjCrptByInstitutionCode(this.editCrptIndepend.getFkByCrptId().getInstitutionCode());
        if(null == tbTjCrpt) {
            JsfUtil.addErrorMessage("未检索到该企业！");
            return;
        }
        TbTjCrptIndepend tbTjCrptIndepend = this.service.findTbTjCrptIndependByInstitutionCodeAndUnitId(this.editCrptIndepend.getFkByCrptId().getInstitutionCode(), this.createUnitId, this.editCrptIndepend.getRid());
        if(null != tbTjCrptIndepend) {
            JsfUtil.addErrorMessage("该企业已添加，请不要重复检索添加！");
            return;
        }
        this.editCrptIndepend.setFkByCrptId(tbTjCrpt);
        this.editCrptIndepend.setLinkman2(null);
        this.editCrptIndepend.setLinkphone2(null);
        this.editZoneId = tbTjCrpt.getTsZoneByZoneId().getRid();
        this.editZoneName = tbTjCrpt.getTsZoneByZoneId().getZoneName();
        this.editZoneType = tbTjCrpt.getTsZoneByZoneId().getZoneType();
        this.initSimpleCode();
        JsfUtil.addSuccessMessage("检索成功！");
    }

    /**
     * @Description : 选择经济类型/行业类别
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 16:54
     **/
    public void selCodeTypeAction(){
        this.displayList = new ArrayList<TsSimpleCode>();
        this.allList = new ArrayList<TsSimpleCode>();
        this.firstList = new ArrayList<TsSimpleCode>();
        this.searchNamOrPy = null;
        this.firstCodeNo = "";
        if("经济类型".equals(this.selCodeName)) {
            this.allList.addAll(this.econList);
        } else if("行业类别".equals(this.selCodeName)) {
            this.allList.addAll(this.indusList);
        }
        dealCodelevel(this.allList);
        if(null != this.allList && this.allList.size() > 0) {
            this.displayList.addAll(this.allList);
        }
        if("行业类别".equals(this.selCodeName)) {
            this.codePanelBean.partInit();
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("selectForm:selectedIndusCodeTable");
            dataTable.setFirst(0);
            dataTable.setRows(10);
        }else{
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("selectForm:selectedIndusTable");
            dataTable.setFirst(0);
        }
    }

    /**
     * @Description : 处理码表层级关系
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 17:03
     **/
    private void dealCodelevel(List<TsSimpleCode> list) {
        if(list != null && list.size() > 0) {
            for(TsSimpleCode code : list) {
                code.setLevelIndex(StringUtils.countMatches(
                        code.getCodeLevelNo(), ".")
                        + "");
                if(StringUtils.containsNone(code.getCodeLevelNo(), ".")) {
                    this.firstList.add(code);
                }
            }
        }
    }

    /**
     * @Description : 行业类别/经济类型查询
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 17:08
     **/
    public void searchCodeAction() {
        //初始化展示页面的疫苗数据集
        this.displayList = new ArrayList<TsSimpleCode>();
        List<TsSimpleCode> list = new ArrayList<TsSimpleCode>();
        if(null != this.allList && this.allList.size() > 0 ) {
            if(StringUtils.isNotBlank(this.firstCodeNo)) {
                for(TsSimpleCode t : this.allList) {
                    if (t.getCodeLevelNo().startsWith(this.firstCodeNo)) {
                        list.add(t);
                    }
                }
                if(StringUtils.isNotBlank(this.searchNamOrPy)){
                    for(TsSimpleCode t :list) {
                        //疫苗名称
                        String codeName = t.getCodeName()==null?"":t.getCodeName().toString();
                        //疫苗拼音码
                        String codePym = t.getSplsht()==null?"":t.getSplsht().toString();
                        //如果模糊匹配上，则增加
                        if (codeName.indexOf(this.searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(this.searchNamOrPy.toUpperCase()) != -1) {
                            this.displayList.add(t);
                        }
                    }
                } else {
                    this.displayList.addAll(list);
                }
            } else if(StringUtils.isNotBlank(this.searchNamOrPy)) {
                for(TsSimpleCode t : this.allList) {
                    //疫苗名称
                    String codeName = t.getCodeName()==null?"":t.getCodeName().toString();
                    //疫苗拼音码
                    String codePym = t.getSplsht()==null?"":t.getSplsht().toString();
                    //如果模糊匹配上，则增加
                    if (codeName.indexOf(this.searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(this.searchNamOrPy.toUpperCase()) != -1) {
                        this.displayList.add(t);
                    }
                }
            } else {
                this.displayList.addAll(this.allList);
            }
        }
    }

    /**
     * @Description : 选择经济类型/行业类别
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 17:10
     **/
    public void selectAction(){
        if ("经济类型".equals(this.selCodeName)) {
            this.editCrptIndepend.getFkByCrptId().setTsSimpleCodeByEconomyId(this.selectPro);
            RequestContext.getCurrentInstance().update("selectForm:economyName");
        } else if ("行业类别".equals(this.selCodeName)) {
            this.editCrptIndepend.getFkByCrptId().setTsSimpleCodeByIndusTypeId(this.selectPro);
            RequestContext.getCurrentInstance().update("selectForm:indusTypeName");
        }
        RequestContext.getCurrentInstance().execute("PF('selDialog').hide()");
    }

    /**
     * @Description: 行业类别选择
     *
     * @MethodAuthor pw,2022年02月7日
     */
    public void selectIndusTypeAction(){
        this.editCrptIndepend.getFkByCrptId().setTsSimpleCodeByIndusTypeId(this.selectPro);
        RequestContext.getCurrentInstance().update("selectForm:indusTypeName");
        RequestContext.getCurrentInstance().execute("PF('selIndusTypeDialog').hide()");
    }

    /**
     * @Description : 清空行业类别/经济类型
     * @MethodAuthor: anjing
     * @Date : 2021/4/15 11:19
     **/
    public void delCodeName(){
        if ("经济类型".equals(this.selCodeName)) {
            this.editCrptIndepend.getFkByCrptId().setTsSimpleCodeByEconomyId(new TsSimpleCode());
            RequestContext.getCurrentInstance().update("selectForm:economyName");
        }else if ("行业类别".equals(this.selCodeName)) {
            this.editCrptIndepend.getFkByCrptId().setTsSimpleCodeByIndusTypeId(new TsSimpleCode());
            RequestContext.getCurrentInstance().update("selectForm:indusTypeName");
        }
    }

    private boolean veryMobile(String mobile) {
        boolean flag = true;
        if (StringUtils.isNotBlank(mobile)
                && !Pattern.matches(Constants.PHONE, mobile)&&!Pattern.matches(Constants.MOBILE_REGEX, mobile)) {
            flag = false;
        }
        return flag;
    }

    /**
     * @Description : 保存企业信息—各单位独立信息前校验
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 17:31
     **/
    private boolean veryTbTjCrptIndepend() {
        boolean flag = true;
        // 社会信用代码
        if(StringUtils.isBlank(this.editCrptIndepend.getFkByCrptId().getInstitutionCode())) {
            JsfUtil.addErrorMessage("社会信用代码不能为空！");
            flag = false;
        }
        if(StringUtils.isNotBlank(this.editCrptIndepend.getFkByCrptId().getInstitutionCode())) {
            // 社会信用证代码合法与重复性
            if (!StringUtils.isCreditCode(this.editCrptIndepend.getFkByCrptId().getInstitutionCode())) {
                JsfUtil.addErrorMessage("社会信用代码不符合规范！");
                flag = false;
            }

        }
        // 所属地区
        if(null == this.editCrptIndepend.getFkByCrptId().getTsZoneByZoneId()
                || null == this.editCrptIndepend.getFkByCrptId().getTsZoneByZoneId().getRid()) {
            JsfUtil.addErrorMessage("请选择所属地区！");
            flag = false;
        } else {
            if(null == this.zoneMap.get(this.editCrptIndepend.getFkByCrptId().getTsZoneByZoneId().getRid().toString())) {
                JsfUtil.addErrorMessage("所属地区已停用，请重新选择！");
                flag = false;
            }
            Short realZoneType = (null == this.editCrptIndepend.getFkByCrptId().getTsZoneByZoneId() || null == this.editCrptIndepend.getFkByCrptId().getTsZoneByZoneId().getRid()|| null == this.zoneMap.get(this.editCrptIndepend.getFkByCrptId().getTsZoneByZoneId().getRid().toString())) ? null
                    : this.zoneMap.get(this.editCrptIndepend.getFkByCrptId().getTsZoneByZoneId().getRid().toString()).getRealZoneType();
            if(null == realZoneType || (null != realZoneType && realZoneType.intValue() < 5)) {
                JsfUtil.addErrorMessage("所属地区必须选择至街道！");
                flag = false;
            }
        }
        // 企业名称
        if(StringUtils.isBlank(this.editCrptIndepend.getFkByCrptId().getCrptName())) {
            JsfUtil.addErrorMessage("企业名称不能为空！");
            flag = false;
        }
        // 单位地址
        if(StringUtils.isBlank(this.editCrptIndepend.getFkByCrptId().getAddress())) {
            JsfUtil.addErrorMessage("单位地址不能为空！");
            flag = false;
        }
        // 行业类别
        if(this.editCrptIndepend.getFkByCrptId().getTsSimpleCodeByIndusTypeId() == null
                || (this.editCrptIndepend.getFkByCrptId().getTsSimpleCodeByIndusTypeId() !=null && this.editCrptIndepend.getFkByCrptId().getTsSimpleCodeByIndusTypeId().getRid() ==null)) {
            JsfUtil.addErrorMessage("请选择行业类别！");
            flag = false;
        } else if(null == this.indusTypeIds || this.indusTypeIds.size() == 0
                || !this.indusTypeIds.contains(this.editCrptIndepend.getFkByCrptId().getTsSimpleCodeByIndusTypeId().getRid())){
            JsfUtil.addErrorMessage("行业类别必须选择至最末级！");
            flag = false;
        }
        // 经济类型
        if(this.editCrptIndepend.getFkByCrptId().getTsSimpleCodeByEconomyId() ==null
                || (this.editCrptIndepend.getFkByCrptId().getTsSimpleCodeByEconomyId() !=null && this.editCrptIndepend.getFkByCrptId().getTsSimpleCodeByEconomyId().getRid() ==null)) {
            JsfUtil.addErrorMessage("请选择经济类型！");
            flag = false;
        } else if(null == this.economyIds || this.economyIds.size() == 0 ||
                !this.economyIds.contains(this.editCrptIndepend.getFkByCrptId().getTsSimpleCodeByEconomyId().getRid())) {
            JsfUtil.addErrorMessage("经济类型必须选择至最末级！");
            flag = false;
        }
        // 企业规模
        if(this.editCrptIndepend.getFkByCrptId().getTsSimpleCodeByCrptSizeId() == null
                || (this.editCrptIndepend.getFkByCrptId().getTsSimpleCodeByCrptSizeId() != null && this.editCrptIndepend.getFkByCrptId().getTsSimpleCodeByCrptSizeId().getRid() ==null)) {
            JsfUtil.addErrorMessage("请选择企业规模！");
            flag = false;
        }
        // 联系人
        if (StringUtils.isBlank(this.editCrptIndepend.getLinkman2())) {
            JsfUtil.addErrorMessage("联系人不能为空！");
            flag = false;
        }
        // 联系电话
        if (StringUtils.isBlank(this.editCrptIndepend.getLinkphone2())) {
            JsfUtil.addErrorMessage("联系电话不能为空！");
            flag = false;
        } else {
            if (!veryMobile(this.editCrptIndepend.getLinkphone2())) {
                JsfUtil.addErrorMessage("联系电话格式不正确！");
                flag = false;
            }
        }
        // 电话
        if (StringUtils.isNotBlank(this.editCrptIndepend.getFkByCrptId().getPhone())
                && !veryMobile(this.editCrptIndepend.getFkByCrptId().getPhone())) {
            JsfUtil.addErrorMessage("法人联系电话格式不正确！");
            flag = false;
        }
        // 邮政编码
        if(StringUtils.isNotBlank(this.editCrptIndepend.getFkByCrptId().getPostCode())) {
            if(!StringUtils.vertyPost(this.editCrptIndepend.getFkByCrptId().getPostCode())) {
                JsfUtil.addErrorMessage("邮政编码格式不正确！");
                flag = false;
            }
        }
        if(null != ifTjlrCrpt && ifTjlrCrpt.equals("1")) {
            if(null == this.editCrptIndepend.getFkByCrptId().getWorkForce()) {
                JsfUtil.addErrorMessage("职工人数不允许为空！");
                flag = false;
            }
            if(null == this.editCrptIndepend.getFkByCrptId().getHoldCardMan()) {
                JsfUtil.addErrorMessage("接触职业病危害因素人数不允许为空！");
                flag = false;
            }
            if(null != this.editCrptIndepend.getFkByCrptId().getWorkForce() && null != this.editCrptIndepend.getFkByCrptId().getHoldCardMan()
                    && this.editCrptIndepend.getFkByCrptId().getWorkForce().compareTo(this.editCrptIndepend.getFkByCrptId().getHoldCardMan()) < 0) {
                JsfUtil.addErrorMessage("接触职业病危害因素人数必须小于等于职工人数！");
                flag = false;
            }
        }
       if(null != ifJcCrpt && "1".equals(ifJcCrpt) ){
           if(null == this.editCrptIndepend.getFkByCrptId().getWorkForce()) {
               JsfUtil.addErrorMessage("职工人数不允许为空！");
               flag = false;
           }
           //职业性有害因素监测卡填报
           if(null == this.editCrptIndepend.getFkByCrptId().getWorkmanNum()) {
               JsfUtil.addErrorMessage("生产工人数不允许为空！");
               flag = false;
           }
           if(null != this.editCrptIndepend.getFkByCrptId().getWorkForce() && null != this.editCrptIndepend.getFkByCrptId().getWorkmanNum()
                   && this.editCrptIndepend.getFkByCrptId().getWorkForce().compareTo(this.editCrptIndepend.getFkByCrptId().getWorkmanNum()) < 0) {
               JsfUtil.addErrorMessage("生产工人数必须小于等于职工人数！");
               flag = false;
           }
       }
        if(StringUtils.isNotBlank(this.createUnitId) && StringUtils.isNotBlank(this.editCrptIndepend.getFkByCrptId().getInstitutionCode())) {
            List<TbTjCrptIndepend> list = this.service.findTbTjCrptIndependListByInstitutionCodeAndUnitId(this.editCrptIndepend.getFkByCrptId().getInstitutionCode(), this.createUnitId, this.editCrptIndepend.getRid());
            if(null != list && list.size() >0) {
                JsfUtil.addErrorMessage("该企业已添加，请不要重复添加！");
                flag = false;
            }
        }
        return flag;
    }

    /**
     * @Description : 选择企业信息—各单位独立信息
     * @MethodAuthor: anjing
     * @Date : 2021/4/14 11:04
     **/
    public void selectCrptIndependAction() {
        if(null != this.selectCrptIndepend) {
            if(StringUtils.isBlank(this.selectCrptIndepend.getLinkman2())
                    || StringUtils.isBlank(this.selectCrptIndepend.getLinkphone2())
                    ||null== this.selectCrptIndepend.getFkByCrptId().getTsZoneByZoneId().getRealZoneType()
                    ||(null!= this.selectCrptIndepend.getFkByCrptId().getTsZoneByZoneId().getRealZoneType() && this.selectCrptIndepend.getFkByCrptId().getTsZoneByZoneId().getRealZoneType().intValue()<5)
                    || StringUtils.isBlank(this.selectCrptIndepend.getFkByCrptId().getCrptName())
                    || StringUtils.isBlank(this.selectCrptIndepend.getFkByCrptId().getInstitutionCode())
                    || StringUtils.isBlank(this.selectCrptIndepend.getFkByCrptId().getAddress())
                    ||null == this.selectCrptIndepend.getFkByCrptId().getTsSimpleCodeByEconomyId()
                    ||null == this.selectCrptIndepend.getFkByCrptId().getTsSimpleCodeByIndusTypeId()
                    ||null == this.selectCrptIndepend.getFkByCrptId().getTsSimpleCodeByCrptSizeId()
                    || null == this.economyIds
                    || null == this.indusTypeIds
                    || !this.economyIds.contains(this.selectCrptIndepend.getFkByCrptId().getTsSimpleCodeByEconomyId().getRid())
                    || !this.indusTypeIds.contains(this.selectCrptIndepend.getFkByCrptId().getTsSimpleCodeByIndusTypeId().getRid())
                    || (null!= this.selectCrptIndepend.getFkByCrptId().getTsZoneByZoneId() && null!= this.selectCrptIndepend.getFkByCrptId().getTsZoneByZoneId().getRid() && null == this.zoneMap.get(this.selectCrptIndepend.getFkByCrptId().getTsZoneByZoneId().getRid().toString()))) {
                JsfUtil.addErrorMessage("请先完善用人单位信息！");
                return;
            }
            // 从体检录入过来的 需要判断职工人数和接触危害因素人数是否为空
            if(null != this.ifTjlrCrpt && ifTjlrCrpt.equals("1") &&
                    (null == this.selectCrptIndepend.getFkByCrptId().getWorkForce() || null == this.selectCrptIndepend.getFkByCrptId().getHoldCardMan())) {
                JsfUtil.addErrorMessage("请先完善所选单位信息！");
                return;
            }
            // 从职业性有害因素监测卡填报过来的 需要判断职工人数和生产工人数是否为空
            if(null != this.ifJcCrpt && ifJcCrpt.equals("1") &&
                    (null == this.selectCrptIndepend.getFkByCrptId().getWorkForce() || null == this.selectCrptIndepend.getFkByCrptId().getWorkmanNum())) {
                JsfUtil.addErrorMessage("请先完善所选单位信息！");
                return;
            }
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("selectCrpt", this.selectCrptIndepend);
            RequestContext.getCurrentInstance().closeDialog(map);
        }
    }

    /**
     * @Description : 修改企业信息—各单位独立信息
     * @MethodAuthor: anjing
     * @Date : 2021/4/14 11:05
     **/
    public void modCrptIndependAction() {
        if(null == this.crptIndependId) {
            return;
        }
        String hql = "from TbTjCrptIndepend t where 1=1 and t.rid =" + crptIndependId;
        this.editCrptIndepend = this.service.findOneByHql(hql, TbTjCrptIndepend.class);
        if(null == this.editCrptIndepend) {
            this.editCrptIndepend = new TbTjCrptIndepend();
            if(StringUtils.isNotBlank(this.createUnitId)) {
                this.editCrptIndepend.setFkByUnitId(new TsUnit(Integer.valueOf(this.createUnitId)));
            }
        } else {
            //地区赋值
            TsZone zone = this.editCrptIndepend.getFkByCrptId().getTsZoneByZoneId();
            if(null != zone) {
                this.editZoneId = zone.getRid();
                this.editZoneName = zone.getZoneName();
                this.editZoneType = zone.getRealZoneType();
            }
        }
        initSimpleCode();
    }

    /**
     * @Description : 保存企业信息—各单位独立信息
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 16:24
     **/
    public void saveCrptIndependAction() {
        try {
            if(null == this.editCrptIndepend) {
                return;
            }
            if("1".equals(this.isShowContract)){
                if(null!=this.editCrptIndepend.getFkByCrptId().getWorkForce()&&null!=this.editCrptIndepend.getFkByCrptId().getOutsourceNum()){
                    if(this.editCrptIndepend.getFkByCrptId().getWorkForce()<this.editCrptIndepend.getFkByCrptId().getOutsourceNum()){
                        JsfUtil.addErrorMessage("在册职工总数应大于等于外委人员数！");
                        return;
                    }
                }
            }
            TsZone tsZone = new TsZone();
            tsZone.setRid(this.editZoneId);
            tsZone.setZoneName(this.editZoneName);
            tsZone.setZoneType(this.editZoneType);
            this.editCrptIndepend.getFkByCrptId().setTsZoneByZoneId(tsZone);
            this.editCrptIndepend.getFkByCrptId().setInterPrcTag((short) 1);

            if(veryTbTjCrptIndepend()) {
                this.service.saveTbTjCrptIndepend(this.editCrptIndepend);
                RequestContext.getCurrentInstance().update("selectForm:crptIndependTable");
                RequestContext.getCurrentInstance().execute("PF('addCrptIndepend').hide();");
                initSimpleCode();
                JsfUtil.addSuccessMessage("保存成功！");
                this.searchAction();
            }
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    public void clearCodeName(){
        this.searchZoneCode = null;
        this.searchZoneName = null;
        this.searchZoneId = null;
        searchAction();

    }
    public List<TbTjCrptIndepend> getCrptIndependList() {
        return crptIndependList;
    }

    public void setCrptIndependList(List<TbTjCrptIndepend> crptIndependList) {
        this.crptIndependList = crptIndependList;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchCrptCode() {
        return searchCrptCode;
    }

    public void setSearchCrptCode(String searchCrptCode) {
        this.searchCrptCode = searchCrptCode;
    }

    public TbTjCrptIndepend getSelectCrptIndepend() {
        return selectCrptIndepend;
    }

    public void setSelectCrptIndepend(TbTjCrptIndepend selectCrptIndepend) {
        this.selectCrptIndepend = selectCrptIndepend;
    }

    public TbTjCrptIndepend getEditCrptIndepend() {
        return editCrptIndepend;
    }

    public void setEditCrptIndepend(TbTjCrptIndepend editCrptIndepend) {
        this.editCrptIndepend = editCrptIndepend;
    }

    public List<TsZone> getEditZoneList() {
        return editZoneList;
    }

    public void setEditZoneList(List<TsZone> editZoneList) {
        this.editZoneList = editZoneList;
    }

    public Integer getEditZoneId() {
        return editZoneId;
    }

    public void setEditZoneId(Integer editZoneId) {
        this.editZoneId = editZoneId;
    }

    public String getEditZoneName() {
        return editZoneName;
    }

    public void setEditZoneName(String editZoneName) {
        this.editZoneName = editZoneName;
    }

    public Short getEditZoneType() {
        return editZoneType;
    }

    public void setEditZoneType(Short editZoneType) {
        this.editZoneType = editZoneType;
    }

    public List<TsSimpleCode> getAllList() {
        return allList;
    }

    public void setAllList(List<TsSimpleCode> allList) {
        this.allList = allList;
    }

    public List<TsSimpleCode> getDisplayList() {
        return displayList;
    }

    public void setDisplayList(List<TsSimpleCode> displayList) {
        this.displayList = displayList;
    }

    public List<TsSimpleCode> getFirstList() {
        return firstList;
    }

    public void setFirstList(List<TsSimpleCode> firstList) {
        this.firstList = firstList;
    }

    public String getSearchNamOrPy() {
        return searchNamOrPy;
    }

    public void setSearchNamOrPy(String searchNamOrPy) {
        this.searchNamOrPy = searchNamOrPy;
    }

    public String getSelCodeName() {
        return selCodeName;
    }

    public void setSelCodeName(String selCodeName) {
        this.selCodeName = selCodeName;
    }

    public String getFirstCodeNo() {
        return firstCodeNo;
    }

    public void setFirstCodeNo(String firstCodeNo) {
        this.firstCodeNo = firstCodeNo;
    }

    public TsSimpleCode getSelectPro() {
        return selectPro;
    }

    public void setSelectPro(TsSimpleCode selectPro) {
        this.selectPro = selectPro;
    }

    public List<TsSimpleCode> getCrptsizwList() {
        return crptsizwList;
    }

    public void setCrptsizwList(List<TsSimpleCode> crptsizwList) {
        this.crptsizwList = crptsizwList;
    }

    public String getIfTjlrCrpt() {
        return ifTjlrCrpt;
    }

    public void setIfTjlrCrpt(String ifTjlrCrpt) {
        this.ifTjlrCrpt = ifTjlrCrpt;
    }

    public Integer getCrptIndependId() {
        return crptIndependId;
    }

    public void setCrptIndependId(Integer crptIndependId) {
        this.crptIndependId = crptIndependId;
    }

    public List<Integer> getEconomyIds() {
        return economyIds;
    }

    public void setEconomyIds(List<Integer> economyIds) {
        this.economyIds = economyIds;
    }

    public List<Integer> getIndusTypeIds() {
        return indusTypeIds;
    }

    public void setIndusTypeIds(List<Integer> indusTypeIds) {
        this.indusTypeIds = indusTypeIds;
    }

    public String getIsShowContract() {
        return isShowContract;
    }

    public void setIsShowContract(String isShowContract) {
        this.isShowContract = isShowContract;
    }

    public String getIfShowOtherZone() {
        return ifShowOtherZone;
    }

    public void setIfShowOtherZone(String ifShowOtherZone) {
        this.ifShowOtherZone = ifShowOtherZone;
    }

    public Integer getSearchZoneId() {
        return searchZoneId;
    }

    public void setSearchZoneId(Integer searchZoneId) {
        this.searchZoneId = searchZoneId;
    }

    public String getCrpyName() {
        return crpyName;
    }

    public void setCrpyName(String crpyName) {
        this.crpyName = crpyName;
    }

    public String getDefaultZone() {
        return defaultZone;
    }

    public void setDefaultZone(String defaultZone) {
        this.defaultZone = defaultZone;
    }

    public String getIfJcCrpt() {
        return ifJcCrpt;
    }

    public void setIfJcCrpt(String ifJcCrpt) {
        this.ifJcCrpt = ifJcCrpt;
    }

    public IndusTypeCodePanelBean getCodePanelBean() {
        return codePanelBean;
    }

    public void setCodePanelBean(IndusTypeCodePanelBean codePanelBean) {
        this.codePanelBean = codePanelBean;
    }
}
