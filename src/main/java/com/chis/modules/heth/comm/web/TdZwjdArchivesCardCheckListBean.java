package com.chis.modules.heth.comm.web;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.heth.comm.entity.TdZwBgkFlow;
import com.chis.modules.heth.comm.entity.TdZwBgkLastSta;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.utils.HolidayUtil;
import org.springframework.util.CollectionUtils;

/**
 * <p>类描述：职业病鉴定报告卡审核</p>
 * @ClassAuthor qrr,2021年11月8日,TdZwjdArchivesCardCheckListBean
 * */
@ManagedBean(name = "tdZwjdArchivesCardCheckListBean")
@ViewScoped
public class TdZwjdArchivesCardCheckListBean extends AbstractArchivesCardCheckListBean {
	private static final long serialVersionUID = 1L;
    /**查询条件：鉴定机构地区集合*/
    private List<TsZone> zoneList;
    /**查询条件：鉴定机构地区名称*/
    private String searchZoneName;
    /**查询条件：鉴定机构地区编码*/
    private String searchZoneCode;
    /**查询条件：用人单位名称*/
    private String searchUnitName;
    /**查询条件：用工单位名称*/
    private String searchCrptName;
    /**查询条件：姓名*/
    private String searchPsnName;
    /**查询条件：姓名*/
    private String searchIdc;
    /**查询条件：鉴定类型*/
    private String[] searchJdTypes;
    private List<TsSimpleCode> jdTypeList;
    /**查询条件：首次鉴定结论*/
    private List<TsSimpleCode> firstJdResultList;
	private String selectFirstJdResultNames;
	private String selectFirstJdResultIds;
	/**查询条件：再次鉴定结论*/
    private List<TsSimpleCode> nextJdResultList;
	private String selectNextJdResulNames;
	private String selectNextJdResulIds;
    /**查询条件：鉴定日期-开始日期*/
    private Date searchJdBdate;
    /**查询条件：鉴定日期-结束日期*/
    private Date searchJdEdate;
    /**查询条件：报告日期查询-开始日期*/
    private Date searchRptBdate;
    /**查询条件：报告日期查询-结束日期*/
    private Date searchRptEdate;
    /**查询条件：接收日期查询-开始日期*/
    private Date searchRcvBdate;
    /**查询条件：接收日期查询-结束日期*/
    private Date searchRcvEdate;
    /**历次审核意见*/
    private List<TdZwBgkFlow> bgkFlows;
    /**历次审核意见*/
    private List<Object[]> historyList;
    /** 填报的托管Bean 用于详情页展示数据 */
    private TdZwHethAppraisalRptCardListBean rptCardListBean;

    public TdZwjdArchivesCardCheckListBean(){
        this.ifSQL = true;

        this.jdTypeList = commService.findLevelSimpleCodesByTypeId("5543");
        this.firstJdResultList = commService.findLevelSimpleCodesByTypeId("5544");
        this.nextJdResultList = commService.findLevelSimpleCodesByTypeId("5545");
        this.searchJdBdate = DateUtils.addYears(new Date(), -1);
        this.searchJdEdate = DateUtils.getDateOnly(new Date());
        initZone();
        this.rptCardListBean = new TdZwHethAppraisalRptCardListBean();
        this.searchAction();
    }

    @Override
    public Integer getCartType() {
        return 8;
    }

    /**
 	 * <p>方法描述：初始化地区</p>
 	 * @MethodAuthor qrr,2021年11月8日,initZone
     * */
    private void initZone() {
    	TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		if (null==tsZone) {
			tsZone = Global.getUser().getTsUnit().getTsZone();
		}
        this.searchZoneCode = tsZone.getZoneCode();
        this.searchZoneName = tsZone.getZoneName();
    	this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb(), true,null,null);
        if(!CollectionUtils.isEmpty(zoneList)){
            this.searchZoneCode = zoneList.get(0).getZoneCode();
            this.searchZoneName = zoneList.get(0).getZoneName();
        }
	}

    /**
     *  <p>方法描述：提交前验证</p>
     * @MethodAuthor hsj
     */
    public void  beforeSubmit(){
        try{
            if(veryData()){
                return;
            }
            RequestContext context = RequestContext.getCurrentInstance();
            context.execute("PF('ConfirmDialog').show()");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }


    /**
     *  <p>方法描述：提交审核结果</p>
     * @MethodAuthor hsj
     */
    @Override
    public void saveAction() {
        try {
            //更新报告卡最新状态/报告卡审批流程
            saveOrUpdateNewFlowOrBgkFlow();
            JsfUtil.addSuccessMessage("提交成功！");
            this.backAction();
            this.searchAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
     *  <p>方法描述：审核状态修改</p>
     * @MethodAuthor hsj
     */
    public void changeCheckState(){
        if(null != defaultAuditAdv && "1".equals(checkResult)){
            //通过
            if(3 == level && StringUtils.isBlank(newFlow.getProAuditAdv())){
                newFlow.setProAuditAdv(defaultAuditAdv);
            }
            if(2 == level && StringUtils.isBlank(newFlow.getCityAuditAdv())){
                newFlow.setCityAuditAdv(defaultAuditAdv);
            }
            if(1 == level && StringUtils.isBlank(newFlow.getCountAuditAdv())){
                newFlow.setCountAuditAdv(defaultAuditAdv);
            }

        }else {
            if(3 == level && defaultAuditAdv.equals(newFlow.getProAuditAdv())){
                newFlow.setProAuditAdv("");
            }
            if(2 == level && defaultAuditAdv.equals(newFlow.getCityAuditAdv())){
                newFlow.setCityAuditAdv("");
            }
            if(1 == level  && defaultAuditAdv.equals(newFlow.getCountAuditAdv())){
                newFlow.setCountAuditAdv("");
            }
        }
    }


    @Override
    public void addInit() {

    }

    @Override
    public void modInit() {
        //审核意见
        if(0 == ifIsCheckout){
            initFlow();
            this.checkResult = "";
        }
        //获取历次审核
        historyList = appraisalRptCardService.findHisotryList(rid,getCartType());
        //初始化历次审核意见
        initHistoryCheckout();
        super.viewInit();
    }

    /**
     *  <p>方法描述：初始化审核意见</p>
     * @MethodAuthor hsj
     */
    protected void initFlow() {
        this.newFlow = this.chkSmaryService.findTdZwBgkLastSta(rid, getCartType());
        if(null == this.newFlow) {
            this.newFlow = new TdZwBgkLastSta();
        }
        //审核，审核意见、审核人为空
        if (3==level) {
            //终审
            this.newFlow.setProAuditAdv(null);
            this.newFlow.setProChkPsn(Global.getUser().getUsername());
        }else if (2==level) {
            //复审
            this.newFlow.setCityAuditAdv(null);
            this.newFlow.setCityChkPsn(Global.getUser().getUsername());
        }else {
            //初审
            this.newFlow.setCountAuditAdv(null);
            this.newFlow.setCountyChkPsn(Global.getUser().getUsername());
        }

        if(null == this.newFlow.getState()) {
            return;
        }
    }

    @Override
    public void viewInit() {
        if(null == this.rid){
            return;
        }
        /**鉴定结果默认显示*/
        this.ifAgainRst = Boolean.TRUE;
        //审核意见
        if(0 == ifIsCheckout){
            initFlow();
            this.checkResult = "";
        }
        this.rptCardListBean.setRid(this.rid);
        this.rptCardListBean.viewInit();
        this.rptCardListBean.setIfBirthEncrypt(1);
        archivesCard = this.rptCardListBean.getArchivesCard();
        if(null != archivesCard){
            if(StringUtils.isNotBlank(archivesCard.getIdc())){
                archivesCard.setIdc(StringUtils.encryptIdc(archivesCard.getIdc()));
            }
            if(StringUtils.isNotBlank(archivesCard.getLinktel())){
                archivesCard.setLinktel(StringUtils.encryptPhone(archivesCard.getLinktel()));
            }
            if(StringUtils.isNotBlank(archivesCard.getSafephone())){
                archivesCard.setSafephone(StringUtils.encryptPhone(archivesCard.getSafephone()));
            }
            if(StringUtils.isNotBlank(archivesCard.getFillLink())){
                archivesCard.setFillLink(StringUtils.encryptPhone(archivesCard.getFillLink()));
            }
            if(StringUtils.isNotBlank(archivesCard.getRptLink())){
                archivesCard.setRptLink(StringUtils.encryptPhone(archivesCard.getRptLink()));
            }
            // 不关联职业病鉴定档案的 按原先流程判断显示职业病鉴定结果区域
            if (null == this.archivesCard.getFkByMainId() || null == this.archivesCard.getFkByMainId().getRid()) {
                if("1".equals(archivesCard.getFkByJdTypeId().getExtendS1()) && "1".equals(archivesCard.getFkByJdRstId().getExtendS1())){
                    this.ifAgainRst = Boolean.FALSE;
                }else if("2".equals(archivesCard.getFkByJdTypeId().getExtendS1()) && "1".equals(archivesCard.getFkByJdRstId().getExtendS1()) &&  archivesCard.getFkByJdAgainRstId() != null && "1".equals(archivesCard.getFkByJdAgainRstId().getExtendS1())){
                    this.ifAgainRst = Boolean.FALSE;
                }else {
                    this.ifAgainRst = Boolean.TRUE;
                }
            }
        }
        //获取历次审核
        historyList = this.appraisalRptCardService.findHisotryList(rid, 8);
        //初始化历次审核意见
        initHistoryCheckout();
    }


    /**
     * @Description: 历次审核意见处理
     *
     * @MethodAuthor pw,2021年11月10日
     */
    public void initHistoryCheckout(){
        if(!CollectionUtils.isEmpty(historyList)){
            List<Object[]> checkOutList = new ArrayList<>();
            for(Object[] objArr:historyList){
                Integer opegFlag = null == objArr[7] ? 0 : Integer.parseInt(objArr[7].toString()) ;
                String type = null;
                //审核级别为3
                if("3".equals(checkLevel)){
                    Integer ifCityDirect =  null == objArr[5] ? null : Integer.parseInt(objArr[5].toString()) ;
                    //根据是否市直属判断审核类型
                    if(Integer.valueOf(1).equals(ifCityDirect)){
                        switch (opegFlag){
                            case 41:
                                type = "市级审核通过";
                                break;
                            case 13:
                                type = "市级审核退回";
                                break;
                            case 42:
                                type = "省级审核通过";
                                break;
                            case 32:
                                type = "省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }else{
                        switch (opegFlag) {
                            case 31:
                                type = "区县级审核通过";
                                break;
                            case 11:
                                type = "区县级审核退回";
                                break;
                            case 41:
                                type = "市级审核通过";
                                break;
                            case 22:
                                type = "市级审核退回";
                                break;
                            case 42:
                                type = "省级审核通过";
                                break;
                            case 32:
                                type = "省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }
                }
                //审核级别为2
                if("2".equals(checkLevel)){
                    Integer ifProvDirect =  null == objArr[6] ? null : Integer.parseInt(objArr[6].toString()) ;
                    //根据是否省直属判断审核类型
                    if(Integer.valueOf(1).equals(ifProvDirect)){
                        switch (opegFlag){
                            case 42:
                                type = "1".equals(this.platVersion)?"市级审核通过":"省级审核通过";
                                break;
                            case 32:
                                type = "1".equals(this.platVersion)?"市级审核退回":"省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }else{
                        switch (opegFlag) {
                            case 43:
                                type = "区县级审核通过";
                                break;
                            case 11:
                                type = "区县级审核退回";
                                break;
                            case 42:
                                type = "1".equals(this.platVersion)?"市级审核通过":"省级审核通过";
                                break;
                            case 32:
                                type = "1".equals(this.platVersion)?"市级审核退回":"省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }
                }
                objArr[4] = type;
                if(type != null){
                    checkOutList.add(objArr);
                }
            }
            historyList = checkOutList;
        }
    }

    @Override
    public void processData(List<?> list) {
    	try {
            if (StringUtils.isBlank(limitTime) || null==level) {
                return;
            }
            if (null!=list && list.size()>0) {
                List<Object[]> result = (List<Object[]>) list;
                for (Object[] obj : result) {
                    if (null == obj[12]) {
                        continue;
                    }
                    if (1==level.intValue() && "1".equals(obj[12].toString()) && null!=obj[13]) {//初审-待审核
                    	obj[16] = calLimitTime(DateUtils.parseDate(obj[13].toString()));
					}else if (2==level.intValue() && "3".equals(obj[12].toString()) && null!=obj[14]) {//复审-待审核
						obj[16] = calLimitTime(DateUtils.parseDate(obj[14].toString()));
					}else if (3==level.intValue() && "5".equals(obj[12].toString()) && null!=obj[15]) {//终审-待审核
						obj[16] = calLimitTime(DateUtils.parseDate(obj[15].toString()));
					}
                    //证件号码加密
                    if (null!=obj[4]) {
                    	obj[4] = StringUtils.encryptIdc(obj[4].toString());
					}
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
 	 * <p>方法描述：计算处理期限</p>
 	 * @MethodAuthor qrr,2021年10月26日,calLimitTime
     * */
	private int calLimitTime(Date sDate) {
		if (null==sDate) {
			return -1;
		}
		//剩余天数
		int day = HolidayUtil.calRemainingDate(sDate, new Date(),limitTime);
		if (day == 0) {
			return -1;
		}
		return day;
	}

    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder();
        sb.append(" FROM TD_ZWJD_ARCHIVES_CARD T ");
        sb.append(" INNER JOIN TD_ZW_BGK_LAST_STA T2 on T.rid=T2.BUS_ID and T2.CART_TYPE=8 ");
        sb.append(" INNER JOIN TS_UNIT T3 on T.JD_UNIT_ID = T3.RID ");
        sb.append(" INNER JOIN TS_ZONE T4 on T3.ZONE_ID = T4.RID ");
        sb.append(" LEFT JOIN TS_SIMPLE_CODE T5 on T.JD_TYPE_ID = T5.RID ");
        sb.append(" LEFT JOIN TS_SIMPLE_CODE T6 on T.JD_RST_ID = T6.RID ");
        sb.append(" LEFT JOIN TS_SIMPLE_CODE T7 on T.JD_AGAIN_RST_ID = T7.RID ");
        sb.append(" WHERE 1=1  ");
        //鉴定机构地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sb.append(" AND T4.ZONE_GB LIKE :zonecode escape '\\\'");
            this.paramMap.put("zonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        //用人单位名称
        if (StringUtils.isNotBlank(this.searchUnitName)) {
            sb.append(" AND T.CRPT_NAME LIKE :unitName escape '\\\'");
            this.paramMap.put("unitName", "%" + StringUtils.convertBFH(this.searchUnitName.trim()) + "%");
        }
        //用工单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sb.append(" AND T.EMP_CRPT_NAME LIKE :crptName escape '\\\'");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        //姓名
        if (StringUtils.isNotBlank(this.searchPsnName)) {
            sb.append(" AND T.PERSONNEL_NAME LIKE :searchPsnName escape '\\\'");
            this.paramMap.put("searchPsnName", "%" + StringUtils.convertBFH(this.searchPsnName.trim()) + "%");
        }
        //证件号码
        if (StringUtils.isNotBlank(this.searchIdc)) {
            sb.append(" AND T.IDC =:searchIdc");
            this.paramMap.put("searchIdc", this.searchIdc.trim());
        }
        //鉴定类型
        if (null!=searchJdTypes && searchJdTypes.length>0) {
        	sb.append(" AND T.JD_TYPE_ID IN (").append(StringUtils.array2string(searchJdTypes, ",")).append(")");
		}
        //鉴定日期
        if (null != this.searchJdBdate) {
            sb.append(" AND T.APRS_CENT_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchJdBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchJdEdate) {
            sb.append(" AND T.APRS_CENT_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchJdEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        //首次鉴定结论
        if (StringUtils.isNotBlank(this.selectFirstJdResultIds)) {
        	sb.append(" AND T.JD_RST_ID IN (").append(this.selectFirstJdResultIds).append(")");
		}
        //再次鉴定结论
        if (StringUtils.isNotBlank(this.selectNextJdResulIds)) {
        	sb.append(" AND T.JD_AGAIN_RST_ID IN (").append(this.selectNextJdResulIds).append(")");
		}
        //报告日期
        if (null != this.searchRptBdate) {
            sb.append(" AND T.RPT_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRptBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchRptEdate) {
            sb.append(" AND T.RPT_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRptEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != states && states.length > 0) {
            sb.append(" AND T2.STATE IN (:list)");
            this.paramMap.put("list", Arrays.asList(states));
        }
        //接收日期
        if (1==this.level.intValue()) {//初审
        	if (null != this.searchRcvBdate) {
            	sb.append(" AND T2.COUNTY_RCV_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRcvBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
    		}
    		if (null != this.searchRcvEdate) {
    			sb.append(" AND T2.COUNTY_RCV_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRcvEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
    		}
		}else if (2==this.level.intValue()) {//复审+市直属初审
			if (null != this.searchRcvBdate) {
            	sb.append(" AND T2.CITY_RCV_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRcvBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
    		}
    		if (null != this.searchRcvEdate) {
    			sb.append(" AND T2.CITY_RCV_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRcvEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
    		}
		}else if (3==this.level.intValue()) {//终审
			if (null != this.searchRcvBdate) {
            	sb.append(" AND T2.PRO_RCV_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRcvBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
    		}
    		if (null != this.searchRcvEdate) {
    			sb.append(" AND T2.PRO_RCV_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRcvEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
    		}
		}

        sb.append(" AND T.DEL_MARK !=1 ");
        String h2 = "SELECT COUNT(*) " + sb.toString();
        StringBuffer searchSql = new StringBuffer();
        searchSql.append("select T.rid, CASE WHEN T4.ZONE_TYPE > 2 THEN SUBSTR( T4.FULL_NAME, INSTR( T4.FULL_NAME, '_' ) + 1 ) ELSE T4.FULL_NAME END ZONE_NAME");
        searchSql.append(",T.CRPT_NAME,T.PERSONNEL_NAME,T.IDC,T.EMP_CRPT_NAME,T5.CODE_NAME AS JD_TYPE,to_char(T.APRS_CENT_DATE, 'yyyy-mm-dd'),T6.CODE_NAME AS FIRST_RESULT,T7.CODE_NAME AS NEXT_RESULT,T3.UNITNAME ");
        searchSql.append(",to_char(T.RPT_DATE, 'yyyy-mm-dd'),T2.STATE");
        searchSql.append(",T2.COUNTY_RCV_DATE,T2.CITY_RCV_DATE,T2.PRO_RCV_DATE,'' AS LIMIT_DAY,T.MAIN_ID ");
        searchSql.append(sb);
        searchSql.append(" ORDER BY T.RPT_DATE DESC, T4.ZONE_CODE,T.CRPT_NAME,T.PERSONNEL_NAME");
        return new String[] { searchSql.toString(), h2 };
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public Date getSearchRptBdate() {
        return searchRptBdate;
    }

    public void setSearchRptBdate(Date searchRptBdate) {
        this.searchRptBdate = searchRptBdate;
    }

    public Date getSearchRptEdate() {
        return searchRptEdate;
    }

    public void setSearchRptEdate(Date searchRptEdate) {
        this.searchRptEdate = searchRptEdate;
    }

    public Date getSearchRcvBdate() {
        return searchRcvBdate;
    }

    public void setSearchRcvBdate(Date searchRcvBdate) {
        this.searchRcvBdate = searchRcvBdate;
    }

    public Date getSearchRcvEdate() {
        return searchRcvEdate;
    }

    public void setSearchRcvEdate(Date searchRcvEdate) {
        this.searchRcvEdate = searchRcvEdate;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public List<TdZwBgkFlow> getBgkFlows() {
        return bgkFlows;
    }

    public void setBgkFlows(List<TdZwBgkFlow> bgkFlows) {
        this.bgkFlows = bgkFlows;
    }

	public String getSearchPsnName() {
		return searchPsnName;
	}

	public void setSearchPsnName(String searchPsnName) {
		this.searchPsnName = searchPsnName;
	}

	public String getSearchIdc() {
		return searchIdc;
	}

	public void setSearchIdc(String searchIdc) {
		this.searchIdc = searchIdc;
	}

	public String[] getSearchJdTypes() {
		return searchJdTypes;
	}

	public void setSearchJdTypes(String[] searchJdTypes) {
		this.searchJdTypes = searchJdTypes;
	}

	public List<TsSimpleCode> getJdTypeList() {
		return jdTypeList;
	}

	public void setJdTypeList(List<TsSimpleCode> jdTypeList) {
		this.jdTypeList = jdTypeList;
	}

	public List<TsSimpleCode> getFirstJdResultList() {
		return firstJdResultList;
	}

	public void setFirstJdResultList(List<TsSimpleCode> firstJdResultList) {
		this.firstJdResultList = firstJdResultList;
	}

	public String getSelectFirstJdResultNames() {
		return selectFirstJdResultNames;
	}

	public void setSelectFirstJdResultNames(String selectFirstJdResultNames) {
		this.selectFirstJdResultNames = selectFirstJdResultNames;
	}

	public String getSelectFirstJdResultIds() {
		return selectFirstJdResultIds;
	}

	public void setSelectFirstJdResultIds(String selectFirstJdResultIds) {
		this.selectFirstJdResultIds = selectFirstJdResultIds;
	}

	public List<TsSimpleCode> getNextJdResultList() {
		return nextJdResultList;
	}

	public void setNextJdResultList(List<TsSimpleCode> nextJdResultList) {
		this.nextJdResultList = nextJdResultList;
	}

	public String getSelectNextJdResulNames() {
		return selectNextJdResulNames;
	}

	public void setSelectNextJdResulNames(String selectNextJdResulNames) {
		this.selectNextJdResulNames = selectNextJdResulNames;
	}

	public String getSelectNextJdResulIds() {
		return selectNextJdResulIds;
	}

	public void setSelectNextJdResulIds(String selectNextJdResulIds) {
		this.selectNextJdResulIds = selectNextJdResulIds;
	}

	public Date getSearchJdBdate() {
		return searchJdBdate;
	}

	public void setSearchJdBdate(Date searchJdBdate) {
		this.searchJdBdate = searchJdBdate;
	}

	public Date getSearchJdEdate() {
		return searchJdEdate;
	}

	public void setSearchJdEdate(Date searchJdEdate) {
		this.searchJdEdate = searchJdEdate;
	}

    public TdZwHethAppraisalRptCardListBean getRptCardListBean() {
        return rptCardListBean;
    }

    public void setRptCardListBean(TdZwHethAppraisalRptCardListBean rptCardListBean) {
        this.rptCardListBean = rptCardListBean;
    }

    public List<Object[]> getHistoryList() {
        return historyList;
    }

    public void setHistoryList(List<Object[]> historyList) {
        this.historyList = historyList;
    }
}
