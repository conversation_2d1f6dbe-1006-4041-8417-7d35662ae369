package com.chis.modules.heth.comm.utils;

import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>类描述： </p>
 *
 * @ClassAuthor: yzz
 * @date： 2022年09月12日
 **/
public class UnitbasicinfoUtils {

    /**导入数据中空值判断值*/
    private static String nullVal;
    private static List<String> nullValList;
    static {
        //导入数据中空值的判断值
        nullVal = PropertyUtils.getValueWithoutException("siteMonitoringNullVal");
        nullValList = new ArrayList<>();
        if (StringUtils.isNotBlank(nullVal)) {
            nullValList = StringUtils.string2list(nullVal, ",");
        }
    }
    /**
     * <p>方法描述：和配置值比较，判断是否无需对照,直接存空 </p>
     * @MethodAuthor： yzz
     * @Date：2022-09-12
     **/
    public static Boolean ifMatcherNull(String str){
        str = StringUtils.objectToString(str);
        if (StringUtils.isBlank(str)) {
            return true;
        }
        return nullValList.contains(str);
    }

    /**
     * <p>方法描述：获取对照的业务类型</p>
     * @MethodAuthor： yzz
     * @Date：2022-09-12
     **/
    public static Integer getType(String name){
        Integer type=null;
        if(StringUtils.isNotBlank(name)){
            if("3年内技术改造、引进项目情况".equals(name)||
                    "有无接触粉尘".equals(name)||
                    "有无接触化学因素".equals(name)||
                    "有无接触物理因素".equals(name)||
                    "上一年度在岗期间职业健康检查情况".equals(name)||
                    "防尘口罩-发放情况".equals(name)||
                    "防毒口罩或面罩-发放情况".equals(name)||
                    "防噪声耳罩或耳塞-发放情况".equals(name)){
                type=32;
            }else if("预评价开展情况".equals(name)||
                    "职业病防护设施专篇".equals(name)||
                    "控制效果评价开展情况".equals(name)){
                type=34;
            }else if("上一年度检测情况".equals(name)||
                    "是否检测粉尘因素".equals(name)||
                    "化学毒物有无检测".equals(name)||
                    "物理因素有无检测".equals(name)){
                type=43;
            }else if("防尘设施-设置情况".equals(name)||
                    "防毒设施-设置情况".equals(name)||
                    "防噪声设施-设置情况".equals(name)||
                    "粉尘职业病危害警示标识及警示说明".equals(name)||
                    "化学毒物职业病危害警示标识及警示说明".equals(name)||
                    "噪声职业病危害警示标识及警示说明".equals(name)){
                type=36;
            }else if("防尘设施-防护效果".equals(name)||
                    "防毒设施-防护效果".equals(name)||
                    "防噪声设施-防护效果".equals(name)){
                type=37;
            }else if("防尘口罩-佩戴情况".equals(name)||
                    "防毒口罩或面罩-佩戴情况".equals(name)||
                    "防噪声耳罩或耳塞-佩戴情况".equals(name)){
                type=38;
            }else if( "粉尘因素有无体检".equals(name)||
                    "化学因素有无体检".equals(name)||
                    "物理因素有无体检".equals(name)){
                type=44;
            }
        }
        return type;
    }

}
