package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * GBZ188修订记录
 * 
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置<br/>
 * 
 * <AUTHOR>
 * @createDate 2014-9-1
 */
@Entity
@Table(name = "TD_ZW_GBZLOG")
@SequenceGenerator(name = "TdZwGbzlog_Seq", sequenceName = "TD_ZW_GBZLOG_SEQ", allocationSize = 1)
public class TdZwGbzlogComm implements java.io.Serializable {

	private static final long serialVersionUID = 2879437169043370938L;
	private Integer rid;
	private String tabName;
	private Integer recId;
	private Integer verNum;
	private short delFlag;
	private Date createDate;
	private Integer createManid;

	public TdZwGbzlogComm() {
	}

	public TdZwGbzlogComm(Integer rid, String tabName, Integer recId,
                          Integer verNum, short delFlag, Date createDate,
                          Integer createManid) {
		this.rid = rid;
		this.tabName = tabName;
		this.recId = recId;
		this.verNum = verNum;
		this.delFlag = delFlag;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	@Id
	@Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwGbzlog_Seq")
    public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Column(name = "TAB_NAME" , length = 50)
	public String getTabName() {
		return this.tabName;
	}

	public void setTabName(String tabName) {
		this.tabName = tabName;
	}

	@Column(name = "REC_ID" )
	public Integer getRecId() {
		return this.recId;
	}

	public void setRecId(Integer recId) {
		this.recId = recId;
	}

	@Column(name = "VER_NUM" )
	public Integer getVerNum() {
		return this.verNum;
	}

	public void setVerNum(Integer verNum) {
		this.verNum = verNum;
	}

	@Column(name = "DEL_FLAG" )
	public short getDelFlag() {
		return this.delFlag;
	}

	public void setDelFlag(short delFlag) {
		this.delFlag = delFlag;
	}

    @Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" )
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" )
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

}