package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-3-27
 */
@Entity
@Table(name = "TB_TJ_CRPT_INVEST")
@SequenceGenerator(name = "TbTjCrptInvest", sequenceName = "TB_TJ_CRPT_INVEST_SEQ", allocationSize = 1)
public class TbTjCrptInvest implements java.io.Serializable, IzwCrptInfo {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsSimpleCode fkByInfoSourceId;
	private TsZone fkByZoneId;
	private String institutionCode;
	private String crptName;
	private Integer ifSubOrg;
	private TsSimpleCode fkByIndusTypeId;
	private TsSimpleCode fkByEconomyId;
	private TsSimpleCode fkByCrptSizeId;
	private String address;
	private String corporateDeputy;
	private String phone;
	private String linkman2;
	private String linkphone2;
	private Integer workForce;
	private Integer holdCardMan;
	private Date investDate;
	private TsUnit fkByFillUnitId;
	private Integer stateMark;
	private Date checkDate;
	private TsUnit fkByCheckUnitId;
	private String backRsn;
	private TsZone fkByOrginZoneId;
	private TbTjCrptCheck fkByCheckId;
	private Integer existState;
	private Integer produceState;
	private TsSimpleCode fkByNoProduceStateId;
	private String otherProduceState;
	private Integer ifHasUranium;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	public TbTjCrptInvest() {
	}

	public TbTjCrptInvest(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjCrptInvest")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "INFO_SOURCE_ID")			
	public TsSimpleCode getFkByInfoSourceId() {
		return fkByInfoSourceId;
	}

	public void setFkByInfoSourceId(TsSimpleCode fkByInfoSourceId) {
		this.fkByInfoSourceId = fkByInfoSourceId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ZONE_ID")			
	public TsZone getFkByZoneId() {
		return fkByZoneId;
	}

	public void setFkByZoneId(TsZone fkByZoneId) {
		this.fkByZoneId = fkByZoneId;
	}	
			
	@Column(name = "INSTITUTION_CODE")	
	public String getInstitutionCode() {
		return institutionCode;
	}

	public void setInstitutionCode(String institutionCode) {
		this.institutionCode = institutionCode;
	}	
			
	@Column(name = "CRPT_NAME")	
	public String getCrptName() {
		return crptName;
	}

	public void setCrptName(String crptName) {
		this.crptName = crptName;
	}	
			
	@Column(name = "IF_SUB_ORG")	
	public Integer getIfSubOrg() {
		return ifSubOrg;
	}

	public void setIfSubOrg(Integer ifSubOrg) {
		this.ifSubOrg = ifSubOrg;
	}	
			
	@ManyToOne
	@JoinColumn(name = "INDUS_TYPE_ID")			
	public TsSimpleCode getFkByIndusTypeId() {
		return fkByIndusTypeId;
	}

	public void setFkByIndusTypeId(TsSimpleCode fkByIndusTypeId) {
		this.fkByIndusTypeId = fkByIndusTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ECONOMY_ID")			
	public TsSimpleCode getFkByEconomyId() {
		return fkByEconomyId;
	}

	public void setFkByEconomyId(TsSimpleCode fkByEconomyId) {
		this.fkByEconomyId = fkByEconomyId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_SIZE_ID")			
	public TsSimpleCode getFkByCrptSizeId() {
		return fkByCrptSizeId;
	}

	public void setFkByCrptSizeId(TsSimpleCode fkByCrptSizeId) {
		this.fkByCrptSizeId = fkByCrptSizeId;
	}	
			
	@Column(name = "ADDRESS")	
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}	
			
	@Column(name = "CORPORATE_DEPUTY")	
	public String getCorporateDeputy() {
		return corporateDeputy;
	}

	public void setCorporateDeputy(String corporateDeputy) {
		this.corporateDeputy = corporateDeputy;
	}	
			
	@Column(name = "PHONE")	
	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}	
			
	@Column(name = "LINKMAN2")	
	public String getLinkman2() {
		return linkman2;
	}

	public void setLinkman2(String linkman2) {
		this.linkman2 = linkman2;
	}	
			
	@Column(name = "LINKPHONE2")	
	public String getLinkphone2() {
		return linkphone2;
	}

	public void setLinkphone2(String linkphone2) {
		this.linkphone2 = linkphone2;
	}	
			
	@Column(name = "WORK_FORCE")	
	public Integer getWorkForce() {
		return workForce;
	}

	public void setWorkForce(Integer workForce) {
		this.workForce = workForce;
	}	
			
	@Column(name = "HOLD_CARD_MAN")	
	public Integer getHoldCardMan() {
		return holdCardMan;
	}

	public void setHoldCardMan(Integer holdCardMan) {
		this.holdCardMan = holdCardMan;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "INVEST_DATE")			
	public Date getInvestDate() {
		return investDate;
	}

	public void setInvestDate(Date investDate) {
		this.investDate = investDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "FILL_UNIT_ID")			
	public TsUnit getFkByFillUnitId() {
		return fkByFillUnitId;
	}

	public void setFkByFillUnitId(TsUnit fkByFillUnitId) {
		this.fkByFillUnitId = fkByFillUnitId;
	}	
			
	@Column(name = "STATE_MARK")	
	public Integer getStateMark() {
		return stateMark;
	}

	public void setStateMark(Integer stateMark) {
		this.stateMark = stateMark;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "CHECK_DATE")			
	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CHECK_UNIT_ID")			
	public TsUnit getFkByCheckUnitId() {
		return fkByCheckUnitId;
	}

	public void setFkByCheckUnitId(TsUnit fkByCheckUnitId) {
		this.fkByCheckUnitId = fkByCheckUnitId;
	}	
			
	@Column(name = "BACK_RSN")	
	public String getBackRsn() {
		return backRsn;
	}

	public void setBackRsn(String backRsn) {
		this.backRsn = backRsn;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ORGIN_ZONE_ID")			
	public TsZone getFkByOrginZoneId() {
		return fkByOrginZoneId;
	}

	public void setFkByOrginZoneId(TsZone fkByOrginZoneId) {
		this.fkByOrginZoneId = fkByOrginZoneId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CHECK_ID")			
	public TbTjCrptCheck getFkByCheckId() {
		return fkByCheckId;
	}

	public void setFkByCheckId(TbTjCrptCheck fkByCheckId) {
		this.fkByCheckId = fkByCheckId;
	}	
			
	@Column(name = "EXIST_STATE")	
	public Integer getExistState() {
		return existState;
	}

	public void setExistState(Integer existState) {
		this.existState = existState;
	}	
			
	@Column(name = "PRODUCE_STATE")	
	public Integer getProduceState() {
		return produceState;
	}

	public void setProduceState(Integer produceState) {
		this.produceState = produceState;
	}	
			
	@ManyToOne
	@JoinColumn(name = "NO_PRODUCE_STATE_ID")			
	public TsSimpleCode getFkByNoProduceStateId() {
		return fkByNoProduceStateId;
	}

	public void setFkByNoProduceStateId(TsSimpleCode fkByNoProduceStateId) {
		this.fkByNoProduceStateId = fkByNoProduceStateId;
	}	
			
	@Column(name = "OTHER_PRODUCE_STATE")	
	public String getOtherProduceState() {
		return otherProduceState;
	}

	public void setOtherProduceState(String otherProduceState) {
		this.otherProduceState = otherProduceState;
	}	
			
	@Column(name = "IF_HAS_URANIUM")	
	public Integer getIfHasUranium() {
		return ifHasUranium;
	}

	public void setIfHasUranium(Integer ifHasUranium) {
		this.ifHasUranium = ifHasUranium;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
}