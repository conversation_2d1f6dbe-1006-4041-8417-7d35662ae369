package com.chis.modules.heth.comm.json;

import java.io.Serializable;
import java.util.Date;

public class BhkAll<PERSON><PERSON>ck<PERSON>son implements Serializable {
    private static final long serialVersionUID = 3753092172463086890L;

    private String checkLevel;
    private String zoneType;
    private String username;
    private String searchZoneCodeEmp;
    private String searchCrptNameEmp;
    private String searchCreditCodeEmp;
    private String searchZoneCode;
    private String searchCrptName;
    private String searchCreditCode;
    private String searchIdc;
    private String searchPersonName;
    private String searchBhkType;
    private Date searchBhkBdate;
    private Date searchBhkEdate;
    private Date startCreateDate;
    private Date endCreateDate;
    private Date startRptPrintDate;
    private Date endRptPrintDate;
    private String selectOnGuardIds;
    private String selectBadRsnIds;
    private String searchSelBhkrstIds;
    private Date searchRcvBdate;
    private Date searchRcvEdate;
    private String jcTypes;
    private String ifRhks;
    private String ifAbnormals;
    private String searchAbnormals;
    private String searchUnitId;
    private Date limitDate;

    public BhkAllCheckJson() {
    }

    public String getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(String checkLevel) {
        this.checkLevel = checkLevel;
    }

    public String getZoneType() {
        return zoneType;
    }

    public void setZoneType(String zoneType) {
        this.zoneType = zoneType;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getSearchZoneCodeEmp() {
        return searchZoneCodeEmp;
    }

    public void setSearchZoneCodeEmp(String searchZoneCodeEmp) {
        this.searchZoneCodeEmp = searchZoneCodeEmp;
    }

    public String getSearchCrptNameEmp() {
        return searchCrptNameEmp;
    }

    public void setSearchCrptNameEmp(String searchCrptNameEmp) {
        this.searchCrptNameEmp = searchCrptNameEmp;
    }

    public String getSearchCreditCodeEmp() {
        return searchCreditCodeEmp;
    }

    public void setSearchCreditCodeEmp(String searchCreditCodeEmp) {
        this.searchCreditCodeEmp = searchCreditCodeEmp;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchCreditCode() {
        return searchCreditCode;
    }

    public void setSearchCreditCode(String searchCreditCode) {
        this.searchCreditCode = searchCreditCode;
    }

    public String getSearchIdc() {
        return searchIdc;
    }

    public void setSearchIdc(String searchIdc) {
        this.searchIdc = searchIdc;
    }

    public String getSearchPersonName() {
        return searchPersonName;
    }

    public void setSearchPersonName(String searchPersonName) {
        this.searchPersonName = searchPersonName;
    }

    public String getSearchBhkType() {
        return searchBhkType;
    }

    public void setSearchBhkType(String searchBhkType) {
        this.searchBhkType = searchBhkType;
    }

    public Date getSearchBhkBdate() {
        return searchBhkBdate;
    }

    public void setSearchBhkBdate(Date searchBhkBdate) {
        this.searchBhkBdate = searchBhkBdate;
    }

    public Date getSearchBhkEdate() {
        return searchBhkEdate;
    }

    public void setSearchBhkEdate(Date searchBhkEdate) {
        this.searchBhkEdate = searchBhkEdate;
    }

    public Date getStartCreateDate() {
        return startCreateDate;
    }

    public void setStartCreateDate(Date startCreateDate) {
        this.startCreateDate = startCreateDate;
    }

    public Date getEndCreateDate() {
        return endCreateDate;
    }

    public void setEndCreateDate(Date endCreateDate) {
        this.endCreateDate = endCreateDate;
    }

    public Date getStartRptPrintDate() {
        return startRptPrintDate;
    }

    public void setStartRptPrintDate(Date startRptPrintDate) {
        this.startRptPrintDate = startRptPrintDate;
    }

    public Date getEndRptPrintDate() {
        return endRptPrintDate;
    }

    public void setEndRptPrintDate(Date endRptPrintDate) {
        this.endRptPrintDate = endRptPrintDate;
    }

    public String getSelectOnGuardIds() {
        return selectOnGuardIds;
    }

    public void setSelectOnGuardIds(String selectOnGuardIds) {
        this.selectOnGuardIds = selectOnGuardIds;
    }

    public String getSelectBadRsnIds() {
        return selectBadRsnIds;
    }

    public void setSelectBadRsnIds(String selectBadRsnIds) {
        this.selectBadRsnIds = selectBadRsnIds;
    }

    public Date getSearchRcvBdate() {
        return searchRcvBdate;
    }

    public void setSearchRcvBdate(Date searchRcvBdate) {
        this.searchRcvBdate = searchRcvBdate;
    }

    public Date getSearchRcvEdate() {
        return searchRcvEdate;
    }

    public void setSearchRcvEdate(Date searchRcvEdate) {
        this.searchRcvEdate = searchRcvEdate;
    }

    public String getJcTypes() {
        return jcTypes;
    }

    public void setJcTypes(String jcTypes) {
        this.jcTypes = jcTypes;
    }

    public String getIfRhks() {
        return ifRhks;
    }

    public void setIfRhks(String ifRhks) {
        this.ifRhks = ifRhks;
    }

    public String getIfAbnormals() {
        return ifAbnormals;
    }

    public void setIfAbnormals(String ifAbnormals) {
        this.ifAbnormals = ifAbnormals;
    }

    public String getSearchUnitId() {
        return searchUnitId;
    }

    public void setSearchUnitId(String searchUnitId) {
        this.searchUnitId = searchUnitId;
    }

    public Date getLimitDate() {
        return limitDate;
    }

    public void setLimitDate(Date limitDate) {
        this.limitDate = limitDate;
    }

    public String getSearchSelBhkrstIds() {
        return searchSelBhkrstIds;
    }

    public void setSearchSelBhkrstIds(String searchSelBhkrstIds) {
        this.searchSelBhkrstIds = searchSelBhkrstIds;
    }

    public String getSearchAbnormals() {
        return searchAbnormals;
    }

    public void setSearchAbnormals(String searchAbnormals) {
        this.searchAbnormals = searchAbnormals;
    }
}
