package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesSimpleBean;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  <p>类描述：
 *  放射单位单位选择
 *  已选择的不再选择
 *  地区为登录人所在省</p>
 * @ClassAuthor hsj 2022-08-20 16:34
 */
@ManagedBean(name="selectRadHethListBean")
@ViewScoped
public class SelectRadHethListBean extends FacesSimpleBean implements IProcessData{
	private static final long serialVersionUID = 1L;
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	/**查询条件：地区*/
	private List<TsZone> zoneList;
	private String searchZoneGb;
	private String searchZoneName;
	/**查询条件：单位名称*/
	private String searchName;
	/**查询条件：社会信用代码*/
	private String searchCode;

	/**页面已选择的*/
	private String selectIds;
	private Object[] object;


    public SelectRadHethListBean() {
    	this.ifSQL = true;
		 selectIds = JsfUtil.getRequestParameter("selectIds");
		 initZone();
    	this.searchAction();
	}

	private void initZone() {
		//当前登录人的省份
		String jsZone = Global.getUser().getTsUnit().getTsZone().getZoneGb().substring(0, 2) + "00000000";
		this.zoneList = this.commService.findZoneListByGbAndType(jsZone, true, null, null);
		searchZoneGb = zoneList.get(0).getZoneGb();
		searchZoneName = zoneList.get(0).getZoneName();
	}

	@Override
	public String[] buildHqls() {
    	StringBuffer sql = new StringBuffer();
		sql.append("SELECT T1.RID, CASE WHEN T2.ZONE_TYPE >2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME,'_')+1) ELSE T2.FULL_NAME END ZONE_NAME   ,T1.CREDIT_CODE");
		sql.append(" ,T.UNIT_NAME ,T.ADDRESS,T.LINK_MAN,T.LINK_TEL,T1.ZONE_ID");
		sql.append(" FROM  TB_TJ_RADHETH T  ");
		sql.append(" LEFT JOIN TS_UNIT T1  ON T.MANAGE_UNIT_ID = T1.RID   ");
		sql.append("  LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID  = T2.RID   ");
		sql.append(" WHERE 1=1 ");
		if(StringUtils.isNotBlank(selectIds)){
			sql.append("  AND T1.RID !=  ").append(selectIds);
		}
		//地区
		if (StringUtils.isNotBlank(searchZoneGb)) {
			sql.append(" and T2.zone_gb like :searchZoneGb ");
			this.paramMap.put("searchZoneGb", ZoneUtil.zoneSelect(searchZoneGb)+"%");
		}
		//社会信用代码
		if(StringUtils.isNotBlank(searchCode)){
			sql.append(" and T1.CREDIT_CODE like :searchCode escape '\\\'");
			this.paramMap.put("searchCode", "%"+StringUtils.convertBFH(searchCode.trim())+"%");
		}
		if (StringUtils.isNotBlank(searchName)) {
			sql.append(" and T.UNIT_NAME like :searchName escape '\\\'");
			this.paramMap.put("searchName", "%"+StringUtils.convertBFH(searchName.trim())+"%");
		}
		sql.append(" ORDER BY T2.ZONE_GB ,T.UNIT_NAME ");
		String h2 = "SELECT COUNT(*) FROM (" + sql.toString() + ")";
        String h1 = "SELECT * FROM (" + sql.toString() + ")";
        return new String[]{h1,h2};
	}
	/**
	 *  <p>方法描述：清空地区</p>
	 * @MethodAuthor hsj 2022-08-22 15:44
	 */
	public void clearSearchZone(){
		this.searchZoneGb = null;
		this.searchZoneName = null;
	}
	@Override
	public void processData(List<?> list) {
	}
	public void selectAction() {
		if (null!=object) {
			Map<String, Object[]> map = new HashMap<String, Object[]>();
			map.put("selectPros", this.object);
			RequestContext.getCurrentInstance().closeDialog(map);
		}else {
			JsfUtil.addErrorMessage("请选择数据！");
			return;
		}
	}
    /**
 	 * <p>方法描述：关闭</p>
 	 * @MethodAuthor qrr,2018年4月9日,dialogClose
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

	public String getSearchZoneGb() {
		return searchZoneGb;
	}

	public void setSearchZoneGb(String searchZoneGb) {
		this.searchZoneGb = searchZoneGb;
	}

	public String getSearchZoneName() {
		return searchZoneName;
	}

	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}

	public String getSearchName() {
		return searchName;
	}

	public void setSearchName(String searchName) {
		this.searchName = searchName;
	}

	public String getSearchCode() {
		return searchCode;
	}

	public void setSearchCode(String searchCode) {
		this.searchCode = searchCode;
	}

	public String getSelectIds() {
		return selectIds;
	}

	public void setSelectIds(String selectIds) {
		this.selectIds = selectIds;
	}

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public Object[] getObject() {
		return object;
	}

	public void setObject(Object[] object) {
		this.object = object;
	}
}
