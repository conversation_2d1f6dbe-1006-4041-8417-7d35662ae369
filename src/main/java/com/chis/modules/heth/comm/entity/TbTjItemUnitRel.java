package com.chis.modules.heth.comm.entity;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 计量单位关系表
 * <AUTHOR>
 * @createTime 2020-6-24
 */
@Entity
@Table(name = "TB_TJ_ITEM_UNIT_REL")
@SequenceGenerator(name = "TbTjItemUnitRel", sequenceName = "TB_TJ_ITEM_UNIT_REL_SEQ", allocationSize = 1)
public class TbTjItemUnitRel implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbTjItems fkByItemId;
	private TsSimpleCode fkByMsruntId;
	private BigDecimal minval;
	private BigDecimal maxval;
	private String itemStdvalue;
	private Integer ifDefaut;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TbTjItemUnitRel() {
	}

	public TbTjItemUnitRel(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjItemUnitRel")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ITEM_ID")			
	public TbTjItems getFkByItemId() {
		return fkByItemId;
	}

	public void setFkByItemId(TbTjItems fkByItemId) {
		this.fkByItemId = fkByItemId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "MSRUNT_ID")			
	public TsSimpleCode getFkByMsruntId() {
		return fkByMsruntId;
	}

	public void setFkByMsruntId(TsSimpleCode fkByMsruntId) {
		this.fkByMsruntId = fkByMsruntId;
	}	
			
	@Column(name = "MINVAL")	
	public BigDecimal getMinval() {
		return minval;
	}

	public void setMinval(BigDecimal minval) {
		this.minval = minval;
	}	
			
	@Column(name = "MAXVAL")	
	public BigDecimal getMaxval() {
		return maxval;
	}

	public void setMaxval(BigDecimal maxval) {
		this.maxval = maxval;
	}	
			
	@Column(name = "ITEM_STDVALUE")	
	public String getItemStdvalue() {
		return itemStdvalue;
	}

	public void setItemStdvalue(String itemStdvalue) {
		this.itemStdvalue = itemStdvalue;
	}	
			
	@Column(name = "IF_DEFAUT")	
	public Integer getIfDefaut() {
		return ifDefaut;
	}

	public void setIfDefaut(Integer ifDefaut) {
		this.ifDefaut = ifDefaut;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}