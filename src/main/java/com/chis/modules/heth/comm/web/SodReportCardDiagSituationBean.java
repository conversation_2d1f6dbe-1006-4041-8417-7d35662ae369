package com.chis.modules.heth.comm.web;

import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesBean;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.context.RequestContext;
import org.primefaces.model.DefaultStreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.*;

/**
 * 疑似职业病诊断情况Bean
 * <p>使用对象：用于各级管理机构查询辖区内有疑似职业病报告卡的劳动者的诊断情况
 *
 * <AUTHOR>
 * @date 2022/6/27
 */
@ManagedBean(name = "sodReportCardDiagSituationBean")
@ViewScoped
public class SodReportCardDiagSituationBean extends FacesBean {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
     * 查询条件：地区集合
     */
    private List<TsZone> searchZoneList = new ArrayList<>();
    /**
     * 查询条件：地区编码
     */
    private String searchZoneCode;
    /**
     * 查询条件：地区名称
     */
    private String searchZoneName;
    /**
     * 查询条件：疑似职业病报告日期-开始日期
     */
    private Date searchReportBeginDate;
    /**
     * 查询条件：疑似职业病报告日期-结束日期
     */
    private Date searchReportEndDate;
    private Map<Integer, String> simpleCodeMap;
    /**
     * 查询条件-职业危害因素
     */
    private String selectBadRsnNames;
    private String selectBadRsnRids;
    private List<TsSimpleCode> badRsnList;

    private List<Object[]> dataTableList;

    public SodReportCardDiagSituationBean() {
        this.dataTableList = new ArrayList<>();
        init();
    }

    private void init() {
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        //地区
        if (null == this.searchZoneList || this.searchZoneList.size() <= 0) {
            this.searchZoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "4");
            this.searchZoneCode = this.searchZoneList.get(0).getZoneCode();
            this.searchZoneName = this.searchZoneList.get(0).getZoneName();
        }
        //疑似职业病报告日期
        this.searchReportEndDate = new Date();
        this.searchReportBeginDate = DateUtils.addYears(this.searchReportEndDate, -1);
        this.simpleCodeMap = new HashMap<>(16);
        //危害因素
        this.badRsnList = commService.findSimpleCodesByTypeId("5007");
        if (!CollectionUtils.isEmpty(this.badRsnList)) {
            for (TsSimpleCode t : this.badRsnList) {
                this.simpleCodeMap.put(t.getRid(), t.getCodeName());
            }
        }
        searchAction();
    }

    public void searchAction() {
        if (verifyQueryRequiredFailed()) {
            return;
        }
        executeSearchSql();
    }

    private boolean verifyQueryRequiredFailed() {
        boolean verify = false;
        if (this.searchZoneCode == null) {
            JsfUtil.addErrorMessage("请选择地区！");
            verify = true;
        }
        if (this.searchReportBeginDate == null) {
            JsfUtil.addErrorMessage("请选择疑似职业病报告开始日期！");
            verify = true;
        }
        if (this.searchReportEndDate == null) {
            JsfUtil.addErrorMessage("请选择疑似职业病报告结束日期！");
            verify = true;
        }
        return verify;
    }

    public void executeSearchSql() {
        Map<String, Object> paramMap = new HashMap<>();
        StringBuilder sql = new StringBuilder();
        boolean showCity = ZoneUtil.getZoneType(this.searchZoneCode) == 2;
        boolean showDistrict = ZoneUtil.getZoneType(this.searchZoneCode) == 3;
        String showZoneStr = showCity ? "CITY" : (showDistrict ? "DISTRICT" : "STREET");
        String zoneType = showCity ? "3" : (showDistrict ? "4" : "5");
        sql.append("WITH RPT_TEMP_TABLE AS (SELECT YR.RID, ");
        sql.append("                               Z2.ZONE_GB                         AS                                       CITY_CODE, ");
        sql.append("                               Z2.ZONE_NAME                       AS                                       CITY, ");
        sql.append("                               Z1.ZONE_GB                         AS                                       DISTRICT_CODE, ");
        sql.append("                               Z1.ZONE_NAME                       AS                                       DISTRICT, ");
        sql.append("                               Z3.ZONE_GB                         AS                                       STREET_CODE, ");
        sql.append("                               Z3.ZONE_NAME                       AS                                       STREET, ");
        sql.append("                               DECODE(SC.EXTENDS1, 1, 1, 2, 2, 3) AS                                       SOURCE, ");
        sql.append("                               CASE ");
        sql.append("                                   WHEN NVL(O.RID, 0) = 0 OR SC1.CODE_NO <> '01' THEN 0 ");
        sql.append("                                   WHEN NVL(O.STATE_MARK, 0) <= 5 THEN 1 ");
        sql.append("                                   WHEN NVL(O.IS_DIS, 0) = 1 THEN 2 ");
        sql.append("                                   ELSE 3 END                     AS                                       DIAG, ");
        sql.append("                               YR.OCC_DISEID, ");
        sql.append("                               ROW_NUMBER() OVER (PARTITION BY YR.RID, YR.IDC, YR.OCC_DISEID ORDER BY O.APPLY_DATE, O.RID) RN ");
        sql.append("                        FROM TD_ZW_YSZYB_RPT YR ");
        sql.append("                                 INNER JOIN TD_ZW_BGK_LAST_STA S ");
        sql.append("                                            ON YR.RID = S.BUS_ID AND S.CART_TYPE = 2 AND NVL(S.STATE, 0) > 0 ");
        sql.append("                                 LEFT JOIN TS_SIMPLE_CODE SC ON YR.SOURCE_ID = SC.RID ");
        sql.append("                                 INNER JOIN TS_ZONE Z ON YR.ZONE_ID = Z.RID ");
        sql.append("                                 INNER JOIN TS_ZONE Z1 ON SUBSTR(Z.ZONE_GB, 1, 6) || '0000' = Z1.ZONE_GB ");
        sql.append("                                 INNER JOIN TS_ZONE Z2 ON SUBSTR(Z.ZONE_GB, 1, 4) || '000000' = Z2.ZONE_GB ");
        sql.append("                                 INNER JOIN TS_ZONE Z3 ON SUBSTR(Z.ZONE_GB, 1, 8) || '00' = Z3.ZONE_GB ");
        sql.append("                                 LEFT JOIN TS_SIMPLE_CODE SC1 ON YR.CARD_TYPE_ID = SC1.RID ");
        sql.append("                                 LEFT JOIN TS_SIMPLE_CODE SC2 ON YR.OCC_DISEID = SC2.RID ");
        sql.append("                                 LEFT JOIN TS_CODE_TYPE CT ON CT.CODE_TYPE_NAME = '5026' ");
        sql.append("                                 LEFT JOIN TS_SIMPLE_CODE SC3 ");
        sql.append("                                           ON (',' || SC2.EXTENDS3 || ',') LIKE ('%,' || SC3.CODE_NO || ',%') AND ");
        sql.append("                                              SC3.CODE_TYPE_ID = CT.RID ");
        sql.append("                                 LEFT JOIN TD_ZW_OCCDISCASE O ON ");
        sql.append("                                    YR.IDC = O.IDC AND NVL(O.DEL_MARK, 0) = 0 AND O.APPLY_DATE > YR.FIND_DATE AND ");
        sql.append("                                    SC3.RID = O.APY_ZYB_SMALL_ID ");
        sql.append("                        WHERE NVL(YR.DEL_MARK, 0) = 0 ");
        sql.append("      AND Z.ZONE_GB LIKE :zoneCode ");
        paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(StringUtils.objectToString(this.searchZoneCode)).trim()) + "%");
        sql.append("   AND YR.RPT_DATE >= TO_DATE(:searchReportBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
        paramMap.put("searchReportBeginDate", DateUtils.formatDate(this.searchReportBeginDate) + " 00:00:00");
        sql.append("   AND YR.RPT_DATE <= TO_DATE(:searchReportEndDate, 'YYYY-MM-DD HH24:MI:SS')");
        paramMap.put("searchReportEndDate", DateUtils.formatDate(this.searchReportEndDate) + " 23:59:59");
        if (StringUtils.isNotBlank(this.selectBadRsnRids)) {
            sql.append("    AND EXISTS(SELECT 1 FROM TD_ZW_YSZYB_TCH_BADRSN B WHERE YR.RID = B.MAIN_ID AND B.BADRSN_ID IN (:badRsnRids)) ");
            paramMap.put("badRsnRids", StringUtils.string2list(this.selectBadRsnRids, ","));
        }
        sql.append("), ");
        sql.append("     FIN_TEMP_TABLE AS (SELECT RT.CITY_CODE, RT.CITY, RT.DISTRICT_CODE, RT.DISTRICT, RT.STREET_CODE, RT.STREET, RT.SOURCE, RT.DIAG ");
        sql.append("                        FROM RPT_TEMP_TABLE RT ");
        sql.append("                        WHERE RT.RN = 1), ");
        sql.append("     SOURCE_TEMP_TABLE AS (SELECT * ");
        sql.append("                           FROM (SELECT ").append(showZoneStr).append("_CODE AS ZONE_CODE, ").append(showZoneStr).append(" AS ZONE, SOURCE FROM FIN_TEMP_TABLE) ");
        sql.append("                               PIVOT (COUNT(SOURCE) FOR SOURCE IN (1 AS COL1, 2 AS COL2, 3 AS COL3))), ");
        sql.append("     DIAG_TEMP_TABLE AS (SELECT * ");
        sql.append("                         FROM (SELECT ").append(showZoneStr).append("_CODE AS ZONE_CODE, ").append(showZoneStr).append(" AS ZONE, DIAG FROM FIN_TEMP_TABLE) ");
        sql.append("                             PIVOT (COUNT(DIAG) FOR DIAG IN (0 AS COL4,1 AS COL5,2 AS COL6,3 AS COL7))) ");
        sql.append("SELECT Z.ZONE_NAME                                                           AS ZONE, ");
        sql.append("       NVL((S.COL1 + S.COL2 + S.COL3), 0)                                    AS COL0, ");
        sql.append("       NVL(S.COL1, 0)                                                        AS COL1, ");
        sql.append("       NVL(S.COL2, 0)                                                        AS COL2, ");
        sql.append("       NVL(S.COL3, 0)                                                        AS COL3, ");
        sql.append("       NVL(D.COL4, 0)                                                        AS COL4, ");
        sql.append("       NVL(D.COL5, 0)                                                        AS COL5, ");
        sql.append("       NVL(D.COL6, 0)                                                        AS COL6, ");
        sql.append("       NVL(D.COL7, 0)                                                        AS COL7, ");
        sql.append("       ROUND(NVL((D.COL5 + D.COL6 + D.COL7) / (S.COL1 + S.COL2 + S.COL3) * 100, 0),2) AS COL8 ");
        sql.append("FROM TS_ZONE Z ");
        sql.append("         LEFT JOIN SOURCE_TEMP_TABLE S ON S.ZONE_CODE = Z.ZONE_GB ");
        sql.append("         LEFT JOIN DIAG_TEMP_TABLE D ON D.ZONE = S.ZONE ");
        sql.append("WHERE Z.ZONE_GB LIKE :zoneCode ");
        sql.append("  AND Z.ZONE_TYPE = :zoneType ");
        paramMap.put("zoneType", zoneType);
        sql.append("  AND Z.IF_REVEAL = 1 ");
        sql.append("ORDER BY Z.ZONE_CODE");
        this.dataTableList = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql.toString(), paramMap));
    }

    public void exportDataAction() {
        if (verifyQueryRequiredFailed()) {
            return;
        }

        RequestContext.getCurrentInstance().execute("getDownloadFileClick()");
    }

    public DefaultStreamedContent export() {
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("showStatus();");
        String[] excelHeaders = new String[]{"用人单位地区", "用人单位名称", "社会信用代码", "姓名", "证件类型", "证件号码", "疑似职业病种类", "疑似职业病名称", "疑似职业病报告日期"};
        ExcelExportUtil excelExportUtil = new ExcelExportUtil("疑似职业病未进入诊断名单", excelHeaders, pakExcelExportDataList(executeExportSql()));
        excelExportUtil.setFrozenPaneRowsNum(2);
        Workbook wb = excelExportUtil.exportExcel("");
        if (wb != null) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                String fileName = "疑似职业病未进入诊断名单.xlsx";
                fileName = URLEncoder.encode(fileName, "UTF-8");
                wb.write(baos);
                baos.flush();
                byte[] aa = baos.toByteArray();
                context.execute("hideStatus();");
                return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
            } catch (Exception e) {
                JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            }
        }
        context.execute("hideStatus();");
        return null;
    }

    private List<ExcelExportObject[]> pakExcelExportDataList(List<Object[]> dataList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        for (Object[] objects : dataList) {
            String fullName = StringUtils.objectToString(objects[0]);
            objects[0] = fullName.substring(fullName.indexOf("_") + 1);
            objects[5] = StringUtils.encryptIdc(StringUtils.objectToString(objects[5]));
        }
        List<ExcelExportObject[]> excelExportObjectList = new ArrayList<>();
        for (Object[] data : dataList) {
            ExcelExportObject[] objects = new ExcelExportObject[9];
            objects[0] = new ExcelExportObject(StringUtils.objectToString(data[0]));
            objects[1] = new ExcelExportObject(StringUtils.objectToString(data[1]));
            objects[2] = new ExcelExportObject(StringUtils.objectToString(data[2]), XSSFCellStyle.ALIGN_CENTER);
            objects[3] = new ExcelExportObject(StringUtils.objectToString(data[3]), XSSFCellStyle.ALIGN_CENTER);
            objects[4] = new ExcelExportObject(StringUtils.objectToString(data[4]), XSSFCellStyle.ALIGN_CENTER);
            objects[5] = new ExcelExportObject(StringUtils.objectToString(data[5]), XSSFCellStyle.ALIGN_CENTER);
            objects[6] = new ExcelExportObject(StringUtils.objectToString(data[6]), XSSFCellStyle.ALIGN_CENTER);
            objects[7] = new ExcelExportObject(StringUtils.objectToString(data[7]), XSSFCellStyle.ALIGN_CENTER);
            objects[8] = new ExcelExportObject(DateUtils.formatDate((Timestamp) data[8], "yyyy/MM/dd"), XSSFCellStyle.ALIGN_CENTER);
            excelExportObjectList.add(objects);
        }
        return excelExportObjectList;
    }

    public List<Object[]> executeExportSql() {
        Map<String, Object> paramMap = new HashMap<>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT FULL_NAME, CRPT_NAME, CREDIT_CODE, PERSONNEL_NAME, IDC_TYPE, IDC, YSZYB_TYPE_NAME, OCC_NAME, RPT_DATE ");
        sql.append("FROM (SELECT YR.RID, Z.ZONE_GB, Z.FULL_NAME, YR.CRPT_NAME, YR.CREDIT_CODE, YR.PERSONNEL_NAME, SC1.CODE_NAME AS IDC_TYPE, YR.IDC, YR.YSZYB_TYPE_NAME, SC2.CODE_NAME AS OCC_NAME, YR.RPT_DATE, ");
        sql.append("             CASE WHEN NVL(O.RID, 0) = 0 OR SC1.CODE_NO <> '01' THEN 0 ELSE 3 END AS DIAG, ");
        sql.append("             ROW_NUMBER() OVER (PARTITION BY YR.RID, YR.IDC, YR.OCC_DISEID ORDER BY O.APPLY_DATE, O.RID) RN ");
        sql.append("      FROM TD_ZW_YSZYB_RPT YR ");
        sql.append("               INNER JOIN TD_ZW_BGK_LAST_STA S ");
        sql.append("                          ON YR.RID = S.BUS_ID AND S.CART_TYPE = 2 AND NVL(S.STATE, 0) > 0 ");
        sql.append("               LEFT JOIN TS_SIMPLE_CODE SC ON YR.SOURCE_ID = SC.RID ");
        sql.append("               INNER JOIN TS_ZONE Z ON YR.ZONE_ID = Z.RID ");
        sql.append("               INNER JOIN TB_TJ_CRPT TC ON YR.CRPT_ID = TC.RID ");
        sql.append("               LEFT JOIN TS_SIMPLE_CODE SC1 ON YR.CARD_TYPE_ID = SC1.RID ");
        sql.append("               LEFT JOIN TS_SIMPLE_CODE SC2 ON YR.OCC_DISEID = SC2.RID ");
        sql.append("               LEFT JOIN TS_CODE_TYPE CT ON CT.CODE_TYPE_NAME = '5026' ");
        sql.append("               LEFT JOIN TS_SIMPLE_CODE SC3 ");
        sql.append("                         ON (',' || SC2.EXTENDS3 || ',') LIKE ('%,' || SC3.CODE_NO || ',%') AND ");
        sql.append("                            SC3.CODE_TYPE_ID = CT.RID ");
        sql.append("               LEFT JOIN TD_ZW_OCCDISCASE O ON YR.IDC = O.IDC AND NVL(O.DEL_MARK, 0) = 0 AND ");
        sql.append("                                               O.APPLY_DATE > YR.FIND_DATE AND ");
        sql.append("                                               SC3.RID = O.APY_ZYB_SMALL_ID ");
        sql.append("      WHERE NVL(YR.DEL_MARK, 0) = 0 ");
        sql.append("        AND Z.ZONE_GB LIKE :zoneCode ");
        paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(StringUtils.objectToString(this.searchZoneCode)).trim()) + "%");
        sql.append("        AND YR.RPT_DATE >= TO_DATE(:searchReportBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
        paramMap.put("searchReportBeginDate", DateUtils.formatDate(this.searchReportBeginDate) + " 00:00:00");
        sql.append("        AND YR.RPT_DATE <= TO_DATE(:searchReportEndDate, 'YYYY-MM-DD HH24:MI:SS')");
        paramMap.put("searchReportEndDate", DateUtils.formatDate(this.searchReportEndDate) + " 23:59:59");
        if (StringUtils.isNotBlank(this.selectBadRsnRids)) {
            sql.append("    AND EXISTS(SELECT 1 FROM TD_ZW_YSZYB_TCH_BADRSN B WHERE YR.RID = B.MAIN_ID AND B.BADRSN_ID IN (:badRsnRids)) ");
            paramMap.put("badRsnRids", StringUtils.string2list(this.selectBadRsnRids, ","));
        }
        sql.append("      ) RT ");
        sql.append("WHERE RT.RN = 1 AND RT.DIAG = 0 ");
        sql.append("ORDER BY RT.ZONE_GB, RT.CREDIT_CODE, RT.IDC, RT.RPT_DATE ");
        return CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql.toString(), paramMap));
    }

    public CommServiceImpl getCommService() {
        return commService;
    }

    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public Date getSearchReportBeginDate() {
        return searchReportBeginDate;
    }

    public void setSearchReportBeginDate(Date searchReportBeginDate) {
        this.searchReportBeginDate = searchReportBeginDate;
    }

    public Date getSearchReportEndDate() {
        return searchReportEndDate;
    }

    public void setSearchReportEndDate(Date searchReportEndDate) {
        this.searchReportEndDate = searchReportEndDate;
    }

    public Map<Integer, String> getSimpleCodeMap() {
        return simpleCodeMap;
    }

    public void setSimpleCodeMap(Map<Integer, String> simpleCodeMap) {
        this.simpleCodeMap = simpleCodeMap;
    }

    public String getSelectBadRsnNames() {
        return selectBadRsnNames;
    }

    public void setSelectBadRsnNames(String selectBadRsnNames) {
        this.selectBadRsnNames = selectBadRsnNames;
    }

    public String getSelectBadRsnRids() {
        return selectBadRsnRids;
    }

    public void setSelectBadRsnRids(String selectBadRsnRids) {
        this.selectBadRsnRids = selectBadRsnRids;
    }

    public List<TsSimpleCode> getBadRsnList() {
        return badRsnList;
    }

    public void setBadRsnList(List<TsSimpleCode> badRsnList) {
        this.badRsnList = badRsnList;
    }

    public List<Object[]> getDataTableList() {
        return dataTableList;
    }

    public void setDataTableList(List<Object[]> dataTableList) {
        this.dataTableList = dataTableList;
    }
}
