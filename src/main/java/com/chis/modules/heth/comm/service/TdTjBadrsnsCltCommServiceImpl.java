package com.chis.modules.heth.comm.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.modules.heth.comm.entity.TdTjBadrsnsClt;
import com.chis.modules.system.service.AbstractTemplate;

/**
 * @Description : 接触危害因素（数据录入）-业务层
 * @ClassAuthor : anjing
 * @Date : 2019/5/17 13:55
 **/
@Service
@Transactional(readOnly = true)
public class TdTjBadrsnsCltCommServiceImpl extends AbstractTemplate {

    /**
     * @Description : 根据体检信息Id获取危害因素集合
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 9:00
     **/
    public List<TdTjBadrsnsClt> selectBadrsnsCltListByBhkId(Integer bhkId) {
        if (null != bhkId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdTjBadrsnsClt t ");
            sb.append(" where t.fkByBhkId.rid = ").append(bhkId);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }
}
