package com.chis.modules.heth.comm.json;

import java.io.Serializable;
import java.util.Date;

/**
 *  <p>类描述：审核退回json</p>
 * @ClassAuthor hsj 2025-03-26 16:15
 */
public class SendBack<PERSON>heck<PERSON>son implements Serializable {
    private static final long serialVersionUID = 3753092172463086890L;

    private String filePath;
    private String checkLevel;
    private String zoneType;
    private String username;
    private Date limitDate;

    public SendBackCheckJson() {
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(String checkLevel) {
        this.checkLevel = checkLevel;
    }

    public String getZoneType() {
        return zoneType;
    }

    public void setZoneType(String zoneType) {
        this.zoneType = zoneType;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Date getLimitDate() {
        return limitDate;
    }

    public void setLimitDate(Date limitDate) {
        this.limitDate = limitDate;
    }
}
