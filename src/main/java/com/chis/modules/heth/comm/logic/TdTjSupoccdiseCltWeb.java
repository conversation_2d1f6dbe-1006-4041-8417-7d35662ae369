package com.chis.modules.heth.comm.logic;

import java.util.ArrayList;
import java.util.List;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * @Description : 疑似职业病-体检结果页面显示
 * @ClassAuthor : anjing
 * @Date : 2019/5/28 13:32
 **/
public class TdTjSupoccdiseCltWeb {

    private Integer  badRsnId;
    private TsSimpleCode tsSimpleCodeByBadrsnId;
    private String supoccdise;
    private List<Integer> supoccdiseIdList = new ArrayList<>();
    private List<TsSimpleCode> supoccdiseCltList = new ArrayList<>();

    private String crptName;


    public TdTjSupoccdiseCltWeb() {
    }

    public TsSimpleCode getTsSimpleCodeByBadrsnId() {
        return tsSimpleCodeByBadrsnId;
    }

    public void setTsSimpleCodeByBadrsnId(TsSimpleCode tsSimpleCodeByBadrsnId) {
        this.tsSimpleCodeByBadrsnId = tsSimpleCodeByBadrsnId;
    }

    public String getSupoccdise() {
        return supoccdise;
    }

    public void setSupoccdise(String supoccdise) {
        this.supoccdise = supoccdise;
    }

    public List<Integer> getSupoccdiseIdList() {
        return supoccdiseIdList;
    }

    public void setSupoccdiseIdList(List<Integer> supoccdiseIdList) {
        this.supoccdiseIdList = supoccdiseIdList;
    }

    public List<TsSimpleCode> getSupoccdiseCltList() {
        return supoccdiseCltList;
    }

    public void setSupoccdiseCltList(List<TsSimpleCode> supoccdiseCltList) {
        this.supoccdiseCltList = supoccdiseCltList;
    }

    public Integer getBadRsnId() {
        return badRsnId;
    }

    public void setBadRsnId(Integer badRsnId) {
        this.badRsnId = badRsnId;
    }

    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }
}
