package com.chis.modules.heth.comm.service;

import com.chis.modules.heth.comm.entity.TdTjSupoccdiselist;
import com.chis.modules.heth.comm.entity.TdZwBgkLastSta;
import com.chis.modules.heth.comm.entity.TdZwTjorginfoComm;
import com.chis.modules.heth.comm.entity.TdZwYszybRpt;
import com.chis.modules.heth.comm.utils.CalLimitTimeCommUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.TypedQuery;
import java.util.List;

/**
 * @Description : 疑似职业病报告卡-业务层
 * @ClassAuthor : anjing
 * @Date : 2019/9/4 18:20
 **/
@Service
@Transactional(readOnly = true)
public class TdZwYszybRptServiceCommImpl extends AbstractTemplate {

    /**
    * @Description : 根据体检主表Id获取疑似职业病集合
    * @MethodAuthor: anjing
    * @Date : 2019/9/5 9:16
    **/
    public List<TdTjSupoccdiselist> findSupoccdiselistListByBhkId(Integer bhkId) {
        if (null != bhkId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdTjSupoccdiselist t ");
            sb.append(" where t.tdTjBhk.rid = ").append(bhkId);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    public List<TdTjSupoccdiselist> findSupoccdiselistListByBhkId(Integer bhkId,Integer occdiseId) {
        if (null != bhkId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdTjSupoccdiselist t ");
            sb.append(" where t.tdTjBhk.rid = ").append(bhkId);
            sb.append(" and t.tsSimpleCodeByOccDiseid.rid = ").append(occdiseId);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    /**
    * @Description : 获取信息来源-职业健康检查
    * @MethodAuthor: anjing
    * @Date : 2019/9/5 9:41
    **/
    public TsSimpleCode findSimpleCodeByCodeTypeNameAndExtendS1(String codeTypeName, String extendS1) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select t from TsSimpleCode t ");
        sb.append(" where t.tsCodeType.codeTypeName = '").append(codeTypeName).append("' ");
        sb.append(" and t.extendS1 = '").append(extendS1).append("' ");
        sb.append(" and t.ifReveal = 1 ");
        List<TsSimpleCode> list = em.createQuery(sb.toString()).getResultList();
        if(CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
    * @Description : 根据当前登录用户获取是否具有健康检查资质
    * @MethodAuthor: anjing
    * @Date : 2019/9/7 10:55
    **/
    public List<TdZwTjorginfoComm> selectOrgListByOrgId(Integer orgId) {
        if (null != orgId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdZwTjorginfoComm t ");
            // 通用标记 0 停用 1 启用
            sb.append(" where t.state = 1");
            sb.append(" and t.tsUnit.rid = ").append(orgId);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    public TdZwBgkLastSta findTdZwBgkLastStaInfoByBusIdAndCardType(Integer budId, Integer cartType) {
        if (null != budId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdZwBgkLastSta t ");
            sb.append(" where t.busId = ").append(budId);
            sb.append(" and t.cartType = ").append(cartType);
            List<TdZwBgkLastSta> list = em.createQuery(sb.toString()).getResultList();
            if(!CollectionUtils.isEmpty(list)) {
                return list.get(0);
            }
            return null;
        }
        return null;
    }

    public <T> T findOneByHql(String hql, Class<T> t) {
        TypedQuery<T> query = em.createQuery(hql, t).setMaxResults(1);
        List<T> resultList = query.getResultList();
        if (resultList != null && resultList.size() > 0) {
            return resultList.get(0);
        }
        return null;
    }

}
