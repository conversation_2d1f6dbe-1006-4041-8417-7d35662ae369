package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-4-24
 */
@Entity
@Table(name = "TB_TJ_STAD_ITEMS")
@SequenceGenerator(name = "TbTjStadItems", sequenceName = "TB_TJ_STAD_ITEMS_SEQ", allocationSize = 1)
public class TbTjStadItems implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbTjStadItemsStatus fkByMainId;
	private TbTjItems fkByItemId;
	private BigDecimal minval;
	private BigDecimal maxval;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private Integer jdgptn;
	private Integer sex;
	private TsSimpleCode fkByMsruntId;
	
	public TbTjStadItems() {
	}

	public TbTjStadItems(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjStadItems")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TbTjStadItemsStatus getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TbTjStadItemsStatus fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_ID")			
	public TbTjItems getFkByItemId() {
		return fkByItemId;
	}

	public void setFkByItemId(TbTjItems fkByItemId) {
		this.fkByItemId = fkByItemId;
	}	
			
	@Column(name = "MINVAL")	
	public BigDecimal getMinval() {
		return minval;
	}

	public void setMinval(BigDecimal minval) {
		this.minval = minval;
	}	
			
	@Column(name = "MAXVAL")	
	public BigDecimal getMaxval() {
		return maxval;
	}

	public void setMaxval(BigDecimal maxval) {
		this.maxval = maxval;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Column(name = "JDGPTN")
	public Integer getJdgptn() {
		return jdgptn;
	}

	public void setJdgptn(Integer jdgptn) {
		this.jdgptn = jdgptn;
	}

	@Column(name = "SEX")
	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	@ManyToOne
	@JoinColumn(name = "MSRUNT_ID")
	public TsSimpleCode getFkByMsruntId() {
		return fkByMsruntId;
	}

	public void setFkByMsruntId(TsSimpleCode fkByMsruntId) {
		this.fkByMsruntId = fkByMsruntId;
	}

}