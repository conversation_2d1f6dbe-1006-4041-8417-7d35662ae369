package com.chis.modules.heth.comm.utils;

import com.chis.modules.heth.comm.entity.TdZwHethChkSmaryComm;
import com.chis.modules.heth.comm.entity.TdZwHethJcSubComm;
import com.chis.modules.system.entity.TsSimpleCode;

import java.util.List;

/**
 *  <p>类描述：职业健康检查汇总工具类</p>
 * @ClassAuthor hsj 2021/10/26 9:13
 */
public class ZwChkSmaryCommUtils {
	/**
	 *  <p>方法描述：关联实体初始化</p>
	 * @MethodAuthor hsj
	 */
	public static void initEntity(TdZwHethChkSmaryComm chkSmary) {
		List<TdZwHethJcSubComm> jcSubList = chkSmary.getJcSubList();
		if (null!=jcSubList && jcSubList.size()>0) {
			for (TdZwHethJcSubComm sub : jcSubList) {
				if (null==sub.getFkByBadrsnId()) {
					sub.setFkByBadrsnId(new TsSimpleCode());
				}
				TsSimpleCode fkByThickTypeId = sub.getFkByThickTypeId();
				if (null==fkByThickTypeId) {
					sub.setFkByThickTypeId(new TsSimpleCode());
				}else {
					sub.setThickTypeId(sub.getFkByThickTypeId().getRid());
				}
			}
		}
	}
	/**
	 *  <p>方法描述：职业性有害因素情况清空</p>
	 * @MethodAuthor hsj
	 */
	public  static void clearJcSubCommEntity(TdZwHethChkSmaryComm chkSmary) {
		List<TdZwHethJcSubComm> jcSubList = chkSmary.getJcSubList();
		if (null!=jcSubList && jcSubList.size()>0) {
			for (TdZwHethJcSubComm sub : jcSubList) {
				TsSimpleCode fkByBadrsnId = sub.getFkByBadrsnId();
				if (null==fkByBadrsnId||null==fkByBadrsnId.getRid()) {
					sub.setFkByBadrsnId(null);
				}
				TsSimpleCode fkByThickTypeId = sub.getFkByThickTypeId();
				if (null==fkByThickTypeId||null==fkByThickTypeId.getRid()) {
					sub.setFkByThickTypeId(null);
				}
			}
		}
	}
}
