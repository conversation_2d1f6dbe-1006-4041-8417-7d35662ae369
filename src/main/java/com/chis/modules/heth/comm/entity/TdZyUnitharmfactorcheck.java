package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-6
 */
@Entity
@Table(name = "TD_ZY_UNITHARMFACTORCHECK")
@SequenceGenerator(name = "TdZyUnitharmfactorcheck", sequenceName = "TD_ZY_UNITHARMFACTORCHECK_SEQ", allocationSize = 1)
public class TdZyUnitharmfactorcheck implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZyUnitbasicinfo fkByMainId;
	private Integer ifat;
	private String testUnitNames;
	private String testReportNos;
	private Integer ifatDust;
	private Integer ifatDustAllChecknum;
	private Integer ifatDustAllExcessnum;
	private Integer ifatDustChecknum;
	private Integer ifatDustExcessnum;
	private Integer ifatDustCoalChecknum;
	private Integer ifatDustCoalExcessnum;
	private Integer ifatDustAsbestosChecknum;
	private Integer ifatDustAsbestosExcessnum;
	private Integer ifatChemistry;
	private Integer ifatChemistryAllChecknum;
	private Integer ifatChemistryAllExcessnum;
	private Integer ifatChemistryChecknum;
	private Integer ifatChemistryExcessnum;
	private Integer ifatChemistryBenzeneChecknu;
	private Integer ifatChemistryBenzeneExcessn;
	private Integer ifatPhysics;
	private Integer ifatPhysicsAllChecknum;
	private Integer ifatPhysicsAllExcessnum;
	private Integer ifatPhysicsChecknum;
	private Integer ifatPhysicsExcessnum;
	private Integer ifatRadioactivity;
	private Integer ifatRadioactivityChecknum;
	private Integer ifatRadioactivityExcessnum;
	private Integer ifatBiologyother;
	private Integer ifatBiologyotherChecknum;
	private Integer ifatBiologyotherExcessnum;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZyUnitharmfactorcheck() {
	}

	public TdZyUnitharmfactorcheck(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZyUnitharmfactorcheck")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZyUnitbasicinfo getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZyUnitbasicinfo fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "IFAT")	
	public Integer getIfat() {
		return ifat;
	}

	public void setIfat(Integer ifat) {
		this.ifat = ifat;
	}	
			
	@Column(name = "TEST_UNIT_NAMES")	
	public String getTestUnitNames() {
		return testUnitNames;
	}

	public void setTestUnitNames(String testUnitNames) {
		this.testUnitNames = testUnitNames;
	}	
			
	@Column(name = "TEST_REPORT_NOS")	
	public String getTestReportNos() {
		return testReportNos;
	}

	public void setTestReportNos(String testReportNos) {
		this.testReportNos = testReportNos;
	}	
			
	@Column(name = "IFAT_DUST")	
	public Integer getIfatDust() {
		return ifatDust;
	}

	public void setIfatDust(Integer ifatDust) {
		this.ifatDust = ifatDust;
	}	
			
	@Column(name = "IFAT_DUST_ALL_CHECKNUM")	
	public Integer getIfatDustAllChecknum() {
		return ifatDustAllChecknum;
	}

	public void setIfatDustAllChecknum(Integer ifatDustAllChecknum) {
		this.ifatDustAllChecknum = ifatDustAllChecknum;
	}	
			
	@Column(name = "IFAT_DUST_ALL_EXCESSNUM")	
	public Integer getIfatDustAllExcessnum() {
		return ifatDustAllExcessnum;
	}

	public void setIfatDustAllExcessnum(Integer ifatDustAllExcessnum) {
		this.ifatDustAllExcessnum = ifatDustAllExcessnum;
	}	
			
	@Column(name = "IFAT_DUST_CHECKNUM")	
	public Integer getIfatDustChecknum() {
		return ifatDustChecknum;
	}

	public void setIfatDustChecknum(Integer ifatDustChecknum) {
		this.ifatDustChecknum = ifatDustChecknum;
	}	
			
	@Column(name = "IFAT_DUST_EXCESSNUM")	
	public Integer getIfatDustExcessnum() {
		return ifatDustExcessnum;
	}

	public void setIfatDustExcessnum(Integer ifatDustExcessnum) {
		this.ifatDustExcessnum = ifatDustExcessnum;
	}	
			
	@Column(name = "IFAT_DUST_COAL_CHECKNUM")	
	public Integer getIfatDustCoalChecknum() {
		return ifatDustCoalChecknum;
	}

	public void setIfatDustCoalChecknum(Integer ifatDustCoalChecknum) {
		this.ifatDustCoalChecknum = ifatDustCoalChecknum;
	}	
			
	@Column(name = "IFAT_DUST_COAL_EXCESSNUM")	
	public Integer getIfatDustCoalExcessnum() {
		return ifatDustCoalExcessnum;
	}

	public void setIfatDustCoalExcessnum(Integer ifatDustCoalExcessnum) {
		this.ifatDustCoalExcessnum = ifatDustCoalExcessnum;
	}	
			
	@Column(name = "IFAT_DUST_ASBESTOS_CHECKNUM")	
	public Integer getIfatDustAsbestosChecknum() {
		return ifatDustAsbestosChecknum;
	}

	public void setIfatDustAsbestosChecknum(Integer ifatDustAsbestosChecknum) {
		this.ifatDustAsbestosChecknum = ifatDustAsbestosChecknum;
	}	
			
	@Column(name = "IFAT_DUST_ASBESTOS_EXCESSNUM")	
	public Integer getIfatDustAsbestosExcessnum() {
		return ifatDustAsbestosExcessnum;
	}

	public void setIfatDustAsbestosExcessnum(Integer ifatDustAsbestosExcessnum) {
		this.ifatDustAsbestosExcessnum = ifatDustAsbestosExcessnum;
	}	
			
	@Column(name = "IFAT_CHEMISTRY")	
	public Integer getIfatChemistry() {
		return ifatChemistry;
	}

	public void setIfatChemistry(Integer ifatChemistry) {
		this.ifatChemistry = ifatChemistry;
	}	
			
	@Column(name = "IFAT_CHEMISTRY_ALL_CHECKNUM")	
	public Integer getIfatChemistryAllChecknum() {
		return ifatChemistryAllChecknum;
	}

	public void setIfatChemistryAllChecknum(Integer ifatChemistryAllChecknum) {
		this.ifatChemistryAllChecknum = ifatChemistryAllChecknum;
	}	
			
	@Column(name = "IFAT_CHEMISTRY_ALL_EXCESSNUM")	
	public Integer getIfatChemistryAllExcessnum() {
		return ifatChemistryAllExcessnum;
	}

	public void setIfatChemistryAllExcessnum(Integer ifatChemistryAllExcessnum) {
		this.ifatChemistryAllExcessnum = ifatChemistryAllExcessnum;
	}	
			
	@Column(name = "IFAT_CHEMISTRY_CHECKNUM")	
	public Integer getIfatChemistryChecknum() {
		return ifatChemistryChecknum;
	}

	public void setIfatChemistryChecknum(Integer ifatChemistryChecknum) {
		this.ifatChemistryChecknum = ifatChemistryChecknum;
	}	
			
	@Column(name = "IFAT_CHEMISTRY_EXCESSNUM")	
	public Integer getIfatChemistryExcessnum() {
		return ifatChemistryExcessnum;
	}

	public void setIfatChemistryExcessnum(Integer ifatChemistryExcessnum) {
		this.ifatChemistryExcessnum = ifatChemistryExcessnum;
	}	
			
	@Column(name = "IFAT_CHEMISTRY_BENZENE_CHECKNU")	
	public Integer getIfatChemistryBenzeneChecknu() {
		return ifatChemistryBenzeneChecknu;
	}

	public void setIfatChemistryBenzeneChecknu(Integer ifatChemistryBenzeneChecknu) {
		this.ifatChemistryBenzeneChecknu = ifatChemistryBenzeneChecknu;
	}	
			
	@Column(name = "IFAT_CHEMISTRY_BENZENE_EXCESSN")	
	public Integer getIfatChemistryBenzeneExcessn() {
		return ifatChemistryBenzeneExcessn;
	}

	public void setIfatChemistryBenzeneExcessn(Integer ifatChemistryBenzeneExcessn) {
		this.ifatChemistryBenzeneExcessn = ifatChemistryBenzeneExcessn;
	}	
			
	@Column(name = "IFAT_PHYSICS")	
	public Integer getIfatPhysics() {
		return ifatPhysics;
	}

	public void setIfatPhysics(Integer ifatPhysics) {
		this.ifatPhysics = ifatPhysics;
	}	
			
	@Column(name = "IFAT_PHYSICS_ALL_CHECKNUM")	
	public Integer getIfatPhysicsAllChecknum() {
		return ifatPhysicsAllChecknum;
	}

	public void setIfatPhysicsAllChecknum(Integer ifatPhysicsAllChecknum) {
		this.ifatPhysicsAllChecknum = ifatPhysicsAllChecknum;
	}	
			
	@Column(name = "IFAT_PHYSICS_ALL_EXCESSNUM")	
	public Integer getIfatPhysicsAllExcessnum() {
		return ifatPhysicsAllExcessnum;
	}

	public void setIfatPhysicsAllExcessnum(Integer ifatPhysicsAllExcessnum) {
		this.ifatPhysicsAllExcessnum = ifatPhysicsAllExcessnum;
	}	
			
	@Column(name = "IFAT_PHYSICS_CHECKNUM")	
	public Integer getIfatPhysicsChecknum() {
		return ifatPhysicsChecknum;
	}

	public void setIfatPhysicsChecknum(Integer ifatPhysicsChecknum) {
		this.ifatPhysicsChecknum = ifatPhysicsChecknum;
	}	
			
	@Column(name = "IFAT_PHYSICS_EXCESSNUM")	
	public Integer getIfatPhysicsExcessnum() {
		return ifatPhysicsExcessnum;
	}

	public void setIfatPhysicsExcessnum(Integer ifatPhysicsExcessnum) {
		this.ifatPhysicsExcessnum = ifatPhysicsExcessnum;
	}	
			
	@Column(name = "IFAT_RADIOACTIVITY")	
	public Integer getIfatRadioactivity() {
		return ifatRadioactivity;
	}

	public void setIfatRadioactivity(Integer ifatRadioactivity) {
		this.ifatRadioactivity = ifatRadioactivity;
	}	
			
	@Column(name = "IFAT_RADIOACTIVITY_CHECKNUM")	
	public Integer getIfatRadioactivityChecknum() {
		return ifatRadioactivityChecknum;
	}

	public void setIfatRadioactivityChecknum(Integer ifatRadioactivityChecknum) {
		this.ifatRadioactivityChecknum = ifatRadioactivityChecknum;
	}	
			
	@Column(name = "IFAT_RADIOACTIVITY_EXCESSNUM")	
	public Integer getIfatRadioactivityExcessnum() {
		return ifatRadioactivityExcessnum;
	}

	public void setIfatRadioactivityExcessnum(Integer ifatRadioactivityExcessnum) {
		this.ifatRadioactivityExcessnum = ifatRadioactivityExcessnum;
	}	
			
	@Column(name = "IFAT_BIOLOGYOTHER")	
	public Integer getIfatBiologyother() {
		return ifatBiologyother;
	}

	public void setIfatBiologyother(Integer ifatBiologyother) {
		this.ifatBiologyother = ifatBiologyother;
	}	
			
	@Column(name = "IFAT_BIOLOGYOTHER_CHECKNUM")	
	public Integer getIfatBiologyotherChecknum() {
		return ifatBiologyotherChecknum;
	}

	public void setIfatBiologyotherChecknum(Integer ifatBiologyotherChecknum) {
		this.ifatBiologyotherChecknum = ifatBiologyotherChecknum;
	}	
			
	@Column(name = "IFAT_BIOLOGYOTHER_EXCESSNUM")	
	public Integer getIfatBiologyotherExcessnum() {
		return ifatBiologyotherExcessnum;
	}

	public void setIfatBiologyotherExcessnum(Integer ifatBiologyotherExcessnum) {
		this.ifatBiologyotherExcessnum = ifatBiologyotherExcessnum;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}