package com.chis.modules.heth.comm.utils;

import com.chis.modules.heth.comm.entity.TdZwBgkFlow;
import com.chis.modules.heth.comm.entity.TdZwBgkLastSta;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.utils.HolidayUtil;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 审核流程相关标记处理工具类
 */
public class BgkMarkUtils {

    /**
     * 审核等级 2
     */
    private final static Integer CHECK_LEVEL_2 = 2;
    /**
     * 审核等级 3
     */
    private final static Integer CHECK_LEVEL_3 = 3;


    /**
     * @param zoneType   地区级别
     * @param checkState 1:通过 2：退回
     * @description: 处理最新状态表状态值 直接退回到填报情况
     * <AUTHOR>
     */
    public static TdZwBgkLastSta dealLastStaMark(Integer zoneType, Boolean checkState, TdZwBgkLastSta bgkLastSta,String checkRst) {
        if (zoneType > 3) {
            //区县级
            bgkLastSta.setState(checkState ? 3 : 2);
            //接收日期
            if (checkState) {
                bgkLastSta.setCityRcvDate(new Date());
            }
            bgkLastSta.setCountyChkPsn(Global.getUser().getUsername());
            bgkLastSta.setCountyRst(checkState ? 1 : 2);
            bgkLastSta.setCountAuditAdv(checkRst);
        } else if (zoneType == 3) {
            //市级
            bgkLastSta.setState(checkState ? 5 : 4);
            if (checkState) {
                bgkLastSta.setProRcvDate(new Date());
            }
            bgkLastSta.setCityChkPsn(Global.getUser().getUsername());
            bgkLastSta.setCityRst(checkState ? 1 : 2);
            bgkLastSta.setCityAuditAdv(checkRst);
        } else {
            //省级
            bgkLastSta.setState(checkState ? 7 : 6);
            bgkLastSta.setProChkPsn(Global.getUser().getUsername());
            bgkLastSta.setProRst(checkState ? 1 : 2);
            bgkLastSta.setProAuditAdv(checkRst);
        }
        return bgkLastSta;
    }

    /**
     * @param checkLevel         审核级别
     * @param ifCityOrProvDirect checkLevel=3 是市直属  checkLevel=2 是省直属
     * @param zoneType           地区级别
     * @param checkState         1:通过 2：退回
     * @description:
     * <AUTHOR>
     */
    public static void dealBgkFlowMark(Integer checkLevel, Boolean ifCityOrProvDirect, Integer zoneType, Boolean checkState, TdZwBgkFlow bgkFlow) {
        if (CHECK_LEVEL_3.equals(checkLevel)) {
            if (zoneType > 3) {
                bgkFlow.setOperFlag(checkState ? 31 : 11);
                bgkFlow.setPreviousOperFlag(21);
            } else if (zoneType == 3) {
                if (ifCityOrProvDirect) {
                    bgkFlow.setOperFlag(checkState ? 41 : 13);
                    bgkFlow.setPreviousOperFlag(33);
                } else {
                    bgkFlow.setOperFlag(checkState ? 41 : 22);
                    bgkFlow.setPreviousOperFlag(31);
                }
            } else if (zoneType == 2) {
                //省级操作终审结果
                bgkFlow.setOperFlag(checkState ? 42 : 32);
                bgkFlow.setPreviousOperFlag(41);
            }
        } else if (CHECK_LEVEL_2.equals(checkLevel)) {
            //省直属或者市级省级单位
            if (zoneType <= 3) {
                bgkFlow.setOperFlag(checkState ? 42 : 32);
                if (ifCityOrProvDirect) {
                    bgkFlow.setPreviousOperFlag(44);
                } else {
                    bgkFlow.setPreviousOperFlag(43);
                }
            } else {
                bgkFlow.setOperFlag(checkState ? 43 : 11);
                bgkFlow.setPreviousOperFlag(21);
            }
        }
    }


    /**
     * <p>方法描述：计算超期</p>
     *
     * @MethodAuthor rcj, 2020年10月16日, calLimitTime
     */
    public static int calLimitTime(Date sDate, String limitTime) {
        if (null == sDate) {
            return -1;
        }
        //剩余天数
        int day = HolidayUtil.calRemainingDate(sDate, new Date(), limitTime);
        if (day == 0) {
            return -1;
        }
        return day;
    }

    /**
     *  <p>方法描述：初始化历次审核意见</p>
     * @MethodAuthor hsj
     */
    public static void initHistoryList(List<Object[]> historyList, Integer checkLevel) {
        if(!CollectionUtils.isEmpty(historyList)){
            List<Object[]> checkOutList = new ArrayList<>();
            for(Object[] objArr:historyList){
                int opegFlag = null == objArr[7] ? 0 : Integer.parseInt(objArr[7].toString()) ;
                String type = null;
                //审核级别为3
                if(CHECK_LEVEL_3.equals(checkLevel)){
                    Integer ifCityDirect =  null == objArr[5] ? null : Integer.parseInt(objArr[5].toString()) ;
                    //根据是否市直属判断审核类型
                    if(Integer.valueOf(1).equals(ifCityDirect)){
                        switch (opegFlag){
                            case 41:
                                type = "市级审核通过";
                                break;
                            case 13:
                                type = "市级审核退回";
                                break;
                            case 42:
                                type = "省级审核通过";
                                break;
                            case 32:
                                type = "省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }else{
                        switch (opegFlag) {
                            case 31:
                                type = "区县级审核通过";
                                break;
                            case 11:
                                type = "区县级审核退回";
                                break;
                            case 41:
                                type = "市级审核通过";
                                break;
                            case 22:
                                type = "市级审核退回";
                                break;
                            case 42:
                                type = "省级审核通过";
                                break;
                            case 32:
                                type = "省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }
                }
                //审核级别为2
                if(CHECK_LEVEL_2.equals(checkLevel)){
                    Integer ifProvDirect =  null == objArr[6] ? null : Integer.parseInt(objArr[6].toString()) ;
                    //根据是否省直属判断审核类型
                    if(Integer.valueOf(1).equals(ifProvDirect)){
                        switch (opegFlag){
                            case 42:
                                type = "省级审核通过";
                                break;
                            case 32:
                                type = "省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }else{
                        switch (opegFlag) {
                            case 43:
                                type = "区县级审核通过";
                                break;
                            case 11:
                                type = "区县级审核退回";
                                break;
                            case 42:
                                type = "省级审核通过";
                                break;
                            case 32:
                                type = "省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }
                }
                objArr[4] = type;
                if(type != null){
                    checkOutList.add(objArr);
                }
            }
            historyList = checkOutList;
        }
    }
}
