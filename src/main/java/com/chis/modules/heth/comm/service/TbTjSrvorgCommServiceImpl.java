package com.chis.modules.heth.comm.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.system.service.AbstractTemplate;

/**
 * @Description : 资质服务机构注册信息-业务层
 * @ClassAuthor : anjing
 * @Date : 2019/5/17 14:47
 **/
@Service
@Transactional(readOnly = true)
public class TbTjSrvorgCommServiceImpl extends AbstractTemplate {

    /**
     * @Description : 根据机构/单位Id查找资质服务机构注册信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/17 14:50
     **/
    public List<TbTjSrvorg> selectSrvorgListByOrgId(Integer orgId) {
        if (null != orgId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TbTjSrvorg t ");
            // 通用标记 0 停用 1 启用
            sb.append(" where t.stopTag = 1");
            sb.append(" and t.tsUnit.rid = ").append(orgId);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    /**
    * @Description : 根据机构/单位Id、状态查询职业健康检查机构基本信息
    * @MethodAuthor: anjing
    * @Date : 2020/4/24 9:08
    **/
    public List<Object[]> selectTjorginfoListByOrgIdAndState(Integer orgId, Integer state) {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT T.RID, T.ORG_ID, T.ORG_NAME, T.ORG_ADDR, T.ORG_FZ, T.ORG_FZZW, T.LINK_MAN, ");
        sb.append(" T.LINK_MB, T.LINK_TEL, T.FAX, T.ZIPCODE, T.EMAIL, T.CERT_NO, T.FIRST_GETDAY, ");
        sb.append(" T.VALID_DATE, T.STATE, T.CANCEL_STATE, T.CANCEL_DATE ");
        sb.append(" FROM TD_ZW_TJORGINFO T WHERE 1=1 ");
        sb.append(" AND T.ORG_ID = '").append(orgId).append("' ");
        sb.append(" AND T.STATE = '").append(state).append("' ");
        return em.createNativeQuery(sb.toString()).getResultList();
    }
}
