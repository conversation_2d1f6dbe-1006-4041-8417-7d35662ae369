package com.chis.modules.heth.comm.plugin;

import com.chis.modules.system.entity.TsRpt;
import com.chis.modules.system.enumn.SystemType;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @createDate 2015-03-04.
 */
public class ReportTemplatePluginObj {
    public static Set<TsRpt> dataSet;


    static {
        dataSet = new HashSet<TsRpt>();

        TsRpt rpt = new TsRpt();
        rpt = new TsRpt();
        rpt.setCreateDate(new Date());
        rpt.setCreateManid(1);
        rpt.setRmk("超范围服务汇报文书");
        rpt.setRptCod("HETH_6001");
        rpt.setRptnam("超范围服务汇报文书");
        rpt.setRptver(1);
        rpt.setSystemType(SystemType.ZYWS_TY);
        rpt.setRptpath("/rpt/heth/HETH_6001.fr3");
        dataSet.add(rpt);
        
        rpt = new TsRpt();
        rpt.setCreateDate(new Date());
        rpt.setCreateManid(1);
        rpt.setRmk("疑似职业病报告卡");
        rpt.setRptCod("HETH_2031");
        rpt.setRptnam("疑似职业病报告卡");
        rpt.setRptver(1);
        rpt.setSystemType(SystemType.ZYWS_TY);
        rpt.setRptpath("/rpt/heth/HETH_2031.fr3");
        dataSet.add(rpt);

        //职业性有害因素监测卡
        rpt = new TsRpt();
        rpt.setCreateDate(new Date());
        rpt.setCreateManid(1);
        rpt.setRmk("职业性有害因素监测卡");
        rpt.setRptCod("HETH_2037");
        rpt.setRptnam("职业性有害因素监测卡");
        rpt.setRptver(1);
        rpt.setSystemType(SystemType.ZYWS_TY);
        rpt.setRptpath("/rpt/heth/HETH_2037.fr3");
        dataSet.add(rpt);

        //职业病鉴定报告卡
        rpt = new TsRpt();
        rpt.setCreateDate(new Date());
        rpt.setCreateManid(1);
        rpt.setRmk("职业病鉴定报告卡");
        rpt.setRptCod("HETH_2038");
        rpt.setRptnam("职业病鉴定报告卡");
        rpt.setRptver(1);
        rpt.setSystemType(SystemType.ZYWS_TY);
        rpt.setRptpath("/rpt/heth/HETH_2038.fr3");
        dataSet.add(rpt);

        rpt = new TsRpt();
        rpt.setCreateDate(new Date());
        rpt.setCreateManid(1);
        rpt.setRmk("放射卫生技术服务信息报送卡");
        rpt.setRptCod("HETH_RPT_1001");
        rpt.setRptnam("放射卫生技术服务信息报送卡");
        rpt.setRptver(1);
        rpt.setSystemType(SystemType.ZYWS_TY);
        rpt.setRptpath("/rptWord/heth/HETH_RPT_1001.docx");
        dataSet.add(rpt);

        rpt = new TsRpt();
        rpt.setCreateDate(new Date());
        rpt.setCreateManid(1);
        rpt.setRmk("职业卫生技术服务信息报送卡");
        rpt.setRptCod("HETH_RPT_1002");
        rpt.setRptnam("职业卫生技术服务信息报送卡");
        rpt.setRptver(1);
        rpt.setSystemType(SystemType.ZYWS_TY);
        rpt.setRptpath("/rptWord/heth/HETH_RPT_1002.docx");
        dataSet.add(rpt);
    }
}
