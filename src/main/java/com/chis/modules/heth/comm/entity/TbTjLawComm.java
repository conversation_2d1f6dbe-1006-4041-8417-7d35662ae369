package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by wlj on 2015/5/26.
 */
@Entity
@Table(name = "TB_TJ_LAW")
@SequenceGenerator(name = "TbTjLawSeq", sequenceName = "TB_TJ_LAW_SEQ", allocationSize = 1)
@NamedQueries({
        @NamedQuery(name = "TbTjLawComm.findByRid", query = "SELECT t FROM TbTjLawComm t WHERE t.rid=:rid ")
})
public class TbTjLawComm implements Serializable {

    private static final long serialVersionUID = 4510454398249012303L;
    private Integer rid;
    private String lawCode;
    private String lawName;
    private String briefExplain;
    private Date decreeDate;
    private String decreeOrgan;
    private String fileName;
    private String filePath;
    private Short stateMark;
    private TsSimpleCode tsSimpleCodeByLawTypeId;

    private Integer xh;

    public TbTjLawComm() {
    }

    public TbTjLawComm(Integer rid) {
        this.rid = rid;
    }

    @Id
    @GeneratedValue(generator = "TbTjLawSeq", strategy = GenerationType.SEQUENCE)
    @Column(name = "RID", unique = true)
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Column(name = "LAW_CODE", length = 20)
    public String getLawCode() {
        return lawCode;
    }

    public void setLawCode(String lawCode) {
        this.lawCode = lawCode;
    }

    @Column(name = "LAW_NAME", length = 100)
    public String getLawName() {
        return lawName;
    }

    public void setLawName(String lawName) {
        this.lawName = lawName;
    }

    @Column(name = "BRIEF_EXPLAIN", length = 1000)
    public String getBriefExplain() {
        return briefExplain;
    }

    public void setBriefExplain(String briefExplain) {
        this.briefExplain = briefExplain;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "DECREE_DATE")
    public Date getDecreeDate() {
        return decreeDate;
    }

    public void setDecreeDate(Date decreeDate) {
        this.decreeDate = decreeDate;
    }

    @Column(name = "DECREE_ORGAN", length = 200)
    public String getDecreeOrgan() {
        return decreeOrgan;
    }

    public void setDecreeOrgan(String decreeOrgan) {
        this.decreeOrgan = decreeOrgan;
    }

    @Column(name = "FILE_NAME", length = 200)
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }


    @Column(name = "FILE_PATH", length = 200)
    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Column(name = "STATE_MARK", length = 1)
    public Short getStateMark() {
        return stateMark;
    }

    public void setStateMark(Short stateMark) {
        this.stateMark = stateMark;
    }

    @ManyToOne
    @JoinColumn(name = "LAW_TYPE_ID")
    public TsSimpleCode getTsSimpleCodeByLawTypeId() {
        return tsSimpleCodeByLawTypeId;
    }

    public void setTsSimpleCodeByLawTypeId(TsSimpleCode tsSimpleCodeByLawTypeId) {
        this.tsSimpleCodeByLawTypeId = tsSimpleCodeByLawTypeId;
    }

    @Column(name = "XH")
    public Integer getXh() {
        return xh;
    }

    public void setXh(Integer xh) {
        this.xh = xh;
    }
}
