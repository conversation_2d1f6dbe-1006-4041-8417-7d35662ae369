package com.chis.modules.heth.comm.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * @Description : 问诊（非）放射职业史信息表（数据录入）
 * @ClassAuthor: anjing
 * @Date : 2019/5/14 10:08
 **/
@Entity
@Table(name = "TD_TJ_EMHISTORY_CLT")
@SequenceGenerator(name = "TdTjEmhistoryClt_Seq", sequenceName = "TD_TJ_EMHISTORY_CLT_SEQ", allocationSize = 1)
public class TdTjEmhistoryClt implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /**主键Id*/
    private Integer rid;
    /**关联：体检主表-数据录入（体检主表Id）*/
    private TdTjBhkClt fkByBhkId;
    /**类型：1 放射 2 非放射*/
    private Integer hisType;
    /**序号*/
    private Integer num;
    /**起止日期*/
    private String stastpDate;
    /**工作单位名称*/
    private String unitName;
    /**部门车间*/
    private String department;
    /**工种*/
    private String workType;
    /**接触有害因素*/
    private String prfraysrt;
    /**(非放射)防护措施*/
    private String defendStep;
    /**(放射)职业史每日工作时数或工作量*/
    private String prfwrklod;
    /**(放射)职业史累积受照剂量*/
    private String prfshnvlu;
    /**(放射)职业史过量照射史*/
    private String prfexcshn;
    /**(放射)职业照射种类*/
    private String prfraysrt2;
    /**(放射)放射线种类*/
    private String fsszl;
    /**创建日期*/
    private Date createDate;
    /**创建人*/
    private Integer createManid;
    /**修改日期*/
    private Date modifyDate;
    /**修改人*/
    private Integer modifyManid;
    /**开始日期*/
    private Date startDate;
    /**结束日期*/
    private Date stopDate;

    public TdTjEmhistoryClt() {
    }

    public TdTjEmhistoryClt(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjEmhistoryClt_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BHK_ID" )
    public TdTjBhkClt getFkByBhkId() {
        return fkByBhkId;
    }

    public void setFkByBhkId(TdTjBhkClt fkByBhkId) {
        this.fkByBhkId = fkByBhkId;
    }

    @Column(name = "HIS_TYPE" )
    public Integer getHisType() {
        return hisType;
    }

    public void setHisType(Integer hisType) {
        this.hisType = hisType;
    }

    @Column(name = "NUM")
    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    @Column(name = "STASTP_DATE" , length = 100)
    public String getStastpDate() {
        return stastpDate;
    }

    public void setStastpDate(String stastpDate) {
        this.stastpDate = stastpDate;
    }

    @Column(name = "UNIT_NAME" , length = 50)
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Column(name = "DEPARTMENT", length = 100)
    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    @Column(name = "WORK_TYPE", length = 50)
    public String getWorkType() {
        return workType;
    }

    public void setWorkType(String workType) {
        this.workType = workType;
    }

    @Column(name = "PRFRAYSRT", length = 100)
    public String getPrfraysrt() {
        return prfraysrt;
    }

    public void setPrfraysrt(String prfraysrt) {
        this.prfraysrt = prfraysrt;
    }

    @Column(name = "DEFEND_STEP", length = 50)
    public String getDefendStep() {
        return defendStep;
    }

    public void setDefendStep(String defendStep) {
        this.defendStep = defendStep;
    }

    @Column(name = "PRFWRKLOD", length = 100)
    public String getPrfwrklod() {
        return prfwrklod;
    }

    public void setPrfwrklod(String prfwrklod) {
        this.prfwrklod = prfwrklod;
    }

    @Column(name = "PRFSHNVLU", length = 100)
    public String getPrfshnvlu() {
        return prfshnvlu;
    }

    public void setPrfshnvlu(String prfshnvlu) {
        this.prfshnvlu = prfshnvlu;
    }

    @Column(name = "PRFEXCSHN", length = 100)
    public String getPrfexcshn() {
        return prfexcshn;
    }

    public void setPrfexcshn(String prfexcshn) {
        this.prfexcshn = prfexcshn;
    }

    @Column(name = "PRFRAYSRT2", length = 500)
    public String getPrfraysrt2() {
        return prfraysrt2;
    }

    public void setPrfraysrt2(String prfraysrt2) {
        this.prfraysrt2 = prfraysrt2;
    }

    @Column(name = "FSSZL", length = 200)
    public String getFsszl() {
        return fsszl;
    }

    public void setFsszl(String fsszl) {
        this.fsszl = fsszl;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }
    @Column(name = "START_DATE")
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }
    @Column(name = "STOP_DATE")
    public Date getStopDate() {
        return stopDate;
    }

    public void setStopDate(Date stopDate) {
        this.stopDate = stopDate;
    }
}
