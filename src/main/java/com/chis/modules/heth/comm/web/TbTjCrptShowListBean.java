package com.chis.modules.heth.comm.web;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.entity.TdZyUnitbasicinfo;
import com.chis.modules.heth.comm.entity.TdZyUnithealthcustody;
import com.chis.modules.heth.comm.json.CrptShowDangerObj;
import com.chis.modules.heth.comm.service.TdZyUnitbasicinfoService;
import com.github.abel533.echarts.axis.CategoryAxis;
import com.github.abel533.echarts.axis.ValueAxis;
import com.github.abel533.echarts.code.*;
import com.github.abel533.echarts.json.GsonOption;
import com.github.abel533.echarts.series.Bar;
import com.github.abel533.echarts.series.Series;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.DefaultLazyDataModel;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.springframework.util.CollectionUtils;

/**
 * <p>类描述：企业综合展示</p>
 * @ClassAuthor qrr,2020年12月9日,TbTjCrptShowListBean
 * */
@ManagedBean(name="tbTjCrptShowListBean")
@ViewScoped
public class TbTjCrptShowListBean extends FacesEditBean{
	private static final long serialVersionUID = 1L;
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private TdZyUnitbasicinfoService basicinfoService = SpringContextHolder.getBean(TdZyUnitbasicinfoService.class);
	/**查询条件：用人单位地区*/
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
    /**查询条件：用人单位名称*/
    private String searchUnitName;
    private String searchCreditCode;
    /**查询条件-经济类型*/
	private String selectEconomyNames;
	private String selectEconomyIds;
	/**查询条件-企业规模*/
	private List<TsSimpleCode> crptSizeList;
	private String selectCrptSizes;
	private String selectCrptSizeIds;
	/**查询条件-行业类别*/
	private String selectIndusTypeNames;
	private String selectIndusTypeIds;
	
	private String simpleCodeType;
	/**首个数据来源的rid*/
    private Integer crptId;
    /**另一个数据来源的rid*/
    private Integer otherId;
    /**默认数据类型*/
    private String dataSource;

    /**详情企业数据来源*/
    private TdZyUnitbasicinfo tdZyUnitbasicinfo;
	/**详情体检数据来源*/
	private TbTjCrpt tbTjCrpt;

    private Integer unitBasicId;


	/**危害情况信息集合*/
	private List<String> badConditionList;
	/**危害因素列表*/
	private List<Object[]> msgList;
	/**查询类型*/
	private Integer analyType;
	/**限制月份*/
	private Integer limitMonth;

	private DefaultLazyDataModel onLineReportModel;


	/** 年份列表 */
	private List<Integer> yearList;
	/** 选中年份 */
	private Integer year;
	/** 企业综合展示 详情 危害因素分析 表格数据列表及左图数据 */
	private List<CrptShowDangerObj> dangerTableList;
	/*企业综合展示 详情 危害因素分析 右图数据 */
	private List<CrptShowDangerObj> dangerRightList;
	/** 危害因素列表 */
	private List<TsSimpleCode> dangerSimpleList;
	/** 大类危害因素对应的包含自己的所有子类 对应的大类及子类危害因素集合 比如粉尘类 包含粉尘类 矽尘 煤尘 石棉粉等 */
	private Map<Integer,List<Integer>> fatherChildListMap = new HashMap<>();
	/** 大类危害因素 */
	private Map<Integer,TsSimpleCode> fatherMap = new HashMap<>();
	private Map<Integer,TsSimpleCode> simpleCodeMap = new HashMap<>();
	/** 大类危害因素集合 */
	private List<TsSimpleCode> fatherSimpleList;
	/** 小类危害因素集合 */
	private List<TsSimpleCode> childSimpleList;
	/**
	 * extend3 对应的危害因素rid
	 * 标识危害因素类型：1：粉尘，2：化学物质，3：物理因素，4：放射性因素，5：生物因素，6：其他
	 * 11：矽尘，12：煤尘，13：石棉粉尘，14：铅，15：苯，16：噪声
	 * key rid value like 1,2,3,4......
	 * */
	private Map<Integer,Integer> extendMap = new HashMap<>();
	/** key 1,2,3,4..... value rid */
	private Map<Integer,List<Integer>> extendRidMap = new HashMap<>();
	/** 危害因素rid对应数据对象 */
	private Map<Integer,CrptShowDangerObj> dangerChatMap = new HashMap<>();
	private String lineLeftJson;
	private String lineRightJson;
	/** key 危害因素codeNo value危害因素码表rid集合 */
	private Map<String, List<Integer>> codeNoRidMap = new HashMap<>();
	/** key扩展字段3 value codeNo */
	private Map<Integer,String> extendCodeNoMap = new HashMap<>();

	public TbTjCrptShowListBean(){
		this.ifSQL = true;
		limitMonth = Integer.valueOf(PropertyUtils.getValueWithoutException("showLimitMonth")==null?"":PropertyUtils.getValueWithoutException("showLimitMonth"));
		this.init();
		this.searchAction();
	}
	/** 危害因素类 初始化 */
	private void initDanger(){
		year = DateUtils.getYearInt();
		yearList = new ArrayList<>();
		for(int k = 0; k < 10; k++){
			yearList.add(year-k);
		}
		dangerSimpleList = this.commService.findSimpleCodesByTypeId("5007");
		List<TsSimpleCode> tmpSimpleCodeList = this.commService.findallSimpleCodesByTypeId("5007");
		//避免停用的码表数据统计不到
		if(!CollectionUtils.isEmpty(tmpSimpleCodeList)){
			for(TsSimpleCode ts : tmpSimpleCodeList){
				simpleCodeMap.put(ts.getRid().intValue(),ts);
				if(StringUtils.isNotBlank(ts.getCodeNo())){
					List<Integer> tmpList = codeNoRidMap.get(ts.getCodeNo());
					if(CollectionUtils.isEmpty(tmpList)){
						tmpList = new ArrayList<>();
					}
					tmpList.add(ts.getRid());
					codeNoRidMap.put(ts.getCodeNo(),tmpList);
				}
				if(StringUtils.isNotBlank(ts.getExtendS3())){
					List<Integer> tmpList = extendRidMap.get(Integer.parseInt(ts.getExtendS3()));
					if(CollectionUtils.isEmpty(tmpList)){
						tmpList = new ArrayList<>();
					}
					tmpList.add(ts.getRid());
					extendRidMap.put(Integer.parseInt(ts.getExtendS3()),tmpList);
				}
			}
		}
		if(!CollectionUtils.isEmpty(dangerSimpleList)){
			fatherSimpleList = new ArrayList<>();
			childSimpleList = new ArrayList<>();
			for(TsSimpleCode ts : dangerSimpleList){
				if(null == ts.getCodeNo() || null == ts.getCodeLevelNo()){
					continue;
				}
				if(StringUtils.isNotBlank(ts.getExtendS3())){
					extendMap.put(ts.getRid(), Integer.parseInt(ts.getExtendS3()));
					extendCodeNoMap.put(Integer.parseInt(ts.getExtendS3()), ts.getCodeNo());
				}
				if(!ts.getCodeNo().trim().equals(ts.getCodeLevelNo().trim())){
					childSimpleList.add(ts);//小类
				}
				if(ts.getCodeNo().trim().equals(ts.getCodeLevelNo().trim())){
					List<Integer> childList = new ArrayList<>();
					childList.add(ts.getRid().intValue());
					fatherMap.put(ts.getRid().intValue(), ts);
					fatherChildListMap.put(ts.getRid().intValue(), childList);
					fatherSimpleList.add(ts);
				}
			}
			if(!CollectionUtils.isEmpty(fatherMap)){
				for(Map.Entry<Integer,TsSimpleCode> mapVal : fatherMap.entrySet()){
					String codeNo = mapVal.getValue().getCodeNo().trim();
					List<Integer> childList = fatherChildListMap.get(mapVal.getKey().intValue());
					if(CollectionUtils.isEmpty(childList)){
						continue;
					}
					for(TsSimpleCode simpleCode : dangerSimpleList){
						if(null == simpleCode.getCodeNo() || null == simpleCode.getCodeLevelNo()){
							continue;
						}
						if(simpleCode.getCodeLevelNo().trim().startsWith(codeNo+".")){
							childList.add(simpleCode.getRid().intValue());
						}
					}
					fatherChildListMap.put(mapVal.getKey().intValue(), childList);
				}
			}
		}
	}
	/**
 	 * <p>方法描述：初始化</p>
 	 * @MethodAuthor qrr,2020年12月9日,init
 	 * */
	private void init() {
		this.initZone();
		this.crptSizeList = this.commService.findSimpleCodesByTypeId("5004");
		initDanger();
	}


	/**
 	 * <p>方法描述：初始化地区</p>
 	 * @MethodAuthor qrr,2020年12月9日,initZone
	 * */
	private void initZone() {
		TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		if (null==tsZone) {
			tsZone = Global.getUser().getTsUnit().getTsZone();
		}
		this.searchZoneCode = tsZone.getZoneGb();
		this.searchZoneName = tsZone.getZoneName();
		this.zoneList = this.commService.findZoneList(false, this.searchZoneCode, null, null);
	}

	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2020年6月5日,selSimpleCodeAction
	 * */
	public void selSimpleCodeAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
		paramList.add("5002".equals(this.simpleCodeType) ? "行业类别" : "经济类型");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add(this.simpleCodeType);
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
		if ("5002".equals(this.simpleCodeType)) {
			paramList.add(this.selectIndusTypeIds);
		} else if ("5003".equals(this.simpleCodeType)) {
			paramList.add(this.selectEconomyIds);
		}
		paramMap.put("selectIds", paramList);
		if ("5002".equals(this.simpleCodeType)||"5003".equals(this.simpleCodeType)) {
			paramList = new ArrayList<String>();
	        paramList.add("true");
	        paramMap.put("ifShowFirstCode", paramList);
		}
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList", options, paramMap);
	}
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2019年12月3日,onSimpleCodeAction
	 * */
	public void onSimpleCodeAction(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event
				.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
			if (null!=list && list.size()>0) {
				StringBuffer names = new StringBuffer();
				StringBuffer ids = new StringBuffer();
				for (TsSimpleCode t : list) {
					names.append("，").append(t.getCodeName());
					ids.append(",").append(t.getRid());
				}
				if ("5002".equals(simpleCodeType)) {
					//行业类别
					this.selectIndusTypeIds = ids.substring(1);
					this.selectIndusTypeNames = names.substring(1);
				}else if ("5003".equals(simpleCodeType)){
					//经济类型
					this.selectEconomyIds = ids.substring(1);
					this.selectEconomyNames = names.substring(1);
				}
			}
		}
	}
	/**
 	 * <p>方法描述：清空行业类别</p>
 	 * @MethodAuthor qrr,2019年12月2日,clearIndusName
	 * */
	public void clearSimpleCode() {
		if ("5002".equals(simpleCodeType)) {
			//行业类别
			this.selectIndusTypeNames = null;
			this.selectIndusTypeIds = null;
		}else if ("5003".equals(simpleCodeType)){
			//经济类型
			this.selectEconomyNames = null;
			this.selectEconomyIds = null;
		}
	}
	@Override
	public void viewInit() {

	}

	/**
	 * <p>方法描述：切换数据来源 1,体检； 2，企业下载</p>
	 * @MethodAuthor rcj,2020年12月9日,changeSource
	 * */
	public void changeSource(){
		//默认读取体检数据表
		if("1".equals(dataSource)){
			tbTjCrpt = commService.find(TbTjCrpt.class, crptId);
		}else if("2".equals(dataSource)){
			//默认读取申报企业表
			tdZyUnitbasicinfo = commService.find(TdZyUnitbasicinfo.class, otherId);
		}
		if(null == year){//避免全部提交 导致year 然后NPE
			year = DateUtils.getYearInt();
		}
	}
	/**
 	 * <p>方法描述：在线申报详情</p>
 	 * @MethodAuthor qrr,2020年12月9日,viewUnitBasicInfo
	 * */
	public void viewUnitBasicInfo(){
		if (null==unitBasicId) {
			return;
		}
		StringBuffer url = new StringBuffer();
		url.append("/webapp/heth/comm/crptshow/tdZyUnitBasicInfoView.faces?rid=").append(unitBasicId);
		RequestContext.getCurrentInstance().execute("top.ShortcutMenuClick('','申报信息','"+url+"','');");
	}


	@Override
	public String[] buildHqls() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from (");
		sql.append(" SELECT A.*,rank() over (partition by INSTITUTION_CODE order by DATA_SOURCE) AS NUM ");
		sql.append(" FROM (");
		sql.append("SELECT T.RID,CASE WHEN T1.ZONE_TYPE >2 THEN SUBSTR(T1.FULL_NAME, INSTR(T1.FULL_NAME,'_')+1) ELSE T1.FULL_NAME END ZONE_NAME,");
		sql.append(" T.CRPT_NAME,T.INSTITUTION_CODE,T3.CODE_NAME AS ECONOMY_NAME,T4.CODE_NAME AS CRPT_SIZE_NAME,T2.CODE_NAME AS INDUS_TYPE_NAME,1 AS DATA_SOURCE");
		sql.append(" ,CASE WHEN T5.RID IS NOT NULL THEN '职业健康检查，企业申报' ELSE '职业健康检查' END ");
		sql.append(",T1.ZONE_GB,T.ECONOMY_ID,T.CRPT_SIZE_ID,T.INDUS_TYPE_ID,T5.RID as otherId");
		sql.append(" FROM TB_TJ_CRPT T");
		sql.append(" LEFT JOIN TS_ZONE T1 ON T1.RID = T.ZONE_ID");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T2.RID = T.INDUS_TYPE_ID");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T3.RID = T.ECONOMY_ID");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T4.RID = T.CRPT_SIZE_ID");
		sql.append(" LEFT JOIN (SELECT * FROM (");
		sql.append(" SELECT TT.CREDIT_CODE,TT.RID,rank() over (partition by TT.CREDIT_CODE order by TT.DECLARE_DATE DESC,TT.RID desc) AS NUM ");
		sql.append(" FROM TD_ZY_UNITBASICINFO TT ");
		sql.append(") WHERE NUM=1");
		sql.append(")T5 ON T5.CREDIT_CODE = T.INSTITUTION_CODE");
		sql.append(" WHERE T.INTER_PRC_TAG =1 ");
		sql.append(" UNION ALL");
		sql.append(" SELECT RID,ZONE_NAME,UNIT_NAME,CREDIT_CODE,ECONOMY_NAME,CRPT_SIZE_NAME,INDUS_TYPE_NAME,DATA_SOURCE,DATA_SOURCE_NAME ");
		sql.append(",ZONE_GB,ECONOMIC_ID,ENTERPRISE_SCALE_ID,INDUSTRY_CATE_ID,otherId");
		sql.append(" FROM (");
		sql.append(" SELECT T.RID,CASE WHEN T1.ZONE_TYPE >2 THEN SUBSTR(T1.FULL_NAME, INSTR(T1.FULL_NAME,'_')+1) ELSE T1.FULL_NAME END ZONE_NAME");
		sql.append(" ,T.UNIT_NAME,T.CREDIT_CODE,T3.CODE_NAME AS ECONOMY_NAME,T4.CODE_NAME AS CRPT_SIZE_NAME,T2.CODE_NAME AS INDUS_TYPE_NAME,2 AS DATA_SOURCE");
		sql.append(" ,'企业申报' AS DATA_SOURCE_NAME ");
		sql.append(",T1.ZONE_GB,T.ECONOMIC_ID,T.ENTERPRISE_SCALE_ID,T.INDUSTRY_CATE_ID,T.RID as otherId");
		sql.append(",rank() over (partition by T.CREDIT_CODE order by T.DECLARE_DATE DESC,T.RID desc) AS NUM");
		sql.append(" FROM TD_ZY_UNITBASICINFO T");
		sql.append(" LEFT JOIN TS_ZONE T1 ON T1.RID = T.ZONE_ID");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T2.RID = T.INDUSTRY_CATE_ID");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T3.RID = T.ECONOMIC_ID");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T4.RID = T.ENTERPRISE_SCALE_ID");
		sql.append(") WHERE NUM=1");
		sql.append(")A WHERE 1=1 ");
		if(StringUtils.isNotBlank(this.searchZoneCode)){
			sql.append(" AND A.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%'");
		}
		if(StringUtils.isNotBlank(this.searchUnitName)){
			sql.append(" AND A.CRPT_NAME LIKE :searchUnitName escape '\\\'");
			this.paramMap.put("searchUnitName","%"+StringUtils.convertBFH(this.searchUnitName.trim())+"%");
		}
		if(StringUtils.isNotBlank(this.searchCreditCode)){
			sql.append(" AND A.INSTITUTION_CODE LIKE :searchCreditCode escape '\\\'");
			this.paramMap.put("searchCreditCode","%"+StringUtils.convertBFH(this.searchCreditCode.trim())+"%");
		}
		if (StringUtils.isNotBlank(this.selectEconomyIds)) {
			sql.append(" AND A.ECONOMY_ID IN (").append(this.selectEconomyIds).append(")");
		}
		if (StringUtils.isNotBlank(this.selectCrptSizeIds)) {
			sql.append(" AND A.CRPT_SIZE_ID IN (").append(this.selectCrptSizeIds).append(")");
		}
		if (StringUtils.isNotBlank(this.selectIndusTypeIds)) {
			sql.append(" AND A.INDUS_TYPE_ID IN (").append(this.selectIndusTypeIds).append(")");
		}
		sql.append(") WHERE NUM=1 ");
		String countSql = "SELECT COUNT(*) FROM ("+sql.toString()+")";
		String selectSql = "SELECT * FROM ("+sql.toString()+")"+" ORDER BY ZONE_GB,CRPT_NAME";
		return new String[]{selectSql,countSql};
	}
	
	@Override
	public void addInit() {
		
	}

	@Override
	public void modInit() {
		this.tbTjCrpt = null;
		//默认读取体检数据表
		if("1".equals(dataSource)){
			tbTjCrpt = commService.find(TbTjCrpt.class, crptId);
			if(null != otherId){
				tdZyUnitbasicinfo = commService.find(TdZyUnitbasicinfo.class, otherId);
			}else{
				tdZyUnitbasicinfo = new TdZyUnitbasicinfo();
				tdZyUnitbasicinfo.setFkByEconomicId(new TsSimpleCode());
				tdZyUnitbasicinfo.setFkByEnterpriseScaleId(new TsSimpleCode());
				tdZyUnitbasicinfo.setFkByIndustryCateId(new TsSimpleCode());
				tdZyUnitbasicinfo.setFkByZoneId(new TsZone());
			}
		}else if("2".equals(dataSource)){
			//默认读取申报企业表
			tdZyUnitbasicinfo = commService.find(TdZyUnitbasicinfo.class, crptId);
			otherId = null;
			Integer ifSubOrg = this.tdZyUnitbasicinfo.getIfBranch();
			String hql = "select t from TbTjCrpt t WHERE t.institutionCode ='"+this.tdZyUnitbasicinfo.getCreditCode() +"' and t.ifSubOrg="+ifSubOrg;
			if(null != ifSubOrg && 1 == ifSubOrg){
				hql = hql + " and t.crptName='"+this.tdZyUnitbasicinfo.getUnitName()+"'";
			}
			tbTjCrpt = commService.findOneByHql(hql,TbTjCrpt.class);
		}
		if(null == tbTjCrpt){
			tbTjCrpt = new TbTjCrpt();
			tbTjCrpt.setTsZoneByZoneId(new TsZone());
			tbTjCrpt.setTsSimpleCodeByIndusTypeId(new TsSimpleCode());
			tbTjCrpt.setTsSimpleCodeByEconomyId(new TsSimpleCode());
			tbTjCrpt.setTsSimpleCodeByCrptSizeId(new TsSimpleCode());
		}else{
			if(tbTjCrpt.getTsSimpleCodeByIndusTypeId() == null){
				tbTjCrpt.setTsSimpleCodeByIndusTypeId(new TsSimpleCode());
			}
			if(tbTjCrpt.getTsSimpleCodeByEconomyId() == null){
				tbTjCrpt.setTsSimpleCodeByEconomyId(new TsSimpleCode());
			}
			if(tbTjCrpt.getTsSimpleCodeByCrptSizeId() == null){
				tbTjCrpt.setTsSimpleCodeByCrptSizeId(new TsSimpleCode());
			}
			if(tbTjCrpt.getTsZoneByZoneId() == null){
				tbTjCrpt.setTsZoneByZoneId(new TsZone());
			}
		}
		if(null == tdZyUnitbasicinfo){
			tdZyUnitbasicinfo = new TdZyUnitbasicinfo();
			tdZyUnitbasicinfo.setFkByEconomicId(new TsSimpleCode());
			tdZyUnitbasicinfo.setFkByEnterpriseScaleId(new TsSimpleCode());
			tdZyUnitbasicinfo.setFkByIndustryCateId(new TsSimpleCode());
			tdZyUnitbasicinfo.setFkByZoneId(new TsZone());
		}else{
			if(tdZyUnitbasicinfo.getFkByEconomicId() == null){
				tdZyUnitbasicinfo.setFkByEconomicId(new TsSimpleCode());
			}
			if(tdZyUnitbasicinfo.getFkByEnterpriseScaleId() == null){
				tdZyUnitbasicinfo.setFkByEnterpriseScaleId(new TsSimpleCode());
			}
			if(tdZyUnitbasicinfo.getFkByIndustryCateId() == null){
				tdZyUnitbasicinfo.setFkByIndustryCateId(new TsSimpleCode());
			}
			if(tdZyUnitbasicinfo.getFkByZoneId() == null){
				tdZyUnitbasicinfo.setFkByZoneId(new TsZone());
			}
		}


		initTips();
		initMsgDiv();
		this.searchOnLineReport();
		year = DateUtils.getYearInt();
		changeYear();
	}

	/**
	 * 我的消息查看
	 *
	 * @editContent 加载提示区域数据
	 *
	 * <AUTHOR>
	 * @history 2020-12-10
	 */
	private void initTips(){
		badConditionList = new ArrayList<>();

		if(null != tdZyUnitbasicinfo && null != tdZyUnitbasicinfo.getDeclareYear() && tdZyUnitbasicinfo.getDeclareYear().intValue()==DateUtils.getYear(new Date())){
			StringBuffer sb =new StringBuffer();
			sb.append(" SELECT T1.RID FROM TD_TJ_BHK T1 WHERE T1.CRPT_ID = ").append(crptId);
			//体检日期
			sb.append(" AND T1.BHK_DATE >= TO_DATE('")
					.append(DateUtils.formatDate(DateUtils.getYearFirstDay(new Date()), "yyyy-MM-dd"))
					.append("','yyyy-MM-dd')");
			sb.append(" AND T1.BHK_DATE  <= TO_DATE('")
					.append(DateUtils.formatDate(DateUtils.getYearLastDay(new Date()), "yyyy-MM-dd"))
					.append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
			int count = commService.findCountBySql(sb.toString());
			if(count<1) {
				//当前年有申报记录
				String hql ="select t from TdZyUnithealthcustody t where t.fkByMainId.rid="+tdZyUnitbasicinfo.getRid();
				List<TdZyUnithealthcustody> list = commService.findByHql(hql, TdZyUnithealthcustody.class);
				if(!CollectionUtils.isEmpty(list)){
					if(null != list.get(0).getIfhea() && list.get(0).getIfhea().intValue()==1){
						//申报记录中有开展职业健康检查，提示“该企业当年体检数据未上报！”
						badConditionList.add("该企业当年体检数据未上报！");
					}else{
						//没有开展职业健康检查，提示“该企业当年无体检数据！”
						badConditionList.add("该企业当年无体检数据！");
					}
				}else{
					//没有开展职业健康检查，提示“该企业当年无体检数据！”
					badConditionList.add("该企业当年无体检数据！");
				}
			}
		}


		if(tdZyUnitbasicinfo.getRid() == null){
			badConditionList.add("该企业未申报！");
		}else{
			Date declareDate = tdZyUnitbasicinfo.getDeclareDate();
			try {
				int month = getMonthSpace(DateUtils.formatDate(declareDate), DateUtils.formatDate(new Date()));
				if( month>limitMonth.intValue()){
					badConditionList.add("该企业超期未申报！");
				}
			}catch (Exception e){
				e.printStackTrace();
			}
		}

	}

	private  int getMonthSpace(String date1, String date2) throws  Exception{
		 SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		 int result=0;
		 Calendar cal1 = new GregorianCalendar();
		 cal1.setTime(sdf.parse(date1));
		 Calendar cal2 = new GregorianCalendar();
		 cal2.setTime(sdf.parse(date2));
		 result =(cal1.get(Calendar.YEAR) - cal2.get(Calendar.YEAR)) * 12 + cal1.get(Calendar.MONTH)- cal2.get(Calendar.MONTH);
//		 int i = cal1.get(Calendar.DATE) - cal2.get(Calendar.DATE);
		 int abs = Math.abs(result);
//		 if(i<0){
//			return abs+1;
//		 }
		 return abs;
	}

	/**
	 * 我的消息查看
	 *
	 * @editContent 加载查询块显示区域数据
	 *
	 * <AUTHOR>
	 * @history 2020-12-10
	 */
	private void initMsgDiv() {
		msgList = new ArrayList<Object[]>();
			for (int i = 0; i < 5; i++) {
					Object[] obj  = new Object[11];
					obj[0] = i;
					//名称
					if(i == 0){
						obj[1] = "危害因素分析";
					}else if(i == 1){
						obj[1] = "人群分析";
					}else if(i == 2){
						obj[1] = "体检情况";
					}else if(i == 3){
						obj[1] = "在线申报情况";
					}else if(i == 4){
						obj[1] = "在线检测情况";
					}
					//数量颜色
					obj[4] = "rgba(255,255,255,1)";
					//报告卡名字颜色
					obj[5] = "rgba(255,255,255,1)";
					//字体大小
					obj[6] = "26px";
					//数字位置
					obj[7] = "15px";
					//背景圆圈色
					obj[8] = "/resources/images/heth/crptShow.png";
					//背景连接
					obj[9] = "visible";
					if(i == 0){
						//背景色
						obj[3] = "#6193fc";
						obj[4] = "#E14D4D";
						obj[5] = "#461010";
						obj[6] = "32px";
						obj[7] = "15px";
						obj[8] = "/resources/images/heth/crptShowSelected.png";
						obj[9] = "hidden";
					}else if(i == 1){
						obj[3] = "#f89052";
					}else if(i == 2){
						obj[3] = "#8460fa";
					}else if(i == 3){
						obj[3] = "#6bc539";
					}else if(i == 4){
						obj[3] = "#f66d5a";
					}
					if(0==i||3==i){
						msgList.add(obj);
					}
			}

			analyType = 0;
	}

	/**
	 * 我的消息查看
	 *
	 * @editContent 分析操作
	 *
	 * <AUTHOR>
	 * @history 2020-12-10
	 */
	public void analyAction(){
		if(!CollectionUtils.isEmpty(msgList)){
			for (int i = 0; i <msgList.size() ; i++) {
				Object[] obj = msgList.get(i);
				int val = new Integer(obj[0].toString()).intValue();
				if(val==analyType.intValue()){
					obj[8] = "/resources/images/heth/crptShowSelected.png";
				}else{
					obj[8] = "/resources/images/heth/crptShow.png";
				}
			}
		}
		if(0 == analyType){
			changeYear();
		}
	}
	/**
 	 * <p>方法描述：查询在线申报数据</p>
 	 * @MethodAuthor qrr,2020年12月11日,searchOnLineReport
	 * */
	public void searchOnLineReport(){
		if (null == this.tdZyUnitbasicinfo
				|| StringUtils.isBlank(this.tdZyUnitbasicinfo.getCreditCode())) {
			this.onLineReportModel = null;
			return;
		}
		String[] sql = this.buildOnLineReportSqls();
		this.onLineReportModel = new DefaultLazyDataModel(sql[0], sql[1], null, null, "", this.ifSQL);
	}
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2020年12月11日,buildOnLineReportSqls
	 * */
	private String[] buildOnLineReportSqls() {

		StringBuffer sql = new StringBuffer();
		sql.append("select t.rid,t.DECLARE_DATE,t.DECLARE_TYPE");
		sql.append(",case when t1.IF_LEADERS_TRAIN =1 and t1.IF_MANAGERS_TRAIN =1 and t1.TRAIN_SUM>0 then 1 else 0 end as ifTrain");
		sql.append(",t.DECLARE_YEAR");
		sql.append(" from TD_ZY_UNITBASICINFO t ");
		sql.append(" left join TD_ZY_TRAIN_SITUATION t1 on t1.MAIN_ID = t.rid");
		sql.append(" where 1=1 ");
		String creditCode = this.tdZyUnitbasicinfo.getCreditCode();
		if (StringUtils.isNotBlank(creditCode)) {
			sql.append(" and t.CREDIT_CODE ='").append(creditCode).append("'");
		}
		String countSql = "select count(*) from ("+sql.toString()+")";
		String selectSql = "select * from ("+sql.toString()+") order by DECLARE_DATE desc";
		return new String[]{selectSql,countSql};
	}
	@Override
	public void saveAction() {
		
	}

	public void dangerTableAndChatQuery(){
		dangerTableAndChatDataFill();
		buildJson(true);
		buildJson(false);
	}

	/** 危害因素分析 表格及图表数据组合 */
	private void dangerTableAndChatDataFill(){
		dangerChatMap.clear();
		Object[] declareInfoObj = this.basicinfoService.declareInfoQuery(this.tbTjCrpt, this.tdZyUnitbasicinfo, this.year);//企业申报的信息

		boolean flag = true;
		List<Integer> resultRidList = new ArrayList<>();
		Map<String,TsSimpleCode> fatherCodeMap = new HashMap<>();
		//大类多个
		for(TsSimpleCode fatherSimple : fatherSimpleList){
			fatherCodeMap.put(fatherSimple.getCodeNo(), fatherSimple);
			Integer num = extendMap.get(fatherSimple.getRid().intValue());
			List<String> codeList = new ArrayList<>();
			codeList.add(fatherSimple.getCodeNo());
			if(null != num){
				List<Integer> ridList = extendRidMap.get(num);
				if(!CollectionUtils.isEmpty(ridList)){
					for(Integer tmpRid : ridList){
						codeList.add(simpleCodeMap.get(tmpRid.intValue()).getCodeNo());
					}
				}
			}
			Integer k = fatherCheckNumQuery(null, codeList);
			if(null != k){
				flag = false;
				resultRidList.add(fatherSimple.getRid().intValue());
			}
			CrptShowDangerObj t = new CrptShowDangerObj(k);
			t.setDangerType(fatherSimple.getCodeName());
			dangerChatMap.put(fatherSimple.getRid().intValue(), t);
		}
		Integer tmpMainRid = null == declareInfoObj || null == declareInfoObj[0] ? null :
				Integer.parseInt(declareInfoObj[0].toString()); //是否有粉尘危害因素
		List<TsSimpleCode> tmpChildSimpleList = new ArrayList<>();
		List<String> onlineCodeNoList = this.basicinfoService.findCrptOnlineCodeNoByMainRid(tmpMainRid);
		//小类多个
		for(TsSimpleCode childSimple : childSimpleList){
			List<Integer> ridList = this.codeNoRidMap.get(childSimple.getCodeNo());
			Integer k = fatherCheckNumQuery(ridList, null);
			if(null != k){
				flag = false;
				resultRidList.add(childSimple.getRid().intValue());
			}
			if((null == k || 0 == k) && !onlineCodeNoList.contains(childSimple.getCodeNo())){
				continue;
			}
			tmpChildSimpleList.add(childSimple);
			CrptShowDangerObj t = new CrptShowDangerObj(k);
			t.setDangerType(childSimple.getCodeName());
			dangerChatMap.put(childSimple.getRid().intValue(), t);
		}

		if(year == DateUtils.getYearInt() && flag && null != declareInfoObj){
			//可以提示 该企业当年体检数据未上报！
		}

		StringBuffer tipBuffer = new StringBuffer("");
		if(null != tmpMainRid){
			Map<Integer,List<Object[]>> declareSubMap = this.basicinfoService.declareSubInfoQuery(tmpMainRid);
			Map<String, Integer> totalMap = new HashMap<>();
			Map<String, Integer> checkMap = new HashMap<>();
			if(!CollectionUtils.isEmpty(declareSubMap)){
				this.fillTotalOrCheckMap(declareSubMap.get(1), totalMap);
				this.fillTotalOrCheckMap(declareSubMap.get(2), checkMap);
			}
			Integer hasDust = null == declareInfoObj[1] ? null : Integer.parseInt(declareInfoObj[1].toString()); //是否有粉尘危害因素
			Integer dustTotal = null == declareInfoObj[2] ? null : Integer.parseInt(declareInfoObj[2].toString());//粉尘接触总人数

			Integer hasChemistry = null == declareInfoObj[3] ? null : Integer.parseInt(declareInfoObj[3].toString());//是否有化学物质危害因素
			Integer chemistryTotal = null == declareInfoObj[4] ? null : Integer.parseInt(declareInfoObj[4].toString());//化学物质危害因素接触总人数

			Integer hasPhysics = null == declareInfoObj[5] ? null : Integer.parseInt(declareInfoObj[5].toString());//是否有物理危害因素
			Integer physicsTotal = null == declareInfoObj[6] ? null : Integer.parseInt(declareInfoObj[6].toString());//物理因素接触总人数

			Integer hasRadio = null == declareInfoObj[7] ? null : Integer.parseInt(declareInfoObj[7].toString());//是否有放射性因素
			Integer radioTotal= null == declareInfoObj[8] ? null : Integer.parseInt(declareInfoObj[8].toString());//放射性因素接触总人数

			Integer hasBiology = null == declareInfoObj[9] ? null : Integer.parseInt(declareInfoObj[9].toString());//是否有生物因素
			Integer bioLogyTotal = null == declareInfoObj[10] ? null : Integer.parseInt(declareInfoObj[10].toString());//生物因素接触总人数

			Integer hasOther = null == declareInfoObj[11] ? null : Integer.parseInt(declareInfoObj[11].toString());//是否有其他因素
			Integer otherTotal = null == declareInfoObj[12] ? null : Integer.parseInt(declareInfoObj[12].toString());//其他因素接触总人数

			//以下 申报检查人数
			Integer hasCheckDust = null == declareInfoObj[13] ? null : Integer.parseInt(declareInfoObj[13].toString());//是否有健康检查粉尘
			Integer dustCheckNum = null == declareInfoObj[14] ? null : Integer.parseInt(declareInfoObj[14].toString());//粉尘体检人数

			Integer hasCheckChemistry = null == declareInfoObj[15] ? null : Integer.parseInt(declareInfoObj[15].toString());//是否有健康检查化学物质
			Integer chemistryCheckNum = null == declareInfoObj[16] ? null : Integer.parseInt(declareInfoObj[16].toString());//化学物质体检人数

			Integer hasCheckPhysics = null == declareInfoObj[17] ? null : Integer.parseInt(declareInfoObj[17].toString());//是否有健康检查物理因素
			Integer physicsCheckNum = null == declareInfoObj[18] ? null : Integer.parseInt(declareInfoObj[18].toString());//物理因素体检人数

			Integer hasCheckRadio = null == declareInfoObj[19] ? null : Integer.parseInt(declareInfoObj[19].toString());//是否有健康检查放射性因素
			Integer radioCheckNum = null == declareInfoObj[20] ? null : Integer.parseInt(declareInfoObj[20].toString());//放射性因素体检人数

			String str = fillDangerData(this.extendCodeNoMap.get(1), hasDust, dustTotal, resultRidList, hasCheckDust, dustCheckNum);
			if(null != str){
				tipBuffer.append(str);
			}

			str = fillDangerData(this.extendCodeNoMap.get(2), hasChemistry, chemistryTotal, resultRidList, hasCheckChemistry, chemistryCheckNum);
			if(null != str){
				tipBuffer.append(str);
			}

			str = fillDangerData(this.extendCodeNoMap.get(3), hasPhysics, physicsTotal, resultRidList, hasCheckPhysics, physicsCheckNum);
			if(null != str){
				tipBuffer.append(str);
			}

			str = fillDangerData(this.extendCodeNoMap.get(4), hasRadio, radioTotal, resultRidList, hasCheckRadio, radioCheckNum);
			if(null != str){
				tipBuffer.append(str);
			}

			str = fillDangerData(this.extendCodeNoMap.get(5), hasBiology, bioLogyTotal, resultRidList, 0, null);
			if(null != str){
				tipBuffer.append(str);
			}

			str = fillDangerData(this.extendCodeNoMap.get(6), hasOther, otherTotal, resultRidList, 0, null);
			if(null != str){
				tipBuffer.append(str);
			}
			if(!CollectionUtils.isEmpty(tmpChildSimpleList)){
				for(TsSimpleCode childSimpleCode : tmpChildSimpleList){
					if(StringUtils.isBlank(childSimpleCode.getCodeLevelNo()) ||
							childSimpleCode.getCodeLevelNo().indexOf(".") == -1){
						continue;
					}
					String fatherCodeNo = childSimpleCode.getCodeLevelNo().substring(0,childSimpleCode.getCodeLevelNo().indexOf("."));
					TsSimpleCode simpleCode = fatherCodeMap.get(fatherCodeNo);
					String extends3 = null == simpleCode ? null : simpleCode.getExtendS3();
					if(StringUtils.isBlank(extends3)){
						continue;
					}
					Integer hasFlag = null;
					Integer hasCheck = null;
					if("1".equals(extends3)){
						hasFlag = hasDust;
						hasCheck = hasCheckDust;
					}else if("2".equals(extends3)){
						hasFlag = hasChemistry;
						hasCheck = hasCheckChemistry;
					}else if("3".equals(extends3)){
						hasFlag = hasPhysics;
						hasCheck = hasCheckPhysics;
					}else if("4".equals(extends3)){
						hasFlag = hasRadio;
						hasCheck = hasCheckRadio;
					}else if("5".equals(extends3)){
						hasFlag = hasBiology;
						hasCheck = 0;
					}else if("6".equals(extends3)){
						hasFlag = hasOther;
						hasCheck = 0;
					}
					String codeNo = childSimpleCode.getCodeNo();
					if(null == hasFlag || StringUtils.isBlank(codeNo)){
						continue;
					}
					str = fillDangerData(codeNo, hasFlag, totalMap.get(codeNo), resultRidList, hasCheck, checkMap.get(codeNo));
					if(null != str){
						tipBuffer.append(str);
					}
				}
			}
		}
		dangerTableList = new ArrayList<>();
		dangerRightList = new ArrayList<>();
		boolean tipNumberFlag = false;
		for(TsSimpleCode fatherSimple : fatherSimpleList){
			CrptShowDangerObj dangerObj = dangerChatMap.get(fatherSimple.getRid().intValue());
			if(null != dangerObj){
				dangerTableList.add(dangerObj);
				if(null != dangerObj.getCheckNumber() && null != dangerObj.getReciveNumber() &&
						dangerObj.getCheckNumber().compareTo(dangerObj.getReciveNumber()) != 0){
					tipNumberFlag = true;
				}
			}
		}
		for(TsSimpleCode childSimple : tmpChildSimpleList){
			CrptShowDangerObj dangerObj = dangerChatMap.get(childSimple.getRid().intValue());
			if(null != dangerObj){
				dangerRightList.add(dangerObj);
			}
		}

		if(year == DateUtils.getYearInt()){
			String tip1 = tipBuffer.toString();
			if(!flag && StringUtils.isNotBlank(tip1)){//没有体检 不需要这个提示
				badConditionList.add("职业健康检查的危害因素与企业申报的危害因素不符！");
			}
			if(tipNumberFlag){
				badConditionList.add("职业健康检查人数与企业申报的接害人数不符！");
			}
		}
	}

	/**
	 * <p>方法描述：合并同codeNo的数据 </p>
	 * @MethodAuthor： pw 2022/9/12
	 **/
	private void fillTotalOrCheckMap(List<Object[]> list, Map<String,Integer> map){
		if(CollectionUtils.isEmpty(list)){
			return;
		}
		for(Object[] objArr : list){
			String codeNo = null == objArr[4] ? null : objArr[4].toString();
			Integer num = null == objArr[2] ? null : Integer.parseInt(objArr[2].toString());
			if(StringUtils.isBlank(codeNo) || null == num){
				continue;
			}
			if(null != map.get(codeNo)){
				num += map.get(codeNo);
			}
			map.put(codeNo, num);
		}
	}

	private void buildJson(boolean flag){
		if(flag){
			lineLeftJson = null;
			if(CollectionUtils.isEmpty(dangerTableList)){
				return;
			}
		}else{
			lineRightJson = null;
			if(CollectionUtils.isEmpty(dangerRightList)){
				return;
			}
		}

		boolean formatFlag = true;
		if(flag){
			for(CrptShowDangerObj dangerObj : dangerTableList){
				if(null != dangerObj.getCheckNumber() || null != dangerObj.getReciveNumber()){
					formatFlag = false;
					break;
				}
			}
		}else{
			for(CrptShowDangerObj dangerObj : dangerRightList){
				if(null != dangerObj.getCheckNumber() || null != dangerObj.getReciveNumber()){
					formatFlag = false;
					break;
				}
			}
		}
		GsonOption op = new GsonOption();
		if(!flag){
			op.dataZoom().show(true);
			op.dataZoom().realtime(true);
			op.dataZoom().start(0);
			BigDecimal one = new BigDecimal(6);
			BigDecimal two = new BigDecimal(dangerRightList.size());
			BigDecimal divide = one.multiply(new BigDecimal(100)).divide(
					two, 0);
			int end = divide.intValue();
			if(end > 100){
				end = 100;
			}
			//end是按照百分比 不是个数
			op.dataZoom().end(end);
		}
		op.tooltip().trigger(Trigger.axis);
		op.grid().y(65);
		op.grid().x(85);
		op.grid().x2(65);
		op.grid().y2("20%");
		op.title().x(X.center);
		String title = year+(flag ? "年危害因素大类检查人数对比" : "年危害因素小类检查人数对比");

		op.title(title);
		op.title().textStyle().fontFamily("微软雅黑");
		op.title().textStyle().fontWeight("bolder");
		op.title().textStyle().setColor("rgba(30,144,255,0.8)");

		op.legend().show(true);
		op.legend().textStyle().fontFamily("微软雅黑");
		op.legend().padding(new Integer[]{30,0,0,0});
		String [] arr =  {"健康检查","企业申报"};
		op.legend().x(X.right).y(Y.top).data(arr);

		//纵坐标左
		//https://github.com/abel533/ECharts/blob/master/src/main/java/com/github/abel533/echarts/axis/ValueAxis.java
		ValueAxis v = new ValueAxis();
		v.axisLabel().textStyle().color("rgba(0,0,0,1)");
		v.axisLine().lineStyle().color("rgba(30,144,255,0.8)");
		v.splitNumber(5);
		v.precision(0);
		v.scale(true);
		if(formatFlag){
			v.axisLabel().setFormatter("function(value,index){return value*10}");
		}
		v.min(0);
		v.setName("单位：人");
		v.setShow(true);
		op.yAxis(v);

		/*CategoryAxis left = new CategoryAxis();
		left.type(AxisType.value);
		left.boundaryGap(true).axisLabel();
		left.axisLine().lineStyle().setColor("rgba(30,144,255,0.8)");
		left.axisLabel().setFormatter("function(value,index){return value.toFixed(0)}");
		left.setName("单位：人");
		left.setShow(true);
		op.yAxis(left);*/

		//横坐标
		CategoryAxis categorxAxis = new CategoryAxis();
		categorxAxis.setType(AxisType.category);
		categorxAxis.boundaryGap(true).axisLabel().interval(0);
		categorxAxis.setShow(true);
		categorxAxis.axisLabel().textStyle().fontFamily("微软雅黑");
		//categorxAxis.axisLabel().textStyle().fontWeight("bolder");
		categorxAxis.axisLabel().setRotate(20);
		categorxAxis.axisLine().lineStyle().setColor("rgba(30,144,255,0.8)");
		categorxAxis.axisLabel().textStyle().setColor("rgba(0,0,0,1)");
		List<Series> series = new ArrayList<>();
		List<Object> lineDatas1 = new ArrayList<>();
		List<Object> lineDatas2 = new ArrayList<>();
		if(flag){
			for(CrptShowDangerObj dangerObj : dangerTableList){
				categorxAxis.data(dangerObj.getDangerType());
				lineDatas1.add(dangerObj.getCheckNumber() == null ? 0 : dangerObj.getCheckNumber());
				lineDatas2.add(dangerObj.getReciveNumber() == null ? 0 : dangerObj.getReciveNumber());
			}
		}else{
			for(CrptShowDangerObj dangerObj : dangerRightList){
				categorxAxis.data(dangerObj.getDangerType());
				lineDatas1.add(dangerObj.getCheckNumber() == null ? 0 : dangerObj.getCheckNumber());
				lineDatas2.add(dangerObj.getReciveNumber() == null ? 0 : dangerObj.getReciveNumber());
			}
		}
		op.xAxis(categorxAxis);
		Bar bar = new Bar("健康检查");
		bar.setData(lineDatas1);
		bar.itemStyle().normal().barBorderRadius(3);
		series.add(bar);
		Bar bar2 = new Bar("企业申报");
		bar2.setData(lineDatas2);
		bar2.itemStyle().normal().barBorderRadius(3);
		series.add(bar2);
		op.series(series);

		if(flag){
			lineLeftJson = op.toString();
		}else{
			lineRightJson = op.toString();
		}
	}

	/** 赋值 */
	private String fillDangerData(String codeNo, Integer hasDust, Integer dustTotal,
								  List<Integer> resultRidList, Integer hasCheckDust, Integer dustCheckNum){
		StringBuffer tipBuffer = new StringBuffer("");
		List<Integer> ridList = this.codeNoRidMap.get(codeNo);
		boolean flag = false;
		if(!CollectionUtils.isEmpty(ridList)){
			for(Integer i : ridList){
				CrptShowDangerObj tmpDangerObj = dangerChatMap.get(i.intValue());
				TsSimpleCode simpleCode = simpleCodeMap.get(i.intValue());
				if(null == tmpDangerObj || null == simpleCode){
					continue;
				}
				if(null != hasDust){
					if((resultRidList.contains(i.intValue()) && 0 == hasDust) || (!resultRidList.contains(i.intValue()) && 1 == hasDust)){
						tipBuffer.append("，").append(simpleCode.getCodeName());
					}
				}
				if(null != fatherMap.get(i.intValue())){
					flag = true;
				}
				if(null != hasDust && 1 == hasDust){
					tmpDangerObj.setReciveNumber(dustTotal);
				}
				if(null != hasCheckDust && 1 == hasCheckDust){
					tmpDangerObj.setCrptCheckNumber(dustCheckNum);
				}
				dangerChatMap.put(i.intValue(), tmpDangerObj);
			}
		}
		String tip1 = tipBuffer.toString();
		if(StringUtils.isNotBlank(tip1) && flag){
			return tip1;
		}
		return null;
	}

	/** 危害因素大类 检查人数查询 */
	private Integer fatherCheckNumQuery(List<Integer> rids, List<String> codeList){
		if((CollectionUtils.isEmpty(rids) && CollectionUtils.isEmpty(codeList)) || null == tbTjCrpt || null == tbTjCrpt.getRid() || null == year){
			return null;
		}
		StringBuffer buffer = new StringBuffer();
		Map<String,Object> paramMap = new HashMap<>();
		buffer.append(" SELECT COUNT(DISTINCT T.PERSON_ID) from TD_TJ_BHK T ");
		buffer.append(" INNER JOIN TD_TJ_BADRSNS T1 ON T.RID = T1.BHK_ID ");
		if(!CollectionUtils.isEmpty(codeList)){
			buffer.append(" INNER JOIN TS_SIMPLE_CODE T2 ON T2.RID = T1.BADRSN_ID ");
		}
		buffer.append(" WHERE T.CRPT_ID= :crptId ");
		paramMap.put("crptId",tbTjCrpt.getRid());
		buffer.append("  AND T.BHK_DATE >= to_date('").append(year).append("-01-01").append(" 00:00:00','yyyy-mm-dd hh24:mi:ss')");
		buffer.append("  AND T.BHK_DATE <= to_date('").append(year).append("-12-31").append(" 23:59:59','yyyy-mm-dd hh24:mi:ss')");
		if(!CollectionUtils.isEmpty(codeList)){
			if(codeList.size() == 1){
				buffer.append(" AND T2.CODE_NO like '").append(codeList.get(0)).append("%'");
			}else{
				StringBuffer badBuffer = new StringBuffer();
				for(String tmpStr : codeList){
					badBuffer.append("OR T2.CODE_NO like '").append(tmpStr).append("%'");
				}
				buffer.append(" AND (").append(badBuffer.substring(2)).append(")");
			}
		}else{
			StringBuffer badBuffer = new StringBuffer();
			for(Integer badId : rids){
				badBuffer.append(",").append(badId.intValue());
			}
			buffer.append(" AND T1.BADRSN_ID IN (").append(badBuffer.substring(1)).append(") ");
		}
		List<Object> resultList = commService.findDataBySqlNoPage(buffer.toString(),paramMap);
		if(!CollectionUtils.isEmpty(resultList)){
			return Integer.parseInt(resultList.get(0).toString()) == 0 ? null : Integer.parseInt(resultList.get(0).toString());
		}
		return null;
	}

	/** 危害因素分析 年份选择 */
	public void changeYear(){
		if(0 == analyType){
			dangerTableAndChatQuery();
			RequestContext.getCurrentInstance().execute("buildChart()");
		}
	}


	public List<TsZone> getZoneList() {
		return zoneList;
	}
	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}
	public String getSearchZoneCode() {
		return searchZoneCode;
	}
	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}
	public String getSearchZoneName() {
		return searchZoneName;
	}
	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}
	public String getSearchUnitName() {
		return searchUnitName;
	}
	public void setSearchUnitName(String searchUnitName) {
		this.searchUnitName = searchUnitName;
	}
	public String getSelectEconomyNames() {
		return selectEconomyNames;
	}
	public void setSelectEconomyNames(String selectEconomyNames) {
		this.selectEconomyNames = selectEconomyNames;
	}
	public List<TsSimpleCode> getCrptSizeList() {
		return crptSizeList;
	}
	public void setCrptSizeList(List<TsSimpleCode> crptSizeList) {
		this.crptSizeList = crptSizeList;
	}
	public String getSelectCrptSizes() {
		return selectCrptSizes;
	}
	public void setSelectCrptSizes(String selectCrptSizes) {
		this.selectCrptSizes = selectCrptSizes;
	}
	public String getSelectCrptSizeIds() {
		return selectCrptSizeIds;
	}
	public void setSelectCrptSizeIds(String selectCrptSizeIds) {
		this.selectCrptSizeIds = selectCrptSizeIds;
	}
	public String getSelectIndusTypeNames() {
		return selectIndusTypeNames;
	}
	public void setSelectIndusTypeNames(String selectIndusTypeNames) {
		this.selectIndusTypeNames = selectIndusTypeNames;
	}
	public String getSimpleCodeType() {
		return simpleCodeType;
	}
	public void setSimpleCodeType(String simpleCodeType) {
		this.simpleCodeType = simpleCodeType;
	}
	public Integer getCrptId() {
		return crptId;
	}
	public void setCrptId(Integer crptId) {
		this.crptId = crptId;
	}
	public String getDataSource() {
		return dataSource;
	}
	public void setDataSource(String dataSource) {
		this.dataSource = dataSource;
	}
	public String getSearchCreditCode() {
		return searchCreditCode;
	}
	public void setSearchCreditCode(String searchCreditCode) {
		this.searchCreditCode = searchCreditCode;
	}

	public Integer getOtherId() {
		return otherId;
	}

	public void setOtherId(Integer otherId) {
		this.otherId = otherId;
	}

	public TdZyUnitbasicinfo getTdZyUnitbasicinfo() {
		return tdZyUnitbasicinfo;
	}

	public void setTdZyUnitbasicinfo(TdZyUnitbasicinfo tdZyUnitbasicinfo) {
		this.tdZyUnitbasicinfo = tdZyUnitbasicinfo;
	}

	public TbTjCrpt getTbTjCrpt() {
		return tbTjCrpt;
	}

	public void setTbTjCrpt(TbTjCrpt tbTjCrpt) {
		this.tbTjCrpt = tbTjCrpt;
	}

	public List<Object[]> getMsgList() {
		return msgList;
	}

	public void setMsgList(List<Object[]> msgList) {
		this.msgList = msgList;
	}

	public Integer getAnalyType() {
		return analyType;
	}

	public void setAnalyType(Integer analyType) {
		this.analyType = analyType;
	}


	public List<String> getBadConditionList() {
		return badConditionList;
	}

	public void setBadConditionList(List<String> badConditionList) {
		this.badConditionList = badConditionList;
	}
	public DefaultLazyDataModel getOnLineReportModel() {
		return onLineReportModel;
	}
	public void setOnLineReportModel(DefaultLazyDataModel onLineReportModel) {
		this.onLineReportModel = onLineReportModel;
	}
	public Integer getUnitBasicId() {
		return unitBasicId;
	}
	public void setUnitBasicId(Integer unitBasicId) {
		this.unitBasicId = unitBasicId;
	}

	public List<Integer> getYearList() {
		return yearList;
	}

	public void setYearList(List<Integer> yearList) {
		this.yearList = yearList;
	}

	public Integer getYear() {
		return year;
	}

	public void setYear(Integer year) {
		this.year = year;
	}

	public List<CrptShowDangerObj> getDangerTableList() {
		return dangerTableList;
	}

	public void setDangerTableList(List<CrptShowDangerObj> dangerTableList) {
		this.dangerTableList = dangerTableList;
	}

	public List<CrptShowDangerObj> getDangerRightList() {
		return dangerRightList;
	}

	public void setDangerRightList(List<CrptShowDangerObj> dangerRightList) {
		this.dangerRightList = dangerRightList;
	}

	public List<TsSimpleCode> getDangerSimpleList() {
		return dangerSimpleList;
	}

	public void setDangerSimpleList(List<TsSimpleCode> dangerSimpleList) {
		this.dangerSimpleList = dangerSimpleList;
	}

	public Map<Integer, List<Integer>> getFatherChildListMap() {
		return fatherChildListMap;
	}

	public void setFatherChildListMap(Map<Integer, List<Integer>> fatherChildListMap) {
		this.fatherChildListMap = fatherChildListMap;
	}

	public Map<Integer, TsSimpleCode> getFatherMap() {
		return fatherMap;
	}

	public void setFatherMap(Map<Integer, TsSimpleCode> fatherMap) {
		this.fatherMap = fatherMap;
	}

	public Map<Integer, TsSimpleCode> getSimpleCodeMap() {
		return simpleCodeMap;
	}

	public void setSimpleCodeMap(Map<Integer, TsSimpleCode> simpleCodeMap) {
		this.simpleCodeMap = simpleCodeMap;
	}

	public List<TsSimpleCode> getFatherSimpleList() {
		return fatherSimpleList;
	}

	public void setFatherSimpleList(List<TsSimpleCode> fatherSimpleList) {
		this.fatherSimpleList = fatherSimpleList;
	}

	public List<TsSimpleCode> getChildSimpleList() {
		return childSimpleList;
	}

	public void setChildSimpleList(List<TsSimpleCode> childSimpleList) {
		this.childSimpleList = childSimpleList;
	}

	public Map<Integer, Integer> getExtendMap() {
		return extendMap;
	}

	public void setExtendMap(Map<Integer, Integer> extendMap) {
		this.extendMap = extendMap;
	}

	public Map<Integer, List<Integer>> getExtendRidMap() {
		return extendRidMap;
	}

	public void setExtendRidMap(Map<Integer, List<Integer>> extendRidMap) {
		this.extendRidMap = extendRidMap;
	}

	public Map<Integer, CrptShowDangerObj> getDangerChatMap() {
		return dangerChatMap;
	}

	public void setDangerChatMap(Map<Integer, CrptShowDangerObj> dangerChatMap) {
		this.dangerChatMap = dangerChatMap;
	}

	public String getLineLeftJson() {
		return lineLeftJson;
	}

	public void setLineLeftJson(String lineLeftJson) {
		this.lineLeftJson = lineLeftJson;
	}

	public String getLineRightJson() {
		return lineRightJson;
	}

	public void setLineRightJson(String lineRightJson) {
		this.lineRightJson = lineRightJson;
	}

	public Map<String, List<Integer>> getCodeNoRidMap() {
		return codeNoRidMap;
	}

	public void setCodeNoRidMap(Map<String, List<Integer>> codeNoRidMap) {
		this.codeNoRidMap = codeNoRidMap;
	}

	public Map<Integer, String> getExtendCodeNoMap() {
		return extendCodeNoMap;
	}

	public void setExtendCodeNoMap(Map<Integer, String> extendCodeNoMap) {
		this.extendCodeNoMap = extendCodeNoMap;
	}
}
