package com.chis.modules.heth.comm.logic;

import com.chis.modules.heth.comm.entity.TdZdzybAnalyDetailComm;

import java.util.Date;
import java.util.List;

/**
 * <p>类描述：个案查询-查询条件</p>
 * @ClassAuthor qrr,2020年5月25日,TjPersonSearchConditionPO
 * */
public class TjPersonSearchConditionPO {

	/***查询条件-人员姓名*/
	private String searchPersonName;
	/***查询条件-身份证号*/
	private String searchIDC;
	/**查询条件：体检机构*/
    private String searchUnitId;
    /**查询条件：年龄*/
    private List<TdZdzybAnalyDetailComm> selectAgeAnalyDetails;
    /**查询条件：工龄*/
    private List<TdZdzybAnalyDetailComm> selectWorkAnalyDetails;
	/**
	 * 查询条件-用工单位
	 */
	private String searchEntrustCrptZoneGb;
	private String searchEntrustCrptZoneName;
	private String searchEntrustCrptName;
	/***查询条件-用人单位地区编码*/
	private String searchZoneCode;
	/**用人单位名称*/
	private String searchCrptName;
	/**体检类型*/
	private String[] searchBhkType;
	/***查询条件-体检开始时间*/
	private Date searchStartTime;
	/***查询条件-体检结束时间*/
	private Date searchEndTime;
	/***查询条件-报告打印日期开始时间*/
	private Date startRptPrintDate;
	/***查询条件-报告打印日期结束时间*/
	private Date endRptPrintDate;
	/***查询条件-填报日期开始时间*/
	private Date startCreateDate;
	/***查询条件-填报日期结束时间*/
	private Date endCreateDate;
	/**查询条件-在岗状态*/
	private String selectOnGuardIds;
	/**查询条件-危害因素*/
	private String selectBadRsnIds;
	 /**查询条件：选择的危害因素结论*/
	private String searchSelBhkrstIds;
	/**
	 * 查询条件-选择的主检结论
	 */
	private String searchSelMhkrstIds;
	private String searchSelMhkrstName;
	/**选择的体检项目*/
	private String searchItemIds;
	 /**档案份数*/
    private String searchBhkNum;
    /**查询条件-身份证类型*/
    private String searchPsnType;
    /**查询条件-监测类型*/
    private String[] searchJcType;
    /**导出项目*/
    public String itemNames;
	public String getSearchPersonName() {
		return searchPersonName;
	}
	public String getSearchIDC() {
		return searchIDC;
	}
	public String getSearchUnitId() {
		return searchUnitId;
	}
	public List<TdZdzybAnalyDetailComm> getSelectAgeAnalyDetails() {
		return selectAgeAnalyDetails;
	}
	public List<TdZdzybAnalyDetailComm> getSelectWorkAnalyDetails() {
		return selectWorkAnalyDetails;
	}

	public String getSearchEntrustCrptZoneGb() {
		return searchEntrustCrptZoneGb;
	}

	public void setSearchEntrustCrptZoneGb(String searchEntrustCrptZoneGb) {
		this.searchEntrustCrptZoneGb = searchEntrustCrptZoneGb;
	}

	public String getSearchEntrustCrptZoneName() {
		return searchEntrustCrptZoneName;
	}

	public void setSearchEntrustCrptZoneName(String searchEntrustCrptZoneName) {
		this.searchEntrustCrptZoneName = searchEntrustCrptZoneName;
	}

	public String getSearchEntrustCrptName() {
		return searchEntrustCrptName;
	}

	public void setSearchEntrustCrptName(String searchEntrustCrptName) {
		this.searchEntrustCrptName = searchEntrustCrptName;
	}

	public String getSearchZoneCode() {
		return searchZoneCode;
	}
	public String getSearchCrptName() {
		return searchCrptName;
	}
	public String[] getSearchBhkType() {
		return searchBhkType;
	}
	public Date getSearchStartTime() {
		return searchStartTime;
	}
	public Date getSearchEndTime() {
		return searchEndTime;
	}
	public String getSelectOnGuardIds() {
		return selectOnGuardIds;
	}
	public String getSelectBadRsnIds() {
		return selectBadRsnIds;
	}
	public String getSearchSelBhkrstIds() {
		return searchSelBhkrstIds;
	}
	public String getSearchItemIds() {
		return searchItemIds;
	}

	public String getSearchSelMhkrstIds() {
		return searchSelMhkrstIds;
	}

	public void setSearchSelMhkrstIds(String searchSelMhkrstIds) {
		this.searchSelMhkrstIds = searchSelMhkrstIds;
	}

	public String getSearchSelMhkrstName() {
		return searchSelMhkrstName;
	}

	public void setSearchSelMhkrstName(String searchSelMhkrstName) {
		this.searchSelMhkrstName = searchSelMhkrstName;
	}

	public String getSearchBhkNum() {
		return searchBhkNum;
	}
	public void setSearchPersonName(String searchPersonName) {
		this.searchPersonName = searchPersonName;
	}
	public void setSearchIDC(String searchIDC) {
		this.searchIDC = searchIDC;
	}
	public void setSearchUnitId(String searchUnitId) {
		this.searchUnitId = searchUnitId;
	}
	public void setSelectAgeAnalyDetails(
			List<TdZdzybAnalyDetailComm> selectAgeAnalyDetails) {
		this.selectAgeAnalyDetails = selectAgeAnalyDetails;
	}
	public void setSelectWorkAnalyDetails(
			List<TdZdzybAnalyDetailComm> selectWorkAnalyDetails) {
		this.selectWorkAnalyDetails = selectWorkAnalyDetails;
	}
	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}
	public void setSearchCrptName(String searchCrptName) {
		this.searchCrptName = searchCrptName;
	}
	public void setSearchBhkType(String[] searchBhkType) {
		this.searchBhkType = searchBhkType;
	}
	public void setSearchStartTime(Date searchStartTime) {
		this.searchStartTime = searchStartTime;
	}
	public void setSearchEndTime(Date searchEndTime) {
		this.searchEndTime = searchEndTime;
	}
	public void setSelectOnGuardIds(String selectOnGuardIds) {
		this.selectOnGuardIds = selectOnGuardIds;
	}
	public void setSelectBadRsnIds(String selectBadRsnIds) {
		this.selectBadRsnIds = selectBadRsnIds;
	}
	public void setSearchSelBhkrstIds(String searchSelBhkrstIds) {
		this.searchSelBhkrstIds = searchSelBhkrstIds;
	}
	public void setSearchItemIds(String searchItemIds) {
		this.searchItemIds = searchItemIds;
	}
	public void setSearchBhkNum(String searchBhkNum) {
		this.searchBhkNum = searchBhkNum;
	}
	public String getSearchPsnType() {
		return searchPsnType;
	}
	public void setSearchPsnType(String searchPsnType) {
		this.searchPsnType = searchPsnType;
	}
	public String[] getSearchJcType() {
		return searchJcType;
	}
	public void setSearchJcType(String[] searchJcType) {
		this.searchJcType = searchJcType;
	}

	public Date getStartRptPrintDate() {
		return startRptPrintDate;
	}

	public void setStartRptPrintDate(Date startRptPrintDate) {
		this.startRptPrintDate = startRptPrintDate;
	}

	public Date getEndRptPrintDate() {
		return endRptPrintDate;
	}

	public void setEndRptPrintDate(Date endRptPrintDate) {
		this.endRptPrintDate = endRptPrintDate;
	}

	public Date getStartCreateDate() {
		return startCreateDate;
	}

	public void setStartCreateDate(Date startCreateDate) {
		this.startCreateDate = startCreateDate;
	}

	public Date getEndCreateDate() {
		return endCreateDate;
	}

	public void setEndCreateDate(Date endCreateDate) {
		this.endCreateDate = endCreateDate;
	}

	public String getItemNames() {
		return itemNames;
	}

	public void setItemNames(String itemNames) {
		this.itemNames = itemNames;
	}
}
