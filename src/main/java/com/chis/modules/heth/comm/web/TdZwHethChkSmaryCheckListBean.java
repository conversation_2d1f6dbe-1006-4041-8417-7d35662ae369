package com.chis.modules.heth.comm.web;

import cn.hutool.core.convert.Convert;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.rptvo.SelectManyOrgVo;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.utils.HolidayUtil;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * <p>描述：职业性有害因素监测卡审核</p>
 *
 *  @ClassAuthor: 龚哲,2021/10/22 9:02,TdZwHethChkSmaryCheckListBean
 */
@ManagedBean(name = "tdZwHethChkSmaryCheckListBean")
@ViewScoped
public class TdZwHethChkSmaryCheckListBean extends AbstractReportCardChkSmaryListBean implements IProcessData {

    /**查询条件：用工单位地区集合*/
    private List<TsZone> zoneList;
    /**查询条件：用工单位地区名称*/
    private String searchZoneName;
    /**查询条件：用工单位地区编码*/
    private String searchZoneCode;
    /**查询条件：用工单位名称*/
    private String searchUnitName;
    /**查询条件：用人单位名称*/
    private String searchCrptName;
    /**查询条件：报告日期查询-开始日期*/
    private Date searchRptBdate;
    /**查询条件：报告日期查询-结束日期*/
    private Date searchRptEdate;
    /**查询条件：接收日期查询-开始日期*/
    private Date searchRcvBdate;
    /**查询条件：接收日期查询-结束日期*/
    private Date searchRcvEdate;
    /**查询条件：状态*/
    protected List<SelectItem> stateList = new ArrayList<>();
    /**查询条件：选中状态*/
    private String[] states;
    /**
     * 查询条件：报告卡编码
     */
    private String searchRptNo;
    /**查询条件-报告单位*/
    private String fillSysUnitIds;
    private String fillSysUnitNames;
    /**不同审核级别的全部状态*/
    private String allStates;
    /**审核等级*/
    private String checkLevel;
    /**今天*/
    private Date today;
    /**地区级别*/
    private Integer zoneType;
    /**审核级别*/
    private Integer level;
    /** 选择的结果集 */
    private List<Object[]> selectEntitys;
    /**批量审核提示信息*/
    private String tipInfo;
    /** 审核期限 */
	protected String limitTime;
    private TsZone tsZone;

    /**报告卡最新状态*/
    protected TdZwBgkLastSta newFlow = new TdZwBgkLastSta();
    /**历次审核意见*/
    private List<TdZwBgkFlow> bgkFlows;
    /**审核结果*/
    protected String checkResult;
    /**默认审核意见*/
    private String defaultAuditAdv;

    /**历次审核意见*/
    private List<Object[]> historyList;

    /**市级平台/省级平台*/
    private String platVersion;
    /**
     * 审核界面 审核结果
     */
    private Integer checkState;
    /**
     * 审核界面 审核意见
     */
    private String checkRst;
    /**
     * 详情界面 撤销按钮是否显示
     */
    private Boolean cancelFlag;
    /**
     * 详情界面 撤销后状态
     */
    private Integer cancelState;

    private List<Integer> exportIds;
    private StreamedContent downloadFile;
    private Map<Integer,Object[]> stateOrAuditMap;

    public TdZwHethChkSmaryCheckListBean(){
        this.ifSQL = true;
        this.limitTime = PropertyUtils.getValueWithoutException("limitTime");
        /**批量审核默认审核意见*/
        defaultAuditAdv = PropertyUtils.getValueWithoutException("defaultAuditAdv");
        this.tipInfo = "批量审核：若存在审核意见为空的数据则默认置为【"+defaultAuditAdv+"】；审核人默认为当前登录人。";
        checkLevel=PropertyUtils.getValueWithoutException("checkLevel");
        platVersion=PropertyUtils.getValueWithoutException("platVersion");
        tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        if(null==tsZone.getRealZoneType()){
            this.zoneType = tsZone.getZoneType().intValue();
        }else {
            this.zoneType = tsZone.getRealZoneType().intValue();
        }
        colInt = commService.findSimpleCodesByTypeIdAndExtends1("'5009'","1,2,3").size();
        initParam();
        initState();
        this.searchAction();
    }
    

    /**
     * <p>方法描述：初始化状态</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-21
     **/
    public void initState(){
        level = 0;//1：初审2：复审3：终审
        if("2".equals(checkLevel)){//2级审核
            if (zoneType <= 3) {//终审
                level = 3;
            }else {//初审
                level = 1;
            }
        }else if ("3".equals(checkLevel)) {//3级审核
            if (zoneType == 2) {//终审
                level = 3;
            }else if (zoneType == 3){//复审
                level = 2;
            }else {//初审
                level = 1;
            }
        }
        if("3".equals(checkLevel)){
            stateList.add(new SelectItem("0","待提交") );
            stateList.add(new SelectItem("1","区县级待审") );
            stateList.add(new SelectItem("2","区县级退回") );
            stateList.add(new SelectItem("3","市级待审") );
            stateList.add(new SelectItem("4","市级退回") );
            stateList.add(new SelectItem("5","省级待审") );
            stateList.add(new SelectItem("6","省级退回") );
            stateList.add(new SelectItem("7","省级通过") );
            if(this.zoneType==4){
                states=new String[]{"1"};
            }else if(this.zoneType==3){
                states=new String[]{"3"};
            }else if(this.zoneType==2){
                states=new String[]{"5"};
            }
        }else if("2".equals(checkLevel)){
            if("1".equals(platVersion)){
                stateList.add(new SelectItem("0","待提交") );
                stateList.add(new SelectItem("1","区县级待审") );
                stateList.add(new SelectItem("2","区县级退回") );
                stateList.add(new SelectItem("5","市级待审") );
                stateList.add(new SelectItem("6","市级退回") );
                stateList.add(new SelectItem("7","市级通过") );
                if(this.zoneType==4){
                    states=new String[]{"1"};
                }else if(this.zoneType==3 || this.zoneType==2){
                    states=new String[]{"5"};
                }
            }else if("2".equals(platVersion)){
                stateList.add(new SelectItem("0","待提交") );
                stateList.add(new SelectItem("1","区县级待审") );
                stateList.add(new SelectItem("2","区县级退回") );
                stateList.add(new SelectItem("5","省级待审") );
                stateList.add(new SelectItem("6","省级退回") );
                stateList.add(new SelectItem("7","省级通过") );
                if(this.zoneType==4){
                    states=new String[]{"1"};
                }else if(this.zoneType==2 || this.zoneType==3){
                    states=new String[]{"5"};
                }
            }
        }
    }


    /**
     * <p>方法描述：初始化方法</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-21
     **/
    public void initParam(){
        this.searchZoneCode = tsZone.getZoneCode();//默认显示登录人所在单位管辖地区

        this.searchZoneName = tsZone.getZoneName();

        /*** 地区初始化 ,下拉展示管辖地区及下属地区*/
        this.zoneList = this.commService.findZoneList(false, searchZoneCode, null, null);

    }

    @Override
    public void addInit() {

    }

    /**
     *  <p>方法描述：审核页面初始化</p>
     * @MethodAuthor hsj
     */
    @Override
    public void modInit() {
        //审核意见
        if(0 == ifIsCheckout){
            initFlow();
            this.checkResult = "";
        }
        //获取历次审核
        historyList = chkSmaryService.findHisotryList(rid);
        //初始化历次审核意见
        initHistoryCheckout();
        super.viewInit();
        this.viewInit();
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:jcSubList");
        if(null != dataTable){
            dataTable.setRows(10);
            dataTable.setFirst(0);
        }
    }

    @Override
    public void viewInit() {
        pakCancelFlag();
    }

    /**
     * <p>方法描述：初始化历次审核意见</p>
     * @MethodAuthor： ljy
     * @Date：2021-10-27
     **/
    public void initHistoryCheckout(){
        if(!CollectionUtils.isEmpty(historyList)){
            List<Object[]> checkOutList = new ArrayList<>();
            for(Object[] objArr:historyList){
                Integer opegFlag = null == objArr[7] ? 0 : Integer.parseInt(objArr[7].toString()) ;
                String type = null;
                //审核级别为3
                if("3".equals(checkLevel)){
                    Integer ifCityDirect =  null == objArr[5] ? null : Integer.parseInt(objArr[5].toString()) ;
                    //根据是否市直属判断审核类型
                    if(Integer.valueOf(1).equals(ifCityDirect)){
                        switch (opegFlag){
                            case 41:
                                type = "市级审核通过";
                                break;
                            case 13:
                                type = "市级审核退回";
                                break;
                            case 42:
                                type = "省级审核通过";
                                break;
                            case 32:
                                type = "省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }else{
                        switch (opegFlag) {
                            case 31:
                                type = "区县级审核通过";
                                break;
                            case 11:
                                type = "区县级审核退回";
                                break;
                            case 41:
                                type = "市级审核通过";
                                break;
                            case 22:
                                type = "市级审核退回";
                                break;
                            case 42:
                                type = "省级审核通过";
                                break;
                            case 32:
                                type = "省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }
                }
                //审核级别为2
                if("2".equals(checkLevel)) {
                    Integer ifProvDirect =  null == objArr[6] ? null : Integer.parseInt(objArr[6].toString()) ;
                    //根据是否省直属判断审核类型
                    if(Integer.valueOf(1).equals(ifProvDirect)){
                        switch (opegFlag){
                            case 42:
                                type = "1".equals(this.platVersion)?"市级审核通过":"省级审核通过";
                                break;
                            case 32:
                                type = "1".equals(this.platVersion)?"市级审核退回":"省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }else{
                        switch (opegFlag) {
                            case 43:
                                type = "区县级审核通过";
                                break;
                            case 11:
                                type = "区县级审核退回";
                                break;
                            case 42:
                                type = "1".equals(this.platVersion)?"市级审核通过":"省级审核通过";
                                break;
                            case 32:
                                type = "1".equals(this.platVersion)?"市级审核退回":"省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }
                }
                objArr[4] = type;
                if(type != null){
                    checkOutList.add(objArr);
                }
            }
            historyList = checkOutList;
        }
    }

    /**
     *  <p>方法描述：初始化审核意见</p>
     * @MethodAuthor hsj
     */
    protected void initFlow() {
        this.newFlow = this.chkSmaryService.findTdZwBgkLastSta(rid, getCardType());
        if(null == this.newFlow) {
            this.newFlow = new TdZwBgkLastSta();
        }
        //审核，审核意见、审核人为空
        if (3==level) {
            //终审
            this.newFlow.setProAuditAdv(null);
            this.newFlow.setProChkPsn(Global.getUser().getUsername());
        }else if (2==level) {
            //复审
            this.newFlow.setCityAuditAdv(null);
            this.newFlow.setCityChkPsn(Global.getUser().getUsername());
        }else {
            //初审
            this.newFlow.setCountAuditAdv(null);
            this.newFlow.setCountyChkPsn(Global.getUser().getUsername());
        }

        if(null == this.newFlow.getState()) {
            return;
        }

//        this.bgkFlows = this.chkSmaryService.selectFlowInfos(rid, getCardType());
//        if (CollectionUtils.isEmpty(bgkFlows)) {
//            bgkFlows = new ArrayList<>();
//        }
    }

    public void changeCheckStateBatch(){
        if(null != defaultAuditAdv && new Integer(1).equals(checkState) && StringUtils.isBlank(checkRst)){
            checkRst = defaultAuditAdv;
        }
        if(null != defaultAuditAdv && new Integer(2).equals(checkState) && defaultAuditAdv.equals(checkRst)){
            checkRst = "";
        }
    }

    /**
     *  <p>方法描述：审核状态修改</p>
     * @MethodAuthor hsj
     */
    public void changeCheckState(){
        if(null != defaultAuditAdv && "1".equals(checkResult)){
            //通过
            if(3 == level && StringUtils.isBlank(newFlow.getProAuditAdv())){
                newFlow.setProAuditAdv(defaultAuditAdv);
            }
            if(2 == level && StringUtils.isBlank(newFlow.getCityAuditAdv())){
                newFlow.setCityAuditAdv(defaultAuditAdv);
            }
            if(1 == level && StringUtils.isBlank(newFlow.getCountAuditAdv())){
                newFlow.setCountAuditAdv(defaultAuditAdv);
            }

        }else {
            if(3 == level && defaultAuditAdv.equals(newFlow.getProAuditAdv())){
                newFlow.setProAuditAdv("");
            }
            if(2 == level && defaultAuditAdv.equals(newFlow.getCityAuditAdv())){
                newFlow.setCityAuditAdv("");
            }
            if(1 == level  && defaultAuditAdv.equals(newFlow.getCountAuditAdv())){
                newFlow.setCountAuditAdv("");
            }
        }
    }
    /**
     *  <p>方法描述：提交前验证</p>
     * @MethodAuthor hsj
     */
    public void  beforeSubmit(){
       try{
           if(veryData()){
               return;
           }
           RequestContext context = RequestContext.getCurrentInstance();
           context.execute("PF('ConfirmDialog').show()");
       }catch (Exception e){
           e.printStackTrace();
           JsfUtil.addErrorMessage("提交失败！");
       }
    }
    /**
     *  <p>方法描述：提交审核结果</p>
     * @MethodAuthor hsj
     */
    @Override
    public void saveAction() {
        try {
            //更新报告卡最新状态/报告卡审批流程
            saveOrUpdateNewFlowOrBgkFlow();
            JsfUtil.addSuccessMessage("提交成功！");
            this.backAction();
            this.searchAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }
    /**
     *  <p>方法描述：更新报告卡最新状态</p>
     * @MethodAuthor hsj
     */
    private void saveOrUpdateNewFlowOrBgkFlow() {
        TdZwBgkFlow tdZwBgkFlow=new TdZwBgkFlow();
        tdZwBgkFlow.setCartType(getCardType());
        tdZwBgkFlow.setBusId(rid);
        tdZwBgkFlow.setRcvDate(new Date());
        tdZwBgkFlow.setFkBySmtPsnId(new TsUserInfo(Global.getUser().getRid()));
        //获取用人单位地址
        TsZone zone = chkSmary.getFkByEmpZoneId();
        if("2".equals(checkLevel)){
            //二级审核
            switch (level){
                case 1:
                    if("1".equals(checkResult)){
                        newFlow.setProRcvDate(new Date());
                        newFlow.setState(5);
                        //市级平台/省级平台  审核通过 操作标识
                        tdZwBgkFlow.setOperFlag(43);
                    }else {
                        newFlow.setOrgRcvDate(new Date());
                        newFlow.setState(2);
                        tdZwBgkFlow.setOperFlag(11);
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getCountAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getCountyChkPsn());
                    break;
                case 3:
                    if("1".equals(checkResult)){
                        newFlow.setProSmtDate(new Date());
                        newFlow.setState(7);
                        //市级平台/省级平台  审核通过 操作标识
                        tdZwBgkFlow.setOperFlag(42);
                    }else {
                        newFlow.setOrgRcvDate(new Date());
                        newFlow.setState(6);
                        //市级平台/省级平台  审核退回 操作标识
                        tdZwBgkFlow.setOperFlag(32);
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getProAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getProChkPsn());
                    break;
                default:
                    break;
            }

        }else if("3".equals(checkLevel)){
            //三级审核
            switch (level){
                case 1:
                    if("1".equals(checkResult)){
                        newFlow.setCityRcvDate(new Date());
                        newFlow.setState(3);
                        tdZwBgkFlow.setOperFlag(31);
                    }else {
                        newFlow.setOrgRcvDate(new Date());
                        newFlow.setState(2);
                        tdZwBgkFlow.setOperFlag(11);
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getCountAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getCountyChkPsn());
                    break;
                case 2:
                    if("1".equals(checkResult)){
                        newFlow.setProRcvDate(new Date());
                        newFlow.setState(5);
                        tdZwBgkFlow.setOperFlag(41);
                    }else {
                        newFlow.setOrgRcvDate(new Date());
                        newFlow.setState(4);
                        if ("1".equals(zone.getIfCityDirect())) {//市直属
                            tdZwBgkFlow.setOperFlag(13);
                        }else{
                            tdZwBgkFlow.setOperFlag(22);
                        }
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getCityAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getCityChkPsn());
                    break;
                case 3:
                    if("1".equals(checkResult)){
                        newFlow.setProSmtDate(new Date());
                        newFlow.setState(7);
                        tdZwBgkFlow.setOperFlag(42);
                    }else {
                        newFlow.setOrgRcvDate(new Date());
                        newFlow.setState(6);
                        tdZwBgkFlow.setOperFlag(32);
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getProAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getProChkPsn());
                    break;
                default:
                    break;
            }
        }
        chkSmaryService.updateInsLastRecord(newFlow,tdZwBgkFlow,limitTime);
    }

    /**
     *  <p>方法描述：提交审核验证</p>
     * @MethodAuthor hsj
     */
    private boolean veryData() {
        boolean flag = false;
        if(StringUtils.isBlank(checkResult)){
            JsfUtil.addErrorMessage("审核结果不能为空！");
            flag = true;
        }
        if(3 == level ){
            if(StringUtils.isBlank(newFlow.getProAuditAdv())){
                JsfUtil.addErrorMessage("审核意见不能为空！");
                flag = true;
            }
            if(StringUtils.isBlank(newFlow.getProChkPsn())){
                JsfUtil.addErrorMessage("审核人不能为空！");
                flag = true;
            }

        }
        if(2 == level  ){
            if( StringUtils.isBlank(newFlow.getCityAuditAdv())){
                JsfUtil.addErrorMessage("审核意见不能为空！");
                flag = true;
            }
            if(StringUtils.isBlank(newFlow.getCityChkPsn())){
                JsfUtil.addErrorMessage("审核人不能为空！");
                flag = true;
            }
        }
        if(1 == level ){
            if( StringUtils.isBlank(newFlow.getCountAuditAdv())){
                JsfUtil.addErrorMessage("审核意见不能为空！");
                flag = true;
            }
            if(StringUtils.isBlank(newFlow.getCountyChkPsn())){
                JsfUtil.addErrorMessage("审核人不能为空！");
                flag = true;
            }
        }
        return flag;
    }
    @Override
    public void processData(List<?> list) {
    	try {
            if (StringUtils.isBlank(limitTime) || null==level) {
                return;
            }
            if (null!=list && list.size()>0) {
                List<Object[]> result = (List<Object[]>) list;
                for (Object[] obj : result) {
                    if (null == obj[9]) {
                        continue;
                    }
                    if (1==level.intValue() && "1".equals(obj[9].toString()) && null!=obj[10]) {//初审-待审核
                    	obj[13] = calLimitTime(DateUtils.parseDate(obj[10].toString()));
					}else if (2==level.intValue() && "3".equals(obj[9].toString()) && null!=obj[11]) {//复审-待审核
						obj[13] = calLimitTime(DateUtils.parseDate(obj[11].toString()));
					}else if (3==level.intValue() && "5".equals(obj[9].toString()) && null!=obj[12]) {//终审-待审核
						obj[13] = calLimitTime(DateUtils.parseDate(obj[12].toString()));
					}
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
 	 * <p>方法描述：计算处理期限</p>
 	 * @MethodAuthor qrr,2021年10月26日,calLimitTime
     * */
	private int calLimitTime(Date sDate) {
		if (null==sDate) {
			return -1;
		}
		//剩余天数
		int day = HolidayUtil.calRemainingDate(sDate, new Date(),limitTime);
		if (day == 0) {
			return -1;
		}
		return day;
	}

    /**
     *  <p>方法描述：报告卡类型</p>
     * @MethodAuthor hsj
     */
    public Integer getCardType() {
        return 1;
    }
    @Override
    public void searchAction() {

        super.searchAction();
    }

    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder();
        sb.append(" FROM TD_ZW_HETH_CHK_SMARY T ");
        sb.append(" INNER JOIN TS_ZONE T1 on T.EMP_ZONE_ID=T1.RID ");
        sb.append(" INNER JOIN TD_ZW_BGK_LAST_STA T2 on T.rid=T2.BUS_ID and T2.CART_TYPE=1 ");
        sb.append(" INNER JOIN TS_UNIT T3 on T.FILL_SYS_UNIT_ID = T3.RID ");
        sb.append(" WHERE 1=1  ");
        //用工单位地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sb.append(" AND T1.ZONE_GB LIKE :zonecode escape '\\\'");
            this.paramMap.put("zonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        //用工单位名称
        if (StringUtils.isNotBlank(this.searchUnitName)) {
            sb.append(" AND T.EMP_CRPT_NAME LIKE :unitName escape '\\\'");
            this.paramMap.put("unitName", "%" + StringUtils.convertBFH(this.searchUnitName.trim()) + "%");
        }
        //用人单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sb.append(" AND T.CRPT_NAME LIKE :crptName escape '\\\'");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        //报告单位
        if (StringUtils.isNotBlank(this.fillSysUnitIds)) {
            //弹出框已控制最大1000
            sb.append(" AND T.FILL_SYS_UNIT_ID in (").append(this.fillSysUnitIds).append(")");
        }
        //报告日期
        if (null != this.searchRptBdate) {
            sb.append(" AND T.RPT_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRptBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchRptEdate) {
            sb.append(" AND T.RPT_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRptEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != states && states.length > 0) {
            sb.append(" AND T2.STATE IN (:list)");
            this.paramMap.put("list", Arrays.asList(states));
        }
        //接收日期
        if (1==this.level.intValue()) {//初审
        	if (null != this.searchRcvBdate) {
            	sb.append(" AND T2.COUNTY_RCV_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRcvBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
    		}
    		if (null != this.searchRcvEdate) {
    			sb.append(" AND T2.COUNTY_RCV_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRcvEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
    		}
		}else if (2==this.level.intValue()) {//复审+市直属初审
			if (null != this.searchRcvBdate) {
            	sb.append(" AND T2.CITY_RCV_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRcvBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
    		}
    		if (null != this.searchRcvEdate) {
    			sb.append(" AND T2.CITY_RCV_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRcvEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
    		}
		}else if (3==this.level.intValue()) {//终审
			if (null != this.searchRcvBdate) {
            	sb.append(" AND T2.PRO_RCV_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRcvBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
    		}
    		if (null != this.searchRcvEdate) {
    			sb.append(" AND T2.PRO_RCV_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRcvEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
    		}
		}
        //报告卡编码
        if (StringUtils.isNotBlank(this.searchRptNo)) {
            sb.append(" AND T.RPT_NO LIKE :searchRptNo escape '\\\'");
            this.paramMap.put("searchRptNo", "%" + StringUtils.convertBFH(this.searchRptNo.trim()) + "%");
        }
        sb.append(" AND T.DEL_MARK !=1 ");
        String h2 = "SELECT COUNT(*) " + sb.toString();
        StringBuffer searchSql = new StringBuffer();
        searchSql.append("select T.rid, CASE WHEN T1.ZONE_TYPE > 2 THEN SUBSTR( T1.FULL_NAME, INSTR( T1.FULL_NAME, '_' ) + 1 ) ELSE T1.FULL_NAME END ZONE_NAME");
        searchSql.append(",T.EMP_CRPT_NAME,T.EMP_CREDIT_CODE,T.CRPT_NAME,T3.UNITNAME ");
        searchSql.append(",to_char(T.RPT_DATE, 'yyyy-mm-dd'),'' RCV_DATE,'' DEAL_LIMIT,T2.STATE");
        searchSql.append(",T2.COUNTY_RCV_DATE,T2.CITY_RCV_DATE,T2.PRO_RCV_DATE,'' AS LIMIT_DAY, T1.IF_CITY_DIRECT, T.RPT_NO");
        searchSql.append(sb);
        searchSql.append(" ORDER BY T.RPT_DATE DESC, T1.ZONE_CODE,T.EMP_CRPT_NAME,T.CRPT_NAME");
        return new String[] { searchSql.toString(), h2 };
    }


    /**
     * <p>方法描述：批量审核</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-22
     **/
    public void openReviewConfirmDialog() {
        this.checkState = 1;
        if (StringUtils.isNotBlank(this.defaultAuditAdv)) {
            this.checkRst = this.defaultAuditAdv;
        } else {
            this.checkRst = "";
        }
        if (ObjectUtil.isEmpty(this.selectEntitys)) {
            JsfUtil.addErrorMessage("请选择需要审批的数据！");
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ReviewConfirmDialog').show()");
        RequestContext.getCurrentInstance().update("tabView:mainForm:reviewConfirmDialog");
    }

    /**
     * <p>方法描述：批量审核</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-22
     **/
    public void reviewBatchAction() {
        if (null == this.checkState) {
            JsfUtil.addErrorMessage("审核结果不能为空！");
            return;
        }
        boolean passed = new Integer(1).equals(this.checkState);
        if (StringUtils.isBlank(this.checkRst)) {
            JsfUtil.addErrorMessage("审核意见不能为空！");
            return;
        }
        if (this.checkRst.length() > 100) {
            JsfUtil.addErrorMessage("审核意见内容超出长度限制！");
            return;
        }

        for (Object[] obj : this.selectEntitys) {
            TdZwBgkLastSta tdZwBgkLastSta = chkSmaryService.findTdZwBgkLastSta(Integer.valueOf(obj[0].toString()), 1);
            // 报告卡审批流程
            TdZwBgkFlow tdZwBgkFlow = new TdZwBgkFlow();
            tdZwBgkFlow.setRcvDate(new Date());
            tdZwBgkFlow.setFkBySmtPsnId(new TsUserInfo(Global.getUser().getRid()));
            tdZwBgkFlow.setAuditAdv(this.checkRst);
            tdZwBgkFlow.setAuditMan(Global.getUser().getUsername());
            tdZwBgkFlow.setBusId(Integer.valueOf(obj[0].toString()));
            tdZwBgkFlow.setCartType(1);
            if (tdZwBgkLastSta == null) {
                continue;
            }
            if ("2".equals(checkLevel)) {//2级审核
                if ("1".equals(obj[9].toString())) {
                    //报告卡最新状态
                    tdZwBgkLastSta.setState(passed ? 5 : 2);
                    tdZwBgkLastSta.setCountyChkPsn(Global.getUser().getUsername());
                    tdZwBgkLastSta.setCountAuditAdv(this.checkRst);
                    if (passed) {
                        tdZwBgkLastSta.setProRcvDate(new Date());
                    } else {
                        tdZwBgkLastSta.setOrgRcvDate(new Date());
                    }
                    //报告卡审批流程  状态
                    tdZwBgkFlow.setOperFlag(passed ? 43 : 11);
                } else if ("5".equals(obj[9].toString())) {
                    tdZwBgkLastSta.setState(passed ? 7 : 6);
                    tdZwBgkLastSta.setProChkPsn(Global.getUser().getUsername());
                    tdZwBgkLastSta.setProAuditAdv(this.checkRst);
                    if (passed) {
                        tdZwBgkLastSta.setProSmtDate(new Date());
                    } else {
                        tdZwBgkLastSta.setOrgRcvDate(new Date());
                    }
                    //报告卡审批流程  状态
                    tdZwBgkFlow.setOperFlag(passed ? 42 : 32);
                }
            } else if ("3".equals(checkLevel)) {//3级审核
                if ("1".equals(obj[9].toString())) {
                    tdZwBgkLastSta.setState(passed ? 3 : 2);
                    tdZwBgkLastSta.setCountyChkPsn(Global.getUser().getUsername());
                    tdZwBgkLastSta.setCountAuditAdv(this.checkRst);
                    if (passed) {
                        tdZwBgkLastSta.setCityRcvDate(new Date());
                    } else {
                        tdZwBgkLastSta.setOrgRcvDate(new Date());
                    }
                    //报告卡审批流程  状态
                    tdZwBgkFlow.setOperFlag(passed ? 31 : 11);
                } else if ("3".equals(obj[9].toString())) {
                    tdZwBgkLastSta.setState(passed ? 5 : 4);
                    tdZwBgkLastSta.setCityChkPsn(Global.getUser().getUsername());
                    tdZwBgkLastSta.setCityAuditAdv(this.checkRst);
                    if (passed) {
                        tdZwBgkLastSta.setProRcvDate(new Date());
                    } else {
                        tdZwBgkLastSta.setOrgRcvDate(new Date());
                    }
                    //报告卡审批流程  状态
                    //退回 -> 三级审核市直属 13/三级审核非市直属 22
                    boolean cityDirect = "1".equals(StringUtils.objectToString(obj[14]));
                    int operFlag = 41;
                    if (!passed) {
                        operFlag = cityDirect ? 13 : 22;
                    }
                    tdZwBgkFlow.setOperFlag(operFlag);
                } else if ("5".equals(obj[9].toString())) {
                    tdZwBgkLastSta.setState(passed ? 7 : 6);
                    tdZwBgkLastSta.setProChkPsn(Global.getUser().getUsername());
                    tdZwBgkLastSta.setProAuditAdv(this.checkRst);
                    if (passed) {
                        tdZwBgkLastSta.setProSmtDate(new Date());
                    } else {
                        tdZwBgkLastSta.setOrgRcvDate(new Date());
                    }
                    //报告卡审批流程  状态
                    tdZwBgkFlow.setOperFlag(passed ? 42 : 32);
                }
            }
            chkSmaryService.updateInsLastRecord(tdZwBgkLastSta, tdZwBgkFlow, limitTime);
        }
        JsfUtil.addSuccessMessage("审核成功！");
        this.searchAction();
        RequestContext.getCurrentInstance().execute("PF('ReviewConfirmDialog').hide();datatableOffClick();");
        RequestContext.getCurrentInstance().update("tabView:mainForm:dataTable");
    }

    /**
     *  <p>方法描述：报告单位选择弹出框</p>
     * @MethodAuthor hsj 2024-04-25 14:56
     */
    public void selectOrgList() {
        Map<String, Object> options = MapUtils.produceDialogMap(null,720,null,470);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> tmpList = new ArrayList<>();
        tmpList.add("1");
        paramMap.put("checkType", tmpList);
        tmpList = new ArrayList<>();
        tmpList.add(this.fillSysUnitIds);
        paramMap.put("selectIds", tmpList);
        tmpList = new ArrayList<>();
        tmpList.add(this.tsZone.getZoneGb().substring(0,2));
        paramMap.put("searchZoneCode", tmpList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectManyOrgList", options, paramMap);
    }

    /**
     *  <p>方法描述：报告单位选择后</p>
     * @MethodAuthor hsj 2024-04-25 14:59
     */
    public void onOrgSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(selectedMap == null || selectedMap.size() == 0){
            return;
        }
        List<SelectManyOrgVo> results = (List<SelectManyOrgVo>)selectedMap.get("selectPros");
        if(CollectionUtils.isEmpty(results)){
            return;
        }
        clearSelectOrg();
        List<String> ids= new ArrayList<>();
        List<String> names= new ArrayList<>();
        for(SelectManyOrgVo vo : results){
            ids.add(vo.getRid().toString());
            names.add(vo.getUnitName());
        }
        this.fillSysUnitIds = StringUtils.list2string(ids,",");
        this.fillSysUnitNames = StringUtils.list2string(names,"，");
    }
    /**
     *  <p>方法描述：</p>
     * @MethodAuthor hsj 2024-04-25 15:19
     */
    public void clearSelectOrg(){
        this.fillSysUnitIds = null;
        this.fillSysUnitNames = null;
    }
    /**
     *  <p>方法描述：导出数据查询</p>
     * @MethodAuthor hsj 2024-05-28 10:58
     */
    public void preExport(){
        this.exportIds = findTdZwHethChkSmaryListByCondition();
        if (CollectionUtils.isEmpty(this.exportIds)) {
            JsfUtil.addErrorMessage("导出无数据！");
            return;
        }
        RequestContext.getCurrentInstance().execute("downloadFileClick()");
    }

    /**
     *  <p>方法描述：导出数据查询</p>
     * @MethodAuthor hsj 2024-05-28 11:05
     */
    private List<Integer> findTdZwHethChkSmaryListByCondition() {
        this.stateOrAuditMap = new HashMap<>();
        List<Integer> ids = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        Map<String, Object> conditionMap = new HashMap<>();
        sb.append("SELECT T.RID,T2.STATE,T2.ORG_RCV_DATE,T2.COUNTY_RCV_DATE,T2.CITY_RCV_DATE,T2.PRO_RCV_DATE,T2.PRO_SMT_DATE ");
        sb.append(" ,T2.COUNTY_AUDIT_ADV,T2.CITY_AUDIT_ADV,T2.PRO_AUDIT_ADV ");
        sb.append(" FROM TD_ZW_HETH_CHK_SMARY T ");
        sb.append(" INNER JOIN TS_ZONE T1 on T.EMP_ZONE_ID=T1.RID ");
        sb.append(" INNER JOIN TD_ZW_BGK_LAST_STA T2 on T.rid=T2.BUS_ID and T2.CART_TYPE=1 ");
        sb.append(" INNER JOIN TS_UNIT T3 on T.FILL_SYS_UNIT_ID = T3.RID ");
        sb.append(" WHERE T.DEL_MARK !=1 ");
        getCondition(sb,conditionMap);
        sb.append(" ORDER BY T.RPT_DATE DESC, T1.ZONE_CODE,T.EMP_CRPT_NAME,T.CRPT_NAME");
        List<Object[]> list =  this.chkSmaryService.findDataBySqlNoPage(sb.toString(),conditionMap);
        if(CollectionUtils.isEmpty(list)){
            return ids;
        }
        for(Object[] obj:list){
            ids.add(Convert.toInt(obj[0]));
            this.stateOrAuditMap.put(Convert.toInt(obj[0]),obj);
        }
        return ids;
    }
    private void getCondition(StringBuilder sb ,Map<String, Object> conditionMap ) {
        //用工单位地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sb.append(" AND T1.ZONE_GB LIKE :zonecode escape '\\\'");
            conditionMap.put("zonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        //用工单位名称
        if (StringUtils.isNotBlank(this.searchUnitName)) {
            sb.append(" AND T.EMP_CRPT_NAME LIKE :unitName escape '\\\'");
            conditionMap.put("unitName", "%" + StringUtils.convertBFH(this.searchUnitName.trim()) + "%");
        }
        //用人单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sb.append(" AND T.CRPT_NAME LIKE :crptName escape '\\\'");
            conditionMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        //报告单位
        if (StringUtils.isNotBlank(this.fillSysUnitIds)) {
            //弹出框已控制最大1000
            sb.append(" AND T.FILL_SYS_UNIT_ID in (").append(this.fillSysUnitIds).append(")");
        }
        //报告日期
        if (null != this.searchRptBdate) {
            sb.append(" AND T.RPT_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRptBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchRptEdate) {
            sb.append(" AND T.RPT_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRptEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != states && states.length > 0) {
            sb.append(" AND T2.STATE IN (:list)");
            conditionMap.put("list", Arrays.asList(states));
        }
        //接收日期
        if (1==this.level.intValue()) {//初审
            if (null != this.searchRcvBdate) {
                sb.append(" AND T2.COUNTY_RCV_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRcvBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
            }
            if (null != this.searchRcvEdate) {
                sb.append(" AND T2.COUNTY_RCV_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRcvEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
            }
        }else if (2==this.level.intValue()) {//复审+市直属初审
            if (null != this.searchRcvBdate) {
                sb.append(" AND T2.CITY_RCV_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRcvBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
            }
            if (null != this.searchRcvEdate) {
                sb.append(" AND T2.CITY_RCV_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRcvEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
            }
        }else if (3==this.level.intValue()) {//终审
            if (null != this.searchRcvBdate) {
                sb.append(" AND T2.PRO_RCV_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRcvBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
            }
            if (null != this.searchRcvEdate) {
                sb.append(" AND T2.PRO_RCV_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRcvEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
            }
        }
        //报告卡编码
        if (StringUtils.isNotBlank(this.searchRptNo)) {
            sb.append(" AND T.RPT_NO LIKE :searchRptNo escape '\\\'");
            conditionMap.put("searchRptNo", "%" + StringUtils.convertBFH(this.searchRptNo.trim()) + "%");
        }
    }
    /**
     *  <p>方法描述：文件导出-多sheet</p>
     * @MethodAuthor hsj 2024-05-28 11:55
     */
    public StreamedContent getDownloadFile() {
        //数据查询
        List<TdZwHethChkSmaryComm> chkSmaryList =  this.chkSmaryService.findTdZwHethChkSmaryListByIds(this.exportIds);
        ByteArrayOutputStream baos = null;
        try {
            SXSSFWorkbook wBook = new SXSSFWorkbook();

            SXSSFSheet sheet = (SXSSFSheet)wBook.createSheet("职业性有害因素监测用人单位列表");
            this.initXlsxHead(wBook, sheet);
            SXSSFSheet sheet1 = (SXSSFSheet)wBook.createSheet("危害因素监测列表");
            this.initXlsxHead1(wBook, sheet1);
            //居中显示
            CellStyle cellStyle = wBook.createCellStyle();
            cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
            //自动换行
            cellStyle.setWrapText(true);
            cellStyle.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
            customBorderSizeStyle(cellStyle);
            //居左显示
            CellStyle leftStyle = wBook.createCellStyle();
            leftStyle.setAlignment(HSSFCellStyle.ALIGN_LEFT);
            //自动换行
            leftStyle.setWrapText(true);
            leftStyle.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
            customBorderSizeStyle(leftStyle);
            //数据填充
            getDatas(chkSmaryList,wBook,sheet,sheet1,cellStyle,leftStyle);
            baos = new ByteArrayOutputStream();
            wBook.write(baos);
            baos.flush();
            byte[] aa = baos.toByteArray();
            String fileName = new String(("职业性有害因素监测卡" + ".xlsx").getBytes("GBK"),"ISO-8859-1");
            return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
        } catch(IOException e) {
            JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            e.printStackTrace();
        } finally {
            if (baos != null) {
                try {
                    baos.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    /**
     *  <p>方法描述：数据填充</p>
     * @MethodAuthor hsj 2024-05-28 15:22
     */
    private void getDatas(List<TdZwHethChkSmaryComm> chkSmaryList,SXSSFWorkbook wBook, SXSSFSheet sheet, SXSSFSheet sheet1, CellStyle cellStyle, CellStyle leftStyle) {
        Integer i = 1;//表格1的起始行
        Integer j = 1;//表格2的起始行
        for(TdZwHethChkSmaryComm chkSmary : chkSmaryList){
            Row row = sheet.createRow(i);
            int subscript = 0;
            this.createNomalCell(row,cellStyle, subscript++,Convert.toStr(i));
            this.createNomalCell(row,leftStyle, subscript++,chkSmary.getRptNo());
            this.createNomalCell(row,leftStyle, subscript++,chkSmary.getFkByZoneId() == null ? null : chkSmary.getFkByZoneId().getFullName());
            this.createNomalCell(row,leftStyle, subscript++,chkSmary.getCrptName());
            this.createNomalCell(row,cellStyle, subscript++,chkSmary.getCreditCode());
            this.createNomalCell(row,cellStyle, subscript++,chkSmary.getFkByEconomyId() == null ? null : chkSmary.getFkByEconomyId().getCodeName());
            this.createNomalCell(row,cellStyle, subscript++,chkSmary.getFkByIndusTypeId() == null ? null : chkSmary.getFkByIndusTypeId().getCodeName());
            this.createNomalCell(row,cellStyle, subscript++,chkSmary.getFkByCrptSizeId() == null ? null : chkSmary.getFkByCrptSizeId().getCodeName());
            this.createNomalCell(row,leftStyle, subscript++,chkSmary.getAddress());
            this.createNomalCell(row,cellStyle, subscript++,chkSmary.getPostcode());
            this.createNomalCell(row,cellStyle, subscript++,chkSmary.getSafeposition());
            this.createNomalCell(row,cellStyle, subscript++,StringUtils.encryptPhone(chkSmary.getSafephone()));
            this.createNomalCell(row,cellStyle, subscript++,Convert.toStr(chkSmary.getStaffNum()));
            this.createNomalCell(row,cellStyle, subscript++,Convert.toStr(chkSmary.getWorkNum()));
            this.createNomalCell(row,cellStyle, subscript++,Convert.toStr(chkSmary.getTchBadrsnNum()));
            List<TdZwHethChkSubComm> chkSubList = chkSmary.getChkSubList();
            if(!CollectionUtils.isEmpty(this.badrsnTypeList) && !CollectionUtils.isEmpty(this.postList)){
                if(!CollectionUtils.isEmpty(chkSubList)){
                    //根据getFkByTchRsnId分组
                    Map<Integer,List<TdZwHethChkSubComm>> tdZwHethChkSubCommMap = new HashMap<>();
                    String[] methodName = new String[]{"getFkByTchRsnId","getRid"};
                    GroupUtil.listsGroup2Map(chkSubList,tdZwHethChkSubCommMap,TdZwHethChkSubComm.class,methodName);
                    for(TsSimpleCode t : this.badrsnTypeList){
                        if(!tdZwHethChkSubCommMap.containsKey(t.getRid())){
                            for(int k = 0;k<this.postList.size()+1;k++){
                                this.createNomalCell(row,cellStyle, subscript++,null);
                            }
                            continue;
                        }
                        List<TdZwHethChkSubComm> chkSubs = tdZwHethChkSubCommMap.get(t.getRid());
                        Map<Integer,String> map = new HashMap<>();
                        for(TdZwHethChkSubComm chkSub : chkSubs){
                            if(null == chkSub.getFkByOnguardStateid() ){
                                this.createNomalCell(row,cellStyle, subscript++,Convert.toStr(chkSub.getTouchNum()));
                            }else {
                                map.put(chkSub.getFkByOnguardStateid().getRid(),Convert.toStr(chkSub.getNeedChkNum()));
                            }
                        }
                        for(TsSimpleCode post:postList){
                            if(!map.containsKey(post.getRid())){
                                this.createNomalCell(row,cellStyle, subscript++,null);
                                continue;
                            }
                            this.createNomalCell(row,cellStyle, subscript++,map.get(post.getRid()));
                        }
                    }
                }else {
                    int k = this.badrsnTypeList.size()*(this.postList.size()+1);
                    for(int l=0;l<k;l++){
                        this.createNomalCell(row,cellStyle, subscript++,null);
                    }
                }
            }
            this.createNomalCell(row,leftStyle, subscript++,chkSmary.getJcUnitName());
            this.createNomalCell(row,cellStyle, subscript++,chkSmary.getJcUnitCharge());
            this.createNomalCell(row,cellStyle, subscript++,chkSmary.getFillFormPsn());
            this.createNomalCell(row,cellStyle, subscript++,StringUtils.encryptPhone(chkSmary.getFillLink()));
            this.createNomalCell(row,cellStyle, subscript++,chkSmary.getFillDate() == null ? null : DateUtils.formatDate(chkSmary.getFillDate(),"yyyy-MM-dd"));
            this.createNomalCell(row,leftStyle, subscript++,chkSmary.getFkByFillSysUnitId() == null ? null :chkSmary.getFkByFillSysUnitId().getUnitname());
            this.createNomalCell(row,cellStyle, subscript++,chkSmary.getRptDate() == null ? null : DateUtils.formatDate(chkSmary.getRptDate(),"yyyy-MM-dd"));
            boolean flag = this.stateOrAuditMap.containsKey(chkSmary.getRid()) && StringUtils.isNotBlank(Convert.toStr(this.stateOrAuditMap.get(chkSmary.getRid())[1]));
           if(flag){
               //状态不为空
               Object[] obj = this.stateOrAuditMap.get(chkSmary.getRid());
               String str = Convert.toStr(obj[1]);
               String state = dealState(str);
               this.createNomalCell(row,cellStyle, subscript++,state);
               //审核意见
               Integer s = Convert.toInt(str);
               //级别为3且为市直属或者级别为2且为省直属
               String ifCityDirect = chkSmary.getFkByEmpCrptId().getTsZoneByZoneId().getIfCityDirect();
               String ifProvDirect = chkSmary.getFkByEmpCrptId().getTsZoneByZoneId().getIfProvDirect();
               boolean f = "3".equals(checkLevel) && "1".equals(ifCityDirect) || ("2".equals(checkLevel) && "1".equals(ifProvDirect));
               if(f || s < 2){
                   //区县级不显示
                   this.createNomalCell(row,cellStyle, subscript++,null);
                   this.createNomalCell(row,leftStyle, subscript++,null);
               }else {
                    //显示
                   if(s == 2){
                       this.createNomalCell(row,cellStyle, subscript++, obj[2] == null ? null : DateUtils.formatDate(DateUtils.parseDate(obj[2]),"yyyy-MM-dd"));
                   }else {
                       if("3".equals(checkLevel)){
                           this.createNomalCell(row,cellStyle, subscript++,obj[4] == null ? null : DateUtils.formatDate(DateUtils.parseDate(obj[4]),"yyyy-MM-dd"));
                       }else{
                           this.createNomalCell(row,cellStyle, subscript++,obj[5] == null ? null : DateUtils.formatDate(DateUtils.parseDate(obj[5]),"yyyy-MM-dd"));
                       }
                   }
                   this.createNomalCell(row,leftStyle, subscript++,Convert.toStr(obj[7]));
               }
               if("3".equals(checkLevel)){
                   if(s < 4){
                       this.createNomalCell(row,cellStyle, subscript++,null);
                       this.createNomalCell(row,leftStyle, subscript++,null);
                   }else {
                       if(s == 4){
                           this.createNomalCell(row,cellStyle, subscript++,obj[2] == null ? null : DateUtils.formatDate(DateUtils.parseDate(obj[2]),"yyyy-MM-dd"));
                       }else {
                           this.createNomalCell(row,cellStyle, subscript++,obj[5] == null ? null : DateUtils.formatDate(DateUtils.parseDate(obj[5]),"yyyy-MM-dd"));
                       }
                       this.createNomalCell(row,leftStyle, subscript++,Convert.toStr(obj[8]));
                   }
               }
               if(s < 6){
                   this.createNomalCell(row,cellStyle, subscript++,null);
                   this.createNomalCell(row,leftStyle, subscript++,null);
               }else {
                   if(s == 6){
                       this.createNomalCell(row,cellStyle, subscript++,obj[2] == null ? null : DateUtils.formatDate(DateUtils.parseDate(obj[2]),"yyyy-MM-dd"));
                   }else {
                       this.createNomalCell(row,cellStyle, subscript++,obj[6] == null ? null : DateUtils.formatDate(DateUtils.parseDate(obj[6]),"yyyy-MM-dd"));
                   }
                   this.createNomalCell(row,leftStyle, subscript++,Convert.toStr(obj[9]));
               }
           }else {
               int k = "3".equals(checkLevel) ? 7 : 5;
               for(int l=0;l<k;l++){
                   this.createNomalCell(row,cellStyle, subscript++,null);
               }
           }
           this.createNomalCell(row,leftStyle, subscript,chkSmary.getRmk());
            i++;
            //危害因素监测列表
            List<TdZwHethJcSubComm> jcSubList = chkSmary.getJcSubList();
            if(CollectionUtils.isEmpty(jcSubList)){
                continue;
            }
            //根据rid 排序
            Collections.sort(jcSubList, new Comparator<TdZwHethJcSubComm>() {
                @Override
                public int compare(TdZwHethJcSubComm o1, TdZwHethJcSubComm o2) {
                    return o1.getRid().compareTo(o2.getRid());
                }
            });
            for(TdZwHethJcSubComm jcSub : jcSubList){
                Row row1 = sheet1.createRow(j);
                int subscript1 = 0;
                this.createNomalCell(row1,cellStyle,subscript1++,Convert.toStr(j));
                this.createNomalCell(row1,leftStyle,subscript1++,chkSmary.getRptNo());
                this.createNomalCell(row1,leftStyle,subscript1++,jcSub.getFkByBadrsnId() == null ? null :jcSub.getFkByBadrsnId().getCodeName());
                this.createNomalCell(row1,leftStyle,subscript1++,jcSub.getWorkPlace());
                this.createNomalCell(row1,leftStyle,subscript1++,jcSub.getWorkType());
                this.createNomalCell(row1,leftStyle,subscript1++,jcSub.getFkByThickTypeId() == null ? null : jcSub.getFkByThickTypeId().getCodeName());
                this.createNomalCell(row1,leftStyle,subscript1++,jcSub.getJcValMin());
                this.createNomalCell(row1,leftStyle,subscript1++,jcSub.getJcValMax());
                this.createNomalCell(row1,cellStyle,subscript1++,jcSub.getJcTime() == null ? null :  DateUtils.formatDate(jcSub.getJcTime(),"yyyy-MM-dd"));
                this.createNomalCell(row1,cellStyle,subscript1,jcSub.getHgFlag() == null ? null : (jcSub.getHgFlag() == 0 ? "不合格" : "合格"));
                j++;
            }
        }
    }
    /**
     *  <p>方法描述：状态的处理</p>
     * @MethodAuthor hsj 2024-05-29 9:24
     */
    private String dealState(String str) {
        String state=null;
        switch (str){
            case "0":
                state = "待提交";
                break;
            case "1":
                state = "区县级待审";
                break;
            case "2":
                state = "区县级退回";
                break;
            case "3":
                state = "3".equals(checkLevel) ? "市级待审" : null;
                break;
            case "4":
                state = "3".equals(checkLevel) ? "市级退回" : null;
                break;
            case "5":
                state = ("2".equals(checkLevel) && "1".equals(platVersion)) ? "市级待审" : "省级待审";
                break;
            case "6":
                state = ("2".equals(checkLevel) && "1".equals(platVersion)) ? "市级退回" : "省级退回";
                break;
            case "7":
                state = ("2".equals(checkLevel) && "1".equals(platVersion)) ? "市级通过": "省级通过";
                break;
            default:
                break;
        }
        return state;
    }

    /**
     * @Description: 创建数据单元格
     *
     * @MethodAuthor pw,2022年03月9日
     */
    private void createNomalCell(Row row,
                                 CellStyle cellStyle, int columnIndex, String cellValue){
        Cell cell = row.createCell(columnIndex);
        cell.setCellValue(cellValue);
        cell.setCellStyle(cellStyle);
    }
    /**
     *  <p>方法描述：职业性有害因素监测用人单位列表</p>
     * @MethodAuthor hsj 2024-05-28 13:42
     */
    private void initXlsxHead(SXSSFWorkbook wBook, SXSSFSheet sheet) {
        // 表头文字格式
        CellStyle headCellStyle = wBook.createCellStyle();
        headCellStyle.setAlignment(XSSFCellStyle.ALIGN_CENTER);
        headCellStyle.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
        Font headFont = wBook.createFont();
        headFont.setFontName("宋体");
        headFont.setFontHeightInPoints((short) 11);
        headFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
        headCellStyle.setFont(headFont);
        customBorderSizeStyle(headCellStyle);
        Row row = sheet.createRow(0);
        row.setHeightInPoints(20);
        String[] name =new String[]{"序号","报告卡编码","用人单位地区","用人单位名称","统一社会信用代码","经济类型","行业类别","企业规模","用人单位地址","用人单位地址邮编","用人单位联系人","用人单位联系人电话","职工总人数","生产工人数","当年接触职业性有害因素作业人数"};
        Integer[] widths =new Integer[]{3000,8000,8000,8000,6000,8000,8000,5000,12000,5000,6000,6000,4000,4000,8000};
        for(int i = 0;i<name.length;i++){
            this.createHeadCell(sheet, row, headCellStyle, i, widths[i],name[i]);
        }
        int i = name.length;
        if(!CollectionUtils.isEmpty(badrsnTypeList) && !CollectionUtils.isEmpty(postList)){
            for(TsSimpleCode t : badrsnTypeList){
                this.createHeadCell(sheet, row, headCellStyle, i, 7000,t.getCodeName());
                i++;
                for(TsSimpleCode t1:postList){
                    this.createHeadCell(sheet, row, headCellStyle, i, 6000,"应检人数："+t1.getCodeName());
                    i++;
                }
            }
        }
        String[] name1 =new String[]{"检测单位名称","检测单位负责人","填报人","填报人联系电话","填卡日期","报告单位","报告日期","审核状态"};
        Integer[] widths1 =new Integer[]{8000,6000,4000,6000,4000,8000,4000,4000};
        for(int j = 0;j<name1.length;j++){
            this.createHeadCell(sheet, row, headCellStyle, j+i, widths1[j],name1[j]);
        }
        i=i+name1.length;
        //审核意见
        this.createHeadCell(sheet, row, headCellStyle, i++, 4000,"区县级审核日期");
        this.createHeadCell(sheet, row, headCellStyle, i++, 8000,"区县级审核意见");
        if("3".equals(checkLevel)){
            this.createHeadCell(sheet, row, headCellStyle, i++, 4000,"市级审核日期");
            this.createHeadCell(sheet, row, headCellStyle, i++, 8000,"市级审核意见");
            this.createHeadCell(sheet, row, headCellStyle, i++, 4000,"省级审核日期");
            this.createHeadCell(sheet, row, headCellStyle, i++, 8000,"省级审核意见");
        }else if("2".equals(checkLevel)) {
            this.createHeadCell(sheet, row, headCellStyle, i++, 4000,"1".equals(platVersion) ? "市级审核日期" : "省级审核日期");
            this.createHeadCell(sheet, row, headCellStyle, i++, 8000,"1".equals(platVersion) ? "市级审核意见" : "省级审核意见");
        }
        //备注
        this.createHeadCell(sheet, row, headCellStyle, i, 8000,"备注");
    }
   /**
    *  <p>方法描述：危害因素监测列表</p>
    * @MethodAuthor hsj 2024-05-28 15:20
    */
    private void initXlsxHead1(SXSSFWorkbook wBook,SXSSFSheet sheet) {
        CellStyle headCellStyle = wBook.createCellStyle();
        headCellStyle.setAlignment(XSSFCellStyle.ALIGN_CENTER);
        headCellStyle.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
        Font headFont = wBook.createFont();
        headFont.setFontName("宋体");
        headFont.setFontHeightInPoints((short) 11);
        headFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
        headCellStyle.setFont(headFont);
        customBorderSizeStyle(headCellStyle);
        Row row = sheet.createRow(0);
        row.setHeightInPoints(20);
        String[] name =new String[]{"序号","报告卡编码","职业病危害因素名称","工作场所名称","岗位/工种","浓/强度类型","检测值（最小值）","检测值（最大值）","检测日期","合格情况"};
        Integer[] widths =new Integer[]{3000,8000,8000,6000,6000,4000,4000,4000,4000,4000};
        for(int i = 0;i<name.length;i++){
            this.createHeadCell(sheet, row, headCellStyle, i, widths[i],name[i]);
        }
    }
    /**
     *  <p>方法描述：创建表头单元格</p>
     * @MethodAuthor hsj 2022-08-12 15:37
     */
    private void createHeadCell(SXSSFSheet sheet, Row row, CellStyle headCellStyle, int columnIndex, int columnWidth, String cellValue){
        Cell cell = row.createCell(columnIndex);
        cell.setCellValue(cellValue);
        cell.setCellStyle(headCellStyle);
        sheet.setColumnWidth(columnIndex, columnWidth);
    }
    /**
     *  <p>方法描述：自定义单元格边框线大小样式</p>
     * @MethodAuthor hsj 2023-03-17 17:29
     */
    private CellStyle customBorderSizeStyle(CellStyle style) {
        if(null == style){
            return null;
        }
        style.setBorderLeft(CellStyle.BORDER_THIN); // 左边框
        style.setBorderRight(CellStyle.BORDER_THIN); // 右边框
        style.setBorderTop(CellStyle.BORDER_THIN); // 上边框
        style.setBorderBottom(CellStyle.BORDER_THIN); // 下边框
        return style;
    }

    /**
     * 撤销
     */
    public void revokeCheck() {
        pakCancelFlag();
        if (!Boolean.TRUE.equals(this.cancelFlag)) {
            JsfUtil.addErrorMessage("数据状态已发生变化！");
            this.ifIsCheckout = 1;
            this.modInitAction();
            this.searchAction();
            RequestContext.getCurrentInstance().update("tabView");
            return;
        }
        this.chkSmaryService.revokeCheck(this.rid, this.cancelState);
        this.ifIsCheckout = 0;
        this.modInitAction();
        this.searchAction();
        RequestContext.getCurrentInstance().update("tabView");
    }

    /**
     * 封装是否可撤销
     */
    public void pakCancelFlag() {
        this.cancelState = null;
        this.cancelFlag = null;
        Map<String, Integer> map = this.chkSmaryService.findHethChkSmaryLastStateByRid(this.rid);
        boolean level2 = new Integer(2).equals(this.level);
        boolean level3 = new Integer(3).equals(this.level);
        boolean checkLevel2 = "2".equals(this.checkLevel);
        boolean checkLevel3 = "3".equals(this.checkLevel);
        if (map.get("state") == null) {
            return;
        }
        boolean state3 = new Integer(3).equals(map.get("state"));
        boolean state5 = new Integer(5).equals(map.get("state"));
        boolean state7 = new Integer(7).equals(map.get("state"));
        boolean city = new Integer(1).equals(map.get("city"));
        boolean prov = new Integer(1).equals(map.get("prov"));
        if (level3) {
            //终审 状态为终审通过 时 可撤销
            this.cancelFlag = state7;
        } else if (level2) {
            //复审 状态为待终审 时 可撤销
            this.cancelFlag = state5;
        } else if (checkLevel3 && !city) {
            //初审（三级非市直属） 状态为待复审 时 可撤销
            this.cancelFlag = state3;
        } else if (checkLevel2 && !prov) {
            //初审（二级非省直属） 状态为待终审 时 可撤销
            this.cancelFlag = state5;
        }
        if (!Boolean.TRUE.equals(this.cancelFlag)) {
            return;
        }
        if (state7) {
            //状态为审核完成，撤回后状态为待终审
            this.cancelState = 5;
        } else if (state5 && checkLevel3) {
            //状态为待终审 && 三级审核，撤回后状态为待复审
            this.cancelState = 3;
        } else if (state5 && !prov) {
            //状态为待终审 && 二级审核 && 非省直属，撤回后状态为待初审
            this.cancelState = 1;
        } else if (state3 && checkLevel3 && !city) {
            //状态为待复审 && 三级审核 && 非市直属，撤回后状态为待初审
            this.cancelState = 1;
        }
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public Date getSearchRptBdate() {
        return searchRptBdate;
    }

    public void setSearchRptBdate(Date searchRptBdate) {
        this.searchRptBdate = searchRptBdate;
    }

    public Date getSearchRptEdate() {
        return searchRptEdate;
    }

    public void setSearchRptEdate(Date searchRptEdate) {
        this.searchRptEdate = searchRptEdate;
    }

    public Date getSearchRcvBdate() {
        return searchRcvBdate;
    }

    public void setSearchRcvBdate(Date searchRcvBdate) {
        this.searchRcvBdate = searchRcvBdate;
    }

    public Date getSearchRcvEdate() {
        return searchRcvEdate;
    }

    public void setSearchRcvEdate(Date searchRcvEdate) {
        this.searchRcvEdate = searchRcvEdate;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public String getSearchRptNo() {
        return searchRptNo;
    }

    public void setSearchRptNo(String searchRptNo) {
        this.searchRptNo = searchRptNo;
    }

    public String getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(String checkLevel) {
        this.checkLevel = checkLevel;
    }

    public Date getToday() {
        return new Date();
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public List<Object[]> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<Object[]> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }

    public String getAllStates() {
        return allStates;
    }

    public void setAllStates(String allStates) {
        this.allStates = allStates;
    }

    public void setToday(Date today) {
        this.today = today;
    }

    public Integer getZoneType() {
        return zoneType;
    }

    public void setZoneType(Integer zoneType) {
        this.zoneType = zoneType;
    }

    public String getTipInfo() {
        return tipInfo;
    }

    public void setTipInfo(String tipInfo) {
        this.tipInfo = tipInfo;
    }

    public TsZone getTsZone() {
        return tsZone;
    }

    public void setTsZone(TsZone tsZone) {
        this.tsZone = tsZone;
    }

    public TdZwBgkLastSta getNewFlow() {
        return newFlow;
    }

    public void setNewFlow(TdZwBgkLastSta newFlow) {
        this.newFlow = newFlow;
    }

    public List<TdZwBgkFlow> getBgkFlows() {
        return bgkFlows;
    }

    public void setBgkFlows(List<TdZwBgkFlow> bgkFlows) {
        this.bgkFlows = bgkFlows;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult;
    }

    public String getDefaultAuditAdv() {
        return defaultAuditAdv;
    }

    public void setDefaultAuditAdv(String defaultAuditAdv) {
        this.defaultAuditAdv = defaultAuditAdv;
    }

    public List<Object[]> getHistoryList() {
        return historyList;
    }

    public void setHistoryList(List<Object[]> historyList) {
        this.historyList = historyList;
    }

    public String getPlatVersion() {
        return platVersion;
    }

    public void setPlatVersion(String platVersion) {
        this.platVersion = platVersion;
    }

    public String getFillSysUnitIds() {
        return fillSysUnitIds;
    }

    public void setFillSysUnitIds(String fillSysUnitIds) {
        this.fillSysUnitIds = fillSysUnitIds;
    }

    public String getFillSysUnitNames() {
        return fillSysUnitNames;
    }

    public void setFillSysUnitNames(String fillSysUnitNames) {
        this.fillSysUnitNames = fillSysUnitNames;
    }

    public Integer getCheckState() {
        return checkState;
    }

    public void setCheckState(Integer checkState) {
        this.checkState = checkState;
    }

    public String getCheckRst() {
        return checkRst;
    }

    public void setCheckRst(String checkRst) {
        this.checkRst = checkRst;
    }

    public List<Integer> getExportIds() {
        return exportIds;
    }

    public void setExportIds(List<Integer> exportIds) {
        this.exportIds = exportIds;
    }

    public void setDownloadFile(StreamedContent downloadFile) {
        this.downloadFile = downloadFile;
    }

    public Boolean getCancelFlag() {
        return cancelFlag;
    }

    public void setCancelFlag(Boolean cancelFlag) {
        this.cancelFlag = cancelFlag;
    }
}
