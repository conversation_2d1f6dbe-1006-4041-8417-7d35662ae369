package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 职业卫生技术服务信息报送卡—技术服务领域
 *
 * <AUTHOR>
 * @createTime 2022-8-22
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_CARD_SERVICE")
@SequenceGenerator(name = "TdZwOcchethCardService", sequenceName = "TD_ZW_OCCHETH_CARD_SERVICE_SEQ", allocationSize = 1)
public class TdZwOcchethCardService implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwOcchethCard fkByMainId;
    private TsSimpleCode fkByServiceAreaId;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    public TdZwOcchethCardService() {
    }

    public TdZwOcchethCardService(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethCardService")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwOcchethCard getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwOcchethCard fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "SERVICE_AREA_ID")
    public TsSimpleCode getFkByServiceAreaId() {
        return fkByServiceAreaId;
    }

    public void setFkByServiceAreaId(TsSimpleCode fkByServiceAreaId) {
        this.fkByServiceAreaId = fkByServiceAreaId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

}