package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-5-12
 */
@Entity
@Table(name = "TD_ZW_BADRSN_ITEM")
@SequenceGenerator(name = "TdZwBadrsnItem", sequenceName = "TD_ZW_BADRSN_ITEM_SEQ", allocationSize = 1)
public class TdZwBadrsnItemComm implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwBadrsnStdComm fkByMainId;
	private TbTjItems fkByItemId;
	private String rmk;
	private Date modifyDate;
	private Integer modifyManid;
	private Date createDate;
	private Integer createManid;
	
	public TdZwBadrsnItemComm() {
	}

	public TdZwBadrsnItemComm(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwBadrsnItem")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwBadrsnStdComm getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwBadrsnStdComm fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_ID")			
	public TbTjItems getFkByItemId() {
		return fkByItemId;
	}

	public void setFkByItemId(TbTjItems fkByItemId) {
		this.fkByItemId = fkByItemId;
	}	
			
	@Column(name = "RMK")	
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
}