package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;

/**
 * 企业信息 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置
 *
 * <AUTHOR>
 * @createDate 2014年9月1日 上午8:47:11
 * @LastModify LuXuekun
 * @ModifyDate 2014年9月1日 上午8:47:11
 *
 * <p>修订内容：新增字段邮政编码</p>
 * @ClassReviser qrr,2018年4月23日,TbTjCrpt
 */
@Entity
@Table(name = "TB_TJ_CRPT")
@SequenceGenerator(name = "TbTjCrptSeq", sequenceName = "TB_TJ_CRPT_SEQ", allocationSize = 1)
public class TbTjCrpt implements java.io.Serializable {

    private static final long serialVersionUID = 1024367960887766745L;
    private Integer rid;
    private TsZone tsZoneByZoneId;
    private TsSimpleCode tsSimpleCodeByEconomyId;
    private TsSimpleCode tsSimpleCodeByCrptSizeId;
    private TsSimpleCode tsSimpleCodeByIndusTypeId;
    private TsZone tsZoneByTownId;
    private String crptName;
    private String address;
    private String phone;
    private String corporateDeputy;
    private Integer workForce;
    private Integer workmanNum;
    private Integer workmistressNum;
    private Integer holdCardMan;
    private String postalcode;
    private String workArea;
    private String registerFund;
    private String institutionCode;
    private String safetyPrincipal;
    private Date filingDate;
    private Date buildDate;
    private String linkman1;
    private String position1;
    private String linkphone1;
    private String linkman2;
    private String oriLinkman2;
    private String position2;
    private String linkphone2;
    private String oriLinkphone2;
    private String safeposition;
    private String safephone;
    private String subjeConn;
    private String enrolAddress;
    private String enrolPostalcode;
    private String occManaOffice;
    private String regCode;
    private Short regMark;
    private Date createDate;
    private Integer createManid;
    private Integer outsourceNum;
	/*private List<TdZwOccdiscase> tdZwOccdiscases = new ArrayList<TdZwOccdiscase>(
			0);
	private List<TdTjBhk> tdTjBhks = new ArrayList<TdTjBhk>(0);*/

    //++
    private String uuid;
    private String tarUuid;

    private String economyName;
    private String crptSizeName;
    private String indusTypeName;
    private String zoneName;
    /**女工数*/
    private Integer femaleStaffNum;
    /**女生产工人数*/
    private Integer femaleWorkNum;
    //邮政编码
    private String postCode;

    private Short interPrcTag;

    private String pym;

    private TsUnit fkByCreateUnitId;

    /**
     * 是否分支机构
     */
    private Short ifSubOrg;
    /**
     * 上级企业
     */
    private TbTjCrpt fkByUpperUnitId;
    /** 创建体检机构ID*/
    private TbTjSrvorg fkByCreateOrgId;
    /**是否刪除-默认为否（0）*/
    private Short delMark;
    /**
     * 不可选择
     */
    private Boolean notSelectable = Boolean.FALSE;
    /**
     * 经营状态
     */
    private Integer operationStatus;
    /**
     * 上级企业名称
     */
    private String upperCrptName;
    /**
     * 修改时间
     */
    private Date modifyDate;
    /**
     * 修改人ID
     */
    private Integer modifyManid;
    /**
     * 是否虚拟社会信用代码
     */
    private Integer ifVirtual;
    /**
     * 单位编码
     */
    private String unitCode;
    /**
     * 主数据时间戳
     */
    private Date masterDataTime;

    public TbTjCrpt() {
    }

    public TbTjCrpt(Integer rid) {
        this.rid = rid;
    }

    public TbTjCrpt(Integer rid, String crptName, String institutionCode,
                        Date createDate, Integer createManid) {
        this.rid = rid;
        this.crptName = crptName;
        this.institutionCode = institutionCode;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    @Id
    @GeneratedValue(generator = "TbTjCrptSeq", strategy = GenerationType.SEQUENCE)
    @Column(name = "RID", unique = true )
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "ZONE_ID")
    public TsZone getTsZoneByZoneId() {
        return this.tsZoneByZoneId;
    }

    public void setTsZoneByZoneId(TsZone tsZoneByZoneId) {
        this.tsZoneByZoneId = tsZoneByZoneId;
    }

    @ManyToOne
    @JoinColumn(name = "ECONOMY_ID")
    public TsSimpleCode getTsSimpleCodeByEconomyId() {
        return this.tsSimpleCodeByEconomyId;
    }

    public void setTsSimpleCodeByEconomyId(TsSimpleCode tsSimpleCodeByEconomyId) {
        this.tsSimpleCodeByEconomyId = tsSimpleCodeByEconomyId;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_SIZE_ID")
    public TsSimpleCode getTsSimpleCodeByCrptSizeId() {
        return this.tsSimpleCodeByCrptSizeId;
    }

    public void setTsSimpleCodeByCrptSizeId(
            TsSimpleCode tsSimpleCodeByCrptSizeId) {
        this.tsSimpleCodeByCrptSizeId = tsSimpleCodeByCrptSizeId;
    }

    @ManyToOne
    @JoinColumn(name = "INDUS_TYPE_ID")
    public TsSimpleCode getTsSimpleCodeByIndusTypeId() {
        return this.tsSimpleCodeByIndusTypeId;
    }

    public void setTsSimpleCodeByIndusTypeId(
            TsSimpleCode tsSimpleCodeByIndusTypeId) {
        this.tsSimpleCodeByIndusTypeId = tsSimpleCodeByIndusTypeId;
    }

    @ManyToOne
    @JoinColumn(name = "TOWN_ID")
    public TsZone getTsZoneByTownId() {
        return this.tsZoneByTownId;
    }

    public void setTsZoneByTownId(TsZone tsZoneByTownId) {
        this.tsZoneByTownId = tsZoneByTownId;
    }

    @Column(name = "CRPT_NAME" , length = 100)
    public String getCrptName() {
        return this.crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    @Column(name = "ADDRESS", length = 200)
    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Column(name = "PHONE", length = 30)
    public String getPhone() {
        return this.phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Column(name = "CORPORATE_DEPUTY", length = 50)
    public String getCorporateDeputy() {
        return this.corporateDeputy;
    }

    public void setCorporateDeputy(String corporateDeputy) {
        this.corporateDeputy = corporateDeputy;
    }

    @Column(name = "WORK_FORCE")
    public Integer getWorkForce() {
        return this.workForce;
    }

    public void setWorkForce(Integer workForce) {
        this.workForce = workForce;
    }

    @Column(name = "WORKMAN_NUM")
    public Integer getWorkmanNum() {
        return this.workmanNum;
    }

    public void setWorkmanNum(Integer workmanNum) {
        this.workmanNum = workmanNum;
    }

    @Column(name = "WORKMISTRESS_NUM")
    public Integer getWorkmistressNum() {
        return this.workmistressNum;
    }

    public void setWorkmistressNum(Integer workmistressNum) {
        this.workmistressNum = workmistressNum;
    }

    @Column(name = "HOLD_CARD_MAN")
    public Integer getHoldCardMan() {
        return this.holdCardMan;
    }

    public void setHoldCardMan(Integer holdCardMan) {
        this.holdCardMan = holdCardMan;
    }

    @Column(name = "POSTALCODE", length = 6)
    public String getPostalcode() {
        return this.postalcode;
    }

    public void setPostalcode(String postalcode) {
        this.postalcode = postalcode;
    }

    @Column(name = "WORK_AREA", length = 50)
    public String getWorkArea() {
        return this.workArea;
    }

    public void setWorkArea(String workArea) {
        this.workArea = workArea;
    }

    @Column(name = "REGISTER_FUND", length = 50)
    public String getRegisterFund() {
        return this.registerFund;
    }

    public void setRegisterFund(String registerFund) {
        this.registerFund = registerFund;
    }

    @Column(name = "INSTITUTION_CODE" , length = 50)
    public String getInstitutionCode() {
        return this.institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    @Column(name = "SAFETY_PRINCIPAL", length = 50)
    public String getSafetyPrincipal() {
        return this.safetyPrincipal;
    }

    public void setSafetyPrincipal(String safetyPrincipal) {
        this.safetyPrincipal = safetyPrincipal;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "FILING_DATE", length = 7)
    public Date getFilingDate() {
        return this.filingDate;
    }

    public void setFilingDate(Date filingDate) {
        this.filingDate = filingDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BUILD_DATE", length = 7)
    public Date getBuildDate() {
        return this.buildDate;
    }

    public void setBuildDate(Date buildDate) {
        this.buildDate = buildDate;
    }

    @Column(name = "LINKMAN1", length = 50)
    public String getLinkman1() {
        return this.linkman1;
    }

    public void setLinkman1(String linkman1) {
        this.linkman1 = linkman1;
    }

    @Column(name = "POSITION1", length = 50)
    public String getPosition1() {
        return this.position1;
    }

    public void setPosition1(String position1) {
        this.position1 = position1;
    }

    @Column(name = "LINKPHONE1", length = 30)
    public String getLinkphone1() {
        return this.linkphone1;
    }

    public void setLinkphone1(String linkphone1) {
        this.linkphone1 = linkphone1;
    }

    @Column(name = "LINKMAN2", length = 50)
    public String getLinkman2() {
        return this.linkman2;
    }

    public void setLinkman2(String linkman2) {
        this.linkman2 = linkman2;
    }

    @Column(name = "POSITION2", length = 50)
    public String getPosition2() {
        return this.position2;
    }

    public void setPosition2(String position2) {
        this.position2 = position2;
    }

    @Column(name = "LINKPHONE2", length = 30)
    public String getLinkphone2() {
        return this.linkphone2;
    }

    public void setLinkphone2(String linkphone2) {
        this.linkphone2 = linkphone2;
    }

    @Column(name = "SAFEPOSITION", length = 50)
    public String getSafeposition() {
        return this.safeposition;
    }

    public void setSafeposition(String safeposition) {
        this.safeposition = safeposition;
    }

    @Column(name = "SAFEPHONE", length = 30)
    public String getSafephone() {
        return this.safephone;
    }

    public void setSafephone(String safephone) {
        this.safephone = safephone;
    }

    @Column(name = "SUBJE_CONN", length = 100)
    public String getSubjeConn() {
        return this.subjeConn;
    }

    public void setSubjeConn(String subjeConn) {
        this.subjeConn = subjeConn;
    }

    @Column(name = "ENROL_ADDRESS", length = 200)
    public String getEnrolAddress() {
        return this.enrolAddress;
    }

    public void setEnrolAddress(String enrolAddress) {
        this.enrolAddress = enrolAddress;
    }

    @Column(name = "ENROL_POSTALCODE", length = 6)
    public String getEnrolPostalcode() {
        return this.enrolPostalcode;
    }

    public void setEnrolPostalcode(String enrolPostalcode) {
        this.enrolPostalcode = enrolPostalcode;
    }

    @Column(name = "OCC_MANA_OFFICE", length = 200)
    public String getOccManaOffice() {
        return this.occManaOffice;
    }

    public void setOccManaOffice(String occManaOffice) {
        this.occManaOffice = occManaOffice;
    }

    @Column(name = "REG_CODE", length = 100)
    public String getRegCode() {
        return this.regCode;
    }

    public void setRegCode(String regCode) {
        this.regCode = regCode;
    }

    @Column(name = "REG_MARK")
    public Short getRegMark() {
        return this.regMark;
    }

    public void setRegMark(Short regMark) {
        this.regMark = regMark;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    /*@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbTjCrpt")
    public List<TdZwOccdiscase> getTdZwOccdiscases() {
        return this.tdZwOccdiscases;
    }

    public void setTdZwOccdiscases(List<TdZwOccdiscase> tdZwOccdiscases) {
        this.tdZwOccdiscases = tdZwOccdiscases;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbTjCrpt")
    public List<TdTjBhk> getTdTjBhks() {
        return this.tdTjBhks;
    }

    public void setTdTjBhks(List<TdTjBhk> tdTjBhks) {
        this.tdTjBhks = tdTjBhks;
    }*/
    @Column(name = "TAR_UUID", length = 50)
    public String getTarUuid() {
        return tarUuid;
    }

    public void setTarUuid(String tarUuid) {
        this.tarUuid = tarUuid;
    }

    @Column(name = "UUID", length = 50)
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Transient
    public String getEconomyName() {
        return economyName;
    }

    public void setEconomyName(String economyName) {
        this.economyName = economyName;
    }

    @Transient
    public String getCrptSizeName() {
        return crptSizeName;
    }

    public void setCrptSizeName(String crptSizeName) {
        this.crptSizeName = crptSizeName;
    }

    @Transient
    public String getIndusTypeName() {
        return indusTypeName;
    }

    public void setIndusTypeName(String indusTypeName) {
        this.indusTypeName = indusTypeName;
    }

    @Transient
    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }
    @Column(name="POSTCODE")
    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    @Column(name="INTER_PRC_TAG")
    public Short getInterPrcTag() {
        return interPrcTag;
    }

    public void setInterPrcTag(Short interPrcTag) {
        this.interPrcTag = interPrcTag;
    }
    @Column(name="FEMALE_STAFF_NUM")
    public Integer getFemaleStaffNum() {
        return femaleStaffNum;
    }

    public void setFemaleStaffNum(Integer femaleStaffNum) {
        this.femaleStaffNum = femaleStaffNum;
    }
    @Column(name="FEMALE_WORK_NUM")
    public Integer getFemaleWorkNum() {
        return femaleWorkNum;
    }

    public void setFemaleWorkNum(Integer femaleWorkNum) {
        this.femaleWorkNum = femaleWorkNum;
    }

    @Column(name="PYM")
    public String getPym() {
        return pym;
    }

    public void setPym(String pym) {
        this.pym = pym;
    }

    @Transient
    public TsUnit getFkByCreateUnitId() {
        return fkByCreateUnitId;
    }

    public void setFkByCreateUnitId(TsUnit fkByCreateUnitId) {
        this.fkByCreateUnitId = fkByCreateUnitId;
    }

    @Column(name="OUTSOURCE_NUM")
    public Integer getOutsourceNum() {
        return outsourceNum;
    }

    public void setOutsourceNum(Integer outsourceNum) {
        this.outsourceNum = outsourceNum;
    }

    @Column(name="IF_SUB_ORG")
    public Short getIfSubOrg() {
        return ifSubOrg;
    }

    public void setIfSubOrg(Short ifSubOrg) {
        this.ifSubOrg = ifSubOrg;
    }

    @ManyToOne
    @JoinColumn(name = "UPPER_UNIT_ID")
    public TbTjCrpt getFkByUpperUnitId() {
        return fkByUpperUnitId;
    }

    public void setFkByUpperUnitId(TbTjCrpt fkByUpperUnitId) {
        this.fkByUpperUnitId = fkByUpperUnitId;
    }

    @ManyToOne
    @JoinColumn(name = "CREATE_ORG_ID")
    public TbTjSrvorg getFkByCreateOrgId() {
        return fkByCreateOrgId;
    }

    public void setFkByCreateOrgId(TbTjSrvorg fkByCreateOrgId) {
        this.fkByCreateOrgId = fkByCreateOrgId;
    }

    @Transient
    public String getOriLinkman2() {
        return oriLinkman2;
    }

    public void setOriLinkman2(String oriLinkman2) {
        this.oriLinkman2 = oriLinkman2;
    }

    @Transient
    public String getOriLinkphone2() {
        return oriLinkphone2;
    }

    public void setOriLinkphone2(String oriLinkphone2) {
        this.oriLinkphone2 = oriLinkphone2;
    }

    @Column(name="DEL_MARK")
    public Short getDelMark() {
        return delMark;
    }

    public void setDelMark(Short delMark) {
        this.delMark = delMark;
    }

    @Transient
    public Boolean getNotSelectable() {
        return notSelectable;
    }

    public void setNotSelectable(Boolean notSelectable) {
        this.notSelectable = notSelectable;
    }

    @Column(name="OPERATION_STATUS")
    public Integer getOperationStatus() {
        return operationStatus;
    }

    public void setOperationStatus(Integer operationStatus) {
        this.operationStatus = operationStatus;
    }

    @Transient
    public String getUpperCrptName() {
        return upperCrptName;
    }

    public void setUpperCrptName(String upperCrptName) {
        this.upperCrptName = upperCrptName;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Column(name = "IF_VIRTUAL")
    public Integer getIfVirtual() {
        return ifVirtual;
    }

    public void setIfVirtual(Integer ifVirtual) {
        this.ifVirtual = ifVirtual;
    }

    @Column(name = "UNIT_CODE")
    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    @Column(name = "MASTER_DATA_TIME")
    public Date getMasterDataTime() {
        return masterDataTime;
    }

    public void setMasterDataTime(Date masterDataTime) {
        this.masterDataTime = masterDataTime;
    }
}
