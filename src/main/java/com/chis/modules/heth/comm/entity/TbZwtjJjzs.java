package com.chis.modules.heth.comm.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 职业健康监护标准禁忌症（GBZ-188）
 *
 * 修改人：LXK 修改时间：2014-09-03 修改停用和发布标记为short<br/>
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 */
@Entity
@Table(name = "TB_ZWTJ_JJZS")
@SequenceGenerator(name = "TbZwtjJjzs_Seq", sequenceName = "TB_ZWTJ_JJZS_SEQ", allocationSize = 1)
public class TbZwtjJjzs implements java.io.Serializable {

    private static final long serialVersionUID = 6037644690059185332L;
    private Integer rid;
    private TsSimpleCode tsSimpleCode;
    private TbZwtjMainstd tbZwtjMainstd;
    private short stopTag;
    private short publishTag;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    // Constructors

    /** default constructor */
    public TbZwtjJjzs() {
    }

    /** minimal constructor */
    public TbZwtjJjzs(Integer rid, TsSimpleCode tsSimpleCode,
                          TbZwtjMainstd tbZwtjMainstd, short stopTag, short publishTag,
                      Date createDate, Integer createManid) {
        this.rid = rid;
        this.tsSimpleCode = tsSimpleCode;
        this.tbZwtjMainstd = tbZwtjMainstd;
        this.stopTag = stopTag;
        this.publishTag = publishTag;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    /** full constructor */
    public TbZwtjJjzs(Integer rid, TsSimpleCode tsSimpleCode,
                          TbZwtjMainstd tbZwtjMainstd, short stopTag, short publishTag,
                      Date createDate, Integer createManid, Date modifyDate,
                      Integer modifyManid) {
        this.rid = rid;
        this.tsSimpleCode = tsSimpleCode;
        this.tbZwtjMainstd = tbZwtjMainstd;
        this.stopTag = stopTag;
        this.publishTag = publishTag;
        this.createDate = createDate;
        this.createManid = createManid;
        this.modifyDate = modifyDate;
        this.modifyManid = modifyManid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbZwtjJjzs_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "CONTRAIND_ID" )
    public TsSimpleCode getTsSimpleCode() {
        return this.tsSimpleCode;
    }

    public void setTsSimpleCode(TsSimpleCode tsSimpleCode) {
        this.tsSimpleCode = tsSimpleCode;
    }

    @ManyToOne
    @JoinColumn(name = "SCHEME_ID" )
    public TbZwtjMainstd getTbZwtjMainstd() {
        return this.tbZwtjMainstd;
    }

    public void setTbZwtjMainstd(TbZwtjMainstd tbZwtjMainstd) {
        this.tbZwtjMainstd = tbZwtjMainstd;
    }

    @Column(name = "STOP_TAG" )
    public short getStopTag() {
        return this.stopTag;
    }

    public void setStopTag(short stopTag) {
        this.stopTag = stopTag;
    }

    @Column(name = "PUBLISH_TAG" )
    public short getPublishTag() {
        return this.publishTag;
    }

    public void setPublishTag(short publishTag) {
        this.publishTag = publishTag;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }
}
