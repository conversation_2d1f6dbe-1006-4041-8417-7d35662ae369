package com.chis.modules.heth.comm.service;


import com.chis.common.utils.DateUtils;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.heth.comm.entity.TdZwDiagorginfoComm;
import com.chis.modules.heth.comm.entity.TdZwOcchethInfoComm;
import com.chis.modules.heth.comm.entity.TdZwSrvorginfoComm;
import com.chis.modules.heth.comm.entity.TdZwTjorginfoComm;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 职业卫生资质申报会话Bean
 *
 * <AUTHOR>
 * @createDate 2015年4月20日
 */
@Service
@Transactional(readOnly = false)
public class ZwIntellReportCommServiceImpl extends AbstractTemplate {

    /**
     * <p>方法描述：根据机构/单位Id、状态查询职业卫生技术服务机构基本信息</p>
     *
     * @MethodAuthor qrr, 2020年2月14日, selectTdZwOcchethInfoByOrgIdAndState
     */
    public List<TdZwOcchethInfoComm> selectTdZwOcchethInfoByOrgIdAndState(Integer orgId, Integer state) {
        if (null != orgId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdZwOcchethInfoComm t ");
            sb.append(" where t.fkByOrgId.rid = ").append(orgId);
            sb.append(" and t.state = ").append(state);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    /**
     * 根据机构/单位Id、状态查询职业病诊断机构资质信息
     *
     * @param orgId
     * @param state
     * @return
     */
    public List<TdZwDiagorginfoComm> selectTdZwDiagorginfoByOrgIdAndState(Integer orgId, Integer state) {
        if (null != orgId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdZwDiagorginfoComm t ");
            sb.append(" where t.tsUnit.rid = ").append(orgId);
            sb.append(" and t.state = ").append(state);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    /**
     * @Description : 根据机构/单位Id、状态查询放射卫生技术服务机构资质信息
     * @MethodAuthor: anjing
     * @Date : 2020/2/17 15:36
     **/
    public List<TdZwSrvorginfoComm> selectTdZwSrvorginfoByOrgIdAndState(Integer orgId, Integer state) {
        if (null != orgId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdZwSrvorginfoComm t ");
            sb.append(" where t.tsUnit.rid = ").append(orgId);
            sb.append(" and t.state = ").append(state);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    /**
     * 根据机构/单位Id、状态查询职业健康检查机构资质信息
     *
     * @param orgId
     * @param state
     * @return
     */
    public List<TdZwTjorginfoComm> selectTdZwTjorginfoByOrgIdAndState(Integer orgId, Integer state) {
        if (null != orgId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdZwTjorginfoComm t ");
            sb.append(" where t.tsUnit.rid = ").append(orgId);
            sb.append(" and t.state = ").append(state);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    @Transactional(readOnly = true)
    public List<Object[]> findExportOccHethList(String searchZoneCode, String orgName, Date rptDateSearchStart, Date rptDateSearchEnd
            , String searchUnitName, String jsfwTreeCode, String manageNo, String searchUnitZoneCode, String[] states) {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        StringBuffer sql = new StringBuffer();
        sql.append("select  t1.MANAGE_NO, t1.CRPT_NAME, CASE WHEN T2.ZONE_TYPE > 2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME, '_') + 1)ELSE T2.FULL_NAME END ZONE_NAME, ");
        sql.append(" t1.CREDIT_CODE, t1.ADDRESS, t5.code_name as hyname, t6.code_name as jjname,t9.code_name as crptSizeName,  t1.LINK_MAN,t1.LINK_PHONE, t7.unitname, t4.code_name, to_char(t1.RPT_DATE,'yyyy-MM-dd'),t1.PROJECT_NAME,t1.RPT_NO ");
        sql.append(",t8.code_name as riskName,case when t1.CHK_WAY_TYPE=1 then '院内体检' when t1.CHK_WAY_TYPE=2 then '外出体检' when t1.CHK_WAY_TYPE=3 then '院内体检，外出体检' end");
        sql.append(" from TD_ZW_OCCHETH_RPT t1 ");
        sql.append(" left join ts_zone t2 on t1.zone_id = t2.rid");
        sql.append(" left join ts_simple_code t4 on t1.SORT_ID = t4.rid ");
        sql.append(" left join ts_simple_code t5 on t1.INDUS_TYPE_ID = t5.rid ");
        sql.append(" left join ts_simple_code t6 on t1.ECONOMY_ID = t6.rid ");
        sql.append(" left join TS_UNIT t7 on t1.UNIT_ID = t7.rid ");
        sql.append(" LEFT JOIN ts_zone t3 ON t3.rid = t7.ZONE_ID ");
        sql.append(" left join ts_simple_code t8 on t1.ZYB_RISK_ID = t8.rid ");
        sql.append(" left join ts_simple_code t9 on t1.CRPT_SIZE_ID = t9.rid ");
        sql.append(" where 1= 1 and nvl(t1.DEL_MARK,0) =0 ");
        if (StringUtils.isNotBlank(searchZoneCode)) {
            sql.append(" and t2.zone_gb like :searchZoneCode");
            paramMap.put("searchZoneCode", ZoneUtil.zoneSelect(searchZoneCode) + "%");
        }
        if (StringUtils.isNotBlank(searchUnitZoneCode)) {
            sql.append(" and t3.zone_gb like :searchUnitZoneCode");
            paramMap.put("searchUnitZoneCode", ZoneUtil.zoneSelect(searchUnitZoneCode) + "%");
        }
        if (StringUtils.isNotBlank(orgName)) {
            sql.append(" and t1.CRPT_NAME like :orgName");
            paramMap.put("orgName", "%" + orgName + "%");
        }

        if (null != rptDateSearchStart) {
            sql.append(" and T1.RPT_DATE >= to_date(:rptDateSearchStart,'YYYY-MM-DD HH24:MI:SS') ");
            paramMap.put("rptDateSearchStart", DateUtils.formatDate(rptDateSearchStart) + " 00:00:00");
        }
        if (null != rptDateSearchEnd) {
            sql.append(" and  T1.RPT_DATE <= to_date(:rptDateSearchEnd,'YYYY-MM-DD HH24:MI:SS') ");
            paramMap.put("rptDateSearchEnd", DateUtils.formatDate(rptDateSearchEnd) + " 23:59:59");
        }
        if (StringUtils.isNotBlank(searchUnitName)) {
            sql.append(" and t7.UNITNAME like :searchUnitName escape '\\\'");
            paramMap.put("searchUnitName", "%" + StringUtils.convertBFH(searchUnitName.trim()) + "%");
        }

        if (StringUtils.isNotEmpty(jsfwTreeCode)) {
            sql.append("and T4.rid in (").append(jsfwTreeCode).append(") ");
        }
        if (null != states && states.length > 0) {
            sql.append(" AND t1.state in (").append(StringUtils.join(states, ",")).append(")");
        }

        if (StringUtils.isNotBlank(manageNo)) {
            sql.append(" and t1.MANAGE_NO like :manageNo");
            paramMap.put("manageNo", "%" + manageNo + "%");
        }
        sql.append(" order by t1.MANAGE_NO desc");

        List<Object[]> list = findDataBySqlNoPage(sql.toString(), paramMap);
        return list;
    }
}
