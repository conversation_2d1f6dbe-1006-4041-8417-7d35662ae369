package com.chis.modules.heth.comm.entity;

import javax.persistence.*;

import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2024-10-14
 */
@Entity
@Table(name = "TD_TJ_CHECK_TASK")
@SequenceGenerator(name = "TdTjCheckTask", sequenceName = "TD_TJ_CHECK_TASK_SEQ", allocationSize = 1)
public class TdTjCheckTask implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TsZone fkByZoneId;
    private String exportCondition;
    private Integer checkRst;
    private String checkAdv;
    private TsUnit fkByCheckUnitId;
    private TsUserInfo fkByCheckRsnId;
    private Date checkDate;
    private BigDecimal totalNum;
    private Integer state;
    private String errorMsg;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private Integer taskType;
    private String errorFilePath;

    public TdTjCheckTask() {
    }

    public TdTjCheckTask(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjCheckTask")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "ZONE_ID")
    public TsZone getFkByZoneId() {
        return fkByZoneId;
    }

    public void setFkByZoneId(TsZone fkByZoneId) {
        this.fkByZoneId = fkByZoneId;
    }

    @Column(name = "EXPORT_CONDITION")
    public String getExportCondition() {
        return exportCondition;
    }

    public void setExportCondition(String exportCondition) {
        this.exportCondition = exportCondition;
    }

    @Column(name = "CHECK_RST")
    public Integer getCheckRst() {
        return checkRst;
    }

    public void setCheckRst(Integer checkRst) {
        this.checkRst = checkRst;
    }

    @Column(name = "CHECK_ADV")
    public String getCheckAdv() {
        return checkAdv;
    }

    public void setCheckAdv(String checkAdv) {
        this.checkAdv = checkAdv;
    }

    @ManyToOne
    @JoinColumn(name = "CHECK_UNIT_ID")
    public TsUnit getFkByCheckUnitId() {
        return fkByCheckUnitId;
    }

    public void setFkByCheckUnitId(TsUnit fkByCheckUnitId) {
        this.fkByCheckUnitId = fkByCheckUnitId;
    }

    @ManyToOne
    @JoinColumn(name = "CHECK_RSN_ID")
    public TsUserInfo getFkByCheckRsnId() {
        return fkByCheckRsnId;
    }

    public void setFkByCheckRsnId(TsUserInfo fkByCheckRsnId) {
        this.fkByCheckRsnId = fkByCheckRsnId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CHECK_DATE")
    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    @Column(name = "TOTAL_NUM")
    public BigDecimal getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(BigDecimal totalNum) {
        this.totalNum = totalNum;
    }

    @Column(name = "STATE")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Column(name = "ERROR_MSG")
    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Column(name = "TASK_TYPE")
    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    @Column(name = "ERROR_FILE_PATH")
    public String getErrorFilePath() {
        return errorFilePath;
    }

    public void setErrorFilePath(String errorFilePath) {
        this.errorFilePath = errorFilePath;
    }
}