package com.chis.modules.heth.comm.rptvo;

import com.chis.modules.system.rptvo.ZwxFieldDoc;

/**
 *
 * <p>描述：职业性有害因素监测卡</p>
 *
 *  @MethodAuthor: 龚哲,2021/10/22 18:27,HethChkSmaryCommVo
 */
public class HethChkSmaryCommVo {
	@ZwxFieldDoc(value = "报告年份")
	private String rptYear;

	@ZwxFieldDoc(value = "用人单位名称")
	private String crptName;

	@ZwxFieldDoc(value = "用人单位社会信用代码")
	private String creditCode;

	@ZwxFieldDoc(value = "用人单位经济类型")
	private String economyName;

	@ZwxFieldDoc(value = "用人单位行业")
	private String indusTypeName;

	@ZwxFieldDoc(value = "用人单位企业规模")
	private String crptSizeName;

	@ZwxFieldDoc(value = "用人单位地区")
	private String zoneName;

	@ZwxFieldDoc(value = "用人单位地址")
	private String address;

	@ZwxFieldDoc(value = "用人单位地址邮编")
	private String postCode;

	@ZwxFieldDoc(value = "用人单位联系人")
	private String safePosition;

	@ZwxFieldDoc(value = "用人单位联系人电话")
	private String safePhone;

	@ZwxFieldDoc(value = "用人单位职工总人数")
	private String staffNum;

	@ZwxFieldDoc(value = "用人单位生产工人数")
	private String workNum;

	@ZwxFieldDoc(value = "用工单位名称")
	private String empCrptName;

	@ZwxFieldDoc(value = "用工单位社会信用代码")
	private String empCreditCode;

	@ZwxFieldDoc(value = "用工单位经济类型")
	private String empEconomyName;

	@ZwxFieldDoc(value = "用工单位行业")
	private String empIndusTypeName;

	@ZwxFieldDoc(value = "用工单位企业规模")
	private String empCrptSizeName;

	@ZwxFieldDoc(value = "用工单位地区")
	private String empZoneName;

    @ZwxFieldDoc(value = "当年接触职业性有害因素作业人数")
    private String tchBadrsnNum;

	@ZwxFieldDoc(value = "检测单位")
	private String jcUnitName;

	@ZwxFieldDoc(value = "检测单位负责人")
	private String jcUnitCharge;

	@ZwxFieldDoc(value = "填表单位")
	private String fillUnitName;

	@ZwxFieldDoc(value = "填表人")
	private String fillFormPsn;

	@ZwxFieldDoc(value = "填表人联系电话")
	private String fillLink;

	@ZwxFieldDoc(value = "填表日期")
	private String fillDate;

	@ZwxFieldDoc(value = "报告单位")
	private String rptUnitName;

	@ZwxFieldDoc(value = "报告人")
	private String rptPsn;

	@ZwxFieldDoc(value = "报告人联系电话")
	private String rptLink;

	@ZwxFieldDoc(value = "报告日期")
	private String rptDate;

	@ZwxFieldDoc(value = "备注")
	private String rmk;

	public String getRptYear() {
		return rptYear;
	}

	public void setRptYear(String rptYear) {
		this.rptYear = rptYear;
	}

	public String getCrptName() {
		return crptName;
	}

	public void setCrptName(String crptName) {
		this.crptName = crptName;
	}

	public String getCreditCode() {
		return creditCode;
	}

	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}

	public String getEconomyName() {
		return economyName;
	}

	public void setEconomyName(String economyName) {
		this.economyName = economyName;
	}

	public String getIndusTypeName() {
		return indusTypeName;
	}

	public void setIndusTypeName(String indusTypeName) {
		this.indusTypeName = indusTypeName;
	}

	public String getCrptSizeName() {
		return crptSizeName;
	}

	public void setCrptSizeName(String crptSizeName) {
		this.crptSizeName = crptSizeName;
	}

	public String getZoneName() {
		return zoneName;
	}

	public void setZoneName(String zoneName) {
		this.zoneName = zoneName;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getPostCode() {
		return postCode;
	}

	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}

	public String getSafePosition() {
		return safePosition;
	}

	public void setSafePosition(String safePosition) {
		this.safePosition = safePosition;
	}

	public String getSafePhone() {
		return safePhone;
	}

	public void setSafePhone(String safePhone) {
		this.safePhone = safePhone;
	}

	public String getStaffNum() {
		return staffNum;
	}

	public void setStaffNum(String staffNum) {
		this.staffNum = staffNum;
	}

	public String getWorkNum() {
		return workNum;
	}

	public void setWorkNum(String workNum) {
		this.workNum = workNum;
	}

	public String getEmpCrptName() {
		return empCrptName;
	}

	public void setEmpCrptName(String empCrptName) {
		this.empCrptName = empCrptName;
	}

	public String getEmpCreditCode() {
		return empCreditCode;
	}

	public void setEmpCreditCode(String empCreditCode) {
		this.empCreditCode = empCreditCode;
	}

	public String getEmpEconomyName() {
		return empEconomyName;
	}

	public void setEmpEconomyName(String empEconomyName) {
		this.empEconomyName = empEconomyName;
	}

	public String getEmpIndusTypeName() {
		return empIndusTypeName;
	}

	public void setEmpIndusTypeName(String empIndusTypeName) {
		this.empIndusTypeName = empIndusTypeName;
	}

	public String getEmpCrptSizeName() {
		return empCrptSizeName;
	}

	public void setEmpCrptSizeName(String empCrptSizeName) {
		this.empCrptSizeName = empCrptSizeName;
	}

	public String getEmpZoneName() {
		return empZoneName;
	}

	public void setEmpZoneName(String empZoneName) {
		this.empZoneName = empZoneName;
	}

    public String getTchBadrsnNum() {
        return tchBadrsnNum;
    }

    public void setTchBadrsnNum(String tchBadrsnNum) {
        this.tchBadrsnNum = tchBadrsnNum;
    }

    public String getJcUnitName() {
		return jcUnitName;
	}

	public void setJcUnitName(String jcUnitName) {
		this.jcUnitName = jcUnitName;
	}

	public String getJcUnitCharge() {
		return jcUnitCharge;
	}

	public void setJcUnitCharge(String jcUnitCharge) {
		this.jcUnitCharge = jcUnitCharge;
	}

	public String getFillUnitName() {
		return fillUnitName;
	}

	public void setFillUnitName(String fillUnitName) {
		this.fillUnitName = fillUnitName;
	}

	public String getFillFormPsn() {
		return fillFormPsn;
	}

	public void setFillFormPsn(String fillFormPsn) {
		this.fillFormPsn = fillFormPsn;
	}

	public String getFillLink() {
		return fillLink;
	}

	public void setFillLink(String fillLink) {
		this.fillLink = fillLink;
	}

	public String getFillDate() {
		return fillDate;
	}

	public void setFillDate(String fillDate) {
		this.fillDate = fillDate;
	}

	public String getRptUnitName() {
		return rptUnitName;
	}

	public void setRptUnitName(String rptUnitName) {
		this.rptUnitName = rptUnitName;
	}

	public String getRptPsn() {
		return rptPsn;
	}

	public void setRptPsn(String rptPsn) {
		this.rptPsn = rptPsn;
	}

	public String getRptLink() {
		return rptLink;
	}

	public void setRptLink(String rptLink) {
		this.rptLink = rptLink;
	}

	public String getRptDate() {
		return rptDate;
	}

	public void setRptDate(String rptDate) {
		this.rptDate = rptDate;
	}

	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}
}
