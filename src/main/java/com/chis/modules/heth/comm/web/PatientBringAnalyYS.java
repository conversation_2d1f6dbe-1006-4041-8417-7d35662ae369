package com.chis.modules.heth.comm.web;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 演示使用
 * @date 2023年04月18日
 */

@ManagedBean(name="patientBringAnalyYS")
@ViewScoped
public class PatientBringAnalyYS {
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
    private Date searchSDate;
    private Date searchEDate;

    private List<Object[]> dataList;
    private String perPageSize = "20,50,100";


    public PatientBringAnalyYS() {
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        searchZoneCode=tsZone.getZoneCode();
        searchZoneName=tsZone.getZoneName();
        this.zoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "");
        //开始日期默认当年1月1日
        this.searchSDate = DateUtils.getYearFirstDay(new Date());
        //结束日期默认当前日期
        this.searchEDate = new Date();
        dataList=new ArrayList<>();
        this.dataList.add(this.generateData(1, "陕西省", "44","567190","283595","174400","58810","50385"));
        this.dataList.add(this.generateData(1, "宝鸡市", "5","50910","25455","14500","6500","4455"));
        this.dataList.add(this.generateData(1, "咸阳市", "6","73800","36900","18900","8900","9100"));
        this.dataList.add(this.generateData(1, "铜川市", "5","43360","21680","12000","5000","4680"));
        this.dataList.add(this.generateData(1, "渭南市", "8","105600","52800","36800","9500","6500"));
        this.dataList.add(this.generateData(1, "延安市", "7","91720","45860","32000","8560","5300"));
        this.dataList.add(this.generateData(1, "榆林市", "2","24000","12000","6000","3500","2500"));
        this.dataList.add(this.generateData(1, "汉中市", "2","37000","18500","9600","4200","4700"));
        this.dataList.add(this.generateData(1, "安康市", "3","57800","28900","18600","4850","5450"));
        this.dataList.add(this.generateData(1, "商洛市", "6","83000","41500","26000","7800","7700"));
    }

    private Object[] generateData(int num, String data1, String data2, String data3,String data4,String data5,String data6,String data7){
        Object[] objArr = new Object[8];
        objArr[0] = num;
        objArr[1] = data1;
        objArr[2] = data2;
        objArr[3] = data3;
        objArr[4] = data4;
        objArr[5] = data5;
        objArr[6] = data6;
        objArr[7] = data7;
        return objArr;
    }


    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public Date getSearchSDate() {
        return searchSDate;
    }

    public void setSearchSDate(Date searchSDate) {
        this.searchSDate = searchSDate;
    }

    public Date getSearchEDate() {
        return searchEDate;
    }

    public void setSearchEDate(Date searchEDate) {
        this.searchEDate = searchEDate;
    }

    public List<Object[]> getDataList() {
        return dataList;
    }

    public void setDataList(List<Object[]> dataList) {
        this.dataList = dataList;
    }

    public String getPerPageSize() {
        return perPageSize;
    }

    public void setPerPageSize(String perPageSize) {
        this.perPageSize = perPageSize;
    }
}
