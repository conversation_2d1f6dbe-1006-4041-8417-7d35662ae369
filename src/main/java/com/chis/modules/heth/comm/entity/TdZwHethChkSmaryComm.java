package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2019-9-4
 */
@Entity
@Table(name = "TD_ZW_HETH_CHK_SMARY")
@SequenceGenerator(name = "TdZwHethChkSmary", sequenceName = "TD_ZW_HETH_CHK_SMARY_SEQ", allocationSize = 1)
public class TdZwHethChkSmaryComm implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer rptYear;
	private Date bgnBhkDate;
	private Date endBhkDate;
	private TsZone fkByZoneId;
	private TbTjCrpt fkByCrptId;
	private String crptName;
	private String creditCode;
	private String address;
	private String postcode;
	private String safeposition;
	private String safephone;
	private TsSimpleCode fkByEconomyId;
	private TsSimpleCode fkByIndusTypeId;
	private TsSimpleCode fkByCrptSizeId;
	private Integer staffNum;
	private Integer femaleStaffNum;
	private Integer workNum;
	private Integer femaleWorkNum;
	private Integer tchBadrsnNum;
	private Integer femaleTchBadrsnNum;
	private TdZwTjorginfoNew fkByFillUnitId;
	private String unitRespPsn;
	private String fillFormPsn;
	private String fillLink;
	private Date fillDate;
	private Integer state;
	private Integer delMark;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private String annexPath;

	/**用工单位ID20210802*/
	private TbTjCrpt fkByEmpCrptId;
	/**+用工单位地区ID20210802*/
	private TsZone fkByEmpZoneId;
	/**+用工单位名称20210802*/
	private String  empCrptName;
	/**+用工单位社会信用代码20210802*/
	private String  empCreditCode;
	/**+用工单位经济类型ID20210802*/
	private TsSimpleCode fkByEmpEconomyId;
	/**+用工单位行业类别ID20210802*/
	private TsSimpleCode fkByEmpIndusTypeId;
	/**++用工单位企业规模ID20210802*/
	private TsSimpleCode fkByEmpCrptSizeId;
	/**+检测单位名称20210803*/
	private String jcUnitName;
	/**+检测单位负责人20210803*/
	private String jcUnitCharge;
	/**+填报单位ID20211018*/
	private TsUnit fkByFillSysUnitId;
	/**+报告人20211021*/
	private String rptPsn;
	/**+报告人联系电话20211021*/
	private String rptLink;
	/**+报告日期20211021*/
	private Date rptDate;
	/**+备注20211022*/
	private String  rmk;
	/**
	 * 一个周期内是否开展职业性有害因素检测
	 */
	private Integer ifOpenOneWeek;

	private List<TdZwHethChkSubComm> chkSubList = new ArrayList<>();
	private List<TdZwHethJcSubComm> jcSubList = new ArrayList<>();

	/**报告卡编码*/
	private String rptNo;
	
	public TdZwHethChkSmaryComm() {
	}

	public TdZwHethChkSmaryComm(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwHethChkSmary")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "RPT_YEAR")	
	public Integer getRptYear() {
		return rptYear;
	}

	public void setRptYear(Integer rptYear) {
		this.rptYear = rptYear;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "BGN_BHK_DATE")			
	public Date getBgnBhkDate() {
		return bgnBhkDate;
	}

	public void setBgnBhkDate(Date bgnBhkDate) {
		this.bgnBhkDate = bgnBhkDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "END_BHK_DATE")			
	public Date getEndBhkDate() {
		return endBhkDate;
	}

	public void setEndBhkDate(Date endBhkDate) {
		this.endBhkDate = endBhkDate;
	}	
	@ManyToOne
	@JoinColumn(name = "ZONE_ID")
	public TsZone getFkByZoneId() {
		return fkByZoneId;
	}

	public void setFkByZoneId(TsZone fkByZoneId) {
		this.fkByZoneId = fkByZoneId;
	}

	@ManyToOne
	@JoinColumn(name = "CRPT_ID")			
	public TbTjCrpt getFkByCrptId() {
		return fkByCrptId;
	}

	public void setFkByCrptId(TbTjCrpt fkByCrptId) {
		this.fkByCrptId = fkByCrptId;
	}	
			
	@Column(name = "CRPT_NAME")	
	public String getCrptName() {
		return crptName;
	}

	public void setCrptName(String crptName) {
		this.crptName = crptName;
	}	
			
	@Column(name = "CREDIT_CODE")	
	public String getCreditCode() {
		return creditCode;
	}

	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}	
			
	@Column(name = "ADDRESS")	
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}	
			
	@Column(name = "POSTCODE")	
	public String getPostcode() {
		return postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}	
			
	@Column(name = "SAFEPOSITION")	
	public String getSafeposition() {
		return safeposition;
	}

	public void setSafeposition(String safeposition) {
		this.safeposition = safeposition;
	}	
			
	@Column(name = "SAFEPHONE")	
	public String getSafephone() {
		return safephone;
	}

	public void setSafephone(String safephone) {
		this.safephone = safephone;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ECONOMY_ID")			
	public TsSimpleCode getFkByEconomyId() {
		return fkByEconomyId;
	}

	public void setFkByEconomyId(TsSimpleCode fkByEconomyId) {
		this.fkByEconomyId = fkByEconomyId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "INDUS_TYPE_ID")			
	public TsSimpleCode getFkByIndusTypeId() {
		return fkByIndusTypeId;
	}

	public void setFkByIndusTypeId(TsSimpleCode fkByIndusTypeId) {
		this.fkByIndusTypeId = fkByIndusTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_SIZE_ID")			
	public TsSimpleCode getFkByCrptSizeId() {
		return fkByCrptSizeId;
	}

	public void setFkByCrptSizeId(TsSimpleCode fkByCrptSizeId) {
		this.fkByCrptSizeId = fkByCrptSizeId;
	}	
			
	@Column(name = "STAFF_NUM")	
	public Integer getStaffNum() {
		return staffNum;
	}

	public void setStaffNum(Integer staffNum) {
		this.staffNum = staffNum;
	}	
			
	@Column(name = "FEMALE_STAFF_NUM")	
	public Integer getFemaleStaffNum() {
		return femaleStaffNum;
	}

	public void setFemaleStaffNum(Integer femaleStaffNum) {
		this.femaleStaffNum = femaleStaffNum;
	}	
			
	@Column(name = "WORK_NUM")	
	public Integer getWorkNum() {
		return workNum;
	}

	public void setWorkNum(Integer workNum) {
		this.workNum = workNum;
	}	
			
	@Column(name = "FEMALE_WORK_NUM")	
	public Integer getFemaleWorkNum() {
		return femaleWorkNum;
	}

	public void setFemaleWorkNum(Integer femaleWorkNum) {
		this.femaleWorkNum = femaleWorkNum;
	}	
			
	@Column(name = "TCH_BADRSN_NUM")	
	public Integer getTchBadrsnNum() {
		return tchBadrsnNum;
	}

	public void setTchBadrsnNum(Integer tchBadrsnNum) {
		this.tchBadrsnNum = tchBadrsnNum;
	}	
			
	@Column(name = "FEMALE_TCH_BADRSN_NUM")	
	public Integer getFemaleTchBadrsnNum() {
		return femaleTchBadrsnNum;
	}

	public void setFemaleTchBadrsnNum(Integer femaleTchBadrsnNum) {
		this.femaleTchBadrsnNum = femaleTchBadrsnNum;
	}	
			
	@ManyToOne
	@JoinColumn(name = "FILL_UNIT_ID")			
	public TdZwTjorginfoNew getFkByFillUnitId() {
		return fkByFillUnitId;
	}

	public void setFkByFillUnitId(TdZwTjorginfoNew fkByFillUnitId) {
		this.fkByFillUnitId = fkByFillUnitId;
	}	
			
	@Column(name = "UNIT_RESP_PSN")	
	public String getUnitRespPsn() {
		return unitRespPsn;
	}

	public void setUnitRespPsn(String unitRespPsn) {
		this.unitRespPsn = unitRespPsn;
	}	
			
	@Column(name = "FILL_FORM_PSN")	
	public String getFillFormPsn() {
		return fillFormPsn;
	}

	public void setFillFormPsn(String fillFormPsn) {
		this.fillFormPsn = fillFormPsn;
	}	
			
	@Column(name = "FILL_LINK")	
	public String getFillLink() {
		return fillLink;
	}

	public void setFillLink(String fillLink) {
		this.fillLink = fillLink;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "FILL_DATE")			
	public Date getFillDate() {
		return fillDate;
	}

	public void setFillDate(Date fillDate) {
		this.fillDate = fillDate;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Column(name = "DEL_MARK")	
	public Integer getDelMark() {
		return delMark;
	}

	public void setDelMark(Integer delMark) {
		this.delMark = delMark;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwHethChkSubComm> getChkSubList() {
		return chkSubList;
	}

	public void setChkSubList(List<TdZwHethChkSubComm> chkSubList) {
		this.chkSubList = chkSubList;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwHethJcSubComm> getJcSubList() {
		return jcSubList;
	}

	public void setJcSubList(List<TdZwHethJcSubComm> jcSubList) {
		this.jcSubList = jcSubList;
	}
	@Column(name = "ANNEX_PATH")
	public String getAnnexPath() {
		return annexPath;
	}

	public void setAnnexPath(String annexPath) {
		this.annexPath = annexPath;
	}
	@ManyToOne
	@JoinColumn(name = "EMP_CRPT_ID")
	public TbTjCrpt getFkByEmpCrptId() {
		return fkByEmpCrptId;
	}

	public void setFkByEmpCrptId(TbTjCrpt fkByEmpCrptId) {
		this.fkByEmpCrptId = fkByEmpCrptId;
	}
	@ManyToOne
	@JoinColumn(name = "EMP_ZONE_ID")
	public TsZone getFkByEmpZoneId() {
		return fkByEmpZoneId;
	}

	public void setFkByEmpZoneId(TsZone fkByEmpZoneId) {
		this.fkByEmpZoneId = fkByEmpZoneId;
	}
	@Column(name = "EMP_CRPT_NAME")
	public String getEmpCrptName() {
		return empCrptName;
	}

	public void setEmpCrptName(String empCrptName) {
		this.empCrptName = empCrptName;
	}
	@Column(name = "EMP_CREDIT_CODE")
	public String getEmpCreditCode() {
		return empCreditCode;
	}

	public void setEmpCreditCode(String empCreditCode) {
		this.empCreditCode = empCreditCode;
	}
	@ManyToOne
	@JoinColumn(name = "EMP_ECONOMY_ID")
	public TsSimpleCode getFkByEmpEconomyId() {
		return fkByEmpEconomyId;
	}

	public void setFkByEmpEconomyId(TsSimpleCode fkByEmpEconomyId) {
		this.fkByEmpEconomyId = fkByEmpEconomyId;
	}
	@ManyToOne
	@JoinColumn(name = "EMP_INDUS_TYPE_ID")
	public TsSimpleCode getFkByEmpIndusTypeId() {
		return fkByEmpIndusTypeId;
	}

	public void setFkByEmpIndusTypeId(TsSimpleCode fkByEmpIndusTypeId) {
		this.fkByEmpIndusTypeId = fkByEmpIndusTypeId;
	}
	@ManyToOne
	@JoinColumn(name = "EMP_CRPT_SIZE_ID")
	public TsSimpleCode getFkByEmpCrptSizeId() {
		return fkByEmpCrptSizeId;
	}

	public void setFkByEmpCrptSizeId(TsSimpleCode fkByEmpCrptSizeId) {
		this.fkByEmpCrptSizeId = fkByEmpCrptSizeId;
	}
	@Column(name = "JC_UNIT_NAME")
	public String getJcUnitName() {
		return jcUnitName;
	}

	public void setJcUnitName(String jcUnitName) {
		this.jcUnitName = jcUnitName;
	}
	@Column(name = "JC_UNIT_CHARGE")
	public String getJcUnitCharge() {
		return jcUnitCharge;
	}

	public void setJcUnitCharge(String jcUnitCharge) {
		this.jcUnitCharge = jcUnitCharge;
	}
	@ManyToOne
	@JoinColumn(name = "FILL_SYS_UNIT_ID")
	public TsUnit getFkByFillSysUnitId() {
		return fkByFillSysUnitId;
	}

	public void setFkByFillSysUnitId(TsUnit fkByFillSysUnitId) {
		this.fkByFillSysUnitId = fkByFillSysUnitId;
	}
	@Column(name = "RPT_PSN")
	public String getRptPsn() {
		return rptPsn;
	}

	public void setRptPsn(String rptPsn) {
		this.rptPsn = rptPsn;
	}
	@Column(name = "RPT_LINK")
	public String getRptLink() {
		return rptLink;
	}

	public void setRptLink(String rptLink) {
		this.rptLink = rptLink;
	}
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "RPT_DATE")
	public Date getRptDate() {
		return rptDate;
	}

	public void setRptDate(Date rptDate) {
		this.rptDate = rptDate;
	}
	@Column(name = "RMK")
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

	@Column(name = "IF_OPEN_ONE_WEEK")
	public Integer getIfOpenOneWeek() {
		return ifOpenOneWeek;
	}

	public void setIfOpenOneWeek(Integer ifOpenOneWeek) {
		this.ifOpenOneWeek = ifOpenOneWeek;
	}

	@Column(name = "RPT_NO")
	public String getRptNo() {
		return rptNo;
	}

	public void setRptNo(String rptNo) {
		this.rptNo = rptNo;
	}
}