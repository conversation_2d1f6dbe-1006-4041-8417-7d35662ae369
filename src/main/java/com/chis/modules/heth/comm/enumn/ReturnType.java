package com.chis.modules.heth.comm.enumn;

public enum ReturnType {
	SUCCESS_PROCESS("00") {
		public String getTypeCN() {
			return "发送成功";
		}
	},
	UNIT_VERIFY_FAIL("01") {
		public String getTypeCN() {
			return "单位编码或密码验证失败";
		}
	},
	TOKENID_VALID("02") {
		public String getTypeCN() {
			return "验证TokenId失败";
		}
	},
	NO_DATA("03") {
		public String getTypeCN() {
			return "原数据不存在";
		}
	},
	DOC_STYLE_ERROR("04") {
		public String getTypeCN() {
			return "文档格式错误";
		}
	},
	LOGIC_ERROR("05") {
		public String getTypeCN() {
			return "逻辑校验错误";
		}
	},
	PASSWORD_ERROR("06"){
		public String getTypeCN() {
			return "密码错误";
		}
	},
	FORBIDDEN_ERROR("07"){
		public String getTypeCN() {
			return "账号禁用";
		}
	},
	EXCEPTION_PROCESS("99") {
		public String getTypeCN() {
			return "其它异常";
		}
	}

	;

	private final String typeNo;

	ReturnType(String typeNo) {
		this.typeNo = typeNo;
	}

	public String toString() {
		return typeNo;
	}

	public String getTypeNo() {
		return typeNo;
	}

	public abstract String getTypeCN();
}
