package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdZwOccdisCardBadrsn;
import com.chis.modules.heth.comm.entity.TdZwOccdisCardNew;
import com.chis.modules.heth.comm.service.ZwReportCardCommServiceImpl;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.List;

/**
 *  <p>方法描述：职业病报告卡详情基类</p>
 * @MethodAuthor hsj 2023-11-14 10:29
 */
@ManagedBean(name = "cardNewInfoViewBean")
@ViewScoped
public class CardNewInfoViewBean {
    private ZwReportCardCommServiceImpl cardServiceImpl = SpringContextHolder.getBean(ZwReportCardCommServiceImpl.class);

    /**报告卡信息*/
    private TdZwOccdisCardNew disCard = new TdZwOccdisCardNew();
    /**接触的职业性有害因素*/
    private String selectBadRsnNames;
    /**合并症*/
    private String hbzName;
    private Integer cardRid;

    public CardNewInfoViewBean(){
        this.cardRid = Integer.parseInt(JsfUtil.getRequest().getParameter("cardRid"));
        this.initCardNewInfo();
    }
    private void  initCardNewInfo(){
        this.disCard = cardServiceImpl.findTdZwOccdisCardNewByRid(cardRid);
        //接触的职业性有害因素
        this.initOccdisCardBadrsn();
        //接触的职业性有害因素
        this.initHbz();
        this.encrypt();
    }

    /**
     *  <p>方法描述：报告卡信息点加密</p>
     * @MethodAuthor hsj 2023-11-08 17:11
     */
    private void encrypt() {
        //信息点加密
        this.disCard.setIdc(StringUtils.encryptIdc(this.disCard.getIdc()));
        //联系电话
        this.disCard.setLinktel(StringUtils.encryptPhone(this.disCard.getLinktel()));
        // 隐藏联系电话
        this.disCard.setSafephone(StringUtils.encryptPhone(this.disCard.getSafephone()));
        // 填表人联系电话的隐藏
        this.disCard.setFillLink(StringUtils.encryptPhone(this.disCard.getFillLink()));
        // 紧急联系电话的隐藏
        this.disCard.setEmergLinktel(StringUtils.encryptPhone(this.disCard.getEmergLinktel()));
        this.disCard.setRptLink(StringUtils.encryptPhone(this.disCard.getRptLink()));
    }
    /**
     *  <p>方法描述：接触的职业性有害因素</p>
     * @MethodAuthor hsj 2022-05-17 14:15
     */
    private void initOccdisCardBadrsn(){
        this.selectBadRsnNames = null;
        if (null!=disCard.getRid()) {
            List<TdZwOccdisCardBadrsn> list = cardServiceImpl.findEntityListByMainId(TdZwOccdisCardBadrsn.class, disCard.getRid());
            if (!CollectionUtils.isEmpty(list)) {
                StringBuffer badRsnNames = new StringBuffer();
                for (TdZwOccdisCardBadrsn t : list) {
                    badRsnNames.append("，").append(t.getFkByBadrsnId().getCodeName());
                }
                this.selectBadRsnNames = badRsnNames.substring(1);
            }
        }
    }
    /**
     *  <p>方法描述：合并症</p>
     * @MethodAuthor hsj 2022-05-17 14:29
     */
    private void initHbz(){
        this.hbzName = null;
        StringBuilder stringBuilder = new StringBuilder();
        Integer ifTb = this.disCard.getIfTb();
        if (null!=ifTb && ifTb.intValue()==1) {
            stringBuilder.append("，").append("肺结核");
        }
        Integer ifPulInfection = this.disCard.getIfPulInfection();
        if (null!=ifPulInfection && ifPulInfection.intValue()==1) {
            stringBuilder.append("，").append("肺及支气管感染");
        }
        Integer ifThePneum = this.disCard.getIfThePneum();
        if (null!=ifThePneum && ifThePneum.intValue()==1) {
            stringBuilder.append("，").append("自发性气胸");
        }
        Integer ifPulHeart = this.disCard.getIfPulHeart();
        if (null!=ifPulHeart && ifPulHeart.intValue()==1) {
            stringBuilder.append("，").append("肺心病");
        }
        Integer ifLungCancer = this.disCard.getIfLungCancer();
        if (null!=ifLungCancer && ifLungCancer.intValue()==1) {
            stringBuilder.append("，").append("肺癌");
        }
        if(StringUtils.isNotBlank(stringBuilder)){
            this.hbzName = stringBuilder.toString().substring(1,stringBuilder.length());
        }
    }

    public TdZwOccdisCardNew getDisCard() {
        return disCard;
    }

    public void setDisCard(TdZwOccdisCardNew disCard) {
        this.disCard = disCard;
    }

    public String getSelectBadRsnNames() {
        return selectBadRsnNames;
    }

    public void setSelectBadRsnNames(String selectBadRsnNames) {
        this.selectBadRsnNames = selectBadRsnNames;
    }

    public String getHbzName() {
        return hbzName;
    }

    public void setHbzName(String hbzName) {
        this.hbzName = hbzName;
    }

    public Integer getCardRid() {
        return cardRid;
    }

    public void setCardRid(Integer cardRid) {
        this.cardRid = cardRid;
    }

}
