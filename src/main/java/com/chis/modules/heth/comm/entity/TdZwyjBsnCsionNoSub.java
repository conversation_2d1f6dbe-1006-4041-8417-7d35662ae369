package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-10-29
 */
@Entity
@Table(name = "TD_ZWYJ_BSN_CSION_NO_SUB")
@SequenceGenerator(name = "TdZwyjBsnCsionNoSub", sequenceName = "TD_ZWYJ_BSN_CSION_NO_SUB_SEQ", allocationSize = 1)
public class TdZwyjBsnCsionNoSub implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwyjBsnCsionNo fkByMainId;
	private TsSimpleCode fkByBhkrstId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjBsnCsionNoSub() {
	}

	public TdZwyjBsnCsionNoSub(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjBsnCsionNoSub")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwyjBsnCsionNo getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwyjBsnCsionNo fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BHKRST_ID")			
	public TsSimpleCode getFkByBhkrstId() {
		return fkByBhkrstId;
	}

	public void setFkByBhkrstId(TsSimpleCode fkByBhkrstId) {
		this.fkByBhkrstId = fkByBhkrstId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}