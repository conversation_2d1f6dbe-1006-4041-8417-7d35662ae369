package com.chis.modules.heth.comm.web.dialog;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.service.ActiveMonitoringTaskServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.web.FacesBean;
import org.apache.commons.collections.CollectionUtils;
import org.primefaces.context.RequestContext;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  <p>类描述：码表弹出框
 *  主动监测任务维护 模块专用</p>
 * @ClassAuthor hsj 2023-08-14 9:41
 */
@ManagedBean(name = "codeRadioSelectTaskListBean")
@ViewScoped
public class CodeRadioSelectTaskListBean extends FacesBean {

    private static final long serialVersionUID = -738445454536625063L;
	private final ActiveMonitoringTaskServiceImpl activeMonitoringTaskService =SpringContextHolder.getBean(ActiveMonitoringTaskServiceImpl.class);

    /** 名称 或 拼音码*/
    private String searchNamOrPy;
    /** 标题*/
    private String titleName;
    /** 选择的对象*/
    private TsSimpleCode selectPro;
    /** 查询列集合*/
    private List<TsSimpleCode> displayList;
    /** 所有集合*/
    private List<TsSimpleCode> allList;
    private Boolean ifAllSelect = false;
    //码表大类
    private String firstCodeNo;
    private List<TsSimpleCode> firstList;
    //查询条件大类是否显示
    private Boolean ifShowFirstCode = false;
	/**类型*/
	private String type;
	/**行业类别*/
	private String indusTypeId;
	private String postId;
	/**已选择的码表*/
	private String selectIds;

	/**
	 *  <p>方法描述：初始化数据</p>
	 * @MethodAuthor hsj 2023-08-14 14:23
	 */
    public CodeRadioSelectTaskListBean() {
        this.titleName = JsfUtil.getRequest().getParameter("titleName");
		this.selectIds = JsfUtil.getRequest().getParameter("selectIds");//已选择的码表rid，以逗号隔开
		this.type=JsfUtil.getRequest().getParameter("type");
		this.indusTypeId=JsfUtil.getRequest().getParameter("indusTypeId");
		this.postId=JsfUtil.getRequest().getParameter("postId");
		String ifAllSelect = JsfUtil.getRequest().getParameter("ifAllSelect");
		String ifShowFirstCode = JsfUtil.getRequest().getParameter("ifShowFirstCode");
        if (StringUtils.isNotBlank(ifAllSelect)) {
			if ("true".equals(ifAllSelect)) {
				this.ifAllSelect = true;
			}else if ("false".equals(ifAllSelect)) {
				this.ifAllSelect = false;
			}
		}
        if (StringUtils.isNotBlank(ifShowFirstCode)) {
			if ("true".equals(ifShowFirstCode)) {
				this.ifShowFirstCode = true;
			}else if ("false".equals(ifShowFirstCode)) {
				this.ifShowFirstCode = false;
			}
		}
		this.init();
    }

	/**
	 *  <p>方法描述：查询码表数据</p>
	 * @MethodAuthor hsj 2023-08-14 14:23
	 */
    private void init()   {
        this.displayList = new ArrayList<>();
        this.allList = new ArrayList<>();
        this.firstList = new ArrayList<>();
		List<Object[]> list ;
		try {
			list = activeMonitoringTaskService.findSimpleByParamTask(this.selectIds,this.type,this.indusTypeId,this.postId);
		}catch (Exception e){
			e.printStackTrace();
			list = new ArrayList<>();
		}
		if(CollectionUtils.isEmpty(list)){
			return;
		}
		for (Object[] obj : list) {
			TsSimpleCode code = new TsSimpleCode();
			code.setRid(null!=obj[0]?Integer.valueOf(obj[0].toString()):null);
			code.setCodeName(null!=obj[1]?obj[1].toString():null);
			code.setCodeLevelNo(null!=obj[3]?obj[3].toString():null);
			code.setCodeNo(null!=obj[2]?obj[2].toString():null);
			code.setSplsht(null!=obj[4]?obj[4].toString():null);
			code.setExtendS1(null!=obj[5]?obj[5].toString():null);
			code.setCodeDesc(null!=obj[9]?obj[9].toString():null);
			allList.add(code);
		}
        dealCodelevel(allList);
		if(CollectionUtils.isEmpty(allList)){
			return;
		}
		this.displayList.addAll(allList);
		for (TsSimpleCode t : allList) {
			if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
				firstList.add(t);
			}
		}
    }

	/**
	 *  <p>方法描述：处理码表层级关系</p>
	 * @MethodAuthor hsj 2023-08-14 14:23
	 */
	private void dealCodelevel(List<TsSimpleCode> list){
		if(CollectionUtils.isEmpty(list)){
			return;
		}
		for (TsSimpleCode code : list) {
			code.setLevelIndex(StringUtils.countMatches( code.getCodeLevelNo(), ".") + "");
		}
	}

	/**
	 *  <p>方法描述：根据名称、拼音码过滤数据</p>
	 * @MethodAuthor hsj 2023-08-14 14:22
	 */
    public void searchAction() {
        if(CollectionUtils.isEmpty(allList)){
			return;
		}
        this.displayList = new ArrayList<>();
		if(StringUtils.isNotBlank(searchNamOrPy)){
			for(TsSimpleCode t :allList)   {
				String codeName = t.getCodeName()==null?"":t.getCodeName().toString();
				String codePym = t.getSplsht()==null?"":t.getSplsht().toString();
				if(StringUtils.isNotBlank(firstCodeNo))    {
					if (t.getCodeLevelNo().startsWith(firstCodeNo)&&(codeName.indexOf(searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(searchNamOrPy.toUpperCase()) != -1)) {
						this.displayList.add(t);
					}
				}else {
					if (codeName.indexOf(searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(searchNamOrPy.toUpperCase()) != -1) {
						this.displayList.add(t);
					}
				}
			}
		}else{
			//码表大类
			if(StringUtils.isNotBlank(firstCodeNo))    {
				for(TsSimpleCode t :allList)   {
					if (t.getCodeLevelNo().startsWith(firstCodeNo)) {
						this.displayList.add(t);
					}
				}
			}else{
				this.displayList.addAll(allList);
			}
		}
    }

    /**
 	 * <p>方法描述：选择确定方法</p>
 	 * @MethodAuthor qrr,2018年4月9日,selectAction
     */
    public void selectAction() {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("selectPro",selectPro);
        RequestContext.getCurrentInstance().closeDialog(map);
    }
    /**
 	 * <p>方法描述：关闭</p>
 	 * @MethodAuthor qrr,2018年4月9日,dialogClose
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }


    public String getSearchNamOrPy() {
        return searchNamOrPy;
    }

    public void setSearchNamOrPy(String searchNamOrPy) {
        this.searchNamOrPy = searchNamOrPy;
    }

	public String getTitleName() {
		return titleName;
	}

	public void setTitleName(String titleName) {
		this.titleName = titleName;
	}

	public TsSimpleCode getSelectPro() {
		return selectPro;
	}

	public void setSelectPro(TsSimpleCode selectPro) {
		this.selectPro = selectPro;
	}

	public List<TsSimpleCode> getDisplayList() {
		return displayList;
	}

	public void setDisplayList(List<TsSimpleCode> displayList) {
		this.displayList = displayList;
	}
	public Boolean getIfAllSelect() {
		return ifAllSelect;
	}
	public void setIfAllSelect(Boolean ifAllSelect) {
		this.ifAllSelect = ifAllSelect;
	}
	public String getFirstCodeNo() {
		return firstCodeNo;
	}
	public void setFirstCodeNo(String firstCodeNo) {
		this.firstCodeNo = firstCodeNo;
	}
	public Boolean getIfShowFirstCode() {
		return ifShowFirstCode;
	}
	public void setIfShowFirstCode(Boolean ifShowFirstCode) {
		this.ifShowFirstCode = ifShowFirstCode;
	}
	public List<TsSimpleCode> getFirstList() {
		return firstList;
	}
	public void setFirstList(List<TsSimpleCode> firstList) {
		this.firstList = firstList;
	}
}
