package com.chis.modules.heth.comm.json;

import java.io.Serializable;

/**
 * <p>类描述：场所监测 导入 单个返回对象  </p>
 * @ClassAuthor： pw 2023/6/21
 **/
public class CrptUploadRepSingleDTO implements Serializable {
    private static final long serialVersionUID = -1890911648625275457L;
    private String type;
    private String mess;
    private String uuid;
    private Integer rid;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMess() {
        return mess;
    }

    public void setMess(String mess) {
        this.mess = mess;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }
}
