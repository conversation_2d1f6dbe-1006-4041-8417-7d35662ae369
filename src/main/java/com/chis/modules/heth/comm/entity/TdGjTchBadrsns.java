package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2024-8-15
 */
@Entity
@Table(name = "TD_GJ_TCH_BADRSNS")
@SequenceGenerator(name = "TdGjTchBadrsns", sequenceName = "TD_GJ_TCH_BADRSNS_SEQ", allocationSize = 1)
public class TdGjTchBadrsns implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdGjBhk fkByMainId;
    private TsSimpleCode fkByBadrsnId;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    public TdGjTchBadrsns() {
    }

    public TdGjTchBadrsns(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdGjTchBadrsns")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdGjBhk getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdGjBhk fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "BADRSN_ID")
    public TsSimpleCode getFkByBadrsnId() {
        return fkByBadrsnId;
    }

    public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
        this.fkByBadrsnId = fkByBadrsnId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }
	
}