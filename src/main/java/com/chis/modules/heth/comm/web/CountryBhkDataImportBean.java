package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSONObject;
import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;

/**
 *  <p>类描述：国家体检数据导入</p>
 * @ClassAuthor hsj 2022-11-30 9:41
 */
@ManagedBean(name = "countryBhkDataImportBean")
@ViewScoped
public class CountryBhkDataImportBean  extends FacesEditBean {
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    /****查询条件****/
    //用人单位地区
    private List<TsZone> crptZoneList;
    private String searchZoneCode;
    private String searchZoneName;
    //用人单位名称
    private String searchCrptName;
    //社会信用代码
    private String searchCode;
    //姓名
    private String searchPersonName;
    //证件号码
    private String searchIDC;
    //体检日期
    private Date searchStartTime;
    private Date searchEndTime;
    /****查询条件****/
    /*****导入*****/
    //错误数据文件路径
    private String importErrFilePath;
    //导入错误文件下载
    private StreamedContent errorImportFile;
    //导入错误文件名称
    private String uuidFileName;
    private String delUrl;

    /*****导入*****/
    public CountryBhkDataImportBean() {
        ifSQL = true;
        //用人单位地区
        this.crptZoneList = this.commService.findZoneListByGbAndTypeNoNation("", true, "", "");
        //体检日期
        this.searchEndTime = new Date();
        this.searchStartTime = DateUtils.getYearFirstDay(new Date());
        delUrl = PropertyUtils.getValue("delUrl");
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();
        sql.append(" FROM  TD_TJ_BHK T ");
        sql.append(" LEFT JOIN TB_TJ_CRPT T1 ON T.CRPT_ID = T1.RID ");
        sql.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID ");
        sql.append(" LEFT JOIN TB_TJ_SRVORG T3 ON T.BHKORG_ID = T3.RID ");
        sql.append(" WHERE T.DATA_SOURCE =4 ");
        //用人单位地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sql.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%'");
        }
        //用人单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sql.append(" AND T.CRPT_NAME LIKE :searchCrptName escape '\\\'");
            this.paramMap.put("searchCrptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        //社会信用代码
        if (StringUtils.isNotBlank(this.searchCode)) {
            sql.append(" AND T1.INSTITUTION_CODE  =:searchCode");
            this.paramMap.put("searchCode", StringUtils.convertBFH(this.searchCode.trim()));
        }
        //姓名
        if (StringUtils.isNotBlank(this.searchPersonName)) {
            sql.append(" AND T.PERSON_NAME LIKE :searchPersonName escape '\\\'");
            this.paramMap.put("searchPersonName", "%" + StringUtils.convertBFH(this.searchPersonName.trim()) + "%");
        }
        //证件号码
        if (StringUtils.isNotBlank(this.searchIDC)) {
            sql.append(" AND T.IDC LIKE :searchIDC escape '\\\'");
            this.paramMap.put("searchIDC", "%" + StringUtils.convertBFH(this.searchIDC.trim()) + "%");
        }
        //体检日期
        if (null != this.searchStartTime) {
            sql.append(" AND T.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(searchStartTime, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
        }
        if (null != this.searchEndTime) {
            sql.append(" AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(searchEndTime, "yyyy-MM-dd")).append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
        }
        String h2 = new StringBuffer("SELECT COUNT(*) ").append(sql).toString();
        sql.append(" ORDER BY T2.ZONE_GB ,T.CRPT_NAME,T.PERSON_NAME,T.BHK_DATE ASC,T.RID  ");
        StringBuffer h1 = new StringBuffer(" SELECT  CASE WHEN T2.REAL_ZONE_TYPE >2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME,'_')+1) ELSE T2.FULL_NAME END,T.CRPT_NAME,T1.INSTITUTION_CODE,T.BHK_CODE,T.PERSON_NAME,T.IDC,T.BHK_DATE,T3.UNIT_NAME");
        h1.append(sql);
        return new String[]{h1.toString(), h2};
    }

    /**
     * <p>方法描述：上传</p>
     *
     * @MethodAuthor hsj 2022-12-01 11:41
     */
    public void importDataAction(FileUploadEvent event) {
        // 删除历史错误文件
        if (ObjectUtil.isNotEmpty(this.importErrFilePath)) {
            File errorFile = new File(JsfUtil.getAbsolutePath() + this.importErrFilePath);
            if (errorFile.exists()) {
                boolean ignore = errorFile.delete();
            }
        }
        this.importErrFilePath = null;
        String updateFormId = "tabView:mainForm:uploadFileDialog";
        if (event == null || event.getFile() == null) {
            RequestContext.getCurrentInstance().update(updateFormId);
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
            JsfUtil.addErrorMessage("文件上传失败！");
            return;
        }
        try {
            //格式验证
            UploadedFile file = event.getFile();
            String fileName = file.getFileName();// 文件名称
            String contentType = file.getContentType().toLowerCase();
            String errorMsg = FileUtils.veryFile(file.getInputstream(),contentType, fileName, "5");
            if (StringUtils.isNotBlank(errorMsg)) {
                JsfUtil.addErrorMessage(errorMsg);
                RequestContext.getCurrentInstance().update(updateFormId);
                RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').show()");
                return;
            }
            String urlStr = delUrl + "/easyExcel/fileupload";
            String responseStr = HttpRequestUtil.httpRequestByRaw(urlStr, file.getInputstream());
            if (StringUtils.isNotBlank(responseStr)) {
                JSONObject responseJson = JSONObject.parseObject(responseStr);
                Boolean flag = ObjectUtil.convert(Boolean.class, responseJson.get("flag"), false);
                String msg = StringUtils.objectToString(responseJson.get("msg"));
                String errorFileName = StringUtils.objectToString(responseJson.get("errorFile"));
                if (flag) {
                    JsfUtil.addSuccessMessage(msg);
                } else {
                    if (ObjectUtil.isEmpty(msg)) {
                        msg = "导入文件异常！";
                    }
                    JsfUtil.addErrorMessage(msg);
                }
                if (ObjectUtil.isNotEmpty(errorFileName)) {
                    this.importErrFilePath = File.separator + "comm" + File.separator + "temp" + File.separator + errorFileName;
                } else {
                    this.importErrFilePath = "";
                }
            } else {
                JsfUtil.addErrorMessage("导入文件异常！");
            }
            RequestContext.getCurrentInstance().update("tabView:mainForm:buttonsPanel");
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
        } catch (Exception e) {
            e.printStackTrace();
            RequestContext.getCurrentInstance().update(updateFormId);
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
            JsfUtil.addErrorMessage("导入文件异常！");
        }
    }

    public List<TsZone> getCrptZoneList() {
        return crptZoneList;
    }

    public void setCrptZoneList(List<TsZone> crptZoneList) {
        this.crptZoneList = crptZoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchCode() {
        return searchCode;
    }

    public void setSearchCode(String searchCode) {
        this.searchCode = searchCode;
    }

    public String getSearchPersonName() {
        return searchPersonName;
    }

    public void setSearchPersonName(String searchPersonName) {
        this.searchPersonName = searchPersonName;
    }

    public String getSearchIDC() {
        return searchIDC;
    }

    public void setSearchIDC(String searchIDC) {
        this.searchIDC = searchIDC;
    }

    public Date getSearchStartTime() {
        return searchStartTime;
    }

    public void setSearchStartTime(Date searchStartTime) {
        this.searchStartTime = searchStartTime;
    }

    public Date getSearchEndTime() {
        return searchEndTime;
    }

    public void setSearchEndTime(Date searchEndTime) {
        this.searchEndTime = searchEndTime;
    }
    public StreamedContent getErrorImportFile() {
        if (StringUtils.isBlank(this.importErrFilePath)) {
            return null;
        }
        InputStream stream;
        try {
            stream = new FileInputStream(JsfUtil.getAbsolutePath() + this.importErrFilePath);
            String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            String fileName = "国家体检数据导入错误数据.xlsx";
            if (this.importErrFilePath.endsWith(".xls")) {
                contentType = "application/vnd.ms-excel";
                fileName = "国家体检数据导入导入错误数据.xls";
            }
            return new DefaultStreamedContent(stream, contentType, URLEncoder.encode(fileName, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("下载失败!");
        }
        return null;
    }

    public void setErrorImportFile(StreamedContent errorImportFile) {
        this.errorImportFile = errorImportFile;
    }

    public String getImportErrFilePath() {
        return importErrFilePath;
    }

    public void setImportErrFilePath(String importErrFilePath) {
        this.importErrFilePath = importErrFilePath;
    }

    public String getUuidFileName() {
        return uuidFileName;
    }

    public void setUuidFileName(String uuidFileName) {
        this.uuidFileName = uuidFileName;
    }
}
