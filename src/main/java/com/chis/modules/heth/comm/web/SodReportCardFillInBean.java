package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSON;
import com.chis.common.bean.FastReportData;
import com.chis.common.bean.FastReportDataRef;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.json.CrptDialogParamJson;
import com.chis.modules.heth.comm.service.TdZwYszybRptServiceCommImpl;
import com.chis.modules.heth.comm.service.ZwReportCardCommServiceImpl;
import com.chis.modules.heth.comm.utils.RptCardTempCommUtil;
import com.chis.modules.system.entity.TbZwWritsort;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IFastReport;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SysReturnPojo;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import com.chis.modules.system.web.FastReportBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * 疑似职业病报告卡录入
 *
 * <AUTHOR>
 * @date 2022/04/15
 */
@ManagedBean(name = "sodReportCardFillInBean")
@ViewScoped
public class SodReportCardFillInBean extends FacesEditBean implements IProcessData , IFastReport {
    private ZwReportCardCommServiceImpl zwReportCardService = SpringContextHolder.getBean(ZwReportCardCommServiceImpl.class);
    private TdZwYszybRptServiceCommImpl service = SpringContextHolder.getBean(TdZwYszybRptServiceCommImpl.class);
    /**
     * 用户单位
     */
    private TsUnit userUnit;
    /**
     * 查询条件：用工单位地区集合
     */
    private List<TsZone> zoneList = new ArrayList<>();
    /**
     * 查询条件：用工单位地区编码
     */
    private String searchZoneCode;
    /**
     * 查询条件：用工单位地区名称
     */
    private String searchZoneName;
    /**
     * 查询条件：用工单位名称
     */
    private String searchCrptName;
    /**
     * 查询条件：社会信用代码
     */
    private String searchCreditCode;
    /**
     * 查询条件：人员姓名
     */
    private String searchPersonnelName;
    /**
     * 查询条件：证件号码
     */
    private String searchIdc;
    /**
     * 查询条件：疑似职业病名称
     */
    private String searchSodName;
    /**
     * 查询条件：报告日期查询-开始日期
     */
    private Date searchReportBeginDate;
    /**
     * 查询条件：报告日期查询-结束日期
     */
    private Date searchReportEndDate;
    /**
     * 本单位
     */
    private Boolean thisUnit = Boolean.TRUE;
    private String[] searchUnits;
    private List<SelectItem> searchUnitList = new ArrayList<>();
    /**
     * 状态
     */
    private String[] states;
    private List<SelectItem> stateList = new ArrayList<>();
    /**添加修改页面*/
    private TdZwYszybRpt tdZwYszybRpt;
    /**证件类型*/
    private Integer editCardTypeId;
    private List<TsSimpleCode> cardTypeList;
    private Map<Integer, TsSimpleCode> cardTypeMap ;
    private boolean ifIdcAble = Boolean.TRUE;
    /**单位类型*/
    private String crpyType = null;
    private Boolean selEmpCrpt = Boolean.FALSE;
     /**信息来源*/
    private Integer sourceId;
    private Integer sourceLsId;
    private List<TsSimpleCode> sourceList;
    private Map<Integer, TsSimpleCode> sourceMap ;
     /**是否化学中毒*/
    private boolean ifChemical;
     /**诊断机构*/
    private TdZwDiagorginfoComm tjorginfo;
     /**是否工种其他*/
    private boolean ifOtherWork;
    /**报告卡最新状态*/
    private TdZwBgkLastSta tdZwBgkLastSta;
    /**操作rid*/
    private Integer rptRid;
    /**审核级别*/
    private String checkLevel ;
    /**用于显示*/
    private String tchBadrsns;
    private String yszybName;
    /**文书*/
    private TbZwWritsort writsort;
    /**文书报表*/
    private FastReportBean fastReportBean;
    /**是否设计*/
    private String ifDesign;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    public SodReportCardFillInBean() {
        this.init();
        initTdZwYszybRpt();
        this.ifSQL = true;
        this.searchAction();
    }
    /**
     *  <p>方法描述：添加初始化</p>
     * @MethodAuthor hsj 2022/4/15 15:15
     */
    @Override
    public void addInit() {
        //报告卡信息
        tdZwYszybRpt = new TdZwYszybRpt();
        //默认证件类型
        this.ifIdcAble = true;
        if(!CollectionUtils.isEmpty(cardTypeList)){
            editCardTypeId = cardTypeList.get(0).getRid();
            tdZwYszybRpt.setFkByCardTypeId(cardTypeMap.get(editCardTypeId));
            //是否为暂未获取
            if( null != editCardTypeId && "88".equals(cardTypeMap.get(editCardTypeId).getCodeNo())) {
                this.ifIdcAble = false;
            }
        }
        //危害因素
        tdZwYszybRpt.setBadrsnList(new ArrayList<TdZwYszybTchBadrsn>());
        //是否为化学中毒
        ifChemical = false;
        //信息来源,默认读取码表5316中扩展字段1为2
        sourceId = sourceLsId;
        tdZwYszybRpt.setFkBySourceId(sourceMap.get(sourceId));
        //发现单位
        StringBuffer hql = new StringBuffer();
        hql.append("select  t from TdZwDiagorginfoComm t where  t.tsUnit.rid =").append(Global.getUser().getTsUnit().getRid());
        List<TdZwDiagorginfoComm> tdZwTjorginfoComms = commService.findByHql(hql.toString(), TdZwDiagorginfoComm.class);
        tjorginfo = null;
        if(!CollectionUtils.isEmpty(tdZwTjorginfoComms)) {
            tjorginfo = tdZwTjorginfoComms.get(0);
        }
        tdZwYszybRpt.setDiscoveryUnit(tjorginfo == null ? Global.getUser().getTsUnit().getUnitname() : tjorginfo.getOrgName());
        tdZwYszybRpt.setDiscoveryRespPsn(tjorginfo == null ? "" : tjorginfo.getLinkMan());
        //填表人
        tdZwYszybRpt.setFillFormPsn(Global.getUser().getUsername());
        tdZwYszybRpt.setFillLink(Global.getUser().getMbNum());
        tdZwYszybRpt.setFillDate(new Date());
        //报告人
        tdZwYszybRpt.setRptPsn(Global.getUser().getUsername());
        tdZwYszybRpt.setRptLink(Global.getUser().getMbNum());
        tdZwYszybRpt.setRptDate(new Date());
        tdZwYszybRpt.setFkRptUnitId(Global.getUser().getTsUnit());
        tdZwBgkLastSta = new TdZwBgkLastSta();
        this.tdZwBgkLastSta.setCartType(2);
        this.tdZwBgkLastSta.setState(0);

    }

    @Override
    public void viewInit() {
        tdZwYszybRptInit();
    }
    /**
     *  <p>方法描述：根据rid</p>
     * @MethodAuthor hsj 2022/4/16 13:35
     */
    private void tdZwYszybRptInit() {
        this.tdZwYszybRpt = this.commService.find(TdZwYszybRpt.class, this.rptRid);
        String str = "select t from TdZwYszybTchBadrsn t where t.fkByMainId.rid = "+ rptRid + " ORDER BY t.fkByBadrsnId.num,t.fkByBadrsnId.codeLevelNo,t.fkByBadrsnId.codeNo";
        List<TdZwYszybTchBadrsn> list = this.commService.findByHql(str);
        if(CollectionUtils.isEmpty(list)){
            tdZwYszybRpt.setBadrsnList(new ArrayList<TdZwYszybTchBadrsn>());
        }else {
            tdZwYszybRpt.setBadrsnList(list);
            //可能接触的主要职业性有害因
            StringBuilder s = new StringBuilder();
            for(TdZwYszybTchBadrsn tchBadrsn : list){
                s.append("，").append(tchBadrsn.getFkByBadrsnId().getCodeName());
            }
            tdZwYszybRpt.setTchBadrsns(s.substring(1,s.length()));
        }
        this.tdZwYszybRpt.setModifyDate(new Date());
        this.tdZwYszybRpt.setModifyManid(Global.getUser().getRid());
        //证件类型
        editCardTypeId = null !=  tdZwYszybRpt.getFkByCardTypeId() ? tdZwYszybRpt.getFkByCardTypeId().getRid() : null;
        if(null == editCardTypeId || null ==cardTypeMap.get(editCardTypeId) ){
            //已选择的码表被禁用后选择最新的一个
            if(!CollectionUtils.isEmpty(cardTypeList)){
                editCardTypeId = cardTypeList.get(0).getRid();
                tdZwYszybRpt.setFkByCardTypeId(cardTypeMap.get(editCardTypeId));
            }
        }
        //是否为暂未获取
        if( null != editCardTypeId && "88".equals(cardTypeMap.get(editCardTypeId).getCodeNo())) {
            this.ifIdcAble = false;
        }
        //信息来源
        sourceId = null != tdZwYszybRpt.getFkBySourceId() ? tdZwYszybRpt.getFkBySourceId().getRid() : null;
        //是否为化学中毒
        ifChemical = false;
        if(null != tdZwYszybRpt.getFkByOccDiseid() && "1".equals(tdZwYszybRpt.getFkByOccDiseid().getExtendS1())){
            ifChemical = true;
        }
        //工种
        ifOtherWork =false;
        String workName="";
        if(null != tdZwYszybRpt.getFkByWorkTypeId()){
            workName = tdZwYszybRpt.getFkByWorkTypeId().getCodeName();
            if("1".equals(tdZwYszybRpt.getFkByWorkTypeId().getExtendS1())){
                ifOtherWork =true;
            }
        }
        tdZwYszybRpt.setWorkName(workName);
        //疑似职业病
        if(null != tdZwYszybRpt.getFkByOccDiseid() && null != tdZwYszybRpt.getFkByOccDiseid().getRid()){
            tdZwYszybRpt.setYszybName(tdZwYszybRpt.getFkByOccDiseid().getCodeName());
        }
    }

    /**
     *  <p>方法描述：编辑页面</p>
     * @MethodAuthor hsj 2022/4/16 9:21
     */
    @Override
    public void modInit() {
        tdZwYszybRptInit();
        this.selEmpCrpt = this.tdZwYszybRpt.getFkByIndusTypeId() != null
                && "2".equals(this.tdZwYszybRpt.getFkByIndusTypeId().getExtendS1());
        if(StringUtils.isNotBlank(tdZwYszybRpt.getAnnexPath())){
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.update("tabView:editForm");
            currentInstance.execute("disabledInput('true','writ_yszyb')");
        }
        this.tdZwBgkLastSta = this.service.findTdZwBgkLastStaInfoByBusIdAndCardType(rptRid, 2);
    }
    /**
     *  <p>方法描述：暂存</p>
     * @MethodAuthor hsj 2022/4/15 15:35
     */
    @Override
    public void saveAction() {
        try {
            // 若已制作，不走逻辑
            if (StringUtils.isNotBlank(tdZwYszybRpt.getAnnexPath())) {
                JsfUtil.addSuccessMessage("保存成功！");
                return;
            }
            if (veryData()) {
                return;
            }
            if(null == tdZwBgkLastSta.getState() || tdZwBgkLastSta.getState() == 0){
                //初次保存填报接收日期
                tdZwBgkLastSta.setOrgRcvDate(tdZwYszybRpt.getRptDate());
            }
            //数据清空
            cleanTdZwYszybRpt();
            this.zwReportCardService.saveOrUpdateTdZwYszybRpt(tdZwYszybRpt,tdZwBgkLastSta);
            addTdZwYszybRpt();
            JsfUtil.addSuccessMessage("保存成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }
    /**
     *  <p>方法描述：赋值</p>
     * @MethodAuthor hsj 2022/4/22 11:16
     */
    private void addTdZwYszybRpt() {
        //保存成功后赋值，用于页面显示
        tdZwYszybRpt.setTchBadrsns(tchBadrsns);
        tdZwYszybRpt.setYszybName(yszybName);
    }

    /**
     *  <p>方法描述：多余字段清空</p>
     * @MethodAuthor hsj 2022/4/22 11:14
     */
    private void cleanTdZwYszybRpt() {
         tchBadrsns =  tdZwYszybRpt.getTchBadrsns();
         yszybName =  tdZwYszybRpt.getYszybName();
        //保存前清空
        tdZwYszybRpt.setTchBadrsns(null);
        tdZwYszybRpt.setYszybName(null);
    }

    /**
     *  <p>方法描述：处理流程最新状态</p>
     * @MethodAuthor hsj 2022/4/18 10:31
     */
    private void delBgkLastSta() {
        TsZone zone = tdZwYszybRpt.getFkByEmpZoneId();
        if(null == tdZwBgkLastSta.getState() || tdZwBgkLastSta.getState() == 0){
            //初次提交时填报接收日期
            tdZwBgkLastSta.setOrgRcvDate(tdZwYszybRpt.getRptDate());
        }
        if ("1".equals(zone.getIfCityDirect())) {//市直属
            this.tdZwBgkLastSta.setCityRcvDate(new Date());
            this.tdZwBgkLastSta.setState(3);
        } else if ("1".equals(zone.getIfProvDirect())){//省直属
            this.tdZwBgkLastSta.setProRcvDate(new Date());
            this.tdZwBgkLastSta.setState(5);
        }else {
            this.tdZwBgkLastSta.setCountyRcvDate(new Date());
            this.tdZwBgkLastSta.setState(1);
        }
    }

    /**
     *  <p>方法描述：提交</p>
     * @MethodAuthor hsj 2022/4/15 17:17
     */
    public void submitAction() {
        try {
            this.delBgkLastSta();
            //数据清空
            cleanTdZwYszybRpt();
            this.zwReportCardService.submitOrUpdateYszybRpt(tdZwYszybRpt,tdZwBgkLastSta);
            addTdZwYszybRpt();
            this.backAction();
            this.searchAction();
            JsfUtil.addSuccessMessage("提交成功！");
            this.backAction();
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }
    @Override
    public String[] buildHqls() {
        StringBuilder baseSql = new StringBuilder();
        baseSql.append(" SELECT ")
                .append(" T.RID P0, ")
                .append(" CASE WHEN T1.ZONE_TYPE > 2 THEN SUBSTR( T1.FULL_NAME, INSTR( T1.FULL_NAME, '_' ) + 1 ) ELSE T1.FULL_NAME END P1, ")
                .append(" T.EMP_CRPT_NAME P2, ")
                .append(" T.PERSONNEL_NAME P3, ")
                .append(" T.IDC P4, ")
                .append(" T5.CODE_NAME P5, ")
                .append(" T.RPT_DATE P6, ")
                .append(" T2.UNITNAME P7, ")
                .append(" T3.STATE P8, ")
                .append(" T.RPT_UNIT_ID P9, ")
                .append(" 0 P10, ")
                .append(" NVL(T1.IF_CITY_DIRECT, 0) P11 ")
                .append(" FROM TD_ZW_YSZYB_RPT T ")
                .append("   LEFT JOIN TS_ZONE T1 ON T.EMP_ZONE_ID = T1.RID ")
                .append("   LEFT JOIN TS_UNIT T2 ON T.RPT_UNIT_ID = T2.RID ")
                .append("   LEFT JOIN TD_ZW_BGK_LAST_STA T3 ON T3.CART_TYPE = '2' AND T.RID = T3.BUS_ID ")
                .append("   LEFT JOIN TS_SIMPLE_CODE T4 ON T.SOURCE_ID = T4.RID ")
                .append("   LEFT JOIN TS_SIMPLE_CODE T5 ON T.OCC_DISEID = T5.RID ")
                //非标删
                .append(" WHERE NVL(T.DEL_MARK, 0) = 0 ");
        //用工单位地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            baseSql.append("   AND T1.ZONE_GB LIKE :zoneCode escape '\\\' ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(StringUtils.objectToString(this.searchZoneCode)).trim()) + "%");
        }
        //用工单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            baseSql.append("   AND T.EMP_CRPT_NAME LIKE :crptName escape '\\\' ");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchCrptName).trim()) + "%");
        }
        //人员姓名
        if (StringUtils.isNotBlank(this.searchPersonnelName)) {
            baseSql.append("   AND T.PERSONNEL_NAME LIKE :personnelName escape '\\\' ");
            this.paramMap.put("personnelName", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchPersonnelName).trim()) + "%");
        }
        //证件号码
        if (StringUtils.isNotBlank(this.searchIdc)) {
            baseSql.append("   AND T.IDC = :idc ");
            super.paramMap.put("idc", StringUtils.objectToString(this.searchIdc.trim()));
        }
        //疑似职业病名称
        if (StringUtils.isNotBlank(this.searchSodName)) {
            baseSql.append("   AND T5.CODE_NAME LIKE :yszybName escape '\\\' ");
            super.paramMap.put("yszybName", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchSodName).trim()) + "%");
        }
        //报告日期
        if (null != this.searchReportBeginDate) {
            baseSql.append("   AND T.RPT_DATE >= TO_DATE(:searchReportBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            super.paramMap.put("searchReportBeginDate", DateUtils.formatDate(this.searchReportBeginDate) + " 00:00:00");
        }
        if (null != this.searchReportEndDate) {
            baseSql.append("   AND T.RPT_DATE <= TO_DATE(:searchReportEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            super.paramMap.put("searchReportEndDate", DateUtils.formatDate(this.searchReportEndDate) + " 23:59:59");
        }
        //状态
        if (this.thisUnit && null != this.states && this.states.length > 0) {
            //是否查询"待初审"状态
            boolean pendingInitialAudit = false;
            //是否查询"待复审"状态
            boolean pendingReview = false;
            StringBuilder stateSb = new StringBuilder();
            for (String state : states) {
                switch (state) {
                    case "1":
                        stateSb.append(", 0");
                        break;
                    case "2":
                        stateSb.append(", 2, 4, 6");
                        break;
                    case "3":
                        pendingInitialAudit = true;
                        stateSb.append(", 1");
                        break;
                    case "4":
                        pendingReview = true;
                        break;
                    case "5":
                        stateSb.append(", 5");
                        break;
                    case "6":
                        stateSb.append(", 7");
                        break;
                    default:
                        break;
                }
            }
            baseSql.append(" AND ( ");
            if (StringUtils.isNotBlank(stateSb)) {
                baseSql.append(" T3.STATE IN (").append(stateSb.substring(1)).append(") ");
            } else {
                baseSql.append(" 1=0 ");

            }
            if (pendingInitialAudit) {
                baseSql.append(" OR ( T1.IF_CITY_DIRECT = 1 AND T3.STATE = 3 )");
            }
            if (pendingReview) {
                baseSql.append(" OR ( NVL(T1.IF_CITY_DIRECT, 0) = 0 AND T3.STATE = 3 )");
            }
            baseSql.append(" ) ");
        }

        super.paramMap.put("rptUnitId", this.userUnit.getRid());
        if (this.thisUnit) {
            //勾选本单位时查询本单位且不显示信息来源（码表5316）中扩展字段1为1的职业健康检查的数据
            baseSql.append("   AND T.RPT_UNIT_ID = :rptUnitId ");
            baseSql.append("   AND NVL(T4.EXTENDS1, 0) <> 1 ");
        } else {
            //未勾选本单位时根据证件号码查询"状态>0"或"本单位"数据
            baseSql.append("   AND (T3.STATE > 0 OR T.RPT_UNIT_ID = :rptUnitId) ");
        }
        String sql1 = "SELECT * FROM (" + baseSql + ")AA ORDER BY AA.P6 DESC";
        String sql2 = "SELECT COUNT(1) FROM (" + baseSql + ")";
        return new String[]{sql1, sql2};
    }

    @Override
    public void searchAction() {
        if (!this.thisUnit && StringUtils.isBlank(this.searchIdc)) {
            JsfUtil.addErrorMessage("未勾选本单位，证件号码必填！");
            return;
        }
        super.searchAction();

        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update("tabView:mainForm:dataTable");
    }

    @Override
    public void processData(List<?> list) {
        if (null != list && list.size() > 0) {
            List<Object[]> result = (List<Object[]>) list;
            for (Object[] obj : result) {
                if (this.userUnit.getRid() != null &&
                        !this.userUnit.getRid().equals(Integer.parseInt(StringUtils.objectToString(obj[9])))) {
                    obj[8] = "已提交";
                } else {
                    boolean isCityDirect = "1".equals(StringUtils.objectToString(obj[11]));
                    switch (StringUtils.objectToString(obj[8])) {
                        case "0":
                            obj[8] = "待提交";
                            if (this.thisUnit) {
                                obj[10] = "1";
                            }
                            break;
                        case "2":
                        case "4":
                        case "6":
                            obj[8] = "已退回";
                            if (this.thisUnit) {
                                obj[10] = "1";
                            }
                            break;
                        case "1":
                            obj[8] = "待初审";
                            break;
                        case "3":
                            obj[8] = isCityDirect ? "待初审" : "待复审";
                            break;
                        case "5":
                            obj[8] = "待终审";
                            break;
                        case "7":
                            obj[8] = "终审通过";
                            break;
                        default:
                            break;
                    }
                }
            }
        }
    }

    private void init() {
        this.userUnit = Global.getUser().getTsUnit();
        //地区
        TsZone tsZone = this.userUnit.getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = this.userUnit.getTsZone();
        }
        if (null == this.zoneList || this.zoneList.size() <= 0) {
            this.searchZoneCode = tsZone.getZoneGb().substring(0, 2) + "00000000";
            this.zoneList = this.commService.findZoneList(false, this.searchZoneCode, null, null);
            this.searchZoneName = this.zoneList.get(0).getZoneName();
        }
        //报告开始日期默认当前日期-1年
        this.searchReportBeginDate = DateUtils.addYears(new Date(), -1);
        //报告结束日期默认今天
        this.searchReportEndDate = new Date();
        //状态
        checkLevel = PropertyUtils.getValue("checkLevel");
        this.states = new String[]{"1", "2"};
        this.stateList.add(new SelectItem("1", "待提交"));
        this.stateList.add(new SelectItem("2", "已退回"));
        this.stateList.add(new SelectItem("3", "待初审"));
        if ("3".equals(checkLevel)) {
            this.stateList.add(new SelectItem("4", "待复审"));
        }
        this.stateList.add(new SelectItem("5", "待终审"));
        this.stateList.add(new SelectItem("6", "终审通过"));
        ifDesign = PropertyUtils.getValue("rpt.ifDesign");
    }

    /**
     * 诊断机构删除疑似职业病报告卡(标删)
     */
    public void deleteAction() {
        try {
            zwReportCardService.deleteTdZwYszybRpt(this.rptRid);
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     *  <p>方法描述：添加修改初始化</p>
     * @MethodAuthor hsj 2022/4/15 11:02
     */
    private void initTdZwYszybRpt() {
        //证件类型
        getCardTypeMap();
        //信息来源
        getsourceList();
    }
    /**
     *  <p>方法描述：获取信息来源</p>
     * @MethodAuthor hsj 2022/4/15 11:38
     */
    private void getsourceList() {
        //不显示扩展字段1为1
        sourceList = new ArrayList<>();
        sourceMap = new HashMap<>();
        sourceLsId = null;
        List<TsSimpleCode> tsSimpleCodeList = this.commService.findLevelSimpleCodesByTypeId("5316");
        for(TsSimpleCode entity : tsSimpleCodeList) {
            if("1".equals(entity.getExtendS1())){
                continue;
            }
            if("2".equals(entity.getExtendS1())){
                sourceLsId = entity.getRid();
            }
            sourceList.add(entity);
            sourceMap.put(entity.getRid(), entity);
        }
    }
    /**
     *  <p>方法描述:信息来源的切换</p>
     * @MethodAuthor hsj 2022/4/15 11:45
     */
    public void onSourceChangeAction(){
        if(null != sourceId){
            tdZwYszybRpt.setFkBySourceId(sourceMap.get(sourceId));
        }
    }
    /**
     *  <p>方法描述：获取证件类型</p>
     * @MethodAuthor hsj 2022/4/15 9:55
     */
    private void getCardTypeMap() {
        cardTypeList = new ArrayList<>();
        cardTypeMap = new HashMap<>();
        cardTypeList = this.commService.findLevelSimpleCodesByTypeId("5503");
        for(TsSimpleCode entity : cardTypeList) {
            cardTypeMap.put(entity.getRid(), entity);
        }
    }
    /**
     *  <p>方法描述：证件类型切换</p>
     * @MethodAuthor hsj 2022/4/15 9:52
     */
    public void onPsnTypeChangeAction() {
        // “88”是无人员类型
        this.ifIdcAble = true;
        if(null != editCardTypeId && null != cardTypeMap.get(editCardTypeId) && "88".equals(cardTypeMap.get(editCardTypeId).getCodeNo())) {
            this.ifIdcAble = false;
        }
        //证件类型赋值
        tdZwYszybRpt.setFkByCardTypeId(cardTypeMap.get(editCardTypeId));
    }

    /**
     *  <p>方法描述：根本身份证号自动填写出生日期、性别</p>
     * @MethodAuthor hsj 2022/4/15 10:08
     */
    public void findFlowByIdc() {
        if(ifIdcAble && null != editCardTypeId) {
            if (null != cardTypeMap.get(editCardTypeId) && "01".equals(cardTypeMap.get(editCardTypeId).getCodeNo()) && StringUtils.isNotBlank(tdZwYszybRpt.getIdc())) {
                String checkIDC = IdcUtils.checkIDC(tdZwYszybRpt.getIdc());
                if (StringUtils.isBlank(checkIDC)) {
                    // 性别
                    tdZwYszybRpt.setSex(IdcUtils.getSex(tdZwYszybRpt.getIdc()));
                    //出生年月
                    tdZwYszybRpt.setBirthday(IdcUtils.calBirthday(tdZwYszybRpt.getIdc()));
                }
            }
        }
    }
    /**
     *  <p>方法描述：初始化企业信息</p>
     * @MethodAuthor hsj 2022/4/15 10:49
     */
    public void selectCrptList() {
        Map<String, Object> options = MapUtils.produceDialogMap(1080, 1050, 520, 505);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        CrptDialogParamJson json = new CrptDialogParamJson();
        json.setBusType("1");
        json.setIfShowOtherZone(true);
        json.setIfSearchZoneDefault(false);
        if ("1".equals(this.crpyType)) {
            json.setSearchCrptName(StringUtils.objectToString(this.tdZwYszybRpt.getCrptName()));
            json.setAreaValidate(1);
        } else if ("2".equals(this.crpyType)) {
            json.setSearchCrptName(StringUtils.objectToString(this.tdZwYszybRpt.getEmpCrptName()));
            json.setAreaValidate(2);
            if (this.tdZwYszybRpt.getFkByCrptId() != null) {
                json.setExcludeRids(String.valueOf(this.tdZwYszybRpt.getFkByCrptId().getRid()));
            }
        }
        json.setIfSelectOperationStopData(Boolean.TRUE);
        paramList.add(JSON.toJSONString(json));
        paramMap.put("jsonParam", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        String useNewCrptSelect = this.commService.findParamValue("USE_NEW_CRPT_SELECT");
        String crptSelectPath = "/webapp/heth/comm/crptCommSelectList";
        if ("1".equals(useNewCrptSelect)) {
            crptSelectPath = "/webapp/heth/comm/crptCommSelectListNew";
        }
        requestContext.openDialog(crptSelectPath, options, paramMap);
    }
    /**
     *  <p>方法描述：选择企业后</p>
     * @MethodAuthor hsj 2022/4/15 10:50
     */
    public void onCrptSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            TbTjCrptIndepend tbTjCrptIndepend = (TbTjCrptIndepend) selectedMap.get("selectCrptIndepend");
            TbTjCrpt tbTjCrpt = tbTjCrptIndepend.getFkByCrptId();
            if ("1".equals(this.crpyType)) {
                this.tdZwYszybRpt.setFkByCrptId(tbTjCrpt);
                this.tdZwYszybRpt.setCrptName(tbTjCrpt.getCrptName());
                this.tdZwYszybRpt.setCreditCode(tbTjCrpt.getInstitutionCode());
                this.tdZwYszybRpt.setFkByEconomyId(tbTjCrpt.getTsSimpleCodeByEconomyId());
                this.tdZwYszybRpt.setFkByIndusTypeId(tbTjCrpt.getTsSimpleCodeByIndusTypeId());
                this.tdZwYszybRpt.setFkByCrptSizeId(tbTjCrpt.getTsSimpleCodeByCrptSizeId());
                this.tdZwYszybRpt.setFkByZoneId(tbTjCrpt.getTsZoneByZoneId());
                this.tdZwYszybRpt.setAddress(tbTjCrpt.getAddress());
                this.tdZwYszybRpt.setPostcode(tbTjCrpt.getPostCode());
                this.tdZwYszybRpt.setSafeposition(tbTjCrptIndepend.getLinkman2());
                this.tdZwYszybRpt.setSafephone(tbTjCrptIndepend.getLinkphone2());
                this.selEmpCrpt = this.tdZwYszybRpt.getFkByIndusTypeId() != null
                        && "2".equals(this.tdZwYszybRpt.getFkByIndusTypeId().getExtendS1());
                if (!this.selEmpCrpt) {
                    pakEmpCrptInfo(tbTjCrpt);
                } else if (tbTjCrpt.getRid() != null && this.tdZwYszybRpt.getFkByEmpCrptId() != null
                        && tbTjCrpt.getRid().equals(this.tdZwYszybRpt.getFkByEmpCrptId().getRid())) {
                    this.tdZwYszybRpt.setFkByEmpCrptId(null);
                    this.tdZwYszybRpt.setEmpCrptName(null);
                    this.tdZwYszybRpt.setEmpCreditCode(null);
                    this.tdZwYszybRpt.setFkByEmpEconomyId(null);
                    this.tdZwYszybRpt.setFkByEmpIndusTypeId(null);
                    this.tdZwYszybRpt.setFkByEmpCrptSizeId(null);
                    this.tdZwYszybRpt.setFkByEmpZoneId(null);
                }
            } else if ("2".equals(this.crpyType)) {
                pakEmpCrptInfo(tbTjCrpt);
            }
        }
    }

    private void pakEmpCrptInfo(TbTjCrpt tbTjCrpt) {
        this.tdZwYszybRpt.setFkByEmpCrptId(tbTjCrpt);
        this.tdZwYszybRpt.setEmpCrptName(tbTjCrpt.getCrptName());
        this.tdZwYszybRpt.setEmpCreditCode(tbTjCrpt.getInstitutionCode());
        this.tdZwYszybRpt.setFkByEmpEconomyId(tbTjCrpt.getTsSimpleCodeByEconomyId());
        this.tdZwYszybRpt.setFkByEmpIndusTypeId(tbTjCrpt.getTsSimpleCodeByIndusTypeId());
        this.tdZwYszybRpt.setFkByEmpCrptSizeId(tbTjCrpt.getTsSimpleCodeByCrptSizeId());
        this.tdZwYszybRpt.setFkByEmpZoneId(tbTjCrpt.getTsZoneByZoneId());
    }

    /**
     *  <p>方法描述：初始化疑似职业病</p>
     * @MethodAuthor hsj 2022/4/15 11:06
     */
    public void selectZybTypeAction() {
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("width", 680);
        options.put("contentWidth", 625);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add("疑似职业病");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add("5010");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        paramList.add("true");
        paramMap.put("ifAllSelect", paramList);
        paramList = new ArrayList<String>();
        paramList.add("false");
        paramMap.put("ifShowFirstCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeRadioSelectListNew", options, paramMap);
    }
    /**
     *  <p>方法描述：选择疑似职业病后</p>
     * @MethodAuthor hsj 2022/4/15 11:06
     */
    public void onZybTypeSelect(SelectEvent event){
        //ifChemical :是否为化学中毒
        ifChemical = false;
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            //已选择的
            TsSimpleCode code = selectedMap.get("selectPro") == null ? null
                    : (TsSimpleCode) selectedMap.get("selectPro");
            if (null != code) {
                //疑似职业病种类
                tdZwYszybRpt.setYszybTypeName(code.getCodeDesc());
                //当前
                tdZwYszybRpt.setFkByOccDiseid(code);
                tdZwYszybRpt.setYszybName(code.getCodeName());

                if("1".equals(code.getExtendS1())){
                    //化学中毒
                    ifChemical = true;
                }else {
                    ifChemical = false;
                    tdZwYszybRpt.setZyPoisonType(null);
                }
            }else{
                tdZwYszybRpt.setFkByOccDiseid(new TsSimpleCode());
                JsfUtil.addErrorMessage("职业病鉴定信息请选择职业病");
            }
        }
    }
    /**
     *  <p>方法描述：初始化危害因素</p>
     * @MethodAuthor hsj 2022/4/15 11:51
     */
    public void selectBadtree() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);
        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>(1);
        paramList = new ArrayList<String>(1);
        //标题
        paramList.add("危害因素");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>(1);
        //码表类型
        paramList.add("5007");
        paramMap.put("typeNo", paramList);
        //已选择的rid
        List<TdZwYszybTchBadrsn> list = tdZwYszybRpt.getBadrsnList();
        String rids = "";
        if(!CollectionUtils.isEmpty(list)){
            for(TdZwYszybTchBadrsn badrsnsClt : list) {
                rids += ","+badrsnsClt.getFkByBadrsnId().getRid();
            }
        }
        paramList = new ArrayList<String>(1);
        paramList.add(rids.replaceFirst(",", ""));
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<String>();
        //查询条件是否显示大类
        paramList.add("true");
        paramMap.put("ifShowFirstCode", paramList);
        paramList = new ArrayList<String>();
        //为1时,选择时只选择最末级
        paramList.add("1");
        paramMap.put("selLast", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList.xhtml", options, paramMap);
    }
    /**
     *  <p>方法描述：选择危害因素后</p>
     * @MethodAuthor hsj 2022/4/15 11:54
     */
    public void onBadtreeSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            tdZwYszybRpt.getBadrsnList().clear();
            List<TsSimpleCode> badfactorList = (List<TsSimpleCode>) selectedMap.get("selectPros");
            StringBuffer buffer = new StringBuffer();
            for(TsSimpleCode tsSimpleCode : badfactorList) {
                buffer.append("，").append(tsSimpleCode.getCodeName());
                TdZwYszybTchBadrsn tdZwYszybTchBadrsn = new TdZwYszybTchBadrsn();
                tdZwYszybTchBadrsn.setCreateManid(Global.getUser().getRid());
                tdZwYszybTchBadrsn.setCreateDate(new Date());
                tdZwYszybTchBadrsn.setFkByMainId(tdZwYszybRpt);
                tdZwYszybTchBadrsn.setFkByBadrsnId(tsSimpleCode);
                tdZwYszybRpt.getBadrsnList().add(tdZwYszybTchBadrsn);
            }
            tdZwYszybRpt.setTchBadrsns(buffer.deleteCharAt(0).toString());
            RequestContext.getCurrentInstance().update("tabView:editTabView:badRsnPanel");
        }
    }
    /**
     *  <p>方法描述：初始化工种</p>
     * @MethodAuthor hsj 2022/4/15 14:18
     */
    public void selectWorkTypeAction(){
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("width", 680);
        options.put("contentWidth", 625);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add("工种");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add("5502");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        paramList.add("true");
        paramMap.put("ifAllSelect", paramList);
        paramList = new ArrayList<String>();
        paramList.add("false");
        paramMap.put("ifShowFirstCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeRadioSelectListNew", options, paramMap);
    }
    /**
     *  <p>方法描述：切换工种</p>
     * @MethodAuthor hsj 2022/4/15 14:20
     */
    public void onWorkTypeSearch(SelectEvent event) {
        //是否为其他
        ifOtherWork = false;
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            TsSimpleCode code = selectedMap.get("selectPro") == null ? null
                    : (TsSimpleCode) selectedMap.get("selectPro");
            code = commService.findTsSimpleCodeByRid(code.getRid());
            tdZwYszybRpt.setFkByWorkTypeId(code);
            tdZwYszybRpt.setWorkName(code.getCodeName());
            if("1".equals(code.getExtendS1())){
                ifOtherWork = true;
            }else {
                ifOtherWork = false;
                tdZwYszybRpt.setWorkOther(null);
            }
        }
    }
    /**
     *  <p>方法描述：清除工种</p>
     * @MethodAuthor hsj 2022/4/16 10:30
     */
    public void clearWorkType(){
        tdZwYszybRpt.setFkByWorkTypeId(null);
        ifOtherWork = false;
        tdZwYszybRpt.setWorkOther(null);
        tdZwYszybRpt.setWorkName(null);
        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update(":tabView:editForm:work')");
    }
    /**
     *  <p>方法描述：提交前验证</p>
     * @MethodAuthor hsj 2022/4/15 15:34
     */
    public void beforeSubmit() {
        try{
            //为空验证
            boolean ifFlag = false;
            if (veryNullData()) {
                ifFlag = true;
            }
            if (veryData()) {
                ifFlag = true;
            }
            if(ifFlag){
                return;
            }
            // 若已制作，不可提交
            if (StringUtils.isBlank(tdZwYszybRpt.getAnnexPath())) {
                JsfUtil.addErrorMessage("请先制作文书！");
                return;
            }
            RequestContext context = RequestContext.getCurrentInstance();
            context.execute("PF('ConfirmDialog').show()");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }
    /**
     *  <p>方法描述：为空验证</p>
     * @MethodAuthor hsj 2022/4/15 15:37
     */
    public boolean veryNullData(){
        boolean flag = false;
        //劳动者信息
        if(null == tdZwYszybRpt.getFkByCardTypeId() || null == tdZwYszybRpt.getFkByCardTypeId().getRid()){
            JsfUtil.addErrorMessage("证件类型不能为空！");
            flag = true;
        }else{
            //证件号码：当证件类型为非“暂未获取”时，必填
            if(!"88".equals(tdZwYszybRpt.getFkByCardTypeId().getCodeNo()) && StringUtils.isBlank(tdZwYszybRpt.getIdc())){
                JsfUtil.addErrorMessage("证件号码不能为空！");
                flag = true;
            }
        }
        if(null == tdZwYszybRpt.getSex()){
            JsfUtil.addErrorMessage("性别不能为空！");
            flag = true;
        }
        if(null == tdZwYszybRpt.getBirthday()){
            JsfUtil.addErrorMessage("出生日期不能为空！");
            flag = true;
        }
        if(StringUtils.isBlank(tdZwYszybRpt.getLinktel())){
            JsfUtil.addErrorMessage("联系电话不能为空！");
            flag = true;
        }

        //疑似职业病报告信息
        if(StringUtils.isBlank(tdZwYszybRpt.getYszybName())){
            JsfUtil.addErrorMessage("疑似职业病名称不能为空！");
            flag = true;
        }
        if(null == tdZwYszybRpt.getFkBySourceId() || null == tdZwYszybRpt.getFkBySourceId().getRid()){
            JsfUtil.addErrorMessage("信息来源不能为空！");
            flag = true;
        }
        //职业性化学中毒分类：当选择的“疑似职业病名称”的扩展字段4为2（职业性化学中毒）时显示，单选，1急性2慢性，必填
        if(ifChemical && null == tdZwYszybRpt.getZyPoisonType()){
            JsfUtil.addErrorMessage("职业性化学中毒分类不能为空！");
            flag = true;
        }
        if(CollectionUtils.isEmpty(tdZwYszybRpt.getBadrsnList())){
            JsfUtil.addErrorMessage("接触的职业性有害因素不能为空！");
            flag = true;
        }
        if(null == tdZwYszybRpt.getFindDate()){
            JsfUtil.addErrorMessage("发现日期不能为空！");
            flag = true;
        }
        if(null == tdZwYszybRpt.getHarmStartDate()){
            JsfUtil.addErrorMessage("接害开始日期不能为空！");
            flag = true;
        }
        if(null == tdZwYszybRpt.getTchWorkYear()||null == tdZwYszybRpt.getTchWorkMonth()){
            JsfUtil.addErrorMessage("专业工龄的年月都不能为空！");
            flag = true;
        }
        if(null == tdZwYszybRpt.getFkByWorkTypeId() || null ==tdZwYszybRpt.getFkByWorkTypeId().getRid()){
            JsfUtil.addErrorMessage("统计工种不能为空！");
            flag = true;
        }else if(ifOtherWork && StringUtils.isBlank(tdZwYszybRpt.getWorkOther())){
            JsfUtil.addErrorMessage("工种其他不能为空！");
            flag = true;
        }
        if(StringUtils.isBlank(tdZwYszybRpt.getDiscoveryUnit())){
            JsfUtil.addErrorMessage("发现单位不能为空！");
            flag = true;
        }
        if(StringUtils.isBlank(tdZwYszybRpt.getDiscoveryRespPsn())){
            JsfUtil.addErrorMessage("发现单位负责人不能为空！");
            flag = true;
        }
        if(StringUtils.isBlank(tdZwYszybRpt.getFillFormPsn())) {
            JsfUtil.addErrorMessage("填表人不能为空！");
            flag = true;
        }
        if(StringUtils.isBlank(tdZwYszybRpt.getFillLink())) {
            JsfUtil.addErrorMessage("填表人联系电话不能为空！");
            flag = true;
        }
        if(null == tdZwYszybRpt.getFillDate()) {
            JsfUtil.addErrorMessage("填表日期不能为空！");
            flag = true;
        }
        if(StringUtils.isBlank(tdZwYszybRpt.getRptPsn())) {
            JsfUtil.addErrorMessage("报告人不能为空！");
            flag = true;
        }
        if(StringUtils.isBlank(tdZwYszybRpt.getRptLink())) {
            JsfUtil.addErrorMessage("报告人联系电话不能为空！");
            flag = true;
        }
        return flag;
    }
    /**
     *  <p>方法描述：验证数据</p>
     * @MethodAuthor hsj 2022/4/15 15:36
     */
    private boolean veryData() {
        boolean flag = false;
        //验证姓名、用人单位、报告日期不能为空
        if(StringUtils.isBlank(tdZwYszybRpt.getPersonnelName())){
            JsfUtil.addErrorMessage("姓名不能为空！");
            flag = true;
        }
        String comZoneGb2 = "";
        if (this.userUnit != null && this.userUnit.getTsZone() != null
                && StringUtils.isNotBlank(this.userUnit.getTsZone().getZoneGb())
                && this.userUnit.getTsZone().getZoneGb().length() >= 2) {
            comZoneGb2 = this.userUnit.getTsZone().getZoneGb().substring(0, 2);
        }
        //用人单位信息
        if (StringUtils.isBlank(tdZwYszybRpt.getCrptName())) {
            JsfUtil.addErrorMessage("用人单位名称不能为空！");
            flag = true;
        } else if (this.tdZwYszybRpt.getFkByIndusTypeId() == null
                || !"2".equals(this.tdZwYszybRpt.getFkByIndusTypeId().getExtendS1())) {
            String zoneGb = "";
            if (this.tdZwYszybRpt.getFkByZoneId() != null
                    && StringUtils.isNotBlank(this.tdZwYszybRpt.getFkByZoneId().getZoneGb())) {
                zoneGb = this.tdZwYszybRpt.getFkByZoneId().getZoneGb();
            }
            if (StringUtils.isBlank(zoneGb) || StringUtils.isBlank(comZoneGb2) || !zoneGb.startsWith(comZoneGb2)) {
                JsfUtil.addErrorMessage("用人单位行业类别不为“人力资源服务”行业时，仅可选择本省范围内的单位！");
                flag = true;
            }
        } else if (this.tdZwYszybRpt.getFkByIndusTypeId() != null
                && "2".equals(this.tdZwYszybRpt.getFkByIndusTypeId().getExtendS1())) {
            if (this.tdZwYszybRpt.getFkByCrptId() != null && this.tdZwYszybRpt.getFkByEmpCrptId() != null
                    && this.tdZwYszybRpt.getFkByCrptId().getRid() != null
                    && this.tdZwYszybRpt.getFkByCrptId().getRid().equals(this.tdZwYszybRpt.getFkByEmpCrptId().getRid())) {
                JsfUtil.addErrorMessage("当用人单位行业类别是人力资源时，用人单位和用工单位不能相同！");
                flag = true;
            }
        }
        //用工单位信息
        if (StringUtils.isBlank(tdZwYszybRpt.getEmpCrptName())) {
            JsfUtil.addErrorMessage("用工单位名称不能为空！");
            flag = true;
        } else {
            String zoneGb = "";
            if (this.tdZwYszybRpt.getFkByEmpZoneId() != null
                    && StringUtils.isNotBlank(this.tdZwYszybRpt.getFkByEmpZoneId().getZoneGb())) {
                zoneGb = this.tdZwYszybRpt.getFkByEmpZoneId().getZoneGb();
            }
            if (StringUtils.isBlank(zoneGb) || StringUtils.isBlank(comZoneGb2) || !zoneGb.startsWith(comZoneGb2)) {
                JsfUtil.addErrorMessage("用工单位仅可选择本省范围内的单位！");
                flag = true;
            }
            if (this.tdZwYszybRpt.getFkByEmpIndusTypeId() != null
                    && "2".equals(this.tdZwYszybRpt.getFkByEmpIndusTypeId().getExtendS1())) {
                JsfUtil.addErrorMessage("用工单位的行业类别不能为“人力资源服务”行业！");
                flag = true;
            }
        }
        if(null == tdZwYszybRpt.getRptDate()) {
            JsfUtil.addErrorMessage("报告日期不能为空！");
            flag = true;
        }
        if(StringUtils.isBlank(tdZwYszybRpt.getRptNo()) && null != tdZwYszybRpt.getFkByZoneId() && null != tdZwYszybRpt.getFkByZoneId().getRid()){
            //新增设置报告卡编号
            String aa = commService.getAutoCode("HETH_YSZYB_RPT_CODE", null);
            tdZwYszybRpt.setRptNo("YS"+tdZwYszybRpt.getFkByZoneId().getZoneGb()+DateUtils.formatDate(new Date(),"yyyyMMdd")+aa);
        }
        //劳动者信息
        //证件类型格式验证
        if(null != tdZwYszybRpt.getFkByCardTypeId() && null != tdZwYszybRpt.getFkByCardTypeId().getRid() && StringUtils.isNotBlank(tdZwYszybRpt.getIdc())){
            //暂未获取时，清空证件号码
            if("88".equals(tdZwYszybRpt.getFkByCardTypeId().getCodeNo())){
                tdZwYszybRpt.setIdc(null);
            }else {
                //非暂未获取时验证
                String msg = validateIdc(tdZwYszybRpt.getIdc(),tdZwYszybRpt.getFkByCardTypeId());
                if(StringUtils.isNotBlank(msg)){
                    JsfUtil.addErrorMessage(msg);
                    flag = true;
                }
            }
        }
        //联系电话格式
        if(StringUtils.isNotBlank(tdZwYszybRpt.getLinktel()) && !StringUtils.vertyPhone(tdZwYszybRpt.getLinktel())){
            JsfUtil.addErrorMessage("联系电话格式不正确！");
            flag = true;
        }
        //接害开始日期<=发现日期<=当前日期
        if(DateUtils.isCompareDate(tdZwYszybRpt.getFindDate(),">",new Date())){
            JsfUtil.addErrorMessage("发现日期应小于等于当前日期！");
            flag = true;
        }
        if(DateUtils.isCompareDate(tdZwYszybRpt.getHarmStartDate(),">",tdZwYszybRpt.getFindDate())){
            JsfUtil.addErrorMessage("接害开始日期应小于等于发现日期！");
            flag = true;
        }
        //出生日期<接害开始日期<=发现日期
        if(DateUtils.isCompareDate(tdZwYszybRpt.getHarmStartDate(),">",new Date())){
            JsfUtil.addErrorMessage("接害开始日期应小于等于当前日期！");
            flag = true;
        }
        if(DateUtils.isCompareDate(tdZwYszybRpt.getHarmStartDate(),"<=",tdZwYszybRpt.getBirthday())){
            JsfUtil.addErrorMessage("接害开始日期应大于出生日期！");
            flag = true;
        }
        if(null != tdZwYszybRpt.getHarmStartDate() ){

        }
        if(null!= tdZwYszybRpt.getTchWorkMonth() && tdZwYszybRpt.getTchWorkMonth()>11){
            JsfUtil.addErrorMessage("专业工龄（月份）格式不正确！");
            flag = true;
        }
        if(null!= tdZwYszybRpt.getTchWorkDay() && tdZwYszybRpt.getTchWorkDay()>31){
            JsfUtil.addErrorMessage("专业工龄（日）格式不正确！");
            flag = true;
        }
        if(null!= tdZwYszybRpt.getTchWorkHour() && tdZwYszybRpt.getTchWorkHour()>23){
            JsfUtil.addErrorMessage("专业工龄（时）格式不正确！");
            flag = true;
        }
        if(null!= tdZwYszybRpt.getTchWorkMinute() && tdZwYszybRpt.getTchWorkMinute()>59){
            JsfUtil.addErrorMessage("专业工龄（分）格式不正确！");
            flag = true;
        }
        if(StringUtils.isNotBlank(tdZwYszybRpt.getFillLink()) && !StringUtils.vertyPhone(tdZwYszybRpt.getFillLink())) {
            JsfUtil.addErrorMessage("填表人联系电话格式错误！");
            flag = true;
        }
        //发现日期<=填表日期<=当前日期
        if(DateUtils.isCompareDate(tdZwYszybRpt.getFillDate(),">",new Date())){
            JsfUtil.addErrorMessage("填表日期应小于等于当前日期！");
            flag = true;
        }
        if(DateUtils.isCompareDate(tdZwYszybRpt.getFillDate(),"<",tdZwYszybRpt.getFindDate())){
            JsfUtil.addErrorMessage("填表日期应大于等于发现日期！");
            flag = true;
        }
        if(StringUtils.isNotBlank(tdZwYszybRpt.getRptLink()) && !StringUtils.vertyPhone(tdZwYszybRpt.getRptLink())) {
            JsfUtil.addErrorMessage("报告人联系电话格式错误！");
            flag = true;
        }
        //填表日期<=报告日期<=当前日期
        if( DateUtils.isCompareDate(tdZwYszybRpt.getRptDate(),">",new Date())){
            JsfUtil.addErrorMessage("报告日期应小于等于当前日期！");
            flag = true;
        }
        if(DateUtils.isCompareDate(tdZwYszybRpt.getRptDate(),"<",tdZwYszybRpt.getFillDate())){
            JsfUtil.addErrorMessage("报告日期应大于等于填表日期！");
            flag = true;
        }
        return flag;
    }
    /**
     *  <p>方法描述：证件类型格式验证</p>
     * @MethodAuthor hsj 2022/4/15 16:07
     */
    public String validateIdc(String tmpIdc, TsSimpleCode tsSimpleCode){
        String checkIDC = null;
        boolean flag = false;
        if(null != tsSimpleCode){
            if(tsSimpleCode.getCodeNo().equals("01")){
                // 验证身份证号合法性
                checkIDC = IdcUtils.checkIDC(tmpIdc);
                if(null != checkIDC){
                    flag = true;
                }
            }else if(tsSimpleCode.getCodeNo().equals("02") && !IdcUtils.isHousehold(tmpIdc)){
                // 验证户口簿
                flag = true;
            }else if(tsSimpleCode.getCodeNo().equals("03") && !IdcUtils.isPassPort(tmpIdc)){
                // 验证护照
                flag = true;
            }else if(tsSimpleCode.getCodeNo().equals("04") && !IdcUtils.isMilitary(tmpIdc)){
                // 验证军官证
                flag = true;
            }else if(tsSimpleCode.getCodeNo().equals("06") && !IdcUtils.isHmCard(tmpIdc)){
                // 验证港澳通行证
                flag = true;
            }else if(tsSimpleCode.getCodeNo().equals("07") && !IdcUtils.isTaiWanEntry(tmpIdc)){
                // 验证台胞证
                flag = true;
            }
        }
        if(flag){
            checkIDC = "证件号码格式错误！";
        }
        return checkIDC;
    }
   /**
    *  <p>方法描述：设计文书</p>
    * @MethodAuthor hsj 2022-05-09 10:05
    */
    public void designWritReport() {
        if(!veryWritsort()){
            return;
        }
        this.fastReportBean = new FastReportBean(this, "HETH_2031");
        fastReportBean.designAction();
        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update("tabView:editForm");
        currentInstance.execute("frpt_design();");
        if(StringUtils.isNotBlank(tdZwYszybRpt.getAnnexPath())){
            currentInstance.execute("disabledInput('true','writ_yszyb')");
        }else {
            currentInstance.execute("disabledInput('false','writ_yszyb')");
        }


    }
   /**
    *  <p>方法描述：当前文书验证</p>
    * @MethodAuthor hsj 2022-05-09 10:06
    */
    private boolean veryWritsort(){
        boolean flag = true;
        writsort = getWritsort("2031");
        //文书未维护
        if (null==writsort) {
            JsfUtil.addErrorMessage("请在文书类型管理中维护该文书的相关信息！");
            return false;
        }
        if (null==writsort.getFkByRptTemplId()) {
            JsfUtil.addErrorMessage("请在文书类型管理中维护该文书的报表模板！");
            return false;
        }
        return flag;
    }
    public  TbZwWritsort getWritsort(String writCode){
        if (StringUtils.isNotBlank(writCode)) {
            String hql = "select t from TbZwWritsort t where t.writCode = "
                    + writCode;
            TbZwWritsort writsort = service.findOneByHql(hql,
                    TbZwWritsort.class);
            return writsort;
        }
        return null;

    }
    /**
     *  <p>方法描述：制作文书</p>
     * @MethodAuthor hsj 2022-05-09 10:13
     */
    public void buildWritReport() {
        if(veryNullData() || veryData() || !veryWritsort()){
            return;
        }
        if(null == tdZwBgkLastSta.getState() || tdZwBgkLastSta.getState() == 0){
            //初次保存填报接收日期
            tdZwBgkLastSta.setOrgRcvDate(tdZwYszybRpt.getRptDate());
        }
        cleanTdZwYszybRpt();
        this.zwReportCardService.saveOrUpdateTdZwYszybRpt(tdZwYszybRpt,tdZwBgkLastSta);
        addTdZwYszybRpt();
        showShadeTip();
        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update("tabView:editForm");
        currentInstance.execute("disabledInput('true','writ_yszyb')");
    }
    /**
     * <p>方法描述：制作文书加载</p>
     * @MethodAuthor rcj,2019年9月5日,buildWritReport
     * */
    public void showShadeTip(){
        RequestContext.getCurrentInstance().execute("showShadeTip()");
    }
    public TsUnit getUserUnit() {
        return userUnit;
    }
   /**
    *  <p>方法描述：附件查看</p>
    * @MethodAuthor hsj 2022-05-09 10:21
    */
    public void toAnnexView() {
        if (StringUtils.isBlank(tdZwYszybRpt.getAnnexPath())) {
            JsfUtil.addErrorMessage("附件已经被删除，请重新上传！");
            return;
        }
        RequestContext.getCurrentInstance().execute(
                "window.open('" + FileUtils.previewFile(tdZwYszybRpt.getAnnexPath(),
                        "") + "')");
    }
    /**
     *  <p>方法描述：附件删除</p>
     * @MethodAuthor hsj 2022-05-09 10:21
     */
    public void delMadedwrit() {
        try {
            tdZwYszybRpt.setAnnexPath(null);
            cleanTdZwYszybRpt();
            service.upsertEntity(tdZwYszybRpt);
            addTdZwYszybRpt();
            JsfUtil.addSuccessMessage("删除成功！");
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("disabledInput('false','writ_yszyb')");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }
    /**
     *  <p>方法描述：制作文书</p>
     * @MethodAuthor hsj 2022-05-09 10:30
     */
    public void tobuildWritReport(){
        //根据模板生成附件1、文书编号2、封装签章工具2、上传到本地
        this.fastReportBean = new FastReportBean(this, "HETH_2031");
        SysReturnPojo returnPojo = fastReportBean.showPDFReportAction("");
        String type = returnPojo.getType();
        String mess = returnPojo.getMess();

        if (!"00".equals(type)) {
            System.out.println("type："+type+"！！！！！mess："+mess);
            JsfUtil.addErrorMessage("生成文书失败，请稍后尝试！");
            return;
        }
        //附件地址
        tdZwYszybRpt.setAnnexPath(mess);
        //保存已制作、文书附件
        cleanTdZwYszybRpt();
        this.service.upsertEntity(this.tdZwYszybRpt);
        addTdZwYszybRpt();
        JsfUtil.addSuccessMessage("制作成功！");
        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.execute("disabledInput('true','writ_yszyb')");
    }

    public void setUserUnit(TsUnit userUnit) {
        this.userUnit = userUnit;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchCreditCode() {
        return searchCreditCode;
    }

    public void setSearchCreditCode(String searchCreditCode) {
        this.searchCreditCode = searchCreditCode;
    }

    public String getSearchPersonnelName() {
        return searchPersonnelName;
    }

    public void setSearchPersonnelName(String searchPersonnelName) {
        this.searchPersonnelName = searchPersonnelName;
    }

    public String getSearchIdc() {
        return searchIdc;
    }

    public void setSearchIdc(String searchIdc) {
        this.searchIdc = searchIdc;
    }

    public String getSearchSodName() {
        return searchSodName;
    }

    public void setSearchSodName(String searchSodName) {
        this.searchSodName = searchSodName;
    }

    public Date getSearchReportBeginDate() {
        return searchReportBeginDate;
    }

    public void setSearchReportBeginDate(Date searchReportBeginDate) {
        this.searchReportBeginDate = searchReportBeginDate;
    }

    public Date getSearchReportEndDate() {
        return searchReportEndDate;
    }

    public void setSearchReportEndDate(Date searchReportEndDate) {
        this.searchReportEndDate = searchReportEndDate;
    }

    public Boolean getThisUnit() {
        return thisUnit;
    }

    public void setThisUnit(Boolean thisUnit) {
        this.thisUnit = thisUnit;
    }

    public String[] getSearchUnits() {
        return searchUnits;
    }

    public void setSearchUnits(String[] searchUnits) {
        this.searchUnits = searchUnits;
    }

    public List<SelectItem> getSearchUnitList() {
        return searchUnitList;
    }

    public void setSearchUnitList(List<SelectItem> searchUnitList) {
        this.searchUnitList = searchUnitList;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }
    public Integer getEditCardTypeId() {
        return editCardTypeId;
    }

    public void setEditCardTypeId(Integer editCardTypeId) {
        this.editCardTypeId = editCardTypeId;
    }

    public List<TsSimpleCode> getCardTypeList() {
        return cardTypeList;
    }

    public void setCardTypeList(List<TsSimpleCode> cardTypeList) {
        this.cardTypeList = cardTypeList;
    }

    public void setCardTypeMap(Map<Integer, TsSimpleCode> cardTypeMap) {
        this.cardTypeMap = cardTypeMap;
    }

    public boolean isIfIdcAble() {
        return ifIdcAble;
    }

    public void setIfIdcAble(boolean ifIdcAble) {
        this.ifIdcAble = ifIdcAble;
    }

    public String getCrpyType() {
        return crpyType;
    }

    public void setCrpyType(String crpyType) {
        this.crpyType = crpyType;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public Integer getSourceLsId() {
        return sourceLsId;
    }

    public void setSourceLsId(Integer sourceLsId) {
        this.sourceLsId = sourceLsId;
    }

    public List<TsSimpleCode> getSourceList() {
        return sourceList;
    }

    public void setSourceList(List<TsSimpleCode> sourceList) {
        this.sourceList = sourceList;
    }

    public Map<Integer, TsSimpleCode> getSourceMap() {
        return sourceMap;
    }

    public void setSourceMap(Map<Integer, TsSimpleCode> sourceMap) {
        this.sourceMap = sourceMap;
    }

    public TdZwYszybRpt getTdZwYszybRpt() {
        return tdZwYszybRpt;
    }

    public void setTdZwYszybRpt(TdZwYszybRpt tdZwYszybRpt) {
        this.tdZwYszybRpt = tdZwYszybRpt;
    }

    public boolean isIfChemical() {
        return ifChemical;
    }

    public void setIfChemical(boolean ifChemical) {
        this.ifChemical = ifChemical;
    }

    public TdZwDiagorginfoComm getTjorginfo() {
        return tjorginfo;
    }

    public void setTjorginfo(TdZwDiagorginfoComm tjorginfo) {
        this.tjorginfo = tjorginfo;
    }

    public boolean isIfOtherWork() {
        return ifOtherWork;
    }

    public void setIfOtherWork(boolean ifOtherWork) {
        this.ifOtherWork = ifOtherWork;
    }

    public TdZwBgkLastSta getTdZwBgkLastSta() {
        return tdZwBgkLastSta;
    }

    public void setTdZwBgkLastSta(TdZwBgkLastSta tdZwBgkLastSta) {
        this.tdZwBgkLastSta = tdZwBgkLastSta;
    }

    public Integer getRptRid() {
        return rptRid;
    }

    public void setRptRid(Integer rptRid) {
        this.rptRid = rptRid;
    }

    public String getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(String checkLevel) {
        this.checkLevel = checkLevel;
    }

    @Override
    public FastReportBean getFastReportBean() {
        return fastReportBean;
    }

    @Override
    public List<FastReportData> supportFastReportDataSet() {
        return RptCardTempCommUtil.packageDatas("HETH_2031", tdZwYszybRpt.getRid());
    }

    @Override
    public List<FastReportDataRef> supportFastReportDataRef() {
        return null;
    }

    public TbZwWritsort getWritsort() {
        return writsort;
    }

    public void setWritsort(TbZwWritsort writsort) {
        this.writsort = writsort;
    }

    public String getIfDesign() {
        return ifDesign;
    }

    public void setIfDesign(String ifDesign) {
        this.ifDesign = ifDesign;
    }

    public Boolean getSelEmpCrpt() {
        return selEmpCrpt;
    }

    public void setSelEmpCrpt(Boolean selEmpCrpt) {
        this.selEmpCrpt = selEmpCrpt;
    }
}
