package com.chis.modules.heth.comm.entity;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 *
 * <AUTHOR>
 * @createTime 2019-5-24
 */
@Entity
@Table(name = "TB_TJ_ITEMS_SPE")
@SequenceGenerator(name = "TbTjItemsSpe", sequenceName = "TB_TJ_ITEMS_SPE_SEQ", allocationSize = 1)
public class TbTjItemsSpe implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TbTjItems fkByItemId;
    private TsSimpleCode fkByOnguardStateid;
    private TsSimpleCode fkByBadRsnId;
    private String sex;
    private String msrunt;
    private BigDecimal minval;
    private BigDecimal maxval;
    private String itemStdvalue;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    /************** 非实体字段 ***************/
    /**最最小值*/
    private BigDecimal mMinval;
    /**最最大值*/
    private BigDecimal mMaxval;
    /**在岗状态id*/
    private Integer onguardStateid;

    public TbTjItemsSpe() {
    }

    public TbTjItemsSpe(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjItemsSpe")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "ITEM_ID")
    public TbTjItems getFkByItemId() {
        return fkByItemId;
    }

    public void setFkByItemId(TbTjItems fkByItemId) {
        this.fkByItemId = fkByItemId;
    }

    @ManyToOne
    @JoinColumn(name = "ONGUARD_STATEID")
    public TsSimpleCode getFkByOnguardStateid() {
        return fkByOnguardStateid;
    }

    public void setFkByOnguardStateid(TsSimpleCode fkByOnguardStateid) {
        this.fkByOnguardStateid = fkByOnguardStateid;
    }

    @ManyToOne
    @JoinColumn(name = "BAD_RSN_ID")
    public TsSimpleCode getFkByBadRsnId() {
        return fkByBadRsnId;
    }

    public void setFkByBadRsnId(TsSimpleCode fkByBadRsnId) {
        this.fkByBadRsnId = fkByBadRsnId;
    }

    @Column(name = "SEX")
    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    @Column(name = "MSRUNT")
    public String getMsrunt() {
        return msrunt;
    }

    public void setMsrunt(String msrunt) {
        this.msrunt = msrunt;
    }

    @Column(name = "MINVAL")
    public BigDecimal getMinval() {
        return minval;
    }

    public void setMinval(BigDecimal minval) {
        this.minval = minval;
    }

    @Column(name = "MAXVAL")
    public BigDecimal getMaxval() {
        return maxval;
    }

    public void setMaxval(BigDecimal maxval) {
        this.maxval = maxval;
    }

    @Column(name = "ITEM_STDVALUE")
    public String getItemStdvalue() {
        return itemStdvalue;
    }

    public void setItemStdvalue(String itemStdvalue) {
        this.itemStdvalue = itemStdvalue;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Transient
    public BigDecimal getmMinval() {
        return mMinval;
    }

    public void setmMinval(BigDecimal mMinval) {
        this.mMinval = mMinval;
    }

    @Transient
    public BigDecimal getmMaxval() {
        return mMaxval;
    }

    public void setmMaxval(BigDecimal mMaxval) {
        this.mMaxval = mMaxval;
    }

    @Transient
    public Integer getOnguardStateid() {
        return onguardStateid;
    }

    public void setOnguardStateid(Integer onguardStateid) {
        this.onguardStateid = onguardStateid;
    }
}
