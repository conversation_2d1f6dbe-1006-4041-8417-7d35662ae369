package com.chis.modules.heth.comm.utils;

import com.chis.modules.heth.comm.entity.TdZwSrvorgCard;
import com.chis.modules.heth.comm.entity.TdZwSrvorgCardZone;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 *  <p>类描述：放射卫生技术服务信息报送卡填报工具类</p>
 * @ClassAuthor hsj 2021/10/26 9:13
 */
public class ZwSrvorgCardUtils {
	/**
	 *  <p>方法描述：关联实体初始化</p>
	 * @MethodAuthor hsj
	 */
	public static void initEntity(TdZwSrvorgCard srvorgCard) {
		if(null == srvorgCard.getFkBySutId()){
			srvorgCard.setFkBySutId(new TsSimpleCode());
		}
		if(null == srvorgCard.getFkByJcTypeId()){
			srvorgCard.setFkByJcTypeId(new TsSimpleCode());
		}
		//技术服务地址
		List<TdZwSrvorgCardZone> zwSrvorgCardZones = srvorgCard.getSrvorgCardZoneList();
		if(!CollectionUtils.isEmpty(zwSrvorgCardZones)){
			for(TdZwSrvorgCardZone zone : zwSrvorgCardZones ){
				if(null == zone.getFkByZoneId()){
					zone.setFkByZoneId(new TsZone());
				}
			}
		}
	}
	/**
	 *  <p>方法描述：职业性有害因素情况清空</p>
	 * @MethodAuthor hsj
	 */
	public  static void clearJcSubCommEntity(TdZwSrvorgCard srvorgCard) {
		if(null == srvorgCard.getFkBySutId() || null == srvorgCard.getFkBySutId().getRid() ){
			srvorgCard.setFkBySutId(null);
		}
		if (null == srvorgCard.getFkByJcTypeId() || null == srvorgCard.getFkByJcTypeId().getRid()) {
			srvorgCard.setFkByJcTypeId(null);
		}
		//技术服务地址
		List<TdZwSrvorgCardZone> zwSrvorgCardZones = srvorgCard.getSrvorgCardZoneList();
		if(!CollectionUtils.isEmpty(zwSrvorgCardZones)){
			for(TdZwSrvorgCardZone zone : zwSrvorgCardZones ){
				if(null == zone.getFkByZoneId() || null == zone.getFkByZoneId().getRid()){
					zone.setFkByZoneId(null);
				}
			}
		}
	}
}
