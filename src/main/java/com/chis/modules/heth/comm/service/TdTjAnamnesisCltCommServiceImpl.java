package com.chis.modules.heth.comm.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.modules.heth.comm.entity.TdTjAnamnesisClt;
import com.chis.modules.system.service.AbstractTemplate;

/**
 * @Description : 问诊既往病史表（数据录入）-业务层
 * @ClassAuthor : anjing
 * @Date : 2019/5/18 15:55
 **/
@Service
@Transactional(readOnly = true)
public class TdTjAnamnesisCltCommServiceImpl extends AbstractTemplate {

    /**
     * @Description : 根据主表I的、类型获取既往史列表
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 15:56
     **/
    public List<TdTjAnamnesisClt> selectAnamnesisCltListByBhkId(Integer bhkId) {
        if (null != bhkId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdTjAnamnesisClt t ");
            sb.append(" where t.fkByBhkId.rid = ").append(bhkId);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }
}
