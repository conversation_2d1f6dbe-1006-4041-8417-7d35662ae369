package com.chis.modules.heth.comm.rptvo;

import com.chis.modules.system.rptvo.ZwxFieldDoc;

/**
 * @ClassName OverRangeInfo
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020-11-20
 * @Version V1.0
 **/
public class OverRangeVo {
    @ZwxFieldDoc(value = "通知单位")
    private String noticeUnitName;

    @ZwxFieldDoc(value = "文书内容")
    private String writContent;

    @ZwxFieldDoc(value = "处置单位")
    private String dealUnitName;

    @ZwxFieldDoc(value = "文书日期")
    private String writDate;

    public String getNoticeUnitName() {
        return noticeUnitName;
    }

    public void setNoticeUnitName(String noticeUnitName) {
        this.noticeUnitName = noticeUnitName;
    }

    public String getWritContent() {
        return writContent;
    }

    public void setWritContent(String writContent) {
        this.writContent = writContent;
    }

    public String getDealUnitName() {
        return dealUnitName;
    }

    public void setDealUnitName(String dealUnitName) {
        this.dealUnitName = dealUnitName;
    }

    public String getWritDate() {
        return writDate;
    }

    public void setWritDate(String writDate) {
        this.writDate = writDate;
    }
}
