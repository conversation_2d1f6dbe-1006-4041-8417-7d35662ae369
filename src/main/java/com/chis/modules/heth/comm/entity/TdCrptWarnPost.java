package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-4-20
 */
@Entity
@Table(name = "TD_CRPT_WARN_POST")
@SequenceGenerator(name = "TdCrptWarnPost", sequenceName = "TD_CRPT_WARN_POST_SEQ", allocationSize = 1)
public class TdCrptWarnPost implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String creditCode;
	private String postNameCheck;
	private String postNameJc;
	private Integer num;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdCrptWarnPost() {
	}

	public TdCrptWarnPost(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdCrptWarnPost")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "CREDIT_CODE")	
	public String getCreditCode() {
		return creditCode;
	}

	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}	
			
	@Column(name = "POST_NAME_CHECK")	
	public String getPostNameCheck() {
		return postNameCheck;
	}

	public void setPostNameCheck(String postNameCheck) {
		this.postNameCheck = postNameCheck;
	}	
			
	@Column(name = "POST_NAME_JC")	
	public String getPostNameJc() {
		return postNameJc;
	}

	public void setPostNameJc(String postNameJc) {
		this.postNameJc = postNameJc;
	}	
			
	@Column(name = "NUM")	
	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}