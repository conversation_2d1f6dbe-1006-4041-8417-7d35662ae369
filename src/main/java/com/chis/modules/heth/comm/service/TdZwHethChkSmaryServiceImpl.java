package com.chis.modules.heth.comm.service;

import com.chis.common.utils.CollectionUtil;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.utils.ZwChkSmaryCommUtils;
import com.chis.modules.system.entity.TbZwWritsort;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.utils.HolidayUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <p>类描述：职业性有害因素监测卡填报  service </p>
 *
 * @ClassAuthor: yzz
 * @date： 2021年10月21日
 **/
@Service
@Transactional(readOnly = true)
public class TdZwHethChkSmaryServiceImpl extends AbstractTemplate {

    protected CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
     * <p>方法描述：删除</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-22
     **/
    public void delChkSmary(Integer rid){

        StringBuilder sql = new StringBuilder();
        sql.append("update TD_ZW_HETH_CHK_SMARY ");
        sql.append(" set DEL_MARK= 1");
        sql.append(" where rid =");
        sql.append(rid);
        em.createNativeQuery(sql.toString()).executeUpdate();

    }
    /**
     *  <p>方法描述：查询职业健康检查汇总信息</p>
     * @MethodAuthor hsj
     */
    public TdZwHethChkSmaryComm searchChkSmary(Integer rid) {
        if (null==rid) {
            return null;
        }
        TdZwHethChkSmaryComm chkSmary = super.find(TdZwHethChkSmaryComm.class, rid);
        chkSmary.getChkSubList().size();
        if(chkSmary.getJcSubList().size() > 0){
            List<TdZwHethJcSubComm> list = chkSmary.getJcSubList();
            //根据rid 排序
            Collections.sort(list, new Comparator<TdZwHethJcSubComm>() {
                @Override
                public int compare(TdZwHethJcSubComm o1, TdZwHethJcSubComm o2) {
                    return o1.getRid().compareTo(o2.getRid());
                }
            });
        }
        return chkSmary;
    }

    /**
     *  <p>方法描述：保存或更新</p>
     * @MethodAuthor hsj
     */
    @Transactional(readOnly = false)
    public void saveOrUpdateChkSmary(TdZwHethChkSmaryComm chkSmary) {
        if (null == chkSmary) {
            return;
        }
        //生成报告卡编号
        if(StringUtils.isBlank(chkSmary.getRptNo())){
            String autoCode = this.commService.getAutoCode("SMARY_CARD_CODE", null);
            String cardNo = "JC" + chkSmary.getFkByZoneId().getZoneGb() + DateUtils.formatDate(new Date(), "yyyyMMdd") + autoCode;
            chkSmary.setRptNo(cardNo);
        }
        List<TdZwHethChkSubComm> chkSubList = chkSmary.getChkSubList();
        if (null != chkSubList && chkSubList.size() > 0) {
            for (TdZwHethChkSubComm sub : chkSubList) {
                sub.setFkByMainId(chkSmary);
                if (null == sub.getRid()) {
                    preInsert(sub);
                } else {
                    preUpdate(sub);
                }
            }
        }
        //一个周期内是否开展职业性有害因素检测为否清空职业性有害因素检测情况信息
        if (!new Integer(1).equals(chkSmary.getIfOpenOneWeek())) {
            chkSmary.setJcSubList(new ArrayList<TdZwHethJcSubComm>());
            chkSmary.setJcUnitName(null);
            chkSmary.setJcUnitCharge(null);
        }
        List<TdZwHethJcSubComm> jcSubList = chkSmary.getJcSubList();
        if (!CollectionUtils.isEmpty(jcSubList)) {
            //职业性有害因素情况清空
            ZwChkSmaryCommUtils.clearJcSubCommEntity(chkSmary);
            for (TdZwHethJcSubComm sub : jcSubList) {
                sub.setFkByMainId(chkSmary);
                if (null == sub.getRid()) {
                    preInsert(sub);
                } else {
                    preUpdate(sub);
                }
            }
        }
        super.upsertEntity(chkSmary);
    }


    /**
     * <p>方法描述：批量更新状态</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-22
     **/
    public void batchUpdateState( TdZwBgkLastSta tdZwBgkLastSta){

        this.updateObj(tdZwBgkLastSta);

    }


    /**
     * <p>方法描述：根据id获取报告卡最新状态表信息</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-22
     **/
    @Deprecated
    @Transactional
    public TdZwBgkLastSta findlastStaById(Integer rid){
        StringBuffer hql=new StringBuffer();
        hql.append("select T from TdZwBgkLastSta T");
        hql.append(" where T.cartType=1 ");
        hql.append(" and T.busId= ");
        hql.append(rid);
        return this.findOneByHql(hql.toString(),TdZwBgkLastSta.class);

    }

    /**
 	 * <p>方法描述：根据业务主键和报告卡类型查询最新状态表</p>
 	 * @MethodAuthor qrr,2021年10月26日,findTdZwBgkLastSta
     * */
    public TdZwBgkLastSta findTdZwBgkLastSta(Integer budId, Integer cartType) {
        if (null != budId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdZwBgkLastSta t ");
            sb.append(" where t.busId = ").append(budId);
            sb.append(" and t.cartType = ").append(cartType);
            List<TdZwBgkLastSta> list = em.createQuery(sb.toString()).getResultList();
            if(!CollectionUtils.isEmpty(list)) {
                return list.get(0);
            }
            return null;
        }
        return null;
    }

    /**
     * <p>方法描述：根据文书编码获取文书信息</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-25
     **/
    public TbZwWritsort getWritsort(String writCode){
        if (StringUtils.isNotBlank(writCode)) {
            String hql = "select t from TbZwWritsort t where t.writCode = "
                    + writCode;
            TbZwWritsort writsort = this.findOneByHql(hql,
                    TbZwWritsort.class);
            return writsort;
        }
        return null;
    }

    /**
 	 * <p>方法描述：机构填报保存职业有害因素监测卡</p>
 	 * @MethodAuthor qrr,2021年10月22日,saveTdZwHethChkSmary
     * */
    @Transactional(readOnly = false)
    public void saveTdZwHethChkSmary(TdZwHethChkSmaryComm chkSmary, TdZwBgkLastSta lastSta) {
        this.saveOrUpdateChkSmary(chkSmary);
        // 最新流程
        if (null != lastSta) {
        	lastSta.setBusId(chkSmary.getRid());
            this.upsertEntity(lastSta);
        }
    }
    /**
 	 * <p>方法描述：机构填报提交职业有害因素监测卡</p>
 	 * @MethodAuthor qrr,2021年10月26日,submitTdZwHethChkSmary
     * */
    @Transactional(readOnly = false)
    public void submitTdZwHethChkSmary(TdZwHethChkSmaryComm chkSmary, TdZwBgkLastSta lastSta) {
        this.saveOrUpdateChkSmary(chkSmary);
        // 最新流程
        if (null != lastSta) {
        	lastSta.setBusId(chkSmary.getRid());
            this.upsertEntity(lastSta);
        }
        //流程记录
        saveOrgTdZwBgkFlow(chkSmary.getRid(), 1,chkSmary.getFkByEmpZoneId());
    }

    /**
 	 * <p>方法描述：机构填报流程记录</p>
 	 * @MethodAuthor qrr,2021年10月22日,saveOrgTdZwBgkFlow
     * */
    @Transactional(readOnly = false)
    private void saveOrgTdZwBgkFlow(Integer busId, Integer cardType,TsZone zone) {
        if (null == busId || null == cardType) {
            return;
        }
        // 新增流程记录
        TdZwBgkFlow bgkFlow = new TdZwBgkFlow();
        bgkFlow.setBusId(busId);
        bgkFlow.setCartType(cardType);
        bgkFlow.setRcvDate(new Date());
        bgkFlow.setFkBySmtPsnId(Global.getUser());
        if ("1".equals(zone.getIfCityDirect())) {//市直属
        	bgkFlow.setOperFlag(33);
        } else if ("1".equals(zone.getIfProvDirect())){//省直属
        	bgkFlow.setOperFlag(44);
        } else {
            bgkFlow.setOperFlag(21);
        }
        preInsert(bgkFlow);
        this.save(bgkFlow);
    }

    /**
     * <p>方法描述：报告卡最新状态记录保存，审批流程表保存并更新上条记录</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-26
     **/
    @Transactional(readOnly = false)
    public void updateInsLastRecord(TdZwBgkLastSta tdZwBgkLastSta,TdZwBgkFlow tdZwBgkFlow,String limitTime){
        //保存  报告卡最新状态
        this.upsertEntity(tdZwBgkLastSta);

        //获取 报告卡审批流程  上一条记录
        StringBuffer hql=new StringBuffer();
        hql.append("select T from TdZwBgkFlow T");
        hql.append(" where T.cartType=").append(tdZwBgkFlow.getCartType());
        hql.append(" and T.busId=").append(tdZwBgkLastSta.getBusId());
        hql.append(" order by T.createDate desc ");
        List<TdZwBgkFlow> tdZwBgkFlowList= this.findByHql(hql.toString(),TdZwBgkFlow.class);
        if(!CollectionUtils.isEmpty(tdZwBgkFlowList)){
            //创建时间倒序取第一条记录
            TdZwBgkFlow lastRecord= tdZwBgkFlowList.get(0);
            lastRecord.setOperDate(tdZwBgkFlow.getRcvDate());
            lastRecord.setFkByOperPsnId(tdZwBgkFlow.getFkBySmtPsnId());
            //判断是否超期
            if(lastRecord.getRcvDate()!=null&&lastRecord.getOperDate()!=null){
                int num= HolidayUtil.calRemainingDate(lastRecord.getRcvDate(), lastRecord.getOperDate(),limitTime);
               if(num>0){
                   lastRecord.setIfInTime(1);
               }else{
                   lastRecord.setIfInTime(0);
               }
            }
            this.upsertEntity(lastRecord);
        }
        //保存   报告卡审批流程
        this.upsertEntity(tdZwBgkFlow);
    }


    /**
     * <p>方法描述：报告卡最新状态记录保存，审批流程表保存并更新上条记录</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-26
     **/
    @Transactional(readOnly = false)
    public void updateInsLastRecordTwo(TdZwBgkLastSta tdZwBgkLastSta,TdZwBgkFlow tdZwBgkFlow,String limitTime,Integer mainRid){
        //保存  报告卡最新状态
        this.upsertEntity(tdZwBgkLastSta);

        //获取 报告卡审批流程  上一条记录
        StringBuffer hql=new StringBuffer();
        hql.append("select T from TdZwBgkFlow T");
        hql.append(" where T.cartType=").append(tdZwBgkFlow.getCartType());
        hql.append(" and T.busId=").append(tdZwBgkLastSta.getBusId());
        hql.append(" order by T.createDate desc ");
        List<TdZwBgkFlow> tdZwBgkFlowList= this.findByHql(hql.toString(),TdZwBgkFlow.class);
        if(!CollectionUtils.isEmpty(tdZwBgkFlowList)){
            //创建时间倒序取第一条记录
            TdZwBgkFlow lastRecord= tdZwBgkFlowList.get(0);
            lastRecord.setOperDate(tdZwBgkFlow.getRcvDate());
            lastRecord.setFkByOperPsnId(tdZwBgkFlow.getFkBySmtPsnId());
            //判断是否超期
            if(lastRecord.getRcvDate()!=null&&lastRecord.getOperDate()!=null){
                int num= HolidayUtil.calRemainingDate(lastRecord.getRcvDate(), lastRecord.getOperDate(),limitTime);
                if(num>0){
                    lastRecord.setIfInTime(1);
                }else{
                    lastRecord.setIfInTime(0);
                }
            }
            this.upsertEntity(lastRecord);
        }
        //保存   报告卡审批流程
        this.upsertEntity(tdZwBgkFlow);

        //更新 档案表状态
        if(mainRid!=null && new Integer("7").equals(tdZwBgkLastSta.getState())){
            String sql="update TD_ZWJD_ARCHIVES set STATE=8,MODIFY_DATE=sysdate,MODIFY_MANID= "+Global.getUser().getRid()+" where rid="+mainRid;
            this.executeSql(sql.toString(),null);
        }
    }

    /**
     *  <p>方法描述：查询最大接收日期的流程记录</p>
     * @MethodAuthor hsj
     */
    public List<TdZwBgkFlow> selectFlowInfos(Integer bussId, Integer cardType) {
        if(null == bussId || null == cardType) {
            return null;
        }
        StringBuffer hql = new StringBuffer();
        hql.append(" select t from TdZwBgkFlow t where t.cartType =").append(cardType);
        hql.append(" and t.busId =").append(bussId);
        hql.append(" and t.operFlag in (11,13,22,31,32,41,42,43)");
        hql.append(" order by t.createDate desc");
        return this.findByHql(hql.toString());
    }

    /**
     * <p>方法描述：查询历次审核意见</p>
     * @MethodAuthor： ljy
     * @Date：2021-10-27
     **/
    @Transactional(readOnly=false)
    public List<Object[]>  findHisotryList(Integer rid){
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select T1.CART_TYPE,T1.AUDIT_ADV,T1.AUDIT_MAN,T1.CREATE_DATE,'',T3.IF_CITY_DIRECT,IF_PROV_DIRECT,T1.OPER_FLAG from TD_ZW_BGK_FLOW T1 ");
        sqlBuilder.append(" LEFT JOIN TD_ZW_HETH_CHK_SMARY T2 on T1.BUS_ID = T2.RID ");
        sqlBuilder.append(" LEFT JOIN TS_ZONE T3 ON T2.EMP_ZONE_ID = T3.RID " );
        sqlBuilder.append(" where T1.BUS_ID = ").append(rid).append(" and T1.CART_TYPE = 1 ");
        sqlBuilder.append(" and T1.AUDIT_ADV is not null and T1.AUDIT_MAN is not null ");
        sqlBuilder.append(" ORDER BY T1.CREATE_DATE desc ");
        List<Object[]> sqlResultList = this.findSqlResultList(sqlBuilder.toString());
        return sqlResultList;
    }

    @Transactional(readOnly=false)
    public List<TdZwHethChkSmaryComm> findTdZwHethChkSmaryListByIds(List<Integer> exportIds) {
        List<TdZwHethChkSmaryComm> chkSmaryList = new LinkedList<>();
        List<List<Integer>> lists =  StringUtils.splitListProxy(exportIds, 1000);
        for(List<Integer> idList : lists){
            StringBuilder sb = new StringBuilder();
            Map<String, Object> conditionMap = new HashMap<>();
            sb.append(" SELECT t FROM TdZwHethChkSmaryComm t WHERE t.rid in (:rids)");
            sb.append(" ORDER BY t.rptDate DESC, t.fkByZoneId.zoneCode,t.empCrptName,t.crptName");
            conditionMap.put("rids",idList);
            List<TdZwHethChkSmaryComm> chkSmarys = this.findDataByHqlNoPage(sb.toString(),conditionMap);
            if(CollectionUtils.isEmpty(chkSmarys)){
                continue;
            }
            for(TdZwHethChkSmaryComm chkSmaryComm : chkSmarys){
                chkSmaryComm.getChkSubList().size();
                chkSmaryComm.getJcSubList().size();
            }
            chkSmaryList.addAll(chkSmarys);
        }
        return chkSmaryList;
    }

    /**
    * <p>Description：撤销 </p>
    * <p>Author： yzz 2024-05-29 </p>
    */
    public void cancelChkSmary(Integer rid,String operFlag) {
        if(rid==null || StringUtils.isEmpty(operFlag)){
            return;
        }
        //更新最新状态表
        StringBuilder sql=new StringBuilder();
        sql.append(" update TD_ZW_BGK_LAST_STA set STATE=0 where BUS_ID=").append(rid).append(" and CART_TYPE=1");
        this.executeSql(sql.toString(),null);
        //删除流程记录
        sql=new StringBuilder();
        sql.append(" delete from TD_ZW_BGK_FLOW where BUS_ID=").append(rid).append(" and CART_TYPE=1").append(" and OPER_FLAG=").append(Integer.parseInt(operFlag));
        this.executeSql(sql.toString(),null);
    }

    /**
     * 根据RID查询职业性有害因素监测卡市直属、省直属、最新状态
     *
     * @param rid RID
     * @return 职业性有害因素监测卡市直属、省直属、最新状态
     */
    public Map<String, Integer> findHethChkSmaryLastStateByRid(Integer rid) {
        Map<String, Integer> map = new HashMap<>();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", rid);
        String sql = "SELECT S.RID, Z.IF_CITY_DIRECT, Z.IF_PROV_DIRECT " +
                "FROM TD_ZW_HETH_CHK_SMARY S " +
                "         LEFT JOIN TB_TJ_CRPT C ON S.EMP_CRPT_ID = C.RID " +
                "         LEFT JOIN TS_ZONE Z ON C.ZONE_ID = Z.RID " +
                "WHERE S.RID = :rid";
        List<Object[]> list = CollectionUtil.castList(Object[].class, this.findDataBySqlNoPage(sql, paramMap));
        if (CollectionUtils.isEmpty(list)) {
            return map;
        }
        map.put("city", ObjectUtil.convert(Integer.class, list.get(0)[1], 0));
        map.put("prov", ObjectUtil.convert(Integer.class, list.get(0)[2], 0));
        sql = "SELECT RID, STATE " +
                "FROM TD_ZW_BGK_LAST_STA " +
                "WHERE CART_TYPE = 1 AND BUS_ID = :rid " +
                "ORDER BY CREATE_DATE DESC";
        list = CollectionUtil.castList(Object[].class, this.findDataBySqlNoPage(sql, paramMap));
        if (CollectionUtils.isEmpty(list)) {
            return map;
        }
        map.put("state", ObjectUtil.convert(Integer.class, list.get(0)[1], null));
        return map;
    }

    /**
     * 职业性有害因素监测卡撤销
     *
     * @param rid   职业性有害因素监测卡rid
     * @param state 撤销后更新状态
     */
    public void revokeCheck(Integer rid, int state) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", rid);
        //查询最新地审核流程RID
        String sql = "SELECT FF.RID " +
                " FROM TD_ZW_BGK_FLOW FF " +
                " WHERE FF.CART_TYPE = 1 AND FF.BUS_ID = :rid " +
                " ORDER BY FF.CREATE_DATE DESC, FF.RID DESC";
        List<BigDecimal> list = CollectionUtil.castList(BigDecimal.class, this.findDataBySqlNoPage(sql, paramMap));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Integer flowId1 = ObjectUtil.convert(Integer.class, list.get(0), null);
        if (flowId1 != null) {
            //删除最新地审核流程
            paramMap = new HashMap<>();
            paramMap.put("flowId", flowId1);
            sql = "DELETE FROM TD_ZW_BGK_FLOW WHERE RID = :flowId";
            this.executeSql(sql, paramMap);
        }
        Integer flowId2 = null;
        if (list.size() > 1) {
            flowId2 = ObjectUtil.convert(Integer.class, list.get(1), null);
        }
        if (flowId2 != null) {
            //更新最新地审核流程
            paramMap = new HashMap<>();
            paramMap.put("flowId", flowId2);
            paramMap.put("date", new Date());
            paramMap.put("user", Global.getUser().getRid());
            sql = "UPDATE TD_ZW_BGK_FLOW SET OPER_DATE = NULL, OPER_PSN_ID = NULL, IF_IN_TIME = NULL, MODIFY_DATE = :date, MODIFY_MANID = :user WHERE RID = :flowId";
            this.executeSql(sql, paramMap);
        }
        //更新最新状态表状态
        paramMap = new HashMap<>();
        paramMap.put("rid", rid);
        paramMap.put("state", state);
        paramMap.put("date", new Date());
        paramMap.put("user", Global.getUser().getRid());
        sql = "UPDATE TD_ZW_BGK_LAST_STA SET STATE = :state, MODIFY_DATE = :date, MODIFY_MANID = :user WHERE CART_TYPE = 1 AND BUS_ID = :rid";
        this.executeSql(sql, paramMap);
    }

    /**
     * 保存有害因素监测卡文书
     *
     * @param chkSmary 有害因素监测卡
     */
    public void saveChkSmaryAnnexPath(TdZwHethChkSmaryComm chkSmary) {
        preUpdate(chkSmary);
        String sql = "UPDATE TD_ZW_HETH_CHK_SMARY SET ANNEX_PATH = :path, MODIFY_DATE = :date, MODIFY_MANID = :user WHERE RID = :rid";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", chkSmary.getRid());
        paramMap.put("path", chkSmary.getAnnexPath());
        paramMap.put("date", new Date());
        paramMap.put("user", Global.getUser().getRid());
        this.executeSql(sql, paramMap);
    }
}
