package com.chis.modules.heth.comm.entity;

import java.math.BigInteger;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 职业禁忌证人群
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置<br/>
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 */
@Entity
@Table(name = "TD_TJ_CONTRAINDLIST", uniqueConstraints = @UniqueConstraint(columnNames = {
        "BHK_ID", "CONTRAIND_ID" }))
@SequenceGenerator(name = "TdTjContraindlist_Seq", sequenceName = "TD_TJ_CONTRAINDLIST_SEQ", allocationSize = 1)
public class TdTjContraindlist implements java.io.Serializable {

    private static final long serialVersionUID = 5461885713661644311L;
    private BigInteger rid;
    private TdTjBhk tdTjBhk;
    private TsSimpleCode tsSimpleCodeByContraindId;
    private TsSimpleCode tsSimpleCodeByBadrsnId;
    private Date createDate;
    private Integer createManid;

    public TdTjContraindlist() {
    }

    public TdTjContraindlist(BigInteger rid, TdTjBhk tdTjBhk,
                             TsSimpleCode tsSimpleCodeByContraindId,
                             TsSimpleCode tsSimpleCodeByBadrsnId, Date createDate,
                             Integer createManid) {
        this.rid = rid;
        this.tdTjBhk = tdTjBhk;
        this.tsSimpleCodeByContraindId = tsSimpleCodeByContraindId;
        this.tsSimpleCodeByBadrsnId = tsSimpleCodeByBadrsnId;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjContraindlist_Seq")
    public BigInteger getRid() {
        return this.rid;
    }

    public void setRid(BigInteger rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "BHK_ID" )
    public TdTjBhk getTdTjBhk() {
        return this.tdTjBhk;
    }

    public void setTdTjBhk(TdTjBhk tdTjBhk) {
        this.tdTjBhk = tdTjBhk;
    }

    @ManyToOne
    @JoinColumn(name = "CONTRAIND_ID" )
    public TsSimpleCode getTsSimpleCodeByContraindId() {
        return this.tsSimpleCodeByContraindId;
    }

    public void setTsSimpleCodeByContraindId(
            TsSimpleCode tsSimpleCodeByContraindId) {
        this.tsSimpleCodeByContraindId = tsSimpleCodeByContraindId;
    }

    @ManyToOne
    @JoinColumn(name = "BADRSN_ID" )
    public TsSimpleCode getTsSimpleCodeByBadrsnId() {
        return this.tsSimpleCodeByBadrsnId;
    }

    public void setTsSimpleCodeByBadrsnId(TsSimpleCode tsSimpleCodeByBadrsnId) {
        this.tsSimpleCodeByBadrsnId = tsSimpleCodeByBadrsnId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

}
