package com.chis.modules.heth.comm.web;

import java.util.*;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import com.chis.modules.heth.comm.entity.IzwCrptInfo;

import com.chis.modules.system.web.IndusTypeCodePanelBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.NodeUnselectEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.TreeNode;
import org.springframework.util.CollectionUtils;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MapUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.heth.comm.entity.TbTjCrptInvest;
import com.chis.modules.heth.comm.entity.TbTjCrptInvstMining;
import com.chis.modules.heth.comm.entity.TbTjCrptMulti;
import com.chis.modules.heth.comm.service.TbTjCrptInvestServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;

@ManagedBean(name="crptInfoAuditBean")
@ViewScoped
public class CrptInfoAuditBean extends FacesEditBean implements IProcessData {
	private static final long serialVersionUID = 1L;
	/**查询条件：地区集合*/
    private List<TsZone> searchZoneList;
    /**查询条件： 地区Id */
    private Integer searchZoneId;
    /**查询条件： 地区编码 */
    private String searchZoneCode;
    /**查询条件： 地区名称 */
    private String searchZoneName;
    /**查询条件： 企业名称 */
    private String searchUnitName;
    /**查询条件： 社会信用代码 */
    private String searchCreditCode;

    /**查询条件： 行业类别*/
    private String selectIndusTypeNames;
    private String selectIndusTypeIds;
	private List<Integer> indusTypeIds;
    /**查询条件： 经济类型*/
    private String selectEconomyNames;
    private String selectEconomyIds;
    private List<Integer> economyIds;
    /**查询条件： 企业规模*/
    private List<TsSimpleCode> crptSizeList;
    private String selectCrptSizes;
    private String selectCrptSizeIds;
    /**查询条件： 状态 */
    private List<String> stateArray;
    /** 数据来源码表 */
    private List<TsSimpleCode> dataSourceList;
    /**列表 数据来源Map key 5512 中的CODE_NO value 5512中的EXTENDS3 列表页只是判断数据差异 不需要判断顺序 */
    private Map<String,String> dataTableMap;

    private String simpleCodeType;

    private Integer rid;
    private TbTjCrptInvest crptInvest;
    /**非正常生产情况*/
    private List<TsSimpleCode> noProduceStateList;
    private Map<Integer, TsSimpleCode> noProduceStateMap;
    /**开采方式*/
    private TreeNode miningSortTree;
    private List<TsSimpleCode> selectMinings;
    private String selectMiningNames;
    /**地区名称*/
    private String zoneName;
    

    /** 选择的结果集 */
    protected List<Object[]> selectEntitys;
    /**批量选择的企业行业类别是否包含采矿业*/
    boolean ifMining = false;

    /*****************地区变更相关******************/
    private Integer changeZoneId;
    private String changeZoneName;
    private String changeZoneCode;
    private String changeZoneFullName;
    private List<TsZone> changeZoneList;
    /**转出前地区*/
    private TsZone orginZone;

    /*****************信息比对相关******************/
    /**所属地区*/
    private boolean showZoneNameCompare = Boolean.FALSE;
    /**用人单位名称*/
    private boolean showCrptNameCompare = Boolean.FALSE;
    /**单位地址*/
    private boolean showAddressCompare = Boolean.FALSE;
    /**行业类别*/
    private boolean showIndusTypeCompare = Boolean.FALSE;
    /**经济类型*/
    private boolean showEconomyCompare = Boolean.FALSE;
    /**企业规模*/
    private boolean showCrptSizeCompare = Boolean.FALSE;
    /**法人*/
    private boolean showCorporateDeputyCompare = Boolean.FALSE;
    /**法人联系电话*/
    private boolean showPhoneCompare = Boolean.FALSE;
    /**联系人*/
    private boolean showLinkmanCompare = Boolean.FALSE;
    /**联系人电话*/
    private boolean showLinkphoneCompare = Boolean.FALSE;
    /**职工总人数*/
    private boolean showWorkForceCompare = Boolean.FALSE;
    /**接害总人数*/
    private boolean showHoldCardManCompare = Boolean.FALSE;

    private List<TbTjCrptMulti> infoSorceList;
    private TsSimpleCode tjSource;
    private TsSimpleCode sbSource;
    private List<TbTjCrptMulti> showCompareList;
    private Integer compareType;
    private String compareLink;

    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private TbTjCrptInvestServiceImpl investServiceImpl = SpringContextHolder.getBean(TbTjCrptInvestServiceImpl.class);

    /** 行业类别弹框Bean */
    private IndusTypeCodePanelBean codePanelBean;
    /** 选择的行业类别 */
    private TsSimpleCode selectPro;
    public CrptInfoAuditBean(){
        this.ifSQL = true;
        stateArray = new ArrayList<>();
        stateArray.add("0,5");
        stateArray.add("2");
        initZone();
        initCrptSize();
        initSortTree();
        initDataSourceInfo();
        this.noProduceStateList = this.commService.findLevelSimpleCodesByTypeId("5513");
        this.noProduceStateMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(noProduceStateList)) {
			for (TsSimpleCode t : noProduceStateList) {
				this.noProduceStateMap.put(t.getRid(), t);
			}
		}
        List<TsSimpleCode> econList = commService.findNumSimpleCodesByTypeId("5003");
		List<TsSimpleCode> indusList = commService.findNumSimpleCodesByTypeId("5002");
		this.economyIds = ridListByList(econList);
		this.indusTypeIds = ridListByList(indusList);
		this.codePanelBean = new IndusTypeCodePanelBean();
        this.searchAction();
    }
    /** 查找经济性质与行业类别最后一级的rid集合 */
	private List<Integer> ridListByList(List<TsSimpleCode> resList){
		List<Integer> resultList = new ArrayList<>();
		if(null != resList && resList.size() > 0){
			Map<String,String[]> map = new HashMap<>();
			for(TsSimpleCode t : resList){
				map.put(t.getCodeNo(),new String[]{t.getRid().toString(),t.getCodeLevelNo()});
			}

			if(null != map && !map.isEmpty()){
				List<String[]> list = new ArrayList<>();
				list.addAll(map.values());
				Iterator<String> keyIter = map.keySet().iterator();
				while(keyIter.hasNext()){
					String key = keyIter.next();
					boolean flag = true;
					for(String[] arr : list){
						String codeLevelNo = arr[1].trim();
						//判断是否有下级标签
						if(codeLevelNo.startsWith(key.trim()+".") || codeLevelNo.contains("."+key.trim()+".")){
							flag = false;
							break;
						}
					}
					if(flag){
						resultList.add(Integer.parseInt(map.get(key)[0]));
					}
				}
			}
		}
		return resultList;
	}
	private void initSortTree() {
    	List<TsSimpleCode> list = this.commService.findLevelSimpleCodesByTypeId("5514");
    	this.miningSortTree = new CheckboxTreeNode("root", null);
    	if (null != list && list.size() > 0) {
			for (TsSimpleCode t : list) {
				new CheckboxTreeNode(t, miningSortTree);
			}
		}
	}
    @Override
    public void addInit() {}

    @Override
    public void viewInit() {
    	this.crptInvest = commService.find(TbTjCrptInvest.class, rid);
    	this.selectMiningNames = null;
    	List<TbTjCrptInvstMining> list = commService.findEntityListByMainId(TbTjCrptInvstMining.class, this.rid);
		if (!CollectionUtils.isEmpty(list)) {
    		StringBuffer names = new StringBuffer();
			for (TbTjCrptInvstMining t : list) {
				names.append("，").append(t.getFkByMiningId().getCodeName());
			}
			this.selectMiningNames = names.substring(1);
		}
		if (null!=this.crptInvest.getFkByZoneId()) {
    		Short zoneType = this.crptInvest.getFkByZoneId().getZoneType();
    		String fullName = this.crptInvest.getFkByZoneId().getFullName();
    		if (zoneType.intValue()>2) {
    			int start = fullName.indexOf("_")+1;
    			this.zoneName = fullName.substring(start).replace("_", "");
			}else {
				this.zoneName = fullName;
			}
		}
    }
    

    @Override
    public void modInit() {
    	this.crptInvest = commService.find(TbTjCrptInvest.class, rid);
    	if (null==this.crptInvest.getFkByCrptSizeId()) {
    		this.crptInvest.setFkByCrptSizeId(new TsSimpleCode());
		}
    	if (null==this.crptInvest.getFkByEconomyId()) {
    		this.crptInvest.setFkByEconomyId(new TsSimpleCode());
		}
    	if (null==this.crptInvest.getFkByIndusTypeId()) {
    		this.crptInvest.setFkByIndusTypeId(new TsSimpleCode());
		}
    	if (null==this.crptInvest.getFkByNoProduceStateId()) {
    		this.crptInvest.setFkByNoProduceStateId(new TsSimpleCode());
		}
    	if (null!=this.crptInvest.getFkByZoneId()) {
    		Short zoneType = this.crptInvest.getFkByZoneId().getZoneType();
    		String fullName = this.crptInvest.getFkByZoneId().getFullName();
    		if (zoneType.intValue()>2) {
    			int start = fullName.indexOf("_")+1;
    			this.zoneName = fullName.substring(start).replace("_", "");
			}else {
				this.zoneName = fullName;
			}
		}

    	//开采方式
    	modMinings();
        // 地区变更初始化
        this.initChangZoneList();
        // 信息比对
        this.initCrptCompareInfo();
    }
    /**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2021年3月31日,initMinings
     * */
    private void modMinings() {
    	List<TbTjCrptInvstMining> list = commService.findEntityListByMainId(TbTjCrptInvstMining.class, this.rid);
    	this.selectMiningNames = null;
    	Map<Integer, TsSimpleCode> map = new HashMap<>();
    	if (!CollectionUtils.isEmpty(list)) {
    		StringBuffer names = new StringBuffer();
			for (TbTjCrptInvstMining t : list) {
				names.append("，").append(t.getFkByMiningId().getCodeName());
				map.put(t.getFkByMiningId().getRid(), t.getFkByMiningId());
			}
			this.selectMiningNames = names.substring(1);
		}
    	this.selectMinings = new ArrayList<>();
    	List<TreeNode> children = this.miningSortTree.getChildren();
		if (null!=children && children.size()>0) {
			for (TreeNode node : children) {
				TsSimpleCode data = (TsSimpleCode) node.getData();
				if (null!=map.get(data.getRid())) {
					node.setSelected(true);
					this.selectMinings.add(data);
				}else {
					node.setSelected(false);
				}
			}
		}
	}

    @Override
    public String[] buildHqls() {
        //组合查询条件
        StringBuffer conditionBuffer = new StringBuffer();
        conditionBuffer.append("WHERE 1=1 ");
        if(StringUtils.isNotBlank(this.searchZoneCode)){
            conditionBuffer.append("AND T1.ZONE_GB LIKE :zoneCode  ");
            this.paramMap.put("zoneCode", ZoneUtil.zoneSelect(this.searchZoneCode)+"%");
        }else{//地区为空 应该查询不到数据
            conditionBuffer.append("AND 1=2 ");
        }
        if(StringUtils.isNotBlank(this.searchUnitName)){
            conditionBuffer.append("AND T.CRPT_NAME LIKE :crptName  escape '\\\'  ");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchUnitName.trim()) + "%");
        }
        if(StringUtils.isNotBlank(this.searchCreditCode)){
            conditionBuffer.append("AND T.INSTITUTION_CODE LIKE :creditCode  escape '\\\'  ");
            this.paramMap.put("creditCode", "%" + StringUtils.convertBFH(this.searchCreditCode.trim()) + "%");
        }
        if(StringUtils.isNotBlank(this.selectIndusTypeIds)){
            conditionBuffer.append("AND T.INDUS_TYPE_ID IN (").append(this.selectIndusTypeIds).append(")  ");
        }
        if(StringUtils.isNotBlank(this.selectEconomyIds)){
            conditionBuffer.append("AND T.ECONOMY_ID IN (").append(this.selectEconomyIds).append(") ");
        }
        if(StringUtils.isNotBlank(this.selectCrptSizeIds)){
            conditionBuffer.append("AND T.CRPT_SIZE_ID IN (").append(this.selectCrptSizeIds).append(") ");
        }
        if(!CollectionUtils.isEmpty(this.stateArray)){
            StringBuffer stateBuffer = new StringBuffer();
            for(String state : stateArray){
                stateBuffer.append(",").append(state);
            }
            conditionBuffer.append("AND T.STATE_MARK IN (").append(stateBuffer.substring(1)).append(") ");
        }else{//未选择状态 查询状态不为4的所有数据
            conditionBuffer.append("AND T.STATE_MARK != 4 ");
        }
        // 主查询
        StringBuffer querySqlBuffer = new StringBuffer();
        querySqlBuffer.append("SELECT ")
                .append("T1.FULL_NAME AS ZONEFULLNAME, ") //0 地区全称 需要二次处理 去掉第一个下划线前的内容
                .append("T .CRPT_NAME AS CRPTNAME, ")  //1 企业名称
                .append("T .INSTITUTION_CODE AS INSTITUTIONCODE, ")//2 社会信用代码
                .append("T2.CODE_NAME AS INDUSTYPENAME, ")//3 行业类别
                .append("T3.CODE_NAME AS ECONOMYNAME, ")//4 经济类型
                .append("T4.CODE_NAME AS CRPTSIZENAME, ")//5 企业规模
                .append("T .LINKMAN2 AS LINKMAN2, ")//6 联系人
                .append("T .LINKPHONE2 AS LINKPHONE2, ")//7 联系电话
                .append("T .EXIST_STATE AS EXISTSTATE, ")//8 存在情况
                .append("T .INVEST_DATE AS INVESTDATE, ")//9 填报日期
                .append("T .STATE_MARK AS STATEMARK, ")//10 状态
                .append("T.RID AS RID, ")//11 调查表 RID
                .append("T2.RID AS INDUSTYPERID, ")//12 行业类别 RID
                .append("T3.RID AS ECONOMYRID, ")//13 经济类型 RID
                .append("T4.RID AS CRPTSIZERID, ")//14 企业规模 RID
                .append("T.IF_SUB_ORG AS IFSUBORG, ")//15 是否分支机构
                //辅助用其他信息 start 新增的信息 都加在这下边
                .append("0 AS IFSELECT, ")//16 是否置灰选择按钮 默认不置灰 置灰的数据需二次处理调整
                .append("0 AS IFRED, ")//17 是否标红 默认不标红 需标红的数据要二次处理调整成标红
                .append("0 AS IFSELECTED, ")//18 是否选择 需二次处理成默认false
                .append("0 AS IFZONERED, ")//19 地区是否标红 默认不标红 需标红的数据要二次处理调整成标红
                .append("0 AS IFNAMERED, ")//20 企业名称是否标红 默认不标红 需标红的数据要二次处理调整成标红
                .append("0 AS IFINDUSTYPERED, ")//21 行业类别是否标红 默认不标红 需标红的数据要二次处理调整成标红
                .append("0 AS IFECONOMYRED, ")//22 经济类型是否标红 默认不标红 需标红的数据要二次处理调整成标红
                .append("0 AS IFCRPTSIZERED, ")//23 企业规模是否标红 默认不标红 需标红的数据要二次处理调整成标红
                .append("T1.RID AS ZONERID, ")//24 地区RID
                .append("T2.extendS1 AS extendS1, ")//25 行业类别扩展字段1
                .append("T.PHONE, ")//26 法人联系电话
                .append("T.WORK_FORCE, ")//27 职工总人数
                .append("T.HOLD_CARD_MAN, ")//28 接害总人数
                .append("T.ADDRESS, ")//29 单位地址
                .append("0 AS IFINSTITUTIONCODERED, ")//30 社会信用代码是否标红
                .append("0 AS IFLINKPHONETWORED, ")//31 联系人电话是否标红
                //辅助用其他信息 end
                //空字段 不使用 避免出现查询中多一个逗号导致查询异常的情况
                .append("'' AS ENDSTR  ");
        querySqlBuffer.append("FROM TB_TJ_CRPT_INVEST T ")
                .append("LEFT JOIN TS_ZONE T1 ON T .ZONE_ID = T1.RID ")
                .append("LEFT JOIN TS_SIMPLE_CODE T2 ON T .INDUS_TYPE_ID = T2.RID ")
                .append("LEFT JOIN TS_SIMPLE_CODE T3 ON T .ECONOMY_ID = T3.RID ")
                .append("LEFT JOIN TS_SIMPLE_CODE T4 ON T .CRPT_SIZE_ID = T4.RID ");
        querySqlBuffer.append(conditionBuffer.toString()).append(" ");// 加入查询条件
        querySqlBuffer.append("ORDER BY T .ZONE_ID, T .CRPT_NAME "); // 排序

        //count 查询
        String countSql = "SELECT COUNT(*) FROM TB_TJ_CRPT_INVEST T LEFT JOIN TS_ZONE T1 ON T .ZONE_ID = T1.RID " + conditionBuffer.toString();

        return new String[]{querySqlBuffer.toString(), countSql};
    }

    @Override
    public void processData(List<?> list) {
        if(!CollectionUtils.isEmpty(list)){
            List<Object[]> result = (List<Object[]>) list;
            Set<String> creditCodeSet = new HashSet<>();
            for(Object[] object : result){
                // 处理地区
                String zoneFullName = null == object[0] ? null : object[0].toString();
                if(StringUtils.isNotBlank(zoneFullName)){
                    object[0] = (-1 == zoneFullName.indexOf("_")) ? zoneFullName : zoneFullName.substring(zoneFullName.indexOf("_")+1);
                }
                String creditCode = null == object[2] ? null : object[2].toString();
                if(null != creditCode){
                    creditCodeSet.add(creditCode);
                }
                //是否选择 默认设置成false
                object[18] = false;
                if(null != object[10] && !StringUtils.objectToString(object[10]).equals("0") &&
                        !StringUtils.objectToString(object[10]).equals("5")){
                    // 非待填报状态 置灰
                    object[16] = 1;
                }else{
                    if(null == object[0] || null == object[1] || null == object[2]
                            || null == object[3] || null == object[4] || null == object[5]
                            || null == object[15] || null == object[29]){
                        // 有信息点空 置灰
                        object[16] = 1;
                    }
                    if (null != object[2] && !StringUtils.isCreditCode(object[2].toString())) {//社会信用代码格式不正确
                        object[16] = 1;
                        object[30] = 1;
                        object[17] = 1;
                    }
                    if (null != object[26] && !StringUtils.vertyPhone(object[26].toString())) {//法人联系电话格式不正确
                        object[16] = 1;
                    }
                    if (null != object[7] && !StringUtils.vertyPhone(object[7].toString())) {//联系人电话格式不正确
                        object[16] = 1;
                        object[31] = 1;
                        object[17] = 1;
                    }
                    if (null != object[12] && !indusTypeIds.contains(new Integer(object[12].toString()))) {//行业类别未选择最末级
                        object[16] = 1;
                        object[21] = 1;
                        object[17] = 1;
                    }
                    if (null != object[13] && !economyIds.contains(new Integer(object[13].toString()))) {//经济类型未选择最末级
                        object[16] = 1;
                        object[22] = 1;
                        object[17] = 1;
                    }
                    if (null != object[27] && null != object[28]) {
                        Integer workForce = new Integer(object[27].toString());
                        Integer holdCardMan = new Integer(object[28].toString());
                        if (holdCardMan.compareTo(workForce)>0) {
                            object[16] = 1;
                        }
                    }
                }
            }
            //待填报的需要调整置灰选择按钮 标红数据
            if(null != dataTableMap && !dataTableMap.isEmpty() &&
                    (CollectionUtils.isEmpty(this.stateArray) || (!CollectionUtils.isEmpty(this.stateArray) && stateArray.contains("0,5"))) &&
                    !creditCodeSet.isEmpty()){
                Iterator<String> iterator = creditCodeSet.iterator();
                StringBuffer creditCodeBuffer = new StringBuffer();
                while(iterator.hasNext()){
                    creditCodeBuffer.append(",'").append(iterator.next()).append("'");
                }
                String creditCodes = creditCodeBuffer.substring(1);
                List<Object[]> multiCrptInfoList = null; // 企业信息多来源
                List<Object[]> tjCrptInfoList = null; // 体检系统企业信息
                List<Object[]> reportCrptInfoList = null; // 申报系统企业信息
                for(String queryTable : dataTableMap.values()){
                    if(queryTable.trim().equalsIgnoreCase("TB_TJ_CRPT_MULTI")){
                        multiCrptInfoList = new ArrayList<>();
                    }else if(queryTable.trim().equalsIgnoreCase("TB_TJ_CRPT")){
                        tjCrptInfoList = new ArrayList<>();
                    }else if(queryTable.trim().equalsIgnoreCase("TD_ZY_UNITBASICINFO")){
                        reportCrptInfoList = new ArrayList<>();
                    }
                }
                String crptSql = null;
                Map<String, List<Object[]>> crptDataForCompareMap = new HashMap<>();
                //地区 企业名称 行业类别 经济类型 企业规模 社会信用代码 是否分支机构
                if(null != multiCrptInfoList){
                    crptSql = "SELECT T.ZONE_ID, T.CRPT_NAME, T.INDUS_TYPE_ID, T.ECONOMY_ID, T.CRPT_SIZE_ID, " +
                            "T.INSTITUTION_CODE, T.IF_SUB_ORG " +
                            "FROM  TB_TJ_CRPT_MULTI T WHERE T.INSTITUTION_CODE IN ("+creditCodes+")";
                    multiCrptInfoList = commService.findDataBySqlNoPage(crptSql,null);
                }
                if(null != tjCrptInfoList){
                    crptSql = "SELECT T.ZONE_ID, T.CRPT_NAME, T.INDUS_TYPE_ID, T.ECONOMY_ID, T.CRPT_SIZE_ID, " +
                            "T.INSTITUTION_CODE, 0 " +
                            "FROM TB_TJ_CRPT T WHERE T.INTER_PRC_TAG = 1 AND T.INSTITUTION_CODE IN ("+creditCodes+")";
                    tjCrptInfoList = commService.findDataBySqlNoPage(crptSql,null);
                }
                if(null != reportCrptInfoList){
                    crptSql = "SELECT T.ZONE_ID, T.UNIT_NAME, T.INDUSTRY_CATE_ID, T.ECONOMIC_ID, T.ENTERPRISE_SCALE_ID, " +
                            "T.CREDIT_CODE, T.IF_BRANCH " +
                            "FROM TD_ZY_UNITBASICINFO T WHERE T.CREDIT_CODE IN ("+creditCodes+") " +
                            "ORDER BY DECLARE_DATE DESC";
                    reportCrptInfoList = commService.findDataBySqlNoPage(crptSql,null);
                    //去重
                    if(!CollectionUtils.isEmpty(reportCrptInfoList)){
                        List<Object[]> tmpList = new ArrayList<>();
                        //每页最多十八条数据 数据量少
                        for(Object[] objArr : reportCrptInfoList){
                            if(CollectionUtils.isEmpty(tmpList)){
                                tmpList.add(objArr);
                                continue;
                            }
                            boolean flag = true;
                            for(Object[] unionArr : tmpList){
                                //比较 地区 企业名称 行业类别 经济类型 企业规模 社会信用代码 是否分支机构
                                String crptName = null == objArr[1] ? null : objArr[1].toString();
                                String crptTmpName = null == unionArr[1] ? null : unionArr[1].toString();
                                String ifSub = null == objArr[6] ? "0" : objArr[6].toString();
                                String ifSubTmp = null == unionArr[6] ? "0" : unionArr[6].toString();
                                // 同一公司 只取第一条数据
                                if(StringUtils.objectToString(objArr[5]).equals(StringUtils.objectToString(unionArr[5]))
                                        && ifSub.equals(ifSubTmp)){
                                    if("1".equals(ifSub) && ((null == crptName && null == crptTmpName) ||
                                            (null != crptName && null != crptTmpName &&
                                                    StringUtils.objectToString(objArr[1])
                                                            .equals(StringUtils.objectToString(unionArr[1]))))){
                                        flag = false;
                                    }else if("0".equals(ifSub)){
                                        flag = false;
                                    }
                                }
                            }
                            if(flag){
                                tmpList.add(objArr);
                            }
                        }
                        reportCrptInfoList = tmpList;
                    }
                }

                crptDataForCompareMap = mixCrptInfoListMap(multiCrptInfoList, crptDataForCompareMap);
                crptDataForCompareMap = mixCrptInfoListMap(tjCrptInfoList, crptDataForCompareMap);
                crptDataForCompareMap = mixCrptInfoListMap(reportCrptInfoList, crptDataForCompareMap);
                if(!CollectionUtils.isEmpty(crptDataForCompareMap)){
                    //同一数据源下的企业是否唯一
                    for(Object[] objArr : result){
                        if(null != objArr[10] && !StringUtils.objectToString(objArr[10]).equals("0") &&
                                !StringUtils.objectToString(objArr[10]).equals("5")){
                            // 非待填报状态 不需要标红
                            continue;
                        }
                        String creditCode = null == objArr[2] ? null : objArr[2].toString();
                        if(null == creditCode){
                            continue;
                        }
                        List<Object[]> tmpList = crptDataForCompareMap.get(creditCode);
                        if(!CollectionUtils.isEmpty(tmpList)){
                            String ifSub = null == objArr[15] ? "0" : objArr[15].toString();
                            String zoneId = StringUtils.objectToString(objArr[24]);
                            String crptName = StringUtils.objectToString(objArr[1]);
                            String indusType = StringUtils.objectToString(objArr[12]);
                            String economyType = StringUtils.objectToString(objArr[13]);
                            String crptSizeType = StringUtils.objectToString(objArr[14]);
                            for(Object[] unionArr : tmpList){
                                //比较 地区 企业名称 行业类别 经济类型 企业规模 社会信用代码 是否分支机构
                                String ifSubTmp = null == unionArr[6] ? "0" : unionArr[6].toString();
                                String crptNameTmp = StringUtils.objectToString(unionArr[1]);
                                if(!ifSub.equals(ifSubTmp)){
                                    continue;
                                }else if(ifSub.equals("1") && !crptName.equals(crptNameTmp)){
                                    continue;
                                }
                                String zoneIdTmp = StringUtils.objectToString(unionArr[0]);
                                String indusTypeTmp = StringUtils.objectToString(unionArr[2]);
                                String economyTypeTmp = StringUtils.objectToString(unionArr[3]);
                                String crptSizeTypeTmp = StringUtils.objectToString(unionArr[4]);
                                if(null != unionArr[0] && !zoneId.equals(zoneIdTmp)){
                                    objArr[19] = 1;
                                }
                                if(null != unionArr[1] && !crptName.equals(crptNameTmp)){
                                    objArr[20] = 1;
                                }
                                if(null != unionArr[2] && !indusType.equals(indusTypeTmp)){
                                    objArr[21] = 1;
                                }
                                if(null != unionArr[3] && !economyType.equals(economyTypeTmp)){
                                    objArr[22] = 1;
                                }
                                if(null != unionArr[4] && !crptSizeType.equals(crptSizeTypeTmp)){
                                    objArr[23] = 1;
                                }
                            }
                            if(!StringUtils.objectToString(objArr[19]).equals("0") ||
                                    !StringUtils.objectToString(objArr[20]).equals("0") ||
                                    !StringUtils.objectToString(objArr[21]).equals("0") ||
                                    !StringUtils.objectToString(objArr[22]).equals("0") ||
                                    !StringUtils.objectToString(objArr[23]).equals("0")){
                                // 标红
                                objArr[17] = 1;
                                // 置灰
                                objArr[16] = 1;
                            }
                        }
                    }
                }
            }
        }
    }

    /** 获取社会信用代码 对应的 企业信息Map */
    private Map<String, List<Object[]>> mixCrptInfoListMap(List<Object[]> list,
                                                           Map<String, List<Object[]>> crptDataForCompareMap){
        if(!CollectionUtils.isEmpty(list)){
            for(Object[] objArr : list){
                String creditCode = null == objArr[5] ? null : objArr[5].toString();
                if(null == creditCode){
                    continue;
                }
                List<Object[]> crptInfoList = crptDataForCompareMap.get(creditCode);
                if(null == crptInfoList){
                    crptInfoList = new ArrayList<>();
                }
                crptInfoList.add(objArr);
                crptDataForCompareMap.put(creditCode, crptInfoList);
            }
        }
        return crptDataForCompareMap;
    }

    /** 初始化地区 */
    public void initZone(){
        TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
        }
        this.searchZoneCode = tsZone.getZoneCode();
        this.searchZoneName = tsZone.getZoneName();
        this.searchZoneId = tsZone.getRid();
        this.searchZoneList = this.systemModuleService.findZoneListICanSee(false, tsZone.getZoneCode());
    }

    /** 初始化企业规模 */
    public void initCrptSize(){
        this.crptSizeList = this.commService.findSimpleCodesByTypeId("5004");
    }

    /** 初始化数据来源信息 */
    public void initDataSourceInfo(){
        this.tjSource = null;
        this.sbSource = null;
        this.dataSourceList = this.commService.findSimpleCodesByTypeId("5512");
        if(!CollectionUtils.isEmpty(dataSourceList)){
            dataTableMap = new HashMap<>();
            for(TsSimpleCode simpleCode : dataSourceList){
                dataTableMap.put(simpleCode.getCodeNo(), simpleCode.getExtendS3());
                if(StringUtils.isNotBlank(simpleCode.getExtendS3())) {
                    if(simpleCode.getExtendS3().equals("TB_TJ_CRPT")) {
                        this.tjSource = simpleCode;
                    }
                    if(simpleCode.getExtendS3().equals("TD_ZY_UNITBASICINFO")) {
                        this.sbSource = simpleCode;
                    }
                }
            }
        }
        this.tjSource = this.commService.find(TsSimpleCode.class, this.tjSource.getRid());
        this.sbSource = this.commService.find(TsSimpleCode.class, this.sbSource.getRid());
    }

    /** 行业类别与经济类型弹出框 */
    public void selSimpleCodeAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add("5002".equals(this.simpleCodeType) ? "行业类别" : "经济类型");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add(this.simpleCodeType);
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        if ("5002".equals(this.simpleCodeType)) {
            paramList.add(this.selectIndusTypeIds);
        } else if ("5003".equals(this.simpleCodeType)) {
            paramList.add(this.selectEconomyIds);
        }
        paramMap.put("selectIds", paramList);
        if ("5002".equals(this.simpleCodeType)||"5003".equals(this.simpleCodeType)) {
            paramList = new ArrayList<String>();
            paramList.add("true");
            paramMap.put("ifShowFirstCode", paramList);
        }
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList", options, paramMap);
    }
    /** 选择行业类别与经济类型后赋值 */
    public void onSimpleCodeAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
            if (null!=list && list.size()>0) {
                StringBuffer names = new StringBuffer();
                StringBuffer ids = new StringBuffer();
                for (TsSimpleCode t : list) {
                    names.append("，").append(t.getCodeName());
                    ids.append(",").append(t.getRid());
                }
                if ("5002".equals(simpleCodeType)) {
                    //行业类别
                    this.selectIndusTypeIds = ids.substring(1);
                    this.selectIndusTypeNames = names.substring(1);
                }else if ("5003".equals(simpleCodeType)){
                    //经济类型
                    this.selectEconomyIds = ids.substring(1);
                    this.selectEconomyNames = names.substring(1);
                }
            }
        }
    }
    /**
     * <p>方法描述：清空行业类别</p>
     * */
    public void clearSimpleCode() {
        if ("5002".equals(simpleCodeType)) {
            //行业类别
            this.selectIndusTypeNames = null;
            this.selectIndusTypeIds = null;
        }else if ("5003".equals(simpleCodeType)){
            //经济类型
            this.selectEconomyNames = null;
            this.selectEconomyIds = null;
        }
    }

    /**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2021年3月29日,editSelSimpleCodeAction
     * */
    public void editSelSimpleCodeAction() {
    	Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 400);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add("5002".equals(this.simpleCodeType) ? "行业类别" : "经济类型");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add(this.simpleCodeType);
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        if ("5002".equals(this.simpleCodeType)) {
            paramList.add(this.selectIndusTypeIds);
        } else if ("5003".equals(this.simpleCodeType)) {
            paramList.add(this.selectEconomyIds);
        }
        paramMap.put("selectIds", paramList);
        if ("5002".equals(this.simpleCodeType)||"5003".equals(this.simpleCodeType)) {
            paramList = new ArrayList<String>();
            paramList.add("true");
            paramMap.put("ifShowFirstCode", paramList);
        }
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeRadioSelectListNew2", options, paramMap);
	}


	/**
	 * @Description: 行业类别弹框列表初始化
	 *
	 * @MethodAuthor pw,2022年02月7日
	 */
	public void selIndusCodeTypeAction(){
        this.codePanelBean.partInit();
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:selectedIndusCodeTable");
        dataTable.setFirst(0);
        dataTable.setRows(10);
    }

    /**
     * @Description: 行业类别选择
     *
     * @MethodAuthor pw,2022年02月7日
     */
    public void selectIndusTypeAction(){
        this.crptInvest.setFkByIndusTypeId(this.selectPro);
        RequestContext.getCurrentInstance().update("tabView:editForm:editIndusTypeName");
        RequestContext.getCurrentInstance().execute("PF('selIndusTypeDialog').hide()");
    }

    /**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2021年3月29日,onEditSimpleCodeAction
     * */
    public void onEditSimpleCodeAction(SelectEvent event) {
    	Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
        	TsSimpleCode t = (TsSimpleCode) selectedMap.get("selectPro");
            if (null!=t) {
                if ("5002".equals(simpleCodeType)) {
                    //行业类别
                	this.crptInvest.setFkByIndusTypeId(t);
                }else if ("5003".equals(simpleCodeType)){
                    //经济类型
                	this.crptInvest.setFkByEconomyId(t);
                }
            }
        }
	}
    /**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2021年3月29日,clearEditSimpleCode
     * */
    public void clearEditSimpleCode() {
    	if ("5002".equals(simpleCodeType)) {
            //行业类别
    		this.crptInvest.setFkByIndusTypeId(new TsSimpleCode());
        }else if ("5003".equals(simpleCodeType)){
            //经济类型
        	this.crptInvest.setFkByEconomyId(new TsSimpleCode());
        }
	}
    
    /**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2021年3月29日,hideMiningAction
     * */
    public void hideMiningAction() {
		this.selectMiningNames = null;
		if (this.selectMinings != null && selectMinings.size() > 0) {
			StringBuilder nameSb = new StringBuilder(); // 分类名称
			for (TsSimpleCode t : selectMinings) {
				nameSb.append("，").append(String.valueOf(t.getCodeName()));
			}
			this.selectMiningNames = nameSb.substring(1);
		}
	}
    public void onNodeSelect(NodeSelectEvent event){
        TreeNode selectNode = event.getTreeNode();
        selectNode.setSelected(true);
        // 获得第一级的节点
        if (null != selectNode) {
        	TsSimpleCode t = (TsSimpleCode) selectNode.getData();
            this.selectMinings.add(t);
        }
    }

    public void onNodeNoSelect(NodeUnselectEvent event) {
    	TreeNode selectNode = event.getTreeNode();
    	selectNode.setSelected(false);
        // 获得第一级的节点
        if (null != selectNode) {
        	TsSimpleCode t = (TsSimpleCode) selectNode.getData();
            this.selectMinings.remove(t);
        }
    }
    /**
 	 * <p>方法描述：清空开采方式</p>
 	 * @MethodAuthor qrr,2021年3月29日,clearSelectMining
     * */
    public void clearSelectMining() {
    	this.selectMiningNames = null;
		this.selectMinings = new ArrayList<>();
		List<TreeNode> children = this.miningSortTree.getChildren();
		if (null!=children && children.size()>0) {
			for (TreeNode node : children) {
				node.setSelected(false);
			}
		}
	}
    

    @Override
    public void saveAction() {
    	try {
    		if (beforeSave()) {
    			return;
    		}
    		dealInvestInfo();
    		investServiceImpl.saveTbTjCrptInvest(this.crptInvest, selectMinings);
    		this.initChangZoneList();
    		JsfUtil.addSuccessMessage("保存成功！");
    		RequestContext.getCurrentInstance().update("tabView:editForm");
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("保存失败！");
		}
    }
    /**
 	 * <p>方法描述：数据处理</p>
 	 * @MethodAuthor qrr,2021年3月30日,dealInvestInfo
     * */
    private void dealInvestInfo() {
    	this.crptInvest.setInvestDate(new Date());
    	this.crptInvest.setFkByFillUnitId(Global.getUser().getTsUnit());
    	Integer existState = this.crptInvest.getExistState();
    	if (null!=existState && 0==existState) {
    		this.crptInvest.setProduceState(null);
    		this.crptInvest.setFkByNoProduceStateId(null);
    		this.crptInvest.setOtherProduceState(null);
    		this.crptInvest.setIfHasUranium(null);
    		this.selectMiningNames = null;
    		this.selectMinings = new ArrayList<>();
		}
    	Integer produceState = this.crptInvest.getProduceState();
    	if (null!=produceState && 1==produceState) {
    		this.crptInvest.setFkByNoProduceStateId(null);
    		this.crptInvest.setOtherProduceState(null);
		}
    	TsSimpleCode produceStateId = this.crptInvest.getFkByNoProduceStateId();
    	if (null==produceStateId || null==produceStateId.getRid()) {
    		this.crptInvest.setOtherProduceState(null);
		}else {
			TsSimpleCode code = this.noProduceStateMap.get(produceStateId.getRid());
			if (!"1".equals(code.getExtendS1())) {
				this.crptInvest.setOtherProduceState(null);
			}
		}
    	TsSimpleCode indusTypeId = this.crptInvest.getFkByIndusTypeId();
    	if (null==indusTypeId || null==indusTypeId.getRid() ) {
    		this.selectMiningNames = null;
    		this.selectMinings = new ArrayList<>();
    		this.crptInvest.setIfHasUranium(null);
		}else {
			if (!"1".equals(indusTypeId.getExtendS1()) ) {
				this.selectMiningNames = null;
	    		this.selectMinings = new ArrayList<>();
	    		this.crptInvest.setIfHasUranium(null);
			}
		}
    	if (CollectionUtils.isEmpty(selectMinings)) {
    		List<TreeNode> children = this.miningSortTree.getChildren();
    		if (null!=children && children.size()>0) {
    			for (TreeNode node : children) {
    				node.setSelected(false);
    			}
    		}
		}
	}
    /**
 	 * <p>方法描述：保存前验证</p>
 	 * @MethodAuthor qrr,2021年3月29日,beforeSave
     * */
    private boolean beforeSave(){
    	boolean flag = false;
    	Integer existState = this.crptInvest.getExistState();
    	if (null!=existState && 1==existState) {
    		TsZone zone = this.crptInvest.getFkByZoneId();
    		if (null!=zone && zone.getZoneType().intValue()<4) {
    			JsfUtil.addErrorMessage("所属地区请选择至区县及以下！");
				flag = true;
			}
    		String institutionCode = this.crptInvest.getInstitutionCode();
    		if (StringUtils.isNotBlank(institutionCode)) {
    			if (!StringUtils.isCreditCode(institutionCode)) {
    				JsfUtil.addErrorMessage("社会信用代码格式不正确！");
    				flag = true;
    			}
    		}
    		//最末级
    		TsSimpleCode indusTypeId = this.crptInvest.getFkByIndusTypeId();
    		if (null!=indusTypeId && null!=indusTypeId.getRid()) {
    			if(!indusTypeIds.contains(indusTypeId.getRid())){
        			JsfUtil.addErrorMessage("行业类别必须选择至最末级！");
        			flag = true;
        		}
    		}
    		
    		TsSimpleCode economyId = this.crptInvest.getFkByEconomyId();
    		if (null!=economyId && null!=economyId.getRid()) {
    			if(!economyIds.contains(economyId.getRid())){
        			JsfUtil.addErrorMessage("经济类型必须选择至最末级！");
        			flag = true;
        		}
			}
    		
    		String phone = this.crptInvest.getPhone();
    		if (StringUtils.isNotBlank(phone)) {
    			if (!StringUtils.vertyPhone(phone)) {
    				JsfUtil.addErrorMessage("法人联系电话格式不正确！");
    				flag = true;
    			}
    		}
    		String linkphone2 = this.crptInvest.getLinkphone2();
    		if (StringUtils.isNotBlank(linkphone2)) {
    			if (!StringUtils.vertyPhone(linkphone2)) {
    				JsfUtil.addErrorMessage("联系人电话格式不正确！");
    				flag = true;
    			}
    		}
    		Integer workForce = this.crptInvest.getWorkForce();
    		Integer holdCardMan = this.crptInvest.getHoldCardMan();
    		if (null!=workForce && null!=holdCardMan) {
    			if (workForce.intValue()<holdCardMan.intValue()) {
    				JsfUtil.addErrorMessage("职工总人数应大于等于接害总人数！");
    				flag = true;
    			}
    		}
		}
    	return flag;
    }
    /**
 	 * <p>方法描述：验证空值</p>
 	 * @MethodAuthor qrr,2021年3月29日,veryData
     * */
    private boolean veryData() {
    	boolean flag = false;
    	Integer existState = this.crptInvest.getExistState();
    	if (null==existState) {
    		JsfUtil.addErrorMessage("存在情况不能为空！");
			flag = true;
		}else {
			if (1==existState) {//存在
				Integer produceState = this.crptInvest.getProduceState();
				if (null==produceState) {
		    		JsfUtil.addErrorMessage("生产情况不能为空！");
					flag = true;
				}
				if (null!=produceState && 0==produceState) {
					TsSimpleCode produceStateId = this.crptInvest.getFkByNoProduceStateId();
					if (null==produceStateId ||null==produceStateId.getRid()) {
						JsfUtil.addErrorMessage("非正常生产情况不能为空！");
						flag = true;
					}else {
						TsSimpleCode code = this.noProduceStateMap.get(produceStateId.getRid());
						if ("1".equals(code.getExtendS1())) {
							String otherProduceState = this.crptInvest.getOtherProduceState();
							if (StringUtils.isBlank(otherProduceState)) {
								JsfUtil.addErrorMessage("非正常生产其他不能为空！");
								flag = true;
							}
						}
					}
				}
				TsSimpleCode indusTypeId = this.crptInvest.getFkByIndusTypeId();
				if (null!=indusTypeId && "1".equals(indusTypeId.getExtendS1())) {
					if (CollectionUtils.isEmpty(this.selectMinings)) {
						JsfUtil.addErrorMessage("开采方式不能为空！");
						flag = true;
					}
					Integer ifHasUranium = this.crptInvest.getIfHasUranium();
					if (null==ifHasUranium) {
						JsfUtil.addErrorMessage("含铀情况不能为空！");
						flag = true;
					}
				}
				String crptName = this.crptInvest.getCrptName();
				if (StringUtils.isBlank(crptName)) {
					JsfUtil.addErrorMessage("用人单位名称不能为空！");
					flag = true;
				}
				String institutionCode = this.crptInvest.getInstitutionCode();
				if (StringUtils.isBlank(institutionCode)) {
					JsfUtil.addErrorMessage("社会信用代码不能为空！");
					flag = true;
				}
				Integer ifSubOrg = this.crptInvest.getIfSubOrg();
				if (null==ifSubOrg) {
					JsfUtil.addErrorMessage("是否分支机构不能为空！");
					flag = true;
				}
				String address = this.crptInvest.getAddress();
				if (StringUtils.isBlank(address)) {
					JsfUtil.addErrorMessage("单位地址不能为空！");
					flag = true;
				}
				if (null==indusTypeId || null==indusTypeId.getRid()) {
					JsfUtil.addErrorMessage("行业类别不能为空！");
					flag = true;
				}
				TsSimpleCode economyId = this.crptInvest.getFkByEconomyId();
				if (null==economyId || null==economyId.getRid()) {
					JsfUtil.addErrorMessage("经济类型不能为空！");
					flag = true;
				}
				TsSimpleCode crptSizeId = this.crptInvest.getFkByCrptSizeId();
				if (null==crptSizeId || null==crptSizeId.getRid()) {
					JsfUtil.addErrorMessage("企业规模不能为空！");
					flag = true;
				}
			}
		}
    	return flag;
	}
    /**
 	 * <p>方法描述：提交前验证</p>
 	 * @MethodAuthor qrr,2021年3月29日,beforeSubmit
     * */
    public void beforeSubmit() {
    	if (veryData() || beforeSave()) {
			return;
		}
    	RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show()");
	}
    /**
 	 * <p>方法描述：提交</p>
 	 * @MethodAuthor qrr,2021年3月29日,submitAction
     * */
    public void submitAction() {
    	try {
    		dealInvestInfo();
    		this.crptInvest.setStateMark(1);
    		investServiceImpl.saveTbTjCrptInvest(this.crptInvest, selectMinings);
    		this.searchAction();
    		this.backAction();
    		JsfUtil.addSuccessMessage("提交成功！");
    		RequestContext.getCurrentInstance().update("tabView");
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("提交失败！");
		}
	}
    /**
 	 * <p>方法描述：撤销</p>
 	 * @MethodAuthor qrr,2021年3月31日,cancleAction
	 * */
	public void cancleAction() {
		try {
			this.crptInvest.setStateMark(0);
			investServiceImpl.upsertEntity(this.crptInvest);
			JsfUtil.addSuccessMessage("撤销成功！");
			this.modInitAction();
			this.searchAction();
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("撤销失败！");
		}
	}

    /******************************************* 地区变更相关 *******************************************/
    /**
     * @Description : 变更地区下拉集合初始化
     * @MethodAuthor: anjing
     * @Date : 2021/3/29 11:28
     **/
    private void initChangZoneList() {
        this.orginZone = this.crptInvest.getFkByZoneId();
        this.changeZoneName = this.crptInvest.getFkByZoneId().getZoneName();
        this.changeZoneId = this.crptInvest.getFkByZoneId().getRid();
        this.changeZoneCode = this.crptInvest.getFkByZoneId().getZoneGb();
        String fullName = this.crptInvest.getFkByZoneId().getFullName();
        Short zoneType = this.crptInvest.getFkByZoneId().getZoneType();
        if(zoneType.intValue()>2) {
            int start = fullName.indexOf("_")+1;
            this.zoneName = fullName.substring(start).replace("_", "");
        } else {
            this.zoneName = fullName;
        }
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if(null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        // 默认当前登录人所在管辖地区所在省所有地区
        this.changeZoneList = this.commService.findZoneListNew(false, tsZone.getZoneGb().substring(0, 2),"3",null);
    }

    /**
    * @Description : 打开变更地区弹出框
    * @MethodAuthor: anjing
    * @Date : 2021/3/30 10:00
    **/
    public void openChangeZoneDialogAction() {
        this.initChangZoneList();
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("PF('ZoneChangeDialog').show()");
        context.update("tabView:editForm:zoneChangePanel");
    }

    /**
     * @Description : 变更地区确认
     * @MethodAuthor: anjing
     * @Date : 2021/3/29 10:44
     **/
    public void saveChangeZoneAction() {
        this.changeZoneFullName = "";
        if(StringUtils.isBlank(this.changeZoneCode)) {
            JsfUtil.addErrorMessage("变更地区不能为空！");
            return;
        }
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if(null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        // 当前登录人所在区编码前缀
        String prefix = tsZone.getZoneGb().substring(0, 6);
        TsZone changeZone = this.commService.find(TsZone.class, this.changeZoneId);
        Short zoneType = changeZone.getZoneType();
        String fullName = changeZone.getFullName();
        if (zoneType.intValue()>2) {
            int start = fullName.indexOf("_")+1;
            this.zoneName = fullName.substring(start).replace("_", "");
            this.changeZoneFullName = fullName.substring(start).replace("_", "");
        } else {
            this.zoneName = fullName;
            this.changeZoneFullName = fullName;
        }
        RequestContext context = RequestContext.getCurrentInstance();
        if(this.changeZoneCode.startsWith(prefix)) {
            this.crptInvest.setFkByZoneId(changeZone);
            context.execute("PF('ZoneChangeDialog').hide()");
            context.update("tabView:editForm:crptInfo");
        } else {
            context.execute("PF('ZoneChangeConfirmDialog').show()");
            context.update("tabView:editForm:zoneChangeConfirmDialog");
        }
    }

    /**
     * @Description : 地区变更确认
     * @MethodAuthor: anjing
     * @Date : 2021/3/29 14:56
     **/
    public void zoneChangeConfirmAction() {
        TsZone changeZone = this.commService.find(TsZone.class, this.changeZoneId);
        Short zoneType = changeZone.getZoneType();
        String fullName = changeZone.getFullName();
        if (zoneType.intValue()>2) {
            int start = fullName.indexOf("_")+1;
            this.zoneName = fullName.substring(start).replace("_", "");
        } else {
            this.zoneName = fullName;
        }
        this.selectMiningNames = "";
        this.crptInvest.setFkByZoneId(changeZone);
        this.crptInvest.setFkByOrginZoneId(this.orginZone);
        this.crptInvest.setStateMark(4);
        this.crptInvest.setInvestDate(new Date());
        this.crptInvest.setFkByFillUnitId(Global.getUser().getTsUnit());
        // 清空调查信息（企业信息不用调整）
        this.crptInvest.setExistState(null);
        this.crptInvest.setProduceState(null);
        this.crptInvest.setFkByNoProduceStateId(null);
        this.crptInvest.setOtherProduceState(null);
        this.crptInvest.setIfHasUranium(null);
        // 清空企业信息（调查）—开采方式
        this.selectMinings = new ArrayList<>();
        this.investServiceImpl.saveTbTjCrptInvest(this.crptInvest, selectMinings);
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("PF('ZoneChangeDialog').hide()");
        this.searchAction();
        this.backAction();
        JsfUtil.addSuccessMessage("变更地区成功！");
        RequestContext.getCurrentInstance().update("tabView");
    }

    /**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2021年3月27日,openReviewConfirmDialog
	 * */
	public void openInvestDialog() {
        if(this.selectEntitys == null || this.selectEntitys.size()==0){
            JsfUtil.addErrorMessage("请选择数据！");
            return ;
        }
        this.ifMining = false;
        for (Object[] obj : this.selectEntitys) {
			if (null==obj[25]) {
				continue;
			}
			if ("1".equals(obj[25].toString())) {
				this.ifMining = true;
				break;
			}
		}
        this.crptInvest = new TbTjCrptInvest();
        this.crptInvest.setFkByNoProduceStateId(new TsSimpleCode());
        this.selectMiningNames = null;
        this.selectMinings = new ArrayList<>();
        List<TreeNode> children = this.miningSortTree.getChildren();
		if (null!=children && children.size()>0) {
			for (TreeNode node : children) {
				node.setSelected(false);
			}
		}
        RequestContext.getCurrentInstance().execute("PF('InvestInfoDialog').show()");
        RequestContext.getCurrentInstance().update("tabView:mainForm:investInfoDialog");
        RequestContext.getCurrentInstance().update("tabView:mainForm:miningPanel");
    }
	/**
 	 * <p>方法描述：批量提交验证</p>
 	 * @MethodAuthor qrr,2021年3月31日,beforeInvestBatch
	 * */
	private boolean beforeInvestBatch() {
		boolean flag = false;
		Integer existState = this.crptInvest.getExistState();
		if (null==existState) {
			JsfUtil.addErrorMessage("存在情况不能为空！");
			flag = true;
		}else {
			if (1==existState) {//存在
				Integer produceState = this.crptInvest.getProduceState();
				if (null==produceState) {
					JsfUtil.addErrorMessage("生产情况不能为空！");
					flag = true;
				}
				if (null!=produceState && 0==produceState) {
					TsSimpleCode produceStateId = this.crptInvest.getFkByNoProduceStateId();
					if (null==produceStateId ||null==produceStateId.getRid()) {
						JsfUtil.addErrorMessage("非正常生产情况不能为空！");
						flag = true;
					}else {
						TsSimpleCode code = this.noProduceStateMap.get(produceStateId.getRid());
						if ("1".equals(code.getExtendS1())) {
							String otherProduceState = this.crptInvest.getOtherProduceState();
							if (StringUtils.isBlank(otherProduceState)) {
								JsfUtil.addErrorMessage("非正常生产其他不能为空！");
								flag = true;
							}
						}
					}
				}
				if (ifMining) {
					if (CollectionUtils.isEmpty(this.selectMinings)) {
						JsfUtil.addErrorMessage("开采方式不能为空！");
						flag = true;
					}
					Integer ifHasUranium = this.crptInvest.getIfHasUranium();
					if (null==ifHasUranium) {
						JsfUtil.addErrorMessage("含铀情况不能为空！");
						flag = true;
					}
				}
			}
		}
		return flag;
	}
	/**
 	 * <p>方法描述：批量填报</p>
 	 * @MethodAuthor qrr,2021年3月27日,reviewBatchAction
	 * */
	public void investBatchAction() {
		try {
			if (beforeInvestBatch()) {
				return;
			}
			StringBuffer rids = new StringBuffer();
			StringBuffer miningids = new StringBuffer();
			for (Object[] obj : this.selectEntitys) {
				rids.append(",").append(obj[11]);
				if (null!=obj[25] && "1".equals(obj[25].toString())) {
					miningids.append(",").append(obj[11]);
				}
			}
			String minigIds = null;
			if (miningids.length()>0) {
				minigIds = miningids.substring(1);
			}
	    	TsSimpleCode produceStateId = this.crptInvest.getFkByNoProduceStateId();
	    	if (null!=produceStateId && null!=produceStateId.getRid()) {
				TsSimpleCode code = this.noProduceStateMap.get(produceStateId.getRid());
				this.crptInvest.setFkByNoProduceStateId(code);
			}
			this.investServiceImpl.updateTbTjCrptInvest(rids.substring(1), crptInvest, minigIds, selectMinings);
			JsfUtil.addSuccessMessage("提交成功！");
			this.searchAction();
			RequestContext context = RequestContext.getCurrentInstance();
			context.execute("PF('InvestInfoDialog').hide()");
			context.update("tabView:mainForm");
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("提交失败！");
		}
	}
    /******************************************* 信息比对相关 *******************************************/
    /**
    * @Description : 信息比对
    * @MethodAuthor: anjing
    * @Date : 2021/3/30 11:13
    **/
    private void initCrptCompareInfo() {
        this.showZoneNameCompare = false;
        this.showCrptNameCompare = false;
        this.showAddressCompare = false;
        this.showIndusTypeCompare = false;
        this.showEconomyCompare = false;
        this.showCrptSizeCompare = false;
        this.showCorporateDeputyCompare = false;
        this.showPhoneCompare = false;
        this.showLinkmanCompare = false;
        this.showLinkphoneCompare = false;
        this.showWorkForceCompare = false;
        this.showHoldCardManCompare = false;
        this.infoSorceList = new ArrayList<>();
        this.showCompareList = new ArrayList<>();
        this.compareLink = "zoneNameCompareLink";
        if(null != this.crptInvest.getStateMark()
                && (0 == this.crptInvest.getStateMark().intValue() || 5 == this.crptInvest.getStateMark().intValue())
                && StringUtils.isNotBlank(this.crptInvest.getInstitutionCode())
                && null != dataTableMap && !dataTableMap.isEmpty()) {
            // 企业信息多来源
            List<Object[]> multiCrptInfoList = null;
            // 体检系统企业信息
            List<Object[]> tjCrptInfoList = null;
            // 申报系统企业信息
            List<Object[]> reportCrptInfoList = null;
            String institutionCode = this.crptInvest.getInstitutionCode();
            for(String queryTable : this.dataTableMap.values()){
                if(queryTable.trim().equalsIgnoreCase("TB_TJ_CRPT_MULTI")){
                    multiCrptInfoList = new ArrayList<>();
                }else if(queryTable.trim().equalsIgnoreCase("TB_TJ_CRPT")){
                    tjCrptInfoList = new ArrayList<>();
                }else if(queryTable.trim().equalsIgnoreCase("TD_ZY_UNITBASICINFO")){
                    reportCrptInfoList = new ArrayList<>();
                }
            }
            StringBuilder crptSql = new StringBuilder();
            Map<String, List<Object[]>> crptDataForCompareMap = new HashMap<>();
            //地区 企业名称 行业类别 经济类型 企业规模 社会信用代码 是否分支机构
            if(null != multiCrptInfoList) {
                crptSql = new StringBuilder();
                crptSql.append(" SELECT T.ZONE_ID, T.CRPT_NAME, T.INDUS_TYPE_ID, T.ECONOMY_ID, T.CRPT_SIZE_ID, ");
                crptSql.append(" T.INSTITUTION_CODE, T.IF_SUB_ORG, ");
                crptSql.append(" T1.ZONE_NAME, T2.CODE_NAME AS INDUS_TYPE_NAME, T3.CODE_NAME AS ECONOMY_NAME, T4.CODE_NAME AS CRPT_SIZE_NAME, ");
                crptSql.append(" T.INFO_SOURCE_ID, T5.CODE_NAME AS INFO_SOURCE_NAME, ");
                crptSql.append(" CASE WHEN T1.ZONE_TYPE >2 THEN SUBSTR(T1.FULL_NAME, INSTR(T1.FULL_NAME,'_')+1) ELSE T1.FULL_NAME END ZONE_FULL_NAME, ");
                crptSql.append(" T2.EXTENDS1, T.ADDRESS, T.CORPORATE_DEPUTY, T.PHONE, T.LINKMAN2, T.LINKPHONE2, T.WORK_FORCE, T.HOLD_CARD_MAN, T5.NUM ");
                crptSql.append(" FROM TB_TJ_CRPT_MULTI T ");
                crptSql.append(" LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID ");
                crptSql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T.INDUS_TYPE_ID = T2.RID ");
                crptSql.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.ECONOMY_ID = T3.RID ");
                crptSql.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T.CRPT_SIZE_ID = T4.RID ");
                crptSql.append(" LEFT JOIN TS_SIMPLE_CODE T5 ON T.INFO_SOURCE_ID = T5.RID ");
                crptSql.append(" WHERE T.INSTITUTION_CODE = '").append(institutionCode).append("' ");
                crptSql.append(" AND NVL(T.IF_SUB_ORG, 0) =").append(this.crptInvest.getIfSubOrg()==null?0:this.crptInvest.getIfSubOrg());
                if(null != this.crptInvest.getIfSubOrg() && this.crptInvest.getIfSubOrg().intValue() == 1) {
                    crptSql.append(" AND T.CRPT_NAME ='").append(this.crptInvest.getCrptName()).append("' ");
                }
                multiCrptInfoList = commService.findDataBySqlNoPage(crptSql.toString(),null);
            }
            if(null != tjCrptInfoList) {
                crptSql = new StringBuilder();
                if(null == this.crptInvest.getIfSubOrg() || this.crptInvest.getIfSubOrg().intValue() == 0) {
                    crptSql.append(" SELECT T.ZONE_ID, T.CRPT_NAME, T.INDUS_TYPE_ID, T.ECONOMY_ID, T.CRPT_SIZE_ID, ");
                    crptSql.append(" T.INSTITUTION_CODE, 0, ");
                    crptSql.append(" T1.ZONE_NAME, T2.CODE_NAME AS INDUS_TYPE_NAME, T3.CODE_NAME AS ECONOMY_NAME, T4.CODE_NAME AS CRPT_SIZE_NAME, ");
                    crptSql.append(" '").append(this.tjSource.getRid().toString()).append("', '").append(this.tjSource.getCodeName()).append("', ");
                    crptSql.append(" CASE WHEN T1.ZONE_TYPE >2 THEN SUBSTR(T1.FULL_NAME, INSTR(T1.FULL_NAME,'_')+1) ELSE T1.FULL_NAME END ZONE_FULL_NAME, ");
                    crptSql.append(" T2.EXTENDS1, T.ADDRESS, T.CORPORATE_DEPUTY, T.PHONE, T.LINKMAN2, T.LINKPHONE2, T.WORK_FORCE, T.HOLD_CARD_MAN, ");
                    crptSql.append(" '").append(this.tjSource.getNum()).append("' ");
                    crptSql.append(" FROM TB_TJ_CRPT T ");
                    crptSql.append(" LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID ");
                    crptSql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T.INDUS_TYPE_ID = T2.RID ");
                    crptSql.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.ECONOMY_ID = T3.RID ");
                    crptSql.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T.CRPT_SIZE_ID = T4.RID ");
                    crptSql.append(" WHERE T.INTER_PRC_TAG = 1 AND T.INSTITUTION_CODE = '").append(institutionCode).append("' ");
                    tjCrptInfoList = commService.findDataBySqlNoPage(crptSql.toString(), null);
                }
            }
            if(null != reportCrptInfoList){
                crptSql = new StringBuilder();
                crptSql.append(" SELECT T.ZONE_ID, T.UNIT_NAME, T.INDUSTRY_CATE_ID, T.ECONOMIC_ID, T.ENTERPRISE_SCALE_ID, ");
                crptSql.append(" T.CREDIT_CODE, T.IF_BRANCH, ");
                crptSql.append(" T1.ZONE_NAME, T2.CODE_NAME AS INDUS_TYPE_NAME, T3.CODE_NAME AS ECONOMY_NAME, T4.CODE_NAME AS CRPT_SIZE_NAME, ");
                crptSql.append(" '").append(this.sbSource.getRid().toString()).append("', '").append(this.sbSource.getCodeName()).append("', ");
                crptSql.append(" CASE WHEN T1.ZONE_TYPE >2 THEN SUBSTR(T1.FULL_NAME, INSTR(T1.FULL_NAME,'_')+1) ELSE T1.FULL_NAME END ZONE_FULL_NAME, ");
                crptSql.append(" T2.EXTENDS1, T.REG_ADDR, T.LEGAL_PERSON, T.LEGAL_PERSON_PHONE, T.LINK_MANAGER, T.LINK_PHONE, T.EMP_NUM, T.VICTIMS_NUM, ");
                crptSql.append(" '").append(this.sbSource.getNum()).append("' ");
                crptSql.append(" FROM TD_ZY_UNITBASICINFO T ");
                crptSql.append(" LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID ");
                crptSql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T.INDUSTRY_CATE_ID = T2.RID ");
                crptSql.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.ECONOMIC_ID = T3.RID ");
                crptSql.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T.ENTERPRISE_SCALE_ID = T4.RID ");
                crptSql.append(" WHERE T.CREDIT_CODE = '").append(institutionCode).append("' ");
                crptSql.append(" AND NVL(T.IF_BRANCH, 0) =").append(this.crptInvest.getIfSubOrg()==null?0:this.crptInvest.getIfSubOrg());
                if(null != this.crptInvest.getIfSubOrg() && this.crptInvest.getIfSubOrg().intValue() == 1) {
                    crptSql.append(" AND T.UNIT_NAME ='").append(this.crptInvest.getCrptName()).append("' ");
                }
                crptSql.append(" ORDER BY DECLARE_DATE DESC ");
                reportCrptInfoList = commService.findDataBySqlNoPage(crptSql.toString(),null);
                if(!CollectionUtils.isEmpty(reportCrptInfoList)) {
                    List<Object[]> tmpList = new ArrayList<>();
                    for(Object[] objArr : reportCrptInfoList) {
                        if(CollectionUtils.isEmpty(tmpList)) {
                            tmpList.add(objArr);
                            continue;
                        }
                    }
                    reportCrptInfoList = tmpList;
                }
            }
            crptDataForCompareMap = mixCrptInfoListMap(multiCrptInfoList, crptDataForCompareMap);
            crptDataForCompareMap = mixCrptInfoListMap(tjCrptInfoList, crptDataForCompareMap);
            crptDataForCompareMap = mixCrptInfoListMap(reportCrptInfoList, crptDataForCompareMap);
            if(!CollectionUtils.isEmpty(crptDataForCompareMap)){
                List<Object[]> tmpList = crptDataForCompareMap.get(institutionCode);
                if(!CollectionUtils.isEmpty(tmpList)){
                    String zoneId = this.crptInvest.getFkByZoneId() == null ? "" : (this.crptInvest.getFkByZoneId().getRid() == null ? "" : this.crptInvest.getFkByZoneId().getRid().toString());
                    String crptName = StringUtils.objectToString(this.crptInvest.getCrptName());
                    String indusTypeId = this.crptInvest.getFkByIndusTypeId() == null ? "" : (this.crptInvest.getFkByIndusTypeId().getRid() == null ? "" : this.crptInvest.getFkByIndusTypeId().getRid().toString());
                    String economyId = this.crptInvest.getFkByEconomyId() == null ? "" : (this.crptInvest.getFkByEconomyId().getRid() == null ? "" : this.crptInvest.getFkByEconomyId().getRid().toString());
                    String crptSizeId = this.crptInvest.getFkByCrptSizeId() == null ? "" : (this.crptInvest.getFkByCrptSizeId().getRid() == null ? "" : this.crptInvest.getFkByCrptSizeId().getRid().toString());
                    String address = StringUtils.objectToString(this.crptInvest.getAddress());
                    String corporateDeputy = StringUtils.objectToString(this.crptInvest.getCorporateDeputy());
                    String phone = StringUtils.objectToString(this.crptInvest.getPhone());
                    String linkmana = StringUtils.objectToString(this.crptInvest.getLinkman2());
                    String linkphone = StringUtils.objectToString(this.crptInvest.getLinkphone2());
                    String workForce = StringUtils.objectToString(this.crptInvest.getWorkForce());
                    String holdCardMan = StringUtils.objectToString(this.crptInvest.getHoldCardMan());
                    for(Object[] unionArr : tmpList) {
                        String infoSourceIdTmp = StringUtils.objectToString(unionArr[11]);
                        if(StringUtils.isBlank(infoSourceIdTmp)) {
                            continue;
                        }
                        String crptNameTmp = StringUtils.objectToString(unionArr[1]);
                        String zoneIdTmp = StringUtils.objectToString(unionArr[0]);
                        String zoneNameTmp = StringUtils.objectToString(unionArr[7]);
                        String fullNameTmp = StringUtils.isBlank(unionArr[13].toString()) ? "" : unionArr[13].toString().replace("_", "");
                        String indusTypeIdTmp = StringUtils.objectToString(unionArr[2]);
                        String indusTypeNameTmp = StringUtils.objectToString(unionArr[8]);
                        String economyIdTmp = StringUtils.objectToString(unionArr[3]);
                        String economyNameTmp = StringUtils.objectToString(unionArr[9]);
                        String crptSizeIdTmp = StringUtils.objectToString(unionArr[4]);
                        String crptSizeNameTmp = StringUtils.objectToString(unionArr[10]);
                        String infoSourceNameTmp = StringUtils.objectToString(unionArr[12]);
                        String addressTmp = StringUtils.objectToString(unionArr[15]);
                        String corporateDeputyTmp = StringUtils.objectToString(unionArr[16]);
                        String phoneTmp = StringUtils.objectToString(unionArr[17]);
                        String linkmanaTmp = StringUtils.objectToString(unionArr[18]);
                        String linkphoneTmp = StringUtils.objectToString(unionArr[19]);
                        String workForceTmp = StringUtils.objectToString(unionArr[20]);
                        String holdCardManTmp = StringUtils.objectToString(unionArr[21]);

                        if (StringUtils.isNotBlank(zoneIdTmp) && !zoneId.equals(zoneIdTmp)) {
                            this.showZoneNameCompare = true;
                        }
                        if (StringUtils.isNotBlank(crptNameTmp) && !crptName.equals(crptNameTmp)) {
                            this.showCrptNameCompare = true;
                        }
                        if (StringUtils.isNotBlank(addressTmp) && !address.equals(addressTmp)) {
                            this.showAddressCompare = true;
                        }
                        if (StringUtils.isNotBlank(indusTypeIdTmp) && !indusTypeId.equals(indusTypeIdTmp)) {
                            this.showIndusTypeCompare = true;
                        }
                        if (StringUtils.isNotBlank(economyIdTmp) && !economyId.equals(economyIdTmp)) {
                            this.showEconomyCompare = true;
                        }
                        if (StringUtils.isNotBlank(crptSizeIdTmp) && !crptSizeId.equals(crptSizeIdTmp)) {
                            this.showCrptSizeCompare = true;
                        }
                        if (StringUtils.isNotBlank(corporateDeputyTmp) && !corporateDeputy.equals(corporateDeputyTmp)) {
                            this.showCorporateDeputyCompare = true;
                        }
                        if (StringUtils.isNotBlank(phoneTmp) && !phone.equals(phoneTmp)) {
                            this.showPhoneCompare = true;
                        }
                        if (StringUtils.isNotBlank(linkmanaTmp) && !linkmana.equals(linkmanaTmp)) {
                            this.showLinkmanCompare = true;
                        }
                        if (StringUtils.isNotBlank(linkphoneTmp) && !linkphone.equals(linkphoneTmp)) {
                            this.showLinkphoneCompare = true;
                        }
                        if (StringUtils.isNotBlank(workForceTmp) && !workForce.equals(workForceTmp)) {
                            this.showWorkForceCompare = true;
                        }
                        if (StringUtils.isNotBlank(holdCardManTmp) && !holdCardMan.equals(holdCardManTmp)) {
                            this.showHoldCardManCompare = true;
                        }

                        TbTjCrptMulti tbTjCrptMulti = new TbTjCrptMulti();
                        if(StringUtils.isNotBlank(zoneIdTmp)) {
                            TsZone tsZone = new TsZone(Integer.valueOf(zoneIdTmp));
                            tsZone.setZoneName(zoneNameTmp);
                            tbTjCrptMulti.setFkByZoneId(tsZone);
                        }
                        tbTjCrptMulti.setCrptName(crptNameTmp);
                        TsSimpleCode fkByInfoSourceId = new TsSimpleCode(Integer.valueOf(infoSourceIdTmp));
                        fkByInfoSourceId.setCodeName(infoSourceNameTmp);
                        fkByInfoSourceId.setNum(unionArr[22] == null ? null : Integer.valueOf(unionArr[22].toString()));
                        tbTjCrptMulti.setFkByInfoSourceId(fkByInfoSourceId);
                        if(StringUtils.isNotBlank(indusTypeIdTmp)) {
                            TsSimpleCode fkByIndusTypeId = new TsSimpleCode(Integer.valueOf(indusTypeIdTmp));
                            fkByIndusTypeId.setCodeName(indusTypeNameTmp);
                            fkByIndusTypeId.setExtendS1(StringUtils.objectToString(unionArr[14]));
                            tbTjCrptMulti.setFkByIndusTypeId(fkByIndusTypeId);
                        }
                        if(StringUtils.isNotBlank(economyIdTmp)) {
                            TsSimpleCode fkByEconomyId = new TsSimpleCode(Integer.valueOf(economyIdTmp));
                            fkByEconomyId.setCodeName(economyNameTmp);
                            tbTjCrptMulti.setFkByEconomyId(fkByEconomyId);
                        }
                        if(StringUtils.isNotBlank(crptSizeIdTmp)) {
                            TsSimpleCode fkByCrptSizeId = new TsSimpleCode(Integer.valueOf(crptSizeIdTmp));
                            fkByCrptSizeId.setCodeName(crptSizeNameTmp);
                            tbTjCrptMulti.setFkByCrptSizeId(fkByCrptSizeId);
                        }
                        tbTjCrptMulti.setFullName(fullNameTmp);
                        tbTjCrptMulti.setAddress(addressTmp);
                        tbTjCrptMulti.setCorporateDeputy(corporateDeputyTmp);
                        tbTjCrptMulti.setPhone(phoneTmp);
                        tbTjCrptMulti.setLinkman2(linkmanaTmp);
                        tbTjCrptMulti.setLinkphone2(linkphoneTmp);
                        tbTjCrptMulti.setWorkForce(StringUtils.isBlank(workForceTmp)?null:Integer.valueOf(workForceTmp));
                        tbTjCrptMulti.setHoldCardMan(StringUtils.isBlank(holdCardManTmp)?null:Integer.valueOf(holdCardManTmp));
                        this.infoSorceList.add(tbTjCrptMulti);
                    }
                }
            }
        }
    }

    /**
    * @Description : 获取比对字符串
    * @MethodAuthor: anjing
    * @Date : 2021/3/31 17:32
    **/
    private String getCompareStr(IzwCrptInfo entity) {
        String compareStr = "";
        switch (this.compareType) {
            case 1 : // 所属地区
                compareStr = entity.getFkByZoneId()==null?"":(entity.getFkByZoneId().getRid()==null?"":entity.getFkByZoneId().getRid().toString());
                break;
            case 2 : // 用人单位名称
                compareStr = StringUtils.objectToString(entity.getCrptName());
                break;
            case 3 : // 单位地址
                compareStr = entity.getAddress();
                break;
            case 4 : // 行业类别
                compareStr = entity.getFkByIndusTypeId()==null?"":(entity.getFkByIndusTypeId().getRid()==null?"":entity.getFkByIndusTypeId().getRid().toString());
                break;
            case 5 : // 经济类型
                compareStr = entity.getFkByEconomyId()==null?"":(entity.getFkByEconomyId().getRid()==null?"":entity.getFkByEconomyId().getRid().toString());
                break;
            case 6 : // 企业规模
                compareStr = entity.getFkByCrptSizeId()==null?"":(entity.getFkByCrptSizeId().getRid()==null?"":entity.getFkByCrptSizeId().getRid().toString());
                break;
            case 7 : // 法人
                compareStr = StringUtils.objectToString(entity.getCorporateDeputy());
                break;
            case 8 : // 法人联系电话
                compareStr = StringUtils.objectToString(entity.getPhone());
                break;
            case 9 : // 联系人
                compareStr = StringUtils.objectToString(entity.getLinkman2());
                break;
            case 10 : // 联系人电话
                compareStr = StringUtils.objectToString(entity.getLinkphone2());
                break;
            case 11 : // 职工总人数
                compareStr = StringUtils.objectToString(entity.getWorkForce());
                break;
            case 12 : // 接害总人数
                compareStr = StringUtils.objectToString(entity.getHoldCardMan());
                break;
        }
        return compareStr;
    }

    /**
    * @Description : 获取比对List-去重
    * @MethodAuthor: anjing
    * @Date : 2021/3/31 18:13
    **/
    private List<TbTjCrptMulti> getCompareListTwo() {
        Map<String, TbTjCrptMulti> showCompareMap = new HashMap<>();
        List<TbTjCrptMulti> tbTjCrptMultiList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(this.infoSorceList)) {
            String compareStr = "";
            String compareTmpStr = "";
            compareStr = this.getCompareStr(this.crptInvest);
            for(TbTjCrptMulti entity : this.infoSorceList) {
                compareTmpStr = this.getCompareStr(entity);
                if(StringUtils.isBlank(compareTmpStr)) {
                    continue;
                }
                if(compareTmpStr.equals(compareStr)) {
                    continue;
                }
                String key = entity.getFkByInfoSourceId().getCodeName() + "&&" + compareStr;
                showCompareMap.put(key, entity);
            }
        }
        if(!CollectionUtils.isEmpty(showCompareMap)) {
            for(Map.Entry entry : showCompareMap.entrySet()) {
                tbTjCrptMultiList.add((TbTjCrptMulti) entry.getValue());
            }
        }
        return tbTjCrptMultiList;
    }

    /**
     * @Description : 获取比对List-不去重
     * @MethodAuthor: anjing
     * @Date : 2021/3/31 18:13
     **/
    private List<TbTjCrptMulti> getCompareList() {
        List<TbTjCrptMulti> tbTjCrptMultiList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(this.infoSorceList)) {
            String compareTmpStr = "";
            for(TbTjCrptMulti entity : this.infoSorceList) {
                compareTmpStr = this.getCompareStr(entity);
                if(StringUtils.isBlank(compareTmpStr)) {
                    continue;
                }
                entity.setIfselected(false);
                tbTjCrptMultiList.add(entity);
            }
        }
        // 排序
        Collections.sort(tbTjCrptMultiList, new Comparator<TbTjCrptMulti>() {
            @Override
            public int compare(TbTjCrptMulti o1, TbTjCrptMulti o2) {
                if(null != o1.getFkByInfoSourceId() && null != o2.getFkByInfoSourceId()) {
                    Integer num1 = o1.getFkByInfoSourceId().getNum();
                    Integer num2 = o2.getFkByInfoSourceId().getNum();
                    if(null != num1 && null != num1) {
                        return num1-num2;
                    } else {
                        return o1.getRid().compareTo(o2.getRid());
                    }
                }
                return 0;
            }
        });
        return tbTjCrptMultiList;
    }

    /**
    * @Description : 打开比对信息弹出框
    * @MethodAuthor: anjing
    * @Date : 2021/3/30 18:28
    **/
    public void openCompareOverlayPanel() {
        this.showCompareList = this.getCompareList();
    }

    /**
    * @Description : 比对信息切换事件
    * @MethodAuthor: anjing
    * @Date : 2021/3/30 17:41
    **/
    public void compareChangeAction(TbTjCrptMulti tbTjCrptMulti) {
        if(tbTjCrptMulti.isIfselected()) {
            String compareStr = this.getCompareStr(this.crptInvest);
            String compareTempStr = "";
            if(!CollectionUtils.isEmpty(this.showCompareList)) {
                for(TbTjCrptMulti entity : this.showCompareList) {
                    compareTempStr = this.getCompareStr(entity);
                    if(!compareStr.equals(compareTempStr)) {
                        entity.setIfselected(false);
                    }
                }
            }
            switch (this.compareType) {
                case 1 : // 所属地区
                    this.crptInvest.setFkByZoneId(tbTjCrptMulti.getFkByZoneId());
                    break;
                case 2 : // 用人单位名称
                    this.crptInvest.setCrptName(tbTjCrptMulti.getCrptName());
                    break;
                case 3 : // 单位地址
                    this.crptInvest.setAddress(tbTjCrptMulti.getAddress());
                    break;
                case 4 : // 行业类别
                    this.crptInvest.setFkByIndusTypeId(tbTjCrptMulti.getFkByIndusTypeId());
                    break;
                case 5 : // 经济类型
                    this.crptInvest.setFkByEconomyId(tbTjCrptMulti.getFkByEconomyId());
                    break;
                case 6 : // 企业规模
                    this.crptInvest.setFkByCrptSizeId(tbTjCrptMulti.getFkByCrptSizeId());
                    break;
                case 7 : // 法人
                    this.crptInvest.setCorporateDeputy(tbTjCrptMulti.getCorporateDeputy());
                    break;
                case 8 : // 法人联系电话
                    this.crptInvest.setPhone(tbTjCrptMulti.getPhone());
                    break;
                case 9 : // 联系人
                    this.crptInvest.setLinkman2(tbTjCrptMulti.getLinkman2());
                    break;
                case 10 : // 联系人电话
                    this.crptInvest.setLinkphone2(tbTjCrptMulti.getLinkphone2());
                    break;
                case 11 : // 职工总人数
                    this.crptInvest.setWorkForce(tbTjCrptMulti.getWorkForce());
                    break;
                case 12 : // 接害总人数
                    this.crptInvest.setHoldCardMan(tbTjCrptMulti.getHoldCardMan());
                    break;
            }
        }
    }

    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public Integer getSearchZoneId() {
        return searchZoneId;
    }

    public void setSearchZoneId(Integer searchZoneId) {
        this.searchZoneId = searchZoneId;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public String getSearchCreditCode() {
        return searchCreditCode;
    }

    public void setSearchCreditCode(String searchCreditCode) {
        this.searchCreditCode = searchCreditCode;
    }

    public String getSelectIndusTypeNames() {
        return selectIndusTypeNames;
    }

    public void setSelectIndusTypeNames(String selectIndusTypeNames) {
        this.selectIndusTypeNames = selectIndusTypeNames;
    }

    public String getSelectIndusTypeIds() {
        return selectIndusTypeIds;
    }

    public void setSelectIndusTypeIds(String selectIndusTypeIds) {
        this.selectIndusTypeIds = selectIndusTypeIds;
    }

    public String getSelectEconomyNames() {
        return selectEconomyNames;
    }

    public void setSelectEconomyNames(String selectEconomyNames) {
        this.selectEconomyNames = selectEconomyNames;
    }

    public String getSelectEconomyIds() {
        return selectEconomyIds;
    }

    public void setSelectEconomyIds(String selectEconomyIds) {
        this.selectEconomyIds = selectEconomyIds;
    }

    public List<TsSimpleCode> getCrptSizeList() {
        return crptSizeList;
    }

    public void setCrptSizeList(List<TsSimpleCode> crptSizeList) {
        this.crptSizeList = crptSizeList;
    }

    public String getSelectCrptSizes() {
        return selectCrptSizes;
    }

    public void setSelectCrptSizes(String selectCrptSizes) {
        this.selectCrptSizes = selectCrptSizes;
    }

    public String getSelectCrptSizeIds() {
        return selectCrptSizeIds;
    }

    public void setSelectCrptSizeIds(String selectCrptSizeIds) {
        this.selectCrptSizeIds = selectCrptSizeIds;
    }

    public List<String> getStateArray() {
        return stateArray;
    }

    public void setStateArray(List<String> stateArray) {
        this.stateArray = stateArray;
    }

    public String getSimpleCodeType() {
        return simpleCodeType;
    }

    public void setSimpleCodeType(String simpleCodeType) {
        this.simpleCodeType = simpleCodeType;
    }

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public TbTjCrptInvest getCrptInvest() {
		return crptInvest;
	}

	public void setCrptInvest(TbTjCrptInvest crptInvest) {
		this.crptInvest = crptInvest;
	}

	public List<TsSimpleCode> getNoProduceStateList() {
		return noProduceStateList;
	}

	public void setNoProduceStateList(List<TsSimpleCode> noProduceStateList) {
		this.noProduceStateList = noProduceStateList;
	}

	public String getSelectMiningNames() {
		return selectMiningNames;
	}

	public void setSelectMiningNames(String selectMiningNames) {
		this.selectMiningNames = selectMiningNames;
	}

	public Map<Integer, TsSimpleCode> getNoProduceStateMap() {
		return noProduceStateMap;
	}

	public void setNoProduceStateMap(Map<Integer, TsSimpleCode> noProduceStateMap) {
		this.noProduceStateMap = noProduceStateMap;
	}
	public TreeNode getMiningSortTree() {
		return miningSortTree;
	}
	public void setMiningSortTree(TreeNode miningSortTree) {
		this.miningSortTree = miningSortTree;
	}

    public Integer getChangeZoneId() {
        return changeZoneId;
    }

    public void setChangeZoneId(Integer changeZoneId) {
        this.changeZoneId = changeZoneId;
    }

    public String getChangeZoneName() {
        return changeZoneName;
    }

    public void setChangeZoneName(String changeZoneName) {
        this.changeZoneName = changeZoneName;
    }

    public String getChangeZoneCode() {
        return changeZoneCode;
    }

    public void setChangeZoneCode(String changeZoneCode) {
        this.changeZoneCode = changeZoneCode;
    }

    public String getChangeZoneFullName() {
        return changeZoneFullName;
    }

    public void setChangeZoneFullName(String changeZoneFullName) {
        this.changeZoneFullName = changeZoneFullName;
    }

    public List<TsZone> getChangeZoneList() {
        return changeZoneList;
    }

    public void setChangeZoneList(List<TsZone> changeZoneList) {
        this.changeZoneList = changeZoneList;
    }

    public TsZone getOrginZone() {
        return orginZone;
    }

    public void setOrginZone(TsZone orginZone) {
        this.orginZone = orginZone;
    }

    public List<Object[]> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<Object[]> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }
	public String getZoneName() {
		return zoneName;
	}
	public void setZoneName(String zoneName) {
		this.zoneName = zoneName;
	}
	public boolean isIfMining() {
		return ifMining;
	}
	public void setIfMining(boolean ifMining) {
		this.ifMining = ifMining;
	}

    public boolean isShowCrptNameCompare() {
        return showCrptNameCompare;
    }

    public void setShowCrptNameCompare(boolean showCrptNameCompare) {
        this.showCrptNameCompare = showCrptNameCompare;
    }

    public boolean isShowIndusTypeCompare() {
        return showIndusTypeCompare;
    }

    public void setShowIndusTypeCompare(boolean showIndusTypeCompare) {
        this.showIndusTypeCompare = showIndusTypeCompare;
    }

    public boolean isShowEconomyCompare() {
        return showEconomyCompare;
    }

    public void setShowEconomyCompare(boolean showEconomyCompare) {
        this.showEconomyCompare = showEconomyCompare;
    }

    public boolean isShowCrptSizeCompare() {
        return showCrptSizeCompare;
    }

    public void setShowCrptSizeCompare(boolean showCrptSizeCompare) {
        this.showCrptSizeCompare = showCrptSizeCompare;
    }

    public boolean isShowZoneNameCompare() {
        return showZoneNameCompare;
    }

    public void setShowZoneNameCompare(boolean showZoneNameCompare) {
        this.showZoneNameCompare = showZoneNameCompare;
    }

    public List<TbTjCrptMulti> getInfoSorceList() {
        return infoSorceList;
    }

    public void setInfoSorceList(List<TbTjCrptMulti> infoSorceList) {
        this.infoSorceList = infoSorceList;
    }

    public boolean isShowAddressCompare() {
        return showAddressCompare;
    }

    public void setShowAddressCompare(boolean showAddressCompare) {
        this.showAddressCompare = showAddressCompare;
    }

    public boolean isShowCorporateDeputyCompare() {
        return showCorporateDeputyCompare;
    }

    public void setShowCorporateDeputyCompare(boolean showCorporateDeputyCompare) {
        this.showCorporateDeputyCompare = showCorporateDeputyCompare;
    }

    public boolean isShowPhoneCompare() {
        return showPhoneCompare;
    }

    public void setShowPhoneCompare(boolean showPhoneCompare) {
        this.showPhoneCompare = showPhoneCompare;
    }

    public boolean isShowLinkmanCompare() {
        return showLinkmanCompare;
    }

    public void setShowLinkmanCompare(boolean showLinkmanCompare) {
        this.showLinkmanCompare = showLinkmanCompare;
    }

    public boolean isShowLinkphoneCompare() {
        return showLinkphoneCompare;
    }

    public void setShowLinkphoneCompare(boolean showLinkphoneCompare) {
        this.showLinkphoneCompare = showLinkphoneCompare;
    }

    public boolean isShowWorkForceCompare() {
        return showWorkForceCompare;
    }

    public void setShowWorkForceCompare(boolean showWorkForceCompare) {
        this.showWorkForceCompare = showWorkForceCompare;
    }

    public boolean isShowHoldCardManCompare() {
        return showHoldCardManCompare;
    }

    public void setShowHoldCardManCompare(boolean showHoldCardManCompare) {
        this.showHoldCardManCompare = showHoldCardManCompare;
    }

    public List<TbTjCrptMulti> getShowCompareList() {
        return showCompareList;
    }

    public void setShowCompareList(List<TbTjCrptMulti> showCompareList) {
        this.showCompareList = showCompareList;
    }

    public Integer getCompareType() {
        return compareType;
    }

    public void setCompareType(Integer compareType) {
        this.compareType = compareType;
    }

    public String getCompareLink() {
        return compareLink;
    }

    public void setCompareLink(String compareLink) {
        this.compareLink = compareLink;
    }

    public IndusTypeCodePanelBean getCodePanelBean() {
        return codePanelBean;
    }

    public void setCodePanelBean(IndusTypeCodePanelBean codePanelBean) {
        this.codePanelBean = codePanelBean;
    }

    public TsSimpleCode getSelectPro() {
        return selectPro;
    }

    public void setSelectPro(TsSimpleCode selectPro) {
        this.selectPro = selectPro;
    }
}
