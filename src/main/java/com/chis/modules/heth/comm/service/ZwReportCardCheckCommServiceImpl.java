package com.chis.modules.heth.comm.service;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.HolidayUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 类描述：职业卫生报告卡审核统一业务层
 * </p>
 *
 * @ClassAuthor qrr, 2019-09-16, ZwReportCardCheckServiceImpl
 */
@Service
@Transactional(readOnly = true)
public class ZwReportCardCheckCommServiceImpl extends AbstractTemplate {

    /******************************---qrr----**********************************/
    /**
     * <p>
     * 方法描述：查询最新流程信息
     * </p>
     *
     * @MethodAuthor qrr,2019年9月17日,searchNewFlow
     * */
    public TdZwBgkLastSta searchNewFlow(Integer bussId, Integer cardType) {
        if(null == bussId || null == cardType) {
            return null;
        }
        StringBuffer hql = new StringBuffer();
        hql.append("select t from TdZwBgkLastSta t where t.cartType =")
                .append(cardType).append(" and t.busId =").append(bussId);
        List<TdZwBgkLastSta> list = this.findByHql(hql.toString());
        if(null != list && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    /**
     * <p>方法描述：查询最大接收日期的流程记录</p>
     * @MethodAuthor qrr,2019年9月17日,selectMaxRevDateInfo
     * */
    public Object[] selectMaxRevDateInfo(Integer bussId, Integer cardType) {
        if(null == bussId || null == cardType) {
            return null;
        }
        StringBuffer sql = new StringBuffer();
        sql.append("select rid,max(CREATE_DATE),SMT_PSN_ID from TD_ZW_BGK_FLOW where CART_TYPE =")
                .append(cardType).append(" and BUS_ID =").append(bussId).append(" group by rid,SMT_PSN_ID ORDER BY max(CREATE_DATE) DESC");
        List<Object[]> list = this.findSqlResultList(sql.toString());
        if(null==list || list.size()==0) {
            return null;
        }
        return list.get(0);
    }

    /**
     * <p>
     * 方法描述：保存流程信息
     * bgkLastStaNew:报告卡最新状态
     * revBgkFlow:审批流程接收记录
     * backRsn：有退回原因时更新
     * flowId:根据rid更新操作记录
     * </p>
     * @MethodAuthor qrr,2019年9月17日,saveOrUpdateFlow
     * */
    @Transactional(readOnly = false)
    public void saveOrUpdateFlow(TdZwBgkLastSta bgkLastStaNew, TdZwBgkFlow revBgkFlow,
    		String backRsn,Integer flowId, TdZwOccdiscaseComm tdZwOccdiscase) {
        if(null == bgkLastStaNew || null == revBgkFlow) {
            return;
        }

        // 新增或更新报告卡最新状态
        this.upsertEntity(bgkLastStaNew);
        // 更新最新接收日期的操作记录
        if(null != flowId) {
            String limitTime = PropertyUtils.getValue("limitTime");
            Integer due = Integer.valueOf(limitTime).intValue()+1;
            //计算及时性
            TdZwBgkFlow flowInfo = find(TdZwBgkFlow.class, flowId);
            Integer flag = null;
            //查询出上一次操作流程记录，计算是否及时
            if(null != flowInfo){
                int calLimitTime = calLimitTime(flowInfo.getRcvDate(), due);
                if(calLimitTime < 0) {
                    flag =0;
                } else {
                    flag =1;
                }
            }

            StringBuffer updateSql = new StringBuffer();
            updateSql.append("update TD_ZW_BGK_FLOW set OPER_DATE = to_date('").append(DateUtils.getDate()).append("','yyyy-MM-dd')");//更新操作日期
            updateSql.append(",OPER_PSN_ID =").append(revBgkFlow.getFkBySmtPsnId().getRid());//操作人
            if (StringUtils.isNotBlank(backRsn)) {
                updateSql.append(",BACK_RSN ='").append(backRsn).append("'");//退回原因
            }
            if(null != flag ){
                updateSql.append(",IF_IN_TIME ='").append(flag).append("'");//及时性
            }
            updateSql.append(" where rid =").append(flowId);
            this.executeSql(updateSql.toString());
        }
        // 新增接收记录
        this.upsertEntity(revBgkFlow);
    }

    /**
     * <p>方法描述：计算超期</p>
     * @MethodAuthor qrr,2019年9月16日,calLimitTime
     * */
    private int calLimitTime(Date sDate, Integer due) {
        String limitTime = PropertyUtils.getValue("limitTime");
        if (null==sDate) {
            return 0;
        }
        Date date = DateUtils.addDateByWorkDay(sDate, due);
        if (date.after(new Date())) {
            //剩余多少天
            int day = DateUtils.getDistanceOfWorkDay(new Date(), date);
            if(day == 0) {
                return -1;
            }
            return day>Integer.valueOf(limitTime).intValue()?Integer.valueOf(limitTime).intValue():day;
        }else {
            //超期
            return -1;
        }
    }
    
    /**
     * <p>方法描述：查询最大接收日期的流程记录</p>
     * @MethodAuthor qrr,2019年9月17日,selectMaxRevDateInfo
     * */
    public List<TdZwBgkFlow> selectFlowInfos(Integer bussId, Integer cardType) {
        if(null == bussId || null == cardType) {
            return null;
        }
        StringBuffer hql = new StringBuffer();
        hql.append(" select t from TdZwBgkFlow t where t.cartType =").append(cardType);
        hql.append(" and t.busId =").append(bussId);
        hql.append(" and t.operFlag in (11,13,22,31,32,41,42,43)");
        hql.append(" order by t.createDate desc");
        return this.findByHql(hql.toString());
    }

    /******************************---rcj----**********************************/
    /**
     * <p>方法描述：撤销更新操作</p>
     * @MethodAuthor rcj,2019年9月17日,selectMaxRevDateInfo
     * */
    @Transactional(readOnly=false)
    public void revokeAction(Integer mainId, TdZwBgkLastSta newFlow) {
        //更新最新记录表
        upsertEntity(newFlow);
        //更新诊断流程主表状态
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE TD_ZW_OCCDISCASE SET STATE_MARK = 6 WHERE RID = ").append(mainId);
        executeSql(sql.toString());
    }

    /******************************---mx----**********************************/

    /**
     * <p>
     * 方法描述：查询最新流程信息
     * </p>
     *
     * @MethodAuthor rcj,2020年11月2日,searchTdZwyjBsnCheckByMainId
     * */
    public TdZwyjBsnCheck searchTdZwyjBsnCheckByMainId(Integer bussId) {
        if (null == bussId ) {
            return null;
        }
        StringBuffer hql = new StringBuffer();
        hql.append("select t from TdZwyjBsnCheck t where  t.fkByMainId.rid =").append(bussId);
        List<TdZwyjBsnCheck> list = this.findByHql(hql.toString());
        if (null != list && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }


    /**
     * <p>方法描述：撤销更新操作</p>
     * @MethodAuthor rcj,2020年9月17日,selectMaxRevDateInfo
     * */
    @Transactional(readOnly=false)
    public void revokeTdZwyjBsnCheckAction(TdZwyjBsnCheck newFlow) {
        //更新最新记录表
        upsertEntity(newFlow);
        //更新诊断流程主表状态
        StringBuffer sql = new StringBuffer();
        sql.append(" UPDATE TD_ZW_OCCDISCASE SET STATE_MARK = 6 WHERE RID = ").append(newFlow.getFkByMainId().getRid());
        executeSql(sql.toString());
    }

    /**
     * <p>方法描述：根据id查询疑似职业病报告卡</p>
     * @MethodAuthor： yzz
     * @Date：2022-05-09
     **/
    public TdZwYszybRpt findYszybRpt(Integer rid) {
        TdZwYszybRpt tdZwYszybRpt=this.find(TdZwYszybRpt.class, rid);
        if(tdZwYszybRpt!=null){
            tdZwYszybRpt.getBadrsnList().size();
        }
        return tdZwYszybRpt;
    }
}
