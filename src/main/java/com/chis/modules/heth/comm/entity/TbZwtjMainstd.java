package com.chis.modules.heth.comm.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 职业健康监护方案标准（GBZ-188）
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置<br/>
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 */
@Entity
@Table(name = "TB_ZWTJ_MAINSTD", uniqueConstraints = @UniqueConstraint(columnNames = {
        "BADRSN_ID", "WORK_STATEID" }))
@SequenceGenerator(name = "TbZwtjMainstd_Seq", sequenceName = "TB_ZWTJ_MAINSTD_SEQ", allocationSize = 1)
public class TbZwtjMainstd implements java.io.Serializable {

    private static final long serialVersionUID = 195146935705664327L;
    private Integer rid;
    private TsSimpleCode tsSimpleCodeByBadrsnId;
    private TsSimpleCode tsSimpleCodeByWorkStateid;
    private short stopTag;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private List<TbZwtjOccdises> tbZwtjOccdiseses = new ArrayList<TbZwtjOccdises>(
            0);
    private List<TbZwtjAskitems> tbZwtjAskitemses = new ArrayList<TbZwtjAskitems>(
            0);
    private List<TbZwtjJjzs> tbZwtjJjzses = new ArrayList<TbZwtjJjzs>(0);
    private List<TbZwtjSchemeItems> tbZwtjSchemeItemses = new ArrayList<TbZwtjSchemeItems>(
            0);
    private List<TbZwtjBswake> tbZwtjBswakes = new ArrayList<TbZwtjBswake>(0);

    public TbZwtjMainstd() {
    }

    public TbZwtjMainstd(Integer rid) {
        this.rid = rid;
    }

    public TbZwtjMainstd(Integer rid, TsSimpleCode tsSimpleCodeByBadrsnId,
                         TsSimpleCode tsSimpleCodeByWorkStateid, short stopTag,
                         Date createDate, Integer createManid) {
        this.rid = rid;
        this.tsSimpleCodeByBadrsnId = tsSimpleCodeByBadrsnId;
        this.tsSimpleCodeByWorkStateid = tsSimpleCodeByWorkStateid;
        this.stopTag = stopTag;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    /** full constructor */
    public TbZwtjMainstd(Integer rid, TsSimpleCode tsSimpleCodeByBadrsnId,
                         TsSimpleCode tsSimpleCodeByWorkStateid, short stopTag,
                         Date createDate, Integer createManid, Date modifyDate,
                         Integer modifyManid, List<TbZwtjOccdises> tbZwtjOccdiseses,
                         List<TbZwtjAskitems> tbZwtjAskitemses,
                         List<TbZwtjJjzs> tbZwtjJjzses,
                         List<TbZwtjSchemeItems> tbZwtjSchemeItemses,
                         List<TbZwtjBswake> tbZwtjBswakes) {
        this.rid = rid;
        this.tsSimpleCodeByBadrsnId = tsSimpleCodeByBadrsnId;
        this.tsSimpleCodeByWorkStateid = tsSimpleCodeByWorkStateid;
        this.stopTag = stopTag;
        this.createDate = createDate;
        this.createManid = createManid;
        this.modifyDate = modifyDate;
        this.modifyManid = modifyManid;
        this.tbZwtjOccdiseses = tbZwtjOccdiseses;
        this.tbZwtjAskitemses = tbZwtjAskitemses;
        this.tbZwtjJjzses = tbZwtjJjzses;
        this.tbZwtjSchemeItemses = tbZwtjSchemeItemses;
        this.tbZwtjBswakes = tbZwtjBswakes;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbZwtjMainstd_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "BADRSN_ID" )
    public TsSimpleCode getTsSimpleCodeByBadrsnId() {
        return this.tsSimpleCodeByBadrsnId;
    }

    public void setTsSimpleCodeByBadrsnId(TsSimpleCode tsSimpleCodeByBadrsnId) {
        this.tsSimpleCodeByBadrsnId = tsSimpleCodeByBadrsnId;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "WORK_STATEID" )
    public TsSimpleCode getTsSimpleCodeByWorkStateid() {
        return this.tsSimpleCodeByWorkStateid;
    }

    public void setTsSimpleCodeByWorkStateid(
            TsSimpleCode tsSimpleCodeByWorkStateid) {
        this.tsSimpleCodeByWorkStateid = tsSimpleCodeByWorkStateid;
    }

    @Column(name = "STOP_TAG" )
    public short getStopTag() {
        return this.stopTag;
    }

    public void setStopTag(short stopTag) {
        this.stopTag = stopTag;
    }

    @Column(name = "CREATE_DATE" )
    @Temporal(TemporalType.DATE)
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Column(name = "MODIFY_DATE")
    @Temporal(TemporalType.DATE)
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbZwtjMainstd")
    public List<TbZwtjOccdises> getTbZwtjOccdiseses() {
        return this.tbZwtjOccdiseses;
    }

    public void setTbZwtjOccdiseses(List<TbZwtjOccdises> tbZwtjOccdiseses) {
        this.tbZwtjOccdiseses = tbZwtjOccdiseses;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbZwtjMainstd")
    public List<TbZwtjAskitems> getTbZwtjAskitemses() {
        return this.tbZwtjAskitemses;
    }

    public void setTbZwtjAskitemses(List<TbZwtjAskitems> tbZwtjAskitemses) {
        this.tbZwtjAskitemses = tbZwtjAskitemses;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbZwtjMainstd")
    public List<TbZwtjJjzs> getTbZwtjJjzses() {
        return this.tbZwtjJjzses;
    }

    public void setTbZwtjJjzses(List<TbZwtjJjzs> tbZwtjJjzses) {
        this.tbZwtjJjzses = tbZwtjJjzses;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbZwtjMainstd")
    public List<TbZwtjSchemeItems> getTbZwtjSchemeItemses() {
        return this.tbZwtjSchemeItemses;
    }

    public void setTbZwtjSchemeItemses(
            List<TbZwtjSchemeItems> tbZwtjSchemeItemses) {
        this.tbZwtjSchemeItemses = tbZwtjSchemeItemses;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbZwtjMainstd")
    public List<TbZwtjBswake> getTbZwtjBswakes() {
        return this.tbZwtjBswakes;
    }

    public void setTbZwtjBswakes(List<TbZwtjBswake> tbZwtjBswakes) {
        this.tbZwtjBswakes = tbZwtjBswakes;
    }
}
