package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsUnit;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-10-30
 */
@Entity
@Table(name = "TD_ZWYJ_BSN_CHECK")
@SequenceGenerator(name = "TdZwyjBsnCheck", sequenceName = "TD_ZWYJ_BSN_CHECK_SEQ", allocationSize = 1)
public class TdZwyjBsnCheck implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwyjBsnBhk fkByMainId;
	private Integer state;
	private Integer ifNormal;
	private Date countySmtDate;
	private String countyAuditAdv;
	private TsUnit fkByCountyChkOrgid;
	private Date citySmtDate;
	private Integer cityRst;
	private String cityAuditAdv;
	private TsUnit fkByCiytChkOrgid;
	private Date proSmtDate;
	private Integer cityRst2;
	private String proAuditAdv;
	private TsUnit fkByProChkOrgid;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	public TdZwyjBsnCheck() {
	}

	public TdZwyjBsnCheck(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjBsnCheck")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwyjBsnBhk getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwyjBsnBhk fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Column(name = "IF_NORMAL")	
	public Integer getIfNormal() {
		return ifNormal;
	}

	public void setIfNormal(Integer ifNormal) {
		this.ifNormal = ifNormal;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "COUNTY_SMT_DATE")			
	public Date getCountySmtDate() {
		return countySmtDate;
	}

	public void setCountySmtDate(Date countySmtDate) {
		this.countySmtDate = countySmtDate;
	}	
			
	@Column(name = "COUNTY_AUDIT_ADV")	
	public String getCountyAuditAdv() {
		return countyAuditAdv;
	}

	public void setCountyAuditAdv(String countyAuditAdv) {
		this.countyAuditAdv = countyAuditAdv;
	}	
			
	@ManyToOne
	@JoinColumn(name = "COUNTY_CHK_ORGID")			
	public TsUnit getFkByCountyChkOrgid() {
		return fkByCountyChkOrgid;
	}

	public void setFkByCountyChkOrgid(TsUnit fkByCountyChkOrgid) {
		this.fkByCountyChkOrgid = fkByCountyChkOrgid;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "CITY_SMT_DATE")			
	public Date getCitySmtDate() {
		return citySmtDate;
	}

	public void setCitySmtDate(Date citySmtDate) {
		this.citySmtDate = citySmtDate;
	}	
			
	@Column(name = "CITY_RST")	
	public Integer getCityRst() {
		return cityRst;
	}

	public void setCityRst(Integer cityRst) {
		this.cityRst = cityRst;
	}	
			
	@Column(name = "CITY_AUDIT_ADV")	
	public String getCityAuditAdv() {
		return cityAuditAdv;
	}

	public void setCityAuditAdv(String cityAuditAdv) {
		this.cityAuditAdv = cityAuditAdv;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CIYT_CHK_ORGID")			
	public TsUnit getFkByCiytChkOrgid() {
		return fkByCiytChkOrgid;
	}

	public void setFkByCiytChkOrgid(TsUnit fkByCiytChkOrgid) {
		this.fkByCiytChkOrgid = fkByCiytChkOrgid;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "PRO_SMT_DATE")			
	public Date getProSmtDate() {
		return proSmtDate;
	}

	public void setProSmtDate(Date proSmtDate) {
		this.proSmtDate = proSmtDate;
	}	
			
	@Column(name = "CITY_RST2")	
	public Integer getCityRst2() {
		return cityRst2;
	}

	public void setCityRst2(Integer cityRst2) {
		this.cityRst2 = cityRst2;
	}	
			
	@Column(name = "PRO_AUDIT_ADV")	
	public String getProAuditAdv() {
		return proAuditAdv;
	}

	public void setProAuditAdv(String proAuditAdv) {
		this.proAuditAdv = proAuditAdv;
	}	
			
	@ManyToOne
	@JoinColumn(name = "PRO_CHK_ORGID")			
	public TsUnit getFkByProChkOrgid() {
		return fkByProChkOrgid;
	}

	public void setFkByProChkOrgid(TsUnit fkByProChkOrgid) {
		this.fkByProChkOrgid = fkByProChkOrgid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

}