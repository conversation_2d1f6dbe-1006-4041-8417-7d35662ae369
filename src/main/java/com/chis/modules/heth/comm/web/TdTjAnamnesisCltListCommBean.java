package com.chis.modules.heth.comm.web;

import java.util.ArrayList;
import java.util.List;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdTjAnamnesisClt;
import com.chis.modules.heth.comm.entity.TdTjBhkClt;
import com.chis.modules.heth.comm.service.TdTjAnamnesisCltCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;

/**
 * @Description : 问诊既往病史表（数据录入）-列表页
 * @ClassAuthor : anjing
 * @Date : 2019/5/18 15:53
 **/
public class TdTjAnamnesisCltListCommBean extends FacesEditBean {

    private Integer rid;

    /**体检问诊：既往史列表*/
    private List<TdTjAnamnesisClt> anamnesisHisList = new ArrayList<>();
    /**添加页面：既往史*/
    private  TdTjAnamnesisClt tdTjAnamnesisClt = new TdTjAnamnesisClt();

    /**关联：体检信息（录入数据）*/
    private TdTjBhkClt fkByBhkId;

    /**添加页面：疾病名称下拉*/
    private List<String> hstnamList = new ArrayList<>();

    private TdTjAnamnesisCltCommServiceImpl tdTjAnamnesisCltService = SpringContextHolder.getBean(TdTjAnamnesisCltCommServiceImpl.class);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    /** 体检录入既往史是否可以填【无】*/
    private Boolean ifFstChkNeedAnamnesis;

    public TdTjAnamnesisCltListCommBean() {
        ifFstChkNeedAnamnesis = "1".equals(commService.findParamValue("IF_CHK_ANAMNESIS"));
    }

    /**
     * @Description : 参数初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 18:27
     **/
    public void initParam() {
        if(null != this.fkByBhkId && null != this.fkByBhkId.getRid()) {
            // 初始化既往史列表
            this.initAnamnesisList();
            // 初始化疾病名称下拉列表
            this.initHstnamList();
        }
    }

    /**
     * @Description : 既往史列表-初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 16:01
     **/
    private void initAnamnesisList() {
        this.anamnesisHisList = this.tdTjAnamnesisCltService.selectAnamnesisCltListByBhkId(this.fkByBhkId.getRid());
    }

    /**
     * @Description : 疾病名称下拉初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 18:27
     **/
    private void initHstnamList() {
        List<TsSimpleCode> tsSimpleCodeList = commService.findallSimpleCodesByTypeIdAndExtends1("5301", 1);
        for(TsSimpleCode entity : tsSimpleCodeList) {
            this.hstnamList.add(entity.getCodeName());
        }
    }

    /**
     * @Description : 添加初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 16:00
     **/
    @Override
    public void addInit() {
        this.tdTjAnamnesisClt = new TdTjAnamnesisClt();
        this.tdTjAnamnesisClt.setFkByBhkId(this.fkByBhkId);
    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {
        this.tdTjAnamnesisClt = this.tdTjAnamnesisCltService.find(TdTjAnamnesisClt.class, this.rid);
        if(StringUtils.isNotBlank(this.tdTjAnamnesisClt.getHstdat())) {
            this.tdTjAnamnesisClt.setHstDate(DateUtils.parseDate(this.tdTjAnamnesisClt.getHstdat()));
        }
    }

    /**
     * @Description : 保存前数据校验
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 16:05
     **/
    private boolean beforeSaveAction(TdTjAnamnesisClt tdTjAnamnesisClt) {
        boolean flag = true;
        String id = "tabView\\\\:editTabView\\\\:exmsdataListForm\\\\:";
        if(StringUtils.isBlank(tdTjAnamnesisClt.getHstnam())) {
            Global.markErrorInfo(true, id+"hstnam", "疾病名称不能为空！");
            flag = false;
        }
        return flag;
    }

    @Override
    public void saveAction() {
        if(!this.beforeSaveAction(this.tdTjAnamnesisClt)) {
            return;
        }
        try {
            if(null != this.tdTjAnamnesisClt.getHstDate()) {
                this.tdTjAnamnesisClt.setHstdat(DateUtils.formatDate(this.tdTjAnamnesisClt.getHstDate()));
            }
            this.tdTjAnamnesisCltService.upsertEntity(this.tdTjAnamnesisClt);
            RequestContext.getCurrentInstance().execute("PF('AnamnesisHisDialog').hide()");
            this.initAnamnesisList();
            JsfUtil.addSuccessMessage("保存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
     * @Description : 执行删除
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 16:07
     **/
    public void deleteAction() {
        try {
            this.tdTjAnamnesisCltService.deleteEntity(TdTjAnamnesisClt.class, this.tdTjAnamnesisClt.getRid());
            this.initAnamnesisList();
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("已被引用,无法删除！");
        }
    }

    @Override
    public String[] buildHqls() {
        return new String[0];
    }

    public List<TdTjAnamnesisClt> getAnamnesisHisList() {
        return anamnesisHisList;
    }

    public void setAnamnesisHisList(List<TdTjAnamnesisClt> anamnesisHisList) {
        this.anamnesisHisList = anamnesisHisList;
    }

    public TdTjAnamnesisClt getTdTjAnamnesisClt() {
        return tdTjAnamnesisClt;
    }

    public void setTdTjAnamnesisClt(TdTjAnamnesisClt tdTjAnamnesisClt) {
        this.tdTjAnamnesisClt = tdTjAnamnesisClt;
    }

    public TdTjBhkClt getFkByBhkId() {
        return fkByBhkId;
    }

    public void setFkByBhkId(TdTjBhkClt fkByBhkId) {
        this.fkByBhkId = fkByBhkId;
    }

    public List<String> getHstnamList() {
        return hstnamList;
    }

    public void setHstnamList(List<String> hstnamList) {
        this.hstnamList = hstnamList;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Boolean getIfFstChkNeedAnamnesis() {
        return ifFstChkNeedAnamnesis;
    }

    public void setIfFstChkNeedAnamnesis(Boolean ifFstChkNeedAnamnesis) {
        this.ifFstChkNeedAnamnesis = ifFstChkNeedAnamnesis;
    }
}
