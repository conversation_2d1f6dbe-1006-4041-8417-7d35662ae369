package com.chis.modules.heth.comm.javabean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.faces.component.html.HtmlOutputText;
import javax.faces.context.FacesContext;

import org.primefaces.component.column.Column;
import org.primefaces.component.fieldset.Fieldset;
import org.primefaces.component.outputpanel.OutputPanel;
import org.primefaces.component.panelgrid.PanelGrid;
import org.primefaces.component.row.Row;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbTjCrptIndepend;
import com.chis.modules.heth.comm.entity.TdTjBhk;
import com.chis.modules.heth.comm.entity.TdTjEmhistory;
import com.chis.modules.heth.comm.entity.TdTjExmsdata;
import com.chis.modules.heth.comm.service.HethStaQueryCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;

/**
 * <p>类描述：人员体检信息页面初始化Bean</p>
 * @ClassAuthor qrr,2020年3月3日,TdTjBhkInfoBean
 * */
@Deprecated
public class TdTjBhkInfoNewBean {
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private HethStaQueryCommServiceImpl hethStaQueryServiceImpl = SpringContextHolder
			.getBean(HethStaQueryCommServiceImpl.class);

	private Integer rid;
	/**
	 * 体检显示页面的实例
	 */
	private TdTjBhk tdTjBhkShow;
	/**
	 * 问诊
	 */
	private TdTjExmsdata tdTjExmsdata = new TdTjExmsdata();
	/**
	 * 职业史集合
	 */
	private List<TdTjEmhistory> tdTjEmhistoryList;
	/**
	 * 放射史集合
	 */
	private List<TdTjEmhistory> tdTjEmhistoryList2;
	/**
	 * 用人单位的联系人和联系电话
	 */
	private TbTjCrptIndepend tbTjCrptIndepend = new TbTjCrptIndepend();
	/**
	 * 体检项目的布局表格
	 */
	private OutputPanel archivePanel = (OutputPanel) JsfUtil.getApplication().createComponent(
			OutputPanel.COMPONENT_TYPE);
	// 系统参数

	/**是否管理机构，true：身份证号隐藏显示*/
	private boolean ifManagedOrg;

	private boolean ifRmkSubCover;
	//只显示基础信息
	private boolean ifShowBaseOnly = false;

	public TdTjBhkInfoNewBean() {
		this.initParam();
	}

	public TdTjBhkInfoNewBean(boolean ifRmkSubCover) {
		this.ifRmkSubCover = ifRmkSubCover;
		this.initParam();
	}

	private void initParam() {

	}

	/**
	 * <p>方法描述：查询体检相关信息</p>
	 * @MethodAuthor qrr,2020年3月3日,initBhkInfo
	 * */
	public void initBhkInfo() {
		// 查看选定的体检的详情,转到体检详情页面
		tdTjBhkShow = hethStaQueryServiceImpl.findTdTjBhkById(rid);
		//用人单位的联系人和联系电话
		if(tdTjBhkShow!=null && tdTjBhkShow.getTbTjCrpt()!=null && tdTjBhkShow.getTbTjCrpt().getRid()!=null && tdTjBhkShow.getTbTjSrvorg()!=null
				&&tdTjBhkShow.getTbTjSrvorg().getTsUnit()!=null && tdTjBhkShow.getTbTjSrvorg().getTsUnit().getRid()!=null){
			this.tbTjCrptIndepend = hethStaQueryServiceImpl.findTbTjCrptIndepend(tdTjBhkShow.getTbTjCrpt().getRid(),tdTjBhkShow.getTbTjSrvorg().getTsUnit().getRid());
		}
		tdTjExmsdata =  new TdTjExmsdata();
		String idc = tdTjBhkShow.getIdc();
		//加密身份证号
		if (this.ifManagedOrg) {
			if (null!=tdTjBhkShow.getPsnType()) {
				if (1==tdTjBhkShow.getPsnType()) {
					//加密身份证号
					String encryptIdc = StringUtils.encryptIdc(idc);
					tdTjBhkShow.setIdc(encryptIdc);
				}else if (2==tdTjBhkShow.getPsnType()) {
					if(StringUtils.isNotBlank(idc)){
						if (idc.trim().length()>4) {
							tdTjBhkShow.setIdc(idc.trim().substring(0,idc.trim().length()-4)+"****");
						}else {
							tdTjBhkShow.setIdc("****");
						}
					}
				}
			}
		}
		// 问诊项目
		List<TdTjExmsdata> tdTjExmsdataList = tdTjBhkShow.getTdTjExmsdatas();
		if (null != tdTjExmsdataList && tdTjExmsdataList.size() > 0) {
			tdTjExmsdata = tdTjExmsdataList.get(0);
		}
		// 放射史和非放射史数据分开
		List<TdTjEmhistory> list = tdTjBhkShow.getTdTjEmhistories();
		tdTjEmhistoryList = new ArrayList<TdTjEmhistory>();
		tdTjEmhistoryList2 = new ArrayList<TdTjEmhistory>();
		if (null != list && list.size() > 0) {
			for (TdTjEmhistory t : list) {
				if (t.getHisType() == 1) {
					tdTjEmhistoryList2.add(t);
				} else {
					tdTjEmhistoryList.add(t);
				}
			}
		}
		// 创建动态组件
 		Map<TsSimpleCode, List<Object[]>> dataMap = hethStaQueryServiceImpl.findProjectInfoNewBHK(this.rid);
		this.createOutPanel(dataMap);
		FacesContext.getCurrentInstance().renderResponse();

	}
	/**
	 * 构建outputPanel，依次页面结构层次是：outputpanel, panel, panelGrid, row, column,控件
	 *
	 * @param dataMap
	 *            组件数据集合
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private void createOutPanel(Map<TsSimpleCode, List<Object[]>> dataMap) {
		archivePanel.getChildren().clear();
		if (null != dataMap && dataMap.size() > 0) {
			Set<TsSimpleCode> tsSimpleCodeSet = dataMap.keySet();
			// 遍历数据集合，当集合中出现一级分类时，就创建一个Fieldset
			for (TsSimpleCode t : tsSimpleCodeSet) {
				if (t.getCodeLevelNo().split("\\.").length == 1) {
					createPanel(t, dataMap);
				}
			}
		}
	}

	/**
	 * 创建Fieldset
	 *
	 * @param legend
	 *            标题
	 * @param style
	 *            样式
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private Fieldset createFieldset(String legend, String style) {
		Fieldset fieldset = (Fieldset) JsfUtil.getApplication().createComponent(Fieldset.COMPONENT_TYPE);
		JsfUtil.getApplication().createComponent(PanelGrid.COMPONENT_TYPE);
		fieldset.setLegend(legend);
		fieldset.setStyle(style);
		fieldset.setToggleable(true);
		fieldset.setToggleSpeed(500);
		return fieldset;
	}

	/**
	 * 创建PanelGrid
	 *
	 * @param style
	 *            样式
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private PanelGrid createPanelGrid(String style) {
		PanelGrid panelGrid = (PanelGrid) JsfUtil.getApplication().createComponent(PanelGrid.COMPONENT_TYPE);
		if (null != style) {
			panelGrid.setStyle(style);
		}
		return panelGrid;
	}

	/**
	 * 创建Row
	 *
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private Row createRow() {
		Row row = (Row) JsfUtil.getApplication().createComponent(Row.COMPONENT_TYPE);
		return row;
	}

	/**
	 * 创建列
	 *
	 * @param colspan
	 *            合并列数
	 * @param styleClass
	 *            样式类
	 * @param style
	 *            样式
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private Column createColumn(Integer colspan, String styleClass, String style) {
		Column column = (Column) JsfUtil.getApplication().createComponent(Column.COMPONENT_TYPE);
		if (null != colspan) {
			column.setColspan(colspan);
		}
		if (null != styleClass) {
			column.setStyleClass(styleClass);
		}
		if (null != style) {
			column.setStyle(style);
		}
		return column;
	}

	/**
	 * 创建HtmlOutputText
	 *
	 * @param value
	 *            值
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private HtmlOutputText createOutputtext(Object value) {
		HtmlOutputText htmlOutputText = (HtmlOutputText) JsfUtil.getApplication().createComponent(
				HtmlOutputText.COMPONENT_TYPE);
		if (null != value) {
			htmlOutputText.setValue(value);
		}
		return htmlOutputText;
	}

	/**
	 * 创建Fieldset，并加入到archivePanel中 创建步骤： 1、创建一个新的Fieldset，设置样式和内容
	 * 2、遍历数据集合，每找到一个该一级分类下的二级分类，则为该二级分类建一个新的PanelGrid
	 * 3、给新创建的PanelGrid设置表格属性，表头内容，并调用相应方法创建表体 4、将新建的PanelGrid加入到Fieldset中
	 * 5、继续循环，将集合中该一级分类下所有的二级分类中的数据都做成PanelGrid，添加到Fieldset
	 *
	 * @param ts
	 *            一级分类的码表实例
	 * @param dataMap
	 *            数据集合
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private void createPanel(TsSimpleCode ts, Map<TsSimpleCode, List<Object[]>> dataMap) {
		// 第一步，创建Fieldset
		final Fieldset fieldset = createFieldset(ts.getCodeName(), "margin-top: 5px;margin-bottom: 5px;");

		// 第二步，遍历集合，创建Fieldset下的PanelGrid
		Set<TsSimpleCode> tsSimpleCodeSet = dataMap.keySet();
		for (TsSimpleCode t : tsSimpleCodeSet) {
			String[] str = t.getCodeLevelNo().split("\\.");
			// 第三步,判断此二级分类是否在该一级分类下，若是则创建PanelGrid
			if (str.length == 2 && str[0].equals(ts.getCodeLevelNo())) {
				List<Object[]> list = dataMap.get(t);
				final PanelGrid panelGrid = createPanelGrid("width:100%;margin-top:10px;");
				final Row headRow = createRow();
				final Column headColumn = createColumn(8, "ui-widget-header", "height:22px;");
				final HtmlOutputText headText = createOutputtext(t.getCodeName());
				headColumn.getChildren().add(headText);
				headRow.getChildren().add(headColumn);
				panelGrid.getChildren().add(headRow);
				// 调用相应方法创建表体，根据系统参数值，调用对应的方法创建表体
				boolean bool = true;
				bool = this.createPanelGrid3(panelGrid, list);
				/*if (str[0].equals(tsParam)) {
					bool = this.createPanelGrid1(panelGrid, list);
				} else if (str[0].equals(tzParam)) {
					bool = this.createPanelGrid2(panelGrid, list);
				} else if (str[0].equals(hyParam)) {

				} else {
					bool = this.createPanelGrid1(panelGrid, list);
				}*/
				// 第四步，如果表体内有组件，则将该表添加到Fieldse
				if (!bool) {
					fieldset.getChildren().add(panelGrid);
				}
			}
		}
		// 第五步，将Fieldse添加到outpanel中
		if (fieldset.getChildren().size() != 0) {
			archivePanel.getChildren().add(fieldset);
		}
	}

	/**
	 * 该方法用于创建功能性及特殊检查的panelgrid 1、设置表格的第一行内容 2、遍历该分类下的数据集合，将集合数据添加到表格中 3、返回创建结果
	 *
	 * @param panelGrid
	 *            需要创建内容的panelGrid
	 * @param list
	 *            该分类下的数据list集合
	 * @return 表格有内容返回true，否则返回false
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private boolean createPanelGrid1(PanelGrid panelGrid, List<Object[]> list) {
		boolean isBlank = true;
		// 标题行
		final Row titleRow = createRow();
		// 标题列
		final Column titleColumn1 = createColumn(null, "ui-state-default",
				"width:250px;text-align: center;height:20px;");
		final HtmlOutputText titleText1 = createOutputtext("项目");
		titleColumn1.getChildren().add(titleText1);
		final Column titleColumn2 = createColumn(null, "ui-state-default", "text-align: center;");
		final HtmlOutputText titleText2 = createOutputtext("结果");
		titleColumn2.getChildren().add(titleText2);
		// 组件关联
		titleRow.getChildren().add(titleColumn1);
		titleRow.getChildren().add(titleColumn2);
		panelGrid.getChildren().add(titleRow);
		// 遍历数据集合
		for (Object[] obj : list) {
			final Row contentRow = createRow();
			// 某条数据记录的第一列，设置内容和样式，数据为空，显示"—"
			final Column contentColumn1 = createColumn(null, null, "text-align: left;height:20px;");
			final HtmlOutputText contentText1 = createOutputtext(obj[0] == null ? "—" : obj[0]);
			contentColumn1.getChildren().add(contentText1);
			// 某条数据记录的第二列，设置内容和样式，数据为空，显示"—"
			final Column contentColumn2 = createColumn(null, null, "text-align: left;");
			final HtmlOutputText contentText2 = createOutputtext(obj[1] == null ? "—" : obj[1]);
			contentColumn2.getChildren().add(contentText2);
			// 组件关联
			contentRow.getChildren().add(contentColumn1);
			contentRow.getChildren().add(contentColumn2);
			panelGrid.getChildren().add(contentRow);
			isBlank = false;
		}
		return isBlank;
	}

	/**
	 * 该方法用于创建体征panelgrid，体征需要分栏显示，因此，需要在createPanelGrid1基础增加循环
	 * 1、设置表格的第一行内容，内容需要循环一次，做成分栏效果 2、遍历该分类下的数据集合，将集合数据添加到表格中，每行中添加两条记录，循环实现
	 * 3、返回创建结果
	 *
	 * @param panelGrid
	 *            需要创建内容的panelGrid
	 * @param list
	 *            该分类下的数据list数据集合
	 * @return 表格有内容返回true，否则返回false
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private boolean createPanelGrid2(PanelGrid panelGrid, List<Object[]> list) {
		boolean isBlank = true;
		// 1.创建标题行，分栏需要循环创建两次，四列
		final Row titleRow = createRow();
		for (int i = 0; i < 2; i++) {
			final Column titleColumn1 = createColumn(null, "ui-state-default",
					"width:150px;text-align: center;height:20px;");
			final HtmlOutputText titleText1 = createOutputtext("项目");
			titleColumn1.getChildren().add(titleText1);
			final Column titleColumn2 = createColumn(null, "ui-state-default", "width:300px;text-align: center;");
			final HtmlOutputText titleText2 = createOutputtext("结果");
			titleColumn2.getChildren().add(titleText2);
			titleRow.getChildren().add(titleColumn1);
			titleRow.getChildren().add(titleColumn2);
		}
		panelGrid.getChildren().add(titleRow);
		// 2.创建内容行
		int count = 0;
		int length = list.size();
		Row contentRow = null;
		// 3.遍历集合，添加数据，分栏添加
		for (int i = 0; i < length; i++) {
			Object[] obj = list.get(i);
			// 如果该行已有两列数据则创建一个新行
			if (count == 0) {
				contentRow = createRow();
			}
			// 给该行添加数据列
			final Column contentColumn1 = createColumn(null, null, "text-align: left;height:20px;");
			final HtmlOutputText contentText1 = createOutputtext(obj[0] == null ? "—" : obj[0]);
			contentColumn1.getChildren().add(contentText1);
			final Column contentColumn2 = createColumn(null, null, "text-align: left;");
			// 如果是最后一个且该行只有一条记录则合并列3列数据
			if (i == (length - 1) && count == 0) {
				contentColumn2.setColspan(3);
			}
			final HtmlOutputText contentText2 = createOutputtext(obj[1] == null ? "—" : obj[1]);
			// 关联组件
			contentColumn2.getChildren().add(contentText2);
			contentRow.getChildren().add(contentColumn1);
			contentRow.getChildren().add(contentColumn2);
			count++;
			// 如果该行已有两条数据记录，则将该行添加到表格中，并创建一个新行
			if (count == 2) {
				panelGrid.getChildren().add(contentRow);
				isBlank = false;
				count = 0;
			}
		}
		// 添加最后一行的数据记录
		if (count == 1) {
			panelGrid.getChildren().add(contentRow);
			isBlank = false;
		}
		return isBlank;
	}

	/**
	 * 该方法用于创建化验检查检查panelgrid，化验需要添加新的数据列，因此，需要在createPanelGrid1基础增加新列
	 * 1、设置表格的第一行内容 2、遍历该分类下的数据集合，将集合数据添加到表格中 3、返回创建结果
	 *
	 * @param panelGrid
	 *            需要创建内容的panelGrid
	 * @param list
	 *            该分类下的数据list数据集合
	 * @return 表格有内容返回true，否则返回false
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private boolean createPanelGrid3(PanelGrid panelGrid, List<Object[]> list) {
		boolean isBlank = true;
		// 第一步.创建标题行，分栏需要循环创建两次，八列
		final Row titleRow = createRow();
		/*for (int i = 0; i < 2; i++) {*/
		boolean flag = false;

		if(null!=list&&list.size()>0){

			for (Object[] obj :list) {
				if(obj[10]==null){
					continue;
				}
				BigDecimal rowTmp = (BigDecimal) obj[10];
				if(rowTmp!=null&&rowTmp.intValue()==30){
					flag = true;
				}

			}


		}
			// 添加项目列
			final Column titleColumn1 = createColumn(null, "ui-state-default",
					"width:120px;text-align: center;height:20px;");
			final HtmlOutputText titleText1 = createOutputtext("项目");
			titleColumn1.getChildren().add(titleText1);
			// 添加结果列
			final Column titleColumn2 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
			final HtmlOutputText titleText2 = createOutputtext("结果");
			titleColumn2.getChildren().add(titleText2);
			// 添加参考值列
			final Column titleColumn3 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
			final HtmlOutputText titleText3 = createOutputtext("参考值");
			titleColumn3.getChildren().add(titleText3);
			// 添加计量单位列
			final Column titleColumn4 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
			final HtmlOutputText titleText4 = createOutputtext("计量单位");
			titleColumn4.getChildren().add(titleText4);
			// 添加合格
			final Column titleColumn5 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
			final HtmlOutputText titleText5 = createOutputtext("合格");
			titleColumn5.getChildren().add(titleText5);



			// 添加未检
			final Column titleColumn7 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
			final HtmlOutputText titleText7 = createOutputtext("未检");
			titleColumn7.getChildren().add(titleText7);

			// 组件关联
			titleRow.getChildren().add(titleColumn1);
			titleRow.getChildren().add(titleColumn2);
			titleRow.getChildren().add(titleColumn3);
			titleRow.getChildren().add(titleColumn4);
			titleRow.getChildren().add(titleColumn5);
			//添加结果判定
			if(flag){
				final Column titleColumn6 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
				final HtmlOutputText titleText6 = createOutputtext("结果判定");
				titleColumn6.getChildren().add(titleText6);
				titleRow.getChildren().add(titleColumn6);
			}
			titleRow.getChildren().add(titleColumn7);
		//}
		panelGrid.getChildren().add(titleRow);
		// 第二步，创建内容行，遍历集合，添加数据，分栏添加
		int count = 0;
		int length = list.size();
		Row contentRow = null;
		for (int i = 0; i < length; i++) {
			Object[] obj = list.get(i);
			// 1.如果该行已有两条数据则创建一个新行
			if (count == 0) {
				contentRow = createRow();
			}
			// 2.添加项目名称内容
			final Column contentColumn1 = createColumn(null, null, "text-align: left;height:20px;");
			final HtmlOutputText contentText1 = createOutputtext(obj[0]);
			contentColumn1.getChildren().add(contentText1);
			// 3.添加体检结果内容
			final Column contentColumn2 = createColumn(null, null, null);
			final HtmlOutputText contentText2 = createOutputtext(null);
			this.setPanelgridValue(contentColumn2, contentText2, obj);
			contentColumn2.getChildren().add(contentText2);
			// 4.添加参考值列的内容
			final Column contentColumn3 = createColumn(null, null, "text-align: left");
			final HtmlOutputText contentText3 = createOutputtext( obj[3]);
			contentColumn3.getChildren().add(contentText3);
			// 5.添加计量单位的列的内容
			final Column contentColumn4 = createColumn(null, null, "text-align: center;");
			// 如果是最后一个且该行只有一条记录则合并列3列数据
		/*	if (i == (length - 1) && count == 0) {
				contentColumn4.setColspan(5);
			}*/
			final HtmlOutputText contentText4 = createOutputtext(obj[4]);

			Column contentColumn5 = null;
			HtmlOutputText contentText5 = null;
			// 6.添加合格
			BigDecimal tmp = (BigDecimal) obj[7];
			if(tmp.intValue()==1){
				contentColumn5 = createColumn(null, null, "text-align: center");
				contentText5 = createOutputtext( "是");

			}else {
				contentColumn5 = createColumn(null, null, "text-align: center;color:red");
				contentText5 = createOutputtext( "否");
			}
			contentColumn5.getChildren().add(contentText5);



			// 8.添加未检
			Column contentColumn7 = null;
			HtmlOutputText contentText7 = null;

			if(obj[9]!=null){
				BigDecimal tmp2 = (BigDecimal) obj[9];
				if(tmp2.intValue()==1){
					contentColumn7 = createColumn(null, null, "text-align: center;color:red");
					contentText7 = createOutputtext( "是");
				}else {
					contentColumn7 = createColumn(null, null, "text-align: center");
					contentText7 = createOutputtext( "否");
				}
			}else {
				contentColumn7 = createColumn(null, null, "text-align: center;color:red");
				contentText7 = createOutputtext( "");
			}
			contentColumn7.getChildren().add(contentText7);




			// 6.组件关联
			contentColumn4.getChildren().add(contentText4);
			contentRow.getChildren().add(contentColumn1);
			contentRow.getChildren().add(contentColumn2);
			contentRow.getChildren().add(contentColumn3);
			contentRow.getChildren().add(contentColumn4);
			contentRow.getChildren().add(contentColumn5);
			// 7.添加结果判定
			  /*0：未见异常
				1：尘肺样改变
				2：其他异常
				3：未检查*/
			if(flag){
				BigDecimal tmp1 = (BigDecimal) obj[8];
				Column contentColumn6 = null;
				HtmlOutputText contentText6 = null;
				//（业务分类下存在胸片项目才要显示，项目标记为30的代表胸片），非正常标红
				if(tmp1!=null){
					if(tmp1.intValue()==0){
						contentColumn6 = createColumn(null, null, "text-align: left");
						contentText6 = createOutputtext( "未见异常");
					}else{
						contentColumn6 = createColumn(null, null, "text-align: left;color:red");
						if(tmp1.intValue()==1){
							contentText6 = createOutputtext( "尘肺样改变");
						}else if(tmp1.intValue()==2){
							contentText6 = createOutputtext( "其他异常");
						}else if(tmp1.intValue()==3){
							contentText6 = createOutputtext( "未检查");
						}
					}
				}else {
					contentColumn6 = createColumn(null, null, "text-align: left");
					contentText6 = createOutputtext( "");
				}

				contentColumn6.getChildren().add(contentText6);
				contentRow.getChildren().add(contentColumn6);
			}

			contentRow.getChildren().add(contentColumn7);
			count++;
			// 如果该行已有两条数据记录，则将该行添加到表格中，并创建一个新行
			if (count == 1) {
				panelGrid.getChildren().add(contentRow);
				isBlank = false;
				count = 0;
			}
		}
		// 添加最后一行的数据记录
		if (count == 1) {
			panelGrid.getChildren().add(contentRow);
			isBlank = false;
		}
		return isBlank;
	}

	public String getLinkedPhone2EnCode(String linkphone2){
		if(ifManagedOrg){
			return StringUtils.encryptPhone(linkphone2);
		}
		return linkphone2;
	}



	/**
	 * 设置化验结果列的样式和值
	 *
	 * @param contentColumn
	 * @param contentText
	 * @param obj
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private void setPanelgridValue(Column contentColumn, HtmlOutputText contentText, Object[] obj) {
		// 定量且结果偏低或偏高则结果加标志，并设置颜色
		Integer result = null;
		if (null != obj[2]) {
			result = Integer.valueOf(obj[2].toString());
		}
		if (null != obj[1]) {
			if (null != result) {
				if (result == 2) {
					// 如果偏高，则添加向上箭头，并设置颜色为红色
					contentColumn.setStyle("text-align: left;color: red");
					String string = obj[1].toString() + "↑";
					contentText.setValue(string);
				} else if (result == 1) {
					// 如果偏低，则添加向下箭头，并设置颜色为蓝色
					contentColumn.setStyle("text-align: left;color: blue");
					String string = obj[1].toString() + "↓";
					contentText.setValue(string);
				} else {
					// 否则，原样显示
					contentColumn.setStyle("text-align: left;");
					contentText.setValue(obj[1]);
				}
			} else {
				contentColumn.setStyle("text-align: left;");
				contentText.setValue(obj[1]);
			}
		} else {
			// 内容为空，则显示为"—"
			contentColumn.setStyle("text-align: left;");
			contentText.setValue("—");
		}
	}



	public TdTjBhk getTdTjBhkShow() {
		return tdTjBhkShow;
	}

	public TdTjExmsdata getTdTjExmsdata() {
		return tdTjExmsdata;
	}

	public void setTdTjBhkShow(TdTjBhk tdTjBhkShow) {
		this.tdTjBhkShow = tdTjBhkShow;
	}

	public void setTdTjExmsdata(TdTjExmsdata tdTjExmsdata) {
		this.tdTjExmsdata = tdTjExmsdata;
	}

	public List<TdTjEmhistory> getTdTjEmhistoryList() {
		return tdTjEmhistoryList;
	}

	public List<TdTjEmhistory> getTdTjEmhistoryList2() {
		return tdTjEmhistoryList2;
	}

	public void setTdTjEmhistoryList(List<TdTjEmhistory> tdTjEmhistoryList) {
		this.tdTjEmhistoryList = tdTjEmhistoryList;
	}

	public void setTdTjEmhistoryList2(List<TdTjEmhistory> tdTjEmhistoryList2) {
		this.tdTjEmhistoryList2 = tdTjEmhistoryList2;
	}

	public Integer getRid() {
		return rid;
	}
	public void setRid(Integer rid) {
		this.rid = rid;
	}



	public OutputPanel getArchivePanel() {
		return archivePanel;
	}
	public void setArchivePanel(OutputPanel archivePanel) {
		this.archivePanel = archivePanel;
	}

	public boolean isIfManagedOrg() {
		return ifManagedOrg;
	}
	public void setIfManagedOrg(boolean ifManagedOrg) {
		this.ifManagedOrg = ifManagedOrg;
	}

	public boolean isIfRmkSubCover() {
		return ifRmkSubCover;
	}

	public void setIfRmkSubCover(boolean ifRmkSubCover) {
		this.ifRmkSubCover = ifRmkSubCover;
	}

	public boolean isIfShowBaseOnly() {
		return ifShowBaseOnly;
	}

	public void setIfShowBaseOnly(boolean ifShowBaseOnly) {
		this.ifShowBaseOnly = ifShowBaseOnly;
	}

	public TbTjCrptIndepend getTbTjCrptIndepend() {
		return tbTjCrptIndepend;
	}

	public void setTbTjCrptIndepend(TbTjCrptIndepend tbTjCrptIndepend) {
		this.tbTjCrptIndepend = tbTjCrptIndepend;
	}
}
