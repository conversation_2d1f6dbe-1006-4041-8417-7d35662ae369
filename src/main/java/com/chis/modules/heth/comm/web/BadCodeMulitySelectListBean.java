package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.service.TdTjBhkCltCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>类描述：码表弹出框</p>
 * @ClassAuthor qrr,2018年4月9日,CodeRadioSelectBean
 */
@ManagedBean(name = "badCodeMulitySelectListBean")
@ViewScoped
public class BadCodeMulitySelectListBean extends FacesBean {

    private static final long serialVersionUID = -738445454536625063L;

    /** 名称 或 拼音码*/
    private String searchNamOrPy;
    /** 标题*/
    private String titleName;
    /** 码表类型*/
    private String typeNo;
    /**码表大类*/
    private String firstCodeNo;
    private List<TsSimpleCode> firstList;
    /**查询条件大类是否显示*/
    private Boolean ifShowFirstCode = false;
    /** 查询列集合*/
    private List<TsSimpleCode> displayList;
    /** 所有集合*/
    private List<TsSimpleCode> allList;

    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	private TdTjBhkCltCommServiceImpl service = (TdTjBhkCltCommServiceImpl) SpringContextHolder.getBean(TdTjBhkCltCommServiceImpl.class);
    /**为1时只能选择最末级*/
    private String selectLast;
    private Map<String, List<Object[]>> levelMap;
    /**只可选择同一层级的选项，选择一个级别后，非此级别的灰掉不可选择*/
    private boolean selectSameLevel = false;
    /**可选择个数*/
    private Integer selectNum;
    /**
 	 * <p>方法描述：初始化数据</p>
 	 * @MethodAuthor qrr,2018年4月9日,CodeRadioSelectBean
     * */
    public BadCodeMulitySelectListBean() {
        this.titleName = JsfUtil.getRequest().getParameter("titleName");
        String typeNo = JsfUtil.getRequest().getParameter("typeNo");
        String rids = JsfUtil.getRequest().getParameter("allBadIds");
        String selectIds = JsfUtil.getRequest().getParameter("selectIds");//已选择的码表rid，以逗号隔开
        String ifShowFirstCode = JsfUtil.getRequest().getParameter("ifShowFirstCode");
        this.selectLast = JsfUtil.getRequest().getParameter("selectLast");
        String noContainsSelected = JsfUtil.getRequest().getParameter("noContainsSelected");
        this.typeNo = typeNo;
        if (StringUtils.isNotBlank(ifShowFirstCode)) {
            if ("true".equals(ifShowFirstCode)) {
                this.ifShowFirstCode = true;
            }else if ("false".equals(ifShowFirstCode)) {
                this.ifShowFirstCode = false;
            }
        }
        String selectSameLevel = JsfUtil.getRequest().getParameter("selectSameLevel");
        if ("1".equals(selectSameLevel)) {
        	this.selectSameLevel = true;
		}else {
			this.selectSameLevel = false;
		}
        String selectNum = JsfUtil.getRequest().getParameter("selectNum");
        if (StringUtils.isNotBlank(selectNum)) {
			this.selectNum = Integer.valueOf(selectNum);
		}
        this.init(rids,selectIds,noContainsSelected);
    }
    /**
 	 * <p>方法描述：查询码表数据</p>
 	 * @MethodAuthor qrr,2018年4月9日,CodeRadioSelectBean
     * */
    private void init(String rids,String selectIds,String noContainsSelected)   {
        this.displayList = new ArrayList<TsSimpleCode>();
        this.allList = new ArrayList<TsSimpleCode>();
        this.firstList = new ArrayList<TsSimpleCode>();
        List<Object[]> list = service.findSimpleByParam(typeNo,rids);
        dealLevelMap(list);
        int row = -1;// 第一次出现勾选的当前页的第一行数
        TsSimpleCode selectLevelCode = null;
        if (null!=list && list.size()>0) {
        	int  i = 0;
			List<String> resList = new ArrayList();
			if(StringUtils.isNotBlank(selectIds)){
				if(selectIds.contains(",")){
					String[] split = selectIds.split(",");
					for(String str:split){
						resList.add(str);
					}
				}else {
					resList.add(selectIds);
				}
			}
        	for (Object[] obj : list) {
        		if (null==obj[0]) {
					continue;
				}
        		if ("1".equals(noContainsSelected)) {//已选择的不显示
        			if (dealNoContainsSelected(resList,obj)) {
						continue;
					}
				}
				TsSimpleCode code = new TsSimpleCode();
				code.setRid(null!=obj[0]?Integer.valueOf(obj[0].toString()):null);
				code.setCodeName(null!=obj[1]?obj[1].toString():null);
				code.setCodeLevelNo(null!=obj[3]?obj[3].toString():null);
				code.setCodeNo(null!=obj[2]?obj[2].toString():null);
				code.setSplsht(null!=obj[4]?obj[4].toString():null);
				code.setExtendS1(null!=obj[5]?obj[5].toString():null);
				code.setExtendS2(null!=obj[6]?Integer.valueOf(obj[6].toString()):null);
				code.setLevelIndex(StringUtils.countMatches(code.getCodeLevelNo(), ".")+ "");
				
				if (StringUtils.isNotBlank(selectIds)
						&& resList.contains(code.getRid().toString())) {
					code.setIfSelected(true);
					if (row == -1) {
						row = i - i % 10;
					}
					//已选择的级别
					if (null==selectLevelCode) {
						selectLevelCode = code;
					}
				}
				if (StringUtils.containsNone(code.getCodeLevelNo(), ".")) {
                    firstList.add(code);
                }
				allList.add(code);
        		i++;
			}
        	
		}
        if( null != allList && allList.size() > 0)    {
            this.displayList.addAll(allList);
            //初始化选择当前页的第一行数
            if (row>-1) {
            	DataTable dataTable = (DataTable) FacesContext
            			.getCurrentInstance().getViewRoot()
            			.findComponent("codeForm:selectedIndusTable");
            	dataTable.setFirst(row);
            }
            if (selectSameLevel && null!=selectLevelCode) {
            	dealIfDisabled(selectLevelCode);
			}
        }
    }
    /**
 	 * <p>方法描述：处理码表层级关系</p>
 	 * @MethodAuthor qrr,2018年4月8日,dealCodelevel
	 * */
	private void dealLevelMap(List<Object[]> list){
		this.levelMap = new HashMap<>();
		if (list != null && list.size() > 0) {
			for (Object[] obj : list) {
				if (null==obj[3]) {
					continue;
				}
				this.levelMap.put(obj[3].toString(), new ArrayList<Object[]>());
				if (StringUtils.contains(obj[3].toString(), ".")) {
					String[] split = obj[3].toString().split("\\.");
					StringBuffer parentCodeSb = new StringBuffer();
					for (int i = 0; i < split.length-1; i++) {//仅找出父级
						parentCodeSb.append(".").append(split[i]);
						String parentCode = parentCodeSb.substring(1);
						List<Object[]> childs = this.levelMap.get(parentCode);
						if (null==childs) {
							childs = new ArrayList<Object[]>();
							this.levelMap.put(parentCode, childs);
						}
						childs.add(obj);
					}
				}
			}
		}
	}
	/**
 	 * <p>方法描述：处理已选择的数据，界面不显示</p>
 	 * @MethodAuthor qrr,2020年2月15日,dealNoContainsSelected
	 * */
	private boolean dealNoContainsSelected(List resList,Object[] obj) {
		if (!CollectionUtils.isEmpty(resList)) {
			if (resList.contains(obj[0].toString())) {
				return true;
			}
			//最末级全部已选择，上级全部不显示
			List<Object[]> childs = this.levelMap.get(obj[3].toString());
			if (null!=childs && childs.size()>0) {
				boolean flag = false;
				for (Object[] child : childs) {
					List<Object[]> child2s = this.levelMap.get(child[3].toString());
					if (null!=child2s && child2s.size()>0) {//不是最末级，继续
						continue;
					}
					if (!resList.contains(child[0].toString())) {//最末子级存在未选择的，则退出循环
						flag = true;
        				break;
        			}
				}
				if (!flag) {//最末级全部已选择，父级不显示
					return true;
				}
			}
		}
		return false;
	}
	/**
 	 * <p>方法描述：勾选大类，则小类默认全部选择</p>
 	 * @MethodAuthor qrr,2019年12月2日,selectAction
	 * */
	public void selectAction(TsSimpleCode code){
		if (!selectSameLevel) {
			dealIfSelected(code);
		}else {
			dealIfDisabled(code);
		}
		
	}
	/**
 	 * <p>方法描述：处理是否选中</p>
 	 * @MethodAuthor qrr,2020年6月5日,dealIfSelected
	 * */
	private void dealIfSelected(TsSimpleCode code) {
		String codeLevelNo = code.getCodeLevelNo();
		if (StringUtils.isBlank(codeLevelNo)) {
			return;
		}
		if (code.isIfSelected()){//大类勾选，小类全部选择
			for (TsSimpleCode t : allList) {
				String levelNo = t.getCodeLevelNo();
				if (StringUtils.isBlank(levelNo)) {
					continue;
				}
				if (levelNo.startsWith(codeLevelNo + ".")) {//下级全部勾选
					t.setIfSelected(true);
				}
			}
		}else {//大类不勾选，小类不选择
			for (TsSimpleCode t : allList) {
				String levelNo = t.getCodeLevelNo();
				if (StringUtils.isBlank(levelNo)) {
					continue;
				}
				if (codeLevelNo.startsWith(levelNo + ".")) {//上级不勾选
					t.setIfSelected(false);
				}
				if (levelNo.startsWith(codeLevelNo + ".")) {//下级不勾选
					t.setIfSelected(false);
				}
			}
		}
	}
	/**
 	 * <p>方法描述：处理是否允许选择</p>
 	 * @MethodAuthor qrr,2020年6月5日,dealIfSelected
	 * */
	private void dealIfDisabled(TsSimpleCode code) {
		if (code.isIfSelected()){
			if (StringUtils.isBlank(code.getCodeLevelNo())) {
				return;
			}
			String[] selectLevel = code.getCodeLevelNo().split("\\.");
			//非同级disabled
			for (TsSimpleCode t : allList) {
				if (StringUtils.isBlank(t.getCodeLevelNo())) {
					continue;
				}
				String[] level = t.getCodeLevelNo().split("\\.");
				if (selectLevel.length!=level.length) {
					t.setIfDisabled(true);
				}else {
					t.setIfDisabled(false);
				}
			}
		}else {
			//所有码表都未选中，所有码表允许选择
			for (TsSimpleCode t : allList) {
				if (t.isIfSelected()) {
					return;
				}
			}
			for (TsSimpleCode t : allList) {
				t.setIfDisabled(false);
			}
		}
	}
    /**
 	 * <p>方法描述：根据名称、拼音码过滤数据</p>
 	 * @MethodAuthor qrr,2018年4月9日,searchAction
     */
    public void searchAction() {
		 //初始化展示页面的疫苗数据集
        this.displayList = new ArrayList<TsSimpleCode>();
        List<TsSimpleCode> list = new ArrayList<TsSimpleCode>();
        if(null != allList && allList.size() > 0 ){
        	if(StringUtils.isNotBlank(firstCodeNo)) {
        		for(TsSimpleCode t :allList)   {
        			if (StringUtils.isBlank(t.getCodeLevelNo())) {
        				continue;
					}
        			if (t.getCodeLevelNo().startsWith(firstCodeNo)) {
                        list.add(t);
                    }
        		}
        		if(StringUtils.isNotBlank(searchNamOrPy)){
        			for(TsSimpleCode t :list)   {
        				//疫苗名称
        				String codeName = t.getCodeName()==null?"":t.getCodeName().toString();
        				//疫苗拼音码
        				String codePym = t.getSplsht()==null?"":t.getSplsht().toString();
        				//如果模糊匹配上，则增加
        				if (codeName.indexOf(searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(searchNamOrPy.toUpperCase()) != -1) {
        					this.displayList.add(t);
        				}
        			}
        		}else{
        			this.displayList.addAll(list);
        		}
        	}else if(StringUtils.isNotBlank(searchNamOrPy)){
        		for(TsSimpleCode t :allList)   {
    				//疫苗名称
    				String codeName = t.getCodeName()==null?"":t.getCodeName().toString();
    				//疫苗拼音码
    				String codePym = t.getSplsht()==null?"":t.getSplsht().toString();
    				//如果模糊匹配上，则增加
    				if (codeName.indexOf(searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(searchNamOrPy.toUpperCase()) != -1) {
    					this.displayList.add(t);
    				}
    			}
        	}else{
        		this.displayList.addAll(allList);
        	}
        	
        }
        DataTable dataTable = (DataTable) FacesContext
    			.getCurrentInstance().getViewRoot()
    			.findComponent("codeForm:selectedIndusTable");
    	dataTable.setFirst(0);
	}
    /**
 	 * <p>方法描述：提交</p>
 	 * @MethodAuthor qrr,2019年12月2日,submitAction
     * */
    public void submitAction() {
    	if (null!=allList && allList.size()>0) {
			List<TsSimpleCode> results = new ArrayList<>();
    		for (TsSimpleCode t : allList) {
				if (t.isIfSelected()) {
					if ("1".equals(selectLast)) {
						List<Object[]> childs = this.levelMap.get(t.getCodeLevelNo());
						if (null==childs||childs.size()==0) {//最末级，无子级
							results.add(t);
						}
					}else {
						results.add(t);
					}
				}
			}
    		if (null==results ||results.size()==0) {
    			JsfUtil.addErrorMessage("请选择数据！");
    			return;
			}else {
				if (null != selectNum && results.size() > selectNum.intValue()) {
					JsfUtil.addErrorMessage("最多只能选择"+selectNum+"条数据！");
					return;
				}
				if (results.size()>1000) {
					JsfUtil.addErrorMessage("最多只能选择1000条数据！");
	    			return;
				}
			}
    		Map<String, List<TsSimpleCode>> map = new HashMap<String, List<TsSimpleCode>>();
    		map.put("selectPros", results);
    		RequestContext.getCurrentInstance().closeDialog(map);
		}
	}
    /**
 	 * <p>方法描述：关闭</p>
 	 * @MethodAuthor qrr,2018年4月9日,dialogClose
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }


    public String getSearchNamOrPy() {
        return searchNamOrPy;
    }

    public void setSearchNamOrPy(String searchNamOrPy) {
        this.searchNamOrPy = searchNamOrPy;
    }

	public String getTitleName() {
		return titleName;
	}

	public void setTitleName(String titleName) {
		this.titleName = titleName;
	}

	public List<TsSimpleCode> getDisplayList() {
		return displayList;
	}

	public void setDisplayList(List<TsSimpleCode> displayList) {
		this.displayList = displayList;
	}

    public String getFirstCodeNo() {
        return firstCodeNo;
    }

    public void setFirstCodeNo(String firstCodeNo) {
        this.firstCodeNo = firstCodeNo;
    }

    public List<TsSimpleCode> getFirstList() {
        return firstList;
    }

    public void setFirstList(List<TsSimpleCode> firstList) {
        this.firstList = firstList;
    }

    public Boolean getIfShowFirstCode() {
        return ifShowFirstCode;
    }

    public void setIfShowFirstCode(Boolean ifShowFirstCode) {
        this.ifShowFirstCode = ifShowFirstCode;
    }
	public String getSelectLast() {
		return selectLast;
	}
	public void setSelectLast(String selectLast) {
		this.selectLast = selectLast;
	}
	public Integer getSelectNum() {
		return selectNum;
	}
	public void setSelectNum(Integer selectNum) {
		this.selectNum = selectNum;
	}
	public boolean isSelectSameLevel() {
		return selectSameLevel;
	}
	public void setSelectSameLevel(boolean selectSameLevel) {
		this.selectSameLevel = selectSameLevel;
	}
}
