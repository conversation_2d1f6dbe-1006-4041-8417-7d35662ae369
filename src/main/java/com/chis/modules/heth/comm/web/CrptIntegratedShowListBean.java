package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSON;
import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.rptvo.PlaceInfo;
import com.chis.modules.heth.comm.rptvo.UnitbasicInfo;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import com.github.abel533.echarts.DataZoom;
import com.github.abel533.echarts.Grid;
import com.github.abel533.echarts.Legend;
import com.github.abel533.echarts.axis.CategoryAxis;
import com.github.abel533.echarts.axis.SplitArea;
import com.github.abel533.echarts.axis.SplitLine;
import com.github.abel533.echarts.axis.ValueAxis;
import com.github.abel533.echarts.code.*;
import com.github.abel533.echarts.data.Data;
import com.github.abel533.echarts.json.GsonOption;
import com.github.abel533.echarts.series.Bar;
import com.github.abel533.echarts.series.Pie;
import com.github.abel533.echarts.series.Series;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;

/**
 * 用人单位综合展示
 * 当前托管Bean已经弃用，用人单位综合展示使用/webapp/heth/comm/gscrptshow/gsCrptIntegratedShowList.faces 菜单
 * <AUTHOR>
 * @version 1.0
 */
@Deprecated
@ManagedBean(name = "crptIntegratedShowListBean")
@ViewScoped
public class CrptIntegratedShowListBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
     * 查询条件：地区
     */
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
    /**
     * 查询条件：单位名称
     */
    private String searchCrptName;
    /**
     * 查询条件：社会信用代码
     */
    private String searchInstitutionCode;
    /**
     * 查询条件：经济类型
     */
    private String selectEconomyIds;
    private String selectEconomyNames;
    private List<TsSimpleCode> economyList;
    /**
     * 查询条件：企业规模
     */
    private String selectCrptSizeIds;
    private List<TsSimpleCode> crptSizeList;
    /**
     * 当前操作码表类型
     */
    private String simpleCodeOpType;
    /**
     * 查询条件：行业类别
     */
    private String selectIndusTypeIds;
    private String selectIndusTypeNames;
    private List<TsSimpleCode> indusList;
    /**
     * 查询条件：职业病危害因素
     */
    private String selectBadRsnIds;
    private String selectBadRsnNames;
    private List<TsSimpleCode> badRsnList;
    /**
     * 用人单位RID
     */
    private Integer rid;
    /**
     * 详情页面：基本信息
     */
    private Object[] crptBaseInfo;
    /**
     * 职业风险分类码表拓展字段1对应码表名称
     */
    private Map<String, String> occRiskClassMap;
    /**
     * 职业病危害因素弹出框数据
     */
    private List<Object[]> occRiskClassList;
    /**
     * 展示职业健康管理档案详情<pre>1:在线申报档案</pre><pre>2:场所监测档案</pre><pre>其他:不展示</pre>
     */
    private Integer showArchives;
    /**
     * 是否当年无申报数据
     */
    private Boolean hasDeclareDataForTheYear;
    /**
     * 最新申报日期
     */
    private Date declareData;
    /**
     * 历次在线申报档案
     */
    private List<TdZyUnitbasicinfo> unitbasicinfoSelList;
    /**
     * 在线申报档案rid
     */
    private Integer unitbasicinfoRid;
    /**
     * 在线申报档案
     */
    private TdZyUnitbasicinfo unitbasicinfo;
    /*****************************场所监测**************************************/
    /**场所监测年份*/
    private Integer placeDate;
    private List<Integer> placeDateList;
    /**
     * 是否当年无场所监测
     */
    private Boolean placeThatYear;
    /**基本情况概况*/
    private UnitbasicInfo unitbasic;
    /***/
    private List<PlaceInfo> unitharmChkInfos;
    private List<PlaceInfo> unitHethCusInfos;
    private List<PlaceInfo> resultProInfos;
    /*****************************场所监测**************************************/
    /******************************** 统计图 *********************************/
    /** 统计图表类型 1 地区分布 2 行业类别 3 经济类型 4 企业规模  5 高毒 6 致癌 */
    private Integer chartType;
    /** 统计图json */
    private String chartJson;
    /** 缓存对应类型的统计图json */
    private Map<Integer, String> chartJsonMap;
    /** 数据条数 用于判断柱状图是否自动滚动 */
    private Integer chartDataSize;
    /** 缓存数据条数 柱状图自动滚动用 */
    private Map<Integer, Integer> chartDataSizeMap;
    /** 地区缓存 key zoneGb  */
    private Map<String, TsZone> chartZoneMap;
    /** 码表缓存 key codeNameDesc subKey codeNo */
    private Map<String,Map<String,TsSimpleCode>> chartSimpleCodeMap;
    /******************************** 统计图 *********************************/

    public CrptIntegratedShowListBean() {
        initSimpleCode();
        otherInit();
        searchAction();
    }

    private void initSimpleCode() {
        this.chartSimpleCodeMap = new HashMap<>();
        //经济类型
        if (CollectionUtils.isEmpty(this.economyList)) {
            this.economyList = this.commService.findNumSimpleCodesByTypeId("5003");
        }
        if(!CollectionUtils.isEmpty(this.economyList)){
            Map<String,TsSimpleCode> fillMap = new HashMap<>();
            for(TsSimpleCode simpleCode : this.economyList){
                fillMap.put(simpleCode.getCodeNo(), simpleCode);
            }
            this.chartSimpleCodeMap.put("5003", fillMap);
        }
        //企业规模
        if (CollectionUtils.isEmpty(this.crptSizeList)) {
            this.crptSizeList = this.commService.findLevelSimpleCodesByTypeId("5004");
        }
        if(!CollectionUtils.isEmpty(this.crptSizeList)){
            Map<String,TsSimpleCode> fillMap = new HashMap<>();
            for(TsSimpleCode simpleCode : this.crptSizeList){
                fillMap.put(simpleCode.getCodeNo(), simpleCode);
            }
            this.chartSimpleCodeMap.put("5004", fillMap);
        }
        //行业类别
        if (CollectionUtils.isEmpty(this.indusList)) {
            this.indusList = this.commService.findNumSimpleCodesByTypeId("5002");
        }
        if(!CollectionUtils.isEmpty(this.indusList)){
            Map<String,TsSimpleCode> fillMap = new HashMap<>();
            for(TsSimpleCode simpleCode : this.indusList){
                fillMap.put(simpleCode.getCodeNo(), simpleCode);
            }
            this.chartSimpleCodeMap.put("5002", fillMap);
        }
        //职业病危害因素
        if (CollectionUtils.isEmpty(this.badRsnList)) {
            this.badRsnList = this.commService.findSimpleCodesByTypeId("5007");
        }
        if(!CollectionUtils.isEmpty(this.badRsnList)){
            Map<String,TsSimpleCode> fillMap = new HashMap<>();
            for(TsSimpleCode simpleCode : this.badRsnList){
                fillMap.put(simpleCode.getCodeNo(), simpleCode);
            }
            this.chartSimpleCodeMap.put("5007", fillMap);
        }
    }

    private void otherInit() {
        this.ifSQL = true;
        //查询条件：地区
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        this.chartZoneMap = new HashMap<>();
        if (CollectionUtils.isEmpty(this.zoneList)) {
            this.zoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "");
            this.searchZoneCode = this.zoneList.get(0).getZoneCode();
            this.searchZoneName = this.zoneList.get(0).getZoneName();
        }
        if(!CollectionUtils.isEmpty(this.zoneList)){
            for(TsZone zone : this.zoneList){
                this.chartZoneMap.put(zone.getZoneGb(), zone);
            }
        }

        this.initChart();
    }

    @Override
    public void searchAction() {
        //过滤职业病危害因素RID，防止SQL注入
        if (StringUtils.isNotBlank(this.selectBadRsnIds)) {
            List<String> selectBadRsnIdList = StringUtils.string2list(this.selectBadRsnIds, ",");
            for (int i = selectBadRsnIdList.size() - 1; i >= 0; i--) {
                if (ObjectUtil.convert(Integer.class, selectBadRsnIdList.get(i)) == null) {
                    selectBadRsnIdList.remove(i);
                }
            }
            this.selectBadRsnIds = StringUtils.list2string(selectBadRsnIdList, ",");
        }
        super.searchAction();
    }

    /**
     * 生成SQL
     *
     * @param type <pre>1：查询</pre><pre>2：查询count</pre><pre>3：导出</pre>
     * @return SQL
     */
    public String buildSql(int type) {
        this.paramMap.clear();
        StringBuilder sql = new StringBuilder();
        if (StringUtils.isNotBlank(this.selectBadRsnIds)) {
            sql.append(" WITH TEMP_TABLE AS ( ");
            sql.append(" SELECT REGEXP_SUBSTR(IDS, '[^,]+', 1, LEVEL, 'i') AS ID ");
            sql.append(" FROM (SELECT '").append(this.selectBadRsnIds).append("' IDS FROM DUAL) ");
            sql.append(" CONNECT BY LEVEL <= LENGTH(IDS) - LENGTH(REGEXP_REPLACE(IDS, ',', '')) + 1 ) ");
        }
        sql.append("SELECT ");
        if (type == 1) {
            sql.append(" C.RID, ");
            sql.append(" Z.FULL_NAME, ");
            sql.append(" C.CRPT_NAME, ");
            sql.append(" C.INSTITUTION_CODE, ");
            sql.append(" SC1.CODE_NAME ECONOMY, ");
            sql.append(" SC2.CODE_NAME CRPT_SIZE, ");
            sql.append(" SC3.CODE_NAME INDUS_TYPE, ");
            sql.append(" C.WORK_FORCE, ");
            sql.append(" C.HOLD_CARD_MAN, ");
            sql.append(" C.IF_SUB_ORG, ");
            sql.append(" Z.ZONE_GB ");
        }
        if (type == 2) {
            sql.append(" COUNT(*) ");
        }
        if (type == 3) {
            sql.append("        T.RID, ");
            sql.append("        T.FULL_NAME, ");
            sql.append("        T.CRPT_NAME, ");
            sql.append("        T.INSTITUTION_CODE, ");
            sql.append("        T.INDUS_TYPE, ");
            sql.append("        T.ECONOMY, ");
            sql.append("        T.CRPT_SIZE, ");
            sql.append("        T.LINKMAN2, ");
            sql.append("        T.LINKPHONE2, ");
            sql.append("        T.WORK_FORCE, ");
            sql.append("        T.HOLD_CARD_MAN, ");
            sql.append("        T.ZONE_GB, ");
            sql.append("        LISTAGG(BADRSN_NAME, '、') WITHIN GROUP ( ORDER BY BADRSN_NO, BADRSN_NUM ) ");
            sql.append(" FROM (SELECT C.RID, ");
            sql.append("              Z.FULL_NAME, ");
            sql.append("              C.CRPT_NAME, ");
            sql.append("              C.INSTITUTION_CODE, ");
            sql.append("              SC3.CODE_PATH INDUS_TYPE, ");
            sql.append("              SC1.CODE_PATH ECONOMY, ");
            sql.append("              SC2.CODE_NAME CRPT_SIZE, ");
            sql.append("              C.LINKMAN2, ");
            sql.append("              C.LINKPHONE2, ");
            sql.append("              C.WORK_FORCE, ");
            sql.append("              C.HOLD_CARD_MAN, ");
            sql.append("              SC5.CODE_NAME BADRSN_NAME, ");
            sql.append("              SC5.CODE_NO   BADRSN_NO, ");
            sql.append("              SC5.NUM       BADRSN_NUM, ");
            sql.append("              Z.ZONE_GB ");
        }
        sql.append(" FROM TB_TJ_CRPT C ");
        sql.append("         LEFT JOIN TS_ZONE Z ON C.ZONE_ID = Z.RID ");
        sql.append("         LEFT JOIN TS_SIMPLE_CODE SC1 ON C.ECONOMY_ID = SC1.RID ");
        sql.append("         LEFT JOIN TS_SIMPLE_CODE SC2 ON C.CRPT_SIZE_ID = SC2.RID ");
        sql.append("         LEFT JOIN TS_SIMPLE_CODE SC3 ON C.INDUS_TYPE_ID = SC3.RID ");
        if (type == 3) {
            sql.append("         LEFT JOIN TB_TJ_CRPT_BADRSN BR ON C.RID = BR.CRPT_ID ");
            sql.append("         LEFT JOIN TS_SIMPLE_CODE SC4 ON BR.BADRSN_ID = SC4.RID ");
            sql.append("         LEFT JOIN TS_SIMPLE_CODE SC5 ON SC4.CODE_LEVEL_NO LIKE SC5.CODE_NO || '.%' AND SC5.CODE_NO = SC5.CODE_LEVEL_NO AND  SC5.CODE_TYPE_ID = SC4.CODE_TYPE_ID ");
        }
        sql.append("WHERE NVL(C.DEL_MARK, 0) = 0 ");
        sql.append("  AND NVL(C.OPERATION_STATUS, 1) = 1 ");
        //地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sql.append(" AND Z.ZONE_GB LIKE :zoneCode ");
            this.paramMap.put("zoneCode", ZoneUtil.zoneSelect(this.searchZoneCode) + "%");
        }
        //用人单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sql.append(" AND C.CRPT_NAME LIKE :searchCrptName escape '\\' ");
            this.paramMap.put("searchCrptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        //社会信用代码
        if (StringUtils.isNotBlank(this.searchInstitutionCode)) {
            sql.append(" AND C.INSTITUTION_CODE LIKE :searchInstitutionCode escape '\\' ");
            this.paramMap.put("searchInstitutionCode", "%" + StringUtils.convertBFH(this.searchInstitutionCode.trim()) + "%");
        }
        // 经济类型
        if (StringUtils.isNotBlank(this.selectEconomyIds)) {
            sql.append(" AND C.ECONOMY_ID IN (:economyIdList) ");
            List<String> economyIdList = StringUtils.string2list(this.selectEconomyIds, ",");
            this.paramMap.put("economyIdList", economyIdList);
        }
        // 企业规模
        if (StringUtils.isNotBlank(this.selectCrptSizeIds)) {
            sql.append(" AND C.CRPT_SIZE_ID IN (:crptSizeIdList) ");
            List<String> crptSizeIdList = StringUtils.string2list(this.selectCrptSizeIds, ",");
            this.paramMap.put("crptSizeIdList", crptSizeIdList);
        }
        // 行业类别
        if (StringUtils.isNotBlank(this.selectIndusTypeIds)) {
            sql.append(" AND C.INDUS_TYPE_ID IN (:indusTypeIdList) ");
            List<String> indusTypeIdList = StringUtils.string2list(this.selectIndusTypeIds, ",");
            this.paramMap.put("indusTypeIdList", indusTypeIdList);
        }
        //职业病危害因素
        if (StringUtils.isNotBlank(this.selectBadRsnIds)) {
            sql.append(" AND EXISTS ( SELECT 1 FROM TB_TJ_CRPT_BADRSN BR WHERE BR.CRPT_ID = C.RID ");
            sql.append(" AND EXISTS(SELECT 1 FROM TEMP_TABLE TEMP WHERE BR.BADRSN_ID = TEMP.ID) ) ");
        }
        if (type == 1) {
            sql.append(" ORDER BY Z.ZONE_GB, C.CRPT_NAME ");
        }
        if (type == 3) {
            sql.append(" GROUP BY C.RID, Z.FULL_NAME, C.CRPT_NAME, C.INSTITUTION_CODE, SC3.CODE_PATH, SC1.CODE_PATH, SC2.CODE_NAME, C.LINKMAN2, C.LINKPHONE2, C.WORK_FORCE, C.HOLD_CARD_MAN, SC5.CODE_NAME, SC5.CODE_NO, SC5.NUM, Z.ZONE_GB ");
            sql.append(") T ");
            sql.append("GROUP BY RID, FULL_NAME, CRPT_NAME, INSTITUTION_CODE, INDUS_TYPE, ECONOMY, CRPT_SIZE, LINKMAN2, LINKPHONE2, WORK_FORCE, HOLD_CARD_MAN, ZONE_GB ");
            sql.append("ORDER BY ZONE_GB, CRPT_NAME ");

        }
        return sql.toString();
    }

    @Override
    public String[] buildHqls() {
        return new String[]{buildSql(1), buildSql(2)};
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<Object[]> result = CollectionUtil.castList(Object[].class, list);
        for (Object[] obj : result) {
            //地区去除省份
            String fullName = StringUtils.objectToString(obj[1]);
            obj[1] = fullName.substring(fullName.indexOf("_") + 1);
        }
    }

    /**
     * 选择码表弹框页面
     */
    public void selSimpleCodeAction() {
        String titleName = "";
        String selectIds = "";
        String dialogUrl = "";
        Integer width = null;
        Integer contentWidth = null;
        Integer height = null;
        Integer contentHeight = null;
        switch (this.simpleCodeOpType) {
            case "5002":
                titleName = "行业类别";
                selectIds = this.selectIndusTypeIds;
                dialogUrl = "/webapp/system/indusTypeCodeMulitySelectList";
                contentWidth = 800;
                contentHeight = 500;
                break;
            case "5003":
                titleName = "经济类型";
                selectIds = this.selectEconomyIds;
                dialogUrl = "/webapp/system/codeMulitySelectList";
                contentWidth = 700;
                contentHeight = 500;
                break;
            case "5007":
                titleName = "危害因素";
                selectIds = this.selectBadRsnIds;
                dialogUrl = "/webapp/system/codeMulitySelectList";
                contentWidth = 700;
                contentHeight = 500;
                break;
            default:
                break;
        }
        Map<String, Object> options = MapUtils.produceDialogMap(width, contentWidth, height, contentHeight);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add(titleName);
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>();
        paramList.add(this.simpleCodeOpType);
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<>();
        paramList.add(selectIds);
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<>();
        paramList.add("true");
        paramMap.put("ifShowFirstCode", paramList);

        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog(dialogUrl, options, paramMap);
    }

    /**
     * 选择码表后操作
     *
     * @param event 选择项
     */
    public void onSimpleCodeAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = CollectionUtil.castList(TsSimpleCode.class, selectedMap.get("selectPros"));
            if (ObjectUtil.isNotEmpty(list)) {
                StringBuilder names = new StringBuilder();
                StringBuilder ids = new StringBuilder();
                for (TsSimpleCode t : list) {
                    names.append("，").append(t.getCodeName());
                    ids.append(",").append(t.getRid());
                }
                switch (this.simpleCodeOpType) {
                    case "5002":
                        this.selectIndusTypeIds = ids.substring(1);
                        this.selectIndusTypeNames = names.substring(1);
                        break;
                    case "5003":
                        this.selectEconomyIds = ids.substring(1);
                        this.selectEconomyNames = names.substring(1);
                        break;
                    case "5007":
                        this.selectBadRsnIds = ids.substring(1);
                        this.selectBadRsnNames = names.substring(1);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    /**
     * 清除页面选择码表
     */
    public void clearSimpleCode() {
        switch (this.simpleCodeOpType) {
            case "5002":
                this.selectIndusTypeNames = null;
                this.selectIndusTypeIds = null;
                break;
            case "5003":
                this.selectEconomyNames = null;
                this.selectEconomyIds = null;
                break;
            case "5007":
                this.selectBadRsnIds = null;
                this.selectBadRsnNames = null;
                break;
            default:
                break;
        }
    }

    public void exportBefore() {
        RequestContext context = RequestContext.getCurrentInstance();
        int count = this.commService.findCountBySql(buildSql(2), this.paramMap);
        if (count == 0) {
            JsfUtil.addErrorMessage("无可导出的数据！");
            return;
        }
        context.execute("getDownloadFileClick();");
    }

    /**
     * 导出操作
     */
    public DefaultStreamedContent export() {
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("showStatus();");
        ExcelExportUtil excelExportUtil;
        String[] excelHeaders = new String[]{"地区", "用人单位名称", "社会信用代码", "行业类别", "经济类型", "企业规模", "联系人", "联系电话", "职工人数", "接害人数", "职业病危害因素种类"};
        excelExportUtil = new ExcelExportUtil("用人单位综合展示", excelHeaders, pakExcelExportDataList(executeExportSql(), excelHeaders.length));
        excelExportUtil.setColumnWidths(new Integer[]{null, null, 18, null, null, 6, 9, 12, 6, 6, null});
        excelExportUtil.setNeedTitle(false);
        excelExportUtil.setFrozenPaneRowsNum(1);
        Workbook wb = excelExportUtil.exportExcel();
        if (wb != null) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                String fileName = URLEncoder.encode("用人单位综合展示.xlsx", "UTF-8");
                wb.write(baos);
                baos.flush();
                byte[] aa = baos.toByteArray();
                context.execute("hideStatus();");
                return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
            } catch (Exception e) {
                JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            }
        }
        context.execute("hideStatus();");
        return null;
    }

    /**
     * 查询导出数据
     *
     * @return 导出数据
     */
    private List<Object[]> executeExportSql() {
        return CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(buildSql(3), this.paramMap));
    }

    private List<ExcelExportObject[]> pakExcelExportDataList(List<Object[]> dataList, int headerSize) {
        if (ObjectUtil.isEmpty(dataList) || headerSize <= 0) {
            return new ArrayList<>();
        }
        List<ExcelExportObject[]> excelExportObjectList = new ArrayList<>();
        for (Object[] data : dataList) {
            ExcelExportObject[] objects = new ExcelExportObject[headerSize];
            //电话脱敏
            data[8] = StringUtils.encryptPhone(StringUtils.objectToString(data[8]));

            int index = 0;
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]));
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]));
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]));
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]));
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[++index]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index + 2]));
            excelExportObjectList.add(objects);
        }
        return excelExportObjectList;
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {
        this.showArchives = null;
        initBaseInfo();
        initArchivesBase();
        //场所监测档案
        initPlaceBase();
        if (this.declareData != null) {
            onDeclareClick();
            RequestContext.getCurrentInstance().execute("changeDaPanelItemSelByArchives(1);");
        } else if (this.placeDate != null) {
            onMonitoringClick();
            RequestContext.getCurrentInstance().execute("changeDaPanelItemSelByArchives(2);");
        }
    }
    /**
     *  <p>方法描述：场所监测档案</p>
     * @MethodAuthor hsj 2023-06-27 9:52
     */
    private void initPlaceBase() {
        this.placeDate = null;
        this.placeDateList =new LinkedList<>();
        this.placeThatYear = Boolean.FALSE;
        if(this.rid == null ){
            return;
        }
        StringBuffer sql = new StringBuffer();
        sql.append(" select T.RID,T.YEAR ");
        sql.append(" from TD_ZXJC_UNITBASICINFO T ");
        sql.append(" where exists(select 1 from TD_ZXJC_UNITFACTORCROWD T1 where T1.MAIN_ID=T.RID) ");
        sql.append("  and exists(select 1 from TD_ZXJC_RESULT_PRO T2 where T2.MAIN_ID=T.RID) ");
        sql.append("  and T.CRPT_ID=").append(rid);
        sql.append(" order by T.YEAR desc ");
        List<Object[]> dataList = this.commService.findDataBySqlNoPage(sql.toString(),null);
        if(!CollectionUtils.isEmpty(dataList)){
            this.placeDate = dataList.get(0)[1] == null ? null : Integer.valueOf(dataList.get(0)[1].toString());

            if(placeDate != null && placeDate == DateUtils.getYearInt()){
                this.placeThatYear = Boolean.TRUE;
            }
            for(Object[] obj : dataList){
                Integer key =obj[1] == null ? null : Integer.valueOf(obj[1].toString());
                if(!this.placeDateList.contains(key)){
                    this.placeDateList.add(key);
                }
            }
        }
    }

    private void initBaseInfo() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", this.rid);
        String sql = "SELECT Z.FULL_NAME, " +
                "       C.CRPT_NAME, " +
                "       C.INSTITUTION_CODE, " +
                "       C.ADDRESS, " +
                "       C.ENROL_ADDRESS, " +
                "       SC3.CODE_NAME INDUS_TYPE, " +
                "       SC1.CODE_NAME ECONOMY, " +
                "       SC2.CODE_NAME CRPT_SIZE, " +
                "       C.LINKMAN2, " +
                "       C.LINKPHONE2, " +
                "       SC3.CODE_DESC OCC_RISK_CLASS, " +
                "       ''            BADRSN " +
                "FROM TB_TJ_CRPT C " +
                "         LEFT JOIN TS_ZONE Z ON C.ZONE_ID = Z.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC1 ON C.ECONOMY_ID = SC1.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC2 ON C.CRPT_SIZE_ID = SC2.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC3 ON C.INDUS_TYPE_ID = SC3.RID " +
                "         LEFT JOIN TB_TJ_CRPT_BADRSN BR ON C.RID = BR.CRPT_ID " +
                "WHERE C.RID = :rid";
        List<Object[]> dataList = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql, paramMap));
        if (CollectionUtils.isEmpty(dataList)) {
            this.crptBaseInfo = new Object[11];
            return;
        }
        this.crptBaseInfo = dataList.get(0);
        //地区去除省份
        String fullName = StringUtils.objectToString(this.crptBaseInfo[0]);
        this.crptBaseInfo[0] = fullName.substring(fullName.indexOf("_") + 1);
        if (ObjectUtil.isEmpty(this.occRiskClassMap)) {
            this.occRiskClassMap = new HashMap<>();
            List<TsSimpleCode> occRiskClassStrList = this.commService.findNumSimpleCodesByTypeId("5559");
            for (TsSimpleCode simpleCode : occRiskClassStrList) {
                this.occRiskClassMap.put(simpleCode.getExtendS1(), simpleCode.getCodeName());
            }
        }
        this.crptBaseInfo[10] = this.occRiskClassMap.get(StringUtils.objectToString(this.crptBaseInfo[10]));
        sql = "SELECT LISTAGG(BADRSN_NAME || '：' || BADRSN_NUM || '个', ' | ') WITHIN GROUP ( ORDER BY BADRSN_NUM DESC, BADRSN_NAME ) " +
                "FROM (SELECT RID, BADRSN_NAME, COUNT(1) BADRSN_NUM " +
                "      FROM (SELECT C.RID, " +
                "                   SC5.CODE_NAME BADRSN_NAME " +
                "            FROM TB_TJ_CRPT C " +
                "                     LEFT JOIN TB_TJ_CRPT_BADRSN BR ON C.RID = BR.CRPT_ID " +
                "                     LEFT JOIN TS_SIMPLE_CODE SC4 ON BR.BADRSN_ID = SC4.RID " +
                "                     LEFT JOIN TS_SIMPLE_CODE SC5 ON SC4.CODE_LEVEL_NO LIKE SC5.CODE_NO || '.%' AND SC5.CODE_NO = SC5.CODE_LEVEL_NO AND SC5.CODE_TYPE_ID = SC4.CODE_TYPE_ID " +
                "            WHERE SC5.CODE_NAME IS NOT NULL AND C.RID = :rid " +
                "            GROUP BY C.RID, BR.BADRSN_ID, SC5.CODE_NAME, SC5.CODE_NO) " +
                "      GROUP BY RID, BADRSN_NAME) T " +
                "GROUP BY RID";
        List<String> badrsnDataList = CollectionUtil.castList(String.class, this.commService.findDataBySqlNoPage(sql, paramMap));
        if (CollectionUtils.isEmpty(badrsnDataList)) {
            return;
        }
        this.crptBaseInfo[11] = badrsnDataList.get(0);
    }

    public void openBadrsnDialog() {
        //重置页面弹出框当前页码
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:viewForm:occRiskClassTable");
        if (dataTable != null) {
            dataTable.setFirst(0);
            dataTable.setRows(10);
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", this.rid);
        String sql = "SELECT SC2.CODE_NAME B, SC1.CODE_NAME S, SC1.EXTENDS7, '', '' " +
                "FROM TB_TJ_CRPT C " +
                "         LEFT JOIN TB_TJ_CRPT_BADRSN BR ON C.RID = BR.CRPT_ID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC1 ON BR.BADRSN_ID = SC1.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC2 ON SC1.CODE_LEVEL_NO LIKE SC2.CODE_NO || '.%' AND SC2.CODE_NO = SC2.CODE_LEVEL_NO AND SC2.CODE_TYPE_ID = SC1.CODE_TYPE_ID " +
                "WHERE SC1.CODE_NAME IS NOT NULL AND C.RID = :rid " +
                "GROUP BY SC2.CODE_NAME, SC1.CODE_NAME, SC1.EXTENDS7, SC1.NUM, SC1.CODE_NO " +
                "ORDER BY SC1.NUM, SC1.CODE_NO";
        this.occRiskClassList = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql, paramMap));
        for (Object[] occRiskClass : this.occRiskClassList) {
            String extends7 = StringUtils.objectToString(occRiskClass[2]);
            if ("1".equals(extends7)) {
                occRiskClass[3] = 1;
            } else if ("2".equals(extends7)) {
                occRiskClass[4] = 1;
            } else if ("1,2".equals(extends7)) {
                occRiskClass[3] = 1;
                occRiskClass[4] = 1;
            }
        }
        RequestContext.getCurrentInstance().execute("PF('OccRiskClassDialog').show()");
    }

    /**
     * 初始化职业健康管理档案基础信息
     */
    private void initArchivesBase() {
        this.hasDeclareDataForTheYear = null;
        this.declareData = null;
        this.unitbasicinfoRid = null;
        this.unitbasicinfo = new TdZyUnitbasicinfo();
        Map<String, Object> paramMap1 = new HashMap<>();
        paramMap1.put("rid", this.rid);
        String hql = "select new TdZyUnitbasicinfo(t.rid, t.declareDate) from TdZyUnitbasicinfo t where t.declareDate is not null and t.fkByCrpt.rid = :rid order by t.declareDate desc, t.rid desc";
        this.unitbasicinfoSelList = CollectionUtil.castList(TdZyUnitbasicinfo.class, this.commService.findData(hql, paramMap1));
        if (!CollectionUtils.isEmpty(this.unitbasicinfoSelList)) {
            this.declareData = this.unitbasicinfoSelList.get(0).getDeclareDate();
            this.unitbasicinfoRid = this.unitbasicinfoSelList.get(0).getRid();
        }
        if (this.declareData != null) {
            this.hasDeclareDataForTheYear = DateUtils.getYear().equals(DateUtils.formatDate(this.declareData, "yyyy"));
        }
    }

    /**
     * 展示在线申报档案信息
     */
    public void onDeclareClick() {
        if (new Integer(1).equals(this.showArchives)) {
            return;
        }
        this.showArchives = 1;
        pakDeclareInfo(this.unitbasicinfoRid);
    }

    /**
     * 封装在线申报档案信息
     */
    public void pakDeclareInfo(Integer unitbasicinfoRid) {
        if (unitbasicinfoRid == null) {
            unitbasicinfoRid = this.unitbasicinfoSelList.get(0).getRid();
        }
        this.unitbasicinfo = this.commService.find(TdZyUnitbasicinfo.class, unitbasicinfoRid);
        //培训情况
        this.unitbasicinfo.setTrainSituation(this.commService.findEntityByMainId(TdZyTrainSituation.class, unitbasicinfoRid));
        if (this.unitbasicinfo.getTrainSituation() == null) {
            this.unitbasicinfo.setTrainSituation(new TdZyTrainSituation());
        }
        //主要产品
        List<TdZyUnitmainprod> prodList = this.commService.findEntityListByMainId(TdZyUnitmainprod.class, unitbasicinfoRid);
        List<String> prodStrList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(prodList)) {
            for (TdZyUnitmainprod unitmainprod : prodList) {
                if ("无".equals(unitmainprod.getProdName())) {
                    prodStrList.add("无");
                    continue;
                }
                prodStrList.add(unitmainprod.getProdName() + "：" + unitmainprod.getAnnualOutput() + "（" + unitmainprod.getUnitName() + "）");
            }
        }
        if (CollectionUtils.isEmpty(prodStrList)) {
            this.unitbasicinfo.setProStr("/");
        } else {
            this.unitbasicinfo.setProStr(StringUtils.list2string(prodStrList, "；"));
        }
        //技术服务机构
        List<TdZyHethOrg> hethOrgList = this.commService.findEntityListByMainId(TdZyHethOrg.class, unitbasicinfoRid);
        List<String> hethOrgStrList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(hethOrgList)) {
            for (TdZyHethOrg hethOrg : hethOrgList) {
                hethOrgStrList.add(hethOrg.getUnitName());
            }
        }
        if (CollectionUtils.isEmpty(hethOrgStrList)) {
            this.unitbasicinfo.setHethOrgStr("/");
        } else {
            CollectionUtil.removeDupByContains(hethOrgStrList);
            this.unitbasicinfo.setHethOrgStr(StringUtils.list2string(hethOrgStrList, "，"));
        }
        List<TdZyJcOrg> jcOrgList = this.commService.findEntityListByMainId(TdZyJcOrg.class, unitbasicinfoRid);
        List<String> jcOrgStrList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(jcOrgList)) {
            for (TdZyJcOrg jcOrg : jcOrgList) {
                jcOrgStrList.add(jcOrg.getUnitName());
            }
        }
        if (CollectionUtils.isEmpty(jcOrgStrList)) {
            this.unitbasicinfo.setJcOrgStr("/");
        } else {
            CollectionUtil.removeDupByContains(jcOrgStrList);
            this.unitbasicinfo.setJcOrgStr(StringUtils.list2string(jcOrgStrList, "，"));
        }
        //职业病危害因素种类及接触人数
        this.unitbasicinfo.setUnitfactorcrowd(this.commService.findEntityByMainId(TdZyUnitfactorcrowd.class, unitbasicinfoRid));
        if (this.unitbasicinfo.getUnitfactorcrowd() == null) {
            this.unitbasicinfo.setUnitfactorcrowd(new TdZyUnitfactorcrowd());
        }
        //职业健康监护开展情况
        this.unitbasicinfo.setUnithealthcustody(this.commService.findEntityByMainId(TdZyUnithealthcustody.class, unitbasicinfoRid));
        if (this.unitbasicinfo.getUnithealthcustody() == null) {
            this.unitbasicinfo.setUnithealthcustody(new TdZyUnithealthcustody());
        }
        //职业病危害因素检测情况
        this.unitbasicinfo.setUnitharmfactorcheck(this.commService.findEntityByMainId(TdZyUnitharmfactorcheck.class, unitbasicinfoRid));
        if (this.unitbasicinfo.getUnitharmfactorcheck() == null) {
            this.unitbasicinfo.setUnitharmfactorcheck(new TdZyUnitharmfactorcheck());
        }
        List<List<List<String>>> detailList = new ArrayList<>();
        detailList.add(new ArrayList<List<String>>());
        detailList.add(new ArrayList<List<String>>());
        detailList.add(new ArrayList<List<String>>());
        detailList.add(new ArrayList<List<String>>());
        detailList.add(new ArrayList<List<String>>());
        detailList.add(new ArrayList<List<String>>());
        Map<String, Object> paramMap1 = new HashMap<>();
        paramMap1.put("rid", unitbasicinfoRid);
        String sql = "SELECT F.MAIN_ID, F.HAZARDS_SORT, F.HAZARDS_NAME, F.CONTACT_NUMBER, HE.PE_NUM, H.CHECK_POINTS, H.OVERPROOF_POINTS " +
                "FROM TD_ZY_UNITFACTORC_DETAIL F " +
                "         LEFT JOIN TS_SIMPLE_CODE SC " +
                "                   ON F.HAZARDS_ID = SC.RID " +
                "         LEFT JOIN TD_ZY_UNITHEALTH_DETAIL HE " +
                "                   ON HE.MAIN_ID = F.MAIN_ID AND HE.HAZARDS_ID = F.HAZARDS_ID AND " +
                "                      HE.HAZARDS_SORT = F.HAZARDS_SORT " +
                "         LEFT JOIN TD_ZY_UNITHARMFACT_DETAIL H " +
                "                   ON H.MAIN_ID = F.MAIN_ID AND H.HAZARDS_ID = F.HAZARDS_ID AND " +
                "                      H.HAZARDS_SORT = F.HAZARDS_SORT " +
                "WHERE F.CONTACT_NUMBER > 0 AND F.MAIN_ID = :rid " +
                "GROUP BY F.MAIN_ID, F.HAZARDS_SORT, F.HAZARDS_NAME, F.CONTACT_NUMBER, HE.PE_NUM, H.CHECK_POINTS, H.OVERPROOF_POINTS, SC.NUM, SC.CODE_NO " +
                "ORDER BY SC.NUM, SC.CODE_NO";
        List<Object[]> dataList = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql, paramMap1));
        if (!CollectionUtils.isEmpty(dataList)) {
            for (Object[] objects : dataList) {
                Integer sort = ObjectUtil.convert(Integer.class, objects[1]);
                if (sort == null || sort < 1 || sort > 6) {
                    continue;
                }
                List<String> hazardList = new ArrayList<>();
                String hazardsName = StringUtils.objectToString(objects[2]);
                hazardList.add(hazardsName);
                String contactNumber = ObjectUtil.convert(String.class, objects[3], "—");
                hazardList.add(contactNumber);
                String peNum = ObjectUtil.convert(String.class, objects[4], "—");
                hazardList.add(peNum);
                String checkPoints = ObjectUtil.convert(String.class, objects[5], "—");
                hazardList.add(checkPoints);
                String overproofPoints = ObjectUtil.convert(String.class, objects[6], "—");
                hazardList.add(overproofPoints);
                detailList.get(sort - 1).add(hazardList);
            }
        }
        this.unitbasicinfo.setDetailList(detailList);
    }

    /**
     * 跳转申报信息页面
     */
    public void viewUnitBasicInfo() {
        if (this.unitbasicinfo == null || this.unitbasicinfo.getRid() == null) {
            return;
        }
        RequestContext.getCurrentInstance().execute("top.ShortcutMenuClick('','在线申报档案','" + "/webapp/heth/comm/crptshow/tdZyUnitBasicInfoView.faces?rid=" + this.unitbasicinfo.getRid() + "','');");
    }

    /**
     * 展示场所监测档案信息
     */
    public void onMonitoringClick() {
        if (new Integer(2).equals(this.showArchives)) {
            return;
        }
        this.showArchives = 2;
        initPlaceInfo();
    }
    /**
     *  <p>方法描述：跳转场所监测档案详情</p>
     * @MethodAuthor hsj 2023-06-27 10:33
     */
    public void csjcViewAction(){
        if (null==unitbasic || null== unitbasic.getRid()) {
            return;
        }
        StringBuffer url = new StringBuffer();
        url.append("/webapp/heth/comm/crptshow/TdZxjcUnitBasicInfoView.faces?rid=").append(unitbasic.getRid());
        RequestContext.getCurrentInstance().execute("top.ShortcutMenuClick('','场所监测档案','"+url+"','');");
    }
    /**
     *  <p>方法描述：场所监测档案</p>
     * @MethodAuthor hsj 2023-06-26 9:18
     */
    public void initPlaceInfo() {
        this.unitbasic = new UnitbasicInfo();
        this.unitharmChkInfos = new LinkedList<>();
        this.unitHethCusInfos =new LinkedList<>();
        this.resultProInfos =new LinkedList<>();
        if(this.rid == null || this.placeDate == null){
            return;
        }
        //基本情况概况
        placeBaseInfo();
        if(unitbasic != null && unitbasic.getRid() != null){
            //上一年度检测情况
            unitharmChk(unitbasic.getRid());
            //上一年度职业健康检查情况
            unitHethCus(unitbasic.getRid());
            //监测结果
            resultPro(unitbasic.getRid());
        }

    }
    /**
     *  <p>方法描述：监测结果</p>
     * @MethodAuthor hsj 2023-06-26 14:39
     */
    private void resultPro(Integer mianId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mianId", mianId);
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT T.FACTOR_ID,T1.CODE_NAME ,T.JOB_NAME ,T.JOB_NAME_OTHER,T.FACTOR_PRO_NAME,T.IF_QUALIFIED,T.JOB_ID,T.IF_PLACE_HG,T.IF_CTWA_HG");
        sql.append(" FROM  TD_ZXJC_RESULT_PRO T ");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T1 ON T.FACTOR_ID = T1.RID");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T.FACTOR_PRO_ID = T1.RID ");
        sql.append(" WHERE T.MAIN_ID =:mianId");
        sql.append(" ORDER BY (T.FACTOR_ID || '&' || T.JOB_NAME || '&' || T.JOB_NAME_OTHER) ,T2.NUM,T.FIX_TYPE DESC,T.RID DESC");
        List<Object[]> dataList = this.commService.findDataBySqlNoPage(sql.toString(),paramMap);
        List<Integer> rids = new LinkedList<>();
        Map<Integer,List<UnitbasicInfo>> map =new HashMap<>();
        Map<Integer,String> nameMap =new HashMap<>();
        if(!CollectionUtils.isEmpty(dataList)){
            for(Object[] obj : dataList){
                Integer key = obj[0] == null ? null : Integer.valueOf(obj[0].toString());
                if(!rids.contains(key)){
                    rids.add(key);
                }
                nameMap.put(key,ObjectUtil.toStr(obj[1]));
                UnitbasicInfo unitbasicInfo = new UnitbasicInfo();

                String jobName = ObjectUtil.toStr(obj[2]);
                if(obj[3] != null){
                    jobName += "（"+ ObjectUtil.toStr(obj[3]) + "）";
                }
                unitbasicInfo.setValue1(jobName);
                unitbasicInfo.setValue2( ObjectUtil.toStr(obj[4]));
                unitbasicInfo.setValue3("1".equals(ObjectUtil.toStr(obj[5])) ? "是" : "否");
                //
                unitbasicInfo.setValue4(ObjectUtil.toStr(obj[6]));
                unitbasicInfo.setValue5(ObjectUtil.toStr(obj[7]));
                unitbasicInfo.setValue6(ObjectUtil.toStr(obj[8]));
                if(map.containsKey(key)){
                    map.get(key).add(unitbasicInfo);
                }else {
                    List<UnitbasicInfo> unitbasicInfos = new LinkedList<>();
                    unitbasicInfos.add(unitbasicInfo);
                    map.put(key,unitbasicInfos);
                }
            }
            for (Integer rid : rids) {
                if(map.containsKey(rid)){
                    PlaceInfo placeInfo = new PlaceInfo();
                    placeInfo.setName(nameMap.get(rid));
                    List<UnitbasicInfo> unitbasics = new LinkedList<>();
                    List<UnitbasicInfo> unitbasicInfos = map.get(rid);
                    if(CollectionUtils.isEmpty(unitbasicInfos)){
                        continue;
                    }
                    //根据岗位rid分组
                    Map<String,List<UnitbasicInfo>> basicMap = new HashMap<>();
                    GroupUtil.listGroup2Map(unitbasicInfos,basicMap,UnitbasicInfo.class,"getValue4");
                    for(Map.Entry<String, List<UnitbasicInfo>> entry : basicMap.entrySet()) {
                        List<UnitbasicInfo> list = entry.getValue();
                        UnitbasicInfo unitbasicInfo = new UnitbasicInfo();
                        unitbasicInfo.setValue1(list.get(0).getValue1());
                        unitbasicInfo.setValue2(list.get(0).getValue2() == null ? "—" : list.get(0).getValue2());
                        unitbasicInfo.setValue4(list.get(0).getValue3());
                        boolean isFalg = true;//默认合格
                        for(UnitbasicInfo info : list){
                            if("0".equals(info.getValue5()) || "0".equals(info.getValue6())){
                                isFalg = false;
                                break;
                            }
                        }
                        unitbasicInfo.setValue3(isFalg ? "是" : "否");
                        unitbasics.add(unitbasicInfo);
                    }
                    placeInfo.setUnitbasicInfos(unitbasics);
                    this.resultProInfos.add(placeInfo);
                }
            }
        }
    }

    /**
     *  <p>方法描述：上一年度职业健康检查情况</p>
     * @MethodAuthor hsj 2023-06-26 14:02
     */
    private void unitHethCus(Integer mianId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mianId", mianId);
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT T1.IFHEA, T1.IFHEA_DUST,T1.IFHEA_CHEMISTRY,T1.IFHEA_PHYSICS,T1.IFHEA_EMIT,T1.IFHEA_BIOLOGICAL,T1.IFHEA_OTHER");
        sql.append(" ,T.TYPE,T.FACTOR_NAME ,T.CHECK_MONITOR_PEOPLES,T.CHECK_SHOULD_PEOPLES,T.CHECK_ACTUAL_PEOPLES,T.UNUSUAL_NUM");
        sql.append(" FROM TD_ZXJC_UNITHEALTH_ITEM  T");
        sql.append(" LEFT JOIN TD_ZXJC_UNIT_HETH_CUS T1 ON T.MAIN_ID = T1.RID ");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T.FACTOR_ID = T2.RID");
        sql.append(" WHERE T.IFHEA_FACTOR = 1");
        sql.append(" AND T1.MAIN_ID =:mianId");
        sql.append(" ORDER BY T.TYPE,T.FACTOR_ID,T2.NUM");
        List<Object[]> dataList = this.commService.findDataBySqlNoPage(sql.toString(),paramMap);
        dealUnit(dataList,2);
    }

    /**
     *  <p>方法描述：上一年度检测情况</p>
     * @MethodAuthor hsj 2023-06-26 11:16
     */
    private void unitharmChk(Integer mianId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mianId", mianId);
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT T1.IFAT, T1.IFAT_DUST,T1.IFAT_CHEMISTRY,T1.IFAT_PHYSICS,T1.IFAT_EMIT,T1.IFAT_BIOLOGICAL,T1.IFAT_OTHER");
        sql.append(" ,T.TYPE,T.FACTOR_NAME ,T.CHECK_NUM,T.EXCESS_NUM,T.WORK_NUM,T.WORK_EXCESS_NUM");
        sql.append(" FROM TD_ZXJC_UNITHARMCHECK_SUB T");
        sql.append(" LEFT JOIN TD_ZXJC_UNITHARM_CHK T1 ON T.MAIN_ID = T1.RID ");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T.FACTOR_ID = T2.RID");
        sql.append(" WHERE T.IFAT_FACTOR = 1");
        sql.append(" AND T1.MAIN_ID =:mianId");
        sql.append(" ORDER BY T.TYPE,T.FACTOR_ID,T2.NUM");
        List<Object[]> dataList = this.commService.findDataBySqlNoPage(sql.toString(),paramMap);
        dealUnit(dataList,1);

    }
    /**
     *  <p>方法描述：上一年度检测情况/上一年度职业健康检查情况处理</p>
     * @MethodAuthor hsj 2023-06-26 14:03
     */
    private void dealUnit(List<Object[]> dataList,Integer dealType) {
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }
        Map<String,List<UnitbasicInfo>> map =new HashMap<>();
        Map<String,String> nameMap =new HashMap<>();
        for(Object[] obj : dataList){
            String ifat = ObjectUtil.toStr(obj[0]);
            if(!"1".equals(ifat)){
                break;
            }
            String type = ObjectUtil.toStr(obj[7]);
            if(StringUtils.isBlank(type)){
                continue;
            }
            boolean ifExist = false;
            switch (type){
                case "1":
                    //粉尘_检测【IFAT_DUST】
                    nameMap.put(type,"粉尘因素");
                    if("1".equals(ObjectUtil.toStr(obj[1]))){
                        ifExist = true;
                    }
                    break;
                case "2":
                    //化学物质_检测【IFAT_CHEMISTRY】
                    nameMap.put(type,"化学因素");
                    if("1".equals(ObjectUtil.toStr(obj[2]))){
                        ifExist = true;
                    }
                    break;
                case "3":
                    //物理因素_检测【IFAT_PHYSICS】
                    nameMap.put(type,"物理因素");
                    if("1".equals(ObjectUtil.toStr(obj[3]))){
                        ifExist = true;
                    }
                    break;
                case "4":
                    //放射因素_检测【IFAT_EMIT】
                    nameMap.put(type,"放射因素");
                    if("1".equals(ObjectUtil.toStr(obj[4]))){
                        ifExist = true;
                    }
                    break;
                case "5":
                    //生物因素_检测【IFAT_BIOLOGICAL】
                    nameMap.put(type,"生物因素");
                    if("1".equals(ObjectUtil.toStr(obj[5]))){
                        ifExist = true;
                    }
                    break;
                case "6":
                    //其他因素_检测【IFAT_OTHER】
                    nameMap.put(type,"其他因素");
                    if("1".equals(ObjectUtil.toStr(obj[6]))){
                        ifExist = true;
                    }
                    break;

            }
            if(!ifExist){
                map.put(type,null);
                continue;
            }
            UnitbasicInfo unitbasicInfo = new UnitbasicInfo();
            unitbasicInfo.setValue1(ObjectUtil.toStr(obj[8]));
            unitbasicInfo.setValue2(obj[9] == null ? "0" : obj[9].toString());
            unitbasicInfo.setValue3(obj[10] == null ? "0" : obj[10].toString());
            unitbasicInfo.setValue4(obj[11] == null ? "0" : obj[11].toString());
            unitbasicInfo.setValue5(obj[12] == null ? "0" : obj[12].toString());
            if(map.containsKey(type)){
                map.get(type).add(unitbasicInfo);
            }else {
                List<UnitbasicInfo> unitbasicInfos = new LinkedList<>();
                unitbasicInfos.add(unitbasicInfo);
                map.put(type,unitbasicInfos);
            }
        }
        for(Integer i = 1;i<= 6;i++){
            if(map.containsKey(i.toString())){
                PlaceInfo placeInfo = new PlaceInfo();
                placeInfo.setName(nameMap.get(i.toString()));
                placeInfo.setUnitbasicInfos(map.get(i.toString()));
                if(dealType == 1){
                    this.unitharmChkInfos.add(placeInfo);
                }else {
                    this.unitHethCusInfos.add(placeInfo);
                }
            }
        }
    }

    /**
     *  <p>方法描述：基本情况概况</p>
     * @MethodAuthor hsj 2023-06-26 10:42
     */
    private void placeBaseInfo() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", this.rid);
        paramMap.put("placeDate", this.placeDate);
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT T.EMP_NUM,T.EXTERNAL_NUM,T1.CONTACT_TOTAL_PEOPLES,T.IF_LEADERS_TRAIN,T.IF_MANAGERS_TRAIN,T.TRAIN_SUM,T.RID FROM TD_ZXJC_UNITBASICINFO T");
        sql.append(" LEFT JOIN TD_ZXJC_UNITFACTORCROWD T1 ON T.RID  = T1.MAIN_ID");
        sql.append(" WHERE T.CRPT_ID =:rid ");
        sql.append(" AND T.YEAR = :placeDate ");
        sql.append(" ORDER BY T.RID DESC");
        List<Object[]> dataList = this.commService.findDataBySqlNoPage(sql.toString(),paramMap);
        if(!CollectionUtils.isEmpty(dataList)){
            Object[] obj= dataList.get(0);
            unitbasic.setValue1(obj[0] == null ? "0" :  obj[0].toString());
            unitbasic.setValue2(obj[1] == null ? "0" :  obj[1].toString());
            unitbasic.setValue3(obj[2] == null ? "0" :  obj[2].toString());
            unitbasic.setValue4("1".equals(ObjectUtil.toStr(obj[3])) ? "已培训" : "未培训");
            unitbasic.setValue5("1".equals(ObjectUtil.toStr(obj[4])) ? "已培训" : "未培训");
            unitbasic.setValue6(obj[5] == null ? "0" :  obj[5].toString());
            unitbasic.setRid(obj[6] == null ? null : Integer.valueOf( obj[6].toString()));
        }
    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    /**
     * <p>方法描述：切换统计图类型 </p>
     * @MethodAuthor： pw 2023/6/27
     **/
    public void changeChartType(){
        this.chartJson = this.chartJsonMap.get(this.chartType);
        this.chartDataSize = this.chartDataSizeMap.get(this.chartType);
    }

    /**
     * <p>方法描述： 统计图初始化 </p>
     * @MethodAuthor： pw 2023/6/27
     **/
    private void initChart(){
        this.chartType = 1;
        this.chartJsonMap = new HashMap<>();
        this.chartDataSize = 0;
        this.chartDataSizeMap = new HashMap<>();
        this.initChartJsonMap();
        this.changeChartType();
    }

    /**
     * <p>方法描述：缓存各类型统计图 </p>
     * @MethodAuthor： pw 2023/6/27
     **/
    private void initChartJsonMap(){
        TsZone manageZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        this.generateZoneChart(manageZone);
        this.generateIndusTypeChart(manageZone);
        this.generateEconomyChart(manageZone);
        this.generateCrptSizeChart(manageZone);
        this.generateDrugChart(manageZone);
        this.generateCaChart(manageZone);
    }

    /**
     * <p>方法描述：生成地区的统计图 </p>
     * @MethodAuthor： pw 2023/6/27
     **/
    private void generateZoneChart(TsZone manageZone){
        this.chartJsonMap.put(1,"");
        this.chartDataSizeMap.put(1, 0);
        if(null == manageZone){
            return;
        }
        String zoneGb = manageZone.getZoneGb();
        //截取后的地区编码
        String subZoneGb = ZoneUtil.zoneSelect(zoneGb);
        Integer subZoneSize = subZoneGb.length();
        //非 省市区
        if(subZoneSize != 2 && subZoneSize != 4 && subZoneSize != 6){
            return;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT  M.* FROM ( SELECT");
        sqlBuffer.append(subZoneSize == 2 ? " SUBSTR(T1.ZONE_GB, 0, 4) || '000000'  " : " SUBSTR(T1.ZONE_GB, 0,6)||'0000' ");
        sqlBuffer.append(" AS ZONEGB, COUNT(T.RID) AS CURNUM ")
                .append(" FROM TB_TJ_CRPT T ")
                .append(" LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID ")
                .append(" WHERE T.DEL_MARK = 0 AND NVL(T.OPERATION_STATUS,1)=1 ").append(" AND T1.ZONE_GB LIKE :zoneGb ")
                .append(" GROUP BY  ");
        sqlBuffer.append(subZoneSize == 2 ? " SUBSTR(T1.ZONE_GB, 0, 4) || '000000' " : " SUBSTR(T1.ZONE_GB, 0,6)||'0000' ");
        sqlBuffer.append(" ) M ORDER BY M.CURNUM DESC, M.ZONEGB");
        Map<String,Object> paramsMap = new HashMap<>();
        paramsMap.put("zoneGb", subZoneGb + "%");
        List<Object[]> queryResult = this.commService.findDataBySqlNoPage(sqlBuffer.toString(), paramsMap);
        if(CollectionUtils.isEmpty(queryResult)){
            return;
        }
        List<Object[]> fillList = new ArrayList<>();
        Map<String,Object> tmpMap = new HashMap<>();
        for(Object[] objArr : queryResult){
            String curGb = null == objArr[0] ? null : objArr[0].toString();
            if(StringUtils.isBlank(curGb)){
                continue;
            }
            //省、市 对应本省或者本市 丢弃
            boolean flag = (subZoneSize == 2 || subZoneSize == 4) && curGb.equals(zoneGb);
            if(flag){
                continue;
            }
            TsZone tsZone = this.chartZoneMap.get(curGb);
            //可能出现停用的地区
            if(null == tsZone){
                tmpMap.put("zoneGb",curGb);
                List<TsZone> tmpList = this.commService.findDataByHqlNoPage(" select t from TsZone t where t.zoneGb=:zoneGb order by t.ifReveal desc ",tmpMap);
                if(CollectionUtils.isEmpty(tmpList)){
                    continue;
                }
                tsZone = tmpList.get(0);
                this.chartZoneMap.put(curGb, tsZone);
            }
            Object[] fillArr = new Object[2];
            fillArr[0] = null == tsZone.getZoneShortName() ? "" : tsZone.getZoneShortName();
            fillArr[1] = objArr[1];
            fillList.add(fillArr);
        }
        this.generateLineChart(fillList,  "地区", 1);
    }

    /**
     * <p>方法描述：生成行业类别的统计图 </p>
     * @MethodAuthor： pw 2023/6/27
     **/
    private void generateIndusTypeChart(TsZone manageZone){
        this.chartJsonMap.put(2,"");
        this.chartDataSizeMap.put(2, 0);
        if(null == manageZone){
            return;
        }
        String zoneGb = manageZone.getZoneGb();
        //截取后的地区编码
        String subZoneGb = ZoneUtil.zoneSelect(zoneGb);
        StringBuffer sqlBuffer = new StringBuffer();
        //行业类别大类
        sqlBuffer.append(" SELECT M.CODENO, M.CURNUM FROM ( ")
                .append(" SELECT CASE WHEN INSTR(T2.CODE_LEVEL_NO, '.') = 0 THEN T2.CODE_LEVEL_NO ELSE SUBSTR(T2.CODE_LEVEL_NO, 0, INSTR(T2.CODE_LEVEL_NO, '.') - 1) END AS CODENO, ")
                .append(" COUNT(T.RID) AS CURNUM ")
                .append(" FROM TB_TJ_CRPT T ")
                .append(" LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID ")
                .append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T.INDUS_TYPE_ID = T2.RID ")
                .append(" WHERE T.DEL_MARK = 0 AND NVL(T.OPERATION_STATUS, 1) = 1 ")
                .append("   AND T1.ZONE_GB LIKE :zoneGb ")
                .append(" GROUP BY CASE WHEN INSTR(T2.CODE_LEVEL_NO, '.') = 0 THEN T2.CODE_LEVEL_NO ELSE SUBSTR(T2.CODE_LEVEL_NO, 0, INSTR(T2.CODE_LEVEL_NO, '.') - 1) END ")
                .append(" ) M ")
                .append(" ORDER BY M.CURNUM DESC, M.CODENO ");

        Map<String,Object> paramsMap = new HashMap<>();
        paramsMap.put("zoneGb", subZoneGb + "%");
        List<Object[]> queryResult = this.commService.findDataBySqlNoPage(sqlBuffer.toString(), paramsMap);
        if(CollectionUtils.isEmpty(queryResult)){
            return;
        }
        List<Object[]> fillList = new ArrayList<>();
        for(Object[] objArr : queryResult){
            String codeNo = null == objArr[0] ? null : objArr[0].toString();
            TsSimpleCode simpleCode = this.findChartSimpleCode("5002", codeNo);
            if(null == simpleCode){
                continue;
            }
            Object[] fillArr = new Object[2];
            fillArr[0] = null == simpleCode.getCodeName() ? "" : simpleCode.getCodeName();
            fillArr[1] = objArr[1];
            fillList.add(fillArr);
        }
        this.generateLineChart(fillList,  "行业类别", 2);
    }

    /**
     * <p>方法描述： 生成经济类型的统计图 </p>
     * @MethodAuthor： pw 2023/6/27
     **/
    private void generateEconomyChart(TsZone manageZone){
        this.chartJsonMap.put(3,"");
        this.chartDataSizeMap.put(3, 0);
        if(null == manageZone){
            return;
        }
        String zoneGb = manageZone.getZoneGb();
        //截取后的地区编码
        String subZoneGb = ZoneUtil.zoneSelect(zoneGb);
        StringBuffer sqlBuffer = new StringBuffer();
        //二级类别
        sqlBuffer.append(" SELECT M.CODENO ,M.CURNUM FROM ( ")
                .append(" SELECT CASE WHEN INSTR(SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1), '.')=0 THEN ")
                .append(" SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1) ELSE ")
                .append(" SUBSTR(SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1),0,INSTR(SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1), '.')-1) END AS CODENO, ")
                .append(" COUNT(T.RID) AS CURNUM  ")
                .append(" FROM TB_TJ_CRPT T ")
                .append(" LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID ")
                .append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T.ECONOMY_ID = T2.RID ")
                .append(" WHERE T.DEL_MARK = 0 AND NVL(T.OPERATION_STATUS, 1) = 1 AND T1.ZONE_GB LIKE :zoneGb AND INSTR( T2.CODE_LEVEL_NO, '.') > 0  ")
                .append(" GROUP BY CASE WHEN INSTR(SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1), '.')=0 THEN ")
                .append("   SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1) ELSE ")
                .append("  SUBSTR(SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1),0,INSTR(SUBSTR(T2.CODE_LEVEL_NO,INSTR( T2.CODE_LEVEL_NO, '.')+1), '.')-1) END ")
                .append(" ) M ")
                .append(" ORDER BY M.CURNUM DESC,M.CODENO  ");
        Map<String,Object> paramsMap = new HashMap<>();
        paramsMap.put("zoneGb", subZoneGb + "%");
        List<Object[]> queryResult = this.commService.findDataBySqlNoPage(sqlBuffer.toString(), paramsMap);
        if(CollectionUtils.isEmpty(queryResult)){
            return;
        }
        List<Object[]> fillList = new ArrayList<>();
        for(Object[] objArr : queryResult){
            String codeNo = null == objArr[0] ? null : objArr[0].toString();
            TsSimpleCode simpleCode = this.findChartSimpleCode("5003", codeNo);
            if(null == simpleCode){
                continue;
            }
            Object[] fillArr = new Object[2];
            fillArr[0] = null == simpleCode.getCodeName() ? "" : simpleCode.getCodeName();
            fillArr[1] = objArr[1];
            fillList.add(fillArr);
        }
        this.generateLineChart(fillList,  "经济类型", 3);
    }

    /**
     * <p>方法描述： 生成企业规模的统计图 </p>
     * @MethodAuthor： pw 2023/6/27
     **/
    private void generateCrptSizeChart(TsZone manageZone){
        this.chartJsonMap.put(4,"");
        this.chartDataSizeMap.put(4, 0);
        if(null == manageZone){
            return;
        }
        String zoneGb = manageZone.getZoneGb();
        //截取后的地区编码
        String subZoneGb = ZoneUtil.zoneSelect(zoneGb);
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT M1.CODE_NAME, NVL(M.CURNUM, 0) ")
                .append("   FROM TS_SIMPLE_CODE M1 ")
                .append("   INNER JOIN TS_CODE_TYPE M2 ON M1.CODE_TYPE_ID=M2.RID ")
                .append("   LEFT JOIN (")
                .append("    SELECT T2.RID,COUNT(T.RID) AS CURNUM ")
                .append("    FROM TS_SIMPLE_CODE T2 ")
                .append("    LEFT JOIN TB_TJ_CRPT T ON T.CRPT_SIZE_ID = T2.RID ")
                .append("    LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID ")
                .append("    WHERE T.DEL_MARK = 0 AND NVL(T.OPERATION_STATUS, 1) = 1 AND T1.ZONE_GB LIKE :zoneGb ")
                .append("   GROUP BY T2.RID) M ON M1.RID = M.RID")
                .append("  WHERE M2.CODE_TYPE_NAME='5004' AND (M1.IF_REVEAL = '1' OR M.CURNUM > 0) ")
                .append("  ORDER BY M1.NUM ");
        Map<String,Object> paramsMap = new HashMap<>();
        paramsMap.put("zoneGb", subZoneGb + "%");
        List<Object[]> queryResult = this.commService.findDataBySqlNoPage(sqlBuffer.toString(), paramsMap);
        if(CollectionUtils.isEmpty(queryResult)){
            return;
        }
        this.generatePieChart(queryResult, 4);
    }

    /**
     * <p>方法描述：生成高毒的统计图 </p>
     * @MethodAuthor： pw 2023/6/27
     **/
    private void generateDrugChart(TsZone manageZone){
        this.chartJsonMap.put(5,"");
        this.chartDataSizeMap.put(5, 0);
        if(null == manageZone){
            return;
        }
        String zoneGb = manageZone.getZoneGb();
        this.executeDrugAndCaData(zoneGb, 5);
    }

    /**
     * <p>方法描述： 生成致癌的统计图</p>
     * @MethodAuthor： pw 2023/6/28
     **/
    private void generateCaChart(TsZone manageZone){
        this.chartJsonMap.put(6,"");
        this.chartDataSizeMap.put(6, 0);
        if(null == manageZone){
            return;
        }
        String zoneGb = manageZone.getZoneGb();
        this.executeDrugAndCaData(zoneGb, 6);
    }

    /**
     * <p>方法描述： 高毒、致癌通用处理 </p>
     * @MethodAuthor： pw 2023/6/28
     **/
    private void executeDrugAndCaData(String zoneGb, Integer curChartType){
        //截取后的地区编码
        String subZoneGb = ZoneUtil.zoneSelect(zoneGb);
        Integer subZoneSize = subZoneGb.length();
        //非 省市区
        if(subZoneSize != 2 && subZoneSize != 4 && subZoneSize != 6){
            return;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT M.ZONEGB,M.CURNUM FROM ( SELECT");
        sqlBuffer.append(subZoneSize == 2 ? " SUBSTR(T1.ZONE_GB, 0, 4) || '000000'  " : " SUBSTR(T1.ZONE_GB, 0,6)||'0000' ");
        sqlBuffer.append(" AS ZONEGB, COUNT(T.RID) AS CURNUM ")
                .append(" FROM TB_TJ_CRPT T ")
                .append(" LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID ")
                .append(" LEFT JOIN TB_TJ_CRPT_BADRSN T2 ON T2.CRPT_ID = T.RID ")
                .append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T2.BADRSN_ID = T3.RID ")
                .append(" WHERE T.DEL_MARK = 0 ")
                .append(" AND NVL(T.OPERATION_STATUS, 1) = 1 ")
                .append(" AND T3.EXTENDS7 like :ext7 ")
                .append(" AND T1.ZONE_GB LIKE :zoneGb ");
        sqlBuffer.append(" GROUP BY ")
                .append(subZoneSize == 2 ? " SUBSTR(T1.ZONE_GB, 0, 4) || '000000' " : " SUBSTR(T1.ZONE_GB, 0,6)||'0000' ")
                .append("  ) M ")
                .append("  ORDER BY M.CURNUM DESC, M.ZONEGB ");

        Map<String,Object> paramsMap = new HashMap<>();
        paramsMap.put("zoneGb", subZoneGb + "%");
        paramsMap.put("ext7", "%"+(5 == curChartType ? "1" : "2") + "%");
        List<Object[]> queryResult = this.commService.findDataBySqlNoPage(sqlBuffer.toString(), paramsMap);
        if(CollectionUtils.isEmpty(queryResult)){
            return;
        }
        List<Object[]> fillList = new ArrayList<>();
        Map<String,Object> tmpMap = new HashMap<>();
        for(Object[] objArr : queryResult){
            String curGb = null == objArr[0] ? null : objArr[0].toString();
            if(StringUtils.isBlank(curGb)){
                continue;
            }
            //省、市 对应本省或者本市 丢弃
            boolean flag = (subZoneSize == 2 || subZoneSize == 4) && curGb.equals(zoneGb);
            if(flag){
                continue;
            }
            TsZone tsZone = this.chartZoneMap.get(curGb);
            //可能出现停用的地区
            if(null == tsZone){
                tmpMap.put("zoneGb",curGb);
                List<TsZone> tmpList = this.commService.findDataByHqlNoPage(" select t from TsZone t where t.zoneGb=:zoneGb order by t.ifReveal desc ",tmpMap);
                if(CollectionUtils.isEmpty(tmpList)){
                    continue;
                }
                tsZone = tmpList.get(0);
                this.chartZoneMap.put(curGb, tsZone);
            }
            Object[] fillArr = new Object[2];
            fillArr[0] = null == tsZone.getZoneShortName() ? "" : tsZone.getZoneShortName();
            fillArr[1] = objArr[1];
            fillList.add(fillArr);
        }
        this.generateLineChart(fillList,  "地区", curChartType);
    }

    /**
     * <p>方法描述： 通过类型编码与码表编码获取码表对象 </p>
     * @MethodAuthor： pw 2023/6/28
     **/
    private TsSimpleCode findChartSimpleCode(String codeTypeName, String codeNo){
        if(StringUtils.isBlank(codeTypeName) || StringUtils.isBlank(codeNo)){
            return null;
        }
        Map<String,TsSimpleCode> fillMap = this.chartSimpleCodeMap.get(codeTypeName);
        TsSimpleCode simpleCode = null == fillMap ? null : fillMap.get(codeNo);
        if(null == simpleCode){
            Map<String,Object> tmpParamMap = new HashMap<>();
            tmpParamMap.put("codeTypeName", codeTypeName);
            tmpParamMap.put("codeNo", codeNo);
            List<TsSimpleCode> tmpList = this.commService.findDataByHqlNoPage(" select t from TsSimpleCode t where t.tsCodeType.codeTypeName=:codeTypeName and t.codeNo=:codeNo order by t.ifReveal desc ",tmpParamMap);
            if(CollectionUtils.isEmpty(tmpList)){
                return null;
            }
            simpleCode = tmpList.get(0);
            if(null == fillMap){
                fillMap = new HashMap<>();
            }
            fillMap.put(codeNo, simpleCode);
            this.chartSimpleCodeMap.put(codeTypeName, fillMap);
        }
        return simpleCode;
    }

    /**
     * <p>方法描述：生成柱状图 </p>
     * @MethodAuthor： pw 2023/6/27
     **/
    private void generateLineChart(List<Object[]> dataList, String xName, Integer curChartType){
        GsonOption option = new GsonOption();
        Grid grid = new Grid();
        grid.setShow(false);
        grid.setHeight("150");
        //设置距离上边与左边的距离
        grid.setY(30);
        //x轴距左边距离
        grid.setX(60);
        //x轴距右边距离
        grid.setX2(80);
        option.setGrid(grid);
        //标题不显示
        option.title().setShow(false);

        //标注
        option.legend().data();
        option.legend().y(35);
        option.legend().x(X.center);

        //移动到柱状图 显示对应数量
        option.tooltip().trigger(Trigger.item);
        option.tooltip().axisPointer().type(PointerType.none);

        //定义工具
        option.toolbox().show(false);
        option.calculable(true);

        //纵坐标
        ValueAxis v = new ValueAxis();
        v.show(true);
        v.splitLine().show(false);
        v.splitArea().show(false);
        v.axisTick().show(false);
        v.setName("单位数量");
        v.min(0);

        //横坐标
        CategoryAxis xAxis = new CategoryAxis();
        xAxis.setType(AxisType.category);
        xAxis.setShow(true);
        xAxis.axisLabel().formatter("function(value){return (value.length >5 ? (value.slice(0,4)+'...') : value )}");
        //interval(0) 避免缩小分辨率导致x轴文本不显示
        //xAxis.axisLabel().interval(0).formatter("function(value){ if(null == value || undefined == value || value.length <=5){ return value } var len = (value.length%10 > 0 ? 1 : 0) + parseInt(value.length); var result = ''; for(var i=0;i<len;i++){ result = result.concat(value.slice(i*5,(i+1)*5)); if(i != len-1){ result = result.concat('\n') }} return result}");
        //去除栏栅
        SplitLine splitLine = new SplitLine();
        splitLine.setShow(false);
        xAxis.setSplitLine(splitLine);
        xAxis.setName(xName);
        option.xAxis(xAxis);

        List<Object> data1 = new ArrayList<>();

        int max = 0;
        if(!CollectionUtils.isEmpty(dataList)){
            List<Object> xAxisDataList = new ArrayList<>();
            for (Object[] obj : dataList) {
                xAxisDataList.add(null == obj[0] ? "" : obj[0].toString());
                data1.add(null ==obj[1]?"":obj[1].toString());
                Integer num = null == obj[1] ? null : Integer.parseInt(obj[1].toString());
                if(null != num && num > max){
                    max = num;
                }
            }
            xAxis.data(xAxisDataList.toArray());
        }
        //设置y轴数值固定
        //v.max(max+10 - max%10);
        v.max(createMaxVal(max));
        option.yAxis(v);

        Bar hwBar1 = new Bar();
        hwBar1.stack("单位数量");
        hwBar1.setData(data1);
        hwBar1.setBarWidth(30);
        hwBar1.itemStyle().normal().label().show(true).position(Position.top).formatter("{c}");
        option.series(hwBar1);
        String result = option.toString();
        if(StringUtils.isNotBlank(result)){
            this.chartJsonMap.put(curChartType,result);
            this.chartDataSizeMap.put(curChartType, CollectionUtils.isEmpty(dataList) ? 0 : dataList.size());
        }
    }

    /**
     * <p>方法描述： 避免最大2650 y轴最大2500 </p>
     * @MethodAuthor： pw 2023/7/1
     **/
    private Integer createMaxVal(int max){
        int size = String.valueOf(max).length()-1;
        int base = 1;
        for(int i=0;i< size;i++){
            base = base*10;
        }
        if(max%base != 0){
            return max - (max%base) +base;
        }else {
            return max;
        }
    }


    /**
     * <p>方法描述： 生成饼图 </p>
     * @MethodAuthor： pw 2023/6/28
     **/
    private void generatePieChart(List<Object[]> dataList, Integer curChartType){
        GsonOption option = new GsonOption();
        //标题不显示
        option.title().setShow(false);

        //异步触发
        option.tooltip().trigger(Trigger.item);
        option.tooltip().formatter("{b}<br/>数量：{c} <br/>占比：{d}%");
        //工具栏
        option.toolbox().show(false);
        //option.legend()
        //数据
        Pie pie2 = new Pie();
        pie2.itemStyle().normal().label().show(true);
        //pie2.itemStyle().normal().label().formatter("{b} : {d}%");
        pie2.itemStyle().normal().label().formatter("{b} : {c} ({d}%)");
        pie2.itemStyle().normal().label().position(Position.outer);
        //pie2.itemStyle().normal().label().setInterval(0);
        pie2.itemStyle().normal().labelLine().show(true);
        pie2.itemStyle().normal().labelLine().setLength(2);
        //设置饼图大小 设置一个百分比 实心饼图 比如80% 设置数组那么前后差形成空心饼图 注意 后边的一定要比前边的大 因为标线是从后边的边框开始的
        //pie2.radius(new String[]{"60%","30%"});
        pie2.radius(new String[]{"30%","60%"});
        //pie2.radius("80%");
        //前 距离左边距离 后边调整上下距离
        pie2.center(new String[]{"50%","45%"});
        int dataSize = 0;
        List<String> legendDataList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(dataList)){
            for (Object[] object : dataList) {
                Data data = new Data();
                String tip = object[0].toString();
                data.name(tip);
                data.value(object[1].toString());
                legendDataList.add(tip);
                pie2.data().add(data);
            }
            dataSize = dataList.size();
        }
        option.legend().orient(Orient.vertical).x("30%").y("13%").data(legendDataList);
        option.series(pie2);
        String result = option.toString();
        if(StringUtils.isNotBlank(result)){
            this.chartJsonMap.put(curChartType,result);
            this.chartDataSizeMap.put(curChartType, dataSize);
        }
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchInstitutionCode() {
        return searchInstitutionCode;
    }

    public void setSearchInstitutionCode(String searchInstitutionCode) {
        this.searchInstitutionCode = searchInstitutionCode;
    }

    public String getSelectEconomyIds() {
        return selectEconomyIds;
    }

    public void setSelectEconomyIds(String selectEconomyIds) {
        this.selectEconomyIds = selectEconomyIds;
    }

    public String getSelectEconomyNames() {
        return selectEconomyNames;
    }

    public void setSelectEconomyNames(String selectEconomyNames) {
        this.selectEconomyNames = selectEconomyNames;
    }

    public List<TsSimpleCode> getEconomyList() {
        return economyList;
    }

    public void setEconomyList(List<TsSimpleCode> economyList) {
        this.economyList = economyList;
    }

    public String getSelectCrptSizeIds() {
        return selectCrptSizeIds;
    }

    public void setSelectCrptSizeIds(String selectCrptSizeIds) {
        this.selectCrptSizeIds = selectCrptSizeIds;
    }

    public List<TsSimpleCode> getCrptSizeList() {
        return crptSizeList;
    }

    public void setCrptSizeList(List<TsSimpleCode> crptSizeList) {
        this.crptSizeList = crptSizeList;
    }

    public String getSimpleCodeOpType() {
        return simpleCodeOpType;
    }

    public void setSimpleCodeOpType(String simpleCodeOpType) {
        this.simpleCodeOpType = simpleCodeOpType;
    }

    public String getSelectIndusTypeIds() {
        return selectIndusTypeIds;
    }

    public void setSelectIndusTypeIds(String selectIndusTypeIds) {
        this.selectIndusTypeIds = selectIndusTypeIds;
    }

    public String getSelectIndusTypeNames() {
        return selectIndusTypeNames;
    }

    public void setSelectIndusTypeNames(String selectIndusTypeNames) {
        this.selectIndusTypeNames = selectIndusTypeNames;
    }

    public List<TsSimpleCode> getIndusList() {
        return indusList;
    }

    public void setIndusList(List<TsSimpleCode> indusList) {
        this.indusList = indusList;
    }

    public String getSelectBadRsnIds() {
        return selectBadRsnIds;
    }

    public void setSelectBadRsnIds(String selectBadRsnIds) {
        this.selectBadRsnIds = selectBadRsnIds;
    }

    public String getSelectBadRsnNames() {
        return selectBadRsnNames;
    }

    public void setSelectBadRsnNames(String selectBadRsnNames) {
        this.selectBadRsnNames = selectBadRsnNames;
    }

    public List<TsSimpleCode> getBadRsnList() {
        return badRsnList;
    }

    public void setBadRsnList(List<TsSimpleCode> badRsnList) {
        this.badRsnList = badRsnList;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Object[] getCrptBaseInfo() {
        return crptBaseInfo;
    }

    public void setCrptBaseInfo(Object[] crptBaseInfo) {
        this.crptBaseInfo = crptBaseInfo;
    }

    public List<Object[]> getOccRiskClassList() {
        return occRiskClassList;
    }

    public void setOccRiskClassList(List<Object[]> occRiskClassList) {
        this.occRiskClassList = occRiskClassList;
    }

    public Integer getShowArchives() {
        return showArchives;
    }

    public void setShowArchives(Integer showArchives) {
        this.showArchives = showArchives;
    }

    public Boolean getHasDeclareDataForTheYear() {
        return hasDeclareDataForTheYear;
    }

    public void setHasDeclareDataForTheYear(Boolean hasDeclareDataForTheYear) {
        this.hasDeclareDataForTheYear = hasDeclareDataForTheYear;
    }

    public Date getDeclareData() {
        return declareData;
    }

    public void setDeclareData(Date declareData) {
        this.declareData = declareData;
    }


    public List<TdZyUnitbasicinfo> getUnitbasicinfoSelList() {
        return unitbasicinfoSelList;
    }

    public void setUnitbasicinfoSelList(List<TdZyUnitbasicinfo> unitbasicinfoSelList) {
        this.unitbasicinfoSelList = unitbasicinfoSelList;
    }

    public Integer getUnitbasicinfoRid() {
        return unitbasicinfoRid;
    }

    public void setUnitbasicinfoRid(Integer unitbasicinfoRid) {
        this.unitbasicinfoRid = unitbasicinfoRid;
    }

    public TdZyUnitbasicinfo getUnitbasicinfo() {
        return unitbasicinfo;
    }

    public void setUnitbasicinfo(TdZyUnitbasicinfo unitbasicinfo) {
        this.unitbasicinfo = unitbasicinfo;
    }

    public Integer getPlaceDate() {
        return placeDate;
    }

    public void setPlaceDate(Integer placeDate) {
        this.placeDate = placeDate;
    }

    public Boolean getPlaceThatYear() {
        return placeThatYear;
    }

    public void setPlaceThatYear(Boolean placeThatYear) {
        this.placeThatYear = placeThatYear;
    }

    public UnitbasicInfo getUnitbasic() {
        return unitbasic;
    }

    public void setUnitbasic(UnitbasicInfo unitbasic) {
        this.unitbasic = unitbasic;
    }

    public List<PlaceInfo> getUnitharmChkInfos() {
        return unitharmChkInfos;
    }

    public void setUnitharmChkInfos(List<PlaceInfo> unitharmChkInfos) {
        this.unitharmChkInfos = unitharmChkInfos;
    }

    public List<PlaceInfo> getUnitHethCusInfos() {
        return unitHethCusInfos;
    }

    public void setUnitHethCusInfos(List<PlaceInfo> unitHethCusInfos) {
        this.unitHethCusInfos = unitHethCusInfos;
    }

    public List<PlaceInfo> getResultProInfos() {
        return resultProInfos;
    }

    public void setResultProInfos(List<PlaceInfo> resultProInfos) {
        this.resultProInfos = resultProInfos;
    }

    public List<Integer> getPlaceDateList() {
        return placeDateList;
    }

    public void setPlaceDateList(List<Integer> placeDateList) {
        this.placeDateList = placeDateList;
    }

    public Integer getChartType() {
        return chartType;
    }

    public void setChartType(Integer chartType) {
        this.chartType = chartType;
    }

    public String getChartJson() {
        return chartJson;
    }

    public void setChartJson(String chartJson) {
        this.chartJson = chartJson;
    }

    public Map<Integer, String> getChartJsonMap() {
        return chartJsonMap;
    }

    public void setChartJsonMap(Map<Integer, String> chartJsonMap) {
        this.chartJsonMap = chartJsonMap;
    }

    public Integer getChartDataSize() {
        return chartDataSize;
    }

    public void setChartDataSize(Integer chartDataSize) {
        this.chartDataSize = chartDataSize;
    }

    public Map<Integer, Integer> getChartDataSizeMap() {
        return chartDataSizeMap;
    }

    public void setChartDataSizeMap(Map<Integer, Integer> chartDataSizeMap) {
        this.chartDataSizeMap = chartDataSizeMap;
    }
}
