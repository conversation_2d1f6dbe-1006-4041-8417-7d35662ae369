package com.chis.modules.heth.comm.plugin;

import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsMenuBtn;

/**
 * 菜单按钮的插件
 * <AUTHOR>
 */
public class HethMenuBtnPluginObj {

	public static Set<TsMenuBtn> menuSet;
	
	static {
		menuSet = new HashSet<TsMenuBtn>();
        // 个案查询--个案内部导出
//        menuSet.add(new TsMenuBtn("heth_comm_gacx","heth_comm_gacx_nbexport","内部导出"));
		//职业卫生技术服务信息报送卡查询-导入
		//menuSet.add(new TsMenuBtn("heth_comm_gs_zywsjsfwxxbskcx","heth_comm_gs_zywsjsfwxxbskcx_imp","导入"));
		//放射卫生技术服务信息报送卡查询-导入
		//menuSet.add(new TsMenuBtn("heth_comm_gs_fswsjsfwxxbscx","heth_comm_gs_fswsjsfwxxbscx_imp","导入"));
		//用人单位综合展示-修订
		//menuSet.add(new TsMenuBtn("heth_comm_crpt_gs_integrated_show", "heth_comm_crpt_gs_integrated_show_fix", "修订"));
		//职业健康检查数据审核-内部导出
		//menuSet.add(new TsMenuBtn("heth_zyjkjcsjsh", "heth_zyjkjcsjsh_innerexp", "内部导出"));
		//menuSet.add(new TsMenuBtn("heth_zyjkjcsjsh", "heth_zyjkjcsjsh_import", "批量退回数据导入"));

		//国家体检数据导入--导入--山东
		//menuSet.add(new TsMenuBtn("heth_comm_sdgjtjsjdr","heth_comm_sdgjtjsjdr_imp","导入"));
	}
	
}