package com.chis.modules.heth.comm.rptvo;

import com.chis.modules.system.rptvo.ZwxFieldDoc;

/***
 *  <p>类描述：接害情况[职业性有害因素监测卡]</p>
 *
 * @ClassAuthor maox,2019年9月5日,HethChkSmaryVo
 * <AUTHOR>
 *
 */
public class HethChkSmaryCommPsnSubVo {

    @ZwxFieldDoc(value = "当年接触有害因素名称")
    private String tchRsnName;

    @ZwxFieldDoc(value = "当年接触有害因素人次")
    private String touchNum;

    @ZwxFieldDoc(value = "应检人数岗前")
    private String needChkNumBefore;

    @ZwxFieldDoc(value = "应检人数在岗")
    private String needChkNumDuring;

    @ZwxFieldDoc(value = "应检人数离岗时")
    private String needChkNumAfter;


    public String getTchRsnName() {
        return tchRsnName;
    }

    public void setTchRsnName(String tchRsnName) {
        this.tchRsnName = tchRsnName;
    }

    public String getTouchNum() {
        return touchNum;
    }

    public void setTouchNum(String touchNum) {
        this.touchNum = touchNum;
    }

    public String getNeedChkNumBefore() {
        return needChkNumBefore;
    }

    public void setNeedChkNumBefore(String needChkNumBefore) {
        this.needChkNumBefore = needChkNumBefore;
    }

    public String getNeedChkNumDuring() {
        return needChkNumDuring;
    }

    public void setNeedChkNumDuring(String needChkNumDuring) {
        this.needChkNumDuring = needChkNumDuring;
    }

    public String getNeedChkNumAfter() {
        return needChkNumAfter;
    }

    public void setNeedChkNumAfter(String needChkNumAfter) {
        this.needChkNumAfter = needChkNumAfter;
    }
}
