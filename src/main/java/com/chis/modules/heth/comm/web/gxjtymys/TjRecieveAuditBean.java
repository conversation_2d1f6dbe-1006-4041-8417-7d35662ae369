package com.chis.modules.heth.comm.web.gxjtymys;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ManagedBean(name = "tjRecieveAuditBean")
@ViewScoped
public class TjRecieveAuditBean {
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private String perPageSize = "20,50,100";
    /******************* 职健康体检受理审核参数 ↓ **********************/
    private List<Object[]> auditList;
    private List<TsZone> auditZoneList;
    private String searchAuditZoneCode;
    private String searchAuditZoneName;
    private Date auditStartDate;
    private Date auditEndDate;
    /******************* 职健康体检受理审核参数 ↑ **********************/
    public TjRecieveAuditBean(){
        this.initAuditData();
    }

    /**
     * <p>方法描述： 职健康体检受理审核 数据初始化 </p>
     * @MethodAuthor： pw 2023/4/18
     **/
    private void initAuditData(){
        this.auditStartDate = DateUtils.getYearFirstDay(new Date());
        this.auditEndDate = new Date();

        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        /*** 地区初始化 */
        this.auditZoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneCode(), true,null,null);
        if(!CollectionUtils.isEmpty(this.auditZoneList)){
            this.searchAuditZoneCode = this.auditZoneList.get(0).getZoneCode();
            this.searchAuditZoneName = this.auditZoneList.get(0).getZoneName();
        }

        this.auditList = new ArrayList<>();
        Object[] objArr = new Object[5];
        objArr[0] = "西安市_新城区";
        objArr[1] = "测试单位";
        objArr[2] = "测试人员";
        objArr[3] = "2023-1-5";
        objArr[4] = "待审核";
        this.auditList.add(objArr);
    }

    public List<Object[]> getAuditList() {
        return auditList;
    }

    public void setAuditList(List<Object[]> auditList) {
        this.auditList = auditList;
    }

    public List<TsZone> getAuditZoneList() {
        return auditZoneList;
    }

    public void setAuditZoneList(List<TsZone> auditZoneList) {
        this.auditZoneList = auditZoneList;
    }

    public String getSearchAuditZoneCode() {
        return searchAuditZoneCode;
    }

    public void setSearchAuditZoneCode(String searchAuditZoneCode) {
        this.searchAuditZoneCode = searchAuditZoneCode;
    }

    public String getSearchAuditZoneName() {
        return searchAuditZoneName;
    }

    public void setSearchAuditZoneName(String searchAuditZoneName) {
        this.searchAuditZoneName = searchAuditZoneName;
    }

    public Date getAuditStartDate() {
        return auditStartDate;
    }

    public void setAuditStartDate(Date auditStartDate) {
        this.auditStartDate = auditStartDate;
    }

    public Date getAuditEndDate() {
        return auditEndDate;
    }

    public void setAuditEndDate(Date auditEndDate) {
        this.auditEndDate = auditEndDate;
    }

    public String getPerPageSize() {
        return perPageSize;
    }

    public void setPerPageSize(String perPageSize) {
        this.perPageSize = perPageSize;
    }
}
