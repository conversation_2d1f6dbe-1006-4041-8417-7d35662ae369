package com.chis.modules.heth.comm.json;

import java.io.Serializable;

/**
 * <p>描述 单位弹窗传参Json对象</p>
 * <p>ifSearchZoneDefault 默认为选中 即默认选中登录人所在单位的省级</p>
 * <p>ifShowOtherZone 默认为否 即默认不展示其他地区</p>
 * <p>ifEdit 默认有 即默认有编辑功能</p>
 * <p>source </p>
 * <p>busType </p>
 * @ClassAuthor gongzhe,2022/7/21 14:21,CrptDialogParamJson
 */
public class CrptDialogParamJson implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 是否需要查询列表地区默认选中登录人所在单位的省级，默认选中*/
    private Boolean ifSearchZoneDefault = Boolean.TRUE;
    /** 是否展示其他地区（全国） 默认否不展示 */
    private Boolean ifShowOtherZone = Boolean.FALSE;
    /** 弹窗来源 用于控制页面展示及特殊字段处理
     *  1 展示职工人数+接害人数且新标记为1，如：体检录入
     *  2 展示职工人数+生产工人数，如：职业性有害因素监测卡填报
     *  3 展示在册职工总数+外委人员数 如：职业性有害因素检测流程
     * */
    private String source;
    /** 业务类型 只用于查/存储企业关系表
     *1：职业健康检查
     *2：职业病诊断
     *3：职业病鉴定
     *4：职业病危害因素检测
     *5：技术服务申报
     *6：在线申报
     *7：工作场所监测
     *8：现状调查
     *9：放射卫生服务机构检测评价填报
     *10：职业卫生服务机构检测评价填报
     * */
    private String busType;
    /** 是否需要添加功能+修改功能 默认有*/
    private Boolean ifEdit = Boolean.TRUE;
    /** 查询条件-单位名称*/
    private String searchCrptName;
    /**
     * 用人单位编辑时，单位地址、行业类别、企业规模、经济类型、联系人、联系电话6个信息点是否不必填 <pre>默认否
     */
    private Boolean ifGs = Boolean.FALSE;
    /**
     * 判断验证地区必须为当前职卫平台所属省份地区 null不验证 默认null
     * 1 当选择的用人单位行业类别不为人力资源服务时，地区必须为当前职卫平台所属省份地区
     * 2 选择的用工单位地区必须为当前职卫平台所属省份地区
     * */
    private Integer areaValidate = null;
    /**
     * 是否显示健康企业建设情况 默认false 不显示
     * 当前20230512 体检录入用人单位弹框框需要显示
     * */
    private Boolean ifShowHealthCrpt = Boolean.FALSE;

    /**单位弹出框中排除掉的记录rid  多个用英文逗号拼接*/
    private String excludeRids;

    /**
     * 是否主动监测任务选择用人单位, 默认否
     */
    private Boolean ifActiveMonitoringTask = Boolean.FALSE;
    /**
     * 当前操作主动监测任务RID
     */
    private Integer activeMonitoringTaskRid;
    /**
     * 是否可以选择经营状态不为正常的数据，默认否；
     * <p>是: </p>
     * <li>可以选择虚拟社会信用代码的数据</li>
     * <li>添加/修改界面显示经营状态</li>
     */
    private Boolean ifSelectOperationStopData = Boolean.FALSE;

    public Boolean getIfSearchZoneDefault() {
        return ifSearchZoneDefault;
    }

    public void setIfSearchZoneDefault(Boolean ifSearchZoneDefault) {
        this.ifSearchZoneDefault = ifSearchZoneDefault;
    }

    public Boolean getIfShowOtherZone() {
        return ifShowOtherZone;
    }

    public void setIfShowOtherZone(Boolean ifShowOtherZone) {
        this.ifShowOtherZone = ifShowOtherZone;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public Boolean getIfEdit() {
        return ifEdit;
    }

    public void setIfEdit(Boolean ifEdit) {
        this.ifEdit = ifEdit;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public Boolean getIfGs() {
        return ifGs;
    }

    public void setIfGs(Boolean ifGs) {
        this.ifGs = ifGs;
    }

    public Integer getAreaValidate() {
        return areaValidate;
    }

    public void setAreaValidate(Integer areaValidate) {
        this.areaValidate = areaValidate;
    }

    public Boolean getIfShowHealthCrpt() {
        return ifShowHealthCrpt;
    }

    public void setIfShowHealthCrpt(Boolean ifShowHealthCrpt) {
        this.ifShowHealthCrpt = ifShowHealthCrpt;
    }

    public String getExcludeRids() {
        return excludeRids;
    }

    public void setExcludeRids(String excludeRids) {
        this.excludeRids = excludeRids;
    }

    public Boolean getIfActiveMonitoringTask() {
        return ifActiveMonitoringTask;
    }

    public void setIfActiveMonitoringTask(Boolean ifActiveMonitoringTask) {
        this.ifActiveMonitoringTask = ifActiveMonitoringTask;
    }

    public Integer getActiveMonitoringTaskRid() {
        return activeMonitoringTaskRid;
    }

    public void setActiveMonitoringTaskRid(Integer activeMonitoringTaskRid) {
        this.activeMonitoringTaskRid = activeMonitoringTaskRid;
    }

    public Boolean getIfSelectOperationStopData() {
        return ifSelectOperationStopData;
    }

    public void setIfSelectOperationStopData(Boolean ifSelectOperationStopData) {
        this.ifSelectOperationStopData = ifSelectOperationStopData;
    }
}
