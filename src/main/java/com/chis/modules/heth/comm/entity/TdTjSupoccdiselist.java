package com.chis.modules.heth.comm.entity;

import java.math.BigInteger;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 疑似职业病人群
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置<br/>
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 */
@Entity
@Table(name = "TD_TJ_SUPOCCDISELIST", uniqueConstraints = @UniqueConstraint(columnNames = {
        "BHK_ID", "OCC_DISEID" }))
@SequenceGenerator(name = "TdTjSupoccdiselist_Seq", sequenceName = "TD_TJ_SUPOCCDISELIST_SEQ", allocationSize = 1)
public class TdTjSupoccdiselist implements java.io.Serializable {

    private static final long serialVersionUID = 5934239360045234399L;
    private BigInteger rid;
    private TdTjBhk tdTjBhk;
    private TsSimpleCode tsSimpleCodeByBadrsnId;
    private TsSimpleCode tsSimpleCodeByOccDiseid;
    private Date createDate;
    private Integer createManid;

    /**用人单位名称*/
    private String crptName;

    public TdTjSupoccdiselist() {
    }

    public TdTjSupoccdiselist(BigInteger rid, TdTjBhk tdTjBhk,
                              TsSimpleCode tsSimpleCodeByBadrsnId,
                              TsSimpleCode tsSimpleCodeByOccDiseid, Date createDate,
                              Integer createManid) {
        this.rid = rid;
        this.tdTjBhk = tdTjBhk;
        this.tsSimpleCodeByBadrsnId = tsSimpleCodeByBadrsnId;
        this.tsSimpleCodeByOccDiseid = tsSimpleCodeByOccDiseid;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjSupoccdiselist_Seq")
    public BigInteger getRid() {
        return this.rid;
    }

    public void setRid(BigInteger rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "BHK_ID" )
    public TdTjBhk getTdTjBhk() {
        return this.tdTjBhk;
    }

    public void setTdTjBhk(TdTjBhk tdTjBhk) {
        this.tdTjBhk = tdTjBhk;
    }

    @ManyToOne
    @JoinColumn(name = "BADRSN_ID" )
    public TsSimpleCode getTsSimpleCodeByBadrsnId() {
        return this.tsSimpleCodeByBadrsnId;
    }

    public void setTsSimpleCodeByBadrsnId(TsSimpleCode tsSimpleCodeByBadrsnId) {
        this.tsSimpleCodeByBadrsnId = tsSimpleCodeByBadrsnId;
    }

    @ManyToOne
    @JoinColumn(name = "OCC_DISEID" )
    public TsSimpleCode getTsSimpleCodeByOccDiseid() {
        return this.tsSimpleCodeByOccDiseid;
    }

    public void setTsSimpleCodeByOccDiseid(TsSimpleCode tsSimpleCodeByOccDiseid) {
        this.tsSimpleCodeByOccDiseid = tsSimpleCodeByOccDiseid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Column(name = "CRPT_NAME" )
    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }
}
