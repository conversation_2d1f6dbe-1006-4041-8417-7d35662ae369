package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdZwBgkFlow;
import com.chis.modules.heth.comm.entity.TdZwBgkLastSta;
import com.chis.modules.heth.comm.entity.TdZwGbz188NostdComm;
import com.chis.modules.heth.comm.entity.TdZwOccdiscaseComm;
import com.chis.modules.heth.comm.service.ZwReportCardCheckCommServiceImpl;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description : 报告卡查询基类
 * @ClassAuthor : rcj
 * @Date : 2021/1/14 13:41
 * 
 * 
 * <p>修订内容：GBZ188不规范数据查询基类不再使用</p>
 * @ClassReviser qrr,2022年6月27日,AbstractReportCardSearchCommBean
 **/
@Deprecated
public abstract class AbstractReportCardSearchCommBean extends FacesEditBean {

    /**业务主键*/
    protected Integer rid;
    protected TdZwBgkLastSta newFlow = new TdZwBgkLastSta();
    /**退回原因*/
    private String backRsn;
    /**退回原因只读*/
    private Boolean readOnly = false;
    /**2:省级3：市级4：区县级*/
    protected Integer zoneType;
    /**是否审核界面*/
    protected Boolean ifCheck = false;
    /**状态*/
    protected String[] states;
    protected List<SelectItem> stateList = new ArrayList<>();

    /**选择的结果集*/
    protected List<Object[]> selectEntitys;

    protected SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    protected ZwReportCardCheckCommServiceImpl cardCheckServiceImpl = SpringContextHolder.getBean(ZwReportCardCheckCommServiceImpl.class);


    public AbstractReportCardSearchCommBean() {
        TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
        if(null == tsZone) {
            tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
        }
        this.zoneType = tsZone.getZoneType().intValue();

        // 状态初始化
        initSearchStates();
    }

    /**
    * @Description : 初始化状态
    * @MethodAuthor: rcj
    * @Date : 2021/1/14 13:49
    **/
    private void initSearchStates() {
        // 默认：终审完成
        states = new String[]{"7"};
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem("0.5", "待填报"));
        this.stateList.add(new SelectItem("0", "待提交"));
        this.stateList.add(new SelectItem("2,4,6", "已退回"));
        this.stateList.add(new SelectItem("1", "待初审"));
        this.stateList.add(new SelectItem("3", "待复审"));
        this.stateList.add(new SelectItem("5", "待终审"));
        this.stateList.add(new SelectItem("7", "终审完成"));
    }

    /**
    * @Description : 初始化退回原因
    * @MethodAuthor: rcj
    * @Date : 2021/1/14 14:16
    **/
    protected void initBackRsn() {
        this.newFlow = this.cardCheckServiceImpl.searchNewFlow(rid, getCardType());
        if(null == this.newFlow) {
            this.newFlow = new TdZwBgkLastSta();
        }
        if(null == this.newFlow.getState()) {
            return;
        }

        //根据状态加载退回原因
        if(this.newFlow.getState() == 2){
            this.backRsn = this.newFlow.getCountyBackRsn();
        }else if(this.newFlow.getState() == 4){
            this.backRsn = this.newFlow.getCityBackRsn();
        }else if(this.newFlow.getState() == 6){
            this.backRsn = this.newFlow.getProBackRsn();
        }

    }

    /**
    * @Description : 打开退回弹出框
    * @MethodAuthor: rcj
    * @Date : 2021/1/14 15:23
    **/
    public void initDialog() {
        if(this.readOnly) {
            initBackRsn();
        } else {
            this.backRsn = null;
        }
    }

    /**
    * @Description : 退回
    * @MethodAuthor: rcj
    * @Date : 2021/1/14 15:37
    **/
    public void returnAction() {
        if(StringUtils.isBlank(this.backRsn)) {
            JsfUtil.addErrorMessage("退回原因不能为空！");
            return;
        }
        if(null == this.rid) {
            JsfUtil.addErrorMessage("业务主键不能为空！");
            return;
        }
        if(null == getCardType()) {
            JsfUtil.addErrorMessage("报告卡类型不能为空！");
            return;
        }

        // 根据报告卡类型和报告卡id查询出报告卡的填报机构地区
        if(5 == getCardType()) {
            // 5：GBZ188不规范审核 TD_ZW_GBZ188_NOSTD
            TdZwGbz188NostdComm rpt = this.cardCheckServiceImpl.find(TdZwGbz188NostdComm.class, this.rid);
            TsZone zone = rpt.getFkByMainId().getTbTjSrvorg().getTsUnit().getTsZone();
            packageFlow(zone);
        }
        JsfUtil.addSuccessMessage("退回成功！");
        // 发消息，接收单位所有用户？提交人？
        //sendMsg(users);
        this.searchAction();
        this.backAction();
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("PF('ReasonDialog').hide();");
        context.update("tabView");
    }

    /**
    * @Description : 封装退回原因
    * @MethodAuthor: rcj
    * @Date : 2021/1/14 15:39
    **/
    private void packageFlow(TsZone zone) {
        // 二级退回
        if("1".equals(zone.getIfCityDirect())){
            // 更新报告卡最新状态
            TdZwBgkLastSta newFlow = this.cardCheckServiceImpl.searchNewFlow(this.rid, getCardType());
            if(null == newFlow) {
                newFlow = new TdZwBgkLastSta();
            }
            newFlow.setBusId(this.rid);
            newFlow.setCartType(getCardType());
            if(this.zoneType == 2) {
                // 市级接收日期
                newFlow.setCityRcvDate(new Date());
                newFlow.setProBackRsn(this.backRsn);
                newFlow.setState(6);
            } else {
                // 填报接收日期
                newFlow.setOrgRcvDate(new Date());
                newFlow.setCityBackRsn(this.backRsn);
                newFlow.setState(4);
            }
            // 新增接收记录
            TdZwBgkFlow tdZwBgkFlow = new TdZwBgkFlow();
            tdZwBgkFlow.setBusId(this.rid);
            tdZwBgkFlow.setCartType(getCardType());

            if(this.zoneType == 2) {
                // 省级退回
                tdZwBgkFlow.setOperFlag(32);
            } else {
                // 市级退回
                tdZwBgkFlow.setOperFlag(13);
            }
            tdZwBgkFlow.setRcvDate(new Date());
            tdZwBgkFlow.setFkBySmtPsnId(this.sessionData.getUser());

            // 本次操作，更新操作记录
            Object[] info = this.cardCheckServiceImpl.selectMaxRevDateInfo(this.rid, getCardType());
            this.cardCheckServiceImpl.saveOrUpdateFlow(newFlow, tdZwBgkFlow, this.backRsn,null!=info && null!=info[0]?Integer.valueOf(info[0].toString()):null, null);
        } else {
            // 更新报告卡最新状态
            TdZwBgkLastSta newFlow = this.cardCheckServiceImpl.searchNewFlow(this.rid, getCardType());
            if(null == newFlow) {
                newFlow = new TdZwBgkLastSta();
            }
            newFlow.setBusId(this.rid);
            newFlow.setCartType(getCardType());
            if(this.zoneType == 2) {
                // 市级接收日期
                newFlow.setCityRcvDate(new Date());
                newFlow.setProBackRsn(this.backRsn);
                newFlow.setState(6);
            } else if (this.zoneType == 3) {
                // 区县接收日期
                newFlow.setCountyRcvDate(new Date());
                newFlow.setCityBackRsn(this.backRsn);
                newFlow.setState(4);
            }else {
                // 填报接收日期
                newFlow.setOrgRcvDate(new Date());
                newFlow.setCountyBackRsn(this.backRsn);
                newFlow.setState(2);
            }
            // 新增接收记录
            TdZwBgkFlow tdZwBgkFlow = new TdZwBgkFlow();
            tdZwBgkFlow.setBusId(this.rid);
            tdZwBgkFlow.setCartType(getCardType());

            if(this.zoneType == 2) {
                tdZwBgkFlow.setOperFlag(32);
            } else if (this.zoneType == 3) {
                // 市级退回
                tdZwBgkFlow.setOperFlag(22);
            } else {
                // 县区退回
                tdZwBgkFlow.setOperFlag(11);
            }
            tdZwBgkFlow.setRcvDate(new Date());
            tdZwBgkFlow.setFkBySmtPsnId(this.sessionData.getUser());

            // 本次操作，更新操作记录
            Object[] info = this.cardCheckServiceImpl.selectMaxRevDateInfo(rid, getCardType());
            this.cardCheckServiceImpl.saveOrUpdateFlow(newFlow, tdZwBgkFlow, this.backRsn,null!=info && null!=info[0]?Integer.valueOf(info[0].toString()):null, null);
        }
    }

    /**
    * @Description : 审核
    * @MethodAuthor: rcj
    * @Date : 2021/1/14 15:46
    **/
    public void reviewAction() {
        if(null == this.rid) {
            JsfUtil.addErrorMessage("业务主键不能为空！");
            return;
        }
        if(null == getCardType()) {
            JsfUtil.addErrorMessage("报告卡类型不能为空！");
            return;
        }
        // 审核
        this.saveOrUpdateTdZwBgkLastStaInfo(this.rid, this.newFlow);
        JsfUtil.addSuccessMessage("审核成功！");
        // 发消息，接收单位所有用户？提交人？
        //sendMsg(users);
        this.searchAction();
        this.backAction();
        RequestContext context = RequestContext.getCurrentInstance();
        context.update("tabView");
    }

    /**
     * @Description : 执行审核操作
     * @MethodAuthor: rcj
     * @Date : 2019/9/18 8:23
     **/
    private void saveOrUpdateTdZwBgkLastStaInfo(Integer rid, TdZwBgkLastSta newFlow) {
        //更新报告卡最新状态
        if (null==newFlow) {
            newFlow = new TdZwBgkLastSta();
        }
        newFlow.setBusId(rid);
        newFlow.setCartType(getCardType());
        TdZwOccdiscaseComm tdZwOccdiscase = null;
        if(this.zoneType.intValue() == 2) {
            if(null != tdZwOccdiscase) {
                tdZwOccdiscase.setStateMark(7);
            }
            // 省级提交日期
            newFlow.setProSmtDate(new Date());
            newFlow.setState(7);
        } else if (this.zoneType.intValue() == 3) {
            // 省级接收日期
            newFlow.setProRcvDate(new Date());
            newFlow.setState(5);
        } else {
            newFlow.setCountySmtDate(new Date());
            // 市级接收日期
            newFlow.setCityRcvDate(new Date());
            newFlow.setState(3);
        }

        //新增接收记录
        TdZwBgkFlow tdZwBgkFlow = new TdZwBgkFlow();
        tdZwBgkFlow.setBusId(rid);
        tdZwBgkFlow.setCartType(getCardType());

        tdZwBgkFlow.setRcvDate(new Date());
        tdZwBgkFlow.setFkBySmtPsnId(this.sessionData.getUser());

        // 本次操作，更新操作记录
        Integer operFlag = null;
        if(this.zoneType.intValue() == 2) {
            // 省级审核
            operFlag = 42;
        } else if (this.zoneType.intValue() == 3) {
            // 市级审核
            operFlag = 41;
        } else {
            // 县区审核
            operFlag = 31;
        }
        tdZwBgkFlow.setOperFlag(operFlag);
        Object[] info = this.cardCheckServiceImpl.selectMaxRevDateInfo(rid, getCardType());
        this.cardCheckServiceImpl.saveOrUpdateFlow(newFlow, tdZwBgkFlow, null,null!=info && null!=info[0]?Integer.valueOf(info[0].toString()):null, tdZwOccdiscase);
    }

    /**
    * @Description : 打开批量审核确认框,判断是否存在未填写规范性审核的数据
    * @MethodAuthor: rcj
    * @Date : 2021/1/14 18:26
    **/
    public void openReviewConfirmDialog() {
        if(this.selectEntitys == null || this.selectEntitys.size()==0){
            JsfUtil.addErrorMessage("请选择需要审批的数据！");
            return ;
        }
        RequestContext.getCurrentInstance().execute("PF('ReviewConfirmDialog').show()");
    }

    /**
    * @Description : 只有GBZ188不规范报告卡审核，且批量非省级审核时，初始化审核意见
    * @MethodAuthor: rcj
    * @Date : 2021/1/14 18:33
    **/
    private void initAuditAdvice(String ifCityDirect, TdZwBgkLastSta newFlow) {
        String defaultAuditAdv = PropertyUtils.getValue("defaultAuditAdv");
        if(this.zoneType == 2) {
            // 省级审核意见
            if(StringUtils.isBlank(newFlow.getProAuditAdv())) {
                newFlow.setProAuditAdv(defaultAuditAdv);
            }
            // 省级审核人
            if(StringUtils.isBlank(newFlow.getProChkPsn())) {
                newFlow.setProChkPsn(this.sessionData.getUser().getUsername());
            }
        } else if(zoneType == 4 && !"1".equals(ifCityDirect)) {
            // 区级审核意见
            if(StringUtils.isBlank(newFlow.getCountAuditAdv())) {
                newFlow.setCountAuditAdv(defaultAuditAdv);
            }
            // 区级审核人
            if(StringUtils.isBlank(newFlow.getCountyChkPsn())) {
                newFlow.setCountyChkPsn(this.sessionData.getUser().getUsername());
            }
        } else {
            // 市级审核意见
            if(StringUtils.isBlank(newFlow.getCityAuditAdv())) {
                newFlow.setCityAuditAdv(defaultAuditAdv);
            }
            // 市级审核人
            if(StringUtils.isBlank(newFlow.getCityChkPsn())) {
                newFlow.setCityChkPsn(this.sessionData.getUser().getUsername());
            }
        }
    }

    /**
    * @Description : 批量审核
    * @MethodAuthor: rcj
    * @Date : 2021/1/14 18:32
    **/
    public void reviewBatchAction() {
        if (null==getCardType()) {
            JsfUtil.addErrorMessage("报告卡类型不能为空！");
            return;
        }
        for(Object[] obj : this.selectEntitys) {
            if(null == obj || null == obj[0]) {
                continue;
            }
            TdZwBgkLastSta newFlow = cardCheckServiceImpl.searchNewFlow(Integer.parseInt(obj[0].toString()), getCardType());
            if (5==getCardType()) {
                initAuditAdvice(null!=obj[18]?obj[18].toString():"", newFlow);
            }
            this.saveOrUpdateTdZwBgkLastStaInfo(Integer.parseInt(obj[0].toString()),newFlow);

            if(getCardType() == 5){
                TdZwGbz188NostdComm nostd = cardCheckServiceImpl.find(TdZwGbz188NostdComm.class, Integer.parseInt(obj[0].toString()));
                //未填写规范性审核的报告卡则默认为1
                if(null == nostd.getChkStdFlag()){
                    nostd.setChkStdFlag(1);
                    cardCheckServiceImpl.upsertEntity(nostd);
                }
            }
        }
        JsfUtil.addSuccessMessage("审核成功！");
        this.searchAction();
    }

    /**
    * @Description : 报告卡类型
    * @MethodAuthor: rcj
    * @Date : 2021/1/14 14:19
    **/
    public abstract Integer getCardType();

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwBgkLastSta getNewFlow() {
        return newFlow;
    }

    public void setNewFlow(TdZwBgkLastSta newFlow) {
        this.newFlow = newFlow;
    }

    public String getBackRsn() {
        return backRsn;
    }

    public void setBackRsn(String backRsn) {
        this.backRsn = backRsn;
    }

    public Boolean getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(Boolean readOnly) {
        this.readOnly = readOnly;
    }

    public Integer getZoneType() {
        return zoneType;
    }

    public void setZoneType(Integer zoneType) {
        this.zoneType = zoneType;
    }

    public Boolean getIfCheck() {
        return ifCheck;
    }

    public void setIfCheck(Boolean ifCheck) {
        this.ifCheck = ifCheck;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public List<Object[]> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<Object[]> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }

}
