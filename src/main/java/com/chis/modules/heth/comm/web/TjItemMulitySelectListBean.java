package com.chis.modules.heth.comm.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbTjItems;
import com.chis.modules.heth.comm.service.HethStaQueryCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesBean;

/**
 * <p>类描述：体检项目弹出框</p>
 * @ClassAuthor qrr,2018年4月9日,tjItemMulitySelectListBean
 */
@ManagedBean(name = "tjItemMulitySelectListBean")
@ViewScoped
public class TjItemMulitySelectListBean extends FacesBean {

    private static final long serialVersionUID = -738445454536625063L;

    /** 名称 或 拼音码*/
    private String searchNamOrPy;
    /**项目分类*/
    private String firstCodeNo;
    private List<TsSimpleCode> firstList;
    /** 查询列集合*/
    private List<TbTjItems> displayList;
    /** 所有集合*/
    private List<TbTjItems> allList;
    private HethStaQueryCommServiceImpl hethStaQueryServiceImpl = SpringContextHolder
			.getBean(HethStaQueryCommServiceImpl.class);
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    /**
 	 * <p>方法描述：初始化数据</p>
 	 * @MethodAuthor qrr,2018年4月9日,CodeRadioSelectBean
     * */
    public TjItemMulitySelectListBean() {
        String selectIds = JsfUtil.getRequest().getParameter("selectIds");//已选择的码表rid，以逗号隔开
        /**key：项目id，value：0不合格*/
        Map<Integer, String> map = new HashMap<>();
        if (StringUtils.isNotBlank(selectIds)) {
			String[] split = selectIds.split(",");
			for (String s : split) {
				String[] split2 = s.split("@@");
				if (StringUtils.isBlank(split2[0])) {
					continue;
				}
				map.put(Integer.valueOf(split2[0]), split2.length>1?split2[1]:"");
			}
        }
        this.init(map);
    }
    /**
 	 * <p>方法描述：查询体检项目数据</p>
 	 * @MethodAuthor qrr,2018年4月9日,CodeRadioSelectBean
     * */
    private void init(Map<Integer, String> selectIdMap)   {
        this.displayList = new ArrayList<TbTjItems>();
        this.allList = new ArrayList<TbTjItems>();
        this.firstList = this.commService.findallSimpleCodesByTypeIdAndCodeLevelNo("5008");
        List<TbTjItems> list = hethStaQueryServiceImpl.findTjItemList(null, null, Short.valueOf("1"), null, null);
        int row = -1;// 第一次出现勾选的当前页的第一行数
        if (null!=list && list.size()>0) {
        	Map<Integer, TbTjItems> map = new HashMap<>();
        	int  i = 0;
        	for (TbTjItems t : list) {
        		TsSimpleCode tsSimpleCode = t.getTsSimpleCode();
        		if (null==map.get(tsSimpleCode.getRid())) {//项目分类
        			TbTjItems itm = new TbTjItems();
    				itm.setRid(-1);
    				itm.setItemName(tsSimpleCode.getCodeName());
    				itm.setLevelCode(tsSimpleCode.getCodeLevelNo());
        			map.put(tsSimpleCode.getRid(), itm);
        			allList.add(itm);
        			i++;
				}
				if (null!=selectIdMap.get(t.getRid())) {
					t.setIfSelected(true);
					String notHg = selectIdMap.get(t.getRid());
					t.setIfNotHg("0".equals(notHg)?true:false);
					if (row == -1) {
						row = i - i % 10;
					}
				}
        		//项目层级编码
        		String codeLevelNo = t.getTsSimpleCode().getCodeLevelNo();
        		
        		t.setLevelCode(new StringBuffer(codeLevelNo).append(".").append(t.getItemCode()).toString());
        		allList.add(t);
        		i++;
			}
        	//子级全部勾选，父级则勾选
        	for (Entry<Integer, TbTjItems> entry : map.entrySet()) {
        		TbTjItems parent = entry.getValue();
        		boolean childAllSelected = true;
        		for (TbTjItems t : allList) {
        			if (-1 == t.getRid().intValue()) {
						continue;
					}
        			String levelCode = t.getLevelCode();
        			if (levelCode.startsWith(parent.getLevelCode())) {
        				if (!t.isIfSelected()) {
        					childAllSelected = false;
        					break;
        				}
					}
        		}
        		if (childAllSelected) {
        			parent.setIfSelected(true);
				}
        	}
		}
        if( null != allList && allList.size() > 0)    {
            this.displayList.addAll(allList);
            //初始化选择当前页的第一行数
            if (row>-1) {
            	DataTable dataTable = (DataTable) FacesContext
            			.getCurrentInstance().getViewRoot()
            			.findComponent("codeForm:selectedTable");
            	dataTable.setFirst(row);
            }
        }
    }
	/**
 	 * <p>方法描述：勾选大类，则小类默认全部选择</p>
 	 * @MethodAuthor qrr,2019年12月2日,selectAction
	 * */
	public void selectAction(TbTjItems code){
		String codeLevelNo = code.getLevelCode();
		if (StringUtils.isBlank(codeLevelNo)) {
			return;
		}
		if (code.isIfSelected()){//大类勾选，小类全部选择
			for (TbTjItems t : allList) {
				String levelNo = t.getLevelCode();
				if (levelNo.startsWith(codeLevelNo + ".")) {//下级全部勾选
					t.setIfSelected(true);
				}
			}
		}else {//大类不勾选，小类不选择
			for (TbTjItems t : allList) {
				String levelNo = t.getLevelCode();
				if (codeLevelNo.startsWith(levelNo + ".")) {//上级不勾选
					t.setIfSelected(false);
				}
				if (levelNo.startsWith(codeLevelNo + ".")) {//下级不勾选
					t.setIfSelected(false);
				}
			}
		}
	}
    /**
 	 * <p>方法描述：根据名称、拼音码过滤数据</p>
 	 * @MethodAuthor qrr,2018年4月9日,searchAction
     */
    public void searchAction() {
		 //初始化展示页面的疫苗数据集
        this.displayList = new ArrayList<TbTjItems>();
        List<TbTjItems> list = new ArrayList<TbTjItems>();
        if(null != allList && allList.size() > 0 ){
        	if (StringUtils.isNotBlank(firstCodeNo)) {
        		for(TbTjItems t :allList)   {
        			if (t.getLevelCode().startsWith(firstCodeNo)) {
                        list.add(t);
                    }
        		}
        		if(StringUtils.isNotBlank(searchNamOrPy)){
        			for(TbTjItems t :list)   {
        				//名称
        				String codeName = t.getItemName()==null?"":t.getItemName();
        				//如果模糊匹配上，则增加
        				if (codeName.indexOf(searchNamOrPy) != -1) {
        					this.displayList.add(t);
        				}
        			}
        		}else{
        			this.displayList.addAll(list);
        		}
			}else if(StringUtils.isNotBlank(searchNamOrPy)){
        		for(TbTjItems t :allList)   {
    				//名称
    				String codeName = t.getItemName()==null?"":t.getItemName();
    				//如果模糊匹配上，则增加
    				if (codeName.indexOf(searchNamOrPy) != -1) {
    					this.displayList.add(t);
    				}
    			}
        	}else{
        		this.displayList.addAll(allList);
        	}
        	
        }
        DataTable dataTable = (DataTable) FacesContext
    			.getCurrentInstance().getViewRoot()
    			.findComponent("codeForm:selectedTable");
    	dataTable.setFirst(0);
	}
    /**
 	 * <p>方法描述：提交</p>
 	 * @MethodAuthor qrr,2019年12月2日,submitAction
     * */
    public void submitAction() {
    	if (null!=allList && allList.size()>0) {
			List<TbTjItems> results = new ArrayList<>();
    		for (TbTjItems t : allList) {
    			if (-1==t.getRid().intValue()) {
					continue;
				}
    			if (t.isIfSelected()) {
    				results.add(t);
    			}
			}
    		if (null==results ||results.size()==0) {
    			JsfUtil.addErrorMessage("请选择数据！");
    			return;
			}else {
				if (results.size()>1000) {
					JsfUtil.addErrorMessage("最多只能选择1000条数据！");
	    			return;
				}
			}
    		Map<String, List<TbTjItems>> map = new HashMap<String, List<TbTjItems>>();
    		map.put("selectPros", results);
    		RequestContext.getCurrentInstance().closeDialog(map);
		}
	}
    /**
 	 * <p>方法描述：关闭</p>
 	 * @MethodAuthor qrr,2018年4月9日,dialogClose
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }


    public String getSearchNamOrPy() {
        return searchNamOrPy;
    }

    public void setSearchNamOrPy(String searchNamOrPy) {
        this.searchNamOrPy = searchNamOrPy;
    }

    public List<TsSimpleCode> getFirstList() {
        return firstList;
    }

    public void setFirstList(List<TsSimpleCode> firstList) {
        this.firstList = firstList;
    }
	public List<TbTjItems> getDisplayList() {
		return displayList;
	}
	public void setDisplayList(List<TbTjItems> displayList) {
		this.displayList = displayList;
	}
	public String getFirstCodeNo() {
		return firstCodeNo;
	}
	public void setFirstCodeNo(String firstCodeNo) {
		this.firstCodeNo = firstCodeNo;
	}
}
