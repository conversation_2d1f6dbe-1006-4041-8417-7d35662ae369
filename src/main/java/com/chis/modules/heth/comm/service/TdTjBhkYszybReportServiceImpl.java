package com.chis.modules.heth.comm.service;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>类描述： </p>
 *
 * @ClassAuthor: yzz
 * @date： 2022年06月27日
 **/
@Service
@Transactional(readOnly = false)
public class TdTjBhkYszybReportServiceImpl extends AbstractTemplate {


    public List<Object[]> getReturnedDataList(String searchZoneCode, String searchUnitId, Date searchDateStart, Date searchDateEnd) {
        StringBuilder sql=new StringBuilder();
        sql.append(" select * from ( ");
        sql.append(" SELECT  distinct NULL  AS RID, T.RID AS REL_BHK_ID,T11.ZONE_GB, " );
        sql.append(" CASE WHEN T11.ZONE_TYPE > 2 THEN SUBSTR(T11.FULL_NAME, INSTR(T11.FULL_NAME, '_') + 1)  ELSE T11.FULL_NAME END ZONE_NAME, ");
        sql.append(" T2.UNITNAME, T3.PERSON_NAME, T10.CODE_NAME as CARD_TYPE, ");
        sql.append("  DECODE(LENGTH(T3.IDC), 15, SUBSTR(T3.IDC, 1, 6) || '******' || SUBSTR(T3.IDC, 13), 18, ");
        sql.append("  SUBSTR(T3.IDC, 1, 6) || '********' || SUBSTR(T3.IDC, 15), ");
        sql.append("   CASE WHEN T3.IDC IS NOT NULL THEN SUBSTR(T3.IDC, 1, LENGTH(T3.IDC) - 4) || '****' END) AS IDC, ");
        sql.append("   T9.CODE_NAME as OCC_DISENAME, T.BHK_CODE, to_char(T.BHK_DATE,'yyyy-mm-dd') as BHK_DATE, ");
        sql.append("  CASE WHEN T5.ZONE_TYPE > 2 THEN SUBSTR(T5.FULL_NAME, INSTR(T5.FULL_NAME, '_') + 1) ELSE T5.FULL_NAME END CRPT_ZONE_NAME, ");
        sql.append("  T4.CRPT_NAME,T4.INSTITUTION_CODE ");
        sql.append("  FROM TD_TJ_BHK T ");
        sql.append("  INNER JOIN TB_TJ_SRVORG T1 ON T.BHKORG_ID = T1.RID ");
        sql.append("  INNER JOIN TS_UNIT T2 ON T1.REG_ORGID = T2.RID ");
        sql.append("  LEFT JOIN TS_ZONE T11 ON T2.ZONE_ID = T11.RID ");
        sql.append("  LEFT JOIN TB_TJ_CRPT T4 ON T.CRPT_ID = T4.RID ");
        sql.append("  LEFT JOIN TS_ZONE T5 ON T4.ZONE_ID = T5.RID ");
        sql.append("  Inner JOIN TD_TJ_SUPOCCDISELIST t8 on t8.BHK_ID = t.rid ");
        sql.append("  LEFT JOIN TS_SIMPLE_CODE T9 ON T9.RID = T8.OCC_DISEID ");
        sql.append("  left join TD_TJ_PERSON T3 on T.PERSON_ID = T3.rid ");
        sql.append("  left join TS_SIMPLE_CODE T10 on T3.CARD_TYPE_ID = T10.RID ");
        sql.append("  WHERE T.BHK_TYPE IN (3, 4) ");
        sql.append("  AND NOT EXISTS( ");
        sql.append("  SELECT 1 FROM TD_ZW_YSZYB_RPT TT ");
        sql.append("   INNER JOIN TD_ZW_BGK_LAST_STA TT1 ON TT1.BUS_ID = TT.RID ");
        sql.append("   INNER JOIN TS_SIMPLE_CODE TT2 ON TT2.RID = TT.SOURCE_ID ");
        sql.append("   WHERE NVL(TT.DEL_MARK, 0) = 0 ");
        sql.append("   AND TT.REL_BHK_ID = T.RID ");
        sql.append("   and tt.OCC_DISEID = T8.OCC_DISEID ");
        sql.append("   AND TT1.CART_TYPE=2 ");
        sql.append("   AND TT2.EXTENDS1 = 1 and TT1.STATE>0 ) ");
        sql.append("   AND T4.INTER_PRC_TAG=1 ");
        if(StringUtils.isNotBlank(searchZoneCode)){
            sql.append(" AND T11.ZONE_GB LIKE '"+ ZoneUtil.zoneSelect(searchZoneCode) + "%' ");
        }
        if(searchUnitId != null ){
            sql.append(" AND T2.RID in ( ").append(searchUnitId).append(")");
        }
        if(searchDateStart != null ){
            sql.append(" AND T.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(searchDateStart, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
        }
        if(searchDateEnd != null){
            sql.append(" AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(searchDateEnd, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
        }
        sql.append("  ) TTA ");
        sql.append(" order by TTA.ZONE_GB, TTA.UNITNAME, TTA.IDC, TTA.BHK_DATE ");
       return em.createNativeQuery(sql.toString()).getResultList();
    }
}
