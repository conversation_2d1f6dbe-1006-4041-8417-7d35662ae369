package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSONObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.entity.TbTjCrptIndepend;
import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.heth.comm.json.CrptDialogParamJson;
import com.chis.modules.heth.comm.logic.SymCodeCommPO;
import com.chis.modules.heth.comm.service.TbTjCrptIndependCommServiceImpl;
import com.chis.modules.heth.comm.service.TbTjSrvorgCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.DefaultLazyDataModel;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesBean;
import com.chis.modules.system.web.IndusTypeCodePanelBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>描述 用人单位选择</p>
 *
 * @ClassAuthor gongzhe,2022/7/18 9:25,CrptCommSelectListBean
 */
@ManagedBean(name="crptCommSelectListBean")
@ViewScoped
public class CrptCommSelectListBean extends FacesBean {
    private static final long serialVersionUID = 1L;
    /**填报机构Id*/
    private String createUnitId;
    /**表格数据*/
    private DefaultLazyDataModel<TbTjCrpt> crptCommList;
    private final String TABLE_ID = "selectForm:crptCommTable";
    /**查询条件：地区*/
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
    private Integer searchZoneId;
    /**查询条件：单位名称*/
    private String searchCrptName;
    /**查询条件：社会信用代码*/
    private String searchCrptCode;
    private TbTjCrpt selectCrptComm;
    private Integer crptCommId;
    /**修改页面：企业*/
    private TbTjCrpt editCrptComm;
    private TbTjCrpt oriEditCrptComm;
    private TbTjCrptIndepend crptIndepend;
    /**修改页面：地区*/
    private List<TsZone> editZoneList;
    private Integer editZoneId;
    private String editZoneName;
    private Short editZoneType;
    /**企业规模下拉*/
    private List<TsSimpleCode> crptsizwList;
    private List<Integer> economyIds;
    private List<Integer> indusTypeIds;
    private List<TsSimpleCode> econList;
    private List<TsSimpleCode> indusList;
    /**行业类别/经济类型：所有码表数据*/
    private List<TsSimpleCode> allList;
    /**行业类别/经济类型：展示码表数据*/
    private List<TsSimpleCode> displayList;
    /**行业类别/经济类型弹出框*/
    private String selCodeName;
    private String firstCodeNo;
    private List<TsSimpleCode> firstList;
    private String searchNamOrPy;
    private TsSimpleCode selectPro;
    /** 是否显示本省外的其他省份地区 默认不展示 */
    private Boolean ifShowOtherZone;
    /** 是否需要查询列表地区默认选中登录人所在单位的省级，默认选中*/
    private Boolean ifSearchZoneDefault = Boolean.TRUE;
    /** 来源
     *  1 体检录入
     *  2 职业性有害因素监测卡填报
     * */
    private String source;
    /** 业务类型
     *1：职业健康检查
     *2：职业病诊断
     *3：职业病鉴定
     *4：职业病危害因素检测
     *5：技术服务申报
     *6：在线申报
     *7：工作场所监测
     *8：现状调查
     * */
    private String busType;
    /** 是否需要添加功能+修改功能 默认有*/
    private Boolean ifEdit = Boolean.TRUE;
    /**是否体检录入模块*/
    private Boolean ifTjlrCrpt = Boolean.FALSE;
    /**是否从职业性有害因素监测卡填报进来*/
    private Boolean ifJcCrpt = Boolean.FALSE;
    /** 在册职工总数+外委人员数 如：职业性有害因素检测流程*/
    private Boolean ifJcLcCrpt = Boolean.FALSE;
    /**
     * 用人单位编辑时，单位地址、行业类别、企业规模、经济类型、联系人、联系电话6个信息点是否不必填 <pre>默认否
     */
    private Boolean ifGs = Boolean.FALSE;

    private Map<String, TsZone> zoneMap = new HashMap<String, TsZone>();

    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private TbTjCrptIndependCommServiceImpl service = SpringContextHolder.getBean(TbTjCrptIndependCommServiceImpl.class);
    private TbTjSrvorgCommServiceImpl tbTjSrvorgService = SpringContextHolder.getBean(TbTjSrvorgCommServiceImpl.class);

    /** 行业类别弹框Bean */
    private IndusTypeCodePanelBean codePanelBean;
    /** 上级单位*/
    private List<TsZone> zoneUpperList;
    private String searchUpperZoneCode;
    private String searchUpperZoneName;
    private Integer searchUpperZoneId;
    /**查询条件：单位名称*/
    private String searchUpperCrptName;
    /**查询条件：社会信用代码*/
    private String searchUpperCrptCode;

    private DefaultLazyDataModel<TbTjCrpt> crptUpperList;
    private final String UPPER_TABLE_ID = "selectForm:crptUpperTable";

    private TbTjCrpt selectCrptUpper;
    /** 是否审核*/
    private Boolean ifCheck;
    /** 机构是否已被关联*/
    private Boolean ifLink = false;
    /**
     * 判断验证地区必须为当前职卫平台所属省份地区 null不验证 默认null
     * 1 当选择的用人单位行业类别不为人力资源服务时，地区必须为当前职卫平台所属省份地区
     * 2 选择的用工单位地区必须为当前职卫平台所属省份地区，不能为人力资源服务
     * 3 选择的用人单位地区必须为当前职卫平台所属省份地区
     * 4 选择的用工单位不能为人力资源服务
     * */
    private Integer areaValidate = null;

    /**职工人数，接触职业病危害因素人数 是否必填 体检录入 当“行业类别”选择“人力资源服务”时不必填;默认必填*/
    private Boolean ifTj = Boolean.TRUE;
    /** 是否显示健康企业建设情况 */
    private Boolean ifShowHealthCrpt;
    /** 健康企业建设情况码表 */
    private List<TsSimpleCode> healthCrptSimpleCodeList;
    /** 健康企业建设情况组件数据 */
    private List<SymCodeCommPO> symCodeList;
    /** 选中的健康企业建设情况 */
    private List<Integer> selectHealthCrptIdList;
    /** 扩展字段1为1的健康企业建设情况 */
    private List<Integer> specialHealthRidList;
    private List<List<SymCodeCommPO>> groupSymCodeList;

    /**单位弹出框中排除掉的记录rid 多个用英文逗号拼接*/
    private String excludeRids;

    /**
     * 是否主动监测任务选择用人单位, 默认否
     */
    private Boolean ifActiveMonitoringTask = Boolean.FALSE;
    /**
     * 当前操作主动监测任务RID
     */
    private Integer activeMonitoringTaskRid;
    /**
     * 用于存放重点行业的行业类别RID(5002)
     */
    private Set<Integer> keyIndustryTypeSubSet;

    /**
     * 社会信用代码是否使用老规则校验
     */
    private boolean ifWeakVerify=Boolean.FALSE;

    public CrptCommSelectListBean() {
        this.initReqParams();
        this.initZone();
        this.initCrptComm();
        this.initSimpleCodeList();
        this.indusTypeIds = ridListByList(this.indusList);
        this.economyIds = ridListByList(this.econList);
        this.codePanelBean = new IndusTypeCodePanelBean();
        if(StringUtils.isNotBlank(this.searchCrptName)){
            this.searchAction();
        }
    }

    /**
     * <p>描述 初始化请求参数</p>
     *
     * @MethodAuthor gongzhe,2022/7/20 19:02,initReqParams
     * @return void
     */
    private void initReqParams(){
        String jsonParamStr = JsfUtil.getRequest().getParameter("jsonParam");
        CrptDialogParamJson json = JSONObject.parseObject(jsonParamStr,CrptDialogParamJson.class);
        this.ifSearchZoneDefault = json.getIfSearchZoneDefault();
        this.ifShowOtherZone = json.getIfShowOtherZone();
        this.source = json.getSource();
        initSource();
        //业务类型
        this.busType = json.getBusType();
        initWeakVerify();
        this.ifEdit = json.getIfEdit();
        this.ifGs = json.getIfGs();
        this.createUnitId = Global.getUser().getTsUnit().getRid().toString();
        if(StringUtils.isNotBlank(json.getSearchCrptName())){
            this.searchCrptName = json.getSearchCrptName();
        }
        this.areaValidate = json.getAreaValidate();
        this.ifShowHealthCrpt = null != json.getIfShowHealthCrpt() && json.getIfShowHealthCrpt();
        this.excludeRids=json.getExcludeRids();
        this.ifActiveMonitoringTask = json.getIfActiveMonitoringTask();
        this.activeMonitoringTaskRid = json.getActiveMonitoringTaskRid();
    }

    /** 弹窗来源 用于控制页面展示及特殊字段处理
     *  1 展示职工人数+接害人数且新标记为1，如：体检录入
     *  2 展示职工人数+生产工人数，如：职业性有害因素监测卡填报
     *  3 展示在册职工总数+外委人员数 如：职业性有害因素检测流程
     * */
    private void initSource(){
        if(StringUtils.isBlank(this.source)){
            return ;
        }
        switch(source){
            case "1":
                ifTjlrCrpt = true;
                break;
            case "2":
                ifJcCrpt = true;
                break;
            case "3":
                ifJcLcCrpt = true;
                break;

        }
    }

    /**
    * <p>Description：社会信用代码通过模块区分验证方法 hutool工具或者老版验证 </p>
    * <p>Author： yzz 2024-02-22 </p>
    */
    public void initWeakVerify(){
        //如果后续有哪个模块要求使用hutool工具校验，则在这添加busType的判断
        ifWeakVerify = true;
    }


    /**
     * @Description : 地区初始化
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 15:16
     **/
    private void initZone() {
        String jsZone = Global.getUser().getTsUnit().getTsZone().getZoneGb().substring(0, 2) + "00000000";
        if (this.ifShowOtherZone) {
            this.editZoneList = this.commService.findZoneListByGbAndTypeNoNation("", true, "", "");
        } else {
            this.editZoneList = this.commService.findZoneListByGbAndTypeNoNation(jsZone, true, "", "");
        }

        this.zoneList = editZoneList;
        if (!CollectionUtils.isEmpty(this.editZoneList)) {
            for (TsZone tsZone : this.editZoneList) {
                if (null != tsZone.getZoneGb() && tsZone.getZoneGb().trim().equals(jsZone) && this.ifSearchZoneDefault) {
                    this.searchZoneCode = jsZone;
                    this.searchZoneName = tsZone.getZoneName();
                    this.searchZoneId = tsZone.getRid();
                }
                this.zoneMap.put(tsZone.getRid().toString(), tsZone);
            }
        }
        this.zoneUpperList = this.zoneList;
        this.searchUpperZoneCode = this.searchZoneCode;
        this.searchUpperZoneName = this.searchZoneName;
        this.searchUpperZoneId = this.searchZoneId;
    }

    /**
     * @Description : 初始化码表数据
     * @MethodAuthor: anjing
     * @Date : 2021/4/14 10:48
     **/
    private void initSimpleCode() {
        if(this.editCrptComm == null){
            editCrptComm = new TbTjCrpt();
            editCrptComm.setIfSubOrg((short) 0);
            ifCheck = false;
        }
        if(null == this.editCrptComm.getTsSimpleCodeByEconomyId()) {
            this.editCrptComm.setTsSimpleCodeByEconomyId(new TsSimpleCode());
        }
        if(null == this.editCrptComm.getTsSimpleCodeByIndusTypeId()) {
            this.editCrptComm.setTsSimpleCodeByIndusTypeId(new TsSimpleCode());
        }
        if(null == this.editCrptComm.getTsSimpleCodeByCrptSizeId()) {
            this.editCrptComm.setTsSimpleCodeByCrptSizeId(new TsSimpleCode());
        }
    }

    /**
     * @Description : 企业信息
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 16:13
     **/
    private void initCrptComm() {
        this.initSimpleCode();
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if(null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.editZoneId = tsZone.getRid();
        this.editZoneName = tsZone.getZoneName();
        this.editZoneType = tsZone.getZoneType();
    }

    /**
     * @Description : 企业规模码表初始化
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 17:21
     **/
    private void initSimpleCodeList() {
        this.indusList = this.commService.findNumSimpleCodesByTypeId("5002");
        this.econList = this.commService.findNumSimpleCodesByTypeId("5003");
        this.crptsizwList = this.commService.findLevelSimpleCodesByTypeId("5004");
        this.healthCrptSimpleCodeList = this.ifShowHealthCrpt ?
                this.commService.findLevelSimpleCodesByTypeId("5589") : null;
        this.symCodeList = new ArrayList<>();
        this.selectHealthCrptIdList = new ArrayList<>();
        this.specialHealthRidList = new ArrayList<>();
        this.groupSymCodeList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(this.healthCrptSimpleCodeList)){
            for(TsSimpleCode simpleCode : this.healthCrptSimpleCodeList){
                Integer rid = simpleCode.getRid();
                String ext1 = simpleCode.getExtendS1();
                if("1".equals(simpleCode.getExtendS1())){
                    specialHealthRidList.add(rid);
                }
                SymCodeCommPO commPO = new SymCodeCommPO();
                commPO.setRid(rid);
                commPO.setCodeName(simpleCode.getCodeName());
                commPO.setIfSelected(false);
                commPO.setSelectAble(true);
                commPO.setExtendS1(ext1);
                commPO.setCodeNo(simpleCode.getCodeNo());
                this.symCodeList.add(commPO);
            }
            this.groupSymCodeList = StringUtils.splitListProxy(this.symCodeList, 2);
        }
        this.keyIndustryTypeSubSet = new HashSet<>();
        //主动监测任务 初始化“重点行业”下的行业类别RID 和 当年已关联且未标删的主动监测任务关联的企业RID(当前操作主动监测任务除外)
        if (this.ifActiveMonitoringTask) {
            String sql = "SELECT DS.ANALY_ITEM_ID " +
                    "FROM TD_ZDZYB_ANALY_DETAIL_SUB DS " +
                    "         LEFT JOIN TD_ZDZYB_ANALY_DETAIL D ON DS.MAIN_ID = D.RID " +
                    "         LEFT JOIN TD_ZDZYB_ANALY_TYPE T ON D.MAIN_ID = T.RID " +
                    "WHERE T.BUS_TYPE = 4 AND T.ANALY_TYPE = 3";
            List<BigDecimal> keyIndustryTypeSubList =
                    CollectionUtil.castList(BigDecimal.class, this.commService.findDataBySqlNoPage(sql, null));
            for (BigDecimal keyIndustryTypeSub : keyIndustryTypeSubList) {
                Integer keyIndustryTypeSubInt = ObjectUtil.convert(Integer.class, keyIndustryTypeSub);
                if (keyIndustryTypeSubInt == null) {
                    continue;
                }
                this.keyIndustryTypeSubSet.add(keyIndustryTypeSubInt);
            }
        }
    }

    /**
    * @Description : 查找经济性质与行业类别最后一级的rid集合
    * @MethodAuthor: anjing
    * @Date : 2021/4/15 13:42
    **/
    private List<Integer> ridListByList(List<TsSimpleCode> resList) {
        List<Integer> resultList = new ArrayList<>();
        if (resList != null && resList.size() > 0) {
            Map<String, List<TsSimpleCode>> levelMap = new HashMap<>();
            for (TsSimpleCode obj : resList) {
                String codeLevelNo = obj.getCodeLevelNo();
                if (StringUtils.isBlank(codeLevelNo)) {
                    continue;
                }
                levelMap.put(codeLevelNo, new ArrayList<TsSimpleCode>());
                if (StringUtils.contains(codeLevelNo, ".")) {
                    String[] split = codeLevelNo.split("\\.");
                    StringBuffer parentCodeSb = new StringBuffer();
                    for (int i = 0; i < split.length-1; i++) {//仅找出父级
                        parentCodeSb.append(".").append(split[i]);
                        String parentCode = parentCodeSb.substring(1);
                        List<TsSimpleCode> childs = levelMap.get(parentCode);
                        if (null==childs) {
                            childs = new ArrayList<TsSimpleCode>();
                            levelMap.put(parentCode, childs);
                        }
                        childs.add(obj);
                    }
                }
            }
            for (TsSimpleCode t : resList) {
                t.setLevelIndex(StringUtils.countMatches(t.getCodeLevelNo(), ".")+ "");
                // 默认不可选择
                t.setIfSelected(false);
                List<TsSimpleCode> childs = levelMap.get(t.getCodeLevelNo());
                if (CollectionUtils.isEmpty(childs)) {
                    resultList.add(t.getRid());
                    // 最末级支持选择
                    t.setIfSelected(true);
                }
            }
        }
        return resultList;
    }

    /**
     * @Description : 执行查询
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 14:41
     **/
    public void searchAction() {
        if(StringUtils.isBlank(this.searchCrptName) && StringUtils.isBlank(this.searchCrptCode)){
            JsfUtil.addErrorMessage("单位名称和社会信用代码必填其中一项!");
            return;
        }
        Map<String, Object> paramMap = new HashMap<>();
        StringBuilder sb = new StringBuilder();
        StringBuilder sbCount = new StringBuilder();
        StringBuilder sql = new StringBuilder();
        sql.append(" FROM TbTjCrpt T ");
        //查询未标删的
        sql.append(" WHERE T.delMark = 0 ");
        // 地区
        if(StringUtils.isNotBlank(this.searchZoneCode)){
            sql.append(" AND T.tsZoneByZoneId.zoneGb LIKE '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%' ");
        }
        // 单位名称
        if(StringUtils.isNotBlank(this.searchCrptName)) {
            sql.append(" AND T.crptName LIKE :searchCrptName ");
            paramMap.put("searchCrptName", "%" + this.searchCrptName + "%");
        }
        // 社会信用代码
        if(StringUtils.isNotBlank(this.searchCrptCode)) {
            sql.append(" AND T.institutionCode LIKE :searchCrptCode ");
            paramMap.put("searchCrptCode", "%" + this.searchCrptCode + "%");
        }
        if(StringUtils.isNotBlank(this.excludeRids)){
            sql.append(" and T.rid not in(").append(excludeRids).append(")");

        }
        sb.append("SELECT T ").append(sql).append(" ORDER BY T.tsZoneByZoneId.zoneGb,T.crptName ");
        sbCount.append("SELECT COUNT(T.rid) ").append(sql);
        String[] hql = new String[]{sb.toString(),sbCount.toString()};
        this.crptCommList = new DefaultLazyDataModel(hql[0], hql[1], paramMap, TABLE_ID);
    }

    public void searchUpperAction() {
        if(StringUtils.isBlank(this.searchUpperCrptName) && StringUtils.isBlank(this.searchUpperCrptCode)){
            JsfUtil.addErrorMessage("单位名称和社会信用代码必填其中一项!");
            return;
        }
        Map<String, Object> paramMap = new HashMap<>();
        StringBuilder sb = new StringBuilder();
        StringBuilder sbCount = new StringBuilder();
        StringBuilder sql = new StringBuilder();
        sql.append(" FROM TbTjCrpt T ");
        sql.append(" WHERE  T.delMark = 0  ");
        if(this.crptCommId != null){
            sql.append(" AND T.rid != ").append(crptCommId);
        }
        // 地区
        if(StringUtils.isNotBlank(this.searchUpperZoneCode)){
            sql.append(" AND T.tsZoneByZoneId.zoneGb LIKE '").append(ZoneUtil.zoneSelect(this.searchUpperZoneCode)).append("%' ");
        }
        // 单位名称
        if(StringUtils.isNotBlank(this.searchUpperCrptName)) {
            sql.append(" AND T.crptName LIKE :searchCrptName ");
            paramMap.put("searchCrptName", "%" + this.searchUpperCrptName + "%");
        }
        // 社会信用代码
        if(StringUtils.isNotBlank(this.searchUpperCrptCode)) {
            sql.append(" AND T.institutionCode LIKE :searchCrptCode ");
            paramMap.put("searchCrptCode", "%" + this.searchUpperCrptCode + "%");
        }
        //查询分支机构为否的，即主体机构
        sql.append(" AND T.ifSubOrg = 0 ");
        sb.append("SELECT T ").append(sql).append(" ORDER BY T.tsZoneByZoneId.zoneGb,T.crptName");
        sbCount.append("SELECT COUNT(T.rid) ").append(sql);
        String[] hql = new String[]{sb.toString(),sbCount.toString()};
        this.crptUpperList = new DefaultLazyDataModel(hql[0], hql[1], paramMap, UPPER_TABLE_ID);
    }

    /**
     * @Description : 添加企业信息—各单位独立信息
     * @MethodAuthor: anjing
     * @Date : 2021/4/14 10:34
     **/
    public void addCrptCommAction() {
        this.crptCommId = null;
        this.editCrptComm = null;
        initCrptComm();
        this.ifCheck = false;
        this.ifLink = false;
        this.ifTj = true;//为空时必填
        this.oriEditCrptComm = new TbTjCrpt();
        if(this.ifShowHealthCrpt){
            //码表rid与name
            this.selectHealthCrptIdList.clear();
            if(!CollectionUtils.isEmpty(this.symCodeList)){
                for(SymCodeCommPO commPO : this.symCodeList){
                    commPO.setIfSelected(false);
                    commPO.setSelectAble(true);
                }
            }
        }
        RequestContext.getCurrentInstance().update("selectForm:addCrptCommDialog");
        RequestContext.getCurrentInstance().execute("PF('addCrptComm').show();");
    }

    /**
     * @Description : 关闭弹出框
     * @MethodAuthor: anjing
     * @Date : 2021/4/14 10:35
     **/
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }


    /**
     * @Description : 选择经济类型/行业类别
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 16:54
     **/
    public void selCodeTypeAction(){
        this.displayList = new ArrayList<TsSimpleCode>();
        this.allList = new ArrayList<TsSimpleCode>();
        this.firstList = new ArrayList<TsSimpleCode>();
        this.searchNamOrPy = null;
        this.firstCodeNo = "";
        if("经济类型".equals(this.selCodeName)) {
            this.allList.addAll(this.econList);
        } else if("行业类别".equals(this.selCodeName)) {
            this.allList.addAll(this.indusList);
        }
        dealCodelevel(this.allList);
        if(null != this.allList && this.allList.size() > 0) {
            this.displayList.addAll(this.allList);
        }
        if("行业类别".equals(this.selCodeName)) {
            this.codePanelBean.partInit();
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("selectForm:selectedIndusCodeTable");
            dataTable.setFirst(0);
            dataTable.setRows(10);
        }else{
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("selectForm:selectedIndusTable");
            dataTable.setFirst(0);
        }
    }

    /**
     * @Description : 处理码表层级关系
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 17:03
     **/
    private void dealCodelevel(List<TsSimpleCode> list) {
        if(list != null && list.size() > 0) {
            for(TsSimpleCode code : list) {
                code.setLevelIndex(StringUtils.countMatches(
                        code.getCodeLevelNo(), ".")
                        + "");
                if(StringUtils.containsNone(code.getCodeLevelNo(), ".")) {
                    this.firstList.add(code);
                }
            }
        }
    }

    /**
     * @Description : 行业类别/经济类型查询
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 17:08
     **/
    public void searchCodeAction() {
        //初始化展示页面的疫苗数据集
        this.displayList = new ArrayList<TsSimpleCode>();
        List<TsSimpleCode> list = new ArrayList<TsSimpleCode>();
        if(null != this.allList && this.allList.size() > 0 ) {
            if(StringUtils.isNotBlank(this.firstCodeNo)) {
                for(TsSimpleCode t : this.allList) {
                    if (t.getCodeLevelNo().startsWith(this.firstCodeNo)) {
                        list.add(t);
                    }
                }
                if(StringUtils.isNotBlank(this.searchNamOrPy)){
                    for(TsSimpleCode t :list) {
                        //疫苗名称
                        String codeName = t.getCodeName()==null?"":t.getCodeName().toString();
                        //疫苗拼音码
                        String codePym = t.getSplsht()==null?"":t.getSplsht().toString();
                        //如果模糊匹配上，则增加
                        if (codeName.indexOf(this.searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(this.searchNamOrPy.toUpperCase()) != -1) {
                            this.displayList.add(t);
                        }
                    }
                } else {
                    this.displayList.addAll(list);
                }
            } else if(StringUtils.isNotBlank(this.searchNamOrPy)) {
                for(TsSimpleCode t : this.allList) {
                    //疫苗名称
                    String codeName = t.getCodeName()==null?"":t.getCodeName().toString();
                    //疫苗拼音码
                    String codePym = t.getSplsht()==null?"":t.getSplsht().toString();
                    //如果模糊匹配上，则增加
                    if (codeName.indexOf(this.searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(this.searchNamOrPy.toUpperCase()) != -1) {
                        this.displayList.add(t);
                    }
                }
            } else {
                this.displayList.addAll(this.allList);
            }
        }
    }

    /**
     * @Description : 选择经济类型/行业类别
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 17:10
     **/
    public void selectAction(){
        if ("经济类型".equals(this.selCodeName)) {
            this.editCrptComm.setTsSimpleCodeByEconomyId(this.selectPro);
            RequestContext.getCurrentInstance().update("selectForm:economyName");
        } else if ("行业类别".equals(this.selCodeName)) {
            this.editCrptComm.setTsSimpleCodeByIndusTypeId(this.selectPro);
            RequestContext.getCurrentInstance().update("selectForm:indusTypeName");
        }
        RequestContext.getCurrentInstance().execute("PF('selDialog').hide()");
    }

    /**
     * @Description: 行业类别选择
     *
     * @MethodAuthor pw,2022年02月7日
     */
    public void selectIndusTypeAction(){
        this.editCrptComm.setTsSimpleCodeByIndusTypeId(this.selectPro);
        //当“行业类别”选择“人力资源服务”时，“职工人数”和“接触职业病危害因素人数”不必填
        if(ifTjlrCrpt){
            //体检录入页面进入
            if("2".equals(selectPro.getExtendS1())){
                this.ifTj = false;
            }else {
                this.ifTj = true;
            }
        }
        RequestContext.getCurrentInstance().update("selectForm:addCrptCommPanel");
        RequestContext.getCurrentInstance().execute("PF('selIndusTypeDialog').hide()");
    }

    /**
     * @Description : 清空行业类别/经济类型
     * @MethodAuthor: anjing
     * @Date : 2021/4/15 11:19
     **/
    public void delCodeName(){
        if ("经济类型".equals(this.selCodeName)) {
            this.editCrptComm.setTsSimpleCodeByEconomyId(new TsSimpleCode());
            RequestContext.getCurrentInstance().update("selectForm:economyName");
        }else if ("行业类别".equals(this.selCodeName)) {
            this.editCrptComm.setTsSimpleCodeByIndusTypeId(new TsSimpleCode());
            if(ifTjlrCrpt){
                this.ifTj = true;
            }
            RequestContext.getCurrentInstance().update("selectForm:addCrptCommPanel");
        }
    }

    /**
     * @Description : 保存企业信息—各单位独立信息前校验
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 17:31
     **/
    private boolean veryTbTjCrptComm() {
        boolean flag = true;
        if (this.oriEditCrptComm == null) {
            this.oriEditCrptComm = new TbTjCrpt();
        }
        // 所属地区
        if (null == this.editCrptComm.getTsZoneByZoneId()
                || null == this.editCrptComm.getTsZoneByZoneId().getRid()) {
            JsfUtil.addErrorMessage("请选择所属地区！");
            flag = false;
        } else {
            if (null == this.zoneMap.get(this.editCrptComm.getTsZoneByZoneId().getRid().toString())) {
                JsfUtil.addErrorMessage("所属地区已停用，请重新选择！");
                flag = false;
            }
            Short realZoneType = (null == this.editCrptComm.getTsZoneByZoneId() || null == this.editCrptComm.getTsZoneByZoneId().getRid() || null == this.zoneMap.get(this.editCrptComm.getTsZoneByZoneId().getRid().toString())) ? null
                    : this.zoneMap.get(this.editCrptComm.getTsZoneByZoneId().getRid().toString()).getRealZoneType();
            if (null == realZoneType || realZoneType.intValue() < 5) {
                JsfUtil.addErrorMessage("所属地区必须选择至街道！");
                flag = false;
            }
        }
        // 企业名称
        if (StringUtils.isBlank(this.editCrptComm.getCrptName())) {
            JsfUtil.addErrorMessage("单位名称不能为空！");
            flag = false;
        }
        if (this.editCrptComm.getIfSubOrg() == null) {
            JsfUtil.addErrorMessage("是否分支机构不能为空！");
            flag = false;
        }
        //是否分支机构 1是
        if (this.editCrptComm.getIfSubOrg() != null && this.editCrptComm.getIfSubOrg() == 1) {
            if (this.editCrptComm.getFkByUpperUnitId() == null || StringUtils.isBlank(this.editCrptComm.getFkByUpperUnitId().getCrptName())) {
                JsfUtil.addErrorMessage("上级单位名称不能为空！");
                flag = false;
            }
        }
        // 社会信用代码
        if (StringUtils.isBlank(this.editCrptComm.getInstitutionCode())) {
            JsfUtil.addErrorMessage("社会信用代码不能为空！");
            flag = false;
        } else {
            // 社会信用证代码合法与重复性
            if (!StringUtils.verifyCreditCode(ifWeakVerify,this.editCrptComm.getInstitutionCode())) {
                JsfUtil.addErrorMessage("社会信用代码不符合规范！");
                flag = false;
            } else {
                int num = 0;
                if (this.editCrptComm.getIfSubOrg() != null && this.editCrptComm.getIfSubOrg() == 0) {
                    //主体机构，验证社会信仰代码唯一
                    num = this.service.findTbTjCrptCountsByInstitutionCodeAndCrptName(this.editCrptComm.getInstitutionCode().trim(), null, this.editCrptComm.getRid(), this.editCrptComm.getIfSubOrg());
                    if (num > 0) {
                        JsfUtil.addErrorMessage("主体机构内社会信用代码不允许重复！");
                        flag = false;
                    }
                }
                if (num == 0 && StringUtils.isNotBlank(this.editCrptComm.getCrptName())) {
                    //验证单位名称+社会信用代码唯一 (全系统唯一）
                    num = this.service.findTbTjCrptCountsByInstitutionCodeAndCrptName(this.editCrptComm.getInstitutionCode().trim(), this.editCrptComm.getCrptName().trim(), this.editCrptComm.getRid(), null);
                    if (num > 0) {
                        JsfUtil.addErrorMessage("同社会信用代码内单位名称不允许重复！");
                        flag = false;
                    }
                }
            }
        }
        // 单位地址
        if (StringUtils.isBlank(this.editCrptComm.getAddress())) {
            if (!this.ifGs) {
                JsfUtil.addErrorMessage("单位地址不能为空！");
                flag = false;
            } else if (StringUtils.isNotBlank(this.oriEditCrptComm.getAddress())) {
                JsfUtil.addErrorMessage("单位地址有值不能清空！");
                flag = false;
            }
        }
        // 行业类别
        if (this.editCrptComm.getTsSimpleCodeByIndusTypeId() == null
                || (this.editCrptComm.getTsSimpleCodeByIndusTypeId() != null && this.editCrptComm.getTsSimpleCodeByIndusTypeId().getRid() == null)) {
            if (!this.ifGs) {
                JsfUtil.addErrorMessage("请选择行业类别！");
                flag = false;
            } else if (this.oriEditCrptComm.getTsSimpleCodeByIndusTypeId() != null) {
                JsfUtil.addErrorMessage("行业类别有值不能清空！");
                flag = false;
            }
        } else if (null == this.indusTypeIds || this.indusTypeIds.size() == 0
                || !this.indusTypeIds.contains(this.editCrptComm.getTsSimpleCodeByIndusTypeId().getRid())) {
            JsfUtil.addErrorMessage("行业类别必须选择至最末级！");
            flag = false;
        }
        // 经济类型
        if (this.editCrptComm.getTsSimpleCodeByEconomyId() == null
                || (this.editCrptComm.getTsSimpleCodeByEconomyId() != null && this.editCrptComm.getTsSimpleCodeByEconomyId().getRid() == null)) {
            if (!this.ifGs) {
                JsfUtil.addErrorMessage("请选择经济类型！");
                flag = false;
            } else if (this.oriEditCrptComm.getTsSimpleCodeByEconomyId() != null) {
                JsfUtil.addErrorMessage("经济类型有值不能清空！");
                flag = false;
            }
        } else if (null == this.economyIds || this.economyIds.size() == 0 ||
                !this.economyIds.contains(this.editCrptComm.getTsSimpleCodeByEconomyId().getRid())) {
            JsfUtil.addErrorMessage("经济类型必须选择至最末级！");
            flag = false;
        }
        // 企业规模
        if (this.editCrptComm.getTsSimpleCodeByCrptSizeId() == null
                || (this.editCrptComm.getTsSimpleCodeByCrptSizeId() != null && this.editCrptComm.getTsSimpleCodeByCrptSizeId().getRid() == null)) {
            if (!this.ifGs) {
                JsfUtil.addErrorMessage("请选择企业规模！");
                flag = false;
            } else if (this.oriEditCrptComm.getTsSimpleCodeByCrptSizeId() != null) {
                JsfUtil.addErrorMessage("企业规模有值不能清空！");
                flag = false;
            }
        }
        // 联系人
        if (StringUtils.isBlank(this.editCrptComm.getLinkman2())) {
            if (!this.ifGs) {
                JsfUtil.addErrorMessage("联系人不能为空！");
                flag = false;
            } else if (StringUtils.isNotBlank(this.oriEditCrptComm.getLinkman2())) {
                JsfUtil.addErrorMessage("联系人有值不能清空！");
                flag = false;
            }
        }
        // 联系电话
        if (StringUtils.isBlank(this.editCrptComm.getLinkphone2())) {
            if (!this.ifGs) {
                JsfUtil.addErrorMessage("联系电话不能为空！");
                flag = false;
            } else if (StringUtils.isNotBlank(this.oriEditCrptComm.getLinkphone2())) {
                JsfUtil.addErrorMessage("联系电话有值不能清空！");
                flag = false;
            }
        } else {
            if (!StringUtils.vertyPhone(this.editCrptComm.getLinkphone2())) {
                JsfUtil.addErrorMessage("联系电话格式不正确！");
                flag = false;
            }
        }
        // 法定代表人（负责人）
        if (StringUtils.isBlank(this.editCrptComm.getCorporateDeputy())
                && StringUtils.isNotBlank(this.oriEditCrptComm.getCorporateDeputy())) {
            JsfUtil.addErrorMessage("法定代表人（负责人）有值不能清空！");
            flag = false;
        }
        // 法人联系电话
        if (StringUtils.isBlank(this.editCrptComm.getPhone())) {
            if (StringUtils.isNotBlank(this.oriEditCrptComm.getPhone())) {
                JsfUtil.addErrorMessage("法人联系电话有值不能清空！");
                flag = false;
            }
        } else if (!StringUtils.vertyPhone(this.editCrptComm.getPhone())) {
            JsfUtil.addErrorMessage("法人联系电话格式不正确！");
            flag = false;
        }
        // 邮政编码
        if (StringUtils.isBlank(this.editCrptComm.getPostCode())) {
            if (StringUtils.isNotBlank(this.oriEditCrptComm.getPostCode())) {
                JsfUtil.addErrorMessage("邮政编码有值不能清空！");
                flag = false;
            }
        } else if (!StringUtils.vertyPost(this.editCrptComm.getPostCode())) {
            JsfUtil.addErrorMessage("邮政编码格式不正确！");
            flag = false;
        }
        if(ifTjlrCrpt) {
            if(ifTj){
                //必填
                if(null == this.editCrptComm.getWorkForce()) {
                    JsfUtil.addErrorMessage("职工人数不允许为空！");
                    flag = false;
                }else if(this.editCrptComm.getWorkForce() <= 0 ){
                    JsfUtil.addErrorMessage("职工人数必须大于0！");
                    flag = false;
                }
                if(null == this.editCrptComm.getHoldCardMan()) {
                    JsfUtil.addErrorMessage("接触职业病危害因素人数不允许为空！");
                    flag = false;
                }else if(this.editCrptComm.getHoldCardMan() <= 0){
                    JsfUtil.addErrorMessage("接触职业病危害因素人数必须大于0！");
                    flag = false;
                }
            }
            if(null != this.editCrptComm.getWorkForce() && null != this.editCrptComm.getHoldCardMan()
                    && this.editCrptComm.getWorkForce().compareTo(this.editCrptComm.getHoldCardMan()) < 0) {
                JsfUtil.addErrorMessage("接触职业病危害因素人数必须小于等于职工人数！");
                flag = false;
            }
        }
       if(ifJcCrpt){
           if(null == this.editCrptComm.getWorkForce()) {
               JsfUtil.addErrorMessage("职工人数不允许为空！");
               flag = false;
           }else if(this.editCrptComm.getWorkForce() <= 0){
               JsfUtil.addErrorMessage("职工人数必须大于0！");
               flag = false;
           }
           //职业性有害因素监测卡填报
           if(null == this.editCrptComm.getWorkmanNum()) {
               JsfUtil.addErrorMessage("生产工人数不允许为空！");
               flag = false;
           }else if(this.editCrptComm.getWorkmanNum() <= 0){
               JsfUtil.addErrorMessage("生产工人数必须大于0！");
               flag = false;
           }
           if(null != this.editCrptComm.getWorkForce() && null != this.editCrptComm.getWorkmanNum()
                   && this.editCrptComm.getWorkForce().compareTo(this.editCrptComm.getWorkmanNum()) < 0) {
               JsfUtil.addErrorMessage("生产工人数必须小于等于职工人数！");
               flag = false;
           }
       }
       if(ifJcLcCrpt){
           if (null != this.editCrptComm.getWorkForce() && null != this.editCrptComm.getOutsourceNum()
                   && this.editCrptComm.getWorkForce() < this.editCrptComm.getOutsourceNum()) {
               JsfUtil.addErrorMessage("在册职工总数应大于等于外委人员数！");
               flag = false;
           }
       }
       // 健康企业建设情况
        if(this.ifShowHealthCrpt){
            if(CollectionUtils.isEmpty(this.selectHealthCrptIdList)){
                JsfUtil.addErrorMessage("请选择健康企业建设情况！");
                flag = false;
            }else{
                Boolean ifExt1 = null;
                boolean flagShow = false;
                for(Integer rid : this.selectHealthCrptIdList){
                    if(null == ifExt1){
                        ifExt1 = !CollectionUtils.isEmpty(this.specialHealthRidList) && this.specialHealthRidList.contains(rid);
                    }
                    if(ifExt1.booleanValue() !=
                            (!CollectionUtils.isEmpty(this.specialHealthRidList) && this.specialHealthRidList.contains(rid))){
                        flagShow = true;
                        break;
                    }

                }
                if(flagShow){
                    JsfUtil.addErrorMessage("选择健康企业建设情况否，不能选择其他健康企业建设情况！");
                    flag = false;
                }
            }
        }
        return flag;
    }

    /**
     * @Description : 选择企业信息—各单位独立信息
     * @MethodAuthor: anjing
     * @Date : 2021/4/14 11:04
     **/
    public void selectCrptCommAction() {
        Map<String, Object> map = new HashMap<>();
        if(this.ifEdit){
            boolean completeFlag = true;
            if(null != this.selectCrptComm) {
                //主动监测任务选择用人单位, 只能选择“重点行业”的企业并且不能选择当年未标删且关联主动监测任务的企业(当前操作主动监测任务除外)
                if (this.ifActiveMonitoringTask) {
                    Integer indusTypeId = this.selectCrptComm.getTsSimpleCodeByIndusTypeId() == null ? null : this.selectCrptComm.getTsSimpleCodeByIndusTypeId().getRid();
                    if (indusTypeId == null || !this.keyIndustryTypeSubSet.contains(indusTypeId)) {
                        JsfUtil.addErrorMessage("只能选择重点行业且当年未填报的单位！");
                        return;
                    }
                    Map<String, Object> paramMap = new HashMap<>();
                    String sql = "SELECT 1 FROM TB_TJ_JC_TASK T WHERE NVL(T.DEL_MARK, 0) = 0 AND T.YEAR = :year AND T.CRPT_ID = :crptId ";
                    paramMap.put("crptId", this.selectCrptComm.getRid());
                    paramMap.put("year", DateUtils.getYear());
                    if (this.activeMonitoringTaskRid != null) {
                        sql += "AND T.RID <> :rid ";
                        paramMap.put("rid", this.activeMonitoringTaskRid);
                    }
                    List<BigDecimal> activeMonitoringTaskCrptList =
                            CollectionUtil.castList(BigDecimal.class, this.commService.findDataBySqlNoPage(sql, paramMap));
                    if (!CollectionUtils.isEmpty(activeMonitoringTaskCrptList)) {
                        JsfUtil.addErrorMessage("只能选择重点行业且当年未填报的单位！");
                        return;
                    }
                }

                boolean tsZoneVery = null != this.selectCrptComm.getTsZoneByZoneId()
                        && null != this.selectCrptComm.getTsZoneByZoneId().getRid()
                        && null != this.selectCrptComm.getTsZoneByZoneId().getRealZoneType()
                        && this.selectCrptComm.getTsZoneByZoneId().getRealZoneType().intValue() >= 5
                        && new Short((short) 1).equals(this.selectCrptComm.getTsZoneByZoneId().getIfReveal())
                        && null != this.zoneMap.get(this.selectCrptComm.getTsZoneByZoneId().getRid().toString());
                boolean crptNameVery = StringUtils.isNotBlank(this.selectCrptComm.getCrptName());
                boolean ifSubOrgVery = new Short((short) 0).equals(this.selectCrptComm.getIfSubOrg())
                        || (new Short((short) 1).equals(this.selectCrptComm.getIfSubOrg()) && this.selectCrptComm.getFkByUpperUnitId() != null);
                boolean institutionCodeVery = StringUtils.isNotBlank(this.selectCrptComm.getInstitutionCode())
                        && StringUtils.verifyCreditCode(ifWeakVerify,this.selectCrptComm.getInstitutionCode());
                boolean postCodeVery = StringUtils.isBlank(this.selectCrptComm.getPostCode())
                        || StringUtils.vertyPost(this.selectCrptComm.getPostCode());
                boolean addressVery = this.ifGs || StringUtils.isNotBlank(this.selectCrptComm.getAddress());
                boolean indusTypeVery = (this.ifGs && null == this.selectCrptComm.getTsSimpleCodeByIndusTypeId())
                        || (null != this.selectCrptComm.getTsSimpleCodeByIndusTypeId() && null != this.indusTypeIds
                        && this.indusTypeIds.contains(this.selectCrptComm.getTsSimpleCodeByIndusTypeId().getRid()));
                boolean economyVery = (this.ifGs && null == this.selectCrptComm.getTsSimpleCodeByEconomyId())
                        || (null != this.selectCrptComm.getTsSimpleCodeByEconomyId() && null != this.economyIds
                        && this.economyIds.contains(this.selectCrptComm.getTsSimpleCodeByEconomyId().getRid()));
                boolean crptSizeVery = this.ifGs || null != this.selectCrptComm.getTsSimpleCodeByCrptSizeId();
                boolean phoneVery = StringUtils.isBlank(this.selectCrptComm.getPhone())
                        || (StringUtils.isNotBlank(this.selectCrptComm.getPhone())
                        && StringUtils.vertyPhone(this.selectCrptComm.getPhone()));
                completeFlag = tsZoneVery && crptNameVery && ifSubOrgVery && postCodeVery && institutionCodeVery && addressVery
                        && indusTypeVery && economyVery && crptSizeVery && phoneVery;
                // 从体检录入过来的 需要判断职工人数和接触危害因素人数是否为空,新增企业新标记验证
                if(this.ifTjlrCrpt){
                    if( null == this.selectCrptComm.getTsSimpleCodeByIndusTypeId() ||
                            !"2".equals(this.selectCrptComm.getTsSimpleCodeByIndusTypeId().getExtendS1())){
                       if(null == this.selectCrptComm.getWorkForce() || null == this.selectCrptComm.getHoldCardMan()
                               || this.selectCrptComm.getWorkForce() <= 0 ||  this.selectCrptComm.getHoldCardMan() <= 0){
                           completeFlag = false;
                       }
                    }
                if(null != this.selectCrptComm.getWorkForce() && null != this.selectCrptComm.getHoldCardMan()
                        && this.selectCrptComm.getWorkForce().compareTo(this.selectCrptComm.getHoldCardMan()) < 0){
                    completeFlag = false;
                }
                if( this.selectCrptComm.getInterPrcTag() == null || this.selectCrptComm.getInterPrcTag() != 1){
                    completeFlag = false;
                }
                }
                // 从职业性有害因素监测卡填报过来的 需要判断职工人数和生产工人数是否为空,职工人数<生产工人数
                if(this.ifJcCrpt){
                    if(null == this.selectCrptComm.getWorkForce() || null == this.selectCrptComm.getWorkmanNum()
                            || this.selectCrptComm.getWorkForce() <= 0 ||  this.selectCrptComm.getWorkmanNum() <= 0
                            || this.selectCrptComm.getWorkForce().compareTo(this.selectCrptComm.getWorkmanNum()) < 0) {
                        completeFlag = false;
                    }
                }
                TbTjCrptIndepend independ = this.service.findTbTjCrptIndependByCrptIdUnitId(selectCrptComm.getRid(),this.createUnitId,busType==null?null:Integer.valueOf(busType));
                if (this.ifGs) {
                    if (independ != null && StringUtils.isNotBlank(independ.getLinkphone2())
                            && !StringUtils.vertyPhone(independ.getLinkphone2())) {
                        completeFlag = false;
                    }
                } else {
                    if (independ == null) {
                        completeFlag = false;
                    } else {
                        if (StringUtils.isBlank(independ.getLinkman2())
                                || StringUtils.isBlank(independ.getLinkphone2())
                                || !StringUtils.vertyPhone(independ.getLinkphone2())) {
                            completeFlag = false;
                        }
                    }
                }
                // 查询判断是否存在健康企业建设情况
                if(completeFlag && this.ifShowHealthCrpt &&
                        this.service.findCrptConstructCount(this.selectCrptComm.getRid()) == 0){
                    completeFlag = false;
                }
                if(!completeFlag){
                    JsfUtil.addErrorMessage("请先完善单位信息！");
                    return;
                }
                map.put("selectCrptIndepend", independ);
            }
        }
        //目前只有体检录入和疑似职业病报告卡录入不为空
        if(null != this.areaValidate){
            TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
            if(null == tsZone) {
                tsZone = Global.getUser().getTsUnit().getTsZone();
            }
            //本省的省级编码
            String compareZoneGb = StringUtils.isBlank(tsZone.getZoneGb()) || tsZone.getZoneGb().length() < 2 ? null :
                    tsZone.getZoneGb().substring(0,2);
            String curZoneGb = this.selectCrptComm.getTsZoneByZoneId().getZoneGb();
            boolean ifOtherArea = false;
            if(StringUtils.isBlank(curZoneGb) || StringUtils.isBlank(compareZoneGb) ||
                    !curZoneGb.startsWith(compareZoneGb)){
                ifOtherArea = true;
            }
            if(1 == this.areaValidate){
                //当选择的用人单位行业类别不为人力资源服务时，地区必须为当前职卫平台所属省份地区
                if(ifOtherArea && null != this.selectCrptComm.getTsSimpleCodeByIndusTypeId() &&
                        !"2".equals(this.selectCrptComm.getTsSimpleCodeByIndusTypeId().getExtendS1())){
                    JsfUtil.addErrorMessage("单位行业类别不为“人力资源服务”行业时，仅可选择本省范围内的单位！");
                    return;
                }
            }else if(2 == this.areaValidate){
                if(ifOtherArea){
                    JsfUtil.addErrorMessage("用工单位仅可选择本省范围内的单位！");
                    return;
                }
                //用工单位 行业类别不能为“人力资源服务”行业。
                if( null != this.selectCrptComm.getTsSimpleCodeByIndusTypeId() &&
                        "2".equals(this.selectCrptComm.getTsSimpleCodeByIndusTypeId().getExtendS1())){
                    JsfUtil.addErrorMessage("用工单位的行业类别不能为“人力资源服务”行业！");
                    return;
                }
            }else if(3 == this.areaValidate){
                if(ifOtherArea){
                    JsfUtil.addErrorMessage("用人单位仅可选择本省范围内的单位！");
                    return;
                }
            }else if(4 == this.areaValidate){
                //用工单位 行业类别不能为“人力资源服务”行业。
                if( null != this.selectCrptComm.getTsSimpleCodeByIndusTypeId() &&
                        "2".equals(this.selectCrptComm.getTsSimpleCodeByIndusTypeId().getExtendS1())){
                    JsfUtil.addErrorMessage("用工单位的行业类别不能为“人力资源服务”行业！");
                    return;
                }
            }        }
        map.put("selectCrpt", this.selectCrptComm);
        RequestContext.getCurrentInstance().closeDialog(map);
    }

    /**
     * @Description : 修改企业信息—各单位独立信息
     * @MethodAuthor: anjing
     * @Date : 2021/4/14 11:05
     **/
    public void modCrptCommAction() {
        if(null == this.crptCommId || StringUtils.isBlank(this.createUnitId)) {
            return;
        }
        this.editCrptComm = this.service.find(TbTjCrpt.class,crptCommId);
        int staCount = this.service.findTdZwBgkLastStaCrpt(crptCommId);
        ifCheck = staCount > 0;
        if(this.editCrptComm != null){
            this.editCrptComm.setOriLinkman2(this.editCrptComm.getLinkman2());
            this.editCrptComm.setOriLinkphone2(this.editCrptComm.getLinkphone2());
            if(new Short((short)0).equals(editCrptComm.getIfSubOrg())){
                //若是主体机构，查询是否已被分支机构关联
                int num = this.service.findTbTjCrptSubCountsByUpperCrpt(editCrptComm.getRid());
                this.ifLink = num > 0;
            }else{
                this.ifLink = false;
            }
            this.crptIndepend = this.service.findTbTjCrptIndependByCrptIdUnitId(crptCommId,this.createUnitId,Integer.valueOf(this.busType));
            if(null == this.crptIndepend) {
                this.crptIndepend = new TbTjCrptIndepend();
                this.crptIndepend.setFkByUnitId(new TsUnit(Integer.valueOf(this.createUnitId)));
            }
            //地区赋值
            TsZone zone = this.editCrptComm.getTsZoneByZoneId();
            if(null != zone) {
                this.editZoneId = zone.getRid();
                this.editZoneName = zone.getZoneName();
                this.editZoneType = zone.getRealZoneType();
            } else {
                this.editZoneId = null;
                this.editZoneName = null;
                this.editZoneType = null;
            }
            editCrptComm.setLinkman2(this.crptIndepend.getLinkman2());
            editCrptComm.setLinkphone2(this.crptIndepend.getLinkphone2());
            this.oriEditCrptComm = new TbTjCrpt();
            try {
                ObjectCopyUtil.copyProperties(this.editCrptComm, this.oriEditCrptComm);
            } catch (Exception e) {
                e.printStackTrace();
            }
            initSimpleCode();
            //体检录入进入(“职工人数”和“接触职业病危害因素人数”)是否必填
            if(ifTjlrCrpt){
                if( null != this.editCrptComm.getTsSimpleCodeByIndusTypeId() && !"2".equals(this.editCrptComm.getTsSimpleCodeByIndusTypeId().getExtendS1())){
                    //非人力资源
                    this.ifTj = true;
                }else {
                    this.ifTj = false;
                }
            }
            //健康企业建设情况
            if(this.ifShowHealthCrpt){
                //码表rid与name
                List<Object[]> healthCrptList = this.service.findCrptConstructInfo(this.crptCommId);
                this.selectHealthCrptIdList.clear();
                if(!CollectionUtils.isEmpty(healthCrptList)){
                    for(Object[] objArr : healthCrptList){
                        String conRid = null == objArr[0] ? null : objArr[0].toString();
                        String conName = null == objArr[1] ? null : objArr[1].toString();
                        if(StringUtils.isNotBlank(conRid) && StringUtils.isNotBlank(conName)){
                            this.selectHealthCrptIdList.add(Integer.parseInt(conRid));
                        }
                    }
                }
                if(!CollectionUtils.isEmpty(this.symCodeList)){
                    for(SymCodeCommPO commPO : this.symCodeList){
                        boolean ifExt1 = !CollectionUtils.isEmpty(this.selectHealthCrptIdList)
                                && !CollectionUtils.isEmpty(this.specialHealthRidList) &&
                                this.specialHealthRidList.contains(this.selectHealthCrptIdList.get(0));
                        if(!CollectionUtils.isEmpty(this.selectHealthCrptIdList) &&
                                this.selectHealthCrptIdList.contains(commPO.getRid())){
                            commPO.setIfSelected(true);
                        }else{
                            commPO.setIfSelected(false);
                        }
                        boolean ifEnable = CollectionUtils.isEmpty(this.selectHealthCrptIdList) ||
                                (ifExt1 ? ("1".equals(commPO.getExtendS1())) : (!"1".equals(commPO.getExtendS1())));
                        commPO.setSelectAble(ifEnable);
                    }
                }
            }
        }
        RequestContext.getCurrentInstance().update("selectForm:addCrptCommDialog");
        RequestContext.getCurrentInstance().execute("PF('addCrptComm').show();");
    }

    /**
     * @Description : 保存企业信息—各单位独立信息
     * @MethodAuthor: anjing
     * @Date : 2021/4/13 16:24
     **/
    public void saveCrptCommAction() {
        try {
            if(null == this.editCrptComm) {
                return;
            }
            TsZone tsZone = new TsZone();
            tsZone.setRid(this.editZoneId);
            tsZone.setZoneName(this.editZoneName);
            tsZone.setZoneType(this.editZoneType);
            this.editCrptComm.setTsZoneByZoneId(tsZone);
            if(ifTjlrCrpt) {
                if(this.editCrptComm.getFkByCreateOrgId() == null && this.editCrptComm.getRid() == null){
                    List<TbTjSrvorg> tbTjSrvorgList = tbTjSrvorgService.selectSrvorgListByOrgId(Global.getUser().getTsUnit().getRid());
                    if(CollectionUtils.isEmpty(tbTjSrvorgList)) {
                        JsfUtil.addErrorMessage("资质服务机构未注册，请联系管理员！");
                        return ;
                    }
                    this.editCrptComm.setFkByCreateOrgId(tbTjSrvorgList.get(0));
                }
                this.editCrptComm.setInterPrcTag((short) 1);
            }
            if (this.ifActiveMonitoringTask) {
                this.editCrptComm.setInterPrcTag((short) 1);
            }
            if(veryTbTjCrptComm()) {
                if(null == this.crptIndepend || this.editCrptComm.getRid() == null) {
                    this.crptIndepend = new TbTjCrptIndepend();
                    this.crptIndepend.setFkByUnitId(new TsUnit(Integer.valueOf(this.createUnitId)));
                }
                crptIndepend.setBusType(busType==null?null:Integer.valueOf(busType));
                this.service.saveTbTjCrptIndepend(crptIndepend,editCrptComm,this.ifShowHealthCrpt ? this.selectHealthCrptIdList : null);
                RequestContext.getCurrentInstance().update("selectForm:crptCommTable");
                RequestContext.getCurrentInstance().execute("PF('addCrptComm').hide();");
                initSimpleCode();
                JsfUtil.addSuccessMessage("保存成功！");
                if(StringUtils.isBlank(this.searchCrptName) && StringUtils.isBlank(this.searchCrptCode)){
                    this.searchCrptName = this.editCrptComm.getCrptName();
                }
                this.searchAction();
                RequestContext.getCurrentInstance().update("selectForm:searchCrptName");
            }
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    public void clearCodeName(){
        this.searchZoneCode = null;
        this.searchZoneName = null;
        this.searchZoneId = null;
        searchAction();

    }

    public void selUpperUnitAction(){
        this.searchUpperZoneId = null;
        this.searchUpperZoneName = null;
        this.searchUpperZoneCode = null;
        String jsZone = Global.getUser().getTsUnit().getTsZone().getZoneGb().substring(0, 2) + "00000000";
        for(TsZone tsZone : this.zoneUpperList) {
            if( null != tsZone.getZoneGb() && tsZone.getZoneGb().trim().equals(jsZone) && ifSearchZoneDefault){
                this.searchUpperZoneCode = jsZone;
                this.searchUpperZoneName = tsZone.getZoneName();
                this.searchUpperZoneId = tsZone.getRid();
            }
        }
        this.searchUpperCrptName = null;
        this.searchUpperCrptCode = null;
        if(this.crptUpperList != null){
            crptUpperList = null;
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("selectForm:crptUpperTable");
            dataTable.reset();
        }
        RequestContext.getCurrentInstance().update("selectForm:crptUpperTable");
        RequestContext.getCurrentInstance().update("selectForm:upperUnitPanel");
        RequestContext.getCurrentInstance().execute("PF('selUpperUnitDialog').show();");
    }

    public void delUpperUnitName(){
        this.editCrptComm.setFkByUpperUnitId(null);
        this.editCrptComm.setInstitutionCode(null);
    }

    public void selectCrptUpperAction(){
        this.editCrptComm.setFkByUpperUnitId(this.selectCrptUpper);
        this.editCrptComm.setInstitutionCode(this.selectCrptUpper.getInstitutionCode());
        RequestContext.getCurrentInstance().update("selectForm:addCrptCommPanel");
        RequestContext.getCurrentInstance().execute("PF('selUpperUnitDialog').hide();");
        RequestContext.getCurrentInstance().execute("removeExtraZonePael('#selectForm\\\\:areaZone\\\\:zonePanel');");
        if(StringUtils.isNotBlank(this.editCrptComm.getCrptName())) {
            //验证单位名称+社会信用代码唯一 (全系统唯一）
            int num = this.service.findTbTjCrptCountsByInstitutionCodeAndCrptName(this.editCrptComm.getInstitutionCode().trim(), this.editCrptComm.getCrptName().trim(), this.editCrptComm.getRid(),null);
            if(num >0) {
                JsfUtil.addErrorMessage("同社会信用代码内单位名称不允许重复！");
            }
        }
    }

    public void changeIfSubOrgListener(){
        //切换了是否分支机构，清空上级
        this.editCrptComm.setFkByUpperUnitId(null);
        this.editCrptComm.setInstitutionCode(null);
    }

    /**
     * <p>描述 单位名称和社会信用代码失焦验证 重复性</p>
     *
     * @MethodAuthor gongzhe,2022/7/21 11:31,checkCptNameAndCode
     * @return void
     */
    public void checkCptNameAndCode(){
        if (!StringUtils.verifyCreditCode(ifWeakVerify,this.editCrptComm.getInstitutionCode())) {
            JsfUtil.addErrorMessage("社会信用代码不符合规范！");
        }else if(StringUtils.isNotBlank(this.editCrptComm.getInstitutionCode())) {
            int num = 0;
            if(this.editCrptComm.getIfSubOrg()!=null && this.editCrptComm.getIfSubOrg() == 0){
                //主体机构，验证社会信仰代码唯一
                num = this.service.findTbTjCrptCountsByInstitutionCodeAndCrptName(this.editCrptComm.getInstitutionCode().trim(), null, this.editCrptComm.getRid(),this.editCrptComm.getIfSubOrg());
                if(num >0) {
                    JsfUtil.addErrorMessage("主体机构内社会信用代码不允许重复！");
                }
            }
            if(num == 0 && StringUtils.isNotBlank(this.editCrptComm.getCrptName())){
                //验证单位名称+社会信用代码唯一 (全系统唯一）
                num = this.service.findTbTjCrptCountsByInstitutionCodeAndCrptName(this.editCrptComm.getInstitutionCode().trim(), this.editCrptComm.getCrptName().trim(), this.editCrptComm.getRid(),null);
                if(num >0) {
                    JsfUtil.addErrorMessage("同社会信用代码内单位名称不允许重复！");
                }
            }
        }
    }

    /**
     * <p>方法描述： 健康企业建设情况 选择</p>
     * @MethodAuthor： pw 2023/5/15
     **/
    public void selectSymCodeAction(SymCodeCommPO currentSymCodeCommPO){
        Integer rid = currentSymCodeCommPO.getRid();
        //是否扩展字段1=1
        boolean flag = !CollectionUtils.isEmpty(this.specialHealthRidList) && this.specialHealthRidList.contains(rid);
        if(currentSymCodeCommPO.isIfSelected()){
            this.selectHealthCrptIdList.add(rid);
        }else{
            this.selectHealthCrptIdList.remove(rid);
        }
        for(SymCodeCommPO commPO : this.symCodeList){
            if(CollectionUtils.isEmpty(this.selectHealthCrptIdList)){
                commPO.setSelectAble(true);
            }else if((flag && "1".equals(commPO.getExtendS1())) || (!flag && !"1".equals(commPO.getExtendS1()))){
                commPO.setSelectAble(true);
            }else{
                commPO.setSelectAble(false);
            }
        }
    }

    public DefaultLazyDataModel<TbTjCrpt> getCrptCommList() {
        return crptCommList;
    }

    public void setCrptCommList(DefaultLazyDataModel<TbTjCrpt> crptCommList) {
        this.crptCommList = crptCommList;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchCrptCode() {
        return searchCrptCode;
    }

    public void setSearchCrptCode(String searchCrptCode) {
        this.searchCrptCode = searchCrptCode;
    }

    public TbTjCrpt getSelectCrptComm() {
        return selectCrptComm;
    }

    public void setSelectCrptComm(TbTjCrpt selectCrptComm) {
        this.selectCrptComm = selectCrptComm;
    }

    public TbTjCrpt getEditCrptComm() {
        return editCrptComm;
    }

    public void setEditCrptComm(TbTjCrpt editCrptComm) {
        this.editCrptComm = editCrptComm;
    }

    public List<TsZone> getEditZoneList() {
        return editZoneList;
    }

    public void setEditZoneList(List<TsZone> editZoneList) {
        this.editZoneList = editZoneList;
    }

    public Integer getEditZoneId() {
        return editZoneId;
    }

    public void setEditZoneId(Integer editZoneId) {
        this.editZoneId = editZoneId;
    }

    public String getEditZoneName() {
        return editZoneName;
    }

    public void setEditZoneName(String editZoneName) {
        this.editZoneName = editZoneName;
    }

    public Short getEditZoneType() {
        return editZoneType;
    }

    public void setEditZoneType(Short editZoneType) {
        this.editZoneType = editZoneType;
    }

    public List<TsSimpleCode> getAllList() {
        return allList;
    }

    public void setAllList(List<TsSimpleCode> allList) {
        this.allList = allList;
    }

    public List<TsSimpleCode> getDisplayList() {
        return displayList;
    }

    public void setDisplayList(List<TsSimpleCode> displayList) {
        this.displayList = displayList;
    }

    public List<TsSimpleCode> getFirstList() {
        return firstList;
    }

    public void setFirstList(List<TsSimpleCode> firstList) {
        this.firstList = firstList;
    }

    public String getSearchNamOrPy() {
        return searchNamOrPy;
    }

    public void setSearchNamOrPy(String searchNamOrPy) {
        this.searchNamOrPy = searchNamOrPy;
    }

    public String getSelCodeName() {
        return selCodeName;
    }

    public void setSelCodeName(String selCodeName) {
        this.selCodeName = selCodeName;
    }

    public String getFirstCodeNo() {
        return firstCodeNo;
    }

    public void setFirstCodeNo(String firstCodeNo) {
        this.firstCodeNo = firstCodeNo;
    }

    public TsSimpleCode getSelectPro() {
        return selectPro;
    }

    public void setSelectPro(TsSimpleCode selectPro) {
        this.selectPro = selectPro;
    }

    public List<TsSimpleCode> getCrptsizwList() {
        return crptsizwList;
    }

    public void setCrptsizwList(List<TsSimpleCode> crptsizwList) {
        this.crptsizwList = crptsizwList;
    }

    public Integer getCrptCommId() {
        return crptCommId;
    }

    public void setCrptCommId(Integer crptCommId) {
        this.crptCommId = crptCommId;
    }

    public List<Integer> getEconomyIds() {
        return economyIds;
    }

    public void setEconomyIds(List<Integer> economyIds) {
        this.economyIds = economyIds;
    }

    public List<Integer> getIndusTypeIds() {
        return indusTypeIds;
    }

    public void setIndusTypeIds(List<Integer> indusTypeIds) {
        this.indusTypeIds = indusTypeIds;
    }

    public Integer getSearchZoneId() {
        return searchZoneId;
    }

    public void setSearchZoneId(Integer searchZoneId) {
        this.searchZoneId = searchZoneId;
    }

    public Boolean getIfTjlrCrpt() {
        return ifTjlrCrpt;
    }

    public void setIfTjlrCrpt(Boolean ifTjlrCrpt) {
        this.ifTjlrCrpt = ifTjlrCrpt;
    }

    public Boolean getIfJcCrpt() {
        return ifJcCrpt;
    }

    public void setIfJcCrpt(Boolean ifJcCrpt) {
        this.ifJcCrpt = ifJcCrpt;
    }

    public IndusTypeCodePanelBean getCodePanelBean() {
        return codePanelBean;
    }

    public void setCodePanelBean(IndusTypeCodePanelBean codePanelBean) {
        this.codePanelBean = codePanelBean;
    }

    @Override
    public int getPageSize() {
        return pageSize;
    }

    @Override
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }


    public String getCreateUnitId() {
        return createUnitId;
    }

    public void setCreateUnitId(String createUnitId) {
        this.createUnitId = createUnitId;
    }

    public TbTjCrptIndepend getCrptIndepend() {
        return crptIndepend;
    }

    public void setCrptIndepend(TbTjCrptIndepend crptIndepend) {
        this.crptIndepend = crptIndepend;
    }

    public List<TsSimpleCode> getEconList() {
        return econList;
    }

    public void setEconList(List<TsSimpleCode> econList) {
        this.econList = econList;
    }

    public List<TsSimpleCode> getIndusList() {
        return indusList;
    }

    public void setIndusList(List<TsSimpleCode> indusList) {
        this.indusList = indusList;
    }

    public Boolean getIfShowOtherZone() {
        return ifShowOtherZone;
    }

    public void setIfShowOtherZone(Boolean ifShowOtherZone) {
        this.ifShowOtherZone = ifShowOtherZone;
    }

    public Boolean getIfSearchZoneDefault() {
        return ifSearchZoneDefault;
    }

    public void setIfSearchZoneDefault(Boolean ifSearchZoneDefault) {
        this.ifSearchZoneDefault = ifSearchZoneDefault;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public Boolean getIfEdit() {
        return ifEdit;
    }

    public void setIfEdit(Boolean ifEdit) {
        this.ifEdit = ifEdit;
    }

    public Map<String, TsZone> getZoneMap() {
        return zoneMap;
    }

    public void setZoneMap(Map<String, TsZone> zoneMap) {
        this.zoneMap = zoneMap;
    }

    public List<TsZone> getZoneUpperList() {
        return zoneUpperList;
    }

    public void setZoneUpperList(List<TsZone> zoneUpperList) {
        this.zoneUpperList = zoneUpperList;
    }

    public String getSearchUpperZoneCode() {
        return searchUpperZoneCode;
    }

    public void setSearchUpperZoneCode(String searchUpperZoneCode) {
        this.searchUpperZoneCode = searchUpperZoneCode;
    }

    public String getSearchUpperZoneName() {
        return searchUpperZoneName;
    }

    public void setSearchUpperZoneName(String searchUpperZoneName) {
        this.searchUpperZoneName = searchUpperZoneName;
    }

    public Integer getSearchUpperZoneId() {
        return searchUpperZoneId;
    }

    public void setSearchUpperZoneId(Integer searchUpperZoneId) {
        this.searchUpperZoneId = searchUpperZoneId;
    }

    public String getSearchUpperCrptName() {
        return searchUpperCrptName;
    }

    public void setSearchUpperCrptName(String searchUpperCrptName) {
        this.searchUpperCrptName = searchUpperCrptName;
    }

    public String getSearchUpperCrptCode() {
        return searchUpperCrptCode;
    }

    public void setSearchUpperCrptCode(String searchUpperCrptCode) {
        this.searchUpperCrptCode = searchUpperCrptCode;
    }

    public DefaultLazyDataModel<TbTjCrpt> getCrptUpperList() {
        return crptUpperList;
    }

    public void setCrptUpperList(DefaultLazyDataModel<TbTjCrpt> crptUpperList) {
        this.crptUpperList = crptUpperList;
    }

    public TbTjCrpt getSelectCrptUpper() {
        return selectCrptUpper;
    }

    public void setSelectCrptUpper(TbTjCrpt selectCrptUpper) {
        this.selectCrptUpper = selectCrptUpper;
    }

    public Boolean getIfCheck() {
        return ifCheck;
    }

    public void setIfCheck(Boolean ifCheck) {
        this.ifCheck = ifCheck;
    }

    public Boolean getIfLink() {
        return ifLink;
    }

    public void setIfLink(Boolean ifLink) {
        this.ifLink = ifLink;
    }

    public Boolean getIfJcLcCrpt() {
        return ifJcLcCrpt;
    }

    public void setIfJcLcCrpt(Boolean ifJcLcCrpt) {
        this.ifJcLcCrpt = ifJcLcCrpt;
    }

    public Boolean getIfGs() {
        return ifGs;
    }

    public void setIfGs(Boolean ifGs) {
        this.ifGs = ifGs;
    }

    public Integer getAreaValidate() {
        return areaValidate;
    }

    public void setAreaValidate(Integer areaValidate) {
        this.areaValidate = areaValidate;
    }

    public Boolean getIfTj() {
        return ifTj;
    }

    public void setIfTj(Boolean ifTj) {
        this.ifTj = ifTj;
    }

    public Boolean getIfShowHealthCrpt() {
        return ifShowHealthCrpt;
    }

    public void setIfShowHealthCrpt(Boolean ifShowHealthCrpt) {
        this.ifShowHealthCrpt = ifShowHealthCrpt;
    }

    public List<TsSimpleCode> getHealthCrptSimpleCodeList() {
        return healthCrptSimpleCodeList;
    }

    public void setHealthCrptSimpleCodeList(List<TsSimpleCode> healthCrptSimpleCodeList) {
        this.healthCrptSimpleCodeList = healthCrptSimpleCodeList;
    }

    public List<SymCodeCommPO> getSymCodeList() {
        return symCodeList;
    }

    public void setSymCodeList(List<SymCodeCommPO> symCodeList) {
        this.symCodeList = symCodeList;
    }

    public List<Integer> getSelectHealthCrptIdList() {
        return selectHealthCrptIdList;
    }

    public void setSelectHealthCrptIdList(List<Integer> selectHealthCrptIdList) {
        this.selectHealthCrptIdList = selectHealthCrptIdList;
    }

    public List<Integer> getSpecialHealthRidList() {
        return specialHealthRidList;
    }

    public void setSpecialHealthRidList(List<Integer> specialHealthRidList) {
        this.specialHealthRidList = specialHealthRidList;
    }

    public List<List<SymCodeCommPO>> getGroupSymCodeList() {
        return groupSymCodeList;
    }

    public void setGroupSymCodeList(List<List<SymCodeCommPO>> groupSymCodeList) {
        this.groupSymCodeList = groupSymCodeList;
    }

    public String getExcludeRids() {
        return excludeRids;
    }

    public void setExcludeRids(String excludeRids) {
        this.excludeRids = excludeRids;
    }

    public boolean isIfWeakVerify() {
        return ifWeakVerify;
    }

    public void setIfWeakVerify(boolean ifWeakVerify) {
        this.ifWeakVerify = ifWeakVerify;
    }

    public Boolean getIfActiveMonitoringTask() {
        return ifActiveMonitoringTask;
    }

    public void setIfActiveMonitoringTask(Boolean ifActiveMonitoringTask) {
        this.ifActiveMonitoringTask = ifActiveMonitoringTask;
    }
}
