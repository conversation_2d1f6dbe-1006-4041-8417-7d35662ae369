package com.chis.modules.heth.comm.rptvo;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.rptvo.ZwxFieldDoc;

import java.util.List;

/**
 *  <p>类描述：接触有害因素类型-岗位</p>
 * @ClassAuthor hsj 2021/10/22 18:52
 */
public class BadRsnTypeVo {
    @ZwxFieldDoc(value = "接触有害因素类型")
    private TsSimpleCode badrsnType;

    @ZwxFieldDoc(value = "接触人数")
    private Integer touchNum;

    @ZwxFieldDoc(value = "岗位信息")
    private List<PostVo> postVos;

    public TsSimpleCode getBadrsnType() {
        return badrsnType;
    }

    public void setBadrsnType(TsSimpleCode badrsnType) {
        this.badrsnType = badrsnType;
    }

    public Integer getTouchNum() {
        return touchNum;
    }

    public void setTouchNum(Integer touchNum) {
        this.touchNum = touchNum;
    }

    public List<PostVo> getPostVos() {
        return postVos;
    }

    public void setPostVos(List<PostVo> postVos) {
        this.postVos = postVos;
    }
}
