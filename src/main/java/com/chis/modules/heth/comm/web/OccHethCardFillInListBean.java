package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.enumn.ReturnType;
import com.chis.modules.heth.comm.json.CrptDialogParamJson;
import com.chis.modules.heth.comm.rptvo.SrvorgPsnVo;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.WordReportDTO;
import com.chis.modules.system.logic.WordReportJson;
import com.chis.modules.system.utils.Global;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.text.Collator;
import java.util.*;

/**
 * 职业卫生技术服务信息报送卡填报
 *
 * <AUTHOR>
 * @version 1.0
 */
@ManagedBean(name = "occHethCardFillInListBean")
@ViewScoped
public class OccHethCardFillInListBean extends AbstractOccHethCardBean {
    private static final long serialVersionUID = 1L;

    /**
     * 编辑页面：技术服务地址地区
     */
    private List<TsZone> editZoneList = new ArrayList<>();
    private Map<Integer, TsZone> editZoneMap = new HashMap<>(16);
    /**
     * 承担的服务事项码表
     */
    private List<TsSimpleCode> servicesUndertakenList = new ArrayList<>();
    /**
     * 预览文书文件地址
     */
    private String reportFilePath;

    /******************职业病危害因素检测***************************/
    /**
     * 1:职业病危害因素检测  2:职业病危害现状评价
     */
    private Integer type;
    /**
     * 危害因素大类 码表
     */
    private List<TsSimpleCode> firsBadRsntList;
    /**
     * 危害因素列表
     */
    private List<Object[]> limitVals;
    /**
     * 选中的危害因素
     */
    private Object[] selectBadRsn;
    /**
     * 危害因素选择框--查询条件 危害因素大类rid
     */
    private Integer firstBadRsnId;
    /**
     * 危害因素选择框--查询条件 名称/拼音码
     */
    private String searchNamOrPy;
    /**
     * 超标检测项目 下拉
     */
    private List<Object[]> itemList2;
    /**
     * 计量单位特殊值
     */
    private TreeNode msruntTree;
    /**
     * 选择的危害因素
     */
    private List<Object[]> selectBadRsns;
    /**
     * 修改子表 对象
     */
    private TdZwOcchethCardJcSub jcSub;
    /**
     * 子表 集合
     */
    private List<TdZwOcchethCardJcSub> jcSubList = new ArrayList<>();
    /******************职业病危害现状评价***************************/
    /**
     * 选中的危害因素
     */
    private Object[] selectPjBadRsn;
    /**
     * 选择的危害因素
     */
    private List<Object[]> selectPjBadRsns;
    /**
     * 修改子表 对象
     */
    private TdZwOcchethCardPjSub pjSub;
    /**
     * 子表 集合
     */
    private List<TdZwOcchethCardPjSub> pjSubList = new ArrayList<>();

    private Integer postNumF;
    private Integer numF;

    /**危害因素map key TB_YSJC_LIMIT_VAL.RID value : 码表 rid*/
    private Map<Integer,String> limitValMap ;


    public OccHethCardFillInListBean() {
        this.init();
        this.initSimpleCode();
        this.initLimitVal();
        String id =JsfUtil.getRequest().getParameter("rid");
        if(ObjectUtil.isNotEmpty(id)){
            this.mainRid = Integer.valueOf(id);
            this.viewInit();
            this.forwardOtherPage();
        }else{
            this.searchAction();
        }
    }

    /**
     *  <p>方法描述：TB_YSJC_LIMIT_VAL</p>
     * @MethodAuthor hsj 2023-11-25 16:33 
     */
    private void initLimitVal() {
        this.limitValMap = new HashMap<>();
        String hql ="SELECT t FROM TbYsjcLimitValComm t";
        List<TbYsjcLimitValComm> limitValCommList = this.commService.findDataByHqlNoPage(hql,null);
        if(CollectionUtils.isEmpty(limitValCommList)){
            return;
        }
        for(TbYsjcLimitValComm l : limitValCommList){
            this.limitValMap.put(l.getRid(),l.getFkByBadrsnId().getRid().toString());
        }
    }

    private void init() {
        //服务单位地区
        this.searchZoneList = this.commService.findZoneListByGbAndTypeNoNation("", true, "", "");
        //技术服务地址地区
        this.editZoneList = this.commService.findZoneListByGbAndTypeNoNation("", true, "", "4");
        for (TsZone zone : this.editZoneList) {
            this.editZoneMap.put(zone.getRid(), zone);
        }
        //状态
        this.searchStateList = new ArrayList<>();
        this.searchStateList.add(0);
        this.ifSupportCancel = Boolean.TRUE;
    }

    private void initSimpleCode() {
        //承担的服务事项
        this.servicesUndertakenList = this.commService.findNumSimpleCodesByTypeId("5577");
        addSimpleCodeMapByList(this.servicesUndertakenList);
        //危害因素大类
        firsBadRsntList = this.commService.findNumSimpleCodesByTypeId("5517");
        //用于对照-需查询所有
        List<TsSimpleCode> simpleCodes = this.commService.findallSimpleCodesByTypeIdOrderByNum("5517");
        addSimpleCodeMapByList(simpleCodes);
    }

    @Override
    public void searchAction() {
        super.searchAction();
    }

    @Override
    public String[] buildHqls() {
        Integer unitId = Global.getUser().getTsUnit().getRid();
        StringBuilder baseSql = new StringBuilder();
        baseSql.append("SELECT T.RID, CASE WHEN Z.ZONE_TYPE > 2 THEN SUBSTR(Z.FULL_NAME, INSTR(Z.FULL_NAME, '_') + 1) ELSE Z.FULL_NAME END ZONE_NAME, T.CRPT_NAME, T.RPT_DATE, T.STATE, Z.ZONE_GB ");
        baseSql.append("FROM TD_ZW_OCCHETH_CARD T ");
        baseSql.append("LEFT JOIN TS_ZONE Z ON T.ZONE_ID = Z.RID ");
        baseSql.append("WHERE NVL(T.DEL_MARK, 0) = 0 ");
        // 当前单位填报
        baseSql.append(" AND T.FILL_UNIT_ID = :fillUnitId ");
        this.paramMap.put("fillUnitId", unitId);
        // 服务单位地区
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            baseSql.append(" AND Z.ZONE_GB LIKE :zoneCode escape '\\\' ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneGb)) + "%");
        }
        // 服务单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            baseSql.append(" AND T.CRPT_NAME LIKE :searchCrptName escape '\\\' ");
            this.paramMap.put("searchCrptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        // 出具技术服务报告时间
        if (ObjectUtil.isNotEmpty(this.searchRptBeginDate)) {
            baseSql.append(" AND T.RPT_DATE >= TO_DATE(:searchRptBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchRptBeginDate", DateUtils.formatDate(this.searchRptBeginDate) + " 00:00:00");
        }
        if (ObjectUtil.isNotEmpty(this.searchRptEndDate)) {
            baseSql.append(" AND T.RPT_DATE <= TO_DATE(:searchRptEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchRptEndDate", DateUtils.formatDate(this.searchRptEndDate) + " 23:59:59");
        }
        // 状态
        if (ObjectUtil.isNotEmpty(this.searchStateList)) {
            baseSql.append(" AND T.STATE IN (:searchStateList)");
            this.paramMap.put("searchStateList", this.searchStateList);
        }
        String sql1 = "SELECT * FROM (" + baseSql + ")AA ORDER BY AA.RPT_DATE DESC, AA.ZONE_GB, AA.CRPT_NAME, AA.RID DESC";
        String sql2 = "SELECT COUNT(1) FROM (" + baseSql + ")";
        return new String[]{sql1, sql2};
    }

    public void deleteAction() {
        if (this.mainRid == null) {
            return;
        }
        try {
            this.occhethCardService.deleteOcchethCardByRid(this.mainRid);
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
        this.searchAction();
    }

    /**
     * 添加前验证
     */
    public void addBeforeVerify() {
        if (this.occhethCardService.ifTdZwOcchethInfoComplete(Global.getUser().getTsUnit().getRid())) {
            super.addInitAction();
        } else {
            JsfUtil.addErrorMessage("请完善并提交【职业卫生技术服务机构资质】信息！");
        }
    }

    @Override
    public void addInit() {
        this.occhethCard = new TdZwOcchethCard();
        this.occhethCard.setSource(0);
        //初始化-机构信息
        Object[] occhethBaseInfo = this.occhethCardService.findTdZwOcchethBaseInfo(Global.getUser().getTsUnit().getRid());
        String occhethInfoRid;
        if (ObjectUtil.isNotEmpty(occhethBaseInfo)) {
            occhethInfoRid = StringUtils.objectToString(occhethBaseInfo[0]);
            //初始化-机构信息
            this.occhethCard.setOrgId(occhethInfoRid);
            this.occhethCard.setOrgName(StringUtils.objectToString(occhethBaseInfo[1]));
            this.occhethCard.setOrgFz(StringUtils.objectToString(occhethBaseInfo[2]));
            this.occhethCard.setOrgAddr(StringUtils.objectToString(occhethBaseInfo[3]));
            this.occhethCard.setCertNo(StringUtils.objectToString(occhethBaseInfo[4]));
            //初始化-报告信息-填报单位信息
            this.occhethCard.setFillUnitName(StringUtils.objectToString(occhethBaseInfo[1]));
            this.occhethCard.setOrgFzMan(StringUtils.objectToString(occhethBaseInfo[5]));
            this.occhethCard.setOrgZoneGb(StringUtils.objectToString(occhethBaseInfo[6]));
            //初始化-机构信息-项目负责人信息
            Object[] occhethPsnInfo = this.occhethCardService.findOcchethPsnInfo(occhethInfoRid);
            if (ObjectUtil.isNotEmpty(occhethPsnInfo)) {
                this.occhethCard.setProFz(StringUtils.objectToString(occhethPsnInfo[0]));
                this.occhethCard.setProLinktel(StringUtils.objectToString(occhethPsnInfo[1]));
            }
            //初始化-机构信息-资质业务范围
            List<TdZwOcchethCardItems> occhethCardItemsList = new ArrayList<>();
            List<Object> occhethItemsRidList = this.occhethCardService.findOcchethItemsRidList(occhethInfoRid);
            for (Object object : occhethItemsRidList) {
                if (ObjectUtil.isNotEmpty(object) && this.simpleCodeMap.containsKey(StringUtils.objectToString(object))) {
                    TdZwOcchethCardItems occhethCardItems = new TdZwOcchethCardItems();
                    occhethCardItems.setFkByMainId(this.occhethCard);
                    occhethCardItems.setFkByItemId(this.simpleCodeMap.get(StringUtils.objectToString(object)));
                    occhethCardItems.setCreateDate(new Date());
                    occhethCardItems.setCreateManid(Global.getUser().getRid());
                    occhethCardItemsList.add(occhethCardItems);
                }
            }
            this.occhethCard.setOcchethCardItemsList(occhethCardItemsList);
        }
        //初始化-技术服务信息-出具技术服务报告时间
        this.occhethCard.setRptDate(new Date());
        //初始化-报告信息-填报单位信息
        this.occhethCard.setFkByFillUnitId(Global.getUser().getTsUnit());
        this.occhethCard.setFillFormPsn(Global.getUser().getUsername());
        this.occhethCard.setFillLink(Global.getUser().getMbNum());
        this.occhethCard.setFillDate(new Date());

        publicInit();
        //职业病危害因素检测
        tdZwOcchethCardJc = new TdZwOcchethCardJc();
        //职业病危害现状评价
        tdZwOcchethCardPj = new TdZwOcchethCardPj();
    }

    @Override
    public void modInit() {
        this.occhethCard = this.occhethCardService.findTdZwOcchethCardByRid(this.mainRid);

        //初0始化-机构信息
        Object[] occhethBaseInfo = this.occhethCardService.findTdZwOcchethBaseInfo(Global.getUser().getTsUnit().getRid());
        String occhethInfoRid;
        if (ObjectUtil.isNotEmpty(occhethBaseInfo)) {
            occhethInfoRid = StringUtils.objectToString(occhethBaseInfo[0]);
            //初始化-机构信息
            this.occhethCard.setOrgId(occhethInfoRid);
        }
        //处理-参与人员信息
        this.occhethCard.setOcchethCardPsnRidList(new ArrayList<String>());
        for (TdZwOcchethCardPsn occhethCardPsn : this.occhethCard.getOcchethCardPsnList()) {
            if (ObjectUtil.isEmpty(occhethCardPsn.getFkByPsnId()) || ObjectUtil.isEmpty(occhethCardPsn.getFkByPsnId().getRid())) {
                continue;
            }
            this.occhethCard.getOcchethCardPsnRidList().add(occhethCardPsn.getFkByPsnId().getRid().toString());
            for (TdZwOcchethCardItem occhethCardItem : occhethCardPsn.getOcchethCardItemList()) {
                if (ObjectUtil.isEmpty(occhethCardItem.getFkByItemId())) {
                    continue;
                }
                occhethCardPsn.getOcchethCardItemSimpleCodeList().add(occhethCardItem.getFkByItemId().getRid().toString());
            }
        }
        //处理-服务的用人单位信息-技术服务地址
        for (TdZwOcchethCardZone occhethCardZone : this.occhethCard.getOcchethCardZoneList()) {
            if (ObjectUtil.isEmpty(occhethCardZone.getFkByZoneId())) {
                continue;
            }
            TsZone zone = occhethCardZone.getFkByZoneId();
            occhethCardZone.setZoneRid(zone.getRid());
            occhethCardZone.setZoneName(zone.getZoneName());
            occhethCardZone.setZoneGb(zone.getZoneGb());
        }
        //处理-技术服务信息-技术服务领域
        this.occhethCard.setOcchethCardServiceSimpleCodeList(new ArrayList<String>());
        for (TdZwOcchethCardService occhethCardService : this.occhethCard.getOcchethCardServiceList()) {
            if (ObjectUtil.isEmpty(occhethCardService.getFkByServiceAreaId())) {
                continue;
            }
            this.occhethCard.getOcchethCardServiceSimpleCodeList().add(occhethCardService.getFkByServiceAreaId().getRid().toString());
        }

        dealTechnicalServiceResults();
        publicInit();

        if (0 == this.occhethCard.getState() && StringUtils.isNotBlank(this.occhethCard.getAnnexPath())) {
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("disabledInput('true','occHethCard')");
        }
    }

    @Override
    public void viewInit() {
        if(null != this.mainRid){
            this.ifSupportCancel = this.occhethCardService.queryRcdCountByParams(12,this.mainRid, 3) == 0;
        }
     super.viewInit();
    }



    @Override
    public void saveAction() {
        OcchethCardJcSort();
        computeRowsSpan();
        OcchethCardPjSort();
        computePjRowsSpan();
        if (verifyFailed(false)) {
            return;
        }
        try {
            saveOcchethCard(0);
            JsfUtil.addSuccessMessage("暂存成功！");
            if (ObjectUtil.isNotEmpty(this.occhethCard.getAnnexPath())) {
                RequestContext.getCurrentInstance().execute("disabledInput('true','occHethCard')");
            }
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("暂存失败！");
        }
    }

    public void checkSubmitAction() {
        if (verifyFailed(true)) {
            return;
        }
        boolean flag=false;
        if (StringUtils.isBlank(this.occhethCard.getAnnexPath())) {
            JsfUtil.addErrorMessage("请先上传《职业卫生技术服务信息报送卡》文书！");
            flag=true;
        }
        if (StringUtils.isBlank(this.occhethCard.getSignAddress())) {
            JsfUtil.addErrorMessage("请先上传《职业卫生技术服务报告首页、签发页》文书！");
            flag=true;
        }
        if(flag){
            return;
        }
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("PF('ConfirmDialog').show();");
    }

    public void submitAction() {
        try {
            saveOcchethCard(1);
            JsfUtil.addSuccessMessage("提交成功！");
        } catch (Exception e) {
            this.occhethCard.setState(0);
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
        try {
            this.viewInitAction();
            RequestContext.getCurrentInstance().update("tabView");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("跳转详情页面失败！");
        }
    }

    /**
     * 保存主表
     *
     * @param state 主表状态
     */
    public void saveOcchethCard(int state) {
        saveOcchethCardBefore();
        this.occhethCard.setState(state);
        this.occhethCardService.saveOcchethCard(this.occhethCard);
        this.mainRid = this.occhethCard.getRid();
    }

    /**
     * 保存/提交验证是否不通过
     *
     * @param isSubmit 是否提交操作
     * @return boolean true 验证不通过;false 验证通过
     */
    private boolean verifyFailed(boolean isSubmit) {
        boolean verifyFailed = false;
        //项目负责人
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getProFz())) {
            JsfUtil.addErrorMessage("项目负责人不能为空");
            verifyFailed = true;
        }
        //联系电话
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getProLinktel())) {
            JsfUtil.addErrorMessage("联系电话不能为空");
            verifyFailed = true;
        }
        if (ObjectUtil.isNotEmpty(this.occhethCard.getProLinktel())
                && !StringUtils.vertyPhone(this.occhethCard.getProLinktel())) {
            JsfUtil.addErrorMessage("联系电话格式错误！");
            verifyFailed = true;
        }
        //参与人员信息
        if (isSubmit) {
            if (ObjectUtil.isEmpty(this.occhethCard.getOcchethCardPsnList())) {
                JsfUtil.addErrorMessage("请至少添加一条参与人员信息！");
                verifyFailed = true;
            } else {
                //承担的服务事项
                for (TdZwOcchethCardPsn occhethCardPsn : this.occhethCard.getOcchethCardPsnList()) {
                    if (ObjectUtil.isEmpty(occhethCardPsn.getOcchethCardItemSimpleCodeList())) {
                        JsfUtil.addErrorMessage(occhethCardPsn.getPsnName() + "承担的服务事项不能为空！");
                        verifyFailed = true;
                    }
                }
            }
        }
        //服务的用人单位
        if (ObjectUtil.isEmpty(this.occhethCard.getFkByCrptId())) {
            JsfUtil.addErrorMessage("请选择服务的用人单位！");
            verifyFailed = true;
        }
        //技术服务地址
        if (isSubmit) {
            List<TdZwOcchethCardZone> occhethCardZoneList = this.occhethCard.getOcchethCardZoneList();
            for (int i = 0; i < occhethCardZoneList.size(); i++) {
                TdZwOcchethCardZone occhethCardZone = occhethCardZoneList.get(i);
                if (ObjectUtil.isEmpty(occhethCardZone.getZoneRid())) {
                    JsfUtil.addErrorMessage("第" + (i + 1) + "行技术服务地址不能为空！");
                    verifyFailed = true;
                }
            }
        }
        //技术服务领域
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getOcchethCardServiceSimpleCodeList())) {
            JsfUtil.addErrorMessage("请选择技术服务领域！");
            verifyFailed = true;
        }
        //现场调查时间
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getInvestStartDate())) {
            JsfUtil.addErrorMessage("请选择现场调查开始时间！");
            verifyFailed = true;
        }
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getInvestEndDate())) {
            JsfUtil.addErrorMessage("请选择现场调查结束时间！");
            verifyFailed = true;
        }
        //现场采样/检测时间
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getJcStartDate())) {
            JsfUtil.addErrorMessage("请选择现场采样/检测开始时间！");
            verifyFailed = true;
        }
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getJcEndDate())) {
            JsfUtil.addErrorMessage("请选择现场采样/检测结束时间！");
            verifyFailed = true;
        }
        //出具技术服务报告时间
        if (ObjectUtil.isEmpty(this.occhethCard.getRptDate())) {
            JsfUtil.addErrorMessage("请选择出具技术服务报告时间！");
            verifyFailed = true;
        } else {
            if (ObjectUtil.isNotEmpty(this.occhethCard.getInvestEndDate())
                    && DateUtils.isCompareDate(this.occhethCard.getRptDate(), "<", this.occhethCard.getInvestEndDate())) {
                JsfUtil.addErrorMessage("出具技术服务报告时间应大于等于现场调查结束时间！");
                verifyFailed = true;
            }
            if (ObjectUtil.isNotEmpty(this.occhethCard.getJcEndDate())
                    && DateUtils.isCompareDate(this.occhethCard.getRptDate(), "<", this.occhethCard.getJcEndDate())) {
                JsfUtil.addErrorMessage("出具技术服务报告时间应大于等于现场采样/检测结束日期！");
                verifyFailed = true;
            }
        }

        if (occhethCard.getHasBadrsnJc() && CollectionUtils.isEmpty(occhethCard.getOcchethCardJcList())) {
            JsfUtil.addErrorMessage("请填写职业病危害因素检测信息！");
            verifyFailed = true;
        }
        if (occhethCard.getHasStatusPj() && CollectionUtils.isEmpty(occhethCard.getOcchethCardPjList())) {
            JsfUtil.addErrorMessage("请填写职业病危害现状评价信息！");
            verifyFailed = true;
        }

        if (isSubmit && occhethCard.getHasBadrsnJc() && !CollectionUtils.isEmpty(occhethCard.getOcchethCardJcList())) {
            for (TdZwOcchethCardJc cardJc : occhethCard.getOcchethCardJcList()) {
                if (cardJc.getPostNum() == null) {
                    JsfUtil.addErrorMessage("职业病危害因素检测中"+cardJc.getFkByBadrsnId().getRsnCnName()+"的信息不完整！");
                    verifyFailed = true;
                }
            }
        }
        if (isSubmit && occhethCard.getHasStatusPj() && !CollectionUtils.isEmpty(occhethCard.getOcchethCardPjList())) {
            for (TdZwOcchethCardPj cardPj : occhethCard.getOcchethCardPjList()) {
                if (cardPj.getPostNum() == null) {
                    JsfUtil.addErrorMessage("职业病危害现状评价中"+cardPj.getFkByBadrsnId().getRsnCnName()+"的信息不完整！");
                    verifyFailed = true;
                }
            }
        }
        //技术服务报告编号
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getRptNo())) {
            JsfUtil.addErrorMessage("技术服务报告编号不能为空！");
            verifyFailed = true;
        }
        //技术服务结果
        if (isSubmit && !this.occhethCard.getHasBadrsnJc() && !this.occhethCard.getHasStatusPj() && !this.occhethCard.getHasInstUsePj()) {
            JsfUtil.addErrorMessage("技术服务结果至少选择一项！");
            verifyFailed = true;
        }

        if (isSubmit && this.occhethCard.getHasInstUsePj() && !this.occhethCard.getHasJcInst() && !this.occhethCard.getHasJcUse()) {
            JsfUtil.addErrorMessage("职业病防护设备设施与防护用品的效果评价至少选择一项！");
            verifyFailed = true;
        }

        if (isSubmit && this.occhethCard.getHasInstUsePj() && this.occhethCard.getHasJcInst() && occhethCard.getJcInstNum() == null) {
            JsfUtil.addErrorMessage("检测设备设施数量不能为空！");
            verifyFailed = true;
        }

        if (isSubmit && this.occhethCard.getHasInstUsePj() && this.occhethCard.getHasJcInst() && this.occhethCard.getJcNotHgInstNum() == null) {
            JsfUtil.addErrorMessage("检测结果不合格的设备设施数量不能为空！");
            verifyFailed = true;
        }
        if (this.occhethCard.getHasInstUsePj() && this.occhethCard.getHasJcInst() && occhethCard.getJcInstNum() != null && this.occhethCard.getJcNotHgInstNum() != null && occhethCard.getJcInstNum() < this.occhethCard.getJcNotHgInstNum()) {
            JsfUtil.addErrorMessage("检测设备设施数量应大于等于检测结果不合格的设备设施数量！");
            verifyFailed = true;
        }
        if (isSubmit && this.occhethCard.getHasInstUsePj()
                && this.occhethCard.getHasJcInst()
                && this.occhethCard.getJcNotHgInstNum() != null
                && this.occhethCard.getJcNotHgInstNum() > 0
                && StringUtils.isBlank(occhethCard.getNotHgInstName())) {
            JsfUtil.addErrorMessage("不合格的设备设施名称不能为空！");
            verifyFailed = true;
        }

        if (isSubmit && this.occhethCard.getHasJcUse() && this.occhethCard.getHasInstUsePj() && occhethCard.getJcUseNum() == null) {
            JsfUtil.addErrorMessage("检测防护用品数量不能为空！");
            verifyFailed = true;
        }

        if (isSubmit && this.occhethCard.getHasJcUse() && this.occhethCard.getHasInstUsePj() && this.occhethCard.getJcNotHgUseNum() == null) {
            JsfUtil.addErrorMessage("结果不合格的防护用品数量不能为空！");
            verifyFailed = true;
        }
        if (this.occhethCard.getHasInstUsePj() && this.occhethCard.getHasJcUse() && occhethCard.getJcUseNum() != null && this.occhethCard.getJcNotHgUseNum() != null && occhethCard.getJcUseNum() < this.occhethCard.getJcNotHgUseNum()) {
            JsfUtil.addErrorMessage("检测防护用品数量应大于等于结果不合格的防护用品数量！");
            verifyFailed = true;
        }
        if (isSubmit && this.occhethCard.getHasInstUsePj()
                && this.occhethCard.getHasJcUse()
                && this.occhethCard.getJcNotHgUseNum() != null
                && this.occhethCard.getJcNotHgUseNum() > 0
                && StringUtils.isBlank(occhethCard.getNotHgUseName())) {
            JsfUtil.addErrorMessage("不合格的防护用品名称不能为空！");
            verifyFailed = true;
        }

        //单位负责人
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getOrgFzMan())) {
            JsfUtil.addErrorMessage("单位负责人不能为空！");
            verifyFailed = true;
        }
        //填表人
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getFillFormPsn())) {
            JsfUtil.addErrorMessage("填表人不能为空！");
            verifyFailed = true;
        }
        //填表人联系电话
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getFillLink())) {
            JsfUtil.addErrorMessage("填表人联系电话不能为空！");
            verifyFailed = true;
        }
        if (ObjectUtil.isNotEmpty(this.occhethCard.getFillLink())
                && !StringUtils.vertyPhone(this.occhethCard.getFillLink())) {
            JsfUtil.addErrorMessage("填表人联系电话格式错误！");
            verifyFailed = true;
        }
        //填表日期
        if (isSubmit && ObjectUtil.isEmpty(this.occhethCard.getFillDate())) {
            JsfUtil.addErrorMessage("请选择填表日期！");
            verifyFailed = true;
        }
        if (ObjectUtil.isNotEmpty(this.occhethCard.getFillDate()) && ObjectUtil.isNotEmpty(this.occhethCard.getRptDate())
                && DateUtils.isCompareDate(this.occhethCard.getFillDate(), "<", this.occhethCard.getRptDate())) {
            JsfUtil.addErrorMessage("填表日期应大于等于出具技术服务报告时间！");
            verifyFailed = true;
        }
        return verifyFailed;
    }

    /**
     * 保存前处理报送卡实体操作
     */
    private void saveOcchethCardBefore() {
        //报告卡编码
        //ZYWSJC+职业卫生技术服务机构地区编码10位+年月日+5位流水
        if (ObjectUtil.isEmpty(this.occhethCard.getCardNo())) {
            String aa = this.commService.getAutoCode("OCCHETH_CARD_CODE", null);
            String cardNo = "ZYWSJC" + this.occhethCard.getOrgZoneGb() + DateUtils.formatDate(new Date(), "yyyyMMdd") + aa;
            this.occhethCard.setCardNo(cardNo);
        }
        //处理-参与人员信息
        List<TdZwOcchethCardPsn> occhethCardPsnList = this.occhethCard.getOcchethCardPsnList();
        //姓名排序
        Collections.sort(occhethCardPsnList, new Comparator<TdZwOcchethCardPsn>() {
            @Override
            public int compare(TdZwOcchethCardPsn p1, TdZwOcchethCardPsn p2) {
                Collator com = Collator.getInstance(Locale.CHINA);
                return com.compare(p1.getPsnName(), p2.getPsnName());
            }
        });
        for (TdZwOcchethCardPsn occhethCardPsn : occhethCardPsnList) {
            occhethCardPsn.setOcchethCardItemList(new ArrayList<TdZwOcchethCardItem>());
            for (String simpleCodeRid : occhethCardPsn.getOcchethCardItemSimpleCodeList()) {
                if (this.simpleCodeMap.containsKey(simpleCodeRid)) {
                    TdZwOcchethCardItem occhethCardItem = new TdZwOcchethCardItem();
                    occhethCardItem.setFkByMainId(occhethCardPsn);
                    occhethCardItem.setFkByItemId(this.simpleCodeMap.get(simpleCodeRid));
                    occhethCardItem.setCreateDate(new Date());
                    occhethCardItem.setCreateManid(Global.getUser().getRid());
                    occhethCardPsn.getOcchethCardItemList().add(occhethCardItem);
                }
            }
        }
        //处理-服务的用人单位信息-技术服务地址
        //地区排序
        List<TdZwOcchethCardZone> occhethCardZoneList = this.occhethCard.getOcchethCardZoneList();
        for (TdZwOcchethCardZone occhethCardZone : occhethCardZoneList) {
            if (ObjectUtil.isEmpty(occhethCardZone.getFkByZoneId()) || ObjectUtil.isEmpty(occhethCardZone.getZoneRid())) {
                occhethCardZone.setFkByZoneId(null);
            }
        }
        Collections.sort(occhethCardZoneList, new Comparator<TdZwOcchethCardZone>() {
            @Override
            public int compare(TdZwOcchethCardZone p1, TdZwOcchethCardZone p2) {
                Collator com = Collator.getInstance(Locale.CHINA);
                if (ObjectUtil.isEmpty(p1.getZoneGb())) {
                    return 1;
                } else if (ObjectUtil.isEmpty(p2.getZoneGb())) {
                    return -1;
                }
                return com.compare(p1.getZoneGb(), p2.getZoneGb());
            }
        });

        //处理-技术服务信息-技术服务领域
        this.occhethCard.setOcchethCardServiceList(new ArrayList<TdZwOcchethCardService>());
        for (String simpleCodeRid : this.occhethCard.getOcchethCardServiceSimpleCodeList()) {
            if (this.simpleCodeMap.containsKey(simpleCodeRid)) {
                TdZwOcchethCardService occhethCardService = new TdZwOcchethCardService();
                occhethCardService.setFkByMainId(this.occhethCard);
                occhethCardService.setFkByServiceAreaId(this.simpleCodeMap.get(simpleCodeRid));
                occhethCardService.setCreateDate(new Date());
                occhethCardService.setCreateManid(Global.getUser().getRid());
                this.occhethCard.getOcchethCardServiceList().add(occhethCardService);
            }
        }
        //处理-技术服务结果
        this.occhethCard.setIfBadrsnJc(this.occhethCard.getHasBadrsnJc() ? 1 : 0);
        this.occhethCard.setIfStatusPj(this.occhethCard.getHasStatusPj() ? 1 : 0);
        this.occhethCard.setIfInstUsePj(this.occhethCard.getHasInstUsePj() ? 1 : 0);

        this.occhethCard.setIfJcInst(this.occhethCard.getHasJcInst() ? 1 : 0);
        this.occhethCard.setIfJcUse(this.occhethCard.getHasJcUse() ? 1 : 0);

        //职业病危害因素检测
        if(!occhethCard.getHasBadrsnJc()){
            occhethCard.getOcchethCardJcList().clear();
        }
        Integer  jcPostNum = 0;
        Integer  jcOverNum = 0;
        Set<String> jcSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(occhethCard.getOcchethCardJcList())) {
            for (TdZwOcchethCardJc cardJc : occhethCard.getOcchethCardJcList()) {
                //cardJc.setRid(null);
                //cardJc.setFkByMainId(occhethCard);
                if (!CollectionUtils.isEmpty(cardJc.getOcchethCardJcSubList())) {
                    for (TdZwOcchethCardJcSub jcSub : cardJc.getOcchethCardJcSubList()) {
                        //jcSub.setRid(null);
                        jcSub.setFkByMainId(cardJc);
                        if (jcSub.getIndexRid() == null) {
                            jcSub.setFkByIndexId(null);
                        }
                        if (jcSub.getFkByItemId() != null && jcSub.getFkByItemId().getRid() != null) {
                            jcSub.getFkByItemId().setFkByMainId(new TbYsjcLimitValComm(cardJc.getFkByBadrsnId().getRid()));
                        } else {
                            jcSub.setFkByItemId(null);
                        }
                        if (jcSub.getFkByMsruntId() == null || jcSub.getFkByMsruntId().getRid() == null) {
                            jcSub.setFkByMsruntId(null);
                        }
                        jcSub.setCreateDate(new Date());
                        jcSub.setCreateManid(Global.getUser().getRid());
                    }
                }
                jcPostNum += (cardJc.getPostNum() == null ? 0 : cardJc.getPostNum() );
                jcOverNum += (cardJc.getNum() == null ? 0 : cardJc.getNum() );
                if(cardJc.getNum() != null && cardJc.getNum() > 0){
                    jcSet.add(this.limitValMap.get(cardJc.getFkByBadrsnId().getRid()));
                }
            }
        }
        occhethCard.setJcPostNum(jcPostNum);
        occhethCard.setJcOverNum(jcOverNum);
        List<TdZwOcchethJcBadrsn> occhethJcBadrsns = new ArrayList<>();
        if(!CollectionUtils.isEmpty(jcSet)){
            for(String str : jcSet){
                if(!this.simpleCodeMap.containsKey(str)){
                    continue;
                }
                TdZwOcchethJcBadrsn badrsn = new TdZwOcchethJcBadrsn();
                badrsn.setFkByBadrsnId(this.simpleCodeMap.get(str));
                occhethJcBadrsns.add(badrsn);
            }
        }
        occhethCard.setOcchethJcBadrsns(occhethJcBadrsns);
        //职业病危害现状评价
        if(!occhethCard.getHasStatusPj()){
            occhethCard.getOcchethCardPjList().clear();
        }
        Integer pjPostNum = 0;
        Integer pjOverNum = 0;
        Set<String> pjSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(occhethCard.getOcchethCardPjList())) {
            for (TdZwOcchethCardPj cardPj : occhethCard.getOcchethCardPjList()) {
                //cardPj.setRid(null);
                //cardPj.setFkByMainId(occhethCard);
                if (!CollectionUtils.isEmpty(cardPj.getOcchethCardPjSubList())) {
                    for (TdZwOcchethCardPjSub pjSub : cardPj.getOcchethCardPjSubList()) {
                        //pjSub.setRid(null);
                        pjSub.setFkByMainId(cardPj);
                        if (pjSub.getIndexRid() == null) {
                            pjSub.setFkByIndexId(null);
                        }
                        if (pjSub.getFkByItemId() != null && pjSub.getFkByItemId().getRid() != null) {
                            pjSub.getFkByItemId().setFkByMainId(new TbYsjcLimitValComm(cardPj.getFkByBadrsnId().getRid()));
                        } else {
                            pjSub.setFkByItemId(null);
                        }
                        if (pjSub.getFkByMsruntId() == null || pjSub.getFkByMsruntId().getRid() == null) {
                            pjSub.setFkByMsruntId(null);
                        }
                        pjSub.setCreateDate(new Date());
                        pjSub.setCreateManid(Global.getUser().getRid());
                    }
                }
                pjPostNum += (cardPj.getPostNum() == null ? 0 : cardPj.getPostNum() );
                pjOverNum += (cardPj.getNum() == null ? 0 : cardPj.getNum() );
                if(cardPj.getNum() != null && cardPj.getNum() > 0){
                    pjSet.add(this.limitValMap.get(cardPj.getFkByBadrsnId().getRid()));
                }
            }
        }
        occhethCard.setPjPostNum(pjPostNum);
        occhethCard.setPjOverNum(pjOverNum);
        List<TdZwOcchethPjBadrsn> occhethPjBadrsns = new ArrayList<>();
        if(!CollectionUtils.isEmpty(pjSet)){
            for(String str : pjSet){
                if(!this.simpleCodeMap.containsKey(str)){
                    continue;
                }
                TdZwOcchethPjBadrsn badrsn = new TdZwOcchethPjBadrsn();
                badrsn.setFkByBadrsnId(this.simpleCodeMap.get(str));
                occhethPjBadrsns.add(badrsn);
            }
        }
        occhethCard.setOcchethPjBadrsns(occhethPjBadrsns);
        //职业病防护设备设施与防护用品的效果评价
        if(!occhethCard.getHasInstUsePj()){
            this.occhethCard.setHasJcInst(false);
            this.occhethCard.setIfJcInst(null);
            this.occhethCard.setJcInstNum(null);
            this.occhethCard.setJcNotHgInstNum(null);
            this.occhethCard.setNotHgInstName(null);
            this.occhethCard.setHasJcUse(false);
            this.occhethCard.setIfJcUse(null);
            this.occhethCard.setJcUseNum(null);
            this.occhethCard.setJcNotHgUseNum(null);
            this.occhethCard.setNotHgUseName(null);
        }else {
            if (this.occhethCard.getHasJcInst()){
                if (occhethCard.getJcNotHgInstNum() == null || occhethCard.getJcNotHgInstNum() == 0){
                    this.occhethCard.setNotHgInstName(null);
                }
            }else{
                this.occhethCard.setHasJcInst(false);
                this.occhethCard.setIfJcInst(null);
                this.occhethCard.setJcInstNum(null);
                this.occhethCard.setJcNotHgInstNum(null);
                this.occhethCard.setNotHgInstName(null);
            }
            if(this.occhethCard.getHasJcUse()) {
                if (occhethCard.getJcNotHgUseNum() == null || occhethCard.getJcNotHgUseNum() == 0) {
                    this.occhethCard.setNotHgUseName(null);
                }
            }else{
                this.occhethCard.setHasJcUse(false);
                this.occhethCard.setIfJcUse(null);
                this.occhethCard.setJcUseNum(null);
                this.occhethCard.setJcNotHgUseNum(null);
                this.occhethCard.setNotHgUseName(null);
            }
        }

    }

    /**
     * 撤销操作
     */
    public void revokeAction() {
        if (this.mainRid == null) {
            return;
        }
        try {
            this.occhethCardService.revokeOcchethCardByRid(this.mainRid);
            JsfUtil.addSuccessMessage("撤销成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
        try {
            this.modInitAction();
            RequestContext.getCurrentInstance().update("tabView");
            if (ObjectUtil.isNotEmpty(this.occhethCard.getAnnexPath())) {
                RequestContext.getCurrentInstance().execute("disabledInput('true','occHethCard')");
            }
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("跳转编辑页面失败！");
        }
    }

    /**
     * 制作文书
     */
    public void buildWritReport() {
        if (verifyFailed(true)) {
            return;
        }
        this.reportFilePath = null;
        String returnJson = "";
        try {
            OcchethCardJcSort();
            computeRowsSpan();
            OcchethCardPjSort();
            computePjRowsSpan();
            saveOcchethCard(0);
            WordReportJson reportJson = new WordReportJson();
            reportJson.setRid(this.occhethCard.getRid());
            reportJson.setRptCode("HETH_RPT_1002");
            reportJson.setIfPdf(1);
            String requestJson = JSON.toJSONString(reportJson);
            String debug = PropertyUtils.getValue("encrypt.debug");
            String encryptKey = PropertyUtils.getValue("encrypt.key");
            //JSON是否需要加密
            String encodeJson = "true".equals(debug) ? requestJson : AesEncryptUtils.aesEncrypt(requestJson, encryptKey);
            //调用接口
            String delUrl = PropertyUtils.getValue("delUrl");
            String reposeJson = HttpRequestUtil.httpRequestByRaw(delUrl + "/word/generate", encodeJson);
            //JSON是否需要解密
            returnJson = "true".equals(debug) ? reposeJson : AesEncryptUtils.aesDecrypt(reposeJson, encryptKey);
        } catch (Exception e) {
            JsfUtil.addErrorMessage("生成文书失败！");
            e.printStackTrace();
            return;
        }
        if (StringUtils.isNotBlank(returnJson)) {
            WordReportDTO returnDTO = JSON.parseObject(returnJson, WordReportDTO.class);
            if (null == returnDTO || !ReturnType.SUCCESS_PROCESS.getTypeNo().equals(returnDTO.getType())) {
                logger.error(null == returnDTO ? "" : returnDTO.getMess());
                JsfUtil.addErrorMessage("生成文书失败！");
                return;
            } else {
                reportFilePath = returnDTO.getFilePath();
                if (StringUtils.isNotBlank(reportFilePath)) {
                    RequestContext.getCurrentInstance().execute("openPDFClick()");
                } else {
                    JsfUtil.addErrorMessage("生成文书失败！");
                    return;
                }
            }
        }
        RequestContext.getCurrentInstance().update("tabView:editForm:editPanel");
    }

    /**
     * 文书上传前验证
     */
    public void beforeUpload() {
        if (verifyFailed(true)) {
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('FileDialog').show();");
        RequestContext.getCurrentInstance().update("fileDialog");
    }

    /**
     * 文书上传
     */
    public void fileUpload(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            try {
                // 文件名称
                String fileName = file.getFileName();
                String errorMsg = FileUtils.veryFile(file.getInputstream(),file.getContentType(), fileName, "2");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    return;
                }
                String uuid = java.util.UUID.randomUUID().toString().replaceAll("-", "");
                String path = JsfUtil.getAbsolutePath();
                String relativePath = "heth/comm/reportcard/" + uuid + fileName.substring(fileName.lastIndexOf("."));
                // 文件路径
                String filePath = path + relativePath;
                this.occhethCard.setAnnexPath(relativePath);
                FileUtils.copyFile(filePath, file.getInputstream());
                OcchethCardJcSort();
                computeRowsSpan();
                OcchethCardPjSort();
                computePjRowsSpan();
                saveOcchethCard(0);
                RequestContext currentInstance = RequestContext.getCurrentInstance();
                currentInstance.execute("PF('FileDialog').hide();");
                currentInstance.update("tabView:editForm:editPanel");
                currentInstance.execute("disabledInput('true','occHethCard')");
                JsfUtil.addSuccessMessage("上传成功！");
                OcchethCardJcSort();
                computeRowsSpan();
                OcchethCardPjSort();
                computePjRowsSpan();
                this.searchAction();
            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("上传失败！");
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 删除文书
     */
    public void delMadedwrit() {
        try {
            this.occhethCardService.deleteOcchethCardAnnexPathByRid(this.occhethCard.getRid());
            JsfUtil.addSuccessMessage("删除成功！");
            modInit();
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.update("tabView:editForm:editPanel");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * 新增参与人员信息
     */
    public void addOcchethCardPsn() {
        Map<String, Object> options = new HashMap<>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("height", 495);
        options.put("width", 660);
        options.put("contentWidth", 630);
        options.put("contentHeight", 490);

        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add(StringUtils.list2string(this.occhethCard.getOcchethCardPsnRidList(), ","));
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<>();
        paramList.add("2");
        paramMap.put("type", paramList);
        paramList = new ArrayList<>();
        paramList.add(this.occhethCard.getOrgId());
        paramMap.put("orgId", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/dialog/selectOrgPsnList.xhtml", options, paramMap);
    }

    /**
     * 选择人员后操作
     *
     * @param event 选择的人员
     */
    public void onOcchethCardPsnSel(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (ObjectUtil.isNotEmpty(selectedMap)) {
            List<SrvorgPsnVo> list = CollectionUtil.castList(SrvorgPsnVo.class, selectedMap.get("selectPros"));
            if (ObjectUtil.isNotEmpty(list)) {
                for (SrvorgPsnVo t : list) {
                    if (this.occhethCard.getOcchethCardPsnRidList().contains(t.getRid().toString())) {
                        continue;
                    }
                    TdZwOcchethCardPsn occhethCardPsn = new TdZwOcchethCardPsn();
                    occhethCardPsn.setFkByMainId(this.occhethCard);
                    occhethCardPsn.setFkByPsnId(new TdZwPsninfoComm(t.getRid()));
                    occhethCardPsn.setPsnName(t.getEmpName());
                    occhethCardPsn.setCreateDate(new Date());
                    occhethCardPsn.setCreateManid(Global.getUser().getRid());
                    this.occhethCard.getOcchethCardPsnList().add(occhethCardPsn);
                    this.occhethCard.getOcchethCardPsnRidList().add(t.getRid().toString());
                }
            }
        }
    }

    /**
     * 删除参与人员信息
     *
     * @param occhethCardPsn 参与人员信息
     */
    public void delOcchethCardPsn(TdZwOcchethCardPsn occhethCardPsn) {
        this.occhethCard.getOcchethCardPsnList().remove(occhethCardPsn);
        for (int i = this.occhethCard.getOcchethCardPsnRidList().size() - 1; i >= 0; i--) {
            if (this.occhethCard.getOcchethCardPsnRidList().get(i).equals(occhethCardPsn.getFkByPsnId().getRid().toString())) {
                this.occhethCard.getOcchethCardPsnRidList().remove(i);
                break;
            }
        }
    }

    /**
     * 用人单位选择
     */
    public void selectCrptList() {
        Map<String, Object> options = MapUtils.produceDialogMap(1077, 1050, 520, 505);
        Map<String, List<String>> paramMap = new HashMap<>();
        CrptDialogParamJson json = new CrptDialogParamJson();
        json.setBusType("4");
        json.setIfShowOtherZone(true);
        json.setIfSearchZoneDefault(false);
        if (ObjectUtil.isNotEmpty(this.occhethCard.getCrptName())) {
            json.setSearchCrptName(this.occhethCard.getCrptName());
        }
        List<String> paramList = new ArrayList<>();
        paramList.add(JSON.toJSONString(json));
        paramMap.put("jsonParam", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        String useNewCrptSelect = this.commService.findParamValue("USE_NEW_CRPT_SELECT");
        String crptSelectPath = "/webapp/heth/comm/crptCommSelectList";
        if ("1".equals(useNewCrptSelect)) {
            crptSelectPath = "/webapp/heth/comm/crptCommSelectListNew";
        }
        requestContext.openDialog(crptSelectPath, options, paramMap);
    }

    /**
     * 选择用人单位后
     *
     * @param event 选择的用人单位
     */
    public void onCrptSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            TbTjCrptIndepend tbTjCrptIndepend = (TbTjCrptIndepend) selectedMap.get("selectCrptIndepend");
            TbTjCrpt tbTjCrpt = tbTjCrptIndepend.getFkByCrptId();
            this.occhethCard.setFkByCrptId(tbTjCrpt);
            this.occhethCard.setFkByZoneId(tbTjCrpt.getTsZoneByZoneId());
            this.occhethCard.setCrptName(tbTjCrpt.getCrptName());
            this.occhethCard.setAddress(tbTjCrpt.getAddress());
            this.occhethCard.setSafeposition(tbTjCrptIndepend.getLinkman2());
            this.occhethCard.setSafephone(tbTjCrptIndepend.getLinkphone2());
            this.occhethCard.setCreditCode(tbTjCrpt.getInstitutionCode());
            this.occhethCard.setFkByCrptSizeId(tbTjCrpt.getTsSimpleCodeByCrptSizeId());
        }
    }

    /**
     * 新增技术服务地址
     */
    public void addOcchethCardZone() {
        TdZwOcchethCardZone occhethCardZone = new TdZwOcchethCardZone();
        occhethCardZone.setFkByMainId(this.occhethCard);
        occhethCardZone.setFkByZoneId(new TsZone());
        occhethCardZone.setCreateDate(new Date());
        occhethCardZone.setCreateManid(Global.getUser().getRid());
        this.occhethCard.getOcchethCardZoneList().add(occhethCardZone);
    }

    /**
     * 选择技术服务地址
     *
     * @param occhethCardZone 当前选择的技术服务地址
     */
    public void onOcchethCardZoneSelect(TdZwOcchethCardZone occhethCardZone) {
        if (ObjectUtil.isNotEmpty(occhethCardZone.getZoneRid())
                && this.editZoneMap.containsKey(occhethCardZone.getZoneRid())) {
            TsZone zone = this.editZoneMap.get(occhethCardZone.getZoneRid());
            occhethCardZone.setFkByZoneId(zone);
            occhethCardZone.setFullName(zone.getFullName());
        }
    }

    /**
     * 删除技术服务地址
     *
     * @param occhethCardZone 当前选择的技术服务地址
     */
    public void delOcchethCardZone(TdZwOcchethCardZone occhethCardZone) {
        this.occhethCard.getOcchethCardZoneList().remove(occhethCardZone);
    }

    /**
     * <p>方法描述：添加职业病危害因素检测</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-27
     **/
    public void openBadrsnJcDialog() {
        //初始化检测项目
        initItem();
        this.postNumF=null;
        this.numF=null;
        if (this.type == 1) {
            postNumF=tdZwOcchethCardJc.getPostNum();
            numF=tdZwOcchethCardJc.getNum();
            if (!CollectionUtils.isEmpty(tdZwOcchethCardJc.getOcchethCardJcSubList())) {
                if (tdZwOcchethCardJc.getOcchethCardJcSubList().size() == 1
                        && tdZwOcchethCardJc.getOcchethCardJcSubList().get(0).getIfFirst()) {
                    jcSubList = new ArrayList<>();
                } else {
                    jcSubList = new ArrayList<>();
                    jcSubList=ObjectCopyUtil.deepCopy(tdZwOcchethCardJc.getOcchethCardJcSubList());
                    for (TdZwOcchethCardJcSub jcSub : jcSubList) {
                        try{
                            TbYsjcRsnRelItemComm item = new TbYsjcRsnRelItemComm();
                            ObjectCopyUtil.copyProperties(jcSub.getFkByItemId(),item);
                            jcSub.setFkByItemId(item);
                        }catch (Exception e){
                            e.printStackTrace();
                        }
                        changeItemAction(jcSub,false);
                    }
                }
            }
        } else if (this.type == 2) {
            postNumF=tdZwOcchethCardPj.getPostNum();
            numF=tdZwOcchethCardPj.getNum();
            if (!CollectionUtils.isEmpty(tdZwOcchethCardPj.getOcchethCardPjSubList())) {
                if (tdZwOcchethCardPj.getOcchethCardPjSubList().size() == 1
                        && tdZwOcchethCardPj.getOcchethCardPjSubList().get(0).getIfFirst()) {
                    pjSubList = new ArrayList<>();
                } else {
                    pjSubList = new ArrayList<>();
                    pjSubList=ObjectCopyUtil.deepCopy(tdZwOcchethCardPj.getOcchethCardPjSubList());
                    for (TdZwOcchethCardPjSub pjSub : pjSubList) {
                        try{
                            TbYsjcRsnRelItemComm item = new TbYsjcRsnRelItemComm();
                            ObjectCopyUtil.copyProperties(pjSub.getFkByItemId(),item);
                            pjSub.setFkByItemId(item);
                        }catch (Exception e){
                            e.printStackTrace();
                        }
                        changePjItemAction(pjSub,false);
                    }
                }
            }
        }
        RequestContext.getCurrentInstance().execute("PF('AddBadrsnJcDialog').show()");
    }

    /**
     * <p>方法描述：初始化检测项目</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-09-01
     **/
    public void initItem() {
        itemList2 = new ArrayList<>();
        List<Object[]> itemObjList = new ArrayList<>();
        if (this.type == 1) {
            itemObjList = occhethCardService.findItemByBadrsnId(Integer.parseInt(tdZwOcchethCardJc.getFkByBadrsnId().getRid().toString()), null);
        } else if (this.type == 2) {
            itemObjList = occhethCardService.findItemByBadrsnId(Integer.parseInt(tdZwOcchethCardPj.getFkByBadrsnId().getRid().toString()), null);
        }
        if (!CollectionUtils.isEmpty(itemObjList)) {
            itemList2.addAll(itemObjList);
            for (Object[] objects : itemObjList) {
                TsSimpleCode newitem = new TsSimpleCode();
                newitem.setRid(Integer.parseInt(objects[0].toString()));
                newitem.setCodeName(objects[1].toString());
                newitem.setNum(objects[7] != null ? Integer.parseInt(objects[7].toString()) : null);
                objects[8]=objects[1].toString().replaceAll("(<sub>)|(</sub>)|(<sup>)|(</sup>)", "");
                if (!itemMap.containsKey(newitem.getRid())) {
                    itemMap.put(newitem.getRid(), newitem);
                }
            }
        }
    }

    /**
     * <p>方法描述：确定 职业病危害因素检测</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-27
     **/
    public void addBadrsnJcAction() {
        boolean flag = false;
        if (this.postNumF == null) {
            JsfUtil.addErrorMessage("岗位/工种数不能为空！");
            flag = true;
        }
        if(null == this.numF){
            JsfUtil.addErrorMessage("超标岗位/工种数不能为空！");
            flag = true;
        }

        int count = 1;
        for (TdZwOcchethCardJcSub jcSub : this.jcSubList) {
            if (StringUtils.isBlank(jcSub.getPostName())) {
                JsfUtil.addErrorMessage("第" + count + "行超标岗位/工种名称不能为空！");
                flag = true;
            }
            if (jcSub.getFkByItemId() == null || jcSub.getFkByItemId().getRid() == null) {
                JsfUtil.addErrorMessage("第" + count + "行超标检测项目不能为空！");
                flag = true;
            }
            if (jcSub.getIndexRid() == null) {
                JsfUtil.addErrorMessage("第" + count + "行超标检测指标不能为空！");
                flag = true;
            }
            if (jcSub.getRstLow() == null) {
                JsfUtil.addErrorMessage("第" + count + "行超标值下限不能为空！");
                flag = true;
            }
            if (jcSub.getRstUpper() == null) {
                JsfUtil.addErrorMessage("第" + count + "行超标值上限不能为空！");
                flag = true;
            }
            if (jcSub.getRstLow() != null && jcSub.getRstUpper() != null && jcSub.getRstLow().compareTo(jcSub.getRstUpper()) > 0) {
                JsfUtil.addErrorMessage("第" + count + "行超标值下限不能大于超标值上限！");
                flag = true;
            }
            count++;
        }
        //超标岗位/工种数 小于等于 “岗位/工种数”
        if (postNumF != null && null != numF && postNumF < numF) {
            JsfUtil.addErrorMessage("岗位/工种数应大于等于超标岗位/工种数！");
            flag = true;
        }

        if (flag) {
            return;
        }
        tdZwOcchethCardJc.setPostNum(this.postNumF);
        tdZwOcchethCardJc.setNum(this.numF);
        tdZwOcchethCardJc.getOcchethCardJcSubList().clear();
        tdZwOcchethCardJc.getOcchethCardJcSubList().addAll(this.jcSubList);
        if (!CollectionUtils.isEmpty(tdZwOcchethCardJc.getOcchethCardJcSubList())) {
            if (tdZwOcchethCardJc.getOcchethCardJcSubList().size() == 1
                    && tdZwOcchethCardJc.getOcchethCardJcSubList().get(0).getIfFirst()) {
                tdZwOcchethCardJc.getOcchethCardJcSubList().get(0).setIfFirst(false);
            }
        }
        //计算行合并
        OcchethCardJcSort();
        computeRowsSpan();
        RequestContext.getCurrentInstance().update("tabView:editForm:badrsnJcDataTable");
        RequestContext.getCurrentInstance().execute("PF('AddBadrsnJcDialog').hide()");
    }


    /**
     * <p>方法描述：职业病危害现状评价</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-09-01
     **/
    public void addPjAction() {
        boolean flag = false;
        if (this.postNumF == null) {
            JsfUtil.addErrorMessage("岗位/工种数不能为空！");
            flag = true;
        }
        if(null == this.numF){
            JsfUtil.addErrorMessage("超标岗位/工种数不能为空！");
            flag = true;
        }
        int count = 1;
        for (TdZwOcchethCardPjSub pjSub : this.pjSubList) {
            if (StringUtils.isBlank(pjSub.getPostName())) {
                JsfUtil.addErrorMessage("第" + count + "行超标岗位/工种名称不能为空！");
                flag = true;
            }
            if (pjSub.getFkByItemId() == null || pjSub.getFkByItemId().getRid() == null) {
                JsfUtil.addErrorMessage("第" + count + "行超标检测项目不能为空！");
                flag = true;
            }
            if (pjSub.getIndexRid() == null) {
                JsfUtil.addErrorMessage("第" + count + "行超标检测指标不能为空！");
                flag = true;
            }
            if (pjSub.getRstLow() == null) {
                JsfUtil.addErrorMessage("第" + count + "行超标值下限不能为空！");
                flag = true;
            }
            if (pjSub.getRstUpper() == null) {
                JsfUtil.addErrorMessage("第" + count + "行超标值上限不能为空！");
                flag = true;
            }
            if (pjSub.getRstLow() != null && pjSub.getRstUpper() != null && pjSub.getRstLow().compareTo(pjSub.getRstUpper()) > 0) {
                JsfUtil.addErrorMessage("第" + count + "行超标值下限不能大于超标值上限！");
                flag = true;
            }
            count++;
        }
        if (postNumF != null && null != numF && postNumF < numF) {
            JsfUtil.addErrorMessage("岗位/工种数应大于等于超标岗位/工种数！");
            flag = true;
        }

        if (flag) {
            return;
        }
        tdZwOcchethCardPj.setPostNum(this.postNumF);
        tdZwOcchethCardPj.setNum(this.numF);
        tdZwOcchethCardPj.setOcchethCardPjSubList(this.pjSubList);
        if (!CollectionUtils.isEmpty(tdZwOcchethCardPj.getOcchethCardPjSubList())) {
            if (tdZwOcchethCardPj.getOcchethCardPjSubList().size() == 1
                    && tdZwOcchethCardPj.getOcchethCardPjSubList().get(0).getIfFirst()) {
                tdZwOcchethCardPj.getOcchethCardPjSubList().get(0).setIfFirst(false);
            }
        }
        //计算行合并
        OcchethCardPjSort();
        computePjRowsSpan();
        RequestContext.getCurrentInstance().update("tabView:editForm:badrsnPjDataTable");
        RequestContext.getCurrentInstance().execute("PF('AddBadrsnJcDialog').hide()");
    }


    /**
     * <p>方法描述：计算行合并</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-30
     **/
    public void computeRowsSpan() {
        if (!CollectionUtils.isEmpty(occhethCard.getOcchethCardJcList())) {
            Map<String, Integer> countRidMap = new HashMap<>();
            Map<String, Integer> countPostMap = new HashMap<>();
            Map<String, Integer> countItemMap = new HashMap<>();
            Set<String> ridSet = new HashSet<>();
            for (TdZwOcchethCardJc cardJc : occhethCard.getOcchethCardJcList()) {
                if (!CollectionUtils.isEmpty(cardJc.getOcchethCardJcSubList()) && cardJc.getOcchethCardJcSubList().size() > 1) {
                    for (TdZwOcchethCardJcSub jcSub : cardJc.getOcchethCardJcSubList()) {
                        if (countRidMap.get(cardJc.getFkByBadrsnId().getRid().toString()) == null) {
                            countRidMap.put(cardJc.getFkByBadrsnId().getRid().toString(), 1);
                        } else {
                            countRidMap.put(cardJc.getFkByBadrsnId().getRid().toString(), countRidMap.get(cardJc.getFkByBadrsnId().getRid().toString()) + 1);
                        }
                        if (countPostMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName()) == null) {
                            countPostMap.put(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName(), 1);
                        } else {
                            countPostMap.put(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName(), countPostMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName()) + 1);
                        }
                        if (countItemMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid()) == null) {
                            countItemMap.put(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid(), 1);
                        } else {
                            countItemMap.put(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid(), countItemMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid()) + 1);
                        }
                        if (ridSet.add(cardJc.getFkByBadrsnId().getRid().toString())) {
                            jcSub.setBadrsnName(cardJc.getFkByBadrsnId().getRsnCnName());
                            jcSub.setNum(cardJc.getNum());
                            jcSub.setPostNum(cardJc.getPostNum());
                            jcSub.setBadrsnRowspan(cardJc.getOcchethCardJcSubList().size());
                        } else {
                            jcSub.setBadrsnName(null);
                            jcSub.setPostNum(null);
                            jcSub.setNum(null);
                        }
                        if (StringUtils.isNotBlank(jcSub.getPostName()) && jcSub.getFkByItemId() != null && jcSub.getFkByItemId().getRid() != null) {
                            if (ridSet.add(cardJc.getFkByBadrsnId().getRid().toString() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid())) {
                                jcSub.setItermName(itemMap.get(jcSub.getFkByItemId().getRid()).getCodeName());
                                jcSub.getFkByItemId().setFkByItemId(new TsSimpleCode("", itemMap.get(jcSub.getFkByItemId().getRid()).getCodeName()));
                            } else {
                                jcSub.setItermName(null);
                            }
                        } else {
                            jcSub.setItermName(null);
                        }
                        jcSub.setFkByIndexId(indexMap.get(jcSub.getIndexRid()));
                    }
                } else {
                    if (CollectionUtils.isEmpty(cardJc.getOcchethCardJcSubList())) {
                        TdZwOcchethCardJcSub occhethCardJcSub = new TdZwOcchethCardJcSub();
                        occhethCardJcSub.setBadrsnName(cardJc.getFkByBadrsnId().getRsnCnName());
                        occhethCardJcSub.setIfFirst(true);
                        occhethCardJcSub.setFkByMainId(cardJc);
                        occhethCardJcSub.setFkByItemId(new TbYsjcRsnRelItemComm());
                        occhethCardJcSub.setFkByIndexId(new TsSimpleCode());
                        occhethCardJcSub.setFkByMsruntId(new TsSimpleCode());
                        List<TdZwOcchethCardJcSub> subList = new ArrayList<>();
                        subList.add(occhethCardJcSub);
                        cardJc.setOcchethCardJcSubList(subList);
                    }
                    if(cardJc.getPostNum()!=null){
                        cardJc.getOcchethCardJcSubList().get(0).setPostNum(cardJc.getPostNum());
                    }
                    cardJc.getOcchethCardJcSubList().get(0).setBadrsnName(cardJc.getFkByBadrsnId().getRsnCnName());
                    if (StringUtils.isNotBlank(cardJc.getOcchethCardJcSubList().get(0).getPostName())) {
                        cardJc.getOcchethCardJcSubList().get(0).setNum(cardJc.getNum());
                        cardJc.getOcchethCardJcSubList().get(0).getFkByItemId().setFkByItemId(new TsSimpleCode("", itemMap.get(cardJc.getOcchethCardJcSubList().get(0).getFkByItemId().getRid()).getCodeName()));
                        cardJc.getOcchethCardJcSubList().get(0).setItermName(itemMap.get(cardJc.getOcchethCardJcSubList().get(0).getFkByItemId().getRid()).getCodeName());
                        cardJc.getOcchethCardJcSubList().get(0).setFkByIndexId(indexMap.get(cardJc.getOcchethCardJcSubList().get(0).getIndexRid()));
                    } else {
                        cardJc.getOcchethCardJcSubList().get(0).setItermName("");
                        cardJc.getOcchethCardJcSubList().get(0).setNum(cardJc.getNum());
                        cardJc.getOcchethCardJcSubList().get(0).setPostNameShow("");
                    }
                    cardJc.getOcchethCardJcSubList().get(0).setBadrsnRowspan(1);
                    cardJc.getOcchethCardJcSubList().get(0).setItemsRowspan(1);
                    cardJc.getOcchethCardJcSubList().get(0).setPostRowspan(1);
                }
            }
            Set<String> ridsSet = new HashSet<>();
            for (TdZwOcchethCardJc cardJc : occhethCard.getOcchethCardJcList()) {
                if (!CollectionUtils.isEmpty(cardJc.getOcchethCardJcSubList()) && cardJc.getOcchethCardJcSubList().size() > 1) {
                    for (TdZwOcchethCardJcSub jcSub : cardJc.getOcchethCardJcSubList()) {
                        if (!countItemMap.isEmpty() && countItemMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid()) != null && ridsSet.add(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid())) {
                            jcSub.setItemsRowspan(countItemMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName() + "&" + jcSub.getFkByItemId().getRid()));
                        } else {
                            jcSub.setItermName(null);
                        }
                        if (!countPostMap.isEmpty() && countPostMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName()) != null && ridsSet.add(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName())) {
                            jcSub.setPostRowspan(countPostMap.get(cardJc.getFkByBadrsnId().getRid() + "&" + jcSub.getPostName()));
                        } else {
                            jcSub.setPostNameShow(null);
                        }
                    }
                }
            }

        }
    }

    /**
     * <p>方法描述：计算行合并</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-30
     **/
    public void computePjRowsSpan() {
        if (!CollectionUtils.isEmpty(occhethCard.getOcchethCardPjList())) {
            Map<String, Integer> countRidMap = new HashMap<>();
            Map<String, Integer> countPostMap = new HashMap<>();
            Map<String, Integer> countItemMap = new HashMap<>();
            Set<String> ridSet = new HashSet<>();
            for (TdZwOcchethCardPj cardPj : occhethCard.getOcchethCardPjList()) {
                if (!CollectionUtils.isEmpty(cardPj.getOcchethCardPjSubList()) && cardPj.getOcchethCardPjSubList().size() > 1) {
                    for (TdZwOcchethCardPjSub pjSub : cardPj.getOcchethCardPjSubList()) {
                        if (countRidMap.get(cardPj.getFkByBadrsnId().getRid().toString()) == null) {
                            countRidMap.put(cardPj.getFkByBadrsnId().getRid().toString(), 1);
                        } else {
                            countRidMap.put(cardPj.getFkByBadrsnId().getRid().toString(), countRidMap.get(cardPj.getFkByBadrsnId().getRid().toString()) + 1);
                        }
                        if (countPostMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName()) == null) {
                            countPostMap.put(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName(), 1);
                        } else {
                            countPostMap.put(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName(), countPostMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName()) + 1);
                        }
                        if (countItemMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid()) == null) {
                            countItemMap.put(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid(), 1);
                        } else {
                            countItemMap.put(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid(), countItemMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid()) + 1);
                        }
                        if (ridSet.add(cardPj.getFkByBadrsnId().getRid().toString())) {
                            pjSub.setBadrsnName(cardPj.getFkByBadrsnId().getRsnCnName());
                            pjSub.setNum(cardPj.getNum());
                            pjSub.setPostNum(cardPj.getPostNum());
                            pjSub.setBadrsnRowspan(cardPj.getOcchethCardPjSubList().size());
                        } else {
                            pjSub.setBadrsnName(null);
                            pjSub.setPostNum(null);
                            pjSub.setNum(null);
                        }
                        if (StringUtils.isNotBlank(pjSub.getPostName()) && pjSub.getFkByItemId() != null && pjSub.getFkByItemId().getRid() != null) {
                            if (ridSet.add(cardPj.getFkByBadrsnId().getRid().toString() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid())) {
                                pjSub.setItermName(itemMap.get(pjSub.getFkByItemId().getRid()).getCodeName());
                                pjSub.getFkByItemId().setFkByItemId(new TsSimpleCode("", itemMap.get(pjSub.getFkByItemId().getRid()).getCodeName()));
                            } else {
                                pjSub.setItermName(null);
                            }
                        } else {
                            pjSub.setItermName(null);
                        }
                        pjSub.setFkByIndexId(indexMap.get(pjSub.getIndexRid()));
                    }
                } else {
                    if (CollectionUtils.isEmpty(cardPj.getOcchethCardPjSubList())) {
                        TdZwOcchethCardPjSub occhethCardPjSub = new TdZwOcchethCardPjSub();
                        occhethCardPjSub.setBadrsnName(cardPj.getFkByBadrsnId().getRsnCnName());
                        occhethCardPjSub.setIfFirst(true);
                        occhethCardPjSub.setFkByMainId(cardPj);
                        occhethCardPjSub.setFkByItemId(new TbYsjcRsnRelItemComm());
                        occhethCardPjSub.setFkByIndexId(new TsSimpleCode());
                        occhethCardPjSub.setFkByMsruntId(new TsSimpleCode());
                        List<TdZwOcchethCardPjSub> subList = new ArrayList<>();
                        subList.add(occhethCardPjSub);
                        cardPj.setOcchethCardPjSubList(subList);
                    }
                    if(cardPj.getPostNum()!=null){
                        cardPj.getOcchethCardPjSubList().get(0).setPostNum(cardPj.getPostNum());
                    }
                    cardPj.getOcchethCardPjSubList().get(0).setBadrsnName(cardPj.getFkByBadrsnId().getRsnCnName());
                    if (StringUtils.isNotBlank(cardPj.getOcchethCardPjSubList().get(0).getPostName())) {
                        cardPj.getOcchethCardPjSubList().get(0).setNum(cardPj.getNum());
                        cardPj.getOcchethCardPjSubList().get(0).getFkByItemId().setFkByItemId(new TsSimpleCode("", itemMap.get(cardPj.getOcchethCardPjSubList().get(0).getFkByItemId().getRid()).getCodeName()));
                        cardPj.getOcchethCardPjSubList().get(0).setItermName(itemMap.get(cardPj.getOcchethCardPjSubList().get(0).getFkByItemId().getRid()).getCodeName());
                        cardPj.getOcchethCardPjSubList().get(0).setFkByIndexId(indexMap.get(cardPj.getOcchethCardPjSubList().get(0).getIndexRid()));
                    } else {
                        cardPj.getOcchethCardPjSubList().get(0).setItermName("");
                        cardPj.getOcchethCardPjSubList().get(0).setNum(cardPj.getNum());
                        cardPj.getOcchethCardPjSubList().get(0).setPostNameShow("");
                    }
                    cardPj.getOcchethCardPjSubList().get(0).setBadrsnRowspan(1);
                    cardPj.getOcchethCardPjSubList().get(0).setItemsRowspan(1);
                    cardPj.getOcchethCardPjSubList().get(0).setPostRowspan(1);
                }
            }
            Set<String> ridsSet = new HashSet<>();
            for (TdZwOcchethCardPj cardPj : occhethCard.getOcchethCardPjList()) {
                if (!CollectionUtils.isEmpty(cardPj.getOcchethCardPjSubList()) && cardPj.getOcchethCardPjSubList().size() > 1) {
                    for (TdZwOcchethCardPjSub pjSub : cardPj.getOcchethCardPjSubList()) {
                        if (!countItemMap.isEmpty() && countItemMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid()) != null && ridsSet.add(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid())) {
                            pjSub.setItemsRowspan(countItemMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName() + "&" + pjSub.getFkByItemId().getRid()));
                        } else {
                            pjSub.setItermName(null);
                        }
                        if (!countPostMap.isEmpty() && countPostMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName()) != null && ridsSet.add(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName())) {
                            pjSub.setPostRowspan(countPostMap.get(cardPj.getFkByBadrsnId().getRid() + "&" + pjSub.getPostName()));
                        } else {
                            pjSub.setPostNameShow(null);
                        }
                    }
                }
            }
        }
    }


    /**
     * <p>方法描述：列表  排序</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-30
     **/
    public void OcchethCardJcSort() {
        if (!CollectionUtils.isEmpty(occhethCard.getOcchethCardJcList())) {
            //危害因素 排序
            Collections.sort(occhethCard.getOcchethCardJcList(), new Comparator<TdZwOcchethCardJc>() {
                @Override
                public int compare(TdZwOcchethCardJc o1, TdZwOcchethCardJc o2) {
                    Integer num1 = o1.getFkByBadrsnId().getNum();
                    Integer num2 = o2.getFkByBadrsnId().getNum();
                    if (null != num1 && null != num2) {
                        return num1.compareTo(num2);
                    }
                    return 0;
                }
            });
            for (TdZwOcchethCardJc cardJc : occhethCard.getOcchethCardJcList()) {
                if (!CollectionUtils.isEmpty(cardJc.getOcchethCardJcSubList())) {
                    for (TdZwOcchethCardJcSub tdZwOcchethCardJcSub : cardJc.getOcchethCardJcSubList()) {
                        if (null == tdZwOcchethCardJcSub.getFkByItemId() || null == tdZwOcchethCardJcSub.getFkByItemId().getRid()) {
                            tdZwOcchethCardJcSub.setItemNumber(-1);
                        } else if (itemMap.containsKey(tdZwOcchethCardJcSub.getFkByItemId().getRid())) {
                            tdZwOcchethCardJcSub.setItemNumber(itemMap.get(tdZwOcchethCardJcSub.getFkByItemId().getRid()).getNum());
                        }
                        if (null == tdZwOcchethCardJcSub.getIndexRid()) {
                            tdZwOcchethCardJcSub.setIndexNumber(-1);
                        } else if (indexMap.containsKey(tdZwOcchethCardJcSub.getIndexRid())) {
                            tdZwOcchethCardJcSub.setIndexNumber(indexMap.get(tdZwOcchethCardJcSub.getIndexRid()).getNum());
                        }
                    }
                    Collections.sort(cardJc.getOcchethCardJcSubList(), new Comparator<TdZwOcchethCardJcSub>() {
                        @Override
                        public int compare(TdZwOcchethCardJcSub o1, TdZwOcchethCardJcSub o2) {
                            String postName1 = StringUtils.objectToString(o1.getPostName());
                            String postName2 = StringUtils.objectToString(o2.getPostName());

                            String itemNum1 = StringUtils.objectToString(o1.getItemNumber());
                            String itemNum2 = StringUtils.objectToString(o2.getItemNumber());

                            String indexNum1 = StringUtils.objectToString(o1.getIndexNumber());
                            String indexNum2 = StringUtils.objectToString(o2.getIndexNumber());

                            if (!postName1.equals(postName2)) {
                                return postName1.compareTo(postName2);
                            } else if (!itemNum1.equals(itemNum2)) {
                                return itemNum1.compareTo(itemNum2);
                            } else if (!indexNum1.equals(indexNum2)) {
                                return indexNum1.compareTo(indexNum2);
                            }
                            return 0;
                        }
                    });
                }
            }
        }
    }

    /**
     * <p>方法描述：添加 子表</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-27
     **/
    public void addOcchethCardJcSub() {
        if (this.type == 1) {
            TdZwOcchethCardJcSub jcSub = new TdZwOcchethCardJcSub();
            jcSub.setFkByMainId(this.tdZwOcchethCardJc);
            if (!CollectionUtils.isEmpty(itemList2) && itemList2.size() == 1) {
                TbYsjcRsnRelItemComm itemComm = new TbYsjcRsnRelItemComm();
                itemComm.setRid(Integer.parseInt(itemList2.get(0)[0].toString()));
                itemComm.setNum(Integer.parseInt(itemList2.get(0)[7].toString()));
                jcSub.setFkByItemId(itemComm);
                changeItemAction(jcSub,true);
            } else {
                jcSub.setFkByItemId(new TbYsjcRsnRelItemComm());
                jcSub.setMsruntTree(new DefaultTreeNode());
            }
            jcSub.setIndexRid(null);
            jcSub.setFkByIndexId(new TsSimpleCode());
            jcSub.setCreateDate(new Date());
            jcSub.setIfFirst(false);
            jcSub.setCreateManid(Global.getUser().getRid());
            this.jcSubList.add(jcSub);
        } else if (this.type == 2) {
            TdZwOcchethCardPjSub pjSub = new TdZwOcchethCardPjSub();
            pjSub.setFkByMainId(this.tdZwOcchethCardPj);
            if (!CollectionUtils.isEmpty(itemList2) && itemList2.size() == 1) {
                TbYsjcRsnRelItemComm itemComm = new TbYsjcRsnRelItemComm();
                itemComm.setRid(Integer.parseInt(itemList2.get(0)[0].toString()));
                itemComm.setNum(Integer.parseInt(itemList2.get(0)[7].toString()));
                pjSub.setFkByItemId(itemComm);
                changePjItemAction(pjSub,true);
            } else {
                pjSub.setFkByItemId(new TbYsjcRsnRelItemComm());
            }
            pjSub.setIndexRid(null);
            pjSub.setFkByIndexId(new TsSimpleCode());
            pjSub.setCreateDate(new Date());
            pjSub.setCreateManid(Global.getUser().getRid());
            pjSub.setIfFirst(false);
            this.pjSubList.add(pjSub);
        }
    }

    /**
     * <p>方法描述：列表  排序</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-30
     **/
    public void OcchethCardPjSort() {
        if (!CollectionUtils.isEmpty(occhethCard.getOcchethCardPjList())) {
            //危害因素 排序
            Collections.sort(occhethCard.getOcchethCardPjList(), new Comparator<TdZwOcchethCardPj>() {
                @Override
                public int compare(TdZwOcchethCardPj o1, TdZwOcchethCardPj o2) {
                    Integer num1 = o1.getFkByBadrsnId().getNum();
                    Integer num2 = o2.getFkByBadrsnId().getNum();
                    if (null != num1 && null != num2) {
                        return num1.compareTo(num2);
                    }
                    return 0;
                }
            });
            for (TdZwOcchethCardPj cardPj : occhethCard.getOcchethCardPjList()) {
                if (!CollectionUtils.isEmpty(cardPj.getOcchethCardPjSubList())) {
                    for (TdZwOcchethCardPjSub tdZwOcchethCardPjSub : cardPj.getOcchethCardPjSubList()) {

                        if (null == tdZwOcchethCardPjSub.getFkByItemId() || null == tdZwOcchethCardPjSub.getFkByItemId().getRid()) {
                            tdZwOcchethCardPjSub.setItemNumber(-1);
                        } else if (itemMap.containsKey(tdZwOcchethCardPjSub.getFkByItemId().getRid())) {
                            tdZwOcchethCardPjSub.setItemNumber(itemMap.get(tdZwOcchethCardPjSub.getFkByItemId().getRid()).getNum());
                        }
                        if (null == tdZwOcchethCardPjSub.getIndexRid()) {
                            tdZwOcchethCardPjSub.setIndexNumber(-1);
                        } else if (indexMap.containsKey(tdZwOcchethCardPjSub.getIndexRid())) {
                            tdZwOcchethCardPjSub.setIndexNumber(indexMap.get(tdZwOcchethCardPjSub.getIndexRid()).getNum());
                        }
                    }
                    Collections.sort(cardPj.getOcchethCardPjSubList(), new Comparator<TdZwOcchethCardPjSub>() {
                        @Override
                        public int compare(TdZwOcchethCardPjSub o1, TdZwOcchethCardPjSub o2) {
                            String postName1 = StringUtils.objectToString(o1.getPostName());
                            String postName2 = StringUtils.objectToString(o2.getPostName());

                            String itemNum1 = StringUtils.objectToString(o1.getItemNumber());
                            String itemNum2 = StringUtils.objectToString(o2.getItemNumber());

                            String indexNum1 = StringUtils.objectToString(o1.getIndexNumber());
                            String indexNum2 = StringUtils.objectToString(o2.getIndexNumber());

                            if (!postName1.equals(postName2)) {
                                return postName1.compareTo(postName2);
                            } else if (!itemNum1.equals(itemNum2)) {
                                return itemNum1.compareTo(itemNum2);
                            } else if (!indexNum1.equals(indexNum2)) {
                                return indexNum1.compareTo(indexNum2);
                            }
                            return 0;
                        }
                    });
                }
            }
        }
    }


    /**
     * <p>方法描述：危害因素选择后事件</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-27
     **/
    public void openBadrsnDialog() {
        if (this.type == 1) {
            this.selectBadRsns = new ArrayList<>();
        } else if (this.type == 2) {
            this.selectPjBadRsns = new ArrayList<>();
        }

        //危害因素列表初始化
        this.firstBadRsnId = null;
        this.searchNamOrPy = null;
        badRsnSearch();
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:selectedBadRsnTable");
        dataTable.setFirst(0);
        dataTable.setRows(10);
        RequestContext.getCurrentInstance().execute("PF('BadRsnDialog').show()");
    }

    /**
     * <p>方法描述：确定 危害因素选择弹框</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-27
     **/
    public void saveBadRsnAction() {

        if (this.type == 1) {

            if (CollectionUtils.isEmpty(selectBadRsns)) {
                JsfUtil.addErrorMessage("请选择危害因素！");
                return;
            }

            for (Object[] badRsn : selectBadRsns) {
                TdZwOcchethCardJc occhethCardJc = new TdZwOcchethCardJc();
                occhethCardJc.setFkByMainId(occhethCard);
                TbYsjcLimitValComm limitValComm = new TbYsjcLimitValComm();
                limitValComm.setRid(Integer.parseInt(badRsn[0].toString()));
                limitValComm.setRsnCnName(badRsn[1].toString());
                limitValComm.setNum(badRsn[2] != null ? Integer.parseInt(badRsn[2].toString()) : null);
                occhethCardJc.setFkByBadrsnId(limitValComm);
                TdZwOcchethCardJcSub occhethCardJcSub = new TdZwOcchethCardJcSub();
                occhethCardJcSub.setBadrsnName(badRsn[1].toString());
                occhethCardJcSub.setPostNameShow("");
                occhethCardJcSub.setPostNum(0);
                occhethCardJcSub.setIfFirst(true);
                occhethCardJcSub.setFkByMainId(occhethCardJc);
                occhethCardJcSub.setFkByItemId(new TbYsjcRsnRelItemComm());
                occhethCardJcSub.setItermName("");
                occhethCardJcSub.setFkByIndexId(new TsSimpleCode());
                occhethCardJcSub.setFkByMsruntId(new TsSimpleCode());
                occhethCardJcSub.setCreateDate(new Date());
                occhethCardJcSub.setCreateManid(Global.getUser().getRid());
                List<TdZwOcchethCardJcSub> subList = new ArrayList<>();
                subList.add(occhethCardJcSub);
                occhethCardJc.setOcchethCardJcSubList(subList);
                occhethCard.getOcchethCardJcList().add(occhethCardJc);
            }
            OcchethCardJcSort();
            RequestContext.getCurrentInstance().update("tabView:editForm:badrsnJcDataTable");
        } else if (this.type == 2) {

            if (CollectionUtils.isEmpty(selectPjBadRsns)) {
                JsfUtil.addErrorMessage("请选择危害因素！");
                return;
            }
            for (Object[] badRsn : selectPjBadRsns) {
                TdZwOcchethCardPj occhethCardPj = new TdZwOcchethCardPj();
                occhethCardPj.setFkByMainId(occhethCard);
                TbYsjcLimitValComm limitValComm = new TbYsjcLimitValComm();
                limitValComm.setRid(Integer.parseInt(badRsn[0].toString()));
                limitValComm.setRsnCnName(badRsn[1].toString());
                limitValComm.setNum(badRsn[2] != null ? Integer.parseInt(badRsn[2].toString()) : null);
                occhethCardPj.setFkByBadrsnId(limitValComm);
                TdZwOcchethCardPjSub occhethCardPjSub = new TdZwOcchethCardPjSub();
                occhethCardPjSub.setBadrsnName(badRsn[1].toString());
                occhethCardPjSub.setPostNameShow("");
                occhethCardPjSub.setPostNum(0);
                occhethCardPjSub.setIfFirst(true);
                occhethCardPjSub.setFkByMainId(occhethCardPj);
                occhethCardPjSub.setFkByItemId(new TbYsjcRsnRelItemComm());
                occhethCardPjSub.setItermName("");
                occhethCardPjSub.setFkByIndexId(new TsSimpleCode());
                occhethCardPjSub.setFkByMsruntId(new TsSimpleCode());
                occhethCardPjSub.setCreateDate(new Date());
                occhethCardPjSub.setCreateManid(Global.getUser().getRid());
                List<TdZwOcchethCardPjSub> subList = new ArrayList<>();
                subList.add(occhethCardPjSub);
                occhethCardPj.setOcchethCardPjSubList(subList);
                occhethCard.getOcchethCardPjList().add(occhethCardPj);
            }
            OcchethCardPjSort();
            RequestContext.getCurrentInstance().update("tabView:editForm:badrsnPjDataTable");
        }
        RequestContext.getCurrentInstance().execute("PF('BadRsnDialog').hide()");
    }

    /**
     * <p>方法描述：</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-09-02
     **/
    public void sortBadrsnName(List<Object[]> selectBadRsns) {
        Collections.sort(selectBadRsns, new Comparator<Object[]>() {
            @Override
            public int compare(Object[] o1, Object[] o2) {
                Integer num1 = o1[2] == null ? null : Integer.parseInt(o1[2].toString());
                Integer num2 = o2[2] == null ? null : Integer.parseInt(o2[2].toString());
                if (null != num1 && null != num2) {
                    return num1.compareTo(num2);
                }
                return 0;
            }
        });
    }

    /**
     * <p>方法描述：选择 危害因素选择弹框</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-29
     **/
    public void selectBadRsnAction() {
        if (this.type == 1) {
            this.selectBadRsns.add(selectBadRsn);
            this.limitVals.remove(selectBadRsn);
        } else if (this.type == 2) {
            this.selectPjBadRsns.add(selectPjBadRsn);
            this.limitVals.remove(selectPjBadRsn);
        }

    }

    /**
     * <p>方法描述：危害因素查询</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-27
     **/
    public void badRsnSearch() {
        String excludeIds = "";
        if (this.type == 1) {
            if (!CollectionUtils.isEmpty(occhethCard.getOcchethCardJcList())) {
                for (TdZwOcchethCardJc cardJc : occhethCard.getOcchethCardJcList()) {
                    excludeIds += "," + cardJc.getFkByBadrsnId().getRid().toString();
                }
            }
        } else if (this.type == 2) {
            if (!CollectionUtils.isEmpty(occhethCard.getOcchethCardPjList())) {
                for (TdZwOcchethCardPj cardPj : occhethCard.getOcchethCardPjList()) {
                    excludeIds += "," + cardPj.getFkByBadrsnId().getRid().toString();
                }
            }
        }
        if (excludeIds.length() > 0) {
            excludeIds = excludeIds.substring(1);
        }
        if (this.type == 1) {
            this.limitVals = occhethCardService.findBadrsnList(occhethCard.getRid(), firstBadRsnId, searchNamOrPy, excludeIds, type);
        } else if (this.type == 2) {
            this.limitVals = occhethCardService.findBadrsnList(occhethCard.getRid(), firstBadRsnId, searchNamOrPy, excludeIds, type);
        }
    }

    /**
     * <p>方法描述：删除 职业病危害因素检测</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-29
     **/
    public void delOcchethCardVo() {
        if (this.type == 1) {
            if (occhethCard == null || tdZwOcchethCardJc == null) {
                return;
            }
            occhethCard.getOcchethCardJcList().remove(tdZwOcchethCardJc);
        } else if (this.type == 2) {
            if (occhethCard == null || tdZwOcchethCardPj == null) {
                return;
            }
            occhethCard.getOcchethCardPjList().remove(tdZwOcchethCardPj);
        }
    }

    /**
     * <p>方法描述：删除 职业病危害因素检测 sub</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-29
     **/
    public void delJcSubAction() {
        if (this.type == 1) {
            this.jcSubList.remove(jcSub);
        } else if (this.type == 2) {
            this.pjSubList.remove(pjSub);
        }
    }

    /**
     * <p>方法描述：检测项目 change 事件</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-29
     **/
    public void changeItemAction(TdZwOcchethCardJcSub jcSub,Boolean flag) {
        if (tdZwOcchethCardJc == null || jcSub.getFkByItemId() == null || jcSub.getFkByItemId().getRid() == null) {
            jcSub.setMsruntTree(new DefaultTreeNode());
            jcSub.setFkByMsruntId(new TsSimpleCode());
            return;
        }

        if (!CollectionUtils.isEmpty(itemList2)) {
            List<TsSimpleCode> msruntList = new ArrayList<>();
            for (Object[] objects : itemList2) {
                if (jcSub.getFkByItemId().getRid() == Integer.parseInt(objects[0].toString())) {
                    if (objects[3] != null && objects[4] != null) {
                        msruntList.add(new TsSimpleCode(Integer.parseInt(objects[3].toString()), objects[4].toString(), null));
                    }
                    if (objects[5] != null && objects[6] != null) {
                        msruntList.add(new TsSimpleCode(Integer.parseInt(objects[5].toString()), objects[6].toString(), null));
                    }
                    break;
                }
            }
            jcSub.setMsruntTree(initMsruntTree(msruntList));
            if(flag){
                //计量单位是一个时，默认选中
                if (!CollectionUtils.isEmpty(msruntList) && msruntList.size() == 1) {
                    jcSub.setFkByMsruntId(msruntList.get(0));
                } else {
                    jcSub.setFkByMsruntId(new TsSimpleCode());
                }
            }
        }
    }

    /**
     * <p>方法描述：检测评价 change 事件</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-29
     **/
    public void changePjItemAction(TdZwOcchethCardPjSub pjSub, Boolean flag) {
        if (tdZwOcchethCardPj == null || pjSub.getFkByItemId() == null || pjSub.getFkByItemId().getRid() == null) {
            pjSub.setMsruntTree(new DefaultTreeNode());
            pjSub.setFkByMsruntId(new TsSimpleCode());
            return;
        }

        if (!CollectionUtils.isEmpty(itemList2)) {
            List<TsSimpleCode> msruntList = new ArrayList<>();
            for (Object[] objects : itemList2) {
                if (pjSub.getFkByItemId().getRid() == Integer.parseInt(objects[0].toString())) {
                    if (objects[3] != null && objects[4] != null) {
                        msruntList.add(new TsSimpleCode(Integer.parseInt(objects[3].toString()), objects[4].toString(), null));
                    }
                    if (objects[5] != null && objects[6] != null) {
                        msruntList.add(new TsSimpleCode(Integer.parseInt(objects[5].toString()), objects[6].toString(), null));
                    }
                    break;
                }
            }
            pjSub.setMsruntTree(initMsruntTree(msruntList));
            //计量单位是一个时，默认选中
            if(flag){
                if (!CollectionUtils.isEmpty(msruntList) && msruntList.size() == 1) {
                    pjSub.setFkByMsruntId(msruntList.get(0));
                } else {
                    pjSub.setFkByMsruntId(new TsSimpleCode());
                }
            }
        }
    }

    /**
     * <p>方法描述：初始化计量单位特殊值码表</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-29
     **/
    private TreeNode initMsruntTree(List<TsSimpleCode> msruntList) {
        this.msruntTree = new DefaultTreeNode("root", null);
        if (!CollectionUtils.isEmpty(msruntList)) {
            for (TsSimpleCode t : msruntList) {
                new DefaultTreeNode(t, this.msruntTree);
            }
        }
        return msruntTree;
    }

    /**
     * <p>方法描述：选择计量单位特殊值</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-29
     **/
    public void onMsruntSelect(NodeSelectEvent event) {
        TreeNode selectNode = event.getTreeNode();
        if (null != selectNode) {
            TsSimpleCode t = (TsSimpleCode) selectNode.getData();
            if (this.type == 1) {
                this.jcSub.setFkByMsruntId(t);
            } else if (this.type == 2) {
                this.pjSub.setFkByMsruntId(t);
            }
        }
    }

    public List<TsZone> getEditZoneList() {
        return editZoneList;
    }

    public void setEditZoneList(List<TsZone> editZoneList) {
        this.editZoneList = editZoneList;
    }

    public Map<Integer, TsZone> getEditZoneMap() {
        return editZoneMap;
    }

    public void setEditZoneMap(Map<Integer, TsZone> editZoneMap) {
        this.editZoneMap = editZoneMap;
    }

    public List<TsSimpleCode> getServicesUndertakenList() {
        return servicesUndertakenList;
    }

    public void setServicesUndertakenList(List<TsSimpleCode> servicesUndertakenList) {
        this.servicesUndertakenList = servicesUndertakenList;
    }

    public String getReportFilePath() {
        return reportFilePath;
    }

    public void setReportFilePath(String reportFilePath) {
        this.reportFilePath = reportFilePath;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<TsSimpleCode> getFirsBadRsntList() {
        return firsBadRsntList;
    }

    public void setFirsBadRsntList(List<TsSimpleCode> firsBadRsntList) {
        this.firsBadRsntList = firsBadRsntList;
    }

    public List<Object[]> getLimitVals() {
        return limitVals;
    }

    public void setLimitVals(List<Object[]> limitVals) {
        this.limitVals = limitVals;
    }

    public Object[] getSelectBadRsn() {
        return selectBadRsn;
    }

    public void setSelectBadRsn(Object[] selectBadRsn) {
        this.selectBadRsn = selectBadRsn;
    }

    public Integer getFirstBadRsnId() {
        return firstBadRsnId;
    }

    public void setFirstBadRsnId(Integer firstBadRsnId) {
        this.firstBadRsnId = firstBadRsnId;
    }

    public String getSearchNamOrPy() {
        return searchNamOrPy;
    }

    public void setSearchNamOrPy(String searchNamOrPy) {
        this.searchNamOrPy = searchNamOrPy;
    }

    public List<Object[]> getItemList2() {
        return itemList2;
    }

    public void setItemList2(List<Object[]> itemList2) {
        this.itemList2 = itemList2;
    }

    public TreeNode getMsruntTree() {
        return msruntTree;
    }

    public void setMsruntTree(TreeNode msruntTree) {
        this.msruntTree = msruntTree;
    }

    public List<Object[]> getSelectBadRsns() {
        return selectBadRsns;
    }

    public void setSelectBadRsns(List<Object[]> selectBadRsns) {
        this.selectBadRsns = selectBadRsns;
    }

    public TdZwOcchethCardJcSub getJcSub() {
        return jcSub;
    }

    public void setJcSub(TdZwOcchethCardJcSub jcSub) {
        this.jcSub = jcSub;
    }

    public List<TdZwOcchethCardJcSub> getJcSubList() {
        return jcSubList;
    }

    public void setJcSubList(List<TdZwOcchethCardJcSub> jcSubList) {
        this.jcSubList = jcSubList;
    }

    public Object[] getSelectPjBadRsn() {
        return selectPjBadRsn;
    }

    public void setSelectPjBadRsn(Object[] selectPjBadRsn) {
        this.selectPjBadRsn = selectPjBadRsn;
    }

    public List<Object[]> getSelectPjBadRsns() {
        return selectPjBadRsns;
    }

    public void setSelectPjBadRsns(List<Object[]> selectPjBadRsns) {
        this.selectPjBadRsns = selectPjBadRsns;
    }

    public TdZwOcchethCardPjSub getPjSub() {
        return pjSub;
    }

    public void setPjSub(TdZwOcchethCardPjSub pjSub) {
        this.pjSub = pjSub;
    }

    public List<TdZwOcchethCardPjSub> getPjSubList() {
        return pjSubList;
    }

    public void setPjSubList(List<TdZwOcchethCardPjSub> pjSubList) {
        this.pjSubList = pjSubList;
    }

    public Integer getPostNumF() {
        return postNumF;
    }

    public void setPostNumF(Integer postNumF) {
        this.postNumF = postNumF;
    }

    public Integer getNumF() {
        return numF;
    }

    public void setNumF(Integer numF) {
        this.numF = numF;
    }

    public Map<Integer, String> getLimitValMap() {
        return limitValMap;
    }

    public void setLimitValMap(Map<Integer, String> limitValMap) {
        this.limitValMap = limitValMap;
    }
}