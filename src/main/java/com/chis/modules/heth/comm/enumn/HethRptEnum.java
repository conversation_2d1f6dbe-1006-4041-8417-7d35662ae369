package com.chis.modules.heth.comm.enumn;

import com.chis.modules.system.enumn.IWritTypeEnum;

/**
 * <p>类描述：职卫报告卡</p>
 * @ClassAuthor qrr,2020年12月30日,HethRptEnum
 */
public enum HethRptEnum implements IWritTypeEnum{
    YSZYBBGK("疑似职业病报告卡", "2031", "HETH_2031");
    

    private HethRptEnum(String writName, String writCode, String writRptCode) {
        this.writName = writName;
        this.writCode = writCode;
        this.writRptCode = writRptCode;
    }

    private String writName;
    private String writCode;
    /**
     * 文书模版编号
     */
    private String writRptCode;

    public String getWritName() {
        return writName;
    }

    public void setWritName(String writName) {
        this.writName = writName;
    }

    public String getWritCode() {
        return writCode;
    }

    public void setWritCode(String writCode) {
        this.writCode = writCode;
    }

    public String getWritRptCode() {
        return writRptCode;
    }

    public void setWritRptCode(String writRptCode) {
        this.writRptCode = writRptCode;
    }
}
