package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-5-12
 */
@Entity
@Table(name = "TD_ZW_BADRSN_STD")
@SequenceGenerator(name = "TdZwBadrsnStd", sequenceName = "TD_ZW_BADRSN_STD_SEQ", allocationSize = 1)
public class TdZwBadrsnStdComm implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsSimpleCode fkByBadrsnId;
	private Integer stopTag;
	private Date modifyDate;
	private Integer modifyManid;
	private Date createDate;
	private Integer createManid;
	private String itemDesc;
	private Integer deterWay;
	private TsSimpleCode fkByOnguardId;
	
	public TdZwBadrsnStdComm() {
	}

	public TdZwBadrsnStdComm(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwBadrsnStd")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "BADRSN_ID")			
	public TsSimpleCode getFkByBadrsnId() {
		return fkByBadrsnId;
	}

	public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
		this.fkByBadrsnId = fkByBadrsnId;
	}	
			
	@Column(name = "STOP_TAG")	
	public Integer getStopTag() {
		return stopTag;
	}

	public void setStopTag(Integer stopTag) {
		this.stopTag = stopTag;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Column(name = "ITEM_DESC")
	public String getItemDesc() {
		return itemDesc;
	}

	public void setItemDesc(String itemDesc) {
		this.itemDesc = itemDesc;
	}

	@Column(name = "DETER_WAY")
	public Integer getDeterWay() {
		return deterWay;
	}

	public void setDeterWay(Integer deterWay) {
		this.deterWay = deterWay;
	}
	@ManyToOne
	@JoinColumn(name = "ONGUARD_STATEID")
	public TsSimpleCode getFkByOnguardId() {
		return fkByOnguardId;
	}

	public void setFkByOnguardId(TsSimpleCode fkByOnguardId) {
		this.fkByOnguardId = fkByOnguardId;
	}

}