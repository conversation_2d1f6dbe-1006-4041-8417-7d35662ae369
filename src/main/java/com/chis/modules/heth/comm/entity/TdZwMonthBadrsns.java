package com.chis.modules.heth.comm.entity;

import javax.persistence.*;

import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2024-8-9
 */
@Entity
@Table(name = "TD_ZW_MONTH_BADRSNS")
@SequenceGenerator(name = "TdZwMonthBadrsns", sequenceName = "TD_ZW_MONTH_BADRSNS_SEQ", allocationSize = 1)
public class TdZwMonthBadrsns implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwMonthCrpt fkByMainId;
    private TsSimpleCode fkByBadrsnId;
    private Integer holdCardNum;
    private Integer bhkNum;
    private Integer suspectedNum;
    private Integer contraindlistNum;
    private Integer sysBhkNum;
    private Integer sysSuspectedNum;
    private Integer sysContraindlistNum;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    public TdZwMonthBadrsns() {
    }

    public TdZwMonthBadrsns(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwMonthBadrsns")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwMonthCrpt getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwMonthCrpt fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "BADRSN_ID")
    public TsSimpleCode getFkByBadrsnId() {
        return fkByBadrsnId;
    }

    public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
        this.fkByBadrsnId = fkByBadrsnId;
    }

    @Column(name = "HOLD_CARD_NUM")
    public Integer getHoldCardNum() {
        return holdCardNum;
    }

    public void setHoldCardNum(Integer holdCardNum) {
        this.holdCardNum = holdCardNum;
    }

    @Column(name = "BHK_NUM")
    public Integer getBhkNum() {
        return bhkNum;
    }

    public void setBhkNum(Integer bhkNum) {
        this.bhkNum = bhkNum;
    }

    @Column(name = "SUSPECTED_NUM")
    public Integer getSuspectedNum() {
        return suspectedNum;
    }

    public void setSuspectedNum(Integer suspectedNum) {
        this.suspectedNum = suspectedNum;
    }

    @Column(name = "CONTRAINDLIST_NUM")
    public Integer getContraindlistNum() {
        return contraindlistNum;
    }

    public void setContraindlistNum(Integer contraindlistNum) {
        this.contraindlistNum = contraindlistNum;
    }

    @Column(name = "SYS_BHK_NUM")
    public Integer getSysBhkNum() {
        return sysBhkNum;
    }

    public void setSysBhkNum(Integer sysBhkNum) {
        this.sysBhkNum = sysBhkNum;
    }

    @Column(name = "SYS_SUSPECTED_NUM")
    public Integer getSysSuspectedNum() {
        return sysSuspectedNum;
    }

    public void setSysSuspectedNum(Integer sysSuspectedNum) {
        this.sysSuspectedNum = sysSuspectedNum;
    }

    @Column(name = "SYS_CONTRAINDLIST_NUM")
    public Integer getSysContraindlistNum() {
        return sysContraindlistNum;
    }

    public void setSysContraindlistNum(Integer sysContraindlistNum) {
        this.sysContraindlistNum = sysContraindlistNum;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

}