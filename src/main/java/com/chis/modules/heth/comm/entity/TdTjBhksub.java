package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * 体检子表
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置<br/>
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 *
 * <p>修订内容：新增字段判断模式、最小值、最大值、诊断结论</p>
 * @ClassReviser qrr,2018年12月26日,TdTjBhksub
 */
@Entity
@Table(name = "TD_TJ_BHKSUB")
@SequenceGenerator(name = "TdTjBhksub_Seq", sequenceName = "TD_TJ_BHKSUB_SEQ", allocationSize = 1)
public class TdTjBhksub implements java.io.Serializable {

    private static final long serialVersionUID = 4319667182363946225L;
    private BigInteger rid;
    private TbTjItems tbTjItems;
    private TdTjBhk tdTjBhk;
    private String msrunt;
    private String itemStdvalue;
    private String itemRst;
    private Integer rgltag;
    private String rstDesc;
    private Integer ifLack;
    private Date chkdat;
    private String chkdoct;
    private Date createDate;
    private Integer createManid;
    /**判断模式*/
    private Integer jdgptn;
    /**最小值*/
    private BigDecimal minval;
    /**最大值*/
    private BigDecimal maxval;
    /**诊断结论*/
    private String diagRst;

    private Integer msruntId;
    /**+结果判定标记20210504*/
    private Integer rstFlag;

    /**
     * 符号ID 20220704
     */
    private TsSimpleCode fkByDataVersionId;

    public TdTjBhksub() {
    }

    public TdTjBhksub(BigInteger rid, TbTjItems tbTjItems, String itemRst,
                      Integer rgltag, Integer ifLack, Date createDate, Integer createManid) {
        this.rid = rid;
        this.tbTjItems = tbTjItems;
        this.itemRst = itemRst;
        this.rgltag = rgltag;
        this.ifLack = ifLack;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    public TdTjBhksub(BigInteger rid, TbTjItems tbTjItems, TdTjBhk tdTjBhk,
                      String msrunt, String itemStdvalue, String itemRst, Integer rgltag,
                      String rstDesc, Integer ifLack, Date chkdat, String chkdoct,
                      Date createDate, Integer createManid) {
        this.rid = rid;
        this.tbTjItems = tbTjItems;
        this.tdTjBhk = tdTjBhk;
        this.msrunt = msrunt;
        this.itemStdvalue = itemStdvalue;
        this.itemRst = itemRst;
        this.rgltag = rgltag;
        this.rstDesc = rstDesc;
        this.ifLack = ifLack;
        this.chkdat = chkdat;
        this.chkdoct = chkdoct;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjBhksub_Seq")
    public BigInteger getRid() {
        return this.rid;
    }

    public void setRid(BigInteger rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "ITEM_ID" )
    public TbTjItems getTbTjItems() {
        return this.tbTjItems;
    }

    public void setTbTjItems(TbTjItems tbTjItems) {
        this.tbTjItems = tbTjItems;
    }

    @ManyToOne
    @JoinColumn(name = "BHK_ID")
    public TdTjBhk getTdTjBhk() {
        return this.tdTjBhk;
    }

    public void setTdTjBhk(TdTjBhk tdTjBhk) {
        this.tdTjBhk = tdTjBhk;
    }

    @Column(name = "MSRUNT", length = 50)
    public String getMsrunt() {
        return this.msrunt;
    }

    public void setMsrunt(String msrunt) {
        this.msrunt = msrunt;
    }

    @Column(name = "ITEM_STDVALUE", length = 100)
    public String getItemStdvalue() {
        return this.itemStdvalue;
    }

    public void setItemStdvalue(String itemStdvalue) {
        this.itemStdvalue = itemStdvalue;
    }

    @Lob
    @Column(name = "ITEM_RST" )
    public String getItemRst() {
        return this.itemRst;
    }

    public void setItemRst(String itemRst) {
        this.itemRst = itemRst;
    }

    @Column(name = "RGLTAG" )
    public Integer getRgltag() {
        return this.rgltag;
    }

    public void setRgltag(Integer rgltag) {
        this.rgltag = rgltag;
    }

    @Column(name = "RST_DESC", length = 10)
    public String getRstDesc() {
        return this.rstDesc;
    }

    public void setRstDesc(String rstDesc) {
        this.rstDesc = rstDesc;
    }

    @Column(name = "IF_LACK" )
    public Integer getIfLack() {
        return this.ifLack;
    }

    public void setIfLack(Integer ifLack) {
        this.ifLack = ifLack;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "CHKDAT", length = 7)
    public Date getChkdat() {
        return this.chkdat;
    }

    public void setChkdat(Date chkdat) {
        this.chkdat = chkdat;
    }

    @Column(name = "CHKDOCT", length = 50)
    public String getChkdoct() {
        return this.chkdoct;
    }

    public void setChkdoct(String chkdoct) {
        this.chkdoct = chkdoct;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }
    @Column(name="JDGPTN")
    public Integer getJdgptn() {
        return jdgptn;
    }

    public void setJdgptn(Integer jdgptn) {
        this.jdgptn = jdgptn;
    }
    @Column(name="MINVAL")
    public BigDecimal getMinval() {
        return minval;
    }

    public void setMinval(BigDecimal minval) {
        this.minval = minval;
    }
    @Column(name="MAXVAL")
    public BigDecimal getMaxval() {
        return maxval;
    }

    public void setMaxval(BigDecimal maxval) {
        this.maxval = maxval;
    }
    @Column(name="DIAG_REST")
    public String getDiagRst() {
        return diagRst;
    }

    public void setDiagRst(String diagRst) {
        this.diagRst = diagRst;
    }

    @Column(name = "MSRUNT_ID")
    public Integer getMsruntId() {
        return msruntId;
    }

    public void setMsruntId(Integer msruntId) {
        this.msruntId = msruntId;
    }

    @Column(name = "RST_FLAG")
    public Integer getRstFlag() {
        return rstFlag;
    }

    public void setRstFlag(Integer rstFlag) {
        this.rstFlag = rstFlag;
    }

    @ManyToOne
    @JoinColumn(name = "DATA_VERSION_ID")
    public TsSimpleCode getFkByDataVersionId() {
        return fkByDataVersionId;
    }

    public void setFkByDataVersionId(TsSimpleCode fkByDataVersionId) {
        this.fkByDataVersionId = fkByDataVersionId;
    }
}
