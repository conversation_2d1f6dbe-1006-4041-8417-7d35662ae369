package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-8-22
 */
@Entity
@Table(name = "TB_YSJC_LIMIT_VAL")
@SequenceGenerator(name = "TbYsjcLimitVal", sequenceName = "TB_YSJC_LIMIT_VAL_SEQ", allocationSize = 1)
public class TbYsjcLimitValComm implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbYsjcJcStdComm fkByStdId;
	private TsSimpleCode fkByBadrsnId;
	private String rsnCnName;
	private String pym;
	private String rsnEnName;
	private String casNo;
	private Integer num;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private Integer sampType;
	
	public TbYsjcLimitValComm() {
	}

	public TbYsjcLimitValComm(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbYsjcLimitVal")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "STD_ID")			
	public TbYsjcJcStdComm getFkByStdId() {
		return fkByStdId;
	}

	public void setFkByStdId(TbYsjcJcStdComm fkByStdId) {
		this.fkByStdId = fkByStdId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BADRSN_ID")			
	public TsSimpleCode getFkByBadrsnId() {
		return fkByBadrsnId;
	}

	public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
		this.fkByBadrsnId = fkByBadrsnId;
	}	
			
	@Column(name = "RSN_CN_NAME")	
	public String getRsnCnName() {
		return rsnCnName;
	}

	public void setRsnCnName(String rsnCnName) {
		this.rsnCnName = rsnCnName;
	}	
			
	@Column(name = "PYM")	
	public String getPym() {
		return pym;
	}

	public void setPym(String pym) {
		this.pym = pym;
	}	
			
	@Column(name = "RSN_EN_NAME")	
	public String getRsnEnName() {
		return rsnEnName;
	}

	public void setRsnEnName(String rsnEnName) {
		this.rsnEnName = rsnEnName;
	}	
			
	@Column(name = "CAS_NO")	
	public String getCasNo() {
		return casNo;
	}

	public void setCasNo(String casNo) {
		this.casNo = casNo;
	}	
			
	@Column(name = "NUM")	
	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
	@Column(name = "SAMP_TYPE")	
	public Integer getSampType() {
		return sampType;
	}

	public void setSampType(Integer sampType) {
		this.sampType = sampType;
	}	
			
}