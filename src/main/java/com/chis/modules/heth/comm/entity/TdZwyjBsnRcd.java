package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-10-30
 */
@Entity
@Table(name = "TD_ZWYJ_BSN_RCD")
@SequenceGenerator(name = "TdZwyjBsnRcd", sequenceName = "TD_ZWYJ_BSN_RCD_SEQ", allocationSize = 1)
public class TdZwyjBsnRcd implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdTjBhk fkByBhkId;
	private Integer dataMark;
	private String errMsg;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjBsnRcd() {
	}

	public TdZwyjBsnRcd(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjBsnRcd")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "BHK_ID")			
	public TdTjBhk getFkByBhkId() {
		return fkByBhkId;
	}

	public void setFkByBhkId(TdTjBhk fkByBhkId) {
		this.fkByBhkId = fkByBhkId;
	}	
			
	@Column(name = "DATA_MARK")	
	public Integer getDataMark() {
		return dataMark;
	}

	public void setDataMark(Integer dataMark) {
		this.dataMark = dataMark;
	}	
			
	@Column(name = "ERR_MSG")	
	public String getErrMsg() {
		return errMsg;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}