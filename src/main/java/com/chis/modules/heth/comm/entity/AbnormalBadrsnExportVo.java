package com.chis.modules.heth.comm.entity;

import java.io.Serializable;

/**
 * 职业健康检查审核情况(体检机构)-退回数据导出部分的危害因素异常VO
 *
 * <AUTHOR>
 * @date 2022/1/28
 */
public class AbnormalBadrsnExportVo implements Serializable {
    private static final long serialVersionUID = -1767869569177359536L;
    private Integer bhkRid;
    /** 不规范标记 */
    /**
     * 不规范标记
     */
    private Integer nostdFlag;
    /** 危害因素rid */
    /**
     * 危害因素rid
     */
    private Integer badRsnRid;
    /** 危害因素名称 */
    /**
     * 危害因素名称
     */
    private String badRsnName;
    /** 项目名称 */
    /**
     * 项目名称
     */
    private String itemName;
    /** 配置表里的项目说明 */
    /**
     * 配置表里的项目说明
     */
    private String itmDesc;
    /** 项目判定方式 */
    /**
     * 项目判定方式
     */
    private Integer deterWay;

    public Integer getBhkRid() {
        return bhkRid;
    }

    public void setBhkRid(Integer bhkRid) {
        this.bhkRid = bhkRid;
    }

    public Integer getNostdFlag() {
        return nostdFlag;
    }

    public void setNostdFlag(Integer nostdFlag) {
        this.nostdFlag = nostdFlag;
    }

    public Integer getBadRsnRid() {
        return badRsnRid;
    }

    public void setBadRsnRid(Integer badRsnRid) {
        this.badRsnRid = badRsnRid;
    }

    public String getBadRsnName() {
        return badRsnName;
    }

    public void setBadRsnName(String badRsnName) {
        this.badRsnName = badRsnName;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItmDesc() {
        return itmDesc;
    }

    public void setItmDesc(String itmDesc) {
        this.itmDesc = itmDesc;
    }

    public Integer getDeterWay() {
        return deterWay;
    }

    public void setDeterWay(Integer deterWay) {
        this.deterWay = deterWay;
    }
}
