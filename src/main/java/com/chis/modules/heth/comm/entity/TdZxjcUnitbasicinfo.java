package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.entity.TsSimpleCode;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-4
 */
@Entity
@Table(name = "TD_ZXJC_UNITBASICINFO")
@SequenceGenerator(name = "TdZxjcUnitbasicinfo", sequenceName = "TD_ZXJC_UNITBASICINFO_SEQ", allocationSize = 1)
public class TdZxjcUnitbasicinfo implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String uuid;
	private Integer year;
	private String unitName;
	private TsZone fkByZoneId;
	private String creditCode;
	private Integer ifKeyIndustry;
	private TsSimpleCode fkByIndustryId;
	private TsSimpleCode fkByEconomicId;
	private TsSimpleCode fkByEnterpriseScaleId;
	private Integer ifDeclare;
	private Integer ifAfterDeclare;
	private Integer ifAnnualUpdate;
	private Integer empNum;
	private Integer externalNum;
	private Integer ifLeadersTrain;
	private Integer ifManagersTrain;
	private Integer trainSum;
	private Integer ifImport;
	private String workNode;
	private Integer ifPreLaunch;
	private Integer ifDesign;
	private Integer ifLaunch;
	private Integer ifAnalysis;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	/** +企业共享表ID */
	private TbTjCrpt fkByCrptId;
	
	public TdZxjcUnitbasicinfo() {
	}

	public TdZxjcUnitbasicinfo(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZxjcUnitbasicinfo")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "UUID")	
	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}	
			
	@Column(name = "YEAR")	
	public Integer getYear() {
		return year;
	}

	public void setYear(Integer year) {
		this.year = year;
	}	
			
	@Column(name = "UNIT_NAME")	
	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ZONE_ID")			
	public TsZone getFkByZoneId() {
		return fkByZoneId;
	}

	public void setFkByZoneId(TsZone fkByZoneId) {
		this.fkByZoneId = fkByZoneId;
	}	
			
	@Column(name = "CREDIT_CODE")	
	public String getCreditCode() {
		return creditCode;
	}

	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}	
			
	@Column(name = "IF_KEY_INDUSTRY")	
	public Integer getIfKeyIndustry() {
		return ifKeyIndustry;
	}

	public void setIfKeyIndustry(Integer ifKeyIndustry) {
		this.ifKeyIndustry = ifKeyIndustry;
	}	
			
	@ManyToOne
	@JoinColumn(name = "INDUSTRY_ID")			
	public TsSimpleCode getFkByIndustryId() {
		return fkByIndustryId;
	}

	public void setFkByIndustryId(TsSimpleCode fkByIndustryId) {
		this.fkByIndustryId = fkByIndustryId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ECONOMIC_ID")			
	public TsSimpleCode getFkByEconomicId() {
		return fkByEconomicId;
	}

	public void setFkByEconomicId(TsSimpleCode fkByEconomicId) {
		this.fkByEconomicId = fkByEconomicId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ENTERPRISE_SCALE_ID")			
	public TsSimpleCode getFkByEnterpriseScaleId() {
		return fkByEnterpriseScaleId;
	}

	public void setFkByEnterpriseScaleId(TsSimpleCode fkByEnterpriseScaleId) {
		this.fkByEnterpriseScaleId = fkByEnterpriseScaleId;
	}	
			
	@Column(name = "IF_DECLARE")	
	public Integer getIfDeclare() {
		return ifDeclare;
	}

	public void setIfDeclare(Integer ifDeclare) {
		this.ifDeclare = ifDeclare;
	}	
			
	@Column(name = "IF_AFTER_DECLARE")	
	public Integer getIfAfterDeclare() {
		return ifAfterDeclare;
	}

	public void setIfAfterDeclare(Integer ifAfterDeclare) {
		this.ifAfterDeclare = ifAfterDeclare;
	}	
			
	@Column(name = "IF_ANNUAL_UPDATE")	
	public Integer getIfAnnualUpdate() {
		return ifAnnualUpdate;
	}

	public void setIfAnnualUpdate(Integer ifAnnualUpdate) {
		this.ifAnnualUpdate = ifAnnualUpdate;
	}	
			
	@Column(name = "EMP_NUM")	
	public Integer getEmpNum() {
		return empNum;
	}

	public void setEmpNum(Integer empNum) {
		this.empNum = empNum;
	}	
			
	@Column(name = "EXTERNAL_NUM")	
	public Integer getExternalNum() {
		return externalNum;
	}

	public void setExternalNum(Integer externalNum) {
		this.externalNum = externalNum;
	}	
			
	@Column(name = "IF_LEADERS_TRAIN")	
	public Integer getIfLeadersTrain() {
		return ifLeadersTrain;
	}

	public void setIfLeadersTrain(Integer ifLeadersTrain) {
		this.ifLeadersTrain = ifLeadersTrain;
	}	
			
	@Column(name = "IF_MANAGERS_TRAIN")	
	public Integer getIfManagersTrain() {
		return ifManagersTrain;
	}

	public void setIfManagersTrain(Integer ifManagersTrain) {
		this.ifManagersTrain = ifManagersTrain;
	}	
			
	@Column(name = "TRAIN_SUM")	
	public Integer getTrainSum() {
		return trainSum;
	}

	public void setTrainSum(Integer trainSum) {
		this.trainSum = trainSum;
	}	
			
	@Column(name = "IF_IMPORT")	
	public Integer getIfImport() {
		return ifImport;
	}

	public void setIfImport(Integer ifImport) {
		this.ifImport = ifImport;
	}	
			
	@Column(name = "WORK_NODE")	
	public String getWorkNode() {
		return workNode;
	}

	public void setWorkNode(String workNode) {
		this.workNode = workNode;
	}	
			
	@Column(name = "IF_PRE_LAUNCH")	
	public Integer getIfPreLaunch() {
		return ifPreLaunch;
	}

	public void setIfPreLaunch(Integer ifPreLaunch) {
		this.ifPreLaunch = ifPreLaunch;
	}	
			
	@Column(name = "IF_DESIGN")	
	public Integer getIfDesign() {
		return ifDesign;
	}

	public void setIfDesign(Integer ifDesign) {
		this.ifDesign = ifDesign;
	}	
			
	@Column(name = "IF_LAUNCH")	
	public Integer getIfLaunch() {
		return ifLaunch;
	}

	public void setIfLaunch(Integer ifLaunch) {
		this.ifLaunch = ifLaunch;
	}	
			
	@Column(name = "IF_ANALYSIS")	
	public Integer getIfAnalysis() {
		return ifAnalysis;
	}

	public void setIfAnalysis(Integer ifAnalysis) {
		this.ifAnalysis = ifAnalysis;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@ManyToOne
	@JoinColumn(name = "CRPT_ID")
	public TbTjCrpt getFkByCrptId() {
		return fkByCrptId;
	}

	public void setFkByCrptId(TbTjCrpt fkByCrptId) {
		this.fkByCrptId = fkByCrptId;
	}
			
}