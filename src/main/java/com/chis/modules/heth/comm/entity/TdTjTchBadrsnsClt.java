package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-7-21
 */
@Entity
@Table(name = "TD_TJ_TCH_BADRSNS_CLT")
@SequenceGenerator(name = "TdTjTchBadrsnsClt", sequenceName = "TD_TJ_TCH_BADRSNS_CLT_SEQ", allocationSize = 1)
public class TdTjTchBadrsnsClt implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdTjBhkClt fkByBhkId;
	private TsSimpleCode fkByBadrsnId;
	private Date createDate;
	private Integer createManid;
	
	public TdTjTchBadrsnsClt() {
	}

	public TdTjTchBadrsnsClt(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjTchBadrsnsClt")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "BHK_ID")			
	public TdTjBhkClt getFkByBhkId() {
		return fkByBhkId;
	}

	public void setFkByBhkId(TdTjBhkClt fkByBhkId) {
		this.fkByBhkId = fkByBhkId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BADRSN_ID")			
	public TsSimpleCode getFkByBadrsnId() {
		return fkByBadrsnId;
	}

	public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
		this.fkByBadrsnId = fkByBadrsnId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
}