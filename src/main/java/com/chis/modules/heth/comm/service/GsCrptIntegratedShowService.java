package com.chis.modules.heth.comm.service;

import com.chis.common.utils.CollectionUtil;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.entity.TbTjCrptWarnComm;
import com.chis.modules.heth.comm.entity.TdTjCrptTask;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 用人单位综合展示
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional
public class GsCrptIntegratedShowService extends AbstractTemplate {
    /**
     * 根据rid更新企业表启用状态（停用时更新停用原因）
     *
     * @param rid     企业表rid
     * @param state   启用状态
     * @param stopRsn 停用原因
     */
    public void updateCrptDelMarkByRid(Integer rid, Integer state, String stopRsn) {
        if (rid == null || state == null || (state == 1 && StringUtils.isBlank(stopRsn))) {
            return;
        }
        String sql = "UPDATE TB_TJ_CRPT SET DEL_MARK = :state, STOP_RSN = :stopRsn, OPERATION_STATUS = :operationStatus WHERE RID = :rid";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", rid);
        paramMap.put("state", state);
        paramMap.put("stopRsn", stopRsn);
        paramMap.put("operationStatus", state == 1 ? 0 : 1);
        this.executeSql(sql, paramMap);
    }

    /**
     * 根据企业RID批量获取企业
     *
     * @param rids 企业RID
     * @return 企业
     */
    public List<TbTjCrpt> findCrptListByRids(String rids) {
        String hql = "select t from TbTjCrpt t where rid in(" + rids + ")";
        List<TbTjCrpt> fixCrptList = this.findByHql(hql, TbTjCrpt.class);
        if (ObjectUtil.isEmpty(fixCrptList)) {
            return new ArrayList<>();
        }
        return fixCrptList;
    }

    /**
     * 创建修订任务
     *
     * @param crptTask    修订任务
     * @param crptRidList 需要修订的企业RID
     */
    public void createCrptFixTask(TdTjCrptTask crptTask, List<Integer> crptRidList) {
        this.upsertEntity(crptTask);
        String sql = "UPDATE TB_TJ_CRPT SET UPDATE_STATE = 1 WHERE RID IN (:ridList)";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("ridList", crptRidList);
        this.executeSql(sql, paramMap);
    }

    /**
     * 查询是否存在同社会信用代码的主体企业，排除当前企业
     *
     * @param creditCode  社会信用代码
     * @param crptRidList 当前企业
     * @return 存在返回true
     */
    public boolean hasSameCreditCode(String creditCode, List<Integer> crptRidList) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("creditCode", creditCode);
        paramMap.put("crptRidList", crptRidList);
        String sql = "SELECT COUNT(1) FROM TB_TJ_CRPT " +
                "WHERE IF_SUB_ORG = 0 AND INSTITUTION_CODE = :creditCode AND RID NOT IN (:crptRidList)";
        int countBySql = this.findCountBySql(sql, paramMap);
        return countBySql > 0;
    }

    /**
     * 查询主体企业下分支机构是否存在同名称的
     *
     * @param crptName    名称
     * @param crptRidList 主体企业
     * @return 存在返回true
     */
    public boolean hasSameNameForSubOrg(String crptName, List<Integer> crptRidList) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("crptName", crptName);
        paramMap.put("crptRidList", crptRidList);
        String sql = "SELECT COUNT(1) FROM TB_TJ_CRPT " +
                "WHERE CRPT_NAME = :crptName AND UPPER_UNIT_ID IN (:crptRidList)";
        int countBySql = this.findCountBySql(sql, paramMap);
        if (countBySql > 0) {
            return true;
        }
        paramMap = new HashMap<>();
        paramMap.put("crptRidList", crptRidList);
        sql = "SELECT CRPT_NAME, COUNT(1) FROM TB_TJ_CRPT " +
                "WHERE UPPER_UNIT_ID IN (:crptRidList) " +
                "GROUP BY CRPT_NAME HAVING COUNT(1) > 1";
        return ObjectUtil.isNotEmpty(this.findDataBySql(sql, paramMap));
    }

    /**
     * 查询是否存在同名称同社会信用代码企业，排除当前企业
     *
     * @param crptName    名称
     * @param creditCode  社会信用代码
     * @param crptRidList 当前企业
     * @return 存在返回true
     */
    public boolean hasSameNameCreditCode(String crptName, String creditCode, List<Integer> crptRidList) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("crptName", crptName);
        paramMap.put("creditCode", creditCode);
        paramMap.put("crptRidList", crptRidList);
        String sql = "SELECT COUNT(1) FROM TB_TJ_CRPT " +
                "WHERE CRPT_NAME = :crptName AND INSTITUTION_CODE = :creditCode AND RID NOT IN (:crptRidList)";
        int countBySql = this.findCountBySql(sql, paramMap);
        return countBySql > 0;
    }

    /**
    * @description: 通过rids批量统计
    * <AUTHOR>
    */
    public List<Object[]> findBatchCrptWarnByRids(List<Integer> rids) {
         if (CollectionUtils.isEmpty(rids)) {
             return new ArrayList<>();
         }
         StringBuffer sql=new StringBuffer();
         sql.append(" select T.CRPT_ID,T.STATE,to_char(T.WARN_DATE,'yyyy-mm-dd') as WARN_DATE,T2.EXTENDS1 ");
         sql.append(" FROM TB_TJ_CRPT_WARN T ");
         sql.append(" LEFT JOIN TS_SIMPLE_CODE T1 on T.WARN_ID = T1.RID ");
         sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 on T.RESULT_ID = T2.RID ");
         sql.append(" WHERE T.CRPT_ID IN(").append(StringUtils.list2string(rids, ",")).append(") ");
         return this.findDataBySqlNoPage(sql.toString(),null);
    }

    /**
     *  <p>方法描述：职业卫生技术服务报告卡数据</p>
     * @MethodAuthor hsj 2023-09-26 15:45
     */
    public List<Object[]> initOcchethCard(Integer rid) {
        if(ObjectUtil.isNull(rid)){
            return new ArrayList<>();
        }
        StringBuffer sql=new StringBuffer();
        sql.append(" WITH exTab AS (");
        sql.append(" SELECT  A.RID, LISTAGG(A.ex1, ',') WITHIN GROUP ( ORDER BY A.ex1) AS ex3 ,sum(A.POST_NUM) AS POST_NUM, sum(A.NUM) AS num");
        sql.append(" FROM  (SELECT T1.RID , (CASE WHEN sum(T.NUM) > 0 THEN T3.EXTENDS1 ELSE 0 END) AS ex1 , sum(T.POST_NUM) AS POST_NUM, sum(T.NUM) AS num ");
        sql.append(" FROM TD_ZW_OCCHETH_CARD_JC T ");
        sql.append(" LEFT JOIN TD_ZW_OCCHETH_CARD T1 ON T.MAIN_ID =T1.RID ");
        sql.append(" LEFT JOIN TB_YSJC_LIMIT_VAL T2 ON T.BADRSN_ID =T2.RID ");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T2.BADRSN_ID =T3.RID ");
        sql.append(" WHERE T1.CRPT_ID =").append(rid);
        sql.append(" GROUP BY T1.RID,T3.EXTENDS1)A GROUP  BY A.RID)");
        sql.append(" SELECT T.RID ,T.RPT_DATE,T1.POST_NUM,T1.NUM,'否','否','否' ,T1.ex3");
        sql.append(" FROM  TD_ZW_OCCHETH_CARD T ");
        sql.append(" LEFT JOIN exTab T1 ON T.RID = T1.RID");
        sql.append(" WHERE T.STATE =1 AND T.IF_BADRSN_JC  =1 AND T.DEL_MARK = 0");
        sql.append(" AND T.CRPT_ID = ").append(rid);
        sql.append(" ORDER BY T.RPT_DATE DESC,T.RID DESC");
        List<Object[]> list =  this.findDataBySqlNoPage(sql.toString(),null);
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        for(Object[] obj : list){
            String ex3 = ObjectUtil.toStr(obj[7]);
            if(StringUtils.isEmpty(ex3)){
                continue;
            }
            List<String> strList = StringUtils.string2list(ex3,",");
            if(strList.contains("2")){
                obj[4] = "是";
            }
            if(strList.contains("3")){
                obj[5] = "是";
            }
            if(strList.contains("1")){
                obj[6] = "是";
            }
        }
        return list;
    }

    /**
     *  <p>方法描述：放射卫生技术服务报告卡数据</p>
     * @MethodAuthor hsj 2023-09-26 16:25
     */
    public List<Object[]> initSrvorgCard(Integer rid) {
        if(ObjectUtil.isNull(rid)){
            return new ArrayList<>();
        }
        TbTjCrpt crpt =  (TbTjCrpt) this.find(TbTjCrpt.class, rid);
        if(crpt == null || crpt.getRid() == null){
            return new ArrayList<>();
        }
        StringBuffer sql=new StringBuffer();
        Map<String, Object> paramMap = new HashMap<>();
        sql.append(" SELECT T.rid,T.RPT_DATE,'',T.IF_FSWS_FH_JC ,T.IF_ITEM_PJ,T.IF_DOSE_MONIT,T.IF_FH_INST_JC ");
        sql.append(" FROM  TD_ZW_SRVORG_CARD T");
        sql.append(" LEFT JOIN  TS_UNIT T1 ON T.UNIT_ID = T1.RID");
        sql.append(" WHERE T.STATE =1 AND T.DEL_MARK = 0");
        sql.append(" AND T1.CREDIT_CODE =:institutionCode ");
        paramMap.put("institutionCode",crpt.getInstitutionCode());
        sql.append(" AND T1.IF_SUB_ORG = ").append(crpt.getIfSubOrg());
        if(crpt.getIfSubOrg() != null && "1".equals(String.valueOf(crpt.getIfSubOrg()))){
            sql.append(" AND T1.UNITNAME =:UNITNAME ");
            paramMap.put("UNITNAME",crpt.getCrptName());
        }
        sql.append(" ORDER BY T.RPT_DATE DESC,T.RID DESC");
        List<Object[]> list =  this.findDataBySqlNoPage(sql.toString(),paramMap);
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        for(Object[] obj : list){
            List<String> strList = new ArrayList<>();
            if("1".equals(ObjectUtil.toStr(obj[3]))){
                strList.add("放射卫生防护检测");
            }
            if("1".equals(ObjectUtil.toStr(obj[4]))){
                strList.add("放射诊疗建设项目评价");
            }
            if("1".equals(ObjectUtil.toStr(obj[5]))){
                strList.add("个人剂量监测");
            }
            if("1".equals(ObjectUtil.toStr(obj[6]))){
                strList.add("放射防护器材和含放射性产品检测");
            }
            if(!CollectionUtils.isEmpty(strList)){
                obj[2] = StringUtils.list2string(strList,"，");
            }
        }
        return list;
    }

    /**
     * @description: 根据企业rid查询预警信息
     * <AUTHOR>
     */
    public List<TbTjCrptWarnComm> findCrptWarnByRid(Integer rid) {
        StringBuffer hql=new StringBuffer();
        hql.append(" select T from TbTjCrptWarnComm T where T.fkByCrptId.rid=").append(rid);
        hql.append(" order by T.warnDate desc,T.rid desc ");
        return this.findByHql(hql.toString(), TbTjCrptWarnComm.class);
    }

    /**
     * 根据用工单位统计年度职业健康检查数据
     *
     * @param crptId 用工单位RID
     * @return 职业健康检查数据
     */
    public List<Object[]> selectBhkDataYearListByCrptId(Integer crptId) {
        String sql = "SELECT TT.YEAR, " +
                "       COUNT(1)               AS PERSON_ID, " +
                "       COUNT(TT.FC)           AS FC, " +
                "       COUNT(TT.HX)           AS HX, " +
                "       COUNT(TT.WL)           AS WL, " +
                "       COUNT(TT.FS)           AS FS, " +
                "       COUNT(TT.SW)           AS SW, " +
                "       COUNT(TT.QT)           AS QT, " +
                "       COUNT(TT.IF_WRKTABU)   AS IF_WRKTABU, " +
                "       COUNT(TT.IF_TARGETDIS) AS IF_TARGETDIS " +
                "FROM (SELECT T.YEAR, " +
                "             T.PERSON_ID, " +
                "             DECODE(COUNT(T.FC), 0, NULL, 1)           AS FC, " +
                "             DECODE(COUNT(T.HX), 0, NULL, 1)           AS HX, " +
                "             DECODE(COUNT(T.WL), 0, NULL, 1)           AS WL, " +
                "             DECODE(COUNT(T.FS), 0, NULL, 1)           AS FS, " +
                "             DECODE(COUNT(T.SW), 0, NULL, 1)           AS SW, " +
                "             DECODE(COUNT(T.QT), 0, NULL, 1)           AS QT, " +
                "             DECODE(COUNT(T.IF_WRKTABU), 0, NULL, 1)   AS IF_WRKTABU, " +
                "             DECODE(COUNT(T.IF_TARGETDIS), 0, NULL, 1) AS IF_TARGETDIS " +
                "      FROM (SELECT EXTRACT(YEAR FROM B.BHK_DATE)                 AS YEAR, " +
                "                   B.PERSON_ID, " +
                "                   CASE WHEN SC.EXTENDS3 = '1' THEN 1 END          AS FC, " +
                "                   CASE WHEN SC.EXTENDS3 = '2' THEN 1 END          AS HX, " +
                "                   CASE WHEN SC.EXTENDS3 = '3' THEN 1 END          AS WL, " +
                "                   CASE WHEN SC.EXTENDS3 = '4' THEN 1 END          AS FS, " +
                "                   CASE WHEN SC.EXTENDS3 = '5' THEN 1 END          AS SW, " +
                "                   CASE WHEN SC.EXTENDS3 = '6' THEN 1 END          AS QT, " +
                "                   CASE WHEN NVL(B.IF_WRKTABU, 0) = 1 THEN 1 END   AS IF_WRKTABU, " +
                "                   CASE WHEN NVL(B.IF_TARGETDIS, 0) = 1 THEN 1 END AS IF_TARGETDIS " +
                "            FROM TD_TJ_BHK B " +
                "                     LEFT JOIN TD_TJ_BADRSNS BR ON B.RID = BR.BHK_ID " +
                "                     LEFT JOIN TS_SIMPLE_CODE SC ON BR.BADRSN_ID = SC.RID " +
                "            WHERE B.ENTRUST_CRPT_ID = :crptId) T " +
                "      GROUP BY T.YEAR, T.PERSON_ID) TT " +
                "GROUP BY TT.YEAR " +
                "ORDER BY TT.YEAR DESC ";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("crptId", crptId);
        return CollectionUtil.castList(Object[].class, this.findDataBySqlNoPage(sql, paramMap));
    }

    /**
     * @description: 通过企业rid获取 申报信息
     * <AUTHOR>
     */
    public List<Object[]> findUnitBasicInfoByRid(Integer rid) {
        StringBuffer sql=new StringBuffer();
        sql.append(" select T.RID,T.DECLARE_YEAR,to_char(T.DECLARE_DATE,'yyyy-mm-dd') as DECLARE_DATE,T.DECLARE_TYPE,T.EMP_NUM ");
        sql.append("        ,T.VICTIMS_NUM,T.EXTERNAL_NUM,T1.IF_LEADERS_TRAIN,T1.IF_MANAGERS_TRAIN,T2.IFAT,T3.IFHEA, ");
        sql.append("        T4.IFHF_DUST,T4.IFHF_CHEMISTRY,T4.IFHF_PHYSICS,T4.IFHF_RADIOACTIVITY,T4.IFHF_BIOLOGY,T4.IFHF_OTHER ");
        sql.append(" from TD_ZY_UNITBASICINFO T ");
        sql.append(" left join TD_ZY_TRAIN_SITUATION T1 on T.RID=T1.MAIN_ID ");
        sql.append(" left join TD_ZY_UNITHARMFACTORCHECK T2 on T.RID=T2.MAIN_ID ");
        sql.append(" left join TD_ZY_UNITHEALTHCUSTODY T3 on T.RID=T3.MAIN_ID ");
        sql.append(" left join TD_ZY_UNITFACTORCROWD T4 on T.RID=T4.MAIN_ID ");
        sql.append(" where T.CRPT_ID= ").append(rid);
        sql.append(" order by T.DECLARE_DATE desc ");
        return this.findDataBySqlNoPage(sql.toString(),null);
    }

    /**
     * @description: 通过企业rid获取 场所检测
     * <AUTHOR>
     */
    public List<Object[]> findUnitBasicInfoJcByRid(Integer rid) {
        StringBuffer sql=new StringBuffer();
        sql.append(" with table1 as (select T.RID, ");
        sql.append("         T.YEAR, ");
        sql.append("         T.EMP_NUM, ");
        sql.append("         T.EXTERNAL_NUM, ");
        sql.append("         T1.CONTACT_TOTAL_PEOPLES, ");
        sql.append("         T.IF_LEADERS_TRAIN, ");
        sql.append("         T.IF_MANAGERS_TRAIN, ");
        sql.append("         T2.IFAT, ");
        sql.append("         T3.IFHEA ");
        sql.append("         from TD_ZXJC_UNITBASICINFO T ");
        sql.append("         LEFT JOIN TD_ZXJC_UNITFACTORCROWD T1 on T.RID = T1.MAIN_ID ");
        sql.append("         left join TD_ZXJC_UNITHARM_CHK T2 on T.RID = T2.MAIN_ID ");
        sql.append("         left join TD_ZXJC_UNIT_HETH_CUS T3 on T.RID = T3.MAIN_ID ");
        sql.append("         where T.CRPT_ID = ").append(rid).append("), ");
        sql.append("         table2 as (select T.RID, count(T.RID) as num" );
        sql.append("         from table1 T ");
        sql.append("         left join TD_ZXJC_RESULT_PRO T1 on T.RID = T1.MAIN_ID ");
        sql.append("         where T1.IF_QUALIFIED = 0 ");
        sql.append("         group by T.RID) ");
        sql.append(" select T.*, nvl(T1.num, 0) as num ");
        sql.append(" from table1 T ");
        sql.append(" left join table2 T1 on T.RID = T1.RID ");
        sql.append(" order by T.YEAR desc ");
        return this.findDataBySqlNoPage(sql.toString(),null);
    }

    /**
     * 根据用工单位统计年度职业病诊断数据
     *
     * @param crptId     用工单位RID
     * @param headerList 表格表头
     * @return 职业病诊断数据
     */
    public List<List<String>> selectDiagDataList(Integer crptId, List<String> headerList) {
        //按年份统计-职业病诊断数据-申请诊断数&确诊职业病数
        String sql = "SELECT YEAR, COUNT(APPLY_NUM) AS APPLY_NUM, COUNT(CONFIRM_NUM) AS CONFIRM_NUM " +
                "FROM (SELECT YEAR, " +
                "             CASE WHEN T.STATE_MARK > 1 THEN 1 END                  AS APPLY_NUM, " +
                "             CASE WHEN T.STATE_MARK > 4 AND T.IS_DIS = 1 THEN 1 END AS CONFIRM_NUM " +
                "      FROM (SELECT EXTRACT(YEAR FROM O.APPLY_DATE) AS YEAR, O.STATE_MARK, O.IS_DIS, O.OCCDISE_ID " +
                "            FROM TD_ZW_OCCDISCASE O " +
                "            WHERE NVL(DEL_MARK, 0) = 0 " +
                "              AND EXTRACT(YEAR FROM O.APPLY_DATE) IS NOT NULL " +
                "              AND O.CRPT_ID = :crptId) T) " +
                "GROUP BY YEAR " +
                "ORDER BY YEAR DESC";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("crptId", crptId);
        List<Object[]> dataList1 = CollectionUtil.castList(Object[].class, this.findDataBySqlNoPage(sql, paramMap));
        //职业病诊断分类和目录-大类&EXTENDS1 = 1
        sql = "SELECT SC.RID, SC.CODE_NAME " +
                "FROM TS_SIMPLE_CODE SC LEFT JOIN TS_CODE_TYPE CT ON SC.CODE_TYPE_ID = CT.RID " +
                "WHERE SC.IF_REVEAL = 1 AND CT.CODE_TYPE_NAME = '5026' " +
                "  AND SC.CODE_LEVEL_NO NOT LIKE '%.%' AND SC.EXTENDS1 = 1 " +
                "ORDER BY SC.CODE_LEVEL_NO, SC.NUM";
        List<Object[]> dataList2 = CollectionUtil.castList(Object[].class, this.findDataBySqlNoPage(sql, null));
        Map<String, String> badrsnMap = new LinkedHashMap<>();
        for (Object[] badrsn : dataList2) {
            String key = StringUtils.objectToString(badrsn[0]);
            if (badrsnMap.containsKey(key)) {
                continue;
            }
            badrsnMap.put(key, StringUtils.objectToString(badrsn[1]));
        }
        //按年份统计确诊职业病种类大类
        sql = "WITH OCCDISCASE AS (SELECT EXTRACT(YEAR FROM O.APPLY_DATE) AS YEAR, O.STATE_MARK, O.IS_DIS, O.OCCDISE_ID " +
                "                    FROM TD_ZW_OCCDISCASE O " +
                "                    WHERE NVL(DEL_MARK, 0) = 0 " +
                "                      AND EXTRACT(YEAR FROM O.APPLY_DATE) IS NOT NULL AND O.CRPT_ID = :crptId) " +
                "SELECT O.YEAR, O.OCCDISE_ID, COUNT(1) COUNT " +
                "FROM OCCDISCASE O " +
                "WHERE O.STATE_MARK > 4 " +
                "  AND O.IS_DIS = 1 " +
                "GROUP BY O.YEAR, O.OCCDISE_ID";
        List<Object[]> dataList3 = CollectionUtil.castList(Object[].class, this.findDataBySqlNoPage(sql, paramMap));
        Set<String> badrsnIdSet = new LinkedHashSet<>();
        Map<String, String> badrsnYearMap = new HashMap<>();
        for (Object[] badrsn : dataList3) {
            String badrsnId = StringUtils.objectToString(badrsn[1]);
            String key = StringUtils.objectToString(badrsn[0]) + "&" + badrsnId;
            if (badrsnYearMap.containsKey(key)) {
                continue;
            }
            badrsnIdSet.add(badrsnId);
            badrsnYearMap.put(key, StringUtils.objectToString(badrsn[2]));
        }
        for (Map.Entry<String, String> entry : badrsnMap.entrySet()) {
            String key = entry.getKey();
            if (!badrsnIdSet.contains(key)) {
                continue;
            }
            headerList.add(entry.getValue());
        }
        List<List<String>> dataList = new ArrayList<>();
        for (Object[] data1 : dataList1) {
            String key1 = StringUtils.objectToString(data1[0]);
            List<String> list = new ArrayList<>();
            list.add(key1);
            list.add(StringUtils.objectToString(data1[1]));
            list.add(StringUtils.objectToString(data1[2]));

            for (Map.Entry<String, String> entry : badrsnMap.entrySet()) {
                String badrsnId = entry.getKey();
                if (!badrsnIdSet.contains(badrsnId)) {
                    continue;
                }
                String key2 = key1 + "&" + badrsnId;
                if (badrsnYearMap.containsKey(key2)) {
                    list.add(badrsnYearMap.get(key2));
                } else {
                    list.add("0");
                }
            }
            dataList.add(list);
        }
        return dataList;
    }

    /**
     * 职业病诊断数据弹出框-当前年份所有未标删、状态大于1的诊断数据
     *
     * @param year   年
     * @param crptId 企业ID
     * @return 诊断数据
     */
    public List<Object[]> selectDiagYearDataList(String year, Integer crptId) {
        String sql = "SELECT O.ARCH_CODE, " +
                "       O.IDC, " +
                "       O.PERSONNEL_NAME, " +
                "       O.SEX, " +
                "       O.APPLY_DATE, " +
                "       SC.CODE_NAME, " +
                "       O.OCC_APP_NAM, " +
                "       O.IS_DIS " +
                "FROM TD_ZW_OCCDISCASE O LEFT JOIN TS_SIMPLE_CODE SC ON O.OCCDISE_APPLYID = SC.RID " +
                "WHERE NVL(O.DEL_MARK, 0) = 0 AND O.STATE_MARK > 1 " +
                "  AND EXTRACT(YEAR FROM O.APPLY_DATE) = :year AND O.CRPT_ID = :crptId " +
                "ORDER BY O.APPLY_DATE DESC, O.RID DESC ";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("year", year);
        paramMap.put("crptId", crptId);
        List<Object[]> dataList = CollectionUtil.castList(Object[].class, this.findDataBySqlNoPage(sql, paramMap));
        for (Object[] data : dataList) {
            data[1] = StringUtils.encryptIdc(StringUtils.objectToString(data[1]));
            data[5] = StringUtils.objectToString(data[5]);
            String occAppNam = StringUtils.objectToString(data[6]);
            if (StringUtils.isNotBlank(occAppNam)) {
                data[5] = data[5] + "（" + occAppNam + "）";
            }
        }
        return dataList;
    }
}
