package com.chis.modules.heth.comm.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.faces.model.SelectItem;

import com.chis.modules.heth.comm.service.TdZwBgkFlowServiceImpl;
import com.chis.modules.system.utils.HolidayUtil;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.FileUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdZwBgkFlow;
import com.chis.modules.heth.comm.entity.TdZwBgkLastSta;
import com.chis.modules.heth.comm.entity.TdZwGbz188NostdComm;
import com.chis.modules.heth.comm.service.ZwReportCardCheckCommServiceImpl;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;

/**
 * @Description : 报告卡审核基类
 * @ClassAuthor : anjing
 * @Date : 2020/7/22 13:41
 **/
public abstract class AbstractReportCardCheckNewBean extends FacesEditBean {

    /**业务主键*/
    protected Integer rid;
    protected TdZwBgkLastSta newFlow = new TdZwBgkLastSta();
    /**退回原因只读*/
    private Boolean readOnly = false;
    /**
     * 当前登录人地区级别<pre>2: 省级</pre><pre>3: 市级</pre><pre>4: 区县级</pre>
     */
    protected Integer zoneType;
    /**是否审核界面*/
    protected Boolean ifCheck = true;
    /**状态*/
    protected String[] states;
    protected List<SelectItem> stateList = new ArrayList<>();
    /** 审核期限 */
	protected String limitTime;
	/**true：处理期限，false：无处理期限*/
	protected String ifshowdeadline;
	/**是否市级直属*/
	protected boolean ifCityDirect;
	/**审核结果*/
	protected String checkResult;
	/**显示提示信息*/
    private String tipInfo;

    /**选择的结果集*/
    protected List<Object[]> selectEntitys;
    /**历次审核意见*/
    protected List<TdZwBgkFlow> bgkFlows;
    /**
     * 市/省级平台 <pre>1: 市级平台</pre><pre>2: 省级平台</pre>
     */
    protected String platVersion;
    /**
     * 审核级别<pre>3: 3级审核</pre><pre>2: 2级审核</pre>
     */
    protected String checkLevel;

    protected SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    protected ZwReportCardCheckCommServiceImpl cardCheckServiceImpl = SpringContextHolder.getBean(ZwReportCardCheckCommServiceImpl.class);
    protected TdZwBgkFlowServiceImpl flowService = SpringContextHolder.getBean(TdZwBgkFlowServiceImpl.class);
    /**
     * 审核级别<pre>1: 初审(区级用户)</pre><pre>2: 复审(三级审核&市级用户)</pre><pre>3: 终审(二级审核&市级用户||省级用户)</pre>
     */
    protected Integer level;
    public AbstractReportCardCheckNewBean() {
        limitTime = PropertyUtils.getValueWithoutException("limitTime");
        ifshowdeadline = PropertyUtils.getValueWithoutException("ifshowdeadline");
        platVersion = PropertyUtils.getValueWithoutException("platVersion");
        checkLevel = PropertyUtils.getValueWithoutException("checkLevel");
        TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
        if (tsZone == null) {
            tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
        }
        if (tsZone.getRealZoneType() == null) {
            this.zoneType = tsZone.getZoneType().intValue();
        } else {
            this.zoneType = tsZone.getRealZoneType().intValue();
        }
        this.tipInfo = "批量审核：若存在审核意见为空的数据则默认置为【"+PropertyUtils.getValue("defaultAuditAdv")+"】；审核人默认为当前登录人。";

        if (this.zoneType == 3 && "3".equals(this.checkLevel)) {
            this.level = 2;
        } else if (this.zoneType > 3) {
            this.level = 1;
        } else {
            this.level = 3;
        }

        // 状态初始化
        initSearchStates();
    }

    /**
    * @Description : 初始化状态
    * @MethodAuthor: anjing
    * @Date : 2020/7/22 13:49
    **/
    private void initSearchStates() {
        boolean checkLevel2 = "2".equals(this.checkLevel);
        boolean checkLevel3 = "3".equals(this.checkLevel);
        boolean platVersion1 = "1".equals(this.platVersion);
        boolean platVersion2 = "2".equals(this.platVersion);

        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem("0", "待提交"));
        this.stateList.add(new SelectItem("1", "区县级待审"));
        this.stateList.add(new SelectItem("2", "区县级退回"));
        if (checkLevel3) {
            this.stateList.add(new SelectItem("3", "市级待审"));
            this.stateList.add(new SelectItem("4", "市级退回"));
            this.stateList.add(new SelectItem("5", "省级待审"));
            this.stateList.add(new SelectItem("6", "省级退回"));
            this.stateList.add(new SelectItem("7", "省级通过"));
        } else if (checkLevel2 && platVersion1) {
            this.stateList.add(new SelectItem("5", "市级待审"));
            this.stateList.add(new SelectItem("6", "市级退回"));
            this.stateList.add(new SelectItem("7", "市级通过"));
        } else if (checkLevel2 && platVersion2) {
            this.stateList.add(new SelectItem("5", "省级待审"));
            this.stateList.add(new SelectItem("6", "省级退回"));
            this.stateList.add(new SelectItem("7", "省级通过"));
        }
        if (this.level == 1) {
            this.states = new String[]{"1"};
        } else if (this.level == 2) {
            this.states = new String[]{"3"};
        } else if (this.level == 3) {
            this.states = new String[]{"5"};
        }
    }
    /**
 	 * <p>方法描述：初始化流程</p>
 	 * @MethodAuthor qrr,2020年9月17日,initFlow
     * */
    protected void initFlow() {
    	this.newFlow = this.cardCheckServiceImpl.searchNewFlow(rid, getCardType());
    	if(null == this.newFlow) {
    		this.newFlow = new TdZwBgkLastSta();
    	}
    	if (ifCheck) {
            //审核，审核意见、审核人为空
            if (this.level == 3) {
                this.newFlow.setProAuditAdv(null);
                this.newFlow.setProChkPsn(Global.getUser().getUsername());
            } else if (this.level == 2) {
                this.newFlow.setCityAuditAdv(null);
                this.newFlow.setCityChkPsn(Global.getUser().getUsername());
            } else {
                this.newFlow.setCountAuditAdv(null);
                this.newFlow.setCountyChkPsn(Global.getUser().getUsername());
            }
		}
    	if(null == this.newFlow.getState()) {
    		return;
    	}
    	//获取历次审核
    	this.bgkFlows = this.cardCheckServiceImpl.selectFlowInfos(rid, getCardType());
    	if (CollectionUtils.isEmpty(bgkFlows)) {
    		bgkFlows = new ArrayList<>();
		}
    }
    /**
     *  <p>方法描述：计算超期 - 去除节假日</p>
     * @MethodAuthor hsj 2022-06-25 14:23
     */
    protected int calLimitTime(Date sDate) {
        if (null==sDate) {
            return -1;
        }
        //剩余天数
        int day = HolidayUtil.calRemainingDate(sDate, new Date(),limitTime);
        if (day == 0) {
            return -1;
        }
        return day;
    }
	/**
     * <p>方法描述：附件查看</p>
     *
     * @MethodAuthor qrr, 2019年9月16日, showReportAction
     */
    public void toAnnexView(String annexPath) {
        if (StringUtils.isBlank(annexPath)) {
            JsfUtil.addErrorMessage("附件已经被删除，请重新上传！");
            return;
        }
        RequestContext.getCurrentInstance().execute(
                "window.open('" + FileUtils.previewFile(annexPath,
                        "") + "')");
    }

    /**
    * @Description : 退回
    * @MethodAuthor: anjing
    * @Date : 2020/7/22 15:37
    **/
    public void returnAction(TsZone zone) {
        if(null == this.rid) {
            JsfUtil.addErrorMessage("业务主键不能为空！");
            return;
        }
        if(null == getCardType()) {
            JsfUtil.addErrorMessage("报告卡类型不能为空！");
            return;
        }
        packageFlow(zone);
        JsfUtil.addSuccessMessage("退回成功！");
        this.searchAction();
        this.backAction();
        RequestContext context = RequestContext.getCurrentInstance();
        context.update("tabView");
    }

    /**
    * @Description : 封装退回原因
    * @MethodAuthor: anjing
    * @Date : 2020/7/22 15:39
    **/
    private void packageFlow(TsZone zone) {
        boolean ifCityDirect = "1".equals(zone.getIfCityDirect());
        // 更新报告卡最新状态
    	if(null == newFlow) {
    		newFlow = new TdZwBgkLastSta();
    	}
    	newFlow.setBusId(this.rid);
        newFlow.setCartType(getCardType());
        // 填报接收日期
        newFlow.setOrgRcvDate(new Date());

        if (this.level == 3) {
            newFlow.setState(6);
        } else if (this.level == 2) {
            newFlow.setState(4);
        } else {
            newFlow.setState(2);
        }
        // 新增接收记录
        TdZwBgkFlow tdZwBgkFlow = new TdZwBgkFlow();
        tdZwBgkFlow.setBusId(this.rid);
        tdZwBgkFlow.setCartType(getCardType());
        tdZwBgkFlow.setRcvDate(new Date());
        tdZwBgkFlow.setFkBySmtPsnId(this.sessionData.getUser());
        if (this.level == 3) {
            tdZwBgkFlow.setOperFlag(32);
        }  else if (this.level == 1) {
            tdZwBgkFlow.setOperFlag(11);
        } else if ("3".equals(this.checkLevel) && ifCityDirect) {
            tdZwBgkFlow.setOperFlag(13);
        } else {
            tdZwBgkFlow.setOperFlag(22);
        }
        String auditAdv;
        String chkPsn;
        if (this.level == 1) {
            auditAdv = this.newFlow.getCountAuditAdv();
            chkPsn = this.newFlow.getCountyChkPsn();
        } else if (this.level == 2) {
            auditAdv = this.newFlow.getCityAuditAdv();
            chkPsn = this.newFlow.getCityChkPsn();
        } else {
            auditAdv = this.newFlow.getProAuditAdv();
            chkPsn = this.newFlow.getProChkPsn();
        }
        tdZwBgkFlow.setAuditAdv(auditAdv);
        tdZwBgkFlow.setAuditMan(chkPsn);
        flowService.updateInsLastRecord(newFlow,tdZwBgkFlow,limitTime);
    }

    /**
     *  <p>方法描述：审核-节假日计算</p>
     * @MethodAuthor hsj 2022-06-25 14:39
     */
    public void reviewHolidaysAction() {
        if(null == this.rid) {
            JsfUtil.addErrorMessage("业务主键不能为空！");
            return;
        }
        if(null == getCardType()) {
            JsfUtil.addErrorMessage("报告卡类型不能为空！");
            return;
        }
        // 审核
        this.saveOrUpdateTdZwBgkLastStaInfoHolidays(this.rid,this.newFlow);
        JsfUtil.addSuccessMessage("审核成功！");
        // 发消息，接收单位所有用户？提交人？
        //sendMsg(users);
        this.searchAction();
        this.backAction();
        RequestContext context = RequestContext.getCurrentInstance();
        context.update("tabView");
    }
    /**
 	 * <p>方法描述：验证审核信息</p>
 	 * @MethodAuthor qrr,2020年9月17日,veryCheckInfo
     * */
    protected boolean veryCheckInfo() {
    	boolean flag = false;
    	String adv = null;
    	String advPsn = null;
        if (this.level == 1) {
            adv = this.newFlow.getCountAuditAdv();
            advPsn = this.newFlow.getCountyChkPsn();
        } else if (this.level == 2) {
            adv = this.newFlow.getCityAuditAdv();
            advPsn = this.newFlow.getCityChkPsn();
        } else {
            adv = this.newFlow.getProAuditAdv();
            advPsn = this.newFlow.getProChkPsn();
        }
    	if (StringUtils.isBlank(adv)) {
			JsfUtil.addErrorMessage("审核意见不能为空！");
			flag = true;
		}else if(adv.length()>100){
            JsfUtil.addErrorMessage("审核意见长度不能超过100！");
            flag = true;
        }
		if (StringUtils.isBlank(advPsn)) {
			JsfUtil.addErrorMessage("审核人不能为空！");
			flag = true;
		}
		return flag;
	}

    /**
     *  <p>方法描述：执行审核操作-周末+节假日不算</p>
     * @MethodAuthor hsj 2022-06-24 14:47
     */
    private void saveOrUpdateTdZwBgkLastStaInfoHolidays(Integer rid,TdZwBgkLastSta newFlow) {
        //更新报告卡最新状态
        if (null==newFlow) {
            newFlow = new TdZwBgkLastSta();
        }
        //新增接收记录
        TdZwBgkFlow tdZwBgkFlow = new TdZwBgkFlow();
        newFlow.setBusId(rid);
        newFlow.setCartType(getCardType());
        if (this.level == 3) {
            newFlow.setState(7);
        } else if (this.level == 1 && "3".equals(this.checkLevel)) {
            newFlow.setState(3);
        } else {
            newFlow.setState(5);
        }
        if (this.level == 3) {
            newFlow.setProSmtDate(new Date());
        } else if (this.level == 1 && "3".equals(this.checkLevel)) {
            newFlow.setCityRcvDate(new Date());
        } else {
            newFlow.setProRcvDate(new Date());
        }
        tdZwBgkFlow.setBusId(rid);
        tdZwBgkFlow.setCartType(getCardType());
        tdZwBgkFlow.setRcvDate(new Date());
        tdZwBgkFlow.setFkBySmtPsnId(this.sessionData.getUser());

        int operFlag;
        String auditAdv;
        String auditMan;
        if (this.level == 3) {
            operFlag = 42;
        } else if (this.level == 1 && "3".equals(this.checkLevel)) {
            operFlag = 31;
        } else if (this.level == 1 && "2".equals(this.checkLevel)) {
            operFlag = 43;
        } else {
            operFlag = 41;
        }
        if (this.level == 1) {
            auditAdv = newFlow.getCountAuditAdv();
            auditMan = newFlow.getCountyChkPsn();
        } else if (this.level == 2) {
            auditAdv = newFlow.getCityAuditAdv();
            auditMan = newFlow.getCityChkPsn();
        } else {
            auditAdv = newFlow.getProAuditAdv();
            auditMan = newFlow.getProChkPsn();
        }
        tdZwBgkFlow.setOperFlag(operFlag);
        tdZwBgkFlow.setAuditAdv(auditAdv);
        tdZwBgkFlow.setAuditMan(auditMan);
        flowService.updateInsLastRecord(newFlow,tdZwBgkFlow,limitTime);
    }


    /**
    * @Description : 打开批量审核确认框,判断是否存在未填写规范性审核的数据
    * @MethodAuthor: anjing
    * @Date : 2020/7/22 18:26
    **/
    public void openReviewConfirmDialog() {
        if(this.selectEntitys == null || this.selectEntitys.size()==0){
            JsfUtil.addErrorMessage("请选择需要审批的数据！");
            return ;
        }
        RequestContext.getCurrentInstance().execute("PF('ReviewConfirmDialog').show()");
    }

    /**
     *  <p>方法描述：批量审核- 周末+节假日不算</p>
     * @MethodAuthor hsj 2022-06-24 14:45
     */
    public void reviewBatchAction() {
        if (null==getCardType()) {
            JsfUtil.addErrorMessage("报告卡类型不能为空！");
            return;
        }
        for(Object[] obj : this.selectEntitys) {
            if(null == obj || null == obj[0]) {
                continue;
            }
            TdZwBgkLastSta newFlow = cardCheckServiceImpl.searchNewFlow(Integer.parseInt(obj[0].toString()), getCardType());
            String defaultAuditAdv = PropertyUtils.getValue("defaultAuditAdv");
            if("2".equals(checkLevel)){
                //2级审核
                if (zoneType <= 3) {
                    //终审
                    // 省级审核意见
                    newFlow.setProAuditAdv(defaultAuditAdv);
                    newFlow.setProChkPsn(this.sessionData.getUser().getUsername());
                }else {
                    //初审
                    // 区级审核意见
                    newFlow.setCountAuditAdv(defaultAuditAdv);
                    // 区级审核人
                    newFlow.setCountyChkPsn(this.sessionData.getUser().getUsername());
                }
            }else if("3".equals(checkLevel)) {
                if (this.zoneType == 2) {
                    // 省级审核意见
                    newFlow.setProAuditAdv(defaultAuditAdv);
                    newFlow.setProChkPsn(this.sessionData.getUser().getUsername());
                } else if (zoneType == 3) {
                    // 市级审核意见
                    newFlow.setCityAuditAdv(defaultAuditAdv);
                    // 市级审核人
                    newFlow.setCityChkPsn(this.sessionData.getUser().getUsername());
                } else {
                    // 区级审核意见
                    newFlow.setCountAuditAdv(defaultAuditAdv);
                    // 区级审核人
                    newFlow.setCountyChkPsn(this.sessionData.getUser().getUsername());
                }
            }
            this.saveOrUpdateTdZwBgkLastStaInfoHolidays(Integer.parseInt(obj[0].toString()),newFlow);
        }
        JsfUtil.addSuccessMessage("审核成功！");
        this.searchAction();
    }

    /**
    * @Description : 报告卡类型
    * @MethodAuthor: anjing
    * @Date : 2020/7/22 14:19
    **/
    public abstract Integer getCardType();

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwBgkLastSta getNewFlow() {
        return newFlow;
    }

    public void setNewFlow(TdZwBgkLastSta newFlow) {
        this.newFlow = newFlow;
    }

    public Boolean getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(Boolean readOnly) {
        this.readOnly = readOnly;
    }

    public Integer getZoneType() {
        return zoneType;
    }

    public void setZoneType(Integer zoneType) {
        this.zoneType = zoneType;
    }

    public Boolean getIfCheck() {
        return ifCheck;
    }

    public void setIfCheck(Boolean ifCheck) {
        this.ifCheck = ifCheck;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public List<Object[]> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<Object[]> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }

	public String getLimitTime() {
		return limitTime;
	}

	public void setLimitTime(String limitTime) {
		this.limitTime = limitTime;
	}

	public String getIfshowdeadline() {
		return ifshowdeadline;
	}

	public void setIfshowdeadline(String ifshowdeadline) {
		this.ifshowdeadline = ifshowdeadline;
	}

	public boolean isIfCityDirect() {
		return ifCityDirect;
	}

	public void setIfCityDirect(boolean ifCityDirect) {
		this.ifCityDirect = ifCityDirect;
	}

	public String getCheckResult() {
		return checkResult;
	}

	public void setCheckResult(String checkResult) {
		this.checkResult = checkResult;
	}

	public String getTipInfo() {
		return tipInfo;
	}

	public void setTipInfo(String tipInfo) {
		this.tipInfo = tipInfo;
	}

	public List<TdZwBgkFlow> getBgkFlows() {
		return bgkFlows;
	}

	public void setBgkFlows(List<TdZwBgkFlow> bgkFlows) {
		this.bgkFlows = bgkFlows;
	}

    public String getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(String checkLevel) {
        this.checkLevel = checkLevel;
    }

    public String getPlatVersion() {
        return platVersion;
    }

    public void setPlatVersion(String platVersion) {
        this.platVersion = platVersion;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }
}
