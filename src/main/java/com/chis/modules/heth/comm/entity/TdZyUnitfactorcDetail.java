package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-6
 */
@Entity
@Table(name = "TD_ZY_UNITFACTORC_DETAIL")
@SequenceGenerator(name = "TdZyUnitfactorcDetail", sequenceName = "TD_ZY_UNITFACTORC_DETAIL_SEQ", allocationSize = 1)
public class TdZyUnitfactorcDetail implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZyUnitbasicinfo fkByMainId;
	private Integer hazardsSort;
	private TsSimpleCode fkByHazardsId;
	private String hazardsName;
	private Integer supervisionRequirement;
	private Integer contactNumber;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZyUnitfactorcDetail() {
	}

	public TdZyUnitfactorcDetail(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZyUnitfactorcDetail")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZyUnitbasicinfo getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZyUnitbasicinfo fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "HAZARDS_SORT")	
	public Integer getHazardsSort() {
		return hazardsSort;
	}

	public void setHazardsSort(Integer hazardsSort) {
		this.hazardsSort = hazardsSort;
	}	
			
	@ManyToOne
	@JoinColumn(name = "HAZARDS_ID")			
	public TsSimpleCode getFkByHazardsId() {
		return fkByHazardsId;
	}

	public void setFkByHazardsId(TsSimpleCode fkByHazardsId) {
		this.fkByHazardsId = fkByHazardsId;
	}	
			
	@Column(name = "HAZARDS_NAME")	
	public String getHazardsName() {
		return hazardsName;
	}

	public void setHazardsName(String hazardsName) {
		this.hazardsName = hazardsName;
	}	
			
	@Column(name = "SUPERVISION_REQUIREMENT")	
	public Integer getSupervisionRequirement() {
		return supervisionRequirement;
	}

	public void setSupervisionRequirement(Integer supervisionRequirement) {
		this.supervisionRequirement = supervisionRequirement;
	}	
			
	@Column(name = "CONTACT_NUMBER")	
	public Integer getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(Integer contactNumber) {
		this.contactNumber = contactNumber;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}