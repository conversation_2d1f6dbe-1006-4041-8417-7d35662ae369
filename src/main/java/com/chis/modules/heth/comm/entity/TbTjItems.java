package com.chis.modules.heth.comm.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;

import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 体检项目标准库（GBZ-188）
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置
 *
 * <AUTHOR>
 * @createDate 2014年9月1日 上午8:49:22
 * @LastModify LuXuekun
 * @ModifyDate 2014年9月1日 上午8:49:22
 *
 * 修改人：lqq 修改时间：2014-09-10
 * 修改内容：增加码表关联
 *
 */
@Entity
@Table(name = "TB_TJ_ITEMS",uniqueConstraints = @UniqueConstraint(columnNames = "ITEM_CODE"))
@SequenceGenerator(name="TbTjItemsSeq",sequenceName="TB_TJ_ITEMS_SEQ",allocationSize=1)
public class TbTjItems implements java.io.Serializable {

    private static final long serialVersionUID = 6282836485587503443L;
    private Integer rid;
    private Integer num;
    private String itemCode;
    private String itemName;
    private String pyxnam;
    private String msrunt;
    private TsSimpleCode tsSimpleCode = new TsSimpleCode();
    private short jdgptn;
    private BigDecimal minval;
    private BigDecimal maxval;
    private String itemStdvalue;
    private String dflt;
    private short stopTag;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private List<TdTjBhksub> tdTjBhksubs = new ArrayList<TdTjBhksub>(0);
    private List<TbZwtjItemcmbItems> tbZwtjItemcmbItemses = new ArrayList<TbZwtjItemcmbItems>(
            0);

    private List<TbTjRstdesc> tbTjRstdescs = new ArrayList<TbTjRstdesc>(0);


    private boolean ifHasUpBtn = false;
    private boolean ifHasDownBtn = false;

    private String levelCode;
    /**性别：1 男 2 女*/
    private Integer sex;
    /**项目标记：1：收缩压、2：舒张压*/
    private Integer itemTag;
    /**码表的rid*/
    private TsSimpleCode itemTagId;

    /************** 非实体字段 ***************/
    /**是否必检：0 非必检 1 必检*/
    private Integer isMust;
    private String itemCmbid;
    /**临时封装itemTag与ITEM_TAG_ID结合的字段--20210507*/
    private String itemTagNew;

    /**是否选择*/
    private boolean ifSelected = false;
    private List<TbTjItemsSpe> tbTjItemsSpeList;
    private List<TbTjItemUnitRel> tbTjItemsUnitList;
    /**是否不合格的项目*/
    private boolean ifNotHg = false;
    private TsSimpleCode mruntId;
    /**计量单位ID*/
    private Integer msruntId;
    public TbTjItems() {
    }

    public TbTjItems(Integer rid) {
        this.rid = rid;
    }

    public TbTjItems(Integer rid, String itemCode, String itemName,
                     TsSimpleCode tsSimpleCode, short jdgptn, short stopTag,
                     Date createDate, Integer createManid) {
        this.rid = rid;
        this.itemCode = itemCode;
        this.itemName = itemName;
        this.tsSimpleCode = tsSimpleCode;
        this.jdgptn = jdgptn;
        this.stopTag = stopTag;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    public TbTjItems(Integer rid, Integer num, String itemCode,
                     String itemName, String pyxnam, String msrunt,
                     TsSimpleCode tsSimpleCode, short jdgptn, BigDecimal minval,
                     BigDecimal maxval, String itemStdvalue, String dflt, short stopTag,
                     Date createDate, Integer createManid, Date modifyDate,
                     Integer modifyManid, List<TdTjBhksub> tdTjBhksubs,
                     List<TbZwtjItemcmbItems> tbZwtjItemcmbItemses,
                     List<TbTjRstdesc> tbTjRstdescs,TsSimpleCode itemTagId) {
        this.rid = rid;
        this.num = num;
        this.itemCode = itemCode;
        this.itemName = itemName;
        this.pyxnam = pyxnam;
        this.msrunt = msrunt;
        this.tsSimpleCode = tsSimpleCode;
        this.jdgptn = jdgptn;
        this.minval = minval;
        this.maxval = maxval;
        this.itemStdvalue = itemStdvalue;
        this.dflt = dflt;
        this.stopTag = stopTag;
        this.createDate = createDate;
        this.createManid = createManid;
        this.modifyDate = modifyDate;
        this.modifyManid = modifyManid;
        this.tdTjBhksubs = tdTjBhksubs;
        this.tbZwtjItemcmbItemses = tbZwtjItemcmbItemses;
        this.tbTjRstdescs = tbTjRstdescs;
        this.itemTagId = itemTagId;
    }

    public TbTjItems(Integer rid, String itemName) {
        this.rid = rid;
        this.itemName = itemName;
    }

    @Id
    @GeneratedValue(strategy=GenerationType.SEQUENCE,generator="TbTjItemsSeq")
    @Column(name = "RID", unique = true )
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Column(name = "NUM")
    public Integer getNum() {
        return this.num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }


    @Column(name = "ITEM_CODE", unique = true)
    public String getItemCode() {
        return this.itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }


    @Column(name = "ITEM_NAME")
    public String getItemName() {
        return this.itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    @Column(name = "PYXNAM", length = 100)
    public String getPyxnam() {
        return this.pyxnam;
    }

    public void setPyxnam(String pyxnam) {
        this.pyxnam = pyxnam;
    }

    @Column(name = "MSRUNT", length = 50)
    public String getMsrunt() {
        return this.msrunt;
    }

    public void setMsrunt(String msrunt) {
        this.msrunt = msrunt;
    }

    @ManyToOne
    @JoinColumn(name = "ITEM_SORTID" )
    public TsSimpleCode getTsSimpleCode() {
        return this.tsSimpleCode;
    }

    public void setTsSimpleCode(TsSimpleCode tsSimpleCode) {
        this.tsSimpleCode = tsSimpleCode;
    }
    //2定量 1:定性
    @NotNull(message = "判断模式不能为空")
    @Column(name = "JDGPTN" )
    public short getJdgptn() {
        return this.jdgptn;
    }

    public void setJdgptn(short jdgptn) {
        this.jdgptn = jdgptn;
    }

    @Digits(integer = 12, fraction =6, message = "输入格式错误，只能在0到999999999999.999999之间的小数！")
    @Column(name = "MINVAL")
    public BigDecimal getMinval() {
        return this.minval;
    }

    public void setMinval(BigDecimal minval) {
        this.minval = minval;
    }

    @Digits(integer = 12, fraction =6, message = "输入格式错误，只能在0到999999999999.999999之间的小数！")
    @Column(name = "MAXVAL")
    public BigDecimal getMaxval() {
        return this.maxval;
    }

    public void setMaxval(BigDecimal maxval) {
        this.maxval = maxval;
    }

    @Column(name = "ITEM_STDVALUE", length = 100)
    public String getItemStdvalue() {
        return this.itemStdvalue;
    }

    public void setItemStdvalue(String itemStdvalue) {
        this.itemStdvalue = itemStdvalue;
    }

    @Column(name = "DFLT", length = 100)
    public String getDflt() {
        return this.dflt;
    }

    public void setDflt(String dflt) {
        this.dflt = dflt;
    }

    @NotNull(message = "体检状态不能为空")
    @Column(name = "STOP_TAG" )
    public short getStopTag() {
        return this.stopTag;
    }

    public void setStopTag(short stopTag) {
        this.stopTag = stopTag;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbTjItems")
    public List<TdTjBhksub> getTdTjBhksubs() {
        return this.tdTjBhksubs;
    }

    public void setTdTjBhksubs(List<TdTjBhksub> tdTjBhksubs) {
        this.tdTjBhksubs = tdTjBhksubs;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbTjItems")
    public List<TbZwtjItemcmbItems> getTbZwtjItemcmbItemses() {
        return this.tbZwtjItemcmbItemses;
    }

    public void setTbZwtjItemcmbItemses(
            List<TbZwtjItemcmbItems> tbZwtjItemcmbItemses) {
        this.tbZwtjItemcmbItemses = tbZwtjItemcmbItemses;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbTjItems",orphanRemoval = true)
    public List<TbTjRstdesc> getTbTjRstdescs() {
        return this.tbTjRstdescs;
    }

    public void setTbTjRstdescs(List<TbTjRstdesc> tbTjRstdescs) {
        this.tbTjRstdescs = tbTjRstdescs;
    }

    @Transient
    public boolean isIfHasUpBtn() {
        return ifHasUpBtn;
    }

    public void setIfHasUpBtn(boolean ifHasUpBtn) {
        this.ifHasUpBtn = ifHasUpBtn;
    }

    @Transient
    public boolean isIfHasDownBtn() {
        return ifHasDownBtn;
    }

    public void setIfHasDownBtn(boolean ifHasDownBtn) {
        this.ifHasDownBtn = ifHasDownBtn;
    }

    @Transient
    public String getLevelCode() {
        return levelCode;
    }

    public void setLevelCode(String levelCode) {
        this.levelCode = levelCode;
    }

    @Transient
    public Integer getIsMust() {
        return isMust;
    }

    public void setIsMust(Integer isMust) {
        this.isMust = isMust;
    }

    @Transient
    public String getItemTagNew() {
        return itemTagNew;
    }

    public void setItemTagNew(String itemTagNew) {
        this.itemTagNew = itemTagNew;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByItemId",orphanRemoval = true)
    public List<TbTjItemsSpe> getTbTjItemsSpeList() {
        return tbTjItemsSpeList;
    }

    public void setTbTjItemsSpeList(List<TbTjItemsSpe> tbTjItemsSpeList) {
        this.tbTjItemsSpeList = tbTjItemsSpeList;
    }

    @Transient
    public String getItemCmbid() {
        return itemCmbid;
    }

    public void setItemCmbid(String itemCmbid) {
        this.itemCmbid = itemCmbid;
    }

    @Column(name = "SEX")
    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }
    @Transient
    public boolean isIfSelected() {
        return ifSelected;
    }

    public void setIfSelected(boolean ifSelected) {
        this.ifSelected = ifSelected;
    }

    @Column(name = "ITEM_TAG")
    public Integer getItemTag() {
        return itemTag;
    }

    public void setItemTag(Integer itemTag) {
        this.itemTag = itemTag;
    }

    @ManyToOne
    @JoinColumn(name = "ITEM_TAG_ID" )
    public TsSimpleCode getItemTagId() {
        return itemTagId;
    }

    public void setItemTagId(TsSimpleCode itemTagId) {
        this.itemTagId = itemTagId;
    }

    @Transient
	public boolean isIfNotHg() {
		return ifNotHg;
	}

	public void setIfNotHg(boolean ifNotHg) {
		this.ifNotHg = ifNotHg;
	}
	@Transient
	public TsSimpleCode getMruntId() {
		return mruntId;
	}

	public void setMruntId(TsSimpleCode mruntId) {
		this.mruntId = mruntId;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByItemId",orphanRemoval = true)
	public List<TbTjItemUnitRel> getTbTjItemsUnitList() {
		return tbTjItemsUnitList;
	}

	public void setTbTjItemsUnitList(List<TbTjItemUnitRel> tbTjItemsUnitList) {
		this.tbTjItemsUnitList = tbTjItemsUnitList;
	}

	@Column(name = "MSRUNT_ID")
	public Integer getMsruntId() {
		return msruntId;
	}

	public void setMsruntId(Integer msruntId) {
		this.msruntId = msruntId;
	}


}
