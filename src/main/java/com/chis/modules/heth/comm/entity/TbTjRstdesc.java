package com.chis.modules.heth.comm.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.validator.constraints.NotBlank;

/**
 * 定性项目描述
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置
 *
 * <AUTHOR>
 * @createDate 2014年9月1日 上午8:51:19
 * @LastModify LuXuekun
 * @ModifyDate 2014年9月1日 上午8:51:19
 */
@Entity
@Table(name = "TB_TJ_RSTDESC")
@SequenceGenerator(name="TbTjRstdescSeq",sequenceName="TB_TJ_RSTDESC_SEQ",allocationSize=1)
public class TbTjRstdesc implements java.io.Serializable {

    private static final long serialVersionUID = 8527777426554292000L;
    private Integer rid;
    private TbTjItems tbTjItems;
    private Integer num;
    private String rstDesc;
    private short egbTag;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    public TbTjRstdesc() {
    }

    public TbTjRstdesc(Integer rid, String rstDesc, short egbTag,
                       Date createDate, Integer createManid) {
        this.rid = rid;
        this.rstDesc = rstDesc;
        this.egbTag = egbTag;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    /** full constructor */
    public TbTjRstdesc(Integer rid, TbTjItems tbTjItems, Integer num,
                       String rstDesc, short egbTag, Date createDate,
                       Integer createManid, Date modifyDate, Integer modifyManid) {
        this.rid = rid;
        this.tbTjItems = tbTjItems;
        this.num = num;
        this.rstDesc = rstDesc;
        this.egbTag = egbTag;
        this.createDate = createDate;
        this.createManid = createManid;
        this.modifyDate = modifyDate;
        this.modifyManid = modifyManid;
    }

    @Id
    @GeneratedValue(generator="TbTjRstdescSeq",strategy=GenerationType.SEQUENCE)
    @Column(name = "RID", unique = true )
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "ITEM_ID")
    public TbTjItems getTbTjItems() {
        return this.tbTjItems;
    }

    public void setTbTjItems(TbTjItems tbTjItems) {
        this.tbTjItems = tbTjItems;
    }

    @Column(name = "NUM")
    public Integer getNum() {
        return this.num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    @Column(name = "RST_DESC" , length = 1000)
    public String getRstDesc() {
        return this.rstDesc;
    }

    public void setRstDesc(String rstDesc) {
        this.rstDesc = rstDesc;
    }

    @Column(name = "EGB_TAG" )
    public short getEgbTag() {
        return this.egbTag;
    }

    public void setEgbTag(short egbTag) {
        this.egbTag = egbTag;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }
}
