package com.chis.modules.heth.comm.service;

import com.chis.modules.heth.comm.entity.TdZxjcUnitbasicinfo;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>类描述： </p>
 *
 * @ClassAuthor: yzz
 * @date： 2023年01月16日
 **/

@Service
@Transactional(readOnly = true)
public class TdZxjcUnitBasicInfoService extends AbstractTemplate {


    /**
     * <p>方法描述：根据社会信用代码+企业名称 匹配单位基本信息</p>
     *
     * @MethodAuthor： yzz
     * @Date：2023-01-14
     **/
    public List<Object[]> findBasicInfo(String institutionCode, String crptName) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select T.RID,T.YEAR,T.IF_DECLARE,T.IF_ANNUAL_UPDATE,T.EMP_NUM,T.EXTERNAL_NUM ");
        sql.append(" from TD_ZXJC_UNITBASICINFO T ");
        sql.append(" where exists(select 1 from TD_ZXJC_UNITFACTORCROWD T1 where T1.MAIN_ID=T.RID) ");
        sql.append("  and exists(select 1 from TD_ZXJC_RESULT_PRO T2 where T2.MAIN_ID=T.RID) ");
        sql.append("  and T.CREDIT_CODE='").append(institutionCode).append("'");
        sql.append("  and T.UNIT_NAME='").append(crptName).append("'");
        sql.append(" order by T.YEAR desc ");
        return this.findSqlResultList(sql.toString());
    }


    /**
     * <p>方法描述：根据主表rid获取职业病危害因素种类及接触人数</p>
     *
     * @MethodAuthor： yzz
     * @Date：2023-01-16
     **/
    public List<Object[]> findUnitfactorcrowd(Integer rid) {

        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT T.RID,T.CONTACT_TOTAL_PEOPLES,T.IFHF_DUST,T.HF_DUST_PEOPLES,T.IFHF_CHEMISTRY,T.HF_CHEMISTRY_PEOPLES,T.IFHF_PHYSICS,T.HF_PHYSICS_PEOPLES ");
        sql.append(" FROM TD_ZXJC_UNITFACTORCROWD T ");
        sql.append(" WHERE MAIN_ID=").append(rid);
        return this.findSqlResultList(sql.toString());
    }

    /**
     * <p>方法描述：根据主表rid 获取 接触因素及人数明细</p>
     *
     * @MethodAuthor： yzz
     * @Date：2023-01-16
     **/
    public List<Object[]> findUnitFactorItem(Integer rid) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select T.RID,T1.IFHF_DUST,T1.HF_DUST_PEOPLES,T1.IFHF_CHEMISTRY,T1.HF_CHEMISTRY_PEOPLES,T1.IFHF_PHYSICS,T1.HF_PHYSICS_PEOPLES,T.TYPE,T.FACTOR_NAME,T.CONTACT_NUM ");
        sql.append(" from TD_ZXJC_UNITFACTOR_ITEM T ");
        sql.append(" inner join TD_ZXJC_UNITFACTORCROWD T1 on T.MAIN_ID=T1.RID ");
        sql.append(" left join TS_SIMPLE_CODE T2 on T.FACTOR_ID=T2.RID ");
        sql.append(" where T1.MAIN_ID=").append(rid);
        sql.append(" order by T.TYPE,T.FACTOR_ID,T2.NUM ");
        return this.findSqlResultList(sql.toString());
    }

    /**
     * <p>方法描述：根据主表rid获取 职业病危害因素检测情况 </p>
     *
     * @MethodAuthor： yzz
     * @Date：2023-01-16
     **/
    public List<Object[]> findUnitharmChk(Integer rid) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select T.RID,T.IFAT,T.IFAT_DUST,T.IFAT_CHEMISTRY,T.IFAT_PHYSICS ");
        sql.append(" from TD_ZXJC_UNITHARM_CHK T ");
        sql.append(" where T.MAIN_ID=").append(rid);
        return this.findSqlResultList(sql.toString());
    }

    /**
     * <p>方法描述：根据主表rid获取危害因素检测情况-危害因素明细</p>
     *
     * @MethodAuthor： yzz
     * @Date：2023-01-17
     **/
    public List<Object[]> findUnitharmcheckSub(Integer rid) {
        StringBuffer sql = new StringBuffer();
        sql.append(" with table1 as ( ");
        sql.append(" select TT.*,ROWNUM as rowsNum ");
        sql.append(" from ( ");
        sql.append(" SELECT T.RID,T.TYPE,T2.CODE_NAME,T.FACTOR_NAME,T.IFAT_FACTOR,");
        sql.append("T.CHECK_NUM,T.EXCESS_NUM,T.WORK_NUM,T.WORK_EXCESS_NUM,T2.NUM,");
        sql.append(" case when T.TYPE=1 then T1.IFAT_DUST when T.TYPE=2 then T1.IFAT_CHEMISTRY when T.TYPE=3 then T1.IFAT_PHYSICS end ifCheck,");
        sql.append("row_number() over (partition by T.TYPE order by T.TYPE,T.FACTOR_ID,T2.NUM) as row_number ");
        sql.append(" FROM TD_ZXJC_UNITHARMCHECK_SUB T ");
        sql.append(" INNER JOIN TD_ZXJC_UNITHARM_CHK T1 on T.MAIN_ID=T1.RID ");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T.FACTOR_ID=T2.RID ");
        sql.append(" WHERE T1.MAIN_ID=").append(rid);
        sql.append(" order by T.TYPE,T.FACTOR_ID,T2.NUM ");
        sql.append(" ) TT ), ");
        sql.append(" table2 as ( ");
        sql.append(" select T.rid, T.TYPE ");
        sql.append(" from table1 T ");
        sql.append(" where T.row_number = 1 ), ");
        sql.append(" table3 as ( ");
        sql.append(" select T.TYPE, count(T.TYPE) as countNum ");
        sql.append(" from table1 T ");
        sql.append(" group by T.TYPE ), ");
        sql.append(" table4 as ( ");
        sql.append(" select T.rid, T1.countNum ");
        sql.append(" from table2 T ");
        sql.append(" left join table3 T1 on T.TYPE=T1.TYPE ), ");
        sql.append(" table5 as ( ");
        sql.append(" select '1' 粉尘因素, '2' 化学因素, '3' 物理因素 from dual ),");
        sql.append(" table6 as ( ");
        sql.append("  select name,title from ");
        sql.append(" table5 ");
        sql.append(" unpivot ");
        sql.append(" (name for title in (粉尘因素,化学因素,物理因素))t ");
        sql.append(" order by name ) ");
        sql.append(" select T1.*,T2.countNum,T.* ");
        sql.append(" from table6 T ");
        sql.append(" left join table1 T1 on T1.TYPE=T.name ");
        sql.append(" left join table4 T2 on T1.RID = T2.RID ");
        sql.append(" order by T.name,T1.rowsNum ");
        return this.findSqlResultList(sql.toString());
    }

    /**
     * <p>方法描述：在线监测-职业健康检查情况</p>
     *
     * @MethodAuthor： yzz
     * @Date：2023-01-17
     **/
    public List<Object[]> findUnitHethCus(Integer rid) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select T.RID,T.IFHEA,T.CHECK_TOTAL_PEOPLES,T.IFHEA_DUST,T.IFHEA_CHEMISTRY,T.IFHEA_PHYSICS ");
        sql.append(" from TD_ZXJC_UNIT_HETH_CUS T ");
        sql.append(" where T.MAIN_ID=").append(rid);
        return this.findSqlResultList(sql.toString());
    }

    /**
     * <p>方法描述：在线监测-职业健康检查情况-危害因素明细</p>
     *
     * @MethodAuthor： yzz
     * @Date：2023-01-17
     **/
    public List<Object[]> findUnitHealthItem(Integer rid) {

        StringBuffer sql = new StringBuffer();
        sql.append(" with table1 as ( ");
        sql.append(" select TT.*,ROWNUM as rowsNum ");
        sql.append(" from ( ");
        sql.append(" SELECT T.RID,T.TYPE,T2.CODE_NAME,T.FACTOR_NAME,T.IFHEA_FACTOR,");
        sql.append("T.CHECK_MONITOR_PEOPLES,T.CHECK_SHOULD_PEOPLES,T.CHECK_ACTUAL_PEOPLES,T.UNUSUAL_NUM,T2.NUM,");
        sql.append("case when T.TYPE=1 then T1.IFHEA_DUST when T.TYPE=2 then T1.IFHEA_CHEMISTRY when T.TYPE=3 then T1.IFHEA_PHYSICS end ifCheck, ");
        sql.append("row_number() over (partition by T.TYPE order by T.TYPE, T.FACTOR_ID, T2.NUM) as row_number ");
        sql.append(" FROM TD_ZXJC_UNITHEALTH_ITEM T ");
        sql.append(" INNER JOIN TD_ZXJC_UNIT_HETH_CUS T1 on T.MAIN_ID=T1.RID ");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T.FACTOR_ID=T2.RID ");
        sql.append(" WHERE T1.MAIN_ID=").append(rid);
        sql.append(" order by T.TYPE,T.FACTOR_ID,T2.NUM ");
        sql.append(" ) TT ), ");
        sql.append(" table2 as ( ");
        sql.append(" select T.rid, T.TYPE ");
        sql.append(" from table1 T ");
        sql.append(" where T.row_number = 1 ), ");
        sql.append(" table3 as ( ");
        sql.append(" select T.TYPE, count(T.TYPE) as countNum ");
        sql.append(" from table1 T ");
        sql.append(" group by T.TYPE ), ");
        sql.append(" table4 as ( ");
        sql.append(" select T.rid, T1.countNum ");
        sql.append(" from table2 T ");
        sql.append(" left join table3 T1 on T.TYPE=T1.TYPE ), ");
        sql.append(" table5 as ( ");
        sql.append(" select '1' 粉尘因素, '2' 化学因素, '3' 物理因素 from dual ),");
        sql.append(" table6 as ( ");
        sql.append("  select name,title from ");
        sql.append(" table5 ");
        sql.append(" unpivot ");
        sql.append(" (name for title in (粉尘因素,化学因素,物理因素))t ");
        sql.append(" order by name ) ");
        sql.append(" select T1.*,T2.countNum,T.* ");
        sql.append(" from table6 T ");
        sql.append(" left join table1 T1 on T1.TYPE=T.name ");
        sql.append(" left join table4 T2 on T1.RID = T2.RID ");
        sql.append(" order by T.name,T1.rowsNum ");
        return this.findSqlResultList(sql.toString());

    }

    /**
     * <p>方法描述：在线监测-职业病防护设施设置及运行情况</p>
     *
     * @MethodAuthor： yzz
     * @Date：2023-01-17
     **/
    public List<Object[]> findFacilitiesOperation(Integer rid) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select T.RID,T.FCSS_SITUATION,T.FCSS_EFFECT,T.FDSS_SITUATION,T.FDSS_EFFECT,T.FZSSS_SITUATION,T.FZSSS_EFFECT ");
        sql.append(" from TD_ZXJC_FACILITIES_OPERATION T ");
        sql.append(" where T.MAIN_ID=").append(rid);
        return this.findSqlResultList(sql.toString());
    }

    /**
     * <p>方法描述：在线监测-职业病防护用品配备及发放情况</p>
     *
     * @MethodAuthor： yzz
     * @Date：2023-01-17
     **/
    public List<Object[]> findEquimentDis(Integer rid) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select T.RID,T.FCKZ_DISTRIBUTION_SITUATION,T.FCKZ_WEAR_SITUATION,T.FDKZ_DISTRIBUTION_SITUATION,T.FDKZ_WEAR_SITUATION,T.FZYES_DISTRIBUTION_SITUATION,T.FZYES_WEAR_SITUATION, ");
        sql.append(" T.SIGN_DUST,T.SIGN_CHEMISTRY,T.SIGN_PHYSICS ");
        sql.append(" from TD_ZXJC_EQUIMENT_DIS T ");
        sql.append(" where T.MAIN_ID=").append(rid);
        return this.findSqlResultList(sql.toString());
    }

    /**
     * <p>方法描述：检测地点下的检测项目</p>
     *
     * @MethodAuthor： yzz
     * @Date：2023-01-17
     **/
    public List<Object[]> findResultPro(Integer rid) {
        StringBuffer sql = new StringBuffer();
        sql.append(" with table1 as ( ");
        sql.append(" select TT.*, ROWNUM as rowsNum ");
        sql.append(" from ( ");
        sql.append(" select T.RID,T1.CODE_NAME,T.JOB_NAME,T.JOB_NAME_OTHER,T.IF_QUALIFIED,T.IF_CTWA_HG,T.IF_PLACE_HG,T.WORKER_NUM,T.WORK_TIME,T.CONTACT_DAYS, ");
        sql.append(" T.CONTACT_TIME,T.FIX_TYPE,T.FACTOR_PRO_NAME,T.PLACE_NAME,T.TCH_TIME,T.SAMPLE1, ");
        sql.append(" T.SAMPLE2,T.SAMPLE3,T.SAMPLE4,T.SAMPLE5,T.SAMPLE6, ");
        sql.append(" T.SILICA_CONTENT,T.FILL_CSTE,T.AVG_VALUE,T.CTWA_VAL,T.CTWA_DIS_VAL,T.LEX_VAL,T1.EXTENDS6,T.FACTOR_ID, (T.FACTOR_ID || '&' || T.JOB_NAME || '&' || T.JOB_NAME_OTHER) as JOB_NAME1,T.CLASS_TIME, ");
        sql.append(" row_number() over (partition by T.FACTOR_ID,T.JOB_NAME,T.JOB_NAME_OTHER order by T1.EXTENDS6,T.FACTOR_ID,T.JOB_NAME,T.JOB_NAME_OTHER,T.FIX_TYPE desc,T2.NUM) as row_number ");
        sql.append(" from TD_ZXJC_RESULT_PRO T ");
        sql.append(" left join TS_SIMPLE_CODE T1 on T.FACTOR_ID=T1.RID ");
        sql.append(" left join TS_SIMPLE_CODE T2 on T.FACTOR_PRO_ID=T2.RID ");
        sql.append(" where T.MAIN_ID=").append(rid);
        sql.append(" order by T1.EXTENDS6,T.FACTOR_ID,T.JOB_NAME,T.JOB_NAME_OTHER,T.FIX_TYPE desc,T2.NUM ");
        sql.append(" )TT ), ");

        sql.append(" table2 as ( ");
        sql.append("  select T.rid, T.JOB_NAME1 ");
        sql.append(" from table1 T where T.row_number = 1 ), ");

        sql.append(" table3 as ( ");
        sql.append(" select T.JOB_NAME1,count(T.JOB_NAME1) as FACTOR_NUM ");
        sql.append(" from table1 T ");
        sql.append(" group by T.FACTOR_ID,T.JOB_NAME1 )");

        sql.append(" select T.*,T2.FACTOR_NUM ");
        sql.append(" from table1 T ");
        sql.append(" left join table2 T1 on T1.RID=T.RID ");
        sql.append(" left join table3 T2 on T1.JOB_NAME1=T2.JOB_NAME1 ");
        sql.append(" order by T.JOB_NAME1,T.row_number ");
        return this.findSqlResultList(sql.toString());
    }
}
