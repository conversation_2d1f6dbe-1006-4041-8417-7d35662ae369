package com.chis.modules.heth.comm.web;

import com.chis.common.bean.FastReportData;
import com.chis.common.bean.FastReportDataRef;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.service.TdZwYszybRptServiceCommImpl;
import com.chis.modules.heth.comm.service.ZwReportCardCheckCommServiceImpl;
import com.chis.modules.heth.comm.service.ZwReportCardCommServiceImpl;
import com.chis.modules.heth.comm.utils.CalLimitTimeCommUtils;
import com.chis.modules.heth.comm.utils.RptCardTempCommUtil;
import com.chis.modules.system.entity.TbZwWritsort;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.logic.SysReturnPojo;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FastReportBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * @Description : 疑似职业病报告卡-业务层
 * @ClassAuthor : anjing
 * @Date : 2019/9/3 10:50
 **/
@ManagedBean(name="tdZwYszybRptListSearchCommBean")
@ViewScoped
public class TdZwYszybRptListSearchCommBean extends AbstractReportCardCommBean {

    /**查询条件：地区集合*/
    private List<TsZone> zoneList;
    /**查询条件：地区名称*/
    private String searchZoneName;
    /**查询条件：地区编码*/
    private String searchZoneCode;
    /**查询条件：随访查询-用人单位名称*/
    private String searchCrptName;
    /**查询条件：随访查询-人员姓名*/
    private String searchPsnName;
    /**查询条件：随访查询-身份证号*/
    private String searchIdc;
    /**查询条件：随访查询-疑似职业病名称*/
    private String searchYszybName;
    /**查询条件：发现日期-查询开始日期*/
    private Date searchFindBdate;
    /**查询条件：发现日期-查询结束日期*/
    private Date searchFindEdate;
    private TdZwTjorginfoComm tjorginfo;

    private Integer rid;
    private Integer relBhkId;
    private Integer occdiseId;//疑似职业病Id
    private TdZwYszybRpt tdZwYszybRpt;
    private int currentYear;
    private Integer otherSourceDisable;
    /**是否设计*/
    private String ifDesign;

    /**true：处理期限，false：无处理期限*/
    private boolean ifshowdeadline;
    //是否化学
    private boolean ifChemical;
    //是否工种其他
    private boolean ifOtherWork;
    /**出生日期是否脱敏*/
    private boolean birthIfShow;

    /**历次审核意见*/
    private List<TdZwBgkFlow> bgkFlows;

    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private TdZwYszybRptServiceCommImpl service = SpringContextHolder.getBean(TdZwYszybRptServiceCommImpl.class);
    private ZwReportCardCheckCommServiceImpl cardCheckServiceImpl = SpringContextHolder.getBean(ZwReportCardCheckCommServiceImpl.class);
    private ZwReportCardCommServiceImpl cardServiceImpl = SpringContextHolder.getBean(ZwReportCardCommServiceImpl.class);
    private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);

    /**文书报表*/
    private FastReportBean fastReportBean;
    /***/
    private TbZwWritsort writsort;
    private TdZwBgkLastSta tdZwBgkLastSta;

    private String yszybNostdTime;
    //页面标识
    private Integer receiveDate;
    private Integer tag;
    private String dateName;
    //是否市直属
    private String cityDirectCode;
    private TsZone rptZone;


    public TdZwYszybRptListSearchCommBean() {
        this.initParam();
        super.ifSQL = true;
        if(!this.verfyBySearch()) {
            return;
        }
        String val2 = JsfUtil.getRequestParameter("tag");
        String val = PropertyUtils.getValue("receiveDate");

        if(StringUtils.isNotBlank(val)){
            receiveDate = Integer.valueOf(val);
        }
        if(StringUtils.isNotBlank(val2)){
            tag = Integer.valueOf(val2);
        }
        if(1==receiveDate){
            dateName ="接收日期";
        }else{
            dateName ="体检日期";
        }
        String sDeadline = PropertyUtils.getValue("ifshowdeadline");
        if("true".equals(sDeadline)){
            ifshowdeadline = true;
        }else{
            ifshowdeadline = false;
        }
        this.searchAction();
    }

    private void initState() {
        if("3".equals(this.checkLevel)){
            this.stateList = new ArrayList<>();
            this.stateList.add(new SelectItem("0.5", "待填报"));
            this.stateList.add(new SelectItem("0", "待提交"));
            this.stateList.add(new SelectItem("2,4,6", "已退回"));
            this.stateList.add(new SelectItem("1", "待初审"));
            this.stateList.add(new SelectItem("3", "待复审"));
            this.stateList.add(new SelectItem("5", "待终审"));
            this.stateList.add(new SelectItem("7", "终审通过"));
        }else{
            this.stateList = new ArrayList<>();
            this.stateList.add(new SelectItem("0.5", "待填报"));
            this.stateList.add(new SelectItem("0", "待提交"));
            this.stateList.add(new SelectItem("2,6", "已退回"));
            this.stateList.add(new SelectItem("1", "待初审"));
            this.stateList.add(new SelectItem("5", "待终审"));
            this.stateList.add(new SelectItem("7", "终审通过"));
        }
        this.states = new String[] { "7"};
    }


    /**
     * @Description : 查询参数初始化
     * @MethodAuthor: anjing
     * @Date : 2019/9/3 10:51
     **/
    private void initParam() {
        List<TdZwTjorginfoComm> tdZwTjorginfoList = this.service.selectOrgListByOrgId(this.sessionData.getUser().getTsUnit().getRid());
        if(CollectionUtils.isEmpty(tdZwTjorginfoList)) {
        } else {
            this.tjorginfo = tdZwTjorginfoList.get(0);
        }
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        // 地区初始化
        if (null == this.zoneList || this.zoneList.size() <= 0) {
            if(null == this.zoneList || this.zoneList.size() <= 0) {
                this.searchZoneCode = tsZone.getZoneGb();
                this.searchZoneName = tsZone.getZoneName();
                this.zoneList = this.systemModuleService.findZoneListICanSee(false, tsZone.getZoneGb());
            }
        }
        Date now = new Date();
        // 默认：当前日期往前推90天
        this.searchFindBdate = DateUtils.getYearFirstDay(new Date());
        // 默认：今天
        this.searchFindEdate = now;

        // 当前年份初始化
        this.currentYear = DateUtils.getYear(now);

        this.yszybNostdTime = PropertyUtils.getValue("yszybNostdTime");
        ifDesign = PropertyUtils.getValue("rpt.ifDesign");
        initState();
    }

    /**
     * @Description : 查询前校验
     * @MethodAuthor: anjing
     * @Date : 2019/9/4 16:17
     **/
    private boolean verfyBySearch() {
        boolean flag = true;
        List<TdZwTjorginfoComm> tdZwTjorginfoList = this.service.selectOrgListByOrgId(this.sessionData.getUser().getTsUnit().getRid());
        if(CollectionUtils.isEmpty(tdZwTjorginfoList)) {
        } else {
            this.tjorginfo = tdZwTjorginfoList.get(0);
        }
        if(null == this.searchFindEdate){
            JsfUtil.addErrorMessage("结束"+dateName+"不能为空！");
            flag = false;
        }
        if(DateUtils.isDateAfter(this.searchFindBdate, this.searchFindEdate)){
            JsfUtil.addErrorMessage("结束"+dateName+"大于等于"+"开始"+dateName+"！");
            flag = false;
        }
        return flag;
    }

    /**
     * @Description : 执行查询
     * @MethodAuthor: anjing
     * @Date : 2019/9/3 10:52
     **/
    @Override
    public void searchAction() {
        if(!this.verfyBySearch()) {
            return;
        }
        super.searchAction();
    }

    @Override
    public String[] buildHqls() {
        // 状态位是否包含待填报
        boolean ifFill = false;
        StringBuffer stateSb = new StringBuffer();
        if (null != states && states.length > 0) {
            for (String state : states) {
                stateSb.append(",").append(state);
                if ("0.5".equals(state)) {
                    ifFill = true;
                }
            }
        }

        StringBuilder sb = new StringBuilder();
        if (ifFill || stateSb.length() == 0) {
            sb.append(" SELECT distinct NULL AS RID, T.RID AS REL_BHK_ID, T5.ZONE_GB AS ZONE_GB, ");
            sb.append("CASE WHEN T5.ZONE_TYPE >2 THEN SUBSTR(T5.FULL_NAME, INSTR(T5.FULL_NAME,'_')+1) ELSE T5.FULL_NAME END ZONE_NAME, ");
            sb.append("T4.CRPT_NAME AS CRPT_NAME, T.PERSON_NAME AS PERSON_NAME, DECODE (LENGTH(T.IDC),15,SUBSTR(T.IDC,1,6) || '******' || SUBSTR (T.IDC,13),18,SUBSTR(T.IDC, 1,6) || '********' || SUBSTR(T.IDC,15),CASE WHEN T.IDC IS NOT NULL THEN SUBSTR(T.IDC, 1,LENGTH(T.IDC)-4) || '****' END) AS IDC, T9.code_name AS YSZYB_NAME,  ");
            if(receiveDate ==1){
                sb.append(" T.RPT_PRINT_DATE AS RCV_DATE,");
            }else{
                sb.append(" T.BHK_DATE AS RCV_DATE,");
            }
            sb.append("NULL AS FILL_DATE, 0.5 AS STATE, 1 AS IF_FILL, 1 AS LIMIT_DAY ,t9.rid occdiseId,nvl(t5.IF_CITY_DIRECT,0) cityDirect");
            sb.append(" FROM TD_TJ_BHK T ");
            sb.append(" INNER JOIN TB_TJ_SRVORG T1 ON T.BHKORG_ID = T1.RID ");
            sb.append(" INNER JOIN TS_UNIT T2 ON T1.REG_ORGID = T2.RID ");
            sb.append(" LEFT JOIN TB_TJ_CRPT T4 ON T.ENTRUST_CRPT_ID = T4.RID ");
            sb.append(" LEFT JOIN TS_ZONE T5 ON T4.ZONE_ID = T5.RID ");
            sb.append(" LEFT JOIN TD_TJ_MHKRST T6 ON T6.BHK_ID = T.RID ");
            sb.append(" LEFT JOIN TS_SIMPLE_CODE T7 ON T6.BHKRST_ID = T7.RID ");
            sb.append(" Inner JOIN TD_TJ_SUPOCCDISELIST t8 on t8.BHK_ID =t.rid ");
            sb.append(" LEFT JOIN TS_SIMPLE_CODE T9 ON T9.RID = T8.OCC_DISEID ");
            sb.append(" WHERE 1=1 ");
            sb.append(" AND T.BHK_TYPE IN (3, 4) ");
            sb.append(" AND T7.EXTENDS2 = 5 ");
            sb.append(" AND NOT EXISTS ( ");
            sb.append(" SELECT 1 FROM TD_ZW_YSZYB_RPT TT INNER JOIN TD_ZW_BGK_LAST_STA TT1 ON TT1.BUS_ID = TT.RID WHERE NVL(TT.DEL_MARK, 0) = 0 AND TT.REL_BHK_ID = T.RID and tt.OCC_DISEID =T8.OCC_DISEID");
            sb.append(" ) ");
            if(receiveDate ==1){
                if (StringUtils.isNotBlank(bhkBeginDate)) {
                    sb.append(" AND T.RPT_PRINT_DATE >=to_date('").append(bhkBeginDate).append("','yyyy-MM-dd')");
                }
                if (null != this.searchFindBdate) {
                    sb.append(" AND T.RPT_PRINT_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchFindBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
                }
                if (null != this.searchFindEdate) {
                    sb.append(" AND T.RPT_PRINT_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchFindEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
                }
            }else{
                if (StringUtils.isNotBlank(bhkBeginDate)) {
                    sb.append(" AND T.BHK_DATE >=to_date('").append(bhkBeginDate).append("','yyyy-MM-dd')");
                }
                if (null != this.searchFindBdate) {
                    sb.append(" AND T.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchFindBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
                }
                if (null != this.searchFindEdate) {
                    sb.append(" AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchFindEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
                }
            }

            if (StringUtils.isNotBlank(this.searchZoneCode)) {
                sb.append(" AND T5.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%' ");
            }
            if (StringUtils.isNotBlank(this.searchCrptName)) {
                sb.append(" AND T4.CRPT_NAME LIKE :crptName escape '\\\'");
                this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
            }
            if (StringUtils.isNotBlank(this.searchPsnName)) {
                sb.append(" AND T.PERSON_NAME LIKE :psnName escape '\\\'");
                this.paramMap.put("psnName", "%" + StringUtils.convertBFH(this.searchPsnName.trim()) + "%");
            }
            if (StringUtils.isNotBlank(this.searchIdc)) {
                sb.append(" AND T.IDC LIKE :idc escape '\\\'");
                this.paramMap.put("idc",StringUtils.convertBFH(this.searchIdc.trim()));
            }
            if (StringUtils.isNotBlank(this.searchYszybName)) {
                sb.append(" AND t9.code_name LIKE :yszybName escape '\\\'");
                this.paramMap.put("yszybName", "%" + StringUtils.convertBFH(this.searchYszybName.trim()) + "%");
            }

            sb.append(" UNION ALL ");
        }

        sb.append(" SELECT T.RID, T.REL_BHK_ID AS REL_BHK_ID, T3.ZONE_GB AS ZONE_GB, " +
                "CASE WHEN T3.ZONE_TYPE >2 THEN SUBSTR(T3.FULL_NAME, INSTR(T3.FULL_NAME,'_')+1) ELSE T3.FULL_NAME END ZONE_NAME, " +
                " to_char(T.EMP_CRPT_NAME) AS CRPT_NAME, T.PERSONNEL_NAME AS PERSON_NAME, DECODE (LENGTH(T.IDC),15,SUBSTR(T.IDC,1,6) || '******' || SUBSTR (T.IDC,13),18,SUBSTR(T.IDC, 1,6) || '********' || SUBSTR(T.IDC,15),CASE WHEN T.IDC IS NOT NULL THEN SUBSTR(T.IDC, 1,LENGTH(T.IDC)-4) || '****' END) AS IDC, T5.CODE_NAME as YSZYB_NAME, T4.ORG_RCV_DATE AS RCV_DATE, T.FILL_DATE, " +
                "case when T3.IF_CITY_DIRECT =1 and T4.STATE =3 then 1 else T4.STATE end as state, 1 AS IF_FILL, 1 AS LIMIT_DAY,t.OCC_DISEID occdiseId,nvl(T3.IF_CITY_DIRECT,0) cityDirect");
        sb.append(" FROM TD_ZW_YSZYB_RPT T ");
        sb.append(" LEFT JOIN TS_ZONE T3 ON T.EMP_ZONE_ID = T3.RID ");
        sb.append(" INNER JOIN TD_ZW_BGK_LAST_STA T4 ON T4.BUS_ID = T.RID ");
        sb.append(" LEFT join TS_SIMPLE_CODE t5 on t.OCC_DISEID = t5.rid");
        sb.append(" WHERE NVL(T.DEL_MARK, 0) = 0 ");
        sb.append(" AND T4.CART_TYPE = 2 ");

        if(StringUtils.isNotBlank(this.searchZoneCode)) {
            sb.append(" AND T3.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%' ");
        }
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sb.append(" AND T.EMP_CRPT_NAME LIKE :crptName escape '\\\'");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        if (StringUtils.isNotBlank(this.searchPsnName)) {
            sb.append(" AND T.PERSONNEL_NAME LIKE :psnName escape '\\\'");
            this.paramMap.put("psnName", "%" + StringUtils.convertBFH(this.searchPsnName.trim()) + "%");
        }
        if(StringUtils.isNotBlank(this.searchIdc)) {
            sb.append(" AND T.IDC LIKE :idc escape '\\\'");
            this.paramMap.put("idc",StringUtils.convertBFH(this.searchIdc.trim()));
        }
        if (null != this.searchFindBdate) {
            sb.append(" AND T4.ORG_RCV_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchFindBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchFindEdate) {
            sb.append(" AND T4.ORG_RCV_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchFindEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if (StringUtils.isNotBlank(this.searchYszybName)) {
            sb.append(" AND (T5.Code_name LIKE :yszybName escape '\\\')");
            this.paramMap.put("yszybName", "%" + StringUtils.convertBFH(this.searchYszybName.trim()) + "%");
        }
        if (stateSb.length() > 0) {
            //市直属数据
            if(stateSb.toString().contains("1")){
                sb.append(" AND (T4.STATE IN (").append(stateSb.substring(1)).append(")");
                sb.append(" or T4.STATE =3 and T3.IF_CITY_DIRECT=1)");
            }else{
                sb.append(" and (T4.STATE in(0,1,2,4,5,6,7) or (T4.STATE =3 and T3.IF_CITY_DIRECT is null))");
                sb.append(" AND T4.STATE IN (").append(stateSb.substring(1)).append(")");
            }

        }

        String h2 = "SELECT COUNT(*) FROM (" + sb.toString()+")";
        String h1 = "SELECT * FROM (" + sb.toString()+") M ORDER BY (CASE  WHEN M.STATE =6 THEN -1  WHEN M.STATE =4 THEN -2  WHEN M.STATE =2 THEN -3 WHEN M.STATE = 0 OR M.STATE = 0.5 THEN M.STATE ELSE 1 END), M.ZONE_GB, M.CRPT_NAME, M.PERSON_NAME, M.RCV_DATE";
        return new String[] { h1, h2 };
    }

    @Override
    public void processData(List<?> list) {
        try {
            if (null != list && list.size() > 0) {
                List<Object[]> result = (List<Object[]>) list;
                for (Object[] obj : result) {
                    if (null == obj[8] || null == obj[10]) {
                        continue;
                    }
                    /*if(null != obj[6]){
                        obj[6] = StringUtils.encryptIdc(obj[6].toString());
                    }*/
                    String state = obj[10].toString();
                    Date date = (Date) obj[8];
                    if ("1".equals(obj[15].toString())) {// 市直属
                        if ("0".equals(state) || "0.5".equals(state) || "4".equals(state) || "2".equals(state)|| "6".equals(state)) {
                            obj[11] = "1";
                            obj[12] = CalLimitTimeCommUtils.calLimitTime(date, StringUtils.isNotBlank(this.yszybNostdTime) ? Integer.valueOf(this.yszybNostdTime) : 15,true);
                        } else {
                            obj[11] = "0";
                        }
                    } else {
                        if ("0".equals(state) || "0.5".equals(state) || "2".equals(state)|| "4".equals(state)|| "6".equals(state)) {
                            obj[11] = "1";
                            obj[12] = CalLimitTimeCommUtils.calLimitTime(date, StringUtils.isNotBlank(this.yszybNostdTime) ? Integer.valueOf(this.yszybNostdTime) : 15,true);
                        } else {
                            obj[11] = "0";
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void addInit() {
        this.tdZwYszybRpt = new TdZwYszybRpt();
        this.tdZwBgkLastSta = new TdZwBgkLastSta();

        TdTjBhk tdTjBhk = this.commService.find(TdTjBhk.class, this.relBhkId);
        if(null != tdTjBhk) {
            this.tdZwYszybRpt.setRelBhkId(tdTjBhk.getRid());
            this.tdZwYszybRpt.setPersonnelName(StringUtils.objectToString(tdTjBhk.getPersonName()));
            String idc =tdTjBhk.getIdc();
            if(StringUtils.isNotBlank(idc)){
                this.tdZwYszybRpt.setIdc(StringUtils.encryptIdc(idc));
            }

            this.tdZwYszybRpt.setLinktel(StringUtils.objectToString(tdTjBhk.getLnktel()));
            this.tdZwYszybRpt.setCrptName(tdTjBhk.getCrptName());
            //证件类型
            this.tdZwYszybRpt.setFkByCardTypeId(tdTjBhk.getFkByCardTypeId());
            if(null != tdTjBhk.getTbTjCrpt() && null != tdTjBhk.getTbTjCrpt().getRid()) {

                this.tdZwYszybRpt.setFkByCrptId(tdTjBhk.getTbTjCrpt());

                this.tdZwYszybRpt.setCreditCode(tdTjBhk.getTbTjCrpt().getInstitutionCode());
                this.tdZwYszybRpt.setAddress(tdTjBhk.getTbTjCrpt().getAddress());
                this.tdZwYszybRpt.setPostcode(tdTjBhk.getTbTjCrpt().getPostCode());
                this.tdZwYszybRpt.setSafeposition(tdTjBhk.getTbTjCrpt().getLinkman2());
                this.tdZwYszybRpt.setSafephone(tdTjBhk.getTbTjCrpt().getLinkphone2());
                if(null != tdTjBhk.getTbTjCrpt().getTsSimpleCodeByEconomyId() && null != tdTjBhk.getTbTjCrpt().getTsSimpleCodeByEconomyId().getRid()) {
                    this.tdZwYszybRpt.setFkByEconomyId(tdTjBhk.getTbTjCrpt().getTsSimpleCodeByEconomyId());
                }
                if(null != tdTjBhk.getTbTjCrpt().getTsSimpleCodeByIndusTypeId() && null != tdTjBhk.getTbTjCrpt().getTsSimpleCodeByIndusTypeId().getRid()) {
                    this.tdZwYszybRpt.setFkByIndusTypeId(tdTjBhk.getTbTjCrpt().getTsSimpleCodeByIndusTypeId());
                }
                if(null != tdTjBhk.getTbTjCrpt().getTsSimpleCodeByCrptSizeId() && null != tdTjBhk.getTbTjCrpt().getTsSimpleCodeByCrptSizeId().getRid()) {
                    this.tdZwYszybRpt.setFkByCrptSizeId(tdTjBhk.getTbTjCrpt().getTsSimpleCodeByCrptSizeId());
                }
                if(null != tdTjBhk.getTbTjCrpt().getTsZoneByZoneId() && null != tdTjBhk.getTbTjCrpt().getTsZoneByZoneId().getRid()) {
                    this.tdZwYszybRpt.setFkByZoneId(tdTjBhk.getTbTjCrpt().getTsZoneByZoneId());
                  }
            }
            if(tdTjBhk.getFkByEntrustCrptId()!=null && tdTjBhk.getFkByEntrustCrptId().getRid()!=null){
                TbTjCrpt empCrpt = tdTjBhk.getFkByEntrustCrptId();
                this.tdZwYszybRpt.setFkByEmpCrptId(empCrpt);
                this.tdZwYszybRpt.setEmpCrptName(empCrpt.getCrptName());
                this.tdZwYszybRpt.setEmpCreditCode(empCrpt.getInstitutionCode());
                if(empCrpt.getTsSimpleCodeByCrptSizeId()!=null && empCrpt.getTsSimpleCodeByCrptSizeId().getRid()!=null){
                    this.tdZwYszybRpt.setFkByEmpCrptSizeId(empCrpt.getTsSimpleCodeByCrptSizeId());
                }
                if(empCrpt.getTsSimpleCodeByIndusTypeId()!=null && empCrpt.getTsSimpleCodeByIndusTypeId().getRid()!=null){
                    this.tdZwYszybRpt.setFkByEmpIndusTypeId(empCrpt.getTsSimpleCodeByIndusTypeId());
                }
                if(empCrpt.getTsZoneByZoneId()!=null && empCrpt.getTsZoneByZoneId().getRid()!=null){
                    rptZone =empCrpt.getTsZoneByZoneId();
                    this.tdZwYszybRpt.setFkByEmpZoneId(rptZone);
                }
                if(empCrpt.getTsSimpleCodeByEconomyId()!=null && empCrpt.getTsSimpleCodeByEconomyId().getRid()!=null){
                    this.tdZwYszybRpt.setFkByEmpEconomyId(empCrpt.getTsSimpleCodeByEconomyId());
                }
            }
            if(StringUtils.isNotBlank(tdTjBhk.getSex())) {
                if("男".equals(tdTjBhk.getSex())) {
                    this.tdZwYszybRpt.setSex(1);
                } else {
                    this.tdZwYszybRpt.setSex(2);
                }
            }
            this.tdZwYszybRpt.setHarmStartDate(tdTjBhk.getHarmStartDate());
            //发现单位
            tdZwYszybRpt.setDiscoveryUnit(tdTjBhk.getTbTjSrvorg().getUnitName());
            if(null != tdTjBhk.getBrth()) {
                this.tdZwYszybRpt.setBirthday(tdTjBhk.getBrth());
            }

            //是否化学
            ifChemical = false;
            if(null != occdiseId){
                TsSimpleCode code =commService.findTsSimpleCodeByRid(occdiseId);
                if(null != code){
                    tdZwYszybRpt.setFkByOccDiseid(code);
                    tdZwYszybRpt.setYszybName(code.getCodeName());
                    tdZwYszybRpt.setYszybTypeName(code.getCodeDesc());
                    if("1".equals(code.getExtendS1())){
                        ifChemical = true;
                    }
                }

            }

            //工种
            ifOtherWork =false;
            if(null != tdTjBhk.getFkByWorkTypeId()){
                if("1".equals(tdTjBhk.getFkByWorkTypeId().getExtendS1())){
                    ifOtherWork =true;
                }
                tdZwYszybRpt.setFkByWorkTypeId(tdTjBhk.getFkByWorkTypeId());
                tdZwYszybRpt.setWorkOther(tdTjBhk.getWorkOther());
            }else{
                tdZwYszybRpt.setFkByWorkTypeId(new TsSimpleCode());
            }


            List<TdTjSupoccdiselist> tdTjSupoccdiselistList = this.service.findSupoccdiselistListByBhkId(this.relBhkId,occdiseId);
            StringBuilder sb1 = new StringBuilder();
            StringBuilder sb2 = new StringBuilder();
            List<TdZwYszybTchBadrsn> badrsns = new ArrayList<>();
            for(TdTjSupoccdiselist tdTjSupoccdiselist : tdTjSupoccdiselistList) {
                if(null == tdTjSupoccdiselist.getTsSimpleCodeByOccDiseid() || null == tdTjSupoccdiselist.getTsSimpleCodeByOccDiseid().getRid()
                        || null == tdTjSupoccdiselist.getTsSimpleCodeByBadrsnId() || null == tdTjSupoccdiselist.getTsSimpleCodeByBadrsnId().getRid()) {
                    continue;
                }
                TdZwYszybTchBadrsn badrsn = new TdZwYszybTchBadrsn();
                badrsn.setFkByMainId(tdZwYszybRpt);
                badrsn.setFkByBadrsnId(tdTjSupoccdiselist.getTsSimpleCodeByBadrsnId());
                badrsn.setCreateDate(new Date());
                badrsn.setCreateManid(sessionData.getUser().getRid());
                badrsns.add(badrsn);
                sb1.append(",").append(tdTjSupoccdiselist.getTsSimpleCodeByOccDiseid().getCodeName());
                sb2.append(",").append(tdTjSupoccdiselist.getTsSimpleCodeByBadrsnId().getCodeName());
            }
            tdZwYszybRpt.setBadrsnList(badrsns);

            if(StringUtils.isNotBlank(sb2.toString())) {
                this.tdZwYszybRpt.setTchBadrsns(sb2.toString().substring(1));
            }
            this.tdZwYszybRpt.setAnalyWork(StringUtils.objectToString(tdTjBhk.getWorkName()));
            if(null != tdTjBhk.getTchbadrsntim()) {
                this.tdZwYszybRpt.setTchWorkYear(tdTjBhk.getTchbadrsntim().intValue());
            }
            if(null != tdTjBhk.getTchbadrsnmonth()) {
                this.tdZwYszybRpt.setTchWorkMonth(tdTjBhk.getTchbadrsnmonth().intValue());
            }
            if(null != tdTjBhk.getJdgdat()) {
                this.tdZwYszybRpt.setFindDate(tdTjBhk.getJdgdat());
            }


        }
        TsSimpleCode tsSimpleCode = this.service.findSimpleCodeByCodeTypeNameAndExtendS1("5316", "1");
        if(null != tsSimpleCode) {
            this.tdZwYszybRpt.setFkBySourceId(tsSimpleCode);
        }

        this.tdZwYszybRpt.setFkByFillUnitId(new TdZwTjorginfoComm());
        if(null != tjorginfo){
            this.tdZwYszybRpt.setUnitRespPsn(StringUtils.objectToString(this.tjorginfo.getOrgFz()));
        }

//        this.tdZwYszybRpt.setFillFormPsn(this.sessionData.getUser().getUsername());
//        this.tdZwYszybRpt.setFillLink(this.sessionData.getUser().getMbNum());
//        this.tdZwYszybRpt.setFillDate(new Date());
//        this.tdZwYszybRpt.setRptYear(this.currentYear);
//        this.tdZwYszybRpt.setDelMark(0);
//        this.tdZwYszybRpt.setCreateDate(new Date());
//        this.tdZwYszybRpt.setCreateManid(this.sessionData.getUser().getRid());

        //报告信息
//        this.tdZwYszybRpt.setRptPsn(this.sessionData.getUser().getUsername());
//        this.tdZwYszybRpt.setRptLink(this.sessionData.getUser().getMbNum());
//        this.tdZwYszybRpt.setRptDate(new Date());

        this.otherSourceDisable = 0;

        this.tdZwBgkLastSta.setCartType(2);
        this.tdZwBgkLastSta.setState(0);
        if(receiveDate ==1){
            this.tdZwBgkLastSta.setOrgRcvDate(tdTjBhk.getRptPrintDate());
        }else{
            this.tdZwBgkLastSta.setOrgRcvDate(tdTjBhk.getBhkDate());
        }
        bgkFlows = new ArrayList<>();
    }

    public void selectWorkTypeAction(){
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("width", 680);
        options.put("contentWidth", 625);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add("工种");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add("5502");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        paramList.add("true");
        paramMap.put("ifAllSelect", paramList);
        paramList = new ArrayList<String>();
        paramList.add("false");
        paramMap.put("ifShowFirstCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeRadioSelectListNew", options, paramMap);
    }

    /**
     * @MethodName: onWorkTypeSearch
     * @Description:
     * @Param: [event]
     * @Return: void
     * @Author: maox
     * @Date: 2020-07-08
     **/
    public void onWorkTypeSearch(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            TsSimpleCode code = selectedMap.get("selectPro") == null ? null
                    : (TsSimpleCode) selectedMap.get("selectPro");
            code = commService.findTsSimpleCodeByRid(code.getRid());
            tdZwYszybRpt.setFkByWorkTypeId(code);
            if("1".equals(code.getExtendS1())){
                ifOtherWork = true;
            }else{
                ifOtherWork = false;
                this.tdZwYszybRpt.setWorkName(null);
            }
        }
    }

    /**
     * @MethodName: clearWorkType
     * @Description: 清除工种
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-07-08
     **/
    public void clearWorkType(){
        tdZwYszybRpt.setFkByWorkTypeId(new TsSimpleCode());
        ifOtherWork = false;
        tdZwYszybRpt.setWorkOther(null);
         RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update(":tabView:editForm:work')");
    }

    @Override
    public void viewInit() {
        if(null != rid){
            this.modInit();
        }else{
            this.addInit();
        }

    }

    @Override
    public void modInit() {
        this.otherSourceDisable = 0;
        this.tdZwYszybRpt =this.cardCheckServiceImpl.findYszybRpt(this.rid);
        String idc =tdZwYszybRpt.getIdc();
        if(StringUtils.isNotBlank(idc)){
            this.tdZwYszybRpt.setIdc(StringUtils.encryptIdc(idc));
        }
        if(StringUtils.isNotBlank(this.tdZwYszybRpt.getLinktel())) {
            this.tdZwYszybRpt.setLinktel(StringUtils.encryptPhone(this.tdZwYszybRpt.getLinktel()));
        }
        if(StringUtils.isNotBlank(this.tdZwYszybRpt.getFillLink())) {
            this.tdZwYszybRpt.setFillLink(StringUtils.encryptPhone(this.tdZwYszybRpt.getFillLink()));
        }
        if(StringUtils.isNotBlank(this.tdZwYszybRpt.getRptLink())) {
            this.tdZwYszybRpt.setRptLink(StringUtils.encryptPhone(this.tdZwYszybRpt.getRptLink()));
        }
        if(StringUtils.isNotBlank(this.tdZwYszybRpt.getSafephone())) {
            this.tdZwYszybRpt.setSafephone(StringUtils.encryptPhone(this.tdZwYszybRpt.getSafephone()));
        }

        if(!CollectionUtils.isEmpty(tdZwYszybRpt.getBadrsnList())){
            StringBuilder s = new StringBuilder();
            for(TdZwYszybTchBadrsn tchBadrsn : tdZwYszybRpt.getBadrsnList()){
                s.append("，").append(tchBadrsn.getFkByBadrsnId().getCodeName());
            }
            tdZwYszybRpt.setTchBadrsns(s.substring(1,s.length()));
        }
        this.tdZwBgkLastSta = this.service.findTdZwBgkLastStaInfoByBusIdAndCardType(this.rid, 2);

        this.tdZwYszybRpt.setModifyDate(new Date());
        this.tdZwYszybRpt.setModifyManid(this.sessionData.getUser().getRid());

        if(null != tdZwYszybRpt.getFkByEmpCrptId() && null !=tdZwYszybRpt.getFkByEmpCrptId().getTsZoneByZoneId()){
            rptZone = tdZwYszybRpt.getFkByEmpCrptId().getTsZoneByZoneId();
        }

        ifChemical = false;
        if(null != occdiseId){
            TsSimpleCode code =commService.findTsSimpleCodeByRid(occdiseId);
            if(null != code){
                if("1".equals(code.getExtendS1())){
                    ifChemical = true;
                }
            }
        }

        //工种
        ifOtherWork =false;
        String workName="";
        if(null != tdZwYszybRpt.getFkByWorkTypeId()){
            workName = tdZwYszybRpt.getFkByWorkTypeId().getCodeName();
            if("1".equals(tdZwYszybRpt.getFkByWorkTypeId().getExtendS1())){
                ifOtherWork =true;
                workName=workName+"（"+tdZwYszybRpt.getWorkOther()+")";
            }
        }else{
            tdZwYszybRpt.setFkByWorkTypeId(new TsSimpleCode());
        }
        tdZwYszybRpt.setWorkName(workName);

        this.bgkFlows = this.cardCheckServiceImpl.selectFlowInfos(rid, 2);
        if (CollectionUtils.isEmpty(bgkFlows)) {
            bgkFlows = new ArrayList<>();
        }
        this.tdZwBgkLastSta.setModifyDate(new Date());
        this.tdZwBgkLastSta.setModifyManid(this.sessionData.getUser().getRid());
    }


    /**
     * <p>方法描述：附件查看</p>
     *
     * @MethodAuthor rcj, 2019年9月5日, showReportAction
     */
    public void toAnnexView() {
        if (StringUtils.isBlank(tdZwYszybRpt.getAnnexPath())) {
            JsfUtil.addErrorMessage("附件已经被删除，请重新上传！");
            return;
        }
        RequestContext.getCurrentInstance().execute(
                "window.open('" + FileUtils.previewFile(tdZwYszybRpt.getAnnexPath(),
                        "") + "')");
    }

    /**
     * @Description : 清空地区
     * @MethodAuthor: anjing
     * @Date : 2019/9/16 13:40
     **/
    public void clearSearchZone() {
        this.searchZoneCode = null;
        this.searchZoneName = null;
    }

    @Override
    public FastReportBean getFastReportBean() {
        return fastReportBean;
    }

    @Override
    public List<FastReportData> supportFastReportDataSet() {
        return RptCardTempCommUtil.packageDatas("HETH_2031", tdZwYszybRpt.getRid());
    }

    @Override
    public List<FastReportDataRef> supportFastReportDataRef() {
        return null;
    }



    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchPsnName() {
        return searchPsnName;
    }

    public void setSearchPsnName(String searchPsnName) {
        this.searchPsnName = searchPsnName;
    }

    public String getSearchIdc() {
        return searchIdc;
    }

    public void setSearchIdc(String searchIdc) {
        this.searchIdc = searchIdc;
    }

    public String getSearchYszybName() {
        return searchYszybName;
    }

    public void setSearchYszybName(String searchYszybName) {
        this.searchYszybName = searchYszybName;
    }

    public Date getSearchFindBdate() {
        return searchFindBdate;
    }

    public void setSearchFindBdate(Date searchFindBdate) {
        this.searchFindBdate = searchFindBdate;
    }

    public Date getSearchFindEdate() {
        return searchFindEdate;
    }

    public void setSearchFindEdate(Date searchFindEdate) {
        this.searchFindEdate = searchFindEdate;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Integer getRelBhkId() {
        return relBhkId;
    }

    public void setRelBhkId(Integer relBhkId) {
        this.relBhkId = relBhkId;
    }

    public TdZwYszybRpt getTdZwYszybRpt() {
        return tdZwYszybRpt;
    }

    public void setTdZwYszybRpt(TdZwYszybRpt tdZwYszybRpt) {
        this.tdZwYszybRpt = tdZwYszybRpt;
    }

    public int getCurrentYear() {
        return currentYear;
    }

    public void setCurrentYear(int currentYear) {
        this.currentYear = currentYear;
    }

    public Integer getOtherSourceDisable() {
        return otherSourceDisable;
    }

    public void setOtherSourceDisable(Integer otherSourceDisable) {
        this.otherSourceDisable = otherSourceDisable;
    }

    public TdZwTjorginfoComm getTjorginfo() {
        return tjorginfo;
    }

    public void setTjorginfo(TdZwTjorginfoComm tjorginfo) {
        this.tjorginfo = tjorginfo;
    }

    public TdZwBgkLastSta getTdZwBgkLastSta() {
        return tdZwBgkLastSta;
    }

    public void setTdZwBgkLastSta(TdZwBgkLastSta tdZwBgkLastSta) {
        this.tdZwBgkLastSta = tdZwBgkLastSta;
    }

    public String getYszybNostdTime() {
        return yszybNostdTime;
    }

    public void setYszybNostdTime(String yszybNostdTime) {
        this.yszybNostdTime = yszybNostdTime;
    }

    public String getIfDesign() {
        return ifDesign;
    }

    public void setIfDesign(String ifDesign) {
        this.ifDesign = ifDesign;
    }

    public Integer getTag() {
        return tag;
    }

    public void setTag(Integer tag) {
        this.tag = tag;
    }

    public boolean isIfshowdeadline() {
        return ifshowdeadline;
    }

    public void setIfshowdeadline(boolean ifshowdeadline) {
        this.ifshowdeadline = ifshowdeadline;
    }

    public String getDateName() {
        return dateName;
    }

    public void setDateName(String dateName) {
        this.dateName = dateName;
    }

    public Integer getOccdiseId() {
        return occdiseId;
    }

    public void setOccdiseId(Integer occdiseId) {
        this.occdiseId = occdiseId;
    }

    public boolean isIfChemical() {
        return ifChemical;
    }

    public void setIfChemical(boolean ifChemical) {
        this.ifChemical = ifChemical;
    }

    public boolean isIfOtherWork() {
        return ifOtherWork;
    }

    public void setIfOtherWork(boolean ifOtherWork) {
        this.ifOtherWork = ifOtherWork;
    }

    public Integer getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Integer receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getCityDirectCode() {
        return cityDirectCode;
    }

    public void setCityDirectCode(String cityDirectCode) {
        this.cityDirectCode = cityDirectCode;
    }

    public TsZone getRptZone() {
        return rptZone;
    }

    public void setRptZone(TsZone rptZone) {
        this.rptZone = rptZone;
    }

    public List<TdZwBgkFlow> getBgkFlows() {
        return bgkFlows;
    }

    public void setBgkFlows(List<TdZwBgkFlow> bgkFlows) {
        this.bgkFlows = bgkFlows;
    }

    public boolean isBirthIfShow() {
        return birthIfShow;
    }

    public void setBirthIfShow(boolean birthIfShow) {
        this.birthIfShow = birthIfShow;
    }
}
