package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.heth.comm.rptvo.SrvorgPsnByZoneVo;
import com.chis.modules.heth.comm.rptvo.SrvorgPsnVo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessPackData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.utils.PackLazyDataModel;
import com.chis.modules.system.web.FacesSimpleBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.ToggleSelectEvent;
import org.primefaces.event.UnselectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 管辖地区下的放射卫生技术服务机构人员，根据身份证号去重，取创建时间最新的一条
 */

@ManagedBean(name = "selectOrgPsnByZoneListBean")
@ViewScoped
public class SelectOrgPsnByZoneListBean extends FacesSimpleBean implements IProcessPackData {
    private static final long serialVersionUID = 1L;
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
     * 查询条件-地区
     */
    protected List<TsZone> zoneList;
    protected String searchZoneGb;
    protected String searchZoneName;

    /**
     * 查询条件-单位名称
     */
    protected String searchUnitName;

    /**
     * 查询条件-姓名
     */
    protected String searchName;

    /**
     * 传参-已选择的人员rid
     */
    protected List<String> selectIds;

    /**
     * 传参-已选择的人员身份证号
     */
    private List<String> selectIdCards;

    /**
     * 传参-资质类型 <pre>1：放射卫生技术服务
     */
    protected String type;

    /**
     * 传参-自定义标题名称
     */
    private String title;

    /**
     * 当前页面已选择
     */
    protected List<SrvorgPsnByZoneVo> selectedDatas;
    /**
     * 所有页面已选择
     */
    protected List<SrvorgPsnByZoneVo> allSelectedDatas;
    /**
     * 真分页模型
     */
    protected PackLazyDataModel extractDataModel;
    /**
     * 表格的ID
     */
    protected static final String EXTRACT_TABLE_ID = "mainForm:dataTable";

    public SelectOrgPsnByZoneListBean() {
        this.ifSQL = true;
        this.selectIds = new ArrayList<>();
        this.selectIdCards = new ArrayList<>();
        this.selectedDatas = new ArrayList<>();
        this.allSelectedDatas = new ArrayList<>();
        initParam();
        // 地区
        String zoneCode = JsfUtil.getRequest().getParameter("searchZoneCode");
        initZone(zoneCode);
        RequestContext.getCurrentInstance().update("mainForm");
        // 置为第一页
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent(EXTRACT_TABLE_ID);
        dataTable.setFirst(0);
        this.searchAction();
    }

    /**
     * <p>Description：初始化传参 </p>
     * <p>Author： yzz 2024-10-30 </p>
     */
    public void initParam() {
        // 已选人员rid
        String selectIds = JsfUtil.getRequestParameter("selectIds");
        if (StringUtils.isNotBlank(selectIds)) {
            String[] split = selectIds.split(",");
            for (String s : split) {
                if (!this.selectIds.contains(s)) {
                    this.selectIds.add(s);
                }
            }
        }
        // 资质类型
        this.type = JsfUtil.getRequestParameter("type");
        // 自定义标题名称
        this.title = JsfUtil.getRequestParameter("title");
        // 与选择人员的身份证号
        String selectIdCards = JsfUtil.getRequestParameter("selectIdCards");
        if (StringUtils.isNotBlank(selectIdCards)) {
            String[] split = selectIdCards.split(",");
            for (String s : split) {
                if (!this.selectIdCards.contains(s)) {
                    this.selectIdCards.add(s);
                }
            }
        }
    }

    /**
     * <p>Description：初始化地区 </p>
     * <p>Author： yzz 2024-10-30 </p>
     */
    public void initZone(String zoneCode) {
        if (StringUtils.isBlank(zoneCode)) {
            // 管辖地区
            TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
            if (null == tsZone) {
                tsZone = Global.getUser().getTsUnit().getTsZone();
            }
            zoneCode = tsZone.getZoneGb().substring(0, 2);
        }

        // 获取地区缓存数据
        this.zoneList = this.commService.findZoneListByGbAndType(zoneCode, true, "", "");
        if (!CollectionUtils.isEmpty(zoneList) && StringUtils.isBlank(searchZoneGb)) {
            this.searchZoneGb = zoneList.get(0).getZoneGb();
            this.searchZoneName = zoneList.get(0).getZoneName();
        }
    }

    @Override
    public void searchAction() {
        this.paramMap.clear();
        String[] hql = this.buildHqls();
        this.extractDataModel = new PackLazyDataModel<SrvorgPsnByZoneVo>(hql[0], hql[1], this.paramMap, this, EXTRACT_TABLE_ID, this.ifSQL);
        selectedDatas.clear();
        selectedDatas.addAll(allSelectedDatas);
    }
    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();
        sql.append(" WITH LatestUnit AS ( ");
        sql.append("         SELECT  T1.RID, ");
        sql.append("                 CASE WHEN T4.ZONE_TYPE > 2 THEN SUBSTR(T4.FULL_NAME, INSTR(T4.FULL_NAME, '_') + 1) ");
        sql.append("                 ELSE T4.FULL_NAME END AS ZONE_NAME, ");
        sql.append("                 T5.UNITNAME, ");
        sql.append("                 T1.EMP_NAME, ");
        sql.append("                 T1.SEX, ");
        sql.append("                 T2.CODE_NAME, ");
        sql.append("                 T4.ZONE_GB, ");
        sql.append("                 T4.RID as ZONE_ID, ");
        sql.append("                 T1.IDC_CARD, ");
        sql.append("                 T1.MOBILE_NO, ");
        sql.append("                 T1.TITLE_ID, ");
        sql.append("                 T1.TITLE_CENT_PATH, ");
        sql.append("                 T5.RID AS ORG_ID, ");
        sql.append("                 ROW_NUMBER() OVER (PARTITION BY T1.IDC_CARD ORDER BY T1.CREATE_DATE DESC) AS ROWNMBER ");
        sql.append(" FROM ");
        if ("1".equals(type)) {
            // 放射
            sql.append(" TD_ZW_SRVORGINFO TT ");
            sql.append(" LEFT JOIN TD_ZW_SRVORGPSNS T on TT.RID=T.ORG_ID ");
        }
        sql.append(" LEFT JOIN TD_ZW_PSNINFO T1 ON T.EMP_ID = T1.RID ");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T1.TITLE_ID = T2.RID ");
        sql.append(" LEFT JOIN TS_UNIT T5 ON T1.ORG_ID = T5.RID ");
        sql.append(" LEFT JOIN TS_ZONE T4 ON T5.ZONE_ID = T4.RID ");
        sql.append(" WHERE T.ON_DUTY = 1 and TT.STATE = 1 ");


        sql.append(" ) ");
        sql.append(" select T.* from LatestUnit T where T.ROWNMBER=1 ");
        // 地区
        if (StringUtils.isNotBlank(searchZoneGb)) {
            sql.append(" and T.ZONE_GB like :searchZoneGb escape '\\\'");
            this.paramMap.put("searchZoneGb", ZoneUtil.zoneSelect(searchZoneGb.trim()) + "%");
        }
        // 单位名称
        if (StringUtils.isNotBlank(searchUnitName)) {
            sql.append(" and T.UNITNAME like :searchUnitName escape '\\\'");
            this.paramMap.put("searchUnitName", "%" + StringUtils.convertBFH(searchUnitName.trim()) + "%");
        }
        // 姓名
        if (StringUtils.isNotBlank(searchName)) {
            sql.append(" and T.EMP_NAME like :searchName escape '\\\'");
            this.paramMap.put("searchName", "%" + StringUtils.convertBFH(searchName.trim()) + "%");
        }
        // 根据rid去除已选择的人员 考虑人员rid超过1000个的情况
        if (selectIds != null && selectIds.size() > 0) {
            List<List<String>> lists = StringUtils.splitListProxy(selectIds, 1000);
            sql.append(" AND ( ");
            if (CollectionUtils.isEmpty(lists)) {
                sql.append("  1=2  ");
            } else {
                for (int i = 0; i < lists.size(); i++) {
                    if (i == 0) {
                        sql.append(" T.RID not in  ( ");
                    } else {
                        sql.append(" and T.RID not in  ( ");
                    }
                    List<String> list = lists.get(i);
                    StringBuilder rids = new StringBuilder();
                    for (String rid : list) {
                        rids.append(",").append(rid);
                    }
                    sql.append(rids.substring(1)).append(" ) ");
                }
            }
            sql.append(" ) ");
        }
        // 根据身份证证号去除已选择的人员 考虑人员身份证号超过500个的情况
        if (!CollectionUtils.isEmpty(this.selectIdCards)) {
            List<List<String>> lists = StringUtils.splitListProxy(selectIdCards, 100);
            sql.append(" AND ( ");
            if (CollectionUtils.isEmpty(lists)) {
                sql.append("  1=2  ");
            } else {
                for (int i = 0; i < lists.size(); i++) {
                    if (i == 0) {
                        sql.append(" T.IDC_CARD not in  ( ");
                    } else {
                        sql.append(" and T.IDC_CARD not in  ( ");
                    }
                    List<String> list = lists.get(i);
                    StringBuilder idcs = new StringBuilder();
                    for (String idc : list) {
                        idcs.append(",'").append(idc).append("' ");
                    }
                    sql.append(idcs.substring(1)).append(")");
                }
            }
            sql.append(" ) ");
        }
        String h2 = "SELECT COUNT(*) FROM (" + sql + ")";
        String h1 = "SELECT * FROM (" + sql + ") AA order by AA.ZONE_GB,AA.UNITNAME,AA.EMP_NAME";
        return new String[]{h1, h2};
    }
    /**
     * <p>方法描述：翻页监听</p>
     *
     * @MethodAuthor hsj 2022-08-26 11:13
     */
    public void pageListener() {
        selectedDatas.clear();
        selectedDatas.addAll(allSelectedDatas);
        RequestContext.getCurrentInstance().update(EXTRACT_TABLE_ID);
    }
    @Override
    public List processPackData(List list) {
        List<Object[]> lists = (List<Object[]>) list;
        List<SrvorgPsnByZoneVo> data = new ArrayList<>();
        if (!CollectionUtils.isEmpty(lists)) {
            for (Object[] obj : lists) {
                SrvorgPsnByZoneVo SrvorgPsnByZoneVo = new SrvorgPsnByZoneVo();
                SrvorgPsnByZoneVo.setRid(obj[0] == null ? null : ((BigDecimal) obj[0]).intValue());
                SrvorgPsnByZoneVo.setZoneName(obj[1] == null ? null : obj[1].toString());
                SrvorgPsnByZoneVo.setUnitName(obj[2] == null ? null : obj[2].toString());
                SrvorgPsnByZoneVo.setEmpName(obj[3] == null ? null : obj[3].toString());
                SrvorgPsnByZoneVo.setSex(obj[4] == null ? null : obj[4].toString());
                SrvorgPsnByZoneVo.setTitleName(obj[5] == null ? null : obj[5].toString());
                SrvorgPsnByZoneVo.setZoneId(obj[7] == null ? null : ((BigDecimal) obj[7]).intValue());
                SrvorgPsnByZoneVo.setIdcCard(obj[8] == null ? null : obj[8].toString());
                SrvorgPsnByZoneVo.setMobileNo(obj[9] == null ? null : obj[9].toString());
                SrvorgPsnByZoneVo.setTitleRid(obj[10] == null ? null : ((BigDecimal) obj[10]).intValue());
                SrvorgPsnByZoneVo.setTitleCentPath(obj[11] == null ? null : obj[11].toString());
                SrvorgPsnByZoneVo.setUnitRid(obj[12] == null ? null : ((BigDecimal) obj[12]).intValue());
                data.add(SrvorgPsnByZoneVo);
            }
        }
        return data;
    }
    /**
     * <p>方法描述：行选中 监听事件</p>
     *
     * @MethodAuthor hsj 2022-08-26 9:52
     */
    public void rowSelectListener(SelectEvent event) {
        SrvorgPsnByZoneVo dto = (SrvorgPsnByZoneVo) event.getObject();
        if (!allSelectedDatas.contains(dto)) {
            allSelectedDatas.add(dto);
        }
    }
    /**
     * <p>方法描述：行取消 监听事件</p>
     *
     * @MethodAuthor hsj 2022-08-26 9:53
     */
    public void rowUnselectListener(UnselectEvent event) {
        SrvorgPsnByZoneVo dto = (SrvorgPsnByZoneVo) event.getObject();
        allSelectedDatas.remove(dto);
    }
    /**
     * <p>方法描述：全选中/取消  监听事件</p>
     *
     * @MethodAuthor hsj 2022-08-26 9:54
     */
    public void toggleSelectListener(ToggleSelectEvent event) {
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent(EXTRACT_TABLE_ID);
        List<SrvorgPsnByZoneVo> displayList = this.extractDataModel.getPackData();
        if (CollectionUtils.isEmpty(displayList)) {
            // 表格无数据
            return;
        }
        if (event.isSelected()) {
            // 当前datatable选中的项
            List<SrvorgPsnByZoneVo> list = (List<SrvorgPsnByZoneVo>) dataTable.getSelection();
            if (!CollectionUtils.isEmpty(list)) {
                for (SrvorgPsnByZoneVo o : list) {
                    if (!allSelectedDatas.contains(o)) {
                        allSelectedDatas.add(o);
                    }
                }
            }

        } else {// 取消全选，需要移除当前页的所有元素
            allSelectedDatas.removeAll(displayList);
        }
    }


    public void submitAction() {
        if (null == allSelectedDatas || allSelectedDatas.size() == 0) {
            JsfUtil.addErrorMessage("请选择人员！");
            return;
        }
        Map<String, List<SrvorgPsnByZoneVo>> map = new HashMap<String, List<SrvorgPsnByZoneVo>>();
        map.put("selectPros", allSelectedDatas);
        RequestContext.getCurrentInstance().closeDialog(map);
    }
    /**
     * <p>方法描述：关闭</p>
     *
     * @MethodAuthor qrr, 2018年4月9日, dialogClose
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

    public String getSearchName() {
        return searchName;
    }
    public void setSearchName(String searchName) {
        this.searchName = searchName;
    }


    public List<String> getSelectIds() {
        return selectIds;
    }
    public void setSelectIds(List<String> selectIds) {
        this.selectIds = selectIds;
    }
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    public List<SrvorgPsnByZoneVo> getSelectedDatas() {
        return selectedDatas;
    }

    public void setSelectedDatas(List<SrvorgPsnByZoneVo> selectedDatas) {
        this.selectedDatas = selectedDatas;
    }

    public List<SrvorgPsnByZoneVo> getAllSelectedDatas() {
        return allSelectedDatas;
    }

    public void setAllSelectedDatas(List<SrvorgPsnByZoneVo> allSelectedDatas) {
        this.allSelectedDatas = allSelectedDatas;
    }


    public PackLazyDataModel getExtractDataModel() {
        return extractDataModel;
    }

    public void setExtractDataModel(PackLazyDataModel extractDataModel) {
        this.extractDataModel = extractDataModel;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }
    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }
    public String getSearchZoneGb() {
        return searchZoneGb;
    }
    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }
    public String getSearchZoneName() {
        return searchZoneName;
    }
    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }
    public String getSearchUnitName() {
        return searchUnitName;
    }
    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public String getTitle() {
        return title;
    }
    public void setTitle(String title) {
        this.title = title;
    }

    public List<String> getSelectIdCards() {
        return selectIdCards;
    }
    public void setSelectIdCards(List<String> selectIdCards) {
        this.selectIdCards = selectIdCards;
    }
}
