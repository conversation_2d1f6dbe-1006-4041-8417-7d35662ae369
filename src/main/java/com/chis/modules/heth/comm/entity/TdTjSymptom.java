package com.chis.modules.heth.comm.entity;

import java.math.BigInteger;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 问诊症状信息
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置<br/>
 *
 * 修改人：wlj; 修改时间：2014-09-19<br/>
 * 修改内容：修改症状属性，关联到码表<br/>
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 */
@Entity
@Table(name = "TD_TJ_SYMPTOM")
@SequenceGenerator(name = "TdTjSymptom_Seq", sequenceName = "TD_TJ_SYMPTOM_SEQ", allocationSize = 1)
public class TdTjSymptom implements java.io.Serializable {

    private static final long serialVersionUID = -3506375035296795490L;
    private BigInteger rid;
    private TdTjBhk tdTjBhk;
    private TsSimpleCode symId;
    private String othsym;
    private Date chkdat;
    private String chkdoct;
    private Date createDate;
    private Integer createManid;

    public TdTjSymptom() {
    }

    /** minimal constructor */
    public TdTjSymptom(BigInteger rid, TdTjBhk tdTjBhk, TsSimpleCode symId,
                       Date chkdat, String chkdoct, Date createDate,
                       Integer createManid) {
        this.rid = rid;
        this.tdTjBhk = tdTjBhk;
        this.symId = symId;
        this.chkdat = chkdat;
        this.chkdoct = chkdoct;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    /** full constructor */
    public TdTjSymptom(BigInteger rid, TdTjBhk tdTjBhk, TsSimpleCode symId,
                       String othsym, Date chkdat, String chkdoct, Date createDate,
                       Integer createManid) {
        this.rid = rid;
        this.tdTjBhk = tdTjBhk;
        this.symId = symId;
        this.othsym = othsym;
        this.chkdat = chkdat;
        this.chkdoct = chkdoct;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjSymptom_Seq")
    public BigInteger getRid() {
        return this.rid;
    }

    public void setRid(BigInteger rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "BHK_ID" )
    public TdTjBhk getTdTjBhk() {
        return this.tdTjBhk;
    }

    public void setTdTjBhk(TdTjBhk tdTjBhk) {
        this.tdTjBhk = tdTjBhk;
    }

    @ManyToOne
    @JoinColumn(name = "SYM_ID" )
    public TsSimpleCode getSymId() {
        return symId;
    }

    public void setSymId(TsSimpleCode symId) {
        this.symId = symId;
    }

    @Column(name = "OTHSYM", length = 200)
    public String getOthsym() {
        return this.othsym;
    }

    public void setOthsym(String othsym) {
        this.othsym = othsym;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "CHKDAT" , length = 7)
    public Date getChkdat() {
        return this.chkdat;
    }

    public void setChkdat(Date chkdat) {
        this.chkdat = chkdat;
    }

    @Column(name = "CHKDOCT" , length = 20)
    public String getChkdoct() {
        return this.chkdoct;
    }

    public void setChkdoct(String chkdoct) {
        this.chkdoct = chkdoct;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }
}
