package com.chis.modules.heth.comm.entity;

import java.math.BigInteger;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * 问诊(非)放射职业史信息表
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置<br/>
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 */
@Entity
@Table(name = "TD_TJ_EMHISTORY")
@SequenceGenerator(name = "TdTjEmhistory_Seq", sequenceName = "TD_TJ_EMHISTORY_SEQ", allocationSize = 1)
public class TdTjEmhistory implements java.io.Serializable {

    private static final long serialVersionUID = 3886560722172224566L;
    private BigInteger rid;
    private TdTjBhk tdTjBhk;
    private Integer hisType;
    private Integer num;
    private String stastpDate;
    private String unitName;
    private String department;
    private String workType;
    private String prfraysrt;
    private String defendStep;
    private String prfwrklod;
    private String prfshnvlu;
    private String prfexcshn;
    private String prfraysrt2;
    private String prfraysrtcods;
    private String fsszl;
    private String chkdoct;
    private Date chkdat;
    private Date createDate;
    private Integer createManid;

    // Constructors

    /** default constructor */
    public TdTjEmhistory() {
    }

    /** minimal constructor */
    public TdTjEmhistory(BigInteger rid, TdTjBhk tdTjBhk, Integer hisType,
                         String stastpDate, String unitName, Date chkdat, Date createDate,
                         Integer createManid) {
        this.rid = rid;
        this.tdTjBhk = tdTjBhk;
        this.hisType = hisType;
        this.stastpDate = stastpDate;
        this.unitName = unitName;
        this.chkdat = chkdat;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    /** full constructor */
    public TdTjEmhistory(BigInteger rid, TdTjBhk tdTjBhk, Integer hisType,
                         Integer num, String stastpDate, String unitName, String department,
                         String workType, String prfraysrt, String defendStep,
                         String prfwrklod, String prfshnvlu, String prfexcshn,
                         String prfraysrt2, String prfraysrtcods, String fsszl,
                         String chkdoct, Date chkdat, Date createDate, Integer createManid) {
        this.rid = rid;
        this.tdTjBhk = tdTjBhk;
        this.hisType = hisType;
        this.num = num;
        this.stastpDate = stastpDate;
        this.unitName = unitName;
        this.department = department;
        this.workType = workType;
        this.prfraysrt = prfraysrt;
        this.defendStep = defendStep;
        this.prfwrklod = prfwrklod;
        this.prfshnvlu = prfshnvlu;
        this.prfexcshn = prfexcshn;
        this.prfraysrt2 = prfraysrt2;
        this.prfraysrtcods = prfraysrtcods;
        this.fsszl = fsszl;
        this.chkdoct = chkdoct;
        this.chkdat = chkdat;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjEmhistory_Seq")
    public BigInteger getRid() {
        return this.rid;
    }

    public void setRid(BigInteger rid) {
        this.rid = rid;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BHK_ID" )
    public TdTjBhk getTdTjBhk() {
        return this.tdTjBhk;
    }

    public void setTdTjBhk(TdTjBhk tdTjBhk) {
        this.tdTjBhk = tdTjBhk;
    }

    @Column(name = "HIS_TYPE" )
    public Integer getHisType() {
        return this.hisType;
    }

    public void setHisType(Integer hisType) {
        this.hisType = hisType;
    }

    @Column(name = "NUM")
    public Integer getNum() {
        return this.num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    @Column(name = "STASTP_DATE" , length = 100)
    public String getStastpDate() {
        return this.stastpDate;
    }

    public void setStastpDate(String stastpDate) {
        this.stastpDate = stastpDate;
    }

    @Column(name = "UNIT_NAME" , length = 50)
    public String getUnitName() {
        return this.unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Column(name = "DEPARTMENT", length = 100)
    public String getDepartment() {
        return this.department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    @Column(name = "WORK_TYPE", length = 50)
    public String getWorkType() {
        return this.workType;
    }

    public void setWorkType(String workType) {
        this.workType = workType;
    }

    @Column(name = "PRFRAYSRT", length = 100)
    public String getPrfraysrt() {
        return this.prfraysrt;
    }

    public void setPrfraysrt(String prfraysrt) {
        this.prfraysrt = prfraysrt;
    }

    @Column(name = "DEFEND_STEP", length = 50)
    public String getDefendStep() {
        return this.defendStep;
    }

    public void setDefendStep(String defendStep) {
        this.defendStep = defendStep;
    }

    @Column(name = "PRFWRKLOD", length = 100)
    public String getPrfwrklod() {
        return this.prfwrklod;
    }

    public void setPrfwrklod(String prfwrklod) {
        this.prfwrklod = prfwrklod;
    }

    @Column(name = "PRFSHNVLU", length = 100)
    public String getPrfshnvlu() {
        return this.prfshnvlu;
    }

    public void setPrfshnvlu(String prfshnvlu) {
        this.prfshnvlu = prfshnvlu;
    }

    @Column(name = "PRFEXCSHN", length = 100)
    public String getPrfexcshn() {
        return this.prfexcshn;
    }

    public void setPrfexcshn(String prfexcshn) {
        this.prfexcshn = prfexcshn;
    }

    @Column(name = "PRFRAYSRT2", length = 500)
    public String getPrfraysrt2() {
        return this.prfraysrt2;
    }

    public void setPrfraysrt2(String prfraysrt2) {
        this.prfraysrt2 = prfraysrt2;
    }

    @Column(name = "PRFRAYSRTCODS", length = 200)
    public String getPrfraysrtcods() {
        return this.prfraysrtcods;
    }

    public void setPrfraysrtcods(String prfraysrtcods) {
        this.prfraysrtcods = prfraysrtcods;
    }

    @Column(name = "FSSZL", length = 200)
    public String getFsszl() {
        return this.fsszl;
    }

    public void setFsszl(String fsszl) {
        this.fsszl = fsszl;
    }

    @Column(name = "CHKDOCT", length = 50)
    public String getChkdoct() {
        return this.chkdoct;
    }

    public void setChkdoct(String chkdoct) {
        this.chkdoct = chkdoct;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "CHKDAT" , length = 7)
    public Date getChkdat() {
        return this.chkdat;
    }

    public void setChkdat(Date chkdat) {
        this.chkdat = chkdat;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }
}
