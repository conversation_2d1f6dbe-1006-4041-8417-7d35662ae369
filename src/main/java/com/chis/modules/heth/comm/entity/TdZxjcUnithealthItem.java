package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-4
 */
@Entity
@Table(name = "TD_ZXJC_UNITHEALTH_ITEM")
@SequenceGenerator(name = "TdZxjcUnithealthItem", sequenceName = "TD_ZXJC_UNITHEALTH_ITEM_SEQ", allocationSize = 1)
public class TdZxjcUnithealthItem implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZxjcUnitHethCus fkByMainId;
	private Integer ifheaFactor;
	private Integer type;
	private TsSimpleCode fkByFactorId;
	private Integer checkMonitorPeoples;
	private Integer checkShouldPeoples;
	private Integer checkActualPeoples;
	private Integer unusualNum;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private String factorName;
	
	public TdZxjcUnithealthItem() {
	}

	public TdZxjcUnithealthItem(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZxjcUnithealthItem")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZxjcUnitHethCus getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZxjcUnitHethCus fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "IFHEA_FACTOR")	
	public Integer getIfheaFactor() {
		return ifheaFactor;
	}

	public void setIfheaFactor(Integer ifheaFactor) {
		this.ifheaFactor = ifheaFactor;
	}	
			
	@Column(name = "TYPE")	
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}	
			
	@ManyToOne
	@JoinColumn(name = "FACTOR_ID")			
	public TsSimpleCode getFkByFactorId() {
		return fkByFactorId;
	}

	public void setFkByFactorId(TsSimpleCode fkByFactorId) {
		this.fkByFactorId = fkByFactorId;
	}	
			
	@Column(name = "CHECK_MONITOR_PEOPLES")	
	public Integer getCheckMonitorPeoples() {
		return checkMonitorPeoples;
	}

	public void setCheckMonitorPeoples(Integer checkMonitorPeoples) {
		this.checkMonitorPeoples = checkMonitorPeoples;
	}	
			
	@Column(name = "CHECK_SHOULD_PEOPLES")	
	public Integer getCheckShouldPeoples() {
		return checkShouldPeoples;
	}

	public void setCheckShouldPeoples(Integer checkShouldPeoples) {
		this.checkShouldPeoples = checkShouldPeoples;
	}	
			
	@Column(name = "CHECK_ACTUAL_PEOPLES")	
	public Integer getCheckActualPeoples() {
		return checkActualPeoples;
	}

	public void setCheckActualPeoples(Integer checkActualPeoples) {
		this.checkActualPeoples = checkActualPeoples;
	}	
			
	@Column(name = "UNUSUAL_NUM")	
	public Integer getUnusualNum() {
		return unusualNum;
	}

	public void setUnusualNum(Integer unusualNum) {
		this.unusualNum = unusualNum;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
	@Column(name = "FACTOR_NAME")	
	public String getFactorName() {
		return factorName;
	}

	public void setFactorName(String factorName) {
		this.factorName = factorName;
	}	
			
}