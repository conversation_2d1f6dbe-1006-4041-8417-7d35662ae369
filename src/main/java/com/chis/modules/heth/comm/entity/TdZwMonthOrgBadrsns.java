package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2024-8-8
 */
@Entity
@Table(name = "TD_ZW_MONTH_ORG_BADRSNS")
@SequenceGenerator(name = "TdZwMonthOrgBadrsns", sequenceName = "TD_ZW_MONTH_ORG_BADRSNS_SEQ", allocationSize = 1)
public class TdZwMonthOrgBadrsns implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwMonthOrg fkByMainId;
    private TsSimpleCode fkByBadrsnId;
    private Integer holdCardNum;
    private Integer bhkNum;
    private Integer suspectedNum;
    private Integer contraindlistNum;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    public TdZwMonthOrgBadrsns() {
    }

    public TdZwMonthOrgBadrsns(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwMonthOrgBadrsns")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwMonthOrg getFkByMainId() {
        return this.fkByMainId;
    }

    public void setFkByMainId(TdZwMonthOrg fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "BADRSN_ID")
    public TsSimpleCode getFkByBadrsnId() {
        return this.fkByBadrsnId;
    }

    public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
        this.fkByBadrsnId = fkByBadrsnId;
    }

    @Column(name = "HOLD_CARD_NUM")
    public Integer getHoldCardNum() {
        return this.holdCardNum;
    }

    public void setHoldCardNum(Integer holdCardNum) {
        this.holdCardNum = holdCardNum;
    }

    @Column(name = "BHK_NUM")
    public Integer getBhkNum() {
        return this.bhkNum;
    }

    public void setBhkNum(Integer bhkNum) {
        this.bhkNum = bhkNum;
    }

    @Column(name = "SUSPECTED_NUM")
    public Integer getSuspectedNum() {
        return this.suspectedNum;
    }

    public void setSuspectedNum(Integer suspectedNum) {
        this.suspectedNum = suspectedNum;
    }

    @Column(name = "CONTRAINDLIST_NUM")
    public Integer getContraindlistNum() {
        return this.contraindlistNum;
    }

    public void setContraindlistNum(Integer contraindlistNum) {
        this.contraindlistNum = contraindlistNum;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

}