package com.chis.modules.heth.comm.web.gxjtymys;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ManagedBean(name = "cfbhzjzjzxxcxBean")
@ViewScoped
public class CfbhzjzjzxxcxBean {
    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private List<Object[]> dataList = new ArrayList<>();
    private List<Object[]> dataList1 = new ArrayList<>();
    private String perPageSize = "20,50,100";
    private List<TsZone> zoneList = new ArrayList<>();
    private String searchZoneCode;
    private String searchZoneName;
    private Date searchHospitalizationStartDate;
    private Date searchHospitalizationEndDate;
    private Date searchDischargeStartDate;
    private Date searchDischargeEndDate;
    private Date searchInputStartDate;
    private Date searchInputEndDate;

    public CfbhzjzjzxxcxBean() {
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (CollectionUtils.isEmpty(this.zoneList)) {
            this.zoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "");
            this.searchZoneCode = this.zoneList.get(0).getZoneCode();
            this.searchZoneName = this.zoneList.get(0).getZoneName();
        }
        Date date1 = DateUtils.getYearFirstDay(new Date());
        Date date2 = DateUtils.parseDate("2023-04-20");
        this.searchHospitalizationStartDate = date1;
        this.searchHospitalizationEndDate = date2;
        this.searchDischargeStartDate = date1;
        this.searchDischargeEndDate = date2;
        this.searchInputStartDate = date1;
        this.searchInputEndDate = date2;
        this.dataList.add(new Object[]{"1", "测试人员", "320682198111287293", "测试单位", "2023-1-5", "3600", "1200"});
        this.dataList1.add(new Object[]{"陕西省", "1044", "126", "216", "306", "396"});
        this.dataList1.add(new Object[]{"宝鸡市", "100", "10", "20", "30", "40"});
        this.dataList1.add(new Object[]{"咸阳市", "104", "11", "21", "31", "41"});
        this.dataList1.add(new Object[]{"铜川市", "108", "12", "22", "32", "42"});
        this.dataList1.add(new Object[]{"渭南市", "112", "13", "23", "33", "43"});
        this.dataList1.add(new Object[]{"延安市", "116", "14", "24", "34", "44"});
        this.dataList1.add(new Object[]{"榆林市", "120", "15", "25", "35", "45"});
        this.dataList1.add(new Object[]{"汉中市", "124", "16", "26", "36", "46"});
        this.dataList1.add(new Object[]{"安康市", "128", "17", "27", "37", "47"});
        this.dataList1.add(new Object[]{"商洛市", "132", "18", "28", "38", "48"});
    }

    public List<Object[]> getDataList() {
        return dataList;
    }

    public void setDataList(List<Object[]> dataList) {
        this.dataList = dataList;
    }

    public List<Object[]> getDataList1() {
        return dataList1;
    }

    public void setDataList1(List<Object[]> dataList1) {
        this.dataList1 = dataList1;
    }

    public String getPerPageSize() {
        return perPageSize;
    }

    public void setPerPageSize(String perPageSize) {
        this.perPageSize = perPageSize;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public Date getSearchHospitalizationStartDate() {
        return searchHospitalizationStartDate;
    }

    public void setSearchHospitalizationStartDate(Date searchHospitalizationStartDate) {
        this.searchHospitalizationStartDate = searchHospitalizationStartDate;
    }

    public Date getSearchHospitalizationEndDate() {
        return searchHospitalizationEndDate;
    }

    public void setSearchHospitalizationEndDate(Date searchHospitalizationEndDate) {
        this.searchHospitalizationEndDate = searchHospitalizationEndDate;
    }

    public Date getSearchDischargeStartDate() {
        return searchDischargeStartDate;
    }

    public void setSearchDischargeStartDate(Date searchDischargeStartDate) {
        this.searchDischargeStartDate = searchDischargeStartDate;
    }

    public Date getSearchDischargeEndDate() {
        return searchDischargeEndDate;
    }

    public void setSearchDischargeEndDate(Date searchDischargeEndDate) {
        this.searchDischargeEndDate = searchDischargeEndDate;
    }

    public Date getSearchInputStartDate() {
        return searchInputStartDate;
    }

    public void setSearchInputStartDate(Date searchInputStartDate) {
        this.searchInputStartDate = searchInputStartDate;
    }

    public Date getSearchInputEndDate() {
        return searchInputEndDate;
    }

    public void setSearchInputEndDate(Date searchInputEndDate) {
        this.searchInputEndDate = searchInputEndDate;
    }
}
