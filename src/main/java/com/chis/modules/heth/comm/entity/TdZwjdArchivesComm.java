package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;

import javax.persistence.*;
import java.util.Date;

/**
 * <p>类描述：职业病鉴定档案表</p>
 * @ClassAuthor qrr,2018年10月18日,TdZwjdArchives
 * 
 * <p>修订内容：新增职业病Id，关联诊断记录、鉴定记录</p>
 * @ClassReviser qrr,2018年11月6日,TdZwjdArchives
 */
@Entity
@Table(name = "TD_ZWJD_ARCHIVES")
@SequenceGenerator(name = "TdZwjdArchives", sequenceName = "TD_ZWJD_ARCHIVES_SEQ", allocationSize = 1)
public class TdZwjdArchivesComm implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer aprsType;
	private String applyName;
	private String personnelName;
	private String sex;
	private Date birthday;
	private String idc;
	private String address;
	private String postcode;
	private String linktel;
	private TbTjCrpt fkByCrptId;
	private String applyJdRsn;
	private String otherDesc;
	/**申请日期*/
	private Date applyDate;
	/**诊断机构收集日期*/
	private Date diagCollDate;
	/**诊断机构提供材料日期*/
	private Date diagTgMatriDate;
	/**鉴定机构收集日期*/
	private Date appraiseCollDate;
	/**鉴定机构提供材料日期*/
	private Date appraiseTgMatriDate;
	/**当事人收集日期*/
	private Date partyCollDate;
	/**协助当事人提供材料日期*/
	private Date xzPartyTgDate;
	/**当事人提供材料日期*/
	private Date partyTgMatriDate;
	/**当事人补正日期*/
	private Date partyBzDate;
	/**当事人补正材料日期*/
	private Date partyBzclDate;
	/**鉴定中止日期*/
	private Date appraiseEndDate;
	/**完成处理日期*/
	private Date finishStopDate;
	/**受理日期*/
	private Date acceptDate;
	/**鉴定抽取日期*/
	private Date extractDate;
	/**抽取通知书日期*/
	private Date extractNtcDate;
	/**意见反馈日期*/
	private Date advFeedbackDate;
	/**医学检查日期*/
	private Date mdlChkDate;
	/**鉴定会日期*/
	private Date aprsMeetDate;
	/**鉴定会通知书日期*/
	private Date aprsMeetNtcDate;
	/**鉴定陈诉日期*/
	private Date aprsDclDate;
	/**鉴定专家讨论开始日期*/
	private Date aprsExpertBgnDate;
	/**鉴定专家讨论结束日期*/
	private Date aprsExpertEndDate;
	/**鉴定书日期*/
	private Date aprsCentDate;
	/**通知签收日期*/
	private Date ntcSignDate;
	/**签收日期*/
	private Date signDate;
	private Integer state;
	private Integer delMark;
	private Integer ifStop;
	private String stopRsn;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private TsUnit fkByOrgId;
	private String photoPath;
	/**鉴定职业病*/
	private TsSimpleCode fkByOccdiseId;
	private TdZwOccdiscaseComm fkByDiagId;
	private TdZwjdArchivesComm fkByAprsId;
	private String occdiseName;
	private String archNo;
	private String archCodeTemp;
	/**当前节点是否超期*/
	private Boolean curInOutTime;
	/**是否超期*/
	private String ifOutTime;
	/**职业病鉴定协助检查日期*/
	private Date xzCheckDate;
	/**是否为职业病20181121*/
	private Integer ifDis;
	/**协助函区分环节       第二环节:true 和 第五环节：false  */
	private boolean distinctXz;
	/**专家鉴定环节--专家通知书id*/
	private Integer expertNtcId;
	/**职业病诊断鉴定结论报告书 日期*/
	private Date aprsResultDate;
	private TsSimpleCode fkByApplySmallId;

	/** 鉴定受理的收齐材料日期 注意 赋值逻辑在当前实体类get方法中 */
	private Date jdCollectDate;

	public TdZwjdArchivesComm() {
	}

	public TdZwjdArchivesComm(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwjdArchives")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "APRS_TYPE")	
	public Integer getAprsType() {
		return aprsType;
	}

	public void setAprsType(Integer aprsType) {
		this.aprsType = aprsType;
	}	
			
	@Column(name = "APPLY_NAME")	
	public String getApplyName() {
		return applyName;
	}

	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}	
			
	@Column(name = "PERSONNEL_NAME")	
	public String getPersonnelName() {
		return personnelName;
	}

	public void setPersonnelName(String personnelName) {
		this.personnelName = personnelName;
	}	
			
	@Column(name = "SEX")	
	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "BIRTHDAY")			
	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}	
			
	@Column(name = "IDC")	
	public String getIdc() {
		return idc;
	}

	public void setIdc(String idc) {
		this.idc = idc;
	}	
		
	@Column(name = "ADDRESS")
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}
	
	@Column(name = "POSTCODE")	
	public String getPostcode() {
		return postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}	
			
	@Column(name = "LINKTEL")	
	public String getLinktel() {
		return linktel;
	}

	public void setLinktel(String linktel) {
		this.linktel = linktel;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_ID")			
	public TbTjCrpt getFkByCrptId() {
		return fkByCrptId;
	}

	public void setFkByCrptId(TbTjCrpt fkByCrptId) {
		this.fkByCrptId = fkByCrptId;
	}	
			
	@Column(name = "APPLY_JD_RSN")	
	public String getApplyJdRsn() {
		return applyJdRsn;
	}

	public void setApplyJdRsn(String applyJdRsn) {
		this.applyJdRsn = applyJdRsn;
	}	
			
	@Column(name = "OTHER_DESC")	
	public String getOtherDesc() {
		return otherDesc;
	}

	public void setOtherDesc(String otherDesc) {
		this.otherDesc = otherDesc;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "APPLY_DATE")			
	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DIAG_COLL_DATE")			
	public Date getDiagCollDate() {
		return diagCollDate;
	}

	public void setDiagCollDate(Date diagCollDate) {
		this.diagCollDate = diagCollDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DIAG_TG_MATRI_DATE")			
	public Date getDiagTgMatriDate() {
		return diagTgMatriDate;
	}

	public void setDiagTgMatriDate(Date diagTgMatriDate) {
		this.diagTgMatriDate = diagTgMatriDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "APPRAISE_COLL_DATE")			
	public Date getAppraiseCollDate() {
		return appraiseCollDate;
	}

	public void setAppraiseCollDate(Date appraiseCollDate) {
		this.appraiseCollDate = appraiseCollDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "APPRAISE_TG_MATRI_DATE")			
	public Date getAppraiseTgMatriDate() {
		return appraiseTgMatriDate;
	}

	public void setAppraiseTgMatriDate(Date appraiseTgMatriDate) {
		this.appraiseTgMatriDate = appraiseTgMatriDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "PARTY_COLL_DATE")			
	public Date getPartyCollDate() {
		return partyCollDate;
	}

	public void setPartyCollDate(Date partyCollDate) {
		this.partyCollDate = partyCollDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "XZ_PARTY_TG_DATE")			
	public Date getXzPartyTgDate() {
		return xzPartyTgDate;
	}

	public void setXzPartyTgDate(Date xzPartyTgDate) {
		this.xzPartyTgDate = xzPartyTgDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "PARTY_TG_MATRI_DATE")			
	public Date getPartyTgMatriDate() {
		return partyTgMatriDate;
	}

	public void setPartyTgMatriDate(Date partyTgMatriDate) {
		this.partyTgMatriDate = partyTgMatriDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "PARTY_BZ_DATE")			
	public Date getPartyBzDate() {
		return partyBzDate;
	}

	public void setPartyBzDate(Date partyBzDate) {
		this.partyBzDate = partyBzDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "PARTY_BZCL_DATE")			
	public Date getPartyBzclDate() {
		return partyBzclDate;
	}

	public void setPartyBzclDate(Date partyBzclDate) {
		this.partyBzclDate = partyBzclDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "APPRAISE_END_DATE")			
	public Date getAppraiseEndDate() {
		return appraiseEndDate;
	}

	public void setAppraiseEndDate(Date appraiseEndDate) {
		this.appraiseEndDate = appraiseEndDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "FINISH_STOP_DATE")			
	public Date getFinishStopDate() {
		return finishStopDate;
	}

	public void setFinishStopDate(Date finishStopDate) {
		this.finishStopDate = finishStopDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "ACCEPT_DATE")			
	public Date getAcceptDate() {
		return acceptDate;
	}

	public void setAcceptDate(Date acceptDate) {
		this.acceptDate = acceptDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EXTRACT_DATE")			
	public Date getExtractDate() {
		return extractDate;
	}

	public void setExtractDate(Date extractDate) {
		this.extractDate = extractDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "EXTRACT_NTC_DATE")			
	public Date getExtractNtcDate() {
		return extractNtcDate;
	}

	public void setExtractNtcDate(Date extractNtcDate) {
		this.extractNtcDate = extractNtcDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "ADV_FEEDBACK_DATE")			
	public Date getAdvFeedbackDate() {
		return advFeedbackDate;
	}

	public void setAdvFeedbackDate(Date advFeedbackDate) {
		this.advFeedbackDate = advFeedbackDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "MDL_CHK_DATE")			
	public Date getMdlChkDate() {
		return mdlChkDate;
	}

	public void setMdlChkDate(Date mdlChkDate) {
		this.mdlChkDate = mdlChkDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "APRS_MEET_DATE")			
	public Date getAprsMeetDate() {
		return aprsMeetDate;
	}

	public void setAprsMeetDate(Date aprsMeetDate) {
		this.aprsMeetDate = aprsMeetDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "APRS_MEET_NTC_DATE")			
	public Date getAprsMeetNtcDate() {
		return aprsMeetNtcDate;
	}

	public void setAprsMeetNtcDate(Date aprsMeetNtcDate) {
		this.aprsMeetNtcDate = aprsMeetNtcDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "APRS_DCL_DATE")			
	public Date getAprsDclDate() {
		return aprsDclDate;
	}

	public void setAprsDclDate(Date aprsDclDate) {
		this.aprsDclDate = aprsDclDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "APRS_EXPERT_BGN_DATE")			
	public Date getAprsExpertBgnDate() {
		return aprsExpertBgnDate;
	}

	public void setAprsExpertBgnDate(Date aprsExpertBgnDate) {
		this.aprsExpertBgnDate = aprsExpertBgnDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "APRS_EXPERT_END_DATE")			
	public Date getAprsExpertEndDate() {
		return aprsExpertEndDate;
	}

	public void setAprsExpertEndDate(Date aprsExpertEndDate) {
		this.aprsExpertEndDate = aprsExpertEndDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "APRS_CENT_DATE")			
	public Date getAprsCentDate() {
		return aprsCentDate;
	}

	public void setAprsCentDate(Date aprsCentDate) {
		this.aprsCentDate = aprsCentDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "NTC_SIGN_DATE")			
	public Date getNtcSignDate() {
		return ntcSignDate;
	}

	public void setNtcSignDate(Date ntcSignDate) {
		this.ntcSignDate = ntcSignDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "SIGN_DATE")			
	public Date getSignDate() {
		return signDate;
	}

	public void setSignDate(Date signDate) {
		this.signDate = signDate;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Column(name = "DEL_MARK")	
	public Integer getDelMark() {
		return delMark;
	}

	public void setDelMark(Integer delMark) {
		this.delMark = delMark;
	}	
			
	@Column(name = "IF_STOP")	
	public Integer getIfStop() {
		return ifStop;
	}

	public void setIfStop(Integer ifStop) {
		this.ifStop = ifStop;
	}	
			
	@Column(name = "STOP_RSN")	
	public String getStopRsn() {
		return stopRsn;
	}

	public void setStopRsn(String stopRsn) {
		this.stopRsn = stopRsn;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	@ManyToOne
	@JoinColumn(name = "ORG_ID")	
	public TsUnit getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TsUnit fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}
	@Column(name="PHOTO_PATH")
	public String getPhotoPath() {
		return photoPath;
	}

	public void setPhotoPath(String photoPath) {
		this.photoPath = photoPath;
	}
	@ManyToOne
	@JoinColumn(name = "OCCDISE_ID")	
	public TsSimpleCode getFkByOccdiseId() {
		return fkByOccdiseId;
	}

	public void setFkByOccdiseId(TsSimpleCode fkByOccdiseId) {
		this.fkByOccdiseId = fkByOccdiseId;
	}
	@ManyToOne
	@JoinColumn(name = "DIAG_ID")
	public TdZwOccdiscaseComm getFkByDiagId() {
		return fkByDiagId;
	}
	public void setFkByDiagId(TdZwOccdiscaseComm fkByDiagId) {
		this.fkByDiagId = fkByDiagId;
	}
	@ManyToOne
	@JoinColumn(name = "APRS_ID")
	public TdZwjdArchivesComm getFkByAprsId() {
		return fkByAprsId;
	}

	public void setFkByAprsId(TdZwjdArchivesComm fkByAprsId) {
		this.fkByAprsId = fkByAprsId;
	}
	@Column(name="OCCDISE_NAME")
	public String getOccdiseName() {
		return occdiseName;
	}

	public void setOccdiseName(String occdiseName) {
		this.occdiseName = occdiseName;
	}
	@Column(name="ARCH_NO")
	public String getArchNo() {
		return archNo;
	}

	public void setArchNo(String archNo) {
		this.archNo = archNo;
	}	
	@Transient
	public String getArchCodeTemp() {
		return archCodeTemp;
	}

	public void setArchCodeTemp(String archCodeTemp) {
		this.archCodeTemp = archCodeTemp;
	}
	@Transient
	public Boolean getCurInOutTime() {
		return curInOutTime;
	}

	public void setCurInOutTime(Boolean curInOutTime) {
		this.curInOutTime = curInOutTime;
	}
	@Column(name = "IF_OUT_TIME")
	public String getIfOutTime() {
		return ifOutTime;
	}

	public void setIfOutTime(String ifOutTime) {
		this.ifOutTime = ifOutTime;
	}

	@Temporal(TemporalType.DATE)
	@Column(name="XZ_CHK_DATE")
	public Date getXzCheckDate() {
		return xzCheckDate;
	}

	public void setXzCheckDate(Date xzCheckDate) {
		this.xzCheckDate = xzCheckDate;
	}

	@Column(name="IS_DIS")
	public Integer getIfDis() {
		return ifDis;
	}

	public void setIfDis(Integer ifDis) {
		this.ifDis = ifDis;
	}

	@Transient
	public boolean isDistinctXz() {
		return distinctXz;
	}

	public void setDistinctXz(boolean distinctXz) {
		this.distinctXz = distinctXz;
	}

	@Transient
	public Integer getExpertNtcId() {
		return expertNtcId;
	}

	public void setExpertNtcId(Integer expertNtcId) {
		this.expertNtcId = expertNtcId;
	}

	@Column(name="APRS_RESULT_DATE")
	public Date getAprsResultDate() {
		return aprsResultDate;
	}

	public void setAprsResultDate(Date aprsResultDate) {
		this.aprsResultDate = aprsResultDate;
	}

	@ManyToOne
	@JoinColumn(name = "APPLY_SMALL_ID")
	public TsSimpleCode getFkByApplySmallId() {
		return fkByApplySmallId;
	}

	public void setFkByApplySmallId(TsSimpleCode fkByApplySmallId) {
		this.fkByApplySmallId = fkByApplySmallId;
	}

	@Transient
	public Date getJdCollectDate() {
		/**
		 * 收齐材料日期：
		 * 省级鉴定：默认诊断机构提供材料日期、鉴定机构提供材料日期、用人单位提供材料日期中三个日期中的最晚的一个日期。
		 * 市级鉴定：默认诊断机构提供材料日期、用人单位提供材料日期中两个日期中的最晚的一个日期。
		 * */
		if (null == this.aprsType) {
			return null;
		}
		jdCollectDate = this.diagTgMatriDate;
		if (null != jdCollectDate && null != this.partyTgMatriDate && this.partyTgMatriDate.after(jdCollectDate)) {
			jdCollectDate = this.partyTgMatriDate;
		}
		if (1 == this.aprsType) {
			return jdCollectDate;
		}
		if (null != jdCollectDate && null != this.appraiseTgMatriDate && this.appraiseTgMatriDate.after(jdCollectDate)) {
			jdCollectDate = this.appraiseTgMatriDate;
		}
		return jdCollectDate;
	}

	public void setJdCollectDate(Date jdCollectDate) {
		this.jdCollectDate = jdCollectDate;
	}
}