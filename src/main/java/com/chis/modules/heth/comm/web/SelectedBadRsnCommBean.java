package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesBean;
import org.primefaces.context.RequestContext;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>类描述：码表弹出框</p>
 * @ClassAuthor qrr,2018年4月9日,CodeRadioSelectBean
 */
@ManagedBean(name = "selectedBadRsnCommBean")
@ViewScoped
public class SelectedBadRsnCommBean extends FacesBean {

    private static final long serialVersionUID = -738445454536625063L;

    /** 名称 或 拼音码*/
    private String searchNamOrPy;
    /** 标题*/
    private String titleName;
    /** 选择的对象*/
    private TsSimpleCode selectPro;
    /** 码表类型*/
    private String typeNo;
    /** 查询列集合*/
    private List<TsSimpleCode> displayList;
    /** 所有集合*/
    private List<TsSimpleCode> allList;
    private List<TsSimpleCode> selectList;
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    private Boolean ifAllSelect = false;
    //码表大类
    private String firstCodeNo;
    private List<TsSimpleCode> firstList;
    //查询条件大类是否显示
    private Boolean ifShowFirstCode = false;
    //是否仅选择一个
    private boolean ifSelf = false;
    /**
 	 * <p>方法描述：初始化数据</p>
 	 * @MethodAuthor qrr,2018年4月9日,CodeRadioSelectBean
     * */
    public SelectedBadRsnCommBean() {
        this.titleName = JsfUtil.getRequest().getParameter("titleName");
        String typeNo = JsfUtil.getRequest().getParameter("typeNo");
        String ifAllSelect = JsfUtil.getRequest().getParameter("ifAllSelect");
        String ifShowFirstCode = JsfUtil.getRequest().getParameter("ifShowFirstCode");
        String extends1 = JsfUtil.getRequest().getParameter("extends1");
        this.typeNo = typeNo;
        if (StringUtils.isNotBlank(ifAllSelect)) {
			if ("true".equals(ifAllSelect)) {
				this.ifAllSelect = true;
			}else if ("false".equals(ifAllSelect)) {
				this.ifAllSelect = false;
			}
		}
        if (StringUtils.isNotBlank(ifShowFirstCode)) {
			if ("true".equals(ifShowFirstCode)) {
				this.ifShowFirstCode = true;
			}else if ("false".equals(ifShowFirstCode)) {
				this.ifShowFirstCode = false;
			}
		}
        String ifSelf = JsfUtil.getRequest().getParameter("ifSelf");
        if (StringUtils.isNotBlank(ifSelf)) {
			if ("true".equals(ifSelf)) {
				this.ifSelf = true;
			}else if ("false".equals(ifSelf)) {
				this.ifSelf = false;
			}
		}
        this.init(extends1);
    }
    /**
 	 * <p>方法描述：查询码表数据</p>
 	 * @MethodAuthor qrr,2018年4月9日,CodeRadioSelectBean
     * */
    private void init(String extends1)   {
        this.displayList = new ArrayList<TsSimpleCode>();
        this.allList = new ArrayList<TsSimpleCode>();
        this.selectList = new ArrayList<TsSimpleCode>();
        firstList = new ArrayList<TsSimpleCode>();
        List<Object[]> list = commService.findSimpleByParam(typeNo,extends1);
        if (null!=list && list.size()>0) {
			for (Object[] obj : list) {
				TsSimpleCode code = new TsSimpleCode();
				code.setRid(null!=obj[0]?Integer.valueOf(obj[0].toString()):null);
				code.setCodeName(null!=obj[1]?obj[1].toString():null);
				code.setCodeLevelNo(null!=obj[3]?obj[3].toString():null);
				code.setCodeNo(null!=obj[2]?obj[2].toString():null);
				code.setSplsht(null!=obj[4]?obj[4].toString():null);
				allList.add(code);
			}
		}
        dealCodelevel(allList);
        if( null != allList && allList.size() > 0)    {
            this.displayList.addAll(allList);
            for (TsSimpleCode t : allList) {
				if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
					firstList.add(t);
				}
			}
        }
    }
    /**
 	 * <p>方法描述：处理码表层级关系</p>
 	 * @MethodAuthor qrr,2018年4月8日,dealCodelevel
	 * */
	private void dealCodelevel(List<TsSimpleCode> list){
		if (list != null && list.size() > 0) {
			for (TsSimpleCode code : list) {
				code.setLevelIndex(StringUtils.countMatches(
						code.getCodeLevelNo(), ".")
						+ "");
			}
		}
	}
    /**
 	 * <p>方法描述：根据名称、拼音码过滤数据</p>
 	 * @MethodAuthor qrr,2018年4月9日,searchAction
     */
    public void searchAction() {
        //初始化展示页面的疫苗数据集
        this.displayList = new ArrayList<TsSimpleCode>();
        if( null != allList && allList.size() > 0)    {
    		if(StringUtils.isNotBlank(searchNamOrPy))    {
    			for(TsSimpleCode t :allList)   {
    				//疫苗名称
    				String codeName = t.getCodeName()==null?"":t.getCodeName().toString();
    				//疫苗拼音码
    				String codePym = t.getSplsht()==null?"":t.getSplsht().toString();
    				//如果模糊匹配上，则增加
    				if(StringUtils.isNotBlank(firstCodeNo))    {
    					if (t.getCodeLevelNo().startsWith(firstCodeNo)&&(codeName.indexOf(searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(searchNamOrPy.toUpperCase()) != -1)) {
    						this.displayList.add(t);
    					}
    				}else {
						if (codeName.indexOf(searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(searchNamOrPy.toUpperCase()) != -1) {
							this.displayList.add(t);
						}
					}
    			}
    		}else{
    			//码表大类
    			if(StringUtils.isNotBlank(firstCodeNo))    {
    				for(TsSimpleCode t :allList)   {
    					if (t.getCodeLevelNo().startsWith(firstCodeNo)) {
    						this.displayList.add(t);
    					}
    				}
    			}else{
    				this.displayList.addAll(allList);
    			}
    		}
    	}

    }

    /**
 	 * <p>方法描述：选择确定方法</p>
 	 * @MethodAuthor qrr,2018年4月9日,selectAction
     */
    public void selectAction() {
        Map<String, Object> map = new HashMap<String, Object>();
        if (ifSelf) {
        	map.put("selectPro",selectPro);
        	RequestContext.getCurrentInstance().closeDialog(map);
		}
        selectList.add(selectPro);
        allList.remove(selectPro);
        displayList.remove(selectPro);
        RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.update("tabView:editForm:tabViewEdit:mainTabView:badRsnTable");
    }
    
    public void submitAction(){
		if (null == selectList ||selectList.size()==0) {
			JsfUtil.addErrorMessage("请选择危害因素");
			return;
		}
		Map<String, Object> map = new HashMap<String, Object>();
        map.put("badfactorList", selectList);
        RequestContext.getCurrentInstance().closeDialog(map);
	}

    /**
 	 * <p>方法描述：关闭</p>
 	 * @MethodAuthor qrr,2018年4月9日,dialogClose
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }


    public String getSearchNamOrPy() {
        return searchNamOrPy;
    }

    public void setSearchNamOrPy(String searchNamOrPy) {
        this.searchNamOrPy = searchNamOrPy;
    }

	public String getTitleName() {
		return titleName;
	}

	public void setTitleName(String titleName) {
		this.titleName = titleName;
	}

	public TsSimpleCode getSelectPro() {
		return selectPro;
	}

	public void setSelectPro(TsSimpleCode selectPro) {
		this.selectPro = selectPro;
	}

	public List<TsSimpleCode> getDisplayList() {
		return displayList;
	}

	public void setDisplayList(List<TsSimpleCode> displayList) {
		this.displayList = displayList;
	}
	public Boolean getIfAllSelect() {
		return ifAllSelect;
	}
	public void setIfAllSelect(Boolean ifAllSelect) {
		this.ifAllSelect = ifAllSelect;
	}
	public String getFirstCodeNo() {
		return firstCodeNo;
	}
	public void setFirstCodeNo(String firstCodeNo) {
		this.firstCodeNo = firstCodeNo;
	}
	public Boolean getIfShowFirstCode() {
		return ifShowFirstCode;
	}
	public void setIfShowFirstCode(Boolean ifShowFirstCode) {
		this.ifShowFirstCode = ifShowFirstCode;
	}
	public List<TsSimpleCode> getFirstList() {
		return firstList;
	}
	public void setFirstList(List<TsSimpleCode> firstList) {
		this.firstList = firstList;
	}
	public List<TsSimpleCode> getSelectList() {
		return selectList;
	}
	public void setSelectList(List<TsSimpleCode> selectList) {
		this.selectList = selectList;
	}
	public boolean isIfSelf() {
		return ifSelf;
	}
	public void setIfSelf(boolean ifSelf) {
		this.ifSelf = ifSelf;
	}
}
