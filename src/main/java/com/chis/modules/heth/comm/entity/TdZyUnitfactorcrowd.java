package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-6
 */
@Entity
@Table(name = "TD_ZY_UNITFACTORCROWD")
@SequenceGenerator(name = "TdZyUnitfactorcrowd", sequenceName = "TD_ZY_UNITFACTORCROWD_SEQ", allocationSize = 1)
public class TdZyUnitfactorcrowd implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZyUnitbasicinfo fkByMainId;
	private Integer ifhfDust;
	private Integer hfDustPeoples;
	private Integer hfDustSiliconPeoples;
	private Integer hfDustCoalPeoples;
	private Integer hfDustAsbestosNum;
	private Integer ifhfChemistry;
	private Integer hfChemistryPeoples;
	private Integer hfChemistryLeadPeoples;
	private Integer hfChemistryBenzenePeoples;
	private Integer ifhfPhysics;
	private Integer hfPhysicsPeoples;
	private Integer hfPhysicsNoisePeoples;
	private Integer ifhfRadioactivity;
	private Integer hfRadioactivityPeoples;
	private Integer ifhfBiology;
	private Integer hfBiologyPeoples;
	private Integer ifhfOther;
	private Integer hfOtherPeoples;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZyUnitfactorcrowd() {
	}

	public TdZyUnitfactorcrowd(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZyUnitfactorcrowd")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZyUnitbasicinfo getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZyUnitbasicinfo fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "IFHF_DUST")	
	public Integer getIfhfDust() {
		return ifhfDust;
	}

	public void setIfhfDust(Integer ifhfDust) {
		this.ifhfDust = ifhfDust;
	}	
			
	@Column(name = "HF_DUST_PEOPLES")	
	public Integer getHfDustPeoples() {
		return hfDustPeoples;
	}

	public void setHfDustPeoples(Integer hfDustPeoples) {
		this.hfDustPeoples = hfDustPeoples;
	}	
			
	@Column(name = "HF_DUST_SILICON_PEOPLES")	
	public Integer getHfDustSiliconPeoples() {
		return hfDustSiliconPeoples;
	}

	public void setHfDustSiliconPeoples(Integer hfDustSiliconPeoples) {
		this.hfDustSiliconPeoples = hfDustSiliconPeoples;
	}	
			
	@Column(name = "HF_DUST_COAL_PEOPLES")	
	public Integer getHfDustCoalPeoples() {
		return hfDustCoalPeoples;
	}

	public void setHfDustCoalPeoples(Integer hfDustCoalPeoples) {
		this.hfDustCoalPeoples = hfDustCoalPeoples;
	}	
			
	@Column(name = "HF_DUST_ASBESTOS_NUM")	
	public Integer getHfDustAsbestosNum() {
		return hfDustAsbestosNum;
	}

	public void setHfDustAsbestosNum(Integer hfDustAsbestosNum) {
		this.hfDustAsbestosNum = hfDustAsbestosNum;
	}	
			
	@Column(name = "IFHF_CHEMISTRY")	
	public Integer getIfhfChemistry() {
		return ifhfChemistry;
	}

	public void setIfhfChemistry(Integer ifhfChemistry) {
		this.ifhfChemistry = ifhfChemistry;
	}	
			
	@Column(name = "HF_CHEMISTRY_PEOPLES")	
	public Integer getHfChemistryPeoples() {
		return hfChemistryPeoples;
	}

	public void setHfChemistryPeoples(Integer hfChemistryPeoples) {
		this.hfChemistryPeoples = hfChemistryPeoples;
	}	
			
	@Column(name = "HF_CHEMISTRY_LEAD_PEOPLES")	
	public Integer getHfChemistryLeadPeoples() {
		return hfChemistryLeadPeoples;
	}

	public void setHfChemistryLeadPeoples(Integer hfChemistryLeadPeoples) {
		this.hfChemistryLeadPeoples = hfChemistryLeadPeoples;
	}	
			
	@Column(name = "HF_CHEMISTRY_BENZENE_PEOPLES")	
	public Integer getHfChemistryBenzenePeoples() {
		return hfChemistryBenzenePeoples;
	}

	public void setHfChemistryBenzenePeoples(Integer hfChemistryBenzenePeoples) {
		this.hfChemistryBenzenePeoples = hfChemistryBenzenePeoples;
	}	
			
	@Column(name = "IFHF_PHYSICS")	
	public Integer getIfhfPhysics() {
		return ifhfPhysics;
	}

	public void setIfhfPhysics(Integer ifhfPhysics) {
		this.ifhfPhysics = ifhfPhysics;
	}	
			
	@Column(name = "HF_PHYSICS_PEOPLES")	
	public Integer getHfPhysicsPeoples() {
		return hfPhysicsPeoples;
	}

	public void setHfPhysicsPeoples(Integer hfPhysicsPeoples) {
		this.hfPhysicsPeoples = hfPhysicsPeoples;
	}	
			
	@Column(name = "HF_PHYSICS_NOISE_PEOPLES")	
	public Integer getHfPhysicsNoisePeoples() {
		return hfPhysicsNoisePeoples;
	}

	public void setHfPhysicsNoisePeoples(Integer hfPhysicsNoisePeoples) {
		this.hfPhysicsNoisePeoples = hfPhysicsNoisePeoples;
	}	
			
	@Column(name = "IFHF_RADIOACTIVITY")	
	public Integer getIfhfRadioactivity() {
		return ifhfRadioactivity;
	}

	public void setIfhfRadioactivity(Integer ifhfRadioactivity) {
		this.ifhfRadioactivity = ifhfRadioactivity;
	}	
			
	@Column(name = "HF_RADIOACTIVITY_PEOPLES")	
	public Integer getHfRadioactivityPeoples() {
		return hfRadioactivityPeoples;
	}

	public void setHfRadioactivityPeoples(Integer hfRadioactivityPeoples) {
		this.hfRadioactivityPeoples = hfRadioactivityPeoples;
	}	
			
	@Column(name = "IFHF_BIOLOGY")	
	public Integer getIfhfBiology() {
		return ifhfBiology;
	}

	public void setIfhfBiology(Integer ifhfBiology) {
		this.ifhfBiology = ifhfBiology;
	}	
			
	@Column(name = "HF_BIOLOGY_PEOPLES")	
	public Integer getHfBiologyPeoples() {
		return hfBiologyPeoples;
	}

	public void setHfBiologyPeoples(Integer hfBiologyPeoples) {
		this.hfBiologyPeoples = hfBiologyPeoples;
	}	
			
	@Column(name = "IFHF_OTHER")	
	public Integer getIfhfOther() {
		return ifhfOther;
	}

	public void setIfhfOther(Integer ifhfOther) {
		this.ifhfOther = ifhfOther;
	}	
			
	@Column(name = "HF_OTHER_PEOPLES")	
	public Integer getHfOtherPeoples() {
		return hfOtherPeoples;
	}

	public void setHfOtherPeoples(Integer hfOtherPeoples) {
		this.hfOtherPeoples = hfOtherPeoples;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}