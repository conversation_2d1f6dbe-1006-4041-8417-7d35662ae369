package com.chis.modules.heth.comm.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.chis.common.utils.DateUtils;
import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.NodeUnselectEvent;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;
import org.springframework.util.CollectionUtils;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdTjBhkClt;
import com.chis.modules.heth.comm.entity.TdTjEmhistoryClt;
import com.chis.modules.heth.comm.service.TdTjEmhistoryCltCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;

/**
 * @Description : 问诊(非)放射职业史信息表（数据录入）-列表页
 * @ClassAuthor : anjing
 * @Date : 2019/5/18 9:34
 **/
public class TdTjEmhistoryCltListCommBean extends FacesEditBean {

    private Integer rid;

    /**体检问诊：职业史列表*/
    private List<TdTjEmhistoryClt> employmentHisList = new ArrayList<>();
    /**体检问诊：放射史列表*/
    private List<TdTjEmhistoryClt> radiationHisList = new ArrayList<>();

    /**添加页面：职业史*/
    private TdTjEmhistoryClt employmentHisClt = new TdTjEmhistoryClt();
    /**添加页面：放射史*/
    private TdTjEmhistoryClt radiationHisClt = new TdTjEmhistoryClt();

    /**关联：体检信息（录入数据）*/
    private TdTjBhkClt fkByBhkId;
    /**类型*/
    private Integer hisType;

    /**添加页面：每日工作时数或工作量下拉*/
    private List<String> prfwrklodList = new ArrayList<String>();
    /**添加页面：职业史累积受照剂量下拉*/
    private List<String> prfshnvluList = new ArrayList<String>();

    /**添加页面：放射线种类下拉*/
    //private List<String> fsszlList = new ArrayList<String>();

    /**有害因素*/
    private TreeNode prfraysrtSortTree;
    private List<String> prfraysrtList;


    /**防护措施树*/
    private TreeNode defendStepSortTree;
    private List<String> defendStepList;

    /**照射种类种类树*/
    private TreeNode prfraysrt2SortTree;
    private List<String> prfraysrt2List;

    /**放射线种类树*/
    private TreeNode fsszlSortTree;
    private List<String> fsszlList;

    private TdTjEmhistoryCltCommServiceImpl tdTjEmhistoryCltService = SpringContextHolder.getBean(TdTjEmhistoryCltCommServiceImpl.class);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /** 职业史起止时间段标记 复选框“无”*/
    private String[] stastpDateMark;
    /** 职业史起止时间段 禁用标记*/
    private Boolean disableDate;
    private Boolean ifZysTimeRange;

    public TdTjEmhistoryCltListCommBean() {
        ifZysTimeRange = "1".equals(commService.findParamValue("IF_ZYS_TIME_RANGE"));
    }

    /**
     * @Description : 参数初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 18:28
     **/
    public void initParam(Integer isShowRad) {
        if(null != this.fkByBhkId && null != this.fkByBhkId.getRid()) {
            // 初始化职业史/放射史列表
            this.initEmployHisList();
            if(isShowRad == 1) {
                this.initRadiHisList();
            }
            // 初始化下拉列表
            this.initSimpleCodeList(this.prfwrklodList, "5302");
            this.initSimpleCodeList(this.prfshnvluList, "5303");
            //this.initSimpleCodeList(this.fsszlList, "5306");

            // 有害因素树初始化
            this.prfraysrtList = new ArrayList<>();
            this.initPrfraysrtTree();
            // 防护措施树初始化
            this.defendStepList = new ArrayList<>();
            this.initDefendStepTree();
            // 照射种类树初始化
            this.prfraysrt2List = new ArrayList<>();
            this.initPrfraysrt2Tree();

            // 放射线种类树初始化
            this.fsszlList = new ArrayList<>();
            this.initFsszlTree();
        }
    }

    /**
     * @Description : 职业史列表-初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 13:50
     **/
    private void initEmployHisList() {
        // 类型 1 放射 2 非放射
        this.employmentHisList = this.tdTjEmhistoryCltService.selectEmhistoryCltListByBhkIdAndHisType(this.fkByBhkId.getRid(), 2);
    }

    /**
     * @Description : 放射史列表-初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 13:51
     **/
    private void initRadiHisList() {
        // 类型 1 放射 2 非放射
        this.radiationHisList = this.tdTjEmhistoryCltService.selectEmhistoryCltListByBhkIdAndHisType(this.fkByBhkId.getRid(), 1);
    }

    /**
     * @Description :有害因素-树初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/29 15:07
     **/
    public void initPrfraysrtTree() {
        List<TsSimpleCode> list = this.commService.findSimpleCodesByTypeNo("5007");
        prfraysrtSortTree = new CheckboxTreeNode("root", null);
        // 调用创建树方法
        this.createtPrfraysrt2Tree(this.prfraysrtSortTree, list);
    }

    /**
     * @Description : 防护措施-树初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/29 15:07
     **/
    public void initDefendStepTree() {
        List<TsSimpleCode> list = this.commService.findSimpleCodesByTypeNo("5307");
        defendStepSortTree = new CheckboxTreeNode("root", null);
        // 调用创建树方法
        this.createtDefendStepTree(this.defendStepSortTree, list);
    }

    /**
     * @Description : 生成防护措施树
     * @MethodAuthor: anjing
     * @Date : 2019/5/29 15:08
     **/
    private void createtDefendStepTree(TreeNode defendStepSortTree, List<TsSimpleCode> list) {
        if (null != list && list.size() > 0) {
            // 只有第一层
            Set<String> firstLevelNoSet = new LinkedHashSet<String>();
            // 没有第一层
            Set<String> levelNoSet = new LinkedHashSet<String>();
            // 所有类别
            Map<String, TsSimpleCode> menuMap = new HashMap<String, TsSimpleCode>();

            for (TsSimpleCode t : list) {
                menuMap.put(t.getCodeName(), t);
                firstLevelNoSet.add(t.getCodeName());
            }
            for (String ln : firstLevelNoSet) {
                TreeNode node = new DefaultTreeNode(menuMap.get(ln), defendStepSortTree);
            }
        }
    }

    /**
     * @Description : 照射种类-树初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/29 15:07
     **/
    public void initPrfraysrt2Tree() {
        List<TsSimpleCode> list = this.commService.findSimpleCodesByTypeNo("5305");
        prfraysrt2SortTree = new CheckboxTreeNode("root", null);
        // 调用创建树方法
        this.createtPrfraysrt2Tree(this.prfraysrt2SortTree, list);
    }

    /**
     * @Description : 生成照射种类树
     * @MethodAuthor: anjing
     * @Date : 2019/5/29 15:08
     **/
    private void createtPrfraysrt2Tree(TreeNode prfraysrt2SortTree, List<TsSimpleCode> list) {
        if (null != list && list.size() > 0) {
            Set<String> firstLevelNoSet = new LinkedHashSet<String>(); // 只有第一层
            Set<String> levelNoSet = new LinkedHashSet<String>(); // 没有第一层
            Map<String, TsSimpleCode> allMap = new HashMap<String, TsSimpleCode>(); // 所有菜单

            for (TsSimpleCode t : list) {
                allMap.put(t.getCodeLevelNo(), t);
                if (StringUtils.isNotBlank(t.getCodeLevelNo())) {
                    if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
                        firstLevelNoSet.add(t.getCodeLevelNo());
                    } else {
                        levelNoSet.add(t.getCodeLevelNo());
                    }
                }
            }

            for (String ln : firstLevelNoSet) {
                TreeNode node = new CheckboxTreeNode(allMap.get(ln), prfraysrt2SortTree);
                node.setSelected(false);
                this.addChildNode(ln, levelNoSet, allMap, node);
            }
        }
    }

    /**
     * @Description : 构建类别树
     * @MethodAuthor: anjing
     * @Date : 2019/5/29 14:47
     **/
    private void addChildNode(String levelNo, Set<String> levelNoSet, Map<String, TsSimpleCode> menuMap, TreeNode parentNode) {
        int level = StringUtils.countMatches(levelNo, ".");
        for (String s : levelNoSet) {
            if (StringUtils.countMatches(s, ".") == (level + 1) && StringUtils.startsWith(s, levelNo + ".")) {
                TreeNode node = new DefaultTreeNode(menuMap.get(s), parentNode);
                node.setSelected(false);
                this.addChildNode(s, levelNoSet, menuMap, node);
            }
        }
    }

    public void openPrfraysrtPanel() {
        this.prfraysrtList = new ArrayList<>();
        RequestContext.getCurrentInstance().execute("PF('PrfraysrtPanel').show()");
    }

    /**
     * @Description : 节点选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:04
     **/
    public void onPrfraysrtNodeSelect(NodeSelectEvent event){
        TreeNode selectNode = event.getTreeNode();
        // 获得第一级的节点
        if (null != selectNode) {
            List<TreeNode> childs = selectNode.getChildren();
            // 得到子级节点 无子级节点（最后一级）刷新输入框，并关闭弹出框
            if (childs == null || childs.size() == 0) {
                TsSimpleCode t = (TsSimpleCode) selectNode.getData();
                this.prfraysrtList.add(t.getCodeName());
            } else {
                for(TreeNode node : childs) {
                    TsSimpleCode t = (TsSimpleCode) node.getData();
                    this.prfraysrtList.add(t.getCodeName());
                }
            }
        }
    }

    /**
     * @Description :节点失去选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:04
     **/
    public void onPrfraysrtNodeNoSelect(NodeUnselectEvent event) {
        TreeNode selectNode = event.getTreeNode();
        // 获得第一级节点
        if (null != selectNode) {
            List<TreeNode> childs = selectNode.getChildren();
            // 得到子级节点 无子级节点（最后一级）刷新输入框，并关闭弹出框
            if (childs == null || childs.size() == 0) {
                TsSimpleCode t = (TsSimpleCode) selectNode.getData();
                this.prfraysrtList.remove(t.getCodeName());
            } else {
                for(TreeNode node : childs) {
                    TsSimpleCode t = (TsSimpleCode) node.getData();
                    this.prfraysrtList.remove(t.getCodeName());
                }
            }
        }
    }

    /**
     * @Description : 隐藏事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:07
     **/
    public void hidePrfraysrtAction() {
        StringBuffer buffer = new StringBuffer();
        this.employmentHisClt.setPrfraysrt(null);
        if (!CollectionUtils.isEmpty(prfraysrtList)) {
            for (String str : prfraysrtList) {
                buffer.append(",").append(str);
            }
            String prfraysrt = buffer.deleteCharAt(0).toString();
            if(prfraysrt.length() > 500) {
                this.employmentHisClt.setPrfraysrt(prfraysrt.substring(0, 500));
            } else {
                this.employmentHisClt.setPrfraysrt(prfraysrt);
            }
        }
    }

    public void openDefendStepPanel() {
        this.defendStepList = new ArrayList<>();
        RequestContext.getCurrentInstance().execute("PF('DefendStepPanel').show()");
    }

    /**
     * @Description : 节点选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:04
     **/
    public void onDefendStepNodeSelect(NodeSelectEvent event){
        TreeNode selectNode = event.getTreeNode();
        if(null != selectNode) {
            TsSimpleCode t = (TsSimpleCode)selectNode.getData();
            this.defendStepList.add(t.getCodeName());
        }
    }

    /**
     * @Description :节点失去选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:04
     **/
    public void onDefendStepNodeNoSelect(NodeUnselectEvent event) {
        TreeNode selectNode = event.getTreeNode();
        if(null != selectNode) {
            TsSimpleCode t = (TsSimpleCode)selectNode.getData();
            this.defendStepList.remove(t.getCodeName());
        }
    }

    /**
     * @Description : 隐藏事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:07
     **/
    public void hideDefendStepAction() {
        StringBuffer buffer = new StringBuffer();
        this.employmentHisClt.setDefendStep(null);
        if (!CollectionUtils.isEmpty(defendStepList)) {
            for (String str : defendStepList) {
                buffer.append(",").append(str);
            }
            String defendStep = buffer.deleteCharAt(0).toString();
            if(defendStep.length() > 25) {
                this.employmentHisClt.setDefendStep(defendStep.substring(0, 25));
            } else {
                this.employmentHisClt.setDefendStep(defendStep);
            }
        }
        RequestContext context = RequestContext.getCurrentInstance();
        context.update("tabView:editTabView:exmsdataListForm:defendStepGrid");
    }

    public void openPrfraysrt2Panel() {
        this.prfraysrt2List = new ArrayList<>();
        RequestContext.getCurrentInstance().execute("PF('Prfraysrt2Panel').show()");
    }

    /**
     * @Description : 节点选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:04
     **/
    public void onPrfraysrt2NodeSelect(NodeSelectEvent event){
        TreeNode selectNode = event.getTreeNode();
        // 获得第一级的节点
        if (null != selectNode) {
            List<TreeNode> childs = selectNode.getChildren();
            // 得到子级节点 无子级节点（最后一级）刷新输入框，并关闭弹出框
            if (childs == null || childs.size() == 0) {
                TsSimpleCode t = (TsSimpleCode) selectNode.getData();
                this.prfraysrt2List.add(t.getCodeName());
            } else {
                for(TreeNode node : childs) {
                    TsSimpleCode t = (TsSimpleCode) node.getData();
                    this.prfraysrt2List.add(t.getCodeName());
                }
            }
        }
    }

    /**
     * @Description :节点失去选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:04
     **/
    public void onPrfraysrt2NodeNoSelect(NodeUnselectEvent event) {
        TreeNode selectNode = event.getTreeNode();
        // 获得第一级的节点
        if (null != selectNode) {
            List<TreeNode> childs = selectNode.getChildren();
            // 得到子级节点 无子级节点（最后一级）刷新输入框，并关闭弹出框
            if (childs == null || childs.size() == 0) {
                TsSimpleCode t = (TsSimpleCode) selectNode.getData();
                this.prfraysrt2List.remove(t.getCodeName());
            } else {
                for(TreeNode node : childs) {
                    TsSimpleCode t = (TsSimpleCode) node.getData();
                    this.prfraysrt2List.remove(t.getCodeName());
                }
            }
        }
    }

    /**
     * @Description : 隐藏事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:07
     **/
    public void hidePrfraysrt2Action() {
        StringBuffer buffer = new StringBuffer();
        this.radiationHisClt.setPrfraysrt2(null);
        if (!CollectionUtils.isEmpty(prfraysrt2List)) {
            for (String str : prfraysrt2List) {
                buffer.append(",").append(str);
            }
            String prfraysrt2 = buffer.deleteCharAt(0).toString();
            if(prfraysrt2.length() > 250) {
                this.radiationHisClt.setPrfraysrt2(prfraysrt2.substring(0, 250));
            } else {
                this.radiationHisClt.setPrfraysrt2(prfraysrt2);
            }

        }
        RequestContext context = RequestContext.getCurrentInstance();
        context.update("tabView:editTabView:exmsdataListForm:prfraysrt2Grid");
    }

    /**
     * @Description : 照射种类选择事件-单选可修改
     * @MethodAuthor: anjing
     * @Date : 2019/5/29 16:39
     **/
    public void onPrfraysrt2SortTreeNodeSelect(NodeSelectEvent event) {
        TreeNode selectNode = event.getTreeNode();
        /** 获得第一级的节点 */
        if (null != selectNode) {
            List<TreeNode> childs = selectNode.getChildren();
            /**
             * 得到子级节点 无子级节点（最后一级）刷新输入框，并关闭弹出框
             * */
            if (childs == null || childs.size() == 0) {
                TsSimpleCode t = (TsSimpleCode) selectNode.getData();
                this.radiationHisClt.setPrfraysrt2(t.getCodeName());
                RequestContext context = RequestContext.getCurrentInstance();
                context.execute("PF('Prfraysrt2Panel').hide()");
                context.update("tabView:editTabView:exmsdataListForm:prfraysrt2");
            }
        }
    }

    /**
     * @Description : 封装码表数据
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 16:00
     **/
    public void initSimpleCodeList(List<String> list, String typeNo) {
        List<TsSimpleCode> tsSimpleCodeList = commService.findSimpleCodesByTypeNo(typeNo);
        for(TsSimpleCode entity : tsSimpleCodeList) {
            list.add(entity.getCodeName());
        }
    }

    /**
     * @Description : 添加初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 14:28
     **/
    @Override
    public void addInit() {
        this.employmentHisClt = new TdTjEmhistoryClt();
        this.employmentHisClt.setFkByBhkId(this.fkByBhkId);
        this.employmentHisClt.setHisType(this.hisType);
        this.radiationHisClt = new TdTjEmhistoryClt();
        this.radiationHisClt.setFkByBhkId(this.fkByBhkId);
        this.radiationHisClt.setHisType(this.hisType);
        this.prfraysrtList = new ArrayList<>();
        this.fsszlList = new ArrayList<>();
        this.initPrfraysrtTree();
        this.initFsszlTree();
        this.disableDate = false;
        this.stastpDateMark = new String[]{"0"};
        RequestContext.getCurrentInstance().update(":tabView:editTabView:exmsdataListForm:employmentHisDialog");
    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {
        if(this.hisType == 1) {
            this.radiationHisClt = this.tdTjEmhistoryCltService.find(TdTjEmhistoryClt.class, this.rid);
        } else{
            this.employmentHisClt = this.tdTjEmhistoryCltService.find(TdTjEmhistoryClt.class, this.rid);
            if(ifZysTimeRange && "无".equals(this.employmentHisClt.getStastpDate())){
                this.disableDate = true;
                this.stastpDateMark = new String[]{"1"};
            }else{
                this.disableDate = false;
                this.stastpDateMark = new String[]{"0"};
            }
        }
    }

    /**
     * @Description : 保存前数据校验
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 15:39
     **/
    private boolean beforeSaveAction(TdTjEmhistoryClt tdTjEmhistoryClt,String type) {
        boolean flag = true;
        String id = "tabView\\\\:editTabView\\\\:exmsdataListForm\\\\:";
        if(StringUtils.isBlank(tdTjEmhistoryClt.getStastpDate())) {
            Global.markErrorInfo(true, id+("1".equals(type)?"stastpDate":"stastpDate2"), "起止时间不能为空！");
            flag = false;
        }
        if(StringUtils.isBlank(tdTjEmhistoryClt.getUnitName())) {
            Global.markErrorInfo(true, id+("1".equals(type)?"unitName":"unitName2"), "工作单位不能为空！");
            flag = false;
        }
        return flag;
    }

    /**
     * @Description : 职业史-执行保存
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 15:18
     **/
    public void saveEmploymentHistClt() {
        boolean flag = true;
        if(ifZysTimeRange){
            if(disableDate){
                //勾选了无
                this.employmentHisClt.setStastpDate("无");
                this.employmentHisClt.setStartDate(null);
                this.employmentHisClt.setStopDate(null);
            }else{
                //未勾选
                String id = "tabView\\\\:editTabView\\\\:exmsdataListForm\\\\:";
                if(employmentHisClt.getStartDate() == null) {
                    Global.markErrorInfo(true, id+"bDate_input", "起止时间开始日期不能为空！");
                    flag = false;
                }else if(DateUtils.isCompareDate(employmentHisClt.getStartDate(),">",this.fkByBhkId.getBhkDate())){
                    Global.markErrorInfo(true, id+"bDate_input", "起止时间开始日期须小于等于体检日期（"+DateUtils.formatDate(this.fkByBhkId.getBhkDate())+"）！");
                    flag = false;
                }else if(DateUtils.isCompareDate(employmentHisClt.getStartDate(),"<=",this.fkByBhkId.getBrth())){
                    Global.markErrorInfo(true, id+"bDate_input", "起止时间开始日期须大于出生日期（"+DateUtils.formatDate(this.fkByBhkId.getBrth())+"）！");
                    flag = false;
                }
                if(employmentHisClt.getStopDate() == null) {
                    Global.markErrorInfo(true, id+"eDate_input", "起止时间结束日期不能为空！");
                    flag = false;
                }
                if(flag){
                    this.employmentHisClt.setStastpDate(DateUtils.formatDate(employmentHisClt.getStartDate())+" ~ "+DateUtils.formatDate(employmentHisClt.getStopDate()));
                }else{
                    //防止出现“起止时间不能为空”的提示
                    this.employmentHisClt.setStastpDate("无");
                }
            }
        }
        //不用短路运算符是因为两个表达式都需要执行
        if( !flag | !this.beforeSaveAction(this.employmentHisClt,"1")) {
            return;
        }
        try {
            this.tdTjEmhistoryCltService.upsertEntity(this.employmentHisClt);
            RequestContext.getCurrentInstance().execute("PF('EmploymentHisDialog').hide()");
            this.initEmployHisList();
            JsfUtil.addSuccessMessage("保存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
     * @Description : 放射史-执行保存
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 15:18
     **/
    public void savRadiationHistClt() {
        if(!this.beforeSaveAction(this.radiationHisClt,"2")) {
            return;
        }
        try {
            this.radiationHisClt.setPrfraysrt(this.radiationHisClt.getFsszl());
            this.tdTjEmhistoryCltService.upsertEntity(this.radiationHisClt);
            RequestContext.getCurrentInstance().execute("PF('RadiationHisDialog').hide()");
            this.initRadiHisList();
            JsfUtil.addSuccessMessage("保存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    @Override
    public void saveAction() {
    }

    /**
     * @Description : 执行删除
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 10:33
     **/
    public void deleteAction() {
        try {
            if(this.hisType == 1) {
                this.tdTjEmhistoryCltService.deleteEntity(TdTjEmhistoryClt.class, this.radiationHisClt.getRid());
                this.initRadiHisList();
            }
            if(this.hisType == 2) {
                this.tdTjEmhistoryCltService.deleteEntity(TdTjEmhistoryClt.class, this.employmentHisClt.getRid());
                this.initEmployHisList();
            }
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("已被引用,无法删除！");
        }
    }

    /**
     * @Description : 放射线种类-树初始化
     * @MethodAuthor: anjing
     * @Date : 2020/04/23 15:07
     **/
    public void initFsszlTree() {
        List<TsSimpleCode> list = this.commService.findSimpleCodesByTypeNo("5306");
        this.fsszlSortTree = new CheckboxTreeNode("root", null);
        // 调用创建树方法
        this.createtDefendStepTree(this.fsszlSortTree, list);
    }

    /**
    * @Description : 打开放射线种类悬浮框
    * @MethodAuthor: anjing
    * @Date : 2020/4/23 14:59
    **/
    public void openFsszlPanel() {
        this.fsszlList = new ArrayList<>();
        RequestContext.getCurrentInstance().execute("PF('FsszlPanel').show()");
    }

    /**
     * @Description : 节点选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:04
     **/
    public void onFsszlNodeSelect(NodeSelectEvent event){
        TreeNode selectNode = event.getTreeNode();
        // 获得第一级的节点
        if (null != selectNode) {
            List<TreeNode> childs = selectNode.getChildren();
            // 得到子级节点 无子级节点（最后一级）刷新输入框，并关闭弹出框
            if (childs == null || childs.size() == 0) {
                TsSimpleCode t = (TsSimpleCode) selectNode.getData();
                this.fsszlList.add(t.getCodeName());
            } else {
                for(TreeNode node : childs) {
                    TsSimpleCode t = (TsSimpleCode) node.getData();
                    this.fsszlList.add(t.getCodeName());
                }
            }
        }
    }

    /**
     * @Description :节点失去选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:04
     **/
    public void onFsszlNodeNoSelect(NodeUnselectEvent event) {
        TreeNode selectNode = event.getTreeNode();
        // 获得第一级的节点
        if (null != selectNode) {
            List<TreeNode> childs = selectNode.getChildren();
            // 得到子级节点 无子级节点（最后一级）刷新输入框，并关闭弹出框
            if (childs == null || childs.size() == 0) {
                TsSimpleCode t = (TsSimpleCode) selectNode.getData();
                this.fsszlList.remove(t.getCodeName());
            } else {
                for(TreeNode node : childs) {
                    TsSimpleCode t = (TsSimpleCode) node.getData();
                    this.fsszlList.remove(t.getCodeName());
                }
            }
        }
    }

    /**
     * @Description : 隐藏事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:07
     **/
    public void hideFszlAction() {
        StringBuffer buffer = new StringBuffer();
        this.radiationHisClt.setFsszl(null);
        if (!CollectionUtils.isEmpty(fsszlList)) {
            for (String str : fsszlList) {
                buffer.append(",").append(str);
            }
            String fsszl = buffer.deleteCharAt(0).toString();
            if(fsszl.length() > 100) {
                this.radiationHisClt.setFsszl(fsszl.substring(0, 100));
            } else {
                this.radiationHisClt.setFsszl(fsszl);
            }

        }
        RequestContext context = RequestContext.getCurrentInstance();
        context.update("tabView:editTabView:exmsdataListForm:fsszlGrid");
    }

    public void changeStastpDateMark(){
        if(stastpDateMark.length>0 && "1".equals(stastpDateMark[0])){
            this.disableDate = true;
            //勾选无
            if(StringUtils.isBlank(this.employmentHisClt.getUnitName())){
                this.employmentHisClt.setUnitName("无");
            }
        }else{
            this.disableDate = false;
        }
    }

    @Override
    public String[] buildHqls() {
        return new String[0];
    }

    public TdTjEmhistoryClt getEmploymentHisClt() {
        return employmentHisClt;
    }

    public void setEmploymentHisClt(TdTjEmhistoryClt employmentHisClt) {
        this.employmentHisClt = employmentHisClt;
    }

    public TdTjEmhistoryClt getRadiationHisClt() {
        return radiationHisClt;
    }

    public void setRadiationHisClt(TdTjEmhistoryClt radiationHisClt) {
        this.radiationHisClt = radiationHisClt;
    }

    public TdTjBhkClt getFkByBhkId() {
        return fkByBhkId;
    }

    public void setFkByBhkId(TdTjBhkClt fkByBhkId) {
        this.fkByBhkId = fkByBhkId;
    }

    public Integer getHisType() {
        return hisType;
    }

    public void setHisType(Integer hisType) {
        this.hisType = hisType;
    }

    public List<TdTjEmhistoryClt> getEmploymentHisList() {
        return employmentHisList;
    }

    public void setEmploymentHisList(List<TdTjEmhistoryClt> employmentHisList) {
        this.employmentHisList = employmentHisList;
    }

    public List<TdTjEmhistoryClt> getRadiationHisList() {
        return radiationHisList;
    }

    public void setRadiationHisList(List<TdTjEmhistoryClt> radiationHisList) {
        this.radiationHisList = radiationHisList;
    }

    public List<String> getPrfwrklodList() {
        return prfwrklodList;
    }

    public void setPrfwrklodList(List<String> prfwrklodList) {
        this.prfwrklodList = prfwrklodList;
    }

    public List<String> getPrfshnvluList() {
        return prfshnvluList;
    }

    public void setPrfshnvluList(List<String> prfshnvluList) {
        this.prfshnvluList = prfshnvluList;
    }

    public List<String> getFsszlList() {
        return fsszlList;
    }

    public void setFsszlList(List<String> fsszlList) {
        this.fsszlList = fsszlList;
    }

    public TreeNode getPrfraysrt2SortTree() {
        return prfraysrt2SortTree;
    }

    public void setPrfraysrt2SortTree(TreeNode prfraysrt2SortTree) {
        this.prfraysrt2SortTree = prfraysrt2SortTree;
    }

    public TreeNode getDefendStepSortTree() {
        return defendStepSortTree;
    }

    public void setDefendStepSortTree(TreeNode defendStepSortTree) {
        this.defendStepSortTree = defendStepSortTree;
    }

    public List<String> getDefendStepList() {
        return defendStepList;
    }

    public void setDefendStepList(List<String> defendStepList) {
        this.defendStepList = defendStepList;
    }

    public TreeNode getPrfraysrtSortTree() {
        return prfraysrtSortTree;
    }

    public void setPrfraysrtSortTree(TreeNode prfraysrtSortTree) {
        this.prfraysrtSortTree = prfraysrtSortTree;
    }

    public List<String> getPrfraysrtList() {
        return prfraysrtList;
    }

    public void setPrfraysrtList(List<String> prfraysrtList) {
        this.prfraysrtList = prfraysrtList;
    }

    public List<String> getPrfraysrt2List() {
        return prfraysrt2List;
    }

    public void setPrfraysrt2List(List<String> prfraysrt2List) {
        this.prfraysrt2List = prfraysrt2List;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TreeNode getFsszlSortTree() {
        return fsszlSortTree;
    }

    public void setFsszlSortTree(TreeNode fsszlSortTree) {
        this.fsszlSortTree = fsszlSortTree;
    }

    public String[] getStastpDateMark() {
        return stastpDateMark;
    }

    public void setStastpDateMark(String[] stastpDateMark) {
        this.stastpDateMark = stastpDateMark;
    }

    public Boolean getDisableDate() {
        return disableDate;
    }

    public void setDisableDate(Boolean disableDate) {
        this.disableDate = disableDate;
    }
}
