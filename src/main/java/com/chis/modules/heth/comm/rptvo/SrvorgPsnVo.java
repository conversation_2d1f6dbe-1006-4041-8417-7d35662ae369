package com.chis.modules.heth.comm.rptvo;

import java.util.Objects;

/**
 *  <p>方法描述：放射卫生技术服务信息报送卡填报
 *  放射资质信息表</p>
 * @MethodAuthor hsj 2022-08-19 15:47
 */
public class SrvorgPsnVo {
    private Integer rid;
    private String empName;
    private String sex;
    private String codeName;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getCodeName() {
        return codeName;
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SrvorgPsnVo that = (SrvorgPsnVo) o;
        return Objects.equals(rid, that.rid);
    }
}
