package com.chis.modules.heth.comm.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 *
 * <AUTHOR>
 * @createTime 2018-7-2
 */
@Entity
@Table(name = "TD_ZDZYB_ANALY_DETAIL")
@SequenceGenerator(name = "TdZdzybAnalyDetailAnaly", sequenceName = "TD_ZDZYB_ANALY_DETAIL_SEQ", allocationSize = 1)
public class TdZdzybAnalyDetailComm {

    private Integer rid;
    private Integer xh;
    private TsSimpleCode analyItem;
    private Integer geNum;
    private Integer gtNum;
    private Integer leNum;
    private Integer ltNum;
    private Date modifyDate;
    private Integer modifyManid;
    private Date createDate;
    private Integer createManid;
    private TdZdzybAnalyTypeComm tdZdzybAnalyType;

    /**临时变量判断区间**/
    private Integer bigValue;
    private Integer smallValue;
    private Integer firstValue;
    private Integer secondValue;
    private String unitName;
    private String unitCode;

    public TdZdzybAnalyDetailComm() {
    }

    public TdZdzybAnalyDetailComm(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZdzybAnalyDetailAnaly")
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Column(name = "XH")
    public Integer getXh() {
        return xh;
    }

    public void setXh(Integer xh) {
        this.xh = xh;
    }

    @ManyToOne
    @JoinColumn(name = "ANALY_ITEM_ID")
    public TsSimpleCode getAnalyItem() {
        return analyItem;
    }

    public void setAnalyItem(TsSimpleCode analyItem) {
        this.analyItem = analyItem;
    }

    @Column(name = "GE")
    public Integer getGeNum() {
        return geNum;
    }

    public void setGeNum(Integer geNum) {
        this.geNum = geNum;
    }

    @Column(name = "GT")
    public Integer getGtNum() {
        return gtNum;
    }

    public void setGtNum(Integer gtNum) {
        this.gtNum = gtNum;
    }

    @Column(name = "LE")
    public Integer getLeNum() {
        return leNum;
    }

    public void setLeNum(Integer leNum) {
        this.leNum = leNum;
    }

    @Column(name = "LT")
    public Integer getLtNum() {
        return ltNum;
    }

    public void setLtNum(Integer ltNum) {
        this.ltNum = ltNum;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZdzybAnalyTypeComm getTdZdzybAnalyType() {
        return tdZdzybAnalyType;
    }

    public void setTdZdzybAnalyType(TdZdzybAnalyTypeComm tdZdzybAnalyType) {
        this.tdZdzybAnalyType = tdZdzybAnalyType;
    }

    @Transient
    public Integer getBigValue() {
        return bigValue;
    }

    public void setBigValue(Integer bigValue) {
        this.bigValue = bigValue;
    }

    @Transient
    public Integer getSmallValue() {
        return smallValue;
    }

    public void setSmallValue(Integer smallValue) {
        this.smallValue = smallValue;
    }

    @Transient
    public Integer getFirstValue() {
        return firstValue;
    }

    public void setFirstValue(Integer firstValue) {
        this.firstValue = firstValue;
    }

    @Transient
    public Integer getSecondValue() {
        return secondValue;
    }

    public void setSecondValue(Integer secondValue) {
        this.secondValue = secondValue;
    }

    @Column(name = "UNIT_NAME")
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Column(name = "UNIT_CODE")
    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }
}
