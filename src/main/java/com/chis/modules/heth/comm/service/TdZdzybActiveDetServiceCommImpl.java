package com.chis.modules.heth.comm.service;

import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdZdzybAnalyDetailComm;
import com.chis.modules.heth.comm.entity.TdZdzybAnalyDetailSubComm;
import com.chis.modules.heth.comm.entity.TdZdzybAnalyTypeComm;
import com.chis.modules.heth.comm.entity.TdZdzybDetailSubRelComm;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;


/***
 *  <p>类描述：重点职业病统一业务层</p>
 *
 * @ClassAuthor maox,2018年7月3日,TdZdzybAnalyServiceImpl
 *
 */
@Service
@Transactional
public class TdZdzybActiveDetServiceCommImpl extends AbstractTemplate {


    /**
    * @description:  查询
    * <AUTHOR>
    */
    public List<Object[]> findAnalyDetail(Integer rid) {
        StringBuilder sql=new StringBuilder();
        sql.append(" with table1 as ( ");
        sql.append("         select T1.MAIN_ID as rid, ");
        sql.append("         T1.RID as ANALY_DETAIL_RID, ");
        sql.append("         T1.ANALY_ITEM_ID, ");
        sql.append("         T2.CODE_NAME,T1.XH,T2.NUM,T2.CODE_NO ");
        sql.append("         from TD_ZDZYB_ANALY_DETAIL T1 ");
        sql.append("         left join TS_SIMPLE_CODE T2 on T1.ANALY_ITEM_ID = T2.RID ");
        sql.append("         where T1.MAIN_ID = ").append(rid);
        sql.append(" )");
        sql.append(" select T.RID,T.ANALY_DETAIL_RID,T.ANALY_ITEM_ID,T.CODE_NAME as industryName,'' as smallIndustryName,'' as POST_NAME,'' as BADRSN_NAME,T.XH ");
        sql.append(" from table1 T ");
        sql.append(" order by T.XH,T.NUM,T.CODE_NO ");
        List<Object[]> resultList = this.findSqlResultList(sql.toString());
        if (CollectionUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        }
        Map<String, List<String>> resultMap2 = findAnalyDetailGenerateTable2(rid);
        Map<String, List<String>> resultMap3 = findAnalyDetailGenerateTable3(rid);
        Map<String, List<String>> resultMap4 = findAnalyDetailGenerateTable4(rid);
        for (Object[] objArr : resultList) {
            String mainRid = objArr[0] == null ? null : objArr[0].toString();
            String analyDetailRid = objArr[1] == null ? null : objArr[1].toString();
            String key = mainRid + "&" + analyDetailRid;
            if (resultMap2.containsKey(key)) {
                objArr[4] = StringUtils.list2string(resultMap2.get(key), "，");
            }
            if (resultMap3.containsKey(key)) {
                objArr[5] = StringUtils.list2string(resultMap3.get(key), "，");
            }
            if (resultMap4.containsKey(key)) {
                objArr[6] = StringUtils.list2string(resultMap4.get(key), "，");
            }
        }
        return resultList;
    }

    /**
    * @description: 批量保存 重点职业病统计维度明细 表
    * <AUTHOR>
    */
    public void saveBatchAnalyDetail(TdZdzybAnalyTypeComm analyType,List<TsSimpleCode> list) {
        if(analyType!=null && analyType.getRid()==null){
            analyType.setAnalyType(3);
            analyType.setBusType(4);
        }
        this.upsertEntity(analyType);
        if(!CollectionUtils.isEmpty(list) && analyType.getRid()!=null){
            for (TsSimpleCode simpleCode : list) {
                TdZdzybAnalyDetailComm analyDetail=new TdZdzybAnalyDetailComm();
                analyDetail.setTdZdzybAnalyType(analyType);
                analyDetail.setAnalyItem(simpleCode);
                this.preEntity(analyDetail);
                this.saveObj(analyDetail);
            }
        }
    }

    /**
    * @description: 删除 重点职业病统计维度明细 及其子表
    * <AUTHOR>
    */
    public void delAnalyDetail(Integer detailRid) {
        //删除 重点职业病统计维度明细子项关系 表
        StringBuilder delSql=new StringBuilder();
        delSql.append(" delete from TD_ZDZYB_DETAIL_SUB_REL T ");
        delSql.append(" where T.MAIN_ID in(select T1.RID ");
        delSql.append(" from TD_ZDZYB_ANALY_DETAIL_SUB T1 where T1.MAIN_ID= ").append(detailRid).append(")");
        this.executeSql(delSql.toString(),null);
        //删除 重点职业病统计维度明细子项 表
        delSql=new StringBuilder();
        delSql.append(" delete from TD_ZDZYB_ANALY_DETAIL_SUB where MAIN_ID= ").append(detailRid);
        this.executeSql(delSql.toString(),null);
        //删除 重点职业病统计维度明细 表
        delSql=new StringBuilder();
        delSql.append(" delete from TD_ZDZYB_ANALY_DETAIL where rid= ").append(detailRid);
        this.executeSql(delSql.toString(),null);
    }

    /**
    * @description: 保存
    * <AUTHOR>
    */
    public void upsertAnalyDetail(TdZdzybAnalyTypeComm analyType, List<Object[]> showAllItemsList) {
        //保存主表
        if(analyType!=null && analyType.getRid()==null){
            analyType.setAnalyType(3);
            analyType.setBusType(4);
        }
        this.upsertEntity(analyType);
        //更新重点行业的序号
        if(!CollectionUtils.isEmpty(showAllItemsList)){
            for (Object[] objects : showAllItemsList) {
                StringBuilder sql=new StringBuilder();
                sql.append(" update TD_ZDZYB_ANALY_DETAIL set XH=").append(ObjectUtil.isEmpty(objects[7])?null:Integer.parseInt(objects[7].toString())).append(" where rid=").append(objects[1]);
                this.executeSql(sql.toString(),null);
            }
        }
    }

    /**
    * @description: 批量保存 重点职业病统计维度明细子项 表
    * <AUTHOR>
    */
    public void saveAnalyDetailSub(List<TdZdzybAnalyDetailSubComm> subCommList) {
        this.saveBatchObjs(subCommList);
    }

    /**
    * @description: 通过 明细表rid 查询子项表的记录
    * <AUTHOR>
    */
    public List<Object[]> findIndustryByDetailRid(Integer rid) {
       StringBuilder sql=new StringBuilder();
        sql.append(" with table1 as (select T.rid, ");
        sql.append("    T1.CODE_NAME as SUB_NAME ");
        sql.append("    ,T1.NUM,T1.CODE_LEVEL_NO,T1.CODE_NO ");
        sql.append("    from TD_ZDZYB_ANALY_DETAIL_SUB T ");
        sql.append("    left join TS_SIMPLE_CODE T1 on T.ANALY_ITEM_ID = T1.RID ");
        sql.append("    where T.MAIN_ID = ").append(rid).append(")");
        sql.append(" select T.RID, T.SUB_NAME, '' as POST_NAME, '' as BADRSN_NAME,'','',T3.RID as sub_rid ");
        sql.append(" from table1 T ");
        sql.append(" left join TD_ZDZYB_ANALY_DETAIL_SUB T2 on T.RID=T2.RID ");
        sql.append(" left join TS_SIMPLE_CODE T3 on T2.ANALY_ITEM_ID = T3.RID ");
        sql.append(" order by T.NUM,T.CODE_LEVEL_NO,T.CODE_NO,T.rid desc ");
        List<Object[]> resultList = this.findSqlResultList(sql.toString());
        if (CollectionUtils.isEmpty(resultList)) {
            return resultList;
        }
        Map<String,String[]> table2Map = this.findIndustryByDetailRidGenerateTable2(rid);
        Map<String,String[]> table3Map = this.findIndustryByDetailRidGenerateTable3(rid);
        for (Object[] objArr : resultList) {
            String id = null == objArr[0] ? "" : objArr[0].toString();
            if (StringUtils.isBlank(id)) {
                continue;
            }
            if (table2Map.containsKey(id)) {
                String[] innerArr = table2Map.get(id);
                objArr[4] = innerArr[0];
                objArr[2] = innerArr[1];
            }
            if (table3Map.containsKey(id)) {
                String[] innerArr = table3Map.get(id);
                objArr[5] = innerArr[0];
                objArr[3] = innerArr[1];
            }
        }
        return resultList;
    }

    /**
    * @description: 批量保存 子项关系表
    * <AUTHOR>
    */
    public void saveBatchSubRel(List<TdZdzybDetailSubRelComm> subRelComms,Integer subRid,Integer type) {
        //删除 重点职业病统计维度明细子项关系 表
        delSubRelByRid(subRid,type);
        //保存子项关系表
        this.saveBatchObjs(subRelComms);
    }

    /**
    * @description:  删除 子项及其子表
    * <AUTHOR>
    */
    public void delSubRel(Integer subRid) {
        //删除 重点职业病统计维度明细子项关系 表
        delSubRelByRid(subRid,null);
        //删除 重点职业病统计维度明细子项 表
        StringBuilder delSql=new StringBuilder();
        delSql.append(" delete from TD_ZDZYB_ANALY_DETAIL_SUB where RID= ").append(subRid);
        this.executeSql(delSql.toString(),null);
    }

    private void delSubRelByRid(Integer subRid,Integer type) {
        //删除 重点职业病统计维度明细子项关系 表
        StringBuilder delSql=new StringBuilder();
        delSql.append(" delete from TD_ZDZYB_DETAIL_SUB_REL T ");
        delSql.append(" where T.MAIN_ID in(select T1.RID ");
        delSql.append(" from TD_ZDZYB_ANALY_DETAIL_SUB T1 where T1.RID= ").append(subRid).append(")");
        if(type!=null){
            delSql.append(" and T.TYPE=").append(type);
        }
        this.executeSql(delSql.toString(),null);
    }

    /**
     * <p>方法描述：处理listagg超长导致的异常 </p>
     * pw 2025/7/2
     **/
    private Map<String, List<String>> findAnalyDetailGenerateTable2(Integer rid) {
        StringBuilder sql=new StringBuilder();
        sql.append(" with table1 as ( ");
        sql.append("         select T1.MAIN_ID as rid, ");
        sql.append("         T1.RID as ANALY_DETAIL_RID, ");
        sql.append("         T1.ANALY_ITEM_ID, ");
        sql.append("         T2.CODE_NAME,T1.XH,T2.NUM,T2.CODE_NO ");
        sql.append("         from TD_ZDZYB_ANALY_DETAIL T1 ");
        sql.append("         left join TS_SIMPLE_CODE T2 on T1.ANALY_ITEM_ID = T2.RID ");
        sql.append("         where T1.MAIN_ID = ").append(rid);
        sql.append(" ) ");
        sql.append("  select T.RID,T.ANALY_DETAIL_RID,T2.CODE_NAME ");
        sql.append("  from table1 T ");
        sql.append("  left join TD_ZDZYB_ANALY_DETAIL_SUB T1 on T.ANALY_DETAIL_RID=T1.MAIN_ID ");
        sql.append("  left join TS_SIMPLE_CODE T2 on T1.ANALY_ITEM_ID = T2.RID ");
        sql.append("  order by T2.NUM,T2.CODE_LEVEL_NO,T2.CODE_NO ");
        //，
        return this.findAnalyDetailGenerateTableHelper(this.findSqlResultList(sql.toString()));
    }

    /**
     * <p>方法描述：辅助组合数据 </p>
     * pw 2025/7/2
     **/
    private Map<String, List<String>> findAnalyDetailGenerateTableHelper(List<Object[]> resultList) {
        Map<String, List<String>> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(resultList)) {
            return resultMap;
        }
        for (Object[] objArr : resultList) {
            String mainRid = objArr[0] == null ? null : objArr[0].toString();
            String analyDetailRid = objArr[1] == null ? null : objArr[1].toString();
            String codeName = objArr[2] == null ? null : objArr[2].toString();
            if (StringUtils.isBlank(mainRid) || StringUtils.isBlank(analyDetailRid) || StringUtils.isBlank(codeName)) {
                continue;
            }
            String key = mainRid + "&" + analyDetailRid;
            List<String> list = resultMap.get(key);
            if (list == null) {
                list = new ArrayList<>();
                resultMap.put(key, list);
            }
            if (list.contains(codeName)) {
                continue;
            }
            list.add(codeName);
        }
        return resultMap;
    }

    /**
     * <p>方法描述：处理listagg超长导致的异常 </p>
     * pw 2025/7/2
     **/
    private Map<String, List<String>> findAnalyDetailGenerateTable3(Integer rid) {
        StringBuilder sql=new StringBuilder();
        sql.append(" with table1 as ( ");
        sql.append("         select T1.MAIN_ID as rid, ");
        sql.append("         T1.RID as ANALY_DETAIL_RID, ");
        sql.append("         T1.ANALY_ITEM_ID, ");
        sql.append("         T2.CODE_NAME,T1.XH,T2.NUM,T2.CODE_NO ");
        sql.append("         from TD_ZDZYB_ANALY_DETAIL T1 ");
        sql.append("         left join TS_SIMPLE_CODE T2 on T1.ANALY_ITEM_ID = T2.RID ");
        sql.append("         where T1.MAIN_ID = ").append(rid);
        sql.append(" ) ");
        sql.append("         select T.RID, ");
        sql.append("         T.ANALY_DETAIL_RID, ");
        sql.append("         T3.CODE_NAME as POST_NAME ");
        sql.append("         from table1 T ");
        sql.append("         left join TD_ZDZYB_ANALY_DETAIL_SUB T1 on T.ANALY_DETAIL_RID=T1.MAIN_ID ");
        sql.append("         left join TD_ZDZYB_DETAIL_SUB_REL T2 on T2.MAIN_ID = T1.RID and T2.TYPE = 1 ");
        sql.append("         left join TS_SIMPLE_CODE T3 on T2.ANALY_ITEM_ID = T3.RID ");
        sql.append("         order by T3.NUM,T3.CODE_LEVEL_NO,T3.CODE_NO ");
        return this.findAnalyDetailGenerateTableHelper(this.findSqlResultList(sql.toString()));
    }

    /**
     * <p>方法描述：处理listagg超长导致的异常 </p>
     * pw 2025/7/2
     **/
    private Map<String, List<String>> findAnalyDetailGenerateTable4(Integer rid) {
        StringBuilder sql=new StringBuilder();
        sql.append(" with table1 as ( ");
        sql.append("         select T1.MAIN_ID as rid, ");
        sql.append("         T1.RID as ANALY_DETAIL_RID, ");
        sql.append("         T1.ANALY_ITEM_ID, ");
        sql.append("         T2.CODE_NAME,T1.XH,T2.NUM,T2.CODE_NO ");
        sql.append("         from TD_ZDZYB_ANALY_DETAIL T1 ");
        sql.append("         left join TS_SIMPLE_CODE T2 on T1.ANALY_ITEM_ID = T2.RID ");
        sql.append("         where T1.MAIN_ID = ").append(rid);
        sql.append(" ) ");
        sql.append("         select T.RID, ");
        sql.append("         T.ANALY_DETAIL_RID, ");
        sql.append("         T3.CODE_NAME ");
        sql.append("         from table1 T ");
        sql.append("         left join TD_ZDZYB_ANALY_DETAIL_SUB T1 on T.ANALY_DETAIL_RID=T1.MAIN_ID ");
        sql.append("         left join TD_ZDZYB_DETAIL_SUB_REL T2 on T2.MAIN_ID = T1.RID and T2.TYPE = 2 ");
        sql.append("         left join TS_SIMPLE_CODE T3 on T2.ANALY_ITEM_ID = T3.RID ");
        sql.append("         order by T3.NUM,T3.CODE_LEVEL_NO,T3.CODE_NO ");
        return this.findAnalyDetailGenerateTableHelper(this.findSqlResultList(sql.toString()));
    }

    /**
     * <p>方法描述：处理listagg超长导致的异常 </p>
     * pw 2025/7/3
     **/
    private Map<String,String[]> findIndustryByDetailRidGenerateTable2(Integer rid) {
        StringBuilder sql=new StringBuilder();
        sql.append(" with table1 as (select T.rid, ");
        sql.append("    T1.CODE_NAME as SUB_NAME ");
        sql.append("    ,T1.NUM,T1.CODE_LEVEL_NO,T1.CODE_NO ");
        sql.append("    from TD_ZDZYB_ANALY_DETAIL_SUB T ");
        sql.append("    left join TS_SIMPLE_CODE T1 on T.ANALY_ITEM_ID = T1.RID ");
        sql.append("    where T.MAIN_ID = ").append(rid).append(")");
        sql.append("  select T.RID, T3.CODE_NAME|| CASE WHEN T3.CODE_NO is not null then ('（'||T3.CODE_NO||'）') else '' end, T3.RID AS TMRID ");
        sql.append("    from table1 T ");
        sql.append("    left join TD_ZDZYB_DETAIL_SUB_REL T2 on T2.MAIN_ID = T.RID and T2.TYPE = 1 ");
        sql.append("    left join TS_SIMPLE_CODE T3 on T2.ANALY_ITEM_ID = T3.RID ");
        sql.append("    order by T3.NUM,T3.CODE_LEVEL_NO,T3.CODE_NO ");
        return this.findIndustryByDetailRidGenerateTableHelper(this.findSqlResultList(sql.toString()));
    }

    /**
     * <p>方法描述： 处理listagg超长导致的异常 </p>
     * pw 2025/7/3
     **/
    private Map<String,String[]> findIndustryByDetailRidGenerateTableHelper(List<Object[]> list) {
        Map<String,String[]> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(list)) {
            return resultMap;
        }
        Set<String> cacheSet = new HashSet<>();
        for (Object[] objArr : list) {
            String id = null == objArr[0] ? "" : objArr[0].toString();
            String codeRid = null == objArr[2] ? "" : objArr[2].toString();
            String names = null == objArr[1] ? "" : objArr[1].toString();
            if (StringUtils.isBlank(id) || StringUtils.isBlank(codeRid)) {
                continue;
            }
            String key = id + "_" + codeRid;
            if (cacheSet.contains(key)) {
                continue;
            }
            cacheSet.add(key);
            String[] arr = resultMap.get(id);
            if (null == arr) {
                arr = new String[]{"",""};
                resultMap.put(id, arr);
            }
            String buRid = arr[0];
            String buName = arr[1];
            if (StringUtils.isBlank(buRid)) {
                arr[0] = codeRid;
            } else {
                arr[0] = buRid + "," + codeRid;
            }
            if (StringUtils.isBlank(names)) {
                continue;
            }
            if (StringUtils.isBlank(buName)) {
                arr[1] = names;
            } else {
                arr[1] = buName + "，" + names;
            }
        }
        return resultMap;
    }

    /**
     * <p>方法描述：处理listagg超长导致的异常 </p>
     * pw 2025/7/3
     **/
    private Map<String,String[]> findIndustryByDetailRidGenerateTable3(Integer rid) {
        StringBuilder sql=new StringBuilder();
        sql.append(" with table1 as (select T.rid, ");
        sql.append("    T1.CODE_NAME as SUB_NAME ");
        sql.append("    ,T1.NUM,T1.CODE_LEVEL_NO,T1.CODE_NO ");
        sql.append("    from TD_ZDZYB_ANALY_DETAIL_SUB T ");
        sql.append("    left join TS_SIMPLE_CODE T1 on T.ANALY_ITEM_ID = T1.RID ");
        sql.append("    where T.MAIN_ID = ").append(rid).append(")");
        sql.append(" select T.RID, T5.CODE_NAME, T5.RID AS TMRID ");
        sql.append("    from table1 T ");
        sql.append("    left join TD_ZDZYB_DETAIL_SUB_REL T4 on T4.MAIN_ID = T.RID and T4.TYPE = 2 ");
        sql.append("    left join TS_SIMPLE_CODE T5 on T4.ANALY_ITEM_ID = T5.RID ");
        sql.append("    order by T5.NUM,T5.CODE_LEVEL_NO,T5.CODE_NO ");
        return this.findIndustryByDetailRidGenerateTableHelper(this.findSqlResultList(sql.toString()));
    }
}
