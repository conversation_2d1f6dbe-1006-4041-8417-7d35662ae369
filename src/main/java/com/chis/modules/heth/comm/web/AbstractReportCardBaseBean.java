package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdZwBgkLastSta;
import com.chis.modules.heth.comm.entity.TdZwOccdisCardNew;
import com.chis.modules.heth.comm.service.TdZwBgkFlowServiceImpl;
import com.chis.modules.heth.comm.service.ZwReportCardCommServiceImpl;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.utils.HolidayUtil;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.model.SelectItem;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *  <p>类描述：报告卡审核基类</p>
 * @ClassAuthor hsj 2022-05-18 10:03
 */
public class AbstractReportCardBaseBean extends FacesEditBean implements IProcessData {

    protected final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    /**职业卫生报告卡统一业务层*/
    protected ZwReportCardCommServiceImpl cardServiceImpl = SpringContextHolder.getBean(ZwReportCardCommServiceImpl.class);
    protected TdZwBgkFlowServiceImpl flowService = SpringContextHolder.getBean(TdZwBgkFlowServiceImpl.class);

    /**查询条件：地区集合*/
    protected List<TsZone> zoneList = new ArrayList<>();
    /**查询条件：地区编码*/
    protected String searchZoneCode;
    /**查询条件：地区名称*/
    protected String searchZoneName;
    /**
     * 状态 1 待审核; 2 已退回; 3 审核通过(默认待审核)
     */
    protected String[] states;
    protected List<SelectItem> stateList = new ArrayList<>();
    /**页面审核级别 0:初审; 1:复审; 2:终审*/
    protected Integer level = -1;
    /**配置文件审核级别*/
    protected String checkLevel;
    /**审核期限*/
    protected String limitTime;
    /**是否为审核页面，控制审核意见区域显示*/
    protected boolean ifAudit;
    /**审核结果*/
    protected String checkResult;
    /**历次审核意见*/
    protected List<Object[]> historyList;
    /**报告卡最新状态*/
    protected TdZwBgkLastSta newFlow;
    /**默认审核意见*/
    protected String defaultAuditAdv;
    /**报告卡id*/
    protected Integer disCardId;
    /**报告卡类型*/
    protected Integer cardType;
    /**审核判断市直属地区*/
    protected TsZone zone;
    /**市级平台/省级平台*/
    protected String platVersion;
    /**地区级别*/
    protected Integer zoneType;

    /**所有状态*/
    protected String[] allStates;


    public AbstractReportCardBaseBean() {
        //当前登录账号单位的地区级别(real_zone_type)-> 审核级别(4->初审; 3->复审;2->终审)
        checkLevel= PropertyUtils.getValueWithoutException("checkLevel");
        /**审核默认审核意见*/
        defaultAuditAdv = PropertyUtils.getValueWithoutException("defaultAuditAdv");
        limitTime = PropertyUtils.getValueWithoutException("limitTime");
        platVersion=PropertyUtils.getValueWithoutException("platVersion");
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        Short realZoneType = tsZone.getRealZoneType();
        if(null==tsZone.getRealZoneType()){
            this.zoneType = tsZone.getZoneType().intValue();
        }else {
            this.zoneType = realZoneType.intValue();
        }
        //地区
        if (null == this.zoneList || this.zoneList.size() <= 0) {
            this.zoneList = this.commService.findZoneList(false, tsZone.getZoneGb(), (realZoneType) + "", "");
            this.searchZoneCode = this.zoneList.get(0).getZoneCode();
            this.searchZoneName = this.zoneList.get(0).getZoneName();
        }
        if("2".equals(checkLevel)){//2级审核
            if (zoneType <= 3) {//终审
                level = 2;
            }else {//初审
                level = 0;
            }
        }else if ("3".equals(checkLevel)) {//3级审核
            if (zoneType == 2) {//终审
                level = 2;
            }else if (zoneType == 3){//复审
                level = 1;
            }else {//初审
                level = 0;
            }
        }

        //状态,默认待审核
        if("3".equals(checkLevel)){
            stateList.add(new SelectItem("1","区县级待审") );
            stateList.add(new SelectItem("2","区县级退回") );
            stateList.add(new SelectItem("3","市级待审") );
            stateList.add(new SelectItem("4","市级退回") );
            stateList.add(new SelectItem("5","省级待审") );
            stateList.add(new SelectItem("6","省级退回") );
            stateList.add(new SelectItem("7","省级通过") );
            if(this.zoneType==4){
                states=new String[]{"1"};
            }else if(this.zoneType==3){
                states=new String[]{"3"};
            }else if(this.zoneType==2){
                states=new String[]{"5"};
            }
            allStates=new String[]{"1","2","3","4","5","6","7"};
        }else if("2".equals(checkLevel)){
            if("1".equals(platVersion)){
                stateList.add(new SelectItem("1","区县级待审") );
                stateList.add(new SelectItem("2","区县级退回") );
                stateList.add(new SelectItem("5","市级待审") );
                stateList.add(new SelectItem("6","市级退回") );
                stateList.add(new SelectItem("7","市级通过") );
                if(this.zoneType==4){
                    states=new String[]{"1"};
                }else if(this.zoneType==3 || this.zoneType==2){
                    states=new String[]{"5"};
                }
                allStates=new String[]{"1","2","5","6","7"};
            }else if("2".equals(platVersion)){
                stateList.add(new SelectItem("1","区县级待审") );
                stateList.add(new SelectItem("2","区县级退回") );
                stateList.add(new SelectItem("5","省级待审") );
                stateList.add(new SelectItem("6","省级退回") );
                stateList.add(new SelectItem("7","省级通过") );
                if(this.zoneType==4){
                    states=new String[]{"1"};
                }else if(this.zoneType==2 || this.zoneType==3){
                    states=new String[]{"5"};
                }
                allStates=new String[]{"1","2","5","6","7"};
            }
        }
    }




    @Override
    public String[] buildHqls() {
        return null;
    }

    @Override
    public void processData(List<?> list) {
    }

    @Override
    public void addInit() {

    }
    /**
     * 计算是否超期，并返回剩余天数
     *
     * @param sDate 日期
     * @return 剩余天数
     */
    protected int calLimitTime(Date sDate) {
        if (null == sDate) {
            return -1;
        }
        //剩余天数
        int day = HolidayUtil.calRemainingDate(sDate, new Date(), limitTime);
        if (day == 0) {
            return -1;
        }
        return day;
    }

    /**
     *  <p>方法描述：打开审核页面</p>
     * @MethodAuthor hsj 2022-05-17 11:29
     */
    @Override
    public void modInit() {
        //审核意见
        if(ifAudit){
            //初始化审核意见
            initFlow();
            //默认为 通过
            this.checkResult = "1";
            //审核意见初始化
            changeCheckState();
        }
        //获取历次审核
        historyList = flowService.findHisotryList(this.disCardId,this.cardType);
        //初始化历次审核意见
        initHistoryCheckout();
    }
    @Override
    public void viewInit() {
    }
    /**
     *  <p>方法描述：初始化审核意见</p>
     * @MethodAuthor hsj
     */
    private void initFlow() {
        newFlow = new TdZwBgkLastSta();
        this.newFlow = this.flowService.findTdZwBgkLastSta(this.disCardId,this.cardType);
        if(null == this.newFlow) {
            this.newFlow = new TdZwBgkLastSta();
        }
        //审核，审核意见、审核人为空
        if (2==level) {
            //终审
            this.newFlow.setProAuditAdv(null);
            this.newFlow.setProChkPsn(Global.getUser().getUsername());
        }else if (1==level) {
            //复审
            this.newFlow.setCityAuditAdv(null);
            this.newFlow.setCityChkPsn(Global.getUser().getUsername());
        }else {
            //初审
            this.newFlow.setCountAuditAdv(null);
            this.newFlow.setCountyChkPsn(Global.getUser().getUsername());
        }

        if(null == this.newFlow.getState()) {
            return;
        }
    }
    /**
     *  <p>方法描述：初始化历次审核意见</p>
     * @MethodAuthor hsj 2022-05-17 15:16
     */
    public void initHistoryCheckout(){
        if(!CollectionUtils.isEmpty(historyList)){
            List<Object[]> checkOutList = new ArrayList<>();
            for(Object[] objArr:historyList){
                Integer opegFlag = null == objArr[7] ? 0 : Integer.parseInt(objArr[7].toString()) ;
                String type = null;
                //审核级别为3
                if("3".equals(checkLevel)){
                    Integer ifCityDirect =  null == objArr[5] ? null : Integer.parseInt(objArr[5].toString()) ;
                    //根据是否市直属判断审核类型
                    if(Integer.valueOf(1).equals(ifCityDirect)){
                        switch (opegFlag){
                            case 41:
                                type = "市级审核通过";
                                break;
                            case 13:
                                type = "市级审核退回";
                                break;
                            case 42:
                                type = "省级审核通过";
                                break;
                            case 32:
                                type = "省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }else{
                        switch (opegFlag) {
                            case 31:
                                type = "区县级审核通过";
                                break;
                            case 11:
                                type = "区县级审核退回";
                                break;
                            case 41:
                                type = "市级审核通过";
                                break;
                            case 22:
                                type = "市级审核退回";
                                break;
                            case 42:
                                type = "省级审核通过";
                                break;
                            case 32:
                                type = "省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }
                }
                //审核级别为2
                if("2".equals(checkLevel)){
                    Integer ifProvDirect =  null == objArr[6] ? null : Integer.parseInt(objArr[6].toString()) ;
                    //根据是否省直属判断审核类型
                    if(Integer.valueOf(1).equals(ifProvDirect)){
                        switch (opegFlag){
                            case 42:
                                type = "1".equals(this.platVersion)?"市级审核通过":"省级审核通过";
                                break;
                            case 32:
                                type = "1".equals(this.platVersion)?"市级审核退回":"省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }else{
                        switch (opegFlag) {
                            case 43:
                                type = "区县级审核通过";
                                break;
                            case 11:
                                type = "区县级审核退回";
                                break;
                            case 42:
                                type = "1".equals(this.platVersion)?"市级审核通过":"省级审核通过";
                                break;
                            case 32:
                                type = "1".equals(this.platVersion)?"市级审核退回":"省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }
                }
                objArr[4] = type;
                if(type != null){
                    checkOutList.add(objArr);
                }
            }
            historyList = checkOutList;
        }
    }

    /**
     *  <p>方法描述：提交前验证</p>
     * @MethodAuthor hsj 2022-05-17 14:41
     */
    public void  beforeSubmit(){
        try{
            if(veryData()){
                return;
            }
            RequestContext context = RequestContext.getCurrentInstance();
            context.execute("PF('ConfirmDialog').show()");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }
   /**
    *  <p>方法描述：提交审核验证</p>
    * @MethodAuthor hsj 2022-05-17 14:41
    */
    private boolean veryData() {
        boolean flag = false;
        if(StringUtils.isBlank(checkResult)){
            JsfUtil.addErrorMessage("审核结果不能为空！");
            flag = true;
        }
        if(2 == level ){
            if(StringUtils.isBlank(newFlow.getProAuditAdv())){
                JsfUtil.addErrorMessage("审核意见不能为空！");
                flag = true;
            }
            if(StringUtils.isBlank(newFlow.getProChkPsn())){
                JsfUtil.addErrorMessage("审核人不能为空！");
                flag = true;
            }

        }
        if(1 == level  ){
            if( StringUtils.isBlank(newFlow.getCityAuditAdv())){
                JsfUtil.addErrorMessage("审核意见不能为空！");
                flag = true;
            }
            if(StringUtils.isBlank(newFlow.getCityChkPsn())){
                JsfUtil.addErrorMessage("审核人不能为空！");
                flag = true;
            }
        }
        if(0 == level ){
            if( StringUtils.isBlank(newFlow.getCountAuditAdv())){
                JsfUtil.addErrorMessage("审核意见不能为空！");
                flag = true;
            }
            if(StringUtils.isBlank(newFlow.getCountyChkPsn())){
                JsfUtil.addErrorMessage("审核人不能为空！");
                flag = true;
            }
        }
        return flag;
    }
    /**
     *  <p>方法描述：提交审核</p>
     * @MethodAuthor hsj 2022-05-17 15:23
     */
    @Override
    public void saveAction() {
        try {
            flowService.saveOrUpdateNewFlowOrBgkFlow(disCardId,cardType,zone,newFlow,checkResult,level,limitTime);
            RequestContext.getCurrentInstance().update("tabView");
            JsfUtil.addSuccessMessage("提交成功！");
            this.backAction();
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            //提交失败只更新审核区域 防止按钮显示有问题
            RequestContext.getCurrentInstance().update("tabView:editForm:check");
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
     *  <p>方法描述：审核状态修改</p>
     * @MethodAuthor hsj
     */
    public void changeCheckState(){
        if(null != defaultAuditAdv && "1".equals(checkResult)){
            //通过
            if(2 == level && StringUtils.isBlank(newFlow.getProAuditAdv())){
                newFlow.setProAuditAdv(defaultAuditAdv);
            }
            if(1 == level && StringUtils.isBlank(newFlow.getCityAuditAdv())){
                newFlow.setCityAuditAdv(defaultAuditAdv);
            }
            if(0 == level && StringUtils.isBlank(newFlow.getCountAuditAdv())){
                newFlow.setCountAuditAdv(defaultAuditAdv);
            }

        }else {
            if(2 == level && defaultAuditAdv.equals(newFlow.getProAuditAdv())){
                newFlow.setProAuditAdv("");
            }
            if(1 == level && defaultAuditAdv.equals(newFlow.getCityAuditAdv())){
                newFlow.setCityAuditAdv("");
            }
            if(0 == level  && defaultAuditAdv.equals(newFlow.getCountAuditAdv())){
                newFlow.setCountAuditAdv("");
            }
        }
    }

    public void reviewBatch(List<TdZwOccdisCardNew> odCardList, List<TdZwBgkLastSta> bgkLastStaList) {
        try {
            String auditAdv = StringUtils.objectToString(this.defaultAuditAdv);
            String username = StringUtils.objectToString(Global.getUser().getUsername());
            for (TdZwBgkLastSta bgkLastSta : bgkLastStaList) {
                if (2 == this.level) {
                    bgkLastSta.setProAuditAdv(auditAdv);
                    bgkLastSta.setProChkPsn(username);
                }
                if (1 == this.level) {
                    bgkLastSta.setCityAuditAdv(auditAdv);
                    bgkLastSta.setCityChkPsn(username);
                }
                if (0 == this.level) {
                    bgkLastSta.setCountAuditAdv(auditAdv);
                    bgkLastSta.setCountyChkPsn(username);
                }
            }
            this.flowService.odBatchReviewPass(odCardList, bgkLastStaList, this.level, this.limitTime);
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("批量审核失败！");
            return;
        }
        JsfUtil.addSuccessMessage("批量审核成功！");
        this.searchAction();
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(String checkLevel) {
        this.checkLevel = checkLevel;
    }

    public String getLimitTime() {
        return limitTime;
    }

    public void setLimitTime(String limitTime) {
        this.limitTime = limitTime;
    }

    public boolean isIfAudit() {
        return ifAudit;
    }

    public void setIfAudit(boolean ifAudit) {
        this.ifAudit = ifAudit;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult;
    }

    public List<Object[]> getHistoryList() {
        return historyList;
    }

    public void setHistoryList(List<Object[]> historyList) {
        this.historyList = historyList;
    }

    public TdZwBgkLastSta getNewFlow() {
        return newFlow;
    }

    public void setNewFlow(TdZwBgkLastSta newFlow) {
        this.newFlow = newFlow;
    }

    public String getDefaultAuditAdv() {
        return defaultAuditAdv;
    }

    public void setDefaultAuditAdv(String defaultAuditAdv) {
        this.defaultAuditAdv = defaultAuditAdv;
    }

    public Integer getDisCardId() {
        return disCardId;
    }

    public void setDisCardId(Integer disCardId) {
        this.disCardId = disCardId;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public TsZone getZone() {
        return zone;
    }

    public void setZone(TsZone zone) {
        this.zone = zone;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public String getPlatVersion() {
        return platVersion;
    }

    public void setPlatVersion(String platVersion) {
        this.platVersion = platVersion;
    }

    public Integer getZoneType() {
        return zoneType;
    }

    public void setZoneType(Integer zoneType) {
        this.zoneType = zoneType;
    }

    public String[] getAllStates() {
        return allStates;
    }

    public void setAllStates(String[] allStates) {
        this.allStates = allStates;
    }
}
