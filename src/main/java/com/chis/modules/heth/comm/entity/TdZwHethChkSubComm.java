package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2019-9-4
 */
@Entity
@Table(name = "TD_ZW_HETH_CHK_SUB")
@SequenceGenerator(name = "TdZwHethChkSub", sequenceName = "TD_ZW_HETH_CHK_SUB_SEQ", allocationSize = 1)
public class TdZwHethChkSubComm implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwHethChkSmaryComm fkByMainId;
	private TsSimpleCode fkByBadrsnId;
	private TsSimpleCode fkByOnguardStateid;
	private Integer touchNum;
	private Integer needChkNum;
	private Integer actCheNum;
	private Integer yszybNum;
	private Integer jjzNum;
	private Integer dlNum;
	private Date bhkDate;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	/**+接触有害因素类型***********/
	private TsSimpleCode fkByTchRsnId;


	public TdZwHethChkSubComm() {
	}

	public TdZwHethChkSubComm(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwHethChkSub")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwHethChkSmaryComm getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwHethChkSmaryComm fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BADRSN_ID")			
	public TsSimpleCode getFkByBadrsnId() {
		return fkByBadrsnId;
	}

	public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
		this.fkByBadrsnId = fkByBadrsnId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ONGUARD_STATEID")			
	public TsSimpleCode getFkByOnguardStateid() {
		return fkByOnguardStateid;
	}

	public void setFkByOnguardStateid(TsSimpleCode fkByOnguardStateid) {
		this.fkByOnguardStateid = fkByOnguardStateid;
	}	
			
	@Column(name = "TOUCH_NUM")	
	public Integer getTouchNum() {
		return touchNum;
	}

	public void setTouchNum(Integer touchNum) {
		this.touchNum = touchNum;
	}	
			
	@Column(name = "NEED_CHK_NUM")	
	public Integer getNeedChkNum() {
		return needChkNum;
	}

	public void setNeedChkNum(Integer needChkNum) {
		this.needChkNum = needChkNum;
	}	
			
	@Column(name = "ACT_CHE_NUM")	
	public Integer getActCheNum() {
		return actCheNum;
	}

	public void setActCheNum(Integer actCheNum) {
		this.actCheNum = actCheNum;
	}	
			
	@Column(name = "YSZYB_NUM")	
	public Integer getYszybNum() {
		return yszybNum;
	}

	public void setYszybNum(Integer yszybNum) {
		this.yszybNum = yszybNum;
	}	
			
	@Column(name = "JJZ_NUM")	
	public Integer getJjzNum() {
		return jjzNum;
	}

	public void setJjzNum(Integer jjzNum) {
		this.jjzNum = jjzNum;
	}	
			
	@Column(name = "DL_NUM")	
	public Integer getDlNum() {
		return dlNum;
	}

	public void setDlNum(Integer dlNum) {
		this.dlNum = dlNum;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "BHK_DATE")			
	public Date getBhkDate() {
		return bhkDate;
	}

	public void setBhkDate(Date bhkDate) {
		this.bhkDate = bhkDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	@ManyToOne
	@JoinColumn(name = "TCH_RSN_ID")
	public TsSimpleCode getFkByTchRsnId() {
		return fkByTchRsnId;
	}

	public void setFkByTchRsnId(TsSimpleCode fkByTchRsnId) {
		this.fkByTchRsnId = fkByTchRsnId;
	}

}