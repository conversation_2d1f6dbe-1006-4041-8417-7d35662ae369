package com.chis.modules.heth.comm.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.NodeUnselectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;
import org.springframework.util.CollectionUtils;

import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.heth.comm.entity.TdTjBhkClt;
import com.chis.modules.heth.comm.entity.TdTjExmsdataClt;
import com.chis.modules.heth.comm.service.TdTjExmsdataCltCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;

/**
 * @Description : 问诊项目（数据录入）
 * @ClassAuthor : anjing
 * @Date : 2019/5/18 18:48
 **/
public class TdTjExmsdataCltCommBean extends FacesEditBean {

    /**添加页面：问诊项目（数据录入）*/
    private TdTjExmsdataClt tdTjExmsdataClt = new TdTjExmsdataClt();

    /**关联：体检信息（录入数据）*/
    private TdTjBhkClt fkByBhkId;

    /**个人史树*/
    private TreeNode grsSortTree;
    /**个人史-下拉集合*/
    private List<String> grsList;

    /**家族史树*/
    private TreeNode jzsSortTree;
    /**家族史-下拉集合*/
    private List<String> jzsList;

    private boolean isxmns;

    private TdTjExmsdataCltCommServiceImpl tdTjExmsdataCltService = SpringContextHolder.getBean(TdTjExmsdataCltCommServiceImpl.class);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    /** 体检录入家族史是否可以填【无】*/
    private Boolean ifFstChkNeedJzs;

    /**目前吸烟情况*/
    private List<TsSimpleCode> smklList;
    private Map<Integer,TsSimpleCode> smklMap;
    /**目前吸烟情况选中值*/
    private Integer smkSelRid;

    /**吸烟史年，月，平均每天吸烟量是否可编辑*/
    private boolean isSmkEdit=true;

    public TdTjExmsdataCltCommBean() {
        ifFstChkNeedJzs = "1".equals(commService.findParamValue("IF_CHK_JZS"));
    }

    /**
     * @Description : 参数初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 18:57
     **/
    public void initParam() {
        this.grsList = new ArrayList<>();
        this.jzsList = new ArrayList<>();
        if(null != this.fkByBhkId && null != this.fkByBhkId.getRid()) {
            // 初始化问诊项目
            List<TdTjExmsdataClt> tdTjExmsdataCltList = this.tdTjExmsdataCltService.selectExmsdataCltByBhkId(this.fkByBhkId.getRid());
            if(!CollectionUtils.isEmpty(tdTjExmsdataCltList)) {
                this.tdTjExmsdataClt = tdTjExmsdataCltList.get(0);
            }
            // 个人史树初始化
            this.initGrsTree();
            // 家族史树初始化
            this.initJzsTree();
            //目前吸烟情况  码表初始化
            this.initSmkList();
        }
    }

    /**
    * <p>Description：目前吸烟情况  码表初始化 </p>
    * <p>Author： yzz 2023-12-22 </p>
    */
    private void initSmkList(){
        this.smklList=new ArrayList<>();
        this.smklMap=new HashMap<>();
        this.smklList=this.commService.findLevelSimpleCodesByTypeId("5611");
        if(CollectionUtils.isEmpty(smklList)){
            return;
        }
        for (TsSimpleCode tsSimpleCode : smklList) {
            smklMap.put(tsSimpleCode.getRid(),tsSimpleCode);
        }
    }


    /**
     * @Description : 个人史-树初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 13:35
     **/
    public void initGrsTree() {
        List<TsSimpleCode> list = this.commService.findallSimpleCodesByTypeIdAndExtends1("5301", 2);
        // 创建新的个人史树
        this.grsSortTree = new DefaultTreeNode("root", null);
        // 调用创建树方法
        this.createBadRsnTree(this.grsSortTree, list);
    }

    /**
     * @Description : 家族史-树初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 15:40
     **/
    public void initJzsTree() {
        List<TsSimpleCode> list = this.commService.findallSimpleCodesByTypeIdAndExtends1("5301", 3);
        // 创建新的个人史树
        this.jzsSortTree = new DefaultTreeNode("root", null);
        // 调用创建树方法
        this.createBadRsnTree(this.jzsSortTree, list);
    }

    /**
     * @Description : 生成个人史树
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 13:35
     **/
    private void createBadRsnTree(TreeNode selectBadRsnTree, List<TsSimpleCode> list) {
        if (null != list && list.size() > 0) {
            // 只有第一层
            Set<String> firstLevelNoSet = new LinkedHashSet<String>();
            // 没有第一层
            Set<String> levelNoSet = new LinkedHashSet<String>();
            // 所有类别
            Map<String, TsSimpleCode> menuMap = new HashMap<String, TsSimpleCode>();

            for (TsSimpleCode t : list) {
                menuMap.put(t.getCodeName(), t);
                firstLevelNoSet.add(t.getCodeName());
            }
            for (String ln : firstLevelNoSet) {
                TreeNode node = new DefaultTreeNode(menuMap.get(ln), selectBadRsnTree);
            }
        }
    }

    public void openGrsPanel() {
        this.grsList = new ArrayList<>();
        RequestContext.getCurrentInstance().execute("PF('GrsPanel').show()");
    }

    /**
     * @Description : 节点选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:04
     **/
    public void onGrsNodeSelect(NodeSelectEvent event){
        TreeNode selectNode = event.getTreeNode();
        if(null != selectNode) {
            TsSimpleCode t = (TsSimpleCode)selectNode.getData();
            this.grsList.add(t.getCodeName());
        }
    }

    /**
     * @Description :节点失去选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:04
     **/
    public void onGrsNodeNoSelect(NodeUnselectEvent event) {
        TreeNode selectNode = event.getTreeNode();
        if(null != selectNode) {
            TsSimpleCode t = (TsSimpleCode)selectNode.getData();
            this.grsList.remove(t.getCodeName());
        }
    }

    /**
     * @Description : 隐藏事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:07
     **/
    public void hideGrsAction() {
        StringBuffer buffer = new StringBuffer();
        this.tdTjExmsdataClt.setGrs(null);
        if (!CollectionUtils.isEmpty(grsList)) {
            for (String str : grsList) {
                buffer.append(",").append(str);
            }
            String grs = buffer.deleteCharAt(0).toString();
            if(grs.length() > 127) {
                this.tdTjExmsdataClt.setGrs(grs.substring(0, 127));
            } else {
                this.tdTjExmsdataClt.setGrs(grs);
            }
        }
        RequestContext context = RequestContext.getCurrentInstance();
        context.update("tabView:editTabView:exmsdataListForm:grsGrid");
    }

    public void openJzsPanel() {
        this.jzsList = new ArrayList<>();
        RequestContext.getCurrentInstance().execute("PF('JzsPanel').show()");
    }

    /**
     * @Description : 节点选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:04
     **/
    public void onJzsNodeSelect(NodeSelectEvent event){
        TreeNode selectNode = event.getTreeNode();
        if(null != selectNode) {
            TsSimpleCode t = (TsSimpleCode)selectNode.getData();
            this.jzsList.add(t.getCodeName());
        }
    }

    /**
     * @Description :节点失去选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:04
     **/
    public void onJzsNodeNoSelect(NodeUnselectEvent event) {
        TreeNode selectNode = event.getTreeNode();
        if(null != selectNode) {
            TsSimpleCode t = (TsSimpleCode)selectNode.getData();
            this.jzsList.remove(t.getCodeName());
        }
    }

    /**
     * @Description : 隐藏事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 14:07
     **/
    public void hideJzsAction() {
        StringBuffer buffer = new StringBuffer();
        this.tdTjExmsdataClt.setJzs(null);
        if (!CollectionUtils.isEmpty(jzsList)) {
            for (String str : jzsList) {
                buffer.append(",").append(str);
            }
            String jzs = buffer.deleteCharAt(0).toString();
            if(jzs.length() > 127) {
                this.tdTjExmsdataClt.setJzs(jzs.substring(0, 127));
            } else {
                this.tdTjExmsdataClt.setJzs(jzs);
            }
        }
        RequestContext context = RequestContext.getCurrentInstance();
        context.update("tabView:editTabView:exmsdataListForm:jzsGrid");
    }


    /**
     * <p>Description：目前吸烟情况 change 事件 </p>
     * <p>Author： yzz 2023-12-22 </p>
     */
    public void changeSmk(){
        this.isSmkEdit=true;
        if(this.smkSelRid==null){
            return;
        }
        //如果选中从不抽烟
        if(this.smklMap.containsKey(smkSelRid) && "1".equals(this.smklMap.get(smkSelRid).getExtendS1())){
            this.isSmkEdit=false;
            this.tdTjExmsdataClt.setSmkyerqty("0");
            this.tdTjExmsdataClt.setSmkmthqty(0);
            this.tdTjExmsdataClt.setSmkdayble("0");
        }
    }

    /**
     * @Description : 添加初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 18:19
     **/
    @Override
    public void addInit() {
        this.tdTjExmsdataClt = new TdTjExmsdataClt();
        this.tdTjExmsdataClt.setCreateManid(Global.getUser().getRid());
        this.tdTjExmsdataClt.setCreateDate(new Date());
        this.tdTjExmsdataClt.setFkByBhkId(this.fkByBhkId);
    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    @Override
    public String[] buildHqls() {
        return new String[0];
    }

    public TdTjExmsdataClt getTdTjExmsdataClt() {
        return tdTjExmsdataClt;
    }

    public void setTdTjExmsdataClt(TdTjExmsdataClt tdTjExmsdataClt) {
        this.tdTjExmsdataClt = tdTjExmsdataClt;
    }

    public TdTjBhkClt getFkByBhkId() {
        return fkByBhkId;
    }

    public void setFkByBhkId(TdTjBhkClt fkByBhkId) {
        this.fkByBhkId = fkByBhkId;
    }

    public TreeNode getGrsSortTree() {
        return grsSortTree;
    }

    public void setGrsSortTree(TreeNode grsSortTree) {
        this.grsSortTree = grsSortTree;
    }

    public TreeNode getJzsSortTree() {
        return jzsSortTree;
    }

    public void setJzsSortTree(TreeNode jzsSortTree) {
        this.jzsSortTree = jzsSortTree;
    }

    public List<String> getGrsList() {
        return grsList;
    }

    public void setGrsList(List<String> grsList) {
        this.grsList = grsList;
    }

    public List<String> getJzsList() {
        return jzsList;
    }

    public void setJzsList(List<String> jzsList) {
        this.jzsList = jzsList;
    }

    public boolean isIsxmns() {
        return isxmns;
    }

    public void setIsxmns(boolean isxmns) {
        this.isxmns = isxmns;
    }

    public Boolean getIfFstChkNeedJzs() {
        return ifFstChkNeedJzs;
    }

    public void setIfFstChkNeedJzs(Boolean ifFstChkNeedJzs) {
        this.ifFstChkNeedJzs = ifFstChkNeedJzs;
    }

    public List<TsSimpleCode> getSmklList() {
        return smklList;
    }

    public void setSmklList(List<TsSimpleCode> smklList) {
        this.smklList = smklList;
    }

    public Integer getSmkSelRid() {
        return smkSelRid;
    }

    public void setSmkSelRid(Integer smkSelRid) {
        this.smkSelRid = smkSelRid;
    }

    public Map<Integer, TsSimpleCode> getSmklMap() {
        return smklMap;
    }

    public void setSmklMap(Map<Integer, TsSimpleCode> smklMap) {
        this.smklMap = smklMap;
    }

    public boolean getIsSmkEdit() {
        return isSmkEdit;
    }

    public void setIsSmkEdit(boolean isSmkEdit) {
        this.isSmkEdit = isSmkEdit;
    }


}
