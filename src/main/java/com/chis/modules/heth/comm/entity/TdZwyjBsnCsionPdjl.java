package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-10-29
 */
@Entity
@Table(name = "TD_ZWYJ_BSN_CSION_PDJL")
@SequenceGenerator(name = "TdZwyjBsnCsionPdjl", sequenceName = "TD_ZWYJ_BSN_CSION_PDJL_SEQ", allocationSize = 1)
public class TdZwyjBsnCsionPdjl implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwyjBsnCsionItemSub fkByMainId;
	private TdZdzybAnalyItmTypeComm fkByPdClusionId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjBsnCsionPdjl() {
	}

	public TdZwyjBsnCsionPdjl(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjBsnCsionPdjl")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwyjBsnCsionItemSub getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwyjBsnCsionItemSub fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "PD_CLUSION_ID")			
	public TdZdzybAnalyItmTypeComm getFkByPdClusionId() {
		return fkByPdClusionId;
	}

	public void setFkByPdClusionId(TdZdzybAnalyItmTypeComm fkByPdClusionId) {
		this.fkByPdClusionId = fkByPdClusionId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}