package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.logic.ZwPsnInfoDTO;
import com.chis.modules.system.interfaces.IProcessPackData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.PackLazyDataModel;
import com.chis.modules.system.web.FacesSimpleBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.ToggleSelectEvent;
import org.primefaces.event.UnselectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>描述 TD_ZW_PSNINFO 人员弹出框</p>
 *
 * @ClassAuthor gongzhe,2022/7/12 1:12,SelectCheckPsnListBean
 */
@ManagedBean(name = "selectCheckPsnCommListBean")
@ViewScoped
public class SelectCheckPsnCommListBean extends FacesSimpleBean implements IProcessPackData {

    private static final long serialVersionUID = -738445454536625063L;

    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	private PackLazyDataModel psnDataModel;
    /** 单位名称*/
    private String searchUnitName;
    /** 查询条件-姓名*/
    private String searchPsnName;
	/** 抽取列表当前页已勾选*/
	private List<ZwPsnInfoDTO> selectedList;
	/** 抽取列表全部已勾选的*/
	private List<ZwPsnInfoDTO> allSelectedList;
	/**考核类型*/
	private String checkType;
	private String TABLE_ID = "codeForm:dataTable";
	/**弹框标题名称*/
	private String titleName;
	/**考核单位rid*/
	private String unitRid;

    public SelectCheckPsnCommListBean() {
    	this.ifSQL = true;
		this.selectedList = new ArrayList<>();
		this.allSelectedList = new ArrayList<>();
		this.searchUnitName = null;
		this.searchPsnName = null;
		this.checkType = JsfUtil.getRequest().getParameter("checkType");
		this.unitRid = JsfUtil.getRequest().getParameter("unitRid");
		this.titleName = JsfUtil.getRequest().getParameter("title");
		RequestContext.getCurrentInstance().update("codeForm");
		//置为第一页
		DataTable dataTable = (DataTable) FacesContext
				.getCurrentInstance().getViewRoot()
				.findComponent(TABLE_ID);
		dataTable.setFirst(0);
		this.searchAction();
    }
	@Override
	public void searchAction() {
		this.paramMap.clear();
		String[] hql = this.buildHqls();
		this.psnDataModel = new PackLazyDataModel<ZwPsnInfoDTO>(hql[0], hql[1], this.paramMap,this, TABLE_ID, this.ifSQL);
		selectedList.clear();
		selectedList.addAll(allSelectedList);
	}

	/**
	 * <p>描述 处理数据</p>
	 *
	 * @param list
	 * @MethodAuthor gongzhe,2022.03.10 11:10,processPackData
	 */
	@Override
	public List<ZwPsnInfoDTO> processPackData(List list){
		List<Object[]> data = (List<Object[]>) list;
		List<Integer> rids = new ArrayList<>(data.size());
		for (Object[] obj: data){
			if(obj[0] != null){
				rids.add(((BigDecimal) obj[0]).intValue());
			}
		}
		return packZwPsnInfoDTO(data);
	}

	/**
	 * <p>描述 </p>
	 *
	 * @param list
	 * @MethodAuthor gongzhe,2022/7/13 10:39,packZwPsnInfoDTO
	 * @return java.util.List<packZwPsnInfoDTO>
	 */
	private List<ZwPsnInfoDTO> packZwPsnInfoDTO(List<Object[]> list){
		List<ZwPsnInfoDTO> result = new ArrayList<>();
		for(Object[] obj: list){
			ZwPsnInfoDTO dto = new ZwPsnInfoDTO();
			dto.setRid(obj[0] == null ? null : ((BigDecimal) obj[0]).intValue());
			dto.setEmpName((String)obj[1]);
			dto.setSex((String)obj[2]);
			dto.setTheTitle((String)obj[4]);
			dto.setOrgName((String)obj[6]);
			dto.setTitleId(obj[3]!=null?Integer.parseInt(obj[3].toString()):null);
			result.add(dto);
		}
		return result;
	}

    @Override
	public String[] buildHqls() {
    	StringBuffer sql = new StringBuffer();
		sql.append(" SELECT T.RID,T.EMP_NAME,T.SEX,T.TITLE_ID,T1.CODE_NAME,T.ORG_ID,T2.UNITNAME ");
		if(StringUtils.isNotBlank(checkType)){
			if("1".equals(checkType)){
				sql.append(" from TD_ZW_TJORGINFO T3 ");
				sql.append(" left join TD_ZW_TJORGPSNS T4 on T4.ORG_ID = T3.RID ");
			}else if("2".equals(checkType)){
				sql.append(" from TD_ZW_DIAGORGINFO T3 ");
				sql.append(" left join TD_ZW_DIAGPSNS T4 on T4.ORG_ID = T3.RID ");
			}else if("3".equals(checkType)){
				sql.append(" from TD_ZW_SRVORGINFO T3 ");
				sql.append(" left join TD_ZW_SRVORGPSNS T4 on T4.ORG_ID = T3.RID ");
			}else if("4".equals(checkType)){
				sql.append(" from TD_ZW_OCCHETH_INFO T3 ");
				sql.append(" left join TD_ZW_OCCHETH_PSNS T4 on T4.ORG_ID = T3.RID ");
			}
			sql.append(" inner join TD_ZW_PSNINFO T on T.RID=T4.EMP_ID ");
		}else{
			sql.append("from TD_ZW_PSNINFO T ");
		}
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T1 ON T1.RID = T.TITLE_ID");
		sql.append(" LEFT JOIN TS_UNIT T2 ON T2.RID = T.ORG_ID WHERE T.ORG_ID IS NOT NULL ");
		if(StringUtils.isNotBlank(checkType)&&StringUtils.isNotBlank(unitRid)){
			sql.append(" and T3.ORG_ID=").append(unitRid);
		}
		if(StringUtils.isNotBlank(checkType)){
			sql.append(" and T4.ON_DUTY=1 ");
		}
		if (StringUtils.isNotBlank(searchUnitName)) {
			sql.append(" AND T2.UNITNAME LIKE :searchUnitName escape '\\\'");
			this.paramMap.put("searchUnitName", "%"+StringUtils.convertBFH(this.searchUnitName.trim())+"%");
		}
		if (StringUtils.isNotBlank(searchPsnName)) {
			sql.append(" AND T.EMP_NAME LIKE :searchPsnName escape '\\\'");
			this.paramMap.put("searchPsnName", "%"+StringUtils.convertBFH(this.searchPsnName.trim())+"%");
		}
		sql.append(" ORDER BY T.EMP_NAME,T2.UNITNAME");
		String h2 = "SELECT COUNT(*) FROM (" + sql + ")";
        String h1 = "SELECT * FROM (" + sql + ")";
        return new String[]{h1,h2};
	}
    /**
 	 * <p>方法描述：提交</p>
 	 * @MethodAuthor qrr,2019年12月2日,submitAction
     * */
    public void submitAction() {
		if(allSelectedList.size()==0){
			JsfUtil.addErrorMessage("请选择人员！");
			return;
		}
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("selected", allSelectedList);
		RequestContext.getCurrentInstance().closeDialog(map);
	}
    /**
 	 * <p>方法描述：关闭</p>
 	 * @MethodAuthor qrr,2018年4月9日,dialogClose
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

	/**
	 * <p>描述 翻页监听</p>
	 *
	 * @MethodAuthor gongzhe,2022.03.04 16:56,pageListener
	 * @return void
	 */
	public void pageListener(){
		selectedList.clear();
		selectedList.addAll(allSelectedList);
		RequestContext.getCurrentInstance().update(TABLE_ID);
	}

	/**
	 * <p>描述 行选中监听，添加元素</p>
	 *
	 * @param event
	 * @MethodAuthor gongzhe,2022.03.04 13:55,rowSelectListener
	 * @return void
	 */
	public void rowSelectListener(SelectEvent event){
		ZwPsnInfoDTO dto = (ZwPsnInfoDTO) event.getObject();
		allSelectedList.add(dto);
	}
	/**
	 *
	 * <p>描述：行取消，移除元素</p>
	 *
	 * @param event
	 * @MethodAuthor gongzhe,2022.03.04 13:55,rowUnselectListener
	 * @return void
	 */
	public void rowUnselectListener(UnselectEvent event){
		ZwPsnInfoDTO dto = (ZwPsnInfoDTO) event.getObject();
		allSelectedList.remove(dto);
	}
	/**
	 *
	 * <p>描述：全选或取消全选</p>
	 *
	 * @param event
	 * @MethodAuthor gongzhe,2022.03.04 13:55,toggleSelectListener
	 * @return void
	 */
	public void toggleSelectListener(ToggleSelectEvent event){
		DataTable dataTable = (DataTable) FacesContext
				.getCurrentInstance().getViewRoot()
				.findComponent(TABLE_ID);
		List<ZwPsnInfoDTO> displayList = this.psnDataModel.getPackData();
		if(CollectionUtils.isEmpty(displayList)){
			//表格无数据
			return;
		}
		if(event.isSelected()){
			//当前datatable选中的项
			List list = (List)dataTable.getSelection();
			allSelectedList.addAll(list);
		}else{//取消全选，需要移除当前页的所有元素
			allSelectedList.removeAll(displayList);
		}
	}

	public String getSearchUnitName() {
		return searchUnitName;
	}

	public void setSearchUnitName(String searchUnitName) {
		this.searchUnitName = searchUnitName;
	}

	public String getSearchPsnName() {
		return searchPsnName;
	}

	public void setSearchPsnName(String searchPsnName) {
		this.searchPsnName = searchPsnName;
	}

	public List<ZwPsnInfoDTO> getSelectedList() {
		return selectedList;
	}

	public void setSelectedList(List<ZwPsnInfoDTO> selectedList) {
		this.selectedList = selectedList;
	}

	public List<ZwPsnInfoDTO> getAllSelectedList() {
		return allSelectedList;
	}

	public void setAllSelectedList(List<ZwPsnInfoDTO> allSelectedList) {
		this.allSelectedList = allSelectedList;
	}

	public PackLazyDataModel getPsnDataModel() {
		return psnDataModel;
	}

	public void setPsnDataModel(PackLazyDataModel psnDataModel) {
		this.psnDataModel = psnDataModel;
	}

	public String getCheckType() {
		return checkType;
	}

	public void setCheckType(String checkType) {
		this.checkType = checkType;
	}

	public String getTitleName() {
		return titleName;
	}

	public void setTitleName(String titleName) {
		this.titleName = titleName;
	}

	public String getUnitRid() {
		return unitRid;
	}

	public void setUnitRid(String unitRid) {
		this.unitRid = unitRid;
	}
}
