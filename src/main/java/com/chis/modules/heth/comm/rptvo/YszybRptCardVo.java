package com.chis.modules.heth.comm.rptvo;

import com.chis.modules.system.rptvo.ZwxFieldDoc;
/**
 * <p>类描述：疑似职业病报告卡</p>
 * @ClassAuthor rcj,2019年9月9日,SuspectedCardVo
 * */
public class YszybRptCardVo {
	//报告卡编号
	@ZwxFieldDoc(value = "报告卡编号")
	private String rptNo;
	//报告年份
	@ZwxFieldDoc(value = "报告年份")
	private String reportYear;
	//劳动者姓名
	@ZwxFieldDoc(value = "劳动者姓名")
	private String personalName;
	//证件类型
	@ZwxFieldDoc(value = "证件类型")
	private String cardType;
	//证件号码
	@ZwxFieldDoc(value = "证件号码")
	private String idcard;
	//联系电话
	@ZwxFieldDoc(value = "联系电话")
	private String personalTel;
	//出生日期
	@ZwxFieldDoc(value = "出生日期")
	private String birthday;
	//性别
	@ZwxFieldDoc(value = "性别")
	private String sex;
	//所属地区
	@ZwxFieldDoc(value = "所属地区")
	private String zoneName;
	//用人单位名称s
	@ZwxFieldDoc(value = "用人单位名称")
	private String crptName;
	//组织机构代码
	@ZwxFieldDoc(value = "统一社会信用代码")
	private String unitCode;
	//邮政编码
	@ZwxFieldDoc(value = "邮政编码")
	private String postCode;
	//通讯地址
	@ZwxFieldDoc(value = "通讯地址")
	private String linkAddr;
	//单位联系人
	@ZwxFieldDoc(value = "单位联系人")
	private String crptLinkMan;
	//单位联系电话
	@ZwxFieldDoc(value = "单位联系电话")
	private String crptLinkTel;
	//经济类型
	@ZwxFieldDoc(value = "经济类型")
	private String economy;
	//行业
	@ZwxFieldDoc(value = "行业")
	private String indusType;
	//企业规模
	@ZwxFieldDoc(value = "企业规模")
	private String crptSize;
	
	//疑似职业病名称
	@ZwxFieldDoc(value = "疑似职业病名称")
	private String suspectedName;
	//疑似职业病种类
	@ZwxFieldDoc(value = "疑似职业病种类")
	private String yszybTypeName;
	//职业性化学中毒分类
	@ZwxFieldDoc(value = "职业性化学中毒分类")
	private String zsPoisonName;
	//可能接触的主要职业性有害因素
	@ZwxFieldDoc(value = "接触的职业性有害因素")
	private String banrsns;
	//统计工种
	@ZwxFieldDoc(value = "统计工种")
	private String analyWork;
	
	//接害开始日期
	@ZwxFieldDoc(value = "接害开始日期")
	private String harmStartDate;
	//发现日期
	@ZwxFieldDoc(value = "发现日期")
	private String findDate;
	
	//实际接尘工龄年数
	@ZwxFieldDoc(value = "实际工龄年数")
	private String tchWorkYear; 
	
	//实际接尘工龄月份
	@ZwxFieldDoc(value = "实际工龄月份")
	private String tchWorkMonth;
	//实际接尘工龄天数
	@ZwxFieldDoc(value = "实际工龄天数")
	private String tchWorkDay;
	//实际接尘工龄时
	@ZwxFieldDoc(value = "实际工龄时数")
	private String tchWorkHour;
	//实际接尘工龄分
	@ZwxFieldDoc(value = "实际工龄分数")
	private String tchWorkMinute;
	
	
	//信息来源
	@ZwxFieldDoc(value = "信息来源")
	private String infoSource;
	
	//其他信息来源
	@ZwxFieldDoc(value = "其他信息来源")
	private String otherInfoSource;
	//发现单位
	@ZwxFieldDoc(value = "发现单位")
    private String  discoveryUnit;
	//发现单位负责人
	@ZwxFieldDoc(value = "发现单位负责人")
    private String  discoveryPsn;
	
	//报告单位
	@ZwxFieldDoc(value = "报告单位")
    private String  diagUnit;
	//报告人
	@ZwxFieldDoc(value = "报告人")
    private String rptPsn;
	//报告人联系电话
	@ZwxFieldDoc(value = "报告人联系电话")
    private String rptLink;
	//报告日期
	@ZwxFieldDoc(value = "报告日期")
    private String rptDate;
	//填表人
	@ZwxFieldDoc(value = "填表人")
    private String fillFormPsn;
	//填表人联系电话
	@ZwxFieldDoc(value = "填表人联系电话")
    private String fillLink;
	//填表日期
	@ZwxFieldDoc(value = "填表日期")
    private String fillDate;
	//填表单位
	@ZwxFieldDoc(value = "填表单位")
    private String fillUnit;
	//备注
	@ZwxFieldDoc(value = "备注")
    private String rmk;

	//用工单位所属地区
	@ZwxFieldDoc(value = "所属地区")
	private String empZoneName;
	//用工单位名称
	@ZwxFieldDoc(value = "用工单位名称")
	private String empCrptName;
	//用工单位组织机构代码
	@ZwxFieldDoc(value = "统一社会信用代码")
	private String empUnitCode;

	//用工单位经济类型
	@ZwxFieldDoc(value = "经济类型")
	private String empEconomy;
	//用工单位行业
	@ZwxFieldDoc(value = "行业")
	private String empIndusType;
	//用工单位企业规模
	@ZwxFieldDoc(value = "企业规模")
	private String empCrptSize;
	
	public String getPersonalName() {
		return personalName;
	}
	public void setPersonalName(String personalName) {
		this.personalName = personalName;
	}
	public String getIdcard() {
		return idcard;
	}
	public void setIdcard(String idcard) {
		this.idcard = idcard;
	}
	public String getPersonalTel() {
		return personalTel;
	}
	public void setPersonalTel(String personalTel) {
		this.personalTel = personalTel;
	}
	public String getZoneName() {
		return zoneName;
	}
	public void setZoneName(String zoneName) {
		this.zoneName = zoneName;
	}
	public String getCrptName() {
		return crptName;
	}
	public void setCrptName(String crptName) {
		this.crptName = crptName;
	}
	public String getUnitCode() {
		return unitCode;
	}
	public void setUnitCode(String unitCode) {
		this.unitCode = unitCode;
	}
	public String getCrptLinkTel() {
		return crptLinkTel;
	}
	public void setCrptLinkTel(String crptLinkTel) {
		this.crptLinkTel = crptLinkTel;
	}
	public String getPostCode() {
		return postCode;
	}
	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}
	public String getCrptLinkMan() {
		return crptLinkMan;
	}
	public void setCrptLinkMan(String crptLinkMan) {
		this.crptLinkMan = crptLinkMan;
	}
	public String getLinkAddr() {
		return linkAddr;
	}
	public void setLinkAddr(String linkAddr) {
		this.linkAddr = linkAddr;
	}
	public String getEconomy() {
		return economy;
	}
	public void setEconomy(String economy) {
		this.economy = economy;
	}
	public String getIndusType() {
		return indusType;
	}
	public void setIndusType(String indusType) {
		this.indusType = indusType;
	}
	public String getCrptSize() {
		return crptSize;
	}
	public void setCrptSize(String crptSize) {
		this.crptSize = crptSize;
	}
	public String getBirthday() {
		return birthday;
	}
	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public String getSuspectedName() {
		return suspectedName;
	}
	public void setSuspectedName(String suspectedName) {
		this.suspectedName = suspectedName;
	}
	public String getBanrsns() {
		return banrsns;
	}
	public void setBanrsns(String banrsns) {
		this.banrsns = banrsns;
	}
	public String getAnalyWork() {
		return analyWork;
	}
	public void setAnalyWork(String analyWork) {
		this.analyWork = analyWork;
	}
	public String getTchWorkYear() {
		return tchWorkYear;
	}
	public void setTchWorkYear(String tchWorkYear) {
		this.tchWorkYear = tchWorkYear;
	}
	public String getTchWorkMonth() {
		return tchWorkMonth;
	}
	public void setTchWorkMonth(String tchWorkMonth) {
		this.tchWorkMonth = tchWorkMonth;
	}
	public String getInfoSource() {
		return infoSource;
	}
	public void setInfoSource(String infoSource) {
		this.infoSource = infoSource;
	}
	public String getOtherInfoSource() {
		return otherInfoSource;
	}
	public void setOtherInfoSource(String otherInfoSource) {
		this.otherInfoSource = otherInfoSource;
	}
	public String getDiagUnit() {
		return diagUnit;
	}
	public void setDiagUnit(String diagUnit) {
		this.diagUnit = diagUnit;
	}
	public String getFillFormPsn() {
		return fillFormPsn;
	}
	public void setFillFormPsn(String fillFormPsn) {
		this.fillFormPsn = fillFormPsn;
	}
	public String getFillLink() {
		return fillLink;
	}
	public void setFillLink(String fillLink) {
		this.fillLink = fillLink;
	}
	public String getFillDate() {
		return fillDate;
	}
	public void setFillDate(String fillDate) {
		this.fillDate = fillDate;
	}
	public String getReportYear() {
		return reportYear;
	}
	public void setReportYear(String reportYear) {
		this.reportYear = reportYear;
	}
	public String getRptNo() {
		return rptNo;
	}
	public void setRptNo(String rptNo) {
		this.rptNo = rptNo;
	}
	public String getCardType() {
		return cardType;
	}
	public void setCardType(String cardType) {
		this.cardType = cardType;
	}
	public String getYszybTypeName() {
		return yszybTypeName;
	}
	public void setYszybTypeName(String yszybTypeName) {
		this.yszybTypeName = yszybTypeName;
	}
	public String getZsPoisonName() {
		return zsPoisonName;
	}
	public void setZsPoisonName(String zsPoisonName) {
		this.zsPoisonName = zsPoisonName;
	}
	public String getHarmStartDate() {
		return harmStartDate;
	}
	public void setHarmStartDate(String harmStartDate) {
		this.harmStartDate = harmStartDate;
	}
	public String getFindDate() {
		return findDate;
	}
	public void setFindDate(String findDate) {
		this.findDate = findDate;
	}
	public String getTchWorkDay() {
		return tchWorkDay;
	}
	public void setTchWorkDay(String tchWorkDay) {
		this.tchWorkDay = tchWorkDay;
	}
	public String getTchWorkHour() {
		return tchWorkHour;
	}
	public void setTchWorkHour(String tchWorkHour) {
		this.tchWorkHour = tchWorkHour;
	}
	public String getTchWorkMinute() {
		return tchWorkMinute;
	}
	public void setTchWorkMinute(String tchWorkMinute) {
		this.tchWorkMinute = tchWorkMinute;
	}
	public String getDiscoveryUnit() {
		return discoveryUnit;
	}
	public void setDiscoveryUnit(String discoveryUnit) {
		this.discoveryUnit = discoveryUnit;
	}
	public String getDiscoveryPsn() {
		return discoveryPsn;
	}
	public void setDiscoveryPsn(String discoveryPsn) {
		this.discoveryPsn = discoveryPsn;
	}
	public String getRptPsn() {
		return rptPsn;
	}
	public void setRptPsn(String rptPsn) {
		this.rptPsn = rptPsn;
	}
	public String getRptLink() {
		return rptLink;
	}
	public void setRptLink(String rptLink) {
		this.rptLink = rptLink;
	}
	public String getRptDate() {
		return rptDate;
	}
	public void setRptDate(String rptDate) {
		this.rptDate = rptDate;
	}
	public String getFillUnit() {
		return fillUnit;
	}
	public void setFillUnit(String fillUnit) {
		this.fillUnit = fillUnit;
	}
	public String getRmk() {
		return rmk;
	}
	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

	public String getEmpZoneName() {
		return empZoneName;
	}

	public void setEmpZoneName(String empZoneName) {
		this.empZoneName = empZoneName;
	}

	public String getEmpCrptName() {
		return empCrptName;
	}

	public void setEmpCrptName(String empCrptName) {
		this.empCrptName = empCrptName;
	}

	public String getEmpUnitCode() {
		return empUnitCode;
	}

	public void setEmpUnitCode(String empUnitCode) {
		this.empUnitCode = empUnitCode;
	}

	public String getEmpEconomy() {
		return empEconomy;
	}

	public void setEmpEconomy(String empEconomy) {
		this.empEconomy = empEconomy;
	}

	public String getEmpIndusType() {
		return empIndusType;
	}

	public void setEmpIndusType(String empIndusType) {
		this.empIndusType = empIndusType;
	}

	public String getEmpCrptSize() {
		return empCrptSize;
	}

	public void setEmpCrptSize(String empCrptSize) {
		this.empCrptSize = empCrptSize;
	}
}
