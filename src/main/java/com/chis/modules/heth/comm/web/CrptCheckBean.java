package com.chis.modules.heth.comm.web;

import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.CrptCheckBO;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.entity.TdZwBgkLastSta;
import com.chis.modules.heth.comm.entity.TdZwTjorginfoNew;
import com.chis.modules.heth.comm.service.CrptCheckServiceImpl;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import com.chis.modules.system.web.IndusTypeCodePanelBean;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 用人单位审核Bean
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/07/25
 */
@ManagedBean(name = "crptCheckBean")
@ViewScoped
public class CrptCheckBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final CrptCheckServiceImpl crptCheckService = SpringContextHolder.getBean(CrptCheckServiceImpl.class);

    /**
     * 选择的结果集
     */
    private List<Object[]> selectEntitys;
    /**
     * 查询条件：用人单位地区集合
     */
    private List<TsZone> zoneList = new ArrayList<>();
    /**
     * 查询条件：用人单位地区编码
     */
    private String searchZoneCode;
    /**
     * 查询条件：用人单位地区名称
     */
    private String searchZoneName;
    /**
     * 查询条件：单位名称
     */
    private String searchCrptName;
    /**
     * 查询条件：社会信用代码
     */
    private String searchInstitutionCode;
    /**
     * 查询条件：行业类别
     */
    private String selectIndusTypeIds;
    private String selectIndusTypeNames;
    /**
     * 查询条件：经济类型
     */
    private String selectEconomyIds;
    private String selectEconomyNames;
    /**
     * 查询条件：企业规模
     */
    private String selectCrptSizeIds;
    private List<TsSimpleCode> crptSizeList = new ArrayList<>();
    /**
     * 当前操作码表类型
     */
    private String simpleCodeOpType;
    /**
     * 查询条件：状态
     * <pre>1：区县级待审<pre>3：市级待审<pre>4：市级退回<pre>5：省级待审/市级通过<pre>6：省级退回<pre>7：省级通过
     */
    private String[] states;
    private List<SelectItem> stateList = new ArrayList<>();
    /**
     * 默认状态/审核权限
     */
    private List<String> defStateList = new ArrayList<>();
    private List<String> defStateStrList = new ArrayList<>();
    /**
     * 配置参数：审核级别 <pre>3：(区->)市->省<pre>2：(区->)市/省
     */
    private String checkLevel;
    /**
     * 是否二级审核
     */
    private boolean checkLevel2;
    /**
     * 是否三级审核
     */
    private boolean checkLevel3;
    /**
     * 配置参数：市/省级平台 <pre>1：市级平台<pre>2：省级平台
     */
    private String platVersion;
    /**
     * 二级审核且市级平台
     */
    private boolean platVersionCity;
    /**
     * 二级审核且省级平台
     */
    private boolean platVersionProv;
    /**
     * 账号地区级别 <pre>4：区县级<pre>3：市级<pre>2：省级
     */
    private String accountLevel;
    /**
     * 审核操作类型 <pre>1：批量审核<pre>2：全部审核<pre>3：单个审核
     */
    private Integer checkOpType;
    /**
     * 体检机构提示信息：体检机构：xx 联系人：xx 联系电话：xx
     */
    private String tipInfo;
    /****************审核界面**********************/
    private Integer rid;
    /**审核状态*/
    private Integer checkState;
    /**审核意见*/
    private String checkRst;
    /**历次审核意见*/
    private List<Object[]> historyList;
    /**审核页面当前操作的企业对象*/
    private TbTjCrpt tbTjCrpt;
    /**审核通过默认配置*/
    private String defaultAuditAdv;
    /**是否禁用退回功能*/
    private boolean itemDisabled=Boolean.FALSE;

    /** 所属地区 */
    private TsZone editAreaZone;
    /** 单位名称 */
    private String editCrptName;
    /** 单位地址 */
    private String editAddress;
    /** 经济类型 */
    private TsSimpleCode editEconomy;
    /** 企业规模 */
    private Integer editCrptSizeId;
    /** 行业类别 */
    private TsSimpleCode editIndusType;
    /** 邮政编码 */
    private String editPostCard;
    /** 法人 */
    private String editCorporateName;
    /** 法人联系电话 */
    private String editCorporatePhone;
    /** 体检联系人 */
    private String editLinkMan2;
    /** 体检联系人电话 */
    private String editLinkPhone2;
    /** 职工人数 */
    private Integer editWorkForce;
    /** 接触职业病危害因素人数 */
    private Integer editHoldCardMan;
    /** 外委人数 */
    private Integer editOutsourceNum;
    /** 生产工人数 */
    private Integer editWorkmanNum;
    /** 同社会信用代码关联单位列表 */
    private List<Object[]> showRelationUnitList;
    /** 改为主体机构传递的rid */
    private Integer editUpperUnitId;
    /** 经济类型码表 */
    private List<TsSimpleCode> econList;
    /** 行业类别码表 */
    private List<TsSimpleCode> indusList;
    /** 最末级经济类型码表rid集合 */
    private List<Integer> validateEconomyIds;
    /** 最末级行业类别码表rid集合 */
    private List<Integer> validateIndusTypeIds;


    /** 行业类别弹框Bean */
    private IndusTypeCodePanelBean codePanelBean;
    /** 经济类型弹出框显示标题 */
    private String selCodeName;
    /** 经济类型弹框查询条件大类编码 */
    private String firstCodeNo;
    /**经济类型：大类码表数据*/
    private List<TsSimpleCode> firstList;
    /**经济类型：所有码表数据*/
    private List<TsSimpleCode> allList;
    /**经济类型：展示码表数据*/
    private List<TsSimpleCode> displayList;
    /** 经济类型弹框查询条件名称或者拼音码 */
    private String searchNamOrPy;
    /** 行业类别/经济类型选中的对象 */
    private TsSimpleCode selectPro;
    /****************审核界面**********************/
    /****************详情界面**********************/
    /** 是否显示撤销 */
    private boolean ifShowCancel;
    /** 最新状态记录 */
    private TdZwBgkLastSta bgkLastSta;
    /** 审批流程操作标识 用于撤销时删除审批流程 */
    private Integer bgkFlowState;
    /** 准备修改的最新状态记录的状态 用于撤销时修改最新状态表 */
    private Integer resetBgkState;
    /****************详情界面**********************/


    /****************地区变更**********************/
    private List<TsZone> changeZoneList = new ArrayList<>();
    /**
     * 地区编码
     */
    private String changeZoneCode;
    /**
     * 地区名称
     */
    private String changeZoneName;
    /** 地区级别*/
    private Integer changeZoneType;
    /** 地区id*/
    private Integer changeZoneId;
    /** 用人单位初始地区*/
    private TsZone initZone;

    /****************地区变更**********************/
    public CrptCheckBean() {
        initSimpleCode();
        otherInit();
        searchAction();
    }

    /**
     * 码表初始化
     */
    private void initSimpleCode() {
        long stime = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(this.indusList)) {
            this.indusList = this.commService.findNumSimpleCodesByTypeId("5002");
        }
        if (CollectionUtils.isEmpty(this.econList)) {
            this.econList = this.commService.findNumSimpleCodesByTypeId("5003");
        }
        if (CollectionUtils.isEmpty(this.crptSizeList)) {
            this.crptSizeList = this.commService.findLevelSimpleCodesByTypeId("5004");
        }
        long etime = System.currentTimeMillis();
        System.out.printf("2.5耗时：%d 毫秒.\n", (etime - stime));
        this.validateIndusTypeIds = ridListByList(this.indusList);
        this.validateEconomyIds = ridListByList(this.econList);
    }

    /**
     * 配置等其它初始化
     */
    private void otherInit() {
        this.ifSQL = true;
        //审核级别
        this.checkLevel = StringUtils.objectToString(PropertyUtils.getValueWithoutException("checkLevel"));
        this.checkLevel2 = "2".equals(this.checkLevel);
        this.checkLevel3 = "3".equals(this.checkLevel);
        //市/省级平台
        this.platVersion = StringUtils.objectToString(PropertyUtils.getValueWithoutException("platVersion"));
        this.platVersionCity = this.checkLevel2 && "1".equals(this.platVersion);
        this.platVersionProv = this.checkLevel2 && "2".equals(this.platVersion);
        //账号地区级别
        //当前登录账号单位的地区级别(real_zone_type)-> 账号地区级别(4->区县级; 3->市级;2->省级)
        this.accountLevel = StringUtils.objectToString(Global.getUser().getTsUnit().getFkByManagedZoneId().getRealZoneType());
        //批量审核默认审核意见
        this.defaultAuditAdv = PropertyUtils.getValueWithoutException("defaultAuditAdv");
        this.codePanelBean = new IndusTypeCodePanelBean(this.indusList);
        //地区
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        //查询条件：地区
        if (null == this.zoneList || this.zoneList.size() == 0) {
            this.zoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "");
            this.searchZoneCode = this.zoneList.get(0).getZoneCode();
            this.searchZoneName = this.zoneList.get(0).getZoneName();
        }
        //地区变更：可选择省内所有地区
        if (null == this.changeZoneList || this.changeZoneList.size() == 0) {
            this.changeZoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb().substring(0, 2) + "**********", true, "", "");
        }
        //状态
        statesInit();
    }

    /**
     * 状态初始化
     */
    private void statesInit() {
        //状态默认值
        if ("4".equals(this.accountLevel)) {
            if (this.platVersionProv) {
                //区县级(省级平台)->1、6
                this.states = new String[]{"1", "6"};
                this.defStateList.add("1");
                this.defStateList.add("6");
                this.defStateStrList.add("区县级待审");
                this.defStateStrList.add("省级退回");
            } else {
                //区县级->1、4
                this.states = new String[]{"1", "4"};
                this.defStateList.add("1");
                this.defStateList.add("4");
                this.defStateStrList.add("区县级待审");
                this.defStateStrList.add("市级退回");
            }
        } else if ("3".equals(this.accountLevel)) {
            if (this.platVersionCity) {
                //市级(市级平台)->3
                this.states = new String[]{"3"};
                this.defStateList.add("3");
                this.defStateStrList.add("市级待审");
            } else {
                //市级->3、6
                this.states = new String[]{"3", "6"};
                this.defStateList.add("3");
                this.defStateList.add("6");
                this.defStateStrList.add("市级待审");
                this.defStateStrList.add("省级退回");
            }
        } else if ("2".equals(this.accountLevel)) {
            //省级->5
            this.states = new String[]{"5"};
            this.defStateList.add("5");
            this.defStateStrList.add(this.platVersionCity ? "市级通过" : "省级待审");
        }
        //状态页面显示值
        this.stateList.add(new SelectItem("1", "区县级待审"));
        if (!this.platVersionProv) {
            this.stateList.add(new SelectItem("3", "市级待审"));
            this.stateList.add(new SelectItem("4", "市级退回"));
        }
        this.stateList.add(new SelectItem("5", this.platVersionCity ? "市级通过" : "省级待审"));
        if (!this.platVersionCity) {
            this.stateList.add(new SelectItem("6", "省级退回"));
            this.stateList.add(new SelectItem("7", "省级通过"));
        }
    }

    /**
     * 生成SQL
     *
     * @param type 类型 <pre>1: 查询列表(默认)<pre>2: 导出<pre>3: 全部审核
     * @return SQL
     */
    private String buildSql(int type) {
        this.paramMap = new HashMap<>(16);
        StringBuilder baseSql = new StringBuilder();
        baseSql.append("SELECT ");
        if (type == 3) {
            baseSql.append("       C.RID              AS P0, ");
            baseSql.append("       C.CRPT_NAME        AS P1, ");
            baseSql.append("       C.INSTITUTION_CODE AS P2, ");
            baseSql.append("       Z.RID              AS P3, ");
            baseSql.append("       Z.REAL_ZONE_TYPE   AS P4, ");
            baseSql.append("       Z.IF_CITY_DIRECT   AS P5, ");
            baseSql.append("       Z.IF_PROV_DIRECT   AS P6, ");
            baseSql.append("       SC1.RID            AS P7, ");
            baseSql.append("       SC2.RID            AS P8, ");
            baseSql.append("       SC3.RID            AS P9, ");
            baseSql.append("       C.POSTCODE         AS P10, ");
            baseSql.append("       C.PHONE            AS P11, ");
            baseSql.append("       C.LINKPHONE2       AS P12, ");
            baseSql.append("       C.WORK_FORCE       AS P13, ");
            baseSql.append("       C.HOLD_CARD_MAN    AS P14, ");
            baseSql.append("       C.WORKMAN_NUM      AS P15, ");
            baseSql.append("       C.OUTSOURCE_NUM    AS P16, ");
            baseSql.append("       S.STATE            AS P17 ");
        } else if (type == 2) {
            baseSql.append("       Z.FULL_NAME        AS P0, ");
            baseSql.append("       C.CRPT_NAME        AS P1, ");
            baseSql.append("       C.INSTITUTION_CODE AS P2, ");
            baseSql.append("       C.IF_SUB_ORG       AS P3, ");
            baseSql.append("       C.ADDRESS          AS P4, ");
            baseSql.append("       C.CORPORATE_DEPUTY AS P5, ");
            baseSql.append("       C.PHONE            AS P6, ");
            baseSql.append("       C.LINKMAN2         AS P7, ");
            baseSql.append("       C.LINKPHONE2       AS P8, ");
            baseSql.append("       SC1.CODE_PATH      AS P9, ");
            baseSql.append("       SC2.CODE_PATH      AS P10, ");
            baseSql.append("       SC3.CODE_NAME      AS P11, ");
            baseSql.append("       C.WORK_FORCE       AS P12, ");
            baseSql.append("       C.HOLD_CARD_MAN    AS P13, ");
            baseSql.append("       C.WORKMAN_NUM      AS P14, ");
            baseSql.append("       S.STATE            AS P15, ");
            baseSql.append("       T.UNITNAME         AS P16, ");
            baseSql.append("       T.LINK_MAN         AS P17, ");
            baseSql.append("       T.LINK_TEL         AS P18, ");
            baseSql.append("       Z.IF_CITY_DIRECT   AS P19, ");
            baseSql.append("       Z.IF_PROV_DIRECT   AS P20, ");
            baseSql.append("       Z.ZONE_GB          AS P21 ");
        } else {
            baseSql.append("       C.RID              AS P0, ");
            baseSql.append("       Z.FULL_NAME        AS P1, ");
            baseSql.append("       C.CRPT_NAME        AS P2, ");
            baseSql.append("       C.INSTITUTION_CODE AS P3, ");
            baseSql.append("       C.IF_SUB_ORG       AS P4, ");
            baseSql.append("       SC1.CODE_NAME      AS P5, ");
            baseSql.append("       SC2.CODE_NAME      AS P6, ");
            baseSql.append("       SC3.CODE_NAME      AS P7, ");
            baseSql.append("       S.STATE            AS P8, ");
            baseSql.append("       DECODE(S.STATE, 4, S.CITY_AUDIT_ADV, 6, S.PRO_AUDIT_ADV, '') AS P9, ");
            baseSql.append("       Z.IF_CITY_DIRECT AS P10, ");
            baseSql.append("       Z.IF_PROV_DIRECT AS P11, ");
            baseSql.append("       Z.ZONE_GB AS P12, ");
            baseSql.append("       '' AS P13, ");
            baseSql.append("       C.IF_SUB_ORG AS P14, ");//是否分支机构标识
            baseSql.append("       S.STATE AS P15, ");//最新状态表的状态标识
            baseSql.append("       '' AS P16 ");
        }
        baseSql.append("FROM TB_TJ_CRPT C ");
        baseSql.append("         LEFT JOIN TS_ZONE Z ON C.ZONE_ID = Z.RID ");
        baseSql.append("         LEFT JOIN TS_SIMPLE_CODE SC1 ON C.INDUS_TYPE_ID = SC1.RID ");
        baseSql.append("         LEFT JOIN TS_SIMPLE_CODE SC2 ON C.ECONOMY_ID = SC2.RID ");
        baseSql.append("         LEFT JOIN TS_SIMPLE_CODE SC3 ON C.CRPT_SIZE_ID = SC3.RID ");
        baseSql.append("         LEFT JOIN TD_ZW_BGK_LAST_STA S ON C.RID = S.BUS_ID AND S.CART_TYPE = '10' ");
        //baseSql.append("         LEFT JOIN TB_TJ_CRPT C1 ON C.UPPER_UNIT_ID = C1.RID ");
        if (type == 2) {
            baseSql.append("         LEFT JOIN TEMP T ON C.RID = T.CRPT_ID ");
        }
        baseSql.append("WHERE 1 = 1 AND C.INTER_PRC_TAG =1 AND nvl(C.DEL_MARK,0)=0 ");
        baseSql.append("  AND C.INDUS_TYPE_ID IS NOT NULL ");
        baseSql.append("  AND C.ECONOMY_ID IS NOT NULL ");
        baseSql.append("  AND C.CRPT_SIZE_ID IS NOT NULL ");
        // 用人单位地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            baseSql.append(" AND Z.ZONE_GB LIKE :zoneCode ");
            this.paramMap.put("zoneCode", ZoneUtil.zoneSelect(this.searchZoneCode) + "%");
        }
        // 单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            baseSql.append(" AND C.CRPT_NAME LIKE :searchCrptName escape '\\\' ");
            this.paramMap.put("searchCrptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        // 社会信用代码
        if (StringUtils.isNotBlank(this.searchInstitutionCode)) {
            baseSql.append(" AND C.INSTITUTION_CODE LIKE :searchInstitutionCode escape '\\\' ");
            this.paramMap.put("searchInstitutionCode", "%" + StringUtils.convertBFH(this.searchInstitutionCode.trim()) + "%");
        }
        // 行业类别
        if (StringUtils.isNotBlank(this.selectIndusTypeIds)) {
            baseSql.append(" AND C.INDUS_TYPE_ID IN (:indusTypeIdList) ");
            List<String> indusTypeIdList = StringUtils.string2list(this.selectIndusTypeIds, ",");
            this.paramMap.put("indusTypeIdList", indusTypeIdList);
        }
        // 经济类型
        if (StringUtils.isNotBlank(this.selectEconomyIds)) {
            baseSql.append(" AND C.ECONOMY_ID IN (:economyIdList) ");
            List<String> economyIdList = StringUtils.string2list(this.selectEconomyIds, ",");
            this.paramMap.put("economyIdList", economyIdList);
        }
        // 企业规模
        if (StringUtils.isNotBlank(this.selectCrptSizeIds)) {
            baseSql.append(" AND C.CRPT_SIZE_ID IN (:crptSizeIdList) ");
            List<String> crptSizeIdList = StringUtils.string2list(this.selectCrptSizeIds, ",");
            this.paramMap.put("crptSizeIdList", crptSizeIdList);
        }
        // 状态 states
        baseSql.append(" AND ").append(pakStatusConditionSql(type == 3));
        return baseSql.toString();
    }

    /**
     * 封装状态查询条件
     *
     * @param onlyCheckState 是否仅查询当前选择且具有审核权限的状态
     * @return 状态查询条件
     */
    private String pakStatusConditionSql(boolean onlyCheckState) {
        StringBuilder str = new StringBuilder();
        str.append("(");
        List<String> searchStatusList = new ArrayList<>();
        List<String> stateList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(this.states)) {
            stateList = Arrays.asList(this.states);
        } else {
            for (SelectItem selectItem : this.stateList) {
                stateList.add(StringUtils.objectToString(selectItem.getValue()));
            }
        }
        if (onlyCheckState) {
            //仅查询当前选择且具有审核权限的状态
            for (String state : defStateList) {
                if (stateList.contains(state)) {
                    searchStatusList.add(state);
                }
            }
        } else {
            //查询当前账号所有状态
            searchStatusList.addAll(stateList);
        }
        if (ObjectUtil.isNotEmpty(searchStatusList)) {
            str.append("S.STATE IN (").append(StringUtils.list2string(searchStatusList, ",")).append(") ");
        } else {
            return "1 = 2";
        }
        if (searchStatusList.contains("1")) {
            if (this.checkLevel3) {
                str.append(" OR (NVL(Z.IF_CITY_DIRECT, 0) = 0 AND S.STATE IS NULL)");
            }
            if (this.checkLevel2) {
                str.append(" OR (NVL(Z.IF_PROV_DIRECT, 0) = 0 AND S.STATE IS NULL)");
            }
        }
        if (searchStatusList.contains("3")) {
            if (this.checkLevel3) {
                str.append(" OR (NVL(Z.IF_CITY_DIRECT, 0) = 1 AND S.STATE IS NULL)");
            }
            if (this.platVersionCity) {
                str.append(" OR (NVL(Z.IF_PROV_DIRECT, 0) = 1 AND S.STATE IS NULL)");
            }
        }
        if (searchStatusList.contains("5")) {
            if (this.platVersionProv) {
                str.append(" OR (NVL(Z.IF_PROV_DIRECT, 0) = 1 AND S.STATE IS NULL)");
            }
        }

        str.append(") ");
        return str.toString();
    }

    @Override
    public String[] buildHqls() {
        String sql = buildSql(1);
        String h1 = "SELECT * FROM (" + sql + ")AA ORDER BY AA.P12, AA.P2";
        String h2 = "SELECT COUNT(*) FROM (" + sql + ")";
        return new String[]{h1, h2};
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<Object[]> result = CollectionUtil.castList(Object[].class, list);
        for (Object[] obj : result) {
            //地区去除省份
            String fullName = StringUtils.objectToString(obj[1]);
            obj[1] = fullName.substring(fullName.indexOf("_") + 1);
            obj[4] = "0".equals(StringUtils.objectToString(obj[4])) ? "否" : "是";
            boolean ifCityDirect = this.checkLevel3 && "1".equals(StringUtils.objectToString(obj[10]));
            boolean ifProvDirect = this.checkLevel2 && "1".equals(StringUtils.objectToString(obj[11]));
            obj[8] = getStateStr(StringUtils.objectToString(obj[8]), ifCityDirect, ifProvDirect);
            obj[13] = this.defStateStrList.contains(obj[8]);
        }
    }

    /**
     * 判断单位当前显示状态
     *
     * @param state        状态
     * @param ifCityDirect 是否市直属
     * @param ifProvDirect 是否省直属
     * @return 显示状态
     */
    private String getStateStr(String state, boolean ifCityDirect, boolean ifProvDirect) {
        if (ObjectUtil.isEmpty(state)) {
            if (!ifCityDirect && !ifProvDirect) {
                return "区县级待审";
            }
            if (ifCityDirect || this.platVersionCity) {
                return "市级待审";
            }
            if (this.platVersionProv) {
                return "省级待审";
            }
            return "";
        }
        switch (state) {
            case "1":
                return "区县级待审";
            case "3":
                return "市级待审";
            case "4":
                return "市级退回";
            case "5":
                return this.platVersionCity ? "市级通过" : "省级待审";
            case "6":
                return "省级退回";
            case "7":
                return "省级通过";
            default:
                return "";
        }
    }

    @Override
    public void searchAction() {
        super.searchAction();

        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update("tabView:mainForm:dataTable");
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {
        if(this.rid==null){
            return;
        }
        this.initCrptInfo();
        //初始化历次审核意见
        this.historyList = this.crptCheckService.findHisotryListByCrpt(this.rid);
        this.initIfShowCancel(this.tbTjCrpt.getTsZoneByZoneId());
        this.initShowRelationUnitList();
        tipInfoInit();
    }

    @Override
    public void modInit() {

    }

    @Override
    public void backAction(){
        this.setActiveTab(0);
        this.searchAction();
    }

    /**
     * 提示信息初始化
     */
    public void tipInfoInit() {
        this.tipInfo = "";
        TdZwTjorginfoNew orgInfo = this.crptCheckService.findOrgInfoByUnitLatest(this.rid);
        if (ObjectUtil.isNotEmpty(orgInfo) && ObjectUtil.isNotEmpty(orgInfo.getRid())) {
            this.tipInfo = "体检机构：" + orgInfo.getOrgName() + "　｜　联系人：" + orgInfo.getLinkMan() + "　｜　联系电话：" + orgInfo.getLinkTel();
        }
    }

    /**
     * 选择码表弹框页面
     */
    public void selSimpleCodeAction() {
        int contentWidth = 700;
        if ("5002".equals(this.simpleCodeOpType)) {
            contentWidth = 800;
        }
        Map<String, Object> options = MapUtils.produceDialogMap(null, contentWidth, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add("5002".equals(this.simpleCodeOpType) ? "行业类别" : "经济类型");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>();
        paramList.add(this.simpleCodeOpType);
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<>();
        if ("5002".equals(this.simpleCodeOpType)) {
            paramList.add(this.selectIndusTypeIds);
        } else {
            paramList.add(this.selectEconomyIds);
        }
        paramMap.put("selectIds", paramList);
        if ("5002".equals(this.simpleCodeOpType) || "5003".equals(this.simpleCodeOpType)) {
            paramList = new ArrayList<>();
            paramList.add("true");
            paramMap.put("ifShowFirstCode", paramList);
        }
        RequestContext requestContext = RequestContext.getCurrentInstance();
        String dialogUrl = "/webapp/system/";
        if ("5002".equals(this.simpleCodeOpType)) {
            dialogUrl += "indusTypeCodeMulitySelectList";
        } else {
            dialogUrl += "codeMulitySelectList";
        }
        requestContext.openDialog(dialogUrl, options, paramMap);
    }

    /**
     * 选择码表后操作
     *
     * @param event 选择项
     */
    public void onSimpleCodeAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = CollectionUtil.castList(TsSimpleCode.class, selectedMap.get("selectPros"));
            if (ObjectUtil.isNotEmpty(list)) {
                StringBuilder names = new StringBuilder();
                StringBuilder ids = new StringBuilder();
                for (TsSimpleCode t : list) {
                    names.append("，").append(t.getCodeName());
                    ids.append(",").append(t.getRid());
                }
                if ("5002".equals(this.simpleCodeOpType)) {
                    //行业类别
                    this.selectIndusTypeIds = ids.substring(1);
                    this.selectIndusTypeNames = names.substring(1);
                } else if ("5003".equals(this.simpleCodeOpType)) {
                    //经济类型
                    this.selectEconomyIds = ids.substring(1);
                    this.selectEconomyNames = names.substring(1);
                }
            }
        }
    }

    /**
     * 清除页面选择码表
     */
    public void clearSimpleCode() {
        if ("5002".equals(this.simpleCodeOpType)) {
            //行业类别
            this.selectIndusTypeNames = null;
            this.selectIndusTypeIds = null;
        } else if ("5003".equals(this.simpleCodeOpType)) {
            //经济类型
            this.selectEconomyNames = null;
            this.selectEconomyIds = null;
        }
    }

    public static Integer returnIntegerOrNull(Object obj) {
        String str = StringUtils.objectToString(obj);
        if (ObjectUtil.isEmpty(str)) {
            return null;
        }
        Pattern pattern = Pattern.compile("\\d*");
        return pattern.matcher(str).matches() ? Integer.valueOf(str) : null;
    }

    /**
     * 审核弹出框(用于批量/全部审核)
     *
     * @param checkOpType 审核操作类型 <pre>1：批量审核<pre>2：全部审核
     */
    public void openCheckConfirmDialog(Integer checkOpType) {
        if (ObjectUtil.isEmpty(checkOpType) || (checkOpType != 1 && checkOpType != 2)) {
            return;
        }
        this.checkOpType = checkOpType;
        //默认通过
        this.checkState = 1;
        this.checkRst = StringUtils.objectToString(this.defaultAuditAdv);

        if (checkOpType == 1) {
            //批量审核
            if (this.selectEntitys == null || this.selectEntitys.size() == 0) {
                JsfUtil.addErrorMessage("请选择需要审核的数据！");
                return;
            }
        } else {
            //全部审核
            String sql = "SELECT COUNT(*) FROM (" + buildSql(3) + ")";
            int count = this.commService.findCountBySql(sql, this.paramMap);
            if (count == 0) {
                JsfUtil.addErrorMessage("无待审核的数据！");
                return;
            }
        }
        RequestContext.getCurrentInstance().execute("PF('CheckConfirmDialog').show()");
        RequestContext.getCurrentInstance().update("tabView:mainForm:checkConfirmDialog");
    }

    public void checkBatchAction() {
        if (ObjectUtil.isEmpty(this.checkOpType) || (this.checkOpType != 1 && this.checkOpType != 2)) {
            return;
        }
        //验证审核结果、审核意见
        if (!verifyAuditResults()) {
            return;
        }
        //是否审核通过
        boolean approved = this.checkState == 1;
        //存储批量/全部审核的数据
        List<CrptCheckBO> srcCrptCheckList = new ArrayList<>();
        if (this.checkOpType == 1) {
            //crptCheckList = this.selectEntitys;
            JsfUtil.addErrorMessage("批量审核正在开发中！");
            return;
        } else {
            List<Object[]> dataList = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(buildSql(3), this.paramMap));
            for (Object[] data : dataList) {
                srcCrptCheckList.add(pakCrptCheckBOByObj(data));
            }
        }
        //若退回，计算市/省直属地区不能退回条数并移除不能退回数据不参与后续流程
        int checkUnnecessaryReturn = calCheckUnnecessaryReturnNum(srcCrptCheckList);
        //验证数据是否必填且逻辑是否正确并移除不通过数据不参与后续流程
        int checkFailed = calCheckValidateFailedNum(srcCrptCheckList);
        List<CrptCheckBO> crptCheckList = ObjectCopyUtil.deepCopy(srcCrptCheckList);
        //key: 状态: sa(新增)/se+状态(更新);操作标识: opa+操作标识(新增) value:rid List
        Map<String, List<Integer>> dataMap = new HashMap<>();
        //遍历单位判断状态与操作标识
        for (CrptCheckBO crptCheck : crptCheckList) {
            Integer crptRid = crptCheck.getCrptRid();
            //最新状态表操作
            putState(dataMap, approved, crptCheck.getIfProvDirect(), crptCheck.getStateNew(), crptRid);
            //历史审核流程操作
            putLastCheckProcessForInsert(dataMap, approved, crptCheck.getIfProvDirect(), crptRid);
        }
        try {
            //批量处理最新状态表及历史审核流程数据
            int checkSuccess = this.crptCheckService.batchCrptCheck(dataMap, this.accountLevel, this.checkState, this.checkRst, this.checkLevel2);

            String message = "审核成功" + checkSuccess + "条，失败" + checkFailed;
            if (checkUnnecessaryReturn != 0) {
                message += "条，" + (this.platVersionProv ? "省直属" : "市直属") + "地区不能退回" + checkUnnecessaryReturn;
            }
            message += "条。";
            JsfUtil.addSuccessMessage(message);
            RequestContext.getCurrentInstance().execute("PF('CheckConfirmDialog').hide();datatableOffClick();");
            this.searchAction();
            RequestContext.getCurrentInstance().update("tabView:mainForm:dataTable");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("审核失败！");
            e.printStackTrace();
        }
    }

    private int calCheckUnnecessaryReturnNum(List<CrptCheckBO> srcCrptCheckList) {
        if (this.checkState == 1) {
            return 0;
        }
        int checkUnnecessaryReturn = 0;
        for (int i = srcCrptCheckList.size() - 1; i >= 0; i--) {
            CrptCheckBO crptCheck = srcCrptCheckList.get(i);
            if (("3".equals(this.accountLevel) && crptCheck.getIfCityDirect()) || crptCheck.getIfProvDirect()) {
                checkUnnecessaryReturn++;
                srcCrptCheckList.remove(i);
            }
        }
        return checkUnnecessaryReturn;
    }

    private int calCheckValidateFailedNum(List<CrptCheckBO> srcCrptCheckList) {
        int checkFailed = 0;
        for (int i = srcCrptCheckList.size() - 1; i >= 0; i--) {
            CrptCheckBO crptCheck = srcCrptCheckList.get(i);
            if (!crptCheckAllValidate(crptCheck)) {
                checkFailed++;
                srcCrptCheckList.remove(i);
            }
        }
        return checkFailed;
    }

    private boolean verifyAuditResults() {
        boolean pass = true;
        if (null == this.checkState) {
            JsfUtil.addErrorMessage("审核结果不能为空！");
            pass = false;
        }
        if (StringUtils.isBlank(this.checkRst)) {
            JsfUtil.addErrorMessage("审核意见不能为空！");
            pass = false;
        } else if (this.checkRst.length() > 100) {
            JsfUtil.addErrorMessage("审核意见内容超出长度限制！");
            pass = false;
        }
        if (this.checkState != 1) {
            if ("4".equals(this.accountLevel)) {
                JsfUtil.addErrorMessage("区县级不能退回！");
                pass = false;
            }
        }
        return pass;
    }

    private void putState(Map<String, List<Integer>> dataMap,
                          boolean approved, boolean ifProvDirect, boolean needInsert, Integer crptRid) {
        //如果最新状态表没有记录需要插入
        if (needInsert) {
            mapListAdd(dataMap, "sa", crptRid);
        }
        if ("4".equals(this.accountLevel)) {
            if (approved) {
                if (!ifProvDirect && this.platVersionProv) {
                    //区县级账号二级审核非省直属省级平台通过
                    mapListAdd(dataMap, "se5", crptRid);
                } else {
                    //区县级账号其它通过
                    mapListAdd(dataMap, "se3", crptRid);
                }
            }
        } else if ("3".equals(this.accountLevel)) {
            if (approved) {
                //市级账号通过
                mapListAdd(dataMap, "se5", crptRid);
            } else {
                //市级账号退回
                mapListAdd(dataMap, "se4", crptRid);
            }
        } else if ("2".equals(this.accountLevel)) {
            if (approved) {
                //省级账号通过
                mapListAdd(dataMap, "se7", crptRid);
            } else {
                //省级账号退回
                mapListAdd(dataMap, "se6", crptRid);
            }
        }
    }

    private void putLastCheckProcessForInsert(Map<String, List<Integer>> dataMap,
                                              boolean approved, boolean ifProvDirect, Integer crptRid) {
        if ("4".equals(this.accountLevel)) {
            if (approved) {
                if (!ifProvDirect && this.platVersionProv) {
                    //区县级账号二级审核非省直属省级平台通过
                    mapListAdd(dataMap, "opa43", crptRid);
                } else {
                    //区县级账号其它通过
                    mapListAdd(dataMap, "opa31", crptRid);
                }
            }
        } else if ("3".equals(this.accountLevel)) {
            if (approved) {
                if (this.checkLevel3) {
                    //市级账号三级审核通过
                    mapListAdd(dataMap, "opa41", crptRid);
                } else if (this.checkLevel2) {
                    //市级账号二级审核通过
                    mapListAdd(dataMap, "opa33", crptRid);
                }
            } else {
                //市级账号退回
                mapListAdd(dataMap, "opa21", crptRid);
            }
        } else if ("2".equals(this.accountLevel)) {
            if (approved) {
                //省级账号通过
                mapListAdd(dataMap, "opa42", crptRid);
            } else {
                if (this.checkLevel3) {
                    //省级账号三级审核退回
                    mapListAdd(dataMap, "opa32", crptRid);
                } else if (this.checkLevel2) {
                    //省级账号二级审核退回
                    mapListAdd(dataMap, "opa22", crptRid);
                }
            }
        }
    }

    private void mapListAdd(Map<String, List<Integer>> dataMap, String key, Integer value) {
        if (!dataMap.containsKey(key)) {
            dataMap.put(key, new ArrayList<Integer>());
        }
        dataMap.get(key).add(value);
    }

    private CrptCheckBO pakCrptCheckBOByObj(Object[] data) {
        CrptCheckBO crptCheckBO1 = new CrptCheckBO();
        crptCheckBO1.setCrptRid(returnIntegerOrNull(data[0]));
        crptCheckBO1.setCrptName(StringUtils.objectToString(data[1]));
        crptCheckBO1.setInstitutionCode(StringUtils.objectToString(data[2]));
        crptCheckBO1.setZoneRid(returnIntegerOrNull(data[3]));
        crptCheckBO1.setZoneRealType(returnIntegerOrNull(data[4]));
        crptCheckBO1.setIfCityDirect(this.checkLevel3 && "1".equals(StringUtils.objectToString(data[5])));
        crptCheckBO1.setIfProvDirect(this.checkLevel2 && "1".equals(StringUtils.objectToString(data[6])));
        crptCheckBO1.setIndusTypeRid(returnIntegerOrNull(data[7]));
        crptCheckBO1.setEconomyRid(returnIntegerOrNull(data[8]));
        crptCheckBO1.setCrptSizeRid(returnIntegerOrNull(data[9]));
        crptCheckBO1.setPostCard(StringUtils.objectToString(data[10]));
        crptCheckBO1.setCorporatePhone(StringUtils.objectToString(data[11]));
        crptCheckBO1.setLinkPhone2(StringUtils.objectToString(data[12]));
        crptCheckBO1.setWorkForce(returnIntegerOrNull(data[13]));
        crptCheckBO1.setHoldCardMan(returnIntegerOrNull(data[14]));
        crptCheckBO1.setWorkmanNum(returnIntegerOrNull(data[15]));
        crptCheckBO1.setOutsourceNum(returnIntegerOrNull(data[16]));
        crptCheckBO1.setStateNew(ObjectUtil.isEmpty(data[17]));
        if (ObjectUtil.isEmpty(StringUtils.objectToString(data[17]))) {
            if (!crptCheckBO1.getIfCityDirect() && !crptCheckBO1.getIfProvDirect()) {
                crptCheckBO1.setState(1);
            } else if (this.platVersionProv) {
                crptCheckBO1.setState(5);
            } else {
                crptCheckBO1.setState(3);
            }
        } else {
            crptCheckBO1.setState(returnIntegerOrNull(data[17]));
        }
        return crptCheckBO1;
    }

    private boolean crptCheckAllValidate(CrptCheckBO crptCheckBO) {
        //单位名称必填
        if (StringUtils.isBlank(crptCheckBO.getCrptName())) {
            return false;
        }
        //社会信用代码必填
        if (StringUtils.isBlank(crptCheckBO.getInstitutionCode())) {
            return false;
        }
        //地区必填
        if (ObjectUtil.isEmpty(crptCheckBO.getZoneRid())) {
            return false;
        }
        //所属地区必须选择至街道
        if (ObjectUtil.isEmpty(crptCheckBO.getZoneRealType()) || crptCheckBO.getZoneRealType() < 5) {
            return false;
        }
        //行业类别必填 选择最末级
        if (ObjectUtil.isEmpty(crptCheckBO.getIndusTypeRid()) || !this.validateIndusTypeIds.contains(crptCheckBO.getIndusTypeRid())) {
            return false;
        }
        //经济类型必填 选择最末级
        if (ObjectUtil.isEmpty(crptCheckBO.getEconomyRid()) || !this.validateEconomyIds.contains(crptCheckBO.getEconomyRid())) {
            return false;
        }
        //企业规模必填
        if (ObjectUtil.isEmpty(crptCheckBO.getCrptSizeRid())) {
            return false;
        }
        //邮政编码如果存在 格式是否通过验证
        if (StringUtils.isNotBlank(crptCheckBO.getPostCard()) && !StringUtils.vertyPost(crptCheckBO.getPostCard())) {
            return false;
        }
        //法人联系电话如果存在 格式是否通过验证
        if (StringUtils.isNotBlank(crptCheckBO.getCorporatePhone()) && !StringUtils.vertyPhone(crptCheckBO.getCorporatePhone())) {
            return false;
        }
        //联系人电话如果存在 格式是否通过验证
        if (StringUtils.isNotBlank(crptCheckBO.getLinkPhone2()) && !StringUtils.vertyPhone(crptCheckBO.getLinkPhone2())) {
            return false;
        }
        if (ObjectUtil.isNotEmpty(crptCheckBO.getWorkForce())) {
            if (crptCheckBO.getWorkForce() <= 0) {
                return false;
            }
            if (ObjectUtil.isNotEmpty(crptCheckBO.getHoldCardMan()) && crptCheckBO.getHoldCardMan().compareTo(crptCheckBO.getWorkForce()) > 0) {
                return false;
            }
            if (ObjectUtil.isNotEmpty(crptCheckBO.getWorkmanNum()) && crptCheckBO.getWorkmanNum().compareTo(crptCheckBO.getWorkForce()) > 0) {
                return false;
            }
            if (ObjectUtil.isNotEmpty(crptCheckBO.getOutsourceNum()) && crptCheckBO.getOutsourceNum().compareTo(crptCheckBO.getWorkForce()) > 0) {
                return false;
            }
        }
        return ObjectUtil.isEmpty(crptCheckBO.getHoldCardMan()) || crptCheckBO.getHoldCardMan() > 0;
    }

    public void exportBefore() {
        RequestContext context = RequestContext.getCurrentInstance();
        int count = this.commService.findCountBySql(buildHqls()[1], this.paramMap);
        if (count == 0) {
            JsfUtil.addErrorMessage("无可导出的数据！");
            return;
        }
        context.execute("getDownloadFileClick();");
    }

    /**
     * 导出操作
     */
    public DefaultStreamedContent export() {
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("showStatus();");
        String excelTitle;
        ExcelExportUtil excelExportUtil;
        excelTitle = "用人单位审核";
        String[][] excelHeaders = new String[][]{{"用人单位审核","","","","","","","","","","","","","","","","报告机构信息","",""},{"所属区域", "单位名称", "社会信用代码", "是否分支机构", "单位地址", "法人", "法人联系电话", "联系人", "联系电话", "行业类别", "经济性质", "企业规模", "职工人数", "接触职业病危害人数", "生产工人数", "审核状态", "体检机构", "联系人", "联系电话"}};
        excelExportUtil = new ExcelExportUtil(excelHeaders, pakExcelExportDataList(executeExportSql(), excelHeaders[1].length));
        excelExportUtil.setFrozenPaneRowsNum(2);
        excelExportUtil.setNeedTitle(false);
        excelExportUtil.setSheetName(excelTitle);
        //合并单元格
        excelExportUtil.addMergeCells(0, 0, 0, 15);
        excelExportUtil.addMergeCells(0, 0, 16, 18);
        Workbook wb = excelExportUtil.exportExcel();
        if (wb != null) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                String fileName = excelTitle + ".xlsx";
                fileName = URLEncoder.encode(fileName, "UTF-8");
                wb.write(baos);
                baos.flush();
                byte[] aa = baos.toByteArray();
                context.execute("hideStatus();");
                return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
            } catch (Exception e) {
                JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            }
        }
        context.execute("hideStatus();");
        return null;
    }

    /**
     * 查询导出数据
     *
     * @return 导出数据
     */
    private List<Object[]> executeExportSql() {
        String sql = "WITH TEMP AS (" +
                "SELECT CRPT_ID, UNITNAME, LINK_MAN, LINK_TEL " +
                "FROM (SELECT I.RID, I.CRPT_ID, U.ORG_NAME AS UNITNAME, U.LINK_MAN, U.LINK_TEL, ROW_NUMBER() OVER (PARTITION BY I.CRPT_ID ORDER BY I.RID DESC ) RN " +
                "      FROM TB_TJ_CRPT_INDEPEND I " +
                "               LEFT JOIN TD_ZW_TJORGINFO U ON I.UNIT_ID = U.ORG_ID " +
                "      WHERE I.BUS_TYPE = 1) " +
                "WHERE RN = 1 " +
                ") SELECT * FROM (" + buildSql(2) + ")AA ORDER BY AA.P21, AA.P1";
        return CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql, this.paramMap));
    }

    private List<ExcelExportObject[]> pakExcelExportDataList(List<Object[]> dataList, int headerSize) {
        if (ObjectUtil.isEmpty(dataList) || headerSize <= 0) {
            return new ArrayList<>();
        }
        List<ExcelExportObject[]> excelExportObjectList = new ArrayList<>();
        for (Object[] data : dataList) {
            ExcelExportObject[] objects = new ExcelExportObject[headerSize];
            data[3] = "0".equals(StringUtils.objectToString(data[3])) ? "否" : "是";
            //电话脱敏
            data[6] = StringUtils.encryptPhone(StringUtils.objectToString(data[6]));
            data[8] = StringUtils.encryptPhone(StringUtils.objectToString(data[8]));
            //审核状态
            boolean ifCityDirect = this.checkLevel3 && "1".equals(StringUtils.objectToString(data[19]));
            boolean ifProvDirect = this.checkLevel2 && "1".equals(StringUtils.objectToString(data[20]));
            String statue = getStateStr(StringUtils.objectToString(data[15]), ifCityDirect, ifProvDirect);

            int index = 0;
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]));
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]));
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]));
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]));
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]));
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(statue, XSSFCellStyle.ALIGN_CENTER);
            index++;
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]));
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index++]), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(StringUtils.objectToString(data[index]), XSSFCellStyle.ALIGN_CENTER);
            excelExportObjectList.add(objects);
        }
        return excelExportObjectList;
    }

    /**
     * <p>方法描述：初始化审核信息</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-01
     **/
    private void initCheckInfo(TbTjCrpt tbTjCrpt,String accountLevel) {
        List<Object[]> obj= crptCheckService.findLastStas(tbTjCrpt.getRid());
        if(!CollectionUtils.isEmpty(obj)){
            Object[] lastStaArr=obj.get(0);
            if("2".equals(accountLevel)){
                this.checkState=Integer.parseInt(lastStaArr[15]==null?"1":lastStaArr[15].toString());
                this.checkRst=lastStaArr[8]==null?defaultAuditAdv:lastStaArr[8].toString();
            }else if("3".equals(accountLevel)){
                this.checkState=Integer.parseInt(lastStaArr[14]==null?"1":lastStaArr[14].toString());
                this.checkRst=lastStaArr[7]==null?defaultAuditAdv:lastStaArr[7].toString();
            }else if("4".equals(accountLevel)){
                this.checkState=Integer.parseInt(lastStaArr[13]==null?"1":lastStaArr[13].toString());
                this.checkRst=lastStaArr[6]==null?defaultAuditAdv:lastStaArr[6].toString();
            }
        }
    }

    /**
     * <p>方法描述：保存</p>
     * @MethodAuthor： yzz
     * @Date：2022-07-29
     **/
    @Override
    public void saveAction() {
        if(tbTjCrpt==null||tbTjCrpt.getRid()==null||StringUtils.isBlank(checkLevel)||StringUtils.isBlank(accountLevel)){
            return;
        }
        boolean flag = !this.crptSaveValidate(true);
        if(StringUtils.isNotBlank(this.checkRst) && this.checkRst.length() > 200){
            JsfUtil.addErrorMessage("审核意见内容超出长度限制！");
            flag = true;
        }
        try {
            if(flag){
                return;
            }
            this.fillCrptParam();
            crptCheckService.saveProcessInfo(tbTjCrpt,checkLevel,accountLevel,this.checkState,this.checkRst);
            this.initShowRelationUnitList();
            this.searchAction();
            JsfUtil.addSuccessMessage("保存成功！");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
     * <p>方法描述：提交</p>
     * @MethodAuthor： yzz
     * @Date：2022-07-29
     **/
    public void submitAction(){
        if(tbTjCrpt==null||tbTjCrpt.getRid()==null||StringUtils.isBlank(checkLevel)||StringUtils.isBlank(accountLevel)){
            return;
        }
        boolean flag = !this.crptSubmitValidate(true);
        if(StringUtils.isBlank(checkRst)){
            JsfUtil.addErrorMessage("审核意见不能为空！");
            flag = true;
        }
        if(StringUtils.isNotBlank(this.checkRst) && this.checkRst.length() > 200){
            JsfUtil.addErrorMessage("审核意见内容超出长度限制！");
            flag = true;
        }
        try {
            if(flag){
                return;
            }
            this.fillCrptParam();
            crptCheckService.submitProcessInfo(tbTjCrpt,checkLevel,accountLevel,platVersion,this.checkState,this.checkRst);
            this.initShowRelationUnitList();
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.update("tabView");
            JsfUtil.addSuccessMessage("提交成功！");
            this.searchAction();
            this.viewInitAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
     * <p>方法描述：撤销 </p>
     * @MethodAuthor： pw 2022/8/1
     **/
    public void cancelAction(){
        if(!this.ifShowCancel || null == this.bgkLastSta){
            return;
        }
        try{
            this.bgkLastSta.setState(this.resetBgkState);
            this.crptCheckService.cancelCrptCheck(this.bgkLastSta, this.bgkFlowState);
            //撤销后需要跳转到编辑页
            this.modView();
            JsfUtil.addSuccessMessage("撤销成功！");
        }catch(Exception e){
            e.printStackTrace();
        }
        this.searchAction();
    }

    /**
     * <p>方法描述：审核结果切换事件</p>
     * @MethodAuthor： yzz
     * @Date：2022-07-29
     **/
    public void changeCheckState(){
        if(null != defaultAuditAdv && null != checkState && 1 == checkState && StringUtils.isBlank(checkRst)){
            checkRst = defaultAuditAdv;
        }
        if(null != defaultAuditAdv && null != checkState && 2 == checkState && defaultAuditAdv.equals(checkRst)){
            checkRst = "";
        }
    }

    /**
     * <p>方法描述：审核跳转</p>
     * @MethodAuthor： yzz
     * @Date：2022-07-29
     **/
    public void modView(){
        if(this.rid==null){
            return;
        }
        //默认选择通过
        this.checkState=1;
        this.checkRst=defaultAuditAdv;
        this.itemDisabled=false;
        this.initCrptInfo();
        //初始化
        initCheckInfo(tbTjCrpt,accountLevel);
        //初始化 有无退回功能
        initItemDisabled(tbTjCrpt.getTsZoneByZoneId());
        //初始化历次审核意见
        historyList=crptCheckService.findHisotryListByCrpt(rid);
        this.crptInitData();
        tipInfoInit();

        this.forwardEditPage();
    }

    /**
     * <p>方法描述：初始化是否禁用退回功能</p>
     * @MethodAuthor： yzz
     * @Date：2022-07-29
     **/
    public void initItemDisabled(TsZone tsZone){
        //省级  省直属
        if("2".equals(accountLevel)){
            if("2".equals(checkLevel)&&StringUtils.isNotBlank(tsZone.getIfProvDirect())&&"1".equals(tsZone.getIfProvDirect())){
                this.itemDisabled=true;
            }
        }else if("3".equals(accountLevel)){
            //市级 市直属  省直属
            if("3".equals(checkLevel)&&StringUtils.isNotBlank(tsZone.getIfCityDirect())&&"1".equals(tsZone.getIfCityDirect())){
                this.itemDisabled=true;
            }else if("2".equals(checkLevel)&&StringUtils.isNotBlank(tsZone.getIfProvDirect())&&"1".equals(tsZone.getIfProvDirect())){
                this.itemDisabled=true;
            }
        }else if("4".equals(accountLevel)){
            //区县级
            this.itemDisabled=true;
        }
    }

    /**
     * <p>方法描述：初始化数据 </p>
     * @MethodAuthor： pw 2022/7/29
     **/
    private void crptInitData(){
        this.showCrptParam();
        this.initShowRelationUnitList();
    }

    /**
     * <p>方法描述：通过rid获取用人单位信息 </p>
     * @MethodAuthor： pw 2022/7/29
     **/
    private void initCrptInfo(){
        this.tbTjCrpt = null == this.rid ? null : this.crptCheckService.find(TbTjCrpt.class, this.rid);
        if(this.tbTjCrpt != null){
            this.initZone = this.tbTjCrpt.getTsZoneByZoneId();
        }
    }

    /**
     * <p>方法描述：填充页面显示数据 </p>
     * @MethodAuthor： pw 2022/7/29
     **/
    private void showCrptParam(){
        this.editAreaZone = null;
        this.editCrptName = null;
        this.editAddress = null;
        this.editEconomy = null;
        this.editIndusType = null;
        this.editCrptSizeId = null;
        this.editPostCard = null;
        this.editCorporateName = null;
        this.editCorporatePhone = null;
        this.editLinkMan2 = null;
        this.editLinkPhone2 = null;
        this.editWorkForce = null;
        this.editHoldCardMan = null;
        this.editOutsourceNum = null;
        this.editWorkmanNum = null;
        if(null == this.tbTjCrpt){
            return;
        }
        this.editAreaZone = this.tbTjCrpt.getTsZoneByZoneId();
        this.editCrptName = this.tbTjCrpt.getCrptName();
        this.editAddress = this.tbTjCrpt.getAddress();
        this.editEconomy = this.tbTjCrpt.getTsSimpleCodeByEconomyId();
        this.editIndusType = this.tbTjCrpt.getTsSimpleCodeByIndusTypeId();
        this.editCrptSizeId = null == this.tbTjCrpt.getTsSimpleCodeByCrptSizeId() ? null :
                this.tbTjCrpt.getTsSimpleCodeByCrptSizeId().getRid();
        this.editPostCard = this.tbTjCrpt.getPostCode();
        this.editCorporateName = this.tbTjCrpt.getCorporateDeputy();
        this.editCorporatePhone = this.tbTjCrpt.getPhone();
        this.editLinkMan2 = this.tbTjCrpt.getLinkman2();
        this.editLinkPhone2 = this.tbTjCrpt.getLinkphone2();
        this.editWorkForce = this.tbTjCrpt.getWorkForce();
        this.editHoldCardMan = this.tbTjCrpt.getHoldCardMan();
        this.editOutsourceNum = this.tbTjCrpt.getOutsourceNum();
        this.editWorkmanNum = this.tbTjCrpt.getWorkmanNum();
    }

    /**
     * <p>方法描述：初始化同社会信用代码关联单位 </p>
     * @MethodAuthor： pw 2022/7/29
     **/
    private void initShowRelationUnitList(){
        this.editUpperUnitId = null;
        this.showRelationUnitList = this.crptCheckService.findRelationUnitListByInstitutionCode(null == this.tbTjCrpt ?
                null : this.tbTjCrpt.getInstitutionCode());
        this.processRelationUnitList();
    }

    /**
     * <p>方法描述：二次处理查询出的同社会信用代码关联单位 </p>
     * @MethodAuthor： pw 2022/7/29
     **/
    private void processRelationUnitList(){
        if(CollectionUtils.isEmpty(this.showRelationUnitList)){
            return;
        }
        for(Object[] objArr : this.showRelationUnitList){
            //处理地区
            String fullName = null == objArr[1] ? null : objArr[1].toString();
            if(StringUtils.isNotBlank(fullName)){
                objArr[1] = fullName.substring(fullName.indexOf("_")+1);
            }
        }
    }

    /**
     * <p>方法描述：改为主体机构 </p>
     * @MethodAuthor： pw 2022/7/29
     **/
    public void changeUpperUnit(){
        if(null == this.editUpperUnitId || CollectionUtils.isEmpty(this.showRelationUnitList)){
            return;
        }
        List<Integer> subOrgRidList = new ArrayList<>();
        for(Object[] objArr : this.showRelationUnitList){
            Integer curRid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            if(null == curRid){
                continue;
            }
            if(curRid.intValue() != this.editUpperUnitId){
                subOrgRidList.add(curRid);
            }
        }
        try{
            this.crptCheckService.changeUpperUnit(this.editUpperUnitId, subOrgRidList);
            //重新查询当前用人单位
            this.initCrptInfo();
            //重新查询同社会信用代码关联单位
            this.initShowRelationUnitList();
            JsfUtil.addSuccessMessage("操作成功！");
        }catch(Exception e){
            e.printStackTrace();
        }
    }

    /**
     * <p>方法描述：暂存提交前页面信息点填充到用人单位对象 </p>
     * @MethodAuthor： pw 2022/7/29
     **/
    private void fillCrptParam(){
        if(null == this.tbTjCrpt){
            return;
        }
        this.tbTjCrpt.setTsZoneByZoneId(this.editAreaZone);
        this.tbTjCrpt.setCrptName(this.editCrptName);
        this.tbTjCrpt.setAddress(this.editAddress);
        this.tbTjCrpt.setTsSimpleCodeByEconomyId(this.editEconomy);
        this.tbTjCrpt.setTsSimpleCodeByIndusTypeId(this.editIndusType);
        this.tbTjCrpt.setTsSimpleCodeByCrptSizeId(null == this.editCrptSizeId ? null : new TsSimpleCode(this.editCrptSizeId));
        this.tbTjCrpt.setPostCode(this.editPostCard);
        this.tbTjCrpt.setCorporateDeputy(this.editCorporateName);
        this.tbTjCrpt.setPhone(this.editCorporatePhone);
        this.tbTjCrpt.setLinkman2(this.editLinkMan2);
        this.tbTjCrpt.setLinkphone2(this.editLinkPhone2);
        this.tbTjCrpt.setWorkForce(this.editWorkForce);
        this.tbTjCrpt.setHoldCardMan(this.editHoldCardMan);
        this.tbTjCrpt.setOutsourceNum(this.editOutsourceNum);
        this.tbTjCrpt.setWorkmanNum(this.editWorkmanNum);
    }

    /**
     * <p>方法描述：用人单位暂存验证 </p>
     * @MethodAuthor： pw 2022/7/29
     **/
    private boolean crptSaveValidate(boolean ifValidZoneType){
        boolean flag = true;
        List<String> errMsgList = new ArrayList<>(20);
        if(ifValidZoneType){
            Integer areaZoneType = null == this.editAreaZone || null == this.editAreaZone.getRealZoneType() ? null :
                    this.editAreaZone.getRealZoneType().intValue();
            if (null != areaZoneType && areaZoneType.intValue()<5) {
                errMsgList.add("所属地区必须选择至街道！");
            }
        }
        //单位名称必填 因要保证单位名称与社会信用代码唯一 单位名称不可能为空所以只要为空就是被清空了
        if(StringUtils.isBlank(this.editCrptName)){
            errMsgList.add("单位名称有值不能清空！");
        }
        //用人单位名称与社会信用代码数据库唯一
        String validateInstitutionCode = this.tbTjCrpt.getInstitutionCode();
        if(StringUtils.isNotBlank(this.editCrptName) && StringUtils.isNotBlank(validateInstitutionCode)){
            this.editCrptName = this.editCrptName.trim();
            if(this.crptCheckService.checkIfNameAndInstitutionCodeExist(this.editCrptName,validateInstitutionCode,this.rid)){
                errMsgList.add("同社会信用代码下单位名称重复！");
            }
        }
        // 单位地址 有值不能清空
        if(StringUtils.isBlank(this.editAddress) && StringUtils.isNotBlank(this.tbTjCrpt.getAddress())){
            errMsgList.add("单位地址有值不能清空！");
        }
        //经济类型如果存在 是否选择最末级
        Integer validateEnconomyId = null == this.editEconomy ? null : this.editEconomy.getRid();
        if(null != validateEnconomyId && !this.validateEconomyIds.contains(validateEnconomyId)){
            errMsgList.add("经济类型请选择到最末级！");
        }
        //行业类别如果存在 是否选择最末级
        Integer validateIndusTypeId = null == this.editIndusType ? null : this.editIndusType.getRid();
        if(null != validateIndusTypeId && !this.validateIndusTypeIds.contains(validateIndusTypeId)){
            errMsgList.add("行业类别请选择到最末级！");
        }
        //企业规模有值不能清空
        if(null == this.editCrptSizeId && null != this.tbTjCrpt.getTsSimpleCodeByCrptSizeId()){
            errMsgList.add("企业规模有值不能清空！");
        }
        //邮政编码如果存在 格式是否通过验证
        if(StringUtils.isNotBlank(this.editPostCard) && !StringUtils.vertyPost(this.editPostCard)){
            errMsgList.add("邮政编码格式不正确！");
        }
        //邮政编码有值不能清空
        if(StringUtils.isBlank(this.editPostCard) && StringUtils.isNotBlank(this.tbTjCrpt.getPostCode())){
            errMsgList.add("邮政编码有值不能清空！");
        }
        //法人有值不能清空
        if(StringUtils.isBlank(this.editCorporateName) && StringUtils.isNotBlank(this.tbTjCrpt.getCorporateDeputy())){
            errMsgList.add("法人有值不能清空！");
        }
        //法人联系电话如果存在 格式是否通过验证
        if(StringUtils.isNotBlank(this.editCorporatePhone) && !StringUtils.vertyPhone(this.editCorporatePhone)){
            errMsgList.add("法人联系电话格式不正确！");
        }
        //法人联系电话有值不能清空
        if(StringUtils.isBlank(this.editCorporatePhone) && StringUtils.isNotBlank(this.tbTjCrpt.getPhone())){
            errMsgList.add("法人联系电话有值不能清空！");
        }
        //联系人有值不能清空
        if(StringUtils.isBlank(this.editLinkMan2) && StringUtils.isNotBlank(this.tbTjCrpt.getLinkman2())){
            errMsgList.add("联系人有值不能清空！");
        }
        //联系人电话如果存在 格式是否通过验证
        if(StringUtils.isNotBlank(this.editLinkPhone2) && !StringUtils.vertyPhone(this.editLinkPhone2)){
            errMsgList.add("联系人电话格式不正确！");
        }
        //联系人电话有值不能清空
        if(StringUtils.isBlank(this.editLinkPhone2) && StringUtils.isNotBlank(this.tbTjCrpt.getLinkphone2())){
            errMsgList.add("联系人电话有值不能清空！");
        }
        if(null != this.editWorkForce){
            //职工人数大于0
            if(this.editWorkForce <= 0){
                errMsgList.add("职工人数应大于0！");
            }
            //条件验证 接触职业病危害因素人数必须小于等于职工人数
            if(null != this.editHoldCardMan && this.editHoldCardMan.compareTo(this.editWorkForce) > 0){
                errMsgList.add("接触职业病危害因素人数必须小于等于职工人数！");
            }
            //条件验证 生产工人数必须小于等于职工人数
            if(null != this.editWorkmanNum && this.editWorkmanNum.compareTo(this.editWorkForce) > 0){
                errMsgList.add("生产工人数应小于等于职工人数！");
            }
            //条件验证 外委人员数应小于等于职工人数
            if(null != this.editOutsourceNum && this.editOutsourceNum.compareTo(this.editWorkForce) > 0){
                errMsgList.add("外委人员数应小于等于职工人数！");
            }
        }
        //接触职业病危害因素人数大于0
        if(editHoldCardMan!=null&&this.editHoldCardMan <= 0){
            errMsgList.add("接触职业病危害因素人数应大于0！");
        }
        //职工人数有值不能清空
        if(null == this.editWorkForce && null != this.tbTjCrpt.getWorkForce()){
            errMsgList.add("职工人数有值不能清空！");
        }
        //接触职业病危害因素人数有值不能清空
        if(null == this.editHoldCardMan && null != this.tbTjCrpt.getHoldCardMan()){
            errMsgList.add("接触职业病危害因素人数有值不能清空！");
        }
        //生产工人数有值不能清空
        if(null == this.editWorkmanNum && null != this.tbTjCrpt.getWorkmanNum()){
            errMsgList.add("生产工人数有值不能清空！");
        }
        //外委人员数有值不能清空
        if(null == this.editOutsourceNum && null != this.tbTjCrpt.getOutsourceNum()){
            errMsgList.add("外委人员数有值不能清空！");
            flag = false;
        }
        if(!CollectionUtils.isEmpty(errMsgList)){
            flag = false;
            for(String errMsg : errMsgList){
                JsfUtil.addErrorMessage(errMsg);
            }
        }
        return flag;
    }

    /**
     * <p>方法描述：用人单位提交验证 </p>
     * @MethodAuthor： pw 2022/7/29
     **/
    private boolean crptSubmitValidate(boolean ifValidZoneType){
        boolean flag = this.crptSaveValidate(ifValidZoneType);
        if(null == this.editAreaZone || null == this.editAreaZone.getRid()){
            JsfUtil.addErrorMessage("所属地区不允许为空！");
            flag = false;
        }
        //查询条件中已经加入了经济类型 企业规模以及行业类别不为空的条件
        //经济类型以及行业类别没有清空操作 不需要考虑
        //企业规模虽然可以清空 但在保存验证中验证有值不能清空 这里不需要重复企业规模的验证
        return flag;
    }

    /**
     * <p>方法描述：选择经济类型/行业类别 </p>
     * @MethodAuthor： pw 2022/7/30
     **/
    public void selCodeTypeAction(){
        if("行业类别".equals(this.selCodeName)) {
            this.codePanelBean.partInit();
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("tabView:editForm:selectedIndusCodeTable");
            dataTable.setFirst(0);
            dataTable.setRows(10);
            return;
        }
        this.displayList = new ArrayList<TsSimpleCode>();
        this.allList = new ArrayList<TsSimpleCode>();
        this.firstList = new ArrayList<TsSimpleCode>();
        this.searchNamOrPy = null;
        this.firstCodeNo = null;
        if("经济类型".equals(this.selCodeName)) {
            this.allList.addAll(this.econList);
        }
        dealCodelevel(this.allList);
        if(null != this.allList && this.allList.size() > 0) {
            this.displayList.addAll(this.allList);
        }
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:selectedIndusTable");
        dataTable.setFirst(0);
    }

    /**
     * <p>方法描述：经济类型查询 </p>
     * @MethodAuthor： pw 2022/7/30
     **/
    public void searchCodeAction() {
        this.displayList = new ArrayList<TsSimpleCode>();
        List<TsSimpleCode> list = new ArrayList<TsSimpleCode>();
        if(null != this.allList && this.allList.size() > 0 ) {
            if(StringUtils.isNotBlank(this.firstCodeNo)) {
                for(TsSimpleCode t : this.allList) {
                    if (t.getCodeLevelNo().startsWith(this.firstCodeNo)) {
                        list.add(t);
                    }
                }
                if(StringUtils.isNotBlank(this.searchNamOrPy)){
                    for(TsSimpleCode t :list) {
                        String codeName = t.getCodeName()==null?"":t.getCodeName();
                        String codePym = t.getSplsht()==null?"":t.getSplsht();
                        //如果模糊匹配上，则增加
                        if (codeName.indexOf(this.searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(this.searchNamOrPy.toUpperCase()) != -1) {
                            this.displayList.add(t);
                        }
                    }
                } else {
                    this.displayList.addAll(list);
                }
            } else if(StringUtils.isNotBlank(this.searchNamOrPy)) {
                for(TsSimpleCode t : this.allList) {
                    String codeName = t.getCodeName()==null?"":t.getCodeName();
                    String codePym = t.getSplsht()==null?"":t.getSplsht();
                    //如果模糊匹配上，则增加
                    if (codeName.indexOf(this.searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(this.searchNamOrPy.toUpperCase()) != -1) {
                        this.displayList.add(t);
                    }
                }
            } else {
                this.displayList.addAll(this.allList);
            }
        }
    }

    /**
     * <p>方法描述：经济类型选择 </p>
     * @MethodAuthor： pw 2022/7/30
     **/
    public void selectAction(){
        this.editEconomy = this.selectPro;
        RequestContext.getCurrentInstance().update("tabView:editForm:economyName");
        RequestContext.getCurrentInstance().execute("PF('selDialog').hide()");
    }

    /**
     * <p>方法描述：行业类别选择 </p>
     * @MethodAuthor： pw 2022/7/30
     **/
    public void selectIndusTypeAction(){
        this.editIndusType = this.selectPro;
        RequestContext.getCurrentInstance().update("tabView:editForm:indusTypeName");
        RequestContext.getCurrentInstance().execute("PF('selIndusTypeDialog').hide()");
    }

    /**
     * <p>方法描述：处理码表层级关系 </p>
     * @MethodAuthor： pw 2022/7/30
     **/
    private void dealCodelevel(List<TsSimpleCode> list) {
        if(list != null && list.size() > 0) {
            for(TsSimpleCode code : list) {
                code.setLevelIndex(StringUtils.countMatches(
                        code.getCodeLevelNo(), ".")
                        + "");
                if(StringUtils.containsNone(code.getCodeLevelNo(), ".")) {
                    this.firstList.add(code);
                }
            }
        }
    }

    /**
     * <p>方法描述：查找经济性质与行业类别最后一级的rid集合 </p>
     * @MethodAuthor： pw 2022/7/30
     **/
    private List<Integer> ridListByList(List<TsSimpleCode> resList) {
        List<Integer> resultList = new ArrayList<>();
        if (resList != null && resList.size() > 0) {
            Map<String, List<TsSimpleCode>> levelMap = new HashMap<>();
            for (TsSimpleCode obj : resList) {
                String codeLevelNo = obj.getCodeLevelNo();
                if (StringUtils.isBlank(codeLevelNo)) {
                    continue;
                }
                levelMap.put(codeLevelNo, new ArrayList<TsSimpleCode>());
                if (StringUtils.contains(codeLevelNo, ".")) {
                    String[] split = codeLevelNo.split("\\.");
                    StringBuffer parentCodeSb = new StringBuffer();
                    for (int i = 0; i < split.length-1; i++) {//仅找出父级
                        parentCodeSb.append(".").append(split[i]);
                        String parentCode = parentCodeSb.substring(1);
                        List<TsSimpleCode> childs = levelMap.get(parentCode);
                        if (null==childs) {
                            childs = new ArrayList<TsSimpleCode>();
                            levelMap.put(parentCode, childs);
                        }
                        childs.add(obj);
                    }
                }
            }
            for (TsSimpleCode t : resList) {
                t.setLevelIndex(StringUtils.countMatches(t.getCodeLevelNo(), ".")+ "");
                // 默认不可选择
                t.setIfSelected(false);
                List<TsSimpleCode> childs = levelMap.get(t.getCodeLevelNo());
                if (CollectionUtils.isEmpty(childs)) {
                    resultList.add(t.getRid());
                    // 最末级支持选择
                    t.setIfSelected(true);
                }
            }
        }
        return resultList;
    }

    /**
     * <p>方法描述：初始化 是否显示撤销按钮 </p>
     * @MethodAuthor： pw 2022/8/2
     **/
    private void initIfShowCancel(TsZone tsZone){
        this.ifShowCancel = false;
        this.bgkFlowState = null;
        this.resetBgkState = null;
        //查询数据库是否已存数据
        String hql = "select T from TdZwBgkLastSta T where T.cartType=10 and T.busId="+this.rid;
        this.bgkLastSta = this.crptCheckService.findOneByHql(hql,TdZwBgkLastSta.class);
        if(null == this.bgkLastSta){
            return;
        }
        Integer bgkSta = this.bgkLastSta.getState();
        //非审核通过的状态 不可以撤回
        if(null == bgkSta || (1 != bgkSta && 3 != bgkSta && 5 != bgkSta && 7 != bgkSta)){
            return;
        }
        if("2".equals(checkLevel)){
            //省直属
            if(StringUtils.isNotBlank(tsZone.getIfProvDirect())
                    &&"1".equals(tsZone.getIfProvDirect())){
                if(StringUtils.isNotBlank(this.accountLevel)&&"3".equals(this.accountLevel)){
                    //市级平台 审核通过
                    if(5 == bgkSta){
                        this.ifShowCancel = true;
                    }
                    this.bgkFlowState = 33;
                    //二级审核 市级平台撤销 状态调整成3
                    this.resetBgkState = 3;
                }else if(StringUtils.isNotBlank(this.accountLevel)&&"2".equals(this.accountLevel)){
                    //省级平台 审核通过
                    if(7 == bgkSta){
                        this.ifShowCancel = true;
                    }
                    this.bgkFlowState = 42;
                    //二级审核 省级平台撤销 状态调整成5
                    this.resetBgkState = 5;
                }
            }else{
                //非省直属
                if(StringUtils.isNotBlank(this.accountLevel)&&"4".equals(this.accountLevel)){
                    if(StringUtils.isNotBlank(platVersion)&&"1".equals(platVersion)){
                        //市级平台 区县通过 市级未审
                        if(3 == bgkSta && null == this.bgkLastSta.getCityRst()){
                            this.ifShowCancel = true;
                        }
                        //状态调整成1
                        this.resetBgkState = 1;
                        this.bgkFlowState = 31;
                    }else if(StringUtils.isNotBlank(platVersion)&&"2".equals(platVersion)){
                        //省级平台 区县通过 省级未审
                        if(5 == bgkSta && null == this.bgkLastSta.getProRst()){
                            this.ifShowCancel = true;
                        }
                        //状态调整成1
                        this.resetBgkState = 1;
                        this.bgkFlowState = 43;
                    }
                }else if(StringUtils.isNotBlank(this.accountLevel)&&"3".equals(this.accountLevel)){
                    //市级平台 审核通过
                    if(5 == bgkSta){
                        this.ifShowCancel = true;
                    }
                    //状态调整成3
                    this.resetBgkState = 3;
                    this.bgkFlowState = 33;
                }else if(StringUtils.isNotBlank(this.accountLevel)&&"2".equals(this.accountLevel)){
                    //省级通过
                    if(7 == bgkSta){
                        this.ifShowCancel = true;
                    }
                    //状态调整成5
                    this.resetBgkState = 5;
                    this.bgkFlowState = 42;
                }
            }
        }else if("3".equals(checkLevel)){
            //市直属
            if(StringUtils.isNotBlank(tsZone.getIfCityDirect())
                    &&"1".equals(tsZone.getIfCityDirect())){
                //审核机构市级
                if(StringUtils.isNotBlank(this.accountLevel)&&"3".equals(this.accountLevel)){
                    //市级通过 省级未审
                    if(5 == bgkSta && null == this.bgkLastSta.getProRst()){
                        this.ifShowCancel = true;
                    }
                    // 状态调整成3
                    this.resetBgkState = 3;
                    this.bgkFlowState = 41;
                }else if(StringUtils.isNotBlank(this.accountLevel)&&"2".equals(this.accountLevel)){
                    //省级通过
                    if(7 == bgkSta){
                        this.ifShowCancel = true;
                    }
                    //状态调整成5
                    this.resetBgkState = 5;
                    this.bgkFlowState = 42;
                }
            }else{
                //非市直属  区县
                if(StringUtils.isNotBlank(this.accountLevel)&&"4".equals(this.accountLevel)){
                    //区县通过 市级未审
                    if(3 == bgkSta && null == this.bgkLastSta.getCityRst()){
                        this.ifShowCancel = true;
                    }
                    //状态调整成1
                    this.resetBgkState = 1;
                    this.bgkFlowState = 31;
                }else if(StringUtils.isNotBlank(this.accountLevel)&&"3".equals(this.accountLevel)){
                    //市级通过 省级未审
                    if(5 == bgkSta && null == this.bgkLastSta.getProRst()){
                        this.ifShowCancel = true;
                    }
                    //状态调整成3
                    this.resetBgkState = 3;
                    this.bgkFlowState = 41;
                }else if(StringUtils.isNotBlank(this.accountLevel)&&"2".equals(this.accountLevel)){
                    //省级通过
                    if(7 == bgkSta){
                        this.ifShowCancel = true;
                    }
                    //状态调整成5
                    this.resetBgkState = 5;
                    this.bgkFlowState = 42;
                }
            }
        }
    }

    /**
     * <p>描述 地区变更</p>
     *
     * @MethodAuthor gongzhe,2022/8/2 14:32,beforeChangeZoneAction
     * @return void
     */
    public void beforeChangeZoneAction(){
        this.changeZoneCode = this.editAreaZone.getZoneGb();
        this.changeZoneName = this.editAreaZone.getZoneName();
        this.changeZoneId = this.editAreaZone.getRid();
        this.changeZoneType = new Integer(this.editAreaZone.getRealZoneType());
        RequestContext.getCurrentInstance().update("tabView:editForm:changeZonePanel");
        RequestContext.getCurrentInstance().execute("PF('changeZoneDialog').show()");
    }

    /**
     * <p>描述 确认变更地区</p>
     *
     * @MethodAuthor gongzhe,2022/8/3 8:47,confirmZoneChange
     * @return void
     */
    public void confirmZoneChange(){
        if (null == this.changeZoneType ||  this.changeZoneType.intValue()<5) {
            JsfUtil.addErrorMessage("变更地区必须选择至街道！");
            return;
        }
        try{
            //管辖地区
            TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
            TsZone changeZone = commService.find(TsZone.class,this.changeZoneId);
            this.tbTjCrpt.setTsZoneByZoneId(changeZone);
            if(!ifCrossZone(tsZone,changeZone,this.editAreaZone)){
                //不跨辖区
                this.editAreaZone = changeZone;
                RequestContext.getCurrentInstance().update("tabView:editForm:checkGrid");
                RequestContext.getCurrentInstance().update("tabView:editForm:editAreaZoneLabel");
                RequestContext.getCurrentInstance().execute("PF('changeZoneDialog').hide()");
            }else{
                //跨辖区
                String rst = "地区变更："+(initZone !=null ? initZone.getFullName():"")+" --> "+changeZone.getFullName();
                this.crptCheckService.saveChangeZone(this.tbTjCrpt,checkLevel,platVersion,accountLevel,1,rst);
                JsfUtil.addSuccessMessage("变更地区成功！");
                RequestContext.getCurrentInstance().execute("PF('changeZoneDialog').hide()");
                RequestContext.getCurrentInstance().update("tabView");
                this.searchAction();
                this.backAction();
            }
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("变更地区失败！");
        }
    }

    /**
     * <p>描述 是否跨辖区以及是否能退回</p>
     *
     * @param managedZone
     * @param changeZone
     * @param oldZone
     * @MethodAuthor gongzhe,2022/8/8 14:46,ifCrossZone
     * @return boolean
     */
    private boolean ifCrossZone(TsZone managedZone,TsZone changeZone,TsZone oldZone){
        boolean cross = false;
        if(ZoneUtil.zoneSelect(managedZone.getZoneGb()).startsWith(ZoneUtil.zoneSelect(this.changeZoneCode))
                || ZoneUtil.zoneSelect(this.changeZoneCode).startsWith(ZoneUtil.zoneSelect(managedZone.getZoneGb()))){
            //同级下
            if("3".equals(checkLevel)){
                if("4".equals(accountLevel)){
                    //当前是区级 非市直属-->市直属 走跨辖区流程
                    if(!"1".equals(oldZone.getIfCityDirect()) && "1".equals(changeZone.getIfCityDirect())){
                        cross = true;
                    }
                }else if("3".equals(accountLevel)){
                    //当前是市级 非市直属-->市直属 不能退回
                    if(!"1".equals(oldZone.getIfCityDirect()) && "1".equals(changeZone.getIfCityDirect())){
                        this.itemDisabled = true;
                    }
                    // 市直属-->非市直属 能退回
                    if("1".equals(oldZone.getIfCityDirect()) && !"1".equals(changeZone.getIfCityDirect())){
                        this.itemDisabled = false;
                    }
                }
            }else if("2".equals(checkLevel)){
                if("1".equals(platVersion)){
                    //市级平台
                    if("4".equals(accountLevel)){
                        //当前是区级 非省直属-->省直属 走跨辖区流程
                        if(!"1".equals(oldZone.getIfProvDirect()) && "1".equals(changeZone.getIfProvDirect())){
                            cross = true;
                        }
                    }else if("3".equals(accountLevel)){
                        //当前是市级 非省直属-->省直属 不能退回
                        if(!"1".equals(oldZone.getIfProvDirect()) && "1".equals(changeZone.getIfProvDirect())){
                            this.itemDisabled = true;
                        }
                        // 省直属-->非省直属 能退回
                        if("1".equals(oldZone.getIfProvDirect()) && !"1".equals(changeZone.getIfProvDirect())){
                            this.itemDisabled = false;
                        }
                    }
                }else if("2".equals(platVersion)){
                    //省平台
                    if("4".equals(accountLevel)){
                        //当前是区级 非省直属-->省直属 走跨辖区流程
                        if(!"1".equals(oldZone.getIfProvDirect()) && "1".equals(changeZone.getIfProvDirect())){
                            cross = true;
                        }
                    }else if("2".equals(accountLevel)){
                        //当前是省级 非省直属-->省直属 不能退回
                        if(!"1".equals(oldZone.getIfProvDirect()) && "1".equals(changeZone.getIfProvDirect())){
                            this.itemDisabled = true;
                        }
                        // 省直属-->非省直属 能退回
                        if("1".equals(oldZone.getIfProvDirect()) && !"1".equals(changeZone.getIfProvDirect())){
                            this.itemDisabled = false;
                        }
                    }
                }
            }
        }else{
            cross = true;
        }
        return cross;
    }

    public List<Object[]> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<Object[]> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchInstitutionCode() {
        return searchInstitutionCode;
    }

    public void setSearchInstitutionCode(String searchInstitutionCode) {
        this.searchInstitutionCode = searchInstitutionCode;
    }

    public String getSelectIndusTypeIds() {
        return selectIndusTypeIds;
    }

    public void setSelectIndusTypeIds(String selectIndusTypeIds) {
        this.selectIndusTypeIds = selectIndusTypeIds;
    }

    public String getSelectIndusTypeNames() {
        return selectIndusTypeNames;
    }

    public void setSelectIndusTypeNames(String selectIndusTypeNames) {
        this.selectIndusTypeNames = selectIndusTypeNames;
    }

    public String getSelectEconomyIds() {
        return selectEconomyIds;
    }

    public void setSelectEconomyIds(String selectEconomyIds) {
        this.selectEconomyIds = selectEconomyIds;
    }

    public String getSelectEconomyNames() {
        return selectEconomyNames;
    }

    public void setSelectEconomyNames(String selectEconomyNames) {
        this.selectEconomyNames = selectEconomyNames;
    }

    public String getSelectCrptSizeIds() {
        return selectCrptSizeIds;
    }

    public void setSelectCrptSizeIds(String selectCrptSizeIds) {
        this.selectCrptSizeIds = selectCrptSizeIds;
    }

    public List<TsSimpleCode> getCrptSizeList() {
        return crptSizeList;
    }

    public void setCrptSizeList(List<TsSimpleCode> crptSizeList) {
        this.crptSizeList = crptSizeList;
    }

    public String getSimpleCodeOpType() {
        return simpleCodeOpType;
    }

    public void setSimpleCodeOpType(String simpleCodeOpType) {
        this.simpleCodeOpType = simpleCodeOpType;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public List<String> getDefStateList() {
        return defStateList;
    }

    public void setDefStateList(List<String> defStateList) {
        this.defStateList = defStateList;
    }

    public List<String> getDefStateStrList() {
        return defStateStrList;
    }

    public void setDefStateStrList(List<String> defStateStrList) {
        this.defStateStrList = defStateStrList;
    }

    public String getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(String checkLevel) {
        this.checkLevel = checkLevel;
    }

    public boolean isCheckLevel2() {
        return checkLevel2;
    }

    public void setCheckLevel2(boolean checkLevel2) {
        this.checkLevel2 = checkLevel2;
    }

    public boolean isCheckLevel3() {
        return checkLevel3;
    }

    public void setCheckLevel3(boolean checkLevel3) {
        this.checkLevel3 = checkLevel3;
    }

    public String getPlatVersion() {
        return platVersion;
    }

    public void setPlatVersion(String platVersion) {
        this.platVersion = platVersion;
    }

    public boolean isPlatVersionCity() {
        return platVersionCity;
    }

    public void setPlatVersionCity(boolean platVersionCity) {
        this.platVersionCity = platVersionCity;
    }

    public boolean isPlatVersionProv() {
        return platVersionProv;
    }

    public void setPlatVersionProv(boolean platVersionProv) {
        this.platVersionProv = platVersionProv;
    }

    public String getAccountLevel() {
        return accountLevel;
    }

    public void setAccountLevel(String accountLevel) {
        this.accountLevel = accountLevel;
    }

    public Integer getCheckOpType() {
        return checkOpType;
    }

    public void setCheckOpType(Integer checkOpType) {
        this.checkOpType = checkOpType;
    }

    public String getTipInfo() {
        return tipInfo;
    }

    public void setTipInfo(String tipInfo) {
        this.tipInfo = tipInfo;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Integer getCheckState() {
        return checkState;
    }

    public void setCheckState(Integer checkState) {
        this.checkState = checkState;
    }

    public String getCheckRst() {
        return checkRst;
    }

    public void setCheckRst(String checkRst) {
        this.checkRst = checkRst;
    }

    public List<Object[]> getHistoryList() {
        return historyList;
    }

    public void setHistoryList(List<Object[]> historyList) {
        this.historyList = historyList;
    }

    public TbTjCrpt getTbTjCrpt() {
        return tbTjCrpt;
    }

    public void setTbTjCrpt(TbTjCrpt tbTjCrpt) {
        this.tbTjCrpt = tbTjCrpt;
    }

    public String getDefaultAuditAdv() {
        return defaultAuditAdv;
    }

    public void setDefaultAuditAdv(String defaultAuditAdv) {
        this.defaultAuditAdv = defaultAuditAdv;
    }

    public boolean isItemDisabled() {
        return itemDisabled;
    }

    public void setItemDisabled(boolean itemDisabled) {
        this.itemDisabled = itemDisabled;
    }

    public TsZone getEditAreaZone() {
        return editAreaZone;
    }

    public void setEditAreaZone(TsZone editAreaZone) {
        this.editAreaZone = editAreaZone;
    }

    public String getEditCrptName() {
        return editCrptName;
    }

    public void setEditCrptName(String editCrptName) {
        this.editCrptName = editCrptName;
    }

    public String getEditAddress() {
        return editAddress;
    }

    public void setEditAddress(String editAddress) {
        this.editAddress = editAddress;
    }

    public TsSimpleCode getEditEconomy() {
        return editEconomy;
    }

    public void setEditEconomy(TsSimpleCode editEconomy) {
        this.editEconomy = editEconomy;
    }

    public Integer getEditCrptSizeId() {
        return editCrptSizeId;
    }

    public void setEditCrptSizeId(Integer editCrptSizeId) {
        this.editCrptSizeId = editCrptSizeId;
    }

    public TsSimpleCode getEditIndusType() {
        return editIndusType;
    }

    public void setEditIndusType(TsSimpleCode editIndusType) {
        this.editIndusType = editIndusType;
    }

    public String getEditPostCard() {
        return editPostCard;
    }

    public void setEditPostCard(String editPostCard) {
        this.editPostCard = editPostCard;
    }

    public String getEditCorporateName() {
        return editCorporateName;
    }

    public void setEditCorporateName(String editCorporateName) {
        this.editCorporateName = editCorporateName;
    }

    public String getEditCorporatePhone() {
        return editCorporatePhone;
    }

    public void setEditCorporatePhone(String editCorporatePhone) {
        this.editCorporatePhone = editCorporatePhone;
    }

    public String getEditLinkMan2() {
        return editLinkMan2;
    }

    public void setEditLinkMan2(String editLinkMan2) {
        this.editLinkMan2 = editLinkMan2;
    }

    public String getEditLinkPhone2() {
        return editLinkPhone2;
    }

    public void setEditLinkPhone2(String editLinkPhone2) {
        this.editLinkPhone2 = editLinkPhone2;
    }

    public Integer getEditWorkForce() {
        return editWorkForce;
    }

    public void setEditWorkForce(Integer editWorkForce) {
        this.editWorkForce = editWorkForce;
    }

    public Integer getEditHoldCardMan() {
        return editHoldCardMan;
    }

    public void setEditHoldCardMan(Integer editHoldCardMan) {
        this.editHoldCardMan = editHoldCardMan;
    }

    public Integer getEditOutsourceNum() {
        return editOutsourceNum;
    }

    public void setEditOutsourceNum(Integer editOutsourceNum) {
        this.editOutsourceNum = editOutsourceNum;
    }

    public Integer getEditWorkmanNum() {
        return editWorkmanNum;
    }

    public void setEditWorkmanNum(Integer editWorkmanNum) {
        this.editWorkmanNum = editWorkmanNum;
    }

    public List<Object[]> getShowRelationUnitList() {
        return showRelationUnitList;
    }

    public void setShowRelationUnitList(List<Object[]> showRelationUnitList) {
        this.showRelationUnitList = showRelationUnitList;
    }

    public Integer getEditUpperUnitId() {
        return editUpperUnitId;
    }

    public void setEditUpperUnitId(Integer editUpperUnitId) {
        this.editUpperUnitId = editUpperUnitId;
    }

    public List<TsSimpleCode> getEconList() {
        return econList;
    }

    public void setEconList(List<TsSimpleCode> econList) {
        this.econList = econList;
    }

    public List<TsSimpleCode> getIndusList() {
        return indusList;
    }

    public void setIndusList(List<TsSimpleCode> indusList) {
        this.indusList = indusList;
    }

    public List<Integer> getValidateEconomyIds() {
        return validateEconomyIds;
    }

    public void setValidateEconomyIds(List<Integer> validateEconomyIds) {
        this.validateEconomyIds = validateEconomyIds;
    }

    public List<Integer> getValidateIndusTypeIds() {
        return validateIndusTypeIds;
    }

    public void setValidateIndusTypeIds(List<Integer> validateIndusTypeIds) {
        this.validateIndusTypeIds = validateIndusTypeIds;
    }

    public IndusTypeCodePanelBean getCodePanelBean() {
        return codePanelBean;
    }

    public void setCodePanelBean(IndusTypeCodePanelBean codePanelBean) {
        this.codePanelBean = codePanelBean;
    }

    public String getSelCodeName() {
        return selCodeName;
    }

    public void setSelCodeName(String selCodeName) {
        this.selCodeName = selCodeName;
    }

    public String getFirstCodeNo() {
        return firstCodeNo;
    }

    public void setFirstCodeNo(String firstCodeNo) {
        this.firstCodeNo = firstCodeNo;
    }

    public List<TsSimpleCode> getFirstList() {
        return firstList;
    }

    public void setFirstList(List<TsSimpleCode> firstList) {
        this.firstList = firstList;
    }

    public List<TsSimpleCode> getAllList() {
        return allList;
    }

    public void setAllList(List<TsSimpleCode> allList) {
        this.allList = allList;
    }

    public List<TsSimpleCode> getDisplayList() {
        return displayList;
    }

    public void setDisplayList(List<TsSimpleCode> displayList) {
        this.displayList = displayList;
    }

    public String getSearchNamOrPy() {
        return searchNamOrPy;
    }

    public void setSearchNamOrPy(String searchNamOrPy) {
        this.searchNamOrPy = searchNamOrPy;
    }

    public TsSimpleCode getSelectPro() {
        return selectPro;
    }

    public void setSelectPro(TsSimpleCode selectPro) {
        this.selectPro = selectPro;
    }

    public boolean isIfShowCancel() {
        return ifShowCancel;
    }

    public void setIfShowCancel(boolean ifShowCancel) {
        this.ifShowCancel = ifShowCancel;
    }

    public TdZwBgkLastSta getBgkLastSta() {
        return bgkLastSta;
    }

    public void setBgkLastSta(TdZwBgkLastSta bgkLastSta) {
        this.bgkLastSta = bgkLastSta;
    }

    public Integer getBgkFlowState() {
        return bgkFlowState;
    }

    public void setBgkFlowState(Integer bgkFlowState) {
        this.bgkFlowState = bgkFlowState;
    }

    public Integer getResetBgkState() {
        return resetBgkState;
    }

    public void setResetBgkState(Integer resetBgkState) {
        this.resetBgkState = resetBgkState;
    }

    public List<TsZone> getChangeZoneList() {
        return changeZoneList;
    }

    public void setChangeZoneList(List<TsZone> changeZoneList) {
        this.changeZoneList = changeZoneList;
    }

    public String getChangeZoneCode() {
        return changeZoneCode;
    }

    public void setChangeZoneCode(String changeZoneCode) {
        this.changeZoneCode = changeZoneCode;
    }

    public String getChangeZoneName() {
        return changeZoneName;
    }

    public void setChangeZoneName(String changeZoneName) {
        this.changeZoneName = changeZoneName;
    }

    public Integer getChangeZoneType() {
        return changeZoneType;
    }

    public void setChangeZoneType(Integer changeZoneType) {
        this.changeZoneType = changeZoneType;
    }

    public Integer getChangeZoneId() {
        return changeZoneId;
    }

    public void setChangeZoneId(Integer changeZoneId) {
        this.changeZoneId = changeZoneId;
    }
}
