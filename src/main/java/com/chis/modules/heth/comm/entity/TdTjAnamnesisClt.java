package com.chis.modules.heth.comm.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * @Description : 问诊既往病史表（数据录入）
 * @ClassAuthor: anjing
 * @Date : 2019/5/14 10:14
 **/
@Entity
@Table(name = "TD_TJ_ANAMNESIS_CLT")
@SequenceGenerator(name = "TdTjAnamnesisClt_Seq", sequenceName = "TD_TJ_ANAMNESIS_CLT_SEQ", allocationSize = 1)
public class TdTjAnamnesisClt implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /**主键Id*/
    private Integer rid;
    /**关联：体检主表-数据录入（体检主表Id）*/
    private TdTjBhkClt fkByBhkId;
    /**疾病名称*/
    private String hstnam;
    /**诊断日期*/
    private String hstdat;
    /**诊断单位*/
    private String hstunt;
    /**治疗经过*/
    private String hstcruprc;
    /**转归*/
    private String hstlps;
    /**创建日期*/
    private Date createDate;
    /**创建人*/
    private Integer createManid;
    /**修改日期*/
    private Date modifyDate;
    /**修改人*/
    private Integer modifyManid;

    /*********************** 非实体部分 **********************/
    /**诊断日期*/
    private Date hstDate;

    public TdTjAnamnesisClt() {
    }

    public TdTjAnamnesisClt(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjAnamnesisClt_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "BHK_ID" )
    public TdTjBhkClt getFkByBhkId() {
        return fkByBhkId;
    }

    public void setFkByBhkId(TdTjBhkClt fkByBhkId) {
        this.fkByBhkId = fkByBhkId;
    }

    @Column(name = "HSTNAM" , length = 100)
    public String getHstnam() {
        return hstnam;
    }

    public void setHstnam(String hstnam) {
        this.hstnam = hstnam;
    }

    @Column(name = "HSTDAT", length = 100)
    public String getHstdat() {
        return hstdat;
    }

    public void setHstdat(String hstdat) {
        this.hstdat = hstdat;
    }

    @Column(name = "HSTUNT", length = 100)
    public String getHstunt() {
        return hstunt;
    }

    public void setHstunt(String hstunt) {
        this.hstunt = hstunt;
    }

    @Column(name = "HSTCRUPRC")
    public String getHstcruprc() {
        return hstcruprc;
    }

    public void setHstcruprc(String hstcruprc) {
        this.hstcruprc = hstcruprc;
    }

    @Column(name = "HSTLPS", length = 50)
    public String getHstlps() {
        return hstlps;
    }

    public void setHstlps(String hstlps) {
        this.hstlps = hstlps;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Transient
    public Date getHstDate() {
        return hstDate;
    }

    public void setHstDate(Date hstDate) {
        this.hstDate = hstDate;
    }
}
