package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 职业病诊断受理档案表
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置<br/>
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 *
 * <p>修订内容：新增字段</p>
 * @ClassReviser qrr,2018年4月20日,TdZwOccdiscase
 */
@Entity
@Table(name = "TD_ZW_OCCDISCASE", uniqueConstraints = @UniqueConstraint(columnNames = "ARCH_CODE"))
@SequenceGenerator(name = "TdZwOccdiscase_Seq", sequenceName = "TD_ZW_OCCDISCASE_SEQ", allocationSize = 1)
public class TdZwOccdiscaseComm implements java.io.Serializable {

    private static final long serialVersionUID = -3289252129318130809L;
    private Integer rid;
    private TsSimpleCode tsSimpleCodeByOccdiseApplyid;
    private TsSimpleCode tsSimpleCodeByOccdiseId;
    private TdTjPerson tdTjPerson;
    private TsUnit tsUnit;
    private TbTjCrpt tbTjCrpt;
    private String archCode;//登记编号
    private String archCodeTemp;//登记编号
    private String zdWritNo;//诊断编号
    private String zdWritNoTemp;
    private String personnelName;
    private String idc;
    private String sex;
    private Date birthday;
    private String postcode;
    private String linktel;
    private String address;
    private String crptName;
    private String institutionCode;
    private String diagInfo;
    private String rmk;
    private String acceptPsn;
    private Date acceptDate;
    private Date startDate;
    private Integer startHour;
    private Integer startMint;
    private Date endDate;
    private Integer endHour;
    private Integer endMint;
    private String compere;
    private String atchDiagPersons;
    private String atchDiagPersonIds;
    private String annaler;
    private String diagPlace;
    private Integer isDis;
    private String occLevel;
    private String diagStd;
    private String diagClusion;
    private String dealVote;
    private Integer stateMark;
    private Date updateTime;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private List<TdZwBadrsnHisComm> tdZwBadrsnHises = new ArrayList<TdZwBadrsnHisComm>(
            0);
    /** 是否能修改 */
    private boolean canModfiy;
    //+登记日期
    private Date applyDate;
    private Date diagZmDate;
    private String occdiseName;

    /** 参与人员集合 */
    private List<TdZwOccPsnComm> tdZwOccPsnList = new ArrayList<TdZwOccPsnComm>();

    // Constructors

    /** default constructor */
    /******************************新增字段******************************/
    //+劳动者既往病史20180411
    private String hisDiseases;
    //+是否代理20180418
    private Integer ifProxy;
    //+代理人姓名20180411
    private String proxyPsnName;
    //+代理人关系20180411
    private Integer proxyRelation;
    //+身份证号20180411
    private String proxyIdc;
    //+联系电话20180411
    private String proxyLinktel;
    //+申请诊断病名称20180416
    private String occAppNam;
    //+收集日期20180418
    private Date tgDate;
    //+协助提供材料日期20180418
    private Date xzTgDate;
    //+提供材料日期20180419
    private Date tgMatriDate;
    //+补正日期20180418
    private Date bzDate;
    //+补正材料日期20180419
    private Date bzSjDate;
    //+通知日期20180418
    private Date tcQrDate;
    //+确认日期20180418
    private Date qrDate;
    //+仲裁日期20180418
    private Date czDate;
    //+协助监测日期20180418
    private Date xzjcDate;
    //+诊断中止日期20180418
    private Date zdzzDate;
    //+完成处理日期20180418
    private Date wcClDate;
    //+讨论日期20180418
    private Date tlDate;
    //+延期日期20180418
    private Date yqDate;
    //+诊断日期20180418
    private Date zdDate;
    //+通知签收日期20180418
    private Date tzQsDate;
    //+签收日期20180418
    private Date qsDate;
    //+是否上报职业病报告20180413
    private Integer ifUpZybRpt;
    //+职业病报告上报时间20180413
    private Date upRptDate;
    //+报告类型：1、尘肺报告卡2、职业病报告卡
    private String rptCardType;

    private Integer avoidId;//回避书rid
    //工种
    private String workType;
    //所属部门
    private String deptName;
    //标删
    private String delMark;
    //是否超期
    private String ifOutTime;
    //当前节点是否超期
    private boolean curInOutTime;
    //是否终止
    private Integer ifStop;
    //终止原因
    private String stopRsn;
    /**关联会诊记录*/
    //private TdZwRemoteGroup fkByRelRmtId;

    /**资料审核日期*/
    private Date auditDate;

    /**三级审核状态*/
    private Integer checkState;

    private String backRsn;

    private String cityBackRsn;

    /**三级审核日期*/
    private Date countySubmitDate;

    private Date citySubmitDate;

    private Date proSubmitDate;

    private Date countryBackDate;

    private Date countyRcvDate;

    private Date ciytRcvDate;

    private Integer apyZybSmallId;
    private Integer cfmZybSmallId;

    private Date orgSmtDate;

    private Integer age;
    private Integer tchWorkYear;
    private Integer tchWorkMonth;
    private Integer tchWorkDay;
    private Integer tchDays;
    private Integer tchHours;
    private Integer tchMinutes;

    /**电子健康卡号*/
    private String hethCardQrcode;

    public TdZwOccdiscaseComm() {
    }
    public TdZwOccdiscaseComm(Integer rid) {
    	this.rid = rid;
    }

    /** minimal constructor */
    public TdZwOccdiscaseComm(Integer rid,
                          TsSimpleCode tsSimpleCodeByOccdiseApplyid, TsUnit tsUnit,
                          String archCode, String personnelName, String idc, Date birthday,
                          String crptName, String institutionCode, String acceptPsn,
                          Date acceptDate, Integer stateMark, Date createDate,
                          Integer createManid) {
        this.rid = rid;
        this.tsSimpleCodeByOccdiseApplyid = tsSimpleCodeByOccdiseApplyid;
        this.tsUnit = tsUnit;
        this.archCode = archCode;
        this.personnelName = personnelName;
        this.idc = idc;
        this.birthday = birthday;
        this.crptName = crptName;
        this.institutionCode = institutionCode;
        this.acceptPsn = acceptPsn;
        this.acceptDate = acceptDate;
        this.stateMark = stateMark;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    /** full constructor */
    public TdZwOccdiscaseComm(Integer rid,
                          TsSimpleCode tsSimpleCodeByOccdiseApplyid,
                          TsSimpleCode tsSimpleCodeByOccdiseId, TdTjPerson tdTjPerson,
                          TsUnit tsUnit, TbTjCrpt tbTjCrpt, String archCode,
                          String personnelName, String idc, String sex, Date birthday,
                          String postcode, String linktel, String address, String crptName,
                          String institutionCode, String diagInfo, String rmk,
                          String acceptPsn, Date acceptDate, Date startDate,
                          Integer startHour, Integer startMint, Date endDate,
                          Integer endHour, Integer endMint, String compere,
                          String atchDiagPersons, String annaler, String diagPlace,
                          Integer isDis, String occLevel, String diagStd, String diagClusion,
                          String dealVote, Integer stateMark, Date updateTime,
                          Date createDate, Integer createManid, Date modifyDate,
                          Integer modifyManid, List<TdZwBadrsnHisComm> tdZwBadrsnHises) {
        this.rid = rid;
        this.tsSimpleCodeByOccdiseApplyid = tsSimpleCodeByOccdiseApplyid;
        this.tsSimpleCodeByOccdiseId = tsSimpleCodeByOccdiseId;
        this.tdTjPerson = tdTjPerson;
        this.tsUnit = tsUnit;
        this.tbTjCrpt = tbTjCrpt;
        this.archCode = archCode;
        this.personnelName = personnelName;
        this.idc = idc;
        this.sex = sex;
        this.birthday = birthday;
        this.postcode = postcode;
        this.linktel = linktel;
        this.address = address;
        this.crptName = crptName;
        this.institutionCode = institutionCode;
        this.diagInfo = diagInfo;
        this.rmk = rmk;
        this.acceptPsn = acceptPsn;
        this.acceptDate = acceptDate;
        this.startDate = startDate;
        this.startHour = startHour;
        this.startMint = startMint;
        this.endDate = endDate;
        this.endHour = endHour;
        this.endMint = endMint;
        this.compere = compere;
        this.atchDiagPersons = atchDiagPersons;
        this.annaler = annaler;
        this.diagPlace = diagPlace;
        this.isDis = isDis;
        this.occLevel = occLevel;
        this.diagStd = diagStd;
        this.diagClusion = diagClusion;
        this.dealVote = dealVote;
        this.stateMark = stateMark;
        this.updateTime = updateTime;
        this.createDate = createDate;
        this.createManid = createManid;
        this.modifyDate = modifyDate;
        this.modifyManid = modifyManid;
        this.tdZwBadrsnHises = tdZwBadrsnHises;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOccdiscase_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "OCCDISE_APPLYID")
    public TsSimpleCode getTsSimpleCodeByOccdiseApplyid() {
        return this.tsSimpleCodeByOccdiseApplyid;
    }

    public void setTsSimpleCodeByOccdiseApplyid(
            TsSimpleCode tsSimpleCodeByOccdiseApplyid) {
        this.tsSimpleCodeByOccdiseApplyid = tsSimpleCodeByOccdiseApplyid;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "OCCDISE_ID")
    public TsSimpleCode getTsSimpleCodeByOccdiseId() {
        return this.tsSimpleCodeByOccdiseId;
    }

    public void setTsSimpleCodeByOccdiseId(TsSimpleCode tsSimpleCodeByOccdiseId) {
        this.tsSimpleCodeByOccdiseId = tsSimpleCodeByOccdiseId;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "BHKPSN_ID")
    public TdTjPerson getTdTjPerson() {
        return this.tdTjPerson;
    }

    public void setTdTjPerson(TdTjPerson tdTjPerson) {
        this.tdTjPerson = tdTjPerson;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "ACPTORG_ID" )
    public TsUnit getTsUnit() {
        return this.tsUnit;
    }

    public void setTsUnit(TsUnit tsUnit) {
        this.tsUnit = tsUnit;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "CRPT_ID")
    public TbTjCrpt getTbTjCrpt() {
        return this.tbTjCrpt;
    }

    public void setTbTjCrpt(TbTjCrpt tbTjCrpt) {
        this.tbTjCrpt = tbTjCrpt;
    }

    @Column(name = "ARCH_CODE", unique = true, length = 50)
    public String getArchCode() {
        return this.archCode;
    }

    public void setArchCode(String archCode) {
        this.archCode = archCode;
    }

    @Column(name = "PERSONNEL_NAME", length = 50)
    public String getPersonnelName() {
        return this.personnelName;
    }

    public void setPersonnelName(String personnelName) {
        this.personnelName = personnelName;
    }

    @Column(name = "IDC", length = 20)
    public String getIdc() {
        return this.idc;
    }

    public void setIdc(String idc) {
        this.idc = idc;
    }

    @Column(name = "SEX", length = 10)
    public String getSex() {
        return this.sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BIRTHDAY", length = 7)
    public Date getBirthday() {
        return this.birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    @Column(name = "POSTCODE", length = 6)
    public String getPostcode() {
        return this.postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    @Column(name = "LINKTEL", length = 50)
    public String getLinktel() {
        return this.linktel;
    }

    public void setLinktel(String linktel) {
        this.linktel = linktel;
    }

    @Column(name = "ADDRESS", length = 100)
    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Column(name = "CRPT_NAME", length = 500)
    public String getCrptName() {
        return this.crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    @Column(name = "INSTITUTION_CODE" , length = 20)
    public String getInstitutionCode() {
        return this.institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    @Lob
    @Column(name = "DIAG_INFO")
    public String getDiagInfo() {
        return this.diagInfo;
    }

    public void setDiagInfo(String diagInfo) {
        this.diagInfo = diagInfo;
    }

    @Column(name = "RMK", length = 100)
    public String getRmk() {
        return this.rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    @Column(name = "ACCEPT_PSN",length = 50)
    public String getAcceptPsn() {
        return this.acceptPsn;
    }

    public void setAcceptPsn(String acceptPsn) {
        this.acceptPsn = acceptPsn;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "ACCEPT_DATE", length = 7)
    public Date getAcceptDate() {
        return this.acceptDate;
    }

    public void setAcceptDate(Date acceptDate) {
        this.acceptDate = acceptDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "START_DATE", length = 7)
    public Date getStartDate() {
        return this.startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Column(name = "START_HOUR")
    public Integer getStartHour() {
        return this.startHour;
    }

    public void setStartHour(Integer startHour) {
        this.startHour = startHour;
    }

    @Column(name = "START_MINT")
    public Integer getStartMint() {
        return this.startMint;
    }

    public void setStartMint(Integer startMint) {
        this.startMint = startMint;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "END_DATE", length = 7)
    public Date getEndDate() {
        return this.endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Column(name = "END_HOUR")
    public Integer getEndHour() {
        return this.endHour;
    }

    public void setEndHour(Integer endHour) {
        this.endHour = endHour;
    }

    @Column(name = "END_MINT")
    public Integer getEndMint() {
        return this.endMint;
    }

    public void setEndMint(Integer endMint) {
        this.endMint = endMint;
    }

    @Column(name = "COMPERE", length = 50)
    public String getCompere() {
        return this.compere;
    }

    public void setCompere(String compere) {
        this.compere = compere;
    }

    @Transient
    public String getAtchDiagPersons() {
        return this.atchDiagPersons;
    }

    public void setAtchDiagPersons(String atchDiagPersons) {
        this.atchDiagPersons = atchDiagPersons;
    }

    @Column(name = "ANNALER", length = 50)
    public String getAnnaler() {
        return this.annaler;
    }

    public void setAnnaler(String annaler) {
        this.annaler = annaler;
    }

    @Column(name = "DIAG_PLACE", length = 200)
    public String getDiagPlace() {
        return this.diagPlace;
    }

    public void setDiagPlace(String diagPlace) {
        this.diagPlace = diagPlace;
    }

    @Column(name = "IS_DIS")
    public Integer getIsDis() {
        return this.isDis;
    }

    public void setIsDis(Integer isDis) {
        this.isDis = isDis;
    }

    @Column(name = "OCC_LEVEL", length = 50)
    public String getOccLevel() {
        return this.occLevel;
    }

    public void setOccLevel(String occLevel) {
        this.occLevel = occLevel;
    }

    @Column(name = "DIAG_STD", length = 100)
    public String getDiagStd() {
        return this.diagStd;
    }

    public void setDiagStd(String diagStd) {
        this.diagStd = diagStd;
    }

    @Lob
    @Column(name = "DIAG_CLUSION")
    public String getDiagClusion() {
        return this.diagClusion;
    }

    public void setDiagClusion(String diagClusion) {
        this.diagClusion = diagClusion;
    }

    @Lob
    @Column(name = "DEAL_VOTE")
    public String getDealVote() {
        return this.dealVote;
    }

    public void setDealVote(String dealVote) {
        this.dealVote = dealVote;
    }

    @Column(name = "STATE_MARK" )
    public Integer getStateMark() {
        return this.stateMark;
    }

    public void setStateMark(Integer stateMark) {
        this.stateMark = stateMark;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATE_TIME")
    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdZwOccdiscase", orphanRemoval = true)
    public List<TdZwBadrsnHisComm> getTdZwBadrsnHises() {
        return this.tdZwBadrsnHises;
    }

    public void setTdZwBadrsnHises(List<TdZwBadrsnHisComm> tdZwBadrsnHises) {
        this.tdZwBadrsnHises = tdZwBadrsnHises;
    }

    @Transient
    public boolean isCanModfiy() {
        return canModfiy;
    }

    public void setCanModfiy(boolean canModfiy) {
        this.canModfiy = canModfiy;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "APPLY_DATE")
    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "DIAG_ZM_DATE")
    public Date getDiagZmDate() {
        return diagZmDate;
    }

    public void setDiagZmDate(Date diagZmDate) {
        this.diagZmDate = diagZmDate;
    }

    @Transient
    public String getAtchDiagPersonIds() {
        return atchDiagPersonIds;
    }

    public void setAtchDiagPersonIds(String atchDiagPersonIds) {
        this.atchDiagPersonIds = atchDiagPersonIds;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwOccPsnComm> getTdZwOccPsnList() {
        return tdZwOccPsnList;
    }

    public void setTdZwOccPsnList(List<TdZwOccPsnComm> tdZwOccPsnList) {
        this.tdZwOccPsnList = tdZwOccPsnList;
    }

    @Column(name = "OCCDISE_NAME")
    public String getOccdiseName() {
        return occdiseName;
    }

    public void setOccdiseName(String occdiseName) {
        this.occdiseName = occdiseName;
    }
    @Column(name = "IF_PROXY")
    public Integer getIfProxy() {
        return ifProxy;
    }

    public void setIfProxy(Integer ifProxy) {
        this.ifProxy = ifProxy;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "TG_DATE")
    public Date getTgDate() {
        return tgDate;
    }

    public void setTgDate(Date tgDate) {
        this.tgDate = tgDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "XZ_TG_DATE")
    public Date getXzTgDate() {
        return xzTgDate;
    }

    public void setXzTgDate(Date xzTgDate) {
        this.xzTgDate = xzTgDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "TG_MATRI_DATE")
    public Date getTgMatriDate() {
        return tgMatriDate;
    }

    public void setTgMatriDate(Date tgMatriDate) {
        this.tgMatriDate = tgMatriDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "BZ_DATE")
    public Date getBzDate() {
        return bzDate;
    }

    public void setBzDate(Date bzDate) {
        this.bzDate = bzDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "BZ_SJ_DATE")
    public Date getBzSjDate() {
        return bzSjDate;
    }

    public void setBzSjDate(Date bzSjDate) {
        this.bzSjDate = bzSjDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "TC_QR_DATE")
    public Date getTcQrDate() {
        return tcQrDate;
    }

    public void setTcQrDate(Date tcQrDate) {
        this.tcQrDate = tcQrDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "QR_DATE")
    public Date getQrDate() {
        return qrDate;
    }

    public void setQrDate(Date qrDate) {
        this.qrDate = qrDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "CZ_DATE")
    public Date getCzDate() {
        return czDate;
    }

    public void setCzDate(Date czDate) {
        this.czDate = czDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "XZJC_DATE")
    public Date getXzjcDate() {
        return xzjcDate;
    }

    public void setXzjcDate(Date xzjcDate) {
        this.xzjcDate = xzjcDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "ZDZZ_DATE")
    public Date getZdzzDate() {
        return zdzzDate;
    }

    public void setZdzzDate(Date zdzzDate) {
        this.zdzzDate = zdzzDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "WC_CL_DATE")
    public Date getWcClDate() {
        return wcClDate;
    }

    public void setWcClDate(Date wcClDate) {
        this.wcClDate = wcClDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "TL_DATE")
    public Date getTlDate() {
        return tlDate;
    }

    public void setTlDate(Date tlDate) {
        this.tlDate = tlDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "YQ_DATE")
    public Date getYqDate() {
        return yqDate;
    }

    public void setYqDate(Date yqDate) {
        this.yqDate = yqDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "ZD_DATE")
    public Date getZdDate() {
        return zdDate;
    }

    public void setZdDate(Date zdDate) {
        this.zdDate = zdDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "TZ_QS_DATE")
    public Date getTzQsDate() {
        return tzQsDate;
    }

    public void setTzQsDate(Date tzQsDate) {
        this.tzQsDate = tzQsDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "QS_DATE")
    public Date getQsDate() {
        return qsDate;
    }

    public void setQsDate(Date qsDate) {
        this.qsDate = qsDate;
    }
    @Temporal(TemporalType.DATE)
    @Column(name = "UP_RPT_DATE")
    public Date getUpRptDate() {
        return upRptDate;
    }

    public void setUpRptDate(Date upRptDate) {
        this.upRptDate = upRptDate;
    }
    @Column(name = "HIS_DISEASES")
    public String getHisDiseases() {
        return hisDiseases;
    }

    public void setHisDiseases(String hisDiseases) {
        this.hisDiseases = hisDiseases;
    }
    @Column(name = "PROXY_PSN_NAME")
    public String getProxyPsnName() {
        return proxyPsnName;
    }

    public void setProxyPsnName(String proxyPsnName) {
        this.proxyPsnName = proxyPsnName;
    }
    @Column(name = "PROXY_RELATION")
    public Integer getProxyRelation() {
        return proxyRelation;
    }

    public void setProxyRelation(Integer proxyRelation) {
        this.proxyRelation = proxyRelation;
    }
    @Column(name = "PROXY_IDC")
    public String getProxyIdc() {
        return proxyIdc;
    }

    public void setProxyIdc(String proxyIdc) {
        this.proxyIdc = proxyIdc;
    }
    @Column(name = "PROXY_LINKTEL")
    public String getProxyLinktel() {
        return proxyLinktel;
    }

    public void setProxyLinktel(String proxyLinktel) {
        this.proxyLinktel = proxyLinktel;
    }
    @Column(name = "OCC_APP_NAM")
    public String getOccAppNam() {
        return occAppNam;
    }

    public void setOccAppNam(String occAppNam) {
        this.occAppNam = occAppNam;
    }
    @Column(name = "IF_UP_ZYB_RPT")
    public Integer getIfUpZybRpt() {
        return ifUpZybRpt;
    }

    public void setIfUpZybRpt(Integer ifUpZybRpt) {
        this.ifUpZybRpt = ifUpZybRpt;
    }
    @Column(name = "RPT_CARD_TYPE")
    public String getRptCardType() {
        return rptCardType;
    }

    public void setRptCardType(String rptCardType) {
        this.rptCardType = rptCardType;
    }

    @Column(name = "ZD_WRIT_NO")
    public String getZdWritNo() {
        return zdWritNo;
    }

    public void setZdWritNo(String zdWritNo) {
        this.zdWritNo = zdWritNo;
    }

    @Transient
    public Integer getAvoidId() {
        return avoidId;
    }

    public void setAvoidId(Integer avoidId) {
        this.avoidId = avoidId;
    }
    @Column(name = "WORK_TYPE")
    public String getWorkType() {
        return workType;
    }

    public void setWorkType(String workType) {
        this.workType = workType;
    }
    @Column(name = "DEPT_NAME")
    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    @Column(name = "DEL_MARK")
    public String getDelMark() {
        return delMark;
    }

    public void setDelMark(String delMark) {
        this.delMark = delMark;
    }


    @Transient
    public String getArchCodeTemp() {
        return archCodeTemp;
    }

    public void setArchCodeTemp(String archCodeTemp) {
        this.archCodeTemp = archCodeTemp;
    }
    @Transient
    public String getZdWritNoTemp() {
        return zdWritNoTemp;
    }

    public void setZdWritNoTemp(String zdWritNoTemp) {
        this.zdWritNoTemp = zdWritNoTemp;
    }

    @Column(name = "IF_OUT_TIME")
    public String getIfOutTime() {
        return ifOutTime;
    }

    public void setIfOutTime(String ifOutTime) {
        this.ifOutTime = ifOutTime;
    }

    @Transient
    public boolean getCurInOutTime() {
        return curInOutTime;
    }

    public void setCurInOutTime(boolean curInOutTime) {
        this.curInOutTime = curInOutTime;
    }

    @Column(name = "IF_STOP")
    public Integer getIfStop() {
        return ifStop;
    }

    public void setIfStop(Integer ifStop) {
        this.ifStop = ifStop;
    }

    @Column(name = "STOP_RSN")
    public String getStopRsn() {
        return stopRsn;
    }

    public void setStopRsn(String stopRsn) {
        this.stopRsn = stopRsn;
    }

    /*@ManyToOne
    @JoinColumn(name = "REL_RMT_ID")
    public TdZwRemoteGroup getFkByRelRmtId() {
        return fkByRelRmtId;
    }

    public void setFkByRelRmtId(TdZwRemoteGroup fkByRelRmtId) {
        this.fkByRelRmtId = fkByRelRmtId;
    }*/

    @Temporal(TemporalType.DATE)
    @Column(name = "AUDIT_DATE")
    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    @Transient
    public Integer getCheckState() {
        return checkState;
    }

    public void setCheckState(Integer checkState) {
        this.checkState = checkState;
    }

    @Transient
    public String getBackRsn() {
        return backRsn;
    }

    public void setBackRsn(String backRsn) {
        this.backRsn = backRsn;
    }

    @Transient
    public Date getCountySubmitDate() {
        return countySubmitDate;
    }

    public void setCountySubmitDate(Date countySubmitDate) {
        this.countySubmitDate = countySubmitDate;
    }

    @Transient
    public Date getCitySubmitDate() {
        return citySubmitDate;
    }

    public void setCitySubmitDate(Date citySubmitDate) {
        this.citySubmitDate = citySubmitDate;
    }

    @Transient
    public Date getProSubmitDate() {
        return proSubmitDate;
    }

    public void setProSubmitDate(Date proSubmitDate) {
        this.proSubmitDate = proSubmitDate;
    }

    @Transient
    public Date getCountryBackDate() {
        return countryBackDate;
    }

    public void setCountryBackDate(Date countryBackDate) {
        this.countryBackDate = countryBackDate;
    }

    @Transient
    public Date getCountyRcvDate() {
        return countyRcvDate;
    }

    public void setCountyRcvDate(Date countyRcvDate) {
        this.countyRcvDate = countyRcvDate;
    }

    @Transient
    public Date getCiytRcvDate() {
        return ciytRcvDate;
    }

    public void setCiytRcvDate(Date ciytRcvDate) {
        this.ciytRcvDate = ciytRcvDate;
    }
    @Column(name = "APY_ZYB_SMALL_ID")
    public Integer getApyZybSmallId() {
        return apyZybSmallId;
    }

    public void setApyZybSmallId(Integer apyZybSmallId) {
        this.apyZybSmallId = apyZybSmallId;
    }

    @Column(name = "CFM_ZYB_SMALL_ID")
    public Integer getCfmZybSmallId() {
        return cfmZybSmallId;
    }

    public void setCfmZybSmallId(Integer cfmZybSmallId) {
        this.cfmZybSmallId = cfmZybSmallId;
    }

    @Transient
    public String getCityBackRsn() {
        return cityBackRsn;
    }

    public void setCityBackRsn(String cityBackRsn) {
        this.cityBackRsn = cityBackRsn;
    }

    @Transient
    public Date getOrgSmtDate() {
        return orgSmtDate;
    }

    public void setOrgSmtDate(Date orgSmtDate) {
        this.orgSmtDate = orgSmtDate;
    }

    @Column(name = "AGE")
    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    @Column(name = "TCH_WORK_YEAR")
    public Integer getTchWorkYear() {
        return tchWorkYear;
    }

    public void setTchWorkYear(Integer tchWorkYear) {
        this.tchWorkYear = tchWorkYear;
    }

    @Column(name = "TCH_WORK_MONTH")
    public Integer getTchWorkMonth() {
        return tchWorkMonth;
    }

    public void setTchWorkMonth(Integer tchWorkMonth) {
        this.tchWorkMonth = tchWorkMonth;
    }

    @Column(name = "TCH_WORK_DAY")
    public Integer getTchWorkDay() {
        return tchWorkDay;
    }

    public void setTchWorkDay(Integer tchWorkDay) {
        this.tchWorkDay = tchWorkDay;
    }

    @Column(name = "TCH_DAYS")
    public Integer getTchDays() {
        return tchDays;
    }

    public void setTchDays(Integer tchDays) {
        this.tchDays = tchDays;
    }

    @Column(name = "TCH_HOURS")
    public Integer getTchHours() {
        return tchHours;
    }

    public void setTchHours(Integer tchHours) {
        this.tchHours = tchHours;
    }

    @Column(name = "TCH_MINUTES")
    public Integer getTchMinutes() {
        return tchMinutes;
    }

    public void setTchMinutes(Integer tchMinutes) {
        this.tchMinutes = tchMinutes;
    }

    @Column(name = "HETH_CARD_QRCODE")
    public String getHethCardQrcode() {
        return hethCardQrcode;
    }

    public void setHethCardQrcode(String hethCardQrcode) {
        this.hethCardQrcode = hethCardQrcode;
    }
}
