package com.chis.modules.heth.comm.entity;

import java.util.Date;
import java.util.List;

import javax.persistence.*;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-2-18
 */
@Entity
@Table(name = "TD_ZW_OCCDIS_CARD_NEW")
@SequenceGenerator(name = "TdZwOccdisCardNew", sequenceName = "TD_ZW_OCCDIS_CARD_NEW_SEQ", allocationSize = 1)
public class TdZwOccdisCardNew implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer mainId;
	private String rptNo;
	private String personnelName;
	private TsSimpleCode fkByCardTypeId;
	private String idc;
	private String sex;
	private Date birthday;
	private String linktel;
	private String emergLinkName;
	private String emergLinktel;
	private TbTjCrpt fkByCrptId;
	private String crptName;
	private String creditCode;
	private TsSimpleCode fkByEconomyId;
	private TsSimpleCode fkByIndusTypeId;
	private TsSimpleCode fkByCrptSizeId;
	private TsZone fkByZoneId;
	private String address;
	private String postcode;
	private String safeposition;
	private String safephone;
	private Date applyDate;
	private Date diagDate;
	private Date diag1Date;
	private Date diag2Date;
	private Date diag3Date;
	private TsSimpleCode fkByZybTypeId;
	private String zybDisName;
	private TsSimpleCode fkByZybDisTypeId;
	private Integer zyPoisonType;
	private TsSimpleCode fkByRptTypeId;
	private TsSimpleCode fkByWorkTypeId;
	private String workOther;
	private Date deathDate;
	private String dieRsn;
	private Date begTchDust;
	private Integer tchDustYear;
	private Integer tchDustMonth;
	private Integer tchDays;
	private Integer tchHours;
	private Integer tchMinutes;
	private Integer ifTb;
	private Boolean ifTbValue;
	private Integer ifPulInfection;
	private Boolean ifPulInfectionValue;
	private Integer ifThePneum;
	private Boolean ifThePneumValue;
	private Integer ifPulHeart;
	private Boolean ifPulHeartValue;
	private Integer ifLungCancer;
	private Boolean ifLungCancerValue;
	private TsUnit fkByDiagUnitId;
	private String diagUnitName;
	private String diagRespPsn;
	private String fillFormPsn;
	private String fillLink;
	private Date fillDate;
	private String fillUnitName;
	private String rptPsn;
	private String rptLink;
	private Date rptDate;
	private String rptUnitName;
	private String rmk;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private String annexPath;

	private TbTjCrpt fkByEmpCrptId;

	private TsZone fkByEmpZoneId;

	private String empCrptName;

	private String empCreditCode;

	private TsSimpleCode fkByEmpEconomyId;

	private TsSimpleCode fkByEmpIndusTypeId;

	private TsSimpleCode fkByEmpCrptSizeId;
	private List<TdZwOccdisCardDoc> cardDocList;

	public TdZwOccdisCardNew() {
	}

	public TdZwOccdisCardNew(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOccdisCardNew")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "MAIN_ID")
	public Integer getMainId() {
		return mainId;
	}

	public void setMainId(Integer mainId) {
		this.mainId = mainId;
	}	
			
	@Column(name = "RPT_NO")	
	public String getRptNo() {
		return rptNo;
	}

	public void setRptNo(String rptNo) {
		this.rptNo = rptNo;
	}	
			
	@Column(name = "PERSONNEL_NAME")	
	public String getPersonnelName() {
		return personnelName;
	}

	public void setPersonnelName(String personnelName) {
		this.personnelName = personnelName;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CARD_TYPE_ID")			
	public TsSimpleCode getFkByCardTypeId() {
		return fkByCardTypeId;
	}

	public void setFkByCardTypeId(TsSimpleCode fkByCardTypeId) {
		this.fkByCardTypeId = fkByCardTypeId;
	}	
			
	@Column(name = "IDC")	
	public String getIdc() {
		return idc;
	}

	public void setIdc(String idc) {
		this.idc = idc;
	}	
			
	@Column(name = "SEX")	
	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "BIRTHDAY")			
	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}	
			
	@Column(name = "LINKTEL")	
	public String getLinktel() {
		return linktel;
	}

	public void setLinktel(String linktel) {
		this.linktel = linktel;
	}	
			
	@Column(name = "EMERG_LINK_NAME")	
	public String getEmergLinkName() {
		return emergLinkName;
	}

	public void setEmergLinkName(String emergLinkName) {
		this.emergLinkName = emergLinkName;
	}	
			
	@Column(name = "EMERG_LINKTEL")	
	public String getEmergLinktel() {
		return emergLinktel;
	}

	public void setEmergLinktel(String emergLinktel) {
		this.emergLinktel = emergLinktel;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_ID")			
	public TbTjCrpt getFkByCrptId() {
		return fkByCrptId;
	}

	public void setFkByCrptId(TbTjCrpt fkByCrptId) {
		this.fkByCrptId = fkByCrptId;
	}	
			
	@Column(name = "CRPT_NAME")	
	public String getCrptName() {
		return crptName;
	}

	public void setCrptName(String crptName) {
		this.crptName = crptName;
	}	
			
	@Column(name = "CREDIT_CODE")	
	public String getCreditCode() {
		return creditCode;
	}

	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ECONOMY_ID")			
	public TsSimpleCode getFkByEconomyId() {
		return fkByEconomyId;
	}

	public void setFkByEconomyId(TsSimpleCode fkByEconomyId) {
		this.fkByEconomyId = fkByEconomyId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "INDUS_TYPE_ID")			
	public TsSimpleCode getFkByIndusTypeId() {
		return fkByIndusTypeId;
	}

	public void setFkByIndusTypeId(TsSimpleCode fkByIndusTypeId) {
		this.fkByIndusTypeId = fkByIndusTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_SIZE_ID")			
	public TsSimpleCode getFkByCrptSizeId() {
		return fkByCrptSizeId;
	}

	public void setFkByCrptSizeId(TsSimpleCode fkByCrptSizeId) {
		this.fkByCrptSizeId = fkByCrptSizeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ZONE_ID")			
	public TsZone getFkByZoneId() {
		return fkByZoneId;
	}

	public void setFkByZoneId(TsZone fkByZoneId) {
		this.fkByZoneId = fkByZoneId;
	}	
			
	@Column(name = "ADDRESS")	
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}	
			
	@Column(name = "POSTCODE")	
	public String getPostcode() {
		return postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}	
			
	@Column(name = "SAFEPOSITION")	
	public String getSafeposition() {
		return safeposition;
	}

	public void setSafeposition(String safeposition) {
		this.safeposition = safeposition;
	}	
			
	@Column(name = "SAFEPHONE")	
	public String getSafephone() {
		return safephone;
	}

	public void setSafephone(String safephone) {
		this.safephone = safephone;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "APPLY_DATE")			
	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DIAG_DATE")			
	public Date getDiagDate() {
		return diagDate;
	}

	public void setDiagDate(Date diagDate) {
		this.diagDate = diagDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DIAG_1_DATE")			
	public Date getDiag1Date() {
		return diag1Date;
	}

	public void setDiag1Date(Date diag1Date) {
		this.diag1Date = diag1Date;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DIAG_2_DATE")			
	public Date getDiag2Date() {
		return diag2Date;
	}

	public void setDiag2Date(Date diag2Date) {
		this.diag2Date = diag2Date;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DIAG_3_DATE")			
	public Date getDiag3Date() {
		return diag3Date;
	}

	public void setDiag3Date(Date diag3Date) {
		this.diag3Date = diag3Date;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ZYB_TYPE_ID")			
	public TsSimpleCode getFkByZybTypeId() {
		return fkByZybTypeId;
	}

	public void setFkByZybTypeId(TsSimpleCode fkByZybTypeId) {
		this.fkByZybTypeId = fkByZybTypeId;
	}	
			
	@Column(name = "ZYB_DIS_NAME")	
	public String getZybDisName() {
		return zybDisName;
	}

	public void setZybDisName(String zybDisName) {
		this.zybDisName = zybDisName;
	}	
			
	@Column(name = "ZY_POISON_TYPE")	
	public Integer getZyPoisonType() {
		return zyPoisonType;
	}

	public void setZyPoisonType(Integer zyPoisonType) {
		this.zyPoisonType = zyPoisonType;
	}	
			
	@ManyToOne
	@JoinColumn(name = "RPT_TYPE_ID")			
	public TsSimpleCode getFkByRptTypeId() {
		return fkByRptTypeId;
	}

	public void setFkByRptTypeId(TsSimpleCode fkByRptTypeId) {
		this.fkByRptTypeId = fkByRptTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "WORK_TYPE_ID")			
	public TsSimpleCode getFkByWorkTypeId() {
		return fkByWorkTypeId;
	}

	public void setFkByWorkTypeId(TsSimpleCode fkByWorkTypeId) {
		this.fkByWorkTypeId = fkByWorkTypeId;
	}	
			
	@Column(name = "WORK_OTHER")	
	public String getWorkOther() {
		return workOther;
	}

	public void setWorkOther(String workOther) {
		this.workOther = workOther;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DEATH_DATE")			
	public Date getDeathDate() {
		return deathDate;
	}

	public void setDeathDate(Date deathDate) {
		this.deathDate = deathDate;
	}	
			
	@Column(name = "DIE_RSN")	
	public String getDieRsn() {
		return dieRsn;
	}

	public void setDieRsn(String dieRsn) {
		this.dieRsn = dieRsn;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "BEG_TCH_DUST")			
	public Date getBegTchDust() {
		return begTchDust;
	}

	public void setBegTchDust(Date begTchDust) {
		this.begTchDust = begTchDust;
	}	
			
	@Column(name = "TCH_DUST_YEAR")	
	public Integer getTchDustYear() {
		return tchDustYear;
	}

	public void setTchDustYear(Integer tchDustYear) {
		this.tchDustYear = tchDustYear;
	}	
			
	@Column(name = "TCH_DUST_MONTH")	
	public Integer getTchDustMonth() {
		return tchDustMonth;
	}

	public void setTchDustMonth(Integer tchDustMonth) {
		this.tchDustMonth = tchDustMonth;
	}	
			
	@Column(name = "TCH_DAYS")	
	public Integer getTchDays() {
		return tchDays;
	}

	public void setTchDays(Integer tchDays) {
		this.tchDays = tchDays;
	}	
			
	@Column(name = "TCH_HOURS")	
	public Integer getTchHours() {
		return tchHours;
	}

	public void setTchHours(Integer tchHours) {
		this.tchHours = tchHours;
	}	
			
	@Column(name = "TCH_MINUTES")	
	public Integer getTchMinutes() {
		return tchMinutes;
	}

	public void setTchMinutes(Integer tchMinutes) {
		this.tchMinutes = tchMinutes;
	}	
			
	@Column(name = "IF_TB")	
	public Integer getIfTb() {
		return ifTb;
	}

	public void setIfTb(Integer ifTb) {
		this.ifTb = ifTb;
	}	
			
	@Column(name = "IF_PUL_INFECTION")	
	public Integer getIfPulInfection() {
		return ifPulInfection;
	}

	public void setIfPulInfection(Integer ifPulInfection) {
		this.ifPulInfection = ifPulInfection;
	}	
			
	@Column(name = "IF_THE_PNEUM")	
	public Integer getIfThePneum() {
		return ifThePneum;
	}

	public void setIfThePneum(Integer ifThePneum) {
		this.ifThePneum = ifThePneum;
	}	
			
	@Column(name = "IF_PUL_HEART")	
	public Integer getIfPulHeart() {
		return ifPulHeart;
	}

	public void setIfPulHeart(Integer ifPulHeart) {
		this.ifPulHeart = ifPulHeart;
	}	
			
	@Column(name = "IF_LUNG_CANCER")	
	public Integer getIfLungCancer() {
		return ifLungCancer;
	}

	public void setIfLungCancer(Integer ifLungCancer) {
		this.ifLungCancer = ifLungCancer;
	}	
			
	@ManyToOne
	@JoinColumn(name = "DIAG_UNIT_ID")			
	public TsUnit getFkByDiagUnitId() {
		return fkByDiagUnitId;
	}

	public void setFkByDiagUnitId(TsUnit fkByDiagUnitId) {
		this.fkByDiagUnitId = fkByDiagUnitId;
	}	
			
	@Column(name = "DIAG_UNIT_NAME")	
	public String getDiagUnitName() {
		return diagUnitName;
	}

	public void setDiagUnitName(String diagUnitName) {
		this.diagUnitName = diagUnitName;
	}	
			
	@Column(name = "DIAG_RESP_PSN")	
	public String getDiagRespPsn() {
		return diagRespPsn;
	}

	public void setDiagRespPsn(String diagRespPsn) {
		this.diagRespPsn = diagRespPsn;
	}	
			
	@Column(name = "FILL_FORM_PSN")	
	public String getFillFormPsn() {
		return fillFormPsn;
	}

	public void setFillFormPsn(String fillFormPsn) {
		this.fillFormPsn = fillFormPsn;
	}	
			
	@Column(name = "FILL_LINK")	
	public String getFillLink() {
		return fillLink;
	}

	public void setFillLink(String fillLink) {
		this.fillLink = fillLink;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "FILL_DATE")			
	public Date getFillDate() {
		return fillDate;
	}

	public void setFillDate(Date fillDate) {
		this.fillDate = fillDate;
	}	
			
	@Column(name = "FILL_UNIT_NAME")	
	public String getFillUnitName() {
		return fillUnitName;
	}

	public void setFillUnitName(String fillUnitName) {
		this.fillUnitName = fillUnitName;
	}	
			
	@Column(name = "RPT_PSN")	
	public String getRptPsn() {
		return rptPsn;
	}

	public void setRptPsn(String rptPsn) {
		this.rptPsn = rptPsn;
	}	
			
	@Column(name = "RPT_LINK")	
	public String getRptLink() {
		return rptLink;
	}

	public void setRptLink(String rptLink) {
		this.rptLink = rptLink;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "RPT_DATE")			
	public Date getRptDate() {
		return rptDate;
	}

	public void setRptDate(Date rptDate) {
		this.rptDate = rptDate;
	}	
			
	@Column(name = "RPT_UNIT_NAME")	
	public String getRptUnitName() {
		return rptUnitName;
	}

	public void setRptUnitName(String rptUnitName) {
		this.rptUnitName = rptUnitName;
	}	
			
	@Column(name = "RMK")	
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	@Transient
	public Boolean getIfTbValue() {
		return ifTbValue;
	}

	public void setIfTbValue(Boolean ifTbValue) {
		this.ifTbValue = ifTbValue;
	}
	@Transient
	public Boolean getIfPulInfectionValue() {
		return ifPulInfectionValue;
	}

	public void setIfPulInfectionValue(Boolean ifPulInfectionValue) {
		this.ifPulInfectionValue = ifPulInfectionValue;
	}
	@Transient
	public Boolean getIfThePneumValue() {
		return ifThePneumValue;
	}

	public void setIfThePneumValue(Boolean ifThePneumValue) {
		this.ifThePneumValue = ifThePneumValue;
	}
	@Transient
	public Boolean getIfPulHeartValue() {
		return ifPulHeartValue;
	}

	public void setIfPulHeartValue(Boolean ifPulHeartValue) {
		this.ifPulHeartValue = ifPulHeartValue;
	}
	@Transient
	public Boolean getIfLungCancerValue() {
		return ifLungCancerValue;
	}

	public void setIfLungCancerValue(Boolean ifLungCancerValue) {
		this.ifLungCancerValue = ifLungCancerValue;
	}
	@ManyToOne
	@JoinColumn(name = "ZYB_DIS_TYPE_ID")
	public TsSimpleCode getFkByZybDisTypeId() {
		return fkByZybDisTypeId;
	}

	public void setFkByZybDisTypeId(TsSimpleCode fkByZybDisTypeId) {
		this.fkByZybDisTypeId = fkByZybDisTypeId;
	}

	@Column(name = "ANNEX_PATH")
	public String getAnnexPath() {
		return annexPath;
	}

	public void setAnnexPath(String annexPath) {
		this.annexPath = annexPath;
	}
	@ManyToOne
	@JoinColumn(name = "EMP_CRPT_ID")
	public TbTjCrpt getFkByEmpCrptId() {
		return fkByEmpCrptId;
	}

	public void setFkByEmpCrptId(TbTjCrpt fkByEmpCrptId) {
		this.fkByEmpCrptId = fkByEmpCrptId;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_ZONE_ID")
	public TsZone getFkByEmpZoneId() {
		return fkByEmpZoneId;
	}

	public void setFkByEmpZoneId(TsZone fkByEmpZoneId) {
		this.fkByEmpZoneId = fkByEmpZoneId;
	}

	@Column(name = "EMP_CRPT_NAME")
	public String getEmpCrptName() {
		return empCrptName;
	}

	public void setEmpCrptName(String empCrptName) {
		this.empCrptName = empCrptName;
	}

	@Column(name = "EMP_CREDIT_CODE")
	public String getEmpCreditCode() {
		return empCreditCode;
	}

	public void setEmpCreditCode(String empCreditCode) {
		this.empCreditCode = empCreditCode;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_ECONOMY_ID")
	public TsSimpleCode getFkByEmpEconomyId() {
		return fkByEmpEconomyId;
	}

	public void setFkByEmpEconomyId(TsSimpleCode fkByEmpEconomyId) {
		this.fkByEmpEconomyId = fkByEmpEconomyId;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_INDUS_TYPE_ID")
	public TsSimpleCode getFkByEmpIndusTypeId() {
		return fkByEmpIndusTypeId;
	}

	public void setFkByEmpIndusTypeId(TsSimpleCode fkByEmpIndusTypeId) {
		this.fkByEmpIndusTypeId = fkByEmpIndusTypeId;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_CRPT_SIZE_ID")
	public TsSimpleCode getFkByEmpCrptSizeId() {
		return fkByEmpCrptSizeId;
	}

	public void setFkByEmpCrptSizeId(TsSimpleCode fkByEmpCrptSizeId) {
		this.fkByEmpCrptSizeId = fkByEmpCrptSizeId;
	}
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
	public List<TdZwOccdisCardDoc> getCardDocList() {
		return cardDocList;
	}

	public void setCardDocList(List<TdZwOccdisCardDoc> cardDocList) {
		this.cardDocList = cardDocList;
	}

}