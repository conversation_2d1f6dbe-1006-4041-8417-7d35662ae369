package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.entity.TdZxjcResultPro;
import com.chis.modules.heth.comm.entity.TdZxjcUnitbasicinfo;
import com.chis.modules.heth.comm.enumn.ReturnType;
import com.chis.modules.heth.comm.json.CrptUploadRepDTO;
import com.chis.modules.heth.comm.json.CrptUploadRepSingleDTO;
import com.chis.modules.heth.comm.logic.ExcelHeaderDTO;
import com.chis.modules.heth.comm.logic.ExcelHeaderDetailDTO;
import com.chis.modules.heth.comm.service.SiteMonitoringDataService;
import com.chis.modules.heth.comm.utils.UnitbasicinfoUtils;
import com.chis.modules.system.entity.TsContraSub;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.utils.ImportExcelUtil;
import com.chis.modules.system.web.FacesSimpleBean;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

/**
 * 场所监测数据导入Bean
 *
 * <AUTHOR>
 * @version 1.0
 */
@ManagedBean(name = "siteMonitoringDataBean")
@ViewScoped
public class SiteMonitoringDataBean extends FacesSimpleBean {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final SiteMonitoringDataService siteMonitoringDataService = SpringContextHolder.getBean(SiteMonitoringDataService.class);

    /**
     * 查询/导入条件：年份
     */
    private Integer searchYear;
    private Integer importYear;
    private List<Integer> searchYearList;
    /**
     * 查询条件：地区集合
     */
    private List<TsZone> searchZoneList = new ArrayList<>();
    private Map<String, TsZone> zoneMap = new HashMap<>(16);
    /**
     * 查询条件：地区编码
     */
    private String searchZoneGb;
    /**
     * 查询条件：地区名称
     */
    private String searchZoneName;
    /**
     * 查询条件：企业ID
     */
    private String searchUuId;
    /**
     * 查询条件：单位名称
     */
    private String searchUnitName;
    /**
     * 查询条件：社会信用代码
     */
    private String searchCreditCode;
    /**
     * 查询/导入条件：导入数据类型
     */
    private List<Integer> searchDataTypeList;
    private Integer importDataType;
    /**
     * 码表Map key：码表类型TYPE
     */
    private Map<String, Map<String, TsSimpleCode>> simpleCodeMap;

    /**
     * 对照数据
     */
    private Map<Integer, Map<String, String>> contrastMap;
    /**
     * 错误数据文件路径
     */
    private String importErrFilePath;
    /**
     * 导入模板下载
     */
    private StreamedContent templateFile;
    /**
     * 导入错误文件下载
     */
    private StreamedContent errorImportFile;
    /**
     * 导入错误文件名称
     */
    private String uuidFileName;

    /**
     * 调查信息 表头信息封装
     */
    private ExcelHeaderDTO headerDTO;
    /** 企业信息来源 监测系统 */
    private Integer jcCrptSourceId;

    public SiteMonitoringDataBean() {
        init();
        initSimpleCode();
        this.ifSQL = true;
    }

    private void init() {
        this.searchYearList = new ArrayList<>();
        this.searchYear = DateUtils.getYearInt();
        for (int i = 0; i < 10; i++) {
            this.searchYearList.add(this.searchYear - i);
        }
        //地区
        this.searchZoneList = this.commService.findZoneListByGbAndTypeNoNation("", true, "", "");
        for (TsZone zone : this.searchZoneList) {
            this.zoneMap.put(zone.getZoneGb(), zone);
        }
        //导入数据
        this.searchDataTypeList = new ArrayList<>();
        this.contrastMap = new HashMap<>(16);
    }

    private void initSimpleCode() {
        this.simpleCodeMap = new HashMap<>(16);
        //行业类别
        addSimpleCodeMapByList(this.commService.findNumSimpleCodesByTypeId("5002"), "5002");
        //经济类型
        addSimpleCodeMapByList(this.commService.findNumSimpleCodesByTypeId("5003"), "5003");
        //企业规模
        addSimpleCodeMapByList(this.commService.findNumSimpleCodesByTypeId("5004"), "5004");
        //危害因素
        addSimpleCodeMapByList(this.commService.findNumSimpleCodesByTypeId("5007"), "5007");
        //监测因素
        addSimpleCodeMapByList(this.commService.findNumSimpleCodesByTypeId("5007"), "5007");
        //监测项目
        addSimpleCodeMapByList(this.commService.findNumSimpleCodesByTypeId("5581"), "5581");

        //企业信息来源
        List<TsSimpleCode> crptSourceList = this.commService.findLevelSimpleCodesByTypeId("5512");
        if(!CollectionUtils.isEmpty(crptSourceList)){
            for(TsSimpleCode simpleCode : crptSourceList){
                if("1001".equals(simpleCode.getCodeNo())){
                    this.jcCrptSourceId = simpleCode.getRid();
                }
            }
        }
    }

    private void addSimpleCodeMapByList(List<TsSimpleCode> simpleCodeList, String codeType) {
        for (TsSimpleCode simpleCode : simpleCodeList) {
            if (!this.simpleCodeMap.containsKey(codeType)) {
                this.simpleCodeMap.put(codeType, new HashMap<String, TsSimpleCode>(16));
            }
            if (codeType.equals("5581")) {
                this.simpleCodeMap.get(codeType).put(simpleCode.getCodeName(), simpleCode);
            } else {
                this.simpleCodeMap.get(codeType).put(simpleCode.getCodeNo(), simpleCode);
            }
        }
    }

    @Override
    public void searchAction() {
        super.searchAction();

        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update("mainForm:dataTable");
    }

    @Override
    public String[] buildHqls() {
        StringBuilder baseSql = new StringBuilder();
        baseSql.append("SELECT T.RID, T.YEAR, CASE WHEN Z.ZONE_TYPE > 2 THEN SUBSTR(Z.FULL_NAME, INSTR(Z.FULL_NAME, '_') + 1) ELSE Z.FULL_NAME END ZONE_NAME, T.UNIT_NAME, T.CREDIT_CODE, T.UUID, (SELECT COUNT(1) FROM TD_ZXJC_UNITFACTORCROWD B WHERE T.RID = B.MAIN_ID) A, (SELECT COUNT(1) FROM TD_ZXJC_RESULT_PRO R WHERE T.RID = R.MAIN_ID) B, Z.ZONE_GB ");
        baseSql.append("FROM TD_ZXJC_UNITBASICINFO T ");
        baseSql.append("LEFT JOIN TS_ZONE Z ON T.ZONE_ID = Z.RID ");
        baseSql.append("WHERE 1 = 1 ");
        //年份
        if (ObjectUtil.isNotEmpty(this.searchYear)) {
            baseSql.append(" AND T.YEAR = :searchYear ");
            this.paramMap.put("searchYear", this.searchYear);
        }
        //地区
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            baseSql.append(" AND Z.ZONE_GB LIKE :zoneCode escape '\\\' ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneGb)) + "%");
        }
        //企业ID
        if (StringUtils.isNotBlank(this.searchUuId)) {
            baseSql.append(" AND T.UUID LIKE :searchUuId escape '\\\' ");
            this.paramMap.put("searchUuId", "%" + StringUtils.convertBFH(this.searchUuId.trim()) + "%");
        }
        //单位名称
        if (StringUtils.isNotBlank(this.searchUnitName)) {
            baseSql.append(" AND T.UNIT_NAME LIKE :searchUnitName escape '\\\' ");
            this.paramMap.put("searchUnitName", "%" + StringUtils.convertBFH(this.searchUnitName.trim()) + "%");
        }
        //社会信用代码
        if (StringUtils.isNotBlank(this.searchCreditCode)) {
            baseSql.append(" AND T.CREDIT_CODE LIKE :searchCreditCode escape '\\\' ");
            this.paramMap.put("searchCreditCode", "%" + StringUtils.convertBFH(this.searchCreditCode.trim()) + "%");
        }
        //导入数据
        if (ObjectUtil.isNotEmpty(this.searchDataTypeList)) {
            baseSql.append(" AND ( ");
            if (this.searchDataTypeList.contains(1)) {
                baseSql.append(" EXISTS(SELECT 1 FROM TD_ZXJC_UNITFACTORCROWD B WHERE T.RID = B.MAIN_ID) ");
            }
            if (this.searchDataTypeList.contains(2)) {
                if (this.searchDataTypeList.contains(1)) {
                    baseSql.append(" OR ");
                }
                baseSql.append(" EXISTS(SELECT 1 FROM TD_ZXJC_RESULT_PRO R WHERE T.RID = R.MAIN_ID) ");
            }
            baseSql.append(" ) ");
        }
        String sql1 = "SELECT * FROM (" + baseSql + ")AA ORDER BY AA.YEAR DESC, AA.ZONE_GB, AA.UNIT_NAME, AA.RID DESC";
        String sql2 = "SELECT COUNT(1) FROM (" + baseSql + ")";
        return new String[]{sql1, sql2};
    }

    /**
     * 打开导入弹出框
     */
    public void openImportDialog() {
        this.importYear = null;
        this.importDataType = null;
        this.importErrFilePath = null;
        RequestContext.getCurrentInstance().update("mainForm:importTable");
        RequestContext.getCurrentInstance().execute("PF('ImportDialog').show();");
    }

    /**
     * 导入前操作
     */
    public void importBefore() {
        boolean verifyFailed = false;
        if (ObjectUtil.isEmpty(this.importDataType)) {
            JsfUtil.addErrorMessage("请选择数据类型！");
            verifyFailed = true;
        } else if (new Integer(0).equals(this.importDataType) && ObjectUtil.isEmpty(this.importYear)) {
            JsfUtil.addErrorMessage("请选择年份！");
            verifyFailed = true;
        }
        if (verifyFailed) {
            return;
        }
        if (!new Integer(0).equals(this.importDataType)) {
            this.importYear = null;
        }
        RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').show();");
    }

    /**
     *  <p>方法描述：模版下载前验证</p>
     * @MethodAuthor hsj 2025-01-17 16:34
     */
    public void templateImportBefore() {
        if (ObjectUtil.isEmpty(this.importDataType)) {
            JsfUtil.addErrorMessage("请选择数据类型！");
            return ;
        }
        RequestContext.getCurrentInstance().execute("getDownloadFileClick()");
    }
    /**
     * <p>方法描述：导入模板下载</p>
     *
     * @MethodAuthor hsj 2025-01-16 14:19
     */
    public StreamedContent getTemplateFiles() {
        String fileName = "";
        if(new Integer(0).equals(this.importDataType)){
            fileName = "基本信息";
        }else if(new Integer(1).equals(this.importDataType)){
            fileName = "调查信息";
        }else if(new Integer(2).equals(this.importDataType)){
            fileName = "结果信息";
        }
        fileName += "导入模板.xlsx";
        if (StringUtils.isBlank(fileName)) {
            return null;
        }
        InputStream stream = null;
        try {
            String moudleFilePath = "/resources/template/excel/" + fileName;
            stream = FacesContext.getCurrentInstance().getExternalContext().getResourceAsStream(moudleFilePath);
            return new DefaultStreamedContent(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", URLEncoder.encode(fileName, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("文件下载失败!");
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }
        return null;
    }

    /**
     * 导入
     *
     * @param event 上传的文件
     */
    public void importDataAction(FileUploadEvent event) {
        this.importErrFilePath = null;
        // 删除历史错误文件
        File errorFile = new File(JsfUtil.getAbsolutePath() + "heth/comm/temp/" + this.uuidFileName + ".xlsx");
        if (errorFile.exists()) {
            boolean ignore = errorFile.delete();
        }
        errorFile = new File(JsfUtil.getAbsolutePath() + "heth/comm/temp/" + this.uuidFileName + ".xls");
        if (errorFile.exists()) {
            boolean ignore = errorFile.delete();
        }

        String updateFormId = "tabView:mainForm:uploadFileDialog";
        if (event == null || event.getFile() == null) {
            RequestContext.getCurrentInstance().update(updateFormId);
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
            JsfUtil.addErrorMessage("文件上传失败！");
            return;
        }

        Workbook wb = null;
        String fileName = null;
        List<List<Object>> excelRowList = new ArrayList<>();
        try {
            //处理数据长度
            int rowSize = returnRowNum();
            this.uuidFileName = StringUtils.uuid();
            UploadedFile file = event.getFile();
            fileName = file.getFileName();
            String errorMsg = FileUtils.veryFile(file.getInputstream(),file.getContentType(), fileName, "5");
            if (StringUtils.isNotBlank(errorMsg)) {
                JsfUtil.addErrorMessage(errorMsg);
                RequestContext.getCurrentInstance().update(updateFormId);
                RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
                return;
            }
            // 验证数据总数量是否满足
            wb = ImportExcelUtil.getWorkbook(file);
            //读取workbook第一页sheet的数据
            if (!verifyWorkBook(wb, excelRowList, rowSize)) {
                RequestContext.getCurrentInstance().update(updateFormId);
                RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
                return;
            }
            //处理数据长度
            int colSize = returnColNum();
            dealExcelRowList(excelRowList, colSize);
            //获取对照
            pakContrastMap();
            //验证数据
            Map<Integer, String> errorMap = verifyData(excelRowList, rowSize);
            //封装数据并保存
            pakDataAndSave(excelRowList, errorMap, rowSize);
            //生成错误数据文件
            generateImportErrorFile(wb, fileName, colSize, rowSize, excelRowList, errorMap);
            //计算成功/失败条数并提示
            pakTipStr(excelRowList, errorMap, rowSize);
            RequestContext.getCurrentInstance().update("mainForm:importTable");
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
        } catch (Exception e) {
            e.printStackTrace();
            RequestContext.getCurrentInstance().update(updateFormId);
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
            JsfUtil.addErrorMessage("导入文件异常！");
        }
    }

    /**
     * 验证导入文件是否有数据
     *
     * @param wb           excel
     * @param excelRowList 导入excel数据行
     * @param rowSize      列头行数
     * @return 导入文件是否有数据
     */
    private boolean verifyWorkBook(Workbook wb, List<List<Object>> excelRowList, int rowSize) {
        try {
            if (null == wb || wb.getNumberOfSheets() <= 0) {
                JsfUtil.addErrorMessage("Excel表格无数据，请重新选择文件导入！");
                return false;
            }
            int sheetNum = ImportExcelUtil.countExcel(wb, 0);
            if (sheetNum <= 0) {
                JsfUtil.addErrorMessage("Excel表格无数据，请重新选择文件导入！");
                return false;
            }
            List<List<Object>> readExcelRowList = ImportExcelUtil.readNewExcelWithEmptyRow(wb, 0);
            //初始化表头下标
            if (new Integer(1).equals(this.importDataType)) {
                packageHearderInfo(readExcelRowList.get(0));
            }
            if (ObjectUtil.isNotEmpty(readExcelRowList)) {
                for (int size = rowSize - 1; size >= 0; size--) {
                    readExcelRowList.remove(size);
                }
            }
            if (isListAllNull(readExcelRowList)) {
                JsfUtil.addErrorMessage("Excel表格无数据，请重新选择文件导入！");
                return false;
            }
            excelRowList.addAll(readExcelRowList);
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("Excel表格无数据，请重新选择文件导入！");
            return false;
        }
        return true;
    }

    /**
     * 判断list是否都为null
     *
     * @param list list
     * @return list是否都为null
     */
    private boolean isListAllNull(List<List<Object>> list) {
        if (ObjectUtil.isEmpty(list)) {
            return true;
        }
        for (List<Object> objects : list) {
            if (ObjectUtil.isNotEmpty(objects)) {
                return false;
            }
        }
        return true;
    }

    /**
     * <p>方法描述：封装表头下标</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-09-09
     **/
    public void packageHearderInfo(List<Object> list) {
        headerDTO = new ExcelHeaderDTO();
        headerDTO.setSingleIndexHeader(new String[]{"企业ID", "3年内技术改造、引进项目情况", "当前工作阶段",
                "预评价开展情况", "职业病防护设施专篇", "控制效果评价开展情况", "接触危害总人数",
                "有无接触粉尘", "有无接触化学因素", "有无接触物理因素",
                "上一年度检测情况", "上一年度在岗期间职业健康检查情况", "体检总人数",
                "防尘设施-设置情况", "防尘设施-防护效果", "防毒设施-设置情况", "防毒设施-防护效果",
                "防噪声设施-设置情况", "防噪声设施-防护效果", "防尘口罩-发放情况", "防尘口罩-佩戴情况",
                "防毒口罩或面罩-发放情况", "防毒口罩或面罩-佩戴情况", "防噪声耳罩或耳塞-发放情况",
                "防噪声耳罩或耳塞-佩戴情况", "粉尘职业病危害警示标识及警示说明", "化学毒物职业病危害警示标识及警示说明", "噪声职业病危害警示标识及警示说明"});
        headerDTO.setFactorcrowdIndexHeader(new String[]{"接触粉尘总人数", "接触化学总人数", "物理因素接触人数"});
        headerDTO.setChkIndexHeader(new String[]{"是否检测粉尘因素", "化学毒物有无检测", "物理因素有无检测"});
        headerDTO.setCusIndexHeader(new String[]{"粉尘因素有无体检", "化学因素有无体检", "物理因素有无体检"});
        List<String> singleH = Arrays.asList(headerDTO.getSingleIndexHeader());
        List<String> singleF = Arrays.asList(headerDTO.getFactorcrowdIndexHeader());
        List<String> singleC = Arrays.asList(headerDTO.getChkIndexHeader());
        List<String> singleCu = Arrays.asList(headerDTO.getCusIndexHeader());
        headerDTO.setDetailMap(new HashMap<String, ExcelHeaderDetailDTO>());
        headerDTO.setHearderIndexMap(new HashMap<Integer, String>());
        Map<String, ExcelHeaderDetailDTO> fDTOMapTemp = new HashMap<>();
        Map<String, ExcelHeaderDetailDTO> cDTOMapTemp = new HashMap<>();
        Map<String, ExcelHeaderDetailDTO> cuDTOMapTemp = new HashMap<>();
        for (int i = 0; i < list.size(); i++) {
            String titleName = list.get(i).toString().replaceAll("\\s*|\r|\n|\t", "");
            if (singleH.contains(titleName)) {
                ExcelHeaderDetailDTO detailDTO = new ExcelHeaderDetailDTO();
                detailDTO.setStartIndex(i);
                headerDTO.getDetailMap().put(titleName, detailDTO);
            }
            if (singleF.contains(titleName)) {
                ExcelHeaderDetailDTO detailDTO = new ExcelHeaderDetailDTO();
                detailDTO.setStartIndex(i);
                fDTOMapTemp.put(titleName, detailDTO);
            }
            if (singleC.contains(titleName)) {
                ExcelHeaderDetailDTO detailDTO = new ExcelHeaderDetailDTO();
                detailDTO.setStartIndex(i);
                cDTOMapTemp.put(titleName, detailDTO);
            }
            if (singleCu.contains(titleName)) {
                ExcelHeaderDetailDTO detailDTO = new ExcelHeaderDetailDTO();
                detailDTO.setStartIndex(i);
                cuDTOMapTemp.put(titleName, detailDTO);
            }
            headerDTO.getHearderIndexMap().put(i, titleName);
        }
        if (!fDTOMapTemp.isEmpty()) {
            for (Map.Entry<String, ExcelHeaderDetailDTO> mDTO : fDTOMapTemp.entrySet()) {
                for (int i = mDTO.getValue().getStartIndex() + 1; i < list.size(); i++) {
                    String titleName = list.get(i).toString().replaceAll("\\s*|\r|\n|\t", "");
                    if (!StringUtils.replaceBlank(titleName).endsWith("接触人数")) {
                        mDTO.getValue().setEndIndex(i - 1);
                        break;
                    }
                }
            }
            headerDTO.getDetailMap().putAll(fDTOMapTemp);
        }
        if (!cDTOMapTemp.isEmpty()) {
            for (Map.Entry<String, ExcelHeaderDetailDTO> mDTO : cDTOMapTemp.entrySet()) {
                for (int i = mDTO.getValue().getStartIndex() + 5; i < list.size(); i += 5) {
                    String titleName = list.get(i).toString().replaceAll("\\s*|\r|\n|\t", "");
                    if (!"超标岗位数".equals(StringUtils.replaceBlank(titleName))) {
                        mDTO.getValue().setEndIndex(i - 5);
                        break;
                    }
                }
            }
            headerDTO.getDetailMap().putAll(cDTOMapTemp);
        }
        if (!cuDTOMapTemp.isEmpty()) {
            for (Map.Entry<String, ExcelHeaderDetailDTO> mDTO : cuDTOMapTemp.entrySet()) {
                for (int i = mDTO.getValue().getStartIndex() + 5; i < list.size(); i += 5) {
                    String titleName = list.get(i).toString().replaceAll("\\s*|\r|\n|\t", "");
                    if (!"异常人数".equals(StringUtils.replaceBlank(titleName))) {
                        mDTO.getValue().setEndIndex(i - 5);
                        break;
                    }
                }
            }
            headerDTO.getDetailMap().putAll(cuDTOMapTemp);
        }
    }

    /**
     * 返回列数
     *
     * @return 列数
     */
    private int returnColNum() {
        int num = 0;
        if (new Integer(0).equals(this.importDataType)) {
            num = 18;
        } else if (new Integer(1).equals(this.importDataType)) {
            num = headerDTO.getHearderIndexMap().size();
        } else if (new Integer(2).equals(this.importDataType)) {
            num = 38;
        }
        return num;
    }

    /**
     * 返回列头行数
     *
     * @return 列数
     */
    private int returnRowNum() {
        int num = 0;
        if (new Integer(0).equals(this.importDataType)) {
            num = 1;
        } else if (new Integer(1).equals(this.importDataType)) {
            num = 1;
        } else if (new Integer(2).equals(this.importDataType)) {
            num = 2;
        }
        return num;
    }

    /**
     * 处理数据，保证list长度一致
     *
     * @param excelRowList 导入数据
     */
    private void dealExcelRowList(List<List<Object>> excelRowList, int size) {
        if (ObjectUtil.isEmpty(excelRowList)) {
            return;
        }
        for (List<Object> objects : excelRowList) {
            if (ObjectUtil.isEmpty(objects)) {
                continue;
            }
            int size1 = objects.size();
            if (size1 < size) {
                while (size1++ < size) {
                    objects.add(null);
                }
            }
        }
    }

    /**
     * 封装大类为12的对照
     */
    private void pakContrastMap() {
        this.contrastMap = new HashMap<>(16);
        List<TsContraSub> allTsContraSubList = this.commService.getContraSubListByMainContraCode("12");
        for (TsContraSub contraSub : allTsContraSubList) {
            if (!this.contrastMap.containsKey(contraSub.getBusiType())) {
                this.contrastMap.put(contraSub.getBusiType(), new HashMap<String, String>(16));
            }
            this.contrastMap.get(contraSub.getBusiType()).put(contraSub.getLeftCode().replaceAll("\\s*|\r|\n|\t",""), contraSub.getRightCode().replaceAll("\\s*|\r|\n|\t",""));
        }
    }

    /**
     * 验证数据，并返回错误
     *
     * @param excelRowList 数据
     * @param rowSize      列头行数
     * @return 错误信息Map
     */
    private Map<Integer, String> verifyData(List<List<Object>> excelRowList, int rowSize) {
        Map<Integer, String> errorMap = new HashMap<>(16);
        if (new Integer(0).equals(this.importDataType)) {
            errorMap = verifyDataBaseInfo(excelRowList, rowSize);
        } else if (new Integer(1).equals(this.importDataType)) {
             errorMap = verifyDataCheckInfo(excelRowList, rowSize);
        } else if (new Integer(2).equals(this.importDataType)) {
            errorMap = verifyDataResultInfo(excelRowList, rowSize);
        }
        return errorMap;
    }

    /**
     * 验证数据，并返回错误(基本信息)
     *
     * @param excelRowList 数据
     * @param rowSize      列头行数
     * @return 错误信息Map
     */
    private Map<Integer, String> verifyDataBaseInfo(List<List<Object>> excelRowList, int rowSize) {
        if (ObjectUtil.isEmpty(excelRowList)) {
            return new HashMap<>(16);
        }
        Map<String, Integer> uuidMap = new HashMap<>(16);
        Map<Integer, String> errorMap = new HashMap<>(16);
        for (int i = 0, excelRowListSize = excelRowList.size(); i < excelRowListSize; i++) {
            int index = i + rowSize;
            if (ObjectUtil.isEmpty(excelRowList.get(i))) {
                putErrorOfMap(errorMap, index, "企业ID为空");
                continue;
            }
            List<Object> objects = excelRowList.get(i);
            if (ObjectUtil.isEmpty(StringUtils.objectToString(objects.get(0)))) {
                putErrorOfMap(errorMap, index, "企业ID为空");
                continue;
            }
            String uuid = StringUtils.objectToString(objects.get(0));
            if (uuidMap.containsKey(uuid)) {
                putErrorOfMap(errorMap, uuidMap.get(uuid), "企业ID重复");
                putErrorOfMap(errorMap, index, "企业ID重复");
            } else {
                uuidMap.put(uuid, index);
            }
            String msg;
            //所属区域
            msg = "所属区域[" + StringUtils.objectToString(objects.get(2)) + "]在平台上对照不存在";
            putContrastErrorOfMap(StringUtils.objectToString(objects.get(2)), 1, errorMap, index, msg);
            String contrastRight = getContrastByBusType(1, StringUtils.objectToString(objects.get(2)));
            if(StringUtils.isNotBlank(contrastRight) && null == this.zoneMap.get(contrastRight)){
                msg = "所属区域[" + StringUtils.objectToString(objects.get(2)) + "]在平台上不存在或者已停用";
                putErrorOfMap(errorMap, index, msg);
            }

            //是否重点行业
            msg = "是否重点行业[" + StringUtils.objectToString(objects.get(4)) + "]在平台上对照不存在";
            putContrastErrorOfMap(StringUtils.objectToString(objects.get(4)), 42, errorMap, index, msg);
            //行业类别
            if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(objects.get(8)))) {
                //若小类不为空则直接对照小类
                msg = "行业类别[" + StringUtils.objectToString(objects.get(8)) + "]在平台上对照不存在";
                putContrastErrorOfMap(StringUtils.objectToString(objects.get(8)), 2, errorMap, index, msg);
                contrastRight = getContrastByBusType(2, StringUtils.objectToString(objects.get(8)));
                if(StringUtils.isNotBlank(contrastRight) && null != this.simpleCodeMap.get("5002")
                        && null == this.simpleCodeMap.get("5002").get(contrastRight)){
                    msg = "行业类别[" + StringUtils.objectToString(objects.get(8)) + "]在平台上不存在或者已经停用";
                    putErrorOfMap(errorMap, index, msg);
                }
            } else {
                //若小类为空则去对照中类
                msg = "行业类别[" + StringUtils.objectToString(objects.get(7)) + "]在平台上对照不存在";
                putContrastErrorOfMap(StringUtils.objectToString(objects.get(7)), 2, errorMap, index, msg);
                contrastRight = getContrastByBusType(2, StringUtils.objectToString(objects.get(7)));
                if(StringUtils.isNotBlank(contrastRight) && null != this.simpleCodeMap.get("5002")
                        && null == this.simpleCodeMap.get("5002").get(contrastRight)){
                    msg = "行业类别[" + StringUtils.objectToString(objects.get(7)) + "]在平台上不存在或者已经停用";
                    putErrorOfMap(errorMap, index, msg);
                }
            }
            //经济类型
            msg = "经济类型[" + StringUtils.objectToString(objects.get(9)) + "]在平台上对照不存在";
            putContrastErrorOfMap(StringUtils.objectToString(objects.get(9)), 3, errorMap, index, msg);
            contrastRight = getContrastByBusType(3, StringUtils.objectToString(objects.get(9)));
            if(StringUtils.isNotBlank(contrastRight) && null != this.simpleCodeMap.get("5003")
                    && null == this.simpleCodeMap.get("5003").get(contrastRight)){
                msg = "经济类型[" + StringUtils.objectToString(objects.get(9)) + "]在平台上不存在或者已经停用";
                putErrorOfMap(errorMap, index, msg);
            }
            //企业规模
            msg = "企业规模[" + StringUtils.objectToString(objects.get(10)) + "]在平台上对照不存在";
            putContrastErrorOfMap(StringUtils.objectToString(objects.get(10)), 4, errorMap, index, msg);
            contrastRight = getContrastByBusType(4, StringUtils.objectToString(objects.get(10)));
            if(StringUtils.isNotBlank(contrastRight) && null != this.simpleCodeMap.get("5004")
                    && null == this.simpleCodeMap.get("5004").get(contrastRight)){
                msg = "企业规模[" + StringUtils.objectToString(objects.get(10)) + "]在平台上不存在或者已经停用";
                putErrorOfMap(errorMap, index, msg);
            }
            //是否申报
            msg = "是否申报[" + StringUtils.objectToString(objects.get(11)) + "]在平台上对照不存在";
            putContrastErrorOfMap(StringUtils.objectToString(objects.get(11)), 30, errorMap, index, msg);
            //是否年度更新
            msg = "是否年度更新[" + StringUtils.objectToString(objects.get(12)) + "]在平台上对照不存在";
            putContrastErrorOfMap(StringUtils.objectToString(objects.get(12)), 31, errorMap, index, msg);
            //在册职工数
            msg = "在册职工数[" + StringUtils.objectToString(objects.get(13)) + "]应为整数";
            putConvertErrorOfMap(1, objects.get(13), errorMap, index, msg);
            //外委人员数
            msg = "外委人员数[" + StringUtils.objectToString(objects.get(14)) + "]应为整数";
            putConvertErrorOfMap(1, objects.get(14), errorMap, index, msg);
            //用人单位负责人培训情况
            msg = "用人单位负责人培训情况[" + StringUtils.objectToString(objects.get(15)) + "]在平台上对照不存在";
            putContrastErrorOfMap(StringUtils.objectToString(objects.get(15)), 32, errorMap, index, msg);
            //职业卫生管理人员培训情况
            msg = "职业卫生管理人员培训情况[" + StringUtils.objectToString(objects.get(16)) + "]在平台上对照不存在";
            putContrastErrorOfMap(StringUtils.objectToString(objects.get(16)), 32, errorMap, index, msg);
            //接触职业病危害劳动者培训人数
            msg = "接触职业病危害劳动者培训人数[" + StringUtils.objectToString(objects.get(17)) + "]应为整数";
            putConvertErrorOfMap(1, objects.get(17), errorMap, index, msg);
        }
        return errorMap;
    }

    /**
     * 验证数据，并返回错误(调查信息)
     */
    private Map<Integer, String> verifyDataCheckInfo(List<List<Object>> excelRowList, int rowSize) {
        if (ObjectUtil.isEmpty(headerDTO.getDetailMap())) {
            return new HashMap<>(16);
        }
        Map<String, Integer> uuidMap = new HashMap<>(16);
        Map<Integer, String> errorMap = new HashMap<>(16);
        Map<String, ExcelHeaderDetailDTO> detailMap = headerDTO.getDetailMap();
        Map<Integer, String> hearderIndexMap = headerDTO.getHearderIndexMap();
        //表头涉及危害因素校验
        StringBuilder erroMsg = new StringBuilder();
        String[] factorcrowdIndexHeader = headerDTO.getFactorcrowdIndexHeader();
        verifyBadrsn(factorcrowdIndexHeader, detailMap, hearderIndexMap, erroMsg, 1, "接触人数");

        String[] chkIndexHeader = headerDTO.getChkIndexHeader();
        verifyBadrsn(chkIndexHeader, detailMap, hearderIndexMap, erroMsg, 5, "有无检测");

        String[] cusIndexHeader = headerDTO.getCusIndexHeader();
        verifyBadrsn(cusIndexHeader, detailMap, hearderIndexMap, erroMsg, 5, "有无体检");
        if (erroMsg.length() > 0) {
            for (int i = 0, excelRowListSize = excelRowList.size(); i < excelRowListSize; i++) {
                int index = i + rowSize;
                if (index == 1) {
                    putErrorOfMap(errorMap, 1, erroMsg.substring(1));
                } else {
                    putErrorOfMap(errorMap, index, null);
                }
            }
        }
        //行数据校验
        for (int i = 0, excelRowListSize = excelRowList.size(); i < excelRowListSize; i++) {
            int index = i + rowSize;
            List<Object> objects = excelRowList.get(i);

            if (detailMap != null && detailMap.get("企业ID") != null) {
                //判空
                if (objects==null||ObjectUtil.isEmpty(StringUtils.objectToString(objects.get(detailMap.get("企业ID").getStartIndex())))) {
                    putErrorOfMap(errorMap, index, "企业ID为空");
                    continue;
                } else {
                    //判断数据库中该企业ID是否已经存在
                    String uuid = StringUtils.objectToString(objects.get(detailMap.get("企业ID").getStartIndex()));
                    TdZxjcUnitbasicinfo unitBasicInfo = this.siteMonitoringDataService.findUnitBasicInfoByUuid(uuid);
                    if (ObjectUtil.isEmpty(unitBasicInfo.getRid())) {
                        putErrorOfMap(errorMap, index, "该单位的基本信息未导入");
                        continue;
                    }
                    //判重
                    if (uuidMap.containsKey(uuid)) {
                        putErrorOfMap(errorMap, uuidMap.get(uuid), "企业ID重复");
                        putErrorOfMap(errorMap, index, "企业ID重复");
                    } else {
                        uuidMap.put(uuid, index);
                    }
                }
            }

            String currVal = StringUtils.objectToString(objects.get(detailMap.get("当前工作阶段").getStartIndex()));
            if (detailMap.get("当前工作阶段") != null && !UnitbasicinfoUtils.ifMatcherNull(currVal)) {
                //当前工作阶段
                String[] vals = currVal.split(" ");
                for (String val : vals) {
                    putErrorMsg("当前工作阶段", val, 33, errorMap, index);
                }
            }
            String[] singleIndexHeader = headerDTO.getSingleIndexHeader();
            if (singleIndexHeader != null && singleIndexHeader.length > 0) {
                for (String header : singleIndexHeader) {
                    if (!"企业ID".equals(header) &&
                            !"当前工作阶段".equals(header) &&
                            !"接触危害总人数".equals(header) &&
                            !"体检总人数".equals(header)) {
                        String value = StringUtils.objectToString(objects.get(detailMap.get(header).getStartIndex()));
                        if (detailMap.get(header) != null && !UnitbasicinfoUtils.ifMatcherNull(value)) {
                            Integer type = UnitbasicinfoUtils.getType(header);
                            if (type != null) {
                                putErrorMsg(header, value, type, errorMap, index);
                            }
                        }
                    }
                }
            }

            //上一年度检测情况  有无检测 对照
            String[] chkIndexHeaders = headerDTO.getChkIndexHeader();
            for (int i1 = 0; i1 < chkIndexHeaders.length; i1++) {
                String value = StringUtils.objectToString(objects.get(detailMap.get(chkIndexHeaders[i1]).getStartIndex()));
                if (detailMap.get(chkIndexHeaders[i1]) != null && !UnitbasicinfoUtils.ifMatcherNull(value)) {
                    Integer type = UnitbasicinfoUtils.getType(chkIndexHeaders[i1]);
                    if (type != null) {
                        putErrorMsg(chkIndexHeaders[i1], value, type, errorMap, index);
                    }
                }
                if (StringUtils.isNotBlank(value) && contrastMap.get(43) != null && "1".equals(contrastMap.get(43).get(value))) {
                    ExcelHeaderDetailDTO detailDTO = detailMap.get(chkIndexHeaders[i1]);
                    for (int k = detailDTO.getStartIndex() + 1; k <= detailDTO.getEndIndex(); k += 5) {
                        String header=  hearderIndexMap.get(k);
                        if (!UnitbasicinfoUtils.ifMatcherNull(objects.get(k).toString())) {
                            Integer type = UnitbasicinfoUtils.getType(chkIndexHeaders[i1]);
                            if (type != null) {
                                putErrorMsg(header, objects.get(k).toString(), type, errorMap, index);
                            }
                        }
                    }
                }
            }
            //上一年度在岗期间职业健康检查情况
            String[] cusIndexHeaders = headerDTO.getCusIndexHeader();
            for (int i1 = 0; i1 < cusIndexHeaders.length; i1++) {
                String value = StringUtils.objectToString(objects.get(detailMap.get(cusIndexHeaders[i1]).getStartIndex()));
                if (detailMap.get(cusIndexHeaders[i1]) != null && !UnitbasicinfoUtils.ifMatcherNull(value)) {
                    Integer type = UnitbasicinfoUtils.getType(cusIndexHeaders[i1]);
                    if (type != null) {
                        putErrorMsg(cusIndexHeaders[i1], value, type, errorMap, index);
                    }
                }
                if (StringUtils.isNotBlank(value) && contrastMap.get(44) != null && "1".equals(contrastMap.get(44).get(value))) {
                    ExcelHeaderDetailDTO detailDTO = detailMap.get(cusIndexHeaders[i1]);
                    for (int k = detailDTO.getStartIndex() + 1; k <= detailDTO.getEndIndex(); k += 5) {
                        String header = hearderIndexMap.get(k);
                        if (!UnitbasicinfoUtils.ifMatcherNull(objects.get(k).toString())) {
                            putErrorMsg(header, objects.get(k).toString(), 42, errorMap, index);
                        }
                    }
                }
            }
        }
        return errorMap;
    }


    /**
     * <p>方法描述：检验表头中涉及的危害因素是否对照成功</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-09-13
     **/
    public void verifyBadrsn(String[] headerNames, Map<String, ExcelHeaderDetailDTO> detailMap,
                             Map<Integer, String> hearderIndexMap, StringBuilder erroMsg, Integer stepSize, String relapceStr) {
        for (String name : headerNames) {
            ExcelHeaderDetailDTO detailDTO = detailMap.get(name);
            for (int i = detailDTO.getStartIndex() + 1; i <= detailDTO.getEndIndex(); i += stepSize) {
                String hearderName = hearderIndexMap.get(i).replace(relapceStr, "").replaceAll("\\s*|\r|\n|\t","");
                if(!ObjectUtil.isEmpty(contrastMap.get(5))){
                    String rightCode = contrastMap.get(5).get(hearderName);
                    if (StringUtils.isBlank(rightCode)) {
                        erroMsg.append("；第").append(i).append("列危害因素[").append(hearderIndexMap.get(i).replace(relapceStr, "")).append("]在平台上对照不存在，以下列均有此问题");
                    }else if(null != this.simpleCodeMap.get("5007") && null == this.simpleCodeMap.get("5007").get(rightCode)){
                        erroMsg.append("；第").append(i).append("列危害因素[").append(hearderIndexMap.get(i).replace(relapceStr, "")).append("]在平台上不存在或者已经停用，以下列均有此问题");
                    }
                }

            }
        }
    }


    /**
     * <p>方法描述：封装对照 错误信息</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-09-12
     **/
    public void putErrorMsg(String hearder, String value, Integer type, Map<Integer, String> errorMap, Integer index) {
        String msg = hearder + "[" + value + "]在平台上对照不存在";
        putContrastErrorOfMap(value, type, errorMap, index, msg);
    }


    /**
     * 验证数据，并返回错误(结果信息)
     *
     * @param excelRowList 数据
     * @param rowSize      列头行数
     * @return 错误信息Map
     */
    private Map<Integer, String> verifyDataResultInfo(List<List<Object>> excelRowList, int rowSize) {
        if (ObjectUtil.isEmpty(excelRowList)) {
            return new HashMap<>(16);
        }
        Map<Integer, String> errorMap = new HashMap<>(16);

        for (int i = 0, excelRowListSize = excelRowList.size(); i < excelRowListSize; i++) {
            int index = i + rowSize;
            if (ObjectUtil.isEmpty(excelRowList.get(i))) {
                putErrorOfMap(errorMap, index, "企业ID为空");
                continue;
            }
            List<Object> objects = excelRowList.get(i);
            if (ObjectUtil.isEmpty(StringUtils.objectToString(objects.get(1)))) {
                putErrorOfMap(errorMap, index, "企业ID为空");
                continue;
            }
            String uuid = StringUtils.objectToString(objects.get(1));
            TdZxjcUnitbasicinfo unitBasicInfo = this.siteMonitoringDataService.findUnitBasicInfoByUuid(uuid);
            if (ObjectUtil.isEmpty(unitBasicInfo.getRid())) {
                putErrorOfMap(errorMap, index, "该单位的基本信息未导入");
                continue;
            }

            String msg;
            //有机溶剂开展定性分析
            msg = "有机溶剂开展定性分析[" + StringUtils.objectToString(objects.get(8)) + "]在平台上对照不存在";
            putContrastErrorOfMap(StringUtils.objectToString(objects.get(8)), 39, errorMap, index, msg);
            //检测因素
            msg = "检测因素[" + StringUtils.objectToString(objects.get(9)) + "]在平台上对照不存在";
            putContrastErrorOfMap(StringUtils.objectToString(objects.get(9)), 5, errorMap, index, msg);
            //岗位是否合格
            msg = "岗位是否合格[" + StringUtils.objectToString(objects.get(13)) + "]在平台上对照不存在";
            putContrastErrorOfMap(StringUtils.objectToString(objects.get(13)), 40, errorMap, index, msg);
            //Ctwa是否合格
            msg = "Ctwa是否合格[" + StringUtils.objectToString(objects.get(14)) + "]在平台上对照不存在";
            putContrastErrorOfMap(StringUtils.objectToString(objects.get(14)), 40, errorMap, index, msg);
            //检测场所是否合格
            msg = "检测场所是否合格[" + StringUtils.objectToString(objects.get(15)) + "]在平台上对照不存在";
            putContrastErrorOfMap(StringUtils.objectToString(objects.get(15)), 40, errorMap, index, msg);
            //作业人数
            msg = "作业人数[" + StringUtils.objectToString(objects.get(16)) + "]应为整数";
            putConvertErrorOfMap(1, objects.get(16), errorMap, index, msg);
            //监测类型
            msg = "监测类型[" + StringUtils.objectToString(objects.get(21)) + "]在平台上对照不存在";
            putContrastErrorOfMap(StringUtils.objectToString(objects.get(21)), 41, errorMap, index, msg);
            //监测项目
            msg = "监测项目[" + StringUtils.objectToString(objects.get(24)) + "]在平台上对照不存在";
            if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(objects.get(24)))) {
                String codeName = StringUtils.objectToString(objects.get(24));
                if (ObjectUtil.isEmpty(this.simpleCodeMap.get("5581"))
                        || !this.simpleCodeMap.get("5581").containsKey(codeName)) {
                    putErrorOfMap(errorMap, index, msg);
                }
            }
            //接触时间
            msg = "接触时间[" + StringUtils.objectToString(objects.get(25)) + "]应为数字";
            putConvertErrorOfMap(2, objects.get(25), errorMap, index, msg);
        }
        return errorMap;
    }

    /**
     * 封装对照错误信息
     *
     * @param dataStr  需要对照的数据
     * @param type     对照业务类型
     * @param errorMap 错误信息Map
     * @param key      行
     * @param msg      对照错误提示
     */
    private void putContrastErrorOfMap(String dataStr, int type, Map<Integer, String> errorMap, Integer key, String msg) {
        if (ObjectUtil.isEmpty(dataStr)) {
            return;
        }
        if (UnitbasicinfoUtils.ifMatcherNull(dataStr)) {
            return;
        }
        dataStr = dataStr.replaceAll("\\s*|\r|\n|\t","");
        if (ObjectUtil.isNotEmpty(this.contrastMap.get(type)) && !this.contrastMap.get(type).containsKey(dataStr)) {
            putErrorOfMap(errorMap, key, msg);
        }
    }

    /**
     * 封装类型转换错误信息
     *
     * @param type     目标类型 1: Integer 2: BigDecimal
     * @param data     需要类型转换的数据
     * @param errorMap 错误信息Map
     * @param key      行
     * @param msg      错误提示
     */
    private void putConvertErrorOfMap(int type, Object data, Map<Integer, String> errorMap, Integer key, String msg) {
        if (ObjectUtil.isEmpty(data)) {
            return;
        }
        if (UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(data))) {
            return;
        }
        if (ObjectUtil.isEmpty(ObjectUtil.convert(type == 1 ? Integer.class : BigDecimal.class, data))) {
            putErrorOfMap(errorMap, key, msg);
        }
    }

    /**
     * 封装错误信息到Map
     *
     * @param errorMap 错误信息Map
     * @param key      行
     * @param msg      错误提示
     */
    private void putErrorOfMap(Map<Integer, String> errorMap, Integer key, String msg) {
        if (StringUtils.isBlank(msg) && !errorMap.containsKey(key)) {
            errorMap.put(key, "");
            return;
        }
        if (errorMap.containsKey(key)) {
            String value = msg;
            if (ObjectUtil.isNotEmpty(errorMap.get(key))) {
                value = errorMap.get(key) + "；" + msg;
            }
            errorMap.put(key, value);
        } else {
            errorMap.put(key, msg);
        }
    }

    /**
     * 封装数据并保存
     *
     * @param excelRowList 原数据
     * @param errorMap     错误信息Map
     * @param rowSize      列头行数
     */
    private void pakDataAndSave(List<List<Object>> excelRowList, Map<Integer, String> errorMap, int rowSize) {
        if (ObjectUtil.isEmpty(excelRowList)) {
            return;
        }

        //key uuid value 基本信息
        Map<String,TdZxjcUnitbasicinfo> basicInfoMap = new HashMap<>();
        Map<String, String> errMap = null;
        if(null != this.importDataType){
            //uuid 用于查询对应的调查信息和结果信息（调查导入时查询结果信息是否存在 结果导入时查询调查信息是否存在 基本信息修改时结果与调查信息是否存在）
            List<String> uuidList = new ArrayList<>();
            Map<String,List<Object>> rowDataMap = new HashMap<>();
            for(int i = 0, excelRowListSize = excelRowList.size(); i < excelRowListSize; i++){
                if (errorMap.containsKey(i + rowSize)) {
                    continue;
                }
                List<Object> excelRowData = excelRowList.get(i);
                String uuid =  StringUtils.objectToString(excelRowData.get(1 == this.importDataType || 0 == this.importDataType ? 0 : 1));
                if(StringUtils.isNotBlank(uuid)){
                    uuidList.add(uuid);
                }
                if(1 == this.importDataType){
                    rowDataMap.put(uuid, excelRowData);
                }else if(0 == this.importDataType){
                    TdZxjcUnitbasicinfo unitbasicinfo = pakDataBaseInfo(excelRowData);
                    basicInfoMap.put(uuid, unitbasicinfo);
                }
            }
            if(0 != this.importDataType){
                basicInfoMap = this.siteMonitoringDataService.findBasicInfoByUuid(uuidList);
                // 查询需要调用同步用人单位信息接口的uuid
                uuidList = this.siteMonitoringDataService.findUUidForSyncCrpt(uuidList, 1 == this.importDataType, false);
                errMap = this.fillCrptIdAndResultErrMap(rowDataMap, uuidList, basicInfoMap);
            }else{
                // 查询需要调用同步用人单位信息接口的uuid
                uuidList = this.siteMonitoringDataService.findUUidForSyncCrpt(uuidList, null, true);
                errMap = this.fillCrptIdAndResultErrMap(null, uuidList, basicInfoMap);
            }
        }

        //导入结果信息需要批量保存
        List<TdZxjcResultPro> resultProList = new ArrayList<>();
        for (int i = 0, excelRowListSize = excelRowList.size(); i < excelRowListSize; i++) {
            List<Object> excelRowData = excelRowList.get(i);
            if (errorMap.containsKey(i + rowSize)) {
                continue;
            }
            String uuid = StringUtils.objectToString(excelRowData.get(1 == this.importDataType || 0 == this.importDataType ? 0 : 1));
            String errMsg = CollectionUtils.isEmpty(errMap) || StringUtils.isBlank(uuid) ? null : errMap.get(uuid);
            //存在调用同步用人单位接口 错误信息
            if(StringUtils.isNotBlank(errMsg)){
                errorMap.put(i + rowSize, errMsg);
                continue;
            }
            try {
                if (new Integer(0).equals(this.importDataType)) {
                    TdZxjcUnitbasicinfo unitBasicInfo = basicInfoMap.get(uuid);
                    if(null != unitBasicInfo){
                        this.commService.upsertEntity(unitBasicInfo);
                    }
                } else if (new Integer(1).equals(this.importDataType)) {
                    TdZxjcUnitbasicinfo unitBasicInfo = basicInfoMap.get(uuid);
                    if(null == unitBasicInfo){
                        unitBasicInfo = this.siteMonitoringDataService.findUnitBasicInfoByUuid(uuid);
                    }
                    this.siteMonitoringDataService.dealAllDate(unitBasicInfo, headerDTO, excelRowData, this.contrastMap, simpleCodeMap.get("5007"));
                } else if (new Integer(2).equals(this.importDataType)) {
                    TdZxjcUnitbasicinfo unitBasicInfo = basicInfoMap.get(uuid);
                    if(null == unitBasicInfo){
                        unitBasicInfo = this.siteMonitoringDataService.findUnitBasicInfoByUuid(uuid);
                    }
                    pakData2(excelRowData, resultProList, unitBasicInfo);
                }
            } catch (Exception e) {
                e.printStackTrace();
                putErrorOfMap(errorMap, i + rowSize, e.getMessage());
            }
        }
        if (new Integer(2).equals(this.importDataType) && ObjectUtil.isNotEmpty(resultProList)) {
            removeErrorData(excelRowList, errorMap, resultProList, rowSize);
            this.siteMonitoringDataService.insertResultProBatch(resultProList);
        }
    }

    /**
     * 封装数据并保存(基本信息)
     *
     * @param excelRowData 原数据
     */
    private TdZxjcUnitbasicinfo pakDataBaseInfo(List<Object> excelRowData) {
        String uuid = StringUtils.objectToString(excelRowData.get(0));
        TdZxjcUnitbasicinfo unitBasicInfo = this.siteMonitoringDataService.findUnitBasicInfoByUuid(uuid);
        //年份
        unitBasicInfo.setYear(this.importYear);
        //企业ID
        unitBasicInfo.setUuid(uuid);
        //单位名称
        unitBasicInfo.setUnitName(StringUtils.objectToString(excelRowData.get(1)));
        //所属区域
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(2)))) {
            String contrastRight = getContrastByBusType(1, StringUtils.objectToString(excelRowData.get(2)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                unitBasicInfo.setFkByZoneId(null);
            } else {
                unitBasicInfo.setFkByZoneId(this.zoneMap.get(contrastRight));
            }
        } else {
            unitBasicInfo.setFkByZoneId(null);
        }
        //信用代码
        unitBasicInfo.setCreditCode(StringUtils.objectToString(excelRowData.get(3)));
        //是否重点行业
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(4)))) {
            String contrastRight = getContrastByBusType(42, StringUtils.objectToString(excelRowData.get(4)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                unitBasicInfo.setIfKeyIndustry(null);
            } else {
                unitBasicInfo.setIfKeyIndustry(ObjectUtil.convert(Integer.class, contrastRight));
            }
        } else {
            unitBasicInfo.setIfKeyIndustry(null);
        }
        //行业类别 5002
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(8)))) {
            //小类不为空
            String contrastRight = getContrastByBusType(2, StringUtils.objectToString(excelRowData.get(8)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                unitBasicInfo.setFkByIndustryId(null);
            } else {
                unitBasicInfo.setFkByIndustryId(this.simpleCodeMap.get("5002").get(contrastRight));
            }
        } else if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(7)))) {
            //小类为空直接找中类
            String contrastRight = getContrastByBusType(2, StringUtils.objectToString(excelRowData.get(7)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                unitBasicInfo.setFkByIndustryId(null);
            } else {
                unitBasicInfo.setFkByIndustryId(this.simpleCodeMap.get("5002").get(contrastRight));
            }
        } else {
            unitBasicInfo.setFkByIndustryId(null);
        }
        //经济类型 5003
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(9)))) {
            String contrastRight = getContrastByBusType(3, StringUtils.objectToString(excelRowData.get(9)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                unitBasicInfo.setFkByEconomicId(null);
            } else {
                unitBasicInfo.setFkByEconomicId(this.simpleCodeMap.get("5003").get(contrastRight));
            }
        } else {
            unitBasicInfo.setFkByEconomicId(null);
        }
        //企业规模 5004
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(10)))) {
            String contrastRight = getContrastByBusType(4, StringUtils.objectToString(excelRowData.get(10)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                unitBasicInfo.setFkByEnterpriseScaleId(null);
            } else {
                unitBasicInfo.setFkByEnterpriseScaleId(this.simpleCodeMap.get("5004").get(contrastRight));
            }
        } else {
            unitBasicInfo.setFkByEnterpriseScaleId(null);
        }
        //是否申报
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(11)))) {
            String contrastRight = getContrastByBusType(30, StringUtils.objectToString(excelRowData.get(11)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                unitBasicInfo.setIfDeclare(null);
            } else {
                unitBasicInfo.setIfDeclare(ObjectUtil.convert(Integer.class, contrastRight));
            }
        } else {
            unitBasicInfo.setIfDeclare(null);
        }
        //是否年度更新
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(12)))) {
            String contrastRight = getContrastByBusType(31, StringUtils.objectToString(excelRowData.get(12)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                unitBasicInfo.setIfAnnualUpdate(null);
            } else {
                unitBasicInfo.setIfAnnualUpdate(ObjectUtil.convert(Integer.class, contrastRight));
            }
        } else {
            unitBasicInfo.setIfAnnualUpdate(null);
        }
        //在册职工数
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(13)))) {
            unitBasicInfo.setEmpNum(ObjectUtil.convert(Integer.class, excelRowData.get(13)));
        } else {
            unitBasicInfo.setEmpNum(null);
        }
        //外委人员数
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(14)))) {
            unitBasicInfo.setExternalNum(ObjectUtil.convert(Integer.class, excelRowData.get(14)));
        } else {
            unitBasicInfo.setExternalNum(null);
        }
        //用人单位负责人培训情况
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(15)))) {
            String contrastRight = getContrastByBusType(32, StringUtils.objectToString(excelRowData.get(15)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                unitBasicInfo.setIfLeadersTrain(null);
            } else {
                unitBasicInfo.setIfLeadersTrain(ObjectUtil.convert(Integer.class, contrastRight));
            }
        } else {
            unitBasicInfo.setIfLeadersTrain(null);
        }
        //职业卫生管理人员培训情况
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(16)))) {
            String contrastRight = getContrastByBusType(32, StringUtils.objectToString(excelRowData.get(16)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                unitBasicInfo.setIfManagersTrain(null);
            } else {
                unitBasicInfo.setIfManagersTrain(ObjectUtil.convert(Integer.class, contrastRight));
            }
        } else {
            unitBasicInfo.setIfManagersTrain(null);
        }
        //接触职业病危害劳动者培训人数
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(17)))) {
            unitBasicInfo.setTrainSum(ObjectUtil.convert(Integer.class, excelRowData.get(17)));
        } else {
            unitBasicInfo.setTrainSum(null);
        }
        unitBasicInfo.setCreateDate(new Date());
        unitBasicInfo.setCreateManid(Global.getUser().getRid());
        return unitBasicInfo;
    }

    /**
     * 封装数据并保存(结果信息)
     *
     * @param excelRowData  原数据
     * @param resultProList 封装后的数据List
     */
    private void pakData2(List<Object> excelRowData, List<TdZxjcResultPro> resultProList, TdZxjcUnitbasicinfo unitBasicInfo) {
        //有机溶剂开展定性分析
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(8)))) {
            String contrastRight = getContrastByBusType(39, StringUtils.objectToString(excelRowData.get(8)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                unitBasicInfo.setIfAnalysis(null);
            } else {
                unitBasicInfo.setIfAnalysis(ObjectUtil.convert(Integer.class, contrastRight));
            }
        } else {
            unitBasicInfo.setIfAnalysis(null);
        }

        TdZxjcResultPro resultPro = new TdZxjcResultPro();
        resultPro.setFkByMainId(unitBasicInfo);
        //检测因素 5007
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(9)))) {
            String contrastRight = getContrastByBusType(5, StringUtils.objectToString(excelRowData.get(9)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                resultPro.setFkByFactorId(null);
            } else {
                resultPro.setFkByFactorId(this.simpleCodeMap.get("5007").get(contrastRight));
            }
        } else {
            resultPro.setFkByFactorId(null);
        }
        //岗位UUID
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(10)))) {
            resultPro.setJobId(StringUtils.objectToString(excelRowData.get(10)));
        } else {
            resultPro.setJobId(null);
        }
        //岗位/环节
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(11)))) {
            resultPro.setJobName(StringUtils.objectToString(excelRowData.get(11)));
        } else {
            resultPro.setJobName(null);
        }
        //其他岗位描述
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(12)))) {
            resultPro.setJobNameOther(StringUtils.objectToString(excelRowData.get(12)));
        } else {
            resultPro.setJobNameOther(null);
        }
        //岗位是否合格
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(13)))) {
            String contrastRight = getContrastByBusType(40, StringUtils.objectToString(excelRowData.get(13)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                resultPro.setIfQualified(null);
            } else {
                resultPro.setIfQualified(ObjectUtil.convert(Integer.class, contrastRight));
            }
        } else {
            resultPro.setIfQualified(null);
        }
        //Ctwa是否合格
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(14)))) {
            String contrastRight = getContrastByBusType(40, StringUtils.objectToString(excelRowData.get(14)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                resultPro.setIfCtwaHg(null);
            } else {
                resultPro.setIfCtwaHg(ObjectUtil.convert(Integer.class, contrastRight));
            }
        } else {
            resultPro.setIfCtwaHg(null);
        }
        //检测场所是否合格
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(15)))) {
            String contrastRight = getContrastByBusType(40, StringUtils.objectToString(excelRowData.get(15)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                resultPro.setIfPlaceHg(null);
            } else {
                resultPro.setIfPlaceHg(ObjectUtil.convert(Integer.class, contrastRight));
            }
        } else {
            resultPro.setIfPlaceHg(null);
        }
        //作业人数
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(16)))) {
            resultPro.setWorkerNum(ObjectUtil.convert(Integer.class, excelRowData.get(16)));
        } else {
            resultPro.setWorkerNum(null);
        }
        //工作班制时长
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(17)))) {
            resultPro.setWorkTime(StringUtils.objectToString(excelRowData.get(17)));
        } else {
            resultPro.setWorkTime(null);
        }
        //每班接触时间
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(18)))) {
            resultPro.setClassTime(StringUtils.objectToString(excelRowData.get(18)));
        } else {
            resultPro.setClassTime(null);
        }
        //每周接触天数
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(19)))) {
            resultPro.setContactDays(StringUtils.objectToString(excelRowData.get(19)));
        } else {
            resultPro.setContactDays(null);
        }
        //每周接触时间
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(20)))) {
            resultPro.setContactTime(StringUtils.objectToString(excelRowData.get(20)));
        } else {
            resultPro.setContactTime(null);
        }
        //监测类型
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(21)))) {
            String contrastRight = getContrastByBusType(41, StringUtils.objectToString(excelRowData.get(21)));
            if (ObjectUtil.isEmpty(contrastRight)) {
                resultPro.setFixType(null);
            } else {
                resultPro.setFixType(ObjectUtil.convert(Integer.class, contrastRight));
            }
        } else {
            resultPro.setFixType(null);
        }
        //场所UUID
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(22)))) {
            resultPro.setPlaceMainId(StringUtils.objectToString(excelRowData.get(22)));
        } else {
            resultPro.setPlaceMainId(null);
        }
        //监测地点
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(23)))) {
            resultPro.setPlaceName(StringUtils.objectToString(excelRowData.get(23)));
        } else {
            resultPro.setPlaceName(null);
        }
        //监测项目
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(24)))) {
            String codeName = StringUtils.objectToString(excelRowData.get(24));
            if (ObjectUtil.isNotEmpty(this.simpleCodeMap.get("5581"))
                    && this.simpleCodeMap.get("5581").containsKey(codeName)) {
                resultPro.setFkByFactorProId(this.simpleCodeMap.get("5581").get(codeName));
                resultPro.setFactorProName(resultPro.getFkByFactorProId().getCodeName());
            }
        } else {
            resultPro.setFkByFactorProId(null);
            resultPro.setFactorProName(null);
        }
        //接触时间
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(25)))) {
            resultPro.setTchTime(ObjectUtil.convert(BigDecimal.class, excelRowData.get(25)));
        } else {
            resultPro.setTchTime(null);
        }
        //样本1
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(26)))) {
            resultPro.setSample1(StringUtils.objectToString(excelRowData.get(26)));
        } else {
            resultPro.setSample1(null);
        }
        //样本2
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(27)))) {
            resultPro.setSample2(StringUtils.objectToString(excelRowData.get(27)));
        } else {
            resultPro.setSample2(null);
        }
        //样本3
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(28)))) {
            resultPro.setSample3(StringUtils.objectToString(excelRowData.get(28)));
        } else {
            resultPro.setSample3(null);
        }
        //样本4
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(29)))) {
            resultPro.setSample4(StringUtils.objectToString(excelRowData.get(29)));
        } else {
            resultPro.setSample4(null);
        }
        //样本5
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(30)))) {
            resultPro.setSample5(StringUtils.objectToString(excelRowData.get(30)));
        } else {
            resultPro.setSample5(null);
        }
        //样本6
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(31)))) {
            resultPro.setSample6(StringUtils.objectToString(excelRowData.get(31)));
        } else {
            resultPro.setSample6(null);
        }
        //游离二氧化硅含量
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(32)))) {
            resultPro.setSilicaContent(StringUtils.objectToString(excelRowData.get(32)));
        } else {
            resultPro.setSilicaContent(null);
        }
        //Cste值
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(33)))) {
            resultPro.setFillCste(StringUtils.objectToString(excelRowData.get(33)));
        } else {
            resultPro.setFillCste(null);
        }
        //平均值
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(34)))) {
            resultPro.setAvgValue(StringUtils.objectToString(excelRowData.get(34)));
        } else {
            resultPro.setAvgValue(null);
        }
        //Ctwa值
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(35)))) {
            resultPro.setCtwaVal(StringUtils.objectToString(excelRowData.get(35)));
        } else {
            resultPro.setCtwaVal(null);
        }
        //Ctwa折减限值
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(36)))) {
            resultPro.setCtwaDisVal(StringUtils.objectToString(excelRowData.get(36)));
        } else {
            resultPro.setCtwaDisVal(null);
        }
        //LEX,8h/LEX,40h
        if (!UnitbasicinfoUtils.ifMatcherNull(StringUtils.objectToString(excelRowData.get(37)))) {
            resultPro.setLexVal(StringUtils.objectToString(excelRowData.get(37)));
        } else {
            resultPro.setLexVal(null);
        }

        resultPro.setCreateDate(new Date());
        resultPro.setCreateManid(Global.getUser().getRid());

        resultProList.add(resultPro);
    }

    /**
     * 移除错误数据同UUID的正确数据
     *
     * @param excelRowList  原数据
     * @param errorMap      错误信息Map
     * @param resultProList 封装后的数据List
     * @param rowSize       列头行数
     */
    private void removeErrorData(List<List<Object>> excelRowList,
                                 Map<Integer, String> errorMap,
                                 List<TdZxjcResultPro> resultProList,
                                 int rowSize) {
        //先找出有错误的uuid
        Set<String> errorUuidSet = new HashSet<>();
        for (int i = 0, excelRowListSize = excelRowList.size(); i < excelRowListSize; i++) {
            List<Object> excelRowData = excelRowList.get(i);
            String uuid = StringUtils.objectToString(excelRowData.get(1));
            if (errorMap.containsKey(i + rowSize)) {
                errorUuidSet.add(uuid);
            }
        }
        //根据错误数据的uuid找到同uuid正确的数据
        for (int i = 0, excelRowListSize = excelRowList.size(); i < excelRowListSize; i++) {
            List<Object> excelRowData = excelRowList.get(i);
            if (errorMap.containsKey(i + rowSize)) {
                continue;
            }
            String uuid = StringUtils.objectToString(excelRowData.get(1));
            if (errorUuidSet.contains(uuid)) {
                putErrorOfMap(errorMap, i + rowSize, "文件内同企业ID存在错误数据");
            }
        }
        //根据错误数据的uuid删除同uuid正确的数据
        for (int i = resultProList.size() - 1; i >= 0; i--) {
            TdZxjcResultPro resultPro = resultProList.get(i);
            if (errorUuidSet.contains(resultPro.getFkByMainId().getUuid())) {
                resultProList.remove(i);
            }
        }
    }

    /**
     * 生成错误数据文件
     *
     * @param wb           excel文件
     * @param oldFileName  原文件名称
     * @param rowSize      列数
     * @param colSize      列头行数
     * @param excelRowList excel数据
     * @param errorMap     错误信息Map
     */
    private void generateImportErrorFile(Workbook wb, String oldFileName, int colSize, int rowSize,
                                         List<List<Object>> excelRowList,
                                         Map<Integer, String> errorMap) {
        if (ObjectUtil.isEmpty(errorMap)) {
            return;
        }
        CellStyle cs = wb.createCellStyle();
        CellStyle cs2 = wb.createCellStyle();
        this.errColumnStyle(wb, cs, cs2);
        // 增加sheet0错误列
        Sheet sheet0 = wb.getSheetAt(0);
        Row headRow0 = sheet0.getRow(0);
        Cell headCell0 = headRow0.createCell(colSize);
        headCell0.setCellValue("数据错误原因");
        headCell0.setCellStyle(cs);
        if (colSize > 1) {
            sheet0.addMergedRegion(new CellRangeAddress(0, rowSize - 1, colSize, colSize));
        }
        sheet0.setColumnWidth(colSize, 20000);
        List<Integer> removeRowIndexList = new ArrayList<>();
        int excelRowListSize = excelRowList.size() + rowSize;
        for (int i = rowSize; i < excelRowListSize; i++) {
            if (errorMap.containsKey(i)) {
                String errMsg = errorMap.get(i);
                //sheet0 错误信息写入对应行
                Row curRow = sheet0.getRow(i);
                Cell cell = curRow.createCell(colSize);
                cell.setCellValue(errMsg);
                cell.setCellStyle(cs2);
            } else {
                removeRowIndexList.add(i);
            }
        }
        Map<Integer, List<Integer>> sheetIndexWithRemoveIndexListMap = new HashMap<>();
        sheetIndexWithRemoveIndexListMap.put(0, removeRowIndexList);
        ExcelExportUtil.removeSheetRows(wb, sheetIndexWithRemoveIndexListMap);
        String endType = ".xlsx";
        if (StringUtils.isNotBlank(oldFileName) && oldFileName.contains(".")) {
            endType = oldFileName.substring(oldFileName.lastIndexOf("."));
        }
        //错误信息写入到文件
        String filePath = JsfUtil.getAbsolutePath() + "heth/comm/temp/" + this.uuidFileName + endType;
        File dirFile = new File(JsfUtil.getAbsolutePath() + "heth/comm/temp/");
        if (!dirFile.exists()) {
            dirFile.mkdirs();
        }
        OutputStream outputStream = null;
        try {
            outputStream = Files.newOutputStream(Paths.get(filePath));
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
            this.importErrFilePath = "/heth/comm/temp/" + this.uuidFileName + endType;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * <p>方法描述：错误数据行 增加样式 </p>
     *
     * @MethodAuthor pw
     */
    private void errColumnStyle(Workbook wb, CellStyle cs, CellStyle cs2) {
        //创建两种字体
        Font f = wb.createFont();
        Font f2 = wb.createFont();
        //创建第一种字体样式（用于列名）
        f.setFontName("宋体");
        f.setFontHeightInPoints((short) 11);
        f.setColor(IndexedColors.BLACK.getIndex());
        f.setBoldweight(Font.BOLDWEIGHT_BOLD);
        //创建第二种字体样式（用于值）
        f2.setFontName("宋体");
        // 设置行高
        f2.setFontHeightInPoints((short) 11);
        f2.setColor(IndexedColors.RED.getIndex());
        //设置第一种单元格的样式（用于列名）
        cs.setFont(f);
        cs.setAlignment(CellStyle.ALIGN_CENTER);
        cs.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
        //设置第二种单元格的样式（用于值）
        cs2.setFont(f2);
        cs2.setAlignment(CellStyle.ALIGN_LEFT);
        cs2.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
    }

    /**
     * 封装提示信息并提示
     *
     * @param excelRowList excel数据
     * @param errorMap     错误信息Map
     * @param rowSize      列头行数
     */
    private void pakTipStr(List<List<Object>> excelRowList, Map<Integer, String> errorMap, int rowSize) {
        String str;
        int successCount = 0;
        int failedCount = 0;
        int excelRowListSize = excelRowList.size();
        for (int i = 0; i < excelRowListSize; i++) {
            if (errorMap.containsKey(i + rowSize)) {
                failedCount++;
            } else {
                successCount++;
            }
        }
        str = "共导入" + excelRowListSize + "条数据。成功" + successCount + "条数据，失败" + failedCount + "条数据。";
        if (failedCount > 0) {
            str += "请下载错误数据！";
            JsfUtil.addErrorMessage(str);
        } else {
            JsfUtil.addSuccessMessage(str);
        }
        if (successCount > 0) {
            // 执行查询
            this.searchAction();
            //更新列表
            RequestContext.getCurrentInstance().update("mainForm:dataTable");
        }
    }

    /**
     * <p>方法描述：调用同步用人单位信息接口填充企业共享表Id 返回异常信息 </p>
     * @MethodAuthor： pw 2023/6/21
     **/
    private Map<String, String> fillCrptIdAndResultErrMap(Map<String,List<Object>> rowDataMap, List<String> uuidList,
                                                         Map<String,TdZxjcUnitbasicinfo> basicInfoMap){
        if(CollectionUtils.isEmpty(uuidList)){
            return Collections.EMPTY_MAP;
        }
        Map<String, String> resultMap = new HashMap<>();
        if(null == this.jcCrptSourceId){
            for(String uuid : uuidList){
                resultMap.put(uuid, "缺少场所监测的信息来源，请在5512码表中维护");
            }
            return resultMap;
        }
        List<Map<String, Object>> paramList = null;
        if(1 == this.importDataType){
            paramList = this.generateRequestParamMapListForTypeOne(rowDataMap, uuidList, basicInfoMap);
        }else if(2 == this.importDataType || 0 == this.importDataType){
            paramList = this.generateRequestParamMapListForTypeTwo( uuidList, basicInfoMap);
        }
        //调用同步用人单位信息接口返回
        Map<String, CrptUploadRepSingleDTO> responseMap = this.requestCrptUploadInterface(paramList);
        for(String uuid : uuidList){
            CrptUploadRepSingleDTO singleDTO = responseMap.get(uuid);
            if(null != singleDTO && ReturnType.SUCCESS_PROCESS.getTypeNo().equals(singleDTO.getType())){
                TdZxjcUnitbasicinfo basicInfo = basicInfoMap.get(uuid);
                if(null != basicInfo && null != singleDTO.getRid()){
                    basicInfo.setFkByCrptId(new TbTjCrpt(singleDTO.getRid()));
                }
            }
            if(null == singleDTO || !ReturnType.SUCCESS_PROCESS.getTypeNo().equals(singleDTO.getType())){
                resultMap.put(uuid, "同步用人单位信息失败"+(null == singleDTO || StringUtils.isBlank(singleDTO.getMess()) ? "" : singleDTO.getMess()));
            }
        }
        return resultMap;
    }

    /**
     * <p>方法描述： 调用同步用人单位信息接口 </p>
     * @MethodAuthor： pw 2023/6/21
     **/
    private Map<String, CrptUploadRepSingleDTO> requestCrptUploadInterface(List<Map<String, Object>> paramList){
        String returnJson = "";
        Map<String, CrptUploadRepSingleDTO> resultMap = new HashMap<>();
        try{
            Map<String,List<Map<String, Object>>> requestParam = new HashMap<>(1);
            requestParam.put("crptList", paramList);
            String requestJson = JSON.toJSONString(requestParam);
            String debug = PropertyUtils.getValue("encrypt.debug");
            String encryptKey = PropertyUtils.getValue("encrypt.key");
            //JSON是否需要加密
            String encodeJson = "true".equals(debug) ? requestJson : AesEncryptUtils.aesEncrypt(requestJson, encryptKey);
            //调用接口
            String delUrl = PropertyUtils.getValue("delUrl");
            String reposeJson = HttpRequestUtil.httpRequestByRaw(delUrl + "/crpt/saveOrUpdateCrpt", encodeJson);
            //JSON是否需要解密
            returnJson = "true".equals(debug) ? reposeJson : AesEncryptUtils.aesDecrypt(reposeJson, encryptKey);
            System.out.println("场所监测调用同步用人单位信息接口参数："+requestJson);
            System.out.println("场所监测调用同步用人单位信息接口返回："+reposeJson);
            logger.info("场所监测调用同步用人单位信息接口参数："+requestJson+" 返回："+returnJson);
        }catch(Exception e){
            e.printStackTrace();
        }
        if (StringUtils.isNotBlank(returnJson)){
            CrptUploadRepDTO repDTO = JSON.parseObject(returnJson, CrptUploadRepDTO.class);
            if(null != repDTO && ReturnType.SUCCESS_PROCESS.getTypeNo().equals(repDTO.getType())){
                List<CrptUploadRepSingleDTO> singleDTOList = repDTO.getRidList();
                if(!CollectionUtils.isEmpty(singleDTOList)){
                    for(CrptUploadRepSingleDTO singleDTO : singleDTOList){
                        String uuid = singleDTO.getUuid();
                        if(StringUtils.isNotBlank(uuid)){
                            resultMap.put(uuid, singleDTO);
                        }
                    }
                }
            }
        }
        return resultMap;
    }

    /**
     * <p>方法描述：调查信息导入时 封装请求同步用人单位信息接口参数 </p>
     * @MethodAuthor： pw 2023/6/21
     **/
    private List<Map<String, Object>> generateRequestParamMapListForTypeOne(Map<String,List<Object>> rowDataMap,List<String> uuidList,
                                           Map<String,TdZxjcUnitbasicinfo> basicInfoMap){
        if(CollectionUtils.isEmpty(uuidList)){
            return Collections.EMPTY_LIST;
        }
        List<Map<String, Object>> resultList = new ArrayList<>();
        for(String uuid : uuidList){
            TdZxjcUnitbasicinfo basicInfo = basicInfoMap.get(uuid);
            List<Object> rowData = rowDataMap.get(uuid);
            if(null == basicInfo){
                continue;
            }
            resultList.add(this.generateChildRequestParam(uuid, null == basicInfo.getFkByZoneId() ? null : basicInfo.getFkByZoneId().getRid(),
                    basicInfo.getUnitName(), basicInfo.getCreditCode(),
                    null == basicInfo.getFkByEnterpriseScaleId() ? null : basicInfo.getFkByEnterpriseScaleId().getRid(),
                    null == basicInfo.getFkByEconomicId() ? null : basicInfo.getFkByEconomicId().getRid(),
                    null == basicInfo.getFkByIndustryId() ? null : basicInfo.getFkByIndustryId().getRid(),
                    basicInfo.getEmpNum(), basicInfo.getExternalNum(),
                    this.siteMonitoringDataService.getContactTotalPeoples(this.headerDTO,rowData, this.contrastMap),
                    this.siteMonitoringDataService.getFactorIdList(this.headerDTO, rowData, this.contrastMap,this.simpleCodeMap.get("5007"))));
        }
        return resultList;
    }

    /**
     * <p>方法描述： 结果信息导入时 封装请求同步用人单位信息接口参数 </p>
     * @MethodAuthor： pw 2023/6/21
     **/
    private List<Map<String, Object>> generateRequestParamMapListForTypeTwo(List<String> uuidList, Map<String,TdZxjcUnitbasicinfo> basicInfoMap){
        if(CollectionUtils.isEmpty(uuidList)){
            return Collections.EMPTY_LIST;
        }
        Map<String, Integer> contactTotalPeopleMap = this.siteMonitoringDataService.findContactTotalPeopleByUuid(uuidList);
        Map<String,List<Integer>> factorIdsMap = this.siteMonitoringDataService.findFactorIdsByUuid(uuidList);
        List<Map<String, Object>> resultList = new ArrayList<>();
        for(String uuid : uuidList){
            TdZxjcUnitbasicinfo basicInfo = basicInfoMap.get(uuid);
            if(null == basicInfo){
                continue;
            }
            resultList.add(this.generateChildRequestParam(uuid, null == basicInfo.getFkByZoneId() ? null : basicInfo.getFkByZoneId().getRid(),
                    basicInfo.getUnitName(), basicInfo.getCreditCode(),
                    null == basicInfo.getFkByEnterpriseScaleId() ? null : basicInfo.getFkByEnterpriseScaleId().getRid(),
                    null == basicInfo.getFkByEconomicId() ? null : basicInfo.getFkByEconomicId().getRid(),
                    null == basicInfo.getFkByIndustryId() ? null : basicInfo.getFkByIndustryId().getRid(),
                    basicInfo.getEmpNum(), basicInfo.getExternalNum(), contactTotalPeopleMap.get(uuid),
                     factorIdsMap.get(uuid)));
        }
        return resultList;
    }

    /**
     * <p>方法描述： 封装调用同步用人单位信息接口数据 </p>
     * @MethodAuthor： pw 2023/6/21
     **/
    private Map<String, Object> generateChildRequestParam(String uuid, Integer zoneId, String crptName, String institutionCode,
                                                          Integer crptSizeId, Integer economyId, Integer indusTypeId,
                                                          Integer workForce, Integer outSourceNum, Integer holdCardMan,
                                                          List<Integer> badRsnIdList){
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("zoneId", zoneId);
        resultMap.put("crptName", crptName);
        resultMap.put("ifSubPrg", 0);
        resultMap.put("institutionCode", institutionCode);
        resultMap.put("crptSizeId", crptSizeId);
        resultMap.put("economyId", economyId);
        resultMap.put("indusTypeId", indusTypeId);
        resultMap.put("workForce", workForce);
        resultMap.put("outsourceNum", outSourceNum);
        resultMap.put("holdCardMan", holdCardMan);
        resultMap.put("sourceCode", "1001");
        resultMap.put("uuid", uuid);
        resultMap.put("badRsnList", badRsnIdList);
        return resultMap;
    }

    private String getContrastByBusType(Integer busType, String leftStr) {
        if (null == busType || StringUtils.isBlank(leftStr)) {
            return "";
        }
        leftStr = leftStr.replaceAll("\\s*|\r|\n|\t","");
        return StringUtils.objectToString(this.contrastMap.get(busType).get(leftStr));
    }

    public Integer getSearchYear() {
        return searchYear;
    }

    public void setSearchYear(Integer searchYear) {
        this.searchYear = searchYear;
    }

    public Integer getImportYear() {
        return importYear;
    }

    public void setImportYear(Integer importYear) {
        this.importYear = importYear;
    }

    public List<Integer> getSearchYearList() {
        return searchYearList;
    }

    public void setSearchYearList(List<Integer> searchYearList) {
        this.searchYearList = searchYearList;
    }

    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public String getSearchZoneGb() {
        return searchZoneGb;
    }

    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchUuId() {
        return searchUuId;
    }

    public void setSearchUuId(String searchUuId) {
        this.searchUuId = searchUuId;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public String getSearchCreditCode() {
        return searchCreditCode;
    }

    public void setSearchCreditCode(String searchCreditCode) {
        this.searchCreditCode = searchCreditCode;
    }

    public List<Integer> getSearchDataTypeList() {
        return searchDataTypeList;
    }

    public void setSearchDataTypeList(List<Integer> searchDataTypeList) {
        this.searchDataTypeList = searchDataTypeList;
    }

    public Integer getImportDataType() {
        return importDataType;
    }

    public void setImportDataType(Integer importDataType) {
        this.importDataType = importDataType;
    }

    public Map<Integer, Map<String, String>> getContrastMap() {
        return contrastMap;
    }

    public void setContrastMap(Map<Integer, Map<String, String>> contrastMap) {
        this.contrastMap = contrastMap;
    }

    public String getImportErrFilePath() {
        return importErrFilePath;
    }

    public void setImportErrFilePath(String importErrFilePath) {
        this.importErrFilePath = importErrFilePath;
    }

    public StreamedContent getTemplateFile() {
        return templateFile;
    }

    public void setTemplateFile(StreamedContent templateFile) {
        this.templateFile = templateFile;
    }

    public StreamedContent getErrorImportFile() {
        if (StringUtils.isBlank(this.importErrFilePath)) {
            return null;
        }
        InputStream stream;
        try {
            stream = new FileInputStream(JsfUtil.getAbsolutePath() + this.importErrFilePath);
            String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            String fileName = "场所监测数据导入错误数据.xlsx";
            if (this.importErrFilePath.endsWith(".xls")) {
                contentType = "application/vnd.ms-excel";
                fileName = "场所监测数据导入错误数据.xls";
            }
            this.templateFile = new DefaultStreamedContent(stream, contentType, URLEncoder.encode(fileName, "UTF-8"));
            return templateFile;
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("下载失败!");
        }
        return null;
    }

    public void setErrorImportFile(StreamedContent errorImportFile) {
        this.errorImportFile = errorImportFile;
    }

    public String getUuidFileName() {
        return uuidFileName;
    }

    public void setUuidFileName(String uuidFileName) {
        this.uuidFileName = uuidFileName;
    }

    public ExcelHeaderDTO getHeaderDTO() {
        return headerDTO;
    }

    public void setHeaderDTO(ExcelHeaderDTO headerDTO) {
        this.headerDTO = headerDTO;
    }

    public Integer getJcCrptSourceId() {
        return jcCrptSourceId;
    }

    public void setJcCrptSourceId(Integer jcCrptSourceId) {
        this.jcCrptSourceId = jcCrptSourceId;
    }
}
