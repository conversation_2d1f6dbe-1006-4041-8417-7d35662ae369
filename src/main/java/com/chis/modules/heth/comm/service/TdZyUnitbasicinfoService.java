package com.chis.modules.heth.comm.service;

import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.entity.TdZyUnitbasicinfo;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>类描述：企业在线申报Service </p>
 * @ClassAuthor： pw 2022/9/9
 **/
@Service
@Transactional(readOnly = true)
public class TdZyUnitbasicinfoService extends AbstractTemplate {

    /**
     * <p>方法描述：通过主表rid获取 危害因素接触情况明细 检测情况明细 职业健康监护开展情况明细 </p>
     * @param mainRid
     * @param type 1危害因素接触情况明细 2检测情况明细 3职业健康监护开展情况明细
     * @MethodAuthor： pw 2022/9/9
     **/
    public Map<Integer,List<Object[]>> findBadRsnCountInfoByTypeAndMainRid(Integer mainRid, int type){
        if(null == mainRid){
            return null;
        }
        Map<Integer,List<Object[]>> resultMap = new HashMap<>();
        String querySql = null;
        if(1 == type){
            //危害因素种类接触危害因素情况
            querySql = "SELECT T.HAZARDS_SORT AS M1,T.HAZARDS_NAME AS M2,T.CONTACT_NUMBER AS M3,'' AS R4 FROM TD_ZY_UNITFACTORC_DETAIL T INNER JOIN TS_SIMPLE_CODE T1 ON T.HAZARDS_ID=T1.RID WHERE T.MAIN_ID="+mainRid+" ORDER BY T1.CODE_NO ";
        }else if(2 == type){
            //危害因素检测情况
            querySql = "SELECT T.HAZARDS_SORT AS M1,T.HAZARDS_NAME AS M2,T.CHECK_POINTS AS M3,T.OVERPROOF_POINTS AS M4, '' AS M5 FROM TD_ZY_UNITHARMFACT_DETAIL T INNER JOIN TS_SIMPLE_CODE T1 ON T.HAZARDS_ID=T1.RID WHERE T.MAIN_ID="+mainRid+" ORDER BY T1.CODE_NO ";
        }else if(3 == type){
            //职业健康监护开展情况
            querySql = "SELECT T.HAZARDS_SORT AS M1,T.HAZARDS_NAME AS M2,T.PE_NUM AS M3,'' AS M4 FROM TD_ZY_UNITHEALTH_DETAIL T INNER JOIN TS_SIMPLE_CODE T1 ON T.HAZARDS_ID=T1.RID WHERE T.MAIN_ID="+mainRid+" ORDER BY T1.CODE_NO ";
        }
        if(StringUtils.isBlank(querySql)){
            return null;
        }
        List<Object[]> queryResultList = this.findSqlResultList(querySql);
        if(CollectionUtils.isEmpty(queryResultList)){
            return null;
        }
        for(Object[] objArr : queryResultList){
            Integer hazardsSort = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            if(null == hazardsSort){
                continue;
            }
            List<Object[]> tmpList = resultMap.get(hazardsSort);
            if(null == tmpList){
                tmpList = new ArrayList<>();
            }
            tmpList.add(objArr);
            resultMap.put(hazardsSort, tmpList);
        }
        return resultMap;
    }

    /**
     * <p>方法描述：通过主表rid 获取检测机构与职业健康监护机构 </p>
     * @MethodAuthor： pw 2022/9/9
     **/
    public Map<Integer,List<Object[]>> findOrgInfoByMainRid(Integer mainRid){
        if(null == mainRid){
            return null;
        }
        Map<Integer,List<Object[]>> resultMap = new HashMap<>();
        StringBuffer queryBuffer = new StringBuffer();
        queryBuffer.append("SELECT 1 AS M1,T.UNIT_NAME AS M2,T.REPORT_NO AS M3,'' as M4 FROM TD_ZY_JC_ORG T WHERE T.MAIN_ID=")
                .append(mainRid)
                .append(" UNION ")
                .append(" SELECT 2 AS M1,T.UNIT_NAME AS M2,T.REPORT_NO AS M3,'' as M4 FROM TD_ZY_HETH_ORG T WHERE T.MAIN_ID=")
                .append(mainRid);
        List<Object[]> queryResultList = this.findSqlResultList(queryBuffer.toString());
        if(CollectionUtils.isEmpty(queryResultList)){
            return null;
        }
        for(Object[] objArr : queryResultList){
            Integer type = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            if(null == type){
                continue;
            }
            List<Object[]> tmpList = resultMap.get(type);
            if(null == tmpList){
                tmpList = new ArrayList<>();
            }
            tmpList.add(objArr);
            resultMap.put(type, tmpList);
        }
        return resultMap;
    }

    /**
     * <p>方法描述：获取企业申报危害因素小类 接害人数与检查人数 </p>
     * @MethodAuthor： pw 2022/9/12
     **/
    public Map<Integer,List<Object[]>> declareSubInfoQuery(Integer mainRid){
        Map<Integer,List<Object[]>> resultMap = new HashMap<>();
        if(null == mainRid){
            return resultMap;
        }
        StringBuffer queryBuffer = new StringBuffer();
        //0 type1接触人数2体检人数  1危害因素rid 2数量 3扩展字段3 4危害因素codeNo 5空字段
        queryBuffer.append(" SELECT 1 AS QUERYTYPE, T.HAZARDS_ID AS M1,T.CONTACT_NUMBER AS M2,T1.EXTENDS3 AS M3,T1.CODE_NO AS M5, '' AS M4 ")
                .append(" FROM TD_ZY_UNITFACTORC_DETAIL T INNER JOIN TS_SIMPLE_CODE T1 ON T.HAZARDS_ID=T1.RID ")
                .append(" WHERE T.MAIN_ID=").append(mainRid)
                .append(" AND T.CONTACT_NUMBER IS NOT NULL ")
                .append(" UNION ALL ")
                .append(" SELECT 2 AS QUERYTYPE, T.HAZARDS_ID AS M1,T.PE_NUM AS M2,T1.EXTENDS3 AS M3,T1.CODE_NO AS M5,'' AS M4 ")
                .append(" FROM TD_ZY_UNITHEALTH_DETAIL T INNER JOIN TS_SIMPLE_CODE T1 ON T.HAZARDS_ID=T1.RID ")
                .append(" WHERE T.MAIN_ID=").append(mainRid)
                .append(" AND T.PE_NUM IS NOT NULL ");
        List<Object[]> queryResultList = this.findSqlResultList(queryBuffer.toString());
        if(CollectionUtils.isEmpty(queryResultList)){
            return resultMap;
        }
        for(Object[] objArr : queryResultList){
            Integer queryType = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            if(null == queryType){
                continue;
            }
            List<Object[]> tmpList = resultMap.get(queryType);
            if(null == tmpList){
                tmpList = new ArrayList<>();
            }
            tmpList.add(objArr);
            resultMap.put(queryType, tmpList);
        }
        return resultMap;

    }

    /**
     * <p>方法描述：获取企业申报危害因素大类 接害人数与检查人数 </p>
     * @MethodAuthor： pw 2022/9/12
     **/
    public Object[] declareInfoQuery(TbTjCrpt tbTjCrpt, TdZyUnitbasicinfo basicinfo, Integer year){
        Object[] objArr = null;
        String creditCode = null;
        String crptName = null;
        Integer ifBranch = null;
        if (null!=tbTjCrpt) {
            creditCode = tbTjCrpt.getInstitutionCode();
            crptName = tbTjCrpt.getCrptName();
            ifBranch = null == tbTjCrpt.getIfSubOrg() ? null : tbTjCrpt.getIfSubOrg().intValue();
        }
        if (null!=basicinfo) {
            creditCode = basicinfo.getCreditCode();
            crptName = basicinfo.getUnitName();
            ifBranch = basicinfo.getIfBranch();
        }
        if(StringUtils.isBlank(creditCode) || StringUtils.isBlank(crptName) || null == year || null == ifBranch){
            return null;
        }
        StringBuffer buffer = new StringBuffer();
        Map<String,Object> paramMap = new HashMap<>();
        buffer.append(" SELECT T.RID AS BASICRID, ");
        //接害人数
        buffer.append(" T1.IFHF_DUST,");//是否有粉尘危害因素 0无 1有
        buffer.append(" T1.HF_DUST_PEOPLES,");//粉尘接触总人数

        buffer.append(" T1.IFHF_CHEMISTRY,");//是否有化学物质危害因素
        buffer.append(" T1.HF_CHEMISTRY_PEOPLES,");//化学物质危害因素接触总人数

        buffer.append(" T1.IFHF_PHYSICS,");//是否有物理危害因素
        buffer.append(" T1.HF_PHYSICS_PEOPLES,");//物理因素接触总人数

        buffer.append(" T1.IFHF_RADIOACTIVITY,");//是否有放射性因素
        buffer.append(" T1.HF_RADIOACTIVITY_PEOPLES,");//放射性因素接触总人数

        buffer.append(" T1.IFHF_BIOLOGY,");//是否有生物因素
        buffer.append(" T1.HF_BIOLOGY_PEOPLES,");//生物因素接触总人数

        buffer.append(" T1.IFHF_OTHER,");//是否有其他因素
        buffer.append(" T1.HF_OTHER_PEOPLES,");//其他因素接触总人数

        //检查人数
        buffer.append(" T2.IFHEA_DUST AS T2_D1,");//是否有健康检查粉尘
        buffer.append(" T2.HEA_DUST_PEOPLES AS T2_D2,");//粉尘体检人数

        buffer.append(" T2.IFHEA_CHEMISTRY AS T2_D3,");//是否有健康检查化学物质
        buffer.append(" T2.HEA_CHEMISTRY_PEOPLES AS T2_D4,");//化学物质体检人数

        buffer.append(" T2.IFHEA_PHYSICS AS T2_D5,");//是否有健康检查物理因素
        buffer.append(" T2.HEA_PHYSICS_PEOPLES AS T2_D6,");//物理因素体检人数

        buffer.append(" T2.IFHEA_RADIOACTIVITY AS T2_D7,");//是否有健康检查放射性因素
        buffer.append(" T2.HEA_RADIOACTIVITY_PEOPLES AS T2_D8 ");//放射性因素体检人数

        buffer.append(" FROM TD_ZY_UNITBASICINFO T ");
        buffer.append(" LEFT JOIN TD_ZY_UNITFACTORCROWD T1 ON T.RID = T1.MAIN_ID ");
        buffer.append(" LEFT JOIN TD_ZY_UNITHEALTHCUSTODY T2 ON T.RID = T2.MAIN_ID ");
        buffer.append(" WHERE T.DECLARE_YEAR = :year AND T.CREDIT_CODE= :creditCode ");
        //分支机构 加入名称条件
        if(1 == ifBranch){
            buffer.append(" AND T.UNIT_NAME= :crptName ");
            paramMap.put("crptName", crptName);
        }
        buffer.append(" AND T.IF_BRANCH= :branch ");
        paramMap.put("year",year);
        paramMap.put("creditCode",creditCode);
        paramMap.put("branch", ifBranch);
        buffer.append(" ORDER BY T.DECLARE_DATE DESC ");

        List<Object[]> list = this.findDataBySqlNoPage(buffer.toString(),paramMap);
        if(!CollectionUtils.isEmpty(list)){
            objArr = list.get(0);
        }
        return objArr;
    }

    /**
     * <p>方法描述：获取企业在线申报的危害因素CODE_NO</p>
     * @MethodAuthor： pw 2022/9/13
     **/
    public List<String> findCrptOnlineCodeNoByMainRid(Integer mainRid){
        List<String> resultList = new ArrayList<>();
        if(null == mainRid){
            return resultList;
        }
        StringBuffer queryBuffer = new StringBuffer();
        queryBuffer.append(" SELECT T1.CODE_NO AS M3 ")
                .append(" FROM TD_ZY_UNITFACTORC_DETAIL T INNER JOIN TS_SIMPLE_CODE T1 ON T.HAZARDS_ID=T1.RID ")
                .append(" WHERE T.MAIN_ID=").append(mainRid)
                .append(" AND T.CONTACT_NUMBER IS NOT NULL ")
                .append(" UNION ")
                .append(" SELECT T1.CODE_NO AS M3 ")
                .append(" FROM TD_ZY_UNITHEALTH_DETAIL T INNER JOIN TS_SIMPLE_CODE T1 ON T.HAZARDS_ID=T1.RID ")
                .append(" WHERE T.MAIN_ID=").append(mainRid)
                .append(" AND T.PE_NUM IS NOT NULL ");
        List<Object> queryResultList = this.findSqlResultList(queryBuffer.toString());
        if(CollectionUtils.isEmpty(queryResultList)){
            return resultList;
        }
        for(Object obj : queryResultList){
            if(null == obj){}
            resultList.add(obj.toString());
        }
        return resultList;

    }
}
