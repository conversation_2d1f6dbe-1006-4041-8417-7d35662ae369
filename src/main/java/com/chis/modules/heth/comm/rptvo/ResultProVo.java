package com.chis.modules.heth.comm.rptvo;

import java.io.Serializable;
import java.util.List;

/**
 * <p>类描述： </p>
 *
 * @ClassAuthor: yzz
 * @date： 2023年01月18日
 **/
public class ResultProVo  implements Serializable {
    private static final long serialVersionUID = 1L;

    //监测因素名称
    private String  factorName;
    //监测因素分类 1：粉尘 2：化学  3：物理
    private Integer flag;
    //结果集
    private List<Object[]> resultPro;

    public String getFactorName() {
        return factorName;
    }

    public void setFactorName(String factorName) {
        this.factorName = factorName;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public List<Object[]> getResultPro() {
        return resultPro;
    }

    public void setResultPro(List<Object[]> resultPro) {
        this.resultPro = resultPro;
    }
}
