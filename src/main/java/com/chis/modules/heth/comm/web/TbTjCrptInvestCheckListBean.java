package com.chis.modules.heth.comm.web;

import java.util.*;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.modules.heth.comm.entity.TbTjCrptInvest;
import com.chis.modules.heth.comm.entity.TbTjCrptInvstMining;
import com.chis.modules.heth.comm.service.TbTjCrptInvestServiceImpl;

import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.web.FacesEditBean;
import org.springframework.util.CollectionUtils;

/**
 * <p>类描述：用人单位信息审核</p>
 * @ClassAuthor qrr,2021年3月27日,TbTjCrptInvestCheckListBean
 * */
@ManagedBean(name="tbTjCrptInvestCheckListBean")
@ViewScoped
public class TbTjCrptInvestCheckListBean extends FacesEditBean{
	private static final long serialVersionUID = 1L;
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private TbTjCrptInvestServiceImpl crptInvestService = SpringContextHolder.getBean(TbTjCrptInvestServiceImpl.class);
	
	private List<TsZone> searchZoneList;
	private String searchZoneCode;
	private String searchZoneName;
	private String searchCrptName;
	private String searchCreditCode;
	private String[] searchState;
	/**行业类别*/
	private String searchIndusTypeName;
	private String searchIndusTypeId;
	/**经济类型*/
	private String searchEconomyName;
	private String searchEconomyId;
	/**码表类型*/
	private String simpleCodeType;
	/**企业规模*/
	private List<TsSimpleCode> crptSizeList;
    private String selectCrptSizeNames;
    private String selectCrptSizeIds;
    /** 审核按钮传递rid */
	private Integer rid;
	/** 编辑页准备处理的实体 */
	private TbTjCrptInvest crptInvest;
	/** 退回原因 赋值字段 */
	private String backRsn;
	/**开采方式*/
	private String selectMiningNames;
	private String zoneName;
	/** 选择的结果集 */
    protected List<Object[]> selectEntitys;


    /*****************地区变更相关******************/
    private String orginZoneName;
    private Integer changeZoneId;
    private String changeZoneName;
    private String changeZoneCode;
    private String changeZoneFullName;
    private List<TsZone> changeZoneList;
    /**转出前地区*/
    private TsZone orginZone;

	public TbTjCrptInvestCheckListBean(){
		this.ifSQL = true;
		this.searchState = new String[]{"1","4"};
		// 在岗状态下拉初始化
        this.crptSizeList = commService.findSimpleCodesByTypeId("5004");
		this.initZone();
		this.searchAction();
	}
	/**
	 * <p>
	 * 方法描述：初始化地区
	 * </p>
	 * 
	 * @MethodAuthor qrr,2019年11月20日,initZone
	 * */
	public void initZone() {
		TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
		if (null==tsZone) {
			tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
		}
		this.searchZoneCode = tsZone.getZoneCode();
		this.searchZoneName = tsZone.getZoneName();
		this.searchZoneList = this.systemModuleService.findZoneListICanSee(
				false, tsZone.getZoneCode());
	}
	/**
 	 * <p>方法描述：选择行业类别</p>
 	 * @MethodAuthor qrr,2019年12月2日,selIndusTypeAction
	 * */
	public void selSimpleCodeAction() {
		Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("contentWidth", 650);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
		paramList.add("5002".equals(this.simpleCodeType) ? "行业类别" : "经济类型");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add(this.simpleCodeType);
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
		if ("5002".equals(this.simpleCodeType)) {
			paramList.add(this.searchIndusTypeId);
		} else if ("5003".equals(this.simpleCodeType)) {
			paramList.add(this.searchEconomyId);
		}
		paramMap.put("selectIds", paramList);
		if ("5002".equals(this.simpleCodeType)||"5003".equals(this.simpleCodeType)) {
			paramList = new ArrayList<String>();
	        paramList.add("true");
	        paramMap.put("ifShowFirstCode", paramList);
		}
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList", options, paramMap);
	}
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2019年12月3日,onSimpleCodeAction
	 * */
	public void onSimpleCodeAction(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event
				.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
			if (null!=list && list.size()>0) {
				StringBuffer names = new StringBuffer();
				StringBuffer ids = new StringBuffer();
				for (TsSimpleCode t : list) {
					names.append("，").append(t.getCodeName());
					ids.append(",").append(t.getRid());
				}
				if ("5002".equals(simpleCodeType)) {
					//行业类别
					this.searchIndusTypeId = ids.substring(1);
					this.searchIndusTypeName = names.substring(1);
				}else if ("5003".equals(simpleCodeType)){
					//经济类型
					this.searchEconomyId = ids.substring(1);
					this.searchEconomyName = names.substring(1);
				}
			}
		}
	}
	/**
 	 * <p>方法描述：清空行业类别</p>
 	 * @MethodAuthor qrr,2019年12月2日,clearIndusName
	 * */
	public void clearSimpleCode() {
		if ("5002".equals(simpleCodeType)) {
			//行业类别
			this.searchIndusTypeName = null;
			this.searchIndusTypeId = null;
		}else if ("5003".equals(simpleCodeType)){
			//经济类型
			this.searchEconomyId = null;
			this.searchEconomyName = null;
		}
	}
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2021年3月27日,openReviewConfirmDialog
	 * */
	public void openReviewConfirmDialog() {
        if(this.selectEntitys == null || this.selectEntitys.size()==0){
            JsfUtil.addErrorMessage("请选择需要审批的数据！");
            return ;
        }
        RequestContext.getCurrentInstance().execute("PF('ReviewConfirmDialog').show()");
    }
	/**
 	 * <p>方法描述：批量审核</p>
 	 * @MethodAuthor qrr,2021年3月27日,reviewBatchAction
	 * */
	public void reviewBatchAction() {
		try{
			if(!CollectionUtils.isEmpty(this.selectEntitys)){
				StringBuffer ridBuffer = new StringBuffer();
				for(Object[] objArr : selectEntitys){
					if(null == objArr[0]){
						continue;
					}
					ridBuffer.append(",").append(objArr[0].toString());
				}
				String hql = "SELECT T FROM TbTjCrptInvest T WHERE T.rid IN ("+ridBuffer.substring(1)+")";
				List<TbTjCrptInvest> list = this.crptInvestService.findByHql(hql,TbTjCrptInvest.class);
				this.crptInvestService.investPassAndUpdateCrptCheck(list);
			}
			this.backAction();
			this.searchAction();
			JsfUtil.addSuccessMessage("审核成功！");
			RequestContext.getCurrentInstance().update("tabView");
		}catch(Exception e){
			JsfUtil.addErrorMessage("审核失败！");
			e.printStackTrace();
		}
	}
	@Override
	public void modInit() {
		this.crptInvest = commService.find(TbTjCrptInvest.class, rid);
        if(null!=this.crptInvest.getFkByZoneId()) {
            Short zoneType = this.crptInvest.getFkByZoneId().getZoneType();
            String fullName = this.crptInvest.getFkByZoneId().getFullName();
            if (zoneType.intValue()>2) {
                int start = fullName.indexOf("_")+1;
                this.zoneName = fullName.substring(start).replace("_", "");
            }else {
                this.zoneName = fullName;
            }
        }
        if(null!=this.crptInvest.getFkByOrginZoneId()) {
            Short zoneType = this.crptInvest.getFkByOrginZoneId().getZoneType();
            String fullName = this.crptInvest.getFkByOrginZoneId().getFullName();
            if (zoneType.intValue()>2) {
                int start = fullName.indexOf("_")+1;
                this.orginZoneName = fullName.substring(start).replace("_", "");
            }else {
                this.orginZoneName = fullName;
            }
        }

		// 开采方式赋值
		this.selectMiningNames = null;
		//mgrbean.crptInvest.fkByIndusTypeId.extendS1==1  crptInvest.existState==1
		if(null != crptInvest && null != crptInvest.getExistState() && 1 == crptInvest.getExistState() &&
				null != crptInvest.getFkByIndusTypeId() && null != crptInvest.getFkByIndusTypeId().getExtendS1() &&
				"1".equals(crptInvest.getFkByIndusTypeId().getExtendS1())){
			List<TbTjCrptInvstMining> miningList = commService.findEntityListByMainId(TbTjCrptInvstMining.class, this.rid);
			if(!CollectionUtils.isEmpty(miningList)){
				StringBuffer miningBuffer = new StringBuffer();
				for(TbTjCrptInvstMining t : miningList){
					if(null != t.getFkByMiningId() && null != t.getFkByMiningId().getCodeName()){
						miningBuffer.append("，").append(t.getFkByMiningId().getCodeName());
					}
				}
				if(miningBuffer.length() > 0){
					this.selectMiningNames = miningBuffer.substring(1);
				}
			}
		}
		// 变更地区初始化
        initChangZoneList();
	}
	@Override
	public void saveAction() {
		
	}
	@Override
	public String[] buildHqls() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select t.rid,CASE WHEN t1.ZONE_TYPE >2 THEN SUBSTR(t1.FULL_NAME, INSTR(t1.FULL_NAME,'_')+1) ELSE t1.FULL_NAME END ZONE_NAME");
		sql.append(",t.CRPT_NAME,t.INSTITUTION_CODE,T3.CODE_NAME AS INDUS_TYPE_NAME, T4.CODE_NAME AS ECONOMY_NAME, T5.CODE_NAME AS CRPT_SIZE_NAME");
		sql.append(",t.LINKMAN2,t.LINKPHONE2,t.EXIST_STATE,t2.UNITNAME,t.INVEST_DATE,t.STATE_MARK");
		sql.append(" from TB_TJ_CRPT_INVEST t");
		sql.append(" left join ts_zone t1 on t1.rid = t.zone_id");
		sql.append(" left join TS_UNIT t2 on t2.rid = t.FILL_UNIT_ID");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.INDUS_TYPE_ID = T3.RID ");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T.ECONOMY_ID = T4.RID ");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T5 ON T.CRPT_SIZE_ID = T5.RID ");
		sql.append(" where 1=1 ");
		if (StringUtils.isNotBlank(this.searchZoneCode)) {
			sql.append(" and t1.zone_gb like '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%'");
		}
		if (StringUtils.isNotBlank(this.searchIndusTypeId)) {
			sql.append(" and t.INDUS_TYPE_ID in (").append(this.searchIndusTypeId).append(")");
		}
		if (StringUtils.isNotBlank(this.searchEconomyId)) {
			sql.append(" and t.ECONOMY_ID in (").append(this.searchEconomyId).append(")");
		}
		if (StringUtils.isNotBlank(this.selectCrptSizeIds)) {
			sql.append(" and t.CRPT_SIZE_ID in (").append(this.selectCrptSizeIds).append(")");
		}
		if (StringUtils.isNotBlank(this.searchCrptName)) {
			sql.append(" AND T.CRPT_NAME LIKE :searchCrptName  escape '\\\'");
			this.paramMap.put("searchCrptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
		}
		if (StringUtils.isNotBlank(this.searchCreditCode)) {
			sql.append(" AND T.INSTITUTION_CODE LIKE :searchCreditCode  escape '\\\'");
			this.paramMap.put("searchCreditCode", "%" + StringUtils.convertBFH(this.searchCreditCode.trim()) + "%");
		}
		if (null!=this.searchState && this.searchState.length>0) {
			String state = StringUtils.array2string(searchState, ",");
			sql.append(" AND T.STATE_MARK IN (").append(state).append(")");
		}else {
			sql.append(" AND T.STATE_MARK IN (1,2,3,4)");
		}
		sql.append(" order by t1.zone_gb,t.CRPT_NAME");
		String h2 = "SELECT COUNT(*) FROM (" + sql.toString() + ")";
        String h1 = sql.toString();
        return new String[] { h1, h2 };
	}
	@Override
	public void addInit() {
		
	}
	@Override
	public void viewInit() {
		this.crptInvest = commService.find(TbTjCrptInvest.class, rid);
		List<TbTjCrptInvstMining> list = commService.findEntityListByMainId(TbTjCrptInvstMining.class, this.rid);
		if (!CollectionUtils.isEmpty(list)) {
    		StringBuffer names = new StringBuffer();
			for (TbTjCrptInvstMining t : list) {
				names.append("，").append(t.getFkByMiningId().getCodeName());
			}
			this.selectMiningNames = names.substring(1);
		}
		if (null!=this.crptInvest.getFkByZoneId()) {
    		Short zoneType = this.crptInvest.getFkByZoneId().getZoneType();
    		String fullName = this.crptInvest.getFkByZoneId().getFullName();
    		if (zoneType.intValue()>2) {
    			int start = fullName.indexOf("_")+1;
    			this.zoneName = fullName.substring(start).replace("_", "");
			}else {
				this.zoneName = fullName;
			}
		}
	}
	/** 审核通过 */
	public void passAction(){
		try{
			List<TbTjCrptInvest> list = new ArrayList<TbTjCrptInvest>();
			list.add(this.crptInvest);
			crptInvestService.investPassAndUpdateCrptCheck(list);
			this.backAction();
			this.searchAction();
			JsfUtil.addSuccessMessage("审核成功！");
			RequestContext.getCurrentInstance().update("tabView");
		}catch(Exception e){
			JsfUtil.addErrorMessage("审核失败！");
		}
	}

	public void preBackAction(){
		this.backRsn = null;
		RequestContext.getCurrentInstance().update("tabView:editForm:reasonDialog");
		RequestContext.getCurrentInstance().execute("PF('ReasonDialog').show()");
	}
	/** 退回 */
	public void returnBackAction(){
		if(StringUtils.isBlank(this.backRsn)){
			JsfUtil.addErrorMessage("退回原因不允许为空！");
			return;
		}else if(this.backRsn.length() > 200){
			JsfUtil.addErrorMessage("退回原因太长，请缩减部分内容！");
			return;
		}
		try{
			if(null != this.crptInvest && null != this.crptInvest.getRid()){
				this.crptInvest.setCheckDate(new Date());
				this.crptInvest.setFkByCheckUnitId(sessionData.getUser().getTsUnit());
				this.crptInvest.setStateMark(2);
				this.crptInvest.setModifyDate(new Date());
				this.crptInvest.setModifyManid(sessionData.getUser().getRid());
				this.crptInvest.setBackRsn(backRsn);
				this.commService.update(crptInvest);
			}
			this.backRsn = null;
			this.backAction();
			this.searchAction();
			JsfUtil.addSuccessMessage("退回成功！");
			RequestContext.getCurrentInstance().update("tabView");
		}catch(Exception e){
			JsfUtil.addErrorMessage("退回失败！");
			e.printStackTrace();
		}
	}
	/**
 	 * <p>方法描述：撤销</p>
 	 * @MethodAuthor qrr,2021年3月31日,cancleAction
	 * */
	public void cancleAction() {
		try {
			this.crptInvest.setStateMark(1);
			crptInvestService.upsertEntity(this.crptInvest);
			JsfUtil.addSuccessMessage("撤销成功！");
			this.forwardEditPage();
			this.searchAction();
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("撤销失败！");
		}
	}


    /******************************************* 地区变更相关 *******************************************/
    /**
    * @Description : 变更地区下拉集合初始化
    * @MethodAuthor: anjing
    * @Date : 2021/4/1 9:30
    **/
    private void initChangZoneList() {
        this.orginZone = this.crptInvest.getFkByZoneId();
        this.changeZoneName = this.crptInvest.getFkByZoneId().getZoneName();
        this.changeZoneId = this.crptInvest.getFkByZoneId().getRid();
        this.changeZoneCode = this.crptInvest.getFkByZoneId().getZoneGb();
        String fullName = this.crptInvest.getFkByZoneId().getFullName();
        Short zoneType = this.crptInvest.getFkByZoneId().getZoneType();
        if(zoneType.intValue()>2) {
            int start = fullName.indexOf("_")+1;
            this.zoneName = fullName.substring(start).replace("_", "");
        } else {
            this.zoneName = fullName;
        }
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if(null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        // 默认当前登录人所在管辖地区所在省所有地区
        this.changeZoneList = this.commService.findZoneListNew(false, tsZone.getZoneGb().substring(0, 2),"3",null);
    }

	/**
	* @Description : 打开变更地区弹出框
	* @MethodAuthor: anjing
	* @Date : 2021/4/1 9:29
	**/
	public void openChangeZoneDialogAction() {
        this.initChangZoneList();
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("PF('ZoneChangeDialog').show()");
        context.update("tabView:editForm:zoneChangePanel");
    }

    /**
    * @Description : 变更地区确认
    * @MethodAuthor: anjing
    * @Date : 2021/4/1 9:42
    **/
    public void saveChangeZoneAction() {
        this.changeZoneFullName = "";
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if(null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        // 当前登录人所在区编码前缀
        String prefix = tsZone.getZoneGb().substring(0, 4);
        TsZone changeZone = this.commService.find(TsZone.class, this.changeZoneId);
        Short zoneType = changeZone.getZoneType();
        String fullName = changeZone.getFullName();
        if (zoneType.intValue()>2) {
            int start = fullName.indexOf("_")+1;
            int end = StringUtils.ordinalIndexOf(fullName,"_",2);
            this.zoneName = fullName.substring(start).replace("_", "");
            this.changeZoneFullName = fullName.substring(start).replace("_", "");
            if(end > 0) {
                this.changeZoneFullName = fullName.substring(start, end);
            }
        } else {
            this.zoneName = fullName;
            this.changeZoneFullName = fullName;
        }
        RequestContext context = RequestContext.getCurrentInstance();
        if(this.changeZoneCode.startsWith(prefix)) {
            if(zoneType < 4) {
                JsfUtil.addErrorMessage("本辖区内请选择至区县及以下地区！");
                return;
            }
            this.crptInvest.setFkByZoneId(changeZone);
            this.crptInvest.setInvestDate(null);
            this.crptInvest.setFkByFillUnitId(null);
            context.execute("PF('ZoneChangeDialog').hide()");
            context.update("tabView:editForm:crptInfo");
        } else {
            context.execute("PF('ZoneChangeConfirmDialog').show()");
            context.update("tabView:editForm:zoneChangeConfirmDialog");
        }
    }

    /**
     * @Description : 地区变更确认
     * @MethodAuthor: anjing
     * @Date : 2021/3/29 14:56
     **/
    public void zoneChangeConfirmAction() {
        try{
            TsZone changeZone = this.commService.find(TsZone.class, this.changeZoneId);
            Short zoneType = changeZone.getZoneType();
            String fullName = changeZone.getFullName();
            if (zoneType.intValue()>2) {
                int start = fullName.indexOf("_")+1;
                this.zoneName = fullName.substring(start).replace("_", "");
            } else {
                this.zoneName = fullName;
            }
            this.crptInvest.setFkByZoneId(changeZone);
            this.crptInvest.setFkByOrginZoneId(this.orginZone);
            this.crptInvest.setStateMark(4);
            RequestContext context = RequestContext.getCurrentInstance();
            context.execute("PF('ZoneChangeDialog').hide()");
            this.crptInvestService.update(this.crptInvest);
            this.backAction();
            this.searchAction();
            JsfUtil.addSuccessMessage("变更地区成功！");
            context.update("tabView");
        } catch(Exception e) {
            JsfUtil.addErrorMessage("变更地区失败！");
        }
    }

    /**
    * @Description : 确认变更地区提交数据库
    * @MethodAuthor: anjing
    * @Date : 2021/4/1 10:13
    **/
    public void submitZoneChangeAction() {
        try{
            TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
            if(null == tsZone) {
                tsZone = Global.getUser().getTsUnit().getTsZone();
            }
            // 当前登录人所在区编码前缀
            String prefix = tsZone.getZoneGb().substring(0, 4);
            Short zoneType = this.crptInvest.getFkByZoneId().getZoneType();
            this.changeZoneCode = this.crptInvest.getFkByZoneId().getZoneGb();
            if(this.changeZoneCode.startsWith(prefix)) {
                if(zoneType < 4) {
                    JsfUtil.addErrorMessage("本辖区内请选择至区县及以下地区！");
                    return;
                }
                this.crptInvest.setStateMark(5);
                this.crptInvestService.update(this.crptInvest);
            } else {
                this.crptInvest.setStateMark(4);
            }
            this.backAction();
            this.searchAction();
            JsfUtil.addSuccessMessage("确认成功！");
            RequestContext context = RequestContext.getCurrentInstance();
            context.update("tabView");
        } catch(Exception e) {
            JsfUtil.addErrorMessage("确认失败！");
        }
    }

	public List<TsZone> getSearchZoneList() {
		return searchZoneList;
	}
	public String getSearchZoneCode() {
		return searchZoneCode;
	}
	public String getSearchZoneName() {
		return searchZoneName;
	}
	public String getSearchIndusTypeName() {
		return searchIndusTypeName;
	}
	public String getSearchEconomyName() {
		return searchEconomyName;
	}
	public void setSearchZoneList(List<TsZone> searchZoneList) {
		this.searchZoneList = searchZoneList;
	}
	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}
	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}
	public void setSearchIndusTypeName(String searchIndusTypeName) {
		this.searchIndusTypeName = searchIndusTypeName;
	}
	public void setSearchEconomyName(String searchEconomyName) {
		this.searchEconomyName = searchEconomyName;
	}
	public String getSimpleCodeType() {
		return simpleCodeType;
	}
	public void setSimpleCodeType(String simpleCodeType) {
		this.simpleCodeType = simpleCodeType;
	}
	public String getSearchCrptName() {
		return searchCrptName;
	}
	public void setSearchCrptName(String searchCrptName) {
		this.searchCrptName = searchCrptName;
	}
	public String getSearchCreditCode() {
		return searchCreditCode;
	}
	public void setSearchCreditCode(String searchCreditCode) {
		this.searchCreditCode = searchCreditCode;
	}
	public String[] getSearchState() {
		return searchState;
	}
	public void setSearchState(String[] searchState) {
		this.searchState = searchState;
	}
	public Integer getRid() {
		return rid;
	}
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	public List<Object[]> getSelectEntitys() {
		return selectEntitys;
	}
	public void setSelectEntitys(List<Object[]> selectEntitys) {
		this.selectEntitys = selectEntitys;
	}
	public List<TsSimpleCode> getCrptSizeList() {
		return crptSizeList;
	}
	public void setCrptSizeList(List<TsSimpleCode> crptSizeList) {
		this.crptSizeList = crptSizeList;
	}
	public String getSelectCrptSizeNames() {
		return selectCrptSizeNames;
	}
	public void setSelectCrptSizeNames(String selectCrptSizeNames) {
		this.selectCrptSizeNames = selectCrptSizeNames;
	}
	public String getSelectCrptSizeIds() {
		return selectCrptSizeIds;
	}
	public void setSelectCrptSizeIds(String selectCrptSizeIds) {
		this.selectCrptSizeIds = selectCrptSizeIds;
	}

	public TbTjCrptInvest getCrptInvest() {
		return crptInvest;
	}

	public void setCrptInvest(TbTjCrptInvest crptInvest) {
		this.crptInvest = crptInvest;
	}

	public String getBackRsn() {
		return backRsn;
	}

	public void setBackRsn(String backRsn) {
		this.backRsn = backRsn;
	}
	public String getSelectMiningNames() {
		return selectMiningNames;
	}
	public void setSelectMiningNames(String selectMiningNames) {
		this.selectMiningNames = selectMiningNames;
	}
	public String getZoneName() {
		return zoneName;
	}
	public void setZoneName(String zoneName) {
		this.zoneName = zoneName;
	}

    public String getOrginZoneName() {
        return orginZoneName;
    }

    public void setOrginZoneName(String orginZoneName) {
        this.orginZoneName = orginZoneName;
    }

    public Integer getChangeZoneId() {
        return changeZoneId;
    }

    public void setChangeZoneId(Integer changeZoneId) {
        this.changeZoneId = changeZoneId;
    }

    public String getChangeZoneName() {
        return changeZoneName;
    }

    public void setChangeZoneName(String changeZoneName) {
        this.changeZoneName = changeZoneName;
    }

    public String getChangeZoneCode() {
        return changeZoneCode;
    }

    public void setChangeZoneCode(String changeZoneCode) {
        this.changeZoneCode = changeZoneCode;
    }

    public String getChangeZoneFullName() {
        return changeZoneFullName;
    }

    public void setChangeZoneFullName(String changeZoneFullName) {
        this.changeZoneFullName = changeZoneFullName;
    }

    public List<TsZone> getChangeZoneList() {
        return changeZoneList;
    }

    public void setChangeZoneList(List<TsZone> changeZoneList) {
        this.changeZoneList = changeZoneList;
    }
}
