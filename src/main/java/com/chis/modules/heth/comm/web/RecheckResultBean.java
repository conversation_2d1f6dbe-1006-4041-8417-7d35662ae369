package com.chis.modules.heth.comm.web;

import com.chis.common.pojo.*;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Workbook;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;

/**
 * 单位复检结果查询
 *
 * <AUTHOR>
 * @date 2022/5/26
 */
@ManagedBean(name = "recheckResultBean")
@ViewScoped
public class RecheckResultBean extends FacesEditBean implements IProcessData {
    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
     * 查询条件：地区集合
     */
    private List<TsZone> searchZoneList = new ArrayList<>();
    /**
     * 查询条件：地区编码
     */
    private String searchZoneCode;
    /**
     * 查询条件：地区名称
     */
    private String searchZoneName;
    /**
     * 查询条件：用人单位名称
     */
    private String searchCrptName;
    /**
     * 社会信用代码
     */
    private String searchCreditCode;
    /**
     * 查询条件：检查机构
     */
    private String searchUnitName;
    private String searchUnitId;
    /**
     * 查询条件：体检日期-开始日期
     */
    private Date searchBhkBeginDate;
    /**
     * 查询条件：体检日期-结束日期
     */
    private Date searchBhkEndDate;
    /**
     * 查询条件：报告出具日期-开始日期
     */
    private Date searchRptPrintBeginDate;
    /**
     * 查询条件：报告出具日期-结束日期
     */
    private Date searchRptPrintEndDate;
    /**
     * 查询条件-在岗状态
     */
    private String selectOnGuardNames;
    private String selectOnGuardRids;
    private List<TsSimpleCode> onGuardList;
    /**
     * 查询条件-复查危害因素
     */
    private String selectBadRsnNames;
    private String selectBadRsnRids;
    private List<TsSimpleCode> badRsnList;

    private Map<Integer, String> simpleCodeMap;
    /**
     * 未复检总人数
     */
    private Integer totalNumOfUnreviewed;

    private DefaultStreamedContent downloadFile;
    /** Excel 导出工具类 */
    private DynamicExcelExportUtil excelExportUtil;
    /** 动态表格 标题列集合 */
    private List<DynamicRowPO> headRowList;
    /** 动态表格 数据列集合 */
    private List<DynamicRowPO> dataRowList;
    /** 动态表格 标题列集合 */
    private List<DynamicRowPO> headRowList2;
    /** 动态表格 数据列集合 */
    private List<DynamicRowPO> dataRowList2;
    /** 无数据时的colspan */
    private Integer colspanWithoutData;
    /** 查询出的主数据集合 */
    private List<Object[]> queryResultList;
    private List<Object[]> queryResultList2;
    private List<Integer> crptIdList;
    private CellStyle titleCenter;
    private CellStyle conCenter;
    private CellStyle conLeft;

    public RecheckResultBean() {
        init();
    }

    private void init() {
        super.ifSQL = true;
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        //地区
        if (null == this.searchZoneList || this.searchZoneList.size() <= 0) {
            this.searchZoneList = this.commService.findZoneList(false, tsZone.getZoneGb(), "", "");
            this.searchZoneCode = this.searchZoneList.get(0).getZoneCode();
            this.searchZoneName = this.searchZoneList.get(0).getZoneName();
        }
        //报告出具日期
        this.searchRptPrintBeginDate = DateUtils.getDateByDays(-90);
        this.searchRptPrintEndDate = new Date();
        this.simpleCodeMap = new HashMap<>(16);
        //在岗状态
        this.onGuardList = this.commService.findSimpleCodesByTypeId("5009");
        if (!CollectionUtils.isEmpty(this.onGuardList)) {
            for (TsSimpleCode t : this.onGuardList) {
                this.simpleCodeMap.put(t.getRid(), t.getCodeName());
            }
        }
        //危害因素
        this.badRsnList = commService.findSimpleCodesByTypeId("5007");
        if (!CollectionUtils.isEmpty(this.badRsnList)) {
            for (TsSimpleCode t : this.badRsnList) {
                this.simpleCodeMap.put(t.getRid(), t.getCodeName());
            }
        }
        //未复检总人数
        this.totalNumOfUnreviewed = 20;
        try {
            this.totalNumOfUnreviewed = Integer.valueOf(PropertyUtils.getValue("totalNumOfUnreviewed"));
        } catch (Exception ignored) {
        }
    }

    /**
     * 方法描述：查询检查机构
     */
    public void selUnitAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 625, null, 500);

        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add(this.searchUnitId);
        paramMap.put("selectIds", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
    }

    /**
     * 方法描述：处理选择的检查机构
     */
    public void onSelectUnitAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TbTjSrvorg> list = (List<TbTjSrvorg>) selectedMap.get("selectPros");
            if (null != list && list.size() > 0) {
                StringBuilder names = new StringBuilder();
                StringBuilder ids = new StringBuilder();
                for (TbTjSrvorg t : list) {
                    names.append("，").append(t.getUnitName());
                    ids.append(",").append(t.getRid());
                }
                this.searchUnitId = ids.substring(1);
                this.searchUnitName = names.substring(1);
            }
        }
    }

    /**
     * 方法描述：清空检查机构
     */
    public void clearUnit() {
        this.searchUnitId = null;
        this.searchUnitName = null;
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    public void searchAction() {
        if (verifyQueryRequiredFailed()) {
            return;
        }
        super.searchAction();
    }

    private boolean verifyQueryRequiredFailed() {
        boolean verify = false;
        if (StringUtils.isBlank(this.searchZoneCode)) {
            JsfUtil.addErrorMessage("请选择用人单位地区！");
            verify = true;
        }
        if (this.searchRptPrintBeginDate == null) {
            JsfUtil.addErrorMessage("请选择报告打印开始日期！");
            verify = true;
        }
        if (this.searchRptPrintEndDate == null) {
            JsfUtil.addErrorMessage("请选择报告打印结束日期！");
            verify = true;
        }
        return verify;
    }

    @Override
    public String[] buildHqls() {
        StringBuilder baseSql = new StringBuilder();
        baseSql.append("WITH PER_CRPT_TABLE AS ( ");
        baseSql.append("    SELECT T.ENTRUST_CRPT_ID, COUNT(DISTINCT T.PERSON_ID) NUM ");
        baseSql.append("    FROM TD_TJ_BHK T ");
        baseSql.append("        INNER JOIN TB_TJ_CRPT C ON T.ENTRUST_CRPT_ID = C.RID ");
        baseSql.append("        INNER JOIN TS_ZONE Z ON C.ZONE_ID = Z.RID ");
        baseSql.append("        LEFT JOIN TS_SIMPLE_CODE SC1 ON T.ONGUARD_STATEID = SC1.RID ");
        baseSql.append("        LEFT JOIN TD_TJ_BADRSNS B ON T.RID = B.BHK_ID ");
        baseSql.append("        LEFT JOIN TS_SIMPLE_CODE SC2 ON B.EXAM_CONCLUSION_ID = SC2.RID ");
        baseSql.append("    WHERE C.INTER_PRC_TAG = 1 ");
        baseSql.append("      AND T.BHK_TYPE IN (3, 4) ");
        baseSql.append("      AND T.IF_RHK = 0 ");
        baseSql.append("      AND T.NEED_FJ_BUT_NO = 1 ");
        //用人单位地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            baseSql.append("      AND Z.ZONE_GB LIKE :zoneCode ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(StringUtils.objectToString(this.searchZoneCode)).trim()) + "%");
        }
        //用人单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            baseSql.append("  AND C.CRPT_NAME LIKE :crptName ");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchCrptName).trim()) + "%");
        }
        //社会信用代码
        if (StringUtils.isNotBlank(this.searchCreditCode)) {
            baseSql.append("  AND C.INSTITUTION_CODE = :creditCode ");
            this.paramMap.put("creditCode", this.searchCreditCode);
        }
        //检查机构
        if (StringUtils.isNotBlank(this.searchUnitId)) {
            baseSql.append("  AND T.BHKORG_ID IN (:unitId)");
            this.paramMap.put("unitId", StringUtils.string2list(this.searchUnitId, ","));
        }
        //体检日期
        if (this.searchBhkBeginDate != null) {
            baseSql.append("   AND T.BHK_DATE >= TO_DATE(:searchBhkBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchBhkBeginDate", DateUtils.formatDate(this.searchBhkBeginDate) + " 00:00:00");
        }
        if (this.searchBhkEndDate != null) {
            baseSql.append("   AND T.BHK_DATE <= TO_DATE(:searchBhkEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchBhkEndDate", DateUtils.formatDate(this.searchBhkEndDate) + " 23:59:59");
        }
        //报告出具日期
        if (this.searchRptPrintBeginDate != null) {
            baseSql.append("   AND T.RPT_PRINT_DATE >= TO_DATE(:searchRptPrintBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchRptPrintBeginDate", DateUtils.formatDate(this.searchRptPrintBeginDate) + " 00:00:00");
        }
        if (this.searchRptPrintEndDate != null) {
            baseSql.append("   AND T.RPT_PRINT_DATE <= TO_DATE(:searchRptPrintEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchRptPrintEndDate", DateUtils.formatDate(this.searchRptPrintEndDate) + " 23:59:59");
        }
        //在岗状态
        if (StringUtils.isNotBlank(this.selectOnGuardRids)) {
            baseSql.append("  AND T.ONGUARD_STATEID IN (:onGuardRids) ");
            this.paramMap.put("onGuardRids", StringUtils.string2list(this.selectOnGuardRids, ","));
        }
        //复查危害因素
        if (StringUtils.isNotBlank(this.selectBadRsnRids)) {
            baseSql.append("  AND B.BADRSN_ID IN (:badRsnRids) ");
            baseSql.append("  AND SC2.EXTENDS2 = 2 ");
            this.paramMap.put("badRsnRids", StringUtils.string2list(this.selectBadRsnRids, ","));
        }
        baseSql.append("    GROUP BY T.ENTRUST_CRPT_ID ");
        baseSql.append("                        ) ");
        baseSql.append("SELECT T.ENTRUST_CRPT_ID          AS P0, ");
        baseSql.append("       Z.FULL_NAME        AS P1, ");
        baseSql.append("       C.CRPT_NAME        AS P2, ");
        baseSql.append("       C.INSTITUTION_CODE AS P3, ");
        baseSql.append("       C.ADDRESS          AS P4, ");
        baseSql.append("       SC1.CODE_PATH      AS P5, ");
        baseSql.append("       SC2.CODE_NAME      AS P6, ");
        baseSql.append("       SC3.CODE_NAME      AS P7, ");
        baseSql.append("       C.LINKMAN2         AS P8, ");
        baseSql.append("       C.LINKPHONE2       AS P9, ");
        baseSql.append("       T.NUM              AS P10, ");
        baseSql.append("       SC1.CODE_NAME      AS P11 ");
        baseSql.append("FROM PER_CRPT_TABLE T ");
        baseSql.append("         INNER JOIN TB_TJ_CRPT C ON T.ENTRUST_CRPT_ID = C.RID ");
        baseSql.append("         INNER JOIN TS_ZONE Z ON C.ZONE_ID = Z.RID ");
        baseSql.append("         INNER JOIN TS_SIMPLE_CODE SC1 ON C.INDUS_TYPE_ID = SC1.RID ");
        baseSql.append("         INNER JOIN TS_SIMPLE_CODE SC2 ON C.ECONOMY_ID = SC2.RID ");
        baseSql.append("         INNER JOIN TS_SIMPLE_CODE SC3 ON C.CRPT_SIZE_ID = SC3.RID ");
        //未复检总人数
        if (this.totalNumOfUnreviewed != null) {
            baseSql.append("WHERE T.NUM >= :totalNumOfUnreviewed ");
            this.paramMap.put("totalNumOfUnreviewed", this.totalNumOfUnreviewed);
        }
        String sql1 = "SELECT * FROM (" + baseSql + ") AA ORDER BY P1, P2";
        String sql2 = "SELECT COUNT(1) FROM (" + baseSql + ")";
        return new String[]{sql1, sql2};
    }

    @Override
    public void processData(List<?> list) {
        if (!CollectionUtils.isEmpty(list)) {
            List<Object[]> result = (List<Object[]>) list;
            for (Object[] o : result) {
                //地区去除省
                o[1] = removeProv((String) o[1]);
                //联系人电话脱敏
                o[9] = StringUtils.encryptPhone((String) o[9]);
            }
        }
    }

    /**
     * <p>描述 地区移除省</p>
     *
     * @param fullname
     * @MethodAuthor gongzhe,2022/6/22 16:59,removeProv
     * @return java.lang.String
     */
    private String removeProv(String fullname){
        if(StringUtils.isNotBlank(fullname)) {
            int index = fullname.indexOf("_");
            if(index!=-1){
                return fullname.substring(index+1);
            }
        }
        return fullname;
    }

    /**
     * <p>描述 导出查询</p>
     *
     * @MethodAuthor gongzhe,2022/6/21 19:20,executeQueryInfo
     * @return void
     */
    private void executeQueryInfo(){
        this.headRowList=new ArrayList<>();
        this.dataRowList=new ArrayList<>();
        this.headRowList2=new ArrayList<>();
        this.dataRowList2=new ArrayList<>();
        this.fillHeadRows();
        String[] sql = buildHqls();
        this.queryResultList = this.commService.findDataBySqlNoPage(sql[0], paramMap);
        this.fillDataRows1();
        if(!CollectionUtils.isEmpty(this.queryResultList)){
            queryResultList2 = new ArrayList<>();
            int length = queryResultList.size();
            int allDataCount = length % 500 == 0 ? length / 500 : ((length / 500) + 1);
            for (int i = 0; i < allDataCount; i++) {
                int endIndex = Math.min((i+1)*500,length);
                List<Integer> subList = crptIdList.subList(i*500,endIndex);
                queryResultList2.addAll(executeQueryDetailInfo(subList));
            }
            this.fillDataRows2();
        }
    }

    /**
     * <p>描述 导出查询劳动者档案明细</p>
     *
     * @MethodAuthor gongzhe,2022/6/21 19:20,executeQueryDetailInfo
     * @return void
     */
    private List<Object[]> executeQueryDetailInfo(List<Integer> crptIds){
        StringBuilder baseSql = new StringBuilder();
        Map<String, Object> map = new HashMap<>();
        baseSql.append(" WITH CRPT_ID_TABLE AS ( ");
        baseSql.append(" SELECT TRIM(REGEXP_SUBSTR('"+StringUtils.list2string(crptIds,",")+"','[^,]+', 1, level)) AS CRPT_ID ");
        baseSql.append(" FROM DUAL CONNECT BY level <= "+crptIds.size()+"");
        baseSql.append(" ),FIRST_TABLE AS (");
        baseSql.append(" SELECT T.RID,ROW_NUMBER() OVER (PARTITION BY T.PERSON_ID,T.CRPT_ID ORDER BY T.BHK_DATE DESC,T.RID DESC) ROWRN ");
        baseSql.append(" FROM TD_TJ_BHK T");
        baseSql.append(" INNER JOIN CRPT_ID_TABLE ON CRPT_ID_TABLE.CRPT_ID = T.ENTRUST_CRPT_ID");
        baseSql.append(" INNER JOIN TB_TJ_CRPT C ON CRPT_ID_TABLE.CRPT_ID = C.RID ");
        baseSql.append(" INNER JOIN TS_ZONE Z ON C.ZONE_ID = Z.RID");
        baseSql.append(" LEFT JOIN TS_SIMPLE_CODE SC1 ON T.ONGUARD_STATEID = SC1.RID");
        baseSql.append(" LEFT JOIN TB_TJ_SRVORG SRVORG ON T.BHKORG_ID = SRVORG.RID");
        baseSql.append(" LEFT JOIN TD_TJ_BADRSNS B ON T.RID = B.BHK_ID");
        baseSql.append(" LEFT JOIN TS_SIMPLE_CODE SC3 ON B.EXAM_CONCLUSION_ID = SC3.RID ");
        baseSql.append(" WHERE 1 = 1");
        baseSql.append(" AND T.BHK_TYPE IN (3, 4)");
        baseSql.append(" AND T.IF_RHK = 0");
        baseSql.append(" AND T.NEED_FJ_BUT_NO = 1");
        //检查机构
        if (StringUtils.isNotBlank(this.searchUnitId)) {
            baseSql.append("  AND T.BHKORG_ID IN (:unitId)");
            map.put("unitId", StringUtils.string2list(this.searchUnitId, ","));
        }
        //体检日期
        if (this.searchBhkBeginDate != null) {
            baseSql.append("   AND T.BHK_DATE >= TO_DATE(:searchBhkBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            map.put("searchBhkBeginDate", DateUtils.formatDate(this.searchBhkBeginDate) + " 00:00:00");
        }
        if (this.searchBhkEndDate != null) {
            baseSql.append("   AND T.BHK_DATE <= TO_DATE(:searchBhkEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            map.put("searchBhkEndDate", DateUtils.formatDate(this.searchBhkEndDate) + " 23:59:59");
        }
        //报告出具日期
        if (this.searchRptPrintBeginDate != null) {
            baseSql.append("   AND T.RPT_PRINT_DATE >= TO_DATE(:searchRptPrintBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            map.put("searchRptPrintBeginDate", DateUtils.formatDate(this.searchRptPrintBeginDate) + " 00:00:00");
        }
        if (this.searchRptPrintEndDate != null) {
            baseSql.append("   AND T.RPT_PRINT_DATE <= TO_DATE(:searchRptPrintEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            map.put("searchRptPrintEndDate", DateUtils.formatDate(this.searchRptPrintEndDate) + " 23:59:59");
        }
        //在岗状态
        if (StringUtils.isNotBlank(this.selectOnGuardRids)) {
            baseSql.append("  AND T.ONGUARD_STATEID IN (:onGuardRids) ");
            map.put("onGuardRids", StringUtils.string2list(this.selectOnGuardRids, ","));
        }
        //复查危害因素
        if (StringUtils.isNotBlank(this.selectBadRsnRids)) {
            baseSql.append("  AND B.BADRSN_ID IN (:badRsnRids) ");
            baseSql.append("  AND SC3.EXTENDS2 = 2 ");
            map.put("badRsnRids", StringUtils.string2list(this.selectBadRsnRids, ","));
        }
        baseSql.append("),SEC_TABLE AS (SELECT Z.FULL_NAME, C.CRPT_NAME, C.ADDRESS,C.INSTITUTION_CODE,T.PERSON_NAME,");
        baseSql.append("  SC1.CODE_NAME, SRVORG.UNIT_NAME,");
        baseSql.append("  T.BHK_CODE        AS F_BHK_CODE,");
        baseSql.append("  T.BHK_DATE        AS F_BHK_DATE,");
        baseSql.append("  T.RPT_PRINT_DATE  AS F_RPT_PRINT_DATE,");
        baseSql.append("  LISTAGG(DECODE(SC3.EXTENDS2,2,SC2.CODE_NAME,NULL), ',') WITHIN GROUP (ORDER BY SC2.NUM) AS F_BADNAMES,");
        baseSql.append("  TO_CHAR(T.MHKADV) AS F_MHKADV,T.BHKORG_ID AS F_SRVORGRID,E.CRPT_NAME AS crptName,E.INSTITUTION_CODE AS institutionCode");
        baseSql.append("  FROM TD_TJ_BHK T INNER JOIN FIRST_TABLE ON FIRST_TABLE.RID = T.RID AND FIRST_TABLE.ROWRN = 1");
        baseSql.append("  INNER JOIN TB_TJ_CRPT C ON T.ENTRUST_CRPT_ID = C.RID AND C.INTER_PRC_TAG = 1 ");
        baseSql.append("  INNER JOIN TB_TJ_CRPT E ON T.CRPT_ID = E.RID ");
        baseSql.append("  INNER JOIN TS_ZONE Z ON C.ZONE_ID = Z.RID");
        baseSql.append("  LEFT JOIN TS_SIMPLE_CODE SC1 ON T.ONGUARD_STATEID = SC1.RID");
        baseSql.append("  LEFT JOIN TB_TJ_SRVORG SRVORG ON T.BHKORG_ID = SRVORG.RID");
        baseSql.append("  LEFT JOIN TD_TJ_BADRSNS B ON T.RID = B.BHK_ID");
        baseSql.append("  LEFT JOIN TS_SIMPLE_CODE SC2 ON B.BADRSN_ID = SC2.RID");
        baseSql.append("  LEFT JOIN TS_SIMPLE_CODE SC3 ON B.EXAM_CONCLUSION_ID = SC3.RID ");
        baseSql.append("    GROUP BY T.RID,Z.FULL_NAME,C.CRPT_NAME,C.ADDRESS,C.INSTITUTION_CODE,T.PERSON_NAME,SC1.CODE_NAME,");
        baseSql.append("SRVORG.UNIT_NAME,T.BHK_CODE,T.BHK_DATE,T.RPT_PRINT_DATE,TO_CHAR(T.MHKADV),T.BHKORG_ID,E.CRPT_NAME,E.INSTITUTION_CODE");
        baseSql.append("), RECHECK_TABLE AS (");
        baseSql.append(" SELECT BB.LAST_FST_BHK_CODE,");
        baseSql.append(" BB.R_BHK_CODE,");
        baseSql.append(" BB.R_BHK_DATE,");
        baseSql.append(" BB.R_RPT_PRINT_DATE,");
        baseSql.append(" BB.R_MHKADV,");
        baseSql.append(" LISTAGG(DECODE(SC3.EXTENDS2,2,SC2.CODE_NAME,NULL), ',') WITHIN GROUP (ORDER BY SC2.NUM) AS R_BADNAMES");
        baseSql.append(" FROM (SELECT T.RID,");
        baseSql.append(" T.LAST_FST_BHK_CODE,");
        baseSql.append(" T.BHK_CODE        AS R_BHK_CODE,");
        baseSql.append(" T.BHK_DATE        AS R_BHK_DATE,");
        baseSql.append(" T.RPT_PRINT_DATE  AS R_RPT_PRINT_DATE,");
        baseSql.append(" TO_CHAR(T.MHKADV) AS R_MHKADV,");
        baseSql.append(" ROW_NUMBER() OVER (PARTITION BY T.LAST_FST_BHK_CODE,T.BHKORG_ID ORDER BY T.BHK_CODE DESC) ROWRN");
        baseSql.append(" FROM TD_TJ_BHK T");
        baseSql.append(" INNER JOIN SEC_TABLE ON SEC_TABLE.F_BHK_CODE = T.LAST_FST_BHK_CODE AND SEC_TABLE.F_SRVORGRID = T.BHKORG_ID");
        baseSql.append(" LEFT JOIN TB_TJ_SRVORG SRVORG ON T.BHKORG_ID = SRVORG.RID");
        baseSql.append(" WHERE T.IF_RHK = 1");
        baseSql.append(" ) BB LEFT JOIN TD_TJ_BADRSNS B ON BB.RID = B.BHK_ID");
        baseSql.append(" LEFT JOIN TS_SIMPLE_CODE SC2 ON B.BADRSN_ID = SC2.RID");
        baseSql.append("  LEFT JOIN TS_SIMPLE_CODE SC3 ON B.EXAM_CONCLUSION_ID = SC3.RID ");
        baseSql.append(" WHERE ROWRN = 1");
        baseSql.append(" GROUP BY BB.LAST_FST_BHK_CODE, BB.R_BHK_CODE, BB.R_BHK_DATE, BB.R_RPT_PRINT_DATE, BB.R_MHKADV");
        baseSql.append(")");
        baseSql.append(" SELECT * ");
        baseSql.append(" FROM SEC_TABLE F LEFT JOIN RECHECK_TABLE R ON R.LAST_FST_BHK_CODE = F.F_BHK_CODE");
        baseSql.append(" ORDER BY F.FULL_NAME, F.CRPT_NAME ");
        return this.commService.findDataBySqlNoPage(baseSql.toString(), map);
    }

    /**
     * <p>描述 填充sheet标题</p>
     *
     * @MethodAuthor gongzhe,2022/6/21 19:20,fillHeadRows
     * @return void
     */
    public void fillHeadRows() {
        //第一个sheet
        DynamicRowPO row = new DynamicRowPO();
        List<DynamicColPO> cols = new ArrayList<>();
        cols.add(this.headColHelper("用工单位地区", 1, 1,"width:200px;" , 9000));
        cols.add(this.headColHelper("用工单位名称", 1, 1,"width:200px;" , 9000));
        cols.add(this.headColHelper("单位地址", 1, 1,"width:200px;" , 9000));
        cols.add(this.headColHelper("社会信用代码", 1, 1,"width:160px;" , 6000));
        cols.add(this.headColHelper("行业类别", 1, 1,"width:160px;" , 6000));
        cols.add(this.headColHelper("经济类型", 1, 1,"width:120px;" , 4000));
        cols.add(this.headColHelper("企业规模", 1, 1,"width:100px;" , 4000));
        cols.add(this.headColHelper("联系人", 1, 1,"width:100px;" , 4000));
        cols.add(this.headColHelper("联系电话", 1, 1,"width:100px;" , 4000));
        cols.add(this.headColHelper("未复检总人数", 1, 1,"width:100px;" , 4000));
        row.setCols(cols);
        this.headRowList.add(row);
        //第二个sheet
        DynamicRowPO row1 = new DynamicRowPO();
        List<DynamicColPO> cols1 = new ArrayList<>();
        cols1.add(this.headColHelper("用工单位地区", 2, 1,"width:200px;" , 9000));
        cols1.add(this.headColHelper("用工单位名称", 2, 1,"width:200px;" , 9000));
        cols1.add(this.headColHelper("用工单位地址", 2, 1,"width:200px;" , 9000));
        cols1.add(this.headColHelper("用工单位社会信用代码", 2, 1,"width:160px;" , 6000));
        cols1.add(this.headColHelper("用人单位名称", 2, 1,"width:200px;" , 9000));
        cols1.add(this.headColHelper("用人单位社会信用代码", 2, 1,"width:160px;" , 6000));
        cols1.add(this.headColHelper("姓名", 2, 1,"width:100px;" , 4000));
        cols1.add(this.headColHelper("在岗状态", 2, 1,"width:100px;" , 4000));
        cols1.add(this.headColHelper("体检机构名称", 2, 1,"width:160px;" , 6000));
        cols1.add(this.headColHelper("初检信息", 1, 5,"width:100px;" , 4000));
        cols1.add(this.headColHelper("最新复检信息", 1, 5,"width:100px;" , 4000));
        row1.setCols(cols1);
        this.headRowList2.add(row1);
        DynamicRowPO row2 = new DynamicRowPO();
        List<DynamicColPO> cols2 = new ArrayList<>();
        cols2.add(this.headColHelper("体检编号", 1, 1,"width:160px;" , 6000));
        cols2.add(this.headColHelper("体检日期", 1, 1,"width:100px;" , 4000));
        cols2.add(this.headColHelper("报告出具日期", 1, 1,"width:100px;" , 4000));
        cols2.add(this.headColHelper("复查危害因素", 1, 1,"width:200px;" , 9000));
        cols2.add(this.headColHelper("主检建议", 1, 1,"width:200px;" , 9000));
        cols2.add(this.headColHelper("体检编号", 1, 1,"width:100px;" , 6000));
        cols2.add(this.headColHelper("体检日期", 1, 1,"width:160px;" , 4000));
        cols2.add(this.headColHelper("报告出具日期", 1, 1,"width:100px;" , 4000));
        cols2.add(this.headColHelper("复查危害因素", 1, 1,"width:200px;" , 9000));
        cols2.add(this.headColHelper("主检建议", 1, 1,"width:200px;" , 9000));
        row2.setCols(cols2);
        this.headRowList2.add(row2);
    }

    /**
     * <p>描述 填充sheet1数据（企业汇总）</p>
     *
     * @MethodAuthor gongzhe,2022/6/21 19:21,fillDataRows1
     * @return void
     */
    public void fillDataRows1() {
        if(CollectionUtils.isEmpty(this.queryResultList)){
            return;
        }
        crptIdList = new ArrayList<>();
        //用人单位地区	用人单位名称	单位地址	社会信用代码	行业类别	经济类型	企业规模	联系人	联系电话	未复检总人数
        for(Object[] itm : this.queryResultList){
            crptIdList.add(Integer.valueOf(StringUtils.objectToString(itm[0])));
            DynamicRowPO rowPO = new DynamicRowPO();
            List<DynamicColPO> cols = new ArrayList<>();
            cols.add(this.createStrColWithCeLLAlign(removeProv(StringUtils.objectToString(itm[1])),CellStyle.ALIGN_LEFT));
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[2]),CellStyle.ALIGN_LEFT));
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[4]),CellStyle.ALIGN_LEFT));
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[3]),CellStyle.ALIGN_CENTER));
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[5]),CellStyle.ALIGN_LEFT));
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[6]),CellStyle.ALIGN_LEFT));
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[7]),CellStyle.ALIGN_CENTER));
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[8]),CellStyle.ALIGN_CENTER));
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[9]),CellStyle.ALIGN_CENTER));
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[10]),CellStyle.ALIGN_CENTER));
            rowPO.setCols(cols);
            this.dataRowList.add(rowPO);
        }
    }
    /**
     * <p>描述 填充sheet2数据（劳动者明细）</p>
     *
     * @MethodAuthor gongzhe,2022/6/21 19:21,fillDataRows2
     * @return void
     */
    public void fillDataRows2() {
        if(CollectionUtils.isEmpty(this.queryResultList2)){
            return;
        }
        //用人单位地区 用人单位名称 单位地址 社会信用代码 姓名 在岗状态 体检机构名称
        // 体检编号 体检日期 报告出具日期 复查危害因素 主检建议
        // 体检编号 体检日期 报告出具日期 复查危害因素 主检建议
        for(Object[] itm : this.queryResultList2){
            DynamicRowPO rowPO = new DynamicRowPO();
            List<DynamicColPO> cols = new ArrayList<>();
            cols.add(this.createStrColWithCeLLAlign(removeProv(StringUtils.objectToString(itm[0])),CellStyle.ALIGN_LEFT));//用人单位地区
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[1]),CellStyle.ALIGN_LEFT));// 用人单位名称
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[2]),CellStyle.ALIGN_LEFT));//单位地址
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[3]),CellStyle.ALIGN_CENTER));//社会信用代码

            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[13]),CellStyle.ALIGN_LEFT));//单位名称
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[14]),CellStyle.ALIGN_CENTER));//社会信用代码

            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[4]),CellStyle.ALIGN_CENTER));//姓名
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[5]),CellStyle.ALIGN_CENTER));//在岗状态
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[6]),CellStyle.ALIGN_LEFT));//体检机构名称
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[7]),CellStyle.ALIGN_CENTER));//体检编号
            cols.add(this.createStrColWithCeLLAlign(itm[8] == null ? "" : DateUtils.formatDate((Date)itm[8]),CellStyle.ALIGN_CENTER));//体检日期
            cols.add(this.createStrColWithCeLLAlign(itm[9] == null ? "" : DateUtils.formatDate((Date)itm[9]),CellStyle.ALIGN_CENTER));//报告出具日期
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[10]),CellStyle.ALIGN_LEFT));//复查危害因素
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[11]),CellStyle.ALIGN_LEFT));//主检建议
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[16]),CellStyle.ALIGN_CENTER));//体检编号
            cols.add(this.createStrColWithCeLLAlign(itm[17] == null ? "" : DateUtils.formatDate((Date)itm[17]),CellStyle.ALIGN_CENTER));//体检日期
            cols.add(this.createStrColWithCeLLAlign(itm[18] == null ? "" : DateUtils.formatDate((Date)itm[18]),CellStyle.ALIGN_CENTER));//报告出具日期
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[20]),CellStyle.ALIGN_LEFT));//复查危害因素
            cols.add(this.createStrColWithCeLLAlign(StringUtils.objectToString(itm[19]),CellStyle.ALIGN_LEFT));//主检建议
            rowPO.setCols(cols);
            this.dataRowList2.add(rowPO);
        }
    }
    /**
     * @Description: 创建有对齐方式的文本单元格
     *
     * @MethodAuthor pw,2022年04月21日
     */
    private DynamicColPO createStrColWithCeLLAlign(String strVal, Short cellAlign){
        DynamicColPO colPO = new DynamicColPO(null, 1, 1, null, null, null, null,
                strVal, null, null, null, null);
        if(cellAlign != null){
            colPO.setCellAlign(cellAlign);
        }
        return colPO;
    }
    /**
     * @Description: 辅助生成普通文本单元格
     *
     * @MethodAuthor pw,2022年04月3日
     */
    protected DynamicColPO headColHelper(String colStrVal, Integer rowspan,
                                         Integer colspan, String style, Integer columnWidth){
        return new DynamicColPO(null, rowspan, colspan, style, null, null, "bold",
                colStrVal, null, null, columnWidth, null);
    }

    /**
     * <p>描述 辅助生成Workbook</p>
     *
     * @MethodAuthor gongzhe,2022/6/21 19:22,generateWorkBook
     * @return org.apache.poi.ss.usermodel.Workbook
     */
    private Workbook generateWorkBook(){
        if(null == this.excelExportUtil){
            this.excelExportUtil = new DynamicExcelExportUtil();
        }
        //重新创建一下 避免操作的上次导出的对象
        this.excelExportUtil.createWorkbook();
        this.titleCenter =   this.excelExportUtil.customDataLeftStyle(CellStyle.ALIGN_CENTER, (short) 4, false,
                (short)11, true);
        this.conCenter =   this.excelExportUtil.customDataLeftStyle(CellStyle.ALIGN_CENTER, (short) 4, false,
                (short)11, false);
        this.conLeft =  this.excelExportUtil.customDataLeftStyle(CellStyle.ALIGN_LEFT, (short) 4, false,
                (short)11, false);
        this.fillUtilSheetList();
        return this.excelExportUtil.generateWorkbook();
    }

    private void fillUtilSheetList(){
        List<ExportDynamicSheetPO> sheetList = new ArrayList<>();
        ExportDynamicSheetPO sheetPO = new ExportDynamicSheetPO();
        sheetPO.setSheetName("企业汇总信息");
        //headRowList
        sheetPO.setHeadRowList(this.createHeadRowList(this.headRowList));
        //dataRowList
        sheetPO.setDataRowList(this.createRowHelper(this.dataRowList));
        sheetList.add(sheetPO);
        ExportDynamicSheetPO sheetPO2 = new ExportDynamicSheetPO();
        sheetPO2.setSheetName("劳动者个案信息");
        //headRowList
        sheetPO2.setHeadRowList(this.createHeadRowList(this.headRowList2));
        sheetPO2.setDataRowList(this.createRowHelper(this.dataRowList2));
        sheetList.add(sheetPO2);
        this.excelExportUtil.setSheetList(sheetList);
    }

    /**
     * @Description: 创建excel导出 标题行
     *
     * @MethodAuthor pw,2022年04月13日
     */
    private List<ExportDynamicRowPO> createHeadRowList(List<DynamicRowPO> headRowList){
        List<ExportDynamicRowPO> resultList = new ArrayList<>();
        ExportDynamicRowPO rowPO = new ExportDynamicRowPO();
        rowPO.setHeightInPoints(30f);
        List<ExportDynamicRowPO> tmpList = this.createRowHelper(headRowList);
        if(!CollectionUtils.isEmpty(tmpList)){
            resultList.addAll(tmpList);
        }
        if(resultList.size() > 1){
            resultList.get(1).setHeightInPoints(25f);
        }
        return resultList;
    }

    private List<ExportDynamicRowPO> createRowHelper(List<DynamicRowPO> rowList){
        List<ExportDynamicRowPO> resultList = new ArrayList<>();
        if(CollectionUtils.isEmpty(rowList)){
            return resultList;
        }
        for(DynamicRowPO rowPO : rowList){
            ExportDynamicRowPO dynamicRowPO = new ExportDynamicRowPO();
            List<DynamicColPO> cols = rowPO.getCols();
            if(CollectionUtils.isEmpty(cols)){
                continue;
            }
            dynamicRowPO.setCols(new ArrayList<ExportDynamicColPO>());
            for(DynamicColPO colPO : cols){
                dynamicRowPO.getCols().add(this.createDynamicCol(colPO));
            }
            resultList.add(dynamicRowPO);
        }
        return resultList;
    }

    /**
     * @Description: 创建导出的单元格对象
     *
     * @MethodAuthor pw,2022年04月13日
     */
    private ExportDynamicColPO createDynamicCol(DynamicColPO colPO){
        ExportDynamicColPO dynamicColPO = new ExportDynamicColPO();
        dynamicColPO.setRowspan(colPO.getRowspan());
        dynamicColPO.setColspan(colPO.getColspan());
        if(null != colPO.getColumnWidth()){
            dynamicColPO.setColWidth(colPO.getColumnWidth());
        }
        Integer type = colPO.getType();
        dynamicColPO.setColVal(null == type ? "" : (1 == type ? colPO.getColStrVal() : (2 == type ? colPO.getColIntVal() : (3 == type ? colPO.getColDecimalVal() : ""))));
        Short cellAlign = colPO.getCellAlign();
        if("bold".equals(colPO.getFontWeight())){
            //表格标题
            dynamicColPO.setColStyle(titleCenter);
        }else if(null != cellAlign){
            if(cellAlign.compareTo(CellStyle.ALIGN_CENTER) == 0){
                dynamicColPO.setColStyle(conCenter);
            }else {
                dynamicColPO.setColStyle(conLeft);
            }
        }
        return dynamicColPO;
    }

    public DefaultStreamedContent getDownloadFile(){
        if (verifyQueryRequiredFailed()) {
            return null;
        }
        ByteArrayOutputStream baos = null;
        try {
            executeQueryInfo();
            Workbook wBook = this.generateWorkBook();
            baos = new ByteArrayOutputStream();
            wBook.write(baos);
            baos.flush();
            byte[] aa = baos.toByteArray();
            String fileName = new String( "体检复查结果信息汇总.xlsx".getBytes("GBK"),"ISO-8859-1");
            return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
        } catch(Exception e) {
            JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            e.printStackTrace();
        } finally {
            if (baos != null) {
                try {
                    baos.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }


    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchCreditCode() {
        return searchCreditCode;
    }

    public void setSearchCreditCode(String searchCreditCode) {
        this.searchCreditCode = searchCreditCode;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public String getSearchUnitId() {
        return searchUnitId;
    }

    public void setSearchUnitId(String searchUnitId) {
        this.searchUnitId = searchUnitId;
    }

    public Date getSearchBhkBeginDate() {
        return searchBhkBeginDate;
    }

    public void setSearchBhkBeginDate(Date searchBhkBeginDate) {
        this.searchBhkBeginDate = searchBhkBeginDate;
    }

    public Date getSearchBhkEndDate() {
        return searchBhkEndDate;
    }

    public void setSearchBhkEndDate(Date searchBhkEndDate) {
        this.searchBhkEndDate = searchBhkEndDate;
    }

    public Date getSearchRptPrintBeginDate() {
        return searchRptPrintBeginDate;
    }

    public void setSearchRptPrintBeginDate(Date searchRptPrintBeginDate) {
        this.searchRptPrintBeginDate = searchRptPrintBeginDate;
    }

    public Date getSearchRptPrintEndDate() {
        return searchRptPrintEndDate;
    }

    public void setSearchRptPrintEndDate(Date searchRptPrintEndDate) {
        this.searchRptPrintEndDate = searchRptPrintEndDate;
    }

    public String getSelectOnGuardNames() {
        return selectOnGuardNames;
    }

    public void setSelectOnGuardNames(String selectOnGuardNames) {
        this.selectOnGuardNames = selectOnGuardNames;
    }

    public String getSelectOnGuardRids() {
        return selectOnGuardRids;
    }

    public void setSelectOnGuardRids(String selectOnGuardRids) {
        this.selectOnGuardRids = selectOnGuardRids;
    }

    public List<TsSimpleCode> getOnGuardList() {
        return onGuardList;
    }

    public void setOnGuardList(List<TsSimpleCode> onGuardList) {
        this.onGuardList = onGuardList;
    }

    public String getSelectBadRsnNames() {
        return selectBadRsnNames;
    }

    public void setSelectBadRsnNames(String selectBadRsnNames) {
        this.selectBadRsnNames = selectBadRsnNames;
    }

    public String getSelectBadRsnRids() {
        return selectBadRsnRids;
    }

    public void setSelectBadRsnRids(String selectBadRsnRids) {
        this.selectBadRsnRids = selectBadRsnRids;
    }

    public List<TsSimpleCode> getBadRsnList() {
        return badRsnList;
    }

    public void setBadRsnList(List<TsSimpleCode> badRsnList) {
        this.badRsnList = badRsnList;
    }

    public Map<Integer, String> getSimpleCodeMap() {
        return simpleCodeMap;
    }

    public void setSimpleCodeMap(Map<Integer, String> simpleCodeMap) {
        this.simpleCodeMap = simpleCodeMap;
    }

    public Integer getTotalNumOfUnreviewed() {
        return totalNumOfUnreviewed;
    }

    public void setTotalNumOfUnreviewed(Integer totalNumOfUnreviewed) {
        this.totalNumOfUnreviewed = totalNumOfUnreviewed;
    }
    public void setDownloadFile(DefaultStreamedContent downloadFile) {
        this.downloadFile = downloadFile;
    }

    public Integer getColspanWithoutData() {
        this.colspanWithoutData = 0;
        if(!CollectionUtils.isEmpty(this.headRowList)){
            List<DynamicColPO> firstColPOList = this.headRowList.get(0).getCols();
            if(!CollectionUtils.isEmpty(firstColPOList)){
                for(DynamicColPO colPO : firstColPOList){
                    Integer colSpan = colPO.getColspan();
                    if(null != colSpan){
                        this.colspanWithoutData += colSpan;
                    }
                }
            }
        }
        if(0 == this.colspanWithoutData){
            this.colspanWithoutData = 1;
        }
        return colspanWithoutData;
    }

    public void setColspanWithoutData(Integer colspanWithoutData) {
        this.colspanWithoutData = colspanWithoutData;
    }
}
