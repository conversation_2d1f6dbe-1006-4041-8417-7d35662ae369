package com.chis.modules.heth.comm.entity;

import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2019-9-3
 */
@Entity
@Table(name = "TD_ZW_YSZYB_RPT")
@SequenceGenerator(name = "TdZwYszybRpt", sequenceName = "TD_ZW_YSZYB_RPT_SEQ", allocationSize = 1)
public class TdZwYszybRpt implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer rptYear;
	private String personnelName;
	private String idc;
	private String linktel;
	private TbTjCrpt fkByCrptId;
	private TsZone fkByZoneId;
	private String crptName;
	private String creditCode;
	private String address;
	private String postcode;
	private String safeposition;
	private String safephone;
	private TsSimpleCode fkByEconomyId;
	private TsSimpleCode fkByIndusTypeId;
	private TsSimpleCode fkByCrptSizeId;
	private Integer sex;
	private Date birthday;
	private String yszybName;
	private String tchBadrsns;
	private String analyWork;
	private Integer tchWorkYear;
	private Integer tchWorkMonth;
	private Date findDate;
	private TsSimpleCode fkBySourceId;
	private String otherSource;
	private TdZwTjorginfoComm fkByFillUnitId;
	private String unitRespPsn;
	private String fillFormPsn;
	private String fillLink;
	private Date fillDate;
	private Integer state;
	private Integer delMark;
	private Integer relBhkId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	private String annexPath;
	private String rptNo;
	private Integer tchWorkDay;
	private Integer tchWorkHour;
	private Integer tchWorkMinute;
	private TsSimpleCode fkByCardTypeId;
	private Date harmStartDate;
	private TsSimpleCode fkByOccDiseid;
	private String yszybTypeName;
	private Integer zyPoisonType;
	private TsSimpleCode fkByWorkTypeId;
	private String workOther;
	private String discoveryUnit;
	private String discoveryRespPsn;
	private String rptPsn;
	private String rptLink;
	private Date rptDate;
	private String rmk;
	//详情显示工种
	private String workName;

	/**
	 * 上报单位
	 */
	private TsUnit fkRptUnitId;
	/**
	 * 用工单位ID
	 */
	private TbTjCrpt fkByEmpCrptId;
	/**
	 * 用工单位地区ID
	 */
	private TsZone fkByEmpZoneId;
	/**
	 * 用工单位名称
	 */
	private String empCrptName;
	/**
	 * 用工单位社会信用代码
	 */
	private String empCreditCode;
	/**
	 * 用工单位经济类型ID
	 */
	private TsSimpleCode fkByEmpEconomyId;
	/**
	 * 用工单位行业类别ID
	 */
	private TsSimpleCode fkByEmpIndusTypeId;
	/**
	 * 用工单位企业规模ID
	 */
	private TsSimpleCode fkByEmpCrptSizeId;

	private List<TdZwYszybTchBadrsn> badrsnList;
	public TdZwYszybRpt() {
	}

	public TdZwYszybRpt(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwYszybRpt")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "RPT_YEAR")	
	public Integer getRptYear() {
		return rptYear;
	}

	public void setRptYear(Integer rptYear) {
		this.rptYear = rptYear;
	}	
			
	@Column(name = "PERSONNEL_NAME")	
	public String getPersonnelName() {
		return personnelName;
	}

	public void setPersonnelName(String personnelName) {
		this.personnelName = personnelName;
	}	
			
	@Column(name = "IDC")	
	public String getIdc() {
		return idc;
	}

	public void setIdc(String idc) {
		this.idc = idc;
	}	
			
	@Column(name = "LINKTEL")	
	public String getLinktel() {
		return linktel;
	}

	public void setLinktel(String linktel) {
		this.linktel = linktel;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_ID")			
	public TbTjCrpt getFkByCrptId() {
		return fkByCrptId;
	}

	public void setFkByCrptId(TbTjCrpt fkByCrptId) {
		this.fkByCrptId = fkByCrptId;
	}

	@ManyToOne
	@JoinColumn(name = "ZONE_ID")
	public TsZone getFkByZoneId() {
		return fkByZoneId;
	}

	public void setFkByZoneId(TsZone fkByZoneId) {
		this.fkByZoneId = fkByZoneId;
	}

	@Column(name = "CRPT_NAME")
	public String getCrptName() {
		return crptName;
	}

	public void setCrptName(String crptName) {
		this.crptName = crptName;
	}	
			
	@Column(name = "CREDIT_CODE")	
	public String getCreditCode() {
		return creditCode;
	}

	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}	
			
	@Column(name = "ADDRESS")	
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}	
			
	@Column(name = "POSTCODE")	
	public String getPostcode() {
		return postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}	
			
	@Column(name = "SAFEPOSITION")	
	public String getSafeposition() {
		return safeposition;
	}

	public void setSafeposition(String safeposition) {
		this.safeposition = safeposition;
	}	
			
	@Column(name = "SAFEPHONE")	
	public String getSafephone() {
		return safephone;
	}

	public void setSafephone(String safephone) {
		this.safephone = safephone;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ECONOMY_ID")			
	public TsSimpleCode getFkByEconomyId() {
		return fkByEconomyId;
	}

	public void setFkByEconomyId(TsSimpleCode fkByEconomyId) {
		this.fkByEconomyId = fkByEconomyId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "INDUS_TYPE_ID")			
	public TsSimpleCode getFkByIndusTypeId() {
		return fkByIndusTypeId;
	}

	public void setFkByIndusTypeId(TsSimpleCode fkByIndusTypeId) {
		this.fkByIndusTypeId = fkByIndusTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_SIZE_ID")			
	public TsSimpleCode getFkByCrptSizeId() {
		return fkByCrptSizeId;
	}

	public void setFkByCrptSizeId(TsSimpleCode fkByCrptSizeId) {
		this.fkByCrptSizeId = fkByCrptSizeId;
	}	
			
	@Column(name = "SEX")	
	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "BIRTHDAY")			
	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}	
			
	@Column(name = "YSZYB_NAME")	
	public String getYszybName() {
		return yszybName;
	}

	public void setYszybName(String yszybName) {
		this.yszybName = yszybName;
	}	
			
	@Column(name = "TCH_BADRSNS")	
	public String getTchBadrsns() {
		return tchBadrsns;
	}

	public void setTchBadrsns(String tchBadrsns) {
		this.tchBadrsns = tchBadrsns;
	}	
			
	@Column(name = "ANALY_WORK")	
	public String getAnalyWork() {
		return analyWork;
	}

	public void setAnalyWork(String analyWork) {
		this.analyWork = analyWork;
	}	
			
	@Column(name = "TCH_WORK_YEAR")	
	public Integer getTchWorkYear() {
		return tchWorkYear;
	}

	public void setTchWorkYear(Integer tchWorkYear) {
		this.tchWorkYear = tchWorkYear;
	}	
			
	@Column(name = "TCH_WORK_MONTH")	
	public Integer getTchWorkMonth() {
		return tchWorkMonth;
	}

	public void setTchWorkMonth(Integer tchWorkMonth) {
		this.tchWorkMonth = tchWorkMonth;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "FIND_DATE")			
	public Date getFindDate() {
		return findDate;
	}

	public void setFindDate(Date findDate) {
		this.findDate = findDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "SOURCE_ID")			
	public TsSimpleCode getFkBySourceId() {
		return fkBySourceId;
	}

	public void setFkBySourceId(TsSimpleCode fkBySourceId) {
		this.fkBySourceId = fkBySourceId;
	}	
			
	@Column(name = "OTHER_SOURCE")	
	public String getOtherSource() {
		return otherSource;
	}

	public void setOtherSource(String otherSource) {
		this.otherSource = otherSource;
	}	
			
	@ManyToOne
	@JoinColumn(name = "FILL_UNIT_ID")			
	public TdZwTjorginfoComm getFkByFillUnitId() {
		return fkByFillUnitId;
	}

	public void setFkByFillUnitId(TdZwTjorginfoComm fkByFillUnitId) {
		this.fkByFillUnitId = fkByFillUnitId;
	}	
			
	@Column(name = "UNIT_RESP_PSN")	
	public String getUnitRespPsn() {
		return unitRespPsn;
	}

	public void setUnitRespPsn(String unitRespPsn) {
		this.unitRespPsn = unitRespPsn;
	}	
			
	@Column(name = "FILL_FORM_PSN")	
	public String getFillFormPsn() {
		return fillFormPsn;
	}

	public void setFillFormPsn(String fillFormPsn) {
		this.fillFormPsn = fillFormPsn;
	}	
			
	@Column(name = "FILL_LINK")	
	public String getFillLink() {
		return fillLink;
	}

	public void setFillLink(String fillLink) {
		this.fillLink = fillLink;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "FILL_DATE")			
	public Date getFillDate() {
		return fillDate;
	}

	public void setFillDate(Date fillDate) {
		this.fillDate = fillDate;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Column(name = "DEL_MARK")	
	public Integer getDelMark() {
		return delMark;
	}

	public void setDelMark(Integer delMark) {
		this.delMark = delMark;
	}	
			
	@Column(name = "REL_BHK_ID")	
	public Integer getRelBhkId() {
		return relBhkId;
	}

	public void setRelBhkId(Integer relBhkId) {
		this.relBhkId = relBhkId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	
	@Column(name = "ANNEX_PATH")	
	public String getAnnexPath() {
		return annexPath;
	}

	public void setAnnexPath(String annexPath) {
		this.annexPath = annexPath;
	}
	@Column(name = "RPT_NO")	
	public String getRptNo() {
		return rptNo;
	}

	public void setRptNo(String rptNo) {
		this.rptNo = rptNo;
	}	
			
	@Column(name = "TCH_WORK_HOUR")	
	public Integer getTchWorkHour() {
		return tchWorkHour;
	}

	public void setTchWorkHour(Integer tchWorkHour) {
		this.tchWorkHour = tchWorkHour;
	}	
			
	@Column(name = "TCH_WORK_MINUTE")	
	public Integer getTchWorkMinute() {
		return tchWorkMinute;
	}

	public void setTchWorkMinute(Integer tchWorkMinute) {
		this.tchWorkMinute = tchWorkMinute;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CARD_TYPE_ID")			
	public TsSimpleCode getFkByCardTypeId() {
		return fkByCardTypeId;
	}

	public void setFkByCardTypeId(TsSimpleCode fkByCardTypeId) {
		this.fkByCardTypeId = fkByCardTypeId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "HARM_START_DATE")			
	public Date getHarmStartDate() {
		return harmStartDate;
	}

	public void setHarmStartDate(Date harmStartDate) {
		this.harmStartDate = harmStartDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "OCC_DISEID")			
	public TsSimpleCode getFkByOccDiseid() {
		return fkByOccDiseid;
	}

	public void setFkByOccDiseid(TsSimpleCode fkByOccDiseid) {
		this.fkByOccDiseid = fkByOccDiseid;
	}	
			
	@Column(name = "YSZYB_TYPE_NAME")	
	public String getYszybTypeName() {
		return yszybTypeName;
	}

	public void setYszybTypeName(String yszybTypeName) {
		this.yszybTypeName = yszybTypeName;
	}	
			
	@Column(name = "ZY_POISON_TYPE")	
	public Integer getZyPoisonType() {
		return zyPoisonType;
	}

	public void setZyPoisonType(Integer zyPoisonType) {
		this.zyPoisonType = zyPoisonType;
	}	
			
	@ManyToOne
	@JoinColumn(name = "WORK_TYPE_ID")			
	public TsSimpleCode getFkByWorkTypeId() {
		return fkByWorkTypeId;
	}

	public void setFkByWorkTypeId(TsSimpleCode fkByWorkTypeId) {
		this.fkByWorkTypeId = fkByWorkTypeId;
	}	
			
	@Column(name = "WORK_OTHER")	
	public String getWorkOther() {
		return workOther;
	}

	public void setWorkOther(String workOther) {
		this.workOther = workOther;
	}	
			
	@Column(name = "DISCOVERY_UNIT")	
	public String getDiscoveryUnit() {
		return discoveryUnit;
	}

	public void setDiscoveryUnit(String discoveryUnit) {
		this.discoveryUnit = discoveryUnit;
	}	
			
	@Column(name = "DISCOVERY_RESP_PSN")	
	public String getDiscoveryRespPsn() {
		return discoveryRespPsn;
	}

	public void setDiscoveryRespPsn(String discoveryRespPsn) {
		this.discoveryRespPsn = discoveryRespPsn;
	}	
			
	@Column(name = "RPT_PSN")	
	public String getRptPsn() {
		return rptPsn;
	}

	public void setRptPsn(String rptPsn) {
		this.rptPsn = rptPsn;
	}	
			
	@Column(name = "RPT_LINK")	
	public String getRptLink() {
		return rptLink;
	}

	public void setRptLink(String rptLink) {
		this.rptLink = rptLink;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "RPT_DATE")			
	public Date getRptDate() {
		return rptDate;
	}

	public void setRptDate(Date rptDate) {
		this.rptDate = rptDate;
	}	
			
	@Column(name = "RMK")	
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

	@Column(name = "TCH_WORK_DAY")
	public Integer getTchWorkDay() {
		return tchWorkDay;
	}

	public void setTchWorkDay(Integer tchWorkDay) {
		this.tchWorkDay = tchWorkDay;
	}

	@Transient
	public String getWorkName() {
		return workName;
	}

	public void setWorkName(String workName) {
		this.workName = workName;
	}


    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId",orphanRemoval = true)
    public List<TdZwYszybTchBadrsn> getBadrsnList() {
        return badrsnList;
    }

    public void setBadrsnList(List<TdZwYszybTchBadrsn> badrsnList) {
        this.badrsnList = badrsnList;
    }

	@ManyToOne
	@JoinColumn(name = "RPT_UNIT_ID")
	public TsUnit getFkRptUnitId() {
		return fkRptUnitId;
	}

	public void setFkRptUnitId(TsUnit fkRptUnitId) {
		this.fkRptUnitId = fkRptUnitId;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_CRPT_ID")
	public TbTjCrpt getFkByEmpCrptId() {
		return fkByEmpCrptId;
	}

	public void setFkByEmpCrptId(TbTjCrpt fkByEmpCrptId) {
		this.fkByEmpCrptId = fkByEmpCrptId;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_ZONE_ID")
	public TsZone getFkByEmpZoneId() {
		return fkByEmpZoneId;
	}

	public void setFkByEmpZoneId(TsZone fkByEmpZoneId) {
		this.fkByEmpZoneId = fkByEmpZoneId;
	}

	@Column(name = "EMP_CRPT_NAME")
	public String getEmpCrptName() {
		return empCrptName;
	}

	public void setEmpCrptName(String empCrptName) {
		this.empCrptName = empCrptName;
	}

	@Column(name = "EMP_CREDIT_CODE")
	public String getEmpCreditCode() {
		return empCreditCode;
	}

	public void setEmpCreditCode(String empCreditCode) {
		this.empCreditCode = empCreditCode;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_ECONOMY_ID")
	public TsSimpleCode getFkByEmpEconomyId() {
		return fkByEmpEconomyId;
	}

	public void setFkByEmpEconomyId(TsSimpleCode fkByEmpEconomyId) {
		this.fkByEmpEconomyId = fkByEmpEconomyId;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_INDUS_TYPE_ID")
	public TsSimpleCode getFkByEmpIndusTypeId() {
		return fkByEmpIndusTypeId;
	}

	public void setFkByEmpIndusTypeId(TsSimpleCode fkByEmpIndusTypeId) {
		this.fkByEmpIndusTypeId = fkByEmpIndusTypeId;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_CRPT_SIZE_ID")
	public TsSimpleCode getFkByEmpCrptSizeId() {
		return fkByEmpCrptSizeId;
	}

	public void setFkByEmpCrptSizeId(TsSimpleCode fkByEmpCrptSizeId) {
		this.fkByEmpCrptSizeId = fkByEmpCrptSizeId;
	}
}