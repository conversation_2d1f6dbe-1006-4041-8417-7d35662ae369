package com.chis.modules.heth.comm.rptvo;

import com.chis.modules.system.rptvo.ZwxFieldDoc;
/**
 *
 * <p>描述：职业病鉴定报告卡文书数据封装</p>
 *
 *  @Author: 龚哲,2021/11/8 16:34,ZwjdArchivesCardVo
 */
public class ZwjdArchivesCardVo {
    @ZwxFieldDoc(value = "劳动者姓名")
    private String personnelName;

    @ZwxFieldDoc(value = "劳动者证件类型")
    private String cardTypeName;

    @ZwxFieldDoc(value = "劳动者证件号码")
    private String idc;

    @ZwxFieldDoc(value = "劳动者性别")
    private String sex;

    @ZwxFieldDoc(value = "劳动者出生日期")
    private String birthday;

    @ZwxFieldDoc(value = "劳动者联系电话")
    private String linktel;

    @ZwxFieldDoc(value = "用人单位名称")
    private String crptName;

    @ZwxFieldDoc(value = "用人单位社会信用代码")
    private String creditCode;

    @ZwxFieldDoc(value = "用人单位经济类型")
    private String economyName;

    @ZwxFieldDoc(value = "用人单位行业类别")
    private String indusTypeName;

    @ZwxFieldDoc(value = "用人单位企业规模")
    private String crptSizeName;

    @ZwxFieldDoc(value = "用人单位地区")
    private String zoneName;

    @ZwxFieldDoc(value = "用人单位详细地址")
    private String address;

    @ZwxFieldDoc(value = "用人单位地址邮编")
    private String postcode;

    @ZwxFieldDoc(value = "用人单位联系人")
    private String safeposition;

    @ZwxFieldDoc(value = "用人单位联系电话")
    private String safephone;

    @ZwxFieldDoc(value = "用工单位名称")
    private String empCrptName;

    @ZwxFieldDoc(value = "用工单位地区")
    private String empZoneName;

    @ZwxFieldDoc(value = "用工单位社会信用代码")
    private String empCreditCode;

    @ZwxFieldDoc(value = "用工单位经济类型")
    private String empEconomyName;

    @ZwxFieldDoc(value = "用工单位社会行业类别")
    private String empIndusTypeName;

    @ZwxFieldDoc(value = "用工单位企业规模")
    private String empCrptSizeName;

    @ZwxFieldDoc(value = "是否诊断为职业病")
    private String ifZyb;

    @ZwxFieldDoc(value = "职业病类型")
    private String zybTypeName;

    @ZwxFieldDoc(value = "其他职业病名称")
    private String zybDisName;

    @ZwxFieldDoc(value = "职业病种类")
    private String zybDisTypeName;

    @ZwxFieldDoc(value = "申请诊断日期")
    private String applyDate;

    //是否为尘肺病1是
    @ZwxFieldDoc(value = "诊断病种类型是否为尘肺病")
    private String iszdcfb;

    @ZwxFieldDoc(value = "诊断I期日期（尘肺病）")
    private String diag1Date;

    @ZwxFieldDoc(value = "诊断Ⅱ期日期（尘肺病）")
    private String diag2Date;

    @ZwxFieldDoc(value = "诊断Ⅲ期日期（尘肺病）")
    private String diag3Date;

    @ZwxFieldDoc(value = "病例类型ID（尘肺病）")
    private String rptTypeName;

    @ZwxFieldDoc(value = "职业性化学中毒分类")
    private String zyPoisonType;

    @ZwxFieldDoc(value = "诊断日期")
    private String diagDate;

    @ZwxFieldDoc(value = "诊断单位名称")
    private String diagUnitName;

    @ZwxFieldDoc(value = "申请鉴定日期")
    private String applyJdDate;

    @ZwxFieldDoc(value = "鉴定日期")
    private String aprsCentDate;

    @ZwxFieldDoc(value = "鉴定类型")
    private String jdTypeName;

    @ZwxFieldDoc(value = "首次鉴定结论")
    private String jdRstName;

    @ZwxFieldDoc(value = "再次鉴定结论")
    private String jdAgainRstName;

    @ZwxFieldDoc(value = "鉴定机构名称")
    private String jdUnitName;

    @ZwxFieldDoc(value = "鉴定机构社会信用代码")
    private String jdUnitCreditCode;

    @ZwxFieldDoc(value = "是否鉴定为职业病")
    private String ifJdZyb;

    @ZwxFieldDoc(value = "鉴定结果职业病类型")
    private String jdZybTypeName;

    @ZwxFieldDoc(value = "鉴定结果其他职业病名称")
    private String jdZybDisName;

    @ZwxFieldDoc(value = "鉴定结果职业病种类")
    private String jdZybDisTypeName;
    //是否为尘肺病1是
    @ZwxFieldDoc(value = "鉴定病种类型是否为尘肺病")
    private String isjdcfb;

    @ZwxFieldDoc(value = "鉴定结果诊断I期日期（尘肺病）")
    private String jdDiag1Date;

    @ZwxFieldDoc(value = "鉴定结果诊断Ⅱ期日期（尘肺病）")
    private String jdDiag2Date;

    @ZwxFieldDoc(value = "鉴定结果诊断Ⅲ期日期（尘肺病）")
    private String jdDiag3Date;

    @ZwxFieldDoc(value = "鉴定结果病例类型ID（尘肺病）")
    private String jdRptTypeName;

    @ZwxFieldDoc(value = "鉴定结果职业性化学中毒分类")
    private String jdZyPoisonType;

    @ZwxFieldDoc(value = "填表单位")
    private String fillUnitName;

    @ZwxFieldDoc(value = "填表人")
    private String fillFormPsn;

    @ZwxFieldDoc(value = "填表人联系电话")
    private String fillLink;

    @ZwxFieldDoc(value = "填表日期")
    private String fillDate;

    @ZwxFieldDoc(value = "报告单位")
    private String rptUnitName;

    @ZwxFieldDoc(value = "报告人")
    private String rptPsn;

    @ZwxFieldDoc(value = "报告人联系电话")
    private String rptLink;

    @ZwxFieldDoc(value = "报告日期")
    private String rptDate;

    @ZwxFieldDoc(value = "备注")
    private String rmk;

    public String getPersonnelName() {
        return personnelName;
    }

    public void setPersonnelName(String personnelName) {
        this.personnelName = personnelName;
    }

    public String getCardTypeName() {
        return cardTypeName;
    }

    public void setCardTypeName(String cardTypeName) {
        this.cardTypeName = cardTypeName;
    }

    public String getIdc() {
        return idc;
    }

    public void setIdc(String idc) {
        this.idc = idc;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getLinktel() {
        return linktel;
    }

    public void setLinktel(String linktel) {
        this.linktel = linktel;
    }

    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getEconomyName() {
        return economyName;
    }

    public void setEconomyName(String economyName) {
        this.economyName = economyName;
    }

    public String getIndusTypeName() {
        return indusTypeName;
    }

    public void setIndusTypeName(String indusTypeName) {
        this.indusTypeName = indusTypeName;
    }

    public String getCrptSizeName() {
        return crptSizeName;
    }

    public void setCrptSizeName(String crptSizeName) {
        this.crptSizeName = crptSizeName;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getSafeposition() {
        return safeposition;
    }

    public void setSafeposition(String safeposition) {
        this.safeposition = safeposition;
    }

    public String getSafephone() {
        return safephone;
    }

    public void setSafephone(String safephone) {
        this.safephone = safephone;
    }

    public String getEmpCrptName() {
        return empCrptName;
    }

    public void setEmpCrptName(String empCrptName) {
        this.empCrptName = empCrptName;
    }

    public String getEmpZoneName() {
        return empZoneName;
    }

    public void setEmpZoneName(String empZoneName) {
        this.empZoneName = empZoneName;
    }

    public String getEmpCreditCode() {
        return empCreditCode;
    }

    public void setEmpCreditCode(String empCreditCode) {
        this.empCreditCode = empCreditCode;
    }

    public String getEmpEconomyName() {
        return empEconomyName;
    }

    public void setEmpEconomyName(String empEconomyName) {
        this.empEconomyName = empEconomyName;
    }

    public String getEmpIndusTypeName() {
        return empIndusTypeName;
    }

    public void setEmpIndusTypeName(String empIndusTypeName) {
        this.empIndusTypeName = empIndusTypeName;
    }

    public String getEmpCrptSizeName() {
        return empCrptSizeName;
    }

    public void setEmpCrptSizeName(String empCrptSizeName) {
        this.empCrptSizeName = empCrptSizeName;
    }

    public String getIfZyb() {
        return ifZyb;
    }

    public void setIfZyb(String ifZyb) {
        this.ifZyb = ifZyb;
    }

    public String getZybTypeName() {
        return zybTypeName;
    }

    public void setZybTypeName(String zybTypeName) {
        this.zybTypeName = zybTypeName;
    }

    public String getZybDisName() {
        return zybDisName;
    }

    public void setZybDisName(String zybDisName) {
        this.zybDisName = zybDisName;
    }

    public String getZybDisTypeName() {
        return zybDisTypeName;
    }

    public void setZybDisTypeName(String zybDisTypeName) {
        this.zybDisTypeName = zybDisTypeName;
    }

    public String getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(String applyDate) {
        this.applyDate = applyDate;
    }

    public String getDiag1Date() {
        return diag1Date;
    }

    public void setDiag1Date(String diag1Date) {
        this.diag1Date = diag1Date;
    }

    public String getDiag2Date() {
        return diag2Date;
    }

    public void setDiag2Date(String diag2Date) {
        this.diag2Date = diag2Date;
    }

    public String getDiag3Date() {
        return diag3Date;
    }

    public void setDiag3Date(String diag3Date) {
        this.diag3Date = diag3Date;
    }

    public String getRptTypeName() {
        return rptTypeName;
    }

    public void setRptTypeName(String rptTypeName) {
        this.rptTypeName = rptTypeName;
    }

    public String getZyPoisonType() {
        return zyPoisonType;
    }

    public void setZyPoisonType(String zyPoisonType) {
        this.zyPoisonType = zyPoisonType;
    }

    public String getDiagDate() {
        return diagDate;
    }

    public void setDiagDate(String diagDate) {
        this.diagDate = diagDate;
    }

    public String getDiagUnitName() {
        return diagUnitName;
    }

    public void setDiagUnitName(String diagUnitName) {
        this.diagUnitName = diagUnitName;
    }

    public String getApplyJdDate() {
        return applyJdDate;
    }

    public void setApplyJdDate(String applyJdDate) {
        this.applyJdDate = applyJdDate;
    }

    public String getAprsCentDate() {
        return aprsCentDate;
    }

    public void setAprsCentDate(String aprsCentDate) {
        this.aprsCentDate = aprsCentDate;
    }

    public String getJdTypeName() {
        return jdTypeName;
    }

    public void setJdTypeName(String jdTypeName) {
        this.jdTypeName = jdTypeName;
    }

    public String getJdRstName() {
        return jdRstName;
    }

    public void setJdRstName(String jdRstName) {
        this.jdRstName = jdRstName;
    }

    public String getJdAgainRstName() {
        return jdAgainRstName;
    }

    public void setJdAgainRstName(String jdAgainRstName) {
        this.jdAgainRstName = jdAgainRstName;
    }

    public String getJdUnitName() {
        return jdUnitName;
    }

    public void setJdUnitName(String jdUnitName) {
        this.jdUnitName = jdUnitName;
    }

    public String getJdUnitCreditCode() {
        return jdUnitCreditCode;
    }

    public void setJdUnitCreditCode(String jdUnitCreditCode) {
        this.jdUnitCreditCode = jdUnitCreditCode;
    }

    public String getIfJdZyb() {
        return ifJdZyb;
    }

    public void setIfJdZyb(String ifJdZyb) {
        this.ifJdZyb = ifJdZyb;
    }

    public String getJdZybTypeName() {
        return jdZybTypeName;
    }

    public void setJdZybTypeName(String jdZybTypeName) {
        this.jdZybTypeName = jdZybTypeName;
    }

    public String getJdZybDisName() {
        return jdZybDisName;
    }

    public void setJdZybDisName(String jdZybDisName) {
        this.jdZybDisName = jdZybDisName;
    }

    public String getJdZybDisTypeName() {
        return jdZybDisTypeName;
    }

    public void setJdZybDisTypeName(String jdZybDisTypeName) {
        this.jdZybDisTypeName = jdZybDisTypeName;
    }

    public String getJdDiag1Date() {
        return jdDiag1Date;
    }

    public void setJdDiag1Date(String jdDiag1Date) {
        this.jdDiag1Date = jdDiag1Date;
    }

    public String getJdDiag2Date() {
        return jdDiag2Date;
    }

    public void setJdDiag2Date(String jdDiag2Date) {
        this.jdDiag2Date = jdDiag2Date;
    }

    public String getJdDiag3Date() {
        return jdDiag3Date;
    }

    public void setJdDiag3Date(String jdDiag3Date) {
        this.jdDiag3Date = jdDiag3Date;
    }

    public String getJdRptTypeName() {
        return jdRptTypeName;
    }

    public void setJdRptTypeName(String jdRptTypeName) {
        this.jdRptTypeName = jdRptTypeName;
    }

    public String getJdZyPoisonType() {
        return jdZyPoisonType;
    }

    public void setJdZyPoisonType(String jdZyPoisonType) {
        this.jdZyPoisonType = jdZyPoisonType;
    }

    public String getFillUnitName() {
        return fillUnitName;
    }

    public void setFillUnitName(String fillUnitName) {
        this.fillUnitName = fillUnitName;
    }

    public String getFillFormPsn() {
        return fillFormPsn;
    }

    public void setFillFormPsn(String fillFormPsn) {
        this.fillFormPsn = fillFormPsn;
    }

    public String getFillLink() {
        return fillLink;
    }

    public void setFillLink(String fillLink) {
        this.fillLink = fillLink;
    }

    public String getFillDate() {
        return fillDate;
    }

    public void setFillDate(String fillDate) {
        this.fillDate = fillDate;
    }

    public String getRptUnitName() {
        return rptUnitName;
    }

    public void setRptUnitName(String rptUnitName) {
        this.rptUnitName = rptUnitName;
    }

    public String getRptPsn() {
        return rptPsn;
    }

    public void setRptPsn(String rptPsn) {
        this.rptPsn = rptPsn;
    }

    public String getRptLink() {
        return rptLink;
    }

    public void setRptLink(String rptLink) {
        this.rptLink = rptLink;
    }

    public String getRptDate() {
        return rptDate;
    }

    public void setRptDate(String rptDate) {
        this.rptDate = rptDate;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getIszdcfb() {
        return iszdcfb;
    }

    public void setIszdcfb(String iszdcfb) {
        this.iszdcfb = iszdcfb;
    }

    public String getIsjdcfb() {
        return isjdcfb;
    }

    public void setIsjdcfb(String isjdcfb) {
        this.isjdcfb = isjdcfb;
    }
}
