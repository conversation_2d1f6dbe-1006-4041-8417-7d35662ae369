package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-10-29
 */
@Entity
@Table(name = "TD_ZWYJ_BSN_CSION_NO")
@SequenceGenerator(name = "TdZwyjBsnCsionNo", sequenceName = "TD_ZWYJ_BSN_CSION_NO_SEQ", allocationSize = 1)
public class TdZwyjBsnCsionNo implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String noDesc;
	private TsSimpleCode fkByOnguardStateid;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjBsnCsionNo() {
	}

	public TdZwyjBsnCsionNo(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjBsnCsionNo")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "NO_DESC")	
	public String getNoDesc() {
		return noDesc;
	}

	public void setNoDesc(String noDesc) {
		this.noDesc = noDesc;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ONGUARD_STATEID")			
	public TsSimpleCode getFkByOnguardStateid() {
		return fkByOnguardStateid;
	}

	public void setFkByOnguardStateid(TsSimpleCode fkByOnguardStateid) {
		this.fkByOnguardStateid = fkByOnguardStateid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}