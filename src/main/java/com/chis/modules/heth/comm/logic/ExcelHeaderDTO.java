package com.chis.modules.heth.comm.logic;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>类描述：Excel导入 表头信息 </p>
 *
 * @ClassAuthor: yzz
 * @date： 2022年09月09日
 **/
public class ExcelHeaderDTO {

    /**需要处理单个下标的表头*/
    private String[] singleIndexHeader=new String[]{};
    /**需要处理 种类及接触人数 下标的表头*/
    private String[] factorcrowdIndexHeader=new String[]{};
    /**需要处理 检测情况 下标的表头*/
    private String[] chkIndexHeader=new String[]{};
    /**需要处理 检查情况 下标的表头*/
    private String[] cusIndexHeader=new String[]{};
    /**需要对照的表头 检查情况 */
    private String[] contrastHeader=new String[]{};

    /**表头对应的详细信息 包含下标*/
    private Map<String,ExcelHeaderDetailDTO> detailMap=new HashMap<>();
    /**key 下标 value 表头*/
    private Map<Integer,String> hearderIndexMap=new HashMap<>();


    public String[] getSingleIndexHeader() {
        return singleIndexHeader;
    }

    public void setSingleIndexHeader(String[] singleIndexHeader) {
        this.singleIndexHeader = singleIndexHeader;
    }

    public String[] getFactorcrowdIndexHeader() {
        return factorcrowdIndexHeader;
    }

    public void setFactorcrowdIndexHeader(String[] factorcrowdIndexHeader) {
        this.factorcrowdIndexHeader = factorcrowdIndexHeader;
    }

    public String[] getChkIndexHeader() {
        return chkIndexHeader;
    }

    public void setChkIndexHeader(String[] chkIndexHeader) {
        this.chkIndexHeader = chkIndexHeader;
    }

    public String[] getCusIndexHeader() {
        return cusIndexHeader;
    }

    public void setCusIndexHeader(String[] cusIndexHeader) {
        this.cusIndexHeader = cusIndexHeader;
    }

    public Map<String, ExcelHeaderDetailDTO> getDetailMap() {
        return detailMap;
    }

    public void setDetailMap(Map<String, ExcelHeaderDetailDTO> detailMap) {
        this.detailMap = detailMap;
    }

    public String[] getContrastHeader() {
        return contrastHeader;
    }

    public void setContrastHeader(String[] contrastHeader) {
        this.contrastHeader = contrastHeader;
    }

    public Map<Integer,String> getHearderIndexMap() {
        return hearderIndexMap;
    }

    public void setHearderIndexMap(Map<Integer,String> hearderIndexMap) {
        this.hearderIndexMap = hearderIndexMap;
    }
}
