package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-11-8
 */
@Entity
@Table(name = "TD_ZWJD_ARCHIVES_CARD")
@SequenceGenerator(name = "TdZwjdArchivesCard", sequenceName = "TD_ZWJD_ARCHIVES_CARD_SEQ", allocationSize = 1)
public class TdZwjdArchivesCard implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwjdArchivesComm fkByMainId;
	private String rptNo;
	private String personnelName;
	/**码表：5503*/
	private TsSimpleCode fkByCardTypeId;
	private String idc;
	/**1：男2：女*/
	private Integer sex;
	private Date birthday;
	private String linktel;
	private TbTjCrpt fkByCrptId;
	private String crptName;
	private String creditCode;
	/**码表：5003*/
	private TsSimpleCode fkByEconomyId;
	/**码表：5002*/
	private TsSimpleCode fkByIndusTypeId;
	/**码表：5004*/
	private TsSimpleCode fkByCrptSizeId;
	private TsZone fkByZoneId;
	private String address;
	private String postcode;
	private String safeposition;
	private String safephone;
	private TbTjCrpt fkByEmpCrptId;
	private TsZone fkByEmpZoneId;
	private String empCrptName;
	private String empCreditCode;
	/**码表：5003*/
	private TsSimpleCode fkByEmpEconomyId;
	/**码表：5002*/
	private TsSimpleCode fkByEmpIndusTypeId;
	/**码表：5004*/
	private TsSimpleCode fkByEmpCrptSizeId;
	/**0：否1：是*/
	private Integer ifZyb;
	/**码表5026*/
	private TsSimpleCode fkByZybTypeId;
	private String zybDisName;
	/**码表5026（对应病种ID上一级）*/
	private TsSimpleCode fkByZybDisTypeId;
	private Date applyDate;
	private Date diag1Date;
	private Date diag2Date;
	private Date diag3Date;
	/**码表：5505*/
	private TsSimpleCode fkByRptTypeId;
	/**1：急性2：慢性*/
	private Integer zyPoisonType;
	private Date diagDate;
	private TsUnit fkByDiagUnitId;
	private String diagUnitName;
	private Date applyJdDate;
	private Date aprsCentDate;
	/**码表5543*/
	private TsSimpleCode fkByJdTypeId;
	/**码表5544*/
	private TsSimpleCode fkByJdRstId;
	/**码表5545*/
	private TsSimpleCode fkByJdAgainRstId;
	private TsUnit fkByJdUnitId;
	private String jdUnitName;
	private String jdUnitCreditCode;
	/**0：否1：是*/
	private Integer ifJdZyb;
	/**码表5026*/
	private TsSimpleCode fkByJdZybTypeId;
	private String jdZybDisName;
	/**码表5026（对应病种ID上一级）*/
	private TsSimpleCode fkByJdZybDisTypeId;
	private Date jdDiag1Date;
	private Date jdDiag2Date;
	private Date jdDiag3Date;
	/**码表：5505*/
	private TsSimpleCode fkByJdRptTypeId;
	/**1：急性2：慢性*/
	private Integer jdZyPoisonType;
	private String fillFormPsn;
	private String fillLink;
	private Date fillDate;
	private TsUnit fkByFillUnitId;
	private String fillUnitName;
	private String rptPsn;
	private String rptLink;
	private Date rptDate;
	private String rptUnitName;
	private String rmk;
	/**0：否1：是*/
	private Integer delMark;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private String annexPath;
	
	public TdZwjdArchivesCard() {
	}

	public TdZwjdArchivesCard(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwjdArchivesCard")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "RPT_NO")	
	public String getRptNo() {
		return rptNo;
	}

	public void setRptNo(String rptNo) {
		this.rptNo = rptNo;
	}	
			
	@Column(name = "PERSONNEL_NAME")	
	public String getPersonnelName() {
		return personnelName;
	}

	public void setPersonnelName(String personnelName) {
		this.personnelName = personnelName;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CARD_TYPE_ID")			
	public TsSimpleCode getFkByCardTypeId() {
		return fkByCardTypeId;
	}

	public void setFkByCardTypeId(TsSimpleCode fkByCardTypeId) {
		this.fkByCardTypeId = fkByCardTypeId;
	}	
			
	@Column(name = "IDC")	
	public String getIdc() {
		return idc;
	}

	public void setIdc(String idc) {
		this.idc = idc;
	}	
			
	@Column(name = "SEX")	
	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "BIRTHDAY")			
	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}	
			
	@Column(name = "LINKTEL")	
	public String getLinktel() {
		return linktel;
	}

	public void setLinktel(String linktel) {
		this.linktel = linktel;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_ID")			
	public TbTjCrpt getFkByCrptId() {
		return fkByCrptId;
	}

	public void setFkByCrptId(TbTjCrpt fkByCrptId) {
		this.fkByCrptId = fkByCrptId;
	}	
			
	@Column(name = "CRPT_NAME")	
	public String getCrptName() {
		return crptName;
	}

	public void setCrptName(String crptName) {
		this.crptName = crptName;
	}	
			
	@Column(name = "CREDIT_CODE")	
	public String getCreditCode() {
		return creditCode;
	}

	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ECONOMY_ID")			
	public TsSimpleCode getFkByEconomyId() {
		return fkByEconomyId;
	}

	public void setFkByEconomyId(TsSimpleCode fkByEconomyId) {
		this.fkByEconomyId = fkByEconomyId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "INDUS_TYPE_ID")			
	public TsSimpleCode getFkByIndusTypeId() {
		return fkByIndusTypeId;
	}

	public void setFkByIndusTypeId(TsSimpleCode fkByIndusTypeId) {
		this.fkByIndusTypeId = fkByIndusTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_SIZE_ID")			
	public TsSimpleCode getFkByCrptSizeId() {
		return fkByCrptSizeId;
	}

	public void setFkByCrptSizeId(TsSimpleCode fkByCrptSizeId) {
		this.fkByCrptSizeId = fkByCrptSizeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ZONE_ID")			
	public TsZone getFkByZoneId() {
		return fkByZoneId;
	}

	public void setFkByZoneId(TsZone fkByZoneId) {
		this.fkByZoneId = fkByZoneId;
	}	
			
	@Column(name = "ADDRESS")	
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}	
			
	@Column(name = "POSTCODE")	
	public String getPostcode() {
		return postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}	
			
	@Column(name = "SAFEPOSITION")	
	public String getSafeposition() {
		return safeposition;
	}

	public void setSafeposition(String safeposition) {
		this.safeposition = safeposition;
	}	
			
	@Column(name = "SAFEPHONE")	
	public String getSafephone() {
		return safephone;
	}

	public void setSafephone(String safephone) {
		this.safephone = safephone;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EMP_CRPT_ID")			
	public TbTjCrpt getFkByEmpCrptId() {
		return fkByEmpCrptId;
	}

	public void setFkByEmpCrptId(TbTjCrpt fkByEmpCrptId) {
		this.fkByEmpCrptId = fkByEmpCrptId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EMP_ZONE_ID")			
	public TsZone getFkByEmpZoneId() {
		return fkByEmpZoneId;
	}

	public void setFkByEmpZoneId(TsZone fkByEmpZoneId) {
		this.fkByEmpZoneId = fkByEmpZoneId;
	}	
			
	@Column(name = "EMP_CRPT_NAME")	
	public String getEmpCrptName() {
		return empCrptName;
	}

	public void setEmpCrptName(String empCrptName) {
		this.empCrptName = empCrptName;
	}	
			
	@Column(name = "EMP_CREDIT_CODE")	
	public String getEmpCreditCode() {
		return empCreditCode;
	}

	public void setEmpCreditCode(String empCreditCode) {
		this.empCreditCode = empCreditCode;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EMP_ECONOMY_ID")			
	public TsSimpleCode getFkByEmpEconomyId() {
		return fkByEmpEconomyId;
	}

	public void setFkByEmpEconomyId(TsSimpleCode fkByEmpEconomyId) {
		this.fkByEmpEconomyId = fkByEmpEconomyId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EMP_INDUS_TYPE_ID")			
	public TsSimpleCode getFkByEmpIndusTypeId() {
		return fkByEmpIndusTypeId;
	}

	public void setFkByEmpIndusTypeId(TsSimpleCode fkByEmpIndusTypeId) {
		this.fkByEmpIndusTypeId = fkByEmpIndusTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EMP_CRPT_SIZE_ID")			
	public TsSimpleCode getFkByEmpCrptSizeId() {
		return fkByEmpCrptSizeId;
	}

	public void setFkByEmpCrptSizeId(TsSimpleCode fkByEmpCrptSizeId) {
		this.fkByEmpCrptSizeId = fkByEmpCrptSizeId;
	}	
			
	@Column(name = "IF_ZYB")	
	public Integer getIfZyb() {
		return ifZyb;
	}

	public void setIfZyb(Integer ifZyb) {
		this.ifZyb = ifZyb;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ZYB_TYPE_ID")			
	public TsSimpleCode getFkByZybTypeId() {
		return fkByZybTypeId;
	}

	public void setFkByZybTypeId(TsSimpleCode fkByZybTypeId) {
		this.fkByZybTypeId = fkByZybTypeId;
	}	
			
	@Column(name = "ZYB_DIS_NAME")	
	public String getZybDisName() {
		return zybDisName;
	}

	public void setZybDisName(String zybDisName) {
		this.zybDisName = zybDisName;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ZYB_DIS_TYPE_ID")			
	public TsSimpleCode getFkByZybDisTypeId() {
		return fkByZybDisTypeId;
	}

	public void setFkByZybDisTypeId(TsSimpleCode fkByZybDisTypeId) {
		this.fkByZybDisTypeId = fkByZybDisTypeId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "APPLY_DATE")			
	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DIAG_1_DATE")			
	public Date getDiag1Date() {
		return diag1Date;
	}

	public void setDiag1Date(Date diag1Date) {
		this.diag1Date = diag1Date;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DIAG_2_DATE")			
	public Date getDiag2Date() {
		return diag2Date;
	}

	public void setDiag2Date(Date diag2Date) {
		this.diag2Date = diag2Date;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DIAG_3_DATE")			
	public Date getDiag3Date() {
		return diag3Date;
	}

	public void setDiag3Date(Date diag3Date) {
		this.diag3Date = diag3Date;
	}	
			
	@ManyToOne
	@JoinColumn(name = "RPT_TYPE_ID")			
	public TsSimpleCode getFkByRptTypeId() {
		return fkByRptTypeId;
	}

	public void setFkByRptTypeId(TsSimpleCode fkByRptTypeId) {
		this.fkByRptTypeId = fkByRptTypeId;
	}	
			
	@Column(name = "ZY_POISON_TYPE")	
	public Integer getZyPoisonType() {
		return zyPoisonType;
	}

	public void setZyPoisonType(Integer zyPoisonType) {
		this.zyPoisonType = zyPoisonType;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DIAG_DATE")			
	public Date getDiagDate() {
		return diagDate;
	}

	public void setDiagDate(Date diagDate) {
		this.diagDate = diagDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "DIAG_UNIT_ID")			
	public TsUnit getFkByDiagUnitId() {
		return fkByDiagUnitId;
	}

	public void setFkByDiagUnitId(TsUnit fkByDiagUnitId) {
		this.fkByDiagUnitId = fkByDiagUnitId;
	}	
			
	@Column(name = "DIAG_UNIT_NAME")	
	public String getDiagUnitName() {
		return diagUnitName;
	}

	public void setDiagUnitName(String diagUnitName) {
		this.diagUnitName = diagUnitName;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "APPLY_JD_DATE")			
	public Date getApplyJdDate() {
		return applyJdDate;
	}

	public void setApplyJdDate(Date applyJdDate) {
		this.applyJdDate = applyJdDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "APRS_CENT_DATE")			
	public Date getAprsCentDate() {
		return aprsCentDate;
	}

	public void setAprsCentDate(Date aprsCentDate) {
		this.aprsCentDate = aprsCentDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "JD_TYPE_ID")			
	public TsSimpleCode getFkByJdTypeId() {
		return fkByJdTypeId;
	}

	public void setFkByJdTypeId(TsSimpleCode fkByJdTypeId) {
		this.fkByJdTypeId = fkByJdTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "JD_RST_ID")			
	public TsSimpleCode getFkByJdRstId() {
		return fkByJdRstId;
	}

	public void setFkByJdRstId(TsSimpleCode fkByJdRstId) {
		this.fkByJdRstId = fkByJdRstId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "JD_AGAIN_RST_ID")			
	public TsSimpleCode getFkByJdAgainRstId() {
		return fkByJdAgainRstId;
	}

	public void setFkByJdAgainRstId(TsSimpleCode fkByJdAgainRstId) {
		this.fkByJdAgainRstId = fkByJdAgainRstId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "JD_UNIT_ID")			
	public TsUnit getFkByJdUnitId() {
		return fkByJdUnitId;
	}

	public void setFkByJdUnitId(TsUnit fkByJdUnitId) {
		this.fkByJdUnitId = fkByJdUnitId;
	}	
			
	@Column(name = "JD_UNIT_NAME")	
	public String getJdUnitName() {
		return jdUnitName;
	}

	public void setJdUnitName(String jdUnitName) {
		this.jdUnitName = jdUnitName;
	}	
			
	@Column(name = "JD_UNIT_CREDIT_CODE")	
	public String getJdUnitCreditCode() {
		return jdUnitCreditCode;
	}

	public void setJdUnitCreditCode(String jdUnitCreditCode) {
		this.jdUnitCreditCode = jdUnitCreditCode;
	}	
			
	@Column(name = "IF_JD_ZYB")	
	public Integer getIfJdZyb() {
		return ifJdZyb;
	}

	public void setIfJdZyb(Integer ifJdZyb) {
		this.ifJdZyb = ifJdZyb;
	}	
			
	@ManyToOne
	@JoinColumn(name = "JD_ZYB_TYPE_ID")			
	public TsSimpleCode getFkByJdZybTypeId() {
		return fkByJdZybTypeId;
	}

	public void setFkByJdZybTypeId(TsSimpleCode fkByJdZybTypeId) {
		this.fkByJdZybTypeId = fkByJdZybTypeId;
	}	
			
	@Column(name = "JD_ZYB_DIS_NAME")	
	public String getJdZybDisName() {
		return jdZybDisName;
	}

	public void setJdZybDisName(String jdZybDisName) {
		this.jdZybDisName = jdZybDisName;
	}	
			
	@ManyToOne
	@JoinColumn(name = "JD_ZYB_DIS_TYPE_ID")			
	public TsSimpleCode getFkByJdZybDisTypeId() {
		return fkByJdZybDisTypeId;
	}

	public void setFkByJdZybDisTypeId(TsSimpleCode fkByJdZybDisTypeId) {
		this.fkByJdZybDisTypeId = fkByJdZybDisTypeId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "JD_DIAG_1_DATE")			
	public Date getJdDiag1Date() {
		return jdDiag1Date;
	}

	public void setJdDiag1Date(Date jdDiag1Date) {
		this.jdDiag1Date = jdDiag1Date;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "JD_DIAG_2_DATE")			
	public Date getJdDiag2Date() {
		return jdDiag2Date;
	}

	public void setJdDiag2Date(Date jdDiag2Date) {
		this.jdDiag2Date = jdDiag2Date;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "JD_DIAG_3_DATE")			
	public Date getJdDiag3Date() {
		return jdDiag3Date;
	}

	public void setJdDiag3Date(Date jdDiag3Date) {
		this.jdDiag3Date = jdDiag3Date;
	}	
			
	@ManyToOne
	@JoinColumn(name = "JD_RPT_TYPE_ID")			
	public TsSimpleCode getFkByJdRptTypeId() {
		return fkByJdRptTypeId;
	}

	public void setFkByJdRptTypeId(TsSimpleCode fkByJdRptTypeId) {
		this.fkByJdRptTypeId = fkByJdRptTypeId;
	}	
			
	@Column(name = "JD_ZY_POISON_TYPE")	
	public Integer getJdZyPoisonType() {
		return jdZyPoisonType;
	}

	public void setJdZyPoisonType(Integer jdZyPoisonType) {
		this.jdZyPoisonType = jdZyPoisonType;
	}	
			
	@Column(name = "FILL_FORM_PSN")	
	public String getFillFormPsn() {
		return fillFormPsn;
	}

	public void setFillFormPsn(String fillFormPsn) {
		this.fillFormPsn = fillFormPsn;
	}	
			
	@Column(name = "FILL_LINK")	
	public String getFillLink() {
		return fillLink;
	}

	public void setFillLink(String fillLink) {
		this.fillLink = fillLink;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "FILL_DATE")			
	public Date getFillDate() {
		return fillDate;
	}

	public void setFillDate(Date fillDate) {
		this.fillDate = fillDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "FILL_UNIT_ID")			
	public TsUnit getFkByFillUnitId() {
		return fkByFillUnitId;
	}

	public void setFkByFillUnitId(TsUnit fkByFillUnitId) {
		this.fkByFillUnitId = fkByFillUnitId;
	}	
			
	@Column(name = "FILL_UNIT_NAME")	
	public String getFillUnitName() {
		return fillUnitName;
	}

	public void setFillUnitName(String fillUnitName) {
		this.fillUnitName = fillUnitName;
	}	
			
	@Column(name = "RPT_PSN")	
	public String getRptPsn() {
		return rptPsn;
	}

	public void setRptPsn(String rptPsn) {
		this.rptPsn = rptPsn;
	}	
			
	@Column(name = "RPT_LINK")	
	public String getRptLink() {
		return rptLink;
	}

	public void setRptLink(String rptLink) {
		this.rptLink = rptLink;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "RPT_DATE")			
	public Date getRptDate() {
		return rptDate;
	}

	public void setRptDate(Date rptDate) {
		this.rptDate = rptDate;
	}	
			
	@Column(name = "RPT_UNIT_NAME")	
	public String getRptUnitName() {
		return rptUnitName;
	}

	public void setRptUnitName(String rptUnitName) {
		this.rptUnitName = rptUnitName;
	}	
			
	@Column(name = "RMK")	
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}	
			
	@Column(name = "DEL_MARK")	
	public Integer getDelMark() {
		return delMark;
	}

	public void setDelMark(Integer delMark) {
		this.delMark = delMark;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	@Column(name = "ANNEX_PATH")
	public String getAnnexPath() {
		return annexPath;
	}

	public void setAnnexPath(String annexPath) {
		this.annexPath = annexPath;
	}
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")
	public TdZwjdArchivesComm getFkByMainId() {
		return fkByMainId;
	}
	public void setFkByMainId(TdZwjdArchivesComm fkByMainId) {
		this.fkByMainId = fkByMainId;
	}
}