package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;

/**
* @Description : 企业接口
* @MethodAuthor: anjing
* @Date : 2021/3/31 17:52
**/
public interface IzwCrptInfo {

    public TsZone getFkByZoneId();
    public void setFkByZoneId(TsZone fkByZoneId);

    public String getCrptName();
    public void setCrptName(String crptName);

    public String getAddress();
    public void setAddress(String address);

    public TsSimpleCode getFkByIndusTypeId();
    public void setFkByIndusTypeId(TsSimpleCode fkByIndusTypeId);

    public TsSimpleCode getFkByEconomyId();
    public void setFkByEconomyId(TsSimpleCode fkByEconomyId);

    public TsSimpleCode getFkByCrptSizeId();
    public void setFkByCrptSizeId(TsSimpleCode fkByCrptSizeId);

    public String getCorporateDeputy();
    public void setCorporateDeputy(String corporateDeputy);

    public String getPhone();
    public void setPhone(String phone);

    public String getLinkman2();
    public void setLinkman2(String linkman2);

    public String getLinkphone2();
    public void setLinkphone2(String linkphone2);

    public Integer getWorkForce();
    public void setWorkForce(Integer workForce);

    public Integer getHoldCardMan();
    public void setHoldCardMan(Integer holdCardMan);
}
