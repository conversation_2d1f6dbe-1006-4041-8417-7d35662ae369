package com.chis.modules.heth.comm.service;

import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.entity.TbTjCrptConstruct;
import com.chis.modules.heth.comm.entity.TbTjCrptIndepend;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @Description : 企业信息—各单位独立信息-业务层
 * @ClassAuthor : anjing
 * @Date : 2021/4/13 15:35
 **/
@Service
@Transactional(readOnly = true)
public class TbTjCrptIndependCommServiceImpl extends AbstractTemplate {

    /**
    * @Description : 根据社会信用代码获取企业信息
    * @MethodAuthor: anjing
    * @Date : 2021/4/13 15:40
    **/
    public TbTjCrpt findTbTjCrptByInstitutionCode(String institutionCode) {
        StringBuilder hql = new StringBuilder();
        hql.append("select t from TbTjCrpt t where 1=1 and t.institutionCode = '").append(institutionCode).append("' ");
        List<TbTjCrpt> list = em.createQuery(hql.toString()).getResultList();
        if(null != list && list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /**
    * @Description : 根本社会信用代码获取企业信息—各单位独立信息
    * @MethodAuthor: anjing
    * @Date : 2021/4/13 15:38
    **/
    public TbTjCrptIndepend findTbTjCrptIndependByInstitutionCodeAndUnitId(String institutionCode, String unitId, Integer rid) {
        StringBuilder hql = new StringBuilder();
        hql.append("select t from TbTjCrptIndepend t where 1=1 ");
        hql.append("and t.fkByCrptId.institutionCode = '").append(institutionCode).append("' ");
        hql.append("and t.fkByUnitId.rid =").append(unitId);
        if(null != rid) {
            hql.append("and t.rid !=").append(rid);
        }
        List<TbTjCrptIndepend> list = em.createQuery(hql.toString()).getResultList();
        if(null != list && list.size()>0){
            return list.get(0);
        }
        return null;
    }
    
    /**
    * @Description : 保存企业信息—各单位独立信息
    * @MethodAuthor: anjing
    * @Date : 2021/4/13 16:27
    **/
    @Transactional(readOnly = false)
    public String saveTbTjCrptIndepend(TbTjCrptIndepend tbTjCrptIndepend) {
        if(null == tbTjCrptIndepend || null == tbTjCrptIndepend.getFkByCrptId() || StringUtils.isBlank(tbTjCrptIndepend.getFkByCrptId().getInstitutionCode())) {
            return null;
        }
        // 根据社会信用代码获取企业信息
        TbTjCrpt tbTjCrpt = this.findTbTjCrptByInstitutionCode(tbTjCrptIndepend.getFkByCrptId().getInstitutionCode());
        if(null == tbTjCrpt) {
            // 新增企业
            tbTjCrpt = tbTjCrptIndepend.getFkByCrptId();
            tbTjCrpt.setRid(null);
            tbTjCrpt.setCreateDate(new Date());
            tbTjCrpt.setCreateManid(Global.getUser().getRid());
            tbTjCrpt.setLinkman2(tbTjCrptIndepend.getLinkman2());
            tbTjCrpt.setLinkphone2(tbTjCrptIndepend.getLinkphone2());
            this.save(tbTjCrpt);
        } else {
            // 更新企业
            tbTjCrptIndepend.getFkByCrptId().setRid(tbTjCrpt.getRid());
            tbTjCrptIndepend.getFkByCrptId().setLinkman2(tbTjCrptIndepend.getLinkman2());
            tbTjCrptIndepend.getFkByCrptId().setLinkphone2(tbTjCrptIndepend.getLinkphone2());
            tbTjCrptIndepend.getFkByCrptId().setCreateDate(tbTjCrpt.getCreateDate());
            tbTjCrptIndepend.getFkByCrptId().setCreateManid(tbTjCrpt.getCreateManid());
            this.update(tbTjCrptIndepend.getFkByCrptId());
        }
        // 保存企业信息—各单位独立信息
        if(tbTjCrptIndepend.getRid() == null) {
            this.save(tbTjCrptIndepend);
        } else {
            this.update(tbTjCrptIndepend);
        }
        return null;
    }

    /**
     * @Description : 根据社会信用代码、创建企业ID获取企业信息
     * @MethodAuthor: anjing
     * @Date : 2021/3/31 15:36
     **/
    public List<TbTjCrpt> findTbTjCrptByCreditCodeAndCreateUnitId(String creditCode, Integer rid, String createUnitId) {
        Map<String, Object> paramMap = new HashMap<>();
        StringBuffer sql = new StringBuffer();
        sql.append(" select new TbTjCrpt(t.rid) from TbTjCrpt t where 1=1");
        if (StringUtils.isNotBlank(creditCode)) {
            sql.append(" and t.institutionCode = :creditCode ");
            paramMap.put("creditCode",creditCode);
        }
        if (null != rid) {
            sql.append(" and t.rid !=").append(rid);
        }
        if(StringUtils.isNotBlank(createUnitId)) {
            sql.append(" and t.fkByCreateUnitId.rid ='").append(createUnitId).append("' ");
        }
        return this.findDataByHqlNoPage(sql.toString(),paramMap);
    }

    /**
    * @Description : 根据社会信用代码获取企业信息—各单位独立信息集合
    * @MethodAuthor: anjing
    * @Date : 2021/4/15 14:26
    **/
    public List<TbTjCrptIndepend> findTbTjCrptIndependListByInstitutionCodeAndUnitId(String institutionCode, String unitId, Integer rid) {
        StringBuilder hql = new StringBuilder();
        hql.append("select t from TbTjCrptIndepend t where 1=1 ");
        hql.append("and t.fkByCrptId.institutionCode = '").append(institutionCode).append("' ");
        hql.append("and t.fkByUnitId.rid =").append(unitId);
        if(null != rid) {
            hql.append("and t.rid !=").append(rid);
        }
        return em.createQuery(hql.toString()).getResultList();
    }

    /**
     * <p>描述 查询 单位名称+社会信用代码 相同的企业的数量</p>
     *
     * @param institutionCode
     * @param crptName
     * @param rid
     * @MethodAuthor gongzhe,2022/7/18 17:30,findTbTjCrptByInstitutionCodeAndCrptName
     * @return int
     */
    public int findTbTjCrptCountsByInstitutionCodeAndCrptName(String institutionCode, String crptName, Integer rid,Short ifSubOrg){
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT COUNT(1) FROM TB_TJ_CRPT WHERE NVL(DEL_MARK, 0) = 0 ");
        sql.append(" AND INSTITUTION_CODE = '").append(institutionCode).append("'");
        if(StringUtils.isNotBlank(crptName)){
            sql.append(" AND CRPT_NAME = '").append(crptName).append("'");
        }
        if(ifSubOrg!=null){
            sql.append(" AND IF_SUB_ORG = ").append(ifSubOrg);
        }
        if(null != rid) {
            sql.append(" AND RID !=").append(rid);
        }
        return this.findCountBySql(sql.toString());
    }

    /**
     * <p>描述 根据主体机构ID查询分支机构数量</p>
     *
     * @param upperCrptRid
     * @MethodAuthor gongzhe,2022/7/21 9:09,findTbTjCrptSubCountsByUpperCrpt
     * @return int
     */
    public int findTbTjCrptSubCountsByUpperCrpt(Integer upperCrptRid){
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT COUNT(1) FROM TB_TJ_CRPT WHERE 1=1 ");
        sql.append(" AND UPPER_UNIT_ID = ").append(upperCrptRid);
        sql.append(" AND IF_SUB_ORG = 1 ");
        return this.findCountBySql(sql.toString());
    }

    /**
     * <p>描述 根据填报机构ID和企业ID查询单位独立信息表</p>
     *
     * @param crptId
     * @param unitId
     * @MethodAuthor gongzhe,2022/7/18 18:07,findTbTjCrptIndependByCrptIdUnitId
     * @return com.chis.modules.heth.comm.entity.TbTjCrptIndepend
     */
    public TbTjCrptIndepend findTbTjCrptIndependByCrptIdUnitId(Integer crptId, String unitId,Integer busType) {
        if(crptId == null || StringUtils.isBlank(unitId) || busType == null){
            return null;
        }
        StringBuilder hql = new StringBuilder();
        hql.append("select t from TbTjCrptIndepend t where 1=1 ");
        hql.append("and t.fkByCrptId.rid = ").append(crptId);
        hql.append("and t.fkByUnitId.rid = ").append(unitId);
        hql.append("and t.busType = ").append(busType);
        List<TbTjCrptIndepend> list = em.createQuery(hql.toString()).getResultList();
        if(null != list && list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /**
     * <p>描述 保存企业信息和企业独立信息</p>
     *
     * @param crptIndepend
     * @param editCrptComm
     * @MethodAuthor gongzhe,2022/7/19 16:18,saveTbTjCrptIndepend
     * @return void
     */
    @Transactional(readOnly = false)
    public void saveTbTjCrptIndepend(TbTjCrptIndepend crptIndepend,TbTjCrpt editCrptComm) {
        if(null == crptIndepend || null ==editCrptComm) {
            return;
        }
        if (editCrptComm.getTsSimpleCodeByIndusTypeId() != null && editCrptComm.getTsSimpleCodeByIndusTypeId().getRid() == null) {
            editCrptComm.setTsSimpleCodeByIndusTypeId(null);
        }
        if (editCrptComm.getTsSimpleCodeByEconomyId() != null && editCrptComm.getTsSimpleCodeByEconomyId().getRid() == null) {
            editCrptComm.setTsSimpleCodeByEconomyId(null);
        }
        if (editCrptComm.getTsSimpleCodeByCrptSizeId() != null && editCrptComm.getTsSimpleCodeByCrptSizeId().getRid() == null) {
            editCrptComm.setTsSimpleCodeByCrptSizeId(null);
        }
        editCrptComm.setDelMark((short)0);
        this.upsertEntityNoMod(editCrptComm);
        boolean needUpdate = false;
        StringBuilder updateSql = new StringBuilder();
        updateSql.append("UPDATE TB_TJ_CRPT SET ");
        if (ObjectUtil.isEmpty(editCrptComm.getLinkman2()) && ObjectUtil.isNotEmpty(editCrptComm.getOriLinkman2())) {
            updateSql.append("LINKMAN2 = '").append(editCrptComm.getOriLinkman2()).append("' ");
            needUpdate = true;
        }
        if (ObjectUtil.isEmpty(editCrptComm.getLinkphone2()) && ObjectUtil.isNotEmpty(editCrptComm.getOriLinkphone2())) {
            if (needUpdate) {
                updateSql.append(", ");
            }
            updateSql.append("LINKPHONE2 = '").append(editCrptComm.getOriLinkphone2()).append("' ");
            needUpdate = true;
        }
        if (needUpdate) {
            updateSql.append("WHERE RID = ").append(editCrptComm.getRid());
            em.createNativeQuery(updateSql.toString()).executeUpdate();
        }
        if (new Short((short) 0).equals(editCrptComm.getIfSubOrg())) {
            //主体机构，同步更新其他分支机构的社会信用代码
            StringBuilder sql = new StringBuilder();
            sql.append(" UPDATE TB_TJ_CRPT SET INSTITUTION_CODE = '").append(editCrptComm.getInstitutionCode()).append("'");
            sql.append(" WHERE IF_SUB_ORG = 1 AND UPPER_UNIT_ID = ").append(editCrptComm.getRid());
            em.createNativeQuery(sql.toString()).executeUpdate();
        }
        crptIndepend.setFkByCrptId(editCrptComm);
        crptIndepend.setLinkman2(editCrptComm.getLinkman2());
        crptIndepend.setLinkphone2(editCrptComm.getLinkphone2());
        this.upsertEntity(crptIndepend);
    }

    /**
     * <p>描述 保存企业信息和企业独立信息</p>
     *
     * @param crptIndepend
     * @param editCrptComm
     * @MethodAuthor gongzhe,2022/7/19 16:18,saveTbTjCrptIndepend
     * @return void
     */
    @Transactional(readOnly = false)
    public void saveTbTjCrptIndependNew(TbTjCrptIndepend crptIndepend,TbTjCrpt editCrptComm) {
        if(null == crptIndepend || null ==editCrptComm) {
            return;
        }
        if (editCrptComm.getTsSimpleCodeByIndusTypeId() != null && editCrptComm.getTsSimpleCodeByIndusTypeId().getRid() == null) {
            editCrptComm.setTsSimpleCodeByIndusTypeId(null);
        }
        if (editCrptComm.getTsSimpleCodeByEconomyId() != null && editCrptComm.getTsSimpleCodeByEconomyId().getRid() == null) {
            editCrptComm.setTsSimpleCodeByEconomyId(null);
        }
        if (editCrptComm.getTsSimpleCodeByCrptSizeId() != null && editCrptComm.getTsSimpleCodeByCrptSizeId().getRid() == null) {
            editCrptComm.setTsSimpleCodeByCrptSizeId(null);
        }
        if (ObjectUtil.isEmpty(editCrptComm.getLinkman2()) && ObjectUtil.isNotEmpty(editCrptComm.getOriLinkman2())) {
            editCrptComm.setLinkman2(editCrptComm.getOriLinkman2());
        }
        if (ObjectUtil.isEmpty(editCrptComm.getLinkphone2()) && ObjectUtil.isNotEmpty(editCrptComm.getOriLinkphone2())) {
            editCrptComm.setLinkphone2(editCrptComm.getOriLinkphone2());
        }
        if (ObjectUtil.isEmpty(editCrptComm.getOperationStatus())) {
            editCrptComm.setOperationStatus(1);
        }
        if (ObjectUtil.isEmpty(editCrptComm.getIfSubOrg())) {
            editCrptComm.setIfSubOrg(new Short("0"));
        }
        if (ObjectUtil.isEmpty(editCrptComm.getIfVirtual())) {
            editCrptComm.setIfVirtual(0);
        }
        editCrptComm.setDelMark((short)0);
        this.upsertEntity(editCrptComm);
        updateCrptMasterDataTimeByRid(editCrptComm.getRid());
        crptIndepend.setFkByCrptId(editCrptComm);
        crptIndepend.setLinkman2(editCrptComm.getLinkman2());
        crptIndepend.setLinkphone2(editCrptComm.getLinkphone2());
        this.upsertEntity(crptIndepend);
    }

    /**
     * <p>方法描述：描述 保存企业信息和企业独立信息 </p>
     * 增加健康企业建设情况信息
     * 基于saveTbTjCrptIndepend调整
     * @MethodAuthor： pw 2023/5/12
     **/
    @Transactional(readOnly = false)
    public void saveTbTjCrptIndepend(TbTjCrptIndepend crptIndepend,TbTjCrpt editCrptComm, List<Integer> selectHealthCrptIdList) {
        if(null == crptIndepend || null ==editCrptComm) {
            return;
        }
        if (editCrptComm.getTsSimpleCodeByIndusTypeId() != null && editCrptComm.getTsSimpleCodeByIndusTypeId().getRid() == null) {
            editCrptComm.setTsSimpleCodeByIndusTypeId(null);
        }
        if (editCrptComm.getTsSimpleCodeByEconomyId() != null && editCrptComm.getTsSimpleCodeByEconomyId().getRid() == null) {
            editCrptComm.setTsSimpleCodeByEconomyId(null);
        }
        if (editCrptComm.getTsSimpleCodeByCrptSizeId() != null && editCrptComm.getTsSimpleCodeByCrptSizeId().getRid() == null) {
            editCrptComm.setTsSimpleCodeByCrptSizeId(null);
        }
        editCrptComm.setDelMark((short)0);
        this.upsertEntityNoMod(editCrptComm);
        boolean needUpdate = false;
        StringBuilder updateSql = new StringBuilder();
        updateSql.append("UPDATE TB_TJ_CRPT SET ");
        if (ObjectUtil.isEmpty(editCrptComm.getLinkman2()) && ObjectUtil.isNotEmpty(editCrptComm.getOriLinkman2())) {
            updateSql.append("LINKMAN2 = '").append(editCrptComm.getOriLinkman2()).append("' ");
            needUpdate = true;
        }
        if (ObjectUtil.isEmpty(editCrptComm.getLinkphone2()) && ObjectUtil.isNotEmpty(editCrptComm.getOriLinkphone2())) {
            if (needUpdate) {
                updateSql.append(", ");
            }
            updateSql.append("LINKPHONE2 = '").append(editCrptComm.getOriLinkphone2()).append("' ");
            needUpdate = true;
        }
        if (needUpdate) {
            updateSql.append("WHERE RID = ").append(editCrptComm.getRid());
            em.createNativeQuery(updateSql.toString()).executeUpdate();
        }
        if (new Short((short) 0).equals(editCrptComm.getIfSubOrg())) {
            //主体机构，同步更新其他分支机构的社会信用代码
            StringBuilder sql = new StringBuilder();
            sql.append(" UPDATE TB_TJ_CRPT SET INSTITUTION_CODE = '").append(editCrptComm.getInstitutionCode()).append("'");
            sql.append(" WHERE IF_SUB_ORG = 1 AND UPPER_UNIT_ID = ").append(editCrptComm.getRid());
            em.createNativeQuery(sql.toString()).executeUpdate();
        }
        //健康企业建设情况
        if(!CollectionUtils.isEmpty(selectHealthCrptIdList)){
            String delSql = " DELETE FROM TB_TJ_CRPT_CONSTRUCT T WHERE T.CRPT_ID="+editCrptComm.getRid();
            this.executeSql(delSql, null);
            List<TbTjCrptConstruct> saveConstructList = new ArrayList<>();
            for(Integer conRid : selectHealthCrptIdList){
                if(null == conRid){
                    continue;
                }
                TbTjCrptConstruct crptConstruct = new TbTjCrptConstruct();
                crptConstruct.setFkByCrptId(editCrptComm);
                crptConstruct.setFkByConstructId(new TsSimpleCode(conRid));
                preInsert(crptConstruct);
                saveConstructList.add(crptConstruct);
            }
            if(!CollectionUtils.isEmpty(saveConstructList)){
                this.saveBatchObjs(saveConstructList);
            }
        }
        crptIndepend.setFkByCrptId(editCrptComm);
        crptIndepend.setLinkman2(editCrptComm.getLinkman2());
        crptIndepend.setLinkphone2(editCrptComm.getLinkphone2());
        this.upsertEntity(crptIndepend);
    }

    /**
     * 保存企业信息和企业独立信息
     * 基于saveTbTjCrptIndepend调整
     **/
    @Transactional(readOnly = false)
    public void saveTbTjCrptIndependNew(TbTjCrptIndepend crptIndepend,TbTjCrpt editCrptComm, List<Integer> selectHealthCrptIdList) {
        if (null == crptIndepend || null == editCrptComm) {
            return;
        }
        if (editCrptComm.getTsSimpleCodeByIndusTypeId() != null && editCrptComm.getTsSimpleCodeByIndusTypeId().getRid() == null) {
            editCrptComm.setTsSimpleCodeByIndusTypeId(null);
        }
        if (editCrptComm.getTsSimpleCodeByEconomyId() != null && editCrptComm.getTsSimpleCodeByEconomyId().getRid() == null) {
            editCrptComm.setTsSimpleCodeByEconomyId(null);
        }
        if (editCrptComm.getTsSimpleCodeByCrptSizeId() != null && editCrptComm.getTsSimpleCodeByCrptSizeId().getRid() == null) {
            editCrptComm.setTsSimpleCodeByCrptSizeId(null);
        }
        if (ObjectUtil.isEmpty(editCrptComm.getLinkman2()) && ObjectUtil.isNotEmpty(editCrptComm.getOriLinkman2())) {
            editCrptComm.setLinkman2(editCrptComm.getOriLinkman2());
        }
        if (ObjectUtil.isEmpty(editCrptComm.getLinkphone2()) && ObjectUtil.isNotEmpty(editCrptComm.getOriLinkphone2())) {
            editCrptComm.setLinkphone2(editCrptComm.getOriLinkphone2());
        }
        if (ObjectUtil.isEmpty(editCrptComm.getOperationStatus())) {
            editCrptComm.setOperationStatus(1);
        }
        if (ObjectUtil.isEmpty(editCrptComm.getIfSubOrg())) {
            editCrptComm.setIfSubOrg(new Short("0"));
        }
        if (ObjectUtil.isEmpty(editCrptComm.getIfVirtual())) {
            editCrptComm.setIfVirtual(0);
        }
        editCrptComm.setDelMark((short) 0);
        this.upsertEntity(editCrptComm);
        updateCrptMasterDataTimeByRid(editCrptComm.getRid());

        //健康企业建设情况
        if (!CollectionUtils.isEmpty(selectHealthCrptIdList)) {
            String delSql = " DELETE FROM TB_TJ_CRPT_CONSTRUCT T WHERE T.CRPT_ID=" + editCrptComm.getRid();
            this.executeSql(delSql, null);
            List<TbTjCrptConstruct> saveConstructList = new ArrayList<>();
            for (Integer conRid : selectHealthCrptIdList) {
                if (null == conRid) {
                    continue;
                }
                TbTjCrptConstruct crptConstruct = new TbTjCrptConstruct();
                crptConstruct.setFkByCrptId(editCrptComm);
                crptConstruct.setFkByConstructId(new TsSimpleCode(conRid));
                preInsert(crptConstruct);
                saveConstructList.add(crptConstruct);
            }
            if (!CollectionUtils.isEmpty(saveConstructList)) {
                this.saveBatchObjs(saveConstructList);
            }
        }
        crptIndepend.setFkByCrptId(editCrptComm);
        crptIndepend.setLinkman2(editCrptComm.getLinkman2());
        crptIndepend.setLinkphone2(editCrptComm.getLinkphone2());
        this.upsertEntity(crptIndepend);
    }

    /**
     * 根据RID更新主数据同步时间
     *
     * @param rid rid
     */
    public void updateCrptMasterDataTimeByRid(Integer rid) {
        String sql = "UPDATE TB_TJ_CRPT SET MASTER_DATA_TIME = SYSDATE WHERE RID = :rid";
        Map<String, Object> param = new HashMap<>();
        param.put("rid", rid);
        executeSql(sql, param);
    }

    /**
     * <p>描述 查询用人单位审核记录数</p>
     *
     * @param crptId
     * @MethodAuthor gongzhe,2022/7/19 16:19,findTdZwBgkLastStaCrpt
     * @return int
     */
    public int findTdZwBgkLastStaCrpt(Integer crptId){
        if(crptId != null){
            String sql =  " select count(1) from TD_ZW_BGK_LAST_STA where CART_TYPE = 10 and BUS_ID = "+crptId;
            return this.findCountBySql(sql.toString());
        }
        return 0;
    }

    /**
     * <p>方法描述：查询健康企业建设情况数量 </p>
     * @MethodAuthor： pw 2023/5/12
     **/
    public int findCrptConstructCount(Integer crptId){
        if(crptId != null){
            String sql =  " select count(1) from TB_TJ_CRPT_CONSTRUCT where CRPT_ID = "+crptId;
            return this.findCountBySql(sql.toString());
        }
        return 0;
    }

    /**
     * <p>方法描述：查询健康企业建设情况 </p>
     * @MethodAuthor： pw 2023/5/12
     **/
    public List<Object[]> findCrptConstructInfo(Integer crptId){
        if(null == crptId){
            return null;
        }
        String sql = " SELECT T1.RID,T1.CODE_NAME FROM TB_TJ_CRPT_CONSTRUCT T LEFT JOIN TS_SIMPLE_CODE T1 ON T.CONSTRUCT_ID = T1.RID WHERE T.CRPT_ID ="+crptId+" ORDER BY T1.NUM ";
        return this.findDataBySqlNoPage(sql, null);
    }
}
