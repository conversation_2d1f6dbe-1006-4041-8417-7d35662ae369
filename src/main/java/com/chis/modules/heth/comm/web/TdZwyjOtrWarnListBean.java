package com.chis.modules.heth.comm.web;

import com.chis.common.bean.FastReportData;
import com.chis.common.bean.FastReportDataRef;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.heth.comm.entity.TdZwyjOtrDealWrit;
import com.chis.modules.heth.comm.entity.TdZwyjOtrWarn;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.rptvo.OverRangeRecordVo;
import com.chis.modules.heth.comm.rptvo.OverRangeRecordVoForZone;
import com.chis.modules.heth.comm.rptvo.OverRangeVo;
import com.chis.modules.heth.comm.service.ZwReportCardCommServiceImpl;
import com.chis.modules.system.entity.*;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.interfaces.IFastReport;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.HolidayUtil;
import com.chis.modules.system.web.FacesEditBean;
import com.chis.modules.system.web.FastReportBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * @ClassName TdZwyjOtrWarnListBean
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020-11-16
 * @Version V1.0
 **/
@ManagedBean(name = "tdZwyjOtrWarnListBean")
@ViewScoped
public class TdZwyjOtrWarnListBean extends FacesEditBean implements IProcessData , IFastReport {

    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private ZwReportCardCommServiceImpl cardServiceImpl = SpringContextHolder.getBean(ZwReportCardCommServiceImpl.class);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    private String limitTime;
    /**判断初审复审*/
    private Integer checkType;

    /**查询条件：地区名称*/
    private String searchZoneName;
    /**查询条件：地区编码*/
    private String searchZoneCode;
    /**查询条件：地区集合*/
    private List<TsZone> zoneList;
    /**预警类型*/
    private Integer warnType;
    /**发现日期*/
    private Date happenStartDate;
    private Date happenEndDate;
    /**处置日期*/
    private Date dealStartDate;
    private Date dealEndDate;
    /**审核日期*/
    private Date checkStartDate;
    private Date checkEndDate;

    /**是否超范围*/
    private String[] outRanges;
    /**状态*/
    private List<SelectItem> stateList = new ArrayList<>();
    private String[] states;

    /**预警主表ID*/
    private Integer rid;

    /**预警主表*/
    private TdZwyjOtrWarn tdZwyjOtrWarn;
    /**默认审核意见*/
    private String defaultAuditAdv;
    /**是否已提交资质机构*/
    private boolean ifZzOrg;
    /**是否有资质备案*/
    private boolean ifZzba;
    /***检查机构资质信息*/
    private TdZwTjorginfoComm tdZwTjorginfoComm;
    /***系统单位信息*/
    private TsUnit tsUnit;
    /**备案信息地区*/
    private Map<Integer, TsZone> recordZoneMap;
    /**是否按地区备案，0否1是*/
    private String ifZoneRecord;
    /**地区备案信息集合*/
    private List<TdZwTjorgRecordComm> tjorgRecords;
    /**管辖地区显示*/
    private String managerZone;
    /**超范围服务预警企业名单集合*/
    private List<TdZwyjOtrCrptList> tdZwyjOtrCrptList;
    /**超范围服务预警企业名单[超范围预警]*/
    private TdZwyjOtrCrptList tdZwyjOtrCrpt;
    /**超范围服务预警人员名单[超范围预警]*/
    private List<TdZwyjOtrBhk> personList;
    /**企业id*/
    private Integer crptId;

    /**报表设计模板*/
    protected FastReportBean fastReportBean;
    /**文书模版*/
    private TsRpt tsRpt;
    /**是否可设计*/
    private boolean ifdesign;
    /**文书实体*/
    private TdZwyjOtrDealWrit tdZwyjOtrDealWrit;
    /***检查机构资质信息备案更新日期*/
    private TdZwTjorginfoComm tdZwTjorginfoDate;

    public TdZwyjOtrWarnListBean(){
        this.ifSQL = true;
        String val = JsfUtil.getRequest().getParameter("tag");
        if("1".equals(val)){
            checkType = 1;
        }else {
            checkType = 2;
        }
        String value = PropertyUtils.getValueWithoutException("rpt.ifDesign");
        if("1".equals(value)){
            ifdesign = true;
        }else{
            ifdesign = false;
        }
        init();
        this.searchAction();
    }

    private void init(){
        /**默认审核意见*/
        defaultAuditAdv = PropertyUtils.getValue("zdzybdefaultAuditAdv");
        this.ifZoneRecord = PropertyUtils.getValue("ifZoneRecord");
        limitTime = PropertyUtils.getValue("outRangeLimitTime");
        this.happenStartDate = DateUtils.getYearFirstDay(new Date());
        this.happenEndDate = new Date();
        TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
        }

        //备案地区
        List<TsZone> recordZoneList = commService.findZoneList(tsZone.getZoneGb().substring(0, 2), 2);
        this.recordZoneMap = new HashMap<Integer, TsZone>();
        if (!CollectionUtils.isEmpty(recordZoneList)) {
            for (TsZone t : recordZoneList) {
                this.recordZoneMap.put(t.getRid(), t);
            }
        }

        this.searchZoneCode = tsZone.getZoneCode();
        this.searchZoneName = tsZone.getZoneName();
        this.zoneList = this.commService.findZoneList(false, tsZone.getZoneGb(), null, null);
        initSearchStates();
    }

    /**
     * @MethodName: initSearchStates
     * @Description: 状态
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-11-16
    **/
    private void initSearchStates() {
        // 1、区县审核，状态为：待处置、已处置、终审退回、终审通过，默认（待审核、复审退回）
        // 2、省级审核，状态为：待审核、已退回、审核通过

        this.stateList = new ArrayList<>();
        if (checkType == 2) {
            this.states = new String[] {"1"};
            this.stateList.add(new SelectItem("1", "待审核"));
            this.stateList.add(new SelectItem("2", "已退回"));
            this.stateList.add(new SelectItem("3", "审核通过"));
        }  else {
            this.states = new String[] {"0","2"};
            this.stateList.add(new SelectItem("0", "待处置"));
            this.stateList.add(new SelectItem("1", "已处置"));
            this.stateList.add(new SelectItem("2", "终审退回"));
            this.stateList.add(new SelectItem("3", "终审通过"));
        }
    }


    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {
        if(StringUtils.isNotBlank(tdZwyjOtrDealWrit.getWritAnnexPath())){
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("disabledInput('true','writ_yszyb')");
        }
    }


    @Override
    public void modInit() {
        tdZwyjOtrWarn = commService.find(TdZwyjOtrWarn.class,rid);
        ifZzba =false;
        tdZwTjorginfoComm = new TdZwTjorginfoComm();
        tdZwTjorginfoComm.setTsUnit(new TsUnit());
        tdZwTjorginfoComm.getTsUnit().setTsZone(new TsZone());
        tsUnit = new TsUnit();
        StringBuffer hql = new StringBuffer();
        hql.append("select  t from TdZwTjorginfoComm t where t.state = 1 and  t.tsUnit.rid =").append(tdZwyjOtrWarn.getFkByBhkorgId().getRid());
        List<TdZwTjorginfoComm> tdZwTjorginfoComms = cardServiceImpl.findByHql(hql.toString(), TdZwTjorginfoComm.class);
        if(!CollectionUtils.isEmpty(tdZwTjorginfoComms)){
            tdZwTjorginfoComm = tdZwTjorginfoComms.get(0);
            if(null != tdZwTjorginfoComm.getTsUnit().getTsZone() && null != tdZwTjorginfoComm.getTsUnit().getTsZone().getFullName()){
                String fullName = tdZwTjorginfoComm.getTsUnit().getTsZone().getFullName();
                int i = fullName.indexOf("_");
                tdZwTjorginfoComm.getTsUnit().getTsZone().setFullName(fullName.substring(i+1));
            }
            ifZzOrg =true;
            if ("1".equals(ifZoneRecord)) {
                initTjorgRecords();
            }
            managerZone = tdZwyjOtrWarn.getFkByCityZoneId().getZoneName();
        }else{
            tsUnit = cardServiceImpl.find(TsUnit.class,tdZwyjOtrWarn.getFkByBhkorgId().getRid());
            ifZzOrg =false;
            if(null != tsUnit.getTsZone() && null !=tsUnit.getTsZone().getFullName()){
                String fullName = tsUnit.getTsZone().getFullName();
                int i = fullName.indexOf("_");
                tsUnit.getTsZone().setFullName(fullName.substring(i+1));
            }
            managerZone = tdZwyjOtrWarn.getFkByCityZoneId().getZoneName();
        }



        StringBuffer hql2 = new StringBuffer();
        hql2.append("select  t from TdZwTjorginfoComm t where  t.tsUnit.rid =").append(tdZwyjOtrWarn.getFkByBhkorgId().getRid());
        List<TdZwTjorginfoComm> dates = cardServiceImpl.findByHql(hql2.toString(), TdZwTjorginfoComm.class);
        if(!CollectionUtils.isEmpty(dates)) {
            tdZwTjorginfoDate = dates.get(0);
        }

        initTdZwyjOtrCrptList();
        initTdZwyjOtrDealWrit();
        RequestContext.getCurrentInstance().scrollTo("tabView:editForm:editTitleGrid");
    }


    /**
     * <p>方法描述：初始化超范围服务预警人员名单[超范围预警]</p>
     * @MethodAuthor rcj,2020年11月19日,viewPersonList
     * */
    public  void viewPersonList(){
        StringBuffer hql = new StringBuffer();
        hql.append(" select t.fkByCrptId from TdZwyjOtrPsnList t where t.fkByMainId.rid = ").append(crptId).append(" order by t.fkByCrptId.happenDate desc,t.fkByCrptId.personName");
        personList = cardServiceImpl.findByHql(hql.toString(),TdZwyjOtrBhk.class);
        if(!CollectionUtils.isEmpty(personList)){
            for (TdZwyjOtrBhk tdZwyjOtrBhk:personList) {
                if(StringUtils.isNotBlank(tdZwyjOtrBhk.getIdc())){
                    String trim = tdZwyjOtrBhk.getIdc().trim();
                    if(tdZwyjOtrBhk.getIdc().trim().length()==15){
                        tdZwyjOtrBhk.setIdc(trim.substring(0,6)+"******"+trim.substring(12)) ;
                    }else if(tdZwyjOtrBhk.getIdc().trim().length()==18){
                        tdZwyjOtrBhk.setIdc(trim.substring(0,6)+"********"+trim.substring(14))  ;
                    }else if(tdZwyjOtrBhk.getIdc().trim().length()>=4){
                        tdZwyjOtrBhk.setIdc(trim.substring(0,trim.length()-4)+"****")  ;
                    }
                }
            }
        }
    }


    /**
     * <p>方法描述：初始化超范围服务预警企业名单[超范围预警]</p>
     * @MethodAuthor rcj,2020年11月19日,initTdZwyjOtrCrptList
     * */
    private void initTdZwyjOtrCrptList(){
        tdZwyjOtrCrptList = cardServiceImpl.findTdZwyjOtrCrptList(rid);
    }

    /**
     * <p>方法描述：初始化备案记录</p>
     * @MethodAuthor rcj,2020年11月19日,initTjorgRecords
     * */
    private void initTjorgRecords() {
        this.tjorgRecords = cardServiceImpl.findTjorgRecords(tdZwTjorginfoComm.getRid(),ZoneUtil.zoneSelect(tdZwyjOtrWarn.getFkByCityZoneId().getZoneGb()));
        if (CollectionUtils.isEmpty(this.tjorgRecords)) {
            this.tjorgRecords = new ArrayList<>();
        }else{
            ifZzba =true;
        }
        for (TdZwTjorgRecordComm t : this.tjorgRecords) {
            List<TdZwTjorgRcdItemComm> rcdItems = t.getRcdItems();
            if (!CollectionUtils.isEmpty(rcdItems)) {
                List<String> serviceObj = new ArrayList<>();
                StringBuffer sb = new StringBuffer();
                for (TdZwTjorgRcdItemComm itm : rcdItems) {
                    sb.append("，").append(itm.getFkByItemId().getCodeName());
                    serviceObj.add(itm.getFkByItemId().getRid().toString());
                }
                t.setServiceItems(sb.substring(1));
                t.setServiceObj(serviceObj);

                //地区
                t.setZoneId(t.getFkByZoneId().getRid());
            }
        }


    }

    private void initTdZwyjOtrDealWrit(){
        tdZwyjOtrDealWrit= cardServiceImpl.findTdZwyjOtrDealWritByRid(rid);
        if(null ==tdZwyjOtrDealWrit){
            tdZwyjOtrDealWrit = new TdZwyjOtrDealWrit();
            TsUnit unit = sessionData.getUser().getTsUnit();
            tdZwyjOtrDealWrit.setWritContent(tdZwyjOtrWarn.getWarnCont());
            tdZwyjOtrDealWrit.setDealUnit(unit.getUnitname());
            tdZwyjOtrDealWrit.setWritDate(new Date());
            tdZwyjOtrDealWrit.setFkByMainId(tdZwyjOtrWarn);
        }
    }


    /**
     * <p>方法描述：切换审核结果</p>
     * @MethodAuthor rcj,2020年11月18日,changeSubmitRst
     * */
    public void changeSubmitRst(){

        // 通过且意见为空则默认通过
        if(null != tdZwyjOtrWarn.getCheckRst() && tdZwyjOtrWarn.getCheckRst().intValue() == 1){
            if(StringUtils.isBlank(tdZwyjOtrWarn.getCheckCont())){
                tdZwyjOtrWarn.setCheckCont(defaultAuditAdv);
            }
        }
    }

    @Override
    public void saveAction() {
        try {
            cardServiceImpl.upsertEntity(tdZwyjOtrWarn);
            JsfUtil.addSuccessMessage("保存成功！");
        }catch (Exception e ){
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }

    }

    /**
     * @MethodName: submitAction提交
     * @Return: void
     * @Author: rcj
     * @Date: 2020-11-18
     **/
    public void submitAction() {
        try {
            boolean flag =false;
            if(checkType == 1){

                if(tdZwyjOtrWarn.getIfOutRange() == null){
                    JsfUtil.addErrorMessage("是否超范围不能为空！");
                    flag = true;
                }
                if(StringUtils.isBlank(tdZwyjOtrWarn.getDealCont())){
                    JsfUtil.addErrorMessage("处置说明不能为空！");
                    flag = true;
                }


            }else if(checkType == 2){
                if(tdZwyjOtrWarn.getCheckRst() == null){
                    JsfUtil.addErrorMessage("审核结果不能为空！");
                    flag = true;
                }
                if(StringUtils.isBlank(tdZwyjOtrWarn.getCheckCont())){
                    JsfUtil.addErrorMessage("审核说明不能为空！");
                    flag = true;
                }
            }
            if (flag) {
                return;
            }
            if(checkType==1){
                tdZwyjOtrWarn.setStateMark(1);
                tdZwyjOtrWarn.setDealDate(new Date());
                tdZwyjOtrWarn.setFkByDealOrgId(sessionData.getUser().getTsUnit());
                tdZwyjOtrWarn.setCheckCont(null);
                tdZwyjOtrWarn.setCheckRst(null);
                tdZwyjOtrWarn.setCheckDate(null);
            }else if(checkType==2){
                if(null != tdZwyjOtrWarn.getCheckRst() && tdZwyjOtrWarn.getCheckRst().intValue()==1){
                    tdZwyjOtrWarn.setStateMark(3);
                }else if(null != tdZwyjOtrWarn.getCheckRst() && tdZwyjOtrWarn.getCheckRst().intValue()==2){
                    tdZwyjOtrWarn.setStateMark(2);
                }
                tdZwyjOtrWarn.setFkByCheckOrgId(sessionData.getUser().getTsUnit());
                tdZwyjOtrWarn.setCheckDate(new Date());
            }
            cardServiceImpl.upsertEntity(tdZwyjOtrWarn);
            this.searchAction();
            this.backAction();
            RequestContext context = RequestContext.getCurrentInstance();
            context.update("tabView");
            JsfUtil.addSuccessMessage("提交成功！");
        }catch (Exception e ){
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }

    }

    /**
     * @Description : 审核撤销
     * @MethodAuthor: rcj
     * @Date : 2020/11/18 11:13
     **/
    public void revokeAction() {
        try {
            if(checkType==1){
                tdZwyjOtrWarn.setStateMark(0);
                tdZwyjOtrWarn.setDealDate(null);
            }else{
                tdZwyjOtrWarn.setStateMark(1);
                tdZwyjOtrWarn.setCheckDate(null);
            }
            cardServiceImpl.upsertEntity(tdZwyjOtrWarn);
            modInitAction();
            JsfUtil.addSuccessMessage("撤销成功！");
        }catch (Exception e ){
            JsfUtil.addErrorMessage("撤销失败！");
            e.printStackTrace();
        }

    }


    @Override
    public void searchAction() {
        if(DateUtils.isDateAfter(happenStartDate, happenEndDate)){
            JsfUtil.addErrorMessage("发现开始日期应小于等于发现结束日期！");
            return;
        }
        if(DateUtils.isDateAfter(dealStartDate, dealEndDate)){
            JsfUtil.addErrorMessage("处置开始日期应小于等于处置结束日期！");
            return;
        }
        if(DateUtils.isDateAfter(checkStartDate, checkEndDate)){
            JsfUtil.addErrorMessage("审核开始日期应小于等于审核结束日期！");
            return;
        }
        super.searchAction();
    }

    /**
     * @MethodName: saveRptAction
     * @Description: 保存
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-11-19
    **/
    public void saveRptAction(){
        try{
            cardServiceImpl.upsertEntity(tdZwyjOtrDealWrit);
            JsfUtil.addSuccessMessage("保存成功！");
        }catch (Exception e){
            JsfUtil.addErrorMessage("保存失败！");
        }

    }


    public void deleteRpt(){
        try {
            tdZwyjOtrDealWrit.setWritAnnexPath(null);
            cardServiceImpl.upsertEntity(tdZwyjOtrDealWrit);
            JsfUtil.addSuccessMessage("删除成功！");
        }catch (Exception e){
            JsfUtil.addErrorMessage("删除失败！");
            e.printStackTrace();
        }
    }

    /**
     * @MethodName: beforeUpload
     * @Description: 上传前验证
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-11-19
    **/
    public void beforeUpload(){
        if(veryUpload()){
            return;
        }

        RequestContext.getCurrentInstance().execute("PF('FileDialog').show()");
        RequestContext.getCurrentInstance().update("fileDialog");
    }

    /**
     * @MethodName: veryUpload
     * @Description: 上传前验证
     * @Param: []
     * @Return: boolean
     * @Author: maox
     * @Date: 2020-11-20
    **/
    private boolean veryUpload(){
        boolean flag = false;
        if(StringUtils.isBlank(tdZwyjOtrDealWrit.getNoticeUnit())){
            JsfUtil.addErrorMessage("请输入卫生健康委部门名称！");
            flag = true;
        }
        if(StringUtils.isBlank(tdZwyjOtrDealWrit.getWritContent())){
            JsfUtil.addErrorMessage("请输入文书内容信息！");
            flag = true;
        }
        if(null ==tdZwyjOtrDealWrit.getWritDate()){
            JsfUtil.addErrorMessage("请填写文书日期！");
            flag = true;
        }
            return flag;
    }


    /**
     * @MethodName: fileUpload
     * @Description: 上传附件
     * @Param: [event]
     * @Return: void
     * @Author: maox
     * @Date: 2020-11-19
    **/
    public void fileUpload(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            try {
                String fileName = file.getFileName();// 文件名称
                String errorMsg = FileUtils.veryFile(file.getInputstream(),file.getContentType(),fileName, "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    return;
                }
                String uuid = java.util.UUID.randomUUID().toString()
                        .replaceAll("-", "");
                String path = JsfUtil.getAbsolutePath();
                String relativePath = new StringBuffer("law/")
                        .append(uuid)
                        .append(fileName.substring(fileName
                                .lastIndexOf("."))).toString();
                // 文件路径
                String filePath = new StringBuilder(path).append(
                        relativePath).toString();
                tdZwyjOtrDealWrit.setWritAnnexPath(relativePath);

                FileUtils.copyFile(filePath, file.getInputstream());

                cardServiceImpl.upsertEntity(tdZwyjOtrDealWrit);
                RequestContext.getCurrentInstance().execute(
                        "PF('FileDialog').hide()");
                RequestContext.getCurrentInstance().update(
                        "tabView:viewForm");
                JsfUtil.addSuccessMessage("上传成功！");
                RequestContext currentInstance = RequestContext.getCurrentInstance();
                currentInstance.execute("disabledInput('true','writ_yszyb')");
            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("上传失败！");
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * @MethodName: delAnnex
     * @Description: 删除附件
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-11-19
    **/
    public void delMadedwrit(){
        try {
            tdZwyjOtrDealWrit.setWritAnnexPath(null);
            cardServiceImpl.upsertEntity(tdZwyjOtrDealWrit);
            JsfUtil.addSuccessMessage("删除成功！");
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("disabledInput('false','writ_yszyb')");
        }catch (Exception e){
            JsfUtil.addErrorMessage("删除失败！");
            e.printStackTrace();
        }

    }

    public void buildWritReport() {
        try {
            if(veryUpload()||veryWritsort()){
                return;
            }
            cardServiceImpl.upsertEntity(tdZwyjOtrDealWrit);
            this.showReportAction();
        } catch (Exception e) {
            JsfUtil.addErrorMessage("打印失败！");
        }

    }

    public void showReportAction(){
        if (tsRpt != null) {
            try {
                this.fastReportBean = new FastReportBean(this, this.tsRpt.getRptCod());
                fastReportBean.showPDFReportAction(null);
                RequestContext.getCurrentInstance().execute("frpt_showPDF();");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @MethodName: designWritReport
     * @Description: 设计文书
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-11-19
    **/
    public void designWritReport() {
        if(veryUpload()||veryWritsort()){
            return;
        }
        this.fastReportBean = new FastReportBean(this, tsRpt.getRptCod());
        fastReportBean.designAction();
    }

    /**
     * @MethodName: veryWritsort
     * @Description: 文书验证
     * @Param: []
     * @Return: boolean
     * @Author: maox
     * @Date: 2020-11-19
    **/
    private boolean veryWritsort(){
        boolean flag = false;
        tsRpt = getWritsort("HETH_6001");
        if (tsRpt == null || tsRpt.getRid() == null) {
            JsfUtil.addErrorMessage("请在维护报表模板！");
            flag = true;
        }
        return flag;
    }


    private  TsRpt getWritsort(String writCode){
        if (StringUtils.isNotBlank(writCode)) {
            String hql = "select t from TsRpt t where t.rptCod = '"
                    + writCode+"'";
            TsRpt rpt = commService.findOneByHql(hql,
                    TsRpt.class);
            return rpt;
        }
        return null;

    }


    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();

        sql.append(" from TD_ZWYJ_OTR_WARN t");
        sql.append(" left join ts_zone t1 on t.BHKORG_ZONE_ID = t1.rid");
        sql.append(" left join TS_UNIT t2 on t.bhkorg_id = t2.rid");
        sql.append(" left join ts_unit t3 on t.DEAL_ORG_ID =t3.rid");
        sql.append(" left join ts_zone t4 on t.CITY_ZONE_ID = t4.rid ");
        sql.append(" WHERE 1=1 ");

        //地区
        if(checkType ==1){
            sql.append(" AND T4.ZONE_GB LIKE :cityzonecode escape '\\\'");
            this.paramMap.put("cityzonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }else{
//            if (StringUtils.isNotBlank(this.searchZoneCode)) {
//                sql.append(" AND T1.ZONE_GB LIKE :zonecode escape '\\\'");
//                this.paramMap.put("zonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
//            }
        }

        //预警类型
        if(null != warnType){
            sql.append("AND t.WARN_TYPE =:warnType");
            this.paramMap.put("warnType",warnType);
        }
        //发现日期
        if (null != this.happenStartDate) {
            sql.append(" AND T.HAPPEN_DATE  >= TO_DATE('").append(DateUtils.formatDate(this.happenStartDate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.happenEndDate) {
            sql.append(" AND t.HAPPEN_DATE <= TO_DATE('").append(DateUtils.formatDate(this.happenEndDate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        //处置日期
        if (null != this.dealStartDate) {
            sql.append(" AND T.DEAL_DATE  >= TO_DATE('").append(DateUtils.formatDate(this.dealStartDate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.dealEndDate) {
            sql.append(" AND t.DEAL_DATE <= TO_DATE('").append(DateUtils.formatDate(this.dealEndDate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        //是否超范围
        if(null != outRanges && outRanges.length>0){
            StringBuffer stateSb = new StringBuffer();
            for (String state : outRanges) {
                stateSb.append(",").append(state);
            }

            sql.append(" AND t.IF_OUT_RANGE IN (").append(stateSb.substring(1)).append(")");
        }
        //审核日期
        if (null != this.checkStartDate) {
            sql.append(" AND T.CHECK_DATE  >= TO_DATE('").append(DateUtils.formatDate(this.checkStartDate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.checkEndDate) {
            sql.append(" AND t.CHECK_DATE <= TO_DATE('").append(DateUtils.formatDate(this.checkEndDate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }

        if(null != states && states.length>0){
            StringBuffer stateSb = new StringBuffer();
            for (String state : states) {
                stateSb.append(",").append(state);
            }
            sql.append(" AND t.STATE_MARK IN (").append(stateSb.substring(1)).append(")");
        }
        if(checkType==2){
            sql.append(" AND t.STATE_MARK != 0");
        }


        StringBuffer searchSql = new StringBuffer();
        searchSql.append(" select t.rid,case  when t1.ZONE_TYPE > 3 then  substr(t1.FULL_NAME, instr(t1.full_name, '_', 1, t1.ZONE_TYPE - 3) + 1) else t1.zone_name end,t2.unitname tjName,t.WARN_TYPE,t.OTR_PSNS,t.happen_date ,t.IF_OUT_RANGE");
        searchSql.append(" ,t.CHECK_DATE check1,t.deal_date,t3.unitname czName,t.check_date check2,'' days,t.state_mark ").append(sql);
        searchSql.append(" order by t.happen_date ,t1.zone_gb,t3.unitname");
        if(checkType ==2){
            searchSql.append(",t.deal_date");
        }
        StringBuffer countSql = new StringBuffer();
        countSql.append("select count(*) ").append(sql);
        return new String[] { searchSql.toString(), countSql.toString() };
    }


    @Override
    public void processData(List<?> list) {
        if(!CollectionUtils.isEmpty(list)){
                List<Object[]> result = (List<Object[]>) list;
                for (Object[] obj : result) {
                    if(null == obj[12]){
                        continue;
                    }
                    if(checkType==1){
                        if (null == obj[5]) {
                            continue;
                        }
                        Date date = new Date();
                        if("2".equals(obj[12].toString())){
                            date = DateUtils.parseDate(obj[7]);
                        }else{
                            date = DateUtils.parseDate(obj[5]);
                        }

                        int a =HolidayUtil.calRemainingDate(date,new Date(),limitTime);
                        obj[11] =a;
                    }else{
                        if (null == obj[8]) {
                            continue;
                        }
                        Date dealDate = DateUtils.parseDate(obj[8]);
                        int a =HolidayUtil.calRemainingDate(dealDate,new Date(),limitTime);
                        obj[11] =a;
                    }

                }
        }
    }

    /**
     * @MethodName: packageDatas
     * @Description: 文书封装
     * @Param: []
     * @Return: java.util.List<com.chis.common.bean.FastReportData>
     * @Author: maox
     * @Date: 2020-11-19
    **/
    private List<FastReportData> packageDatas() {
        OverRangeVo overRangeVo = new OverRangeVo();
        overRangeVo.setNoticeUnitName(tdZwyjOtrDealWrit.getNoticeUnit());
        overRangeVo.setWritContent(tdZwyjOtrDealWrit.getWritContent());
        overRangeVo.setDealUnitName(tdZwyjOtrDealWrit.getDealUnit());
        overRangeVo.setWritDate(DateUtils.formatDate(tdZwyjOtrDealWrit.getWritDate()));

        List<OverRangeRecordVo> overRangeRecordVos  = new ArrayList<>();
        if(!CollectionUtils.isEmpty(tdZwyjOtrCrptList) && null != tdZwyjOtrWarn.getWarnType() && tdZwyjOtrWarn.getWarnType().intValue()==2){
            for(TdZwyjOtrCrptList crptList:tdZwyjOtrCrptList){
                OverRangeRecordVo overRangeRecordVo = new OverRangeRecordVo();
                overRangeRecordVo.setZoneName(crptList.getFkByCrptZoneId().getFullName());
                overRangeRecordVo.setUnitName(crptList.getCrptName());
                overRangeRecordVo.setCreitCode(crptList.getCreditCode());
                overRangeRecordVo.setPsnNum(crptList.getOtrPsns().toString());
                overRangeRecordVo.setBhkDate(DateUtils.getChineseStringDate(crptList.getBeginBhkDate())+"~"+DateUtils.getChineseStringDate(crptList.getEndBhkDate()));
                overRangeRecordVo.setItemNames(crptList.getItems());
                overRangeRecordVos.add(overRangeRecordVo);
            }

        }
        List<OverRangeRecordVoForZone> overRangeRecordVoZones  = new ArrayList<>();
        if(!CollectionUtils.isEmpty(tdZwyjOtrCrptList) && null != tdZwyjOtrWarn.getWarnType() && tdZwyjOtrWarn.getWarnType().intValue()==1){
            for(TdZwyjOtrCrptList crptList:tdZwyjOtrCrptList){
                OverRangeRecordVoForZone overRangeRecordVoForZone = new OverRangeRecordVoForZone();
                overRangeRecordVoForZone.setZoneName(crptList.getFkByCrptZoneId().getFullName());
                overRangeRecordVoForZone.setUnitName(crptList.getCrptName());
                overRangeRecordVoForZone.setCreitCode(crptList.getCreditCode());
                overRangeRecordVoForZone.setPsnNum(crptList.getOtrPsns().toString());
                overRangeRecordVoForZone.setBhkDate(DateUtils.getChineseStringDate(crptList.getBeginBhkDate())+"~"+DateUtils.getChineseStringDate(crptList.getEndBhkDate()));
                overRangeRecordVoZones.add(overRangeRecordVoForZone);
            }

        }
        List<OverRangeVo> overRangeVos = new ArrayList<>();
        overRangeVos.add(overRangeVo);
        List<FastReportData> rptDataList = new ArrayList<>();
        rptDataList.add(new FastReportData<>(OverRangeVo.class,"overRangeVo", overRangeVos));
        rptDataList.add(new FastReportData<>(OverRangeRecordVo.class,"overRangeRecordVos", overRangeRecordVos));
        rptDataList.add(new FastReportData<>(OverRangeRecordVoForZone.class,"overRangeRecordVoZones", overRangeRecordVoZones));
        return rptDataList;
    }

    public Integer getCheckType() {
        return checkType;
    }

    public void setCheckType(Integer checkType) {
        this.checkType = checkType;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public Integer getWarnType() {
        return warnType;
    }

    public void setWarnType(Integer warnType) {
        this.warnType = warnType;
    }

    public Date getHappenStartDate() {
        return happenStartDate;
    }

    public void setHappenStartDate(Date happenStartDate) {
        this.happenStartDate = happenStartDate;
    }

    public Date getHappenEndDate() {
        return happenEndDate;
    }

    public void setHappenEndDate(Date happenEndDate) {
        this.happenEndDate = happenEndDate;
    }

    public Date getDealStartDate() {
        return dealStartDate;
    }

    public void setDealStartDate(Date dealStartDate) {
        this.dealStartDate = dealStartDate;
    }

    public Date getDealEndDate() {
        return dealEndDate;
    }

    public void setDealEndDate(Date dealEndDate) {
        this.dealEndDate = dealEndDate;
    }

    public Date getCheckStartDate() {
        return checkStartDate;
    }

    public void setCheckStartDate(Date checkStartDate) {
        this.checkStartDate = checkStartDate;
    }

    public Date getCheckEndDate() {
        return checkEndDate;
    }

    public void setCheckEndDate(Date checkEndDate) {
        this.checkEndDate = checkEndDate;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwyjOtrWarn getTdZwyjOtrWarn() {
        return tdZwyjOtrWarn;
    }

    public void setTdZwyjOtrWarn(TdZwyjOtrWarn tdZwyjOtrWarn) {
        this.tdZwyjOtrWarn = tdZwyjOtrWarn;
    }

    public String getLimitTime() {
        return limitTime;
    }

    public void setLimitTime(String limitTime) {
        this.limitTime = limitTime;
    }

    public String[] getOutRanges() {
        return outRanges;
    }

    public void setOutRanges(String[] outRanges) {
        this.outRanges = outRanges;
    }

    public TdZwyjOtrDealWrit getTdZwyjOtrDealWrit() {
        return tdZwyjOtrDealWrit;
    }

    public void setTdZwyjOtrDealWrit(TdZwyjOtrDealWrit tdZwyjOtrDealWrit) {
        this.tdZwyjOtrDealWrit = tdZwyjOtrDealWrit;
    }

    public boolean isIfZzOrg() {
        return ifZzOrg;
    }

    public void setIfZzOrg(boolean ifZzOrg) {
        this.ifZzOrg = ifZzOrg;
    }

    public TdZwTjorginfoComm getTdZwTjorginfoComm() {
        return tdZwTjorginfoComm;
    }

    public void setTdZwTjorginfoComm(TdZwTjorginfoComm tdZwTjorginfoComm) {
        this.tdZwTjorginfoComm = tdZwTjorginfoComm;
    }

    public TsUnit getTsUnit() {
        return tsUnit;
    }

    public void setTsUnit(TsUnit tsUnit) {
        this.tsUnit = tsUnit;
    }

    public Map<Integer, TsZone> getRecordZoneMap() {
        return recordZoneMap;
    }

    public void setRecordZoneMap(Map<Integer, TsZone> recordZoneMap) {
        this.recordZoneMap = recordZoneMap;
    }

    public List<TdZwTjorgRecordComm> getTjorgRecords() {
        return tjorgRecords;
    }

    public void setTjorgRecords(List<TdZwTjorgRecordComm> tjorgRecords) {
        this.tjorgRecords = tjorgRecords;
    }

    public String getManagerZone() {
        return managerZone;
    }

    public void setManagerZone(String managerZone) {
        this.managerZone = managerZone;
    }

    public List<TdZwyjOtrCrptList> getTdZwyjOtrCrptList() {
        return tdZwyjOtrCrptList;
    }

    public void setTdZwyjOtrCrptList(List<TdZwyjOtrCrptList> tdZwyjOtrCrptList) {
        this.tdZwyjOtrCrptList = tdZwyjOtrCrptList;
    }

    public TdZwyjOtrCrptList getTdZwyjOtrCrpt() {
        return tdZwyjOtrCrpt;
    }

    public void setTdZwyjOtrCrpt(TdZwyjOtrCrptList tdZwyjOtrCrpt) {
        this.tdZwyjOtrCrpt = tdZwyjOtrCrpt;
    }

    public List<TdZwyjOtrBhk> getPersonList() {
        return personList;
    }

    public void setPersonList(List<TdZwyjOtrBhk> personList) {
        this.personList = personList;
    }

    public Integer getCrptId() {
        return crptId;
    }

    public void setCrptId(Integer crptId) {
        this.crptId = crptId;
    }

    public TsRpt getTsRpt() {
        return tsRpt;
    }

    public void setTsRpt(TsRpt tsRpt) {
        this.tsRpt = tsRpt;
    }

    public boolean isIfdesign() {
        return ifdesign;
    }

    public void setIfdesign(boolean ifdesign) {
        this.ifdesign = ifdesign;
    }

    @Override
    public FastReportBean getFastReportBean() {
        return fastReportBean;
    }

    @Override
    public List<FastReportData> supportFastReportDataSet() {
        return packageDatas();
    }

    @Override
    public List<FastReportDataRef> supportFastReportDataRef() {
        return null;
    }

    public void setFastReportBean(FastReportBean fastReportBean) {
        this.fastReportBean = fastReportBean;
    }

    public boolean isIfZzba() {
        return ifZzba;
    }

    public void setIfZzba(boolean ifZzba) {
        this.ifZzba = ifZzba;
    }

    public TdZwTjorginfoComm getTdZwTjorginfoDate() {
        return tdZwTjorginfoDate;
    }

    public void setTdZwTjorginfoDate(TdZwTjorginfoComm tdZwTjorginfoDate) {
        this.tdZwTjorginfoDate = tdZwTjorginfoDate;
    }
}
