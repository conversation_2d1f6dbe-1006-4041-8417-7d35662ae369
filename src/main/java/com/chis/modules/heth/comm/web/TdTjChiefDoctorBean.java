package com.chis.modules.heth.comm.web;

import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TdTjBhkClt;
import com.chis.modules.heth.comm.entity.TdTjChiefDoctor;
import com.chis.modules.heth.comm.entity.TdZwPsninfoComm;
import com.chis.modules.heth.comm.service.TdTjBhkCltCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.NodeUnselectEvent;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;
import org.springframework.util.CollectionUtils;

import java.util.*;


/**
 * @Description : 主检医师
 * @ClassAuthor : anjing
 * @Date : 2021/3/25 14:10
 **/
public class TdTjChiefDoctorBean extends FacesEditBean {

    /**关联：体检信息（录入数据）*/
    private TdTjBhkClt fkByBhkId;
    /**主检医师树*/
    private TreeNode chiefDoctorSortTree;
    /**主检医师-下拉集合*/
    private List<TdZwPsninfoComm> chiefDoctorList = new ArrayList<>();
    private String chiefDoctor;

    private TdTjBhkCltCommServiceImpl service = SpringContextHolder.getBean(TdTjBhkCltCommServiceImpl.class);

    public TdTjChiefDoctorBean() {
    }

    /**
    * @Description : 参数初始化
    * @MethodAuthor: anjing
    * @Date : 2021/3/25 14:14
    **/
    public void initParam() {
        if(null != this.fkByBhkId && null != this.fkByBhkId.getRid()) {
            this.initChiefDoctorTree();
        }
    }

    /**
    * @Description : 主检医师-树初始化
    * @MethodAuthor: anjing
    * @Date : 2021/3/25 14:15
    **/
    public void initChiefDoctorTree() {
        List<TdZwPsninfoComm> list = this.service.getTdZwPsninfoList("'2'");
        this.chiefDoctorSortTree = new CheckboxTreeNode("root", null);
        if(null != list && list.size() > 0) {
            for(TdZwPsninfoComm t : list) {
                new CheckboxTreeNode(t, this.chiefDoctorSortTree);
            }
        }
        List<TdTjChiefDoctor> tdTjChiefDoctorList = this.service.getTdTjChiefDoctorListByBhkId(this.fkByBhkId.getRid());
        if(!CollectionUtils.isEmpty(tdTjChiefDoctorList)) {
            StringBuilder name = new StringBuilder();
            StringBuilder idSb = new StringBuilder();
            for(TdTjChiefDoctor tdTjChiefDoctor : tdTjChiefDoctorList) {
                name.append("，").append(tdTjChiefDoctor.getFkByMhkdctId().getEmpName());
                idSb.append(",").append(tdTjChiefDoctor.getFkByMhkdctId().getRid());
            }
            this.chiefDoctor = name.deleteCharAt(0).toString();
            String selectIds = idSb.deleteCharAt(0).toString();
            List<TreeNode> children = this.chiefDoctorSortTree.getChildren();
            List<TreeNode> selectNodes = new ArrayList<>();
            for(TreeNode node : children) {
                TdZwPsninfoComm data = (TdZwPsninfoComm) node.getData();
                String ids = "," + selectIds + ",";
                if(ids.contains("," + data.getRid() + ",")) {
                    node.setSelected(true);
                    selectNodes.add(node);
                }else {
                    node.setSelected(false);
                }
            }
        }
    }

    /**
     * 参数初始化(根据传参)
     **/
    public void initParam(List<TdZwPsninfoComm> psninfoCommList) {
        this.chiefDoctorList.clear();
        List<TdTjChiefDoctor> tdTjChiefDoctorList = this.service.getTdTjChiefDoctorListByBhkId(this.fkByBhkId.getRid());
        if (CollectionUtils.isEmpty(tdTjChiefDoctorList)) {
            tdTjChiefDoctorList = new ArrayList<>();
        }
        for (TdTjChiefDoctor tdTjChiefDoctor : tdTjChiefDoctorList) {
            this.chiefDoctorList.add(tdTjChiefDoctor.getFkByMhkdctId());
        }
        if (CollectionUtils.isEmpty(this.chiefDoctorList)) {
            this.chiefDoctorList.addAll(psninfoCommList);
        }
        this.initChiefDoctorTree(this.chiefDoctorList);
    }

    /**
     * 主检医师-树初始化(根据传参)
     **/
    public void initChiefDoctorTree(List<TdZwPsninfoComm> psninfoCommList) {
        List<TdZwPsninfoComm> list = this.service.getTdZwPsninfoList("'2'");
        this.chiefDoctorSortTree = new CheckboxTreeNode("root", null);
        if (!CollectionUtils.isEmpty(list)) {
            for (TdZwPsninfoComm t : list) {
                new CheckboxTreeNode(t, this.chiefDoctorSortTree);
            }
        }
        if (CollectionUtils.isEmpty(psninfoCommList)) {
            return;
        }
        List<String> empNameList = new ArrayList<>();
        List<String> empIdList = new ArrayList<>();
        for (TdZwPsninfoComm psninfoComm : psninfoCommList) {
            empNameList.add(psninfoComm.getEmpName());
            empIdList.add(StringUtils.objectToString(psninfoComm.getRid()));
        }
        this.chiefDoctor = StringUtils.list2string(empNameList, "，");
        List<TreeNode> children = this.chiefDoctorSortTree.getChildren();
        for (TreeNode node : children) {
            TdZwPsninfoComm data = (TdZwPsninfoComm) node.getData();
            node.setSelected(empIdList.contains(StringUtils.objectToString(data.getRid())));
        }
    }

    /**
    * @Description : 节点选择事件
    * @MethodAuthor: anjing
    * @Date : 2021/3/25 14:26
    **/
    public void onChiefDoctorNodeSelect(NodeSelectEvent event){
        TreeNode selectNode = event.getTreeNode();
        selectNode.setSelected(true);
        if(null != selectNode) {
            TdZwPsninfoComm t = (TdZwPsninfoComm)selectNode.getData();
            this.chiefDoctorList.add(t);
        }
    }

    /**
    * @Description : 节点失去选择事件
    * @MethodAuthor: anjing
    * @Date : 2021/3/25 14:33
    **/
    public void onChiefDoctorNodeNoSelect(NodeUnselectEvent event) {
        TreeNode selectNode = event.getTreeNode();
        selectNode.setSelected(false);
        if(null != selectNode) {
            TdZwPsninfoComm t = (TdZwPsninfoComm)selectNode.getData();
            this.chiefDoctorList.remove(t);
        }
    }

    /**
    * @Description : 隐藏事件
    * @MethodAuthor: anjing
    * @Date : 2021/3/25 14:27
    **/
    public void hideChiefDoctorAction() {
        this.chiefDoctor = "";
        StringBuffer buffer = new StringBuffer();
        if(!CollectionUtils.isEmpty(this.chiefDoctorList)) {
            for(TdZwPsninfoComm t : this.chiefDoctorList) {
                buffer.append("，").append(t.getEmpName());
            }
            this.chiefDoctor = buffer.deleteCharAt(0).toString();
        }
        RequestContext context = RequestContext.getCurrentInstance();
        context.update("tabView:editTabView:mhkrstListForm:chiefDoctorGrid");
    }

    @Override
    public String[] buildHqls() {
        return new String[0];
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    public TdTjBhkClt getFkByBhkId() {
        return fkByBhkId;
    }

    public void setFkByBhkId(TdTjBhkClt fkByBhkId) {
        this.fkByBhkId = fkByBhkId;
    }

    public TreeNode getChiefDoctorSortTree() {
        return chiefDoctorSortTree;
    }

    public void setChiefDoctorSortTree(TreeNode chiefDoctorSortTree) {
        this.chiefDoctorSortTree = chiefDoctorSortTree;
    }

    public List<TdZwPsninfoComm> getChiefDoctorList() {
        return chiefDoctorList;
    }

    public void setChiefDoctorList(List<TdZwPsninfoComm> chiefDoctorList) {
        this.chiefDoctorList = chiefDoctorList;
    }

    public String getChiefDoctor() {
        return chiefDoctor;
    }

    public void setChiefDoctor(String chiefDoctor) {
        this.chiefDoctor = chiefDoctor;
    }
}
