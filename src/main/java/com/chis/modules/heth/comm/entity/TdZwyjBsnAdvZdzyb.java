package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-10-29
 */
@Entity
@Table(name = "TD_ZWYJ_BSN_ADV_ZDZYB")
@SequenceGenerator(name = "TdZwyjBsnAdvZdzyb", sequenceName = "TD_ZWYJ_BSN_ADV_ZDZYB_SEQ", allocationSize = 1)
public class TdZwyjBsnAdvZdzyb implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwyjBsnBhk fkByBhkId;
	private TdZwyjBsnCsionPdjl fkByAdvId;
	private TbTjItems fkByItemId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwyjBsnAdvZdzyb() {
	}

	public TdZwyjBsnAdvZdzyb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwyjBsnAdvZdzyb")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "BHK_ID")			
	public TdZwyjBsnBhk getFkByBhkId() {
		return fkByBhkId;
	}

	public void setFkByBhkId(TdZwyjBsnBhk fkByBhkId) {
		this.fkByBhkId = fkByBhkId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ADV_ID")			
	public TdZwyjBsnCsionPdjl getFkByAdvId() {
		return fkByAdvId;
	}

	public void setFkByAdvId(TdZwyjBsnCsionPdjl fkByAdvId) {
		this.fkByAdvId = fkByAdvId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_ID")			
	public TbTjItems getFkByItemId() {
		return fkByItemId;
	}

	public void setFkByItemId(TbTjItems fkByItemId) {
		this.fkByItemId = fkByItemId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}