package com.chis.modules.heth.comm.plugin;

import java.util.LinkedHashSet;
import java.util.Set;

import com.chis.modules.heth.comm.enumn.HethRptEnum;
import com.chis.modules.system.entity.TbZwWritsort;
import com.chis.modules.system.enumn.WritTypeEnum;

/**
 * <p>类描述：文书类型</p>
 * @ClassAuthor qrr,2020年12月30日,HethWritSortPluginObj
 * */
public class HethWritSortPluginObj {
	public static Set<TbZwWritsort> writSortSet;

	static {
		//接诊登记
		writSortSet = new LinkedHashSet<>();
		writSortSet.add(new TbZwWritsort(null, HethRptEnum.YSZYBBGK, null));
		//职业性有害因素监测卡
		writSortSet.add(new TbZwWritsort(3, WritTypeEnum.ZYBYHYSJC, ""));
		//职业病鉴定报告卡
		writSortSet.add(new TbZwWritsort(3, WritTypeEnum.ZYBJDBG, ""));
	}
}
