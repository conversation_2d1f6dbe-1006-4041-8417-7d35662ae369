package com.chis.modules.heth.comm.web;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.common.utils.ObjectUtil;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.service.TbTjItemsGjCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.service.HethBaseCommServiceImpl;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.web.FacesEditBean;

/**
 * 体检项目维护
 * 
 * <AUTHOR>
 * @createDate 2014年9月12日 下午3:45
 * @LastModify wjh
 * @ModifyDate 2014年9月12日 下午3:45
 */
@ManagedBean(name = "tbTjStadItemsStatusListBean")
@ViewScoped
public class TbTjStadItemsStatusListBean extends FacesEditBean {

	private static final long serialVersionUID = 1L;
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private HethBaseCommServiceImpl iHethBaseService = (HethBaseCommServiceImpl) SpringContextHolder
			.getBean(HethBaseCommServiceImpl.class);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	private TbTjItemsGjCommServiceImpl itemsGjCommService = (TbTjItemsGjCommServiceImpl) SpringContextHolder.getBean(TbTjItemsGjCommServiceImpl.class);
	//状态
	private Integer state;
	//所有定量项目数量
	private Map<Integer,Object[]> map;
	//单位Id
	private Integer unitId;
	//页面列表
	private List<Object[]> resultList;
	//结构配置
	private TbTjStadItemsStatus stadItemStatus;
	
	/**查询条件：体检项目*/
    private String searchItemName;
    private String searchItemId;
    private List<TbTjItems> selectItems = new ArrayList<>();

    /** 定量 未删除的体检项目标准库 */
    private List<TbTjItems> effectiveItemList;
	/** 定量 未删除的体检项目标准库Map */
    private Map<Integer,TbTjItems> tjItemsMap = new HashMap<>();
    /** 体检项目标准库与计量单位关系配置Map */
    private Map<Integer,List<TbTjItemUnitRel>> itemUnitRelMap = new HashMap<>();
    /**
	 * 性别为通用的体检项目标准库与在岗状态和有害因素为空的特殊标准Map
	 * 通用项目 如果选的是定量 是否分男女 就是通过的这个Map 并且 计量单位由这个Map的值控制
	 */
    private Map<Integer,List<TbTjItemsSpe>> itemSpeMap = new HashMap<>();

    /** 存在定性项目描述的项目id Map */
    private Map<Integer,Integer> rstDescItemMap = new HashMap<>();
    /** 缓存 */
	private Map<Integer,List<Object[]>> cacheMap;
	private Map<Integer,String> unitMap;
	/**缓存 国家接口标准数据 key:体检项目rid+类型   value:实体 */
	private Map<String,List<TbTjItemsGj>>  ItemsGjMap=new HashMap<>();

	public TbTjStadItemsStatusListBean(){
		this.ifSQL = true;
		init();
		initItemsGj();
		searchAction();
		cacheMap = new HashMap<>();
		if(null != resultList && resultList.size() >0){
			for(Object[] orr:resultList){
				List<Object[]> tmpList = cacheMap.get(Integer.parseInt(orr[8].toString()));
				if(null == tmpList){
					tmpList = new ArrayList<>();
				}
				//初始化的数据 不需要考虑是否有重复的
				tmpList.add(copyObjectArr(orr));
				cacheMap.put(Integer.parseInt(orr[8].toString()),tmpList);
			}
		}
	}
	
	/***
	 *  <p>方法描述：初始化参数</p>
     *
     * @MethodAuthor maox,2020年4月26日,init
	 *
	 * <p>
	 *       修订内容: 判定模式与单位可选择，男女标准，初始化调整
	 * </p>
	 *
	 * @MethodReviser pw,2020年11月11日,init
	 */
	public  void init(){
		unitId = sessionData.getUser().getTsUnit().getRid();
		stadItemStatus = iHethBaseService.findTbTjStadItemsStatus(unitId);
		if(null ==stadItemStatus){
			stadItemStatus = new TbTjStadItemsStatus();
			stadItemStatus.setFkByOrgId(new TsUnit(unitId));
			stadItemStatus.setState(0);
			stadItemStatus.setCreateDate(new Date());
			stadItemStatus.setCreateManid(1);
		}
		state = stadItemStatus.getState();
		unitMap = new HashMap<>();
		List<TsSimpleCode> unitList = commService.findallSimpleCodesByTypeIdOrderByNum("5501");
		if(null != unitList && unitList.size() > 0){
			for(TsSimpleCode tmpUnit : unitList){
				unitMap.put(tmpUnit.getRid(), tmpUnit.getCodeName());
			}
		}
		initEffectiveItemListFillMapBySql();
	}

	
	/**
	 * <p>方法描述：初始化缓存国家接口标准</p>
	 * @MethodAuthor： yzz
	 * @Date：2022-09-17
	 **/
	public void initItemsGj(){
		ItemsGjMap=new HashMap<>();
		List<TbTjItemsGj> itemsGjList= itemsGjCommService.findItemsGjByHql();
		if(!CollectionUtils.isEmpty(itemsGjList)){
			for (TbTjItemsGj tbTjItemsGj : itemsGjList) {
				if(tbTjItemsGj.getType()==1){
					if(!ItemsGjMap.containsKey(tbTjItemsGj.getFkByItemId().getRid().toString())){
						List<TbTjItemsGj> newItemsGjList=new ArrayList<>();
						newItemsGjList.add(tbTjItemsGj);
						ItemsGjMap.put(tbTjItemsGj.getFkByItemId().getRid().toString(),newItemsGjList);
					}else{
						ItemsGjMap.get(tbTjItemsGj.getFkByItemId().getRid().toString()).add(tbTjItemsGj);
					}
				}
			}
		}
	}
	
	public void searchAction(){
		//刷新基础数据
		initEffectiveItemListFillMapBySql();
		Date date = new Date();
		resultList = new ArrayList<>();
		List<Object[]> itemResultList = iHethBaseService.findStadItemList(unitId,searchItemId);
		//对查询出的数据 进行map封装
		Map<Integer,List<Object[]>> tmpMap = null;
		if(null != itemResultList && itemResultList.size() > 0){
			tmpMap = new HashMap<>();
			for(Object[] obj : itemResultList){
				if(null == obj[0] || null == obj[1]){// rid 与 项目id不为空
					continue;
				}
				List<Object[]> tmpList = tmpMap.get(Integer.parseInt(obj[1].toString()));
				if(null == tmpList){
					tmpList = new ArrayList<>();
				}
				tmpList.add(obj);
				tmpMap.put(Integer.parseInt(obj[1].toString()), tmpList);
			}
		}
		List<TbTjItems> tbTjItems = new ArrayList<>();
		if(StringUtils.isNotBlank(searchItemId)){
			String[] searchArr = searchItemId.split(",");
			for(TbTjItems itm : effectiveItemList){
				if(!searchItemId.contains(itm.getRid()+"")){
					continue;
				}
				for(String itmStr : searchArr){
					if(itmStr.trim().equals(itm.getRid()+"")){
						tbTjItems.add(itm);
						break;
					}
				}
			}
		}else{
			tbTjItems = effectiveItemList;
		}
		if(null != tbTjItems && tbTjItems.size() > 0){
			for(TbTjItems itm : tbTjItems){
				if(null != tmpMap && !tmpMap.isEmpty() && null != tmpMap.get(itm.getRid())){
					//存储的数据
					fillResultList(tmpMap.get(itm.getRid()), itm);
				}else{
					// 默认值
					fillResultList(null, itm);
				}
			}
		}

		fillRowspan();
		System.out.println("标准值配置数据组合时间："+(new Date().getTime() - date.getTime())+"毫秒");
	}

	
	public void selItemCodeAction() {
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("contentWidth", 650);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add(this.searchItemId);
        paramMap.put("selectItemIds", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/tbTjItemsSelectList", options, paramMap);
    }
	
	
	 public void onItemCodeAction(SelectEvent event) {
	        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
	        if (null != selectedMap && selectedMap.size() > 0) {
	            List<TbTjItems> list = (List<TbTjItems>) selectedMap.get("selectPros");
	            if(!CollectionUtils.isEmpty(list)) {
	                StringBuffer names = new StringBuffer();
	                StringBuffer ids = new StringBuffer();
	                for(TbTjItems t : list) {
	                    names.append("，").append(t.getItemName());
	                    ids.append(",").append(t.getRid());
	                    this.selectItems.add(t);
	                }
	                this.searchItemId = ids.substring(1);
	                this.searchItemName = names.substring(1);
	            }
	        }
	 }
	 
   public void clearItemCode() {
        this.searchItemId = null;
        this.searchItemName = null;
        this.selectItems = new ArrayList<>();
    }
   
   /***
    *  <p>方法描述：提交数据</p>
     *
     * @MethodAuthor maox,2020年4月26日,submitAction
    */
   public void submitAction(){
       Integer audioModel = null;
       if(!CollectionUtils.isEmpty(this.resultList)) {
           for(Object[] orr : resultList) {
               if(null != orr[15] && "1".equals(orr[15].toString())) {
                   if(null == orr[16]) {
                       JsfUtil.addErrorMessage("请选择电测听结果的录入模式！");
                       return;
                   }
				   audioModel = Integer.valueOf(orr[16].toString());
				   break;
			   }
           }
       }
	   boolean flag = flagResult(true);
	   if(flag){
		   stadItemStatus.setState(1);
           stadItemStatus.setAudioModel(audioModel);
		   if(null != stadItemStatus.getRid()){
			   stadItemStatus.setModifyDate(new Date());
			   stadItemStatus.setModifyManid(Global.getUser().getRid());
		   }
		   iHethBaseService.saveStadItemStatus(stadItemStatus);
		   JsfUtil.addSuccessMessage("提交成功！");
		   state = 1;
		   searchAction();
	   }
   }
   
   /***
    *  <p>方法描述：撤销数据</p>
     *
     * @MethodAuthor maox,2020年4月26日,cancleAction
    */
   public void cancleAction(){
	   state = 0;
	   iHethBaseService.cancleStadItemStatus(stadItemStatus);
	   stadItemStatus.setState(state);//撤销成功后 调整状态
	   searchAction();
	   JsfUtil.addSuccessMessage("撤销成功！");
   }
	 
	@Override
	public void addInit() {
	}

	@Override
	public void viewInit() {
	}

	@Override
	public void modInit() {
		
	}

	@Override
	public void saveAction() {
       Integer audioModel = null;
       if(!CollectionUtils.isEmpty(this.resultList)) {
           for(Object[] orr : resultList) {
               if(null != orr[15] && "1".equals(orr[15].toString())) {
                   if(null == orr[16]) {
                       JsfUtil.addErrorMessage("请选择电测听结果的录入模式！");
                       return;
                   }
                   if(null != orr[16]) {
                       audioModel = Integer.valueOf(orr[16].toString());
                       break;
                   }
               }
           }
       }
		boolean flag = flagResult(false);
		if(flag){
			stadItemStatus.setState(0);
            stadItemStatus.setAudioModel(audioModel);
			iHethBaseService.saveStadItemStatus(stadItemStatus);
			JsfUtil.addSuccessMessage("保存成功！");
			state = 0;
			searchAction();
		}
	}

	/** 调整单位 */
	public void changeUnitRel(Object[] tmpObj){
		if(null != tmpObj && null != resultList && resultList.size() > 0
				&& resultList.contains(tmpObj)){
			for(Object[] obj : resultList){
				if(tmpObj.equals(obj)){
					TbTjItems tjItems = tjItemsMap.get(Integer.parseInt(tmpObj[8].toString()));
					if(null == itemSpeMap.get(tjItems.getRid())){
						List<TbTjItemUnitRel> unitRelList = itemUnitRelMap.get(tjItems.getRid());
						if(null != unitRelList && unitRelList.size() > 0){
							for(TbTjItemUnitRel rel : unitRelList){
								if(Integer.parseInt(tmpObj[2].toString()) == rel.getFkByMsruntId().getRid()){
									tmpObj[3] = rel.getMinval();
									tmpObj[4] = rel.getMaxval();
								}
							}
						}
					}else{
						List<TbTjItemsSpe> speList = itemSpeMap.get(tjItems.getRid());
						if(null != speList && speList.size() > 0){
							for(TbTjItemsSpe spe : speList){
								if(Integer.parseInt(spe.getMsrunt()) == Integer.parseInt(tmpObj[2].toString()) &&
										Integer.parseInt(tmpObj[10].toString()) == Integer.parseInt(spe.getSex())){
									tmpObj[3] = spe.getMinval();
									tmpObj[4] = spe.getMaxval();
								}
							}
						}
					}
				}
			}
		}
	}

	/** 调整判定模式 */
	public void changeJdgptn(Object[] tmpObj){
		if(null != tmpObj && null != resultList && resultList.size() > 0
				&& resultList.contains(tmpObj)){
			List<Object[]> list = new ArrayList<>();
			boolean flag = false;
			for(Object[] obj : resultList){
				if(flag && tmpObj[8] == obj[8]){
					continue;
				}
				if(tmpObj.equals(obj)){
					if(1 == Integer.parseInt(tmpObj[9].toString())){
						tmpObj[12]=1;//需要将合并变成1 不是2
						list.add(tmpObj);
						flag = true;
					}else{
						TbTjItems tjItems = tjItemsMap.get(Integer.parseInt(tmpObj[8].toString()));
						//生成男女数据
						if(null == tjItems.getSex() && null != itemSpeMap.get(tjItems.getRid())){
							List<TbTjItemsSpe> speList = itemSpeMap.get(tjItems.getRid());
							boolean rowsp = true;
							for(TbTjItemsSpe spe : speList){
								if(tjItems.getMsruntId().intValue() == Integer.parseInt(spe.getMsrunt()) &&
										null != spe.getSex()){
									Object[] objArr = fillResultObjectArr(tjItems,
											2,
											Integer.parseInt(spe.getSex()),
											tjItems.getMsruntId().intValue(),
											spe.getMinval(),
											spe.getMaxval(),
											null,
											false);
									if(rowsp){
										objArr[12] = 2;
										rowsp = false;
									}else{
										objArr[12] = 0;
									}
									list.add(objArr);
								}
							}
						}else{
							list.add(fillResultObjectArr(tjItems,
									2,
									null,
									null,
									null,
									null,
									null,
									true));
						}
					}
				}else{
					list.add(obj);
				}
			}
			resultList = list;
			fillRowspan();
		}
	}

	
	@Override
	public String[] buildHqls() {
		return null;
	}

	/** 初始化定量未删除的体检项目标准库 并且填充itemUnitRelMap 与 itemSpeMap  tjItemsMap */
	private void initEffectiveItemListFillMapBySql(){
		Date date = new Date();
		List<Object[]> itemsObjects = iHethBaseService.findTbTjBaseItemsSQLList();
		if(null == itemsObjects || itemsObjects.size() == 0){
			return;
		}
		effectiveItemList = new ArrayList<>();
		tjItemsMap.clear();
		StringBuilder itemIds = new StringBuilder("");
		for(Object[] objArray : itemsObjects){
			TbTjItems itemTmp = fillItemsByObjectArray(objArray);
			if(null != itemTmp){
				effectiveItemList.add(itemTmp);
				itemIds.append(",");
				itemIds.append(itemTmp.getRid().intValue());
				tjItemsMap.put(itemTmp.getRid(),itemTmp);
			}
		}

		itemUnitRelMap.clear();
		List<Object[]> unitRelObjects = iHethBaseService.findUnitRelSQLList();
		if(null != unitRelObjects && unitRelObjects.size() > 0){
			for(Object[] objArray : unitRelObjects){
				TbTjItemUnitRel unitRelTmp = fillUnitRelByObjectArray(objArray);
				if(null != unitRelTmp){
					List<TbTjItemUnitRel> relList = itemUnitRelMap.get(unitRelTmp.getFkByItemId().getRid());
					if(null == relList){
						relList = new ArrayList<>();
					}
					relList.add(unitRelTmp);
					itemUnitRelMap.put(unitRelTmp.getFkByItemId().getRid(), relList);
				}
			}
		}

		itemSpeMap.clear();
		if(itemIds.length() > 0){
			List<Object[]> speObjects = iHethBaseService.findItemsSpeSQLList(itemIds.substring(1));
			if(null != speObjects && speObjects.size() > 0){
				for(Object[] objArray : speObjects){
					TbTjItemsSpe speTmp = fillSpeByObjectArray(objArray);
					if(null != speTmp){
						List<TbTjItemsSpe> itemsSpeList = itemSpeMap.get(speTmp.getFkByItemId().getRid());
						if(null == itemsSpeList){
							itemsSpeList = new ArrayList<>();
						}
						itemsSpeList.add(speTmp);
						itemSpeMap.put(speTmp.getFkByItemId().getRid(),itemsSpeList);

						//通用标准的通用值 替换计量单位标准中的最大最小值
						if(null == speTmp.getSex()){
							List<TbTjItemUnitRel> relList = itemUnitRelMap.get(speTmp.getFkByItemId().getRid());
							if(null != relList && relList.size() > 0){
								for(TbTjItemUnitRel rel : relList){
									if(rel.getFkByMsruntId().getRid().intValue() == Integer.parseInt(speTmp.getMsrunt())){
										rel.setMaxval(speTmp.getMaxval());
										rel.setMinval(speTmp.getMinval());
										break;
									}
								}
								itemUnitRelMap.put(speTmp.getFkByItemId().getRid(), relList);
							}
						}
					}
				}
			}
		}

		rstDescItemMap.clear();
		List<Object> itemArr = iHethBaseService.findRstDescItemList();
		if(null != itemArr && itemArr.size() > 0){
			for(Object objArr : itemArr){
				if(null != objArr){
					int itemId = Integer.parseInt(objArr.toString());
					rstDescItemMap.put(itemId,itemId);
				}
			}
		}

		for(TbTjItems t : effectiveItemList){
			if(null != t.getSex()){//固定性别的不需要 只取通用
				continue;
			}
			List<TbTjItemsSpe> tmpSpeList = itemSpeMap.get(t.getRid());
			List<TbTjItemUnitRel> unitRelList = itemUnitRelMap.get(t.getRid());
			if(null != tmpSpeList && tmpSpeList.size() > 0 && null != unitRelList && unitRelList.size() > 0){
				Map<Integer,List<TbTjItemsSpe>> sexItemSpeMap = new HashMap<>();
				sexItemSpeMap.put(1,new ArrayList<TbTjItemsSpe>());
				sexItemSpeMap.put(2,new ArrayList<TbTjItemsSpe>());
				for(TbTjItemsSpe s : tmpSpeList){
					if(StringUtils.isBlank(s.getMsrunt()) || StringUtils.isBlank(s.getSex())){
						continue;
					}
					if(null != sexItemSpeMap.get(Integer.valueOf(s.getSex()))){
						sexItemSpeMap.get(Integer.valueOf(s.getSex())).add(s);
					}
				}

				for(TbTjItemUnitRel unitRel : unitRelList){
					boolean flag = true;
					for(TbTjItemsSpe s : sexItemSpeMap.get(1)){
						if(unitRel.getFkByMsruntId().getRid().intValue() == Integer.valueOf(s.getMsrunt())){
							flag = false;
							break;
						}
					}
					if(flag){
						TbTjItemsSpe tmp = new TbTjItemsSpe();
						tmp.setSex("1");
						tmp.setMaxval(unitRel.getMaxval());
						tmp.setMinval(unitRel.getMinval());
						tmp.setMsrunt(unitRel.getFkByMsruntId().getRid().toString());
						sexItemSpeMap.get(1).add(tmp);
					}

					flag = true;
					for(TbTjItemsSpe s : sexItemSpeMap.get(2)){
						if(unitRel.getFkByMsruntId().getRid().intValue() == Integer.valueOf(s.getMsrunt())){
							flag = false;
							break;
						}
					}
					if(flag){
						TbTjItemsSpe tmp = new TbTjItemsSpe();
						tmp.setSex("2");
						tmp.setMaxval(unitRel.getMaxval());
						tmp.setMinval(unitRel.getMinval());
						tmp.setMsrunt(unitRel.getFkByMsruntId().getRid().toString());
						sexItemSpeMap.get(2).add(tmp);
					}
				}
				List<TbTjItemsSpe> speFillList = new ArrayList<>();
				if(null != sexItemSpeMap.get(1) && sexItemSpeMap.get(1).size() > 0){
					speFillList.addAll(sexItemSpeMap.get(1));
				}
				if(null != sexItemSpeMap.get(2) && sexItemSpeMap.get(2).size() > 0){
					speFillList.addAll(sexItemSpeMap.get(2));
				}
				if(null != speFillList && speFillList.size() > 0){
					itemSpeMap.put(t.getRid(), speFillList);
				}
			}
		}
		System.out.println("标准值配置sql用时："+(new Date().getTime() - date.getTime())+"毫秒");
	}

	/** sql获取的数据 组合成计量单位关系配置对象 */
	public TbTjItemUnitRel fillUnitRelByObjectArray(Object[] arr){
		TbTjItemUnitRel rel = null;
		if(null != arr && null != arr[0] && null != arr[1] && null != arr[2] && null != arr[3]){
			rel = new TbTjItemUnitRel();
			rel.setFkByItemId(new TbTjItems(Integer.parseInt(arr[0].toString())));//项目rid ITEM_ID
			String codeName = unitMap.get(Integer.parseInt(arr[1].toString()));
			rel.setFkByMsruntId(new TsSimpleCode(Integer.parseInt(arr[1].toString()),codeName,null));//计量单位rid MSRUNT_ID
			rel.setMinval(new BigDecimal(arr[2].toString()));//最小值MINVAL
			rel.setMaxval(new BigDecimal(arr[3].toString()));//最大值MAXVAL
			rel.setIfDefaut(null == arr[4] ? 0 : Integer.parseInt(arr[4].toString()));//是否默认IF_DEFAUT 0否 1是
		}
		return rel;
	}

	/** sql获取的数据 组合成体检项目特殊标准对象 */
	public TbTjItemsSpe fillSpeByObjectArray(Object[] arr){
		TbTjItemsSpe spe = null;
		if(null != arr && null != arr[0] && null != arr[1] && null != arr[2] ){
			spe = new TbTjItemsSpe();
			spe.setFkByItemId(new TbTjItems(Integer.parseInt(arr[0].toString())));//项目rid ITEM_ID
			//MSRUNT_ID 直接赋值到 msrunt 取项目默认的单位id
			TbTjItems tjItems = tjItemsMap.get(Integer.parseInt(arr[0].toString()));
			if(null == tjItems){
				return null;
			}
			spe.setMsrunt(tjItems.getMsruntId().toString());//计量单位rid MSRUNT_ID
			spe.setMinval(new BigDecimal(arr[1].toString()));//最小值MINVAL
			spe.setMaxval(new BigDecimal(arr[2].toString()));//最大值MAXVAL
			spe.setSex(null == arr[3] ? null : arr[3].toString());//性别SEX 取的是数值字符串 1男 2女 通用的空
		}
		return spe;
	}

	/** sql获取的数据 组合成定量的未删除的体检项目标准库对象 */
	public TbTjItems fillItemsByObjectArray(Object[] arr){
		TbTjItems item = null;
		if(null != arr &&
				null != arr[2] && null != arr[3] && null != arr[4] && null != arr[5] && null != arr[6]){
			item = new TbTjItems();
			item.setRid(Integer.parseInt(arr[0].toString()));
			item.setSex(null == arr[1] ? null : Integer.parseInt(arr[1].toString()));// 性别1男 2女 SEX
			item.setItemCode(null == arr[2] ? null : arr[2].toString()); //项目编码ITEM_CODE
			item.setItemName(null == arr[3] ? null : arr[3].toString()); //项目名称ITEM_NAME
			item.setMsruntId(null == arr[4] ? null : Integer.parseInt(arr[4].toString())); //计量单位id MSRUNT_ID
			TsSimpleCode sortId = new TsSimpleCode();
			sortId.setRid(null == arr[5] ? null : Integer.parseInt(arr[5].toString()));//业务分类id ITEM_SORTID
			sortId.setCodeName(null == arr[6] ? null : arr[6].toString());//业务分类名称 CODE_NAME
            sortId.setExtendS1(null == arr[9] ? null : arr[9].toString());
			item.setTsSimpleCode(sortId);//业务分类
			item.setMinval(null == arr[7] ? null : new BigDecimal(arr[7].toString())); //最小值
			item.setMaxval(null == arr[8] ? null : new BigDecimal(arr[8].toString())); //最大值
		}
		return item;
	}

	/**
	 * 返回数据Object[]
	 * 0 业务分类名称code_name
	 * 1 项目名称item_name
	 * 2 计量单位名称msrunt 替换成MSRUNT_ID
	 * 3 最小值minval
	 * 4 最大值maxval
	 * 5 标准值配置rid
	 * 6 业务分类rid
	 * 7 业务分类合并行 默认1
	 * 8 项目rid
	 * 9 判断模式
	 * 10 性别
	 * 11 项目编码 item_code
	 * 12 性别合并行 默认1 最大2
	 * 13 定量定性下拉框是否显示 1 不显示 2显示 默认不显示
	 * 14 是否只有一个单位
	 * flag 是否初始化赋值
	 * @param itm
	 * @param jdgptn
	 * @param sex
	 * @param msruntId
	 * @param minval
	 * @param maxval
	 * @param rid
	 * @param flag
	 * @return
	 */
	private Object[] fillResultObjectArr(TbTjItems itm, Integer jdgptn, Integer sex, Integer msruntId,
										 BigDecimal minval, BigDecimal maxval,Integer rid,boolean flag){
		//注意 调整这里的数组长度 同样需要调整copyObjectArr的数组长度
		Object[] objArr = new Object[17];
		if(1 == jdgptn){
			sex = null;
			msruntId = null;
			minval = null;
			maxval = null;
		}else if(flag){
			sex = itm.getSex();
			msruntId = itm.getMsruntId();
			jdgptn = 2;
			minval = itm.getMinval();
			maxval = itm.getMaxval();
		}
		objArr[0] = itm.getTsSimpleCode().getCodeName();
		objArr[1] = itm.getItemName();
		objArr[2] = msruntId;
		objArr[3] = minval;
		objArr[4] = maxval;
		objArr[5] = rid;
		objArr[6] = itm.getTsSimpleCode().getRid();
		objArr[7] = 1;
		objArr[8] = itm.getRid();
		objArr[9] = jdgptn;
		objArr[10] = sex;
		objArr[11] = itm.getItemCode();
		objArr[12] = 1;
		objArr[13] = (null == rstDescItemMap || null == rstDescItemMap.get(itm.getRid())) ? 1 : 2;
		List<TbTjItemUnitRel> unitRels = itemUnitRelMap.get(itm.getRid());
		objArr[14] = null == unitRels ? 0 : unitRels.size(); //如果只有一个计量单位不显示下拉框 则打开注释即可
        objArr[15] = itm.getTsSimpleCode().getExtendS1();
        if(null != objArr[15] && "1".equals(objArr[15].toString())) {
            objArr[16] = this.stadItemStatus.getAudioModel();
        }
		return objArr;
	}

	/**
	 * 生成前端显示的数据
	 * */
	private void fillResultList(List<Object[]> objects, TbTjItems itm){
		boolean dxFlag = true;
		if(null != objects && objects.size() > 0){
			if(null != objects.get(0)[4] && 1 == Integer.parseInt(objects.get(0)[4].toString())){
				//如果定性描述被删除了 需要恢复到默认状态 相当于objects null
				if(rstDescItemMap.isEmpty() || null == rstDescItemMap.get(itm.getRid())){
					dxFlag = false;
				}
			}
		}
		if(dxFlag && null != objects && objects.size() > 0){
			if(null != objects.get(0)[4] && 1 == Integer.parseInt(objects.get(0)[4].toString())){
				//定性的项目
				Object[] objArr = fillResultObjectArr(itm,1,null,null,null,
						null,null,false);
				if(null != objArr){
					resultList.add(objArr);
				}
			}else{
				List<TbTjItemUnitRel> unList = itemUnitRelMap.get(itm.getRid());
				if(null == unList || unList.size() == 0){
					return;
				}
				List<Integer> msruntIds = new ArrayList<>();
				for(TbTjItemUnitRel unitRel : unList){
					if(null != unitRel.getFkByMsruntId() && null != unitRel.getFkByMsruntId().getRid()){
						msruntIds.add(unitRel.getFkByMsruntId().getRid().intValue());
					}
				}

				//定量的项目
				if(null == itm.getSex()){
					//需从objects 提取赋值
					//通用性别
					if(null == itemSpeMap.get(itm.getRid())){
						Object[] tmpObj = null;
						for(Object[] ob : objects){
							if(null != ob[6] && msruntIds.contains(Integer.parseInt(ob[6].toString())) && null == ob[5]){
								tmpObj = ob;
								break;
							}
						}
						Object[] objArr = fillResultObjectArr(itm,
								2,
                                (null == tmpObj || null == tmpObj[5]) ? null : Integer.parseInt(tmpObj[5].toString()),
                                (null == tmpObj || null == tmpObj[6]) ? null : Integer.parseInt(tmpObj[6].toString()),
                                (null == tmpObj || null == tmpObj[2]) ? null : new BigDecimal(tmpObj[2].toString()),
                                (null == tmpObj || null == tmpObj[3]) ? null : new BigDecimal(tmpObj[3].toString()),
								null,null == tmpObj ? true : false);
						if(null != objArr){
							resultList.add(objArr);
						}
					}else{
						List<TbTjItemsSpe> speList = itemSpeMap.get(itm.getRid());
						boolean flag = true;
						boolean childFlag = false;
						Map<Integer,Object[]> tmpMap = new HashMap<>();
						if(null == objects.get(0)[5]){
							Object[] ob = null;
							if(null != objects.get(0)[6] && msruntIds.contains(Integer.parseInt(objects.get(0)[6].toString()))){
								ob = objects.get(0);
							}
							if(null == ob){
								ob = new Object[7];
								ob[0] = null;
								ob[1] = itm.getRid();
								ob[2] = itm.getMinval();
								ob[3] = itm.getMaxval();
								ob[4] = 2;
								ob[5] = itm.getSex();
								ob[6] = itm.getMsruntId();
							}
							tmpMap.put(1,ob);
							tmpMap.put(2,ob);
						}else{
							for(Object[] obj : objects){
								if(null == obj[6] || !msruntIds.contains(Integer.parseInt(obj[6].toString()))){
									continue;
								}
								if(null != obj[5] && Integer.parseInt(obj[5].toString()) == 1){
									tmpMap.put(1,obj);
								}else if(null != obj[5] && Integer.parseInt(obj[5].toString()) == 2){
									tmpMap.put(2,obj);
								}
							}
						}
						for(TbTjItemsSpe spe : speList){
							if(null == spe.getSex()){
								continue;
							}
							if(StringUtils.isNotBlank(spe.getMsrunt()) &&
									itm.getMsruntId().intValue() == Integer.parseInt(spe.getMsrunt())){
								Object[] objArr = null;
								if(!tmpMap.isEmpty() && tmpMap.entrySet().size() > 1){
									if(null != spe.getSex() && null != tmpMap.get(Integer.parseInt(spe.getSex()))){
										Object[] tobj = tmpMap.get(Integer.parseInt(spe.getSex()));
										objArr = fillResultObjectArr(itm,
												2,
												null == spe.getSex() ? null : Integer.parseInt(spe.getSex()),
												null == tobj[6] ? null : Integer.parseInt(tobj[6].toString()),
												null == tobj[2] ? null : new BigDecimal(tobj[2].toString()),
												null == tobj[3] ? null : new BigDecimal(tobj[3].toString()),
												null,
												false);
									}
								}else{
									Object[] tobj = null;
									if(!tmpMap.isEmpty()){
										tobj = tmpMap.get(Integer.parseInt(spe.getSex()));
									}
									objArr = fillResultObjectArr(itm,
											2,
											null == spe.getSex() ? null : Integer.parseInt(spe.getSex()),
											(null == tobj || null == tobj[6]) ? itm.getMsruntId() : Integer.parseInt(tobj[6].toString()),
											(null == tobj || null == tobj[2]) ? spe.getMinval() : new BigDecimal(tobj[2].toString()),
											(null == tobj || null == tobj[3]) ? spe.getMaxval() : new BigDecimal(tobj[3].toString()),
											null,
											false);
								}
								if(null != objArr){
									if(childFlag){
										objArr[12] = 0;
										childFlag = false;
									}
									if(flag){
										objArr[12] = 2;
										childFlag = true;
									}
									flag = false;
									resultList.add(objArr);
								}
							}
						}
					}
				}else{
					//固定性别
					boolean flag = true;
					for(Object[] obj : objects){
						if(null != obj[5] && itm.getSex().intValue() == Integer.parseInt(obj[5].toString())
								&& null != obj[6] && msruntIds.contains(Integer.parseInt(obj[6].toString()))){//有对应性别 存在单位 才赋值
							//非通用项目 需从objects 提取赋值
							Object[] objArr = fillResultObjectArr(itm,
									2,
									null == obj[5] ? null : Integer.parseInt(obj[5].toString()),
									null == obj[6] ? null : Integer.parseInt(obj[6].toString()),
									null == obj[2] ? null : new BigDecimal(obj[2].toString()),
									null == obj[3] ? null : new BigDecimal(obj[3].toString()),
									null,false);
							if(null != objArr){
								flag = false;
								resultList.add(objArr);
							}
						}
					}
					//如果已经存在记录 但未找到对应性别的记录 按照记录错误处理
					if(flag){
						Object[] objArr = fillResultObjectArr(itm,
								2,
								null,
								null,
								null,
								null,
								null,true);
						if(null != objArr){
							resultList.add(objArr);
						}
					}
				}
			}
		}else{
			if(null == itm.getSex()){
				//通用项目
				if(null == itemSpeMap.get(itm.getRid())){
					Object[] objArr = fillResultObjectArr(itm,2,null,null,null,
							null,null,true);
					if(null != objArr){
						resultList.add(objArr);
					}
				}else{
					List<TbTjItemsSpe> speList = itemSpeMap.get(itm.getRid());
					boolean flag = true;
					boolean childFlag = false;
					for(TbTjItemsSpe spe : speList){
						if(null == spe.getSex()){
							continue;
						}
						if(StringUtils.isNotBlank(spe.getMsrunt()) &&
								itm.getMsruntId().intValue() == Integer.parseInt(spe.getMsrunt())){
							Object[] objArr = fillResultObjectArr(itm,
									2,
									null == spe.getSex() ? null : Integer.parseInt(spe.getSex()),
									itm.getMsruntId(),
									spe.getMinval(),
									spe.getMaxval(),
									null,
									false);
							if(null != objArr){
								if(childFlag){
									objArr[12] = 0;
									childFlag = false;
								}
								if(flag){
									objArr[12] = 2;
									childFlag = true;
								}
								flag = false;
								resultList.add(objArr);
							}
						}
					}
				}
			}else{
				Object[] objArr = fillResultObjectArr(itm,2,null,null,null,
						null,null,true);
				if(null != objArr){
					resultList.add(objArr);
				}
			}
		}
	}

	/**
	 * 验证数据 组合存储数据
	 * @return
	 */
	public boolean flagResult(boolean excFlag){
		boolean flag = true;
		if(null != cacheMap && cacheMap.size() >0){
			List<Object[]> allList = new ArrayList<>();
			if(null != resultList && resultList.size() >0){
				for(Object[] orr :resultList){
					List<Object[]> tmpList = cacheMap.get(Integer.parseInt(orr[8].toString()));
					if(null == tmpList || tmpList.size() == 0){
						tmpList = new  ArrayList<>();
						tmpList.add(orr);
						cacheMap.put(Integer.parseInt(orr[8].toString()), tmpList);
						continue;
					}
					if(null != orr[9] && 1 == Integer.parseInt(orr[9].toString())){
						tmpList = new ArrayList<>();
						tmpList.add(orr);
					}else{
						if(null == orr[9] || null == tmpList.get(0)[9]){
							continue;
						}
						if(2 == Integer.parseInt(orr[9].toString())
								&& 1 == Integer.parseInt(tmpList.get(0)[9].toString())){
							//如果是定性的 调整成男女的 那么在第一轮循环 tmpList 是new 继续循环的时候tmpList取的是第一轮new的
							//所以第二轮循环到女 就不会走这里
							tmpList = new ArrayList<>();
							tmpList.add(orr);
						}else{
							boolean exitsFlag = true;
							for(Object[] objArr : tmpList){
								if((null == objArr[10] && null == orr[10]) || (null != objArr[10] && null != orr[10] &&
										Integer.parseInt(objArr[10].toString()) == Integer.parseInt(orr[10].toString()))){
									objArr[2] = orr[2];
									objArr[3] = orr[3];
									objArr[4] = orr[4];
									exitsFlag = false;
								}
							}
							if(exitsFlag){
								tmpList.add(orr);
							}
						}
					}
					cacheMap.put(Integer.parseInt(orr[8].toString()), tmpList);
				}
			}
			for (Integer key : cacheMap.keySet()) {
				allList.addAll(cacheMap.get(key));
			}
			List<TbTjStadItems> itemList = new ArrayList<>();
			for(Object[] orr :allList){
				TbTjStadItems item = new TbTjStadItems();
				item.setJdgptn(Integer.parseInt(orr[9].toString()));
				if(2 == item.getJdgptn()){
					if(!"".equals(StringUtils.objectToString(orr[3])) && !"".equals(StringUtils.objectToString(orr[4]))){
						BigDecimal a =new BigDecimal(orr[3].toString());
						BigDecimal b =new BigDecimal(orr[4].toString());
						if(b.compareTo(a) == -1){
							JsfUtil.addErrorMessage(orr[0].toString()+"-"+orr[1].toString()+"-"+
									(null == orr[10] ? "通用" : (1 == Integer.parseInt(orr[10].toString())) ? "男" : "女")+
									"最大值应大于等于最小值！");
							flag = false;
						}else{
							item.setMinval(a);
							item.setMaxval(b);
						}
					}else if(!"".equals(StringUtils.objectToString(orr[3])) || !"".equals(StringUtils.objectToString(orr[4]))){
						if(!"".equals(StringUtils.objectToString(orr[3]))){
							BigDecimal a =new BigDecimal(orr[3].toString());
							item.setMinval(a);
						}else{
							BigDecimal a =new BigDecimal(orr[4].toString());
							item.setMaxval(a);
						}
					}
					if(excFlag){
						if("".equals(StringUtils.objectToString(orr[3]))){
							JsfUtil.addErrorMessage(orr[0].toString()+"-"+orr[1].toString()+"最小值不能为空！");
							flag = false;
						}else if("".equals(StringUtils.objectToString(orr[4]))){
							JsfUtil.addErrorMessage(orr[0].toString()+"-"+orr[1].toString()+"最大值不能为空！");
							flag = false;
						}
					}
					if(null == orr[2] || StringUtils.isBlank(orr[2].toString())){
						JsfUtil.addErrorMessage(orr[0].toString()+"-"+orr[1].toString()+"计量单位不能为空！");
						flag = false;
					}else{
						item.setFkByMsruntId(new TsSimpleCode(Integer.parseInt(orr[2].toString())));
					}
					item.setSex(null == orr[10] ? null : Integer.parseInt(orr[10].toString()));
					//增加国家接口标准的比较
					if (!ObjectUtil.isEmpty(this.ItemsGjMap) &&
							orr[8] != null && orr[2] != null &&
							ItemsGjMap.containsKey(orr[8].toString()) &&
							item.getMinval() != null && item.getMaxval() != null) {
						List<TbTjItemsGj> itemsGjList = ItemsGjMap.get(orr[8].toString());
						if (!CollectionUtils.isEmpty(itemsGjList)) {
							for (TbTjItemsGj itemsGj : itemsGjList) {
								if (((itemsGj.getSex() == null && orr[10] == null) ||
										(itemsGj.getSex() != null && orr[10] != null && itemsGj.getSex().equals(Integer.parseInt(orr[10].toString())))) &&
										Integer.parseInt(orr[2].toString()) == itemsGj.getFkByMsruntId().getRid()) {
									BigDecimal minval = itemsGj.getMinval();
									BigDecimal maxval = itemsGj.getMaxval();
									if (!(item.getMinval().compareTo(minval) >= 0 && item.getMaxval().compareTo(maxval) <= 0)) {
										JsfUtil.addErrorMessage(orr[1].toString() +"在性别为"+(orr[10]==null?"通用":"1".equals(orr[10].toString())?"男":"女")+ "时的最小值、最大值允许范围为" + minval + "~" + maxval + "！");
										flag = false;
									}
								}
							}
						}
					}
				}
				item.setFkByItemId(iHethBaseService.findTbTjItems(Integer.parseInt(orr[8].toString())));
				item.setFkByMainId(stadItemStatus);
				item.setCreateDate(new Date());
				item.setCreateManid(Global.getUser().getRid());
				itemList.add(item);
			}
			stadItemStatus.setItemList(itemList);
		}
		return flag;
	}

	/**
	 * 标识rowspan
	 */
	private void fillRowspan(){
		Map<String,Integer> map = new HashMap<>();
		if(null != resultList && resultList.size()>0){
			for(Object[] orr:resultList){
				String codeId = orr[6].toString();
				if(map.containsKey(codeId)){
					Integer num = map.get(codeId);
					map.put(codeId,num+1);
				}else{
					map.put(codeId, 1);
				}
			}

			for(Object[] orr:resultList){
				String codeId = orr[6].toString();
				if(map.containsKey(codeId)){
					orr[7] = map.get(codeId);
					map.put(codeId,0);
				}
			}
		}
	}

	/** 拷贝对象 避免出现 进入页面的时候调整数据影响到cacheMap */
	private Object[] copyObjectArr(Object[] orr){
		//注意 与fillResultObjectArr中数组长度保持一致
		Object[] arr = new Object[17];
		arr[0] = orr[0];
		arr[1] = orr[1];
		arr[2] = orr[2];
		arr[3] = orr[3];
		arr[4] = orr[4];
		arr[5] = orr[5];
		arr[6] = orr[6];
		arr[7] = orr[7];
		arr[8] = orr[8];
		arr[9] = orr[9];
		arr[10] = orr[10];
		arr[11] = orr[11];
		arr[12] = orr[12];
		arr[13] = orr[13];
		arr[14] = orr[14];
		arr[15] = orr[15];
		arr[16] = orr[16];
		return arr;
	}

	public List<Object[]> getResultList() {
		return resultList;
	}

	public void setResultList(List<Object[]> resultList) {
		this.resultList = resultList;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public String getSearchItemName() {
		return searchItemName;
	}

	public void setSearchItemName(String searchItemName) {
		this.searchItemName = searchItemName;
	}

	public String getSearchItemId() {
		return searchItemId;
	}

	public void setSearchItemId(String searchItemId) {
		this.searchItemId = searchItemId;
	}

	public List<TbTjItems> getSelectItems() {
		return selectItems;
	}

	public void setSelectItems(List<TbTjItems> selectItems) {
		this.selectItems = selectItems;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public TbTjStadItemsStatus getStadItemStatus() {
		return stadItemStatus;
	}

	public void setStadItemStatus(TbTjStadItemsStatus stadItemStatus) {
		this.stadItemStatus = stadItemStatus;
	}

	public Map<Integer,Object[]> getMap() {
		return map;
	}

	public void setMap(Map<Integer,Object[]> map) {
		this.map = map;
	}

	public List<TbTjItems> getEffectiveItemList() {
		return effectiveItemList;
	}

	public void setEffectiveItemList(List<TbTjItems> effectiveItemList) {
		this.effectiveItemList = effectiveItemList;
	}

	public Map<Integer, List<TbTjItemUnitRel>> getItemUnitRelMap() {
		return itemUnitRelMap;
	}

	public void setItemUnitRelMap(Map<Integer, List<TbTjItemUnitRel>> itemUnitRelMap) {
		this.itemUnitRelMap = itemUnitRelMap;
	}

	public Map<Integer, List<TbTjItemsSpe>> getItemSpeMap() {
		return itemSpeMap;
	}

	public void setItemSpeMap(Map<Integer, List<TbTjItemsSpe>> itemSpeMap) {
		this.itemSpeMap = itemSpeMap;
	}

	public Map<Integer, Integer> getRstDescItemMap() {
		return rstDescItemMap;
	}

	public void setRstDescItemMap(Map<Integer, Integer> rstDescItemMap) {
		this.rstDescItemMap = rstDescItemMap;
	}

	public Map<Integer, List<Object[]>> getCacheMap() {
		return cacheMap;
	}

	public void setCacheMap(Map<Integer, List<Object[]>> cacheMap) {
		this.cacheMap = cacheMap;
	}

	public Map<Integer, String> getUnitMap() {
		return unitMap;
	}

	public void setUnitMap(Map<Integer, String> unitMap) {
		this.unitMap = unitMap;
	}

	public Map<Integer, TbTjItems> getTjItemsMap() {
		return tjItemsMap;
	}

	public void setTjItemsMap(Map<Integer, TbTjItems> tjItemsMap) {
		this.tjItemsMap = tjItemsMap;
	}

	public Map<String, List<TbTjItemsGj>> getItemsGjMap() {
		return ItemsGjMap;
	}

	public void setItemsGjMap(Map<String, List<TbTjItemsGj>> itemsGjMap) {
		ItemsGjMap = itemsGjMap;
	}
}
