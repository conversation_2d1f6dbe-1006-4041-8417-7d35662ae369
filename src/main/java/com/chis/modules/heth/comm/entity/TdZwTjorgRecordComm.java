package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-10-14
 */
@Entity
@Table(name = "TD_ZW_TJORG_RECORD")
@SequenceGenerator(name = "TdZwTjorgRecord", sequenceName = "TD_ZW_TJORG_RECORD_SEQ", allocationSize = 1)
public class TdZwTjorgRecordComm implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwTjorginfoComm fkByMainId;
	private TsZone fkByZoneId;
	private String rcdNo;
	private Date certDate;
	private Integer stateMark;
	private Date logoutDate;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private List<TdZwTjorgRcdItemComm> rcdItems = new ArrayList<>();

	/*********************临时字段*****************************/
	/**服务项目*/
	private String serviceItems;
	private List<String> serviceObj;
	/**地区rid*/
	private Integer zoneId;

	public TdZwTjorgRecordComm() {
	}

	public TdZwTjorgRecordComm(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjorgRecord")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwTjorginfoComm getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwTjorginfoComm fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ZONE_ID")			
	public TsZone getFkByZoneId() {
		return fkByZoneId;
	}

	public void setFkByZoneId(TsZone fkByZoneId) {
		this.fkByZoneId = fkByZoneId;
	}	
			
	@Column(name = "RCD_NO")	
	public String getRcdNo() {
		return rcdNo;
	}

	public void setRcdNo(String rcdNo) {
		this.rcdNo = rcdNo;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "CERT_DATE")			
	public Date getCertDate() {
		return certDate;
	}

	public void setCertDate(Date certDate) {
		this.certDate = certDate;
	}	
			
	@Column(name = "STATE_MARK")	
	public Integer getStateMark() {
		return stateMark;
	}

	public void setStateMark(Integer stateMark) {
		this.stateMark = stateMark;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "LOGOUT_DATE")			
	public Date getLogoutDate() {
		return logoutDate;
	}

	public void setLogoutDate(Date logoutDate) {
		this.logoutDate = logoutDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Transient
	public String getServiceItems() {
		return serviceItems;
	}

	public void setServiceItems(String serviceItems) {
		this.serviceItems = serviceItems;
	}
	@Transient
	public List<String> getServiceObj() {
		return serviceObj;
	}

	public void setServiceObj(List<String> serviceObj) {
		this.serviceObj = serviceObj;
	}
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
	public List<TdZwTjorgRcdItemComm> getRcdItems() {
		return rcdItems;
	}

	public void setRcdItems(List<TdZwTjorgRcdItemComm> rcdItems) {
		this.rcdItems = rcdItems;
	}
	@Transient
	public Integer getZoneId() {
		return zoneId;
	}

	public void setZoneId(Integer zoneId) {
		this.zoneId = zoneId;
	}

	
			
}