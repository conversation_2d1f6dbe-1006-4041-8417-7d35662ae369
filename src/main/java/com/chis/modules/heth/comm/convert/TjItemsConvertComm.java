package com.chis.modules.heth.comm.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbTjItems;

/**
 * 体检项目排序转换器
 * 
 * <AUTHOR>
 * @createdate 2015-8-3
 */
@FacesConverter("heth.TjItemsConvertComm")
public class TjItemsConvertComm implements Converter {
	@Override
	public Object getAsObject(FacesContext facesContext, UIComponent uiComponent, String s) {
		if (StringUtils.isNotBlank(s)) {
			String[] arr = s.split(":");
			TbTjItems tbTjItems = new TbTjItems();
			tbTjItems.setRid(Integer.valueOf(arr[0]));
			tbTjItems.setItemName(arr[1]);
			return tbTjItems;
		}
		return null;
	}

	@Override
	public String getAsString(FacesContext facesContext, UIComponent uiComponent, Object o) {
		if (o == null) {
			return "";
		} else {
			TbTjItems u = (TbTjItems) o;
			return u.getRid() + ":" + u.getItemName();
		}
	}
}
