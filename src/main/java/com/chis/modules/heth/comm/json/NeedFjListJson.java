package com.chis.modules.heth.comm.json;

/**
 *  <p>类描述：更新应复检未检标记和最新主检结论参数</p>
 * @ClassAuthor hsj 2022-06-15 16:49
 */
public class NeedFjListJson {
    /**操作标识（1新增 2修改 3删除）*/
    private Integer type;
    /**是否复检（1是 2否）*/
    private Integer isFj;
    private String lastFstBhkCode;
    private Integer bhkOrgRid;
    private Integer persionRid;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getIsFj() {
        return isFj;
    }

    public void setIsFj(Integer isFj) {
        this.isFj = isFj;
    }

    public String getLastFstBhkCode() {
        return lastFstBhkCode;
    }

    public void setLastFstBhkCode(String lastFstBhkCode) {
        this.lastFstBhkCode = lastFstBhkCode;
    }

    public Integer getBhkOrgRid() {
        return bhkOrgRid;
    }

    public void setBhkOrgRid(Integer bhkOrgRid) {
        this.bhkOrgRid = bhkOrgRid;
    }

    public Integer getPersionRid() {
        return persionRid;
    }

    public void setPersionRid(Integer persionRid) {
        this.persionRid = persionRid;
    }
}
