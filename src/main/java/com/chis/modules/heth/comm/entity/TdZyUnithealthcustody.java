package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-6
 */
@Entity
@Table(name = "TD_ZY_UNITHEALTHCUSTODY")
@SequenceGenerator(name = "TdZyUnithealthcustody", sequenceName = "TD_ZY_UNITHEALTHCUSTODY_SEQ", allocationSize = 1)
public class TdZyUnithealthcustody implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZyUnitbasicinfo fkByMainId;
	private Integer ifhea;
	private String checkUnitNames;
	private String checkReportNos;
	private Integer ifheaDust;
	private Integer heaDustPeoples;
	private Integer ifheaChemistry;
	private Integer heaChemistryPeoples;
	private Integer heaChemistryLeadPeoples;
	private Integer heaTchemistryBenzenePeoples;
	private Integer ifheaPhysics;
	private Integer heaPhysicsPeoples;
	private Integer heaPhysicsNoisePeoples;
	private Integer ifheaRadioactivity;
	private Integer heaRadioactivityPeoples;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZyUnithealthcustody() {
	}

	public TdZyUnithealthcustody(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZyUnithealthcustody")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZyUnitbasicinfo getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZyUnitbasicinfo fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "IFHEA")	
	public Integer getIfhea() {
		return ifhea;
	}

	public void setIfhea(Integer ifhea) {
		this.ifhea = ifhea;
	}	
			
	@Column(name = "CHECK_UNIT_NAMES")	
	public String getCheckUnitNames() {
		return checkUnitNames;
	}

	public void setCheckUnitNames(String checkUnitNames) {
		this.checkUnitNames = checkUnitNames;
	}	
			
	@Column(name = "CHECK_REPORT_NOS")	
	public String getCheckReportNos() {
		return checkReportNos;
	}

	public void setCheckReportNos(String checkReportNos) {
		this.checkReportNos = checkReportNos;
	}	
			
	@Column(name = "IFHEA_DUST")	
	public Integer getIfheaDust() {
		return ifheaDust;
	}

	public void setIfheaDust(Integer ifheaDust) {
		this.ifheaDust = ifheaDust;
	}	
			
	@Column(name = "HEA_DUST_PEOPLES")	
	public Integer getHeaDustPeoples() {
		return heaDustPeoples;
	}

	public void setHeaDustPeoples(Integer heaDustPeoples) {
		this.heaDustPeoples = heaDustPeoples;
	}	
			
	@Column(name = "IFHEA_CHEMISTRY")	
	public Integer getIfheaChemistry() {
		return ifheaChemistry;
	}

	public void setIfheaChemistry(Integer ifheaChemistry) {
		this.ifheaChemistry = ifheaChemistry;
	}	
			
	@Column(name = "HEA_CHEMISTRY_PEOPLES")	
	public Integer getHeaChemistryPeoples() {
		return heaChemistryPeoples;
	}

	public void setHeaChemistryPeoples(Integer heaChemistryPeoples) {
		this.heaChemistryPeoples = heaChemistryPeoples;
	}	
			
	@Column(name = "HEA_CHEMISTRY_LEAD_PEOPLES")	
	public Integer getHeaChemistryLeadPeoples() {
		return heaChemistryLeadPeoples;
	}

	public void setHeaChemistryLeadPeoples(Integer heaChemistryLeadPeoples) {
		this.heaChemistryLeadPeoples = heaChemistryLeadPeoples;
	}	
			
	@Column(name = "HEA_TCHEMISTRY_BENZENE_PEOPLES")	
	public Integer getHeaTchemistryBenzenePeoples() {
		return heaTchemistryBenzenePeoples;
	}

	public void setHeaTchemistryBenzenePeoples(Integer heaTchemistryBenzenePeoples) {
		this.heaTchemistryBenzenePeoples = heaTchemistryBenzenePeoples;
	}	
			
	@Column(name = "IFHEA_PHYSICS")	
	public Integer getIfheaPhysics() {
		return ifheaPhysics;
	}

	public void setIfheaPhysics(Integer ifheaPhysics) {
		this.ifheaPhysics = ifheaPhysics;
	}	
			
	@Column(name = "HEA_PHYSICS_PEOPLES")	
	public Integer getHeaPhysicsPeoples() {
		return heaPhysicsPeoples;
	}

	public void setHeaPhysicsPeoples(Integer heaPhysicsPeoples) {
		this.heaPhysicsPeoples = heaPhysicsPeoples;
	}	
			
	@Column(name = "HEA_PHYSICS_NOISE_PEOPLES")	
	public Integer getHeaPhysicsNoisePeoples() {
		return heaPhysicsNoisePeoples;
	}

	public void setHeaPhysicsNoisePeoples(Integer heaPhysicsNoisePeoples) {
		this.heaPhysicsNoisePeoples = heaPhysicsNoisePeoples;
	}	
			
	@Column(name = "IFHEA_RADIOACTIVITY")	
	public Integer getIfheaRadioactivity() {
		return ifheaRadioactivity;
	}

	public void setIfheaRadioactivity(Integer ifheaRadioactivity) {
		this.ifheaRadioactivity = ifheaRadioactivity;
	}	
			
	@Column(name = "HEA_RADIOACTIVITY_PEOPLES")	
	public Integer getHeaRadioactivityPeoples() {
		return heaRadioactivityPeoples;
	}

	public void setHeaRadioactivityPeoples(Integer heaRadioactivityPeoples) {
		this.heaRadioactivityPeoples = heaRadioactivityPeoples;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}