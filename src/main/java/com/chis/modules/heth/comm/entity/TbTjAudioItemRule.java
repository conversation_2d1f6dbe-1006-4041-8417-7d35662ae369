package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-4-10
 */
@Entity
@Table(name = "TB_TJ_AUDIO_ITEM_RULE")
@SequenceGenerator(name = "TbTjAudioItemRule", sequenceName = "TB_TJ_AUDIO_ITEM_RULE_SEQ", allocationSize = 1)
public class TbTjAudioItemRule implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer itemType;
	private Integer sex;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	private List<TbTjAdoItmRuleSub> tbTjAdoItmRuleSubList = new ArrayList<TbTjAdoItmRuleSub>(0);
	
	public TbTjAudioItemRule() {
	}

	public TbTjAudioItemRule(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjAudioItemRule")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "ITEM_TYPE")	
	public Integer getItemType() {
		return itemType;
	}

	public void setItemType(Integer itemType) {
		this.itemType = itemType;
	}	
			
	@Column(name = "SEX")	
	public Integer getSex() {
		return sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId",orphanRemoval=true)
    public List<TbTjAdoItmRuleSub> getTbTjAdoItmRuleSubList() {
        return tbTjAdoItmRuleSubList;
    }

    public void setTbTjAdoItmRuleSubList(List<TbTjAdoItmRuleSub> tbTjAdoItmRuleSubList) {
        this.tbTjAdoItmRuleSubList = tbTjAdoItmRuleSubList;
    }
}