package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2024-8-15
 */
@Entity
@Table(name = "TD_GJ_BHKSUB")
@SequenceGenerator(name = "TdGjBhksub", sequenceName = "TD_GJ_BHKSUB_SEQ", allocationSize = 1)
public class TdGjBhksub implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdGjBhk fkByMainId;
    private TbTjItems fkByItemId;
    private String itemRst;
    private String msrunt;
    private Integer ifAbnormal;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    public TdGjBhksub() {
    }

    public TdGjBhksub(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdGjBhksub")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdGjBhk getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdGjBhk fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "ITEM_ID")
    public TbTjItems getFkByItemId() {
        return fkByItemId;
    }

    public void setFkByItemId(TbTjItems fkByItemId) {
        this.fkByItemId = fkByItemId;
    }

    @Column(name = "ITEM_RST")
    public String getItemRst() {
        return itemRst;
    }

    public void setItemRst(String itemRst) {
        this.itemRst = itemRst;
    }

    @Column(name = "MSRUNT")
    public String getMsrunt() {
        return msrunt;
    }

    public void setMsrunt(String msrunt) {
        this.msrunt = msrunt;
    }

    @Column(name = "IF_ABNORMAL")
    public Integer getIfAbnormal() {
        return ifAbnormal;
    }

    public void setIfAbnormal(Integer ifAbnormal) {
        this.ifAbnormal = ifAbnormal;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

}