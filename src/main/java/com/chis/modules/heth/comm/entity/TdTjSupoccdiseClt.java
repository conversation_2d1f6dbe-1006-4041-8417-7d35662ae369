package com.chis.modules.heth.comm.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * @Description : 疑似职业病人群（数据录入）
 * @ClassAuthor: anjing
 * @Date : 2019/5/14 10:40
 **/
@Entity
@Table(name = "TD_TJ_SUPOCCDISE_CLT", uniqueConstraints = @UniqueConstraint(columnNames = {"BHK_ID", "OCC_DISEID" }))
@SequenceGenerator(name = "TdTjSupoccdiseClt_Seq", sequenceName = "TD_TJ_SUPOCCDISE_CLT_SEQ", allocationSize = 1)
public class TdTjSupoccdiseClt implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /**主键Id*/
    private Integer rid;
    /**关联：体检主表-数据录入（体检主表Id）*/
    private TdTjBhkClt fkByBhkId;
    /**关联：码表（职业病Id）*/
    private TsSimpleCode fkByOccDiseid;
    /**关联：码表（危害因素Id）*/
    private TsSimpleCode fkByBadrsnId;
    /**创建日期*/
    private Date createDate;
    /**创建人*/
    private Integer createManid;
    /**修改日期*/
    private Date modifyDate;
    /**修改人*/
    private Integer modifyManid;

    /**用人单位名称*/
    private String crptName;

    public TdTjSupoccdiseClt() {
    }

    public TdTjSupoccdiseClt(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjSupoccdiseClt_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "BHK_ID" )
    public TdTjBhkClt getFkByBhkId() {
        return fkByBhkId;
    }

    public void setFkByBhkId(TdTjBhkClt fkByBhkId) {
        this.fkByBhkId = fkByBhkId;
    }

    @ManyToOne
    @JoinColumn(name = "OCC_DISEID" )
    public TsSimpleCode getFkByOccDiseid() {
        return fkByOccDiseid;
    }

    public void setFkByOccDiseid(TsSimpleCode fkByOccDiseid) {
        this.fkByOccDiseid = fkByOccDiseid;
    }

    @ManyToOne
    @JoinColumn(name = "BADRSN_ID" )
    public TsSimpleCode getFkByBadrsnId() {
        return fkByBadrsnId;
    }

    public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
        this.fkByBadrsnId = fkByBadrsnId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Column(name = "CRPT_NAME")
    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }
}
