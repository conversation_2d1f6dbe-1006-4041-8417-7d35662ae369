package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.service.Gbz188PageCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.TabChangeEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 *  <p>类描述：GBZ188维护：web-heth包迁移comm</p>
 * @ClassAuthor hsj 2023-07-18 8:58
 */
@ManagedBean(name = "gbz188PageCommBean")
@ViewScoped
public class Gbz188PageCommBean extends FacesEditBean {

	private static final long serialVersionUID = 8516699735307181564L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/** ejb session bean */
	private Gbz188PageCommServiceImpl gbz188PageCommService = (Gbz188PageCommServiceImpl) SpringContextHolder
			.getBean(Gbz188PageCommServiceImpl.class);
	/** 公用会话Bean服务 */
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	/** 查询的危害因素列表 */
	private List<TbZwtjMainstd> badRsnList;
	/** 过滤的危害因素列表 */
	private List<TbZwtjMainstd> filterBadRsnList;
	/** 在岗状态列表 */
	private List<SelectItem> jobStateList;
	/** 选择的方案标准 */
	private TbZwtjMainstd selectedMainstd;
	/*** 标准项目组合列表 */
	private List<TbZwtjSchemeItems> tbZwtjSchemeItemses;
	/** 职业禁忌证 */
	private List<TbZwtjJjzs> tbZwtjJjzs;
	/** 疑似职来病列表 */
	private List<TbZwtjOccdises> tbZwtjOccdises;
	/** 询问症状列表 */
	private List<TbZwtjAskitems> tbZwtjAskitemses;
	/** 编辑标准页面的危害因素名称中临时变量 */
	private String editBadRsnName;
	/** 编辑标准页面的危害因素RID */
	private Integer editBadRsnRid;
	/** 编辑标准界面的在岗状态 */
	private Integer editJobstate;
	/** 危害因素树 */
	private TreeNode badRsnTree;
	/** 编辑对象 */
	private TbZwtjMainstd curzwtjMainstd;
	/** 是否停用 */
	private Boolean editStopTag;
	/** 选择计算 */
	private Integer selectCount;
	/** 操作表名 */
	private String optTableName;
	/** 操作主键 */
	private Integer optRid;
	/** 活动选项卡标题 */
	private String actionTabTitle;
	/** 显示停用 */
	private boolean showStop;
	/** 过滤危害因素 */
	private String filterBadrsn;
	/** 过滤在岗状态 */
	private String filterJobState;

	/********************************** 项目组合选项卡 ******************************/
	/** 选择查询项目编码 */
	private String searchItemCode;
	/** 选择查询项目名称 */
	private String searchItemName;
	/** 可选择的项目列表 */
	private List<TbZwtjSchemeItems> selectItemList;
	/** 过滤可选择的项目列表 */
	private List<TbZwtjSchemeItems> filterSelectItemList;
	/** 已选择的项目列表 */
	private List<TbZwtjSchemeItems> selectedItemList;
	/** 选择的项目组合 */
	private TbZwtjSchemeItems selectTbzwItems;
	/** 编辑业务提醒 */
	private String editBsWake;
	/** 选择的当前行项目组合名称 **/
	private TbZwtjSchemeItems selecedSchemeItem;

	/******************************** 职业禁忌选项卡 ************************************/
	/** 职业禁忌证可选 */
	private List<TbZwtjJjzs> selectJjzList;
	/** 过滤职来禁忌证列表 */
	private List<TbZwtjJjzs> filterSelectJjzList;
	/** 选择的职业禁忌证 */
	private TbZwtjJjzs selectTbzwJjs;
	/** 查询禁忌证编码 */
	private String searchJjsCode;
	/** 查询禁忌证名称 */
	private String searchJjzName;
	/** 已经选择的职业禁忌证 */
	private LinkedList<TbZwtjJjzs> selectedJjzList;

	/******************************** 职业病选项卡 **********************************/
	/** 疑似职业病可选 */
	private List<TbZwtjOccdises> selectOccList;
	/** 过滤疑似职业病列表 */
	private List<TbZwtjOccdises> filterSelectOccList;
	/** 选择的疑似职业病 */
	private TbZwtjOccdises selectTbzwOcc;
	/** 查询疑似职业病编码 */
	private String searchOccCode;
	/** 查询疑似职业病名称 */
	private String searchOccName;
	/** 已经选择的疑似职业病 */
	private LinkedList<TbZwtjOccdises> selectedOccList;

	/******************************** 询问症状选项卡 **********************************/
	/** 询问症状可选 */
	private List<TbZwtjAskitems> selectAskItemList;
	/** 过滤询问症状列表 */
	private List<TbZwtjAskitems> filterSelectAskItemList;
	/** 选择的询问症状 */
	private TbZwtjAskitems selectTbzwAskItem;
	/** 查询询问症状编码 */
	private String searchAskItemCode;
	/** 查询询问症状名称 */
	private String searchAskItemName;
	/** 已经选择的询问症状 */
	private LinkedList<TbZwtjAskitems> selectedAskItemList;

	/**
	 *  <p>方法描述：初始化方法</p>
	 * @MethodAuthor hsj 2023-07-18 9:06
	 */
	public Gbz188PageCommBean() {
		this.pageInit();
		this.searchAction();
		//危害因素列表
		this.filterBadRsnList = badRsnList;
		this.setActiveTab(0);
		//初始化危害因素树型菜单
		this.initBadRsnTree();
	}

	/**
	 *  <p>方法描述：选项卡切换事件</p>
	 * @MethodAuthor hsj 2023-07-18 11:32
	 */
	public void onTabChange(TabChangeEvent event) {
		this.actionTabTitle = event.getTab().getId();
		if (null != this.selectedMainstd) {
			if ("item".equals(this.actionTabTitle)) {
				this.initSchemeItems(this.selectedMainstd);
				RequestContext.getCurrentInstance().update(":mainForm:tableView:schemeItemsTable");
			} else if ("jjz".equals(this.actionTabTitle)) {
				this.initJjzs(this.selectedMainstd);
				RequestContext.getCurrentInstance().update(":mainForm:tableView:jjzTable");
			} else if ("occ".equals(this.actionTabTitle)) {
				this.initOccdises(this.selectedMainstd);
				RequestContext.getCurrentInstance().update(":mainForm:tableView:occTable");
			} else if ("askItems".equals(this.actionTabTitle)) {
				this.initAskItems(this.selectedMainstd);
				RequestContext.getCurrentInstance().update(":mainForm:tableView:askItemsTable");
			}
		}
	}

	/**
	 *  <p>方法描述：页面初始化-码表</p>
	 * @MethodAuthor hsj 2023-07-18 9:06
	 */
	private void pageInit() {
		// 在岗状态下拉列表初始化
		List<TsSimpleCode> simpleCodeList5009 = this.commService.findSimpleCodesByTypeId("5009");
		// 默认活动的选项卡为项目组合
		this.actionTabTitle = "item";
		// 组织下拉列表
		if (null != simpleCodeList5009 && simpleCodeList5009.size() > 0) {
			this.jobStateList = new LinkedList<>();
			for (TsSimpleCode tsSimpleCode : simpleCodeList5009) {
				this.jobStateList.add(new SelectItem(tsSimpleCode.getRid(), tsSimpleCode.getCodeName()));
			}
		}

	}

	/**
	 *  <p>方法描述：查询</p>
	 * @MethodAuthor hsj 2023-07-18 9:07
	 */
	@Override
	public void searchAction() {
		this.badRsnList = this.gbz188PageCommService.getZwtjMainstd(showStop);
	}

	/**
	 *  <p>方法描述：过滤危害因素</p>
	 * @MethodAuthor hsj 2023-07-18 16:02
	 */
	public void filterBadRsn() {
		// 如果两个查询输入框都为空则不做过滤
		if ((null == this.filterBadrsn || this.filterBadrsn.trim().length() <= 0)
				&& (null == this.filterJobState || this.filterJobState.trim().length() <= 0)) {
			this.filterBadRsnList = this.badRsnList;
		} else {
			this.filterBadRsnList = new LinkedList<>();
			for (TbZwtjMainstd tbZwtjMainstd : this.badRsnList) {
				String badSplsht = tbZwtjMainstd.getTsSimpleCodeByBadrsnId().getSplsht();
				String badName = tbZwtjMainstd.getTsSimpleCodeByBadrsnId().getCodeName();

				String jobstateSplsht = tbZwtjMainstd.getTsSimpleCodeByWorkStateid().getSplsht();
				String jobstateName = tbZwtjMainstd.getTsSimpleCodeByWorkStateid().getCodeName();

				// 如果危害因素为空或危害因素符合条件
				boolean badrsnFlag = null == this.filterBadrsn || this.filterBadrsn.trim().length() <= 0
						|| badSplsht.toUpperCase().indexOf(this.filterBadrsn.toUpperCase().trim()) > -1
						|| badName.indexOf(this.filterBadrsn.toUpperCase().trim()) > -1;
				// 如果在岗状态为空或在岗状态符合条件
				boolean jobstateFlag = null == this.filterJobState || this.filterJobState.trim().length() <= 0
						|| jobstateSplsht.toUpperCase().indexOf(this.filterJobState.toUpperCase().trim()) > -1
						|| jobstateName.indexOf(this.filterJobState.trim()) > -1;

				// 如果危害因素和在岗状态的条件都成立
				if (badrsnFlag && jobstateFlag) {
					this.filterBadRsnList.add(tbZwtjMainstd);
				}
			}
		}
	}

	/**
	 *  <p>方法描述：添加标准初始化方法</p>
	 * @MethodAuthor hsj 2023-07-18 9:24
	 */
	public void addMainInit() {
		this.curzwtjMainstd = new TbZwtjMainstd();
		// 初始化添加弹出框界面
		this.editBadRsnName = null;
		this.editBadRsnRid = null;
		this.editJobstate = null;
		this.editStopTag = false;

	}

	/**
	 *  <p>方法描述：编辑标准初始化方法</p>
	 * @MethodAuthor hsj 2023-07-18 9:25
	 */
	public void modMainInit() {
		if (null != this.selectedMainstd) {
			this.curzwtjMainstd = this.selectedMainstd;
			// 危害因素名称
			this.editBadRsnName = this.curzwtjMainstd.getTsSimpleCodeByBadrsnId().getCodeName();
			// 危害因素ID
			this.editBadRsnRid = this.curzwtjMainstd.getTsSimpleCodeByBadrsnId().getRid();
			// 在岗状态ID
			this.editJobstate = this.curzwtjMainstd.getTsSimpleCodeByWorkStateid().getRid();
			// 停用状态
			this.editStopTag = this.curzwtjMainstd.getStopTag() == 0;
			RequestContext.getCurrentInstance().execute("PF('dlg1').show();");
		} else {
			JsfUtil.addErrorMessage("请选择要编辑的监护标准！");
		}
	}

	/**
	 *  <p>方法描述：初始化危害因素树型菜单</p>
	 * @MethodAuthor hsj 2023-07-18 9:13
	 */
	private void initBadRsnTree() {
		this.badRsnTree = new DefaultTreeNode("root", null);
		List<TsSimpleCode> simpleCodesByType_5007 = this.commService.findLevelSimpleCodesByTypeId("5007");
		if (null != simpleCodesByType_5007 && simpleCodesByType_5007.size() > 0) {
			// 只有第一层
			Set<String> firstLevelNoSet = new LinkedHashSet<>();
			// 没有第一层
			Set<String> levelNoSet = new LinkedHashSet<>();
			// 所有类别
			Map<String, TsSimpleCode> menuMap = new HashMap<>();
			for (TsSimpleCode t : simpleCodesByType_5007) {
				menuMap.put(t.getCodeLevelNo(), t);
				if (StringUtils.isNotBlank(t.getCodeLevelNo())) {
					if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
						firstLevelNoSet.add(t.getCodeLevelNo());
					} else {
						levelNoSet.add(t.getCodeLevelNo());
					}
				}
			}
			// 由第一层开始遍历
			for (String ln : firstLevelNoSet) {
				TreeNode node = new DefaultTreeNode(menuMap.get(ln), this.badRsnTree);
				this.addChildNode(ln, levelNoSet, menuMap, node);
			}
			menuMap.clear();
		}
	}

	/**
	 *  <p>方法描述：构建类别树</p>
	 * @MethodAuthor hsj 2023-07-18 16:03
	 */
	private void addChildNode(String levelNo, Set<String> levelNoSet, Map<String, TsSimpleCode> menuMap,
			TreeNode parentNode) {
		int level = StringUtils.countMatches(levelNo, ".");
		for (String ln : levelNoSet) {
			if (StringUtils.countMatches(ln, ".") == (level + 1) && StringUtils.startsWith(ln, levelNo + ".")) {
				TreeNode node = new DefaultTreeNode(menuMap.get(ln), parentNode);
				this.addChildNode(ln, levelNoSet, menuMap, node);
			}
		}
	}

	/**
	 *  <p>方法描述：标准方案编辑页面危害因素树的选择事件</p>
	 * @MethodAuthor hsj 2023-07-18 11:34
	 */
	public void onNodeSelect(NodeSelectEvent event) {
		this.editBadRsnRid = null;
		this.editBadRsnName = null;
		TreeNode selectNode = event.getTreeNode();
		if (null != selectNode) {
			TsSimpleCode t = (TsSimpleCode) selectNode.getData();
			this.editBadRsnRid = t.getRid();
			this.editBadRsnName = t.getCodeName();
		}
	}


	/**
	 *  <p>方法描述：保存标准维护</p>
	 * @MethodAuthor hsj 2023-07-18 11:42
	 */
	public void saveGbz() {
		if (verfiy()) {
			curzwtjMainstd.setCreateDate(new Date());
			curzwtjMainstd.setCreateManid(sessionData.getUser().getRid());
			curzwtjMainstd.setModifyDate(new Date());
			curzwtjMainstd.setModifyManid(sessionData.getUser().getRid());
			curzwtjMainstd.setStopTag((short) 1);
			curzwtjMainstd.setTsSimpleCodeByBadrsnId(new TsSimpleCode(this.editBadRsnRid));
			curzwtjMainstd.setTsSimpleCodeByWorkStateid(new TsSimpleCode(this.editJobstate));
			if (this.editStopTag) {
				this.curzwtjMainstd.setStopTag((short) 0);
			}
			curzwtjMainstd = this.gbz188PageCommService.saveTbZwtjmainstd(curzwtjMainstd);
			RequestContext.getCurrentInstance().execute("PF('dlg1').hide();");
			JsfUtil.addSuccessMessage("GBZ 188标准保存成功！");
			this.searchAction();
			this.filterBadRsn();
			this.selectedMainstd = null;
		}
	}

	/**
	 *  <p>方法描述：保存标准之前进行输入验证</p>
	 * @MethodAuthor hsj 2023-07-18 15:55
	 */
	private boolean verfiy() {
		if (null == this.editBadRsnName) {
			JsfUtil.addErrorMessage("请选择危害因素！");
			return false;
		}
		if (null == this.editJobstate) {
			JsfUtil.addErrorMessage("请选择在岗状态！");
			return false;
		}

		if (this.gbz188PageCommService.verfiyAk(this.editBadRsnRid, this.editJobstate, this.curzwtjMainstd.getRid())) {
			JsfUtil.addErrorMessage("标准方案已经存在！");
			return false;
		}

		return true;
	}

	/**
	 *  <p>方法描述：职业健康监护方案标准（GBZ-188）选择事件</p>
	 * @MethodAuthor hsj 2023-07-18 9:29
	 */
	public void onRowSelect(SelectEvent event) {
		TbZwtjMainstd mainstd = (TbZwtjMainstd) event.getObject();
		this.tableListUpdate(mainstd);
	}

	/**
	 *  <p>方法描述：职业健康监护方案标准（GBZ-188）选择事件之后初始化每个选项卡</p>
	 * @MethodAuthor hsj 2023-07-18 9:29
	 */
	private void tableListUpdate(TbZwtjMainstd mainstd) {
		// 标准项目组合列表
		if ("item".equals(this.actionTabTitle)) {
			this.initSchemeItems(mainstd);
			// 职业禁忌证
		} else if ("jjz".equals(this.actionTabTitle)) {
			this.initJjzs(mainstd);
			// 职业病
		} else if ("occ".equals(this.actionTabTitle)) {
			this.initOccdises(mainstd);
			// 询问症状
		} else if ("askItems".equals(this.actionTabTitle)) {
			this.initAskItems(mainstd);
		}
	}

	/**
	 *  <p>方法描述：选择项目组合刷新业务提醒</p>
	 * @MethodAuthor hsj 2023-07-18 15:56
	 */
	public void onItemRowSelect(SelectEvent event) {
		// 获取选择行的项目组合对象
		this.setSelecedSchemeItem((TbZwtjSchemeItems) event.getObject());
		// 项目组合ID
		Integer itemRid = getSelecedSchemeItem().getTsSimpleCode().getRid();
		// 标准主表ID
		Integer mainstdId = getSelecedSchemeItem().getTbZwtjMainstd().getRid();
		// 查询业务提醒
		this.editBsWake = this.gbz188PageCommService.getWake(itemRid, mainstdId);
	}

	/**
	 *  <p>方法描述：保存业务提醒</p>
	 * @MethodAuthor hsj 2023-07-18 15:10
	 */
	public void saveBsWake() {
		if (null == this.editBsWake || "".equals(this.editBsWake.trim())) {
			JsfUtil.addErrorMessage("业务提醒不能为空！");
			return;
		}
		if (null != this.selecedSchemeItem) {
			this.gbz188PageCommService.saveBsWake(editBsWake, this.selecedSchemeItem.getTsSimpleCode().getRid(),
					this.selecedSchemeItem.getTbZwtjMainstd().getRid(), selecedSchemeItem);
			JsfUtil.addSuccessMessage("业务提醒保存成功！");
		}
	}

	/**
	 *  <p>方法描述：清除业务提醒表内容</p>
	 * @MethodAuthor hsj 2023-07-18 15:14
	 */
	public void cleanBsWork() {
		this.gbz188PageCommService.cleanBsWork(this.selecedSchemeItem.getTsSimpleCode().getRid(), this.selecedSchemeItem
				.getTbZwtjMainstd().getRid());
		this.editBsWake = "";
		JsfUtil.addSuccessMessage("业务提醒清除成功！");
	}

	/**
	 *  <p>方法描述：初始化标准项目组合选项卡列表</p>
	 * @MethodAuthor hsj 2023-07-18 9:30
	 */
	private void initSchemeItems(TbZwtjMainstd mainstd) {
		if (null != mainstd) {
			this.tbZwtjSchemeItemses = this.gbz188PageCommService.getSchemeitemses(mainstd.getRid());
			this.editBsWake = "";
			this.selecedSchemeItem = null;
		}
	}

	/**
	 *  <p>方法描述：添加标准项目组合初始化</p>
	 * @MethodAuthor hsj 2023-07-18 13:45
	 */
	public void addSchemeItemsInit() {
		this.selectCount = 0;
		this.searchItemCode = null;
		this.searchItemName = null;
		this.selectedItemList = new LinkedList<>();
		// 添加项目组合前要先选择标准
		if (null == this.selectedMainstd) {
			JsfUtil.addErrorMessage("请先选择标准！");
			return;
		}
		// SQL语句查询出未添加的项目组合列表
		List<Object[]> restulList = this.gbz188PageCommService.getSelectItemList(this.selectedMainstd.getRid());
		if (null != restulList && restulList.size() > 0) {
			// 初始化待选择列表
			this.selectItemList = new LinkedList<>();
			// 遍历查询结果，组织成可选择的对象列表
			for (Object[] objs : restulList) {
				TbZwtjSchemeItems tbZwtjSchemeItems = new TbZwtjSchemeItems();
				// 创建人，创建时间
				tbZwtjSchemeItems.setCreateDate(new Date());
				tbZwtjSchemeItems.setCreateManid(sessionData.getUser().getRid());
				// 是否必检
				tbZwtjSchemeItems.setIsMust(false);
				// 是否靶器官
				tbZwtjSchemeItems.setIsTargetitem(false);
				// 修改时间修改人
				tbZwtjSchemeItems.setModifyDate(new Date());
				tbZwtjSchemeItems.setModifyManid(sessionData.getUser().getRid());
				// 是否发布
				tbZwtjSchemeItems.setPublishTag((short) 0);
				// 停用标记
				tbZwtjSchemeItems.setStopTag((short) 1);
				// 主表关联
				tbZwtjSchemeItems.setTbZwtjMainstd(this.selectedMainstd);
				// 项目组合码表实体关联
				TsSimpleCode tsSimpleCode = new TsSimpleCode();
				tsSimpleCode.setRid(Integer.valueOf(String.valueOf(objs[0])));
				tsSimpleCode.setCodeNo(String.valueOf(objs[1]));
				tsSimpleCode.setCodeName(String.valueOf(objs[2]));
				tsSimpleCode.setCodeLevelNo(String.valueOf(objs[3]));
				tsSimpleCode.setSplsht(String.valueOf(objs[4]));
				tbZwtjSchemeItems.setTsSimpleCode(tsSimpleCode);
				// 加入待选列表
				this.selectItemList.add(tbZwtjSchemeItems);
			}
		}
		// 初始化时过滤列表等于待选列表
		this.filterSelectItemList = this.selectItemList;
		// 弹出待选框
		RequestContext.getCurrentInstance().execute("PF('schemeItemsDialog').show();");
	}

	/**
	 *  <p>方法描述：根据查询条件过滤可选择的项目</p>
	 * @MethodAuthor hsj 2023-07-18 14:33
	 */
	public void filterSelectItem() {
		// 如果两个查询输入框都为空则不做过滤
		if ((null == this.searchItemCode || this.searchItemCode.trim().length() <= 0)
				&& (null == this.searchItemName || this.searchItemName.trim().length() <= 0)) {
			this.filterSelectItemList = this.selectItemList;
		} else {
			this.filterSelectItemList = new LinkedList<>();
			for (TbZwtjSchemeItems tbZwtjSchemeItems : this.selectItemList) {
				boolean codeFlag = false;
				boolean nameFlag = false;
				String splsht = tbZwtjSchemeItems.getTsSimpleCode().getSplsht();
				String codeName = tbZwtjSchemeItems.getTsSimpleCode().getCodeName();
				// 如果拼音码为空或拼音码符合条件
				if (null == this.searchItemCode || this.searchItemCode.trim().length() <= 0
						|| splsht.toUpperCase().indexOf(this.searchItemCode.toUpperCase().trim()) > -1) {
					codeFlag = true;
				}
				// 如果名称为空或名称符合条件
				if (null == this.searchItemName || this.searchItemName.trim().length() <= 0
						|| codeName.indexOf(this.searchItemName.trim()) > -1) {
					nameFlag = true;
				}

				// 如果拼音码和名称的条件都成立
				if (codeFlag && nameFlag) {
					this.filterSelectItemList.add(tbZwtjSchemeItems);
				}
			}
		}
	}

	/**
	 *  <p>方法描述：选择标准项目组合</p>
	 * @MethodAuthor hsj 2023-07-18 14:34
	 */
	public void selectItemAction() {
		if (null == this.selectedItemList) {
			this.selectedItemList = new LinkedList<>();
		}
		this.selectedItemList.add(this.selectTbzwItems);
		this.filterSelectItemList.remove(this.selectTbzwItems);
		this.selectCount += 1;
	}

	/**
	 *  <p>方法描述：添加职业禁忌证初始化（弹出框初始化）</p>
	 * @MethodAuthor hsj 2023-07-18 15:42
	 */
	public void addJjzInit() {
		this.selectCount = 0;
		// 添加职业禁忌证前要先选择标准
		if (null == this.selectedMainstd) {
			JsfUtil.addErrorMessage("请先选择标准！");
			return;
		}
		this.searchJjsCode = null;
		this.searchJjzName = null;
		this.selectedJjzList = new LinkedList<>();
		List<Object[]> findJjzList = this.gbz188PageCommService.findJjzList(this.selectedMainstd.getRid());
		if (null != findJjzList && findJjzList.size() > 0) {
			this.selectJjzList = new LinkedList<>();
			for (Object[] objs : findJjzList) {
				TbZwtjJjzs tbZwtjJjzs = new TbZwtjJjzs();
				// 创建人，创建时间，修改人，修改时间
				tbZwtjJjzs.setCreateDate(new Date());
				tbZwtjJjzs.setCreateManid(sessionData.getUser().getRid());
				tbZwtjJjzs.setModifyDate(new Date());
				tbZwtjJjzs.setModifyManid(sessionData.getUser().getRid());
				// 发布标记
				tbZwtjJjzs.setPublishTag((short) 0);
				// 停用标记
				tbZwtjJjzs.setStopTag((short) 1);
				// 标准主表
				tbZwtjJjzs.setTbZwtjMainstd(this.selectedMainstd);
				// 禁忌证码表
				TsSimpleCode tsSimpleCode = new TsSimpleCode();
				tsSimpleCode.setRid(Integer.valueOf(String.valueOf(objs[0])));
				tsSimpleCode.setCodeNo(String.valueOf(objs[1]));
				tsSimpleCode.setCodeName(String.valueOf(objs[2]));
				tsSimpleCode.setSplsht(String.valueOf(objs[3]));
				tbZwtjJjzs.setTsSimpleCode(tsSimpleCode);
				this.selectJjzList.add(tbZwtjJjzs);
			}
			this.filterSelectJjzList = this.selectJjzList;
		}
		RequestContext.getCurrentInstance().execute("PF('jjzDialog').show();");

	}

	/**
	 *  <p>方法描述：过滤可选择的职业禁忌证列表</p>
	 * @MethodAuthor hsj 2023-07-18 15:44
	 */
	public void filterSelectJjz() {
		// 如果两个查询输入框都为空则不做过滤
		if ((null == this.searchJjsCode || this.searchJjsCode.trim().length() <= 0)
				&& (null == this.searchJjzName || this.searchJjzName.trim().length() <= 0)) {
			this.filterSelectJjzList = this.selectJjzList;
		} else {
			this.filterSelectJjzList = new LinkedList<>();
			for (TbZwtjJjzs tbZwtjJjzs : this.selectJjzList) {
				boolean codeFlag = false;
				boolean nameFlag = false;
				String splsht = tbZwtjJjzs.getTsSimpleCode().getSplsht();
				String codeName = tbZwtjJjzs.getTsSimpleCode().getCodeName();
				// 如果拼音码为空或拼音码符合条件
				if (null == this.searchJjsCode || this.searchJjsCode.trim().length() <= 0
						|| splsht.toUpperCase().indexOf(this.searchJjsCode.toUpperCase().trim()) > -1) {
					codeFlag = true;
				}
				// 如果名称为空或名称符合条件
				if (null == this.searchJjzName || this.searchJjzName.trim().length() <= 0
						|| codeName.indexOf(this.searchJjzName.trim()) > -1) {
					nameFlag = true;
				}

				// 如果拼音码和名称的条件都成立
				if (codeFlag && nameFlag) {
					this.filterSelectJjzList.add(tbZwtjJjzs);
				}
			}
		}
	}

	/**
	 *  <p>方法描述：选择职业禁忌证</p>
	 * @MethodAuthor hsj 2023-07-18 15:45
	 */
	public void selectJjzAction() {
		if (null == this.selectedJjzList) {
			this.selectedJjzList = new LinkedList<>();
		}
		this.selectedJjzList.add(this.selectTbzwJjs);
		this.filterSelectJjzList.remove(this.selectTbzwJjs);
		this.selectCount += 1;
	}

	/**
	 *  <p>方法描述：保存选择的职业禁忌证</p>
	 * @MethodAuthor hsj 2023-07-18 15:45
	 */
	public void saveJjzs() {
		if (!CollectionUtils.isEmpty(this.selectedJjzList)) {
			this.gbz188PageCommService.saveJjzList(selectedJjzList);
			this.initJjzs(selectedMainstd);
			JsfUtil.addSuccessMessage("职业禁忌症添加成功！");
		}
		RequestContext.getCurrentInstance().execute("PF('jjzDialog').hide();");
	}

	/**
	 *  <p>方法描述：添加疑似职业病初始化（弹出框初始化）</p>
	 * @MethodAuthor hsj 2023-07-18 15:49
	 */
	public void addOccInit() {
		this.selectCount = 0;
		// 添加疑似职业病前要先选择标准
		if (null == this.selectedMainstd) {
			JsfUtil.addErrorMessage("请先选择标准！");
			return;
		}
		this.searchOccCode = null;
		this.searchOccName = null;
		this.selectedOccList = new LinkedList<>();

		List<Object[]> findOccList = this.gbz188PageCommService.findOccList(this.selectedMainstd.getRid());
		if (null != findOccList && findOccList.size() > 0) {
			this.selectOccList = new LinkedList<>();
			for (Object[] objs : findOccList) {
				TbZwtjOccdises tbZwtjOccdises = new TbZwtjOccdises();
				// 创建人，创建时间，修改人，修改时间
				tbZwtjOccdises.setCreateDate(new Date());
				tbZwtjOccdises.setCreateManid(sessionData.getUser().getRid());
				tbZwtjOccdises.setModifyDate(new Date());
				tbZwtjOccdises.setModifyManid(sessionData.getUser().getRid());
				// 发布标记
				tbZwtjOccdises.setPublishTag((short) 0);
				// 停用标记
				tbZwtjOccdises.setStopTag((short) 1);
				// 标准主表
				tbZwtjOccdises.setTbZwtjMainstd(this.selectedMainstd);
				// 禁忌证码表
				TsSimpleCode tsSimpleCode = new TsSimpleCode();
				tsSimpleCode.setRid(Integer.valueOf(String.valueOf(objs[0])));
				tsSimpleCode.setCodeNo(String.valueOf(objs[1]));
				tsSimpleCode.setCodeName(String.valueOf(objs[2]));
				tsSimpleCode.setCodeLevelNo(String.valueOf(objs[3]));
				tsSimpleCode.setSplsht(String.valueOf(objs[4]));
				tbZwtjOccdises.setTsSimpleCode(tsSimpleCode);
				this.selectOccList.add(tbZwtjOccdises);
			}
			this.filterSelectOccList = this.selectOccList;
		}
		RequestContext.getCurrentInstance().execute("PF('occDialog').show();");
	}

	/**
	 *  <p>方法描述：过滤查询职业病列表</p>
	 * @MethodAuthor hsj 2023-07-18 15:51
	 */
	public void filterSelectOcc() {
		// 如果两个查询输入框都为空则不做过滤
		if ((null == this.searchOccCode || this.searchOccCode.trim().length() <= 0)
				&& (null == this.searchOccName || this.searchOccName.trim().length() <= 0)) {
			this.filterSelectOccList = this.selectOccList;
		} else {
			this.filterSelectOccList = new LinkedList<>();
			for (TbZwtjOccdises tbZwtjOccdises : this.selectOccList) {
				boolean codeFlag = false;
				boolean nameFlag = false;
				String splsht = tbZwtjOccdises.getTsSimpleCode().getSplsht();
				String codeName = tbZwtjOccdises.getTsSimpleCode().getCodeName();
				// 如果拼音码为空或拼音码符合条件
				if (null == this.searchOccCode || this.searchOccCode.trim().length() <= 0
						|| splsht.toUpperCase().indexOf(this.searchOccCode.toUpperCase().trim()) > -1) {
					codeFlag = true;
				}
				// 如果名称为空或名称符合条件
				if (null == this.searchOccName || this.searchOccName.trim().length() <= 0
						|| codeName.indexOf(this.searchOccName.trim()) > -1) {
					nameFlag = true;
				}

				// 如果拼音码和名称的条件都成立
				if (codeFlag && nameFlag) {
					this.filterSelectOccList.add(tbZwtjOccdises);
				}
			}
		}
	}

	/**
	 *  <p>方法描述：选择职业病</p>
	 * @MethodAuthor hsj 2023-07-18 15:51
	 */
	public void selectOccAction() {
		if (null == this.selectedOccList) {
			this.selectedOccList = new LinkedList<>();
		}
		this.selectedOccList.add(this.selectTbzwOcc);
		this.filterSelectOccList.remove(this.selectTbzwOcc);
		this.selectCount += 1;
	}

	/**
	 *  <p>方法描述：保存选择的职业病</p>
	 * @MethodAuthor hsj 2023-07-18 15:51
	 */
	public void saveOccs() {
		if (!CollectionUtils.isEmpty(this.selectedOccList)) {
			this.gbz188PageCommService.saveOccList(selectedOccList);
			this.initOccdises(selectedMainstd);
			JsfUtil.addSuccessMessage("疑似职业病添加成功！");
		}
		RequestContext.getCurrentInstance().execute("PF('occDialog').hide();");
	}

	/**
	 *  <p>方法描述：添加询问项目列表</p>
	 * @MethodAuthor hsj 2023-07-18 15:53
	 */
	public void addAskItemInit() {
		this.selectCount = 0;
		// 添加职业禁忌证前要先选择标准
		if (null == this.selectedMainstd) {
			JsfUtil.addErrorMessage("请先选择标准！");
			return;
		}
		this.selectedAskItemList = new LinkedList<>();

		this.searchAskItemCode = null;
		this.searchAskItemName = null;
		this.selectAskItemList = new LinkedList<>();

		List<Object[]> findAskListList = this.gbz188PageCommService.findAskItemList(this.selectedMainstd.getRid());
		if (null != findAskListList && findAskListList.size() > 0) {
			this.selectAskItemList = new LinkedList<>();
			for (Object[] objs : findAskListList) {
				TbZwtjAskitems tbZwtjAskitems = new TbZwtjAskitems();
				// 创建人，创建时间，修改人，修改时间
				tbZwtjAskitems.setCreateDate(new Date());
				tbZwtjAskitems.setCreateManid(sessionData.getUser().getRid());
				tbZwtjAskitems.setModifyDate(new Date());
				tbZwtjAskitems.setModifyManid(sessionData.getUser().getRid());
				// 发布标记
				tbZwtjAskitems.setPublishTag((short) 0);
				// 停用标记
				tbZwtjAskitems.setStopTag((short) 1);
				// 标准主表
				tbZwtjAskitems.setTbZwtjMainstd(this.selectedMainstd);
				// 禁忌证码表
				TsSimpleCode tsSimpleCode = new TsSimpleCode();
				tsSimpleCode.setRid(Integer.valueOf(String.valueOf(objs[0])));
				tsSimpleCode.setCodeNo(String.valueOf(objs[1]));
				tsSimpleCode.setCodeName(String.valueOf(objs[2]));
				tsSimpleCode.setCodeLevelNo(String.valueOf(objs[3]));
				tsSimpleCode.setSplsht(String.valueOf(objs[4]));
				tbZwtjAskitems.setTsSimpleCode(tsSimpleCode);
				this.selectAskItemList.add(tbZwtjAskitems);
			}
			this.filterSelectAskItemList = this.selectAskItemList;
		}
		RequestContext.getCurrentInstance().execute("PF('askItemDialog').show();");
	}

	/**
	 *  <p>方法描述：过滤可选择的询问项目列表</p>
	 * @MethodAuthor hsj 2023-07-18 16:02
	 */
	public void filterSelectaskItem() {
		// 如果两个查询输入框都为空则不做过滤
		if ((null == this.searchAskItemCode || this.searchAskItemCode.trim().length() <= 0)
				&& (null == this.searchAskItemName || this.searchAskItemName.trim().length() <= 0)) {
			this.filterSelectAskItemList = this.selectAskItemList;
		} else {
			this.filterSelectAskItemList = new LinkedList<>();
			for (TbZwtjAskitems tbZwtjAskitems : this.selectAskItemList) {
				String splsht = tbZwtjAskitems.getTsSimpleCode().getSplsht();
				String codeName = tbZwtjAskitems.getTsSimpleCode().getCodeName();
				// 如果拼音码为空或编码符合条件
				boolean codeFlag = null == this.searchAskItemCode || this.searchAskItemCode.trim().length() <= 0
						|| splsht.toUpperCase().indexOf(this.searchAskItemCode.toUpperCase().trim()) > -1;
				// 如果名称为空或名称符合条件
				boolean nameFlag = null == this.searchAskItemName || this.searchAskItemName.trim().length() <= 0
						|| codeName.indexOf(this.searchAskItemName.trim()) > -1;

				// 如果拼音码和名称的条件都成立
				if (codeFlag && nameFlag) {
					this.filterSelectAskItemList.add(tbZwtjAskitems);
				}
			}
		}
	}

	/**
	 *  <p>方法描述：选择症状事件</p>
	 * @MethodAuthor hsj 2023-07-18 16:04
	 */
	public void selectaskItemAction() {
		if (null == this.selectedAskItemList) {
			this.selectedAskItemList = new LinkedList<TbZwtjAskitems>();
		}
		this.selectedAskItemList.add(this.selectTbzwAskItem);
		this.filterSelectAskItemList.remove(this.selectTbzwAskItem);
		this.selectCount += 1;
	}

	/**
	 *  <p>方法描述：保存选择的症状</p>
	 * @MethodAuthor hsj 2023-07-18 15:58
	 */
	public void saveAskItems() {
		if (!CollectionUtils.isEmpty( this.selectedAskItemList)) {
			this.gbz188PageCommService.saveAskItemList(selectedAskItemList);
			this.initAskItems(selectedMainstd);
			JsfUtil.addSuccessMessage("询问症状添加成功！");
		}
		RequestContext.getCurrentInstance().execute("PF('askItemDialog').hide();");
	}

	/**
	 *  <p>方法描述：停用GBZ 188标准，根据操作子表的名称和RID</p>
	 * @MethodAuthor hsj 2023-07-18 14:39
	 */
	public void stopGbz() {
		this.gbz188PageCommService.stopGbz(this.optTableName, this.optRid, sessionData.getUser().getRid());
		this.tableListUpdate(this.selectedMainstd);
		JsfUtil.addSuccessMessage("停用成功！");
	}

	/**
	 *  <p>方法描述：启用GBZ 188标准，根据操作子表的名称和RID</p>
	 * @MethodAuthor hsj 2023-07-18 15:59
	 */
	public void powerGbz() {
		this.gbz188PageCommService.powerGbz(this.optTableName, this.optRid, sessionData.getUser().getRid());
		this.tableListUpdate(this.selectedMainstd);
		JsfUtil.addSuccessMessage("启用成功！");
	}

	/**
	 *  <p>方法描述： 删除GBZ 188标准，根据操作子表的名称和RID</p>
	 * @MethodAuthor hsj 2023-07-18 14:44
	 */
	public void delGbz() {
		this.gbz188PageCommService.delGbz(this.optTableName, this.optRid);
		this.tableListUpdate(this.selectedMainstd);
		JsfUtil.addSuccessMessage("删除成功！");
	}

	/**
	 *  <p>方法描述：保存标准项目组合列表</p>
	 * @MethodAuthor hsj 2023-07-18 14:34
	 */
	public void saveSchemeItems() {
		if (!CollectionUtils.isEmpty(selectedItemList)) {
			this.gbz188PageCommService.saveItemList(this.selectedItemList);
			this.initSchemeItems(selectedMainstd);
			JsfUtil.addSuccessMessage("标准项目组合添加成功！");
		}
		RequestContext.getCurrentInstance().execute("PF('schemeItemsDialog').hide();");

	}

	/**
	 *  <p>方法描述：初始化职业禁忌证选项卡</p>
	 * @MethodAuthor hsj 2023-07-18 9:33
	 */
	private void initJjzs(TbZwtjMainstd mainstd) {
		if (null != mainstd) {
			this.tbZwtjJjzs = this.gbz188PageCommService.getTbZwtjjjzsList(mainstd.getRid());
		}
	}

	/**
	 *  <p>方法描述：初始化疑似职业病选项卡</p>
	 * @MethodAuthor hsj 2023-07-18 9:35
	 */
	private void initOccdises(TbZwtjMainstd mainstd) {
		if (null != mainstd) {
			this.tbZwtjOccdises = this.gbz188PageCommService.getTbZwtjoccList(mainstd.getRid());
		}
	}

	/**
	 *  <p>方法描述：初始化询问症状选项卡</p>
	 * @MethodAuthor hsj 2023-07-18 11:30
	 */
	private void initAskItems(TbZwtjMainstd mainstd) {
		this.tbZwtjAskitemses = this.gbz188PageCommService.getTbAskItemList(mainstd.getRid());
	}

	/**
	 *  <p>方法描述：<选择显示停用/p>
	 * @MethodAuthor hsj 2023-07-18 9:26
	 */
	public void showStopChange() {
		this.searchAction();
		this.filterBadRsnList = this.badRsnList;
	}

	@Override
	public void addInit() {

	}

	@Override
	public void viewInit() {

	}

	@Override
	public void modInit() {

	}

	@Override
	public void saveAction() {

	}

	@Override
	public String[] buildHqls() {

		return null;
	}

	public SessionData getSessionData() {
		return sessionData;
	}

	public void setSessionData(SessionData sessionData) {
		this.sessionData = sessionData;
	}

	public List<TbZwtjMainstd> getBadRsnList() {
		return badRsnList;
	}

	public void setBadRsnList(List<TbZwtjMainstd> badRsnList) {
		this.badRsnList = badRsnList;
	}

	public List<TbZwtjMainstd> getFilterBadRsnList() {
		return filterBadRsnList;
	}

	public void setFilterBadRsnList(List<TbZwtjMainstd> filterBadRsnList) {
		this.filterBadRsnList = filterBadRsnList;
	}

	public List<SelectItem> getJobStateList() {
		return jobStateList;
	}

	public void setJobStateList(List<SelectItem> jobStateList) {
		this.jobStateList = jobStateList;
	}

	public TbZwtjMainstd getSelectedMainstd() {
		return selectedMainstd;
	}

	public void setSelectedMainstd(TbZwtjMainstd selectedMainstd) {
		this.selectedMainstd = selectedMainstd;
	}

	public List<TbZwtjSchemeItems> getTbZwtjSchemeItemses() {
		return tbZwtjSchemeItemses;
	}

	public void setTbZwtjSchemeItemses(List<TbZwtjSchemeItems> tbZwtjSchemeItemses) {
		this.tbZwtjSchemeItemses = tbZwtjSchemeItemses;
	}

	public List<TbZwtjJjzs> getTbZwtjJjzs() {
		return tbZwtjJjzs;
	}

	public void setTbZwtjJjzs(List<TbZwtjJjzs> tbZwtjJjzs) {
		this.tbZwtjJjzs = tbZwtjJjzs;
	}

	public List<TbZwtjOccdises> getTbZwtjOccdises() {
		return tbZwtjOccdises;
	}

	public void setTbZwtjOccdises(List<TbZwtjOccdises> tbZwtjOccdises) {
		this.tbZwtjOccdises = tbZwtjOccdises;
	}

	public List<TbZwtjAskitems> getTbZwtjAskitemses() {
		return tbZwtjAskitemses;
	}

	public void setTbZwtjAskitemses(List<TbZwtjAskitems> tbZwtjAskitemses) {
		this.tbZwtjAskitemses = tbZwtjAskitemses;
	}

	public String getEditItemName() {
		return editBadRsnName;
	}

	public void setEditItemName(String editItemName) {
		this.editBadRsnName = editItemName;
	}

	public String getEditBadRsnName() {
		return editBadRsnName;
	}

	public void setEditBadRsnName(String editBadRsnName) {
		this.editBadRsnName = editBadRsnName;
	}

	public Integer getEditBadRsnRid() {
		return editBadRsnRid;
	}

	public void setEditBadRsnRid(Integer editBadRsnRid) {
		this.editBadRsnRid = editBadRsnRid;
	}

	public TreeNode getBadRsnTree() {
		return badRsnTree;
	}

	public void setBadRsnTree(TreeNode badRsnTree) {
		this.badRsnTree = badRsnTree;
	}

	public Integer getEditJobstate() {
		return editJobstate;
	}

	public void setEditJobstate(Integer editJobstate) {
		this.editJobstate = editJobstate;
	}

	public TbZwtjMainstd getCurzwtjMainstd() {
		return curzwtjMainstd;
	}

	public void setCurzwtjMainstd(TbZwtjMainstd curzwtjMainstd) {
		this.curzwtjMainstd = curzwtjMainstd;
	}

	public Boolean getEditStopTag() {
		return editStopTag;
	}

	public void setEditStopTag(Boolean editStopTag) {
		this.editStopTag = editStopTag;
	}

	public Integer getSelectCount() {
		return selectCount;
	}

	public void setSelectCount(Integer selectCount) {
		this.selectCount = selectCount;
	}

	public List<TbZwtjSchemeItems> getSelectItemList() {
		return selectItemList;
	}

	public void setSelectItemList(List<TbZwtjSchemeItems> selectItemList) {
		this.selectItemList = selectItemList;
	}

	public String getSearchItemName() {
		return searchItemName;
	}

	public void setSearchItemName(String searchItemName) {
		this.searchItemName = searchItemName;
	}

	public String getSearchItemCode() {
		return searchItemCode;
	}

	public void setSearchItemCode(String searchItemCode) {
		this.searchItemCode = searchItemCode;
	}

	public TbZwtjSchemeItems getSelectTbzwItems() {
		return selectTbzwItems;
	}

	public void setSelectTbzwItems(TbZwtjSchemeItems selectTbzwItems) {
		this.selectTbzwItems = selectTbzwItems;
	}

	public List<TbZwtjSchemeItems> getSelectedItemList() {
		return selectedItemList;
	}

	public void setSelectedItemList(List<TbZwtjSchemeItems> selectedItemList) {
		this.selectedItemList = selectedItemList;
	}

	public String getOptTableName() {
		return optTableName;
	}

	public void setOptTableName(String optTableName) {
		this.optTableName = optTableName;
	}

	public Integer getOptRid() {
		return optRid;
	}

	public void setOptRid(Integer optRid) {
		this.optRid = optRid;
	}

	public List<TbZwtjSchemeItems> getFilterSelectItemList() {
		return filterSelectItemList;
	}

	public void setFilterSelectItemList(List<TbZwtjSchemeItems> filterSelectItemList) {
		this.filterSelectItemList = filterSelectItemList;
	}

	public String getEditBsWake() {
		return editBsWake;
	}

	public void setEditBsWake(String editBsWake) {
		this.editBsWake = editBsWake;
	}

	public TbZwtjSchemeItems getSelecedSchemeItem() {
		return selecedSchemeItem;
	}

	public void setSelecedSchemeItem(TbZwtjSchemeItems selecedSchemeItem) {
		this.selecedSchemeItem = selecedSchemeItem;
	}

	public String getActionTabTitle() {
		return actionTabTitle;
	}

	public void setActionTabTitle(String actionTabTitle) {
		this.actionTabTitle = actionTabTitle;
	}

	public List<TbZwtjJjzs> getSelectJjzList() {
		return selectJjzList;
	}

	public void setSelectJjzList(List<TbZwtjJjzs> selectJjzList) {
		this.selectJjzList = selectJjzList;
	}

	public List<TbZwtjJjzs> getFilterSelectJjzList() {
		return filterSelectJjzList;
	}

	public void setFilterSelectJjzList(List<TbZwtjJjzs> filterSelectJjzList) {
		this.filterSelectJjzList = filterSelectJjzList;
	}

	public TbZwtjJjzs getSelectTbzwJjs() {
		return selectTbzwJjs;
	}

	public void setSelectTbzwJjs(TbZwtjJjzs selectTbzwJjs) {
		this.selectTbzwJjs = selectTbzwJjs;
	}

	public String getSearchJjsCode() {
		return searchJjsCode;
	}

	public void setSearchJjsCode(String searchJjsCode) {
		this.searchJjsCode = searchJjsCode;
	}

	public String getSearchJjzName() {
		return searchJjzName;
	}

	public void setSearchJjzName(String searchJjzName) {
		this.searchJjzName = searchJjzName;
	}

	public LinkedList<TbZwtjJjzs> getSelectedJjzList() {
		return selectedJjzList;
	}

	public void setSelectedJjzList(LinkedList<TbZwtjJjzs> selectedJjzList) {
		this.selectedJjzList = selectedJjzList;
	}

	public List<TbZwtjOccdises> getSelectOccList() {
		return selectOccList;
	}

	public void setSelectOccList(List<TbZwtjOccdises> selectOccList) {
		this.selectOccList = selectOccList;
	}

	public List<TbZwtjOccdises> getFilterSelectOccList() {
		return filterSelectOccList;
	}

	public void setFilterSelectOccList(List<TbZwtjOccdises> filterSelectOccList) {
		this.filterSelectOccList = filterSelectOccList;
	}

	public TbZwtjOccdises getSelectTbzwOcc() {
		return selectTbzwOcc;
	}

	public void setSelectTbzwOcc(TbZwtjOccdises selectTbzwOcc) {
		this.selectTbzwOcc = selectTbzwOcc;
	}

	public String getSearchOccCode() {
		return searchOccCode;
	}

	public void setSearchOccCode(String searchOccCode) {
		this.searchOccCode = searchOccCode;
	}

	public String getSearchOccName() {
		return searchOccName;
	}

	public void setSearchOccName(String searchOccName) {
		this.searchOccName = searchOccName;
	}

	public LinkedList<TbZwtjOccdises> getSelectedOccList() {
		return selectedOccList;
	}

	public void setSelectedOccList(LinkedList<TbZwtjOccdises> selectedOccList) {
		this.selectedOccList = selectedOccList;
	}

	public List<TbZwtjAskitems> getSelectAskItemList() {
		return selectAskItemList;
	}

	public void setSelectAskItemList(List<TbZwtjAskitems> selectAskItemList) {
		this.selectAskItemList = selectAskItemList;
	}

	public List<TbZwtjAskitems> getFilterSelectAskItemList() {
		return filterSelectAskItemList;
	}

	public void setFilterSelectAskItemList(List<TbZwtjAskitems> filterSelectAskItemList) {
		this.filterSelectAskItemList = filterSelectAskItemList;
	}

	public TbZwtjAskitems getSelectTbzwAskItem() {
		return selectTbzwAskItem;
	}

	public void setSelectTbzwAskItem(TbZwtjAskitems selectTbzwAskItem) {
		this.selectTbzwAskItem = selectTbzwAskItem;
	}

	public String getSearchAskItemCode() {
		return searchAskItemCode;
	}

	public void setSearchAskItemCode(String searchAskItemCode) {
		this.searchAskItemCode = searchAskItemCode;
	}

	public String getSearchAskItemName() {
		return searchAskItemName;
	}

	public void setSearchAskItemName(String searchAskItemName) {
		this.searchAskItemName = searchAskItemName;
	}

	public LinkedList<TbZwtjAskitems> getSelectedAskItemList() {
		return selectedAskItemList;
	}

	public void setSelectedAskItemList(LinkedList<TbZwtjAskitems> selectedAskItemList) {
		this.selectedAskItemList = selectedAskItemList;
	}

	public boolean isShowStop() {
		return showStop;
	}

	public void setShowStop(boolean showStop) {
		this.showStop = showStop;
	}

	public String getFilterBadrsn() {
		return filterBadrsn;
	}

	public void setFilterBadrsn(String filterBadrsn) {
		this.filterBadrsn = filterBadrsn;
	}

	public String getFilterJobState() {
		return filterJobState;
	}

	public void setFilterJobState(String filterJobState) {
		this.filterJobState = filterJobState;
	}

}
