package com.chis.modules.heth.comm.entity;

import java.math.BigInteger;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 主检主题结论
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置<br/>
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 */
@Entity
@Table(name = "TD_TJ_MHKRST", uniqueConstraints = @UniqueConstraint(columnNames = {
        "BHK_ID", "BHKRST_ID" }))
@SequenceGenerator(name = "TdTjMhkrst_Seq", sequenceName = "TD_TJ_MHKRST_SEQ", allocationSize = 1)
public class TdTjMhkrst implements java.io.Serializable {

    private static final long serialVersionUID = 6043186928373098282L;
    private BigInteger rid;
    private TsSimpleCode tsSimpleCode;
    private TdTjBhk tdTjBhk;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    public TdTjMhkrst() {
    }

    public TdTjMhkrst(BigInteger rid, TdTjBhk tdTjBhk, Date createDate,
                      Integer createManid) {
        this.rid = rid;
        this.tdTjBhk = tdTjBhk;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    public TdTjMhkrst(BigInteger rid, TsSimpleCode tsSimpleCode, TdTjBhk tdTjBhk,
                      Date createDate, Integer createManid, Date modifyDate,
                      Integer modifyManid) {
        this.rid = rid;
        this.tsSimpleCode = tsSimpleCode;
        this.tdTjBhk = tdTjBhk;
        this.createDate = createDate;
        this.createManid = createManid;
        this.modifyDate = modifyDate;
        this.modifyManid = modifyManid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjMhkrst_Seq")
    public BigInteger getRid() {
        return this.rid;
    }

    public void setRid(BigInteger rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "BHKRST_ID")
    public TsSimpleCode getTsSimpleCode() {
        return this.tsSimpleCode;
    }

    public void setTsSimpleCode(TsSimpleCode tsSimpleCode) {
        this.tsSimpleCode = tsSimpleCode;
    }

    @ManyToOne
    @JoinColumn(name = "BHK_ID" )
    public TdTjBhk getTdTjBhk() {
        return this.tdTjBhk;
    }

    public void setTdTjBhk(TdTjBhk tdTjBhk) {
        this.tdTjBhk = tdTjBhk;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }
}
