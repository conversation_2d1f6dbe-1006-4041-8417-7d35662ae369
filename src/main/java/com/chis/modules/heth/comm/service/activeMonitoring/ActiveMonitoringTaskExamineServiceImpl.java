package com.chis.modules.heth.comm.service.activeMonitoring;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.entity.TbTjJcTask;
import com.chis.modules.heth.comm.entity.TdZwBgkLastSta;
import com.chis.modules.heth.comm.logic.TaskExamineConditionPO;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 *  <p>类描述：主动监测任务审核</p>
 * @ClassAuthor hsj 2023-07-20 14:04
 */
@Service
public class ActiveMonitoringTaskExamineServiceImpl extends AbstractTemplate {

    /**
     *  <p>方法描述：行业类别 ，经济类型弹出框</p>
     * @MethodAuthor hsj 2023-07-20 14:35
     */
    @Transactional(readOnly = true)
    public void selSimpleCodeAction(TaskExamineConditionPO taskExamineCondition, String simpleCodeOpType) {

        String titleName = "";
        String selectIds = "";
        String dialogUrl = "";
        Integer width = null;
        Integer contentWidth = null;
        Integer height = null;
        Integer contentHeight = null;
        boolean ifShowFirstCode = false;
        String type = "";
        switch (simpleCodeOpType) {
            case "5002":
                titleName = "行业类别";
                selectIds = taskExamineCondition.getIndusTypeIds();
                contentWidth = 700;
                contentHeight = 500;
                type = "1";
                dialogUrl = "/webapp/heth/comm/dialog/codeMulitySelectTaskList";
                break;
            case "5003":
                titleName = "经济类型";
                selectIds = taskExamineCondition.getEconomyIds();
                dialogUrl = "/webapp/system/codeMulitySelectList";
                contentWidth = 700;
                contentHeight = 500;
                ifShowFirstCode = true;
                break;
            default:
                break;
        }
        Map<String, Object> options = MapUtils.produceDialogMap(width, contentWidth, height, contentHeight);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add(titleName);
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>();
        paramList.add(simpleCodeOpType);
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<>();
        paramList.add(selectIds);
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<>();
        paramList.add(StringUtils.objectToString(ifShowFirstCode));
        paramMap.put("ifShowFirstCode", paramList);
        paramList = new ArrayList<>();
        paramList.add(type);
        paramMap.put("type", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog(dialogUrl, options, paramMap);
    }
    /**
     *  <p>方法描述：查询sql</p>
     * @MethodAuthor hsj 2023-07-20 14:41
     */
    public String[] searchDatas(TaskExamineConditionPO taskExamineCondition, Map<String, Object> paramMap) {
        String dataSql = "SELECT T.RID, " +
                "       T2.FULL_NAME   AS ZONE_NAME, " +
                "       T1.UNITNAME, " +
                "       T.CRPT_NAME, " +
                "       T.CREDIT_CODE, " +
                "       T3.CODE_NAME AS INDUS_TYPE, " +
                "       T4.CODE_NAME AS ECONOMY, " +
                "       T5.CODE_NAME AS CRPT_SIZE, " +
                "       '', " +
                "       T.BACK_RSN, " +
                "       T.STATE, "+
                "       T.CRPT_ID, "+
                "       T.YEAR ";
        StringBuilder sql = new StringBuilder();
        sql.append(" FROM TB_TJ_JC_TASK T");
        sql.append(" LEFT JOIN TS_UNIT T1 ON T.ORG_ID = T1.RID");
        sql.append("  LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.INDUS_TYPE_ID = T3.RID");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T.ECONOMY_ID = T4.RID");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T5 ON T.CRPT_SIZE_ID = T5.RID");
        sql.append(" WHERE NVL(T.DEL_MARK, 0) = 0");
        //地区
        if (ObjectUtil.isNotEmpty(taskExamineCondition.getZoneCode())) {
            sql.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(taskExamineCondition.getZoneCode())).append("%'");
        }
        //机构名称
        if (ObjectUtil.isNotEmpty(taskExamineCondition.getOrgName())) {
            paramMap.put("orgName", "%" + StringUtils.convertBFH(taskExamineCondition.getOrgName()) + "%");
            sql.append( " AND T1.UNITNAME LIKE :orgName escape '\\' ");
        }
        //年份
        if (ObjectUtil.isNotEmpty(taskExamineCondition.getYear())) {
            paramMap.put("year", taskExamineCondition.getYear());
            sql.append("AND T.YEAR = :year ");
        }
        //单位名称
        if (ObjectUtil.isNotEmpty(taskExamineCondition.getCrptName())) {
            paramMap.put("crptName", "%" + StringUtils.convertBFH(taskExamineCondition.getCrptName()) + "%");
            sql.append(" AND T.CRPT_NAME LIKE :crptName escape '\\' ");
        }
        //社会信用代码
        if (ObjectUtil.isNotEmpty(taskExamineCondition.getCreditCode())) {
            paramMap.put("creditCode", "%" + StringUtils.convertBFH(taskExamineCondition.getCreditCode().trim()) + "%");
            sql.append(" AND T.CREDIT_CODE LIKE :creditCode escape '\\' ");
        }
        //行业类别
        if (StringUtils.isNotBlank(taskExamineCondition.getIndusTypeIds())) {
            List<String> indusTypeIdList = StringUtils.string2list(taskExamineCondition.getIndusTypeIds(), ",");
            paramMap.put("indusTypeIdList", indusTypeIdList);
            sql.append("  AND T.INDUS_TYPE_ID IN (:indusTypeIdList) ");
        }
        //经济类型
        if (StringUtils.isNotBlank(taskExamineCondition.getEconomyIds())) {
            List<String> economyIdList = StringUtils.string2list(taskExamineCondition.getEconomyIds(), ",");
            paramMap.put("economyIdList", economyIdList);
            sql.append(" AND T.ECONOMY_ID IN (:economyIdList) ");
        }
        //企业规模
        if (StringUtils.isNotBlank(taskExamineCondition.getCrptSizeIds())) {
            List<String> crptSizeIdList = StringUtils.string2list(taskExamineCondition.getCrptSizeIds(), ",");
            paramMap.put("crptSizeIdList", crptSizeIdList);
            sql.append(" AND T.CRPT_SIZE_ID IN (:crptSizeIdList) ");
        }
        //姓名&身份证号
        if (ObjectUtil.isNotEmpty(taskExamineCondition.getPsnName()) && ObjectUtil.isNotEmpty(taskExamineCondition.getIdc())) {
            paramMap.put("psnName", "%" + StringUtils.convertBFH(taskExamineCondition.getPsnName().trim()) + "%");
            paramMap.put("idc", taskExamineCondition.getIdc());
            sql.append(" AND EXISTS(SELECT 1 FROM TB_TJ_JC_TASK_PSN P WHERE T.RID = P.MAIN_ID AND P.PSN_NAME LIKE :psnName escape '\\' AND P.IDC = :idc) " );
        } else if (ObjectUtil.isNotEmpty(taskExamineCondition.getPsnName())) {
            paramMap.put("psnName", "%" + StringUtils.convertBFH(taskExamineCondition.getPsnName().trim()) + "%");
            sql.append(" AND EXISTS(SELECT 1 FROM TB_TJ_JC_TASK_PSN P WHERE T.RID = P.MAIN_ID AND P.PSN_NAME LIKE :psnName escape '\\') ");
        } else if (ObjectUtil.isNotEmpty(taskExamineCondition.getIdc())) {
            paramMap.put("idc", taskExamineCondition.getIdc());
            sql.append(" AND EXISTS(SELECT 1 FROM TB_TJ_JC_TASK_PSN P WHERE T.RID = P.MAIN_ID AND P.IDC = :idc) ");
        }
        //状态
        if (ObjectUtil.isNotEmpty(taskExamineCondition.getState())) {
            List<Integer> searchStateList = new ArrayList<>(Arrays.asList(taskExamineCondition.getState()));
            paramMap.put("searchStateList", searchStateList);
            sql.append(" AND T.STATE IN (:searchStateList) ");
        }
        String countSql = "SELECT COUNT(1) " + sql;
        dataSql += sql + "ORDER BY  T2.ZONE_GB, T1.UNITNAME,T.CRPT_NAME, T.CREDIT_CODE";
        return new String[]{dataSql, countSql};
    }

    /**
     *  <p>方法描述：根据rid查询状态</p>
     * @MethodAuthor hsj 2023-07-20 15:43
     */
    @Transactional(readOnly = true)
    public String findStateByRid(Integer rid) {
        if(rid == null){
            return null;
        }
        String sql = "SELECT STATE FROM TB_TJ_JC_TASK WHERE RID = "+ rid;
        Object singleResult = em.createNativeQuery(sql.toString()).getSingleResult();
        if(singleResult == null ){
            return null;
        }
        return singleResult.toString();
    }

    /**
     *  <p>方法描述：审核通过</p>
     * @MethodAuthor hsj 2023-07-20 15:50
     */
    @Transactional(readOnly = false)
    public void examineTask(TbTjJcTask jcTask) {
        if(jcTask == null || jcTask.getRid() == null){
            return;
        }
        String sql = "UPDATE  TB_TJ_JC_TASK SET STATE = 2  WHERE STATE = 1 AND RID = "+ jcTask.getRid();
        this.executeSql(sql,null);
        //更新审核日期
        sql = "UPDATE TB_TJ_JC_TASK_PSN SET MODIFY_DATE = TO_DATE('"+ DateUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss") +"','YYYY-MM-DD HH24:MI:SS') WHERE BHK_CODE IS NULL AND MAIN_ID ="+jcTask.getRid();
        this.executeSql(sql,null);
        //新增用人单位审核流程
        saveOrUpdateLastSta(jcTask.getFkByCrptId().getRid());
    }
    /**
     *  <p>方法描述：新增用人单位审核流程</p>
     * @MethodAuthor hsj 2023-07-20 15:55
     */
    @Transactional(readOnly = false)
    private void saveOrUpdateLastSta(Integer crptId) {
        TdZwBgkLastSta tdZwBgkLastSta = findTdZwBgkLastStaInfoByBusIdAndCardType(crptId, 10);
        if(tdZwBgkLastSta != null &&  tdZwBgkLastSta.getRid() != null){
            return;
        }
        tdZwBgkLastSta = new TdZwBgkLastSta();
        tdZwBgkLastSta.setBusId(crptId);
        tdZwBgkLastSta.setCartType(10);
        this.upsertEntity(tdZwBgkLastSta);
    }

    /**
     *  <p>方法描述：查询最新流程状态</p>
     * @MethodAuthor hsj 2023-07-20 16:05
     */
    @Transactional(readOnly = true)
    public TdZwBgkLastSta findTdZwBgkLastStaInfoByBusIdAndCardType(Integer budId, Integer cartType) {
        if(null == budId){
            return null;
        }
        StringBuilder sb = new StringBuilder();
        sb.append(" select t from TdZwBgkLastSta t ");
        sb.append(" where t.busId = ").append(budId);
        sb.append(" and t.cartType = ").append(cartType);
        List<TdZwBgkLastSta> list = em.createQuery(sb.toString()).getResultList();
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    /**
     *  <p>方法描述：主动监测任务退回</p>
     * @MethodAuthor hsj 2023-07-20 16:17
     */
    @Transactional(readOnly = false)
    public void returnTask(TbTjJcTask jcTask) {
        if(jcTask == null || jcTask.getRid() == null){
            return;
        }
        String sql = "UPDATE  TB_TJ_JC_TASK SET STATE = 3,BACK_RSN = '"+jcTask.getBackRsn()+"' WHERE RID = "+ jcTask.getRid();
        this.executeSql(sql,null);
    }


    /**
     *  <p>方法描述：批量审核任务</p>
     * @MethodAuthor hsj 2023-07-20 17:02
     */
    @Transactional(readOnly = false)
    public void batchExamineTask(List<Object[]> selectEntitys) {
        if(CollectionUtils.isEmpty(selectEntitys)){
            return;
        }
        for(Object[] obj : selectEntitys){
            TbTjJcTask jcTask = new TbTjJcTask();
            jcTask.setRid(Integer.parseInt(ObjectUtil.toStr(obj[0])));
            jcTask.setFkByCrptId(new TbTjCrpt(Integer.parseInt(ObjectUtil.toStr(obj[11]))));
            examineTask(jcTask);
        }
    }

    /**
     *  <p>方法描述：撤销</p>
     * @MethodAuthor hsj 2023-09-27 10:09
     */
    @Transactional(readOnly = false)
    public void cancleAction(Integer rid) {
        if(ObjectUtil.isNull(rid)){
            return;
        }
        String sql = "UPDATE  TB_TJ_JC_TASK SET STATE = 1  WHERE STATE = 2 AND RID = "+ rid;
        this.executeSql(sql,null);
    }

    /**
     * <p>方法描述：通过劳动者花名册rid获取主动监测危害因素 </p>
     * pw 2023/12/16
     **/
    @Transactional(readOnly = true)
    public Map<Integer,Set<String>> findBadRsnByPsnIds(List<Integer> psnRidList) {
        if (CollectionUtils.isEmpty(psnRidList)) {
            return Collections.emptyMap();
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT DISTINCT T.RID,T2.CODE_NAME FROM TB_TJ_JC_TASK_PSN T ")
                .append(" LEFT JOIN TB_TJ_JC_TASK_BADRSN T1 ON T.RID = T1.MAIN_ID ")
                .append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T1.BADRSN_ID = T2.RID WHERE T.RID IN (:psnRidList) ");
        Map<Integer,Set<String>> resultMap = new HashMap<>();
        List<List<Integer>> groupList = StringUtils.splitListProxy(psnRidList, 1000);
        Map<String,Object> paramMap = new HashMap<>();
        String querySql = sqlBuffer.toString();
        List<Object[]> allResultList = new ArrayList<>();
        for (List<Integer> curList : groupList) {
            paramMap.put("psnRidList", curList);
            List<Object[]> queryResultList = this.findDataBySqlNoPage(querySql, paramMap);
            if (CollectionUtils.isEmpty(queryResultList)) {
                continue;
            }
            allResultList.addAll(queryResultList);
        }
        if (CollectionUtils.isEmpty(allResultList)) {
            return resultMap;
        }
        for (Object[] objArr : allResultList) {
            Integer psnRid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            String badRsn = null == objArr[1] ? null : objArr[1].toString();
            if (null == psnRid || StringUtils.isBlank(badRsn)) {
                continue;
            }
            Set<String> resultSet = resultMap.get(psnRid);
            if (null == resultSet) {
                resultSet = new HashSet<>();
            }
            resultSet.add(badRsn);
            resultMap.put(psnRid, resultSet);
        }
        return resultMap;
    }
}
