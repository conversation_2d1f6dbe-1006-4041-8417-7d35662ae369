package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsUserInfo;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2019-9-16
 */
@Entity
@Table(name = "TD_ZW_BGK_FLOW")
@SequenceGenerator(name = "TdZwBgkFlow", sequenceName = "TD_ZW_BGK_FLOW_SEQ", allocationSize = 1)
public class TdZwBgkFlow implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer cartType;
	private Integer busId;
	private Integer operFlag;
	private Date rcvDate;
	private TsUserInfo fkBySmtPsnId;
	private Date operDate;
	private TsUserInfo fkByOperPsnId;
	private String backRsn;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	//是否及时
	private Integer ifInTime;
	private String auditAdv;
	private String auditMan;
	/**审批时 记录更新上一条记录的操作标识*/
	private Integer previousOperFlag;
	/** +体检编号20220629 */
	private String bhkCode;
	/** +体检机构ID20220629 */
	private TbTjSrvorg fkByBhkorgId;

	/**
	 * 审核类型
	 */
	private String auditTypeStr;
	
	public TdZwBgkFlow() {
	}

	public TdZwBgkFlow(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwBgkFlow")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "CART_TYPE")	
	public Integer getCartType() {
		return cartType;
	}

	public void setCartType(Integer cartType) {
		this.cartType = cartType;
	}	
			
	@Column(name = "BUS_ID")	
	public Integer getBusId() {
		return busId;
	}

	public void setBusId(Integer busId) {
		this.busId = busId;
	}	
			
	@Column(name = "OPER_FLAG")	
	public Integer getOperFlag() {
		return operFlag;
	}

	public void setOperFlag(Integer operFlag) {
		this.operFlag = operFlag;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "RCV_DATE")			
	public Date getRcvDate() {
		return rcvDate;
	}

	public void setRcvDate(Date rcvDate) {
		this.rcvDate = rcvDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "SMT_PSN_ID")			
	public TsUserInfo getFkBySmtPsnId() {
		return fkBySmtPsnId;
	}

	public void setFkBySmtPsnId(TsUserInfo fkBySmtPsnId) {
		this.fkBySmtPsnId = fkBySmtPsnId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "OPER_DATE")			
	public Date getOperDate() {
		return operDate;
	}

	public void setOperDate(Date operDate) {
		this.operDate = operDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "OPER_PSN_ID")			
	public TsUserInfo getFkByOperPsnId() {
		return fkByOperPsnId;
	}

	public void setFkByOperPsnId(TsUserInfo fkByOperPsnId) {
		this.fkByOperPsnId = fkByOperPsnId;
	}	
			
	@Column(name = "BACK_RSN")	
	public String getBackRsn() {
		return backRsn;
	}

	public void setBackRsn(String backRsn) {
		this.backRsn = backRsn;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	@Column(name = "IF_IN_TIME")	
	public Integer getIfInTime() {
		return ifInTime;
	}

	public void setIfInTime(Integer ifInTime) {
		this.ifInTime = ifInTime;
	}
	@Column(name = "AUDIT_ADV")	
	public String getAuditAdv() {
		return auditAdv;
	}

	public void setAuditAdv(String auditAdv) {
		this.auditAdv = auditAdv;
	}
	@Column(name = "AUDIT_MAN")	
	public String getAuditMan() {
		return auditMan;
	}

	public void setAuditMan(String auditMan) {
		this.auditMan = auditMan;
	}

	@Transient
	public Integer getPreviousOperFlag() {
		return previousOperFlag;
	}

	public void setPreviousOperFlag(Integer previousOperFlag) {
		this.previousOperFlag = previousOperFlag;
	}

	@Column(name = "BHK_CODE")
	public String getBhkCode() {
		return bhkCode;
	}

	public void setBhkCode(String bhkCode) {
		this.bhkCode = bhkCode;
	}

	@ManyToOne
	@JoinColumn(name = "BHKORG_ID")
	public TbTjSrvorg getFkByBhkorgId() {
		return fkByBhkorgId;
	}

	public void setFkByBhkorgId(TbTjSrvorg fkByBhkorgId) {
		this.fkByBhkorgId = fkByBhkorgId;
	}

	@Transient
	public String getAuditTypeStr() {
		return auditTypeStr;
	}

	public void setAuditTypeStr(String auditTypeStr) {
		this.auditTypeStr = auditTypeStr;
	}
}