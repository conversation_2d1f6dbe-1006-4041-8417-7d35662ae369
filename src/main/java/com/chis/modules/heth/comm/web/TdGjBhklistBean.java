package com.chis.modules.heth.comm.web;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.heth.comm.entity.TdGjBhk;
import com.chis.modules.heth.comm.service.TdGjBhkServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.commons.collections.CollectionUtils;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;

/**
 * <p>方法描述：山东：国家体检数据导入</p>
 *
 * @MethodAuthor hsj 2024-08-15 10:53
 */

@ManagedBean(name = "tdGjBhklistBean")
@ViewScoped
public class TdGjBhklistBean extends FacesEditBean implements IProcessData {


    private static final long serialVersionUID = 2942693167827631153L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final TdGjBhkServiceImpl gjBhkService = SpringContextHolder.getBean(TdGjBhkServiceImpl.class);

    /******************************查询条件***********************************/
    //地区
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
    //体检机构
    private String searchUnitId;
    private String searchUnitName;
    //报告卡编码
    private String searchBhkCode;
    //姓名
    private String searchName;
    //体检危害因素
    private String searchHazardIds;
    private String searchHazardNames;
    //体检结论
    private String searchConclusionIds;
    private String searchConclusionName;
    private List<TsSimpleCode> searchBhkrstList;
    //体检开始日期
    private Date searchStartDate;
    //体检结束时间
    private Date searchEndDate;
    //报告出具开始日期
    private Date searchReportStartDate;
    //报告出具结束日期
    private Date searchReportEndDate;
    //导入开始日期
    private Date searchImportStartDate;
    //导入结束日期
    private Date searchImportEndDate;

    /******************************查询条件***********************************/
    private Integer rid;
    private TdGjBhk gjBhk;

    /**删除接口地址*/
    private String delUrl;

    /**错误数据文件路径*/
    private String importErrFilePath;

    /**导入错误文件下载*/
    private StreamedContent errorImportFile;
    /**导入按钮权限*/
    private boolean ifExport;

    /**失败条数*/
    private Integer failedNum;

    public TdGjBhklistBean() {
        this.ifSQL = Boolean.TRUE;
        delUrl = PropertyUtils.getValue("delUrl");
        if (Global.getBtnSet().contains("heth_comm_sdgjtjsjdr_imp")) {
            this.ifExport = true;
        }else{
            this.ifExport = false;
        }
        this.initZone();
        this.initSimple();
        this.initDate();
        this.searchAction();
    }

    /**
     * <p>方法描述：时间初始化</p>
     *
     * @MethodAuthor hsj 2024-08-15 11:31
     */
    private void initDate() {
        this.searchImportStartDate = DateUtils.getYearFirstDay(new Date());
    }

    /**
     * <p>方法描述：码表初始化</p>
     *
     * @MethodAuthor hsj 2024-08-15 11:19
     */
    private void initSimple() {
        this.searchBhkrstList = this.commService.findSimpleCodesByTypeId("5005");
    }

    /**
     * <p>方法描述：地区初始化：默认为管辖范围内</p>
     *
     * @MethodAuthor hsj 2024-08-15 11:04
     */
    private void initZone() {
        TsZone zone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == zone || null == zone.getRid()) {
            zone = Global.getUser().getTsUnit().getTsZone();
        }
        this.searchZoneCode = zone.getZoneCode();
        this.searchZoneName = zone.getZoneName();
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(zone.getZoneGb(), true, "", "");
    }

    @Override
    public void processData(List<?> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Object[]> result = (List<Object[]>) list;
        List<Integer> ridList = new ArrayList<>();
        for (Object[] obj : result) {
            obj[1] = ZoneUtil.removeProvByFullName(Convert.toStr(obj[1]));
            ridList.add(Convert.toInt(obj[0]));
        }
        Map<Integer, String> map = this.gjBhkService.getTdGjBadrsnsMap(ridList);
        if (null == map || map.size() == 0) {
            return;
        }
        for (Object[] obj : result) {
            obj[7] = map.get(Convert.toInt(obj[0]));
        }
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {
        this.gjBhk = this.gjBhkService.findTdGjBhkByRid(this.rid);
    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder();
        sb.append(" FROM  TD_GJ_BHK T ");
        sb.append(" LEFT JOIN  TB_TJ_SRVORG T1 ON T.BHKORG_ID =T1.RID ");
        sb.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID =T2.RID ");
        sb.append(" LEFT JOIN TB_TJ_CRPT T3 ON T.ENTRUST_CRPT_ID =T3.RID");
        sb.append(" WHERE T2.ZONE_GB LIKE :zoneCode  ");
        this.paramMap.put("zoneCode", ZoneUtil.zoneSelect(this.searchZoneCode) + "%");
        if (StringUtils.isNotEmpty(this.searchUnitId)) {
            sb.append(" AND T1.RID in (:searchUnitId) ");
            this.paramMap.put("searchUnitId", Convert.toList(Integer.class, this.searchUnitId.split(",")));
        }
        if (StringUtils.isNotEmpty(this.searchBhkCode)) {
            sb.append(" AND T.BHK_CODE LIKE :searchBhkCode ");
            this.paramMap.put("searchBhkCode", "%" + StringUtils.convertBFH(this.searchBhkCode.trim()) + "%");
        }
        if (StringUtils.isNotEmpty(this.searchName)) {
            sb.append(" AND T.PERSON_NAME LIKE :searchName ");
            this.paramMap.put("searchName", "%" + StringUtils.convertBFH(this.searchName.trim()) + "%");
        }
        if (StringUtils.isNotEmpty(this.searchHazardIds) || StringUtils.isNotEmpty(this.searchConclusionIds)) {
            sb.append(" AND EXISTS (  SELECT 1 FROM  TD_GJ_BADRSNS TT WHERE TT.MAIN_ID = T.RID   ");
            if (StringUtils.isNotEmpty(this.searchHazardIds)) {
                sb.append(" AND TT.BADRSN_ID in (:searchHazardIds) ");
                this.paramMap.put("searchHazardIds", Convert.toList(Integer.class, this.searchHazardIds.split(",")));
            }
            if (StringUtils.isNotEmpty(this.searchConclusionIds)) {
                sb.append(" AND TT.CONCLUSION_ID in (:searchConclusionIds) ");
                this.paramMap.put("searchConclusionIds", Convert.toList(Integer.class, this.searchConclusionIds.split(",")));
            }
            sb.append(" ) ");
        }
        if (null != this.searchStartDate) {
            sb.append(" AND T.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchStartDate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchEndDate) {
            sb.append(" AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchEndDate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchReportStartDate) {
            sb.append(" AND T.RPT_PRINT_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchReportStartDate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchReportEndDate) {
            sb.append(" AND T.RPT_PRINT_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchReportEndDate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchImportStartDate) {
            sb.append(" AND T.IMPORT_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchImportStartDate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchImportEndDate) {
            sb.append(" AND T.IMPORT_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchImportEndDate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        String h1 = " SELECT T.RID,T2.FULL_NAME ,T1.UNIT_NAME ,T.BHK_CODE,T.PERSON_NAME ,T.BHK_DATE,T.RPT_PRINT_DATE,'',T3.CRPT_NAME ,T.IMPORT_DATE "
                + sb + "  ORDER BY  T2.ZONE_GB ,T1.UNIT_NAME ,T.BHK_CODE ";
        String h2 = " SELECT COUNT(*) " + sb;
        return new String[]{h1, h2};
    }

    /**
     * <p>方法描述：体检机构弹出愉快</p>
     *
     * @MethodAuthor hsj 2024-08-15 11:07
     */
    public void selUnitAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 625, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add(this.searchUnitId);
        paramMap.put("selectIds", paramList);
        List<String> paramList2 = new ArrayList<>();
        paramList2.add(this.searchZoneCode);
        paramMap.put("searchZoneCode", paramList2);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
    }

    /**
     * <p>方法描述：机构信息选择</p>
     *
     * @MethodAuthor hsj 2024-08-15 11:08
     */
    public void onSelectUnitAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null == selectedMap || selectedMap.size() == 0) {
            return;
        }
        List<TbTjSrvorg> list = (List<TbTjSrvorg>) selectedMap.get("selectPros");
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> names = new ArrayList<>();
        List<String> ids = new ArrayList<>();
        for (TbTjSrvorg t : list) {
            names.add(t.getUnitName());
            ids.add(Convert.toStr(t.getRid()));
        }
        this.searchUnitName = StringUtils.list2string(names, "，");
        this.searchUnitId = StringUtils.list2string(ids, ",");
    }
    /**
     *  <p>方法描述：清空机构</p>
     * @MethodAuthor hsj 2024-08-16 15:38
     */
    public void clearUnit() {
        this.searchUnitName = null;
        this.searchUnitId = null;
    }

    /**
     * <p>方法描述：体检危害因素码表5007弹出框</p>
     *
     * @MethodAuthor hsj 2024-08-15 11:12
     */
    public void selSimpleCode5007Action() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add("危害因素");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>();
        paramList.add("5007");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<>();
        paramList.add(this.searchHazardIds);
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<>();
        paramList.add("true");
        paramMap.put("ifShowFirstCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList", options, paramMap);
    }

    /**
     * <p>方法描述：体检危害因素选择</p>
     *
     * @MethodAuthor hsj 2024-08-15 11:13
     */
    public void onSimpleCode5007Action(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null == selectedMap || selectedMap.size() == 0) {
            return;
        }
        List<TsSimpleCode> list = CollectionUtil.castList(TsSimpleCode.class, selectedMap.get("selectPros"));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> ids = new ArrayList<>();
        List<String> names = new ArrayList<>();
        for (TsSimpleCode t : list) {
            ids.add(Convert.toStr(t.getRid()));
            names.add(t.getCodeName());
        }
        this.searchHazardIds = StringUtils.list2string(ids, ",");
        this.searchHazardNames = StringUtils.list2string(names, "，");
    }
    /**
     *  <p>方法描述：体检危害因素清空</p>
     * @MethodAuthor hsj 2024-08-16 15:49
     */
    public void clearSimpleCode5007() {
        this.searchHazardIds = null;
        this.searchHazardNames = null;
    }

    /**
    * <p>Description：上传 </p>
    * <p>Author： yzz 2024-08-17 </p>
    */
    public void importDataAction(FileUploadEvent event) {
        // 删除历史错误文件
        if (ObjectUtil.isNotEmpty(this.importErrFilePath)) {
            File errorFile = new File(JsfUtil.getAbsolutePath() + this.importErrFilePath);
            if (errorFile.exists()) {
                boolean ignore = errorFile.delete();
            }
        }
        this.importErrFilePath = null;
        String updateFormId = "tabView:mainForm:uploadFileDialog";
        if (event == null || event.getFile() == null) {
            RequestContext.getCurrentInstance().update(updateFormId);
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
            JsfUtil.addErrorMessage("文件上传失败！");
            return;
        }
        try {
            //格式验证
            UploadedFile file = event.getFile();
            String fileName = file.getFileName();// 文件名称
            String contentType = file.getContentType().toLowerCase();
            String errorMsg = FileUtils.veryFile(file.getInputstream(),contentType, fileName, "5");
            if (StringUtils.isNotBlank(errorMsg)) {
                JsfUtil.addErrorMessage(errorMsg);
                RequestContext.getCurrentInstance().update(updateFormId);
                RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').show()");
                return;
            }
            String urlStr = delUrl + "/easyExcel/bhkDataUpload";
            String responseStr = HttpRequestUtil.httpRequestByRaw(urlStr, file.getInputstream());
            if (StringUtils.isNotBlank(responseStr)) {
                JSONObject responseJson = JSONObject.parseObject(responseStr);
                Boolean flag = ObjectUtil.convert(Boolean.class, responseJson.get("flag"), false);
                this.failedNum = ObjectUtil.convert(Integer.class, responseJson.get("failedNum"));
                String msg = StringUtils.objectToString(responseJson.get("msg"));
                String errorFileName = StringUtils.objectToString(responseJson.get("errorFile"));
                if (flag) {
                    JsfUtil.addSuccessMessage(msg);
                } else {
                    if (ObjectUtil.isEmpty(msg)) {
                        msg = "导入文件异常！";
                    }
                    JsfUtil.addErrorMessage(msg);
                }
                if (ObjectUtil.isNotEmpty(errorFileName)) {
                    this.importErrFilePath = File.separator + "comm" + File.separator + "temp" + File.separator + errorFileName;
                } else {
                    this.importErrFilePath = "";
                }
            } else {
                JsfUtil.addErrorMessage("导入文件异常！");
            }
            this.searchAction();
            RequestContext.getCurrentInstance().update("tabView:mainForm:dataTable");
            RequestContext.getCurrentInstance().update("tabView:mainForm:buttonsPanel");
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
        } catch (Exception e) {
            e.printStackTrace();
            RequestContext.getCurrentInstance().update(updateFormId);
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
            JsfUtil.addErrorMessage("导入文件异常！");
        }
    }

    /**
    * <p>Description：错误文件下载 </p>
    * <p>Author： yzz 2024-08-17 </p>
    */
    public StreamedContent getErrorImportFile() {
        if (StringUtils.isBlank(this.importErrFilePath)) {
            return null;
        }
        InputStream stream;
        try {
            stream = new FileInputStream(JsfUtil.getAbsolutePath() + this.importErrFilePath);
            String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            String fileName = "国家体检数据导入错误数据.xlsx";
            if (this.importErrFilePath.endsWith(".xls")) {
                contentType = "application/vnd.ms-excel";
                fileName = "国家体检数据导入导入错误数据.xls";
            }
            return new DefaultStreamedContent(stream, contentType, URLEncoder.encode(fileName, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("下载失败!");
        }
        return null;
    }
    public void setErrorImportFile(StreamedContent errorImportFile) {
        this.errorImportFile = errorImportFile;
    }
    public List<TsZone> getZoneList() {
        return this.zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return this.searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return this.searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchUnitId() {
        return this.searchUnitId;
    }

    public void setSearchUnitId(String searchUnitId) {
        this.searchUnitId = searchUnitId;
    }

    public String getSearchUnitName() {
        return this.searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public String getSearchBhkCode() {
        return this.searchBhkCode;
    }

    public void setSearchBhkCode(String searchBhkCode) {
        this.searchBhkCode = searchBhkCode;
    }

    public String getSearchName() {
        return this.searchName;
    }

    public void setSearchName(String searchName) {
        this.searchName = searchName;
    }

    public String getSearchHazardIds() {
        return this.searchHazardIds;
    }

    public void setSearchHazardIds(String searchHazardIds) {
        this.searchHazardIds = searchHazardIds;
    }

    public String getSearchHazardNames() {
        return this.searchHazardNames;
    }

    public void setSearchHazardNames(String searchHazardNames) {
        this.searchHazardNames = searchHazardNames;
    }

    public String getSearchConclusionIds() {
        return this.searchConclusionIds;
    }

    public void setSearchConclusionIds(String searchConclusionIds) {
        this.searchConclusionIds = searchConclusionIds;
    }

    public String getSearchConclusionName() {
        return this.searchConclusionName;
    }

    public void setSearchConclusionName(String searchConclusionName) {
        this.searchConclusionName = searchConclusionName;
    }

    public List<TsSimpleCode> getSearchBhkrstList() {
        return this.searchBhkrstList;
    }

    public void setSearchBhkrstList(List<TsSimpleCode> searchBhkrstList) {
        this.searchBhkrstList = searchBhkrstList;
    }

    public Date getSearchStartDate() {
        return this.searchStartDate;
    }

    public void setSearchStartDate(Date searchStartDate) {
        this.searchStartDate = searchStartDate;
    }

    public Date getSearchEndDate() {
        return this.searchEndDate;
    }

    public void setSearchEndDate(Date searchEndDate) {
        this.searchEndDate = searchEndDate;
    }

    public Date getSearchReportStartDate() {
        return this.searchReportStartDate;
    }

    public void setSearchReportStartDate(Date searchReportStartDate) {
        this.searchReportStartDate = searchReportStartDate;
    }

    public Date getSearchReportEndDate() {
        return this.searchReportEndDate;
    }

    public void setSearchReportEndDate(Date searchReportEndDate) {
        this.searchReportEndDate = searchReportEndDate;
    }

    public Date getSearchImportStartDate() {
        return this.searchImportStartDate;
    }

    public void setSearchImportStartDate(Date searchImportStartDate) {
        this.searchImportStartDate = searchImportStartDate;
    }

    public Date getSearchImportEndDate() {
        return this.searchImportEndDate;
    }

    public void setSearchImportEndDate(Date searchImportEndDate) {
        this.searchImportEndDate = searchImportEndDate;
    }

    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdGjBhk getGjBhk() {
        return this.gjBhk;
    }

    public void setGjBhk(TdGjBhk gjBhk) {
        this.gjBhk = gjBhk;
    }

    public String getImportErrFilePath() {
        return importErrFilePath;
    }
    public void setImportErrFilePath(String importErrFilePath) {
        this.importErrFilePath = importErrFilePath;
    }
    public boolean getIfExport() {
        return ifExport;
    }
    public void setIfExport(boolean ifExport) {
        this.ifExport = ifExport;
    }

    public Integer getFailedNum() {
        return failedNum;
    }
    public void setFailedNum(Integer failedNum) {
        this.failedNum = failedNum;
    }
}
