package com.chis.modules.heth.comm.rptvo;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.rptvo.ZwxFieldDoc;

/**
 * 职业病报告卡[报告卡审核]
 * */
public class ZybRptCardVo {
    //尘肺或者职业 会拼接到文书标题
    @ZwxFieldDoc(value = "报告卡标题")
    private String rptName;
    @ZwxFieldDoc(value = "报告卡编号")
    private String rptNo;
    //劳动者信息
    @ZwxFieldDoc(value = "姓名")
    private String personalName;
    @ZwxFieldDoc(value = "证件类型")
    private String cardType;
    @ZwxFieldDoc(value = "证件号码")
    private String idcard;
    @ZwxFieldDoc(value = "性别")
    private String sex;
    @ZwxFieldDoc(value = "出生日期")
    private String birthday;
    @ZwxFieldDoc(value = "联系电话")
    private String personalTel;
    @ZwxFieldDoc(value = "紧急联系人")
    private String personLinkMan;
    @ZwxFieldDoc(value = "紧急联系人联系方式")
    private String personLinkTel;
    //用人单位信息
    @ZwxFieldDoc(value = "用人单位名称")
    private String crptName;
    @ZwxFieldDoc(value = "统一社会信用代码")
    private String unitCode;
    @ZwxFieldDoc(value = "企业类型")//经济类型
    private String economy;
    @ZwxFieldDoc(value = "行业类别")
    private String indusType;
    @ZwxFieldDoc(value = "企业规模")
    private String crptSize;
    @ZwxFieldDoc(value = "用人单位所在区")
    private String zoneName;
    @ZwxFieldDoc(value = "用人单位详细地址")
    private String linkAddr;
    @ZwxFieldDoc(value = "用人单位地址邮编")
    private String postCode;
    @ZwxFieldDoc(value = "用人单位联系人")
    private String crptLinkMan;
    @ZwxFieldDoc(value = "用人单位联系人电话")
    private String crptLinkTel;
    @ZwxFieldDoc(value = "用工单位名称")
    private String empCrptName;
    @ZwxFieldDoc(value = "统一社会信用代码")
    private String empUnitCode;
    @ZwxFieldDoc(value = "企业类型")//经济类型
    private String empEconomy;
    @ZwxFieldDoc(value = "行业类别")
    private String empIndusType;
    @ZwxFieldDoc(value = "企业规模")
    private String empCrptSize;
    @ZwxFieldDoc(value = "用人单位所在区")
    private String empZoneName;
    //职业病报告信息
    @ZwxFieldDoc(value = "申请诊断日期")
    private String applyDiagDate;
    @ZwxFieldDoc(value = "诊断日期")
    private String diagDate;
    @ZwxFieldDoc(value = "诊断一期")
    private String diagDate1;
    @ZwxFieldDoc(value = "诊断二期")
    private String diagDate2;
    @ZwxFieldDoc(value = "诊断三期")
    private String diagDate3;
    @ZwxFieldDoc(value = "职业病名称")
    private String typeName;
    @ZwxFieldDoc(value = "职业病种类")
    private String disTypeName;
    @ZwxFieldDoc(value = "职业性化学中毒分类")
    private String poisonType;
    @ZwxFieldDoc(value = "病例类型")
    private String rptTypeName;
    @ZwxFieldDoc(value = "统计工种")
    private String workTypeName;
    @ZwxFieldDoc(value = "接触的职业性有害因素")
    private String badRsns;
    @ZwxFieldDoc(value = "死亡日期")
    private String deathDate;
    @ZwxFieldDoc(value = "死亡原因")
    private String dieRsn;
    @ZwxFieldDoc(value = "接害开始日期")
    private String begTchDate;
    @ZwxFieldDoc(value = "接害工龄年数")
    private String tchDustYear;
    @ZwxFieldDoc(value = "接害工龄月数")
    private String tchDustMonth;
    @ZwxFieldDoc(value = "接害工龄天数")
    private String tchDustDays;
    @ZwxFieldDoc(value = "接害工龄小时数")
    private String tchHours;
    @ZwxFieldDoc(value = "接害工龄分钟数")
    private String tchMinutes;
    @ZwxFieldDoc(value = "是否肺结核")
    private String iftb;
    @ZwxFieldDoc(value = "是否肺及支气管感染")
    private String ifPulInfection;
    @ZwxFieldDoc(value = "是否自发性气胸")
    private String ifPneum;
    @ZwxFieldDoc(value = "是否肺心病")
    private String ifPulHeart;
    @ZwxFieldDoc(value = "是否肺癌")
    private String ifLungCa;
    @ZwxFieldDoc(value = "诊断单位")
    private String diagUnitName;
    @ZwxFieldDoc(value = "诊断单位负责人")
    private String diagRespPsn;
    //填表人信息
    @ZwxFieldDoc(value = "填表人")
    private String fillFormPsn;
    @ZwxFieldDoc(value = "填表人联系电话")
    private String fillLink;
    @ZwxFieldDoc(value = "填表日期")
    private String fillDate;
    @ZwxFieldDoc(value = "填表单位")
    private String fillUnit;
    //报告人信息
    @ZwxFieldDoc(value = "报告人")
    private String rptPsn;
    @ZwxFieldDoc(value = "报告人联系电话")
    private String rptLink;
    @ZwxFieldDoc(value = "报告日期")
    private String rptDate;
    @ZwxFieldDoc(value = "报告单位")
    private String diagUnit;
    //备注
    @ZwxFieldDoc(value = "备注")
    private String rmk;
    //辅助用
    @ZwxFieldDoc(value = "报告卡类型")
    private String type;//1 尘肺病 2职业中毒 3其他

    public String getRptName() {
        if(StringUtils.isNotBlank(type) && type.equals("1")){
            return "尘肺";
        }else{
            return "职业";
        }
    }

    public void setRptName(String rptName) {
        this.rptName = rptName;
    }

    public String getRptNo() {
        return rptNo;
    }

    public void setRptNo(String rptNo) {
        this.rptNo = rptNo;
    }

    public String getPersonalName() {
        return personalName;
    }

    public void setPersonalName(String personalName) {
        this.personalName = personalName;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getPersonalTel() {
        return personalTel;
    }

    public void setPersonalTel(String personalTel) {
        this.personalTel = personalTel;
    }

    public String getPersonLinkMan() {
        return personLinkMan;
    }

    public void setPersonLinkMan(String personLinkMan) {
        this.personLinkMan = personLinkMan;
    }

    public String getPersonLinkTel() {
        return personLinkTel;
    }

    public void setPersonLinkTel(String personLinkTel) {
        this.personLinkTel = personLinkTel;
    }

    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getEconomy() {
        return economy;
    }

    public void setEconomy(String economy) {
        this.economy = economy;
    }

    public String getIndusType() {
        return indusType;
    }

    public void setIndusType(String indusType) {
        this.indusType = indusType;
    }

    public String getCrptSize() {
        return crptSize;
    }

    public void setCrptSize(String crptSize) {
        this.crptSize = crptSize;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getLinkAddr() {
        return linkAddr;
    }

    public void setLinkAddr(String linkAddr) {
        this.linkAddr = linkAddr;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getCrptLinkMan() {
        return crptLinkMan;
    }

    public void setCrptLinkMan(String crptLinkMan) {
        this.crptLinkMan = crptLinkMan;
    }

    public String getCrptLinkTel() {
        return crptLinkTel;
    }

    public void setCrptLinkTel(String crptLinkTel) {
        this.crptLinkTel = crptLinkTel;
    }

    public String getApplyDiagDate() {
        return applyDiagDate;
    }

    public void setApplyDiagDate(String applyDiagDate) {
        this.applyDiagDate = applyDiagDate;
    }

    public String getDiagDate() {
        return diagDate;
    }

    public void setDiagDate(String diagDate) {
        this.diagDate = diagDate;
    }

    public String getDiagDate1() {
        return diagDate1;
    }

    public void setDiagDate1(String diagDate1) {
        this.diagDate1 = diagDate1;
    }

    public String getDiagDate2() {
        return diagDate2;
    }

    public void setDiagDate2(String diagDate2) {
        this.diagDate2 = diagDate2;
    }

    public String getDiagDate3() {
        return diagDate3;
    }

    public void setDiagDate3(String diagDate3) {
        this.diagDate3 = diagDate3;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getDisTypeName() {
        return disTypeName;
    }

    public void setDisTypeName(String disTypeName) {
        this.disTypeName = disTypeName;
    }

    public String getPoisonType() {
        return poisonType;
    }

    public void setPoisonType(String poisonType) {
        this.poisonType = poisonType;
    }

    public String getRptTypeName() {
        return rptTypeName;
    }

    public void setRptTypeName(String rptTypeName) {
        this.rptTypeName = rptTypeName;
    }

    public String getWorkTypeName() {
        return workTypeName;
    }

    public void setWorkTypeName(String workTypeName) {
        this.workTypeName = workTypeName;
    }

    public String getBadRsns() {
        return badRsns;
    }

    public void setBadRsns(String badRsns) {
        this.badRsns = badRsns;
    }

    public String getDeathDate() {
        return deathDate;
    }

    public void setDeathDate(String deathDate) {
        this.deathDate = deathDate;
    }

    public String getDieRsn() {
        return dieRsn;
    }

    public void setDieRsn(String dieRsn) {
        this.dieRsn = dieRsn;
    }

    public String getBegTchDate() {
        return begTchDate;
    }

    public void setBegTchDate(String begTchDate) {
        this.begTchDate = begTchDate;
    }

    public String getTchDustYear() {
        return tchDustYear;
    }

    public void setTchDustYear(String tchDustYear) {
        this.tchDustYear = tchDustYear;
    }

    public String getTchDustMonth() {
        return tchDustMonth;
    }

    public void setTchDustMonth(String tchDustMonth) {
        this.tchDustMonth = tchDustMonth;
    }

    public String getTchDustDays() {
        return tchDustDays;
    }

    public void setTchDustDays(String tchDustDays) {
        this.tchDustDays = tchDustDays;
    }

    public String getTchHours() {
        return tchHours;
    }

    public void setTchHours(String tchHours) {
        this.tchHours = tchHours;
    }

    public String getTchMinutes() {
        return tchMinutes;
    }

    public void setTchMinutes(String tchMinutes) {
        this.tchMinutes = tchMinutes;
    }

    public String getIftb() {
        return iftb;
    }

    public void setIftb(String iftb) {
        this.iftb = iftb;
    }

    public String getIfPulInfection() {
        return ifPulInfection;
    }

    public void setIfPulInfection(String ifPulInfection) {
        this.ifPulInfection = ifPulInfection;
    }

    public String getIfPneum() {
        return ifPneum;
    }

    public void setIfPneum(String ifPneum) {
        this.ifPneum = ifPneum;
    }

    public String getIfPulHeart() {
        return ifPulHeart;
    }

    public void setIfPulHeart(String ifPulHeart) {
        this.ifPulHeart = ifPulHeart;
    }

    public String getIfLungCa() {
        return ifLungCa;
    }

    public void setIfLungCa(String ifLungCa) {
        this.ifLungCa = ifLungCa;
    }

    public String getDiagUnitName() {
        return diagUnitName;
    }

    public void setDiagUnitName(String diagUnitName) {
        this.diagUnitName = diagUnitName;
    }

    public String getDiagRespPsn() {
        return diagRespPsn;
    }

    public void setDiagRespPsn(String diagRespPsn) {
        this.diagRespPsn = diagRespPsn;
    }

    public String getFillFormPsn() {
        return fillFormPsn;
    }

    public void setFillFormPsn(String fillFormPsn) {
        this.fillFormPsn = fillFormPsn;
    }

    public String getFillLink() {
        return fillLink;
    }

    public void setFillLink(String fillLink) {
        this.fillLink = fillLink;
    }

    public String getFillDate() {
        return fillDate;
    }

    public void setFillDate(String fillDate) {
        this.fillDate = fillDate;
    }

    public String getFillUnit() {
        return fillUnit;
    }

    public void setFillUnit(String fillUnit) {
        this.fillUnit = fillUnit;
    }

    public String getRptPsn() {
        return rptPsn;
    }

    public void setRptPsn(String rptPsn) {
        this.rptPsn = rptPsn;
    }

    public String getRptLink() {
        return rptLink;
    }

    public void setRptLink(String rptLink) {
        this.rptLink = rptLink;
    }

    public String getRptDate() {
        return rptDate;
    }

    public void setRptDate(String rptDate) {
        this.rptDate = rptDate;
    }

    public String getDiagUnit() {
        return diagUnit;
    }

    public void setDiagUnit(String diagUnit) {
        this.diagUnit = diagUnit;
    }

    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getEmpCrptName() {
        return empCrptName;
    }

    public void setEmpCrptName(String empCrptName) {
        this.empCrptName = empCrptName;
    }

    public String getEmpUnitCode() {
        return empUnitCode;
    }

    public void setEmpUnitCode(String empUnitCode) {
        this.empUnitCode = empUnitCode;
    }

    public String getEmpEconomy() {
        return empEconomy;
    }

    public void setEmpEconomy(String empEconomy) {
        this.empEconomy = empEconomy;
    }

    public String getEmpIndusType() {
        return empIndusType;
    }

    public void setEmpIndusType(String empIndusType) {
        this.empIndusType = empIndusType;
    }

    public String getEmpCrptSize() {
        return empCrptSize;
    }

    public void setEmpCrptSize(String empCrptSize) {
        this.empCrptSize = empCrptSize;
    }

    public String getEmpZoneName() {
        return empZoneName;
    }

    public void setEmpZoneName(String empZoneName) {
        this.empZoneName = empZoneName;
    }
}
