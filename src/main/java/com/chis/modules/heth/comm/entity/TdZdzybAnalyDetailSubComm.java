package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2023-7-18
 */
@Entity
@Table(name = "TD_ZDZYB_ANALY_DETAIL_SUB")
@SequenceGenerator(name = "TdZdzybAnalyDetailSub", sequenceName = "TD_ZDZYB_ANALY_DETAIL_SUB_SEQ", allocationSize = 1)
public class TdZdzybAnalyDetailSubComm implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZdzybAnalyDetailComm fkByMainId;
	private TsSimpleCode fkByAnalyItemId;
	private Date modifyDate;
	private Integer modifyManid;
	private Date createDate;
	private Integer createManid;
	
	public TdZdzybAnalyDetailSubComm() {
	}

	public TdZdzybAnalyDetailSubComm(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZdzybAnalyDetailSub")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZdzybAnalyDetailComm getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZdzybAnalyDetailComm fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ANALY_ITEM_ID")			
	public TsSimpleCode getFkByAnalyItemId() {
		return fkByAnalyItemId;
	}

	public void setFkByAnalyItemId(TsSimpleCode fkByAnalyItemId) {
		this.fkByAnalyItemId = fkByAnalyItemId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
}