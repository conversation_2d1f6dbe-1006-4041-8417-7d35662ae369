package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-4
 */
@Entity
@Table(name = "TD_ZXJC_RESULT_PRO")
@SequenceGenerator(name = "TdZxjcResultPro", sequenceName = "TD_ZXJC_RESULT_PRO_SEQ", allocationSize = 1)
public class TdZxjcResultPro implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZxjcUnitbasicinfo fkByMainId;
	private TsSimpleCode fkByFactorId;
	private String jobId;
	private String jobName;
	private String jobNameOther;
	private Integer ifQualified;
	private Integer ifCtwaHg;
	private Integer ifPlaceHg;
	private Integer workerNum;
	private String workTime;
	private String classTime;
	private String contactDays;
	private String contactTime;
	private Integer fixType;
	private String placeMainId;
	private BigDecimal tchTime;
	private String placeName;
	private TsSimpleCode fkByFactorProId;
	private String factorProName;
	private String sample1;
	private String sample2;
	private String sample3;
	private String sample4;
	private String sample5;
	private String sample6;
	private String silicaContent;
	private String fillCste;
	private String avgValue;
	private String ctwaVal;
	private String ctwaDisVal;
	private String lexVal;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZxjcResultPro() {
	}

	public TdZxjcResultPro(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZxjcResultPro")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZxjcUnitbasicinfo getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZxjcUnitbasicinfo fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "FACTOR_ID")			
	public TsSimpleCode getFkByFactorId() {
		return fkByFactorId;
	}

	public void setFkByFactorId(TsSimpleCode fkByFactorId) {
		this.fkByFactorId = fkByFactorId;
	}	
			
	@Column(name = "JOB_ID")	
	public String getJobId() {
		return jobId;
	}

	public void setJobId(String jobId) {
		this.jobId = jobId;
	}	
			
	@Column(name = "JOB_NAME")	
	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}	
			
	@Column(name = "JOB_NAME_OTHER")	
	public String getJobNameOther() {
		return jobNameOther;
	}

	public void setJobNameOther(String jobNameOther) {
		this.jobNameOther = jobNameOther;
	}	
			
	@Column(name = "IF_QUALIFIED")	
	public Integer getIfQualified() {
		return ifQualified;
	}

	public void setIfQualified(Integer ifQualified) {
		this.ifQualified = ifQualified;
	}	
			
	@Column(name = "IF_CTWA_HG")	
	public Integer getIfCtwaHg() {
		return ifCtwaHg;
	}

	public void setIfCtwaHg(Integer ifCtwaHg) {
		this.ifCtwaHg = ifCtwaHg;
	}	
			
	@Column(name = "IF_PLACE_HG")	
	public Integer getIfPlaceHg() {
		return ifPlaceHg;
	}

	public void setIfPlaceHg(Integer ifPlaceHg) {
		this.ifPlaceHg = ifPlaceHg;
	}	
			
	@Column(name = "WORKER_NUM")	
	public Integer getWorkerNum() {
		return workerNum;
	}

	public void setWorkerNum(Integer workerNum) {
		this.workerNum = workerNum;
	}	
			
	@Column(name = "WORK_TIME")	
	public String getWorkTime() {
		return workTime;
	}

	public void setWorkTime(String workTime) {
		this.workTime = workTime;
	}	
			
	@Column(name = "CONTACT_DAYS")	
	public String getContactDays() {
		return contactDays;
	}

	public void setContactDays(String contactDays) {
		this.contactDays = contactDays;
	}	
			
	@Column(name = "CONTACT_TIME")	
	public String getContactTime() {
		return contactTime;
	}

	public void setContactTime(String contactTime) {
		this.contactTime = contactTime;
	}	
			
	@Column(name = "FIX_TYPE")	
	public Integer getFixType() {
		return fixType;
	}

	public void setFixType(Integer fixType) {
		this.fixType = fixType;
	}	
			
	@Column(name = "PLACE_MAIN_ID")	
	public String getPlaceMainId() {
		return placeMainId;
	}

	public void setPlaceMainId(String placeMainId) {
		this.placeMainId = placeMainId;
	}	
			
	@Column(name = "TCH_TIME")	
	public BigDecimal getTchTime() {
		return tchTime;
	}

	public void setTchTime(BigDecimal tchTime) {
		this.tchTime = tchTime;
	}	
			
	@Column(name = "PLACE_NAME")	
	public String getPlaceName() {
		return placeName;
	}

	public void setPlaceName(String placeName) {
		this.placeName = placeName;
	}	
			
	@ManyToOne
	@JoinColumn(name = "FACTOR_PRO_ID")			
	public TsSimpleCode getFkByFactorProId() {
		return fkByFactorProId;
	}

	public void setFkByFactorProId(TsSimpleCode fkByFactorProId) {
		this.fkByFactorProId = fkByFactorProId;
	}	
			
	@Column(name = "FACTOR_PRO_NAME")	
	public String getFactorProName() {
		return factorProName;
	}

	public void setFactorProName(String factorProName) {
		this.factorProName = factorProName;
	}	
			
	@Column(name = "SAMPLE1")	
	public String getSample1() {
		return sample1;
	}

	public void setSample1(String sample1) {
		this.sample1 = sample1;
	}	
			
	@Column(name = "SAMPLE2")	
	public String getSample2() {
		return sample2;
	}

	public void setSample2(String sample2) {
		this.sample2 = sample2;
	}	
			
	@Column(name = "SAMPLE3")	
	public String getSample3() {
		return sample3;
	}

	public void setSample3(String sample3) {
		this.sample3 = sample3;
	}	
			
	@Column(name = "SAMPLE4")	
	public String getSample4() {
		return sample4;
	}

	public void setSample4(String sample4) {
		this.sample4 = sample4;
	}	
			
	@Column(name = "SAMPLE5")	
	public String getSample5() {
		return sample5;
	}

	public void setSample5(String sample5) {
		this.sample5 = sample5;
	}	
			
	@Column(name = "SAMPLE6")	
	public String getSample6() {
		return sample6;
	}

	public void setSample6(String sample6) {
		this.sample6 = sample6;
	}	
			
	@Column(name = "SILICA_CONTENT")	
	public String getSilicaContent() {
		return silicaContent;
	}

	public void setSilicaContent(String silicaContent) {
		this.silicaContent = silicaContent;
	}	
			
	@Column(name = "FILL_CSTE")	
	public String getFillCste() {
		return fillCste;
	}

	public void setFillCste(String fillCste) {
		this.fillCste = fillCste;
	}	
			
	@Column(name = "AVG_VALUE")	
	public String getAvgValue() {
		return avgValue;
	}

	public void setAvgValue(String avgValue) {
		this.avgValue = avgValue;
	}	
			
	@Column(name = "CTWA_VAL")	
	public String getCtwaVal() {
		return ctwaVal;
	}

	public void setCtwaVal(String ctwaVal) {
		this.ctwaVal = ctwaVal;
	}	
			
	@Column(name = "CTWA_DIS_VAL")	
	public String getCtwaDisVal() {
		return ctwaDisVal;
	}

	public void setCtwaDisVal(String ctwaDisVal) {
		this.ctwaDisVal = ctwaDisVal;
	}	
			
	@Column(name = "LEX_VAL")	
	public String getLexVal() {
		return lexVal;
	}

	public void setLexVal(String lexVal) {
		this.lexVal = lexVal;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Column(name = "CLASS_TIME")
	public String getClassTime() {
		return classTime;
	}

	public void setClassTime(String classTime) {
		this.classTime = classTime;
	}
}