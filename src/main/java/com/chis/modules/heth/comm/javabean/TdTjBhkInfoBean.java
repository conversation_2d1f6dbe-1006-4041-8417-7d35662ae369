package com.chis.modules.heth.comm.javabean;

import cn.hutool.core.convert.Convert;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.logic.BhkItemFirstInfoDTO;
import com.chis.modules.heth.comm.logic.BhkItemSecondInfoDTO;
import com.chis.modules.heth.comm.logic.BhkItemTableInfoDTO;
import com.chis.modules.heth.comm.logic.TjBhkConClusionDTO;
import com.chis.modules.heth.comm.service.HethStaQueryCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import org.primefaces.component.column.Column;
import org.primefaces.component.fieldset.Fieldset;
import org.primefaces.component.outputpanel.OutputPanel;
import org.primefaces.component.panelgrid.PanelGrid;
import org.primefaces.component.row.Row;
import org.springframework.util.CollectionUtils;

import javax.faces.component.html.HtmlOutputText;
import javax.faces.context.FacesContext;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>类描述：人员体检信息页面初始化Bean</p>
 * @ClassAuthor qrr,2020年3月3日,TdTjBhkInfoBean
 * */
public class TdTjBhkInfoBean {
	private HethStaQueryCommServiceImpl hethStaQueryServiceImpl = SpringContextHolder
			.getBean(HethStaQueryCommServiceImpl.class);

	private Integer rid;
	/**
	 * 体检显示页面的实例
	 */
	private TdTjBhk tdTjBhkShow;
	/**
	 * 问诊
	 */
	private TdTjExmsdata tdTjExmsdata = new TdTjExmsdata();
	/**
	 * 职业史集合
	 */
	private List<TdTjEmhistory> tdTjEmhistoryList;
	/**
	 * 放射史集合
	 */
	private List<TdTjEmhistory> tdTjEmhistoryList2;
	/**
	 * 用人单位的联系人和联系电话
	 */
	private TbTjCrptIndepend tbTjCrptIndepend = new TbTjCrptIndepend();
	/**
	 * 用工单位的联系人和联系电话
	 */
	private TbTjCrptIndepend tbTjCrptIndependEntrust = new TbTjCrptIndepend();
	/**
	 * 体检项目的布局表格
	 */
	private OutputPanel archivePanel = (OutputPanel) JsfUtil.getApplication().createComponent(
			OutputPanel.COMPONENT_TYPE);
	// 系统参数

	/**是否管理机构，true：身份证号隐藏显示*/
	private boolean ifManagedOrg;

	//只显示基础信息
	private boolean ifShowBaseOnly = false;
	/**危害因素结论*/
	private List<TjBhkConClusionDTO> clusionDTOs;
	/**重点职业病判定-复检记录覆盖初检*/
	private boolean ifRmkSubCover;
	/**是否为在岗前且存在疑似职业病*/
	private boolean ifShowUnit;
	private List<BhkItemFirstInfoDTO> itemFirstInfos;


	public TdTjBhkInfoBean() {
	}

	public TdTjBhkInfoBean(boolean ifRmkSubCover) {
        this.ifRmkSubCover = ifRmkSubCover;
    }
	/**
	 * <p>方法描述：查询体检相关信息</p>
	 * @MethodAuthor qrr,2020年3月3日,initBhkInfo
	 * */
	public void initBhkInfo() {
		this.ifShowUnit = Boolean.FALSE;//默认不显示
		// 查看选定的体检的详情,转到体检详情页面
		tdTjBhkShow = hethStaQueryServiceImpl.findTdTjBhkById(rid);
		//用人单位的联系人和联系电话
		this.tbTjCrptIndepend = new TbTjCrptIndepend();
		this.tbTjCrptIndependEntrust = new TbTjCrptIndepend();
		if (this.tdTjBhkShow != null
				&& this.tdTjBhkShow.getTbTjSrvorg() != null
				&& this.tdTjBhkShow.getTbTjSrvorg().getTsUnit() != null) {
			Integer unitRid = this.tdTjBhkShow.getTbTjSrvorg().getTsUnit().getRid();
			if (tdTjBhkShow.getTbTjCrpt() != null) {
				Integer crptRid = this.tdTjBhkShow.getTbTjCrpt().getRid();
				this.tbTjCrptIndepend = this.hethStaQueryServiceImpl.findTbTjCrptIndependByBusType(1, crptRid, unitRid);
			}
			if (tdTjBhkShow.getFkByEntrustCrptId() != null) {
				Integer crptRid = this.tdTjBhkShow.getFkByEntrustCrptId().getRid();
				this.tbTjCrptIndependEntrust = this.hethStaQueryServiceImpl.findTbTjCrptIndependByBusType(1, crptRid, unitRid);
			}
		}
		tdTjExmsdata =  new TdTjExmsdata();
		String idc = tdTjBhkShow.getIdc();
		//加密身份证号
		if (this.ifManagedOrg) {
			if (null!=tdTjBhkShow.getPsnType()) {
				if (1==tdTjBhkShow.getPsnType()) {
					//加密身份证号
					String encryptIdc = StringUtils.encryptIdc(idc);
					tdTjBhkShow.setIdc(encryptIdc);
				}else if (2==tdTjBhkShow.getPsnType()) {
					if(StringUtils.isNotBlank(idc)){
						if (idc.trim().length()>4) {
							tdTjBhkShow.setIdc(idc.trim().substring(0,idc.trim().length()-4)+"****");
						}else {
							tdTjBhkShow.setIdc("****");
						}
					}
				}
			}
			//联系电话加密
			if(StringUtils.isNotBlank(tdTjBhkShow.getLnktel())){
				tdTjBhkShow.setLnktel(StringUtils.encryptPhone(tdTjBhkShow.getLnktel()));
			}
			desensitizationPhone();
		}
		// 问诊项目
		List<TdTjExmsdata> tdTjExmsdataList = tdTjBhkShow.getTdTjExmsdatas();
		if (null != tdTjExmsdataList && tdTjExmsdataList.size() > 0) {
			tdTjExmsdata = tdTjExmsdataList.get(0);
		}
		// 放射史和非放射史数据分开
		List<TdTjEmhistory> list = tdTjBhkShow.getTdTjEmhistories();
		tdTjEmhistoryList = new ArrayList<TdTjEmhistory>();
		tdTjEmhistoryList2 = new ArrayList<TdTjEmhistory>();
		if (null != list && list.size() > 0) {
			for (TdTjEmhistory t : list) {
				if (t.getHisType() == 1) {
					tdTjEmhistoryList2.add(t);
				} else {
					tdTjEmhistoryList.add(t);
				}
			}
		}
		this.ifShowUnit = tdTjBhkShow.getTsSimpleCode() != null && "1".equals(tdTjBhkShow.getTsSimpleCode().getExtendS1())
				&& tdTjBhkShow.getIfTargetdis() != null && tdTjBhkShow.getIfTargetdis().compareTo(1) == 0;
		//危害因素结论
		getBadRsnConclusion();
		//接触危害因素
		getTchBadrsnName();
		//体检危害因素
		getBhkBadrsnName();
		//体检项目封装
		this.itemFirstInfos = new LinkedList<>();
		if(!this.ifShowBaseOnly){
			Map<TsSimpleCode, List<Object[]>> dataMap = hethStaQueryServiceImpl.findProjectInfoNew(this.rid,this.ifRmkSubCover);
			this.initItemFirstInfos(dataMap);
		}
		FacesContext.getCurrentInstance().renderResponse();

	}
	/**
	 *  <p>方法描述：获取接触危害因素</p>
	 * @MethodAuthor hsj 2023-03-28 16:54
	 */
	private void getTchBadrsnName() {
		List<TdTjTchBadrsns> tchBadrsnsList = tdTjBhkShow.getTdTjTchBadrsns();
		List<String> names = new ArrayList<>();
		if(!CollectionUtils.isEmpty(tchBadrsnsList)){
			//按照码表num code 排序;num可能为空的情况
			Collections.sort(tchBadrsnsList, new Comparator<TdTjTchBadrsns>() {
				@Override
				public int compare(TdTjTchBadrsns o1, TdTjTchBadrsns o2) {
					if (ObjectUtil.isNotNull(o1.getFkByBadrsnId().getNum()) && ObjectUtil.isNotNull(o2.getFkByBadrsnId().getNum())) {
						int i = o1.getFkByBadrsnId().getNum().compareTo(o2.getFkByBadrsnId().getNum());
					if(i == 0){
						return o1.getFkByBadrsnId().getCodeNo().compareTo(o2.getFkByBadrsnId().getCodeNo());
					}
					return i;
					} else if (ObjectUtil.isNotNull(o1.getFkByBadrsnId().getNum())) {
						return -1;
					} else if (ObjectUtil.isNotNull(o2.getFkByBadrsnId().getNum())) {
						return 1;
					} else {
						return o1.getFkByBadrsnId().getCodeNo().compareTo(o2.getFkByBadrsnId().getCodeNo());
					}
				}
			});
			for(TdTjTchBadrsns bads:tchBadrsnsList){
				names.add(bads.getFkByBadrsnId().getCodeName());
			}
			tdTjBhkShow.setTchBadrsn(StringUtils.list2string(names,"，"));
		}
	}
	/**
	 *  <p>方法描述：获取体检接害因素</p>
	 * @MethodAuthor hsj 2024-05-28 15:53
	 */
	private void getBhkBadrsnName() {
		List<TdTjBadrsns> badrsnsList = this.tdTjBhkShow.getTdTjBadrsnses();
		if(CollectionUtils.isEmpty(badrsnsList)) {
			return;
		}
		List<String> names = new ArrayList<>();
		SortUtil.sortCodeByField(badrsnsList,TdTjBadrsns.class,"getTsSimpleCode");
		for(TdTjBadrsns bads:badrsnsList){
			names.add(bads.getTsSimpleCode().getCodeName());
		}
		this.tdTjBhkShow.setBhkBadrsn(StringUtils.list2string(names,"，"));
	}

	/**
	 * 用人单位/用工单位联系电话脱敏
	 */
	public void desensitizationPhone() {
		this.tbTjCrptIndepend.setLinkphone2(StringUtils.encryptPhone(this.tbTjCrptIndepend.getLinkphone2()));
		this.tbTjCrptIndependEntrust.setLinkphone2(StringUtils.encryptPhone(this.tbTjCrptIndependEntrust.getLinkphone2()));
	}

	/**
 	 * <p>方法描述：危害因素结论明细
 	 * 1、无病种，按照体检结论合并危害因素
 	 * 2、有病种，按照同结论、同病种合并危害因素</p>
 	 * @MethodAuthor qrr,2021年8月13日,getBadRsnConclusion
	 * */
	private void getBadRsnConclusion() {
		this.clusionDTOs = new ArrayList<>();
		List<TdTjContraindlist> tdTjContraindlists = tdTjBhkShow.getTdTjContraindlists();
		Map<Integer, List<TdTjContraindlist>> contraindMap = new HashMap<Integer, List<TdTjContraindlist>>();
		if (!CollectionUtils.isEmpty(tdTjContraindlists)) {
			for (TdTjContraindlist t : tdTjContraindlists) {
				if (null==t.getTsSimpleCodeByBadrsnId()||null==t.getTsSimpleCodeByBadrsnId().getRid()) {
					continue;
				}
				if (null==contraindMap.get(t.getTsSimpleCodeByBadrsnId().getRid())) {
					List<TdTjContraindlist> contraindlist = new  ArrayList<>();
					contraindlist.add(t);
					contraindMap.put(t.getTsSimpleCodeByBadrsnId().getRid(), contraindlist);
				}else {
					List<TdTjContraindlist> contraindlist = contraindMap.get(t.getTsSimpleCodeByBadrsnId().getRid());
					contraindlist.add(t);
				}
			}
		}
		List<TdTjSupoccdiselist> tdTjSupoccdiselists = tdTjBhkShow.getTdTjSupoccdiselists();
		Map<Integer, List<TdTjSupoccdiselist>> supoccdiseMap = new HashMap<Integer, List<TdTjSupoccdiselist>>();
		if (!CollectionUtils.isEmpty(tdTjSupoccdiselists)) {
			for (TdTjSupoccdiselist t : tdTjSupoccdiselists) {
				if (null==t.getTsSimpleCodeByBadrsnId()||null==t.getTsSimpleCodeByBadrsnId().getRid()) {
					continue;
				}
				if (null==supoccdiseMap.get(t.getTsSimpleCodeByBadrsnId().getRid())) {
					List<TdTjSupoccdiselist> supoccdiselist = new  ArrayList<>();
					supoccdiselist.add(t);
					supoccdiseMap.put(t.getTsSimpleCodeByBadrsnId().getRid(), supoccdiselist);
				}else {
					List<TdTjSupoccdiselist> supoccdiselist = supoccdiseMap.get(t.getTsSimpleCodeByBadrsnId().getRid());
					supoccdiselist.add(t);
				}
			}
		}
		List<TdTjBadrsns> tdTjBadrsnses = tdTjBhkShow.getTdTjBadrsnses();
		if (!CollectionUtils.isEmpty(tdTjBadrsnses)) {
			for (TdTjBadrsns t : tdTjBadrsnses) {
				TsSimpleCode badRsn = t.getTsSimpleCode();
				TsSimpleCode conclusionId = t.getFkByExamConclusionId();
				if (null==badRsn||null==conclusionId) {
					continue;
				}
				if (null!=conclusionId.getExtendS2() && 4==conclusionId.getExtendS2().intValue()) {//职业禁忌证
					List<TdTjContraindlist> tdTjContraindlist = contraindMap.get(badRsn.getRid());
					TjBhkConClusionDTO dto = new TjBhkConClusionDTO();
					dto.setFkByConclusionId(conclusionId);
					dto.setBadRsns(badRsn.getCodeName());
					if (!CollectionUtils.isEmpty(tdTjContraindlist)) {
						List<String> name = new ArrayList<>();
						for (TdTjContraindlist contraind : tdTjContraindlist) {
							name.add(contraind.getTsSimpleCodeByContraindId().getCodeName());
						}
						dto.setDiseNames(CollectionUtils.isEmpty(name) ? "" : StringUtils.list2string(name,"，"));
					}
					this.clusionDTOs.add(dto);
				}else if (null!=conclusionId.getExtendS2() && 5==conclusionId.getExtendS2().intValue()) {//疑似职业病
					List<TdTjSupoccdiselist> supoccdiselist = supoccdiseMap.get(badRsn.getRid());
					TjBhkConClusionDTO dto = new TjBhkConClusionDTO();
					dto.setFkByConclusionId(conclusionId);
					dto.setBadRsns(badRsn.getCodeName());
					if (!CollectionUtils.isEmpty(supoccdiselist)) {
						List<String> name = new ArrayList<>();
						List<String> unitName = new ArrayList<>();
						for (TdTjSupoccdiselist supoccdise : supoccdiselist) {
							name.add(supoccdise.getTsSimpleCodeByOccDiseid().getCodeName());
							if(StringUtils.isNotBlank(supoccdise.getCrptName()) && !unitName.contains(supoccdise.getCrptName())){
								unitName.add(supoccdise.getCrptName());
							}
						}
						dto.setDiseNames(CollectionUtils.isEmpty(name) ? "" : StringUtils.list2string(name,"，"));
						dto.setUnitNames(CollectionUtils.isEmpty(unitName) ? null : StringUtils.list2string(unitName,"，"));
					}
					this.clusionDTOs.add(dto);
				}else {
					TjBhkConClusionDTO dto = new TjBhkConClusionDTO();
					dto.setFkByConclusionId(conclusionId);
					dto.setBadRsns(badRsn.getCodeName());
					//如果结论是其他疾病或异常，这显示具体内容
					if(new Integer("3").equals(conclusionId.getExtendS2())){
						dto.setQtjbName(t.getQtjbName());
					}
					this.clusionDTOs.add(dto);
				}
			}
		}
		sortBadClusion();
	}
	/**
 	 * <p>方法描述：危害因素结论排序</p>
 	 * @MethodAuthor qrr,2021年8月16日,sortBadClusion
	 * */
	private void sortBadClusion() {
		if (!CollectionUtils.isEmpty(this.clusionDTOs)) {
			Collections.sort(this.clusionDTOs, new Comparator<TjBhkConClusionDTO>() {
				@Override
				public int compare(TjBhkConClusionDTO o1, TjBhkConClusionDTO o2) {
					TsSimpleCode conclusionId1 = o1.getFkByConclusionId();
					TsSimpleCode conclusionId2 = o2.getFkByConclusionId();
					if (null!=conclusionId1 && null!=conclusionId2) {
						Integer num1 = conclusionId1.getNum();
						Integer num2 = conclusionId2.getNum();
						if (null!=num1 && null!=num2) {
							return num1.compareTo(num2);
						}
					}
					return -1;
				}
			});
		}
	}
	/**
	 * 构建outputPanel，依次页面结构层次是：outputpanel, panel, panelGrid, row, column,控件
	 *
	 * @param dataMap
	 *            组件数据集合
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private void createOutPanel(Map<TsSimpleCode, List<Object[]>> dataMap) {
		archivePanel.getChildren().clear();
		if (null != dataMap && dataMap.size() > 0) {
			Set<TsSimpleCode> tsSimpleCodeSet = dataMap.keySet();
			// 遍历数据集合，当集合中出现一级分类时，就创建一个Fieldset
			for (TsSimpleCode t : tsSimpleCodeSet) {
				if (t.getCodeLevelNo().split("\\.").length == 1) {
					createPanel(t, dataMap);
				}
			}
		}
	}

	/**
	 *  <p>方法描述：体检项目检测结果封装</p>
	 * @MethodAuthor hsj 2024-08-05 9:02
	 */
	private void initItemFirstInfos(Map<TsSimpleCode, List<Object[]>> dataMap) {
		if(null == dataMap || dataMap.size() == 0){
			return;
		}
		Set<TsSimpleCode> tsSimpleCodeSet = dataMap.keySet();
		for (TsSimpleCode t : tsSimpleCodeSet) {
			Integer len = t.getCodeLevelNo().split("\\.").length;
			if ( !new Integer(1).equals(len)) {
				continue;
			}
			initItemSecondInfos(t, dataMap);
		}
	}

	/**
	 * 创建Fieldset
	 *
	 * @param legend
	 *            标题
	 * @param style
	 *            样式
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private Fieldset createFieldset(String legend, String style) {
		Fieldset fieldset = (Fieldset) JsfUtil.getApplication().createComponent(Fieldset.COMPONENT_TYPE);
		JsfUtil.getApplication().createComponent(PanelGrid.COMPONENT_TYPE);
		fieldset.setLegend(legend);
		fieldset.setStyle(style);
		fieldset.setToggleable(true);
		fieldset.setToggleSpeed(500);
		return fieldset;
	}

	/**
	 * 创建PanelGrid
	 *
	 * @param style
	 *            样式
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private PanelGrid createPanelGrid(String style) {
		PanelGrid panelGrid = (PanelGrid) JsfUtil.getApplication().createComponent(PanelGrid.COMPONENT_TYPE);
		if (null != style) {
			panelGrid.setStyle(style);
		}
		return panelGrid;
	}

	/**
	 * 创建Row
	 *
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private Row createRow() {
		Row row = (Row) JsfUtil.getApplication().createComponent(Row.COMPONENT_TYPE);
		return row;
	}

	/**
	 * 创建列
	 *
	 * @param colspan
	 *            合并列数
	 * @param styleClass
	 *            样式类
	 * @param style
	 *            样式
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private Column createColumn(Integer colspan, String styleClass, String style) {
		Column column = (Column) JsfUtil.getApplication().createComponent(Column.COMPONENT_TYPE);
		if (null != colspan) {
			column.setColspan(colspan);
		}
		if (null != styleClass) {
			column.setStyleClass(styleClass);
		}
		if (null != style) {
			column.setStyle(style);
		}
		return column;
	}

	/**
	 * 创建HtmlOutputText
	 *
	 * @param value
	 *            值
	 * @return
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private HtmlOutputText createOutputtext(Object value) {
		HtmlOutputText htmlOutputText = (HtmlOutputText) JsfUtil.getApplication().createComponent(
				HtmlOutputText.COMPONENT_TYPE);
		if (null != value) {
			htmlOutputText.setValue(value);
		}
		return htmlOutputText;
	}

	/**
	 * 创建Fieldset，并加入到archivePanel中 创建步骤： 1、创建一个新的Fieldset，设置样式和内容
	 * 2、遍历数据集合，每找到一个该一级分类下的二级分类，则为该二级分类建一个新的PanelGrid
	 * 3、给新创建的PanelGrid设置表格属性，表头内容，并调用相应方法创建表体 4、将新建的PanelGrid加入到Fieldset中
	 * 5、继续循环，将集合中该一级分类下所有的二级分类中的数据都做成PanelGrid，添加到Fieldset
	 *
	 * @param ts
	 *            一级分类的码表实例
	 * @param dataMap
	 *            数据集合
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private void createPanel(TsSimpleCode ts, Map<TsSimpleCode, List<Object[]>> dataMap) {
		// 第一步，创建Fieldset
		final Fieldset fieldset = createFieldset(ts.getCodeName(), "margin-top: 5px;margin-bottom: 5px;");

		// 第二步，遍历集合，创建Fieldset下的PanelGrid
		Set<TsSimpleCode> tsSimpleCodeSet = dataMap.keySet();
		for (TsSimpleCode t : tsSimpleCodeSet) {
			String[] str = t.getCodeLevelNo().split("\\.");
			// 第三步,判断此二级分类是否在该一级分类下，若是则创建PanelGrid
			if (str.length == 2 && str[0].equals(ts.getCodeLevelNo())) {
				List<Object[]> list = dataMap.get(t);
				final PanelGrid panelGrid = createPanelGrid("width:100%;margin-top:10px;");
				final Row headRow = createRow();
				final Column headColumn = createColumn(8, "ui-widget-header", "height:22px;");
				final HtmlOutputText headText = createOutputtext(t.getCodeName());
				headColumn.getChildren().add(headText);
				headRow.getChildren().add(headColumn);
				panelGrid.getChildren().add(headRow);
				// 调用相应方法创建表体，根据系统参数值，调用对应的方法创建表体
				boolean bool = true;
				bool = this.createPanelGrid3(panelGrid, list);
				// 第四步，如果表体内有组件，则将该表添加到Fieldse
				if (!bool) {
					fieldset.getChildren().add(panelGrid);
				}
			}
		}
		// 第五步，将Fieldse添加到outpanel中
		if (fieldset.getChildren().size() != 0) {
			archivePanel.getChildren().add(fieldset);
		}
	}
	/**
	 *  <p>方法描述：体检项目检测结果封装</p>
	 * @MethodAuthor hsj 2024-08-05 9:04
	 */
	private void initItemSecondInfos(TsSimpleCode ts, Map<TsSimpleCode, List<Object[]>> dataMap) {
		BhkItemFirstInfoDTO dto = new BhkItemFirstInfoDTO();
		dto.setItemName(ts.getCodeName());
		dto.setItemSecondInfoDTOS(new LinkedList<BhkItemSecondInfoDTO>());
		for (Map.Entry<TsSimpleCode, List<Object[]>> entry : dataMap.entrySet()){
			TsSimpleCode t = entry.getKey();
			String[] str = t.getCodeLevelNo().split("\\.");
			if ( !new Integer(2).equals(str.length) || !str[0].equals(ts.getCodeLevelNo())) {
				continue;
			}
			BhkItemSecondInfoDTO secondInfoDTO = new BhkItemSecondInfoDTO();
			secondInfoDTO.setItemName(t.getCodeName());
			secondInfoDTO.setItemTableInfoDTOS(new LinkedList<BhkItemTableInfoDTO>());
			this.initItemTableInfos(entry.getValue(),secondInfoDTO);
			if(!CollectionUtils.isEmpty(secondInfoDTO.getItemTableInfoDTOS())){
				dto.getItemSecondInfoDTOS().add(secondInfoDTO);
			}
		}
		if(!CollectionUtils.isEmpty(dto.getItemSecondInfoDTOS())){
			this.itemFirstInfos.add(dto);
		}
	}
	/**
	 * 该方法用于创建化验检查检查panelgrid，化验需要添加新的数据列，因此，需要在createPanelGrid1基础增加新列
	 * 1、设置表格的第一行内容 2、遍历该分类下的数据集合，将集合数据添加到表格中 3、返回创建结果
	 *
	 * @param panelGrid
	 *            需要创建内容的panelGrid
	 * @param list
	 *            该分类下的数据list数据集合
	 * @return 表格有内容返回true，否则返回false
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private boolean createPanelGrid3(PanelGrid panelGrid, List<Object[]> list) {
		boolean isBlank = true;
		// 第一步.创建标题行，分栏需要循环创建两次，八列
		final Row titleRow = createRow();
		/*for (int i = 0; i < 2; i++) {*/
		boolean flag = false;

		if(null!=list&&list.size()>0){

			for (Object[] obj :list) {
				if(obj[10]==null){
					continue;
				}
				BigDecimal rowTmp = (BigDecimal) obj[10];
				if(rowTmp!=null&&rowTmp.intValue()==30){
					flag = true;
				}

			}


		}
			// 添加项目列
			final Column titleColumn1 = createColumn(null, "ui-state-default",
					"width:120px;text-align: center;height:20px;");
			final HtmlOutputText titleText1 = createOutputtext("项目");
			titleColumn1.getChildren().add(titleText1);
			// 添加结果列
			final Column titleColumn2 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
			final HtmlOutputText titleText2 = createOutputtext("结果");
			titleColumn2.getChildren().add(titleText2);
			// 添加参考值列
			final Column titleColumn3 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
			final HtmlOutputText titleText3 = createOutputtext("参考值");
			titleColumn3.getChildren().add(titleText3);
			// 添加计量单位列
			final Column titleColumn4 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
			final HtmlOutputText titleText4 = createOutputtext("计量单位");
			titleColumn4.getChildren().add(titleText4);
			// 添加合格
			final Column titleColumn5 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
			final HtmlOutputText titleText5 = createOutputtext("合格");
			titleColumn5.getChildren().add(titleText5);



			// 添加未检
			final Column titleColumn7 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
			final HtmlOutputText titleText7 = createOutputtext("未检");
			titleColumn7.getChildren().add(titleText7);

			// 组件关联
			titleRow.getChildren().add(titleColumn1);
			titleRow.getChildren().add(titleColumn2);
			titleRow.getChildren().add(titleColumn3);
			titleRow.getChildren().add(titleColumn4);
			titleRow.getChildren().add(titleColumn5);
			//添加结果判定
			if(flag){
				final Column titleColumn6 = createColumn(null, "ui-state-default", "width:100px;text-align: center;");
				final HtmlOutputText titleText6 = createOutputtext("结果判定");
				titleColumn6.getChildren().add(titleText6);
				titleRow.getChildren().add(titleColumn6);
			}
			titleRow.getChildren().add(titleColumn7);
		//}
		panelGrid.getChildren().add(titleRow);
		// 第二步，创建内容行，遍历集合，添加数据，分栏添加
		int count = 0;
		int length = list.size();
		Row contentRow = null;
		for (int i = 0; i < length; i++) {
			Object[] obj = list.get(i);
			// 1.如果该行已有两条数据则创建一个新行
			if (count == 0) {
				contentRow = createRow();
			}
			// 2.添加项目名称内容
			final Column contentColumn1 = createColumn(null, null, "text-align: left;height:20px;");
			final HtmlOutputText contentText1 = createOutputtext(obj[0]);
			contentColumn1.getChildren().add(contentText1);
			// 3.添加体检结果内容
			final Column contentColumn2 = createColumn(null, null, null);
			final HtmlOutputText contentText2 = createOutputtext(null);
			this.setPanelgridValue(contentColumn2, contentText2, obj);
			contentColumn2.getChildren().add(contentText2);
			// 4.添加参考值列的内容
			final Column contentColumn3 = createColumn(null, null, "text-align: left");
			final HtmlOutputText contentText3 = createOutputtext( obj[3]);
			contentColumn3.getChildren().add(contentText3);
			// 5.添加计量单位的列的内容
			final Column contentColumn4 = createColumn(null, null, "text-align: center;");
			final HtmlOutputText contentText4 = createOutputtext(obj[4]);

			Column contentColumn5 = null;
			HtmlOutputText contentText5 = null;
			// 6.添加合格
			BigDecimal tmp = (BigDecimal) obj[7];
			if(tmp.intValue()==1){
				contentColumn5 = createColumn(null, null, "text-align: center");
				contentText5 = createOutputtext( "是");

			}else {
				contentColumn5 = createColumn(null, null, "text-align: center;color:red");
				contentText5 = createOutputtext( "否");
			}
			contentColumn5.getChildren().add(contentText5);



			// 8.添加未检
			Column contentColumn7 = null;
			HtmlOutputText contentText7 = null;

			if(obj[9]!=null){
				BigDecimal tmp2 = (BigDecimal) obj[9];
				if(tmp2.intValue()==1){
					contentColumn7 = createColumn(null, null, "text-align: center;color:red");
					contentText7 = createOutputtext( "是");
				}else {
					contentColumn7 = createColumn(null, null, "text-align: center");
					contentText7 = createOutputtext( "否");
				}
			}else {
				contentColumn7 = createColumn(null, null, "text-align: center;color:red");
				contentText7 = createOutputtext( "");
			}
			contentColumn7.getChildren().add(contentText7);




			// 6.组件关联
			contentColumn4.getChildren().add(contentText4);
			contentRow.getChildren().add(contentColumn1);
			contentRow.getChildren().add(contentColumn2);
			contentRow.getChildren().add(contentColumn3);
			contentRow.getChildren().add(contentColumn4);
			contentRow.getChildren().add(contentColumn5);
			// 7.添加结果判定
			  /*0：未见异常
				1：尘肺样改变
				2：其他异常
				3：未检查*/
			if(flag){
				BigDecimal tmp1 = (BigDecimal) obj[8];
				Column contentColumn6 = null;
				HtmlOutputText contentText6 = null;
				//（业务分类下存在胸片项目才要显示，项目标记为30的代表胸片），非正常标红
				if(tmp1!=null){
					if(tmp1.intValue()==0){
						contentColumn6 = createColumn(null, null, "text-align: left");
						contentText6 = createOutputtext( "未见异常");
					}else{
						contentColumn6 = createColumn(null, null, "text-align: left;color:red");
						if(tmp1.intValue()==1){
							contentText6 = createOutputtext( "尘肺样改变");
						}else if(tmp1.intValue()==2){
							contentText6 = createOutputtext( "其他异常");
						}else if(tmp1.intValue()==3){
							contentText6 = createOutputtext( "未检查");
						}
					}
				}else {
					contentColumn6 = createColumn(null, null, "text-align: left");
					contentText6 = createOutputtext( "");
				}

				contentColumn6.getChildren().add(contentText6);
				contentRow.getChildren().add(contentColumn6);
			}

			contentRow.getChildren().add(contentColumn7);
			count++;
			// 如果该行已有两条数据记录，则将该行添加到表格中，并创建一个新行
			if (count == 1) {
				panelGrid.getChildren().add(contentRow);
				isBlank = false;
				count = 0;
			}
		}
		// 添加最后一行的数据记录
		if (count == 1) {
			panelGrid.getChildren().add(contentRow);
			isBlank = false;
		}
		return isBlank;
	}
	/**
	 * 初始化体检项目的表格信息
	 * @param list 体检结果数据列表
	 * @param secondInfoDTO 用于封装体检结果信息的对象
	 * @MethodAuthor hsj 2024-08-05 9:13 
	 */
	private void initItemTableInfos( List<Object[]> list, BhkItemSecondInfoDTO secondInfoDTO) {
		if(CollectionUtils.isEmpty(list)){
			return;
		}
		boolean flag = false;
		for (Object[] obj :list) {
			if(!new Integer(30).equals(Convert.toInt(obj[10]))){
				continue;
			}
			flag = true;
			break;
		}
		secondInfoDTO.setIfResultJudgment(flag);
		for (Object[] obj :list) {
			BhkItemTableInfoDTO itemTableInfoDTO = new BhkItemTableInfoDTO();
			// 2.添加体检项目内容
			itemTableInfoDTO.setItemName(Convert.toStr(obj[0]));
			// 3.添加体检结果内容
			this.initItemResult(itemTableInfoDTO, obj);
			// 4.添加参考值列的内容
			itemTableInfoDTO.setReferenceValue(Convert.toStr(obj[3]));
			// 5.添加计量单位的列的内容
			itemTableInfoDTO.setUnit(Convert.toStr(obj[4]));
			// 6.添加合格
			if(new Integer(1).equals(Convert.toInt(obj[7]))){
				itemTableInfoDTO.setPass("是");
			}else {
				itemTableInfoDTO.setPass("否");
				itemTableInfoDTO.setPassColour("color:red;");
			}
			// 8.添加未检
			if(null == obj[9]){
				itemTableInfoDTO.setUnchecked("");
				itemTableInfoDTO.setUncheckedColour("color:red;");
			}else if(new Integer(1).equals(Convert.toInt(obj[9]))){
				itemTableInfoDTO.setUnchecked("是");
				itemTableInfoDTO.setUncheckedColour("color:red;");
			}else {
				itemTableInfoDTO.setUnchecked("否");
			}
			if(!flag){
				secondInfoDTO.getItemTableInfoDTOS().add(itemTableInfoDTO);
				continue;
			}
			// 7.添加结果判定（业务分类下存在胸片项目才要显示，项目标记为30的代表胸片），非正常标红
			if(null == obj[8]){
				itemTableInfoDTO.setResultJudge("");
			}else if(new Integer(0).equals(Convert.toInt(obj[8]))){
				itemTableInfoDTO.setResultJudge("未见异常");
			}else if(new Integer(1).equals(Convert.toInt(obj[8]))){
				itemTableInfoDTO.setResultJudge("尘肺样改变");
				itemTableInfoDTO.setResultJudgeColor("color:red;");
			}else if(new Integer(2).equals(Convert.toInt(obj[8]))){
				itemTableInfoDTO.setResultJudge("其他异常");
				itemTableInfoDTO.setResultJudgeColor("color:red;");
			}else if(new Integer(3).equals(Convert.toInt(obj[8]))){
				itemTableInfoDTO.setResultJudge("未检查");
				itemTableInfoDTO.setResultJudgeColor("color:red;");
			}
			secondInfoDTO.getItemTableInfoDTOS().add(itemTableInfoDTO);
		}
	}

	public String getLinkedPhone2EnCode(String linkphone2){
		if(ifManagedOrg){
			return StringUtils.encryptPhone(linkphone2);
		}
		return linkphone2;
	}



	/**
	 * 设置化验结果列的样式和值
	 *
	 * @param contentColumn
	 * @param contentText
	 * @param obj
	 * @createDate 2014-9-20
	 * <AUTHOR>
	 */
	private void setPanelgridValue(Column contentColumn, HtmlOutputText contentText, Object[] obj) {
		// 定量且结果偏低或偏高则结果加标志，并设置颜色
		Integer result = null;
		if (null != obj[2]) {
			result = Integer.valueOf(obj[2].toString());
		}
		if (null != obj[1]) {
			if (null != result) {
				if (result == 2) {
					// 如果偏高，则添加向上箭头，并设置颜色为红色
					contentColumn.setStyle("text-align: left;color: red");
					String string = obj[1].toString() + "↑";
					contentText.setValue(string);
				} else if (result == 1) {
					// 如果偏低，则添加向下箭头，并设置颜色为蓝色
					contentColumn.setStyle("text-align: left;color: blue");
					String string = obj[1].toString() + "↓";
					contentText.setValue(string);
				} else {
					// 否则，原样显示
					contentColumn.setStyle("text-align: left;");
					contentText.setValue(obj[1]);
				}
			} else {
				contentColumn.setStyle("text-align: left;");
				contentText.setValue(obj[1]);
			}
		} else {
			// 内容为空，则显示为"—"
			contentColumn.setStyle("text-align: left;");
			contentText.setValue("—");
		}
	}
	/**
	 *  <p>方法描述：体检结果值和样式封装</p>
	 *  <p>该方法根据体检项目的检测结果，封装结果值和相应的样式</p>
	 * @param itemTableInfoDTO 体检项目的表格信息DTO，用于封装结果值和样式
	 * @param obj 包含体检结果相关数据的数组，其中obj[1]通常为结果值，obj[2]为结果状态码
	 * @MethodAuthor hsj 2024-08-05 9:28
	 */
	private void initItemResult(BhkItemTableInfoDTO itemTableInfoDTO, Object[] obj) {
	    String string = "—";
	    if(null == obj[1]){
	        itemTableInfoDTO.setResult(string);
	        return;
	    }
	    String resultColour = "";
	    Integer result = Convert.toInt(obj[2]);
	    if(null == result){
	        // 如果结果状态码为空，直接设置结果值
	        string = Convert.toStr(obj[1]);
	    }else if(new Integer(2).equals(result)){
	        // 如果结果状态码为2，表示结果值偏高，设置结果值和样式
	        string = Convert.toStr(obj[1]) + "↑";
	        resultColour = "color: red";
	    }else if(new Integer(1).equals(result)){
	        // 如果结果状态码为1，表示结果值偏低，设置结果值和样式
	        string = Convert.toStr(obj[1]) + "↓";
	        resultColour = "color: blue";
	    }else{
	        // 其他情况，直接设置结果值
	        string = Convert.toStr(obj[1]);
	    }
	    itemTableInfoDTO.setResult(string);
	    itemTableInfoDTO.setResultColour(resultColour);
	}




	public TdTjBhk getTdTjBhkShow() {
		return tdTjBhkShow;
	}

	public TdTjExmsdata getTdTjExmsdata() {
		return tdTjExmsdata;
	}

	public void setTdTjBhkShow(TdTjBhk tdTjBhkShow) {
		this.tdTjBhkShow = tdTjBhkShow;
	}

	public void setTdTjExmsdata(TdTjExmsdata tdTjExmsdata) {
		this.tdTjExmsdata = tdTjExmsdata;
	}

	public List<TdTjEmhistory> getTdTjEmhistoryList() {
		return tdTjEmhistoryList;
	}

	public List<TdTjEmhistory> getTdTjEmhistoryList2() {
		return tdTjEmhistoryList2;
	}

	public void setTdTjEmhistoryList(List<TdTjEmhistory> tdTjEmhistoryList) {
		this.tdTjEmhistoryList = tdTjEmhistoryList;
	}

	public void setTdTjEmhistoryList2(List<TdTjEmhistory> tdTjEmhistoryList2) {
		this.tdTjEmhistoryList2 = tdTjEmhistoryList2;
	}

	public Integer getRid() {
		return rid;
	}
	public void setRid(Integer rid) {
		this.rid = rid;
	}



	public OutputPanel getArchivePanel() {
		return archivePanel;
	}
	public void setArchivePanel(OutputPanel archivePanel) {
		this.archivePanel = archivePanel;
	}

	public boolean isIfManagedOrg() {
		return ifManagedOrg;
	}
	public void setIfManagedOrg(boolean ifManagedOrg) {
		this.ifManagedOrg = ifManagedOrg;
	}

	public boolean isIfShowBaseOnly() {
		return ifShowBaseOnly;
	}

	public void setIfShowBaseOnly(boolean ifShowBaseOnly) {
		this.ifShowBaseOnly = ifShowBaseOnly;
	}

	public TbTjCrptIndepend getTbTjCrptIndepend() {
		return tbTjCrptIndepend;
	}

	public void setTbTjCrptIndepend(TbTjCrptIndepend tbTjCrptIndepend) {
		this.tbTjCrptIndepend = tbTjCrptIndepend;
	}

	public List<TjBhkConClusionDTO> getClusionDTOs() {
		return clusionDTOs;
	}

	public void setClusionDTOs(List<TjBhkConClusionDTO> clusionDTOs) {
		this.clusionDTOs = clusionDTOs;
	}

	public TbTjCrptIndepend getTbTjCrptIndependEntrust() {
		return tbTjCrptIndependEntrust;
	}

	public void setTbTjCrptIndependEntrust(TbTjCrptIndepend tbTjCrptIndependEntrust) {
		this.tbTjCrptIndependEntrust = tbTjCrptIndependEntrust;
	}

	public boolean isIfShowUnit() {
		return ifShowUnit;
	}

	public void setIfShowUnit(boolean ifShowUnit) {
		this.ifShowUnit = ifShowUnit;
	}

	public List<BhkItemFirstInfoDTO> getItemFirstInfos() {
		return itemFirstInfos;
	}

	public void setItemFirstInfos(List<BhkItemFirstInfoDTO> itemFirstInfos) {
		this.itemFirstInfos = itemFirstInfos;
	}
}
