package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsUnit;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2024-8-8
 */
@Entity
@Table(name = "TD_ZW_MONTH_BHK_PROV")
@SequenceGenerator(name = "TdZwMonthBhkProv", sequenceName = "TD_ZW_MONTH_BHK_PROV_SEQ", allocationSize = 1)
public class TdZwMonthBhkProv implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TsUnit fkByOrgId;
    private Date startBhkDate;
    private Date endBhkDate;
    private Integer orgNum;
    private Integer orgSubmitNum;
    private Date fillDate;
    private Integer state;
    private Integer delMark;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    private List<TdZwMonthOrg> tdZwMonthOrgs;
    private Integer selYear;
    private Integer selMonth;
    private List<Integer> selOrgIds;

    public TdZwMonthBhkProv() {
    }

    public TdZwMonthBhkProv(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwMonthBhkProv")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "ORG_ID")
    public TsUnit getFkByOrgId() {
        return this.fkByOrgId;
    }

    public void setFkByOrgId(TsUnit fkByOrgId) {
        this.fkByOrgId = fkByOrgId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "START_BHK_DATE")
    public Date getStartBhkDate() {
        return this.startBhkDate;
    }

    public void setStartBhkDate(Date startBhkDate) {
        this.startBhkDate = startBhkDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "END_BHK_DATE")
    public Date getEndBhkDate() {
        return this.endBhkDate;
    }

    public void setEndBhkDate(Date endBhkDate) {
        this.endBhkDate = endBhkDate;
    }

    @Column(name = "ORG_NUM")
    public Integer getOrgNum() {
        return this.orgNum;
    }

    public void setOrgNum(Integer orgNum) {
        this.orgNum = orgNum;
    }

    @Column(name = "ORG_SUBMIT_NUM")
    public Integer getOrgSubmitNum() {
        return this.orgSubmitNum;
    }

    public void setOrgSubmitNum(Integer orgSubmitNum) {
        this.orgSubmitNum = orgSubmitNum;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "FILL_DATE")
    public Date getFillDate() {
        return this.fillDate;
    }

    public void setFillDate(Date fillDate) {
        this.fillDate = fillDate;
    }

    @Column(name = "STATE")
    public Integer getState() {
        return this.state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Column(name = "DEL_MARK")
    public Integer getDelMark() {
        return this.delMark;
    }

    public void setDelMark(Integer delMark) {
        this.delMark = delMark;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwMonthOrg> getTdZwMonthOrgs() {
        return this.tdZwMonthOrgs;
    }

    public void setTdZwMonthOrgs(List<TdZwMonthOrg> tdZwMonthOrgs) {
        this.tdZwMonthOrgs = tdZwMonthOrgs;
    }

    @Transient
    public Integer getSelYear() {
        return this.selYear;
    }


    public void setSelYear(Integer selYear) {
        this.selYear = selYear;
    }

    @Transient
    public Integer getSelMonth() {
        return this.selMonth;
    }

    public void setSelMonth(Integer selMonth) {
        this.selMonth = selMonth;
    }

    @Transient
    public List<Integer> getSelOrgIds() {
        return this.selOrgIds;
    }

    public void setSelOrgIds(List<Integer> selOrgIds) {
        this.selOrgIds = selOrgIds;
    }
}