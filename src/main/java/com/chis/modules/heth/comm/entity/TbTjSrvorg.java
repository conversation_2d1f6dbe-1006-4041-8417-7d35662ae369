package com.chis.modules.heth.comm.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;

/**
 * 修改人 wjh 修改时间 2014年09月11日 <br/>
 * 修改内容 单位名称，单位编码。启用/停用 提示的说明
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置
 *
 *
 * 资质服务机构
 *
 * <AUTHOR>
 * @createDate 2014年9月1日 上午8:53:08
 * @LastModify LuXuekun
 * @ModifyDate 2014年9月1日 上午8:53:08
 */
@Entity
@Table(name = "TB_TJ_SRVORG", uniqueConstraints = @UniqueConstraint(columnNames = "UNIT_CODE"))
@SequenceGenerator(name = "TbTjSrvorgSeq", sequenceName = "TB_TJ_SRVORG_SEQ", allocationSize = 1)
public class TbTjSrvorg implements java.io.Serializable {
    private static final long serialVersionUID = -6044760525693496963L;
    private Integer rid;
    private TsSimpleCode tsSimpleCode = new TsSimpleCode();
    private TsZone tsZone = new TsZone();
    private String unitCode;
    private String unitName;
    private Integer stopTag;
    private String regCode;
    private Date createDate;
    private Integer createManid;
	//private List<TdTjBhk> tdTjBhks = new ArrayList<TdTjBhk>(0);
	private List<TdZwtjUploadlog> tdZwtjUploadlogs = new ArrayList<TdZwtjUploadlog>(
			0);

    private TsUnit tsUnit;
    private String uuid;
    private boolean ifSelected;

    public TbTjSrvorg() {
    }

    public TbTjSrvorg(Integer rid) {
        this.rid = rid;
    }

    public TbTjSrvorg(Integer rid, TsSimpleCode tsSimpleCode, String unitCode,
                      String unitName, Integer stopTag, Date createDate,
                      Integer createManid) {
        this.rid = rid;
        this.tsSimpleCode = tsSimpleCode;
        this.unitCode = unitCode;
        this.unitName = unitName;
        this.stopTag = stopTag;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    @Id
    @GeneratedValue(generator = "TbTjSrvorgSeq", strategy = GenerationType.SEQUENCE)
    @Column(name = "RID", unique = true )
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "APT_SORTID" )
    public TsSimpleCode getTsSimpleCode() {
        return this.tsSimpleCode;
    }

    public void setTsSimpleCode(TsSimpleCode tsSimpleCode) {
        this.tsSimpleCode = tsSimpleCode;
    }

    @ManyToOne
    @JoinColumn(name = "ZONE_ID")
    public TsZone getTsZone() {
        return this.tsZone;
    }

    public void setTsZone(TsZone tsZone) {
        this.tsZone = tsZone;
    }

    @Column(name = "UNIT_CODE", unique = true , length = 50)
    public String getUnitCode() {
        return this.unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }


    @Column(name = "UNIT_NAME" , length = 100)
    public String getUnitName() {
        return this.unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }


    @Column(name = "STOP_TAG" )
    public Integer getStopTag() {
        return this.stopTag;
    }

    public void setStopTag(Integer stopTag) {
        this.stopTag = stopTag;
    }

    @Column(name = "REG_CODE", length = 50)
    public String getRegCode() {
        return this.regCode;
    }

    public void setRegCode(String regCode) {
        this.regCode = regCode;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @ManyToOne
    @JoinColumn(name = "REG_ORGID")
    public TsUnit getTsUnit() {
        return tsUnit;
    }

    public void setTsUnit(TsUnit tsUnit) {
        this.tsUnit = tsUnit;
    }

	/*@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbTjSrvorg")
	public List<TdTjBhk> getTdTjBhks() {
		return this.tdTjBhks;
	}

	public void setTdTjBhks(List<TdTjBhk> tdTjBhks) {
		this.tdTjBhks = tdTjBhks;
	}*/

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbTjSrvorg")
	public List<TdZwtjUploadlog> getTdZwtjUploadlogs() {
		return this.tdZwtjUploadlogs;
	}

	public void setTdZwtjUploadlogs(List<TdZwtjUploadlog> tdZwtjUploadlogs) {
		this.tdZwtjUploadlogs = tdZwtjUploadlogs;
	}
	@Column(name = "UUID", length = 50)
	public String getUuid() {
	     return uuid;
	}

	public void setUuid(String uuid) {
	     this.uuid = uuid;
	}
	@Transient
	public boolean isIfSelected() {
		return ifSelected;
	}

	public void setIfSelected(boolean ifSelected) {
		this.ifSelected = ifSelected;
	}
}
