package com.chis.modules.heth.comm.web.activeMonitoring;

import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjJcTask;
import com.chis.modules.heth.comm.logic.TaskExamineConditionPO;
import com.chis.modules.heth.comm.service.ActiveMonitoringTaskServiceImpl;
import com.chis.modules.heth.comm.service.activeMonitoring.ActiveMonitoringTaskExamineServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.*;

/**
 *  <p>类描述：主动监测任务审核</p>
 * @ClassAuthor hsj 2023-07-20 14:00
 */
@ManagedBean(name = "activeMonitoringTaskExamineBean")
@ViewScoped
public class ActiveMonitoringTaskExamineBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final ActiveMonitoringTaskExamineServiceImpl taskExamineService = SpringContextHolder.getBean(ActiveMonitoringTaskExamineServiceImpl.class);
    private final ActiveMonitoringTaskServiceImpl activeMonitoringTaskService =
            SpringContextHolder.getBean(ActiveMonitoringTaskServiceImpl.class);

    /**查询条件*/
    private TaskExamineConditionPO taskExamineCondition;
    /**查询条件企业规模*/
    private List<TsSimpleCode> crptSizeList;
    /**条件状态*/
    private List<SelectItem> stateList;
    /**查询条件地区*/
    private List<TsZone> zoneList;
    /**查询条件-年份*/
    private List<Integer> yearList;
    /**
     * 当前操作码表类型
     */
    private String simpleCodeOpType;

    /**
     * 主动监测任务RID
     */
    private Integer rid;
    /**
     * 主动监测任务
     */
    private TbTjJcTask jcTask;
    /** 选择的结果集 */
    protected List<Object[]> selectEntitys;

    /** 用于导出的码表Map key 码表rid value code_name或者code_path*/
    private Map<Integer,String> simpleCodeMapForExport;
    /** 用于导出的监测岗位码表 */
    private Map<Integer, TsSimpleCode> postSimpleCodeMap;
    /** 导出数据集 */
    private List<Object[]> exportDataList;

    public ActiveMonitoringTaskExamineBean() {
        this.ifSQL = true;
        this.taskExamineCondition = new TaskExamineConditionPO();
        initZone();
        initSimpleCode();
        initState();
        initYear();
        super.searchAction();
    }

    /**
     *  <p>方法描述：年份初始化</p>
     * @MethodAuthor hsj 2023-07-25 15:41
     */
    private void initYear() {
        //年份, 最近10年
        this.yearList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            this.yearList.add(DateUtils.getYearInt() - i);
        }
        //默认当年
        this.taskExamineCondition.setYear(this.yearList.get(0));
    }

    /**
     *  <p>方法描述：状态初始化</p>
     * @MethodAuthor hsj 2023-07-20 14:18
     */
    private void initState() {
        //状态,默认“待审核”
        this.taskExamineCondition.setState(new Integer[]{1});
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem(0, "待提交"));
        this.stateList.add(new SelectItem(1, "待审核"));
        this.stateList.add(new SelectItem(3, "已退回"));
        this.stateList.add(new SelectItem(2, "审核通过"));
    }

    /**
     *  <p>方法描述：初始化地区</p>
     * @MethodAuthor hsj 2023-07-20 14:14
     */
    private void initZone() {
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneCode(), true,null,null);
        if(!CollectionUtils.isEmpty(this.zoneList)){
            this.taskExamineCondition.setZoneCode(this.zoneList.get(0).getZoneCode());
            this.taskExamineCondition.setZoneId(this.zoneList.get(0).getRid());
            this.taskExamineCondition.setZoneName(this.zoneList.get(0).getZoneName());
        }
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {
        this.jcTask = this.activeMonitoringTaskService.findTbTjJcTaskByRid(this.rid);
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                .findComponent("tabView:viewForm:jcTaskPsnTable");
        if (dataTable == null) {
            return;
        }
        dataTable.setFirst(0);
        dataTable.setRows(20);
    }

    @Override
    public void modInit() {
        this.jcTask = this.activeMonitoringTaskService.findTbTjJcTaskByRid(this.rid);
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:jcTaskPsnTable");
        if (dataTable == null) {
            return;
        }
        dataTable.setFirst(0);
        dataTable.setRows(20);
    }

    @Override
    public void saveAction() {

    }

    /**
     * 初始化码表相关
     */
    private void initSimpleCode() {
        //企业规模
        if (CollectionUtils.isEmpty(this.crptSizeList)) {
            this.crptSizeList = this.commService.findLevelSimpleCodesByTypeId("5004");
        }
        this.simpleCodeMapForExport = new HashMap<>();
        //行业类别
        this.fillSimpleCodeMapForExport("5002", true);
        //经济类型
        this.fillSimpleCodeMapForExport("5003", true);
        //企业规模
        this.fillSimpleCodeMapForExport("5004", false);
        //证件类型
        this.fillSimpleCodeMapForExport("5503", false);
        //在岗状态
        this.fillSimpleCodeMapForExport("5009", false);
        //防护用品佩戴情况
        this.fillSimpleCodeMapForExport("5618", false);
        this.postSimpleCodeMap = new HashMap<>();
        //监测岗位名称
        List<TsSimpleCode> simpleCodeList = this.commService.findallSimpleCodesByTypeIdOrderByExtends1("5595");
        if (!CollectionUtils.isEmpty(simpleCodeList)) {
            for (TsSimpleCode simpleCode : simpleCodeList) {
                this.postSimpleCodeMap.put(simpleCode.getRid(), simpleCode);
            }
        }
    }

    /**
     * <p>方法描述：simpleCodeMapForExport 填充 </p>
     * pw 2023/12/16
     **/
    private void fillSimpleCodeMapForExport(String typeNo, boolean ifCodePath) {
        List<TsSimpleCode> simpleCodeList = this.commService.findallSimpleCodesByTypeIdOrderByExtends1(typeNo);
        if (CollectionUtils.isEmpty(simpleCodeList)) {
            return;
        }
        for (TsSimpleCode simpleCode : simpleCodeList) {
            Integer rid = simpleCode.getRid();
            String fillVal = ifCodePath ? simpleCode.getCodePath() : simpleCode.getCodeName();
            if (StringUtils.isBlank(fillVal)) {
                continue;
            }
            this.simpleCodeMapForExport.put(rid, fillVal);
        }
    }


    @Override
    public String[] buildHqls() {
        return taskExamineService.searchDatas(this.taskExamineCondition,this.paramMap);
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<Object[]> result = CollectionUtil.castList(Object[].class, list);
        for (Object[] obj : result) {
            //地区去除省份
            String fullName = StringUtils.objectToString(obj[1]);
            obj[1] = fullName.substring(fullName.indexOf("_") + 1);
            //处理页面显示状态
            obj[8] = "";
            String state = StringUtils.objectToString(obj[10]);
            switch (state) {
                case "0":
                    obj[8] = "待提交";
                    break;
                case "1":
                    obj[8] = "待审核";
                    break;
                case "2":
                    obj[8] = "审核通过";
                    break;
                case "3":
                    obj[8] = "已退回";
                    break;
            }
            //仅已退回显示退回原因
            if (!"3".equals(state)) {
                obj[9] = "";
            }
        }
    }
    /**
     *  <p>方法描述：清除页面选择码表</p>
     * @MethodAuthor hsj 2023-07-20 14:26
     */
    public void clearSimpleCode() {
        switch (this.simpleCodeOpType) {
            case "5002":
                this.taskExamineCondition.setIndusTypeIds(null);
                this.taskExamineCondition.setIndusTypeNames(null);
                break;
            case "5003":
                this.taskExamineCondition.setEconomyIds(null);
                this.taskExamineCondition.setEconomyNames(null);
                break;
            default:
                break;
        }
    }

    /**
     *  <p>方法描述：行业类别 ，经济类型</p>
     * @MethodAuthor hsj 2023-07-20 14:33
     */
    public void selSimpleCodeAction() {
        this.taskExamineService.selSimpleCodeAction(this.taskExamineCondition,this.simpleCodeOpType);
    }

   /**
    *  <p>方法描述：行业类别 ，经济类型选中之后</p>
    * @MethodAuthor hsj 2023-07-20 14:36
    */
    public void onSimpleCodeAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (ObjectUtil.isEmpty(selectedMap)) {
            return;
        }
        List<TsSimpleCode> list = CollectionUtil.castList(TsSimpleCode.class, selectedMap.get("selectPros"));
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        StringBuilder names = new StringBuilder();
        StringBuilder ids = new StringBuilder();
        for (TsSimpleCode t : list) {
            names.append("，").append(t.getCodeName());
            ids.append(",").append(t.getRid());
        }
        switch (this.simpleCodeOpType) {
            case "5002":
                this.taskExamineCondition.setIndusTypeIds(ids.substring(1));
                this.taskExamineCondition.setIndusTypeNames(names.substring(1));
                break;
            case "5003":
                this.taskExamineCondition.setEconomyIds(ids.substring(1));
                this.taskExamineCondition.setEconomyNames(names.substring(1));
                break;
            default:
                break;
        }
    }

    /**
     *  <p>方法描述：审核通过验证</p>
     * @MethodAuthor hsj 2023-07-20 15:35
     */
    public void beforeExamineAction(){
        if (verifyExamineState()) {
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show()");
    }
    /**
     *  <p>方法描述：验证当前状态是否为待审核</p>
     * @MethodAuthor hsj 2023-07-20 15:37
     */
    private boolean verifyExamineState() {
        String state = taskExamineService.findStateByRid(this.rid);
        switch (state){
            case "0":
                JsfUtil.addErrorMessage("主动监测任务为待提交不可审核通过！");
                return true;
            case "1":
                return false;
            case "2":
                JsfUtil.addErrorMessage("主动监测任务已审核通过不可审核通过！");
                return true;
            case "3":
                JsfUtil.addErrorMessage("主动监测任务已退回不可审核通过！");
                return true;
            default:
                break;
        }
        return false;
    }
    /**
     *  <p>方法描述：审核通过</p>
     * @MethodAuthor hsj 2023-07-20 15:47
     */
    public void examineAction(){
        try {
            this.taskExamineService.examineTask(this.jcTask);
            this.viewInitAction();
            this.searchAction();
            JsfUtil.addSuccessMessage("审核通过！");
            RequestContext.getCurrentInstance().update("tabView");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("审核通过失败！");
        }
    }

    /**
     *  <p>方法描述：退回验证</p>
     * @MethodAuthor hsj 2023-07-20 16:08
     */
    public void beforeReturnAction(){
        if (verifyReturnState()) {
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ReturnDialog').show()");

    }

    /**
     *  <p>方法描述：验证当前状态是否为待审核</p>
     * @MethodAuthor hsj 2023-07-20 15:37
     */
    private boolean verifyReturnState() {
        String state = taskExamineService.findStateByRid(this.rid);
        switch (state){
            case "0":
                JsfUtil.addErrorMessage("主动监测任务为待提交不可退回！");
                return true;
            case "1":
                return false;
            case "2":
                JsfUtil.addErrorMessage("主动监测任务已审核通过不可退回！");
                return true;
            case "3":
                JsfUtil.addErrorMessage("主动监测任务已退回不可退回！");
                return true;
            default:
                break;
        }
        return false;
    }
    /**
     *  <p>方法描述：退回</p>
     * @MethodAuthor hsj 2023-07-20 16:14
     */
    public void returnBackAction(){
        if(StringUtils.isBlank(this.jcTask.getBackRsn())){
            JsfUtil.addErrorMessage("退回原因不允许为空！");
            return;
        }else if(this.jcTask.getBackRsn().length() > 200){
            JsfUtil.addErrorMessage("退回原因不能超过200！");
            return;
        }
        try{
            this.taskExamineService.returnTask(this.jcTask);
            this.backAction();
            this.searchAction();
            JsfUtil.addSuccessMessage("退回成功！");
            RequestContext.getCurrentInstance().execute("PF('ReturnDialog').hide()");
            RequestContext.getCurrentInstance().update("tabView");
        }catch(Exception e){
            JsfUtil.addErrorMessage("退回失败！");
            e.printStackTrace();
        }
    }
    /**
     *  <p>方法描述：批量审核验证</p>
     * @MethodAuthor hsj 2023-07-20 17:19
     */
    public void beforeBatchExamineAction() {
        if(this.selectEntitys == null || this.selectEntitys.size()==0){
            JsfUtil.addErrorMessage("请选择需要审核的数据！");
            return ;
        }
        RequestContext.getCurrentInstance().execute("PF('ReviewConfirmDialog').show()");
    }

    /**
     *  <p>方法描述：批量审核通过</p>
     * @MethodAuthor hsj 2023-07-20 16:59
     */
     public void batchExamineAction(){
         try {
             this.taskExamineService.batchExamineTask(this.selectEntitys);
             this.searchAction();
             JsfUtil.addSuccessMessage("批量审核成功！");
         }catch (Exception e){
             e.printStackTrace();
             JsfUtil.addErrorMessage("批量审核失败！");
         }

     }
     /**
      *  <p>方法描述：撤销</p>
      * @MethodAuthor hsj 2023-09-27 10:08
      */
    public void cancleAction() {
        try {
            this.taskExamineService.cancleAction(this.rid);
            JsfUtil.addSuccessMessage("撤销成功！");
            this.modInitAction();
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
    }

    /**
     * <p>方法描述：导出前校验 </p>
     * pw 2023/12/19
     **/
    public void exportBefore() {
        this.queryDataForExport();
        if (ObjectUtil.isEmpty(this.exportDataList)) {
            JsfUtil.addErrorMessage("导出无数据！");
            return;
        }
        RequestContext.getCurrentInstance().execute("getDownloadFileClick();");
    }

    /**
     * <p>方法描述：导出数据 </p>
     * pw 2023/12/16
     **/
    public DefaultStreamedContent exportData() {
        ExcelExportUtil excelExportUtil;
        String[] excelHeaders = new String[]{"序号","年份","检查机构地区","检查机构名称","单位名称","单位所属区域","社会信用代码","单位地址","行业类别","经济性质","企业规模","姓名","证件类型","证件号码","监测岗位名称","防护用品佩戴情况","在岗状态","职业病主动监测因素","备注（接触的其他职业病危害因素）","已上报的体检编号","状态","退回原因"};
        String title = this.taskExamineCondition.getYear()+this.taskExamineCondition.getZoneName()+"主动监测任务名单";
        excelExportUtil = new ExcelExportUtil(title, excelHeaders, pakExcelExportDataList(this.exportDataList, excelHeaders.length));
        excelExportUtil.setAdaptiveHeight(true);
        excelExportUtil.setFrozenPaneRowsNum(2);
        Workbook wb = excelExportUtil.exportExcel();
        if (wb != null) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                String fileName = URLEncoder.encode(title+".xlsx", "UTF-8");
                wb.write(baos);
                baos.flush();
                byte[] aa = baos.toByteArray();
                return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
            } catch (Exception e) {
                JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            }
        }
        return null;
    }

    /**
     * <p>方法描述：封装导出行数据 </p>
     * pw 2023/12/16
     **/
    private List<ExcelExportObject[]> pakExcelExportDataList(List<Object[]> dataList, int headerSize) {
        if (ObjectUtil.isEmpty(dataList) || headerSize <= 0) {
            return new ArrayList<>();
        }
        Set<Integer> centerIndex = new HashSet<>();
        centerIndex.add(0);
        centerIndex.add(1);
        centerIndex.add(6);
        centerIndex.add(10);
        centerIndex.add(11);
        centerIndex.add(12);
        centerIndex.add(13);
        centerIndex.add(14);
        centerIndex.add(15);
        centerIndex.add(16);
        centerIndex.add(19);
        centerIndex.add(20);
        List<ExcelExportObject[]> excelExportObjectList = new ArrayList<>();
        for (Object[] data : dataList) {
            ExcelExportObject[] objects = new ExcelExportObject[headerSize];
            for (int i=0; i < headerSize; i++) {
                String dataStr = StringUtils.objectToString(data[i]);
                objects[i] = centerIndex.contains(i) ? new ExcelExportObject(dataStr, XSSFCellStyle.ALIGN_CENTER) :
                        new ExcelExportObject(dataStr);
            }
            excelExportObjectList.add(objects);
        }
        return excelExportObjectList;
    }

    /**
     * <p>方法描述：查询封装准备导出的数据 </p>
     * pw 2023/12/16
     **/
    private void queryDataForExport() {
        this.searchAction();
        this.exportDataList = this.queryBaseDataForExport();
        if (CollectionUtils.isEmpty(this.exportDataList)) {
            return;
        }
        List<Integer> psnRidList = new ArrayList<>();
        for (Object[] objArr : this.exportDataList) {
            Integer psnRid = null == objArr[17] ? null :
                    Integer.parseInt(objArr[17].toString());
            if (null != psnRid) {
                psnRidList.add(psnRid);
            }
        }
        Map<Integer,Set<String>> badRsnMap = this.taskExamineService.findBadRsnByPsnIds(psnRidList);
        int xh = 1;
        for (Object[] objArr : this.exportDataList) {
            objArr[0] = xh++;
            objArr[8] = this.exchangeSimpleCodeVal(objArr[8]);
            objArr[9] = this.exchangeSimpleCodeVal(objArr[9]);
            objArr[10] = this.exchangeSimpleCodeVal(objArr[10]);
            objArr[12] = this.exchangeSimpleCodeVal(objArr[12]);
            objArr[15] = this.exchangeSimpleCodeVal(objArr[15]);
            objArr[16] = this.exchangeSimpleCodeVal(objArr[16]);
            Integer postRid = null == objArr[14] ? null : Integer.parseInt(objArr[14].toString());
            TsSimpleCode postSimpleCode = null == postRid ? null : this.postSimpleCodeMap.get(postRid);
            String postName = "";
            if (null != postSimpleCode) {
                postName = postSimpleCode.getCodeName();
            }
            String otherPostName = StringUtils.objectToString(objArr[22]);
            boolean ifMarch = null != postSimpleCode && "1".equals(postSimpleCode.getExtendS1())
                    && StringUtils.isNotBlank(otherPostName);
            if (ifMarch) {
                postName += "（"+otherPostName+"）";
            }
            objArr[14] = postName;
            Integer psnRid = null == objArr[17] ? null : Integer.parseInt(objArr[17].toString());
            //证件号码脱敏
            objArr[13] = StringUtils.encryptIdc(StringUtils.objectToString(objArr[13]));
            //危害因素
            objArr[17] = this.mixBadRsn(null == psnRid ? null : badRsnMap.get(psnRid));
            String state = StringUtils.objectToString(objArr[20]);
            String stateStr = "";
            switch (state) {
                case "0":
                    stateStr = "待提交";
                    break;
                case "1":
                    stateStr = "待审核";
                    break;
                case "2":
                    stateStr = "审核通过";
                    break;
                case "3":
                    stateStr = "已退回";
                    break;
            }
            objArr[20] = stateStr;
            if (!"3".equals(state)) {
                objArr[21] = "";
            }
        }
    }

    /**
     * <p>方法描述： 导出 拼接危害因素 </p>
     * pw 2023/12/16
     **/
    private String mixBadRsn(Set<String> badRsnSet) {
        if (CollectionUtils.isEmpty(badRsnSet)) {
            return "";
        }
        StringBuffer buffer = new StringBuffer();
        for (String badRsn : badRsnSet) {
            buffer.append("，").append(badRsn);
        }
        return buffer.substring(1);
    }

    /**
     * <p>方法描述：导出 普通码表转换 </p>
     * pw 2023/12/16
     **/
    private String exchangeSimpleCodeVal(Object obj) {
        if (null == obj) {
            return "";
        }
        String result = this.simpleCodeMapForExport.get(Integer.parseInt(obj.toString()));
        return null == result ? "" : result;
    }

    /**
     * <p>方法描述：查询准备导出的数据 </p>
     * pw 2023/12/16
     **/
    private List<Object[]> queryBaseDataForExport() {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT ")
                //序号列占位
                .append("      '' AS C0, ")
                //年份
                .append("      T.YEAR AS C1, ")
                //检查机构地区
                .append("      T2.FULL_NAME AS C2, ")
                //检查机构名称
                .append("      T1.UNITNAME AS C3, ")
                //单位名称
                .append("      T.CRPT_NAME AS C4, ")
                //单位所属区域
                .append("      T4.FULL_NAME AS C5, ")
                //社会信用代码
                .append("      T.CREDIT_CODE AS C6, ")
                //单位地址
                .append("      T.ADDRESS AS C7, ")
                //行业类别 需转换
                .append("      T.INDUS_TYPE_ID AS C8, ")
                //经济性质 需转换
                .append("      T.ECONOMY_ID AS C9, ")
                //企业规模 需转换
                .append("      T.CRPT_SIZE_ID AS C10, ")
                //姓名
                .append("      T3.PSN_NAME AS C11, ")
                //证件类型 需转换
                .append("      T3.CARD_TYPE_ID AS C12, ")
                //证件号码
                .append("      T3.IDC AS C13, ")
                //监测岗位名称 需转换
                .append("      T3.POST_ID AS C14, ")
                //防护用品佩戴情况
                .append("      T3.PROTECT_EQU_ID AS C23, ")
                //在岗状态 需转换
                .append("      T3.ONGUARD_STATEID AS C15, ")
                //职业病主动监测因素 需二次查询处理
                .append("      T3.RID AS C16, ")
                //备注
                .append("      T3.RMK AS C17, ")
                //已上报的体检编号
                .append("      T3.BHK_CODE AS C18, ")
                //状态
                .append("      T.STATE AS C19, ")
                //退回原因
                .append("      T.BACK_RSN AS C20, ")
                //其他监测岗位名称 需拼接
                .append("      T3.WORK_OTHER AS C21, ")
                .append("      '' AS C22 ")
                .append("  FROM TB_TJ_JC_TASK T ")
                .append("  LEFT JOIN TS_UNIT T1 ON T.ORG_ID = T1.RID ")
                .append("  LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID ")
                .append("  LEFT JOIN TB_TJ_JC_TASK_PSN T3 ON T.RID = T3.MAIN_ID ")
                .append("  LEFT JOIN TS_ZONE T4 ON T.ZONE_ID = T4.RID ")
                .append("  WHERE NVL(T.DEL_MARK,0)=0 ");
        Map<String,Object> sqlQueryParam = new HashMap<>();
        //地区
        if (ObjectUtil.isNotEmpty(taskExamineCondition.getZoneCode())) {
            sqlBuffer.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(taskExamineCondition.getZoneCode())).append("%'");
        }
        //机构名称
        if (ObjectUtil.isNotEmpty(taskExamineCondition.getOrgName())) {
            sqlQueryParam.put("orgName", "%" + StringUtils.convertBFH(taskExamineCondition.getOrgName()) + "%");
            sqlBuffer.append( " AND T1.UNITNAME LIKE :orgName escape '\\' ");
        }
        //年份
        if (ObjectUtil.isNotEmpty(taskExamineCondition.getYear())) {
            sqlQueryParam.put("year", taskExamineCondition.getYear());
            sqlBuffer.append("AND T.YEAR = :year ");
        }
        //单位名称
        if (ObjectUtil.isNotEmpty(taskExamineCondition.getCrptName())) {
            sqlQueryParam.put("crptName", "%" + StringUtils.convertBFH(taskExamineCondition.getCrptName()) + "%");
            sqlBuffer.append(" AND T.CRPT_NAME LIKE :crptName escape '\\' ");
        }
        //社会信用代码
        if (ObjectUtil.isNotEmpty(taskExamineCondition.getCreditCode())) {
            sqlQueryParam.put("creditCode", "%" + StringUtils.convertBFH(taskExamineCondition.getCreditCode().trim()) + "%");
            sqlBuffer.append(" AND T.CREDIT_CODE LIKE :creditCode escape '\\' ");
        }
        //行业类别
        if (StringUtils.isNotBlank(taskExamineCondition.getIndusTypeIds())) {
            List<String> indusTypeIdList = StringUtils.string2list(taskExamineCondition.getIndusTypeIds(), ",");
            sqlQueryParam.put("indusTypeIdList", indusTypeIdList);
            sqlBuffer.append("  AND T.INDUS_TYPE_ID IN (:indusTypeIdList) ");
        }
        //经济类型
        if (StringUtils.isNotBlank(taskExamineCondition.getEconomyIds())) {
            List<String> economyIdList = StringUtils.string2list(taskExamineCondition.getEconomyIds(), ",");
            sqlQueryParam.put("economyIdList", economyIdList);
            sqlBuffer.append(" AND T.ECONOMY_ID IN (:economyIdList) ");
        }
        //企业规模
        if (StringUtils.isNotBlank(taskExamineCondition.getCrptSizeIds())) {
            List<String> crptSizeIdList = StringUtils.string2list(taskExamineCondition.getCrptSizeIds(), ",");
            sqlQueryParam.put("crptSizeIdList", crptSizeIdList);
            sqlBuffer.append(" AND T.CRPT_SIZE_ID IN (:crptSizeIdList) ");
        }
        //姓名
        if (StringUtils.isNotBlank(taskExamineCondition.getPsnName())) {
            sqlQueryParam.put("psnName", "%" + StringUtils.convertBFH(taskExamineCondition.getPsnName().trim()) + "%");
            sqlBuffer.append(" AND T3.PSN_NAME LIKE :psnName escape '\\' ");
        }
        //身份证号
        if (StringUtils.isNotBlank(taskExamineCondition.getIdc())) {
            sqlQueryParam.put("idc", taskExamineCondition.getIdc());
            sqlBuffer.append(" AND T3.IDC = :idc ");
        }
        //状态
        if (ObjectUtil.isNotEmpty(taskExamineCondition.getState())) {
            List<Integer> searchStateList = new ArrayList<>(Arrays.asList(taskExamineCondition.getState()));
            sqlQueryParam.put("searchStateList", searchStateList);
            sqlBuffer.append(" AND T.STATE IN (:searchStateList) ");
        }
        //检查机构地区、检查机构、单位名称、社会信用代码、花名册RID 排序
        sqlBuffer.append(" ORDER BY T2.ZONE_GB,T1.UNITNAME,T.CRPT_NAME,T.CREDIT_CODE,T3.RID ");
        return this.commService.findDataBySqlNoPage(sqlBuffer.toString(), sqlQueryParam);
    }
    public TaskExamineConditionPO getTaskExamineCondition() {
        return taskExamineCondition;
    }

    public void setTaskExamineCondition(TaskExamineConditionPO taskExamineCondition) {
        this.taskExamineCondition = taskExamineCondition;
    }

    public List<TsSimpleCode> getCrptSizeList() {
        return crptSizeList;
    }

    public void setCrptSizeList(List<TsSimpleCode> crptSizeList) {
        this.crptSizeList = crptSizeList;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSimpleCodeOpType() {
        return simpleCodeOpType;
    }

    public void setSimpleCodeOpType(String simpleCodeOpType) {
        this.simpleCodeOpType = simpleCodeOpType;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TbTjJcTask getJcTask() {
        return jcTask;
    }

    public void setJcTask(TbTjJcTask jcTask) {
        this.jcTask = jcTask;
    }

    public List<Object[]> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<Object[]> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }

    public List<Integer> getYearList() {
        return yearList;
    }

    public void setYearList(List<Integer> yearList) {
        this.yearList = yearList;
    }
}
