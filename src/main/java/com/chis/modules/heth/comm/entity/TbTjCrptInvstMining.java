package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-3-26
 */
@Entity
@Table(name = "TB_TJ_CRPT_INVST_MINING")
@SequenceGenerator(name = "TbTjCrptInvstMining", sequenceName = "TB_TJ_CRPT_INVST_MINING_SEQ", allocationSize = 1)
public class TbTjCrptInvstMining implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbTjCrptInvest fkByMainId;
	private TsSimpleCode fkByMiningId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TbTjCrptInvstMining() {
	}

	public TbTjCrptInvstMining(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjCrptInvstMining")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TbTjCrptInvest getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TbTjCrptInvest fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "MINING_ID")			
	public TsSimpleCode getFkByMiningId() {
		return fkByMiningId;
	}

	public void setFkByMiningId(TsSimpleCode fkByMiningId) {
		this.fkByMiningId = fkByMiningId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}