package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2023-5-12
 */
@Entity
@Table(name = "TB_TJ_CRPT_CONSTRUCT")
@SequenceGenerator(name = "TbTjCrptConstruct", sequenceName = "TB_TJ_CRPT_CONSTRUCT_SEQ", allocationSize = 1)
public class TbTjCrptConstruct implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbTjCrpt fkByCrptId;
	private TsSimpleCode fkByConstructId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TbTjCrptConstruct() {
	}

	public TbTjCrptConstruct(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjCrptConstruct")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "CRPT_ID")			
	public TbTjCrpt getFkByCrptId() {
		return fkByCrptId;
	}

	public void setFkByCrptId(TbTjCrpt fkByCrptId) {
		this.fkByCrptId = fkByCrptId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CONSTRUCT_ID")			
	public TsSimpleCode getFkByConstructId() {
		return fkByConstructId;
	}

	public void setFkByConstructId(TsSimpleCode fkByConstructId) {
		this.fkByConstructId = fkByConstructId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}