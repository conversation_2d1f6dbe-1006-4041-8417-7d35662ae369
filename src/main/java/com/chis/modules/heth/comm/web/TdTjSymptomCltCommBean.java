package com.chis.modules.heth.comm.web;

import java.util.ArrayList;
import java.util.List;

import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.heth.comm.entity.TdTjBhkClt;
import com.chis.modules.heth.comm.entity.TdTjSymptomClt;
import com.chis.modules.heth.comm.logic.SymCodeCommPO;
import com.chis.modules.heth.comm.logic.SymCodeRowCommPO;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesEditBean;

/**
 * @Description : 问诊症状信息（数据录入）
 * @ClassAuthor : anjing
 * @Date : 2019/5/19 16:15
 **/
public class TdTjSymptomCltCommBean extends FacesEditBean {

    /**添加页面：问诊症状信息（数据录入）*/
    private TdTjSymptomClt tdTjSymptomClt = new TdTjSymptomClt();
    /**关联：体检信息（录入数据）*/
    private TdTjBhkClt fkByBhkId;

    /** 当前选中症状 */
    private String[] selectSymIds;
    private String othsym;
    private List<TsSimpleCode> symCodeList = new ArrayList<>();

    /**症状-扩展字段*/
    private String extends1;

    /**
     * 是否显示其他
     */
    private Boolean showOtherInfo;

    /**症状行集合*/
    private List<SymCodeRowCommPO> symCodeRowCommPOList;

    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    public TdTjSymptomCltCommBean() {
    }

    @Override
    public void addInit() {

    }

    /**
     * @Description : 参数初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 16:26
     **/
    public void initParam() {
        // 症状复选框初始化
        this.symCodeList = this.commService.findallSimpleCodesByTypeIdAndCodeLevelNo("5006");

        this.initSymCodeRowCommPOList();
    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {

    }

    /**
     * @Description : 执行保存
     * @MethodAuthor: anjing
     * @Date : 2019/5/20 9:50
     **/
    @Override
    public void saveAction() {

    }

    /***
    * @Description : 症状-初始化
    * @MethodAuthor: anjing
    * @Date : 2020/4/23 15:40
    **/
    public void initSymCodeRowCommPOList() {
        this.symCodeRowCommPOList = new ArrayList<>();
        for(int i=0; i<this.symCodeList.size(); i+=5) {
            SymCodeRowCommPO symCodeRowCommPO = new SymCodeRowCommPO();

            for(int j=0; j<5; j++) {
                if((i+j) < this.symCodeList.size()) {
                    SymCodeCommPO symCodeCommPO = new SymCodeCommPO();
                    symCodeCommPO.setRid(this.symCodeList.get(i+j).getRid());
                    symCodeCommPO.setCodeName(this.symCodeList.get(i+j).getCodeName());
                    symCodeCommPO.setCodeNo(this.symCodeList.get(i+j).getCodeNo());
                    symCodeCommPO.setExtendS1(this.symCodeList.get(i+j).getExtendS1());
                    symCodeRowCommPO.getSymCodeList().add(symCodeCommPO);
                }
            }
            this.symCodeRowCommPOList.add(symCodeRowCommPO);
        }
    }

    @Override
    public String[] buildHqls() {
        return new String[0];
    }

    public TdTjSymptomClt getTdTjSymptomClt() {
        return tdTjSymptomClt;
    }

    public void setTdTjSymptomClt(TdTjSymptomClt tdTjSymptomClt) {
        this.tdTjSymptomClt = tdTjSymptomClt;
    }

    public TdTjBhkClt getFkByBhkId() {
        return fkByBhkId;
    }

    public void setFkByBhkId(TdTjBhkClt fkByBhkId) {
        this.fkByBhkId = fkByBhkId;
    }


    public String getExtends1() {
        return extends1;
    }

    public void setExtends1(String extends1) {
        this.extends1 = extends1;
    }

    public List<TsSimpleCode> getSymCodeList() {
        return symCodeList;
    }

    public void setSymCodeList(List<TsSimpleCode> symCodeList) {
        this.symCodeList = symCodeList;
    }

    public String[] getSelectSymIds() {
        return selectSymIds;
    }

    public void setSelectSymIds(String[] selectSymIds) {
        this.selectSymIds = selectSymIds;
    }

    public String getOthsym() {
        return othsym;
    }

    public void setOthsym(String othsym) {
        this.othsym = othsym;
    }

    public List<SymCodeRowCommPO> getSymCodeRowCommPOList() {
        return symCodeRowCommPOList;
    }

    public void setSymCodeRowCommPOList(List<SymCodeRowCommPO> symCodeRowCommPOList) {
        this.symCodeRowCommPOList = symCodeRowCommPOList;
    }

    public Boolean getShowOtherInfo() {
        return showOtherInfo;
    }

    public void setShowOtherInfo(Boolean showOtherInfo) {
        this.showOtherInfo = showOtherInfo;
    }
}
