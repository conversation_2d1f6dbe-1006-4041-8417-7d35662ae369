package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 职业卫生和放射卫生报送卡数据上传日志表
 * <AUTHOR>
 * @createTime 2022-12-14
 */
@Entity
@Table(name = "TD_ZYWS_CARD_RCD")
@SequenceGenerator(name = "TdZywsCardRcd", sequenceName = "TD_ZYWS_CARD_RCD_SEQ", allocationSize = 1)
public class TdZywsCardRcd implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer busType;
	private Integer busId;
	private Integer state;
	private Date uploadDate;
	private Integer reportId;
	private String reportCode;
	private Integer reportYear;
	private String errMsg;
	private Date modifyDate;
	private Integer modifyManid;
	private Date createDate;
	private Integer createManid;
	
	public TdZywsCardRcd() {
	}

	public TdZywsCardRcd(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZywsCardRcd")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "BUS_TYPE")	
	public Integer getBusType() {
		return busType;
	}

	public void setBusType(Integer busType) {
		this.busType = busType;
	}	
			
	@Column(name = "BUS_ID")	
	public Integer getBusId() {
		return busId;
	}

	public void setBusId(Integer busId) {
		this.busId = busId;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "UPLOAD_DATE")			
	public Date getUploadDate() {
		return uploadDate;
	}

	public void setUploadDate(Date uploadDate) {
		this.uploadDate = uploadDate;
	}	
			
	@Column(name = "REPORT_ID")	
	public Integer getReportId() {
		return reportId;
	}

	public void setReportId(Integer reportId) {
		this.reportId = reportId;
	}	
			
	@Column(name = "REPORT_CODE")	
	public String getReportCode() {
		return reportCode;
	}

	public void setReportCode(String reportCode) {
		this.reportCode = reportCode;
	}	
			
	@Column(name = "REPORT_YEAR")	
	public Integer getReportYear() {
		return reportYear;
	}

	public void setReportYear(Integer reportYear) {
		this.reportYear = reportYear;
	}	
			
	@Column(name = "ERR_MSG")	
	public String getErrMsg() {
		return errMsg;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
}