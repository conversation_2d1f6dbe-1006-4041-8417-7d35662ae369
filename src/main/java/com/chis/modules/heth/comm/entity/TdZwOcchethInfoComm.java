package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2023-7-6
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_INFO")
@SequenceGenerator(name = "TdZwOcchethInfo", sequenceName = "TD_ZW_OCCHETH_INFO_SEQ", allocationSize = 1)
public class TdZwOcchethInfoComm implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TsUnit fkByOrgId;
    private String orgName;
    private String orgAddr;
    private String orgFz;
    private String orgFzzw;
    private String linkMan;
    private String linkMb;
    private String linkTel;
    private String fax;
    private String zipcode;
    private String email;
    private String certNo;
    private Date firstGetday;
    private Date validDate;
    private Integer state;
    private Integer cancelState;
    private Date cancelDate;
    private TsSimpleCode fkByLevelId;
    private Date createDate;
    private Integer createManid;
    private String communiAddr;
    private TsSimpleCode fkByUnitTypeId;
    private String labAddr;
    private String rcdUnitName;
    private Integer checkRst;
    private String auditAdv;
    private TsUserInfo fkByChkPsnId;

    public TdZwOcchethInfoComm() {
    }

    public TdZwOcchethInfoComm(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethInfo")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "ORG_ID")
    public TsUnit getFkByOrgId() {
        return fkByOrgId;
    }

    public void setFkByOrgId(TsUnit fkByOrgId) {
        this.fkByOrgId = fkByOrgId;
    }

    @Column(name = "ORG_NAME")
    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    @Column(name = "ORG_ADDR")
    public String getOrgAddr() {
        return orgAddr;
    }

    public void setOrgAddr(String orgAddr) {
        this.orgAddr = orgAddr;
    }

    @Column(name = "ORG_FZ")
    public String getOrgFz() {
        return orgFz;
    }

    public void setOrgFz(String orgFz) {
        this.orgFz = orgFz;
    }

    @Column(name = "ORG_FZZW")
    public String getOrgFzzw() {
        return orgFzzw;
    }

    public void setOrgFzzw(String orgFzzw) {
        this.orgFzzw = orgFzzw;
    }

    @Column(name = "LINK_MAN")
    public String getLinkMan() {
        return linkMan;
    }

    public void setLinkMan(String linkMan) {
        this.linkMan = linkMan;
    }

    @Column(name = "LINK_MB")
    public String getLinkMb() {
        return linkMb;
    }

    public void setLinkMb(String linkMb) {
        this.linkMb = linkMb;
    }

    @Column(name = "LINK_TEL")
    public String getLinkTel() {
        return linkTel;
    }

    public void setLinkTel(String linkTel) {
        this.linkTel = linkTel;
    }

    @Column(name = "FAX")
    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    @Column(name = "ZIPCODE")
    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    @Column(name = "EMAIL")
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Column(name = "CERT_NO")
    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "FIRST_GETDAY")
    public Date getFirstGetday() {
        return firstGetday;
    }

    public void setFirstGetday(Date firstGetday) {
        this.firstGetday = firstGetday;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "VALID_DATE")
    public Date getValidDate() {
        return validDate;
    }

    public void setValidDate(Date validDate) {
        this.validDate = validDate;
    }

    @Column(name = "STATE")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Column(name = "CANCEL_STATE")
    public Integer getCancelState() {
        return cancelState;
    }

    public void setCancelState(Integer cancelState) {
        this.cancelState = cancelState;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "CANCEL_DATE")
    public Date getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(Date cancelDate) {
        this.cancelDate = cancelDate;
    }

    @ManyToOne
    @JoinColumn(name = "LEVEL_ID")
    public TsSimpleCode getFkByLevelId() {
        return fkByLevelId;
    }

    public void setFkByLevelId(TsSimpleCode fkByLevelId) {
        this.fkByLevelId = fkByLevelId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Column(name = "COMMUNI_ADDR")
    public String getCommuniAddr() {
        return communiAddr;
    }

    public void setCommuniAddr(String communiAddr) {
        this.communiAddr = communiAddr;
    }

    @ManyToOne
    @JoinColumn(name = "UNIT_TYPE_ID")
    public TsSimpleCode getFkByUnitTypeId() {
        return fkByUnitTypeId;
    }

    public void setFkByUnitTypeId(TsSimpleCode fkByUnitTypeId) {
        this.fkByUnitTypeId = fkByUnitTypeId;
    }

    @Column(name = "LAB_ADDR")
    public String getLabAddr() {
        return labAddr;
    }

    public void setLabAddr(String labAddr) {
        this.labAddr = labAddr;
    }

    @Column(name = "RCD_UNIT_NAME")
    public String getRcdUnitName() {
        return rcdUnitName;
    }

    public void setRcdUnitName(String rcdUnitName) {
        this.rcdUnitName = rcdUnitName;
    }

    @Column(name = "CHECK_RST")
    public Integer getCheckRst() {
        return checkRst;
    }

    public void setCheckRst(Integer checkRst) {
        this.checkRst = checkRst;
    }

    @Column(name = "AUDIT_ADV")
    public String getAuditAdv() {
        return auditAdv;
    }

    public void setAuditAdv(String auditAdv) {
        this.auditAdv = auditAdv;
    }

    @ManyToOne
    @JoinColumn(name = "CHK_PSN_ID")
    public TsUserInfo getFkByChkPsnId() {
        return fkByChkPsnId;
    }

    public void setFkByChkPsnId(TsUserInfo fkByChkPsnId) {
        this.fkByChkPsnId = fkByChkPsnId;
    }

}