package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2024-10-11
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_BP_SUB")
@SequenceGenerator(name = "TdZwOcchethBpBadrsnSub", sequenceName = "TD_ZW_OCCHETH_BP_SUB_SEQ", allocationSize = 1)
public class TdZwOcchethBpSub implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwOcchethBpBadrsn fkByMainId;
    private TbYsjcLimitValComm fkByBadrsnId;
    private Integer badrsnNum;
    private Integer postNum;
    private Integer overPostNum;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    /**
     * 危害因素中文名称
     */
    private String badrsnName;

    /**
     * 危害因素对应的大类的码表扩展字段1
     */
    private String badrsnEx1;

    /**
     * 当前状态 0为添加，1为修改，2为详情
     */
    private String state = "0";

    /**修改前的危害因素*/
    private Integer oldBadrsnId;
    private List<TdZwOcchethBadrsnItem> badrsnItems = new ArrayList<>();

    /**弹出框-使用对象*/
    /**职业病危害因素*/
    private TbYsjcLimitValComm bulletBadrsnId;
    /**列表*/
    private List<TdZwOcchethBadrsnItem> bulletBadrsnItems = new ArrayList<>();
    private Integer bulletBadrsnNum;
    private Integer bulletPostNum;
    private Integer bulletOverPostNum;
    public TdZwOcchethBpSub() {
    }

    public TdZwOcchethBpSub(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethBpBadrsnSub")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwOcchethBpBadrsn getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwOcchethBpBadrsn fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "BADRSN_ID")
    public TbYsjcLimitValComm getFkByBadrsnId() {
        return fkByBadrsnId;
    }

    public void setFkByBadrsnId(TbYsjcLimitValComm fkByBadrsnId) {
        this.fkByBadrsnId = fkByBadrsnId;
    }

    @Column(name = "BADRSN_NUM")
    public Integer getBadrsnNum() {
        return badrsnNum;
    }

    public void setBadrsnNum(Integer badrsnNum) {
        this.badrsnNum = badrsnNum;
    }

    @Column(name = "POST_NUM")
    public Integer getPostNum() {
        return postNum;
    }

    public void setPostNum(Integer postNum) {
        this.postNum = postNum;
    }

    @Column(name = "OVER_POST_NUM")
    public Integer getOverPostNum() {
        return overPostNum;
    }

    public void setOverPostNum(Integer overPostNum) {
        this.overPostNum = overPostNum;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OrderBy("rid")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwOcchethBadrsnItem> getBadrsnItems() {
        return badrsnItems;
    }

    public void setBadrsnItems(List<TdZwOcchethBadrsnItem> badrsnItems) {
        this.badrsnItems = badrsnItems;
    }

    @Transient
    public String getBadrsnName() {
        return badrsnName;
    }

    public void setBadrsnName(String badrsnName) {
        this.badrsnName = badrsnName;
    }

    @Transient
    public String getBadrsnEx1() {
        return badrsnEx1;
    }

    public void setBadrsnEx1(String badrsnEx1) {
        this.badrsnEx1 = badrsnEx1;
    }

    @Transient
    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
    @Transient
    public Integer getOldBadrsnId() {
        return oldBadrsnId;
    }

    public void setOldBadrsnId(Integer oldBadrsnId) {
        this.oldBadrsnId = oldBadrsnId;
    }

    @Transient
    public TbYsjcLimitValComm getBulletBadrsnId() {
        return bulletBadrsnId;
    }

    public void setBulletBadrsnId(TbYsjcLimitValComm bulletBadrsnId) {
        this.bulletBadrsnId = bulletBadrsnId;
    }
    @Transient
    public List<TdZwOcchethBadrsnItem> getBulletBadrsnItems() {
        return bulletBadrsnItems;
    }

    public void setBulletBadrsnItems(List<TdZwOcchethBadrsnItem> bulletBadrsnItems) {
        this.bulletBadrsnItems = bulletBadrsnItems;
    }
    @Transient
    public Integer getBulletBadrsnNum() {
        return bulletBadrsnNum;
    }

    public void setBulletBadrsnNum(Integer bulletBadrsnNum) {
        this.bulletBadrsnNum = bulletBadrsnNum;
    }
    @Transient
    public Integer getBulletPostNum() {
        return bulletPostNum;
    }

    public void setBulletPostNum(Integer bulletPostNum) {
        this.bulletPostNum = bulletPostNum;
    }
    @Transient
    public Integer getBulletOverPostNum() {
        return bulletOverPostNum;
    }

    public void setBulletOverPostNum(Integer bulletOverPostNum) {
        this.bulletOverPostNum = bulletOverPostNum;
    }
}