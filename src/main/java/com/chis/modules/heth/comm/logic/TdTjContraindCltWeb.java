package com.chis.modules.heth.comm.logic;

import java.util.ArrayList;
import java.util.List;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * @Description : 职业禁忌症-体检结果页面显示
 * @ClassAuthor : anjing
 * @Date : 2019/5/28 14:21
 **/
public class TdTjContraindCltWeb {

    private Integer  badRsnId;
    private TsSimpleCode tsSimpleCodeByBadrsnId;
    private String contraind;
    private List<Integer> contraindIdList = new ArrayList<>();
    private List<TsSimpleCode> contraindCltList = new ArrayList<>();

    public TdTjContraindCltWeb() {
    }

    public TsSimpleCode getTsSimpleCodeByBadrsnId() {
        return tsSimpleCodeByBadrsnId;
    }

    public void setTsSimpleCodeByBadrsnId(TsSimpleCode tsSimpleCodeByBadrsnId) {
        this.tsSimpleCodeByBadrsnId = tsSimpleCodeByBadrsnId;
    }

    public String getContraind() {
        return contraind;
    }

    public void setContraind(String contraind) {
        this.contraind = contraind;
    }

    public List<Integer> getContraindIdList() {
        return contraindIdList;
    }

    public void setContraindIdList(List<Integer> contraindIdList) {
        this.contraindIdList = contraindIdList;
    }

    public List<TsSimpleCode> getContraindCltList() {
        return contraindCltList;
    }

    public void setContraindCltList(List<TsSimpleCode> contraindCltList) {
        this.contraindCltList = contraindCltList;
    }

    public Integer getBadRsnId() {
        return badRsnId;
    }

    public void setBadRsnId(Integer badRsnId) {
        this.badRsnId = badRsnId;
    }
}
