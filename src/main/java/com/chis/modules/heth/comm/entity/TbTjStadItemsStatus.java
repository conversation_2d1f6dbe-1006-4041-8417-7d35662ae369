package com.chis.modules.heth.comm.entity;

import javax.persistence.*;

import com.chis.modules.system.entity.TsUnit;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-4-24
 */
@Entity
@Table(name = "TB_TJ_STAD_ITEMS_STATUS")
@SequenceGenerator(name = "TbTjStadItemsStatus", sequenceName = "TB_TJ_STAD_ITEMS_STATUS_SEQ", allocationSize = 1)
public class TbTjStadItemsStatus implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsUnit fkByOrgId;
	/**0：保存1：提交*/
	private Integer state;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private Integer audioModel;
	
	private List<TbTjStadItems> itemList = new ArrayList<TbTjStadItems>(0);
	
	public TbTjStadItemsStatus() {
	}

	public TbTjStadItemsStatus(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjStadItemsStatus")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TsUnit getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TsUnit fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId")
	public List<TbTjStadItems> getItemList() {
		return itemList;
	}

	public void setItemList(List<TbTjStadItems> itemList) {
		this.itemList = itemList;
	}

    @Column(name = "AUDIO_MODEL")
    public Integer getAudioModel() {
        return audioModel;
    }

    public void setAudioModel(Integer audioModel) {
        this.audioModel = audioModel;
    }
}