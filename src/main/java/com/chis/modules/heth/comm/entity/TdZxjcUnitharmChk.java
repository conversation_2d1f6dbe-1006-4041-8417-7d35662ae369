package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-9-4
 */
@Entity
@Table(name = "TD_ZXJC_UNITHARM_CHK")
@SequenceGenerator(name = "TdZxjcUnitharmChk", sequenceName = "TD_ZXJC_UNITHARM_CHK_SEQ", allocationSize = 1)
public class TdZxjcUnitharmChk implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZxjcUnitbasicinfo fkByMainId;
	private Integer ifat;
	private Integer ifatDust;
	private Integer ifatChemistry;
	private Integer ifatPhysics;
	private Integer ifatEmit;
	private Integer ifatBiological;
	private Integer ifatOther;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZxjcUnitharmChk() {
	}

	public TdZxjcUnitharmChk(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZxjcUnitharmChk")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZxjcUnitbasicinfo getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZxjcUnitbasicinfo fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "IFAT")	
	public Integer getIfat() {
		return ifat;
	}

	public void setIfat(Integer ifat) {
		this.ifat = ifat;
	}	
			
	@Column(name = "IFAT_DUST")	
	public Integer getIfatDust() {
		return ifatDust;
	}

	public void setIfatDust(Integer ifatDust) {
		this.ifatDust = ifatDust;
	}	
			
	@Column(name = "IFAT_CHEMISTRY")	
	public Integer getIfatChemistry() {
		return ifatChemistry;
	}

	public void setIfatChemistry(Integer ifatChemistry) {
		this.ifatChemistry = ifatChemistry;
	}	
			
	@Column(name = "IFAT_PHYSICS")	
	public Integer getIfatPhysics() {
		return ifatPhysics;
	}

	public void setIfatPhysics(Integer ifatPhysics) {
		this.ifatPhysics = ifatPhysics;
	}	
			
	@Column(name = "IFAT_EMIT")	
	public Integer getIfatEmit() {
		return ifatEmit;
	}

	public void setIfatEmit(Integer ifatEmit) {
		this.ifatEmit = ifatEmit;
	}	
			
	@Column(name = "IFAT_BIOLOGICAL")	
	public Integer getIfatBiological() {
		return ifatBiological;
	}

	public void setIfatBiological(Integer ifatBiological) {
		this.ifatBiological = ifatBiological;
	}	
			
	@Column(name = "IFAT_OTHER")	
	public Integer getIfatOther() {
		return ifatOther;
	}

	public void setIfatOther(Integer ifatOther) {
		this.ifatOther = ifatOther;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}