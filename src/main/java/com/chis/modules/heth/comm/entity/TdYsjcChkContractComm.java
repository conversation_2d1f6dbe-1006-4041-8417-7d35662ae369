package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2021-5-21
 */
@Entity
@Table(name = "TD_YSJC_CHK_CONTRACT")
@SequenceGenerator(name = "TdYsjcChkContract", sequenceName = "TD_YSJC_CHK_CONTRACT_SEQ", allocationSize = 1)
public class TdYsjcChkContractComm implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TbTjCrpt fkByCrptId;
    private String crptName;
    private String institutionCode;
    private TsZone fkByZoneId;
    private String address;
    private TsSimpleCode fkByIndusTypeId;
    private TsSimpleCode fkByEconomyId;
    private TsSimpleCode fkByCrptSizeId;
    private String linkman2;
    private String linkphone2;
    private Integer workForce;
    private Integer outsourceNum;
    private String taskNo;
    private String proName;
    private TsSimpleCode fkByJcTypeId;
    private TsSimpleCode fkByOtherJcTypeId;
    private String otherJcType;
    private Date orderDate;
    private Date preCompletionDate;
    private String annexPath;
    private Date investDate;
    private TsUnit fkByChkOrgId;
    private Date sampStartDate;
    private Date sampEndDate;
    private Date jcStartDate;
    private Date jcEndDate;
    private Integer state;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private String entrustUnitName;
    private String rptAnnexPath;
    private String manageNo;
    private String jcTaskNo;
    private Integer relTaskId;
    private String contractNo;
    private String contractCont;
    private Integer ifBack;

    /*职业卫生标准 rids*/
    private String selectUseStdRids;
    /*职业卫生标准 names*/
    private String selectUseStdNames;
    private String uuid;

    public TdYsjcChkContractComm() {
    }

    public TdYsjcChkContractComm(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdYsjcChkContract")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_ID")
    public TbTjCrpt getFkByCrptId() {
        return fkByCrptId;
    }

    public void setFkByCrptId(TbTjCrpt fkByCrptId) {
        this.fkByCrptId = fkByCrptId;
    }

    @Column(name = "CRPT_NAME")
    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    @Column(name = "INSTITUTION_CODE")
    public String getInstitutionCode() {
        return institutionCode;
    }

    public void setInstitutionCode(String institutionCode) {
        this.institutionCode = institutionCode;
    }

    @ManyToOne
    @JoinColumn(name = "ZONE_ID")
    public TsZone getFkByZoneId() {
        return fkByZoneId;
    }

    public void setFkByZoneId(TsZone fkByZoneId) {
        this.fkByZoneId = fkByZoneId;
    }

    @Column(name = "ADDRESS")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @ManyToOne
    @JoinColumn(name = "INDUS_TYPE_ID")
    public TsSimpleCode getFkByIndusTypeId() {
        return fkByIndusTypeId;
    }

    public void setFkByIndusTypeId(TsSimpleCode fkByIndusTypeId) {
        this.fkByIndusTypeId = fkByIndusTypeId;
    }

    @ManyToOne
    @JoinColumn(name = "ECONOMY_ID")
    public TsSimpleCode getFkByEconomyId() {
        return fkByEconomyId;
    }

    public void setFkByEconomyId(TsSimpleCode fkByEconomyId) {
        this.fkByEconomyId = fkByEconomyId;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_SIZE_ID")
    public TsSimpleCode getFkByCrptSizeId() {
        return fkByCrptSizeId;
    }

    public void setFkByCrptSizeId(TsSimpleCode fkByCrptSizeId) {
        this.fkByCrptSizeId = fkByCrptSizeId;
    }

    @Column(name = "LINKMAN2")
    public String getLinkman2() {
        return linkman2;
    }

    public void setLinkman2(String linkman2) {
        this.linkman2 = linkman2;
    }

    @Column(name = "LINKPHONE2")
    public String getLinkphone2() {
        return linkphone2;
    }

    public void setLinkphone2(String linkphone2) {
        this.linkphone2 = linkphone2;
    }

    @Column(name = "WORK_FORCE")
    public Integer getWorkForce() {
        return workForce;
    }

    public void setWorkForce(Integer workForce) {
        this.workForce = workForce;
    }

    @Column(name = "OUTSOURCE_NUM")
    public Integer getOutsourceNum() {
        return outsourceNum;
    }

    public void setOutsourceNum(Integer outsourceNum) {
        this.outsourceNum = outsourceNum;
    }

    @Column(name = "TASK_NO")
    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    @Column(name = "PRO_NAME")
    public String getProName() {
        return proName;
    }

    public void setProName(String proName) {
        this.proName = proName;
    }

    @ManyToOne
    @JoinColumn(name = "JC_TYPE_ID")
    public TsSimpleCode getFkByJcTypeId() {
        return fkByJcTypeId;
    }

    public void setFkByJcTypeId(TsSimpleCode fkByJcTypeId) {
        this.fkByJcTypeId = fkByJcTypeId;
    }

    @ManyToOne
    @JoinColumn(name = "OTHER_JC_TYPE_ID")
    public TsSimpleCode getFkByOtherJcTypeId() {
        return fkByOtherJcTypeId;
    }

    public void setFkByOtherJcTypeId(TsSimpleCode fkByOtherJcTypeId) {
        this.fkByOtherJcTypeId = fkByOtherJcTypeId;
    }

    @Column(name = "OTHER_JC_TYPE")
    public String getOtherJcType() {
        return otherJcType;
    }

    public void setOtherJcType(String otherJcType) {
        this.otherJcType = otherJcType;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "ORDER_DATE")
    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "PRE_COMPLETION_DATE")
    public Date getPreCompletionDate() {
        return preCompletionDate;
    }

    public void setPreCompletionDate(Date preCompletionDate) {
        this.preCompletionDate = preCompletionDate;
    }

    @Column(name = "ANNEX_PATH")
    public String getAnnexPath() {
        return annexPath;
    }

    public void setAnnexPath(String annexPath) {
        this.annexPath = annexPath;
    }

    @ManyToOne
    @JoinColumn(name = "CHK_ORG_ID")
    public TsUnit getFkByChkOrgId() {
        return fkByChkOrgId;
    }

    public void setFkByChkOrgId(TsUnit fkByChkOrgId) {
        this.fkByChkOrgId = fkByChkOrgId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "INVEST_DATE")
    public Date getInvestDate() {
        return investDate;
    }

    public void setInvestDate(Date investDate) {
        this.investDate = investDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "SAMP_START_DATE")
    public Date getSampStartDate() {
        return sampStartDate;
    }

    public void setSampStartDate(Date sampStartDate) {
        this.sampStartDate = sampStartDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "SAMP_END_DATE")
    public Date getSampEndDate() {
        return sampEndDate;
    }

    public void setSampEndDate(Date sampEndDate) {
        this.sampEndDate = sampEndDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "JC_START_DATE")
    public Date getJcStartDate() {
        return jcStartDate;
    }

    public void setJcStartDate(Date jcStartDate) {
        this.jcStartDate = jcStartDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "JC_END_DATE")
    public Date getJcEndDate() {
        return jcEndDate;
    }

    public void setJcEndDate(Date jcEndDate) {
        this.jcEndDate = jcEndDate;
    }

    @Column(name = "STATE")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Column(name = "ENTRUST_UNIT_NAME")
    public String getEntrustUnitName() {
        return entrustUnitName;
    }

    public void setEntrustUnitName(String entrustUnitName) {
        this.entrustUnitName = entrustUnitName;
    }

    @Column(name = "RPT_ANNEX_PATH")
    public String getRptAnnexPath() {
        return rptAnnexPath;
    }

    public void setRptAnnexPath(String rptAnnexPath) {
        this.rptAnnexPath = rptAnnexPath;
    }

    @Column(name = "MANAGE_NO")
    public String getManageNo() {
        return manageNo;
    }

    public void setManageNo(String manageNo) {
        this.manageNo = manageNo;
    }

    @Column(name = "JC_TASK_NO")
    public String getJcTaskNo() {
        return jcTaskNo;
    }

    public void setJcTaskNo(String jcTaskNo) {
        this.jcTaskNo = jcTaskNo;
    }

    @Transient
    public String getSelectUseStdRids() {
        return selectUseStdRids;
    }

    public void setSelectUseStdRids(String selectUseStdRids) {
        this.selectUseStdRids = selectUseStdRids;
    }

    @Transient
    public String getSelectUseStdNames() {
        return selectUseStdNames;
    }

    public void setSelectUseStdNames(String selectUseStdNames) {
        this.selectUseStdNames = selectUseStdNames;
    }

    @Column(name = "REL_TASK_ID")
    public Integer getRelTaskId() {
        return relTaskId;
    }

    public void setRelTaskId(Integer relTaskId) {
        this.relTaskId = relTaskId;
    }

    @Column(name = "CONTRACT_NO")
    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    @Column(name = "CONTRACT_CONT")
    public String getContractCont() {
        return contractCont;
    }

    public void setContractCont(String contractCont) {
        this.contractCont = contractCont;
    }

    @Column(name = "IF_BACK")
    public Integer getIfBack() {
        return ifBack;
    }

    public void setIfBack(Integer ifBack) {
        this.ifBack = ifBack;
    }

    @Column(name = "UUID")
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
}
