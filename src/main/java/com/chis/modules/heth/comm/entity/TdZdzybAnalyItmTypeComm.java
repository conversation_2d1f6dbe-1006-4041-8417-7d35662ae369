package com.chis.modules.heth.comm.entity;

import com.google.common.collect.Lists;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2018-7-6
 */
@Entity
@Table(name = "TD_ZDZYB_ANALY_ITM_TYPE")
@SequenceGenerator(name = "TdZdzybAnalyItmTypeComm", sequenceName = "TD_ZDZYB_ANALY_ITM_TYPE_SEQ", allocationSize = 1)
public class TdZdzybAnalyItmTypeComm implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZdzybSpecialAnalyComm fkByMainId;
	private String itemName;
	private String showName;
	private Integer xh;
	private Integer ruleLevel;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	private String itemCode;
	//评估子表集合
	private List<TdZdzybAnalyPgItemComm>  subPgList = Lists.newArrayList();;
	
	
	public TdZdzybAnalyItmTypeComm() {
	}

	public TdZdzybAnalyItmTypeComm(Integer rid) {
		this.rid = rid;
	}

    public TdZdzybAnalyItmTypeComm(String itemName, String showName) {
        this.itemName = itemName;
        this.showName = showName;
    }

    @Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZdzybAnalyItmTypeComm")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZdzybSpecialAnalyComm getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZdzybSpecialAnalyComm fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "ITEM_NAME")	
	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}	
			
	@Column(name = "SHOW_NAME")	
	public String getShowName() {
		return showName;
	}

	public void setShowName(String showName) {
		this.showName = showName;
	}	
			
	@Column(name = "XH")	
	public Integer getXh() {
		return xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}	
			
	@Column(name = "RULE_LEVEL")	
	public Integer getRuleLevel() {
		return ruleLevel;
	}

	public void setRuleLevel(Integer ruleLevel) {
		this.ruleLevel = ruleLevel;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER, mappedBy = "fkByMainId", orphanRemoval = true)
	public List<TdZdzybAnalyPgItemComm> getSubPgList() {
		return subPgList;
	}

	public void setSubPgList(List<TdZdzybAnalyPgItemComm> subPgList) {
		this.subPgList = subPgList;
	}

	@Column(name = "ITEM_CODE")	
	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}	
			
}