package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.entity.TbTjCrptIndepend;
import com.chis.modules.heth.comm.entity.TdZwBgkLastSta;
import com.chis.modules.heth.comm.entity.TdZwjdArchivesCard;
import com.chis.modules.heth.comm.json.CrptDialogParamJson;
import com.chis.modules.heth.comm.utils.ZwArchivesCardCommUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: 职业病鉴定报告卡填报
 *
 * @ClassAuthor pw,2021年11月8日,TdZwHethAppraisalRptCardListBean
 */
@ManagedBean(name = "tdZwHethAppraisalRptCardListBean")
@ViewScoped
public class TdZwHethAppraisalRptCardListBean extends AbstractReportCardAppraisalListBean {

    /** 是否市直属 用于判断初审的状态是1还是3 */
    private Integer isCityDirect;
    /** 是否生日脱敏 1 脱敏 */
    private Integer ifBirthEncrypt;
    public TdZwHethAppraisalRptCardListBean(){
        this.ifSQL = true;
        initState();
        this.searchAction();
    }

    /**
     *  <p>方法描述：添加页面初始化</p>
     * @MethodAuthor hsj
     */
    @Override
    public void addInit() {
        //初始化证件信息
        this.ifIdcAble = true;
        this.idcType = null;
        this.jdType = null;
        this.jdRst = null;
        /**病例类型*/
        this.rptTypeId = null;
        this.jdRptTypeId= null;
        this.jdAgainRstId= null;
        /**鉴定结果默认显示*/
        this.ifAgainRst = Boolean.TRUE;
        archivesCard = new TdZwjdArchivesCard();
        //鉴定机构默认当前单位名称
        archivesCard.setFkByJdUnitId(Global.getUser().getTsUnit());
        //填表人信息，默认为当前登录人
        archivesCard.setFillFormPsn( Global.getUser().getUsername());
        archivesCard.setFillLink(Global.getUser().getMbNum());
        archivesCard.setFillDate(new Date());
        archivesCard.setFkByFillUnitId(Global.getUser().getTsUnit());
        archivesCard.setFillUnitName(Global.getUser().getTsUnit().getUnitname());
        //报告人信息
        archivesCard.setRptPsn(Global.getUser().getUsername());
        archivesCard.setRptLink(Global.getUser().getMbNum());
        archivesCard.setRptDate(new Date());
        archivesCard.setRptUnitName(Global.getUser().getTsUnit().getUnitname());
        archivesCard.setDelMark(0);
        this.tdZwBgkLastSta = new TdZwBgkLastSta();
        this.tdZwBgkLastSta.setCartType(8);
        this.tdZwBgkLastSta.setState(0);
        ZwArchivesCardCommUtils.initEntity(archivesCard);
        //证件初始化
        if(!CollectionUtils.isEmpty(cardTypeList)){
            idcType = cardTypeList.get(0).getRid();
            archivesCard.setFkByCardTypeId(tsSimpleMap.get(idcType));
        }
        //职业病鉴定初始化
        zybjdCode = new TsSimpleCode();
        zybjgCode = new TsSimpleCode();
        //鉴定机构
        archivesCard.setFkByJdUnitId(Global.getUser().getTsUnit());
        archivesCard.setJdUnitName(Global.getUser().getTsUnit().getUnitname());
        archivesCard.setJdUnitCreditCode(Global.getUser().getTsUnit().getCreditCode());

    }
/**
 *  <p>方法描述：修改页面</p>
 * @MethodAuthor hsj
 */
    @Override
    public void modInit() {
        if (null==rid) {
            return;
        }

        this.ifIdcAble = true;
        this.idcType = null;
        this.jdType = null;
        this.jdRst = null;
        /**病例类型*/
         this.rptTypeId = null;
        this.jdRptTypeId= null;
        this.jdAgainRstId= null;
        /**鉴定结果默认显示*/
        this.ifAgainRst = Boolean.TRUE;
        //职业病鉴定初始化
        zybjdCode = new TsSimpleCode();
        zybjgCode = new TsSimpleCode();
        archivesCard =commService.find(TdZwjdArchivesCard.class, rid);
        this.tdZwBgkLastSta = this.chkSmaryService.findTdZwBgkLastSta(this.rid, 8);
        ZwArchivesCardCommUtils.initEntity(archivesCard);
        //证件类型
        if(null != archivesCard.getFkByCardTypeId() && null != archivesCard.getFkByCardTypeId().getRid()){
            idcType = archivesCard.getFkByCardTypeId().getRid();
            if("88".equals(archivesCard.getFkByCardTypeId().getCodeNo())) {
                this.ifIdcAble = false;
            }
        }
        //鉴定类型
        if(null != archivesCard.getFkByJdTypeId() && null != archivesCard.getFkByJdTypeId().getRid()){
            jdType = archivesCard.getFkByJdTypeId().getRid();
        }
        //首次鉴定
        if(null != archivesCard.getFkByJdRstId() && null != archivesCard.getFkByJdRstId().getRid()){
            jdRst = archivesCard.getFkByJdRstId().getRid();
        }
        //病例类型
        if(null != archivesCard.getFkByRptTypeId() && null != archivesCard.getFkByRptTypeId().getRid()){
            rptTypeId = archivesCard.getFkByRptTypeId().getRid();
        }
        if(null != archivesCard.getFkByJdRptTypeId() && null != archivesCard.getFkByJdRptTypeId().getRid()){
            jdRptTypeId = archivesCard.getFkByJdRptTypeId().getRid();
        }
        if(null != archivesCard.getFkByJdAgainRstId() && null != archivesCard.getFkByJdAgainRstId().getRid()){
            jdAgainRstId = archivesCard.getFkByJdAgainRstId().getRid();
        }
        zybjdCode = archivesCard.getFkByZybTypeId();
        zybjgCode = archivesCard.getFkByJdZybTypeId();

        if(null != archivesCard.getIfZyb() && 0 == archivesCard.getIfZyb()){
            if(null == archivesCard.getFkByZybDisTypeId() || null == archivesCard.getFkByZybDisTypeId().getRid()){
                archivesCard.setFkByZybDisTypeId(new TsSimpleCode("0","无"));
            }
            if(null == archivesCard.getFkByZybTypeId() || null == archivesCard.getFkByZybTypeId().getRid()){
                archivesCard.setFkByZybTypeId(new TsSimpleCode("0","无"));
            }
        }
        if(null != archivesCard.getIfJdZyb() && 0 == archivesCard.getIfJdZyb()) {
            if (null == archivesCard.getFkByJdZybDisTypeId() || null == archivesCard.getFkByJdZybDisTypeId().getRid()) {
                archivesCard.setFkByJdZybDisTypeId(new TsSimpleCode("0", "无"));
            }
            if (null == archivesCard.getFkByJdZybTypeId() || null == archivesCard.getFkByJdZybTypeId().getRid()) {
                archivesCard.setFkByJdZybTypeId(new TsSimpleCode("0", "无"));
            }
        }
        if("1".equals(archivesCard.getFkByJdTypeId().getExtendS1()) && "1".equals(archivesCard.getFkByJdRstId().getExtendS1())){
            this.ifAgainRst = Boolean.FALSE;
        }else if("2".equals(archivesCard.getFkByJdTypeId().getExtendS1()) && "1".equals(archivesCard.getFkByJdRstId().getExtendS1()) &&  archivesCard.getFkByJdAgainRstId() != null && "1".equals(archivesCard.getFkByJdAgainRstId().getExtendS1())){
            this.ifAgainRst = Boolean.FALSE;
        }else {
            this.ifAgainRst = Boolean.TRUE;
        }

        if(this.isView!=null&&this.isView==0&&StringUtils.isNotBlank(archivesCard.getAnnexPath())){
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.update("tabView:editForm");
            currentInstance.execute("disabledInput('true','archivesCardDiv')");
        }
        this.isView=0;

    }

    @Override
    public void viewInit() {
          this.isView=1;
          this.modInit();
    }

    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder();
        sb.append(" FROM TD_ZWJD_ARCHIVES_CARD T ");
        sb.append(" INNER JOIN TS_ZONE T1 ON T.ZONE_ID=T1.RID ");
        sb.append(" INNER JOIN TD_ZW_BGK_LAST_STA T2 ON T.RID=T2.BUS_ID AND T2.CART_TYPE=8 ");
        sb.append(" WHERE 1=1  ").append(" AND T.FILL_UNIT_ID = ").append(Global.getUser().getTsUnit().getRid());
        //用人单位地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sb.append(" AND T1.ZONE_GB LIKE :zonecode escape '\\\'");
            this.paramMap.put("zonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        //用人单位名称
        if (StringUtils.isNotBlank(this.searchUnitName)) {
            sb.append(" AND T.CRPT_NAME LIKE :unitName escape '\\\'");
            this.paramMap.put("unitName", "%" + StringUtils.convertBFH(this.searchUnitName.trim()) + "%");
        }
        //用工单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sb.append(" AND T.EMP_CRPT_NAME LIKE :crptName escape '\\\'");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        //姓名
        if (StringUtils.isNotBlank(this.searchPsnName)) {
            sb.append(" AND T.PERSONNEL_NAME LIKE :searchPsnName escape '\\\'");
            this.paramMap.put("searchPsnName", "%" + StringUtils.convertBFH(this.searchPsnName.trim()) + "%");
        }
        //证件号码
        if (StringUtils.isNotBlank(this.searchIdc)) {
            sb.append(" AND T.IDC =:searchIdc");
            this.paramMap.put("searchIdc", this.searchIdc.trim());
        }
        //鉴定类型
        if (null!=searchJdTypes && searchJdTypes.length>0) {
            sb.append(" AND T.JD_TYPE_ID IN (").append(StringUtils.array2string(searchJdTypes, ",")).append(")");
        }
        //鉴定日期
        if (null != this.searchJdBdate) {
            sb.append(" AND T.APRS_CENT_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchJdBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchJdEdate) {
            sb.append(" AND T.APRS_CENT_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchJdEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        //首次鉴定结论
        if (StringUtils.isNotBlank(this.selectFirstJdResultIds)) {
            sb.append(" AND T.JD_RST_ID IN (").append(this.selectFirstJdResultIds).append(")");
        }
        //再次鉴定结论
        if (StringUtils.isNotBlank(this.selectNextJdResulIds)) {
            sb.append(" AND T.JD_AGAIN_RST_ID IN (").append(this.selectNextJdResulIds).append(")");
        }
        //报告日期
        if (null != this.searchRptBdate) {
            sb.append(" AND T.RPT_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRptBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchRptEdate) {
            sb.append(" AND T.RPT_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRptEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        StringBuffer stateSb = new StringBuffer();
        if (null != states && states.length > 0) {
            for (String state : states) {
                stateSb.append(",").append(state);
            }
        }else{
            for(SelectItem itm : this.stateList){
                stateSb.append(",").append(itm.getValue().toString());
            }
        }
        if(stateSb.length() > 0){
            sb.append(" AND T2.STATE IN (").append(stateSb.substring(1)).append(")");
        }
        sb.append(" AND T.DEL_MARK !=1 ");
        String h2 = "SELECT COUNT(*) " + sb.toString();
        StringBuffer searchSql = new StringBuffer();
        searchSql.append(" SELECT T.RID, ")
                .append(" T1.FULL_NAME, ")//1 用人单位地区
                .append(" T.CRPT_NAME,  ")//2 用人单位名称
                .append(" T.PERSONNEL_NAME, ")//3 姓名
                .append(" T.IDC, ")//4 证件号码
                .append(" T.EMP_CRPT_NAME, ")//5 用工单位名称
                .append(" T.JD_TYPE_ID, ")//6 鉴定类型
                .append(" T.APRS_CENT_DATE, ")//7 鉴定日期
                .append(" T.JD_RST_ID, ")//8 首次鉴定结论
                .append(" T.JD_AGAIN_RST_ID, ")//9 再次鉴定结论
                .append(" T.RPT_DATE, ")//10 报告日期
                .append(" T2.STATE ")//11 状态
                .append(sb)
                .append(" ORDER BY T.RPT_DATE DESC, T1.ZONE_CODE,T.CRPT_NAME,T.PERSONNEL_NAME");
        return new String[] { searchSql.toString(), h2 };
    }

    @Override
    public void processData(List<?> list) {
        try{
            if (null!=list && list.size()>0) {
                List<Object[]> result = (List<Object[]>) list;
                for (Object[] objArr : result) {
                    //用人单位地区
                    String fullName = null == objArr[1] ? null : objArr[1].toString();
                    if(StringUtils.isNotBlank(fullName)){
                        objArr[1] = fullName.substring(fullName.indexOf("_")+1);
                    }
                    //鉴定日期
                    Date dateTmp = null == objArr[7] ? null : (Date) objArr[7];
                    if(null != dateTmp){
                        objArr[7] = DateUtils.formatDate(dateTmp, "yyyy-MM-dd");
                    }
                    //报告日期
                    dateTmp = null == objArr[10] ? null : (Date) objArr[10];
                    if(null != dateTmp){
                        objArr[10] = DateUtils.formatDate(dateTmp, "yyyy-MM-dd");
                    }
                    //鉴定类型
                    Integer simpleRid = null == objArr[6] ? null : Integer.parseInt(objArr[6].toString());
                    TsSimpleCode simpleCode = null == simpleRid ? null : tsSimpleMap.get(simpleRid);
                    if(null != simpleCode){
                        objArr[6] = simpleCode.getCodeName();
                    }else{
                        objArr[6] = null;
                    }
                    //首次鉴定结论
                    simpleRid = null == objArr[8] ? null : Integer.parseInt(objArr[8].toString());
                    simpleCode = null == simpleRid ? null : tsSimpleMap.get(simpleRid);
                    if(null != simpleCode){
                        objArr[8] = simpleCode.getCodeName();
                    }else{
                        objArr[8] = null;
                    }
                    //再次鉴定结论
                    simpleRid = null == objArr[9] ? null : Integer.parseInt(objArr[9].toString());
                    simpleCode = null == simpleRid ? null : tsSimpleMap.get(simpleRid);
                    if(null != simpleCode){
                        objArr[9] = simpleCode.getCodeName();
                    }else{
                        objArr[9] = null;
                    }
                }
            }
        }catch(Exception e){
            e.printStackTrace();
        }
    }


    /**
     * @Description: 删除职业病鉴定报告卡
     *
     * @MethodAuthor pw,2021年11月8日
     */
    public void deleteAction(){
        try{
            if(null == this.rid){
                JsfUtil.addErrorMessage("删除失败！");
                return;
            }
            TdZwjdArchivesCard archivesCard = this.commService.find(TdZwjdArchivesCard.class,this.rid);
            if(null == archivesCard){
                JsfUtil.addErrorMessage("删除失败！");
                return;
            }
            archivesCard.setDelMark(1);
            this.commService.upsertEntity(archivesCard);
            JsfUtil.addSuccessMessage("删除成功！");
        }catch(Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * @Description: 状态初始化
     *
     * @MethodAuthor pw,2021年11月8日
     */
    private void initState(){
        isCityDirect = 0;
        int level = 0;
        //获取行政地区
        TsZone tsZone = Global.getUser().getTsUnit().getTsZone();
        if("2".equals(checkLevel)){
            //是否省直属
            if(null != tsZone &&
                    StringUtils.isNotBlank(tsZone.getIfProvDirect()) && tsZone.getIfProvDirect().trim().equals("1")){
                level = 3;
            }else{
                level = 1;
            }
        }else if ("3".equals(checkLevel)) {
            //是否市直属
            if(null != tsZone &&
                    StringUtils.isNotBlank(tsZone.getIfCityDirect()) && tsZone.getIfCityDirect().trim().equals("1")){
                level = 3;
                isCityDirect = 1;
            }else{
                level = 1;
            }
        }
        states=new String[]{"0"};
        stateList.add(new SelectItem("0","待提交") );
        if(StringUtils.isNotBlank(checkLevel)){
            if("3".equals(checkLevel)){
                if(1 == level){
                    this.stateList.add(new SelectItem("1", "待初审"));
                    this.stateList.add(new SelectItem("2,4,6", "已退回"));
                    this.stateList.add(new SelectItem("3", "待复审"));
                    states=new String[]{"0","2,4,6"};
                }else{
                    this.stateList.add(new SelectItem("3", "待初审"));
                    this.stateList.add(new SelectItem("4,6", "已退回"));
                    states=new String[]{"0","4,6"};
                }
                stateList.add(new SelectItem("5","待终审") );
                stateList.add(new SelectItem("7","终审通过") );
            }else if("2".equals(checkLevel)){
                if(3 == level){
                    stateList.add(new SelectItem("5","待终审") );
                    stateList.add(new SelectItem("6","已退回") );
                    stateList.add(new SelectItem("7","终审通过") );
                    states=new String[]{"0","6"};
                }else{
                    this.stateList.add(new SelectItem("1", "待初审"));
                    this.stateList.add(new SelectItem("2,6", "已退回"));
                    stateList.add(new SelectItem("5","待终审") );
                    stateList.add(new SelectItem("7","终审通过") );
                    states=new String[]{"0","2,6"};
                }
            }
        }
    }
    /**
     *  <p>方法描述：初始化企业信息</p>
     * @MethodAuthor hsj
     */
    public void selectCrptList() {
        Map<String, Object> options = MapUtils.produceDialogMap(1080,1050,520,505);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<String>();
        paramList = new ArrayList<String>();
        CrptDialogParamJson json = new CrptDialogParamJson();
        json.setBusType("3");
        json.setIfShowOtherZone(true);
        json.setIfSearchZoneDefault(false);
        if("1".equals(crpyType)){
            json.setSearchCrptName(this.archivesCard.getCrptName());
        }else if("2".equals(crpyType)){
            json.setSearchCrptName(this.archivesCard.getEmpCrptName());
            json.setExcludeRids(this.archivesCard.getFkByCrptId().getRid().toString());
            json.setAreaValidate(4);
        }
        json.setIfSelectOperationStopData(Boolean.TRUE);
        paramList.add(JSON.toJSONString(json));
        paramMap.put("jsonParam", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        String useNewCrptSelect = this.commService.findParamValue("USE_NEW_CRPT_SELECT");
        String crptSelectPath = "/webapp/heth/comm/crptCommSelectList";
        if ("1".equals(useNewCrptSelect)) {
            crptSelectPath = "/webapp/heth/comm/crptCommSelectListNew";
        }
        requestContext.openDialog(crptSelectPath, options, paramMap);
    }
    /**
     *  <p>方法描述：选择企业后</p>
     * @MethodAuthor hsj
     */
    public void onCrptSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
            TbTjCrptIndepend tbTjCrptIndepend = (TbTjCrptIndepend) selectedMap.get("selectCrptIndepend");
            TbTjCrpt tbTjCrpt = tbTjCrptIndepend.getFkByCrptId();
            if("1".equals(crpyType)){
                //用人单位
                archivesCard.setFkByCrptId(tbTjCrpt);
                archivesCard.setCrptName(tbTjCrpt.getCrptName());
                archivesCard.setCreditCode(tbTjCrpt.getInstitutionCode());
                archivesCard.setFkByEconomyId(tbTjCrpt.getTsSimpleCodeByEconomyId());
                archivesCard.setFkByIndusTypeId(tbTjCrpt.getTsSimpleCodeByIndusTypeId());
                archivesCard.setFkByCrptSizeId(tbTjCrpt.getTsSimpleCodeByCrptSizeId());
                archivesCard.setFkByZoneId(tbTjCrpt.getTsZoneByZoneId());
                archivesCard.setAddress(tbTjCrpt.getAddress());
                archivesCard.setPostcode(tbTjCrpt.getPostCode());
                archivesCard.setSafeposition(tbTjCrptIndepend.getLinkman2());
                archivesCard.setSafephone(tbTjCrptIndepend.getLinkphone2());
                //若当前用人单位为非人力资源时
                if(!"2".equals(tbTjCrpt.getTsSimpleCodeByIndusTypeId().getExtendS1())){
                    archivesCard.setFkByEmpCrptId(tbTjCrpt);
                    archivesCard.setEmpCrptName(tbTjCrpt.getCrptName());
                    archivesCard.setEmpCreditCode(tbTjCrpt.getInstitutionCode());
                    archivesCard.setFkByEmpEconomyId(tbTjCrpt.getTsSimpleCodeByEconomyId());
                    archivesCard.setFkByEmpIndusTypeId(tbTjCrpt.getTsSimpleCodeByIndusTypeId());
                    archivesCard.setFkByEmpCrptSizeId(tbTjCrpt.getTsSimpleCodeByCrptSizeId());
                    archivesCard.setFkByEmpZoneId(tbTjCrpt.getTsZoneByZoneId());
                }
            }else if("2".equals(crpyType)){
                //用工单位
                archivesCard.setFkByEmpCrptId(tbTjCrpt);
                archivesCard.setEmpCrptName(tbTjCrpt.getCrptName());
                archivesCard.setEmpCreditCode(tbTjCrpt.getInstitutionCode());
                archivesCard.setFkByEmpEconomyId(tbTjCrpt.getTsSimpleCodeByEconomyId());
                archivesCard.setFkByEmpIndusTypeId(tbTjCrpt.getTsSimpleCodeByIndusTypeId());
                archivesCard.setFkByEmpCrptSizeId(tbTjCrpt.getTsSimpleCodeByCrptSizeId());
                archivesCard.setFkByEmpZoneId(tbTjCrpt.getTsZoneByZoneId());
            }
        }
    }
    /**
     *  <p>方法描述：初始化机构单位</p>
     * @MethodAuthor hsj
     */
    public void selectDiagUnitList() {
//        Map<String, Object> options = MapUtils.produceDialogMap(null,760,null,455);
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("contentHeight", 420);
        options.put("contentWidth", 760);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> typeList = new ArrayList<>();
        typeList.add("1");
        paramMap.put("orgType",typeList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/orgChoose/orgSelectList", options, paramMap);
    }
    /**
     *  <p>方法描述：选择机构单位后</p>
     * @MethodAuthor hsj
     */
    public void onDiagUnitSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
            Object[] selectPro = (Object[]) selectedMap.get("selectOrg");
            if(null != selectPro ){
                TsUnit tsUnit = this.commService.find(TsUnit.class,Integer.valueOf(StringUtils.objectToString(selectPro[0])));
                archivesCard.setFkByDiagUnitId(tsUnit);
                archivesCard.setDiagUnitName(StringUtils.objectToString(selectPro[2]));
            }else {
                JsfUtil.addErrorMessage("请选择诊断机构！");
            }
        }
    }
    /**
     *  <p>方法描述：人员类型的选择事件</p>
     * @MethodAuthor hsj
     */
    public void onPsnTypeChangeAction() {
        // “88”是无人员类型
        this.ifIdcAble = true;
            TsSimpleCode tsSimpleCode = tsSimpleMap.get(this.idcType);
            if(null != tsSimpleCode) {
                archivesCard.setFkByCardTypeId(tsSimpleCode);
                if("88".equals(tsSimpleCode.getCodeNo())) {
                    this.ifIdcAble = false;
                    archivesCard.setIdc(null);
                }
            }
    }
    /**
     *  <p>方法描述：根据身份证号自动填写出生日期、性别</p>
     * @MethodAuthor hsj
     */
    public void findFlowByIdc() {
        if(ifIdcAble) {
            if (StringUtils.isNotBlank(this.archivesCard.getIdc())) {
                String checkIDC = IdcUtils.checkIDC(this.archivesCard.getIdc());
                if (StringUtils.isBlank(checkIDC)) {
                    if (Integer.valueOf(archivesCard.getIdc().substring(
                            archivesCard.getIdc().length() - 2,
                            archivesCard.getIdc().length() - 1)) % 2 == 0) {
                        archivesCard.setSex(2);
                    } else {
                        archivesCard.setSex(1);
                    }// 性别
                    Date birthday = this.calBirthday(archivesCard.getIdc());
                    archivesCard.setBirthday(birthday);
                }

            }
        }
    }
    /**
     *  <p>方法描述：出生日期的计算</p>
     * @MethodAuthor hsj
     */
    public Date calBirthday(String idc){
        Date brithday = null;
        // 日期转换格式
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        try {
            // 18位身份证号
            if (idc.length() == 18) {
                brithday = df.parse(idc.substring(6, 14));
            } else {
                // 15位身份证号前加上"19"
                brithday = df.parse(new StringBuilder("19").append(idc.substring(6, 12)).toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return brithday;
    }
    /**
     *  <p>方法描述：选择鉴定职业病</p>
     * @MethodAuthor hsj
     */
    public void selectZybTypeAction() {
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("width", 680);
        options.put("contentWidth", 625);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add("职业病");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add("5026");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        paramList.add("false");
        paramMap.put("ifAllSelect", paramList);
        paramList = new ArrayList<String>();
        paramList.add("true");
        paramMap.put("ifShowFirstCode", paramList);
        paramList = new ArrayList<String>();
        paramList.add("1");
        paramMap.put("extends1", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeRadioSelectListNew2", options, paramMap);
    }

    /***
     *  <p>方法描述：选择职业病后</p>
     *
     * @MethodAuthor maox,2019年5月14日,onOccdisApplySearch
     * @param event
     */
    public void onZybTypeSelect(SelectEvent event){
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            //已选择的
            TsSimpleCode code = selectedMap.get("selectPro") == null ? null
                    : (TsSimpleCode) selectedMap.get("selectPro");
            if (null != code) {
                //查大类
                TsSimpleCode simpleCode = this.findTsSimpleCode(code);
                archivesCard.setFkByZybDisTypeId(simpleCode);
                archivesCard.setFkByZybTypeId(code);
                zybjdCode = code;
                //更新
                //其他
                if(null == zybjdCode.getExtendS2() || 1 != zybjdCode.getExtendS2()){
                    archivesCard.setZybDisName(null);
                }
                if(!"1".equals(zybjdCode.getExtendS4())){
                    //非尘肺病
                    archivesCard.setDiag1Date(null);
                    archivesCard.setDiag2Date(null);
                    archivesCard.setDiag3Date(null);
                    this.rptTypeId = null;
                    archivesCard.setFkByRptTypeId(new TsSimpleCode());
                }
                if(!"2".equals(zybjdCode.getExtendS4())){
                    //非化学中毒
                    archivesCard.setZyPoisonType(null);
                }
            }else{
                zybjdCode = new TsSimpleCode();
                JsfUtil.addErrorMessage("职业病鉴定信息请选择职业病");
            }

        }
    }
    /**
     *  <p>方法描述：病例类型选择</p>
     * @MethodAuthor hsj
     */
    public void onRptTypeId(){
        if(null !=rptTypeId){
            archivesCard.setFkByRptTypeId(tsSimpleMap.get(rptTypeId));
        }
    }
    /**
     *  <p>方法描述：病例类型选择</p>
     * @MethodAuthor hsj
     */
    public void onJdRptTypeId(){
        if(null !=jdRptTypeId){
            archivesCard.setFkByJdRptTypeId(tsSimpleMap.get(jdRptTypeId));
        }
    }
    /**
     *  <p>方法描述：查询当前小类的上一级</p>
     * @MethodAuthor hsj
     */
    private TsSimpleCode findTsSimpleCode(TsSimpleCode code) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(" select t from TsSimpleCode t where t.tsCodeType.codeTypeName ='5026'");
        if (StringUtils.isNotBlank(code.getCodeLevelNo()) && code.getCodeLevelNo().split("\\.").length > 1) {
            buffer.append(" and t.codeNo = '").append(code.getCodeLevelNo().split("\\.")[code.getCodeLevelNo().split("\\.").length - 2]).append("'");
        }else {
            buffer.append(" and t.codeNo = '").append(code.getCodeNo()).append("'");
        }
        return commService.findOneByHql(buffer.toString(), TsSimpleCode.class);
    }

    /**
     *  <p>方法描述：鉴定结果选择职业病后</p>
     * @MethodAuthor hsj
     */
    public void onJdZybTypeSelect(SelectEvent event){
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            //已选择的
            TsSimpleCode code = selectedMap.get("selectPro") == null ? null
                    : (TsSimpleCode) selectedMap.get("selectPro");
            if (null != code) {
                //查大类
                TsSimpleCode simpleCode = this.findTsSimpleCode(code);
                archivesCard.setFkByJdZybDisTypeId(simpleCode);
                archivesCard.setFkByJdZybTypeId(code);
                zybjgCode = code;
                //更新
                //其他
                if(null == zybjgCode.getExtendS2() || 1 != zybjgCode.getExtendS2()){
                    archivesCard.setJdZybDisName(null);
                }
                if(!"1".equals(zybjgCode.getExtendS4())){
                    //非尘肺病
                    archivesCard.setJdDiag1Date(null);
                    archivesCard.setJdDiag2Date(null);
                    archivesCard.setJdDiag3Date(null);
                    this.jdRptTypeId = null;
                    archivesCard.setFkByJdRptTypeId(new TsSimpleCode());
                }
                if(!"2".equals(zybjgCode.getExtendS4())){
                    //非化学中毒
                    archivesCard.setJdZyPoisonType(null);
                }
            }else{
                zybjgCode = new TsSimpleCode();
                JsfUtil.addErrorMessage("职业病鉴定结果请选择职业病");
            }

        }
    }
    /**
     *  <p>方法描述：是否为职业病的选择</p>
     * @MethodAuthor hsj
     */
    public void  onIfZybSelect(){
        //鉴定
        if(null != archivesCard.getIfZyb() && 1 == archivesCard.getIfZyb() ){
            //是
            zybjdCode = new TsSimpleCode();
            archivesCard.setFkByZybDisTypeId(new TsSimpleCode());
            archivesCard.setFkByZybTypeId(new TsSimpleCode());
            archivesCard.setZybDisName(null);
        }else{
            //否，清空病种信息
            zybjdCode = new TsSimpleCode();
            archivesCard.setFkByZybDisTypeId(new TsSimpleCode("0","无"));
            archivesCard.setFkByZybTypeId(new TsSimpleCode("0","无"));
            archivesCard.setZybDisName(null);
            archivesCard.setZybDisName(null);
            archivesCard.setDiag1Date(null);
            archivesCard.setDiag2Date(null);
            archivesCard.setDiag3Date(null);
            this.rptTypeId = null;
            archivesCard.setFkByRptTypeId(new TsSimpleCode());
            archivesCard.setZyPoisonType(null);
        }

    }
    /**
     *  <p>方法描述：是否鉴定为职业病选择</p>
     * @MethodAuthor hsj
     */
    public void  onIfJdZybSelect(){
        //鉴定
        if(null != archivesCard.getIfJdZyb() && 1 == archivesCard.getIfJdZyb() ){
            //是
            zybjgCode = new TsSimpleCode();
            archivesCard.setFkByJdZybDisTypeId(new TsSimpleCode());
            archivesCard.setFkByJdZybTypeId(new TsSimpleCode());
            archivesCard.setJdZybDisName(null);
        }else{
            //否，清空病种信息
            zybjgCode = new TsSimpleCode();
            archivesCard.setFkByJdZybDisTypeId(new TsSimpleCode("0","无"));
            archivesCard.setFkByJdZybTypeId(new TsSimpleCode("0","无"));
            archivesCard.setJdZybDisName(null);
            archivesCard.setJdZybDisName(null);
            archivesCard.setJdDiag1Date(null);
            archivesCard.setJdDiag2Date(null);
            archivesCard.setJdDiag3Date(null);
            this.jdRptTypeId = null;
            archivesCard.setFkByJdRptTypeId(new TsSimpleCode());
            archivesCard.setJdZyPoisonType(null);
        }
    }
    /**
     *  <p>方法描述：鉴定类型选择</p>
     * @MethodAuthor hsj
     */
    public void  changeJdType(){
        if(null != this.jdType){
            archivesCard.setFkByJdTypeId(tsSimpleMap.get(jdType));
            if(!"2".equals(archivesCard.getFkByJdTypeId().getExtendS1())){
                this.jdAgainRstId = null;
                archivesCard.setFkByJdAgainRstId(tsSimpleMap.get(new TsSimpleCode()));
            }
        }else {
            archivesCard.setFkByJdTypeId(new TsSimpleCode());
        }
        copyArchivesCard();
    }
    /**
     *  <p>方法描述：一致时</p>
     * @MethodAuthor hsj
     */
    private void copyArchivesCard() {
        if("1".equals(archivesCard.getFkByJdTypeId().getExtendS1()) && "1".equals(archivesCard.getFkByJdRstId().getExtendS1())){
            this.ifAgainRst = Boolean.FALSE;
        }else if("2".equals(archivesCard.getFkByJdTypeId().getExtendS1()) && "1".equals(archivesCard.getFkByJdRstId().getExtendS1()) &&  archivesCard.getFkByJdAgainRstId() != null && "1".equals(archivesCard.getFkByJdAgainRstId().getExtendS1())){
            this.ifAgainRst = Boolean.FALSE;
        }else {
            this.ifAgainRst = Boolean.TRUE;
        }
        RequestContext context = RequestContext.getCurrentInstance();
        context.update("tabView:editForm");
    }

    /**
     *  <p>方法描述：再次鉴定结论选择</p>
     * @MethodAuthor hsj
     */
    public void  changeJdAgainRstId(){
        if(null != this.jdAgainRstId){
            //再次鉴定赋值
            archivesCard.setFkByJdAgainRstId(tsSimpleMap.get(jdAgainRstId));
        }else {
            archivesCard.setFkByJdAgainRstId(tsSimpleMap.get(new TsSimpleCode()));
        }
        copyArchivesCard();
    }
    /**
     *  <p>方法描述：首次鉴定选择</p>
     * @MethodAuthor hsj
     */
    public void  changeJdRst(){
        if(null != this.jdRst){
            archivesCard.setFkByJdRstId(tsSimpleMap.get(jdRst));
            copyArchivesCard();
        }else {
            archivesCard.setFkByJdRstId(new TsSimpleCode());
        }

    }


    /**
     *  <p>方法描述：暂存</p>
     * @MethodAuthor hsj
     */
    @Override
    public void saveAction() {
        try {
            // 若已制作，不走逻辑
            if (StringUtils.isNotBlank(archivesCard.getAnnexPath())) {
                JsfUtil.addSuccessMessage("保存成功！");
                return;
            }
            //【职业鉴定结果】信息存储鉴定信息内相同信息点的值
            if(!this.ifAgainRst){
                this.saveJdArchivesCard();
            }
            //码表初始化
            if (veryData()) {
                return;
            }
            this.archivesCardService.saveArchivesCard(archivesCard,this.tdZwBgkLastSta);
            ZwArchivesCardCommUtils.initEntity(archivesCard);
            JsfUtil.addSuccessMessage("保存成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }


    }


    /**
     *  <p>方法描述：提交前验证</p>
     * @MethodAuthor hsj
     */
    public void beforeSubmit() {
        try{
            //码表初始化
            //为空验证
            if (veryNullData() || veryData()) {
                return;
            }
            // 若已制作，不可提交
            if (StringUtils.isBlank(archivesCard.getAnnexPath())) {
                JsfUtil.addErrorMessage("请先制作文书！");
                return;
            }
            RequestContext context = RequestContext.getCurrentInstance();
            context.execute("PF('ConfirmDialog').show()");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
     *  <p>方法描述：提交</p>
     * @MethodAuthor hsj
     */
    public void submitAction() {
        try {
            this.delBgkLastSta();
            //再次鉴定赋值
            if (null != archivesCard.getFkByJdAgainRstId() && null != archivesCard.getFkByJdAgainRstId().getRid())  {
                archivesCard.setFkByJdAgainRstId(tsSimpleMap.get(archivesCard.getFkByJdAgainRstId().getRid()));
            }
            this.archivesCardService.submitArchivesCard(archivesCard, this.tdZwBgkLastSta);
            ZwArchivesCardCommUtils.initEntity(archivesCard);
            JsfUtil.addSuccessMessage("提交成功！");
            this.backAction();
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }

    }
    /**
     *  <p>方法描述：处理最新流程状态</p>
     * @MethodAuthor hsj
     */
    private void delBgkLastSta() {
        //当前登陆者单位地区
        TsZone zone = Global.getUser().getTsUnit().getTsZone();
        if ("1".equals(zone.getIfCityDirect())) {//市直属
            this.tdZwBgkLastSta.setCityRcvDate(new Date());
            this.tdZwBgkLastSta.setState(3);
        } else if ("1".equals(zone.getIfProvDirect())){//省直属
            this.tdZwBgkLastSta.setProRcvDate(new Date());
            this.tdZwBgkLastSta.setState(5);
        }else {
            this.tdZwBgkLastSta.setCountyRcvDate(new Date());
            this.tdZwBgkLastSta.setState(1);
        }
    }

    public Integer getIsCityDirect() {
        return isCityDirect;
    }

    public void setIsCityDirect(Integer isCityDirect) {
        this.isCityDirect = isCityDirect;
    }

    public Integer getIfBirthEncrypt() {
        return ifBirthEncrypt;
    }

    public void setIfBirthEncrypt(Integer ifBirthEncrypt) {
        this.ifBirthEncrypt = ifBirthEncrypt;
    }
}
