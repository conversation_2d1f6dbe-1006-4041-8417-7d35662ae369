package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TdZwOcchethRptComm;
import com.chis.modules.heth.comm.service.ZwIntellReportCommServiceImpl;
import com.chis.modules.heth.comm.utils.QRCodeUtil;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.primefaces.context.RequestContext;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.TreeNode;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * @ClassName TdZwOcchethRptFindBean
 * @Description: 职业卫生技术服务档案查询
 * <AUTHOR>
 * @Date 2020-10-16
 * @Version V1.0
 **/
@ManagedBean(name = "tdZwOcchethRptCommFindBean")
@ViewScoped
public class TdZwOcchethRptCommFindBean extends FacesEditBean {
    private static final long serialVersionUID = 8439387263034336056L;
    /**
     * session对象
     */
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    /**
     * EJB 会话Bean
     */
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    private ZwIntellReportCommServiceImpl intellReportServiceImpl = SpringContextHolder.getBean(ZwIntellReportCommServiceImpl.class);

    /**
     * 机构申报Id
     */
    private Integer rid;

    private TdZwOcchethRptComm tdZwOcchethRpt;


    /* 地区集合地区集合 */
    private List<TsZone> zoneList;
    /* 查询条件：地区名称 */
    private String searchZoneName;
    /* 查询条件：地区编码 */
    private String searchZoneCode;
    /* 查询条件：地区级别 */
    private String searchZoneType;
    /* 查询条件：技术服务机构地区名称 */
    private String searchUnitZoneName;
    /* 查询条件：技术服务机构地区编码 */
    private String searchUnitZoneCode;
    /* 查询条件：技术服务机构地区级别 */
    private String searchUnitZoneType;
    /* 查询条件：单位名称 */
    private String orgName;
    /**
     * 查询条件：检查机构
     */
    private String searchUnitName;
    /**
     * 查询条件：日期
     */
    private Date rptDateSearchStart;
    private Date rptDateSearchEnd;

    private String manageNo;
    /*** 技术服务类别树*/
    private TreeNode jsfwlbTree;
    /**
     * 技术服务类别名称
     */
    private String jsfwTreeName;
    /**
     * 技术服务类别编码
     */
    private String jsfwTreeCode;
    /**
     * 查询条件：已选择的技术服务类别树
     */
    private TreeNode[] searchSelJsfw;

    /**
     * 导出文件
     */
    private DefaultStreamedContent downloadFile;

    /**
     * 用于“技术服务类别”，当选择“职业健康检查总结报告”时 即extend1为1 时候 加载"体检开展方式"
     */
    private List<Integer> tmpExtend1TypeList;
    /**
     * 用于“技术服务类别”，extend1 为2的时候 加载职业病危害风险分类"
     */
    private List<Integer> zybRiskExtend1TypeList;
    private String typeListStr;//详情界面 体检开展方式 文本
    /**
     * 详情里的职业病危害风险分类名称
     */
    private String zybRiskStr;
    /**
     * 技术服务类别id
     */
    private Integer sortId;

    /**
     * 是否申报界面
     */
    private boolean resetFlag = false;
    private String tmpArea;//临时存储所属地区
    /**
     * 详情页是否显示二维码
     */
    private String ifShowQRCode;
    /**
     * 二维码显示内容
     */
    private String qrContent;
    /**
     * 生成二维码图片路径
     */
    private String qrPicPath;
    /**
     * 二维码图片名称
     */
    private String qrPicName;
    /**
     * 详情页标识 0：技术服务申报 1：技术服务档案查询
     */
    private String ifView;
    /**
     * 技术服务类别”，extend4 为2
     */
    private Integer ifExtent4Eq2;
    /**
     * 查询条件-状态
     */
    private List<SelectItem> stateList;
    /**
     * 选中的状态
     */
    private String[] states;

    public TdZwOcchethRptCommFindBean() {
        this.ifSQL = true;
        ifShowQRCode = PropertyUtils.getValueWithoutException("ifShowQRCode");
        if (StringUtils.isNotBlank(ifShowQRCode) && "1".equals(ifShowQRCode)) {
            ifShowQRCode = "1";
        } else {
            ifShowQRCode = "0";
        }
        init();
        this.searchAction();
    }

    /**
     * @MethodName: init
     * @Description: 页面初始化
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-10-16
     **/
    private void init() {
        //初始化状态
        stateList = new ArrayList<>();
        stateList.add(new SelectItem("0", "待提交"));
        stateList.add(new SelectItem("1", "已提交"));
        states = new String[]{"1"};
        //用人单位地区初始化
        initZone();
        rptDateSearchEnd = DateUtils.getDateOnly(new Date());
        rptDateSearchStart = DateUtils.getYearFirstDay(rptDateSearchEnd);
        initJsfwLB();
    }

    private void initZone() {
        TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
        }
        this.searchZoneCode = tsZone.getZoneCode();
        this.searchZoneName = tsZone.getZoneName();
        this.zoneList = this.commService.findZoneList(null, tsZone.getZoneCode(), null, null);

    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {
        if (null != rid) {
            String hql = "from TdZwOcchethRptComm t where t.rid ='" + rid.intValue() + "'";
            List<TdZwOcchethRptComm> list = this.commService.findData(hql, null);
            tdZwOcchethRpt = list.get(0);
            sortId = tdZwOcchethRpt.getFkBySortId().getRid();
            if (tdZwOcchethRpt.getFkBySortId() != null && StringUtils.isNotBlank(tdZwOcchethRpt.getFkBySortId().getExtendS4()) && "2".equals(tdZwOcchethRpt.getFkBySortId().getExtendS4())) {
                this.ifExtent4Eq2 = 1;
            } else {
                this.ifExtent4Eq2 = 0;
            }
            if (null != tdZwOcchethRpt.getFkByZoneId()) {
                tmpArea = tdZwOcchethRpt.getFkByZoneId().getFullName();
            }
            if (StringUtils.isNotEmpty(tmpArea)) {
                tmpArea = tmpArea.substring(tmpArea.indexOf("_") + 1);
            }
            this.typeListStr = null;
            if (null != tdZwOcchethRpt.getChkWayType()) {
                StringBuilder builder = new StringBuilder();
                if (3 == tdZwOcchethRpt.getChkWayType()) {
                    builder.append("院内体检").append("，").append("外出体检");
                } else if (1 == tdZwOcchethRpt.getChkWayType()) {
                    builder.append("院内体检");
                } else if (2 == tdZwOcchethRpt.getChkWayType()) {
                    builder.append("外出体检");
                }
                typeListStr = builder.toString();
            }
            this.zybRiskStr = null;
            Integer zybRiskId = tdZwOcchethRpt.getZybRiskId();
            if (null != zybRiskId) {
                TsSimpleCode t = this.commService.find(TsSimpleCode.class, zybRiskId);
                this.zybRiskStr = t.getCodeName();
            }
            this.sortId = tdZwOcchethRpt.getFkBySortId().getRid();

            String linkPhone = tdZwOcchethRpt.getLinkPhone();
            String encryptPhone = StringUtils.encryptPhone(linkPhone);
            tdZwOcchethRpt.setLinkPhone(encryptPhone);
        }
    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    /**
     * 构建树
     *
     * @param levelNo    层级编码
     * @param levelNoSet 二级以及以上的层级编码集合
     * @param allMap     所有数据级map
     * @param parentNode 上级树节点
     */
    private void addChildNode(String levelNo, Set<String> levelNoSet, Map allMap, TreeNode parentNode) {
        int level = StringUtils.countMatches(levelNo, ".");
        for (String ln : levelNoSet) {
            if (StringUtils.countMatches(ln, ".") == (level + 1) && StringUtils.startsWith(ln, levelNo + ".")) {
                TreeNode node = new CheckboxTreeNode(allMap.get(ln), parentNode);
                this.addChildNode(ln, levelNoSet, allMap, node);
            }
        }
    }

    /**
     * 清空已经选择的技术服务类别
     */
    public void clearJsfwlb() {
        initJsfwLB();//初始化 是否有必要
        List<TreeNode> children = this.jsfwlbTree.getChildren();
        //第一层级
        if (null != children && children.size() > 0) {
            for (TreeNode node : children) {
                node.setSelected(false);
                node.setPartialSelected(false);
                List<TreeNode> children2 = node.getChildren();
                //第二层级
                if (null != children2 && children2.size() > 0) {
                    for (TreeNode node2 : children2) {
                        node2.setSelected(false);
                        node.setPartialSelected(false);
                    }
                }
            }
        }
        searchSelJsfw = new TreeNode[]{};//可以试试这个
        this.jsfwTreeName = null;
        this.jsfwTreeCode = null;
    }

    public void selectJsfwlbAction() {
        // 遍历选择的技术服务类别，获取选择的id与名称
        if (this.searchSelJsfw != null && searchSelJsfw.length > 0) {

            StringBuilder nameSb = new StringBuilder(); // 分类名称
            StringBuilder ridSb = new StringBuilder(); // 分类Id
            for (TreeNode treeNode : searchSelJsfw) {
                TsSimpleCode simpleCode = (TsSimpleCode) treeNode.getData();
                nameSb.append(",").append(String.valueOf(simpleCode.getCodeName()));
                ridSb.append(",").append(simpleCode.getRid());
            }
            if (nameSb.toString().length() > 0) {
                this.jsfwTreeName = nameSb.toString().substring(1);
                this.jsfwTreeCode = ridSb.toString().substring(1);
                return;
            }
        }
        this.jsfwTreeName = null;
        this.jsfwTreeCode = null;
    }

    private void initJsfwLB() {
        this.jsfwlbTree = new CheckboxTreeNode("root", null);
        tmpExtend1TypeList = new ArrayList<>();
        zybRiskExtend1TypeList = new ArrayList<>();
        List<TsSimpleCode> dataBySqlNoPage = commService.findScAllOrNoByTypeId("5506", true);
        if (dataBySqlNoPage != null && dataBySqlNoPage.size() > 0) {
            // 只有第一层
            Set<String> firstLevelNoSet = new LinkedHashSet<>();
            // 没有第一层
            Set<String> levelNoSet = new LinkedHashSet<>();
            // 所有类别
            Map<String, TsSimpleCode> menuMap = new HashMap<>();
            for (TsSimpleCode objects : dataBySqlNoPage) {
                if (StringUtils.isNotBlank(objects.getExtendS1())) {
                    if (!tmpExtend1TypeList.contains(objects.getRid()) &&
                            objects.getExtendS1().trim().equals("1")) {
                        tmpExtend1TypeList.add(objects.getRid());
                    }
                    if (!zybRiskExtend1TypeList.contains(objects.getRid()) &&
                            objects.getExtendS1().trim().equals("2")) {
                        zybRiskExtend1TypeList.add(objects.getRid());
                    }
                }
                if (objects.getRid() != null && objects.getCodeName() != null) {
                    menuMap.put(objects.getRid() + "", objects);
                    /**
                     * 若层级编码不为空，并且不包含“.”,放在第一层 firstLevelNoSet存储所有层级编码
                     */
                    if (StringUtils.containsNone(objects.getRid() + "", ".")) {
                        firstLevelNoSet.add(objects.getRid() + "");
                    } else {
                        /**
                         * 层级编码存在“.”
                         */
                        levelNoSet.add(objects.getRid() + "");
                    }
                }
                for (String ln : firstLevelNoSet) {
                    TsSimpleCode t = menuMap.get(ln);
                    if (null != t) {
                        TreeNode node = new CheckboxTreeNode(t, this.jsfwlbTree);
                        this.addChildNode(ln, levelNoSet, menuMap, node);
                    }
                }

                menuMap.clear();
            }
        }
    }

    @Override
    public void searchAction() {
        boolean isFlag = false;
        //两个地区必须选择一个
        if ((null == searchZoneCode || "".equals(searchZoneCode)) && (null == searchUnitZoneCode || "".equals(searchUnitZoneCode))) {
            JsfUtil.addErrorMessage("请选择用人单位地区或者技术服务机构地区！");
            isFlag = true;
        }
        if (null != rptDateSearchEnd && null != rptDateSearchStart && DateUtils.isDateAfter(rptDateSearchStart, rptDateSearchEnd)) {
            JsfUtil.addErrorMessage("结束日期需要大于等于开始日期！");
            isFlag = true;
        }
        if (isFlag) {
            return;
        }
        super.searchAction();
    }

    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();
        sql.append(" from TD_ZW_OCCHETH_RPT t1 ");
        sql.append(" left join ts_zone t2 on t1.zone_id =t2.rid");
        sql.append(" left join ts_simple_code t4 on t1.SORT_ID = t4.rid ");
        sql.append(" left join TS_UNIT t5 on t1.UNIT_ID = t5.rid ");
        sql.append(" LEFT JOIN ts_zone t3 ON t3.rid = t5.ZONE_ID ");

        sql.append(" where 1= 1 and nvl(t1.DEL_MARK,0) =0 ");

        if (StringUtils.isNotBlank(searchZoneCode)) {
            sql.append(" and t2.zone_gb like :searchZoneCode");
            this.paramMap.put("searchZoneCode", ZoneUtil.zoneSelect(searchZoneCode) + "%");
        }
        if (StringUtils.isNotBlank(searchUnitZoneCode)) {
            sql.append(" and t3.zone_gb like :searchUnitZoneCode");
            this.paramMap.put("searchUnitZoneCode", ZoneUtil.zoneSelect(searchUnitZoneCode) + "%");
        }
        if (StringUtils.isNotBlank(orgName)) {
            sql.append(" and t1.CRPT_NAME like :orgName");
            this.paramMap.put("orgName", "%" + orgName + "%");
        }

        if (null != rptDateSearchStart) {
            sql.append(" and T1.RPT_DATE >= to_date(:rptDateSearchStart,'YYYY-MM-DD HH24:MI:SS') ");
            this.paramMap.put("rptDateSearchStart", DateUtils.formatDate(this.rptDateSearchStart) + " 00:00:00");
        }
        if (null != rptDateSearchEnd) {
            sql.append(" and  T1.RPT_DATE <= to_date(:rptDateSearchEnd,'YYYY-MM-DD HH24:MI:SS') ");
            this.paramMap.put("rptDateSearchEnd", DateUtils.formatDate(this.rptDateSearchEnd) + " 23:59:59");
        }
        if (StringUtils.isNotBlank(this.searchUnitName)) {
            sql.append(" and t5.UNITNAME like :searchUnitName escape '\\\'");
            this.paramMap.put("searchUnitName", "%" + StringUtils.convertBFH(this.searchUnitName.trim()) + "%");
        }

        if (StringUtils.isNotEmpty(jsfwTreeCode)) {
            sql.append(" and T4.rid in (").append(jsfwTreeCode).append(") ");
        }

        if (StringUtils.isNotBlank(manageNo)) {
            sql.append(" and t1.MANAGE_NO like :manageNo");
            this.paramMap.put("manageNo", "%" + manageNo + "%");
        }
        if (null != this.states && this.states.length > 0) {
            sql.append(" AND t1.state in (").append(StringUtils.join(this.states, ",")).append(")");
        }
        sql.append(" order by t1.MANAGE_NO desc");

        StringBuffer searchSql = new StringBuffer();
        searchSql.append("select  t1.rid,t1.MANAGE_NO,t1.CRPT_NAME,CASE WHEN T2.ZONE_TYPE >2 THEN ");
        searchSql.append(" SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME,'_')+1) ELSE T2.FULL_NAME END ZONE_NAME,t1.LINK_MAN,t1.LINK_PHONE,t5.unitname,t1.RPT_DATE,t4.code_name,t1.FILE_PATH,t1.state ").append(sql);

        StringBuffer countSql = new StringBuffer();
        countSql.append(" select count(*)").append(sql);
        return new String[]{searchSql.toString(), countSql.toString()};
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneType() {
        return searchZoneType;
    }

    public void setSearchZoneType(String searchZoneType) {
        this.searchZoneType = searchZoneType;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public Date getRptDateSearchStart() {
        return rptDateSearchStart;
    }

    public void setRptDateSearchStart(Date rptDateSearchStart) {
        this.rptDateSearchStart = rptDateSearchStart;
    }

    public Date getRptDateSearchEnd() {
        return rptDateSearchEnd;
    }

    public void setRptDateSearchEnd(Date rptDateSearchEnd) {
        this.rptDateSearchEnd = rptDateSearchEnd;
    }

    public TreeNode getJsfwlbTree() {
        return jsfwlbTree;
    }

    public void setJsfwlbTree(TreeNode jsfwlbTree) {
        this.jsfwlbTree = jsfwlbTree;
    }

    public String getJsfwTreeName() {
        return jsfwTreeName;
    }

    public void setJsfwTreeName(String jsfwTreeName) {
        this.jsfwTreeName = jsfwTreeName;
    }

    public String getJsfwTreeCode() {
        return jsfwTreeCode;
    }

    public void setJsfwTreeCode(String jsfwTreeCode) {
        this.jsfwTreeCode = jsfwTreeCode;
    }

    public TreeNode[] getSearchSelJsfw() {
        return searchSelJsfw;
    }

    public void setSearchSelJsfw(TreeNode[] searchSelJsfw) {
        this.searchSelJsfw = searchSelJsfw;
    }

    public String getManageNo() {
        return manageNo;
    }

    public void setManageNo(String manageNo) {
        this.manageNo = manageNo;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }


    public TdZwOcchethRptComm getTdZwOcchethRpt() {
        return tdZwOcchethRpt;
    }

    public void setTdZwOcchethRpt(TdZwOcchethRptComm tdZwOcchethRpt) {
        this.tdZwOcchethRpt = tdZwOcchethRpt;
    }

    /**
     * <p>方法描述：下载前验证</p>
     *
     * @MethodAuthor hsj
     */
    public void downloadFileCheck() {
        boolean isFlag = false;
        //两个地区必须选择一个
        if ((null == searchZoneCode || "".equals(searchZoneCode)) && (null == searchUnitZoneCode || "".equals(searchUnitZoneCode))) {
            JsfUtil.addErrorMessage("请选择用人单位地区或者技术服务机构地区！");
            isFlag = true;
        }
        if (null != rptDateSearchEnd && null != rptDateSearchStart && DateUtils.isDateAfter(rptDateSearchStart, rptDateSearchEnd)) {
            JsfUtil.addErrorMessage("结束日期需要大于等于开始日期！");
            isFlag = true;
        }
        if (isFlag) {
            return;
        } else {
            RequestContext.getCurrentInstance().execute("getDownloadFileClick()");
        }
    }

    public DefaultStreamedContent getDownloadFile() {
        OutputStream ouputStream = null;
        ByteArrayOutputStream baos = null;
        try {
            XSSFWorkbook wBook = new XSSFWorkbook();

            //sheet
            XSSFSheet sheet1 = wBook.createSheet("技术服务档案");// 工作表对象

            XSSFCellStyle titleStyle = ExportUtils.setTitleStyle(wBook);
            //标题
            XSSFRow titleRow = sheet1.createRow(0);
            XSSFCell titleCell = titleRow.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue("技术服务档案");

            CellRangeAddress region = new CellRangeAddress(0, 0, 0, 16);
            sheet1.addMergedRegion(region);

            //表头
            XSSFCellStyle headStyle = ExportUtils.setHeadStyle(wBook);

            XSSFRow titleRow2 = sheet1.createRow(1);
            titleRow2.setHeightInPoints(30);
            XSSFCell titleCell60 = titleRow2.createCell(0);
            titleCell60.setCellStyle(headStyle);
            titleCell60.setCellValue("质控编号");
            sheet1.setColumnWidth(0, 8500);
            CellRangeAddress region60 = new CellRangeAddress(1, 1, 0, 0);
            sheet1.addMergedRegion(region60);

            XSSFCell titleCell01 = titleRow2.createCell(1);
            titleCell01.setCellStyle(headStyle);
            titleCell01.setCellValue("单位名称");
            sheet1.setColumnWidth(1, 7000);
            CellRangeAddress region01 = new CellRangeAddress(1, 1, 1, 1);
            sheet1.addMergedRegion(region01);

            XSSFCell titleCell02 = titleRow2.createCell(2);
            titleCell02.setCellStyle(headStyle);
            titleCell02.setCellValue("用人单位地区");
            sheet1.setColumnWidth(2, 8500);
            CellRangeAddress region02 = new CellRangeAddress(1, 1, 2, 2);
            sheet1.addMergedRegion(region02);

            XSSFCell titleCell03 = titleRow2.createCell(3);
            titleCell03.setCellStyle(headStyle);
            titleCell03.setCellValue("社会信用代码");
            sheet1.setColumnWidth(3, 5000);
            CellRangeAddress region03 = new CellRangeAddress(1, 1, 3, 3);
            sheet1.addMergedRegion(region03);

            XSSFCell titleCell04 = titleRow2.createCell(4);
            titleCell04.setCellStyle(headStyle);
            titleCell04.setCellValue("单位地址");
            sheet1.setColumnWidth(4, 8000);
            CellRangeAddress region04 = new CellRangeAddress(1, 1, 4, 4);
            sheet1.addMergedRegion(region04);

            XSSFCell titleCell05 = titleRow2.createCell(5);
            titleCell05.setCellStyle(headStyle);
            titleCell05.setCellValue("行业类别");
            sheet1.setColumnWidth(5, 5500);
            CellRangeAddress region05 = new CellRangeAddress(1, 1, 5, 5);
            sheet1.addMergedRegion(region05);

            XSSFCell titleCell06 = titleRow2.createCell(6);
            titleCell06.setCellStyle(headStyle);
            titleCell06.setCellValue("经济性质");
            sheet1.setColumnWidth(6, 5500);
            CellRangeAddress region06 = new CellRangeAddress(1, 1, 6, 6);
            sheet1.addMergedRegion(region06);

            XSSFCell titleCell061 = titleRow2.createCell(7);
            titleCell061.setCellStyle(headStyle);
            titleCell061.setCellValue("企业规模");
            sheet1.setColumnWidth(7, 3000);
            CellRangeAddress region061 = new CellRangeAddress(1, 1, 7, 7);
            sheet1.addMergedRegion(region061);

            XSSFCell titleCell07 = titleRow2.createCell(8);
            titleCell07.setCellStyle(headStyle);
            titleCell07.setCellValue("联系人");
            sheet1.setColumnWidth(8, 3000);
            CellRangeAddress region07 = new CellRangeAddress(1, 1, 8, 8);
            sheet1.addMergedRegion(region07);

            XSSFCell titleCell08 = titleRow2.createCell(9);
            titleCell08.setCellStyle(headStyle);
            titleCell08.setCellValue("联系电话");
            sheet1.setColumnWidth(9, 4000);
            CellRangeAddress region08 = new CellRangeAddress(1, 1, 9, 9);
            sheet1.addMergedRegion(region08);


            XSSFCell titleCell09 = titleRow2.createCell(10);
            titleCell09.setCellStyle(headStyle);
            titleCell09.setCellValue("技术服务机构");
            sheet1.setColumnWidth(10, 7000);
            CellRangeAddress region09 = new CellRangeAddress(1, 1, 10, 10);
            sheet1.addMergedRegion(region09);

            XSSFCell titleCell10 = titleRow2.createCell(11);
            titleCell10.setCellStyle(headStyle);
            titleCell10.setCellValue("技术服务类别");
            sheet1.setColumnWidth(11, 9000);
            CellRangeAddress region10 = new CellRangeAddress(1, 1, 11, 11);
            sheet1.addMergedRegion(region10);

            XSSFCell titleCell11 = titleRow2.createCell(12);
            titleCell11.setCellStyle(headStyle);
            titleCell11.setCellValue("报告日期");
            sheet1.setColumnWidth(12, 4000);
            CellRangeAddress region11 = new CellRangeAddress(1, 1, 12, 12);
            sheet1.addMergedRegion(region11);

            XSSFCell titleCell120 = titleRow2.createCell(13);
            titleCell120.setCellStyle(headStyle);
            titleCell120.setCellValue("项目名称");
            sheet1.setColumnWidth(13, 8000);
            CellRangeAddress region120 = new CellRangeAddress(1, 1, 13, 13);
            sheet1.addMergedRegion(region120);


            XSSFCell titleCell12 = titleRow2.createCell(14);
            titleCell12.setCellStyle(headStyle);
            titleCell12.setCellValue("报告编号");
            sheet1.setColumnWidth(14, 8000);
            CellRangeAddress region12 = new CellRangeAddress(1, 1, 14, 14);
            sheet1.addMergedRegion(region12);

            XSSFCell titleCell15 = titleRow2.createCell(15);
            titleCell15.setCellStyle(headStyle);
            titleCell15.setCellValue("职业病危害风险分类");
            sheet1.setColumnWidth(15, 5000);
            CellRangeAddress region15 = new CellRangeAddress(1, 1, 15, 15);
            sheet1.addMergedRegion(region15);

            XSSFCell titleCell16 = titleRow2.createCell(16);
            titleCell16.setCellStyle(headStyle);
            titleCell16.setCellValue("体检开展方式");
            sheet1.setColumnWidth(16, 5000);
            CellRangeAddress region16 = new CellRangeAddress(1, 1, 16, 16);
            sheet1.addMergedRegion(region16);


            XSSFCellStyle dataCenterStyle = ExportUtils.setDataCenterStyle(wBook);
            XSSFCellStyle dataLeftStyle = ExportUtils.setDataLeftStyle(wBook);
            //dataLeftStyle.setWrapText(true);
            List<Object[]> dataList = new ArrayList<>();
            dataList = intellReportServiceImpl.findExportOccHethList(searchZoneCode, orgName, rptDateSearchStart, rptDateSearchEnd, this.searchUnitName, jsfwTreeCode, manageNo, searchUnitZoneCode, states);
            if (null != dataList && dataList.size() > 0) {
                int j = 0;
                for (Object[] obj : dataList) {
                    XSSFRow titleRow5 = sheet1.createRow(2 + j);
                    titleRow5.setHeightInPoints(20);
                    for (int k = 0; k < obj.length; k++) {
                        XSSFCell titleCell50 = titleRow5.createCell(k);
                        if (k == 9 && null != obj[k]) {//联系电话加密
                            obj[k] = StringUtils.encryptPhone(obj[k].toString());
                        }
                        titleCell50.setCellValue(null == obj[k] ? "" : obj[k].toString());
                        if (k == 1 || k == 2 || k == 4 || k == 5 || k == 6 || k == 10 || k == 11 || k == 13 || k == 14) {
                            titleCell50.setCellStyle(dataLeftStyle);
                        } else {
                            titleCell50.setCellStyle(dataCenterStyle);
                        }
                    }
                    j++;
                }
            }
            // 设置边框线
            sheet1.autoSizeColumn(17, true);
            Integer totalRows = null != dataList && dataList.size() > 0 ? dataList.size() + 2 : 2;
            ExportUtils.setBorderLine(wBook, sheet1, totalRows, 17);
            String sheet = "技术服务档案.xlsx";
            String fileName = new String(sheet.getBytes("GBK"),
                    "ISO-8859-1");
            baos = new ByteArrayOutputStream();
            wBook.write(baos);
            baos.flush();
            byte[] aa = baos.toByteArray();
            return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
        } catch (IOException e) {
            JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
        } finally {
            if (ouputStream != null) {
                try {
                    ouputStream.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (baos != null) {
                try {
                    baos.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return null;


    }


    /**
     * <p>方法描述：二维码弹框</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-07-07
     **/
    public void openQrCodeDiag() {
        if (tdZwOcchethRpt == null) {
            return;
        }
        //拼接二维码显示内容
        this.qrContent = "服务单位：" + tdZwOcchethRpt.getCrptName() + "\n报告单位：" + tdZwOcchethRpt.getFkByUnitId().getUnitname();
        if (tdZwOcchethRpt.getFkBySortId() != null && tdZwOcchethRpt.getFkBySortId().getExtendS4() != null && "2".equals(tdZwOcchethRpt.getFkBySortId().getExtendS4())) {
            qrContent += "\n体检日期：" + (tdZwOcchethRpt.getBhkBeginDate() == null ? "" : DateUtils.formatDate(tdZwOcchethRpt.getBhkBeginDate(), "yyyy-MM-dd")) + (tdZwOcchethRpt.getBhkBeginDate() != null && tdZwOcchethRpt.getBhkEndDate() != null ? "~" : "") + (tdZwOcchethRpt.getBhkEndDate() == null ? "" : DateUtils.formatDate(tdZwOcchethRpt.getBhkEndDate(), "yyyy-MM-dd")) + "\n体检人数：" + (tdZwOcchethRpt.getBhkPsn() == null ? "" : tdZwOcchethRpt.getBhkPsn());
        }
        String uuid = java.util.UUID.randomUUID().toString().replaceAll("-", "");
        this.qrPicName = uuid + ".jpg";
        try {
            //判断文件夹路径是否存在
            QRCodeUtil.mkdirs(PropertyUtils.getValue("virtual.directory") + "temp/");
            qrPicPath = "temp/" + qrPicName;
            //生成二维码图片
            QRCodeUtil.encode(qrContent, PropertyUtils.getValue("virtual.directory") + qrPicPath);
            RequestContext.getCurrentInstance().execute("PF('QrCodeDialog').show();");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void setDownloadFile(DefaultStreamedContent downloadFile) {
        this.downloadFile = downloadFile;
    }

    public boolean isResetFlag() {
        return resetFlag;
    }

    public void setResetFlag(boolean resetFlag) {
        this.resetFlag = resetFlag;
    }

    public String getTmpArea() {
        return tmpArea;
    }

    public void setTmpArea(String tmpArea) {
        this.tmpArea = tmpArea;
    }

    public List<Integer> getTmpExtend1TypeList() {
        return tmpExtend1TypeList;
    }

    public void setTmpExtend1TypeList(List<Integer> tmpExtend1TypeList) {
        this.tmpExtend1TypeList = tmpExtend1TypeList;
    }

    public List<Integer> getZybRiskExtend1TypeList() {
        return zybRiskExtend1TypeList;
    }

    public void setZybRiskExtend1TypeList(List<Integer> zybRiskExtend1TypeList) {
        this.zybRiskExtend1TypeList = zybRiskExtend1TypeList;
    }

    public String getTypeListStr() {
        return typeListStr;
    }

    public void setTypeListStr(String typeListStr) {
        this.typeListStr = typeListStr;
    }

    public String getZybRiskStr() {
        return zybRiskStr;
    }

    public void setZybRiskStr(String zybRiskStr) {
        this.zybRiskStr = zybRiskStr;
    }

    public Integer getSortId() {
        return sortId;
    }

    public void setSortId(Integer sortId) {
        this.sortId = sortId;
    }

    public String getSearchUnitZoneName() {
        return searchUnitZoneName;
    }

    public void setSearchUnitZoneName(String searchUnitZoneName) {
        this.searchUnitZoneName = searchUnitZoneName;
    }

    public String getSearchUnitZoneCode() {
        return searchUnitZoneCode;
    }

    public void setSearchUnitZoneCode(String searchUnitZoneCode) {
        this.searchUnitZoneCode = searchUnitZoneCode;
    }

    public String getSearchUnitZoneType() {
        return searchUnitZoneType;
    }

    public void setSearchUnitZoneType(String searchUnitZoneType) {
        this.searchUnitZoneType = searchUnitZoneType;
    }

    public String getQrContent() {
        return qrContent;
    }

    public void setQrContent(String qrContent) {
        this.qrContent = qrContent;
    }

    public String getQrPicPath() {
        return qrPicPath;
    }

    public void setQrPicPath(String qrPicPath) {
        this.qrPicPath = qrPicPath;
    }

    public String getQrPicName() {
        return qrPicName;
    }

    public void setQrPicName(String qrPicName) {
        this.qrPicName = qrPicName;
    }

    public String getIfShowQRCode() {
        return ifShowQRCode;
    }

    public void setIfShowQRCode(String ifShowQRCode) {
        this.ifShowQRCode = ifShowQRCode;
    }

    public String getIfView() {
        return ifView;
    }

    public void setIfView(String ifView) {
        this.ifView = ifView;
    }

    public Integer getIfExtent4Eq2() {
        return ifExtent4Eq2;
    }

    public void setIfExtent4Eq2(Integer ifExtent4Eq2) {
        this.ifExtent4Eq2 = ifExtent4Eq2;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }
}
