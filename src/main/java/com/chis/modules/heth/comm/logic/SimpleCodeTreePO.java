package com.chis.modules.heth.comm.logic;

import com.chis.modules.system.entity.TsSimpleCode;

import java.io.Serializable;

/**
 * <p>类描述：用于编辑页平铺显示树形码表的对象
 * 参照放射卫生技术服务信息报送卡填报的技术服务领域
 * </p>
 * pw 2025/5/9
 **/
public class SimpleCodeTreePO implements Serializable {
    private static final long serialVersionUID = 5294519147221769793L;
    // 码表对象
    private TsSimpleCode simpleCode;
    // 是否有子节点
    private Boolean ifHasChild;
    // 是否选中
    private Boolean ifSelected;
    // 是否禁用 true禁用 false启用
    private Boolean ifDisabled;
    // 前置显示
    private String preTip;
    // 后置显示
    private String afterTip;
    // 大类码表对象
    private TsSimpleCode fatherSimple;

    public TsSimpleCode getSimpleCode() {
        return simpleCode;
    }

    public void setSimpleCode(TsSimpleCode simpleCode) {
        this.simpleCode = simpleCode;
    }

    public Boolean getIfHasChild() {
        return ifHasChild;
    }

    public void setIfHasChild(Boolean ifHasChild) {
        this.ifHasChild = ifHasChild;
    }

    public Boolean getIfSelected() {
        return ifSelected;
    }

    public void setIfSelected(Boolean ifSelected) {
        this.ifSelected = ifSelected;
    }

    public Boolean getIfDisabled() {
        return ifDisabled;
    }

    public void setIfDisabled(Boolean ifDisabled) {
        this.ifDisabled = ifDisabled;
    }

    public String getPreTip() {
        return preTip;
    }

    public void setPreTip(String preTip) {
        this.preTip = preTip;
    }

    public String getAfterTip() {
        return afterTip;
    }

    public void setAfterTip(String afterTip) {
        this.afterTip = afterTip;
    }

    public TsSimpleCode getFatherSimple() {
        return fatherSimple;
    }

    public void setFatherSimple(TsSimpleCode fatherSimple) {
        this.fatherSimple = fatherSimple;
    }
}
