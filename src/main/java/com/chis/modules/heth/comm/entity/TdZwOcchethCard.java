package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 职业卫生技术服务信息报送卡
 *
 * <AUTHOR>
 * @createTime 2022-8-25
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_CARD")
@SequenceGenerator(name = "TdZwOcchethCard", sequenceName = "TD_ZW_OCCHETH_CARD_SEQ", allocationSize = 1)
public class TdZwOcchethCard implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private String cardNo;
    private String orgId;
    private String orgName;
    private String orgFz;
    private String orgAddr;
    private String certNo;
    private String proFz;
    private String proLinktel;
    private TbTjCrpt fkByCrptId;
    private TsZone fkByZoneId;
    private String crptName;
    private String address;
    private String safeposition;
    private String safephone;
    private TsSimpleCode fkByCrptSizeId;
    private Date investStartDate;
    private Date investEndDate;
    private Date jcStartDate;
    private Date jcEndDate;
    private Date rptDate;
    private String rptNo;
    private Integer ifBadrsnJc;
    private Boolean hasBadrsnJc;
    private Integer ifStatusPj;
    private Boolean hasStatusPj;
    private Integer ifInstUsePj;
    private Boolean hasInstUsePj;
    private Integer ifJcInst;
    private Boolean hasJcInst;
    private Integer jcInstNum;
    private Integer jcNotHgInstNum;
    private String notHgInstName;
    private Integer ifJcUse;
    private Boolean hasJcUse;
    private Integer jcUseNum;
    private Integer jcNotHgUseNum;
    private String notHgUseName;
    private String orgFzMan;
    private String fillFormPsn;
    private String fillLink;
    private Date fillDate;
    private String fillUnitName;
    private String annexPath;
    private TsUnit fkByFillUnitId;
    private Integer state;
    private Integer delMark;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private String orgZoneGb;
    /**來源*/
    private Integer source;
    private Integer jcPostNum;
    private Integer jcOverNum;
    private Integer pjPostNum;
    private Integer pjOverNum;
    /**
     * 资质业务范围
     */
    private List<TdZwOcchethCardItems> occhethCardItemsList = new ArrayList<>();
    private String occhethCardItemsStr;
    /**
     * 参与人员
     */
    private List<TdZwOcchethCardPsn> occhethCardPsnList = new ArrayList<>();
    private List<String> occhethCardPsnRidList = new ArrayList<>();
    /**
     * 技术服务领域
     */
    private List<TdZwOcchethCardService> occhethCardServiceList = new ArrayList<>();
    private List<String> occhethCardServiceSimpleCodeList = new ArrayList<>();
    private String occhethCardServiceStr;
    /**
     * 服务地址
     */
    private List<TdZwOcchethCardZone> occhethCardZoneList = new ArrayList<>();
    /**
     * 职业病危害因素检测
     */
    private List<TdZwOcchethCardJc> occhethCardJcList = new ArrayList<>();

    /**
     * 职业病危害现状评价
     */
    private List<TdZwOcchethCardPj> occhethCardPjList = new ArrayList<>();
    private List<TdZwOcchethJcBadrsn> occhethJcBadrsns = new ArrayList<>();
    private List<TdZwOcchethPjBadrsn> occhethPjBadrsns = new ArrayList<>();

    /**签发页附件地址*/
    private String signAddress;
    /**社会信用代码*/
    private String creditCode;
    /**
     * 技术服务结果
     */
    private TsSimpleCode fkByServiceResultId;

    /**检测任务*/
    private TdYsjcChkContractComm fkByContractId;
    /**行业类别*/
    private TsSimpleCode fkByIndusTypeId;
    /**
     * 质控号
     */
    private String checkNo;
    /**
     * 超标危害因素类型-名称
     */
    private String selectedBadrsnNames;
    /**
     * 超标危害因素类型-id
     */
    private List<Integer> selectedBadrsnIds;
    /**标配版本-已选择的职业病危害因素*/
    private List<String> selBadrsnList = new ArrayList<>();
    private String selBadrsnListStr;

    /**标配版本-职业病危害因素*/
    private List<TdZwOcchethBpBadrsn> bpBadrsnList = new ArrayList<>();

    // 20250522 +二维码路径
    private String qrCodePath;


    public TdZwOcchethCard() {
    }

    public TdZwOcchethCard(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethCard")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Column(name = "CARD_NO")
    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    @Transient
    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    @Column(name = "ORG_NAME")
    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    @Column(name = "ORG_FZ")
    public String getOrgFz() {
        return orgFz;
    }

    public void setOrgFz(String orgFz) {
        this.orgFz = orgFz;
    }

    @Column(name = "ORG_ADDR")
    public String getOrgAddr() {
        return orgAddr;
    }

    public void setOrgAddr(String orgAddr) {
        this.orgAddr = orgAddr;
    }

    @Column(name = "CERT_NO")
    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    @Column(name = "PRO_FZ")
    public String getProFz() {
        return proFz;
    }

    public void setProFz(String proFz) {
        this.proFz = proFz;
    }

    @Column(name = "PRO_LINKTEL")
    public String getProLinktel() {
        return proLinktel;
    }

    public void setProLinktel(String proLinktel) {
        this.proLinktel = proLinktel;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_ID")
    public TbTjCrpt getFkByCrptId() {
        return fkByCrptId;
    }

    public void setFkByCrptId(TbTjCrpt fkByCrptId) {
        this.fkByCrptId = fkByCrptId;
    }

    @ManyToOne
    @JoinColumn(name = "ZONE_ID")
    public TsZone getFkByZoneId() {
        return fkByZoneId;
    }

    public void setFkByZoneId(TsZone fkByZoneId) {
        this.fkByZoneId = fkByZoneId;
    }

    @Column(name = "CRPT_NAME")
    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    @Column(name = "ADDRESS")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Column(name = "SAFEPOSITION")
    public String getSafeposition() {
        return safeposition;
    }

    public void setSafeposition(String safeposition) {
        this.safeposition = safeposition;
    }

    @Column(name = "SAFEPHONE")
    public String getSafephone() {
        return safephone;
    }

    public void setSafephone(String safephone) {
        this.safephone = safephone;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_SIZE_ID")
    public TsSimpleCode getFkByCrptSizeId() {
        return fkByCrptSizeId;
    }

    public void setFkByCrptSizeId(TsSimpleCode fkByCrptSizeId) {
        this.fkByCrptSizeId = fkByCrptSizeId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "INVEST_START_DATE")
    public Date getInvestStartDate() {
        return investStartDate;
    }

    public void setInvestStartDate(Date investStartDate) {
        this.investStartDate = investStartDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "INVEST_END_DATE")
    public Date getInvestEndDate() {
        return investEndDate;
    }

    public void setInvestEndDate(Date investEndDate) {
        this.investEndDate = investEndDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "JC_START_DATE")
    public Date getJcStartDate() {
        return jcStartDate;
    }

    public void setJcStartDate(Date jcStartDate) {
        this.jcStartDate = jcStartDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "JC_END_DATE")
    public Date getJcEndDate() {
        return jcEndDate;
    }

    public void setJcEndDate(Date jcEndDate) {
        this.jcEndDate = jcEndDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "RPT_DATE")
    public Date getRptDate() {
        return rptDate;
    }

    public void setRptDate(Date rptDate) {
        this.rptDate = rptDate;
    }

    @Column(name = "RPT_NO")
    public String getRptNo() {
        return rptNo;
    }

    public void setRptNo(String rptNo) {
        this.rptNo = rptNo;
    }

    @Column(name = "IF_BADRSN_JC")
    public Integer getIfBadrsnJc() {
        return ifBadrsnJc;
    }

    public void setIfBadrsnJc(Integer ifBadrsnJc) {
        this.ifBadrsnJc = ifBadrsnJc;
    }

    @Transient
    public Boolean getHasBadrsnJc() {
        return hasBadrsnJc;
    }

    public void setHasBadrsnJc(Boolean hasBadrsnJc) {
        this.hasBadrsnJc = hasBadrsnJc;
    }

    @Column(name = "IF_STATUS_PJ")
    public Integer getIfStatusPj() {
        return ifStatusPj;
    }

    public void setIfStatusPj(Integer ifStatusPj) {
        this.ifStatusPj = ifStatusPj;
    }

    @Transient
    public Boolean getHasStatusPj() {
        return hasStatusPj;
    }

    public void setHasStatusPj(Boolean hasStatusPj) {
        this.hasStatusPj = hasStatusPj;
    }

    @Column(name = "IF_INST_USE_PJ")
    public Integer getIfInstUsePj() {
        return ifInstUsePj;
    }

    public void setIfInstUsePj(Integer ifInstUsePj) {
        this.ifInstUsePj = ifInstUsePj;
    }

    @Transient
    public Boolean getHasInstUsePj() {
        return hasInstUsePj;
    }

    public void setHasInstUsePj(Boolean hasInstUsePj) {
        this.hasInstUsePj = hasInstUsePj;
    }

    @Column(name = "IF_JC_INST")
    public Integer getIfJcInst() {
        return ifJcInst;
    }

    public void setIfJcInst(Integer ifJcInst) {
        this.ifJcInst = ifJcInst;
    }

    @Column(name = "JC_INST_NUM")
    public Integer getJcInstNum() {
        return jcInstNum;
    }

    public void setJcInstNum(Integer jcInstNum) {
        this.jcInstNum = jcInstNum;
    }

    @Column(name = "JC_NOT_HG_INST_NUM")
    public Integer getJcNotHgInstNum() {
        return jcNotHgInstNum;
    }

    public void setJcNotHgInstNum(Integer jcNotHgInstNum) {
        this.jcNotHgInstNum = jcNotHgInstNum;
    }

    @Column(name = "NOT_HG_INST_NAME")
    public String getNotHgInstName() {
        return notHgInstName;
    }

    public void setNotHgInstName(String notHgInstName) {
        this.notHgInstName = notHgInstName;
    }

    @Column(name = "IF_JC_USE")
    public Integer getIfJcUse() {
        return ifJcUse;
    }

    public void setIfJcUse(Integer ifJcUse) {
        this.ifJcUse = ifJcUse;
    }

    @Column(name = "JC_USE_NUM")
    public Integer getJcUseNum() {
        return jcUseNum;
    }

    public void setJcUseNum(Integer jcUseNum) {
        this.jcUseNum = jcUseNum;
    }

    @Column(name = "JC_NOT_HG_USE_NUM")
    public Integer getJcNotHgUseNum() {
        return jcNotHgUseNum;
    }

    public void setJcNotHgUseNum(Integer jcNotHgUseNum) {
        this.jcNotHgUseNum = jcNotHgUseNum;
    }

    @Column(name = "NOT_HG_USE_NAME")
    public String getNotHgUseName() {
        return notHgUseName;
    }

    public void setNotHgUseName(String notHgUseName) {
        this.notHgUseName = notHgUseName;
    }

    @Column(name = "ORG_FZ_MAN")
    public String getOrgFzMan() {
        return orgFzMan;
    }

    public void setOrgFzMan(String orgFzMan) {
        this.orgFzMan = orgFzMan;
    }

    @Column(name = "FILL_FORM_PSN")
    public String getFillFormPsn() {
        return fillFormPsn;
    }

    public void setFillFormPsn(String fillFormPsn) {
        this.fillFormPsn = fillFormPsn;
    }

    @Column(name = "FILL_LINK")
    public String getFillLink() {
        return fillLink;
    }

    public void setFillLink(String fillLink) {
        this.fillLink = fillLink;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "FILL_DATE")
    public Date getFillDate() {
        return fillDate;
    }

    public void setFillDate(Date fillDate) {
        this.fillDate = fillDate;
    }

    @Column(name = "FILL_UNIT_NAME")
    public String getFillUnitName() {
        return fillUnitName;
    }

    public void setFillUnitName(String fillUnitName) {
        this.fillUnitName = fillUnitName;
    }

    @Column(name = "ANNEX_PATH")
    public String getAnnexPath() {
        return annexPath;
    }

    public void setAnnexPath(String annexPath) {
        this.annexPath = annexPath;
    }

    @ManyToOne
    @JoinColumn(name = "FILL_UNIT_ID")
    public TsUnit getFkByFillUnitId() {
        return fkByFillUnitId;
    }

    public void setFkByFillUnitId(TsUnit fkByFillUnitId) {
        this.fkByFillUnitId = fkByFillUnitId;
    }

    @Column(name = "STATE")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Column(name = "DEL_MARK")
    public Integer getDelMark() {
        return delMark;
    }

    public void setDelMark(Integer delMark) {
        this.delMark = delMark;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Transient
    public String getOrgZoneGb() {
        return orgZoneGb;
    }

    public void setOrgZoneGb(String orgZoneGb) {
        this.orgZoneGb = orgZoneGb;
    }

    @OrderBy("rid ASC")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwOcchethCardItems> getOcchethCardItemsList() {
        return occhethCardItemsList;
    }

    public void setOcchethCardItemsList(List<TdZwOcchethCardItems> occhethCardItemsList) {
        this.occhethCardItemsList = occhethCardItemsList;
    }

    @Transient
    public String getOcchethCardItemsStr() {
        return occhethCardItemsStr;
    }

    public void setOcchethCardItemsStr(String occhethCardItemsStr) {
        this.occhethCardItemsStr = occhethCardItemsStr;
    }

    @OrderBy("rid ASC")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwOcchethCardPsn> getOcchethCardPsnList() {
        return occhethCardPsnList;
    }

    public void setOcchethCardPsnList(List<TdZwOcchethCardPsn> occhethCardPsnList) {
        this.occhethCardPsnList = occhethCardPsnList;
    }

    @Transient
    public List<String> getOcchethCardPsnRidList() {
        return occhethCardPsnRidList;
    }

    public void setOcchethCardPsnRidList(List<String> occhethCardPsnRidList) {
        this.occhethCardPsnRidList = occhethCardPsnRidList;
    }

    @OrderBy("rid ASC")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwOcchethCardService> getOcchethCardServiceList() {
        return occhethCardServiceList;
    }

    public void setOcchethCardServiceList(List<TdZwOcchethCardService> occhethCardServiceList) {
        this.occhethCardServiceList = occhethCardServiceList;
    }

    @Transient
    public List<String> getOcchethCardServiceSimpleCodeList() {
        return occhethCardServiceSimpleCodeList;
    }

    public void setOcchethCardServiceSimpleCodeList(List<String> occhethCardServiceSimpleCodeList) {
        this.occhethCardServiceSimpleCodeList = occhethCardServiceSimpleCodeList;
    }

    @Transient
    public String getOcchethCardServiceStr() {
        return occhethCardServiceStr;
    }

    public void setOcchethCardServiceStr(String occhethCardServiceStr) {
        this.occhethCardServiceStr = occhethCardServiceStr;
    }

    @OrderBy("rid ASC")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwOcchethCardZone> getOcchethCardZoneList() {
        return occhethCardZoneList;
    }

    public void setOcchethCardZoneList(List<TdZwOcchethCardZone> occhethCardZoneList) {
        this.occhethCardZoneList = occhethCardZoneList;
    }

    @Transient
    public List<TdZwOcchethCardJc> getOcchethCardJcList() {
        return occhethCardJcList;
    }

    public void setOcchethCardJcList(List<TdZwOcchethCardJc> occhethCardJcList) {
        this.occhethCardJcList = occhethCardJcList;
    }

    @Transient
    public List<TdZwOcchethCardPj> getOcchethCardPjList() {
        return occhethCardPjList;
    }

    public void setOcchethCardPjList(List<TdZwOcchethCardPj> occhethCardPjList) {
        this.occhethCardPjList = occhethCardPjList;
    }
    @Transient
    public Boolean getHasJcInst() {
        return hasJcInst;
    }

    public void setHasJcInst(Boolean hasJcInst) {
        this.hasJcInst = hasJcInst;
    }
    @Transient
    public Boolean getHasJcUse() {
        return hasJcUse;
    }

    public void setHasJcUse(Boolean hasJcUse) {
        this.hasJcUse = hasJcUse;
    }

    @Column(name = "SOURCE")
    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    @Column(name = "JC_POST_NUM")
    public Integer getJcPostNum() {
        return jcPostNum;
    }

    public void setJcPostNum(Integer jcPostNum) {
        this.jcPostNum = jcPostNum;
    }

    @Column(name = "JC_OVER_NUM")
    public Integer getJcOverNum() {
        return jcOverNum;
    }

    public void setJcOverNum(Integer jcOverNum) {
        this.jcOverNum = jcOverNum;
    }

    @Column(name = "PJ_POST_NUM")
    public Integer getPjPostNum() {
        return pjPostNum;
    }

    public void setPjPostNum(Integer pjPostNum) {
        this.pjPostNum = pjPostNum;
    }

    @Column(name = "PJ_OVER_NUM")
    public Integer getPjOverNum() {
        return pjOverNum;
    }

    public void setPjOverNum(Integer pjOverNum) {
        this.pjOverNum = pjOverNum;
    }

    @Transient
    public List<TdZwOcchethJcBadrsn> getOcchethJcBadrsns() {
        return occhethJcBadrsns;
    }

    public void setOcchethJcBadrsns(List<TdZwOcchethJcBadrsn> occhethJcBadrsns) {
        this.occhethJcBadrsns = occhethJcBadrsns;
    }

    @Transient
    public List<TdZwOcchethPjBadrsn> getOcchethPjBadrsns() {
        return occhethPjBadrsns;
    }

    public void setOcchethPjBadrsns(List<TdZwOcchethPjBadrsn> occhethPjBadrsns) {
        this.occhethPjBadrsns = occhethPjBadrsns;
    }
    @Column(name = "SIGN_ADDRESS")
    public String getSignAddress() {
        return signAddress;
    }

    public void setSignAddress(String signAddress) {
        this.signAddress = signAddress;
    }

    @Column(name = "CREDIT_CODE")
    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }


    @ManyToOne
    @JoinColumn(name = "SERVICE_RESULT_ID")
    public TsSimpleCode getFkByServiceResultId() {
        return fkByServiceResultId;
    }

    public void setFkByServiceResultId(TsSimpleCode fkByServiceResultId) {
        this.fkByServiceResultId = fkByServiceResultId;
    }

    @ManyToOne
    @JoinColumn(name = "CONTRACT_ID")
    public TdYsjcChkContractComm getFkByContractId() {
        return fkByContractId;
    }

    public void setFkByContractId(TdYsjcChkContractComm fkByContractId) {
        this.fkByContractId = fkByContractId;
    }
    @ManyToOne
    @JoinColumn(name = "INDUS_TYPE_ID")
    public TsSimpleCode getFkByIndusTypeId() {
        return fkByIndusTypeId;
    }

    public void setFkByIndusTypeId(TsSimpleCode fkByIndusTypeId) {
        this.fkByIndusTypeId = fkByIndusTypeId;
    }

    @Column(name = "CHECK_NO")
    public String getCheckNo() {
        return checkNo;
    }

    public void setCheckNo(String checkNo) {
        this.checkNo = checkNo;
    }

    @Column(name = "QRCODE_PATH")
    public String getQrCodePath() {
        return qrCodePath;
    }

    public void setQrCodePath(String qrCodePath) {
        this.qrCodePath = qrCodePath;
    }

    @Transient
    public String getSelectedBadrsnNames() {
        return selectedBadrsnNames;
    }

    public void setSelectedBadrsnNames(String selectedBadrsnNames) {
        this.selectedBadrsnNames = selectedBadrsnNames;
    }

    @Transient
    public List<String> getSelBadrsnList() {
        return selBadrsnList;
    }

    public void setSelBadrsnList(List<String> selBadrsnList) {
        this.selBadrsnList = selBadrsnList;
    }

    @Transient
    public String getSelBadrsnListStr() {
        return selBadrsnListStr;
    }

    public void setSelBadrsnListStr(String selBadrsnListStr) {
        this.selBadrsnListStr = selBadrsnListStr;
    }

    @Transient
    public List<TdZwOcchethBpBadrsn> getBpBadrsnList() {
        return bpBadrsnList;
    }

    public void setBpBadrsnList(List<TdZwOcchethBpBadrsn> bpBadrsnList) {
        this.bpBadrsnList = bpBadrsnList;
    }


    @Transient
    public List<Integer> getSelectedBadrsnIds() {
        return selectedBadrsnIds;
    }

    public void setSelectedBadrsnIds(List<Integer> selectedBadrsnIds) {
        this.selectedBadrsnIds = selectedBadrsnIds;
    }
}