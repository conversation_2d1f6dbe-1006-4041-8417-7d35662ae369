package com.chis.modules.heth.comm.web;

import com.chis.common.bean.FastReportData;
import com.chis.common.bean.FastReportDataRef;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TdZwBgkLastSta;
import com.chis.modules.heth.comm.entity.TdZwjdArchivesCard;
import com.chis.modules.heth.comm.rptvo.ZwjdArchivesCardVo;
import com.chis.modules.heth.comm.service.TdZwHethAppraisalRptCardServiceImpl;
import com.chis.modules.heth.comm.service.TdZwHethChkSmaryServiceImpl;
import com.chis.modules.heth.comm.utils.ZwArchivesCardCommUtils;
import com.chis.modules.system.entity.TbZwWritsort;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IFastReport;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SysReturnPojo;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import com.chis.modules.system.web.FastReportBean;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.model.SelectItem;
import java.util.*;

/**
 * @Description: 职业病鉴定报告卡基类
 * 
 * @ClassAuthor pw,2021年11月8日,AbstractReportCardAppraisalListBean
 */
public class AbstractReportCardAppraisalListBean extends FacesEditBean implements IProcessData, IFastReport {

    protected CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    protected SystemModuleServiceImpl sysService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    protected TdZwHethAppraisalRptCardServiceImpl archivesCardService = SpringContextHolder.getBean(TdZwHethAppraisalRptCardServiceImpl.class);
    protected TdZwHethChkSmaryServiceImpl chkSmaryService = SpringContextHolder.getBean(TdZwHethChkSmaryServiceImpl.class);
    /**查询条件：用人单位地区集合*/
    protected List<TsZone> zoneList;
    /**地区*/
    protected TsZone tsZone;
    /**所在地区*/
    protected TsZone tsLocalZone;
    /**查询条件：用人单位地区名称*/
    protected String searchZoneName;
    /**查询条件：用人单位地区编码*/
    protected String searchZoneCode;
    /**查询条件：用人单位名称*/
    protected String searchUnitName;
    /**查询条件：用工单位名称*/
    protected String searchCrptName;
    /**查询条件：姓名*/
    protected String searchPsnName;
    /**查询条件：证件号码*/
    protected String searchIdc;
    /**查询条件：鉴定类型列表*/
    protected List<TsSimpleCode> jdTypeList;
    /**查询条件：选择的鉴定类型rid集合*/
    protected String[] searchJdTypes;
    /**查询条件：鉴定开始日期*/
    protected Date searchJdBdate;
    /**查询条件：鉴定结束日期*/
    protected Date searchJdEdate;
    /**查询条件：首次鉴定结论列表*/
    protected List<TsSimpleCode> firstJdResultList;
    protected String selectFirstJdResultNames;
    protected String selectFirstJdResultIds;
    /**查询条件：再次鉴定结论列表*/
    protected List<TsSimpleCode> nextJdResultList;
    protected String selectNextJdResulNames;
    protected String selectNextJdResulIds;
    /**查询条件：报告开始日期*/
    protected Date searchRptBdate;
    /**查询条件：报告结束日期*/
    protected Date searchRptEdate;
    /**查询条件：状态*/
    protected List<SelectItem> stateList;
    /**查询条件：选中状态*/
    protected String[] states;
    /**审核等级*/
    protected String checkLevel;
    /**编辑页面*/
    protected TdZwjdArchivesCard archivesCard = new TdZwjdArchivesCard();
    /**证件类型*/
    protected List<TsSimpleCode> cardTypeList;
    /**病种类型*/
    protected List<TsSimpleCode> rptTypeList;
    /**最新流程状态*/
    protected TdZwBgkLastSta tdZwBgkLastSta;
    /**单位类型*/
    protected String crpyType ;
    /**文书设计按钮显隐控制*/
    private boolean ifShowDesign = false;
    /**鉴定类型、首次鉴定结论、再次鉴定结论对应Map key 码表rid value 码表对象 */
    protected Map<Integer,TsSimpleCode> tsSimpleMap;
    /** 职业病鉴定报告卡rid */
    protected Integer rid;
    /**文书报表*/
    protected FastReportBean fastReportBean;
    /**证件*/
    protected boolean ifIdcAble = Boolean.TRUE;
    /**文书类型*/
    private TbZwWritsort writsort;

    /**职业病鉴定*/
    protected TsSimpleCode zybjdCode;
    /**职业病结果*/
    protected TsSimpleCode zybjgCode;

    /**证件类型*/
    protected Integer idcType;
    /**鉴定类型*/
    protected Integer jdType;
    /**首次鉴定结果*/
    protected Integer jdRst;
    /**病例类型*/
    protected Integer rptTypeId;
    /**病例类型*/
    protected Integer jdRptTypeId;
    /**是否编辑页*/
    protected Integer isView;
    /**再次鉴定结论*/
    protected Integer jdAgainRstId;
    /**职业病鉴定结果是否显示 默认显示*/
    protected Boolean ifAgainRst = Boolean.TRUE;


    public AbstractReportCardAppraisalListBean(){
        //文书设计按钮显隐控制
        String value = PropertyUtils.getValueWithoutException("rpt.ifDesign");
        if("1".equals(value)){
            ifShowDesign = true;
        }else{
            ifShowDesign = false;
        }
        //子类中需要初始化状态列表以及默认选中状态 审核的托管Bean中需要加入接收日期
        //初始化用人单位地区
        initZoneList();
        //初始化鉴定日期
        initJdDate();
        //初始化码表相关
        initTsSimpleCode();
        checkLevel = PropertyUtils.getValue("checkLevel");
        stateList = new ArrayList<>();
    }

    @Override
    public void addInit() {
    }


    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    @Override
    public String[] buildHqls() {
        return new String[0];
    }

    @Override
    public void processData(List<?> list) {

    }


    /**
     * @Description: 初始化用人单位地区
     *
     * @MethodAuthor pw,2021年11月8日
     */
    private void initZoneList(){
        tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        tsLocalZone=Global.getUser().getTsUnit().getTsZone();
        String zoneCode = tsZone.getZoneCode().substring(0,2)+"0000000000";
        /*** 地区初始化 */
        this.zoneList = this.sysService.findZoneListWithAllZoneByFlag(true,zoneCode);
    }


    /**
     * @Description: 初始化鉴定日期
     *
     * @MethodAuthor pw,2021年11月8日
     */
    private void initJdDate(){
        //当天-1年
        this.searchJdBdate = DateUtils.addYears(new Date(),-1);
        this.searchJdEdate = new Date();
    }

    /**
     * @Description: 初始化相关码表数据
     *
     * @MethodAuthor pw,2021年11月8日
     */
    private void initTsSimpleCode(){
        tsSimpleMap = new HashMap<>();
        //初始化鉴定类型
        this.jdTypeList = this.commService.findLevelSimpleCodesByTypeId("'5543'");
        if(!CollectionUtils.isEmpty(this.jdTypeList)){
            for(TsSimpleCode simpleCode : this.jdTypeList){
                tsSimpleMap.put(simpleCode.getRid(), simpleCode);
            }
        }
        //初始化首次鉴定结论
        this.firstJdResultList = this.commService.findLevelSimpleCodesByTypeId("'5544'");
        if(!CollectionUtils.isEmpty(this.firstJdResultList)){
            for(TsSimpleCode simpleCode : this.firstJdResultList){
                tsSimpleMap.put(simpleCode.getRid(), simpleCode);
            }
        }
        //初始化再次鉴定结论
        this.nextJdResultList = this.commService.findLevelSimpleCodesByTypeId("'5545'");
        if(!CollectionUtils.isEmpty(this.nextJdResultList)){
            for(TsSimpleCode simpleCode : this.nextJdResultList){
                tsSimpleMap.put(simpleCode.getRid(), simpleCode);
            }
        }
        //证件类型
        this.cardTypeList =  this.commService.findLevelSimpleCodesByTypeId("'5503'");
        if(!CollectionUtils.isEmpty(this.cardTypeList)){
            for(TsSimpleCode simpleCode : this.cardTypeList){
                tsSimpleMap.put(simpleCode.getRid(), simpleCode);
            }
        }
        //病种类型
        this.rptTypeList =  this.commService.findLevelSimpleCodesByTypeId("'5505'");
        if(!CollectionUtils.isEmpty(this.rptTypeList)){
            for(TsSimpleCode simpleCode : this.rptTypeList){
                tsSimpleMap.put(simpleCode.getRid(), simpleCode);
            }
        }
    }
    /**
     * <p>方法描述：制作文书</p>
     * @MethodAuthor rcj,2018年12月24日,tobuildWritReport
     * */
    public void tobuildWritReport(){
        //根据模板生成附件1、文书编号2、封装签章工具2、上传到本地
        this.fastReportBean = new FastReportBean(this, "HETH_2038");
        SysReturnPojo returnPojo = fastReportBean.showPDFReportAction("");
        String type = returnPojo.getType();
        String mess = returnPojo.getMess();
        if (!"00".equals(type)) {
            System.out.println("type："+type+"！！！！！mess："+mess);
            JsfUtil.addErrorMessage("生成文书失败，请稍后尝试！");
            return;
        }
        //附件地址
        archivesCard.setAnnexPath(mess);
        //保存已制作、文书附件
        ZwArchivesCardCommUtils.clearArchivesCardEntity(archivesCard);
        this.commService.upsertEntity(archivesCard);
        ZwArchivesCardCommUtils.initEntity(archivesCard);
        JsfUtil.addSuccessMessage("制作成功！");
        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.execute("disabledInput('true','archivesCardDiv')");
    }

    /**
     * <p>方法描述：文书制作</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-23
     **/
    public void buildWritReport() {
        //【职业鉴定结果】信息存储鉴定信息内相同信息点的值
        if(!this.ifAgainRst){
            this.saveJdArchivesCard();
        }
        // 提交验证  文书验证
        if(veryNullData() || !veryWritsort()||veryData()){
            return;
        }
        //调用保存方法
        this.archivesCardService.saveArchivesCard(archivesCard,this.tdZwBgkLastSta);
        ZwArchivesCardCommUtils.initEntity(archivesCard);
        showShadeTip();
        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update("tabView:editForm");
        currentInstance.execute("disabledInput('true','archivesCardDiv')");

    }
    /**
     * <p>方法描述：当前文书验证</p>
     * @MethodAuthor qrr,2018年4月26日,veryWritsort
     * */
    private boolean veryWritsort(){
        boolean flag = true;
        writsort = commService.getWritsort("2038");
        //文书未维护
        if (null==writsort) {
            JsfUtil.addErrorMessage("请在文书类型管理中维护该文书的相关信息！");
            return false;
        }
        if (null==writsort.getFkByRptTemplId()) {
            JsfUtil.addErrorMessage("请在文书类型管理中维护该文书的报表模板！");
            return false;
        }
        return flag;
    }

    /**
     * <p>方法描述：制作文书加载</p>
     * @MethodAuthor rcj,2019年9月5日,showShadeTip
     * */
    public void showShadeTip(){
        RequestContext.getCurrentInstance().execute("showShadeTip()");
    }

    /**
     *
     * <p>描述：设计文书</p>
     *
     *  @Author: 龚哲,2021/11/10 9:33,designWritReport
     */
    public void designWritReport() {
        if(!veryWritsort()){
            return;
        }
        this.fastReportBean = new FastReportBean(this, "HETH_2038");
        fastReportBean.designAction();
        RequestContext.getCurrentInstance().execute("frpt_design();");

    }
    /**
     *
     * <p>描述：封装职业病鉴定报告卡文书数据</p>
     *
     *  @Author: 龚哲,2021/11/10 9:33,packageDatas
     */
    private List<FastReportData> packageDatas() {
        List<FastReportData> rptDataList = new ArrayList<>();
        if (null == archivesCard.getRid()) {
            return rptDataList;
        }
        TdZwjdArchivesCard card = this.commService.find(TdZwjdArchivesCard.class, this.archivesCard.getRid());
        if (null == card) {
            return rptDataList;
        }
        List<ZwjdArchivesCardVo> cardVos = new ArrayList<>();
        ZwjdArchivesCardVo cardVo = new ZwjdArchivesCardVo();
        cardVo.setPersonnelName(card.getPersonnelName());
        cardVo.setCardTypeName(card.getFkByCardTypeId() == null ? "" : card.getFkByCardTypeId().getCodeName());
        cardVo.setIdc(card.getIdc());
        Integer sex = card.getSex();
        cardVo.setSex("");
        if (sex != null) {
            if (sex == 1) {
                cardVo.setSex("男");
            } else if (sex == 2) {
                cardVo.setSex("女");
            }
        }
        cardVo.setBirthday(card.getBirthday() == null ? "" : DateUtils.formatDate(card.getBirthday()));
        cardVo.setLinktel(card.getLinktel());
        cardVo.setCrptName(card.getCrptName());
        cardVo.setCreditCode(card.getCreditCode());
        cardVo.setEconomyName(card.getFkByEconomyId() == null ? "" : card.getFkByEconomyId().getCodeName());
        cardVo.setIndusTypeName(card.getFkByIndusTypeId() == null ? "" : card.getFkByIndusTypeId().getCodeName());
        cardVo.setCrptSizeName(card.getFkByCrptSizeId() == null ? "" : card.getFkByCrptSizeId().getCodeName());
        cardVo.setZoneName(StringUtils.objectToString(card.getFkByZoneId().getFullName()).replaceAll("_", ""));
        cardVo.setAddress(card.getAddress());
        cardVo.setPostcode(card.getPostcode());
        cardVo.setSafeposition(card.getSafeposition());
        cardVo.setSafephone(card.getSafephone());
        cardVo.setEmpCrptName(card.getEmpCrptName());
        cardVo.setEmpZoneName(StringUtils.objectToString(card.getFkByEmpZoneId().getFullName()).replaceAll("_", ""));
        cardVo.setEmpCreditCode(card.getEmpCreditCode());
        cardVo.setEmpEconomyName(card.getFkByEmpEconomyId() == null ? "" : card.getFkByEmpEconomyId().getCodeName());
        cardVo.setEmpIndusTypeName(card.getFkByEmpIndusTypeId() == null ? "" : card.getFkByEmpIndusTypeId().getCodeName());
        cardVo.setEmpCrptSizeName(card.getFkByEmpCrptSizeId() == null ? "" : card.getFkByEmpCrptSizeId().getCodeName());
        Integer ifZyb = card.getIfZyb();
        cardVo.setIfZyb("");
        if (ifZyb != null) {
            if (ifZyb == 0) {
                cardVo.setIfZyb("否");
                cardVo.setZybTypeName("无");
                cardVo.setZybDisName("");
                cardVo.setZybDisTypeName("无");
            } else if (ifZyb == 1) {
                cardVo.setIfZyb("是");
                cardVo.setZybTypeName(card.getFkByZybTypeId() == null ? "" : card.getFkByZybTypeId().getCodeName());
                cardVo.setZybDisName(StringUtils.isBlank(card.getZybDisName())?"":"（"+card.getZybDisName()+"）");
                cardVo.setZybDisTypeName(card.getFkByZybDisTypeId() == null ? "" : card.getFkByZybDisTypeId().getCodeName());
            }
        }
        cardVo.setApplyDate(card.getApplyDate() == null ? "" : DateUtils.formatDate(card.getApplyDate()));
        TsSimpleCode zydTypeCode = card.getFkByZybTypeId();
        cardVo.setIszdcfb("");
        if (zydTypeCode != null && "1".equals(zydTypeCode.getExtendS4())) {//尘肺病扩展字段4为1
            cardVo.setDiag1Date(card.getDiag1Date() == null ? "" : DateUtils.formatDate(card.getDiag1Date()));
            cardVo.setDiag2Date(card.getDiag2Date() == null ? "" : DateUtils.formatDate(card.getDiag2Date()));
            cardVo.setDiag3Date(card.getDiag3Date() == null ? "" : DateUtils.formatDate(card.getDiag3Date()));
            cardVo.setRptTypeName(card.getFkByRptTypeId() == null ? "" : card.getFkByRptTypeId().getCodeName());
            cardVo.setIszdcfb("1");
        }
        cardVo.setZyPoisonType("");
        if (zydTypeCode != null && "2".equals(zydTypeCode.getExtendS4())) {//化学中毒扩展字段4为2
            Integer zyPoisonType = card.getZyPoisonType();
            if (zyPoisonType == 1) {
                cardVo.setZyPoisonType("急性");
            } else if (zyPoisonType == 2) {
                cardVo.setZyPoisonType("慢性");
            }
        }
        cardVo.setDiagDate(card.getDiagDate() == null ? "" : DateUtils.formatDate(card.getDiagDate()));
        cardVo.setDiagUnitName(card.getDiagUnitName());
        cardVo.setApplyJdDate(card.getApplyJdDate() == null ? "" : DateUtils.formatDate(card.getApplyJdDate()));
        cardVo.setAprsCentDate(card.getAprsCentDate() == null ? "" : DateUtils.formatDate(card.getAprsCentDate()));
        cardVo.setJdTypeName(card.getFkByJdTypeId() == null ? "" : card.getFkByJdTypeId().getCodeName());
        cardVo.setJdRstName(card.getFkByJdRstId() == null ? "" : card.getFkByJdRstId().getCodeName());
        cardVo.setJdAgainRstName(card.getFkByJdAgainRstId() == null ? "" : card.getFkByJdAgainRstId().getCodeName());
        cardVo.setJdUnitName(card.getJdUnitName());
        cardVo.setJdUnitCreditCode(card.getJdUnitCreditCode());
        Integer ifJdZyb = card.getIfJdZyb();
        cardVo.setIfJdZyb("");
        if(ifJdZyb != null){
            if (ifJdZyb == 0) {
                cardVo.setIfJdZyb("否");
                cardVo.setJdZybTypeName("无");
                cardVo.setJdZybDisName("");
                cardVo.setJdZybDisTypeName("无");
            } else if (ifJdZyb == 1) {
                cardVo.setIfJdZyb("是");
                cardVo.setJdZybTypeName(card.getFkByJdZybTypeId() == null ? "" : card.getFkByJdZybTypeId().getCodeName());
                cardVo.setJdZybDisName(StringUtils.isBlank(card.getJdZybDisName())?"":"（"+card.getJdZybDisName()+"）");
                cardVo.setJdZybDisTypeName(card.getFkByJdZybDisTypeId() == null ? "" : card.getFkByJdZybDisTypeId().getCodeName());
            }
        }
        TsSimpleCode jdZydTypeCode = card.getFkByJdZybTypeId();
        cardVo.setIsjdcfb("");
        if (jdZydTypeCode != null && "1".equals(jdZydTypeCode.getExtendS4())) {//尘肺病扩展字段4为1
            cardVo.setJdDiag1Date(card.getJdDiag1Date() == null ? "" : DateUtils.formatDate(card.getJdDiag1Date()));
            cardVo.setJdDiag2Date(card.getJdDiag2Date() == null ? "" : DateUtils.formatDate(card.getJdDiag2Date()));
            cardVo.setJdDiag3Date(card.getJdDiag3Date() == null ? "" : DateUtils.formatDate(card.getJdDiag3Date()));
            cardVo.setJdRptTypeName(card.getFkByJdRptTypeId()== null ? "" : card.getFkByJdRptTypeId().getCodeName());
            cardVo.setIsjdcfb("1");
        }
        cardVo.setJdZyPoisonType("");
        if (jdZydTypeCode != null && "2".equals(jdZydTypeCode.getExtendS4())) {//化学中毒扩展字段4为2
            Integer jdZyPoisonType = card.getJdZyPoisonType();
            if (jdZyPoisonType == 1) {
                cardVo.setJdZyPoisonType("急性");
            } else if (jdZyPoisonType == 2) {
                cardVo.setJdZyPoisonType("慢性");
            }
        }
        cardVo.setFillUnitName(card.getFillUnitName());//填表单位
        cardVo.setFillFormPsn(card.getFillFormPsn());//填表人
        cardVo.setFillLink(card.getFillLink());
        cardVo.setFillDate(card.getFillDate() == null ? "" :DateUtils.formatDate(card.getFillDate()));
        cardVo.setRptUnitName(card.getRptUnitName());//报告单位
        cardVo.setRptPsn(card.getRptPsn());//报告人
        cardVo.setRptLink(card.getRptLink());
        cardVo.setRptDate(card.getRptDate() == null ? "" :DateUtils.formatDate(card.getRptDate()));
        cardVo.setRmk(card.getRmk());
        cardVos.add(cardVo);
        rptDataList.add(new FastReportData<>(ZwjdArchivesCardVo.class,
                "cardVo", cardVos));
        return rptDataList;
    }

    /**
     * <p>方法描述：文书删除</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-23
     **/
    public void delMadedwrit() {
        try {
            archivesCard.setAnnexPath(null);
            ZwArchivesCardCommUtils.clearArchivesCardEntity(archivesCard);
            archivesCardService.upsertEntity(archivesCard);
            ZwArchivesCardCommUtils.initEntity(archivesCard);
            JsfUtil.addSuccessMessage("删除成功！");
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("disabledInput('false','archivesCardDiv')");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * <p>方法描述：附件查看</p>
     * @MethodAuthor： yzz
     * @Date：2021-10-26
     **/
    public void toAnnexView() {
        if (StringUtils.isBlank(archivesCard.getAnnexPath())) {
            JsfUtil.addErrorMessage("附件已经被删除，请重新上传！");
            return;
        }
        RequestContext.getCurrentInstance().execute(
                "window.open('" + FileUtils.previewFile(archivesCard.getAnnexPath(),
                        "") + "')");
    }


    /**
     *  <p>方法描述：验证数据(格式验证)</p>
     * @MethodAuthor hsj
     */
    public boolean veryData() {
        boolean flag = false;
        //劳动者信息
        //劳动者
        if(StringUtils.isBlank(archivesCard.getPersonnelName())){
            JsfUtil.addErrorMessage("劳动者姓名不能为空！");
            flag = true;
        }
        //证件号码
        if(ifIdcAble && null != archivesCard.getFkByCardTypeId() && null != archivesCard.getFkByCardTypeId().getRid()){
            String tmpIdc = this.archivesCard.getIdc();
            if(StringUtils.isBlank(tmpIdc)) {
                JsfUtil.addErrorMessage("当选择证件类别为暂未获取时，证件号码才能为空！");
                flag = true;
            }else {
                String checkIDC = validateIdc(tmpIdc, archivesCard.getFkByCardTypeId());
                if(StringUtils.isNotBlank(checkIDC)) {
                    JsfUtil.addErrorMessage(checkIDC);
                    flag = true;
                }

            }
        }
        //联系电话
        if (StringUtils.isNotBlank(archivesCard.getLinktel())) {
            if (!StringUtils.vertyPhone(archivesCard.getLinktel())) {
                JsfUtil.addErrorMessage("劳动者联系电话格式不正确！");
                flag = true;
            }
        }
        //基本信息
        if(null == archivesCard.getFkByCrptId()){
            JsfUtil.addErrorMessage("用人单位不能为空！");
            flag = true;
        }
        //用人单位行业类别为人力资源时，用工单位社会信用代码不能和用人单位一致
        if(null != archivesCard.getFkByIndusTypeId() && "2".equals(archivesCard.getFkByIndusTypeId().getExtendS1())){
            if(StringUtils.isNotBlank(archivesCard.getCrptName()) && StringUtils.isNotBlank(archivesCard.getEmpCrptName()) && archivesCard.getCrptName().equals(archivesCard.getEmpCrptName()) ){
                JsfUtil.addErrorMessage("用人单位行业类别为人力资源时，用工单位名称不能和用人单位一致！");
                flag = true;
            }
            if(StringUtils.isNotBlank(archivesCard.getCreditCode()) && StringUtils.isNotBlank(archivesCard.getEmpCreditCode()) && archivesCard.getCreditCode().equals(archivesCard.getEmpCreditCode()) ){
                JsfUtil.addErrorMessage("用人单位行业类别为人力资源时，用工单位社会信用代码不能和用人单位一致！");
                flag = true;
            }
        }
        // 验证电话、邮编、填表人联系点话
        if (StringUtils.isNotBlank(archivesCard.getSafephone())) {
            if (!StringUtils.vertyPhone(archivesCard.getSafephone())) {
                JsfUtil.addErrorMessage("用人单位联系人电话格式不正确！");
                flag = true;
            }
        }
        if (StringUtils.isNotBlank(archivesCard.getFillLink())) {
            if (!StringUtils.vertyPhone(archivesCard.getFillLink())) {
                JsfUtil.addErrorMessage("填表人联系电话格式不正确！");
                flag = true;
            }
        }
        if (StringUtils.isNotBlank(archivesCard.getRptLink())) {
            if (!StringUtils.vertyPhone(archivesCard.getRptLink())) {
                JsfUtil.addErrorMessage("报告人联系电话格式不正确！");
                flag = true;
            }
        }
        if (StringUtils.isNotBlank(archivesCard.getPostcode())) {
            if (!StringUtils.vertyPost(archivesCard.getPostcode())) {
                JsfUtil.addErrorMessage("邮编格式不正确！");
                flag = true;
            }
        }
        //鉴定日期
        if(null ==  archivesCard.getAprsCentDate() ){
            JsfUtil.addErrorMessage("鉴定日期不能为空！");
            flag = true;
        }
        if (null == archivesCard.getRptDate()) {
            JsfUtil.addErrorMessage("报告日期不能为空！");
            flag = true;
        }
        //职业病鉴定信息
        //申请诊断日期：大于出生日期
        if(null != archivesCard.getBirthday() && null != archivesCard.getApplyDate()){
            if(archivesCard.getBirthday().after(archivesCard.getApplyDate()) || archivesCard.getBirthday().equals(archivesCard.getApplyDate())){
                String msg ="申请诊断日期应大于出生日期！";
                JsfUtil.addErrorMessage(msg);
                flag = true;
            }
        }
        //诊断日期：大于等于申请诊断日期
        if(null != archivesCard.getDiagDate() && null != archivesCard.getApplyDate()){
            if(archivesCard.getApplyDate().after(archivesCard.getDiagDate())){
                String msg ="诊断日期应大于等于申请诊断日期！";
                JsfUtil.addErrorMessage(msg);
                flag = true;
            }
        }
        //尘肺病种类型（5026的码表扩展字段4为1）
        if(null != archivesCard.getFkByZybTypeId() && "1".equals(archivesCard.getFkByZybTypeId().getExtendS4()) ){
            //诊断1期、诊断Ⅱ期、诊断Ⅲ期：大于出生日期，诊断1期<诊断Ⅱ期<诊断Ⅲ期
                Date birthday = this.archivesCard.getBirthday();
                Date diag1Date = this.archivesCard.getDiag1Date();
                if (null!=diag1Date && null!=birthday) {
                    if (diag1Date.before(birthday) || diag1Date.equals(birthday)) {
                        JsfUtil.addErrorMessage("职业病鉴定信息诊断I期应大于出生日期！");
                        flag = true;
                    }
                }
                Date diag2Date = this.archivesCard.getDiag2Date();
                if (null!=diag2Date && null!=birthday) {
                    if (diag2Date.before(birthday) || diag2Date.equals(birthday)) {
                        JsfUtil.addErrorMessage("职业病鉴定信息诊断Ⅱ期应大于出生日期！");
                        flag = true;
                    }
                }
                if (null!=diag2Date && null!=diag1Date) {
                    if (diag2Date.before(diag1Date) || diag2Date.equals(diag1Date)) {
                        JsfUtil.addErrorMessage("职业病鉴定信息诊断Ⅱ期应大于诊断I期！");
                        flag = true;
                    }
                }
                Date diag3Date = this.archivesCard.getDiag3Date();
                if (null!=diag3Date && null!=diag2Date) {
                    if (diag3Date.before(diag2Date) || diag3Date.equals(diag2Date)) {
                        JsfUtil.addErrorMessage("职业病鉴定信息诊断Ⅲ期应大于诊断Ⅱ期！");
                        flag = true;
                    }
                }
                if (null!=diag3Date && null!=diag1Date) {
                    if (diag3Date.before(diag1Date) || diag3Date.equals(diag1Date)) {
                        JsfUtil.addErrorMessage("职业病鉴定信息诊断Ⅲ期应大于诊断I期！");
                        flag = true;
                    }
                }
                if (null!=diag3Date && null!=birthday) {
                    if (diag3Date.before(birthday) || diag3Date.equals(birthday)) {
                        JsfUtil.addErrorMessage("职业病鉴定信息诊断Ⅲ期应大于出生日期！");
                        flag = true;
                    }
                }
        }
        //申请鉴定日期：大于等于诊断日期
        if(null != archivesCard.getDiagDate() && null != archivesCard.getApplyJdDate()){
            if(archivesCard.getDiagDate().after(archivesCard.getApplyJdDate())){
                String msg ="申请鉴定日期应大于等于诊断日期！";
                JsfUtil.addErrorMessage(msg);
                flag = true;
            }
        }
        //鉴定日期：大于等于申请鉴定日期
        if(null != archivesCard.getAprsCentDate() && null != archivesCard.getApplyJdDate()){
            if(archivesCard.getApplyJdDate().after(archivesCard.getAprsCentDate())){
                String msg ="鉴定日期应大于等于申请鉴定日期！";
                JsfUtil.addErrorMessage(msg);
                flag = true;
            }
        }
        //鉴定类型为首次鉴定时，首次鉴定结论一致时，控制病种一致
        if(null != archivesCard.getFkByJdTypeId() && "1".equals(archivesCard.getFkByJdTypeId().getExtendS1())
                && null !=  archivesCard.getFkByJdRstId() && "1".equals(archivesCard.getFkByJdRstId().getExtendS1())
                && null !=  archivesCard.getIfZyb() &&  null !=  archivesCard.getIfJdZyb()
        ){
            if( !archivesCard.getIfZyb().equals(archivesCard.getIfJdZyb()) ){
                JsfUtil.addErrorMessage("首次鉴定且结论一致时，鉴定信息和鉴定结果内职业病名称应一致！");
                flag = true;
            }else if(null !=  archivesCard.getFkByZybTypeId() && null !=  archivesCard.getFkByZybTypeId().getRid()
                    && null !=  archivesCard.getFkByJdZybTypeId() && null !=  archivesCard.getFkByJdZybTypeId().getRid()
                    && !archivesCard.getFkByZybTypeId().getRid().equals(archivesCard.getFkByJdZybTypeId().getRid())){
                JsfUtil.addErrorMessage("首次鉴定且结论一致时，鉴定信息和鉴定结果内职业病名称应一致！");
                flag = true;
            }
        }
        //尘肺病种类型（5026的码表扩展字段4为1）
        if(null != archivesCard.getFkByJdZybTypeId() && "1".equals(archivesCard.getFkByJdZybTypeId().getExtendS4()) && this.ifAgainRst){
            //诊断1期、诊断Ⅱ期、诊断Ⅲ期：大于出生日期，诊断1期<诊断Ⅱ期<诊断Ⅲ期
                Date birthday = this.archivesCard.getBirthday();
                Date diag1Date = this.archivesCard.getJdDiag1Date();
                if (null!=diag1Date && null!=birthday) {
                    if (diag1Date.before(birthday) || diag1Date.equals(birthday)) {
                        JsfUtil.addErrorMessage("职业病鉴定结果诊断I期应大于出生日期！");
                        flag = true;
                    }
                }
                Date diag2Date = this.archivesCard.getJdDiag2Date();
                if (null!=diag2Date && null!=birthday) {
                    if (diag2Date.before(birthday) || diag2Date.equals(birthday)) {
                        JsfUtil.addErrorMessage("职业病鉴定结果诊断Ⅱ期应大于出生日期！");
                        flag = true;
                    }
                }
                if (null!=diag2Date && null!=diag1Date) {
                    if (diag2Date.before(diag1Date) || diag2Date.equals(diag1Date)) {
                        JsfUtil.addErrorMessage("职业病鉴定结果诊断Ⅱ期应大于诊断I期！");
                        flag = true;
                    }
                }
                Date diag3Date = this.archivesCard.getJdDiag3Date();
                if (null!=diag3Date && null!=diag2Date) {
                    if (diag3Date.before(diag2Date) || diag3Date.equals(diag2Date)) {
                        JsfUtil.addErrorMessage("职业病鉴定结果诊断Ⅲ期应大于诊断Ⅱ期！");
                        flag = true;
                    }
                }
                if (null!=diag3Date && null!=diag1Date) {
                    if (diag3Date.before(diag1Date) || diag3Date.equals(diag1Date)) {
                        JsfUtil.addErrorMessage("职业病鉴定结果诊断Ⅲ期应大于诊断I期！");
                        flag = true;
                    }
                }
                if (null!=diag3Date && null!=birthday) {
                    if (diag3Date.before(birthday) || diag3Date.equals(birthday)) {
                        JsfUtil.addErrorMessage("职业病鉴定结果诊断Ⅲ期应大于出生日期！");
                        flag = true;
                    }
                }
        }
        //报告人，填表人
        //填表日期：大于等于鉴定日期。
        if(null != archivesCard.getFillDate() && null != archivesCard.getAprsCentDate()
                && archivesCard.getAprsCentDate().after(archivesCard.getFillDate())){
            JsfUtil.addErrorMessage("填表日期应大于等于鉴定日期！");
            flag = true;
        }
        //报告日期：默认当前日期，大于等于填报日期，可修改
        if(null != archivesCard.getFillDate() && null != archivesCard.getRptDate()
                && archivesCard.getFillDate().after(archivesCard.getRptDate())){
            JsfUtil.addErrorMessage("报告日期应大于等于填表日期！");
            flag = true;
        }
        return flag;
    }


    /**
     *  <p>方法描述：证件号的验证</p>
     * @MethodAuthor hsj
     */
    public String validateIdc(String tmpIdc, TsSimpleCode tsSimpleCode){
        String checkIDC = null;
        boolean flag = false;
        if(null != tsSimpleCode){
            if(tsSimpleCode.getCodeNo().equals("01")){
                // 验证身份证号合法性
                checkIDC = IdcUtils.checkIDC(tmpIdc);
                if(null != checkIDC){
                    flag = true;
                }
            }else if(tsSimpleCode.getCodeNo().equals("02") && !IdcUtils.isHousehold(tmpIdc)){
                // 验证户口簿
                flag = true;
            }else if(tsSimpleCode.getCodeNo().equals("03") && !IdcUtils.isPassPort(tmpIdc)){
                // 验证护照
                flag = true;
            }else if(tsSimpleCode.getCodeNo().equals("04") && !IdcUtils.isMilitary(tmpIdc)){
                // 验证军官证
                flag = true;
            }else if(tsSimpleCode.getCodeNo().equals("06") && !IdcUtils.isHmCard(tmpIdc)){
                // 验证港澳通行证
                flag = true;
            }else if(tsSimpleCode.getCodeNo().equals("07") && !IdcUtils.isTaiWanEntry(tmpIdc)){
                // 验证台胞证
                flag = true;
            }
        }
        if(flag){
            checkIDC = "证件号码格式错误！";
        }
        return checkIDC;
    }


    /**
     *  <p>方法描述：为空验证</p>
     * @MethodAuthor hsj
     */
    public boolean veryNullData() {
        boolean flag = false;
        //劳动者
        if(StringUtils.isBlank(archivesCard.getPersonnelName())){
            JsfUtil.addErrorMessage("劳动者姓名不能为空！");
            flag = true;
        }

        //证件类型
        if(null == archivesCard.getFkByCardTypeId()){
            JsfUtil.addErrorMessage("证件类型不能为空！");
            flag = true;
        }
        //证件号码
        if(StringUtils.isBlank(archivesCard.getIdc()) && ifIdcAble){
            JsfUtil.addErrorMessage("证件号码不能为空！");
            flag = true;
        }
        if(StringUtils.isNotBlank(archivesCard.getIdc()) && !ifIdcAble){
            JsfUtil.addErrorMessage("证件号码应为空！");
            flag = true;
        }
        //性别
        if(null == archivesCard.getSex()){
            JsfUtil.addErrorMessage("性别不能为空！");
            flag = true;
        }
        //出生日期
        if(null == archivesCard.getBirthday()){
            JsfUtil.addErrorMessage("出生日期不能为空！");
            flag = true;
        }
        //联系电话
        if(StringUtils.isBlank(archivesCard.getLinktel())){
            JsfUtil.addErrorMessage("劳动者联系电话不能为空！");
            flag = true;
        }
        //鉴定日期
        if(null ==  archivesCard.getAprsCentDate() ){
            JsfUtil.addErrorMessage("鉴定日期不能为空！");
            flag = true;
        }
        if (null == archivesCard.getRptDate()) {
            JsfUtil.addErrorMessage("报告日期不能为空！");
            flag = true;
        }
        //基本信息
        if(null == archivesCard.getFkByCrptId()){
            JsfUtil.addErrorMessage("用人单位不能为空！");
            flag = true;
        }
        if(null == archivesCard.getFkByEmpCrptId()){
            JsfUtil.addErrorMessage("用工单位不能为空！");
            flag = true;
        }
        // 验证电话、邮编、填表人联系点话
        if (StringUtils.isBlank(archivesCard.getSafephone())) {
            JsfUtil.addErrorMessage("用人单位联系人电话不能为空！");
            flag = true;
        }

        if (StringUtils.isBlank(archivesCard.getFillFormPsn())) {
            JsfUtil.addErrorMessage("填表人不能为空！");
            flag = true;
        }
        if (StringUtils.isBlank(archivesCard.getFillLink())) {
            JsfUtil.addErrorMessage("填表人联系电话不能为空！");
            flag = true;
        }
        if (null == archivesCard.getFillDate()) {
            JsfUtil.addErrorMessage("填表日期不能为空！");
            flag = true;
        }
        if (null == archivesCard.getFkByFillUnitId()) {
            JsfUtil.addErrorMessage("填表单位/报告单位不能为空！");
            flag = true;
        }
        if (StringUtils.isBlank(archivesCard.getRptPsn())) {
            JsfUtil.addErrorMessage("报告人不能为空！");
            flag = true;
        }
        if (StringUtils.isBlank(archivesCard.getRptLink())) {
            JsfUtil.addErrorMessage("报告人联系电话不能为空！");
            flag = true;
        }

        //是否诊断为职业病
        if (null == archivesCard.getIfZyb()) {
            JsfUtil.addErrorMessage("是否诊断为职业病不能为空！");
            flag = true;
        }
        if(null != archivesCard.getIfZyb() && 1 == archivesCard.getIfZyb()){
            //职业病名称
            if (null == archivesCard.getFkByZybTypeId() || null == archivesCard.getFkByZybTypeId().getRid()) {
                JsfUtil.addErrorMessage("职业病鉴定信息职业病名称不能为空！");
                flag = true;
            }
            if(null != archivesCard.getFkByZybTypeId() && null != archivesCard.getFkByZybTypeId().getRid()
                    && Integer.valueOf(1).equals(archivesCard.getFkByZybTypeId().getExtendS2()) && StringUtils.isBlank(archivesCard.getZybDisName())){
                JsfUtil.addErrorMessage("职业病鉴定信息其他职业病名称不能为空！");
                flag = true;
            }
            //职业病种类
            if (null == archivesCard.getFkByZybDisTypeId() || null == archivesCard.getFkByZybDisTypeId().getRid()) {
                JsfUtil.addErrorMessage("职业病鉴定信息职业病种类不能为空！");
                flag = true;
            }
        }
        //是否为尘肺
        if(null !=  archivesCard.getFkByZybTypeId() && "1".equals(archivesCard.getFkByZybTypeId().getExtendS4())){
            //病例类型
            if(null ==  archivesCard.getFkByRptTypeId() || null ==  archivesCard.getFkByRptTypeId().getRid() ){
                JsfUtil.addErrorMessage("职业病鉴定信息病例类型不能为空！");
                flag = true;
            }else{
                //病例类型是首次晋期病例时，一期二期三期诊断日期必填其中两个，尘肺病病例类型是再次晋期病例时，三个日期都必填，其他类型必填一个。（病例类型根据码表5505的扩展字段1判断）
                Date diag1Date = archivesCard.getDiag1Date();
                Date diag2Date = archivesCard.getDiag2Date();
                Date diag3Date = archivesCard.getDiag3Date();
//                TsSimpleCode t = this.tsSimpleMap.get(archivesCard.getFkByRptTypeId().getRid());
//                archivesCard.setFkByRptTypeId(t);
                if ("1".equals(archivesCard.getFkByRptTypeId().getExtendS1())) {
                    if ((null==diag1Date && null==diag2Date)||(null==diag1Date && null==diag3Date)||(null==diag2Date && null==diag3Date)) {
                        JsfUtil.addErrorMessage("职业病鉴定信息诊断I期、诊断Ⅱ期、诊断Ⅲ期必填其中两个！");
                        flag = true;
                    }
                }else if ("2".equals(archivesCard.getFkByRptTypeId().getExtendS1())) {
                    if (null == diag1Date || null == diag2Date || null == diag3Date) {
                        JsfUtil.addErrorMessage("职业病鉴定信息诊断I期、诊断Ⅱ期、诊断Ⅲ期不能为空！");
                        flag = true;
                    }
                }else {
                    if (null == diag1Date && null == diag2Date
                            && null == diag3Date) {
                        JsfUtil.addErrorMessage("职业病鉴定信息诊断I期、诊断Ⅱ期、诊断Ⅲ期必填其中一个！");
                        flag = true;
                    }
                }
            }

        }
        //化学
        if(null !=  archivesCard.getFkByZybTypeId() && "2".equals(archivesCard.getFkByZybTypeId().getExtendS4())){
            if(null ==  archivesCard.getZyPoisonType() ){
                JsfUtil.addErrorMessage("职业病鉴定信息职业性化学中毒分类不能为空！");
                flag = true;
            }
        }
        //申请诊断日期
        if(null ==  archivesCard.getApplyDate() ){
            JsfUtil.addErrorMessage("申请诊断日期不能为空！");
            flag = true;
        }
        //诊断日期
        if(null ==  archivesCard.getDiagDate() ){
            JsfUtil.addErrorMessage("诊断日期不能为空！");
            flag = true;
        }
        //诊断机构名称
        if(null ==  archivesCard.getFkByDiagUnitId() ){
            JsfUtil.addErrorMessage("诊断机构名称不能为空！");
            flag = true;
        }
        //申请鉴定日期
        if(null ==  archivesCard.getApplyJdDate() ){
            JsfUtil.addErrorMessage("申请鉴定日期不能为空！");
            flag = true;
        }
        //鉴定类型
        if(null ==  archivesCard.getFkByJdTypeId() ||  null ==  archivesCard.getFkByJdTypeId().getRid()){
            JsfUtil.addErrorMessage("鉴定类型不能为空！");
            flag = true;
        }else{
//            TsSimpleCode t = this.tsSimpleMap.get(archivesCard.getFkByJdTypeId().getRid());
//            archivesCard.setFkByJdTypeId(t);
            //首次鉴定结论
            if(null ==  archivesCard.getFkByJdRstId() ||  null ==  archivesCard.getFkByJdRstId().getRid()){
                JsfUtil.addErrorMessage("首次鉴定结论不能为空！");
                flag = true;
            }
            //再次鉴定结论
            if("2".equals(archivesCard.getFkByJdTypeId().getExtendS1())){
                if(null ==  archivesCard.getFkByJdAgainRstId() ||  null ==  archivesCard.getFkByJdAgainRstId().getRid()){
                    JsfUtil.addErrorMessage("再次鉴定结论不能为空！");
                    flag = true;
                }
            }
        }
        //鉴定机构名称
        if(null ==  archivesCard.getFkByJdUnitId() ){
            JsfUtil.addErrorMessage("鉴定机构名称不能为空！");
            flag = true;
        }
        //鉴定机构社会信用代码
        if(StringUtils.isBlank(archivesCard.getJdUnitCreditCode())){
            JsfUtil.addErrorMessage("鉴定机构社会信用代码不能为空！");
            flag = true;
        }
        //职业病鉴定结果
        //是否鉴定为职业病
        if(this.ifAgainRst){
            if (null == archivesCard.getIfJdZyb()) {
                JsfUtil.addErrorMessage("是否鉴定为职业病不能为空！");
                flag = true;
            }
            if(null != archivesCard.getIfJdZyb() && 1 == archivesCard.getIfJdZyb()){
                //职业病名称
                if (null == archivesCard.getFkByJdZybTypeId() || null == archivesCard.getFkByJdZybTypeId().getRid()) {
                    JsfUtil.addErrorMessage("职业病鉴定结果职业病名称不能为空！");
                    flag = true;
                }
                if(null != archivesCard.getFkByJdZybTypeId() && null != archivesCard.getFkByJdZybTypeId().getRid()
                        && Integer.valueOf(1).equals(archivesCard.getFkByJdZybTypeId().getExtendS2()) && StringUtils.isBlank(archivesCard.getJdZybDisName())){
                    JsfUtil.addErrorMessage("职业病鉴定结果其他职业病名称不能为空！");
                    flag = true;
                }
                //职业病种类
                if (null == archivesCard.getFkByJdZybDisTypeId() || null == archivesCard.getFkByJdZybDisTypeId().getRid()) {
                    JsfUtil.addErrorMessage("职业病鉴定结果职业病种类不能为空！");
                    flag = true;
                }
            }
            //是否为尘肺
            if(null !=  archivesCard.getFkByJdZybTypeId() && "1".equals(archivesCard.getFkByJdZybTypeId().getExtendS4())){
                //病例类型
                if(null ==  archivesCard.getFkByJdRptTypeId() || null ==  archivesCard.getFkByJdRptTypeId().getRid() ){
                    JsfUtil.addErrorMessage("职业病鉴定结果病例类型不能为空！");
                    flag = true;
                }else{
                    //病例类型是首次晋期病例时，一期二期三期诊断日期必填其中两个，尘肺病病例类型是再次晋期病例时，三个日期都必填，其他类型必填一个。（病例类型根据码表5505的扩展字段1判断）
                    Date diag1Date = archivesCard.getJdDiag1Date();
                    Date diag2Date = archivesCard.getJdDiag2Date();
                    Date diag3Date = archivesCard.getJdDiag3Date();
//                TsSimpleCode t = this.tsSimpleMap.get(archivesCard.getFkByJdRptTypeId().getRid());
//                archivesCard.setFkByJdRptTypeId(t);
                    if ("1".equals(archivesCard.getFkByJdRptTypeId().getExtendS1())) {
                        if ((null==diag1Date && null==diag2Date)||(null==diag1Date && null==diag3Date)||(null==diag2Date && null==diag3Date)) {
                            JsfUtil.addErrorMessage("职业病鉴定结果诊断I期、诊断Ⅱ期、诊断Ⅲ期必填其中两个！");
                            flag = true;
                        }
                    }else if ("2".equals(archivesCard.getFkByJdRptTypeId().getExtendS1())) {
                        if (null == diag1Date || null == diag2Date || null == diag3Date) {
                            JsfUtil.addErrorMessage("职业病鉴定结果诊断I期、诊断Ⅱ期、诊断Ⅲ期不能为空！");
                            flag = true;
                        }
                    }else {
                        if (null == diag1Date && null == diag2Date
                                && null == diag3Date) {
                            JsfUtil.addErrorMessage("职业病鉴定结果诊断I期、诊断Ⅱ期、诊断Ⅲ期必填其中一个！");
                            flag = true;
                        }
                    }
                }

            }
            //化学
            if(null !=  archivesCard.getFkByJdZybTypeId() && "2".equals(archivesCard.getFkByJdZybTypeId().getExtendS4())){
                if(null ==  archivesCard.getJdZyPoisonType() ){
                    JsfUtil.addErrorMessage("职业病鉴定结果职业性化学中毒分类不能为空！");
                    flag = true;
                }
            }

        }
        return flag;
    }
    /**
     *  <p>方法描述：【职业鉴定结果】信息存储鉴定信息内相同信息点的值</p>
     * @MethodAuthor hsj 2023-06-06 15:30
     */
    public void saveJdArchivesCard() {
        //一致
        archivesCard.setIfJdZyb(archivesCard.getIfZyb());
        archivesCard.setFkByJdZybTypeId(archivesCard.getFkByZybTypeId());
        archivesCard.setJdZybDisName(archivesCard.getZybDisName());
        archivesCard.setFkByJdZybDisTypeId(archivesCard.getFkByZybDisTypeId());
        archivesCard.setJdDiag1Date(archivesCard.getDiag1Date());
        archivesCard.setJdDiag2Date(archivesCard.getDiag2Date());
        archivesCard.setJdDiag3Date(archivesCard.getDiag3Date());
        archivesCard.setFkByJdRptTypeId(archivesCard.getFkByRptTypeId());
        archivesCard.setJdZyPoisonType(archivesCard.getZyPoisonType());
        zybjgCode = zybjdCode;
        this.jdRptTypeId = this.rptTypeId;
    }
    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchPsnName() {
        return searchPsnName;
    }

    public void setSearchPsnName(String searchPsnName) {
        this.searchPsnName = searchPsnName;
    }

    public String getSearchIdc() {
        return searchIdc;
    }

    public void setSearchIdc(String searchIdc) {
        this.searchIdc = searchIdc;
    }

    public List<TsSimpleCode> getJdTypeList() {
        return jdTypeList;
    }

    public void setJdTypeList(List<TsSimpleCode> jdTypeList) {
        this.jdTypeList = jdTypeList;
    }

    public String[] getSearchJdTypes() {
        return searchJdTypes;
    }

    public void setSearchJdTypes(String[] searchJdTypes) {
        this.searchJdTypes = searchJdTypes;
    }

    public Date getSearchJdBdate() {
        return searchJdBdate;
    }

    public void setSearchJdBdate(Date searchJdBdate) {
        this.searchJdBdate = searchJdBdate;
    }

    public Date getSearchJdEdate() {
        return searchJdEdate;
    }

    public void setSearchJdEdate(Date searchJdEdate) {
        this.searchJdEdate = searchJdEdate;
    }

    public List<TsSimpleCode> getFirstJdResultList() {
        return firstJdResultList;
    }

    public void setFirstJdResultList(List<TsSimpleCode> firstJdResultList) {
        this.firstJdResultList = firstJdResultList;
    }

    public String getSelectFirstJdResultNames() {
        return selectFirstJdResultNames;
    }

    public void setSelectFirstJdResultNames(String selectFirstJdResultNames) {
        this.selectFirstJdResultNames = selectFirstJdResultNames;
    }

    public String getSelectFirstJdResultIds() {
        return selectFirstJdResultIds;
    }

    public void setSelectFirstJdResultIds(String selectFirstJdResultIds) {
        this.selectFirstJdResultIds = selectFirstJdResultIds;
    }

    public List<TsSimpleCode> getNextJdResultList() {
        return nextJdResultList;
    }

    public void setNextJdResultList(List<TsSimpleCode> nextJdResultList) {
        this.nextJdResultList = nextJdResultList;
    }

    public String getSelectNextJdResulNames() {
        return selectNextJdResulNames;
    }

    public void setSelectNextJdResulNames(String selectNextJdResulNames) {
        this.selectNextJdResulNames = selectNextJdResulNames;
    }

    public String getSelectNextJdResulIds() {
        return selectNextJdResulIds;
    }

    public void setSelectNextJdResulIds(String selectNextJdResulIds) {
        this.selectNextJdResulIds = selectNextJdResulIds;
    }

    public Date getSearchRptBdate() {
        return searchRptBdate;
    }

    public void setSearchRptBdate(Date searchRptBdate) {
        this.searchRptBdate = searchRptBdate;
    }

    public Date getSearchRptEdate() {
        return searchRptEdate;
    }

    public void setSearchRptEdate(Date searchRptEdate) {
        this.searchRptEdate = searchRptEdate;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public String getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(String checkLevel) {
        this.checkLevel = checkLevel;
    }

    public TdZwjdArchivesCard getArchivesCard() {
        return archivesCard;
    }

    public void setArchivesCard(TdZwjdArchivesCard archivesCard) {
        this.archivesCard = archivesCard;
    }

    public List<TsSimpleCode> getCardTypeList() {
        return cardTypeList;
    }

    public void setCardTypeList(List<TsSimpleCode> cardTypeList) {
        this.cardTypeList = cardTypeList;
    }


    public List<TsSimpleCode> getRptTypeList() {
        return rptTypeList;
    }

    public void setRptTypeList(List<TsSimpleCode> rptTypeList) {
        this.rptTypeList = rptTypeList;
    }

    public TdZwBgkLastSta getTdZwBgkLastSta() {
        return tdZwBgkLastSta;
    }

    public void setTdZwBgkLastSta(TdZwBgkLastSta tdZwBgkLastSta) {
        this.tdZwBgkLastSta = tdZwBgkLastSta;
    }

    public String getCrpyType() {
        return crpyType;
    }

    public void setCrpyType(String crpyType) {
        this.crpyType = crpyType;
    }

    public boolean isIfShowDesign() {
        return ifShowDesign;
    }

    public void setIfShowDesign(boolean ifShowDesign) {
        this.ifShowDesign = ifShowDesign;
    }

    public Map<Integer, TsSimpleCode> getTsSimpleMap() {
        return tsSimpleMap;
    }

    public void setTsSimpleMap(Map<Integer, TsSimpleCode> tsSimpleMap) {
        this.tsSimpleMap = tsSimpleMap;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TbZwWritsort getWritsort() {
        return writsort;
    }

    public void setWritsort(TbZwWritsort writsort) {
        this.writsort = writsort;
    }

    @Override
    public FastReportBean getFastReportBean() {
        return fastReportBean;
    }

    public void setFastReportBean(FastReportBean fastReportBean) {
        this.fastReportBean = fastReportBean;
    }


    @Override
    public List<FastReportData> supportFastReportDataSet() {
        return packageDatas();
    }

    @Override
    public List<FastReportDataRef> supportFastReportDataRef() {
        return null;
    }

    public boolean isIfIdcAble() {
        return ifIdcAble;
    }

    public void setIfIdcAble(boolean ifIdcAble) {
        this.ifIdcAble = ifIdcAble;
    }

    public TsSimpleCode getZybjdCode() {
        return zybjdCode;
    }

    public void setZybjdCode(TsSimpleCode zybjdCode) {
        this.zybjdCode = zybjdCode;
    }

    public TsSimpleCode getZybjgCode() {
        return zybjgCode;
    }

    public void setZybjgCode(TsSimpleCode zybjgCode) {
        this.zybjgCode = zybjgCode;
    }


    public Integer getIdcType() {
        return idcType;
    }

    public void setIdcType(Integer idcType) {
        this.idcType = idcType;
    }

    public Integer getJdType() {
        return jdType;
    }

    public void setJdType(Integer jdType) {
        this.jdType = jdType;
    }

    public Integer getJdRst() {
        return jdRst;
    }

    public void setJdRst(Integer jdRst) {
        this.jdRst = jdRst;
    }

    public Integer getRptTypeId() {
        return rptTypeId;
    }

    public void setRptTypeId(Integer rptTypeId) {
        this.rptTypeId = rptTypeId;
    }

    public Integer getJdRptTypeId() {
        return jdRptTypeId;
    }

    public void setJdRptTypeId(Integer jdRptTypeId) {
        this.jdRptTypeId = jdRptTypeId;
    }

    public Integer getJdAgainRstId() {
        return jdAgainRstId;
    }

    public void setJdAgainRstId(Integer jdAgainRstId) {
        this.jdAgainRstId = jdAgainRstId;
    }

    public TsZone getTsZone() {
        return tsZone;
    }

    public void setTsZone(TsZone tsZone) {
        this.tsZone = tsZone;
    }

    public Integer getIsView() {
        return isView;
    }

    public void setIsView(Integer isView) {
        this.isView = isView;
    }

    public TsZone getTsLocalZone() {
        return tsLocalZone;
    }

    public void setTsLocalZone(TsZone tsLocalZone) {
        this.tsLocalZone = tsLocalZone;
    }

    public Boolean getIfAgainRst() {
        return ifAgainRst;
    }

    public void setIfAgainRst(Boolean ifAgainRst) {
        this.ifAgainRst = ifAgainRst;
    }
}
