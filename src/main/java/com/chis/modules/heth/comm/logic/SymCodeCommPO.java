package com.chis.modules.heth.comm.logic;

/**
 * @Description : 症状
 * @ClassAuthor : anjing
 * @Date : 2020/4/23 16:09
 **/
public class SymCodeCommPO {

    private Integer rid;
    private String codeNo;
    private String codeName;
    private String extendS1;
    /** 是否选择 */
    private boolean ifSelected = false;
    private boolean selectAble = true;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getCodeNo() {
        return codeNo;
    }

    public void setCodeNo(String codeNo) {
        this.codeNo = codeNo;
    }

    public String getCodeName() {
        return codeName;
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    public String getExtendS1() {
        return extendS1;
    }

    public void setExtendS1(String extendS1) {
        this.extendS1 = extendS1;
    }

    public boolean isIfSelected() {
        return ifSelected;
    }

    public void setIfSelected(boolean ifSelected) {
        this.ifSelected = ifSelected;
    }

    public boolean isSelectAble() {
        return selectAble;
    }

    public void setSelectAble(boolean selectAble) {
        this.selectAble = selectAble;
    }
}
