package com.chis.modules.heth.comm.service;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.Reflections;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.logic.ExcelHeaderDTO;
import com.chis.modules.heth.comm.logic.ExcelHeaderDetailDTO;
import com.chis.modules.heth.comm.utils.UnitbasicinfoUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 场所监测数据导入
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(readOnly = false)
public class SiteMonitoringDataService extends AbstractTemplate {

    /**
     * 根据UUID获取基本信息
     *
     * @param uuid 企业ID
     * @return 基本信息
     */
    public TdZxjcUnitbasicinfo findUnitBasicInfoByUuid(String uuid) {
        StringBuilder hql = new StringBuilder();
        hql.append("select new TdZxjcUnitbasicinfo(t.rid) from TdZxjcUnitbasicinfo t ");
        hql.append("where t.uuid = '").append(uuid).append("'");
        TdZxjcUnitbasicinfo unitBasicInfo = super.findOneByHql(hql.toString(), TdZxjcUnitbasicinfo.class);
        if (ObjectUtil.isEmpty(unitBasicInfo)) {
            unitBasicInfo = new TdZxjcUnitbasicinfo();
        }
        unitBasicInfo.setUuid(uuid);
        return unitBasicInfo;
    }

    public void insertResultProBatch(List<TdZxjcResultPro> resultProList) {
        Set<Integer> mainRidSet = new HashSet<>();
        for (TdZxjcResultPro resultPro : resultProList) {
            TdZxjcUnitbasicinfo unitBasicInfo = resultPro.getFkByMainId();
            Integer rid = unitBasicInfo.getRid();
            mainRidSet.add(rid);
            Integer ifAnalysis = unitBasicInfo.getIfAnalysis();
            Integer crptId = null == unitBasicInfo.getFkByCrptId() ? null : unitBasicInfo.getFkByCrptId().getRid();
            if(ifAnalysis==null){
                if(null != crptId){
                    //增加企业共享表Id更新
                    this.executeSql("UPDATE TD_ZXJC_UNITBASICINFO SET CRPT_ID="+crptId+" WHERE RID="+rid, null);
                }
                continue;
            }
            StringBuffer sqlBuffer = new StringBuffer();
            sqlBuffer.append(" UPDATE TD_ZXJC_UNITBASICINFO SET IF_ANALYSIS = :ifAnalysis ");
            //增加企业共享表Id更新
            if(null != crptId){
                sqlBuffer.append(",").append(" CRPT_ID=").append(crptId);
            }
            sqlBuffer.append(" WHERE RID = :rid ");
            String sql = sqlBuffer.toString();
            Map<String, Object> paramMap = new HashMap<>(16);
            paramMap.put("rid", rid);
            paramMap.put("ifAnalysis", ifAnalysis);
            this.executeSql(sql, paramMap);
        }
        List<Integer> mainRidList = new ArrayList<>(mainRidSet);
        if (ObjectUtil.isNotEmpty(mainRidList)) {
            int size = mainRidList.size();
            int count = mainRidList.size() / 999;
            for (int i = 0; i <= count; i++) {
                int endIndex = Math.min((i + 1) * 999, size);
                List<Integer> subList = mainRidList.subList(i * 999, endIndex);
                String sql = "DELETE TD_ZXJC_RESULT_PRO WHERE MAIN_ID IN (:mainRid)";
                Map<String, Object> paramMap = new HashMap<>(16);
                paramMap.put("mainRid", subList);
                this.executeSql(sql, paramMap);
            }
        }
        super.saveBatchObjs(resultProList);
    }

    /**
     * <p>方法描述：更新 调查信息 相关字段</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-09-12
     **/
    public void updateUnitBasicInfoPart(TdZxjcUnitbasicinfo unitBasicInfo) {
        StringBuilder sql = new StringBuilder();
        sql.append(" update TD_ZXJC_UNITBASICINFO  set ");
        sql.append(" IF_IMPORT=").append(unitBasicInfo.getIfImport());
        if (StringUtils.isNotBlank(unitBasicInfo.getWorkNode())) {
            sql.append(" ,WORK_NODE='").append(unitBasicInfo.getWorkNode()).append("'");
        } else {
            sql.append(" ,WORK_NODE=null ");
        }
        //企业共享表ID更新
        Integer crptId = null == unitBasicInfo.getFkByCrptId() ? null : unitBasicInfo.getFkByCrptId().getRid();
        if(null != crptId){
            sql.append(" ,CRPT_ID= ").append(crptId);
        }
        sql.append(" ,IF_PRE_LAUNCH=").append(unitBasicInfo.getIfPreLaunch());
        sql.append(" ,IF_DESIGN=").append(unitBasicInfo.getIfDesign());
        sql.append(" ,IF_LAUNCH=").append(unitBasicInfo.getIfLaunch());
        sql.append(" ,MODIFY_DATE=to_date('").append(DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss")).append("','yyyy-mm-dd hh24:mi:ss')");
        sql.append(" ,MODIFY_MANID=").append(Global.getUser().getRid());
        sql.append(" where rid=").append(unitBasicInfo.getRid());
        em.createNativeQuery(sql.toString()).executeUpdate();
    }

    /**
     * <p>方法描述：调查信息 封装 保存</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-09-12
     **/
    public void dealAllDate(TdZxjcUnitbasicinfo unitBasicInfo, ExcelHeaderDTO headerDTO, List<Object> excelRowData, Map<Integer, Map<String, String>> contrastMap, Map<String, TsSimpleCode> simpleCodeMap)  {
        Map<String, ExcelHeaderDetailDTO> detailMap = headerDTO.getDetailMap();
        Map<Integer, String> hearderIndexMap = headerDTO.getHearderIndexMap();
        StringBuilder delSql = new StringBuilder();
        if (unitBasicInfo != null && unitBasicInfo.getRid() != null && detailMap != null && !detailMap.isEmpty()) {
            /*************************在线监测-单位基本信息************************************/
            if (detailMap.containsKey("3年内技术改造、引进项目情况")) {
                String val = StringUtils.objectToString(excelRowData.get(detailMap.get("3年内技术改造、引进项目情况").getStartIndex()));
                if (UnitbasicinfoUtils.ifMatcherNull(val)) {
                    unitBasicInfo.setIfImport(null);
                } else {
                    if(!ObjectUtil.isEmpty(contrastMap.get(32))&&contrastMap.get(32).containsKey(val)){
                        String contrastRight = StringUtils.objectToString(contrastMap.get(32).get(val));
                        if (ObjectUtil.isEmpty(contrastRight)) {
                            unitBasicInfo.setIfImport(null);
                        } else {
                            unitBasicInfo.setIfImport(Integer.valueOf(contrastRight));
                        }
                    }
                }
            } else {
                unitBasicInfo.setIfImport(null);
            }

            if (detailMap.containsKey("当前工作阶段")) {
                String val = StringUtils.objectToString(excelRowData.get(detailMap.get("当前工作阶段").getStartIndex()));
                if (UnitbasicinfoUtils.ifMatcherNull(val)) {
                    unitBasicInfo.setWorkNode(null);
                } else {
                    StringBuilder value = new StringBuilder();
                    String[] vals = val.split(" ");
                    for (String v : vals) {
                        if(!ObjectUtil.isEmpty(contrastMap.get(33))&&contrastMap.get(33).containsKey(v)) {
                            String contrastRight = StringUtils.objectToString(contrastMap.get(33).get(v));
                            if (StringUtils.isNotBlank(contrastRight)) {
                                value.append(",").append(contrastRight);
                            }
                        }
                    }
                    if (value.length() > 0) {
                        unitBasicInfo.setWorkNode(value.substring(1));
                    } else {
                        unitBasicInfo.setWorkNode(null);
                    }
                }
            } else {
                unitBasicInfo.setWorkNode(null);
            }

            if (detailMap.containsKey("预评价开展情况")) {
                String val = StringUtils.objectToString(excelRowData.get(detailMap.get("预评价开展情况").getStartIndex()));
                if (UnitbasicinfoUtils.ifMatcherNull(val)) {
                    unitBasicInfo.setIfPreLaunch(null);
                } else {
                    if(!ObjectUtil.isEmpty(contrastMap.get(34))&&contrastMap.get(34).containsKey(val)) {
                        String contrastRight = StringUtils.objectToString(contrastMap.get(34).get(val));
                        if (ObjectUtil.isEmpty(contrastRight)) {
                            unitBasicInfo.setIfPreLaunch(null);
                        } else {
                            unitBasicInfo.setIfPreLaunch(Integer.valueOf(contrastRight));
                        }
                    }
                }
            } else {
                unitBasicInfo.setIfPreLaunch(null);
            }
            if (detailMap.containsKey("职业病防护设施专篇")) {
                String val = StringUtils.objectToString(excelRowData.get(detailMap.get("职业病防护设施专篇").getStartIndex()));
                if (UnitbasicinfoUtils.ifMatcherNull(val)) {
                    unitBasicInfo.setIfDesign(null);
                } else {
                    if(!ObjectUtil.isEmpty(contrastMap.get(34))&&contrastMap.get(34).containsKey(val)) {
                        String contrastRight = StringUtils.objectToString(contrastMap.get(34).get(val));
                        if (ObjectUtil.isEmpty(contrastRight)) {
                            unitBasicInfo.setIfDesign(null);
                        } else {
                            unitBasicInfo.setIfDesign(Integer.valueOf(contrastRight));
                        }
                    }
                }
            } else {
                unitBasicInfo.setIfDesign(null);
            }
            if (detailMap.containsKey("控制效果评价开展情况")) {
                String val = StringUtils.objectToString(excelRowData.get(detailMap.get("控制效果评价开展情况").getStartIndex()));
                if (UnitbasicinfoUtils.ifMatcherNull(val)) {
                    unitBasicInfo.setIfLaunch(null);
                } else {
                    if(!ObjectUtil.isEmpty(contrastMap.get(34))&&contrastMap.get(34).containsKey(val)) {
                        String contrastRight = StringUtils.objectToString(contrastMap.get(34).get(val));
                        if (ObjectUtil.isEmpty(contrastRight)) {
                            unitBasicInfo.setIfLaunch(null);
                        } else {
                            unitBasicInfo.setIfLaunch(Integer.valueOf(contrastRight));
                        }
                    }
                }
            } else {
                unitBasicInfo.setIfLaunch(null);
            }
            unitBasicInfo.setModifyDate(new Date());
            unitBasicInfo.setModifyManid(Global.getUser().getRid());
            this.updateUnitBasicInfoPart(unitBasicInfo);
            /*************************在线监测-职业病危害因素种类及接触人数************************************/
            StringBuilder hql = new StringBuilder();
            hql.append(" select T from  TdZxjcUnitfactorcrowd T where T.fkByMainId.rid=").append(unitBasicInfo.getRid());
            TdZxjcUnitfactorcrowd unitfactorcrowd = this.findOneByHql(hql.toString(), TdZxjcUnitfactorcrowd.class);
            if (unitfactorcrowd == null) {
                unitfactorcrowd = new TdZxjcUnitfactorcrowd();
                unitfactorcrowd.setFkByMainId(unitBasicInfo);
                unitfactorcrowd.setModifyDate(new Date());
                unitfactorcrowd.setModifyManid(Global.getUser().getRid());
            }
            Map<String, String> totalPeoplesMap = new HashMap<>();
            totalPeoplesMap.put("接触危害总人数", "contactTotalPeoples");
            totalPeoplesMap.put("接触粉尘总人数", "hfDustPeoples");
            totalPeoplesMap.put("接触化学总人数", "hfChemistryPeoples");
            totalPeoplesMap.put("物理因素接触人数", "hfPhysicsPeoples");
            reflectionSetValNotContrast(totalPeoplesMap, unitfactorcrowd, detailMap, excelRowData, contrastMap);
            Map<String, String> ifHfMap = new HashMap<>();
            ifHfMap.put("有无接触粉尘", "ifhfDust");
            ifHfMap.put("有无接触化学因素", "ifhfChemistry");
            ifHfMap.put("有无接触物理因素", "ifhfPhysics");
            reflectionSetValWithContrast(ifHfMap, unitfactorcrowd, detailMap, excelRowData, contrastMap);
            //封装子表
            List<TdZxjcUnitfactorItem> itemList = new ArrayList<>();
            Map<String, String> totalPeoplesAndIfHfMap = new HashMap<>();
            totalPeoplesAndIfHfMap.put("接触粉尘总人数", "ifhfDust");
            totalPeoplesAndIfHfMap.put("接触化学总人数", "ifhfChemistry");
            totalPeoplesAndIfHfMap.put("物理因素接触人数", "ifhfPhysics");
            String[] factorcrowdIndexHeader = headerDTO.getFactorcrowdIndexHeader();
            for (String name : factorcrowdIndexHeader) {
                //有接触相关危害因素时  再封装子表  否则都不封装
                Object val = Reflections.invokeGetter(unitfactorcrowd, totalPeoplesAndIfHfMap.get(name));
                if (val != null && Integer.parseInt(val.toString()) == 1) {
                    ExcelHeaderDetailDTO detailDTO = detailMap.get(name);
                    for (int i = detailDTO.getStartIndex() + 1; i <= detailDTO.getEndIndex(); i++) {
                        if(!ObjectUtil.isEmpty(contrastMap.get(5))&&contrastMap.get(5).containsKey(hearderIndexMap.get(i).replace("接触人数", ""))) {
                            String rightCode = contrastMap.get(5).get(hearderIndexMap.get(i).replace("接触人数", ""));
                            if (StringUtils.isNotBlank(rightCode) && !UnitbasicinfoUtils.ifMatcherNull(excelRowData.get(i).toString())) {
                                TdZxjcUnitfactorItem item = new TdZxjcUnitfactorItem();
                                item.setFkByMainId(unitfactorcrowd);
                                item.setCreateDate(new Date());
                                item.setCreateManid(Global.getUser().getRid());
                                item.setContactNum(Integer.parseInt(excelRowData.get(i).toString()));
                                TsSimpleCode t = simpleCodeMap.get(rightCode);
                                if (t != null) {
                                    if (t.getCodeLevelNo().contains(".")) {
                                        int n = t.getCodeLevelNo().lastIndexOf(".");
                                        String substring = t.getCodeLevelNo().substring(0, n);
                                        if (StringUtils.isNotBlank(substring) && simpleCodeMap.get(substring) != null && StringUtils.isNotBlank(simpleCodeMap.get(substring).getExtendS3())) {
                                            item.setType(Integer.parseInt(simpleCodeMap.get(substring).getExtendS3()));
                                        }
                                    }
                                    item.setFkByFactorId(t);
                                }
                                item.setFactorName(hearderIndexMap.get(i).replace("接触人数", ""));
                                itemList.add(item);
                            }
                        }
                    }
                }
            }
            //保存主表
            this.upsertEntity(unitfactorcrowd);
            //保存子表
            if (!CollectionUtils.isEmpty(itemList) && unitfactorcrowd.getRid() != null) {
                //删除子表
                delSql = new StringBuilder();
                delSql.append(" delete from TD_ZXJC_UNITFACTOR_ITEM where MAIN_ID=").append(unitfactorcrowd.getRid());
                em.createNativeQuery(delSql.toString()).executeUpdate();
                //保存子表
                this.saveBatchObjs(itemList);
            }
            /*************************在线监测-职业病危害因素检测情况************************************/
            hql = new StringBuilder();
            hql.append(" select T from  TdZxjcUnitharmChk T where T.fkByMainId.rid=").append(unitBasicInfo.getRid());
            TdZxjcUnitharmChk unitharmChk = this.findOneByHql(hql.toString(), TdZxjcUnitharmChk.class);
            if (unitharmChk == null) {
                unitharmChk = new TdZxjcUnitharmChk();
                unitharmChk.setFkByMainId(unitBasicInfo);
                unitharmChk.setModifyDate(new Date());
                unitharmChk.setModifyManid(Global.getUser().getRid());
            }
            Map<String, String> chkHeaderMap = new HashMap<>();
            chkHeaderMap.put("上一年度检测情况", "ifat");
            chkHeaderMap.put("是否检测粉尘因素", "ifatDust");
            chkHeaderMap.put("化学毒物有无检测", "ifatChemistry");
            chkHeaderMap.put("物理因素有无检测", "ifatPhysics");
            reflectionSetValWithContrast(chkHeaderMap, unitharmChk, detailMap, excelRowData, contrastMap);
            //封装子表
            List<TdZxjcUnitharmcheckSub> checkSubList = new ArrayList<>();
            chkHeaderMap.remove("上一年度检测情况");
            for (Map.Entry<String, String> entry : chkHeaderMap.entrySet()) {
                Object val = Reflections.invokeGetter(unitharmChk, entry.getValue());
                if (val != null && Integer.parseInt(val.toString()) == 1) {
                    ExcelHeaderDetailDTO detailDTO = detailMap.get(entry.getKey());
                    for (int i = detailDTO.getStartIndex() + 1; i <= detailDTO.getEndIndex(); i += 5) {
                        if(!ObjectUtil.isEmpty(contrastMap.get(5))&&contrastMap.get(5).containsKey(hearderIndexMap.get(i).replace("有无检测", ""))) {
                            String rightCode = contrastMap.get(5).get(hearderIndexMap.get(i).replace("有无检测", ""));
                            if (StringUtils.isNotBlank(rightCode) && !UnitbasicinfoUtils.ifMatcherNull(excelRowData.get(i).toString())) {
                                TdZxjcUnitharmcheckSub checkSub = new TdZxjcUnitharmcheckSub();
                                checkSub.setFkByMainId(unitharmChk);
                                checkSub.setCreateDate(new Date());
                                checkSub.setCreateManid(Global.getUser().getRid());
                                TsSimpleCode t = simpleCodeMap.get(rightCode);
                                if (t != null) {
                                    if (t.getCodeLevelNo().contains(".")) {
                                        int n = t.getCodeLevelNo().lastIndexOf(".");
                                        String substring = t.getCodeLevelNo().substring(0, n);
                                        if (StringUtils.isNotBlank(substring) && simpleCodeMap.get(substring) != null && StringUtils.isNotBlank(simpleCodeMap.get(substring).getExtendS3())) {
                                            checkSub.setType(Integer.parseInt(simpleCodeMap.get(substring).getExtendS3()));
                                        }
                                    }
                                    checkSub.setFkByFactorId(t);
                                }
                                if(!ObjectUtil.isEmpty(contrastMap.get(43))&&contrastMap.get(43).containsKey(excelRowData.get(i).toString())) {
                                    String ifCheck = contrastMap.get(43).get(excelRowData.get(i).toString());
                                    if (StringUtils.isNotBlank(ifCheck)) {
                                        checkSub.setIfatFactor(Integer.parseInt(ifCheck));
                                        for (int j = i + 1; j < i + 5; j++) {
                                            if (StringUtils.isNotBlank(hearderIndexMap.get(j))) {
                                                if ("检测点数".equals(hearderIndexMap.get(j)) && !UnitbasicinfoUtils.ifMatcherNull(excelRowData.get(j).toString())) {
                                                    checkSub.setCheckNum(Integer.parseInt(excelRowData.get(j).toString()));
                                                }
                                                if (("超标点数".equals(hearderIndexMap.get(j))||(">85db(A)点数".equals(hearderIndexMap.get(j)))) && !UnitbasicinfoUtils.ifMatcherNull(excelRowData.get(j).toString())) {
                                                    checkSub.setExcessNum(Integer.parseInt(excelRowData.get(j).toString()));
                                                }
                                                if ("检测岗位数".equals(hearderIndexMap.get(j)) && !UnitbasicinfoUtils.ifMatcherNull(excelRowData.get(j).toString())) {
                                                    checkSub.setWorkNum(Integer.parseInt(excelRowData.get(j).toString()));
                                                }
                                                if ("超标岗位数".equals(hearderIndexMap.get(j)) && !UnitbasicinfoUtils.ifMatcherNull(excelRowData.get(j).toString())) {
                                                    checkSub.setWorkExcessNum(Integer.parseInt(excelRowData.get(j).toString()));
                                                }
                                            }
                                        }
                                    }
                                }
                                checkSub.setFactorName(hearderIndexMap.get(i).replace("有无检测", ""));
                                checkSubList.add(checkSub);
                            }
                        }
                    }
                }
            }
            //保存主表
            this.upsertEntity(unitharmChk);
            //保存子表
            if (!CollectionUtils.isEmpty(checkSubList) && unitharmChk.getRid() != null) {
                //删除子表
                delSql = new StringBuilder();
                delSql.append(" delete from TD_ZXJC_UNITHARMCHECK_SUB where MAIN_ID=").append(unitharmChk.getRid());
                em.createNativeQuery(delSql.toString()).executeUpdate();
                //保存子表
                this.saveBatchObjs(checkSubList);
            }
            /*************************在线监测-职业健康检查情况************************************/
            hql = new StringBuilder();
            hql.append(" select T from  TdZxjcUnitHethCus T where T.fkByMainId.rid=").append(unitBasicInfo.getRid());
            TdZxjcUnitHethCus hethCus = this.findOneByHql(hql.toString(), TdZxjcUnitHethCus.class);
            if (hethCus == null) {
                hethCus = new TdZxjcUnitHethCus();
                hethCus.setFkByMainId(unitBasicInfo);
                hethCus.setModifyDate(new Date());
                hethCus.setModifyManid(Global.getUser().getRid());
            }
            Map<String, String> cusHeaderMap = new HashMap<>();
            cusHeaderMap.put("上一年度在岗期间职业健康检查情况", "ifhea");
            cusHeaderMap.put("粉尘因素有无体检", "ifheaDust");
            cusHeaderMap.put("化学因素有无体检", "ifheaChemistry");
            cusHeaderMap.put("物理因素有无体检", "ifheaPhysics");
            reflectionSetValWithContrast(cusHeaderMap, hethCus, detailMap, excelRowData, contrastMap);
            Map<String, String> cusNotContrastHeaderMap = new HashMap<>();
            cusNotContrastHeaderMap.put("体检总人数", "checkTotalPeoples");
            reflectionSetValNotContrast(cusNotContrastHeaderMap, hethCus, detailMap, excelRowData, contrastMap);
            //封装子表
            List<TdZxjcUnithealthItem> healthItemList = new ArrayList<>();
            cusHeaderMap.remove("上一年度在岗期间职业健康检查情况");
            for (Map.Entry<String, String> entry : cusHeaderMap.entrySet()) {
                Object val = Reflections.invokeGetter(hethCus, entry.getValue());
                if (val != null && Integer.parseInt(val.toString()) == 1) {
                    ExcelHeaderDetailDTO detailDTO = detailMap.get(entry.getKey());
                    for (int i = detailDTO.getStartIndex() + 1; i <= detailDTO.getEndIndex(); i += 5) {
                        if(!ObjectUtil.isEmpty(contrastMap.get(5))&&contrastMap.get(5).containsKey(hearderIndexMap.get(i).replace("有无体检", ""))) {
                            String rightCode = contrastMap.get(5).get(hearderIndexMap.get(i).replace("有无体检", ""));
                            if (StringUtils.isNotBlank(rightCode) && !UnitbasicinfoUtils.ifMatcherNull(excelRowData.get(i).toString())) {
                                TdZxjcUnithealthItem healthItem = new TdZxjcUnithealthItem();
                                healthItem.setFkByMainId(hethCus);
                                healthItem.setCreateDate(new Date());
                                healthItem.setCreateManid(Global.getUser().getRid());
                                TsSimpleCode t = simpleCodeMap.get(rightCode);
                                if (t != null) {
                                    if (t.getCodeLevelNo().contains(".")) {
                                        int n = t.getCodeLevelNo().lastIndexOf(".");
                                        String substring = t.getCodeLevelNo().substring(0, n);
                                        if (StringUtils.isNotBlank(substring) && simpleCodeMap.get(substring) != null && StringUtils.isNotBlank(simpleCodeMap.get(substring).getExtendS3())) {
                                            healthItem.setType(Integer.parseInt(simpleCodeMap.get(substring).getExtendS3()));
                                        }
                                    }
                                    healthItem.setFkByFactorId(t);
                                }
                                if(!ObjectUtil.isEmpty(contrastMap.get(42))&&contrastMap.get(42).containsKey(excelRowData.get(i).toString())) {
                                    String ifCheck = contrastMap.get(42).get(excelRowData.get(i).toString());
                                    if (StringUtils.isNotBlank(ifCheck)) {
                                        healthItem.setIfheaFactor(Integer.parseInt(ifCheck));
                                        for (int j = i + 1; j < i + 5; j++) {
                                            if (StringUtils.isNotBlank(hearderIndexMap.get(j))) {
                                                if ("体检人数".equals(hearderIndexMap.get(j)) && !UnitbasicinfoUtils.ifMatcherNull(excelRowData.get(j).toString())) {
                                                    healthItem.setCheckMonitorPeoples(Integer.parseInt(excelRowData.get(j).toString()));
                                                }
                                                if ("应复查人数".equals(hearderIndexMap.get(j)) && !UnitbasicinfoUtils.ifMatcherNull(excelRowData.get(j).toString())) {
                                                    healthItem.setCheckShouldPeoples(Integer.parseInt(excelRowData.get(j).toString()));
                                                }
                                                if ("实际复查人数".equals(hearderIndexMap.get(j)) && !UnitbasicinfoUtils.ifMatcherNull(excelRowData.get(j).toString())) {
                                                    healthItem.setCheckActualPeoples(Integer.parseInt(excelRowData.get(j).toString()));
                                                }
                                                if ("异常人数".equals(hearderIndexMap.get(j)) && !UnitbasicinfoUtils.ifMatcherNull(excelRowData.get(j).toString())) {
                                                    healthItem.setUnusualNum(Integer.parseInt(excelRowData.get(j).toString()));
                                                }
                                            }
                                        }
                                    }
                                }
                                healthItem.setFactorName(hearderIndexMap.get(i).replace("有无体检", ""));
                                healthItemList.add(healthItem);
                            }
                        }
                    }
                }
            }
            //保存主表
            this.upsertEntity(hethCus);
            //保存子表
            if (!CollectionUtils.isEmpty(healthItemList) && hethCus.getRid() != null) {
                //删除子表
                delSql = new StringBuilder();
                delSql.append(" delete from TD_ZXJC_UNITHEALTH_ITEM where MAIN_ID=").append(hethCus.getRid());
                em.createNativeQuery(delSql.toString()).executeUpdate();
                //保存子表
                this.saveBatchObjs(healthItemList);
            }
            /*************************在线监测-职业病防护设施设置及运行情况************************************/
            Map<String, String> nameMap = new HashMap<>();
            nameMap.put("防尘设施-设置情况", "fcssSituation");
            nameMap.put("防尘设施-防护效果", "fcssEffect");
            nameMap.put("防毒设施-设置情况", "fdssSituation");
            nameMap.put("防毒设施-防护效果", "fdssEffect");
            nameMap.put("防噪声设施-设置情况", "fzsssSituation");
            nameMap.put("防噪声设施-防护效果", "fzsssEffect");
            TdZxjcFacilitiesOperation operation = new TdZxjcFacilitiesOperation();
            operation.setFkByMainId(unitBasicInfo);
            reflectionSetValWithContrast(nameMap, operation, detailMap, excelRowData, contrastMap);
            operation.setCreateDate(new Date());
            operation.setCreateManid(Global.getUser().getRid());
            //删除 关联数据
            delSql = new StringBuilder();
            delSql.append(" delete from TD_ZXJC_FACILITIES_OPERATION where MAIN_ID=").append(unitBasicInfo.getRid());
            em.createNativeQuery(delSql.toString()).executeUpdate();
            //插入
            this.saveObj(operation);
            /*************************在线监测-职业病防护用品配备及发放情况************************************/
            Map<String, String> equimentDisMap = new HashMap<>();
            equimentDisMap.put("防尘口罩-发放情况", "fckzDistributionSituation");
            equimentDisMap.put("防尘口罩-佩戴情况", "fckzWearSituation");
            equimentDisMap.put("防毒口罩或面罩-发放情况", "fdkzDistributionSituation");
            equimentDisMap.put("防毒口罩或面罩-佩戴情况", "fdkzWearSituation");
            equimentDisMap.put("防噪声耳罩或耳塞-发放情况", "fzyesDistributionSituation");
            equimentDisMap.put("防噪声耳罩或耳塞-佩戴情况", "fzyesWearSituation");
            equimentDisMap.put("粉尘职业病危害警示标识及警示说明", "signDust");
            equimentDisMap.put("化学毒物职业病危害警示标识及警示说明", "signChemistry");
            equimentDisMap.put("噪声职业病危害警示标识及警示说明", "signPhysics");
            TdZxjcEquimentDis equimentDis = new TdZxjcEquimentDis();
            equimentDis.setFkByMainId(unitBasicInfo);
            reflectionSetValWithContrast(equimentDisMap, equimentDis, detailMap, excelRowData, contrastMap);
            equimentDis.setCreateDate(new Date());
            equimentDis.setCreateManid(Global.getUser().getRid());
            //删除 关联数据
            delSql = new StringBuilder();
            delSql.append(" delete from TD_ZXJC_EQUIMENT_DIS where MAIN_ID=").append(unitBasicInfo.getRid());
            em.createNativeQuery(delSql.toString()).executeUpdate();
            //插入
            this.saveObj(equimentDis);
        }
    }

    /**
     * <p>方法描述：反射赋值-有对照</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-09-12
     **/
    private void reflectionSetValWithContrast(Map<String, String> equimentDisMap, Object obj, Map<String, ExcelHeaderDetailDTO> detailMap, List<Object> excelRowData, Map<Integer, Map<String, String>> contrastMap) {
        for (Map.Entry<String, String> entry : equimentDisMap.entrySet()) {
            if (detailMap.containsKey(entry.getKey())) {
                String val = StringUtils.objectToString(excelRowData.get(detailMap.get(entry.getKey()).getStartIndex()));
                if (UnitbasicinfoUtils.ifMatcherNull(val)) {
                    Reflections.invokeSetter(obj, entry.getValue(), null);
                } else {
                    Integer type = UnitbasicinfoUtils.getType(entry.getKey());
                    if (type != null) {
                        if(!ObjectUtil.isEmpty(contrastMap.get(type))&&contrastMap.get(type).containsKey(val)) {
                            String contrastRight = StringUtils.objectToString(contrastMap.get(type).get(val));
                            if (ObjectUtil.isEmpty(contrastRight)) {
                                Reflections.invokeSetter(obj, entry.getValue(), null);
                            } else {
                                Reflections.invokeSetter(obj, entry.getValue(), Integer.parseInt(contrastRight));
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * <p>方法描述：反射赋值-无对照</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-09-12
     **/
    private void reflectionSetValNotContrast(Map<String, String> equimentDisMap, Object obj, Map<String, ExcelHeaderDetailDTO> detailMap, List<Object> excelRowData, Map<Integer, Map<String, String>> contrastMap) {
        for (Map.Entry<String, String> entry : equimentDisMap.entrySet()) {
            if (detailMap.containsKey(entry.getKey())) {
                String val = StringUtils.objectToString(excelRowData.get(detailMap.get(entry.getKey()).getStartIndex()));
                if (StringUtils.isNotBlank(val) && !UnitbasicinfoUtils.ifMatcherNull(val)) {
                    Reflections.invokeSetter(obj, entry.getValue(), Integer.parseInt(val));
                } else {
                    Reflections.invokeSetter(obj, entry.getValue(), null);
                }
            }
        }
    }

    /**
     * <p>方法描述： 通过uuid 查询 单位基本信息 </p>
     * @MethodAuthor： pw 2023/6/21
     **/
    @Transactional(readOnly = true)
    public Map<String,TdZxjcUnitbasicinfo> findBasicInfoByUuid(List<String> uuidList){
        if(CollectionUtils.isEmpty(uuidList)){
            return Collections.EMPTY_MAP;
        }
        Map<String,TdZxjcUnitbasicinfo> resultMap = new HashMap<>();
        String querySql = " SELECT t FROM TdZxjcUnitbasicinfo t WHERE t.uuid IN (:uuidList) ";
        List<List<String>> groupList = StringUtils.splitListProxy(uuidList, 1000);
        Map<String,Object> paramMap = new HashMap<>();
        for(List<String> tmpList : groupList){
            paramMap.put("uuidList", tmpList);
            List<TdZxjcUnitbasicinfo> resultList = this.findDataByHqlNoPage(querySql, paramMap);
            if(!CollectionUtils.isEmpty(resultList)){
                for(TdZxjcUnitbasicinfo basicInfo : resultList){
                    resultMap.put(basicInfo.getUuid(), basicInfo);
                }
            }
        }
        return resultMap;
    }

    /**
     * <p>方法描述： 通过uuid集合获取存在对应业务数据的uuid集合 </p>
     * @param uuidList
     * @param ifResult 是否查询结果
     * @param ifBase 是否修改基本信息
     * @MethodAuthor： pw 2023/6/20
     **/
    @Transactional(readOnly = true)
    public List<String> findUUidForSyncCrpt(List<String> uuidList, Boolean ifResult, boolean ifBase){
        if(CollectionUtils.isEmpty(uuidList)){
            return uuidList;
        }
        StringBuilder sqlBuffer = new StringBuilder();
        if(ifBase){
            //重新导入基本信息 校验结果与调查信息是否存在 结果与调查信息都存在 需要调用同步用人单位信息接口
            sqlBuffer.append(" SELECT DISTINCT T.UUID FROM TD_ZXJC_UNITBASICINFO T ")
                    .append(" INNER JOIN TD_ZXJC_UNITFACTORCROWD T1 ON T1.MAIN_ID = T.RID ")
                    .append(" INNER JOIN TD_ZXJC_RESULT_PRO T2 ON T2.MAIN_ID = T.RID ")
                    .append(" WHERE T.UUID IN(:uuidList) ");
        }else{
            sqlBuffer.append(" SELECT DISTINCT T1.UUID FROM ")
                    .append(null != ifResult && ifResult ? " TD_ZXJC_RESULT_PRO " : " TD_ZXJC_UNITFACTORCROWD ")
                    .append(" T LEFT JOIN TD_ZXJC_UNITBASICINFO T1 ON T.MAIN_ID = T1.RID  ")
                    .append(" WHERE T1.UUID IN (:uuidList) ");
        }
        List<List<String>> groupList = StringUtils.splitListProxy(uuidList, 1000);
        List<String> allResultList = new ArrayList<>();
        Map<String, Object> paramMap = new HashMap<>();
        String sql = sqlBuffer.toString();
        for(List<String> tmpList : groupList){
            paramMap.put("uuidList", tmpList);
            List<String> resultList = this.findDataBySqlNoPage(sql, paramMap);
            if(!CollectionUtils.isEmpty(resultList)){
                //避免重复
                allResultList.removeAll(resultList);
                allResultList.addAll(resultList);
            }
        }
        return allResultList;
    }

    /**
     * <p>方法描述： 通过uuid获取接触职业病危害因素总人数 </p>
     * @MethodAuthor： pw 2023/6/21
     **/
    @Transactional(readOnly = true)
    public Map<String, Integer> findContactTotalPeopleByUuid(List<String> uuidList){
        if(CollectionUtils.isEmpty(uuidList)){
            return Collections.EMPTY_MAP;
        }
        String querySql = "SELECT T.UUID ,T1.CONTACT_TOTAL_PEOPLES , ''  FROM TD_ZXJC_UNITBASICINFO T INNER JOIN TD_ZXJC_UNITFACTORCROWD T1 ON T.RID = T1.MAIN_ID WHERE T.UUID IN(:uuidList) ";
        List<List<String>> groupList = StringUtils.splitListProxy(uuidList, 1000);
        Map<String,Object> paramMap = new HashMap<>();
        Map<String, Integer> resultMap = new HashMap<>();
        for(List<String> tmpList : groupList){
            paramMap.put("uuidList", tmpList);
            List<Object[]> resultList = this.findDataBySqlNoPage(querySql, paramMap);
            if(!CollectionUtils.isEmpty(resultList)){
                for(Object[] objArr : resultList){
                    String uuid = null == objArr[0] ? null : objArr[0].toString();
                    Integer contactTotalPeople = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
                    if(StringUtils.isBlank(uuid) || null == contactTotalPeople){
                        continue;
                    }
                    resultMap.put(uuid, contactTotalPeople);
                }
            }
        }
        return resultMap;
    }

    /**
     * <p>方法描述：通过uuid获取接触人数大于0的危害因素 </p>
     * @MethodAuthor： pw 2023/6/21
     **/
    @Transactional(readOnly = true)
    public Map<String,List<Integer>> findFactorIdsByUuid(List<String> uuidList){
        if(CollectionUtils.isEmpty(uuidList)){
            return Collections.EMPTY_MAP;
        }
        Map<String,List<Integer>> resultMap = new HashMap<>();
        StringBuilder sqlBuffer = new StringBuilder();
        sqlBuffer.append(" SELECT DISTINCT T.UUID ,T2.FACTOR_ID , ''  FROM TD_ZXJC_UNITBASICINFO T  ")
                .append(" INNER JOIN TD_ZXJC_UNITFACTORCROWD T1 ON T.RID = T1.MAIN_ID ")
                .append(" INNER JOIN TD_ZXJC_UNITFACTOR_ITEM T2 ON T2.MAIN_ID = T1.RID ")
                .append(" WHERE T2.CONTACT_NUM > 0 AND T.UUID IN(:uuidList) ");
        String querySql = sqlBuffer.toString();
        List<List<String>> groupList = StringUtils.splitListProxy(uuidList, 1000);
        Map<String,Object> paramMap = new HashMap<>();
        for(List<String> tmpList : groupList){
            paramMap.put("uuidList", tmpList);
            List<Object[]> resultList = this.findDataBySqlNoPage(querySql, paramMap);
            if(!CollectionUtils.isEmpty(resultList)){
                for(Object[] objArr : resultList){
                    String uuid = null == objArr[0] ? null : objArr[0].toString();
                    Integer factorId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
                    if(StringUtils.isBlank(uuid) || null == factorId){
                        continue;
                    }
                    List<Integer> fillList = resultMap.get(uuid);
                    if(null == fillList){
                        fillList = new ArrayList<>();
                    }
                    if(!fillList.contains(factorId)){
                        fillList.add(factorId);
                    }
                    resultMap.put(uuid, fillList);
                }
            }
        }
        return resultMap;
    }

    /**
     * <p>方法描述： 调查信息 获取接害总人数 </p>
     * @MethodAuthor： pw 2023/6/21
     **/
    public Integer getContactTotalPeoples(ExcelHeaderDTO headerDTO, List<Object> excelRowData,
                                          Map<Integer,Map<String, String>> contrastMap)  {
        Map<String, ExcelHeaderDetailDTO> detailMap = headerDTO.getDetailMap();
        TdZxjcUnitfactorcrowd unitfactorcrowd = new TdZxjcUnitfactorcrowd();
        Map<String, String> totalPeoplesMap = new HashMap<>();
        totalPeoplesMap.put("接触危害总人数", "contactTotalPeoples");
        reflectionSetValNotContrast(totalPeoplesMap, unitfactorcrowd, detailMap, excelRowData, contrastMap);
        return unitfactorcrowd.getContactTotalPeoples();
    }

    /**
     * <p>方法描述：调查信息 获取接触人数大于0的危害因素rid集合 </p>
     * @MethodAuthor： pw 2023/6/21
     **/
    public List<Integer> getFactorIdList(ExcelHeaderDTO headerDTO, List<Object> excelRowData,
                             Map<Integer, Map<String, String>> contrastMap,
                             Map<String, TsSimpleCode> simpleCodeMap)  {
        Map<String, ExcelHeaderDetailDTO> detailMap = headerDTO.getDetailMap();
        Map<Integer, String> hearderIndexMap = headerDTO.getHearderIndexMap();
        /*************************在线监测-职业病危害因素种类及接触人数************************************/
        TdZxjcUnitfactorcrowd unitfactorcrowd = new TdZxjcUnitfactorcrowd();
        Map<String, String> tmpMap = new HashMap<>();
        tmpMap.put("有无接触粉尘", "ifhfDust");
        tmpMap.put("有无接触化学因素", "ifhfChemistry");
        tmpMap.put("有无接触物理因素", "ifhfPhysics");
        reflectionSetValWithContrast(tmpMap, unitfactorcrowd, detailMap, excelRowData, contrastMap);
        List<Integer> resultList = new ArrayList<>();
        tmpMap = new HashMap<>();
        tmpMap.put("接触粉尘总人数", "ifhfDust");
        tmpMap.put("接触化学总人数", "ifhfChemistry");
        tmpMap.put("物理因素接触人数", "ifhfPhysics");
        String[] factorcrowdIndexHeader = headerDTO.getFactorcrowdIndexHeader();
        for (String name : factorcrowdIndexHeader) {
            //有接触相关危害因素时 再获取对应的危害因素
            Object val = Reflections.invokeGetter(unitfactorcrowd, tmpMap.get(name));
            if (val != null && Integer.parseInt(val.toString()) == 1) {
                ExcelHeaderDetailDTO detailDTO = detailMap.get(name);
                for (int i = detailDTO.getStartIndex() + 1; i <= detailDTO.getEndIndex(); i++) {
                    if(!ObjectUtil.isEmpty(contrastMap.get(5))&&contrastMap.get(5).containsKey(hearderIndexMap.get(i).replace("接触人数", ""))) {
                        String rightCode = contrastMap.get(5).get(hearderIndexMap.get(i).replace("接触人数", ""));
                        if (StringUtils.isNotBlank(rightCode) && !UnitbasicinfoUtils.ifMatcherNull(excelRowData.get(i).toString())) {
                            Integer contactNum = Integer.parseInt(excelRowData.get(i).toString());
                            TsSimpleCode t = simpleCodeMap.get(rightCode);
                            //接触人数大于0时
                            if (t != null && null != contactNum && contactNum > 0) {
                                resultList.add(t.getRid());
                            }
                        }
                    }
                }
            }
        }
        return resultList;
    }
}
