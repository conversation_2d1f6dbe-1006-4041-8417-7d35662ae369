package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2018-7-6
 */
@Entity
@Table(name = "TD_ZDZYB_ANALY_PG_ITEM")
@SequenceGenerator(name = "TdZdzybAnalyPgItemComm", sequenceName = "TD_ZDZYB_ANALY_PG_ITEM_SEQ", allocationSize = 1)
public class TdZdzybAnalyPgItemComm implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZdzybAnalyItmTypeComm fkByMainId;
	private TbTjItems fkByItemId;
	private Integer jdgptn;
	private Integer hgFlag;
	private BigDecimal ge;
	private BigDecimal gt;
	private BigDecimal le;
	private BigDecimal lt;
	private Integer xh;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	private String msrunt;
	/**结果匹配值*/
	private String rstMatchVal;
    /**偏高偏低标记*/
    private Integer rstDesc;
	
	/**临时变量判断区间**/
	private BigDecimal bigValue;
	private BigDecimal smallValue;
	private BigDecimal firstValue;
	private BigDecimal secondValue;

	//多选框 是否合格
	private Integer[] hgFlags ;
	
	public TdZdzybAnalyPgItemComm() {
	}

	public TdZdzybAnalyPgItemComm(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZdzybAnalyPgItemComm")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZdzybAnalyItmTypeComm getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZdzybAnalyItmTypeComm fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_ID")			
	public TbTjItems getFkByItemId() {
		return fkByItemId;
	}

	public void setFkByItemId(TbTjItems fkByItemId) {
		this.fkByItemId = fkByItemId;
	}	
			
	@Column(name = "JDGPTN")	
	public Integer getJdgptn() {
		return jdgptn;
	}

	public void setJdgptn(Integer jdgptn) {
		this.jdgptn = jdgptn;
	}	
			
	@Column(name = "HG_FLAG")	
	public Integer getHgFlag() {
		return hgFlag;
	}

	public void setHgFlag(Integer hgFlag) {
		this.hgFlag = hgFlag;
	}	
			
	@Column(name = "GE")	
	public BigDecimal getGe() {
		return ge;
	}

	public void setGe(BigDecimal ge) {
		this.ge = ge;
	}	
			
	@Column(name = "GT")	
	public BigDecimal getGt() {
		return gt;
	}

	public void setGt(BigDecimal gt) {
		this.gt = gt;
	}	
			
	@Column(name = "LE")	
	public BigDecimal getLe() {
		return le;
	}

	public void setLe(BigDecimal le) {
		this.le = le;
	}	
			
	@Column(name = "LT")	
	public BigDecimal getLt() {
		return lt;
	}

	public void setLt(BigDecimal lt) {
		this.lt = lt;
	}	
			
	@Column(name = "XH")	
	public Integer getXh() {
		return xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
	
	@Transient
	public BigDecimal getBigValue() {
		return bigValue;
	}

	public void setBigValue(BigDecimal bigValue) {
		this.bigValue = bigValue;
	}

	@Transient
	public BigDecimal getSmallValue() {
		return smallValue;
	}

	public void setSmallValue(BigDecimal smallValue) {
		this.smallValue = smallValue;
	}

	@Transient
	public BigDecimal getFirstValue() {
		return firstValue;
	}

	public void setFirstValue(BigDecimal firstValue) {
		this.firstValue = firstValue;
	}

	@Transient
	public BigDecimal getSecondValue() {
		return secondValue;
	}

	public void setSecondValue(BigDecimal secondValue) {
		this.secondValue = secondValue;
	}
			
	@Column(name = "MSRUNT")	
	public String getMsrunt() {
		return msrunt;
	}

	public void setMsrunt(String msrunt) {
		this.msrunt = msrunt;
	}

	@Transient
	public Integer[] getHgFlags() {
		return hgFlags;
	}

	public void setHgFlags(Integer[] hgFlags) {
		this.hgFlags = hgFlags;
	}

    @Column(name = "RST_MATCH_VAL")
    public String getRstMatchVal() {
        return rstMatchVal;
    }

    public void setRstMatchVal(String rstMatchVal) {
        this.rstMatchVal = rstMatchVal;
    }

    @Column(name = "RST_DESC")
    public Integer getRstDesc() {
        return rstDesc;
    }

    public void setRstDesc(Integer rstDesc) {
        this.rstDesc = rstDesc;
    }
}