package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TdZwBgkLastSta;
import com.chis.modules.heth.comm.entity.TdZwOccdisCardBadrsn;
import com.chis.modules.heth.comm.entity.TdZwOccdisCardNew;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.math.BigDecimal;
import java.util.*;

/**
 * 职业病报告卡审核
 *
 * <AUTHOR>
 * @date 2022/5/16
 */
@ManagedBean(name = "tdZwZybReportCardReviewListBean")
@ViewScoped
public class TdZwZybReportCardReviewListBean extends AbstractReportCardBaseBean {


    /**
     * 选择的结果集
     */
    protected List<Object[]> selectEntitys;

    /**
     * 查询条件：人员姓名
     */
    private String searchPersonnelName;
    /**
     * 查询条件：证件号码
     */
    private String searchIdc;
    /**
     * 用户单位
     */
    private TsUnit userUnit;
    /**
     * 查询条件：用人单位名称
     */
    private String searchCrptName;
    /**
     * 查询条件：职业病名称
     */
    private String searchOdName;
    /**
     * 查询条件：职业病名称RID
     */
    private List<Integer> searchOdRidList;
    /**
     * 查询条件：报告日期查询-开始日期
     */
    private Date searchReportBeginDate;
    /**
     * 查询条件：报告日期查询-结束日期
     */
    private Date searchReportEndDate;
    /**
     * 查询条件：接收日期查询-开始日期
     */
    private Date searchReceiveBeginDate;
    /**
     * 查询条件：接收日期查询-结束日期
     */
    private Date searchReceiveEndDate;
    /**
     * SQL：接收日期字段名称
     */
    private String rcvDateFieldName;
    /**
     * SQL：待审核状态字段对应值
     */
    private List<Integer> realStatesListPending;
    /**
     * SQL：审核通过状态字段对应值
     */
    private List<Integer> realStatesListPassed;
    /**
     * SQL：已退回状态字段对应值
     */
    private List<Integer> realStatesListReturned;
    /********审核**********/
    /**报告卡信息*/
    private TdZwOccdisCardNew disCard;
    /**接触的职业性有害因素*/
    private String selectBadRsnNames;
    /**合并症*/
    private String hbzName;
    /********审核**********/


    public TdZwZybReportCardReviewListBean() {
        this.init();
        this.ifSQL = true;
        this.initSqlRelated();
        this.searchAction();
    }

    private void init() {
        //职业病名称选择
        this.searchOdName = "";
        this.searchOdRidList = new ArrayList<>();

        //当前页面报告卡类型
        this.cardType = 4;
    }

    public void initSqlRelated() {
        this.realStatesListPending = new ArrayList<>();
        this.realStatesListPassed = new ArrayList<>();
        this.realStatesListReturned = new ArrayList<>();
        this.rcvDateFieldName = "''";
        if (this.level == 0) {
            this.rcvDateFieldName = "BLS.COUNTY_RCV_DATE";
            this.realStatesListPending.add(1);
            this.realStatesListPassed.add(3);
            this.realStatesListPassed.add(5);
            this.realStatesListPassed.add(7);
            this.realStatesListReturned.add(2);
        } else if (this.level == 1) {
            this.rcvDateFieldName = "BLS.CITY_RCV_DATE";
            this.realStatesListPending.add(3);
            this.realStatesListPassed.add(5);
            this.realStatesListPassed.add(7);
            this.realStatesListReturned.add(4);
        } else if (this.level == 2) {
            this.rcvDateFieldName = "BLS.PRO_RCV_DATE";
            this.realStatesListPending.add(5);
            this.realStatesListPassed.add(7);
            this.realStatesListReturned.add(6);
        }
    }

    public void initOdSelect() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList;
        paramList = new ArrayList<>(1);
        //标题
        paramList.add("职业病");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>(1);
        //码表类型
        paramList.add("5026");
        paramMap.put("typeNo", paramList);
        //已选择的职业病名称
        paramList = new ArrayList<>(1);
        paramList.add(StringUtils.objectToString(StringUtils.list2string(this.searchOdRidList, ",")));
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<>();
        //查询条件是否显示大类
        paramList.add("true");
        paramMap.put("ifShowFirstCode", paramList);
        paramList = new ArrayList<>();
        //为1时,选择时只选择最末级
        paramList.add("0");
        paramMap.put("selLast", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList.xhtml", options, paramMap);
    }

    public void onOdSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> odItemList = (List<TsSimpleCode>) selectedMap.get("selectPros");
            if (CollectionUtils.isEmpty(odItemList)) {
                return;
            }
            clearOdName();
            StringBuilder sb = new StringBuilder();
            for (TsSimpleCode simpleCode : odItemList) {
                this.searchOdRidList.add(simpleCode.getRid());
                sb.append("，").append(simpleCode.getCodeName());
            }
            this.searchOdName = sb.substring(1);
        }
    }

    public void clearOdName() {
        this.searchOdName = "";
        this.searchOdRidList = new ArrayList<>();
    }

    @Override
    public String[] buildHqls() {
        StringBuilder baseSql = new StringBuilder();
        baseSql.append(" SELECT ");
        baseSql.append("    T.RID               P0, ");
        baseSql.append("    Z.FULL_NAME         P1, ");
        baseSql.append("    T.PERSONNEL_NAME    P2, ");
        baseSql.append("    T.IDC               P3, ");
        baseSql.append("    T.CRPT_NAME         P4, ");
        baseSql.append("    SC.CODE_NAME        P5, ");
        baseSql.append("    T.ZYB_DIS_NAME      P6, ");
        baseSql.append("    T.RPT_DATE          P7, ");
        baseSql.append("    T.RPT_UNIT_NAME     P8, ");
        baseSql.append(" ").append(this.rcvDateFieldName).append(" P9, ");
        baseSql.append("    ''                  P10, ");
        baseSql.append("    BLS.STATE           P11, ");
        baseSql.append("    T.DIAG_UNIT_NAME    P12 ");
        baseSql.append(" FROM TD_ZW_OCCDIS_CARD_NEW T ");
        baseSql.append("    LEFT JOIN TS_UNIT U ON T.DIAG_UNIT_ID = U.RID ");
        baseSql.append("    LEFT JOIN TS_ZONE Z ON U.ZONE_ID = Z.RID ");
        baseSql.append("    LEFT JOIN TS_SIMPLE_CODE SC ON T.ZYB_TYPE_ID = SC.RID ");
        baseSql.append("    INNER JOIN TD_ZW_BGK_LAST_STA BLS ON T.RID = BLS.BUS_ID AND BLS.CART_TYPE = 4 ");
        baseSql.append(" WHERE 1 = 1 ");
        //诊断机构地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            baseSql.append("   AND Z.ZONE_GB LIKE :zoneCode escape '\\\' ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(StringUtils.objectToString(this.searchZoneCode)).trim()) + "%");
        }
        //人员姓名
        if (StringUtils.isNotBlank(this.searchPersonnelName)) {
            baseSql.append("   AND T.PERSONNEL_NAME LIKE :personnelName escape '\\\' ");
            this.paramMap.put("personnelName", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchPersonnelName).trim()) + "%");
        }
        //证件号码
        if (StringUtils.isNotBlank(this.searchIdc)) {
            baseSql.append("   AND T.IDC = :idc ");
            super.paramMap.put("idc", StringUtils.objectToString(this.searchIdc.trim()));
        }
        //用人单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            baseSql.append("   AND T.CRPT_NAME LIKE :crptName escape '\\\' ");
            super.paramMap.put("crptName", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchCrptName).trim()) + "%");
        }
        //职业病名称
        if (!CollectionUtils.isEmpty(this.searchOdRidList)) {
            baseSql.append("   AND SC.RID IN (:odRids) ");
            super.paramMap.put("odRids", this.searchOdRidList);
        }
        //报告日期
        if (null != this.searchReportBeginDate) {
            baseSql.append("   AND T.RPT_DATE >= TO_DATE(:searchReportBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            super.paramMap.put("searchReportBeginDate", DateUtils.formatDate(this.searchReportBeginDate) + " 00:00:00");
        }
        if (null != this.searchReportEndDate) {
            baseSql.append("   AND T.RPT_DATE <= TO_DATE(:searchReportEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            super.paramMap.put("searchReportEndDate", DateUtils.formatDate(this.searchReportEndDate) + " 23:59:59");
        }
        //接收日期
        if (null != this.searchReceiveBeginDate) {
            baseSql.append("   AND ").append(this.rcvDateFieldName).append(" >= TO_DATE(:searchReceiveBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            super.paramMap.put("searchReceiveBeginDate", DateUtils.formatDate(this.searchReceiveBeginDate) + " 00:00:00");
        }
        if (null != this.searchReceiveEndDate) {
            baseSql.append("   AND ").append(this.rcvDateFieldName).append(" <= TO_DATE(:searchReceiveEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            super.paramMap.put("searchReceiveEndDate", DateUtils.formatDate(this.searchReceiveEndDate) + " 23:59:59");
        }
        //状态
        if (null != states && states.length > 0) {
            baseSql.append(" AND BLS.STATE IN (:list)");
            this.paramMap.put("list", Arrays.asList(states));
        }else{
            baseSql.append(" AND BLS.STATE IN (:list)");
            this.paramMap.put("list", Arrays.asList(allStates));
        }

        String sql1 = "SELECT * FROM (" + baseSql + ") AA ORDER BY AA.P7 DESC, P1, P12";
        String sql2 = "SELECT COUNT(1) FROM (" + baseSql + ")";
        return new String[]{sql1, sql2};
    }

    @Override
    public void processData(List<?> list) {
        if (null != list && list.size() > 0) {
            List<Object[]> result = (List<Object[]>) list;
            for (Object[] obj : result) {
                try {
                    //诊断机构地区
                    String zoneName = StringUtils.objectToString(obj[1]);
                    int start = zoneName.indexOf("_") + 1;
                    zoneName = zoneName.substring(start);
                    obj[1] = zoneName;
                    //职业病名称
                    obj[5] = StringUtils.objectToString(obj[5]);
                    //其他职业病名称
                    obj[6] = StringUtils.objectToString(obj[6]);
                    //处理期限
                    //初审-待审核
                    if (0== level && "1".equals(obj[11].toString()) && null!=obj[9]) {
                        obj[10] = calLimitTime(DateUtils.parseDate(obj[9].toString()));
                        //复审-待审核
                    }else if (1== level && "3".equals(obj[11].toString()) && null!=obj[9]) {
                        obj[10] = calLimitTime(DateUtils.parseDate(obj[9].toString()));
                        //终审-待审核
                    }else if (2== level && "5".equals(obj[11].toString()) && null!=obj[9]) {
                        obj[10] = calLimitTime(DateUtils.parseDate(obj[9].toString()));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 打开批量审核弹出框
     */
    public void openBatchReviewDialog() {
        if (this.selectEntitys == null || this.selectEntitys.size() == 0) {
            JsfUtil.addErrorMessage("请选择需要审批的数据！");
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ReviewConfirmDialog').show()");
    }

    public void reviewBatchAction() {
        List<TdZwOccdisCardNew> odCardList = new ArrayList<>();
        List<TdZwBgkLastSta> bgkLastStaList = new ArrayList<>();
        for (Object[] entity : this.selectEntitys) {
            TdZwOccdisCardNew odCard = this.cardServiceImpl.findTdZwOccdisCardNewByRid(((BigDecimal) entity[0]).intValue());
            TdZwBgkLastSta bgkLastSta = this.flowService.findTdZwBgkLastSta(((BigDecimal) entity[0]).intValue(), 4);
            if (odCard != null && bgkLastSta != null) {
                odCardList.add(odCard);
                bgkLastStaList.add(bgkLastSta);
            }
        }
        super.reviewBatch(odCardList, bgkLastStaList);
    }

    @Override
    public void addInit() {

    }


    /********************************hsj审核页面开始**************************/
    /**
     *  <p>方法描述：打开审核页面</p>
     * @MethodAuthor hsj 2022-05-17 11:29
     */
    @Override
    public void modInit() {
        super.modInit();
        this.viewInit();
    }
    @Override
    public void viewInit() {
        if (null==disCardId) {
            return;
        }
        // 职业病报告卡
        disCard = cardServiceImpl.findTdZwOccdisCardNewByRid(disCardId);
        this.zone = disCard.getFkByDiagUnitId().getTsZone();
        //接触的职业性有害因素
        initOccdisCardBadrsn();
        //接触的职业性有害因素
        initHbz();
    }
    /**
     *  <p>方法描述：接触的职业性有害因素</p>
     * @MethodAuthor hsj 2022-05-17 14:15
     */
    private void initOccdisCardBadrsn(){
        this.selectBadRsnNames = null;
        if (null!=disCard.getRid()) {
            List<TdZwOccdisCardBadrsn> list = cardServiceImpl.findEntityListByMainId(TdZwOccdisCardBadrsn.class, disCard.getRid());
            if (!CollectionUtils.isEmpty(list)) {
                StringBuffer badRsnNames = new StringBuffer();
                for (TdZwOccdisCardBadrsn t : list) {
                    badRsnNames.append("，").append(t.getFkByBadrsnId().getCodeName());
                }
                this.selectBadRsnNames = badRsnNames.substring(1);
            }
        }
    }
    /**
     *  <p>方法描述：合并症</p>
     * @MethodAuthor hsj 2022-05-17 14:29
     */
    private void initHbz(){
        this.hbzName = null;
        StringBuilder stringBuilder = new StringBuilder();
        Integer ifTb = disCard.getIfTb();
        if (null!=ifTb && ifTb.intValue()==1) {
            stringBuilder.append("，").append("肺结核");
        }
        Integer ifPulInfection = disCard.getIfPulInfection();
        if (null!=ifPulInfection && ifPulInfection.intValue()==1) {
            stringBuilder.append("，").append("肺及支气管感染");
        }
        Integer ifPulHeart = disCard.getIfPulHeart();
        if (null!=ifPulHeart && ifPulHeart.intValue()==1) {
            stringBuilder.append("，").append("自发性气胸");
        }
        Integer ifThePneum = disCard.getIfThePneum();
        if (null!=ifThePneum && ifThePneum.intValue()==1) {
            stringBuilder.append("，").append("肺心病");
        }
        Integer ifLungCancer = disCard.getIfLungCancer();
        if (null!=ifLungCancer && ifLungCancer.intValue()==1) {
            stringBuilder.append("，").append("肺癌");
        }
        if(StringUtils.isNotBlank(stringBuilder)){
            this.hbzName = stringBuilder.toString().substring(1,stringBuilder.length());
        }
    }
    /**
     *  <p>方法描述：提交审核</p>
     * @MethodAuthor hsj 2022-05-17 15:23
     */
    @Override
    public void saveAction() {
        try {
            flowService.saveOrUpdateNewFlowOrBgkFlowOrOccdiscase(disCard,newFlow,checkResult,level,limitTime);
            RequestContext.getCurrentInstance().update("tabView");
            JsfUtil.addSuccessMessage("提交成功！");
            this.backAction();
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            //提交失败只更新审核区域 防止按钮显示有问题
            RequestContext.getCurrentInstance().update("tabView:editForm:check");
            JsfUtil.addErrorMessage("提交失败！");
        }
    }
    /********************************hsj审核页面结束**************************/

    public List<Object[]> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<Object[]> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }

    public String getSearchPersonnelName() {
        return searchPersonnelName;
    }

    public void setSearchPersonnelName(String searchPersonnelName) {
        this.searchPersonnelName = searchPersonnelName;
    }

    public String getSearchIdc() {
        return searchIdc;
    }

    public void setSearchIdc(String searchIdc) {
        this.searchIdc = searchIdc;
    }

    public TsUnit getUserUnit() {
        return userUnit;
    }

    public void setUserUnit(TsUnit userUnit) {
        this.userUnit = userUnit;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchOdName() {
        return searchOdName;
    }

    public void setSearchOdName(String searchOdName) {
        this.searchOdName = searchOdName;
    }

    public List<Integer> getSearchOdRidList() {
        return searchOdRidList;
    }

    public void setSearchOdRidList(List<Integer> searchOdRidList) {
        this.searchOdRidList = searchOdRidList;
    }

    public Date getSearchReportBeginDate() {
        return searchReportBeginDate;
    }

    public void setSearchReportBeginDate(Date searchReportBeginDate) {
        this.searchReportBeginDate = searchReportBeginDate;
    }

    public Date getSearchReportEndDate() {
        return searchReportEndDate;
    }

    public void setSearchReportEndDate(Date searchReportEndDate) {
        this.searchReportEndDate = searchReportEndDate;
    }

    public Date getSearchReceiveBeginDate() {
        return searchReceiveBeginDate;
    }

    public void setSearchReceiveBeginDate(Date searchReceiveBeginDate) {
        this.searchReceiveBeginDate = searchReceiveBeginDate;
    }

    public Date getSearchReceiveEndDate() {
        return searchReceiveEndDate;
    }

    public void setSearchReceiveEndDate(Date searchReceiveEndDate) {
        this.searchReceiveEndDate = searchReceiveEndDate;
    }


    public String getRcvDateFieldName() {
        return rcvDateFieldName;
    }

    public void setRcvDateFieldName(String rcvDateFieldName) {
        this.rcvDateFieldName = rcvDateFieldName;
    }

    public List<Integer> getRealStatesListPending() {
        return realStatesListPending;
    }

    public void setRealStatesListPending(List<Integer> realStatesListPending) {
        this.realStatesListPending = realStatesListPending;
    }

    public List<Integer> getRealStatesListPassed() {
        return realStatesListPassed;
    }

    public void setRealStatesListPassed(List<Integer> realStatesListPassed) {
        this.realStatesListPassed = realStatesListPassed;
    }

    public List<Integer> getRealStatesListReturned() {
        return realStatesListReturned;
    }

    public void setRealStatesListReturned(List<Integer> realStatesListReturned) {
        this.realStatesListReturned = realStatesListReturned;
    }


    public TdZwOccdisCardNew getDisCard() {
        return disCard;
    }

    public void setDisCard(TdZwOccdisCardNew disCard) {
        this.disCard = disCard;
    }


    public String getSelectBadRsnNames() {
        return selectBadRsnNames;
    }

    public void setSelectBadRsnNames(String selectBadRsnNames) {
        this.selectBadRsnNames = selectBadRsnNames;
    }

    public String getHbzName() {
        return hbzName;
    }

    public void setHbzName(String hbzName) {
        this.hbzName = hbzName;
    }



}
