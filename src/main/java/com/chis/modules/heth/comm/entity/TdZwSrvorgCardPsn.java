package com.chis.modules.heth.comm.entity;


import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-8-19
 */
@Entity
@Table(name = "TD_ZW_SRVORG_CARD_PSN")
@SequenceGenerator(name = "TdZwSrvorgCardPsn", sequenceName = "TD_ZW_SRVORG_CARD_PSN_SEQ", allocationSize = 1)
public class TdZwSrvorgCardPsn implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwSrvorgCard fkByMainId;
	private TdZwPsninfoComm fkByPsnId;
	private String psnName;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private List<TdZwSrvorgCardItem> tdZwSrvorgCardItemList;
	//承担的服务事项
	private String[] srvorgCardItemIds;
	private String srvorgCardItemNames;

	public TdZwSrvorgCardPsn() {
	}

	public TdZwSrvorgCardPsn(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSrvorgCardPsn")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwSrvorgCard getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwSrvorgCard fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "PSN_ID")			
	public TdZwPsninfoComm getFkByPsnId() {
		return fkByPsnId;
	}

	public void setFkByPsnId(TdZwPsninfoComm fkByPsnId) {
		this.fkByPsnId = fkByPsnId;
	}	
			
	@Column(name = "PSN_NAME")	
	public String getPsnName() {
		return psnName;
	}

	public void setPsnName(String psnName) {
		this.psnName = psnName;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwSrvorgCardItem> getTdZwSrvorgCardItemList() {
		return tdZwSrvorgCardItemList;
	}

	public void setTdZwSrvorgCardItemList(List<TdZwSrvorgCardItem> tdZwSrvorgCardItemList) {
		this.tdZwSrvorgCardItemList = tdZwSrvorgCardItemList;
	}

	@Transient
	public String[] getSrvorgCardItemIds() {
		return srvorgCardItemIds;
	}

	public void setSrvorgCardItemIds(String[] srvorgCardItemIds) {
		this.srvorgCardItemIds = srvorgCardItemIds;
	}
	@Transient
	public String getSrvorgCardItemNames() {
		return srvorgCardItemNames;
	}

	public void setSrvorgCardItemNames(String srvorgCardItemNames) {
		this.srvorgCardItemNames = srvorgCardItemNames;
	}
}