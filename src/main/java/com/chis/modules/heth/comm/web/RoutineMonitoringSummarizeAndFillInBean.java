package com.chis.modules.heth.comm.web;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.service.RoutineMonitoringSummarizeService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.*;

@ManagedBean(name = "routineMonitoringSummarizeAndFillInBean")
@ViewScoped
public class RoutineMonitoringSummarizeAndFillInBean extends FacesEditBean {
    private static final long serialVersionUID = 3516701821969646527L;
    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final RoutineMonitoringSummarizeService summarizeService =
            SpringContextHolder.getBean(RoutineMonitoringSummarizeService.class);
    /**
     * 当前体检机构RID
     */
    private Integer bhkOrgRid;
    /**
     * 主表RID
     */
    private Integer rid;
    /**
     * 主表
     */
    private TdZwMonthBhk monthBhk;

    /**
     * 查询条件: 报告出具日期开始日期
     */
    private Date searchRptSDate;
    /**
     * 查询条件: 报告出具日期结束日期
     */
    private Date searchRptEDate;
    /**
     * 查询条件: 用人单位名称
     */
    private String searchCrptName;
    /**
     * 查询条件: 状态
     */
    private String[] searchState;
    /**
     * 5547 统计危害因素
     */
    private List<TsSimpleCode> analyBadRsnList;
    private Map<Integer, TsSimpleCode> analyBadRsnMap;
    private List<SelectItem> selYearList;
    private List<SelectItem> selMonthList;
    /******************************编辑页********************************/
    /**
     * 编辑页查询条件: 用人单位名称
     */
    private String searchECrptName;
    /**
     * 编辑页查询条件: 状态
     */
    private String[] searchEState;
    /**
     * 用人单位信息
     */
    private List<Object[]> dataTableList;

    /**
     * 选中的用人单位信息
     */
    private Object[] selCrpt;
    private Object[] selCrptTemp;

    /**
     * 危害因素汇总
     */
    private List<Object[]> monthBadrsnList;

    private String redFont="<br><font style='color: red;'>注：</font>";

    /**导出文件*/
    private StreamedContent downloadFile;

    /**是否详情页面*/
    private boolean ifView=false;

    /**导出数据*/
    List<Object[]> dataList=new ArrayList<>();
    /******************************编辑页********************************/
    // 当前体检机构
    private TbTjSrvorg tjSrvorg;

    public RoutineMonitoringSummarizeAndFillInBean() {
        init();
    }

    public void searchAction() {
        if (verifyBhkOrg()) {
            JsfUtil.addErrorMessage("当前单位未注册体检机构！");
            return;
        }
        super.searchAction();
    }

    public boolean verifyBhkOrg() {
        if (this.bhkOrgRid != null) {
            return false;
        }
        try {
            this.bhkOrgRid = this.summarizeService.findBhkOrgRidByUnitRid(Global.getUser().getTsUnit().getRid());
            if (null != this.bhkOrgRid) {
                this.tjSrvorg = this.commService.find(TbTjSrvorg.class, this.bhkOrgRid);
            }
        } catch (Exception e) {
            e.printStackTrace();
            this.bhkOrgRid = null;
        }
        return this.bhkOrgRid == null;
    }

    public void init() {
        this.ifSQL = true;
        this.searchRptSDate = DateUtils.getFixedDate(-1, 11, 16);
        // 状态
        this.searchState = new String[]{"0"};
        initSimpleCode();
        this.monthBhk = new TdZwMonthBhk();
        // 添加-年选择 近5年
        this.selYearList = new ArrayList<>();
        int nowYear = DateUtil.year(new Date());
        for (int i = 0; i < 5; i++) {
            int year = nowYear - i;
            this.selYearList.add(new SelectItem(year, year + "年"));
        }
        this.selMonthList = new ArrayList<>();
        this.searchAction();
    }

    private void initSimpleCode() {
        // 5547 统计危害因素 (扩展字段2 为空的)
        this.analyBadRsnList = new ArrayList<>();
        this.analyBadRsnMap = new HashMap<>();
        List<TsSimpleCode> analyBadRsns = this.commService.findSimpleCodeListOrderByNumNo("5547");
        if(CollectionUtils.isEmpty(analyBadRsns)){
            return;
        }
        for (TsSimpleCode simpleCode : analyBadRsns) {
            if(ObjectUtil.isNull(simpleCode.getExtendS2())){
                this.analyBadRsnList.add(simpleCode);
                this.analyBadRsnMap.put(simpleCode.getRid(), simpleCode);

            }
        }
    }

    @Override
    public String[] buildHqls() {
        String sql = "SELECT " +
                "   B.RID              AS P0, " +
                "   B.START_BHK_DATE   AS P1, " +
                "   B.END_BHK_DATE     AS P2, " +
                "   B.CRPT_NUM         AS P3, " +
                "   B.CRPT_SUBMIT_NUM  AS P4, " +
                "   B.FILL_DATE        AS P5, " +
                "   B.MODIFY_DATE      AS P6, " +
                "   B.STATE            AS P7 ";
        String baseSql = buildQueryCriteria(this.paramMap);
        sql += baseSql + " ORDER BY B.START_BHK_DATE DESC ";
        String countSql = "SELECT COUNT(1) " + baseSql;
        return new String[]{sql, countSql};
    }

    private String buildQueryCriteria(Map<String, Object> paramMap) {
        StringBuilder baseSql = new StringBuilder();
        baseSql.append(" FROM TD_ZW_MONTH_BHK B WHERE ");
        if (this.bhkOrgRid == null) {
            baseSql.append(" 1 <> 1 ");
        } else {
            baseSql.append(" B.ORG_ID = :bhkOrgRid ");
            paramMap.put("bhkOrgRid", this.bhkOrgRid);
        }
        baseSql.append(" AND NVL(B.DEL_MARK, 0) = 0 ");
        // 报告出具日期开始日期
        if (ObjectUtil.isNotEmpty(this.searchRptSDate)) {
            Date searchDate;
            if (DateUtil.dayOfMonth(this.searchRptSDate) > 15) {
                searchDate = DateUtil.offsetMonth(this.searchRptSDate, 1);
            } else {
                searchDate = this.searchRptSDate;
            }
            searchDate = DateUtil.beginOfMonth(searchDate);
            baseSql.append(" AND B.END_BHK_DATE >= TO_DATE(:searchRptSDate, 'yyyy-MM-dd') ");
            paramMap.put("searchRptSDate", DateUtils.formatDate(searchDate, "yyyy-MM-dd"));
        }
        // 报告出具日期结束日期
        if (ObjectUtil.isNotEmpty(this.searchRptEDate)) {
            Date searchDate;
            if (DateUtil.dayOfMonth(this.searchRptEDate) > 15) {
                searchDate = DateUtil.offsetMonth(this.searchRptEDate, 1);
            } else {
                searchDate = this.searchRptEDate;
            }
            searchDate = DateUtil.endOfMonth(searchDate);
            baseSql.append(" AND B.END_BHK_DATE <= TO_DATE(:searchRptEDate, 'yyyy-MM-dd') ");
            paramMap.put("searchRptEDate", DateUtils.formatDate(searchDate, "yyyy-MM-dd"));
        }
        // 用人单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            baseSql.append(" AND EXISTS(SELECT 1 FROM TD_ZW_MONTH_CRPT MC ");
            baseSql.append("            LEFT JOIN TB_TJ_CRPT C ON MC.CRPT_ID = C.RID ");
            baseSql.append("        WHERE B.RID = MC.MAIN_ID AND C.CRPT_NAME LIKE :searchCrptName escape '\\\') ");
            paramMap.put("searchCrptName", "%" + this.searchCrptName.trim() + "%");
        }
        // 状态
        if (ObjectUtil.isNotEmpty(this.searchState)) {
            List<String> list = new ArrayList<>();
            for (String s : this.searchState) {
                list.add(s);
                if ("1".equals(s)) {
                    list.add("2");
                }
            }
            baseSql.append(" AND B.STATE IN (:searchState) ");
            paramMap.put("searchState", list);
        } else {
            baseSql.append(" AND B.STATE IN ('0', '1', '2') ");
        }
        return baseSql.toString();
    }

    public void delAction() {
        try {
            this.summarizeService.delTdZwMonthBhk(this.rid);
            JsfUtil.addSuccessMessage("删除成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * 添加-添加前处理
     */
    public void preAddAction() {
        if (verifyBhkOrg()) {
            JsfUtil.addErrorMessage("当前单位未注册体检机构！");
            return;
        }
        this.monthBhk = new TdZwMonthBhk();
        this.monthBhk.setFkByOrgId(new TbTjSrvorg(this.bhkOrgRid));
        this.monthBhk.setFillDate(new Date());
        this.monthBhk.setState(0);
        this.monthBhk.setCrptSubmitNum(0);
        this.monthBhk.setDelMark(0);
        // 年份默认当年
        this.monthBhk.setSelYear(DateUtil.year(new Date()));
        onAddDialogYearChange();
        RequestContext.getCurrentInstance().update("tabView:mainForm:addGrid");
        RequestContext.getCurrentInstance().execute("PF('AddDialog').show();");
    }

    /**
     * 添加-年change事件
     */
    public void onAddDialogYearChange() {
        this.selMonthList = new ArrayList<>();
        Date date = new Date();
        Integer year = DateUtil.year(date);
        // 往年可选12个月
        int maxMonth = 12;
        if (year.equals(this.monthBhk.getSelYear())) {
            int month = DateUtil.month(date) + 1;
            // 当年且当天大于15号可选1月-当月 否则可选1月-上月，若1月则不可选
            if (DateUtil.dayOfMonth(date) > 15) {
                maxMonth = month;
            } else {
                maxMonth = month - 1;
            }
        }
        for (int i = 1; i < maxMonth + 1; i++) {
            this.selMonthList.add(new SelectItem(i, i + "月"));
        }
        this.monthBhk.setSelMonth(null);
        onAddDialogMonthChange();
    }

    /**
     * 添加-月份change事件
     */
    public void onAddDialogMonthChange() {
        if (this.monthBhk.getSelMonth() == null) {
            this.monthBhk.setStartBhkDate(null);
            this.monthBhk.setEndBhkDate(null);
            return;
        }
        int year = this.monthBhk.getSelYear();
        int month = this.monthBhk.getSelMonth();
        String startDateStr;
        if (month == 1) {
            startDateStr = (year - 1) + "-12-16";
        } else {
            startDateStr = year + "-" + (month - 1) + "-16";
        }
        String endDateStr = year + "-" + month + "-15";
        this.monthBhk.setStartBhkDate(DateUtil.parse(startDateStr, "yyyy-MM-dd"));
        this.monthBhk.setEndBhkDate(DateUtil.parse(endDateStr, "yyyy-MM-dd"));
    }

    @Override
    public void addInit() {
        this.ifView=false;
        if (this.monthBhk.getSelYear() == null) {
            JsfUtil.addErrorMessage("请选择报告出具周期年份！");
            RequestContext.getCurrentInstance().execute("zwx_loading_stop();");
            return;
        }
        if (this.monthBhk.getSelMonth() == null) {
            JsfUtil.addErrorMessage("请选择报告出具周期月份！");
            RequestContext.getCurrentInstance().execute("zwx_loading_stop();");
            return;
        }
        if (this.summarizeService.findMonthBhkCountByEndDate(this.bhkOrgRid, this.monthBhk.getEndBhkDate()) > 0) {
            JsfUtil.addErrorMessage("相同报告出具日期已填报，不可重复填报！");
            RequestContext.getCurrentInstance().execute("zwx_loading_stop();");
            return;
        }
        // 检测是否报告出具周期数据已经被管理机构汇总
        String srvorgZoneGb = null == this.tjSrvorg || null == this.tjSrvorg.getTsZone() ? null :
                this.tjSrvorg.getTsZone().getZoneGb();
        Integer collectNum = this.summarizeService.checkIfCollectByManageOrg(srvorgZoneGb,
                this.monthBhk.getStartBhkDate(),
                this.monthBhk.getEndBhkDate());
        if (null == collectNum) {
            JsfUtil.addErrorMessage("添加失败！");
            RequestContext.getCurrentInstance().execute("zwx_loading_stop();");
            return;
        }
        if (collectNum > 0) {
            JsfUtil.addErrorMessage("该报告出具周期数据已被管理机构汇总，无法添加！");
            RequestContext.getCurrentInstance().execute("zwx_loading_stop();");
            return;
        }
        List<Integer> bhkEntrustCrptList = this.summarizeService.findBhkEntrustCrptByBhkOrgCycle(
                this.bhkOrgRid, this.monthBhk.getStartBhkDate(), this.monthBhk.getEndBhkDate(), null
        );
        if (ObjectUtil.isEmpty(bhkEntrustCrptList)) {
            JsfUtil.addErrorMessage("无体检数据，无需填报！");
            RequestContext.getCurrentInstance().execute("zwx_loading_stop();");
            return;
        }
        try {
            this.monthBhk.setCrptNum(bhkEntrustCrptList.size());
            this.monthBhk.setModifyDate(new Date());
            this.summarizeService.upsertEntity(this.monthBhk);
            this.rid = this.monthBhk.getRid();
            this.monthBhk = this.summarizeService.find(TdZwMonthBhk.class, this.rid);
            RequestContext.getCurrentInstance().execute("PF('AddDialog').hide();");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("添加失败！");
            RequestContext.getCurrentInstance().execute("zwx_loading_stop();");
            return;
        }
        List<TdZwMonthCrpt> needMonthCrptList = new ArrayList<>();
        for (Integer rid : bhkEntrustCrptList) {
            needMonthCrptList.add(pakNewCrpt(this.monthBhk, rid));
        }
        initAllSummarizeValue(needMonthCrptList);
        this.summarizeService.saveAllSummarizeData(
                this.analyBadRsnMap, this.monthBhk, needMonthCrptList, new ArrayList<Integer>()
        );
        modInitAction();
        RequestContext.getCurrentInstance().update("tabView");
        RequestContext.getCurrentInstance().execute("zwx_loading_stop();");
    }

    /**
     * 封装新增的用人单位
     *
     * @param bhk    填报
     * @param crptId 用人单位id
     * @return TdZwMonthCrpt 用人单位
     */
    private TdZwMonthCrpt pakNewCrpt(TdZwMonthBhk bhk, Integer crptId) {
        TdZwMonthCrpt crpt = new TdZwMonthCrpt();
        crpt.setFkByMainId(bhk);
        crpt.setFkByCrptId(new TbTjCrpt(crptId));
        crpt.setState(0);
        return crpt;
    }

    /**
     * 进入编辑页面前操作
     */
    public void preModAction(boolean flag) {
        this.monthBhk = this.summarizeService.find(TdZwMonthBhk.class, this.rid);
        if(flag){
            initAllSummarizeValue();
        }
        modInitAction();
        if (flag) {
            RequestContext.getCurrentInstance().execute("zwx_loading_stop();");
        }
    }

    /**
     * 用人单位&汇总数据初始化
     */
    private void initAllSummarizeValue() {
        // 需要汇总的用人单位列表 包括未填报的和新增的
        List<TdZwMonthCrpt> needFillMonthCrptList = new ArrayList<>();
        // 需要删除的用人单位列表
        List<Integer> needDelMonthCrptList = new ArrayList<>();
        // 查询已存储用人单位
        List<TdZwMonthCrpt> monthCrptList = this.summarizeService.findCrptByBhkRid(this.rid);
        // 已有存储人单位的用工单位id
        List<Integer> alreadyExistsCrptIdList = new ArrayList<>();
        // 已存储且未填报用人单位rid key: 用工单位id value: 用人单位rid
        Map<Integer, Integer> alreadyExistsNotFilledCrptMap = new HashMap<>();
        for (TdZwMonthCrpt crpt : monthCrptList) {
            if (crpt.getFkByCrptId() == null || crpt.getFkByCrptId().getRid() == null) {
                continue;
            }
            alreadyExistsCrptIdList.add(crpt.getFkByCrptId().getRid());
            // 未填报的用人单位
            if (new Integer(0).equals(crpt.getState())) {
                alreadyExistsNotFilledCrptMap.put(crpt.getFkByCrptId().getRid(), crpt.getRid());
                needFillMonthCrptList.add(crpt);
            }
        }
        // 查询当前体检机构所有个案在报告出具日期范围内、监测类型为“常规监测”的用工单位
        List<Integer> bhkEntrustCrptList = this.summarizeService.findBhkEntrustCrptByBhkOrgCycle(
                this.bhkOrgRid, this.monthBhk.getStartBhkDate(), this.monthBhk.getEndBhkDate(), null
        );
        // 取已存储用工单位集合与最新用工单位集合差集
        List<Integer> differenceSetCrptIdList =
                (List<Integer>) CollUtil.disjunction(alreadyExistsCrptIdList, bhkEntrustCrptList);
        for (Integer crptId : differenceSetCrptIdList) {
            // 若是已存储用工单位集合中的并且未填报 需要删除
            if (alreadyExistsCrptIdList.contains(crptId) && alreadyExistsNotFilledCrptMap.containsKey(crptId)) {
                needDelMonthCrptList.add(alreadyExistsNotFilledCrptMap.get(crptId));
                continue;
            }
            // 若是最新用工单位集合中的 需要新增
            if (bhkEntrustCrptList.contains(crptId)) {
                needFillMonthCrptList.add(pakNewCrpt(new TdZwMonthBhk(this.rid), crptId));
            }
        }
        initAllSummarizeValue(needFillMonthCrptList);
        this.summarizeService.saveAllSummarizeData(this.analyBadRsnMap, this.monthBhk, needFillMonthCrptList, needDelMonthCrptList);
    }

    /**
     * 汇总需要汇总的用人单位，删除无数据的未填报用人单位
     *
     * @param needFillMonthCrptList 需要汇总的用人单位
     */
    public void initAllSummarizeValue(List<TdZwMonthCrpt> needFillMonthCrptList) {
        Map<Integer, Map<Integer, Object[]>> summarizeSrcDataMap = pakSummarizeSrcDataMap(null);
        for (TdZwMonthCrpt crpt : needFillMonthCrptList) {
            initSummarizeValue(crpt, summarizeSrcDataMap.get(crpt.getFkByCrptId().getRid()));
        }
    }

    /**
     * 重新汇总前操作
     */
    public void beforeReInitSummarizeValue() {
        if (this.selCrpt == null || this.selCrpt[3] == null) {
            return;
        }
        Integer crptId = ObjectUtil.convert(Integer.class, this.selCrpt[3]);
        // 查询当前体检机构所有个案在报告出具日期范围内、监测类型为“常规监测”的用工单位
        List<Integer> bhkEntrustCrptList = this.summarizeService.findBhkEntrustCrptByBhkOrgCycle(
                this.bhkOrgRid, this.monthBhk.getStartBhkDate(), this.monthBhk.getEndBhkDate(), crptId
        );
        if (ObjectUtil.isNotEmpty(bhkEntrustCrptList)) {
            reInitSummarizeValue();
            this.verifyNull();
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ReReSummarizeConfirmDialog').show();");
    }

    /**
     * 重新汇总
     */
    public void reInitSummarizeValue() {
        if (this.selCrpt == null || this.selCrpt[3] == null) {
            return;
        }
        Integer crptId = ObjectUtil.convert(Integer.class, this.selCrpt[3]);
        Map<Integer, Map<Integer, Object[]>> summarizeSrcDataMap = pakSummarizeSrcDataMap(crptId);
        TdZwMonthCrpt crpt = new TdZwMonthCrpt();
        crpt.setFkByCrptId(new TbTjCrpt(crptId));
        initSummarizeValue(crpt, summarizeSrcDataMap.get(crpt.getFkByCrptId().getRid()));
        Map<Integer, TdZwMonthBadrsns> badrsnsMap = new HashMap<>();
        for (TdZwMonthBadrsns badrsns : crpt.getBadrsnsList()) {
            badrsnsMap.put(badrsns.getFkByBadrsnId().getRid(), badrsns);
        }
        //初始化各危害因素table列表
        updateMonthBadTable(badrsnsMap);
    }

    /**
    * <p>Description： 初始化各危害因素table列表</p>
    * <p>Author： yzz 2024-08-13 </p>
    */
    public void updateMonthBadTable(Map<Integer, TdZwMonthBadrsns> badrsnsMap){
        if(badrsnsMap.isEmpty()){
            return;
        }
        //更新汇总数据
        for (Object[] obj : monthBadrsnList) {
            Integer badrsnId =Integer.parseInt(obj[9].toString());
            if(badrsnsMap.containsKey(badrsnId)){
                TdZwMonthBadrsns monthBadrsns = badrsnsMap.get(badrsnId);
                obj[2] = (monthBadrsns.getSysBhkNum() != null && new Integer("0").equals(monthBadrsns.getSysBhkNum()) ? 0 : obj[2]);
                obj[3]=monthBadrsns.getBhkNum();
                obj[4]=monthBadrsns.getSuspectedNum();
                obj[5]=monthBadrsns.getContraindlistNum();
                obj[6]=monthBadrsns.getSysBhkNum();
                obj[7]=monthBadrsns.getSysSuspectedNum();
                obj[8]=monthBadrsns.getSysContraindlistNum();
            }
        }
        RequestContext.getCurrentInstance().update("tabView:editForm:monthBadrsnTable");
    }

    /**
     * 重新汇总-删除用人单位
     */
    public void removeCrpt() {
        if (this.selCrpt == null || this.selCrpt[0] == null) {
            return;
        }
        Integer crptRid = ObjectUtil.convert(Integer.class, this.selCrpt[0]);
        try {
            this.summarizeService.delCrptAndUpdateCrptNumByRid(this.rid, crptRid);
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
            return;
        }
        searchCrptAction();
        RequestContext.getCurrentInstance().execute("PF('ReReSummarizeConfirmDialog').hide();");
    }

    /**
     * 封装汇总原始数据
     *
     * @param crptId 用人单位ID
     */
    private Map<Integer, Map<Integer, Object[]>> pakSummarizeSrcDataMap(Integer crptId) {
        Map<Integer, Map<Integer, Object[]>> dataMap = new HashMap<>();
        List<Object[]> dataList = this.summarizeService.findSummarizeDataByCrptIdAndBhkOrgCycle(
                this.bhkOrgRid, crptId, this.monthBhk.getStartBhkDate(), this.monthBhk.getEndBhkDate()
        );
        for (Object[] data : dataList) {
            Integer crptIdData = ObjectUtil.convert(Integer.class, data[0]);
            Integer badrsnId = ObjectUtil.convert(Integer.class, data[1]);
            if (crptIdData == null || badrsnId == null) {
                continue;
            }
            if (!dataMap.containsKey(crptIdData)) {
                dataMap.put(crptIdData, new HashMap<Integer, Object[]>());
            }
            dataMap.get(crptIdData).put(badrsnId, data);
        }
        return dataMap;
    }

    /**
     * 用人单位的汇总数据初始化
     */
    private void initSummarizeValue(TdZwMonthCrpt crpt, Map<Integer, Object[]> badrsnDateMap) {
        Map<Integer, TdZwMonthBadrsns> badrsnsMap = new HashMap<>();
        if (ObjectUtil.isEmpty(crpt.getBadrsnsList())) {
            crpt.setBadrsnsList(new ArrayList<TdZwMonthBadrsns>());
        } else {
            for (TdZwMonthBadrsns badrsns : crpt.getBadrsnsList()) {
                badrsnsMap.put(badrsns.getFkByBadrsnId().getRid(), badrsns);
            }
        }
        for (TsSimpleCode simpleCode : this.analyBadRsnList) {
            Integer badrsnId = simpleCode.getRid();
            Integer psnNum = 0;
            Integer yszybNum = 0;
            Integer zyjjzNum = 0;
            if (ObjectUtil.isNotEmpty(badrsnDateMap) && badrsnDateMap.containsKey(badrsnId)) {
                Object[] data = badrsnDateMap.get(badrsnId);
                psnNum = ObjectUtil.convert(Integer.class, data[2], 0);
                yszybNum = ObjectUtil.convert(Integer.class, data[3], 0);
                zyjjzNum = ObjectUtil.convert(Integer.class, data[4], 0);
            }
            TdZwMonthBadrsns badrsns = badrsnsMap.get(badrsnId);
            if (badrsns == null) {
                crpt.getBadrsnsList().add(pakTdZwMonthBadrsns(crpt, badrsnId, psnNum, yszybNum, zyjjzNum));
            } else {
                pakTdZwMonthBadrsns(badrsns, psnNum, yszybNum, zyjjzNum);
            }
        }
    }

    /**
     * 封装用人单位重点危害因素统计人数
     *
     * @param crpt     用人单位
     * @param badrsnId 统计危害因素ID
     * @param psnNum   实检人数
     * @param yszybNum 疑似职业病人数
     * @param zyjjzNum 职业禁忌症人数
     * @return TdZwMonthBadrsns 重点危害因素统计人数
     */
    private TdZwMonthBadrsns pakTdZwMonthBadrsns(TdZwMonthCrpt crpt, Integer badrsnId,
                                                 Integer psnNum, Integer yszybNum, Integer zyjjzNum) {
        TdZwMonthBadrsns badrsns = new TdZwMonthBadrsns();
        badrsns.setFkByMainId(crpt);
        badrsns.setFkByBadrsnId(new TsSimpleCode(badrsnId));
        pakTdZwMonthBadrsns(badrsns, psnNum, yszybNum, zyjjzNum);
        return badrsns;
    }

    /**
     * 封装用人单位重点危害因素统计人数
     *
     * @param psnNum   实检人数
     * @param yszybNum 疑似职业病人数
     * @param zyjjzNum 职业禁忌症人数
     */
    private void pakTdZwMonthBadrsns(TdZwMonthBadrsns badrsns, Integer psnNum, Integer yszybNum, Integer zyjjzNum) {
        if (new Integer(0).equals(psnNum)) {
            badrsns.setHoldCardNum(0);
        }
        badrsns.setBhkNum(psnNum);
        badrsns.setSysBhkNum(psnNum);
        badrsns.setSuspectedNum(yszybNum);
        badrsns.setSysSuspectedNum(yszybNum);
        badrsns.setContraindlistNum(zyjjzNum);
        badrsns.setSysContraindlistNum(zyjjzNum);
        this.summarizeService.preEntity(badrsns);
    }

    @Override
    public void modInit() {
        this.dataTableList=new ArrayList<>();
        this.monthBadrsnList=new ArrayList<>();
        this.rid=(this.rid==null?this.monthBhk.getRid():this.rid);
        this.selCrpt=null;
        //状态初始化
        this.searchECrptName=null;
        this.searchEState=null;
        //查询关联的用人单位列表
        dataTableList = summarizeService.findMonthCrptByMainId(this.rid,this.searchECrptName,this.searchEState);
        if(CollectionUtils.isEmpty(dataTableList)){
            return;
        }
        //默认选择第一条用人单位
        selCrpt=dataTableList.get(0);
        selCrptTemp=selCrpt;
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:dataTable");
        dataTable.setSelection(selCrpt);
        dataTable.setFirst(0);
        dataTable.setRows(10);

        this.monthBadrsnList = summarizeService.findBadrsnByMainId(Integer.parseInt(this.selCrpt[0].toString()));
        this.verifyNull();
        RequestContext.getCurrentInstance().update("tabView:editForm:dataTable");
        RequestContext.getCurrentInstance().update("tabView:editForm:monthBadrsnTable");
        RequestContext.getCurrentInstance().update("tabView:editForm:badrsnPanel");
    }

    @Override
    public void saveAction() {
    }

    @Override
    public void viewInit() {
    }

    /**
    * <p>Description：编辑页查询 </p>
    * <p>Author： yzz 2024-08-09 </p>
    */
    public void searchCrptAction() {
        // 查询关联的用人单位列表
        dataTableList = summarizeService.findMonthCrptByMainId(this.rid, this.searchECrptName, this.searchEState);
        if (CollectionUtils.isEmpty(dataTableList)) {
            this.selCrpt = null;
        } else {
            boolean flag = false;
            if (!CollectionUtils.isEmpty(dataTableList)) {
                for (Object[] obj : dataTableList) {
                    if (this.selCrpt != null && obj[0].toString().equals(this.selCrpt[0].toString())) {
                        flag = true;
                        break;
                    }
                }
            }
            // 默认选择第一条用人单位
            this.selCrpt = flag ? this.selCrpt : dataTableList.get(0);
            selCrptTemp=selCrpt;
            this.monthBadrsnList = summarizeService.findBadrsnByMainId(Integer.parseInt(this.selCrpt[0].toString()));
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("tabView:editForm:dataTable");
            dataTable.setSelection(selCrpt);
            this.verifyNull();
        }
        RequestContext.getCurrentInstance().update("tabView:editForm:dataTable");
        RequestContext.getCurrentInstance().update("tabView:editForm:monthBadrsnTable");
        RequestContext.getCurrentInstance().update("tabView:editForm:badrsnPanel");
    }

    /**
    * <p>Description：编辑页面大提交前验证 </p>
    * <p>Author： yzz 2024-08-08 </p>
    */
    public void beforeSubmitAction(){
        if(this.rid==null){
            return;
        }
        List<Object[]> list = summarizeService.findMonthCrptByMainId(this.rid, null, null);
        if(CollectionUtils.isEmpty(list)){
            this.verifyNull();
            JsfUtil.addErrorMessage("用人单位为空，无法提交！");
            return;
        }
         list = summarizeService.findMonthCrptByMainId(this.rid, null, new String[]{"0"});
        if(!CollectionUtils.isEmpty(list)){
            this.verifyNull();
            JsfUtil.addErrorMessage("存在未填报的记录，请核实！");
            return;
        }
        //验证当前选中单位 并保存
        if (verifySave(true)) {
            return ;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show();");
    }

    /**
    * <p>Description：提交 </p>
    * <p>Author： yzz 2024-08-12 </p>
    */
    public void submitAction(){
        //验证是否所有未填报的用人单位
        try {
            summarizeService.submitMonthBhk(this.rid,monthBadrsnList, Integer.parseInt(this.selCrpt[0].toString()), this.monthBhk.getRid(), true);
            this.searchECrptName=null;
            this.searchEState=null;
            this.monthBhk = this.summarizeService.find(TdZwMonthBhk.class, this.rid);
            JsfUtil.addSuccessMessage("提交成功！");
            this.ifView=true;
            preModAction(false);
            this.searchAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
    * <p>Description：撤销前验证 </p>
    * <p>Author： yzz 2024-08-13 </p>
    */
    public void beforeCancleAction(){
        TdZwMonthBhk monthBhk = this.summarizeService.find(TdZwMonthBhk.class, this.rid);
        if(new Integer("2").equals(monthBhk.getState())){
            JsfUtil.addErrorMessage("已汇总，无法撤销！");
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('CancleDialog').show();");
    }

    /**
    * <p>Description：撤销 </p>
    * <p>Author： yzz 2024-08-12 </p>
    */
    public void cancleAction(){
        try {
            //更新主表状态
            summarizeService.updateMainState(this.rid, 0);
            this.searchECrptName=null;
            this.searchEState=null;
            this.ifView=false;
            preModAction(false);
            JsfUtil.addSuccessMessage("撤销成功！");
            this.searchAction();
            RequestContext.getCurrentInstance().update("tabView");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
    }

    /**
    * <p>Description：导出前验证 </p>
    * <p>Author： yzz 2024-08-13 </p>
    */
    public void exportBefore() {
        this.dataList=summarizeService.findExportDataList(this.rid,this.searchECrptName,this.searchEState);
        if (CollectionUtils.isEmpty(this.dataList)) {
            JsfUtil.addErrorMessage("无可导出的数据！");
            return;
        }
        RequestContext.getCurrentInstance().execute("getDownloadFileClick();");
    }


    /**
    * <p>Description：导出 </p>
    * <p>Author： yzz 2024-08-08 </p>
    */
    public StreamedContent getDownloadFile() {
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("showStatus();");
        String[] excelTitle;
        ExcelExportUtil excelExportUtil;
        List<int[]> mergeCellsList = new ArrayList<>();
        Short zoneType = this.monthBhk.getFkByOrgId().getTsZone().getZoneType();
        String fullName = this.monthBhk.getFkByOrgId().getTsZone().getFullName();
        String zoneName;
        if (zoneType.intValue()>2) {
            int start = fullName.indexOf("_")+1;
            zoneName = fullName.substring(start).replace("_", "");
        }else {
            zoneName = fullName;
        }
        excelTitle = new String[]{ "职业健康检查机构职业健康指标常规监测汇总表（"+DateUtils.formatDate(this.monthBhk.getStartBhkDate())+"~"+DateUtils.formatDate(this.monthBhk.getEndBhkDate())+"）","地区：" + zoneName+ "         职业健康检查机构名称：" + this.monthBhk.getFkByOrgId().getUnitName()};

        String[] excelHeaders = new String[]{"用人单位名称", "危害因素", "接触职业病危害因素劳动者（人）", "当年实际接受职业健康检查劳动者（人）", "检出的疑似职业病（人）", "职业禁忌证（人）"}
        ;
        // 添加列头合并单元格
        excelExportUtil = new ExcelExportUtil(excelTitle, excelHeaders, pakExcelExportDataList());
        excelExportUtil.setSheetName("职业健康检查机构职业健康指标常规监测汇总表");
        excelExportUtil.setFrozenPaneRowsNum(3);
        excelExportUtil.setColumnHeight(30);
        excelExportUtil.setAdaptiveHeight(true);
        excelExportUtil.setColumnWidths(new Integer[]{40, 15, 15, 15, 15, 15});
        excelExportUtil.setMultiTitleAlignStyle(new Short[]{2,1});
        excelExportUtil.setMultiTitleFontBoldStyle(new boolean[]{true,false});
        for (int[] mergeCells : mergeCellsList) {
            excelExportUtil.addMergeCells(mergeCells[0], mergeCells[1], mergeCells[2], mergeCells[3]);
        }
        Workbook wb = excelExportUtil.exportExcel("");
        if (wb != null) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                String fileName = excelTitle[0] + ".xlsx";
                fileName = URLEncoder.encode(fileName, "UTF-8");
                wb.write(baos);
                baos.flush();
                byte[] aa = baos.toByteArray();
                context.execute("hideStatus();");
                return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
            } catch (Exception e) {
                JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            }
        }
        context.execute("hideStatus();");
        return null;
    }

    /**
    * <p>Description：数据填充 </p>
    * <p>Author： yzz 2024-08-08 </p>
    */
    private List<ExcelExportObject[]> pakExcelExportDataList() {
        List<ExcelExportObject[]> excelExportObjectList = new ArrayList<>();
        for (int i = 0, dataListSize = dataList.size(); i < dataListSize; i++) {
            Object[] data = dataList.get(i);
            ExcelExportObject[] objects = new ExcelExportObject[6];
            for (int k = 0; k < 6; k++) {
                // 第一第二列居左
                objects[k] = (0 == k) ? new ExcelExportObject(StringUtils.objectToString(data[k])) :
                        new ExcelExportObject(StringUtils.objectToString(data[k]), XSSFCellStyle.ALIGN_CENTER);
            }
            excelExportObjectList.add(objects);
        }
        return excelExportObjectList;
    }

    /**
    * <p>Description：用人单位行选中事件 </p>
    * <p>Author： yzz 2024-08-09 </p>
    */
    public void onRowSelect(SelectEvent event) {
        //保存上一条数据 只验证逻辑  不验证必填
        if(!this.ifView && saveBadrsnAction(selCrptTemp!=null && "1".equals(selCrptTemp[2].toString()),false)){
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("tabView:editForm:dataTable");
            dataTable.setSelection(selCrptTemp);
            return;
        }
        selCrpt = (Object[]) event.getObject();
        selCrptTemp=selCrpt;
        if(selCrpt==null || selCrpt[0]==null){
            return;
        }
        // 初始化右侧危害因素统计信息
        this.monthBadrsnList = summarizeService.findBadrsnByMainId(Integer.parseInt(this.selCrpt[0].toString()));
        //验证 为空的标红
        this.verifyNull();
    }

    /**
    * <p>Description： 验证当前单位，为空标红</p>
    * <p>Author： yzz 2024-09-12 </p>
    */
    public void verifyNull() {
        if (CollectionUtils.isEmpty(this.monthBadrsnList)) {
            return;
        }
        String id = "tabView\\\\:editForm\\\\:monthBadrsnTable\\\\:";
        StringBuffer errorIds = new StringBuffer();
        for (int i = 0; i < monthBadrsnList.size(); i++) {
            Object[] obj = monthBadrsnList.get(i);
            // 接触职业病危害因素劳动者（人）
            if (obj[2] == null || StringUtils.isBlank(obj[2].toString())) {
                errorIds.append(",").append(id).append(i).append("\\\\:").append("psnNum");
            }
            // 当年实际接受职业健康检查劳动者（人）
            if (obj[3] == null || StringUtils.isBlank(obj[3].toString())) {
                errorIds.append(",").append(id).append(i).append("\\\\:").append("actualPsnNum");
            }
            // 检出的疑似职业病（人）
            if (obj[4] == null || StringUtils.isBlank(obj[4].toString())) {
                errorIds.append(",").append(id).append(i).append("\\\\:").append("occPsnNum");
            }
            // 职业禁忌证（人）
            if (obj[5] == null || StringUtils.isBlank(obj[5].toString())) {
                errorIds.append(",").append(id).append(i).append("\\\\:").append("conPsnNum");
            }
        }
        if (errorIds.length() > 0) {
            RequestContext.getCurrentInstance().execute("markErrorInfo('" + errorIds.substring(1) + "')");
        }
    }


    /**
    * <p>Description：保存各危害因素汇总数据  ifRequired:是否验证必填 ifTip:是否需要提示保存成功</p>
    * <p>Author： yzz 2024-08-09 </p>
    */
    public boolean saveBadrsnAction(boolean ifRequired,boolean ifTip) {
        // 验证
        if (CollectionUtils.isEmpty(this.monthBadrsnList)) {
            return false;
        }
        if (verifySave(ifRequired)) {
            return true;
        }
        try {
            summarizeService.saveMonthBadrsn(monthBadrsnList, Integer.parseInt(this.selCrpt[0].toString()), this.monthBhk.getRid(), ifTip);
            if (!ifTip) {
                return false;
            }
            //设置当前选中单位的状态为已填报
            selCrptTemp[2] = 1;
            searchCrptAction();
            searchAction();
            JsfUtil.addSuccessMessage("保存成功！");
        } catch (Exception e) {
            selCrptTemp[2]=0;
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
        return false;
    }
    
    
    /**
    * <p>Description：验证 </p>
    * <p>Author： yzz 2024-09-12 </p>
    */
    private boolean verifySave(boolean ifRequired) {
        String id = "tabView\\\\:editForm\\\\:monthBadrsnTable\\\\:";
        boolean flag = false;
        for (int i = 0; i < this.monthBadrsnList.size(); i++) {
            Object[] obj=monthBadrsnList.get(i);
            // 接触职业病危害因素劳动者（人）
            if (ifRequired && (obj[2] == null || StringUtils.isBlank(obj[2].toString()))) {
                Global.markErrorInfo(true, id+i+"\\\\:psnNum","危害因素【" + obj[1] + "】的接触职业病危害因素劳动者（人）不能为空！");
                flag = true;
            }
            // 当年实际接受职业健康检查劳动者（人）
            if (ifRequired && (obj[3] == null || StringUtils.isBlank(obj[3].toString()))) {
                Global.markErrorInfo(true, id+i+"\\\\:actualPsnNum","危害因素【" + obj[1] + "】的当年实际接受职业健康检查劳动者（人）不能为空！");
                flag = true;
            } else if (!"".equals(obj[3]) && "0".equals(obj[3].toString()) && obj[2] != null && StringUtils.isNotBlank(obj[2].toString()) && Integer.parseInt(obj[2].toString()) > 0) {
                Global.markErrorInfo(true, id+i+"\\\\:psnNum","危害因素【" + obj[1] + "】的当年实际接受职业健康检查劳动者（人）为0时，接触职业病危害因素劳动者（人）必须为0！");
                flag = true;
            } else if (!"".equals(obj[3]) && Integer.parseInt(obj[3].toString()) > 0 && obj[6] != null && StringUtils.isNotBlank(obj[6].toString()) && Integer.parseInt(obj[3].toString()) < Integer.parseInt(obj[6].toString())) {
                Global.markErrorInfo(true, id+i+"\\\\:actualPsnNum","危害因素【" + obj[1] + "】的当年实际接受职业健康检查劳动者（人）必须大于等于系统自动汇总数（" + obj[6] + "）！");
                flag = true;
            } else if (!"".equals(obj[3]) && Integer.parseInt(obj[3].toString()) > 0 && obj[2] != null && StringUtils.isNotBlank(obj[2].toString()) && Integer.parseInt(obj[3].toString()) > Integer.parseInt(obj[2].toString())) {
                Global.markErrorInfo(true, id+i+"\\\\:actualPsnNum","危害因素【" + obj[1] + "】的当年实际接受职业健康检查劳动者（人）必须小于等于接触职业病危害因素劳动者（人）！");
                Global.markErrorInfo(false, id+i+"\\\\:psnNum",null);
                flag = true;
            }
            // 检出的疑似职业病（人）
            if (ifRequired && (obj[4] == null || StringUtils.isBlank(obj[4].toString()))) {
                Global.markErrorInfo(true, id+i+"\\\\:occPsnNum","危害因素【" + obj[1] + "】的检出的疑似职业病（人）不能为空！");
                flag = true;
            } else if (!"".equals(obj[4]) && Integer.parseInt(obj[4].toString()) > 0 && obj[7] != null && StringUtils.isNotBlank(obj[7].toString()) && Integer.parseInt(obj[4].toString()) < Integer.parseInt(obj[7].toString())) {
                Global.markErrorInfo(true, id+i+"\\\\:occPsnNum","危害因素【" + obj[1] + "】的检出的疑似职业病（人）必须大于等于系统自动汇总数（" + obj[7] + "）！");
                flag = true;
            } else if (!"".equals(obj[4]) && Integer.parseInt(obj[4].toString()) > 0 && obj[3] != null && StringUtils.isNotBlank(obj[3].toString()) && Integer.parseInt(obj[4].toString()) > Integer.parseInt(obj[3].toString())) {
                Global.markErrorInfo(true, id+i+"\\\\:occPsnNum","危害因素【" + obj[1] + "】的检出的疑似职业病（人）必须小于当年实际接受职业健康检查劳动者（人）！");
                Global.markErrorInfo(false, id+i+"\\\\:actualPsnNum",null);
                flag = true;
            }
            // 职业禁忌证（人）
            if (ifRequired && (obj[5] == null || StringUtils.isBlank(obj[5].toString()))) {
                Global.markErrorInfo(true, id+i+"\\\\:conPsnNum","危害因素【" + obj[1] + "】的职业禁忌证（人）不能为空！");
                flag = true;
            } else if (!"".equals(obj[5]) && Integer.parseInt(obj[5].toString()) > 0 && obj[8] != null && StringUtils.isNotBlank(obj[8].toString()) && Integer.parseInt(obj[5].toString()) < Integer.parseInt(obj[8].toString())) {
                Global.markErrorInfo(true, id+i+"\\\\:conPsnNum","危害因素【" + obj[1] + "】的职业禁忌证（人）必须大于等于系统自动汇总数（" + obj[7] + "）！");
                flag = true;
            } else if (!"".equals(obj[5]) && Integer.parseInt(obj[5].toString()) > 0 && obj[3] != null && StringUtils.isNotBlank(obj[3].toString()) && Integer.parseInt(obj[5].toString()) > Integer.parseInt(obj[3].toString())) {
                Global.markErrorInfo(true, id+i+"\\\\:conPsnNum","危害因素【" + obj[1] + "】的职业禁忌证（人）必须小于当年实际接受职业健康检查劳动者（人）！");
                Global.markErrorInfo(false, id+i+"\\\\:actualPsnNum",null);
                flag = true;
            }
        }
        return flag;
    }


    public void setDownloadFile(StreamedContent downloadFile) {
        this.downloadFile = downloadFile;
    }
    public Integer getBhkOrgRid() {
        return bhkOrgRid;
    }

    public void setBhkOrgRid(Integer bhkOrgRid) {
        this.bhkOrgRid = bhkOrgRid;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwMonthBhk getMonthBhk() {
        return monthBhk;
    }

    public void setMonthBhk(TdZwMonthBhk monthBhk) {
        this.monthBhk = monthBhk;
    }

    public Date getSearchRptSDate() {
        return searchRptSDate;
    }

    public void setSearchRptSDate(Date searchRptSDate) {
        this.searchRptSDate = searchRptSDate;
    }

    public Date getSearchRptEDate() {
        return searchRptEDate;
    }

    public void setSearchRptEDate(Date searchRptEDate) {
        this.searchRptEDate = searchRptEDate;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String[] getSearchState() {
        return searchState;
    }

    public void setSearchState(String[] searchState) {
        this.searchState = searchState;
    }

    public List<TsSimpleCode> getAnalyBadRsnList() {
        return analyBadRsnList;
    }

    public void setAnalyBadRsnList(List<TsSimpleCode> analyBadRsnList) {
        this.analyBadRsnList = analyBadRsnList;
    }

    public Map<Integer, TsSimpleCode> getAnalyBadRsnMap() {
        return analyBadRsnMap;
    }

    public void setAnalyBadRsnMap(Map<Integer, TsSimpleCode> analyBadRsnMap) {
        this.analyBadRsnMap = analyBadRsnMap;
    }

    public List<SelectItem> getSelYearList() {
        return selYearList;
    }

    public void setSelYearList(List<SelectItem> selYearList) {
        this.selYearList = selYearList;
    }

    public List<SelectItem> getSelMonthList() {
        return selMonthList;
    }

    public void setSelMonthList(List<SelectItem> selMonthList) {
        this.selMonthList = selMonthList;
    }
    public String getSearchECrptName() {
        return searchECrptName;
    }
    public void setSearchECrptName(String searchECrptName) {
        this.searchECrptName = searchECrptName;
    }
    public String[] getSearchEState() {
        return searchEState;
    }
    public void setSearchEState(String[] searchEState) {
        this.searchEState = searchEState;
    }
    public List<Object[]> getDataTableList() {
        return dataTableList;
    }
    public void setDataTableList(List<Object[]> dataTableList) {
        this.dataTableList = dataTableList;
    }

    public Object[] getSelCrpt() {
        return selCrpt;
    }
    public void setSelCrpt(Object[] selCrpt) {
        this.selCrpt = selCrpt;
    }

    public List<Object[]> getMonthBadrsnList() {
        return monthBadrsnList;
    }
    public void setMonthBadrsnList(List<Object[]> monthBadrsnList) {
        this.monthBadrsnList = monthBadrsnList;
    }
    public String getRedFont() {
        return redFont;
    }
    public void setRedFont(String redFont) {
        this.redFont = redFont;
    }

    public boolean getIfView() {
        return ifView;
    }
    public void setIfView(boolean ifView) {
        this.ifView = ifView;
    }

    public Object[] getSelCrptTemp() {
        return selCrptTemp;
    }
    public void setSelCrptTemp(Object[] selCrptTemp) {
        this.selCrptTemp = selCrptTemp;
    }
}
