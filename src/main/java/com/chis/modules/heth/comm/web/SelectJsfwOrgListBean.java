package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.ToggleSelectEvent;
import org.primefaces.event.UnselectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>描述：技术服务机构单位选择弹出框</p>
 *
 *  @Author: 龚哲,2022/1/5 9:48,SelectJsfwOrgListBean
 */
@ManagedBean(name = "selectJsfwOrgListBean")
@ViewScoped
public class SelectJsfwOrgListBean extends FacesBean {

    private static final long serialVersionUID = -738445454536625063L;

    /** 名称 或 拼音码*/
    private String searchName;
    /** 查询列集合*/
    private List<Object[]> displayList;
    /** 所有集合*/
    private List<Object[]> allList;
    /**选中的集合*/
    private List<Object[]> selectedList;
    /**所有选中的集合*/
    private List<Object[]> allSelectedList;

    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
    /** 查询条件-地区*/
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
    /**
     * <p>描述：初始化数据</p>
     * @param
     *
     * @return {@link null}
     * @Author: 龚哲,2022/1/5 14:54,SelectYrOrgListBean
     */
    public SelectJsfwOrgListBean() {
    	String selectIds = JsfUtil.getRequest().getParameter("selectIds");//已选择的码表rid，以逗号隔开
		String zoneCode = JsfUtil.getRequest().getParameter("searchZoneCode");//已选择的码表rid，以逗号隔开
		this.initZone(zoneCode);
		this.init(selectIds,this.searchZoneCode);
    }
    /**
     * <p>描述：查询所有用人单位</p>
     * @param selectIds
     * @param zoneCode
     *
     * @return
     * @Author: 龚哲,2022/1/5 14:54,init
     */
    private void init(String selectIds,String zoneCode)   {
		this.displayList = new ArrayList<>();
		this.allList = new ArrayList<>();
		List<Object[]> list = new ArrayList<>();
		if(StringUtils.isNotBlank(zoneCode)){
			StringBuilder sb = new StringBuilder("SELECT T.RID,T.UNITNAME,T1.ZONE_GB,T1.ZONE_TYPE,T1.FULL_NAME,T1.ZONE_NAME ");
			sb.append(" FROM TS_UNIT T LEFT JOIN TS_ZONE T1 ON T1.RID = T.ZONE_ID WHERE 1=1 ");
			sb.append(" AND T.IF_REVEAL = 1 AND T1.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zoneCode)).append("%'");
			sb.append(" AND( EXISTS(SELECT 1 FROM TD_ZW_SRVORGINFO T2 WHERE T2.ORG_ID = T.RID AND T2.STATE = 1)");
			sb.append(" OR EXISTS(SELECT 1 FROM TD_ZW_TJORGINFO T3 WHERE T3.ORG_ID = T.RID AND T3.STATE = 1)");
			sb.append(" OR EXISTS(SELECT 1 FROM TD_ZW_DIAGORGINFO T4 WHERE T4.ORG_ID = T.RID AND T4.STATE = 1)");
			sb.append(" OR EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_INFO T5 WHERE T5.ORG_ID = T.RID AND T5.STATE = 1) )");
			sb.append(" ORDER BY T1.ZONE_GB ,T.UNITNAME");
			list = commService.findDataBySqlNoPage(sb.toString(),null);
		}
		selectedList = new ArrayList<>();
		allSelectedList = new ArrayList<>();
		int row = -1;// 第一次出现勾选的当前页的第一行数
		if (null != list && list.size() > 0) {
			int  i = 0;
        	for (Object[] obj : list) {
				if (StringUtils.isNotBlank(selectIds)
						&& selectIds.contains(","+StringUtils.objectToString(obj[0])+",")) {
					if (row == -1) {
						row = i - i % 10;
					}
					selectedList.add(obj);
				}
        		i++;
			}
        	this.allList.addAll(list);
        	this.displayList.addAll(list);
        	this.allSelectedList.addAll(selectedList);
		}
		 //初始化选择当前页的第一行数
        if (row>-1) {
        	DataTable dataTable = (DataTable) FacesContext
        			.getCurrentInstance().getViewRoot()
        			.findComponent("codeForm:selectedTable");
        	dataTable.setFirst(row);
        }	
    }
    /**
     * <p>描述：初始化地区</p>
     * @param zoneCode
     *
     * @return
     * @Author: 龚哲,2022/1/5 14:55,initZone
     */
    private void initZone(String zoneCode) {
		if (StringUtils.isBlank(zoneCode)) {
			zoneCode = Global.getUser().getTsUnit().getFkByManagedZoneId().getZoneGb();
		}
		this.zoneList = this.commService.findZoneListByGbAndType(zoneCode, true, "", "");
		this.searchZoneCode = this.zoneList.get(0).getZoneGb();
		this.searchZoneName = this.zoneList.get(0).getZoneName();
	}
    /**
     * <p>描述：查询用人单位</p>
     * @param
     *
     * @return
     * @Author: 龚哲,2022/1/5 12:36,searchAction
     */
    public void searchAction() {
        this.displayList = new ArrayList<>();
        if( null != allList && allList.size() > 0)    {
    		if(StringUtils.isNotBlank(searchName))    {
    			for(Object[] t :allList)   {
    				//单位名称
    				String unitName = StringUtils.objectToString(t[1]);
    				//如果模糊匹配上，则增加
    				if (unitName.indexOf(searchName) != -1) {
    					//地区
    					String zoneGb = StringUtils.objectToString(t[2]);
    					if (zoneGb.startsWith(ZoneUtil.zoneSelect(this.searchZoneCode))) {
    						this.displayList.add(t);
						}
    				}
    			}
    		}else{
    			for(Object[] t :allList)   {
    				String zoneGb = StringUtils.objectToString(t[2]);
					if (zoneGb.startsWith(ZoneUtil.zoneSelect(this.searchZoneCode))) {
						this.displayList.add(t);
					}
    			}
    		}
    	}
        //选中的单位
		selectedList.clear();
		selectedList.addAll(allSelectedList);
		DataTable dataTable = (DataTable) FacesContext
				.getCurrentInstance().getViewRoot()
				.findComponent("codeForm:selectedTable");
		dataTable.setFirst(0);
    }
    /**
     * <p>描述：确定</p>
     * @param
     *
     * @return
     * @Author: 龚哲,2022/1/5 14:55,submitAction
     */
    public void submitAction() {
    	if (null!=allList && allList.size()>0) {
			if(CollectionUtils.isEmpty(allSelectedList)){
				JsfUtil.addErrorMessage("请选择数据！");
				return;
			}else if (allSelectedList.size()>1000) {
				JsfUtil.addErrorMessage("最多可选择1000条数据！");
				return;
			}
    		Map<String, List<Object[]>> map = new HashMap<>();
    		map.put("selectPros", allSelectedList);
    		RequestContext.getCurrentInstance().closeDialog(map);
		}
	}

	/**
	 *
	 * <p>描述：行选中监听，添加元素</p>
	 *
	 *  @Author: 龚哲,2021/11/30 10:23,rowSelectListener
	 */
	public void rowSelectListener(SelectEvent event){
		Object[] unit = (Object[])event.getObject();
		allSelectedList.add(unit);
	}
	/**
	 *
	 * <p>描述：行取消，移除元素</p>
	 *
	 *  @Author: 龚哲,2021/11/30 10:23,rowUnselectListener
	 */
	public void rowUnselectListener(UnselectEvent event){
		Object[] unit = (Object[])event.getObject();
		allSelectedList.remove(unit);
	}
	/**
	 *
	 * <p>描述：全选或取消全选</p>
	 *
	 *  @Author: 龚哲,2021/11/30 10:23,toggleSelectListener
	 */
	public void toggleSelectListener(ToggleSelectEvent event){
		DataTable dataTable = (DataTable) FacesContext
				.getCurrentInstance().getViewRoot()
				.findComponent("codeForm:selectedTable");
		if(displayList.size()==0){//表格无数据
			return;
		}
		if(event.isSelected()){
			List<Object[]> list = (List)dataTable.getSelection();//当前datatable选中的项
			allSelectedList.addAll(list);
		}else{//取消全选，需要移除当前页的所有元素
			int current = dataTable.getPage();//获取当前页码
			int rows = dataTable.getRows();//每页条数
			//遍历当前表格数据，将当前页的数据移除
			int curFirst = current * rows;//当前页第一个元素下标
			int curLast = Math.min(curFirst + rows - 1, displayList.size() - 1);//当前页最后一个元素的下标（取相对小的）
			for (int i = curFirst; i <= curLast; i++) {
				allSelectedList.remove(displayList.get(i));
			}
		}
	}

    /**
     * <p>描述：关闭</p>
     * @param
     *
     * @return
     * @Author: 龚哲,2022/1/5 14:55,dialogClose
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }
	public String getSearchName() {
		return searchName;
	}
	public List<Object[]> getDisplayList() {
		return displayList;
	}
	public void setSearchName(String searchName) {
		this.searchName = searchName;
	}
	public void setDisplayList(List<Object[]> displayList) {
		this.displayList = displayList;
	}
	public List<TsZone> getZoneList() {
		return zoneList;
	}
	public String getSearchZoneCode() {
		return searchZoneCode;
	}
	public String getSearchZoneName() {
		return searchZoneName;
	}
	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}
	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}
	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}

	public List<Object[]> getSelectedList() {
		return selectedList;
	}

	public void setSelectedList(List<Object[]> selectedList) {
		this.selectedList = selectedList;
	}
}
