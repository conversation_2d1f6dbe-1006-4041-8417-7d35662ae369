package com.chis.modules.heth.comm.service;

import java.util.*;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbTjCrptCheck;
import com.chis.modules.heth.comm.entity.TbTjCrptInvest;
import com.chis.modules.heth.comm.entity.TbTjCrptInvstMining;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;

/**
 * <p>类描述：企业调查</p>
 * @ClassAuthor qrr,2021年3月29日,TbTjCrptInvestServiceImpl
 * */
@Service
@Transactional(readOnly = true)
public class TbTjCrptInvestServiceImpl extends AbstractTemplate {

    @Transactional(readOnly = false)
    public void saveTbTjCrptInvest(TbTjCrptInvest t,List<TsSimpleCode> selectMinings) {
    	if (null==t.getFkByCrptSizeId()||null==t.getFkByCrptSizeId().getRid()) {
			t.setFkByCrptSizeId(null);
		}
    	if (null==t.getFkByEconomyId()||null==t.getFkByEconomyId().getRid()) {
			t.setFkByEconomyId(null);
		}
    	if (null==t.getFkByIndusTypeId()||null==t.getFkByIndusTypeId().getRid()) {
			t.setFkByIndusTypeId(null);
		}
    	if (null==t.getFkByNoProduceStateId()||null==t.getFkByNoProduceStateId().getRid()) {
			t.setFkByNoProduceStateId(null);
		}
    	Integer stateMark = t.getStateMark();
    	if (1==stateMark||4==stateMark) {//提交或地区变更清空审核信息
    		t.setBackRsn(null);//退回原因清空
    		t.setCheckDate(null);//审核日期清空
    		t.setFkByCheckUnitId(null);//审核单位清空
		}
    	if (null!=t.getRid()) {
			this.em.createNativeQuery("delete from TB_TJ_CRPT_INVST_MINING where main_id ="+t.getRid()).executeUpdate();
		}
    	this.upsertEntity(t);
    	if (!CollectionUtils.isEmpty(selectMinings)) {
			List<TbTjCrptInvstMining> list = new ArrayList<>();
			for (TsSimpleCode code : selectMinings) {
				TbTjCrptInvstMining mining = new TbTjCrptInvstMining();
				mining.setFkByMainId(t);
				mining.setFkByMiningId(code);
				preInsert(mining);
				list.add(mining);
			}
			this.saveBatchObjs(list);
		}
    	if (null==t.getFkByCrptSizeId()) {
			t.setFkByCrptSizeId(new TsSimpleCode());
		}
    	if (null==t.getFkByEconomyId()) {
			t.setFkByEconomyId(new TsSimpleCode());
		}
    	if (null==t.getFkByIndusTypeId()) {
			t.setFkByIndusTypeId(new TsSimpleCode());
		}
    	if (null==t.getFkByNoProduceStateId()) {
			t.setFkByNoProduceStateId(new TsSimpleCode());
		}
    }
    /**
 	 * <p>方法描述：批量更新调查信息</p>
 	 * @MethodAuthor qrr,2021年3月30日,updateTbTjCrptInvest
     * */
    @Transactional(readOnly = false)
    public void updateTbTjCrptInvest(String ids,TbTjCrptInvest t,String minigIds,List<TsSimpleCode> selectMinings) {
    	if (null!=t.getExistState() && 0 == t.getExistState()) {//不存在
    		t.setProduceState(null);
    		t.setFkByNoProduceStateId(null);
    		t.setOtherProduceState(null);
    	}
    	Integer produceState = t.getProduceState();
    	if (null!=produceState && 1==produceState) {//正常生产
    		t.setFkByNoProduceStateId(null);
    		t.setOtherProduceState(null);
		}
    	TsSimpleCode produceStateId = t.getFkByNoProduceStateId();
    	if (null==produceStateId || null==produceStateId.getRid()) {
    		t.setOtherProduceState(null);
		}else {
			if (!"1".equals(produceStateId.getExtendS1())) {
				t.setOtherProduceState(null);
			}
		}
    	StringBuffer sql = new StringBuffer();
    	sql.append(" update TB_TJ_CRPT_INVEST set FILL_UNIT_ID =").append(Global.getUser().getTsUnit().getRid());
    	sql.append(",INVEST_DATE =sysdate");
    	sql.append(",STATE_MARK=1");
    	sql.append(",EXIST_STATE=").append(t.getExistState());
    	sql.append(",PRODUCE_STATE=:PRODUCE_STATE");
    	sql.append(",NO_PRODUCE_STATE_ID=:NO_PRODUCE_STATE_ID");
    	sql.append(",OTHER_PRODUCE_STATE=:OTHER_PRODUCE_STATE");
    	sql.append(",MODIFY_DATE=sysdate");
    	sql.append(",MODIFY_MANID=").append(Global.getUser().getRid());
    	sql.append(",IF_HAS_URANIUM = null");
    	sql.append(" WHERE RID IN (").append(ids).append(")");
    	Map<String, Object> map = new HashMap<String, Object>();
    	map.put("PRODUCE_STATE", null!=t.getProduceState()?t.getProduceState():"");
    	if (null!=t.getFkByNoProduceStateId() && null!=t.getFkByNoProduceStateId().getRid()) {
    		map.put("NO_PRODUCE_STATE_ID", t.getFkByNoProduceStateId().getRid());
    	}else {
    		map.put("NO_PRODUCE_STATE_ID", "");
		}
    	map.put("OTHER_PRODUCE_STATE", StringUtils.isNotBlank(t.getOtherProduceState())?t.getOtherProduceState():"");
    	this.executeSql(sql.toString(), map);
    	//匹配更新是否含铀
    	if (StringUtils.isNotBlank(minigIds)) {
    		this.em.createNativeQuery("delete from TB_TJ_CRPT_INVST_MINING where main_id in ("+minigIds+")").executeUpdate();
    		if (null!=t.getExistState() && 1 == t.getExistState()) {//存在
    			sql = new StringBuffer();
    			sql.append(" update TB_TJ_CRPT_INVEST set IF_HAS_URANIUM =").append(t.getIfHasUranium());
    			sql.append(" WHERE RID IN (").append(minigIds).append(")");
    			this.em.createNativeQuery(sql.toString()).executeUpdate();
    			
    			if (!CollectionUtils.isEmpty(selectMinings)) {
    				String[] split = minigIds.split(",");
    				for (String s : split) {
    					List<TbTjCrptInvstMining> list = new ArrayList<>();
    					for (TsSimpleCode code : selectMinings) {
    						TbTjCrptInvstMining mining = new TbTjCrptInvstMining();
    						mining.setFkByMainId(new TbTjCrptInvest(Integer.valueOf(s)));
    						mining.setFkByMiningId(code);
    						preInsert(mining);
    						list.add(mining);
    					}
    					this.saveBatchObjs(list);
    				}
				}
    		}
    		
		}
    	
    	
    	
    }

    /** 审核通过 并调整核实表数据 */
	@Transactional(readOnly = false)
    public void investPassAndUpdateCrptCheck(List<TbTjCrptInvest> list){
		if(CollectionUtils.isEmpty(list)){
			return;
		}
		SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
		TsUserInfo user = sessionData.getUser();
		for(TbTjCrptInvest crptInvest : list){
			if(null == crptInvest || null == crptInvest.getRid()){
				continue;
			}
			crptInvest.setStateMark(3);
			crptInvest.setFkByCheckUnitId(user.getTsUnit());
			crptInvest.setCheckDate(new Date());
			if(1 == crptInvest.getExistState()){
				TbTjCrptCheck tjCrptCheck = null != crptInvest.getFkByCheckId() ? crptInvest.getFkByCheckId()
						: null;
				if(null != tjCrptCheck){
					Integer crptCheckIfSubOrg = null == tjCrptCheck.getIfSubOrg() ? 0 : tjCrptCheck.getIfSubOrg();
					Integer crptInvestIfSubOrg = null == crptInvest.getIfSubOrg() ? 0 : crptInvest.getIfSubOrg();
					if(crptCheckIfSubOrg.compareTo(crptInvestIfSubOrg) != 0){
						tjCrptCheck = null;
					}
				}
				if(null == tjCrptCheck && StringUtils.isNotBlank(crptInvest.getInstitutionCode())){
					StringBuffer hqlBuffer = new StringBuffer("SELECT T FROM TbTjCrptCheck T ");
					hqlBuffer.append("WHERE T.institutionCode='").append(crptInvest.getInstitutionCode()).append("' ");
					hqlBuffer.append("AND T.ifSubOrg=").append(null == crptInvest.getIfSubOrg() ? 0 : crptInvest.getIfSubOrg()).append(" ");
					if(null != crptInvest.getIfSubOrg() && 1 == crptInvest.getIfSubOrg()){
						hqlBuffer.append("AND T.crptName='").append(crptInvest.getCrptName()).append("' ");
					}
					tjCrptCheck = this.findOneByHql(hqlBuffer.toString(), TbTjCrptCheck.class);
				}
				if(null == tjCrptCheck){
					tjCrptCheck = new TbTjCrptCheck();
				}
				tjCrptCheck.setFkByZoneId(crptInvest.getFkByZoneId());
				tjCrptCheck.setInstitutionCode(crptInvest.getInstitutionCode());
				tjCrptCheck.setCrptName(crptInvest.getCrptName());
				tjCrptCheck.setIfSubOrg(null == crptInvest.getIfSubOrg() ? 0 : crptInvest.getIfSubOrg());
				tjCrptCheck.setFkByIndusTypeId(crptInvest.getFkByIndusTypeId());
				tjCrptCheck.setFkByEconomyId(crptInvest.getFkByEconomyId());
				tjCrptCheck.setFkByCrptSizeId(crptInvest.getFkByCrptSizeId());
				tjCrptCheck.setAddress(crptInvest.getAddress());
				tjCrptCheck.setCorporateDeputy(crptInvest.getCorporateDeputy());
				tjCrptCheck.setPhone(crptInvest.getPhone());
				tjCrptCheck.setLinkman2(crptInvest.getLinkman2());
				tjCrptCheck.setLinkphone2(crptInvest.getLinkphone2());
				tjCrptCheck.setWorkForce(crptInvest.getWorkForce());
				tjCrptCheck.setHoldCardMan(crptInvest.getHoldCardMan());
				tjCrptCheck.setLastInvestDate(crptInvest.getInvestDate());
				if(null == tjCrptCheck.getRid()){
					preInsert(tjCrptCheck);
					this.save(tjCrptCheck);
				}else{
					preUpdate(tjCrptCheck);
					this.updateObj(tjCrptCheck);
				}
				crptInvest.setFkByCheckId(tjCrptCheck);
			}
			preUpdate(crptInvest);
			this.updateObj(crptInvest);
		}
	}

	public Integer findEntrustCrptExportCount(String searchZoneCode, String searchUnitName, String searchCreditCode, String selectIndusTypeIds, String selectEconomyIds, String selectCrptSizeIds, String selectMiningIds, String selectUnproIds, List<String> stateArray, List<String> existArray, List<String> productArray, List<String> uraniumArray) {

		Map<String, Object> paramMap = new HashMap<String, Object>();

		StringBuffer conditionBuffer = new StringBuffer();
		conditionBuffer.append("WHERE 1=1 ");
		if(StringUtils.isNotBlank(searchZoneCode)){
			conditionBuffer.append("AND T1.ZONE_GB LIKE :zoneCode  ");
			paramMap.put("zoneCode", ZoneUtil.zoneSelect(searchZoneCode)+"%");
		}else{//地区为空 应该查询不到数据
			conditionBuffer.append("AND 1=2 ");
		}
		if(StringUtils.isNotBlank(searchUnitName)){
			conditionBuffer.append("AND T.CRPT_NAME LIKE :crptName  escape '\\\'  ");
			paramMap.put("crptName", "%" + StringUtils.convertBFH(searchUnitName.trim()) + "%");
		}
		if(StringUtils.isNotBlank(searchCreditCode)){
			conditionBuffer.append("AND T.INSTITUTION_CODE LIKE :creditCode  escape '\\\'  ");
			paramMap.put("creditCode", "%" + StringUtils.convertBFH(searchCreditCode.trim()) + "%");
		}
		if(StringUtils.isNotBlank(selectIndusTypeIds)){
			conditionBuffer.append("AND T.INDUS_TYPE_ID IN (").append(selectIndusTypeIds).append(")  ");
		}
		if(StringUtils.isNotBlank(selectEconomyIds)){
			conditionBuffer.append("AND T.ECONOMY_ID IN (").append(selectEconomyIds).append(") ");
		}
		if(StringUtils.isNotBlank(selectCrptSizeIds)){
			conditionBuffer.append("AND T.CRPT_SIZE_ID IN (").append(selectCrptSizeIds).append(") ");
		}
		//开采方式
		if(StringUtils.isNotBlank(selectMiningIds)){
			conditionBuffer.append("AND T.RID IN (").append(" SELECT DISTINCT TTIM.MAIN_ID FROM TB_TJ_CRPT_INVST_MINING TTIM WHERE TTIM.MINING_ID IN( ").append(selectMiningIds).append(")").append(") ");
		}
		//非正常生产情况
		if(StringUtils.isNotBlank(selectUnproIds)){
			conditionBuffer.append("AND T.NO_PRODUCE_STATE_ID IN (").append(selectUnproIds).append(") ");
		}

		//状态
		if(!CollectionUtils.isEmpty(stateArray)){
			StringBuffer stateBuffer = new StringBuffer();
			for(String state : stateArray){
				stateBuffer.append(",").append(state);
			}
			conditionBuffer.append("AND T.STATE_MARK IN (").append(stateBuffer.substring(1)).append(") ");
		}
		//存在情况
		if(!CollectionUtils.isEmpty(existArray)){
			StringBuffer stateBuffer = new StringBuffer();
			for(String state : existArray){
				stateBuffer.append(",").append(state);
			}
			conditionBuffer.append("AND T.EXIST_STATE IN (").append(stateBuffer.substring(1)).append(") ");
		}
		//生产情况
		if(!CollectionUtils.isEmpty(productArray)){
			StringBuffer stateBuffer = new StringBuffer();
			for(String state : productArray){
				stateBuffer.append(",").append(state);
			}
			conditionBuffer.append("AND T.PRODUCE_STATE IN (").append(stateBuffer.substring(1)).append(") ");
		}
		//含铀情况
		if(!CollectionUtils.isEmpty(uraniumArray)){
			StringBuffer stateBuffer = new StringBuffer();
			for(String state : uraniumArray){
				stateBuffer.append(",").append(state);
			}
			conditionBuffer.append("AND T.IF_HAS_URANIUM IN (").append(stateBuffer.substring(1)).append(") ");
		}
		//count 查询
		String countSql = "SELECT COUNT(*) as num FROM TB_TJ_CRPT_INVEST T LEFT JOIN TS_ZONE T1 ON T .ZONE_ID = T1.RID " + conditionBuffer.toString();
		List<Map<String,Object>> resultList = this.findDataBySql(countSql, paramMap);
		if (null != resultList && resultList.size() > 0) {
			Integer count = resultList.get(0) != null ? Integer.valueOf(resultList.get(0).get("NUM").toString()) : 0;
			return count;
		}
		return 0;

	}

	public List<Object[]> findEntrustCrptExportList(String searchZoneCode, String searchUnitName, String searchCreditCode, String selectIndusTypeIds,
													String selectEconomyIds, String selectCrptSizeIds, String selectMiningIds, String selectUnproIds,
													List<String> stateArray, List<String> existArray, List<String> productArray, List<String> uraniumArray,
													int startInd, int endInd) {

		Map<String, Object> paramMap = new HashMap<String, Object>();
		StringBuffer conditionBuffer = new StringBuffer();
		conditionBuffer.append(" WHERE 1=1 ");
		if(StringUtils.isNotBlank(searchZoneCode)){
			conditionBuffer.append("AND T1.ZONE_GB LIKE :zoneCode  ");
			paramMap.put("zoneCode", ZoneUtil.zoneSelect(searchZoneCode)+"%");
		}else{//地区为空 应该查询不到数据
			conditionBuffer.append("AND 1=2 ");
		}
		if(StringUtils.isNotBlank(searchUnitName)){
			conditionBuffer.append("AND T.CRPT_NAME LIKE :crptName  escape '\\\'  ");
			paramMap.put("crptName", "%" + StringUtils.convertBFH(searchUnitName.trim()) + "%");
		}
		if(StringUtils.isNotBlank(searchCreditCode)){
			conditionBuffer.append("AND T.INSTITUTION_CODE LIKE :creditCode  escape '\\\'  ");
			paramMap.put("creditCode", "%" + StringUtils.convertBFH(searchCreditCode.trim()) + "%");
		}
		if(StringUtils.isNotBlank(selectIndusTypeIds)){
			conditionBuffer.append("AND T.INDUS_TYPE_ID IN (").append(selectIndusTypeIds).append(")  ");
		}
		if(StringUtils.isNotBlank(selectEconomyIds)){
			conditionBuffer.append("AND T.ECONOMY_ID IN (").append(selectEconomyIds).append(") ");
		}
		if(StringUtils.isNotBlank(selectCrptSizeIds)){
			conditionBuffer.append("AND T.CRPT_SIZE_ID IN (").append(selectCrptSizeIds).append(") ");
		}
		//开采方式
		if(StringUtils.isNotBlank(selectMiningIds)){
			conditionBuffer.append("AND T.RID IN (").append(" SELECT DISTINCT TTIM.MAIN_ID FROM TB_TJ_CRPT_INVST_MINING TTIM WHERE TTIM.MINING_ID IN( ").append(selectMiningIds).append(")").append(") ");
		}
		//非正常生产情况
		if(StringUtils.isNotBlank(selectUnproIds)){
			conditionBuffer.append("AND T.NO_PRODUCE_STATE_ID IN (").append(selectUnproIds).append(") ");
		}

		//状态
		if(!CollectionUtils.isEmpty(stateArray)){
			StringBuffer stateBuffer = new StringBuffer();
			for(String state : stateArray){
				stateBuffer.append(",").append(state);
			}
			conditionBuffer.append("AND T.STATE_MARK IN (").append(stateBuffer.substring(1)).append(") ");
		}
		//存在情况
		if(!CollectionUtils.isEmpty(existArray)){
			StringBuffer stateBuffer = new StringBuffer();
			for(String state : existArray){
				stateBuffer.append(",").append(state);
			}
			conditionBuffer.append("AND T.EXIST_STATE IN (").append(stateBuffer.substring(1)).append(") ");
		}
		//生产情况
		if(!CollectionUtils.isEmpty(productArray)){
			StringBuffer stateBuffer = new StringBuffer();
			for(String state : productArray){
				stateBuffer.append(",").append(state);
			}
			conditionBuffer.append("AND T.PRODUCE_STATE IN (").append(stateBuffer.substring(1)).append(") ");
		}
		//含铀情况
		if(!CollectionUtils.isEmpty(uraniumArray)){
			StringBuffer stateBuffer = new StringBuffer();
			for(String state : uraniumArray){
				stateBuffer.append(",").append(state);
			}
			conditionBuffer.append("AND T.IF_HAS_URANIUM IN (").append(stateBuffer.substring(1)).append(") ");
		}
		// 主查询
		StringBuffer querySqlBuffer = new StringBuffer();
		querySqlBuffer.append(" SELECT B.* FROM (");
		querySqlBuffer.append(" SELECT A.*, ROWNUM RN FROM (");
		querySqlBuffer.append("SELECT ")
				.append("T1.FULL_NAME AS ZONEFULLNAME, ") //0 地区全称 需要二次处理 去掉第一个下划线前的内容
				.append("T .CRPT_NAME AS CRPTNAME, ")  //1 企业名称
				.append("T .INSTITUTION_CODE AS INSTITUTIONCODE, ")//2 社会信用代码
				.append(" T.IF_SUB_ORG AS IFSUBORG, ")//3 是否分支机构
				.append(" T.ADDRESS AS ADDRESS, ")//4 单位地址
				.append("T2.CODE_NAME AS INDUSTYPENAME, ")//5 行业类别
				.append("T3.CODE_NAME AS ECONOMYNAME, ")//6 经济类型
				.append("T4.CODE_NAME AS CRPTSIZENAME, ")//7 企业规模
				.append("T.CORPORATE_DEPUTY AS CORPORATEDEPUTY, ")//8 法人
				.append("T.PHONE AS PHONE, ")//9 法人联系电话
				.append("T .LINKMAN2 AS LINKMAN2, ")//10 联系人
				.append("T .LINKPHONE2 AS LINKPHONE2, ")//11 联系人电话
				.append("T.WORK_FORCE AS WORKFORCE,")//12 职工总人数
				.append("T.HOLD_CARD_MAN AS HOLDCARDMAN,")//13 接害总人数

				.append("T .EXIST_STATE AS EXISTSTATE, ")//14 存在情况
				.append("T .PRODUCE_STATE AS PRODUCESTATE, ")//15 生产情况
				.append("T8.CODE_NAME AS UNPRODUCESTATE, ")//16 非正常生产情况
				.append("T .OTHER_PRODUCE_STATE AS OTHERPRODUCESTATE, ")//17 其他说明
				.append("T9 .NAMETEST AS NAMETEST, ")//18 开采方式

				//.append("T .PRODUCE_STATE AS PRODUCESTATE1, ")//18 开采方式
				.append("T .IF_HAS_URANIUM AS IFHASURANIUM, ")//19 含铀情况
				.append("T5.UNITNAME AS FILLUNITNAME, ")//20 调查机构
				.append("T .INVEST_DATE AS INVESTDATE, ")//21 调查日期

				.append("T.STATE_MARK AS STATEMARK, ")//22 审核状态
				.append("T7.UNITNAME AS AUDITUNITNAME, ")//23 审核机构
				.append("T.CHECK_DATE AS CHECKDATE, ")//24 审核日期
				.append("T.RID AS RID, ")//12 调查表 RID
				//辅助用其他信息 start 新增的信息 都加在这下边
				//辅助用其他信息 end
				//空字段 不使用 避免出现查询中多一个逗号导致查询异常的情况
				.append("'' AS ENDSTR  ");
		querySqlBuffer.append("FROM TB_TJ_CRPT_INVEST T ")
				.append("LEFT JOIN TS_ZONE T1 ON T .ZONE_ID = T1.RID ")
				.append("LEFT JOIN TS_SIMPLE_CODE T2 ON T .INDUS_TYPE_ID = T2.RID ")
				.append("LEFT JOIN TS_SIMPLE_CODE T3 ON T .ECONOMY_ID = T3.RID ")
				.append("LEFT JOIN TS_SIMPLE_CODE T4 ON T .CRPT_SIZE_ID = T4.RID ")
				.append("LEFT JOIN TS_UNIT T5 ON T.FILL_UNIT_ID = T5.RID ")
				.append("LEFT JOIN TB_TJ_CRPT_CHECK T6 ON T.CHECK_ID = T6.RID ")
				.append("LEFT JOIN TS_UNIT T7 ON T.CHECK_UNIT_ID = T7.RID ")
				.append("LEFT JOIN TS_SIMPLE_CODE T8 ON T .NO_PRODUCE_STATE_ID = T8.RID ")
				.append("LEFT JOIN (SELECT TT1.MAIN_ID,wm_concat(TT2.CODE_NAME) as NAMETEST FROM TB_TJ_CRPT_INVST_MINING TT1 LEFT JOIN TS_SIMPLE_CODE TT2 ON TT1.MINING_ID = TT2.rid\n" +
						"GROUP BY TT1.MAIN_ID ) T9 ON T.RID = T9.MAIN_ID ");
		querySqlBuffer.append(conditionBuffer.toString()).append(" ");// 加入查询条件
		querySqlBuffer.append("ORDER BY T .ZONE_ID, T .CRPT_NAME "); // 排序
		querySqlBuffer.append(") A ");
		querySqlBuffer.append(") B WHERE RN > ").append(startInd).append(" AND RN <= ").append(endInd);
		List<Object[]> resList  = this.findDataBySqlNoPage(querySqlBuffer.toString(),paramMap);
		return resList;

	}


}
