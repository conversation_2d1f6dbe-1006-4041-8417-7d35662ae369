package com.chis.modules.heth.comm.service;

import java.util.*;

import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.utils.HolidayUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.chis.modules.system.utils.Global;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.heth.comm.utils.CalLimitTimeCommUtils;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.AbstractTemplate;

/**
 * <p>
 * 类描述：职业卫生报告卡统一业务层
 * </p>
 *
 * @ClassAuthor qrr, 2018-07-18, BigDataServiceImpl
 */
@Service
@Transactional(readOnly = true)
public class ZwReportCardCommServiceImpl extends AbstractTemplate {

    private ZwReportCardCheckCommServiceImpl checkServiceImpl = SpringContextHolder.getBean(ZwReportCardCheckCommServiceImpl.class);
    private TdZwBgkFlowServiceImpl flowService = SpringContextHolder.getBean(TdZwBgkFlowServiceImpl.class);

    /**
     * <p>方法描述：GBZ1188不规范数据上报不规范原因说明保存、流程信息保存</p>
     * @MethodAuthor qrr,2019年10月9日,saveOrUpdateGbz188Nostd
     * */
    @Transactional(readOnly = false)
    public void saveOrUpdateGbz188Nostd(TdZwGbz188NostdComm gbz188Nostd, TdZwBgkLastSta lastSta, boolean ifsubmit, boolean ifCityDirect, Integer due) {
        this.upsertEntity(gbz188Nostd);
        // 最新状态流程
        if(null != lastSta) {
            lastSta.setBusId(gbz188Nostd.getRid());
            this.upsertEntity(lastSta);
        }
        if(ifsubmit) {
            Integer ifInTime = CalLimitTimeCommUtils.calIfInTime(1,
                    lastSta.getOrgRcvDate(), due, false, null, null);
            saveOrUpdateOrgFlowInfoNew(gbz188Nostd.getRid(), 5,
                    lastSta.getOrgRcvDate(), ifInTime, ifCityDirect);
        }
    }

    /**
     * <p>
     * 方法描述：保存填报机构流程信息
     * cardType：报告卡类型
     * busDate:业务日期
     * ifInTime:是否及时，包含填报是否及时与区级或市级退回后是否操作及时
     * ifCityDirect：是否市直属
     * </p>
     * <p>变更记录：此方法已过时,流程相关如需修改统一迁移至TdZwBgkFlowServiceImpl.saveOrUpdateOrgFlowInfo()
     * @Date 2022年4月24日 16:48:40</p>
     * @MethodAuthor qrr,2019年10月10日,saveOrUpdateFlowInfo
     * */
    @Deprecated
    @Transactional(readOnly = false)
    private void saveOrUpdateOrgFlowInfoNew(Integer busId, Integer cardType,
                                            Date busDate, Integer ifInTime, boolean ifCityDirect) {
        if (null == busId || null == cardType) {
            return;
        }
        Object[] maxRevFlow = checkServiceImpl.selectMaxRevDateInfo(
                busId, cardType);

        SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(
                SessionData.SESSION_DATA);
        TsUserInfo user = sessionData.getUser();
        if (null!=maxRevFlow && null!=maxRevFlow[0]) {
            StringBuffer updateSql = new StringBuffer();
            updateSql.append("update TD_ZW_BGK_FLOW set OPER_DATE = to_date('")
                    .append(DateUtils.getDate()).append("','yyyy-MM-dd')");// 更新操作日期
            updateSql.append(",OPER_PSN_ID =").append(user.getRid());// 操作人
            // 及时性，区级或市级退回操作是否及时
            updateSql.append(",IF_IN_TIME =").append(ifInTime);
            updateSql.append(" where rid =").append(maxRevFlow[0]);
            this.executeSql(updateSql.toString());
        } else {
            // 新增填报记录
        	TdZwBgkFlow bgkFlow = new TdZwBgkFlow();
            bgkFlow.setBusId(busId);
            bgkFlow.setCartType(cardType);
            // 业务日期
            bgkFlow.setRcvDate(busDate);
            bgkFlow.setOperDate(new Date());
            bgkFlow.setIfInTime(ifInTime);
            bgkFlow.setFkByOperPsnId(user);
            bgkFlow.setOperFlag(12);
            preInsert(bgkFlow);
            this.save(bgkFlow);
        }
        // 新增流程记录
        TdZwBgkFlow bgkFlow = new TdZwBgkFlow();
        bgkFlow.setBusId(busId);
        bgkFlow.setCartType(cardType);
        bgkFlow.setRcvDate(new Date());
        bgkFlow.setFkBySmtPsnId(user);
        if (ifCityDirect) {
            // 市直属
            bgkFlow.setOperFlag(33);
        } else {
            bgkFlow.setOperFlag(21);
        }
        preInsert(bgkFlow);
        this.save(bgkFlow);
    }

    @Transactional(readOnly = true)
    public List<Object[]> getSelectItemList(Integer bhkId) {
        if (null != bhkId) {
            StringBuilder sql = new StringBuilder();
            sql.append("select t8.badrsn_id,t9.code_name badName,t2.item_name,dbms_lob.substr(t.ITEM_RST,4000,1),t.ITEM_STDVALUE,t.MSRUNT msrName");
            sql.append(" ,(case t1.JDGPTN when 1 then (case when t1.HG_FLAG=0 then '不合格' else '合格' end) when 2 then (case when t1.ge is not null then '>='||t1.ge when t1.gt is not null then '>'||t1.gt when t1.le is not null then '<='||t1.le else '<'||t1.LT end)");
            sql.append(" WHEN 3 THEN (CASE WHEN t1.HG_FLAG = 0 THEN '未见异常' WHEN t1.HG_FLAG = 1 THEN '尘肺样改变' WHEN t1.HG_FLAG = 2 THEN '其他异常' ELSE '未检查' END)");
            sql.append("  else(case when t1.ge is not null then '>='||t1.ge ||'倍' when t1.gt is not null then '>'||t1.gt ||'倍' when t1.le is not null then '<='||t1.le ||'倍' else '<'||t1.LT ||'倍' end)||'('||t.maxval||')' end)");
            sql.append("  ,t14.clusionName,t7.deter_way ,t13.code_name badRsnName,t7.xh  from TD_ZWYJ_BSN_ADV_CSION t");
            sql.append(" left join TD_ZWYJ_BSN_CSION_ITEMS t1 on t.ADV_ID = t1.rid");
            sql.append(" left join TB_TJ_ITEMS t2 on t1.ITEM_ID = t2.rid");
            sql.append(" left join ts_simple_code t4 on t4.rid = t1.msrunt_id");
            sql.append(" left join TD_ZWYJ_BSN_CSION_ITEM_SUB t6 on t1.MAIN_ID = t6.rid");
            sql.append(" left join TD_ZWYJ_BSN_CLUSION t7 on t7.rid = t6.main_id");
            sql.append(" left join TD_ZWYJ_BSN_CLUSION_RSN t8 on t8.MAIN_ID = t7.rid");
            sql.append(" left join ts_simple_code t9 on t9.rid = t8.badrsn_id");
            sql.append(" left join (select  t10.main_id,REPLACE(wm_concat(t11.code_name),',',' / ') clusionName from TD_ZWYJ_BSN_CLUSION_ADV t10");
            sql.append(" left join ts_simple_code t11 on t11.rid = t10.bhkrst_id");
            sql.append(" group by  t10.main_id) t14 on t14.main_id = t7.rid");
            sql.append(" left join TD_ZWYJ_BSN_BADRSNS t12 on t12.bhk_id = t.bhk_id and t12.badrsn_id = t8.badrsn_id");
            sql.append(" left join ts_simple_code t13 on t13.rid = t12.exam_conclusion_id");
            sql.append(" where t.bhk_id =").append(bhkId);
            sql.append("  and t8.badrsn_id in (select t14.BADRSN_ID  from TD_ZWYJ_BSN_BADRSNS t14 where t14.BHK_ID =").append(bhkId).append(")");
            sql.append(" union all");
            sql.append(" select t8.badrsn_id, t9.code_name badName, t1.item_name,");
            sql.append("   t6.PD_CSION_DESC, '','', t6.PD_CSION_DESC, t14.clusionName, t7.deter_way,t13.code_name badRsnName,t7.xh ");
            sql.append(" from TD_ZWYJ_BSN_ADV_ZDZYB t");
            sql.append(" left join TB_TJ_ITEMS t1 on t.ITEM_ID = t1.rid   ");
            sql.append(" left join TD_ZWYJ_BSN_CSION_PDJL t3 on t3.rid = t.ADV_ID ");
            sql.append(" left join TD_ZDZYB_ANALY_ITM_TYPE t4 on t4.RID =t3.pd_clusion_id");
            sql.append(" left join TD_ZDZYB_SPECIAL_ANALY t5 on t4.MAIN_ID =t5.rid");
            sql.append(" left join TD_ZWYJ_BSN_CSION_ITEM_SUB t6 on t3.main_id = t6.rid ");
            sql.append(" left join TD_ZWYJ_BSN_CLUSION t7 on t7.rid = t6.main_id ");
            sql.append(" left join TD_ZWYJ_BSN_CLUSION_RSN t8 on t8.MAIN_ID = t7.rid ");
            sql.append(" left join ts_simple_code t9 on t9.rid = t8.badrsn_id ");
            sql.append(" left join (select t10.main_id, REPLACE(wm_concat(t11.code_name),',',' / ')  clusionName ");
            sql.append("    from TD_ZWYJ_BSN_CLUSION_ADV t10 ");
            sql.append("    left join ts_simple_code t11 on t11.rid = t10.bhkrst_id ");
            sql.append("     group by t10.main_id) t14 on t14.main_id = t7.rid ");
            sql.append(" left join TD_ZWYJ_BSN_BADRSNS t12 on t12.bhk_id = t.bhk_id ");
            sql.append("      and t12.badrsn_id = t8.badrsn_id ");
            sql.append(" left join ts_simple_code t13 on t13.rid = t12.exam_conclusion_id ");
            sql.append(" where t.bhk_id =").append(bhkId);
            sql.append("  and t8.badrsn_id in (select t14.BADRSN_ID  from TD_ZWYJ_BSN_BADRSNS t14 where t14.BHK_ID =").append(bhkId).append(")");
            sql.append("  and t8.badrsn_id=t5.BADRSN_ID  ");
            sql.append(" order by badrsn_id,xh ");
            List<Object[]> resultList = em.createNativeQuery(sql.toString()).getResultList();
            return resultList;
        }
        return null;
    }

    /**
     * @MethodName: getAbnormalInfoList
     * @Description: 查找异常信息
     * @Param: [bhkId]
     * @Return: java.util.List<java.lang.Object[]>
     * @Author: maox
     * @Date: 2020-11-03
    **/
    @Transactional(readOnly = true)
    public List<Object[]> getAbnormalInfoList(Integer bhkId) {
        if (null != bhkId) {
            StringBuilder sql = new StringBuilder();
            sql.append(" select t.rid,t2.code_name from TD_ZWYJ_BSN_CSION_NO_SUB t ");
            sql.append(" left join TD_ZWYJ_BSN_CTRL_CSION t1 on  t.rid = t1.crtl_id");
            sql.append(" left join ts_simple_code t2 on t.bhkrst_id = t2.rid");
            sql.append(" where t1.bhk_id =").append(bhkId);
            List<Object[]> resultList = em.createNativeQuery(sql.toString()).getResultList();
            return resultList;
        }
        return null;
    }

    @Transactional(readOnly = true)
    public Integer findBhkId(Integer orgId,String bhkCode) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select rid  from TD_TJ_BHK ");
        sql.append(" where BHK_CODE ='").append(bhkCode);
        sql.append("' and BHKORG_ID =").append(orgId);
        List<Object> resultList = this.em.createNativeQuery(sql.toString()).getResultList();
        if (null != resultList && resultList.size() > 0) {
            Object obj = resultList.get(0);
            return obj == null ? null : Integer.valueOf(String.valueOf(obj));
        }
        return null;
    }


    @Transactional(readOnly = true)
    public TdZwyjOtrDealWrit findTdZwyjOtrDealWritByRid(Integer mainId){
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TdZwyjOtrDealWrit t where 1=1 and t.fkByMainId.rid=").append(mainId);
        List<TdZwyjOtrDealWrit> list=em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /**
     * <p>方法描述：查询职业健康检查机构的备案信息</p>
     * @MethodAuthor rcj,2020年11月19日,findTjorgRecords
     * */
    public List<TdZwTjorgRecordComm> findTjorgRecords(Integer mainId,String zoneGb) {
        if (null==mainId) {
            return null;
        }
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT T FROM TdZwTjorgRecordComm T WHERE T.fkByMainId.rid = '").append(mainId).append("' and T.fkByZoneId.zoneGb like '").append(zoneGb).append("%'");
        sb.append(" order by T.stateMark,T.fkByZoneId.zoneGb,T.certDate DESC");
        List<TdZwTjorgRecordComm> list = this.findByHql(sb.toString(), TdZwTjorgRecordComm.class);
        if (!CollectionUtils.isEmpty(list)) {
            for (TdZwTjorgRecordComm t : list) {
                t.getRcdItems().size();
            }
        }
        return list;
    }


    /**
     * <p>方法描述：初始化超范围服务预警企业名单[超范围预警]</p>
     * @MethodAuthor rcj,2020年11月19日,findTdZwyjOtrCrptList
     * */
    public List<TdZwyjOtrCrptList> findTdZwyjOtrCrptList(Integer rid ){
        StringBuffer hql = new StringBuffer();
        hql.append(" select t from TdZwyjOtrCrptList t where t.fkByMainId.rid = ").append(rid).append(" order by t.fkByCrptId.tsZoneByZoneId.zoneGb,t.fkByCrptId.crptName");
        List<TdZwyjOtrCrptList> list = findByHql(hql.toString(), TdZwyjOtrCrptList.class);
        if(!CollectionUtils.isEmpty(list)){
            for (TdZwyjOtrCrptList tdZwyjOtrCrpt:list) {
                List<TdZwyjOtrSerItemCrpt> tdZwyjOtrSerItemCrptList = tdZwyjOtrCrpt.getTdZwyjOtrSerItemCrptList();
                if(!CollectionUtils.isEmpty(tdZwyjOtrSerItemCrptList)){
                    StringBuffer items = new StringBuffer();
                    for (TdZwyjOtrSerItemCrpt crptitem:tdZwyjOtrSerItemCrptList) {
                        if(null != crptitem.getFkByServiceId()){
                            items.append("，").append(crptitem.getFkByServiceId().getCodeName());
                        }
                    }
                    if(items.length()>0){
                        tdZwyjOtrCrpt.setItems(items.substring(1));
                    }
                }

                String fullName = tdZwyjOtrCrpt.getFkByCrptZoneId().getFullName();
                if(null != fullName){
                    int i = fullName.indexOf("_");
                    tdZwyjOtrCrpt.getFkByCrptZoneId().setFullName(fullName.substring(i+1));
                }

            }
        }
        return list;
    }


    /**
     * <p>方法描述：初始化体检主表[危急值]</p>
     * @MethodAuthor rcj,2020年11月19日,findTdZwyjOtrCrptList
     * */
    public TdZwyjDangerBhk findTdZwyjDangerBhkById(Integer rid ){
        TdZwyjDangerBhk tdZwyjDangerBhk = find(TdZwyjDangerBhk.class,rid);
        if(!CollectionUtils.isEmpty(tdZwyjDangerBhk.getTdZwyjDangerResultList())){
            tdZwyjDangerBhk.getTdZwyjDangerResultList().size();
            for (TdZwyjDangerResult tdZwyjDangerResult:tdZwyjDangerBhk.getTdZwyjDangerResultList()) {
                if(tdZwyjDangerResult.getFkByDangerId().getGeVal() != null){
                    tdZwyjDangerResult.getFkByDangerId().setgVal("≥"+tdZwyjDangerResult.getFkByDangerId().getGeVal().toString());
                }else if(tdZwyjDangerResult.getFkByDangerId().getGtVal() != null){
                    tdZwyjDangerResult.getFkByDangerId().setgVal(">"+tdZwyjDangerResult.getFkByDangerId().getGtVal().toString());
                }


                if(tdZwyjDangerResult.getFkByDangerId().getLeVal() != null){
                    tdZwyjDangerResult.getFkByDangerId().setlVal("≤"+tdZwyjDangerResult.getFkByDangerId().getLeVal().toString());
                }else if(tdZwyjDangerResult.getFkByDangerId().getLtVal() != null){
                    tdZwyjDangerResult.getFkByDangerId().setlVal("<"+tdZwyjDangerResult.getFkByDangerId().getLtVal().toString());
                }
            }
            //按照num排序
            Collections.sort(tdZwyjDangerBhk.getTdZwyjDangerResultList(), new Comparator<TdZwyjDangerResult>() {
                @Override
                public int compare(TdZwyjDangerResult o1, TdZwyjDangerResult o2) {
                    return o1.getFkByDangerId().getFkByItemId().getNum()-o2.getFkByDangerId().getFkByItemId().getNum();
                }
            });
        }
        return tdZwyjDangerBhk;
    }
/**
 *  <p>方法描述：</p>
 *  tdZwYszybRpt : 疑似职业病报告卡对象
 *  lastSta：报告卡最新状态
 *  ifsubmit：是否提交
 *  ifCityDirect：是否市直属
 *  due：计算是否及时性
 * @MethodAuthor hsj 2022/4/15 17:32
 */
    @Transactional(readOnly = false)
    public void saveOrUpdateYszybRptInfo(TdZwYszybRpt tdZwYszybRpt, TdZwBgkLastSta lastSta, boolean ifsubmit, Integer due,TsZone zone) {
        super.upsertEntity(tdZwYszybRpt);

        // 最新流程
        if (null != lastSta) {
            lastSta.setBusId(tdZwYszybRpt.getRid());
            this.upsertEntity(lastSta);
        }
        if (ifsubmit) {// 提交
            Integer ifInTime = 0;
            int num= HolidayUtil.calRemainingDate(lastSta.getOrgRcvDate(),new Date(),due.toString());
            if(num>0){
                ifInTime = 1;
            }else{
                ifInTime =0;
            }
            flowService.saveOrUpdateOrgFlowInfoNew(tdZwYszybRpt.getRid(), 2,
                    lastSta.getOrgRcvDate(), ifInTime, zone);
        }
    }


    /**
     * 寻找省级地区
     * @param zoneGb
     * @return
     */
    public TsZone findProByZoneGb(String zoneGb){
        String hql = "select t from TsZone t where t.zoneGb ='"+zoneGb+"'";
        List<TsZone> byHql = findByHql(hql, TsZone.class);
        return byHql.get(0);
    }
    /**
     *  <p>方法描述：根据rid获取职业病报告卡</p>
     * @MethodAuthor hsj 2022-05-17 11:56
     */
    @Transactional(readOnly = true)
    public TdZwOccdisCardNew findTdZwOccdisCardNewByRid(Integer disCardId) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT T FROM TdZwOccdisCardNew T WHERE T.rid = '").append(disCardId).append("'");
        TdZwOccdisCardNew cardNew =  findOneByHql(sb.toString(), TdZwOccdisCardNew.class);
        cardNew.getCardDocList().size();
        return cardNew;
    }

    /**
     *  <p>方法描述：获取文书</p>
     * @MethodAuthor hsj 2022-05-17 17:43
     */
    public String getAnnexPath(Integer disCardId, Integer writCode) {
        String annexPath = null;
        StringBuffer s = new StringBuffer();
        s.append("SELECT T.RID,T2.ANNEX_PATH FROM TD_ZW_MADEDWRIT  T ");
        s.append("LEFT JOIN TB_ZW_WRITSORT T1 ON T.WRIT_SORTID = T1.RID ");
        s.append("LEFT JOIN TD_ZW_DIAG_ANNEXS T2 ON T.WRIT_ANNEX = T1.RID ");
        s.append("WHERE T1.WRIT_CODE =:writCode ");
        s.append("AND  T.MAIN_ID = ( SELECT MAIN_ID FROM TD_ZW_OCCDIS_CARD_NEW WHERE RID =:disCardId )  ");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("writCode",writCode);
        paramMap.put("disCardId",disCardId);
        List<Object[]> list = this.findDataBySqlNoPage(s.toString(),paramMap);
        if(!CollectionUtils.isEmpty(list)){
            annexPath = null == list.get(0)[1] ? null :  list.get(0)[1].toString();
        }
        return annexPath;
    }
    /**
     *  <p>方法描述：疑似职业病报告卡提交</p>
     * @MethodAuthor hsj 2022/4/18 10:34
     */
    @Transactional(readOnly = false)
    public void submitOrUpdateYszybRpt(TdZwYszybRpt tdZwYszybRpt, TdZwBgkLastSta lastSta) {
        this.saveOrUpdateTdZwYszybRpt(tdZwYszybRpt,lastSta);
        //流程记录
        saveOrgTdZwBgkFlow(tdZwYszybRpt.getRid(), 2,tdZwYszybRpt.getFkByEmpCrptId().getTsZoneByZoneId());
    }
    /**
     *  <p>方法描述：最新流程记录</p>
     * @MethodAuthor hsj 2022/4/18 10:36
     */
    @Transactional(readOnly = false)
    private void saveOrgTdZwBgkFlow(Integer busId, Integer cardType,TsZone zone) {
        if (null == busId || null == cardType) {
            return;
        }
        // 新增流程记录
        TdZwBgkFlow bgkFlow = new TdZwBgkFlow();
        bgkFlow.setBusId(busId);
        bgkFlow.setCartType(cardType);
        bgkFlow.setRcvDate(new Date());
        bgkFlow.setFkBySmtPsnId(Global.getUser());
        if ("1".equals(zone.getIfCityDirect())) {//市直属
            bgkFlow.setOperFlag(33);
        } else if ("1".equals(zone.getIfProvDirect())){//省直属
            bgkFlow.setOperFlag(44);
        } else {
            bgkFlow.setOperFlag(21);
        }
        preInsert(bgkFlow);
        this.save(bgkFlow);
    }

    /**
     *  <p>方法描述：疑似职业病报告卡保存</p>
     * @MethodAuthor hsj 2022/4/18 10:35
     */
    @Transactional(readOnly = false)
    public void saveOrUpdateTdZwYszybRpt(TdZwYszybRpt tdZwYszybRpt,TdZwBgkLastSta lastSta) {
        super.upsertEntity(tdZwYszybRpt);
        // 最新流程
        if (null != lastSta) {
            lastSta.setBusId(tdZwYszybRpt.getRid());
            this.upsertEntity(lastSta);
        }
    }

    /**
     * 诊断机构删除疑似职业病报告卡(标删)
     * @param rid 报告卡RID
     */
    @Transactional(readOnly = false)
    public void deleteTdZwYszybRpt(Integer rid) {
        Map<String, Object> paramMap = new HashMap<>(16);
        String sql = "UPDATE TD_ZW_YSZYB_RPT SET DEL_MARK = 1 WHERE RID = :rid";
        paramMap.put("rid", rid);
        executeSql(sql, paramMap);
    }
}
