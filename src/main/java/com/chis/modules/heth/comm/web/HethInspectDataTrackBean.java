package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.heth.comm.entity.TdTjBhk;
import com.chis.modules.heth.comm.javabean.TdTjBhkInfoBean;
import com.chis.modules.heth.comm.service.HethBaseCommServiceImpl;
import com.chis.modules.heth.comm.service.HethInspectDataTrackService;
import com.chis.modules.heth.comm.service.HethStaQueryCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.component.outputpanel.OutputPanel;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * 职业健康检查数据上传情况跟踪Bean
 *
 * <AUTHOR>
 * @version 1.0
 */
@ManagedBean(name = "hethInspectDataTrackBean")
@ViewScoped
public class HethInspectDataTrackBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final HethInspectDataTrackService hidtService = SpringContextHolder.getBean(HethInspectDataTrackService.class);
    private final HethStaQueryCommServiceImpl hethStaQueryCommService = SpringContextHolder.getBean(HethStaQueryCommServiceImpl.class);
    private final HethBaseCommServiceImpl hethBaseCommService = SpringContextHolder.getBean(HethBaseCommServiceImpl.class);

    /**
     * 主表RID
     */
    private Integer rid;
    private TdTjBhk tdTjBhk;
    /**
     * 单个重新上传获取的最新数据
     */
    private Object[] selObjects;
    /**
     * 上传数据操作类型 <pre>1: 单个重新上传<pre>2: 全部上传
     */
    private Integer uploadActionType;
    /**
     * 查询条件：地区集合
     */
    private List<TsZone> searchZoneList = new ArrayList<>();
    /**
     * 查询条件：检查机构地区
     */
    private String searchCheckOrgZoneGb;
    private String searchCheckOrgZoneName;
    /**
     * 查询条件：检查机构
     */
    private String searchCheckOrgId;
    private String searchCheckOrgName;
    /**
     * 查询条件：用人单位名称
     */
    private String searchCrptName;
    /**
     * 查询条件：人员姓名
     */
    private String searchPsnName;
    /**
     * 查询条件：体检类型
     */
    private String[] searchBhkTypes;
    /**
     * 查询条件：监测类型
     */
    private String[] searchJcTypes;
    /**
     * 查询条件：在岗状态
     */
    private List<TsSimpleCode> onGuardList;
    private String selectOnGuardNames;
    private String selectOnGuardIds;
    /**
     * 查询条件：体检危害因素
     */
    private List<TsSimpleCode> badRsnList;
    private String selectBadRsnNames;
    private String selectBadRsnIds;
    /**
     * 查询条件：体检开始日期
     */
    private Date searchBhkBeginDate;
    /**
     * 查询条件：体检结束日期
     */
    private Date searchBhkEndDate;
    /**
     * 查询条件：报告出具开始日期
     */
    private Date searchRptPrintBeginDate;
    /**
     * 查询条件：报告出具结束日期
     */
    private Date searchRptPrintEndDate;
    /**
     * 查询条件：是否复检
     */
    private String[] searchIfRhks;
    /**
     * 查询条件：是否异常
     */
    private String[] searchIfAbnormals;
    /**
     * 查询条件：审核状态
     */
    private List<SelectItem> stateList = new ArrayList<>();
    private Map<String, String> stateMap = new HashMap<>(16);
    private String[] searchStates;
    /**
     * 查询条件：上传国家状态
     */
    private List<SelectItem> upCountryStateList = new ArrayList<>();
    private Map<String, List<String>> upCountryStateMap = new HashMap<>(16);
    private String[] searchUpCountryStates;
    /**
     * 查询条件：国家失败原因
     */
    private String selectErrMsg;
    /**
     * 配置：审核级别
     */
    private String checkLevel;
    private Boolean checkLevel2 = Boolean.FALSE;
    private Boolean checkLevel3 = Boolean.FALSE;
    /**
     * 配置：是否有个案审核业务
     */
    private Boolean ifNeedBhkAudit = Boolean.FALSE;
    /**
     * 详情：是否显示国家失败原因
     */
    private Boolean showNationErrorMsg = Boolean.FALSE;
    /**
     * 详情：国家失败原因
     */
    private String nationErrorMsg;
    /**
     * 详情：历次审核意见
     */
    private List<Object[]> historyList;
    /**
     * 详情：体检基本信息
     */
    private TdTjBhkInfoBean tjBhkInfoBean = new TdTjBhkInfoBean();
    /**
     * 详情：异常情况
     */
    private List<String> unAbnormalsList = new ArrayList<>();
    /**
     * 详情：体检项目的布局表格
     */
    private OutputPanel archivePanel = (OutputPanel) JsfUtil.getApplication().createComponent(OutputPanel.COMPONENT_TYPE);

    public HethInspectDataTrackBean() {
        init();
        this.ifSQL = true;
    }

    private void init() {
        initConfig();
        initSimpleCode();
        //查询条件：检查机构地区-管辖地区
        TsZone zone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        this.searchCheckOrgZoneGb = zone.getZoneGb();
        this.searchCheckOrgZoneName = zone.getZoneName();
        this.searchZoneList = this.commService.findZoneListByGbAndType(zone.getZoneGb(), true, "", "");
        //查询条件：报告出具日期开始日期为上一年度11-16，结束日期：当天
        this.searchRptPrintBeginDate = DateUtils.getFixedDate(-1, 11, 16);
        this.searchRptPrintEndDate = new Date();
        initState();
    }

    /**
     * 初始化配置相关
     */
    private void initConfig() {
        this.checkLevel = PropertyUtils.getValue("checkLevel");
        this.checkLevel2 = "2".equals(this.checkLevel);
        this.checkLevel3 = "3".equals(this.checkLevel);
        this.ifNeedBhkAudit = "1".equals(this.commService.findParamValue("IF_NEED_BHK_AUDIT"));
    }

    /**
     * 初始化码表相关
     */
    private void initSimpleCode() {
        this.onGuardList = this.commService.findNumSimpleCodesByTypeId("5009");
        this.badRsnList = this.commService.findNumSimpleCodesByTypeId("5007");
    }

    /**
     * 初始化状态相关
     */
    private void initState() {
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem("1", "区县级待审"));
        this.stateList.add(new SelectItem("0", "区县级退回"));
        if (this.checkLevel3) {
            this.stateList.add(new SelectItem("3", "市级待审"));
            this.stateList.add(new SelectItem("2", "市级退回"));
        }
        this.stateList.add(new SelectItem("5", "省级待审"));
        this.stateList.add(new SelectItem("4", "省级退回"));
        this.stateList.add(new SelectItem("6", "省级审核通过"));
        this.stateList.add(new SelectItem("7", "国家退回"));
        this.searchStates = new String[]{"6", "7"};
        this.upCountryStateList = new ArrayList<>();
        this.upCountryStateMap = new HashMap<>(16);
        List<String> upCountryStateList;
        this.upCountryStateList.add(new SelectItem("0", "待上传"));
        this.upCountryStateList.add(new SelectItem("1", "已上传"));
        this.upCountryStateList.add(new SelectItem("2", "上传成功"));
        this.upCountryStateList.add(new SelectItem("3", "上传失败"));
        this.upCountryStateList.add(new SelectItem("4", "待删除后更新（上传成功）"));
        this.upCountryStateList.add(new SelectItem("5", "删除失败（上传成功）"));
        upCountryStateList = new ArrayList<>();
        upCountryStateList.add("0");
        this.upCountryStateMap.put("0", upCountryStateList);
        upCountryStateList = new ArrayList<>();
        upCountryStateList.add("1");
        upCountryStateList.add("6");
        this.upCountryStateMap.put("1", upCountryStateList);
        upCountryStateList = new ArrayList<>();
        upCountryStateList.add("2");
        this.upCountryStateMap.put("2", upCountryStateList);
        upCountryStateList = new ArrayList<>();
        upCountryStateList.add("3");
        upCountryStateList.add("7");
        upCountryStateList.add("8");
        this.upCountryStateMap.put("3", upCountryStateList);
        upCountryStateList = new ArrayList<>();
        upCountryStateList.add("9");
        upCountryStateList.add("11");
        upCountryStateList.add("16");
        this.upCountryStateMap.put("4", upCountryStateList);
        upCountryStateList = new ArrayList<>();
        upCountryStateList.add("13");
        upCountryStateList.add("17");
        upCountryStateList.add("18");
        this.upCountryStateMap.put("5", upCountryStateList);
    }

    @Override
    public void searchAction() {
        if (ObjectUtil.isEmpty(this.searchRptPrintBeginDate) || ObjectUtil.isEmpty(this.searchRptPrintEndDate)) {
            if (ObjectUtil.isEmpty(this.searchRptPrintBeginDate)) {
                JsfUtil.addErrorMessage("报告出具开始日期不能为空！");
            }
            if (ObjectUtil.isEmpty(this.searchRptPrintEndDate)) {
                JsfUtil.addErrorMessage("报告出具结束日期不能为空！");
            }
            return;
        }

        super.searchAction();
        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update("tabView:mainForm:dataTable");
    }

    @Override
    public String[] buildHqls() {
        String sql = buildSql(1);
        String sql1 = "SELECT AA.* FROM (" + sql + ")AA ORDER BY AA.P13, AA.P2, AA.P4, AA.P3, AA.P8, AA.P0";
        String sql2 = "SELECT COUNT(1) FROM (" + sql + ")";
        return new String[]{sql1, sql2};
    }

    /**
     * 构建SQL
     *
     * @param type SQL类型 1: 查询(默认); 2: 全部上传
     * @return SQL
     */
    private String buildSql(int type) {
        this.paramMap = new HashMap<>(16);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ");
        if (type == 2) {
            sql.append("    T.RID                                                       AS P0, ");
            sql.append("    UC.RID                                                      AS P1, ");
            sql.append("    NVL(UC.STATE, 0)                                            AS P2 ");
        } else {
            sql.append("    T.RID                                                       AS P0, ");
            sql.append("    Z.FULL_NAME                                                 AS P1, ");
            sql.append("    S.UNIT_NAME                                                 AS P2, ");
            sql.append("    T.PERSON_NAME                                               AS P3, ");
            sql.append("    T.CRPT_NAME                                                 AS P4, ");
            sql.append("    SC1.CODE_NAME                                               AS P5, ");
            sql.append("    T.JC_TYPE                                                   AS P6, ");
            sql.append("    T.IF_RHK                                                    AS P7, ");
            sql.append("    T.BHK_DATE                                                  AS P8, ");
            sql.append("    T.IF_ABNOMAL                                                AS P9, ");
            sql.append("    T.STATE                                                     AS P10, ");
            sql.append("    ''                                                          AS P11, ");
            sql.append("    UC.ERR_MSG                                                  AS P12, ");
            sql.append("    Z.ZONE_GB                                                   AS P13, ");
            sql.append("    NVL(UC.STATE, 0)                                            AS P14, ");
            sql.append("    Z1.IF_CITY_DIRECT                                           AS P15, ");
            sql.append("    ''                                                          AS P16, ");
            sql.append("    T.COUNTY_AUDIT_ADV                                          AS P17, ");
            sql.append("    T.CITY_AUDIT_ADV                                            AS P18, ");
            sql.append("    T.PRO_AUDIT_ADV                                             AS P19, ");
            sql.append("    T.ERR_MSG                                                   AS P20 ");
        }
        sql.append("FROM TD_TJ_BHK T ");
        sql.append("    LEFT JOIN TB_TJ_SRVORG S ON T.BHKORG_ID = S.RID ");
        sql.append("    LEFT JOIN TS_ZONE Z ON S.ZONE_ID = Z.RID ");
        sql.append("    LEFT JOIN TS_SIMPLE_CODE SC1 ON T.ONGUARD_STATEID = SC1.RID ");
        sql.append("    LEFT JOIN TD_ZYWS_UP_COUNTRY UC ON T.RID = UC.BUS_ID AND UC.BUS_TYPE = 2 ");
        sql.append("    LEFT JOIN TB_TJ_CRPT C ON T.CRPT_ID = C.RID ");
        sql.append("    LEFT JOIN TS_ZONE Z1 ON C.ZONE_ID = Z1.RID ");
        sql.append("WHERE 1 = 1 ");
        if (this.ifNeedBhkAudit) {
            sql.append(" AND T.IF_INTO_CHECK = 1 ");
        }
        //检查机构地区
        if (ObjectUtil.isNotEmpty(this.searchCheckOrgZoneGb)) {
            sql.append(" AND Z.ZONE_GB LIKE :searchCheckOrgZoneGb escape '\\\'");
            this.paramMap.put("searchCheckOrgZoneGb", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchCheckOrgZoneGb).trim()) + "%");
        }
        //检查机构
        if (ObjectUtil.isNotEmpty(this.searchCheckOrgId)) {
            sql.append(" AND T.BHKORG_ID IN (:checkOrgIdList) ");
            List<String> checkOrgIdList = StringUtils.string2list(this.searchCheckOrgId, ",");
            this.paramMap.put("checkOrgIdList", checkOrgIdList);
        }
        //用人单位名称
        if (ObjectUtil.isNotEmpty(this.searchCrptName)) {
            sql.append(" AND T.CRPT_NAME LIKE :searchCrptName escape '\\\'");
            this.paramMap.put("searchCrptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        //人员姓名
        if (ObjectUtil.isNotEmpty(this.searchPsnName)) {
            sql.append(" AND T.PERSON_NAME LIKE :searchPsnName escape '\\\'");
            this.paramMap.put("searchPsnName", "%" + StringUtils.convertBFH(this.searchPsnName.trim()) + "%");
        }
        //体检类型
        if (ObjectUtil.isNotEmpty(this.searchBhkTypes)) {
            sql.append(" AND T.BHK_TYPE IN (:bhkTypeList) ");
            List<String> bhkTypeList = Arrays.asList(this.searchBhkTypes);
            this.paramMap.put("bhkTypeList", bhkTypeList);
        }
        //监测类型
        if (ObjectUtil.isNotEmpty(this.searchJcTypes)) {
            sql.append(" AND T.JC_TYPE IN (:jcTypeList) ");
            List<String> jcTypeList = Arrays.asList(this.searchJcTypes);
            this.paramMap.put("jcTypeList", jcTypeList);
        }
        //在岗状态
        if (ObjectUtil.isNotEmpty(this.selectOnGuardIds)) {
            sql.append(" AND T.ONGUARD_STATEID IN (:onGuardIdList) ");
            List<String> onGuardIdList = StringUtils.string2list(this.selectOnGuardIds, ",");
            this.paramMap.put("onGuardIdList", onGuardIdList);
        }
        //体检危害因素
        if (ObjectUtil.isNotEmpty(this.selectBadRsnIds)) {
            sql.append(" AND EXISTS(SELECT 1 FROM TD_TJ_BADRSNS TT WHERE TT.BHK_ID = T.RID AND TT.BADRSN_ID IN (:badRsnIdList))");
            List<String> badRsnIdList = StringUtils.string2list(this.selectBadRsnIds, ",");
            this.paramMap.put("badRsnIdList", badRsnIdList);
        }
        //体检日期
        if (ObjectUtil.isNotEmpty(this.searchBhkBeginDate)) {
            sql.append(" AND T.BHK_DATE >= TO_DATE(:searchBhkBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchBhkBeginDate", DateUtils.formatDate(this.searchBhkBeginDate) + " 00:00:00");
        }
        if (ObjectUtil.isNotEmpty(this.searchBhkEndDate)) {
            sql.append(" AND T.BHK_DATE <= TO_DATE(:searchBhkEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchBhkEndDate", DateUtils.formatDate(this.searchBhkEndDate) + " 23:59:59");
        }
        //报告出具日期
        if (ObjectUtil.isNotEmpty(this.searchRptPrintBeginDate)) {
            sql.append(" AND T.RPT_PRINT_DATE >= TO_DATE(:searchRptPrintBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchRptPrintBeginDate", DateUtils.formatDate(this.searchRptPrintBeginDate) + " 00:00:00");
        }
        if (ObjectUtil.isNotEmpty(this.searchRptPrintEndDate)) {
            sql.append(" AND T.RPT_PRINT_DATE <= TO_DATE(:searchRptPrintEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchRptPrintEndDate", DateUtils.formatDate(this.searchRptPrintEndDate) + " 23:59:59");
        }
        //是否复检
        if (ObjectUtil.isNotEmpty(this.searchIfRhks)) {
            sql.append(" AND T.IF_RHK IN (:ifRhkList) ");
            List<String> ifRhkList = Arrays.asList(this.searchIfRhks);
            this.paramMap.put("ifRhkList", ifRhkList);
        }
        //是否异常
        if (ObjectUtil.isNotEmpty(this.searchIfAbnormals)) {
            sql.append(" AND T.IF_ABNOMAL IN (:ifAbnormalList) ");
            List<String> ifAbnormalList = Arrays.asList(this.searchIfAbnormals);
            this.paramMap.put("ifAbnormalList", ifAbnormalList);
        }
        //审核状态
        if (this.ifNeedBhkAudit && type != 2) {
            sql.append(" AND (").append(pakStatusSearch()).append(") ");
        }
        //上传国家状态
        if (ObjectUtil.isNotEmpty(this.searchUpCountryStates)) {
            sql.append(" AND NVL(UC.STATE, 0) IN (:upCountryStateList) ");
            List<String> upCountryStateList = new ArrayList<>();
            for (String s : this.searchUpCountryStates) {
                upCountryStateList.addAll(this.upCountryStateMap.get(s));
            }
            this.paramMap.put("upCountryStateList", upCountryStateList);
        }
        //国家失败原因
        if (ObjectUtil.isNotEmpty(this.selectErrMsg)) {
            sql.append(" AND UC.ERR_MSG LIKE :selectErrMsg escape '\\\'");
            this.paramMap.put("selectErrMsg", "%" + StringUtils.convertBFH(this.selectErrMsg.trim()) + "%");
        }
        return sql.toString();
    }

    /**
     * 封装status SQL片段
     *
     * @return SQL
     */
    private String pakStatusSearch() {
        List<String> statusList = new ArrayList<>();
        StringBuilder sb1 = new StringBuilder();
        StringBuilder sb2 = new StringBuilder();
        StringBuilder sb3 = new StringBuilder();

        if (ObjectUtil.isNotEmpty(this.searchStates)) {
            statusList.addAll(Arrays.asList(this.searchStates));
        } else {
            for (SelectItem item : this.stateList) {
                statusList.add(StringUtils.objectToString(item.getValue()));
            }
        }
        for (String status : statusList) {
            if ("0".equals(status)) {
                if (this.checkLevel2) {
                    sb1.append(", 0");
                } else {
                    sb2.append(" OR (T.STATE = 0 AND NVL(Z1.IF_CITY_DIRECT, 0) = 0)");
                }
            } else if ("1".equals(status)) {
                if (this.checkLevel2) {
                    sb1.append(", 1");
                } else {
                    sb2.append(" OR (T.STATE = 1 AND NVL(Z1.IF_CITY_DIRECT, 0) = 0)");
                }
            } else if ("2".equals(status)) {
                sb1.append(", 2");
                sb2.append(" OR (T.STATE = 0 AND NVL(Z1.IF_CITY_DIRECT, 0) = 1)");
            } else if ("3".equals(status)) {
                sb1.append(", 3");
                sb2.append(" OR (T.STATE = 1 AND NVL(Z1.IF_CITY_DIRECT, 0) = 1)");
            } else {
                sb1.append(", ").append(status);
            }
        }
        if (ObjectUtil.isNotEmpty(sb1)) {
            sb3.append("T.STATE IN (").append(sb1.substring(2)).append(")");
        }
        if (ObjectUtil.isNotEmpty(sb2)) {
            sb3.append(ObjectUtil.isEmpty(sb3) ? sb2.substring(4) : sb2);
        }
        return sb3.toString();
    }

    @Override
    public void processData(List<?> list) {
        if (null != list && list.size() > 0) {
            List<String> upCountryStateList = new ArrayList<>();
            upCountryStateList.addAll(this.upCountryStateMap.get("3"));
            upCountryStateList.addAll(this.upCountryStateMap.get("5"));
            List<Object[]> result = CollectionUtil.castList(Object[].class, list);
            for (Object[] obj : result) {
                //检查机构地区
                String s1 = StringUtils.objectToString(obj[1]);
                obj[1] = ZoneUtil.removeProvByFullName(s1);
                //监测类型
                String s6 = StringUtils.objectToString(obj[6]);
                if ("1".equals(s6)) {
                    obj[6] = "常规监测";
                } else if ("2".equals(s6)) {
                    obj[6] = "主动监测";
                } else {
                    obj[6] = "";
                }
                //是否复检
                String s7 = StringUtils.objectToString(obj[7]);
                if ("1".equals(s7)) {
                    obj[7] = "是";
                } else if ("0".equals(s7)) {
                    obj[7] = "否";
                } else {
                    obj[7] = "";
                }
                //是否异常
                String s9 = StringUtils.objectToString(obj[9]);
                if ("1".equals(s9)) {
                    obj[9] = "是";
                } else if ("0".equals(s9)) {
                    obj[9] = "否";
                } else {
                    obj[9] = "";
                }
                //退回原因
                String s10 = StringUtils.objectToString(obj[10]);
                if ("0".equals(s10)) {
                    obj[11] = obj[17];
                } else if ("2".equals(s10)) {
                    obj[11] = obj[18];
                } else if ("4".equals(s10)) {
                    obj[11] = obj[19];
                } else if ("7".equals(s10)) {
                    obj[11] = obj[20];
                }
                //上传国家状态是否上传失败或删除失败（上传成功）
                obj[16] = "1";
                if (!upCountryStateList.contains(StringUtils.objectToString(obj[14]))) {
                    obj[16] = "0";
                }
                //上传国家状态
                obj[14] = pakUpStatusStr(StringUtils.objectToString(obj[14]));
                //审核状态
                int status = 1;
                if (ObjectUtil.isNotEmpty(obj[10])) {
                    status = Integer.parseInt(StringUtils.objectToString(obj[10]));
                }
                boolean ifCityDirect = "1".equals(StringUtils.objectToString(obj[15]));
                obj[10] = pakStatusStr(status, ifCityDirect);

            }
        }
    }

    private String pakStatusStr(int status, boolean ifCityDirect) {
        switch (status) {
            case 0:
                if (this.checkLevel2 || !ifCityDirect) {
                    return "区县级退回";
                } else {
                    return "市级退回";
                }
            case 1:
                if (this.checkLevel2 || !ifCityDirect) {
                    return "区县级待审";
                } else {
                    return "市级待审";
                }
            case 2:
                return "市级退回";
            case 3:
                return "市级待审";
            case 4:
                return "省级退回";
            case 5:
                return "省级待审";
            case 6:
                return "省级审核通过";
            case 7:
                return "国家退回";
            default:
                return "";
        }
    }

    private String pakUpStatusStr(String status) {
        switch (status) {
            case "0":
                return "待上传";
            case "1":
            case "6":
                return "已上传";
            case "2":
                return "上传成功";
            case "3":
            case "7":
            case "8":
                return "上传失败";
            case "9":
            case "11":
            case "16":
                return "待删除后更新（上传成功）";
            case "13":
            case "17":
            case "18":
                return "删除失败（上传成功）";
            default:
                return "";
        }
    }

    /**
     * 打开检查机构弹出框
     */
    public void selCheckOrgAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 625, null, 500);

        Map<String, List<String>> paramMap = new HashMap<>(16);
        List<String> paramList = new ArrayList<>();
        paramList.add(this.searchCheckOrgId);
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<>();
        paramList.add(this.searchCheckOrgZoneGb);
        paramMap.put("searchZoneCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
    }

    /**
     * 检查机构弹出框选择后操作
     *
     * @param event 选择的检查机构
     */
    public void onSelectCheckOrgAction(SelectEvent event) {
        Map<String, Object> selectedMap = new HashMap<>();
        try {
            selectedMap = (Map<String, Object>) event.getObject();
        } catch (Exception e) {
            e.printStackTrace();
            this.searchCheckOrgId = null;
            this.searchCheckOrgName = null;
        }
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TbTjSrvorg> list = CollectionUtil.castList(TbTjSrvorg.class, selectedMap.get("selectPros"));
            if (!CollectionUtils.isEmpty(list)) {
                StringBuilder names = new StringBuilder();
                StringBuilder ids = new StringBuilder();
                for (TbTjSrvorg t : list) {
                    names.append("，").append(t.getUnitName());
                    ids.append(",").append(t.getRid());
                }
                this.searchCheckOrgId = ids.substring(1);
                this.searchCheckOrgName = names.substring(1);
            }
        }
    }

    /**
     * 清空选择的检查机构
     */
    public void clearCheckOrg() {
        this.searchCheckOrgId = null;
        this.searchCheckOrgName = null;
        RequestContext.getCurrentInstance().update("tabView:mainForm:checkOrgName");
    }

    /**
     * 全部上传操作
     */
    public void uploadAllDataAction() {
        Boolean ifStateIsNull=false;
        if(searchUpCountryStates.length==0||Arrays.asList(searchUpCountryStates).contains("3")||Arrays.asList(searchUpCountryStates).contains("5")){
            if(searchUpCountryStates.length==0||(Arrays.asList(searchUpCountryStates).contains("3")&&Arrays.asList(searchUpCountryStates).contains("5"))){
                searchUpCountryStates=new String[]{"3","5"};
                ifStateIsNull=true;
            }else if(Arrays.asList(searchUpCountryStates).contains("3")){
                searchUpCountryStates=new String[]{"3"};
                ifStateIsNull=true;
            }else if(Arrays.asList(searchUpCountryStates).contains("5")){
                searchUpCountryStates=new String[]{"5"};
                ifStateIsNull=true;
            }
            String sql = "SELECT COUNT(*) FROM (" + buildSql(2) + ")";
            int count = this.commService.findCountBySql(sql, this.paramMap);
            if(ifStateIsNull){
                searchUpCountryStates=new String[]{};
            }
            if (count == 0) {
                JsfUtil.addErrorMessage("无需要重新上传的数据！");
                return;
            }
            RequestContext.getCurrentInstance().execute("PF('AllUnloadDataDialog').show()");
        }else{
            JsfUtil.addErrorMessage("无需要重新上传的数据！");
        }
    }

    public void reUploadDataBeforeAction() {
        List<String> upCountryStateList = new ArrayList<>();
        upCountryStateList.addAll(this.upCountryStateMap.get("3"));
        upCountryStateList.addAll(this.upCountryStateMap.get("5"));
        this.selObjects = this.hidtService.findUpCountryStatusByBusId(this.rid);
        if (!upCountryStateList.contains(StringUtils.objectToString(this.selObjects[2]))) {
            JsfUtil.addErrorMessage("该数据状态已更新，请刷新页面！");
            this.selObjects = null;
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ReUnloadDataDialog').show();");
    }

    /**
     * 重新上传
     */
    public void reUploadData() {
        if (ObjectUtil.isEmpty(this.selObjects)) {
            return;
        }
        List<Object[]> objectList = new ArrayList<>();
        objectList.add(this.selObjects);
        doUploadData(objectList);
    }

    /**
     * <p>方法描述：全部上传</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-09-23
     **/
    public void allUploadData() {
        List<Object[]> dataList = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(buildSql(2), this.paramMap));
        doUploadData(dataList);
    }

    /**
     * 上传数据
     *
     * @param dataList 待处理数据
     */
    private void doUploadData(List<Object[]> dataList) {
        //全部上传
        List<Integer> state3RidList = new ArrayList<>();
        List<Integer> state5RidList = new ArrayList<>();
        List<Integer> stateUpRidList = new ArrayList<>();
        //上传失败
        List<String> state3 = this.upCountryStateMap.get("3");
        //删除失败
        List<String> state5 = this.upCountryStateMap.get("5");
        for (Object[] objects : dataList) {
            if (!CollectionUtils.isEmpty(state3) && objects[0] != null && objects[2] != null && state3.contains(objects[2].toString())) {
                state3RidList.add(Integer.parseInt(objects[1].toString()));
                stateUpRidList.add(Integer.parseInt(objects[0].toString()));
            }
            if (!CollectionUtils.isEmpty(state5) && objects[0] != null && objects[2] != null && state5.contains(objects[2].toString())) {
                state5RidList.add(Integer.parseInt(objects[1].toString()));
                stateUpRidList.add(Integer.parseInt(objects[0].toString()));
            }
        }
        try {
            hidtService.batchUpdateAllState(state3RidList, state5RidList, stateUpRidList, ifNeedBhkAudit);
            this.searchAction();
            if (new Integer(2).equals(this.uploadActionType)) {
                JsfUtil.addSuccessMessage("处理成功" + stateUpRidList.size() + "条！");
            } else {
                JsfUtil.addSuccessMessage("重新上传成功！");
            }
        } catch (Exception e) {
            if (new Integer(2).equals(this.uploadActionType)) {
                JsfUtil.addErrorMessage("处理失败" + stateUpRidList.size() + "条！");
            } else {
                JsfUtil.addErrorMessage("重新上传失败！");
            }
            e.printStackTrace();
        }
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {
        //初始化国家失败原因
        List<String> upCountryStateList = new ArrayList<>();
        upCountryStateList.addAll(this.upCountryStateMap.get("3"));
        upCountryStateList.addAll(this.upCountryStateMap.get("5"));
        Object[] objects = this.hidtService.findUpCountryStatusByBusId(this.rid);
        if (ObjectUtil.isEmpty(objects) || ObjectUtil.isEmpty(objects[2])) {
            this.showNationErrorMsg = false;
            this.nationErrorMsg = "";
        } else {
            this.showNationErrorMsg = upCountryStateList.contains(StringUtils.objectToString(objects[2]));
            if (ObjectUtil.isNotEmpty(objects[3])) {
                this.nationErrorMsg = StringUtils.objectToString(objects[3]);
            } else {
                this.nationErrorMsg = "无";
            }
        }
        //获取历次审核意见
        this.historyList = this.hethBaseCommService.findHisotryList(this.rid, 9);
        //初始化历次审核意见
        this.initHistoryCheckout();
        //初始化体检基本信息
        this.tjBhkInfoBean.setIfShowBaseOnly(false);
        this.tjBhkInfoBean.setRid(this.rid);
        this.tjBhkInfoBean.setArchivePanel(this.archivePanel);
        this.tjBhkInfoBean.setIfManagedOrg(false);
        this.tjBhkInfoBean.initBhkInfo();
        this.tdTjBhk = this.hethStaQueryCommService.findTdTjBhkById(this.rid);
        this.unAbnormalsList = this.hethBaseCommService.initUnAbnormalsList(this.tdTjBhk);
    }

    private void initHistoryCheckout() {
        if (!CollectionUtils.isEmpty(this.historyList)) {
            List<Object[]> checkOutList = new ArrayList<>();
            for (Object[] objArr : this.historyList) {
                int opegFlag = null == objArr[7] ? 0 : Integer.parseInt(objArr[7].toString());
                String type = null;
                //审核级别为3
                if ("3".equals(this.checkLevel)) {
                    Integer ifCityDirect = null == objArr[5] ? null : Integer.parseInt(objArr[5].toString());
                    //根据是否市直属判断审核类型
                    if (Integer.valueOf(1).equals(ifCityDirect)) {
                        switch (opegFlag) {
                            case 41:
                                type = "市级审核通过";
                                break;
                            case 13:
                                type = "市级审核退回";
                                break;
                            case 42:
                                type = "省级审核通过";
                                break;
                            case 32:
                                type = "省级审核退回";
                                break;
                            default:
                                break;
                        }
                    } else {
                        switch (opegFlag) {
                            case 31:
                                type = "区县级审核通过";
                                break;
                            case 11:
                                type = "区县级审核退回";
                                break;
                            case 41:
                                type = "市级审核通过";
                                break;
                            case 22:
                                type = "市级审核退回";
                                break;
                            case 42:
                                type = "省级审核通过";
                                break;
                            case 32:
                                type = "省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }
                }
                //审核级别为2
                if ("2".equals(this.checkLevel)) {
                    Integer ifProvDirect = null == objArr[6] ? null : Integer.parseInt(objArr[6].toString());
                    //根据是否省直属判断审核类型
                    if (Integer.valueOf(1).equals(ifProvDirect)) {
                        switch (opegFlag) {
                            case 42:
                                type = "省级审核通过";
                                break;
                            case 32:
                                type = "省级审核退回";
                                break;
                            default:
                                break;
                        }
                    } else {
                        switch (opegFlag) {
                            case 43:
                                type = "区县级审核通过";
                                break;
                            case 11:
                                type = "区县级审核退回";
                                break;
                            case 42:
                                type = "省级审核通过";
                                break;
                            case 32:
                                type = "省级审核退回";
                                break;
                            default:
                                break;
                        }
                    }
                }
                objArr[4] = type;
                if (type != null) {
                    checkOutList.add(objArr);
                }
            }
            this.historyList = checkOutList;
        }
    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    public CommServiceImpl getCommService() {
        return commService;
    }

    public HethInspectDataTrackService getHidtService() {
        return hidtService;
    }

    public HethStaQueryCommServiceImpl getHethStaQueryCommService() {
        return hethStaQueryCommService;
    }

    public HethBaseCommServiceImpl getHethBaseCommService() {
        return hethBaseCommService;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdTjBhk getTdTjBhk() {
        return tdTjBhk;
    }

    public void setTdTjBhk(TdTjBhk tdTjBhk) {
        this.tdTjBhk = tdTjBhk;
    }

    public Object[] getSelObjects() {
        return selObjects;
    }

    public void setSelObjects(Object[] selObjects) {
        this.selObjects = selObjects;
    }

    public Integer getUploadActionType() {
        return uploadActionType;
    }

    public void setUploadActionType(Integer uploadActionType) {
        this.uploadActionType = uploadActionType;
    }

    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public String getSearchCheckOrgZoneGb() {
        return searchCheckOrgZoneGb;
    }

    public void setSearchCheckOrgZoneGb(String searchCheckOrgZoneGb) {
        this.searchCheckOrgZoneGb = searchCheckOrgZoneGb;
    }

    public String getSearchCheckOrgZoneName() {
        return searchCheckOrgZoneName;
    }

    public void setSearchCheckOrgZoneName(String searchCheckOrgZoneName) {
        this.searchCheckOrgZoneName = searchCheckOrgZoneName;
    }

    public String getSearchCheckOrgId() {
        return searchCheckOrgId;
    }

    public void setSearchCheckOrgId(String searchCheckOrgId) {
        this.searchCheckOrgId = searchCheckOrgId;
    }

    public String getSearchCheckOrgName() {
        return searchCheckOrgName;
    }

    public void setSearchCheckOrgName(String searchCheckOrgName) {
        this.searchCheckOrgName = searchCheckOrgName;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchPsnName() {
        return searchPsnName;
    }

    public void setSearchPsnName(String searchPsnName) {
        this.searchPsnName = searchPsnName;
    }

    public String[] getSearchBhkTypes() {
        return searchBhkTypes;
    }

    public void setSearchBhkTypes(String[] searchBhkTypes) {
        this.searchBhkTypes = searchBhkTypes;
    }

    public String[] getSearchJcTypes() {
        return searchJcTypes;
    }

    public void setSearchJcTypes(String[] searchJcTypes) {
        this.searchJcTypes = searchJcTypes;
    }

    public List<TsSimpleCode> getOnGuardList() {
        return onGuardList;
    }

    public void setOnGuardList(List<TsSimpleCode> onGuardList) {
        this.onGuardList = onGuardList;
    }

    public String getSelectOnGuardNames() {
        return selectOnGuardNames;
    }

    public void setSelectOnGuardNames(String selectOnGuardNames) {
        this.selectOnGuardNames = selectOnGuardNames;
    }

    public String getSelectOnGuardIds() {
        return selectOnGuardIds;
    }

    public void setSelectOnGuardIds(String selectOnGuardIds) {
        this.selectOnGuardIds = selectOnGuardIds;
    }

    public List<TsSimpleCode> getBadRsnList() {
        return badRsnList;
    }

    public void setBadRsnList(List<TsSimpleCode> badRsnList) {
        this.badRsnList = badRsnList;
    }

    public String getSelectBadRsnNames() {
        return selectBadRsnNames;
    }

    public void setSelectBadRsnNames(String selectBadRsnNames) {
        this.selectBadRsnNames = selectBadRsnNames;
    }

    public String getSelectBadRsnIds() {
        return selectBadRsnIds;
    }

    public void setSelectBadRsnIds(String selectBadRsnIds) {
        this.selectBadRsnIds = selectBadRsnIds;
    }

    public Date getSearchBhkBeginDate() {
        return searchBhkBeginDate;
    }

    public void setSearchBhkBeginDate(Date searchBhkBeginDate) {
        this.searchBhkBeginDate = searchBhkBeginDate;
    }

    public Date getSearchBhkEndDate() {
        return searchBhkEndDate;
    }

    public void setSearchBhkEndDate(Date searchBhkEndDate) {
        this.searchBhkEndDate = searchBhkEndDate;
    }

    public Date getSearchRptPrintBeginDate() {
        return searchRptPrintBeginDate;
    }

    public void setSearchRptPrintBeginDate(Date searchRptPrintBeginDate) {
        this.searchRptPrintBeginDate = searchRptPrintBeginDate;
    }

    public Date getSearchRptPrintEndDate() {
        return searchRptPrintEndDate;
    }

    public void setSearchRptPrintEndDate(Date searchRptPrintEndDate) {
        this.searchRptPrintEndDate = searchRptPrintEndDate;
    }

    public String[] getSearchIfRhks() {
        return searchIfRhks;
    }

    public void setSearchIfRhks(String[] searchIfRhks) {
        this.searchIfRhks = searchIfRhks;
    }

    public String[] getSearchIfAbnormals() {
        return searchIfAbnormals;
    }

    public void setSearchIfAbnormals(String[] searchIfAbnormals) {
        this.searchIfAbnormals = searchIfAbnormals;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public Map<String, String> getStateMap() {
        return stateMap;
    }

    public void setStateMap(Map<String, String> stateMap) {
        this.stateMap = stateMap;
    }

    public String[] getSearchStates() {
        return searchStates;
    }

    public void setSearchStates(String[] searchStates) {
        this.searchStates = searchStates;
    }

    public List<SelectItem> getUpCountryStateList() {
        return upCountryStateList;
    }

    public void setUpCountryStateList(List<SelectItem> upCountryStateList) {
        this.upCountryStateList = upCountryStateList;
    }

    public Map<String, List<String>> getUpCountryStateMap() {
        return upCountryStateMap;
    }

    public void setUpCountryStateMap(Map<String, List<String>> upCountryStateMap) {
        this.upCountryStateMap = upCountryStateMap;
    }

    public String[] getSearchUpCountryStates() {
        return searchUpCountryStates;
    }

    public void setSearchUpCountryStates(String[] searchUpCountryStates) {
        this.searchUpCountryStates = searchUpCountryStates;
    }

    public String getSelectErrMsg() {
        return selectErrMsg;
    }

    public void setSelectErrMsg(String selectErrMsg) {
        this.selectErrMsg = selectErrMsg;
    }

    public String getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(String checkLevel) {
        this.checkLevel = checkLevel;
    }

    public Boolean getCheckLevel2() {
        return checkLevel2;
    }

    public void setCheckLevel2(Boolean checkLevel2) {
        this.checkLevel2 = checkLevel2;
    }

    public Boolean getCheckLevel3() {
        return checkLevel3;
    }

    public void setCheckLevel3(Boolean checkLevel3) {
        this.checkLevel3 = checkLevel3;
    }

    public Boolean getIfNeedBhkAudit() {
        return ifNeedBhkAudit;
    }

    public void setIfNeedBhkAudit(Boolean ifNeedBhkAudit) {
        this.ifNeedBhkAudit = ifNeedBhkAudit;
    }

    public Boolean getShowNationErrorMsg() {
        return showNationErrorMsg;
    }

    public void setShowNationErrorMsg(Boolean showNationErrorMsg) {
        this.showNationErrorMsg = showNationErrorMsg;
    }

    public String getNationErrorMsg() {
        return nationErrorMsg;
    }

    public void setNationErrorMsg(String nationErrorMsg) {
        this.nationErrorMsg = nationErrorMsg;
    }

    public List<Object[]> getHistoryList() {
        return historyList;
    }

    public void setHistoryList(List<Object[]> historyList) {
        this.historyList = historyList;
    }

    public TdTjBhkInfoBean getTjBhkInfoBean() {
        return tjBhkInfoBean;
    }

    public void setTjBhkInfoBean(TdTjBhkInfoBean tjBhkInfoBean) {
        this.tjBhkInfoBean = tjBhkInfoBean;
    }

    public List<String> getUnAbnormalsList() {
        return unAbnormalsList;
    }

    public void setUnAbnormalsList(List<String> unAbnormalsList) {
        this.unAbnormalsList = unAbnormalsList;
    }

    public OutputPanel getArchivePanel() {
        return archivePanel;
    }

    public void setArchivePanel(OutputPanel archivePanel) {
        this.archivePanel = archivePanel;
    }
}
