package com.chis.modules.heth.comm.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.service.HethBaseCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;

@ManagedBean(name="selectedBadRsnCommBeanTwo")
@ViewScoped
public class SelectedBadRsnCommBeanTwo {

    private HethBaseCommServiceImpl hethBaseServiceImpl = (HethBaseCommServiceImpl) SpringContextHolder
            .getBean(HethBaseCommServiceImpl.class);
    private TreeNode sortTree;
    private TreeNode[] selectedBadRsn;
    public SelectedBadRsnCommBeanTwo(){
        initBadtree();
    }
    /**
     * 添加危害因素接触史时弹出选择框<br/>
     * 初始化危害因素树
     *
     * <AUTHOR>
     * @createDate 2014年9月25日
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月25日
     */
    public void initBadtree() {
        // 清空危害因素树中已经选择的危害因素
        this.selectedBadRsn = null;

        // 根据已经选择危害因素RID查询危害因素列表
        List<TsSimpleCode> list = hethBaseServiceImpl.getSimpleCodeById("5007","");
        // 创建新的危害因素树
        this.sortTree = new DefaultTreeNode("root", null);
        // 调用创建树方法
        this.createBadRsnTree(this.sortTree, list);
    }
    /**
     * 生成危害因素树，因受管BEAN中有两处[添加危害因素接触史、生成接触危害因素父类]<br/>
     * 需求重新生成危害因素树，所以将这段代码写成方法;
     *
     * @param selectBadRsnTree
     *            生成的树
     * @param simpleCodesByType_5010
     *            危害因素集合列表
     * <AUTHOR>
     * @createDate 2014年9月25日
     * @LastModify LuXuekun
     * @ModifyDate 2014年9月25日
     */
    private void createBadRsnTree(TreeNode selectBadRsnTree, List<TsSimpleCode> list) {
        List<String> itmStr = new ArrayList<String>();
        String codeNoList = JsfUtil.getRequest().getParameter("codeNos");
        if (StringUtils.isNotBlank(codeNoList)) {
            String[] strings = codeNoList.split(",");
            for (String s : strings) {
                itmStr.add(s);
            }
        }

        if (null != list && list.size() > 0) {
            // 只有第一层
            Set<String> firstLevelNoSet = new LinkedHashSet<String>();
            // 没有第一层
            Set<String> levelNoSet = new LinkedHashSet<String>();
            // 所有类别
            Map<String, TsSimpleCode> menuMap = new HashMap<String, TsSimpleCode>();
            for (TsSimpleCode t : list) {
                menuMap.put(t.getCodeLevelNo(), t);
                if (StringUtils.isNotBlank(t.getCodeLevelNo())) {
                    if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
                        firstLevelNoSet.add(t.getCodeLevelNo());
                    } else {
                        levelNoSet.add(t.getCodeLevelNo());
                    }
                }
            }
            // 由第一层开始遍历
            for (String ln : firstLevelNoSet) {
                TreeNode node = new DefaultTreeNode(menuMap.get(ln), selectBadRsnTree);
                this.addChildNode(ln, levelNoSet, menuMap, node, itmStr);
            }
            menuMap.clear();
        }

        // 去掉没有子集节点的树，如果危害因素树中的危害因素大类没有小类子集则不显示大类
        List<TreeNode> children = selectBadRsnTree.getChildren();
        for (int i = 0; i < children.size(); i++) {
            // 判断子集合大小
            int childCount = children.get(i).getChildCount();
            if (childCount <= 0) {
                selectBadRsnTree.getChildren().remove(i);
                i--;
            }
            // 是否选中
//			if(){
            boolean flag = true;

            for (TreeNode child: children.get(i).getChildren()) {
                if(!child.isSelected()){
                    flag = false;
                    break;
                }
            }
            if(flag){
                children.get(i).setSelected(true);
            }
//			}

        }
    }
    /**
     * 构建类别树递归方法
     *
     * @param levelNo
     *            类别层级编码
     * @param levelNoSet
     *            二级以及以上的菜单的类别编码集合
     * @param menuMap
     *            类别map
     * @param parentNode
     *            上级树节点
     */
    private void addChildNode(String levelNo, Set<String> levelNoSet, Map<String, TsSimpleCode> menuMap,
                              TreeNode parentNode, List<String> codeNoList) {
        int level = StringUtils.countMatches(levelNo, ".");
        for (String ln : levelNoSet) {
            if (StringUtils.countMatches(ln, ".") == (level + 1) && StringUtils.startsWith(ln, levelNo + ".")) {
                TreeNode node = new DefaultTreeNode(menuMap.get(ln), parentNode);
                for(String str : codeNoList) {
                    if(ln.equals(str)) {
                        node.setSelected(true);
                    }
                }
                this.addChildNode(ln, levelNoSet, menuMap, node, codeNoList);
            }
        }
    }
    //提交
    public void submitAction(){
        if (null == this.selectedBadRsn ||selectedBadRsn.length==0) {
            JsfUtil.addErrorMessage("请选择危害因素");
            return;
        }
        List<TsSimpleCode> result = new ArrayList<>();
        // 遍历已经选择的列表，并将选择危害因素转换成危害接触史子表对象
        for (TreeNode tree : this.selectedBadRsn) {
            TreeNode parent = tree.getParent();
            if (!"root".equals(parent.getData())) {
                TsSimpleCode simpleCode = (TsSimpleCode) tree.getData();
                result.add(simpleCode);
            }
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("badfactorList", result);
        RequestContext.getCurrentInstance().closeDialog(map);
    }
    //关闭
    public void dialogClose(){
        RequestContext.getCurrentInstance().closeDialog(null);
    }
    public TreeNode getSortTree() {
        return sortTree;
    }
    public void setSortTree(TreeNode sortTree) {
        this.sortTree = sortTree;
    }
    public TreeNode[] getSelectedBadRsn() {
        return selectedBadRsn;
    }
    public void setSelectedBadRsn(TreeNode[] selectedBadRsn) {
        this.selectedBadRsn = selectedBadRsn;
    }
}
