package com.chis.modules.heth.comm.utils;

import com.chis.common.bean.FastReportData;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.rptvo.YszybRptCardVo;
import com.chis.modules.heth.comm.rptvo.ZybRptCardVo;
import com.chis.modules.heth.comm.service.ZwReportCardCheckCommServiceImpl;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class RptCardTempCommUtil {

	private static ZwReportCardCheckCommServiceImpl cardCheckServiceImpl = SpringContextHolder.getBean(ZwReportCardCheckCommServiceImpl.class);

	public static List<FastReportData> packageDatas(String code,Integer rid) {

		List<FastReportData> rptDataList = new ArrayList<FastReportData>();
		if (rid == null) {
			return rptDataList;
		}
		switch (code) {
		case "HETH_2031":
			packReportData2031(rid, rptDataList);
			break;
		case "HETH_2035":
			packReportData2035(rid, rptDataList);
			break;
		}	
		
		return rptDataList;
	}

	/**
	 * 职业病报告卡
	 * TD_ZW_OCCDIS_CARD_NEW 职业病报告卡[报告卡审核]
	 * TdZwOccdisCardNew 信息主体
	 * TD_ZW_OCCDIS_CARD_BADRSN 危害因素
	 * */
	private static void packReportData2035(Integer rid, List<FastReportData> rptDataList){
		if(null == rid){
			return;
		}
		String hql = "SELECT T FROM TdZwOccdisCardNew T WHERE T.mainId="+rid.intValue();
		TdZwOccdisCardNew occdisCardNew = cardCheckServiceImpl.findOneByHql(hql,TdZwOccdisCardNew.class);
		if(null == occdisCardNew){
			return;
		}
		List<TdZwOccdisCardBadrsn> badrsnList = cardCheckServiceImpl.
				findEntityListByMainId(TdZwOccdisCardBadrsn.class, occdisCardNew.getRid());

		List<ZybRptCardVo> rptCardVoList = new ArrayList<>();
		ZybRptCardVo cardVo = new ZybRptCardVo();
		cardVo.setPersonalName(occdisCardNew.getPersonnelName());
		cardVo.setRptNo(occdisCardNew.getRptNo());
		cardVo.setCardType(null == occdisCardNew.getFkByCardTypeId() ? "" :
				occdisCardNew.getFkByCardTypeId().getCodeName());
		cardVo.setIdcard(occdisCardNew.getIdc());
		String sex = null == occdisCardNew.getSex() ? "" :
				(occdisCardNew.getSex().equals("1") || occdisCardNew.getSex().equals("男")) ? "男" : "女";
		cardVo.setSex(sex);
		cardVo.setBirthday(null == occdisCardNew.getBirthday() ? "" :
				DateUtils.formatDate(occdisCardNew.getBirthday(), "yyyy-MM-dd"));
		cardVo.setPersonalTel(occdisCardNew.getLinktel());
		cardVo.setPersonLinkMan(occdisCardNew.getEmergLinkName());
		cardVo.setPersonLinkTel(occdisCardNew.getEmergLinktel());

		cardVo.setCrptName(occdisCardNew.getCrptName());
		cardVo.setUnitCode(occdisCardNew.getCreditCode());
		cardVo.setEconomy(null == occdisCardNew.getFkByEconomyId() ? "" :
				occdisCardNew.getFkByEconomyId().getCodeName());
		cardVo.setIndusType(null == occdisCardNew.getFkByIndusTypeId() ? "" :
				occdisCardNew.getFkByIndusTypeId().getCodeName());
		cardVo.setCrptSize(null == occdisCardNew.getFkByCrptSizeId() ? "" :
				occdisCardNew.getFkByCrptSizeId().getCodeName());
		cardVo.setZoneName(null == occdisCardNew.getFkByZoneId() ? "" :
				occdisCardNew.getFkByZoneId().getFullName().replace("_",""));
		cardVo.setLinkAddr(occdisCardNew.getAddress());
		cardVo.setPostCode(occdisCardNew.getPostcode());
		cardVo.setCrptLinkMan(occdisCardNew.getSafeposition());
		cardVo.setCrptLinkTel(occdisCardNew.getSafephone());

		cardVo.setApplyDiagDate(null == occdisCardNew.getApplyDate() ? "" :
				DateUtils.formatDate(occdisCardNew.getApplyDate(), "yyyy-MM-dd"));

		String type = null == occdisCardNew.getFkByZybTypeId() ? "3" :
				null == occdisCardNew.getFkByZybTypeId().getExtendS4() ? "3" :
						occdisCardNew.getFkByZybTypeId().getExtendS4().equals("1") ? "1" :
								occdisCardNew.getFkByZybTypeId().getExtendS4().equals("2") ? "2" : "3";
		//诊断日期 确认一下 是否尘肺病的 不需要诊断日期 因存在一期二期 三期日期
		if(type.equals("1")){
			cardVo.setDiagDate1(null == occdisCardNew.getDiag1Date() ? "" :
					DateUtils.formatDate(occdisCardNew.getDiag1Date(), "yyyy-MM-dd"));
			cardVo.setDiagDate2(null == occdisCardNew.getDiag2Date() ? "" :
					DateUtils.formatDate(occdisCardNew.getDiag2Date(), "yyyy-MM-dd"));
			cardVo.setDiagDate3(null == occdisCardNew.getDiag3Date() ? "" :
					DateUtils.formatDate(occdisCardNew.getDiag3Date(), "yyyy-MM-dd"));
		}
		cardVo.setDiagDate(null == occdisCardNew.getDiagDate() ? "" :
				DateUtils.formatDate(occdisCardNew.getDiagDate(), "yyyy-MM-dd"));
		String typeName = null == occdisCardNew.getFkByZybTypeId() ?  "无" : occdisCardNew.getFkByZybTypeId().getCodeName();
		if(null != occdisCardNew.getFkByZybTypeId()
				&& null != occdisCardNew.getFkByZybTypeId().getExtendS2()
				&& 1 == occdisCardNew.getFkByZybTypeId().getExtendS2()
				&& null != occdisCardNew.getZybDisName()){
			typeName = typeName+"("+occdisCardNew.getZybDisName()+")";
		}
		cardVo.setTypeName(typeName);
		cardVo.setDisTypeName(null == occdisCardNew.getFkByZybDisTypeId() ? "无" :
				occdisCardNew.getFkByZybDisTypeId().getCodeName());

		if(type.equals("2")){
			String poisonTypeName = null == occdisCardNew.getZyPoisonType() ? "" :
					1 == occdisCardNew.getZyPoisonType() ? "急性" : 2 == occdisCardNew.getZyPoisonType() ? "慢性" :"";
			//职业性化学中毒分类
			cardVo.setPoisonType(poisonTypeName);
		}

		cardVo.setRptTypeName(null == occdisCardNew.getFkByRptTypeId() ? "" :
				occdisCardNew.getFkByRptTypeId().getCodeName());
		cardVo.setWorkTypeName(null == occdisCardNew.getFkByWorkTypeId() ? "" :
				(null != occdisCardNew.getFkByWorkTypeId().getExtendS1() &&
						occdisCardNew.getFkByWorkTypeId().getExtendS1().equals("1")) ?
						occdisCardNew.getFkByWorkTypeId().getCodeName()+"("+occdisCardNew.getWorkOther()+")" : occdisCardNew.getFkByWorkTypeId().getCodeName() );

		StringBuffer buffer = new StringBuffer();
		//接触的职业性有害因素
		if(!CollectionUtils.isEmpty(badrsnList)){
			for(TdZwOccdisCardBadrsn badrsn : badrsnList){
				if(null != badrsn.getFkByBadrsnId()){
					buffer.append("，").append(badrsn.getFkByBadrsnId().getCodeName());
				}
			}
		}
		cardVo.setBadRsns((null != buffer && buffer.length() > 1) ? buffer.substring(1) : buffer.toString());
		cardVo.setDeathDate(null == occdisCardNew.getDeathDate() ? "" :
				DateUtils.formatDate(occdisCardNew.getDeathDate(), "yyyy-MM-dd"));
		cardVo.setDieRsn(occdisCardNew.getDieRsn());
		cardVo.setBegTchDate(null == occdisCardNew.getBegTchDust() ? "" :
				DateUtils.formatDate(occdisCardNew.getBegTchDust(), "yyyy-MM-dd"));
		cardVo.setTchDustYear(null == occdisCardNew.getTchDustYear() ? "0" : occdisCardNew.getTchDustYear().toString());
		cardVo.setTchDustMonth(null == occdisCardNew.getTchDustMonth() ? "0" : occdisCardNew.getTchDustMonth().toString());
		cardVo.setTchDustDays(null == occdisCardNew.getTchDays() ? "0" : occdisCardNew.getTchDays().toString());
		cardVo.setTchHours(null == occdisCardNew.getTchHours() ? "0" : occdisCardNew.getTchHours().toString());
		cardVo.setTchMinutes(null == occdisCardNew.getTchMinutes() ? "0" : occdisCardNew.getTchMinutes().toString());
		cardVo.setIftb(null == occdisCardNew.getIfTb() ? "" : 1 == occdisCardNew.getIfTb() ? "1" : "0");//是否肺结核 1是 0否
		cardVo.setIfPulInfection(null == occdisCardNew.getIfPulInfection() ? "" :
				1 == occdisCardNew.getIfPulInfection() ? "1" : "0");//是否肺及支气管感染
		cardVo.setIfPneum(null == occdisCardNew.getIfThePneum() ? "" : 1 == occdisCardNew.getIfThePneum() ? "1" : "0");//是否自发性气胸
		cardVo.setIfPulHeart(null == occdisCardNew.getIfPulHeart() ? "" : 1 == occdisCardNew.getIfPulHeart() ? "1" : "0");//是否肺心病
		cardVo.setIfLungCa(null == occdisCardNew.getIfLungCancer() ? "" : 1 == occdisCardNew.getIfLungCancer() ? "1" : "0");//是否肺癌
		cardVo.setDiagUnitName(occdisCardNew.getDiagUnitName());
		cardVo.setDiagRespPsn(occdisCardNew.getDiagRespPsn());

		cardVo.setFillFormPsn(occdisCardNew.getFillFormPsn());
		cardVo.setFillLink(occdisCardNew.getFillLink());
		cardVo.setFillDate(null == occdisCardNew.getFillDate() ? "" :
				DateUtils.formatDate(occdisCardNew.getFillDate(), "yyyy-MM-dd"));
		cardVo.setFillUnit(occdisCardNew.getFillUnitName());

		cardVo.setRptPsn(occdisCardNew.getRptPsn());
		cardVo.setRptLink(occdisCardNew.getRptLink());
		cardVo.setRptDate(null == occdisCardNew.getRptDate() ? "" :
				DateUtils.formatDate(occdisCardNew.getRptDate(), "yyyy-MM-dd"));
		cardVo.setDiagUnit(occdisCardNew.getRptUnitName());
		cardVo.setRmk(occdisCardNew.getRmk());
		//1 尘肺病 2职业中毒 3其他
		cardVo.setType(type);
		rptCardVoList.add(cardVo);
		//用工单位
		cardVo.setEmpCrptName(occdisCardNew.getEmpCrptName());
		cardVo.setEmpUnitCode(occdisCardNew.getEmpCreditCode());
		if(occdisCardNew.getFkByEmpZoneId()!=null && occdisCardNew.getFkByEmpZoneId().getRid()!=null){
			String fullName = occdisCardNew.getFkByEmpZoneId().getFullName();
			if (null != fullName) {
				String replace = fullName.replace("_", "");
				cardVo.setEmpZoneName(replace);
			}
		}
		if(occdisCardNew.getFkByEmpIndusTypeId()!=null && occdisCardNew.getFkByEmpIndusTypeId().getRid()!=null){
			cardVo.setEmpIndusType(occdisCardNew.getFkByEmpIndusTypeId().getCodeName());
		}
		if(occdisCardNew.getFkByEmpEconomyId()!=null && occdisCardNew.getFkByEmpEconomyId().getRid()!=null){
			cardVo.setEmpEconomy(occdisCardNew.getFkByEmpEconomyId().getCodeName());
		}
		if(occdisCardNew.getFkByEmpCrptSizeId()!=null && occdisCardNew.getFkByEmpCrptSizeId().getRid()!=null){
			cardVo.setEmpCrptSize(occdisCardNew.getFkByEmpCrptSizeId().getCodeName());
		}
		rptDataList.add(new FastReportData<>(ZybRptCardVo.class,
				"zybRptCardVo", rptCardVoList));

		List<ZybRptCardVo> rptCardVoPenumList = new ArrayList<>();
		List<ZybRptCardVo> rptCardVoChemicalList = new ArrayList<>();
		List<ZybRptCardVo> rptCardVoNormalList = new ArrayList<>();
		if("1".equals(cardVo.getType())){
			rptCardVoPenumList.add(cardVo);
		}else if("2".equals(cardVo.getType())){
			rptCardVoChemicalList.add(cardVo);
		}else if("3".equals(cardVo.getType())){
			rptCardVoNormalList.add(cardVo);
		}
		rptDataList.add(new FastReportData<>(ZybRptCardVo.class,
				"zybPenumRptCardVo", rptCardVoPenumList));
		rptDataList.add(new FastReportData<>(ZybRptCardVo.class,
				"zybChemicalRptCardVo", rptCardVoChemicalList));
		rptDataList.add(new FastReportData<>(ZybRptCardVo.class,
				"zybNormalRptCardVo", rptCardVoNormalList));
	}
	/**
	 * <p>
	 * 方法描述：疑似职业病报告卡
	 * </p>
	 * TD_ZW_PNEUMSIS_CARD
	 * 
	 * @MethodAuthor rcj,2019年9月5日,packReportData2031
	 * @param rid
	 * @param rptDataList
	 */
	private static void packReportData2031(Integer rid,
			List<FastReportData> rptDataList) {
		if (null != rid) {

			TdZwYszybRpt rpt = cardCheckServiceImpl.find(TdZwYszybRpt.class, rid);

			if (null != rpt) {
				YszybRptCardVo vo = new YszybRptCardVo();
				vo.setRptNo(rpt.getRptNo());
				vo.setPersonalName(rpt.getPersonnelName());
				if (null != rpt.getFkByCardTypeId()) {
					vo.setCardType(rpt.getFkByCardTypeId().getCodeName());
				}
				vo.setIdcard(rpt.getIdc());
				vo.setSex("1".equals(String.valueOf(rpt.getSex())) ? "男" : "女");
				vo.setBirthday(rpt.getBirthday() == null ? null: DateUtils.getChineseStringDate(rpt.getBirthday()));
				vo.setPersonalTel(rpt.getLinktel());
				vo.setReportYear(String.valueOf(rpt.getRptYear()));
				vo.setUnitCode(rpt.getCreditCode());

				if ( null != rpt.getFkByZoneId() && rpt.getFkByZoneId().getRid()!=null) {
					String fullName = rpt.getFkByZoneId().getFullName();
					if (null != fullName) {
						String replace = fullName.replace("_", "");
						vo.setZoneName(replace);
					}
				}
				vo.setCrptName(rpt.getCrptName());
				vo.setPostCode(rpt.getPostcode());
				vo.setCrptLinkMan(rpt.getSafeposition());
				vo.setCrptLinkTel(rpt.getSafephone());
				vo.setLinkAddr(rpt.getAddress());

				if (null != rpt.getFkByEconomyId() && rpt.getFkByEconomyId().getRid()!=null) {
					vo.setEconomy(rpt.getFkByEconomyId().getCodeName());
				}
				if (null != rpt.getFkByIndusTypeId() && rpt.getFkByIndusTypeId().getRid()!=null) {
					vo.setIndusType(rpt.getFkByIndusTypeId().getCodeName());
				}
				if (null != rpt.getFkByCrptSizeId() && rpt.getFkByCrptSizeId().getRid()!=null) {
					vo.setCrptSize(rpt.getFkByCrptSizeId().getCodeName());
				}
				if (null != rpt.getFkByWorkTypeId()) {
					String workType = rpt.getFkByWorkTypeId().getCodeName();
					vo.setAnalyWork(workType);
					if("1".equals(rpt.getFkByWorkTypeId().getExtendS1())){//其他
						vo.setAnalyWork(workType+"（"+rpt.getWorkOther()+"）");
					}
				}
				vo.setTchWorkYear(rpt.getTchWorkYear() == null ? null:rpt.getTchWorkYear().toString());
				vo.setTchWorkMonth(rpt.getTchWorkMonth() == null ? null:rpt.getTchWorkMonth().toString());
				vo.setTchWorkDay(rpt.getTchWorkDay() == null ? null:rpt.getTchWorkDay().toString());
				vo.setTchWorkHour(rpt.getTchWorkHour() == null ? null:rpt.getTchWorkHour().toString());
				vo.setTchWorkMinute(rpt.getTchWorkMinute() == null ? null:rpt.getTchWorkMinute().toString());
				
				if (null != rpt.getFkByOccDiseid()) {
					vo.setSuspectedName(rpt.getFkByOccDiseid().getCodeName());
				}
				vo.setYszybTypeName(rpt.getYszybTypeName());
				if(null!=rpt.getFkBySourceId()){
					vo.setInfoSource(rpt.getFkBySourceId().getCodeName());
					if("9".equals(rpt.getFkBySourceId().getExtendS1())){//其他
						vo.setOtherInfoSource("（"+rpt.getOtherSource()+"）");
					}
				}
				if(null!=rpt.getZyPoisonType()){
					if(1==rpt.getZyPoisonType()){
						vo.setZsPoisonName("急性");
					}else if (2==rpt.getZyPoisonType()) {
						vo.setZsPoisonName("慢性");
					}
				}
				//用工单位
				vo.setEmpCrptName(rpt.getEmpCrptName());
				vo.setEmpUnitCode(rpt.getEmpCreditCode());
				if(rpt.getFkByEmpZoneId()!=null && rpt.getFkByEmpZoneId().getRid()!=null){
					String fullName = rpt.getFkByEmpZoneId().getFullName();
					if (null != fullName) {
						String replace = fullName.replace("_", "");
						vo.setEmpZoneName(replace);
					}
				}
				if(rpt.getFkByEmpIndusTypeId()!=null && rpt.getFkByEmpIndusTypeId().getRid()!=null){
					vo.setEmpIndusType(rpt.getFkByEmpIndusTypeId().getCodeName());
				}
				if(rpt.getFkByEmpEconomyId()!=null && rpt.getFkByEmpEconomyId().getRid()!=null){
					vo.setEmpEconomy(rpt.getFkByEmpEconomyId().getCodeName());
				}
				if(rpt.getFkByEmpCrptSizeId()!=null && rpt.getFkByEmpCrptSizeId().getRid()!=null){
					vo.setEmpCrptSize(rpt.getFkByEmpCrptSizeId().getCodeName());
				}


				String str = "select t from TdZwYszybTchBadrsn t where t.fkByMainId.rid = "+ rid + " ORDER BY t.fkByBadrsnId.num,t.fkByBadrsnId.codeLevelNo,t.fkByBadrsnId.codeNo";
				List<TdZwYszybTchBadrsn> list = cardCheckServiceImpl.findByHql(str);
				if(CollectionUtils.isEmpty(list)){
					vo.setBanrsns(null);
				}else {
					//可能接触的主要职业性有害因
					StringBuilder s = new StringBuilder();
					for(TdZwYszybTchBadrsn tchBadrsn : list){
						s.append("，").append(tchBadrsn.getFkByBadrsnId().getCodeName());
					}
					vo.setBanrsns(s.substring(1,s.length()));
				}
				vo.setFindDate(rpt.getFindDate() == null ? null:DateUtils.getChineseStringDate(rpt.getFindDate()));
				vo.setHarmStartDate(rpt.getHarmStartDate() == null ? null:DateUtils.getChineseStringDate(rpt.getHarmStartDate()));
				
				vo.setDiscoveryUnit(rpt.getDiscoveryUnit());
				vo.setDiscoveryPsn(rpt.getDiscoveryRespPsn());
				
				vo.setFillFormPsn(rpt.getFillFormPsn());
				vo.setFillLink(rpt.getFillLink());
				vo.setFillDate(rpt.getFillDate() == null ? null:DateUtils.getChineseStringDate(rpt.getFillDate()));
				if(null!=rpt.getFkRptUnitId()){
					//变更 报告单位和填报单位均取RPT_UNIT_ID字段 2022年4月26日 13:47:04
					vo.setFillUnit(rpt.getFkRptUnitId().getUnitname());
					vo.setDiagUnit(rpt.getFkRptUnitId().getUnitname());
				}
				
				vo.setRptPsn(rpt.getRptPsn());
				vo.setRptLink(rpt.getRptLink());
				vo.setRptDate(rpt.getRptDate() == null ? null:DateUtils.getChineseStringDate(rpt.getRptDate()));
				
				vo.setRmk(rpt.getRmk());
				
				List<YszybRptCardVo> suspectedCardVos = new ArrayList<>();
				suspectedCardVos.add(vo);
				rptDataList.add(new FastReportData<>(YszybRptCardVo.class,
						"yszybRptCardVo", suspectedCardVos));
			}

		}

	}
}
