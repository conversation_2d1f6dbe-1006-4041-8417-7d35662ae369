package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbTjItems;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesBean;
import org.apache.commons.collections.CollectionUtils;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* <p>Description：体检项目-选择弹出框 </p>
* <p>Author： yzz 2024-05-07 </p>
*/
@ManagedBean(name = "tbTjItemsSelectListComm2Bean")
@ViewScoped
public class TbTjItemsSelectListComm2Bean extends FacesBean {

    /**查询条件：业务分类*/
    private String itemSortId;
    private List<TsSimpleCode> itemSortList;
    private Map<String, List<TsSimpleCode>> itemSortMap;
    /**查询条件：名称或拼音码*/
    private String searchNamOrPy;

    /**表格：查询集合*/
    private List<TbTjItems> displayList;
    /**表格：所有集合*/
    private List<TbTjItems> allList;

    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    public TbTjItemsSelectListComm2Bean() {
        String selectItemIds = JsfUtil.getRequest().getParameter("selectItemIds");
        this.init(selectItemIds);
    }

    /**
    * @Description : 参数初始化
    * @MethodAuthor: anjing
    * @Date : 2019/12/19 14:05
    **/
    public void init(String selectItemIds) {
        this.displayList = new ArrayList<>();
        this.itemSortList = new ArrayList<>();
        this.itemSortMap = new HashMap<>();
        // 业务分类查询
        List<TsSimpleCode> list = this.commService.findAllSimpleCodes("5008");
        if(!CollectionUtils.isEmpty(list)) {
            String codelevelNoStr = "";
            List<TsSimpleCode> sortList = null;
            for(TsSimpleCode tsSimpleCode : list) {
                tsSimpleCode.setLevelIndex(StringUtils.countMatches(
                        tsSimpleCode.getCodeLevelNo(), ".")
                        + "");
                if(!StringUtils.containsNone(tsSimpleCode.getCodeLevelNo(), ".")) {
                    tsSimpleCode.setCodeName("    " + tsSimpleCode.getCodeName());
                    int index = tsSimpleCode.getCodeLevelNo().indexOf(".");
                    codelevelNoStr = tsSimpleCode.getCodeLevelNo().substring(0, index);
                } else {
                    codelevelNoStr = tsSimpleCode.getCodeLevelNo();
                }
                this.itemSortList.add(tsSimpleCode);
                if(null == this.itemSortMap.get(codelevelNoStr)) {
                    sortList = new ArrayList<>();
                    sortList.add(tsSimpleCode);
                    this.itemSortMap.put(codelevelNoStr, sortList);
                } else {
                    this.itemSortMap.get(codelevelNoStr).add(tsSimpleCode);
                }
            }
        }
        String hql;
        if(StringUtils.isNotBlank(selectItemIds)){
            hql="select t from TbTjItems t where t.stopTag = 1 and t.rid not in("+selectItemIds+") order by t.tsSimpleCode.num, t.num";
        }else{
            hql="select t from TbTjItems t where t.stopTag = 1 order by t.tsSimpleCode.num, t.num";
        }
        this.allList = this.commService.findByHql(hql,TbTjItems.class);
        int row = -1;
        if(!CollectionUtils.isEmpty(this.allList)) {
            int  i = 0;
            for(TbTjItems t : this.allList) {
                if (StringUtils.isNotBlank(selectItemIds) && selectItemIds.contains(t.getRid().toString())) {
                    //t.setIfSelected(true);
                    if (row == -1) {
                        row = i - i % 10;
                    }
                }
                i++;
            }

            // 初始化选择当前页的第一行数
            if (row > -1) {
                DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                        .findComponent("itemForm:selectedItemTable");
                dataTable.setFirst(row);
            }
            this.displayList.addAll(this.allList);
        }
    }

    /**
     * @Description : 执行查询
     * @MethodAuthor: anjing
     * @Date : 2019/12/19 14:10
     **/
    public void searchAction() {
        this.displayList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(this.allList)) {
            if(StringUtils.isNotBlank(this.searchNamOrPy))    {
                for(TbTjItems t : this.allList)   {
                    String itemName = StringUtils.objectToString(t.getItemName());
                    String codePym = StringUtils.objectToString(t.getPyxnam());
                    if (itemName.indexOf(this.searchNamOrPy) != -1 || codePym.toUpperCase().indexOf(this.searchNamOrPy.toUpperCase()) != -1) {
                        this.displayList.add(t);
                    }
                }
            } else {
                //码表大类
                if(StringUtils.isNotBlank(this.itemSortId)) {
                    List<String> itemSortIds = new ArrayList<>();
                    TsSimpleCode tsSimpleCode = this.commService.find(TsSimpleCode.class, Integer.valueOf(this.itemSortId));
                    if(!StringUtils.containsNone(tsSimpleCode.getCodeLevelNo(), ".")) {
                        itemSortIds.add(this.itemSortId);
                    } else {
                        List<TsSimpleCode> sortList = this.itemSortMap.get(tsSimpleCode.getCodeLevelNo());
                        if(!CollectionUtils.isEmpty(sortList)) {
                            for(TsSimpleCode t : sortList) {
                                itemSortIds.add(t.getRid().toString());
                            }
                        }

                    }
                    for(TbTjItems t : allList) {
                        if(null != t.getTsSimpleCode() && null != t.getTsSimpleCode().getRid()) {
                            for(String str : itemSortIds) {
                                if (t.getTsSimpleCode().getRid().toString().equals(str)) {
                                    this.displayList.add(t);
                                }
                            }

                        }
                    }
                } else {
                    this.displayList.addAll(allList);
                }
            }
        }
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("itemForm:selectedItemTable");
        dataTable.setFirst(0);
    }

    /**
    * @Description : 确定选择体检项目
    * @MethodAuthor: anjing
    * @Date : 2019/12/19 14:18
    **/
    public void submitAction() {
        if(!CollectionUtils.isEmpty(this.allList)) {
            List<TbTjItems> rst = new ArrayList<>();
            for(TbTjItems t : this.allList) {
                if(t.isIfSelected()) {
                    rst.add(t);
                }
            }

            if(CollectionUtils.isEmpty(rst)) {
                JsfUtil.addErrorMessage("请选择体检项目！");
                return;
            } else {
                if (rst.size() > 1000) {
                    JsfUtil.addErrorMessage("最多只能选择1000条数据！");
                    return;
                }
            }
            Map<String, List<TbTjItems>> map = new HashMap<>(0);
            map.put("selectPros", rst);
            RequestContext.getCurrentInstance().closeDialog(map);
        }
    }

    /**
    * @Description : 关闭弹出框
    * @MethodAuthor: anjing
    * @Date : 2019/12/19 14:24
    **/
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

    public String getItemSortId() {
        return itemSortId;
    }

    public void setItemSortId(String itemSortId) {
        this.itemSortId = itemSortId;
    }

    public List<TsSimpleCode> getItemSortList() {
        return itemSortList;
    }

    public void setItemSortList(List<TsSimpleCode> itemSortList) {
        this.itemSortList = itemSortList;
    }

    public String getSearchNamOrPy() {
        return searchNamOrPy;
    }

    public void setSearchNamOrPy(String searchNamOrPy) {
        this.searchNamOrPy = searchNamOrPy;
    }

    public List<TbTjItems> getDisplayList() {
        return displayList;
    }

    public void setDisplayList(List<TbTjItems> displayList) {
        this.displayList = displayList;
    }

    public List<TbTjItems> getAllList() {
        return allList;
    }

    public void setAllList(List<TbTjItems> allList) {
        this.allList = allList;
    }
}
