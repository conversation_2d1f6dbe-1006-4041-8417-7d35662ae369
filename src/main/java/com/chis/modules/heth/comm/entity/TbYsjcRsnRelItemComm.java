package com.chis.modules.heth.comm.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-8-22
 */
@Entity
@Table(name = "TB_YSJC_RSN_REL_ITEM")
@SequenceGenerator(name = "TbYsjcRsnRelItem", sequenceName = "TB_YSJC_RSN_REL_ITEM_SEQ", allocationSize = 1)
public class TbYsjcRsnRelItemComm implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbYsjcLimitValComm fkByMainId;
	private TsSimpleCode fkByItemId;
	private String itemDesc;
	private TsSimpleCode fkByMsruntId;
	private Integer num;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TbYsjcRsnRelItemComm() {
	}

	public TbYsjcRsnRelItemComm(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbYsjcRsnRelItem")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TbYsjcLimitValComm getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TbYsjcLimitValComm fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_ID")			
	public TsSimpleCode getFkByItemId() {
		return fkByItemId;
	}

	public void setFkByItemId(TsSimpleCode fkByItemId) {
		this.fkByItemId = fkByItemId;
	}	
			
	@Column(name = "ITEM_DESC")	
	public String getItemDesc() {
		return itemDesc;
	}

	public void setItemDesc(String itemDesc) {
		this.itemDesc = itemDesc;
	}	
			
	@ManyToOne
	@JoinColumn(name = "MSRUNT_ID")			
	public TsSimpleCode getFkByMsruntId() {
		return fkByMsruntId;
	}

	public void setFkByMsruntId(TsSimpleCode fkByMsruntId) {
		this.fkByMsruntId = fkByMsruntId;
	}	
			
	@Column(name = "NUM")	
	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}