package com.chis.modules.heth.comm.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.service.AbstractTemplate;

/**
 * <p>类描述：企业核实</p>
 * @ClassAuthor qrr,2021年12月2日,TbTjCrptCheckService
 * */
@Service
@Transactional(readOnly = true)
public class TbTjCrptCheckService extends AbstractTemplate{
	/**
 	 * <p>方法描述：获取异常信息</p>
 	 * @MethodAuthor qrr,2021年12月2日,findTdTjCrptWarnTip
	 * */
	public Map<String, List<String>> findTdTjCrptWarnTip(String rids) {
		Map<String, List<String>> map = new HashMap<>();
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT T1.MAIN_ID,T2.CODE_NAME");
		sql.append(" FROM TD_TJ_CRPT_WARN_TIP T1 ");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T2.RID = T1.TIP_ID ");
		sql.append(" WHERE T1.MAIN_ID IN (").append(rids).append(")");
		sql.append(" ORDER BY T2.NUM ");
		List<Object[]> list = findSqlResultList(sql.toString());
		if (!CollectionUtils.isEmpty(list)) {
			for (Object[] obj : list) {
				if (null==obj[0]||null==obj[1]) {
					continue;
				}
				if (null==map.get(obj[0].toString())) {
					List<String> names = new ArrayList<>();
					names.add(StringUtils.objectToString(obj[1]));
					map.put(obj[0].toString(), names);
				}else {
					List<String> names = map.get(obj[0].toString());
					names.add(StringUtils.objectToString(obj[1]));
				}
			}
		}
		return map;
	}
}
