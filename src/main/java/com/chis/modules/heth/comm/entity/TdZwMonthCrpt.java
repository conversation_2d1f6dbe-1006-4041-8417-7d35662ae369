package com.chis.modules.heth.comm.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2024-8-8
 */
@Entity
@Table(name = "TD_ZW_MONTH_CRPT")
@SequenceGenerator(name = "TdZwMonthCrpt", sequenceName = "TD_ZW_MONTH_CRPT_SEQ", allocationSize = 1)
public class TdZwMonthCrpt implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwMonthBhk fkByMainId;
    private TbTjCrpt fkByCrptId;
    private Integer state;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private List<TdZwMonthBadrsns> badrsnsList;

    public TdZwMonthCrpt() {
    }

    public TdZwMonthCrpt(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwMonthCrpt")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwMonthBhk getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwMonthBhk fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_ID")
    public TbTjCrpt getFkByCrptId() {
        return fkByCrptId;
    }

    public void setFkByCrptId(TbTjCrpt fkByCrptId) {
        this.fkByCrptId = fkByCrptId;
    }

    @Column(name = "STATE")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Transient
    public List<TdZwMonthBadrsns> getBadrsnsList() {
        return badrsnsList;
    }

    public void setBadrsnsList(List<TdZwMonthBadrsns> badrsnsList) {
        this.badrsnsList = badrsnsList;
    }
}