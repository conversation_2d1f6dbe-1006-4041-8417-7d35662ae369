package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.logic.ZwPsnInfoDTO;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckTechItem;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckTechMain;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckTechPsn;
import com.chis.modules.heth.zkcheck.service.TdZwCheckTechMainService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.NodeUnselectEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.TreeNode;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * 质量控制技能考核录入
 *
 * <AUTHOR>
 * @version 1.0
 */
@ManagedBean(name = "qcSkillCheckInputListBean")
@ViewScoped
public class QcSkillCheckInputListBean extends FacesEditBean {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final TdZwCheckTechMainService checkTechMainService = SpringContextHolder.getBean(TdZwCheckTechMainService.class);

    private Integer mainRid;

    /**
     * 查询条件：地区集合
     */
    private List<TsZone> searchZoneList = new ArrayList<>();
    /**
     * 查询条件：地区编码
     */
    private String searchZoneGb;
    /**
     * 查询条件：地区名称
     */
    private String searchZoneName;
    /**
     * 查询条件：机构名称
     */
    private String searchOrgName;
    /**
     * 查询条件：考核日期-开始日期
     */
    private Date searchCheckBeginDate;
    /**
     * 查询条件：考核日期-结束日期
     */
    private Date searchCheckEndDate;
    /**
     * 查询条件：考核结果
     */
    private List<Integer> searchCheckRstList;
    /**
     * 查询条件：被考核机构确认意见
     */
    private List<Integer> searchIfConfirmList;
    /**
     * 查询条件：状态
     */
    private List<Integer> searchStateList;

    /**编辑页接收对象*/
    private TdZwCheckTechMain checkTechMain;

    /**考核类型 码表*/
    private List<TsSimpleCode> checkTypeList;
    /**考核类型 Map */
    private Map<Integer,TsSimpleCode> checkTypeMap;
    /**被考核人员列表*/
    private List<TdZwCheckTechPsn> checkTechPsnList;
    /**被考核人员*/
    private TdZwCheckTechPsn techPsn;
    /**技能考核备案项目*/
    private List<TdZwCheckTechItem> techItemList;
    /**职称集合 -码表*/
    private List<TsSimpleCode> jobTitleList;
    private Map<Integer,TsSimpleCode> jobTitleMap;
    /**人员属性 -码表*/
    private List<TsSimpleCode> psnTypeList;
    private Map<Integer,TsSimpleCode> psnTypeMap;
    /**考核内容 -码表*/
    private List<TsSimpleCode> checkContentList;
    /**考核内容*/
    private TreeNode checkContentTree;
    /**选中的考核内容*/
    private List<String> selectCheckContent;
    /** 人员弹出框类型 1专家组组长，2专家组组员*/
    private String psnSelectType;
    /** 红星星标识 */
    private String redStar;

    public QcSkillCheckInputListBean() {
        redStar= "<font color='red'>*</font>";
        init();
        initTsSimp();
        this.ifSQL = true;
        this.searchAction();
    }

    private void init() {
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        //地区
        if (null == this.searchZoneList || this.searchZoneList.size() <= 0) {
            this.searchZoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "");
            this.searchZoneGb = this.searchZoneList.get(0).getZoneCode();
            this.searchZoneName = this.searchZoneList.get(0).getZoneName();
        }
        //考核日期
        this.searchCheckEndDate = new Date();
        this.searchCheckBeginDate = DateUtils.getYearFirstDay(this.searchCheckEndDate);
        //状态
        this.searchStateList = new ArrayList<>();
        this.searchStateList.add(0);

    }

    /**
     * <p>方法描述：初始化码表</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-20
     **/
    public void initTsSimp(){
        checkTypeList=new ArrayList<>();
        jobTitleList=new ArrayList<>();
        psnTypeList=new ArrayList<>();
        checkContentList=new ArrayList<>();
        checkTypeMap=new Hashtable<>();
        checkTypeList=commService.findLevelSimpleCodesByTypeId("5554");
        jobTitleList=commService.findLevelSimpleCodesByTypeId("2003");
        psnTypeList=commService.findLevelSimpleCodesByTypeId("5572");
        checkContentList=commService.findLevelSimpleCodesByTypeId("5573");
        if(!CollectionUtils.isEmpty(checkTypeList)){
            for(TsSimpleCode t:checkTypeList){
                checkTypeMap.put(t.getRid(),t);
            }
        }
        psnTypeMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(psnTypeList)){
            for (TsSimpleCode t:psnTypeList){
                psnTypeMap.put(t.getRid(),t);
            }
        }
        jobTitleMap=new HashMap<>();
        if(!CollectionUtils.isEmpty(jobTitleList)){
            for (TsSimpleCode tsSimpleCode : jobTitleList) {
                jobTitleMap.put(tsSimpleCode.getRid(),tsSimpleCode);
            }
        }

    }

    @Override
    public void searchAction() {
        super.searchAction();

        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update("tabView:mainForm:dataTable");
    }

    @Override
    public void addInit() {
        techItemList=new ArrayList<>();
        checkTechPsnList=new ArrayList<>();
        checkTechMain=new TdZwCheckTechMain();
        checkTechMain.setFkByCheckTypeId(new TsSimpleCode());
        checkTechMain.setFkByUnitId(new TsUnit());
        checkTechMain.setCheckRst(1);
        checkTechMain.setIfConfirm(1);
        checkTechMain.setState(0);
        checkTechMain.setDelMark(0);
        checkTechMain.setCheckDate(new Date());
        checkTechMain.setFkByCheckUnitId(Global.getUser().getTsUnit());
        //初始化考核内容
        checkContentTree=null;
        initCheckContents();
        //初始化值
        if(!CollectionUtils.isEmpty(checkContentList)){
            StringBuffer names=new StringBuffer();
            for(TsSimpleCode t: checkContentList){
                if(StringUtils.isNotBlank(t.getExtendS1())&&"1".equals(t.getExtendS1())){
                    names.append("；").append(t.getCodeName());
                }
            }
            if(names.length()>0){
                checkTechMain.setCheckContent(names.substring(1));
            }
        }
    }

    @Override
    public void viewInit() {
        if(mainRid==null){
            return;
        }
        checkTechMain=checkTechMainService.find(TdZwCheckTechMain.class,mainRid);
        checkTechPsnList=checkTechMainService.findTechPsnByMainId(checkTechMain.getRid());
        //初始化备案类别
        techItemList=new ArrayList<>();
        techItemList=checkTechMainService.findTechItemByMainId(checkTechMain.getRid());
        if(!CollectionUtils.isEmpty(techItemList)){
            StringBuffer name=new StringBuffer();
            for (TdZwCheckTechItem techItem : techItemList) {
                name.append("，").append(techItem.getFkByItemId().getCodeName());
            }
            if(name.length()>0){
                checkTechMain.setFiliTypeName(name.substring(1));
            }
        }
    }

    @Override
    public void modInit() {
        if(mainRid==null){
            return;
        }
        checkTechMain=checkTechMainService.find(TdZwCheckTechMain.class,mainRid);
        /**初始化考核内容*/
        checkContentTree=null;
        initCheckContents();
        //初始化被考核人员
        checkTechPsnList=new ArrayList<>();
        checkTechPsnList=checkTechMainService.findTechPsnByMainId(checkTechMain.getRid());
        if(!CollectionUtils.isEmpty(checkTechPsnList)){
            for (TdZwCheckTechPsn checkTechPsn : checkTechPsnList) {
                if(checkTechPsn.getFkByPsnTypeId()==null){
                    checkTechPsn.setFkByPsnTypeId(new TsSimpleCode());
                }else{
                    checkTechPsn.setPsnTypeId(checkTechPsn.getFkByPsnTypeId().getRid());
                }
                if(checkTechPsn.getFkByTitleId()==null){
                    checkTechPsn.setFkByTitleId(new TsSimpleCode());
                }else{
                    checkTechPsn.setTitleId(checkTechPsn.getFkByTitleId().getRid());
                }
            }
        }
        //初始化备案类别
        techItemList=new ArrayList<>();
        techItemList=checkTechMainService.findTechItemByMainId(checkTechMain.getRid());
        if(!CollectionUtils.isEmpty(techItemList)){
            StringBuffer name=new StringBuffer();
            for (TdZwCheckTechItem techItem : techItemList) {
                name.append("，").append(techItem.getFkByItemId().getCodeName());
            }
            if(name.length()>0){
                checkTechMain.setFiliTypeName(name.substring(1));
            }
        }
    }


    /**
     * <p>方法描述：初始化考核内容 下拉多选</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-20
     **/
    public void initCheckContents(){
        if (ObjectUtil.isEmpty(this.checkContentTree)) {
            this.checkContentTree = new CheckboxTreeNode("root", null);
            this.checkContentTree.setExpanded(true);
            for (TsSimpleCode simpleCode : this.checkContentList) {
                TreeNode node = new CheckboxTreeNode(simpleCode, this.checkContentTree);
                node.setExpanded(true);
            }
        }
    }

    /**
     * <p>方法描述：考核内容选中消失后事件</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-20
     **/
    public void hideCheckContentAction(){
        if(!CollectionUtils.isEmpty(selectCheckContent)){
            StringBuffer name=new StringBuffer();
            for (String nodeName : this.selectCheckContent) {
                name.append("；").append(nodeName);
            }
            String checkContent=checkTechMain.getCheckContent();
            if(name.length()>0&&StringUtils.isNotBlank(checkContent)){
                checkTechMain.setCheckContent(checkContent+"；"+name.substring(1));
            }
        }
    }

    /**
     * <p>方法描述：onCheckNodeSelect</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-22
     **/
    public void onCheckNodeSelect(NodeSelectEvent event){
        TreeNode selectNode = event.getTreeNode();
        if(null != selectNode) {
            TsSimpleCode t = (TsSimpleCode)selectNode.getData();
            this.selectCheckContent.add(t.getCodeName());
        }
    }


   /**
    * <p>方法描述：onCheckNodeNoSelect</p>
    * @MethodAuthor： yzz
    * @Date：2022-08-22
    **/
    public void onCheckNodeNoSelect(NodeUnselectEvent event) {
        TreeNode selectNode = event.getTreeNode();
        if(null != selectNode) {
            TsSimpleCode t = (TsSimpleCode)selectNode.getData();
            this.selectCheckContent.remove(t.getCodeName());
        }
    }

    /**
     * <p>方法描述：clickCheckContent</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-22
     **/
    public void clickCheckContent() {
        this.selectCheckContent = new ArrayList<>();
        RequestContext.getCurrentInstance().execute("PF('CheckContentOverlayPanel').show()");
    }

    @Override
    public void saveAction() {
        if(saveVerify()){
            return;
        }
        try{
            for (TdZwCheckTechPsn t:checkTechPsnList){
                Integer psnTypeId = t.getPsnTypeId();
                Integer titleId = t.getTitleId();
                if (null==psnTypeId){
                    t.setFkByPsnTypeId(null);
                }else{
                    if (null!=psnTypeMap.get(psnTypeId)){
                        t.setFkByPsnTypeId(psnTypeMap.get(psnTypeId));
                    }
                }
                if (null==titleId){
                    t.setFkByTitleId(null);
                }else{
                    if (null!=jobTitleMap.get(titleId)){
                        t.setFkByTitleId(jobTitleMap.get(titleId));
                    }
                }
            }
            this.mainRid=checkTechMainService.saveCheckTechMain(checkTechMain,checkTechPsnList,techItemList);
            this.modInit();
            JsfUtil.addSuccessMessage("保存成功！");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
     * <p>方法描述：保存校验</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-22
     **/
    public Boolean saveVerify(){
        boolean flag = false;
        if (null==this.checkTechMain.getFkByCheckTypeId()||checkTechMain.getFkByCheckTypeId().getRid()==null) {
            JsfUtil.addErrorMessage("考核类型不能为空！");
            flag = true;
        }
        if (null==this.checkTechMain.getFkByUnitId()||checkTechMain.getFkByUnitId().getRid()==null) {
            JsfUtil.addErrorMessage("机构名称不能为空！");
            flag = true;
        }
        if (null==this.checkTechMain.getCheckDate()) {
            JsfUtil.addErrorMessage("考核日期不能为空！");
            flag = true;
        }
        if(this.checkTechMain.getCheckContent().length()>1000){
            JsfUtil.addErrorMessage("考核内容长度不能超过1000！");
            flag = true;
        }
        if(this.checkTechMain.getCheckDesc().length()>1000){
            JsfUtil.addErrorMessage("考核过程及结果描述长度不能超过1000！");
            flag = true;
        }
        if(this.checkTechMain.getExpertAdv().length()>1000){
            JsfUtil.addErrorMessage("考核专家确认意见长度不能超过1000！");
            flag = true;
        }
        if(this.checkTechMain.getLeaderAdv().length()>1000){
            JsfUtil.addErrorMessage("考核组组长确认意见长度不能超过1000！");
            flag = true;
        }
        if(this.checkTechMain.getCheckExperts().length()>100){
            JsfUtil.addErrorMessage("考核专家长度不能超过100！");
            flag = true;
        }
        return flag;
    }


    /**
     * <p>方法描述：提交方法</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-20
     **/
    public void submitAction(){
        if(saveVerify()||submitVerify()){
            return;
        }
        try{
            checkTechMain.setState(1);
            for (TdZwCheckTechPsn t:checkTechPsnList){
                Integer psnTypeId = t.getPsnTypeId();
                Integer titleId = t.getTitleId();
                if (null==psnTypeId){
                    t.setFkByPsnTypeId(null);
                }else{
                    if (null!=psnTypeMap.get(psnTypeId)){
                        t.setFkByPsnTypeId(psnTypeMap.get(psnTypeId));
                    }
                }
                if (null==titleId){
                    t.setFkByTitleId(null);
                }else{
                    if (null!=jobTitleMap.get(titleId)){
                        t.setFkByTitleId(jobTitleMap.get(titleId));
                    }
                }
            }
            this.mainRid=checkTechMainService.saveCheckTechMain(checkTechMain,checkTechPsnList,techItemList);
            JsfUtil.addSuccessMessage("提交成功！");
            this.viewInitAction();
            this.searchAction();
            RequestContext context = RequestContext.getCurrentInstance();
            context.update("tabView");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
     * <p>方法描述：提交校验</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-22
     **/
    public Boolean submitVerify(){
        boolean flag = false;
        if(CollectionUtils.isEmpty(checkTechPsnList)){
            JsfUtil.addErrorMessage("被考核人员至少添加一条！");
            flag = true;
        }else{
            for (int i = 0; i < checkTechPsnList.size(); i++) {
                if(StringUtils.isBlank(checkTechPsnList.get(i).getPsnName())){
                    JsfUtil.addErrorMessage("被考核人员第"+(i+1)+"行姓名不能为空！");
                    flag = true;
                }
                if(checkTechPsnList.get(i).getPsnTypeId()==null){
                    JsfUtil.addErrorMessage("被考核人员第"+(i+1)+"行人员属性不能为空！");
                    flag = true;
                }
            }
        }
        if(StringUtils.isBlank(checkTechMain.getCheckAddr())){
            JsfUtil.addErrorMessage("考核地点不能为空！");
            flag = true;
        }
        if(StringUtils.isBlank(checkTechMain.getCheckContent())){
            JsfUtil.addErrorMessage("考核内容不能为空！");
            flag = true;
        }
        if(StringUtils.isBlank(checkTechMain.getCheckDesc())){
            JsfUtil.addErrorMessage("考核过程及结果描述不能为空！");
            flag = true;
        }
        if(checkTechMain.getCheckRst()==null){
            JsfUtil.addErrorMessage("考核结果不能为空！");
            flag = true;
        }
        if(StringUtils.isBlank(checkTechMain.getCheckExperts())){
            JsfUtil.addErrorMessage("考核专家不能为空！");
            flag = true;
        }
        if(checkTechMain.getIfConfirm()==null){
            JsfUtil.addErrorMessage("被考核机构确认意见不能为空！");
            flag = true;
        }
        return flag;
    }


    @Override
    public String[] buildHqls() {
        StringBuilder baseSql = new StringBuilder();
        baseSql.append("SELECT T.RID, CASE WHEN Z.ZONE_TYPE > 2 THEN SUBSTR(Z.FULL_NAME, INSTR(Z.FULL_NAME, '_') + 1) ELSE Z.FULL_NAME END ZONE_NAME, U.UNITNAME, SC.CODE_NAME, T.CHECK_RST, T.IF_CONFIRM, T.CHECK_DATE, T.STATE, Z.ZONE_GB ");
        baseSql.append("FROM TD_ZW_CHECK_TECH_MAIN T ");
        baseSql.append("LEFT JOIN TS_UNIT U ON T.UNIT_ID = U.RID ");
        baseSql.append("LEFT JOIN TS_ZONE Z ON U.ZONE_ID = Z.RID ");
        baseSql.append("LEFT JOIN TS_SIMPLE_CODE SC ON T.CHECK_TYPE_ID = SC.RID ");
        baseSql.append("WHERE NVL(T.DEL_MARK, 0) = 0 ");
        // 地区
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            baseSql.append(" AND Z.ZONE_GB LIKE :zoneCode escape '\\\' ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneGb)) + "%");
        }
        // 机构名称
        if (StringUtils.isNotBlank(this.searchOrgName)) {
            baseSql.append(" AND U.UNITNAME LIKE :searchOrgName escape '\\\' ");
            this.paramMap.put("searchOrgName", "%" + StringUtils.convertBFH(this.searchOrgName.trim()) + "%");
        }
        // 考核日期
        if (ObjectUtil.isNotEmpty(this.searchCheckBeginDate)) {
            baseSql.append(" AND T.CHECK_DATE >= TO_DATE(:searchCheckBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchCheckBeginDate", DateUtils.formatDate(this.searchCheckBeginDate) + " 00:00:00");
        }
        if (ObjectUtil.isNotEmpty(this.searchCheckEndDate)) {
            baseSql.append(" AND T.CHECK_DATE <= TO_DATE(:searchCheckEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchCheckEndDate", DateUtils.formatDate(this.searchCheckEndDate) + " 23:59:59");
        }
        // 考核结果
        if (ObjectUtil.isNotEmpty(this.searchCheckRstList)) {
            baseSql.append(" AND T.CHECK_RST IN (:searchCheckRstList)");
            this.paramMap.put("searchCheckRstList", this.searchCheckRstList);
        }
        // 被考核机构确认意见
        if (ObjectUtil.isNotEmpty(this.searchIfConfirmList)) {
            baseSql.append(" AND T.IF_CONFIRM IN (:searchIfConfirmList)");
            this.paramMap.put("searchIfConfirmList", this.searchIfConfirmList);
        }
        // 状态
        if (ObjectUtil.isNotEmpty(this.searchStateList)) {
            baseSql.append(" AND T.STATE IN (:searchStateList)");
            this.paramMap.put("searchStateList", this.searchStateList);
        }
        String sql1 = "SELECT * FROM (" + baseSql + ")AA ORDER BY AA.CHECK_DATE DESC, AA.ZONE_GB, AA.UNITNAME, AA.RID DESC";
        String sql2 = "SELECT COUNT(1) FROM (" + baseSql + ")";
        return new String[]{sql1, sql2};
    }

    public void deleteAction() {
        if (this.mainRid == null) {
            return;
        }
        try {
            this.checkTechMainService.deleteCheckTechMainByRid(this.mainRid);
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
        this.searchAction();
    }

    /**
     * <p>方法描述：添加被考核人员</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-20
     **/
    public void addCheckTechPsn(){
        TdZwCheckTechPsn checkTechPsn=new TdZwCheckTechPsn();
        checkTechPsn.setFkByMainId(checkTechMain);
        checkTechPsn.setFkByPsnTypeId(new TsSimpleCode());
        checkTechPsn.setFkByTitleId(new TsSimpleCode());
        checkTechPsn.setCreateDate(new Date());
        checkTechPsn.setCreateManid(Global.getUser().getRid());
        checkTechPsnList.add(checkTechPsn);
    }

    /**
     * <p>方法描述：选择被考核人员</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-20
     **/
    public void chooseCheckTechPsn(){
        if(checkTypeMap==null||checkTypeMap.get(this.checkTechMain.getFkByCheckTypeId().getRid())==null){
            return;
        }
        if(checkTechMain.getFkByUnitId()==null||checkTechMain.getFkByUnitId().getRid()==null){
            JsfUtil.addErrorMessage("请先选择机构！");
            return;
        }
        Map<String, Object> options = MapUtils.produceDialogMap(null,760,null,470);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> tmpList = new ArrayList<>();
        tmpList.add(checkTypeMap.get(this.checkTechMain.getFkByCheckTypeId().getRid()).getExtendS1());
        paramMap.put("checkType", tmpList);
        List<String> tmpList1 = new ArrayList<>();
        tmpList1.add(checkTechMain.getFkByUnitId().getRid().toString());
        paramMap.put("unitRid", tmpList1);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectCheckPsnList", options, paramMap);
    }

    /**
     * <p>方法描述：onTechPsnSelect</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-23
     **/
    public void onTechPsnSelect(SelectEvent event){
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
            List<ZwPsnInfoDTO> obj = (List<ZwPsnInfoDTO>) selectedMap.get("selected");
            if(!CollectionUtils.isEmpty(obj)){
                for (ZwPsnInfoDTO zwPsnInfoDTO : obj) {
                    TdZwCheckTechPsn checkTechPsn=new TdZwCheckTechPsn();
                    checkTechPsn.setPsnName(zwPsnInfoDTO.getEmpName());
                    checkTechPsn.setFkByMainId(checkTechMain);
                    checkTechPsn.setFkByPsnTypeId(new TsSimpleCode());
                    checkTechPsn.setFkByTitleId(new TsSimpleCode(zwPsnInfoDTO.getTitleId()));
                    checkTechPsn.setTitleId(zwPsnInfoDTO.getTitleId());
                    checkTechPsn.setCreateDate(new Date());
                    checkTechPsn.setCreateManid(Global.getUser().getRid());
                    checkTechPsnList.add(checkTechPsn);
                }
            }
        }
    }

    /**
     * <p>方法描述：删除被考核人员</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-20
     **/
    public void delTechPsnAction(){
        if(this.techPsn==null){
            return;
        }
        checkTechPsnList.remove(techPsn);
    }

    /**
     * <p>方法描述：上传</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-20
     **/
    public void uploadFile(){
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("PF('FileDialog').show();");
    }

    /**
     * <p>方法描述：附件上传</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-20
     **/
    public void fileUpload(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            try {
                String errorMsg = FileUtils.veryFile(file.getInputstream(), file.getContentType(), file.getFileName(), "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    RequestContext.getCurrentInstance().update("tabView:editForm:fileUpload");
                    return;
                }
                // 文件名称
                String fileName = file.getFileName();
                String uuid = java.util.UUID.randomUUID().toString()
                        .replaceAll("-", "");
                String path = JsfUtil.getAbsolutePath();
                String relativePath = new StringBuffer("heth/zworgannex/")
                        .append(uuid)
                        .append(fileName.substring(fileName
                                .lastIndexOf("."))).toString();
                // 文件路径
                String filePath = new StringBuilder(path).append(
                        relativePath).toString();
                checkTechMain.setFilePath(relativePath);
                FileUtils.copyFile(filePath, file.getInputstream());
                RequestContext.getCurrentInstance().execute(
                        "PF('FileDialog').hide()");
                RequestContext.getCurrentInstance().update(
                        "tabView:editForm:annexPanel");
                JsfUtil.addSuccessMessage("上传成功！");

            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("上传失败！");
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * <p>方法描述：查看附件</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-20
     **/
    public void toAnnexView() {
        if (StringUtils.isBlank(checkTechMain.getFilePath())) {
            JsfUtil.addErrorMessage("附件已经被删除，请重新上传！");
            return;
        }
        RequestContext.getCurrentInstance().execute(
                "window.open('" + FileUtils.previewFile(checkTechMain.getFilePath(),
                        "") + "')");
    }

    /**
     * <p>方法描述：删除附件</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-20
     **/
    public void delAnnex(){
        checkTechMain.setFilePath(null);
    }


    /**
     * <p>描述 用人单位选择</p>
     *
     * @param event
     * @MethodAuthor gongzhe,2022/7/13 11:03,onPsnSelect
     * @return void
     */
    public void onPsnSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
            List<ZwPsnInfoDTO> obj = (List<ZwPsnInfoDTO>) selectedMap.get("selected");
            StringBuilder psnName = new StringBuilder();
            for(ZwPsnInfoDTO psnInfo : obj){
                psnName.append("，").append(psnInfo.getEmpName());
            }
            String name = psnName.substring(1);
            String expertLeaders = this.checkTechMain.getCheckExperts();
            if(StringUtils.isNotBlank(expertLeaders) ){
                name=expertLeaders + "，" + name;
            }
            this.checkTechMain.setCheckExperts(name);
        }
    }
    /**
     * <p>描述 用人单位</p>
     *
     * @MethodAuthor gongzhe,2022/7/13 11:03,selectPsnList
     * @return void
     */
    public void selectPsnList() {
        Map<String, Object> options = MapUtils.produceDialogMap(null,760,null,470);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> tmpList1 = new ArrayList<>();
        tmpList1.add("选择考核专家");
        paramMap.put("title", tmpList1);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectCheckPsnList", options, paramMap);
    }

    /**
     * <p>描述 清空专家组长</p>
     *
     * @MethodAuthor gongzhe,2022/7/13 11:03,clearSelectLeaders
     * @return void
     */
    public void clearSelectLeaders(){
        this.checkTechMain.setCheckExperts(null);
    }

    /**
     * <p>方法描述：撤销功能</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-22
     **/
    public void cancelAction(){
        try{
            if(mainRid==null){
                return;
            }
            checkTechMainService.cancelStatue(mainRid);
            JsfUtil.addSuccessMessage("撤销成功！");
            this.modInitAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
    }

    /**
     * <p>方法描述：机构名称选择框</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-23
     **/
    public void selectUnitList(){
        if(checkTechMain.getFkByCheckTypeId()==null||checkTechMain.getFkByCheckTypeId().getRid()==null|| checkTypeMap==null||checkTypeMap.get(checkTechMain.getFkByCheckTypeId().getRid())==null){
            return;
        }
        Map<String, Object> options = MapUtils.produceDialogMap(null,760,null,370);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> tmpList = new ArrayList<>();
        tmpList.add(checkTypeMap.get(this.checkTechMain.getFkByCheckTypeId().getRid()).getExtendS1());
        paramMap.put("checkType", tmpList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectCheckOrgList", options, paramMap);
    }

    /**
     * <p>方法描述：选择机构名称</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-23
     **/
    public void onUnitSelect(SelectEvent event){
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
            Object[] obj = (Object[]) selectedMap.get("selected");
            TsUnit unit = new TsUnit();
            unit.setRid(new Integer(obj[0].toString()));
            unit.setUnitname(StringUtils.objectToString(obj[2]));
            this.checkTechMain.setFkByUnitId(unit);
            this.checkTechMain.setCheckAddr(StringUtils.objectToString(obj[3]));
            initFiliType(unit.getRid());
        }
    }

    /**
     * <p>方法描述：初始化备案类别</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-23
     **/
    public void initFiliType(Integer rid){
        String checkType= checkTypeMap.get(this.checkTechMain.getFkByCheckTypeId().getRid()).getExtendS1();
        if(StringUtils.isNotBlank(checkType)){
            List<Object[]> result= checkTechMainService.findCheckType(checkType,rid);
            if(!CollectionUtils.isEmpty(result)){
                techItemList=new ArrayList<>();
                StringBuffer name=new StringBuffer();
                for (Object[] objects : result) {
                    if(objects[1]==null||objects[2]==null){
                        continue;
                    }
                    TdZwCheckTechItem techItem=new TdZwCheckTechItem();
                    techItem.setFkByMainId(checkTechMain);
                    techItem.setFkByItemId(new TsSimpleCode(Integer.parseInt(objects[2].toString())));
                    techItem.setCreateDate(new Date());
                    techItem.setCreateManid(Global.getUser().getRid());
                    techItemList.add(techItem);
                    name.append("，").append(objects[1].toString());
                }
                if(name.length()>0){
                    checkTechMain.setFiliTypeName(name.substring(1));
                }
            }
        }
    }


    /**
     * <p>方法描述：changeCheckType</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-23
     **/
    public void changeCheckType(){
        //清空机构名称
        checkTechMain.setFkByUnitId(new TsUnit());
        checkTechMain.setCheckAddr(null);
        checkTechMain.setFiliTypeName(null);
        techItemList=new ArrayList<>();
    }



    public CommServiceImpl getCommService() {
        return commService;
    }

    public TdZwCheckTechMainService getCheckTechMainService() {
        return checkTechMainService;
    }

    public Integer getMainRid() {
        return mainRid;
    }

    public void setMainRid(Integer mainRid) {
        this.mainRid = mainRid;
    }

    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public String getSearchZoneGb() {
        return searchZoneGb;
    }

    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchOrgName() {
        return searchOrgName;
    }

    public void setSearchOrgName(String searchOrgName) {
        this.searchOrgName = searchOrgName;
    }

    public Date getSearchCheckBeginDate() {
        return searchCheckBeginDate;
    }

    public void setSearchCheckBeginDate(Date searchCheckBeginDate) {
        this.searchCheckBeginDate = searchCheckBeginDate;
    }

    public Date getSearchCheckEndDate() {
        return searchCheckEndDate;
    }

    public void setSearchCheckEndDate(Date searchCheckEndDate) {
        this.searchCheckEndDate = searchCheckEndDate;
    }

    public List<Integer> getSearchCheckRstList() {
        return searchCheckRstList;
    }

    public void setSearchCheckRstList(List<Integer> searchCheckRstList) {
        this.searchCheckRstList = searchCheckRstList;
    }

    public List<Integer> getSearchIfConfirmList() {
        return searchIfConfirmList;
    }

    public void setSearchIfConfirmList(List<Integer> searchIfConfirmList) {
        this.searchIfConfirmList = searchIfConfirmList;
    }

    public List<Integer> getSearchStateList() {
        return searchStateList;
    }

    public void setSearchStateList(List<Integer> searchStateList) {
        this.searchStateList = searchStateList;
    }

    public TdZwCheckTechMain getCheckTechMain() {
        return checkTechMain;
    }

    public void setCheckTechMain(TdZwCheckTechMain checkTechMain) {
        this.checkTechMain = checkTechMain;
    }

    public List<TsSimpleCode> getCheckTypeList() {
        return checkTypeList;
    }

    public void setCheckTypeList(List<TsSimpleCode> checkTypeList) {
        this.checkTypeList = checkTypeList;
    }

    public List<TdZwCheckTechPsn> getCheckTechPsnList() {
        return checkTechPsnList;
    }

    public void setCheckTechPsnList(List<TdZwCheckTechPsn> checkTechPsnList) {
        this.checkTechPsnList = checkTechPsnList;
    }

    public TdZwCheckTechPsn getTechPsn() {
        return techPsn;
    }

    public void setTechPsn(TdZwCheckTechPsn techPsn) {
        this.techPsn = techPsn;
    }

    public List<TsSimpleCode> getJobTitleList() {
        return jobTitleList;
    }

    public void setJobTitleList(List<TsSimpleCode> jobTitleList) {
        this.jobTitleList = jobTitleList;
    }

    public List<TsSimpleCode> getPsnTypeList() {
        return psnTypeList;
    }

    public void setPsnTypeList(List<TsSimpleCode> psnTypeList) {
        this.psnTypeList = psnTypeList;
    }

    public List<TsSimpleCode> getCheckContentList() {
        return checkContentList;
    }

    public void setCheckContentList(List<TsSimpleCode> checkContentList) {
        this.checkContentList = checkContentList;
    }

    public TreeNode getCheckContentTree() {
        return checkContentTree;
    }

    public void setCheckContentTree(TreeNode checkContentTree) {
        this.checkContentTree = checkContentTree;
    }

    public List<String> getSelectCheckContent() {
        return selectCheckContent;
    }

    public void setSelectCheckContent(List<String> selectCheckContent) {
        this.selectCheckContent = selectCheckContent;
    }

    public String getPsnSelectType() {
        return psnSelectType;
    }

    public void setPsnSelectType(String psnSelectType) {
        this.psnSelectType = psnSelectType;
    }

    public String getRedStar() {
        return redStar;
    }

    public void setRedStar(String redStar) {
        this.redStar = redStar;
    }

    public Map<Integer, TsSimpleCode> getCheckTypeMap() {
        return checkTypeMap;
    }

    public void setCheckTypeMap(Map<Integer, TsSimpleCode> checkTypeMap) {
        this.checkTypeMap = checkTypeMap;
    }

    public List<TdZwCheckTechItem> getTechItemList() {
        return techItemList;
    }

    public void setTechItemList(List<TdZwCheckTechItem> techItemList) {
        this.techItemList = techItemList;
    }
}
