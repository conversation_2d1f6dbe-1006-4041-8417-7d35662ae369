package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckClsItem;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckConclusion;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckItemRst;
import com.chis.modules.heth.zkcheck.entity.TdZwZkCheckMain;
import com.chis.modules.heth.zkcheck.service.TdZwCheckConclusionService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * <p>方法描述：</p>
 * @MethodAuthor： yzz
 * @Date：2022-08-23
 **/
@ManagedBean(name = "tdZwCheckConclusionBean")
@ViewScoped
public class TdZwCheckConclusionBean extends FacesEditBean {

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final TdZwCheckConclusionService conclusionService = SpringContextHolder.getBean(TdZwCheckConclusionService.class);

    /**
     * 查询条件：地区集合
     */
    private List<TsZone> searchZoneList = new ArrayList<>();
    /**
     * 查询条件：地区编码
     */
    private String searchZoneGb;
    /**
     * 查询条件：地区名称
     */
    private String searchZoneName;
    /**
     * 查询条件：机构名称
     */
    private String searchOrgName;
    /**
     * 查询条件：考核日期-开始日期
     */
    private Date searchCheckBeginDate;
    /**
     * 查询条件：考核日期-结束日期
     */
    private Date searchCheckEndDate;
    /**
     * 查询条件：考核结果
     */
    private List<Integer> searchCheckRstList;
    /**
     * 查询条件：被考核机构确认意见
     */
    private List<Integer> searchIfConfirmList;
    /**
     * 查询条件：状态
     */
    private List<Integer> searchStateList;

    /** 体检机构服务项目5018 */
    private List<TsSimpleCode> tjOrgServiceItemSimpleCodeList;
    /** 诊断机构服务项目5020 */
    private List<TsSimpleCode> diagOrgServiceItemSimpleCodeList;
    /** 质控考核指标5530 */
    private List<TsSimpleCode> zkCheckIndicatorSimpleCodeList;
    /** 评分项结果5534 */
    private List<TsSimpleCode> scoreRstSimpleCodeList;
    /** 考核类型（质量控制考核）5554 */
    private List<TsSimpleCode> checkTypeSimpleCodeList;
    /** 考核结论表的考核项目5574 */
    private List<TsSimpleCode> conclusionItemSimpleCodeList;
    /** 现场考核质量结论5575 */
    private List<TsSimpleCode> conclusionResultSimpleCodeList;
    /** 质量考核项目结果 */
    private List<TdZwCheckItemRst> checkItemRstList;
    /** 考核结论表备案项目 */
    private List<TdZwCheckClsItem> checkClsItemList;
    /** 考核类型Map（质量控制考核）5554 */
    private Map<Integer,TsSimpleCode> checkTypeSimpleCodeMap;
    /** 现场考核质量结论Map5575 */
    private Map<Integer,TsSimpleCode> conclusionResultSimpleCodeMap;
    /** 考核结论对象 */
    private TdZwCheckConclusion checkConclusion;
    /** 考核类型ID */
    private Integer checkTypeId;
    /** 备案类别 */
    private String recordCategory;
    /** 未通过考核的备案项目选择 */
    private List<TsSimpleCode> recordCategorySimpleCodeList;
    /** 选择的未通过考核的备案项目名称 */
    private String selectRecordCateGorys;
    /** 选择的未通过考核的备案项目Ids */
    private String selectRecordCateGoryIds;
    /** 选中的现场质量考核结论 */
    private Integer conclusionId;
    /** 是否关联了质控考核记录 1关联 其他不关联 */
    private Integer relCheckState;
    /** 待提交、已提交时修改，或者详情传递的质量控制考核结论Id 待录入时修改传递的健康检查质控考核主表Id */
    private Integer rid;
    /** 是否重置选择组件 给组件传递 */
    private boolean ifResetComp;
    /** 详情页未通过考核的备案项目 */
    private String unPassItem;
    /** 详情页考核结论表的考核项目5574 */
    private List<TsSimpleCode> conclusionItemSimpleCodeViewList;
    /** 详情页评分项结果5534 */
    private List<TsSimpleCode> scoreRstSimpleCodeViewList;


    public TdZwCheckConclusionBean(){
        this.ifSQL=true;
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        //地区
        if (null == this.searchZoneList || this.searchZoneList.size() <= 0) {
            this.searchZoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "");
            this.searchZoneGb = this.searchZoneList.get(0).getZoneCode();
            this.searchZoneName = this.searchZoneList.get(0).getZoneName();
        }
        //考核日期
        this.searchCheckEndDate = new Date();
        this.searchCheckBeginDate = DateUtils.getYearFirstDay(this.searchCheckEndDate);
        //状态
        this.searchStateList = new ArrayList<>();
        this.searchStateList.add(0);
        this.searchStateList.add(99);
        this.ifResetComp = true;
        initSimpleCode();
        this.checkItemRstList = new ArrayList<>();
        this.checkClsItemList = new ArrayList<>();
        this.searchAction();
    }

    @Override
    public void addInit() {
        this.relCheckState = 0;
        this.commAddInit();
        this.checkConclusion.setCheckDate(new Date());
    }

    /**
     * <p>方法描述：待录入状态时修改 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    public void specialAddAction(){
        TdZwZkCheckMain checkMain = null == this.rid ? null :
                this.conclusionService.find(TdZwZkCheckMain.class, this.rid);
        if(null == checkMain){
            JsfUtil.addErrorMessage("修改异常！");
            return;
        }
        this.relCheckState = 1;
        this.commAddInit();
        this.specialAddInit(checkMain);
        this.forwardEditPage();
    }

    @Override
    public void viewInit() {
        this.unPassItem = null;
        this.conclusionItemSimpleCodeViewList.clear();
        this.scoreRstSimpleCodeViewList.clear();
        this.recordCategory = null;
        this.checkItemRstList.clear();
        this.checkClsItemList.clear();
        this.checkConclusion = this.conclusionService.findTdZwCheckConclusionByRid(this.rid);
        if(null == this.checkConclusion){
            return;
        }
        if(!CollectionUtils.isEmpty(this.checkConclusion.getZwCheckItemRstList())){
            this.checkItemRstList.addAll(this.checkConclusion.getZwCheckItemRstList());
        }
        if(!CollectionUtils.isEmpty(this.checkConclusion.getZwCheckClsItemList())){
            this.checkClsItemList.addAll(this.checkConclusion.getZwCheckClsItemList());
        }
        if(!CollectionUtils.isEmpty(this.checkItemRstList)){
            Set<Integer> ridSet = new HashSet<>();
            for(TdZwCheckItemRst itemRst : this.checkItemRstList){
                TsSimpleCode simpleCode = itemRst.getFkByItemId();
                if(null != simpleCode && !ridSet.contains(simpleCode.getRid())){
                    this.conclusionItemSimpleCodeViewList.add(simpleCode);
                    ridSet.add(simpleCode.getRid());
                }
                simpleCode = itemRst.getFkByRstId();
                if(null != simpleCode && !ridSet.contains(simpleCode.getRid())){
                    this.scoreRstSimpleCodeViewList.add(simpleCode);
                    ridSet.add(simpleCode.getRid());
                }
            }
        }
        //排序
        if(!CollectionUtils.isEmpty(this.conclusionItemSimpleCodeViewList)){
            Collections.sort(this.conclusionItemSimpleCodeViewList, new Comparator<TsSimpleCode>() {
                @Override
                public int compare(TsSimpleCode o1, TsSimpleCode o2) {
                    Integer num1 = o1.getNum();
                    Integer num2 = o2.getNum();
                    return numberCompare(num1,num2);
                }
            });
        }
        if(!CollectionUtils.isEmpty(this.scoreRstSimpleCodeViewList)){
            Collections.sort(this.scoreRstSimpleCodeViewList, new Comparator<TsSimpleCode>() {
                @Override
                public int compare(TsSimpleCode o1, TsSimpleCode o2) {
                    Integer num1 = o1.getNum();
                    Integer num2 = o2.getNum();
                    return numberCompare(num1,num2);
                }
            });
        }
        if(!CollectionUtils.isEmpty(this.checkClsItemList)){
            Collections.sort(this.checkClsItemList, new Comparator<TdZwCheckClsItem>() {
                @Override
                public int compare(TdZwCheckClsItem o1, TdZwCheckClsItem o2) {
                    Integer num1 = null == o1.getFkByItemId() ? null : o1.getFkByItemId().getNum();
                    Integer num2 = null == o2.getFkByItemId() ? null : o2.getFkByItemId().getNum();
                    return numberCompare(num1,num2);
                }
            });
            StringBuffer recordBuffer = new StringBuffer();
            StringBuffer unPassItemBuffer = new StringBuffer();
            for(TdZwCheckClsItem clsItem : this.checkClsItemList){
                TsSimpleCode itemSimpleCode = clsItem.getFkByItemId();
                if(null == itemSimpleCode){
                    continue;
                }
                this.recordCategorySimpleCodeList.add(itemSimpleCode);
                String codeName = itemSimpleCode.getCodeName();
                recordBuffer.append("，").append(codeName);
                if(null != clsItem.getIfPass() && 0 == clsItem.getIfPass()){
                    unPassItemBuffer.append("，").append(codeName);
                }
            }
            this.recordCategory = recordBuffer.length() > 0 ? recordBuffer.substring(1) : null;
            this.unPassItem = unPassItemBuffer.length() > 0 ? unPassItemBuffer.substring(1) : null;
        }
    }

    /**
     * <p>方法描述：非待录入状态时修改 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    @Override
    public void modInit() {
        this.ifResetComp = true;
        this.checkConclusion = this.conclusionService.findTdZwCheckConclusionByRid(this.rid);
        if(null == this.checkConclusion){
            JsfUtil.addErrorMessage("修改失败！");
            return;
        }
        this.recordCategorySimpleCodeList.clear();
        this.checkItemRstList.clear();
        this.checkClsItemList.clear();
        this.relCheckState = null == this.checkConclusion.getFkByRelZkCheckId() ? 0 : 1;
        if(!CollectionUtils.isEmpty(this.checkConclusion.getZwCheckItemRstList())){
            this.checkItemRstList.addAll(this.checkConclusion.getZwCheckItemRstList());
        }
        if(!CollectionUtils.isEmpty(this.checkConclusion.getZwCheckClsItemList())){
            this.checkClsItemList.addAll(this.checkConclusion.getZwCheckClsItemList());
        }
        this.checkTypeId = null == this.checkConclusion.getFkByCheckTypeId() ? null :
                this.checkConclusion.getFkByCheckTypeId().getRid();
        this.conclusionId = null == this.checkConclusion.getFkByConclusionId() ? null :
                this.checkConclusion.getFkByConclusionId().getRid();
        this.recordCategory = null;
        this.selectRecordCateGorys = null;
        this.selectRecordCateGoryIds = null;
        if(!CollectionUtils.isEmpty(this.checkClsItemList)){
            Collections.sort(this.checkClsItemList, new Comparator<TdZwCheckClsItem>() {
                @Override
                public int compare(TdZwCheckClsItem o1, TdZwCheckClsItem o2) {
                    Integer num1 = null == o1.getFkByItemId() ? null : o1.getFkByItemId().getNum();
                    Integer num2 = null == o2.getFkByItemId() ? null : o2.getFkByItemId().getNum();
                    return numberCompare(num1,num2);
                }
            });
            StringBuffer recordBuffer = new StringBuffer();
            StringBuffer selectRecordBuffer = new StringBuffer();
            StringBuffer selectRecordIdBuffer = new StringBuffer();
            for(TdZwCheckClsItem clsItem : this.checkClsItemList){
                TsSimpleCode itemSimpleCode = clsItem.getFkByItemId();
                if(null == itemSimpleCode){
                    continue;
                }
                this.recordCategorySimpleCodeList.add(itemSimpleCode);
                String codeName = itemSimpleCode.getCodeName();
                recordBuffer.append("，").append(codeName);
                if(null != clsItem.getIfPass() && 0 == clsItem.getIfPass()){
                    selectRecordBuffer.append("，").append(codeName);
                    selectRecordIdBuffer.append(",").append(itemSimpleCode.getRid());
                }
            }
            this.recordCategory = recordBuffer.length() > 0 ? recordBuffer.substring(1) : null;
            this.selectRecordCateGorys = selectRecordBuffer.length() > 0 ? selectRecordBuffer.substring(1) : null;
            this.selectRecordCateGoryIds = selectRecordIdBuffer.length() > 0 ? selectRecordIdBuffer.substring(1) : null;
        }
    }

    @Override
    public void saveAction() {
        if(!this.saveValidate()){
            return;
        }
        try{
            this.beforeSaveFillData();
            this.checkConclusion.setState(0);
            this.conclusionService.saveOrUpdateTdZwCheckConclusion(this.checkConclusion);
            this.rid = this.checkConclusion.getRid();
            this.searchAction();
            JsfUtil.addSuccessMessage("暂存成功！");
        }catch(Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("暂存失败！");
        }
    }

    /**
     * <p>方法描述：提交 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    public void submitAction(){
        if(!this.submitValidate()){
            return;
        }
        try{
            this.beforeSaveFillData();
            this.checkConclusion.setState(1);
            this.conclusionService.saveOrUpdateTdZwCheckConclusion(this.checkConclusion);
            this.rid = this.checkConclusion.getRid();
            JsfUtil.addSuccessMessage("提交成功！");
            this.searchAction();
            this.viewInitAction();
        }catch(Exception e){
            e.printStackTrace();
            this.checkConclusion.setState(0);
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
     * <p>方法描述：撤销 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    public void cancelAction(){
        try{
            this.conclusionService.cancelCheckConclusion(this.rid);
            JsfUtil.addSuccessMessage("撤销成功！");
            this.searchAction();
            //回到编辑页
            this.modInitAction();
        }catch(Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
    }

    @Override
    public String[] buildHqls() {
        StringBuilder baseSql = new StringBuilder();
        baseSql.append(" select * from ( ");
        baseSql.append("  select T.rid,CASE WHEN T2.ZONE_TYPE > 2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME, '_') + 1) ELSE T2.FULL_NAME END ZONE_NAME, ");
        baseSql.append("  T1.UNITNAME,T3.CODE_NAME as CHECK_TYPE,to_number(null) as  CONCLUSION, to_number(null) as LEADER_ADV,T.CHECK_DATE  as  CHECK_DATE, ");
        baseSql.append("  99 as STATE, T.ORG_ID as unitRid,T2.ZONE_GB, to_number(null) as CHECK_RST,to_number(null) as  IF_CONFIRM ");
        baseSql.append("  from TD_ZW_ZK_CHECK_MAIN T ");
        baseSql.append("  LEFT JOIN TS_UNIT T1 ON T.ORG_ID = T1.RID ");
        baseSql.append("  LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID ");
        baseSql.append("  LEFT JOIN TS_SIMPLE_CODE T3 ON T3.RID = T.CHECK_TYPE_ID ");
        baseSql.append("  where T.STATE_MARK = 1 and T.DEL_MARK = 0 ");
        baseSql.append("    and not exists(select 1 from TD_ZW_CHECK_CONCLUSION TT where TT.REL_ZK_CHECK_ID = T.RID and TT.DEL_MARK=0 ) ");
        baseSql.append("  union all ");
        baseSql.append("  select T.rid, CASE WHEN T2.ZONE_TYPE > 2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME, '_') + 1) ELSE T2.FULL_NAME END ZONE_NAME, ");
        baseSql.append("  T1.UNITNAME, T3.CODE_NAME as CHECK_TYPE, T.CONCLUSION_ID as CONCLUSION,T.IF_CONFIRM as LEADER_ADV, T.CHECK_DATE, T.STATE, ");
        baseSql.append("  T.UNIT_ID as unitRid, T2.ZONE_GB, T.CHECK_RST, T.IF_CONFIRM ");
        baseSql.append("  from TD_ZW_CHECK_CONCLUSION T ");
        baseSql.append("  LEFT JOIN TS_UNIT T1 ON T.UNIT_ID = T1.RID ");
        baseSql.append("  LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID ");
        baseSql.append("  LEFT JOIN TS_SIMPLE_CODE T3 ON T3.RID = T.CHECK_TYPE_ID ");
        baseSql.append("  where T.DEL_MARK = 0 and T.STATE is not null ");
        baseSql.append(" ) TT1 where 1=1 ");

        // 地区
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            baseSql.append(" AND TT1.ZONE_GB LIKE :zoneCode escape '\\\' ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneGb)) + "%");
        }
        // 机构名称
        if (StringUtils.isNotBlank(this.searchOrgName)) {
            baseSql.append(" AND TT1.UNITNAME LIKE :searchOrgName escape '\\\' ");
            this.paramMap.put("searchOrgName", "%" + StringUtils.convertBFH(this.searchOrgName.trim()) + "%");
        }
        // 考核日期
        if (ObjectUtil.isNotEmpty(this.searchCheckBeginDate)) {
            baseSql.append(" AND TT1.CHECK_DATE >= TO_DATE(:searchCheckBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchCheckBeginDate", DateUtils.formatDate(this.searchCheckBeginDate) + " 00:00:00");
        }
        if (ObjectUtil.isNotEmpty(this.searchCheckEndDate)) {
            baseSql.append(" AND TT1.CHECK_DATE <= TO_DATE(:searchCheckEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchCheckEndDate", DateUtils.formatDate(this.searchCheckEndDate) + " 23:59:59");
        }
        // 考核结果
        if (ObjectUtil.isNotEmpty(this.searchCheckRstList)) {
            baseSql.append(" AND TT1.CHECK_RST IN (:searchCheckRstList)");
            this.paramMap.put("searchCheckRstList", this.searchCheckRstList);
        }
        // 被考核机构确认意见
        if (ObjectUtil.isNotEmpty(this.searchIfConfirmList)) {
            baseSql.append(" AND TT1.IF_CONFIRM IN (:searchIfConfirmList)");
            this.paramMap.put("searchIfConfirmList", this.searchIfConfirmList);
        }
        // 状态
        if (ObjectUtil.isNotEmpty(this.searchStateList)) {
            baseSql.append(" AND TT1.STATE IN (:searchStateList)");
            this.paramMap.put("searchStateList", this.searchStateList);
        }
        String sql1 = "SELECT * FROM (" + baseSql + ") AA order by AA.CHECK_DATE desc, AA.ZONE_GB asc, AA.UNITNAME asc";
        String sql2 = "SELECT COUNT(1) FROM (" + baseSql + ")";
        return new String[]{sql1, sql2};
    }

    /**
     * <p>方法描述：选择机构弹框 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    public void selectOrgList(){
        TsSimpleCode simpleCode = null == this.checkTypeId ? null : this.checkTypeSimpleCodeMap.get(this.checkTypeId);
        String extends1 = null == simpleCode ? null : simpleCode.getExtendS1();
        if(StringUtils.isBlank(extends1)){
            JsfUtil.addErrorMessage("请选择考核类型！");
            return;
        }
        Map<String, Object> options = MapUtils.produceDialogMap(null,760,null,370);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> tmpList = new ArrayList<>();
        tmpList.add(extends1);
        paramMap.put("checkType", tmpList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectCheckOrgList", options, paramMap);
    }

    /**
     * <p>方法描述：选择机构返回 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    public void onOrgSelect(SelectEvent event){
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
            Object[] obj = (Object[]) selectedMap.get("selected");
            this.checkConclusion.setFkByUnitId(new TsUnit());
            this.checkConclusion.getFkByUnitId().setRid(new Integer(obj[0].toString()));
            this.checkConclusion.getFkByUnitId().setUnitname(StringUtils.objectToString(obj[2]));
            this.initEditRecordCateGorySimpleCode(this.checkConclusion.getFkByUnitId().getRid());
        }
    }

    /**
     * <p>方法描述：切换考核类型 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    public void changeCheckType(){
        this.checkConclusion.setFkByUnitId(null);
        this.recordCategory = null;
        this.selectRecordCateGorys = null;
        this.selectRecordCateGoryIds = null;
        this.ifResetComp = true;
        this.checkClsItemList.clear();
        this.recordCategorySimpleCodeList.clear();
    }

    /**
     * <p>方法描述：切换现场质量考核结论 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    public void changeCheckResult(){
        TsSimpleCode simpleCode = this.conclusionResultSimpleCodeMap.get(this.conclusionId);
        this.checkConclusion.setFkByConclusionId(simpleCode);
    }

    /**
     * <p>方法描述：删除附件 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    public void delConclusFilePath(){
        this.checkConclusion.setFilePath(null);
    }

    /**
     * <p>方法描述：上传附件 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    public void executeUploadFile(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            try {
                String errorMsg = FileUtils.veryFile(file.getInputstream(), file.getContentType(), file.getFileName(), "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    RequestContext.getCurrentInstance().update("tabView:editForm:fileUpload");
                    return;
                }
                String fileName = file.getFileName();// 文件名称
                String uuid = java.util.UUID.randomUUID().toString()
                        .replaceAll("-", "");
                String path = JsfUtil.getAbsolutePath();
                String relativePath = new StringBuffer("heth/zworgannex/")
                        .append(uuid)
                        .append(fileName.substring(fileName
                                .lastIndexOf("."))).toString();
                // 文件路径
                String filePath = new StringBuilder(path).append(
                        relativePath).toString();
                this.checkConclusion.setFilePath(relativePath);
                FileUtils.copyFile(filePath, file.getInputstream());
                RequestContext.getCurrentInstance().execute(
                        "PF('FileDialog').hide()");
                RequestContext.getCurrentInstance().update(
                        "tabView:editForm");
                JsfUtil.addSuccessMessage("上传成功！");
            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("上传失败！");
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * <p>方法描述：Integer 比较 </p>
     * @MethodAuthor： pw 2022/8/24
     **/
    private int numberCompare(Integer num1, Integer num2){
        int result = 0;
        if(null != num1 && null != num2){
            result = num1.compareTo(num2);
        }else if(null == num1 && null != num2){
            result = -1;
        }else if(null == num2 && null != num1){
            result = 1;
        }
        return result;
    }

    /**
     * <p>方法描述：待录入状态时修改初始化 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    private void specialAddInit(TdZwZkCheckMain checkMain){
        //赋值考核类型、机构名称、备案类别、考核日期以及可选择的未通过考核的备案项目
        this.checkConclusion.setFkByCheckTypeId(checkMain.getFkByCheckTypeId());
        this.checkConclusion.setFkByUnitId(checkMain.getFkByOrgId());
        this.checkConclusion.setCheckDate(checkMain.getCheckDate());
        this.checkConclusion.setFkByRelZkCheckId(checkMain);
        this.checkTypeId = null == checkMain.getFkByCheckTypeId() ? null : checkMain.getFkByCheckTypeId().getRid();
        this.initEditRecordCateGorySimpleCode(null == checkMain.getFkByOrgId() ? null :
                checkMain.getFkByOrgId().getRid());
        //初始化质量考核结果
        this.initSpecialItemRst(checkMain.getRid());
    }

    /**
     * <p>方法描述：初始化质量考核结果 </p>
     * @MethodAuthor： pw 2022/8/24
     **/
    private void initSpecialItemRst(Integer checkMainId){
        if(CollectionUtils.isEmpty(this.checkItemRstList) || null == checkMainId){
           return;
        }
        //key 码表5530CODE_LEVEL_NO截取的大类编码 value Map key 码表5534CODE_NO value数量
        Map<String,Map<String,Integer>> normMap = this.conclusionService.findResultByCheckMainId(checkMainId);
        if(CollectionUtils.isEmpty(normMap)){
            return;
        }
        for(TdZwCheckItemRst itemRst : this.checkItemRstList){
            TsSimpleCode scoreSimple = itemRst.getFkByRstId();
            String scoreCodeNo = null == scoreSimple ? null : scoreSimple.getCodeNo();
            TsSimpleCode itemSimple = itemRst.getFkByItemId();
            int count = 0;
            itemRst.setNums(count);
            if(StringUtils.isBlank(scoreCodeNo) || null == itemSimple || StringUtils.isBlank(itemSimple.getExtendS3())){
                continue;
            }
            String[] codeNoArr = itemSimple.getExtendS3().split(",");
            for(String itemCodeNo : codeNoArr){
                if(StringUtils.isBlank(itemCodeNo)){
                    continue;
                }
                Map<String,Integer> scoreMap = normMap.get(itemCodeNo);
                if(null == scoreMap){
                    continue;
                }
                Integer tmpCount = scoreMap.get(scoreCodeNo);
                if(null != tmpCount){
                    count += tmpCount;
                }
            }
            itemRst.setNums(count);
        }
    }

    /**
     * <p>方法描述：添加数据初始化 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    private void commAddInit(){
        this.ifResetComp = true;
        this.checkConclusion = new TdZwCheckConclusion();
        this.checkConclusion.setCheckRst(1);
        this.checkConclusion.setIfConfirm(1);
        this.checkConclusion.setDelMark(0);
        this.checkConclusion.setFkByCheckUnitId(Global.getUser().getTsUnit());
        this.editInitReset();
        for(TsSimpleCode itemSimple : this.conclusionItemSimpleCodeList){
            for(TsSimpleCode scoreSimple : this.scoreRstSimpleCodeList){
                TdZwCheckItemRst checkItemRst = new TdZwCheckItemRst();
                checkItemRst.setFkByMainId(this.checkConclusion);
                checkItemRst.setFkByItemId(itemSimple);
                checkItemRst.setFkByRstId(scoreSimple);
                this.checkItemRstList.add(checkItemRst);
            }
        }
    }

    /**
     * <p>方法描述：初始化考核的备案项目 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    private void initEditRecordCateGorySimpleCode(Integer unitId){
        this.ifResetComp = true;
        //清空未通过考核的备案项目以及备案类别
        this.selectRecordCateGorys = null;
        this.selectRecordCateGoryIds = null;
        this.recordCategory = null;

        this.recordCategorySimpleCodeList.clear();
        this.checkClsItemList.clear();
        if(null == unitId){
            return;
        }
        TsSimpleCode simpleCode = null == this.checkTypeId ? null : this.checkTypeSimpleCodeMap.get(this.checkTypeId);
        String extends1 = null == simpleCode ? null : simpleCode.getExtendS1();
        if(StringUtils.isBlank(extends1)){
            return;
        }
        //查询职业健康检查机构或者职业病诊断机构对应的服务项目
        List<TsSimpleCode> cateGorySimpleCodeList = this.filterRecordCateGorySimpleCode(unitId, extends1);
        //赋值备案类别 清空未通过考核的备案项目
        if(!CollectionUtils.isEmpty(cateGorySimpleCodeList)){
            this.recordCategorySimpleCodeList.addAll(cateGorySimpleCodeList);
            StringBuffer buffer = new StringBuffer();
            for(TsSimpleCode serItem : cateGorySimpleCodeList){
                TdZwCheckClsItem clsItem = new TdZwCheckClsItem();
                clsItem.setFkByMainId(this.checkConclusion);
                clsItem.setFkByItemId(serItem);
                //默认通过
                clsItem.setIfPass(1);
                this.checkClsItemList.add(clsItem);
                buffer.append("，").append(serItem.getCodeName());
            }
            //备案类别赋值
            this.recordCategory = buffer.substring(1);
        }
    }

    /**
     * <p>方法描述：过滤出对应的服务项目 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    private List<TsSimpleCode> filterRecordCateGorySimpleCode(Integer unitId, String extends1){
        List<String> codeNoList = this.conclusionService.findOrgServiceItemsByOrgUnitId(unitId, extends1);
        List<TsSimpleCode> resultList = new ArrayList<>();
        if(CollectionUtils.isEmpty(codeNoList)){
            return resultList;
        }
        List<TsSimpleCode> tmpList = new ArrayList<>();
        if("1".equals(extends1)){
            tmpList.addAll(this.tjOrgServiceItemSimpleCodeList);
        }else if("2".equals(extends1)){
            tmpList.addAll(this.diagOrgServiceItemSimpleCodeList);
        }
        if(CollectionUtils.isEmpty(tmpList)){
            return resultList;
        }
        for(TsSimpleCode simpleCode : tmpList){
            String codeNo = simpleCode.getCodeNo();
            if(codeNoList.contains(codeNo)){
                resultList.add(simpleCode);
            }
        }
        return resultList;
    }

    /**
     * <p>方法描述：暂存验证 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    private boolean saveValidate(){
        boolean flag = true;
        List<String> errMsgList = new ArrayList<>();
        //不关联现场考核记录ID时验证
        if(null != this.relCheckState && this.relCheckState == 0){
            if(null == this.checkTypeId){
                errMsgList.add("请选择考核类型！");
            }
            if(null == this.checkConclusion.getFkByUnitId() || null == this.checkConclusion.getFkByUnitId().getRid()){
                errMsgList.add("请选择机构！");
            }
            if(null == this.checkConclusion.getCheckDate()){
                errMsgList.add("考核日期不允许为空！");
            }
        }
        int length = 1000;
        String str = this.checkConclusion.getOthers();
        if(!this.strLengthValidate(str, length)){
            errMsgList.add("其他内容过长，不能超过"+length+"个字！");
        }
        str = this.checkConclusion.getSummary();
        if(!this.strLengthValidate(str, length)){
            errMsgList.add("总结内容过长，不能超过"+length+"个字！");
        }
        String concluExtends1 = null == this.checkConclusion.getFkByConclusionId() ? null :
                this.checkConclusion.getFkByConclusionId().getExtendS1();
        if("1".equals(concluExtends1) && StringUtils.isBlank(this.selectRecordCateGoryIds)){
            errMsgList.add("请选择未通过考核的备案项目！");
        }
        str = this.checkConclusion.getExpertAdv();
        if(!this.strLengthValidate(str, length)){
            errMsgList.add("考核专家确认意见内容过长，不能超过"+length+"个字！");
        }
        str = this.checkConclusion.getLeaderAdv();
        if(!this.strLengthValidate(str, length)){
            errMsgList.add("考核组组长确认意见内容过长，不能超过"+length+"个字！");
        }
        if(CollectionUtils.isEmpty(errMsgList)){
            return flag;
        }
        for(String message : errMsgList){
            JsfUtil.addErrorMessage(message);
        }
        return false;
    }

    /**
     * <p>方法描述：字符串长度验证 </p>
     * @MethodAuthor： pw 2022/8/24
     **/
    private boolean strLengthValidate(String str, int length){
        if(StringUtils.isBlank(str)){
            return true;
        }
        return str.length() <= length;
    }

    /**
     * <p>方法描述：提交验证 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    private boolean submitValidate(){
        boolean flag = this.saveValidate();
        List<String> errMsgList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(this.checkItemRstList)){
            for(TdZwCheckItemRst itemRst : this.checkItemRstList){
                if(null == itemRst.getNums()){
                    String message = itemRst.getFkByItemId().getCodeName()+itemRst.getFkByRstId().getCodeName()+"不允许为空！";
                    errMsgList.add(message);
                }
            }
        }
        if(null == this.checkConclusion.getCheckRst()){
            errMsgList.add("请选择结论！");
        }
        if(null == this.checkConclusion.getFkByConclusionId() ||
                null == this.checkConclusion.getFkByConclusionId().getRid()){
            errMsgList.add("请选择现场质量考核结论！");
        }
        if(null == this.checkConclusion.getIfConfirm()){
            errMsgList.add("请选择被考核机构确认意见！");
        }
        if(CollectionUtils.isEmpty(errMsgList)){
            return flag;
        }
        for(String message : errMsgList){
            JsfUtil.addErrorMessage(message);
        }
        return false;
    }

    /**
     * <p>方法描述：存储前数据填充 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    private void beforeSaveFillData(){
        String extends1 = null == this.checkConclusion.getFkByConclusionId() ? null :
                this.checkConclusion.getFkByConclusionId().getExtendS1();
        if(!"1".equals(extends1)){
            //重置未通过考核的备案项目选择组件 否则会出现缓存默认被选中的情况
            this.ifResetComp = true;
            this.selectRecordCateGoryIds = null;
            this.selectRecordCateGorys = null;
        }
        //通过与不通过赋值
        String tmpStr = ","+this.selectRecordCateGoryIds+",";
        for(TdZwCheckClsItem clsItem : this.checkClsItemList){
            Integer rid = null == clsItem.getFkByItemId() ? null : clsItem.getFkByItemId().getRid();
            if(null != rid && tmpStr.contains(","+rid+",")){
                clsItem.setIfPass(0);
            }else{
                clsItem.setIfPass(1);
            }
        }
        this.checkConclusion.setZwCheckClsItemList(this.checkClsItemList);
        this.checkConclusion.setZwCheckItemRstList(this.checkItemRstList);
        //未关联质控考核的 需要赋值
        if(0 == this.relCheckState){
            this.checkConclusion.setFkByCheckTypeId(new TsSimpleCode(this.checkTypeId));
        }
    }

    /**
     * <p>方法描述：编辑初始化重置 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    private void editInitReset(){
        this.checkItemRstList.clear();
        this.checkClsItemList.clear();
        this.checkTypeId = null;
        this.recordCategory = null;
        this.recordCategorySimpleCodeList.clear();
        this.selectRecordCateGorys = null;
        this.selectRecordCateGoryIds = null;
        this.conclusionId = null;
    }
    /**
     * <p>方法描述：初始化码表 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    private void initSimpleCode(){
        this.tjOrgServiceItemSimpleCodeList = this.commService.findLevelSimpleCodesByTypeId("5018");
        this.diagOrgServiceItemSimpleCodeList = this.commService.findLevelSimpleCodesByTypeId("5020");
        this.zkCheckIndicatorSimpleCodeList = this.commService.findLevelSimpleCodesByTypeId("5030");
        this.scoreRstSimpleCodeList = this.commService.findLevelSimpleCodesByTypeId("5534");
        this.checkTypeSimpleCodeList = this.commService.findLevelSimpleCodesByTypeId("5554");
        this.conclusionItemSimpleCodeList = this.commService.findLevelSimpleCodesByTypeId("5574");
        this.conclusionResultSimpleCodeList = this.commService.findLevelSimpleCodesByTypeId("5575");
        this.recordCategorySimpleCodeList = new ArrayList<>();
        this.checkTypeSimpleCodeMap = new HashMap<>();
        this.conclusionResultSimpleCodeMap = new HashMap<>();
        this.fillSimpleCodeMap(this.checkTypeSimpleCodeList, this.checkTypeSimpleCodeMap);
        this.fillSimpleCodeMap(this.conclusionResultSimpleCodeList, this.conclusionResultSimpleCodeMap);
        this.conclusionItemSimpleCodeViewList = new ArrayList<>();
        this.scoreRstSimpleCodeViewList = new ArrayList<>();
    }

    /**
     * <p>方法描述：码表给Map赋值 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    private void fillSimpleCodeMap(List<TsSimpleCode> simpleCodeList , Map<Integer,TsSimpleCode> map){
        if(!CollectionUtils.isEmpty(simpleCodeList)){
            for(TsSimpleCode simpleCode : simpleCodeList){
                map.put(simpleCode.getRid(),simpleCode);
            }
        }
    }


    /**
     * <p>方法描述：deleteAction</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-24
     **/
    public void deleteAction(){
        if(null == rid){
            return;
        }
        try{
            conclusionService.deleteConclusion(rid);
            JsfUtil.addSuccessMessage("删除成功！");
            this.searchAction();
        }catch(Exception e){
            e.printStackTrace();
            JsfUtil.addSuccessMessage("删除失败！");
        }
    }

    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public String getSearchZoneGb() {
        return searchZoneGb;
    }

    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchOrgName() {
        return searchOrgName;
    }

    public void setSearchOrgName(String searchOrgName) {
        this.searchOrgName = searchOrgName;
    }

    public Date getSearchCheckBeginDate() {
        return searchCheckBeginDate;
    }

    public void setSearchCheckBeginDate(Date searchCheckBeginDate) {
        this.searchCheckBeginDate = searchCheckBeginDate;
    }

    public Date getSearchCheckEndDate() {
        return searchCheckEndDate;
    }

    public void setSearchCheckEndDate(Date searchCheckEndDate) {
        this.searchCheckEndDate = searchCheckEndDate;
    }

    public List<Integer> getSearchCheckRstList() {
        return searchCheckRstList;
    }

    public void setSearchCheckRstList(List<Integer> searchCheckRstList) {
        this.searchCheckRstList = searchCheckRstList;
    }

    public List<Integer> getSearchIfConfirmList() {
        return searchIfConfirmList;
    }

    public void setSearchIfConfirmList(List<Integer> searchIfConfirmList) {
        this.searchIfConfirmList = searchIfConfirmList;
    }

    public List<Integer> getSearchStateList() {
        return searchStateList;
    }

    public void setSearchStateList(List<Integer> searchStateList) {
        this.searchStateList = searchStateList;
    }

    public List<TsSimpleCode> getTjOrgServiceItemSimpleCodeList() {
        return tjOrgServiceItemSimpleCodeList;
    }

    public void setTjOrgServiceItemSimpleCodeList(List<TsSimpleCode> tjOrgServiceItemSimpleCodeList) {
        this.tjOrgServiceItemSimpleCodeList = tjOrgServiceItemSimpleCodeList;
    }

    public List<TsSimpleCode> getDiagOrgServiceItemSimpleCodeList() {
        return diagOrgServiceItemSimpleCodeList;
    }

    public void setDiagOrgServiceItemSimpleCodeList(List<TsSimpleCode> diagOrgServiceItemSimpleCodeList) {
        this.diagOrgServiceItemSimpleCodeList = diagOrgServiceItemSimpleCodeList;
    }

    public List<TsSimpleCode> getZkCheckIndicatorSimpleCodeList() {
        return zkCheckIndicatorSimpleCodeList;
    }

    public void setZkCheckIndicatorSimpleCodeList(List<TsSimpleCode> zkCheckIndicatorSimpleCodeList) {
        this.zkCheckIndicatorSimpleCodeList = zkCheckIndicatorSimpleCodeList;
    }

    public List<TsSimpleCode> getScoreRstSimpleCodeList() {
        return scoreRstSimpleCodeList;
    }

    public void setScoreRstSimpleCodeList(List<TsSimpleCode> scoreRstSimpleCodeList) {
        this.scoreRstSimpleCodeList = scoreRstSimpleCodeList;
    }

    public List<TsSimpleCode> getCheckTypeSimpleCodeList() {
        return checkTypeSimpleCodeList;
    }

    public void setCheckTypeSimpleCodeList(List<TsSimpleCode> checkTypeSimpleCodeList) {
        this.checkTypeSimpleCodeList = checkTypeSimpleCodeList;
    }

    public List<TsSimpleCode> getConclusionItemSimpleCodeList() {
        return conclusionItemSimpleCodeList;
    }

    public void setConclusionItemSimpleCodeList(List<TsSimpleCode> conclusionItemSimpleCodeList) {
        this.conclusionItemSimpleCodeList = conclusionItemSimpleCodeList;
    }

    public List<TsSimpleCode> getConclusionResultSimpleCodeList() {
        return conclusionResultSimpleCodeList;
    }

    public void setConclusionResultSimpleCodeList(List<TsSimpleCode> conclusionResultSimpleCodeList) {
        this.conclusionResultSimpleCodeList = conclusionResultSimpleCodeList;
    }

    public List<TdZwCheckItemRst> getCheckItemRstList() {
        return checkItemRstList;
    }

    public void setCheckItemRstList(List<TdZwCheckItemRst> checkItemRstList) {
        this.checkItemRstList = checkItemRstList;
    }

    public List<TdZwCheckClsItem> getCheckClsItemList() {
        return checkClsItemList;
    }

    public void setCheckClsItemList(List<TdZwCheckClsItem> checkClsItemList) {
        this.checkClsItemList = checkClsItemList;
    }

    public Map<Integer, TsSimpleCode> getCheckTypeSimpleCodeMap() {
        return checkTypeSimpleCodeMap;
    }

    public void setCheckTypeSimpleCodeMap(Map<Integer, TsSimpleCode> checkTypeSimpleCodeMap) {
        this.checkTypeSimpleCodeMap = checkTypeSimpleCodeMap;
    }

    public TdZwCheckConclusion getCheckConclusion() {
        return checkConclusion;
    }

    public void setCheckConclusion(TdZwCheckConclusion checkConclusion) {
        this.checkConclusion = checkConclusion;
    }

    public Integer getCheckTypeId() {
        return checkTypeId;
    }

    public void setCheckTypeId(Integer checkTypeId) {
        this.checkTypeId = checkTypeId;
    }

    public String getRecordCategory() {
        return recordCategory;
    }

    public void setRecordCategory(String recordCategory) {
        this.recordCategory = recordCategory;
    }

    public List<TsSimpleCode> getRecordCategorySimpleCodeList() {
        return recordCategorySimpleCodeList;
    }

    public void setRecordCategorySimpleCodeList(List<TsSimpleCode> recordCategorySimpleCodeList) {
        this.recordCategorySimpleCodeList = recordCategorySimpleCodeList;
    }

    public String getSelectRecordCateGorys() {
        return selectRecordCateGorys;
    }

    public void setSelectRecordCateGorys(String selectRecordCateGorys) {
        this.selectRecordCateGorys = selectRecordCateGorys;
    }

    public String getSelectRecordCateGoryIds() {
        return selectRecordCateGoryIds;
    }

    public void setSelectRecordCateGoryIds(String selectRecordCateGoryIds) {
        this.selectRecordCateGoryIds = selectRecordCateGoryIds;
    }

    public Integer getConclusionId() {
        return conclusionId;
    }

    public void setConclusionId(Integer conclusionId) {
        this.conclusionId = conclusionId;
    }

    public Integer getRelCheckState() {
        return relCheckState;
    }

    public void setRelCheckState(Integer relCheckState) {
        this.relCheckState = relCheckState;
    }

    public Map<Integer, TsSimpleCode> getConclusionResultSimpleCodeMap() {
        return conclusionResultSimpleCodeMap;
    }

    public void setConclusionResultSimpleCodeMap(Map<Integer, TsSimpleCode> conclusionResultSimpleCodeMap) {
        this.conclusionResultSimpleCodeMap = conclusionResultSimpleCodeMap;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public boolean isIfResetComp() {
        return ifResetComp;
    }

    public void setIfResetComp(boolean ifResetComp) {
        this.ifResetComp = ifResetComp;
    }

    public String getUnPassItem() {
        return unPassItem;
    }

    public void setUnPassItem(String unPassItem) {
        this.unPassItem = unPassItem;
    }

    public List<TsSimpleCode> getConclusionItemSimpleCodeViewList() {
        return conclusionItemSimpleCodeViewList;
    }

    public void setConclusionItemSimpleCodeViewList(List<TsSimpleCode> conclusionItemSimpleCodeViewList) {
        this.conclusionItemSimpleCodeViewList = conclusionItemSimpleCodeViewList;
    }

    public List<TsSimpleCode> getScoreRstSimpleCodeViewList() {
        return scoreRstSimpleCodeViewList;
    }

    public void setScoreRstSimpleCodeViewList(List<TsSimpleCode> scoreRstSimpleCodeViewList) {
        this.scoreRstSimpleCodeViewList = scoreRstSimpleCodeViewList;
    }
}
