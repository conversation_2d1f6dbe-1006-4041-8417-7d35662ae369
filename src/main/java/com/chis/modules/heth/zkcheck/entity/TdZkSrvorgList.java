package com.chis.modules.heth.zkcheck.entity;

import javax.persistence.*;
import java.util.Date;

import com.chis.modules.heth.comm.entity.TdZwSrvorginfoComm;
import com.chis.modules.system.entity.TsUserInfo;
import java.math.BigDecimal;

/**
 * 
 * <AUTHOR>
 * @createTime 2024-11-1
 */
@Entity
@Table(name = "TD_ZK_SRVORG_LIST")
@SequenceGenerator(name = "TdZkSrvorgList", sequenceName = "TD_ZK_SRVORG_LIST_SEQ", allocationSize = 1)
public class TdZkSrvorgList implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZkSrvorgPlan fkByMainId;
	private TdZwSrvorginfoComm fkByOrgId;
	private Integer state;
	private Integer checkState;
	private Integer auditRst;
	private String auditAdv;
	private Date auditDate;
	private TsUserInfo fkByAuditPsnId;
	private Date inspectDate;
	private BigDecimal totalCheckVal;
	private BigDecimal totalScoreVal;
	private Integer checkRstId;
	private Integer riskRstId;
	private String writePath;
	private String checkTablePath;
	private String improveFileAddr;
	private Integer corrAuditRst;
	private String corrAuditAdv;
	private Date corrAuditDate;
	private Integer corrAuditPsnId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZkSrvorgList() {
	}

	public TdZkSrvorgList(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZkSrvorgList")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZkSrvorgPlan getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZkSrvorgPlan fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TdZwSrvorginfoComm getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TdZwSrvorginfoComm fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Column(name = "CHECK_STATE")	
	public Integer getCheckState() {
		return checkState;
	}

	public void setCheckState(Integer checkState) {
		this.checkState = checkState;
	}	
			
	@Column(name = "AUDIT_RST")	
	public Integer getAuditRst() {
		return auditRst;
	}

	public void setAuditRst(Integer auditRst) {
		this.auditRst = auditRst;
	}	
			
	@Column(name = "AUDIT_ADV")	
	public String getAuditAdv() {
		return auditAdv;
	}

	public void setAuditAdv(String auditAdv) {
		this.auditAdv = auditAdv;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "AUDIT_DATE")			
	public Date getAuditDate() {
		return auditDate;
	}

	public void setAuditDate(Date auditDate) {
		this.auditDate = auditDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "AUDIT_PSN_ID")			
	public TsUserInfo getFkByAuditPsnId() {
		return fkByAuditPsnId;
	}

	public void setFkByAuditPsnId(TsUserInfo fkByAuditPsnId) {
		this.fkByAuditPsnId = fkByAuditPsnId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "INSPECT_DATE")			
	public Date getInspectDate() {
		return inspectDate;
	}

	public void setInspectDate(Date inspectDate) {
		this.inspectDate = inspectDate;
	}	
			
	@Column(name = "TOTAL_CHECK_VAL")	
	public BigDecimal getTotalCheckVal() {
		return totalCheckVal;
	}

	public void setTotalCheckVal(BigDecimal totalCheckVal) {
		this.totalCheckVal = totalCheckVal;
	}	
			
	@Column(name = "TOTAL_SCORE_VAL")	
	public BigDecimal getTotalScoreVal() {
		return totalScoreVal;
	}

	public void setTotalScoreVal(BigDecimal totalScoreVal) {
		this.totalScoreVal = totalScoreVal;
	}	
			
	@Column(name = "CHECK_RST_ID")	
	public Integer getCheckRstId() {
		return checkRstId;
	}

	public void setCheckRstId(Integer checkRstId) {
		this.checkRstId = checkRstId;
	}	
			
	@Column(name = "RISK_RST_ID")	
	public Integer getRiskRstId() {
		return riskRstId;
	}

	public void setRiskRstId(Integer riskRstId) {
		this.riskRstId = riskRstId;
	}	
			
	@Column(name = "WRITE_PATH")	
	public String getWritePath() {
		return writePath;
	}

	public void setWritePath(String writePath) {
		this.writePath = writePath;
	}	
			
	@Column(name = "CHECK_TABLE_PATH")	
	public String getCheckTablePath() {
		return checkTablePath;
	}

	public void setCheckTablePath(String checkTablePath) {
		this.checkTablePath = checkTablePath;
	}	
			
	@Column(name = "IMPROVE_FILE_ADDR")	
	public String getImproveFileAddr() {
		return improveFileAddr;
	}

	public void setImproveFileAddr(String improveFileAddr) {
		this.improveFileAddr = improveFileAddr;
	}	
			
	@Column(name = "CORR_AUDIT_RST")	
	public Integer getCorrAuditRst() {
		return corrAuditRst;
	}

	public void setCorrAuditRst(Integer corrAuditRst) {
		this.corrAuditRst = corrAuditRst;
	}	
			
	@Column(name = "CORR_AUDIT_ADV")	
	public String getCorrAuditAdv() {
		return corrAuditAdv;
	}

	public void setCorrAuditAdv(String corrAuditAdv) {
		this.corrAuditAdv = corrAuditAdv;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "CORR_AUDIT_DATE")			
	public Date getCorrAuditDate() {
		return corrAuditDate;
	}

	public void setCorrAuditDate(Date corrAuditDate) {
		this.corrAuditDate = corrAuditDate;
	}	
			
	@Column(name = "CORR_AUDIT_PSN_ID")	
	public Integer getCorrAuditPsnId() {
		return corrAuditPsnId;
	}

	public void setCorrAuditPsnId(Integer corrAuditPsnId) {
		this.corrAuditPsnId = corrAuditPsnId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}