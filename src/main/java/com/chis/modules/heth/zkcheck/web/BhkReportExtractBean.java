package com.chis.modules.heth.zkcheck.web;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.heth.comm.javabean.TdTjBhkInfoBean;
import com.chis.modules.heth.zkcheck.entity.TdZkExtractRule;
import com.chis.modules.heth.zkcheck.json.RptExtractKeyMapParamJson;
import com.chis.modules.heth.zkcheck.json.RptExtractRequestJson;
import com.chis.modules.heth.zkcheck.logic.BhkRptExtractResponseDTO;
import com.chis.modules.heth.zkcheck.service.BhkReportExtractService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.component.outputpanel.OutputPanel;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 体检报告抽取
 */
@ManagedBean(name = "bhkReportExtractBean")
@ViewScoped
public class BhkReportExtractBean extends FacesEditBean implements IProcessData {

    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private BhkReportExtractService bhkReportExtractService = SpringContextHolder.getBean(BhkReportExtractService.class);
    /***查询条件-地区*/
    private List<TsZone> zoneList;
    private String searchZoneGb;
    private String searchZoneName;

    /***查询条件-体检机构*/
    private String searchUnitName;
    private String searchUnitId;

    /***查询条件-抽取类别*/
    private String[] searchBhkType;
    private List<TsSimpleCode> bhkTypeList;

    /***查询条件-抽取日期*/
    private Date searchSDate;
    private Date searchEDate;
    /***查询条件-危害因素结论*/
    private List<TsSimpleCode> searchBhkrstList;
    private String searchBhkrstName;
    private String searchSelBhkrstIds;

    private Integer rid;
    private Object[] extractBhk;

    /**
     * 体检基本信息-详情
     */
    private TdTjBhkInfoBean tjBhkInfoBean = new TdTjBhkInfoBean();
    /**
     * 详情 rid
     */
    private Integer viewRids;


    /****************************抽取规则********************************/
    /**
     * 抽取-地区
     */
    private String extractZoneGb;
    /**
     * 抽取-地区
     */
    private String extractZoneName;
    /**
     * 抽取-机构
     */
    private String extractUnitName;
    /**
     * 抽取-机构
     */
    private String extractUnitId;
    /**
     * 抽取-体检日期开始时间
     */
    private Date bhkDateStr;

    /**
     * 抽取-体检日期结束时间
     */
    private Date bhkDateEnd;

    /**
     * 抽取-报告出具日期开始时间
     */
    private Date rptDateStr;

    /**
     * 抽取-报告出具日期结束时间
     */
    private Date rptDateEnd;

    /**
     * 抽取-总数
     */
    private List<Object[]> extractTypeList;
    /**
     * 抽取-列表
     */

    private List<TdZkExtractRule> extractRuleList;
    /**
     * 抽取-列表对象
     */
    private TdZkExtractRule extractRule;
    /**
     * 抽取-胸片结论
     */
    private List<SelectItem> rabatList;
    /**
     * 听力检测结论 -码表5622
     */
    private List<TsSimpleCode> hearingSimpleList;
    /**
     * 抽取-预警关联
     */
    private List<SelectItem> warningList;
    /**
     * 在岗状态 -码表5009
     */
    private List<TsSimpleCode> onDutyStatusList;
    private Map<Integer, TsSimpleCode> simpleCodeMap;

    private String redStar;
    private Integer extractId;

    // 删除接口是否加密
    private boolean ifDebug;
    // AES加密的key
    private String encryptKey;
    // 删除接口地址
    private String delUrl;
    // 电测听的判断异常的项目编码
    private List<String> specItemCodeList;

    /****************************抽取规则********************************/

    public BhkReportExtractBean() {
        this.ifSQL = true;
        this.redStar = "<font color='red'>*</font>";
        // 抽取日期初始化
        this.searchSDate = DateUtils.getYearFirstDay(new Date());
        this.searchEDate = new Date();
        // 码表初始化
        initTsSimpleCode();
        // 地区
        initZone();
        //抽取相关信息初始化
        this.initExtract();
        this.initInterfaceParams();
        this.searchAction();
    }

    /**
     * <p>方法描述：抽取相关信息点初始化</p>
     *
     * @MethodAuthor hsj 2024-07-02 14:04
     */
    private void initExtract() {
        this.rabatList = new ArrayList<>();
        this.rabatList.add(new SelectItem("0", "未见异常"));
        this.rabatList.add(new SelectItem("1", "尘肺样改变"));
        this.rabatList.add(new SelectItem("2", "其他异常"));
        this.warningList = new ArrayList<>();
        this.warningList.add(new SelectItem("0", "否"));
        this.warningList.add(new SelectItem("1", "是"));
        this.hearingSimpleList = this.commService.findNumSimpleCodesByTypeId("5622");
        this.specItemCodeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(this.hearingSimpleList)) {
            for (TsSimpleCode simpleCode : this.hearingSimpleList) {
                this.fillSpecItemCodeList(simpleCode.getExtendS3());
            }
        }
        this.onDutyStatusList = this.commService.findNumSimpleCodesByTypeId("5009");
    }

    /**
     * <p>Description：地区初始化 </p>
     * <p>Author： yzz 2024-06-29 </p>
     */
    public void initZone() {
        // 地区信息初始化
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb(), true, "", "");
        // 默认当前省份
        this.searchZoneGb = this.zoneList.get(0).getZoneGb();
        this.searchZoneName = this.zoneList.get(0).getZoneName();
    }


    /**
     * <p>Description：码表初始化 </p>
     * <p>Author： yzz 2024-06-29 </p>
     */
    public void initTsSimpleCode() {
        this.simpleCodeMap = new HashMap<>();
        //抽取类别
        this.bhkTypeList = commService.findNumSimpleCodesByTypeId("5621");
        if (!CollectionUtils.isEmpty(this.bhkTypeList)) {
            for (TsSimpleCode code : this.bhkTypeList) {
                this.simpleCodeMap.put(code.getRid(), code);
            }
        }
        //危害因素结论
        this.searchBhkrstList = commService.findSimpleCodesByTypeId("5005");
    }


    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();
        sql.append(" select  T.RID, ");
        sql.append("         CASE WHEN T1.ZONE_TYPE >2 THEN SUBSTR(T1.FULL_NAME, INSTR(T1.FULL_NAME,'_')+1) ELSE T1.FULL_NAME END A1, ");
        sql.append("         T2.UNIT_NAME as A2, ");
        sql.append("         T3.CODE_NAME as A3, ");
        sql.append("         T.PERSON_NAME as A4, ");
        sql.append("         T.BHK_CODE as A5, ");
        sql.append("         T.PRT_DATE as A6, ");
        sql.append("         T4.CODE_NAME as A7, ");
        sql.append("         case when T3.EXTENDS1 ='1' then T5.CODE_NAME when T3.EXTENDS1 ='2' then T6.CODE_NAME end as A8, ");
        sql.append("         T.TCHBADRSNTIM as A9, ");
        sql.append("         T7.CRPT_NAME as A10, ");
        sql.append("         T.EXTRACT_DATE as A11, ");
        sql.append("         case when nvl(T.STATE,0)>0 then 1 else 0 end A12, ");
        sql.append("         T.ORG_ID as A13 ");
        sql.append(" from TD_ZK_EXTRACT_BHK T ");
        sql.append(" inner join TS_ZONE T1 on T.ZONE_ID=T1.RID ");
        sql.append(" inner join TB_TJ_SRVORG T2 on T.ORG_ID=T2.RID ");
        sql.append(" left join TS_SIMPLE_CODE T3 on T.EXTRACT_TYPE_ID=T3.RID ");
        sql.append(" left join TS_SIMPLE_CODE T4 on T.BHK_RST_ID=T4.RID ");
        sql.append(" left join TS_SIMPLE_CODE T5 on T.CHEST_RST_ID=T5.RID ");
        sql.append(" left join TS_SIMPLE_CODE T6 on T.HEAR_RST_ID=T6.RID ");
        sql.append(" left join TB_TJ_CRPT T7 on T.ENTRUST_CRPT_ID = T7.RID ");
        genQueryCriteria(sql, this.paramMap);
        String searchSql = sql + " order by T.EXTRACT_DATE desc,T1.ZONE_GB,T2.UNIT_NAME,T.PERSON_NAME ";
        String countSql = " SELECT COUNT(*) from (" + sql + ")";
        return new String[]{searchSql, countSql};
    }

    /**
     * 生成查询标准条件的SQL语句和参数映射。
     *
     * @param sql      用于构建SQL查询语句的StringBuffer对象。
     * @param paramMap 用于存储SQL查询中参数及其值的映射关系的Map对象。
     */
    private void genQueryCriteria(StringBuffer sql, Map<String, Object> paramMap) {
        // 本用户
        sql.append(" WHERE T.EXTRACT_USER_ID = ").append(Global.getUser().getRid());
        // 地区
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            sql.append(" AND T1.ZONE_GB LIKE :zoneGb escape '\\\'");
            paramMap.put("zoneGb", ZoneUtil.zoneSelect(this.searchZoneGb.trim()) + "%");
        }
        // 体检机构
        if (StringUtils.isNotBlank(this.searchUnitId)) {
            sql.append(" AND T.ORG_ID IN (:orgId)");
            paramMap.put("orgId", StringUtils.string2list(this.searchUnitId, ","));
        }
        // 抽取类别
        if (ObjectUtil.isNotEmpty(this.searchBhkType)) {
            sql.append(" AND T.EXTRACT_TYPE_ID in(:extractTypeId)");
            paramMap.put("extractTypeId", Arrays.asList(this.searchBhkType));
        }
        // 抽取日期
        if (null != this.searchSDate) {
            sql.append(" AND T.EXTRACT_DATE >= TO_DATE(:searchSDate,'yyyy-MM-dd')");
            paramMap.put("searchSDate", DateUtils.formatDate(searchSDate, "yyyy-MM-dd"));
        }
        if (null != this.searchEDate) {
            sql.append(" AND T.EXTRACT_DATE <= TO_DATE(:searchEDate,'yyyy-MM-dd HH24:mi:ss')");
            paramMap.put("searchEDate", DateUtils.formatDate(searchEDate, "yyyy-MM-dd") + " 23:59:59");
        }
        // 危害因素结论
        if (StringUtils.isNotBlank(this.searchSelBhkrstIds)) {
            sql.append(" AND T.BHK_RST_ID IN (:bhkRstId)");
            paramMap.put("bhkRstId", StringUtils.string2list(this.searchSelBhkrstIds, ","));
        }
    }


    /**
     * <p>Description：查询体检机构 </p>
     * <p>Author： yzz 2024-06-29 </p>
     */
    public void selUnitAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 625, null, 500);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add(this.searchUnitId);
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<String>();
        paramList.add(this.searchZoneGb);
        paramMap.put("searchZoneCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
    }

    /**
     * <p>Description：处理选择的体检机构 </p>
     * <p>Author： yzz 2024-06-29 </p>
     */
    public void onSelectUnitAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (selectedMap == null || selectedMap.isEmpty()) {
            return;
        }
        List<TbTjSrvorg> list = (List<TbTjSrvorg>) selectedMap.get("selectPros");
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        StringBuffer names = new StringBuffer();
        StringBuffer ids = new StringBuffer();
        for (TbTjSrvorg t : list) {
            names.append("，").append(t.getUnitName());
            ids.append(",").append(t.getRid());
        }
        this.searchUnitId = ids.substring(1);
        this.searchUnitName = names.substring(1);
    }

    /**
     * <p>Description：清空单位 </p>
     * <p>Author： yzz 2024-06-29 </p>
     */
    public void clearUnit() {
        this.searchUnitId = null;
        this.searchUnitName = null;
    }

    /**
     * <p>Description：删除 </p>
     * <p>Author： yzz 2024-06-29 </p>
     */
    public void delAction() {
        if (this.rid == null) {
            return;
        }
        try {
            bhkReportExtractService.delExtractBhkById(this.rid);
            this.searchAction();
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }


    @Override
    public void processData(List<?> list) {
    }

    /**
     * 导出数据前的处理函数。
     * 该函数用于在导出数据前检查是否有符合查询条件的数据。如果没有数据，则提示用户；如果有数据，则触发导出文件的下载。
     */
    public void exportBefore() {
        RequestContext context = RequestContext.getCurrentInstance();
        Map<String, Object> exportParamMap = new HashMap<>();
        StringBuffer sql = new StringBuffer();
        sql.append("  SELECT COUNT(*) ");
        sql.append(" from TD_ZK_EXTRACT_BHK T ");
        sql.append(" inner join TS_ZONE T1 on T.ZONE_ID=T1.RID ");
        sql.append(" inner join TB_TJ_SRVORG T2 on T.ORG_ID=T2.RID ");
        sql.append(" left join TS_SIMPLE_CODE T3 on T.EXTRACT_TYPE_ID=T3.RID ");
        sql.append(" left join TS_SIMPLE_CODE T4 on T.BHK_RST_ID=T4.RID ");
        sql.append(" left join TS_SIMPLE_CODE T5 on T.CHEST_RST_ID=T5.RID ");
        sql.append(" left join TS_SIMPLE_CODE T6 on T.HEAR_RST_ID=T6.RID ");
        sql.append(" left join TB_TJ_CRPT T7 on T.ENTRUST_CRPT_ID = T7.RID ");
        genQueryCriteria(sql, exportParamMap);
        int count = this.commService.findCountBySql(sql.toString(), exportParamMap);
        if (count == 0) {
            JsfUtil.addErrorMessage("无可导出的数据！");
            return;
        }
        context.execute("getDownloadFileClick();");
    }

    /**
     * 导出体检报告抽取数据为Excel文件。
     *
     * @return 返回DefaultStreamedContent对象，用于浏览器下载Excel文件。返回null表示导出失败。
     * 文件名为"体检报告抽取.xlsx"，编码为UTF-8，内容包含体检报告抽取相关数据。
     */
    public DefaultStreamedContent export() {
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("showStatus();");
        String excelTitle = "体检报告抽取";
        String[] excelHeaders = new String[]{"地区", "体检机构名称", "抽取类别", "姓名", "身份证号码", "用工单位名称",
                "体检编号", "体检日期", "报告出具日期", "在岗状态", "接害工龄(年)", "抽取危害因素结论", "抽取危害因素", "是否复检",
                "末次体检危害因素结论", "岗位/工种", "接触的职业性危害因素种类", "用人单位名称", "抽取日期"};
        List<ExcelExportObject[]> excelExportObjects = new ArrayList<>();
        try {
            excelExportObjects = pakExcelExportDataList(executeExportSql(), excelHeaders.length);
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("文件导出错误！");
            return null;
        }
        ExcelExportUtil excelExportUtil = new ExcelExportUtil(excelTitle, excelHeaders, excelExportObjects);
        excelExportUtil.setFrozenPaneRowsNum(2);
        Workbook wb = excelExportUtil.exportExcel();
        if (wb != null) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                String fileName = excelTitle + ".xlsx";
                fileName = URLEncoder.encode(fileName, "UTF-8");
                wb.write(baos);
                baos.flush();
                byte[] aa = baos.toByteArray();
                context.execute("hideStatus();");
                return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
            } catch (Exception e) {
                JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            }
        }
        context.execute("hideStatus();");
        return null;
    }

    /**
     * 执行导出SQL查询，获取并处理数据，用于报表生成。
     *
     * @return 返回一个包含查询结果的Object数组列表。
     */
    private List<Object[]> executeExportSql() {
        Map<String, Object> paramMap = new HashMap<>();
        // 生成查询条件SQL
        StringBuffer queryCriteriaSql = new StringBuffer();
        genQueryCriteria(queryCriteriaSql, paramMap);
        String infoSql = "SELECT " +
                "  T1.FULL_NAME   AS P0, " +
                "  T2.UNIT_NAME   AS P1, " +
                "  T3.CODE_NAME   AS P2, " +
                "  T.PERSON_NAME  AS P3, " +
                "  T.IDC          AS P4, " +
                "  C2.CRPT_NAME   AS P5, " +
                "  T.BHK_CODE     AS P6, " +
                "  T.BHK_DATE     AS P7, " +
                "  T.PRT_DATE     AS P8, " +
                "  T4.CODE_NAME   AS P9, " +
                "  T.TCHBADRSNTIM AS P10, " +
                "  T5.CODE_NAME   AS P11, " +
                "  ''             AS P12, " +
                "  T.IF_RHK       AS P13, " +
                "  T6.CODE_NAME   AS P14, " +
                "  T7.CODE_NAME   AS P15, " +
                "  ''             AS P16, " +
                "  C1.CRPT_NAME   AS P17, " +
                "  T.EXTRACT_DATE AS P18, " +
                "  T.RID          AS P19, " +
                "  T.WORK_OTHER   AS P20 " +
                " FROM TD_ZK_EXTRACT_BHK T " +
                "  INNER JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID " +
                "  INNER JOIN TB_TJ_SRVORG T2 ON T.ORG_ID = T2.RID " +
                "  LEFT JOIN TS_SIMPLE_CODE T3 ON T.EXTRACT_TYPE_ID = T3.RID " +
                "  LEFT JOIN TS_SIMPLE_CODE T4 ON T.ONGUARD_STATEID = T4.RID " +
                "  LEFT JOIN TS_SIMPLE_CODE T5 ON T.BHK_RST_ID = T5.RID " +
                "  LEFT JOIN TS_SIMPLE_CODE T6 ON T.RE_BHK_RST_ID = T6.RID " +
                "  LEFT JOIN TS_SIMPLE_CODE T7 ON T.WORK_TYPE_ID = T7.RID " +
                "  LEFT JOIN TB_TJ_CRPT C1 ON T.CRPT_ID = C1.RID " +
                "  LEFT JOIN TB_TJ_CRPT C2 ON T.ENTRUST_CRPT_ID = C2.RID " +
                queryCriteriaSql +
                " GROUP BY T.RID, T1.ZONE_GB, T1.FULL_NAME, T2.UNIT_NAME, T3.CODE_NAME, T.PERSON_NAME, T.IDC, " +
                "  C2.CRPT_NAME, T.BHK_CODE, T.BHK_DATE, T.PRT_DATE, T4.CODE_NAME, T.TCHBADRSNTIM, " +
                "  T5.CODE_NAME, T.IF_RHK, T6.CODE_NAME, T7.CODE_NAME, C1.CRPT_NAME, T.EXTRACT_DATE, T.WORK_OTHER " +
                "ORDER BY T.EXTRACT_DATE DESC, T1.ZONE_GB, T2.UNIT_NAME, T.PERSON_NAME ";
        List<Object[]> list = CollectionUtil.castList(
                Object[].class, this.commService.findDataBySqlNoPage(infoSql, paramMap)
        );
        // 获取抽取危害因素的数据
        Map<String, List<String>> badRsnMap = getBadRsnData(0, queryCriteriaSql, paramMap);
        // 获取接触危害因素的数据
        Map<String, List<String>> tchBadRsnMap = getBadRsnData(1, queryCriteriaSql, paramMap);

        // 对查询结果进行处理，填充抽取危害因素和是否复检的信息
        for (Object[] data : list) {
            String rid = StringUtils.objectToString(data[19]);
            // 填充抽取危害因素
            data[12] = "";
            if (badRsnMap.containsKey(rid)) {
                data[12] = StringUtils.list2string(badRsnMap.get(rid), "，");
            }
            // 填充是否复检
            data[13] = "1".equals(StringUtils.objectToString(data[13])) ? "是" : "否";
            // 填充工种
            String workType = StringUtils.objectToString(data[15]);
            String workTypeOther = StringUtils.objectToString(data[20]);
            if (StringUtils.isBlank(workType)) {
                data[15] = workTypeOther;
            } else if (StringUtils.isNotBlank(workTypeOther)) {
                data[15] = workType + "（" + workTypeOther + "）";
            } else {
                data[15] = workType;
            }
            // 填充接触危害因素
            data[16] = "";
            if (tchBadRsnMap.containsKey(rid)) {
                data[16] = StringUtils.list2string(tchBadRsnMap.get(rid), "，");
            }
        }
        return list;
    }

    /**
     * 获取危害因素数据，根据类型查询不同的表格。
     *
     * @param type             查询类型，1 代表查询 TD_ZK_BHK_TCH_BADRSNS 表，其他值查询 TD_ZK_BHK_BADRSNS。
     * @param queryCriteriaSql 动态查询条件SQL语句，用于过滤数据。
     * @param paramMap         SQL查询参数，键值对形式存储。
     * @return 返回一个Map，键为RID（记录ID），值为对应RID的危害因素列表。
     * Map结构为：{RID: [CODE_NAME, ...], ...}，CODE_NAME表示危害因素名称。
     */
    private Map<String, List<String>> getBadRsnData(int type,
                                                    StringBuffer queryCriteriaSql,
                                                    Map<String, Object> paramMap) {
        String tableName = "TD_ZK_BHK_BADRSNS";
        if (type == 1) {
            tableName = "TD_ZK_BHK_TCH_BADRSNS";
        }
        Map<String, List<String>> badRsnMap = new HashMap<>();
        String badRsnSql = "SELECT T.RID, SC.CODE_NAME " +
                " FROM TD_ZK_EXTRACT_BHK T " +
                "  INNER JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID " +
                "  LEFT JOIN " + tableName + " BR ON T.RID = BR.MAIN_ID " +
                "  LEFT JOIN TS_SIMPLE_CODE SC ON BR.BADRSN_ID = SC.RID " +
                queryCriteriaSql +
                " GROUP BY T.RID, SC.CODE_NAME, SC.NUM, SC.CODE_LEVEL_NO, SC.CODE_NO " +
                " ORDER BY SC.NUM, SC.CODE_LEVEL_NO, SC.CODE_NO ";
        List<Object[]> badRsnList = CollectionUtil.castList(
                Object[].class, this.commService.findDataBySqlNoPage(badRsnSql, paramMap)
        );
        for (Object[] objects : badRsnList) {
            String rid = StringUtils.objectToString(objects[0]);
            String badRsn = StringUtils.objectToString(objects[1]);
            if (StringUtils.isBlank(rid) || StringUtils.isBlank(badRsn)) {
                continue;
            }
            if (!badRsnMap.containsKey(rid)) {
                badRsnMap.put(rid, new ArrayList<String>());
            }
            badRsnMap.get(rid).add(badRsn);
        }
        return badRsnMap;
    }

    /**
     * 将数据列表打包为Excel导出对象列表。
     *
     * @param dataList   数据列表，每个元素是一个对象数组，代表一行数据。
     * @param headerSize Excel表格的头部大小，用于确定每个数据行中导出对象的数量。
     * @return 包含Excel导出对象的列表，每个对象数组对应一行数据。
     */
    private List<ExcelExportObject[]> pakExcelExportDataList(List<Object[]> dataList, int headerSize) {
        // 检查数据列表和头部大小是否有效，如果无效则返回空列表
        if (ObjectUtil.isEmpty(dataList) || headerSize <= 0) {
            return new ArrayList<>();
        }

        // 初始化Excel导出对象列表
        List<ExcelExportObject[]> excelExportObjectList = new ArrayList<>();

        // 遍历数据列表中的每一行数据
        for (Object[] data : dataList) {
            // 初始化当前行的导出对象数组
            ExcelExportObject[] objects = new ExcelExportObject[headerSize];

            // 用于索引当前行的导出对象数组
            int index = 0;

            // 将每个数据项打包为Excel导出对象，并根据其内容设置对齐方式
            // 地区
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_LEFT);
            // 体检机构名称
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_LEFT);
            // 抽取类别
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_CENTER);
            // 姓名
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_CENTER);
            // 身份证号码
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_CENTER);
            // 用工单位名称
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_LEFT);
            // 体检编号
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_CENTER);
            // 体检日期
            objects[index] = pakExcelExportObject(pakDate(data[index++]), XSSFCellStyle.ALIGN_CENTER, (short) 49);
            // 报告出具日期
            objects[index] = pakExcelExportObject(pakDate(data[index++]), XSSFCellStyle.ALIGN_CENTER, (short) 49);
            // 在岗状态
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_CENTER);
            // 接害工龄(年)
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_CENTER);
            // 抽取危害因素结论
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_CENTER);
            // 抽取危害因素
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_LEFT);
            // 是否复检
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_CENTER);
            // 末次体检危害因素结论
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_CENTER);
            // 岗位/工种
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_CENTER);
            // 接触的职业性危害因素种类
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_LEFT);
            // 用人单位名称
            objects[index] = pakExcelExportObject(data[index++], XSSFCellStyle.ALIGN_LEFT);
            // 抽取日期
            objects[index] = pakExcelExportObject(pakDate(data[index]), XSSFCellStyle.ALIGN_CENTER, (short) 49);

            // 将当前行的导出对象数组添加到列表中
            excelExportObjectList.add(objects);
        }

        // 返回打包后的Excel导出对象列表
        return excelExportObjectList;
    }

    /**
     * 根据水平样式封装单元格（垂直居中+自动换行）
     *
     * @param object         单元格内容
     * @param alignStyleType 水平样式
     * @return 单元格
     */
    private ExcelExportObject pakExcelExportObject(Object object, short alignStyleType) {
        return new ExcelExportObject(
                StringUtils.objectToString(object), alignStyleType, XSSFCellStyle.VERTICAL_CENTER, true
        );
    }

    /**
     * 根据水平样式+单元格格式封装单元格（垂直居中+自动换行）
     *
     * @param object         单元格内容
     * @param alignStyleType 水平样式
     * @param dataFormat     单元格格式
     * @return 单元格
     */
    private ExcelExportObject pakExcelExportObject(Object object, short alignStyleType, short dataFormat) {
        ExcelExportObject excelExportObject = pakExcelExportObject(object, alignStyleType);
        excelExportObject.setDataFormat(dataFormat);
        return excelExportObject;
    }

    /**
     * 封装日期字符串
     *
     * @param data 日期
     * @return 日期String
     */
    private String pakDate(Object data) {
        Date date = ObjectUtil.convert(Date.class, data, null);
        String dateStr = "";
        if (date != null) {
            dateStr = DateUtils.formatDate(date, "yyyy/MM/dd");
        }
        return dateStr;
    }

    @Override
    public void addInit() {
    }

    /**
     * <p>Description：详情前验证 </p>
     * <p>Author： yzz 2024-07-03 </p>
     */
    public void beforeViewInit() {
        viewRids = null;
        if (this.extractBhk == null || extractBhk[13] == null || extractBhk[5] == null) {
            return;
        }
        // 根据体检机构+体检编码查询体检记录
        String sql = " select RID from TD_TJ_BHK where BHKORG_ID =" + Integer.parseInt(extractBhk[13].toString())
                + " and BHK_CODE='" + extractBhk[5].toString() + "'";
        List<Object> bhkRids = commService.findDataBySqlNoPage(sql, null);

        if (CollectionUtils.isEmpty(bhkRids)) {
            JsfUtil.addErrorMessage("体检记录不存在！");
            return;
        }
        viewRids = Integer.parseInt(bhkRids.get(0).toString());
        /*初始化体检基本信息*/
        tjBhkInfoBean.setRid(viewRids);
        tjBhkInfoBean.setIfManagedOrg(true);
        tjBhkInfoBean.initBhkInfo();
        this.forwardViewPage();
    }


    @Override
    public void viewInit() {
    }

    /***************************抽取页面**********************************/
    @Override
    public void modInit() {
        this.extractRuleList = this.bhkReportExtractService.findTdZkExtractRuleByUserId(Global.getUser().getRid());
        this.initIfEdit();
        //按照fkByExtractTypeId的num,code、priority、rid排序
        this.sortExtractRuleList();
        this.extractRule = new TdZkExtractRule();
        this.extractRule.setFkByExtractTypeId(new TsSimpleCode());
        this.extractZoneGb = Global.getUser().getTsUnit().getFkByManagedZoneId().getZoneGb();
        this.extractZoneName = Global.getUser().getTsUnit().getFkByManagedZoneId().getZoneName();
        //开始日期默认上一年的7月1号
        this.rptDateStr = DateUtils.getFixedDate(-1, 7, 1);
        //结束日期默认当年的6月30号
        this.rptDateEnd = DateUtils.getFixedDate(0, 6, 30);
        this.bhkDateStr = null;
        this.bhkDateEnd = null;
        //抽取总数
        this.extractTypeList = new ArrayList<>();
        this.extractUnitName = null;
        this.extractUnitId = null;
        if (CollectionUtils.isEmpty(this.bhkTypeList)) {
            return;
        }
        for (TsSimpleCode code : this.bhkTypeList) {
            Object[] obj = new Object[4];
            obj[0] = code.getRid();
            obj[1] = code.getCodeName();
            obj[3] = code.getExtendS1();
            this.extractTypeList.add(obj);
        }
    }

    /**
     * <p>方法描述：初始化默认可编辑</p>
     *
     * @MethodAuthor hsj 2024-08-02 10:44
     */
    private void initIfEdit() {
        if (CollectionUtils.isEmpty(this.extractRuleList)) {
            return;
        }
        for (TdZkExtractRule rule : this.extractRuleList) {
            rule.setIfEdit(true);
        }
    }

    /**
     * <p>方法描述：按照fkByExtractTypeId的num,code、priority、rid排序</p>
     *
     * @MethodAuthor hsj 2024-08-02 9:42
     */
    private void sortExtractRuleList() {
        if (CollectionUtils.isEmpty(this.extractRuleList)) {
            return;
        }
        Collections.sort(this.extractRuleList, new Comparator<TdZkExtractRule>() {
            @Override
            public int compare(TdZkExtractRule o1, TdZkExtractRule o2) {
                if (ObjectUtil.isNotNull(o1.getFkByExtractTypeId().getNum()) && ObjectUtil.isNotNull(o2.getFkByExtractTypeId().getNum())) {
                    int result = Integer.compare(o1.getFkByExtractTypeId().getNum(), o2.getFkByExtractTypeId().getNum());
                    if (result != 0) {
                        return result;
                    }
                    result = o1.getFkByExtractTypeId().getCodeNo().compareTo(o2.getFkByExtractTypeId().getCodeNo());
                    if (result != 0) {
                        return result;
                    }
                    result = Integer.compare(o1.getPriority(), o2.getPriority());
                    if (result != 0) {
                        return result;
                    }
                    return o1.getRid().compareTo(o2.getRid());
                } else if (ObjectUtil.isNotNull(o1.getFkByExtractTypeId().getNum())) {
                    return -1;
                } else if (ObjectUtil.isNotNull(o2.getFkByExtractTypeId().getNum())) {
                    return 1;
                }
                return 0;
            }
        });
    }

    /**
     * <p>方法描述：抽取-选择机构弹出框</p>
     *
     * @MethodAuthor hsj 2024-07-02 11:09
     */
    public void selExtractUnitAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 625, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add(this.extractUnitId);
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<>();
        paramList.add(this.extractZoneGb);
        paramMap.put("searchZoneCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
    }

    /**
     * <p>方法描述：选中抽取机构</p>
     *
     * @MethodAuthor hsj 2024-07-02 11:10
     */
    public void onSelectExtractUnitAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (selectedMap == null || selectedMap.size() == 0) {
            return;
        }
        List<TbTjSrvorg> list = (List<TbTjSrvorg>) selectedMap.get("selectPros");
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> names = new ArrayList<>();
        List<String> rids = new ArrayList<>();
        for (TbTjSrvorg t : list) {
            names.add(t.getUnitName());
            rids.add(t.getRid().toString());
        }
        this.extractUnitName = StringUtils.list2string(names, "，");
        this.extractUnitId = StringUtils.list2string(rids, ",");
    }

    /**
     * <p>方法描述：清空抽取机构</p>
     *
     * @MethodAuthor hsj 2024-07-02 11:19
     */
    public void clearExtractUnit() {
        this.extractUnitId = null;
        this.extractUnitName = null;
    }

    /**
     * <p>方法描述：输入抽取数量</p>
     *
     * @MethodAuthor hsj 2024-08-02 10:09
     */
    public void changeExtractTypeNum(Object[] obj) {
        if (CollectionUtils.isEmpty(this.extractRuleList)) {
            return;
        }
        boolean ifEidt = !new Integer(0).equals(Convert.toInt(obj[2]));
        for (TdZkExtractRule rule : this.extractRuleList) {
            if (!rule.getFkByExtractTypeId().getRid().equals(Convert.toInt(obj[0]))) {
                continue;
            }
            rule.setIfEdit(ifEidt);
            if (ifEidt) {
              continue;
            }
            rule.setSampleNumber(null);
        }
    }

    /**
     * <p>方法描述：添加抽取规则</p>
     *
     * @MethodAuthor hsj 2024-07-02 13:49
     */
    public void addExtractRuleAction() {
        if (CollectionUtils.isEmpty(this.bhkTypeList)) {
            JsfUtil.addErrorMessage("抽取类型不能为空！");
        }
        this.extractRule = new TdZkExtractRule();
        this.extractRule.setFkByUserId(Global.getUser());
        //抽取类别 5621 -默认第一个
        this.extractRule.setFkByExtractTypeId(this.bhkTypeList.get(0));
        this.extractRule.setExtractTypeId(this.bhkTypeList.get(0).getRid());
        RequestContext.getCurrentInstance().update("tabView:editForm:extractRuleDialog");
        RequestContext.getCurrentInstance().execute("PF('ExtractRuleDialog').show()");
    }

    /**
     * <p>方法描述：抽取规则修改</p>
     *
     * @MethodAuthor hsj 2024-07-02 16:42
     */
    public void editExtractAction() {
        this.extractRule = this.bhkReportExtractService.findTdZkExtractRuleById(this.extractId);
        RequestContext.getCurrentInstance().update("tabView:editForm:extractRuleDialog");
        RequestContext.getCurrentInstance().execute("PF('ExtractRuleDialog').show()");
    }

    /**
     * <p>方法描述：抽取规则删除</p>
     *
     * @MethodAuthor hsj 2024-07-02 16:42
     */
    public void delExtractAction() {
        try {
            this.bhkReportExtractService.delExtractRule(this.extractId);
            this.delExtractIdLine();
            this.sortExtractRuleList();
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("删除失败！");
            e.printStackTrace();
        }

    }

    /**
     * <p>方法描述：刪除当前操作的行数</p>
     *
     * @MethodAuthor hsj 2024-08-02 9:52
     */
    private void delExtractIdLine() {
        if (CollectionUtils.isEmpty(this.extractRuleList)) {
            return;
        }
        TdZkExtractRule zkExtractRule = null;
        for (TdZkExtractRule rule : this.extractRuleList) {
            if (!rule.getRid().equals(this.extractId)) {
                continue;
            }
            zkExtractRule = rule;
            break;
        }
        if (null != zkExtractRule) {
            this.extractRuleList.remove(zkExtractRule);
        }
    }

    /**
     * <p>方法描述：切换抽取类别</p>
     *
     * @MethodAuthor hsj 2024-07-02 14:29
     */
    public void changeExtractType() {
        this.extractRule.setChestResultList(null);
        this.extractRule.setFkByHearingRstId(null);
        this.extractRule.setIfWarn(null);
        if (this.simpleCodeMap.containsKey(this.extractRule.getExtractTypeId())) {
            this.extractRule.setFkByExtractTypeId(this.simpleCodeMap.get(this.extractRule.getExtractTypeId()));
        }

    }

    /**
     * <p>方法描述： 体检危害因素5007码表弹出框</p>
     *
     * @MethodAuthor hsj 2024-07-02 14:40
     */
    public void selExtractBadRsnAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add("危害因素");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>();
        paramList.add("5007");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<>();
        paramList.add(this.extractRule.getBadrsnsIds());
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<>();
        paramList.add("true");
        paramMap.put("ifShowFirstCode", paramList);
        paramList = new ArrayList<>();
        paramList.add("1");
        paramMap.put("selectFirst", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList", options, paramMap);
    }

    /**
     * <p>方法描述：体检危害因素5007选择</p>
     *
     * @MethodAuthor hsj 2024-07-02 15:00
     */
    public void onExtractBadRsnAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null == selectedMap || selectedMap.size() == 0) {
            return;
        }
        List<TsSimpleCode> list = CollectionUtil.castList(TsSimpleCode.class, selectedMap.get("selectPros"));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> names = new ArrayList<>();
        List<String> ids = new ArrayList<>();
        for (TsSimpleCode t : list) {
            names.add(t.getCodeName());
            ids.add(t.getRid().toString());
        }
        this.extractRule.setBadrsnsIds(StringUtils.list2string(ids, ","));
        this.extractRule.setBadrsnsName(StringUtils.list2string(names, "，"));
    }

    /**
     * <p>方法描述：体检危害因素5007清空</p>
     *
     * @MethodAuthor hsj 2024-07-02 14:47
     */
    public void clearExtractBadRsn() {
        this.extractRule.setBadrsnsIds(null);
        this.extractRule.setBadrsnsName(null);
    }

    /**
     * <p>方法描述：添加抽取规则</p>
     *
     * @MethodAuthor hsj 2024-07-02 16:07
     */
    public void saveExtractRuleAction() {
        if (verifyExtractRuleData()) {
            return;
        }
        try {
            this.bhkReportExtractService.saveExtractRule(this.extractRule);
            this.extractRule = this.bhkReportExtractService.findTdZkExtractRuleById(this.extractRule.getRid());
            this.dealIfEdit();
            this.sortExtractRuleList();
            RequestContext.getCurrentInstance().update("tabView:editForm");
            RequestContext.getCurrentInstance().execute("PF('ExtractRuleDialog').hide()");
            JsfUtil.addSuccessMessage("保存成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("保存失败！");
            e.printStackTrace();
        }
    }

    /**
     * <p>方法描述：新增的数据处理是否可编辑</p>
     *
     * @MethodAuthor hsj 2024-08-02 9:57
     */
    private void dealIfEdit() {
        if (this.extractRule.getIfEdit() == null) {
            this.extractRule.setIfEdit(true);
        }
        Integer extractTypeId = this.extractRule.getFkByExtractTypeId().getRid();
        for (Object[] obj : this.extractTypeList) {
            boolean flag = extractTypeId.equals(Convert.toInt(obj[0])) && new Integer(0).equals(Convert.toInt(obj[2]));
            if (!flag) {
               continue;
            }
            this.extractRule.setIfEdit(false);
            break;
        }
        if (!this.extractRule.getIfEdit()) {
            this.extractRule.setSampleNumber(null);
        }
        this.extractId = this.extractRule.getRid();
        this.delExtractIdLine();
        this.extractRuleList.add(this.extractRule);
    }

    /**
     * <p>方法描述：抽取规则验证</p>
     *
     * @MethodAuthor hsj 2024-07-02 16:07
     */
    private boolean verifyExtractRuleData() {
        boolean flag = false;
        if (StringUtils.isBlank(this.extractRule.getBadrsnsIds())) {
            JsfUtil.addErrorMessage("请选择体检危害因素！");
            flag = true;
        }
        if (StringUtils.isBlank(this.extractRule.getBadrsnsRstIds())) {
            JsfUtil.addErrorMessage("请选择单危害因素结论！");
            flag = true;
        }
        if (ObjectUtil.isNull(this.extractRule.getPriority())) {
            JsfUtil.addErrorMessage("优先级不能为空！");
            flag = true;
        }
        return flag;
    }

    /**
     * <p>方法描述：抽取 </p>
     * pw 2024/7/4
     **/
    public void exeExtract() {
        if (this.extractValidate()) {
            return;
        }
        try {
            this.invokeExtractInterface();
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("抽取失败，请联系管理员！");
        }
    }

    /**
     * <p>方法描述：抽取校验 </p>
     * pw 2024/7/4
     **/
    private boolean extractValidate() {
        boolean flag = false;
        if (StringUtils.isBlank(this.extractUnitId)) {
            JsfUtil.addErrorMessage("请选择体检机构！");
            flag = true;
        }
        if (null == this.rptDateStr) {
            JsfUtil.addErrorMessage("报告出具日期开始日期不能为空！");
            flag = true;
        }
        if (null == this.rptDateEnd) {
            JsfUtil.addErrorMessage("报告出具日期结束日期不能为空！");
            flag = true;
        }
        Map<Integer, Integer> totalMap = new LinkedHashMap<>();
        Map<Integer, String> nameMap = new HashMap<>();
        // 大于0的抽取类型的扩展字段1 用于判断胸片或者电测听是否有没有处理完的数据
        Map<Integer, Integer> extRidMap = new HashMap<>();
        if (this.validateExtractTypeTotal(totalMap, nameMap, extRidMap)) {
            flag = true;
        }
        // key 抽取类型rid 抽取份数
        Map<Integer, Integer> ruleCountMap = new HashMap<>();
        if (this.validateExtractRule(ruleCountMap)) {
            flag = true;
        }
        if (!flag) {
            // 数量都不为空的时候 才能进行总数匹配校验
            flag = this.validateExtractRuleCountAndTypeTotal(totalMap, ruleCountMap, nameMap);
        }
        if (!flag && this.validateSpecInfo(extRidMap)) {
            flag = true;
        }
        return flag;
    }

    /**
     * <p>方法描述：特殊校验 - 胸片与电测听 </p>
     * pw 2024/7/4
     **/
    private boolean validateSpecInfo(Map<Integer, Integer> extRidMap) {
        boolean flag = StringUtils.isBlank(this.extractUnitId) ||
                CollectionUtils.isEmpty(extRidMap);
        if (flag) {
            return false;
        }
        flag = false;
        boolean ifMarch = extRidMap.containsKey(1) && this.validateXray();
        if (ifMarch) {
            JsfUtil.addErrorMessage("胸片结论未判定完成，请联系管理员！");
            flag = true;
        }

        ifMarch = extRidMap.containsKey(2) && this.validateAudiometry();
        if (ifMarch) {
            JsfUtil.addErrorMessage("电测听结论未判定完成，请联系管理员！");
            flag = true;
        }
        return flag;
    }

    /**
     * <p>方法描述：胸片校验 </p>
     * pw 2024/7/4
     **/
    private boolean validateXray() {
        return this.bhkReportExtractService.findUnValidateCount(1, this.extractUnitId, this.rptDateStr, this.rptDateEnd, null) > 0;
    }

    /**
     * <p>方法描述：电测听校验 </p>
     * pw 2024/7/4
     **/
    private boolean validateAudiometry() {
        return this.bhkReportExtractService.findUnValidateCount(2, this.extractUnitId, this.rptDateStr, this.rptDateEnd, this.specItemCodeList) > 0;
    }

    /**
     * <p>方法描述： 校验抽取规则与对应抽取总数是否匹配 </p>
     * pw 2024/7/4
     **/
    private boolean validateExtractRuleCountAndTypeTotal(Map<Integer, Integer> totalMap,
                                                         Map<Integer, Integer> ruleCountMap,
                                                         Map<Integer, String> nameMap) {
        boolean flag = CollectionUtils.isEmpty(totalMap);
        if (flag) {
            return false;
        }
        flag = false;
        Set<Integer> keySet = totalMap.keySet();
        for (Integer key : keySet) {
            String codeName = nameMap.get(key);
            Integer count = totalMap.get(key);
            Integer ruleCount = ruleCountMap.get(key);
            if (count > 0 && null == ruleCount) {
                JsfUtil.addErrorMessage(codeName + "抽取总数大于0，请添加" + codeName + "抽取规则！");
                flag = true;
                continue;
            }
            if (null == ruleCount || count.compareTo(ruleCount) == 0) {
                continue;
            }
            if (count == 0) {
                JsfUtil.addErrorMessage(codeName + "所有规则份数之和大于0，" + codeName + "抽取总数必须大于0！");
                flag = true;
                continue;
            }
            if (count.compareTo(ruleCount) > 0) {
                JsfUtil.addErrorMessage(codeName + "抽取总数应小于等于" + codeName + "所有规则份数之和！");
                flag = true;
            }
        }
        return flag;
    }

    /**
     * <p>方法描述：抽取规则校验 </p>
     * pw 2024/7/4
     **/
    private boolean validateExtractRule(Map<Integer, Integer> ruleCountMap) {
        if (CollectionUtils.isEmpty(this.extractRuleList)) {
            JsfUtil.addErrorMessage("请添加抽取规则！");
            return true;
        }
        boolean flag = false;
        boolean ifMarch = false;
        for (TdZkExtractRule extractRule : this.extractRuleList) {
            // 不能编辑的 过滤掉
            if (!extractRule.getIfEdit()) {
                continue;
            }
            Integer sampleNumber = extractRule.getSampleNumber();
            if (null == sampleNumber) {
                ifMarch = true;
                continue;
            }
            Integer extraTypeId = null == extractRule.getFkByExtractTypeId() ? null :
                    extractRule.getFkByExtractTypeId().getRid();
            if (null == extraTypeId) {
                continue;
            }
            Integer count = ruleCountMap.get(extraTypeId);
            if (null == count) {
                count = 0;
            }
            count += sampleNumber;
            ruleCountMap.put(extraTypeId, count);
        }
        if (ifMarch) {
            JsfUtil.addErrorMessage("抽取规则中可编辑的抽取份数不允许为空！");
            flag = true;
        }
        return flag;
    }

    /**
     * <p>方法描述：抽取总数的校验 </p>
     * pw 2024/7/4
     **/
    private boolean validateExtractTypeTotal(Map<Integer, Integer> totalMap,
                                             Map<Integer, String> nameMap,
                                             Map<Integer, Integer> extRidMap) {
        boolean flag = false;
        boolean ifMarch = false;
        for (Object[] arr : this.extractTypeList) {
            if (null == arr[0]) {
                flag = true;
                continue;
            }
            String codeName = null == arr[1] ? null : arr[1].toString();
            if (StringUtils.isBlank(codeName)) {
                continue;
            }
            String str = null == arr[3] ? null : arr[3].toString();
            if (StringUtils.isBlank(str)) {
                JsfUtil.addErrorMessage(codeName + "配置异常，请联系管理员！");
                flag = true;
                continue;
            }
            Integer key = Integer.parseInt(str);
            str = null == arr[2] ? null : arr[2].toString();
            if (StringUtils.isBlank(str)) {
                JsfUtil.addErrorMessage(codeName + "抽取总数不允许为空！");
                flag = true;
                continue;
            }
            Integer total = Integer.parseInt(str);
            Integer rid = Integer.parseInt(arr[0].toString());
            if (total > 0) {
                extRidMap.put(key, rid);
                ifMarch = true;
            }
            nameMap.put(rid, codeName);
            totalMap.put(rid, total);
        }
        if (!flag && !ifMarch) {
            JsfUtil.addErrorMessage("至少有一个抽取总数大于0！");
            flag = true;
        }
        return flag;
    }

    /**
     * <p>方法描述：调用抽取接口 </p>
     * pw 2024/7/4
     **/
    private void invokeExtractInterface() throws Exception {
        String url = this.delUrl + "/bhkRpt/extract";
        String requestJson = JSON.toJSONString(this.generateRequestObj());
        String encodeJson;
        if (this.ifDebug) {
            encodeJson = requestJson;
        } else {
            encodeJson = AesEncryptUtils.aesEncrypt(requestJson, this.encryptKey);
        }
        String reposeJson = HttpRequestUtil.httpRequestByRaw(url, encodeJson);
        if (!this.ifDebug) {
            reposeJson = AesEncryptUtils.aesDecrypt(reposeJson, encryptKey);
        }
        BhkRptExtractResponseDTO responseDTO = JSON.parseObject(reposeJson, BhkRptExtractResponseDTO.class);
        String succCode = "00";
        String mess = null == responseDTO ? null : responseDTO.getMess();
        if (null == responseDTO || !succCode.equals(responseDTO.getType())) {
            if (StringUtils.isBlank(mess)) {
                mess = "抽取失败，请联系管理员！";
            }
            JsfUtil.addErrorMessage(mess);
            return;
        }
        JsfUtil.addSuccessMessage(mess);
        List<String> errMsgList = responseDTO.getErrMsgList();
        if (CollectionUtils.isEmpty(errMsgList)) {
            return;
        }
        for (String err : errMsgList) {
            JsfUtil.addErrorMessage(err);
        }
    }

    /**
     * <p>方法描述：组装请求对象 </p>
     * pw 2024/7/4
     **/
    private RptExtractRequestJson generateRequestObj() {
        List<Integer> orgIdList = new ArrayList<>();
        String[] arr = this.extractUnitId.split(",");
        for (String str : arr) {
            if (StringUtils.isBlank(str)) {
                continue;
            }
            Integer orgId = Integer.parseInt(str);
            if (!orgIdList.contains(orgId)) {
                orgIdList.add(orgId);
            }
        }
        RptExtractRequestJson requestObj = new RptExtractRequestJson();
        requestObj.setOrgIdList(orgIdList);
        requestObj.setUserId(Global.getUser().getRid());
        requestObj.setExtractTotalList(this.generateExtractTotalList());
        requestObj.setRuleNumList(this.generateExtractRuleList());
        requestObj.setBhkStartDate(this.bhkDateStr);
        requestObj.setBhkEndDate(this.bhkDateEnd);
        requestObj.setRptStartDate(this.rptDateStr);
        requestObj.setRptEndDate(this.rptDateEnd);
        return requestObj;
    }

    /**
     * <p>方法描述： 抽取总数 </p>
     * pw 2024/7/4
     **/
    private List<RptExtractKeyMapParamJson> generateExtractTotalList() {
        if (CollectionUtils.isEmpty(this.extractTypeList)) {
            return null;
        }
        List<RptExtractKeyMapParamJson> extractTotalList = new ArrayList<>();
        for (Object[] arr : this.extractTypeList) {
            String str = null == arr[0] ? null : arr[0].toString();
            if (StringUtils.isBlank(str)) {
                continue;
            }
            Integer key = Integer.parseInt(str);
            str = null == arr[2] ? null : arr[2].toString();
            if (StringUtils.isBlank(str)) {
                continue;
            }
            Integer num = Integer.parseInt(str);
            if (0 == num) {
                continue;
            }
            RptExtractKeyMapParamJson json = new RptExtractKeyMapParamJson();
            json.setKey(key);
            json.setValue(num);
            extractTotalList.add(json);
        }
        return extractTotalList;
    }

    /**
     * <p>方法描述：抽取份数 </p>
     * pw 2024/7/4
     **/
    private List<RptExtractKeyMapParamJson> generateExtractRuleList() {
        if (CollectionUtils.isEmpty(this.extractRuleList)) {
            return null;
        }
        List<RptExtractKeyMapParamJson> ruleNumList = new ArrayList<>();
        for (TdZkExtractRule r : this.extractRuleList) {
            // 去除不可编辑的
            if (null == r.getIfEdit() || !r.getIfEdit() || null == r.getSampleNumber()) {
                continue;
            }
            RptExtractKeyMapParamJson json = new RptExtractKeyMapParamJson();
            json.setKey(r.getRid());
            json.setValue(r.getSampleNumber());
            ruleNumList.add(json);
        }
        return ruleNumList;
    }

    /**
     * <p>方法描述：请求接口相关参数初始化 </p>
     * pw 2024/7/4
     **/
    private void initInterfaceParams() {
        String debug = PropertyUtils.getValue("encrypt.debug");
        this.ifDebug = "true".equals(debug);
        this.encryptKey = PropertyUtils.getValue("encrypt.key");
        this.delUrl = PropertyUtils.getValue("delUrl");
    }

    /**
     * <p>方法描述：specItemCodeList填充 </p>
     * pw 2024/7/4
     **/
    private void fillSpecItemCodeList(String ext3) {
        if (StringUtils.isBlank(ext3)) {
            return;
        }
        ext3 = ext3.replace("；", ";");
        String[] arr = ext3.split(";");
        for (String str : arr) {
            if (StringUtils.isBlank(str) || !str.contains("@")) {
                continue;
            }
            String result = str.substring(0, str.indexOf("@")).trim();
            if (StringUtils.isNotBlank(result) && !this.specItemCodeList.contains(result)) {
                this.specItemCodeList.add(result);
            }
        }
    }

    /***************************抽取页面**********************************/


    @Override
    public void saveAction() {

    }


    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneGb() {
        return searchZoneGb;
    }

    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public String getSearchUnitId() {
        return searchUnitId;
    }

    public void setSearchUnitId(String searchUnitId) {
        this.searchUnitId = searchUnitId;
    }

    public Date getSearchSDate() {
        return searchSDate;
    }

    public void setSearchSDate(Date searchSDate) {
        this.searchSDate = searchSDate;
    }

    public Date getSearchEDate() {
        return searchEDate;
    }

    public void setSearchEDate(Date searchEDate) {
        this.searchEDate = searchEDate;
    }

    public List<TsSimpleCode> getSearchBhkrstList() {
        return searchBhkrstList;
    }

    public void setSearchBhkrstList(List<TsSimpleCode> searchBhkrstList) {
        this.searchBhkrstList = searchBhkrstList;
    }

    public String getSearchBhkrstName() {
        return searchBhkrstName;
    }

    public void setSearchBhkrstName(String searchBhkrstName) {
        this.searchBhkrstName = searchBhkrstName;
    }

    public String getSearchSelBhkrstIds() {
        return searchSelBhkrstIds;
    }

    public void setSearchSelBhkrstIds(String searchSelBhkrstIds) {
        this.searchSelBhkrstIds = searchSelBhkrstIds;
    }

    public String[] getSearchBhkType() {
        return searchBhkType;
    }

    public void setSearchBhkType(String[] searchBhkType) {
        this.searchBhkType = searchBhkType;
    }

    public List<TsSimpleCode> getBhkTypeList() {
        return bhkTypeList;
    }

    public void setBhkTypeList(List<TsSimpleCode> bhkTypeList) {
        this.bhkTypeList = bhkTypeList;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdTjBhkInfoBean getTjBhkInfoBean() {
        return tjBhkInfoBean;
    }

    public void setTjBhkInfoBean(TdTjBhkInfoBean tjBhkInfoBean) {
        this.tjBhkInfoBean = tjBhkInfoBean;
    }


    public Object[] getExtractBhk() {
        return extractBhk;
    }

    public void setExtractBhk(Object[] extractBhk) {
        this.extractBhk = extractBhk;
    }

    public String getExtractZoneGb() {
        return extractZoneGb;
    }

    public void setExtractZoneGb(String extractZoneGb) {
        this.extractZoneGb = extractZoneGb;
    }

    public String getExtractZoneName() {
        return extractZoneName;
    }

    public void setExtractZoneName(String extractZoneName) {
        this.extractZoneName = extractZoneName;
    }

    public String getExtractUnitName() {
        return extractUnitName;
    }

    public void setExtractUnitName(String extractUnitName) {
        this.extractUnitName = extractUnitName;
    }

    public String getExtractUnitId() {
        return extractUnitId;
    }

    public void setExtractUnitId(String extractUnitId) {
        this.extractUnitId = extractUnitId;
    }

    public List<Object[]> getExtractTypeList() {
        return extractTypeList;
    }

    public void setExtractTypeList(List<Object[]> extractTypeList) {
        this.extractTypeList = extractTypeList;
    }

    public List<TdZkExtractRule> getExtractRuleList() {
        return extractRuleList;
    }

    public void setExtractRuleList(List<TdZkExtractRule> extractRuleList) {
        this.extractRuleList = extractRuleList;
    }

    public TdZkExtractRule getExtractRule() {
        return extractRule;
    }

    public void setExtractRule(TdZkExtractRule extractRule) {
        this.extractRule = extractRule;
    }

    public List<SelectItem> getRabatList() {
        return rabatList;
    }

    public void setRabatList(List<SelectItem> rabatList) {
        this.rabatList = rabatList;
    }

    public List<TsSimpleCode> getHearingSimpleList() {
        return hearingSimpleList;
    }

    public void setHearingSimpleList(List<TsSimpleCode> hearingSimpleList) {
        this.hearingSimpleList = hearingSimpleList;
    }

    public List<SelectItem> getWarningList() {
        return warningList;
    }

    public void setWarningList(List<SelectItem> warningList) {
        this.warningList = warningList;
    }

    public List<TsSimpleCode> getOnDutyStatusList() {
        return onDutyStatusList;
    }

    public void setOnDutyStatusList(List<TsSimpleCode> onDutyStatusList) {
        this.onDutyStatusList = onDutyStatusList;
    }

    public String getRedStar() {
        return redStar;
    }

    public void setRedStar(String redStar) {
        this.redStar = redStar;
    }

    public Integer getExtractId() {
        return extractId;
    }

    public void setExtractId(Integer extractId) {
        this.extractId = extractId;
    }

    public Date getBhkDateStr() {
        return bhkDateStr;
    }

    public void setBhkDateStr(Date bhkDateStr) {
        this.bhkDateStr = bhkDateStr;
    }

    public Date getBhkDateEnd() {
        return bhkDateEnd;
    }

    public void setBhkDateEnd(Date bhkDateEnd) {
        this.bhkDateEnd = bhkDateEnd;
    }

    public Date getRptDateStr() {
        return rptDateStr;
    }

    public void setRptDateStr(Date rptDateStr) {
        this.rptDateStr = rptDateStr;
    }

    public Date getRptDateEnd() {
        return rptDateEnd;
    }

    public void setRptDateEnd(Date rptDateEnd) {
        this.rptDateEnd = rptDateEnd;
    }
}
