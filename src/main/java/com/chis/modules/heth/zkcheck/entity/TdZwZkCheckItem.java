package com.chis.modules.heth.zkcheck.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.chis.modules.system.entity.TsSimpleCode;
import org.springframework.util.CollectionUtils;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-6-26
 */
@Entity
@Table(name = "TD_ZW_ZK_CHECK_ITEM")
@SequenceGenerator(name = "TdZwZkCheckItem", sequenceName = "TD_ZW_ZK_CHECK_ITEM_SEQ", allocationSize = 1)
public class TdZwZkCheckItem implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwZkCheckTable fkByMainId;
	private TsSimpleCode fkByItemId;
	private BigDecimal checkVal;
	private BigDecimal scoreVal;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	/****************临时字段*****************/
	/**扣分原因*/
	private String deductRsn;
	/**扣分原因特殊处理支持换行*/
	private String deductRsn2;
	/** 质控考核结果子表集合 */
	private List<TdZwZkCheckSub> checkSubList;
	/** 临时字段 是否有评分项 */
	private Boolean ifHasScore;
	
	public TdZwZkCheckItem() {
	}

	public TdZwZkCheckItem(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwZkCheckItem")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwZkCheckTable getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwZkCheckTable fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_ID")			
	public TsSimpleCode getFkByItemId() {
		return fkByItemId;
	}

	public void setFkByItemId(TsSimpleCode fkByItemId) {
		this.fkByItemId = fkByItemId;
	}	
			
	@Column(name = "CHECK_VAL")	
	public BigDecimal getCheckVal() {
		return checkVal;
	}

	public void setCheckVal(BigDecimal checkVal) {
		this.checkVal = checkVal;
	}	
			
	@Column(name = "SCORE_VAL")	
	public BigDecimal getScoreVal() {
		return scoreVal;
	}

	public void setScoreVal(BigDecimal scoreVal) {
		this.scoreVal = scoreVal;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	@Transient
	public String getDeductRsn() {
		return deductRsn;
	}

	public void setDeductRsn(String deductRsn) {
		this.deductRsn = deductRsn;
	}
	@Transient
	public String getDeductRsn2() {
		return deductRsn2;
	}

	public void setDeductRsn2(String deductRsn2) {
		this.deductRsn2 = deductRsn2;
	}

	@Transient
	public List<TdZwZkCheckSub> getCheckSubList() {
		return checkSubList;
	}

	public void setCheckSubList(List<TdZwZkCheckSub> checkSubList) {
		this.checkSubList = checkSubList;
	}

	@Transient
	public Boolean getIfHasScore() {
		if(!CollectionUtils.isEmpty(this.checkSubList)){
			for(TdZwZkCheckSub checkSub : this.checkSubList){
				//checkSub.fkByScoreId.fkByItemTypeId.extendS1 == 3
				String extends1 = null != checkSub.getFkByScoreId() && null != checkSub.getFkByScoreId().getFkByItemTypeId() ?
						checkSub.getFkByScoreId().getFkByItemTypeId().getExtendS1() : null;
				if(!"3".equals(extends1)){
					return Boolean.TRUE;
				}

			}
		}
		return Boolean.FALSE;
	}

	public void setIfHasScore(Boolean ifHasScore) {
		this.ifHasScore = ifHasScore;
	}
}