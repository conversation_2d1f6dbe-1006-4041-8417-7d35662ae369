package com.chis.modules.heth.zkcheck.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-8-20
 */
@Entity
@Table(name = "TD_ZW_CHECK_TECH_PSN")
@SequenceGenerator(name = "TdZwCheckTechPsn", sequenceName = "TD_ZW_CHECK_TECH_PSN_SEQ", allocationSize = 1)
public class TdZwCheckTechPsn implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwCheckTechMain fkByMainId;
	private String psnName;
	private TsSimpleCode fkByTitleId;
	private TsSimpleCode fkByPsnTypeId;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	private Integer psnTypeId;
	private Integer titleId;
	
	public TdZwCheckTechPsn() {
	}

	public TdZwCheckTechPsn(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwCheckTechPsn")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwCheckTechMain getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwCheckTechMain fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "PSN_NAME")	
	public String getPsnName() {
		return psnName;
	}

	public void setPsnName(String psnName) {
		this.psnName = psnName;
	}	
			
	@ManyToOne
	@JoinColumn(name = "TITLE_ID")			
	public TsSimpleCode getFkByTitleId() {
		return fkByTitleId;
	}

	public void setFkByTitleId(TsSimpleCode fkByTitleId) {
		this.fkByTitleId = fkByTitleId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "PSN_TYPE_ID")			
	public TsSimpleCode getFkByPsnTypeId() {
		return fkByPsnTypeId;
	}

	public void setFkByPsnTypeId(TsSimpleCode fkByPsnTypeId) {
		this.fkByPsnTypeId = fkByPsnTypeId;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Transient
	public Integer getPsnTypeId() {
		return psnTypeId;
	}

	public void setPsnTypeId(Integer psnTypeId) {
		this.psnTypeId = psnTypeId;
	}

	@Transient
	public Integer getTitleId() {
		return titleId;
	}

	public void setTitleId(Integer titleId) {
		this.titleId = titleId;
	}
}