package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesSimpleBean;
import org.primefaces.context.RequestContext;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  <p>类描述：检测报告名称（编号）选择</p>
 * @ClassAuthor hsj 2023-03-30 8:54
 */
@ManagedBean(name = "SelectRptNoListBean")
@ViewScoped
public class SelectRptNoListBean extends FacesSimpleBean {

    private static final long serialVersionUID = -738445454536625063L;

    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    /** 检测报告名称（编号）*/
    private String searchRptNo;
	/**选中机构*/
    private String orgId;
	/**已被关联但又取消还未保存时*/
    private String editRptNoRid;
	/**已选择*/
	private Object[] selectPro;

    public SelectRptNoListBean() {
    	this.ifSQL = true;
		this.orgId = JsfUtil.getRequest().getParameter("orgId");
		this.editRptNoRid = JsfUtil.getRequest().getParameter("editRptNoRid");
		this.searchAction();
    }
	/**
	 *  <p>方法描述：查询</p>
	 * @MethodAuthor hsj 2023-03-30 9:14
	 */
    @Override
	public String[] buildHqls() {
    	StringBuffer sql = new StringBuffer();
		sql.append(" SELECT T.RID ,T.RPT_NO,CASE WHEN tz.ZONE_TYPE >2 THEN SUBSTR(tz.FULL_NAME, INSTR(tz.FULL_NAME,'_')+1) ELSE tz.FULL_NAME END ZONE_NAME,");
		sql.append(" T.CRPT_NAME, T.RPT_DATE, T.STATE FROM  TD_ZW_CHECK_RPT T ");
		sql.append(" LEFT JOIN TS_ZONE tz ON T.ZONE_ID = tz.RID ");
		sql.append(" WHERE T.STATE  IN (1,2)");
		if(StringUtils.isNotBlank(this.orgId)){
			sql.append(" AND  T.UNIT_ID = ").append(this.orgId);
		}
		if (StringUtils.isNotBlank(searchRptNo)) {
			sql.append(" AND T.RPT_NO LIKE :searchRptNo escape '\\\'");
			this.paramMap.put("searchRptNo", "%"+StringUtils.convertBFH(this.searchRptNo.trim())+"%");
		}
		sql.append(" AND NOT EXISTS (SELECT 1 FROM TD_ZW_ZK_CHECK_MAIN TT WHERE TT.JC_RPT_ID = T.RID AND TT.DEL_MARK = 0 ");
		if(StringUtils.isNotBlank(editRptNoRid)){
			sql.append(" AND TT.JC_RPT_ID != ").append(editRptNoRid);
		}
		sql.append(")");
		sql.append(" ORDER BY T.RPT_NO DESC");
		String h2 = "SELECT COUNT(*) FROM (" + sql.toString() + ")";
        String h1 = "SELECT * FROM (" + sql.toString() + ")";
        return new String[]{h1,h2};
	}
   /**
    *  <p>方法描述：提交</p>
    * @MethodAuthor hsj 2023-03-30 9:14
    */
    public void submitAction() {
    	if (selectPro == null) {
			JsfUtil.addErrorMessage("请选择检测报告名称（编号）！");
			return;
		}
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("selected", selectPro);
		RequestContext.getCurrentInstance().closeDialog(map);
	}
   /**
    *  <p>方法描述：关闭</p>
    * @MethodAuthor hsj 2023-03-30 9:14
    */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

	public String getSearchRptNo() {
		return searchRptNo;
	}

	public void setSearchRptNo(String searchRptNo) {
		this.searchRptNo = searchRptNo;
	}


	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public Object[] getSelectPro() {
		return selectPro;
	}

	public void setSelectPro(Object[] selectPro) {
		this.selectPro = selectPro;
	}
}
