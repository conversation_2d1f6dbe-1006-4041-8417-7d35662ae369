package com.chis.modules.heth.zkcheck.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 用人单位现场核查-存在问题选择弹出框用
 */
public class CheckDeductSelVO implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer scoreId;
    private String searchName;
    private String selName;
    private List<Object[]> allList;
    private List<Object[]> showList;
    private List<Integer> ridList;

    public CheckDeductSelVO() {
        this.allList = new ArrayList<>();
        init();
    }

    public void init() {
        this.searchName = "";
        this.selName = "";
        this.showList = new ArrayList<>();
        this.ridList = new ArrayList<>();
        for (Object[] objects : this.allList) {
            objects[3] = false;
        }
    }

    public Integer getScoreId() {
        return scoreId;
    }

    public void setScoreId(Integer scoreId) {
        this.scoreId = scoreId;
    }

    public String getSearchName() {
        return searchName;
    }

    public void setSearchName(String searchName) {
        this.searchName = searchName;
    }

    public String getSelName() {
        return selName;
    }

    public void setSelName(String selName) {
        this.selName = selName;
    }

    public List<Object[]> getAllList() {
        return allList;
    }

    public void setAllList(List<Object[]> allList) {
        this.allList = allList;
    }

    public List<Object[]> getShowList() {
        return showList;
    }

    public void setShowList(List<Object[]> showList) {
        this.showList = showList;
    }

    public List<Integer> getRidList() {
        return ridList;
    }

    public void setRidList(List<Integer> ridList) {
        this.ridList = ridList;
    }

}
