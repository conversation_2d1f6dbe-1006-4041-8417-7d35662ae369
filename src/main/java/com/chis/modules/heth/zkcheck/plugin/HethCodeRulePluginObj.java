package com.chis.modules.heth.zkcheck.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsCodeRule;
import com.chis.modules.system.enumn.SystemType;

/**
 * 职卫平台自动编号规则的插件
 * <AUTHOR>
 * @createDate 2014年8月29日 上午11:06:07
 * @LastModify LuXuekun
 * @ModifyDate 2014年8月29日 上午11:06:07
 */
public class HethCodeRulePluginObj {

	public static Set<TsCodeRule> ruleSet;

	/**
	 * 初始化
	 */
	static {
		ruleSet = new HashSet<TsCodeRule>();
		ruleSet.add(new TsCodeRule("HETH_ZK_CHECK_RST_CODE", SystemType.ZK_CHECK,"质控考核结果表编号",null,"",null,(short)3,"com.chis.ejb.service.autocode.impl.CodeLiquidServiceImpl", new Date(), 1));
	}

    public Set<TsCodeRule> getRuleSet() {
        return ruleSet;
    }
}
