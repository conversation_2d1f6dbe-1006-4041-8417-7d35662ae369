package com.chis.modules.heth.zkcheck.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUserInfo;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2024-7-2
 */
@Entity
@Table(name = "TD_ZK_EXTRACT_RULE")
@SequenceGenerator(name = "TdZkExtractRule", sequenceName = "TD_ZK_EXTRACT_RULE_SEQ", allocationSize = 1)
public class TdZkExtractRule implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TsSimpleCode fkByExtractTypeId;
    private Date bhkDateStr;
    private Date bhkDateEnd;
    private Date rptDateStr;
    private Date rptDateEnd;
    private TsSimpleCode fkByHearingRstId;
    private Integer ifWarn;
    private Integer priority;
    private TsUserInfo fkByUserId;
    private Integer createManid;
    private Date createDate;
    private Date modifyDate;
    private Integer modifyManid;

    private List<TdZkOnguardState> onguardStateList;
    private List<TdZkBadrsns> badrsnsList;
    private List<TdZkBadrsnsRst> badrsnsRstList;
    private List<TdZkChestResult> chestResultList;
    private String onguardStateName;
    private String onguardStateIds;
    private String badrsnsName;
    private String badrsnsIds;
    private String badrsnsRstName;
    private String badrsnsRstIds;
    private String[] chestResultIds;
    private String chestResultName;
    private Integer sampleNumber;
    private Integer hearingRstId;
    private String hearingRstName;

    private Integer extractTypeId;
    private Boolean ifEdit;

    public TdZkExtractRule() {
    }

    public TdZkExtractRule(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZkExtractRule")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "EXTRACT_TYPE_ID")
    public TsSimpleCode getFkByExtractTypeId() {
        return fkByExtractTypeId;
    }

    public void setFkByExtractTypeId(TsSimpleCode fkByExtractTypeId) {
        this.fkByExtractTypeId = fkByExtractTypeId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BHK_DATE_STR")
    public Date getBhkDateStr() {
        return bhkDateStr;
    }

    public void setBhkDateStr(Date bhkDateStr) {
        this.bhkDateStr = bhkDateStr;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BHK_DATE_END")
    public Date getBhkDateEnd() {
        return bhkDateEnd;
    }

    public void setBhkDateEnd(Date bhkDateEnd) {
        this.bhkDateEnd = bhkDateEnd;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "RPT_DATE_STR")
    public Date getRptDateStr() {
        return rptDateStr;
    }

    public void setRptDateStr(Date rptDateStr) {
        this.rptDateStr = rptDateStr;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "RPT_DATE_END")
    public Date getRptDateEnd() {
        return rptDateEnd;
    }

    public void setRptDateEnd(Date rptDateEnd) {
        this.rptDateEnd = rptDateEnd;
    }

    @ManyToOne
    @JoinColumn(name = "HEARING_RST_ID")
    public TsSimpleCode getFkByHearingRstId() {
        return fkByHearingRstId;
    }

    public void setFkByHearingRstId(TsSimpleCode fkByHearingRstId) {
        this.fkByHearingRstId = fkByHearingRstId;
    }

    @Column(name = "IF_WARN")
    public Integer getIfWarn() {
        return ifWarn;
    }

    public void setIfWarn(Integer ifWarn) {
        this.ifWarn = ifWarn;
    }

    @Column(name = "PRIORITY")
    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    @ManyToOne
    @JoinColumn(name = "USER_ID")
    public TsUserInfo getFkByUserId() {
        return fkByUserId;
    }

    public void setFkByUserId(TsUserInfo fkByUserId) {
        this.fkByUserId = fkByUserId;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId")
    public List<TdZkOnguardState> getOnguardStateList() {
        return onguardStateList;
    }

    public void setOnguardStateList(List<TdZkOnguardState> onguardStateList) {
        this.onguardStateList = onguardStateList;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId")
    public List<TdZkBadrsns> getBadrsnsList() {
        return badrsnsList;
    }

    public void setBadrsnsList(List<TdZkBadrsns> badrsnsList) {
        this.badrsnsList = badrsnsList;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId")
    public List<TdZkBadrsnsRst> getBadrsnsRstList() {
        return badrsnsRstList;
    }

    public void setBadrsnsRstList(List<TdZkBadrsnsRst> badrsnsRstList) {
        this.badrsnsRstList = badrsnsRstList;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId")
    public List<TdZkChestResult> getChestResultList() {
        return chestResultList;
    }

    public void setChestResultList(List<TdZkChestResult> chestResultList) {
        this.chestResultList = chestResultList;
    }

    @Transient
    public String getOnguardStateName() {
        return onguardStateName;
    }

    public void setOnguardStateName(String onguardStateName) {
        this.onguardStateName = onguardStateName;
    }

    @Transient
    public String getBadrsnsName() {
        return badrsnsName;
    }

    public void setBadrsnsName(String badrsnsName) {
        this.badrsnsName = badrsnsName;
    }

    @Transient
    public String getBadrsnsRstName() {
        return badrsnsRstName;
    }


    public void setBadrsnsRstName(String badrsnsRstName) {
        this.badrsnsRstName = badrsnsRstName;
    }


    @Transient
    public Integer getSampleNumber() {
        return sampleNumber;
    }

    public void setSampleNumber(Integer sampleNumber) {
        this.sampleNumber = sampleNumber;
    }

    @Transient
    public String getOnguardStateIds() {
        return onguardStateIds;
    }

    public void setOnguardStateIds(String onguardStateIds) {
        this.onguardStateIds = onguardStateIds;
    }

    @Transient
    public String getBadrsnsIds() {
        return badrsnsIds;
    }

    public void setBadrsnsIds(String badrsnsIds) {
        this.badrsnsIds = badrsnsIds;
    }

    @Transient
    public String getBadrsnsRstIds() {
        return badrsnsRstIds;
    }

    public void setBadrsnsRstIds(String badrsnsRstIds) {
        this.badrsnsRstIds = badrsnsRstIds;
    }

    @Transient
    public String[] getChestResultIds() {
        return chestResultIds;
    }

    public void setChestResultIds(String[] chestResultIds) {
        this.chestResultIds = chestResultIds;
    }

    @Transient
    public Integer getHearingRstId() {
        return hearingRstId;
    }

    public void setHearingRstId(Integer hearingRstId) {
        this.hearingRstId = hearingRstId;
    }

    @Transient
    public Integer getExtractTypeId() {
        return extractTypeId;
    }

    public void setExtractTypeId(Integer extractTypeId) {
        this.extractTypeId = extractTypeId;
    }

    @Transient
    public String getHearingRstName() {
        return hearingRstName;
    }

    public void setHearingRstName(String hearingRstName) {
        this.hearingRstName = hearingRstName;
    }

    @Transient
    public String getChestResultName() {
        return chestResultName;
    }

    public void setChestResultName(String chestResultName) {
        this.chestResultName = chestResultName;
    }

    @Transient
    public Boolean getIfEdit() {
        return ifEdit;
    }

    public void setIfEdit(Boolean ifEdit) {
        this.ifEdit = ifEdit;
    }
}