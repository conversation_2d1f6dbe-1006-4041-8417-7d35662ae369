package com.chis.modules.heth.zkcheck.entity;

import javax.persistence.*;
import java.util.Date;
import com.chis.modules.system.entity.TsUnit;

/**
 * 
 * <AUTHOR>
 * @createTime 2024-11-1
 */
@Entity
@Table(name = "TD_ZK_SRVORG_PLAN")
@SequenceGenerator(name = "TdZkSrvorgPlan", sequenceName = "TD_ZK_SRVORG_PLAN_SEQ", allocationSize = 1)
public class TdZkSrvorgPlan implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String planName;
	private Date planDate;
	private String rmk;
	private String filePath;
	private Integer state;
	private TsUnit fkByPubishUnitId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZkSrvorgPlan() {
	}

	public TdZkSrvorgPlan(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZkSrvorgPlan")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "PLAN_NAME")	
	public String getPlanName() {
		return planName;
	}

	public void setPlanName(String planName) {
		this.planName = planName;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "PLAN_DATE")			
	public Date getPlanDate() {
		return planDate;
	}

	public void setPlanDate(Date planDate) {
		this.planDate = planDate;
	}	
			
	@Column(name = "RMK")	
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}	
			
	@Column(name = "FILE_PATH")	
	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@ManyToOne
	@JoinColumn(name = "PUBISH_UNIT_ID")			
	public TsUnit getFkByPubishUnitId() {
		return fkByPubishUnitId;
	}

	public void setFkByPubishUnitId(TsUnit fkByPubishUnitId) {
		this.fkByPubishUnitId = fkByPubishUnitId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}