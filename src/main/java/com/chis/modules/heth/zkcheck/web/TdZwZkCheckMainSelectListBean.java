package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.*;
import com.chis.modules.system.utils.Global;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * <p>类描述：质控考核填报</p>
 * @ClassAuthor qrr,2021年6月26日,TdZwZkCheckMainListBean
 * */
@ManagedBean(name="tdZwZkCheckMainSelectListBean")
@ViewScoped
public class TdZwZkCheckMainSelectListBean  extends AbstractTdZwZkCheckMainListBean{
	private static final long serialVersionUID = 1L;

	@Override
	public void initData() {
		this.ifSQL = true;
		super.initScoreRstInfo();
		initParam();
		this.searchAction();
	}

	@Override
	public Integer pageType() {
		return 2;
	}

	@Override
	public void searchAction() {
		if (null != searchCheckStartTime && null != searchCheckEndTime) {
			if (searchCheckEndTime.before(searchCheckStartTime)) {
				JsfUtil.addErrorMessage("考核开始日期应小于等于结束日期！");
				return;
			}
		}
		super.searchAction();
	}


	private void initParam() {
		 super.initParamItemMap();
		searchNeedImproveStateList = new ArrayList<>();
		searchFinishImproveStateList = new ArrayList<>();
		searchCheckStartTime = DateUtils.getYearFirstDay(new Date());
		searchCheckEndTime = new Date();
	}

	@Override
	public String[] buildHqls() {
		StringBuffer buffer = new StringBuffer();
		buffer.append(" FROM TD_ZW_ZK_CHECK_MAIN T ")
				.append(" LEFT JOIN TS_UNIT T1 ON T.RECORD_ORG_ID = T1.RID ")
				.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID ")
				.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.CHECK_TYPE_ID = T3.RID ")
				.append(" WHERE T.DEL_MARK = 0 AND T.STATE_MARK = 1 ")
				.append(" AND T.ORG_ID = ").append(Global.getUser().getTsUnit().getRid());

		if(null != searchCheckStartTime){
			buffer.append(" AND T.CHECK_DATE>= TO_DATE('").append(DateUtils.formatDate(this.searchCheckStartTime))
					.append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
		}
		if(null != searchCheckEndTime){
			buffer.append(" AND T.CHECK_DATE<= TO_DATE('").append(DateUtils.formatDate(this.searchCheckEndTime))
					.append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
		}
		// 是否需要整改
		if (!CollectionUtils.isEmpty(this.searchNeedImproveStateList)) {
			buffer.append(" AND T.IF_NEED_IMPROVE IN(").append(StringUtils.list2string(this.searchNeedImproveStateList, ",")).append(") ");
		}
		// 整改状态
		if (!CollectionUtils.isEmpty(this.searchFinishImproveStateList)) {
			buffer.append(" AND T.IF_IMPROVE_END IN(").append(StringUtils.list2string(this.searchFinishImproveStateList, ",")).append(") ");
		}
		String searchSql = " SELECT T.RID AS A0,T1.UNITNAME AS A1,T3.CODE_NAME AS A2,T.CHECK_DATE AS A3,T.IF_DEVELOP AS A4,T.TOTAL_SCORE_VAL AS A5,T.REVIEW_CONCLUSION AS A6,T.IF_NEED_IMPROVE AS A7,T.IF_IMPROVE_END AS A8 ";
		searchSql = searchSql + buffer + " ORDER BY T.CHECK_DATE ";
		String countSql = " SELECT COUNT(*) " + buffer;
		return new String[]{searchSql, countSql};
	}

	@Override
	public void processData(List<?> list) {

	}

	public List<Integer> getSearchNeedImproveStateList() {
		return searchNeedImproveStateList;
	}

	public void setSearchNeedImproveStateList(List<Integer> searchNeedImproveStateList) {
		this.searchNeedImproveStateList = searchNeedImproveStateList;
	}

	public List<Integer> getSearchFinishImproveStateList() {
		return searchFinishImproveStateList;
	}

	public void setSearchFinishImproveStateList(List<Integer> searchFinishImproveStateList) {
		this.searchFinishImproveStateList = searchFinishImproveStateList;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}
}
