package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zkcheck.entity.TbZwZkBadrsnStand;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.utils.Global;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * <p>类描述：质控考核填报</p>
 *
 * @ClassAuthor qrr, 2021年6月26日, TdZwZkCheckMainListBean
 */
@ManagedBean(name = "tdZwZkCheckMainListBean")
@ViewScoped
public class TdZwZkCheckMainListBean extends AbstractTdZwZkCheckMainListBean {

    /**
     * 查询条件 - 被考核机构
     * */
    private String searchOrgName;
    /**
     * 查询条件 - 考核类型
     */
    private Integer queryCheckType;
    /**
     * 查询条件 - 本年度是否开展工作
     * */
    private List<Integer> ifDevelopList;
    /**
     * 查询条件 - 状态
     * */
    private List<Integer> searchStateMarkList;
    /**
     * 考核类型
     */
    private List<TsSimpleCode> queryCheckTypeList;

    @Override
    public void initData() {
        this.ifRemoveCheckResults = "1".equals(PropertyUtils.getValueWithoutException("ifRemoveCheckResults"));
        this.ifRemoveCheckResults2 = "2".equals(PropertyUtils.getValueWithoutException("ifRemoveCheckResults"));
        this.showCancel = "1".equals(PropertyUtils.getValueWithoutException("ifShowCancelBtn"));
        this.ifSQL = true;
        super.initScoreRstInfo();
        initParam();
        initCheckType();
        this.searchAction();
    }

    @Override
    public Integer pageType() {
        return 1;
    }

    @Override
    public String[] buildHqls() {
        StringBuffer buffer = new StringBuffer();
        buffer.append(" FROM TD_ZW_ZK_CHECK_MAIN T ");
        buffer.append(" LEFT JOIN TS_UNIT T1 ON T.ORG_ID = T1.RID ");
        buffer.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID ");
        buffer.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T.CHECK_TYPE_ID = T4.RID ");
        if (this.jurisdiction) {
            buffer.append(" LEFT JOIN TS_USER_INFO T5 ON T.CREATE_MANID = T5.RID ");
            buffer.append(" LEFT JOIN TS_UNIT T6 ON T5.UNIT_RID = T6.RID ");
            buffer.append(" LEFT JOIN TS_ZONE T7 ON T6.ZONE_ID = T7.RID ");
        }
        buffer.append(" WHERE T.DEL_MARK = 0 AND T.CHECK_TYPE_ID IS NOT NULL  ");

        if (null != this.queryCheckType) {
            buffer.append(" AND T4.RID = :queryCheckType ");
            this.paramMap.put("queryCheckType", this.queryCheckType);
        }

        if (this.jurisdiction) {
            //当前登录人有“按管辖范围”权限：查询创建人所在机构的所属地区在当前机构管辖范围下的数据
            TsUnit unit = Global.getUser().getTsUnit();
            if (ObjectUtil.isEmpty(unit) ||
                    ObjectUtil.isEmpty(unit.getFkByManagedZoneId()) ||
                    ObjectUtil.isEmpty(unit.getFkByManagedZoneId().getZoneGb())) {
                buffer.append(" AND 1 = 2 ");
            } else {
                buffer.append(" AND T7.ZONE_GB LIKE :userZoneGb ");
                this.paramMap.put("userZoneGb", ZoneUtil.zoneSelect(unit.getFkByManagedZoneId().getZoneGb()) + "%");
            }
        } else {
            //当前登录人没有“按管辖范围”权限：只能查询创建人为当前登录人的数据
            buffer.append(" AND T.CREATE_MANID = :userId ");
            this.paramMap.put("userId", Global.getUser().getRid());
        }
        if (StringUtils.isNotBlank(searchZoneCode)) {
            buffer.append(" AND T2.ZONE_GB LIKE :zoneCode escape '\\\' ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        if (StringUtils.isNotBlank(searchOrgName)) {
            buffer.append(" AND T1.UNITNAME LIKE :orgName escape '\\\' ");
            this.paramMap.put("orgName", "%" + StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchOrgName).trim()) + "%");
        }
        if (null != searchCheckStartTime) {
            buffer.append(" AND T.CHECK_DATE>= TO_DATE('").append(DateUtils.formatDate(this.searchCheckStartTime))
                    .append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != searchCheckEndTime) {
            buffer.append(" AND T.CHECK_DATE<= TO_DATE('").append(DateUtils.formatDate(this.searchCheckEndTime))
                    .append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if (!CollectionUtils.isEmpty(searchStateMarkList)) {
            StringBuffer stateBuffer = new StringBuffer();
            for (Integer state : searchStateMarkList) {
                stateBuffer.append(",").append(state);
            }
            //避免以后不只是两种状态 用IN语句查询 若最终确认仅两种状态 这里可省略
            buffer.append(" AND T.STATE_MARK IN (").append(stateBuffer.substring(1)).append(") ");
        }
        // 本年度是否开展工作
        if (!CollectionUtils.isEmpty(this.ifDevelopList)) {
            buffer.append(" AND T.IF_DEVELOP IN(").append(StringUtils.list2string(this.ifDevelopList, ",")).append(") ");
        }
        // 是否需要整改
        if (!CollectionUtils.isEmpty(this.searchNeedImproveStateList)) {
            buffer.append(" AND T.IF_NEED_IMPROVE IN(").append(StringUtils.list2string(this.searchNeedImproveStateList, ",")).append(") ");
        }
        // 整改状态
        if (!CollectionUtils.isEmpty(this.searchFinishImproveStateList)) {
            buffer.append(" AND T.IF_IMPROVE_END IN(").append(StringUtils.list2string(this.searchFinishImproveStateList, ",")).append(") ");
        }
        String searchSql = " SELECT T.RID AS A0,T2.FULL_NAME AS A1,T1.UNITNAME AS A2,T4.CODE_NAME AS A3,T.CHECK_DATE AS A4,T.IF_DEVELOP AS A5,T.TOTAL_SCORE_VAL AS A6,T.REVIEW_CONCLUSION AS A7,T.STATE_MARK AS A8,T.IF_NEED_IMPROVE AS A9,T.IF_IMPROVE_END AS A10,T.RECORD_ORG_ID AS A11,0 AS A12  ";
        searchSql = searchSql + buffer + " ORDER BY T2.ZONE_GB,T.ORG_ID,T.CHECK_DATE desc ";
        String countSql = " SELECT COUNT(*) " + buffer;
        return new String[]{searchSql, countSql};
    }

    @Override
    public void processData(List<?> list) {
        if (!CollectionUtils.isEmpty(list)) {
            List<Object[]> result = (List<Object[]>) list;
            for (Object[] obj : result) {
                Integer unitCode = Global.getUser().getTsUnit().getRid();
                /**本单位为录入机构时，显示删除按钮*/
                if (null != obj[11] && unitCode.intValue() == Integer.valueOf(obj[11].toString())) {
                    obj[12] = 1;
                } else {
                    obj[12] = 0;
                }
                if (null != obj[1]) {
                    obj[1] = obj[1].toString().substring(obj[1].toString().indexOf("_") + 1);
                }
            }
        }
    }


    /**
     * <p>方法描述：初始化参数</p>
     *
     * @MethodAuthor qrr, 2021年6月26日, initParam
     */
    private void initParam() {
        super.initParamItemMap();
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.zoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), false, "", "");
        if (!CollectionUtils.isEmpty(zoneList)) {
            this.searchZoneCode = zoneList.get(0).getZoneGb();
            this.searchZoneName = zoneList.get(0).getZoneName();
        }

        searchNeedImproveStateList = new ArrayList<>();
        searchFinishImproveStateList = new ArrayList<>();
        searchStateMarkList = new ArrayList<>();
        searchStateMarkList.add(0);
        searchCheckStartTime = DateUtils.getYearFirstDay(new Date());
        searchCheckEndTime = new Date();

        //按钮权限 初始化
        Set<String> btnSet = Global.getBtnSet();
        //按管辖范围
        if (btnSet.contains("jurisdiction")) {
            this.jurisdiction = Boolean.TRUE;
        }

        /** 考核类型码表 */
        this.queryCheckTypeList = this.commService.findLevelSimpleCodesByTypeId("5554");
    }

    /**
     * <p>方法描述：初始化考核类型</p>
     *
     * @MethodAuthor qrr, 2021年6月28日, initCheckType
     */
    private void initCheckType() {
        this.checkTypeList = new ArrayList<>();
        List<TbZwZkBadrsnStand> list = standService.findTbZwZkBadrsnStand();
        if (!CollectionUtils.isEmpty(list)) {
            Map<Integer, TbZwZkBadrsnStand> map = new HashMap<>();
            for (TbZwZkBadrsnStand t : list) {
                boolean extendS1 = (null == t.getFkByCheckTypeId() || (t.getFkByCheckTypeId() != null && StringUtils.isBlank(t.getFkByCheckTypeId().getExtendS1())));
                if (extendS1) {
                    continue;
                }
                if (null == map.get(t.getFkByCheckTypeId().getRid())) {
                    this.checkTypeList.add(new SelectItem(t.getFkByCheckTypeId().getRid(), t.getFkByCheckTypeId().getCodeName()));
                    map.put(t.getFkByCheckTypeId().getRid(), t);
                }
            }
        }
    }

    public Integer getQueryCheckType() {
        return queryCheckType;
    }

    public void setQueryCheckType(Integer queryCheckType) {
        this.queryCheckType = queryCheckType;
    }

    public List<TsSimpleCode> getQueryCheckTypeList() {
        return queryCheckTypeList;
    }

    public void setQueryCheckTypeList(List<TsSimpleCode> queryCheckTypeList) {
        this.queryCheckTypeList = queryCheckTypeList;
    }

    public String getSearchOrgName() {
        return searchOrgName;
    }

    public void setSearchOrgName(String searchOrgName) {
        this.searchOrgName = searchOrgName;
    }

    public List<Integer> getIfDevelopList() {
        return ifDevelopList;
    }

    public void setIfDevelopList(List<Integer> ifDevelopList) {
        this.ifDevelopList = ifDevelopList;
    }

    public List<Integer> getSearchStateMarkList() {
        return searchStateMarkList;
    }

    public void setSearchStateMarkList(List<Integer> searchStateMarkList) {
        this.searchStateMarkList = searchStateMarkList;
    }
}

