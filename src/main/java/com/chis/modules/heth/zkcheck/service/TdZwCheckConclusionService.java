package com.chis.modules.heth.zkcheck.service;

import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckClsItem;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckConclusion;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckItemRst;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <p>类描述：质量控制考核结论录入Service </p>
 * @ClassAuthor： pw 2022/8/23
 **/
@Service
@Transactional(readOnly = false)
public class TdZwCheckConclusionService extends AbstractTemplate {
    
    /**
     * <p>方法描述：通过质控考核主表获取质控考核指标对应的考核项结果对应的数量 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    @Transactional(readOnly = true)
    public Map<String,Map<String,Integer>> findResultByCheckMainId(Integer checkMainId){
        if(null == checkMainId){
            return Collections.EMPTY_MAP;
        }
        //key 码表5530大类CODE_NO value Map key 码表5534CODE_NO value数量
        Map<String,Map<String,Integer>> resultMap = new HashMap<>();
        StringBuffer queryBuffer = new StringBuffer();
        //TD_ZW_ZK_CHECK_ITEM的ITEM_ID 就是存储的大类码表 sql查询不能出现同列名 否则会出现前边的列值覆盖后边的值
        queryBuffer.append(" SELECT T4.CODE_NO AS CODENO1, T5.CODE_NO AS CODENO2, COUNT(T3.RID) ")
                .append(" FROM TD_ZW_ZK_CHECK_MAIN T ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_TABLE T1 ON T.RID = T1.MAIN_ID ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_ITEM T2 ON T2.MAIN_ID = T1.RID ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_SUB T3 ON T3.MAIN_ID = T2.RID ")
                .append(" INNER JOIN TS_SIMPLE_CODE T4 ON T2.ITEM_ID=T4.RID ")
                .append(" INNER JOIN TS_SIMPLE_CODE T5 ON T3.SCORE_RST_ID=T5.RID ")
                .append(" WHERE T.RID =").append(checkMainId)
                .append(" GROUP BY T4.CODE_NO, T5.CODE_NO ");
        List<Object[]> resultList = this.findSqlResultList(queryBuffer.toString());
        if(CollectionUtils.isEmpty(resultList)){
            return resultMap;
        }
        for(Object[] objArr : resultList){
            String itmCodeNo = null == objArr[0] ? null : objArr[0].toString();
            String codeNo = null == objArr[1] ? null : objArr[1].toString();
            Integer count = null == objArr[2] ? null : Integer.parseInt(objArr[2].toString());
            if(StringUtils.isBlank(itmCodeNo) || StringUtils.isBlank(codeNo) || null == count){
                continue;
            }
            Map<String,Integer> tmpMap = resultMap.get(itmCodeNo);
            if(null == tmpMap){
                tmpMap = new HashMap<>();
            }
            Integer cacheCount = tmpMap.get(codeNo);
            if(null != cacheCount){
                count += cacheCount;
            }
            tmpMap.put(codeNo, count);
            resultMap.put(itmCodeNo, tmpMap);
        }
        return resultMap;
    }

    /**
     * <p>方法描述：通过rid获取质量控制考核结论 加载子表 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    @Transactional(readOnly = true)
    public TdZwCheckConclusion findTdZwCheckConclusionByRid(Integer rid){
        if(null == rid){
            return null;
        }
        TdZwCheckConclusion conclusion = this.find(TdZwCheckConclusion.class, rid);
        if(null == conclusion){
            return null;
        }
        //加载子表
        if(null != conclusion.getZwCheckItemRstList()){
            conclusion.getZwCheckItemRstList().size();
        }
        if(null != conclusion.getZwCheckClsItemList()){
            conclusion.getZwCheckClsItemList().size();
        }
        return conclusion;
    }

    /**
     * <p>方法描述：通过资质机构单位rid以及资质机构类型获取服务项目codeNo集合 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    @Transactional(readOnly = true)
    public List<String> findOrgServiceItemsByOrgUnitId(Integer unitId, String orgType){
        if(null == unitId || StringUtils.isBlank(orgType)){
            return Collections.EMPTY_LIST;
        }
        String orgTableName = "";
        String orgItemTableName = "";
        if("1".equals(orgType)){
            //职业健康检查机构
            orgTableName = " TD_ZW_TJORGINFO ";
            orgItemTableName = " TD_ZW_TJORGGITEMS ";
        }else if("2".equals(orgType)){
            //职业病诊断机构
            orgTableName = " TD_ZW_DIAGORGINFO ";
            orgItemTableName = " TD_ZW_DIAGITEMS ";
        }else if("3".equals(orgType)){
            //放射卫生技术服务机构
            orgTableName = " TD_ZW_SRVORGINFO ";
            orgItemTableName = " TD_ZW_SRVORGITEMS ";
        }else if("4".equals(orgType)){
            //职业卫生技术服务机构
            orgTableName = " TD_ZW_OCCHETH_INFO ";
            orgItemTableName = " TD_ZW_OCCHETH_ITEMS ";
        }
        List<String> resultList = new ArrayList<>();
        if(StringUtils.isBlank(orgTableName) || StringUtils.isBlank(orgItemTableName)){
            return resultList;
        }
        StringBuffer queryBuffer = new StringBuffer();
        queryBuffer.append(" SELECT DISTINCT T1.ITEM_CODE FROM ").append(orgTableName)
                .append(" T INNER JOIN ").append(orgItemTableName)
                .append(" T1 ON T1.ORG_ID=T.RID ").append(" WHERE T.ORG_ID=").append(unitId);
        List<Object> queryResultList = this.findSqlResultList(queryBuffer.toString());
        if(CollectionUtils.isEmpty(queryResultList)){
            return resultList;
        }
        for(Object obj : queryResultList){
            if(null != obj){
                resultList.add(obj.toString());
            }
        }
        return resultList;
    }

    /**
     * <p>方法描述：存儲质量控制考核结论相关数据 </p>
     * @MethodAuthor： pw 2022/8/23
     **/
    public void saveOrUpdateTdZwCheckConclusion(TdZwCheckConclusion checkConclusion){
        Integer rid = checkConclusion.getRid();
        if(null != rid){
            //删除质量考核项目结果以及考核结论表备案项目
            String deleteSql = " DELETE FROM TD_ZW_CHECK_ITEM_RST T WHERE T.MAIN_ID="+rid;
            this.executeSql(deleteSql, null);
            deleteSql = " DELETE FROM TD_ZW_CHECK_CLS_ITEM T WHERE T.MAIN_ID="+rid;
            this.executeSql(deleteSql, null);
        }
        List<TdZwCheckItemRst> zwCheckItemRstList = checkConclusion.getZwCheckItemRstList();
        List<TdZwCheckClsItem> zwCheckClsItemList = checkConclusion.getZwCheckClsItemList();
        if(!CollectionUtils.isEmpty(zwCheckItemRstList)){
            for(TdZwCheckItemRst itemRst : zwCheckItemRstList){
                if(null == itemRst.getRid()){
                    preInsert(itemRst);
                }else{
                    preUpdate(itemRst);
                }
            }
        }
        if(!CollectionUtils.isEmpty(zwCheckClsItemList)){
            for(TdZwCheckClsItem clsItem : zwCheckClsItemList){
                if(null == clsItem.getRid()){
                    preInsert(clsItem);
                }else{
                    preUpdate(clsItem);
                }
            }
        }
        this.upsertEntity(checkConclusion);
    }

    /**
     * <p>方法描述：质量控制考核结论撤销 </p>
     * @MethodAuthor： pw 2022/8/24
     **/
    public void cancelCheckConclusion(Integer rid){
        if(null == rid){
            return;
        }
        String deleteSql = " UPDATE TD_ZW_CHECK_CONCLUSION SET STATE=0 WHERE RID="+rid;
        this.executeSql(deleteSql, null);
    }

    /**
     * <p>方法描述：删除 标删</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-30
     **/
    public void deleteConclusion(Integer rid) {
        String deleteSql = " UPDATE TD_ZW_CHECK_CONCLUSION SET DEL_MARK=1 WHERE RID="+rid;
        this.executeSql(deleteSql,null);
    }
}
