package com.chis.modules.heth.zkcheck.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2023-7-6
 */
@Entity
@Table(name = "TD_ZW_ZK_CHECK_DETAILS")
@SequenceGenerator(name = "TdZwZkCheckDetails", sequenceName = "TD_ZW_ZK_CHECK_DETAILS_SEQ", allocationSize = 1)
public class TdZwZkCheckDetails implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwZkCheckRecord fkByMainId;
	private TsSimpleCode fkByItemId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	public TdZwZkCheckDetails() {
	}

	public TdZwZkCheckDetails(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwZkCheckDetails")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwZkCheckRecord getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwZkCheckRecord fkByMainId) {
		this.fkByMainId = fkByMainId;
	}
	@ManyToOne
	@JoinColumn(name = "ITEM_ID")
	public TsSimpleCode getFkByItemId() {
		return fkByItemId;
	}

	public void setFkByItemId(TsSimpleCode fkByItemId) {
		this.fkByItemId = fkByItemId;
	}


			
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}