package com.chis.modules.heth.zkcheck.entity;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSimpleCode;

import org.springframework.util.CollectionUtils;

import javax.persistence.*;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2021-6-26
 */
@Entity
@Table(name = "TB_ZW_ZK_SCORES")
@SequenceGenerator(name = "TbZwZkScores", sequenceName = "TB_ZW_ZK_SCORES_SEQ", allocationSize = 1)
public class TbZwZkScores implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TbZwZkBadrsnStand fkByMainId;
    private BigDecimal score;
    private Integer specialFlag;
    private Integer xh;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private List<TbZwZkScoreIndex> indexList;
    private List<TbZwZkScoreDeduct> deductList;
    /**
     * 项类
     */
    private TsSimpleCode fkByItemTypeId;

    private Integer itemTypeRid;
    /**
     * 题库类型/检测指标对应扩展字段
     */
    private String busExtends;
    /**
     * 临时字段
     */
    private String indexStr;
    /**
     * 临时字段-特殊标记
     */
    private Integer specialFlagRid;

    /**
     * 临时字段-特殊标记说明
     */
    private Integer busExtendsRid;

    /**
     * 存在问题 拼接
     */
    private String deductStr;
    private String deductStr2;

    /**index表的序号*/
    private String indexXh;

    /**指标 码表*/
    private TsSimpleCode fkByIndexId;

    /**指标大类名称*/
    private String firstIndexName;

    /**结果项*/
    private List<TbZwZkScoreOption> scoreOptionList;

    public TbZwZkScores() {
    }

    public TbZwZkScores(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbZwZkScores")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TbZwZkBadrsnStand getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TbZwZkBadrsnStand fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @Column(name = "SCORE")
    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    @Column(name = "SPECIAL_FLAG")
    public Integer getSpecialFlag() {
        return specialFlag;
    }

    public void setSpecialFlag(Integer specialFlag) {
        this.specialFlag = specialFlag;
    }

    @Column(name = "XH")
    public Integer getXh() {
        return xh;
    }

    public void setXh(Integer xh) {
        this.xh = xh;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TbZwZkScoreIndex> getIndexList() {
        return indexList;
    }

    public void setIndexList(List<TbZwZkScoreIndex> indexList) {
        this.indexList = indexList;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TbZwZkScoreDeduct> getDeductList() {
        return deductList;
    }

    public void setDeductList(List<TbZwZkScoreDeduct> deductList) {
        this.deductList = deductList;
    }

    @Transient
    public String getIndexStr() {
        indexStr = null;
        if (!CollectionUtils.isEmpty(indexList)) {
            Collections.sort(indexList, new Comparator<TbZwZkScoreIndex>() {
                @Override
                public int compare(TbZwZkScoreIndex o1, TbZwZkScoreIndex o2) {
                    Integer xh1 = o1.getXh();
                    Integer xh2 = o2.getXh();
                    if (null != xh1 && null != xh2) {
                        return xh1.compareTo(xh2);
                    } else if (null != xh1) {
                        return 1;
                    } else if (null != xh2) {
                        return -1;
                    }
                    return 0;
                }
            });
            StringBuffer buffer = new StringBuffer();
            for (TbZwZkScoreIndex scoreIndex : indexList) {
                if (null == scoreIndex.getFkByIndexId()) {
                    continue;
                }
                buffer.append("；</br>");
                if (null != scoreIndex.getIndexXh()) {
                    buffer.append(scoreIndex.getIndexXh())
                            .append("、");
                }
                buffer.append(scoreIndex.getFkByIndexId().getCodeName());
            }
            indexStr = buffer.toString();
            if (StringUtils.isNotBlank(indexStr)) {
                indexStr = indexStr.substring(6);
            }
        }
        return indexStr;
    }

    public void setIndexStr(String indexStr) {
        this.indexStr = indexStr;
    }
    @ManyToOne
    @JoinColumn(name = "ITEM_TYPE_ID")
    public TsSimpleCode getFkByItemTypeId() {
        return fkByItemTypeId;
    }

    public void setFkByItemTypeId(TsSimpleCode fkByItemTypeId) {
        this.fkByItemTypeId = fkByItemTypeId;
    }


    @Column(name = "BUS_EXTENDS")
    public String getBusExtends() {
        return busExtends;
    }

    public void setBusExtends(String busExtends) {
        this.busExtends = busExtends;
    }

    @Transient
    public Integer getSpecialFlagRid() {
        return specialFlagRid;
    }
    public void setSpecialFlagRid(Integer specialFlagRid) {
        this.specialFlagRid = specialFlagRid;
    }
    @Transient
    public Integer getBusExtendsRid() {
        return busExtendsRid;
    }
    public void setBusExtendsRid(Integer busExtendsRid) {
        this.busExtendsRid = busExtendsRid;
    }
    @Transient
    public Integer getItemTypeRid() {
        return itemTypeRid;
    }
    public void setItemTypeRid(Integer itemTypeRid) {
        this.itemTypeRid = itemTypeRid;
    }
    @Transient
    public String getDeductStr() {
        return deductStr;
    }
    public void setDeductStr(String deductStr) {
        this.deductStr = deductStr;
    }
    @Transient
    public String getDeductStr2() {
        return deductStr2;
    }
    public void setDeductStr2(String deductStr2) {
        this.deductStr2 = deductStr2;
    }
    @Transient
    public String getIndexXh() {
        return indexXh;
    }
    public void setIndexXh(String indexXh) {
        this.indexXh = indexXh;
    }
    @Transient
    public TsSimpleCode getFkByIndexId() {
        return fkByIndexId;
    }
    public void setFkByIndexId(TsSimpleCode fkByIndexId) {
        this.fkByIndexId = fkByIndexId;
    }
    @Transient
    public String getFirstIndexName() {
        return firstIndexName;
    }
    public void setFirstIndexName(String firstIndexName) {
        this.firstIndexName = firstIndexName;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TbZwZkScoreOption> getScoreOptionList() {
        return scoreOptionList;
    }

    public void setScoreOptionList(List<TbZwZkScoreOption> scoreOptionList) {
        this.scoreOptionList = scoreOptionList;
    }
}