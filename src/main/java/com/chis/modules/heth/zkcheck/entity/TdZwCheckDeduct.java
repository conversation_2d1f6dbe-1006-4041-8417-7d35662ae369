package com.chis.modules.heth.zkcheck.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2023-3-30
 */
@Entity
@Table(name = "TD_ZW_CHECK_DEDUCT")
@SequenceGenerator(name = "TdZwCheckDeduct", sequenceName = "TD_ZW_CHECK_DEDUCT_SEQ", allocationSize = 1)
public class TdZwCheckDeduct implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwCheckSub fkByMainId;
	private TbZwZkScoreDeduct fkByDeductId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwCheckDeduct() {
	}

	public TdZwCheckDeduct(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwCheckDeduct")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwCheckSub getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwCheckSub fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "DEDUCT_ID")			
	public TbZwZkScoreDeduct getFkByDeductId() {
		return fkByDeductId;
	}

	public void setFkByDeductId(TbZwZkScoreDeduct fkByDeductId) {
		this.fkByDeductId = fkByDeductId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}