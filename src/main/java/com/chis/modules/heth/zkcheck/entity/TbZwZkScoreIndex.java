package com.chis.modules.heth.zkcheck.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-6-26
 */
@Entity
@Table(name = "TB_ZW_ZK_SCORE_INDEX")
@SequenceGenerator(name = "TbZwZkScoreIndex", sequenceName = "TB_ZW_ZK_SCORE_INDEX_SEQ", allocationSize = 1)
public class TbZwZkScoreIndex implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbZwZkScores fkByMainId;
	private TsSimpleCode fkByFirstIndexId;
	private TsSimpleCode fkByIndexId;
	private String indexXh;
	private Integer xh;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TbZwZkScoreIndex() {
	}

	public TbZwZkScoreIndex(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbZwZkScoreIndex")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TbZwZkScores getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TbZwZkScores fkByMainId) {
		this.fkByMainId = fkByMainId;
	}

	@ManyToOne
	@JoinColumn(name = "FIRST_INDEX_ID")
	public TsSimpleCode getFkByFirstIndexId() {
		return fkByFirstIndexId;
	}
	public void setFkByFirstIndexId(TsSimpleCode fkByFirstIndexId) {
		this.fkByFirstIndexId = fkByFirstIndexId;
	}
	@ManyToOne
	@JoinColumn(name = "INDEX_ID")			
	public TsSimpleCode getFkByIndexId() {
		return fkByIndexId;
	}

	public void setFkByIndexId(TsSimpleCode fkByIndexId) {
		this.fkByIndexId = fkByIndexId;
	}	
			
	@Column(name = "INDEX_XH")	
	public String getIndexXh() {
		return indexXh;
	}

	public void setIndexXh(String indexXh) {
		this.indexXh = indexXh;
	}	
			
	@Column(name = "XH")	
	public Integer getXh() {
		return xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}