package com.chis.modules.heth.zkcheck.service;

import cn.hutool.core.convert.Convert;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.SortUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zkcheck.entity.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <p>类描述：质控考核service</p>
 *
 * @ClassAuthor qrr, 2021年6月26日, TdZwZkCheckMainService
 */
@Service
@Transactional(readOnly = false)
public class TdZwZkCheckMainService extends AbstractTemplate {
    @Autowired
    private TdZwZkCheckSummaryService checkSummaryService;

    /**
     * <p>方法描述：</p>
     *
     * @MethodAuthor qrr, 2021年6月26日, findTdZwZkCheckMain
     */
    @Transactional(readOnly = true)
    public TdZwZkCheckMain findTdZwZkCheckMain(Integer rid) {
        TdZwZkCheckMain main = this.find(TdZwZkCheckMain.class, rid);
        if (ObjectUtil.isNotNull(main.getFkByCheckTypeId()) && ObjectUtil.isNotNull(main.getFkByCheckTypeId().getRid())) {
            main.setCheckTypeId(main.getFkByCheckTypeId().getRid());
        }
        initializeCollections(main);
        List<TdZwZkCheckTable> checkTables = main.getCheckTables();
        if (!CollectionUtils.isEmpty(checkTables)) {
            Collections.sort(checkTables, new Comparator<TdZwZkCheckTable>() {
                @Override
                public int compare(TdZwZkCheckTable o1, TdZwZkCheckTable o2) {
                    if (o1 != null && o2 != null
                            && o1.getFkByCheckTableId() != null && o2.getFkByCheckTableId() != null
                            && o1.getFkByCheckTableId().getXh() != null) {
                        return o1.getFkByCheckTableId().getXh().compareTo(o2.getFkByCheckTableId().getXh());
                    }
                    return 0;
                }
            });
            for (TdZwZkCheckTable t : checkTables) {
                t.getCheckItems().size();
            }
        }
        // 处理备案类别
        processCheckRecords(main.getCheckRecords(), main);
        //证明材料与整改附件处理
        processMaterialCheckProves(main);
        return main;
    }

    /**
     * <p>方法描述：证明材料与整改附件处理</p>
     *
     * @MethodAuthor hsj 2025-06-05 15:01
     */
    private void processMaterialCheckProves(TdZwZkCheckMain main) {
        main.setMaterialCheckProves(new ArrayList<TdZwZkCheckProve>());
        main.setImproveCheckProves(new ArrayList<TdZwZkCheckProve>());
        List<TdZwZkCheckProve> checkProves = main.getCheckProves();
        if (CollectionUtils.isEmpty(checkProves)) {
            return;
        }
        for (TdZwZkCheckProve checkProve : checkProves) {
            if ("1".equals(Convert.toStr(checkProve.getAnnexType()))) {
                main.getImproveCheckProves().add(checkProve);
            } else {
                main.getMaterialCheckProves().add(checkProve);
            }
        }
    }

    /**
     * <p>方法描述：初始化集合大小以避免懒加载</p>
     *
     * @MethodAuthor hsj 2025-04-24 22:13
     */
    private void initializeCollections(TdZwZkCheckMain main) {
        if (main == null) {
            return;
        }
        List<TdZwZkCheckProve> checkProves = main.getCheckProves();
        if (checkProves != null) {
            checkProves.size();
        }

        List<TdZwZkCheckTable> checkTables = main.getCheckTables();
        if (checkTables != null) {
            checkTables.size();
        }
        List<TdZwZkCheckRecord> checkRecords = main.getCheckRecords();
        if (checkRecords != null) {
            checkRecords.size();
            for (TdZwZkCheckRecord record : checkRecords) {
                if (record != null && record.getTdZwZkCheckDetails() != null) {
                    record.getTdZwZkCheckDetails().size();
                }
            }
        }
    }

    /**
     * <p>方法描述：备案类别</p>
     *
     * @MethodAuthor hsj 2025-04-24 22:07
     */
    private void processCheckRecords(List<TdZwZkCheckRecord> checkRecords, TdZwZkCheckMain main) {
        if (CollectionUtils.isEmpty(checkRecords)) {
            return;
        }
        //码表排序
        SortUtil.sortCodeByField(checkRecords, TdZwZkCheckRecord.class, "getFkByTypeId");
        StringBuffer buffer = new StringBuffer();
        for (TdZwZkCheckRecord checkRecord : checkRecords) {
            buffer.append("，").append(checkRecord.getFkByTypeId().getCodeDesc());
        }
        if (buffer.length() > 0) {
            main.setRecordStr(buffer.substring(1));
        }
    }

    /**
     * <p>方法描述：获取扣分原因</p>
     *
     * @MethodAuthor qrr, 2021年6月26日, findTdZwZkCheckDeduct
     */
    @Transactional(readOnly = true)
    public List<TdZwZkCheckDeduct> findTdZwZkCheckDeductList(Integer mainId) {
        StringBuffer hql = new StringBuffer();
        hql.append("SELECT T FROM TdZwZkCheckDeduct T ");
        hql.append(" WHERE T.fkByMainId.fkByMainId.fkByMainId.fkByMainId.rid =").append(mainId);
        hql.append(" ORDER BY T.fkByDeductId.xh");
        return this.findByHql(hql.toString(), TdZwZkCheckDeduct.class);
    }

    /**
     * <p>
     * 方法描述：初始化考核材料值
     * </p>
     *
     * @MethodAuthor yph, 2021年06月30日
     */
    private void initProves(List<TdZwZkCheckProve> checkProves, TdZwZkCheckMain checkMain) {
        for (TdZwZkCheckProve itm : checkProves
        ) {
            itm.setRid(null);
            itm.setFkByMainId(checkMain);
            itm.setModifyManid(Global.getUser().getRid());
            itm.setModifyDate(new Date());
            itm.setCreateManid(Global.getUser().getRid());
            itm.setCreateDate(new Date());
        }
    }

    /**
     * @Description: 通过质控考核评估考核项目RID集合 获取RID对应质控考核结果子表集合的Map
     * @MethodAuthor pw, 2021年07月7日
     */
    @Transactional(readOnly = true)
    public Map<Integer, List<TdZwZkCheckSub>> findTdZwZkCheckSubListByMainRidsForMapResult(List<Integer> mainRidList) {
        Map<Integer, List<TdZwZkCheckSub>> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(mainRidList)) {
            return resultMap;
        }
        StringBuffer mainRidBuffer = new StringBuffer();
        for (Integer rid : mainRidList) {
            if (null != rid) {
                mainRidBuffer.append(",").append(rid);
            }
        }
        String mainRidStr = mainRidBuffer.toString();
        if (StringUtils.isBlank(mainRidStr)) {
            return resultMap;
        }
        String hql = " FROM TdZwZkCheckSub T WHERE T.fkByMainId.rid IN (" + mainRidStr.substring(1) + ") ";
        List<TdZwZkCheckSub> checkSubList = this.findByHql(hql, TdZwZkCheckSub.class);
        if (!CollectionUtils.isEmpty(checkSubList)) {
            for (TdZwZkCheckSub checkSub : checkSubList) {
                if (checkSub.getFkByScoreId() != null) {
                    checkSub.getFkByScoreId().getIndexList().size();
                    checkSub.getFkByScoreId().getDeductList().size();
                    checkSub.getFkByScoreId().getScoreOptionList().size();
                }
                checkSub.getCheckDeductList().size();
                List<TdZwZkCheckSub> tmpList = resultMap.get(checkSub.getFkByMainId().getRid());
                if (null == tmpList) {
                    tmpList = new ArrayList<>();
                }
                tmpList.add(checkSub);
                resultMap.put(checkSub.getFkByMainId().getRid(), tmpList);
            }
        }
        return resultMap;
    }

    /**
     * <p>描述 根据CheckMainRid查询CheckSubList</p>
     *
     * @param rid
     * @return java.util.Map<java.lang.Integer, java.util.List < com.chis.modules.heth.zkcheck.entity.TdZwZkCheckSub>>
     * @MethodAuthor gongzhe, 2022/7/11 16:00,findTdZwZkCheckSubListByCheckMainRidForMapResult
     */
    @Transactional(readOnly = true)
    public Map<Integer, List<TdZwZkCheckSub>> findTdZwZkCheckSubListByCheckMainRidForMapResult(Integer rid) {
        Map<Integer, List<TdZwZkCheckSub>> resultMap = new HashMap<>();
        if (rid == null) {
            return resultMap;
        }
        String hql = " FROM TdZwZkCheckSub T WHERE T.fkByMainId.fkByMainId.fkByMainId.rid= " + rid;
        List<TdZwZkCheckSub> checkSubList = this.findByHql(hql, TdZwZkCheckSub.class);
        if (!CollectionUtils.isEmpty(checkSubList)) {
            for (TdZwZkCheckSub checkSub : checkSubList) {
                if (checkSub.getFkByScoreId() != null) {
                    checkSub.getFkByScoreId().getIndexList().size();
                    checkSub.getFkByScoreId().getDeductList().size();
                    checkSub.getFkByScoreId().getScoreOptionList().size();
                }
                checkSub.getCheckDeductList().size();
                List<TdZwZkCheckSub> tmpList = resultMap.get(checkSub.getFkByMainId().getRid());
                if (null == tmpList) {
                    tmpList = new ArrayList<>();
                }
                tmpList.add(checkSub);
                resultMap.put(checkSub.getFkByMainId().getRid(), tmpList);
            }
        }
        return resultMap;
    }

    /**
     * @Description: 存储考核评估表下的考核结果子表以及考核扣分原因
     * @MethodAuthor pw, 2021年07月10日
     */
    public void saveCheckSubByTdZwZkCheckTable(TdZwZkCheckTable curCheckTable) {
        if (null == curCheckTable) {
            return;
        }
        List<TdZwZkCheckItem> checkItemList = curCheckTable.getCheckItems();
        if (CollectionUtils.isEmpty(checkItemList)) {
            return;
        }
        List<TdZwZkCheckSub> saveCheckSubList = new ArrayList<>();
        List<TdZwZkCheckSub> updateCheckSubList = new ArrayList<>();
        for (TdZwZkCheckItem checkItem : checkItemList) {
            List<TdZwZkCheckSub> checkSubList = checkItem.getCheckSubList();
            if (CollectionUtils.isEmpty(checkSubList)) {
                continue;
            }
            for (TdZwZkCheckSub checkSub : checkSubList) {
                if (null == checkSub.getRid()) {
                    preInsert(checkSub);
                    saveCheckSubList.add(checkSub);
                } else {
                    preUpdate(checkSub);
                    updateCheckSubList.add(checkSub);
                }
            }
        }
        if (!CollectionUtils.isEmpty(saveCheckSubList)) {
            this.saveBatchObjs(saveCheckSubList);
        }
        if (!CollectionUtils.isEmpty(updateCheckSubList)) {
            this.updateBatchObjs(updateCheckSubList);
        }
    }


    /**
     * @Description: 考核评估表撤销
     * 注意 该方法仅会更新状态字段
     * @MethodAuthor pw, 2021年07月10日
     */
    public void cancelCheckTable(TdZwZkCheckTable curCheckTable) {
        if (null == curCheckTable || null == curCheckTable.getRid()) {
            return;
        }
        if (null != curCheckTable.getStateMark() && 1 == curCheckTable.getStateMark()) {
            String updateSql = " UPDATE TD_ZW_ZK_CHECK_TABLE SET STATE_MARK=0 WHERE RID=" + curCheckTable.getRid().intValue();
            this.executeSql(updateSql, null);
        }
    }

    /**
     * <p>方法描述：级联保存考核主表前  因考核考核结果子表为级联，所以保存时单独操作</p>
     *
     * @MethodAuthor： yzz
     * @Date：2021-07-12
     **/
    @Transactional(readOnly = true)
    public void deleteCheckSubByItemsIds(TdZwZkCheckMain checkMain) {
        StringBuffer itemIds = new StringBuffer();
        for (TdZwZkCheckTable checkTable : checkMain.getCheckTables()) {
            for (TdZwZkCheckItem checkItem : checkTable.getCheckItems()) {
                if (null != checkItem.getRid()) {
                    itemIds.append(",").append(checkItem.getRid());
                }
            }
        }

        StringBuffer hql = new StringBuffer();
        hql.append("select T from TdZwZkCheckSub T ");
        hql.append(" where 1=1 ");
        if (itemIds.length() > 0) {
            hql.append(" and T.fkByMainId.rid not in (");
            hql.append(itemIds.substring(1));
            hql.append(")");
        }
        hql.append(" and T.fkByMainId.fkByMainId.fkByMainId.rid=");
        hql.append(checkMain.getRid());
        List<TdZwZkCheckSub> checkSubList = this.findByHql(hql.toString(), TdZwZkCheckSub.class);
        if (!CollectionUtils.isEmpty(checkSubList)) {
            for (TdZwZkCheckSub checkSub : checkSubList) {
                checkSub.getCheckDeductList().size();
            }
            for (TdZwZkCheckSub checkSub : checkSubList) {
                this.deleteEntity(checkSub);
            }
        }

    }


    /**
     * @Description: 通过评估表rid集合 获取 是否需要整改
     * 如果质控考核结果子表有扣分原因 那么需要整改
     * @MethodAuthor pw, 2021年07月12日
     */
    public boolean ifNeedImproveCheckQuery(List<Integer> checkTableRidList) {
        boolean flag = false;
        if (CollectionUtils.isEmpty(checkTableRidList)) {
            return flag;
        }
        StringBuffer buffer = new StringBuffer();
        for (Integer tableRid : checkTableRidList) {
            buffer.append(",").append(tableRid.intValue());
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT COUNT(*) FROM TD_ZW_ZK_CHECK_TABLE T  ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_ITEM T1 ON T.RID = T1.MAIN_ID ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_SUB T2 ON T1.RID = T2.MAIN_ID ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_DEDUCT T3 ON T2.RID = T3.MAIN_ID ")
                .append(" WHERE T.RID IN (")
                .append(buffer.substring(1)).append(") ");
        int count = this.findCountBySql(sqlBuffer.toString());
        if (count > 0) {
            flag = true;
        }
        return flag;
    }


    /**
     * @Description: 通过评估表rid 获取评估表
     * @MethodAuthor pw, 2021年07月13日
     */
    @Transactional(readOnly = true)
    public TdZwZkCheckTable findTdZwCheckTable(Integer rid) {
        TdZwZkCheckTable checkTable = this.find(TdZwZkCheckTable.class, rid);
        if (null != checkTable) {
            checkTable.getCheckItems().size();
        }
        return checkTable;
    }

    /**
     * <p>方法描述：查询评估表的状态</p>
     *
     * @MethodAuthor qrr, 2021年9月19日, findZkCheckTableState
     */
    @Transactional(readOnly = true)
    public List<Object[]> findZkCheckTableState(Integer mainId) {
        if (null == mainId) {
            return null;
        }
        StringBuffer sql = new StringBuffer();
        sql.append("select  t.rid,t.STATE_MARK");
        sql.append(" from TD_ZW_ZK_CHECK_TABLE t where 1=1 ");
        sql.append(" and t.MAIN_ID =").append(mainId);
        return this.findSqlResultList(sql.toString());
    }

    /**
     * <p>方法描述：查询所有考核的汇总信息</p>
     *
     * @MethodAuthor qrr, 2021年9月19日, findCheckSubSummary
     */
    @Transactional(readOnly = true)
    public List<Object[]> findCheckSubSummary(Integer mainId) {
        if (null == mainId) {
            return null;
        }
        StringBuffer sql = new StringBuffer();
        sql.append("select t3.ITEM_TYPE_ID,t.SCORE_RST_ID,count(t.rid)");
        sql.append(" from TD_ZW_ZK_CHECK_SUB t ");
        sql.append(" left join TD_ZW_ZK_CHECK_ITEM t1 on t1.rid = t.MAIN_ID ");
        sql.append(" left join TD_ZW_ZK_CHECK_TABLE t2 on t2.rid = t1.MAIN_ID ");
        sql.append(" left join TB_ZW_ZK_SCORES t3 on t3.rid = t.SCORE_ID ");
        sql.append(" where t2.MAIN_ID =").append(mainId);
        sql.append(" group by t3.ITEM_TYPE_ID,t.SCORE_RST_ID ");
        return this.findSqlResultList(sql.toString());
    }

    /**
     * <p>方法描述：保存汇总信息</p>
     *
     * @MethodAuthor qrr, 2021年9月19日, saveCheckSummary
     */
    public void saveCheckSummary(TdZwZkCheckMain checkMain, List<TdZwZkCheckSummary> summaries) {
        if (null == checkMain.getRid()) {
            return;
        }
        //更新考核等级、是否合格、文书编号
        StringBuffer sql = new StringBuffer();
        sql.append("update TD_ZW_ZK_CHECK_MAIN set CHECK_NO =").append(checkMain.getCheckNo());
        if (null != checkMain.getFkByCheckRstId() && null != checkMain.getFkByCheckRstId().getRid()) {
            sql.append(",CHECK_RST_ID =").append(checkMain.getFkByCheckRstId().getRid());
        }
        if (StringUtils.isNotBlank(checkMain.getIfHg())) {
            sql.append(",IF_HG =").append(checkMain.getIfHg());
        }
        sql.append(" where rid =").append(checkMain.getRid());
        this.executeSql(sql.toString(), null);
        //保存考核汇总信息
        this.checkSummaryService.saveTdZwZkCheckSummary(checkMain.getRid(), summaries);
    }

    /**
     * <p>方法描述：根据人员抽取题目拼接备备注信息</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-06-14
     **/
    public List<Object[]> findRmkBypsn(String busExtends, Integer unitRid, String checkType) {

        StringBuffer sql = new StringBuffer();
        sql.append(" select T.PSN_NAME,listagg(T1.CODE_NAME,'，') within group ( order by T1.num) as queType,T.SCORE ");
        sql.append(" from TD_PROCHK_EXTRACT_PSN T ");
        sql.append(" left join TB_PROCHK_EXTRACT_SUBJECT T2 on T.RID=T2.MAIN_ID ");
        sql.append(" left join TS_SIMPLE_CODE T1 on T2.QUE_TYPE_ID=T1.RID ");
        sql.append(" where substr(to_char(T.EXTRACT_DATE,'yyyy-mm-dd'),0,4)='").append(DateUtils.getYear()).append("'");
        sql.append(" and T.STATE=2 ");
        sql.append(" and T.UNIT_ID= ").append(unitRid);
        if (StringUtils.isNoneBlank(checkType)) {
            if ("1".equals(checkType)) {
                sql.append(" and T.CHECK_TYPE= 1 ");
            } else if ("2".equals(checkType)) {
                sql.append(" and T.CHECK_TYPE= 2 ");
            } else if ("3".equals(checkType)) {
                sql.append(" and T.CHECK_TYPE= 3 ");
            } else if ("4".equals(checkType)) {
                sql.append(" and T.CHECK_TYPE= 4 ");
            }
        }
        sql.append(" and exists(select 1 from TB_PROCHK_EXTRACT_SUBJECT tt ");
        sql.append(" left join TS_SIMPLE_CODE Tt1 on tt.QUE_TYPE_ID=Tt1.RID ");
        sql.append(" where tt.MAIN_ID = t.rid and Tt1.EXTENDS2 in (").append(busExtends).append(")) ");
        sql.append(" group by T.PSN_NAME,T.EXTRACT_DATE,T.SCORE,t.rid ");
        sql.append(" order by T.EXTRACT_DATE desc,T.PSN_NAME ");

        return this.findSqlResultList(sql.toString());
    }

    /**
     * 根据指标EXTENDS2获取被评估单位盲样考核结果录入合格数
     *
     * @param unitId 被评估单位
     * @return 合格、不合格数
     */
    public List<Map<String, Object>> findZwmyRcdRstHgCount(Integer unitId) {
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "WITH LAST_UP AS (SELECT T1.RID " +
                "                 FROM ( SELECT ZP.RID FROM TD_ZWMY_PLAN ZP WHERE ZP.STATE = 1 ORDER BY ZP.PUBLISH_DATE DESC,ZP.RID DESC ) T1 " +
                "                 WHERE ROWNUM = 1) " +
                "SELECT TSC.EXTENDS2, TSC.CODE_NAME, ZRR.IF_HG, COUNT(1) SUM " +
                "FROM TD_ZWMY_SAMP_SUB ZSS " +
                "         INNER JOIN TD_ZWMY_FETCH_SAMP ZFS ON ZFS.RID = ZSS.MAIN_ID " +
                "         INNER JOIN TS_SIMPLE_CODE TSC ON ZSS.ITEM_ID = TSC.RID " +
                "         INNER JOIN TD_ZWMY_RCD_RST ZRR ON ZSS.RID = ZRR.MAIN_ID " +
                "         INNER JOIN TD_ZWMY_SIGN_UP ZSU ON ZFS.MAIN_ID = ZSU.RID " +
                "WHERE EXISTS(SELECT 1 FROM LAST_UP LU WHERE ZSU.MAIN_ID = LU.RID) " +
                " AND ZSU.UNIT_ID = :unitId " +
                " AND ZSU.STATE >= 6 " +
                "GROUP BY TSC.EXTENDS2, TSC.CODE_NAME, ZRR.IF_HG " +
                "ORDER BY TSC.EXTENDS2, ZRR.IF_HG DESC ";
        paramMap.put("unitId", unitId);
        return this.findDataBySql(sql, paramMap);
    }


    /**
     * <p>方法描述：根据考核主表rid查询评估表</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-06-17
     **/
    public List<TdZwZkCheckTable> findCheckTableByRid(Integer rid) {
        StringBuffer hql = new StringBuffer();
        hql.append(" select T from TdZwZkCheckTable T where T.fkByMainId=").append(rid);
        return this.findByHql(hql.toString(), TdZwZkCheckTable.class);

    }

    /**
     * <p>方法描述：更新主表状态</p>
     *
     * @MethodAuthor： yzz
     * @Date：2023-01-12
     **/
    @Transactional(readOnly = false)
    public void updateCheckMainStatue(Integer statue, Integer rid) {
        if (statue == null) {
            return;
        }
        //更新状态
        StringBuffer sql = new StringBuffer();
        sql.append("update TD_ZW_ZK_CHECK_MAIN set STATE_MARK =").append(statue);
        sql.append(" where rid =").append(rid);
        this.executeSql(sql.toString(), null);
    }

    /**
     * <p>方法描述：查询现场质控考核项目</p>
     *
     * @MethodAuthor hsj 2023-03-30 11:49
     */
    public List<TdZwCheckItem> findTdZwCheckItems(Integer jcRptId, Integer checkTableId, Integer indexId) {
        if (ObjectUtil.isNull(jcRptId) || ObjectUtil.isNull(checkTableId) || ObjectUtil.isNull(indexId)) {
            return new ArrayList<>();
        }
        StringBuffer hql = new StringBuffer();
        hql.append(" SELECT T FROM TdZwCheckItem T WHERE T.fkByMainId.fkByMainId.rid = ").append(jcRptId)
                .append(" AND T.fkByMainId.fkByCheckTableId.rid = ").append(checkTableId)
                .append(" AND T.fkByItemId.rid=").append(indexId);
        List<TdZwCheckItem> list = this.findByHql(hql.toString(), TdZwCheckItem.class);
        return list;

    }

    public List<TdZwCheckDeduct> findTbZwZkScoreDeductList(Integer mainId) {
        StringBuffer hql = new StringBuffer();
        hql.append("SELECT T FROM TdZwCheckDeduct T ");
        hql.append(" WHERE T.fkByMainId.fkByMainId.rid =").append(mainId);
        hql.append(" ORDER BY T.fkByDeductId.xh");
        return this.findByHql(hql.toString(), TdZwCheckDeduct.class);
    }

    /**
     * <p>方法描述： 通过现场质控考核项目rid获取结果子表及扣分原因 </p>
     *
     * @MethodAuthor： pw 2023/4/4
     **/
    @Transactional(readOnly = true)
    public List<TdZwCheckSub> findTdZwZkCheckSubList(Integer itemId) {
        String hql = "SELECT T FROM TdZwCheckSub T WHERE T.fkByMainId.rid=" + itemId;
        List<TdZwCheckSub> resultList = this.findByHql(hql, TdZwCheckSub.class);
        if (!CollectionUtils.isEmpty(resultList)) {
            for (TdZwCheckSub sub : resultList) {
                sub.getTdZwCheckDeductList().size();
            }
        }
        return resultList;
    }

    /**
     * <p>方法描述： 存储考核评估表下的考核结果子表以及考核扣分原因 更新评估表 </p>
     *
     * @MethodAuthor： pw 2023/4/6
     **/
    public void saveCheckSubAndUpdateTdZwZkCheckTable(TdZwZkCheckTable curCheckTable) {
        if (null == curCheckTable) {
            return;
        }
        List<TdZwZkCheckItem> checkItemList = curCheckTable.getCheckItems();
        if (CollectionUtils.isEmpty(checkItemList)) {
            return;
        }
        List<TdZwZkCheckSub> saveCheckSubList = new ArrayList<>();
        List<Object> updateObjList = new ArrayList<>();
        for (TdZwZkCheckItem checkItem : checkItemList) {
            List<TdZwZkCheckSub> checkSubList = checkItem.getCheckSubList();
            if (CollectionUtils.isEmpty(checkSubList)) {
                continue;
            }
            for (TdZwZkCheckSub checkSub : checkSubList) {
                if (null == checkSub.getRid()) {
                    preInsert(checkSub);
                    saveCheckSubList.add(checkSub);
                } else {
                    preUpdate(checkSub);
                    updateObjList.add(checkSub);
                }
            }
        }
        if (null != curCheckTable.getRid()) {
            preUpdate(curCheckTable);
            updateObjList.add(curCheckTable);
        }
        if (!CollectionUtils.isEmpty(saveCheckSubList)) {
            this.saveBatchObjs(saveCheckSubList);
        }
        if (!CollectionUtils.isEmpty(updateObjList)) {
            this.updateBatchObjs(updateObjList);
        }
    }

    /**
     * <p>方法描述： 撤销考核评估表 </p>
     *
     * @MethodAuthor： pw 2023/4/6
     **/
    public void cancelCheckTable(Integer rid) {
        if (null == rid) {
            return;
        }
        String sql = " UPDATE TD_ZW_ZK_CHECK_TABLE SET STATE_MARK=0 WHERE RID=" + rid;
        this.executeSql(sql, null);
    }

    /**
     * <p>方法描述： 通过检测报告rid 获取项目对应的现场质控考核结果子表
     * 因健康检查质控考核主表的考核结果需要考核结果子表的否决项选项值判断赋值 这里返回项目码表对应的考核结果子表
     * </p>
     *
     * @MethodAuthor： pw 2023/4/6
     **/
    @Transactional(readOnly = true)
    public Map<Integer, List<TdZwCheckSub>> findTdZwCheckSubListByRptRidForMapResult(Integer rptRid) {
        Map<Integer, List<TdZwCheckSub>> resultMap = new HashMap<>();
        if (null == rptRid) {
            return resultMap;
        }
        StringBuffer hqlBuffer = new StringBuffer();
        hqlBuffer.append(" select t from TdZwCheckSub t where t.fkByMainId.fkByMainId.fkByMainId.rid=").append(rptRid);
        List<TdZwCheckSub> checkSubList = this.findByHql(hqlBuffer.toString(), TdZwCheckSub.class);
        if (!CollectionUtils.isEmpty(checkSubList)) {
            for (TdZwCheckSub checkSub : checkSubList) {
                Integer itemId = checkSub.getFkByMainId().getFkByItemId().getRid();
                if (null == itemId) {
                    continue;
                }
                List<TdZwCheckSub> tmpList = resultMap.get(itemId);
                if (null == tmpList) {
                    tmpList = new ArrayList<>();
                }
                tmpList.add(checkSub);
                resultMap.put(itemId, tmpList);
            }
        }
        return resultMap;
    }

    /**
     * <p>方法描述： 存储考核日期及检测报告 </p>
     *
     * @MethodAuthor： pw 2023/4/8
     **/
    public void updateTdZwZkCheckMainDateAndRptByEntity(TdZwZkCheckMain checkMain) {
        if (null == checkMain.getRid()) {
            return;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" UPDATE TD_ZW_ZK_CHECK_MAIN SET CHECK_DATE= ");
        Date checkDate = checkMain.getCheckDate();
        Integer checkRptRid = null == checkMain.getFkByJcRptId() ? null : checkMain.getFkByJcRptId().getRid();
        if (null == checkDate) {
            sqlBuffer.append(" NULL ");
        } else {
            sqlBuffer.append("to_date('" + DateUtils.formatDate(checkDate, "yyyy-MM-dd") + "','yyyy-MM-dd')");
        }
        sqlBuffer.append(", JC_RPT_ID =");
        if (null == checkRptRid) {
            sqlBuffer.append(" NULL ");
        } else {
            sqlBuffer.append(checkRptRid);
        }
        String writePath = checkMain.getWritePath();
        sqlBuffer.append(", WRITE_PATH =");
        if (StringUtils.isBlank(writePath)) {
            sqlBuffer.append(" NULL ");
        } else {
            sqlBuffer.append("'").append(writePath).append("'");
        }
        sqlBuffer.append("  WHERE RID = ").append(checkMain.getRid());
        this.executeSql(sqlBuffer.toString(), null);
    }

    /**
     * <p>方法描述：职业健康检查机构服务明细</p>
     *
     * @MethodAuthor hsj 2025-04-24 9:10
     */
    public List<Object[]> findTdZwTjorgItmDetailByMainId(Integer mainId) {
        List<Object[]> list = new ArrayList<>();
        if (ObjectUtil.isNull(mainId)) {
            return list;
        }
        StringBuffer buffer = new StringBuffer();
        buffer.append(" WITH simpleTab AS ( ");
        buffer.append(" SELECT T.*  FROM  TS_SIMPLE_CODE T ");
        buffer.append(" LEFT JOIN TS_CODE_TYPE T1 ON ");
        buffer.append(" T.CODE_TYPE_ID = T1.RID ");
        buffer.append(" WHERE  T1.CODE_TYPE_NAME = '5018' ) ");

        buffer.append(" SELECT T3.RID as typeRid,T3.NUM as typeNum,T3.CODE_NO as typeCodeNo, T3.CODE_NAME as typeCodeName,T3.CODE_DESC,T.ITEM_DETAIL_ID ,SC.NUM,SC.CODE_NO,SC.CODE_NAME,T1.RID as mainId");
        buffer.append(" FROM TD_ZW_TJORG_ITM_DETAIL T ");
        buffer.append(" LEFT JOIN  TD_ZW_TJORGGITEMS T1 ON T.MAIN_ID = T1.RID ");
        buffer.append(" LEFT JOIN  TS_SIMPLE_CODE SC ON SC.RID = T.ITEM_DETAIL_ID  ");
        buffer.append(" LEFT JOIN  simpleTab T3 ON T3.CODE_NO = T1.ITEM_CODE  ");
        buffer.append(" WHERE T1.ORG_ID = ").append(mainId);
        return this.findDataBySqlNoPage(buffer.toString(), null);
    }

    /**
     * <p>方法描述：根据主表rid 查询备案类别大类</p>
     *
     * @MethodAuthor hsj '2023-07-06' 17:25
     */
    public List<TsSimpleCode> findTdZwTjorgRcdItemByMainId(Integer mainId, String extendS1) {
        List<TsSimpleCode> simpleCodes = new ArrayList<>();
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT DISTINCT T1.RID ,T1.CODE_NAME,T1.CODE_DESC ,T1.num,T1.code_no FROM ");
        switch (extendS1) {
            case "1":
                //职业健康检查 TD_ZW_TJORG_RCD_ITEM
                sql.append(" TD_ZW_TJORG_RCD_ITEM T left join TS_SIMPLE_CODE T1 on T.ITEM_ID = T1.rid");
                sql.append(" left join TD_ZW_TJORG_RECORD T2 on T2.rid =T.MAIN_ID ");
                sql.append(" where T2.MAIN_ID = ").append(mainId);
                break;
            case "2":
                //诊断 TD_ZW_DIAGITEMS
                sql.append(" TD_ZW_DIAGITEMS T left join TS_SIMPLE_CODE T1 on T.ITEM_CODE = T1.CODE_NO");
                sql.append(" left join TS_CODE_TYPE T2 on T1.CODE_TYPE_ID = T2.rid");
                sql.append(" where T.ORG_ID = ").append(mainId);
                sql.append(" and  T2.CODE_TYPE_NAME = 5020");
                break;
            default:
                sql = new StringBuffer();
                break;

        }
        if (sql != null && StringUtils.isNotBlank(sql.toString())) {
            sql.append(" order by T1.num,T1.code_no");
            List<Object[]> list = this.findSqlResultList(sql.toString());
            if (!CollectionUtils.isEmpty(list)) {
                for (Object[] obj : list) {
                    TsSimpleCode tsSimpleCode = new TsSimpleCode();
                    tsSimpleCode.setRid(Integer.parseInt(ObjectUtil.toStr(obj[0])));
                    tsSimpleCode.setCodeName(ObjectUtil.toStr(obj[1]));
                    tsSimpleCode.setCodeDesc(ObjectUtil.toStr(obj[2]));
                    tsSimpleCode.setCodeNo(ObjectUtil.toStr(obj[4]));
                    simpleCodes.add(tsSimpleCode);
                }
            }
        }
        return simpleCodes;
    }

    /**
     * <p>方法描述：状态查询</p>
     *
     * @MethodAuthor hsj 2025-06-05 11:12
     */
    @Transactional(readOnly = true)
    public String findStateById(Integer rid) {
        if (ObjectUtil.isNull(rid)) {
            return null;
        }
        String sql = "SELECT RID , STATE_MARK FROM TD_ZW_ZK_CHECK_MAIN WHERE NVL(DEL_MARK,0) = 0 AND RID = " + rid;
        List<Object[]> list = this.findSqlResultList(sql);
        if (!CollectionUtils.isEmpty(list)) {
            return Convert.toStr(list.get(0)[1]);
        }
        return null;
    }

    /**
     * <p>方法描述：整改状态的查询</p>
     *
     * @MethodAuthor hsj 2025-06-06 14:05
     */
    @Transactional(readOnly = true)
    public String findIfImproveEndById(Integer rid) {
        if (ObjectUtil.isNull(rid)) {
            return null;
        }
        String sql = "SELECT RID , IF_IMPROVE_END FROM TD_ZW_ZK_CHECK_MAIN WHERE NVL(DEL_MARK,0) = 0 AND  RID = " + rid;
        List<Object[]> list = this.findSqlResultList(sql);
        if (!CollectionUtils.isEmpty(list)) {
            return Convert.toStr(list.get(0)[1]);
        }
        return null;
    }

    /**
     * <p>方法描述：整改审核</p>
     *
     * @MethodAuthor hsj 2025-06-06 14:12
     */
    @Transactional(readOnly = false)
    public void improveAudit(TdZwZkCheckMain checkMain) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append("UPDATE TD_ZW_ZK_CHECK_MAIN SET IF_IMPROVE_END =:ifImproveEnd ");
        sqlBuffer.append(", CHECK_OPINION =:checkOpinion");
        sqlBuffer.append(", IMPROVE_DATE =:improveDate ");
        sqlBuffer.append(", CHECK_ORG_ID =:checkOrgId ");
        sqlBuffer.append(", CHECK_PSN_ID =:checkPsnId ");
        sqlBuffer.append(", MODIFY_DATE =:modifyDate ");
        sqlBuffer.append(", MODIFY_MANID =:modifyManid ");
        sqlBuffer.append(" WHERE RID = ").append(checkMain.getRid());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("ifImproveEnd", checkMain.getIfImproveEnd());
        paramMap.put("checkOpinion", checkMain.getCheckOpinion());
        paramMap.put("improveDate", new Date());
        paramMap.put("checkOrgId", Global.getUser().getTsUnit().getRid());
        paramMap.put("checkPsnId", Global.getUser().getRid());
        paramMap.put("modifyDate", new Date());
        paramMap.put("modifyManid", Global.getUser().getRid());
        this.executeSql(sqlBuffer.toString(), paramMap);
    }

    /**
     * <p>方法描述：整改</p>
     *
     * @MethodAuthor hsj 2025-06-06 14:35
     */
    @Transactional(readOnly = false)
    public void improveSave(TdZwZkCheckMain checkMain) {
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append("UPDATE TD_ZW_ZK_CHECK_MAIN SET IF_IMPROVE_END =:ifImproveEnd ");
        sqlBuffer.append(", IMPROVE_LINKMAN =:improveLinkman");
        sqlBuffer.append(", IMPROVE_PHONE =:improvePhone ");
        sqlBuffer.append(", MODIFY_DATE =:modifyDate ");
        sqlBuffer.append(", MODIFY_MANID =:modifyManid ");
        if ("1".equals(Convert.toStr(checkMain.getIfImproveEnd()))) {
            //提交时清空审核相关信息点
            sqlBuffer.append(", CHECK_OPINION = null");
            sqlBuffer.append(", IMPROVE_DATE = null");
            sqlBuffer.append(", CHECK_ORG_ID = null");
            sqlBuffer.append(", CHECK_PSN_ID = null");
        }
        sqlBuffer.append(" WHERE RID = ").append(checkMain.getRid());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("ifImproveEnd", checkMain.getIfImproveEnd());
        paramMap.put("improveLinkman", checkMain.getImproveLinkman());
        paramMap.put("improvePhone", checkMain.getImprovePhone());
        paramMap.put("modifyDate", new Date());
        paramMap.put("modifyManid", Global.getUser().getRid());
        this.executeSql(sqlBuffer.toString(), paramMap);
        //整改附件
        String sql = "DELETE FROM TD_ZW_ZK_CHECK_PROVE WHERE ANNEX_TYPE =1 AND MAIN_ID = " + checkMain.getRid();
        this.executeSql(sql);
        if (!CollectionUtils.isEmpty(checkMain.getImproveCheckProves())) {
            for (TdZwZkCheckProve checkProve : checkMain.getImproveCheckProves()) {
                checkProve.setRid(null);
                upsertEntity(checkProve);
            }
        }
    }

    /**
     * <p>方法描述：同考核类型 同被考核机构 同年份的考核日期</p>
     *
     * @MethodAuthor hsj 2025-06-06 17:47
     */
    public int findCountCheckMainExistsByCurYear(TdZwZkCheckMain checkMain) {
        Integer checkMainRid = checkMain.getRid();
        Integer checkYear = null == checkMain.getCheckDate() ? null : DateUtils.getYear(checkMain.getCheckDate());
        Integer orgId = null == checkMain.getFkByOrgId() ? null : checkMain.getFkByOrgId().getRid();
        Integer checkTypeId = null == checkMain.getFkByCheckTypeId() ? null : checkMain.getFkByCheckTypeId().getRid();
        if (null == checkTypeId || null == orgId || null == checkYear) {
            return 0;
        }
        String querySql = " SELECT COUNT(1) FROM TD_ZW_ZK_CHECK_MAIN T WHERE NVL(T.DEL_MARK,0)=0 AND T.CHECK_TYPE_ID=" + checkTypeId + " AND T.ORG_ID=" + orgId + " AND EXTRACT(YEAR FROM T.CHECK_DATE)=" + checkYear;
        if (null != checkMainRid) {
            querySql += " AND T.RID != " + checkMainRid;
        }
        return this.findCountBySql(querySql, null);
    }

    /**
     * <p>方法描述：一级页面存储方法</p>
     *
     * @MethodAuthor hsj 2025-06-07 9:07
     */
    @Transactional(readOnly = false)
    public void tosave(TdZwZkCheckMain checkMain, List<TdZwZkCheckSub> noAssessCheckSubList) {
        boolean ifUpdate = false;//是否局部更新
        /*清除结果子表中的记录*/
        if (null != checkMain.getRid()) {
            this.deleteCheckSubByItemsIds(checkMain);
            if(!CollectionUtils.isEmpty( checkMain.getCheckTables())){
                for (TdZwZkCheckTable checkTable : checkMain.getCheckTables()) {
                    if(ObjectUtil.isNotNull(checkTable.getRid())){
                        ifUpdate = true;
                    }
                }
            }
        }
        if(ifUpdate){
            StringBuffer sqlBuffer = new StringBuffer();
            sqlBuffer.append("UPDATE TD_ZW_ZK_CHECK_MAIN SET ");
            //考核日期
            sqlBuffer.append(" CHECK_DATE =:checkDate ");
            //现场意见表
            sqlBuffer.append(", WRITE_PATH =:writePath ");
            //现场考核意见书
            sqlBuffer.append(", CHECK_TABLE_PATH =:checkTablePath ");
            sqlBuffer.append(", STATE_MARK =:stateMark ");
            //是否需要整改是否为1
            if("1".equals(Convert.toStr(checkMain.getIfNeedImprove()))){
                sqlBuffer.append(", IF_IMPROVE_END = 0 ");
            }
            //更新时间
            sqlBuffer.append(", MODIFY_DATE =:modifyDate ");
            sqlBuffer.append(", MODIFY_MANID =:modifyManid ");

            sqlBuffer.append(" WHERE RID = ").append(checkMain.getRid());
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("checkDate", checkMain.getCheckDate());
            paramMap.put("writePath", checkMain.getWritePath());
            paramMap.put("checkTablePath", checkMain.getCheckTablePath());
            paramMap.put("stateMark", checkMain.getStateMark());
            paramMap.put("modifyDate", new Date());
            paramMap.put("modifyManid", Global.getUser().getRid());
            this.executeSql(sqlBuffer.toString(), paramMap);
            //考核证明材料
            List<TdZwZkCheckProve> materialCheckProves = checkMain.getMaterialCheckProves();
            String sql = "DELETE FROM TD_ZW_ZK_CHECK_PROVE WHERE NVL(ANNEX_TYPE,0) =0 AND MAIN_ID = " + checkMain.getRid();
            this.executeSql(sql);
            if (!CollectionUtils.isEmpty(materialCheckProves)) {
                for (TdZwZkCheckProve checkProve : materialCheckProves) {
                    checkProve.setRid(null);
                    upsertEntity(checkProve);
                }
            }
        }else{
            this.upsertEntity(checkMain);
        }
        if (null != checkMain.getRid()) {
            checkMain = this.findTdZwZkCheckMain(checkMain.getRid());
            if (ObjectUtil.isNotNull(checkMain.getFkByCheckTypeId()) && ObjectUtil.isNotNull(checkMain.getFkByCheckTypeId().getRid())) {
                checkMain.setCheckTypeId(checkMain.getFkByCheckTypeId().getRid());
            }
        }
        if (!CollectionUtils.isEmpty(noAssessCheckSubList)) {
            Map<Integer, TdZwZkCheckItem> checkItemMap = new HashMap<>();
            List<TdZwZkCheckTable> checkTables = checkMain.getCheckTables();
            if (!CollectionUtils.isEmpty(checkTables)) {
                for (TdZwZkCheckTable checkSub : checkTables) {
                    List<TdZwZkCheckItem> checkItems = checkSub.getCheckItems();
                    if (CollectionUtils.isEmpty(checkItems)) {
                        continue;
                    }
                    for (TdZwZkCheckItem checkItem : checkItems) {
                        checkItemMap.put(checkItem.getFkByItemId().getRid(), checkItem);
                    }
                }
            }
            for (TdZwZkCheckSub checkSub : noAssessCheckSubList) {
                Integer itemId = checkSub.getFkByMainId().getFkByItemId().getRid();
                if (!checkItemMap.containsKey(itemId)) {
                    continue;
                }
                checkSub.setRid(null);
                checkSub.setFkByMainId(checkItemMap.get(itemId));
                preInsert(checkSub);
            }
            this.saveBatchObjs(noAssessCheckSubList);
            //存储成功后将清空子表缓存
            noAssessCheckSubList.clear();
        }
    }

    /**
    * <p>Description：保存当前考核表 </p>
    * <p>Author： yzz 2025/6/12 </p>
    */
    @Transactional
    public void saveCheckTable(TdZwZkCheckTable checkTable) {
        // 只保存考核表，不需要处理子项列表
        this.upsertEntity(checkTable);
    }
    /**
    * <p>Description：更新主表字段 </p>
    * <p>Author： yzz 2025/6/12 </p>
    */
    @Transactional
    public void toSaveMain(TdZwZkCheckMain checkMain) {
        String sql=" update TD_ZW_ZK_CHECK_MAIN set TOTAL_SCORE_VAL=";
        if(checkMain.getTotalScoreVal()!=null){
            sql+=checkMain.getTotalScoreVal();
        }else {
            sql+="null";
        }
        if(checkMain.getIfNeedImprove()!=null){
            sql+=", IF_NEED_IMPROVE="+checkMain.getIfNeedImprove();
        }else {
            sql+=", IF_NEED_IMPROVE=null";
        }
        if(checkMain.getReviewConclusion()!=null){
            sql+=", REVIEW_CONCLUSION="+checkMain.getReviewConclusion();
        }else{
            sql+=", REVIEW_CONCLUSION=null";
        }
        sql+=" where RID="+checkMain.getRid();
        this.executeSql(sql,null);
    }
    /**
     *  <p>方法描述：附件上传</p>
     * @MethodAuthor hsj 2025-06-16 14:27
     */

    @Transactional(readOnly = false)
    public void updatePath(String simpleFilePath, String annexTypeStr, Integer rid) {
        if(StringUtils.isBlank(annexTypeStr) || ObjectUtil.isNull(rid)){
                return;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        Map<String, Object> paramMap = new HashMap<>();
        sqlBuffer.append("UPDATE TD_ZW_ZK_CHECK_MAIN SET ");
        if("2".equals(annexTypeStr)){
            //现场意见表
            sqlBuffer.append("WRITE_PATH =:writePath ");
            paramMap.put("writePath", simpleFilePath);
        }else if("3".equals(annexTypeStr)){
            //现场检查表
            sqlBuffer.append("CHECK_TABLE_PATH =:checkTablePath ");
            paramMap.put("checkTablePath", simpleFilePath);
        }
        //更新时间
        sqlBuffer.append(", MODIFY_DATE =:modifyDate ");
        sqlBuffer.append(", MODIFY_MANID =:modifyManid ");
        sqlBuffer.append(" WHERE RID = ").append(rid);
        paramMap.put("modifyDate", new Date());
        paramMap.put("modifyManid", Global.getUser().getRid());
        this.executeSql(sqlBuffer.toString(), paramMap);
    }
}
