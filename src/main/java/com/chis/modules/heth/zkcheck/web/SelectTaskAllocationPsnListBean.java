package com.chis.modules.heth.zkcheck.web;

import cn.hutool.core.convert.Convert;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.heth.zkcheck.logic.UserPsnInfoDTO;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessPackData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.PackLazyDataModel;
import com.chis.modules.system.web.FacesSimpleBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.ToggleSelectEvent;
import org.primefaces.event.UnselectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>类描述：系统单位人员弹出框</p>
 *
 * @ClassAuthor hsj 2024-07-08 16:00
 */
@ManagedBean(name = "selectTaskAllocationPsnListBean")
@ViewScoped
public class SelectTaskAllocationPsnListBean extends FacesSimpleBean implements IProcessPackData {

    private static final long serialVersionUID = -738445454536625063L;

    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    private PackLazyDataModel psnDataModel;
    /***查询条件-地区*/
    private List<TsZone> zoneList;
    private String searchZoneGb;
    private String searchZoneName;
    /**
     * 查询条件-单位名称
     */
    private String searchUnitName;
    /**
     * 查询条件-姓名
     */
    private String searchPsnName;
    /**
     * 抽取列表当前页已勾选
     */
    private List<UserPsnInfoDTO> selectedList;
    /**
     * 抽取列表全部已勾选的
     */
    private List<UserPsnInfoDTO> allSelectedList;
    private String TABLE_ID = "codeForm:dataTable";
    /**
     * 地区
     */
    private String zoneGb;

    public SelectTaskAllocationPsnListBean() {
        this.ifSQL = true;
        this.selectedList = new ArrayList<>();
        this.allSelectedList = new ArrayList<>();
        this.searchUnitName = null;
        this.searchPsnName = null;
        this.zoneGb = JsfUtil.getRequest().getParameter("zoneGb");
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(this.zoneGb.substring(0, 2), true, "", "");
        this.searchZoneGb = this.zoneList.get(0).getZoneGb();
        this.searchZoneName = this.zoneList.get(0).getZoneName();
        RequestContext.getCurrentInstance().update("codeForm");
        //置为第一页
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(TABLE_ID);
        dataTable.setFirst(0);
        this.searchAction();
    }

    @Override
    public void searchAction() {
        this.paramMap.clear();
        String[] hql = this.buildHqls();
        this.psnDataModel = new PackLazyDataModel<UserPsnInfoDTO>(hql[0], hql[1], this.paramMap, this, TABLE_ID, this.ifSQL);
        this.selectedList.clear();
        this.selectedList.addAll(allSelectedList);
    }

    /**
     * <p>描述 处理数据</p>
     *
     * @param list
     * @MethodAuthor gongzhe, 2022.03.10 11:10,processPackData
     */
    @Override
    public List<UserPsnInfoDTO> processPackData(List list) {
        List<Object[]> data = (List<Object[]>) list;
        List<UserPsnInfoDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        for (Object[] obj : data) {
            UserPsnInfoDTO dto = new UserPsnInfoDTO();
            dto.setRid(Convert.toInt(obj[0]));
            dto.setUnitname((Convert.toStr(obj[1])));
            dto.setUsername((Convert.toStr(obj[2])));
            dto.setZoneId((Convert.toInt(obj[3])));
            dto.setZoneName((Convert.toStr(obj[4])));
            result.add(dto);
        }
        return result;
    }


    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();
        sql.append(" FROM  TS_USER_INFO T  ");
        sql.append(" LEFT JOIN TS_UNIT T1 ON  T.UNIT_RID  = T1.RID ");
        sql.append(" LEFT JOIN  TS_ZONE T2 ON  T1.ZONE_ID = T2.RID  ");
        sql.append(" WHERE T.IF_REVEAL = 1 ");
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            sql.append(" AND T2.ZONE_GB LIKE :zoneGb escape '\\\'");
            this.paramMap.put("zoneGb", ZoneUtil.zoneSelect(this.searchZoneGb.trim()) + "%");
        }
        if (StringUtils.isNotBlank(searchUnitName)) {
            sql.append(" AND T1.UNITNAME LIKE :searchUnitName escape '\\\'");
            this.paramMap.put("searchUnitName", "%" + StringUtils.convertBFH(this.searchUnitName.trim()) + "%");
        }
        if (StringUtils.isNotBlank(searchPsnName)) {
            sql.append(" AND T.USERNAME  LIKE :searchPsnName escape '\\\'");
            this.paramMap.put("searchPsnName", "%" + StringUtils.convertBFH(this.searchPsnName.trim()) + "%");
        }
        String h2 = "SELECT COUNT(*) " + sql;
        String h1 = "SELECT T.RID ,T1.UNITNAME ,T.USERNAME ,T2.RID as zoneId  ,CASE WHEN T2.ZONE_TYPE >2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME,'_')+1) ELSE T2.FULL_NAME END zoneName  "
                + sql + " ORDER BY  T2.ZONE_GB ,T1.UNITNAME ,T.USERNAME ";
        return new String[]{h1, h2};
    }

    /**
     * <p>方法描述：确定</p>
     *
     * @MethodAuthor hsj 2024-07-08 16:13
     */
    public void submitAction() {
        if (null == this.allSelectedList || this.allSelectedList.size() == 0) {
            JsfUtil.addErrorMessage("请选择人员！");
            return;
        }
        if (this.allSelectedList.size() > 1000) {
            JsfUtil.addErrorMessage("最多可选择1000条数据！");
            return;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("selected", this.allSelectedList);
        RequestContext.getCurrentInstance().closeDialog(map);
    }

    /**
     * <p>方法描述：关闭</p>
     *
     * @MethodAuthor hsj 2024-07-08 16:13
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

    /**
     * <p>方法描述： 翻页监听</p>
     *
     * @MethodAuthor hsj 2024-07-08 16:14
     */
    public void pageListener() {
        this.selectedList.clear();
        this.selectedList.addAll(this.allSelectedList);
        RequestContext.getCurrentInstance().update(this.TABLE_ID);
    }

    /**
     * <p>方法描述：行选中监听，添加元素</p>
     *
     * @MethodAuthor hsj 2024-07-08 16:15
     */
    public void rowSelectListener(SelectEvent event) {
        UserPsnInfoDTO dto = (UserPsnInfoDTO) event.getObject();
        this.allSelectedList.add(dto);
    }

    /**
     * <p>方法描述：行取消，移除元素</p>
     *
     * @MethodAuthor hsj 2024-07-08 16:15
     */
    public void rowUnselectListener(UnselectEvent event) {
        UserPsnInfoDTO dto = (UserPsnInfoDTO) event.getObject();
        this.allSelectedList.remove(dto);
    }

    /**
     * <p>方法描述：全选或取消全选</p>
     *
     * @MethodAuthor hsj 2024-07-08 16:15
     */
    public void toggleSelectListener(ToggleSelectEvent event) {
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(this.TABLE_ID);
        List<UserPsnInfoDTO> displayList = this.psnDataModel.getPackData();
        if (CollectionUtils.isEmpty(displayList)) {
            return;
        }
        if (event.isSelected()) {
            List<UserPsnInfoDTO> list = (List<UserPsnInfoDTO>) dataTable.getSelection();
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            for (UserPsnInfoDTO o : list) {
                if (!this.allSelectedList.contains(o)) {
                    this.allSelectedList.add(o);
                }
            }
        } else {//取消全选，需要移除当前页的所有元素
            this.allSelectedList.removeAll(displayList);
        }
    }

    public PackLazyDataModel getPsnDataModel() {
        return psnDataModel;
    }

    public void setPsnDataModel(PackLazyDataModel psnDataModel) {
        this.psnDataModel = psnDataModel;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneGb() {
        return searchZoneGb;
    }

    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public String getSearchPsnName() {
        return searchPsnName;
    }

    public void setSearchPsnName(String searchPsnName) {
        this.searchPsnName = searchPsnName;
    }

    public List<UserPsnInfoDTO> getSelectedList() {
        return selectedList;
    }

    public void setSelectedList(List<UserPsnInfoDTO> selectedList) {
        this.selectedList = selectedList;
    }

    public List<UserPsnInfoDTO> getAllSelectedList() {
        return allSelectedList;
    }

    public void setAllSelectedList(List<UserPsnInfoDTO> allSelectedList) {
        this.allSelectedList = allSelectedList;
    }

    public String getTABLE_ID() {
        return TABLE_ID;
    }

    public void setTABLE_ID(String TABLE_ID) {
        this.TABLE_ID = TABLE_ID;
    }

    public String getZoneGb() {
        return zoneGb;
    }

    public void setZoneGb(String zoneGb) {
        this.zoneGb = zoneGb;
    }
}
