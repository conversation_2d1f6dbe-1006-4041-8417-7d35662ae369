package com.chis.modules.heth.zkcheck.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-9-18
 */
@Entity
@Table(name = "TD_ZW_ZK_CHECK_SUMMARY")
@SequenceGenerator(name = "TdZwZkCheckSummary", sequenceName = "TD_ZW_ZK_CHECK_SUMMARY_SEQ", allocationSize = 1)
public class TdZwZkCheckSummary implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwZkCheckMain fkByMainId;
	private TsSimpleCode fkByItemTypeId;
	private TsSimpleCode fkByScoreRstId;
	private Integer itemNum;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwZkCheckSummary() {
	}

	public TdZwZkCheckSummary(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwZkCheckSummary")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwZkCheckMain getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwZkCheckMain fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_TYPE_ID")			
	public TsSimpleCode getFkByItemTypeId() {
		return fkByItemTypeId;
	}

	public void setFkByItemTypeId(TsSimpleCode fkByItemTypeId) {
		this.fkByItemTypeId = fkByItemTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "SCORE_RST_ID")			
	public TsSimpleCode getFkByScoreRstId() {
		return fkByScoreRstId;
	}

	public void setFkByScoreRstId(TsSimpleCode fkByScoreRstId) {
		this.fkByScoreRstId = fkByScoreRstId;
	}	
			
	@Column(name = "ITEM_NUM")	
	public Integer getItemNum() {
		return itemNum;
	}

	public void setItemNum(Integer itemNum) {
		this.itemNum = itemNum;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}