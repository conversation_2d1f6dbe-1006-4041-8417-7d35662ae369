package com.chis.modules.heth.zkcheck.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2023-3-30
 */
@Entity
@Table(name = "TD_ZW_CHECK_TABLE")
@SequenceGenerator(name = "TdZwCheckTable", sequenceName = "TD_ZW_CHECK_TABLE_SEQ", allocationSize = 1)
public class TdZwCheckTable implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwCheckRpt fkByMainId;
    private TbZwZkBadrsnStand fkByCheckTableId;
    private String checkPsn;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private List<TdZwCheckItem> checkItemList;

    public TdZwCheckTable() {
    }

    public TdZwCheckTable(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwCheckTable")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwCheckRpt getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwCheckRpt fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "CHECK_TABLE_ID")
    public TbZwZkBadrsnStand getFkByCheckTableId() {
        return fkByCheckTableId;
    }

    public void setFkByCheckTableId(TbZwZkBadrsnStand fkByCheckTableId) {
        this.fkByCheckTableId = fkByCheckTableId;
    }

    @Column(name = "CHECK_PSN")
    public String getCheckPsn() {
        return checkPsn;
    }

    public void setCheckPsn(String checkPsn) {
        this.checkPsn = checkPsn;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwCheckItem> getCheckItemList() {
        return checkItemList;
    }

    public void setCheckItemList(List<TdZwCheckItem> checkItemList) {
        this.checkItemList = checkItemList;
    }
}