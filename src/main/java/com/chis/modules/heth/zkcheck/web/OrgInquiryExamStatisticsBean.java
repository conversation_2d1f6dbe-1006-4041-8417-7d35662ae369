package com.chis.modules.heth.zkcheck.web;

import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesBean;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.context.RequestContext;
import org.primefaces.model.DefaultStreamedContent;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;

/**
 * 机构质控考核情况统计查询
 *
 * <AUTHOR>
 * @version 1.0
 */
@ManagedBean(name = "orgInquiryExamStatisticsBean")
@ViewScoped
public class OrgInquiryExamStatisticsBean extends FacesBean {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
     * 查询条件：地区集合
     */
    private List<TsZone> searchZoneList = new ArrayList<>();
    /**
     * 查询条件：地区编码
     */
    private String searchZoneCode;
    /**
     * 查询条件：地区名称
     */
    private String searchZoneName;
    /**
     * 查询条件：考核类型
     */
    private Integer examTypeRid;
    private List<TsSimpleCode> examTypeSimpleCodeList;
    private Map<Integer, String> examTypeSimpleCodeNameMap;
    /**
     * 查询条件：考核日期-开始日期
     */
    private Date searchExamBeginDate;
    /**
     * 查询条件：考核日期-结束日期
     */
    private Date searchExamEndDate;
    /**
     * 查询条件：质控类别（省级用户显示）<p>2:省级<p>3:市级
     */
    private List<Integer> inquiryExamTypeList;
    private String excelTitle = "";

    private Boolean isProvUser = Boolean.FALSE;
    private List<Object[]> dataTableList;
    private Integer dataTableListCount = 0;
    /**
     * 评分项结果
     */
    private List<TsSimpleCode> rstSimpleCodeList;

    public OrgInquiryExamStatisticsBean() {
        this.dataTableList = new ArrayList<>();
        init();
    }

    private void init() {
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        if (tsZone == null) {
            tsZone = new TsZone();
        }
        if (tsZone.getRealZoneType() != null) {
            this.isProvUser = tsZone.getRealZoneType() == 2;
        }
        //地区
        this.searchZoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "4");
        this.searchZoneCode = this.searchZoneList.get(0).getZoneCode();
        this.searchZoneName = this.searchZoneList.get(0).getZoneName();
        //考核类型
        this.examTypeSimpleCodeNameMap = new HashMap<>(16);
        this.examTypeSimpleCodeList = this.commService.findNumSimpleCodesByTypeId("5554");
        this.rstSimpleCodeList = this.commService.findNumSimpleCodesByTypeId("5534");
        if (ObjectUtil.isEmpty(this.rstSimpleCodeList)) {
            this.rstSimpleCodeList = new ArrayList<>();
        }
        if (ObjectUtil.isNotEmpty(this.examTypeSimpleCodeList)) {
            this.examTypeRid = this.examTypeSimpleCodeList.get(0).getRid();
            for (TsSimpleCode simpleCode : this.examTypeSimpleCodeList) {
                this.examTypeSimpleCodeNameMap.put(simpleCode.getRid(), simpleCode.getCodeName());
            }
        }
        //考核日期
        this.searchExamEndDate = new Date();
        this.searchExamBeginDate = DateUtils.getYearFirstDay(this.searchExamEndDate);
        //质控类别
        this.inquiryExamTypeList = new ArrayList<>();
    }

    public void searchAction() {
        if (verifyQueryRequiredFailed()) {
            return;
        }
        executeSearchSql();
    }

    private boolean verifyQueryRequiredFailed() {
        boolean verify = false;
        if (ObjectUtil.isEmpty(this.searchZoneCode)) {
            JsfUtil.addErrorMessage("地区不允许为空！");
            verify = true;
        }
        if (ObjectUtil.isEmpty(this.examTypeRid)) {
            JsfUtil.addErrorMessage("考核类型不允许为空！");
            verify = true;
        }
        if (ObjectUtil.isEmpty(this.searchExamBeginDate)) {
            JsfUtil.addErrorMessage("考核开始日期不允许为空！");
            verify = true;
        }
        if (ObjectUtil.isEmpty(this.searchExamEndDate)) {
            JsfUtil.addErrorMessage("考核结束日期不允许为空！");
            verify = true;
        }
        if (ObjectUtil.isNotEmpty(this.searchExamBeginDate) && ObjectUtil.isNotEmpty(this.searchExamEndDate)) {
            if (DateUtils.getYear(this.searchExamBeginDate) != DateUtils.getYear(this.searchExamEndDate)) {
                JsfUtil.addErrorMessage("考核日期不允许跨年查询！");
                verify = true;
            }
        }
        return verify;
    }

    public void executeSearchSql() {
        Map<String, Object> paramMap = new HashMap<>();
        StringBuilder sql = new StringBuilder();

        sql.append("WITH ZK_CHECK_MAIN AS ( ");
        sql.append("    SELECT RID FROM ( ");
        sql.append("        SELECT CM.RID, ROW_NUMBER() OVER (PARTITION BY CM.ORG_ID ORDER BY CM.CHECK_DATE DESC, CM.RID DESC) CMRN ");
        sql.append("        FROM TD_ZW_ZK_CHECK_MAIN CM ");
        sql.append("            LEFT JOIN TS_UNIT U1 ON CM.ORG_ID = U1.RID ");
        sql.append("            LEFT JOIN TS_ZONE Z1 ON U1.ZONE_ID = Z1.RID ");
        sql.append("            LEFT JOIN TS_UNIT U2 ON CM.RECORD_ORG_ID = U2.RID ");
        sql.append("            LEFT JOIN TS_ZONE Z2 ON U2.MANAGE_ZONE_ID = Z2.RID ");
        sql.append("            LEFT JOIN TD_ZW_ZK_CHECK_TABLE CT ON CM.RID = CT.MAIN_ID ");
        sql.append("            LEFT JOIN TB_ZW_ZK_BADRSN_STAND BS ON CT.CHECK_TABLE_ID = BS.RID ");
        sql.append("        WHERE CM.STATE_MARK = 1 AND NVL(CM.DEL_MARK, 0) = 0 ");
        sql.append("            AND Z1.ZONE_GB LIKE :zoneCode ");
        paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(StringUtils.objectToString(this.searchZoneCode)).trim()) + "%");
        sql.append("            AND BS.CHECK_TYPE_ID = :checkType ");
        paramMap.put("checkType", this.examTypeRid);
        sql.append("            AND CM.CHECK_DATE >= TO_DATE(:searchExamBeginDate, 'YYYY-MM-DD HH24:MI:SS') ");
        sql.append("            AND CM.CHECK_DATE <= TO_DATE(:searchExamEndDate, 'YYYY-MM-DD HH24:MI:SS') ");
        paramMap.put("searchExamBeginDate", DateUtils.formatDate(this.searchExamBeginDate) + " 00:00:00");
        paramMap.put("searchExamEndDate", DateUtils.formatDate(this.searchExamEndDate) + " 23:59:59");
        if (this.isProvUser) {
            if (ObjectUtil.isNotEmpty(this.inquiryExamTypeList)) {
                sql.append("            AND Z2.REAL_ZONE_TYPE IN (:realZoneTypeList) ");
                paramMap.put("realZoneTypeList", this.inquiryExamTypeList);
            }
        } else {
            //市级管理机构只能统计“录入机构 TD_ZW_ZK_CHECK_MAIN.RECORD_ORG_ID”为本单位的质控考核数据
            sql.append("            AND CM.RECORD_ORG_ID = :recordOrgId ");
            paramMap.put("recordOrgId", Global.getUser().getTsUnit().getRid());
        }
        sql.append("        ) WHERE CMRN = 1 ");
        sql.append("    ), ");
        sql.append("    ORG_SCORE AS ( ");
        sql.append("        SELECT CM.ORG_ID, U.UNITNAME, CS.SCORE_ID, SC2.RID, SC2.NUM ");
        sql.append("        FROM TD_ZW_ZK_CHECK_MAIN CM ");
        sql.append("            LEFT JOIN TS_UNIT U ON CM.ORG_ID = U.RID ");
        sql.append("            LEFT JOIN TS_SIMPLE_CODE SC1 ON CM.CHECK_TYPE_ID = SC1.RID ");
        sql.append("            LEFT JOIN TD_ZW_ZK_CHECK_TABLE CT ON CM.RID = CT.MAIN_ID ");
        sql.append("            LEFT JOIN TD_ZW_ZK_CHECK_ITEM CI ON CT.RID = CI.MAIN_ID ");
        sql.append("            LEFT JOIN TD_ZW_ZK_CHECK_SUB CS ON CI.RID = CS.MAIN_ID ");
        sql.append("            LEFT JOIN TS_SIMPLE_CODE SC2 ON CS.SCORE_RST_ID = SC2.RID ");
        sql.append("        WHERE EXISTS(SELECT 1 FROM ZK_CHECK_MAIN ZCM WHERE CM.RID = ZCM.RID) ");
        sql.append("        ), ");
        sql.append("    SCORE_TARGET_SUM AS (SELECT SCORE_ID S_RID, RID TARGET, SUM(1) SUM FROM ORG_SCORE OS GROUP BY SCORE_ID, RID), ");
        sql.append("    SCORE_TARGET_SUM_NAME AS (SELECT ");
        sql.append("                                SCORE_ID S_RID, RID JG_RID, ");
        sql.append("                                RTRIM(EXTRACT(XMLAGG(XMLELEMENT(E, UNITNAME || ',')), '/E/text()').GETCLOBVAL(), ',') AS UNITNAMES ");
        sql.append("                            FROM ORG_SCORE OS GROUP BY SCORE_ID, RID), ");
        sql.append("    SCORE_TARGET_SUM_NAME1 AS (SELECT ");
        sql.append("                                SCORE_ID S_RID, RID JG_RID, ");
        sql.append("                                RTRIM(EXTRACT(XMLAGG(XMLELEMENT(E, UNITNAME || ',')), '/E/text()').GETCLOBVAL(), ',') AS UNITNAMES ");
        sql.append("                            FROM ORG_SCORE OS GROUP BY SCORE_ID, RID), ");
        sql.append("    SCORE_SUM AS (SELECT SCORE_ID S_RID, SUM(1) SUM FROM ORG_SCORE OS GROUP BY SCORE_ID), ");
        sql.append("    SCORE_INDEX AS ( ");
        sql.append("        SELECT S.RID S_RID, BS.CHECK_NAME BS_NAME, BS.XH BS_XH, SC2.CODE_NAME SI_NAME, S.XH SI_XH, SC4.RID JG_RID, SC4.CODE_NAME JG_NAME, SC4.NUM JG_NUM ");
        sql.append("        FROM TB_ZW_ZK_BADRSN_STAND BS ");
        sql.append("            LEFT JOIN TB_ZW_ZK_SCORES S ON BS.RID = S.MAIN_ID ");
        sql.append("            LEFT JOIN TB_ZW_ZK_SCORE_INDEX SI ON S.RID = SI.MAIN_ID ");
        sql.append("            LEFT JOIN TS_SIMPLE_CODE SC1 ON SI.INDEX_ID = SC1.RID ");
        sql.append("            LEFT JOIN TS_SIMPLE_CODE SC2 ON SC1.CODE_LEVEL_NO = SC2.CODE_LEVEL_NO || '.' || SC1.CODE_NO ");
        sql.append("            INNER JOIN TS_CODE_TYPE CT ON SC2.CODE_TYPE_ID = CT.RID AND CT.CODE_TYPE_NAME = '5530' ");
        sql.append("            LEFT JOIN TS_SIMPLE_CODE SC3 ON S.ITEM_TYPE_ID = SC3.RID ");
        sql.append("            INNER JOIN TS_SIMPLE_CODE SC4 ON (',' || SC4.EXTENDS3 || ',') LIKE '%,' || SC3.CODE_NO || ',%' AND SC4.IF_REVEAL = 1 ");
        sql.append("            INNER JOIN TS_CODE_TYPE CT1 ON SC4.CODE_TYPE_ID = CT1.RID AND CT1.CODE_TYPE_NAME = '5534' ");
        sql.append("        WHERE BS.STATE_MARK = 1 AND BS.CHECK_TYPE_ID = :checkType ");
        sql.append("        GROUP BY S.RID, BS.CHECK_NAME, BS.XH, SC2.CODE_NAME, S.XH, SC4.RID, SC4.CODE_NAME, SC4.NUM, CT1.RID ");
        sql.append("    ), ");
        sql.append("    SCORE_INDEX1 AS ( ");
        sql.append("        SELECT S.RID S_RID, BS.CHECK_NAME BS_NAME, BS.XH BS_XH, SC2.CODE_NAME SI_NAME, S.XH SI_XH, SC4.RID JG_RID, SC4.CODE_NAME JG_NAME, SC4.NUM JG_NUM ");
        sql.append("        FROM TB_ZW_ZK_BADRSN_STAND BS ");
        sql.append("            LEFT JOIN TB_ZW_ZK_SCORES S ON BS.RID = S.MAIN_ID ");
        sql.append("            LEFT JOIN TB_ZW_ZK_SCORE_INDEX SI ON S.RID = SI.MAIN_ID ");
        sql.append("            LEFT JOIN TS_SIMPLE_CODE SC1 ON SI.INDEX_ID = SC1.RID ");
        sql.append("            LEFT JOIN TS_SIMPLE_CODE SC2 ON SC1.CODE_LEVEL_NO = SC2.CODE_LEVEL_NO || '.' || SC1.CODE_NO ");
        sql.append("            INNER JOIN TS_CODE_TYPE CT ON SC2.CODE_TYPE_ID = CT.RID AND CT.CODE_TYPE_NAME = '5530' ");
        sql.append("            LEFT JOIN TS_SIMPLE_CODE SC3 ON S.ITEM_TYPE_ID = SC3.RID ");
        sql.append("            INNER JOIN TS_SIMPLE_CODE SC4 ON (',' || SC4.EXTENDS3 || ',') NOT LIKE '%,' || SC3.CODE_NO || ',%' AND SC4.IF_REVEAL = 1 ");
        sql.append("            INNER JOIN TS_CODE_TYPE CT1 ON SC4.CODE_TYPE_ID = CT1.RID AND CT1.CODE_TYPE_NAME = '5534' ");
        sql.append("        WHERE BS.STATE_MARK = 1 AND BS.CHECK_TYPE_ID = :checkType ");
        sql.append("        GROUP BY S.RID, BS.CHECK_NAME, BS.XH, SC2.CODE_NAME, S.XH, SC4.RID, SC4.CODE_NAME, SC4.NUM, CT1.RID ");
        sql.append("    ) ");
        sql.append("SELECT * FROM ( ");
        sql.append("    SELECT ");
        sql.append("        SI.S_RID                                                  AS SI_RID, ");
        sql.append("        SI.BS_NAME                                                AS BS_NAME, ");
        sql.append("        SI.SI_NAME                                                AS SI_NAME, ");
        sql.append("        SI.SI_XH                                                  AS SI_XH, ");
        sql.append("        NVL(SS.SUM, 0)                                            AS ALL_SUM, ");
        sql.append("        SI.JG_RID                                                 AS JG_RID, ");
        sql.append("        NVL(STS.SUM, 0)                                           AS SUM, ");
        sql.append("        ROUND((NVL(STS.SUM, 0) / NVL(SS.SUM, 1) * 100), 2)        AS PASSING_RATE, ");
        sql.append("        STSN.UNITNAMES                                            AS UNIT_NAMES, ");
        sql.append("        SI.BS_XH                                                  AS BS_XH, ");
        sql.append("        SI.JG_NUM                                                 AS JG_NUM ");
        sql.append("    FROM SCORE_INDEX SI ");
        sql.append("        LEFT JOIN SCORE_SUM SS ON SI.S_RID = SS.S_RID ");
        sql.append("        LEFT JOIN SCORE_TARGET_SUM STS ON SI.S_RID = STS.S_RID AND SI.JG_RID = STS.TARGET ");
        sql.append("        LEFT JOIN SCORE_TARGET_SUM_NAME STSN ON SI.S_RID = STSN.S_RID AND SI.JG_RID = STSN.JG_RID ");
        sql.append("    UNION ALL ");
        sql.append("    SELECT ");
        sql.append("        SI.S_RID                                                  AS SI_RID, ");
        sql.append("        SI.BS_NAME                                                AS BS_NAME, ");
        sql.append("        SI.SI_NAME                                                AS SI_NAME, ");
        sql.append("        SI.SI_XH                                                  AS SI_XH, ");
        sql.append("        -1                                                        AS ALL_SUM, ");
        sql.append("        SI.JG_RID                                                 AS JG_RID, ");
        sql.append("        -1                                                        AS SUM, ");
        sql.append("        -1                                                        AS PASSING_RATE, ");
        sql.append("        STSN.UNITNAMES                                            AS UNIT_NAMES, ");
        sql.append("        SI.BS_XH                                                  AS BS_XH, ");
        sql.append("        SI.JG_NUM                                                 AS JG_NUM ");
        sql.append("    FROM SCORE_INDEX1 SI ");
        sql.append("        INNER JOIN SCORE_TARGET_SUM_NAME1 STSN ON SI.S_RID = STSN.S_RID AND SI.JG_RID = STSN.JG_RID ");
        sql.append(") ");
        sql.append("ORDER BY BS_XH, SI_XH, JG_NUM ");

        this.dataTableList = new ArrayList<>();
        this.excelTitle = "";
        this.dataTableListCount = 0;

        List<Object[]> dataList = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql.toString(), paramMap));
        dealDataList(dataList);
        dealMergeCol();
        this.excelTitle = DateUtils.getYear(this.searchExamBeginDate) + this.examTypeSimpleCodeNameMap.get(this.examTypeRid) + "质控情况统计";
    }

    private void dealDataList(List<Object[]> dataList) {
        int size = this.rstSimpleCodeList.size();
        //计算最终结果数组长度（固定列（6）：一级指标名称、一级指标合并单元格标记、二级指标名称、二级指标合并单元格标记、考核内容序号、参与考核机构数；动态评分项结果列（每项占3列））
        int length = 6 + size * 3;
        //存储评分项结果序号
        Map<String, Integer> rstNumMap = new HashMap<>(16);
        List<TsSimpleCode> simpleCodeList = this.rstSimpleCodeList;
        for (int i = 0; i < simpleCodeList.size(); i++) {
            rstNumMap.put(simpleCodeList.get(i).getRid().toString(), i);
        }
        Map<String, Object[]> siRidMap = new LinkedHashMap<>(16);
        for (Object[] objects : dataList) {
            if (!siRidMap.containsKey(StringUtils.objectToString(objects[0]))) {
                Object[] object = new Object[length];
                //一级指标
                object[0] = StringUtils.objectToString(objects[1]);
                object[1] = 0;
                //二级指标
                object[2] = StringUtils.objectToString(objects[2]);
                object[3] = 0;
                //考核内容序号
                object[4] = StringUtils.objectToString(objects[3]);
                //参与考核机构数
                object[5] = StringUtils.objectToString(objects[4]);
                for (int i = 0; i < size * 3; i++) {
                    object[i + 6] = "/";
                }
                siRidMap.put(StringUtils.objectToString(objects[0]), object);
            }
            Object[] object = siRidMap.get(StringUtils.objectToString(objects[0]));
            if (rstNumMap.containsKey(StringUtils.objectToString(objects[5]))) {
                Integer index = rstNumMap.get(StringUtils.objectToString(objects[5]));
                //机构数
                String orgNum = StringUtils.objectToString(objects[6]);
                if (!"-1".equals(orgNum)) {
                    object[6 + index * 3] = orgNum;
                }
                //率(%)
                String orgPer = StringUtils.objectToString(objects[7]);
                if (!"-1".equals(orgPer)) {
                    object[7 + index * 3] = orgPer;
                }
                //机构名称
                String orgNames = ObjectUtil.convert(String.class, objects[8]);
                object[8 + index * 3] = orgNames;
            }
        }
        if (ObjectUtil.isEmpty(siRidMap)) {
            return;
        }
        this.dataTableListCount = siRidMap.size();
        for (Map.Entry<String, Object[]> entry : siRidMap.entrySet()) {
            this.dataTableList.add(entry.getValue());
        }
    }

    /**
     * 处理合并单元格
     */
    private void dealMergeCol() {
        if (ObjectUtil.isEmpty(this.dataTableList)) {
            return;
        }
        String colName1 = StringUtils.objectToString(this.dataTableList.get(0)[0]);
        String colName2 = StringUtils.objectToString(this.dataTableList.get(0)[2]);
        int col1 = 1;
        int colIndex1 = 0;
        int col2 = 1;
        int colIndex2 = 0;
        List<Object[]> tableList = this.dataTableList;
        for (int i = 0; i < tableList.size(); i++) {
            if (i == 0) {
                continue;
            }
            Object[] objects = tableList.get(i);
            String name1 = StringUtils.objectToString(objects[0]);
            String name2 = StringUtils.objectToString(objects[2]);
            if (colName1.equals(name1)) {
                col1++;
            } else {
                this.dataTableList.get(colIndex1)[1] = col1;
                colName1 = name1;
                col1 = 1;
                colIndex1 = i;
            }
            if (colName2.equals(name2)) {
                col2++;
            } else {
                this.dataTableList.get(colIndex2)[3] = col2;
                colName2 = name2;
                col2 = 1;
                colIndex2 = i;
            }
        }
        this.dataTableList.get(colIndex1)[1] = col1;
        this.dataTableList.get(colIndex2)[3] = col2;
    }

    public void preExport() {
        if (ObjectUtil.isEmpty(this.dataTableList)) {
            JsfUtil.addErrorMessage("无导出数据！");
            return;
        }
        RequestContext.getCurrentInstance().execute("getDownloadFileClick()");
    }

    public DefaultStreamedContent export() {
        RequestContext context = RequestContext.getCurrentInstance();

        context.execute("showStatus();");
        ExcelExportUtil excelExportUtil;

        //封装列头
        int size = this.rstSimpleCodeList.size();
        int length = size * 2 + 5;
        String[] excelHeaders0 = new String[length];
        String[] excelHeaders1 = new String[length];
        excelHeaders0[0] = "一级指标";
        excelHeaders1[0] = "";
        excelHeaders0[1] = "二级指标";
        excelHeaders1[1] = "";
        excelHeaders0[2] = "考核内容序号";
        excelHeaders1[2] = "";
        excelHeaders0[3] = "参与考核机构数";
        excelHeaders1[3] = "";
        for (int i = 0; i < size; i++) {
            String codeName = this.rstSimpleCodeList.get(i).getCodeName();
            excelHeaders0[4 + i * 2] = codeName;
            excelHeaders1[4 + i * 2] = "机构数";
            excelHeaders0[5 + i * 2] = "";
            excelHeaders1[5 + i * 2] = "率(%)";
        }
        excelHeaders0[length - 1] = "机构名称";
        excelHeaders1[length - 1] = "";
        String[][] excelHeaders = {excelHeaders0, excelHeaders1};

        //添加列头合并单元格
        List<int[]> mergeCellsList = new ArrayList<>();
        mergeCellsList.add(new int[]{1, 2, 0, 0});
        mergeCellsList.add(new int[]{1, 2, 1, 1});
        mergeCellsList.add(new int[]{1, 2, 2, 2});
        mergeCellsList.add(new int[]{1, 2, 3, 3});
        for (int i = 0; i < size; i++) {
            mergeCellsList.add(new int[]{1, 1, 4 + i * 2, 5 + i * 2});
        }
        mergeCellsList.add(new int[]{1, 2, length - 1, length - 1});

        excelExportUtil = new ExcelExportUtil(this.excelTitle, excelHeaders, pakExcelExportDataList(this.dataTableList, mergeCellsList));
        excelExportUtil.setFrozenPaneRowsNum(3);

        for (int[] mergeCells : mergeCellsList) {
            excelExportUtil.addMergeCells(mergeCells[0], mergeCells[1], mergeCells[2], mergeCells[3]);
        }
        Workbook wb = excelExportUtil.exportExcel("");
        if (wb != null) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                String fileName = this.excelTitle + ".xlsx";
                fileName = URLEncoder.encode(fileName, "UTF-8");
                wb.write(baos);
                baos.flush();
                byte[] aa = baos.toByteArray();
                context.execute("hideStatus();");
                return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
            } catch (Exception e) {
                JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            }
        }
        context.execute("hideStatus();");
        return null;
    }

    private List<ExcelExportObject[]> pakExcelExportDataList(List<Object[]> dataList, List<int[]> mergeCellsList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        reDealDataList(dataList);
        List<ExcelExportObject[]> excelExportObjectList = new ArrayList<>();
        int size = this.rstSimpleCodeList.size();
        for (int i = 0, dataListSize = dataList.size(); i < dataListSize; i++) {
            Object[] data = dataList.get(i);
            ExcelExportObject[] objects = new ExcelExportObject[5 + size * 2];
            //一级指标
            objects[0] = new ExcelExportObject(StringUtils.objectToString(data[0]), XSSFCellStyle.ALIGN_CENTER);
            //二级指标
            objects[1] = new ExcelExportObject(StringUtils.objectToString(data[2]), XSSFCellStyle.ALIGN_CENTER);
            //考核内容序号
            objects[2] = new ExcelExportObject(StringUtils.objectToString(data[4]), XSSFCellStyle.ALIGN_CENTER);
            //参与考核机构数
            objects[3] = new ExcelExportObject(StringUtils.objectToString(data[5]), XSSFCellStyle.ALIGN_CENTER);
            List<String> orgNamesList = new ArrayList<>();
            String orgNames2 = "";
            String orgNames3 = "";
            for (int j = 0; j < size; j++) {
                //机构数
                objects[4 + j * 2] = new ExcelExportObject(StringUtils.objectToString(data[6 + j * 3]), XSSFCellStyle.ALIGN_CENTER);
                //率(%)
                objects[5 + j * 2] = new ExcelExportObject(StringUtils.objectToString(data[7 + j * 3]), XSSFCellStyle.ALIGN_CENTER);
                //机构名称
                TsSimpleCode simpleCode = this.rstSimpleCodeList.get(j);
                if ("2".equals(simpleCode.getExtendS1())) {
                    orgNames2 = StringUtils.objectToString(data[8 + j * 3]);
                    orgNames2 = "/".equals(orgNames2) ? "" : orgNames2;
                }
                if ("3".equals(simpleCode.getExtendS1())) {
                    orgNames3 = StringUtils.objectToString(data[8 + j * 3]);
                    orgNames3 = "/".equals(orgNames3) ? "" : orgNames3;
                }
            }
            if (ObjectUtil.isNotEmpty(orgNames2)) {
                orgNamesList.add("基本通过机构：" + orgNames2);
            }
            if (ObjectUtil.isNotEmpty(orgNames3)) {
                orgNamesList.add("不通过机构：" + orgNames3);
            }
            //机构名称
            objects[4 + size * 2] = new ExcelExportObject(StringUtils.list2string(orgNamesList, "\n"));
            //处理单元格合并
            if (!"0".equals(StringUtils.objectToString(data[1]))) {
                try {
                    int endLine = 3 + i + ObjectUtil.convert(Integer.class, data[1]) - 1;
                    mergeCellsList.add(new int[]{3 + i, endLine, 0, 0});
                } catch (Exception ignored) {
                }
            }
            if (!"0".equals(StringUtils.objectToString(data[3]))) {
                try {
                    int endLine = 3 + i + ObjectUtil.convert(Integer.class, data[3]) - 1;
                    mergeCellsList.add(new int[]{3 + i, endLine, 1, 1});
                } catch (Exception ignored) {
                }
            }
            excelExportObjectList.add(objects);
        }
        return excelExportObjectList;
    }

    private void reDealDataList(List<Object[]> dataList) {
        for (Object[] objects : dataList) {

        }
    }

    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public Integer getExamTypeRid() {
        return examTypeRid;
    }

    public void setExamTypeRid(Integer examTypeRid) {
        this.examTypeRid = examTypeRid;
    }

    public List<TsSimpleCode> getExamTypeSimpleCodeList() {
        return examTypeSimpleCodeList;
    }

    public void setExamTypeSimpleCodeList(List<TsSimpleCode> examTypeSimpleCodeList) {
        this.examTypeSimpleCodeList = examTypeSimpleCodeList;
    }

    public Date getSearchExamBeginDate() {
        return searchExamBeginDate;
    }

    public void setSearchExamBeginDate(Date searchExamBeginDate) {
        this.searchExamBeginDate = searchExamBeginDate;
    }

    public Date getSearchExamEndDate() {
        return searchExamEndDate;
    }

    public void setSearchExamEndDate(Date searchExamEndDate) {
        this.searchExamEndDate = searchExamEndDate;
    }

    public List<Integer> getInquiryExamTypeList() {
        return inquiryExamTypeList;
    }

    public void setInquiryExamTypeList(List<Integer> inquiryExamTypeList) {
        this.inquiryExamTypeList = inquiryExamTypeList;
    }

    public Boolean getIsProvUser() {
        return isProvUser;
    }

    public void setProvUser(Boolean provUser) {
        isProvUser = provUser;
    }

    public List<Object[]> getDataTableList() {
        return dataTableList;
    }

    public void setDataTableList(List<Object[]> dataTableList) {
        this.dataTableList = dataTableList;
    }

    public Integer getDataTableListCount() {
        return dataTableListCount;
    }

    public void setDataTableListCount(Integer dataTableListCount) {
        this.dataTableListCount = dataTableListCount;
    }

    public List<TsSimpleCode> getRstSimpleCodeList() {
        return rstSimpleCodeList;
    }

    public void setRstSimpleCodeList(List<TsSimpleCode> rstSimpleCodeList) {
        this.rstSimpleCodeList = rstSimpleCodeList;
    }
}