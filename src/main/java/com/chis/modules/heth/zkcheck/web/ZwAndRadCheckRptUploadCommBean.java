package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zkcheck.entity.TdRadCheckRpt;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckRpt;
import com.chis.modules.heth.zkcheck.service.TdRadCheckRptService;
import com.chis.modules.heth.zkcheck.service.TdZwCheckRptListService;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesEditBean;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>类描述： 放射卫生技术服务机构与职业卫生技术服务机构检测报告上传与职业卫生技术服务机构检测报告审核公共类 </p>
 * @ClassAuthor： pw 2023/6/16
 **/
public abstract class ZwAndRadCheckRptUploadCommBean extends FacesEditBean implements IProcessData  {
    protected final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    protected final TdZwCheckRptListService tdZwCheckRptLisService = SpringContextHolder.getBean(TdZwCheckRptListService.class);
    protected final TdRadCheckRptService tdRadCheckRptService = SpringContextHolder.getBean(TdRadCheckRptService.class);
    /** 查询条件：报告开始日期 */
    protected Date searchReportBeginDate;
    /** 查询条件：报告结束日期 */
    protected Date searchReportEndDate;
    /** 查询条件：检测报告编号 */
    protected String searchRptNo;
    /** 查询条件：选中的状态 */
    protected String[] states;
    /** 查询条件：状态 */
    protected List<SelectItem> stateList = new ArrayList<>();

    /** 修改、详情、删除传递的rid */
    protected Integer rid;
    /** 职业卫生技术服务机构 编辑与详情操作对象 */
    protected TdZwCheckRpt tdZwCheckRpt;
    /** 下载的文件名称 */
    protected String dowloadFileName;
    protected Boolean readOnly;
    /** 退回原因 */
    protected String backRsn;
    /** 检测报告类型 1 放射 */
    protected Integer rptType;
    /** 放射卫生技术服务机构 编辑与详情操作对象 */
    protected TdRadCheckRpt radCheckRpt;

    public ZwAndRadCheckRptUploadCommBean(){
        this.ifSQL=true;
        //报告日期初始化
        this.searchReportEndDate = new Date();
        this.searchReportBeginDate = DateUtils.getYearFirstDay(this.searchReportEndDate);
    }

    @Override
    public void viewInit() {
        this.initCheckRpt();
    }

    /**
     * <p>方法描述： 通过rid查询检测报告上传对象 </p>
     * @MethodAuthor： pw 2023/6/16
     **/
    public void initCheckRpt(){
        if(this.rid==null){
            return;
        }
        if(null == this.rptType || 1 != this.rptType){
            this.tdZwCheckRpt = this.tdZwCheckRptLisService.find(TdZwCheckRpt.class,this.rid);
        }else{
            this.radCheckRpt = this.tdRadCheckRptService.find(TdRadCheckRpt.class, this.rid);
        }
    }

    /**
     * <p>方法描述： 打开退回原因弹出框 </p>
     * @MethodAuthor： pw 2023/6/17
     **/
    public void initDialog() {
        if(this.readOnly) {
            if(null == this.rptType || 1 != this.rptType){
                this.backRsn = null == this.tdZwCheckRpt ? null : this.tdZwCheckRpt.getBackRan();
            }else{
                this.backRsn = null == this.radCheckRpt ? null : this.radCheckRpt.getBackRan();
            }
        } else {
            this.backRsn = null;
        }
    }

    /**
     * <p>方法描述： 退回原因确定 审核用</p>
     * @MethodAuthor： pw 2023/6/17
     **/
    public void returnAction(){}

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwCheckRpt getTdZwCheckRpt() {
        return tdZwCheckRpt;
    }

    public void setTdZwCheckRpt(TdZwCheckRpt tdZwCheckRpt) {
        this.tdZwCheckRpt = tdZwCheckRpt;
    }

    public String getDowloadFileName() {
        String filePath = null;
        if((null == this.rptType || 1 != this.rptType) &&
                StringUtils.isNotBlank(this.tdZwCheckRpt.getSourceFilePath())){
            filePath = this.tdZwCheckRpt.getSourceFilePath();
        }
        if(null != this.rptType && 1 == this.rptType && StringUtils.isNotBlank(this.radCheckRpt.getSourceFilePath())){
            filePath = this.radCheckRpt.getSourceFilePath();
        }
        Integer dotIndex = filePath.lastIndexOf(".");
        String endName = filePath.substring(dotIndex+1);
        return "文审材料附件"+"."+endName;
    }

    public void setDowloadFileName(String dowloadFileName) {
        this.dowloadFileName = dowloadFileName;
    }

    public Boolean getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(Boolean readOnly) {
        this.readOnly = readOnly;
    }

    public String getBackRsn() {
        return backRsn;
    }

    public void setBackRsn(String backRsn) {
        this.backRsn = backRsn;
    }

    public Date getSearchReportBeginDate() {
        return searchReportBeginDate;
    }

    public void setSearchReportBeginDate(Date searchReportBeginDate) {
        this.searchReportBeginDate = searchReportBeginDate;
    }

    public Date getSearchReportEndDate() {
        return searchReportEndDate;
    }

    public void setSearchReportEndDate(Date searchReportEndDate) {
        this.searchReportEndDate = searchReportEndDate;
    }

    public String getSearchRptNo() {
        return searchRptNo;
    }

    public void setSearchRptNo(String searchRptNo) {
        this.searchRptNo = searchRptNo;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public Integer getRptType() {
        return rptType;
    }

    public void setRptType(Integer rptType) {
        this.rptType = rptType;
    }

    public TdRadCheckRpt getRadCheckRpt() {
        return radCheckRpt;
    }

    public void setRadCheckRpt(TdRadCheckRpt radCheckRpt) {
        this.radCheckRpt = radCheckRpt;
    }
}
