package com.chis.modules.heth.zkcheck.service;

import com.chis.modules.heth.zkcheck.entity.TdZwCheckSummary;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>类描述： </p>
 *
 * @ClassAuthor: yzz
 * @date： 2022年08月15日
 **/
@Service
@Transactional(readOnly = false)
public class TdZwCheckSummaryService extends AbstractTemplate {


    /**
     * <p>方法描述：根据rid删除汇总表记录-标删</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-15
     **/
    public void deleteCheckSummaryByRid(Integer rid){
        StringBuffer sql=new StringBuffer();
        sql.append(" update TD_ZW_CHECK_SUMMARY set DEL_MARK=1 where rid=").append(rid);
        em.createNativeQuery(sql.toString()).executeUpdate();
    }
}
