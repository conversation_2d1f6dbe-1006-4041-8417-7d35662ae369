package com.chis.modules.heth.zkcheck.web;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.entity.TbTjCrptIndepend;
import com.chis.modules.heth.comm.json.CrptDialogParamJson;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckRpt;
import com.chis.modules.heth.zkcheck.service.TdZwCheckRptListService;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.UploadedFile;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-03-29
 */
@ManagedBean(name = "tdZwCheckRptListBean")
@ViewScoped
public class TdZwCheckRptListBean extends FacesEditBean implements IProcessData {

    private final TdZwCheckRptListService tdZwCheckRptLisService = SpringContextHolder.getBean(TdZwCheckRptListService.class);
    /**
     *查询条件：报告开始日期
     */
    private Date searchReportBeginDate;
    /**
     *查询条件：报告结束日期
     */
    private Date searchReportEndDate;

    /**
     * 查询条件：单位名称
     */
    private String searchCrptName;

    /**
     *查询条件：检测报告名称（编号）
     */
    private String searchRptNo;

    /**
     * 查询条件：选中的状态
     */
    private String[] states;
    /**
     * 查询条件：审核状态
     */
    private List<SelectItem> stateList = new ArrayList<>();

    /**
     * 列表操作列传值
     */
    private Integer rid;

    /**
     * 实体
     */
    private TdZwCheckRpt tdZwCheckRpt;

    public TdZwCheckRptListBean() {
        this.ifSQL=true;
        //报告日期初始化
        this.searchReportEndDate = new Date();
        this.searchReportBeginDate = DateUtils.getYearFirstDay(this.searchReportEndDate);
        //状态初始化
        stateList=new ArrayList<>();
        stateList.add(new SelectItem("0", "待提交"));
        stateList.add(new SelectItem("1", "已提交"));
        //默认选中“待提交”
        states=new String[]{"0"};
        this.searchAction();
    }


    @Override
    public String[] buildHqls() {

        StringBuilder sql = new StringBuilder();
        sql.append("select T.RID,T.RPT_NO, ");
        sql.append("CASE WHEN T2.ZONE_TYPE >2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME,'_')+1) ELSE T2.FULL_NAME END ZONE_NAME, ");
        sql.append("T.CRPT_NAME,T.LINK_MAN,T.LINK_PHONE,T.RPT_DATE,T.STATE ");
        sql.append("from TD_ZW_CHECK_RPT T ");
        sql.append("left join TS_ZONE T2 on T.ZONE_ID=T2.RID ");
        sql.append(" where T.UNIT_ID=").append(Global.getUser().getTsUnit().getRid());

        //报告日期
        if (null != this.searchReportBeginDate) {
            sql.append("   AND T.RPT_DATE >= TO_DATE(:searchReportBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchReportBeginDate", DateUtils.formatDate(this.searchReportBeginDate) + " 00:00:00");
        }
        if (null != this.searchReportEndDate) {
            sql.append("   AND T.RPT_DATE <= TO_DATE(:searchReportEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchReportEndDate", DateUtils.formatDate(this.searchReportEndDate) + " 23:59:59");
        }
        //单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sql.append("  AND T.CRPT_NAME LIKE :crptName ");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchCrptName).trim()) + "%");
        }
        //检测报告名称（编号）
        if (StringUtils.isNotBlank(this.searchRptNo)) {
            sql.append("  AND T.RPT_NO LIKE :rptNo ");
            this.paramMap.put("rptNo", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchRptNo).trim()) + "%");
        }
        //状态
        if (null!=states && states.length==1) {
            sql.append(" AND T.STATE in (:state)");
            if("0".equals(states[0])){
                this.paramMap.put("state",this.states[0]);
            }else{
                this.paramMap.put("state",StringUtils.string2list("1,2",","));
            }
        }
        String h2 = "SELECT COUNT(*) FROM (" + sql+ ")";
        String h1 = "SELECT * FROM (" + sql + ")AA  order by AA.RPT_NO desc ";
        return new String[] { h1, h2 };
    }

    /**
     * 删除
     * <AUTHOR>
     * @date 2023-03-29
     */
    public void deleteAction(){
        if (this.rid == null) {
            return;
        }
        try {
            this.tdZwCheckRptLisService.deleteByRid(this.rid);
            JsfUtil.addSuccessMessage("删除成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     *  <p>方法描述：初始化企业信息</p>
     * @MethodAuthor yzz
     */
    public void selectCrptList() {
        Map<String, Object> options = MapUtils.produceDialogMap(1080,1050,520,505);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<String>();
        paramList = new ArrayList<String>();
        CrptDialogParamJson json = new CrptDialogParamJson();
        json.setBusType("4");
        json.setIfShowOtherZone(true);
        json.setIfSearchZoneDefault(false);
        json.setAreaValidate(3);
        if(this.tdZwCheckRpt!=null&&StringUtils.isNotBlank(this.tdZwCheckRpt.getCrptName())){
            json.setSearchCrptName(this.tdZwCheckRpt.getCrptName());
        }
        paramList.add(JSON.toJSONString(json));
        paramMap.put("jsonParam", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/crptCommSelectList", options, paramMap);
    }
    /**
     * 选择用人单位后
     *
     * @param event 选择的用人单位
     */
    public void onCrptSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            TbTjCrptIndepend tbTjCrptIndepend = (TbTjCrptIndepend) selectedMap.get("selectCrptIndepend");
            TbTjCrpt tbTjCrpt = tbTjCrptIndepend.getFkByCrptId();
            this.tdZwCheckRpt.setFkByCrptId(tbTjCrpt);
            this.tdZwCheckRpt.setFkByZoneId(tbTjCrpt.getTsZoneByZoneId());
            this.tdZwCheckRpt.setCrptName(tbTjCrpt.getCrptName());
            this.tdZwCheckRpt.setAddress(tbTjCrpt.getAddress());
            this.tdZwCheckRpt.setLinkMan(tbTjCrptIndepend.getLinkman2());
            this.tdZwCheckRpt.setLinkPhone(tbTjCrptIndepend.getLinkphone2());
            this.tdZwCheckRpt.setFkByCrptSizeId(tbTjCrpt.getTsSimpleCodeByCrptSizeId());
            this.tdZwCheckRpt.setCreditCode(tbTjCrpt.getInstitutionCode());
            this.tdZwCheckRpt.setFkByIndusTypeId(tbTjCrpt.getTsSimpleCodeByIndusTypeId());
            this.tdZwCheckRpt.setFkByEconomyId(tbTjCrpt.getTsSimpleCodeByEconomyId());
        }
    }

    /**
     *上传
     */
    public void beforeUpload(){
        RequestContext.getCurrentInstance().execute("PF('FileDialog').show()");
        RequestContext.getCurrentInstance().update("fileDialog");
    }


    @Override
    public void processData(List<?> list) {

    }

    @Override
    public void addInit() {
        tdZwCheckRpt=new TdZwCheckRpt();
        tdZwCheckRpt.setRptDate(new Date());
    }

    @Override
    public void viewInit() {
        if(this.rid==null){
            return;
        }
        tdZwCheckRpt=tdZwCheckRptLisService.find(TdZwCheckRpt.class,this.rid);
    }

    @Override
    public void modInit() {
        if(this.rid==null){
            return;
        }
        tdZwCheckRpt=tdZwCheckRptLisService.find(TdZwCheckRpt.class,this.rid);
    }
    /**
     * @description:  保存
     * <AUTHOR>
     * @date 2023-03-29
     */
    @Override
    public void saveAction() {
        if(this.tdZwCheckRpt==null){
            return;
        }
        boolean bool=false;
        if(tdZwCheckRpt.getFkByCrptId()==null || tdZwCheckRpt.getFkByCrptId().getRid()==null){
            JsfUtil.addErrorMessage("单位名称不能为空！");
            bool=true;
        }
        if(tdZwCheckRpt.getRptDate()==null){
            JsfUtil.addErrorMessage("报告日期不能为空！");
            bool=true;
        }
        if(StringUtils.isBlank(tdZwCheckRpt.getRptNo())){
            JsfUtil.addErrorMessage("检测报告名称（编号）不能为空！");
            bool=true;
        }
        if(StringUtils.isNotBlank(tdZwCheckRpt.getRptNo()) && tdZwCheckRpt.getRptNo().length()>50){
            JsfUtil.addErrorMessage("检测报告名称（编号）的长度不能超过50！");
            bool=true;
        }
        if(bool){
            return;
        }
        try{
            tdZwCheckRpt.setState(0);
            tdZwCheckRptLisService.saveOrUpdateCheckRpt(tdZwCheckRpt);
            JsfUtil.addSuccessMessage("暂存成功！");
            this.searchAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("暂存失败！");
        }
    }

    /**
     * @description:  提交前验证
     * <AUTHOR>
     * @date 2023-03-29
     */
    public void beforeSubmit() {
        if(this.tdZwCheckRpt==null){
            return;
        }
        boolean bool=false;
        if(tdZwCheckRpt.getFkByCrptId()==null || tdZwCheckRpt.getFkByCrptId().getRid()==null){
            JsfUtil.addErrorMessage("单位名称不能为空！");
            bool=true;
        }
        if(StringUtils.isBlank(tdZwCheckRpt.getWorkName())){
            JsfUtil.addErrorMessage("工作场所名称不能为空！");
            bool=true;
        }
        if(StringUtils.isNotBlank(tdZwCheckRpt.getWorkName()) && tdZwCheckRpt.getWorkName().length()>500){
            JsfUtil.addErrorMessage("工作场所名称长度不能超过500！");
            bool=true;
        }
        if(tdZwCheckRpt.getRptDate()==null){
            JsfUtil.addErrorMessage("报告日期不能为空！");
            bool=true;
        }
        if(StringUtils.isBlank(tdZwCheckRpt.getRptNo())){
            JsfUtil.addErrorMessage("检测报告名称（编号）不能为空！");
            bool=true;
        }
        if(StringUtils.isNotBlank(tdZwCheckRpt.getRptNo()) && tdZwCheckRpt.getRptNo().length()>50){
            JsfUtil.addErrorMessage("检测报告名称（编号）的长度不能超过50！");
            bool=true;
        }
        if(StringUtils.isBlank(tdZwCheckRpt.getFilePath())){
            JsfUtil.addErrorMessage("检测报告附件必须上传！");
            bool=true;
        }
        if(bool){
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show()");
    }


    /**
     * @description:  提交
     * <AUTHOR>
     * @date 2023-03-29
     */
    public void submitAction() {
        try{
            tdZwCheckRpt.setState(1);
            tdZwCheckRptLisService.saveOrUpdateCheckRpt(tdZwCheckRpt);
            RequestContext.getCurrentInstance().update("tabView");
            JsfUtil.addSuccessMessage("提交成功！");
            this.searchAction();
            this.rid=tdZwCheckRpt.getRid();
            this.viewInitAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
     * 文书上传
     */
    public void fileUpload(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            try {
                String errorMsg = FileUtils.veryFile(file.getInputstream(), file.getContentType(), file.getFileName(), "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    RequestContext.getCurrentInstance().update("tabView:editForm:fileUpload");
                    return;
                }
                // 文件名称
                String fileName = file.getFileName();
                String uuid = java.util.UUID.randomUUID().toString().replaceAll("-", "");
                String path = JsfUtil.getAbsolutePath();
                String relativePath = "heth/zkcheckjs/checkRpt/" + uuid + fileName.substring(fileName.lastIndexOf("."));
                // 文件路径
                String filePath = path + relativePath;
                // 文件路径
                this.tdZwCheckRpt.setFilePath(relativePath);
                FileUtils.copyFile(filePath, file.getInputstream());
                RequestContext currentInstance = RequestContext.getCurrentInstance();
                currentInstance.execute("PF('FileDialog').hide();");
                currentInstance.update("tabView:editForm:rptPanel");
                JsfUtil.addSuccessMessage("上传成功！");

            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("上传失败！");
                throw new RuntimeException(e);
            }
        }
    }

   /**
   * @description: 文书删除
   * <AUTHOR>
   * @date 2023-03-29
   */
    public void delRptFile() {
        try {
            tdZwCheckRpt.setFilePath(null);
            JsfUtil.addSuccessMessage("删除成功！");
            RequestContext.getCurrentInstance().update("tabView:editForm:rptPanel");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    public Date getSearchReportBeginDate() {
        return searchReportBeginDate;
    }

    public void setSearchReportBeginDate(Date searchReportBeginDate) {
        this.searchReportBeginDate = searchReportBeginDate;
    }

    public Date getSearchReportEndDate() {
        return searchReportEndDate;
    }

    public void setSearchReportEndDate(Date searchReportEndDate) {
        this.searchReportEndDate = searchReportEndDate;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchRptNo() {
        return searchRptNo;
    }

    public void setSearchRptNo(String searchRptNo) {
        this.searchRptNo = searchRptNo;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwCheckRpt getTdZwCheckRpt() {
        return tdZwCheckRpt;
    }

    public void setTdZwCheckRpt(TdZwCheckRpt tdZwCheckRpt) {
        this.tdZwCheckRpt = tdZwCheckRpt;
    }
}
