package com.chis.modules.heth.zkcheck.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.chis.modules.heth.zkcheck.entity.TdZwZkCheckSummary;
import com.chis.modules.system.service.AbstractTemplate;

@Service
@Transactional(readOnly = false)
public class TdZwZkCheckSummaryService extends AbstractTemplate{
	/**
 	 * <p>方法描述：保存考核汇总信息</p>
 	 * @MethodAuthor qrr,2021年9月19日,saveTdZwZkCheckSummary
	 * */
	public void saveTdZwZkCheckSummary(Integer mainId,List<TdZwZkCheckSummary> checkSummaries) {
		if (null==mainId) {
			return;
		}
		this.executeSql("delete from TD_ZW_ZK_CHECK_SUMMARY where MAIN_ID ="+mainId, null);
		if (!CollectionUtils.isEmpty(checkSummaries)) {
			for (TdZwZkCheckSummary t : checkSummaries) {
				this.upsertEntity(t);
			}
		}
	}
}
