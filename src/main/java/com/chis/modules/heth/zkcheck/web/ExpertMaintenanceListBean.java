package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.rptvo.SrvorgPsnByZoneVo;
import com.chis.modules.heth.zkcheck.entity.TdZwExpert;
import com.chis.modules.heth.zkcheck.service.ExpertMaintenanceListService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * <AUTHOR>
 * @description:
 */
@ManagedBean(name = "expertMaintenanceListBean")
@ViewScoped
public class ExpertMaintenanceListBean extends FacesEditBean {

    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private ExpertMaintenanceListService expertMaintenanceListService = SpringContextHolder.getBean(ExpertMaintenanceListService.class);
    /***查询条件-地区*/
    protected List<TsZone> zoneList;
    protected String searchZoneGb;
    protected String searchZoneName;

    /***查询条件-单位名称*/
    protected String searchUnitName;
    /***查询条件-姓名*/
    protected String searchUserName;
    /***查询条件-身份证号*/
    protected String searchIDC;

    /**
     * 当前需要添加的专家
     */
    private TdZwExpert tdZwExpert;

    /**
     * 职称码表
     */
    private List<TsSimpleCode> jobTitleList;
    private Map<Integer, TsSimpleCode> jobTitleMap;
    /**
     * 当前操作记录的rid
     */
    private Integer rid;
    /**
     * 职称 是否选择无职称
     */
    private boolean ifNotJobTitle;

    /**
     * 保存标记 1：保存 2：连续保存
     */
    private Integer saveMark;

    /**
     * 数据来源
     */
    private Integer dataSource;

    /**
     * 1：添加，  2：修改
     */
    private Integer type;


    public ExpertMaintenanceListBean() {
        this.ifSQL = true;
        initZone();
        initSimp();
        initExpert();
        this.searchAction();
    }

    /**
     * <p>Description：初始化码表 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void initSimp() {
        this.jobTitleList = commService.findLevelSimpleCodesByTypeId("2003");
        this.jobTitleMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(jobTitleList)) {
            for (TsSimpleCode tsSimpleCode : jobTitleList) {
                jobTitleMap.put(tsSimpleCode.getRid(), tsSimpleCode);
            }
        }
    }

    /**
     * <p>Description：地区初始化</p>
     * <p>Author：yzz 2024-07-03 </p>
     */
    private void initZone() {
        // 地区信息初始化
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(null, true, "", "");
    }

    /**
     * <p>Description：初始化专家实体 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void initExpert() {
        this.tdZwExpert = new TdZwExpert();
        this.tdZwExpert.setFkByUnitId(new TsUnit());
        this.tdZwExpert.setFkByTitleId(new TsSimpleCode());
    }


    @Override
    public void addInit() {

    }
    @Override
    public void viewInit() {

    }
    @Override
    public void modInit() {

    }
    @Override
    public void saveAction() {

    }
    @Override
    public String[] buildHqls() {
        StringBuilder sql = new StringBuilder();
        sql.append(" select  T.RID        as                                                                                          A0, ");
        sql.append("         CASE WHEN T1.ZONE_TYPE > 2 THEN SUBSTR(T1.FULL_NAME, INSTR(T1.FULL_NAME, '_') + 1) ELSE T1.FULL_NAME END A1, ");
        sql.append("         T3.UNITNAME  as                                                                                          A2, ");
        sql.append("         T.USER_NAME  as                                                                                          A3, ");
        sql.append("         T2.CODE_NAME as                                                                                          A4, ");
        sql.append("         T.FILE_PATH  as                                                                                          A5, ");
        sql.append("         T1.ZONE_GB   as                                                                                          A6 ");
        sql.append(" from TD_ZW_EXPERT T ");
        sql.append(" left join TS_UNIT T3 on T.UNIT_ID = T3.RID ");
        sql.append(" left join TS_ZONE T1 on T3.ZONE_ID = T1.RID ");
        sql.append(" left join TS_SIMPLE_CODE T2 on T.TITLE_ID = T2.RID ");
        sql.append(" where nvl(T.DEL_MARK, 0) = 0 ");
        // 地区
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            sql.append(" AND T1.ZONE_GB LIKE :zoneGb escape '\\\' ");
            paramMap.put("zoneGb", ZoneUtil.zoneSelect(this.searchZoneGb.trim()) + "%");
        }
        // 单位名称
        if (StringUtils.isNotBlank(this.searchUnitName)) {
            sql.append(" AND T3.UNITNAME LIKE :unitName  escape '\\\'");
            this.paramMap.put("unitName", "%" + StringUtils.convertBFH(this.searchUnitName.trim()) + "%");
        }
        // 姓名
        if (StringUtils.isNotBlank(this.searchUserName)) {
            sql.append(" AND T.USER_NAME LIKE :userName  escape '\\\'");
            this.paramMap.put("userName", "%" + StringUtils.convertBFH(this.searchUserName.trim()) + "%");
        }
        // 身份证号
        if (StringUtils.isNotBlank(this.searchIDC)) {
            sql.append(" AND T.IDC = :idc ");
            this.paramMap.put("idc", searchIDC);
        }
        String h2 = "SELECT COUNT(*) FROM (" + sql + ")";
        String h1 = "SELECT * FROM (" + sql + ")AA  ORDER BY AA.A6,AA.A2,AA.A3 ";
        return new String[]{h1, h2};
    }

    /**
     * <p>Description：添加专家弹框 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void addExpertAction() {
        // 专家初始化
        initExpert();
        this.ifNotJobTitle = true;
        RequestContext.getCurrentInstance().execute("PF('AddExpertDialog').show();");
    }

    /**
     * <p>Description：删除专家 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void delExpertAction() {
        if (this.rid == null) {
            return;
        }
        try {
            this.expertMaintenanceListService.updateFieldByValue("DEL_MARK", "1", this.rid, Integer.class);
            this.searchAction();
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * <p>Description：修改专家 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void modExpertAction() {
        if (this.rid == null) {
            return;
        }
        this.tdZwExpert = this.commService.find(TdZwExpert.class, this.rid);
        this.tdZwExpert.setUnitName(this.tdZwExpert.getFkByUnitId().getUnitname());
        this.ifNotJobTitle = false;
        if (this.tdZwExpert.getFkByTitleId() != null && "1".equals(this.tdZwExpert.getFkByTitleId().getExtendS1())) {
            this.ifNotJobTitle = true;
        }
        RequestContext.getCurrentInstance().execute("PF('AddExpertDialog').show();");
    }


    /**
     * <p>Description： 专家保存</p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void saveExpertAction() {
        boolean flag = false;
        if (this.tdZwExpert.getFkByUnitId()==null || this.tdZwExpert.getFkByUnitId().getRid()==null) {
            JsfUtil.addErrorMessage("单位名称不能为空！");
            flag = true;
        }
        if (StringUtils.isBlank(this.tdZwExpert.getUserName())) {
            JsfUtil.addErrorMessage("姓名不能为空！");
            flag = true;
        }
        if (StringUtils.isBlank(this.tdZwExpert.getIdc())) {
            JsfUtil.addErrorMessage("身份证号不能为空！");
            flag = true;
        } else if (StringUtils.isNotBlank(IdcUtils.checkIDC(this.tdZwExpert.getIdc()))) {
            JsfUtil.addErrorMessage("身份证号格式不正确！");
            flag = true;
        }
        if (StringUtils.isBlank(this.tdZwExpert.getLinkPhone())) {
            JsfUtil.addErrorMessage("手机号码不能为空！");
            flag = true;
        } else if (!StringUtils.vertyMobilePhone(this.tdZwExpert.getLinkPhone())) {
            JsfUtil.addErrorMessage("手机号码格式不正确！");
            flag = true;
        }
        if (this.tdZwExpert.getFkByTitleId() == null || this.tdZwExpert.getFkByTitleId().getRid() == null) {
            JsfUtil.addErrorMessage("请选择职称！");
            flag = true;
        }
        if (!this.ifNotJobTitle && StringUtils.isBlank(this.tdZwExpert.getFilePath())) {
            JsfUtil.addErrorMessage("请上传附件！");
            flag = true;
        }
        // 验证专家库内唯一
        boolean ifUnique = StringUtils.isNotBlank(this.tdZwExpert.getIdc())
                && this.expertMaintenanceListService.findExpertByIDC(this.tdZwExpert.getIdc(), this.tdZwExpert.getRid()) > 0;
        if (ifUnique) {
            JsfUtil.addErrorMessage("身份证号已存在！");
            flag = true;
        }
        if (flag) {
            return;
        }
        try {
            this.tdZwExpert.setDelMark(0);
            this.tdZwExpert.setDataSource(this.dataSource);
            this.expertMaintenanceListService.saveExpert(this.tdZwExpert);
            this.searchAction();
            if (new Integer("1").equals(this.saveMark)) {
                RequestContext.getCurrentInstance().execute("PF('AddExpertDialog').hide();");
            } else if (new Integer("2").equals(this.saveMark)) {
                this.initExpert();
                this.ifNotJobTitle = false;
                RequestContext.getCurrentInstance().update("tabView:mainForm:expertInfo");
            }
            RequestContext.getCurrentInstance().update("tabView:mainForm:dataTable");
            JsfUtil.addSuccessMessage("保存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
     * <p>Description：身份证号失焦事件，获取性别 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void findSexByIdc() {
        if (StringUtils.isBlank(this.tdZwExpert.getIdc())) {
            return;
        }
        if (StringUtils.isNotBlank(IdcUtils.checkIDC(this.tdZwExpert.getIdc()))) {
            JsfUtil.addErrorMessage("身份证号格式不正确！");
            return;
        }
        this.tdZwExpert.setSex(IdcUtils.getSex(this.tdZwExpert.getIdc()));
    }

    /**
     * <p>Description：上传附件 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void fileUpload(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            try {
                String contentType = file.getContentType().toLowerCase();
                String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, file.getFileName(), "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    return;
                }
                String fileName = file.getFileName();// 文件名称
                String uuid = java.util.UUID.randomUUID().toString().replaceAll("-", "");
                String relativePath = "heth/zkCheckJs/expert" + uuid + fileName.substring(fileName.lastIndexOf("."));
                // 文件路径
                String filePath = JsfUtil.getAbsolutePath() + relativePath;
                FileUtils.copyFile(filePath, file.getInputstream());
                RequestContext.getCurrentInstance().execute("PF('FileDialog').hide()");
                this.tdZwExpert.setFilePath(relativePath);
                RequestContext.getCurrentInstance().update("tabView:mainForm:fileUploadPanel");
                RequestContext.getCurrentInstance().update("tabView:mainForm:fileDialog");
                JsfUtil.addSuccessMessage("上传成功！");
            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("上传失败！");
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * <p>Description：删除附件 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void delFilePath() {
        this.tdZwExpert.setFilePath(null);
        RequestContext.getCurrentInstance().update("tabView:mainForm:fileUploadPanel");
    }


    /**
     * <p>Description：选择单位 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void selectCrptList() {
        Map<String, Object> options = MapUtils.produceDialogMap(870, 835, 505, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/dialog/selectOrgUnitList", options, paramMap);
    }

    /**
     * <p>Description： 选择单位后事件</p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void onCrptSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null == selectedMap || selectedMap.isEmpty()) {
            return;
        }
        Object[] selectOrgs = (Object[]) selectedMap.get("selectPros");
        if (null != selectOrgs) {
            this.tdZwExpert.setFkByUnitId(new TsUnit(Integer.parseInt(selectOrgs[0].toString())));
            this.tdZwExpert.setUnitName(selectOrgs[3].toString());
        }
    }

    /**
     * <p>Description：选择专家 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void addPsnAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(870, 835, 505, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        // 查询已选择的专家
        List<Object> expertList = this.expertMaintenanceListService.findExistsExperts();
        if (!CollectionUtils.isEmpty(expertList)) {
            Set<String> rids = new HashSet<>();
            for (Object idc : expertList) {
                rids.add(idc.toString());
            }
            paramList.add(StringUtils.collection2string(rids, ","));
            paramMap.put("selectIdCards", paramList);
        }
        // 资质类型
        paramList = new ArrayList<>();
        paramList.add("1");
        paramMap.put("type", paramList);
        // 弹框标题
        paramList = new ArrayList<>();
        paramList.add("选择专家");
        paramMap.put("title", paramList);
        // 查询管辖地区数据
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        paramList = new ArrayList<>();
        paramList.add(ZoneUtil.zoneSelect(tsZone.getZoneGb()));
        paramMap.put("searchZoneCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/dialog/selectOrgPsnByZoneList", options, paramMap);
    }


    /**
     * <p>Description：选择专家后事件 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void onPsnSel(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (selectedMap == null || selectedMap.isEmpty()) {
            return;
        }
        List<SrvorgPsnByZoneVo> list = CollectionUtil.castList(SrvorgPsnByZoneVo.class, selectedMap.get("selectPros"));
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<TdZwExpert> tdZwExperts = new ArrayList<>();
        for (SrvorgPsnByZoneVo t : list) {
            TdZwExpert tdZwExpert = new TdZwExpert();
            tdZwExpert.setFkByUnitId(new TsUnit(t.getUnitRid()));
            tdZwExpert.setUnitName(t.getUnitName());
            tdZwExpert.setUserName(StringUtils.isNotBlank(t.getEmpName()) ? t.getEmpName() : null);
            tdZwExpert.setIdc(StringUtils.isNotBlank(t.getIdcCard()) ? t.getIdcCard() : null);
            tdZwExpert.setSex(StringUtils.isNotBlank(t.getSex()) ? "男".equals(t.getSex()) ? Integer.valueOf(1) : "女".equals(t.getSex()) ? 2 : null : null);
            tdZwExpert.setLinkPhone(StringUtils.isNotBlank(t.getMobileNo()) ? t.getMobileNo() : null);
            tdZwExpert.setFkByTitleId(t.getTitleRid() == null ? null : new TsSimpleCode(t.getTitleRid()));
            tdZwExpert.setFilePath(StringUtils.isNotBlank(t.getTitleCentPath()) ? t.getTitleCentPath() : null);
            tdZwExpert.setDataSource(1);
            tdZwExpert.setDelMark(0);
            tdZwExperts.add(tdZwExpert);
            this.expertMaintenanceListService.preEntity(tdZwExpert);
        }
        try {
            this.expertMaintenanceListService.saveBatchExperts(tdZwExperts);
            this.searchAction();
            RequestContext.getCurrentInstance().update("tabView:mainForm:dataTable");
            JsfUtil.addSuccessMessage("保存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }


    /**
     * <p>Description：职称change事件 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void changeTitle() {
        this.ifNotJobTitle = false;
        if (this.jobTitleMap.isEmpty() || this.tdZwExpert.getFkByTitleId() == null || this.tdZwExpert.getFkByTitleId().getRid() == null) {
            return;
        }
        if (this.jobTitleMap.containsKey(this.tdZwExpert.getFkByTitleId().getRid())
                && "1".equals(this.jobTitleMap.get(this.tdZwExpert.getFkByTitleId().getRid()).getExtendS1())) {
            this.ifNotJobTitle = true;
        }
    }

    /**
     * <p>Description：清空地区 </p>
     * <p>Author： yzz 2024-10-30 </p>
     */
    public void clearUnit() {
        this.searchZoneName = null;
        this.searchZoneGb = null;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }
    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }
    public String getSearchZoneGb() {
        return searchZoneGb;
    }
    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }
    public String getSearchZoneName() {
        return searchZoneName;
    }
    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }
    public String getSearchUnitName() {
        return searchUnitName;
    }
    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }
    public String getSearchUserName() {
        return searchUserName;
    }
    public void setSearchUserName(String searchUserName) {
        this.searchUserName = searchUserName;
    }
    public String getSearchIDC() {
        return searchIDC;
    }
    public void setSearchIDC(String searchIDC) {
        this.searchIDC = searchIDC;
    }

    public TdZwExpert getTdZwExpert() {
        return tdZwExpert;
    }
    public void setTdZwExpert(TdZwExpert tdZwExpert) {
        this.tdZwExpert = tdZwExpert;
    }
    public List<TsSimpleCode> getJobTitleList() {
        return jobTitleList;
    }
    public void setJobTitleList(List<TsSimpleCode> jobTitleList) {
        this.jobTitleList = jobTitleList;
    }

    public Integer getRid() {
        return rid;
    }
    public void setRid(Integer rid) {
        this.rid = rid;
    }
    public boolean getIfNotJobTitle() {
        return ifNotJobTitle;
    }
    public void setIfNotJobTitle(boolean ifNotJobTitle) {
        this.ifNotJobTitle = ifNotJobTitle;
    }

    public Integer getSaveMark() {
        return saveMark;
    }
    public void setSaveMark(Integer saveMark) {
        this.saveMark = saveMark;
    }

    public Integer getDataSource() {
        return dataSource;
    }
    public void setDataSource(Integer dataSource) {
        this.dataSource = dataSource;
    }

    public Integer getType() {
        return type;
    }
    public void setType(Integer type) {
        this.type = type;
    }
}
