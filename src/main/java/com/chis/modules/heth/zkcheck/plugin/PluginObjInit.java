package com.chis.modules.heth.zkcheck.plugin;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.modules.system.entity.TsCodeRule;
import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsMenu;
import com.chis.modules.system.entity.TsMenuBtn;
import com.chis.modules.system.entity.TsQuartz;
import com.chis.modules.system.entity.TsRpt;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsSystemParam;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.init.PluginInit;

@Component(value = "PluginObjInit_Heth_203")
@Transactional(readOnly = false)
public class PluginObjInit extends PluginInit {

    @Override
    public void run() {
        //super.initDataStructureByXml();
        super.run();
        initTsMenuBtn();
        initReportTemplates();
    }
    
    /**
	 * 初始化菜单按钮
	 * 
	 * <AUTHOR>
	 * @createDate 2020-04-26
	 */
	private void initTsMenuBtn() {
		Set<TsMenuBtn> set = HethMenuBtnPluginObj.menuSet;
		if (null != set && set.size() > 0) {
			for (TsMenuBtn t : set) {
				List<TsQuartz> list = em.createNamedQuery("TsMenuBtn.findByCode")
						.setParameter("buttoncode", t.getBtnCode()).getResultList();
				if (null == list || list.size() == 0) {
					TsMenu tsMenu = (TsMenu) em.createNamedQuery("TsMenu.findByMenuEn")
							.setParameter("menuEn", t.getMenucode()).getSingleResult();
					if (tsMenu != null) {
						t.setTsMenu(tsMenu);
						t.setLevelno("1");
						t.setIfReveal((short) 1);
						pluginUpdateService.save(t);
					}
				}
			}
		}
	}

    private void initReportTemplates() {
        Set<TsRpt> set = ReportTemplatePluginObj.dataSet;
        if (null != set && set.size() > 0) {
            for (TsRpt t : set) {
                List<TsRpt> list = em.createNamedQuery("TsRpt.findByCode").setParameter("rptCod", t.getRptCod())
                        .getResultList();
                if (null == list || list.size() == 0) {
                    t.setParamType(t.getSystemType().getTypeNo().intValue());
                    pluginUpdateService.save(t);
                }
            }
        }
    }
    
    @Override
    public List<String> buildDataStructurePlugin() {
        List<String> result = new ArrayList<>();
        return result;
    }

    @Override
    public Set<TsMenu> buildMenuPlugin() {
        return HethMenuPluginObj.menuSet;
    }

    @Override
    public Set<TsCodeRule> buildCodeRulePlugin() {
        return HethCodeRulePluginObj.ruleSet;
    }

    @Override
    public Set<TsSystemParam> buildSystemParamPlugin() {
        return new HashSet<TsSystemParam>();
    }

    @Override
    public Set<TsSimpleCode> buildCodePlugin() {
        return new HashSet<TsSimpleCode>();
    }

    @Override
    public Set<TsCodeType> buildCodeTypePlugin() {
        return  HethTsCodePluginObj.codeTypeSet;
    }

    @Override
    public SystemType buildSystemType() {
        return SystemType.ZK_CHECK;
    }
}
