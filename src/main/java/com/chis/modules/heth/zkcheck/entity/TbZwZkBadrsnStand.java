package com.chis.modules.heth.zkcheck.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2021-6-26
 */
@Entity
@Table(name = "TB_ZW_ZK_BADRSN_STAND")
@SequenceGenerator(name = "TbZwZkBadrsnStand", sequenceName = "TB_ZW_ZK_BADRSN_STAND_SEQ", allocationSize = 1)
public class TbZwZkBadrsnStand implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private Integer checkType;
    private TsSimpleCode fkByCheckTypeId;
    private String checkName;
    private Integer stateMark;
    private Integer xh;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    private String checkXh;

    private String specialFlag;
    private String specialFlagName;
    /**
     * 选中特殊标记码表rid
     */
    private Integer selSpFlagId;

    private Integer year;

    public TbZwZkBadrsnStand() {
    }

    public TbZwZkBadrsnStand(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbZwZkBadrsnStand")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Column(name = "CHECK_TYPE")
    public Integer getCheckType() {
        return checkType;
    }

    public void setCheckType(Integer checkType) {
        this.checkType = checkType;
    }

    @ManyToOne
    @JoinColumn(name = "CHECK_TYPE_ID")
    public TsSimpleCode getFkByCheckTypeId() {
        return fkByCheckTypeId;
    }

    public void setFkByCheckTypeId(TsSimpleCode fkByCheckTypeId) {
        this.fkByCheckTypeId = fkByCheckTypeId;
    }

    @Column(name = "CHECK_NAME")
    public String getCheckName() {
        return checkName;
    }

    public void setCheckName(String checkName) {
        this.checkName = checkName;
    }

    @Column(name = "STATE_MARK")
    public Integer getStateMark() {
        return stateMark;
    }

    public void setStateMark(Integer stateMark) {
        this.stateMark = stateMark;
    }

    @Column(name = "XH")
    public Integer getXh() {
        return xh;
    }

    public void setXh(Integer xh) {
        this.xh = xh;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Column(name = "CHECK_XH")
    public String getCheckXh() {
        return checkXh;
    }
    public void setCheckXh(String checkXh) {
        this.checkXh = checkXh;
    }
    @Column(name = "SPECIAL_FLAG")
    public String getSpecialFlag() {
        return specialFlag;
    }
    public void setSpecialFlag(String specialFlag) {
        this.specialFlag = specialFlag;
    }
    @Column(name = "YEAR")
    public Integer getYear() {
        return year;
    }
    public void setYear(Integer year) {
        this.year = year;
    }

    @Transient
    public Integer getSelSpFlagId() {
        return selSpFlagId;
    }
    public void setSelSpFlagId(Integer selSpFlagId) {
        this.selSpFlagId = selSpFlagId;
    }

    @Transient
    public String getSpecialFlagName() {
        return specialFlagName;
    }
    public void setSpecialFlagName(String specialFlagName) {
        this.specialFlagName = specialFlagName;
    }
}