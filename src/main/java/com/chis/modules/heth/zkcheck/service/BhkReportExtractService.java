package com.chis.modules.heth.zkcheck.service;

import cn.hutool.core.convert.Convert;
import com.chis.common.utils.*;
import com.chis.modules.heth.zkcheck.entity.*;
import com.chis.modules.heth.zkcheck.logic.UserPsnInfoDTO;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Transactional(readOnly = false)
public class BhkReportExtractService extends AbstractTemplate {


    /**
     * <p>Description  根据rid删除个案抽取表以及子表 </p>
     * <p>Author： yzz 2024-06-29 </p>
     */
    public void delExtractBhkById(Integer rid) {
        //删除 接触危害因素
        StringBuffer sql = new StringBuffer();
        sql.append("delete from TD_ZK_BHK_TCH_BADRSNS where MAIN_ID = ").append(rid);
        executeSql(sql.toString(), null);
        //删除 抽取危害因素
        sql = new StringBuffer();
        sql.append("delete from TD_ZK_BHK_BADRSNS where MAIN_ID = ").append(rid);
        executeSql(sql.toString(), null);
        //删除个案抽取
        sql = new StringBuffer();
        sql.append("delete from TD_ZK_EXTRACT_BHK where rid = ").append(rid);
        executeSql(sql.toString(), null);
    }


    /**
     * <p>方法描述：根据用户查询抽取规则</p>
     *
     * @MethodAuthor hsj 2024-07-02 11:48
     */
    @Transactional(readOnly = true)
    public List<TdZkExtractRule> findTdZkExtractRuleByUserId(Integer userId) {
        List<TdZkExtractRule> list = new ArrayList<>();
        if (ObjectUtil.isNull(userId)) {
            return list;
        }
        StringBuffer hql = new StringBuffer();
        hql.append(" SELECT t FROM TdZkExtractRule t WHERE t.fkByUserId.rid = ").append(userId);
        hql.append(" ORDER BY t.fkByExtractTypeId.num,t.fkByExtractTypeId.codeNo,t.priority,t.rid");
        list = this.findDataByHqlNoPage(hql.toString(), null);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        for (TdZkExtractRule rule : list) {
           this.dealRule(rule);
        }
        return list;
    }

    /**
     *  <p>方法描述：单个规则处理</p>
     * @MethodAuthor hsj 2024-07-03 14:41
     */
    private void dealRule(TdZkExtractRule rule) {
        if(null == rule || null == rule.getRid()){
            return;
        }
        rule.getChestResultList().size();
        List<TdZkChestResult> chestResultList = rule.getChestResultList();
        if (!CollectionUtils.isEmpty(chestResultList)) {
            SortUtil.sortIntSingleByField(chestResultList,TdZkChestResult.class,"getChestResult");
            String[] chestResultIds = new String[chestResultList.size()];
            List<String> names = new ArrayList<>();
            int i = 0;
            for (TdZkChestResult chestResult : chestResultList) {
                String result = chestResult.getChestResult().toString();
                chestResultIds[i] = result;
                switch (result) {
                    case "0":
                        names.add("未见异常");
                        break;
                    case "1":
                        names.add("尘肺样改变");
                        break;
                    case "2":
                        names.add("其他异常");
                        break;
                    default:
                        break;
                }
                i++;
            }
            rule.setChestResultIds(chestResultIds);
            rule.setChestResultName(StringUtils.list2string(names, "，"));
        }
        rule.getBadrsnsList().size();
        List<TdZkBadrsns> badrsnsList = rule.getBadrsnsList();
        if (!CollectionUtils.isEmpty(badrsnsList)) {
            SortUtil.sortCodeByField(badrsnsList,TdZkBadrsns.class,"getFkByBadrsnId");
            List<String> ids = new ArrayList<>();
            List<String> names = new ArrayList<>();
            for (TdZkBadrsns badrsns : badrsnsList) {
                ids.add(Convert.toStr(badrsns.getFkByBadrsnId().getRid()));
                names.add(badrsns.getFkByBadrsnId().getCodeName());
            }
            rule.setBadrsnsIds(StringUtils.list2string(ids, ","));
            rule.setBadrsnsName(StringUtils.list2string(names, "，"));
        }
        rule.getBadrsnsRstList().size();
        List<TdZkBadrsnsRst> badrsnsRstList = rule.getBadrsnsRstList();
        if (!CollectionUtils.isEmpty(badrsnsRstList)) {
            SortUtil.sortCodeByField(badrsnsRstList,TdZkBadrsnsRst.class,"getFkByExamConclusionId");
            List<String> ids = new ArrayList<>();
            List<String> names = new ArrayList<>();
            for (TdZkBadrsnsRst badrsnsRst : badrsnsRstList) {
                ids.add(Convert.toStr(badrsnsRst.getFkByExamConclusionId().getRid()));
                names.add(badrsnsRst.getFkByExamConclusionId().getCodeName());
            }
            rule.setBadrsnsRstIds(StringUtils.list2string(ids, ","));
            rule.setBadrsnsRstName(StringUtils.list2string(names, "，"));
        }
        rule.getOnguardStateList().size();
        List<TdZkOnguardState> onguardStateList = rule.getOnguardStateList();
        if (!CollectionUtils.isEmpty(onguardStateList)) {
            SortUtil.sortCodeByField(onguardStateList,TdZkOnguardState.class,"getFkByOnguardStateid");
            List<String> ids = new ArrayList<>();
            List<String> names = new ArrayList<>();
            for (TdZkOnguardState onguardState : onguardStateList) {
                ids.add(Convert.toStr(onguardState.getFkByOnguardStateid().getRid()));
                names.add(onguardState.getFkByOnguardStateid().getCodeName());
            }
            rule.setOnguardStateIds(StringUtils.list2string(ids, ","));
            rule.setOnguardStateName(StringUtils.list2string(names, "，"));
        }

        rule.setExtractTypeId(rule.getFkByExtractTypeId().getRid());
        if (null != rule.getFkByHearingRstId() && null != rule.getFkByHearingRstId().getRid()) {
            rule.setHearingRstId(rule.getFkByHearingRstId().getRid());
            rule.setHearingRstName(rule.getFkByHearingRstId().getCodeName());
        }
    }

    /**
     * <p>方法描述：保存</p>
     *
     * @MethodAuthor hsj 2024-07-02 16:15
     */
    @Transactional(readOnly = false)
    public void saveExtractRule(TdZkExtractRule extractRule) {
        //清空相关子表信息点
        if (null != extractRule.getRid()) {
            StringBuffer sql = new StringBuffer();
            sql.append("DELETE TD_ZK_ONGUARD_STATE WHERE MAIN_ID = ").append(extractRule.getRid());
            this.executeSql(sql.toString(), null);
            sql = new StringBuffer();
            sql.append("DELETE TD_ZK_BADRSNS WHERE MAIN_ID = ").append(extractRule.getRid());
            this.executeSql(sql.toString(), null);
            sql = new StringBuffer();
            sql.append("DELETE TD_ZK_BADRSNS_RST WHERE MAIN_ID = ").append(extractRule.getRid());
            this.executeSql(sql.toString(), null);
            sql = new StringBuffer();
            sql.append("DELETE TD_ZK_CHEST_RESULT WHERE MAIN_ID = ").append(extractRule.getRid());
            this.executeSql(sql.toString(), null);
            sql = new StringBuffer();
            sql.append("UPDATE TD_ZK_EXTRACT_RULE SET IF_WARN = null, HEARING_RST_ID = null WHERE RID = ").append(extractRule.getRid());
            this.executeSql(sql.toString(), null);
            extractRule.setOnguardStateList(null);
            extractRule.setBadrsnsList(null);
            extractRule.setBadrsnsRstList(null);
            extractRule.setChestResultList(null);
        }
        String ex1 = extractRule.getFkByExtractTypeId().getExtendS1();
        String onguardStateIds = extractRule.getOnguardStateIds();
        if (StringUtils.isNotBlank(onguardStateIds)) {
            List<String> list = StringUtils.string2list(onguardStateIds, ",");
            List<TdZkOnguardState> states = new ArrayList<>();
            for (String str : list) {
                TdZkOnguardState state = new TdZkOnguardState();
                state.setFkByMainId(extractRule);
                state.setFkByOnguardStateid(new TsSimpleCode(Convert.toInt(str)));
                state.setCreateManid(Global.getUser().getRid());
                state.setCreateDate(new Date());
                states.add(state);
            }
            extractRule.setOnguardStateList(states);
        }
        String badrsnsIds = extractRule.getBadrsnsIds();
        if (StringUtils.isNotBlank(badrsnsIds)) {
            List<String> list = StringUtils.string2list(badrsnsIds, ",");
            List<TdZkBadrsns> badrsnsList = new ArrayList<>();
            for (String str : list) {
                TdZkBadrsns badrsns = new TdZkBadrsns();
                badrsns.setFkByMainId(extractRule);
                badrsns.setFkByBadrsnId(new TsSimpleCode(Convert.toInt(str)));
                badrsns.setCreateManid(Global.getUser().getRid());
                badrsns.setCreateDate(new Date());
                badrsnsList.add(badrsns);
            }
            extractRule.setBadrsnsList(badrsnsList);
        }
        String badrsnsRstIds = extractRule.getBadrsnsRstIds();
        if (StringUtils.isNotBlank(badrsnsRstIds)) {
            List<String> list = StringUtils.string2list(badrsnsRstIds, ",");
            List<TdZkBadrsnsRst> badrsnsRsts = new ArrayList<>();
            for (String str : list) {
                TdZkBadrsnsRst badrsnsRst = new TdZkBadrsnsRst();
                badrsnsRst.setFkByMainId(extractRule);
                badrsnsRst.setFkByExamConclusionId(new TsSimpleCode(Convert.toInt(str)));
                badrsnsRst.setCreateManid(Global.getUser().getRid());
                badrsnsRst.setCreateDate(new Date());
                badrsnsRsts.add(badrsnsRst);
            }
            extractRule.setBadrsnsRstList(badrsnsRsts);
        }
        String[] chestResultIds = extractRule.getChestResultIds();
        if ("1".equals(ex1) && ObjectUtil.isNotNull(chestResultIds)) {
            List<TdZkChestResult> chestResultList = new ArrayList<>();
            for (String str : chestResultIds) {
                TdZkChestResult chestResult = new TdZkChestResult();
                chestResult.setFkByMainId(extractRule);
                chestResult.setChestResult(Convert.toInt(str));
                chestResult.setCreateManid(Global.getUser().getRid());
                chestResult.setCreateDate(new Date());
                chestResultList.add(chestResult);
            }
            extractRule.setChestResultList(chestResultList);
        }
        Integer hearingRstId = extractRule.getHearingRstId();
        if ("2".equals(ex1) && ObjectUtil.isNotNull(hearingRstId)) {
            extractRule.setFkByHearingRstId(new TsSimpleCode(hearingRstId));
        }
        this.upsertEntity(extractRule);
    }

    /**
     * <p>方法描述：删除</p>
     *
     * @MethodAuthor hsj 2024-07-02 16:44
     */
    @Transactional(readOnly = false)
    public void delExtractRule(Integer rid) {
        if(ObjectUtil.isNull(rid)){
            return;
        }
        StringBuffer sql = new StringBuffer();
        sql.append("DELETE TD_ZK_ONGUARD_STATE WHERE MAIN_ID = ").append(rid);
        this.executeSql(sql.toString(), null);
        sql = new StringBuffer();
        sql.append("DELETE TD_ZK_BADRSNS WHERE MAIN_ID = ").append(rid);
        this.executeSql(sql.toString(), null);
        sql = new StringBuffer();
        sql.append("DELETE TD_ZK_BADRSNS_RST WHERE MAIN_ID = ").append(rid);
        this.executeSql(sql.toString(), null);
        sql = new StringBuffer();
        sql.append("DELETE TD_ZK_CHEST_RESULT WHERE MAIN_ID = ").append(rid);
        this.executeSql(sql.toString(), null);
        sql = new StringBuffer();
        sql.append("DELETE TD_ZK_EXTRACT_RULE WHERE RID = ").append(rid);
        this.executeSql(sql.toString(), null);
    }

    /**
     *  <p>方法描述：根据rid查询</p>
     * @MethodAuthor hsj 2024-07-03 14:40
     */
    @Transactional(readOnly = true)
    public TdZkExtractRule findTdZkExtractRuleById(Integer extractId) {
        TdZkExtractRule rule = this.find(TdZkExtractRule.class,extractId);
        this.dealRule(rule);
        return rule;
    }

    /**
     * <p>方法描述：获取未处理完的胸片或者电测听数量 </p>
     * pw 2024/7/4
     **/
    @Transactional(readOnly = true)
    public int findUnValidateCount(Integer type, String extractUnitId, Date rptStartDate,
                                   Date rptEndDate, List<String> itemCodeList) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(" SELECT COUNT(B.RID) ")
                .append("  FROM TD_TJ_BHK B ")
                .append("  INNER JOIN TD_TJ_BHKSUB BS ON B.RID = BS.BHK_ID ")
                .append("  INNER JOIN TB_TJ_ITEMS I ON BS.ITEM_ID = I.RID ");
        if (1 == type) {
            buffer.append(" INNER JOIN TS_SIMPLE_CODE K ON I.ITEM_TAG_ID = K.RID ");
        }
        buffer.append("  WHERE 1=1 AND BS.IF_LACK=0 ")
                .append(" AND B.BHKORG_ID IN (").append(extractUnitId).append(") ");
        if (null != rptStartDate) {
            buffer.append(" AND B.RPT_PRINT_DATE>=to_date('").append(DateUtils.formatDate(rptStartDate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS') ");
        }
        if (null != rptEndDate) {
            buffer.append(" AND B.RPT_PRINT_DATE<=to_date('").append(DateUtils.formatDate(rptEndDate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS') ");
        }
        Map<String, Object> paramMap = new HashMap<>();
        if (1 == type) {
            // 注意 胸片标记使用项目标记码表的扩展字段3匹配
            buffer.append(" AND K.EXTENDS3 = '30' ")
                    .append(" AND B.CHEST_RESULT IS NULL ")
                    .append(" AND BS.RST_FLAG IS NOT NULL ");
        } else {
            buffer.append(" AND I.ITEM_CODE IN (:itemCodeList) ")
                    .append(" AND B.HEARING_RST_ID IS NULL ");
            paramMap.put("itemCodeList", itemCodeList);
        }
        return this.findCountBySql(buffer.toString(), paramMap);
    }

    /**
     *  <p>方法描述：根据rid查询状态</p>
     * @MethodAuthor hsj 2024-07-04 10:42
     */
    @Transactional(readOnly = true)
    public Integer findTdZkExtractBhkStateByRid(Integer rid) {
        if(ObjectUtil.isNull(rid)){
            return null;
        }
        String sql = "SELECT T.RID,T.STATE FROM  TD_ZK_EXTRACT_BHK T WHERE T.RID ="+ rid;
        List<Object[]> list = this.findDataBySqlNoPage(sql,null);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return Convert.toInt(list.get(0)[1]);
    }


    /**
     *  <p>方法描述：根据rid-更新状态</p>
     * @MethodAuthor hsj 2024-07-04 15:00
     */
    @Transactional(readOnly = false)
    public void updateTdZkExtractBhkStateByRid(Integer rid,Integer state) {
        if(ObjectUtil.isNull(rid)){
            return ;
        }
        String sql = "UPDATE TD_ZK_EXTRACT_BHK T SET T.STATE =:state, T.MODIFY_DATE =SYSDATE,T.MODIFY_MANID =:modifyManid WHERE T.RID =:id";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("state",state);
        paramMap.put("modifyManid",Global.getUser().getRid());
        paramMap.put("id",rid);
        this.executeSql(sql, paramMap);
    }

    /**
     *  <p>方法描述：提交</p>
     * @MethodAuthor hsj 2024-07-04 16:43
     */
    @Transactional(readOnly = false)
    public void updateTdZkExtractBhk(TdZkExtractBhk extractBhk) {
        String ex1 = extractBhk.getFkByExtractTypeId().getExtendS1();
        if("1".equals(ex1)){
            extractBhk.setFkByExpertHearRstId(null);
        }else if("2".equals(ex1)){
            extractBhk.setFkByExpertChestId(null);
            extractBhk.setFkByChestLevelId(null);
        }
        StringBuffer sql = new StringBuffer();
        Map<String, Object> paramMap = new HashMap<>();
        sql.append(" UPDATE TD_ZK_EXTRACT_BHK T SET T.STATE =:state");
        paramMap.put("state",extractBhk.getState());
        boolean flag = ObjectUtil.isNotNull(extractBhk.getFkByExpertChestId()) &&  ObjectUtil.isNotNull(extractBhk.getFkByExpertChestId().getRid());
        if(flag){
            sql.append(" , T.EXPERT_CHEST_ID =:expertChestId");
            paramMap.put("expertChestId",extractBhk.getFkByExpertChestId().getRid());
        }else {
            sql.append(" , T.EXPERT_CHEST_ID = null");
        }
        flag = ObjectUtil.isNotNull(extractBhk.getFkByExpertHearRstId()) &&  ObjectUtil.isNotNull(extractBhk.getFkByExpertHearRstId().getRid());
        if(flag){
            sql.append(" , T.EXPERT_HEAR_RST_ID =:expertHearRstId");
            paramMap.put("expertHearRstId",extractBhk.getFkByExpertHearRstId().getRid());
        }else {
            sql.append(" , T.EXPERT_HEAR_RST_ID = null");
        }
        flag = ObjectUtil.isNotNull(extractBhk.getFkByExpertRstId()) &&  ObjectUtil.isNotNull(extractBhk.getFkByExpertRstId().getRid());
        if(flag){
            sql.append(" , T.EXPERT_RST_ID =:expertRstId");
            paramMap.put("expertRstId",extractBhk.getFkByExpertRstId().getRid());
        }else {
            sql.append(" , T.EXPERT_RST_ID = null");
        }
        flag = ObjectUtil.isNotNull(extractBhk.getFkByChestLevelId()) &&  ObjectUtil.isNotNull(extractBhk.getFkByChestLevelId().getRid());
        if(flag){
            sql.append(" , T.CHEST_LEVEL_ID =:chestLevelId");
            paramMap.put("chestLevelId",extractBhk.getFkByChestLevelId().getRid());
        }else {
            sql.append(" , T.CHEST_LEVEL_ID = null");
        }
        if(StringUtils.isNotBlank(extractBhk.getCheckUnitName())){
            sql.append(" , T.CHECK_UNIT_NAME =:checkUnitName");
            paramMap.put("checkUnitName",extractBhk.getCheckUnitName());
        }else {
            sql.append(" , T.CHECK_UNIT_NAME = null");
        }
        if(StringUtils.isNotBlank(extractBhk.getCheckPsn())){
            sql.append(" , T.CHECK_PSN =:checkPsn");
            paramMap.put("checkPsn",extractBhk.getCheckPsn());
        }else {
            sql.append(" , T.CHECK_PSN = null");
        }
        if(ObjectUtil.isNotNull(extractBhk.getCheckDate())){
            sql.append(" , T.CHECK_DATE =:checkDate");
            paramMap.put("checkDate",extractBhk.getCheckDate());
        }else {
            sql.append(" , T.CHECK_DATE = null");
        }
        if(StringUtils.isNotBlank(extractBhk.getCheckLinktel())){
            sql.append(" , T.CHECK_LINKTEL =:checkLinktel");
            paramMap.put("checkLinktel",extractBhk.getCheckLinktel());
        }else {
            sql.append(" , T.CHECK_LINKTEL = null");
        }
        sql.append(" ,T.CHECK_USER_ID =:checkUserId, T.MODIFY_DATE = SYSDATE,T.MODIFY_MANID =:modifyManid");
        paramMap.put("checkUserId",Global.getUser().getRid());
        paramMap.put("modifyManid",Global.getUser().getRid());
        sql.append(" WHERE T.RID =").append(extractBhk.getRid());
        this.executeSql(sql.toString(), paramMap);
    }
    /**
     *  <p>方法描述：任务分配</p>
     * @MethodAuthor hsj 2024-07-08 16:34
     */
    @Transactional(readOnly = false)
    public void saveBhkExpertList(List<Integer> taskRids, List<UserPsnInfoDTO> list) {
        if(CollectionUtils.isEmpty(taskRids) || CollectionUtils.isEmpty(list)){
            return;
        }
        List<TdZkBhkExpert> experts = new ArrayList<>();
        for( Integer rid : taskRids){
            for(UserPsnInfoDTO dto : list){
                TdZkBhkExpert expert = new TdZkBhkExpert();
                expert.setFkByMainId(new TdZkExtractBhk(rid));
                expert.setFkByUserId(new TsUserInfo(dto.getRid()));
                expert.setFkByZoneId(new TsZone(dto.getZoneId()));
                expert.setUnitName(dto.getUnitname());
                expert.setUserName(dto.getUsername());
                expert.setCreateDate(new Date());
                expert.setCreateManid(Global.getUser().getRid());
                experts.add(expert);
            }
        }
        this.saveBatchObjs(experts);
        this.updateTdZkExtractBhkStateByRids(taskRids,4);
    }
    /**
     *  <p>方法描述：批量更新状态</p>
     * @MethodAuthor hsj 2024-07-08 18:00
     */
    @Transactional(readOnly = false)
    public void updateTdZkExtractBhkStateByRids(List<Integer> rids,Integer state) {
        if(CollectionUtils.isEmpty(rids)){
            return ;
        }
        String sql = "UPDATE TD_ZK_EXTRACT_BHK T SET T.STATE =:state, T.MODIFY_DATE =SYSDATE,T.MODIFY_MANID =:modifyManid WHERE T.RID in (:ids)";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("state",state);
        paramMap.put("modifyManid",Global.getUser().getRid());
        paramMap.put("ids",rids);
        this.executeSql(sql, paramMap);
    }

    /**
     *  <p>方法描述：根据rids和状态查询符合改状态的rid</p>
     * @MethodAuthor hsj 2024-07-09 10:31
     */
    @Transactional(readOnly = true)
    public List<Integer> findTdZkExtractBhkStateByRids(List<Integer> rids,Integer state) {
        List<Integer> ridList = new ArrayList<>();
        if(ObjectUtil.isNull(state) || CollectionUtils.isEmpty(rids)){
            return ridList;
        }
        String sql = "SELECT T.RID,T.STATE FROM  TD_ZK_EXTRACT_BHK T WHERE T.RID in (:rids) and state =:state";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rids",rids);
        paramMap.put("state",state);
        List<Object[]> list = this.findDataBySqlNoPage(sql,paramMap);
        if(CollectionUtils.isEmpty(list)){
            return ridList;
        }
        for(Object[] obj : list ){
            ridList.add(Convert.toInt(obj[0]));
        }
        return ridList;
    }

    /**
     *  <p>方法描述：根据主表rid查询专家账号</p>
     * @MethodAuthor hsj 2024-07-09 11:37
     */
    @Transactional(readOnly = true)
    public List<Object[]> findTdZkBhkExpertByMainId(Integer mainId) {
        if(ObjectUtil.isNull(mainId)){
            return new ArrayList<>();
        }
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT T.RID ,CASE WHEN T1.ZONE_TYPE > 2 THEN SUBSTR(T1.FULL_NAME, INSTR(T1.FULL_NAME, '_') + 1) ELSE T1.FULL_NAME END ZONE_NAME  ");
        sql.append(" ,T.UNIT_NAME ,T.USER_NAME ,T2.USER_NO ");
        sql.append(" FROM TD_ZK_BHK_EXPERT T LEFT JOIN TS_ZONE T1 ON  T.ZONE_ID = T1.RID");
        sql.append(" LEFT JOIN  TS_USER_INFO T2 ON  T.USER_ID = T2.RID");
        sql.append(" WHERE T.MAIN_ID = ").append(mainId);
        sql.append(" ORDER BY T1.ZONE_GB ,T.UNIT_NAME ,T.USER_NAME");
        return this.findDataBySqlNoPage(sql.toString(),null);
    }


}
