package com.chis.modules.heth.zkcheck.service;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <p>类描述： 机构考核一览表服务类 </p>
 * @ClassAuthor： pw 2022/11/8
 **/
@Service
@Transactional(readOnly = true)
public class OrgExamResultOverService extends AbstractTemplate {

    /**
     * <p>方法描述： 通过质控考核主表rid集合 查询存在问题 </p>
     * @MethodAuthor： pw 2022/11/8
     **/
    public Map<Integer,String> queryDeduct(List<Integer> mainRidList){
        if(CollectionUtils.isEmpty(mainRidList)){
            return Collections.EMPTY_MAP;
        }
        Map<Integer,String> resultMap = new HashMap<>();
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" WITH ZK_CHECK_MAIN_RID_TB AS (");
        int size = mainRidList.size();
        for(int i=0; i<size; i++){
            if(i == 0){
                sqlBuffer.append(" SELECT ");
            }else{
                sqlBuffer.append(" UNION SELECT ");
            }
            sqlBuffer.append(mainRidList.get(i)).append(" AS RID FROM DUAL ");
        }
        sqlBuffer.append(")");
        sqlBuffer.append(" SELECT ")
                .append("    K.MAIN_ID, ")
                .append("    K2.RID, ")
                .append("    K3.XH, ")
                .append("    K7.CODE_NAME, ")
                .append("    K2.RMK, ")
                .append("    '' ")
                .append(" FROM TD_ZW_ZK_CHECK_TABLE K ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_ITEM K1 ON K1.MAIN_ID = K.RID ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_SUB K2 ON K2.MAIN_ID = K1.RID ")
                .append(" INNER JOIN TB_ZW_ZK_SCORES K3 ON K2.SCORE_ID = K3.RID ")
                .append(" INNER JOIN TS_SIMPLE_CODE K4 ON K2.SCORE_RST_ID = K4.RID ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_DEDUCT K5 ON K5.MAIN_ID = K2.RID ")
                .append(" INNER JOIN TB_ZW_ZK_SCORE_DEDUCT K6 ON K6.RID = K5.DEDUCT_ID ")
                .append(" INNER JOIN TS_SIMPLE_CODE K7 ON K6.DEDUCT_ID = K7.RID ")
                .append(" WHERE K4.EXTENDS1 IN (2,3) ")
                .append(" AND EXISTS (SELECT 1 FROM ZK_CHECK_MAIN_RID_TB M WHERE M.RID=K.MAIN_ID) ");
        sqlBuffer.append(" ORDER BY K.MAIN_ID,K3.XH,K7.NUM ");
        List<Object[]> queryResultList = this.findDataBySqlNoPage(sqlBuffer.toString(), null);
        if(CollectionUtils.isEmpty(queryResultList)){
            return Collections.EMPTY_MAP;
        }
        Map<Integer,List<Object[]>> map = new LinkedHashMap<>(queryResultList.size());
        for(Object[] objArr : queryResultList){
            Integer rid = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
            if(null == rid){
                continue;
            }
            List<Object[]> tmpList = map.get(rid);
            if(null == tmpList){
                tmpList = new ArrayList<>();
            }
            tmpList.add(objArr);
            map.put(rid, tmpList);
        }
        if(CollectionUtils.isEmpty(map)){
            return Collections.EMPTY_MAP;
        }
        for(Map.Entry<Integer,List<Object[]>> mapEntity : map.entrySet()){
            List<Object[]> tmpList = mapEntity.getValue();
            Object[] objArr = tmpList.get(0);
            Integer mainRid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            if(null == mainRid){
                continue;
            }
            Integer xh = null == objArr[2] ? null : Integer.parseInt(objArr[2].toString());
            String rmk = null == objArr[4] ? null : objArr[4].toString();
            StringBuffer subBuffer = new StringBuffer();
            for(Object[] arr : tmpList){
                String codeName = null == arr[3] ? null : arr[3].toString();
                if(StringUtils.isNotBlank(codeName)){
                    subBuffer.append("；").append(codeName);
                }
            }
            StringBuffer tipBuffer = new StringBuffer();
            if(null != xh){
                tipBuffer.append(xh).append("、");
            }
            if(subBuffer.length() > 0){
                tipBuffer.append(subBuffer.substring(1));
            }
            if(StringUtils.isNotBlank(rmk)){
                tipBuffer.append("（").append(rmk).append("）");
            }
            if(tipBuffer.length() > 0){
                String tip = resultMap.get(mainRid);
                tipBuffer.append("；");
                if(null == tip){
                    tip = tipBuffer.toString();
                }else{
                    tip = tip+((char)10)+tipBuffer.toString();
                }
                resultMap.put(mainRid, tip);
            }
        }
        return resultMap;
    }

    /**
     * <p>方法描述： 通过质控考核主表rid集合 获取主检医师技能考核结果 </p>
     * @MethodAuthor： pw 2022/11/8
     **/
    public Map<Integer,List<Object[]>> querySkillEvaluation(List<Integer> mainRidList){
        if(CollectionUtils.isEmpty(mainRidList)){
            return Collections.EMPTY_MAP;
        }
        Map<Integer,List<Object[]>>  resultMap = new HashMap<>();
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" WITH ZK_CHECK_MAIN_RID_TB AS (");
        int size = mainRidList.size();
        for(int i=0; i<size; i++){
            if(i == 0){
                sqlBuffer.append(" SELECT ");
            }else{
                sqlBuffer.append(" UNION SELECT ");
            }
            sqlBuffer.append(mainRidList.get(i)).append(" AS RID FROM DUAL ");
        }
        sqlBuffer.append(")");
        sqlBuffer.append(" SELECT ")
                .append("    K.MAIN_ID, ")
                .append("    K3.BUS_EXTENDS, ")
                .append("    K4.CODE_NAME, ")
                .append("    K2.RMK ")
                .append(" FROM TD_ZW_ZK_CHECK_TABLE K ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_ITEM K1 ON K1.MAIN_ID = K.RID ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_SUB K2 ON K2.MAIN_ID = K1.RID ")
                .append(" INNER JOIN TB_ZW_ZK_SCORES K3 ON K2.SCORE_ID = K3.RID ")
                .append(" LEFT JOIN TS_SIMPLE_CODE K4 ON K2.SCORE_RST_ID = K4.RID ")
                .append(" WHERE K3.SPECIAL_FLAG=2 AND EXISTS (SELECT 1 FROM ZK_CHECK_MAIN_RID_TB M WHERE M.RID=K.MAIN_ID) ")
                .append(" GROUP BY K.MAIN_ID ,K3.BUS_EXTENDS, K4.CODE_NAME, K2.RMK ");
        List<Object[]> queryResultList = this.findDataBySqlNoPage(sqlBuffer.toString(), null);
        if(CollectionUtils.isEmpty(queryResultList)){
            return Collections.EMPTY_MAP;
        }
        for(Object[] objArr : queryResultList){
            Integer mainId = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            if(null == mainId){
                continue;
            }
            List<Object[]> tmpList = resultMap.get(mainId);
            if(null == tmpList){
                tmpList = new ArrayList<>();
            }
            tmpList.add(objArr);
            resultMap.put(mainId, tmpList);
        }
        return resultMap;
    }

    /**
     * <p>方法描述： 通过质控考核主表rid集合 查询是否存在盲样考核的项目 </p>
     * key 质控考核主表rid+@+质控指标分值维护的检测指标对应扩展字段
     * @MethodAuthor： pw 2022/11/8
     **/
    public Map<String,Integer> selectMyItemExistByMainIdGroupByBusExt(List<Integer> mainRidList){
        if(CollectionUtils.isEmpty(mainRidList)){
            return Collections.EMPTY_MAP;
        }
        Map<String,Integer> resultMap = new HashMap<>();
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" WITH ZK_CHECK_MAIN_RID_TB AS (");
        int size = mainRidList.size();
        for(int i=0; i<size; i++){
            if(i == 0){
                sqlBuffer.append(" SELECT ");
            }else{
                sqlBuffer.append(" UNION SELECT ");
            }
            sqlBuffer.append(mainRidList.get(i)).append(" AS RID FROM DUAL ");
        }
        sqlBuffer.append(")");
        sqlBuffer.append(" SELECT ")
                .append("    K.MAIN_ID, ")
                .append("    K3.BUS_EXTENDS, ")
                .append("    COUNT(1) AS COUNTNUM ")
                .append(" FROM TD_ZW_ZK_CHECK_TABLE K ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_ITEM K1 ON K1.MAIN_ID = K.RID ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_SUB K2 ON K2.MAIN_ID = K1.RID ")
                .append(" INNER JOIN TB_ZW_ZK_SCORES K3 ON K2.SCORE_ID = K3.RID ")
                .append(" WHERE K3.SPECIAL_FLAG=3 AND EXISTS (SELECT 1 FROM ZK_CHECK_MAIN_RID_TB M WHERE M.RID=K.MAIN_ID) ")
                .append(" GROUP BY K.MAIN_ID ,K3.BUS_EXTENDS ");
        List<Object[]> queryResultList = this.findDataBySqlNoPage(sqlBuffer.toString(), null);
        if(CollectionUtils.isEmpty(queryResultList)){
            return Collections.EMPTY_MAP;
        }
        for(Object[] objArr : queryResultList){
            Integer mainId = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            String scoreRstExt1 = null == objArr[1] ? null : objArr[1].toString();
            if(null == mainId || StringUtils.isBlank(scoreRstExt1)){
                continue;
            }
            Integer count = null == objArr[2] ? 0 : Integer.parseInt(objArr[2].toString());
            //scoreRstExt1 可能维护有逗号分隔的血铅 尿镉
            for(String str : scoreRstExt1.split(",")){
                resultMap.put(mainId+"@"+str, count);
            }
        }
        return resultMap;
    }

    /**
     * <p>方法描述： 通过评估机构rid集合 获取盲样考核结果 </p>
     * key 评估机构rid
     * subKey 盲样领取明细信息的指标的扩展字段2（码表5401 扩展字段2 1：血铅2：尿隔3：血清生化）+@+合格标记(0否 1是)
     * subValue 对应的数量
     * @MethodAuthor： pw 2022/11/8
     **/
    public Map<Integer, Map<String,Integer>> queryMyCountResultByUnitRidGroupByUnitAndExt2(List<Integer> unitList){
        if(CollectionUtils.isEmpty(unitList)){
            return Collections.EMPTY_MAP;
        }
        Map<Integer, Map<String,Integer>> resultMap = new HashMap<>();
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" WITH ZK_CHECK_UNIT_RID_TB AS (");
        int size = unitList.size();
        for(int i=0; i<size; i++){
            if(i == 0){
                sqlBuffer.append(" SELECT ");
            }else{
                sqlBuffer.append(" UNION SELECT ");
            }
            sqlBuffer.append(unitList.get(i)).append(" AS RID FROM DUAL ");
        }
        sqlBuffer.append(")");
        sqlBuffer.append(", MY_TMP_TB AS ( ");
        sqlBuffer.append("  SELECT RID FROM ( ");
        sqlBuffer.append("     SELECT ZP.RID FROM TD_ZWMY_PLAN ZP WHERE ZP.STATE = 1 ORDER BY ZP.PUBLISH_DATE DESC,ZP.RID DESC ");
        sqlBuffer.append("  ) WHERE ROWNUM=1");
        sqlBuffer.append(")");
        sqlBuffer.append(" SELECT ")
                .append("    ZSU.UNIT_ID,TSC.EXTENDS2, ZRR.IF_HG, COUNT(1) AS COUNTNUM ")
                .append("  FROM TD_ZWMY_SAMP_SUB ZSS ")
                .append("  INNER JOIN TD_ZWMY_FETCH_SAMP ZFS ON ZFS.RID = ZSS.MAIN_ID ")
                .append("  INNER JOIN TS_SIMPLE_CODE TSC ON ZSS.ITEM_ID = TSC.RID ")
                .append("  INNER JOIN TD_ZWMY_RCD_RST ZRR ON ZSS.RID = ZRR.MAIN_ID ")
                .append("  INNER JOIN TD_ZWMY_SIGN_UP ZSU ON ZFS.MAIN_ID = ZSU.RID ")
                .append("  INNER JOIN MY_TMP_TB TMP ON TMP.RID = ZSU.MAIN_ID ")
                .append("  WHERE ")
                .append("    ZSU.STATE >= 6 ")
                .append("    AND EXISTS (SELECT 1 FROM ZK_CHECK_UNIT_RID_TB TB WHERE TB.RID=ZSU.UNIT_ID) ")
                .append("  GROUP BY ZSU.UNIT_ID,TSC.EXTENDS2, ZRR.IF_HG ");
        List<Object[]> queryResultList = this.findDataBySqlNoPage(sqlBuffer.toString(), null);
        if(CollectionUtils.isEmpty(queryResultList)){
            return Collections.EMPTY_MAP;
        }
        for(Object[] objArr : queryResultList){
            Integer mainId = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            Integer ext2 = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
            Integer ifHg = null == objArr[2] ? null : Integer.parseInt(objArr[2].toString());
            if(null == mainId || null == ext2 || null==ifHg){
                continue;
            }
            Integer count = null == objArr[3] ? 0 : Integer.parseInt(objArr[3].toString());
            String subKey = ext2+"@"+ifHg;
            Map<String,Integer> map = resultMap.get(mainId);
            if(null == map){
                map = new HashMap<>(6);
            }
            map.put(subKey, count);
            resultMap.put(mainId, map);
        }
        return resultMap;
    }
    /**
     * <p>方法描述： 通过质控考核主表rid集合 按项类扩展字段1以及评分项结果扩展字段1 获取对应的数量 </p>
     * @MethodAuthor： pw 2022/11/8
     **/
    public Map<Integer, Map<String,Integer>> queryCountByMainIdGroupByItemTypeExtends1AndRstExtends1(List<Integer> mainRidList){
        if(CollectionUtils.isEmpty(mainRidList)){
            return Collections.EMPTY_MAP;
        }
        Map<Integer, Map<String,Integer>> resultMap = new HashMap<>();
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" WITH ZK_CHECK_MAIN_RID_TB AS (");
        int size = mainRidList.size();
        for(int i=0; i<size; i++){
            if(i == 0){
                sqlBuffer.append(" SELECT ");
            }else{
                sqlBuffer.append(" UNION SELECT ");
            }
            sqlBuffer.append(mainRidList.get(i)).append(" AS RID FROM DUAL ");
        }
        sqlBuffer.append(")");
        sqlBuffer.append(" SELECT ")
                .append("    K.MAIN_ID, ")
                .append("    K4.EXTENDS1 AS SCORERSTEXT1, ")
                .append("    K5.EXTENDS1 AS ITEMTYPEEXT1, ")
                .append("    COUNT(1) AS COUNTNUM ")
                .append(" FROM TD_ZW_ZK_CHECK_TABLE K ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_ITEM K1 ON K1.MAIN_ID = K.RID ")
                .append(" INNER JOIN TD_ZW_ZK_CHECK_SUB K2 ON K2.MAIN_ID = K1.RID ")
                .append(" INNER JOIN TB_ZW_ZK_SCORES K3 ON K2.SCORE_ID = K3.RID ")
                .append(" LEFT JOIN TS_SIMPLE_CODE K4 ON K2.SCORE_RST_ID = K4.RID ")
                .append(" LEFT JOIN TS_SIMPLE_CODE K5 ON K3.ITEM_TYPE_ID = K5.RID ")
                .append(" WHERE EXISTS (SELECT 1 FROM ZK_CHECK_MAIN_RID_TB M WHERE M.RID=K.MAIN_ID) ")
                .append(" GROUP BY K.MAIN_ID ,K4.EXTENDS1 ,K5.EXTENDS1 ");
        List<Object[]> queryResultList = this.findDataBySqlNoPage(sqlBuffer.toString(), null);
        if(CollectionUtils.isEmpty(queryResultList)){
            return Collections.EMPTY_MAP;
        }
        for(Object[] objArr : queryResultList){
            Integer mainId = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            String scoreRstExt1 = null == objArr[1] ? null : objArr[1].toString();
            String itemTypeExt1 = null == objArr[2] ? null : objArr[2].toString();
            if(null == mainId || StringUtils.isBlank(scoreRstExt1) || StringUtils.isBlank(itemTypeExt1)){
                continue;
            }
            Integer count = null == objArr[3] ? 0 : Integer.parseInt(objArr[3].toString());
            String subKey = itemTypeExt1+"@"+scoreRstExt1;
            Map<String,Integer> map = resultMap.get(mainId);
            if(null == map){
                map = new HashMap<>(6);
            }
            map.put(subKey, count);
            resultMap.put(mainId, map);
        }
        return resultMap;
    }
}
