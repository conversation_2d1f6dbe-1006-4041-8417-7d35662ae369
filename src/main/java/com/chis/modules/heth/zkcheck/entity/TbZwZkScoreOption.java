package com.chis.modules.heth.zkcheck.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2024-8-15
 */
@Entity
@Table(name = "TB_ZW_ZK_SCORE_OPTION")
@SequenceGenerator(name = "TbZwZkScoreOption", sequenceName = "TB_ZW_ZK_SCORE_OPTION_SEQ", allocationSize = 1)
public class TbZwZkScoreOption implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TbZwZkScores fkByMainId;
    private TsSimpleCode fkByOptionId;
    private BigDecimal checkVal;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    public TbZwZkScoreOption() {
    }

    public TbZwZkScoreOption(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbZwZkScoreOption")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TbZwZkScores getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TbZwZkScores fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "OPTION_ID")
    public TsSimpleCode getFkByOptionId() {
        return fkByOptionId;
    }

    public void setFkByOptionId(TsSimpleCode fkByOptionId) {
        this.fkByOptionId = fkByOptionId;
    }

    @Column(name = "CHECK_VAL")
    public BigDecimal getCheckVal() {
        return checkVal;
    }

    public void setCheckVal(BigDecimal checkVal) {
        this.checkVal = checkVal;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

}