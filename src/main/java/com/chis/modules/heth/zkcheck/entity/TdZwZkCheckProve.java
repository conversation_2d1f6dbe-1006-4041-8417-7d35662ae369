package com.chis.modules.heth.zkcheck.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-6-26
 */
@Entity
@Table(name = "TD_ZW_ZK_CHECK_PROVE")
@SequenceGenerator(name = "TdZwZkCheckProve", sequenceName = "TD_ZW_ZK_CHECK_PROVE_SEQ", allocationSize = 1)
public class TdZwZkCheckProve implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwZkCheckMain fkByMainId;
	private String annexName;
	private String annexAddr;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	/**
	 * 上一个序号 为空则为第一个
	 */
	private Integer preIndex;
	/**
	 * 下一个序号 为空则为最后
	 */
	private Integer nextIndex;
	private  Integer annexType;
	
	public TdZwZkCheckProve() {
	}

	public TdZwZkCheckProve(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwZkCheckProve")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwZkCheckMain getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwZkCheckMain fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "ANNEX_NAME")	
	public String getAnnexName() {
		return annexName;
	}

	public void setAnnexName(String annexName) {
		this.annexName = annexName;
	}	
			
	@Column(name = "ANNEX_ADDR")	
	public String getAnnexAddr() {
		return annexAddr;
	}

	public void setAnnexAddr(String annexAddr) {
		this.annexAddr = annexAddr;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Transient
	public Integer getPreIndex() {
		return preIndex;
	}

	public void setPreIndex(Integer preIndex) {
		this.preIndex = preIndex;
	}
	@Transient
	public Integer getNextIndex() {
		return nextIndex;
	}

	public void setNextIndex(Integer nextIndex) {
		this.nextIndex = nextIndex;
	}

	@Column(name = "ANNEX_TYPE")
	public Integer getAnnexType() {
		return annexType;
	}

	public void setAnnexType(Integer annexType) {
		this.annexType = annexType;
	}
}