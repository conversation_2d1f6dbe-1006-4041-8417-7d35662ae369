package com.chis.modules.heth.zkcheck.entity;

import javax.persistence.*;

import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.entity.TsSimpleCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 
 * <AUTHOR>
 * @createTime 2024-7-3
 */
@Entity
@Table(name = "TD_ZK_EXTRACT_BHK")
@SequenceGenerator(name = "TdZkExtractBhk", sequenceName = "TD_ZK_EXTRACT_BHK_SEQ", allocationSize = 1)
public class TdZkExtractBhk implements Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsZone fkByZoneId;
	private TbTjSrvorg fkByOrgId;
	private String bhkCode;
	private TsSimpleCode fkByExtractTypeId;
	private String personName;
	private String idc;
	private BigDecimal tchbadrsntim;
	private TsSimpleCode fkByOnguardStateid;
	private TsSimpleCode fkByWorkTypeId;
	private String workOther;
	private Integer ifRhk;
	private Date bhkDate;
	private Date prtDate;
	private TsSimpleCode fkByChestRstId;
	private TsSimpleCode fkByHearRstId;
	private TsSimpleCode fkByBhkRstId;
	private TsSimpleCode fkByReBhkRstId;
	private Date extractDate;
	private TsUserInfo fkByExtractUserId;
	private Integer state;
	private String chestNo;
	private String bhkRptPath;
	private String chestPath;
	private String backRsn;
	private TsSimpleCode fkByExpertChestId;
	private TsSimpleCode fkByExpertHearRstId;
	private TsSimpleCode fkByExpertRstId;
	private TsSimpleCode fkByChestLevelId;
	private String checkUnitName;
	private String checkPsn;
	private Date checkDate;
	private String checkLinktel;
	private TsUserInfo fkByCheckUserId;
	private TsSimpleCode fkByProvChestId;
	private TsSimpleCode fkByProvHearRstId;
	private Integer provAdvice;
	private String provOtherRmk;
	private String provUnitName;
	private String provCheckPsn;
	private Date provCheckDate;
	private String provLinktel;
	private TsUserInfo fkByProvUserId;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	private TbTjCrpt fkByCrptId;
	private TbTjCrpt fkByEntrustCrptId;

	private String zoneName;
	private Integer provChestId;
	private Integer provHearRstId;
	private String idcEncrypted;

	public TdZkExtractBhk() {
	}

	public TdZkExtractBhk(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZkExtractBhk")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ZONE_ID")			
	public TsZone getFkByZoneId() {
		return fkByZoneId;
	}

	public void setFkByZoneId(TsZone fkByZoneId) {
		this.fkByZoneId = fkByZoneId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TbTjSrvorg getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TbTjSrvorg fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@Column(name = "BHK_CODE")	
	public String getBhkCode() {
		return bhkCode;
	}

	public void setBhkCode(String bhkCode) {
		this.bhkCode = bhkCode;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EXTRACT_TYPE_ID")			
	public TsSimpleCode getFkByExtractTypeId() {
		return fkByExtractTypeId;
	}

	public void setFkByExtractTypeId(TsSimpleCode fkByExtractTypeId) {
		this.fkByExtractTypeId = fkByExtractTypeId;
	}	
			
	@Column(name = "PERSON_NAME")	
	public String getPersonName() {
		return personName;
	}

	public void setPersonName(String personName) {
		this.personName = personName;
	}	
			
	@Column(name = "IDC")	
	public String getIdc() {
		return idc;
	}

	public void setIdc(String idc) {
		this.idc = idc;
	}	
			
	@Column(name = "TCHBADRSNTIM")	
	public BigDecimal getTchbadrsntim() {
		return tchbadrsntim;
	}

	public void setTchbadrsntim(BigDecimal tchbadrsntim) {
		this.tchbadrsntim = tchbadrsntim;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ONGUARD_STATEID")			
	public TsSimpleCode getFkByOnguardStateid() {
		return fkByOnguardStateid;
	}

	public void setFkByOnguardStateid(TsSimpleCode fkByOnguardStateid) {
		this.fkByOnguardStateid = fkByOnguardStateid;
	}	
			
	@ManyToOne
	@JoinColumn(name = "WORK_TYPE_ID")			
	public TsSimpleCode getFkByWorkTypeId() {
		return fkByWorkTypeId;
	}

	public void setFkByWorkTypeId(TsSimpleCode fkByWorkTypeId) {
		this.fkByWorkTypeId = fkByWorkTypeId;
	}	
			
	@Column(name = "WORK_OTHER")	
	public String getWorkOther() {
		return workOther;
	}

	public void setWorkOther(String workOther) {
		this.workOther = workOther;
	}	
			
	@Column(name = "IF_RHK")	
	public Integer getIfRhk() {
		return ifRhk;
	}

	public void setIfRhk(Integer ifRhk) {
		this.ifRhk = ifRhk;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "BHK_DATE")			
	public Date getBhkDate() {
		return bhkDate;
	}

	public void setBhkDate(Date bhkDate) {
		this.bhkDate = bhkDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "PRT_DATE")			
	public Date getPrtDate() {
		return prtDate;
	}

	public void setPrtDate(Date prtDate) {
		this.prtDate = prtDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CHEST_RST_ID")			
	public TsSimpleCode getFkByChestRstId() {
		return fkByChestRstId;
	}

	public void setFkByChestRstId(TsSimpleCode fkByChestRstId) {
		this.fkByChestRstId = fkByChestRstId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "HEAR_RST_ID")			
	public TsSimpleCode getFkByHearRstId() {
		return fkByHearRstId;
	}

	public void setFkByHearRstId(TsSimpleCode fkByHearRstId) {
		this.fkByHearRstId = fkByHearRstId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BHK_RST_ID")			
	public TsSimpleCode getFkByBhkRstId() {
		return fkByBhkRstId;
	}

	public void setFkByBhkRstId(TsSimpleCode fkByBhkRstId) {
		this.fkByBhkRstId = fkByBhkRstId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "RE_BHK_RST_ID")			
	public TsSimpleCode getFkByReBhkRstId() {
		return fkByReBhkRstId;
	}

	public void setFkByReBhkRstId(TsSimpleCode fkByReBhkRstId) {
		this.fkByReBhkRstId = fkByReBhkRstId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "EXTRACT_DATE")			
	public Date getExtractDate() {
		return extractDate;
	}

	public void setExtractDate(Date extractDate) {
		this.extractDate = extractDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EXTRACT_USER_ID")			
	public TsUserInfo getFkByExtractUserId() {
		return fkByExtractUserId;
	}

	public void setFkByExtractUserId(TsUserInfo fkByExtractUserId) {
		this.fkByExtractUserId = fkByExtractUserId;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Column(name = "CHEST_NO")	
	public String getChestNo() {
		return chestNo;
	}

	public void setChestNo(String chestNo) {
		this.chestNo = chestNo;
	}	
			
	@Column(name = "BHK_RPT_PATH")	
	public String getBhkRptPath() {
		return bhkRptPath;
	}

	public void setBhkRptPath(String bhkRptPath) {
		this.bhkRptPath = bhkRptPath;
	}	
			
	@Column(name = "CHEST_PATH")	
	public String getChestPath() {
		return chestPath;
	}

	public void setChestPath(String chestPath) {
		this.chestPath = chestPath;
	}	
			
	@Column(name = "BACK_RSN")	
	public String getBackRsn() {
		return backRsn;
	}

	public void setBackRsn(String backRsn) {
		this.backRsn = backRsn;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EXPERT_CHEST_ID")			
	public TsSimpleCode getFkByExpertChestId() {
		return fkByExpertChestId;
	}

	public void setFkByExpertChestId(TsSimpleCode fkByExpertChestId) {
		this.fkByExpertChestId = fkByExpertChestId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EXPERT_HEAR_RST_ID")			
	public TsSimpleCode getFkByExpertHearRstId() {
		return fkByExpertHearRstId;
	}

	public void setFkByExpertHearRstId(TsSimpleCode fkByExpertHearRstId) {
		this.fkByExpertHearRstId = fkByExpertHearRstId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EXPERT_RST_ID")			
	public TsSimpleCode getFkByExpertRstId() {
		return fkByExpertRstId;
	}

	public void setFkByExpertRstId(TsSimpleCode fkByExpertRstId) {
		this.fkByExpertRstId = fkByExpertRstId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CHEST_LEVEL_ID")			
	public TsSimpleCode getFkByChestLevelId() {
		return fkByChestLevelId;
	}

	public void setFkByChestLevelId(TsSimpleCode fkByChestLevelId) {
		this.fkByChestLevelId = fkByChestLevelId;
	}	
			
	@Column(name = "CHECK_UNIT_NAME")	
	public String getCheckUnitName() {
		return checkUnitName;
	}

	public void setCheckUnitName(String checkUnitName) {
		this.checkUnitName = checkUnitName;
	}	
			
	@Column(name = "CHECK_PSN")	
	public String getCheckPsn() {
		return checkPsn;
	}

	public void setCheckPsn(String checkPsn) {
		this.checkPsn = checkPsn;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "CHECK_DATE")			
	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}	
			
	@Column(name = "CHECK_LINKTEL")	
	public String getCheckLinktel() {
		return checkLinktel;
	}

	public void setCheckLinktel(String checkLinktel) {
		this.checkLinktel = checkLinktel;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CHECK_USER_ID")			
	public TsUserInfo getFkByCheckUserId() {
		return fkByCheckUserId;
	}

	public void setFkByCheckUserId(TsUserInfo fkByCheckUserId) {
		this.fkByCheckUserId = fkByCheckUserId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "PROV_CHEST_ID")			
	public TsSimpleCode getFkByProvChestId() {
		return fkByProvChestId;
	}

	public void setFkByProvChestId(TsSimpleCode fkByProvChestId) {
		this.fkByProvChestId = fkByProvChestId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "PROV_HEAR_RST_ID")			
	public TsSimpleCode getFkByProvHearRstId() {
		return fkByProvHearRstId;
	}

	public void setFkByProvHearRstId(TsSimpleCode fkByProvHearRstId) {
		this.fkByProvHearRstId = fkByProvHearRstId;
	}	
			
	@Column(name = "PROV_ADVICE")	
	public Integer getProvAdvice() {
		return provAdvice;
	}

	public void setProvAdvice(Integer provAdvice) {
		this.provAdvice = provAdvice;
	}	
			
	@Column(name = "PROV_OTHER_RMK")	
	public String getProvOtherRmk() {
		return provOtherRmk;
	}

	public void setProvOtherRmk(String provOtherRmk) {
		this.provOtherRmk = provOtherRmk;
	}	
			
	@Column(name = "PROV_UNIT_NAME")	
	public String getProvUnitName() {
		return provUnitName;
	}

	public void setProvUnitName(String provUnitName) {
		this.provUnitName = provUnitName;
	}	
			
	@Column(name = "PROV_CHECK_PSN")	
	public String getProvCheckPsn() {
		return provCheckPsn;
	}

	public void setProvCheckPsn(String provCheckPsn) {
		this.provCheckPsn = provCheckPsn;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "PROV_CHECK_DATE")			
	public Date getProvCheckDate() {
		return provCheckDate;
	}

	public void setProvCheckDate(Date provCheckDate) {
		this.provCheckDate = provCheckDate;
	}	
			
	@Column(name = "PROV_LINKTEL")	
	public String getProvLinktel() {
		return provLinktel;
	}

	public void setProvLinktel(String provLinktel) {
		this.provLinktel = provLinktel;
	}	
			
	@ManyToOne
	@JoinColumn(name = "PROV_USER_ID")			
	public TsUserInfo getFkByProvUserId() {
		return fkByProvUserId;
	}

	public void setFkByProvUserId(TsUserInfo fkByProvUserId) {
		this.fkByProvUserId = fkByProvUserId;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CRPT_ID")			
	public TbTjCrpt getFkByCrptId() {
		return fkByCrptId;
	}

	public void setFkByCrptId(TbTjCrpt fkByCrptId) {
		this.fkByCrptId = fkByCrptId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ENTRUST_CRPT_ID")			
	public TbTjCrpt getFkByEntrustCrptId() {
		return fkByEntrustCrptId;
	}

	public void setFkByEntrustCrptId(TbTjCrpt fkByEntrustCrptId) {
		this.fkByEntrustCrptId = fkByEntrustCrptId;
	}

	@Transient
	public String getZoneName() {
		return zoneName;
	}

	public void setZoneName(String zoneName) {
		this.zoneName = zoneName;
	}

	@Transient
	public Integer getProvChestId() {
		return provChestId;
	}

	public void setProvChestId(Integer provChestId) {
		this.provChestId = provChestId;
	}

	@Transient
	public Integer getProvHearRstId() {
		return provHearRstId;
	}

	public void setProvHearRstId(Integer provHearRstId) {
		this.provHearRstId = provHearRstId;
	}

	@Transient
	public String getIdcEncrypted() {
		return idcEncrypted;
	}

	public void setIdcEncrypted(String idcEncrypted) {
		this.idcEncrypted = idcEncrypted;
	}
}