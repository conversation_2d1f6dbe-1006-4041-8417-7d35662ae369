package com.chis.modules.heth.zkcheck.entity;

import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2023-6-16
 */
@Entity
@Table(name = "TD_RAD_CHECK_RPT")
@SequenceGenerator(name = "TdRadCheckRpt", sequenceName = "TD_RAD_CHECK_RPT_SEQ", allocationSize = 1)
public class TdRadCheckRpt implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsUnit fkByUnitId;
	private String crptName;
	private TsZone fkByZoneId;
	private String creditCode;
	private String address;
	private String linkMan;
	private String linkPhone;
	private Date rptDate;
	private String rptNo;
	private String rptName;
	private String filePath;
	private String sourceFilePath;
	private Integer state;
	private TsUnit fkByCheckUnitId;
	private TsUserInfo fkByCheckPsnId;
	private Date checkDate;
	private String backRan;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdRadCheckRpt() {
	}

	public TdRadCheckRpt(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdRadCheckRpt")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "UNIT_ID")			
	public TsUnit getFkByUnitId() {
		return fkByUnitId;
	}

	public void setFkByUnitId(TsUnit fkByUnitId) {
		this.fkByUnitId = fkByUnitId;
	}	
			
	@Column(name = "CRPT_NAME")	
	public String getCrptName() {
		return crptName;
	}

	public void setCrptName(String crptName) {
		this.crptName = crptName;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ZONE_ID")			
	public TsZone getFkByZoneId() {
		return fkByZoneId;
	}

	public void setFkByZoneId(TsZone fkByZoneId) {
		this.fkByZoneId = fkByZoneId;
	}	
			
	@Column(name = "CREDIT_CODE")	
	public String getCreditCode() {
		return creditCode;
	}

	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}	
			
	@Column(name = "ADDRESS")	
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}	
			
	@Column(name = "LINK_MAN")	
	public String getLinkMan() {
		return linkMan;
	}

	public void setLinkMan(String linkMan) {
		this.linkMan = linkMan;
	}	
			
	@Column(name = "LINK_PHONE")	
	public String getLinkPhone() {
		return linkPhone;
	}

	public void setLinkPhone(String linkPhone) {
		this.linkPhone = linkPhone;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "RPT_DATE")			
	public Date getRptDate() {
		return rptDate;
	}

	public void setRptDate(Date rptDate) {
		this.rptDate = rptDate;
	}	
			
	@Column(name = "RPT_NO")	
	public String getRptNo() {
		return rptNo;
	}

	public void setRptNo(String rptNo) {
		this.rptNo = rptNo;
	}	
			
	@Column(name = "RPT_NAME")	
	public String getRptName() {
		return rptName;
	}

	public void setRptName(String rptName) {
		this.rptName = rptName;
	}	
			
	@Column(name = "FILE_PATH")	
	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}	
			
	@Column(name = "SOURCE_FILE_PATH")	
	public String getSourceFilePath() {
		return sourceFilePath;
	}

	public void setSourceFilePath(String sourceFilePath) {
		this.sourceFilePath = sourceFilePath;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CHECK_UNIT_ID")			
	public TsUnit getFkByCheckUnitId() {
		return fkByCheckUnitId;
	}

	public void setFkByCheckUnitId(TsUnit fkByCheckUnitId) {
		this.fkByCheckUnitId = fkByCheckUnitId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CHECK_PSN_ID")			
	public TsUserInfo getFkByCheckPsnId() {
		return fkByCheckPsnId;
	}

	public void setFkByCheckPsnId(TsUserInfo fkByCheckPsnId) {
		this.fkByCheckPsnId = fkByCheckPsnId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "CHECK_DATE")			
	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}	
			
	@Column(name = "BACK_RAN")	
	public String getBackRan() {
		return backRan;
	}

	public void setBackRan(String backRan) {
		this.backRan = backRan;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}