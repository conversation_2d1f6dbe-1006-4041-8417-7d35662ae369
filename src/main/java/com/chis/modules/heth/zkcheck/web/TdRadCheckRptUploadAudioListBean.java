package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <p>类描述： 放射卫生技术服务机构检测报告审核 </p>
 * @ClassAuthor： pw 2023/6/19
 **/
@ManagedBean(name = "tdRadCheckRptUploadAudioListBean")
@ViewScoped
public class TdRadCheckRptUploadAudioListBean extends ZwAndRadCheckRptUploadCommBean{
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /** 查询条件的地区列表 */
    private List<TsZone> zoneList;
    /** 查询条件 选中的地区编码 */
    private String searchZoneCode;
    /** 查询条件 选中的地区名称 */
    private String searchZoneName;
    /** 查询条件 技术服务机构 */
    private String searchOrgName;

    /** 批量审核 选中的数据  */
    private List<Object[]> selectEntitys;
    public TdRadCheckRptUploadAudioListBean(){
        this.rptType = 1;
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }

        this.searchZoneCode = tsZone.getZoneGb();
        this.searchZoneName = tsZone.getZoneName();
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation( tsZone.getZoneGb(), false, null,null);
        //状态初始化
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem("1", "待审核"));
        this.stateList.add(new SelectItem("3", "已退回"));
        this.stateList.add(new SelectItem("2", "审核通过"));
        //默认选中待审核
        this.states = new String[]{"1"};
        this.searchAction();
    }

    @Override
    public void processData(List<?> list) {

    }

    @Override
    public void addInit() {

    }

    @Override
    public void modInit() {
        this.initCheckRpt();
    }

    @Override
    public void saveAction() {
        try{
            this.radCheckRpt.setState(2);
            this.radCheckRpt.setFkByCheckPsnId(Global.getUser());
            this.radCheckRpt.setCheckDate(new Date());
            this.tdRadCheckRptService.saveOrUpdateCheckRpt(this.radCheckRpt);
            RequestContext.getCurrentInstance().update("tabView");
            JsfUtil.addSuccessMessage("审核通过成功！");
            this.searchAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("审核通过失败！");
        }
    }

    @Override
    public String[] buildHqls() {
        StringBuilder sqlBuffer = new StringBuilder();
        sqlBuffer.append(" SELECT T.RID, ")
                .append(" CASE WHEN T2.ZONE_TYPE >2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME,'_')+1) ELSE T2.FULL_NAME END ZONE_NAME, ")
                .append(" T3.UNITNAME,T.CRPT_NAME,T.RPT_NAME, ")
                .append(" T.RPT_NO,T.RPT_DATE,T.STATE,T.BACK_RAN,T2.ZONE_GB ")
                .append(" FROM TD_RAD_CHECK_RPT T ")
                .append(" LEFT JOIN TS_UNIT T3 ON T3.RID = T.CHECK_UNIT_ID ")
                .append(" LEFT JOIN TS_ZONE T2 ON T3.ZONE_ID=T2.RID ")
                .append(" WHERE 1=1 ");
        //地区
        if(StringUtils.isNotBlank(this.searchZoneCode)){
            sqlBuffer.append(" AND T2.ZONE_GB LIKE :searchZoneCode escape '\\\'");
            this.paramMap.put("searchZoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        //报告日期
        if (null != this.searchReportBeginDate) {
            sqlBuffer.append("   AND T.RPT_DATE >= TO_DATE(:searchReportBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchReportBeginDate", DateUtils.formatDate(this.searchReportBeginDate) + " 00:00:00");
        }
        if (null != this.searchReportEndDate) {
            sqlBuffer.append("   AND T.RPT_DATE <= TO_DATE(:searchReportEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchReportEndDate", DateUtils.formatDate(this.searchReportEndDate) + " 23:59:59");
        }
        //技术服务机构
        if (StringUtils.isNotBlank(this.searchOrgName)) {
            sqlBuffer.append("  AND T3.UNITNAME LIKE :searchOrgName ");
            this.paramMap.put("searchOrgName", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchOrgName).trim()) + "%");
        }
        //检测报告编号
        if (StringUtils.isNotBlank(this.searchRptNo)) {
            sqlBuffer.append("  AND T.RPT_NO LIKE :rptNo ");
            this.paramMap.put("rptNo", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchRptNo).trim()) + "%");
        }
        //状态
        if (null != this.states && this.states.length>0) {
            sqlBuffer.append(" AND T.STATE IN (:state)");
            this.paramMap.put("state", Arrays.asList(this.states));
        }else{
            sqlBuffer.append(" AND T.STATE IN (1,2,3)");
        }
        String h2 = "SELECT COUNT(*) FROM (" + sqlBuffer+ ")";
        String h1 = "SELECT * FROM (" + sqlBuffer + ")AA  ORDER BY AA.ZONE_GB,AA.UNITNAME,AA.RPT_NO  ";
        return new String[] { h1, h2 };
    }

    /**
     * <p>方法描述：批量审核弹框 </p>
     * @MethodAuthor： pw 2023/6/17
     **/
    public void openReviewConfirmDialog(){
        if(CollectionUtils.isEmpty(this.selectEntitys)){
            JsfUtil.addErrorMessage("请选择需要审批的数据！");
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ReviewConfirmDialog').show();");
    }

    /**
     * <p>方法描述： 退回 </p>
     * @MethodAuthor： pw 2023/6/17
     **/
    @Override
    public void returnAction(){
        if(StringUtils.isBlank(this.backRsn)){
            JsfUtil.addErrorMessage("退回原因不能为空！");
            return;
        }else if(this.backRsn.length() > 100){
            JsfUtil.addErrorMessage("退回原因不能超过100个字！");
            return;
        }
        try{
            this.radCheckRpt.setState(3);
            this.radCheckRpt.setBackRan(this.backRsn);
            this.radCheckRpt.setFkByCheckPsnId(Global.getUser());
            this.radCheckRpt.setCheckDate(new Date());
            this.tdRadCheckRptService.saveOrUpdateCheckRpt(this.radCheckRpt);
            RequestContext.getCurrentInstance().execute("PF('ReasonDialog').hide();");
            RequestContext.getCurrentInstance().update("tabView");
            JsfUtil.addSuccessMessage("退回成功！");
            this.searchAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("退回失败！");
        }
    }

    /**
     * <p>方法描述： 批量审核通过 </p>
     * @MethodAuthor： pw 2023/6/17
     **/
    public void reviewBatchAction(){
        if(CollectionUtils.isEmpty(this.selectEntitys)){
            return;
        }
        try{
            List<Integer> ridList = new ArrayList<>();
            for(Object[] objArr : this.selectEntitys){
                Integer tmpRid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
                if(null != tmpRid){
                    ridList.add(tmpRid);
                }
            }
            this.tdRadCheckRptService.reviewBatchAction(ridList);
            JsfUtil.addSuccessMessage("批量审核通过成功！");
            this.searchAction();
        }catch(Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("批量审核通过失败！");
        }
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchOrgName() {
        return searchOrgName;
    }

    public void setSearchOrgName(String searchOrgName) {
        this.searchOrgName = searchOrgName;
    }

    public List<Object[]> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<Object[]> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }
}
