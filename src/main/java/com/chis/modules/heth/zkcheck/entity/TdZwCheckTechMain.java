package com.chis.modules.heth.zkcheck.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-8-20
 */
@Entity
@Table(name = "TD_ZW_CHECK_TECH_MAIN")
@SequenceGenerator(name = "TdZwCheckTechMain", sequenceName = "TD_ZW_CHECK_TECH_MAIN_SEQ", allocationSize = 1)
public class TdZwCheckTechMain implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsSimpleCode fkByCheckTypeId;
	private TsUnit fkByUnitId;
	private String checkAddr;
	private Date checkDate;
	private String checkContent;
	private String checkDesc;
	private Integer checkRst;
	private String checkExperts;
	private Integer ifConfirm;
	private String filePath;
	private String expertAdv;
	private String leaderAdv;
	private TsUnit fkByCheckUnitId;
	private Integer state;
	private Integer delMark;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	/**备案类型名称*/
	private String filiTypeName;
	/**考核表专家名称*/
	private String checkExpertNames;
	
	public TdZwCheckTechMain() {
	}

	public TdZwCheckTechMain(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwCheckTechMain")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "CHECK_TYPE_ID")			
	public TsSimpleCode getFkByCheckTypeId() {
		return fkByCheckTypeId;
	}

	public void setFkByCheckTypeId(TsSimpleCode fkByCheckTypeId) {
		this.fkByCheckTypeId = fkByCheckTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "UNIT_ID")			
	public TsUnit getFkByUnitId() {
		return fkByUnitId;
	}

	public void setFkByUnitId(TsUnit fkByUnitId) {
		this.fkByUnitId = fkByUnitId;
	}	
			
	@Column(name = "CHECK_ADDR")	
	public String getCheckAddr() {
		return checkAddr;
	}

	public void setCheckAddr(String checkAddr) {
		this.checkAddr = checkAddr;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "CHECK_DATE")			
	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}	
			
	@Column(name = "CHECK_CONTENT")	
	public String getCheckContent() {
		return checkContent;
	}

	public void setCheckContent(String checkContent) {
		this.checkContent = checkContent;
	}	
			
	@Column(name = "CHECK_DESC")	
	public String getCheckDesc() {
		return checkDesc;
	}

	public void setCheckDesc(String checkDesc) {
		this.checkDesc = checkDesc;
	}	
			
	@Column(name = "CHECK_RST")	
	public Integer getCheckRst() {
		return checkRst;
	}

	public void setCheckRst(Integer checkRst) {
		this.checkRst = checkRst;
	}	
			
	@Column(name = "CHECK_EXPERTS")	
	public String getCheckExperts() {
		return checkExperts;
	}

	public void setCheckExperts(String checkExperts) {
		this.checkExperts = checkExperts;
	}	
			
	@Column(name = "IF_CONFIRM")	
	public Integer getIfConfirm() {
		return ifConfirm;
	}

	public void setIfConfirm(Integer ifConfirm) {
		this.ifConfirm = ifConfirm;
	}	
			
	@Column(name = "FILE_PATH")	
	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}	
			
	@Column(name = "EXPERT_ADV")	
	public String getExpertAdv() {
		return expertAdv;
	}

	public void setExpertAdv(String expertAdv) {
		this.expertAdv = expertAdv;
	}	
			
	@Column(name = "LEADER_ADV")	
	public String getLeaderAdv() {
		return leaderAdv;
	}

	public void setLeaderAdv(String leaderAdv) {
		this.leaderAdv = leaderAdv;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CHECK_UNIT_ID")			
	public TsUnit getFkByCheckUnitId() {
		return fkByCheckUnitId;
	}

	public void setFkByCheckUnitId(TsUnit fkByCheckUnitId) {
		this.fkByCheckUnitId = fkByCheckUnitId;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Column(name = "DEL_MARK")	
	public Integer getDelMark() {
		return delMark;
	}

	public void setDelMark(Integer delMark) {
		this.delMark = delMark;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Transient
	public String getFiliTypeName() {
		return filiTypeName;
	}

	public void setFiliTypeName(String filiTypeName) {
		this.filiTypeName = filiTypeName;
	}

	@Transient
	public String getCheckExpertNames() {
		return checkExpertNames;
	}

	public void setCheckExpertNames(String checkExpertNames) {
		this.checkExpertNames = checkExpertNames;
	}
}