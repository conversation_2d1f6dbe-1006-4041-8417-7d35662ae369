package com.chis.modules.heth.zkcheck.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-8-13
 */
@Entity
@Table(name = "TD_ZW_CHECK_SUMMARY")
@SequenceGenerator(name = "TdZwCheckSummary", sequenceName = "TD_ZW_CHECK_SUMMARY_SEQ", allocationSize = 1)
public class TdZwCheckSummary implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsSimpleCode fkByCheckTypeId;
	private TsUnit fkByUnitId;
	private Date checkDate;
	private String checkAdv;
	private Integer ifConfirm;
	private String filePath;
	private String checkExperts;
	private String checkLeaders;
	private TsUnit fkByCheckUnitId;
	private Integer state;
	private Integer delMark;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwCheckSummary() {
	}

	public TdZwCheckSummary(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwCheckSummary")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "CHECK_TYPE_ID")			
	public TsSimpleCode getFkByCheckTypeId() {
		return fkByCheckTypeId;
	}

	public void setFkByCheckTypeId(TsSimpleCode fkByCheckTypeId) {
		this.fkByCheckTypeId = fkByCheckTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "UNIT_ID")			
	public TsUnit getFkByUnitId() {
		return fkByUnitId;
	}

	public void setFkByUnitId(TsUnit fkByUnitId) {
		this.fkByUnitId = fkByUnitId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "CHECK_DATE")			
	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}	
			
	@Column(name = "CHECK_ADV")	
	public String getCheckAdv() {
		return checkAdv;
	}

	public void setCheckAdv(String checkAdv) {
		this.checkAdv = checkAdv;
	}	
			
	@Column(name = "IF_CONFIRM")	
	public Integer getIfConfirm() {
		return ifConfirm;
	}

	public void setIfConfirm(Integer ifConfirm) {
		this.ifConfirm = ifConfirm;
	}	
			
	@Column(name = "FILE_PATH")	
	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}	
			
	@Column(name = "CHECK_EXPERTS")	
	public String getCheckExperts() {
		return checkExperts;
	}

	public void setCheckExperts(String checkExperts) {
		this.checkExperts = checkExperts;
	}	
			
	@Column(name = "CHECK_LEADERS")	
	public String getCheckLeaders() {
		return checkLeaders;
	}

	public void setCheckLeaders(String checkLeaders) {
		this.checkLeaders = checkLeaders;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CHECK_UNIT_ID")			
	public TsUnit getFkByCheckUnitId() {
		return fkByCheckUnitId;
	}

	public void setFkByCheckUnitId(TsUnit fkByCheckUnitId) {
		this.fkByCheckUnitId = fkByCheckUnitId;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Column(name = "DEL_MARK")	
	public Integer getDelMark() {
		return delMark;
	}

	public void setDelMark(Integer delMark) {
		this.delMark = delMark;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}