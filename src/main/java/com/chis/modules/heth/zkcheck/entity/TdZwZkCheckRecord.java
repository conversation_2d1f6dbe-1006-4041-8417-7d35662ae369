package com.chis.modules.heth.zkcheck.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2023-7-6
 */
@Entity
@Table(name = "TD_ZW_ZK_CHECK_RECORD")
@SequenceGenerator(name = "TdZwZkCheckRecord", sequenceName = "TD_ZW_ZK_CHECK_RECORD_SEQ", allocationSize = 1)
public class TdZwZkCheckRecord implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwZkCheckMain fkByMainId;
	private TsSimpleCode fkByTypeId;
	private Integer ifHg;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	private List<TdZwZkCheckDetails> tdZwZkCheckDetails;


	public TdZwZkCheckRecord() {
	}

	public TdZwZkCheckRecord(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwZkCheckRecord")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwZkCheckMain getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwZkCheckMain fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "TYPE_ID")			
	public TsSimpleCode getFkByTypeId() {
		return fkByTypeId;
	}

	public void setFkByTypeId(TsSimpleCode fkByTypeId) {
		this.fkByTypeId = fkByTypeId;
	}	
			
	@Column(name = "IF_HG")	
	public Integer getIfHg() {
		return ifHg;
	}

	public void setIfHg(Integer ifHg) {
		this.ifHg = ifHg;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY,  mappedBy = "fkByMainId",orphanRemoval=true)
	public List<TdZwZkCheckDetails> getTdZwZkCheckDetails() {
		return tdZwZkCheckDetails;
	}
	public void setTdZwZkCheckDetails(List<TdZwZkCheckDetails> tdZwZkCheckDetails) {
		this.tdZwZkCheckDetails = tdZwZkCheckDetails;
	}

}