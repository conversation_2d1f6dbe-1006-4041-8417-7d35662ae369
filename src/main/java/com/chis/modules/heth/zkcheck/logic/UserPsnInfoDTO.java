package com.chis.modules.heth.zkcheck.logic;

import java.io.Serializable;
import java.util.Objects;

/**
 *  <p>方法描述：用户信息表</p>
 * @MethodAuthor hsj 2024-07-08 16:09
 */

public class UserPsnInfoDTO implements Serializable {

   private Integer rid;
   private String unitname;
   private String username;
   private String zoneName;
   private Integer zoneId;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getUnitname() {
        return unitname;
    }

    public void setUnitname(String unitname) {
        this.unitname = unitname;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getZoneId() {
        return zoneId;
    }

    public void setZoneId(Integer zoneId) {
        this.zoneId = zoneId;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        UserPsnInfoDTO that = (UserPsnInfoDTO) o;
        return Objects.equals(rid, that.rid);
    }
}
