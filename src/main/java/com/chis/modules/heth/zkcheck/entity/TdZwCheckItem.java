package com.chis.modules.heth.zkcheck.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2023-3-30
 */
@Entity
@Table(name = "TD_ZW_CHECK_ITEM")
@SequenceGenerator(name = "TdZwCheckItem", sequenceName = "TD_ZW_CHECK_ITEM_SEQ", allocationSize = 1)
public class TdZwCheckItem implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwCheckTable fkByMainId;
    private TsSimpleCode fkByItemId;
    private BigDecimal checkVal;
    private BigDecimal scoreVal;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private List<TdZwCheckSub> tdZwCheckSubList;

    public TdZwCheckItem() {
    }

    public TdZwCheckItem(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwCheckItem")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwCheckTable getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwCheckTable fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "ITEM_ID")
    public TsSimpleCode getFkByItemId() {
        return fkByItemId;
    }

    public void setFkByItemId(TsSimpleCode fkByItemId) {
        this.fkByItemId = fkByItemId;
    }

    @Column(name = "CHECK_VAL")
    public BigDecimal getCheckVal() {
        return checkVal;
    }

    public void setCheckVal(BigDecimal checkVal) {
        this.checkVal = checkVal;
    }

    @Column(name = "SCORE_VAL")
    public BigDecimal getScoreVal() {
        return scoreVal;
    }

    public void setScoreVal(BigDecimal scoreVal) {
        this.scoreVal = scoreVal;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Transient
    public List<TdZwCheckSub> getTdZwCheckSubList() {
        return tdZwCheckSubList;
    }

    public void setTdZwCheckSubList(List<TdZwCheckSub> tdZwCheckSubList) {
        this.tdZwCheckSubList = tdZwCheckSubList;
    }
}