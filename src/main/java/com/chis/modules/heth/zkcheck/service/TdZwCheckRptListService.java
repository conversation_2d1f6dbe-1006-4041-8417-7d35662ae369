package com.chis.modules.heth.zkcheck.service;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckRpt;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * <AUTHOR>
 * @date 2023年03月29日
 */
@Service
@Transactional(readOnly = false)
public class TdZwCheckRptListService extends AbstractTemplate {


    /**
    * @description: 删除
    * <AUTHOR>
    * @date 2023年03月29日
    */
    public void deleteByRid(Integer rid) {
        Map<String, Object> paramMap = new HashMap<>(16);
        String sql = "DELETE FROM TD_ZW_CHECK_RPT WHERE RID = :rid";
        paramMap.put("rid", rid);
        executeSql(sql, paramMap);
    }

    /**
    * @description: 暂存方法
    * <AUTHOR>
    * @date 2023年03月29日
    */
    public void saveOrUpdateCheckRpt(TdZwCheckRpt tdZwCheckRpt) {
        if(tdZwCheckRpt==null){
            return;
        }
        tdZwCheckRpt.setFkByUnitId(Global.getUser().getTsUnit());
        this.upsertEntity(tdZwCheckRpt);
    }

    /**
     * <p>方法描述： 暂存、提交 </p>
     * @MethodAuthor： pw 2023/6/17
     **/
    public void saveOrUpdateCheckRptUnFillUnit(TdZwCheckRpt tdZwCheckRpt) {
        if(tdZwCheckRpt==null){
            return;
        }
        this.upsertEntity(tdZwCheckRpt);
    }

    /**
     * <p>方法描述： 批量审核通过 </p>
     * @MethodAuthor： pw 2023/6/17
     **/
    public void reviewBatchAction(List<Integer> ridList){
        if(CollectionUtils.isEmpty(ridList)){
            return;
        }
        String updateSql = "UPDATE TD_ZW_CHECK_RPT SET STATE=2,CHECK_PSN_ID =:checkPsnId,CHECK_DATE = TO_DATE(:checkDate,'YYYY-MM-DD'),MODIFY_DATE=sysdate,MODIFY_MANID=:checkPsnId WHERE RID IN(:ridList) ";
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("checkPsnId", Global.getUser().getRid());
        paramMap.put("checkDate",DateUtils.formatDate(new Date()));
        List<List<Integer>> groupList = StringUtils.splitListProxy(ridList, 1000);
        for(List<Integer> tmpRidList : groupList){
            paramMap.put("ridList", tmpRidList);
            this.executeSql(updateSql, paramMap);
        }
    }
}
