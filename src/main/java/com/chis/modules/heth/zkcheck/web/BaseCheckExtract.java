package com.chis.modules.heth.zkcheck.web;

import cn.hutool.core.util.URLUtil;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.heth.zkcheck.entity.TdZkExtractBhk;
import com.chis.modules.heth.zkcheck.service.BhkReportExtractService;
import com.chis.modules.heth.zkcheck.service.CheckExpertListService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.model.SelectItem;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @description:
 */
public class BaseCheckExtract extends FacesEditBean {

    protected CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    protected CheckExpertListService checkExpertListService = SpringContextHolder.getBean(CheckExpertListService.class);
    protected BhkReportExtractService bhkReportExtractService = SpringContextHolder.getBean(BhkReportExtractService.class);

    /***查询条件-地区*/
    protected List<TsZone> zoneList;
    protected String searchZoneGb;
    protected String searchZoneName;

    /***查询条件-体检机构*/
    protected String searchUnitName;
    protected String searchUnitId;

    /***查询条件-抽取类别*/
    protected String[] searchBhkType;
    protected List<TsSimpleCode> bhkTypeList;

    /***查询条件-抽取日期*/
    protected Date searchSDate;
    protected Date searchEDate;
    /***查询条件-危害因素结论*/
    protected List<TsSimpleCode> searchBhkrstList;
    protected String searchBhkrstName;
    protected String searchSelBhkrstIds;

    /***查询条件-状态*/
    protected String[] states;
    protected List<SelectItem> stateList;

    /**
     * 体检报告附件名称
     */
    protected String bhkRptFileName;

    /**
     * 胸片报告附件名称
     */
    protected String chestFileName;

    /**
     * 是否显示 体检报告附件
     */
    protected Boolean ifShowFileDownload;
    /**
     * 是否显示 听力图谱附件
     */
    protected Boolean ifShowChest;

    /**
     * 编辑/审核页面 记录rid
     */
    protected Integer rid;

    /**
     * 编辑/审核页面实体
     */
    protected TdZkExtractBhk extractBhk;

    /**
     * 抽取类别 码表extends1 1:胸片  2：电测听
     */
    protected static final String EXTENSION_TYPE_EXTENDS1_2 = "2";
    protected static final String EXTENSION_TYPE_EXTENDS1_1 = "1";

    /**
     * 是否详情页面
     */
    protected boolean ifView;

    /**
     * 当前抽取类型<pre>1: 胸片</pre><pre>2: 电测听</pre>
     */
    protected Integer extractType;

    /**文件下载*/
    protected StreamedContent streamedContent;
    /**下载路径*/
    private String filePath;
    /**下载文件名*/
    private String fileName;

    public BaseCheckExtract() {
        this.ifSQL = true;
        // 码表初始化
        initTsSimpleCode();
        // 抽取日期初始化
        this.searchSDate = DateUtils.getYearFirstDay(new Date());
        this.searchEDate = new Date();
    }

    /**
     * <p>Description：码表初始化</p>
     * <p>Author：yzz 2024-07-03 </p>
     */
    private void initTsSimpleCode() {
        // 抽取类别
        this.bhkTypeList = commService.findSimpleCodesByTypeId("5621");
        // 危害因素结论
        this.searchBhkrstList = commService.findSimpleCodesByTypeId("5005");
    }


    /**
     * <p>Description：查询体检机构 </p>
     * <p>Author： yzz 2024-06-29 </p>
     */
    public void selUnitAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 625, null, 500);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add(this.searchUnitId);
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<String>();
        paramList.add(this.searchZoneGb);
        paramMap.put("searchZoneCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
    }

    /**
     * <p>Description：处理选择的体检机构 </p>
     * <p>Author： yzz 2024-06-29 </p>
     */
    public void onSelectUnitAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (selectedMap == null || selectedMap.isEmpty()) {
            return;
        }
        List<TbTjSrvorg> list = (List<TbTjSrvorg>) selectedMap.get("selectPros");
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        StringBuffer names = new StringBuffer();
        StringBuffer ids = new StringBuffer();
        for (TbTjSrvorg t : list) {
            names.append("，").append(t.getUnitName());
            ids.append(",").append(t.getRid());
        }
        this.searchUnitId = ids.substring(1);
        this.searchUnitName = names.substring(1);
    }

    /**
     * <p>Description：清空单位 </p>
     * <p>Author： yzz 2024-06-29 </p>
     */
    public void clearUnit() {
        this.searchUnitId = null;
        this.searchUnitName = null;
    }

    @Override
    public void addInit() {

    }
    @Override
    public void viewInit() {

    }
    @Override
    public void modInit() {
        extractBhk = commService.find(TdZkExtractBhk.class, this.rid);
        // 地区去除省份
        this.extractBhk.setZoneName(ZoneUtil.removeProvByFullName(extractBhk.getFkByZoneId().getFullName()));
        //身份证号脱敏
        this.extractBhk.setIdcEncrypted(StringUtils.encryptIdc(this.extractBhk.getIdc()));
        this.ifShowFileDownload = null;
        this.ifShowChest = null;
        if (this.extractBhk.getFkByExtractTypeId() == null
                || this.extractBhk.getFkByExtractTypeId().getExtendS1() == null) {
            this.extractType = null;
            return;
        }
        pakFileInfo(1, this.extractBhk.getBhkRptPath());
        // 抽取类型
        switch (this.extractBhk.getFkByExtractTypeId().getExtendS1()) {
            case "1":
                this.extractType = 1;
                pakFileInfo(2, this.extractBhk.getChestPath());
                break;
            case "2":
                this.extractType = 2;
                pakFileInfo(3, this.extractBhk.getChestPath());
                break;
            default:
                this.extractType = null;
                break;
        }
    }


    /**
     * 封装附件信息
     *
     * @param type 附件类型<pre>1: 体检报告附件</pre><pre>2: 胸片附件</pre><pre>3: 听力图谱附件</pre>
     */
    public void pakFileInfo(int type, String path) {
        if (ObjectUtil.isEmpty(path)) {
            return;
        }
        boolean ifShowDownload;
        String suffix = "";
        if (path.lastIndexOf('.') + 1 == 0 || path.lastIndexOf('.') + 1 == path.length()) {
            ifShowDownload = true;
        } else {
            suffix = path.substring(path.lastIndexOf('.') + 1).toLowerCase();
            ifShowDownload = "zip".equals(suffix) || "rar".equals(suffix) || "dcm".equals(suffix);
            suffix = "." + suffix;
        }
        if (type == 1) {
            this.bhkRptFileName = this.extractBhk.getFkByOrgId().getUnitName() + "_" +
                    this.extractBhk.getPersonName() + "_" + "体检报告附件" + suffix;
            this.ifShowFileDownload = ifShowDownload;
        } else {
            String typeName = type == 2 ? "胸片附件" : "听力图谱附件";
            this.chestFileName = this.extractBhk.getFkByOrgId().getUnitName() + "_" +
                    this.extractBhk.getPersonName() + "_" + typeName + suffix;
            this.ifShowChest = ifShowDownload;
        }
    }

    /**
     * <p>方法描述：下载</p>
     *
     * @MethodAuthor hsj 2024-07-04 11:47
     */

    public StreamedContent getStreamedContent() {
        try {
            if(StringUtils.isBlank(this.filePath)){
                JsfUtil.addErrorMessage("文件地址不能为空！");
                return null;
            }
            String xnPath = JsfUtil.getAbsolutePath();
            String path="";
            if(this.filePath.indexOf(xnPath) != -1){
                path = this.filePath;
            }else{
                path = xnPath + this.filePath;
            }
            InputStream stream = new FileInputStream(path);
            this.streamedContent = new DefaultStreamedContent(stream, "application/pdf", URLUtil.encode(fileName, StandardCharsets.UTF_8));
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("系统找不到指定的文件！");
            return null;
        }
        return streamedContent;
    }

    public void setStreamedContent(StreamedContent streamedContent) {
        this.streamedContent = streamedContent;
    }
    @Override
    public void saveAction() {

    }
    @Override
    public String[] buildHqls() {
        return new String[0];
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }
    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }
    public String getSearchZoneGb() {
        return searchZoneGb;
    }
    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }
    public String getSearchZoneName() {
        return searchZoneName;
    }
    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }
    public String getSearchUnitName() {
        return searchUnitName;
    }
    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }
    public String getSearchUnitId() {
        return searchUnitId;
    }
    public void setSearchUnitId(String searchUnitId) {
        this.searchUnitId = searchUnitId;
    }
    public String[] getSearchBhkType() {
        return searchBhkType;
    }
    public void setSearchBhkType(String[] searchBhkType) {
        this.searchBhkType = searchBhkType;
    }
    public List<TsSimpleCode> getBhkTypeList() {
        return bhkTypeList;
    }
    public void setBhkTypeList(List<TsSimpleCode> bhkTypeList) {
        this.bhkTypeList = bhkTypeList;
    }
    public Date getSearchSDate() {
        return searchSDate;
    }
    public void setSearchSDate(Date searchSDate) {
        this.searchSDate = searchSDate;
    }
    public Date getSearchEDate() {
        return searchEDate;
    }
    public void setSearchEDate(Date searchEDate) {
        this.searchEDate = searchEDate;
    }
    public List<TsSimpleCode> getSearchBhkrstList() {
        return searchBhkrstList;
    }
    public void setSearchBhkrstList(List<TsSimpleCode> searchBhkrstList) {
        this.searchBhkrstList = searchBhkrstList;
    }
    public String getSearchBhkrstName() {
        return searchBhkrstName;
    }
    public void setSearchBhkrstName(String searchBhkrstName) {
        this.searchBhkrstName = searchBhkrstName;
    }
    public String getSearchSelBhkrstIds() {
        return searchSelBhkrstIds;
    }
    public void setSearchSelBhkrstIds(String searchSelBhkrstIds) {
        this.searchSelBhkrstIds = searchSelBhkrstIds;
    }
    public String[] getStates() {
        return states;
    }
    public void setStates(String[] states) {
        this.states = states;
    }
    public List<SelectItem> getStateList() {
        return stateList;
    }
    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public Integer getRid() {
        return rid;
    }
    public void setRid(Integer rid) {
        this.rid = rid;
    }
    public TdZkExtractBhk getExtractBhk() {
        return extractBhk;
    }
    public void setExtractBhk(TdZkExtractBhk extractBhk) {
        this.extractBhk = extractBhk;
    }

    public Boolean getIfShowFileDownload() {
        return ifShowFileDownload;
    }
    public void setIfShowFileDownload(Boolean ifShowFileDownload) {
        this.ifShowFileDownload = ifShowFileDownload;
    }
    public String getBhkRptFileName() {
        return bhkRptFileName;
    }
    public void setBhkRptFileName(String bhkRptFileName) {
        this.bhkRptFileName = bhkRptFileName;
    }
    public String getChestFileName() {
        return chestFileName;
    }
    public void setChestFileName(String chestFileName) {
        this.chestFileName = chestFileName;
    }
    public Boolean getIfShowChest() {
        return ifShowChest;
    }
    public void setIfShowChest(Boolean ifShowChest) {
        this.ifShowChest = ifShowChest;
    }

    public boolean getIfView() {
        return ifView;
    }
    public void setIfView(boolean ifView) {
        this.ifView = ifView;
    }

    public Integer getExtractType() {
        return extractType;
    }

    public void setExtractType(Integer extractType) {
        this.extractType = extractType;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}
