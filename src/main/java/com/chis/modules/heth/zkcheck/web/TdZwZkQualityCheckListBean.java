package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.utils.Global;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 *  <p>类描述：技术服务机构质量监测</p>
 * @ClassAuthor hsj 2023-03-29 10:39
 */
@ManagedBean(name="tdZwZkQualityCheckListBean")
@ViewScoped
public class TdZwZkQualityCheckListBean extends AbstractTdZwZkQualityCheckListBean {


    @Override
    public void initData() {
        this.jCCancelBtn="1".equals(PropertyUtils.getValueWithoutException("ifJCCancelBtn"));
        this.ifSQL = true;
        this.initScoreRstInfo();
        initParam();
        this.searchAction();
    }

    @Override
    public Integer pageType() {
        return 1;
    }
    /**
     *  <p>方法描述：技术服务机构质量监测 - 查询</p>
     * @MethodAuthor hsj 2023-03-29 10:49
     */
    @Override
    public String[] buildHqls() {
        StringBuffer buffer = new StringBuffer();
        buffer.append(" FROM TD_ZW_ZK_CHECK_MAIN T ");
        buffer.append(" LEFT JOIN TS_UNIT T1 ON T.ORG_ID = T1.RID ");
        buffer.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID ");
        buffer.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T3.RID = T.CHECK_RST_ID ");
        buffer.append(" WHERE T.DEL_MARK = 0 AND T.CHECK_TYPE = 4 AND T.RECORD_ORG_ID = ").append(Global.getUser().getTsUnit().getRid());
        if(StringUtils.isNotBlank(searchZoneCode)){
            buffer.append(" AND T2.ZONE_GB LIKE :zoneCode escape '\\\' ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        if(StringUtils.isNotBlank(searchOrgName)){
            buffer.append(" AND T1.UNITNAME LIKE :orgName escape '\\\' ");
            this.paramMap.put("orgName", "%" +StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchOrgName).trim()) + "%");
        }
        if(ObjectUtil.isNotNull(searchCheckStartTime)){
            buffer.append(" AND T.CHECK_DATE>= TO_DATE('").append(DateUtils.formatDate(this.searchCheckStartTime))
                    .append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if(ObjectUtil.isNotNull(searchCheckEndTime)){
            buffer.append(" AND T.CHECK_DATE<= TO_DATE('").append(DateUtils.formatDate(this.searchCheckEndTime))
                    .append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if(StringUtils.isNotBlank(selectRstIds)){
            buffer.append(" AND T.CHECK_RST_ID in ( ").append(selectRstIds).append(" )");
        }
        if(!CollectionUtils.isEmpty(searchStateMarkList)){
            StringBuffer stateBuffer = new StringBuffer();
            for(Integer state : searchStateMarkList){
                stateBuffer.append(",").append(state);
            }
            //避免以后不只是两种状态 用IN语句查询 若最终确认仅两种状态 这里可省略
            buffer.append(" AND T.STATE_MARK IN (").append(stateBuffer.substring(1)).append(") ");
        }
        String searchSql = " SELECT T.RID,CASE WHEN T2.ZONE_TYPE >2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME,'_')+1) ELSE T2.FULL_NAME END ZONE_NAME,T1.UNITNAME,T.TOTAL_SCORE_VAL,T3.CODE_NAME,T.CHECK_DATE,T.STATE_MARK  ";
        searchSql = searchSql + buffer.toString() + " ORDER BY T2.ZONE_GB,T.ORG_ID,T.CHECK_DATE desc ";
        String countSql = " SELECT COUNT(*) " + buffer.toString();
        return new String[]{searchSql, countSql};
    }

    @Override
    public void processData(List<?> list) {
    }



    /**
     *  <p>方法描述：初始化参数</p>
     * @MethodAuthor hsj 2023-03-29 13:37
     */
    private void initParam() {
        super.initParamItemMap();
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.zoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), false, "", "");
        if(!CollectionUtils.isEmpty(zoneList)){
            this.searchZoneCode = zoneList.get(0).getZoneGb();
            this.searchZoneName = zoneList.get(0).getZoneName();
        }
        /**考核结果  码表*/
        checkRstList=this.commService.findLevelSimpleCodesByTypeId("5586");
        this.checkRstMap = new HashMap<>();
        if(CollectionUtils.isEmpty(checkRstList)){
            checkRstList=new ArrayList<>();
        }else{
            for(TsSimpleCode simpleCode : this.checkRstList){
                String ext1 = simpleCode.getExtendS1();
                if(StringUtils.isNotBlank(ext1)){
                    this.checkRstMap.put(ext1, simpleCode);
                }
            }
        }

        searchStateMarkList = new ArrayList<>();
        searchStateMarkList.add(0);
        searchCheckStartTime = DateUtils.getYearFirstDay(new Date());
        searchCheckEndTime = new Date();
    }

}

