package com.chis.modules.heth.zkcheck.entity;

import javax.persistence.*;

import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;
import java.util.List;

import com.chis.modules.system.entity.TsUnit;

/**
 * <AUTHOR>
 * @createTime 2023-3-29
 */
@Entity
@Table(name = "TD_ZW_CHECK_RPT")
@SequenceGenerator(name = "TdZwCheckRpt", sequenceName = "TD_ZW_CHECK_RPT_SEQ", allocationSize = 1)
public class TdZwCheckRpt implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TbTjCrpt fkByCrptId;
    private String crptName;
    private TsZone fkByZoneId;
    private String creditCode;
    private String address;
    private TsSimpleCode fkByIndusTypeId;
    private TsSimpleCode fkByEconomyId;
    private TsSimpleCode fkByCrptSizeId;
    private String linkMan;
    private String linkPhone;
    private String workName;
    private Date rptDate;
    private String rptNo;
    private String filePath;
    private Integer state;
    private TsUnit fkByUnitId;
    private Integer createManid;
    private Date createDate;
    private Date modifyDate;
    private Integer modifyManid;

    /** +检测报告名称20230610 */
    private String rptName;
    /** +文审材料附件20230610 */
    private String sourceFilePath;
    /** +退回原因20230610 */
    private String backRan;
    /** +审核人ID20230610 */
    private TsUserInfo fkByCheckPsnId;
    /** +审核日期20230610 */
    private Date checkDate;
    /**
     * 现场考核表
     */
    private List<CheckTableVO> checkTableVOList;

    public TdZwCheckRpt() {
    }

    public TdZwCheckRpt(Integer rid) {
        this.rid = rid;
    }

    public TdZwCheckRpt(Integer rid, String rptNo) {
        this.rid = rid;
        this.rptNo = rptNo;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwCheckRpt")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_ID")
    public TbTjCrpt getFkByCrptId() {
        return fkByCrptId;
    }

    public void setFkByCrptId(TbTjCrpt fkByCrptId) {
        this.fkByCrptId = fkByCrptId;
    }

    @Column(name = "CRPT_NAME")
    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    @ManyToOne
    @JoinColumn(name = "ZONE_ID")
    public TsZone getFkByZoneId() {
        return fkByZoneId;
    }

    public void setFkByZoneId(TsZone fkByZoneId) {
        this.fkByZoneId = fkByZoneId;
    }

    @Column(name = "CREDIT_CODE")
    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    @Column(name = "ADDRESS")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @ManyToOne
    @JoinColumn(name = "INDUS_TYPE_ID")
    public TsSimpleCode getFkByIndusTypeId() {
        return fkByIndusTypeId;
    }

    public void setFkByIndusTypeId(TsSimpleCode fkByIndusTypeId) {
        this.fkByIndusTypeId = fkByIndusTypeId;
    }

    @ManyToOne
    @JoinColumn(name = "ECONOMY_ID")
    public TsSimpleCode getFkByEconomyId() {
        return fkByEconomyId;
    }

    public void setFkByEconomyId(TsSimpleCode fkByEconomyId) {
        this.fkByEconomyId = fkByEconomyId;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_SIZE_ID")
    public TsSimpleCode getFkByCrptSizeId() {
        return fkByCrptSizeId;
    }

    public void setFkByCrptSizeId(TsSimpleCode fkByCrptSizeId) {
        this.fkByCrptSizeId = fkByCrptSizeId;
    }

    @Column(name = "LINK_MAN")
    public String getLinkMan() {
        return linkMan;
    }

    public void setLinkMan(String linkMan) {
        this.linkMan = linkMan;
    }

    @Column(name = "LINK_PHONE")
    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }

    @Column(name = "WORK_NAME")
    public String getWorkName() {
        return workName;
    }

    public void setWorkName(String workName) {
        this.workName = workName;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "RPT_DATE")
    public Date getRptDate() {
        return rptDate;
    }

    public void setRptDate(Date rptDate) {
        this.rptDate = rptDate;
    }

    @Column(name = "RPT_NO")
    public String getRptNo() {
        return rptNo;
    }

    public void setRptNo(String rptNo) {
        this.rptNo = rptNo;
    }

    @Column(name = "FILE_PATH")
    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Column(name = "STATE")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @ManyToOne
    @JoinColumn(name = "UNIT_ID")
    public TsUnit getFkByUnitId() {
        return fkByUnitId;
    }

    public void setFkByUnitId(TsUnit fkByUnitId) {
        this.fkByUnitId = fkByUnitId;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Column(name = "RPT_NAME")
    public String getRptName() {
        return rptName;
    }

    public void setRptName(String rptName) {
        this.rptName = rptName;
    }

    @Column(name = "SOURCE_FILE_PATH")
    public String getSourceFilePath() {
        return sourceFilePath;
    }

    public void setSourceFilePath(String sourceFilePath) {
        this.sourceFilePath = sourceFilePath;
    }

    @Column(name = "BACK_RAN")
    public String getBackRan() {
        return backRan;
    }

    public void setBackRan(String backRan) {
        this.backRan = backRan;
    }

    @ManyToOne
    @JoinColumn(name = "CHECK_PSN_ID")
    public TsUserInfo getFkByCheckPsnId() {
        return fkByCheckPsnId;
    }

    public void setFkByCheckPsnId(TsUserInfo fkByCheckPsnId) {
        this.fkByCheckPsnId = fkByCheckPsnId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "CHECK_DATE")
    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    @Transient
    public List<CheckTableVO> getCheckTableVOList() {
        return checkTableVOList;
    }

    public void setCheckTableVOList(List<CheckTableVO> checkTableVOList) {
        this.checkTableVOList = checkTableVOList;
    }
}