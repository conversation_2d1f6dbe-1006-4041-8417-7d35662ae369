package com.chis.modules.heth.zkcheck.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-8-20
 */
@Entity
@Table(name = "TD_ZW_CHECK_CONCLUSION")
@SequenceGenerator(name = "TdZwCheckConclusion", sequenceName = "TD_ZW_CHECK_CONCLUSION_SEQ", allocationSize = 1)
public class TdZwCheckConclusion implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsSimpleCode fkByCheckTypeId;
	private TsUnit fkByUnitId;
	private Date checkDate;
	private Integer checkRst;
	private String others;
	private String summary;
	private TsSimpleCode fkByConclusionId;
	private Integer ifConfirm;
	private String filePath;
	private String expertAdv;
	private String leaderAdv;
	private TsUnit fkByCheckUnitId;
	private TdZwZkCheckMain fkByRelZkCheckId;
	private Integer state;
	private Integer delMark;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;

	/** 质量考核项目结果 */
	private List<TdZwCheckItemRst> zwCheckItemRstList;
	/** 考核结论表备案项目 */
	private List<TdZwCheckClsItem> zwCheckClsItemList;
	
	public TdZwCheckConclusion() {
	}

	public TdZwCheckConclusion(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwCheckConclusion")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "CHECK_TYPE_ID")			
	public TsSimpleCode getFkByCheckTypeId() {
		return fkByCheckTypeId;
	}

	public void setFkByCheckTypeId(TsSimpleCode fkByCheckTypeId) {
		this.fkByCheckTypeId = fkByCheckTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "UNIT_ID")			
	public TsUnit getFkByUnitId() {
		return fkByUnitId;
	}

	public void setFkByUnitId(TsUnit fkByUnitId) {
		this.fkByUnitId = fkByUnitId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "CHECK_DATE")			
	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}	
			
	@Column(name = "CHECK_RST")	
	public Integer getCheckRst() {
		return checkRst;
	}

	public void setCheckRst(Integer checkRst) {
		this.checkRst = checkRst;
	}	
			
	@Column(name = "OTHERS")	
	public String getOthers() {
		return others;
	}

	public void setOthers(String others) {
		this.others = others;
	}	
			
	@Column(name = "SUMMARY")	
	public String getSummary() {
		return summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	@ManyToOne
	@JoinColumn(name = "CONCLUSION_ID")
	public TsSimpleCode getFkByConclusionId() {
		return fkByConclusionId;
	}

	public void setFkByConclusionId(TsSimpleCode fkByConclusionId) {
		this.fkByConclusionId = fkByConclusionId;
	}

			
	@Column(name = "IF_CONFIRM")	
	public Integer getIfConfirm() {
		return ifConfirm;
	}

	public void setIfConfirm(Integer ifConfirm) {
		this.ifConfirm = ifConfirm;
	}	
			
	@Column(name = "FILE_PATH")	
	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}	
			
	@Column(name = "EXPERT_ADV")	
	public String getExpertAdv() {
		return expertAdv;
	}

	public void setExpertAdv(String expertAdv) {
		this.expertAdv = expertAdv;
	}	
			
	@Column(name = "LEADER_ADV")	
	public String getLeaderAdv() {
		return leaderAdv;
	}

	public void setLeaderAdv(String leaderAdv) {
		this.leaderAdv = leaderAdv;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CHECK_UNIT_ID")			
	public TsUnit getFkByCheckUnitId() {
		return fkByCheckUnitId;
	}

	public void setFkByCheckUnitId(TsUnit fkByCheckUnitId) {
		this.fkByCheckUnitId = fkByCheckUnitId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "REL_ZK_CHECK_ID")			
	public TdZwZkCheckMain getFkByRelZkCheckId() {
		return fkByRelZkCheckId;
	}

	public void setFkByRelZkCheckId(TdZwZkCheckMain fkByRelZkCheckId) {
		this.fkByRelZkCheckId = fkByRelZkCheckId;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Column(name = "DEL_MARK")	
	public Integer getDelMark() {
		return delMark;
	}

	public void setDelMark(Integer delMark) {
		this.delMark = delMark;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId")
	public List<TdZwCheckItemRst> getZwCheckItemRstList() {
		return zwCheckItemRstList;
	}

	public void setZwCheckItemRstList(List<TdZwCheckItemRst> zwCheckItemRstList) {
		this.zwCheckItemRstList = zwCheckItemRstList;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId")
	public List<TdZwCheckClsItem> getZwCheckClsItemList() {
		return zwCheckClsItemList;
	}

	public void setZwCheckClsItemList(List<TdZwCheckClsItem> zwCheckClsItemList) {
		this.zwCheckClsItemList = zwCheckClsItemList;
	}
}