package com.chis.modules.heth.zkcheck.entity;

import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-9-18
 */
@Entity
@Table(name = "TB_ZW_ZK_RST_RULE")
@SequenceGenerator(name = "TbZwZkRstRule", sequenceName = "TB_ZW_ZK_RST_RULE_SEQ", allocationSize = 1)
public class TbZwZkRstRule implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer checkType;
	private TsSimpleCode fkByCheckTypeId;
	private TsSimpleCode fkByCheckGrageId;
	private Integer xh;
	private String bak;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private List<TbZwZkRstRuleSub> rstRuleSubs;
	
	public TbZwZkRstRule() {
	}

	public TbZwZkRstRule(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbZwZkRstRule")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "CHECK_TYPE")	
	public Integer getCheckType() {
		return checkType;
	}

	public void setCheckType(Integer checkType) {
		this.checkType = checkType;
	}

	@ManyToOne
	@JoinColumn(name = "CHECK_TYPE_ID")
	public TsSimpleCode getFkByCheckTypeId() {
		return fkByCheckTypeId;
	}

	public void setFkByCheckTypeId(TsSimpleCode fkByCheckTypeId) {
		this.fkByCheckTypeId = fkByCheckTypeId;
	}
	@ManyToOne
	@JoinColumn(name = "CHECK_GRAGE_ID")			
	public TsSimpleCode getFkByCheckGrageId() {
		return fkByCheckGrageId;
	}

	public void setFkByCheckGrageId(TsSimpleCode fkByCheckGrageId) {
		this.fkByCheckGrageId = fkByCheckGrageId;
	}	
			
	@Column(name = "XH")	
	public Integer getXh() {
		return xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}	
			
	@Column(name = "BAK")	
	public String getBak() {
		return bak;
	}

	public void setBak(String bak) {
		this.bak = bak;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TbZwZkRstRuleSub> getRstRuleSubs() {
		return rstRuleSubs;
	}

	public void setRstRuleSubs(List<TbZwZkRstRuleSub> rstRuleSubs) {
		this.rstRuleSubs = rstRuleSubs;
	}	
			
}