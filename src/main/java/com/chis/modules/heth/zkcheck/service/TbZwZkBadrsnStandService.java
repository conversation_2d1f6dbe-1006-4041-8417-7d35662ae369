package com.chis.modules.heth.zkcheck.service;

import com.chis.modules.heth.zkcheck.entity.TbZwZkBadrsnStand;
import com.chis.modules.heth.zkcheck.entity.TbZwZkScores;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>类描述：质控考核指标Service</p>
 *
 * @ClassAuthor qrr, 2021年6月26日, TbZwZkBadrsnStandService
 */
@Service
@Transactional(readOnly = false)
public class TbZwZkBadrsnStandService extends AbstractTemplate {
    /**
     * <p>方法描述：查询启用的考核类型</p>
     *
     * @MethodAuthor qrr, 2021年6月28日, findTbZwZkBadrsnStand
     */
    @Transactional(readOnly = true)
    public List<TbZwZkBadrsnStand> findTbZwZkBadrsnStand() {
        StringBuffer hql = new StringBuffer();
        hql.append(" SELECT T FROM TbZwZkBadrsnStand T WHERE T.stateMark =1 and T.fkByCheckTypeId.ifReveal = 1 ORDER BY T.fkByCheckTypeId.num,T.fkByCheckTypeId.codeNo ");
        return this.findByHql(hql.toString(), TbZwZkBadrsnStand.class);
    }

    /**
     * <p>方法描述：</p>
     *
     * @MethodAuthor qrr, 2021年6月26日, findTbZwZkBadrsnStandList
     */
    @Transactional(readOnly = true)
    public List<TbZwZkScores> findTbZwZkScoresList(Integer checkTypeRid) {
        StringBuffer hql = new StringBuffer();
        hql.append(" SELECT T FROM TbZwZkScores T WHERE T.fkByMainId.stateMark =1");
        if (null != checkTypeRid) {
            hql.append(" AND T.fkByMainId.fkByCheckTypeId.rid =").append(checkTypeRid);
        }
        hql.append(" ORDER BY T.fkByMainId.xh,T.xh");
        List<TbZwZkScores> list = this.findByHql(hql.toString(), TbZwZkScores.class);
        if (!CollectionUtils.isEmpty(list)) {
            for (TbZwZkScores t : list) {
                t.getIndexList().size();
                t.getDeductList().size();
            }
        }
        return list;
    }

    /**
     * <p>方法描述：质控指标分值维护</p>
     *
     * @MethodAuthor hsj 2023-03-29 16:24
     */
    @Transactional(readOnly = true)
    public List<TbZwZkScores> findTbZwZkScoresListByType(Integer checkType) {
        StringBuffer hql = new StringBuffer();
        hql.append(" SELECT T FROM TbZwZkScores T WHERE T.fkByMainId.stateMark =1");
        if (null != checkType) {
            hql.append(" AND T.fkByMainId.checkType =").append(checkType);
        }
        hql.append(" ORDER BY T.fkByMainId.xh,T.xh");
        List<TbZwZkScores> list = this.findByHql(hql.toString(), TbZwZkScores.class);
        if (!CollectionUtils.isEmpty(list)) {
            for (TbZwZkScores t : list) {
                t.getIndexList().size();
                t.getDeductList().size();
            }
        }
        return list;
    }


    /**
     * @param checkTypeRid 考核类型码表id
     * @param flag         是否过滤启用状态的 true过滤 false不过滤
     * @Description: 依据考核类型获取质控指标标准维护集合
     * @MethodAuthor pw, 2021年07月7日
     */
    @Transactional(readOnly = true)
    public List<TbZwZkScores> findTbZwZkScoresListByParams(Integer checkTypeRid, boolean flag) {
        StringBuffer hql = new StringBuffer();
        hql.append(" SELECT T FROM TbZwZkScores T WHERE 1=1 ");
        if (flag) {
            hql.append(" AND T.fkByMainId.stateMark =1 ");
        }
        if (null != checkTypeRid) {
            hql.append(" AND T.fkByMainId.fkByCheckTypeId.rid =").append(checkTypeRid);
        }
        hql.append(" ORDER BY T.fkByMainId.xh,T.xh");
        List<TbZwZkScores> list = this.findByHql(hql.toString(), TbZwZkScores.class);
        if (!CollectionUtils.isEmpty(list)) {
            for (TbZwZkScores t : list) {
                t.getIndexList().size();
                t.getDeductList().size();
                t.getScoreOptionList().size();
            }
        }
        return list;
    }

    @Transactional(readOnly = true)
    public List<TbZwZkScores> findTbZwZkScoresListByMainId(Integer mainId, boolean flag) {
        StringBuffer hql = new StringBuffer();
        hql.append(" SELECT T FROM TbZwZkScores T WHERE 1=1 ");
        if (flag) {
            hql.append(" AND T.fkByMainId.stateMark =1 ");
        }
        if (null != mainId) {
            hql.append(" AND T.fkByMainId.rid =").append(mainId);
        }
        hql.append(" ORDER BY T.xh");
        List<TbZwZkScores> list = this.findByHql(hql.toString(), TbZwZkScores.class);
        if (!CollectionUtils.isEmpty(list)) {
            for (TbZwZkScores t : list) {
                t.getIndexList().size();
                t.getDeductList().size();
            }
        }
        return list;
    }
}
