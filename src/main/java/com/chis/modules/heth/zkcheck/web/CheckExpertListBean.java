package com.chis.modules.heth.zkcheck.web;

import cn.hutool.core.convert.Convert;
import com.chis.common.utils.*;
import com.chis.modules.heth.zkcheck.logic.UserPsnInfoDTO;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.utils.Global;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 报告审核与专家分配
 */
@ManagedBean(name = "checkExpertListBean")
@ViewScoped
public class CheckExpertListBean extends BaseCheckExtract {

    /**
     * 选择的结果集
     */
    private List<Object[]> selectEntitys;
    /**
     * 审核界面 审核结果
     */
    private Integer checkState;
    /**
     * 审核界面 审核意见
     */
    private String checkRst;

    /**
     * 退回原因只读
     */
    private Boolean readOnly = false;

    /**
     * 退回原因
     */
    private String backRsn;

    /**任务分配数*/
    private List<Integer> taskRids;
    /**专家账号信息*/
    private List<Object[]> bhkExperts;



    public CheckExpertListBean() {
        this.ifSQL = true;
        // 地区初始化
        initZone();
        // 初始化状态
        this.stateList = new ArrayList<>();
        this.states = new String[]{"1", "2"};
        this.stateList.add(new SelectItem("1", "待审核"));
        this.stateList.add(new SelectItem("3", "审核退回"));
        this.stateList.add(new SelectItem("2", "待分配"));
        this.stateList.add(new SelectItem("4", "已分配"));

        this.searchAction();
    }

    /**
     * <p>Description：地区初始化</p>
     * <p>Author：yzz 2024-07-03 </p>
     */
    private void initZone() {
        // 地区信息初始化
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb(), true, "", "");
        // 默认管辖地区
        this.searchZoneGb = this.zoneList.get(0).getZoneGb();
        this.searchZoneName = this.zoneList.get(0).getZoneName();
    }

    @Override
    public void modInit() {
        super.modInit();
        if (this.ifView &&  new Integer("3").equals(this.extractBhk.getState())) {
            this.backRsn = this.extractBhk.getBackRsn();
        }
    }
    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();
        sql.append(" select  T.RID, ");
        sql.append("         CASE WHEN T1.ZONE_TYPE >2 THEN SUBSTR(T1.FULL_NAME, INSTR(T1.FULL_NAME,'_')+1) ELSE T1.FULL_NAME END A1, ");
        sql.append("         T2.UNIT_NAME   as A2, ");
        sql.append("         T3.CODE_NAME   as A3, ");
        sql.append("         T.PERSON_NAME  as A4, ");
        sql.append("         T.BHK_CODE     as A5, ");
        sql.append("         T.PRT_DATE     as A6, ");
        sql.append("         T4.CODE_NAME   as A7, ");
        sql.append("         case when T3.EXTENDS1 ='1' then T5.CODE_NAME when T3.EXTENDS1 ='2' then T6.CODE_NAME end as A8, ");
        sql.append("         T.TCHBADRSNTIM as A9, ");
        sql.append("         T7.CRPT_NAME   as A10, ");
        sql.append("         T.EXTRACT_DATE as A11, ");
        sql.append("         case when nvl(T.STATE,0)>0 then 1 else 0 end A12, ");
        sql.append("         T.ORG_ID       as A13, ");
        sql.append("         T.BACK_RSN     as  A14, ");
        sql.append("         nvl(T.STATE,0) as  A15 ");
        sql.append(" from TD_ZK_EXTRACT_BHK T ");
        sql.append(" inner join TS_ZONE T1 on T.ZONE_ID=T1.RID ");
        sql.append(" inner join TB_TJ_SRVORG T2 on T.ORG_ID=T2.RID ");
        sql.append(" left join TS_SIMPLE_CODE T3 on T.EXTRACT_TYPE_ID=T3.RID ");
        sql.append(" left join TS_SIMPLE_CODE T4 on T.BHK_RST_ID=T4.RID ");
        sql.append(" left join TS_SIMPLE_CODE T5 on T.CHEST_RST_ID=T5.RID ");
        sql.append(" left join TS_SIMPLE_CODE T6 on T.HEAR_RST_ID=T6.RID ");
        sql.append(" left join TB_TJ_CRPT T7 on T.ENTRUST_CRPT_ID = T7.RID ");
        sql.append(" where T.EXTRACT_USER_ID= ").append(Global.getUser().getRid());

        // 地区
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            sql.append(" AND T1.ZONE_GB LIKE :zoneGb escape '\\\'");
            this.paramMap.put("zoneGb", ZoneUtil.zoneSelect(this.searchZoneGb.trim()) + "%");
        }
        // 体检机构
        if (StringUtils.isNotBlank(this.searchUnitId)) {
            sql.append(" AND T.ORG_ID IN (:orgId)");
            this.paramMap.put("orgId", StringUtils.string2list(this.searchUnitId, ","));
        }
        // 抽取类别
        if (ObjectUtil.isNotEmpty(this.searchBhkType)) {
            sql.append(" AND T.EXTRACT_TYPE_ID in(:extractTypeId)");
            this.paramMap.put("extractTypeId", Arrays.asList(this.searchBhkType));
        }
        // 抽取日期
        if (null != this.searchSDate) {
            sql.append(" AND T.EXTRACT_DATE >= TO_DATE(:searchSDate,'yyyy-MM-dd')");
            this.paramMap.put("searchSDate", DateUtils.formatDate(searchSDate, "yyyy-MM-dd"));
        }
        if (null != this.searchEDate) {
            sql.append(" AND T.EXTRACT_DATE <= TO_DATE(:searchEDate,'yyyy-MM-dd HH24:mi:ss')");
            this.paramMap.put("searchEDate", DateUtils.formatDate(searchEDate, "yyyy-MM-dd") + " 23:59:59");
        }
        // 危害因素结论
        if (StringUtils.isNotBlank(this.searchSelBhkrstIds)) {
            sql.append(" AND T.BHK_RST_ID IN (:bhkRstId)");
            this.paramMap.put("bhkRstId", StringUtils.string2list(this.searchSelBhkrstIds, ","));
        }
        // 状态
        if (ObjectUtil.isNotEmpty(this.states)) {
            List<String> statues = Arrays.asList(this.states);
            if(statues.contains("4")){
                if(statues.size()>1){
                    List<Integer> statueNoInclude4 = new ArrayList<>();
                    for (String statue : statues) {
                        if("4".equals(statue)){
                            continue;
                        }
                        statueNoInclude4.add(Integer.parseInt(statue));
                    }
                    sql.append(" AND (T.STATE in(:state) or T.STATE>=4) ");
                    this.paramMap.put("state", statueNoInclude4);
                }else{
                    sql.append(" AND T.STATE>=4 ");
                }
            }else{
                sql.append(" AND T.STATE in(:state)");
                this.paramMap.put("state", Arrays.asList(this.states));
            }
        }else{
            sql.append(" AND nvl(T.STATE,0)>0 ");
        }
        String searchSql = sql + " order by to_char(T.EXTRACT_DATE,'yyyy-mm-dd') desc,T1.ZONE_GB,T2.UNIT_NAME,T3.NUM,T3.CODE_NO,T.PERSON_NAME ";
        String countSql = " SELECT COUNT(*) from (" + sql + ")";
        return new String[]{searchSql, countSql};
    }


    /**
     * <p>Description：批量审核前验证 </p>
     * <p>Author： yzz 2024-07-03 </p>
     */
    public void beforeCheckAction() {
        // 审核意见,退回原因初始化
        this.checkState = 1;
        this.checkRst = null;
        if (ObjectUtil.isEmpty(this.selectEntitys)) {
            JsfUtil.addErrorMessage("请选择需要审核的数据！");
            return;
        } else {
            // 是否有待审核的数据
            boolean flag = false;
            for (Object[] obj : selectEntitys) {
                if (obj[15] == null || !"1".equals(obj[15].toString())) {
                    continue;
                }
                flag = true;
                break;
            }
            if (!flag) {
                JsfUtil.addErrorMessage("请选择待审核的数据！");
                return;
            }
        }
        RequestContext.getCurrentInstance().execute("PF('CheckConfirmDialog').show()");
        RequestContext.getCurrentInstance().update("tabView:mainForm:checkConfirmDialog");
    }

    /**
     * <p>Description：批量审核弹框-审核结果change事件 </p>
     * <p>Author： yzz 2024-07-03 </p>
     */
    public void changeCheckStateBatch() {
        if (new Integer("1").equals(this.checkState)) {
            this.checkRst = null;
        }
    }

    /**
     * <p>Description：批量审核弹框-确定 </p>
     * <p>Author： yzz 2024-07-03 </p>
     */
    public void reviewBatchAction() {
        if (null == this.checkState) {
            JsfUtil.addErrorMessage("审核意见不能为空！");
            return;
        }
        boolean passed = new Integer(1).equals(this.checkState);
        if (!passed && StringUtils.isBlank(this.checkRst)) {
            JsfUtil.addErrorMessage("退回原因不能为空！");
            return;
        }
        if (!passed && this.checkRst.length() > 200) {
            JsfUtil.addErrorMessage("退回原因长度不能超过200！");
            return;
        }
        List<Integer> rids = new ArrayList<>();
        int count = 0;
        for (Object[] obj : this.selectEntitys) {
            if (obj[15] == null || !"1".equals(obj[15].toString())) {
                continue;
            }
            rids.add(Integer.parseInt(obj[0].toString()));
        }
        try {
            count = checkExpertListService.batchUpdateStatueByRid(rids, passed ? 2 : 3, checkRst);
            JsfUtil.addSuccessMessage("审核成功" + count + "条！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("审核失败" + rids.size() + "条！");
            e.printStackTrace();
        }
        this.searchAction();
        RequestContext.getCurrentInstance().execute("PF('CheckConfirmDialog').hide();datatableOffClick();");
        RequestContext.getCurrentInstance().update("tabView:mainForm:dataTable");
    }

    /**
     * <p>Description：审核页面-审核通过 </p>
     * <p>Author： yzz 2024-07-04 </p>
     */
    public void passCheckAction() {
        try {
            List<Integer> rids = Arrays.asList(this.extractBhk.getRid());
            checkExpertListService.batchUpdateStatueByRid(rids, 2, null);
            this.backAction();
            this.searchAction();
            JsfUtil.addSuccessMessage("审核成功！");
            RequestContext.getCurrentInstance().update("tabView");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("审核失败！");
        }
    }

    /**
     * <p>Description：退回前操作 </p>
     * <p>Author： yzz 2024-07-04 </p>
     */
    public void preBackAction() {
        this.backRsn = null;
        RequestContext.getCurrentInstance().update("tabView:editForm:reasonDialog");
        RequestContext.getCurrentInstance().execute("PF('ReasonDialog').show()");
    }

    /**
     * <p>Description：退回逻辑处理 </p>
     * <p>Author： yzz 2024-07-04 </p>
     */
    public void returnBackAction() {
        if (StringUtils.isBlank(this.backRsn)) {
            JsfUtil.addErrorMessage("退回原因不允许为空！");
            return;
        } else if (this.backRsn.length() > 200) {
            JsfUtil.addErrorMessage("退回原因长度不能超过200！");
            return;
        }
        try {
            List<Integer> rids = Arrays.asList(this.extractBhk.getRid());
            checkExpertListService.batchUpdateStatueByRid(rids, 3, backRsn);
            this.backRsn = null;
            this.backAction();
            this.searchAction();
            JsfUtil.addSuccessMessage("退回成功！");
            RequestContext.getCurrentInstance().execute("PF('ReasonDialog').hide();");
            RequestContext.getCurrentInstance().update("tabView");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("退回失败！");
            e.printStackTrace();
        }
    }
    /***************************任务分配*********************************/
    /**
     *  <p>方法描述：任务分配前验证</p>
     * @MethodAuthor hsj 2024-07-08 15:42
     */
    public void beforeTaskAllocationAction(){
        this.taskRids = new ArrayList<>();
        Integer state = this.bhkReportExtractService.findTdZkExtractBhkStateByRid(this.rid);
        if (ObjectUtil.isNull(state)) {
            JsfUtil.addErrorMessage("该记录不存在，请联系管理员！");
            return;
        }
        if (!new Integer(2).equals(state)) {
            JsfUtil.addErrorMessage("该记录状态不可分配，请联系管理员！");
            return;
        }
        this.taskRids.add(this.rid);
        //验证通过-弹出人员选择框
        chooseTaskAllocationPsn();
    }
    /**
     *  <p>方法描述：批量任务分配</p>
     * @MethodAuthor hsj 2024-07-09 10:23
     */
    public void beforeBatchAllocationAction(){
        this.taskRids = new ArrayList<>();
        if (ObjectUtil.isEmpty(this.selectEntitys)) {
            JsfUtil.addErrorMessage("请选择待分配的数据！");
            return;
        }
        List<Integer> rids = new ArrayList<>();
        for(Object[] obj : this.selectEntitys){
            rids.add(Convert.toInt(obj[0]));
        }
        this.taskRids = this.bhkReportExtractService.findTdZkExtractBhkStateByRids(rids,2);
        if(CollectionUtils.isEmpty(this.taskRids)){
            JsfUtil.addErrorMessage("请选择待分配的数据！");
            return;
        }
        //验证通过-弹出人员选择框
        chooseTaskAllocationPsn();
    }
    /**
     *  <p>方法描述：打开人员选择弹出框</p>
     * @MethodAuthor hsj 2024-07-08 15:50
     */
    public void chooseTaskAllocationPsn(){
        Map<String, Object> options = MapUtils.produceDialogMap(null,746,null,490);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> tmpList = new ArrayList<>();
        tmpList.add(this.searchZoneGb);
        paramMap.put("zoneGb", tmpList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/zkcheck/selectTaskAllocationPsnList", options, paramMap);
    }
    /**
     *  <p>方法描述：人员选择</p>
     * @MethodAuthor hsj 2024-07-08 16:27
     */
    public void onTaskAllocationSelect(SelectEvent event){
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null == selectedMap || selectedMap.size() ==0) {
            return;
        }
        List<UserPsnInfoDTO> list = (List<UserPsnInfoDTO>) selectedMap.get("selected");
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        try {
            this.bhkReportExtractService.saveBhkExpertList(this.taskRids,list);
            JsfUtil.addSuccessMessage("任务分配成功"+this.taskRids.size()+"条！");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("任务分配失败"+this.taskRids.size()+"条！");
        }
        this.searchAction();
        RequestContext.getCurrentInstance().execute("datatableOffClick();");
        RequestContext.getCurrentInstance().update("tabView:mainForm:dataTable");
    }
    /**
     *  <p>方法描述：专家账号</p>
     * @MethodAuthor hsj 2024-07-09 11:29
     */
    public void  expertAccountAction(){
        this.bhkExperts = this.bhkReportExtractService.findTdZkBhkExpertByMainId(this.rid);
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:mainForm:expertAccountTable");
        dataTable.setFirst(0);
        dataTable.setRows(10);
        RequestContext.getCurrentInstance().update("tabView:mainForm:expertAccountDialog");
        RequestContext.getCurrentInstance().execute("PF('ExpertAccountDialog').show()");
    }
    /***************************任务分配*********************************/

    public List<Object[]> getSelectEntitys() {
        return selectEntitys;
    }
    public void setSelectEntitys(List<Object[]> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }

    public Integer getCheckState() {
        return checkState;
    }
    public void setCheckState(Integer checkState) {
        this.checkState = checkState;
    }
    public String getCheckRst() {
        return checkRst;
    }
    public void setCheckRst(String checkRst) {
        this.checkRst = checkRst;
    }

    public Boolean getReadOnly() {
        return readOnly;
    }
    public void setReadOnly(Boolean readOnly) {
        this.readOnly = readOnly;
    }
    public String getBackRsn() {
        return backRsn;
    }
    public void setBackRsn(String backRsn) {
        this.backRsn = backRsn;
    }

    public List<Integer> getTaskRids() {
        return taskRids;
    }

    public void setTaskRids(List<Integer> taskRids) {
        this.taskRids = taskRids;
    }

    public List<Object[]> getBhkExperts() {
        return bhkExperts;
    }

    public void setBhkExperts(List<Object[]> bhkExperts) {
        this.bhkExperts = bhkExperts;
    }
}
