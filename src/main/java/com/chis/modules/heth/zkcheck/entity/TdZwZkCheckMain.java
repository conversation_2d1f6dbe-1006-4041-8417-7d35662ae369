package com.chis.modules.heth.zkcheck.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-6-26
 */
@Entity
@Table(name = "TD_ZW_ZK_CHECK_MAIN")
@SequenceGenerator(name = "TdZwZkCheckMain", sequenceName = "TD_ZW_ZK_CHECK_MAIN_SEQ", allocationSize = 1)
public class TdZwZkCheckMain implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer checkType;
	private TsSimpleCode fkByCheckTypeId;
	private TsUnit fkByOrgId;
	private Date checkDate;
	private BigDecimal totalCheckVal;
	private BigDecimal totalScoreVal;
	private Integer stateMark;
	private TsUnit fkByRecordOrgId;
	private Integer ifNeedImprove;
	private Integer ifImproveEnd;
	private String improveFileName;
	private String improveFileAddr;
	private String writePath;
	private Integer delMark;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private List<TdZwZkCheckTable> checkTables;
	private List<TdZwZkCheckProve> checkProves;
	/**考核结果ID*/
	private TsSimpleCode fkByCheckRstId;
	/**是否合格*/
	private String ifHg;
	/**考核档案号*/
	private String checkNo;
	private String orgFz;
	private String orgAddr;
	private TdZwCheckRpt fkByJcRptId;
	private String checkTablePath;

	private Integer ifDevelop;
	private Integer reviewConclusion;
	private String improveLinkman;
	private String improvePhone;
	private Date improveDate;
	private TsUnit fkByCheckOrgId;
	private String checkOpinion;
	private TsUserInfo fkByCheckPsnId;

	//备案类别
	private List<TdZwZkCheckRecord> checkRecords;

	/**备案类别*/
	private String recordStr;
	/**证明材料附件*/
	private List<TdZwZkCheckProve> materialCheckProves;
	/**整改附件*/
	private List<TdZwZkCheckProve> improveCheckProves;

	/**考核类型id*/
	private Integer checkTypeId;

	public TdZwZkCheckMain() {
	}

	public TdZwZkCheckMain(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwZkCheckMain")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "CHECK_TYPE")	
	public Integer getCheckType() {
		return checkType;
	}

	public void setCheckType(Integer checkType) {
		this.checkType = checkType;
	}

	@ManyToOne
	@JoinColumn(name = "CHECK_TYPE_ID")
	public TsSimpleCode getFkByCheckTypeId() {
		return fkByCheckTypeId;
	}

	public void setFkByCheckTypeId(TsSimpleCode fkByCheckTypeId) {
		this.fkByCheckTypeId = fkByCheckTypeId;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TsUnit getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TsUnit fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "CHECK_DATE")			
	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}	
			
	@Column(name = "TOTAL_CHECK_VAL")	
	public BigDecimal getTotalCheckVal() {
		return totalCheckVal;
	}

	public void setTotalCheckVal(BigDecimal totalCheckVal) {
		this.totalCheckVal = totalCheckVal;
	}	
			
	@Column(name = "TOTAL_SCORE_VAL")	
	public BigDecimal getTotalScoreVal() {
		return totalScoreVal;
	}

	public void setTotalScoreVal(BigDecimal totalScoreVal) {
		this.totalScoreVal = totalScoreVal;
	}	
			
	@Column(name = "STATE_MARK")	
	public Integer getStateMark() {
		return stateMark;
	}

	public void setStateMark(Integer stateMark) {
		this.stateMark = stateMark;
	}	
			
	@ManyToOne
	@JoinColumn(name = "RECORD_ORG_ID")			
	public TsUnit getFkByRecordOrgId() {
		return fkByRecordOrgId;
	}

	public void setFkByRecordOrgId(TsUnit fkByRecordOrgId) {
		this.fkByRecordOrgId = fkByRecordOrgId;
	}	
			
	@Column(name = "IF_NEED_IMPROVE")	
	public Integer getIfNeedImprove() {
		return ifNeedImprove;
	}

	public void setIfNeedImprove(Integer ifNeedImprove) {
		this.ifNeedImprove = ifNeedImprove;
	}	
			
	@Column(name = "IF_IMPROVE_END")	
	public Integer getIfImproveEnd() {
		return ifImproveEnd;
	}

	public void setIfImproveEnd(Integer ifImproveEnd) {
		this.ifImproveEnd = ifImproveEnd;
	}	
			
	@Column(name = "IMPROVE_FILE_NAME")	
	public String getImproveFileName() {
		return improveFileName;
	}

	public void setImproveFileName(String improveFileName) {
		this.improveFileName = improveFileName;
	}	
			
	@Column(name = "IMPROVE_FILE_ADDR")	
	public String getImproveFileAddr() {
		return improveFileAddr;
	}

	public void setImproveFileAddr(String improveFileAddr) {
		this.improveFileAddr = improveFileAddr;
	}	
			
	@Column(name = "WRITE_PATH")	
	public String getWritePath() {
		return writePath;
	}

	public void setWritePath(String writePath) {
		this.writePath = writePath;
	}	
			
	@Column(name = "DEL_MARK")	
	public Integer getDelMark() {
		return delMark;
	}

	public void setDelMark(Integer delMark) {
		this.delMark = delMark;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwZkCheckTable> getCheckTables() {
		return checkTables;
	}

	public void setCheckTables(List<TdZwZkCheckTable> checkTables) {
		this.checkTables = checkTables;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwZkCheckProve> getCheckProves() {
		return checkProves;
	}

	public void setCheckProves(List<TdZwZkCheckProve> checkProves) {
		this.checkProves = checkProves;
	}
	@ManyToOne
	@JoinColumn(name="CHECK_RST_ID")
	public TsSimpleCode getFkByCheckRstId() {
		return fkByCheckRstId;
	}

	public void setFkByCheckRstId(TsSimpleCode fkByCheckRstId) {
		this.fkByCheckRstId = fkByCheckRstId;
	}
	@Column(name="IF_HG")
	public String getIfHg() {
		return ifHg;
	}

	public void setIfHg(String ifHg) {
		this.ifHg = ifHg;
	}
	@Column(name="CHECK_NO")
	public String getCheckNo() {
		return checkNo;
	}

	public void setCheckNo(String checkNo) {
		this.checkNo = checkNo;
	}
	@Column(name="ORG_FZ")
	public String getOrgFz() {
		return orgFz;
	}

	public void setOrgFz(String orgFz) {
		this.orgFz = orgFz;
	}
	@Column(name="ORG_ADDR")
	public String getOrgAddr() {
		return orgAddr;
	}

	public void setOrgAddr(String orgAddr) {
		this.orgAddr = orgAddr;
	}
	@ManyToOne
	@JoinColumn(name="JC_RPT_ID")
	public TdZwCheckRpt getFkByJcRptId() {
		return fkByJcRptId;
	}

	public void setFkByJcRptId(TdZwCheckRpt fkByJcRptId) {
		this.fkByJcRptId = fkByJcRptId;
	}

	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwZkCheckRecord> getCheckRecords() {
		return checkRecords;
	}

	public void setCheckRecords(List<TdZwZkCheckRecord> checkRecords) {
		this.checkRecords = checkRecords;
	}

	@Column(name="CHECK_TABLE_PATH")
	public String getCheckTablePath() {
		return checkTablePath;
	}

	public void setCheckTablePath(String checkTablePath) {
		this.checkTablePath = checkTablePath;
	}

	@Column(name="IF_DEVELOP")
	public Integer getIfDevelop() {
		return ifDevelop;
	}

	public void setIfDevelop(Integer ifDevelop) {
		this.ifDevelop = ifDevelop;
	}

	@Column(name="REVIEW_CONCLUSION")
	public Integer getReviewConclusion() {
		return reviewConclusion;
	}

	public void setReviewConclusion(Integer reviewConclusion) {
		this.reviewConclusion = reviewConclusion;
	}

	@Column(name="IMPROVE_LINKMAN")
	public String getImproveLinkman() {
		return improveLinkman;
	}

	public void setImproveLinkman(String improveLinkman) {
		this.improveLinkman = improveLinkman;
	}

	@Column(name="IMPROVE_PHONE")
	public String getImprovePhone() {
		return improvePhone;
	}

	public void setImprovePhone(String improvePhone) {
		this.improvePhone = improvePhone;
	}

	@Column(name="IMPROVE_DATE")
	public Date getImproveDate() {
		return improveDate;
	}

	public void setImproveDate(Date improveDate) {
		this.improveDate = improveDate;
	}

	@ManyToOne
	@JoinColumn(name="CHECK_ORG_ID")
	public TsUnit getFkByCheckOrgId() {
		return fkByCheckOrgId;
	}

	public void setFkByCheckOrgId(TsUnit fkByCheckOrgId) {
		this.fkByCheckOrgId = fkByCheckOrgId;
	}

	@Column(name="CHECK_OPINION")
	public String getCheckOpinion() {
		return checkOpinion;
	}

	public void setCheckOpinion(String checkOpinion) {
		this.checkOpinion = checkOpinion;
	}

	@ManyToOne
	@JoinColumn(name="CHECK_PSN_ID")
	public TsUserInfo getFkByCheckPsnId() {
		return fkByCheckPsnId;
	}

	public void setFkByCheckPsnId(TsUserInfo fkByCheckPsnId) {
		this.fkByCheckPsnId = fkByCheckPsnId;
	}

	@Transient
	public String getRecordStr() {
		return recordStr;
	}

	public void setRecordStr(String recordStr) {
		this.recordStr = recordStr;
	}
	@Transient
	public List<TdZwZkCheckProve> getMaterialCheckProves() {
		return materialCheckProves;
	}

	public void setMaterialCheckProves(List<TdZwZkCheckProve> materialCheckProves) {
		this.materialCheckProves = materialCheckProves;
	}
	@Transient
	public List<TdZwZkCheckProve> getImproveCheckProves() {
		return improveCheckProves;
	}

	public void setImproveCheckProves(List<TdZwZkCheckProve> improveCheckProves) {
		this.improveCheckProves = improveCheckProves;
	}

	@Transient
	public Integer getCheckTypeId() {
		return checkTypeId;
	}

	public void setCheckTypeId(Integer checkTypeId) {
		this.checkTypeId = checkTypeId;
	}
}