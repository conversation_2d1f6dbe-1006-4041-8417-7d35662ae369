package com.chis.modules.heth.zkcheck.web;

import com.chis.common.pojo.*;
import com.chis.common.utils.DynamicExcelExportUtil;
import com.chis.common.utils.JsfUtil;
import com.chis.modules.system.web.FacesBean;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Workbook;
import org.primefaces.context.RequestContext;
import org.primefaces.model.DefaultStreamedContent;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>类描述： 动态表格数据展示与导出 </p>
 * 仅一个sheet导出的情况 不考虑多sheet导出
 * @ClassAuthor： pw 2022/11/5
 **/
public abstract class DynamicShowExportBase extends FacesBean implements Serializable {
    private static final long serialVersionUID = -2953876816582804659L;
    /** 标题行list */
    protected List<DynamicRowPO> headRowList;
    /** 数据行list */
    protected List<DynamicRowPO> dataRowList;
    /** 导出的数据行是否与显示的数据行一致 */
    protected Boolean ifExportSource = Boolean.TRUE;
    /** 导出的标题行 */
    protected List<DynamicRowPO> exportHeadRowList;
    /** 导出的数据行 */
    protected List<DynamicRowPO> exportDataRowList;
    /** 用于导出的文件流 */
    protected DefaultStreamedContent downloadFile;
    /** 导出文件工具类 */
    protected DynamicExcelExportUtil excelExportUtil;
    /** 导出表格数据行居中样式 */
    protected CellStyle centerStyle;
    /** 导出表格数据行居左样式 */
    protected CellStyle leftStyle;
    /** 导出的文件名称 */
    protected String exportFileName;
    /** 大标题列数据合并列数 */
    protected Integer topColSpan;

    /**
     * <p>方法描述： 生成导出文件 </p>
     * @MethodAuthor： pw 2022/11/5
     **/
    public DefaultStreamedContent getDownloadFile() {
        ByteArrayOutputStream baos = null;
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("zwx_loading_start_pub();");
        try {
            long curTime = System.currentTimeMillis();
            //生成包含sheet以及对应sheet中标题行的Workbook
            Workbook wBook = this.generateWorkBook();
            this.centerStyle = this.excelExportUtil.customDataLeftStyle(CellStyle.ALIGN_CENTER, (short) 4, false,(short)11, false);
            //边框线
            this.centerStyle.setBorderBottom((short) 1);
            this.centerStyle.setBorderLeft((short) 1);
            this.centerStyle.setBorderRight((short) 1);
            this.centerStyle.setBorderTop((short) 1);
            this.leftStyle = this.excelExportUtil.customDataLeftStyle(CellStyle.ALIGN_LEFT, (short) 4, false,(short)11, false);
            this.leftStyle.setBorderBottom((short) 1);
            this.leftStyle.setBorderLeft((short) 1);
            this.leftStyle.setBorderRight((short) 1);
            this.leftStyle.setBorderTop((short) 1);
            //向workbook中添加数据行
            this.generateDataRowList();
            baos = new ByteArrayOutputStream();
            wBook.write(baos);
            baos.flush();
            byte[] aa = baos.toByteArray();
            String tmpName = this.exportFileName+".xlsx";
            String fileName = new String( tmpName.getBytes("GBK"),"ISO-8859-1");
            System.out.println("机构考核结果一览表导出整体用时："+(System.currentTimeMillis()-curTime)+"毫秒");
            return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
        } catch(Exception e) {
            JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            e.printStackTrace();
        } finally {
            if (baos != null) {
                try {
                    baos.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            context.execute("zwx_loading_stop_pub();");
        }
        return downloadFile;
    }

    /**
     * <p>方法描述： 辅助生成显示标题单元格 </p>
     * @MethodAuthor： pw 2022/11/7
     **/
    public DynamicColPO headColHelper(String colStrVal, Integer rowspan,
                                       Integer colspan, String style, Integer columnWidth){
        return new DynamicColPO(null, rowspan, colspan, style, null, null, null,
                colStrVal, null, null, columnWidth, null);
    }

    /**
     * <p>方法描述：辅助生成显示的普通单元格 </p>
     * @MethodAuthor： pw 2022/11/7
     **/
    public DynamicColPO normalColHelper(Integer type, Object val, Integer rowspan, Integer colspan,
                                         String style, Integer columnWidth){
        DynamicColPO colPO = new DynamicColPO();
        colPO.setType(type);
        if(null == type){}else if(2 == type){
            colPO.setColIntVal(null == val ? null : (Integer) val);
        }else if(3 == type){
            colPO.setColDecimalVal(null == val ? null : (BigDecimal) val);
        }else{
            colPO.setColStrVal(null == val ? null : val.toString());
        }
        colPO.setRowspan(rowspan);
        colPO.setColspan(colspan);
        colPO.setStyle(style);
        colPO.setColumnWidth(columnWidth);
        return colPO;
    }

    /**
     * <p>方法描述： 设定标题行特殊样式 </p>
     * @MethodAuthor： pw 2022/11/7
     **/
    public abstract void fillHeadRowStyle(List<ExportDynamicRowPO> headRowList, Workbook wBook);

    /**
     * <p>方法描述： 设定数据行特殊样式 </p>
     * @MethodAuthor： pw 2022/11/7
     **/
    public abstract void fillDataRowStyle(List<ExportDynamicRowPO> dataRowList, Workbook wBook);

    /** 设置导出文件名 */
    public abstract void generateExportFileName();

    /**
     * <p>方法描述： 准备导出的数据 </p>
     * @MethodAuthor： pw 2022/11/5
     **/
    private void prepareForExport(){
        if(!this.ifExportSource){
            return;
        }
        this.exportHeadRowList = this.headRowList;
        this.exportDataRowList = this.dataRowList;
    }

    /**
     * <p>方法描述： 生成包含sheet以及对应sheet中标题行的Workbook </p>
     * @MethodAuthor： pw 2022/11/7
     **/
    private Workbook generateWorkBook(){
        this.prepareForExport();
        if(null == this.excelExportUtil){
            this.excelExportUtil = new DynamicExcelExportUtil();
        }
        //重新创建一下 避免操作的上次导出的对象
        this.excelExportUtil.createWorkbook();
        List<ExportDynamicSheetPO> sheetList = new ArrayList<>();
        ExportDynamicSheetPO sheetPO = new ExportDynamicSheetPO();
        sheetPO.setSheetName(this.exportFileName);
        sheetPO.setHeadRowList(this.generateHeadRowList());
        sheetList.add(sheetPO);
        this.excelExportUtil.setSheetList(sheetList);
        return this.excelExportUtil.generateWorkbook();
    }

    /**
     * <p>方法描述： 生成标题行 </p>
     * @MethodAuthor： pw 2022/11/7
     **/
    private List<ExportDynamicRowPO> generateHeadRowList(){
        List<ExportDynamicRowPO> headRowList = new ArrayList<>();
        if(CollectionUtils.isEmpty(this.exportHeadRowList)){
            return Collections.EMPTY_LIST;
        }
        for(DynamicRowPO dataRowPO : this.exportHeadRowList){
            ExportDynamicRowPO exportRowPO = new ExportDynamicRowPO();
            exportRowPO.setHeightInPoints(30f);
            headRowList.add(exportRowPO);
            List<ExportDynamicColPO> cols = new ArrayList<>();
            exportRowPO.setCols(cols);
            List<DynamicColPO> dataColPOS = dataRowPO.getCols();
            ExportDynamicColPO colPO;
            for(DynamicColPO dataCol : dataColPOS){
                colPO = new ExportDynamicColPO();
                colPO.setColVal(dataCol.getColStrVal());
                colPO.setColspan(dataCol.getColspan());
                colPO.setRowspan(dataCol.getRowspan());
                if(null != dataCol.getColumnWidth()){
                    colPO.setColWidth(dataCol.getColumnWidth());
                }
                cols.add(colPO);
            }
        }
        //调整标题行样式
        this.fillHeadRowStyle(headRowList, this.excelExportUtil.getwBook());
        return headRowList;
    }

    /**
     * <p>方法描述： 生成数据行 </p>
     * @MethodAuthor： pw 2022/11/7
     **/
    private void generateDataRowList(){
        List<ExportDynamicRowPO> dataRowList = new ArrayList<>();
        for(DynamicRowPO dataRowPO : this.exportDataRowList){
            ExportDynamicRowPO exportRowPO = new ExportDynamicRowPO();
            exportRowPO.setHeightInPoints(25f);
            dataRowList.add(exportRowPO);
            List<ExportDynamicColPO> cols = new ArrayList<>();
            exportRowPO.setCols(cols);
            List<DynamicColPO> dataColPOS = dataRowPO.getCols();
            ExportDynamicColPO colPO;
            for(DynamicColPO dataCol : dataColPOS){
                colPO = new ExportDynamicColPO();
                colPO.setColVal(this.extractCellVal(dataCol));
                colPO.setColspan(dataCol.getColspan());
                colPO.setRowspan(dataCol.getRowspan());
                colPO.setColWidth(null);
                cols.add(colPO);
            }
        }
        //调整数据行样式
        this.fillDataRowStyle(dataRowList, this.excelExportUtil.getwBook());
        this.excelExportUtil.appendSheetDataByDataRow(this.excelExportUtil.getwBook().getSheetAt(0), null, true, dataRowList, false);
    }

    /**
     * <p>方法描述： 提取需要填充数据单元格的值 </p>
     * @MethodAuthor： pw 2022/11/7
     **/
    private String extractCellVal(DynamicColPO dataCol){
        Integer type = dataCol.getType();
        if(null == type){
            return "";
        }else if(1 == type){
            return dataCol.getColStrVal();
        }else if(3 == type){
            return null == dataCol.getColDecimalVal() ? "" : dataCol.getColDecimalVal().toPlainString();
        }else if(null != dataCol.getColIntVal()){
            return dataCol.getColIntVal().toString();
        }
        return "";
    }

    public List<DynamicRowPO> getHeadRowList() {
        return headRowList;
    }

    public void setHeadRowList(List<DynamicRowPO> headRowList) {
        this.headRowList = headRowList;
    }

    public List<DynamicRowPO> getDataRowList() {
        return dataRowList;
    }

    public void setDataRowList(List<DynamicRowPO> dataRowList) {
        this.dataRowList = dataRowList;
    }

    public Boolean getIfExportSource() {
        return ifExportSource;
    }

    public void setIfExportSource(Boolean ifExportSource) {
        this.ifExportSource = ifExportSource;
    }

    public List<DynamicRowPO> getExportHeadRowList() {
        return exportHeadRowList;
    }

    public void setExportHeadRowList(List<DynamicRowPO> exportHeadRowList) {
        this.exportHeadRowList = exportHeadRowList;
    }

    public List<DynamicRowPO> getExportDataRowList() {
        return exportDataRowList;
    }

    public void setExportDataRowList(List<DynamicRowPO> exportDataRowList) {
        this.exportDataRowList = exportDataRowList;
    }

    public DynamicExcelExportUtil getExcelExportUtil() {
        return excelExportUtil;
    }

    public void setExcelExportUtil(DynamicExcelExportUtil excelExportUtil) {
        this.excelExportUtil = excelExportUtil;
    }

    public CellStyle getCenterStyle() {
        return centerStyle;
    }

    public void setCenterStyle(CellStyle centerStyle) {
        this.centerStyle = centerStyle;
    }

    public CellStyle getLeftStyle() {
        return leftStyle;
    }

    public void setLeftStyle(CellStyle leftStyle) {
        this.leftStyle = leftStyle;
    }

    public String getExportFileName() {
        return exportFileName;
    }

    public void setExportFileName(String exportFileName) {
        this.exportFileName = exportFileName;
    }

    public void setDownloadFile(DefaultStreamedContent downloadFile) {
        this.downloadFile = downloadFile;
    }

    public Integer getTopColSpan() {
        return topColSpan;
    }

    public void setTopColSpan(Integer topColSpan) {
        this.topColSpan = topColSpan;
    }
}
