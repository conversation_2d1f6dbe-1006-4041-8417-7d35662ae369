package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zkcheck.service.CheckExpertListService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

@ManagedBean(name = "bhkReportProvincialReviewBean")
@ViewScoped
public class BhkReportProvincialReviewBean extends BaseCheckExtract implements IProcessData {
    private static final long serialVersionUID = 1L;
    private final CheckExpertListService expertListService = SpringContextHolder.getBean(CheckExpertListService.class);
    /**
     * 专家阅片结论
     */
    private List<TsSimpleCode> expertChestList;
    /**
     * 专家判定结论
     */
    private List<TsSimpleCode> expertHearRstList;

    public BhkReportProvincialReviewBean() {
        init();
    }

    public void init() {
        initSimpleCode();
        initZone();
        // 状态
        this.states = new String[]{"6"};
        searchAction();
    }

    private void initSimpleCode() {
        // 5598 专家阅片结论
        this.expertChestList = this.commService.findSimpleCodeListOrderByNumNo("5598");
        // 5622 专家判定结论
        this.expertHearRstList = this.commService.findSimpleCodeListOrderByNumNo("5622");
    }

    private void initZone() {
        // 地区信息初始化 管辖地区
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb(), true, "", "");
        this.searchZoneGb = this.zoneList.get(0).getZoneGb();
        this.searchZoneName = this.zoneList.get(0).getZoneName();
    }

    @Override
    public String[] buildHqls() {
        String sql = "SELECT " +
                "   EB.RID             AS P0, " +
                "   Z.FULL_NAME        AS P1, " +
                "   S.UNIT_NAME        AS P2, " +
                "   SC1.CODE_NAME      AS P3, " +
                "   EB.PERSON_NAME     AS P4, " +
                "   SC2.CODE_NAME      AS P5, " +
                "   ''                 AS P6, " +
                "   SC5.CODE_NAME      AS P7, " +
                "   ''                 AS P8, " +
                "   EB.PROV_ADVICE     AS P9, " +
                "   EB.EXTRACT_DATE    AS P10, " +
                "   EB.PROV_CHECK_DATE AS P11, " +
                "   ''                 AS P12, " +
                "   EB.STATE           AS P13, " +
                "   SC1.EXTENDS1       AS P14, " +
                "   SC3.CODE_NAME      AS P15, " +
                "   SC4.CODE_NAME      AS P16, " +
                "   SC6.CODE_NAME      AS P17, " +
                "   SC7.CODE_NAME      AS P18 ";
        String baseSql = buildQueryCriteria(this.paramMap);
        sql += baseSql + " ORDER BY TO_CHAR(EB.EXTRACT_DATE, 'yyyy-MM-dd') DESC, Z.ZONE_GB, S.UNIT_NAME, SC1.NUM, SC1.CODE_NO, EB.PERSON_NAME";
        String countSql = "SELECT COUNT(1) " + baseSql;
        return new String[]{sql, countSql};
    }

    private String buildQueryCriteria(Map<String, Object> paramMap) {
        StringBuilder baseSql = new StringBuilder();
        baseSql.append(" FROM TD_ZK_EXTRACT_BHK EB ")
                .append("    LEFT JOIN TS_ZONE Z ON EB.ZONE_ID = Z.RID ")
                .append("    LEFT JOIN TB_TJ_CRPT C ON EB.ENTRUST_CRPT_ID = C.RID ")
                .append("    LEFT JOIN TB_TJ_SRVORG S ON EB.ORG_ID = S.RID ")
                .append("    LEFT JOIN TS_SIMPLE_CODE SC1 ON EB.EXTRACT_TYPE_ID = SC1.RID ")
                .append("    LEFT JOIN TS_SIMPLE_CODE SC2 ON EB.BHK_RST_ID = SC2.RID ")
                .append("    LEFT JOIN TS_SIMPLE_CODE SC3 ON EB.EXPERT_CHEST_ID = SC3.RID ")
                .append("    LEFT JOIN TS_SIMPLE_CODE SC4 ON EB.EXPERT_HEAR_RST_ID = SC4.RID ")
                .append("    LEFT JOIN TS_SIMPLE_CODE SC5 ON EB.EXPERT_RST_ID = SC5.RID ")
                .append("    LEFT JOIN TS_SIMPLE_CODE SC6 ON EB.PROV_CHEST_ID = SC6.RID ")
                .append("    LEFT JOIN TS_SIMPLE_CODE SC7 ON EB.PROV_HEAR_RST_ID = SC7.RID ")
                .append(" WHERE 1 = 1 ");
        // 地区
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            baseSql.append(" AND Z.ZONE_GB LIKE :zoneGb escape '\\\' ");
            paramMap.put("zoneGb", ZoneUtil.zoneSelect(this.searchZoneGb.trim()) + "%");
        }
        // 体检机构
        if (StringUtils.isNotBlank(this.searchUnitId)) {
            baseSql.append(" AND EB.ORG_ID IN (:orgId) ");
            paramMap.put("orgId", StringUtils.string2list(this.searchUnitId, ","));
        }
        // 抽取类别
        if (ObjectUtil.isNotEmpty(this.searchBhkType)) {
            baseSql.append(" AND EB.EXTRACT_TYPE_ID IN (:searchBhkType) ");
            paramMap.put("searchBhkType", Arrays.asList(this.searchBhkType));
        }
        // 抽取开始日期
        if (ObjectUtil.isNotEmpty(this.searchSDate)) {
            baseSql.append(" AND EB.EXTRACT_DATE >= TO_DATE(:searchSDate, 'yyyy-MM-dd') ");
            paramMap.put("searchSDate", DateUtils.formatDate(this.searchSDate));
        }
        // 抽取结束日期
        if (ObjectUtil.isNotEmpty(this.searchEDate)) {
            baseSql.append(" AND EB.EXTRACT_DATE <= TO_DATE(:searchEDate, 'yyyy-MM-dd HH24:MI:SS') ");
            paramMap.put("searchEDate", DateUtils.formatDate(this.searchEDate) + " 23:59:59");
        }
        // 职业健康检查结论
        if (ObjectUtil.isNotEmpty(this.searchSelBhkrstIds)) {
            baseSql.append(" AND EB.BHK_RST_ID IN (:searchSelBhkrstIds) ");
            paramMap.put("searchSelBhkrstIds", StringUtils.string2list(this.searchSelBhkrstIds, ","));
        }
        // 状态
        if (ObjectUtil.isNotEmpty(this.states)) {
            baseSql.append(" AND EB.STATE IN (:states) ");
            paramMap.put("states", Arrays.asList(this.states));
        } else {
            baseSql.append(" AND EB.STATE IN ('6', '7') ");
        }
        return baseSql.toString();
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<Object[]> result = CollectionUtil.castList(Object[].class, list);
        for (Object[] obj : result) {
            // 地区
            String fullName = StringUtils.objectToString(obj[1]);
            obj[1] = fullName.substring(fullName.indexOf("_") + 1);
            // 抽取类型
            String extractType = StringUtils.objectToString(obj[14]);
            if (EXTENSION_TYPE_EXTENDS1_1.equals(extractType)) {
                obj[6] = StringUtils.objectToString(obj[15]);
                obj[8] = StringUtils.objectToString(obj[17]);
            } else if (EXTENSION_TYPE_EXTENDS1_2.equals(extractType)) {
                obj[6] = StringUtils.objectToString(obj[16]);
                obj[8] = StringUtils.objectToString(obj[18]);
            }
            // 省级复核体检结论修改意见
            String provAdviceStr = StringUtils.objectToString(obj[9]);
            switch (provAdviceStr) {
                case "1":
                    obj[9] = "同意";
                    break;
                case "2":
                    obj[9] = "不同意";
                    break;
                default:
                    obj[9] = "";
                    break;
            }
            // 状态
            String stateOri = StringUtils.objectToString(obj[13]);
            switch (stateOri) {
                case "6":
                    obj[12] = "待复核";
                    break;
                case "7":
                    obj[12] = "复核完成";
                    break;
                default:
                    obj[12] = "";
                    break;
            }
        }
    }

    @Override
    public void modInit() {
        super.modInit();
        this.ifView = !new Integer(6).equals(super.extractBhk.getState());
        initDefaultValue();
    }

    /**
     * 编辑实体初始化处理
     */
    private void initDefaultValue() {
        if (this.ifView) {
            return;
        }
        if (ObjectUtil.isNotEmpty(this.extractBhk.getFkByProvChestId())) {
            this.extractBhk.setProvChestId(this.extractBhk.getFkByProvChestId().getRid());
        }
        if (ObjectUtil.isNotEmpty(this.extractBhk.getFkByProvHearRstId())) {
            this.extractBhk.setProvHearRstId(this.extractBhk.getFkByProvHearRstId().getRid());
        }
        TsUserInfo user = Global.getUser();
        if (ObjectUtil.isEmpty(this.extractBhk.getProvUnitName())) {
            this.extractBhk.setProvUnitName(user.getTsUnit().getUnitname());
        }
        if (ObjectUtil.isEmpty(this.extractBhk.getProvCheckPsn())) {
            this.extractBhk.setProvCheckPsn(user.getUsername());
        }
        if (ObjectUtil.isEmpty(this.extractBhk.getProvCheckDate())) {
            this.extractBhk.setProvCheckDate(new Date());
        }
        if (ObjectUtil.isEmpty(this.extractBhk.getProvLinktel())) {
            this.extractBhk.setProvLinktel(user.getMbNum());
        }
    }

    /**
     * 提交前验证
     */
    public void preSubmitAction() {
        if (verify()) {
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show();");
    }

    /**
     * 提交
     */
    public void submitAction() {
        try {
            // 更新状态为“复核完成”
            this.extractBhk.setState(7);
            if (ObjectUtil.isNotEmpty(this.extractBhk.getProvChestId())) {
                this.extractBhk.setFkByProvChestId(new TsSimpleCode(this.extractBhk.getProvChestId()));
            } else {
                this.extractBhk.setFkByProvChestId(null);
            }
            if (ObjectUtil.isNotEmpty(this.extractBhk.getProvHearRstId())) {
                this.extractBhk.setFkByProvHearRstId(new TsSimpleCode(this.extractBhk.getProvHearRstId()));
            } else {
                this.extractBhk.setFkByProvHearRstId(null);
            }
            this.extractBhk.setFkByProvUserId(Global.getUser());
            this.expertListService.updateTdZkExtractBhk(this.extractBhk);
            JsfUtil.addSuccessMessage("提交成功！");
            this.searchAction();
            this.backAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
     * 验证
     *
     * @return 验证是否失败 <pre>true: 验证失败</pre><pre>false: 验证成功</pre>
     */
    public boolean verify() {
        boolean isFlag = false;
        if (new Integer(1).equals(this.extractType)) {
            if (ObjectUtil.isEmpty(this.extractBhk.getProvChestId())) {
                JsfUtil.addErrorMessage("请选择省级复核阅片结论！");
                isFlag = true;
            }
        } else if (new Integer(2).equals(this.extractType)) {
            if (ObjectUtil.isEmpty(this.extractBhk.getProvHearRstId())) {
                JsfUtil.addErrorMessage("请选择省级复核判定结论！");
                isFlag = true;
            }
        }
        if (ObjectUtil.isEmpty(this.extractBhk.getProvAdvice())) {
            JsfUtil.addErrorMessage("请选择省级复核体检结论修改意见！");
            isFlag = true;
        } else if (new Integer(2).equals(this.extractBhk.getProvAdvice())
                && ObjectUtil.isEmpty(this.extractBhk.getProvOtherRmk())) {
            JsfUtil.addErrorMessage("其他不能为空！");
            isFlag = true;
        }
        if (ObjectUtil.isEmpty(this.extractBhk.getProvUnitName())) {
            JsfUtil.addErrorMessage("复核单位不能为空！");
            isFlag = true;
        }
        if (ObjectUtil.isEmpty(this.extractBhk.getProvCheckPsn())) {
            JsfUtil.addErrorMessage("复核人不能为空！");
            isFlag = true;
        }
        Date extractDate = this.extractBhk.getExtractDate();
        if (ObjectUtil.isEmpty(this.extractBhk.getProvCheckDate())) {
            JsfUtil.addErrorMessage("复核日期不能为空！");
            isFlag = true;
        } else if (DateUtils.isCompareDate(this.extractBhk.getProvCheckDate(), ">", new Date())) {
            JsfUtil.addErrorMessage("复核日期应小于等于当天！");
            isFlag = true;
        } else if (DateUtils.isCompareDate(this.extractBhk.getProvCheckDate(), "<", extractDate)) {
            JsfUtil.addErrorMessage("复核日期应大于等于填报日期！");
            isFlag = true;
        }
        if (ObjectUtil.isEmpty(this.extractBhk.getProvLinktel())) {
            JsfUtil.addErrorMessage("联系电话不能为空！");
            isFlag = true;
        } else if (!StringUtils.vertyPhone(this.extractBhk.getProvLinktel())) {
            JsfUtil.addErrorMessage("联系电话格式不正确！");
            isFlag = true;
        }
        return isFlag;
    }

    public List<TsSimpleCode> getExpertChestList() {
        return expertChestList;
    }

    public void setExpertChestList(List<TsSimpleCode> expertChestList) {
        this.expertChestList = expertChestList;
    }

    public List<TsSimpleCode> getExpertHearRstList() {
        return expertHearRstList;
    }

    public void setExpertHearRstList(List<TsSimpleCode> expertHearRstList) {
        this.expertHearRstList = expertHearRstList;
    }
}
