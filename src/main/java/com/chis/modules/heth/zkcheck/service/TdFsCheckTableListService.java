package com.chis.modules.heth.zkcheck.service;

import com.chis.common.utils.ObjectCopyUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zkcheck.entity.TbZwZkBadrsnStand;
import com.chis.modules.heth.zkcheck.entity.TbZwZkScoreDeduct;
import com.chis.modules.heth.zkcheck.entity.TbZwZkScoreIndex;
import com.chis.modules.heth.zkcheck.entity.TbZwZkScores;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Transactional(readOnly = true)
public class TdFsCheckTableListService extends AbstractTemplate {

    /**
     * <p>Description：保存指标表 </p>
     * <p>Author： yzz 2024-11-02 </p>
     */
    public void saveStand(TbZwZkBadrsnStand addStand,Object[] standObj,Map<Integer, TsSimpleCode> checkTypeMap) {
        if (standObj!=null && standObj[2]!=null && standObj[4]!=null && (Integer.parseInt(standObj[4].toString())!=addStand.getFkByCheckTypeId().getRid() || Integer.parseInt(standObj[2].toString())!=addStand.getYear())) {
            String sql = " update TB_ZW_ZK_BADRSN_STAND set  CHECK_TYPE_ID=" + addStand.getFkByCheckTypeId().getRid()+ ",YEAR=" + addStand.getYear()+ ",MODIFY_DATE =SYSDATE,MODIFY_MANID =" + Global.getUser().getRid();
            if (checkTypeMap.containsKey(addStand.getFkByCheckTypeId().getRid()) && ("3".equals(checkTypeMap.get(addStand.getFkByCheckTypeId().getRid()).getExtendS1())) || "4".equals(checkTypeMap.get(addStand.getFkByCheckTypeId().getRid()).getExtendS1())) {
                sql += ",CHECK_TYPE=" + checkTypeMap.get(addStand.getFkByCheckTypeId().getRid()).getExtendS1();
            }else{
                sql += ",CHECK_TYPE=null";
            }
            sql += " where CHECK_TYPE_ID=" + Integer.parseInt(standObj[4].toString()) + " and YEAR" +("9999".equals(standObj[2].toString())?" is null ":"= "+Integer.parseInt(standObj[2].toString()));
            this.executeSql(sql, null);
        }
        this.upsertEntity(addStand);
    }

    /**
     * <p>Description：查询指标列表 </p>
     * <p>Author： yzz 2024-11-02 </p>
     */
    public List<TbZwZkBadrsnStand> findStandList(Integer year, Integer checkTypeId, Map<String, TsSimpleCode> standSpFlagMap) {
        String hql = "select T from TbZwZkBadrsnStand T where T.year" + (year==9999?" is null ":"="+year) + " and T.fkByCheckTypeId.rid=" + checkTypeId + " order by T.xh,T.rid desc";
        List<TbZwZkBadrsnStand> badrsnStands = this.findByHql(hql, TbZwZkBadrsnStand.class);
        if (standSpFlagMap.isEmpty()) {
            return badrsnStands;
        }
        for (TbZwZkBadrsnStand badrsnStand : badrsnStands) {
            if (badrsnStand.getSpecialFlag() != null && standSpFlagMap.containsKey(badrsnStand.getSpecialFlag())) {
                badrsnStand.setSelSpFlagId(standSpFlagMap.get(badrsnStand.getSpecialFlag()).getRid());
                badrsnStand.setSpecialFlagName(standSpFlagMap.get(badrsnStand.getSpecialFlag()).getCodeName());
            }
        }
        return this.findByHql(hql, TbZwZkBadrsnStand.class);
    }

    /**
     * <p>Description：判断该年份下是否有同类型考核表 </p>
     * <p>Author： yzz 2024-11-04 </p>
     */
    public boolean checkStand(Integer year, Integer checkTypeRid, List<Integer> rids) {
        StringBuilder sql;
        if (!CollectionUtils.isEmpty(rids)) {
            sql = new StringBuilder("SELECT COUNT(1) FROM TB_ZW_ZK_BADRSN_STAND WHERE YEAR=" + year + " AND CHECK_TYPE_ID = " + checkTypeRid);
            sql.append(" AND ( ");
            List<List<Integer>> lists = StringUtils.splitListProxy(rids, 1000);
            for (int i = 0; i < lists.size(); i++) {
                if (i == 0) {
                    sql.append(" RID not in  ( ");
                } else {
                    sql.append(" and RID not in  ( ");
                }
                List<Integer> list = lists.get(i);
                StringBuilder ridStr = new StringBuilder();
                for (Integer rid : list) {
                    ridStr.append(",").append(rid.toString());
                }
                sql.append(ridStr.substring(1)).append(" ) ");
            }
            sql.append(" ) ");
        } else {
            sql = new StringBuilder("SELECT COUNT(1) FROM TB_ZW_ZK_BADRSN_STAND WHERE YEAR=" + year + " AND CHECK_TYPE_ID=" + checkTypeRid);
        }

        return this.findCountBySql(sql.toString()) > 0;
    }

    /**
     * <p>Description：批量保存分值表 </p>
     * <p>Author： yzz 2024-11-04 </p>
     */
    public void saveScoresList(List<TbZwZkScores> scoresList) {
        if (CollectionUtils.isEmpty(scoresList)) {
            return;
        }
        for (TbZwZkScores scores : scoresList) {
            this.upsertEntity(scores);
        }
    }

    /**
     * <p>Description：获取分值数据 </p>
     * <p>Author： yzz 2024-11-04 </p>
     */
    public List<TbZwZkScores> findScoresList(Integer rid, Map<String, TsSimpleCode> specialFlagWithScoreExtends1Map, Map<String, TsSimpleCode> busExtendsWithExtends3Map) {
        String hql = " select T from TbZwZkScores T join T.indexList T1 where T.fkByMainId.rid= " + rid+" order by T.xh,T1.indexXh,T1.fkByIndexId.num,T1.fkByIndexId.codeLevelNo,T1.fkByIndexId.codeNo,T.rid desc";
        List<TbZwZkScores> scores = this.findByHql(hql, TbZwZkScores.class);
        if (CollectionUtils.isEmpty(scores)) {
            return new ArrayList<>();
        }
        for (TbZwZkScores score : scores) {
            // 项类赋值
            if (score.getFkByItemTypeId() != null && score.getFkByItemTypeId().getRid() != null) {
                score.setItemTypeRid(score.getFkByItemTypeId().getRid());
            }
            // 特殊标记
            if (!specialFlagWithScoreExtends1Map.isEmpty()
                    && score.getSpecialFlag() != null
                    && specialFlagWithScoreExtends1Map.containsKey(score.getSpecialFlag().toString())) {
                score.setSpecialFlagRid(specialFlagWithScoreExtends1Map.get(score.getSpecialFlag().toString()).getRid());
            }
            // 说明
            if (!busExtendsWithExtends3Map.isEmpty()
                    && score.getBusExtends() != null
                    && busExtendsWithExtends3Map.containsKey(score.getBusExtends())) {
                score.setBusExtendsRid(busExtendsWithExtends3Map.get(score.getBusExtends()).getRid());
            }
            // 指标 序号
            if(score.getIndexList().size()>0){
                score.setIndexXh(score.getIndexList().get(0).getIndexXh());
                score.setFkByIndexId(score.getIndexList().get(0).getFkByIndexId());
                score.setFirstIndexName(score.getIndexList().get(0).getFkByFirstIndexId()!=null?score.getIndexList().get(0).getFkByFirstIndexId().getCodeName():"");
            }
            score.getDeductList().size();
        }
        return this.findByHql(hql, TbZwZkScores.class);
    }

    /**
     * <p>Description：删除分指表以及子表 </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public void delScoreByRid(Integer scoreRid) {
        //删除存在问题
        String sql = "delete from TB_ZW_ZK_SCORE_DEDUCT T where T.MAIN_ID =" + scoreRid;
        this.executeSql(sql, null);
        // 删除质控指标评分项维护表
        sql = " delete from TB_ZW_ZK_SCORE_INDEX where MAIN_ID=" + scoreRid;
        this.executeSql(sql, null);

        // 删除指标表
        sql = " delete from TB_ZW_ZK_SCORES where rid=" + scoreRid;
        this.executeSql(sql, null);

    }
    /**
     * <p>Description：删除质控指标标准维护表 </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public void delStand(Integer rid) {
        // 删除存在问题
        String sql = "delete from TB_ZW_ZK_SCORE_DEDUCT T where exists( select 1 from TB_ZW_ZK_SCORES T1 where T.MAIN_ID = T1.RID and T1.MAIN_ID =" + rid + " )";
        this.executeSql(sql, null);
        // 删除质控指标评分项维护表
        sql = "delete from TB_ZW_ZK_SCORE_INDEX T where exists( select 1 from TB_ZW_ZK_SCORES T1 where T.MAIN_ID = T1.RID and T1.MAIN_ID =" + rid + " )";
        this.executeSql(sql, null);
        // 删除指标表
        sql = " delete from TB_ZW_ZK_SCORES where MAIN_ID=" + rid;
        this.executeSql(sql, null);
        // 删除控指标标准维护表
        sql = " delete from TB_ZW_ZK_BADRSN_STAND where RID=" + rid;
        this.executeSql(sql, null);
    }

    /**
     * <p>Description：批量存储分值表数据 </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public void saveBatchScores(List<TbZwZkScores> scoresList, Map<Integer, TsSimpleCode> itemTypeMap,
                                Map<Integer, TsSimpleCode> specialFlagWithScoreMap, Map<Integer, TsSimpleCode> busExtendsMap) {
        if (CollectionUtils.isEmpty(scoresList)) {
            return;
        }
        // 处理数据
        for (TbZwZkScores scores : scoresList) {
            // 项类赋值
            if (!itemTypeMap.isEmpty() && itemTypeMap.containsKey(scores.getItemTypeRid())) {
                scores.setFkByItemTypeId(new TsSimpleCode(scores.getItemTypeRid()));
            }else{
                scores.setFkByItemTypeId(null);
            }
            // 特殊标记 判断码表是否存在 且 ExtendS1不等于空
            if (!specialFlagWithScoreMap.isEmpty()
                    && specialFlagWithScoreMap.containsKey(scores.getSpecialFlagRid())
                    && StringUtils.isNotBlank(specialFlagWithScoreMap.get(scores.getSpecialFlagRid()).getExtendS1())) {
                scores.setSpecialFlag(Integer.parseInt(specialFlagWithScoreMap.get(scores.getSpecialFlagRid()).getExtendS1()));
            }else{
                scores.setSpecialFlag(null);
            }
            // 说明 判断码表是否存在 且 ExtendS3不等于空
            if (!busExtendsMap.isEmpty()
                    && busExtendsMap.containsKey(scores.getBusExtendsRid())
                    && StringUtils.isNotBlank(busExtendsMap.get(scores.getBusExtendsRid()).getExtendS3())) {
                scores.setBusExtends(busExtendsMap.get(scores.getBusExtendsRid()).getExtendS3().substring(0, Math.min(busExtendsMap.get(scores.getBusExtendsRid()).getExtendS3().length(),50)));
            }else{
                scores.setBusExtends(null);
            }
            // 指标序号赋值
            if (scores.getIndexList().size()>0) {
                scores.getIndexList().get(0).setXh(scores.getXh());
                scores.getIndexList().get(0).setIndexXh(scores.getIndexXh());
                scores.getIndexList().get(0).setFkByIndexId(scores.getFkByIndexId());
            }
            this.upsertEntity(scores);
        }
    }

    /**
     * <p>Description：通过STAND表rid查询关联的指标rid </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public List<Object> findScoresRidsByStandRid(TbZwZkBadrsnStand selStand) {
        String sql = " select T.INDEX_ID from TB_ZW_ZK_SCORE_INDEX T where exists(select 1 from TB_ZW_ZK_SCORES T1" +
                " inner join TB_ZW_ZK_BADRSN_STAND T2 on T1.MAIN_ID=T2.rid where T.MAIN_ID = T1.RID and T2.YEAR" + (selStand.getYear()==null?" is null":"="+selStand.getYear()) + " AND T2.CHECK_TYPE_ID=" + selStand.getFkByCheckTypeId().getRid()+ ") ";
        return this.findDataBySqlNoPage(sql, null);
    }

    /**
     * <p>Description： 保存</p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public void updateStand(Object[] standObj, List<TbZwZkScores> scoresList, Integer checkTypeRid, Integer year, Map<Integer, TsSimpleCode> itemTypeMap, Map<Integer, TsSimpleCode> specialFlagWithScoreMap, Map<Integer, TsSimpleCode> busExtendsMap, Map<Integer, TsSimpleCode> checkTypeMap) {
        if (standObj==null) {
            return;
        }
        if (standObj[2]!=null && standObj[4]!=null && (Integer.parseInt(standObj[4].toString())!=checkTypeRid || Integer.parseInt(standObj[2].toString())!=year)) {
            String sql = " update TB_ZW_ZK_BADRSN_STAND set  CHECK_TYPE_ID=" + checkTypeRid+ ",YEAR=" + year+ ",MODIFY_DATE =SYSDATE,MODIFY_MANID =" + Global.getUser().getRid();
            if (checkTypeMap.containsKey(checkTypeRid) && ("3".equals(checkTypeMap.get(checkTypeRid).getExtendS1())) || "4".equals(checkTypeMap.get(checkTypeRid).getExtendS1())) {
                sql += ",CHECK_TYPE=" + checkTypeMap.get(checkTypeRid).getExtendS1();
            }else{
                sql += ",CHECK_TYPE=null";
            }
            sql += " where CHECK_TYPE_ID=" + Integer.parseInt(standObj[4].toString()) + " and YEAR" +("9999".equals(standObj[2].toString())?" is null ":"= "+Integer.parseInt(standObj[2].toString()));
            this.executeSql(sql, null);
        }
        this.saveBatchScores(scoresList, itemTypeMap, specialFlagWithScoreMap, busExtendsMap);
    }
    /**
     * <p>Description：保存存在问题 </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public void saveDeduct(List<TbZwZkScoreDeduct> scoreDeductList) {
        if (CollectionUtils.isEmpty(scoreDeductList)) {
            return;
        }
        for (TbZwZkScoreDeduct tbZwZkScoreDeduct : scoreDeductList) {
            this.upsertEntity(tbZwZkScoreDeduct);
        }
    }

    /**
     * <p>Description：通过分值表查询关联的存在问题 </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public List<Object> findDeductRidsByScoreRid(Integer rid) {
        String sql = " select T.DEDUCT_ID from TB_ZW_ZK_SCORE_DEDUCT T where T.MAIN_ID= " + rid;
        return this.findDataBySqlNoPage(sql, null);
    }

    /**
     * <p>Description：通过分值表rid获取存在问题 </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public List<TbZwZkScoreDeduct> findDeductByMainId(Integer rid) {
        String hql = "select T from  TbZwZkScoreDeduct T where T.fkByMainId.rid=" + rid + " order by T.xh,T.fkByDeductId.num,T.fkByDeductId.codeLevelNo,T.fkByDeductId.codeNo";
        return this.findByHql(hql, TbZwZkScoreDeduct.class);
    }

    /**
     * <p>Description：删除存在问题 </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public void delDeductByRid(Integer deductRid) {
        String sql = "delete from TB_ZW_ZK_SCORE_DEDUCT T where T.RID =" + deductRid;
        this.executeSql(sql, null);
    }
    
    /**
    * <p>Description：复制功能 </p>
    * <p>Author： yzz 2024-11-05 </p>
    */
    public void copyTableList(Object[] standObj) {
        StringBuilder hql =new StringBuilder( "select T from TbZwZkBadrsnStand T where T.year" + ("9999".equals(standObj[2].toString())?" is null ":"="+standObj[2]) + " and T.fkByCheckTypeId.rid=" + Integer.parseInt(standObj[4].toString()));
        List<TbZwZkBadrsnStand> badrsnStands = this.findByHql(hql.toString(), TbZwZkBadrsnStand.class);
        List<Integer> standRids = new ArrayList<>();
        for (TbZwZkBadrsnStand badrsnStand : badrsnStands) {
            standRids.add(badrsnStand.getRid());
        }
        hql=new StringBuilder("select T from TbZwZkScores T where 1=1 ");
        List<List<Integer>> lists = StringUtils.splitListProxy(standRids, 500);
        hql.append(" AND ( ");
        if (CollectionUtils.isEmpty(lists)) {
            hql.append(" 1=2 ");
        } else {
            for (int i = 0; i < lists.size(); i++) {
                if (i == 0) {
                    hql.append(" T.fkByMainId.rid in  ( ");
                } else {
                    hql.append(" and T.fkByMainId.rid in  ( ");
                }
                List<Integer> list = lists.get(i);
                StringBuilder rids = new StringBuilder();
                for (Integer rid : list) {
                    rids.append(",").append(rid);
                }
                hql.append(rids.substring(1)).append(" ) ");
            }
        }
        hql.append(" ) ");
        List<TbZwZkScores> scoresList = this.findByHql(hql.toString(), TbZwZkScores.class);
        Map<Integer,List<TbZwZkScores>> map = new HashMap<>();
        if(!CollectionUtils.isEmpty(scoresList)){
            for (TbZwZkScores scores : scoresList) {
                scores.getIndexList().size();
                scores.getDeductList().size();
                if(!map.containsKey(scores.getFkByMainId().getRid())){
                    map.put(scores.getFkByMainId().getRid(),new ArrayList<TbZwZkScores>());
                }
                map.get(scores.getFkByMainId().getRid()).add(scores);
            }
        }
        for (TbZwZkBadrsnStand badrsnStand : badrsnStands) {
            List<TbZwZkScores> scores=new ArrayList<>();
            if(map.containsKey(badrsnStand.getRid())){
                scores = map.get(badrsnStand.getRid());
            }
            this.preEntity(badrsnStand);
            TbZwZkBadrsnStand newBadrsnStand=new TbZwZkBadrsnStand();
            copyPropertiesExclude(badrsnStand,newBadrsnStand,new String[]{"rid","year"});
            this.saveObj(newBadrsnStand);

            //保存分值表
            List<TbZwZkScores> newScoresList=new ArrayList<>();
            if(!CollectionUtils.isEmpty(scores)){
                newScoresList=ObjectCopyUtil.deepCopy(scores);
            }
            for (TbZwZkScores tbZwZkScores : newScoresList) {
                tbZwZkScores.setFkByMainId(newBadrsnStand);
                this.preEntity(tbZwZkScores);
                TbZwZkScores newScores=new TbZwZkScores();
                copyPropertiesExclude(tbZwZkScores,newScores,new String[]{"rid"});
                List<TbZwZkScoreIndex> newIndexList=new ArrayList<>();
                if(!CollectionUtils.isEmpty(tbZwZkScores.getIndexList())){
                    for (TbZwZkScoreIndex scoreIndex : tbZwZkScores.getIndexList()) {
                        scoreIndex.setFkByMainId(newScores);
                        this.preEntity(scoreIndex);
                        TbZwZkScoreIndex newIndex=new TbZwZkScoreIndex();
                        copyPropertiesExclude(scoreIndex,newIndex,new String[]{"rid"});
                        newIndexList.add(newIndex);
                    }
                }
                List<TbZwZkScoreDeduct> newDeductList=new ArrayList<>();
                if(!CollectionUtils.isEmpty(tbZwZkScores.getDeductList())){
                    for (TbZwZkScoreDeduct scoreDeduct : tbZwZkScores.getDeductList()) {
                        scoreDeduct.setFkByMainId(newScores);
                        this.preEntity(scoreDeduct);
                        TbZwZkScoreDeduct newDeduct=new TbZwZkScoreDeduct();
                        copyPropertiesExclude(scoreDeduct,newDeduct,new String[]{"rid"});
                        newDeductList.add(newDeduct);
                    }
                }
                newScores.setIndexList(newIndexList);
                newScores.setDeductList(newDeductList);
                this.saveObj(newScores);
            }
        }
    }

    /**
    * <p>Description：拷贝 </p>
    * <p>Author： yzz 2024-11-06 </p>
    */
    public void copyPropertiesExclude(Object from, Object to, String[] excludsArray) {
        try{
            ObjectCopyUtil.copyPropertiesExclude(from,to,excludsArray);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
    * <p>Description： 查询是否有指标为空的评估表</p>
    * <p>Author： yzz 2024-11-06 </p>
    */
    public List<Object[]> findScoresCountByRids(List<Integer> rids) {
        Map<String, Object> paramMap = new HashMap<>();
        StringBuilder sql=new StringBuilder();
        sql.append(" select * ");
        sql.append(" from (select T.RID,T.CHECK_NAME, count(T1.RID) countNum ");
        sql.append("         from TB_ZW_ZK_BADRSN_STAND T ");
        sql.append("         left join TB_ZW_ZK_SCORES T1 on T.RID = T1.MAIN_ID ");
        sql.append("         where T.RID in (:list) ");
        sql.append("         group by T.RID,T.CHECK_NAME) TT ");
        sql.append(" where TT.countNum = 0 ");
        paramMap.put("list",rids);
        return this.findDataBySqlNoPage(sql.toString(),paramMap);
    }
    
    /**
    * <p>Description：存在问题 </p>
    * <p>Author： yzz 2024-11-06 </p>
    */
    public List<Object[]> findDeductCountByRids(List<Integer> rids) {
        Map<String, Object> paramMap = new HashMap<>();
        StringBuilder sql=new StringBuilder();
        sql.append(" select TT.RID,TT.CHECK_NAME ");
        sql.append(" from (select TT.RID,TT.CHECK_NAME,T.RID as SCORESRID, count(T1.RID) countNum ");
        sql.append("         from TB_ZW_ZK_BADRSN_STAND TT ");
        sql.append("         left join TB_ZW_ZK_SCORES T on TT.RID = T.MAIN_ID ");
        sql.append("         left join TB_ZW_ZK_SCORE_DEDUCT T1 on T.RID = T1.MAIN_ID ");
        sql.append("         where T.MAIN_ID in (:list) ");
        sql.append(" group by TT.RID,TT.CHECK_NAME,T.RID) TT ");
        sql.append(" where TT.countNum = 0 ");
        sql.append(" group by TT.RID,TT.CHECK_NAME ");
        paramMap.put("list",rids);
        return this.findDataBySqlNoPage(sql.toString(),paramMap);
    }
}
