package com.chis.modules.heth.zkcheck.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsMenu;

/**
 * 职业卫生平台-通用模块菜单
 */
public class HethMenuPluginObj {

    public static Set<TsMenu> menuSet;

    /**
     * 菜单默认图标
     */
    private static final String DEFAULT_MENU_ICON = "default.png";

    static {
        menuSet = new HashSet<TsMenu>();
        /*
        menuSet.add(new TsMenu("058","质控考核","heth_zkcheck_zkkh","质控考核", Short.valueOf("0"),"#","default.png","MYGH-B-045.png",new Date(),1,1));
	    menuSet.add(new TsMenu("058.001","现场考核管理","heth_zkcheck_zkkhtb","现场考核管理", Short.valueOf("1"),"/webapp/heth/zkcheck/tdZwZkCheckMainList.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,1));
	    menuSet.add(new TsMenu("058.005","质控考核情况查询","heth_zkcheck_zkkhqkcx","质控考核情况查询", Short.valueOf("1"),"/webapp/heth/zkcheck/inquiryExamStatistics.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,5));

        menuSet.add(new TsMenu("058.005","质量控制考核汇总录入","heth_qualityControl_zlkzkhhzlr","质量控制考核汇总录入", Short.valueOf("1"),"/webapp/heth/zkcheck/tdZwCheckSummaryList.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,5));
        menuSet.add(new TsMenu("058.006","质量控制技能考核录入","heth_qualityControl_zlkzjnkhlr","质量控制技能考核录入", Short.valueOf("1"),"/webapp/heth/zkcheck/qcSkillCheckInputList.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,6));
        menuSet.add(new TsMenu("058.007","质量控制考核结论录入","heth_qualityControl_zlkzkhjhlr","质量控制考核结论录入", Short.valueOf("1"),"/webapp/heth/zkcheck/tdZwCheckConclusionList.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,6));

        menuSet.add(new TsMenu("060.005","机构质控情况统计","heth_zkcheck_jgzkqktj","机构质控情况统计", Short.valueOf("1"),"/webapp/heth/zkcheck/orgInquiryExamStatistics.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,5));
        menuSet.add(new TsMenu("060.006","机构考核结果一览表","heth_zkcheck_jgkhjgylb","机构考核结果一览表", Short.valueOf("1"),"/webapp/heth/zkcheck/orgExamResultOverView.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,6));
        //技术服务机构质控考核业务
        menuSet.add(new TsMenu("060.007","技术服务机构质量监测","heth_zkcheck_jsfwjgzljc","技术服务机构质量监测", Short.valueOf("1"),"/webapp/heth/zkcheck/tdZwZkQualityCheckList.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,7));
        menuSet.add(new TsMenu("060.008","用人单位现场核查","heth_zkcheck_yrdwxchc","用人单位现场核查", Short.valueOf("1"),"/webapp/heth/zkcheck/crptOnSiteVerifyList.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,8));
        
        //不再使用
        menuSet.add(new TsMenu("058.009","检测报告上传","heth_zkcheck_jcbgsc","检测报告上传", Short.valueOf("1"),"/webapp/heth/zkcheck/tdZwCheckRptList.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,6));
        
        //技术服务机构检测报告质控管理
        menuSet.add(new TsMenu("058.005","职业卫生技术服务机构检测报告上传","heth_zwcheckrpt_jcbgsc","职业卫生技术服务机构检测报告上传", Short.valueOf("1"),"/webapp/heth/zkcheck/tdZwCheckRptUploadList.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,6));
        menuSet.add(new TsMenu("058.006","职业卫生技术服务机构检测报告审核","heth_zwcheckaudio_jcbgsc","职业卫生技术服务机构检测报告审核", Short.valueOf("1"),"/webapp/heth/zkcheck/tdZwCheckRptUploadAudioList.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,7));
        menuSet.add(new TsMenu("058.007","放射卫生技术服务机构检测报告上传","heth_radcheckrpt_jcbgsc","放射卫生技术服务机构检测报告上传", Short.valueOf("1"),"/webapp/heth/zkcheck/tdRadCheckRptUploadList.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,8));
        menuSet.add(new TsMenu("058.008","放射卫生技术服务机构检测报告审核","heth_radcheckaudio_jcbgsc","放射卫生技术服务机构检测报告审核", Short.valueOf("1"),"/webapp/heth/zkcheck/tdRadCheckRptUploadAudioList.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,9));

        */
        //现场质控个案抽取（吉林）
        //menuSet.add(new TsMenu("206", "现场质控个案抽取", "zk_check_xczkgacq", "现场质控个案抽取", Short.valueOf("0"), "#", "perf-indicators.png", "MYGH-B-048.png", new Date(), 1, 206));
        //menuSet.add(new TsMenu("206.001","体检报告抽取","heth_zkcheck_tjbgcq","体检报告抽取", Short.valueOf("1"),"/webapp/heth/zkcheck/bhkReportExtract.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,1));
        //menuSet.add(new TsMenu("206.002","体检报告抽取查询","heth_zkcheck_tjbgcqcx","体检报告抽取查询", Short.valueOf("1"),"/webapp/heth/zkcheck/bhkReportExtractSearch.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,2));
        //现场质控个案抽取（江苏）
        //menuSet.add(new TsMenu("206", "现场质控个案抽取", "zk_check_xczkgacq", "现场质控个案抽取", Short.valueOf("0"), "#", "perf-indicators.png", "MYGH-B-048.png", new Date(), 1, 206));
        //menuSet.add(new TsMenu("206.001", "体检报告抽取", "heth_zkcheck_tjbgcq", "体检报告抽取", Short.valueOf("1"), "/webapp/heth/zkcheck/bhkReportExtract.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 1));
        //menuSet.add(new TsMenu("206.002", "体检报告上传", "heth_zkcheck_tjbgsc", "体检报告上传", Short.valueOf("1"), "/webapp/heth/zkcheck/bhkReportUploadSearch.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 2));
        //menuSet.add(new TsMenu("206.003", "报告审核与专家分配", "heth_zkcheck_bgshyzjfp", "报告审核与专家分配", Short.valueOf("1"), "/webapp/heth/zkcheck/checkExpertList.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 3));
        //menuSet.add(new TsMenu("206.004", "专家调查", "heth_zkcheck_zjdc", "专家调查", Short.valueOf("1"), "/webapp/heth/zkcheck/bhkExpertSurveyList.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 4));
        //menuSet.add(new TsMenu("206.005", "省级复核", "heth_zkcheck_sjfh", "省级复核", Short.valueOf("1"), "/webapp/heth/zkcheck/bhkExpertProvincialReviewSearch.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 5));

        //【内蒙放卫质量】放射卫生技术服务机构监测任务发布
        //menuSet.add(new TsMenu("207", "放射卫生技术服务机构监测任务发布", "zk_checkJs_srvorg", "放射卫生技术服务机构监测任务发布", Short.valueOf("0"), "#", "perf-indicators.png", "MYGH-B-048.png", new Date(), 1, 206));
        //menuSet.add(new TsMenu("207.001", "放射卫生技术服务机构监测任务发布", "zk_checkJs_srvorg_planpublish", "放射卫生技术服务机构监测任务发布", Short.valueOf("1"), "/webapp/heth/zkcheck/srvorgPlanPublish.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 1));
        //menuSet.add(new TsMenu("207.003", "专家库维护", "zk_checkjs_expert", "专家库维护", Short.valueOf("1"), "/webapp/heth/zkcheck/expertMaintenanceList.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 1));
        //menuSet.add(new TsMenu("207.003", "质控考核表维护", "zk_checkjs_check_table", "质控考核表维护", Short.valueOf("1"), "/webapp/heth/zkcheck/tdFsCheckTableList.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 11));

        /*menuSet.add(new TsMenu("058.006","机构质控结果评判","heth_zkcheck_zkkhqkcx","机构质控结果评判", Short.valueOf("1"),"/webapp/heth/zkcheck/tdZwZkCheckJudgeList.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,6));*/
    }
}
