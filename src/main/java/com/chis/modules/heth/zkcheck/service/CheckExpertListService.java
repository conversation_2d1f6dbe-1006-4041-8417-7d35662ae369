package com.chis.modules.heth.zkcheck.service;

import com.chis.common.utils.DateUtils;
import com.chis.modules.heth.zkcheck.entity.TdZkExtractBhk;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 */

@Service
@Transactional(readOnly = false)
public class CheckExpertListService extends AbstractTemplate {

    /**
     * 更新个案抽取主表
     *
     * @param extractBhk 个案抽取实体
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTdZkExtractBhk(TdZkExtractBhk extractBhk) {
        this.upsertEntity(extractBhk);
    }


    /**
    * <p>Description：根据rid更新审核信息 </p>
    * <p>Author： yzz 2024-07-03 </p>
    */
    public int batchUpdateStatueByRid(List<Integer> rids, int statue, String checkRst) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid",rids);
        paramMap.put("checkRst",checkRst);
        paramMap.put("statue",statue);
        paramMap.put("upPsn", Global.getUser().getRid());
        paramMap.put("upDate", DateUtils.formatDateTime(new Date()));

        return this.executeSql(" UPDATE TD_ZK_EXTRACT_BHK SET BACK_RSN=:checkRst,STATE=:statue,MODIFY_DATE=to_date(:upDate,'yyyy-mm-dd hh24:mi:ss'),MODIFY_MANID=:upPsn WHERE RID in (:rid) ",paramMap);
    }
}
