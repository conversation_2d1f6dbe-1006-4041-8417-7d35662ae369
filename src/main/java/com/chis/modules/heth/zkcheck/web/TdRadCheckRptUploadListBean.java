package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zkcheck.entity.TdRadCheckRpt;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.UploadedFile;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * <p>类描述： 放射卫生技术服务机构检测报告上传 </p>
 * @ClassAuthor： pw 2023/6/17
 **/
@ManagedBean(name = "tdRadCheckRptUploadListBean")
@ViewScoped
public class TdRadCheckRptUploadListBean extends ZwAndRadCheckRptUploadCommBean{
    /** 查询条件：单位名称 */
    private String searchCrptName;
    /** 编辑页 用于区别检测报告附件与文审材料附件 0检测报告附件 1文审材料附件 */
    private Integer uploadType;

    public TdRadCheckRptUploadListBean(){
        this.rptType = 1;
        //状态初始化
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem("0", "待提交"));
        this.stateList.add(new SelectItem("1", "待审核"));
        this.stateList.add(new SelectItem("3", "已退回"));
        this.stateList.add(new SelectItem("2", "审核通过"));
        //默认选中待提交 已退回
        this.states = new String[]{"0","3"};
        this.readOnly = Boolean.TRUE;
        this.searchAction();
    }

    @Override
    public void addInit() {
        this.radCheckRpt = new TdRadCheckRpt();
        this.radCheckRpt.setRptDate(new Date());
    }

    @Override
    public void modInit() {
        this.initCheckRpt();
    }

    @Override
    public void saveAction() {
        if(!this.validateBeforeSave(false)){
            return;
        }
        try{
            if(null == this.radCheckRpt.getRid()){
                this.radCheckRpt.setState(0);
            }
            this.radCheckRpt.setFkByCheckUnitId(Global.getUser().getTsUnit());
            this.tdRadCheckRptService.saveOrUpdateCheckRpt(this.radCheckRpt);
            RequestContext.getCurrentInstance().update("tabView");
            JsfUtil.addSuccessMessage("暂存成功！");
            this.searchAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("暂存失败！");
        }
    }

    @Override
    public String[] buildHqls() {
        StringBuilder sqlBuffer = new StringBuilder();
        sqlBuffer.append(" SELECT T.RID,T.RPT_NO,T.RPT_NAME, ")
                .append(" CASE WHEN T2.ZONE_TYPE >2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME,'_')+1) ELSE T2.FULL_NAME END ZONE_NAME, ")
                .append(" T.CRPT_NAME,T.LINK_MAN,T.LINK_PHONE,T.RPT_DATE,T.STATE,T.BACK_RAN ")
                .append(" FROM TD_RAD_CHECK_RPT T ")
                .append(" LEFT JOIN TS_ZONE T2 ON T.ZONE_ID=T2.RID ")
                .append(" WHERE T.CHECK_UNIT_ID=").append(Global.getUser().getTsUnit().getRid());

        //报告日期
        if (null != this.searchReportBeginDate) {
            sqlBuffer.append("   AND T.RPT_DATE >= TO_DATE(:searchReportBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchReportBeginDate", DateUtils.formatDate(this.searchReportBeginDate) + " 00:00:00");
        }
        if (null != this.searchReportEndDate) {
            sqlBuffer.append("   AND T.RPT_DATE <= TO_DATE(:searchReportEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchReportEndDate", DateUtils.formatDate(this.searchReportEndDate) + " 23:59:59");
        }
        //单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sqlBuffer.append("  AND T.CRPT_NAME LIKE :crptName ");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchCrptName).trim()) + "%");
        }
        //检测报告编号
        if (StringUtils.isNotBlank(this.searchRptNo)) {
            sqlBuffer.append("  AND T.RPT_NO LIKE :rptNo ");
            this.paramMap.put("rptNo", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchRptNo).trim()) + "%");
        }
        //状态
        if (null != this.states && this.states.length>0) {
            sqlBuffer.append(" AND T.STATE IN (:state)");
            this.paramMap.put("state", Arrays.asList(this.states));
        }
        String h2 = "SELECT COUNT(*) FROM (" + sqlBuffer+ ")";
        String h1 = "SELECT * FROM (" + sqlBuffer + ")AA  ORDER BY AA.RPT_NO DESC ";
        return new String[] { h1, h2 };
    }

    /**
     * <p>方法描述： 医疗机构选择 </p>
     * @MethodAuthor： pw 2023/6/19
     **/
    public void selectUnitList() {
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("height", 505);
        options.put("width", 870);
        options.put("contentWidth", 835);
        options.put("contentHeight", 500);

        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<String>();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        //已经选择的不显示
        if(null != this.radCheckRpt.getFkByUnitId()){
            paramList = new ArrayList<String>();
            paramList.add(this.radCheckRpt.getFkByUnitId().getRid().toString());
            paramMap.put("selectIds", paramList);
        }
        requestContext.openDialog("/webapp/heth/comm/dialog/selectRadHethList", options, paramMap);

    }

    /**
     * <p>方法描述： 选择单位 </p>
     * @MethodAuthor： pw 2023/6/19
     **/
    public void onUnitSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            Object[] selectOrgs = (Object[]) selectedMap.get("selectPros");
            if (null!=selectOrgs) {
                this.radCheckRpt.setFkByUnitId(null);
                this.radCheckRpt.setAddress(null);
                this.radCheckRpt.setLinkMan(null);
                this.radCheckRpt.setLinkPhone(null);
                this.radCheckRpt.setFkByZoneId(null);
                this.radCheckRpt.setCrptName(null);
                this.radCheckRpt.setCreditCode(null);
                TsUnit selectedUnit = this.tdRadCheckRptService.find(TsUnit.class,Integer.valueOf(selectOrgs[0].toString()));
               if(null != selectedUnit){
                   this.radCheckRpt.setFkByUnitId(selectedUnit);
                   this.radCheckRpt.setFkByZoneId(selectedUnit.getTsZone());
                   this.radCheckRpt.setCreditCode(selectedUnit.getCreditCode());
               }
                this.radCheckRpt.setCrptName(null == selectOrgs[3] ? null : selectOrgs[3].toString());
                this.radCheckRpt.setAddress(null == selectOrgs[4] ? null : selectOrgs[4].toString());
                this.radCheckRpt.setLinkMan(null == selectOrgs[5] ? null : selectOrgs[5].toString());
                this.radCheckRpt.setLinkPhone(null == selectOrgs[6] ? null : selectOrgs[6].toString());
            }
        }
    }

    /**
     * <p>方法描述： 删除 </p>
     * @MethodAuthor： pw 2023/6/16
     **/
    public void deleteAction(){
        if (this.rid == null) {
            return;
        }
        try {
            this.tdRadCheckRptService.deleteByRid(this.rid);
            JsfUtil.addSuccessMessage("删除成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * <p>方法描述： 提交前校验 </p>
     * @MethodAuthor： pw 2023/6/16
     **/
    public void beforeSubmit(){
        if(this.validateBeforeSave(true)){
            RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show()");
        }
    }

    /**
     * <p>方法描述： 提交 </p>
     * @MethodAuthor： pw 2023/6/16
     **/
    public void submitAction() {
        try{
            this.radCheckRpt.setState(1);
            this.radCheckRpt.setBackRan(null);
            this.radCheckRpt.setFkByCheckUnitId(Global.getUser().getTsUnit());
            this.tdRadCheckRptService.saveOrUpdateCheckRpt(this.radCheckRpt);
            RequestContext.getCurrentInstance().update("tabView");
            JsfUtil.addSuccessMessage("提交成功！");
            this.searchAction();
            this.rid = this.radCheckRpt.getRid();
            this.viewInitAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
     * <p>方法描述： 附件上传 </p>
     * @MethodAuthor： pw 2023/6/16
     **/
    public void fileUpload(FileUploadEvent event) {
        if (null == event || null == this.uploadType) {
            return;
        }
        UploadedFile file = event.getFile();
        try {
            // 文件名称
            String fileName = file.getFileName();
            String uuid = java.util.UUID.randomUUID().toString().replaceAll("-", "");
            String path = JsfUtil.getAbsolutePath();
            String relativePath = "heth/zkcheckjs/checkRpt/" + uuid + fileName.substring(fileName.lastIndexOf("."));
            // 文件路径
            String filePath = path + relativePath;
            if (0 == this.uploadType) {
                String errorMsg = FileUtils.veryFile(file.getInputstream(), file.getContentType(), file.getFileName(), "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    RequestContext.getCurrentInstance().update("tabView:editForm:fileUpload1");
                    return;
                }
                FileUtils.copyFile(filePath, file.getInputstream());
                // 文件路径
                this.radCheckRpt.setFilePath(relativePath);
                RequestContext currentInstance = RequestContext.getCurrentInstance();
                currentInstance.execute("PF('FileDialog').hide();");
                currentInstance.update("tabView:editForm:rptPanel");
                JsfUtil.addSuccessMessage("上传成功！");

            }
            if (1 == this.uploadType) {
                String errorMsg = FileUtils.veryFile(file.getInputstream(), file.getContentType(), file.getFileName(), "7");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    RequestContext.getCurrentInstance().update("tabView:editForm:fileUpload");
                    return;
                }
                FileUtils.copyFile(filePath, file.getInputstream());
                // 文件路径
                this.radCheckRpt.setSourceFilePath(relativePath);
                RequestContext currentInstance = RequestContext.getCurrentInstance();
                currentInstance.execute("PF('SourceFileDialog').hide();");
                currentInstance.update("tabView:editForm:rptPanel");
                JsfUtil.addSuccessMessage("上传成功！");

            }
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("上传失败！");
            throw new RuntimeException(e);
        }
    }

    /**
     * <p>方法描述： 删除附件 </p>
     * @MethodAuthor： pw 2023/6/16
     **/
    public void delRptFile() {
        if(null == this.uploadType){
            return;
        }
        try {
            if(0 == this.uploadType){
                this.radCheckRpt.setFilePath(null);
            }else{
                this.radCheckRpt.setSourceFilePath(null);
            }
            JsfUtil.addSuccessMessage("删除成功！");
            RequestContext.getCurrentInstance().update("tabView:editForm:rptPanel");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * <p>方法描述：上传窗口打开 </p>
     * @MethodAuthor： pw 2023/6/17
     **/
    public void beforeUpload(){
        if(null == this.uploadType){
            return;
        }
        if(0 == this.uploadType){
            RequestContext.getCurrentInstance().execute("PF('FileDialog').show()");
            RequestContext.getCurrentInstance().update("fileDialog");
        }else if(1 == this.uploadType){
            RequestContext.getCurrentInstance().execute("PF('SourceFileDialog').show()");
            RequestContext.getCurrentInstance().update("sourceFileDialog");
        }
    }

    /**
     * <p>方法描述： 暂存与提交校验 </p>
     * @MethodAuthor： pw 2023/6/17
     **/
    private boolean validateBeforeSave(boolean ifSubmit){
        if(null == this.radCheckRpt){
            return false;
        }
        boolean flag = true;
        if(null == this.radCheckRpt.getFkByUnitId() || null == this.radCheckRpt.getFkByUnitId().getRid()){
            JsfUtil.addErrorMessage("单位名称不能为空！");
            flag=false;
        }
        if(StringUtils.isBlank(this.radCheckRpt.getRptNo())){
            JsfUtil.addErrorMessage("检测报告编号不能为空！");
            flag=false;
        }
        if(StringUtils.isNotBlank(this.radCheckRpt.getRptNo()) && this.radCheckRpt.getRptNo().length() > 50){
            JsfUtil.addErrorMessage("检测报告编号不能超过50个字！");
            flag=false;
        }
        if(StringUtils.isBlank(this.radCheckRpt.getRptName())){
            JsfUtil.addErrorMessage("检测报告名称不能为空！");
            flag=false;
        }
        if(StringUtils.isNotBlank(this.radCheckRpt.getRptName()) && this.radCheckRpt.getRptName().length() > 100){
            JsfUtil.addErrorMessage("检测报告名称不能超过100个字！");
            flag=false;
        }
        if(null == this.radCheckRpt.getRptDate()){
            JsfUtil.addErrorMessage("报告日期不能为空！");
            flag=false;
        }
        if(null != this.radCheckRpt.getRptDate() && this.radCheckRpt.getRptDate().after(new Date())){
            JsfUtil.addErrorMessage("报告日期不能大于当天！");
            flag=false;
        }
        if(ifSubmit){
            if(StringUtils.isBlank(this.radCheckRpt.getFilePath())){
                JsfUtil.addErrorMessage("请上传检测报告附件！");
                flag=false;
            }
            if(StringUtils.isBlank(this.radCheckRpt.getSourceFilePath())){
                JsfUtil.addErrorMessage("请上传文审材料附件！");
                flag=false;
            }
        }

        return flag;
    }

    @Override
    public void processData(List<?> list) {

    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public Integer getUploadType() {
        return uploadType;
    }

    public void setUploadType(Integer uploadType) {
        this.uploadType = uploadType;
    }
}
