package com.chis.modules.heth.zkcheck.service;

import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zkcheck.entity.TdZwExpert;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Transactional(readOnly = true)
public class ExpertMaintenanceListService extends AbstractTemplate {


    /**
     * <p>Description：保存专家 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void saveExpert(TdZwExpert tdZwExpert) {
        this.upsertEntity(tdZwExpert);
    }

    /**
     * <p>Description：根据身份证号查询是否已经存在 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public Integer findExpertByIDC(String idc, Integer rid) {
        String sql = " select count(T.RID) from TD_ZW_EXPERT T where T.IDC='" + idc + "' and nvl(T.DEL_MARK, 0) = 0 ";
        if (rid != null) {
            sql += " and T.RID!=" + rid;
        }
        return this.findCountBySql(sql);
    }

    /**
     * <p>Description：单字段更新 </p>
     * <p>Author： yzz 2024-10-29 </p>
     */
    public void updateFieldByValue(String field, String value, Integer rid, Class<?> type) {
        String upSql = " update TD_ZW_EXPERT set ";
        if (type.equals(String.class)) {
            upSql += field + "='" + value + "'";
        } else if (type.equals(Integer.class)) {
            upSql += field + "=" + value;
        }
        upSql += " where RID=" + rid;
        this.executeSql(upSql, null);
    }

    /**
     * <p>Description：批量保存专家 </p>
     * <p>Author： yzz 2024-10-30 </p>
     */
    public void saveBatchExperts(List<TdZwExpert> tdZwExperts) {
        if (CollectionUtils.isEmpty(tdZwExperts)) {
            return;
        }
        this.saveBatchObjs(tdZwExperts);
    }

    /**
     * <p>Description：查询专家库已经存在同身份证的专家 </p>
     * <p>Author： yzz 2024-11-01 </p>
     */
    public List<Object> findExistsExperts() {
        StringBuilder sql = new StringBuilder();
        sql.append(" WITH LatestUnit AS (SELECT T1.RID                                                                    AS EMP_ID, ");
        sql.append("         ROW_NUMBER() OVER (PARTITION BY T1.IDC_CARD ORDER BY T1.CREATE_DATE DESC) AS RN ");
        sql.append("         FROM TD_ZW_PSNINFO T1) ");
        sql.append(" select T1.IDC_CARD ");
        sql.append(" from TD_ZW_SRVORGINFO TT ");
        sql.append(" LEFT JOIN TD_ZW_SRVORGPSNS T on TT.RID = T.ORG_ID ");
        sql.append(" LEFT JOIN TD_ZW_PSNINFO T1 ON T.EMP_ID = T1.RID ");
        sql.append(" left join LatestUnit T3 on T1.RID = T3.EMP_ID and T3.rn = 1 ");
        sql.append(" where T.ON_DUTY = 1 ");
        sql.append(" and TT.STATE = 1 ");
        sql.append(" and exists( ");
        sql.append("         select * from TD_ZW_EXPERT T2 where T2.IDC = T1.IDC_CARD and nvl(T2.DEL_MARK, 0) = 0 ");
        sql.append(" ) ");
        return this.findDataBySqlNoPage(sql.toString(), null);
    }

}
