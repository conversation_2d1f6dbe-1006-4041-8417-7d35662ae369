package com.chis.modules.heth.zkcheck.service;

import com.chis.common.utils.CollectionUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckItem;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckSub;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckTable;
import com.chis.modules.heth.zkcheck.entity.TdZwZkCheckSub;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用人单位现场核查Service
 */
@Service
@Transactional(readOnly = false)
public class CrptOnSiteVerifyListService extends AbstractTemplate {

    /**
     * 查询考核表，并级联所有下级表
     *
     * @param rid 检测报告RID
     * @return 考核表
     */
    public List<TdZwCheckTable> findTdZwCheckTableListByMainId(Integer rid) {
        List<TdZwCheckTable> tdZwCheckTableList = this.findEntityListByMainId(TdZwCheckTable.class, rid);
        if (CollectionUtils.isEmpty(tdZwCheckTableList)) {
            return new ArrayList<>();
        }
        List<Integer> itemRidList = new ArrayList<>();
        for (TdZwCheckTable tdZwCheckTable : tdZwCheckTableList) {
            if (tdZwCheckTable == null) {
                continue;
            }
            tdZwCheckTable.getCheckItemList().size();
            if (CollectionUtils.isEmpty(tdZwCheckTable.getCheckItemList())) {
                continue;
            }
            for (TdZwCheckItem checkItem : tdZwCheckTable.getCheckItemList()) {
                if (checkItem.getRid() == null) {
                    continue;
                }
                itemRidList.add(checkItem.getRid());
            }
        }
        String hql = " FROM TdZwCheckSub T WHERE T.fkByMainId.rid IN (" + StringUtils.list2string(itemRidList, ",") + ") ";
        List<TdZwCheckSub> checkSubList = this.findByHql(hql, TdZwCheckSub.class);
        Map<Integer, List<TdZwCheckSub>> map = new HashMap<>();
        for (TdZwCheckSub checkSub : checkSubList) {
            if (checkSub == null || checkSub.getFkByMainId() == null) {
                continue;
            }
            checkSub.getTdZwCheckDeductList().size();
            checkSub.getFkByScoreId().getIndexList().size();
            Integer rid1 = checkSub.getFkByMainId().getRid();
            if (!map.containsKey(rid1)) {
                map.put(rid1, new ArrayList<TdZwCheckSub>());
            }
            map.get(rid1).add(checkSub);
        }
        for (TdZwCheckTable tdZwCheckTable : tdZwCheckTableList) {
            if (tdZwCheckTable == null) {
                continue;
            }
            if (CollectionUtils.isEmpty(tdZwCheckTable.getCheckItemList())) {
                continue;
            }
            for (TdZwCheckItem checkItem : tdZwCheckTable.getCheckItemList()) {
                if (checkItem.getRid() == null) {
                    continue;
                }
                if (map.containsKey(checkItem.getRid())) {
                    checkItem.setTdZwCheckSubList(map.get(checkItem.getRid()));
                } else {
                    checkItem.setTdZwCheckSubList(new ArrayList<TdZwCheckSub>());
                }
            }
        }
        return tdZwCheckTableList;
    }

    public List<TdZwCheckSub> findTdZwCheckSubListByMainId(Integer rid) {
        List<TdZwCheckSub> tdZwCheckSubList = this.findEntityListByMainId(TdZwCheckSub.class, rid);
        if (CollectionUtils.isEmpty(tdZwCheckSubList)) {
            return new ArrayList<>();
        }
        for (TdZwCheckSub tdZwCheckSub : tdZwCheckSubList) {
            if (tdZwCheckSub == null) {
                continue;
            }
            tdZwCheckSub.getTdZwCheckDeductList().size();
            tdZwCheckSub.getFkByScoreId().getIndexList().size();
        }
        return tdZwCheckSubList;
    }

    /**
     * 获取现场考核项
     *
     * @return 现场考核项
     */
    public List<Object[]> findInitTdZwCheckTableInfo() {
        String sql = "SELECT BS.RID AS STAND_RID, BS.CHECK_NAME, SC2.RID AS ITEM_RID, SC2.CODE_NAME AS ITEM_NAME, S.RID AS SCORES_RID, S.SCORE, SC.EXTENDS1, SI.XH INDEX_XH, SI.INDEX_XH || '、' || SC1.CODE_NAME AS SUB_ITEM_NAME " +
                "FROM TB_ZW_ZK_BADRSN_STAND BS " +
                "     LEFT JOIN TB_ZW_ZK_SCORES S ON BS.RID = S.MAIN_ID " +
                "     LEFT JOIN TS_SIMPLE_CODE SC ON S.ITEM_TYPE_ID = SC.RID " +
                "     LEFT JOIN TB_ZW_ZK_SCORE_INDEX SI ON S.RID = SI.MAIN_ID " +
                "     LEFT JOIN TS_SIMPLE_CODE SC1 ON SI.INDEX_ID = SC1.RID " +
                "     LEFT JOIN TS_SIMPLE_CODE SC2 ON SC1.CODE_TYPE_ID = SC2.CODE_TYPE_ID AND SC2.CODE_NO || '.' || SC1.CODE_NO = SC1.CODE_LEVEL_NO " +
                "WHERE BS.STATE_MARK = 1 AND BS.CHECK_TYPE = 4 AND SC1.EXTENDS1 = 1 AND SC1.IF_REVEAL = 1 AND SC2.IF_REVEAL = 1 " +
                "ORDER BY BS.XH, S.XH, SI.XH ";
        return CollectionUtil.castList(Object[].class, this.findDataBySqlNoPage(sql, null));
    }

    public List<Object[]> findAllDeductList(Integer mainId) {
        if (mainId == null) {
            return new ArrayList<>();
        }
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "SELECT SD.RID, SC.CODE_NAME, SD.XH, '' " +
                "FROM TB_ZW_ZK_SCORE_DEDUCT SD " +
                "     LEFT JOIN TS_SIMPLE_CODE SC ON SD.DEDUCT_ID = SC.RID " +
                "WHERE SD.MAIN_ID = :mainId " +
                "ORDER BY SD.XH ";
        paramMap.put("mainId", mainId);
        return CollectionUtil.castList(Object[].class, this.findDataBySqlNoPage(sql, paramMap));
    }

    public void updateTdZwCheckRptState(Integer rid, int state) {
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "UPDATE TD_ZW_CHECK_RPT SET STATE = :state WHERE RID = :rid";
        paramMap.put("rid", rid);
        paramMap.put("state", state);
        this.executeSql(sql, paramMap);
    }

    public void updateTdZwCheckTable(List<TdZwCheckTable> checkTableList) {
        for (TdZwCheckTable checkTable : checkTableList) {
            this.upsertEntity(checkTable);
            for (TdZwCheckItem checkItem : checkTable.getCheckItemList()) {
                for (TdZwCheckSub checkSub : checkItem.getTdZwCheckSubList()) {
                    if (checkSub.getScoreRstId() == null) {
                        checkSub.setFkByScoreRstId(null);
                    } else {
                        checkSub.setFkByScoreRstId(new TsSimpleCode(checkSub.getScoreRstId()));
                    }
                    this.upsertEntity(checkSub);
                }
            }
        }
    }

    public void submitTdZwCheckRpt(Integer rid, List<TdZwCheckTable> checkTableList) {
        updateTdZwCheckRptState(rid, 2);
        updateTdZwCheckTable(checkTableList);
    }

    public boolean selectMainTotalScoreValByRptId(Integer rptId) {
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "SELECT TOTAL_SCORE_VAL FROM TD_ZW_ZK_CHECK_MAIN WHERE NVL(DEL_MARK, 0) = 0 AND JC_RPT_ID = :rptId ";
        paramMap.put("rptId", rptId);
        List<BigDecimal> list = CollectionUtil.castList(BigDecimal.class, this.findDataBySqlNoPage(sql, paramMap));
        if (CollectionUtils.isEmpty(list) || list.get(0) == null) {
            return true;
        }
        return "".equals(StringUtils.objectToString(list.get(0)));
    }
}
