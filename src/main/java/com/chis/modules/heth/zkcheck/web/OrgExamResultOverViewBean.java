package com.chis.modules.heth.zkcheck.web;

import com.chis.common.pojo.DynamicColPO;
import com.chis.common.pojo.DynamicRowPO;
import com.chis.common.pojo.ExportDynamicColPO;
import com.chis.common.pojo.ExportDynamicRowPO;
import com.chis.common.utils.*;
import com.chis.modules.heth.zkcheck.service.OrgExamResultOverService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import org.apache.poi.ss.usermodel.Workbook;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * <p>类描述： 机构考核结果一览表托管Bean</p>
 * @ClassAuthor： pw 2022/11/5
 **/
@ManagedBean(name = "orgExamResultOverViewBean")
@ViewScoped
public class OrgExamResultOverViewBean extends DynamicShowExportBase {

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final OrgExamResultOverService overService = SpringContextHolder.getBean(OrgExamResultOverService.class);
    /** 地区集合 */
    private List<TsZone> searchZoneList;
    /** 地区编码 */
    private String searchZoneCode;
    /** 地区名称 */
    private String searchZoneName;
    /** 选中的考核类型 */
    private Integer checkTypeRid;
    /** 考核类型码表 5554 */
    private List<TsSimpleCode> checkTypeList;
    private Map<Integer, TsSimpleCode> checkTypeMap;
    /** 考核日期 开始时间 */
    private Date searchCheckDateStart;
    /** 考核日期 结束时间 */
    private Date searchCheckDateEnd;
    /** 当前登录用户所在单位的管辖地区 是否省级 */
    private Boolean ifProvUser = Boolean.FALSE;
    /** 质控类别（省级用户显示）<p>2:省级<p>3:市级 */
    private List<Integer> inquiryExamTypeList;
    /** 考核等级码表 5535 */
    private List<TsSimpleCode> checkRstList;
    private Map<Integer, TsSimpleCode> checkRstMap;
    /** 弹出框 考核等级 注意zwx:SelectManyMenu组件的key与value都需要是String 这里需要有序的 用LinkedHashMap */
    private Map<String, String> selectCheckRstMap = new LinkedHashMap<>(0);
    /** 弹出框 选中考核等级rid拼接的String */
    private String checkRstSelectRids;
    /** 弹出框 选中考核等级名称拼接的String */
    private String checkRstSelectNames;
    /** 项类 码表 5533 */
    private List<TsSimpleCode> itemTypeList;
    private Map<Integer,TsSimpleCode> itemTypeMap;
    /** 评分项结果 5534 */
    private List<TsSimpleCode> scoreRstList;
    /** 项类对应的评分项结果 key 项类 value 评分项结果 */
    private Map<Integer, List<TsSimpleCode>> itemTypeWithScoreRstMap;
    /** 考核等级是否可选择 体检机构可选择 */
    private Boolean ifCheckRstCanSelect;
    /** 查询出的质控考核主表rid */
    private List<Integer> checkMainRidList;
    /** 是否已经查询封装过导出的存在问题 */
    private Boolean ifQueryedDeduct = Boolean.FALSE;

    public OrgExamResultOverViewBean(){
        //导出行与显示行不一致
        this.ifExportSource = Boolean.FALSE;
        this.dataRowList = new ArrayList<>();
        this.exportDataRowList = new ArrayList<>();
        this.init();
        this.headRowGenerate();
    }

    /**
     * <p>方法描述： 导出前校验是否有数据 </p>
     * @MethodAuthor： pw 2022/11/5
     **/
    public void preExport(){
        if(CollectionUtils.isEmpty(this.exportDataRowList)){
            JsfUtil.addErrorMessage("无导出数据！");
            return;
        }
        this.fillDeduct();
        RequestContext.getCurrentInstance().execute("downloadFileClick()");
    }

    @Override
    public void fillHeadRowStyle(List<ExportDynamicRowPO> headRowList, Workbook wBook) {

    }

    @Override
    public void fillDataRowStyle(List<ExportDynamicRowPO> dataRowList, Workbook wBook) {
        if(CollectionUtils.isEmpty(dataRowList)){
            return;
        }
        TsSimpleCode simpleCode = null == this.checkTypeRid || CollectionUtils.isEmpty(this.checkTypeMap) ? null :
                this.checkTypeMap.get(this.checkTypeRid);
        String extends1 = null == simpleCode ? null : simpleCode.getExtendS1();
        Integer topSize = null;
        for(ExportDynamicRowPO rowPO : dataRowList){
            if(CollectionUtils.isEmpty(rowPO.getCols())){
                continue;
            }
            int size = rowPO.getCols().size();
            if(null == topSize){
                topSize = size;
            }
            for (int i=0;i<size; i++){
                ExportDynamicColPO colPO = rowPO.getCols().get(i);
                //机构名称以及存在问题居左
                if((i == 2 && topSize == size) || (i == 1 && topSize != size) || i == (size-1)){
                    colPO.setColStyle(this.leftStyle);
                }else{
                    colPO.setColStyle(this.centerStyle);
                }
                //备注居左
                if("1".equals(extends1)){
                    if(i == (size-4)){
                        colPO.setColStyle(this.leftStyle);
                    }
                }else if("2".equals(extends1)){
                    if(i == (size-3) || i == (size-5)){
                        colPO.setColStyle(this.leftStyle);
                    }
                }
            }
        }
    }

    @Override
    public void generateExportFileName() {
        TsSimpleCode simpleCode = null == this.checkTypeRid || CollectionUtils.isEmpty(this.checkTypeMap) ? null :
                this.checkTypeMap.get(this.checkTypeRid);
        this.exportFileName = DateUtils.getYear(this.searchCheckDateEnd)+(null == simpleCode ? "" : simpleCode.getCodeName())+"考核结果一览表";
    }

    /**
     * <p>方法描述： 修改考核类型 </p>
     * @MethodAuthor： pw 2022/11/7
     **/
    public void changeCheckType(){
        TsSimpleCode simpleCode = null == this.checkTypeRid || CollectionUtils.isEmpty(this.checkTypeMap) ? null :
                this.checkTypeMap.get(this.checkTypeRid);
        String extends1 = null == simpleCode ? null : simpleCode.getExtendS1();
        this.ifCheckRstCanSelect = "1".equals(extends1);
    }

    /**
     * <p>方法描述： 查询 </p>
     * @MethodAuthor： pw 2022/11/8
     **/
    public void searchAction(){
        if(!this.validateQueryCondition()){
            return;
        }
        this.executeQuery();
    }

    /**
     * <p>方法描述： 执行查询 </p>
     * @MethodAuthor： pw 2022/11/8
     **/
    private void executeQuery(){
        this.dataRowList.clear();
        this.exportDataRowList.clear();
        this.checkMainRidList = new ArrayList<>();
        this.ifQueryedDeduct = Boolean.FALSE;
        this.headRowGenerate();
        StringBuffer sqlBuffer = new StringBuffer();
        Map<String, Object> paramMap = new HashMap<>();
        //获取质控考核主表rid、地区编码、地区全称、评估机构rid，评估机构名称，考核结果码表名称，录入机构对应的真实地区级别用于判断质控类别
        sqlBuffer.append(" SELECT RID,ZONE_GB,FULL_NAME,ORG_ID,ORG_NAME,CODE_NAME,ZKLB,'' FROM (");
        sqlBuffer.append(" SELECT ")
                .append(" T.RID AS RID, ")
                .append(" T2.ZONE_GB AS ZONE_GB, ")
                .append(" T2.FULL_NAME AS FULL_NAME, ")
                .append(" T.ORG_ID AS ORG_ID, ")
                .append(" T1.UNITNAME AS ORG_NAME, ")
                .append(" T5.CODE_NAME AS CODE_NAME, ")
                .append(" T4.REAL_ZONE_TYPE AS ZKLB, ")
                .append(" ROW_NUMBER() OVER (PARTITION BY T.ORG_ID ORDER BY T.CHECK_DATE DESC, T.RID DESC) AS RN ")
                .append(" FROM TD_ZW_ZK_CHECK_MAIN T ");
        sqlBuffer.append(" LEFT JOIN TS_UNIT T1 ON T.ORG_ID = T1.RID ")
                .append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID ")
                .append(" LEFT JOIN TS_UNIT T3 ON T.RECORD_ORG_ID = T3.RID ")
                .append(" LEFT JOIN TS_ZONE T4 ON T3.MANAGE_ZONE_ID = T4.RID ")
                .append(" LEFT JOIN TS_SIMPLE_CODE T5 ON T.CHECK_RST_ID = T5.RID ");
        sqlBuffer.append(" WHERE ")
                .append(" T.STATE_MARK = 1 ")
                .append(" AND NVL(T.DEL_MARK, 0) = 0 ")
                .append(" AND T2.ZONE_GB LIKE :zoneCode ")
                .append(" AND T.CHECK_TYPE_ID = :checkTypeId ")
                .append(" AND T.CHECK_DATE >= TO_DATE(:searchCheckDateStart, 'YYYY-MM-DD HH24:MI:SS') ")
                .append(" AND T.CHECK_DATE <= TO_DATE(:searchCheckDateEnd, 'YYYY-MM-DD HH24:MI:SS') ");
        if(this.ifCheckRstCanSelect && StringUtils.isNotBlank(this.checkRstSelectRids)){
            sqlBuffer.append(" AND T.CHECK_RST_ID IN(").append(this.checkRstSelectRids).append(") ");
        }
        if(this.ifProvUser){
            if (!CollectionUtils.isEmpty(this.inquiryExamTypeList)) {
                sqlBuffer.append(" AND T4.REAL_ZONE_TYPE IN (:realZoneTypeList) ");
                paramMap.put("realZoneTypeList", this.inquiryExamTypeList);
            }
        }else{
            sqlBuffer.append(" AND T.RECORD_ORG_ID =").append(Global.getUser().getTsUnit().getRid());
        }
        paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(StringUtils.objectToString(this.searchZoneCode)).trim()) + "%");
        paramMap.put("checkTypeId", this.checkTypeRid);
        paramMap.put("searchCheckDateStart", DateUtils.formatDate(this.searchCheckDateStart) + " 00:00:00");
        paramMap.put("searchCheckDateEnd", DateUtils.formatDate(this.searchCheckDateEnd) + " 23:59:59");
        sqlBuffer.append(") WHERE RN=1 ");
        //地区、机构名称排序
        sqlBuffer.append(" ORDER BY ZONE_GB,ORG_NAME ");
        List<Object[]> mainQueryResultList = CollectionUtil.castList(Object[].class,
                this.commService.findDataBySqlNoPage(sqlBuffer.toString(), paramMap));
        if(CollectionUtils.isEmpty(mainQueryResultList)){
            return;
        }
        //质控考核主表rid、地区编码、地区全称、评估机构rid，评估机构名称，考核结果码表名称，录入机构对应的真实地区级别用于判断质控类别
        //key 质控考核主表rid value对应的评估机构rid
        Map<Integer,Integer> checkMainWithUnitIdMap = new HashMap<>();
        List<Integer> unitRidList = new ArrayList<>();
        for(Object[] objArr : mainQueryResultList){
            Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            Integer unitRid = null == objArr[3] ? null : Integer.parseInt(objArr[3].toString());
            if(null == rid || null == unitRid){
                continue;
            }
            checkMainWithUnitIdMap.put(rid, unitRid);
            this.checkMainRidList.add(rid);
            unitRidList.add(unitRid);
        }

        // 关键项一般项查询
        Map<Integer, Map<String,Integer>> itemTypeCountResultMap = this.overService
                .queryCountByMainIdGroupByItemTypeExtends1AndRstExtends1(this.checkMainRidList);
        // 体检的盲样考核
        Map<String,Integer> myItemExistMap = new HashMap<>(1);
        Map<Integer, Map<String,Integer>> myCountResultMap = new HashMap<>(1);
        if(this.ifCheckRstCanSelect){
            myItemExistMap = this.overService.selectMyItemExistByMainIdGroupByBusExt(this.checkMainRidList);
            myCountResultMap = this.overService
                    .queryMyCountResultByUnitRidGroupByUnitAndExt2(unitRidList);
        }
        //主检医师技能考核  质控考核主表rid 检测指标对应扩展字段 结果名称 备注
        Map<Integer,List<Object[]>> skillEvaluationMap = this.overService.querySkillEvaluation(this.checkMainRidList);
        this.fillDataRow(mainQueryResultList, itemTypeCountResultMap, myItemExistMap,
                myCountResultMap, skillEvaluationMap);
    }

    /**
     * <p>方法描述： 填充数据行 </p>
     * @param mainQueryResultList 质控考核主表rid、地区编码、地区全称、评估机构rid，评估机构名称，考核结果码表名称，
     *                            录入机构对应的真实地区级别用于判断质控类别
     * @param itemTypeCountResultMap  质控考核主表rid对应的项类扩展字段1+@+评分项结果扩展字段1 对应的数量
     * @param myItemExistMap      key 质控考核主表rid+@+质控指标分值维护的检测指标对应扩展字段
     * @param myCountResultMap    key 评估机构rid subKey 盲样领取明细信息的指标的扩展字段2（码表5401 扩展字段2 1：血铅2：尿隔3：血清生化）+@+合格标记(0否 1是)
     *                            subValue 对应的数量
     * @param skillEvaluationMap 考核主表rid 对应的 主检医师技能考核结果
     * @MethodAuthor： pw 2022/11/8
     **/
    private void fillDataRow(List<Object[]> mainQueryResultList, Map<Integer, Map<String,Integer>> itemTypeCountResultMap,
                             Map<String,Integer> myItemExistMap, Map<Integer, Map<String,Integer>> myCountResultMap,
                             Map<Integer,List<Object[]>> skillEvaluationMap){
        TsSimpleCode checkTypeSimpleCode = null == this.checkTypeRid || CollectionUtils.isEmpty(this.checkTypeMap) ? null :
                this.checkTypeMap.get(this.checkTypeRid);
        String extends1 = null == checkTypeSimpleCode ? null : checkTypeSimpleCode.getExtendS1();
        int resultSize = mainQueryResultList.size();
        DynamicRowPO rowPO;
        List<DynamicColPO> cols;
        int zoneType = ZoneUtil.getZoneType(this.searchZoneCode);
        for(int i=0; i<resultSize; i++){
            rowPO = new DynamicRowPO();
            cols = new ArrayList<>();
            rowPO.setCols(cols);
            this.dataRowList.add(rowPO);
            Object[] objArr = mainQueryResultList.get(i);
            DynamicColPO colPO = this.normalColHelper(2, (i+1), 1, 1,null, null);
            cols.add(colPO);
            //地区
            cols.add(this.normalColHelper(1, this.areaSplit(StringUtils.objectToString(objArr[2]), zoneType), 1, 1,null, null));
            //机构名称
            colPO = this.normalColHelper(1, StringUtils.objectToString(objArr[4]), 1, 1,null, null);
            colPO.setTextAlign("left");
            cols.add(colPO);
            Integer mainRid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            //关键项以及一般项目
            this.fillItemTypeResult(mainRid, itemTypeCountResultMap, cols);
            if("1".equals(extends1)){
                //体检机构 盲样考核
                this.fillMyResult(mainRid, null == objArr[3] ? null : Integer.parseInt(objArr[3].toString()),
                        myItemExistMap, myCountResultMap, cols);
            }
            //主检医师技能
            this.fillSkillEvaluation(mainRid, skillEvaluationMap, extends1, cols);
            if("1".equals(extends1)){
                //体检机构 考核等级
                cols.add(this.normalColHelper(1, StringUtils.objectToString(objArr[5]), 1, 1,null, null));
            }
            //质控类别
            String zklb = StringUtils.objectToString(objArr[6]);
            cols.add(this.normalColHelper(1, "2".equals(zklb) ? "省级" : ("3".equals(zklb) ? "市级" : ""), 1, 1,null, null));
            //加入到导出数据中
            this.fillExportData(mainRid, extends1, skillEvaluationMap, cols);
        }
        this.dataRowMerge();
    }

    /**
     * <p>方法描述： 合并数据行 </p>
     * @MethodAuthor： pw 2022/11/8
     **/
    private void dataRowMerge(){
        this.executeMergeDataRow(this.dataRowList);
        this.executeMergeDataRow(this.exportDataRowList);
    }

    /**
     * <p>方法描述： 执行合并 </p>
     * @MethodAuthor： pw 2022/11/8
     **/
    private void executeMergeDataRow(List<DynamicRowPO> list){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        DynamicColPO colPO = null;
        for(DynamicRowPO rowPO : list){
            DynamicColPO tmpCol = rowPO.getCols().get(1);
            if(null == colPO){
                colPO = tmpCol;
                continue;
            }
            String area = tmpCol.getColStrVal();
            if(StringUtils.isNotBlank(area) && area.equals(colPO.getColStrVal())){
                colPO.setRowspan(colPO.getRowspan()+1);
                //移除当前单元格
                rowPO.getCols().remove(tmpCol);
            }else{
                colPO = tmpCol;
            }
        }
    }

    /**
     * <p>方法描述： exportDataRowList 赋值 </p>
     * @MethodAuthor： pw 2022/11/8
     **/
    private void fillExportData(Integer mainRid,String extends1,Map<Integer,List<Object[]>> skillEvaluationMap, List<DynamicColPO> cols){
        DynamicRowPO rowPO = new DynamicRowPO();
        this.exportDataRowList.add(rowPO);
        List<DynamicColPO> newCols = new ArrayList<>();
        List<Object[]> tmpList = CollectionUtils.isEmpty(skillEvaluationMap) ? null : skillEvaluationMap.get(mainRid);
        rowPO.setCols(newCols);
        for(DynamicColPO colPO : cols){
            DynamicColPO colPO1 = new DynamicColPO(colPO.getType(), colPO.getRowspan(), colPO.getColspan(), colPO.getStyle(),
                    colPO.getColor(), colPO.getTextAlign(), colPO.getFontWeight(), colPO.getColStrVal(),
                    colPO.getColIntVal(), colPO.getColDecimalVal(), colPO.getColumnWidth(), colPO.getCellAlign());
            newCols.add(colPO1);
        }
        int size = cols.size();
        DynamicColPO colPO;
        //加入备注列
        if("1".equals(extends1)){
            colPO = this.normalColHelper(1, CollectionUtils.isEmpty(tmpList) ? "" : StringUtils.objectToString(tmpList.get(0)[3]), 1, 1,null, null);
            newCols.add(size-2, colPO);
        }else if("2".equals(extends1)){
            String[] busExtArr = new String[]{"100","101"};
            for(String busExt : busExtArr){
                int index;
                if(busExt.equals("100")){
                    index = size -2;
                }else{
                    index = size;
                }
                colPO = this.normalColHelper(1, "", 1, 1,null, null);
                newCols.add(index, colPO);
                if(CollectionUtils.isEmpty(tmpList)){
                    continue;
                }
                for(Object[] objArr : tmpList){
                    if(busExt.equals(StringUtils.objectToString(objArr[1]).trim())){
                        colPO.setColStrVal(StringUtils.objectToString(objArr[3]));
                        break;
                    }
                }
            }
        }
        newCols.add(this.normalColHelper(2, mainRid, 1, 1,null, null));
    }

    /**
     * <p>方法描述： 填充主检医师技能 </p>
     * @MethodAuthor： pw 2022/11/8
     **/
    private void fillSkillEvaluation(Integer mainRid, Map<Integer,List<Object[]>> skillEvaluationMap, String extends1, List<DynamicColPO> cols){
        List<Object[]> tmpList = CollectionUtils.isEmpty(skillEvaluationMap) ? null : skillEvaluationMap.get(mainRid);
        DynamicColPO colPO;
        if("1".equals(extends1)){
            colPO = this.normalColHelper(1, CollectionUtils.isEmpty(tmpList) ? "/" : StringUtils.objectToString(tmpList.get(0)[2]), 1, 1,null, null);
            cols.add(colPO);
        }else if("2".equals(extends1)){
            String[] busExtArr = new String[]{"100","101"};
            for(String busExt : busExtArr){
                colPO = this.normalColHelper(1, "/", 1, 1,null, null);
                cols.add(colPO);
                if(CollectionUtils.isEmpty(tmpList)){
                    continue;
                }
                for(Object[] objArr : tmpList){
                    if(busExt.equals(StringUtils.objectToString(objArr[1]).trim())){
                        colPO.setColStrVal(StringUtils.objectToString(objArr[2]));
                        break;
                    }
                }
            }
        }
    }

    /**
     * <p>方法描述：填充盲样考核 </p>
     * @MethodAuthor： pw 2022/11/8
     **/
    private void fillMyResult(Integer mainRid, Integer unitRid, Map<String,Integer> myItemExistMap,
                              Map<Integer, Map<String,Integer>> myCountResultMap, List<DynamicColPO> cols){
        DynamicColPO colPO;
        for(int i=1;i<=3;i++){
            colPO = this.normalColHelper(1, "/", 1, 1,null, null);
            cols.add(colPO);
            String key = mainRid+"@"+i;
            if(null != myItemExistMap.get(key)){
                Map<String,Integer> map = myCountResultMap.get(unitRid);
                if(CollectionUtils.isEmpty(map)){
                    continue;
                }
                //不合格的
                key = i+"@"+0;
                Integer count = map.get(key);
                //合格的
                key = i+"@"+1;
                if(null == count && null != map.get(key)){
                    count = 0;
                }
                if(null != count){
                    colPO.setType(2);
                    colPO.setColIntVal(count);
                    colPO.setColStrVal(null);
                }
            }
        }
    }

    /**
     * <p>方法描述： 填充关键项以及一般项目 </p>
     * @MethodAuthor： pw 2022/11/8
     **/
    private void fillItemTypeResult(Integer mainRid, Map<Integer, Map<String,Integer>> itemTypeCountResultMap, List<DynamicColPO> cols){
        if(!CollectionUtils.isEmpty(this.itemTypeList)){
            for(TsSimpleCode simpleCode : this.itemTypeList){
                List<TsSimpleCode> tmpList = this.itemTypeWithScoreRstMap.get(simpleCode.getRid());
                if(CollectionUtils.isEmpty(tmpList)){
                    continue;
                }
                Map<String,Integer> tmpMap = itemTypeCountResultMap.get(mainRid);
                for(TsSimpleCode subSimpleCode : tmpList){
                    String key = simpleCode.getExtendS1()+"@"+subSimpleCode.getExtendS1();
                    Integer count = null == tmpMap || null == tmpMap.get(key) ? 0 : tmpMap.get(key);
                    cols.add(this.normalColHelper(2, null == count ? 0 : count, 1, 1,null, null));
                }
            }
        }
    }

    /**
     * <p>方法描述：截取地区 </p>
     * @MethodAuthor： pw 2022/11/8
     **/
    private String areaSplit(String fullName, int zoneType){
        if(StringUtils.isBlank(fullName)){
            return fullName;
        }
        //1-国家 2-省 3-市 4-县区
        String[] areaArr = fullName.split("_");
        if(2 == zoneType){
            return areaArr.length > 1 ? areaArr[1] : fullName;
        }else if(3 <= zoneType){
            return areaArr.length > 2 ? areaArr[2] : fullName;
        }else{
            return fullName;
        }
    }

    /**
     * <p>方法描述：查询存在问题 并赋值 </p>
     * @MethodAuthor： pw 2022/11/8
     **/
    private void fillDeduct(){
        if(CollectionUtils.isEmpty(this.exportDataRowList) || this.ifQueryedDeduct){
            return;
        }
        int size = 0;
        Map<Integer,String> deductMap = this.overService.queryDeduct(this.checkMainRidList);
        for(DynamicRowPO rowPO : this.exportDataRowList){
            size = rowPO.getCols().size();
            DynamicColPO colPO = rowPO.getCols().get(size-1);
            Integer mainRid = colPO.getColIntVal();
            String tip = null == mainRid ? null : deductMap.get(mainRid);
            colPO.setType(1);
            colPO.setColStrVal(tip);
            colPO.setColIntVal(null);
        }
        this.ifQueryedDeduct = Boolean.TRUE;

    }

    /**
     * <p>方法描述：查询验证 </p>
     * @MethodAuthor： pw 2022/11/8
     **/
    private boolean validateQueryCondition(){
        boolean flag = true;
        if(null == this.searchCheckDateStart){
            JsfUtil.addErrorMessage("考核开始日期不允许为空！");
            flag = false;
        }
        if(null == this.searchCheckDateEnd){
            JsfUtil.addErrorMessage("考核结束日期不允许为空！");
            flag = false;
        }
        if(null != this.searchCheckDateStart && null != this.searchCheckDateEnd &&
                DateUtils.getYear(this.searchCheckDateStart) != DateUtils.getYear(this.searchCheckDateEnd)){
            JsfUtil.addErrorMessage("考核日期不允许跨年查询！");
            flag = false;
        }
        return flag;
    }

    /**
     * <p>方法描述：动态标题按条件生成 </p>
     * @MethodAuthor： pw 2022/11/7
     **/
    private void headRowGenerate(){
        this.headRowList = new ArrayList<>();
        this.exportHeadRowList = new ArrayList<>();
        this.ifCheckRstCanSelect = Boolean.FALSE;
        this.generateExportFileName();
        TsSimpleCode simpleCode = null == this.checkTypeRid || CollectionUtils.isEmpty(this.checkTypeMap) ? null :
                this.checkTypeMap.get(this.checkTypeRid);
        String extends1 = null == simpleCode ? null : simpleCode.getExtendS1();
        if("1".equals(extends1)){
            this.ifCheckRstCanSelect = Boolean.TRUE;
            this.tjHeadRowGenerate(false);
            this.tjHeadRowGenerate(true);
        }else if("2".equals(extends1)){
            this.diagHeadRowGenerate(false);
            this.diagHeadRowGenerate(true);
        }
    }

    /**
     * <p>方法描述：体检机构标题生成 </p>
     * @MethodAuthor： pw 2022/11/7
     **/
    private void tjHeadRowGenerate(boolean ifExport){
        //序号 地区 机构名称 关键项-- 实验室比对  主检医师技能考核 考核等级 质控类别
        //导出的 序号 地区 机构名称 关键项-- 实验室比对  主检医师技能考核(结果与备注) 考核等级 质控类别 存在问题
        DynamicRowPO rowPO = new DynamicRowPO();
        if(ifExport){
            this.exportHeadRowList.add(rowPO);
        }else{
            //用于大标题合并列
            this.topColSpan = 0;
            this.headRowList.add(rowPO);
        }
        List<DynamicColPO> cols = new ArrayList<>();
        rowPO.setCols(cols);
        cols.add(this.headColHelper("序号", 2, 1,"width:60px;height:30px;" , 2000));
        cols.add(this.headColHelper("地区", 2, 1,"width:100px;" , 4000));
        cols.add(this.headColHelper("机构名称", 2, 1,"width:260px;" , 6000));
        int nomalWidth = 2567;
        //项类
        if(!CollectionUtils.isEmpty(this.itemTypeList)){
            for(TsSimpleCode simpleCode : this.itemTypeList){
                List<TsSimpleCode> tmpList = this.itemTypeWithScoreRstMap.get(simpleCode.getRid());
                if(CollectionUtils.isEmpty(tmpList)){
                    continue;
                }
                int rowspan = tmpList.size();
                cols.add(this.headColHelper(simpleCode.getCodeName(), 1, rowspan,"width:"+(rowspan*100)+"px;" , nomalWidth));
            }
        }
        cols.add(this.headColHelper("实验室比对（盲样考核）", 1, 3,"width:300px;" , nomalWidth));
        cols.add(this.headColHelper("主检医师技能考核", ifExport ? 1 : 2, ifExport ? 2 : 1,ifExport ? "width:200px;": "width:100px;" , nomalWidth));
        cols.add(this.headColHelper("考核等级", 2, 1,"width:100px;" , nomalWidth));
        cols.add(this.headColHelper("质控类别", 2, 1,"width:100px;" , nomalWidth));
        if(ifExport){
            cols.add(this.headColHelper("存在问题", 2, 1,"width:100px;" , 8000));
        }
        if(!ifExport){
            for(DynamicColPO colPO : cols){
                this.topColSpan += colPO.getColspan();
            }
        }

        rowPO = new DynamicRowPO();
        if(ifExport){
            this.exportHeadRowList.add(rowPO);
        }else{
            this.headRowList.add(rowPO);
        }
        cols = new ArrayList<>();
        rowPO.setCols(cols);
        //项类结果
        if(!CollectionUtils.isEmpty(this.itemTypeList)){
            for(TsSimpleCode simpleCode : this.itemTypeList){
                List<TsSimpleCode> tmpList = this.itemTypeWithScoreRstMap.get(simpleCode.getRid());
                if(CollectionUtils.isEmpty(tmpList)){
                    continue;
                }
                for(TsSimpleCode subSimpleCode : tmpList){
                    cols.add(this.headColHelper(subSimpleCode.getCodeName(), 1, 1,"width:100px;" , nomalWidth));
                }
            }
        }

        cols.add(this.headColHelper("血铅", 1, 1,"width:100px;" , nomalWidth));
        cols.add(this.headColHelper("尿镉", 1, 1,"width:100px;" , nomalWidth));
        cols.add(this.headColHelper("血清生化", 1, 1,"width:100px;" , nomalWidth));
        if(ifExport){
            cols.add(this.headColHelper("结果", 1, 1,"width:100px;" , nomalWidth));
            cols.add(this.headColHelper("备注", 1, 1,"width:100px;" , 4000));
        }

        if(ifExport){
            rowPO = new DynamicRowPO();
            cols = new ArrayList<>();
            rowPO.setCols(cols);
            cols.add(this.headColHelper(this.exportFileName, 1, this.topColSpan+2,"width:100px;" , 4000));
            //大标题 放第一行
            this.exportHeadRowList.add(0,rowPO);
        }
    }

    /**
     * <p>方法描述：诊断机构标题生成 </p>
     * @MethodAuthor： pw 2022/11/7
     **/
    private void diagHeadRowGenerate(boolean ifExport){
        //序号 地区 机构名称 关键项-- 主检医师技能考核 质控类别
        //序号 地区 机构名称 关键项-- 主检医师技能考核（项目结果与备注） 质控类别 存在问题
        DynamicRowPO rowPO = new DynamicRowPO();
        if(ifExport){
            this.exportHeadRowList.add(rowPO);
        }else{
            //用于大标题合并列
            this.topColSpan = 0;
            this.headRowList.add(rowPO);
        }
        List<DynamicColPO> cols = new ArrayList<>();
        rowPO.setCols(cols);
        cols.add(this.headColHelper("序号", 2, 1,"width:60px;height:30px;" , 2000));
        cols.add(this.headColHelper("地区", 2, 1,"width:100px;" , 4000));
        cols.add(this.headColHelper("机构名称", 2, 1,"width:260px;" , 6000));
        int nomalWidth = 2567;
        //项类
        if(!CollectionUtils.isEmpty(this.itemTypeList)){
            for(TsSimpleCode simpleCode : this.itemTypeList){
                List<TsSimpleCode> tmpList = this.itemTypeWithScoreRstMap.get(simpleCode.getRid());
                if(CollectionUtils.isEmpty(tmpList)){
                    continue;
                }
                int rowspan = tmpList.size();
                cols.add(this.headColHelper(simpleCode.getCodeName(), 1, rowspan,"width:"+(rowspan*100)+"px;" , nomalWidth));
            }
        }
        cols.add(this.headColHelper("主检医师技能考核", 1, ifExport ? 4 : 2,ifExport ? "width:200px;": "width:100px;" , nomalWidth));
        cols.add(this.headColHelper("质控类别", 2, 1,"width:100px;" , nomalWidth));
        if(ifExport){
            cols.add(this.headColHelper("存在问题", 2, 1,"width:100px;" , 8000));
        }
        if(!ifExport){
            for(DynamicColPO colPO : cols){
                this.topColSpan += colPO.getColspan();
            }
        }

        rowPO = new DynamicRowPO();
        if(ifExport){
            this.exportHeadRowList.add(rowPO);
        }else{
            this.headRowList.add(rowPO);
        }
        cols = new ArrayList<>();
        rowPO.setCols(cols);
        //项类结果
        if(!CollectionUtils.isEmpty(this.itemTypeList)){
            for(TsSimpleCode simpleCode : this.itemTypeList){
                List<TsSimpleCode> tmpList = this.itemTypeWithScoreRstMap.get(simpleCode.getRid());
                if(CollectionUtils.isEmpty(tmpList)){
                    continue;
                }
                for(TsSimpleCode subSimpleCode : tmpList){
                    cols.add(this.headColHelper(subSimpleCode.getCodeName(), 1, 1,"width:100px;" , nomalWidth));
                }
            }
        }

        cols.add(this.headColHelper("胸片", 1, 1,"width:100px;" , nomalWidth));
        if(ifExport){
            cols.add(this.headColHelper("备注", 1, 1,"width:100px;" , 4000));
        }
        cols.add(this.headColHelper("电测听", 1, 1,"width:100px;" , nomalWidth));
        if(ifExport){
            cols.add(this.headColHelper("备注", 1, 1,"width:100px;" , 4000));
        }

        if(ifExport){
            rowPO = new DynamicRowPO();
            //大标题 放第一行
            this.exportHeadRowList.add(0,rowPO);
            cols = new ArrayList<>();
            rowPO.setCols(cols);
            cols.add(this.headColHelper(this.exportFileName, 1, this.topColSpan+3,"width:100px;" , 4000));
        }
    }

    /**
     * <p>方法描述： 初始化 </p>
     * @MethodAuthor： pw 2022/11/7
     **/
    private void init(){
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        if (tsZone == null) {
            tsZone = new TsZone();
        }
        //当前登录用户所在单位的管辖地区是否省级
        if (tsZone.getRealZoneType() != null) {
            this.ifProvUser = tsZone.getRealZoneType() == 2;
        }
        //地区
        this.searchZoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "4");
        this.searchZoneCode = this.searchZoneList.get(0).getZoneCode();
        this.searchZoneName = this.searchZoneList.get(0).getZoneName();

        //考核类型
        this.checkTypeMap = new HashMap<>();
        this.checkTypeList = this.commService.findNumSimpleCodesByTypeId("5554");
        if(!CollectionUtils.isEmpty(this.checkTypeList)){
            //默认第一个考核类型
            this.checkTypeRid = this.checkTypeList.get(0).getRid();
            for(TsSimpleCode simpleCode : this.checkTypeList){
                this.checkTypeMap.put(simpleCode.getRid(), simpleCode);
            }
        }

        //考核日期 结束日期默认当天 开始日期默认当年第一天
        this.searchCheckDateEnd = new Date();
        this.searchCheckDateStart = DateUtils.getYearFirstDay(this.searchCheckDateEnd);

        //质控类别
        this.inquiryExamTypeList = new ArrayList<>();

        //考核等级
        this.checkRstMap = new HashMap<>();
        this.checkRstList = this.commService.findNumSimpleCodesByTypeId("5535");
        this.checkRstSelectRids = null;
        this.checkRstSelectNames = null;
        if(!CollectionUtils.isEmpty(this.checkRstList)){
            for(TsSimpleCode simpleCode : this.checkRstList){
                this.checkRstMap.put(simpleCode.getRid(), simpleCode);
                this.selectCheckRstMap.put(simpleCode.getCodeName(), simpleCode.getRid().toString());
            }
        }

        //项类
        this.itemTypeMap = new HashMap<>();
        this.itemTypeList = this.commService.findNumSimpleCodesByTypeId("5533");

        //评分项结果
        this.itemTypeWithScoreRstMap = new HashMap<>();
        this.scoreRstList = this.commService.findNumSimpleCodesByTypeId("5534");

        if(!CollectionUtils.isEmpty(this.itemTypeList) && !CollectionUtils.isEmpty(this.scoreRstList)){
            for(TsSimpleCode simpleCode : this.itemTypeList){
                Integer rid = simpleCode.getRid();
                String codeNo = simpleCode.getCodeNo();
                List<TsSimpleCode> tmpList = new ArrayList<>();
                for(TsSimpleCode scoreRst : this.scoreRstList){
                    String extends3 = scoreRst.getExtendS3();
                    if(StringUtils.isNotBlank(extends3) && Arrays.asList(extends3.split(",")).contains(codeNo)){
                        tmpList.add(scoreRst);
                    }
                }
                if(CollectionUtils.isEmpty(tmpList)){
                    continue;
                }
                this.itemTypeWithScoreRstMap.put(rid, tmpList);
            }
        }
    }

    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public Integer getCheckTypeRid() {
        return checkTypeRid;
    }

    public void setCheckTypeRid(Integer checkTypeRid) {
        this.checkTypeRid = checkTypeRid;
    }

    public List<TsSimpleCode> getCheckTypeList() {
        return checkTypeList;
    }

    public void setCheckTypeList(List<TsSimpleCode> checkTypeList) {
        this.checkTypeList = checkTypeList;
    }

    public Map<Integer, TsSimpleCode> getCheckTypeMap() {
        return checkTypeMap;
    }

    public void setCheckTypeMap(Map<Integer, TsSimpleCode> checkTypeMap) {
        this.checkTypeMap = checkTypeMap;
    }

    public Date getSearchCheckDateStart() {
        return searchCheckDateStart;
    }

    public void setSearchCheckDateStart(Date searchCheckDateStart) {
        this.searchCheckDateStart = searchCheckDateStart;
    }

    public Date getSearchCheckDateEnd() {
        return searchCheckDateEnd;
    }

    public void setSearchCheckDateEnd(Date searchCheckDateEnd) {
        this.searchCheckDateEnd = searchCheckDateEnd;
    }

    public Boolean getIfProvUser() {
        return ifProvUser;
    }

    public void setIfProvUser(Boolean ifProvUser) {
        this.ifProvUser = ifProvUser;
    }

    public List<Integer> getInquiryExamTypeList() {
        return inquiryExamTypeList;
    }

    public void setInquiryExamTypeList(List<Integer> inquiryExamTypeList) {
        this.inquiryExamTypeList = inquiryExamTypeList;
    }

    public List<TsSimpleCode> getCheckRstList() {
        return checkRstList;
    }

    public void setCheckRstList(List<TsSimpleCode> checkRstList) {
        this.checkRstList = checkRstList;
    }

    public Map<Integer, TsSimpleCode> getCheckRstMap() {
        return checkRstMap;
    }

    public void setCheckRstMap(Map<Integer, TsSimpleCode> checkRstMap) {
        this.checkRstMap = checkRstMap;
    }

    public Map<String, String> getSelectCheckRstMap() {
        return selectCheckRstMap;
    }

    public void setSelectCheckRstMap(Map<String, String> selectCheckRstMap) {
        this.selectCheckRstMap = selectCheckRstMap;
    }

    public String getCheckRstSelectRids() {
        return checkRstSelectRids;
    }

    public void setCheckRstSelectRids(String checkRstSelectRids) {
        this.checkRstSelectRids = checkRstSelectRids;
    }

    public String getCheckRstSelectNames() {
        return checkRstSelectNames;
    }

    public void setCheckRstSelectNames(String checkRstSelectNames) {
        this.checkRstSelectNames = checkRstSelectNames;
    }

    public List<TsSimpleCode> getItemTypeList() {
        return itemTypeList;
    }

    public void setItemTypeList(List<TsSimpleCode> itemTypeList) {
        this.itemTypeList = itemTypeList;
    }

    public Map<Integer, TsSimpleCode> getItemTypeMap() {
        return itemTypeMap;
    }

    public void setItemTypeMap(Map<Integer, TsSimpleCode> itemTypeMap) {
        this.itemTypeMap = itemTypeMap;
    }

    public List<TsSimpleCode> getScoreRstList() {
        return scoreRstList;
    }

    public void setScoreRstList(List<TsSimpleCode> scoreRstList) {
        this.scoreRstList = scoreRstList;
    }

    public Map<Integer, List<TsSimpleCode>> getItemTypeWithScoreRstMap() {
        return itemTypeWithScoreRstMap;
    }

    public void setItemTypeWithScoreRstMap(Map<Integer, List<TsSimpleCode>> itemTypeWithScoreRstMap) {
        this.itemTypeWithScoreRstMap = itemTypeWithScoreRstMap;
    }

    public Boolean getIfCheckRstCanSelect() {
        return ifCheckRstCanSelect;
    }

    public void setIfCheckRstCanSelect(Boolean ifCheckRstCanSelect) {
        this.ifCheckRstCanSelect = ifCheckRstCanSelect;
    }

    public List<Integer> getCheckMainRidList() {
        return checkMainRidList;
    }

    public void setCheckMainRidList(List<Integer> checkMainRidList) {
        this.checkMainRidList = checkMainRidList;
    }

    public Boolean getIfQueryedDeduct() {
        return ifQueryedDeduct;
    }

    public void setIfQueryedDeduct(Boolean ifQueryedDeduct) {
        this.ifQueryedDeduct = ifQueryedDeduct;
    }
}
