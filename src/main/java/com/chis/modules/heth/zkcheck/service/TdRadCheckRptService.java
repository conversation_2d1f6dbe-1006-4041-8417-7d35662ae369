package com.chis.modules.heth.zkcheck.service;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zkcheck.entity.TdRadCheckRpt;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>类描述： 放射卫生技术服务机构检测报告上传服务类 </p>
 * @ClassAuthor： pw 2023/6/17
 **/
@Service
@Transactional(readOnly = false)
public class TdRadCheckRptService extends AbstractTemplate {
    /**
     * <p>方法描述： 删除 </p>
     * @MethodAuthor： pw 2023/6/17
     **/
    public void deleteByRid(Integer rid) {
        Map<String, Object> paramMap = new HashMap<>(16);
        String sql = "DELETE FROM TD_RAD_CHECK_RPT WHERE RID = :rid";
        paramMap.put("rid", rid);
        executeSql(sql, paramMap);
    }

    /**
     * <p>方法描述： </p>
     * @MethodAuthor： pw 2023/6/17
     **/
    public void saveOrUpdateCheckRpt(TdRadCheckRpt checkRpt){
        if(checkRpt==null){
            return;
        }
        this.upsertEntity(checkRpt);
    }

    /**
     * <p>方法描述： 批量审核通过 </p>
     * @MethodAuthor： pw 2023/6/17
     **/
    public void reviewBatchAction(List<Integer> ridList){
        if(CollectionUtils.isEmpty(ridList)){
            return;
        }
        String updateSql = "UPDATE TD_RAD_CHECK_RPT SET STATE=2,CHECK_PSN_ID =:checkPsnId,CHECK_DATE = TO_DATE(:checkDate,'YYYY-MM-DD'),MODIFY_DATE=sysdate,MODIFY_MANID=:checkPsnId WHERE RID IN(:ridList) ";
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("checkPsnId", Global.getUser().getRid());
        paramMap.put("checkDate", DateUtils.formatDate(new Date()));
        List<List<Integer>> groupList = StringUtils.splitListProxy(ridList, 1000);
        for(List<Integer> tmpRidList : groupList){
            paramMap.put("ridList", tmpRidList);
            this.executeSql(updateSql, paramMap);
        }
    }
}
