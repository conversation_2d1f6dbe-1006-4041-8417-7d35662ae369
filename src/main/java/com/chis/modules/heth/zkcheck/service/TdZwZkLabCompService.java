package com.chis.modules.heth.zkcheck.service;

import com.chis.common.utils.DateUtils;
import com.chis.modules.heth.zkcheck.entity.TdZwZkCheckJudge;
import com.chis.modules.heth.zkcheck.entity.TdZwZkCheckMain;
import com.chis.modules.heth.zkcheck.entity.TdZwZkLabComp;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Transactional(readOnly = false)
public class TdZwZkLabCompService extends AbstractTemplate {


    /**
    * <p>Description：获取同机构，同考核类型，考核日期同年份的实验室比对记录 </p>
    * <p>Author： yzz 2025/8/12 </p>
    */
    public TdZwZkLabComp findLatestLabComp(TdZwZkCheckMain checkMain) {
        if(checkMain==null || checkMain.getRid()==null || checkMain.getFkByOrgId()==null || checkMain.getFkByOrgId().getRid()==null || checkMain.getFkByCheckTypeId()==null || checkMain.getFkByCheckTypeId().getRid()==null || checkMain.getCheckDate()==null){
            return null;
        }
        Integer orgId=checkMain.getFkByOrgId().getRid();
        Integer checkTypeId=checkMain.getFkByCheckTypeId().getRid();
        String checkYear= DateUtils.formatDate(checkMain.getCheckDate(),"yyyy");
        //获取同被考核机构，同考核类型，同年份最新一条记录
        String hql="select t from TdZwZkLabComp t where t.fkByOrgId.rid="+orgId+" and t.fkByCheckTypeId.rid="+checkTypeId+" and to_char(t.planDate,'yyyy')="+checkYear+" order by t.planDate desc";
        return this.findOneByHql(hql,TdZwZkLabComp.class);

    }
}
