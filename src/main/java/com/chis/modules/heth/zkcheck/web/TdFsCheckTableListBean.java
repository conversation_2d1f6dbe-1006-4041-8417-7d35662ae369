package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zkcheck.entity.*;
import com.chis.modules.heth.zkcheck.service.TdFsCheckTableListService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 质控考核表维护
 */
@ManagedBean(name = "tdFsCheckTableListBean")
@ViewScoped
public class TdFsCheckTableListBean extends FacesEditBean {
    protected CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    protected TdFsCheckTableListService checkTableListService = SpringContextHolder.getBean(TdFsCheckTableListService.class);

    /**
     * 列表结果
     */
    private List<Object[]> tableList;

    /**
     * 修改-当前操作行对象
     */
    private Object[] standObj;

    /**
     * 当前操作记录rid
     */
    private Integer rid;

    /**
     * 年份 列表
     */
    private List<Integer> yearList;
    /**
     * 当前年份
     */
    private Integer year;

    /**
     * 考核类型 码表
     */
    private List<TsSimpleCode> checkTypeList;
    /**
     * key:码表rid
     */
    private Map<Integer, TsSimpleCode> checkTypeMap;

    /**
     * 选中的考核类型rid
     */
    private Integer checkTypeRid;

    /**
     * 质控指标维护列表
     */
    private List<TbZwZkBadrsnStand> standList;
    /**
     * 左侧-当前选中行
     */
    private TbZwZkBadrsnStand selStand;

    /**
     * 分值列表
     */
    private List<TbZwZkScores> scoresList;

    /**
     * 指标-特殊标记
     */
    private List<TsSimpleCode> standSpFlagList;
    /*key:码表rid*/
    private Map<Integer, TsSimpleCode> standSpFlagMap;
    /*key:extends1*/
    private Map<String, TsSimpleCode> standSpFlagwithExtends1Map;

    /**
     * 项类 码表
     */
    private List<TsSimpleCode> itemTypeList;

    /**
     * key:码表rid
     */
    private Map<Integer, TsSimpleCode> itemTypeMap;
    /**
     * 项类-特殊标记
     */
    private List<TsSimpleCode> specialFlagWithScoreList;
    /**
     * key:码表rid
     */
    private Map<Integer, TsSimpleCode> specialFlagWithScoreMap;
    /**
     * key:码表Extends1
     */
    private Map<String, TsSimpleCode> specialFlagWithScoreExtends1Map;

    /**
     * 特殊标记说明 码表
     */
    private List<TsSimpleCode> busExtendsList;
    /**
     * key:码表rid
     */
    private Map<Integer, TsSimpleCode> busExtendsMap;
    /**
     * key:码表Extends3
     */
    private Map<String, TsSimpleCode> busExtendsWithExtends3Map;
    /**
     * 左侧-添加实体
     */
    private TbZwZkBadrsnStand addStand;

    /**
     * 分值表rid
     */
    private TbZwZkScores score;

    /**
     * 存在问题
     */
    private List<TbZwZkScoreDeduct> scoreDeductList;

    /**
     * 存在问题记录
     */
    private Integer deductRid;
    /**
     * 指标大类Map
     */
    private Map<String,TsSimpleCode> firstIndexMap;

    public TdFsCheckTableListBean() {
        this.tableList = new ArrayList<>();
        initSimp();
        initYear();
        this.searchTableAction();
    }


    /**
     * <p>Description：初始化码表 </p>
     * <p>Author： yzz 2024-11-02 </p>
     */
    public void initSimp() {
        // 考核类型 码表初始化
        this.checkTypeMap = new HashMap<>();
        this.checkTypeList = this.commService.findNumSimpleCodesByTypeId("5554");
        if (!CollectionUtils.isEmpty(this.checkTypeList)) {
            for (TsSimpleCode tsSimpleCode : this.checkTypeList) {
                this.checkTypeMap.put(tsSimpleCode.getRid(), tsSimpleCode);
            }
        }
        // 指标特殊标记
        this.standSpFlagMap = new HashMap<>();
        this.standSpFlagwithExtends1Map = new HashMap<>();
        this.standSpFlagList = this.commService.findNumSimpleCodesByTypeId("5637");
        if (!CollectionUtils.isEmpty(this.standSpFlagList)) {
            for (TsSimpleCode tsSimpleCode : this.standSpFlagList) {
                this.standSpFlagMap.put(tsSimpleCode.getRid(), tsSimpleCode);
                if (StringUtils.isNotBlank(tsSimpleCode.getExtendS1())) {
                    this.standSpFlagwithExtends1Map.put(tsSimpleCode.getExtendS1(), tsSimpleCode);
                }
            }
        }
        // 特殊标记
        this.specialFlagWithScoreList = this.commService.findNumSimpleCodesByTypeId("5638");
        this.specialFlagWithScoreMap = new HashMap<>();
        this.specialFlagWithScoreExtends1Map = new HashMap<>();
        if (!CollectionUtils.isEmpty(specialFlagWithScoreList)) {
            for (TsSimpleCode tsSimpleCode : specialFlagWithScoreList) {
                specialFlagWithScoreMap.put(tsSimpleCode.getRid(), tsSimpleCode);
                if (tsSimpleCode.getExtendS1() != null) {
                    specialFlagWithScoreExtends1Map.put(tsSimpleCode.getExtendS1(), tsSimpleCode);
                }
            }

        }
        // 特殊标记说明
        this.busExtendsList = this.commService.findNumSimpleCodesByTypeId("5639");
        this.busExtendsMap = new HashMap<>();
        this.busExtendsWithExtends3Map = new HashMap<>();
        if (!CollectionUtils.isEmpty(this.busExtendsList)) {
            for (TsSimpleCode tsSimpleCode : this.busExtendsList) {
                this.busExtendsMap.put(tsSimpleCode.getRid(), tsSimpleCode);
                if (StringUtils.isNotBlank(tsSimpleCode.getExtendS3())) {
                    busExtendsWithExtends3Map.put(tsSimpleCode.getExtendS3(), tsSimpleCode);
                }
            }
        }

        // 项类
        this.itemTypeList = this.commService.findNumSimpleCodesByTypeId("5533");
        this.itemTypeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(itemTypeList)) {
            for (TsSimpleCode tsSimpleCode : itemTypeList) {
                itemTypeMap.put(tsSimpleCode.getRid(), tsSimpleCode);
            }
        }

        //指标
        List<TsSimpleCode> firstIndexList = this.commService.findNumSimpleCodesByTypeId("5530");
        this.firstIndexMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(firstIndexList)){
            for (TsSimpleCode tsSimpleCode : firstIndexList) {
                this.firstIndexMap.put(tsSimpleCode.getCodeLevelNo(), tsSimpleCode);
            }
        }
    }

    /**
     * <p>Description：初始化年份 </p>
     * <p>Author： yzz 2024-11-02 </p>
     */
    public void initYear() {
        year = DateUtils.getYearInt();
        yearList = new ArrayList<>();
        for (int k = 0; k < 5; k++) {
            yearList.add(year - k);
        }
    }

    /**
     * <p>Description：查询列表数据 </p>
     * <p>Author： yzz 2024-11-02 </p>
     */
    public void searchTableAction() {
        String sql = " select T.RID, " +
                "         T2.CODE_NAME as CHECK_TYPE_NAME, " +
                "         T.YEAR, " +
                "         T.CHECK_NAME, " +
                "         T2.rid       as CHECK_TYPE_RID, " +
                "         0            as rowspan1, " +
                "         0            as rowspan2 " +
                " from TB_ZW_ZK_BADRSN_STAND T " +
                " left join TS_SIMPLE_CODE T2 on T.CHECK_TYPE_ID = T2.RID " +
                " where T.STATE_MARK = 1 " +
                " order by T2.NUM, T2.CODE_LEVEL_NO, T2.CODE_NO, T.YEAR, T.XH ";
        this.tableList = this.commService.findDataBySqlNoPage(sql, null);
        dealRowSpan();
    }

    @Override
    public void searchAction() {

    }

    /**
     * <p>Description：计算列表合并单元格 </p>
     * <p>Author： yzz 2024-11-02 </p>
     */
    private void dealRowSpan() {
        if (CollectionUtils.isEmpty(this.tableList)) {
            return;
        }
        String ridStr = null;
        int rowSpan = 0;
        String ridAndYearStr = null;
        int rowSpan2 = 0;
        int index = 0;
        int index2 = 0;
        for (int i = 0; i < tableList.size(); i++) {
            Object[] obj = tableList.get(i);
            if (StringUtils.isBlank(ridStr)) {
                ridStr = obj[4].toString();
                ridAndYearStr = ridStr + "&" + (obj[2] == null ? "" : obj[2].toString());
                obj[2] = obj[2] == null ? "9999" : obj[2].toString();
                rowSpan = 1;
                rowSpan2 = 1;
                index = i;
                index2 = i;
            } else {
                if (ridStr.equals(obj[4].toString())) {
                    rowSpan += 1;
                    obj[1] = null;
                } else {
                    ridStr = obj[4].toString();
                    tableList.get(index)[5] = rowSpan;
                    rowSpan = 1;
                    index = i;
                }
                if (ridAndYearStr.equals(ridStr + "&" + (obj[2] == null ? "" : obj[2].toString()))) {
                    obj[2] = null;
                    rowSpan2 += 1;
                } else {
                    ridAndYearStr = ridStr + "&" + (obj[2] == null ? "" : obj[2].toString());
                    tableList.get(index2)[6] = rowSpan2;
                    obj[2] = obj[2] == null ? "9999" : obj[2].toString();
                    rowSpan2 = 1;
                    index2 = i;
                }
            }
            if (i == tableList.size() - 1) {
                tableList.get(index)[5] = rowSpan;
                tableList.get(index2)[6] = rowSpan2;
            }
        }
    }

    /**
     * <p>Description：左侧-添加评估表 </p>
     * <p>Author： yzz 2024-11-02 </p>
     */
    public void addStandAction() {
        if (verifyStand()) {
            return;
        }
        this.addStand = new TbZwZkBadrsnStand();
        this.addStand.setFkByCheckTypeId(new TsSimpleCode());
        this.checkTableListService.preEntity(this.addStand);
        RequestContext.getCurrentInstance().execute("PF('AddStandDialog').show()");
    }
    /**
     * <p>Description：修改考核表 </p>
     * <p>Author： yzz 2024-11-04 </p>
     */
    public void modStandAction() {
        if (verifyStand()) {
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('AddStandDialog').show()");
    }

    /**
     * <p>Description：左侧-删除 </p>
     * <p>Author： yzz 2024-11-02 </p>
     */
    public void delStandAction() {
        if (this.addStand == null || this.addStand.getRid() == null) {
            return;
        }
        try {
            this.checkTableListService.delStand(this.addStand.getRid());
            initStandList(Integer.parseInt(standObj[2].toString()), Integer.parseInt(standObj[4].toString()));
            if(CollectionUtils.isEmpty(this.standList)){
                this.scoresList=new ArrayList<>();
                this.selStand=null;
            }
            this.searchTableAction();
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("已有业务数据关联，删除失败！");
        }
    }

    /**
     * <p>Description：验证考核表 </p>
     * <p>Author： yzz 2024-11-04 </p>
     */
    public boolean verifyStand() {
        boolean flag = false;
        if (this.checkTypeRid == null) {
            JsfUtil.addErrorMessage("请选择考核类型！");
            flag = true;
        }
        if (this.year == null) {
            JsfUtil.addErrorMessage("请选择年份！");
            flag = true;
        }
        // 验证该年份下有无同类型考核表
        List<Integer> rids = new ArrayList<>();
        if (!CollectionUtils.isEmpty(standList)) {
            for (TbZwZkBadrsnStand tbZwZkBadrsnStand : standList) {
                rids.add(tbZwZkBadrsnStand.getRid());
            }
        }
        if (this.checkTableListService.checkStand(this.year, this.checkTypeRid, rids)) {
            JsfUtil.addErrorMessage("该年份下已存在同类型评估表！");
            flag = true;
        }
        return flag;
    }

    /**
     * <p>Description：保存指标 </p>
     * <p>Author： yzz 2024-11-02 </p>
     */
    public void saveStandAction() {
        boolean flag = false;
        if (StringUtils.isBlank(this.addStand.getCheckName())) {
            JsfUtil.addErrorMessage("评估表名称不能为空！");
            flag = true;
        }
        if (flag) {
            return;
        }
        this.addStand.setFkByCheckTypeId(new TsSimpleCode(this.checkTypeRid));
        // 扩展字段为3或者4时赋值
        if (!this.checkTypeMap.isEmpty()
                && this.checkTypeMap.containsKey(this.checkTypeRid)
                && ("3".equals(this.checkTypeMap.get(this.checkTypeRid).getExtendS1())
                || "4".equals(this.checkTypeMap.get(this.checkTypeRid).getExtendS1()))) {
            this.addStand.setCheckType(Integer.parseInt(this.checkTypeMap.get(this.checkTypeRid).getExtendS1()));
        }
        this.addStand.setYear(this.year);
        this.addStand.setStateMark(1);
        // 特殊标记赋值
        if (this.addStand.getSelSpFlagId() != null
                && this.standSpFlagMap.containsKey(this.addStand.getSelSpFlagId())
                && StringUtils.isNotBlank(this.standSpFlagMap.get(this.addStand.getSelSpFlagId()).getExtendS1())) {
            this.addStand.setSpecialFlag(this.standSpFlagMap.get(this.addStand.getSelSpFlagId()).getExtendS1());
        }
        try {
            this.checkTableListService.saveStand(this.addStand,this.standObj,this.checkTypeMap);
            initStandList(this.year, this.checkTypeRid);
            RequestContext.getCurrentInstance().update("tabView:editForm:standTable");
            RequestContext.getCurrentInstance().update("tabView:editForm:scoresGrid");
            RequestContext.getCurrentInstance().execute("PF('AddStandDialog').hide()");
            // 默认选中当前行
            JsfUtil.addSuccessMessage("保存成功！");
            this.searchTableAction();
            if(this.standObj!=null){
                this.standObj[2]=this.year;
                this.standObj[4]=this.checkTypeRid;
            }else{
                this.standObj=new Object[6];
                this.standObj[2]=this.year;
                this.standObj[4]=this.checkTypeRid;
            }
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
    * <p>Description：查询评估表 </p>
    * <p>Author： yzz 2024-11-05 </p>
    */
    public void initStandList(Integer year, Integer checkTypeId){
        this.standList = this.checkTableListService.findStandList(year, checkTypeId, this.standSpFlagwithExtends1Map);
        if(!CollectionUtils.isEmpty(this.standList)){
            this.selStand = this.standList.get(0);
            initScroreList();
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("tabView:editForm:standTable");
            dataTable.setSelection(selStand);
            dataTable.setFirst(0);
            dataTable.setRows(10);
        }
    }

    /**
     * <p>Description：左侧-行选中事件 </p>
     * <p>Author： yzz 2024-08-09 </p>
     */
    public void onRowSelect(SelectEvent event) {
        // 保存右侧
        this.checkTableListService.saveBatchScores(this.scoresList, this.itemTypeMap, this.specialFlagWithScoreMap, this.busExtendsMap);
        // 选中行 查询当前考核表对应的分值表
        selStand = (TbZwZkBadrsnStand) event.getObject();
        initScroreList();
    }


    /**
    * <p>Description：初始化指标列表 </p>
    * <p>Author： yzz 2024-11-06 </p>
    */
    public void initScroreList(){
        this.scoresList = this.checkTableListService.findScoresList(this.selStand.getRid(), this.specialFlagWithScoreExtends1Map, this.busExtendsWithExtends3Map);
        // 拼接存在问题
        if (CollectionUtils.isEmpty(this.scoresList)) {
            return;
        }
        for (TbZwZkScores scores : this.scoresList) {
            spliceDeductStr(scores.getDeductList(), scores);
        }
    }


    /**
     * <p>Description：右侧-添加分值表 </p>
     * <p>Author： yzz 2024-11-02 </p>
     */
    public void addScoresAction() {
        if (this.selStand == null) {
            JsfUtil.addErrorMessage("请选择一条评估表记录！");
            return;
        }

        Map<String, Object> options = MapUtils.produceDialogMap(null, 800, null, 550);
        Map<String, List<String>> paramMap = new HashMap<>();
        // 弹框标题
        List<String> paramList = new ArrayList<>();
        paramList.add("考核指标");
        paramMap.put("titleName", paramList);

        // 类型编号
        paramList = new ArrayList<>();
        paramList.add("5530");
        paramMap.put("typeNo", paramList);

        // 查询条件名称
        paramList = new ArrayList<>();
        paramList.add("考核指标名称");
        paramMap.put("searchName", paramList);

        // 显示大类查询条件
        paramList = new ArrayList<>();
        paramList.add("true");
        paramMap.put("ifShowFirstCode", paramList);

        // 排序方式
        paramList = new ArrayList<>();
        paramList.add("2");
        paramMap.put("ifShowExpiryDate", paramList);

        // 提交时只提交最末级
        paramList = new ArrayList<>();
        paramList.add("1");
        paramMap.put("selectLast", paramList);

        //末级全选时，父级不显示
        paramList = new ArrayList<>();
        paramList.add("1");
        paramMap.put("ifShowFirst", paramList);

        // 去除所有评估表已选择的考核指标
        List<Object> indexRids = this.checkTableListService.findScoresRidsByStandRid(this.selStand);
        if (!CollectionUtils.isEmpty(indexRids)) {
            paramList = new ArrayList<>();
            paramList.add(StringUtils.list2string(indexRids, ","));
            paramMap.put("notSelectIds", paramList);
        }
        RequestContext.getCurrentInstance().openDialog("/webapp/system/codeMulitySelectList", options, paramMap);
    }

    /**
     * <p>Description：选择考核指标后事件 </p>
     * <p>Author： yzz 2024-11-04 </p>
     */
    public void onSimpleCodeAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (selectedMap == null || selectedMap.isEmpty()) {
            return;
        }
        List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<TbZwZkScores> scoresList = new ArrayList<>();
        for (TsSimpleCode tsSimpleCode : list) {
            TbZwZkScores scores = new TbZwZkScores();
            scores.setFkByMainId(this.selStand);
            scores.setFkByIndexId(tsSimpleCode);
            this.checkTableListService.preEntity(scores);

            List<TbZwZkScoreIndex> scoreIndexList = new ArrayList<>();
            TbZwZkScoreIndex scoreIndex = new TbZwZkScoreIndex();
            scoreIndex.setFkByMainId(scores);
            scoreIndex.setFkByIndexId(tsSimpleCode);
            if(StringUtils.isNotBlank(tsSimpleCode.getCodeLevelNo())
                    && tsSimpleCode.getCodeLevelNo().contains(".")){
                int lastDotIndex = tsSimpleCode.getCodeLevelNo().lastIndexOf('.');
                String lastCodeLevelNo = tsSimpleCode.getCodeLevelNo().substring(0, lastDotIndex);
                TsSimpleCode lastSimple = this.firstIndexMap.get(lastCodeLevelNo);
                if(lastSimple!=null){
                    scoreIndex.setFkByFirstIndexId(lastSimple);
                    scores.setFirstIndexName(lastSimple.getCodeName());
                }
            }
            this.checkTableListService.preEntity(scoreIndex);
            scoreIndexList.add(scoreIndex);

            scores.setIndexList(scoreIndexList);
            scoresList.add(scores);
        }
        try {
            this.checkTableListService.saveScoresList(scoresList);
            initScroreList();
            JsfUtil.addSuccessMessage("保存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
    * <p>Description：保存评估表 </p>
    * <p>Author： yzz 2024-11-07 </p>
    */
    public void saveScoresAction(){
        if(CollectionUtils.isEmpty(this.scoresList)){
            JsfUtil.addErrorMessage("请添加考核指标记录！");
            return;
        }
        try{
            // 保存右侧指标
            this.checkTableListService.saveBatchScores(this.scoresList, this.itemTypeMap, this.specialFlagWithScoreMap, this.busExtendsMap);
            initScroreList();
            JsfUtil.addSuccessMessage("暂存成功！");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("暂存失败！");
        }
    }

    /**
     * <p>Description：删除分值标 </p>
     * <p>Author： yzz 2024-11-04 </p>
     */
    public void delScoreAction() {
        if (this.score == null || this.selStand.getRid() == null) {
            return;
        }
        try {
            this.checkTableListService.delScoreByRid(this.score.getRid());
            initScroreList();
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("已有业务数据关联，删除失败！");
        }
    }


    /**
     * <p>Description：添加存在问题 链接 </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public void addDeductLinkAction() {
        this.scoreDeductList = this.checkTableListService.findDeductByMainId(this.score.getRid());
        RequestContext.getCurrentInstance().execute("PF('AddDeductDialog').show()");
    }

    /**
     * <p>Description：存在问题  添加按钮 </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public void addDeductBtnAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 800, null, 550);
        Map<String, List<String>> paramMap = new HashMap<>();
        // 弹框标题
        List<String> paramList = new ArrayList<>();
        paramList.add("存在问题");
        paramMap.put("titleName", paramList);

        // 类型编号
        paramList = new ArrayList<>();
        paramList.add("5531");
        paramMap.put("typeNo", paramList);

        // 查询条件名称
        paramList = new ArrayList<>();
        paramList.add("存在问题名称");
        paramMap.put("searchName", paramList);

        // 排序方式
        paramList = new ArrayList<>();
        paramList.add("2");
        paramMap.put("ifShowExpiryDate", paramList);

        // 去除已选择的存在问题
        List<Object> deductRids = this.checkTableListService.findDeductRidsByScoreRid(this.score.getRid());
        if (!CollectionUtils.isEmpty(deductRids)) {
            paramList = new ArrayList<>();
            paramList.add(StringUtils.list2string(deductRids, ","));
            paramMap.put("notSelectIds", paramList);
        }
        RequestContext.getCurrentInstance().openDialog("/webapp/system/codeMulitySelectList", options, paramMap);
    }


    /**
     * <p>Description：存在问题选择后事件 </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public void onselDeductAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (selectedMap == null || selectedMap.isEmpty()) {
            return;
        }
        List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<TbZwZkScoreDeduct> deducts = new ArrayList<>();
        for (TsSimpleCode tsSimpleCode : list) {
            TbZwZkScoreDeduct deduct = new TbZwZkScoreDeduct();
            deduct.setFkByMainId(this.score);
            deduct.setFkByDeductId(tsSimpleCode);
            this.checkTableListService.preEntity(deduct);
            deducts.add(deduct);
        }
        try {
            this.checkTableListService.saveDeduct(deducts);
            refreshScoreAndDeduct();
            RequestContext.getCurrentInstance().update("tabView:editForm:deductTable");
            RequestContext.getCurrentInstance().update("tabView:editForm:scoresTable");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * <p>Description：刷新分值表 </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public void refreshScoreAndDeduct() {
        this.scoreDeductList = this.checkTableListService.findDeductByMainId(this.score.getRid());
        spliceDeductStr(this.scoreDeductList, this.score);
        // 重新赋值
        this.score.setDeductList(this.scoreDeductList);
    }

    /**
     * <p>Description：拼接存在问题 </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public void spliceDeductStr(List<TbZwZkScoreDeduct> scoreDeductList, TbZwZkScores score) {
        StringBuffer deductBuffer1 = new StringBuffer();
        StringBuffer deductBuffer2 = new StringBuffer();
        for (TbZwZkScoreDeduct scoreDeduct : scoreDeductList) {
            deductBuffer1.append("；").append(scoreDeduct.getFkByDeductId().getCodeName());
            deductBuffer2.append("；<br/>").append(scoreDeduct.getFkByDeductId().getCodeName());
        }
        if (StringUtils.isNotBlank(deductBuffer1)) {
            score.setDeductStr(deductBuffer1.substring(1));
        } else {
            score.setDeductStr(null);
        }
        if (StringUtils.isNotBlank(deductBuffer2)) {
            score.setDeductStr2(deductBuffer2.substring(6));
        } else {
            score.setDeductStr2(null);
        }
    }

    /**
     * <p>Description：保存存在问题 </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public void saveDeductAction() {
        if (CollectionUtils.isEmpty(this.scoreDeductList)) {
            JsfUtil.addErrorMessage("请选择存在问题！");
            return;
        }
        try {
            this.checkTableListService.saveDeduct(this.scoreDeductList);
            refreshScoreAndDeduct();
            RequestContext.getCurrentInstance().execute("PF('AddDeductDialog').hide()");
            RequestContext.getCurrentInstance().update("tabView:editForm:scoresTable");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * <p>Description：删除存在问题 </p>
     * <p>Author： yzz 2024-11-05 </p>
     */
    public void delDeductAction() {
        if (this.deductRid == null) {
            return;
        }
        try {
            this.checkTableListService.delDeductByRid(this.deductRid);
            refreshScoreAndDeduct();
            RequestContext.getCurrentInstance().update("tabView:editForm:deductTable");
            RequestContext.getCurrentInstance().update("tabView:editForm:scoresTable");
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("已有业务数据关联，删除失败！");
        }
    }


    @Override
    public void addInit() {

    }
    @Override
    public void viewInit() {

    }
    @Override
    public void modInit() {
        this.addStand = new TbZwZkBadrsnStand();
        this.standList = new ArrayList<>();
        this.scoresList = new ArrayList<>();
        this.selStand = null;
        if (this.standObj == null) {
            this.checkTypeRid=checkTypeList.get(0).getRid();
            this.year=yearList.get(0);
            return;
        }
        initStandList(Integer.parseInt(standObj[2].toString()), Integer.parseInt(standObj[4].toString()));
        //初始化
        if(!CollectionUtils.isEmpty(this.standList)){
            TbZwZkBadrsnStand stand = this.standList.get(0);
            this.checkTypeRid=stand.getFkByCheckTypeId().getRid();
            this.year=stand.getYear();
        }
        initScroreList();
    }
    @Override
    public void saveAction() {
        // 校验
        if (verifyStand()) {
            return;
        }
        if (CollectionUtils.isEmpty(this.standList)) {
            JsfUtil.addErrorMessage("请添加评估表记录！");
            return;
        }
        boolean flag=false;
        List<Integer> rids=new ArrayList<>();
        for (TbZwZkBadrsnStand stand : this.standList) {
            rids.add(stand.getRid());
        }
        List<Object[]> scoresCount = this.checkTableListService.findScoresCountByRids(rids);
        if(scoresCount.size()>0){
            for (Object[] objects : scoresCount) {
                JsfUtil.addErrorMessage("请添加评估表【"+objects[1]+"】的考核指标记录！");
            }
            flag=true;
        }
        List<Object[]> deductCount = this.checkTableListService.findDeductCountByRids(rids);
        if(deductCount.size()>0){
            for (Object[] objects : deductCount) {
                JsfUtil.addErrorMessage("请添加评估表【"+objects[1]+"】的存在问题！");
            }
            flag=true;
        }
        if(flag){
            return;
        }
        try {
            // 保存右侧
            this.checkTableListService.updateStand(this.standObj, this.scoresList, this.checkTypeRid, this.year, this.itemTypeMap, this.specialFlagWithScoreMap, this.busExtendsMap, this.checkTypeMap);
            initScroreList();
            this.searchTableAction();
            if(this.standObj!=null){
                this.standObj[2]=this.year;
                this.standObj[4]=this.checkTypeRid;
            }
            JsfUtil.addSuccessMessage("保存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }
    @Override
    public String[] buildHqls() {
        return new String[0];
    }


    /**
     * <p>Description：复制 </p>
     * <p>Author： yzz 2024-11-02 </p>
     */
    public void copyAction() {
        try {
            this.checkTableListService.copyTableList(standObj);
            this.searchTableAction();
            JsfUtil.addSuccessMessage("复制成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("复制失败！");
        }
    }

    public List<Object[]> getTableList() {
        return tableList;
    }
    public void setTableList(List<Object[]> tableList) {
        this.tableList = tableList;
    }

    public Integer getRid() {
        return rid;
    }
    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public List<Integer> getYearList() {
        return yearList;
    }
    public void setYearList(List<Integer> yearList) {
        this.yearList = yearList;
    }
    public List<TsSimpleCode> getCheckTypeList() {
        return checkTypeList;
    }
    public void setCheckTypeList(List<TsSimpleCode> checkTypeList) {
        this.checkTypeList = checkTypeList;
    }

    public Integer getYear() {
        return year;
    }
    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getCheckTypeRid() {
        return checkTypeRid;
    }
    public void setCheckTypeRid(Integer checkTypeRid) {
        this.checkTypeRid = checkTypeRid;
    }

    public List<TbZwZkBadrsnStand> getStandList() {
        return standList;
    }
    public void setStandList(List<TbZwZkBadrsnStand> standList) {
        this.standList = standList;
    }
    public List<TbZwZkScores> getScoresList() {
        return scoresList;
    }
    public void setScoresList(List<TbZwZkScores> scoresList) {
        this.scoresList = scoresList;
    }

    public TbZwZkBadrsnStand getSelStand() {
        return selStand;
    }
    public void setSelStand(TbZwZkBadrsnStand selStand) {
        this.selStand = selStand;
    }
    public List<TsSimpleCode> getStandSpFlagList() {
        return standSpFlagList;
    }
    public void setStandSpFlagList(List<TsSimpleCode> standSpFlagList) {
        this.standSpFlagList = standSpFlagList;
    }

    public TbZwZkBadrsnStand getAddStand() {
        return addStand;
    }
    public void setAddStand(TbZwZkBadrsnStand addStand) {
        this.addStand = addStand;
    }
    public Object[] getStandObj() {
        return standObj;
    }
    public void setStandObj(Object[] standObj) {
        this.standObj = standObj;
    }

    public List<TsSimpleCode> getItemTypeList() {
        return itemTypeList;
    }
    public void setItemTypeList(List<TsSimpleCode> itemTypeList) {
        this.itemTypeList = itemTypeList;
    }
    public List<TsSimpleCode> getBusExtendsList() {
        return busExtendsList;
    }
    public void setBusExtendsList(List<TsSimpleCode> busExtendsList) {
        this.busExtendsList = busExtendsList;
    }
    public List<TsSimpleCode> getSpecialFlagWithScoreList() {
        return specialFlagWithScoreList;
    }
    public void setSpecialFlagWithScoreList(List<TsSimpleCode> specialFlagWithScoreList) {
        this.specialFlagWithScoreList = specialFlagWithScoreList;
    }

    public TbZwZkScores getScore() {
        return score;
    }
    public void setScore(TbZwZkScores score) {
        this.score = score;
    }
    public List<TbZwZkScoreDeduct> getScoreDeductList() {
        return scoreDeductList;
    }
    public void setScoreDeductList(List<TbZwZkScoreDeduct> scoreDeductList) {
        this.scoreDeductList = scoreDeductList;
    }

    public Integer getDeductRid() {
        return deductRid;
    }
    public void setDeductRid(Integer deductRid) {
        this.deductRid = deductRid;
    }
}
