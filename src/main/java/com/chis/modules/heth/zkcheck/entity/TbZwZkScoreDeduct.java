package com.chis.modules.heth.zkcheck.entity;

import java.util.Date;

import javax.persistence.*;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-6-26
 */
@Entity
@Table(name = "TB_ZW_ZK_SCORE_DEDUCT")
@SequenceGenerator(name = "TbZwZkScoreDeduct", sequenceName = "TB_ZW_ZK_SCORE_DEDUCT_SEQ", allocationSize = 1)
public class TbZwZkScoreDeduct implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbZwZkScores fkByMainId;
	private TsSimpleCode fkByDeductId;
	private Integer xh;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	/** 临时字段 用于标识是否被选中 */
	private boolean selected;
	
	public TbZwZkScoreDeduct() {
	}

	public TbZwZkScoreDeduct(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbZwZkScoreDeduct")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TbZwZkScores getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TbZwZkScores fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "DEDUCT_ID")			
	public TsSimpleCode getFkByDeductId() {
		return fkByDeductId;
	}

	public void setFkByDeductId(TsSimpleCode fkByDeductId) {
		this.fkByDeductId = fkByDeductId;
	}	
			
	@Column(name = "XH")	
	public Integer getXh() {
		return xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Transient
	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}
}