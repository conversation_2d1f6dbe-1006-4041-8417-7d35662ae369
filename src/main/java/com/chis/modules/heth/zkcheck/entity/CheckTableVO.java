package com.chis.modules.heth.zkcheck.entity;

import java.util.ArrayList;
import java.util.List;

/**
 * 现场质控考核VO
 */
public class CheckTableVO implements java.io.Serializable {
    private static final long serialVersionUID = 1L;
    private String tableName;
    /**
     * 现场考核表
     */
    private TdZwCheckTable tdZwCheckTable;
    /**
     * 现场考核项目
     */
    private List<CheckTableItemVO> checkTableItemVOList;

    public CheckTableVO() {
        this.checkTableItemVOList = new ArrayList<>();
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public TdZwCheckTable getTdZwCheckTable() {
        return tdZwCheckTable;
    }

    public void setTdZwCheckTable(TdZwCheckTable tdZwCheckTable) {
        this.tdZwCheckTable = tdZwCheckTable;
    }

    public List<CheckTableItemVO> getCheckTableItemVOList() {
        return checkTableItemVOList;
    }

    public void setCheckTableItemVOList(List<CheckTableItemVO> checkTableItemVOList) {
        this.checkTableItemVOList = checkTableItemVOList;
    }
}