package com.chis.modules.heth.zkcheck.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2024-7-2
 */
@Entity
@Table(name = "TD_ZK_BADRSNS")
@SequenceGenerator(name = "TdZkBadrsns", sequenceName = "TD_ZK_BADRSNS_SEQ", allocationSize = 1)
public class TdZkBadrsns implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZkExtractRule fkByMainId;
	private TsSimpleCode fkByBadrsnId;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZkBadrsns() {
	}

	public TdZkBadrsns(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZkBadrsns")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZkExtractRule getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZkExtractRule fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "BADRSN_ID")			
	public TsSimpleCode getFkByBadrsnId() {
		return fkByBadrsnId;
	}

	public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
		this.fkByBadrsnId = fkByBadrsnId;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}