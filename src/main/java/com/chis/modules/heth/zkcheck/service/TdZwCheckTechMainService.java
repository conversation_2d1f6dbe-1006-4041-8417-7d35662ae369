package com.chis.modules.heth.zkcheck.service;

import com.chis.common.utils.ObjectCopyUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckTechItem;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckTechMain;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckTechPsn;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 质量控制技能考核
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(readOnly = false)
public class TdZwCheckTechMainService extends AbstractTemplate {

    /**
     * 删除质量控制技能考核记录（标删）
     *
     * @param rid 质量控制技能考核记录RID
     */
    public void deleteCheckTechMainByRid(Integer rid) {
        String sql = "UPDATE TD_ZW_CHECK_TECH_MAIN SET DEL_MARK = 1 WHERE RID = :rid";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", rid);
        super.executeSql(sql, paramMap);
    }

    /**
     * <p>方法描述：保存质量控制技能考核表</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-22
     **/
    public Integer saveCheckTechMain(TdZwCheckTechMain checkTechMain, List<TdZwCheckTechPsn> checkTechPsnList,List<TdZwCheckTechItem> techItemList) throws Exception {
        this.upsertEntity(checkTechMain);
        if(checkTechMain.getRid()!=null){
            //删除人员
            StringBuffer delSql=new StringBuffer();
            delSql.append(" delete from TD_ZW_CHECK_TECH_PSN where MAIN_ID=").append(checkTechMain.getRid());
            em.createNativeQuery(delSql.toString()).executeUpdate();
            //删除项目
            StringBuffer delSql1=new StringBuffer();
            delSql1.append(" delete from TD_ZW_CHECK_TECH_ITEM where MAIN_ID=").append(checkTechMain.getRid());
            em.createNativeQuery(delSql1.toString()).executeUpdate();
        }

        List<TdZwCheckTechPsn> checkTechPsnNewList=new ArrayList<>();
        if(!CollectionUtils.isEmpty(checkTechPsnList)){
            for (TdZwCheckTechPsn checkTechPsn : checkTechPsnList) {
                TdZwCheckTechPsn newCheckTechPsn=new TdZwCheckTechPsn();
                ObjectCopyUtil.copyPropertiesExclude(checkTechPsn,newCheckTechPsn,new String[]{"rid"});
                checkTechPsnNewList.add(newCheckTechPsn);
            }
        }
        this.saveBatchObjs(checkTechPsnNewList);
        List<TdZwCheckTechItem> techItemNewList=new ArrayList<>();
        if(!CollectionUtils.isEmpty(techItemList)){
            for (TdZwCheckTechItem techItem : techItemList) {
                TdZwCheckTechItem newTechItem=new TdZwCheckTechItem();
                ObjectCopyUtil.copyPropertiesExclude(techItem,newTechItem,new String[]{"rid"});
                techItemNewList.add(newTechItem);
            }
        }
        this.saveBatchObjs(techItemNewList);
        return checkTechMain.getRid();
    }

    /**
     * <p>方法描述：findTechPsnByMainId</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-22
     **/
    public List<TdZwCheckTechPsn> findTechPsnByMainId(Integer rid) {
        StringBuffer hql=new StringBuffer();
        hql.append(" select T from  TdZwCheckTechPsn T ");
        hql.append(" left join T.fkByPsnTypeId T1 ");
        hql.append(" where T.fkByMainId.rid=").append(rid);
        hql.append(" order by T1.num,T.psnName ");
        return this.findByHql(hql.toString(),TdZwCheckTechPsn.class);
    }

    /**
     * <p>方法描述：findTechItemByMainId</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-22
     **/
    public List<TdZwCheckTechItem> findTechItemByMainId(Integer rid) {
        StringBuffer hql=new StringBuffer();
        hql.append(" select T from  TdZwCheckTechItem T ");
        hql.append(" left join T.fkByItemId T1 ");
        hql.append(" where T.fkByMainId.rid=").append(rid);
        hql.append(" order by T1.num ");
        return this.findByHql(hql.toString(),TdZwCheckTechItem.class);
    }


    /**
     * <p>方法描述：撤销功能</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-22
     **/
    public void cancelStatue(Integer mainRid) {
        StringBuffer sql=new StringBuffer();
        sql.append(" update TD_ZW_CHECK_TECH_MAIN set STATE=0 where rid=").append(mainRid);
        em.createNativeQuery(sql.toString()).executeUpdate();
    }

    /**
     * <p>方法描述：根据企业rid获取对应资质的服务项目</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-23
     **/
    public List<Object[]> findCheckType(String checkType, Integer rid) {
        StringBuffer sql=new StringBuffer();
        sql.append("select T.rid,T2.CODE_NAME,T2.rid as itemRid ");
        if("1".equals(checkType)){
            sql.append(" from TD_ZW_TJORGINFO T ");
            sql.append(" left join TD_ZW_TJORGGITEMS T1 on T1.ORG_ID=T.RID ");
        }else if("2".equals(checkType)){
            sql.append(" from TD_ZW_DIAGORGINFO T ");
            sql.append(" left join TD_ZW_DIAGITEMS T1 on T1.ORG_ID=T.RID ");
        }else if("3".equals(checkType)){
            sql.append(" from TD_ZW_SRVORGINFO T ");
            sql.append(" left join TD_ZW_SRVORGITEMS T1 on T1.ORG_ID=T.RID ");
        }else if("4".equals(checkType)){
            sql.append(" from TD_ZW_OCCHETH_INFO T ");
            sql.append(" left join TD_ZW_OCCHETH_ITEMS T1 on T1.ORG_ID=T.RID ");
        }
        sql.append(" left join TS_SIMPLE_CODE T2 on T1.ITEM_CODE=T2.CODE_NO ");
        sql.append(" left JOIN TS_CODE_TYPE T3 ON T2.CODE_TYPE_ID = T3.RID ");
        sql.append(" where 1=1 ");
        if("1".equals(checkType)){
            sql.append(" and T3.CODE_TYPE_NAME='5018'");
        }else if("2".equals(checkType)){
            sql.append(" and T3.CODE_TYPE_NAME='5020'");
        }else if("3".equals(checkType)){
            sql.append(" and T3.CODE_TYPE_NAME='5019'");
        }else if("4".equals(checkType)){
            sql.append(" and T3.CODE_TYPE_NAME='5320'");
        }
        sql.append("  and T.ORG_ID=").append(rid);
        sql.append(" order by T2.NUM ");
        return em.createNativeQuery(sql.toString()).getResultList();
    }
}
