package com.chis.modules.heth.zkcheck.web;

import cn.hutool.core.util.URLUtil;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.javabean.FileItemBean;
import com.chis.modules.heth.zkcheck.entity.TdZkExtractBhk;
import com.chis.modules.heth.zkcheck.service.CheckExpertListService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.commons.fileupload.FileItem;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

@ManagedBean(name = "bhkReportUploadBean")
@ViewScoped
public class BhkReportUploadBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;
    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final CheckExpertListService expertListService = SpringContextHolder.getBean(CheckExpertListService.class);
    /**
     * 主表RID
     */
    private Integer rid;
    /**
     * 主表
     */
    private TdZkExtractBhk extractBhk;
    /**
     * 是否详情页
     */
    private Boolean ifView;
    /**
     * 当前抽取类型<pre>1: 胸片</pre><pre>2: 电测听</pre>
     */
    private Integer extractType;
    /**
     * 查询条件: 抽取开始日期
     */
    private Date searchDrawingSDate;
    /**
     * 查询条件: 抽取结束日期
     */
    private Date searchDrawingEDate;
    /**
     * 查询条件: 姓名
     */
    private String searchPersonName;
    /**
     * 查询条件: 体检编号
     */
    private String searchBhkNo;
    /**
     * 查询条件: 抽取类别
     */
    private String[] searchDrawingType;
    private List<TsSimpleCode> drawingTypeList;
    /**
     * 查询条件: 状态
     */
    private String[] searchState;
    /**
     * 生成附件操作类型<pre>1: 体检报告附件</pre><pre>2: 胸片附件</pre><pre>3: 听力图谱附件</pre>
     */
    private Integer uploadOptType;
    /**
     * 是否显示体检报告附件下载
     */
    private Boolean ifShowBhkRptFileDownload;
    /**
     * 体检报告附件名称
     */
    private String bhkRptFileName;
    /**
     * 是否显示胸片/听力图谱附件下载
     */
    private Boolean ifShowChestDownload;
    /**
     * 胸片/听力图谱附件名称
     */
    private String chestFileName;
    /**
     * 文件下载
     */
    protected StreamedContent streamedContent;
    /**
     * 下载路径
     */
    private String filePath;
    /**
     * 下载文件名
     */
    private String fileName;

    public BhkReportUploadBean() {
        init();
    }

    public void init() {
        this.ifSQL = true;
        this.searchDrawingEDate = new Date();
        this.searchDrawingSDate = DateUtils.getYearFirstDay(this.searchDrawingEDate);
        this.searchState = new String[]{"0", "3"};
        simpleCodeInit();
        searchAction();
    }

    private void simpleCodeInit() {
        // 5621 抽取类别
        this.drawingTypeList = this.commService.findSimpleCodeListOrderByNumNo("5621");
    }

    @Override
    public String[] buildHqls() {
        String sql = "SELECT " +
                "   EB.RID AS P0, " +
                "   SC1.CODE_NAME AS P1, " +
                "   EB.PERSON_NAME AS P2, " +
                "   EB.BHK_CODE AS P3, " +
                "   EB.PRT_DATE AS P4, " +
                "   SC2.CODE_NAME AS P5, " +
                "   EB.TCHBADRSNTIM AS P6, " +
                "   C.CRPT_NAME AS P7, " +
                "   EB.EXTRACT_DATE AS P8, " +
                "   EB.BACK_RSN AS P9, " +
                "   '' AS P10, " +
                "   NVL(EB.STATE, 0) AS P11 ";
        String baseSql = buildQueryCriteria(this.paramMap);
        sql += baseSql + " ORDER BY TO_CHAR(EB.EXTRACT_DATE, 'yyyy-MM-dd') DESC, SC1.NUM, SC1.CODE_NO, EB.BHK_CODE ";
        String countSql = "SELECT COUNT(1) " + baseSql;
        return new String[]{sql, countSql};
    }

    private String buildQueryCriteria(Map<String, Object> paramMap) {
        StringBuilder baseSql = new StringBuilder();
        baseSql.append(" FROM TD_ZK_EXTRACT_BHK EB ")
                .append("   LEFT JOIN TS_SIMPLE_CODE SC1 ON EB.EXTRACT_TYPE_ID = SC1.RID ")
                .append("   LEFT JOIN TS_SIMPLE_CODE SC2 ON EB.BHK_RST_ID = SC2.RID ")
                .append("   LEFT JOIN TB_TJ_CRPT C ON EB.ENTRUST_CRPT_ID = C.RID ")
                .append("   LEFT JOIN TB_TJ_SRVORG S ON EB.ORG_ID = S.RID ")
                .append(" WHERE S.REG_ORGID = :unitId ");
        paramMap.put("unitId", Global.getUser().getTsUnit().getRid());
        // 抽取开始日期
        if (ObjectUtil.isNotEmpty(this.searchDrawingSDate)) {
            baseSql.append(" AND EB.EXTRACT_DATE >= TO_DATE(:searchDrawingSDate, 'yyyy-MM-dd') ");
            paramMap.put("searchDrawingSDate", DateUtils.formatDate(this.searchDrawingSDate));
        }
        // 抽取结束日期
        if (ObjectUtil.isNotEmpty(this.searchDrawingEDate)) {
            baseSql.append(" AND EB.EXTRACT_DATE <= TO_DATE(:searchDrawingEDate, 'yyyy-MM-dd HH24:MI:SS') ");
            paramMap.put("searchDrawingEDate", DateUtils.formatDate(this.searchDrawingEDate) + " 23:59:59");
        }
        // 姓名
        if (ObjectUtil.isNotEmpty(this.searchPersonName)) {
            baseSql.append(" AND EB.PERSON_NAME LIKE :searchPersonName ESCAPE '\\\' ");
            paramMap.put("searchPersonName", "%" + StringUtils.convertBFH(this.searchPersonName.trim()) + "%");
        }
        // 体检编号
        if (ObjectUtil.isNotEmpty(this.searchBhkNo)) {
            baseSql.append(" AND EB.BHK_CODE LIKE :searchBhkNo ESCAPE '\\\' ");
            paramMap.put("searchBhkNo", "%" + StringUtils.convertBFH(this.searchBhkNo.trim()) + "%");
        }
        // 抽取类别
        if (ObjectUtil.isNotEmpty(this.searchDrawingType)) {
            baseSql.append(" AND EB.EXTRACT_TYPE_ID IN (:searchDrawingType) ");
            paramMap.put("searchDrawingType", Arrays.asList(this.searchDrawingType));
        }
        // 状态
        if (ObjectUtil.isNotEmpty(this.searchState)) {
            Set<String> stateSet = new HashSet<>();
            for (String s : this.searchState) {
                stateSet.add(s);
                if ("2".equals(s)) {
                    stateSet.add("4");
                    stateSet.add("5");
                    stateSet.add("6");
                    stateSet.add("7");
                }
            }
            baseSql.append(" AND EB.STATE IN (:states) ");
            paramMap.put("states", stateSet);
        }
        return baseSql.toString();
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<Object[]> result = CollectionUtil.castList(Object[].class, list);
        for (Object[] obj : result) {
            //状态
            String stateOri = StringUtils.objectToString(obj[11]);
            switch (stateOri) {
                case "0":
                    obj[10] = "待提交";
                    break;
                case "1":
                    obj[10] = "待审核";
                    break;
                case "3":
                    obj[10] = "审核退回";
                    break;
                case "2":
                case "4":
                case "5":
                case "6":
                case "7":
                    obj[10] = "审核通过";
                    break;
                default:
                    obj[10] = "";
                    break;
            }
        }
    }

    @Override
    public void addInit() {
    }

    @Override
    public void modInit() {
        this.extractBhk = this.commService.find(TdZkExtractBhk.class, this.rid);
        this.extractBhk.setZoneName(ZoneUtil.removeProvByFullName(this.extractBhk.getFkByZoneId().getFullName()));
        if (new Integer(0).equals(this.extractBhk.getState()) || new Integer(3).equals(this.extractBhk.getState())) {
            this.ifView = Boolean.FALSE;
        } else {
            this.ifView = Boolean.TRUE;
        }
        this.ifShowBhkRptFileDownload = null;
        this.ifShowChestDownload = null;
        if (this.extractBhk.getFkByExtractTypeId() == null
                || this.extractBhk.getFkByExtractTypeId().getExtendS1() == null) {
            this.extractType = null;
            return;
        }
        pakFileInfo(1, this.extractBhk.getBhkRptPath());
        switch (this.extractBhk.getFkByExtractTypeId().getExtendS1()) {
            case "1":
                this.extractType = 1;
                pakFileInfo(2, this.extractBhk.getChestPath());
                break;
            case "2":
                this.extractType = 2;
                pakFileInfo(3, this.extractBhk.getChestPath());
                break;
            default:
                this.extractType = null;
                break;
        }
    }

    /**
     * 上传附件
     *
     * @param event 附件
     */
    public void fileUpload(FileUploadEvent event) {
        String filePath;
        UploadedFile file = event.getFile();
        FileItem fileItem = new FileItemBean(file);
        String contentType = file.getContentType().toLowerCase();
        try {
            // 体检报告附件、听力图谱附件格式: 图片、PDF、RAR、ZIP
            String veryFileIndex = "7";
            // 胸片附件格式: RAR、ZIP、DCM
            if (new Integer(2).equals(this.uploadOptType)) {
                veryFileIndex = "8";
            }
            String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, file.getFileName(), veryFileIndex);
            if (StringUtils.isNotBlank(errorMsg)) {
                JsfUtil.addErrorMessage(errorMsg);
                return;
            }
            int dotIndex = file.getFileName().lastIndexOf(".");
            //文件名不包含.报错处理
            if (dotIndex < 0) {
                return;
            }
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            filePath = "heth/zkcheckjs/bhkReportUpload/" + uuid + file.getFileName().substring(dotIndex);

            FileUtils.uploadFile(filePath, fileItem);
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage(PropertyUtils.getValue("上传失败"));
            return;
        }
        if (new Integer(1).equals(this.uploadOptType)) {
            this.extractBhk.setBhkRptPath(filePath);
        } else {
            this.extractBhk.setChestPath(filePath);
        }
        pakFileInfo(this.uploadOptType, filePath);
        RequestContext.getCurrentInstance().execute("PF('FileDialog').hide();");
        if (new Integer(1).equals(this.uploadOptType)) {
            RequestContext.getCurrentInstance().update("tabView:editForm:bhkRptFilePanel");
        } else {
            RequestContext.getCurrentInstance().update("tabView:editForm:chestFilePanel");
        }
    }

    /**
     * 封装附件信息
     *
     * @param type 附件类型<pre>1: 体检报告附件</pre><pre>2: 胸片附件</pre><pre>3: 听力图谱附件</pre>
     */
    private void pakFileInfo(int type, String path) {
        if (ObjectUtil.isEmpty(path)) {
            return;
        }
        boolean ifShowDownload;
        String suffix = "";
        if (path.lastIndexOf('.') + 1 == 0 || path.lastIndexOf('.') + 1 == path.length()) {
            ifShowDownload = true;
        } else {
            suffix = path.substring(path.lastIndexOf('.') + 1).toLowerCase();
            ifShowDownload = "zip".equals(suffix) || "rar".equals(suffix) || "dcm".equals(suffix);
            suffix = "." + suffix;
        }
        if (type == 1) {
            this.bhkRptFileName = this.extractBhk.getFkByOrgId().getUnitName() + "_" +
                    this.extractBhk.getPersonName() + "_" + "体检报告附件" + suffix;
            this.ifShowBhkRptFileDownload = ifShowDownload;
        } else {
            String typeName = type == 2 ? "胸片附件" : "听力图谱附件";
            this.chestFileName = this.extractBhk.getFkByOrgId().getUnitName() + "_" +
                    this.extractBhk.getPersonName() + "_" + typeName + suffix;
            this.ifShowChestDownload = ifShowDownload;
        }
    }

    /**
     * 下载
     */
    public StreamedContent getStreamedContent() {
        try {
            String xnPath = JsfUtil.getAbsolutePath();
            String path = "";
            if (this.filePath.indexOf(xnPath) != -1) {
                path = this.filePath;
            } else {
                path = xnPath + this.filePath;
            }
            InputStream stream = new FileInputStream(path);
            this.streamedContent = new DefaultStreamedContent(stream, "application/pdf", URLUtil.encode(fileName, StandardCharsets.UTF_8));
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("系统找不到指定的文件！");
            return null;
        }
        return streamedContent;
    }

    public void setStreamedContent(StreamedContent streamedContent) {
        this.streamedContent = streamedContent;
    }

    @Override
    public void saveAction() {
    }

    /**
     * 提交前验证
     */
    public void preSubmitAction() {
        if (verify()) {
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show();");
    }

    /**
     * 提交
     */
    public void submitAction() {
        try {
            // 更新状态为“待审核”
            this.extractBhk.setState(1);
            this.extractBhk.setBackRsn("");
            this.expertListService.updateTdZkExtractBhk(this.extractBhk);
            JsfUtil.addSuccessMessage("提交成功！");
            this.searchAction();
            this.backAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
     * 验证
     *
     * @return 验证是否失败 <pre>true: 验证失败</pre><pre>false: 验证成功</pre>
     */
    public boolean verify() {
        boolean isFlag = false;
        if (ObjectUtil.isEmpty(this.extractType)) {
            return false;
        }
        if (ObjectUtil.isEmpty(this.extractBhk.getChestNo())) {
            String errorMsg = new Integer(1).equals(this.extractType) ? "胸片号" : "听力图谱编号";
            JsfUtil.addErrorMessage(errorMsg + "不能为空！");
            isFlag = true;
        }
        if (ObjectUtil.isEmpty(this.extractBhk.getBhkRptPath())) {
            JsfUtil.addErrorMessage("请上传体检报告附件！");
            isFlag = true;
        }
        if (ObjectUtil.isEmpty(this.extractBhk.getChestPath())) {
            String errorMsg = new Integer(1).equals(this.extractType) ? "胸片附件" : "听力图谱附件";
            JsfUtil.addErrorMessage("请上传" + errorMsg + "！");
            isFlag = true;
        }
        return isFlag;
    }


    @Override
    public void viewInit() {
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZkExtractBhk getExtractBhk() {
        return extractBhk;
    }

    public void setExtractBhk(TdZkExtractBhk extractBhk) {
        this.extractBhk = extractBhk;
    }

    public Boolean getIfView() {
        return ifView;
    }

    public void setIfView(Boolean ifView) {
        this.ifView = ifView;
    }

    public Integer getExtractType() {
        return extractType;
    }

    public void setExtractType(Integer extractType) {
        this.extractType = extractType;
    }

    public Date getSearchDrawingSDate() {
        return searchDrawingSDate;
    }

    public void setSearchDrawingSDate(Date searchDrawingSDate) {
        this.searchDrawingSDate = searchDrawingSDate;
    }

    public Date getSearchDrawingEDate() {
        return searchDrawingEDate;
    }

    public void setSearchDrawingEDate(Date searchDrawingEDate) {
        this.searchDrawingEDate = searchDrawingEDate;
    }

    public String getSearchPersonName() {
        return searchPersonName;
    }

    public void setSearchPersonName(String searchPersonName) {
        this.searchPersonName = searchPersonName;
    }

    public String getSearchBhkNo() {
        return searchBhkNo;
    }

    public void setSearchBhkNo(String searchBhkNo) {
        this.searchBhkNo = searchBhkNo;
    }

    public String[] getSearchDrawingType() {
        return searchDrawingType;
    }

    public void setSearchDrawingType(String[] searchDrawingType) {
        this.searchDrawingType = searchDrawingType;
    }

    public List<TsSimpleCode> getDrawingTypeList() {
        return drawingTypeList;
    }

    public void setDrawingTypeList(List<TsSimpleCode> drawingTypeList) {
        this.drawingTypeList = drawingTypeList;
    }

    public String[] getSearchState() {
        return searchState;
    }

    public void setSearchState(String[] searchState) {
        this.searchState = searchState;
    }

    public Integer getUploadOptType() {
        return uploadOptType;
    }

    public void setUploadOptType(Integer uploadOptType) {
        this.uploadOptType = uploadOptType;
    }

    public Boolean getIfShowBhkRptFileDownload() {
        return ifShowBhkRptFileDownload;
    }

    public void setIfShowBhkRptFileDownload(Boolean ifShowBhkRptFileDownload) {
        this.ifShowBhkRptFileDownload = ifShowBhkRptFileDownload;
    }

    public String getBhkRptFileName() {
        return bhkRptFileName;
    }

    public void setBhkRptFileName(String bhkRptFileName) {
        this.bhkRptFileName = bhkRptFileName;
    }

    public Boolean getIfShowChestDownload() {
        return ifShowChestDownload;
    }

    public void setIfShowChestDownload(Boolean ifShowChestDownload) {
        this.ifShowChestDownload = ifShowChestDownload;
    }

    public String getChestFileName() {
        return chestFileName;
    }

    public void setChestFileName(String chestFileName) {
        this.chestFileName = chestFileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}
