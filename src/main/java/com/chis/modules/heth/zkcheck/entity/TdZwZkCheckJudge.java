package com.chis.modules.heth.zkcheck.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2025-8-11
 */
@Entity
@Table(name = "TD_ZW_ZK_CHECK_JUDGE")
@SequenceGenerator(name = "TdZwZkCheckJudge", sequenceName = "TD_ZW_ZK_CHECK_JUDGE_SEQ", allocationSize = 1)
public class TdZwZkCheckJudge implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwZkCheckMain fkByMainId;
	private Integer ifUpdateTotalScore;
	private BigDecimal revisedTotalScore;
	private String modReasons;
	private BigDecimal totalScore;
	private Integer checkRst;
	private Integer state;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwZkCheckJudge() {
	}

	public TdZwZkCheckJudge(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwZkCheckJudge")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwZkCheckMain getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwZkCheckMain fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "IF_UPDATE_TOTAL_SCORE")	
	public Integer getIfUpdateTotalScore() {
		return ifUpdateTotalScore;
	}

	public void setIfUpdateTotalScore(Integer ifUpdateTotalScore) {
		this.ifUpdateTotalScore = ifUpdateTotalScore;
	}	
			
	@Column(name = "REVISED_TOTAL_SCORE")	
	public BigDecimal getRevisedTotalScore() {
		return revisedTotalScore;
	}

	public void setRevisedTotalScore(BigDecimal revisedTotalScore) {
		this.revisedTotalScore = revisedTotalScore;
	}	
			
	@Column(name = "MOD_REASONS")	
	public String getModReasons() {
		return modReasons;
	}

	public void setModReasons(String modReasons) {
		this.modReasons = modReasons;
	}	
			
	@Column(name = "TOTAL_SCORE")	
	public BigDecimal getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(BigDecimal totalScore) {
		this.totalScore = totalScore;
	}	
			
	@Column(name = "CHECK_RST")	
	public Integer getCheckRst() {
		return checkRst;
	}

	public void setCheckRst(Integer checkRst) {
		this.checkRst = checkRst;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}