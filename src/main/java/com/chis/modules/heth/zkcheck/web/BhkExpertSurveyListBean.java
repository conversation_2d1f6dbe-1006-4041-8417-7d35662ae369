package com.chis.modules.heth.zkcheck.web;

import cn.hutool.core.convert.Convert;
import com.chis.common.utils.*;
import com.chis.modules.heth.zkcheck.entity.TdZkExtractBhk;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * <p>类描述：专家判定</p>
 *
 * @ClassAuthor hsj 2024-07-04 9:00
 */
@ManagedBean(name = "bhkExpertSurveyListBean")
@ViewScoped
public class BhkExpertSurveyListBean extends BaseCheckExtract {

    /**
     * key：码表rid,value:码表对象
     */
    private Map<Integer, TsSimpleCode> simpleCodeMap;
    /**
     * key:码表extend2 value:码表对象
     */
    private Map<Integer, TsSimpleCode> bhkRstExtend2Map;
    /**
     * 专家阅片结论
     */
    private List<TsSimpleCode> expertChestList;
    /**
     * 专家判定结论
     */
    private List<TsSimpleCode> expertHearRstList;
    /**
     * 专家建议体检结论
     */
    private List<TsSimpleCode> expertRstList;
    /**
     * 胸片质量
     */
    private List<TsSimpleCode> chestLevelList;
    /**
     * 体检结论
     */
    private List<TsSimpleCode> bhkRstList;

    /**专家建议体检结论  是否可修改*/
    private boolean ifReadOnly = false;


    public BhkExpertSurveyListBean() {
        this.ifSQL = true;
        this.initZone();
        this.initState();
        this.initSimple();
        this.searchAction();
    }

    /**
     * <p>方法描述：码表初始化</p>
     *
     * @MethodAuthor hsj 2024-07-04 13:35
     */
    private void initSimple() {
        this.simpleCodeMap = new HashMap<>();
        this.expertRstList = new LinkedList<>();
        this.expertChestList = this.commService.findNumSimpleCodesByTypeId("5598");
        this.dealSimpleMap(this.expertChestList);
        this.expertHearRstList = this.commService.findNumSimpleCodesByTypeId("5622");
        this.dealSimpleMap(this.expertHearRstList);
        this.bhkRstList = this.commService.findLevelSimpleCodesByTypeId("5005");
        this.dealSimpleMap(this.bhkRstList);
        this.dealRstSimpleMap();
        this.chestLevelList = this.commService.findLevelSimpleCodesByTypeId("5599");
        this.dealSimpleMap(this.chestLevelList);
    }

    /**
     * <p>方法描述：处理体检结论
     * 排除扩展字段2为空的和扩展字段2=2（复查）的</p>
     *
     * @MethodAuthor hsj 2024-07-04 14:01
     */
    private void dealRstSimpleMap() {
        this.bhkRstExtend2Map = new HashMap<>();
        if (CollectionUtils.isEmpty(this.bhkRstList)) {
            return;
        }
        List<TsSimpleCode> list = new LinkedList<>();
        for (TsSimpleCode simpleCode : this.bhkRstList) {
            Integer ex2 = simpleCode.getExtendS2();
            if (ObjectUtil.isNull(ex2)) {
                continue;
            }
            if (new Integer(2).equals(ex2)) {
                continue;
            }
            list.add(simpleCode);
            this.bhkRstExtend2Map.put(ex2, simpleCode);
        }
        this.bhkRstList.clear();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        this.bhkRstList.addAll(list);
    }

    /**
     * <p>方法描述：码表map封装处理</p>
     *
     * @MethodAuthor hsj 2024-07-04 13:37
     */
    private void dealSimpleMap(List<TsSimpleCode> simpleCodes) {
        if (CollectionUtils.isEmpty(simpleCodes)) {
            return;
        }
        for (TsSimpleCode code : simpleCodes) {
            this.simpleCodeMap.put(code.getRid(), code);
        }
    }

    /**
     * <p>方法描述：查询条件-状态初始化</p>
     *
     * @MethodAuthor hsj 2024-07-04 9:11
     */
    private void initState() {
        //待判定/判定完成/省级复核/复核完成
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem("4", "待判定"));
        this.stateList.add(new SelectItem("5", "判定完成"));
        this.stateList.add(new SelectItem("6", "省级复核"));
        this.stateList.add(new SelectItem("7", "复核完成"));
        this.states = new String[]{"4"};
    }

    /**
     * <p>方法描述：地区初始化
     * 登录人所在的省份
     * </p>
     *
     * @MethodAuthor hsj 2024-07-04 9:10
     */
    private void initZone() {
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb().substring(0, 2), true, "", "");
        this.searchZoneGb = this.zoneList.get(0).getZoneGb();
        this.searchZoneName = this.zoneList.get(0).getZoneName();
    }


    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {
        super.modInit();
        this.initNull(this.extractBhk);
        String state = Convert.toStr(this.extractBhk.getState());
        if ("4".equals(state)) {
            this.changeExpertChest(false);
        }
    }

    /**
     * <p>方法描述：为空的处理</p>
     *
     * @MethodAuthor hsj 2024-07-04 14:32
     */
    private void initNull(TdZkExtractBhk extractBhk) {
        if (ObjectUtil.isNull(extractBhk.getFkByExpertChestId())) {
            extractBhk.setFkByExpertChestId(new TsSimpleCode());
        }
        if (ObjectUtil.isNull(extractBhk.getFkByExpertHearRstId())) {
            extractBhk.setFkByExpertHearRstId(new TsSimpleCode());
        }
        if (ObjectUtil.isNull(extractBhk.getFkByExpertRstId())) {
            extractBhk.setFkByExpertRstId(new TsSimpleCode());
        }
        if (ObjectUtil.isNull(extractBhk.getFkByChestLevelId())) {
            extractBhk.setFkByChestLevelId(new TsSimpleCode());
        }
        if (StringUtils.isBlank(extractBhk.getCheckUnitName())) {
            extractBhk.setCheckUnitName(Global.getUser().getTsUnit().getUnitname());
        }
        if (StringUtils.isBlank(extractBhk.getCheckPsn())) {
            extractBhk.setCheckPsn(Global.getUser().getUsername());
        }
        if (ObjectUtil.isNull(extractBhk.getCheckDate())) {
            extractBhk.setCheckDate(new Date());
        }
        if (StringUtils.isBlank(extractBhk.getCheckLinktel())) {
            extractBhk.setCheckLinktel(Global.getUser().getMbNum());
        }
    }

    @Override
    public void saveAction() {

    }

    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();
        sql.append(" FROM TD_ZK_BHK_EXPERT T ");
        sql.append(" LEFT JOIN TD_ZK_EXTRACT_BHK T1 ON T1.RID = T.MAIN_ID ");
        sql.append(" LEFT JOIN TS_ZONE T2 ON  T2.RID =T1.ZONE_ID ");
        sql.append(" LEFT JOIN  TB_TJ_SRVORG T3 ON  T3.RID =T1.ORG_ID ");
        sql.append(" LEFT JOIN  TS_SIMPLE_CODE T4 ON  T4.RID =T1.EXTRACT_TYPE_ID ");
        sql.append(" LEFT JOIN  TS_SIMPLE_CODE T5 ON  T5.RID =T1.BHK_RST_ID ");
        sql.append(" LEFT JOIN  TS_SIMPLE_CODE T6 ON  T6.RID =T1.EXPERT_CHEST_ID ");
        sql.append(" LEFT JOIN  TS_SIMPLE_CODE T7 ON  T7.RID =T1.EXPERT_HEAR_RST_ID ");
        sql.append(" LEFT JOIN  TS_SIMPLE_CODE T8 ON  T8.RID =T1.EXPERT_RST_ID ");
        sql.append(" WHERE T.USER_ID  =  ").append(Global.getUser().getRid());
        // 地区
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            sql.append(" AND T2.ZONE_GB LIKE :zoneGb escape '\\\'");
            this.paramMap.put("zoneGb", ZoneUtil.zoneSelect(this.searchZoneGb.trim()) + "%");
        }
        // 体检机构
        if (StringUtils.isNotBlank(this.searchUnitId)) {
            sql.append(" AND T1.ORG_ID IN (:orgId)");
            this.paramMap.put("orgId", StringUtils.string2list(this.searchUnitId, ","));
        }
        // 抽取类别
        if (ObjectUtil.isNotEmpty(this.searchBhkType)) {
            sql.append(" AND T1.EXTRACT_TYPE_ID in(:extractTypeId)");
            this.paramMap.put("extractTypeId", Arrays.asList(this.searchBhkType));
        }
        // 抽取日期
        if (null != this.searchSDate) {
            sql.append(" AND T1.EXTRACT_DATE >= TO_DATE(:searchSDate,'yyyy-MM-dd')");
            this.paramMap.put("searchSDate", DateUtils.formatDate(searchSDate, "yyyy-MM-dd"));
        }
        if (null != this.searchEDate) {
            sql.append(" AND T1.EXTRACT_DATE <= TO_DATE(:searchEDate,'yyyy-MM-dd HH24:mi:ss')");
            this.paramMap.put("searchEDate", DateUtils.formatDate(searchEDate, "yyyy-MM-dd") + " 23:59:59");
        }
        // 职业健康检查结论
        if (StringUtils.isNotBlank(this.searchSelBhkrstIds)) {
            sql.append(" AND T1.BHK_RST_ID IN (:bhkRstId)");
            this.paramMap.put("bhkRstId", StringUtils.string2list(this.searchSelBhkrstIds, ","));
        }
        if (null == this.states || this.states.length == 0) {
            sql.append(" AND T1.STATE IN (4,5,6,7)");
        } else {
            sql.append(" AND T1.STATE IN (:state)");
            this.paramMap.put("state", Arrays.asList(this.states));
        }


        StringBuffer searchSql = new StringBuffer();
        searchSql.append(" SELECT T1.RID ,  CASE WHEN T2.ZONE_TYPE > 2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME, '_') + 1) ELSE T2.FULL_NAME END ZONE_NAME ");
        searchSql.append("  ,T3.UNIT_NAME ,T4.CODE_NAME AS EXTRACT_TYPE_NAME ");
        searchSql.append(" ,T1.PERSON_NAME,T5.CODE_NAME AS BHK_RST_NAME ,T6.CODE_NAME AS EXPERT_CHEST_NAME ");
        searchSql.append(" ,T7.CODE_NAME AS EXPERT_HEAR_RST_NAME,T8.CODE_NAME AS EXPERT_RST_NAME,T1.EXTRACT_DATE ");
        searchSql.append(" ,T1.CHECK_DATE,T1.STATE,T4.EXTENDS1 ");
        searchSql.append(sql);
        searchSql.append(" ORDER BY TO_CHAR(T1.EXTRACT_DATE,'yyyy-MM-dd') DESC ,T2.ZONE_GB ,T3.UNIT_NAME,T4.NUM,T1.PERSON_NAME");
        String countSql = " SELECT COUNT(*) " + sql;
        return new String[]{searchSql.toString(), countSql};
    }

    /**
     * <p>方法描述：提交前验证</p>
     *
     * @MethodAuthor hsj 2024-07-04 10:41
     */
    public void beforeSubmitAction() {
        Integer state = this.bhkReportExtractService.findTdZkExtractBhkStateByRid(this.rid);
        if (ObjectUtil.isNull(state)) {
            JsfUtil.addErrorMessage("该记录不存在，请联系管理员！");
            return;
        }
        if (!new Integer(4).equals(state)) {
            JsfUtil.addErrorMessage("该记录状态不可提交，请联系管理员！");
            return;
        }
        boolean flag = false;
        String ex1 = this.extractBhk.getFkByExtractTypeId().getExtendS1();
        boolean ifFlag;
        if ("1".equals(ex1)) {
            ifFlag = ObjectUtil.isNull(this.extractBhk.getFkByExpertChestId()) || ObjectUtil.isNull(this.extractBhk.getFkByExpertChestId().getRid());
            if (ifFlag) {
                JsfUtil.addErrorMessage("请选择专家阅片结论！");
                flag = true;
            }
            ifFlag = ObjectUtil.isNull(this.extractBhk.getFkByChestLevelId()) || ObjectUtil.isNull(this.extractBhk.getFkByChestLevelId().getRid());
            if (ifFlag) {
                JsfUtil.addErrorMessage("请选择胸片质量！");
                flag = true;
            }
        } else if ("2".equals(ex1)) {
            ifFlag = ObjectUtil.isNull(this.extractBhk.getFkByExpertHearRstId()) || ObjectUtil.isNull(this.extractBhk.getFkByExpertHearRstId().getRid());
            if (ifFlag) {
                JsfUtil.addErrorMessage("请选择专家判定结论！");
                flag = true;
            }
        }
        ifFlag = !this.ifReadOnly && (ObjectUtil.isNull(this.extractBhk.getFkByExpertRstId()) || ObjectUtil.isNull(this.extractBhk.getFkByExpertRstId().getRid()));
        if (ifFlag) {
            JsfUtil.addErrorMessage("请选择专家建议体检结论！");
            flag = true;
        }
        if (StringUtils.isBlank(this.extractBhk.getCheckUnitName())) {
            JsfUtil.addErrorMessage("填报单位不能为空！");
            flag = true;
        }
        if (StringUtils.isBlank(this.extractBhk.getCheckPsn())) {
            JsfUtil.addErrorMessage("填报人不能为空！");
            flag = true;
        }
        if (this.extractBhk.getCheckDate() == null) {
            JsfUtil.addErrorMessage("填报日期不能为空！");
            flag = true;
        }
        ifFlag = ObjectUtil.isNotNull(this.extractBhk.getCheckDate()) && ObjectUtil.isNotNull(this.extractBhk.getExtractDate()) && DateUtils.isCompareDate(this.extractBhk.getExtractDate(),">",this.extractBhk.getCheckDate());
        if(ifFlag){
            JsfUtil.addErrorMessage("填报日期应大于等于抽取日期（"+DateUtils.formatDate(this.extractBhk.getExtractDate(),"yyyy-MM-dd")+"）！");
            flag = true;
        }
        if (StringUtils.isBlank(this.extractBhk.getCheckLinktel())) {
            JsfUtil.addErrorMessage("联系电话不能为空！");
            flag = true;
        }
        ifFlag = StringUtils.isNotBlank(this.extractBhk.getCheckLinktel()) && !StringUtils.vertyPhone(this.extractBhk.getCheckLinktel());
        if (ifFlag) {
            JsfUtil.addErrorMessage("联系电话格式不正确！");
            flag = true;
        }
        if (flag) {
            return;
        }
        //验证通过-弹出提示框
        RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show()");
    }

    /**
     * <p>方法描述：撤销前验证 5,6</p>
     *
     * @MethodAuthor hsj 2024-07-04 14:50
     */
    public void beforeRevocationAction() {
        Integer state = this.bhkReportExtractService.findTdZkExtractBhkStateByRid(this.rid);
        if (ObjectUtil.isNull(state)) {
            JsfUtil.addErrorMessage("该记录不存在，请联系管理员！");
            return;
        }
        if (!new Integer(5).equals(state) && !new Integer(6).equals(state)) {
            JsfUtil.addErrorMessage("该记录状态不可撤销，请联系管理员！");
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmRevocationDialog').show()");
    }

    /**
     * <p>方法描述： 专家阅片结论  change事件
     * flag 是否清空 专家建议体检结论的值</p>
     *
     * @MethodAuthor hsj 2024-07-04 12:00
     */
    public void changeExpertChest(boolean flag) {
        this.ifReadOnly=false;
        this.expertRstList.clear();
        if (flag) {
            this.extractBhk.setFkByExpertRstId(new TsSimpleCode());
        }
        String ex1 = this.extractBhk.getFkByExtractTypeId().getExtendS1();
        if(StringUtils.isBlank(ex1)){
            return;
        }
        switch (ex1) {
            case "1":
                dealRabat();
                break;
            case "2":
                dealHearing();
                break;
            default:
                break;
        }
        if (CollectionUtils.isEmpty(this.expertRstList)) {
            return;
        }
        //按照码表num code 排序;num可能为空的情况
        Collections.sort(this.expertRstList, new Comparator<TsSimpleCode>() {
            @Override
            public int compare(TsSimpleCode o1, TsSimpleCode o2) {
                if (ObjectUtil.isNotNull(o1.getNum()) && ObjectUtil.isNotNull(o2.getNum())) {
                    int i = o1.getNum().compareTo(o2.getNum());
                    if (i == 0) {
                        return o1.getCodeNo().compareTo(o2.getCodeNo());
                    }
                    return i;
                } else if (ObjectUtil.isNotNull(o1.getNum())) {
                    return -1;
                } else if (ObjectUtil.isNotNull(o2.getNum())) {
                    return 1;
                } else {
                    return o1.getCodeNo().compareTo(o2.getCodeNo());
                }
            }
        });
    }

    /**
     * <p>方法描述：电测听体检结论</p>
     *
     * @MethodAuthor hsj 2024-07-04 14:24
     */
    private void dealHearing() {
        boolean bool = this.extractBhk.getFkByExpertHearRstId() != null && this.extractBhk.getFkByExpertHearRstId().getRid() != null
                && this.simpleCodeMap.containsKey(this.extractBhk.getFkByExpertHearRstId().getRid())
                && StringUtils.isNotBlank(this.simpleCodeMap.get(this.extractBhk.getFkByExpertHearRstId().getRid()).getExtendS1());
        if (!bool) {
            return;
        }
        String ex1 = this.simpleCodeMap.get(this.extractBhk.getFkByExpertHearRstId().getRid()).getExtendS1();
        switch (ex1) {
            case "1":
                addEpertRst("1,3");
                break;
            case "2":
                addEpertRst("3,4,5");
                break;
            default:
                addEpertRst(null);
                break;
        }
    }

    /**
     * <p>方法描述：胸片的体检结论</p>
     *
     * @MethodAuthor hsj 2024-07-04 14:13
     */
    private void dealRabat() {
        boolean bool = this.extractBhk.getFkByExpertChestId() != null && this.extractBhk.getFkByExpertChestId().getRid() != null
                && this.simpleCodeMap.containsKey(this.extractBhk.getFkByExpertChestId().getRid())
                && StringUtils.isNotBlank(this.simpleCodeMap.get(this.extractBhk.getFkByExpertChestId().getRid()).getExtendS1());
        if (!bool) {
            return;
        }
        String ex1 = this.simpleCodeMap.get(this.extractBhk.getFkByExpertChestId().getRid()).getExtendS1();
        switch (ex1) {
            case "1":
                addEpertRst("1,3");
                break;
            case "2":
                addEpertRst("5");
                break;
            case "3":
                addEpertRst("3,4");
                break;
            default:
                addEpertRst(null);
                this.ifReadOnly=true;
                break;
        }
    }

    /**
     * <p>方法描述：体检结论内容填充</p>
     *
     * @MethodAuthor hsj 2024-07-04 14:30
     */
    private void addEpertRst(String number) {
        if (StringUtils.isBlank(number)) {
            this.expertRstList.addAll(this.bhkRstList);
            return;
        }
        List<String> list = StringUtils.string2list(number, ",");
        for (String str : list) {
            if (!this.bhkRstExtend2Map.containsKey(Convert.toInt(str))) {
                continue;
            }
            this.expertRstList.add(this.bhkRstExtend2Map.get(Convert.toInt(str)));
        }
    }

    /**
     * <p>方法描述：提交事件</p>
     *
     * @MethodAuthor hsj 2024-07-04 14:53
     */
    public void submitAction() {
        try {
            //取值前判空
            Integer ex2 = this.extractBhk.getFkByExpertRstId()!=null && this.extractBhk.getFkByExpertRstId().getRid()!=null &&
                    this.simpleCodeMap.containsKey(this.extractBhk.getFkByExpertRstId().getRid())?this.simpleCodeMap.get(this.extractBhk.getFkByExpertRstId().getRid()).getExtendS2():null;
            if (new Integer(5).equals(ex2)) {
                this.extractBhk.setState(6);
            } else {
                this.extractBhk.setState(5);
            }
            //专家判定结论 为胸片质量不合格时，清空专家建议体检结论的值
            this.extractBhk.setFkByExpertRstId(this.ifReadOnly?null:this.extractBhk.getFkByExpertRstId());
            this.bhkReportExtractService.updateTdZkExtractBhk(this.extractBhk);
            JsfUtil.addSuccessMessage("提交成功！");
            modInitAction();
        } catch (Exception e) {
            e.printStackTrace();
            this.extractBhk.setState(4);
            this.initNull(this.extractBhk);
            JsfUtil.addErrorMessage("提交失败！");
        }
        this.searchAction();
    }

    /**
     * <p>方法描述：撤销事件</p>
     *
     * @MethodAuthor hsj 2024-07-04 14:54
     */
    public void revocationAction() {
        try {
            this.bhkReportExtractService.updateTdZkExtractBhkStateByRid(this.rid, 4);
            JsfUtil.addSuccessMessage("撤销成功！");
            modInitAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
        this.searchAction();
    }


    public Map<Integer, TsSimpleCode> getSimpleCodeMap() {
        return simpleCodeMap;
    }

    public void setSimpleCodeMap(Map<Integer, TsSimpleCode> simpleCodeMap) {
        this.simpleCodeMap = simpleCodeMap;
    }

    public Map<Integer, TsSimpleCode> getBhkRstExtend2Map() {
        return bhkRstExtend2Map;
    }

    public void setBhkRstExtend2Map(Map<Integer, TsSimpleCode> bhkRstExtend2Map) {
        this.bhkRstExtend2Map = bhkRstExtend2Map;
    }

    public List<TsSimpleCode> getExpertChestList() {
        return expertChestList;
    }

    public void setExpertChestList(List<TsSimpleCode> expertChestList) {
        this.expertChestList = expertChestList;
    }

    public List<TsSimpleCode> getExpertHearRstList() {
        return expertHearRstList;
    }

    public void setExpertHearRstList(List<TsSimpleCode> expertHearRstList) {
        this.expertHearRstList = expertHearRstList;
    }

    public List<TsSimpleCode> getExpertRstList() {
        return expertRstList;
    }

    public void setExpertRstList(List<TsSimpleCode> expertRstList) {
        this.expertRstList = expertRstList;
    }

    public List<TsSimpleCode> getChestLevelList() {
        return chestLevelList;
    }

    public void setChestLevelList(List<TsSimpleCode> chestLevelList) {
        this.chestLevelList = chestLevelList;
    }

    public List<TsSimpleCode> getBhkRstList() {
        return bhkRstList;
    }

    public void setBhkRstList(List<TsSimpleCode> bhkRstList) {
        this.bhkRstList = bhkRstList;
    }

    public boolean getIfReadOnly() {
        return ifReadOnly;
    }
    public void setIfReadOnly(boolean ifReadOnly) {
        this.ifReadOnly = ifReadOnly;
    }
}
