package com.chis.modules.heth.zkcheck.web;

import cn.hutool.core.convert.Convert;
import com.chis.common.utils.*;
import com.chis.modules.heth.zkcheck.entity.*;
import com.chis.modules.heth.zkcheck.service.TbZwZkBadrsnStandService;
import com.chis.modules.heth.zkcheck.service.TbZwZkRstRuleService;
import com.chis.modules.heth.zkcheck.service.TdZwZkCheckMainService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.enumn.SystemMessageEnum;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.javabean.FilePojoNew;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.utils.WordReportGenerateUtil;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <p>类描述：质控考核基类</p>
 *
 * @ClassAuthor qrr, 2021年6月26日, AbstractTdZwZkCheckMainListBean
 */
public abstract class AbstractTdZwZkCheckMainListBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;
    protected CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    protected SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    protected TdZwZkCheckMainService checkMainService = SpringContextHolder.getBean(TdZwZkCheckMainService.class);
    protected TbZwZkBadrsnStandService standService = SpringContextHolder.getBean(TbZwZkBadrsnStandService.class);
    private TbZwZkRstRuleService rstRuleService = SpringContextHolder.getBean(TbZwZkRstRuleService.class);

    protected Integer rid;
    protected TdZwZkCheckMain checkMain;

    protected Map<String, TsSimpleCode> itemMap;

    /**
     * 查询条件：地区集合
     */
    protected List<TsZone> zoneList;
    /**
     * 查询条件：地区名称
     */
    protected String searchZoneName;
    /**
     * 质控评估填报 查询条件 - 地区编码（默认管辖地区）
     */
    protected String searchZoneCode;
    /**
     * 质控评估填报 查询条件 - 评估日期开始
     */
    protected Date searchCheckStartTime;
    /**
     * 质控评估填报 查询条件 - 评估日期结束
     */
    protected Date searchCheckEndTime;
    /**
     * 质控评估填报 查询条件 - 是否需要整改
     */
    protected List<Integer> searchNeedImproveStateList;
    /**
     * 质控评估填报 查询条件 - 整改状态
     */
    protected List<Integer> searchFinishImproveStateList;
    protected List<SelectItem> checkTypeList;

    /**
     * 文件预览html代码
     */
    protected FilePojoNew fileTemp;

    /**
     * 当前查看的附件下标
     */
    protected Integer annexViewIndex;
    /**
     * 附件临时对象
     */
    protected TdZwZkCheckProve checkProveTmp;
    /**
     * 下载报告包含流信息的对象
     */
    protected StreamedContent reportDownloadFile;
    /**
     * 报表路径
     */
    protected String reportFilePath;
    /**
     * 下载文件名称
     */
    private String reportFileName;
    /**
     * 生成报告时的错误信息
     */
    protected String reportGenerateErrMsg;

    /**
     * 编辑页二与详情页二 对应的当前质控考核评估表
     */
    protected TdZwZkCheckTable curCheckTable;
    /**
     * 编辑页二 项目RID对应的评分项列表Map
     */
    protected Map<Integer, List<TbZwZkScores>> itemScoresMap;
    /**
     * 编辑页二与详情页二评估考核项目集合
     */
    protected List<TdZwZkCheckItem> zwZkCheckItemList;
    /**
     * 编辑页二与详情页二 考核指标序号及考核指标描述 key 评估项rid value 指标序号及考核指标描述
     */
    protected Map<Integer, String> indexDescMap;
    /**
     * 编辑页二 扣分原因赋值的对象
     */
    protected TdZwZkCheckSub curCheckSub;
    /**
     * 扣分原因弹出框显示的所有的扣分原因
     */
    protected List<TbZwZkScoreDeduct> allScoreDeductList;
    /**
     * 扣分原因弹出框显示的扣分原因列表
     */
    protected List<TbZwZkScoreDeduct> showScoreDeductList;
    /**
     * 扣分原因弹出框 检索扣分原因
     */
    protected String deductQuery;
    /**
     * 扣分原因弹出框确认后 需要更新的
     */
    protected String deductUpdateId;

    /**
     * 整改报告地址
     */
    protected String uploadImproveFilePath;
    /**
     * 整改报告名称
     */
    protected String uploadImproveFileName;
    /**
     * 整改报告地址缓存
     */
    protected String uploadImproveFileCachePath;
    /**
     * 整改报告名称缓存
     */
    protected String uploadImproveFileCacheName;
    /**
     * 考核类型  码表5554 key 码表id value  编码对象
     */
    protected List<TsSimpleCode> checkTypeSimpleList;
    /**
     * 码表5554 map
     */
    Map<Integer, TsSimpleCode> checkTypeMap;
    /**
     * 考核类型训中rid 上次
     */
    private Integer checkTypeTempRid;
    /**
     * 考核类型码表扩展字段1=1且扩展字段2=3 / 扩字段1=2  显示分值
     */
    private Boolean ifScore;
    /**
     * 评分项结果 过滤掉禁用项
     */
    protected List<TsSimpleCode> scoreRstSimpleList;
    /**
     * 评分项结果Map 包含禁用项
     */
    protected Map<Integer, TsSimpleCode> scoreRstSimpleMap;
    /**
     * 合格的评分项结果rid
     */
    protected Integer hgSoreRstRid;
    /**
     * key项类的编码 value 对应的可选择的结果列表
     */
    protected Map<String, List<TsSimpleCode>> soreRstSimpleMapWithItemTypeCode;
    /**
     * 重新汇总参数
     */
    private TdZwZkCheckSub checkSub;
    /**
     * 江苏【现场考核管理】版本新增"现场考核有无等级和考核结果表"参数（0或空：有，保持江苏原有逻辑，1:无,去除考核等级考核结果显示，去除相关必填控制）
     */
    protected Boolean ifRemoveCheckResults;
    /**
     * 内蒙古版本为2
     */
    protected Boolean ifRemoveCheckResults2;
    /**
     * 是否显示撤销按钮
     */
    protected boolean showCancel;

    /**
     * 是否有 按管辖范围 按钮权限
     */
    protected Boolean jurisdiction = Boolean.FALSE;

    /**
     * 被考核机构
     */
    protected TsUnit orgUnit;

    /**
     * 一级页面业务机构rid
     */
    protected Integer mainId;

    /**
     * 附件类型 0 : 考核证明材料，1：整改附件;2:现场考核表；3：现场意见表
     */
    private Integer annexType;

    /**
     * 详情页面 1:详情，2：整改，3：审核
     */
    private String viewType;
    /**
     * 无需考核的子表
     */

    protected List<TdZwZkCheckSub> noAssessCheckSubList;

    public AbstractTdZwZkCheckMainListBean() {
        initData();
    }

    /**
     * 修订内容:模块初始化
     *
     * @MethodReviser pw, 2021年07月12日
     */
    public abstract void initData();

    /**
     * 修订内容:模块类型，1：现场考核管理；2：现场考核查询
     *
     * @MethodReviser pw, 2021年07月12日
     */
    public abstract Integer pageType();

    /**
     * 题目结果项及分值映射 {key：题目id} -> {value：结果项及分值}
     */
    protected Map<Integer, List<TbZwZkScoreOption>> soreRstMap = new HashMap<>();
    /**
     * 评分项的码表映射 <pre>key: 评分项rid</pre><pre>value: 评分项码表rid</pre>
     */
    //protected Map<Integer, Integer> rstRstRidMap = new HashMap<>();
    /**
     * 合格的评分项结果rid
     */
    protected Set<Integer> hgSoreRstRidSet = new HashSet<>();

    /**
     * 题目结果项分值映射 {key：结果项id} -> {value：结果项对象}
     */
    protected Map<Integer, TbZwZkScoreOption> optionMap = new HashMap<>();

    @Override
    public void searchAction() {
        if (null != searchCheckStartTime && null != searchCheckEndTime) {
            if (searchCheckEndTime.before(searchCheckStartTime)) {
                JsfUtil.addErrorMessage("考核开始日期应小于等于结束日期！");
                return;
            }
        }
        super.searchAction();
    }

    /**
     * <p>方法描述：添加初始化</p>
     *
     * @MethodAuthor hsj 2025-06-04 11:54
     */
    @Override
    public void addInit() {
        this.checkMain = new TdZwZkCheckMain();
        this.checkMain.setFkByCheckTypeId(new TsSimpleCode());
        //考核类型初始化
        if (!CollectionUtils.isEmpty(checkTypeList)) {
            try {
                Integer checkTypeId = Convert.toInt(checkTypeList.get(0).getValue(), null);
                if (checkTypeId != null && this.checkTypeMap.containsKey(checkTypeId)) {
                    TsSimpleCode simpleCode = this.checkTypeMap.get(checkTypeId);
                    TsSimpleCode checkType = new TsSimpleCode();
                    checkType.setRid(simpleCode.getRid());
                    checkType.setExtendS1(simpleCode.getExtendS1());
                    checkType.setExtendS2(simpleCode.getExtendS2());
                    this.checkMain.setFkByCheckTypeId(checkType);
                    this.checkTypeTempRid = simpleCode.getRid();
                    this.checkMain.setCheckTypeId(this.checkTypeTempRid);
                    this.ifScore = "1".equals(simpleCode.getExtendS1())
                            && ObjectUtil.isNull(simpleCode.getExtendS2())
                            || "2".equals(simpleCode.getExtendS1());

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        this.checkMain.setCheckDate(new Date());
        this.checkMain.setStateMark(0);
        this.checkMain.setFkByRecordOrgId(Global.getUser().getTsUnit());
        this.checkMain.setFkByOrgId(new TsUnit());

        this.checkMain.setCheckProves(new ArrayList<TdZwZkCheckProve>());
        this.checkMain.setMaterialCheckProves(new ArrayList<TdZwZkCheckProve>());
        this.checkMain.setImproveCheckProves(new ArrayList<TdZwZkCheckProve>());

        this.checkMain.setDelMark(0);
        //本年度是否开展工作，默认为是
        this.checkMain.setIfDevelop(1);
        this.initCheckTable();
    }

    /**
     * <p>方法描述：码表初始化</p>
     *
     * @MethodAuthor hsj 2025-06-10 13:36
     */
    public void initParamItemMap() {
        this.itemMap = new HashMap<>();
        List<TsSimpleCode> itemList = commService.findUpCodeList("5530");
        if (!CollectionUtils.isEmpty(itemList)) {
            for (TsSimpleCode t : itemList) {
                this.itemMap.put(t.getCodeNo(), t);
            }
        }
        /**考核类型  码表*/
        this.checkTypeMap = new HashMap<>();
        this.checkTypeSimpleList = this.commService.findLevelSimpleCodesByTypeId("5554");
        if (!CollectionUtils.isEmpty(checkTypeSimpleList)) {
            for (TsSimpleCode t : checkTypeSimpleList) {
                this.checkTypeMap.put(t.getRid(), t);
            }
        }
    }

    @Override
    public void backAction() {
        setActiveTab(0);
        this.searchAction();

    }

    /**
     * <p>方法描述：初始化考核表</p>
     *
     * @MethodAuthor hsj 2025-06-04 11:56
     */
    private void initCheckTable() {
        this.noAssessCheckSubList = new ArrayList<>();
        List<TdZwZkCheckTable> checkTables = new ArrayList<>();
        this.checkMain.setCheckTables(checkTables);
        //考核类型为空时返回
        if (this.checkMain.getFkByCheckTypeId() == null || this.checkMain.getFkByCheckTypeId().getRid() == null) {
            return;
        }
        //根据考核类型查询考核表
        List<TbZwZkScores> list = standService.findTbZwZkScoresList(this.checkMain.getFkByCheckTypeId().getRid());
        //职业健康检查机构与诊断机构存在总分
        BigDecimal totalScore = BigDecimal.ZERO;

        if (!CollectionUtils.isEmpty(list)) {
            Map<Integer, TdZwZkCheckTable> standMap = new HashMap<>();
            Map<String, TdZwZkCheckItem> itemMap = new HashMap<>();
            Map<String, BigDecimal> checkValMap = new HashMap<>();

            for (TbZwZkScores t : list) {
                if (this.ifScore && t.getScore() != null) {
                    totalScore = totalScore.add(t.getScore());
                }
                List<TbZwZkScoreIndex> indexList = t.getIndexList();
                if (CollectionUtils.isEmpty(indexList)) {
                    continue;
                }
                TbZwZkScoreIndex scoreIndex = indexList.get(0);//考核指标
                if (null == scoreIndex.getFkByIndexId()) {
                    continue;
                }
                TbZwZkBadrsnStand stand = t.getFkByMainId();

                if (null == standMap.get(stand.getRid())) {
                    TdZwZkCheckTable checkTable = new TdZwZkCheckTable();
                    checkTable.setFkByCheckTableId(stand);
                    checkTable.setStateMark(0);
                    checkTable.setCreateDate(new Date());
                    checkTable.setCreateManid(Global.getUser().getRid());
                    checkTable.setFkByMainId(this.checkMain);
                    checkTables.add(checkTable);

                    String codeLevelNo = scoreIndex.getFkByIndexId().getCodeLevelNo();//指标层级编码
                    String[] split = codeLevelNo.split("\\.");
                    if (null == split || 0 == split.length) {
                        continue;
                    }
                    TsSimpleCode item = this.itemMap.get(split[0]);//父级-考核项目
                    if (item == null || item.getRid() == null){
                        continue;
                    }
                    List<TdZwZkCheckItem> checkItems = new ArrayList<>();
                    TdZwZkCheckItem checkItem = new TdZwZkCheckItem();
                    checkItem.setFkByMainId(checkTable);
                    checkItem.setFkByItemId(item);
                    checkItem.setCreateDate(new Date());
                    checkItem.setCreateManid(Global.getUser().getRid());
                    checkItems.add(checkItem);
                    checkTable.setCheckItems(checkItems);

                    standMap.put(stand.getRid(), checkTable);
                    String key = stand.getRid() + "&" + item.getRid();
                    itemMap.put(key, checkItem);
                    checkValMap.put(key, t.getScore());
                } else {
                    TdZwZkCheckTable checkTable = standMap.get(stand.getRid());
                    String codeLevelNo = scoreIndex.getFkByIndexId().getCodeLevelNo();
                    String[] split = codeLevelNo.split("\\.");
                    if (null == split || 0 == split.length) {
                        continue;
                    }
                    TsSimpleCode item = this.itemMap.get(split[0]);
                    if (item == null || item.getRid() == null){
                        continue;
                    }
                    String key = stand.getRid() + "&" + item.getRid();
                    if (null == itemMap.get(key)) {
                        TdZwZkCheckItem checkItem = new TdZwZkCheckItem();
                        checkItem.setFkByItemId(item);
                        checkItem.setFkByMainId(checkTable);
                        checkItem.setCreateDate(new Date());
                        checkItem.setCreateManid(Global.getUser().getRid());
                        checkTable.getCheckItems().add(checkItem);
                        itemMap.put(key, checkItem);
                        checkValMap.put(key, t.getScore());
                    } else {
                        BigDecimal current = checkValMap.get(key);
                        BigDecimal added = t.getScore() == null ? current :
                                (current == null ? t.getScore() : current.add(t.getScore()));
                        checkValMap.put(key, added);
                    }
                }
            }
            if (this.ifScore) {
                //分值处理
                this.dealCheckVal(checkTables, checkValMap);
                this.checkMain.setTotalCheckVal(totalScore);
            }

            this.initCheckSub();
        }
    }

    /**
     * <p>方法描述：一级页面初始化判断是否无需考核，若无需考核时考核子表赋值</p>
     *
     * @MethodAuthor hsj 2025-06-07 8:25
     */
    private void initCheckSub() {
        if (this.checkMain == null) {
            return;
        }
        boolean isCheck = this.checkMain.getFkByCheckTypeId() == null
                || this.checkMain.getFkByCheckTypeId().getRid() == null
                || this.checkMain.getFkByOrgId() == null
                || this.checkMain.getFkByOrgId().getRid() == null;
        if (isCheck) {
            return;
        }

        List<TdZwZkCheckTable> checkTables = this.checkMain.getCheckTables();
        if (CollectionUtils.isEmpty(checkTables)) {
            return;
        }

        for (TdZwZkCheckTable checkTable : checkTables) {
            this.curCheckTable = checkTable;
            this.initSubAction();

            if (CollectionUtils.isEmpty(this.zwZkCheckItemList)) {
                continue;
            }
            processCheckItems(checkTable);
        }

    }

    /**
     * <p>方法描述：处理每个 checkTable 下的 checkItems 和 checkSubList</p>
     *
     * @MethodAuthor hsj 2025-06-10 11:54
     */
    private void processCheckItems(TdZwZkCheckTable checkTable) {
        if (checkTable == null || this.zwZkCheckItemList == null) {
            return;
        }
        boolean ifAssessMark = true;//默认为无需考核
        List<TdZwZkCheckSub> noAssessCheckSubs = new ArrayList<>();
        for (TdZwZkCheckItem item : this.zwZkCheckItemList) {
            List<TdZwZkCheckSub> checkSubList = item.getCheckSubList();
            if (CollectionUtils.isEmpty(checkSubList)) {
                continue;
            }
            for (TdZwZkCheckSub checkSub : checkSubList) {
                if ("0".equals(Convert.toStr(checkSub.getAssessMark()))) {
                    ifAssessMark = false;
                    break;
                }
                noAssessCheckSubs.add(checkSub);
            }
            if (!ifAssessMark) {
                break;
            }
        }
        if (ifAssessMark) {
            checkTable.setStateMark(2);
            if (this.noAssessCheckSubList == null) {
                this.noAssessCheckSubList = new ArrayList<>();
            }
            this.noAssessCheckSubList.addAll(noAssessCheckSubs);
        }
    }

    /**
     * <p>方法描述：分值处理</p>
     *
     * @MethodAuthor hsj 2025-06-05 14:21
     */
    private void dealCheckVal(List<TdZwZkCheckTable> checkTables, Map<String, BigDecimal> checkValMap) {
        if (CollectionUtils.isEmpty(checkTables) || null == checkValMap) {
            return;
        }
        for (TdZwZkCheckTable checkTable : checkTables) {
            List<TdZwZkCheckItem> items = checkTable.getCheckItems();
            if (CollectionUtils.isEmpty(items)) {
                continue;
            }
            for (TdZwZkCheckItem item : items) {
                String key = checkTable.getFkByCheckTableId().getRid() + "&" + item.getFkByItemId().getRid();
                if (checkValMap.containsKey(key)) {
                    item.setCheckVal(MathUtils.clearPointEndZero(checkValMap.get(key)));
                }
            }
        }
    }

    /**
     * <p>方法描述：切换考核类型</p>
     *
     * @MethodAuthor hsj 2025-06-04 13:32
     */
    public void changeCheckType() {
        TsSimpleCode checkType = new TsSimpleCode();
        checkType.setRid(checkMain.getCheckTypeId());
        checkType.setExtendS1(checkTypeMap.get(checkMain.getCheckTypeId()).getExtendS1());
        checkType.setExtendS2(checkTypeMap.get(checkMain.getCheckTypeId()).getExtendS2());
        this.checkMain.setFkByCheckTypeId(checkType);
        this.checkTypeTempRid = checkType.getRid();
        this.initIfScore();
        //清空被评估机构
        this.checkMain.setFkByOrgId(new TsUnit());
        this.checkMain.setDelMark(0);
        this.mainId = null;
        this.clearCheckMain();
    }

    /**
     * <p>方法描述：取消切换考核类型</p>
     *
     * @MethodAuthor hsj 2025-06-04 14:27
     */
    public void cancelChangeCheckType() {
        this.checkMain.setCheckTypeId(this.checkTypeTempRid);
        this.checkMain.setFkByCheckTypeId(this.checkTypeMap.get(this.checkTypeTempRid));
    }

    /**
     * <p>方法描述：相关信息点清空</p>
     *
     * @MethodAuthor hsj 2025-06-04 13:39
     */
    private void clearCheckMain() {
        //本年度是否开展工作清空
        this.checkMain.setIfDevelop(1);
        this.initTdZwZkCheckRecord();
        this.clearData();
    }

    /**
     * <p>方法描述：相关信息点清空</p>
     *
     * @MethodAuthor hsj 2025-06-04 13:40
     */
    private void clearData() {
        //清空分数
        this.checkMain.setTotalCheckVal(null);
        this.checkMain.setTotalScoreVal(null);
        //评审结论清空
        this.checkMain.setReviewConclusion(null);
        //是否需要整改清空
        this.checkMain.setIfNeedImprove(null);
        this.checkMain.setIfImproveEnd(null);
        this.initCheckTable();
    }


    /**
     * <p>方法描述：选择被评估机构</p>
     *
     * @MethodAuthor hsj 2025-06-04 13:55
     */
    public void selectOrgList() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 760, null, 370);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> tmpList = new ArrayList<>();
        if (this.checkMain.getFkByCheckTypeId() == null || this.checkMain.getFkByCheckTypeId().getRid() == null) {
            SystemMessageEnum.PLEASE_SELECT.formatMessage("考核类型");
            return;
        }
        String ex1 = this.checkMain.getFkByCheckTypeId().getExtendS1();
        if (StringUtils.isBlank(ex1)) {
            SystemMessageEnum.MAINTENANCE_ERROR.formatMessage("考核类型");
            return;
        }
        tmpList.add(ex1);
        paramMap.put("checkType", tmpList);
        tmpList = new ArrayList<>();
        tmpList.add(this.zoneList.get(0).getZoneGb());
        paramMap.put("zoneGb", tmpList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/zkcheck/selectCheckOrgList", options, paramMap);
    }

    /**
     * <p>方法描述：选择被评估机构--验证是否切户机构</p>
     *
     * @MethodAuthor hsj 2025-06-04 13:48
     */
    public void onOrgSelect(SelectEvent event) {
        this.mainId = null;
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null == selectedMap || selectedMap.size() == 0) {
            return;
        }
        Object[] obj = (Object[]) selectedMap.get("selected");
        this.orgUnit = new TsUnit();
        this.orgUnit.setRid(Convert.toInt(obj[0]));
        this.orgUnit.setUnitname(Convert.toStr(obj[2]));
        if (obj[5] != null) {
            this.mainId = Integer.parseInt(obj[5].toString());
        }
        boolean flag = this.checkMain.getFkByOrgId() == null || this.checkMain.getFkByOrgId().getRid() == null;
        if (flag) {
            this.checkMain.setFkByOrgId(this.orgUnit);
            //备案类别初始化、考核表初始化
            initTdZwZkCheckRecord();
            this.initCheckSub();
            RequestContext.getCurrentInstance().update("tabView:editForm");
            return;
        }
        //弹出提示框
        RequestContext.getCurrentInstance().execute("PF('ConfirmOrgDialog').show();");
    }

    /**
     * <p>方法描述：确定机构的切换</p>
     *
     * @MethodAuthor hsj 2025-06-04 13:51
     */
    public void changeOrg() {
        this.checkMain.setFkByOrgId(this.orgUnit);
        this.clearCheckMain();
    }

    /**
     * <p>方法描述：取消机构的切换</p>
     *
     * @MethodAuthor hsj 2025-06-04 14:37
     */
    public void cancelChangeOrg() {
        this.orgUnit = this.checkMain.getFkByOrgId();
    }

    /**
     * <p>方法描述：切换本年度是否开展工作</p>
     *
     * @MethodAuthor hsj 2025-06-04 14:38
     */
    public void changeDevelop() {
        this.clearData();
    }

    /**
     * <p>方法描述：取消本年度是否开展工作</p>
     *
     * @MethodAuthor hsj 2025-06-04 14:40
     */
    public void cancelChangeDevelop() {
        this.checkMain.setIfDevelop(new Integer(1).equals(this.checkMain.getIfDevelop()) ? 0 : 1);
    }

    /**
     * <p>方法描述：选择机构之后初始化备案类别</p>
     *
     * @MethodAuthor hsj 2025-06-04 14:06
     */
    private void initTdZwZkCheckRecord() {
        this.checkMain.setRecordStr(null);
        //备案类别
        this.checkMain.setCheckRecords(new ArrayList<TdZwZkCheckRecord>());
        if (ObjectUtil.isNull(this.mainId)) {
            return;
        }
        String ex1 = this.checkMain.getFkByCheckTypeId().getExtendS1();
        StringBuffer stringBuffer = new StringBuffer();
        if ("1".equals(ex1)) {
            List<Object[]> objs = this.checkMainService.findTdZwTjorgItmDetailByMainId(mainId);
            if (!CollectionUtils.isEmpty(objs)) {
                Map<Integer, TdZwZkCheckRecord> checkRecordMap = new HashMap<>();
                for (Object[] obj : objs) {
                    Integer key = Convert.toInt(obj[9]);
                    if (!checkRecordMap.containsKey(key)) {
                        TdZwZkCheckRecord checkRecord = new TdZwZkCheckRecord();
                        TsSimpleCode simpleCode = new TsSimpleCode();
                        simpleCode.setRid(Convert.toInt(obj[0]));
                        simpleCode.setNum(Convert.toInt(obj[1]));
                        simpleCode.setCodeNo(Convert.toStr(obj[2]));
                        simpleCode.setCodeName(Convert.toStr(obj[3]));
                        simpleCode.setCodeDesc(Convert.toStr(obj[4]));
                        checkRecord.setFkByTypeId(simpleCode);
                        checkRecord.setFkByMainId(this.checkMain);
                        checkRecord.setTdZwZkCheckDetails(new ArrayList<TdZwZkCheckDetails>());
                        checkRecord.setCreateDate(new Date());
                        checkRecord.setCreateManid(Global.getUser().getRid());
                        checkRecordMap.put(key, checkRecord);
                    }
                    TsSimpleCode simpleCode = new TsSimpleCode();
                    simpleCode.setRid(Convert.toInt(obj[5]));
                    simpleCode.setNum(Convert.toInt(obj[6]));
                    simpleCode.setCodeNo(Convert.toStr(obj[7]));
                    simpleCode.setCodeName(Convert.toStr(obj[8]));
                    TdZwZkCheckDetails tdZwZkCheckDetails = new TdZwZkCheckDetails();
                    tdZwZkCheckDetails.setFkByItemId(simpleCode);
                    tdZwZkCheckDetails.setFkByMainId(checkRecordMap.get(key));
                    tdZwZkCheckDetails.setCreateDate(new Date());
                    tdZwZkCheckDetails.setCreateManid(Global.getUser().getRid());
                    checkRecordMap.get(key).getTdZwZkCheckDetails().add(tdZwZkCheckDetails);
                }
                for (Map.Entry<Integer, TdZwZkCheckRecord> entry : checkRecordMap.entrySet()) {
                    this.checkMain.getCheckRecords().add(entry.getValue());
                }
                for (TdZwZkCheckRecord checkRecord : this.checkMain.getCheckRecords()) {
                    List<TdZwZkCheckDetails> tdZwZkCheckDetails = checkRecord.getTdZwZkCheckDetails();
                    if (CollectionUtils.isEmpty(tdZwZkCheckDetails)) {
                        continue;
                    }
                    SortUtil.sortCodeByField(tdZwZkCheckDetails, TdZwZkCheckDetails.class, "getFkByItemId");
                }
                SortUtil.sortCodeByField(this.checkMain.getCheckRecords(), TdZwZkCheckRecord.class, "getFkByTypeId");
                for (TdZwZkCheckRecord checkRecord : this.checkMain.getCheckRecords()) {
                    stringBuffer.append("，").append(checkRecord.getFkByTypeId().getCodeDesc());
                }
            }
        } else {
            List<TsSimpleCode> tsSimpleCodeList = this.checkMainService.findTdZwTjorgRcdItemByMainId(mainId, ex1);
            if (!CollectionUtils.isEmpty(tsSimpleCodeList)) {
                for (TsSimpleCode simpleCode : tsSimpleCodeList) {
                    TdZwZkCheckRecord checkRecord = new TdZwZkCheckRecord();
                    checkRecord.setFkByTypeId(simpleCode);
                    checkRecord.setFkByMainId(this.checkMain);
                    checkRecord.setCreateDate(new Date());
                    checkRecord.setCreateManid(Global.getUser().getRid());
                    this.checkMain.getCheckRecords().add(checkRecord);
                    stringBuffer.append("，").append(simpleCode.getCodeDesc());
                }
            }
        }
        if (stringBuffer.length() > 0) {
            this.checkMain.setRecordStr(stringBuffer.substring(1));
        }
    }

    @Override
    public void viewInit() {
        this.checkMain = checkMainService.findTdZwZkCheckMain(this.rid);
        this.initViewType();
        this.initIfScore();
        this.jointDeductRsn();
    }

    /**
     * <p>方法描述：详情页面初始化</p>
     *
     * @MethodAuthor hsj 2025-06-06 17:56
     */
    private void initViewType() {
        this.viewType = "0";
        Integer ifImproveEnd = this.checkMain.getIfImproveEnd();
        if (ObjectUtil.isNull(ifImproveEnd) || ObjectUtil.isNull(pageType())) {
            return;
        }
        switch (ifImproveEnd) {
            case 0:
                //待整改，管理：正常显示，查询：待整改
                if (pageType() == 2) {
                    this.viewType = "2";
                }
                break;
            case 1:
                //已整改/待审核，管理：待审核，查询：显示整改信息
                if (pageType() == 1) {
                    this.viewType = "3";
                } else {
                    this.viewType = "1";
                }
                break;
            case 2:
                //已审核，管理：已审核，查询：已审核
                this.viewType = "1";
                break;
            case 3:
                //已退回，管理：显示整改信息，查询：待整改
                if (pageType() == 1) {
                    this.viewType = "1";
                } else {
                    this.viewType = "2";
                }
                break;
            default:
                break;
        }
        //当前页面为待整改时
        if ("2".equals(this.viewType)) {
            if (StringUtils.isBlank(this.checkMain.getImproveLinkman())) {
                this.checkMain.setImproveLinkman(Global.getUser().getUsername());
            }
            if (StringUtils.isBlank(this.checkMain.getImprovePhone())) {
                this.checkMain.setImprovePhone(Global.getUser().getMbNum());
            }
        }
    }

    /**
     * <p>方法描述：是否显示分值
     * 扩展2=3时不显示分值</p>
     *
     * @MethodAuthor hsj 2025-06-06 14:21
     */
    private void initIfScore() {
        if (this.checkMain.getFkByCheckTypeId() == null && this.checkMain.getFkByCheckTypeId().getRid() == null) {
            this.ifScore = false;
            return;
        }
        this.ifScore = !"3".equals(Convert.toStr(this.checkMain.getFkByCheckTypeId().getExtendS2()));
    }


    /**
     * @Description : 临时
     * @MethodAuthor:
     * @Date :
     **/
    public void revokeAction() {
        try {
            this.checkMain.setStateMark(0);
            this.commService.update(this.checkMain);
            JsfUtil.addSuccessMessage("撤销成功！");
            modInitAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
    }

    @Override
    public void modInit() {
        this.checkMain = checkMainService.findTdZwZkCheckMain(this.rid);
        //考核类型赋值
        this.checkTypeTempRid = this.checkMain.getCheckTypeId();
        this.initIfScore();
        this.jointDeductRsn();
    }

    /**
     * <p>方法描述：存在问题封装-抽取</p>
     *
     * @MethodAuthor hsj 2025-06-04 16:04
     */
    private void jointDeductRsn() {
        Map<Integer, List<TdZwZkCheckSub>> checkSubListMap = this.checkMainService.findTdZwZkCheckSubListByCheckMainRidForMapResult(checkMain.getRid());
        if (checkSubListMap == null || checkSubListMap.size() == 0) {
            return;
        }
        List<TdZwZkCheckTable> checkTables = this.checkMain.getCheckTables();
        if (CollectionUtils.isEmpty(checkTables)) {
            return;
        }
        sortCheckTableList(checkTables);
        for (TdZwZkCheckTable t : checkTables) {
            List<TdZwZkCheckItem> checkItems = t.getCheckItems();
            if (CollectionUtils.isEmpty(checkItems)) {
                continue;
            }
            sortCheckItemList(checkItems);
            for (TdZwZkCheckItem item : checkItems) {
                if (!checkSubListMap.containsKey(item.getRid())) {
                    continue;
                }
                List<TdZwZkCheckSub> checkSubList = checkSubListMap.get(item.getRid());
                if (CollectionUtils.isEmpty(checkSubList)) {
                    continue;
                }
                List<String> deductRsnList = new ArrayList<>();
                for (TdZwZkCheckSub checkSub : checkSubList) {
                    String deductRsn = checkSub.getDeductRsn();
                    if (StringUtils.isBlank(deductRsn)) {
                        continue;
                    }
                    deductRsnList.add(deductRsn);
                }
                this.updateDeductReasons(item, deductRsnList);
            }
        }
    }

    /**
     * <p>方法描述：扣分原因</p>
     *
     * @MethodAuthor hsj 2025-06-10 13:54
     */
    private void updateDeductReasons(TdZwZkCheckItem item, List<String> deductRsnList) {
        if (CollectionUtils.isEmpty(deductRsnList)) {
            item.setDeductRsn(null);
            item.setDeductRsn2(null);
            return;
        }
        item.setDeductRsn(StringUtils.join(deductRsnList, ";"));
        item.setDeductRsn2(StringUtils.join(deductRsnList, "；<br/>"));
    }

    /**
     * <p>方法描述：进入编辑页二</p>
     *
     * @MethodAuthor qrr, 2021年6月26日, modCheckIndex
     */
    public void modCheckIndex() {
        //判断考核表是否已提交，如果已提交，提示
        if(this.curCheckTable!=null && this.curCheckTable.getRid()!=null){
            String sql ="select STATE_MARK from TD_ZW_ZK_CHECK_TABLE where rid="+this.curCheckTable.getRid();
            List<Object> result = this.checkMainService.findDataBySqlNoPage(sql, null);
            if(CollectionUtils.isEmpty(result) || (null != result.get(0) && curCheckTable.getStateMark()!=null && !result.get(0).toString().equals(curCheckTable.getStateMark().toString()))){
                JsfUtil.addErrorMessage("状态已发生改变，请刷新页面！");
                return;
            }
        }

        if (!new Integer("1").equals(this.curCheckTable.getStateMark())) {
            if (this.validateOneData(0)) {
                return;
            }
            dealCheckTable();
            //调用保存方法
            this.checkMainService.tosave(this.checkMain, this.noAssessCheckSubList);

            this.rid = checkMain.getRid();
            modInit();
            if (!CollectionUtils.isEmpty(this.checkMain.getCheckTables())) {
                for (TdZwZkCheckTable checkTable : this.checkMain.getCheckTables()) {
                    if (checkTable.getFkByCheckTableId() != null && this.curCheckTable.getFkByCheckTableId() != null
                            && checkTable.getFkByCheckTableId().getRid() != null && this.curCheckTable.getFkByCheckTableId().getRid() != null
                            && checkTable.getFkByCheckTableId().getRid().equals(this.curCheckTable.getFkByCheckTableId().getRid())) {
                        this.curCheckTable = checkTable;
                        break;
                    }
                }
            }
        }



        forwardSubPage();
    }

    /**
     * <p>方法描述：一级页面暂存</p>
     *
     * @MethodAuthor hsj 2025-06-05 11:05
     */
    @Override
    public void saveAction() {
        if (this.validateOneData(0)) {
            return;
        }
        try {
            dealCheckTable();
            this.checkMainService.tosave(this.checkMain, this.noAssessCheckSubList);
            this.rid = checkMain.getRid();
            modInit();
            SystemMessageEnum.TEMP_SAVE_SUCCESS.showMessage();
        } catch (Exception e) {
            SystemMessageEnum.TEMP_SAVE_FAIL.showMessage();
            e.printStackTrace();
        }
    }

    /**
     * <p>方法描述：一级页面验证</p>
     *
     * @param type：类型；0：暂存，1：提交，2：现场考核表 3：现场考核意见书
     * @MethodAuthor hsj 2025-06-04 17:31
     */
    private boolean validateOneData(Integer type) {
        if (this.checkMain == null) {
            SystemMessageEnum.DATA_NOT_EXIST.showMessage();
            return true;
        }

        if (type == null) {
            JsfUtil.addErrorMessage("操作异常，请联系管理员！");
            return true;
        }
        boolean flag = false;
        //状态判断
        if (ObjectUtil.isNotNull(this.checkMain.getRid())) {
            String state = this.checkMainService.findStateById(this.checkMain.getRid());
            if (!"0".equals(state)) {
                SystemMessageEnum.CHECK_STATUS_CHANGE.showMessage();
                flag = true;
                return flag;
            }
        }
        if (type != 3) {
            if (this.checkMain.getFkByCheckTypeId() == null || this.checkMain.getFkByCheckTypeId().getRid() == null) {
                SystemMessageEnum.PLEASE_SELECT.formatMessage("考核类型");
                flag = true;
            }
            if (null == this.checkMain.getFkByOrgId() || null == this.checkMain.getFkByOrgId().getRid()) {
                SystemMessageEnum.PLEASE_SELECT.formatMessage("被考核机构");
                flag = true;
            }
            if (null == this.checkMain.getCheckDate()) {
                SystemMessageEnum.NOT_EMPTY.formatMessage("考核日期");
                flag = true;
            }
            if (ObjectUtil.isNull(this.checkMain.getIfDevelop())) {
                SystemMessageEnum.PLEASE_SELECT.formatMessage("本年度是否开展工作");
                flag = true;
            }

            // 同考核类型 同被考核机构 同年份的考核日期 不可以重复
            int count = this.checkMainService.findCountCheckMainExistsByCurYear(this.checkMain);
            if (count > 0) {
                JsfUtil.addErrorMessage("当前被考核机构当年已经存在现场质控考核记录，不允许重复添加！");
                flag = true;
            }
            //校验考核表是否存在“未提交”的
            if (type != 0) {
                for (TdZwZkCheckTable checkTable : this.checkMain.getCheckTables()) {
                    if (checkTable.getStateMark() == 0) {
                        JsfUtil.addErrorMessage(checkTable.getFkByCheckTableId().getCheckName() + "未提交！");
                        flag = true;
                    }
                }
            }
        }
        //提交和考核意见书需验证现场考核表
        if (type == 1 || type == 3) {
            if (StringUtils.isBlank(checkMain.getWritePath())) {
                SystemMessageEnum.PLEASE_UPLOAD.formatMessage("现场考核表");
                flag = true;
            }
        }
        //提交是验证考核意见表
        if (type == 1) {
            if (StringUtils.isBlank(checkMain.getCheckTablePath())) {
                SystemMessageEnum.PLEASE_UPLOAD.formatMessage("现场考核意见书");
                flag = true;
            }
        }
        return flag;
    }

    /**
     * <p>方法描述：处理大保存和小保存中的状态和审核人丢失的问题</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-06-17
     **/
    public void dealCheckTable() {
        List<TdZwZkCheckTable> tmpCheckList = checkMain.getCheckTables();
        if (CollectionUtils.isEmpty(tmpCheckList)) {
            return;
        }
        Map<Integer, TdZwZkCheckTable> tableMap = getCheckTableMap();
        for (TdZwZkCheckTable t : tmpCheckList) {
            if (tableMap != null && tableMap.get(t.getRid()) != null) {
                t.setCheckPsn(tableMap.get(t.getRid()).getCheckPsn());
                t.setStateMark(tableMap.get(t.getRid()).getStateMark());
            }
        }
    }

    /**
     * <p>方法描述：提交前验证</p>
     *
     * @MethodAuthor hsj 2025-06-07 11:13
     */
    public void beforeSubmitAction() {
        if (this.validateOneData(1)) {
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmSubmitDialog').show();");
    }

    /**
     * <p>方法描述：提交</p>
     *
     * @MethodAuthor hsj 2025-06-05 10:49
     */
    public void submitAction() {
        try {
            this.checkMain.setStateMark(1);
            dealCheckTable();
            //若是否需要整改为是时，整改状态为0
            if ("1".equals(Convert.toStr(this.checkMain.getIfNeedImprove()))) {
                this.checkMain.setIfImproveEnd(0);
            }
            this.checkMainService.tosave(this.checkMain, this.noAssessCheckSubList);
            this.rid = checkMain.getRid();
            this.viewInitAction();
            SystemMessageEnum.SUBMIT_SUCCESS.showMessage();
            RequestContext.getCurrentInstance().update("tabView");
        } catch (Exception e) {
            SystemMessageEnum.SUBMIT_FAIL.showMessage();
            this.checkMain.setStateMark(0);
            this.checkMain.setIfImproveEnd(null);
            e.printStackTrace();
        }

    }


    /**
     * <p>
     * 方法描述：附件上传前验证
     * </p>
     *
     * @MethodAuthor yph, 2021年06月28日
     */

    public void uploadFile() {
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("PF('FileDialog').show();");
    }

    /**
     * <p>方法描述：附件查看 annexType 0：证明材料，1：整改附件</p>
     *
     * @MethodAuthor hsj 2025-06-05 15:19
     */
    public void toAnnexView() {
        if (!"0".equals(Convert.toStr(annexType)) && !"1".equals(Convert.toStr(annexType))) {
            return;
        }
        List<TdZwZkCheckProve> checkProves = "1".equals(Convert.toStr(this.annexType))
                ? this.checkMain.getImproveCheckProves()
                : this.checkMain.getMaterialCheckProves();
        if (this.checkMain == null || CollectionUtils.isEmpty(checkProves)) {
            return;
        }
        // 校验 annexViewIndex 是否越界
        if (annexViewIndex == null || annexViewIndex < 0 || annexViewIndex >= checkProves.size()) {
            return;
        }
        this.checkProveTmp = checkProves.get(this.annexViewIndex);

        String annexPath = this.checkProveTmp.getAnnexAddr();
        if (StringUtils.isBlank(annexPath)) {
            SystemMessageEnum.FILE_NOT_EXIST.showMessage();
            return;
        }
        String fileType = "2";
        if (annexPath.endsWith(".pdf")) {
            fileType = "1";
        }
        if ("2".equals(fileType)) {// 图片类型
            this.fileTemp = new FilePojoNew();
            String url = FileUtils.compressImg(annexPath, "jswj-input", 600, 400, "1F1F1F");
            StringBuilder sb = new StringBuilder();
            sb.append(
                            "<div style=\"width:600px;height:400px;margin: auto;\"><img src=\"")
                    .append(url)
                    .append("\" style=\"width:100%;max-height:100%;cursor: pointer;\" onclick=\"window.open('"
                            + FileUtils.previewFile(annexPath, "jswj-input")
                            + "')\" /></div>");
            this.fileTemp.setFilePath(sb.toString());
            this.fileTemp.setFileName(this.checkProveTmp.getAnnexName());
        } else if ("1".equals(fileType)) {// PDF
            this.fileTemp = new FilePojoNew();
            StringBuilder sb = new StringBuilder();
            String url = FileUtils.previewFile(annexPath, "jswj-input");
            sb.append("<div id=\"showpdf\" style=\"width:100%; height:100%\" onmouseover=\"showFullScreen()\" onmouseout=\"hideFullScreen()\">");
            sb.append("<embed width=\"100%\" height=\"100%\" id=\"pdf\" ");
            sb.append(" src=\"" + url + "#toolbar=0\" wmode=\"window\"");
            sb.append(" type=\"application/pdf\"  internalinstanceid=\"1\"");
            sb.append(" </embed>");
            sb.append("<div class=\"fullscreen\"><span title=\"全屏\" onclick=\"openFullScreen()\"></span></div>");
            sb.append(" </div>");
            this.fileTemp.setFilePath(sb.toString());
            this.fileTemp.setFileName(this.checkProveTmp.getAnnexName());
        }
        if (this.annexViewIndex != null) {
            if (this.annexViewIndex != 0) {
                this.checkProveTmp.setPreIndex(this.annexViewIndex - 1);
            } else {
                this.checkProveTmp.setPreIndex(null);
            }

            if (this.annexViewIndex != checkProves.size() - 1) {
                this.checkProveTmp.setNextIndex(this.annexViewIndex + 1);
            } else {
                this.checkProveTmp.setNextIndex(null);
            }
        }
        RequestContext.getCurrentInstance().execute("PF('AnnexViewDialog').show()");
        if (this.checkMain.getStateMark() == 0) {
            RequestContext.getCurrentInstance().update("tabView:editForm:annexViewPanel");
        } else {
            RequestContext.getCurrentInstance().update("tabView:viewForm:annexViewDialogView");
        }

    }

    /**
     * <p>方法描述：附件上传 annexType 0：证明材料，1：整改附件</p>
     *
     * @MethodAuthor hsj 2025-06-05 14:40
     */
    public synchronized void fileUpload(FileUploadEvent event) {
        try {
            if (this.checkMain == null || ObjectUtil.isNull(this.annexType)) {
                return;
            }
            UploadedFile file = event.getFile();
            String fileName = file.getFileName();// 文件名称
            String contentType = file.getContentType().toLowerCase();
            String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, fileName, this.annexType == 0 ? "3" : "2");

            if (StringUtils.isNotBlank(errorMsg)) {
                JsfUtil.addErrorMessage(errorMsg);
                String updateComponent = this.annexType == 0 ? "tabView:editForm:fileUpload" : "tabView:viewForm:fileUpload";
                RequestContext.getCurrentInstance().update(updateComponent);
                return;
            }

            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            String path = JsfUtil.getAbsolutePath();
            String relativePath = new StringBuffer("heth/zkCheck/").append(uuid)
                    .append(fileName.substring(fileName.lastIndexOf("."))).toString();
            // 文件路径
            String filePath = new StringBuilder(path).append(relativePath).toString();
            FileUtils.copyFile(filePath, file.getInputstream());

            TdZwZkCheckProve prove = new TdZwZkCheckProve();
            prove.setAnnexName(fileName);
            prove.setAnnexAddr(relativePath);
            prove.setAnnexType(this.annexType);
            prove.setCreateDate(new Date());
            prove.setCreateManid(Global.getUser().getRid());
            prove.setModifyDate(new Date());
            prove.setModifyManid(Global.getUser().getRid());
            prove.setFkByMainId(this.checkMain);
            this.checkMain.getCheckProves().add(prove);
            if (this.annexType == 0) {
                this.checkMain.getMaterialCheckProves().add(prove);
            } else {
                this.checkMain.getImproveCheckProves().add(prove);
            }
            RequestContext context = RequestContext.getCurrentInstance();
            String updatePanel = this.annexType == 0 ? "tabView:editForm:checkContentPanel" : "tabView:viewForm:improvePanel";
            context.update(updatePanel);
            SystemMessageEnum.UPLOAD_SUCCESS.showMessage();
            context.execute("PF('FileDialog').hide();");
        } catch (Exception e) {
            e.printStackTrace();
            SystemMessageEnum.UPLOAD_FAIL.showMessage();
        }

    }

    /**
     * <p>方法描述：删除附件 annexType 0：证明材料，1：整改附件</p>
     *
     * @MethodAuthor hsj 2025-06-05 15:15
     */
    public void delFileAction() {
        try {
            if (this.checkMain == null) {
                return;
            }
            String annexTypeStr = Convert.toStr(this.annexType);
            List<TdZwZkCheckProve> checkProves = "1".equals(annexTypeStr)
                    ? this.checkMain.getImproveCheckProves()
                    : this.checkMain.getMaterialCheckProves();
            if (null == this.annexViewIndex || CollectionUtils.isEmpty(checkProves) || checkProves.get(this.annexViewIndex) == null) {
                return;
            }
            TdZwZkCheckProve prove = checkProves.get(this.annexViewIndex);
            if (prove == null) {
                return;
            }
            if ("1".equals(annexTypeStr)) {
                this.checkMain.getImproveCheckProves().remove(prove);
            } else {
                this.checkMain.getMaterialCheckProves().remove(prove);
            }
            this.checkMain.getCheckProves().remove(prove);
            if (null == prove.getNextIndex()) {
                if (null == prove.getPreIndex()) {
                    fileTemp.setFilePath(null);
                } else {
                    //显示上一个
                    annexViewIndex = prove.getPreIndex();
                    toAnnexView();
                }
            } else {
                //显示下一个
                annexViewIndex = prove.getNextIndex() - 1;
                toAnnexView();
            }
            SystemMessageEnum.DELETE_SUCCESS.showMessage();
            // 关闭弹窗逻辑
            boolean isLastItem = ("1".equals(annexTypeStr) && CollectionUtils.isEmpty(this.checkMain.getImproveCheckProves()))
                    || ("0".equals(annexTypeStr) && CollectionUtils.isEmpty(this.checkMain.getMaterialCheckProves()));
            if (isLastItem) {
                RequestContext.getCurrentInstance().execute("PF('AnnexViewDialog').hide()");
            }
        } catch (Exception e) {
            SystemMessageEnum.DELETE_FAIL.showMessage();
            e.printStackTrace();
        }
    }


    /**
     * <p>方法描述：现场考核表与现场意见书生成</p>
     * annexType : 2:现场考核表  3:现场意见书
     *
     * @MethodAuthor hsj 2025-06-10 9:46
     */
    public void preGenerateReport() {
        if (this.validateOneData(this.annexType)) {
            RequestContext.getCurrentInstance().execute("hideShade()");
            return;
        }
        // 保存
        try {
            dealCheckTable();
            /*调用保存方法*/
            this.checkMainService.tosave(this.checkMain, this.noAssessCheckSubList);
            this.rid = checkMain.getRid();
            modInit();
        } catch (Exception e) {
            e.printStackTrace();
            // 生成失败
            SystemMessageEnum.GENERATE_FAIL.showMessage();
            RequestContext.getCurrentInstance().execute("hideShade()");
            return;
        }
        String rptCode = null;
        if ("2".equals(Convert.toStr(this.annexType))) {
            TsSimpleCode checkTp = this.checkMain.getFkByCheckTypeId();
            String ext1 = null == checkTp ? null : checkTp.getExtendS1();
            Integer ext2 = null == checkTp ? null : checkTp.getExtendS2();
            if (StringUtils.isBlank(ext1)) {
                SystemMessageEnum.GENERATE_FAIL.showMessage();
                RequestContext.getCurrentInstance().execute("hideShade()");
                return;
            }

            if ("2".equals(ext1)) {
                rptCode = "HETH_ZK_2003";
            }
            if ("1".equals(ext1)) {
                rptCode = null != ext2 && 3 == ext2 ? "HETH_ZK_2002" : "HETH_ZK_2001";
            }
        } else if ("3".equals(Convert.toStr(this.annexType))) {
            rptCode = "HETH_ZK_2004";
        }

        this.reportFilePath = WordReportGenerateUtil.generateReport(this.rid, rptCode);
        if (StringUtils.isBlank(this.reportFilePath)) {
            SystemMessageEnum.GENERATE_FAIL.showMessage();
            RequestContext.getCurrentInstance().execute("hideShade()");
            return;
        }
        if (!WordReportGenerateUtil.validateFileExists(this.reportFilePath)) {
            SystemMessageEnum.FILE_NOT_EXIST.showMessage();
            RequestContext.getCurrentInstance().execute("hideShade()");
            return;
        }
        RequestContext.getCurrentInstance().execute("hideShade()");
        this.fillReportFileName(this.annexType);

        RequestContext.getCurrentInstance().execute("generateClick()");
    }


    /**
     * <p>方法描述：现场考核表与现场意见书-下载</p>
     * annexType : 2:现场考核表  3:现场意见书
     *
     * @MethodAuthor hsj 2025-06-10 9:46
     */
    public void preDownLoadReport() {
        if (!WordReportGenerateUtil.validateFileExists(this.reportFilePath)) {
            SystemMessageEnum.FILE_NOT_EXIST.showMessage();
            return;
        }
        this.fillReportFileName(this.annexType);
        RequestContext.getCurrentInstance().execute("generateClick()");
    }

    /**
     * <p>方法描述：下载文件名称 </p>
     * pw 2025/6/9
     **/
    private void fillReportFileName(Integer type) {
        this.reportFileName = null;
        TsSimpleCode checkTp = this.checkMain.getFkByCheckTypeId();
        String ext1 = null == checkTp ? null : checkTp.getExtendS1();
        Integer ext2 = null == checkTp ? null : checkTp.getExtendS2();
        if (StringUtils.isBlank(ext1)) {
            return;
        }
        String pre = DateUtils.getYear(this.checkMain.getCheckDate()) + "年" + (null == this.checkMain.getFkByOrgId() || null == this.checkMain.getFkByOrgId().getUnitname() ? "" : this.checkMain.getFkByOrgId().getUnitname());
        String name = type == 2 ? "表" : "意见书";
        if ("2".equals(ext1)) {
            this.reportFileName =  pre + "职业病诊疗机构现场考核" + name;
        }
        if ("1".equals(ext1)) {
            this.reportFileName = pre + (null != ext2 && 3 == ext2 ? "放射工作人员职业健康检查机构现场考核" + name : "职业健康检查机构现场考核" + name);
        }
    }


    /**
     * @Description: 上传报告
     * @MethodAuthor pw, 2021年06月9日
     */
    public synchronized void uploadReport(FileUploadEvent event) {
        String annexTypeStr = Convert.toStr(this.annexType);
        if (null != event) {
            UploadedFile file = event.getFile();
            String fileName = file.getFileName();
            // 得到唯一uid
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            // 后缀名
            String hz = fileName.substring(fileName.lastIndexOf(".") + 1);
            File dir = new File(JsfUtil.getAbsolutePath() + "heth/zkCheck/");
            if (!dir.exists()) {
                dir.mkdirs();
            }
            // 文件路径
            String filePath = JsfUtil.getAbsolutePath() + "heth/zkCheck/" + uuid + "." + hz;
            String simpleFilePath = "/heth/zkCheck/" + uuid + "." + hz;

            try {
                if ("pdf".equals(hz)) {
                    String errorMsg = FileUtils.veryFile(file.getInputstream(), file.getContentType(), fileName, "2");
                    if (StringUtils.isNotBlank(errorMsg)) {
                        JsfUtil.addErrorMessage(errorMsg);
                        RequestContext.getCurrentInstance().update("tabView:editForm:uploadReport");
                        return;
                    }
                }
                FileUtils.copyFile(filePath, file.getInputstream());
                if ("2".equals(annexTypeStr)) {
                    this.checkMain.setWritePath(simpleFilePath);
                } else if ("3".equals(annexTypeStr)) {
                    this.checkMain.setCheckTablePath(simpleFilePath);
                }
                this.checkMainService.updatePath(simpleFilePath,annexTypeStr,this.checkMain.getRid());
                SystemMessageEnum.UPLOAD_SUCCESS.showMessage();
                RequestContext.getCurrentInstance().execute("ReportFileUpload.hide();");
                RequestContext.getCurrentInstance().update("tabView:editForm");
            } catch (Exception e) {
                RequestContext.getCurrentInstance().update("tabView:editForm:reportFileUpload");
                RequestContext.getCurrentInstance().execute("ReportFileUpload.show();");
                File tmpFile = new File(filePath);
                if (null != tmpFile && tmpFile.exists()) {
                    tmpFile.delete();
                }
                if ("2".equals(annexTypeStr)) {
                    this.checkMain.setWritePath(null);
                } else if ("3".equals(annexTypeStr)) {
                    this.checkMain.setCheckTablePath(null);
                }
                SystemMessageEnum.UPLOAD_FAIL.showMessage();
                e.printStackTrace();
            }
        }
    }

    /**
     * <p>方法描述：现场考核表与现场意见书上传</p>
     *
     * @MethodAuthor hsj 2025-06-10 9:22
     */
    public void preUploadReport() {
        if (this.validateOneData(this.annexType)) {
            return;
        }
        RequestContext.getCurrentInstance().update("tabView:editForm:reportFileUpload");
        RequestContext.getCurrentInstance().execute("ReportFileUpload.show();");
    }

    /**
     * @Description: 删除报告
     * @MethodAuthor pw, 2021年06月9日
     */
    public void deleteReport() {
        String annexTypeStr = Convert.toStr(this.annexType);

        try {
            this.checkMainService.updatePath(null,annexTypeStr,this.checkMain.getRid());
            if ("2".equals(annexTypeStr)) {
                String filePath = this.checkMain.getWritePath();
                if (StringUtils.isBlank(filePath)) {
                    return;
                }
                this.checkMain.setWritePath(null);
            } else if ("3".equals(annexTypeStr)) {
                String filePath = this.checkMain.getCheckTablePath();
                if (StringUtils.isBlank(filePath)) {
                    return;
                }
                this.checkMain.setCheckTablePath(null);
            }
            SystemMessageEnum.DELETE_SUCCESS.showMessage();
        }catch (Exception e) {
            e.printStackTrace();
            SystemMessageEnum.DELETE_FAIL.showMessage();
        }
    }


    /**
     * <p>方法描述：生成、下载报告 </p>
     * pw 2025/6/9
     **/
    public StreamedContent getReportFile() {
        if (StringUtils.isBlank(this.reportFilePath)) {
            return null;
        }
        String contentType = "application/msword";
        if (this.reportFilePath.toUpperCase().endsWith(".PDF")) {
            contentType = "application/pdf";
        }
        String hz = this.reportFilePath.substring(this.reportFilePath.lastIndexOf(".") + 1);
        return WordReportGenerateUtil.downloadReport(this.reportFilePath, this.reportFileName + "." + hz, contentType);
    }


    @Override
    public IProcessData buildProcessData() {
        return super.buildProcessData();
    }

    @Override
    public void processData(List<?> list) {
        if (!CollectionUtils.isEmpty(list)) {
            List<Object[]> result = (List<Object[]>) list;
            for (Object[] obj : result) {
                //处理地区显示
                if (null != obj[1]) {
                    String[] split = obj[1].toString().split("_");
                    if (split.length > 1) {
                        obj[1] = obj[1].toString().substring(obj[1].toString().indexOf("_") + 1);
                    }
                }
                if (null != obj[9]) {
                    String path = obj[9].toString();
                    if (StringUtils.isBlank(path)) {
                        obj[9] = null;
                    }
                }
            }
        }
    }


    /**
     * @Description: 删除健康检查质控考核主表
     * @MethodAuthor pw, 2021年06月29日
     */
    public void deleteAction() {
        if (null == rid) {
            return;
        }
        String deleteSql = " UPDATE TD_ZW_ZK_CHECK_MAIN SET DEL_MARK=1 WHERE RID=" + rid;
        try {
            this.commService.executeSql(deleteSql, null);
            JsfUtil.addSuccessMessage("删除成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addSuccessMessage("删除失败！");
        }
    }


    /**
     * @Description: 编辑页二暂存
     * @MethodAuthor pw, 2021年07月7日
     */
    public void saveSubAction() {
        try {
            if (validateSaveSub()) {
                if (null != curCheckTable) {
                    curCheckTable.setStateMark(0);
                }
                //changeCheckTable();
                //checkMain重新处理评估表，//更新当前评估表的内容
                //this.checkMainService.tosave(this.checkMain, this.noAssessCheckSubList);

                // 2. 只保存当前考核表，不需要处理noAssessCheckSubList
                this.checkMainService.saveCheckTable(curCheckTable);
                //fillNewCurCheckTable();
                executeSaveSub();
                modInit();
                JsfUtil.addSuccessMessage("暂存成功！");
                RequestContext.getCurrentInstance().update("tabView:editForm2:dataPanel");
                RequestContext.getCurrentInstance().update("tabView:editForm");
            }
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("暂存失败！");
        }
    }

    /**
     * @Description: 编辑页二提交
     * @MethodAuthor pw, 2021年07月7日
     */
    public void submitSubAction() {
        try {
            if (validateSubmitSub()) {
                if (null != curCheckTable) {
                    curCheckTable.setStateMark(1);
                }

                // 2. 只保存当前考核表，不需要处理noAssessCheckSubList
                this.checkMainService.saveCheckTable(curCheckTable);
                //changeCheckTable();
                //fillNewCurCheckTable();
                executeSaveSub();
                //最后一个考核明细表提交时，更新得分
                updateTotalScoreVal();
                //submitSubLastAction();
                //changeItemDeductRsn();
                modInit();
                JsfUtil.addSuccessMessage("提交成功！");
                initSubAction();
                this.forwardEdit3Page();
                RequestContext.getCurrentInstance().update("tabView");
                RequestContext.getCurrentInstance().scrollTo("tabView:editForm3:editTitleGrid2");
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (null != curCheckTable) {
                curCheckTable.setStateMark(0);
            }
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
     * <p>描述 在最后一个考核明细提交时，计算并更新主表得分</p>
     *
     * @return void
     * @MethodAuthor gongzhe, 2022/7/8 13:39,updateTotalScoreVal
     */
    private void updateTotalScoreVal() {
       // List<TdZwZkCheckTable> tmpCheckList = checkMain.getCheckTables();
        TdZwZkCheckMain tempCheckMain = checkMainService.findTdZwZkCheckMain(checkMain.getRid());
        List<TdZwZkCheckTable> tmpCheckList=tempCheckMain.getCheckTables();
        if (CollectionUtils.isEmpty(tmpCheckList)) {
            return;
        }
        boolean calMark = true;
        for (TdZwZkCheckTable checkTable : tmpCheckList) {
            if (!new Integer(1).equals(checkTable.getStateMark()) && !new Integer(2).equals(checkTable.getStateMark())) {
                calMark = false;
                break;
            }
        }
        if (calMark) {
            //计算总得分
            countTatalScores(tmpCheckList);
            //是否需要整改赋值
            evalIfNeedImprove(tmpCheckList);

            //根据考核类型区分的特殊逻辑
            if (this.checkMain.getFkByCheckTypeId() != null && checkTypeMap.containsKey(this.checkMain.getFkByCheckTypeId().getRid())) {
                TsSimpleCode tsSimpleCode = checkTypeMap.get(this.checkMain.getFkByCheckTypeId().getRid());
                if ("1".equals(tsSimpleCode.getExtendS1()) && !new Integer("3").equals(tsSimpleCode.getExtendS2())) {
                    //职业健康检查机构
                } else if ("2".equals(tsSimpleCode.getExtendS1())) {
                    //职业病诊断机构
                } else if ("1".equals(tsSimpleCode.getExtendS1()) && new Integer("3").equals(tsSimpleCode.getExtendS2())) {
                    //放射人员 评审结论
                    evalConclusion(tmpCheckList);
                }
            }
            this.checkMainService.toSaveMain(this.checkMain);
        }
    }

    /**
     * <p>Description：放射人员-评审结论 </p>
     * <p>Author： yzz 2025/6/6 </p>
     */
    public void evalConclusion(List<TdZwZkCheckTable> tmpCheckList) {
        // 初始化计数器
        int keyQualified = 0;      // 关键项合格数
        int keyBasicQualified = 0; // 关键项基本符合数
        int keyUnqualified = 0;    // 关键项不符合数

        int normalQualified = 0;      // 一般项合格数
        int normalBasicQualified = 0; // 一般项基本符合数
        int normalUnqualified = 0;    // 一般项不符合数

        int vetoQualified = 0;      // 否决项合格数
        int vetoBasicQualified = 0; // 否决项基本符合数
        int vetoUnqualified = 0;    // 否决项不符合数

        // 统计逻辑保持不变
        for (TdZwZkCheckTable checkTable : tmpCheckList) {
            //考核表无需考核时，不参与计算
            if (new Integer("2").equals(checkTable.getStateMark())) {
                continue;
            }
            List<TdZwZkCheckItem> checkItems = checkTable.getCheckItems();
            if (ObjectUtil.isNotEmpty(checkItems)) {
                for (TdZwZkCheckItem checkItem : checkItems) {
                    List<TdZwZkCheckSub> checkSubList = checkItem.getCheckSubList();
                    if (CollectionUtils.isEmpty(checkSubList)) {
                        continue;
                    }
                    for (TdZwZkCheckSub checkSub : checkSubList) {
                        //题目是无需考核项时，不参与计算
                        if (new Integer("1").equals(checkSub.getAssessMark()) || new Integer("2").equals(checkSub.getAssessMark())) {
                            continue;
                        }
                        if (checkSub.getFkByScoreId() != null && checkSub.getFkByScoreId().getFkByItemTypeId() != null
                                && StringUtils.isNotBlank(checkSub.getFkByScoreId().getFkByItemTypeId().getExtendS1()) && checkSub.getFkByRstId() != null && checkSub.getFkByRstId().getRid() != null) {
                            String itemType = checkSub.getFkByScoreId().getFkByItemTypeId().getExtendS1();
                            Integer rstRid = checkSub.getFkByRstId().getRid();
                            if (rstRid != null && optionMap.containsKey(rstRid) && !CollectionUtils.isEmpty(checkSub.getFkByScoreId().getScoreOptionList())) {
                                TbZwZkScoreOption option = optionMap.get(rstRid);
                                if (option != null && StringUtils.isNotBlank(option.getFkByOptionId().getExtendS1())) {
                                    String optionType = option.getFkByOptionId().getExtendS1();
                                    // 根据项目类型和选项类型进行统计
                                    if ("1".equals(itemType)) { // 关键项
                                        if ("1".equals(optionType) || "5".equals(optionType)) {
                                            keyQualified++;
                                        } else if ("2".equals(optionType)) {
                                            keyBasicQualified++;
                                        } else if ("3".equals(optionType) || "6".equals(optionType)) {
                                            keyUnqualified++;
                                        }
                                    } else if ("2".equals(itemType)) { // 一般项
                                        if ("1".equals(optionType) || "5".equals(optionType)) {
                                            normalQualified++;
                                        } else if ("2".equals(optionType)) {
                                            normalBasicQualified++;
                                        } else if ("3".equals(optionType) || "6".equals(optionType)) {
                                            normalUnqualified++;
                                        }
                                    } else if ("3".equals(itemType)) { // 否决项
                                        if ("1".equals(optionType) || "5".equals(optionType)) {
                                            vetoQualified++;
                                        } else if ("2".equals(optionType)) {
                                            vetoBasicQualified++;
                                        } else if ("3".equals(optionType) || "6".equals(optionType)) {
                                            vetoUnqualified++;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 根据统计结果判断评审结论
        int conclusion = 1; // 默认通过

        // 4. 不通过 - 最高优先级
        if (vetoUnqualified >= 1 ||
                keyUnqualified > 2 ||
                keyBasicQualified > 5 ||
                normalUnqualified > 5 ||
                normalBasicQualified > 10) {
            conclusion = 4;
        }
        // 3. 整改后复审 - 次高优先级
        else if ((keyUnqualified >= 1 && keyUnqualified <= 2) ||
                (keyBasicQualified >= 3 && keyBasicQualified <= 5) ||
                (normalUnqualified >= 3 && normalUnqualified <= 5) ||
                (normalBasicQualified >= 8 && normalBasicQualified <= 10)) {
            conclusion = 3;
        }
        // 2. 整改后通过 - 次低优先级
        else if ((keyBasicQualified >= 1 && keyBasicQualified <= 2) ||
                (normalUnqualified >= 1 && normalUnqualified <= 2) ||
                (normalBasicQualified >= 6 && normalBasicQualified <= 7)) {
            conclusion = 2;
        }
        // 1. 通过 - 最低优先级
        // 默认情况，不需要额外判断

        // 设置评审结论
        this.checkMain.setReviewConclusion(conclusion);
    }

    /**
     * <p>Description： 是否需要整改赋值</p>
     * <p>Author： yzz 2025/6/6 </p>
     */
    public void evalIfNeedImprove(List<TdZwZkCheckTable> tmpCheckList) {
        boolean ifHasDeductRsn = false;
        for (TdZwZkCheckTable checkTable : tmpCheckList) {
            //考核表整个是无需考核  不参与计算
            if (new Integer("2").equals(checkTable.getStateMark())) {
                continue;
            }
            List<TdZwZkCheckItem> checkItems = checkTable.getCheckItems();
            if (ObjectUtil.isEmpty(checkItems)) {
                continue;
            }
            //计算总得分
            Map<Integer, List<TdZwZkCheckSub>> checkSubListMap = checkMainService.findTdZwZkCheckSubListByCheckMainRidForMapResult(checkMain.getRid());

            for (TdZwZkCheckItem checkItem : checkItems) {
                //页面传入的checkSubList
                List<TdZwZkCheckSub> checkSubList = checkItem.getCheckSubList();
                if (checkSubList == null) {
                    checkSubList = checkSubListMap.get(checkItem.getRid());
                    checkItem.setCheckSubList(checkSubList);
                }
                if (CollectionUtils.isEmpty(checkSubList)) {
                    continue;
                }
                for (TdZwZkCheckSub checkSub : checkSubList) {
                    if (!new Integer("0").equals(checkSub.getAssessMark())) {
                        continue;
                    }
                    if (StringUtils.isNotBlank(checkSub.getDeductRsn())) {
                        ifHasDeductRsn = true;
                        break;
                    }
                }
            }
        }
        if (ifHasDeductRsn) {
            checkMain.setIfNeedImprove(1);
        } else {
            checkMain.setIfNeedImprove(0);
        }
    }

    /**
     * @Description: 最后一个未提交项目的编辑二页面 提交时若是否需要整改为null 需要赋值
     * @MethodAuthor pw, 2021年07月12日
     */
    private void changeIfNeedImproveForSubmitSubAction() {
        if (null != checkMain && null == checkMain.getIfNeedImprove()) {
            List<TdZwZkCheckTable> tmpCheckTableList = checkMain.getCheckTables();
            List<Integer> checkTableRidList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(tmpCheckTableList)) {
                for (TdZwZkCheckTable tmpCheckTable : tmpCheckTableList) {
                    if (tmpCheckTable.equals(curCheckTable)) {
                        continue;
                    }
                    if (null != tmpCheckTable.getFkByCheckTableId() && null != tmpCheckTable.getFkByCheckTableId().getRid()
                            && null != curCheckTable && null != curCheckTable.getFkByCheckTableId()
                            && null != curCheckTable.getFkByCheckTableId().getRid()
                            && curCheckTable.getFkByCheckTableId().getRid() == tmpCheckTable.getFkByCheckTableId().getRid().intValue()) {
                        continue;
                    }
                    if (null == tmpCheckTable.getStateMark() || 1 != tmpCheckTable.getStateMark()) {
                        return;
                    }
                    if (null != tmpCheckTable.getRid()) {
                        checkTableRidList.add(tmpCheckTable.getRid());
                    }
                }
            }

            List<TdZwZkCheckItem> tmpCheckItemList = curCheckTable.getCheckItems();
            if (!CollectionUtils.isEmpty(tmpCheckItemList)) {
                for (TdZwZkCheckItem tmpCheckItem : tmpCheckItemList) {
                    List<TdZwZkCheckSub> tmpCheckSubList = tmpCheckItem.getCheckSubList();
                    if (!CollectionUtils.isEmpty(tmpCheckSubList)) {
                        for (TdZwZkCheckSub tmpCheckSub : tmpCheckSubList) {
                            if (!CollectionUtils.isEmpty(tmpCheckSub.getCheckDeductList())) {
                                checkMain.setIfNeedImprove(1);
                                return;
                            }
                        }
                    }
                }
            }

            if (!CollectionUtils.isEmpty(checkTableRidList) &&
                    this.checkMainService.ifNeedImproveCheckQuery(checkTableRidList)) {
                checkMain.setIfNeedImprove(1);
            } else {
                checkMain.setIfNeedImprove(0);
            }
        }
    }

    /**
     * <p>Description：计算总得分 </p>
     * <p>Author： yzz 2025/6/6 </p>
     */
    private void countTatalScores(List<TdZwZkCheckTable> tmpCheckList) {
        // 未开展工作时，不需要计算总得分
        if (new Integer("0").equals(this.checkMain.getIfDevelop())) {
            this.checkMain.setTotalScoreVal(null);
            return;
        }
        // 计算总得分
        BigDecimal score = null;
        for (TdZwZkCheckTable checkTable : tmpCheckList) {
            if (new Integer("2").equals(checkTable.getStateMark())) {
                continue;
            }
            List<TdZwZkCheckItem> checkItems = checkTable.getCheckItems();
            if (ObjectUtil.isEmpty(checkItems)) {
                continue;
            }
            for (TdZwZkCheckItem checkItem : checkItems) {
                if (checkItem.getScoreVal() == null) {
                    continue;
                }
                if (null == score) {
                    score = BigDecimal.ZERO;
                }
                score = score.add(checkItem.getScoreVal());
            }
        }
        this.checkMain.setTotalScoreVal(score);
    }

    /**
     * <p>方法描述：最后提交的考核项目
     * 1、计算结果等级、合格标记、生成文书的档案编号、生成/更新【文书的汇总信息表】</p>
     *
     * @MethodAuthor qrr, 2021年9月18日, submitSubLastAction
     */
    private void submitSubLastAction() {
        //查询考核项目是否全部提交
        List<Object[]> list = checkMainService.findZkCheckTableState(this.checkMain.getRid());
        if (!CollectionUtils.isEmpty(list)) {
            boolean ifsubmit = true;
            for (Object[] obj : list) {
                if (null == obj[1]) {
                    continue;
                }
                if ("0".equals(obj[1].toString())) {
                    ifsubmit = false;
                }
            }
            if (ifsubmit) {
                //文书编号（年月日+3位流水号）
                if (StringUtils.isBlank(this.checkMain.getCheckNo())) {
                    String autoCode = this.commService.getAutoCode("HETH_ZK_CHECK_RST_CODE", null);
                    this.checkMain.setCheckNo(DateUtils.getDate("yyyyMMdd") + autoCode);
                }
                //考核结果汇总信息
                List<Object[]> checkSubSummarys = this.checkMainService.findCheckSubSummary(this.checkMain.getRid());
                List<TdZwZkCheckSummary> summaries = new ArrayList<>();
                if (!CollectionUtils.isEmpty(checkSubSummarys)) {
                    for (Object[] obj : checkSubSummarys) {
                        if (null == obj[0] || null == obj[1]) {
                            continue;
                        }
                        TdZwZkCheckSummary summary = new TdZwZkCheckSummary();
                        summary.setFkByMainId(this.checkMain);
                        summary.setFkByItemTypeId(new TsSimpleCode(new Integer(obj[0].toString())));
                        summary.setFkByScoreRstId(new TsSimpleCode(new Integer(obj[1].toString())));
                        if (null == obj[2]) {
                            summary.setItemNum(0);
                        } else {
                            summary.setItemNum(new Integer(obj[2].toString()));
                        }
                        summaries.add(summary);
                    }
                }
                //全部提交，根据规则计算等级、是否合格
                List<TbZwZkRstRule> rules = rstRuleService.findTbZwZkRstRule(this.checkMain.getFkByCheckTypeId().getRid());
                if (!CollectionUtils.isEmpty(rules)) {
                    TbZwZkRstRule rule = matchRule(rules, checkSubSummarys);
                    if (null != rule && null != rule.getFkByCheckGrageId()) {
                        this.checkMain.setFkByCheckRstId(rule.getFkByCheckGrageId());
                        this.checkMain.setIfHg(rule.getFkByCheckGrageId().getExtendS1());
                    }
                }
                //保存
                this.checkMainService.saveCheckSummary(checkMain, summaries);
            }
        }
    }

    /**
     * <p>方法描述：匹配规则</p>
     *
     * @MethodAuthor qrr, 2021年9月19日, matchRule
     */
    private TbZwZkRstRule matchRule(List<TbZwZkRstRule> rules, List<Object[]> checkSubSummarys) {
        Map<String, Object[]> summaryMap = new HashMap<>();
        Map<Integer, BigDecimal> itemTypeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(checkSubSummarys)) {
            for (Object[] obj : checkSubSummarys) {
                if (null == obj[0] || null == obj[1]) {
                    continue;
                }
                String key = obj[0] + "&" + obj[1];
                if (null == summaryMap.get(key)) {
                    summaryMap.put(key, obj);
                }
                Integer itemTypeId = new Integer(obj[0].toString());
                if (null == itemTypeMap.get(itemTypeId)) {
                    itemTypeMap.put(itemTypeId, new BigDecimal(obj[2].toString()));
                } else {
                    BigDecimal current = null;
                    current = new BigDecimal(obj[2].toString());
                    BigDecimal val = itemTypeMap.get(itemTypeId);
                    BigDecimal rst = val.add(current);
                    itemTypeMap.put(itemTypeId, rst);
                }
            }
        }
        for (TbZwZkRstRule t : rules) {
            //匹配规则
            List<TbZwZkRstRuleSub> rstRuleSubs = t.getRstRuleSubs();
            if (!CollectionUtils.isEmpty(rstRuleSubs)) {
                boolean ifmatch = true;
                for (TbZwZkRstRuleSub ruleSub : rstRuleSubs) {
                    TsSimpleCode itemTypeId = ruleSub.getFkByItemTypeId();
                    TsSimpleCode scoreRstId = ruleSub.getFkByScoreRstId();
                    TsSimpleCode scoreRst2Id = ruleSub.getFkByScoreRst2Id();
                    if (null == itemTypeId || null == scoreRstId) {
                        continue;
                    }
                    String key = itemTypeId.getRid() + "&" + scoreRstId.getRid();
                    Object[] obj = summaryMap.get(key);
                    Object[] obj2 = null;
                    if (null != scoreRst2Id) {
                        String key2 = itemTypeId.getRid() + "&" + scoreRst2Id.getRid();
                        obj2 = summaryMap.get(key2);
                    }
                    BigDecimal divide = new BigDecimal(0);//默认为0
                    BigDecimal total = itemTypeMap.get(itemTypeId.getRid());
                    if (null != total) {
                        BigDecimal val = new BigDecimal(0);
                        if (null != obj) {
                            val = val.add(new BigDecimal(obj[2].toString()));
                        }
                        if (null != obj2) {
                            val = val.add(new BigDecimal(obj2[2].toString()));
                        }
                        val = val.multiply(new BigDecimal(100));
                        if (val.intValue() > 0 && total.intValue() > 0) {
                            divide = val.divide(total, RoundingMode.HALF_UP);
                        }
                    }
                    Integer calTag = ruleSub.getCalTag();
                    Integer calNum = ruleSub.getCalNum();
                    if (null == calTag || null == calNum) {
                        continue;
                    }
                    if (1 == calTag.intValue()) {
                        if (divide.intValue() < calNum.intValue()) {
                            ifmatch = false;//存在未匹配到，则匹配下一个等级
                            break;
                        }
                    } else if (2 == calTag.intValue()) {
                        if (calNum.intValue() != divide.intValue()) {
                            ifmatch = false;//存在未匹配到，则匹配下一个等级
                            break;
                        }
                    } else if (3 == calTag.intValue()) {
                        if (divide.intValue() >= calNum.intValue()) {
                            ifmatch = false;//存在未匹配到，则匹配下一个等级
                            break;
                        }
                    } else if (4 == calTag.intValue()) {
                        if (divide.intValue() <= calNum.intValue()) {
                            ifmatch = false;//存在未匹配到，则匹配下一个等级
                            break;
                        }
                    } else if (5 == calTag.intValue()) {
                        if (divide.intValue() > calNum.intValue()) {
                            ifmatch = false;//存在未匹配到，则匹配下一个等级
                            break;
                        }
                    }
                }
                if (ifmatch) {//全部匹配成功，得到考核等级
                    return t;
                }
            }
        }
        return null;
    }

    /**
     * <p>方法描述：撤销验证 </p>
     * pw 2025/6/16
     **/
    public void cancelValidate() {
        if (null == this.checkMain || null == this.checkMain.getRid()) {
            return;
        }
        //判断考核表是否已提交，如果已提交，提示
        String sql ="select WRITE_PATH from TD_ZW_ZK_CHECK_MAIN where rid="+this.checkMain.getRid();
        List<Object> result = this.checkMainService.findDataBySqlNoPage(sql, null);
        if(CollectionUtils.isEmpty(result) || null != result.get(0)){
            JsfUtil.addErrorMessage("状态已发生改变，请刷新页面！");
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('CancelConfirmDialog').show()");
    }

    /**
     * @Description: 编辑页二撤销
     * @MethodAuthor pw, 2021年07月7日
     */
    public void cancelSubAction() {
        if (null == curCheckTable || curCheckTable.getRid()==null || this.checkMain==null || this.checkMain.getRid()==null) {
            return;
        }
        try {
            //更新 当前考核表的状态
            this.checkMainService.cancelCheckTable(curCheckTable);
            curCheckTable.setStateMark(0);
            modInit();
            JsfUtil.addSuccessMessage("撤销成功！");
            this.forwardEdit2Page();
            RequestContext.getCurrentInstance().update("tabView");
            RequestContext.getCurrentInstance().scrollTo("tabView:editForm2:editTitleGrid2");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
    }

    /**
     * @Description: 编辑页二返回
     * @MethodAuthor pw, 2021年07月7日
     */
    public void backSubAction(Integer index) {
        this.rid = this.checkMain.getRid();
        if (null != this.checkMain && null != this.checkMain.getStateMark() && 1 == this.checkMain.getStateMark()) {
            index = null;
        }
        if (null != index && 1 == index) {
            this.forwardEditPage();
        } else {
            this.forwardViewPage();
        }
        RequestContext.getCurrentInstance().update("tabView");
    }


    /**
     * @Description: 初始化存在问题
     * @MethodAuthor pw, 2021年07月8日
     */
    public void initDeductCause(TdZwZkCheckSub curTranCheckSub, Integer itemIndex, Integer subIndex) {
        curCheckSub = curTranCheckSub;
        deductQuery = null;
        allScoreDeductList = new ArrayList<>();
        showScoreDeductList = new ArrayList<>();
        deductUpdateId = null;
        if (null == curCheckSub || null == itemIndex || null == subIndex) {
            return;
        }

        // 加载所有存在问题
        allScoreDeductList = null == curTranCheckSub.getFkByScoreId() ? null : curTranCheckSub.getFkByScoreId().getDeductList();
        if (!CollectionUtils.isEmpty(allScoreDeductList)) {
            sortScoreDeductList(allScoreDeductList);

            // 确保所有选项初始化为未选中状态
            for (TbZwZkScoreDeduct scoreDeduct : allScoreDeductList) {
                if (null == scoreDeduct.getRid() || null == scoreDeduct.getFkByDeductId() ||
                        null == scoreDeduct.getFkByDeductId().getRid()) {
                    continue;
                }
                scoreDeduct.setSelected(false);
                // 将所有项添加到 showScoreDeductList 进行首次显示
                showScoreDeductList.add(scoreDeduct);
            }
            sortScoreDeductList(showScoreDeductList);
        }

        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm2:selectedDeductCauseTable");
        dataTable.setFirst(0);

        deductUpdateId = "tabView:editForm2:deductCauseNames" + itemIndex.intValue() + "PK" + subIndex.intValue();
        RequestContext.getCurrentInstance().update("tabView:editForm2:deductCauseDialog");
        RequestContext.getCurrentInstance().execute("PF('DeductCauseDialog').show();");
    }


    /**
     * @Description: 清空存在问题
     * @MethodAuthor pw, 2021年07月8日
     */
    public void clearDeductCause(TdZwZkCheckSub curTranCheckSub) {
        if (null == curTranCheckSub) {
            return;
        }
        curTranCheckSub.setDeductRsn(null);
    }

    /**
     * @Description: 过滤存在问题列表
     * @MethodAuthor pw, 2021年07月8日
     */
    public void executeQueryDeduct() {
        showScoreDeductList = new ArrayList<>();
        if (CollectionUtils.isEmpty(allScoreDeductList)) {
            return;
        }
        sortScoreDeductList(allScoreDeductList);
        for (TbZwZkScoreDeduct zkCheckDeduct : allScoreDeductList) {
            if (null == zkCheckDeduct.getFkByDeductId() ||
                    StringUtils.isBlank(zkCheckDeduct.getFkByDeductId().getCodeName())) {
                continue;
            }
            if (StringUtils.isNotBlank(deductQuery) && !zkCheckDeduct.getFkByDeductId().getCodeName()
                    .contains(deductQuery.trim())) {
                continue;
            }
            showScoreDeductList.add(zkCheckDeduct);
        }
        sortScoreDeductList(showScoreDeductList);
    }


    /**
     * @Description: 存在问题确认
     * @MethodAuthor pw, 2021年07月8日
     */
    public void surePickDeductCause() {
        if (null == curCheckSub) {
            return;
        }
        StringBuffer deductRsn = new StringBuffer();
        if (StringUtils.isNotBlank(curCheckSub.getDeductRsn())) {
            deductRsn.append(curCheckSub.getDeductRsn());
        }
        // 遍历allScoreDeductList来获取所有选中的问题
        boolean isSelected = false;
        if (!CollectionUtils.isEmpty(allScoreDeductList)) {
            for (TbZwZkScoreDeduct deduct : allScoreDeductList) {
                if (deduct.isSelected()) {
                    isSelected = true;
                    if (deductRsn.length() > 0) {
                        deductRsn.append("；");
                    }
                    deductRsn.append(deduct.getFkByDeductId().getCodeName());
                }
            }
        }
        if (!isSelected) {
            JsfUtil.addErrorMessage("请选择存在问题！");
            return;
        }

        // 如果长度超过2000，直接截取
        if (deductRsn.length() > 2000) {
            deductRsn.setLength(2000);
        }

        curCheckSub.setDeductRsn(deductRsn.toString());
        RequestContext.getCurrentInstance().update(deductUpdateId);
        RequestContext.getCurrentInstance().execute("PF('DeductCauseDialog').hide();");
    }

    /**
     * @Description: 专业技术人员总数 中高级技术人员数 外聘人员数调整后
     * 修改中高级技术人员占比 以及 外聘人员占比
     * @MethodAuthor pw, 2021年07月9日
     */
    public void changeSubScore(TdZwZkCheckSub tdZwZkCheckSub) {
        if (null == tdZwZkCheckSub) {
            return;
        }
        Integer techNum = tdZwZkCheckSub.getTechPsn();
        Integer mediumNum = tdZwZkCheckSub.getMediumPsn();
        Integer externalNum = tdZwZkCheckSub.getExternalPsn();
        if (null == techNum) {
            tdZwZkCheckSub.setMediumPsnRate(null);
            tdZwZkCheckSub.setExternalPsnRate(null);
            return;
        }
        BigDecimal techNumDe = new BigDecimal(techNum);
        if (null == mediumNum) {
            tdZwZkCheckSub.setMediumPsnRate(null);
        } else {
            if (mediumNum.intValue() > techNum) {
                tdZwZkCheckSub.setMediumPsnRate(null);
                JsfUtil.addErrorMessage("中高级技术人员数应小于等于专业技术人员总数！");
            } else if (0 == techNum) {
                tdZwZkCheckSub.setMediumPsnRate(null);
            } else if (0 == mediumNum) {
                tdZwZkCheckSub.setMediumPsnRate(BigDecimal.ZERO);
            } else {
                BigDecimal mediumNumDe = new BigDecimal(mediumNum);
                BigDecimal decimal = mediumNumDe.multiply(new BigDecimal(100))
                        .divide(techNumDe, 2, RoundingMode.HALF_UP);
                tdZwZkCheckSub.setMediumPsnRate(clearPointVal(decimal, 2));
            }
        }
        if (null == externalNum) {
            tdZwZkCheckSub.setExternalPsnRate(null);
        } else {
            if (externalNum.intValue() > techNum) {
                tdZwZkCheckSub.setExternalPsnRate(null);
                JsfUtil.addErrorMessage("外聘人员数应小于等于专业技术人员总数！");
            } else if (0 == techNum) {
                tdZwZkCheckSub.setExternalPsnRate(null);
            } else if (0 == externalNum) {
                tdZwZkCheckSub.setExternalPsnRate(BigDecimal.ZERO);
            } else {
                BigDecimal externalDe = new BigDecimal(externalNum);
                BigDecimal decimal = externalDe.multiply(new BigDecimal(100))
                        .divide(techNumDe, 2, RoundingMode.HALF_UP);
                tdZwZkCheckSub.setExternalPsnRate(clearPointVal(decimal, 2));
            }
        }
    }


    /**
     * @Description: 依据结果子表实得分更新评估考核项目的实得分
     * @MethodAuthor pw, 2021年07月9日
     */
    @Deprecated
    public void changeItemScore(TdZwZkCheckItem tdZwZkCheckItem) {
        if (null == tdZwZkCheckItem) {
            return;
        }
        List<TdZwZkCheckSub> subList = tdZwZkCheckItem.getCheckSubList();
        BigDecimal decimal = null;
        if (!CollectionUtils.isEmpty(subList)) {
            Integer ifDevlop = this.checkMain.getIfDevelop();
            for (TdZwZkCheckSub sub : subList) {
                if (sub.getFkByScoreId() == null && !new Integer("0").equals(sub.getAssessMark())) {
                    continue;
                }
                sub.setIfShowRedStar(false);
                // 单选
                if (!CollectionUtils.isEmpty(sub.getFkByScoreId().getScoreOptionList())) {

                    Map<Integer, TbZwZkScoreOption> optionMap = new HashMap<>();
                    for (TbZwZkScoreOption tbZwZkScoreOption : sub.getFkByScoreId().getScoreOptionList()) {
                        optionMap.put(tbZwZkScoreOption.getRid(), tbZwZkScoreOption);
                    }
                    if (sub.getRstRid() != null && optionMap.containsKey(sub.getRstRid())) {
                        TbZwZkScoreOption tbZwZkScoreOption = optionMap.get(sub.getRstRid());
                        //判断存在问题是否显示必填标记
                        if ("2".equals(tbZwZkScoreOption.getFkByOptionId().getExtendS1()) || "3".equals(tbZwZkScoreOption.getFkByOptionId().getExtendS1()) || "6".equals(tbZwZkScoreOption.getFkByOptionId().getExtendS1())) {
                            sub.setIfShowRedStar(true);
                        }
                        if (tbZwZkScoreOption.getCheckVal() == null || tbZwZkScoreOption.getFkByOptionId() == null) {
                            continue;
                        }
                        if (null != ifDevlop && 0 == ifDevlop) {
                            sub.setScoreVal(null);
                            continue;
                        }
                        decimal = decimal == null ? tbZwZkScoreOption.getCheckVal() : decimal.add(tbZwZkScoreOption.getCheckVal());
                        sub.setScoreVal(tbZwZkScoreOption.getCheckVal());
                    }
                } else {
                    //录入
                    BigDecimal subScore = sub.getScoreVal();
                    if (null != subScore) {
                        if (null == decimal) {
                            decimal = subScore;
                        } else {
                            decimal = decimal.add(subScore);
                        }
                    }
                    //判断存在问题是否显示必填标记
                    if (sub.getScoreVal() != null && sub.getFkByScoreId() != null
                            && sub.getFkByScoreId().getScore() != null
                            && sub.getScoreVal().compareTo(sub.getFkByScoreId().getScore()) < 0) {
                        sub.setIfShowRedStar(true);
                    }
                }
            }
        }
        tdZwZkCheckItem.setScoreVal(decimal);
    }


    /**
     * @Description: 初始化评分项结果码表
     * @MethodAuthor pw, 2021年09月22日
     */
    public void initScoreRstInfo() {
        this.scoreRstSimpleList = this.commService.findallSimpleCodesByTypeIdOrderByNum("5534");
        this.scoreRstSimpleMap = new HashMap<>();
        this.soreRstSimpleMapWithItemTypeCode = new HashMap<>();
        if (CollectionUtils.isEmpty(this.scoreRstSimpleList)) {
            this.scoreRstSimpleList = new ArrayList<>();
            return;
        }
        List<TsSimpleCode> removeSimpleList = new ArrayList<>();
        for (TsSimpleCode simpleCode : this.scoreRstSimpleList) {
            this.scoreRstSimpleMap.put(simpleCode.getRid(), simpleCode);
            if (null == simpleCode.getIfReveal() || 1 != simpleCode.getIfReveal()) {
                removeSimpleList.add(simpleCode);
            } else if (null != simpleCode.getExtendS1() && simpleCode.getExtendS1().trim().equals("1")) {
                this.hgSoreRstRid = simpleCode.getRid();
            }
            if ((null != simpleCode.getIfReveal() || 1 == simpleCode.getIfReveal()) && StringUtils.isNotBlank(simpleCode.getExtendS3())) {
                String[] codeArr = simpleCode.getExtendS3().split(",");
                for (String itemCode : codeArr) {
                    List<TsSimpleCode> tmpList = soreRstSimpleMapWithItemTypeCode.get(itemCode.trim());
                    if (null == tmpList) {
                        tmpList = new ArrayList<>();
                    }
                    tmpList.add(simpleCode);
                    soreRstSimpleMapWithItemTypeCode.put(itemCode.trim(), tmpList);
                }
            }
        }
        if (!CollectionUtils.isEmpty(removeSimpleList)) {
            this.scoreRstSimpleList.removeAll(removeSimpleList);
        }
    }

    /**
     * @Description: 结果子表实得分失焦 验证失败提示
     * @MethodAuthor pw, 2021年07月9日
     */
    @Deprecated
    public void showScoreErrorMessage() {
        JsfUtil.addErrorMessage("实得分应大于等于0并且小于等于评估项分值！");
    }


    /**
     * @Description: BigDecimal 去除小数点后多余的0
     * @MethodAuthor pw, 2021年07月9日
     */
    private BigDecimal clearPointVal(BigDecimal decimal, int scale) {
        if (0 == scale || null == decimal) {
            return decimal;
        }
        for (int scaleNum = 0; scaleNum < scale; scaleNum++) {
            BigDecimal tmpDecimal = decimal.setScale((scale - scaleNum - 1),
                    BigDecimal.ROUND_HALF_UP);
            if (tmpDecimal.compareTo(decimal) == 0) {
                decimal = tmpDecimal;
            } else {
                break;
            }
        }
        return decimal;
    }

    /**
     * @Description: 编辑页二或者详情页二初始化
     * @MethodAuthor pw, 2021年07月7日
     */
    private void initSubAction() {
        indexDescMap = new HashMap<>();
        zwZkCheckItemList = new ArrayList<>();
        if (null == curCheckTable) {
            return;
        }
        //评估表id是否存在
        if (null != curCheckTable.getRid()) {
            //查询评估表
            curCheckTable = this.checkMainService.findTdZwCheckTable(curCheckTable.getRid());
        } else {
            List<TdZwZkCheckItem> list = new ArrayList<>();
            try {
                for (TdZwZkCheckItem checkItem : curCheckTable.getCheckItems()) {
                    TdZwZkCheckItem tmpCheckItem = new TdZwZkCheckItem();
                    ObjectCopyUtil.copyProperties(checkItem, tmpCheckItem);
                    list.add(tmpCheckItem);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            TbZwZkBadrsnStand tmpStand = curCheckTable.getFkByCheckTableId();
            //不存在，new新的评估表对象
            curCheckTable = new TdZwZkCheckTable();
            curCheckTable.setCheckItems(list);
            curCheckTable.setFkByCheckTableId(tmpStand);
        }

        zwZkCheckItemList = curCheckTable.getCheckItems();
        if (CollectionUtils.isEmpty(zwZkCheckItemList)) {
            return;
        }
        sortCheckItemList(zwZkCheckItemList);
        List<Integer> ridList = new ArrayList<>();
        for (TdZwZkCheckItem checkItem : zwZkCheckItemList) {
            if (null == checkItem.getRid()) {
                break;
            }
            ridList.add(checkItem.getRid().intValue());
        }
        //查询 考核项目下是否题目
        Map<Integer, List<TdZwZkCheckSub>> checkSubListMap = checkMainService.findTdZwZkCheckSubListByMainRidsForMapResult(ridList);
        boolean flag = false;
        if (CollectionUtils.isEmpty(checkSubListMap)) {
            flag = true;
        }
        fillItemScoresMap(flag);
        if (CollectionUtils.isEmpty(itemScoresMap)) {
            return;
        }
        for (TdZwZkCheckItem checkItem : zwZkCheckItemList) {
            if (!CollectionUtils.isEmpty(checkSubListMap)) {
                //已存在的记录
                List<TdZwZkCheckSub> tmpCheckSubList = checkSubListMap.get(checkItem.getRid());
                if (!CollectionUtils.isEmpty(tmpCheckSubList)) {
                    sortCheckSubList(tmpCheckSubList);
                    initChechSubAssessMark(tmpCheckSubList);
                    //初始化存在问题必填标记
                    for (TdZwZkCheckSub sub : tmpCheckSubList) {
                        if (sub.getRid() == null) {
                            continue;
                        }
                        // 单选
                        if (!CollectionUtils.isEmpty(sub.getFkByScoreId().getScoreOptionList())) {
                            Map<Integer, TbZwZkScoreOption> optionMap = new HashMap<>();
                            for (TbZwZkScoreOption tbZwZkScoreOption : sub.getFkByScoreId().getScoreOptionList()) {
                                optionMap.put(tbZwZkScoreOption.getRid(), tbZwZkScoreOption);
                            }
                            if (sub.getFkByRstId() != null && sub.getFkByRstId().getRid() != null
                                    && optionMap.containsKey(sub.getFkByRstId().getRid())) {
                                TbZwZkScoreOption tbZwZkScoreOption = optionMap.get(sub.getFkByRstId().getRid());
                                //判断存在问题是否显示必填标记
                                if ("2".equals(tbZwZkScoreOption.getFkByOptionId().getExtendS1()) || "3".equals(tbZwZkScoreOption.getFkByOptionId().getExtendS1()) || "6".equals(tbZwZkScoreOption.getFkByOptionId().getExtendS1())) {
                                    sub.setIfShowRedStar(true);
                                }
                            }
                        } else {
                            //判断存在问题是否显示必填标记
                            if (sub.getScoreVal() != null && sub.getFkByScoreId() != null
                                    && sub.getFkByScoreId().getScore() != null
                                    && sub.getScoreVal().compareTo(sub.getFkByScoreId().getScore()) < 0) {
                                sub.setIfShowRedStar(true);
                            }
                        }
                    }
                    checkItem.setCheckSubList(tmpCheckSubList);
                }

                if (CollectionUtils.isEmpty(tmpCheckSubList)) {
                    continue;
                }
                for (TdZwZkCheckSub checkSub : tmpCheckSubList) {
                    if (null != checkSub.getFkByRstId() && checkSub.getFkByRstId().getRid() != null) {
                        checkSub.setRstRid(checkSub.getFkByRstId().getRid());
                    }
                }
                List<TbZwZkScores> scoresList = itemScoresMap.get(checkItem.getFkByItemId().getRid());
                if (!CollectionUtils.isEmpty(scoresList)) {
                    Map<Integer, TbZwZkScores> scoreMap = new HashMap<>();
                    for (TbZwZkScores zwZkScores : scoresList) {
                        scoreMap.put(zwZkScores.getRid().intValue(), zwZkScores);
                    }
                    for (TdZwZkCheckSub checkSub : tmpCheckSubList) {
                        if (null != checkSub.getFkByScoreId() && null != checkSub.getFkByScoreId().getRid()) {
                            TbZwZkScores tmpZkScores = scoreMap.get(checkSub.getFkByScoreId().getRid().intValue());
                            //重新赋值质控指标分值维护的评分项维护列表以及存在问题维护列表 避免出现懒加载异常
                            if (null != tmpZkScores) {
                                //checkSub.getFkByScoreId().setDeductList(tmpZkScores.getDeductList());
                                checkSub.getFkByScoreId().setIndexList(tmpZkScores.getIndexList());
                            }
                            if (null != tmpZkScores && StringUtils.isNotBlank(tmpZkScores.getIndexStr())) {
                                indexDescMap.put(tmpZkScores.getRid(), indexStrMixIndent(tmpZkScores.getIndexStr()));
                            }
                        }
                    }
                }
            } else {
                List<TbZwZkScores> scoresList = itemScoresMap.get(checkItem.getFkByItemId().getRid());
                if (!CollectionUtils.isEmpty(scoresList)) {
                    List<TdZwZkCheckSub> tmpCheckSub = new ArrayList<>();
                    for (TbZwZkScores zwZkScores : scoresList) {
                        TdZwZkCheckSub checkSub = new TdZwZkCheckSub();
                        checkSub.setFkByMainId(checkItem);
                        checkSub.setFkByScoreId(zwZkScores);
                        checkSub.setCreateDate(new Date());
                        checkSub.setCreateManid(Global.getUser().getRid());
                        checkSub.setCheckDeductList(new ArrayList<TdZwZkCheckDeduct>());
                        //checkSub.setRmk(initRmk(checkSub));
                        tmpCheckSub.add(checkSub);
                        if (null != zwZkScores && StringUtils.isNotBlank(zwZkScores.getIndexStr())) {
                            indexDescMap.put(zwZkScores.getRid(), indexStrMixIndent(zwZkScores.getIndexStr()));
                        }
                    }
                    checkItem.setCheckSubList(tmpCheckSub);
                    initChechSubAssessMark(tmpCheckSub);
                }
            }
        }
    }

    /**
     * <p>Description：初始化子表的"无需考核"标记 </p>
     * <p>Author： yzz 2025/6/5 </p>
     */
    public void initChechSubAssessMark(List<TdZwZkCheckSub> checkSubList) {
        if (CollectionUtils.isEmpty(checkSubList)) {
            return;
        }

        // 初始化大类、小类集合
        Set<String> bigTypeSet = new HashSet<>();
        Set<String> smallTypeSet = new HashSet<>();

        // 收集所有的大类和小类编码
        collectTypeCodes(bigTypeSet, smallTypeSet);

        // 处理每个考核子项
        for (TdZwZkCheckSub checkSub : checkSubList) {
            //业务数据存储时读取业务数据
            if(checkSub.getRid()!=null){
                continue;
            }
            processCheckSub(checkSub, bigTypeSet, smallTypeSet);
        }
    }

    /**
     * 收集所有的大类和小类编码
     */
    private void collectTypeCodes(Set<String> bigTypeSet, Set<String> smallTypeSet) {
        if (CollectionUtils.isEmpty(this.checkMain.getCheckRecords())) {
            return;
        }

        for (TdZwZkCheckRecord checkRecord : this.checkMain.getCheckRecords()) {
            // 收集大类编码
            if (checkRecord.getFkByTypeId() != null && StringUtils.isNotBlank(checkRecord.getFkByTypeId().getCodeNo())) {
                bigTypeSet.add(checkRecord.getFkByTypeId().getCodeNo());
            }

            // 收集小类编码
            if (!CollectionUtils.isEmpty(checkRecord.getTdZwZkCheckDetails())) {
                for (TdZwZkCheckDetails checkDetail : checkRecord.getTdZwZkCheckDetails()) {
                    if (checkDetail.getFkByItemId() != null && StringUtils.isNotBlank(checkDetail.getFkByItemId().getCodeNo())) {
                        smallTypeSet.add(checkDetail.getFkByItemId().getCodeNo());
                    }
                }
            }
        }
    }

    /**
     * 处理单个考核子项
     */
    private void processCheckSub(TdZwZkCheckSub checkSub, Set<String> bigTypeSet, Set<String> smallTypeSet) {
        // 检查评分项是否存在
        if (checkSub.getFkByScoreId() == null || CollectionUtils.isEmpty(checkSub.getFkByScoreId().getIndexList())) {
            return;
        }

        // 默认标记为0
        checkSub.setAssessMark(0);

        // 先检查是否无此备案类别
        checkNoRecordType(checkSub, bigTypeSet, smallTypeSet);

        // 如果已经标记为无此备案类别（assessMark=2），则不再检查是否未开展工作
        if (checkSub.getAssessMark() != 2 && Integer.valueOf(0).equals(this.checkMain.getIfDevelop())) {
            checkNotDeveloped(checkSub);
        }
    }

    /**
     * 检查是否未开展工作
     */
    private void checkNotDeveloped(TdZwZkCheckSub checkSub) {
        for (TbZwZkScoreIndex index : checkSub.getFkByScoreId().getIndexList()) {
            if (index.getFkByIndexId() != null && "1".equals(index.getFkByIndexId().getExtendS4())) {
                checkSub.setAssessMark(1);
                break;
            }
        }
    }

    /**
     * 检查是否无此备案类别
     */
    private void checkNoRecordType(TdZwZkCheckSub checkSub, Set<String> bigTypeSet, Set<String> smallTypeSet) {
        for (TbZwZkScoreIndex index : checkSub.getFkByScoreId().getIndexList()) {
            if (index.getFkByIndexId() == null || StringUtils.isBlank(index.getFkByIndexId().getExtendS5())) {
                continue;
            }

            String extendS5 = index.getFkByIndexId().getExtendS5();
            String[] typeGroups = extendS5.split(";");

            for (String typeGroup : typeGroups) {
                String[] parts = typeGroup.split("@");
                if (parts.length != 2) {
                    continue;
                }

                String typeCode = parts[0];
                String typeValues = parts[1];

                if (StringUtils.isBlank(typeValues)) {
                    continue;
                }

                String[] typeValueArray = typeValues.split(",");

                // 检查服务明细小类
                if ("5527".equals(typeCode)) {
                    if (!containsAny(smallTypeSet, typeValueArray)) {
                        checkSub.setAssessMark(2);
                        return;
                    }
                }
                // 检查服务明细大类
                else if ("5018".equals(typeCode)) {
                    if (!containsAny(bigTypeSet, typeValueArray)) {
                        checkSub.setAssessMark(2);
                        return;
                    }
                }
            }
        }
    }

    /**
     * 检查集合是否包含数组中的任何元素
     */
    private boolean containsAny(Set<String> set, String[] array) {
        for (String value : array) {
            if (set.contains(value)) {
                return true;
            }
        }
        return false;
    }


    /**
     * @Description: 增加首行缩进
     * @MethodAuthor pw, 2021年07月12日
     */
    private String indexStrMixIndent(String indexStr) {
        if (StringUtils.isBlank(indexStr)) {
            return null;
        }
        StringBuffer buffer = new StringBuffer();
        if (indexStr.indexOf("</br>") != -1) {
            String[] indexStrArr = indexStr.split("</br>");
            for (int i = 0; i < indexStrArr.length; i++) {
                buffer.append("<div style='text-indent: 2em;width:1220px;'>").append(indexStrArr[i]).append("</div>");
            }
        } else {
            buffer.append("<div style='text-indent: 2em;width:1220px;'>").append(indexStr).append("</div>");
        }
        return buffer.toString();
    }

    /**
     * @Description: TdZwZkCheckItem排序
     * @MethodAuthor pw, 2021年07月7日
     */
    private void sortCheckItemList(List<TdZwZkCheckItem> checkItemList) {
        if (CollectionUtils.isEmpty(checkItemList)) {
            return;
        }
        Collections.sort(checkItemList, new Comparator<TdZwZkCheckItem>() {
            @Override
            public int compare(TdZwZkCheckItem o1, TdZwZkCheckItem o2) {
                Integer num1 = null == o1.getFkByItemId() ? null : o1.getFkByItemId().getNum();
                Integer num2 = null == o2.getFkByItemId() ? null : o2.getFkByItemId().getNum();
                if (null != num1 && null != num2) {
                    return num1.compareTo(num2);
                } else if (null != num1) {
                    return 1;
                } else if (null != num2) {
                    return -1;
                }
                return 0;
            }
        });
    }

    /**
     * @Description: TdZwZkCheckSub 排序
     * @MethodAuthor pw, 2021年07月7日
     */
    private void sortCheckSubList(List<TdZwZkCheckSub> tmpCheckSubList) {
        if (CollectionUtils.isEmpty(tmpCheckSubList)) {
            return;
        }
        Collections.sort(tmpCheckSubList, new Comparator<TdZwZkCheckSub>() {
            @Override
            public int compare(TdZwZkCheckSub o1, TdZwZkCheckSub o2) {
                Integer xh1 = null != o1.getFkByScoreId() ? o1.getFkByScoreId().getXh() : null;
                Integer xh2 = null != o2.getFkByScoreId() ? o2.getFkByScoreId().getXh() : null;
                if (null != xh1 && null != xh2) {
                    return xh1.compareTo(xh2);
                } else if (null != xh1) {
                    return 1;
                } else if (null != xh2) {
                    return -1;
                }
                return 0;
            }
        });
    }


    /**
     * @Description: 对评估表进行排序
     * @MethodAuthor pw, 2021年07月14日
     */
    private void sortCheckTableList(List<TdZwZkCheckTable> checkTableList) {
        if (CollectionUtils.isEmpty(checkTableList)) {
            return;
        }
        Collections.sort(checkTableList, new Comparator<TdZwZkCheckTable>() {
            @Override
            public int compare(TdZwZkCheckTable o1, TdZwZkCheckTable o2) {
                Integer num1 = null == o1.getFkByCheckTableId() ? null : o1.getFkByCheckTableId().getXh();
                Integer num2 = null == o2.getFkByCheckTableId() ? null : o2.getFkByCheckTableId().getXh();
                if (null != num1 && null != num2) {
                    return num1.compareTo(num2);
                } else if (null != num1) {
                    return 1;
                } else if (null != num2) {
                    return -1;
                }
                return 0;
            }
        });
    }

    private void forwardSubPage() {
        initSubAction();
        if (null != curCheckTable.getStateMark() && 1 == curCheckTable.getStateMark()) {
            this.forwardEdit3Page();
        } else {
            this.forwardEdit2Page();
        }
        RequestContext.getCurrentInstance().update("tabView");
    }

    private void fillItemScoresMap(boolean flag) {
        this.soreRstMap = new HashMap<>();
        this.optionMap = new HashMap<>();
        if (null == this.checkMain || null == this.checkMain.getFkByCheckTypeId()) {
            return;
        }
        List<TbZwZkScores> list = standService.findTbZwZkScoresListByParams(this.checkMain.getFkByCheckTypeId().getRid(), flag);
        if (!CollectionUtils.isEmpty(list)) {
            for (TbZwZkScores zwZkScores : list) {
              /*  List<TbZwZkScoreDeduct> tmpList = zwZkScores.getDeductList();
                if (!CollectionUtils.isEmpty(tmpList)) {
                    List<TbZwZkScoreDeduct> removeList = new ArrayList<>();
                    for (TbZwZkScoreDeduct tmpScoreDeduct : tmpList) {
                        if (null != tmpScoreDeduct.getFkByDeductId()
                                && null != tmpScoreDeduct.getFkByDeductId().getIfReveal()
                                && 1 != tmpScoreDeduct.getFkByDeductId().getIfReveal().intValue()) {
                            removeList.add(tmpScoreDeduct);
                        }
                    }
                    if (!CollectionUtils.isEmpty(removeList)) {
                        zwZkScores.getDeductList().removeAll(removeList);
                    }
                }*/
                pakShowScoreOptionList(zwZkScores);
            }
        }
        initItemScoresMap(list);
    }

    /**
     * 封装题目结果项及分值映射
     *
     * @param zwZkScores 题目
     */
    private void pakShowScoreOptionList(TbZwZkScores zwZkScores) {
        if (this.soreRstMap.containsKey(zwZkScores.getRid())) {
            return;
        }

        List<TbZwZkScoreOption> showScoreOptionList = new ArrayList<>();
        this.soreRstMap.put(zwZkScores.getRid(), showScoreOptionList);

        List<TbZwZkScoreOption> scoreOptionList = zwZkScores.getScoreOptionList();
        if (!ObjectUtil.isNotEmpty(scoreOptionList)) {
            return;
        }
        // 按分值排序，空在后面
        Collections.sort(scoreOptionList, new Comparator<TbZwZkScoreOption>() {
            @Override
            public int compare(TbZwZkScoreOption o1, TbZwZkScoreOption o2) {
                if (ObjectUtil.isEmpty(o1.getFkByOptionId()) && ObjectUtil.isEmpty(o2.getFkByOptionId())) {
                    return CompareUtil.compareBigDecimal(o1.getCheckVal(), o2.getCheckVal());
                }
                int res1 = CompareUtil.compareNull(o1.getFkByOptionId(), o1.getFkByOptionId());
                if (res1 != 0) {
                    return res1;
                }
                int res2 = CompareUtil.compareInteger(o1.getFkByOptionId().getNum(), o2.getFkByOptionId().getNum());
                if (res2 != 0) {
                    return res2;
                }
                return CompareUtil.compareBigDecimal(o2.getCheckVal(), o1.getCheckVal());
            }
        });

        for (TbZwZkScoreOption scoreOption : scoreOptionList) {
            Integer optionRid = scoreOption.getRid();
            if (optionRid == null || scoreOption.getFkByOptionId() == null
                    || scoreOption.getFkByOptionId().getRid() == null) {
                continue;
            }
            this.optionMap.put(optionRid, scoreOption);
            //满分考核结果的set
            if ("1".equals(scoreOption.getFkByOptionId().getExtendS1()) || "5".equals(scoreOption.getFkByOptionId().getExtendS1())) {
                this.hgSoreRstRidSet.add(optionRid);
            }
            showScoreOptionList.add(scoreOption);
        }
    }

    /**
     * @Description: 初始化项目RID对应的评分项列表Map
     * @MethodAuthor pw, 2021年07月7日
     */
    private void initItemScoresMap(List<TbZwZkScores> list) {
        itemScoresMap = new HashMap<>();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (TbZwZkScores tbZwZkScores : list) {
            List<TbZwZkScoreIndex> tmpScoreIndexList = tbZwZkScores.getIndexList();
            //无评分项 无存在问题 跳过
            if (CollectionUtils.isEmpty(tmpScoreIndexList)) {
                continue;
            }
            TbZwZkScoreIndex scoreIndex = tmpScoreIndexList.get(0);
            if (null == scoreIndex.getFkByIndexId()) {
                continue;
            }
            String codeLevelNo = scoreIndex.getFkByIndexId().getCodeLevelNo();//指标层级编码
            String[] split = codeLevelNo.split("\\.");
            if (null == split || 0 == split.length) {
                continue;
            }
            TsSimpleCode item = this.itemMap.get(split[0]);//父级考核项目
            if (null == item) {
                continue;
            }
            List<TbZwZkScores> fillMapList = itemScoresMap.get(item.getRid().intValue());
            if (null == fillMapList) {
                fillMapList = new ArrayList<>();
            }
            fillMapList.add(tbZwZkScores);
            itemScoresMap.put(item.getRid().intValue(), fillMapList);
        }
    }

    /**
     * @Description: 编辑页二暂存验证
     * @MethodAuthor pw, 2021年07月9日
     */
    private boolean validateSaveSub() {
        boolean flag = true;
        if (null == curCheckTable) {
            return false;
        }
        String checkPsnStr = curCheckTable.getCheckPsn();
        if (StringUtils.isNotBlank(checkPsnStr) && checkPsnStr.indexOf(",") != -1) {
            JsfUtil.addErrorMessage("多个考核人需要以中文逗号隔开！");
            flag = false;
        }
        List<TdZwZkCheckItem> checkItemList = this.curCheckTable.getCheckItems();
        if (!CollectionUtils.isEmpty(checkItemList)) {
            for (TdZwZkCheckItem tmpCheckItem : checkItemList) {
                List<TdZwZkCheckSub> tmpCheckSubList = tmpCheckItem.getCheckSubList();
                String itemName = null == tmpCheckItem.getFkByItemId() ? null : tmpCheckItem.getFkByItemId().getCodeName();
                if (!CollectionUtils.isEmpty(tmpCheckSubList)) {
                    //验证实得分小于等于评估项分值并且大于等于0
                    for (TdZwZkCheckSub tmpCheckSub : tmpCheckSubList) {
                        //没有置"无需考核"标记的题目参与验证
                        if (!new Integer("0").equals(tmpCheckSub.getAssessMark())) {
                            continue;
                        }
                        String indexStr = tmpCheckSub.getFkByScoreId().getIndexStr();
                        if (StringUtils.isNotBlank(indexStr)) {
                            indexStr.replaceAll("</br>", "");
                        }
                        String tipStr = null == tmpCheckSub.getFkByScoreId() ? itemName : (itemName + " " + indexStr);

                        //区分检查机构和诊断机构
                        if (tmpCheckSub.getFkByScoreId() != null) {
                            //检查机构
                            if (!CollectionUtils.isEmpty(tmpCheckSub.getFkByScoreId().getScoreOptionList())) {
                                //结果 值
                                Integer scoreRstRid = tmpCheckSub.getRstRid();
                                //结果选择"符合"或者"有"时，存在问题要清空
                                if (!CollectionUtils.isEmpty(hgSoreRstRidSet) && scoreRstRid != null && hgSoreRstRidSet.contains(scoreRstRid) && StringUtils.isNotBlank(tmpCheckSub.getDeductRsn())) {
                                    JsfUtil.addErrorMessage(tipStr + "存在问题需要清空！");
                                    flag = false;
                                }
                                if (scoreRstRid != null) {
                                    tmpCheckSub.setFkByRstId(new TbZwZkScoreOption(scoreRstRid));
                                }
                            } else {
                                //诊断机构
                                BigDecimal scoreVal = tmpCheckSub.getScoreVal();
                                if (null != scoreVal) {
                                    if (scoreVal.compareTo(BigDecimal.ZERO) == -1) {
                                        JsfUtil.addErrorMessage(tipStr + "实得分必须大于等于0！");
                                        flag = false;
                                    }
                                    BigDecimal baseScoreVal = null == tmpCheckSub.getFkByScoreId() ? null :
                                            tmpCheckSub.getFkByScoreId().getScore();
                                    if (null != baseScoreVal && scoreVal.compareTo(baseScoreVal) == 1) {
                                        JsfUtil.addErrorMessage(tipStr + "实得分必须小于等于评估项分值！");
                                        flag = false;
                                    } else if (null != baseScoreVal && scoreVal.compareTo(baseScoreVal) == 0 &&
                                            StringUtils.isNotBlank(tmpCheckSub.getDeductRsn())) {
                                        JsfUtil.addErrorMessage(tipStr + "实得分等于评估项分值时，存在问题需要清空！");
                                        flag = false;
                                    }
                                }
                            }
                        }
                        if (StringUtils.isNotBlank(tmpCheckSub.getDeductRsn()) && tmpCheckSub.getDeductRsn().length() > 2000) {
                            JsfUtil.addErrorMessage(tipStr + "存在问题长度不能超过2000！");
                            flag = false;
                        }

                        if (StringUtils.isNotBlank(tmpCheckSub.getRmk()) && tmpCheckSub.getRmk().length() > 2000) {
                            JsfUtil.addErrorMessage(tipStr + "备注长度不能超过2000！");
                            flag = false;
                        }
                    }
                }
            }
        }
        return flag;
    }

    /**
     * @Description: 编辑页二提交验证
     * @MethodAuthor pw, 2021年07月9日
     */
    private boolean validateSubmitSub() {
        boolean flag = validateSaveSub();
        if (null == curCheckTable) {
            return false;
        }
        String checkPsnStr = curCheckTable.getCheckPsn();
        if (StringUtils.isBlank(checkPsnStr)) {
            JsfUtil.addErrorMessage("考核人不允许为空！");
            flag = false;
        }
        List<TdZwZkCheckItem> checkItemList = this.curCheckTable.getCheckItems();
        if (!CollectionUtils.isEmpty(checkItemList)) {
            for (TdZwZkCheckItem tmpCheckItem : checkItemList) {
                List<TdZwZkCheckSub> tmpCheckSubList = tmpCheckItem.getCheckSubList();
                String itemName = null == tmpCheckItem.getFkByItemId() ? null : tmpCheckItem.getFkByItemId().getCodeName();
                if (!CollectionUtils.isEmpty(tmpCheckSubList)) {
                    //验证实得分小于等于评估项分值并且大于等于0
                    for (TdZwZkCheckSub tmpCheckSub : tmpCheckSubList) {
                        if (!new Integer("0").equals(tmpCheckSub.getAssessMark())) {
                            continue;
                        }
                        String indexStr = tmpCheckSub.getFkByScoreId().getIndexStr();
                        if (StringUtils.isNotBlank(indexStr)) {
                            indexStr.replaceAll("</br>", "");
                        }
                        String tipStr = null == tmpCheckSub.getFkByScoreId() ? itemName : (itemName + " " + indexStr);

                        //区分检查机构和诊断机构
                        if (tmpCheckSub.getFkByScoreId() != null) {
                            //检查机构
                            if (!CollectionUtils.isEmpty(tmpCheckSub.getFkByScoreId().getScoreOptionList())) {
                                //结果 值
                                Integer scoreRstRid = tmpCheckSub.getRstRid();
                                if (null == scoreRstRid) {
                                    JsfUtil.addErrorMessage(tipStr + "请选择结果！");
                                    flag = false;
                                } else if (!CollectionUtils.isEmpty(hgSoreRstRidSet) && scoreRstRid != null && !hgSoreRstRidSet.contains(scoreRstRid) && StringUtils.isBlank(tmpCheckSub.getDeductRsn())) {
                                    //结果选择非"符合"或者非"有"时，存在问题不能为空
                                    JsfUtil.addErrorMessage(tipStr + "存在问题不允许为空！");
                                    flag = false;
                                }
                                if (scoreRstRid != null) {
                                    tmpCheckSub.setFkByRstId(new TbZwZkScoreOption(scoreRstRid));
                                }
                            } else {
                                //诊断机构
                                BigDecimal scoreVal = tmpCheckSub.getScoreVal();
                                BigDecimal baseScoreVal = null == tmpCheckSub.getFkByScoreId() ? null :
                                        tmpCheckSub.getFkByScoreId().getScore();
                                //"本年度是否开展工作"为"是"时，实得分必填
                                if (new Integer("1").equals(this.checkMain.getIfDevelop()) && null == scoreVal) {
                                    JsfUtil.addErrorMessage(tipStr + "实得分不允许为空！");
                                    flag = false;
                                }
                                if (null != baseScoreVal && null != scoreVal && baseScoreVal.compareTo(scoreVal) == 1 &&
                                        StringUtils.isBlank(tmpCheckSub.getDeductRsn())) {
                                    JsfUtil.addErrorMessage(tipStr + "实得分小于评估项分值时，存在问题不允许为空！");
                                    flag = false;
                                }
                            }
                        }
                    }
                }
            }
        }
        return flag;
    }

    /**
     * @Description: 编辑页二暂存提交执行存储
     * 调用这个方法前 已经调用父页的暂存公用保存方法 所以考核评估表评估人 与考核评估考核项目的实得分已经更新
     * 编辑二页面 存储仅需要操作考核结果子表 以及 考核扣分原因
     * @MethodAuthor pw, 2021年07月9日
     */
    private void executeSaveSub() throws Exception {
        this.checkMainService.saveCheckSubByTdZwZkCheckTable(curCheckTable);
    }

    /**
     * @Description: 更新第一编辑页评估考核项目 用于显示的扣分原因字段
     * @MethodAuthor pw, 2021年07月10日
     */
    private void changeDeductRsn(TdZwZkCheckItem checkItem, Map<Integer, List<TdZwZkCheckSub>> itmSubListMap) {
        if (null != checkItem) {
            List<TdZwZkCheckSub> checkSubList = checkItem.getCheckSubList();
            //由于checkMain是重新查询赋值的 所以 项目表下并没有子表关联 需重新赋值
            if (CollectionUtils.isEmpty(checkSubList) && !CollectionUtils.isEmpty(itmSubListMap) && null != checkItem.getRid()) {
                checkSubList = itmSubListMap.get(checkItem.getRid());
                checkItem.setCheckSubList(checkSubList);
            }
            //子表排序
            sortCheckSubList(checkSubList);
            if (!CollectionUtils.isEmpty(checkSubList)) {
                StringBuffer deductBuffer1 = new StringBuffer();
                StringBuffer deductBuffer2 = new StringBuffer();
                for (TdZwZkCheckSub checkSub : checkSubList) {
                    if (null != checkSub.getFkByScoreRstId()) {
                        checkSub.setSoreRstRid(checkSub.getFkByScoreRstId().getRid());
                    }
                    List<TdZwZkCheckDeduct> checkDeductList = checkSub.getCheckDeductList();
                    if (CollectionUtils.isEmpty(checkDeductList)) {
                        continue;
                    }
                    for (TdZwZkCheckDeduct checkDeduct : checkDeductList) {
                        String deductRst = (null == checkDeduct.getFkByDeductId() ||
                                null == checkDeduct.getFkByDeductId().getFkByDeductId()) ? null :
                                checkDeduct.getFkByDeductId().getFkByDeductId().getCodeName();
                        if (StringUtils.isNotBlank(deductRst)) {
                            deductBuffer1.append("；").append(deductRst);
                            deductBuffer2.append("；<br/>").append(deductRst);
                        }
                    }
                }
                String deductRsn = deductBuffer1.toString();
                String deductRsn2 = deductBuffer2.toString();
                if (StringUtils.isNotBlank(deductRsn)) {
                    checkItem.setDeductRsn(deductRsn.substring(1));
                } else {
                    checkItem.setDeductRsn(null);
                }
                if (StringUtils.isNotBlank(deductRsn2)) {
                    checkItem.setDeductRsn2(deductRsn2.substring(6));
                } else {
                    checkItem.setDeductRsn2(null);
                }
            } else {
                checkItem.setDeductRsn(null);
                checkItem.setDeductRsn2(null);
            }
        }
    }


    /**
     * @Description: 更新考核类型后 进入第二编辑页暂存 需要对curCheckTable重新赋值 否则因缺少item的rid 导致子表存储异常
     * @MethodAuthor pw, 2021年07月13日
     */
    private void fillNewCurCheckTable() {
        List<TdZwZkCheckTable> checkTableList = checkMain.getCheckTables();
        List<TdZwZkCheckItem> checkItemList = curCheckTable.getCheckItems();
        Map<Integer, TdZwZkCheckItem> tmpMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(checkItemList)) {
            for (TdZwZkCheckItem checkItem : checkItemList) {
                if (null == checkItem.getFkByItemId()) {
                    continue;
                }
                tmpMap.put(checkItem.getFkByItemId().getRid(), checkItem);
            }
        }
        if (!CollectionUtils.isEmpty(checkTableList)) {
            for (TdZwZkCheckTable checkTable : checkTableList) {
                if (null != checkTable.getFkByCheckTableId() && null != curCheckTable.getFkByCheckTableId().getRid()
                        && checkTable.getFkByCheckTableId().getRid().intValue() == curCheckTable.getFkByCheckTableId().getRid()) {
                    List<TdZwZkCheckItem> tmpCheckItemList = checkTable.getCheckItems();
                    if (!CollectionUtils.isEmpty(tmpCheckItemList)) {
                        for (TdZwZkCheckItem checkItem : tmpCheckItemList) {
                            TdZwZkCheckItem tmpCheckItem = tmpMap.get(checkItem.getFkByItemId().getRid());
                            if (null != tmpCheckItem) {
                                List<TdZwZkCheckSub> checkSubList = tmpCheckItem.getCheckSubList();
                                if (CollectionUtils.isEmpty(checkSubList)) {
                                    checkItem.setCheckSubList(new ArrayList<TdZwZkCheckSub>());
                                    continue;
                                }
                                for (TdZwZkCheckSub tmpCheckSub : checkSubList) {
                                    tmpCheckSub.setCreateDate(new Date());
                                    tmpCheckSub.setCreateManid(Global.getUser().getRid());
                                    tmpCheckSub.setFkByMainId(checkItem);
                                    if (tmpCheckSub.getFkByRstId() != null && tmpCheckSub.getFkByRstId().getRid() != null) {
                                        tmpCheckSub.setRstRid(tmpCheckSub.getFkByRstId().getRid());
                                    }
                                   /* List<TdZwZkCheckDeduct> tmpDeductList = tmpCheckSub.getCheckDeductList();
                                    if (!CollectionUtils.isEmpty(tmpDeductList)) {
                                        for (TdZwZkCheckDeduct checkDeduct : tmpDeductList) {
                                            checkDeduct.setCreateDate(new Date());
                                            checkDeduct.setCreateManid(Global.getUser().getRid());
                                            checkDeduct.setFkByMainId(tmpCheckSub);
                                        }
                                    }*/
                                }
                                checkItem.setCheckSubList(checkSubList);
                            }
                        }
                    }
                    curCheckTable = checkTable;
                    break;
                }
            }
        }
    }

    /**
     * @Description: 更新当前评估表的内容
     * @MethodAuthor pw, 2021年07月13日
     */
    private void changeCheckTable() {
        List<TdZwZkCheckTable> tmpCheckList = checkMain.getCheckTables();
        if (CollectionUtils.isEmpty(tmpCheckList)) {
            return;
        }
        Map<Integer, TdZwZkCheckTable> tableMap = getCheckTableMap();
        for (TdZwZkCheckTable t : tmpCheckList) {
            if (null != t.getFkByCheckTableId() && null != t.getFkByCheckTableId().getRid()
                    && null != curCheckTable && null != curCheckTable.getFkByCheckTableId()
                    && null != curCheckTable.getFkByCheckTableId().getRid() &&
                    t.getFkByCheckTableId().getRid().intValue() == curCheckTable.getFkByCheckTableId().getRid()) {
                t.setCheckPsn(curCheckTable.getCheckPsn());
                t.setCheckItems(curCheckTable.getCheckItems());
                t.setStateMark(curCheckTable.getStateMark());
            } else {
                if (tableMap != null && tableMap.get(t.getRid()) != null) {
                    t.setCheckPsn(tableMap.get(t.getRid()).getCheckPsn());
                    t.setStateMark(tableMap.get(t.getRid()).getStateMark());
                }
            }
        }
    }


    /**
     * <p>方法描述：getCheckTableMap</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-06-17
     **/
    public Map<Integer, TdZwZkCheckTable> getCheckTableMap() {
        Map<Integer, TdZwZkCheckTable> tableMap = new HashMap<>();
        List<TdZwZkCheckTable> checkTableList = checkMainService.findCheckTableByRid(checkMain.getRid());
        if (!CollectionUtils.isEmpty(checkTableList)) {
            for (TdZwZkCheckTable table : checkTableList) {
                tableMap.put(table.getRid(), table);
            }
        }
        return tableMap;
    }

    /**
     * @Description: 重新赋值扣分原因
     * @MethodAuthor pw, 2021年07月13日
     */
    private void changeItemDeductRsn() {
        if (null == checkMain) {
            return;
        }
        List<TdZwZkCheckTable> tmpTableList = checkMain.getCheckTables();
        //评估表排序
        sortCheckTableList(tmpTableList);
        if (!CollectionUtils.isEmpty(tmpTableList)) {
            List<Integer> itemRidList = new ArrayList<>();
            for (TdZwZkCheckTable tmpTable : tmpTableList) {
                List<TdZwZkCheckItem> tmpItemList = tmpTable.getCheckItems();
                if (CollectionUtils.isEmpty(tmpItemList)) {
                    continue;
                }
                for (TdZwZkCheckItem checkItem : tmpItemList) {
                    if (null != checkItem.getRid()) {
                        itemRidList.add(checkItem.getRid());
                    }
                }
            }
            Map<Integer, List<TdZwZkCheckSub>> itmSubListMap = checkMainService.findTdZwZkCheckSubListByMainRidsForMapResult(itemRidList);
            for (TdZwZkCheckTable tmpTable : tmpTableList) {
                List<TdZwZkCheckItem> tmpItemList = tmpTable.getCheckItems();
                sortCheckItemList(tmpItemList);
                if (CollectionUtils.isEmpty(tmpItemList)) {
                    continue;
                }
                for (TdZwZkCheckItem checkItem : tmpItemList) {
                    changeDeductRsn(checkItem, itmSubListMap);
                }
            }
        }
    }


    /**
     * @Description: 排序及初始化扣分原因
     * @MethodAuthor pw, 2021年07月14日
     */
    private void sortCheckMainSub() {
        if (null == checkMain) {
            return;
        }
        List<TdZwZkCheckTable> tmpTableList = checkMain.getCheckTables();
        if (CollectionUtils.isEmpty(tmpTableList)) {
            return;
        }
        sortCheckTableList(tmpTableList);
        List<Integer> itemRidList = new ArrayList<>();
        for (TdZwZkCheckTable checkTable : tmpTableList) {
            List<TdZwZkCheckItem> tmpItemList = checkTable.getCheckItems();
            if (CollectionUtils.isEmpty(tmpItemList)) {
                continue;
            }
            sortCheckItemList(tmpItemList);
            for (TdZwZkCheckItem checkItem : tmpItemList) {
                if (null != checkItem.getRid()) {
                    itemRidList.add(checkItem.getRid());
                }
            }
        }
        Map<Integer, List<TdZwZkCheckSub>> checkSubMap = checkMainService.findTdZwZkCheckSubListByMainRidsForMapResult(itemRidList);
        for (TdZwZkCheckTable checkTable : tmpTableList) {
            List<TdZwZkCheckItem> tmpItemList = checkTable.getCheckItems();
            if (CollectionUtils.isEmpty(tmpItemList)) {
                continue;
            }
            for (TdZwZkCheckItem checkItem : tmpItemList) {
                List<TdZwZkCheckSub> tmpCheckSubList = checkItem.getCheckSubList();
                if (CollectionUtils.isEmpty(tmpCheckSubList)) {
                    tmpCheckSubList = checkSubMap.get(checkItem.getRid());
                }
                if (CollectionUtils.isEmpty(tmpCheckSubList)) {
                    continue;
                }
                sortCheckSubList(tmpCheckSubList);
                changeDeductRsn(checkItem, checkSubMap);
            }
        }
    }


    /**
     * @Description: 存在问题排序
     * @MethodAuthor pw, 2021年09月23日
     */
    private void sortScoreDeductList(List<TbZwZkScoreDeduct> sortList) {
        if (CollectionUtils.isEmpty(sortList)) {
            return;
        }
        Collections.sort(sortList, new Comparator<TbZwZkScoreDeduct>() {
            @Override
            public int compare(TbZwZkScoreDeduct o1, TbZwZkScoreDeduct o2) {
                Integer xh1 = o1.getXh();
                Integer xh2 = o2.getXh();
                if (null != xh1 && null == xh2) {
                    return 1;
                } else if (null != xh2 && null == xh1) {
                    return -1;
                } else if (null != xh1 && null != xh2) {
                    return xh1.compareTo(xh2);
                }
                return 0;
            }
        });
    }

    /**
     * <p>方法描述：重新汇总</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-06-14
     **/
    public void againCollect() {
        //校验
        String rmk = initRmk(checkSub);
        if (StringUtils.isBlank(rmk)) {
            JsfUtil.addErrorMessage("无数据！");
        } else {
            checkSub.setRmk(rmk);
            JsfUtil.addSuccessMessage("汇总成功！");
        }
    }

    /**
     * <p>方法描述：初始化备注</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-06-14
     **/
    public String initRmk(TdZwZkCheckSub checkSub) {
        String rmk = null;
        if (checkSub.getFkByScoreId().getSpecialFlag() == null
                || StringUtils.isBlank(checkSub.getFkByScoreId().getBusExtends())
                || this.checkMain.getFkByCheckTypeId() == null
                || this.checkMain.getFkByCheckTypeId().getExtendS1() == null) {
            return null;
        }
        if (2 == checkSub.getFkByScoreId().getSpecialFlag()) {
            //专业技术人员考核
            rmk = initRmkByExtractPsn(
                    checkSub.getFkByScoreId().getBusExtends(),
                    this.checkMain.getFkByOrgId().getRid(),
                    this.checkMain.getFkByCheckTypeId().getExtendS1()
            );
        } else if (3 == checkSub.getFkByScoreId().getSpecialFlag()) {
            //盲样考核
            rmk = initRmkByDetectionIndicator(
                    StringUtils.string2list(checkSub.getFkByScoreId().getBusExtends(), ","),
                    this.checkMain.getFkByOrgId().getRid()
            );
        }
        return rmk;
    }

    /**
     * <p>方法描述：专业技术人员考核 拼装备注</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-06-14
     **/
    public String initRmkByExtractPsn(String busExtends, Integer unitRid, String checkType) {
        List<Object[]> list = checkMainService.findRmkBypsn(busExtends, unitRid, checkType);
        StringBuffer rmkStr = new StringBuffer();
        if (!CollectionUtils.isEmpty(list)) {
            for (Object[] obj : list) {
                rmkStr.append("；").append(obj[0].toString()).append("：").append(obj[1].toString()).append("，").append(obj[2].toString()).append("分");
            }
        }
        if (rmkStr.length() > 0) {
            return rmkStr.substring(1);
        }
        return null;
    }

    /**
     * 根据检测指标初始化备注
     *
     * @param type 1 毒物化学生物样本;2 临检生物样本
     * @return 备注
     */
    public String initRmkByDetectionIndicator(List<String> type, Integer unitId) {
        Map<String, Map<String, Object>> map = new HashMap<>(16);
        List<Map<String, Object>> rcdRstHgCountList = this.checkMainService.findZwmyRcdRstHgCount(unitId);
        if (CollectionUtils.isEmpty(rcdRstHgCountList)) {
            return "";
        }
        for (Map<String, Object> o : rcdRstHgCountList) {
            if (StringUtils.isBlank(StringUtils.objectToString(o.get("EXTENDS2")))) {
                continue;
            }
            if (map.containsKey(StringUtils.objectToString(o.get("EXTENDS2")))) {
                map.put(StringUtils.objectToString(o.get("EXTENDS2")), o);
            } else {
                map.put(StringUtils.objectToString(o.get("EXTENDS2")), o);
            }
        }
        List<String> rmkList = new ArrayList<>();
        for (String s : type) {
            String rmk1 = initRmkByDetectionIndicator(map, s);
            if (StringUtils.isNotBlank(rmk1)) {
                rmkList.add(rmk1);
            }
        }
        if (CollectionUtils.isEmpty(rmkList)) {
            return "";
        }
        return StringUtils.list2string(rmkList, "；");
    }

    public String initRmkByDetectionIndicator(Map<String, Map<String, Object>> map, String extends2) {
        if (map.containsKey(extends2)) {
            Map<String, Object> objects = map.get(extends2);
            String name = StringUtils.objectToString(objects.get("CODE_NAME"));
            boolean isNotHg = "0".equals(StringUtils.objectToString(objects.get("IF_HG")));
            String sum = StringUtils.objectToString(objects.get("SUM"));
            return name + "有" + (isNotHg ? sum : "0") + "个不合格";
        }
        return "";
    }

    /**
     * <p>方法描述：撤销前验证</p>
     *
     * @MethodAuthor hsj 2025-06-05 16:35
     */
    public void beforeCancelAction() {
        String state = this.checkMainService.findStateById(this.checkMain.getRid());
        if (!"1".equals(state)) {
            SystemMessageEnum.CHECK_STATUS_CHANGE.showMessage();
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmCancelDialog').show()");
    }

    /**
     * <p>方法描述：撤销功能</p>
     *
     * @MethodAuthor hsj 2025-06-05 16:35
     */
    public void cancelAction() {
        if (this.checkMain == null || this.checkMain.getRid() == null) {
            return;
        }
        try {
            this.checkMainService.updateCheckMainStatue(0, this.checkMain.getRid());
            this.modInitAction();
            SystemMessageEnum.CANCEL_SUCCESS.showMessage();
        } catch (Exception e) {
            e.printStackTrace();
            SystemMessageEnum.CANCEL_FAIL.showMessage();
        }
    }

    /**
     * @Description: 弹出框选择存在问题
     * @MethodAuthor pw, 2021年07月8日
     */
    public void selectDeductCause(TbZwZkScoreDeduct curCheckDeduct) {
        if (null == curCheckDeduct) {
            return;
        }
    }
    /***********************整改与审核*******************************/
    /**
     * <p>方法描述：整改审核前验证</p>
     *
     * @MethodAuthor hsj 2025-06-06 13:57
     */
    public void beforeImproveAudit() {
        String state = this.checkMainService.findIfImproveEndById(this.checkMain.getRid());
        if (!"1".equals(state)) {
            SystemMessageEnum.CHECK_STATUS_CHANGE.showMessage();
            return;
        }
        boolean hasError = false;
        Integer ifImproveEnd = this.checkMain.getIfImproveEnd();
        if (!"2".equals(Convert.toStr(ifImproveEnd)) && !"3".equals(Convert.toStr(ifImproveEnd))) {
            SystemMessageEnum.PLEASE_SELECT.formatMessage("审核结果");
            hasError = true;
        }
        if ("3".equals(Convert.toStr(ifImproveEnd)) && StringUtils.isBlank(this.checkMain.getCheckOpinion())) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("审核意见");
            hasError = true;
        }
        if (hasError) {
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ImproveAuditDialog').show()");
    }

    /**
     * <p>方法描述：整改审核</p>
     *
     * @MethodAuthor hsj 2025-06-06 14:09
     */
    public void improveAudit() {
        try {
            this.checkMainService.improveAudit(this.checkMain);
            SystemMessageEnum.SUBMIT_SUCCESS.showMessage();
            this.backAction();
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            SystemMessageEnum.SUBMIT_FAIL.showMessage();
        }
    }

    /**
     * <p>方法描述：整改-暂存</p>
     *
     * @MethodAuthor hsj 2025-06-06 14:30
     */
    public void improveSave() {
        if (this.validateImprove(false)) {
            return;
        }
        try {
            this.checkMainService.improveSave(this.checkMain);
            SystemMessageEnum.TEMP_SAVE_SUCCESS.showMessage();
        } catch (Exception e) {
            e.printStackTrace();
            SystemMessageEnum.TEMP_SAVE_FAIL.showMessage();
        }
    }

    /**
     * <p>方法描述：整改验证</p>
     *
     * @param flag false:暂存，true:提交
     * @MethodAuthor hsj 2025-06-06 14:33
     */
    private boolean validateImprove(boolean flag) {
        String state = this.checkMainService.findIfImproveEndById(this.checkMain.getRid());
        if (!"0".equals(state) && !"3".equals(state)) {
            SystemMessageEnum.CHECK_STATUS_CHANGE.showMessage();
            return true;
        }
        boolean result = false;
        if (flag && CollectionUtils.isEmpty(this.checkMain.getImproveCheckProves())) {
            SystemMessageEnum.PLEASE_UPLOAD.formatMessage("整改报告");
            result = true;
        }
        if (!CollectionUtils.isEmpty(this.checkMain.getImproveCheckProves()) && this.checkMain.getImproveCheckProves().size() > 10) {
            JsfUtil.addErrorMessage("整改报告附件最多可上传10个！");
            result = true;
        }
        if (flag && StringUtils.isBlank(this.checkMain.getImproveLinkman())) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("联系人");
            result = true;
        }
        if (flag && StringUtils.isBlank(this.checkMain.getImprovePhone())) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("联系电话");
            result = true;
        }
        if (StringUtils.isNotBlank(this.checkMain.getImprovePhone()) && !StringUtils.vertyPhone(this.checkMain.getImprovePhone())) {
            SystemMessageEnum.FORMAT_DATA.formatMessage("联系电话");
            result = true;
        }
        return result;
    }

    /**
     * <p>方法描述：整改前验证</p>
     *
     * @MethodAuthor hsj 2025-06-06 14:48
     */
    public void beforeImproveSumbit() {
        if (this.validateImprove(true)) {
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ImproveSumbitDialog').show()");
    }

    /**
     * <p>方法描述：整改提交</p>
     *
     * @MethodAuthor hsj 2025-06-06 14:41
     */
    public void improveSumbit() {
        try {
            this.checkMain.setIfImproveEnd(1);
            this.checkMainService.improveSave(this.checkMain);
            SystemMessageEnum.SUBMIT_SUCCESS.showMessage();
            this.backAction();
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            this.checkMain.setIfImproveEnd(0);
            SystemMessageEnum.SUBMIT_FAIL.showMessage();
        }
    }

    /***********************整改与审核*******************************/
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwZkCheckMain getCheckMain() {
        return checkMain;
    }

    public void setCheckMain(TdZwZkCheckMain checkMain) {
        this.checkMain = checkMain;
    }

    public Map<String, TsSimpleCode> getItemMap() {
        return itemMap;
    }

    public void setItemMap(Map<String, TsSimpleCode> itemMap) {
        this.itemMap = itemMap;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public Date getSearchCheckStartTime() {
        return searchCheckStartTime;
    }

    public void setSearchCheckStartTime(Date searchCheckStartTime) {
        this.searchCheckStartTime = searchCheckStartTime;
    }

    public Date getSearchCheckEndTime() {
        return searchCheckEndTime;
    }

    public void setSearchCheckEndTime(Date searchCheckEndTime) {
        this.searchCheckEndTime = searchCheckEndTime;
    }

    public List<Integer> getSearchNeedImproveStateList() {
        return searchNeedImproveStateList;
    }

    public void setSearchNeedImproveStateList(List<Integer> searchNeedImproveStateList) {
        this.searchNeedImproveStateList = searchNeedImproveStateList;
    }

    public List<Integer> getSearchFinishImproveStateList() {
        return searchFinishImproveStateList;
    }

    public void setSearchFinishImproveStateList(List<Integer> searchFinishImproveStateList) {
        this.searchFinishImproveStateList = searchFinishImproveStateList;
    }

    public List<SelectItem> getCheckTypeList() {
        return checkTypeList;
    }

    public void setCheckTypeList(List<SelectItem> checkTypeList) {
        this.checkTypeList = checkTypeList;
    }

    public FilePojoNew getFileTemp() {
        return fileTemp;
    }

    public void setFileTemp(FilePojoNew fileTemp) {
        this.fileTemp = fileTemp;
    }

    public Integer getAnnexViewIndex() {
        return annexViewIndex;
    }

    public void setAnnexViewIndex(Integer annexViewIndex) {
        this.annexViewIndex = annexViewIndex;
    }

    public TdZwZkCheckProve getCheckProveTmp() {
        return checkProveTmp;
    }

    public void setCheckProveTmp(TdZwZkCheckProve checkProveTmp) {
        this.checkProveTmp = checkProveTmp;
    }


    public void setReportDownloadFile(StreamedContent reportDownloadFile) {
        this.reportDownloadFile = reportDownloadFile;
    }

    public String getReportFilePath() {
        return reportFilePath;
    }

    public void setReportFilePath(String reportFilePath) {
        this.reportFilePath = reportFilePath;
    }

    public String getReportGenerateErrMsg() {
        return reportGenerateErrMsg;
    }

    public void setReportGenerateErrMsg(String reportGenerateErrMsg) {
        this.reportGenerateErrMsg = reportGenerateErrMsg;
    }

    public TdZwZkCheckTable getCurCheckTable() {
        return curCheckTable;
    }

    public void setCurCheckTable(TdZwZkCheckTable curCheckTable) {
        this.curCheckTable = curCheckTable;
    }

    public Map<Integer, List<TbZwZkScores>> getItemScoresMap() {
        return itemScoresMap;
    }

    public void setItemScoresMap(Map<Integer, List<TbZwZkScores>> itemScoresMap) {
        this.itemScoresMap = itemScoresMap;
    }

    public List<TdZwZkCheckItem> getZwZkCheckItemList() {
        return zwZkCheckItemList;
    }

    public void setZwZkCheckItemList(List<TdZwZkCheckItem> zwZkCheckItemList) {
        this.zwZkCheckItemList = zwZkCheckItemList;
    }

    public Map<Integer, String> getIndexDescMap() {
        return indexDescMap;
    }

    public void setIndexDescMap(Map<Integer, String> indexDescMap) {
        this.indexDescMap = indexDescMap;
    }

    public TdZwZkCheckSub getCurCheckSub() {
        return curCheckSub;
    }

    public void setCurCheckSub(TdZwZkCheckSub curCheckSub) {
        this.curCheckSub = curCheckSub;
    }

    public List<TbZwZkScoreDeduct> getAllScoreDeductList() {
        return allScoreDeductList;
    }

    public void setAllScoreDeductList(List<TbZwZkScoreDeduct> allScoreDeductList) {
        this.allScoreDeductList = allScoreDeductList;
    }

    public List<TbZwZkScoreDeduct> getShowScoreDeductList() {
        return showScoreDeductList;
    }

    public void setShowScoreDeductList(List<TbZwZkScoreDeduct> showScoreDeductList) {
        this.showScoreDeductList = showScoreDeductList;
    }

    public String getDeductQuery() {
        return deductQuery;
    }

    public void setDeductQuery(String deductQuery) {
        this.deductQuery = deductQuery;
    }

    public String getDeductUpdateId() {
        return deductUpdateId;
    }

    public void setDeductUpdateId(String deductUpdateId) {
        this.deductUpdateId = deductUpdateId;
    }

    public String getUploadImproveFilePath() {
        return uploadImproveFilePath;
    }

    public void setUploadImproveFilePath(String uploadImproveFilePath) {
        this.uploadImproveFilePath = uploadImproveFilePath;
    }

    public String getUploadImproveFileName() {
        return uploadImproveFileName;
    }

    public void setUploadImproveFileName(String uploadImproveFileName) {
        this.uploadImproveFileName = uploadImproveFileName;
    }

    public String getUploadImproveFileCachePath() {
        return uploadImproveFileCachePath;
    }

    public void setUploadImproveFileCachePath(String uploadImproveFileCachePath) {
        this.uploadImproveFileCachePath = uploadImproveFileCachePath;
    }

    public String getUploadImproveFileCacheName() {
        return uploadImproveFileCacheName;
    }

    public void setUploadImproveFileCacheName(String uploadImproveFileCacheName) {
        this.uploadImproveFileCacheName = uploadImproveFileCacheName;
    }

    public List<TsSimpleCode> getScoreRstSimpleList() {
        return scoreRstSimpleList;
    }

    public void setScoreRstSimpleList(List<TsSimpleCode> scoreRstSimpleList) {
        this.scoreRstSimpleList = scoreRstSimpleList;
    }

    public Map<Integer, TsSimpleCode> getScoreRstSimpleMap() {
        return scoreRstSimpleMap;
    }

    public void setScoreRstSimpleMap(Map<Integer, TsSimpleCode> scoreRstSimpleMap) {
        this.scoreRstSimpleMap = scoreRstSimpleMap;
    }

    public Integer getHgSoreRstRid() {
        return hgSoreRstRid;
    }

    public void setHgSoreRstRid(Integer hgSoreRstRid) {
        this.hgSoreRstRid = hgSoreRstRid;
    }

    public Map<String, List<TsSimpleCode>> getSoreRstSimpleMapWithItemTypeCode() {
        return soreRstSimpleMapWithItemTypeCode;
    }

    public void setSoreRstSimpleMapWithItemTypeCode(Map<String, List<TsSimpleCode>> soreRstSimpleMapWithItemTypeCode) {
        this.soreRstSimpleMapWithItemTypeCode = soreRstSimpleMapWithItemTypeCode;
    }

    public Map<Integer, TsSimpleCode> getCheckTypeMap() {
        return checkTypeMap;
    }

    public void setCheckTypeMap(Map<Integer, TsSimpleCode> checkTypeMap) {
        this.checkTypeMap = checkTypeMap;
    }

    public List<TsSimpleCode> getCheckTypeSimpleList() {
        return checkTypeSimpleList;
    }

    public void setCheckTypeSimpleList(List<TsSimpleCode> checkTypeSimpleList) {
        this.checkTypeSimpleList = checkTypeSimpleList;
    }

    public Integer getCheckTypeTempRid() {
        return checkTypeTempRid;
    }

    public void setCheckTypeTempRid(Integer checkTypeTempRid) {
        this.checkTypeTempRid = checkTypeTempRid;
    }

    public TdZwZkCheckSub getCheckSub() {
        return checkSub;
    }

    public void setCheckSub(TdZwZkCheckSub checkSub) {
        this.checkSub = checkSub;
    }

    public Boolean getIfRemoveCheckResults() {
        return ifRemoveCheckResults;
    }

    public void setIfRemoveCheckResults(Boolean ifRemoveCheckResults) {
        this.ifRemoveCheckResults = ifRemoveCheckResults;
    }

    public Boolean getIfRemoveCheckResults2() {
        return ifRemoveCheckResults2;
    }

    public void setIfRemoveCheckResults2(Boolean ifRemoveCheckResults2) {
        this.ifRemoveCheckResults2 = ifRemoveCheckResults2;
    }

    public Boolean getJurisdiction() {
        return jurisdiction;
    }

    public void setJurisdiction(Boolean jurisdiction) {
        this.jurisdiction = jurisdiction;
    }

    public boolean getShowCancel() {
        return showCancel;
    }

    public void setShowCancel(boolean showCancel) {
        this.showCancel = showCancel;
    }

    public boolean isShowCancel() {
        return showCancel;
    }

    public TsUnit getOrgUnit() {
        return orgUnit;
    }

    public void setOrgUnit(TsUnit orgUnit) {
        this.orgUnit = orgUnit;
    }

    public Integer getMainId() {
        return mainId;
    }

    public void setMainId(Integer mainId) {
        this.mainId = mainId;
    }

    public Boolean getIfScore() {
        return ifScore;
    }

    public void setIfScore(Boolean ifScore) {
        this.ifScore = ifScore;
    }

    public Integer getAnnexType() {
        return annexType;
    }

    public void setAnnexType(Integer annexType) {
        this.annexType = annexType;
    }

    public Map<Integer, List<TbZwZkScoreOption>> getSoreRstMap() {
        return soreRstMap;
    }

    public void setSoreRstMap(Map<Integer, List<TbZwZkScoreOption>> soreRstMap) {
        this.soreRstMap = soreRstMap;
    }

    public Set<Integer> getHgSoreRstRidSet() {
        return hgSoreRstRidSet;
    }

    public void setHgSoreRstRidSet(Set<Integer> hgSoreRstRidSet) {
        this.hgSoreRstRidSet = hgSoreRstRidSet;
    }

    /* public Map<Integer, Integer> getRstRstRidMap() {
        return rstRstRidMap;
    }

    public void setRstRstRidMap(Map<Integer, Integer> rstRstRidMap) {
        this.rstRstRidMap = rstRstRidMap;
    }*/

    public String getViewType() {
        return viewType;
    }

    public void setViewType(String viewType) {
        this.viewType = viewType;
    }
}
