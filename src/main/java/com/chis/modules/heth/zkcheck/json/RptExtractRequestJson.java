package com.chis.modules.heth.zkcheck.json;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>类描述：报告抽取请求封装数据 </p>
 * pw 2024/7/4
 **/
public class RptExtractRequestJson implements Serializable {
    private static final long serialVersionUID = 7418157928008007328L;
    private List<Integer> orgIdList;
    private Integer userId;
    private List<RptExtractKeyMapParamJson> extractTotalList;
    private List<RptExtractKeyMapParamJson> ruleNumList;

    // 体检日期开始时间
    private Date bhkStartDate;
    // 体检日期结束时间
    private Date bhkEndDate;
    // 报告出具日期开始时间
    private Date rptStartDate;
    // 报告出具日期结束时间
    private Date rptEndDate;

    public List<Integer> getOrgIdList() {
        return orgIdList;
    }

    public void setOrgIdList(List<Integer> orgIdList) {
        this.orgIdList = orgIdList;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public List<RptExtractKeyMapParamJson> getExtractTotalList() {
        return extractTotalList;
    }

    public void setExtractTotalList(List<RptExtractKeyMapParamJson> extractTotalList) {
        this.extractTotalList = extractTotalList;
    }

    public List<RptExtractKeyMapParamJson> getRuleNumList() {
        return ruleNumList;
    }

    public void setRuleNumList(List<RptExtractKeyMapParamJson> ruleNumList) {
        this.ruleNumList = ruleNumList;
    }

    public Date getBhkStartDate() {
        return bhkStartDate;
    }

    public void setBhkStartDate(Date bhkStartDate) {
        this.bhkStartDate = bhkStartDate;
    }

    public Date getBhkEndDate() {
        return bhkEndDate;
    }

    public void setBhkEndDate(Date bhkEndDate) {
        this.bhkEndDate = bhkEndDate;
    }

    public Date getRptStartDate() {
        return rptStartDate;
    }

    public void setRptStartDate(Date rptStartDate) {
        this.rptStartDate = rptStartDate;
    }

    public Date getRptEndDate() {
        return rptEndDate;
    }

    public void setRptEndDate(Date rptEndDate) {
        this.rptEndDate = rptEndDate;
    }
}
