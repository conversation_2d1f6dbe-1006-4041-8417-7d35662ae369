package com.chis.modules.heth.zkcheck.web;

import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesBean;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.context.RequestContext;
import org.primefaces.model.DefaultStreamedContent;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.*;

/**
 * 质控考核情况查询
 *
 * <AUTHOR>
 * @version 1.0
 */
@ManagedBean(name = "inquiryExamStatisticsBean")
@ViewScoped
public class InquiryExamStatisticsBean extends FacesBean {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
     * 查询条件：地区集合
     */
    private List<TsZone> searchZoneList = new ArrayList<>();
    /**
     * 查询条件：地区编码
     */
    private String searchZoneCode;
    /**
     * 查询条件：地区名称
     */
    private String searchZoneName;
    /**
     * 查询条件：考核类型
     */
    private Integer examTypeRid;
    private List<TsSimpleCode> examTypeSimpleCodeList;
    /**
     * 查询条件：考核日期-开始日期
     */
    private Date searchExamBeginDate;
    /**
     * 查询条件：考核日期-结束日期
     */
    private Date searchExamEndDate;
    /**
     * 查询条件：汇总维度
     */
    private String statisticalDimension;
    private String statisticalDimensionOld;
    private List<SelectItem> statisticalDimensionList;

    private List<Object[]> dataTableList;
    private Integer dataTableListCount;

    public InquiryExamStatisticsBean() {
        this.dataTableList = new ArrayList<>();
        init();
    }

    private void init() {
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        //地区
        if (null == this.searchZoneList || this.searchZoneList.size() <= 0) {
            this.searchZoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "4");
            this.searchZoneCode = this.searchZoneList.get(0).getZoneCode();
            this.searchZoneName = this.searchZoneList.get(0).getZoneName();
        }
        //考核类型
        this.examTypeSimpleCodeList = this.commService.findNumSimpleCodesByTypeId("5554");
        if (ObjectUtil.isNotEmpty(this.examTypeSimpleCodeList)) {
            this.examTypeRid = this.examTypeSimpleCodeList.get(0).getRid();
        }
        //考核日期
        this.searchExamEndDate = new Date();
        this.searchExamBeginDate = DateUtils.getYearFirstDay(this.searchExamEndDate);
        //汇总维度
        this.statisticalDimensionList = new ArrayList<>();
        this.statisticalDimensionList.add(new SelectItem("1", "考核情况"));
        this.statisticalDimensionList.add(new SelectItem("2", "评分情况"));
        this.statisticalDimension = "1";

        searchAction();
    }

    public void searchAction() {
        if (verifyQueryRequiredFailed()) {
            return;
        }
        executeSearchSql();
        this.statisticalDimensionOld = this.statisticalDimension;
    }

    private boolean verifyQueryRequiredFailed() {
        boolean verify = false;
        if (ObjectUtil.isEmpty(this.searchZoneCode)) {
            JsfUtil.addErrorMessage("请选择地区！");
            verify = true;
        }
        if (ObjectUtil.isEmpty(this.examTypeRid)) {
            JsfUtil.addErrorMessage("请选择考核类型！");
            verify = true;
        }
        if (ObjectUtil.isEmpty(this.searchExamBeginDate)) {
            JsfUtil.addErrorMessage("请选择考核开始日期！");
            verify = true;
        }
        if (ObjectUtil.isEmpty(this.searchExamEndDate)) {
            JsfUtil.addErrorMessage("请选择考核结束日期！");
            verify = true;
        }
        if (ObjectUtil.isEmpty(this.statisticalDimension)) {
            JsfUtil.addErrorMessage("请选择汇总维度！");
            verify = true;
        }
        return verify;
    }

    public void executeSearchSql() {
        Map<String, Object> paramMap = new HashMap<>();
        StringBuilder sql = new StringBuilder();
        if ("2".equals(this.statisticalDimension)) {
            boolean showCity = ZoneUtil.getZoneType(this.searchZoneCode) == 2;
            sql.append("WITH ZK_CHECK_MAIN AS (SELECT CM.RID, ROW_NUMBER() OVER (PARTITION BY CM.ORG_ID ORDER BY CM.CHECK_DATE DESC, CM.RID DESC) RN ");
            sql.append("                       FROM TD_ZW_ZK_CHECK_MAIN CM ");
            sql.append("                                LEFT JOIN TS_UNIT U ON CM.ORG_ID = U.RID ");
            sql.append("                                LEFT JOIN TS_ZONE Z ON U.ZONE_ID = Z.RID ");
            sql.append("                                LEFT JOIN TD_ZW_ZK_CHECK_TABLE CT ON CM.RID = CT.MAIN_ID ");
            sql.append("                                LEFT JOIN TB_ZW_ZK_BADRSN_STAND BS ON CT.CHECK_TABLE_ID = BS.RID ");
            sql.append("                       WHERE CM.STATE_MARK = 1 AND NVL(CM.DEL_MARK, 0) = 0 ");
            sql.append("                         AND Z.ZONE_GB LIKE :zoneCode ");
            sql.append("                         AND BS.CHECK_TYPE_ID = :checkType ");
            sql.append("                         AND CM.CHECK_DATE >= TO_DATE(:searchExamBeginDate, 'YYYY-MM-DD HH24:MI:SS') ");
            sql.append("                         AND CM.CHECK_DATE <= TO_DATE(:searchExamEndDate, 'YYYY-MM-DD HH24:MI:SS') ");
            sql.append("), ");
            sql.append("     ORG_SCORE AS (SELECT Z").append(showCity ? "2" : "1").append(".ZONE_NAME, U.UNITNAME, Z").append(showCity ? "2" : "1").append(".ZONE_GB, CM.ORG_ID, CS.SCORE_ID, SC2.EXTENDS1, SC2.NUM, CM.TOTAL_CHECK_VAL, ");
            sql.append("                          DECODE(SC2.EXTENDS1, 1, SC2.EXTENDS5, 0) as SCORE_VAL1, ");
            sql.append("                          DECODE(SC2.EXTENDS1, 2, SC2.EXTENDS5, 0) as SCORE_VAL2, ");
            sql.append("                          CM.TOTAL_SCORE_VAL ");
            sql.append("                   FROM TD_ZW_ZK_CHECK_MAIN CM ");
            sql.append("                            LEFT JOIN TS_SIMPLE_CODE SC1 ON CM.CHECK_TYPE_ID = SC1.RID ");
            sql.append("                            LEFT JOIN TD_ZW_ZK_CHECK_TABLE CT ON CM.RID = CT.MAIN_ID ");
            sql.append("                            LEFT JOIN TD_ZW_ZK_CHECK_ITEM CI ON CT.RID = CI.MAIN_ID ");
            sql.append("                            LEFT JOIN TD_ZW_ZK_CHECK_SUB CS ON CI.RID = CS.MAIN_ID ");
            sql.append("                            LEFT JOIN TB_ZW_ZK_SCORES S ON CS.SCORE_ID = S.RID ");
            sql.append("                            LEFT JOIN TS_SIMPLE_CODE SC2 ON CS.SCORE_RST_ID = SC2.RID ");
            sql.append("                            LEFT JOIN TS_UNIT U ON CM.ORG_ID = U.RID ");
            sql.append("                            LEFT JOIN TS_ZONE Z ON U.ZONE_ID = Z.RID ");
            sql.append("                            INNER JOIN TS_ZONE Z1 ON SUBSTR(Z.ZONE_GB, 1, 6) || '0000' = Z1.ZONE_GB ");
            sql.append("                            INNER JOIN TS_ZONE Z2 ON SUBSTR(Z.ZONE_GB, 1, 4) || '000000' = Z2.ZONE_GB ");
            sql.append("                   WHERE EXISTS(SELECT 1 FROM ZK_CHECK_MAIN ZCM WHERE CM.RID = ZCM.RID AND ZCM.RN = 1)) ");
            sql.append("SELECT OS.ZONE_NAME, OS.UNITNAME, OS.TOTAL_CHECK_VAL, sum(OS.SCORE_VAL1), sum(OS.SCORE_VAL2), OS.TOTAL_SCORE_VAL, ");
            sql.append("       ROW_NUMBER() OVER (PARTITION BY OS.ZONE_NAME ORDER BY OS.ZONE_GB, OS.UNITNAME)      RN1, ");
            sql.append("       ROW_NUMBER() OVER (PARTITION BY OS.ZONE_NAME ORDER BY OS.ZONE_GB, OS.UNITNAME DESC) RN2, ");
            sql.append("       OS.ZONE_GB ");
            sql.append("FROM ORG_SCORE OS ");
            sql.append("GROUP BY OS.ZONE_NAME, OS.UNITNAME, OS.ZONE_GB, OS.TOTAL_CHECK_VAL, OS.TOTAL_SCORE_VAL ");
            sql.append("ORDER BY OS.ZONE_GB, OS.UNITNAME ");
        } else {
            sql.append("WITH ZK_CHECK_MAIN AS (SELECT CM.RID, ROW_NUMBER() OVER (PARTITION BY CM.ORG_ID ORDER BY CM.CHECK_DATE DESC, CM.RID DESC) RN ");
            sql.append("                       FROM TD_ZW_ZK_CHECK_MAIN CM ");
            sql.append("                            LEFT JOIN TS_UNIT U ON CM.ORG_ID = U.RID ");
            sql.append("                            LEFT JOIN TS_ZONE Z ON U.ZONE_ID = Z.RID ");
            sql.append("                            LEFT JOIN TD_ZW_ZK_CHECK_TABLE CT ON CM.RID = CT.MAIN_ID ");
            sql.append("                            LEFT JOIN TB_ZW_ZK_BADRSN_STAND BS ON CT.CHECK_TABLE_ID = BS.RID ");
            sql.append("                       WHERE CM.STATE_MARK = 1 ");
            sql.append("                         AND NVL(CM.DEL_MARK, 0) = 0 ");
            sql.append("                         AND Z.ZONE_GB LIKE :zoneCode ");
            sql.append("                         AND BS.CHECK_TYPE_ID = :checkType ");
            sql.append("                         AND CM.CHECK_DATE >= TO_DATE(:searchExamBeginDate, 'YYYY-MM-DD HH24:MI:SS') ");
            sql.append("                         AND CM.CHECK_DATE <= TO_DATE(:searchExamEndDate, 'YYYY-MM-DD HH24:MI:SS') ");
            sql.append("                         ), ");
            sql.append("     ORG_SCORE AS (SELECT CM.ORG_ID, CS.SCORE_ID, SC2.EXTENDS1, SC2.NUM ");
            sql.append("                   FROM TD_ZW_ZK_CHECK_MAIN CM ");
            sql.append("                            LEFT JOIN TS_SIMPLE_CODE SC1 ON CM.CHECK_TYPE_ID = SC1.RID ");
            sql.append("                            LEFT JOIN TD_ZW_ZK_CHECK_TABLE CT ON CM.RID = CT.MAIN_ID ");
            sql.append("                            LEFT JOIN TD_ZW_ZK_CHECK_ITEM CI ON CT.RID = CI.MAIN_ID ");
            sql.append("                            LEFT JOIN TD_ZW_ZK_CHECK_SUB CS ON CI.RID = CS.MAIN_ID ");
            sql.append("                            LEFT JOIN TS_SIMPLE_CODE SC2 ON CS.SCORE_RST_ID = SC2.RID ");
            sql.append("                   WHERE EXISTS(SELECT 1 FROM ZK_CHECK_MAIN ZCM WHERE CM.RID = ZCM.RID AND ZCM.RN = 1)), ");
            sql.append("     SCORE_TARGET_SUM AS (SELECT SCORE_ID S_RID, EXTENDS1 TARGET, SUM(1) SUM FROM ORG_SCORE OS GROUP BY SCORE_ID, EXTENDS1), ");
            sql.append("     SCORE_SUM AS (SELECT SCORE_ID S_RID, SUM(1) SUM FROM ORG_SCORE OS GROUP BY SCORE_ID), ");
            sql.append("     SCORE_INDEX AS (SELECT S.RID S_RID, SC2.CODE_NAME SI_NAME, S.XH S_XH, BS.XH BS_XH, NVL(CT1.RID, 0) IS_BASE_MEET ");
            sql.append("                     FROM TB_ZW_ZK_BADRSN_STAND BS ");
            sql.append("                              LEFT JOIN TB_ZW_ZK_SCORES S ON BS.RID = S.MAIN_ID ");
            sql.append("                              LEFT JOIN TB_ZW_ZK_SCORE_INDEX SI ON S.RID = SI.MAIN_ID ");
            sql.append("                              LEFT JOIN TS_SIMPLE_CODE SC1 ON SI.INDEX_ID = SC1.RID ");
            sql.append("                              LEFT JOIN TS_SIMPLE_CODE SC2 ON SC1.CODE_LEVEL_NO = SC2.CODE_LEVEL_NO || '.' || SC1.CODE_NO AND SC2.IF_REVEAL = 1 ");
            sql.append("                              INNER JOIN TS_CODE_TYPE CT ON SC2.CODE_TYPE_ID = CT.RID AND CT.CODE_TYPE_NAME = '5530' ");
            sql.append("                              LEFT JOIN TS_SIMPLE_CODE SC3 ON S.ITEM_TYPE_ID = SC3.RID ");
            sql.append("                              LEFT JOIN TS_SIMPLE_CODE SC4 ON (',' || SC4.EXTENDS3 || ',') LIKE ',' || SC3.CODE_NO || ',' AND SC4.EXTENDS1 = 2 AND SC4.IF_REVEAL = 1 ");
            sql.append("                              LEFT JOIN TS_CODE_TYPE CT1 ON SC4.CODE_TYPE_ID = CT1.RID AND CT1.CODE_TYPE_NAME = '5534' ");
            sql.append("                     WHERE BS.STATE_MARK = 1 AND BS.CHECK_TYPE_ID = :checkType ");
            sql.append("                     GROUP BY S.RID, SC2.CODE_NAME, S.XH, BS.XH, CT1.RID) ");
            sql.append("SELECT SI.SI_NAME, ");
            sql.append("       SI.S_XH, ");
            sql.append("       NVL(SS.SUM, 0) AS                                  SUM, ");
            sql.append("       NVL(STS1.SUM, 0)                                   SUM1, ");
            sql.append("       DECODE(SI.IS_BASE_MEET, 0, '/', NVL(STS2.SUM, 0))  SUM2, ");
            sql.append("       ROUND(((NVL(STS1.SUM, 0) + DECODE(SI.IS_BASE_MEET, 0, 0, NVL(STS2.SUM, 0))) / NVL(SS.SUM, 1) * 100), 2) PASSING_RATE, ");
            sql.append("       ROW_NUMBER() OVER (PARTITION BY SI.SI_NAME ORDER BY BS_XH, S_XH) RN1, ");
            sql.append("       ROW_NUMBER() OVER (PARTITION BY SI.SI_NAME ORDER BY BS_XH, S_XH DESC) RN2 ");
            sql.append("FROM SCORE_INDEX SI ");
            sql.append("         LEFT JOIN SCORE_SUM SS ON SI.S_RID = SS.S_RID ");
            sql.append("         LEFT JOIN SCORE_TARGET_SUM STS1 ON SI.S_RID = STS1.S_RID AND STS1.TARGET = 1 ");
            sql.append("         LEFT JOIN SCORE_TARGET_SUM STS2 ON SI.S_RID = STS2.S_RID AND STS2.TARGET = 2 ");
            sql.append("ORDER BY BS_XH, S_XH ");
        }

        paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(StringUtils.objectToString(this.searchZoneCode)).trim()) + "%");
        paramMap.put("checkType", this.examTypeRid);
        paramMap.put("searchExamBeginDate", DateUtils.formatDate(this.searchExamBeginDate) + " 00:00:00");
        paramMap.put("searchExamEndDate", DateUtils.formatDate(this.searchExamEndDate) + " 23:59:59");

        this.dataTableList = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql.toString(), paramMap));
        if (ObjectUtil.isEmpty(this.dataTableList)) {
            this.dataTableList = new ArrayList<>();
        }
        this.dataTableListCount = this.dataTableList.size();
    }

    public DefaultStreamedContent export() {
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("showStatus();");
        String excelTitle;
        ExcelExportUtil excelExportUtil;
        List<int[]> mergeCellsList = new ArrayList<>();
        if ("2".equals(this.statisticalDimensionOld)) {
            excelTitle = "体检机构评分表";
            String[][] excelHeaders = {
                    {"地区", "机构名称", "总分", "符合情况", "", "得分"},
                    {"", "", "", "符合", "基本符合", ""}
            };
            //添加列头合并单元格
            mergeCellsList.add(new int[]{1, 1, 3, 4});
            mergeCellsList.add(new int[]{1, 2, 0, 0});
            mergeCellsList.add(new int[]{1, 2, 1, 1});
            mergeCellsList.add(new int[]{1, 2, 2, 2});
            mergeCellsList.add(new int[]{1, 2, 5, 5});
            excelExportUtil = new ExcelExportUtil(excelTitle, excelHeaders, pakExcelExportDataList(this.dataTableList, mergeCellsList));
            excelExportUtil.setFrozenPaneRowsNum(3);
        } else {
            excelTitle = "职业健康检查机构质量控制考核汇总情况";
            String[] excelHeaders = {"考核二级指标", "考核内容序号", "参与考核机构数", "“符合”的机构数", "“基本符合”的机构数", "各项考核指标符合率（%）"};
            excelExportUtil = new ExcelExportUtil(excelTitle, excelHeaders, pakExcelExportDataList(this.dataTableList, mergeCellsList));
            excelExportUtil.setFrozenPaneRowsNum(2);
        }
        for (int[] mergeCells : mergeCellsList) {
            excelExportUtil.addMergeCells(mergeCells[0], mergeCells[1], mergeCells[2], mergeCells[3]);
        }
        Workbook wb = excelExportUtil.exportExcel("");
        if (wb != null) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                String fileName = excelTitle + ".xlsx";
                fileName = URLEncoder.encode(fileName, "UTF-8");
                wb.write(baos);
                baos.flush();
                byte[] aa = baos.toByteArray();
                context.execute("hideStatus();");
                return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
            } catch (Exception e) {
                JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            }
        }
        context.execute("hideStatus();");
        return null;
    }

    private List<ExcelExportObject[]> pakExcelExportDataList(List<Object[]> dataList, List<int[]> mergeCellsList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        int startLine = "2".equals(this.statisticalDimensionOld) ? 3 : 2;
        List<ExcelExportObject[]> excelExportObjectList = new ArrayList<>();
        for (int i = 0, dataListSize = dataList.size(); i < dataListSize; i++) {
            Object[] data = dataList.get(i);
            ExcelExportObject[] objects = new ExcelExportObject[6];
            objects[0] = new ExcelExportObject(StringUtils.objectToString(data[0]), XSSFCellStyle.ALIGN_CENTER);
            if ("2".equals(this.statisticalDimensionOld)) {
                objects[1] = new ExcelExportObject(StringUtils.objectToString(data[1]));
            } else {
                objects[1] = new ExcelExportObject(StringUtils.objectToString(data[1]), XSSFCellStyle.ALIGN_CENTER);
            }
            objects[2] = new ExcelExportObject(StringUtils.objectToString(data[2]), XSSFCellStyle.ALIGN_CENTER);
            objects[3] = new ExcelExportObject(StringUtils.objectToString(data[3]), XSSFCellStyle.ALIGN_CENTER);
            objects[4] = new ExcelExportObject(StringUtils.objectToString(data[4]), XSSFCellStyle.ALIGN_CENTER);
            objects[5] = new ExcelExportObject(StringUtils.objectToString(data[5]), XSSFCellStyle.ALIGN_CENTER);
            //处理单元格合并
            if ("1".equals(StringUtils.objectToString(data[6]))) {
                try {
                    int endLine = startLine + i + ((BigDecimal) data[7]).intValue() - 1;
                    mergeCellsList.add(new int[]{startLine + i, endLine, 0, 0});
                } catch (Exception ignored) {
                }
            }
            excelExportObjectList.add(objects);
        }
        return excelExportObjectList;
    }

    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public Integer getExamTypeRid() {
        return examTypeRid;
    }

    public void setExamTypeRid(Integer examTypeRid) {
        this.examTypeRid = examTypeRid;
    }

    public List<TsSimpleCode> getExamTypeSimpleCodeList() {
        return examTypeSimpleCodeList;
    }

    public void setExamTypeSimpleCodeList(List<TsSimpleCode> examTypeSimpleCodeList) {
        this.examTypeSimpleCodeList = examTypeSimpleCodeList;
    }

    public Date getSearchExamBeginDate() {
        return searchExamBeginDate;
    }

    public void setSearchExamBeginDate(Date searchExamBeginDate) {
        this.searchExamBeginDate = searchExamBeginDate;
    }

    public Date getSearchExamEndDate() {
        return searchExamEndDate;
    }

    public void setSearchExamEndDate(Date searchExamEndDate) {
        this.searchExamEndDate = searchExamEndDate;
    }

    public String getStatisticalDimension() {
        return statisticalDimension;
    }

    public void setStatisticalDimension(String statisticalDimension) {
        this.statisticalDimension = statisticalDimension;
    }

    public String getStatisticalDimensionOld() {
        return statisticalDimensionOld;
    }

    public void setStatisticalDimensionOld(String statisticalDimensionOld) {
        this.statisticalDimensionOld = statisticalDimensionOld;
    }

    public List<SelectItem> getStatisticalDimensionList() {
        return statisticalDimensionList;
    }

    public void setStatisticalDimensionList(List<SelectItem> statisticalDimensionList) {
        this.statisticalDimensionList = statisticalDimensionList;
    }

    public List<Object[]> getDataTableList() {
        return dataTableList;
    }

    public void setDataTableList(List<Object[]> dataTableList) {
        this.dataTableList = dataTableList;
    }

    public Integer getDataTableListCount() {
        return dataTableListCount;
    }

    public void setDataTableListCount(Integer dataTableListCount) {
        this.dataTableListCount = dataTableListCount;
    }
}