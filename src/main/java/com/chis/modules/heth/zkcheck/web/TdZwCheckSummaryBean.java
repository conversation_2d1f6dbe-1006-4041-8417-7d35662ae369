package com.chis.modules.heth.zkcheck.web;
import java.util.Date;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.logic.ZwPsnInfoDTO;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckSummary;
import com.chis.modules.heth.zkcheck.service.TdZwCheckSummaryService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * <p>类描述：质量控制考核结论录入 </p>
 * <AUTHOR>
 * date： 2022年08月13日
 **/
@ManagedBean(name="tdZwCheckSummaryBean")
@ViewScoped
public class TdZwCheckSummaryBean extends FacesEditBean {
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    protected SystemModuleServiceImpl systemModuleService= SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private TdZwCheckSummaryService checkSummaryService = (TdZwCheckSummaryService) SpringContextHolder.getBean(TdZwCheckSummaryService.class);
    /**查询条件-地区*/
    protected List<TsZone> zoneList;
    protected String searchZoneCode;
    protected String searchZoneName;
    /**查询条件-机构名称*/
    protected String searchOrgName;
    /**查询条件 - 考核日期开始 */
    protected Date searchCheckStartTime;
    /**查询条件 - 考核日期结束*/
    protected Date searchCheckEndTime;
    /**查询条件 被考核机构确认意见*/
    protected List<Integer> searchLeaderAdvList;
    /**查询条件 - 状态*/
    protected List<Integer> searchStateMarkList;
    /**列表rid*/
    private Integer rid;
    /** 质量控制考核汇总录入页面对象*/
    private TdZwCheckSummary checkSummary;
    private List<TsSimpleCode> checkTypeList;
    private Map<Integer,TsSimpleCode> checkTypeMap = new HashMap<>();
    private Date today;
    /** 人员弹出框类型 1专家，2组长*/
    private String psnSelectType;

    public TdZwCheckSummaryBean() {
        this.ifSQL = true;
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        if(null == this.zoneList || this.zoneList.size() <=0) {
            this.zoneList = this.systemModuleService.findZoneListWithAllZoneByFlag(false, tsZone.getZoneGb());
        }
        this.searchZoneCode = this.zoneList.get(0).getZoneGb();
        this.searchZoneName = this.zoneList.get(0).getZoneName();
        //考核日期
        this.searchCheckEndTime = new Date();
        this.searchCheckStartTime = DateUtils.getYearFirstDay(this.searchCheckEndTime);
        //状态初始化
        searchStateMarkList=new ArrayList<>();
        searchStateMarkList.add(0);
        this.searchAction();
        this.checkTypeList = this.commService.findNumSimpleCodesByTypeId("5554");
        if(ObjectUtil.isNotEmpty(checkTypeList)){
            checkTypeMap = new HashMap<>();
            for (TsSimpleCode t : checkTypeList){
                checkTypeMap.put(t.getRid(),t);
            }
        }
        today = new Date();
    }

    @Override
    public void addInit() {
        checkSummary = new TdZwCheckSummary();
        checkSummary.setFkByCheckTypeId(new TsSimpleCode());
        checkSummary.setFkByUnitId(new TsUnit());
        checkSummary.setCheckDate(today);
        checkSummary.setIfConfirm(1);
        checkSummary.setFkByCheckUnitId(Global.getUser().getTsUnit());
        checkSummary.setState(0);
        checkSummary.setDelMark(0);
    }

    @Override
    public void viewInit() {
        this.checkSummary = this.commService.find(TdZwCheckSummary.class,this.rid);
    }

    @Override
    public void modInit() {
        this.checkSummary = commService.find(TdZwCheckSummary.class,this.rid);
    }

    @Override
    public void saveAction() {
        try{
            if(!verifySave()) {
                return;
            }
            commService.upsertEntity(this.checkSummary);
            this.rid = checkSummary.getRid();
            this.modInit();
            JsfUtil.addSuccessMessage("暂存成功！");
            this.searchAction();
        }catch (Exception e){
            JsfUtil.addErrorMessage("暂存失败！");
            e.printStackTrace();
        }
    }

    public void submitAction() {
        try{
            if(!verifySubmit()){
                return;
            }else{
                this.checkSummary.setState(1);
                commService.upsertEntity(this.checkSummary);
                this.rid = checkSummary.getRid();
                this.viewInit();
                this.forwardViewPage();
                JsfUtil.addSuccessMessage("提交成功！");
                RequestContext.getCurrentInstance().update("tabView");
                this.searchAction();
            }
        }catch (Exception e){
            JsfUtil.addErrorMessage("提交失败！");
            e.printStackTrace();
        }
    }

    /**
     * <p>描述 暂存验证</p>
     *
     * @MethodAuthor gongzhe,2022/7/13 11:12,verifySave
     * @return boolean
     */
    private boolean verifySave(){
        boolean flag = true;
        if(this.checkSummary.getFkByCheckTypeId().getRid() == null){
            JsfUtil.addErrorMessage("考核类型不能为空！");
            flag = false;
        }
        if(this.checkSummary.getFkByUnitId().getRid() == null){
            JsfUtil.addErrorMessage("机构名称不能为空！");
            flag = false;
        }
        if(this.checkSummary.getCheckDate() == null){
            JsfUtil.addErrorMessage("考核日期不能为空！");
            flag = false;
        }
        if(this.checkSummary.getCheckAdv()!=null && this.checkSummary.getCheckAdv().length()>1000){
            JsfUtil.addErrorMessage("问题及建议超出限制的1000个字符！");
            flag = false;
        }
        if(this.checkSummary.getCheckExperts()!=null && this.checkSummary.getCheckExperts().length()>100){
            JsfUtil.addErrorMessage("考核专家超出限制的100个字符！");
            flag = false;
        }
        if(this.checkSummary.getCheckLeaders()!=null && this.checkSummary.getCheckLeaders().length()>100){
            JsfUtil.addErrorMessage("考核组组长超出限制的100个字符！");
            flag = false;
        }
        return flag;
    }

    private boolean verifySubmit(){
        boolean flag = true;
        if(this.checkSummary.getFkByCheckTypeId().getRid() == null){
            JsfUtil.addErrorMessage("考核类型不能为空！");
            flag = false;
        }
        if(this.checkSummary.getFkByUnitId().getRid() == null){
            JsfUtil.addErrorMessage("机构名称不能为空！");
            flag = false;
        }
        if(this.checkSummary.getCheckDate() == null){
            JsfUtil.addErrorMessage("考核日期不能为空！");
            flag = false;
        }
        if(StringUtils.isBlank(this.checkSummary.getCheckAdv())){
            JsfUtil.addErrorMessage("问题及建议不能为空！");
            flag = false;
        }else if(this.checkSummary.getCheckAdv().length()>1000){
            JsfUtil.addErrorMessage("问题及建议超出限制的1000个字符！");
            flag = false;
        }
        if(this.checkSummary.getIfConfirm() == null){
            JsfUtil.addErrorMessage("被考核机构确认意见不能为空！");
            flag = false;
        }
        if(StringUtils.isBlank(this.checkSummary.getCheckExperts())){
            JsfUtil.addErrorMessage("考核专家不能为空！");
            flag = false;
        }else if(this.checkSummary.getCheckExperts().length()>100){
            JsfUtil.addErrorMessage("考核专家超出限制的100个字符！");
            flag = false;
        }
        if(StringUtils.isBlank(this.checkSummary.getCheckLeaders())){
            JsfUtil.addErrorMessage("考核组组长不能为空！");
            flag = false;
        }else if(this.checkSummary.getCheckLeaders().length()>100){
            JsfUtil.addErrorMessage("考核组组长超出限制的100个字符！");
            flag = false;
        }
        return flag;
    }

    public void cancelAction(){
        try{
            this.checkSummary.setState(0);
            commService.upsertEntity(this.checkSummary);
            this.rid = checkSummary.getRid();
            modInitAction();
            JsfUtil.addSuccessMessage("撤销成功！");
            RequestContext.getCurrentInstance().update("tabView");
            this.searchAction();
        }catch (Exception e){
            JsfUtil.addErrorMessage("撤销失败！");
            e.printStackTrace();
        }
    }

    @Override
    public String[] buildHqls() {
        StringBuffer buffer = new StringBuffer();
        buffer.append(" from TD_ZW_CHECK_SUMMARY T ");
        buffer.append(" LEFT JOIN TS_UNIT T1 ON T.UNIT_ID = T1.RID ");
        buffer.append(" LEFT JOIN TS_ZONE T2 ON T1.ZONE_ID = T2.RID ");
        buffer.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T3.RID = T.CHECK_TYPE_ID ");
        buffer.append(" where T.DEL_MARK=0 ");
        if(StringUtils.isNotBlank(searchZoneCode)){
            buffer.append(" AND T2.ZONE_GB LIKE :zoneCode escape '\\\' ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        if(StringUtils.isNotBlank(searchOrgName)){
            buffer.append(" AND T1.UNITNAME LIKE :orgName escape '\\\' ");
            this.paramMap.put("orgName", "%" +StringUtils.convertBFH(this.searchOrgName) + "%");
        }
        if(null != searchCheckStartTime){
            buffer.append(" AND T.CHECK_DATE>= TO_DATE('").append(DateUtils.formatDate(this.searchCheckStartTime))
                    .append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if(null != searchCheckEndTime){
            buffer.append(" AND T.CHECK_DATE<= TO_DATE('").append(DateUtils.formatDate(this.searchCheckEndTime))
                    .append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if(!CollectionUtils.isEmpty(searchLeaderAdvList)){
            if(searchLeaderAdvList.size() == 1){
                buffer.append(" AND T.IF_CONFIRM= ").append(searchLeaderAdvList.get(0));
            }else{
                buffer.append(" AND T.IF_CONFIRM in(").append(StringUtils.list2string(searchLeaderAdvList,",")).append(")");
            }
        }

        if(!CollectionUtils.isEmpty(searchStateMarkList)){
            StringBuffer stateBuffer = new StringBuffer();
            for(Integer state : searchStateMarkList){
                stateBuffer.append(",").append(state);
            }
            //避免以后不只是两种状态 用IN语句查询 若最终确认仅两种状态 这里可省略
            buffer.append(" AND T.STATE IN (").append(stateBuffer.substring(1)).append(") ");
        }
        String searchSql = " select T.rid, CASE WHEN T2.ZONE_TYPE > 2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME, '_') + 1)  ELSE T2.FULL_NAME END ZONE_NAME, ";
        searchSql+=" T1.UNITNAME,T3.CODE_NAME, T.IF_CONFIRM,T.CHECK_DATE,T.STATE ";
        searchSql = searchSql + buffer.toString() + " order by T2.ZONE_GB asc,T1.UNITNAME asc,T.CHECK_DATE desc ";
        String countSql = " SELECT COUNT(*) " + buffer.toString();
        return new String[]{searchSql, countSql};
    }

    /**
     * <p>方法描述：标删</p>
     * @MethodAuthor： yzz
     * @Date：2022-08-13
     **/
    public void deleteAction(){
        if(rid==null){
            return;
        }
        try{
            checkSummaryService.deleteCheckSummaryByRid(rid);
            JsfUtil.addSuccessMessage("删除成功！");
            this.searchAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * <p>描述 考核类型切换事件</p>
     *
     * @MethodAuthor gongzhe,2022/7/12 16:43,changeCheckType
     * @return void
     */
    public void changeCheckType(){
        //清空机构名称
        checkSummary.setFkByUnitId(new TsUnit());
    }

    /**
     * <p>描述 机构选择</p>
     *
     * @MethodAuthor gongzhe,2022/7/12 16:52,selectOrgList
     * @return void
     */
    public void selectOrgList() {
        Map<String, Object> options = MapUtils.produceDialogMap(null,760,null,370);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> tmpList = new ArrayList<>();
        tmpList.add(this.checkTypeMap.get(this.checkSummary.getFkByCheckTypeId().getRid()).getExtendS1());
        paramMap.put("checkType", tmpList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectCheckOrgList", options, paramMap);
    }
    /**
     * <p>方法描述：选择机构</p>
     * @MethodAuthor qrr,2021年6月28日,onOrgSelect
     * */
    public void onOrgSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
            Object[] obj = (Object[]) selectedMap.get("selected");
            TsUnit unit = new TsUnit();
            unit.setRid(new Integer(obj[0].toString()));
            unit.setUnitname(StringUtils.objectToString(obj[2]));
            this.checkSummary.setFkByUnitId(unit);
        }
    }
    /**
     * <p>描述 用人单位</p>
     *
     * @MethodAuthor gongzhe,2022/7/13 11:03,selectPsnList
     * @return void
     */
    public void selectPsnList() {
        Map<String, Object> options = MapUtils.produceDialogMap(null,760,null,470);
        Map<String, List<String>> paramMap = new HashMap<>();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectCheckPsnList", options, paramMap);
    }

    /**
     * <p>描述 用人单位选择</p>
     *
     * @param event
     * @MethodAuthor gongzhe,2022/7/13 11:03,onPsnSelect
     * @return void
     */
    public void onPsnSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
            List<ZwPsnInfoDTO> obj = (List<ZwPsnInfoDTO>) selectedMap.get("selected");
            StringBuilder psnName = new StringBuilder();
            for(ZwPsnInfoDTO psnInfo : obj){
                psnName.append("，").append(psnInfo.getEmpName());
            }
            String name = psnName.substring(1);
            if("1".equals(psnSelectType)){
                String checkExperts = this.checkSummary.getCheckExperts();
                this.checkSummary.setCheckExperts(StringUtils.isBlank(checkExperts) ? name : checkExperts + "，" + name);
            }else if("2".equals(psnSelectType)){
                String checkLeaders = this.checkSummary.getCheckLeaders();
                this.checkSummary.setCheckLeaders(StringUtils.isBlank(checkLeaders) ? name : checkLeaders + "，" + name);
            }
        }
    }

    /**
     * <p>描述 清空专家</p>
     *
     * @MethodAuthor gongzhe,2022/7/13 11:03,clearSelectLeaders
     * @return void
     */
    public void clearSelectExperts(){
        this.checkSummary.setCheckExperts(null);
    }

    /**
     * <p>描述 清空组长</p>
     *
     * @MethodAuthor gongzhe,2022/7/13 11:04,clearSelectMembers
     * @return void
     */
    public void clearSelectLeaders(){
        this.checkSummary.setCheckLeaders(null);
    }

    /**
     * <p>描述 删除质控结果告知书</p>
     *
     * @MethodAuthor gongzhe,2022/7/13 11:04,delNoticeFile
     * @return void
     */
    public void delFile(){
        if(StringUtils.isNotBlank(this.checkSummary.getFilePath())){
            FileUtils.delFile(JsfUtil.getAbsolutePath()+this.checkSummary.getFilePath());
            this.checkSummary.setFilePath(null);
        }
    }


    /**
     * <p>描述 附件上传</p>
     *
     * @param event
     * @MethodAuthor gongzhe,2022/7/13 11:11,fileUpload
     * @return void
     */
    public void fileUpload(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            try {
                String errorMsg = FileUtils.veryFile(file.getInputstream(), file.getContentType(), file.getFileName(), "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    RequestContext.getCurrentInstance().update("tabView:editForm:fileUpload");
                    return;
                }
                String fileName = file.getFileName();// 文件名称
                String uuid = java.util.UUID.randomUUID().toString()
                        .replaceAll("-", "");
                String path = JsfUtil.getAbsolutePath();
                String relativePath = new StringBuffer("heth/zworgannex/")
                        .append(uuid)
                        .append(fileName.substring(fileName
                                .lastIndexOf("."))).toString();
                // 文件路径
                String filePath = new StringBuilder(path).append(
                        relativePath).toString();
                checkSummary.setFilePath(relativePath);
                FileUtils.copyFile(filePath, file.getInputstream());

                RequestContext.getCurrentInstance().execute(
                        "PF('FileDialog').hide()");
                RequestContext.getCurrentInstance().update(
                        "tabView:editForm");
                JsfUtil.addSuccessMessage("上传成功！");

            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("上传失败！");
                throw new RuntimeException(e);
            }
        }
    }


    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchOrgName() {
        return searchOrgName;
    }

    public void setSearchOrgName(String searchOrgName) {
        this.searchOrgName = searchOrgName;
    }

    public Date getSearchCheckStartTime() {
        return searchCheckStartTime;
    }

    public void setSearchCheckStartTime(Date searchCheckStartTime) {
        this.searchCheckStartTime = searchCheckStartTime;
    }

    public Date getSearchCheckEndTime() {
        return searchCheckEndTime;
    }

    public void setSearchCheckEndTime(Date searchCheckEndTime) {
        this.searchCheckEndTime = searchCheckEndTime;
    }

    public List<Integer> getSearchLeaderAdvList() {
        return searchLeaderAdvList;
    }

    public void setSearchLeaderAdvList(List<Integer> searchLeaderAdvList) {
        this.searchLeaderAdvList = searchLeaderAdvList;
    }

    public List<Integer> getSearchStateMarkList() {
        return searchStateMarkList;
    }

    public void setSearchStateMarkList(List<Integer> searchStateMarkList) {
        this.searchStateMarkList = searchStateMarkList;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwCheckSummary getCheckSummary() {
        return checkSummary;
    }

    public void setCheckSummary(TdZwCheckSummary checkSummary) {
        this.checkSummary = checkSummary;
    }

    public List<TsSimpleCode> getCheckTypeList() {
        return checkTypeList;
    }

    public void setCheckTypeList(List<TsSimpleCode> checkTypeList) {
        this.checkTypeList = checkTypeList;
    }

    public Map<Integer, TsSimpleCode> getCheckTypeMap() {
        return checkTypeMap;
    }

    public void setCheckTypeMap(Map<Integer, TsSimpleCode> checkTypeMap) {
        this.checkTypeMap = checkTypeMap;
    }

    public Date getToday() {
        return today;
    }

    public void setToday(Date today) {
        this.today = today;
    }

    public String getPsnSelectType() {
        return psnSelectType;
    }

    public void setPsnSelectType(String psnSelectType) {
        this.psnSelectType = psnSelectType;
    }
}
