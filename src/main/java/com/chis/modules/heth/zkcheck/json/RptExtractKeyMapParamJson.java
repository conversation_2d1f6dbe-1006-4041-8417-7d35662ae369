package com.chis.modules.heth.zkcheck.json;

import java.io.Serializable;

public class RptExtractKeyMapParamJson implements Serializable {
    private static final long serialVersionUID = 6190613177075647767L;
    private Integer key;
    private Integer value;

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}
