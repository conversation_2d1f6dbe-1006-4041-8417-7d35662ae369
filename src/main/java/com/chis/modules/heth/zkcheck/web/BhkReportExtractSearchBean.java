package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.javabean.TdTjBhkInfoBean;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.component.outputpanel.OutputPanel;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 体检报告抽取查询
 */
@ManagedBean(name = "bhkReportExtractSearchBean")
@ViewScoped
public class BhkReportExtractSearchBean extends FacesEditBean {

    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /***查询条件-抽取日期*/
    private Date searchSDate;
    private Date searchEDate;
    /***查询条件-姓名*/
    private String searchPersonName;
    /***查询条件-体检编号*/
    private String searchBhkCode;

    /***查询条件-抽取类别*/
    private String[] searchBhkType;
    private List<TsSimpleCode> bhkTypeList;

    private Object[] extractBhk;

    /**
     * 体检基本信息-详情
     */
    private TdTjBhkInfoBean tjBhkInfoBean = new TdTjBhkInfoBean();
    /*** 体检项目的布局表格-详情*/
    private OutputPanel archivePanel = (OutputPanel) JsfUtil.getApplication().createComponent(
            OutputPanel.COMPONENT_TYPE);

    /**详情 rid*/
    private Integer viewRids;

    public BhkReportExtractSearchBean() {
        this.ifSQL = true;
        // 抽取日期初始化
        this.searchSDate = DateUtils.getYearFirstDay(new Date());
        this.searchEDate = new Date();
        // 码表初始化
        initTsSimpleCode();
        this.searchAction();
    }

    /**
     * <p>Description：码表初始化 </p>
     * <p>Author： yzz 2024-06-29 </p>
     */
    public void initTsSimpleCode() {
        // 抽取类别
        this.bhkTypeList = commService.findSimpleCodesByTypeId("5621");
    }


    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();
        sql.append(" select T.RID, ");
        sql.append("        T3.CODE_NAME   as                               A1, ");
        sql.append("        T.PERSON_NAME  as                               A2, ");
        sql.append("        T.BHK_CODE     as                               A3, ");
        sql.append("        T.PRT_DATE     as                               A4, ");
        sql.append("        T4.CODE_NAME   as                               A5, ");
        sql.append("        T.TCHBADRSNTIM as                               A6, ");
        sql.append("        T7.CRPT_NAME   as                               A7, ");
        sql.append("        T.EXTRACT_DATE as                               A8, ");
        sql.append("        case when nvl(T.STATE, 0) > 0 then 1 else 0 end A9, ");
        sql.append("        T.ORG_ID       as                               A10 ");
        sql.append(" from TD_ZK_EXTRACT_BHK T ");
        sql.append(" inner join TS_ZONE T1 on T.ZONE_ID = T1.RID ");
        sql.append(" inner join TB_TJ_SRVORG T2 on T.ORG_ID = T2.RID ");
        sql.append(" left join TS_SIMPLE_CODE T3 on T.EXTRACT_TYPE_ID = T3.RID ");
        sql.append(" left join TS_SIMPLE_CODE T4 on T.BHK_RST_ID = T4.RID ");
        sql.append(" left join TB_TJ_CRPT T7 on T.ENTRUST_CRPT_ID = T7.RID ");
        sql.append(" where T2.REG_ORGID = ").append(Global.getUser().getTsUnit().getRid());
        // 抽取日期
        if (null != this.searchSDate) {
            sql.append(" AND T.EXTRACT_DATE >= TO_DATE(:searchSDate,'yyyy-MM-dd')");
            this.paramMap.put("searchSDate", DateUtils.formatDate(searchSDate, "yyyy-MM-dd"));
        }
        if (null != this.searchEDate) {
            sql.append(" AND T.EXTRACT_DATE <= TO_DATE(:searchEDate,'yyyy-MM-dd HH24:mi:ss')");
            this.paramMap.put("searchEDate", DateUtils.formatDate(searchEDate, "yyyy-MM-dd") + " 23:59:59");
        }
        // 姓名
        if (StringUtils.isNotBlank(this.searchPersonName)) {
            sql.append(" AND T.PERSON_NAME LIKE :searchPersonName escape '\\\'");
            this.paramMap.put("searchPersonName", "%" + StringUtils.convertBFH(this.searchPersonName.trim()) + "%");
        }
        // 体检编号
        if (StringUtils.isNotBlank(this.searchBhkCode)) {
            sql.append(" AND T.BHK_CODE LIKE :bhkCode escape '\\\'");
            this.paramMap.put("bhkCode", "%" + StringUtils.convertBFH(this.searchBhkCode.trim()) + "%");
        }
        // 抽取类别
        if (ObjectUtil.isNotEmpty(this.searchBhkType)) {
            sql.append(" AND T.EXTRACT_TYPE_ID in(:extractTypeId)");
            this.paramMap.put("extractTypeId", Arrays.asList(this.searchBhkType));
        }
        String searchSql = sql + " order by T.EXTRACT_DATE desc, T.BHK_CODE ";
        String countSql = " SELECT COUNT(*) from (" + sql + ")";
        return new String[]{searchSql, countSql};
    }


    @Override
    public void addInit() {

    }

    /**
     * <p>Description：详情前验证 </p>
     * <p>Author： yzz 2024-07-03 </p>
     */
    public void beforeViewInit(){
        viewRids=null;
        if (this.extractBhk == null || extractBhk[10] == null || extractBhk[3] == null) {
            return;
        }
        // 根据体检机构+体检编码查询体检记录
        String sql = " select RID from TD_TJ_BHK where BHKORG_ID =" + Integer.parseInt(extractBhk[10].toString())
                + " and BHK_CODE='" + extractBhk[3].toString() + "'";
        List<Object> bhkRids = commService.findDataBySqlNoPage(sql, null);

        if (CollectionUtils.isEmpty(bhkRids)) {
            JsfUtil.addErrorMessage("体检记录不存在！");
            return;
        }
        viewRids=Integer.parseInt(bhkRids.get(0).toString());
        /*初始化体检基本信息*/
        tjBhkInfoBean.setRid(viewRids);
        tjBhkInfoBean.setArchivePanel(archivePanel);
        tjBhkInfoBean.setIfManagedOrg(true);
        tjBhkInfoBean.initBhkInfo();
        this.forwardViewPage();
    }


    @Override
    public void viewInit() {
    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    public Date getSearchSDate() {
        return searchSDate;
    }
    public void setSearchSDate(Date searchSDate) {
        this.searchSDate = searchSDate;
    }
    public Date getSearchEDate() {
        return searchEDate;
    }
    public void setSearchEDate(Date searchEDate) {
        this.searchEDate = searchEDate;
    }
    public String getSearchPersonName() {
        return searchPersonName;
    }
    public void setSearchPersonName(String searchPersonName) {
        this.searchPersonName = searchPersonName;
    }
    public String getSearchBhkCode() {
        return searchBhkCode;
    }
    public void setSearchBhkCode(String searchBhkCode) {
        this.searchBhkCode = searchBhkCode;
    }

    public String[] getSearchBhkType() {
        return searchBhkType;
    }
    public void setSearchBhkType(String[] searchBhkType) {
        this.searchBhkType = searchBhkType;
    }
    public List<TsSimpleCode> getBhkTypeList() {
        return bhkTypeList;
    }
    public void setBhkTypeList(List<TsSimpleCode> bhkTypeList) {
        this.bhkTypeList = bhkTypeList;
    }
    public Object[] getExtractBhk() {
        return extractBhk;
    }
    public void setExtractBhk(Object[] extractBhk) {
        this.extractBhk = extractBhk;
    }

    public TdTjBhkInfoBean getTjBhkInfoBean() {
        return tjBhkInfoBean;
    }
    public void setTjBhkInfoBean(TdTjBhkInfoBean tjBhkInfoBean) {
        this.tjBhkInfoBean = tjBhkInfoBean;
    }
    public OutputPanel getArchivePanel() {
        return archivePanel;
    }
    public void setArchivePanel(OutputPanel archivePanel) {
        this.archivePanel = archivePanel;
    }
}
