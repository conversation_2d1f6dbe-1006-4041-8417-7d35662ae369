package com.chis.modules.heth.zkcheck.plugin;

import com.chis.modules.system.entity.TsRpt;
import com.chis.modules.system.enumn.SystemType;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @createDate 2015-03-04.
 */
public class ReportTemplatePluginObj {
    public static Set<TsRpt> dataSet;


    static {
        dataSet = new HashSet<TsRpt>();

        TsRpt rpt = new TsRpt();
        rpt = new TsRpt();
        rpt.setCreateDate(new Date());
        rpt.setCreateManid(1);
        rpt.setRmk("职业健康检查机构检查考核意见反馈表");
        rpt.setRptCod("HETH_ZK_1001");
        rpt.setRptnam("职业健康检查机构检查考核意见反馈表文书");
        rpt.setRptver(1);
        rpt.setSystemType(SystemType.ZK_CHECK);
        rpt.setRptpath("/rptWord/heth/HETH_ZK_1001.docx");
        dataSet.add(rpt);

        rpt = new TsRpt();
        rpt.setCreateDate(new Date());
        rpt.setCreateManid(1);
        rpt.setRmk("质量控制考核结果表");
        rpt.setRptCod("HETH_ZK_1002");
        rpt.setRptnam("质量控制考核结果表");
        rpt.setRptver(1);
        rpt.setSystemType(SystemType.ZK_CHECK);
        rpt.setRptpath("/rptWord/heth/HETH_ZK_1002.docx");
        dataSet.add(rpt);

        rpt = new TsRpt();
        rpt.setCreateDate(new Date());
        rpt.setCreateManid(1);
        rpt.setRmk("职业卫生技术服务机构质量监测结果");
        rpt.setRptCod("HETH_ZK_1003");
        rpt.setRptnam("职业卫生技术服务机构质量监测结果");
        rpt.setRptver(1);
        rpt.setSystemType(SystemType.ZK_CHECK);
        rpt.setRptpath("/rptWord/heth/HETH_ZK_1003.docx");
        dataSet.add(rpt);

        rpt = new TsRpt();
        rpt.setCreateDate(new Date());
        rpt.setCreateManid(1);
        rpt.setRmk("职业健康检查机构质量控制考核表");
        rpt.setRptCod("HETH_ZK_2001");
        rpt.setRptnam("职业健康检查机构质量控制考核表文书");
        rpt.setRptver(1);
        rpt.setSystemType(SystemType.ZK_CHECK);
        rpt.setRptpath("/rptWord/heth/HETH_ZK_2001.docx");
        dataSet.add(rpt);

        rpt = new TsRpt();
        rpt.setCreateDate(new Date());
        rpt.setCreateManid(1);
        rpt.setRmk("放射工作人员职业健康检查机构质量考核表");
        rpt.setRptCod("HETH_ZK_2002");
        rpt.setRptnam("放射工作人员职业健康检查机构质量考核表");
        rpt.setRptver(1);
        rpt.setSystemType(SystemType.ZK_CHECK);
        rpt.setRptpath("/rptWord/heth/HETH_ZK_2002.docx");
        dataSet.add(rpt);

        rpt = new TsRpt();
        rpt.setCreateDate(new Date());
        rpt.setCreateManid(1);
        rpt.setRmk("职业病诊疗机构现场考核表");
        rpt.setRptCod("HETH_ZK_2003");
        rpt.setRptnam("职业病诊疗机构现场考核表文书");
        rpt.setRptver(1);
        rpt.setSystemType(SystemType.ZK_CHECK);
        rpt.setRptpath("/rptWord/heth/HETH_ZK_2003.docx");
        dataSet.add(rpt);

        rpt = new TsRpt();
        rpt.setCreateDate(new Date());
        rpt.setCreateManid(1);
        rpt.setRmk("质量控制现场考核意见书");
        rpt.setRptCod("HETH_ZK_2004");
        rpt.setRptnam("质量控制现场考核意见书文书");
        rpt.setRptver(1);
        rpt.setSystemType(SystemType.ZK_CHECK);
        rpt.setRptpath("/rptWord/heth/HETH_ZK_2004.docx");
        dataSet.add(rpt);
    }
}
