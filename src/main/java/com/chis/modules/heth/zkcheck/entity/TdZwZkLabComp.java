
package com.chis.modules.heth.zkcheck.entity;

import javax.persistence.*;
import java.util.Date;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsSimpleCode;
import java.math.BigDecimal;

/**
 * 
 * <AUTHOR>
 * @createTime 2025-8-12
 */
@Entity
@Table(name = "TD_ZW_ZK_LAB_COMP")
@SequenceGenerator(name = "TdZwZkLabComp", sequenceName = "TD_ZW_ZK_LAB_COMP_SEQ", allocationSize = 1)
public class TdZwZkLabComp implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String planName;
	private Date planDate;
	private TsUnit fkByOrgId;
	private TsSimpleCode fkByCheckTypeId;
	private Integer state;
	private BigDecimal biochemicalTest;
	private BigDecimal routineTest;
	private BigDecimal bloodLead;
	private BigDecimal urinaryFluoride;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwZkLabComp() {
	}

	public TdZwZkLabComp(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwZkLabComp")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "PLAN_NAME")	
	public String getPlanName() {
		return planName;
	}

	public void setPlanName(String planName) {
		this.planName = planName;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "PLAN_DATE")			
	public Date getPlanDate() {
		return planDate;
	}

	public void setPlanDate(Date planDate) {
		this.planDate = planDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TsUnit getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TsUnit fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CHECK_TYPE_ID")			
	public TsSimpleCode getFkByCheckTypeId() {
		return fkByCheckTypeId;
	}

	public void setFkByCheckTypeId(TsSimpleCode fkByCheckTypeId) {
		this.fkByCheckTypeId = fkByCheckTypeId;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Column(name = "BIOCHEMICAL_TEST")	
	public BigDecimal getBiochemicalTest() {
		return biochemicalTest;
	}

	public void setBiochemicalTest(BigDecimal biochemicalTest) {
		this.biochemicalTest = biochemicalTest;
	}	
			
	@Column(name = "ROUTINE_TEST")	
	public BigDecimal getRoutineTest() {
		return routineTest;
	}

	public void setRoutineTest(BigDecimal routineTest) {
		this.routineTest = routineTest;
	}	
			
	@Column(name = "BLOOD_LEAD")	
	public BigDecimal getBloodLead() {
		return bloodLead;
	}

	public void setBloodLead(BigDecimal bloodLead) {
		this.bloodLead = bloodLead;
	}	
			
	@Column(name = "URINARY_FLUORIDE")	
	public BigDecimal getUrinaryFluoride() {
		return urinaryFluoride;
	}

	public void setUrinaryFluoride(BigDecimal urinaryFluoride) {
		this.urinaryFluoride = urinaryFluoride;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}