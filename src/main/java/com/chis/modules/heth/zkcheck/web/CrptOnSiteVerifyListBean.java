package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zkcheck.entity.*;
import com.chis.modules.heth.zkcheck.service.CrptOnSiteVerifyListService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.UnselectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.math.BigDecimal;
import java.util.*;

/**
 * 用人单位现场核查Bean
 */
@ManagedBean(name = "crptOnSiteVerifyListBean")
@ViewScoped
public class CrptOnSiteVerifyListBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final CrptOnSiteVerifyListService crptOnSiteVerifyService = SpringContextHolder.getBean(CrptOnSiteVerifyListService.class);

    private Integer rid;
    private TdZwCheckRpt tdZwCheckRpt;
    /**
     * 查询条件：用人单位地区集合
     */
    private List<TsZone> zoneList = new ArrayList<>();
    /**
     * 查询条件：用人单位地区编码
     */
    private String searchZoneCode;
    /**
     * 查询条件：用人单位地区名称
     */
    private String searchZoneName;
    /**
     * 查询条件：单位名称
     */
    private String searchCrptName;
    /**
     * 查询条件：报告日期-开始日期
     */
    private Date searchRptBeginDate;
    /**
     * 查询条件：报告日期-结束日期
     */
    private Date searchRptEndDate;
    /**
     * 查询条件：检测报告名称（编号）
     */
    private String searchRptNo;
    /**
     * 查询条件：状态
     * <pre>1：待核查</pre>
     * <pre>2：已核查</pre>
     */
    private String[] states;
    private List<SelectItem> stateList = new ArrayList<>();
    /**
     * 扣分原因码表
     */
    private List<TsSimpleCode> deductedList;
    /**
     * 扣分原因选择
     */
    private String deductInputId;
    private CheckDeductSelVO checkDeductSelVO;
    /**
     * 评估项结果码表
     */
    private Integer outcomeNotRid;
    private List<TsSimpleCode> outcomeList;
    /**
     * 是否可撤销
     */
    private Boolean quashMark;

    public CrptOnSiteVerifyListBean() {
        initSimpleCode();
        otherInit();
        searchAction();
    }

    private void initSimpleCode() {
        if (CollectionUtils.isEmpty(this.deductedList)) {
            this.deductedList = this.commService.findNumSimpleCodesByTypeId("5531");
        }
        if (CollectionUtils.isEmpty(this.outcomeList)) {
            this.outcomeList = this.commService.findNumSimpleCodesByTypeId("5585");
        }
        //记录否决项结果为否的码表RID
        for (TsSimpleCode simpleCode : this.outcomeList) {
            if ("0".equals(simpleCode.getExtendS1())) {
                this.outcomeNotRid = simpleCode.getRid();
                break;
            }
        }
    }

    private void otherInit() {
        this.ifSQL = true;
        //地区
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        //查询条件：地区
        if (CollectionUtils.isEmpty(this.zoneList)) {
            this.zoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "");
            this.searchZoneCode = this.zoneList.get(0).getZoneCode();
            this.searchZoneName = this.zoneList.get(0).getZoneName();
        }
        //报告日期
        this.searchRptEndDate = new Date();
        this.searchRptBeginDate = DateUtils.getYearFirstDay(this.searchRptEndDate);
        //状态
        this.states = new String[]{"1"};
        this.stateList.add(new SelectItem("1", "待核查"));
        this.stateList.add(new SelectItem("2", "已核查"));
        //初始化存在问题选择
        this.checkDeductSelVO = new CheckDeductSelVO();
    }

    @Override
    public String[] buildHqls() {
        String sql = buildSql();
        String h1 = "SELECT * FROM (" + sql + ")AA ORDER BY AA.P10, AA.P2, AA.P5 DESC";
        String h2 = "SELECT COUNT(*) FROM (" + sql + ")";
        return new String[]{h1, h2};
    }

    private String buildSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ");
        sql.append("    CR.RID          AS P0, ");
        sql.append("    Z.FULL_NAME     AS P1, ");
        sql.append("    CR.CRPT_NAME    AS P2, ");
        sql.append("    CR.LINK_MAN     AS P3, ");
        sql.append("    CR.LINK_PHONE   AS P4, ");
        sql.append("    CR.RPT_NO       AS P5, ");
        sql.append("    CR.RPT_DATE     AS P6, ");
        sql.append("    CR.STATE        AS P7, ");
        sql.append("    U.UNITNAME      AS P8, ");
        sql.append("    ''              AS P9, ");
        sql.append("    Z.ZONE_GB       AS P10 ");
        sql.append("FROM TD_ZW_CHECK_RPT CR ");
        sql.append("     LEFT JOIN TS_ZONE Z ON CR.ZONE_ID = Z.RID ");
        sql.append("     LEFT JOIN TS_UNIT U ON CR.UNIT_ID = U.RID ");
        sql.append("WHERE 1 = 1 ");
        // 用人单位地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sql.append(" AND Z.ZONE_GB LIKE :zoneCode ");
            this.paramMap.put("zoneCode", ZoneUtil.zoneSelect(this.searchZoneCode) + "%");
        }
        // 单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sql.append(" AND CR.CRPT_NAME LIKE :searchCrptName escape '\\\' ");
            this.paramMap.put("searchCrptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        // 报告日期
        if (ObjectUtil.isNotEmpty(this.searchRptBeginDate)) {
            sql.append(" AND CR.RPT_DATE >= TO_DATE(:searchRptBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchRptBeginDate", DateUtils.formatDate(this.searchRptBeginDate) + " 00:00:00");
        }
        if (ObjectUtil.isNotEmpty(this.searchRptEndDate)) {
            sql.append(" AND CR.RPT_DATE <= TO_DATE(:searchRptEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchRptEndDate", DateUtils.formatDate(this.searchRptEndDate) + " 23:59:59");
        }
        // 检测报告名称（编号）
        if (StringUtils.isNotBlank(this.searchRptNo)) {
            sql.append(" AND CR.RPT_NO LIKE :searchRptNo escape '\\\' ");
            this.paramMap.put("searchRptNo", "%" + StringUtils.convertBFH(this.searchRptNo.trim()) + "%");
        }
        // 状态
        sql.append(" AND CR.STATE IN (:searchStatesList) ");
        List<String> searchStatesList = Arrays.asList(this.states);
        if (CollectionUtils.isEmpty(searchStatesList)) {
            searchStatesList = new ArrayList<>();
            searchStatesList.add("1");
            searchStatesList.add("2");
        }
        this.paramMap.put("searchStatesList", searchStatesList);
        return sql.toString();
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<Object[]> result = CollectionUtil.castList(Object[].class, list);
        for (Object[] obj : result) {
            //地区去除省份
            String fullName = StringUtils.objectToString(obj[1]);
            obj[1] = fullName.substring(fullName.indexOf("_") + 1);
            //状态
            String stateOri = StringUtils.objectToString(obj[7]);
            if (stateOri.equals("1")) {
                obj[9] = "待核查";
            } else if (stateOri.equals("2")) {
                obj[9] = "已核查";
            } else {
                obj[9] = "待提交";
            }
        }
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {
        this.tdZwCheckRpt = this.commService.find(TdZwCheckRpt.class, this.rid);
        if (this.tdZwCheckRpt == null) {
            return;
        }
        this.quashMark = this.crptOnSiteVerifyService.selectMainTotalScoreValByRptId(this.rid);
        List<TdZwCheckTable> tdZwCheckTableList = this.crptOnSiteVerifyService.findTdZwCheckTableListByMainId(this.rid);
        initCheckTableVOList(tdZwCheckTableList);
    }

    /**
     * 加载服务真实性
     */
    private void initCheckTableVOList(List<TdZwCheckTable> tdZwCheckTableList) {
        this.tdZwCheckRpt.setCheckTableVOList(new ArrayList<CheckTableVO>());
        CheckTableVO checkTableVO = new CheckTableVO();
        // 如果没有保存过，需要初始化
        if (CollectionUtils.isEmpty(tdZwCheckTableList)) {
            TdZwCheckTable tdZwCheckTable = null;
            Integer checkTableId = null;
            Integer checkItemId = null;
            TdZwCheckItem tdZwCheckItem = null;
            Integer checkSubItemId = null;
            List<Object[]> list = this.crptOnSiteVerifyService.findInitTdZwCheckTableInfo();
            Integer userRid = Global.getUser().getRid();
            for (Object[] o : list) {
                Integer checkTableIdNew = ObjectUtil.convert(Integer.class, o[0]);
                if (checkTableId == null || !checkTableId.equals(checkTableIdNew)) {
                    //初始化现场质控考核评估表
                    CheckTableVO checkTableVONew = new CheckTableVO();
                    TdZwCheckTable tdZwCheckTableNew = new TdZwCheckTable();
                    tdZwCheckTable = tdZwCheckTableNew;
                    tdZwCheckTableNew.setCheckItemList(new ArrayList<TdZwCheckItem>());
                    tdZwCheckTableNew.setFkByMainId(this.tdZwCheckRpt);
                    checkTableVONew.setTdZwCheckTable(tdZwCheckTableNew);
                    checkTableVONew.setTableName(ObjectUtil.convert(String.class, o[1]));
                    checkTableId = checkTableIdNew;
                    tdZwCheckTableNew.setFkByCheckTableId(new TbZwZkBadrsnStand(checkTableId));
                    checkTableVO = checkTableVONew;
                    this.tdZwCheckRpt.getCheckTableVOList().add(checkTableVO);
                }
                Integer checkItemIdNew = ObjectUtil.convert(Integer.class, o[2]);
                Integer checkSubItemIdNew = ObjectUtil.convert(Integer.class, o[4]);
                if (checkItemIdNew == null || checkSubItemIdNew == null) {
                    continue;
                }
                if (!checkItemIdNew.equals(checkItemId)) {
                    //初始化现场质控考核项目
                    checkItemId = checkItemIdNew;
                    CheckTableItemVO checkTableItemVO1 = new CheckTableItemVO();
                    checkTableItemVO1.setItemName(ObjectUtil.convert(String.class, o[3]));
                    checkTableItemVO1.setFjx(true);
                    TdZwCheckItem tdZwCheckItemNew = new TdZwCheckItem();
                    tdZwCheckTable.getCheckItemList().add(tdZwCheckItemNew);
                    tdZwCheckItem = tdZwCheckItemNew;
                    tdZwCheckItemNew.setTdZwCheckSubList(new ArrayList<TdZwCheckSub>());
                    tdZwCheckItemNew.setFkByMainId(checkTableVO.getTdZwCheckTable());
                    tdZwCheckItemNew.setFkByItemId(new TsSimpleCode(checkItemId));
                    tdZwCheckItemNew.setCreateDate(new Date());
                    tdZwCheckItemNew.setCreateManid(userRid);
                    checkTableItemVO1.setTdZwCheckItem(tdZwCheckItemNew);
                    checkTableVO.getCheckTableItemVOList().add(checkTableItemVO1);
                }
                String subItemName = ObjectUtil.convert(String.class, o[8]);
                if (!checkSubItemIdNew.equals(checkSubItemId)) {
                    //初始化现场质控考核结果子表
                    checkSubItemId = checkSubItemIdNew;
                    CheckTableItemVO checkTableItemVO1 = new CheckTableItemVO();
                    checkTableItemVO1.getItemNameList().add(subItemName);
                    TdZwCheckSub tdZwCheckSub = new TdZwCheckSub();
                    tdZwCheckItem.getTdZwCheckSubList().add(tdZwCheckSub);
                    tdZwCheckSub.setFkByMainId(tdZwCheckItem);
                    tdZwCheckSub.setFkByScoreId(new TbZwZkScores(checkSubItemId));
                    tdZwCheckSub.setScore(ObjectUtil.convert(BigDecimal.class, o[5]));
                    tdZwCheckSub.setCreateDate(new Date());
                    tdZwCheckSub.setCreateManid(userRid);
                    tdZwCheckSub.setTitle(subItemName);

                    //计算总分值
                    if (tdZwCheckSub.getScore() != null) {
                        if (tdZwCheckItem.getCheckVal() == null) {
                            tdZwCheckItem.setCheckVal(new BigDecimal(0));
                        }
                        tdZwCheckItem.setCheckVal(tdZwCheckItem.getCheckVal().add(tdZwCheckSub.getScore()));
                    }
                    checkTableItemVO1.setTdZwCheckSub(tdZwCheckSub);
                    checkTableItemVO1.setFjx("3".equals(ObjectUtil.convert(String.class, o[6])));
                    checkTableItemVO1.setCheckDeductSelVO(new CheckDeductSelVO());
                    checkTableItemVO1.getCheckDeductSelVO().setScoreId(tdZwCheckSub.getFkByScoreId().getRid());
                    int size = checkTableVO.getCheckTableItemVOList().size();
                    CheckTableItemVO checkTableItemVO = checkTableVO.getCheckTableItemVOList().get(size - 1);
                    if (checkTableItemVO1.getFjx()) {
                        tdZwCheckSub.setScoreVal(null);
                    } else {
                        tdZwCheckSub.setScoreRstId(null);
                        checkTableItemVO.setFjx(false);
                    }
                    checkTableItemVO.getCheckTableSubItemVOList().add(checkTableItemVO1);
                } else {
                    int size1 = checkTableVO.getCheckTableItemVOList().size();
                    CheckTableItemVO checkTableItemVO = checkTableVO.getCheckTableItemVOList().get(size1 - 1);
                    int size2 = checkTableItemVO.getCheckTableSubItemVOList().size();
                    CheckTableItemVO checkTableItemVO1 = checkTableItemVO.getCheckTableSubItemVOList().get(size2 - 1);
                    checkTableItemVO1.getItemNameList().add(subItemName);
                    checkTableItemVO1.getTdZwCheckSub().setTitle(checkTableItemVO1.getTdZwCheckSub().getTitle() + subItemName);
                }
            }
        } else {
            for (TdZwCheckTable tdZwCheckTable : tdZwCheckTableList) {
                CheckTableVO checkTableVONew = new CheckTableVO();
                checkTableVONew.setTdZwCheckTable(tdZwCheckTable);
                checkTableVONew.setTableName(tdZwCheckTable.getFkByCheckTableId().getCheckName());
                this.tdZwCheckRpt.getCheckTableVOList().add(checkTableVONew);
                if (!CollectionUtils.isEmpty(tdZwCheckTable.getCheckItemList())) {
                    for (TdZwCheckItem tdZwCheckItem : tdZwCheckTable.getCheckItemList()) {
                        CheckTableItemVO checkTableItemVO1 = new CheckTableItemVO();
                        checkTableItemVO1.setTdZwCheckItem(tdZwCheckItem);
                        checkTableItemVO1.setItemName(tdZwCheckItem.getFkByItemId().getCodeName());
                        checkTableVONew.getCheckTableItemVOList().add(checkTableItemVO1);
                        boolean allFjx = true;
                        for (TdZwCheckSub tdZwCheckSub : tdZwCheckItem.getTdZwCheckSubList()) {
                            TbZwZkScores tbZwZkScores = tdZwCheckSub.getFkByScoreId();
                            tdZwCheckSub.setTitle("");
                            CheckTableItemVO checkTableItemVO2 = new CheckTableItemVO();
                            for (TbZwZkScoreIndex tbZwZkScoreIndex : tbZwZkScores.getIndexList()) {
                                String name = tbZwZkScoreIndex.getFkByIndexId().getCodeName();
                                if (StringUtils.isBlank(name)) {
                                    continue;
                                }
                                if (tbZwZkScoreIndex.getXh() != null) {
                                    name = tbZwZkScoreIndex.getIndexXh() + "、" + name;
                                }
                                checkTableItemVO2.getItemNameList().add(name);
                                tdZwCheckSub.setTitle(tdZwCheckSub.getTitle() + name);
                            }
                            tdZwCheckSub.setScore(tbZwZkScores.getScore());
                            if (tdZwCheckSub.getFkByScoreRstId() != null && tdZwCheckSub.getFkByScoreRstId().getRid() != null) {
                                tdZwCheckSub.setScoreRstId(tdZwCheckSub.getFkByScoreRstId().getRid());
                            }
                            checkTableItemVO2.setTdZwCheckSub(tdZwCheckSub);
                            if (tbZwZkScores.getFkByItemTypeId() != null && "3".equals(tbZwZkScores.getFkByItemTypeId().getExtendS1())) {
                                checkTableItemVO2.setFjx(true);
                                tdZwCheckSub.setScoreVal(null);
                            } else {
                                allFjx = false;
                                checkTableItemVO2.setFjx(false);
                                tdZwCheckSub.setScoreRstId(null);
                            }

                            checkTableItemVO2.setCheckDeductSelVO(new CheckDeductSelVO());
                            checkTableItemVO2.getCheckDeductSelVO().setScoreId(tdZwCheckSub.getFkByScoreId().getRid());
                            //初始化存在问题
                            List<String> selNameList = new ArrayList<>();
                            for (TdZwCheckDeduct tdZwCheckDeduct : tdZwCheckSub.getTdZwCheckDeductList()) {
                                checkTableItemVO2.getCheckDeductSelVO().getRidList().add(tdZwCheckDeduct.getFkByDeductId().getRid());
                                selNameList.add(tdZwCheckDeduct.getFkByDeductId().getFkByDeductId().getCodeName());
                            }
                            checkTableItemVO2.getCheckDeductSelVO().setSelName(StringUtils.list2string(selNameList, "；"));
                            checkTableItemVO1.getCheckTableSubItemVOList().add(checkTableItemVO2);
                        }
                        checkTableItemVO1.setFjx(allFjx);
                    }
                }
            }
        }
    }

    /**
     * 计算实得分
     *
     * @param checkTableItemVO 项目
     * @param tableName        评估表名称
     */
    public void calTotalRealScoreAction(CheckTableItemVO checkTableItemVO, String tableName) {
        calTotalRealScore(checkTableItemVO, tableName);
    }

    /**
     * 计算实得分
     *
     * @param checkTableItemVO 项目
     * @param tableName        评估表名称
     * @return 在得分大于分值的情况时返回true
     */
    public boolean calTotalRealScore(CheckTableItemVO checkTableItemVO, String tableName) {
        boolean verify = false;
        TdZwCheckItem tdZwCheckItem = checkTableItemVO.getTdZwCheckItem();
        BigDecimal bigDecimal = null;
        for (TdZwCheckSub tdZwCheckSub : tdZwCheckItem.getTdZwCheckSubList()) {
            if (tdZwCheckSub.getScoreVal() == null || tdZwCheckSub.getScore() == null) {
                continue;
            }
            if (tdZwCheckSub.getScoreVal().compareTo(tdZwCheckSub.getScore()) > 0) {
                String message = tableName + checkTableItemVO.getItemName() + tdZwCheckSub.getTitle() + "得分不能大于分值！";
                JsfUtil.addErrorMessage(message);
                verify = true;
            }
            if (bigDecimal == null) {
                bigDecimal = new BigDecimal(0);
            }
            bigDecimal = bigDecimal.add(tdZwCheckSub.getScoreVal());
        }
        if (!verify) {
            tdZwCheckItem.setScoreVal(bigDecimal);
        }
        return verify;
    }

    /**
     * 打开存在问题弹出框
     */
    public void openDeductSelectAction() {
        initAllDeductList();
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:deductDataTable");
        dataTable.setRows(10);
        dataTable.setFirst(0);
        RequestContext.getCurrentInstance().update("tabView:editForm:searchDeductCausePanel");
        RequestContext.getCurrentInstance().update("tabView:editForm:deductDataTable");
        RequestContext.getCurrentInstance().execute("PF('DeductCauseDialog').show()");
    }

    /**
     * 初始化打开存在问题弹出框
     */
    public void initAllDeductList() {
        this.checkDeductSelVO.setSearchName("");
        findAllDeductList();
        this.checkDeductSelVO.setShowList(new ArrayList<Object[]>());
        if (!CollectionUtils.isEmpty(this.checkDeductSelVO.getAllList())) {
            for (Object[] objects : this.checkDeductSelVO.getAllList()) {
                Integer recoveryInstRid = ObjectUtil.convert(Integer.class, objects[0]);
                if (recoveryInstRid == null) {
                    continue;
                }
                objects[3] = this.checkDeductSelVO.getRidList().contains(recoveryInstRid);
                this.checkDeductSelVO.getShowList().add(objects);
            }
        }
    }

    /**
     * 查询所有对应存在问题
     */
    private void findAllDeductList() {
        if (CollectionUtils.isEmpty(this.checkDeductSelVO.getAllList())) {
            this.checkDeductSelVO.setAllList(this.crptOnSiteVerifyService.findAllDeductList(this.checkDeductSelVO.getScoreId()));
        }
    }

    /**
     * 清空存在问题弹出框选择
     */
    public void emptyDeductSelectAction() {
        this.checkDeductSelVO.init();
    }

    /**
     * 保存存在问题弹出框选择
     */
    public void saveDeductSelectAction() {
        List<String> deductNameList = new ArrayList<>();
        this.checkDeductSelVO.setRidList(new ArrayList<Integer>());
        for (Object[] objects : this.checkDeductSelVO.getAllList()) {
            if (!ObjectUtil.convert(Boolean.class, objects[3], false)) {
                continue;
            }
            deductNameList.add(StringUtils.objectToString(objects[1]));
            this.checkDeductSelVO.getRidList().add(ObjectUtil.convert(Integer.class, objects[0]));
        }
        this.checkDeductSelVO.setSelName(StringUtils.list2string(deductNameList, "；"));
        RequestContext.getCurrentInstance().update(this.deductInputId);
        RequestContext.getCurrentInstance().execute("PF('DeductCauseDialog').hide()");
    }

    /**
     * 查询存在问题
     */
    public void searchDeductSelectAction() {
        findAllDeductList();
        this.checkDeductSelVO.setShowList(new ArrayList<Object[]>());
        if (!CollectionUtils.isEmpty(this.checkDeductSelVO.getAllList())) {
            for (Object[] objects : this.checkDeductSelVO.getAllList()) {
                Integer recoveryInstRid = ObjectUtil.convert(Integer.class, objects[0]);
                if (recoveryInstRid == null) {
                    continue;
                }
                String name = ObjectUtil.convert(String.class, objects[1]);
                if (StringUtils.isBlank(this.checkDeductSelVO.getSearchName())
                        || name.contains(this.checkDeductSelVO.getSearchName())) {
                    this.checkDeductSelVO.getShowList().add(objects);
                }
            }
        }
    }

    /**
     * 暂存
     */
    @Override
    public void saveAction() {
        try {
            if (CollectionUtils.isEmpty(this.tdZwCheckRpt.getCheckTableVOList())) {
                JsfUtil.addSuccessMessage("暂存成功！");
                return;
            }
            boolean verify = false;
            for (CheckTableVO checkTableVO : this.tdZwCheckRpt.getCheckTableVOList()) {
                for (CheckTableItemVO checkTableItemVO : checkTableVO.getCheckTableItemVOList()) {
                    boolean b = calTotalRealScore(checkTableItemVO, checkTableVO.getTableName());
                    if (b) {
                        verify = true;
                    }
                }
            }
            if (verify) {
                return;
            }
            List<TdZwCheckTable> checkTableList = new ArrayList<>();
            for (CheckTableVO checkTableVO : tdZwCheckRpt.getCheckTableVOList()) {
                checkTableList.add(checkTableVO.getTdZwCheckTable());
                for (CheckTableItemVO checkTableItemVO : checkTableVO.getCheckTableItemVOList()) {
                    for (CheckTableItemVO tableItemVO : checkTableItemVO.getCheckTableSubItemVOList()) {
                        TdZwCheckSub tdZwCheckSub = tableItemVO.getTdZwCheckSub();
                        tdZwCheckSub.setTdZwCheckDeductList(new ArrayList<TdZwCheckDeduct>());
                        pakCheckDedust(tableItemVO, tdZwCheckSub);
                    }
                }
            }
            this.crptOnSiteVerifyService.updateTdZwCheckTable(checkTableList);
            modInit();
            JsfUtil.addSuccessMessage("暂存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("暂存失败！");
        }
    }

    public void beforeSubmitAction() {
        boolean verify = false;
        for (CheckTableVO checkTableVO : tdZwCheckRpt.getCheckTableVOList()) {
            TdZwCheckTable checkTable = checkTableVO.getTdZwCheckTable();
            if (StringUtils.isBlank(checkTable.getCheckPsn())) {
                verify = true;
                JsfUtil.addErrorMessage(checkTableVO.getTableName() + "考核人不能为空！");
            }
            for (CheckTableItemVO checkTableItemVO : checkTableVO.getCheckTableItemVOList()) {
                for (CheckTableItemVO tableItemVO : checkTableItemVO.getCheckTableSubItemVOList()) {
                    TdZwCheckSub tdZwCheckSub = tableItemVO.getTdZwCheckSub();
                    tdZwCheckSub.setTdZwCheckDeductList(new ArrayList<TdZwCheckDeduct>());
                    boolean isFjx = Boolean.TRUE.equals(tableItemVO.getFjx());
                    boolean hasSelScoreRst = tdZwCheckSub.getScoreRstId() != null;
                    boolean hasScoreVal = tdZwCheckSub.getScoreVal() != null;
                    String name = checkTableVO.getTableName() + checkTableItemVO.getItemName() + tdZwCheckSub.getTitle();
                    if (isFjx && !hasSelScoreRst) {
                        verify = true;
                        JsfUtil.addErrorMessage("请选择" + name + "结果！");
                    }
                    if (!isFjx && !hasScoreVal) {
                        verify = true;
                        JsfUtil.addErrorMessage(name + "结果不能为空！");
                    }
                    if (!isFjx && hasScoreVal && tdZwCheckSub.getScore() != null
                            && tdZwCheckSub.getScoreVal().compareTo(tdZwCheckSub.getScore()) > 0) {
                        verify = true;
                        JsfUtil.addErrorMessage(name + "得分不能大于分值！");
                    }
                    boolean emptyDeductSel = CollectionUtils.isEmpty(tableItemVO.getCheckDeductSelVO().getRidList());
                    if (emptyDeductSel) {
                        if (isFjx && hasSelScoreRst && tdZwCheckSub.getScoreRstId().equals(this.outcomeNotRid)) {
                            verify = true;
                            JsfUtil.addErrorMessage(name + "结果选择否时，存在问题不能为空！");
                        }
                        if (!isFjx && hasScoreVal && tdZwCheckSub.getScoreVal().compareTo(tdZwCheckSub.getScore()) < 0) {
                            verify = true;
                            JsfUtil.addErrorMessage(name + "结果小于分值时，存在问题不能为空！");
                        }
                    } else {
                        if (isFjx && hasSelScoreRst && !tdZwCheckSub.getScoreRstId().equals(this.outcomeNotRid)) {
                            verify = true;
                            JsfUtil.addErrorMessage(name + "结果选择是时，存在问题必须为空！");
                        }
                        if (!isFjx && hasScoreVal && tdZwCheckSub.getScoreVal().compareTo(tdZwCheckSub.getScore()) == 0) {
                            verify = true;
                            JsfUtil.addErrorMessage(name + "结果等于分值时，存在问题必须为空！");
                        }
                        if (!verify) {
                            pakCheckDedust(tableItemVO, tdZwCheckSub);
                        }
                    }
                }
            }
        }
        if (verify) {
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmDialog1').show()");
    }

    /**
     * 提交
     */
    public void submitAction() {
        try {
            List<TdZwCheckTable> checkTableList = new ArrayList<>();
            for (CheckTableVO checkTableVO : tdZwCheckRpt.getCheckTableVOList()) {
                checkTableList.add(checkTableVO.getTdZwCheckTable());
            }
            this.crptOnSiteVerifyService.submitTdZwCheckRpt(this.rid, checkTableList);
            modInit();
            JsfUtil.addSuccessMessage("提交成功！");
            this.searchAction();
            RequestContext.getCurrentInstance().update("tabView:editForm");
            RequestContext.getCurrentInstance().execute("windowScrollTop();");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
     * 封装存在问题实体
     *
     * @param tableItemVO  项目
     * @param tdZwCheckSub 结果子表
     */
    private void pakCheckDedust(CheckTableItemVO tableItemVO, TdZwCheckSub tdZwCheckSub) {
        for (Integer rid : tableItemVO.getCheckDeductSelVO().getRidList()) {
            TdZwCheckDeduct tdZwCheckDeduct = new TdZwCheckDeduct();
            tdZwCheckDeduct.setFkByMainId(tdZwCheckSub);
            tdZwCheckDeduct.setFkByDeductId(new TbZwZkScoreDeduct(rid));
            tdZwCheckDeduct.setCreateDate(new Date());
            tdZwCheckDeduct.setCreateManid(Global.getUser().getRid());
            tdZwCheckSub.getTdZwCheckDeductList().add(tdZwCheckDeduct);
        }
    }

    public void beforeQuashAction() {
        if (!this.crptOnSiteVerifyService.selectMainTotalScoreValByRptId(this.rid)) {
            JsfUtil.addErrorMessage("已计算总得分无法撤销！");
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmDialog2').show()");
    }

    /**
     * 撤销
     */
    public void quashAction() {
        try {
            this.crptOnSiteVerifyService.updateTdZwCheckRptState(this.rid, 1);
            this.tdZwCheckRpt.setState(1);
            RequestContext.getCurrentInstance().update("tabView:editForm");
            RequestContext.getCurrentInstance().execute("windowScrollTop();");
            JsfUtil.addSuccessMessage("撤销成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwCheckRpt getTdZwCheckRpt() {
        return tdZwCheckRpt;
    }

    public void setTdZwCheckRpt(TdZwCheckRpt tdZwCheckRpt) {
        this.tdZwCheckRpt = tdZwCheckRpt;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public Date getSearchRptBeginDate() {
        return searchRptBeginDate;
    }

    public void setSearchRptBeginDate(Date searchRptBeginDate) {
        this.searchRptBeginDate = searchRptBeginDate;
    }

    public Date getSearchRptEndDate() {
        return searchRptEndDate;
    }

    public void setSearchRptEndDate(Date searchRptEndDate) {
        this.searchRptEndDate = searchRptEndDate;
    }

    public String getSearchRptNo() {
        return searchRptNo;
    }

    public void setSearchRptNo(String searchRptNo) {
        this.searchRptNo = searchRptNo;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public Integer getOutcomeNotRid() {
        return outcomeNotRid;
    }

    public void setOutcomeNotRid(Integer outcomeNotRid) {
        this.outcomeNotRid = outcomeNotRid;
    }

    public List<TsSimpleCode> getDeductedList() {
        return deductedList;
    }

    public void setDeductedList(List<TsSimpleCode> deductedList) {
        this.deductedList = deductedList;
    }

    public String getDeductInputId() {
        return deductInputId;
    }

    public void setDeductInputId(String deductInputId) {
        this.deductInputId = deductInputId;
    }

    public CheckDeductSelVO getCheckDeductSelVO() {
        return checkDeductSelVO;
    }

    public void setCheckDeductSelVO(CheckDeductSelVO checkDeductSelVO) {
        this.checkDeductSelVO = checkDeductSelVO;
    }

    public List<TsSimpleCode> getOutcomeList() {
        return outcomeList;
    }

    public void setOutcomeList(List<TsSimpleCode> outcomeList) {
        this.outcomeList = outcomeList;
    }

    public Boolean getQuashMark() {
        return quashMark;
    }

    public void setQuashMark(Boolean quashMark) {
        this.quashMark = quashMark;
    }
}
