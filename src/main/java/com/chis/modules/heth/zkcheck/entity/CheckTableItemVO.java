package com.chis.modules.heth.zkcheck.entity;

import java.util.ArrayList;
import java.util.List;

/**
 * 现场考核项目VO
 */
public class CheckTableItemVO implements java.io.Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 项目名称
     */
    private String itemName;
    private List<String> itemNameList;
    /**
     * 序号
     */
    private Integer xh;
    /**
     * 是否否决项
     */
    private Boolean fjx;
    /**
     * 现场质控考核项目
     */
    private TdZwCheckItem tdZwCheckItem;
    /**
     * 现场质控考核结果子表
     */
    private TdZwCheckSub tdZwCheckSub;
    /**
     * 扣分原因
     */
    private CheckDeductSelVO checkDeductSelVO;
    /**
     * 现场考核子项目VO
     */
    private List<CheckTableItemVO> checkTableSubItemVOList;

    public CheckTableItemVO() {
        this.itemNameList = new ArrayList<>();
        this.checkTableSubItemVOList = new ArrayList<>();
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public List<String> getItemNameList() {
        return itemNameList;
    }

    public void setItemNameList(List<String> itemNameList) {
        this.itemNameList = itemNameList;
    }

    public Integer getXh() {
        return xh;
    }

    public void setXh(Integer xh) {
        this.xh = xh;
    }

    public Boolean getFjx() {
        return fjx;
    }

    public void setFjx(Boolean fjx) {
        this.fjx = fjx;
    }

    public TdZwCheckItem getTdZwCheckItem() {
        return tdZwCheckItem;
    }

    public void setTdZwCheckItem(TdZwCheckItem tdZwCheckItem) {
        this.tdZwCheckItem = tdZwCheckItem;
    }

    public TdZwCheckSub getTdZwCheckSub() {
        return tdZwCheckSub;
    }

    public void setTdZwCheckSub(TdZwCheckSub tdZwCheckSub) {
        this.tdZwCheckSub = tdZwCheckSub;
    }

    public CheckDeductSelVO getCheckDeductSelVO() {
        return checkDeductSelVO;
    }

    public void setCheckDeductSelVO(CheckDeductSelVO checkDeductSelVO) {
        this.checkDeductSelVO = checkDeductSelVO;
    }

    public List<CheckTableItemVO> getCheckTableSubItemVOList() {
        return checkTableSubItemVOList;
    }

    public void setCheckTableSubItemVOList(List<CheckTableItemVO> checkTableSubItemVOList) {
        this.checkTableSubItemVOList = checkTableSubItemVOList;
    }
}
