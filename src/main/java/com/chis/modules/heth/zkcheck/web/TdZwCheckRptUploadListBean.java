package com.chis.modules.heth.zkcheck.web;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.entity.TbTjCrptIndepend;
import com.chis.modules.heth.comm.json.CrptDialogParamJson;
import com.chis.modules.heth.zkcheck.entity.TdZwCheckRpt;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.UploadedFile;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * <p>类描述： 职业卫生技术服务机构检测报告上传 </p>
 * @ClassAuthor： pw 2023/6/16
 **/
@ManagedBean(name = "tdZwCheckRptUploadListBean")
@ViewScoped
public class TdZwCheckRptUploadListBean extends ZwAndRadCheckRptUploadCommBean {

    /** 查询条件：单位名称 */
    private String searchCrptName;
    /** 编辑页 用于区别检测报告附件与文审材料附件 0检测报告附件 1文审材料附件 */
    private Integer uploadType;

    public TdZwCheckRptUploadListBean(){
        //状态初始化
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem("0", "待提交"));
        this.stateList.add(new SelectItem("1", "待审核"));
        this.stateList.add(new SelectItem("3", "已退回"));
        this.stateList.add(new SelectItem("2", "审核通过"));
        //默认选中待提交 已退回
        this.states = new String[]{"0","3"};
        this.readOnly = Boolean.TRUE;
        this.searchAction();
    }

    @Override
    public void addInit() {
        this.tdZwCheckRpt = new TdZwCheckRpt();
        this.tdZwCheckRpt.setRptDate(new Date());
    }

    @Override
    public void modInit() {
        this.initCheckRpt();
    }

    @Override
    public void saveAction() {
        if(!this.validateBeforeSave(false)){
            return;
        }
        try{
            if(null == this.tdZwCheckRpt.getRid()){
                this.tdZwCheckRpt.setState(0);
            }
            this.tdZwCheckRpt.setFkByUnitId(Global.getUser().getTsUnit());
            this.tdZwCheckRptLisService.saveOrUpdateCheckRptUnFillUnit(this.tdZwCheckRpt);
            RequestContext.getCurrentInstance().update("tabView");
            JsfUtil.addSuccessMessage("暂存成功！");
            this.searchAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("暂存失败！");
        }
    }

    @Override
    public String[] buildHqls() {
        StringBuilder sqlBuffer = new StringBuilder();
        sqlBuffer.append(" SELECT T.RID,T.RPT_NO,T.RPT_NAME, ")
                .append(" CASE WHEN T2.ZONE_TYPE >2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME,'_')+1) ELSE T2.FULL_NAME END ZONE_NAME, ")
                .append(" T.CRPT_NAME,T.LINK_MAN,T.LINK_PHONE,T.RPT_DATE,T.STATE,T.BACK_RAN ")
                .append(" FROM TD_ZW_CHECK_RPT T ")
                .append(" LEFT JOIN TS_ZONE T2 ON T.ZONE_ID=T2.RID ")
                .append(" WHERE T.UNIT_ID=").append(Global.getUser().getTsUnit().getRid());

        //报告日期
        if (null != this.searchReportBeginDate) {
            sqlBuffer.append("   AND T.RPT_DATE >= TO_DATE(:searchReportBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchReportBeginDate", DateUtils.formatDate(this.searchReportBeginDate) + " 00:00:00");
        }
        if (null != this.searchReportEndDate) {
            sqlBuffer.append("   AND T.RPT_DATE <= TO_DATE(:searchReportEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            this.paramMap.put("searchReportEndDate", DateUtils.formatDate(this.searchReportEndDate) + " 23:59:59");
        }
        //单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sqlBuffer.append("  AND T.CRPT_NAME LIKE :crptName ");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchCrptName).trim()) + "%");
        }
        //检测报告编号
        if (StringUtils.isNotBlank(this.searchRptNo)) {
            sqlBuffer.append("  AND T.RPT_NO LIKE :rptNo ");
            this.paramMap.put("rptNo", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchRptNo).trim()) + "%");
        }
        //状态
        if (null != this.states && this.states.length>0) {
            sqlBuffer.append(" AND T.STATE IN (:state)");
            this.paramMap.put("state", Arrays.asList(this.states));
        }
        String h2 = "SELECT COUNT(*) FROM (" + sqlBuffer+ ")";
        String h1 = "SELECT * FROM (" + sqlBuffer + ")AA  ORDER BY AA.RPT_NO DESC ";
        return new String[] { h1, h2 };
    }

    /**
     * <p>方法描述： 删除 </p>
     * @MethodAuthor： pw 2023/6/16
     **/
    public void deleteAction(){
        if (this.rid == null) {
            return;
        }
        try {
            this.tdZwCheckRptLisService.deleteByRid(this.rid);
            JsfUtil.addSuccessMessage("删除成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * <p>方法描述： 提交前校验 </p>
     * @MethodAuthor： pw 2023/6/16
     **/
    public void beforeSubmit(){
        if(this.validateBeforeSave(true)){
            RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show()");
        }
    }

    /**
     * <p>方法描述： 提交 </p>
     * @MethodAuthor： pw 2023/6/16
     **/
    public void submitAction() {
        try{
            this.tdZwCheckRpt.setState(1);
            this.tdZwCheckRpt.setBackRan(null);
            this.tdZwCheckRpt.setFkByUnitId(Global.getUser().getTsUnit());
            this.tdZwCheckRptLisService.saveOrUpdateCheckRptUnFillUnit(this.tdZwCheckRpt);
            RequestContext.getCurrentInstance().update("tabView");
            JsfUtil.addSuccessMessage("提交成功！");
            this.searchAction();
            this.rid = this.tdZwCheckRpt.getRid();
            this.viewInitAction();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
     * <p>方法描述： 附件上传 </p>
     * @MethodAuthor： pw 2023/6/16
     **/
    public void fileUpload(FileUploadEvent event) {
        if (null == event || null == this.uploadType) {
            return;
        }
        UploadedFile file = event.getFile();
        try {
            // 文件名称
            String fileName = file.getFileName();
            String uuid = java.util.UUID.randomUUID().toString().replaceAll("-", "");
            String path = JsfUtil.getAbsolutePath();
            String relativePath = "heth/zkcheckjs/checkRpt/" + uuid + fileName.substring(fileName.lastIndexOf("."));
            // 文件路径
            String filePath = path + relativePath;
            if(0 == this.uploadType) {
                String errorMsg = FileUtils.veryFile(file.getInputstream(), file.getContentType(), file.getFileName(), "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    RequestContext.getCurrentInstance().update("tabView:editForm:fileUpload");
                    return;
                }
                FileUtils.copyFile(filePath, file.getInputstream());
                // 文件路径
                this.tdZwCheckRpt.setFilePath(relativePath);
                RequestContext currentInstance = RequestContext.getCurrentInstance();
                currentInstance.execute("PF('FileDialog').hide();");
                currentInstance.update("tabView:editForm:rptPanel");
                JsfUtil.addSuccessMessage("上传成功！");

            }
            if(1 == this.uploadType) {
                String errorMsg = FileUtils.veryFile(file.getInputstream(), file.getContentType(), file.getFileName(), "7");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    RequestContext.getCurrentInstance().update("tabView:editForm:sourceFileUpload");
                    return;
                }
                FileUtils.copyFile(filePath, file.getInputstream());
                // 文件路径
                this.tdZwCheckRpt.setSourceFilePath(relativePath);
                RequestContext currentInstance = RequestContext.getCurrentInstance();
                currentInstance.execute("PF('SourceFileDialog').hide();");
                currentInstance.update("tabView:editForm:rptPanel");
                JsfUtil.addSuccessMessage("上传成功！");

            }
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("上传失败！");
            throw new RuntimeException(e);
        }
    }

    /**
     * <p>方法描述： 删除附件 </p>
     * @MethodAuthor： pw 2023/6/16
     **/
    public void delRptFile() {
        if(null == this.uploadType){
            return;
        }
        try {
            if(0 == this.uploadType){
                tdZwCheckRpt.setFilePath(null);
            }else{
                this.tdZwCheckRpt.setSourceFilePath(null);
            }
            JsfUtil.addSuccessMessage("删除成功！");
            RequestContext.getCurrentInstance().update("tabView:editForm:rptPanel");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * <p>方法描述：企业信息弹框 </p>
     * @MethodAuthor： pw 2023/6/16
     **/
    public void selectCrptList() {
        Map<String, Object> options = MapUtils.produceDialogMap(1080,1050,520,505);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<String>();
        paramList = new ArrayList<String>();
        CrptDialogParamJson json = new CrptDialogParamJson();
        json.setBusType("4");
        json.setIfShowOtherZone(true);
        json.setIfSearchZoneDefault(false);
        json.setAreaValidate(3);
        if(this.tdZwCheckRpt!=null&&StringUtils.isNotBlank(this.tdZwCheckRpt.getCrptName())){
            json.setSearchCrptName(this.tdZwCheckRpt.getCrptName());
        }
        paramList.add(JSON.toJSONString(json));
        paramMap.put("jsonParam", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        String useNewCrptSelect = this.commService.findParamValue("USE_NEW_CRPT_SELECT");
        String crptSelectPath = "/webapp/heth/comm/crptCommSelectList";
        if ("1".equals(useNewCrptSelect)) {
            crptSelectPath = "/webapp/heth/comm/crptCommSelectListNew";
        }
        requestContext.openDialog(crptSelectPath, options, paramMap);
    }

    /**
     * <p>方法描述： 选择用人单位 </p>
     * @MethodAuthor： pw 2023/6/16
     **/
    public void onCrptSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            TbTjCrptIndepend tbTjCrptIndepend = (TbTjCrptIndepend) selectedMap.get("selectCrptIndepend");
            TbTjCrpt tbTjCrpt = tbTjCrptIndepend.getFkByCrptId();
            this.tdZwCheckRpt.setFkByCrptId(tbTjCrpt);
            this.tdZwCheckRpt.setFkByZoneId(tbTjCrpt.getTsZoneByZoneId());
            this.tdZwCheckRpt.setCrptName(tbTjCrpt.getCrptName());
            this.tdZwCheckRpt.setAddress(tbTjCrpt.getAddress());
            this.tdZwCheckRpt.setLinkMan(tbTjCrptIndepend.getLinkman2());
            this.tdZwCheckRpt.setLinkPhone(tbTjCrptIndepend.getLinkphone2());
            this.tdZwCheckRpt.setFkByCrptSizeId(tbTjCrpt.getTsSimpleCodeByCrptSizeId());
            this.tdZwCheckRpt.setCreditCode(tbTjCrpt.getInstitutionCode());
            this.tdZwCheckRpt.setFkByIndusTypeId(tbTjCrpt.getTsSimpleCodeByIndusTypeId());
            this.tdZwCheckRpt.setFkByEconomyId(tbTjCrpt.getTsSimpleCodeByEconomyId());
        }
    }

    /**
     * <p>方法描述：上传窗口打开 </p>
     * @MethodAuthor： pw 2023/6/17
     **/
    public void beforeUpload(){
        if(null == this.uploadType){
            return;
        }
        if(0 == this.uploadType){
            RequestContext.getCurrentInstance().execute("PF('FileDialog').show()");
            RequestContext.getCurrentInstance().update("fileDialog");
        }else if(1 == this.uploadType){
            RequestContext.getCurrentInstance().execute("PF('SourceFileDialog').show()");
            RequestContext.getCurrentInstance().update("sourceFileDialog");
        }
    }

    /**
     * <p>方法描述： 暂存与提交校验 </p>
     * @MethodAuthor： pw 2023/6/17
     **/
    private boolean validateBeforeSave(boolean ifSubmit){
        if(null == this.tdZwCheckRpt){
            return false;
        }
        boolean flag = true;
        if(null == this.tdZwCheckRpt.getFkByCrptId() || null == this.tdZwCheckRpt.getFkByCrptId().getRid()){
            JsfUtil.addErrorMessage("单位名称不能为空！");
            flag=false;
        }
        if(StringUtils.isBlank(this.tdZwCheckRpt.getRptNo())){
            JsfUtil.addErrorMessage("检测报告编号不能为空！");
            flag=false;
        }
        if(StringUtils.isNotBlank(this.tdZwCheckRpt.getRptNo()) && this.tdZwCheckRpt.getRptNo().length() > 50){
            JsfUtil.addErrorMessage("检测报告编号不能超过50个字！");
            flag=false;
        }
        if(StringUtils.isBlank(this.tdZwCheckRpt.getRptName())){
            JsfUtil.addErrorMessage("检测报告名称不能为空！");
            flag=false;
        }
        if(StringUtils.isNotBlank(this.tdZwCheckRpt.getRptName()) && this.tdZwCheckRpt.getRptName().length() > 100){
            JsfUtil.addErrorMessage("检测报告名称不能超过100个字！");
            flag=false;
        }
        if(null == this.tdZwCheckRpt.getRptDate()){
            JsfUtil.addErrorMessage("报告日期不能为空！");
            flag=false;
        }
        if(null != this.tdZwCheckRpt.getRptDate() && this.tdZwCheckRpt.getRptDate().after(new Date())){
            JsfUtil.addErrorMessage("报告日期不能大于当天！");
            flag=false;
        }
        if(StringUtils.isNotBlank(this.tdZwCheckRpt.getWorkName()) && this.tdZwCheckRpt.getWorkName().length() > 500){
            JsfUtil.addErrorMessage("工作场所名称不能超过500个字！");
            flag=false;
        }
        if(ifSubmit){
            if(StringUtils.isBlank(this.tdZwCheckRpt.getWorkName())){
                JsfUtil.addErrorMessage("工作场所名称不能为空！");
                flag=false;
            }
            if(StringUtils.isBlank(this.tdZwCheckRpt.getFilePath())){
                JsfUtil.addErrorMessage("请上传检测报告附件！");
                flag=false;
            }
            if(StringUtils.isBlank(this.tdZwCheckRpt.getSourceFilePath())){
                JsfUtil.addErrorMessage("请上传文审材料附件！");
                flag=false;
            }
        }

        return flag;
    }

    @Override
    public void processData(List<?> list) {

    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public Integer getUploadType() {
        return uploadType;
    }

    public void setUploadType(Integer uploadType) {
        this.uploadType = uploadType;
    }
}
