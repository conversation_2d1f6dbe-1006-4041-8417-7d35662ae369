package com.chis.modules.heth.zkcheck.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.chis.modules.heth.zkcheck.entity.TbZwZkRstRule;
import com.chis.modules.system.service.AbstractTemplate;

@Service
@Transactional(readOnly = false)
public class TbZwZkRstRuleService  extends AbstractTemplate{
	/**
 	 * <p>方法描述：查询考核配置规则</p>
 	 * @MethodAuthor qrr,2021年9月19日,findTbZwZkRstRule
	 * */
	@Transactional(readOnly = true)
	public List<TbZwZkRstRule> findTbZwZkRstRule(Integer checkTypeRid) {
		StringBuffer hql = new StringBuffer();
		hql.append("SELECT  T FROM TbZwZkRstRule T WHERE 1=1 ");
		if (null!=checkTypeRid) {
			hql.append(" and t.fkByCheckTypeId.rid =").append(checkTypeRid);
		}
		hql.append(" ORDER BY T.xh");
		List<TbZwZkRstRule> list = this.findHqlResultList(hql.toString());
		if (!CollectionUtils.isEmpty(list)) {
			for (TbZwZkRstRule t : list) {
				t.getRstRuleSubs().size();
			}
		}
		return list;
	}
}
