package com.chis.modules.heth.zkcheck.entity;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSimpleCode;

import org.springframework.util.CollectionUtils;

import javax.persistence.*;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-6-26
 */
@Entity
@Table(name = "TD_ZW_ZK_CHECK_SUB")
@SequenceGenerator(name = "TdZwZkCheckSub", sequenceName = "TD_ZW_ZK_CHECK_SUB_SEQ", allocationSize = 1)
public class TdZwZkCheckSub implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwZkCheckItem fkByMainId;
	private TbZwZkScores fkByScoreId;
	private BigDecimal scoreVal;
	private String rmk;
	private Integer techPsn;
	private Integer mediumPsn;
	private BigDecimal mediumPsnRate;
	private Integer externalPsn;
	private BigDecimal externalPsnRate;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	/**评分结果*/
	private TsSimpleCode fkByScoreRstId;

	private List<TdZwZkCheckDeduct> checkDeductList;

	/** 临时字段 扣分原因 */
	private String deductCause;
	/** 临时字段 评分结果rid */
	private Integer soreRstRid;
	/**无需考核标记*/
	private Integer assessMark;

	/**存在问题*/
	private String deductRsn;
	/** 评分结果rid */
	private TbZwZkScoreOption fkByRstId;
	/** 临时字段 评分结果rid */
	private Integer rstRid;

	/**是否显示红星必填标记*/
	private boolean ifShowRedStar;

	public TdZwZkCheckSub() {
	}

	public TdZwZkCheckSub(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwZkCheckSub")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwZkCheckItem getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwZkCheckItem fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "SCORE_ID")			
	public TbZwZkScores getFkByScoreId() {
		return fkByScoreId;
	}

	public void setFkByScoreId(TbZwZkScores fkByScoreId) {
		this.fkByScoreId = fkByScoreId;
	}	
			
	@Column(name = "SCORE_VAL")	
	public BigDecimal getScoreVal() {
		return scoreVal;
	}

	public void setScoreVal(BigDecimal scoreVal) {
		this.scoreVal = scoreVal;
	}	
			
	@Column(name = "RMK")	
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}	
			
	@Column(name = "TECH_PSN")	
	public Integer getTechPsn() {
		return techPsn;
	}

	public void setTechPsn(Integer techPsn) {
		this.techPsn = techPsn;
	}	
			
	@Column(name = "MEDIUM_PSN")	
	public Integer getMediumPsn() {
		return mediumPsn;
	}

	public void setMediumPsn(Integer mediumPsn) {
		this.mediumPsn = mediumPsn;
	}	
			
	@Column(name = "MEDIUM_PSN_RATE")	
	public BigDecimal getMediumPsnRate() {
		return mediumPsnRate;
	}

	public void setMediumPsnRate(BigDecimal mediumPsnRate) {
		this.mediumPsnRate = mediumPsnRate;
	}	
			
	@Column(name = "EXTERNAL_PSN")	
	public Integer getExternalPsn() {
		return externalPsn;
	}

	public void setExternalPsn(Integer externalPsn) {
		this.externalPsn = externalPsn;
	}	
			
	@Column(name = "EXTERNAL_PSN_RATE")	
	public BigDecimal getExternalPsnRate() {
		return externalPsnRate;
	}

	public void setExternalPsnRate(BigDecimal externalPsnRate) {
		this.externalPsnRate = externalPsnRate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByMainId",orphanRemoval=true)
	public List<TdZwZkCheckDeduct> getCheckDeductList() {
		return checkDeductList;
	}

	public void setCheckDeductList(List<TdZwZkCheckDeduct> checkDeductList) {
		this.checkDeductList = checkDeductList;
	}

	@Transient
	public String getDeductCause() {
		deductCause = null;
		if(!CollectionUtils.isEmpty(checkDeductList)){
			//排序
			Collections.sort(checkDeductList, new Comparator<TdZwZkCheckDeduct>() {
				@Override
				public int compare(TdZwZkCheckDeduct o1, TdZwZkCheckDeduct o2) {
					Integer xh1 = null == o1.getFkByDeductId() ? null : o1.getFkByDeductId().getXh();
					Integer xh2 = null == o2.getFkByDeductId() ? null : o2.getFkByDeductId().getXh();
					if(null != xh1 && null != xh2){
						return xh1.compareTo(xh2);
					}else if(null == xh2 && null != xh1){
						return 1;
					}else if(null == xh1 && null != xh2){
						return -1;
					}
					return 0;
				}
			});
			StringBuffer buffer = new StringBuffer();
			for(TdZwZkCheckDeduct checkDeduct : checkDeductList){
				String deductStr = (null == checkDeduct.getFkByDeductId() ||
						null == checkDeduct.getFkByDeductId().getFkByDeductId()) ? null :
						checkDeduct.getFkByDeductId().getFkByDeductId().getCodeName();
				if(StringUtils.isNotBlank(deductStr)){
					buffer.append("；").append(deductStr);
				}
			}
			deductCause = buffer.toString();
			if(StringUtils.isNotBlank(deductCause)){
				deductCause = deductCause.substring(1);
			}
		}
		return deductCause;
	}

	public void setDeductCause(String deductCause) {
		this.deductCause = deductCause;
	}
	@ManyToOne
	@JoinColumn(name="SCORE_RST_ID")
	public TsSimpleCode getFkByScoreRstId() {
		return fkByScoreRstId;
	}

	public void setFkByScoreRstId(TsSimpleCode fkByScoreRstId) {
		this.fkByScoreRstId = fkByScoreRstId;
	}

	@Transient
	public Integer getSoreRstRid() {
		return soreRstRid;
	}

	public void setSoreRstRid(Integer soreRstRid) {
		this.soreRstRid = soreRstRid;
	}

	@Column(name="ASSESS_MARK")
	public Integer getAssessMark() {
		return assessMark;
	}

	public void setAssessMark(Integer assessMark) {
		this.assessMark = assessMark;
	}

	@Column(name = "DEDUCT_RSN")
	public String getDeductRsn() {
		return deductRsn;
	}
	public void setDeductRsn(String deductRsn) {
		this.deductRsn = deductRsn;
	}

	@ManyToOne
	@JoinColumn(name="RST_ID")
	public TbZwZkScoreOption getFkByRstId() {
		return fkByRstId;
	}

	public void setFkByRstId(TbZwZkScoreOption fkByRstId) {
		this.fkByRstId = fkByRstId;
	}

	@Transient
	public Integer getRstRid() {
		return rstRid;
	}

	public void setRstRid(Integer rstRid) {
		this.rstRid = rstRid;
	}

	@Transient
	public boolean getIfShowRedStar() {
		return ifShowRedStar;
	}
	public void setIfShowRedStar(boolean ifShowRedStar) {
		this.ifShowRedStar = ifShowRedStar;
	}
}