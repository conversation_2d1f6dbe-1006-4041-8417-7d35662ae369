package com.chis.modules.heth.zkcheck.web;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.enumn.ReturnType;
import com.chis.modules.heth.comm.javabean.FileItemBean;
import com.chis.modules.heth.zkcheck.entity.*;
import com.chis.modules.heth.zkcheck.json.WordReportJson;
import com.chis.modules.heth.zkcheck.logic.WordReportDTO;
import com.chis.modules.heth.zkcheck.service.TbZwZkBadrsnStandService;
import com.chis.modules.heth.zkcheck.service.TbZwZkRstRuleService;
import com.chis.modules.heth.zkcheck.service.TdZwZkCheckMainService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.javabean.FilePojoNew;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.commons.fileupload.FileItem;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;

/**
 * <p>类描述：技术服务机构质量监测基类</p>
 * @ClassAuthor qrr,2021年6月26日,AbstractTdZwZkCheckMainListBean
 * */
public abstract class AbstractTdZwZkQualityCheckListBean extends FacesEditBean implements IProcessData {
	private static final long serialVersionUID = 1L;
	protected CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	protected TdZwZkCheckMainService checkMainService = SpringContextHolder.getBean(TdZwZkCheckMainService.class);
	protected TbZwZkBadrsnStandService standService = SpringContextHolder.getBean(TbZwZkBadrsnStandService.class);
	protected Integer rid;
	protected TdZwZkCheckMain checkMain;

	/*******************hsj-以下为查询条件*************************/
	/**查询条件：地区集合*/
	protected List<TsZone> zoneList;
	/**查询条件：地区名称*/
	protected String searchZoneName;
	/** 质控评估填报 查询条件 - 地区编码（默认管辖地区） */
	protected String searchZoneCode;
	/** 质控评估填报 查询条件 - 被评估机构 */
	protected String searchOrgName;
	/** 质控评估填报 查询条件 - 评估日期开始 */
	protected Date searchCheckStartTime;
	/** 质控评估填报 查询条件 - 评估日期结束*/
	protected Date searchCheckEndTime;
	/** 质控评估填报 查询条件 - 状态*/
	protected List<Integer> searchStateMarkList;
	protected List<SelectItem> checkTypeList;
	/**考核等级  码表5586*/
	protected  List<TsSimpleCode> checkRstList;
	/** 考核等级Map 码表5586 key 扩展字段1 */
	protected  Map<String,TsSimpleCode> checkRstMap;
	/**评估结果名称*/
	protected String selectRst;
	/**评估结果RID*/
	protected String selectRstIds;
/*******************hsj-以上为查询条件*************************/
/*******************hsj-以下为一级编辑、详情*************************/
	/**被考核机构*/
	protected TsUnit unit;
	protected String orgFz;
	protected String orgAddr;
	protected Map<String, TsSimpleCode> itemMap;
	/** 所有的项目对应的编码对应码表 */
	protected Map<String, TsSimpleCode> allItemMap;
	/**是否显示撤销按钮*/
	protected boolean jCCancelBtn;
	//已选择的现场编号rid
	protected Integer editRptNoRid;
/*******************hsj-以上为一级编辑、详情*************************/

	/** 生成报告下载的包含流信息的对象 */
	protected StreamedContent reportFile;
	/** 下载报告包含流信息的对象 */
	protected StreamedContent reportDownloadFile;
	/**报表路径*/
	protected String reportFilePath;
	/** 生成报告时的错误信息 */
	protected String reportGenerateErrMsg;

	/** 编辑页二与详情页二 对应的当前质控考核评估表 */
	protected TdZwZkCheckTable curCheckTable;
	/** 编辑页二 项目RID对应的评分项列表Map*/
	protected Map<Integer,List<TbZwZkScores>> itemScoresMap;
	/** 编辑页二与详情页二评估考核项目集合 */
	protected List<TdZwZkCheckItem> zwZkCheckItemList;
	/** 编辑页二与详情页二 考核指标序号及考核指标描述 key 评估项rid value 指标序号及考核指标描述*/
	protected Map<Integer,String> indexDescMap;
	/** 编辑页二 扣分原因赋值的对象 */
	protected TdZwZkCheckSub curCheckSub;
	/** 扣分原因弹出框显示的所有的扣分原因 */
	protected List<TbZwZkScoreDeduct> allScoreDeductList;
	/** 扣分原因弹出框显示的扣分原因列表 */
	protected List<TbZwZkScoreDeduct> showScoreDeductList;
	/** 扣分原因弹出框 检索扣分原因 */
	protected String deductQuery;
	/** 扣分原因弹出框确认后 需要更新的 */
	protected String deductUpdateId;


	/** 评分项结果 */
	protected List<TsSimpleCode> scoreRstSimpleList;
	/** 评分项结果否 码表rid */
	protected Integer unhgSoreRstRid;
	/** 评分项结果Map 包含禁用 */
	protected Map<Integer,TsSimpleCode> scoreRstSimpleMap;
	/**重新汇总参数*/
	private TdZwZkCheckSub  checkSub;
	/** 进入、查看是否非现场考核页 0核查 1普通进入查看*/
	protected Integer subEditTag;

	public AbstractTdZwZkQualityCheckListBean() {
		initData();
	}

	/**
	 *
	 * 修订内容:模块初始化
	 *
	 * @MethodReviser pw,2021年07月12日
	 */
	public abstract void initData();

	/**
	 *  <p>方法描述：模块类型，1：技术服务机构质量监测管理；2：技术服务机构质量监测查询</p>
	 * @MethodAuthor hsj 2023-03-29 11:00
	 */
	public abstract Integer pageType();

	/**
	 *  <p>方法描述：查询</p>
	 * @MethodAuthor hsj 2023-03-29 16:51
	 */
	@Override
	public void searchAction() {
		if (null != searchCheckStartTime && null != searchCheckEndTime) {
			if (searchCheckEndTime.before(searchCheckStartTime)) {
				JsfUtil.addErrorMessage("考核开始日期应小于等于结束日期！");
				return;
			}
		}
		super.searchAction();
	}
	/**
	 *  <p>方法描述：添加初始化</p>
	 * @MethodAuthor hsj 2023-03-29 16:51
	 */
	@Override
	public void addInit() {
		this.checkMain = new TdZwZkCheckMain();
		this.checkMain.setCheckType(4);
		this.checkMain.setCheckDate(new Date());
		this.checkMain.setStateMark(0);
		this.checkMain.setFkByRecordOrgId(Global.getUser().getTsUnit());
		this.checkMain.setFkByOrgId(new TsUnit());
		this.checkMain.setCheckProves(new ArrayList<TdZwZkCheckProve>());
		this.checkMain.setDelMark(0);
		this.checkMain.setFkByJcRptId(new TdZwCheckRpt());
		this.editRptNoRid = null;
		initCheckTable();
	}
	/**
	 *  <p>方法描述：</p>
	 * @MethodAuthor hsj 2023-03-29 13:38
	 */
	public void initParamItemMap(){
		this.itemMap = new HashMap<>();
		this.allItemMap = new HashMap<>();
		//5530 质控考核指标（树形）-吉林
		//    extends1，现场核查
		List<TsSimpleCode> itemList = this.commService.findallSimpleCodesByTypeId("5530");
		List<TsSimpleCode> enableList = new ArrayList<>();
		if (!CollectionUtils.isEmpty(itemList)) {
			for (TsSimpleCode t : itemList) {
				if(null != t.getIfReveal() && 1 == t.getIfReveal()){
					this.itemMap.put(t.getCodeNo(), t);
					this.allItemMap.put(t.getCodeNo(), t);
				}else{
					enableList.add(t);
				}
			}
		}
		//避免已暂存过的数据 由于找不到父级项目不处理配置规则 导致异常
		if(!CollectionUtils.isEmpty(enableList)){
			for(TsSimpleCode simpleCode : enableList){
				String codeNo = simpleCode.getCodeNo();
				if(!this.allItemMap.containsKey(codeNo)){
					this.allItemMap.put(codeNo, simpleCode);
				}
			}
		}
	}

	@Override
	public void backAction(){
		setActiveTab(0);
		this.searchAction();

	}

	/**
	 *  <p>方法描述：初始化考核表：</p>
	 * @MethodAuthor hsj 2023-03-29 16:50
	 */
	private void initCheckTable() {
		//健康检查质控考核评估表-维护表
		List<TdZwZkCheckTable> checkTables = new ArrayList<>();
		this.checkMain.setCheckTables(checkTables);
		//查询 质控指标分值维护为4的
		List<TbZwZkScores> list = standService.findTbZwZkScoresListByType(4);
		//总分
		BigDecimal totalScore = new BigDecimal(0);
		if (!CollectionUtils.isEmpty(list)) {
			Map<Integer, TdZwZkCheckTable> standMap = new HashMap<Integer, TdZwZkCheckTable>();
			Map<String, TdZwZkCheckItem> itemMap = new HashMap<String, TdZwZkCheckItem>();
			Map<String, BigDecimal> checkValMap = new HashMap<>();
			for (TbZwZkScores t : list) {
				//质控指标评分项维护-不存在则跳过
				List<TbZwZkScoreIndex> indexList = t.getIndexList();
				if (CollectionUtils.isEmpty(indexList)) {
					continue;
				}
				//指标不存在
				TbZwZkScoreIndex scoreIndex = indexList.get(0);//考核指标
				if (null==scoreIndex.getFkByIndexId()) {
					continue;
				}
				String codeLevelNo = scoreIndex.getFkByIndexId().getCodeLevelNo();//指标层级编码
				String[] split = codeLevelNo.split("\\.");
				if (null==split||0==split.length) {
					continue;
				}
				//项目已停用时不加载
				//父级-考核项目
				TsSimpleCode item = this.itemMap.get(split[0]);
				if(ObjectUtil.isNull(item)){
					continue;
				}
				//有得分时统计得分
				if(ObjectUtil.isNotNull(t.getScore())){
					totalScore = totalScore.add(t.getScore());
				}
				this.jointTdZwZkCheckItem(standMap,itemMap,t,checkTables,item,checkValMap);
			}
			//分值处理
			this.dealCheckVal(checkTables,checkValMap);
			checkMain.setTotalCheckVal(clearNoUseZeroForBigDecimal(totalScore));
		}
	}
	/**
	 *  <p>方法描述： 去掉BigDecimal尾部多余的0，通过stripTrailingZeros().toPlainString()实现</p>
	 * @MethodAuthor hsj 2023-04-06 14:06
	 */
	public static BigDecimal clearNoUseZeroForBigDecimal(BigDecimal num) {
		if(ObjectUtil.isNull(num)){
			return num;
		}
		BigDecimal returnNum = null;
		String numStr = num.stripTrailingZeros().toPlainString();
		if (numStr.indexOf(".") == -1) {
			// 如果num 不含有小数点,使用stripTrailingZeros()处理时,变成了科学计数法
			returnNum = new BigDecimal(numStr);
		} else {
			if (num.compareTo(BigDecimal.ZERO) == 0) {
				returnNum = BigDecimal.ZERO;
			} else {
				returnNum = num.stripTrailingZeros();
			}
		}
		return returnNum;
	}
	/**
	 *  <p>方法描述：分值处理</p>
	 * @MethodAuthor hsj 2023-03-31 9:18
	 */
	private void dealCheckVal(List<TdZwZkCheckTable> checkTables, Map<String, BigDecimal> checkValMap) {
		if(CollectionUtils.isEmpty(checkTables) || null == checkValMap){
			return;
		}
		for(TdZwZkCheckTable checkTable:checkTables){
			if(!CollectionUtils.isEmpty(checkTable.getCheckItems())){
				for(TdZwZkCheckItem item:checkTable.getCheckItems()){
					String key = checkTable.getFkByCheckTableId().getRid()+"&"+item.getFkByItemId().getRid();
					if(checkValMap.containsKey(key)){
						item.setCheckVal(clearNoUseZeroForBigDecimal(checkValMap.get(key)));
					}
				}
			}
			if(!CollectionUtils.isEmpty(checkTable.getZkcheckItems())){
				for(TdZwZkCheckItem item:checkTable.getZkcheckItems()){
					String key = checkTable.getFkByCheckTableId().getRid()+"&"+item.getFkByItemId().getRid();
					if(checkValMap.containsKey(key)){
						item.setCheckVal(clearNoUseZeroForBigDecimal(checkValMap.get(key)));
					}
				}
			}

		}
	}
	/**
	 *  <p>方法描述：项目初始化处理</p>
	 * @MethodAuthor hsj 2023-03-31 10:30
	 */
	private void jointTdZwZkCheckItem(Map<Integer, TdZwZkCheckTable> standMap, Map<String, TdZwZkCheckItem> itemMap, TbZwZkScores t, List<TdZwZkCheckTable> checkTables, TsSimpleCode item,Map<String, BigDecimal> checkValMap) {
		TbZwZkBadrsnStand badrsnStand = t.getFkByMainId();
		if (null==standMap.get(badrsnStand.getRid())) {
			//健康检查质控考核评估表
			TdZwZkCheckTable checkTable = this.createTdZwZkCheckTable(badrsnStand);
			checkTables.add(checkTable);
			//为空
			TdZwZkCheckItem checkItem = new TdZwZkCheckItem();
			checkItem.setFkByMainId(checkTable);
			checkItem.setFkByItemId(item);
			checkItem.setCreateDate(new Date());
			checkItem.setCreateManid(Global.getUser().getRid());
			if("1".equals(item.getExtendS1())){
				checkTable.getZkcheckItems().add(checkItem);
			}else {
				checkTable.getCheckItems().add(checkItem);
			}
			standMap.put(badrsnStand.getRid(), checkTable);
			String key = badrsnStand.getRid()+"&"+item.getRid();
			itemMap.put(key, checkItem);
			checkValMap.put(key,t.getScore());
		}else {
			TdZwZkCheckTable checkTable = standMap.get(badrsnStand.getRid());
			String key = badrsnStand.getRid()+"&"+item.getRid();
			if (null==itemMap.get(key)) {
				TdZwZkCheckItem checkItem = new TdZwZkCheckItem();
				checkItem.setFkByItemId(item);
				checkItem.setFkByMainId(checkTable);
				checkItem.setCreateDate(new Date());
				checkItem.setCreateManid(Global.getUser().getRid());
				if("1".equals(item.getExtendS1())){
					checkTable.getZkcheckItems().add(checkItem);
				}else {
					checkTable.getCheckItems().add(checkItem);
				}
				itemMap.put(key, checkItem);
				checkValMap.put(key,t.getScore());
			}else{
				BigDecimal check = null;
				if(checkValMap.get(key) == null){
					check = t.getScore();
				}else if(t.getScore() == null){
					check = checkValMap.get(key) ;
				}else {
					check =  checkValMap.get(key).add(t.getScore());
				}
				checkValMap.put(key,check);
			}
		}
	}
	/**
	 *  <p>方法描述：</p>
	 * @MethodAuthor hsj 2023-03-31 10:30
	 */
	private TdZwZkCheckTable createTdZwZkCheckTable(TbZwZkBadrsnStand badrsnStand) {
		TdZwZkCheckTable checkTable = new TdZwZkCheckTable();
		checkTable.setFkByCheckTableId(badrsnStand);
		checkTable.setStateMark(0);
		checkTable.setCreateDate(new Date());
		checkTable.setCreateManid(Global.getUser().getRid());
		checkTable.setFkByMainId(this.checkMain);
		checkTable.setCheckItems(new ArrayList<TdZwZkCheckItem>());
		checkTable.setZkcheckItems(new ArrayList<TdZwZkCheckItem>());
		return checkTable;
	}

	/**
	 *  <p>方法描述：切换被考核机构且与原来不一致时</p>
	 * @MethodAuthor hsj 2023-03-29 16:20
	 */
	public void changeCheckType() {
		//清空
		this.checkMain.setDelMark(0);
		this.checkMain.setFkByCheckRstId(null);
		this.checkMain.setIfHg(null);
		//清空 检测报告名称（编号）【JC_RPT_ID】
		this.checkMain.setFkByJcRptId(new TdZwCheckRpt());
		//清空分数
		this.checkMain.setTotalCheckVal(null);
		this.checkMain.setTotalScoreVal(null);
		//被评估机构变换
		this.checkMain.setFkByOrgId(unit);
		this.checkMain.setOrgFz(orgFz);
		this.checkMain.setOrgAddr(orgAddr);
		initCheckTable();
	}
	/**
	 *  <p>方法描述：选择被评估机构</p>
	 * @MethodAuthor hsj 2023-03-29 15:59
	 */
	public void selectOrgList() {
        Map<String, Object> options = MapUtils.produceDialogMap(null,760,null,390);
        Map<String, List<String>> paramMap = new HashMap<>();
		//考核类型默认为4
        List<String> tmpList = new ArrayList<>();
		tmpList.add("4");
        paramMap.put("checkType", tmpList);
		tmpList = new ArrayList<>();
		tmpList.add(this.zoneList.get(0).getZoneGb());
		paramMap.put("zoneGb", tmpList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/zkcheck/selectCheckOrgList", options, paramMap);
    }

    /**
     *  <p>方法描述：选择被评估机构</p>
     * @MethodAuthor hsj 2023-03-29 15:59
     */
    public void onOrgSelect(SelectEvent event) {
		this.unit = new TsUnit();
		this.orgFz = null;
		this.orgAddr = null;
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
        	Object[] obj = (Object[]) selectedMap.get("selected");
        	unit.setRid(new Integer(obj[0].toString()));
        	unit.setUnitname(StringUtils.objectToString(obj[2]));
			this.orgFz = StringUtils.objectToString(obj[3]);
			this.orgAddr = StringUtils.objectToString(obj[4]);
			if(this.checkMain.getFkByOrgId() != null &&  this.checkMain.getFkByOrgId().getRid() != null && !this.checkMain.getFkByOrgId().getRid().equals(new Integer(obj[0].toString()))){
				RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show()");
				return;
			}
        	this.checkMain.setFkByOrgId(unit);
			this.checkMain.setOrgFz(orgFz);
			this.checkMain.setOrgAddr(orgAddr);
        }
    }
	/**
	 *  <p>方法描述：选择检测报告名称（编号）</p>
	 * @MethodAuthor hsj 2023-03-29 15:59
	 */
	public void selectRptNoList() {
		if(this.checkMain.getFkByOrgId() == null || this.checkMain.getFkByOrgId().getRid() == null ){
			JsfUtil.addErrorMessage("请先选择被考核机构！");
			return;
		}
		Map<String, Object> options = MapUtils.produceDialogMap(null,820,null,390);
		Map<String, List<String>> paramMap = new HashMap<>();
		//当前页面编号
		List<String> tmpList = new ArrayList<>();
		tmpList.add(this.checkMain.getFkByOrgId().getRid().toString());
		paramMap.put("orgId", tmpList);
		tmpList = new ArrayList<>();
		tmpList.add(this.editRptNoRid  == null ? null : this.editRptNoRid.toString());
		paramMap.put("editRptNoRid", tmpList);
		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("/webapp/heth/zkcheck/selectRptNoList", options, paramMap);
	}

	/**
	 *  <p>方法描述：选择检测报告名称（编号）</p>
	 * @MethodAuthor hsj 2023-03-29 15:59
	 */
	public void onRptNoSelect(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
		if(null != selectedMap && selectedMap.size() >0) {
			Object[] obj = (Object[]) selectedMap.get("selected");
			TdZwCheckRpt checkRpt = this.checkMainService.find(TdZwCheckRpt.class,Integer.parseInt(obj[0].toString()));
			this.checkMain.setFkByJcRptId(checkRpt);
			//刷新当前页面现场考核信息
			jointSceneDeductRsn();
		}
	}
	/**
	 *  <p>方法描述：刷新现场数据</p>
	 * @MethodAuthor hsj 2023-03-31 10:42
	 */
	private void jointSceneDeductRsn() {
		Map<Integer, String[]> deductMap = new HashMap<>();
		//健康检查质控考核评估表
		List<TdZwZkCheckTable> checkTables = this.checkMain.getCheckTables();
		if (!CollectionUtils.isEmpty(checkTables)) {
			for (TdZwZkCheckTable t : checkTables) {
				List<TdZwZkCheckItem> zwZkCheckItems = t.getZkcheckItems();
				if (!CollectionUtils.isEmpty(zwZkCheckItems)) {
					for (TdZwZkCheckItem item : zwZkCheckItems) {
						t.setZkStateMark(0);
						item.setScoreVal(null);
						item.setDeductRsn(null);//扣分原因
						item.setDeductRsn2(null);//扣分原因
						List<TdZwCheckItem>	checkItemLists =this.checkMainService.findTdZwCheckItems(this.checkMain.getFkByJcRptId().getRid(),item.getFkByMainId().getFkByCheckTableId().getRid(),item.getFkByItemId().getRid());
						if(CollectionUtils.isEmpty(checkItemLists)){
							continue;
						}
						t.setZkStateMark(checkItemLists.get(0).getFkByMainId().getFkByMainId().getState());
						item.setScoreVal(checkItemLists.get(0).getScoreVal());
						//扣分原因
						List<TdZwCheckDeduct> checkDeducts = checkMainService.findTbZwZkScoreDeductList(checkItemLists.get(0).getRid());
						if (!CollectionUtils.isEmpty(checkDeducts)) {
							for (TdZwCheckDeduct deduct2 : checkDeducts) {
								//健康检查质控考核结果子表 为空跳过
								if (null==deduct2.getFkByMainId()||null==deduct2.getFkByMainId().getFkByMainId()) {
									continue;
								}
								//质控指标扣分原因维护 为空跳过
								if (null==deduct2.getFkByDeductId()||null==deduct2.getFkByDeductId().getFkByDeductId()) {
									continue;
								}
								Integer key = deduct2.getFkByMainId().getFkByMainId().getFkByItemId().getRid();
								if (null==deductMap.get(key)) {
									String codeName = deduct2.getFkByDeductId().getFkByDeductId().getCodeName();
									deductMap.put(deduct2.getFkByMainId().getFkByMainId().getFkByItemId().getRid(), new String[]{codeName,codeName});
								}else {
									String[] deduct = deductMap.get(deduct2.getFkByMainId().getFkByMainId().getFkByItemId().getRid());
									String val = deduct[0]+"；"+deduct2.getFkByDeductId().getFkByDeductId().getCodeName();
									String val2 = deduct[1]+"；<br/>"+deduct2.getFkByDeductId().getFkByDeductId().getCodeName();
									deductMap.put(key, new String[]{val,val2});
								}
							}
						}
					String[] deduct = deductMap.get(item.getFkByItemId().getRid());
					if (null!=deduct && deduct.length>0) {
						item.setDeductRsn(deduct[0]);//扣分原因
						item.setDeductRsn2(deduct[1]);//扣分原因
					}
					}
				}
			}
		}
	}


	@Override
	public void viewInit() {
	}



/**
 *  <p>方法描述：一级编辑页初始化</p>
 * @MethodAuthor hsj 2023-03-30 9:47
 */
	@Override
	public void modInit() {
		editRptNoRid = null;
		this.checkMain = checkMainService.findTdZwZkCheckMain(this.rid);
		//检测报告可能为空处理
		if(ObjectUtil.isNull(this.checkMain.getFkByJcRptId()) || ObjectUtil.isNull(this.checkMain.getFkByJcRptId().getRid())){
			this.checkMain.setFkByJcRptId(new TdZwCheckRpt());
		}else {
			editRptNoRid = this.checkMain.getFkByJcRptId().getRid();
		}
		jointDeductRsn();
	}
	/**
	 *  <p>方法描述：健康检查质控考核评估表拼接扣分原因</p>
	 * @MethodAuthor hsj 2023-03-30 16:00
	 */
	private void jointDeductRsn() {
		//key：考核项目rid
		Map<Integer, String[]> deductMap = new HashMap<>();
		List<TdZwZkCheckDeduct> deductList = checkMainService.findTdZwZkCheckDeductList(this.rid);
		if (!CollectionUtils.isEmpty(deductList)) {
			for (TdZwZkCheckDeduct t : deductList) {
				//健康检查质控考核结果子表 为空跳过
				if (null==t.getFkByMainId()||null==t.getFkByMainId().getFkByMainId()) {
					continue;
				}
				//质控指标扣分原因维护 为空跳过
				if (null==t.getFkByDeductId()||null==t.getFkByDeductId().getFkByDeductId()) {
					continue;
				}
				Integer key = t.getFkByMainId().getFkByMainId().getFkByItemId().getRid();
				if (null==deductMap.get(key)) {
					String codeName = t.getFkByDeductId().getFkByDeductId().getCodeName();
					deductMap.put(t.getFkByMainId().getFkByMainId().getFkByItemId().getRid(), new String[]{codeName,codeName});
				}else {
					String[] deduct = deductMap.get(t.getFkByMainId().getFkByMainId().getFkByItemId().getRid());
					String val = deduct[0]+"；"+t.getFkByDeductId().getFkByDeductId().getCodeName();
					String val2 = deduct[1]+"；<br/>"+t.getFkByDeductId().getFkByDeductId().getCodeName();
					deductMap.put(key, new String[]{val,val2});
				}
			}
		}
		//健康检查质控考核评估表
		List<TdZwZkCheckTable> checkTables = this.checkMain.getCheckTables();
		sortCheckTableList(checkTables);
		if (!CollectionUtils.isEmpty(checkTables)) {
			for (TdZwZkCheckTable t : checkTables) {
				List<TdZwZkCheckItem> checkItems = t.getCheckItems();
				//现场的
				List<TdZwZkCheckItem> zwZkCheckItems = new LinkedList<>();
				if (!CollectionUtils.isEmpty(checkItems)) {
					 List<TdZwZkCheckItem> checkItem =new ArrayList<>();
					for (TdZwZkCheckItem item : checkItems) {
						if("1".equals(item.getFkByItemId().getExtendS1())){
							t.setZkStateMark(0);
							item.setScoreVal(null);
							List<TdZwCheckItem>	checkItemLists =this.checkMainService.findTdZwCheckItems(this.checkMain.getFkByJcRptId().getRid(),item.getFkByMainId().getFkByCheckTableId().getRid(),item.getFkByItemId().getRid());
							if(CollectionUtils.isEmpty(checkItemLists)){
								//存在数据时存储一条
								zwZkCheckItems.add(item);
								checkItem.add(item);
								continue;
							}
							t.setZkStateMark(checkItemLists.get(0).getFkByMainId().getFkByMainId().getState());
							item.setScoreVal(checkItemLists.get(0).getScoreVal());
							//扣分原因
							List<TdZwCheckDeduct> checkDeducts = checkMainService.findTbZwZkScoreDeductList(checkItemLists.get(0).getRid());
							if (!CollectionUtils.isEmpty(checkDeducts)) {
								for (TdZwCheckDeduct deduct2 : checkDeducts) {
									//健康检查质控考核结果子表 为空跳过
									if (null==deduct2.getFkByMainId()||null==deduct2.getFkByMainId().getFkByMainId()) {
										continue;
									}
									//质控指标扣分原因维护 为空跳过
									if (null==deduct2.getFkByDeductId()||null==deduct2.getFkByDeductId().getFkByDeductId()) {
										continue;
									}
									Integer key = deduct2.getFkByMainId().getFkByMainId().getFkByItemId().getRid();
									if (null==deductMap.get(key)) {
										String codeName = deduct2.getFkByDeductId().getFkByDeductId().getCodeName();
										deductMap.put(deduct2.getFkByMainId().getFkByMainId().getFkByItemId().getRid(), new String[]{codeName,codeName});
									}else {
										String[] deduct = deductMap.get(deduct2.getFkByMainId().getFkByMainId().getFkByItemId().getRid());
										String val = deduct[0]+"；"+deduct2.getFkByDeductId().getFkByDeductId().getCodeName();
										String val2 = deduct[1]+"；<br/>"+deduct2.getFkByDeductId().getFkByDeductId().getCodeName();
										deductMap.put(key, new String[]{val,val2});
									}
								}
							}
						}
						String[] deduct = deductMap.get(item.getFkByItemId().getRid());
						if (null!=deduct && deduct.length>0) {
							item.setDeductRsn(deduct[0]);//扣分原因
							item.setDeductRsn2(deduct[1]);//扣分原因
						}
						if("1".equals(item.getFkByItemId().getExtendS1())){
							zwZkCheckItems.add(item);
							checkItem.add(item);
						}
					}
					if(!CollectionUtils.isEmpty(checkItem)){
						checkItems.removeAll(checkItem);
					}
				}
				t.setZkcheckItems(zwZkCheckItems);
			}
		}
	}

	/**
 	 * <p>方法描述：进入编辑页二</p>
 	 * @MethodAuthor qrr,2021年6月26日,modCheckIndex
	 * */
	public void modCheckIndex() {
		boolean flag = false;
		if(null == this.curCheckTable || null == this.curCheckTable.getRid()){
			JsfUtil.addErrorMessage("请先暂存现场考核表！");
			flag = true;
		}
		if (null==this.checkMain.getFkByOrgId()||null==this.checkMain.getFkByOrgId().getRid()) {
			JsfUtil.addErrorMessage("被考核机构不能为空！");
			flag = true;
		}
		if (null==this.checkMain.getCheckDate()) {
			JsfUtil.addErrorMessage("考核日期不能为空！");
			flag = true;
		}
		if (flag || null == curCheckTable) {
			return;
		}
		forwardSubPage();
	}

	/**
	 *  <p>方法描述：一级页面暂存</p>
	 * @MethodAuthor hsj 2023-03-30 10:02
	 */
	@Override
	public void saveAction() {
		boolean flag = false;
		try{
			if (null==this.checkMain.getFkByOrgId()||null==this.checkMain.getFkByOrgId().getRid()) {
				JsfUtil.addErrorMessage("被考核机构不能为空！");
				flag = true;
			}
			if (null==this.checkMain.getCheckDate()) {
				JsfUtil.addErrorMessage("考核日期不能为空！");
				flag = true;
			}
			if (flag) {
				return;
			}
			dealCheckTable();
			/*调用保存方法*/
			tosave();
			this.rid = checkMain.getRid();
			modInit();
			JsfUtil.addSuccessMessage("暂存成功！");
		}catch (Exception e){
			JsfUtil.addErrorMessage("暂存失败！");
			e.printStackTrace();
		}
	}

	/**
	 *  <p>方法描述：处理大保存和小保存中【健康检查质控考核评估表】的状态和审核人丢失的问题</p>
	 * @MethodAuthor hsj 2023-03-30 9:45
	 */
	public void dealCheckTable(){
		List<TdZwZkCheckTable> tmpCheckList = checkMain.getCheckTables();
		if(CollectionUtils.isEmpty(tmpCheckList)){
			return;
		}
		Map<Integer,TdZwZkCheckTable>  tableMap=getCheckTableMap();
		for(TdZwZkCheckTable t : tmpCheckList){
				if(tableMap!=null&&tableMap.get(t.getRid())!=null){
					t.setCheckPsn(tableMap.get(t.getRid()).getCheckPsn());
					t.setStateMark(tableMap.get(t.getRid()).getStateMark());
				}
				//现场数据处理
				if(!CollectionUtils.isEmpty(t.getZkcheckItems())){
					t.getCheckItems().addAll(t.getZkcheckItems());
				}
		}
	}
	/**
	 *  <p>方法描述：以及页面提交验证</p>
	 * @MethodAuthor hsj 2023-04-07 11:38
	 */
	public void  beforeSubmitAction(){
		boolean flag = false;
		if (null==this.checkMain.getFkByOrgId()||null==this.checkMain.getFkByOrgId().getRid()) {
			JsfUtil.addErrorMessage("被考核机构不能为空！");
			flag = true;
		}
		if (null==this.checkMain.getCheckDate()) {
			JsfUtil.addErrorMessage("考核日期不能为空！");
			flag = true;
		}
		//检测报告名称（编号）
		if (null==this.checkMain.getFkByJcRptId() || null == this.checkMain.getFkByJcRptId().getRid()) {
			JsfUtil.addErrorMessage("检测报告名称（编号）不能为空！");
			flag = true;
		}
		//评估意见反馈表文书附件地址
		if (StringUtils.isBlank(this.checkMain.getWritePath())) {
			JsfUtil.addErrorMessage("请上传考核结果表！");
			flag = true;
		}

		for(TdZwZkCheckTable checkTable:this.checkMain.getCheckTables()){
			if(checkTable.getStateMark() ==0 && !CollectionUtils.isEmpty(checkTable.getCheckItems())){
				JsfUtil.addErrorMessage(checkTable.getFkByCheckTableId().getCheckName()+"未提交！");
				flag = true;
			}
		}
		if (!flag) {
			RequestContext.getCurrentInstance().execute("PF('Confirm1Dialog').show()");
		}
	}
	/**
	 *  <p>方法描述：一级页面提交</p>
	 * @MethodAuthor hsj 2023-03-30 9:43
	 */
	public void submitAction(){
		try{
			//状态修改
			checkMain.setStateMark(1);
			dealCheckTable();
			/*调用保存方法*/
			tosave();
			this.rid=checkMain.getRid();
			this.modInit();
			JsfUtil.addSuccessMessage("提交成功！");
			RequestContext.getCurrentInstance().update("tabView");
		}catch (Exception e){
			JsfUtil.addErrorMessage("提交失败！");
			e.printStackTrace();
		}

	}

	/**
	 *  <p>方法描述：暂存，提交 抽出的保存</p>
	 * @MethodAuthor hsj 2023-03-30 9:47
	 */
	public void tosave(){
		//检测报告名称（编号）处理
		if(ObjectUtil.isNull(checkMain.getFkByJcRptId()) || ObjectUtil.isNull(checkMain.getFkByJcRptId().getRid())){
			checkMain.setFkByJcRptId(null);
		}
		checkMainService.upsertEntity(checkMain);
		if(null != checkMain.getRid()){
			this.checkMain = checkMainService.findTdZwZkCheckMain(checkMain.getRid());
		}
	}



	/**
	 * <p>
	 *     方法描述：附件上传
	 * </p>
	 *
	 * @MethodAuthor yph,2021年06月28日
	 */
	public synchronized void  fileUpload(FileUploadEvent event) {
		try {

			if(this.checkMain==null||this.checkMain.getCheckProves()==null){
				return;
			}
			UploadedFile file = event.getFile();
			String fileName = file.getFileName();// 文件名称
			String contentType = file.getContentType().toLowerCase();
			boolean flag = FileUtils.allowPdfOrImageMethod(contentType, fileName, "3");

			if(!flag){
				JsfUtil.addErrorMessage("不支持的文件格式，请上传图片、PDF！");
				return;
			}

			String uuid = UUID.randomUUID().toString().replaceAll("-", "");
			String path = JsfUtil.getAbsolutePath();
			String relativePath = new StringBuffer("jlzkkh/").append(uuid)
					.append(fileName.substring(fileName.lastIndexOf("."))).toString();
			// 文件路径
			String filePath = new StringBuilder(path).append(relativePath).toString();
			FileUtils.copyFile(filePath, file.getInputstream());

			TdZwZkCheckProve prove = new TdZwZkCheckProve();
			prove.setAnnexName(fileName);
			prove.setAnnexAddr(relativePath);
			prove.setCreateDate(new Date());
			prove.setCreateManid(Global.getUser().getRid());
			prove.setModifyDate(new Date());
			prove.setModifyManid(Global.getUser().getRid());
			prove.setFkByMainId(this.checkMain);
			this.checkMain.getCheckProves().add(prove);

			RequestContext context = RequestContext.getCurrentInstance();
			context.update("tabView:editForm:checkContentPanel");
			JsfUtil.addSuccessMessage("上传成功！");
			context.execute("PF('FileDialog').hide();");
		} catch (IOException e) {
			e.printStackTrace();
		}

	}




	/**
	 * @Description: 生成下载前验证
	 *
	 * @MethodAuthor yph,2021年06月30日
	 */
	public void preGenerateReport(){
		if(!generateValidata()){
			RequestContext.getCurrentInstance().execute("preGenerateMod();");
		}
	}

	/**
	 * <p>方法描述：生成前 执行重新计算 完成后生成文件 </p>
	 * @MethodAuthor： pw 2023/4/8
	 **/
	public void exeGenerateReport(){
		try{
			this.dealCheckTable();
			//重新计算主表总得分 赋值考核结果 以及现场考核项目实得分
			this.updateTotalScoreVal(this.checkMain);
			this.tosave();
			this.modInit();
			RequestContext.getCurrentInstance().update("tabView:editForm");
			RequestContext.getCurrentInstance().execute("generateClick()");
		}catch(Exception e){
			RequestContext.getCurrentInstance().execute("hideShade();");
			e.printStackTrace();
		}
	}

	/**
	 * <p>方法描述： 生成与上传校验 </p>
	 * @MethodAuthor： pw 2023/4/6
	 **/
	private boolean generateValidata(){
		boolean flag = false;
		if (null==this.checkMain.getFkByOrgId()||null==this.checkMain.getFkByOrgId().getRid()) {
			JsfUtil.addErrorMessage("被考核机构不能为空！");
			flag = true;
		}
		if (null==this.checkMain.getCheckDate()) {
			JsfUtil.addErrorMessage("考核日期不能为空！");
			flag = true;
		}

		List<TdZwZkCheckTable> checkTableList = this.checkMain.getCheckTables();
		if(!CollectionUtils.isEmpty(checkTableList)){
			for (TdZwZkCheckTable checkTable : checkTableList) {
				List<TdZwZkCheckItem> checkItemList = checkTable.getCheckItems();
				if(CollectionUtils.isEmpty(checkItemList)){
					continue;
				}
				if(checkTable.getStateMark() ==0) {
					JsfUtil.addErrorMessage(checkTable.getFkByCheckTableId().getCheckName() + "未提交！");
					flag = true;
				}
			}
		}
		//检测报告名称（编号）
		if (null==this.checkMain.getFkByJcRptId() || null == this.checkMain.getFkByJcRptId().getRid()) {
			JsfUtil.addErrorMessage("检测报告名称（编号）不能为空！");
			flag = true;
		}
		if(null != this.checkMain.getFkByJcRptId() &&
				(null == this.checkMain.getFkByJcRptId().getState() || 2 != this.checkMain.getFkByJcRptId().getState())){
			JsfUtil.addErrorMessage("检测报告未核查！");
			flag = true;
		}
		return flag;
	}

	/**
	 * @Description: 生成报告下载完删除文件
	 *
	 * @MethodAuthor yph,2021年06月30日
	 */
	public void deleteVirReportFile(){
		String path = getPathStr();
		if(StringUtils.isNotBlank(path)){
			File deleteFile = new File(path);
			if(deleteFile.exists() && deleteFile.isFile()){
				deleteFile.delete();
			}
		}
	}


	private boolean validateGenerateReport() {
		boolean flag = false;
		/**判断检测表状态是否全部已提交*/
		if(validateCheckTable()){
			flag = true;
		}
		return flag;
	}

	/**
	 * <p>
	 *     方法描述：判断检测表状态是否全部已提交
	 * </p>
	 *
	 * @MethodAuthor yph,2021年07月3日
	 */
	private boolean validateCheckTable() {
		boolean flag = false;
		List<TdZwZkCheckTable> checkTables = this.checkMain.getCheckTables();
		if(this.checkMain.getFkByOrgId()==null){
			JsfUtil.addErrorMessage("被考核机构不允许为空！");
			flag = true;
		}

		if(this.checkMain.getCheckDate()==null){
			JsfUtil.addErrorMessage("考核日期不允许为空！");
			flag = true;
		}
		if(checkTables==null ||checkTables.size()==0){
			JsfUtil.addErrorMessage("没有考核项目，请检查！");
			flag = true;
		}else {
			for(TdZwZkCheckTable checkTable:checkTables){
				if(checkTable.getStateMark().intValue()==0){
					JsfUtil.addErrorMessage(checkTable.getFkByCheckTableId().getCheckName()+"未提交！");
					flag = true;
				}
			}
		}
		return flag;
	}

	/**
	 * @Description: 上传报告
	 *
	 * @MethodAuthor pw,2021年06月9日
	 */
	public synchronized void uploadReport(FileUploadEvent event){
		if (null != event) {
			UploadedFile file = event.getFile();
			String fileName = file.getFileName();
			// 得到唯一uid
			String uuid = UUID.randomUUID().toString().replaceAll("-", "");
			// 后缀名
			String hz = fileName.substring(fileName.lastIndexOf(".") + 1);
			File dir = new File(JsfUtil.getAbsolutePath() + "rptWordTemp/");
			if(!dir.exists()){
				dir.mkdirs();
			}
			// 文件路径
			String filePath = JsfUtil.getAbsolutePath() + "rptWordTemp/" + uuid + "." + hz;
			String simpleFilePath = "/rptWordTemp/" + uuid + "." + hz;

			try {
				if("pdf".equals(hz)){
					String errorMsg = FileUtils.veryFile(file.getInputstream(), file.getContentType(), fileName, "2");
					if (StringUtils.isNotBlank(errorMsg)) {
						JsfUtil.addErrorMessage(errorMsg);
						RequestContext.getCurrentInstance().update("tabView:editForm:fileUpload");
						return;
					}
				}
				FileUtils.copyFile(filePath, file.getInputstream());
				this.checkMain.setWritePath(simpleFilePath);
				JsfUtil.addSuccessMessage("上传成功！");
				RequestContext.getCurrentInstance().execute("ReportFileUpload.hide();");
				RequestContext.getCurrentInstance().update("tabView:editForm");
			} catch (Exception e) {
				RequestContext.getCurrentInstance().update("tabView:editForm:reportFileUpload");
				RequestContext.getCurrentInstance().execute("ReportFileUpload.show();");
				File tmpFile = new File(filePath);
				if(null != tmpFile && tmpFile.exists()){
					tmpFile.delete();
				}
				this.checkMain.setWritePath(null);
				FacesMessage msg = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", msg);
				e.printStackTrace();
			}
		}
	}

	/**
	 * @Description: 上传报告前校验
	 *
	 * @MethodAuthor pw,2021年06月15日
	 */
	public void preUploadReport(){
		this.reportGenerateErrMsg = null;
		if(!generateValidata()){
			this.dealCheckTable();
			//重新计算主表总得分 赋值考核结果 以及现场考核项目实得分
			this.updateTotalScoreVal(this.checkMain);
			this.tosave();
			this.modInit();
			RequestContext.getCurrentInstance().update("tabView:editForm");
			RequestContext.getCurrentInstance().execute("ReportFileUpload.show();");
		}
	}

	/**
	 * @Description: 删除报告
	 *
	 * @MethodAuthor pw,2021年06月9日
	 */
	public void deleteReport(){
		try{
			this.reportGenerateErrMsg = null;
			String filePath = this.checkMain.getWritePath();
			if(StringUtils.isBlank(filePath)){
				return;
			}
			this.checkMain.setWritePath(null);
			JsfUtil.addSuccessMessage("删除成功！");
		}catch(Exception e){
			JsfUtil.addErrorMessage("删除失败！");
			e.printStackTrace();
		}
	}


	private String getPathStr(){
		if(StringUtils.isBlank(reportFilePath)){
			return null;
		}
		String path = "";
		String xnPath = JsfUtil.getAbsolutePath();
		if(reportFilePath.indexOf(xnPath) != -1){
			path = reportFilePath;
		}else{
			path = xnPath + reportFilePath;
		}
		return path;
	}

	public synchronized StreamedContent getReportFile() {
		reportFile = null;
		InputStream stream = null;
		reportGenerateErrMsg = null;
		try{
			if(null != this.checkMain){
				this.checkMain.setWritePath(null);
			}
			generateReport();
			if(StringUtils.isBlank(reportFilePath)){
				return reportFile;
			}
			String path=getPathStr();
			String fileName = DateUtils.getYear() + this.checkMain.getFkByOrgId().getUnitname()+"职业卫生技术服务机构质量监测结果.docx";
			stream = new FileInputStream(path);// stream不能关 关闭下载会报错
			this.reportFile = new DefaultStreamedContent(stream, "application/msword", URLEncoder.encode(fileName, "UTF-8"));
		}catch(Exception e){
			reportGenerateErrMsg = "生成报告失败！";
			e.printStackTrace();
		}
		return reportFile;
	}

	/**
	 * @Description: 生成报告
	 *
	 * @MethodAuthor pw,2021年06月9日
	 */
	public void generateReport(){
		reportFilePath = null;
		String returnJson = null;
		try{
			WordReportJson reportJson = new WordReportJson();
			reportJson.setRid(this.checkMain.getRid());
			reportJson.setRptCode("HETH_ZK_1003");
			String requestJson = JSON.toJSONString(reportJson);
			String encodeJson = null;
			String debug = PropertyUtils.getValue("encrypt.debug");
			String encryptKey = PropertyUtils.getValue("encrypt.key");
			if ("true".equals(debug)) {
				encodeJson = requestJson;
			}else {
				//加密
				encodeJson = AesEncryptUtils.aesEncrypt(requestJson, encryptKey);
			}
			//调用接口
			String delUrl = PropertyUtils.getValue("delUrl");
			String reposeJson = HttpRequestUtil.httpRequestByRaw(delUrl+"/word/generate",encodeJson);

			//解密
			if ("true".equals(debug)) {
				returnJson = reposeJson;
			}else {
				returnJson = AesEncryptUtils.aesDecrypt(reposeJson, encryptKey);
			}
		}catch (Exception e){
			reportGenerateErrMsg = "生成报告失败！";
			e.printStackTrace();
		}
		if(StringUtils.isNotBlank(returnJson)){
			WordReportDTO returnDTO = JSON.parseObject(returnJson, WordReportDTO.class);
			if(null == returnDTO || !ReturnType.SUCCESS_PROCESS.getTypeNo().equals(returnDTO.getType())){
				logger.error(returnDTO.getMess());
				reportGenerateErrMsg = "生成报告失败！";
			}else{
				reportFilePath = returnDTO.getFilePath();
			}
		}
	}

	/**
	 * @Description: 生成下载报错提示信息
	 *
	 * @MethodAuthor yph,2021年07月03日
	 */
	public void showGenerateReportErrMsg(){
		if(StringUtils.isNotBlank(reportGenerateErrMsg)){
			JsfUtil.addErrorMessage(reportGenerateErrMsg);
		}
	}

	@Override
	public IProcessData buildProcessData() {
		return super.buildProcessData();
	}

	@Override
	public void processData(List<?> list) {
		if (!CollectionUtils.isEmpty(list)) {
			List<Object[]> result = (List<Object[]>) list;
			for (Object[] obj : result) {
				//处理地区显示
				if (null!=obj[1]) {
					String[] split = obj[1].toString().split("_");
					if (split.length>1) {
						obj[1] = obj[1].toString().substring(obj[1].toString().indexOf("_")+1);
					}
				}
				if(null != obj[9]){
					String path = obj[9].toString();
					if(StringUtils.isBlank(path)){
						obj[9] = null;
					}
				}
			}
		}
	}


	/**
	 * @Description: 删除健康检查质控考核主表
	 *
	 * @MethodAuthor pw,2021年06月29日
	 */
	public void deleteAction(){
		if(null == rid){
			return;
		}
		String deleteSql = " UPDATE TD_ZW_ZK_CHECK_MAIN SET DEL_MARK=1 WHERE RID="+rid;
		try{
			this.commService.executeSql(deleteSql,null);
			JsfUtil.addSuccessMessage("删除成功！");
			this.searchAction();
		}catch(Exception e){
			e.printStackTrace();
			JsfUtil.addSuccessMessage("删除失败！");
		}
	}


	/**
	 * @Description: 编辑页二暂存
	 *
	 * @MethodAuthor pw,2021年07月7日
	 */
	public void saveSubAction(){
		try{
			if(validateSaveSub()){
				if(null != curCheckTable){
					curCheckTable.setStateMark(0);
				}
				changeCheckTable();
				//存储当前评估表对象 以及对应的子表结果及存在问题
				this.checkMainService.saveCheckSubAndUpdateTdZwZkCheckTable(this.curCheckTable);
				//更新考核日期及检测报告编号 避免切换了考核日期及检测报告 暂存后返回恢复到数据库存储的原始值
				this.checkMainService.updateTdZwZkCheckMainDateAndRptByEntity(this.checkMain);
				this.modInit();
				this.initSubAction();
				JsfUtil.addSuccessMessage("暂存成功！");
				RequestContext.getCurrentInstance().update("tabView:editForm2:dataPanel");
			}
		}catch (Exception e){
			e.printStackTrace();
			JsfUtil.addErrorMessage("暂存失败！");
		}
	}

	/**
	 * @Description: 编辑页二提交
	 *
	 * @MethodAuthor pw,2021年07月7日
	 */
	public void submitSubAction(){
		try{
			if(validateSubmitSub()){
				if(null != curCheckTable){
					curCheckTable.setStateMark(1);
				}
				changeCheckTable();
				//存储当前评估表对象 以及对应的子表结果及存在问题
				this.checkMainService.saveCheckSubAndUpdateTdZwZkCheckTable(this.curCheckTable);
				//更新考核日期及检测报告编号 避免切换了考核日期及检测报告 提交后返回恢复到数据库存储的原始值
				this.checkMainService.updateTdZwZkCheckMainDateAndRptByEntity(this.checkMain);
				// 最后一个考核明细表提交时，判断更新得分
				this.updateTotalScoreVal(null);
				this.modInit();
				JsfUtil.addSuccessMessage("提交成功！");
				this.initSubAction();
				this.forwardEdit3Page();
				RequestContext.getCurrentInstance().update("tabView");
				RequestContext.getCurrentInstance().scrollTo("tabView:editForm3:editTitleGrid2");
			}
		}catch (Exception e){
			e.printStackTrace();
			if(null != curCheckTable){
				curCheckTable.setStateMark(0);
			}
			JsfUtil.addErrorMessage("提交失败！");
		}
	}


	/**
	 * <p>方法描述： 在最后一个考核明细提交时，计算并更新主表总得分与考核结果
	 * 提交时需要更新主表 生成、上传时仅赋值并不需要更新
	 * </p>
	 * @MethodAuthor： pw 2023/4/6
	 **/
	private void updateTotalScoreVal(TdZwZkCheckMain zwZkCheckMain){
		if(null == zwZkCheckMain){
			zwZkCheckMain = checkMainService.findTdZwZkCheckMain(this.rid);
		}
		List<TdZwZkCheckTable> tmpCheckList = zwZkCheckMain.getCheckTables();
		boolean calMark = true;
		List<TdZwZkCheckItem> zkCheckItemList = new ArrayList<>();
		for(TdZwZkCheckTable checkTable:tmpCheckList) {
			List<TdZwZkCheckItem> checkItemList = checkTable.getCheckItems();
			if(CollectionUtils.isEmpty(checkItemList)){
				continue;
			}
			//是否所有的都是现场考核的项目
			boolean ifAllXc = true;
			for(TdZwZkCheckItem checkItem : checkItemList){
				boolean ifHasXc = "1".equals(checkItem.getFkByItemId().getExtendS1());
				if(ifHasXc){
					zkCheckItemList.add(checkItem);
				}else{
					ifAllXc = false;
				}
			}
			if (!ifAllXc && !new Integer(1).equals(checkTable.getStateMark())) {
				calMark = false;
				break;
			}
		}
		if(calMark){
			//key现场考核项目的项目码表rid
			Map<Integer, List<TdZwCheckSub>> checkSubMap = new HashMap<>();
			if(!CollectionUtils.isEmpty(zkCheckItemList)){
				//检测报告上传不存在 状态不是已核查 不更新主表总得分与考核结果
				if(null == zwZkCheckMain.getFkByJcRptId() || null == zwZkCheckMain.getFkByJcRptId().getState()
						|| 2 != zwZkCheckMain.getFkByJcRptId().getState()){
					return;
				}
				checkSubMap = this.checkMainService.findTdZwCheckSubListByRptRidForMapResult(zwZkCheckMain.getFkByJcRptId().getRid());
			}
			//key 健康检查质控考核评估考核项目rid
			Map<Integer, List<TdZwZkCheckSub>> checkSubListMap = checkMainService.findTdZwZkCheckSubListByCheckMainRidForMapResult(zwZkCheckMain.getRid());
			BigDecimal score = new BigDecimal(0);
			//否决项 是否有否的
			boolean ifUnHg = false;
			for(TdZwZkCheckTable checkTable:tmpCheckList){
				List<TdZwZkCheckItem> checkItems = checkTable.getCheckItems();
				if(ObjectUtil.isNotEmpty(checkItems)){
					for(TdZwZkCheckItem checkItem:checkItems){
						boolean ifHasXc = "1".equals(checkItem.getFkByItemId().getExtendS1());
						if(ifHasXc){
							List<TdZwCheckSub> checkSubList = checkSubMap.get(checkItem.getFkByItemId().getRid());
							if(!CollectionUtils.isEmpty(checkSubList)){
								//更新 现场考核项目的实得分
								checkItem.setScoreVal(checkSubList.get(0).getScoreVal());
								for(TdZwCheckSub checkSub : checkSubList){
									String ext1 = null == checkSub.getFkByScoreId() || null == checkSub.getFkByScoreId().getFkByItemTypeId() ?
											null : checkSub.getFkByScoreId().getFkByItemTypeId().getExtendS1();
									//非否决项 计算总分值
									if(!"3".equals(ext1) && null != checkSub.getScoreVal()){
										score = score.add(checkSub.getScoreVal());
									}
									//否决项 查看结果
									if("3".equals(ext1) && null != checkSub.getFkByScoreRstId() &&
											"0".equals(checkSub.getFkByScoreRstId().getExtendS1())){
										ifUnHg = true;
									}
								}
							}
						}else{
							List<TdZwZkCheckSub> checkSubList = checkSubListMap.get(checkItem.getRid());
							if(!CollectionUtils.isEmpty(checkSubList)){
								for(TdZwZkCheckSub checkSub:checkSubList){
									String ext1 = null == checkSub.getFkByScoreId() || null == checkSub.getFkByScoreId().getFkByItemTypeId() ?
											null : checkSub.getFkByScoreId().getFkByItemTypeId().getExtendS1();
									//非否决项 计算总分值
									if(!"3".equals(ext1) && null != checkSub.getScoreVal()){
										score = score.add(checkSub.getScoreVal());
									}
									//否决项 查看结果
									if("3".equals(ext1) && null != checkSub.getFkByScoreRstId() &&
											"0".equals(checkSub.getFkByScoreRstId().getExtendS1())){
										ifUnHg = true;
									}
								}
							}
						}
					}
				}
			}
			zwZkCheckMain.setTotalScoreVal(score);
			String ext1 = "";
			if(ifUnHg || score.compareTo(new BigDecimal("60")) < 0){
				//不合格 有否决项为否 或者总得分小于60分
				ext1 = "3";
			}else if(score.compareTo(new BigDecimal("90")) < 0){
				//合格 没有否的否决项 并且总得分大于等于60分小于90分
				ext1 = "2";
			}else{
				//优秀 没有否的否决项 并且总得分大于等于90分
				ext1 = "1";
			}
			zwZkCheckMain.setFkByCheckRstId(this.checkRstMap.get(ext1));
			//执行主表更新
			this.checkMainService.upsertEntity(zwZkCheckMain);
		}
	}

	/**
 	 * <p>方法描述：匹配规则</p>
 	 * @MethodAuthor qrr,2021年9月19日,matchRule
	 * */
	private TbZwZkRstRule matchRule(List<TbZwZkRstRule> rules,List<Object[]> checkSubSummarys) {
		Map<String, Object[]> summaryMap = new HashMap<>();
		Map<Integer, BigDecimal> itemTypeMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(checkSubSummarys)) {
			for (Object[] obj : checkSubSummarys) {
				if (null==obj[0]||null==obj[1]) {
					continue;
				}
				String key = obj[0]+"&"+obj[1];
				if (null==summaryMap.get(key)) {
					summaryMap.put(key, obj);
				}
				Integer itemTypeId = new Integer(obj[0].toString());
				if (null==itemTypeMap.get(itemTypeId)) {
					itemTypeMap.put(itemTypeId, new BigDecimal(obj[2].toString()));
				}else {
					BigDecimal current = null;
					current = new BigDecimal(obj[2].toString());
					BigDecimal val = itemTypeMap.get(itemTypeId);
					BigDecimal rst = val.add(current);
					itemTypeMap.put(itemTypeId, rst);
				}
			}
		}
		for (TbZwZkRstRule t : rules) {
			//匹配规则
			List<TbZwZkRstRuleSub> rstRuleSubs = t.getRstRuleSubs();
			if (!CollectionUtils.isEmpty(rstRuleSubs)) {
				boolean ifmatch = true;
				for (TbZwZkRstRuleSub ruleSub : rstRuleSubs) {
					TsSimpleCode itemTypeId = ruleSub.getFkByItemTypeId();
					TsSimpleCode scoreRstId = ruleSub.getFkByScoreRstId();
					TsSimpleCode scoreRst2Id = ruleSub.getFkByScoreRst2Id();
					if (null==itemTypeId||null==scoreRstId) {
						continue;
					}
					String key = itemTypeId.getRid()+"&"+scoreRstId.getRid();
					Object[] obj = summaryMap.get(key);
					Object[] obj2 = null;
					if (null!=scoreRst2Id) {
						String key2 = itemTypeId.getRid()+"&"+scoreRst2Id.getRid();
						obj2 = summaryMap.get(key2);
					}
					BigDecimal divide = new BigDecimal(0);//默认为0
					BigDecimal total = itemTypeMap.get(itemTypeId.getRid());
					if (null!=total) {
						BigDecimal val = new BigDecimal(0);
						if (null!=obj) {
							val = val.add(new BigDecimal(obj[2].toString()));
						}
						if (null!=obj2) {
							val = val.add(new BigDecimal(obj2[2].toString()));
						}
						val = val.multiply(new BigDecimal(100));
						if (val.intValue()>0 && total.intValue()>0) {
							divide = val.divide(total, RoundingMode.HALF_UP);
						}
					}
					Integer calTag = ruleSub.getCalTag();
					Integer calNum = ruleSub.getCalNum();
					if (null==calTag || null==calNum) {
						continue;
					}
					if (1==calTag.intValue()) {
						if (divide.intValue() < calNum.intValue()) {
							ifmatch = false;//存在未匹配到，则匹配下一个等级
							break;
						}
					}else if (2==calTag.intValue()) {
						if (calNum.intValue() != divide.intValue()) {
							ifmatch = false;//存在未匹配到，则匹配下一个等级
							break;
						}
					}else if (3==calTag.intValue()) {
						if (divide.intValue() >= calNum.intValue()) {
							ifmatch = false;//存在未匹配到，则匹配下一个等级
							break;
						}
					}else if (4==calTag.intValue()) {
						if (divide.intValue() <= calNum.intValue()) {
							ifmatch = false;//存在未匹配到，则匹配下一个等级
							break;
						}
					}else if (5==calTag.intValue()) {
						if (divide.intValue() > calNum.intValue()) {
							ifmatch = false;//存在未匹配到，则匹配下一个等级
							break;
						}
					}
				}
				if (ifmatch) {//全部匹配成功，得到考核等级
					return t;
				}
			}
		}
		return null;
	}

	/**
	 * @Description: 编辑页二撤销
	 *
	 * @MethodAuthor pw,2021年07月7日
	 */
	public void cancelSubAction(){
		if(null == curCheckTable){
			return;
		}
		try{
			checkMainService.cancelCheckTable(this.curCheckTable.getRid());
			this.dealCheckTable();
			this.tosave();
			this.modInit();
			this.initSubAction();
			JsfUtil.addSuccessMessage("撤销成功！");
			this.forwardEdit2Page();
			RequestContext.getCurrentInstance().update("tabView");
			RequestContext.getCurrentInstance().scrollTo("tabView:editForm2:editTitleGrid2");
		}catch(Exception e){
			e.printStackTrace();
			JsfUtil.addErrorMessage("撤销失败！");
		}
	}

	/**
	 * @Description: 编辑页二返回
	 *
	 * @MethodAuthor pw,2021年07月7日
	 */
	public void backSubAction(){
		this.rid = this.checkMain.getRid();
		this.forwardEditPage();
		RequestContext.getCurrentInstance().update("tabView");
	}


	/**
	 * @Description: 初始化扣分原因
	 *
	 * @MethodAuthor pw,2021年07月8日
	 */
	public void initDeductCause(TdZwZkCheckSub curTranCheckSub, Integer itemIndex, Integer subIndex){
		curCheckSub = curTranCheckSub;
		deductQuery = null;
		allScoreDeductList = new ArrayList<>();
		showScoreDeductList = new ArrayList<>();
		deductUpdateId = null;
		if(null == curCheckSub || null == itemIndex || null == subIndex){
			return;
		}
		List<TdZwZkCheckDeduct> tmpCheckDeductList = curTranCheckSub.getCheckDeductList();
		List<Integer> selectedScoreDeductRidList = new ArrayList<>();
		if(!CollectionUtils.isEmpty(tmpCheckDeductList)){
			for(TdZwZkCheckDeduct checkDeduct : tmpCheckDeductList){
				if(null == checkDeduct.getFkByDeductId() || null == checkDeduct.getFkByDeductId().getRid()){
					continue;
				}
				selectedScoreDeductRidList.add(checkDeduct.getFkByDeductId().getRid());
			}
		}

		allScoreDeductList = null == curTranCheckSub.getFkByScoreId() ? null :curTranCheckSub.getFkByScoreId().getDeductList();
		if(!CollectionUtils.isEmpty(allScoreDeductList)){
			sortScoreDeductList(allScoreDeductList);
			for(TbZwZkScoreDeduct scoreDeduct : allScoreDeductList){
				if(null == scoreDeduct.getRid() || null == scoreDeduct.getFkByDeductId() ||
						null == scoreDeduct.getFkByDeductId().getRid()){
					continue;
				}
				if(selectedScoreDeductRidList.contains(scoreDeduct.getRid())){
					scoreDeduct.setSelected(true);
				}else{
					scoreDeduct.setSelected(false);
				}
				showScoreDeductList.add(scoreDeduct);
			}
			sortScoreDeductList(showScoreDeductList);
		}

		deductUpdateId = "tabView:editForm2:deductCauseNames"+itemIndex.intValue()+"PK"+subIndex.intValue();
		RequestContext.getCurrentInstance().update("tabView:editForm2:deductCauseDialog");
		RequestContext.getCurrentInstance().execute("PF('DeductCauseDialog').show();");
	}


	/**
	 * @Description: 清空扣分原因
	 *
	 * @MethodAuthor pw,2021年07月8日
	 */
	public void clearDeductCause(TdZwZkCheckSub curTranCheckSub){
		if(null == curTranCheckSub){
			return;
		}
		curTranCheckSub.setCheckDeductList(new ArrayList<TdZwZkCheckDeduct>());
	}

	/**
	 * @Description: 过滤扣分原因列表
	 *
	 * @MethodAuthor pw,2021年07月8日
	 */
	public void executeQueryDeduct(){
		showScoreDeductList = new ArrayList<>();
		if(CollectionUtils.isEmpty(allScoreDeductList)){
			return;
		}
		sortScoreDeductList(allScoreDeductList);
		for(TbZwZkScoreDeduct zkCheckDeduct : allScoreDeductList){
			if(null == zkCheckDeduct.getFkByDeductId() ||
					StringUtils.isBlank(zkCheckDeduct.getFkByDeductId().getCodeName())){
				continue;
			}
			if(StringUtils.isNotBlank(deductQuery) && !zkCheckDeduct.getFkByDeductId().getCodeName()
					.contains(deductQuery.trim())){
				continue;
			}
			showScoreDeductList.add(zkCheckDeduct);
		}
		sortScoreDeductList(showScoreDeductList);
	}


	/**
	 * @Description: 弹出框选择扣分原因
	 *
	 * @MethodAuthor pw,2021年07月8日
	 */
	public void selectDeductCause(TbZwZkScoreDeduct curCheckDeduct){
		if(null == curCheckDeduct){
			return;
		}
	}


	/**
	 * @Description: 扣分原因确认
	 *
	 * @MethodAuthor pw,2021年07月8日
	 */
	public void surePickDeductCause(){
		if(!CollectionUtils.isEmpty(curCheckSub.getCheckDeductList())){
			curCheckSub.getCheckDeductList().clear();
		}else {
			curCheckSub.setCheckDeductList(new ArrayList<TdZwZkCheckDeduct>());
		}
		if(!CollectionUtils.isEmpty(allScoreDeductList)){
			for(TbZwZkScoreDeduct scoreDeduct : allScoreDeductList){
				if(!scoreDeduct.isSelected()){
					continue;
				}
				TdZwZkCheckDeduct checkDeduct = new TdZwZkCheckDeduct();
				checkDeduct.setFkByMainId(curCheckSub);
				checkDeduct.setFkByDeductId(scoreDeduct);
				checkDeduct.setCreateDate(new Date());
				checkDeduct.setCreateManid(Global.getUser().getRid());
				curCheckSub.getCheckDeductList().add(checkDeduct);
			}
		}

		RequestContext.getCurrentInstance().update(deductUpdateId);
		RequestContext.getCurrentInstance().execute("PF('DeductCauseDialog').hide();");
	}


	/**
	 * @Description: 依据结果子表得分结果更新评估考核项目的实得分
	 *
	 * @MethodAuthor pw,2021年07月9日
	 */
	@Deprecated
	public void changeItemScore(TdZwZkCheckItem tdZwZkCheckItem){
		if(null == tdZwZkCheckItem){
			return;
		}
		List<TdZwZkCheckSub> subList = tdZwZkCheckItem.getCheckSubList();
		BigDecimal decimal = null;
		if(!CollectionUtils.isEmpty(subList)){
			for(TdZwZkCheckSub sub : subList){
				BigDecimal subScore = sub.getScoreVal();
				if(null != subScore){
					if(null == decimal){
						decimal = subScore;
					}else{
						decimal = decimal.add(subScore);
					}
				}
			}
		}
		tdZwZkCheckItem.setScoreVal(MathUtils.clearPointEndZero(decimal));
	}


	/**
	 * @Description: 初始化评分项结果码表
	 *
	 * @MethodAuthor pw,2021年09月22日
	 */
	public void initScoreRstInfo(){
		this.scoreRstSimpleList = this.commService.findLevelSimpleCodesByTypeId("5585");
		this.scoreRstSimpleMap = new HashMap<>();
		//给个默认值
		this.unhgSoreRstRid = -1;
		if(CollectionUtils.isEmpty(this.scoreRstSimpleList)){
			this.scoreRstSimpleList = new ArrayList<>();
			return;
		}
		List<TsSimpleCode> removeSimpleList = new ArrayList<>();
		for(TsSimpleCode simpleCode : this.scoreRstSimpleList){
			this.scoreRstSimpleMap.put(simpleCode.getRid(), simpleCode);
			if("0".equals(simpleCode.getExtendS1())){
				this.unhgSoreRstRid = simpleCode.getRid();
			}
		}
		if(!CollectionUtils.isEmpty(removeSimpleList)){
			this.scoreRstSimpleList.removeAll(removeSimpleList);
		}
	}

	/**
	 * @Description: 结果子表得分结果失焦 验证失败提示
	 *
	 * @MethodAuthor pw,2021年07月9日
	 */
	@Deprecated
	public void showScoreErrorMessage(){
		JsfUtil.addErrorMessage("结果应大于等于0并且小于等于评估项分值！");
	}

	/**
	 * @Description: 编辑页二或者详情页二初始化
	 *
	 * @MethodAuthor pw,2021年07月7日
	 */
	private void initSubAction(){
		indexDescMap = new HashMap<>();
		zwZkCheckItemList = new ArrayList<>();
		if(null == curCheckTable){
			return;
		}

		//现场考核考核项目
		Map<Integer, TdZwCheckItem> checkSceneItemMap = new HashMap<>();
		//评估表id是否存在
		if(null != curCheckTable.getRid()){
			//查询评估表
			curCheckTable = this.checkMainService.findTdZwCheckTable(curCheckTable.getRid());
		}else {
			List<TdZwZkCheckItem> list = new ArrayList<>();
			try{
				for(TdZwZkCheckItem checkItem : curCheckTable.getCheckItems()){
					TdZwZkCheckItem tmpCheckItem = new TdZwZkCheckItem();
					ObjectCopyUtil.copyProperties(checkItem, tmpCheckItem);
					list.add(tmpCheckItem);
				}
			}catch (Exception e){
				e.printStackTrace();
			}
			TbZwZkBadrsnStand tmpStand = curCheckTable.getFkByCheckTableId();
			//不存在，new新的评估表对象
			curCheckTable = new TdZwZkCheckTable();
			curCheckTable.setCheckItems(list);
			curCheckTable.setFkByCheckTableId(tmpStand);
		}

		//非全量的curCheckTable.getCheckItems() 现场考核的另外处理
		List<TdZwZkCheckItem> checkItems = this.curCheckTable.getCheckItems();
		if(!CollectionUtils.isEmpty(checkItems)){
			//现场的
			List<TdZwZkCheckItem> zwZkCheckItems = new ArrayList<>();
			if (!CollectionUtils.isEmpty(checkItems)) {
				for (TdZwZkCheckItem item : checkItems) {
					if("1".equals(item.getFkByItemId().getExtendS1())){
						zwZkCheckItems.add(item);
					}
				}
				if(!CollectionUtils.isEmpty(zwZkCheckItems)){
					checkItems.removeAll(zwZkCheckItems);
				}
			}
			this.curCheckTable.setZkcheckItems(zwZkCheckItems);
			//现场核查的项目查看
			if(!CollectionUtils.isEmpty(zwZkCheckItems) && null != this.subEditTag && 0 == this.subEditTag){
				//zwZkCheckItems 全部是现场质控考核
				for(TdZwZkCheckItem checkItem : zwZkCheckItems){
					//现场考核 赋值
					if(null != this.checkMain.getFkByJcRptId()
							&& null != this.checkMain.getFkByJcRptId().getRid()){
						List<TdZwCheckItem>	checkItemLists =this.checkMainService.findTdZwCheckItems(this.checkMain.getFkByJcRptId().getRid(),
								checkItem.getFkByMainId().getFkByCheckTableId().getRid(),checkItem.getFkByItemId().getRid());
						if(!CollectionUtils.isEmpty(checkItemLists)){
							//现场考核的考核人
							if(null != this.subEditTag && 0 == this.subEditTag){
								this.curCheckTable.setCheckPsn(checkItemLists.get(0).getFkByMainId().getCheckPsn());
							}
							this.curCheckTable.setZkStateMark(checkItemLists.get(0).getFkByMainId().getFkByMainId().getState());
							checkItem.setScoreVal(checkItemLists.get(0).getScoreVal());
							for(TdZwCheckItem itm : checkItemLists){
								checkSceneItemMap.put(itm.getFkByItemId().getRid(), itm);
							}
						}
					}
				}
			}
		}

		if(null != this.subEditTag && 0 == this.subEditTag &&
				!CollectionUtils.isEmpty(this.curCheckTable.getZkcheckItems())){
			this.zwZkCheckItemList.addAll(this.curCheckTable.getZkcheckItems());
		}
		if(null != this.subEditTag && 1 == this.subEditTag &&
				!CollectionUtils.isEmpty(this.curCheckTable.getCheckItems())){
			this.zwZkCheckItemList.addAll(this.curCheckTable.getCheckItems());
		}
		if(CollectionUtils.isEmpty(zwZkCheckItemList)){
			return;
		}
		sortCheckItemList(zwZkCheckItemList);
		List<Integer> ridList = new ArrayList<>();
		for(TdZwZkCheckItem checkItem : zwZkCheckItemList){
			if(null == checkItem.getRid()){
				break;
			}
			ridList.add(checkItem.getRid().intValue());
		}
		//现场考核的不需要查询健康检查质控考核结果子表
		Map<Integer, List<TdZwZkCheckSub>> checkSubListMap = null != this.subEditTag && 1 == this.subEditTag ?
				checkMainService.findTdZwZkCheckSubListByMainRidsForMapResult(ridList) : Collections.EMPTY_MAP;
		boolean flag = false;
		if(CollectionUtils.isEmpty(checkSubListMap)){
			flag = true;
		}
		fillItemScoresMap(flag);
		if(CollectionUtils.isEmpty(itemScoresMap)){
			return;
		}
		for(TdZwZkCheckItem checkItem : zwZkCheckItemList){
			//现场考核 项目 对应的结果
			Map<Integer, TdZwCheckSub> checkSubMap = new HashMap<>();
			boolean ifHasXc = "1".equals(checkItem.getFkByItemId().getExtendS1());
			// 判断是否现场的 赋值现场的 通过主表rid以及项目rid 获取现场考核的结果子表以及对应的扣分原因
			if(ifHasXc && null != this.checkMain.getFkByJcRptId()
					&& null != this.checkMain.getFkByJcRptId().getRid() && !CollectionUtils.isEmpty(checkSceneItemMap)){
				TdZwCheckItem itm = checkSceneItemMap.get(checkItem.getFkByItemId().getRid());
				if(null != itm){
					List<TdZwCheckSub> tmpList = this.checkMainService.findTdZwZkCheckSubList(itm.getRid());
					if(!CollectionUtils.isEmpty(tmpList)){
						for(TdZwCheckSub sub : tmpList){
							checkSubMap.put(sub.getFkByScoreId().getRid(), sub);
						}
					}
				}
			}

			if(!CollectionUtils.isEmpty(checkSubListMap)){
				//已存在的记录
				List<TdZwZkCheckSub> tmpCheckSubList = checkSubListMap.get(checkItem.getRid());
				if(!CollectionUtils.isEmpty(tmpCheckSubList)){
					sortCheckSubList(tmpCheckSubList);
					checkItem.setCheckSubList(tmpCheckSubList);
				}
				if(CollectionUtils.isEmpty(tmpCheckSubList)){
					continue;
				}
				if(!CollectionUtils.isEmpty(tmpCheckSubList)){
					for(TdZwZkCheckSub checkSub : tmpCheckSubList){
						if(null != checkSub.getFkByScoreRstId()){
							checkSub.setSoreRstRid(checkSub.getFkByScoreRstId().getRid());
						}
					}
					List<TbZwZkScores> scoresList = itemScoresMap.get(checkItem.getFkByItemId().getRid());
					if(!CollectionUtils.isEmpty(scoresList)){
						Map<Integer,TbZwZkScores> scoreMap = new HashMap<>();
						for(TbZwZkScores zwZkScores : scoresList){
							scoreMap.put(zwZkScores.getRid().intValue(), zwZkScores);
						}
						for(TdZwZkCheckSub checkSub : tmpCheckSubList){
							if(null != checkSub.getFkByScoreId() && null != checkSub.getFkByScoreId().getRid()){
								TbZwZkScores tmpZkScores = scoreMap.get(checkSub.getFkByScoreId().getRid().intValue());
								//重新赋值质控指标分值维护的评分项维护列表以及扣分原因维护列表 避免出现懒加载异常
								if(null != tmpZkScores){
									checkSub.getFkByScoreId().setDeductList(tmpZkScores.getDeductList());
									checkSub.getFkByScoreId().setIndexList(tmpZkScores.getIndexList());
								}
								if(null != tmpZkScores && StringUtils.isNotBlank(tmpZkScores.getIndexStr())){
									indexDescMap.put(tmpZkScores.getRid(), indexStrMixIndent(tmpZkScores.getIndexStr()));
								}
							}
						}
					}
				}
			}else{
				List<TbZwZkScores> scoresList = itemScoresMap.get(checkItem.getFkByItemId().getRid());
				if(!CollectionUtils.isEmpty(scoresList)){
					List<TdZwZkCheckSub> tmpCheckSub = new ArrayList<>();
					for(TbZwZkScores zwZkScores : scoresList){
						TdZwCheckSub zwSub = checkSubMap.get(zwZkScores.getRid());
						TdZwZkCheckSub checkSub = new TdZwZkCheckSub();
						checkSub.setFkByMainId(checkItem);
						checkSub.setFkByScoreId(zwZkScores);
						checkSub.setCreateDate(new Date());
						checkSub.setCreateManid(Global.getUser().getRid());
						checkSub.setCheckDeductList(new ArrayList<TdZwZkCheckDeduct>());
						checkSub.setRmk(null);
						if(null != zwSub){
							//赋值实得分 备注 评分项结果 扣分原因
							checkSub.setScoreVal(zwSub.getScoreVal());
							checkSub.setRmk(zwSub.getRmk());
							checkSub.setFkByScoreRstId(zwSub.getFkByScoreRstId());
							List<TdZwCheckDeduct> tdZwCheckDeductList = zwSub.getTdZwCheckDeductList();
							if(!CollectionUtils.isEmpty(tdZwCheckDeductList)){
								for(TdZwCheckDeduct checkDeduct : tdZwCheckDeductList){
									TdZwZkCheckDeduct deduct = new TdZwZkCheckDeduct();
									deduct.setFkByDeductId(checkDeduct.getFkByDeductId());
									deduct.setFkByMainId(checkSub);
									deduct.setCreateDate(new Date());
									deduct.setCreateManid(Global.getUser().getRid());
									checkSub.getCheckDeductList().add(deduct);
								}
							}

						}
						tmpCheckSub.add(checkSub);
						if(null != zwZkScores && StringUtils.isNotBlank(zwZkScores.getIndexStr())){
							indexDescMap.put(zwZkScores.getRid(), indexStrMixIndent(zwZkScores.getIndexStr()));
						}
					}
					checkItem.setCheckSubList(tmpCheckSub);
				}
			}
		}
	}


	/**
	 * @Description: 增加首行缩进
	 *
	 * @MethodAuthor pw,2021年07月12日
	 */
	private String indexStrMixIndent(String indexStr){
		if(StringUtils.isBlank(indexStr)){
			return null;
		}
		StringBuffer buffer = new StringBuffer();
		if(indexStr.indexOf("</br>") != -1){
			String[] indexStrArr = indexStr.split("</br>");
			for(int i=0; i<indexStrArr.length; i++){
				buffer.append("<div style='text-indent: 2em;width:1220px;'>").append(indexStrArr[i]).append("</div>");
			}
		}else{
			buffer.append("<div style='text-indent: 2em;width:1220px;'>").append(indexStr).append("</div>");
		}
		return buffer.toString();
	}

	/**
	 * @Description: TdZwZkCheckItem排序
	 *
	 * @MethodAuthor pw,2021年07月7日
	 */
	private void sortCheckItemList(List<TdZwZkCheckItem> checkItemList){
		if(CollectionUtils.isEmpty(checkItemList)){
			return;
		}
		Collections.sort(checkItemList, new Comparator<TdZwZkCheckItem>() {
			@Override
			public int compare(TdZwZkCheckItem o1, TdZwZkCheckItem o2) {
				Integer num1 = null == o1.getFkByItemId() ? null : o1.getFkByItemId().getNum();
				Integer num2 = null == o2.getFkByItemId() ? null : o2.getFkByItemId().getNum();
				if(null != num1 && null != num2){
					return num1.compareTo(num2);
				}else if(null != num1){
					return 1;
				}else if(null != num2){
					return -1;
				}
				return 0;
			}
		});
	}

	/**
	 * @Description: TdZwZkCheckSub 排序
	 *
	 * @MethodAuthor pw,2021年07月7日
	 */
	private void sortCheckSubList(List<TdZwZkCheckSub> tmpCheckSubList){
		if(CollectionUtils.isEmpty(tmpCheckSubList)){
			return;
		}
		Collections.sort(tmpCheckSubList, new Comparator<TdZwZkCheckSub>() {
			@Override
			public int compare(TdZwZkCheckSub o1, TdZwZkCheckSub o2) {
				Integer xh1 = null != o1.getFkByScoreId() ? o1.getFkByScoreId().getXh() : null;
				Integer xh2 = null != o2.getFkByScoreId() ? o2.getFkByScoreId().getXh() : null;
				if(null != xh1 && null != xh2){
					return xh1.compareTo(xh2);
				}else if(null != xh1){
					return 1;
				}else if(null != xh2){
					return -1;
				}
				return 0;
			}
		});
	}


	/**
	 * @Description: 对评估表进行排序
	 *
	 * @MethodAuthor pw,2021年07月14日
	 */
	private void sortCheckTableList(List<TdZwZkCheckTable> checkTableList){
		if(CollectionUtils.isEmpty(checkTableList)){
			return;
		}
		Collections.sort(checkTableList, new Comparator<TdZwZkCheckTable>() {
			@Override
			public int compare(TdZwZkCheckTable o1, TdZwZkCheckTable o2) {
				Integer num1 = null == o1.getFkByCheckTableId() ? null : o1.getFkByCheckTableId().getXh();
				Integer num2 = null == o2.getFkByCheckTableId() ? null : o2.getFkByCheckTableId().getXh();
				if(null != num1 && null != num2){
					return num1.compareTo(num2);
				}else if(null != num1){
					return 1;
				}else if(null != num2){
					return -1;
				}
				return 0;
			}
		});
	}

	private void forwardSubPage(){
		initSubAction();
		if((null != this.subEditTag && 0 == this.subEditTag) || (null != curCheckTable.getStateMark() && 1 == curCheckTable.getStateMark())){
			this.forwardEdit3Page();
		}else{
			this.forwardEdit2Page();
		}
		RequestContext.getCurrentInstance().update("tabView");
		//进入页面 滑动到最上边
		RequestContext.getCurrentInstance().execute("window.scrollTo(0, 0)");
	}

	private void fillItemScoresMap(boolean flag){
		if(null == this.checkMain || null == this.curCheckTable.getFkByCheckTableId()){
			return;
		}
		List<TbZwZkScores> list = standService.findTbZwZkScoresListByMainId(this.curCheckTable.getFkByCheckTableId().getRid(), flag);
		if(!CollectionUtils.isEmpty(list)){
			for(TbZwZkScores zwZkScores : list){
				List<TbZwZkScoreDeduct> tmpList = zwZkScores.getDeductList();
				if(!CollectionUtils.isEmpty(tmpList)){
					List<TbZwZkScoreDeduct> removeList = new ArrayList<>();
					for(TbZwZkScoreDeduct tmpScoreDeduct : tmpList){
						if(null != tmpScoreDeduct.getFkByDeductId()
								&& null != tmpScoreDeduct.getFkByDeductId().getIfReveal()
								&& 1 != tmpScoreDeduct.getFkByDeductId().getIfReveal().intValue()){
							removeList.add(tmpScoreDeduct);
						}
					}
					if(!CollectionUtils.isEmpty(removeList)){
						zwZkScores.getDeductList().removeAll(removeList);
					}
				}
			}
		}
		initItemScoresMap(list);
	}
	/**
	 * @Description: 初始化项目RID对应的评分项列表Map
	 *
	 * @MethodAuthor pw,2021年07月7日
	 */
	private void initItemScoresMap(List<TbZwZkScores> list){
		itemScoresMap = new HashMap<>();
		if(CollectionUtils.isEmpty(list)){
			return;
		}
		for(TbZwZkScores tbZwZkScores : list){
			List<TbZwZkScoreIndex> tmpScoreIndexList = tbZwZkScores.getIndexList();
			//无评分项 无扣分原因 跳过
			if(CollectionUtils.isEmpty(tmpScoreIndexList)){
				continue;
			}
			TbZwZkScoreIndex scoreIndex = tmpScoreIndexList.get(0);
			if(null == scoreIndex.getFkByIndexId()){
				continue;
			}
			String codeLevelNo = scoreIndex.getFkByIndexId().getCodeLevelNo();//指标层级编码
			String[] split = codeLevelNo.split("\\.");
			if (null==split||0==split.length) {
				continue;
			}
			TsSimpleCode item = this.allItemMap.get(split[0]);//父级考核项目
			if(null == item){
				continue;
			}
			List<TbZwZkScores> fillMapList = itemScoresMap.get(item.getRid().intValue());
			if(null == fillMapList){
				fillMapList = new ArrayList<>();
			}
			fillMapList.add(tbZwZkScores);
			itemScoresMap.put(item.getRid().intValue(), fillMapList);
		}
	}

	/**
	 * @Description: 编辑页二暂存验证
	 *
	 * @MethodAuthor pw,2021年07月9日
	 */
	private boolean validateSaveSub(){
		boolean flag = true;
		if(null == curCheckTable){
			return false;
		}
		String checkPsnStr = curCheckTable.getCheckPsn();
		if(StringUtils.isNotBlank(checkPsnStr) && checkPsnStr.indexOf(",") != -1){
			JsfUtil.addErrorMessage("多个考核人需要以中文逗号隔开！");
			flag = false;
		}
		List<TdZwZkCheckItem> checkItemList = this.curCheckTable.getCheckItems();
		if(!CollectionUtils.isEmpty(checkItemList)){
			for(TdZwZkCheckItem tmpCheckItem : checkItemList){
				List<TdZwZkCheckSub> tmpCheckSubList = tmpCheckItem.getCheckSubList();
				String itemName = null == tmpCheckItem.getFkByItemId() ? null : tmpCheckItem.getFkByItemId().getCodeName();
				if(!CollectionUtils.isEmpty(tmpCheckSubList)){
					//验证实得分小于等于评估项分值并且大于等于0
					for(TdZwZkCheckSub tmpCheckSub : tmpCheckSubList){
						String indexStr = tmpCheckSub.getFkByScoreId().getIndexStr();
						if(StringUtils.isNotBlank(indexStr)){
							indexStr.replaceAll("</br>", "");
						}
						String tipStr = null == tmpCheckSub.getFkByScoreId() ? itemName : (itemName+" "+indexStr);
						Integer scoreRstRid = tmpCheckSub.getSoreRstRid();
						String ext1 = null == tmpCheckSub.getFkByScoreId() || null == tmpCheckSub.getFkByScoreId().getFkByItemTypeId()
								? null : tmpCheckSub.getFkByScoreId().getFkByItemTypeId().getExtendS1();
						if(null != scoreRstRid && "3".equals(ext1)){
							if(null != this.unhgSoreRstRid && this.unhgSoreRstRid.compareTo(scoreRstRid) != 0 &&
									!CollectionUtils.isEmpty(tmpCheckSub.getCheckDeductList())){
								JsfUtil.addErrorMessage(tipStr+"结果选择是时，存在问题必须为空！");
								flag = false;
							}
							tmpCheckSub.setFkByScoreRstId(new TsSimpleCode(scoreRstRid));
						}else{
							tmpCheckSub.setFkByScoreRstId(null);
						}
						BigDecimal scoreVal = tmpCheckSub.getScoreVal();
						BigDecimal score = null == tmpCheckSub.getFkByScoreId() ? null : 
								tmpCheckSub.getFkByScoreId().getScore();
						if(!"3".equals(ext1)){
							if(null != scoreVal && null != score){
								if(scoreVal.compareTo(score) > 0){
									JsfUtil.addErrorMessage(tipStr+"结果应大于等于0并且小于等于评估项分值！");
									flag = false;
								}else if(scoreVal.compareTo(score) == 0 &&
										!CollectionUtils.isEmpty(tmpCheckSub.getCheckDeductList())){
									JsfUtil.addErrorMessage(tipStr+"结果等于评估项分值存在问题必须为空！");
									flag = false;
								}
							}
						}else{
							tmpCheckSub.setScoreVal(null);
						}
						if(StringUtils.isNotBlank(tmpCheckSub.getRmk()) && tmpCheckSub.getRmk().length() > 2000){
							JsfUtil.addErrorMessage(tipStr+"备注过长！");
							flag = false;
						}
					}
				}
			}
		}
		return flag;
	}

	/**
	 * @Description: 编辑页二提交验证
	 *
	 * @MethodAuthor pw,2021年07月9日
	 */
	private boolean validateSubmitSub(){
		boolean flag = validateSaveSub();
		if(null == curCheckTable){
			return false;
		}
		String checkPsnStr = curCheckTable.getCheckPsn();
		if(StringUtils.isBlank(checkPsnStr)){
			JsfUtil.addErrorMessage("考核人不允许为空！");
			flag = false;
		}
		List<TdZwZkCheckItem> checkItemList = this.curCheckTable.getCheckItems();
		if(!CollectionUtils.isEmpty(checkItemList)){
			for(TdZwZkCheckItem tmpCheckItem : checkItemList){
				List<TdZwZkCheckSub> tmpCheckSubList = tmpCheckItem.getCheckSubList();
				String itemName = null == tmpCheckItem.getFkByItemId() ? null : tmpCheckItem.getFkByItemId().getCodeName();
				if(!CollectionUtils.isEmpty(tmpCheckSubList)){
					//验证实得分小于等于评估项分值并且大于等于0
					for(TdZwZkCheckSub tmpCheckSub : tmpCheckSubList){
						String indexStr = tmpCheckSub.getFkByScoreId().getIndexStr();
						if(StringUtils.isNotBlank(indexStr)){
							indexStr.replaceAll("</br>", "");
						}
						String tipStr = null == tmpCheckSub.getFkByScoreId() ? itemName : (itemName+" "+indexStr);
						Integer scoreRstRid = tmpCheckSub.getSoreRstRid();

						String ext1 = null == tmpCheckSub.getFkByScoreId() || null == tmpCheckSub.getFkByScoreId().getFkByItemTypeId()
								? null : tmpCheckSub.getFkByScoreId().getFkByItemTypeId().getExtendS1();
						boolean ifHasDeduct = false;
						if("3".equals(ext1)){
							if(null == scoreRstRid){
								JsfUtil.addErrorMessage("请选择"+tipStr+"结果！");
								flag = false;
							}else if(null != this.unhgSoreRstRid &&
									this.unhgSoreRstRid.compareTo(scoreRstRid) == 0){
								ifHasDeduct = true;
							}
						}else{
							BigDecimal scoreVal = tmpCheckSub.getScoreVal();
							BigDecimal score = null == tmpCheckSub.getFkByScoreId() ? null :
									tmpCheckSub.getFkByScoreId().getScore();
							if(null == scoreVal){
								JsfUtil.addErrorMessage("请填写"+tipStr+"结果！");
								flag = false;
							}else if(null != score && scoreVal.compareTo(score) < 0){
								ifHasDeduct = true;
							}
						}
						if(ifHasDeduct && CollectionUtils.isEmpty(tmpCheckSub.getCheckDeductList())){
							String tip = tipStr+"结果选择否时，存在问题不能为空！";
							if(!"3".equals(ext1)){
								tip = tipStr+"结果小于评估项分值时，存在问题不能为空！";
							}
							JsfUtil.addErrorMessage(tip);
							flag = false;
						}
					}
				}
			}
		}
		return flag;
	}

	/**
	 * @Description: 更新第一编辑页评估考核项目 用于显示的扣分原因字段
	 *
	 * @MethodAuthor pw,2021年07月10日
	 */
	private void changeDeductRsn(TdZwZkCheckItem checkItem, Map<Integer, List<TdZwZkCheckSub>> itmSubListMap){
		if(null != checkItem){
			List<TdZwZkCheckSub> checkSubList = checkItem.getCheckSubList();
			//由于checkMain是重新查询赋值的 所以 项目表下并没有子表关联 需重新赋值
			if(CollectionUtils.isEmpty(checkSubList) && !CollectionUtils.isEmpty(itmSubListMap) && null != checkItem.getRid()){
				checkSubList = itmSubListMap.get(checkItem.getRid());
				checkItem.setCheckSubList(checkSubList);
			}
			//子表排序
			sortCheckSubList(checkSubList);
			if(!CollectionUtils.isEmpty(checkSubList)){
				StringBuffer deductBuffer1 = new StringBuffer();
				StringBuffer deductBuffer2 = new StringBuffer();
				for(TdZwZkCheckSub checkSub : checkSubList){
					if(null != checkSub.getFkByScoreRstId()){
						checkSub.setSoreRstRid(checkSub.getFkByScoreRstId().getRid());
					}
					List<TdZwZkCheckDeduct> checkDeductList = checkSub.getCheckDeductList();
					if(CollectionUtils.isEmpty(checkDeductList)){
						continue;
					}
					for(TdZwZkCheckDeduct checkDeduct : checkDeductList){
						String deductRst = (null == checkDeduct.getFkByDeductId() ||
								null == checkDeduct.getFkByDeductId().getFkByDeductId()) ? null :
								checkDeduct.getFkByDeductId().getFkByDeductId().getCodeName();
						if(StringUtils.isNotBlank(deductRst)){
							deductBuffer1.append("；").append(deductRst);
							deductBuffer2.append("；<br/>").append(deductRst);
						}
					}
				}
				String deductRsn = deductBuffer1.toString();
				String deductRsn2 = deductBuffer2.toString();
				if(StringUtils.isNotBlank(deductRsn)){
					checkItem.setDeductRsn(deductRsn.substring(1));
				}else{
					checkItem.setDeductRsn(null);
				}
				if(StringUtils.isNotBlank(deductRsn2)){
					checkItem.setDeductRsn2(deductRsn2.substring(6));
				}else{
					checkItem.setDeductRsn2(null);
				}
			}else{
				checkItem.setDeductRsn(null);
				checkItem.setDeductRsn2(null);
			}
		}
	}

	/**
	 * @Description: 更新当前评估表的内容
	 *
	 * @MethodAuthor pw,2021年07月13日
	 */
	private void changeCheckTable(){
		if(null == this.curCheckTable){
			return;
		}

		//暂存、提交 需要给健康检查质控考核评估考核项目实得分赋值
		if(!CollectionUtils.isEmpty(curCheckTable.getCheckItems())){
			for(TdZwZkCheckItem checkItem : curCheckTable.getCheckItems()){
				List<TdZwZkCheckSub> subList = checkItem.getCheckSubList();
				BigDecimal decimal = null;
				for(TdZwZkCheckSub sub : subList){
					String ext1 = null == sub.getFkByScoreId() || null == sub.getFkByScoreId().getFkByItemTypeId() ? null :
							sub.getFkByScoreId().getFkByItemTypeId().getExtendS1();
					if(!"3".equals(ext1) && null != sub.getScoreVal()){
						if(null == decimal){
							decimal = BigDecimal.ZERO;
						}
						decimal = decimal.add(sub.getScoreVal());
					}
				}
				checkItem.setScoreVal(decimal);
			}
		}
		//避免现场考核的项目丢失
		if(!CollectionUtils.isEmpty(curCheckTable.getZkcheckItems())){
			curCheckTable.getCheckItems().addAll(curCheckTable.getZkcheckItems());
		}
	}

	/**
	 * <p>方法描述：getCheckTableMap</p>
	 * @MethodAuthor： yzz
	 * @Date：2022-06-17
	 **/
	public Map<Integer,TdZwZkCheckTable> getCheckTableMap(){
		Map<Integer,TdZwZkCheckTable> tableMap=new HashMap<>();
		List<TdZwZkCheckTable> checkTableList= checkMainService.findCheckTableByRid(checkMain.getRid());
		if(!CollectionUtils.isEmpty(checkTableList)){
			for (TdZwZkCheckTable table : checkTableList) {
				tableMap.put(table.getRid(),table);
			}
		}
		return tableMap;
	}
	/**
	 * @Description: 存在问题排序
	 *
	 * @MethodAuthor pw,2021年09月23日
	 */
	private void sortScoreDeductList(List<TbZwZkScoreDeduct> sortList){
		if(CollectionUtils.isEmpty(sortList)){
			return;
		}
		Collections.sort(sortList, new Comparator<TbZwZkScoreDeduct>() {
			@Override
			public int compare(TbZwZkScoreDeduct o1, TbZwZkScoreDeduct o2) {
				Integer xh1 = o1.getXh();
				Integer xh2 = o2.getXh();
				if(null != xh1 && null == xh2){
					return 1;
				}else if(null != xh2 && null == xh1){
					return -1;
				}else if(null != xh1 && null != xh2){
					return xh1.compareTo(xh2);
				}
				return 0;
			}
		});
	}



	/**
	 *  <p>方法描述：撤销功能</p>
	 * @MethodAuthor hsj 2023-04-01 14:33
	 */
	public void cancelAction(){
		if(this.checkMain==null || this.checkMain.getRid()==null){
			return;
		}
		try {
			checkMainService.updateCheckMainStatue(0, this.checkMain.getRid());
			this.modInitAction();
			JsfUtil.addSuccessMessage("撤销成功！");
		}catch(Exception e){
			e.printStackTrace();
			JsfUtil.addSuccessMessage("撤销失败！");
		}
	}

	/**
	 * <p>方法描述： 编辑页二文本框失焦提示 </p>
	 * @MethodAuthor： pw 2023/4/7
	 **/
	public void showCheckSubMsgTip(TdZwZkCheckSub checkSub){
		String itemName = null == checkSub.getFkByMainId().getFkByItemId() ? null :
				checkSub.getFkByMainId().getFkByItemId().getCodeName();
		String indexStr = null ==  checkSub.getFkByScoreId() ? null :
				checkSub.getFkByScoreId().getIndexStr();
		if(StringUtils.isNotBlank(indexStr)){
			indexStr.replaceAll("</br>", "");
		}
		String tipStr = null == checkSub.getFkByScoreId() ? itemName : (itemName+" "+indexStr);
		JsfUtil.addErrorMessage(tipStr + "结果应大于等于0并且小于等于评估项分值！");
	}

	public Integer getRid() {
		return rid;
	}
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	public TdZwZkCheckMain getCheckMain() {
		return checkMain;
	}
	public void setCheckMain(TdZwZkCheckMain checkMain) {
		this.checkMain = checkMain;
	}

	public Map<String, TsSimpleCode> getItemMap() {
		return itemMap;
	}

	public void setItemMap(Map<String, TsSimpleCode> itemMap) {
		this.itemMap = itemMap;
	}

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public String getSearchZoneName() {
		return searchZoneName;
	}

	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}

	public String getSearchZoneCode() {
		return searchZoneCode;
	}

	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}

	public String getSearchOrgName() {
		return searchOrgName;
	}

	public void setSearchOrgName(String searchOrgName) {
		this.searchOrgName = searchOrgName;
	}

	public Date getSearchCheckStartTime() {
		return searchCheckStartTime;
	}

	public void setSearchCheckStartTime(Date searchCheckStartTime) {
		this.searchCheckStartTime = searchCheckStartTime;
	}

	public Date getSearchCheckEndTime() {
		return searchCheckEndTime;
	}

	public void setSearchCheckEndTime(Date searchCheckEndTime) {
		this.searchCheckEndTime = searchCheckEndTime;
	}


	public List<Integer> getSearchStateMarkList() {
		return searchStateMarkList;
	}

	public void setSearchStateMarkList(List<Integer> searchStateMarkList) {
		this.searchStateMarkList = searchStateMarkList;
	}
	public List<SelectItem> getCheckTypeList() {
		return checkTypeList;
	}
	public void setCheckTypeList(List<SelectItem> checkTypeList) {
		this.checkTypeList = checkTypeList;
	}




	public StreamedContent getReportDownloadFile() {
		this.reportGenerateErrMsg = null;
		this.reportDownloadFile = null;
		try{
			if(null == this.checkMain || StringUtils.isBlank(this.checkMain.getWritePath())){
				reportGenerateErrMsg = "无报告可下载！";
				return null;
			}
			String filePath = "";
			String xnPath = JsfUtil.getAbsolutePath();
			String path = this.checkMain.getWritePath();
			if(path.indexOf(xnPath) != -1){
				filePath = path;
			}else{
				filePath = xnPath + path;
			}
			String hz = filePath.substring(filePath.lastIndexOf(".") + 1);
			String fileName = DateUtils.getYear() + this.checkMain.getFkByOrgId().getUnitname()+"职业卫生技术服务机构质量监测结果."+hz;
			String contextType = (fileName.endsWith(".docx") || fileName.endsWith(".doc")) ? "application/msword" : (fileName.endsWith(".pdf") ? "application/pdf" : null);
			if(null == contextType){
				reportGenerateErrMsg = "无效的报告类型！";
				return null;
			}
			InputStream stream = new FileInputStream(filePath);// stream不能关 关闭下载会报错
			this.reportDownloadFile = new DefaultStreamedContent(stream, contextType, URLEncoder.encode(fileName, "UTF-8"));
			if(null != reportDownloadFile){
				RequestContext.getCurrentInstance().execute("PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);");
			}
		}catch(Exception e){
			reportGenerateErrMsg = "报告下载失败！";
			e.printStackTrace();
		}
		return reportDownloadFile;
	}

	public void setReportDownloadFile(StreamedContent reportDownloadFile) {
		this.reportDownloadFile = reportDownloadFile;
	}

	public String getReportFilePath() {
		return reportFilePath;
	}

	public void setReportFilePath(String reportFilePath) {
		this.reportFilePath = reportFilePath;
	}

	public String getReportGenerateErrMsg() {
		return reportGenerateErrMsg;
	}

	public void setReportGenerateErrMsg(String reportGenerateErrMsg) {
		this.reportGenerateErrMsg = reportGenerateErrMsg;
	}

	public TdZwZkCheckTable getCurCheckTable() {
		return curCheckTable;
	}

	public void setCurCheckTable(TdZwZkCheckTable curCheckTable) {
		this.curCheckTable = curCheckTable;
	}

	public Map<Integer, List<TbZwZkScores>> getItemScoresMap() {
		return itemScoresMap;
	}

	public void setItemScoresMap(Map<Integer, List<TbZwZkScores>> itemScoresMap) {
		this.itemScoresMap = itemScoresMap;
	}

	public List<TdZwZkCheckItem> getZwZkCheckItemList() {
		return zwZkCheckItemList;
	}

	public void setZwZkCheckItemList(List<TdZwZkCheckItem> zwZkCheckItemList) {
		this.zwZkCheckItemList = zwZkCheckItemList;
	}

	public Map<Integer, String> getIndexDescMap() {
		return indexDescMap;
	}

	public void setIndexDescMap(Map<Integer, String> indexDescMap) {
		this.indexDescMap = indexDescMap;
	}

	public TdZwZkCheckSub getCurCheckSub() {
		return curCheckSub;
	}

	public void setCurCheckSub(TdZwZkCheckSub curCheckSub) {
		this.curCheckSub = curCheckSub;
	}

	public List<TbZwZkScoreDeduct> getAllScoreDeductList() {
		return allScoreDeductList;
	}

	public void setAllScoreDeductList(List<TbZwZkScoreDeduct> allScoreDeductList) {
		this.allScoreDeductList = allScoreDeductList;
	}

	public List<TbZwZkScoreDeduct> getShowScoreDeductList() {
		return showScoreDeductList;
	}

	public void setShowScoreDeductList(List<TbZwZkScoreDeduct> showScoreDeductList) {
		this.showScoreDeductList = showScoreDeductList;
	}

	public String getDeductQuery() {
		return deductQuery;
	}

	public void setDeductQuery(String deductQuery) {
		this.deductQuery = deductQuery;
	}

	public String getDeductUpdateId() {
		return deductUpdateId;
	}

	public void setDeductUpdateId(String deductUpdateId) {
		this.deductUpdateId = deductUpdateId;
	}


	public List<TsSimpleCode> getScoreRstSimpleList() {
		return scoreRstSimpleList;
	}

	public void setScoreRstSimpleList(List<TsSimpleCode> scoreRstSimpleList) {
		this.scoreRstSimpleList = scoreRstSimpleList;
	}

	public Map<Integer, TsSimpleCode> getScoreRstSimpleMap() {
		return scoreRstSimpleMap;
	}

	public void setScoreRstSimpleMap(Map<Integer, TsSimpleCode> scoreRstSimpleMap) {
		this.scoreRstSimpleMap = scoreRstSimpleMap;
	}

	public List<TsSimpleCode> getCheckRstList() {
		return checkRstList;
	}

	public void setCheckRstList(List<TsSimpleCode> checkRstList) {
		this.checkRstList = checkRstList;
	}

	public TdZwZkCheckSub getCheckSub() {
		return checkSub;
	}

	public void setCheckSub(TdZwZkCheckSub checkSub) {
		this.checkSub = checkSub;
	}

	public String getSelectRst() {
		return selectRst;
	}

	public void setSelectRst(String selectRst) {
		this.selectRst = selectRst;
	}

	public String getSelectRstIds() {
		return selectRstIds;
	}

	public void setSelectRstIds(String selectRstIds) {
		this.selectRstIds = selectRstIds;
	}

	public boolean isjCCancelBtn() {
		return jCCancelBtn;
	}

	public void setjCCancelBtn(boolean jCCancelBtn) {
		this.jCCancelBtn = jCCancelBtn;
	}

	public Integer getUnhgSoreRstRid() {
		return unhgSoreRstRid;
	}

	public void setUnhgSoreRstRid(Integer unhgSoreRstRid) {
		this.unhgSoreRstRid = unhgSoreRstRid;
	}

	public Integer getSubEditTag() {
		return subEditTag;
	}

	public void setSubEditTag(Integer subEditTag) {
		this.subEditTag = subEditTag;
	}

	public Map<String, TsSimpleCode> getAllItemMap() {
		return allItemMap;
	}

	public void setAllItemMap(Map<String, TsSimpleCode> allItemMap) {
		this.allItemMap = allItemMap;
	}
}
