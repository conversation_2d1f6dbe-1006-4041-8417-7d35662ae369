package com.chis.modules.heth.zkcheck.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2023-3-30
 */
@Entity
@Table(name = "TD_ZW_CHECK_SUB")
@SequenceGenerator(name = "TdZwCheckSub", sequenceName = "TD_ZW_CHECK_SUB_SEQ", allocationSize = 1)
public class TdZwCheckSub implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwCheckItem fkByMainId;
    private TbZwZkScores fkByScoreId;
    private BigDecimal scoreVal;
    private String rmk;
    private TsSimpleCode fkByScoreRstId;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private BigDecimal score;
    private Integer scoreRstId;
    private List<TdZwCheckDeduct> tdZwCheckDeductList;
    private String title;

    public TdZwCheckSub() {
    }

    public TdZwCheckSub(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwCheckSub")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwCheckItem getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwCheckItem fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "SCORE_ID")
    public TbZwZkScores getFkByScoreId() {
        return fkByScoreId;
    }

    public void setFkByScoreId(TbZwZkScores fkByScoreId) {
        this.fkByScoreId = fkByScoreId;
    }

    @Column(name = "SCORE_VAL")
    public BigDecimal getScoreVal() {
        return scoreVal;
    }

    public void setScoreVal(BigDecimal scoreVal) {
        this.scoreVal = scoreVal;
    }

    @Column(name = "RMK")
    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    @ManyToOne
    @JoinColumn(name = "SCORE_RST_ID")
    public TsSimpleCode getFkByScoreRstId() {
        return fkByScoreRstId;
    }

    public void setFkByScoreRstId(TsSimpleCode fkByScoreRstId) {
        this.fkByScoreRstId = fkByScoreRstId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Transient
    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    @Transient
    public Integer getScoreRstId() {
        return scoreRstId;
    }

    public void setScoreRstId(Integer scoreRstId) {
        this.scoreRstId = scoreRstId;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwCheckDeduct> getTdZwCheckDeductList() {
        return tdZwCheckDeductList;
    }

    public void setTdZwCheckDeductList(List<TdZwCheckDeduct> tdZwCheckDeductList) {
        this.tdZwCheckDeductList = tdZwCheckDeductList;
    }

    @Transient
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}