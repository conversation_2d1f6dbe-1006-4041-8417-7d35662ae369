package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.logic.SymCodeCommPO;
import com.chis.modules.heth.zkcheck.entity.TdZwZkCheckJudge;
import com.chis.modules.heth.zkcheck.entity.TdZwZkCheckMain;
import com.chis.modules.heth.zkcheck.entity.TdZwZkLabComp;
import com.chis.modules.heth.zkcheck.service.TdZwZkCheckJudgeService;
import com.chis.modules.heth.zkcheck.service.TdZwZkCheckMainService;
import com.chis.modules.heth.zkcheck.service.TdZwZkLabCompService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;
import org.springframework.util.CollectionUtils;

import javax.el.MethodExpression;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 机构质控结果评判
 */
@ManagedBean(name = "tdZwZkCheckJudgeBean")
@ViewScoped
public class TdZwZkCheckJudgeBean extends FacesEditBean {
    protected CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    protected TdZwZkCheckMainService checkMainService = SpringContextHolder.getBean(TdZwZkCheckMainService.class);
    protected TdZwZkCheckJudgeService checkJudgeService = SpringContextHolder.getBean(TdZwZkCheckJudgeService.class);
    protected TdZwZkLabCompService labCompService = SpringContextHolder.getBean(TdZwZkLabCompService.class);
    /**
     * 查询条件：地区集合
     */
    protected List<TsZone> zoneList;
    /**
     * 查询条件：地区名称
     */
    protected String searchZoneName;
    /**
     * 查询条件 - 地区编码（默认管辖地区）
     */
    protected String searchZoneCode;
    /**
     * 查询条件 - 被考核机构
     */
    private String searchOrgName;
    /**
     * 查询条件 - 考核日期开始
     */
    protected Date searchCheckStartTime;
    /**
     * 查询条件 - 考核日期结束
     */
    protected Date searchCheckEndTime;
    /**
     * 查询条件 - 考核类型
     */
    private Integer searchCheckType;
    /**
     * 查询条件 - 质控结果
     */
    private List<Integer> searchCheckRstList;
    /**
     * 查询条件 - 评判状态
     */
    private List<Integer> searchStateList;

    /**
     * 考核类型  码表
     */
    private List<TsSimpleCode> checkTypeList;

    /**
     * 关键不符合项  码表
     */
    private List<TsSimpleCode> keyList;

    /**
     * 选择的关键不符合项
     */
    private String selKeyIds;
    private String selkeyName;

    private Integer rid;

    /**
    * <p>Description：编辑页-质控考核实体 </p>
    * <p>Author： yzz 2025/8/12 </p>
    */
    protected TdZwZkCheckMain checkMain;

    /**
    * <p>Description：编辑页-质控评判实体 </p>
    * <p>Author： yzz 2025/8/12 </p>
    */
    private TdZwZkCheckJudge checkJudge;

    /**
    * <p>Description：编辑页-实验室比对实体 </p>
    * <p>Author： yzz 2025/8/12 </p>
    */
    private TdZwZkLabComp labComp;

    public TdZwZkCheckJudgeBean() {
        this.ifSQL = true;
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.zoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), false, "", "");
        if (!CollectionUtils.isEmpty(zoneList)) {
            this.searchZoneCode = zoneList.get(0).getZoneGb();
            this.searchZoneName = zoneList.get(0).getZoneName();
        }

        // 初始化考核日期
        searchCheckStartTime = DateUtils.getYearFirstDay(new Date());
        searchCheckEndTime = new Date();

        // 评判状态 默认待提交
        this.searchStateList = new ArrayList<>();
        this.searchStateList.add(0);

        /** 考核类型码表 */
        this.checkTypeList = this.commService.findLevelSimpleCodesByTypeId("5554");

        //初始化关键不符合项
        this.keyList = this.commService.findLevelSimpleCodesByTypeId("5647");

        this.searchAction();
    }


    @Override
    public void addInit() {

    }
    @Override
    public void viewInit() {

    }
    @Override
    public void modInit() {
        this.checkMain = checkMainService.findTdZwZkCheckMain(this.rid);
        if(this.checkMain==null){
            return;
        }
        this.checkJudge = checkJudgeService.findTdZwZkCheckJudge(this.rid);
        if(this.checkJudge==null){
            this.checkJudge = new TdZwZkCheckJudge();
        }
        //修改总得分  默认选择否
        this.checkJudge.setIfUpdateTotalScore(0);
        //实验室比对
        this.labComp = labCompService.findLatestLabComp(this.checkMain);
        if(this.labComp==null){
            this.labComp=new TdZwZkLabComp();
        }

    }



    @Override
    public void saveAction() {

    }
    @Override
    public String[] buildHqls() {
        StringBuffer buffer = new StringBuffer();
        buffer.append(" select T.RID, ");
        buffer.append("         CASE WHEN T2.ZONE_TYPE >2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME,'_')+1) ELSE T2.FULL_NAME END ZONE_NAME, ");
        buffer.append("         T1.UNITNAME, ");
        buffer.append("         T3.CODE_NAME as CHECK_TYPE_NAME, ");
        buffer.append("         T.CHECK_DATE, ");
        buffer.append("         T.IF_DEVELOP, ");
        buffer.append("         T4.CHECK_RST, ");
        buffer.append("         T4.STATE ");
        buffer.append(" from TD_ZW_ZK_CHECK_MAIN T ");
        buffer.append(" left join TS_UNIT T1 on T.ORG_ID = T1.RID ");
        buffer.append(" left join TS_ZONE T2 on T1.ZONE_ID = T2.RID ");
        buffer.append(" left join TS_SIMPLE_CODE T3 on T.CHECK_TYPE_ID=T3.RID ");
        buffer.append(" left join TD_ZW_ZK_CHECK_JUDGE T4 on T.RID = T4.MAIN_ID ");
        buffer.append(" where 1=1 ");
        // 地区
        if (StringUtils.isNotBlank(searchZoneCode)) {
            buffer.append(" AND T2.ZONE_GB LIKE :zoneCode escape '\\\' ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        // 被考核机构
        if (StringUtils.isNotBlank(searchOrgName)) {
            buffer.append(" AND T1.UNITNAME LIKE :orgName escape '\\\' ");
            this.paramMap.put("orgName", "%" + StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchOrgName).trim()) + "%");
        }
        // 考核日期
        if (null != searchCheckStartTime) {
            buffer.append(" AND T.CHECK_DATE>= TO_DATE('").append(DateUtils.formatDate(this.searchCheckStartTime))
                    .append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != searchCheckEndTime) {
            buffer.append(" AND T.CHECK_DATE<= TO_DATE('").append(DateUtils.formatDate(this.searchCheckEndTime))
                    .append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        // 考核类型
        if (null != this.searchCheckType) {
            buffer.append(" AND T3.RID = :queryCheckType ");
            this.paramMap.put("queryCheckType", this.searchCheckType);
        }
        // 质控结果
        if (!CollectionUtils.isEmpty(this.searchCheckRstList)) {
            buffer.append(" AND T4.CHECK_RST IN(").append(StringUtils.list2string(this.searchCheckRstList, ",")).append(") ");
        }
        // 评判状态
        if (!CollectionUtils.isEmpty(this.searchStateList)) {
            buffer.append(" AND nvl(T4.STATE,0) IN(").append(StringUtils.list2string(this.searchStateList, ",")).append(") ");
        }
        String searchSql = buffer + " ORDER BY T4.STATE,T2.ZONE_GB,T1.UNITNAME,T3.NUM,T3.CODE_NO,T.CHECK_DATE ";
        String countSql = " SELECT COUNT(*) FROM (" + buffer + ")";
        return new String[]{searchSql, countSql};
    }

    
    /**
    * <p>Description：关键不符合项 选中事件 </p>
    * <p>Author： yzz 2025/8/12 </p>
    */
    public void selectSymCodeAction(TsSimpleCode tsSimpleCode) {
        if(tsSimpleCode==null){
            return;
        }
        if("1".equals(tsSimpleCode.getExtendS1())){
            for(TsSimpleCode code:keyList){
                if(!code.equals(tsSimpleCode)){
                    code.setIfSelected(false);
                    code.setIfDisabled(true);
                }
            }
        }else {
            for(TsSimpleCode code:keyList){
                if("1".equals(code.getExtendS1())){
                    code.setIfDisabled(true);
                }
            }
        }
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }
    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }
    public String getSearchZoneName() {
        return searchZoneName;
    }
    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }
    public String getSearchZoneCode() {
        return searchZoneCode;
    }
    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }
    public String getSearchOrgName() {
        return searchOrgName;
    }
    public void setSearchOrgName(String searchOrgName) {
        this.searchOrgName = searchOrgName;
    }
    public Date getSearchCheckStartTime() {
        return searchCheckStartTime;
    }
    public void setSearchCheckStartTime(Date searchCheckStartTime) {
        this.searchCheckStartTime = searchCheckStartTime;
    }
    public Date getSearchCheckEndTime() {
        return searchCheckEndTime;
    }
    public void setSearchCheckEndTime(Date searchCheckEndTime) {
        this.searchCheckEndTime = searchCheckEndTime;
    }
    public Integer getSearchCheckType() {
        return searchCheckType;
    }
    public void setSearchCheckType(Integer searchCheckType) {
        this.searchCheckType = searchCheckType;
    }
    public List<Integer> getSearchCheckRstList() {
        return searchCheckRstList;
    }
    public void setSearchCheckRstList(List<Integer> searchCheckRstList) {
        this.searchCheckRstList = searchCheckRstList;
    }
    public List<Integer> getSearchStateList() {
        return searchStateList;
    }
    public void setSearchStateList(List<Integer> searchStateList) {
        this.searchStateList = searchStateList;
    }
    public List<TsSimpleCode> getCheckTypeList() {
        return checkTypeList;
    }
    public void setCheckTypeList(List<TsSimpleCode> checkTypeList) {
        this.checkTypeList = checkTypeList;
    }

    public Integer getRid() {
        return rid;
    }
    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwZkCheckMain getCheckMain() {
        return checkMain;
    }
    public void setCheckMain(TdZwZkCheckMain checkMain) {
        this.checkMain = checkMain;
    }

    public TdZwZkCheckJudge getCheckJudge() {
        return checkJudge;
    }
    public void setCheckJudge(TdZwZkCheckJudge checkJudge) {
        this.checkJudge = checkJudge;
    }

    public TdZwZkLabComp getLabComp() {
        return labComp;
    }
    public void setLabComp(TdZwZkLabComp labComp) {
        this.labComp = labComp;
    }

    public void setKeyList(List<TsSimpleCode> keyList) {
        this.keyList = keyList;
    }

    public List<TsSimpleCode> getKeyList() {
        return keyList;
    }
    public String getSelKeyIds() {
        return selKeyIds;
    }
    public void setSelKeyIds(String selKeyIds) {
        this.selKeyIds = selKeyIds;
    }
    public String getSelkeyName() {
        return selkeyName;
    }
    public void setSelkeyName(String selkeyName) {
        this.selkeyName = selkeyName;
    }



}
