package com.chis.modules.heth.zkcheck.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zkcheck.entity.TdZwZkCheckJudge;
import com.chis.modules.heth.zkcheck.entity.TdZwZkCheckMain;
import com.chis.modules.heth.zkcheck.service.TdZwZkCheckJudgeService;
import com.chis.modules.heth.zkcheck.service.TdZwZkCheckMainService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 机构质控结果评判
 */
@ManagedBean(name = "tdZwZkCheckJudgeBean")
@ViewScoped
public class TdZwZkCheckJudgeBean extends FacesEditBean {
    protected CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    protected TdZwZkCheckMainService checkMainService = SpringContextHolder.getBean(TdZwZkCheckMainService.class);
    protected TdZwZkCheckJudgeService checkJudgeService = SpringContextHolder.getBean(TdZwZkCheckJudgeService.class);
    /**
     * 查询条件：地区集合
     */
    protected List<TsZone> zoneList;
    /**
     * 查询条件：地区名称
     */
    protected String searchZoneName;
    /**
     * 查询条件 - 地区编码（默认管辖地区）
     */
    protected String searchZoneCode;
    /**
     * 查询条件 - 被考核机构
     */
    private String searchOrgName;
    /**
     * 查询条件 - 考核日期开始
     */
    protected Date searchCheckStartTime;
    /**
     * 查询条件 - 考核日期结束
     */
    protected Date searchCheckEndTime;
    /**
     * 查询条件 - 考核类型
     */
    private Integer searchCheckType;
    /**
     * 查询条件 - 质控结果
     */
    private List<Integer> searchCheckRstList;
    /**
     * 查询条件 - 评判状态
     */
    private List<Integer> searchStateList;

    /**
     * 考核类型  码表
     */
    private List<TsSimpleCode> checkTypeList;

    private Integer rid;

    protected TdZwZkCheckMain checkMain;

    private TdZwZkCheckJudge checkJudge;

    public TdZwZkCheckJudgeBean() {
        this.ifSQL = true;
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.zoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), false, "", "");
        if (!CollectionUtils.isEmpty(zoneList)) {
            this.searchZoneCode = zoneList.get(0).getZoneGb();
            this.searchZoneName = zoneList.get(0).getZoneName();
        }

        // 初始化考核日期
        searchCheckStartTime = DateUtils.getYearFirstDay(new Date());
        searchCheckEndTime = new Date();

        // 评判状态 默认待提交
        this.searchStateList = new ArrayList<>();
        this.searchStateList.add(0);

        /** 考核类型码表 */
        this.checkTypeList = this.commService.findLevelSimpleCodesByTypeId("5554");

        this.searchAction();
    }
    @Override
    public void addInit() {

    }
    @Override
    public void viewInit() {

    }
    @Override
    public void modInit() {
        this.checkMain = checkMainService.findTdZwZkCheckMain(this.rid);
        this.checkJudge = checkJudgeService.findTdZwZkCheckJudge(this.rid);

    }
    @Override
    public void saveAction() {

    }
    @Override
    public String[] buildHqls() {
        StringBuffer buffer = new StringBuffer();
        buffer.append(" select T.RID, ");
        buffer.append("         CASE WHEN T2.ZONE_TYPE >2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME,'_')+1) ELSE T2.FULL_NAME END ZONE_NAME, ");
        buffer.append("         T1.UNITNAME, ");
        buffer.append("         T3.CODE_NAME as CHECK_TYPE_NAME, ");
        buffer.append("         T.CHECK_DATE, ");
        buffer.append("         T.IF_DEVELOP, ");
        buffer.append("         T4.CHECK_RST, ");
        buffer.append("         T4.STATE ");
        buffer.append(" from TD_ZW_ZK_CHECK_MAIN T ");
        buffer.append(" left join TS_UNIT T1 on T.ORG_ID = T1.RID ");
        buffer.append(" left join TS_ZONE T2 on T1.ZONE_ID = T2.RID ");
        buffer.append(" left join TS_SIMPLE_CODE T3 on T.CHECK_TYPE_ID=T3.RID ");
        buffer.append(" left join TD_ZW_ZK_CHECK_JUDGE T4 on T.RID = T4.MAIN_ID ");
        buffer.append(" where 1=1 ");
        // 地区
        if (StringUtils.isNotBlank(searchZoneCode)) {
            buffer.append(" AND T2.ZONE_GB LIKE :zoneCode escape '\\\' ");
            this.paramMap.put("zoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        // 被考核机构
        if (StringUtils.isNotBlank(searchOrgName)) {
            buffer.append(" AND T1.UNITNAME LIKE :orgName escape '\\\' ");
            this.paramMap.put("orgName", "%" + StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchOrgName).trim()) + "%");
        }
        // 考核日期
        if (null != searchCheckStartTime) {
            buffer.append(" AND T.CHECK_DATE>= TO_DATE('").append(DateUtils.formatDate(this.searchCheckStartTime))
                    .append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != searchCheckEndTime) {
            buffer.append(" AND T.CHECK_DATE<= TO_DATE('").append(DateUtils.formatDate(this.searchCheckEndTime))
                    .append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        // 考核类型
        if (null != this.searchCheckType) {
            buffer.append(" AND T3.RID = :queryCheckType ");
            this.paramMap.put("queryCheckType", this.searchCheckType);
        }
        // 质控结果
        if (!CollectionUtils.isEmpty(this.searchCheckRstList)) {
            buffer.append(" AND T4.CHECK_RST IN(").append(StringUtils.list2string(this.searchCheckRstList, ",")).append(") ");
        }
        // 评判状态
        if (!CollectionUtils.isEmpty(this.searchStateList)) {
            buffer.append(" AND nvl(T4.STATE,0) IN(").append(StringUtils.list2string(this.searchStateList, ",")).append(") ");
        }
        String searchSql = buffer + " ORDER BY T4.STATE,T2.ZONE_GB,T1.UNITNAME,T3.NUM,T3.CODE_NO,T.CHECK_DATE ";
        String countSql = " SELECT COUNT(*) FROM (" + buffer + ")";
        return new String[]{searchSql, countSql};
    }


    public List<TsZone> getZoneList() {
        return zoneList;
    }
    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }
    public String getSearchZoneName() {
        return searchZoneName;
    }
    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }
    public String getSearchZoneCode() {
        return searchZoneCode;
    }
    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }
    public String getSearchOrgName() {
        return searchOrgName;
    }
    public void setSearchOrgName(String searchOrgName) {
        this.searchOrgName = searchOrgName;
    }
    public Date getSearchCheckStartTime() {
        return searchCheckStartTime;
    }
    public void setSearchCheckStartTime(Date searchCheckStartTime) {
        this.searchCheckStartTime = searchCheckStartTime;
    }
    public Date getSearchCheckEndTime() {
        return searchCheckEndTime;
    }
    public void setSearchCheckEndTime(Date searchCheckEndTime) {
        this.searchCheckEndTime = searchCheckEndTime;
    }
    public Integer getSearchCheckType() {
        return searchCheckType;
    }
    public void setSearchCheckType(Integer searchCheckType) {
        this.searchCheckType = searchCheckType;
    }
    public List<Integer> getSearchCheckRstList() {
        return searchCheckRstList;
    }
    public void setSearchCheckRstList(List<Integer> searchCheckRstList) {
        this.searchCheckRstList = searchCheckRstList;
    }
    public List<Integer> getSearchStateList() {
        return searchStateList;
    }
    public void setSearchStateList(List<Integer> searchStateList) {
        this.searchStateList = searchStateList;
    }
    public List<TsSimpleCode> getCheckTypeList() {
        return checkTypeList;
    }
    public void setCheckTypeList(List<TsSimpleCode> checkTypeList) {
        this.checkTypeList = checkTypeList;
    }

    public Integer getRid() {
        return rid;
    }
    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwZkCheckMain getCheckMain() {
        return checkMain;
    }
    public void setCheckMain(TdZwZkCheckMain checkMain) {
        this.checkMain = checkMain;
    }
}
