package com.chis.modules.heth.zkcheck.entity;

import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2024-7-4
 */
@Entity
@Table(name = "TD_ZK_BHK_EXPERT")
@SequenceGenerator(name = "TdZkBhkExpert", sequenceName = "TD_ZK_BHK_EXPERT_SEQ", allocationSize = 1)
public class TdZkBhkExpert implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZkExtractBhk fkByMainId;
	private TsZone fkByZoneId;
	private String unitName;
	private String userName;
	private TsUserInfo fkByUserId;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZkBhkExpert() {
	}

	public TdZkBhkExpert(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZkBhkExpert")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZkExtractBhk getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZkExtractBhk fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ZONE_ID")			
	public TsZone getFkByZoneId() {
		return fkByZoneId;
	}

	public void setFkByZoneId(TsZone fkByZoneId) {
		this.fkByZoneId = fkByZoneId;
	}	
			
	@Column(name = "UNIT_NAME")	
	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}	
			
	@Column(name = "USER_NAME")	
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}	
			
	@ManyToOne
	@JoinColumn(name = "USER_ID")			
	public TsUserInfo getFkByUserId() {
		return fkByUserId;
	}

	public void setFkByUserId(TsUserInfo fkByUserId) {
		this.fkByUserId = fkByUserId;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}