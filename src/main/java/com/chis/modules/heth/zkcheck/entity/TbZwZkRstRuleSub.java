package com.chis.modules.heth.zkcheck.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-9-18
 */
@Entity
@Table(name = "TB_ZW_ZK_RST_RULE_SUB")
@SequenceGenerator(name = "TbZwZkRstRuleSub", sequenceName = "TB_ZW_ZK_RST_RULE_SUB_SEQ", allocationSize = 1)
public class TbZwZkRstRuleSub implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbZwZkRstRule fkByMainId;
	private TsSimpleCode fkByItemTypeId;
	private TsSimpleCode fkByScoreRstId;
	private TsSimpleCode fkByScoreRst2Id;
	private Integer calTag;
	private Integer calNum;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TbZwZkRstRuleSub() {
	}

	public TbZwZkRstRuleSub(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbZwZkRstRuleSub")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TbZwZkRstRule getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TbZwZkRstRule fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_TYPE_ID")			
	public TsSimpleCode getFkByItemTypeId() {
		return fkByItemTypeId;
	}

	public void setFkByItemTypeId(TsSimpleCode fkByItemTypeId) {
		this.fkByItemTypeId = fkByItemTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "SCORE_RST_ID")			
	public TsSimpleCode getFkByScoreRstId() {
		return fkByScoreRstId;
	}

	public void setFkByScoreRstId(TsSimpleCode fkByScoreRstId) {
		this.fkByScoreRstId = fkByScoreRstId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "SCORE_RST2_ID")			
	public TsSimpleCode getFkByScoreRst2Id() {
		return fkByScoreRst2Id;
	}

	public void setFkByScoreRst2Id(TsSimpleCode fkByScoreRst2Id) {
		this.fkByScoreRst2Id = fkByScoreRst2Id;
	}	
			
	@Column(name = "CAL_TAG")	
	public Integer getCalTag() {
		return calTag;
	}

	public void setCalTag(Integer calTag) {
		this.calTag = calTag;
	}	
			
	@Column(name = "CAL_NUM")	
	public Integer getCalNum() {
		return calNum;
	}

	public void setCalNum(Integer calNum) {
		this.calNum = calNum;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}