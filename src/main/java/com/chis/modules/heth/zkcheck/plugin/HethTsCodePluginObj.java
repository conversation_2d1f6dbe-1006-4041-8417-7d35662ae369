package com.chis.modules.heth.zkcheck.plugin;

import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.enumn.SystemType;

/**
 * 码表的插件 码表编码是写死的
 *
 * <AUTHOR>
 * @createDate 2014年8月29日 上午11:07:02
 * @LastModify LuXuekun
 * @ModifyDate 2014年8月29日 上午11:07:02
 */
public class HethTsCodePluginObj {

    public static Set<TsCodeType> codeTypeSet;
    public static Set<TsSimpleCode> simpleCodeSet;

    static {
        codeTypeSet = new HashSet<TsCodeType>();
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5530", "质控考核项目", (short) 1, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5531", "扣分原因", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5533", "项类", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5534", "评分结果", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5535", "考核等级", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5554", "考核类型", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5574", "考核结论表的考核项目", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5575", "现场考核质量结论", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5572", "被考核人员属性", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5573", "考核内容", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5585", "现场考核-分值", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5586", "现场考核-评估结果", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5621", "抽取类别", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5598", "胸片结论", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5622", "听力检测结论", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5599", "胸片质量", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5637", "评估表-特殊标记", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5638", "分值-特殊标记", (short) 0, (short) 0));
        codeTypeSet.add(new TsCodeType(SystemType.ZK_CHECK, "5639", "分值-特殊标记说明", (short) 0, (short) 0));
    }
}
