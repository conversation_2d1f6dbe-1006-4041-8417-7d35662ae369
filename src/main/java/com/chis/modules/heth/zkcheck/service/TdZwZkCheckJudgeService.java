package com.chis.modules.heth.zkcheck.service;

import com.chis.modules.heth.zkcheck.entity.TdZwZkCheckJudge;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description:
 */

@Service
@Transactional(readOnly = false)
public class TdZwZkCheckJudgeService extends AbstractTemplate {


    public TdZwZkCheckJudge findTdZwZkCheckJudge(Integer rid) {
        return this.findEntityByMainId(TdZwZkCheckJudge.class,rid);
    }
}
