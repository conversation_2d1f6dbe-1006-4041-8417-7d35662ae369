package com.chis.modules.heth.zkcheck.service;

import com.chis.common.utils.DateUtils;
import com.chis.modules.heth.zkcheck.entity.TdZwZkCheckJudge;
import com.chis.modules.heth.zkcheck.entity.TdZwZkCheckMain;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description:
 */

@Service
@Transactional(readOnly = false)
public class TdZwZkCheckJudgeService extends AbstractTemplate {


    public TdZwZkCheckJudge findTdZwZkCheckJudge(TdZwZkCheckMain checkMain) {
        if(checkMain==null || checkMain.getRid()==null || checkMain.getFkByOrgId()==null || checkMain.getFkByOrgId().getRid()==null || checkMain.getFkByCheckTypeId()==null || checkMain.getFkByCheckTypeId().getRid()==null || checkMain.getCheckDate()==null){
            return null;
        }
        Integer orgId=checkMain.getFkByOrgId().getRid();
        Integer checkTypeId=checkMain.getFkByCheckTypeId().getRid();
        String checkYear= DateUtils.formatDate(checkMain.getCheckDate(),"yyyy");
        //获取同被考核机构，同考核类型，同年份最新一条记录
        String hql="select t from TdZwZkCheckJudge t where t.fkByOrgId.rid="+orgId+" and t.fkByCheckTypeId.rid="+checkTypeId+" and t.checkYear=?3 order by t.createTime desc";
        return this.findOneByHql(hql,orgId,checkTypeId,checkYear);

        return this.findEntityByMainId(TdZwZkCheckJudge.class,rid);
    }
}
