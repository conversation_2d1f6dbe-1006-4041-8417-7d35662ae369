package com.chis.modules.heth.zkcheck.json;

import java.io.Serializable;

/**
 * @Description: 调用生成报告的接口 请求JSON实体对象
 *
 * @ClassAuthor pw,2021年06月12日,WordReportJson
 */
public class WordReportJson implements Serializable {
    private static final long serialVersionUID = -3941235705752095417L;
    private Integer rid;
    private String rptCode;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getRptCode() {
        return rptCode;
    }

    public void setRptCode(String rptCode) {
        this.rptCode = rptCode;
    }
}
