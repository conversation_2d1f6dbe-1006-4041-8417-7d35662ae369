package com.chis.modules.system.encrypt.interceptor;

import com.chis.common.utils.CacheUtils;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.sm.Sm4Util;
import com.chis.modules.system.encrypt.annotation.EncryptField;
import com.chis.modules.system.encrypt.annotation.EncryptTable;
import org.hibernate.event.PostInsertEvent;
import org.hibernate.event.PostInsertEventListener;
import org.hibernate.event.def.DefaultLoadEventListener;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
/**
 *  <p>类描述：hibernate保存之后调用 监听器</p>
 * @ClassAuthor hsj 2023-01-14 8:52
 */
public class EncryptListener extends DefaultLoadEventListener implements PostInsertEventListener {
    @Override
    public void onPostInsert(PostInsertEvent postInsertEvent) {
       try {
           //是否需要加解密
           String infoEncryKey = CacheUtils.get("ENCRYKEY","infoEncryKey") == null ? null : CacheUtils.get("ENCRYKEY","infoEncryKey").toString();
           if(StringUtils.isBlank(infoEncryKey)){
               return;
           }
           Object entity = postInsertEvent.getEntity();
           List<String> annotationFields = getAnnotationField(entity);
           if(CollectionUtils.isEmpty(annotationFields)){
               return ;
           }
           for (String aField : annotationFields) {
               Class<?> cla = entity.getClass();
               Field[] fields = cla.getDeclaredFields();
               for (Field field : fields) {
                   field.setAccessible(true);
                   String keyName = field.getName();
                   Object value = field.get(entity);
                   if(keyName.equals(aField)){
                       field.set(entity,Sm4Util.strDecode(StringUtils.objectToString(value),infoEncryKey));
                   }
               }
           }
       }catch (Exception e){
           e.printStackTrace();
       }
    }
    private List<String> getAnnotationField(Object entity) {
        // 判断当前实体类是否有加解密注解
        Class<?> entityClass = entity.getClass();
        if (!entityClass.isAnnotationPresent(EncryptTable.class)) {
            return Collections.emptyList();
        }
        List<String> fields = new ArrayList<>();
        // 获取实体类下的所有成员并判断是否存在加解密注解
        Field[] declaredFields = entityClass.getDeclaredFields();
        for (Field field : declaredFields) {
            EncryptField encryptField = field.getAnnotation(EncryptField.class);
            if (null == encryptField) {
                continue;
            }
            fields.add(field.getName());
        }
        return fields;
    }
}