package com.chis.modules.system.tempmeta;

import java.util.Date;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;

import com.chis.common.utils.DateUtils;
import com.chis.modules.system.interfaces.ITempmetaResolve;
import com.chis.modules.system.logic.MetaCondition;

/**
 * 日期的解析
 * <AUTHOR>
 */
@Component(value="com.chis.ejb.tempmeta.comm.TempmetaResolve4Date")
public class TempmetaResolve4Date implements ITempmetaResolve {

	@Override
	public String resolve(MetaCondition condition, EntityManager em) {
		try {
			return DateUtils.getChineseStringDate(new Date());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public String description() {
		return "获取系统当前日期，如：2014年11月28日";
	}

	@Override
	public String testResult() {
		return null;
	}

}
