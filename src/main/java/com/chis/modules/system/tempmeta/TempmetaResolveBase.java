package com.chis.modules.system.tempmeta;

import java.util.List;

import javax.persistence.EntityManager;

import org.apache.commons.lang.StringUtils;

import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.interfaces.ITempmetaResolve;
import com.chis.modules.system.service.CommServiceImpl;

/**
 * 模板解析基础类
 * <AUTHOR>
 * @createDate 2015-03-17
 */
public abstract class TempmetaResolveBase implements ITempmetaResolve {

	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	
    /*businessId传值*/
    public Object baseResolve(String businessId, String key, String tableName, EntityManager em) throws Exception {
        if (StringUtils.isNotBlank(businessId)) {
            StringBuilder sb = new StringBuilder();
            sb.append(" SELECT ").append(key).append(" FROM ").append(tableName).append(" WHERE RID='");
            sb.append(businessId).append("'");
            List list = em.createNativeQuery(sb.toString()).getResultList();
            if (null != list && list.size() > 0) {
            	return list.get(0);
            }
        }
        return null;
    }

    /*businessId传值*/
    public Object baseResolveBySimpleCode(String businessId, String key, String tableName, EntityManager em,String codeNo) throws Exception {
        if (StringUtils.isNotBlank(businessId)) {
            StringBuilder sb = new StringBuilder();
            sb.append(" SELECT ").append(key).append(" FROM ").append(tableName).append(" WHERE RID='");
            sb.append(businessId).append("'");
            List list = em.createNativeQuery(sb.toString()).getResultList();
            if (null != list && list.size() > 0) {
            	String code=list.get(0).toString();
                List<TsSimpleCode> codeList=commService.findSimpleCodesByTypeId(codeNo);
                if(null != codeList && codeList.size()>0){
                	for(TsSimpleCode t:codeList){
                		if(code.equals(t.getCodeNo().toString())){
                			return t.getCodeName();
                		}
                	}
                }
            }
        }
        return null;
    }
    

}
