package com.chis.modules.system.tempmeta;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;

import com.chis.common.utils.DateUtils;
import com.chis.modules.system.logic.MetaCondition;
import com.chis.modules.system.interfaces.ITempmetaResolve;

/**
 * 时间的解析
 * <AUTHOR>
 */
@Component(value="com.chis.modules.system.tempmeta.TempmetaResolve4Time")
public class TempmetaResolve4Time implements ITempmetaResolve {

	@Override
	public String resolve(MetaCondition condition, EntityManager em) {
		try {
			return DateUtils.getDateTime();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public String description() {
		return "系统当前时间，如：2016-11-7 08:50:50";
	}

	@Override
	public String testResult() {
		return "2016-11-7 08:50:50";
	}

}
