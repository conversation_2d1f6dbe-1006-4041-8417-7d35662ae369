package com.chis.modules.system.tempmeta;

import java.util.List;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;

import com.chis.modules.system.interfaces.ITempmetaResolve;
import com.chis.modules.system.logic.MetaCondition;

/**
 * 用户名的解析
 * <AUTHOR>
 */
@Component(value="com.chis.ejb.tempmeta.comm.TempmetaResolve4Username")
public class TempmetaResolve4Username implements ITempmetaResolve {

	@Override
	public String resolve(MetaCondition condition, EntityManager em) {
		try {
		    StringBuilder sb = new StringBuilder();
		    sb.append(" SELECT USERNAME FROM TS_USER_INFO WHERE RID='");
		    sb.append(condition.getUserId()).append("'");
			
		    List list = em.createNativeQuery(sb.toString()).getResultList();
		    if(null != list && list.size() > 0) {
		    	return (String) list.get(0);
		    }
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public String description() {
		return "获取用户姓名";
	}

	@Override
	public String testResult() {
		return null;
	}

}
