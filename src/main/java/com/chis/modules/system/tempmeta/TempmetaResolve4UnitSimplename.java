package com.chis.modules.system.tempmeta;

import java.util.List;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;

import com.chis.modules.system.interfaces.ITempmetaResolve;
import com.chis.modules.system.logic.MetaCondition;

/**
 * 单位简称的解析
 * <AUTHOR>
 */
@Component(value="com.chis.ejb.tempmeta.comm.TempmetaResolve4UnitSimplename")
public class TempmetaResolve4UnitSimplename implements ITempmetaResolve {

	@Override
	public String resolve(MetaCondition condition, EntityManager em) {
		try {
		    StringBuilder sb = new StringBuilder();
		    sb.append(" SELECT UNIT_SIMPNAME FROM TS_UNIT WHERE RID='");
		    sb.append(condition.getUnitId()).append("'");
			
		    List list = em.createNativeQuery(sb.toString()).getResultList();
		    if(null != list && list.size() > 0) {
		    	return (String) list.get(0);
		    }
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public String description() {
		return "根据单位ID获取单位名称";
	}

	@Override
	public String testResult() {
		return null;
	}

}
