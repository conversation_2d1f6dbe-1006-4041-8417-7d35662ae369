package com.chis.modules.system.tempmeta;

import java.util.List;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;

import com.chis.modules.system.interfaces.ITempmetaResolve;
import com.chis.modules.system.logic.MetaCondition;

/**
 * 单位名称的解析
 * <AUTHOR>
 */
@Component(value="com.chis.ejb.tempmeta.comm.TempmetaResolve4UnitnameByUserId")
public class TempmetaResolve4UnitnameByUserId implements ITempmetaResolve {

	@Override
	public String resolve(MetaCondition condition, EntityManager em) {
		try {
		    StringBuilder sb = new StringBuilder();
		    sb.append(" SELECT T1.UNITNAME ");
		    sb.append(" FROM TS_UNIT T1 ");
		    sb.append(" INNER JOIN TS_USER_INFO T2 ON T2.UNIT_RID = T1.RID ");
		    sb.append(" WHERE T2.RID = '").append(condition.getUserId()).append("' ");
			
		    List list = em.createNativeQuery(sb.toString()).getResultList();
		    if(null != list && list.size() > 0) {
		    	return (String) list.get(0);
		    }
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public String description() {
		return "根据用户ID获取单位名称";
	}

	@Override
	public String testResult() {
		return null;
	}

}
