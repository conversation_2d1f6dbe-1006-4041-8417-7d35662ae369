package com.chis.modules.system.tempmeta;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;

import com.chis.common.utils.DateUtils;
import com.chis.modules.system.interfaces.ITempmetaResolve;
import com.chis.modules.system.logic.MetaCondition;

/**
 * 年份的解析
 * <AUTHOR>
 */
@Component(value="com.chis.ejb.tempmeta.comm.TempmetaResolve4Year")
public class TempmetaResolve4Year implements ITempmetaResolve {

	@Override
	public String resolve(MetaCondition condition, EntityManager em) {
		return DateUtils.getYear()+"年";
	}
	
	@Override
	public String description() {
		return "获取系统当前年份，如：2014年";
	}

	@Override
	public String testResult() {
		return null;
	}

}
