package com.chis.modules.system.interfaces;

import java.util.List;
import java.util.Map;

import com.chis.modules.system.logic.LucenePojo;
import com.chis.modules.system.web.LuceneBusinessType;

/**
 * 模块说明：全文检索加载查询文件及查询接口
 * 
 * <AUTHOR>
 * @createDate 2016年11月1日
 */
public interface ILuceneFile {

	/** ↓↓↓↓↓↓↓↓↓↓ 初始化文件索引 ↓↓↓↓↓↓↓↓↓↓ */
	/**
	 * 获取检索文件的查询对象基础
	 * 
	 * @return
	 */
	public List<LucenePojo> findSearchList();

	/**
	 * 将查询结果数据封装到集合中
	 * 
	 * @param fileSet
	 * @param fileMap
	 */
	public void addDataToMap(Map<String, LucenePojo> fileMap);

	/** ↑↑↑↑↑↑↑↑↑↑ 初始化文件索引 ↑↑↑↑↑↑↑↑↑↑ */

	/** ↓↓↓↓↓↓↓↓↓↓ 文件查询 ↓↓↓↓↓↓↓↓↓↓ */
	/**
	 * 组织查询字符串
	 * 
	 * @return
	 */
	public String powerSearchStr();

	/**
	 * 封装查询业务类别
	 * 
	 * @return
	 */
	public LuceneBusinessType findBusinessType();
	/** ↑↑↑↑↑↑↑↑↑↑ 文件查询 ↑↑↑↑↑↑↑↑↑↑ */

}
