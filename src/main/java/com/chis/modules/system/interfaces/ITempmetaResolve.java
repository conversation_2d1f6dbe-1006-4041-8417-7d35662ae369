package com.chis.modules.system.interfaces;

import javax.persistence.EntityManager;

import com.chis.modules.system.logic.MetaCondition;

/**
 * 模板元素的解析接口
 * <AUTHOR> 2014-11-28
 */
public interface ITempmetaResolve {

	/**
	 * 根据条件解析元素
	 * @param condition 查询条件
	 * @param em 实体管理器
	 * @return 解析结果
	 */
	public String resolve(MetaCondition condition, EntityManager em);
	
	public String description();

	public String testResult();
	
}
