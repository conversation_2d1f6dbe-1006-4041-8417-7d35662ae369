package com.chis.modules.system.interfaces;

import javax.persistence.EntityManager;

import com.chis.modules.system.entity.TsCodeRule;

/**
 * 用于自动编号生成
 * <AUTHOR>
 */
public interface IAutoCodeService {

    /**
     * 根据编码，前缀生成自动编号
     * @param rule 规则对象
     * @param pfx 动态前缀
     * @param em 实体管理器
     * @return 编号
     */
    public String buildCode(TsCodeRule rule, String pfx, EntityManager em);

    /**
     * @return 描述
     */
    public String desciption();

    /**
     * 根据编码，前缀生成测试编号
     * @param rule 规则对象
     * @param pfx 动态前缀
     * @return 测试编号
     */
    public String buildTestCode(TsCodeRule rule, String pfx);

}
