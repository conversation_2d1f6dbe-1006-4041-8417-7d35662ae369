package com.chis.modules.system.interfaces;

import java.util.List;

import com.chis.common.bean.FastReportData;
import com.chis.common.bean.FastReportDataRef;
import com.chis.modules.system.web.FastReportBean;

/**
 * FastReport报表的接口
 * <AUTHOR> 2015-02-11
 */
public interface IFastReport {

	/**
	 * 提供报表工具对象，该对象和托管Bean的生命周期一致
	 * @return 报表工具对象
	 */
	public FastReportBean getFastReportBean();
	
    /**
     * 提供报表的数据集，需重写<br/>
     * @return 数据集
     */
    public List<FastReportData> supportFastReportDataSet();
    
    /**
     * 报表数据集之间的关系，可重写<br/>
     * @return 数据集关系集合
     */
    public List<FastReportDataRef> supportFastReportDataRef();
}
