package com.chis.modules.system.filter;

import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.sm.SmUtilNew;
import com.chis.modules.system.entity.TsMenu;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import org.springframework.util.CollectionUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;

/***
 *  <p>类描述：页面转向权限过滤</p>
 *
 * @ClassAuthor maox,2020年1月17日,ToOtherPageFilter
 * <AUTHOR>
 *
 */
public class ToOtherPageFilter implements javax.servlet.Filter{


	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse res,
						 FilterChain arg2) throws IOException, ServletException {
		// TODO Auto-generated method stub
		HttpServletRequest req = (HttpServletRequest)request;
		HttpServletResponse response = (HttpServletResponse)res;
		String url = req.getRequestURI();
		boolean flag1 = true;
		HttpSession session = ((HttpServletRequest) request).getSession();

		//CSRF 攻击处理
		String referer = req.getHeader("referer");
		System.out.println("referer:"+referer);
		String scheme = req.getScheme();
		String serverName = req.getServerName();
		int serverPort = req.getServerPort();
		String contextPath = req.getContextPath();
		StringBuilder builder = new StringBuilder();
		builder.append(scheme).append("://").append(serverName);
		if(0 < serverPort){
			builder.append(":").append(serverPort);
		}
		builder.append(contextPath);
		List<String> whiteList = new ArrayList<>();
		whiteList.add(builder.toString());

		String whiteProp = PropertyUtils.getValueWithoutException("whiteList");
		if(StringUtils.isNotBlank(whiteProp)){
			for(String str : whiteProp.split(",")){
				whiteList.add(str);
			}
		}

		//referer 不为null的情况
		if(StringUtils.isNotBlank(referer)){
			flag1 = false;
			for(String str : whiteList){
				if(referer.contains(str)){
					flag1 = true;
					break;
				}
			}
		}

		if(flag1 && url.contains("faces") && !url.contains("registerSuccessFs.faces") && !url.contains("registerFs.faces")&& !url.contains("registerSuccess.faces")
				&& !url.contains("loginPsn.faces")&& !url.contains("register.faces")&& !url.contains("logout.faces")&& !url.contains("registerWh.faces")
				&& !url.contains("login.faces")&& !url.contains("browser.faces")&& !url.contains("head.faces")&& !url.contains("error.faces")&& !url.contains("ssoAuth.faces")){
			//System.out.print("--------------------"+url+"---------------");
			SessionData sessionData =(SessionData) session.getAttribute(SessionData.SESSION_DATA);
			if(sessionData != null &&sessionData.getUser() != null){
				TsUserInfo tsUserInfo = sessionData.getUser();
				List<TsMenu> allMenus = tsUserInfo.getAllMenus();
				List<TsMenu> inUseMenus = tsUserInfo.getInUseMenus();

				if(!tsUserInfo.getUserNo().equals(Constants.ADMIN)){
					boolean flag = false;

					for(TsMenu menu:allMenus){
						if(StringUtils.isNotBlank(menu.getMenuUri()) && menu.getMenuUri().contains("/")){
							String menuUrl = menu.getMenuUri().substring(menu.getMenuUri().lastIndexOf("/")+1, menu.getMenuUri().length());
							if(menuUrl.contains("?")){
								menuUrl = menuUrl.substring(0,menuUrl.indexOf("?"));
							}

							url = url.substring(url.lastIndexOf("/")+1, url.length());
							if(menuUrl.equals(url)){
								flag = true;
							}
						}

					}
					if(flag){
						flag1 = false;
						for(TsMenu menu2:inUseMenus){
							if(StringUtils.isNotBlank(menu2.getMenuUri()) && menu2.getMenuUri().contains("/")){
								String menuUrl = menu2.getMenuUri().substring(menu2.getMenuUri().lastIndexOf("/")+1, menu2.getMenuUri().length());
								if(menuUrl.contains("?")){
									menuUrl = menuUrl.substring(0,menuUrl.indexOf("?"));
								}
								if(menuUrl.equals(url)){
									flag1 = true;
								}
							}
						}
					}
				}
			}
		}

		String urlDeal = url.replaceAll("/+", "/");
		if (flag1 && urlDeal.startsWith("/webFile")) {
			flag1 = verifyPermissionsWebFile(urlDeal, session);
		}

		if(flag1){
			try {
				arg2.doFilter(new MyHttpServletRequestWrapper((HttpServletRequest)request), res);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}else{
			response.reset();
			ServletOutputStream out = response.getOutputStream();

			out.println("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\">");
			out.println("<HTML>");
			out.println("  <HEAD><script type='text/javascript'>window.open ('/login.faces','_top');</script></HEAD>");
			out.println("  <BODY>");
			out.println("  </BODY>");
			out.println("</HTML>");
			out.flush();
			out.close();
		}
	}

	private boolean verifyPermissionsWebFile(String url, HttpSession session) {
		String filePath = url.substring(8);

		String publicFilePathListStr = StringUtils.objectToString(PropertyUtils.getValueWithoutException("publicFilePathList"));
		List<String> publicFilePathList = StringUtils.string2list(publicFilePathListStr, ",");
		if (!CollectionUtils.isEmpty(publicFilePathList)) {
			for (String publicFilePath : publicFilePathList) {
				if (filePath.startsWith(publicFilePath)) {
					return true;
				}
			}
		}
		if (session.getAttribute(SessionData.SESSION_DATA) == null) {
			return false;
		}
		try {
			TsUserInfo user = ((SessionData) session.getAttribute(SessionData.SESSION_DATA)).getUser();
			if (user == null) {
				return false;
			}
		} catch (Exception e) {
			return false;
		}
		return true;
	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {
		// TODO Auto-generated method stub

	}
}
