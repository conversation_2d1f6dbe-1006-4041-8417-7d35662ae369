package com.chis.modules.system.filter;

import com.chis.common.utils.StringUtils;
import com.chis.common.utils.sm.SmUtilNew;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.*;

import java.net.URLDecoder;
import java.util.*;

/**
 * <p>类描述：HttpServletRequest包装类 用于对加密参数进行解密 </p>
 * pw 2024/2/6
 **/
public class MyHttpServletRequestWrapper  extends HttpServletRequestWrapper  {

    private Map<String, String[]> params;
    /** 加密参数名称 */
    private final String encryptParamName = "encryptdata";

    public MyHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);
    }

    @Override
    public String getParameter(String name) {
        String result = this.getRequest().getParameter(name);
        if (this.encryptParamName.equals(name)) {
            return result;
        }
        if (null == result) {
            this.fillParams(this.getRequest().getParameter(this.encryptParamName));
            result = this.getEncryptDataParameter(name);
        }
        return result;
    }

    @Override
    public String[] getParameterValues(String name) {
        String[] arr = this.getRequest().getParameterValues(name);
        if (null == arr) {
            this.fillParams(this.getRequest().getParameter(this.encryptParamName));
            if (!CollectionUtils.isEmpty(this.params)) {
                return this.params.get(name);
            }
        }
        return arr;
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        this.fillParams(this.getRequest().getParameter(this.encryptParamName));
        if (CollectionUtils.isEmpty(this.params)) {
            return super.getParameterMap();
        }
        Map<String, String[]> resultParameterMap = new HashMap<>();
        Map<String, String[]> parmaterMap = super.getParameterMap();
        if (!CollectionUtils.isEmpty(parmaterMap)) {
            resultParameterMap.putAll(parmaterMap);
        }
        resultParameterMap.putAll(this.params);
        return resultParameterMap;
    }

    @Override
    public Enumeration<String> getParameterNames() {
        this.fillParams(this.getRequest().getParameter(this.encryptParamName));
        if (CollectionUtils.isEmpty(this.params)) {
            return super.getParameterNames();
        }
        Vector<String> vector = new Vector<>();
        Enumeration<String> parameterNames = super.getParameterNames();
        if (null != parameterNames) {
            while(parameterNames.hasMoreElements()){
                vector.add(parameterNames.nextElement());
            }
        }
        vector.addAll(this.params.keySet());
        return vector.elements();
    }

    /**
     * <p>方法描述：获取解密后的参数 </p>
     * pw 2024/2/6
     **/
    private String getEncryptDataParameter(String name) {
        if (CollectionUtils.isEmpty(this.params)) {
            return null;
        }
        String result;
        String[] v = params.get(name);
        if (v == null) {
            result = null;
        } else {
            if (v.length > 0) {
                result = v[0];
            } else {
                result = null;
            }
        }
        return result;
    }

    /**
     * <p>方法描述：填充加密参数 </p>
     * pw 2024/2/6
     **/
    private void fillParams(String encryptdata){
        //加密参数为空 或者已经解密过 不再重复解密
        if (StringUtils.isBlank(encryptdata) || null != this.params) {
            return;
        }
        this.params = new HashMap<>();
        try{
            /** 对请求加密串，进行解密赋值  begin */
            String encryptdatainfo = SmUtilNew.sm2Decrypt123(encryptdata);
            // 分割请求参数串；获取所有的请求参数
            String [] data = encryptdatainfo.split("&");

            // 验证参数是否为空
            if (null != data && data.length > 0) {
                for (String dataStr :  data) {
                    // 验证参数串是否为空值，若为空值，继续执行下一个
                    if (StringUtils.isBlank(dataStr)) {
                        continue;
                    }
                    dataStr = URLDecoder.decode(dataStr, "UTF-8");
                    System.out.println(dataStr);
                    // 获取参数key
                    String key = dataStr.substring(0, dataStr.indexOf("="));
                    // 获取参数值
                    String value = dataStr.substring(dataStr.indexOf("=") + 1);
                    // 从MAP中获取相同key的value值
                    String[] vals =this.ifExists(this.params, key);
                    if (vals != null) {
                        List<String> dataList = new ArrayList(Arrays.asList(vals));
                        if (StringUtils.isNotBlank(value)) {
                            dataList.add(value);
                        }
                        // 将List转成数组存入参数MAP中
                        this.params.put(key, StringUtils.list2string(dataList, "@@").split("@@"));
                    } else {
                        // 若参数Map没有key值，则直接加入Map中
                        this.params.put(key, new String[]{value});
                    }
                }
            }
        }catch(Exception e){
            e.printStackTrace();
        }
    }

    private String[] ifExists( Map<String, String[]> paramMap, String key) {
        if(CollectionUtils.isEmpty(paramMap)) {
            return null;
        }
        if (paramMap.containsKey(key)) {
            return paramMap.get(key);
        }
        return null;
    }

}
