package com.chis.modules.system.listener;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.init.IAppInit;

import org.springframework.web.context.support.XmlWebApplicationContext;

/**
 * 容器启动好，所有spring管理的对象初始化好后， <br/>
 * 执行的方法onApplicationEvent <br/>
 * 
 * <AUTHOR>
 * @createTime 2015年12月26日
 */
@Component
public class WebContextListener implements ApplicationListener {

	public void onApplicationEvent(ApplicationEvent event) {

		//只在初始化“根上下文”的时候执行
		if (event.getSource() instanceof XmlWebApplicationContext) {
			if (((XmlWebApplicationContext) event.getSource()).getDisplayName().equals("Root WebApplicationContext")) {
				Map<String, IAppInit> beans = SpringContextHolder.getBeans(IAppInit.class);

				List<String> keyList = new ArrayList<String>(beans.keySet());

				Collections.sort(keyList, new Comparator<String>() {
					public int compare(String o1, String o2) {
						String sub1 = o1.substring(o1.lastIndexOf("_") + 1, o1.length());
						String sub2 = o2.substring(o2.lastIndexOf("_") + 1, o2.length());

						if (StringUtils.isNumeric(sub1) && StringUtils.isNumeric(sub2)) {
							return new Integer(sub1).compareTo(new Integer(sub2));
						} else if (StringUtils.isNumeric(sub1)) {
							return -1;
						} else if (StringUtils.isNumeric(sub2)) {
							return 1;
						} else {
							return 0;
						}
					}
				});

				System.err.println(Arrays.toString(keyList.toArray()));
				for (String pluginName : keyList) {
					try {
						IAppInit app = beans.get(pluginName);
						app.run();
					} catch (Exception e) {
						System.err.println("【插件初始化失败】：" + this.getClass().getName());
						e.printStackTrace();
					}
				}
			}
		}
	}

}
