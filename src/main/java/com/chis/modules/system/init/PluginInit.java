package com.chis.modules.system.init;

import java.util.*;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.chis.common.utils.CacheUtils;
import com.chis.common.utils.JaxbMapper;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsCodeRule;
import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsMenu;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsSystemParam;
import com.chis.modules.system.entity.TsSystemUpdate;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.logic.SQLSentence;
import com.chis.modules.system.logic.SQLSentences;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.PluginUpdateService;
import com.chis.modules.system.service.SystemServiceImpl;
import org.springframework.util.CollectionUtils;

/**
 * 初始化插件
 */
public abstract class PluginInit implements IAppInit {

	protected PluginUpdateService pluginUpdateService = SpringContextHolder.getBean(PluginUpdateService.class);
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	protected SystemServiceImpl  systemServiceImpl = SpringContextHolder.getBean(SystemServiceImpl.class);
	@PersistenceContext(unitName = "OracleDS")
    protected EntityManager em;
	/**
	 * 所有菜单HQL
	 */
	private static final String ALL_MENU = "select new TsMenu(t.menuEn) from TsMenu t";
	/**
	 * 所有参数HQL
	 */
	private static final String ALL_PARAM = "select new TsSystemParam(t.paramName) from TsSystemParam t";

	/**
	 * 初始化数据结构插件
	 */
	public abstract List<String> buildDataStructurePlugin();

	/**
	 * 初始化菜单插件
	 */
	public abstract Set<TsMenu> buildMenuPlugin();

	/**
	 * 初始化字段编码规则
	 */
	public abstract Set<TsCodeRule> buildCodeRulePlugin();

	/**
	 * 初始化系统参数
	 */
	public abstract Set<TsSystemParam> buildSystemParamPlugin();

	/**
	 * 初始化码表
	 */
	public abstract Set<TsSimpleCode> buildCodePlugin();

	/**
	 * 初始化码表类型
	 */
	public abstract Set<TsCodeType> buildCodeTypePlugin();


	/**
	 * 获取系统类型
	 */
	public abstract SystemType buildSystemType();

	@Override
	public void run() {
		this.initDataStructure();
		this.initAutoCode();
		this.initCodeType();
		this.initSimpleCode();
		this.initMenu();
		this.initParam();
		this.initEncrypt();
	}
	/**
	 *  <p>方法描述：初始化加密key缓存</p>
	 * @MethodAuthor hsj 2023-01-14 13:54
	 */
	private void initEncrypt() {
		Map<String, String> map = this.commService.findParamValues("'IF_INFO_ENCRY','INFO_ENCRY_KEY'");
		if(!CollectionUtils.isEmpty(map)){
			String ifInfoEncry = map.get("IF_INFO_ENCRY");
			if("1".equals(ifInfoEncry)){
				CacheUtils.put("ENCRYKEY","infoEncryKey",map.get("INFO_ENCRY_KEY"));
			}
		}
	}

	/**
	 * 初始化数据结构
	 */
	public void initDataStructure() {
		List<String> sqlList = this.buildDataStructurePlugin();
		if (sqlList != null && sqlList.size() > 0) {
			pluginUpdateService.dbUpdate(sqlList, this.buildSystemType());
		}
	}
	
	/**
	 * 初始化数据结构，更换成xml格式
	 * 解决问题：
	 * 1、java文件格式的升级语句存在限制，文件较多，不方便管理。
	 * 2、java文件格式不方便现场查看。
	 */
	public void initDataStructureByXml() {
		StringBuilder sb = new StringBuilder();
		sb.append("/db/SQL_").append(this.buildSystemType().getTypeNo()).append(".xml");
		String fileLocal = sb.toString();
		SQLSentences sqlSentences = JaxbMapper.fileToObject(fileLocal, SQLSentences.class);
		if(null != sqlSentences) {
			sb.setLength(0);
			List<SQLSentence> sqlList = sqlSentences.getSqlList();
			// 版本号从1存起，跟xml中的sql的索引保持一致
			if (null != sqlList && sqlList.size() > 0) {
				TsSystemUpdate tsu = pluginUpdateService.findSystemUpdateByXml(this.buildSystemType());
				int version = 0;
				if (null != tsu) {
					version = tsu.getCurVersion();
				}else {
					tsu = new TsSystemUpdate();
					tsu.setParamType(this.buildSystemType().getTypeNo().intValue());
				}
				
				int listsize = sqlList.size();
				if (listsize >= version) {
					String sql = null;
					int ver =0;
					for (int i = version; i < listsize; i++) {
						SQLSentence sqlObj = sqlList.get(i);
						if(null != sqlObj) {
							sql = sqlObj.getSql();
							ver = sqlObj.getVer();
						}
						// 如果sql语句为空时，则不执行
						if (StringUtils.isNotBlank(sql)) {
							try {
								System.out.println("RunSQL:"+sql);
								pluginUpdateService.executeUpdate(sql, ver, true);
							} catch (Exception e) {
								System.out.println("ErrorSQL:"+sql);
								return;
							}
						}
						tsu.setCurVersion(i+1);
						tsu.setUpdateTime(new Date());
						tsu = pluginUpdateService.updateSystemUpdate(tsu);
					}
				}
				if(listsize > version){
					this.showSqlWaining(sqlList, fileLocal);
				}

			}
		}
	}

	/**
	 * <p>方法描述：输出重复语句警告 </p>
	 * @MethodAuthor： pw 2022/8/16
	 **/
	private void showSqlWaining(List<SQLSentence> sqlList, String fileLocal){
		//key 1、列 tableName +#+ columnName 2、键 tableName+&+CONSTRAINT_NAME 3、tableName创建表
		//4、SEQUENCE_NAME+@ 5、INDEX_NAME+* 其他情况酌情添加 但注意分隔符不能重复
		Map<String,List<Integer>> sqlMap = new HashMap<>();
		Map<String,List<Integer>> sqlModifyMap = new HashMap<>();
		Set<Integer> verSet = new HashSet<>();
		StringBuffer sb = new StringBuffer();
		int listsize = sqlList.size();
		//检查是否有重复
		for(int i=0; i<listsize; i++){
			SQLSentence sqlObj = sqlList.get(i);
			String sql = null;
			int ver =0;
			if(null != sqlObj) {
				sql = sqlObj.getSql();
				ver = sqlObj.getVer();
			}
			if (StringUtils.isNotBlank(sql)){
				if(verSet.contains(ver)){
					sb.append(" ver：").append(ver).append("重复；");
				}else{
					verSet.add(ver);
				}
				String sqlKey = this.generateKey(sql);
				if(StringUtils.isBlank(sqlKey)){
					sb.append(" ver：").append(ver).append("类型未匹配到；");
				}else{
					//MODIFY或者DROP时候 注意两边各一个空格
					boolean ifModify = sql.toUpperCase().contains(" MODIFY ") || sql.toUpperCase().contains(" DROP ");
					List<Integer> tmpList = ifModify ? sqlModifyMap.get(sqlKey) : sqlMap.get(sqlKey);
					if(null == tmpList){
						tmpList = new ArrayList<>();
					}
					tmpList.add(ver);
					if(ifModify){
						sqlModifyMap.put(sqlKey, tmpList);
					}else{
						sqlMap.put(sqlKey, tmpList);
					}
				}
			}
		}
		if(!CollectionUtils.isEmpty(sqlMap)){
			for(Map.Entry<String,List<Integer>> mapEntity : sqlMap.entrySet()){
				List<Integer> tmpList = mapEntity.getValue();
				if(null != tmpList && tmpList.size() > 1){
					sb.append(" ver:").append(StringUtils.list2string(tmpList,",")).append("语句重复；");
				}
			}
		}
		if(!CollectionUtils.isEmpty(sqlModifyMap)){
			for(Map.Entry<String,List<Integer>> mapEntity : sqlModifyMap.entrySet()){
				List<Integer> tmpList = mapEntity.getValue();
				if(null != tmpList && tmpList.size() > 1){
					sb.append(" ver:").append(StringUtils.list2string(tmpList,",")).append("语句重复；");
				}
			}
		}
		if(sb.length() > 0){
			System.out.println("升级语句可能重复异常：");
			System.out.println(fileLocal+ sb);
		}
	}

	/**
	 * <p>方法描述：用于简单判断语句是否重复 生成key </p>
	 * @MethodAuthor： pw 2022/8/16
	 **/
	private String generateKey(String sql){
		StringBuffer keyBuffer = new StringBuffer();
		String sequenceName = this.generateKeyHelper(sql,"SEQUENCE_NAME");
		//优先处理不依赖TABLE_NAME的
		if(StringUtils.isNotBlank(sequenceName)){
			//注意 分隔符不可以重复 避免出现分隔符一@ 分隔符二@@的情况
			keyBuffer.append(sequenceName).append("@");
		}else{
			String indexName = this.generateKeyHelper(sql, "INDEX_NAME");
			if(StringUtils.isNotBlank(indexName)){
				//避免出现只有INDEX_NAME的情况
				keyBuffer.append("*").append(indexName).append("*");
			}
			String tableName = this.generateKeyHelper(sql,"TABLE_NAME");
			if(StringUtils.isBlank(tableName)){
				return keyBuffer.toString();
			}
			keyBuffer.append(tableName);
			String columnName = this.generateKeyHelper(sql,"COLUMN_NAME");
			if(StringUtils.isNotBlank(columnName)){
				keyBuffer.append("#").append(columnName);
			}
			String constraintName = this.generateKeyHelper(sql, "CONSTRAINT_NAME");
			if(StringUtils.isNotBlank(constraintName)){
				keyBuffer.append("&").append(constraintName);
			}


		}
		return keyBuffer.toString();
	}

	/**
	 * <p>方法描述：For 方法generateKey sql中截取key的值 </p>
	 * @MethodAuthor： pw 2022/8/16
	 **/
	private String generateKeyHelper(String sql, String key){
		if(StringUtils.isBlank(sql)){
			return null;
		}
		String tmpSql = sql.toUpperCase();
		if(tmpSql.indexOf(key) == -1){
			return null;
		}
		int beginIndex = sql.indexOf("'",tmpSql.indexOf(key)+key.length())+1;
		int endIndex = sql.indexOf("'",beginIndex);
		return sql.substring(beginIndex, endIndex);
	}

	/**
	 * 初始化菜单
	 */
	public void initMenu() {
		Set<TsMenu> menuSet = this.buildMenuPlugin();
		if(menuSet==null||menuSet.size()==0){
			return;
		}
		List<TsMenu> menuList = pluginUpdateService.findByHql(ALL_MENU,TsMenu.class);
		Map<String, TsMenu> allMenuMap = new HashMap<String, TsMenu>();
		if (null != menuList && menuList.size() > 0) {
			for (TsMenu t : menuList) {
				allMenuMap.put(t.getMenuEn(), t);
			}
		}

		for (TsMenu t : menuSet) {
			TsMenu menu = allMenuMap.get(t.getMenuEn());
			if (null == menu) {
				try {
					String localIP = StringUtils.getLocalIP();
					//记录ip地址
					t.setIpAddr(localIP);
					pluginUpdateService.save(t);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * 初始化系统参数
	 */
	public void initParam() {
		Set<TsSystemParam> paramSet = this.buildSystemParamPlugin();
		if(paramSet == null ||paramSet.size()==0){
			return;
		}
		List<TsSystemParam> list = pluginUpdateService.findByHql(ALL_PARAM,TsSystemParam.class);
		Map<String, TsSystemParam> map = new HashMap<String, TsSystemParam>();
		if (null != list && list.size() > 0) {
			for (TsSystemParam t : list) {
				map.put(t.getParamName(), t);
			}
		}

		for (TsSystemParam t : paramSet) {
			TsSystemParam param = map.get(t.getParamName());
			if (null == param) {
				try {
					pluginUpdateService.save(t);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * 初始化码表类型
	 */
	public void initCodeType() {
		Set<TsCodeType> codeTypeSet = this.buildCodeTypePlugin();
		if (null != codeTypeSet && codeTypeSet.size() > 0) {
			StringBuilder sb;
			for (TsCodeType type : codeTypeSet) {
				if (StringUtils.isNotBlank(type.getCodeTypeName())) {
					sb = new StringBuilder("select t from TsCodeType t where t.codeTypeName ='").append(
							type.getCodeTypeName()).append("' ");
					List<TsCodeType> list = pluginUpdateService.findByHql(sb.toString(),TsCodeType.class);
					if (null == list || list.size() == 0) {
						type.setParamType(type.getSystemType().getTypeNo()+0);
						pluginUpdateService.save(type);
					}
				}
			}
		}
	}

	/**
	 * 初始化码表
	 */
	public void initSimpleCode() {
		Set<TsSimpleCode> simpleCodeSet = this.buildCodePlugin();
		if (null != simpleCodeSet && simpleCodeSet.size() > 0) {
			StringBuilder sb;
			for (TsSimpleCode code : simpleCodeSet) {
				if (StringUtils.isNotBlank(code.getCodeNo())) {
					sb = new StringBuilder("select t from TsSimpleCode t where t.codeNo ='").append(code.getCodeNo())
							.append("' and t.tsCodeType.codeTypeName ='");
					sb.append(code.getCodeTypeNo()).append("' ");
					List<TsSimpleCode> codeList = pluginUpdateService.findByHql(sb.toString(),TsSimpleCode.class);
					if (null == codeList || codeList.size() == 0) {
						if (StringUtils.isNotBlank(code.getCodeTypeNo())) {
							sb = new StringBuilder("select t from TsCodeType t where t.codeTypeName ='").append(
									code.getCodeTypeNo()).append("' ");
							List<TsCodeType> list = pluginUpdateService.findByHql(sb.toString(),TsCodeType.class);
							if (null != list && list.size() > 0) {
								TsCodeType type = list.get(0);
								code.setTsCodeType(type);
								code.setIfReveal((short) 1);
								code.setCreateDate(new Date());
								code.setCreateManid(1);
								pluginUpdateService.save(code);
							}
						}
					}
				}
			}
		}
	}

	/**
	 * 初始化自动编号
	 */
	public void initAutoCode() {
		Set<TsCodeRule> codeRuleSet = this.buildCodeRulePlugin();
		if (null != codeRuleSet && codeRuleSet.size() > 0) {
			StringBuilder sb;
			for (TsCodeRule t : codeRuleSet) {
				sb = new StringBuilder("select t from TsCodeRule t where t.idcode ='").append(t.getIdcode()).append(
						"' ");
				List<TsCodeRule> list = pluginUpdateService.findByHql(sb.toString(),TsCodeRule.class);
				if (null == list || list.size() == 0) {
					SystemType systemType = t.getSystemType();
					t.setParamType(Integer.valueOf(systemType.getTypeNo()));
					pluginUpdateService.save(t);
				}
			}
		}
	}

}
