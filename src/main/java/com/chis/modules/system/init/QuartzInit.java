package com.chis.modules.system.init;

import java.util.List;

import org.quartz.Job;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TsQuartz;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.QuartzManager;

/**
 * 模块说明：定时任务启动
 * 
 * <AUTHOR>
 * @createDate 2016年7月13日
 */
@Component
public class QuartzInit implements IAppInit {

	@Autowired
	private SystemModuleServiceImpl service;

	@Override
	public void run() {
		try {
			List<TsQuartz> list = service.findAllRunTimeTask();
            if(list != null && list.size() > 0){
                for(TsQuartz t : list){
                    try {
                    	Job job = SpringContextHolder.getBean(t.getTaskClass());
                        QuartzManager.removeJob(t.getTaskCode());
                        QuartzManager.addJob(t.getTaskCode(), job, formatCronExpression(t));
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
            }
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
    private String formatCronExpression(TsQuartz tsQuartz) {
        if (tsQuartz.getPeriodKind().intValue() == 1) {
            return tsQuartz.getExpressions();
        } else {
            Short execycle = tsQuartz.getPeriodType();
            String excep="" ;
            if (execycle.intValue()== 4) {
                //每月几号如 "0 15 10 15 * ?"
                excep="0 "+tsQuartz.getMinOfDay() +" "+tsQuartz.getHourOfDay()+" "+ tsQuartz.getDayOfMon() +" * ?";
            } else if (execycle.intValue()== 3) {
                //每周周几如 "0 15 10 ? * MON"
                excep="0 "+tsQuartz.getMinOfDay() +" "+tsQuartz.getHourOfDay()+" ? * "+getWeekOfInt(tsQuartz.getDayOfWeek());
            } else if (execycle.intValue()== 2) {
                //每天几点如 "0 15 10 * * ?"
                excep="0 "+tsQuartz.getMinOfDay() +" "+tsQuartz.getHourOfDay()+" * * ?";
            } else if (execycle.intValue()== 1) {
                //每隔小时
                excep="0 0 */"+tsQuartz.getIntervalHour()+" * * ?";
            } else if (execycle.intValue()== 0) {
                //每隔分钟
                excep="0 */"+tsQuartz.getIntervalMin() +" * * * ?";
            }
            return excep;
        }
    }
    
    private String getWeekOfInt(short i){
        String week = null;
        switch (i){
            case 1:week = "MON";
                break;
            case 2:week = "TUE";
                break;
            case 3:week = "WED";
                break;
            case 4:week = "THU";
                break;
            case 5:week = "FRI";
                break;
            case 6:week = "SAT";
                break;
            case 7:week = "SUN";
                break;
            default:week = "MON";
        }
        return week;
    }

}
