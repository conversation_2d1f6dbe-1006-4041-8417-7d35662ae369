package com.chis.modules.system.init;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.chis.modules.system.job.SecheduleJob;
import com.chis.modules.system.service.ScheduleServiceImpl;
import com.chis.modules.system.utils.QuartzManager;

/**
 * 模块说明：日程提醒
 * 
 * <AUTHOR>
 * @createDate 2016年5月17日
 */
@Component
public class ScheduleInit implements IAppInit {

	@Autowired
	private ScheduleServiceImpl scheduleServiceImpl;

	@Override
	public void run() {

		try {
			// 0-任务编码 1-JOB表达式 2-执行人ID 3-执行人手机 4-日程标题 5-日程开始时间 6-提醒方式 7-是否周期性
			List<Object[]> list = scheduleServiceImpl.findNeedRemindSchdule();
			if (null != list && list.size() > 0) {
				for (Object[] o : list) {
					try {
						/**
						 * 解析Cron表达式，获取下一次执行的时间是否大于当前时间； 如果大于当前时间则可以添加任务，否则返回
						 */
						CronExpression exp = new CronExpression(o[1].toString());
						Date now = new Date();
						now = exp.getNextValidTimeAfter(now);
						if (null != now) {
							SecheduleJob job = new SecheduleJob();

							String scheduleId = "schedule_" + o[2] + "_" + o[0];
							Map<String, Object> map = new HashMap<String, Object>();
							map.put("userId", Integer.parseInt(o[2].toString()));
							map.put("mobile", null == o[3] ? "" : o[3].toString());
							map.put("scheduleId", o[0].toString());
							map.put("title", o[4].toString());
							map.put("time", o[5].toString());
							map.put("remindMtd", null == o[6] ? "" : o[6].toString());
							map.put("seasonal", Integer.parseInt(o[7].toString()));
							map.put("userNo",o[8].toString());

							QuartzManager.removeJob(scheduleId);
							QuartzManager.addJob(scheduleId, job, o[1].toString(), map);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
