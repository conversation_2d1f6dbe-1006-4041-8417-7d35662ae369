package com.chis.modules.system.job;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TdMsgMain;
import com.chis.modules.system.entity.TdMsgSub;
import com.chis.modules.system.entity.TsTxtype;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.MessageType;
import com.chis.modules.system.enumn.TxType;
import com.chis.modules.system.logic.MessExtendsPojo;
import com.chis.modules.system.protocol.IMsgSend;
import com.chis.modules.system.protocol.PushMsgToAppImpl;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.MsgSendUtil;
import com.chis.modules.system.utils.QuartzManager;

/**
 * 日程执行的体型Job
 * <AUTHOR>
 * @createTime 2015年10月16日
 */
@Component(value="com.chis.web.job.oa.SecheduleJob")
public class SecheduleJob implements Job {
	
	private Integer userId;
	private String mobile;
	private String scheduleId;
	private String title;
	private String time;
	private String remindMtd;
	private String userNo;
	private Integer seasonal; 
	
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	
	
	public SecheduleJob() {
	}

	public void execute(JobExecutionContext context) throws JobExecutionException {
		
		
		System.out.println("正在启动.....");
		JobDataMap dataMap = context.getJobDetail().getJobDataMap();
		
		try {
			this.userId = Integer.valueOf(dataMap.get("userId").toString());
			this.scheduleId = dataMap.get("scheduleId").toString();
			this.title = dataMap.get("title").toString();
			
			this.time = null == dataMap.get("time") ? DateUtils.formatDateTime(new Date()) : dataMap.get("time").toString();
			this.mobile = null == dataMap.get("mobile") ? "" : dataMap.get("mobile").toString();
			this.remindMtd = null == dataMap.get("remindMtd") ? null : dataMap.get("remindMtd").toString();
			this.userNo = null == dataMap.get("userNo") ? null : dataMap.get("userNo").toString();
			this.seasonal = Integer.valueOf(dataMap.get("seasonal").toString());
			
			System.err.println("【】：" + title);
			
			StringBuilder sb = new StringBuilder();
			sb.append("您有一个日程安排：").append(title).append("【").append(time).append("】");
			
			/**
			 * 保存系统消息
			 */
			TdMsgMain msgMain = new TdMsgMain();
			msgMain.setMessageType(MessageType.QUARTZ);
			msgMain.setSubType(10);
			msgMain.setInfoTitle(sb.toString());
			msgMain.setTsUserInfo(new TsUserInfo(userId));
			msgMain.setPublishTime(new Date());
			//截取传入的日程Id 
			String secId = null;
			String[] split = this.scheduleId.split("_");
			if(split.length == 3){
				secId = split[2];
				
				MessExtendsPojo extendPojo = new MessExtendsPojo();
				extendPojo.setRid(secId);
				extendPojo.setTableName("TD_OA_SCHEDULE");
				msgMain.setAppendKeys(JSON.toJSONString(extendPojo));
			}
			
			List<TdMsgSub> subList = new ArrayList<TdMsgSub>();
			TdMsgSub sub = new TdMsgSub();
			sub.setTdMsgMain(msgMain);
			sub.setTsUserInfo(new TsUserInfo(Integer.valueOf(userId)));
			subList.add(sub);
			
			flowBusinessService.saveTdMsg(msgMain, subList);
			
			MsgSendUtil.sendNewMsg(userId.toString());
			MsgSendUtil.updateMsgInfo();
			
			/**
			 * 其它通讯手段
			 */
			if(StringUtils.isNotBlank(remindMtd)) {
				List<TsTxtype> txList = commService.findTxtypeByIds(remindMtd);
				if(null != txList && txList.size() > 0) {
					for(TsTxtype tx: txList) {
						if(tx.getTxType().equals(TxType.DX) || tx.getTxType().equals(TxType.YY)) {
							/**
							 * 初始化语音发送方式
							 */
							IMsgSend send = null;
							try {
								send = (IMsgSend) Class.forName(tx.getImplClass()).newInstance();
								send.sendMsg(mobile, sb.toString());
							} catch (Exception e) {
								e.printStackTrace();
							}
						}else if(tx.getTxType().equals(TxType.APP) ){
							//参数集合
							Map<String,String> paramMap = new HashMap<String,String>();
							paramMap.put("taskId", secId);
							paramMap.put("msgRid", msgMain.getRid().toString());
							paramMap.put("type", "3");
							new PushMsgToAppImpl(sb.toString(), paramMap, userNo).sendJPush();
						}
					}
				}
			}
			
			//去除不是周期性的
			if(seasonal == 0) {
				QuartzManager.removeJob(scheduleId);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println("启动成功.....");
    }

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getScheduleId() {
		return scheduleId;
	}

	public void setScheduleId(String scheduleId) {
		this.scheduleId = scheduleId;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getTime() {
		return time;
	}

	public void setTime(String time) {
		this.time = time;
	}

	public String getRemindMtd() {
		return remindMtd;
	}

	public void setRemindMtd(String remindMtd) {
		this.remindMtd = remindMtd;
	}

	public Integer getSeasonal() {
		return seasonal;
	}

	public void setSeasonal(Integer seasonal) {
		this.seasonal = seasonal;
	}
	
}
