package com.chis.modules.system.job;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsHoliday;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.service.SystemServiceImpl;

@Component(value="com.chis.web.job.system.HolidayTypeJob")
public class HolidayTypeJob implements Job{
	private static String httpUrl = "http://apis.baidu.com/xiaogg/holiday/holiday";
	private SystemModuleServiceImpl moduleService = SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private SystemServiceImpl service = SpringContextHolder
			.getBean(SystemServiceImpl.class);
	@Override
	public void execute(JobExecutionContext context)
			throws JobExecutionException {
		System.out.println("正在启动.....");
//		JobDataMap dataMap = context.getJobDetail().getJobDataMap();
		String year = DateUtils.formatDate(new Date(), "yyyy");
		
		hol(year +"/01/01");
		hol(year +"/02/01");
		hol(year +"/03/01");
		hol(year +"/04/01");
		hol(year +"/05/01");
		hol(year +"/06/01");
		hol(year +"/07/01");
		hol(year +"/08/01");
		hol(year +"/09/01");
		hol(year +"/10/01");
		hol(year +"/11/01");
		hol(year +"/12/01");
	
		
	}
	public void hol(String date) {
		Date date1  = DateUtils.getMonthLastDay(DateUtils.parseDate(date));
		String h1 = getHoliday(date,date1);
		Map<String, Integer> map1 = toHashMap(h1);
		for (String key : map1.keySet()) {
			Date holdate=null;
			try {
				holdate = DateUtils.parseDate(key, "yyyyMMdd");
			} catch (ParseException e) {
				e.printStackTrace();
			}
			TsHoliday holiday = new TsHoliday();
			String holiday0 = service.selectByDate(holdate,null);
			if (StringUtils.isBlank(holiday0) ) {
				holiday.setCreateDate(new Date());
				holiday.setCreateManid(1);
				holiday.setHolDate(holdate);
				holiday.setDateType(map1.get(key));
				
				SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");
				String year0 = dateFormat.format(holdate);
				holiday.setHolYear(Integer.valueOf(year0));
				SimpleDateFormat dateFormat1 = new SimpleDateFormat("MM");
				String month = dateFormat1.format(holdate);
				holiday.setHolMon(Integer.valueOf(month));
				moduleService.saveOrUpdateHoliday(holiday);
			}
			
		}
	}
	private static HashMap<String, Integer> toHashMap(Object object)  
	   {  
	       HashMap<String, Integer> data = new HashMap<String, Integer>();  
	       // 将json字符串转换成jsonObject  
	       JSONObject jsonObject = JSONObject.fromObject(object);  
	       Iterator it = jsonObject.keys();  
	       // 遍历jsonObject数据，添加到Map对象  
	       while (it.hasNext())  
	       {  
	           String key = String.valueOf(it.next());  
	           Integer value = Integer.valueOf(jsonObject.get(key).toString()) ;  
	           data.put(key, value);  
	       }  
	       return data;  
	   }  
	public String getHoliday(String date0,Date date1) {
		Calendar calBegin = Calendar.getInstance();
		calBegin.setTime(DateUtils.parseDate(date0));
		Calendar calEnd = Calendar.getInstance();
		calEnd.setTime(date1);
		List<Date> lDate = new ArrayList();
		lDate.add(calBegin.getTime());
		while (date1.after(calBegin.getTime())) {
			calBegin.add(Calendar.DAY_OF_MONTH, 1);
			lDate.add(calBegin.getTime());
		}
		String result =null;
		StringBuffer sb = new StringBuffer();
		for (Date date : lDate) {
			sb.append(",").append( DateUtils.formatDate(date, "yyyyMMdd"));
		}
		result = request("http://apis.baidu.com/xiaogg/holiday/holiday", "d=" + sb.deleteCharAt(0).toString());
		return result;
	
	}
	public static String request(String httpUrl, String httpArg) {
		BufferedReader reader = null;
		String result = null;
		StringBuffer sbf = new StringBuffer();
		httpUrl = httpUrl + "?" + httpArg;
		
		try {
			URL url = new URL(httpUrl);
			HttpURLConnection connection = (HttpURLConnection) url
					.openConnection();
			connection.setRequestMethod("GET");
			// 填入apikey到HTTP header
			connection.setRequestProperty("apikey",
					"8a5aac472c7e88dbf4c7c01a4ce25b54");
			connection.connect();
			InputStream is = connection.getInputStream();
			reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			String strRead = null;
			while ((strRead = reader.readLine()) != null) {
				sbf.append(strRead);
				sbf.append("\r\n");
			}
			reader.close();
			result = sbf.toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

}
