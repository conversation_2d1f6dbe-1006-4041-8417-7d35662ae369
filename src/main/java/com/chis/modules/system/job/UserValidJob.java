package com.chis.modules.system.job;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

@Component(value="com.chis.web.job.system.UserValidJob")
public class UserValidJob implements Job{
	private SystemModuleServiceImpl moduleService = SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	@Override
	public void execute(JobExecutionContext context)
			throws JobExecutionException {
		System.out.println("正在启动.....");
		try {
			moduleService.updateExpiredUserInfo(DateUtils.getDate());
		}catch (Exception e){
			e.printStackTrace();
		}
		System.out.println("启动成功.....");
	}


}
