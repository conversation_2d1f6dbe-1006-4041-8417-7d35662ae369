package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * 
 * <AUTHOR>
 * @createTime 2016-10-14
 */
@Entity
@Table(name = "TD_FORM_STATISTICS_DEF")
@SequenceGenerator(name = "TdFormStatisticsDef", sequenceName = "TD_FORM_STATISTICS_DEF_SEQ", allocationSize = 1)
public class TdFormStatisticsDef implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdFormType tdFormTypeByTypeId;
	private String formCode;
	private TdFormTable tdFormTableByTableId;
	private String formName;
	private Integer state;
	private Integer formVersion;
	private Integer isDefault;
	private Integer formModel;
	private Date createDate;
	private Integer createManid;
	private String statisticsHtml;
	private String statisticsSql;
	private String dataShell;
	
	public TdFormStatisticsDef() {
	}

	public TdFormStatisticsDef(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFormStatisticsDef")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "TYPE_ID")			
	public TdFormType getTdFormTypeByTypeId() {
		return tdFormTypeByTypeId;
	}

	public void setTdFormTypeByTypeId(TdFormType tdFormTypeByTypeId) {
		this.tdFormTypeByTypeId = tdFormTypeByTypeId;
	}	
			
	@Column(name = "FORM_CODE")	
	public String getFormCode() {
		return formCode;
	}

	public void setFormCode(String formCode) {
		this.formCode = formCode;
	}	
			
	@ManyToOne
	@JoinColumn(name = "TABLE_ID")			
	public TdFormTable getTdFormTableByTableId() {
		return tdFormTableByTableId;
	}

	public void setTdFormTableByTableId(TdFormTable tdFormTableByTableId) {
		this.tdFormTableByTableId = tdFormTableByTableId;
	}	
			
	@Column(name = "FORM_NAME")	
	public String getFormName() {
		return formName;
	}

	public void setFormName(String formName) {
		this.formName = formName;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Column(name = "FORM_VERSION")	
	public Integer getFormVersion() {
		return formVersion;
	}

	public void setFormVersion(Integer formVersion) {
		this.formVersion = formVersion;
	}	
			
	@Column(name = "IS_DEFAULT")	
	public Integer getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(Integer isDefault) {
		this.isDefault = isDefault;
	}	
			
	@Column(name = "FORM_MODEL")	
	public Integer getFormModel() {
		return formModel;
	}

	public void setFormModel(Integer formModel) {
		this.formModel = formModel;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Column(name = "STATISTICS_HTML")	
	public String getStatisticsHtml() {
		return statisticsHtml;
	}

	public void setStatisticsHtml(String statisticsHtml) {
		this.statisticsHtml = statisticsHtml;
	}	
			
	@Column(name = "STATISTICS_SQL")	
	public String getStatisticsSql() {
		return statisticsSql;
	}

	public void setStatisticsSql(String statisticsSql) {
		this.statisticsSql = statisticsSql;
	}	
			
	@Column(name = "DATA_SHELL")	
	public String getDataShell() {
		return dataShell;
	}

	public void setDataShell(String dataShell) {
		this.dataShell = dataShell;
	}	
			
}