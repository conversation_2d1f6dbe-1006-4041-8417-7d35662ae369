package com.chis.modules.system.entity;

import javax.persistence.*;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @createTime 2020-8-6
 */
@Entity
@Table(name = "TD_SYS_APPLY_ANNEX")
@SequenceGenerator(name = "TdSysApplyAnnex", sequenceName = "TD_SYS_APPLY_ANNEX_SEQ", allocationSize = 1)
public class TdSysApplyAnnex implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdSysApplyAccount fkByMainId;
	private TbSysApplyAnnex fkByAnnexId;
	private String annexName;
	private String annexPath;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	public TdSysApplyAnnex() {
	}

	public TdSysApplyAnnex(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdSysApplyAnnex")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "MAIN_ID")
	public TdSysApplyAccount getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdSysApplyAccount fkByMainId) {
		this.fkByMainId = fkByMainId;
	}

	@ManyToOne
	@JoinColumn(name = "ANNEX_ID")
	public TbSysApplyAnnex getFkByAnnexId() {
		return fkByAnnexId;
	}

	public void setFkByAnnexId(TbSysApplyAnnex fkByAnnexId) {
		this.fkByAnnexId = fkByAnnexId;
	}

	@Column(name = "ANNEX_NAME")
	public String getAnnexName() {
		return annexName;
	}

	public void setAnnexName(String annexName) {
		this.annexName = annexName;
	}

	@Column(name = "ANNEX_PATH")
	public String getAnnexPath() {
		return annexPath;
	}

	public void setAnnexPath(String annexPath) {
		this.annexPath = annexPath;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE")
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID")
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE")
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID")
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

}