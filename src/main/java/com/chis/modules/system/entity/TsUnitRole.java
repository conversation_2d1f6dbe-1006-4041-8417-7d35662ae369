package com.chis.modules.system.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * TsUnitRole entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_UNIT_ROLE")
@SequenceGenerator(name = "TsUnitRole_seq", sequenceName = "TS_UNIT_ROLE_SEQ", allocationSize = 1)
public class TsUnitRole implements java.io.Serializable {

	private static final long serialVersionUID = 4767841807918672590L;
	private Integer rid;
	private TsUnit tsUnit;
	private TsRole tsRole;

	public TsUnitRole() {
	}
	
	public TsUnitRole(Integer rid) {
		this.rid = rid;
	}
	
	public TsUnitRole(TsUnit tsUnit, TsRole tsRole) {
		this.tsUnit = tsUnit;
		this.tsRole = tsRole;
	}

	public TsUnitRole(Integer rid, TsUnit tsUnit, TsRole tsRole) {
		this.rid = rid;
		this.tsUnit = tsUnit;
		this.tsRole = tsRole;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsUnitRole_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "UNIT_ID" )
	public TsUnit getTsUnit() {
		return this.tsUnit;
	}

	public void setTsUnit(TsUnit tsUnit) {
		this.tsUnit = tsUnit;
	}

	@ManyToOne
	@JoinColumn(name = "ROLE_ID" )
	public TsRole getTsRole() {
		return this.tsRole;
	}

	public void setTsRole(TsRole tsRole) {
		this.tsRole = tsRole;
	}

}