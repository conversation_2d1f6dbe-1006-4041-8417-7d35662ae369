package com.chis.modules.system.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2016-10-31
 */
@Entity
@Table(name = "TS_HOLIDAY")
@SequenceGenerator(name = "TsHoliday", sequenceName = "TS_HOLIDAY_SEQ", allocationSize = 1)
public class TsHoliday implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer holYear;
	private Integer holMon;
	private Date holDate;
	private Integer dateType;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private Date searchBeginDate;
	private Date searchEndDate;
	public TsHoliday() {
	}

	public TsHoliday(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsHoliday")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "HOL_YEAR")	
	public Integer getHolYear() {
		return holYear;
	}

	public void setHolYear(Integer holYear) {
		this.holYear = holYear;
	}	
			
	@Column(name = "HOL_MON")	
	public Integer getHolMon() {
		return holMon;
	}

	public void setHolMon(Integer holMon) {
		this.holMon = holMon;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "HOL_DATE")			
	public Date getHolDate() {
		return holDate;
	}

	public void setHolDate(Date holDate) {
		this.holDate = holDate;
	}	
			
	@Column(name = "DATE_TYPE")	
	public Integer getDateType() {
		return dateType;
	}

	public void setDateType(Integer dateType) {
		this.dateType = dateType;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	 @Transient
	public Date getSearchBeginDate() {
		return searchBeginDate;
	}

	public void setSearchBeginDate(Date searchBeginDate) {
		this.searchBeginDate = searchBeginDate;
	}
   @Transient
	public Date getSearchEndDate() {
		return searchEndDate;
	}

	public void setSearchEndDate(Date searchEndDate) {
		this.searchEndDate = searchEndDate;
	}	
			
}