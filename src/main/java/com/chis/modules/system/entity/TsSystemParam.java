package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.chis.modules.system.enumn.DictType;
import com.chis.modules.system.enumn.FieldType;
import com.chis.modules.system.enumn.SystemType;

/**
 * TsSystemParam entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_SYSTEM_PARAM")
@SequenceGenerator(name = "TsSystemParam_seq", sequenceName = "TS_SYSTEM_PARAM_SEQ", allocationSize = 1)
public class TsSystemParam implements java.io.Serializable {

	private static final long serialVersionUID = -1092236690374824312L;
	private Integer rid;
    private SystemType systemType;
	private String paramName;
	private String paramValue;
	private String paramRmk;
	private Short ifReveal;
	private String splsht;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

    private FieldType fieldType;
    private DictType dictType;
    private String dictValue;
    private Integer paramType;
    
	public TsSystemParam() {
	}
	
	public TsSystemParam(Integer rid) {
		this.rid = rid;
	}

	/**
	 * 不允许修改！！！
	 * @param paramName 参数名
	 */
	public TsSystemParam(String paramName) {
		this.paramName = paramName;
	}
	
	/**
	 * 不允许修改！！！
	 * @param paramName 参数名
	 * @param paramValue 参数值
	 */
	public TsSystemParam(String paramName, String paramValue) {
		this.paramName = paramName;
		this.paramValue = paramValue;
	}

	/**
	 * @param systemType 参数类型
	 * @param paramName 参数名
	 * @param paramValue 参数值
	 * @param paramRmk 参数描述
	 * @param ifReveal 是否启用
	 * @param splsht 拼音简写
	 * @param createDate 创建日期
	 * @param createManid 创建人（默认1）
	 */
	public TsSystemParam(SystemType systemType, String paramName, String paramValue, String paramRmk, Short ifReveal, String splsht, Date createDate, Integer createManid) {
		super();
		this.systemType = systemType;
		this.paramName = paramName;
		this.paramValue = paramValue;
		this.paramRmk = paramRmk;
		this.ifReveal = ifReveal;
		this.splsht = splsht;
		this.createDate = createDate;
		this.createManid = createManid;
	}

    /**
     * 不允许修改！！！
     * @param systemType 参数类型
     * @param paramName 参数名
     * @param paramValue 参数值
     * @param paramRmk 参数描述
     * @param ifReveal 是否启用
     * @param splsht 拼音简写
     * @param createDate 创建日期
     * @param createManid 创建人（默认1）
     * @param fieldType 字段类型
     * @param dictType 字典类型
     * @param dictValue 字典值
     */
    public TsSystemParam(SystemType systemType, String paramName, String paramValue, String paramRmk, Short ifReveal, String splsht, Date createDate, Integer createManid, FieldType fieldType, DictType dictType, String dictValue) {
        this.systemType = systemType;
        this.paramName = paramName;
        this.paramValue = paramValue;
        this.paramRmk = paramRmk;
        this.ifReveal = ifReveal;
        this.splsht = splsht;
        this.createDate = createDate;
        this.createManid = createManid;
        this.fieldType = fieldType;
        this.dictType = dictType;
        this.dictValue = dictValue;
    }
    
    public TsSystemParam(Integer paramType, String paramName, String paramValue, String paramRmk, Short ifReveal, String splsht, Date createDate, Integer createManid, FieldType fieldType, DictType dictType, String dictValue) {
        this.paramType = paramType;
        this.paramName = paramName;
        this.paramValue = paramValue;
        this.paramRmk = paramRmk;
        this.ifReveal = ifReveal;
        this.splsht = splsht;
        this.createDate = createDate;
        this.createManid = createManid;
        this.fieldType = fieldType;
        this.dictType = dictType;
        this.dictValue = dictValue;
    }

    // Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsSystemParam_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

    @Enumerated
    @Transient
    public SystemType getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemType systemType) {
        this.systemType = systemType;
    }

	@Column(name = "PARAM_NAME", unique = true , length = 20)
	public String getParamName() {
		return this.paramName;
	}

	public void setParamName(String paramName) {
		this.paramName = paramName;
	}

	@Column(name = "PARAM_VALUE" , length = 1000)
	public String getParamValue() {
		return this.paramValue;
	}

	public void setParamValue(String paramValue) {
		this.paramValue = paramValue;
	}

	@Column(name = "PARAM_RMK", length = 100)
	public String getParamRmk() {
		return this.paramRmk;
	}

	public void setParamRmk(String paramRmk) {
		this.paramRmk = paramRmk;
	}

	@Column(name = "IF_REVEAL" , precision = 1, scale = 0)
	public Short getIfReveal() {
		return this.ifReveal;
	}

	public void setIfReveal(Short ifReveal) {
		this.ifReveal = ifReveal;
	}

	@Column(name = "SPLSHT", length = 100)
	public String getSplsht() {
		return this.splsht;
	}

	public void setSplsht(String splsht) {
		this.splsht = splsht;
	}

	@Column(name = "CREATE_DATE" , length = 11)
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Column(name = "MODIFY_DATE", length = 11)
	@Temporal(TemporalType.TIMESTAMP)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

    @Enumerated
    @Column(name = "DATA_TYPE")
    public FieldType getFieldType() {
        return fieldType;
    }

    public void setFieldType(FieldType fieldType) {
        this.fieldType = fieldType;
    }

    @Enumerated
    @Column(name = "DICT_TYPE")
    public DictType getDictType() {
        return dictType;
    }

    public void setDictType(DictType dictType) {
        this.dictType = dictType;
    }

    @Column(name = "DICT_VALUE")
    public String getDictValue() {
        return dictValue;
    }

    public void setDictValue(String dictValue) {
        this.dictValue = dictValue;
    }

    @Column(name = "PARAM_TYPE")
	public Integer getParamType() {
		return paramType;
	}

	public void setParamType(Integer paramType) {
		this.paramType = paramType;
	}
}