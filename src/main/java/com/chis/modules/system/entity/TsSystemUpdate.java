package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.system.enumn.SystemType;

/**
 * TsSystemUpdate entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_SYSTEM_UPDATE")
@SequenceGenerator(name = "TsSystemUpdate", sequenceName = "TS_SYSTEM_UPDATE_SEQ",allocationSize=1)
public class TsSystemUpdate implements java.io.Serializable {

	// Fields

	private Integer rid;
	private Integer curVersion;
	private Date updateTime;
    private Integer paramType;

	public TsSystemUpdate() {
	}

	/** minimal constructor */
	public TsSystemUpdate(Integer rid) {
		this.rid = rid;
	}

	/** full constructor */
	public TsSystemUpdate(Integer rid, Integer curVersion, Date updateTime) {
		this.rid = rid;
		this.curVersion = curVersion;
		this.updateTime = updateTime;
	}

	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsSystemUpdate")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Column(name = "CUR_VERSION", precision = 22, scale = 0)
	public Integer getCurVersion() {
		return this.curVersion;
	}

	public void setCurVersion(Integer curVersion) {
		this.curVersion = curVersion;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "UPDATE_TIME", length = 7)
	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

    @Column(name = "PARAM_TYPE")
	public Integer getParamType() {
		return paramType;
	}

	public void setParamType(Integer paramType) {
		this.paramType = paramType;
	}

}