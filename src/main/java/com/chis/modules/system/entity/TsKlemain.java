package com.chis.modules.system.entity;

import javax.persistence.*;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2016-11-2
 */
@Entity
@Table(name = "TS_KLEMAIN")
@SequenceGenerator(name = "TsKlemain", sequenceName = "TS_KLEMAIN_SEQ", allocationSize = 1)
public class TsKlemain implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String kleName;
	private String kleKeys;
	private TsKletype kleTypeId;
	private Date fbDate;
	private Integer fbManid;
	private Integer state;
	private Date createDate;
	private Integer createManid;
	private List<TsKleanx> tsKleanx= new LinkedList<TsKleanx>();
	public TsKlemain() {
	}

	public TsKlemain(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsKlemain")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "KLE_NAME")	
	public String getKleName() {
		return kleName;
	}

	public void setKleName(String kleName) {
		this.kleName = kleName;
	}	
			
	@Column(name = "KLE_KEYS")	
	public String getKleKeys() {
		return kleKeys;
	}

	public void setKleKeys(String kleKeys) {
		this.kleKeys = kleKeys;
	}	
	
	
	@ManyToOne
	@JoinColumn(name = "KLE_TYPE_ID" )		
	public TsKletype getKleTypeId() {
		return kleTypeId;
	}

	public void setKleTypeId(TsKletype kleTypeId) {
		this.kleTypeId = kleTypeId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "FB_DATE")			
	public Date getFbDate() {
		return fbDate;
	}

	public void setFbDate(Date fbDate) {
		this.fbDate = fbDate;
	}	
			
	@Column(name = "FB_MANID")	
	public Integer getFbManid() {
		return fbManid;
	}

	public void setFbManid(Integer fbManid) {
		this.fbManid = fbManid;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "mainId",orphanRemoval=true)
	public List<TsKleanx> getTsKleanx() {
		return tsKleanx;
	}

	public void setTsKleanx(List<TsKleanx> tsKleanx) {
		this.tsKleanx = tsKleanx;
	}	
			
}