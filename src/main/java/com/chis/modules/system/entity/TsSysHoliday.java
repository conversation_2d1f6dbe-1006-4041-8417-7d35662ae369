package com.chis.modules.system.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-11-4
 */
@Entity
@Table(name = "TS_SYS_HOLIDAY")
@SequenceGenerator(name = "TsSysHoliday", sequenceName = "TS_SYS_HOLIDAY_SEQ", allocationSize = 1)
public class TsSysHoliday implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String holiDesc;
	private Integer holiType;
	private Date startDate;
	private Date endDate;
	private Integer state;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TsSysHoliday() {
	}

	public TsSysHoliday(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsSysHoliday")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "HOLI_DESC")
	public String getHoliDesc() {
		return holiDesc;
	}

	public void setHoliDesc(String holiDesc) {
		this.holiDesc = holiDesc;
	}	
			
	@Column(name = "HOLI_TYPE")	
	public Integer getHoliType() {
		return holiType;
	}

	public void setHoliType(Integer holiType) {
		this.holiType = holiType;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "START_DATE")			
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "END_DATE")			
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}