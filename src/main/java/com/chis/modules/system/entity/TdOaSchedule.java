package com.chis.modules.system.entity;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.chis.common.utils.StringUtils;

/**
 * 我的日程
 */
@Entity
@Table(name = "TD_OA_SCHEDULE")
@SequenceGenerator(name = "TdOaSchedule", sequenceName = "TD_OA_SCHEDULE_SEQ", allocationSize = 1)
public class TdOaSchedule implements java.io.Serializable {

	private static final long serialVersionUID = -7642663058198034576L;
	private Integer rid;
	private String scheduleTitle;
	private String scheduleTxt;
	private Date beginTime;
	private Date endTime;
	private Integer publicType;
	private String publicScope;
	private Integer scheduleType;
	private Integer isRemind;
	private Integer remindTime = 0;
	private Date remindDatetime;
	private String remindMtd;
	private TsUserInfo executeMan;
	private TsUserInfo arrangeMan;
	private String linkUrl;
	private Date createDate = new Date();
	private Integer createManid;
	//提醒方式集合
	private List<String> remindMtdList;
	//是否是周期性 SEASONAL
	private Integer seasonal = 0;
	private Short periodType = Short.valueOf("0");
	private Short dayOfMon;
	private Short dayOfWeek;
	private Short hourOfDay;
	private Short minOfDay;
	private String expressions;
	
	public TdOaSchedule() {
	}

	/** minimal constructor */
	public TdOaSchedule(Integer rid) {
		this.rid = rid;
	}

	public TdOaSchedule(Integer rid, String scheduleTitle, Date beginTime, Date endTime) {
		this.rid = rid;
		this.scheduleTitle = scheduleTitle;
		this.beginTime = beginTime;
		this.endTime = endTime;
	}
	
	public TdOaSchedule(Integer rid, String scheduleTitle, Date beginTime, Date endTime, Integer seasonal, String expressions) {
		this.rid = rid;
		this.scheduleTitle = scheduleTitle;
		this.beginTime = beginTime;
		this.endTime = endTime;
		this.seasonal = seasonal;
		this.expressions = expressions;
	}
	
	public TdOaSchedule(Integer rid, String scheduleTitle, Date beginTime, Date endTime, Integer seasonal, String expressions, Short periodType) {
		this.rid = rid;
		this.scheduleTitle = scheduleTitle;
		this.beginTime = beginTime;
		this.endTime = endTime;
		this.seasonal = seasonal;
		this.expressions = expressions;
		this.periodType = periodType;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdOaSchedule")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Column(name = "SCHEDULE_TITLE" , length = 200)
	public String getScheduleTitle() {
		return this.scheduleTitle;
	}

	public void setScheduleTitle(String scheduleTitle) {
		this.scheduleTitle = scheduleTitle;
	}

	@Lob
	@Column(name = "SCHEDULE_TXT" )
	public String getScheduleTxt() {
		return this.scheduleTxt;
	}

	public void setScheduleTxt(String scheduleTxt) {
		this.scheduleTxt = scheduleTxt;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "BEGIN_TIME")
	public Date getBeginTime() {
		return this.beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "END_TIME")
	public Date getEndTime() {
		return this.endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	@Column(name = "PUBLIC_TYPE" , precision = 1, scale = 0)
	public Integer getPublicType() {
		return this.publicType;
	}

	public void setPublicType(Integer publicType) {
		this.publicType = publicType;
	}

	@Column(name = "PUBLIC_SCOPE", length = 1000)
	public String getPublicScope() {
		return this.publicScope;
	}

	public void setPublicScope(String publicScope) {
		this.publicScope = publicScope;
	}
	
	@Column(name = "SCHEDULE_TYPE")
	public Integer getScheduleType() {
		return scheduleType;
	}

	public void setScheduleType(Integer scheduleType) {
		this.scheduleType = scheduleType;
	}

	@ManyToOne
	@JoinColumn(name = "EXECUTE_MAN_ID")
	public TsUserInfo getExecuteMan() {
		return executeMan;
	}

	public void setExecuteMan(TsUserInfo executeMan) {
		this.executeMan = executeMan;
	}

	@ManyToOne
	@JoinColumn(name = "ARRANGE_MAN_ID")
	public TsUserInfo getArrangeMan() {
		return arrangeMan;
	}

	public void setArrangeMan(TsUserInfo arrangeMan) {
		this.arrangeMan = arrangeMan;
	}

	@Column(name = "IS_REMIND" , precision = 1, scale = 0)
	public Integer getIsRemind() {
		return this.isRemind;
	}

	public void setIsRemind(Integer isRemind) {
		this.isRemind = isRemind;
	}

	@Column(name = "REMIND_TIME", precision = 22, scale = 0)
	public Integer getRemindTime() {
		return this.remindTime;
	}

	public void setRemindTime(Integer remindTime) {
		this.remindTime = remindTime;
	}

	@Column(name = "REMIND_MTD", length = 200)
	public String getRemindMtd() {
		return this.remindMtd;
	}

	public void setRemindMtd(String remindMtd) {
		this.remindMtd = remindMtd;
	}

	@Column(name = "LINK_URL", length = 500)
	public String getLinkUrl() {
		return this.linkUrl;
	}

	public void setLinkUrl(String linkUrl) {
		this.linkUrl = linkUrl;
	}

	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Transient
	public List<String> getRemindMtdList() {
		if(StringUtils.isNotBlank(this.remindMtd)) {
			this.remindMtdList = StringUtils.string2list(this.remindMtd, ",");
		}
		return remindMtdList;
	}

	public void setRemindMtdList(List<String> remindMtdList) {
		if(null != remindMtdList && remindMtdList.size() > 0) {
			this.remindMtd = StringUtils.list2string(remindMtdList, ",");
		}
		this.remindMtdList = remindMtdList;
	}

	@Column(name = "SEASONAL")
	public Integer getSeasonal() {
		return seasonal;
	}

	public void setSeasonal(Integer seasonal) {
		this.seasonal = seasonal;
	}

	@Column(name = "PERIOD_TYPE")
	public Short getPeriodType() {
		return periodType;
	}

	public void setPeriodType(Short periodType) {
		this.periodType = periodType;
	}

	@Column(name = "DAY_OF_MON")
	public Short getDayOfMon() {
		return dayOfMon;
	}

	public void setDayOfMon(Short dayOfMon) {
		this.dayOfMon = dayOfMon;
	}

	@Column(name = "DAY_OF_WEEK")
	public Short getDayOfWeek() {
		return dayOfWeek;
	}

	public void setDayOfWeek(Short dayOfWeek) {
		this.dayOfWeek = dayOfWeek;
	}

	@Column(name = "HOUR_OF_DAY")
	public Short getHourOfDay() {
		return hourOfDay;
	}

	public void setHourOfDay(Short hourOfDay) {
		this.hourOfDay = hourOfDay;
	}

	@Column(name = "MIN_OF_DAY")
	public Short getMinOfDay() {
		return minOfDay;
	}

	public void setMinOfDay(Short minOfDay) {
		this.minOfDay = minOfDay;
	}
	
	@Column(name = "EXPRESSIONS")
	public String getExpressions() {
		return expressions;
	}

	public void setExpressions(String expressions) {
		this.expressions = expressions;
	}

	@Column(name = "REMIND_DATETIME")
	public Date getRemindDatetime() {
		return remindDatetime;
	}

	public void setRemindDatetime(Date remindDatetime) {
		this.remindDatetime = remindDatetime;
	}
	
}