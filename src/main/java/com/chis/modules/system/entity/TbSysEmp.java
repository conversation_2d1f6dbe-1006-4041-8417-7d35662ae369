package com.chis.modules.system.entity;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Past;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * TbSysEmp entity. <AUTHOR> Persistence Tools
 *
 * @修改人 wlj
 * @修改时间 2014-10-17
 * @修改内容 增加兼职信息集合
 */
@Entity
@Table(name = "TB_SYS_EMP")
@SequenceGenerator(name = "TbSysEmp_seq", sequenceName = "TB_SYS_EMP_SEQ",allocationSize=1)
public class TbSysEmp implements java.io.Serializable {

    private static final long serialVersionUID = 5996399031238004488L;
    private Integer rid;
    private TsOffice tsOffice;
    private Short num;
    private String empCode;
    private String empName;
    private Short empSex = 1;
    private Integer empNation;
    private String idc;
    private Short workTypeid = 1;
    private Date birthday;
    private Short isLeader = 0;
    private String mbNum;
    private Integer duty;
    private Integer position;
    private Date workDay;
    private Date regularDay;
    private Integer politics;
    private String usedName;
    private Integer maritalStatus;
    private Integer religion;
    private Short onreg = 1;
    private Short onduty = 1;
    private TsSimpleCode psnProp;
    private String psnSign;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private Integer workYears;
    private Integer newchilInoc;
    private Integer proflevelid;
    private Set<TsOffice> tsOfficesForManageManid = new HashSet<TsOffice>(0);
    private Set<TsUserInfo> tsUserInfos = new HashSet<TsUserInfo>(0);
    private Set<TsOffice> tsOfficesForDeptLeaderId = new HashSet<TsOffice>(0);
    private List<TsParttimeInfo> tsParttimeInfoList = new ArrayList<TsParttimeInfo>(0);
    /** 参加工作时间 */
    private Date addWork;
    /** 第一学历 */
    private Integer firstEdu;
    /** 第一学位 */
    private Integer firstAcadedu;
    /** 第一学历专业 */
    private String firstProf;
    /** 第一学历学校 */
    private String firstSchl;
    /** 第一学历毕业时间 */
    private Date firstgrdTime;
    /** 最终学历 */
    private Integer eduDegree;
    /** 最终学位 */
    private Integer acadDegree;
    /** 最终专业 */
    private String lastProf;
    /** 最终学历学校 */
    private String lastSchl;
    /** +最终学历毕业时间 */
    private Date lastgrdTime;
    /** 学分卡号 */
    private String creditsCardno;
    /** 籍贯 */
    private String birthPlace;
    /** 住址 */
    private String address;
    
    public TbSysEmp() {
    }

    public TbSysEmp(Integer rid) {
        this.rid = rid;
    }

    /**
     * minimal constructor
     */
    public TbSysEmp(Integer rid, String empCode, String empName,
                    Short empSex, Integer empNation, Short isLeader,
                    Date createDate, Integer createManid) {
        this.rid = rid;
        this.empCode = empCode;
        this.empName = empName;
        this.empSex = empSex;
        this.empNation = empNation;
        this.isLeader = isLeader;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbSysEmp_seq")
    @Column(name = "RID", unique = true , precision = 22, scale = 0)
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "DEPT_ID")
    public TsOffice getTsOffice() {
        return this.tsOffice;
    }

    public void setTsOffice(TsOffice tsOffice) {
        this.tsOffice = tsOffice;
    }

    @ManyToOne
    @JoinColumn(name = "PSN_PROP")
    public TsSimpleCode getPsnProp() {
        return psnProp;
    }

    public void setPsnProp(TsSimpleCode psnProp) {
        this.psnProp = psnProp;
    }

    @Column(name = "NUM", precision = 4, scale = 0)
    public Short getNum() {
        return this.num;
    }

    public void setNum(Short num) {
        this.num = num;
    }

    @Column(name = "EMP_CODE" , length = 20)
    public String getEmpCode() {
        return this.empCode;
    }

    public void setEmpCode(String empCode) {
        this.empCode = empCode;
    }

    @Column(name = "EMP_NAME" , length = 50)
    public String getEmpName() {
        return this.empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    @Column(name = "EMP_SEX" , precision = 1, scale = 0)
    public Short getEmpSex() {
        return this.empSex;
    }

    public void setEmpSex(Short empSex) {
        this.empSex = empSex;
    }

    @Column(name = "EMP_NATION" , precision = 22, scale = 0)
    public Integer getEmpNation() {
        return this.empNation;
    }

    public void setEmpNation(Integer empNation) {
        this.empNation = empNation;
    }

    @Column(name = "IDC", length = 18)
    public String getIdc() {
        return this.idc;
    }

    public void setIdc(String idc) {
        this.idc = idc;
    }

    @Column(name = "WORK_TYPEID", precision = 1, scale = 0)
    public Short getWorkTypeid() {
        return this.workTypeid;
    }

    public void setWorkTypeid(Short workTypeid) {
        this.workTypeid = workTypeid;
    }

    @Past(message = "出生日期不能大于今天")
    @Temporal(TemporalType.DATE)
    @Column(name = "BIRTHDAY", length = 7)
    public Date getBirthday() {
        return this.birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    @Column(name = "IS_LEADER" , precision = 1, scale = 0)
    public Short getIsLeader() {
        return this.isLeader;
    }

    public void setIsLeader(Short isLeader) {
        this.isLeader = isLeader;
    }

    @Column(name = "MB_NUM", length = 13)
    public String getMbNum() {
        return this.mbNum;
    }

    public void setMbNum(String mbNum) {
        this.mbNum = mbNum;
    }

    @Column(name = "DUTY", precision = 22, scale = 0)
    public Integer getDuty() {
        return this.duty;
    }

    public void setDuty(Integer duty) {
        this.duty = duty;
    }

    @Column(name = "POSITION", precision = 22, scale = 0)
    public Integer getPosition() {
        return this.position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    @Column(name = "EDU_DEGREE", precision = 22, scale = 0)
    public Integer getEduDegree() {
        return this.eduDegree;
    }

    public void setEduDegree(Integer eduDegree) {
        this.eduDegree = eduDegree;
    }

    @Past(message = "入职日期不能大于今天")
    @Temporal(TemporalType.DATE)
    @Column(name = "WORK_DAY", length = 7)
    public Date getWorkDay() {
        return this.workDay;
    }

    public void setWorkDay(Date workDay) {
        this.workDay = workDay;
    }

    @Past(message = "转正日期不能大于今天")
    @Temporal(TemporalType.DATE)
    @Column(name = "REGULAR_DAY", length = 7)
    public Date getRegularDay() {
        return this.regularDay;
    }

    public void setRegularDay(Date regularDay) {
        this.regularDay = regularDay;
    }

    @Column(name = "POLITICS", precision = 22, scale = 0)
    public Integer getPolitics() {
        return this.politics;
    }

    public void setPolitics(Integer politics) {
        this.politics = politics;
    }

    @Column(name = "USED_NAME", length = 50)
    public String getUsedName() {
        return this.usedName;
    }

    public void setUsedName(String usedName) {
        this.usedName = usedName;
    }

    @Column(name = "MARITAL_STATUS", precision = 22, scale = 0)
    public Integer getMaritalStatus() {
        return this.maritalStatus;
    }

    public void setMaritalStatus(Integer maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    @Column(name = "RELIGION", precision = 22, scale = 0)
    public Integer getReligion() {
        return this.religion;
    }

    public void setReligion(Integer religion) {
        this.religion = religion;
    }

    @Column(name = "ONREG", precision = 1, scale = 0)
    public Short getOnreg() {
        return this.onreg;
    }

    public void setOnreg(Short onreg) {
        this.onreg = onreg;
    }

    @Column(name = "ONDUTY", precision = 1, scale = 0)
    public Short getOnduty() {
        return this.onduty;
    }

    public void setOnduty(Short onduty) {
        this.onduty = onduty;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" , length = 11)
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" , precision = 22, scale = 0)
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE", length = 11)
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID", precision = 22, scale = 0)
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbSysEmpByManageManid")
    public Set<TsOffice> getTsOfficesForManageManid() {
        return this.tsOfficesForManageManid;
    }

    public void setTsOfficesForManageManid(Set<TsOffice> tsOfficesForManageManid) {
        this.tsOfficesForManageManid = tsOfficesForManageManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbSysEmp")
    public Set<TsUserInfo> getTsUserInfos() {
        return this.tsUserInfos;
    }

    public void setTsUserInfos(Set<TsUserInfo> tsUserInfos) {
        this.tsUserInfos = tsUserInfos;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbSysEmpByDeptLeaderId")
    public Set<TsOffice> getTsOfficesForDeptLeaderId() {
        return this.tsOfficesForDeptLeaderId;
    }

    public void setTsOfficesForDeptLeaderId(
            Set<TsOffice> tsOfficesForDeptLeaderId) {
        this.tsOfficesForDeptLeaderId = tsOfficesForDeptLeaderId;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tbSysEmp", orphanRemoval = true)
    public List<TsParttimeInfo> getTsParttimeInfoList() {
        return tsParttimeInfoList;
    }

    public void setTsParttimeInfoList(List<TsParttimeInfo> tsParttimeInfoList) {
        this.tsParttimeInfoList = tsParttimeInfoList;
    }

    @Column(name = "PSN_SIGN", length = 500)
    public String getPsnSign() {
        return psnSign;
    }

    public void setPsnSign(String psnSign) {
        this.psnSign = psnSign;
    }

    @Column(name = "WORK_YEARS")
    public Integer getWorkYears() {
        return workYears;
    }

    public void setWorkYears(Integer workYears) {
        this.workYears = workYears;
    }

    @Column(name = "NEWCHIL_INOC")
    public Integer getNewchilInoc() {
        return newchilInoc;
    }

    public void setNewchilInoc(Integer newchilInoc) {
        this.newchilInoc = newchilInoc;
    }

    @Column(name = "PROF_LEVELID")
    public Integer getProflevelid() {
        return proflevelid;
    }

    public void setProflevelid(Integer proflevelid) {
        this.proflevelid = proflevelid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "ADDWORK")
	public Date getAddWork() {
		return addWork;
	}

	public void setAddWork(Date addWork) {
		this.addWork = addWork;
	}

	@Column(name = "FIRST_EDU")
	public Integer getFirstEdu() {
		return firstEdu;
	}

	public void setFirstEdu(Integer firstEdu) {
		this.firstEdu = firstEdu;
	}

	@Column(name = "FIRST_ACADEDU")
	public Integer getFirstAcadedu() {
		return firstAcadedu;
	}

	public void setFirstAcadedu(Integer firstAcadedu) {
		this.firstAcadedu = firstAcadedu;
	}

	@Column(name = "FIRST_PROF")
	public String getFirstProf() {
		return firstProf;
	}

	public void setFirstProf(String firstProf) {
		this.firstProf = firstProf;
	}

	@Column(name = "FIRST_SCHL")
	public String getFirstSchl() {
		return firstSchl;
	}

	public void setFirstSchl(String firstSchl) {
		this.firstSchl = firstSchl;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "FIRSTGRD_TIME")
	public Date getFirstgrdTime() {
		return firstgrdTime;
	}

	public void setFirstgrdTime(Date firstgrdTime) {
		this.firstgrdTime = firstgrdTime;
	}

	@Column(name = "ACAD_DEGREE")
	public Integer getAcadDegree() {
		return acadDegree;
	}

	public void setAcadDegree(Integer acadDegree) {
		this.acadDegree = acadDegree;
	}

	@Column(name = "LAST_PROF")
	public String getLastProf() {
		return lastProf;
	}

	public void setLastProf(String lastProf) {
		this.lastProf = lastProf;
	}

	@Column(name = "LAST_SCHL")
	public String getLastSchl() {
		return lastSchl;
	}

	public void setLastSchl(String lastSchl) {
		this.lastSchl = lastSchl;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LASTGRD_TIME")
	public Date getLastgrdTime() {
		return lastgrdTime;
	}

	public void setLastgrdTime(Date lastgrdTime) {
		this.lastgrdTime = lastgrdTime;
	}

	@Column(name = "CREDITS_CARDNO")
	public String getCreditsCardno() {
		return creditsCardno;
	}

	public void setCreditsCardno(String creditsCardno) {
		this.creditsCardno = creditsCardno;
	}

	@Column(name = "BIRTH_PLACE")
	public String getBirthPlace() {
		return birthPlace;
	}

	public void setBirthPlace(String birthPlace) {
		this.birthPlace = birthPlace;
	}

	@Column(name = "ADDRESS")
	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}
    
    
}
