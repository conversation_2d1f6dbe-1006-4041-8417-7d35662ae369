package com.chis.modules.system.entity;

import com.chis.modules.system.logic.Constants;

import org.hibernate.validator.constraints.NotBlank;

import javax.persistence.*;
import javax.validation.constraints.Pattern;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * TsUnit entity. <AUTHOR> Persistence Tools
 * 
 * <p>修订内容：添加临时字段tokenId</p>
 * @ClassReviser qrr,2018年4月21日,TsUnit
 * <p>修订内容：添加职业病诊断证明书备注</p>
 *
 * @ClassReviser mxp,2018-6-22,TsUnit
 */
@Entity
@Table(name = "TS_UNIT")
@SequenceGenerator(name = "TsUnit_seq", sequenceName = "TS_UNIT_SEQ", allocationSize = 1)
public class TsUnit implements java.io.Serializable {

	// Fields

	private Integer rid;
	private TsZone tsZone;
    private String unitCode;
	private String unitname;
	private String unitSimpname;
	private String unitzip;
	private String unitaddr;
	private String unitaera;
	private String linkMan;
	private String unittel;
	private String unitfax;
	private String unitemail;
	private Short ifReveal;
	private Date stopDate;
	private String splsht;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private Set<TsOffice> tsOffices = new HashSet<TsOffice>(0);
	private Set<TsUnitRole> tsUnitRoles = new HashSet<TsUnitRole>(0);
    private Set<TsUnitAttr> tsUnitAttrs = new HashSet<TsUnitAttr>(0);

    private String unitSortNames;
    
    private String regCode;
    /** 经度 */
    private String lng;
    /** 纬度 */ 
    private String lat;

	//新增字段
	private String mediLic;//医疗机构执业许可证号
	private String radLic;//放射诊疗许可证号
	private String orgFz;//法定代表人
	private String orgFzzw;//法定代表人职务
	private String orgTel;//法人手机号
	private String creditCode; //社会信用代码
	private String safeUnitname;//对应安全生产监督管理局20180418
	private String jdUnitname;  //对应鉴定机构20180419
	private String proveBak;
	/**职业病诊断鉴定书备注*/
	private String jdProveBak;
	private Integer ifComplete;//是否已完善
	private String tokenId;
	private Integer updateTag;//+上传省平台标记20190123
	private String errorMsg;//+错误信息20190123
	private Integer gpyVersion;//+高拍仪版本20190807
	private TsZone fkByManagedZoneId;
	private String ifSubOrg;
	
	private String zoneFullName;
	private String manageFullName;
	private String orgIcon;
	private String cmsNo;
	private String cnasNo;

	private String writeNo;
	private String trustWriteNo;
	private String proveWriteNo;
	private String redUnitName;
	//坐标
	private String cenCoordXaxis;
	private String cenCoordYaxis;
	private String tipCoordXaxis;
	private String tipCoordYaxis;
	private String writeNoRule;
	/**机构性质*/
	private TsSimpleCode unitTypeId;
	private Integer unitTypeRid;

	public TsUnit() {
	}
	
	public TsUnit(Integer rid) {
		this.rid = rid;
	}

    public TsUnit(Integer rid, String unitname) {
        this.rid = rid;
        this.unitname = unitname;
    }

    
    
    public TsUnit(Integer rid, String unitCode, String unitname) {
		this.rid = rid;
		this.unitCode = unitCode;
		this.unitname = unitname;
	}

	public TsUnit(Integer rid, TsZone tsZone, String unitname,
			String unitSimpname, Short ifReveal, Date createDate,
			Integer createManid) {
		this.rid = rid;
		this.tsZone = tsZone;
		this.unitname = unitname;
		this.unitSimpname = unitSimpname;
		this.ifReveal = ifReveal;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	/** full constructor */
	public TsUnit(Integer rid, TsZone tsZone, String unitname,
			String unitSimpname, String unitzip, String unitaddr,
			String unitaera, String unittel, String unitfax, String unitemail,
            Short ifReveal, Date stopDate, String splsht,
			Date createDate, Integer createManid, Date modifyDate,
			Integer modifyManid, Set<TsOffice> tsOffices,
			Set<TsUnitRole> tsUnitRoles) {
		this.rid = rid;
		this.tsZone = tsZone;
		this.unitname = unitname;
		this.unitSimpname = unitSimpname;
		this.unitzip = unitzip;
		this.unitaddr = unitaddr;
		this.unitaera = unitaera;
		this.unittel = unittel;
		this.unitfax = unitfax;
		this.unitemail = unitemail;
		this.ifReveal = ifReveal;
		this.stopDate = stopDate;
		this.splsht = splsht;
		this.createDate = createDate;
		this.createManid = createManid;
		this.modifyDate = modifyDate;
		this.modifyManid = modifyManid;
		this.tsOffices = tsOffices;
		this.tsUnitRoles = tsUnitRoles;
	}

	public TsUnit(Integer rid, TsZone tsZone, String unitCode, String unitname, String unitSimpname,
				  String unitzip, String unitaddr, String unitaera, String linkMan, String unittel,
				  String unitfax, String unitemail, Short ifReveal, Date stopDate, String splsht,
				  Date createDate, Integer createManid, Date modifyDate, Integer modifyManid,
				  Set<TsOffice> tsOffices, Set<TsUnitRole> tsUnitRoles, Set<TsUnitAttr> tsUnitAttrs,
				  String unitSortNames, String regCode, String lng, String lat, String mediLic,
				  String radLic, String orgFz, String orgFzzw, String orgTel) {
		this.rid = rid;
		this.tsZone = tsZone;
		this.unitCode = unitCode;
		this.unitname = unitname;
		this.unitSimpname = unitSimpname;
		this.unitzip = unitzip;
		this.unitaddr = unitaddr;
		this.unitaera = unitaera;
		this.linkMan = linkMan;
		this.unittel = unittel;
		this.unitfax = unitfax;
		this.unitemail = unitemail;
		this.ifReveal = ifReveal;
		this.stopDate = stopDate;
		this.splsht = splsht;
		this.createDate = createDate;
		this.createManid = createManid;
		this.modifyDate = modifyDate;
		this.modifyManid = modifyManid;
		this.tsOffices = tsOffices;
		this.tsUnitRoles = tsUnitRoles;
		this.tsUnitAttrs = tsUnitAttrs;
		this.unitSortNames = unitSortNames;
		this.regCode = regCode;
		this.lng = lng;
		this.lat = lat;
		this.mediLic = mediLic;
		this.radLic = radLic;
		this.orgFz = orgFz;
		this.orgFzzw = orgFzzw;
		this.orgTel = orgTel;
	}

	@Column(name = "PROVE_BAK")
	public String getProveBak() {
		return proveBak;
	}

	public void setProveBak(String proveBak) {
		this.proveBak = proveBak;
	}

	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsUnit_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

    @Column(name = "UNIT_CODE" , length = 50)
    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    @ManyToOne
	@JoinColumn(name = "ZONE_ID" )
	public TsZone getTsZone() {
		return this.tsZone;
	}

	public void setTsZone(TsZone tsZone) {
		this.tsZone = tsZone;
	}
	@Column(name = "LINK_MAN", length = 100)
    public String getLinkMan() {
		return linkMan;
	}

	public void setLinkMan(String linkMan) {
		this.linkMan = linkMan;
	}

	@Column(name = "UNITNAME" , length = 100)
	public String getUnitname() {
		return this.unitname;
	}

	public void setUnitname(String unitname) {
		this.unitname = unitname;
	}

	@Column(name = "UNIT_SIMPNAME" , length = 100)
	public String getUnitSimpname() {
		return this.unitSimpname;
	}

	public void setUnitSimpname(String unitSimpname) {
		this.unitSimpname = unitSimpname;
	}

    @Pattern(regexp = "^\\s*$|[0-9]\\d{5}(?!\\d)", message = "请输入正确的邮编！")
	@Column(name = "UNITZIP", length = 6)
	public String getUnitzip() {
		return this.unitzip;
	}

	public void setUnitzip(String unitzip) {
		this.unitzip = unitzip;
	}

	@Column(name = "UNITADDR", length = 100)
	public String getUnitaddr() {
		return this.unitaddr;
	}

	public void setUnitaddr(String unitaddr) {
		this.unitaddr = unitaddr;
	}

	@Column(name = "UNITAERA", length = 4)
	public String getUnitaera() {
		return this.unitaera;
	}

	public void setUnitaera(String unitaera) {
		this.unitaera = unitaera;
	}

/**
 * 供应商管理维护单位的联系电话不能为固定电话，所有取消验证
 * qrr
 * */
//    @Pattern(regexp = Constants.PHONE, message = "请输入正确的联系电话！")
	@Column(name = "UNITTEL", length = 20)
	public String getUnittel() {
		return this.unittel;
	}

	public void setUnittel(String unittel) {
		this.unittel = unittel;
	}

//    @Pattern(regexp = Constants.PHONE, message = "请输入正确的单位传真！")
	@Column(name = "UNITFAX", length = 20)
	public String getUnitfax() {
		return this.unitfax;
	}

	public void setUnitfax(String unitfax) {
		this.unitfax = unitfax;
	}

    @Pattern(regexp = Constants.EMAIL, message = "请输入正确的邮箱地址！")
	@Column(name = "UNITEMAIL", length = 60)
	public String getUnitemail() {
		return this.unitemail;
	}

	public void setUnitemail(String unitemail) {
		this.unitemail = unitemail;
	}

	@Column(name = "IF_REVEAL" , precision = 1, scale = 0)
	public Short getIfReveal() {
		return this.ifReveal;
	}

	public void setIfReveal(Short ifReveal) {
		this.ifReveal = ifReveal;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "STOP_DATE", length = 7)
	public Date getStopDate() {
		return this.stopDate;
	}

	public void setStopDate(Date stopDate) {
		this.stopDate = stopDate;
	}

	@Column(name = "SPLSHT", length = 100)
	public String getSplsht() {
		return this.splsht;
	}

	public void setSplsht(String splsht) {
		this.splsht = splsht;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsUnit")
	public Set<TsOffice> getTsOffices() {
		return this.tsOffices;
	}

	public void setTsOffices(Set<TsOffice> tsOffices) {
		this.tsOffices = tsOffices;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsUnit")
	public Set<TsUnitRole> getTsUnitRoles() {
		return this.tsUnitRoles;
	}

	public void setTsUnitRoles(Set<TsUnitRole> tsUnitRoles) {
		this.tsUnitRoles = tsUnitRoles;
	}

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsUnit")
    public Set<TsUnitAttr> getTsUnitAttrs() {
        return tsUnitAttrs;
    }

    public void setTsUnitAttrs(Set<TsUnitAttr> tsUnitAttrs) {
        this.tsUnitAttrs = tsUnitAttrs;
    }
    
    @Column(name = "REG_CODE", length = 50)
    public String getRegCode() {
		return regCode;
	}

	public void setRegCode(String regCode) {
		this.regCode = regCode;
	}

	@Transient
    public String getUnitSortNames() {
        return unitSortNames;
    }

    public void setUnitSortNames(String unitSortNames) {
        this.unitSortNames = unitSortNames;
    }

    @Column(name = "LNG")
	public String getLng() {
		return lng;
	}

	public void setLng(String lng) {
		this.lng = lng;
	}

	@Column(name = "LAT")
	public String getLat() {
		return lat;
	}

	public void setLat(String lat) {
		this.lat = lat;
	}

	@Column(name = "MEDI_LIC", length = 200)
	public String getMediLic() {
		return mediLic;
	}

	public void setMediLic(String mediLic) {
		this.mediLic = mediLic;
	}

	@Column(name = "RAD_LIC", length = 200)
	public String getRadLic() {
		return radLic;
	}

	public void setRadLic(String radLic) {
		this.radLic = radLic;
	}
	@Column(name = "ORG_FZ", length = 50)
	public String getOrgFz() {
		return orgFz;
	}

	public void setOrgFz(String orgFz) {
		this.orgFz = orgFz;
	}
	@Column(name = "ORG_FZZW", length = 50)
	public String getOrgFzzw() {
		return orgFzzw;
	}

	public void setOrgFzzw(String orgFzzw) {
		this.orgFzzw = orgFzzw;
	}

	@Column(name = "ORG_TEL", length = 50)
	public String getOrgTel() {
		return orgTel;
	}

	public void setOrgTel(String orgTel) {
		this.orgTel = orgTel;
	}
	
//	@NotBlank(message = "社会信用代码不允许为空！")
	@Column(name = "CREDIT_CODE", length = 50)
	public String getCreditCode() {
		return creditCode;
	}

	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}
	@Column(name = "SAFE_UNITNAME", length = 200)
	public String getSafeUnitname() {
		return safeUnitname;
	}

	public void setSafeUnitname(String safeUnitname) {
		this.safeUnitname = safeUnitname;
	}
	
	@Column(name = "JD_UNITNAME", length = 200)
	public String getJdUnitname() {
		return jdUnitname;
	}

	public void setJdUnitname(String jdUnitname) {
		this.jdUnitname = jdUnitname;
	}
	@Transient
	public String getTokenId() {
		return tokenId;
	}

	public void setTokenId(String tokenId) {
		this.tokenId = tokenId;
	}
	@Column(name = "IF_COMPLETE")
	public Integer getIfComplete() {
		return ifComplete;
	}

	public void setIfComplete(Integer ifComplete) {
		this.ifComplete = ifComplete;
	}

	@Column(name = "UPDATETAG")
	public Integer getUpdateTag() {
		return updateTag;
	}

	public void setUpdateTag(Integer updateTag) {
		this.updateTag = updateTag;
	}

	@Column(name = "ERROR_MSG")
	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}
	
	@Column(name = "GPY_VERSION")
	public Integer getGpyVersion() {
		return gpyVersion;
	}

	public void setGpyVersion(Integer gpyVersion) {
		this.gpyVersion = gpyVersion;
	}
	@ManyToOne
	@JoinColumn(name = "MANAGE_ZONE_ID")
	public TsZone getFkByManagedZoneId() {
		return fkByManagedZoneId;
	}

	public void setFkByManagedZoneId(TsZone fkByManagedZoneId) {
		this.fkByManagedZoneId = fkByManagedZoneId;
	}
	
	@Transient
	public String getZoneFullName() {
		return zoneFullName;
	}

	public void setZoneFullName(String zoneFullName) {
		this.zoneFullName = zoneFullName;
	}

	@Transient
	public String getManageFullName() {
		return manageFullName;
	}

	public void setManageFullName(String manageFullName) {
		this.manageFullName = manageFullName;
	}
	@Column(name = "IF_SUB_ORG")
	public String getIfSubOrg() {
		return ifSubOrg;
	}

	public void setIfSubOrg(String ifSubOrg) {
		this.ifSubOrg = ifSubOrg;
	}

	@Column(name = "ORG_ICON")
	public String getOrgIcon( ) {
		return orgIcon;
	}
	public void setOrgIcon(String orgIcon) {
		this.orgIcon = orgIcon;
	}

	@Column(name = "CMA_NO")
	public String getCmsNo() {
		return cmsNo;
	}

	public void setCmsNo(String cmsNo) {
		this.cmsNo = cmsNo;
	}

	@Column(name = "CNAS_NO")
	public String getCnasNo() {
		return cnasNo;
	}

	public void setCnasNo(String cnasNo) {
		this.cnasNo = cnasNo;
	}

	@Column(name = "WRITE_NO")
	public String getWriteNo() {
		return writeNo;
	}

	public void setWriteNo(String writeNo) {
		this.writeNo = writeNo;
	}

	@Column(name = "TRUST_WRITE_NO")
	public String getTrustWriteNo() {
		return trustWriteNo;
	}

	public void setTrustWriteNo(String trustWriteNo) {
		this.trustWriteNo = trustWriteNo;
	}
	@Column(name = "PROVE_WRITE_NO")
	public String getProveWriteNo() {
		return proveWriteNo;
	}

	public void setProveWriteNo(String proveWriteNo) {
		this.proveWriteNo = proveWriteNo;
	}
	@Column(name = "RED_UNIT_NAME")
	public String getRedUnitName() {
		return redUnitName;
	}

	public void setRedUnitName(String redUnitName) {
		this.redUnitName = redUnitName;
	}
	@Column(name = "CEN_COORD_XAXIS")
	public String getCenCoordXaxis() {
		return cenCoordXaxis;
	}

	public void setCenCoordXaxis(String cenCoordXaxis) {
		this.cenCoordXaxis = cenCoordXaxis;
	}

	@Column(name = "CEN_COORD_YAXIS")
	public String getCenCoordYaxis() {
		return cenCoordYaxis;
	}

	public void setCenCoordYaxis(String cenCoordYaxis) {
		this.cenCoordYaxis = cenCoordYaxis;
	}

	@Column(name = "TIP_COORD_XAXIS")
	public String getTipCoordXaxis() {
		return tipCoordXaxis;
	}

	public void setTipCoordXaxis(String tipCoordXaxis) {
		this.tipCoordXaxis = tipCoordXaxis;
	}

	@Column(name = "TIP_COORD_YAXIS")
	public String getTipCoordYaxis() {
		return tipCoordYaxis;
	}

	public void setTipCoordYaxis(String tipCoordYaxis) {
		this.tipCoordYaxis = tipCoordYaxis;
	}

	@Column(name = "WRITE_NO_RULE")
	public String getWriteNoRule() {
		return writeNoRule;
	}

	public void setWriteNoRule(String writeNoRule) {
		this.writeNoRule = writeNoRule;
	}

	@Column(name = "JD_PROVE_BAK")
	public String getJdProveBak() {
		return jdProveBak;
	}
	public void setJdProveBak(String jdProveBak) {
		this.jdProveBak = jdProveBak;
	}
	@ManyToOne
	@JoinColumn(name = "UNIT_TYPE_ID")
	public TsSimpleCode getUnitTypeId() {
		return unitTypeId;
	}

	public void setUnitTypeId(TsSimpleCode unitTypeId) {
		this.unitTypeId = unitTypeId;
	}
	@Transient
	public Integer getUnitTypeRid() {
		return unitTypeRid;
	}

	public void setUnitTypeRid(Integer unitTypeRid) {
		this.unitTypeRid = unitTypeRid;
	}
}