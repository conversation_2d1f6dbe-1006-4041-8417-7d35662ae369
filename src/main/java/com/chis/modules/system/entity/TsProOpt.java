package com.chis.modules.system.entity;

import javax.persistence.*;

import java.util.Date;

/**
 * 题目选项[通用]
 * 
 * <AUTHOR>
 * @createTime 2016-3-24
 */
@Entity
@Table(name = "TS_PRO_OPT")
@SequenceGenerator(name = "TsProOpt", sequenceName = "TS_PRO_OPT_SEQ", allocationSize = 1)
public class TsProOpt implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	/** RID */
	private Integer rid;
	/** 题目ID */
	private TsProbSubject tsProbSubjectByQuestId;
	/** 序号 */
	private Integer num;
	/** 选项描述 */
	private String optionDesc;
	/** 选项图片 */
	private String optionImg;
	/** 选项值 */
	private String optionValue;
	/** ×分值 */
	private Integer optionScore;
	/** 是否需要填空 */
	private Integer needFill;
	/** 备注 */
	private String rmk;
	/** 状态0：停用1：启用 */
	private Integer state;
	/** 附加文字 */
	private String otherDesc;
	/** 附加图片 */
	private String otherImg;
	/** 跳转类型0：正常1：直接显示子题目2：选了之后显示子题目3：跳转到哪个题目 */
	private Integer jumpType;
	/** 跳转至哪题 */
	private String jumpQuestCode;
	/** 创建日期 */
	private Date createDate;
	/** 创建人 */
	private Integer createManid;
	/**是否互斥*/
	private Integer isAlter;
	/**是否正确答案*/
	private Integer isCorrect;
	/**+填空是否多项*/
	private Integer isMulti;
	public TsProOpt() {
	}

	public TsProOpt(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsProOpt")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "QUEST_ID")
	public TsProbSubject getTsProbSubjectByQuestId() {
		return tsProbSubjectByQuestId;
	}

	public void setTsProbSubjectByQuestId(TsProbSubject tsProbSubjectByQuestId) {
		this.tsProbSubjectByQuestId = tsProbSubjectByQuestId;
	}

	@Column(name = "NUM")
	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	@Column(name = "OPTION_DESC")
	public String getOptionDesc() {
		return optionDesc;
	}

	public void setOptionDesc(String optionDesc) {
		this.optionDesc = optionDesc;
	}

	@Column(name = "OPTION_IMG")
	public String getOptionImg() {
		return optionImg;
	}

	public void setOptionImg(String optionImg) {
		this.optionImg = optionImg;
	}

	@Column(name = "OPTION_VALUE")
	public String getOptionValue() {
		return optionValue;
	}

	public void setOptionValue(String optionValue) {
		this.optionValue = optionValue;
	}

	@Column(name = "OPTION_SCORE")
	public Integer getOptionScore() {
		return optionScore;
	}

	public void setOptionScore(Integer optionScore) {
		this.optionScore = optionScore;
	}

	@Column(name = "NEED_FILL")
	public Integer getNeedFill() {
		return needFill;
	}

	public void setNeedFill(Integer needFill) {
		this.needFill = needFill;
	}

	@Column(name = "RMK")
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

	@Column(name = "STATE")
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	@Column(name = "OTHER_DESC")
	public String getOtherDesc() {
		return otherDesc;
	}

	public void setOtherDesc(String otherDesc) {
		this.otherDesc = otherDesc;
	}

	@Column(name = "OTHER_IMG")
	public String getOtherImg() {
		return otherImg;
	}

	public void setOtherImg(String otherImg) {
		this.otherImg = otherImg;
	}

	@Column(name = "JUMP_TYPE")
	public Integer getJumpType() {
		return jumpType;
	}

	public void setJumpType(Integer jumpType) {
		this.jumpType = jumpType;
	}

	@Column(name = "JUMP_QUEST_CODE")
	public String getJumpQuestCode() {
		return jumpQuestCode;
	}

	public void setJumpQuestCode(String jumpQuestCode) {
		this.jumpQuestCode = jumpQuestCode;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE")
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID")
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}
	
	
	@Column(name = "IS_ALTER")
	public Integer getIsAlter() {
		return isAlter;
	}

	public void setIsAlter(Integer isAlter) {
		this.isAlter = isAlter;
	}
	
	@Column(name = "IS_CORRECT")
	public Integer getIsCorrect() {
		return isCorrect;
	}

	public void setIsCorrect(Integer isCorrect) {
		this.isCorrect = isCorrect;
	}
	
	@Column(name = "IS_MULTI")
	public Integer getIsMulti() {
		return isMulti;
	}

	public void setIsMulti(Integer isMulti) {
		this.isMulti = isMulti;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((createDate == null) ? 0 : createDate.hashCode());
		result = prime * result
				+ ((createManid == null) ? 0 : createManid.hashCode());
		result = prime * result + ((isAlter == null) ? 0 : isAlter.hashCode());
		result = prime * result
				+ ((isCorrect == null) ? 0 : isCorrect.hashCode());
		result = prime * result
				+ ((jumpQuestCode == null) ? 0 : jumpQuestCode.hashCode());
		result = prime * result
				+ ((jumpType == null) ? 0 : jumpType.hashCode());
		result = prime * result
				+ ((needFill == null) ? 0 : needFill.hashCode());
		result = prime * result + ((num == null) ? 0 : num.hashCode());
		result = prime * result
				+ ((optionDesc == null) ? 0 : optionDesc.hashCode());
		result = prime * result
				+ ((optionImg == null) ? 0 : optionImg.hashCode());
		result = prime * result
				+ ((optionScore == null) ? 0 : optionScore.hashCode());
		result = prime * result
				+ ((optionValue == null) ? 0 : optionValue.hashCode());
		result = prime * result
				+ ((otherDesc == null) ? 0 : otherDesc.hashCode());
		result = prime * result
				+ ((otherImg == null) ? 0 : otherImg.hashCode());
		result = prime * result + ((rid == null) ? 0 : rid.hashCode());
		result = prime * result + ((rmk == null) ? 0 : rmk.hashCode());
		result = prime * result + ((state == null) ? 0 : state.hashCode());
		result = prime
				* result
				+ ((tsProbSubjectByQuestId == null) ? 0
						: tsProbSubjectByQuestId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		TsProOpt other = (TsProOpt) obj;
		if (createDate == null) {
			if (other.createDate != null)
				return false;
		} else if (!createDate.equals(other.createDate))
			return false;
		if (createManid == null) {
			if (other.createManid != null)
				return false;
		} else if (!createManid.equals(other.createManid))
			return false;
		if (isAlter == null) {
			if (other.isAlter != null)
				return false;
		} else if (!isAlter.equals(other.isAlter))
			return false;
		if (isCorrect == null) {
			if (other.isCorrect != null)
				return false;
		} else if (!isCorrect.equals(other.isCorrect))
			return false;
		if (jumpQuestCode == null) {
			if (other.jumpQuestCode != null)
				return false;
		} else if (!jumpQuestCode.equals(other.jumpQuestCode))
			return false;
		if (jumpType == null) {
			if (other.jumpType != null)
				return false;
		} else if (!jumpType.equals(other.jumpType))
			return false;
		if (needFill == null) {
			if (other.needFill != null)
				return false;
		} else if (!needFill.equals(other.needFill))
			return false;
		if (num == null) {
			if (other.num != null)
				return false;
		} else if (!num.equals(other.num))
			return false;
		if (optionDesc == null) {
			if (other.optionDesc != null)
				return false;
		} else if (!optionDesc.equals(other.optionDesc))
			return false;
		if (optionImg == null) {
			if (other.optionImg != null)
				return false;
		} else if (!optionImg.equals(other.optionImg))
			return false;
		if (optionScore == null) {
			if (other.optionScore != null)
				return false;
		} else if (!optionScore.equals(other.optionScore))
			return false;
		if (optionValue == null) {
			if (other.optionValue != null)
				return false;
		} else if (!optionValue.equals(other.optionValue))
			return false;
		if (otherDesc == null) {
			if (other.otherDesc != null)
				return false;
		} else if (!otherDesc.equals(other.otherDesc))
			return false;
		if (otherImg == null) {
			if (other.otherImg != null)
				return false;
		} else if (!otherImg.equals(other.otherImg))
			return false;
		if (rid == null) {
			if (other.rid != null)
				return false;
		} else if (!rid.equals(other.rid))
			return false;
		if (rmk == null) {
			if (other.rmk != null)
				return false;
		} else if (!rmk.equals(other.rmk))
			return false;
		if (state == null) {
			if (other.state != null)
				return false;
		} else if (!state.equals(other.state))
			return false;
		if (tsProbSubjectByQuestId == null) {
			if (other.tsProbSubjectByQuestId != null)
				return false;
		} else if (!tsProbSubjectByQuestId.equals(other.tsProbSubjectByQuestId))
			return false;
		return true;
	}


}