package com.chis.modules.system.entity;

import javax.persistence.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 * @createTime 2015-12-3
 */
@Entity
@Table(name = "TD_FORM_DEF")
@SequenceGenerator(name = "TdFormDef", sequenceName = "TD_FORM_DEF_SEQ", allocationSize = 1)
@NamedQueries({ @NamedQuery(name = "TdFormDef.findAll", query = "select t from TdFormDef t where t.state = 1 order by t.formCode") })
public class TdFormDef implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdFormType tdFormTypeByTypeId;
	private String formCode;
	private TdFormTable tdFormTableByTableId;
	private String formName;
	private Integer state;
	private Date createDate;
	private Integer createManid;
	/**fastReport编码*/
	private String prtTplCode;
	private String html;
	private String searchHtml;
	private String searchSql;
	private Map<String, Object> valueMap = new HashMap<String, Object>();
	private int rows = 0;
	private Map<String, List<TsSimpleCode>> codeMap = new HashMap<String, List<TsSimpleCode>>();
	
	public TdFormDef() {
	}

	public TdFormDef(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFormDef")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "TYPE_ID")			
	public TdFormType getTdFormTypeByTypeId() {
		return tdFormTypeByTypeId;
	}

	public void setTdFormTypeByTypeId(TdFormType tdFormTypeByTypeId) {
		this.tdFormTypeByTypeId = tdFormTypeByTypeId;
	}	
			
	@Column(name = "FORM_CODE")	
	public String getFormCode() {
		return formCode;
	}

	public void setFormCode(String formCode) {
		this.formCode = formCode;
	}	
			
	@ManyToOne
	@JoinColumn(name = "TABLE_ID")			
	public TdFormTable getTdFormTableByTableId() {
		return tdFormTableByTableId;
	}

	public void setTdFormTableByTableId(TdFormTable tdFormTableByTableId) {
		this.tdFormTableByTableId = tdFormTableByTableId;
	}	
			
	@Column(name = "FORM_NAME")	
	public String getFormName() {
		return formName;
	}

	public void setFormName(String formName) {
		this.formName = formName;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
	
	@Column(name = "PRT_TPL_CODE")
	public String getPrtTplCode() {
		return prtTplCode;
	}

	public void setPrtTplCode(String prtTplCode) {
		this.prtTplCode = prtTplCode;
	}
	@Column(name = "HTML")
	public String getHtml() {
		return html;
	}

	public void setHtml(String html) {
		this.html = html;
	}
	
	
	@Column(name = "SEARCH_HTML")
	public String getSearchHtml() {
		return searchHtml;
	}

	public void setSearchHtml(String searchHtml) {
		this.searchHtml = searchHtml;
	}

	@Column(name = "SEARCH_SQL")
	public String getSearchSql() {
		return searchSql;
	}

	public void setSearchSql(String searchSql) {
		this.searchSql = searchSql;
	}

	@Transient
	public Map<String, Object> getValueMap() {
		return valueMap;
	}

	public void setValueMap(Map<String, Object> valueMap) {
		this.valueMap = valueMap;
	}

	@Transient
	public Map<String, List<TsSimpleCode>> getCodeMap() {
		return codeMap;
	}

	public void setCodeMap(Map<String, List<TsSimpleCode>> codeMap) {
		this.codeMap = codeMap;
	}

	@Transient
	public int getRows() {
		return rows;
	}

	public void setRows(int rows) {
		this.rows = rows;
	}
	
			
}