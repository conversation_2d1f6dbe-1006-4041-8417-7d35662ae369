package com.chis.modules.system.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * TsUserMenu entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_USER_MENU")
@SequenceGenerator(name = "TsUserMenu_seq", sequenceName = "TS_USER_MENU_SEQ", allocationSize = 1)
public class TsUserMenu implements java.io.Serializable {

	private static final long serialVersionUID = 5230476658608286490L;
	private Integer rid;
	private TsUserInfo tsUserInfo;
	private TsMenu tsMenu;

	public TsUserMenu() {
	}

	public TsUserMenu(Integer rid, TsUserInfo tsUserInfo, TsMenu tsMenu) {
		this.rid = rid;
		this.tsUserInfo = tsUserInfo;
		this.tsMenu = tsMenu;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsUserMenu_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "USER_INFO_ID" )
	public TsUserInfo getTsUserInfo() {
		return this.tsUserInfo;
	}

	public void setTsUserInfo(TsUserInfo tsUserInfo) {
		this.tsUserInfo = tsUserInfo;
	}

	@ManyToOne
	@JoinColumn(name = "MENU_TEMPLATE_ID" )
	public TsMenu getTsMenu() {
		return this.tsMenu;
	}

	public void setTsMenu(TsMenu tsMenu) {
		this.tsMenu = tsMenu;
	}

}