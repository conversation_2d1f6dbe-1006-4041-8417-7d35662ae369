package com.chis.modules.system.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.enumn.SystemType;

/**
 * 问卷库[通用]
 * 
 * <AUTHOR>
 * @createTime 2016-3-24
 */
@Entity
@Table(name = "TS_PROB_LIB")
@SequenceGenerator(name = "TsProbLib", sequenceName = "TS_PROB_LIB_SEQ", allocationSize = 1)
public class TsProbLib implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	/** RID */
	private Integer rid;
	/** 单位ID */
	private TsUnit tsUnitByUnitId;
	/** 所属类别 */
	private TsSimpleCode tsSimpleCodeByQuestSortid;
	/** 问卷名称 */
	private String questName;
	/** 问卷描述 */
	private String rmk;
	/** 序号 */
	private Integer num;
	/** 背景图 */
	private String backImage;
	/** 状态0：停用1：启用 */
	private Integer state;
	/** 问卷HTML名称移动端直接加Mobile */
	private String htmlName;
	/** 创建日期 */
	private Date createDate;
	/** 创建人 */
	private Integer createManid;
	/** ++题目集合 */
	private List<TsProbSubject> subjectList = new ArrayList<TsProbSubject>();
	/** 题库脚本 */
	private String libScript;
	/** +验证脚本 */
	private String verifyScript;
	/** 初始化脚本 */
	private String libInitSrc;

	private SystemType systemType;

	private String paramTypeName;

	public TsProbLib() {
	}

	public TsProbLib(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsProbLib")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "UNIT_ID")
	public TsUnit getTsUnitByUnitId() {
		return tsUnitByUnitId;
	}

	public void setTsUnitByUnitId(TsUnit tsUnitByUnitId) {
		this.tsUnitByUnitId = tsUnitByUnitId;
	}

	@ManyToOne
	@JoinColumn(name = "QUEST_SORTID")
	public TsSimpleCode getTsSimpleCodeByQuestSortid() {
		return tsSimpleCodeByQuestSortid;
	}

	public void setTsSimpleCodeByQuestSortid(TsSimpleCode tsSimpleCodeByQuestSortid) {
		this.tsSimpleCodeByQuestSortid = tsSimpleCodeByQuestSortid;
	}

	@Column(name = "QUEST_NAME")
	public String getQuestName() {
		return questName;
	}

	public void setQuestName(String questName) {
		this.questName = questName;
	}

	@Column(name = "RMK")
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

	@Column(name = "NUM")
	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	@Column(name = "BACK_IMAGE")
	public String getBackImage() {
		return backImage;
	}

	public void setBackImage(String backImage) {
		this.backImage = backImage;
	}

	@Column(name = "STATE")
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	@Column(name = "HTML_NAME")
	public String getHtmlName() {
		return htmlName;
	}

	public void setHtmlName(String htmlName) {
		this.htmlName = htmlName;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE")
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID")
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsProbLibByQuestlibId", orphanRemoval = true)
	@OrderBy(value = "num")
	public List<TsProbSubject> getSubjectList() {
		return subjectList;
	}

	public void setSubjectList(List<TsProbSubject> subjectList) {
		this.subjectList = subjectList;
	}

	@Column(name = "LIB_SCRIPT")
	public String getLibScript() {
		return libScript;
	}

	public void setLibScript(String libScript) {
		this.libScript = libScript;
	}

	@Column(name = "LIB_INIT_SRC")
	public String getLibInitSrc() {
		return libInitSrc;
	}

	@Column(name = "VERIFY_SCRIPT")
	public String getVerifyScript() {
		return verifyScript;
	}

	public void setVerifyScript(String verifyScript) {
		this.verifyScript = verifyScript;
	}

	public void setLibInitSrc(String libInitSrc) {
		this.libInitSrc = libInitSrc;
	}

	@Enumerated
	@Column(name = "PARAM_TYPE")
	public SystemType getSystemType() {
		return systemType;
	}

	public void setSystemType(SystemType systemType) {
		this.systemType = systemType;
	}

	@Transient
	public String getParamTypeName() {
		return paramTypeName;
	}

	public void setParamTypeName(String paramTypeName) {
		this.paramTypeName = paramTypeName;
	}

}