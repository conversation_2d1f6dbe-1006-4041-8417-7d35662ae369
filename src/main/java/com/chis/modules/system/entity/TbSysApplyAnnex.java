package com.chis.modules.system.entity;

import com.chis.modules.system.entity.TbSysApplyAccount;

import javax.persistence.*;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @createTime 2020-8-6
 */
@Entity
@Table(name = "TB_SYS_APPLY_ANNEX")
@SequenceGenerator(name = "TbSysApplyAnnex", sequenceName = "TB_SYS_APPLY_ANNEX_SEQ", allocationSize = 1)
public class TbSysApplyAnnex implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbSysApplyAccount fkByMainId;
	private String annexName;
	private Integer num;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	public TbSysApplyAnnex() {
	}

	public TbSysApplyAnnex(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbSysApplyAnnex")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "MAIN_ID")
	public TbSysApplyAccount getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TbSysApplyAccount fkByMainId) {
		this.fkByMainId = fkByMainId;
	}

	@Column(name = "ANNEX_NAME")
	public String getAnnexName() {
		return annexName;
	}

	public void setAnnexName(String annexName) {
		this.annexName = annexName;
	}

	@Column(name = "NUM")
	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE")
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID")
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE")
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID")
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

}