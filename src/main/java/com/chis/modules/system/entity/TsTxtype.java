package com.chis.modules.system.entity;


import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.chis.modules.system.enumn.TxType;

/**
 * 通讯方式
 * 
 * <AUTHOR>
 * @创建时间 2014-11-25
 */
@Entity
@Table(name = "TS_TXTYPE")
@SequenceGenerator(name = "TsTxtype_Seq", sequenceName = "TS_TXTYPE_SEQ", allocationSize = 1)
@NamedQueries({
    @NamedQuery(name = "TsTxtype.findByCode", query = "SELECT t FROM TsTxtype t WHERE t.typeCode=:typeCode"),
    @NamedQuery(name = "TsTxtype.findUsable", query = "SELECT t FROM TsTxtype t WHERE t.status=1 ORDER BY t.xh")
})
public class TsTxtype implements java.io.Serializable {
	
	private static final long serialVersionUID = 8083007828395766188L;
	private Integer rid;
	private String typeCode;
	private TxType txType;
	private String implClass;
	private Integer xh;
	private short status = 1;
	private short selected = 0;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	
	public TsTxtype() {
	}
	
	public TsTxtype(Integer rid) {
		this.rid = rid;
	}
	
	public TsTxtype(String typeCode, TxType txType, String implClass, Integer xh) {
		this.typeCode = typeCode;
		this.txType = txType;
		this.implClass = implClass;
		this.xh = xh;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsTxtype_Seq")
	public Integer getRid() {
		return this.rid;
	}
	
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@Column(name="TYPE_CODE")
	public String getTypeCode() {
		return typeCode;
	}

	public void setTypeCode(String typeCode) {
		this.typeCode = typeCode;
	}

	@Enumerated
	@Column(name = "TYPES")
	public TxType getTxType() {
		return txType;
	}

	public void setTxType(TxType txType) {
		this.txType = txType;
	}

	@Column(name = "IMPL_CLASS", length = 200)
	public String getImplClass() {
		return this.implClass;
	}
	
	public void setImplClass(String implClass) {
		this.implClass = implClass;
	}

    @Column(name = "XH", precision = 2, scale = 0)
    public Integer getXh() {
        return xh;
    }

    public void setXh(Integer xh) {
        this.xh = xh;
    }

	@Column(name = "STATUS" , precision = 1, scale = 0)
	public short getStatus() {
		return this.status;
	}
	
	public void setStatus(short status) {
		this.status = status;
	}

	@Transient
	public short getSelected() {
		return selected;
	}

	public void setSelected(short selected) {
		this.selected = selected;
	}
	

	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID", precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Column(name = "MODIFY_DATE")
	@Temporal(TemporalType.TIMESTAMP)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
}