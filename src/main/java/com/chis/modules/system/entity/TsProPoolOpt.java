package com.chis.modules.system.entity;


import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * TsProPoolOpt entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_PRO_POOL_OPT")
@SequenceGenerator(name = "TsProPoolOpt_Seq", sequenceName = "TS_PRO_POOL_OPT_SEQ", allocationSize = 1)
public class TsProPoolOpt implements java.io.Serializable,Cloneable  {

	// Fields

	private Integer rid;
	private TsProbExampool tsProbExampool;
	private Integer num;
	private String optionDesc;
	private String optionImg;
	private String optionValue;
	/**×分值*/
	private Integer optionScore;
	private Integer needFill;
	private String rmk;
	private Integer state;
	private String otherDesc;
	private String otherImg;
	private Integer jumpType;
	private String jumpQuestCode;
	private Date createDate;
	private Integer createManid;
	/**是否互斥*/
	private Integer isAlter;
	/**是否正确答案*/
	private Integer isCorrect;
	/**+填空是否多项*/
	private Integer isMulti;
	// Constructors
	/**是否有数字填空**/
	private Integer isNumFill;
	/**数字填空脚本***/
	private String numFillScript;
	/** default constructor */
	public TsProPoolOpt() {
	}

	/** minimal constructor */
	public TsProPoolOpt(Integer rid, TsProbExampool tsProbExampool,
			Integer state, Date createDate, Integer createManid) {
		this.rid = rid;
		this.tsProbExampool = tsProbExampool;
		this.state = state;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	/** full constructor */
	public TsProPoolOpt(Integer rid, TsProbExampool tsProbExampool,
			Integer num, String optionDesc, String optionImg,
			String optionValue, Integer optionScore, Integer needFill,
			String rmk, Integer state, String otherDesc, String otherImg,
			Integer jumpType, String jumpQuestCode, Date createDate,
			Integer createManid) {
		this.rid = rid;
		this.tsProbExampool = tsProbExampool;
		this.num = num;
		this.optionDesc = optionDesc;
		this.optionImg = optionImg;
		this.optionValue = optionValue;
		this.optionScore = optionScore;
		this.needFill = needFill;
		this.rmk = rmk;
		this.state = state;
		this.otherDesc = otherDesc;
		this.otherImg = otherImg;
		this.jumpType = jumpType;
		this.jumpQuestCode = jumpQuestCode;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	@Override
	public Object clone() throws CloneNotSupportedException {
		return super.clone();
	}
	
	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsProPoolOpt_Seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "QUEST_ID" )
	public TsProbExampool getTsProbExampool() {
		return this.tsProbExampool;
	}

	public void setTsProbExampool(TsProbExampool tsProbExampool) {
		this.tsProbExampool = tsProbExampool;
	}

	@Column(name = "NUM", precision = 22, scale = 0)
	public Integer getNum() {
		return this.num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	@Column(name = "OPTION_DESC", length = 500)
	public String getOptionDesc() {
		return this.optionDesc;
	}

	public void setOptionDesc(String optionDesc) {
		this.optionDesc = optionDesc;
	}

	@Column(name = "OPTION_IMG", length = 500)
	public String getOptionImg() {
		return this.optionImg;
	}

	public void setOptionImg(String optionImg) {
		this.optionImg = optionImg;
	}

	@Column(name = "OPTION_VALUE")
	public String getOptionValue() {
		return this.optionValue;
	}

	public void setOptionValue(String optionValue) {
		this.optionValue = optionValue;
	}

	@Column(name = "OPTION_SCORE", precision = 5, scale = 0)
	public Integer getOptionScore() {
		return this.optionScore;
	}

	public void setOptionScore(Integer optionScore) {
		this.optionScore = optionScore;
	}

	@Column(name = "NEED_FILL", precision = 1, scale = 0)
	public Integer getNeedFill() {
		return this.needFill;
	}

	public void setNeedFill(Integer needFill) {
		this.needFill = needFill;
	}

	@Column(name = "RMK", length = 500)
	public String getRmk() {
		return this.rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

	@Column(name = "STATE" , precision = 1, scale = 0)
	public Integer getState() {
		return this.state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	@Column(name = "OTHER_DESC", length = 2000)
	public String getOtherDesc() {
		return this.otherDesc;
	}

	public void setOtherDesc(String otherDesc) {
		this.otherDesc = otherDesc;
	}

	@Column(name = "OTHER_IMG", length = 500)
	public String getOtherImg() {
		return this.otherImg;
	}

	public void setOtherImg(String otherImg) {
		this.otherImg = otherImg;
	}

	@Column(name = "JUMP_TYPE", precision = 1, scale = 0)
	public Integer getJumpType() {
		return this.jumpType;
	}

	public void setJumpType(Integer jumpType) {
		this.jumpType = jumpType;
	}

	@Column(name = "JUMP_QUEST_CODE", length = 200)
	public String getJumpQuestCode() {
		return this.jumpQuestCode;
	}

	public void setJumpQuestCode(String jumpQuestCode) {
		this.jumpQuestCode = jumpQuestCode;
	}
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Column(name = "IS_ALTER")
	public Integer getIsAlter() {
		return isAlter;
	}

	public void setIsAlter(Integer isAlter) {
		this.isAlter = isAlter;
	}

	@Column(name = "IS_CORRECT")
	public Integer getIsCorrect() {
		return isCorrect;
	}

	public void setIsCorrect(Integer isCorrect) {
		this.isCorrect = isCorrect;
	}
	
	@Column(name = "IS_MULTI")
	public Integer getIsMulti() {
		return isMulti;
	}

	public void setIsMulti(Integer isMulti) {
		this.isMulti = isMulti;
	}
	@Transient
	public Integer getIsNumFill() {
		return isNumFill;
	}

	public void setIsNumFill(Integer isNumFill) {
		this.isNumFill = isNumFill;
	}
	@Transient
	public String getNumFillScript() {
		return numFillScript;
	}

	public void setNumFillScript(String numFillScript) {
		this.numFillScript = numFillScript;
	}
	
	

}