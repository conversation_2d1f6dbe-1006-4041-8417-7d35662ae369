package com.chis.modules.system.entity;

import java.util.ArrayList;
import java.util.List;

/***
 *  <p>类描述：角色类型</p>
 *
 * @ClassAuthor maox,2020年1月4日,RoleTypePo
 * <AUTHOR>
 *
 */
public class RoleTypePo {
	private Integer rid;
    private String codeName;
    private boolean ifSelected;
    private boolean disabled;
    private List<RolePo> rolePoList = new ArrayList<>();
    private List<String> itemSubs;
    
	public Integer getRid() {
		return rid;
	}
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	public String getCodeName() {
		return codeName;
	}
	public void setCodeName(String codeName) {
		this.codeName = codeName;
	}
	public boolean isIfSelected() {
		return ifSelected;
	}
	public void setIfSelected(boolean ifSelected) {
		this.ifSelected = ifSelected;
	}
	public boolean isDisabled() {
		return disabled;
	}
	public void setDisabled(boolean disabled) {
		this.disabled = disabled;
	}
	public List<RolePo> getRolePoList() {
		return rolePoList;
	}
	public void setRolePoList(List<RolePo> rolePoList) {
		this.rolePoList = rolePoList;
	}
	public List<String> getItemSubs() {
		return itemSubs;
	}
	public void setItemSubs(List<String> itemSubs) {
		this.itemSubs = itemSubs;
	}

}
