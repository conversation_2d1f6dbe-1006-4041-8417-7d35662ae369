package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * 消息子表
 * 
 * <AUTHOR>
 * @history 2014-07-14
 */
@Entity
@Table(name = "TD_MSG_SUB")
@SequenceGenerator(name = "TdMsgSub_seq", sequenceName = "TD_MSG_SUB_SEQ", allocationSize = 1)
public class TdMsgSub implements java.io.Serializable {

	private static final long serialVersionUID = -3959739709449648220L;
	private Integer rid;
	private TsUserInfo tsUserInfo;// 接收人
	private TdMsgMain tdMsgMain;// 消息主表ID
	private Date publishTime = new Date();// 接收时间
	private Integer acceptState = 0;// 接收状态

	public TdMsgSub() {
	}

	public TdMsgSub(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdMsgSub_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "PUBLISH_MAN" )
	public TsUserInfo getTsUserInfo() {
		return this.tsUserInfo;
	}

	public void setTsUserInfo(TsUserInfo tsUserInfo) {
		this.tsUserInfo = tsUserInfo;
	}

	@ManyToOne
	@JoinColumn(name = "MAIN_ID" )
	public TdMsgMain getTdMsgMain() {
		return this.tdMsgMain;
	}

	public void setTdMsgMain(TdMsgMain tdMsgMain) {
		this.tdMsgMain = tdMsgMain;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "PUBLISH_TIME", length = 7)
	public Date getPublishTime() {
		return this.publishTime;
	}

	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}

	@Column(name = "ACCEPT_STATE" , precision = 1, scale = 0)
	public Integer getAcceptState() {
		return this.acceptState;
	}

	public void setAcceptState(Integer acceptState) {
		this.acceptState = acceptState;
	}

}