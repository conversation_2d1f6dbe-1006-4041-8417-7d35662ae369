package com.chis.modules.system.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.system.enumn.SystemType;

/**
 * <AUTHOR>
 * @createDate 2015-02-03.
 */
@Entity
@Table(name = "TD_TEMPMETA_TYPE")
@SequenceGenerator(name = "TdTempmetaType_seq", sequenceName = "TD_TEMPMETA_TYPE_SEQ", allocationSize = 1)
@NamedQueries({
        @NamedQuery(name = "TdTempmetaType.findAll", query = "SELECT t FROM TdTempmetaType t order by t.systemType"),
        @NamedQuery(name = "TdTempmetaType.findByTempCode", query = "SELECT t FROM TdTempmetaType t WHERE t.tempCode = :tempTypecode")
})
public class TdTempmetaType implements Serializable {
    private static final long serialVersionUID = 5659200205111209812L;

    private Integer rid;
    private SystemType systemType;
    private String tempCode;
    private String rmk;
    private Date createDate;
    private Integer createManid;
    private List<TdTempmetaDefine> tdTempmetaDefineList = new ArrayList<TdTempmetaDefine>(0);

    public TdTempmetaType() {
    }

    public TdTempmetaType(Integer rid) {
        this.rid = rid;
    }

    public TdTempmetaType(SystemType systemType, String tempCode,String rmk, Date createDate, Integer createManid) {
        this.systemType = systemType;
        this.tempCode = tempCode;
        this.createDate = createDate;
        this.createManid = createManid;
        this.rmk = rmk;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTempmetaType_seq")
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Enumerated
    @Column(name = "PARAM_TYPE")
    public SystemType getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemType systemType) {
        this.systemType = systemType;
    }

    @Column(name = "TEMP_CODE")
    public String getTempCode() {
        return tempCode;
    }

    public void setTempCode(String tempCode) {
        this.tempCode = tempCode;
    }

    @Column(name = "RMK")
    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdTempmetaType")
    public List<TdTempmetaDefine> getTdTempmetaDefineList() {
        return tdTempmetaDefineList;
    }

    public void setTdTempmetaDefineList(List<TdTempmetaDefine> tdTempmetaDefineList) {
        this.tdTempmetaDefineList = tdTempmetaDefineList;
    }
}
