package com.chis.modules.system.entity;


import javax.persistence.*;

/**
 * 
 * <AUTHOR>
 * @createTime 2019-11-30
 */
@Entity
@Table(name = "TS_CONTRA_SUB")
@SequenceGenerator(name = "TsContraSub_seq", sequenceName = "TS_CONTRA_SUB_SEQ", allocationSize = 1)
public class TsContraSub implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsContraMain fkByMainId;
	private Integer busiType;

	private String busDesc;
	private String leftCode;

	private String leftDesc;
	private String rightCode;
	private String descr;

	private Integer dsfTag;

	private String dsfSpecialDesc;
	
	public TsContraSub() {
	}

	public TsContraSub(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsContraSub_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TsContraMain getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TsContraMain fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "BUSI_TYPE")	
	public Integer getBusiType() {
		return busiType;
	}

	public void setBusiType(Integer busiType) {
		this.busiType = busiType;
	}	
			
	@Column(name = "LEFT_CODE")	
	public String getLeftCode() {
		return leftCode;
	}

	public void setLeftCode(String leftCode) {
		this.leftCode = leftCode;
	}	
			
	@Column(name = "RIGHT_CODE")	
	public String getRightCode() {
		return rightCode;
	}

	public void setRightCode(String rightCode) {
		this.rightCode = rightCode;
	}	
			
	@Column(name = "DESCR")	
	public String getDescr() {
		return descr;
	}

	public void setDescr(String descr) {
		this.descr = descr;
	}

	@Column(name = "BUS_DESC")
	public String getBusDesc() {
		return busDesc;
	}
	public void setBusDesc(String busDesc) {
		this.busDesc = busDesc;
	}

	@Column(name = "LEFT_DESC")
	public String getLeftDesc() {
		return leftDesc;
	}
	public void setLeftDesc(String leftDesc) {
		this.leftDesc = leftDesc;
	}
	@Column(name = "DSF_TAG")
	public Integer getDsfTag() {
		return dsfTag;
	}
	public void setDsfTag(Integer dsfTag) {
		this.dsfTag = dsfTag;
	}
	@Column(name = "DSF_SPECIAL_DESC")
	public String getDsfSpecialDesc() {
		return dsfSpecialDesc;
	}
	public void setDsfSpecialDesc(String dsfSpecialDesc) {
		this.dsfSpecialDesc = dsfSpecialDesc;
	}
}