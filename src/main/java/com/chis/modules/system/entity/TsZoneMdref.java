package com.chis.modules.system.entity;


import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * TsZoneMdref entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_ZONE_MDREF")
@SequenceGenerator(name = "TsZoneMdref_seq", sequenceName = "TS_ZONE_MDREF_SEQ", allocationSize = 1)
public class TsZoneMdref implements java.io.Serializable {

	private static final long serialVersionUID = 5414704688047087512L;
	private Integer rid;
	private TsZone tsZoneByOldZoneid;
	private TsZone tsZoneByNewZoneid;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	public TsZoneMdref() {
	}

	public TsZoneMdref(Integer rid, TsZone tsZoneByOldZoneid,
			TsZone tsZoneByNewZoneid, Date createDate, Integer createManid) {
		this.rid = rid;
		this.tsZoneByOldZoneid = tsZoneByOldZoneid;
		this.tsZoneByNewZoneid = tsZoneByNewZoneid;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	/** full constructor */
	public TsZoneMdref(Integer rid, TsZone tsZoneByOldZoneid,
			TsZone tsZoneByNewZoneid, Date createDate,
			Integer createManid, Date modifyDate, Integer modifyManid) {
		this.rid = rid;
		this.tsZoneByOldZoneid = tsZoneByOldZoneid;
		this.tsZoneByNewZoneid = tsZoneByNewZoneid;
		this.createDate = createDate;
		this.createManid = createManid;
		this.modifyDate = modifyDate;
		this.modifyManid = modifyManid;
	}

	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsZoneMdref_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "OLD_ZONEID" )
	public TsZone getTsZoneByOldZoneid() {
		return this.tsZoneByOldZoneid;
	}

	public void setTsZoneByOldZoneid(TsZone tsZoneByOldZoneid) {
		this.tsZoneByOldZoneid = tsZoneByOldZoneid;
	}

	@ManyToOne
	@JoinColumn(name = "NEW_ZONEID" )
	public TsZone getTsZoneByNewZoneid() {
		return this.tsZoneByNewZoneid;
	}

	public void setTsZoneByNewZoneid(TsZone tsZoneByNewZoneid) {
		this.tsZoneByNewZoneid = tsZoneByNewZoneid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

}