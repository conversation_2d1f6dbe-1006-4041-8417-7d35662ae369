package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * AutoCode entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "AUTO_CODE")
@SequenceGenerator(name = "AutoCode_seq", sequenceName = "AUTO_CODE_SEQ",allocationSize=1)
public class AutoCode implements java.io.Serializable {

	// Fields

	private Integer rid;
	private String codeType;
	private String pfx;
	private Date dte;
	private Integer idxVal;
	private Date crttim;

	// Constructors

	public AutoCode() {
	}

	public AutoCode(Integer rid, String codeType, Integer idxVal,
			Date crttim) {
		this.rid = rid;
		this.codeType = codeType;
		this.idxVal = idxVal;
		this.crttim = crttim;
	}

	/** full constructor */
	public AutoCode(Integer rid, String codeType, String pfx, Date dte,
			Integer idxVal, Date crttim) {
		this.rid = rid;
		this.codeType = codeType;
		this.pfx = pfx;
		this.dte = dte;
		this.idxVal = idxVal;
		this.crttim = crttim;
	}

	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "AutoCode_seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Column(name = "CODE_TYPE" , length = 10)
	public String getCodeType() {
		return this.codeType;
	}

	public void setCodeType(String codeType) {
		this.codeType = codeType;
	}

	@Column(name = "PFX", length = 50)
	public String getPfx() {
		return this.pfx;
	}

	public void setPfx(String pfx) {
		this.pfx = pfx;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "DTE", length = 7)
	public Date getDte() {
		return this.dte;
	}

	public void setDte(Date dte) {
		this.dte = dte;
	}

	@Column(name = "IDX_VAL" , precision = 22, scale = 0)
	public Integer getIdxVal() {
		return this.idxVal;
	}

	public void setIdxVal(Integer idxVal) {
		this.idxVal = idxVal;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "CRTTIM" , length = 7)
	public Date getCrttim() {
		return this.crttim;
	}

	public void setCrttim(Date crttim) {
		this.crttim = crttim;
	}

}