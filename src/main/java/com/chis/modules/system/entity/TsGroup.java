package com.chis.modules.system.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * 组
 * <AUTHOR>
 */
@Entity
@Table(name = "TS_GROUP")
@SequenceGenerator(name = "TsGroup_seq", sequenceName = "TS_GROUP_SEQ", allocationSize = 1)
public class TsGroup implements Serializable{

    private static final long serialVersionUID = 4077165270685191657L;
    private Integer rid;
    private TsUnit tsUnit;
    private String groupName;
    private Integer xh;
    private Integer ifReveal;
    private Date createDate;
    private Integer createManId;
    private Date modifyDate;
    private Integer modifyManId;
    
    /**是否被选中*/
    private boolean selected = Boolean.FALSE;

    public TsGroup() {
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsGroup_seq")
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "UNIT_RID" )
    public TsUnit getTsUnit() {
        return tsUnit;
    }

    public void setTsUnit(TsUnit tsUnit) {
        this.tsUnit = tsUnit;
    }

    //@NotBlank(message = "组名称不允许为空！")
    @Column(name = "GROUP_NAME" ,unique = true   ,length = 200)
    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    @Column(name = "XH"   )
    public Integer getXh() {
        return xh;
    }

    public void setXh(Integer xh) {
        this.xh = xh;
    }

    @Column(name = "IF_REVEAL"   , precision = 1 , scale = 0)
    public Integer getIfReveal() {
        return ifReveal;
    }

    public void setIfReveal(Integer ifReveal) {
        this.ifReveal = ifReveal;
    }

    @Column(name = "CREATE_DATE"  )
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID"  )
    public Integer getCreateManId() {
        return createManId;
    }

    public void setCreateManId(Integer createManId) {
        this.createManId = createManId;
    }

    @Column(name = "MODIFY_DATE" )
    @Temporal(TemporalType.TIMESTAMP)
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID" )
    public Integer getModifyManId() {
        return modifyManId;
    }

    public void setModifyManId(Integer modifyManId) {
        this.modifyManId = modifyManId;
    }

    @Transient
	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}
    
    
}
