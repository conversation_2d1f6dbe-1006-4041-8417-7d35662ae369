package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

/**
 * 问卷选项模版 TsProTempl entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_PRO_TEMPL", uniqueConstraints = @UniqueConstraint(columnNames = "TMPL_CODE"))
@SequenceGenerator(allocationSize = 1, sequenceName = "TS_PRO_TEMPL_SEQ", name = "TsProTemplSeq")
public class TsProTempl implements java.io.Serializable {

	// Fields

	private Integer rid;
	private String tmplName;
	private String tmplCode;
	private String tmplOpts;
	private Date createDate;
	private Integer createManid;

	// Constructors

	/** default constructor */
	public TsProTempl() {
	}

	/** minimal constructor */
	public TsProTempl(Integer rid, String tmplName, String tmplCode,
			Date createDate, Integer createManid) {
		this.rid = rid;
		this.tmplName = tmplName;
		this.tmplCode = tmplCode;
		this.createDate = createDate;
		this.createManid = createManid;
	}
	

	public TsProTempl(String tmplName, String tmplOpts) {
		super();
		this.tmplName = tmplName;
		this.tmplOpts = tmplOpts;
	}

	public TsProTempl(Integer rid, String tmplName, String tmplCode,
			String tmplOpts) {
		super();
		this.rid = rid;
		this.tmplName = tmplName;
		this.tmplCode = tmplCode;
		this.tmplOpts = tmplOpts;
	}

	/** full constructor */
	public TsProTempl(Integer rid, String tmplName, String tmplCode,
			String tmplOpts, Date createDate, Integer createManid) {
		this.rid = rid;
		this.tmplName = tmplName;
		this.tmplCode = tmplCode;
		this.tmplOpts = tmplOpts;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(generator = "TsProTemplSeq", strategy = GenerationType.SEQUENCE)
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Column(name = "TMPL_NAME" , length = 100)
	public String getTmplName() {
		return this.tmplName;
	}

	public void setTmplName(String tmplName) {
		this.tmplName = tmplName;
	}

	@Column(name = "TMPL_CODE", unique = true , length = 50)
	public String getTmplCode() {
		return this.tmplCode;
	}

	public void setTmplCode(String tmplCode) {
		this.tmplCode = tmplCode;
	}

	@Column(name = "TMPL_OPTS")
	public String getTmplOpts() {
		return this.tmplOpts;
	}

	public void setTmplOpts(String tmplOpts) {
		this.tmplOpts = tmplOpts;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

}