package com.chis.modules.system.entity;
import javax.persistence.*;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @createTime 2020-8-6
 */
@Entity
@Table(name = "TB_SYS_APPLY_ACCOUNT")
@SequenceGenerator(name = "TbSysApplyAccount", sequenceName = "TB_SYS_APPLY_ACCOUNT_SEQ", allocationSize = 1)
public class TbSysApplyAccount implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsBsSort fkBySortId;
	private String rmk;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private Integer num;

	public TbSysApplyAccount() {
	}

	public TbSysApplyAccount(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbSysApplyAccount")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "SORT_ID")
	public TsBsSort getFkBySortId() {
		return fkBySortId;
	}

	public void setFkBySortId(TsBsSort fkBySortId) {
		this.fkBySortId = fkBySortId;
	}

	@Column(name = "RMK")
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE")
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID")
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE")
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID")
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Column(name = "NUM")

	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}
}