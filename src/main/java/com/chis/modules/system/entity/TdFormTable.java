package com.chis.modules.system.entity;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.*;

/**
 * 
 * <AUTHOR>
 * @createTime 2015-12-3
 */
@Entity
@Table(name = "TD_FORM_TABLE")
@SequenceGenerator(name = "TdFormTable", sequenceName = "TD_FORM_TABLE_SEQ", allocationSize = 1)
public class TdFormTable implements java.io.Serializable, Cloneable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String cnName;
	private String enName;
	/**1：单表2：主表3：子表*/
	private Integer formProp;
	/**表属性是子表的时候，要选择主表*/
	private TdFormTable tdFormTableByMainTabId;
	private String fkField;
	/**0：保存 1：提交*/
	private Integer state;
	private List<TdFormTable> childList = new ArrayList<TdFormTable>(0);
	private List<TdFormField> fieldList = new ArrayList<TdFormField>(0);
	
	public TdFormTable() {
	}

	public TdFormTable(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFormTable")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "CN_NAME")	
	public String getCnName() {
		return cnName;
	}

	public void setCnName(String cnName) {
		this.cnName = cnName;
	}	
			
	@Column(name = "EN_NAME")	
	public String getEnName() {
		return enName;
	}

	public void setEnName(String enName) {
		this.enName = enName;
	}	
			
	@Column(name = "FORM_PROP")	
	public Integer getFormProp() {
		return formProp;
	}

	public void setFormProp(Integer formProp) {
		this.formProp = formProp;
	}	
			
	@ManyToOne
	@JoinColumn(name = "MAIN_TAB_ID")	
	public TdFormTable getTdFormTableByMainTabId() {
		return tdFormTableByMainTabId;
	}

	public void setTdFormTableByMainTabId(TdFormTable tdFormTableByMainTabId) {
		this.tdFormTableByMainTabId = tdFormTableByMainTabId;
	}	
			
	@Column(name = "FK_FIELD")	
	public String getFkField() {
		return fkField;
	}

	public void setFkField(String fkField) {
		this.fkField = fkField;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdFormTableByMainTabId",orphanRemoval=true)
	public List<TdFormTable> getChildList() {
		return childList;
	}

	public void setChildList(List<TdFormTable> childList) {
		this.childList = childList;
	}

	@OrderBy(value="rowNum ASC, colNum ASC")
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdFormTableByTableId",orphanRemoval=true)
	public List<TdFormField> getFieldList() {
		return fieldList;
	}

	public void setFieldList(List<TdFormField> fieldList) {
		this.fieldList = fieldList;
	}
	
	@Override
	public Object clone() throws CloneNotSupportedException {
		return super.clone();
	}
	
}