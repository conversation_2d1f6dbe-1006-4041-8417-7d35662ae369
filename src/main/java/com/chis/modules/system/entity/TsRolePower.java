package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * 
 * <AUTHOR>
 * @createTime 2019-12-21
 */
@Entity
@Table(name = "TS_ROLE_POWER")
@SequenceGenerator(name = "TsRolePower", sequenceName = "TS_ROLE_POWER_SEQ", allocationSize = 1)
public class TsRolePower implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsRole fkByRoleId;
	private TsSimpleCode fkByRoleTypeId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TsRolePower() {
	}

	public TsRolePower(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsRolePower")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ROLE_ID")			
	public TsRole getFkByRoleId() {
		return fkByRoleId;
	}

	public void setFkByRoleId(TsRole fkByRoleId) {
		this.fkByRoleId = fkByRoleId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ROLE_TYPE_ID")			
	public TsSimpleCode getFkByRoleTypeId() {
		return fkByRoleTypeId;
	}

	public void setFkByRoleTypeId(TsSimpleCode fkByRoleTypeId) {
		this.fkByRoleTypeId = fkByRoleTypeId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}