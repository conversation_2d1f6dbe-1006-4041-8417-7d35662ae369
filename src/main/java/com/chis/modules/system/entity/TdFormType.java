package com.chis.modules.system.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2015-12-3
 */
@Entity
@Table(name = "TD_FORM_TYPE")
@SequenceGenerator(name = "TdFormType", sequenceName = "TD_FORM_TYPE_SEQ", allocationSize = 1)
public class TdFormType implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String typeCode;
	private String typeName;
	private Date createDate;
	private Integer createManid;
	
	public TdFormType() {
	}

	public TdFormType(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFormType")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "TYPE_CODE")	
	public String getTypeCode() {
		return typeCode;
	}

	public void setTypeCode(String typeCode) {
		this.typeCode = typeCode;
	}	
			
	@Column(name = "TYPE_NAME")	
	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
}