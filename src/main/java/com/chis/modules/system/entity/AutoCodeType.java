package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * AutoCodeType entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "AUTO_CODE_TYPE")
@SequenceGenerator(name = "AutoCodeType_seq", sequenceName = "AUTO_CODE_TYPE_SEQ",allocationSize=1)
public class AutoCodeType implements java.io.Serializable {

	// Fields

	private Integer rid;
	private String codeType;
	private String pfx;
	private String connectStr;
	private String sptchr;
	private Integer seqwdt;
	private String implementsClass;
	private String rmk;
	private Date crttim;

	// Constructors

	/** default constructor */
	public AutoCodeType() {
	}

	/** minimal constructor */
	public AutoCodeType(Integer rid, String codeType, String implementsClass) {
		this.rid = rid;
		this.codeType = codeType;
		this.implementsClass = implementsClass;
	}

	/** full constructor */
	public AutoCodeType(Integer rid, String codeType, String pfx,
			String connectStr, String sptchr, Integer seqwdt,
			String implementsClass, String rmk, Date crttim) {
		this.rid = rid;
		this.codeType = codeType;
		this.pfx = pfx;
		this.connectStr = connectStr;
		this.sptchr = sptchr;
		this.seqwdt = seqwdt;
		this.implementsClass = implementsClass;
		this.rmk = rmk;
		this.crttim = crttim;
	}

	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "AutoCodeType_seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Column(name = "CODE_TYPE" , length = 10)
	public String getCodeType() {
		return this.codeType;
	}

	public void setCodeType(String codeType) {
		this.codeType = codeType;
	}

	@Column(name = "PFX", length = 20)
	public String getPfx() {
		return this.pfx;
	}

	public void setPfx(String pfx) {
		this.pfx = pfx;
	}

	@Column(name = "CONNECT_STR", length = 10)
	public String getConnectStr() {
		return this.connectStr;
	}

	public void setConnectStr(String connectStr) {
		this.connectStr = connectStr;
	}

	@Column(name = "SPTCHR", length = 20)
	public String getSptchr() {
		return this.sptchr;
	}

	public void setSptchr(String sptchr) {
		this.sptchr = sptchr;
	}

	@Column(name = "SEQWDT", precision = 22, scale = 0)
	public Integer getSeqwdt() {
		return this.seqwdt;
	}

	public void setSeqwdt(Integer seqwdt) {
		this.seqwdt = seqwdt;
	}

	@Column(name = "IMPLEMENTS_CLASS" , length = 100)
	public String getImplementsClass() {
		return this.implementsClass;
	}

	public void setImplementsClass(String implementsClass) {
		this.implementsClass = implementsClass;
	}

	@Column(name = "RMK", length = 200)
	public String getRmk() {
		return this.rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "CRTTIM", length = 7)
	public Date getCrttim() {
		return this.crttim;
	}

	public void setCrttim(Date crttim) {
		this.crttim = crttim;
	}

}