package com.chis.modules.system.entity;

import javax.persistence.*;
import java.io.Serializable;

/**
 *  用户组
 *  <AUTHOR>
 */
@Entity
@Table(name = "TS_USER_GROUP")
@SequenceGenerator(name = "TsUserGroup_seq", sequenceName = "TS_USER_GROUP_SEQ", allocationSize = 1)
public class TsUserGroup implements Serializable{
    private static final long serialVersionUID = 5268867969688972142L;
    private Integer rid;
    private TsGroup tsGroup;
    private TsUserInfo tsUserInfo;

    public TsUserGroup() {
    }

    public TsUserGroup(TsGroup tsGroup, TsUserInfo tsUserInfo) {
        this.tsGroup = tsGroup;
        this.tsUserInfo = tsUserInfo;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsUserGroup_seq")
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "GROUP_ID" )
    public TsGroup getTsGroup() {
        return tsGroup;
    }

    public void setTsGroup(TsGroup tsGroup) {
        this.tsGroup = tsGroup;
    }

    @ManyToOne
    @JoinColumn(name = "USER_INFO_ID" )
    public TsUserInfo getTsUserInfo() {
        return tsUserInfo;
    }

    public void setTsUserInfo(TsUserInfo tsUserInfo) {
        this.tsUserInfo = tsUserInfo;
    }
}
