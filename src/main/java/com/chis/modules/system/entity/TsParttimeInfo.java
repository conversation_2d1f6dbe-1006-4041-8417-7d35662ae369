package com.chis.modules.system.entity;


import javax.persistence.*;

/**
 * <AUTHOR>
 * @createDate 2014-10-11
 */
@Entity
@Table(name = "TS_PARTTIME_INFO")
@SequenceGenerator(name = "TsParttimeInfo_seq", sequenceName = "TS_PARTTIME_INFO_SEQ",allocationSize=1)
public class TsParttimeInfo  implements java.io.Serializable{
    private static final long serialVersionUID = -2018199448947216259L;
    private Integer rid;
    private TsOffice tsOffice;
    private TbSysEmp tbSysEmp;
    private TsSimpleCode dutyId;
    private short isLeader;

    public TsParttimeInfo() {
    }

    public TsParttimeInfo(Integer rid) {
        this.rid = rid;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsParttimeInfo_seq")
    @Column(name = "RID", unique = true )
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "OFFICE_ID")
    public TsOffice getTsOffice() {
        return tsOffice;
    }

    public void setTsOffice(TsOffice tsOffice) {
        this.tsOffice = tsOffice;
    }

    @ManyToOne
    @JoinColumn(name = "EMP_ID")
    public TbSysEmp getTbSysEmp() {
        return tbSysEmp;
    }

    public void setTbSysEmp(TbSysEmp tbSysEmp) {
        this.tbSysEmp = tbSysEmp;
    }

    @ManyToOne
    @JoinColumn(name = "DUTY_ID")
    public TsSimpleCode getDutyId() {
        return dutyId;
    }

    public void setDutyId(TsSimpleCode dutyId) {
        this.dutyId = dutyId;
    }

    @Column(name = "IS_LEADER")
    public short getIsLeader() {
        return isLeader;
    }

    public void setIsLeader(short isLeader) {
        this.isLeader = isLeader;
    }

}
