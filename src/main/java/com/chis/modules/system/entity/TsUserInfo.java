package com.chis.modules.system.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.NotBlank;

/**
 * TsUserInfo entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_USER_INFO", uniqueConstraints = @UniqueConstraint(columnNames = "USER_NO"))
@SequenceGenerator(name = "TsUserInfo_seq", sequenceName = "TS_USER_INFO_SEQ", allocationSize = 1)
@NamedQueries({ @NamedQuery(name = "TsUserInfo.findByUserNo", query = "SELECT t FROM TsUserInfo t WHERE t.userNo=:userNo") })
public class TsUserInfo implements java.io.Serializable {

	private static final long serialVersionUID = 3044441202695689610L;
	private Integer rid;
	private TbSysEmp tbSysEmp;
	private TsUnit tsUnit;
	private Short userType;
	private String userNo;
	private String username;
	private String password;
	private Short ifModpsw;
	private String rmk;
	private Short ifReveal;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private Boolean useradmin = Boolean.FALSE;
	private List<TsUserDesk> tsUserDesks = new ArrayList<TsUserDesk>(0);
	private List<TsUserMenu> tsUserMenus = new ArrayList<TsUserMenu>(0);
	private List<TsUserRole> tsUserRoles = new ArrayList<TsUserRole>(0);
	/** 新增单位属性Map<单位属性编码，单位属性实体> */
	private Map<String, TsBsSort> tsBsSortMap = null;

	/** 拥有角色的名称，逗号隔开 */
	private String roleName;
	/** 科室名称 */
	private String officeName;
	/** 科室RID */
	private Integer officeId;
	/** 组名称 */
	private String groupName;
	/** 组名称 */
	private Integer groupId;
	/** 职务RID */
	private Integer dutyId;
	/** 职务 */
	private String dutyName;
	/** 是否领导 1:是 2-否 */
	private Integer isLeader;
	/** 手机号码 */
	private String mbPhone;
	/** 邮箱 */
	private String email;
	/** 是否被选中 */
	private boolean selected = Boolean.FALSE;
	/** 当前登录人所在的科室 */
	private TsOffice tsOffice = new TsOffice();

	/** +包含兼职科室的Id */
	private String officeIds;
	/** +包含兼职科室的名称 */
	private String officeNames;
	// 手机号码
	private String mbNum;
	private Integer isAdd;
	private Integer uploadTag;
	private String errMsg;
	
	/*********************临时字段************************/
	/**是否管理员*/
	private boolean ifAdmin;
	
	private List<TsMenu> allMenus = new ArrayList<TsMenu>(0);
	private List<TsMenu> inUseMenus = new ArrayList<TsMenu>(0);
	
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	private Integer dispKjmenu;

	// Constructors
	private Date validBegDate;
	private Date validEndDate;
	private String idc;
	/**+用户类型20210922*/
	private Integer userTypeTag;

	/**
	 *主题
	 */
	private TsSimpleCode themeId;

	private Date pwdBegDate;
	private Date pwdEndDate;

	public TsUserInfo() {
	}

	public TsUserInfo(Integer rid) {
		this.rid = rid;
	}

	public TsUserInfo(Integer rid, String userNo, String username) {
		this.rid = rid;
		this.userNo = userNo;
		this.username = username;
	}

	/**
	 * <p>
	 * 方法描述：
	 * </p>
	 * 
	 * @MethodAuthor rj,2018年3月6日,TsUserInfo(Integer rid, String userNo, String
	 *               username, String mbNum)
	 * @param rid
	 * @param userNo
	 * @param username
	 * @param mbNum
	 */
	public TsUserInfo(Integer rid, String userNo, String username, String mbNum) {
		this.rid = rid;
		this.userNo = userNo;
		this.username = username;
		this.mbNum = mbNum;
	}

	public TsUserInfo(Integer rid, Short userType, String userNo, String password, Short ifReveal, Date createDate,
			Integer createManid) {
		this.rid = rid;
		this.userType = userType;
		this.userNo = userNo;
		this.password = password;
		this.ifReveal = ifReveal;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsUserInfo_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_ID")
	public TbSysEmp getTbSysEmp() {
		return this.tbSysEmp;
	}

	public void setTbSysEmp(TbSysEmp tbSysEmp) {
		this.tbSysEmp = tbSysEmp;
	}

	@ManyToOne
	@JoinColumn(name = "UNIT_RID")
	public TsUnit getTsUnit() {
		return tsUnit;
	}

	public void setTsUnit(TsUnit tsUnit) {
		this.tsUnit = tsUnit;
	}

	@Column(name = "USER_TYPE" , precision = 1, scale = 0)
	public Short getUserType() {
		return this.userType;
	}

	public void setUserType(Short userType) {
		this.userType = userType;
	}

	@Column(name = "USER_NO", unique = true , length = 30)
	public String getUserNo() {
		return this.userNo;
	}

	public void setUserNo(String userNo) {
		this.userNo = userNo;
	}

	@Column(name = "USERNAME", length = 30)
	public String getUsername() {
		return this.username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	@Column(name = "PASSWORD" , length = 50)
	public String getPassword() {
		return this.password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	@Column(name = "IF_MODPSW", precision = 1, scale = 0)
	public Short getIfModpsw() {
		return this.ifModpsw;
	}

	public void setIfModpsw(Short ifModpsw) {
		this.ifModpsw = ifModpsw;
	}

	@Column(name = "RMK", length = 100)
	public String getRmk() {
		return this.rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

	@Column(name = "IF_REVEAL" , precision = 1, scale = 0)
	public Short getIfReveal() {
		return this.ifReveal;
	}

	public void setIfReveal(Short ifReveal) {
		this.ifReveal = ifReveal;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsUserInfo")
	public List<TsUserDesk> getTsUserDesks() {
		return tsUserDesks;
	}

	public void setTsUserDesks(List<TsUserDesk> tsUserDesks) {
		this.tsUserDesks = tsUserDesks;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsUserInfo")
	public List<TsUserMenu> getTsUserMenus() {
		return tsUserMenus;
	}

	public void setTsUserMenus(List<TsUserMenu> tsUserMenus) {
		this.tsUserMenus = tsUserMenus;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsUserInfo")
	public List<TsUserRole> getTsUserRoles() {
		return tsUserRoles;
	}

	public void setTsUserRoles(List<TsUserRole> tsUserRoles) {
		this.tsUserRoles = tsUserRoles;
	}

	@Column(name = "USERADMIN")
	public Boolean getUseradmin() {
		return useradmin;
	}

	public void setUseradmin(Boolean useradmin) {
		this.useradmin = useradmin;
	}

	@Transient
	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	@Transient
	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	@Transient
	public Integer getOfficeId() {
		return officeId;
	}

	public void setOfficeId(Integer officeId) {
		this.officeId = officeId;
	}

	@Transient
	public Integer getDutyId() {
		return dutyId;
	}

	public void setDutyId(Integer dutyId) {
		this.dutyId = dutyId;
	}

	@Transient
	public Integer getIsLeader() {
		return isLeader;
	}

	public void setIsLeader(Integer isLeader) {
		this.isLeader = isLeader;
	}

	@Transient
	public String getMbPhone() {
		return mbPhone;
	}

	public void setMbPhone(String mbPhone) {
		this.mbPhone = mbPhone;
	}

	@Transient
	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	@Transient
	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	@Transient
	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}

	@Transient
	public TsOffice getTsOffice() {
		return tsOffice;
	}

	public void setTsOffice(TsOffice tsOffice) {
		this.tsOffice = tsOffice;
	}

	@Transient
	public Map<String, TsBsSort> getTsBsSortMap() {
		return tsBsSortMap;
	}

	public void setTsBsSortMap(Map<String, TsBsSort> tsBsSortMap) {
		this.tsBsSortMap = tsBsSortMap;
	}

	@Transient
	public String getDutyName() {
		return dutyName;
	}

	public void setDutyName(String dutyName) {
		this.dutyName = dutyName;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((rid == null) ? 0 : rid.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		TsUserInfo other = (TsUserInfo) obj;
		if (rid == null) {
			return false;
		} else if (!rid.equals(other.rid))
			return false;
		return true;
	}

	@Transient
	public String getOfficeIds() {
		return officeIds;
	}

	public void setOfficeIds(String officeIds) {
		this.officeIds = officeIds;
	}

	@Transient
	public String getOfficeNames() {
		return officeNames;
	}

	public void setOfficeNames(String officeNames) {
		this.officeNames = officeNames;
	}

	@Column(name = "MB_NUM", length = 13)
	public String getMbNum() {
		return this.mbNum;
	}

	public void setMbNum(String mbNum) {
		this.mbNum = mbNum;
	}

	@Column(name = "DISP_KJMENU")
	public Integer getDispKjmenu() {
		return dispKjmenu;
	}

	public void setDispKjmenu(Integer dispKjmenu) {
		this.dispKjmenu = dispKjmenu;
	}
	
	@Column(name = "IS_ADD")	
	public Integer getIsAdd() {
		return isAdd;
	}

	public void setIsAdd(Integer isAdd) {
		this.isAdd = isAdd;
	}	
			
	@Column(name = "UPLOAD_TAG")	
	public Integer getUploadTag() {
		return uploadTag;
	}

	public void setUploadTag(Integer uploadTag) {
		this.uploadTag = uploadTag;
	}	
			
	@Column(name = "ERR_MSG")	
	public String getErrMsg() {
		return errMsg;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}

	@Transient
	public boolean isIfAdmin() {
		return ifAdmin;
	}

	public void setIfAdmin(boolean ifAdmin) {
		this.ifAdmin = ifAdmin;
	}

	@Column(name = "VALID_BEG_DATE")
	public Date getValidBegDate() {
		return validBegDate;
	}

	public void setValidBegDate(Date validBegDate) {
		this.validBegDate = validBegDate;
	}

	@Column(name = "VALID_END_DATE")
	public Date getValidEndDate() {
		return validEndDate;
	}

	public void setValidEndDate(Date validEndDate) {
		this.validEndDate = validEndDate;
	}

	@Column(name = "IDC")
	public String getIdc() {
		return idc;
	}

	public void setIdc(String idc) {
		this.idc = idc;
	}

	@Transient
	public List<TsMenu> getAllMenus() {
		return allMenus;
	}

	public void setAllMenus(List<TsMenu> allMenus) {
		this.allMenus = allMenus;
	}

	@Transient
	public List<TsMenu> getInUseMenus() {
		return inUseMenus;
	}

	public void setInUseMenus(List<TsMenu> inUseMenus) {
		this.inUseMenus = inUseMenus;
	}

	@Column(name="USER_TYPE_TAG")
	public Integer getUserTypeTag() {
		return userTypeTag;
	}

	public void setUserTypeTag(Integer userTypeTag) {
		this.userTypeTag = userTypeTag;
	}
	@ManyToOne
	@JoinColumn(name = "THEME_ID")
	public TsSimpleCode getThemeId() {
		return themeId;
	}

	public void setThemeId(TsSimpleCode themeId) {
		this.themeId = themeId;
	}

	@Column(name="PWD_BEG_DATE")
	public Date getPwdBegDate() {
		return pwdBegDate;
	}

	public void setPwdBegDate(Date pwdBegDate) {
		this.pwdBegDate = pwdBegDate;
	}

	@Column(name="PWD_END_DATE")
	public Date getPwdEndDate() {
		return pwdEndDate;
	}

	public void setPwdEndDate(Date pwdEndDate) {
		this.pwdEndDate = pwdEndDate;
	}
}
