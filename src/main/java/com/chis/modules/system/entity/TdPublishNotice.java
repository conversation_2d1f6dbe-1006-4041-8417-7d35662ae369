package com.chis.modules.system.entity;

import java.util.Date;
import java.util.List;

import javax.persistence.*;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-11-29
 */
@Entity
@Table(name = "TD_PUBLISH_NOTICE")
@SequenceGenerator(name = "TdPublishNotice", sequenceName = "TD_PUBLISH_NOTICE_SEQ", allocationSize = 1)
public class TdPublishNotice implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsSimpleCode fkByPublishTypeId;
	private String otherType;
	private String title;
	private String content;
	private Integer reads;
	private Integer totals;
	private Integer state;
	private Date publishDate;
	private TsUserInfo fkByPublishPsnId;
	private TsUnit fkByPublishUnitId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private List<TdPublishNoticeAnnex> noticeAnnexList;

	/** +是否需要回执20230703 */
	private Integer ifNeedFeedback;
	
	public TdPublishNotice() {
	}

	public TdPublishNotice(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdPublishNotice")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "PUBLISH_TYPE_ID")			
	public TsSimpleCode getFkByPublishTypeId() {
		return fkByPublishTypeId;
	}

	public void setFkByPublishTypeId(TsSimpleCode fkByPublishTypeId) {
		this.fkByPublishTypeId = fkByPublishTypeId;
	}	
			
	@Column(name = "OTHER_TYPE")	
	public String getOtherType() {
		return otherType;
	}

	public void setOtherType(String otherType) {
		this.otherType = otherType;
	}	
			
	@Column(name = "TITLE")	
	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}	
			
	@Column(name = "CONTENT")	
	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}	
			
	@Column(name = "READS")	
	public Integer getReads() {
		return reads;
	}

	public void setReads(Integer reads) {
		this.reads = reads;
	}	
			
	@Column(name = "TOTALS")	
	public Integer getTotals() {
		return totals;
	}

	public void setTotals(Integer totals) {
		this.totals = totals;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "PUBLISH_DATE")			
	public Date getPublishDate() {
		return publishDate;
	}

	public void setPublishDate(Date publishDate) {
		this.publishDate = publishDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "PUBLISH_PSN_ID")			
	public TsUserInfo getFkByPublishPsnId() {
		return fkByPublishPsnId;
	}

	public void setFkByPublishPsnId(TsUserInfo fkByPublishPsnId) {
		this.fkByPublishPsnId = fkByPublishPsnId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "PUBLISH_UNIT_ID")			
	public TsUnit getFkByPublishUnitId() {
		return fkByPublishUnitId;
	}

	public void setFkByPublishUnitId(TsUnit fkByPublishUnitId) {
		this.fkByPublishUnitId = fkByPublishUnitId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId")
	public List<TdPublishNoticeAnnex> getNoticeAnnexList() {
		return noticeAnnexList;
	}

	public void setNoticeAnnexList(List<TdPublishNoticeAnnex> noticeAnnexList) {
		this.noticeAnnexList = noticeAnnexList;
	}

	@Column(name = "IF_NEED_FEEDBACK")
	public Integer getIfNeedFeedback() {
		return ifNeedFeedback;
	}

	public void setIfNeedFeedback(Integer ifNeedFeedback) {
		this.ifNeedFeedback = ifNeedFeedback;
	}

}