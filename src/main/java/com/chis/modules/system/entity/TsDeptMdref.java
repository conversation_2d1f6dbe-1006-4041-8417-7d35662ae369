package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * 科室归并
 * <AUTHOR>
 */
@Entity
@Table(name = "TS_DEPT_MDREF")
@SequenceGenerator(name = "TsDeptMdref_seq", sequenceName = "TS_DEPT_MDREF_SEQ",allocationSize=1)
public class TsDeptMdref implements java.io.Serializable {

	private static final long serialVersionUID = 5026933430518573973L;
	private Integer rid;
	private TsOffice tsOfficeOld;
	private TsOffice tsOfficeNew;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	public TsDeptMdref() {
	}


	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsDeptMdref_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "OLD_DEPTID")
	public TsOffice getTsOfficeOld() {
		return tsOfficeOld;
	}

	public void setTsOfficeOld(TsOffice tsOfficeOld) {
		this.tsOfficeOld = tsOfficeOld;
	}

	@ManyToOne
	@JoinColumn(name = "NEW_DEPTID")
	public TsOffice getTsOfficeNew() {
		return tsOfficeNew;
	}


	public void setTsOfficeNew(TsOffice tsOfficeNew) {
		this.tsOfficeNew = tsOfficeNew;
	}


	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

}