package com.chis.modules.system.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-8-20
 */
@Entity
@Table(name = "TD_PROB_ANS_DETAIL")
@SequenceGenerator(name = "TdProbAnsDetail", sequenceName = "TD_PROB_ANS_DETAIL_SEQ", allocationSize = 1)
public class TdProbAnsDetail implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer mainType;
	private Integer mainId;
	private TsProbSubject fkByQuestId;
	private Integer optionValue;
	private String scoreValue;
	private String fillValue;
	private Integer multiNum;
	private Date createDate;
	private Integer createManid;
	/**问卷类型*/
	private TsSimpleCode fkByQueTypeId;
	/**题库id*/
	private TsProbExampool fkByExampoolId;
	
	public TdProbAnsDetail() {
	}

	public TdProbAnsDetail(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdProbAnsDetail")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "MAIN_TYPE")	
	public Integer getMainType() {
		return mainType;
	}

	public void setMainType(Integer mainType) {
		this.mainType = mainType;
	}	
			
	@Column(name = "MAIN_ID")	
	public Integer getMainId() {
		return mainId;
	}

	public void setMainId(Integer mainId) {
		this.mainId = mainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "QUEST_ID")			
	public TsProbSubject getFkByQuestId() {
		return fkByQuestId;
	}

	public void setFkByQuestId(TsProbSubject fkByQuestId) {
		this.fkByQuestId = fkByQuestId;
	}	
			
	@Column(name = "OPTION_VALUE")	
	public Integer getOptionValue() {
		return optionValue;
	}

	public void setOptionValue(Integer optionValue) {
		this.optionValue = optionValue;
	}	
			
	@Column(name = "SCORE_VALUE")	
	public String getScoreValue() {
		return scoreValue;
	}

	public void setScoreValue(String scoreValue) {
		this.scoreValue = scoreValue;
	}	
			
	@Column(name = "FILL_VALUE")	
	public String getFillValue() {
		return fillValue;
	}

	public void setFillValue(String fillValue) {
		this.fillValue = fillValue;
	}	
			
	@Column(name = "MULTI_NUM")	
	public Integer getMultiNum() {
		return multiNum;
	}

	public void setMultiNum(Integer multiNum) {
		this.multiNum = multiNum;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}
	@ManyToOne
	@JoinColumn(name = "QUE_TYPE_ID")
	public TsSimpleCode getFkByQueTypeId() {
		return fkByQueTypeId;
	}

	public void setFkByQueTypeId(TsSimpleCode fkByQueTypeId) {
		this.fkByQueTypeId = fkByQueTypeId;
	}
	@ManyToOne
	@JoinColumn(name = "EXAMPOOL_ID")
	public TsProbExampool getFkByExampoolId() {
		return fkByExampoolId;
	}

	public void setFkByExampoolId(TsProbExampool fkByExampoolId) {
		this.fkByExampoolId = fkByExampoolId;
	}
}