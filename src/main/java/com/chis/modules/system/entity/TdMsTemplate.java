package com.chis.modules.system.entity;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * MS模板
 * , schema = "CZRISK"
 * TdMsTemplate entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TD_MS_TEMPLATE", uniqueConstraints = @UniqueConstraint(columnNames = "RECORDID"))
@SequenceGenerator(name="TdMsTemplate_Seq",sequenceName = "TD_MS_TEMPLATE_SEQ",allocationSize = 1)
public class TdMsTemplate implements java.io.Serializable {

	// Fields

	private Integer rid;
	private String fileName;
	private Short fileType;//0: doc 1: docx 2: xls 3: xlsx 4: ppt 5: pptx
	private Integer fileSize;
	private String filePath;
	private String fileDesc;
	private Integer xh;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private String recordid;
	private List<TdMsBookmark> tdMsBookmarks = new ArrayList<TdMsBookmark>();

	// Constructors

	/** default constructor */
	public TdMsTemplate() {
	}

	/** minimal constructor */
	public TdMsTemplate(Integer rid, String fileName, Short fileType,
			String recordid) {
		this.rid = rid;
		this.fileName = fileName;
		this.fileType = fileType;
		this.recordid = recordid;
	}

	/** full constructor */
	public TdMsTemplate(Integer rid, String fileName, Short fileType,
						Integer fileSize, String filePath, String fileDesc,
						Integer xh, Date createDate, Integer createManid,
						Date modifyDate, Integer modifyManid, String recordid) {
		this.rid = rid;
		this.fileName = fileName;
		this.fileType = fileType;
		this.fileSize = fileSize;
		this.filePath = filePath;
		this.fileDesc = fileDesc;
		this.xh = xh;
		this.createDate = createDate;
		this.createManid = createManid;
		this.modifyDate = modifyDate;
		this.modifyManid = modifyManid;
		this.recordid = recordid;
	}

	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdMsTemplate_Seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Column(name = "FILE_NAME" , length = 100)
	public String getFileName() {
		return this.fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	@Column(name = "FILE_TYPE" , precision = 1, scale = 0)
	public Short getFileType() {
		return this.fileType;
	}

	public void setFileType(Short fileType) {
		this.fileType = fileType;
	}

	@Column(name = "FILE_SIZE", precision = 22, scale = 0)
	public Integer getFileSize() {
		return this.fileSize;
	}

	public void setFileSize(Integer fileSize) {
		this.fileSize = fileSize;
	}

	@Column(name = "FILE_PATH", length = 250)
	public String getFilePath() {
		return this.filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	@Column(name = "FILE_DESC", length = 250)
	public String getFileDesc() {
		return this.fileDesc;
	}

	public void setFileDesc(String fileDesc) {
		this.fileDesc = fileDesc;
	}

	@Column(name = "XH", precision = 22, scale = 0)
	public Integer getXh() {
		return this.xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}

	@Column(name = "CREATE_DATE" , length = 11)
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID", precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Column(name = "MODIFY_DATE" , length = 11)
	@Temporal(TemporalType.TIMESTAMP)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Column(name = "RECORDID", unique = true , length = 25)
	public String getRecordid() {
		return this.recordid;
	}

	public void setRecordid(String recordid) {
		this.recordid = recordid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY,  mappedBy = "tdMsTemplate",orphanRemoval = true)
	@OrderBy(value = "xh")
	public List<TdMsBookmark> getTdMsBookmarks() {
		return tdMsBookmarks;
	}

	public void setTdMsBookmarks(List<TdMsBookmark> tdMsBookmarks) {
		this.tdMsBookmarks = tdMsBookmarks;
	}
}