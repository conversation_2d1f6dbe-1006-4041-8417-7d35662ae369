package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * 
 * <AUTHOR>
 * @createTime 2017-10-19
 */
@Entity
@Table(name = "TB_PRO_POOL_COLSDEFINE")
@SequenceGenerator(name = "TbProPoolColsdefine", sequenceName = "TB_PRO_POOL_COLSDEFINE_SEQ", allocationSize = 1)
public class TbProPoolColsdefine implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbProPoolTabdefine fkByTableId;
	private Integer num;
	private Integer rowIndex;
	private Integer colIndex;
	private String colName;
	private String colDesc;
	private Integer colType;
	private Integer cols;
	private Integer rowspan;
	private String colExpr;
	private Integer colLenth;
	private Integer colPrec;
	private Integer colMust;
	private String colDefvalue;
	private Integer scopeCons;
	private String minValue;
	private String maxValue;
	private Integer dsType;
	private String dsCdcode;
	private String dsSql;
	private Date createDate;
	private Integer createManid;
	private Integer state;

	/** +脚本20220927 */
	private String execScript;

	public TbProPoolColsdefine() {
	}

	public TbProPoolColsdefine(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbProPoolColsdefine")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "TABLE_ID")
	public TbProPoolTabdefine getFkByTableId() {
		return fkByTableId;
	}

	public void setFkByTableId(TbProPoolTabdefine fkByTableId) {
		this.fkByTableId = fkByTableId;
	}

	@Column(name = "NUM")
	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	@Column(name = "ROW_INDEX")
	public Integer getRowIndex() {
		return rowIndex;
	}

	public void setRowIndex(Integer rowIndex) {
		this.rowIndex = rowIndex;
	}

	@Column(name = "COL_INDEX")
	public Integer getColIndex() {
		return colIndex;
	}

	public void setColIndex(Integer colIndex) {
		this.colIndex = colIndex;
	}

	@Column(name = "COL_NAME")
	public String getColName() {
		return colName;
	}

	public void setColName(String colName) {
		this.colName = colName;
	}

	@Column(name = "COL_DESC")
	public String getColDesc() {
		return colDesc;
	}

	public void setColDesc(String colDesc) {
		this.colDesc = colDesc;
	}

	@Column(name = "COL_TYPE")
	public Integer getColType() {
		return colType;
	}

	public void setColType(Integer colType) {
		this.colType = colType;
	}

	@Column(name = "COLS")
	public Integer getCols() {
		return cols;
	}

	public void setCols(Integer cols) {
		this.cols = cols;
	}

	@Column(name = "ROWSPAN")
	public Integer getRowspan() {
		return rowspan;
	}

	public void setRowspan(Integer rowspan) {
		this.rowspan = rowspan;
	}

	@Column(name = "COL_EXPR")
	public String getColExpr() {
		return colExpr;
	}

	public void setColExpr(String colExpr) {
		this.colExpr = colExpr;
	}

	@Column(name = "COL_LENTH")
	public Integer getColLenth() {
		return colLenth;
	}

	public void setColLenth(Integer colLenth) {
		this.colLenth = colLenth;
	}

	@Column(name = "COL_PREC")
	public Integer getColPrec() {
		return colPrec;
	}

	public void setColPrec(Integer colPrec) {
		this.colPrec = colPrec;
	}

	@Column(name = "COL_MUST")
	public Integer getColMust() {
		return colMust;
	}

	public void setColMust(Integer colMust) {
		this.colMust = colMust;
	}

	@Column(name = "COL_DEFVALUE")
	public String getColDefvalue() {
		return colDefvalue;
	}

	public void setColDefvalue(String colDefvalue) {
		this.colDefvalue = colDefvalue;
	}

	@Column(name = "SCOPE_CONS")
	public Integer getScopeCons() {
		return scopeCons;
	}

	public void setScopeCons(Integer scopeCons) {
		this.scopeCons = scopeCons;
	}

	@Column(name = "MIN_VALUE")
	public String getMinValue() {
		return minValue;
	}

	public void setMinValue(String minValue) {
		this.minValue = minValue;
	}

	@Column(name = "MAX_VALUE")
	public String getMaxValue() {
		return maxValue;
	}

	public void setMaxValue(String maxValue) {
		this.maxValue = maxValue;
	}

	@Column(name = "DS_TYPE")
	public Integer getDsType() {
		return dsType;
	}

	public void setDsType(Integer dsType) {
		this.dsType = dsType;
	}

	@Column(name = "DS_CDCODE")
	public String getDsCdcode() {
		return dsCdcode;
	}

	public void setDsCdcode(String dsCdcode) {
		this.dsCdcode = dsCdcode;
	}

	@Column(name = "DS_SQL")
	public String getDsSql() {
		return dsSql;
	}

	public void setDsSql(String dsSql) {
		this.dsSql = dsSql;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE")
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID")
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Column(name = "STATE")
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	@Column(name = "EXEC_SCRIPT")
	public String getExecScript() {
		return execScript;
	}

	public void setExecScript(String execScript) {
		this.execScript = execScript;
	}
}