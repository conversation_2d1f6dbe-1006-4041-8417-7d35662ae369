package com.chis.modules.system.entity;
import javax.persistence.*;

import java.util.Date;

/**
 * 子表
 * <AUTHOR>
 * @createTime 2016-3-22
 */
@Entity
@Table(name = "TS_DSF_SYS_PARAM")
@SequenceGenerator(name = "TsDsfSysParam", sequenceName = "TS_DSF_SYS_PARAM_SEQ", allocationSize = 1)
public class TsDsfSysParam implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	/**RID */
	private Integer rid;
	/** 第三方系统ID*/
	private TsDsfSys tsDsfSysId;	
	/** 参数名*/
	private String paramCn;
	/**参数字段名 */
	private String paramEn;
	/**参数描述 */
	private String paramDesc;
	/**创建日期 */
	private Date createDate;
	/** 创建人*/
	private Integer createManid;
	/**修改日期 */
	private Date modifyDate;
	/**修改人 */
	private Integer modifyManid;
	
	private String enValue;
	
	public TsDsfSysParam() {
	}
	
	public TsDsfSysParam(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsDsfSysParam")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
			
			
	@Column(name = "PARAM_CN")	
	public String getParamCn() {
		return paramCn;
	}

	public void setParamCn(String paramCn) {
		this.paramCn = paramCn;
	}	
			
	@Column(name = "PARAM_EN")	
	public String getParamEn() {
		return paramEn;
	}

	public void setParamEn(String paramEn) {
		this.paramEn = paramEn;
	}	
			
	@Column(name = "PARAM_DESC")	
	public String getParamDesc() {
		return paramDesc;
	}

	public void setParamDesc(String paramDesc) {
		this.paramDesc = paramDesc;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	@ManyToOne
	@JoinColumn(name = "DSF_SYS_ID" )
	public TsDsfSys getTsDsfSysId() {
		return tsDsfSysId;
	}

	public void setTsDsfSysId(TsDsfSys tsDsfSysId) {
		this.tsDsfSysId = tsDsfSysId;
	}

	@Transient
	public String getEnValue() {
		return enValue;
	}

	public void setEnValue(String enValue) {
		this.enValue = enValue;
	}

	
			
}