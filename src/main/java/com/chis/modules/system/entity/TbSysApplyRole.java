package com.chis.modules.system.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-7-30
 */
@Entity
@Table(name = "TB_SYS_APPLY_ROLE")
@SequenceGenerator(name = "TbSysApplyRole", sequenceName = "TB_SYS_APPLY_ROLE_SEQ", allocationSize = 1)
public class TbSysApplyRole implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbSysApplyAccount fkByMainId;
	private TsRole fkByRoleId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TbSysApplyRole() {
	}

	public TbSysApplyRole(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbSysApplyRole")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TbSysApplyAccount getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TbSysApplyAccount fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ROLE_ID")			
	public TsRole getFkByRoleId() {
		return fkByRoleId;
	}

	public void setFkByRoleId(TsRole fkByRoleId) {
		this.fkByRoleId = fkByRoleId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}