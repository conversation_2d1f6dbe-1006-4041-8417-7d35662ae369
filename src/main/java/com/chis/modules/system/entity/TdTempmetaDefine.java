package com.chis.modules.system.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @createDate 2015-02-03.
 */
@Entity
@Table(name = "TD_TEMPMETA_DEFINE")
@SequenceGenerator(name = "TdTempmetaDefine_seq", sequenceName = "TD_TEMPMETA_DEFINE_SEQ", allocationSize = 1)
@NamedQueries({
    @NamedQuery(name = "TdTempmetaDefine.findByTypeCode", query = "SELECT t FROM TdTempmetaDefine t WHERE t.tdTempmetaType.tempCode =:tempCode")
})
public class TdTempmetaDefine implements Serializable {
    private static final long serialVersionUID = 7652623361344483132L;

    private Integer rid;
    private TdTempmetaType tdTempmetaType;
    private String tempContent;
    private Short isDefault;
    private Date createDate;
    private Integer createManid;
    
    public TdTempmetaDefine() {
	}
    
	public TdTempmetaDefine(Integer rid) {
		this.rid = rid;
	}
	
	public TdTempmetaDefine(String tempContent, Short isDefault) {
		this.tempContent = tempContent;
		this.isDefault = isDefault;
	}

	@Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTempmetaDefine_seq")
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "TYPE_ID" )
    public TdTempmetaType getTdTempmetaType() {
        return tdTempmetaType;
    }

    public void setTdTempmetaType(TdTempmetaType tdTempmetaType) {
        this.tdTempmetaType = tdTempmetaType;
    }

    @Column(name = "TEMP_CONT")
    public String getTempContent() {
        return tempContent;
    }

    public void setTempContent(String tempContent) {
        this.tempContent = tempContent;
    }

    @Column(name = "IF_DEFAULT")
    public Short getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Short isDefault) {
        this.isDefault = isDefault;
    }

    @Column(name = "CREATE_DATE" )
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }
}
