package com.chis.modules.system.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 描述
 *
 * <AUTHOR>
 * @createdate 2015-8-28
 */
@Entity
@Table(name = "TS_USER_BTN")
@SequenceGenerator(name = "TsUserBtn_seq", sequenceName = "TS_USER_BTN_SEQ", allocationSize = 1)
public class TsUserBtn implements Serializable {
    private static final long serialVersionUID = 7868907580878759026L;

    private Integer rid;
    private TsUserInfo tsUserInfo;
    private TsMenuBtn tsMenuBtn;

    public TsUserBtn() {
    }

    public TsUserBtn(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID", unique = true , precision = 22, scale = 0)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsUserBtn_seq")
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "USER_INFO_ID")
    public TsUserInfo getTsUserInfo() {
        return tsUserInfo;
    }

    public void setTsUserInfo(TsUserInfo tsUserInfo) {
        this.tsUserInfo = tsUserInfo;
    }

    @ManyToOne
    @JoinColumn(name = "BTN_ID")
    public TsMenuBtn getTsMenuBtn() {
        return tsMenuBtn;
    }

    public void setTsMenuBtn(TsMenuBtn tsMenuBtn) {
        this.tsMenuBtn = tsMenuBtn;
    }
}