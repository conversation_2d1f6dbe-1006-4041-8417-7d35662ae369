package com.chis.modules.system.entity;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.system.enumn.MessageType;

/**
 * 消息主表
 * 
 * <AUTHOR>
 * @history 2014-07-14
 */
@Entity
@Table(name = "TD_MSG_MAIN")
@SequenceGenerator(name = "TdMsgMain_seq", sequenceName = "TD_MSG_MAIN_SEQ", allocationSize = 1)
public class TdMsgMain implements java.io.Serializable {

	private static final long serialVersionUID = -7714195687544022101L;
	private Integer rid;
	private TsUserInfo tsUserInfo;// 发布人
	private TdInfoMain tdInfoMain;// 寻呼消息ID
	private MessageType messageType;// 消息类型
	private String infoTitle;// 标题
	private Date publishTime = new Date();// 发布时间
	private String netAdr;// 链接地址
	private String netName;// 链接名称
	private List<TdMsgSub> tdMsgSubs = new LinkedList<TdMsgSub>();// 消息子表

	private String appendKeys;// 附件内容
	private Integer subType;// 消息类别
	/** 菜单ID */
	private Integer menuId;
	/** 是否待办任务 */
	private Short isTodo;
	/** 待办任务处理状态 */
	private Short todoState;
	/**业务主键*/
	private Integer busId;

	public TdMsgMain() {
	}

	public TdMsgMain(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdMsgMain_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "PUBLISH_MAN" )
	public TsUserInfo getTsUserInfo() {
		return this.tsUserInfo;
	}

	public void setTsUserInfo(TsUserInfo tsUserInfo) {
		this.tsUserInfo = tsUserInfo;
	}

	@ManyToOne
	@JoinColumn(name = "INFO_ID")
	public TdInfoMain getTdInfoMain() {
		return this.tdInfoMain;
	}

	public void setTdInfoMain(TdInfoMain tdInfoMain) {
		this.tdInfoMain = tdInfoMain;
	}

	@Enumerated
	@Column(name = "MSG_TYPE")
	public MessageType getMessageType() {
		return messageType;
	}

	public void setMessageType(MessageType messageType) {
		this.messageType = messageType;
	}

	@Column(name = "INFO_TITLE" , length = 200)
	public String getInfoTitle() {
		return this.infoTitle;
	}

	public void setInfoTitle(String infoTitle) {
		this.infoTitle = infoTitle;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "PUBLISH_TIME" , length = 7)
	public Date getPublishTime() {
		return this.publishTime;
	}

	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}

	@Column(name = "NET_ADR", length = 500)
	public String getNetAdr() {
		return this.netAdr;
	}

	public void setNetAdr(String netAdr) {
		this.netAdr = netAdr;
	}

	@Column(name = "NET_NAME", length = 50)
	public String getNetName() {
		return this.netName;
	}

	public void setNetName(String netName) {
		this.netName = netName;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdMsgMain")
	public List<TdMsgSub> getTdMsgSubs() {
		return this.tdMsgSubs;
	}

	public void setTdMsgSubs(List<TdMsgSub> tdMsgSubs) {
		this.tdMsgSubs = tdMsgSubs;
	}

	@Column(name = "APPEND_KEYS")
	public String getAppendKeys() {
		return appendKeys;
	}

	public void setAppendKeys(String appendKeys) {
		this.appendKeys = appendKeys;
	}

	@Column(name = "SUB_TYPE")
	public Integer getSubType() {
		return subType;
	}

	public void setSubType(Integer subType) {
		this.subType = subType;
	}

	@Column(name = "MENU_ID")
	public Integer getMenuId() {
		return menuId;
	}

	public void setMenuId(Integer menuId) {
		this.menuId = menuId;
	}

	@Column(name = "IS_TODO", length = 1, scale=0)
	public Short getIsTodo() {
		return isTodo;
	}

	public void setIsTodo(Short isTodo) {
		this.isTodo = isTodo;
	}

	@Column(name = "TODO_STATE", length = 1, scale=0)
	public Short getTodoState() {
		return todoState;
	}

	public void setTodoState(Short todoState) {
		this.todoState = todoState;
	}
	@Column(name = "BUS_ID")
	public Integer getBusId() {
		return busId;
	}

	public void setBusId(Integer busId) {
		this.busId = busId;
	}

}