package com.chis.modules.system.entity;

import org.hibernate.validator.constraints.NotEmpty;

import javax.faces.model.SelectItem;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * TsMenu entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_MENU")
@SequenceGenerator(name = "TsMenu_seq", sequenceName = "TS_MENU_SEQ", allocationSize = 1)
@NamedQueries({
        @NamedQuery(name = "TsMenu.findByMenuEn", query = "SELECT new TsMenu(t.rid, t.menuEn) FROM TsMenu t WHERE t.menuEn=:menuEn"),
        @NamedQuery(name = "TsMenu.findFullFieldByMenuEn", query = "SELECT t FROM TsMenu t WHERE t.menuEn=:menuEn")
})
public class TsMenu implements java.io.Serializable {

    private static final long serialVersionUID = -4483560261407449035L;
    private Integer rid;
    private String menuLevelNo;
    private String menuCn;
    private String menuEn;
    private String menuSimple;
    private Short isfunc;
    private String menuUri;
    private String menuIcon;
    private String bigIcon;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private Integer num;
    private String ipAddr;
    private Set<TsUserMenu> tsUserMenus = new HashSet<TsUserMenu>(0);
    private Set<TsRoleMenu> tsRoleMenus = new HashSet<TsRoleMenu>(0);
    private Set<TsUserDesk> tsUserDesks = new HashSet<TsUserDesk>(0);
    private Integer levelNum;//级别号长度
    //按钮
    private String[] btns;
    private List<SelectItem> allbtns;
    private boolean selected;
    private boolean tempselected;
    private Integer ifPop = 0;
    private Integer state;
    private String errRsn;

    public TsMenu() {
    }


    public TsMenu(Integer rid) {
        this.rid = rid;
    }

    /**
     * 不允许修改！！！
     *
     * @param menuEn 菜单编码
     */
    public TsMenu(String menuEn) {
        this.menuEn = menuEn;
    }

    /**
     * 不允许修改！！！
     *
     * @param menuEn 菜单编码
     */
    public TsMenu(Integer rid, String menuEn) {
        this.rid = rid;
        this.menuEn = menuEn;
    }

    /**
     * 不允许修改！！！
     *
     * @param rid         主键
     * @param menuLevelNo 层级编码
     * @param menuCn      菜单中文名称
     */
    public TsMenu(Integer rid, String menuLevelNo, String menuCn) {
        this.rid = rid;
        this.menuLevelNo = menuLevelNo;
        this.menuCn = menuCn;
    }

    public TsMenu(Integer rid, String menuLevelNo, String menuCn, Short isfunc) {
        this.rid = rid;
        this.menuLevelNo = menuLevelNo;
        this.menuCn = menuCn;
        this.isfunc = isfunc;
    }
    
    public TsMenu(Integer rid, String menuLevelNo, String menuCn, Short isfunc, String menuEn) {
        this.rid = rid;
        this.menuLevelNo = menuLevelNo;
        this.menuCn = menuCn;
        this.isfunc = isfunc;
        this.menuEn = menuEn;
    }

    public TsMenu(Integer rid, String menuLevelNo, String menuCn, Short isfunc, String menuEn, String menuUri) {
        this.rid = rid;
        this.menuLevelNo = menuLevelNo;
        this.menuCn = menuCn;
        this.isfunc = isfunc;
        this.menuEn = menuEn;
        this.menuUri = menuUri;
    }
    
    public TsMenu(Integer rid, String menuLevelNo, String menuCn, String menuSimple, Short isfunc, String menuIcon, Date createDate, Integer createManid) {
        this.rid = rid;
        this.menuLevelNo = menuLevelNo;
        this.menuCn = menuCn;
        this.menuSimple = menuSimple;
        this.isfunc = isfunc;
        this.menuIcon = menuIcon;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    /**
     * 不允许修改！！！
     * 菜单升级初始化的对象
     *
     * @param menuLevelNo 层级编码
     * @param menuCn      菜单中文名称
     * @param menuEn      菜单编码（唯一键）
     * @param menuSimple  菜单简称
     * @param isfunc      是否是功能菜单（0-否 1-是）
     * @param menuUri     菜单地址
     * @param menuIcon    菜单图标
     * @param createDate  菜单创建日期
     * @param createManid 菜单创建人（默认1-超管）
     * @param num         序号
     */
    public TsMenu(String menuLevelNo, String menuCn, String menuEn, String menuSimple, Short isfunc, String menuUri, String menuIcon, Date createDate, Integer createManid, Integer num) {
        this.menuLevelNo = menuLevelNo;
        this.menuCn = menuCn;
        this.menuEn = menuEn;
        this.menuSimple = menuSimple;
        this.isfunc = isfunc;
        this.menuUri = menuUri;
        this.menuIcon = menuIcon;
        this.createDate = createDate;
        this.createManid = createManid;
        this.num = num;
    }

    /**
     * 不允许修改！！！
     * 菜单升级初始化的对象
     *
     * @param menuLevelNo 层级编码
     * @param menuCn      菜单中文名称
     * @param menuEn      菜单编码（唯一键）
     * @param menuSimple  菜单简称
     * @param isfunc      是否是功能菜单（0-否 1-是）
     * @param menuUri     菜单地址
     * @param menuIcon    菜单图标
     * @param bigIcon     大菜单图标
     * @param createDate  菜单创建日期
     * @param createManid 菜单创建人（默认1-超管）
     * @param num         序号
     */
    public TsMenu(String menuLevelNo, String menuCn, String menuEn, String menuSimple, Short isfunc, String menuUri, String menuIcon, String bigIcon, Date createDate, Integer createManid, Integer num) {
        this.menuLevelNo = menuLevelNo;
        this.menuCn = menuCn;
        this.menuEn = menuEn;
        this.menuSimple = menuSimple;
        this.isfunc = isfunc;
        this.menuUri = menuUri;
        this.menuIcon = menuIcon;
        this.createDate = createDate;
        this.createManid = createManid;
        this.num = num;
        this.bigIcon = bigIcon;
    }

    /**
     * full constructor
     */
    public TsMenu(Integer rid, String menuLevelNo, String menuCn, String menuEn, String menuSimple, Short isfunc, String menuUri, String menuIcon, Date createDate, Integer createManid,
                  Date modifyDate, Integer modifyManid, Set<TsUserMenu> tsUserMenus, Set<TsRoleMenu> tsRoleMenus, Set<TsUserDesk> tsUserDesks) {
        this.rid = rid;
        this.menuLevelNo = menuLevelNo;
        this.menuCn = menuCn;
        this.menuEn = menuEn;
        this.menuSimple = menuSimple;
        this.isfunc = isfunc;
        this.menuUri = menuUri;
        this.menuIcon = menuIcon;
        this.createDate = createDate;
        this.createManid = createManid;
        this.modifyDate = modifyDate;
        this.modifyManid = modifyManid;
        this.tsUserMenus = tsUserMenus;
        this.tsRoleMenus = tsRoleMenus;
        this.tsUserDesks = tsUserDesks;
    }

    @Id
    @Column(name = "RID", unique = true , precision = 22, scale = 0)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsMenu_seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @NotEmpty(message = "结构层次编码不允许为空！")
    @Column(name = "MENU_LEVEL_NO", unique = true , length = 30)
    public String getMenuLevelNo() {
        return this.menuLevelNo;
    }

    public void setMenuLevelNo(String menuLevelNo) {
        this.menuLevelNo = menuLevelNo;
    }

    @NotEmpty(message = "菜单名称不允许为空！")
    @Column(name = "MENU_CN" , length = 60)
    public String getMenuCn() {
        return this.menuCn;
    }

    public void setMenuCn(String menuCn) {
        this.menuCn = menuCn;
    }

    @Column(name = "MENU_EN", length = 60)
    public String getMenuEn() {
        return this.menuEn;
    }

    public void setMenuEn(String menuEn) {
        this.menuEn = menuEn;
    }

    @NotEmpty(message = "菜单简称不允许为空！")
    @Column(name = "MENU_SIMPLE" , length = 60)
    public String getMenuSimple() {
        return this.menuSimple;
    }

    public void setMenuSimple(String menuSimple) {
        this.menuSimple = menuSimple;
    }

    @NotNull(message = "请选择是否功能菜单！")
    @Column(name = "ISFUNC" , precision = 1, scale = 0)
    public Short getIsfunc() {
        return this.isfunc;
    }

    public void setIsfunc(Short isfunc) {
        this.isfunc = isfunc;
    }

    @Column(name = "MENU_URI", length = 200)
    public String getMenuUri() {
        return this.menuUri;
    }

    public void setMenuUri(String menuUri) {
        this.menuUri = menuUri;
    }

    @NotEmpty(message = "菜单小图标不允许为空！")
    @Column(name = "MENU_ICON" , length = 200)
    public String getMenuIcon() {
        return this.menuIcon;
    }

    public void setMenuIcon(String menuIcon) {
        this.menuIcon = menuIcon;
    }

    @Column(name = "BIG_ICON", length = 200)
    public String getBigIcon() {
        return bigIcon;
    }

    public void setBigIcon(String bigIcon) {
        this.bigIcon = bigIcon;
    }

    @Column(name = "CREATE_DATE" , length = 11)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" , precision = 22, scale = 0)
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Column(name = "MODIFY_DATE", length = 11)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID", precision = 22, scale = 0)
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Column(name = "NUM")
    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsMenu")
    public Set<TsUserMenu> getTsUserMenus() {
        return this.tsUserMenus;
    }

    public void setTsUserMenus(Set<TsUserMenu> tsUserMenus) {
        this.tsUserMenus = tsUserMenus;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsMenu")
    public Set<TsRoleMenu> getTsRoleMenus() {
        return this.tsRoleMenus;
    }

    public void setTsRoleMenus(Set<TsRoleMenu> tsRoleMenus) {
        this.tsRoleMenus = tsRoleMenus;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsMenu")
    public Set<TsUserDesk> getTsUserDesks() {
        return this.tsUserDesks;
    }

    public void setTsUserDesks(Set<TsUserDesk> tsUserDesks) {
        this.tsUserDesks = tsUserDesks;
    }

    @Transient
    public Integer getLevelNum() {
        return levelNum;
    }

    public void setLevelNum(Integer levelNum) {
        this.levelNum = levelNum;
    }

    @Transient
    public String[] getBtns() {
        return btns;
    }

    public void setBtns(String[] btns) {
        this.btns = btns;
    }

    @Transient
    public List<SelectItem> getAllbtns() {
        return allbtns;
    }

    public void setAllbtns(List<SelectItem> allbtns) {
        this.allbtns = allbtns;
    }

    @Transient
    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    @Transient
    public boolean isTempselected() {
        return tempselected;
    }

    public void setTempselected(boolean tempselected) {
        this.tempselected = tempselected;
    }

    @Column(name = "IP_ADDR")
	public String getIpAddr() {
		return ipAddr;
	}

    @Column(name = "IF_POP")
    public Integer getIfPop() {
        return ifPop;
    }

    public void setIfPop(Integer ifPop) {
        this.ifPop = ifPop;
    }
    @Column(name = "STATE")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
    @Column(name = "ERR_RSN")
    public String getErrRsn() {
        return errRsn;
    }

    public void setErrRsn(String errRsn) {
        this.errRsn = errRsn;
    }

    public void setIpAddr(String ipAddr) {
		this.ipAddr = ipAddr;
	}
}