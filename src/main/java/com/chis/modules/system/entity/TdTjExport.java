package com.chis.modules.system.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-12-15
 */
@Entity
@Table(name = "TD_TJ_EXPORT")
@SequenceGenerator(name = "TdTjExport", sequenceName = "TD_TJ_EXPORT_SEQ", allocationSize = 1)
public class TdTjExport implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsSimpleCode fkByBusTypeId;
	private String exportCondition;
	private String exportConditionShow;
	private Date exportDate;
	private Integer state;
	private TsUnit fkByOperUnitId;
	private TsUserInfo fkByOperPsnId;
	private String exportFileName;
	private String exportFilePath;
	private Date exportFileDate;
	private String errorMsg;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private Integer moduleType;

	public TdTjExport() {
	}

	public TdTjExport(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjExport")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "BUS_TYPE_ID")			
	public TsSimpleCode getFkByBusTypeId() {
		return fkByBusTypeId;
	}

	public void setFkByBusTypeId(TsSimpleCode fkByBusTypeId) {
		this.fkByBusTypeId = fkByBusTypeId;
	}	
			
	@Column(name = "EXPORT_CONDITION")	
	public String getExportCondition() {
		return exportCondition;
	}

	public void setExportCondition(String exportCondition) {
		this.exportCondition = exportCondition;
	}	
			
	@Column(name = "EXPORT_CONDITION_SHOW")	
	public String getExportConditionShow() {
		return exportConditionShow;
	}

	public void setExportConditionShow(String exportConditionShow) {
		this.exportConditionShow = exportConditionShow;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EXPORT_DATE")			
	public Date getExportDate() {
		return exportDate;
	}

	public void setExportDate(Date exportDate) {
		this.exportDate = exportDate;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@ManyToOne
	@JoinColumn(name = "OPER_UNIT_ID")			
	public TsUnit getFkByOperUnitId() {
		return fkByOperUnitId;
	}

	public void setFkByOperUnitId(TsUnit fkByOperUnitId) {
		this.fkByOperUnitId = fkByOperUnitId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "OPER_PSN_ID")			
	public TsUserInfo getFkByOperPsnId() {
		return fkByOperPsnId;
	}

	public void setFkByOperPsnId(TsUserInfo fkByOperPsnId) {
		this.fkByOperPsnId = fkByOperPsnId;
	}	
			
	@Column(name = "EXPORT_FILE_NAME")	
	public String getExportFileName() {
		return exportFileName;
	}

	public void setExportFileName(String exportFileName) {
		this.exportFileName = exportFileName;
	}	
			
	@Column(name = "EXPORT_FILE_PATH")	
	public String getExportFilePath() {
		return exportFilePath;
	}

	public void setExportFilePath(String exportFilePath) {
		this.exportFilePath = exportFilePath;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "EXPORT_FILE_DATE")			
	public Date getExportFileDate() {
		return exportFileDate;
	}

	public void setExportFileDate(Date exportFileDate) {
		this.exportFileDate = exportFileDate;
	}	
			
	@Column(name = "ERROR_MSG")	
	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Column(name = "MODULE_TYPE")
	public Integer getModuleType() {
		return moduleType;
	}

	public void setModuleType(Integer moduleType) {
		this.moduleType = moduleType;
	}
}