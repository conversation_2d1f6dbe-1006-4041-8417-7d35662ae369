package com.chis.modules.system.entity;

import javax.persistence.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2017-10-19
 */
@Entity
@Table(name = "TB_PROB_TABDEFINE")
@SequenceGenerator(name = "TbProbTabdefine", sequenceName = "TB_PROB_TABDEFINE_SEQ", allocationSize = 1)
public class TbProbTabdefine implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String tabName;
	private String rmk;
	private Date createDate;
	private Integer createManid;
	private Integer rowFixed;
	private Integer defaultLineNum;
	/** 列定义 */
	private List<TbProbColsdefine> colsdefines = new ArrayList<>(0);
	/** 行标题 */
	private List<TbProbRowtitle> rowtitles = new ArrayList<>(0);

	public TbProbTabdefine() {
	}

	public TbProbTabdefine(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbProbTabdefine")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Column(name = "TAB_NAME")
	public String getTabName() {
		return tabName;
	}

	public void setTabName(String tabName) {
		this.tabName = tabName;
	}

	@Column(name = "RMK")
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE")
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID")
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Column(name = "ROW_FIXED")
	public Integer getRowFixed() {
		return rowFixed;
	}

	public void setRowFixed(Integer rowFixed) {
		this.rowFixed = rowFixed;
	}

	@Column(name = "DEFAULT_LINE_NUM")
	public Integer getDefaultLineNum() {
		return defaultLineNum;
	}

	public void setDefaultLineNum(Integer defaultLineNum) {
		this.defaultLineNum = defaultLineNum;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByTableId", orphanRemoval = true)
	public List<TbProbColsdefine> getColsdefines() {
		return colsdefines;
	}

	public void setColsdefines(List<TbProbColsdefine> colsdefines) {
		this.colsdefines = colsdefines;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByTableId", orphanRemoval = true)
	public List<TbProbRowtitle> getRowtitles() {
		return rowtitles;
	}

	public void setRowtitles(List<TbProbRowtitle> rowtitles) {
		this.rowtitles = rowtitles;
	}

}