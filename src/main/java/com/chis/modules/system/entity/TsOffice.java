package com.chis.modules.system.entity;

import org.hibernate.validator.constraints.NotEmpty;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

/**
 * TsOffice entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_OFFICE", uniqueConstraints = @UniqueConstraint(columnNames = "OFFICECODE"))
@SequenceGenerator(name = "TsOffice_seq", sequenceName = "TS_OFFICE_SEQ", allocationSize = 1)
public class TsOffice implements java.io.Serializable {

	private static final long serialVersionUID = 1570700794649256588L;
	private Integer rid;
	private TsUnit tsUnit;
	private TbSysEmp tbSysEmpByDeptLeaderId;
	private TbSysEmp tbSysEmpByManageManid;
	private TsOffice parentOffice;
	private Short num;
	private String officecode;
	private String officename;
	private String simplName;
	private Short officetype;
	private String officetel;
	private String officefax;
	private String splsht;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private Short ifReveal;
    private Short isYjOffice;
	private Set<TbSysEmp> tbSysEmps = new HashSet<TbSysEmp>(0);
	private Set<TsOffice> childOfficeSet = new HashSet<TsOffice>(0);
    private Integer paddingLeftSize;//前边距大小
    
    /**是否被选中*/
    private boolean selected = Boolean.FALSE;

	public TsOffice() {
	}

    public TsOffice(Integer rid) {
        this.rid = rid;
    }

    public TsOffice(Integer rid, String officename) {
        this.rid = rid;
        this.officename = officename;
    }
    
    public TsOffice(Integer rid, String officename,String officecode,Short num) {
        this.rid = rid;
        this.officename = officename;
        this.officecode=officecode;
        this.num=num;
    }

    public TsOffice(Integer rid, TsUnit tsUnit, String officecode,
			String officename, String simplName, Short officetype,
			Date createDate, Integer createManid, Short ifReveal) {
		this.rid = rid;
		this.tsUnit = tsUnit;
		this.officecode = officecode;
		this.officename = officename;
		this.simplName = simplName;
		this.officetype = officetype;
		this.createDate = createDate;
		this.createManid = createManid;
		this.ifReveal = ifReveal;
	}


	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsOffice_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "UNIT_RID" )
	public TsUnit getTsUnit() {
		return this.tsUnit;
	}

	public void setTsUnit(TsUnit tsUnit) {
		this.tsUnit = tsUnit;
	}

	@ManyToOne
	@JoinColumn(name = "DEPT_LEADER_ID")
	public TbSysEmp getTbSysEmpByDeptLeaderId() {
		return this.tbSysEmpByDeptLeaderId;
	}

	public void setTbSysEmpByDeptLeaderId(TbSysEmp tbSysEmpByDeptLeaderId) {
		this.tbSysEmpByDeptLeaderId = tbSysEmpByDeptLeaderId;
	}

	@ManyToOne
	@JoinColumn(name = "MANAGE_MANID")
	public TbSysEmp getTbSysEmpByManageManid() {
		return this.tbSysEmpByManageManid;
	}

	public void setTbSysEmpByManageManid(TbSysEmp tbSysEmpByManageManid) {
		this.tbSysEmpByManageManid = tbSysEmpByManageManid;
	}

	@Column(name = "NUM", precision = 4, scale = 0)
	public Short getNum() {
		return this.num;
	}

	public void setNum(Short num) {
		this.num = num;
	}

    @NotEmpty(message = "科室编码不能为空！")
	@Column(name = "OFFICECODE", unique = true , length = 20)
	public String getOfficecode() {
		return this.officecode;
	}

	public void setOfficecode(String officecode) {
		this.officecode = officecode;
	}

    @NotEmpty(message = "科室名称不能为空！")
	@Column(name = "OFFICENAME" , length = 50)
	public String getOfficename() {
		return this.officename;
	}

	public void setOfficename(String officename) {
		this.officename = officename;
	}

    @NotEmpty(message = "科室简称不能为空！")
	@Column(name = "SIMPL_NAME" , length = 50)
	public String getSimplName() {
		return this.simplName;
	}

	public void setSimplName(String simplName) {
		this.simplName = simplName;
	}

    @NotNull(message = "科室类型不能为空！")
	@Column(name = "OFFICETYPE" , precision = 1, scale = 0)
	public Short getOfficetype() {
		return this.officetype;
	}

	public void setOfficetype(Short officetype) {
		this.officetype = officetype;
	}

	@Column(name = "OFFICETEL", length = 50)
	public String getOfficetel() {
		return this.officetel;
	}

	public void setOfficetel(String officetel) {
		this.officetel = officetel;
	}

	@Column(name = "OFFICEFAX", length = 50)
	public String getOfficefax() {
		return this.officefax;
	}

	public void setOfficefax(String officefax) {
		this.officefax = officefax;
	}

	@Column(name = "SPLSHT", length = 100)
	public String getSplsht() {
		return this.splsht;
	}

	public void setSplsht(String splsht) {
		this.splsht = splsht;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Column(name = "IF_REVEAL" , precision = 1, scale = 0)
	public Short getIfReveal() {
		return this.ifReveal;
	}

	public void setIfReveal(Short ifReveal) {
		this.ifReveal = ifReveal;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsOffice")
	public Set<TbSysEmp> getTbSysEmps() {
		return this.tbSysEmps;
	}

	public void setTbSysEmps(Set<TbSysEmp> tbSysEmps) {
		this.tbSysEmps = tbSysEmps;
	}

	@ManyToOne
	@JoinColumn(name = "UP_ID")
	public TsOffice getParentOffice() {
		return parentOffice;
	}

	public void setParentOffice(TsOffice parentOffice) {
		this.parentOffice = parentOffice;
	}

	@OneToMany(cascade ={ CascadeType.MERGE,CascadeType.PERSIST,CascadeType.REFRESH}, fetch = FetchType.LAZY, mappedBy = "parentOffice")
	public Set<TsOffice> getChildOfficeSet() {
		return childOfficeSet;
	}
	
    @Transient
	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}

	public void setChildOfficeSet(Set<TsOffice> childOfficeSet) {
		this.childOfficeSet = childOfficeSet;
	}

    @Transient
    public Integer getPaddingLeftSize() {
        return paddingLeftSize;
    }

    public void setPaddingLeftSize(Integer paddingLeftSize) {
        this.paddingLeftSize = paddingLeftSize;
    }

    @Column(name = "IS_YJOFFICE" ,precision = 1, scale = 0)
    public Short getIsYjOffice() {
        return isYjOffice;
    }

    public void setIsYjOffice(Short isYjOffice) {
        this.isYjOffice = isYjOffice;
    }
}