package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "TS_BS_SORT")
@SequenceGenerator(name = "TsBsSort_seq", sequenceName = "TS_BS_SORT_SEQ", allocationSize = 1)
public class TsBsSort implements java.io.Serializable {

	private static final long serialVersionUID = -5248581618549956130L;
	private Integer rid;
    private Short paramType;
	private String sortCode;
	private String sortName;
	private String levelCode;
	private Date createDate;
	private Integer createManid;

	public TsBsSort() {
	}

    public TsBsSort(Integer rid) {
        this.rid = rid;
    }

    public TsBsSort(String sortName, Integer rid) {
        this.sortName = sortName;
        this.rid = rid;
    }

    /**
     * @param sortCode 编码
     * @param sortName 名称
     * @param levelCode 层级编码
     * @param createDate 创建日期
     * @param createManid 创建人
     */
    public TsBsSort(String sortCode, String sortName, String levelCode, Date createDate, Integer createManid) {
        this.sortCode = sortCode;
        this.sortName = sortName;
        this.levelCode = levelCode;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    /**
     * @param paramType 系统类型，参照枚举类SystemType
     * @param sortCode 编码
     * @param sortName 名称
     * @param levelCode 层级编码
     * @param createDate 创建日期
     * @param createManid 创建人
     */
    public TsBsSort(Short paramType, String sortCode, String sortName, String levelCode, Date createDate, Integer createManid) {
        this.paramType = paramType;
        this.sortCode = sortCode;
        this.sortName = sortName;
        this.levelCode = levelCode;
        this.createDate = createDate;
        this.createManid = createManid;
    }



    @Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsBsSort_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

    @Column(name = "PARAM_TYPE")
    public Short getParamType() {
        return paramType;
    }

    public void setParamType(Short paramType) {
        this.paramType = paramType;
    }
	
	@Column(name = "SORT_CODE")
	public String getSortCode() {
		return sortCode;
	}

	public void setSortCode(String sortCode) {
		this.sortCode = sortCode;
	}

	@Column(name = "SORT_NAME")
	public String getSortName() {
		return sortName;
	}

	public void setSortName(String sortName) {
		this.sortName = sortName;
	}

	@Column(name = "LEVEL_CODE")
	public String getLevelCode() {
		return levelCode;
	}

	public void setLevelCode(String levelCode) {
		this.levelCode = levelCode;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID")
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

}