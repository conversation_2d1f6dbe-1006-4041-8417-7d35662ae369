package com.chis.modules.system.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Entity
@Table(name = "TS_DSF_LOGINF")
@SequenceGenerator(name = "TsDsfLoginf_seq", sequenceName = "TS_DSF_LOGINF_SEQ", allocationSize = 1)
public class TsDsfLoginf implements java.io.Serializable{

	private static final long serialVersionUID = 4306605951458472824L;

	private Integer rid;
	private TsUserInfo tsUserInfo;
	private TsDsfSys tsDsfSys;
	private String sysUrl;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	private List<TsDsfLoginfParam> list =new ArrayList<TsDsfLoginfParam>();
	
	public TsDsfLoginf(){
		
	}

	public TsDsfLoginf(Integer rid){
		this.rid=rid;
	}
	
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsDsfLoginf_seq")
	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "USER_ID" )
	public TsUserInfo getTsUserInfo() {
		return tsUserInfo;
	}

	public void setTsUserInfo(TsUserInfo tsUserInfo) {
		this.tsUserInfo = tsUserInfo;
	}

	@ManyToOne
	@JoinColumn(name = "DSF_SYS_ID" )
	public TsDsfSys getTsDsfSys() {
		return tsDsfSys;
	}

	public void setTsDsfSys(TsDsfSys tsDsfSys) {
		this.tsDsfSys = tsDsfSys;
	}

	@Column(name = "SYS_URL" , length = 200)
	public String getSysUrl() {
		return sysUrl;
	}

	public void setSysUrl(String sysUrl) {
		this.sysUrl = sysUrl;
	}

	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsDsfLoginfId",orphanRemoval = true)
	public List<TsDsfLoginfParam> getList() {
		return list;
	}

	public void setList(List<TsDsfLoginfParam> list) {
		this.list = list;
	}
}
