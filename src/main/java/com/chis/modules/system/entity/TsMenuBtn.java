package com.chis.modules.system.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;

/**
 * 描述
 *
 * <AUTHOR>
 * @createdate 2015-8-28
 */
@Entity
@Table(name = "TS_MENU_BTN")
@SequenceGenerator(name = "TsMenuBtn_seq", sequenceName = "TS_MENU_BTN_SEQ", allocationSize = 1)
@NamedQueries({
        @NamedQuery(name = "TsMenuBtn.findByCode", query = "SELECT t FROM TsMenuBtn t WHERE t.btnCode=:buttoncode")
})
public class TsMenuBtn implements Serializable {
    private static final long serialVersionUID = -6995046256327651359L;

    private Integer rid;
    private TsMenu tsMenu;
    private String btnCode;
    private String btnName;
    private String levelno;
    private Short ifReveal;
    private String menucode;

    public TsMenuBtn() {
    }

    public TsMenuBtn(Integer rid) {
        this.rid = rid;
    }

    public TsMenuBtn(TsMenu tsMenu, String btnCode, String btnName, String levelno, Short ifReveal) {
        this.tsMenu = tsMenu;
        this.btnCode = btnCode;
        this.btnName = btnName;
        this.levelno = levelno;
        this.ifReveal = ifReveal;
    }

    public TsMenuBtn(String menucode, String btnCode, String btnName) {
        this.menucode = menucode;
        this.btnCode = btnCode;
        this.btnName = btnName;
    }

    @Id
    @Column(name = "RID", unique = true , precision = 22, scale = 0)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsMenuBtn_seq")
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MENU_TEMPLATE_ID")
    public TsMenu getTsMenu() {
        return tsMenu;
    }

    public void setTsMenu(TsMenu tsMenu) {
        this.tsMenu = tsMenu;
    }

    @Column(name = "BTN_CODE")
    public String getBtnCode() {
        return btnCode;
    }

    public void setBtnCode(String btnCode) {
        this.btnCode = btnCode;
    }

    @Column(name = "BTN_NAME")
    public String getBtnName() {
        return btnName;
    }

    public void setBtnName(String btnName) {
        this.btnName = btnName;
    }

    @Column(name = "LEVEL_NO")
    public String getLevelno() {
        return levelno;
    }

    public void setLevelno(String levelno) {
        this.levelno = levelno;
    }

    @Column(name = "IF_REVEAL")
    public Short getIfReveal() {
        return ifReveal;
    }

    public void setIfReveal(Short ifReveal) {
        this.ifReveal = ifReveal;
    }

    @Transient
    public String getMenucode() {
        return menucode;
    }

    public void setMenucode(String menucode) {
        this.menucode = menucode;
    }
}