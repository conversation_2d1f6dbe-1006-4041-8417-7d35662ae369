package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * 
 * <AUTHOR>
 * @createTime 2017-4-12
 */
@Entity
@Table(name = "TS_UNIT_REL")
@SequenceGenerator(name = "TsUnitRel", sequenceName = "TS_UNIT_REL_SEQ", allocationSize = 1)
public class TsUnitRel implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsUnit fkByUnitId;
	private TsUnit fkByRelUnitId;
	private Integer relType;
	private Date beginTime;
	private Date endTime;
	
	public TsUnitRel() {
	}

	public TsUnitRel(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsUnitRel")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "UNIT_ID")			
	public TsUnit getFkByUnitId() {
		return fkByUnitId;
	}

	public void setFkByUnitId(TsUnit fkByUnitId) {
		this.fkByUnitId = fkByUnitId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "REL_UNIT_ID")			
	public TsUnit getFkByRelUnitId() {
		return fkByRelUnitId;
	}

	public void setFkByRelUnitId(TsUnit fkByRelUnitId) {
		this.fkByRelUnitId = fkByRelUnitId;
	}	
			
	@Column(name = "REL_TYPE")	
	public Integer getRelType() {
		return relType;
	}

	public void setRelType(Integer relType) {
		this.relType = relType;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "BEGIN_TIME")			
	public Date getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "END_TIME")			
	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}	
			
}