package com.chis.modules.system.entity;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "TS_SYS_IDS")
@SequenceGenerator(name = "TsSysIds", sequenceName = "TS_SYS_IDS_SEQ", allocationSize = 1)
public class TsSysIds implements java.io.Serializable {

	private Integer rid;
	private TsCodeRule tsCodeRule;
	private String pfxid;
	private Integer num;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

	public TsSysIds() {
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsSysIds")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "RULE_ID" )
	public TsCodeRule getTsCodeRule() {
		return this.tsCodeRule;
	}

	public void setTsCodeRule(TsCodeRule tsCodeRule) {
		this.tsCodeRule = tsCodeRule;
	}

	@Column(name = "PFXID" , length = 50)
	public String getPfxid() {
		return this.pfxid;
	}

	public void setPfxid(String pfxid) {
		this.pfxid = pfxid;
	}

	@Column(name = "NUM" , precision = 22, scale = 0)
	public Integer getNum() {
		return this.num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

    @Column(name = "CREATE_DATE" , length = 11)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" , precision = 22, scale = 0)
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Column(name = "MODIFY_DATE", length = 11)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID", precision = 22, scale = 0)
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

}