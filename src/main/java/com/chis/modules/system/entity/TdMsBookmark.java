package com.chis.modules.system.entity;

import javax.persistence.*;

import java.util.Date;

/**
 * MS标签
 * TdMsBookmark entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TD_MS_BOOKMARK", uniqueConstraints = @UniqueConstraint(columnNames = {
        "BOOKMARK_NAME", "TEMPLATE_ID"}))
@SequenceGenerator(name="TdMsBookmark_Seq",sequenceName = "TD_MS_BOOKMARK_SEQ",allocationSize = 1)
@NamedQueries({
    @NamedQuery(name = "TdMsBookmark.findByRecordId", 
    		query = "SELECT new TdMsBookmark(t.rid, t.bookmarkName, t.bookmarkDesc, t.bookmarkKey, t.xh, t.recordid) FROM TdMsBookmark t WHERE t.recordid=:recordid ORDER BY t.xh")
})
public class TdMsBookmark implements java.io.Serializable {

	// Fields

	private Integer rid;
	private TdMsTemplate tdMsTemplate;// templateId;
	private String bookmarkName;
	private String bookmarkDesc;
	private String bookmarkKey;
	private Integer xh;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private String recordid;

	// Constructors

	/** default constructor */
	public TdMsBookmark() {
	}

	/** minimal constructor */
	public TdMsBookmark(Integer rid, String bookmarkName,
			String bookmarkDesc, String bookmarkKey, String recordid) {
		this.rid = rid;
		this.bookmarkName = bookmarkName;
		this.bookmarkDesc = bookmarkDesc;
		this.bookmarkKey = bookmarkKey;
		this.recordid = recordid;
	}
	
	public TdMsBookmark(Integer rid, String bookmarkName, String bookmarkDesc, String bookmarkKey, Integer xh, String recordid) {
		this.rid = rid;
		this.bookmarkName = bookmarkName;
		this.bookmarkDesc = bookmarkDesc;
		this.bookmarkKey = bookmarkKey;
		this.xh = xh;
		this.recordid = recordid;
	}

	/** full constructor */
	public TdMsBookmark(Integer rid, TdMsTemplate tdMsTemplate,
			String bookmarkName, String bookmarkDesc, String bookmarkKey,
			Integer xh, Date createDate, Integer createManid,
			Date modifyDate, Integer modifyManid, String recordid) {
		this.rid = rid;
		this.tdMsTemplate =  tdMsTemplate;
		this.bookmarkName = bookmarkName;
		this.bookmarkDesc = bookmarkDesc;
		this.bookmarkKey = bookmarkKey;
		this.xh = xh;
		this.createDate = createDate;
		this.createManid = createManid;
		this.modifyDate = modifyDate;
		this.modifyManid = modifyManid;
		this.recordid = recordid;
	}

	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdMsBookmark_Seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "TEMPLATE_ID", unique = true)
	public TdMsTemplate getTdMsTemplate() {
		return this.tdMsTemplate;
	}

	public void setTdMsTemplate(TdMsTemplate tdMsTemplate) {
		this.tdMsTemplate = tdMsTemplate;
	}

	@Column(name = "BOOKMARK_NAME", unique = true , length = 100)
	public String getBookmarkName() {
		return this.bookmarkName;
	}

	public void setBookmarkName(String bookmarkName) {
		this.bookmarkName = bookmarkName;
	}

	@Column(name = "BOOKMARK_DESC" , length = 100)
	public String getBookmarkDesc() {
		return this.bookmarkDesc;
	}

	public void setBookmarkDesc(String bookmarkDesc) {
		this.bookmarkDesc = bookmarkDesc;
	}

	@Column(name = "BOOKMARK_KEY" , length = 50)
	public String getBookmarkKey() {
		return this.bookmarkKey;
	}

	public void setBookmarkKey(String bookmarkKey) {
		this.bookmarkKey = bookmarkKey;
	}

	@Column(name = "XH", precision = 22, scale = 0)
	public Integer getXh() {
		return this.xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}


	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID", precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Column(name = "MODIFY_DATE")
	@Temporal(TemporalType.TIMESTAMP)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Column(name = "RECORDID" , length = 25)
	public String getRecordid() {
		return this.recordid;
	}

	public void setRecordid(String recordid) {
		this.recordid = recordid;
	}

}