package com.chis.modules.system.entity;

import org.hibernate.validator.constraints.NotBlank;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * @history 2014-9-15 david 添加构造函数
 *
 * <AUTHOR>
 * @createDate 2014年9月15日上午10:57:46
 *
 * @修改人 xt
 * @修改时间 2014-11-28 新增是否选择字段，默认部选择
 */
@Entity
@Table(name = "TS_SIMPLE_CODE")
@SequenceGenerator(name = "TsSimpleCode_seq", sequenceName = "TS_SIMPLE_CODE_SEQ", allocationSize = 1)
public class TsSimpleCode implements java.io.Serializable {

	private static final long serialVersionUID = -9138858292820883183L;
	private Integer rid;
	private TsCodeType tsCodeType;
	private String codeNo;
	private String codeName;
	private String extendS1;
	private Integer extendS2;
	private String extendS3;
	private String extendS4;
	private String extendS5;
	private String extendS6;
	private String extendS7;
	private String extendS8;
	private String codeDesc;
	private String codeLevelNo;
	private Short ifReveal = 1;
	private Date stopDate;
	private String splsht;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	/**码表全路径*/
	private String codePath;
	/**发布标记*/
	private Short publishTag;
    /**码表类型编码*/
    private String codeTypeNo;
    /** 是否选择 */
    private boolean ifSelected = false;

    /** +当前编码，其对应的子节点 */
    private List<TsSimpleCode> subCodeList;
    /** +当前编码，选中的子节点的rid */
    private String[] selCodeRids;
    /** +序号 */
    private Integer num;
    /**UUID*/
    private String UUID;
    
    private String levelIndex;
    
    /**上一级编码名称*/
    private String lastCodeName;
    /** 是否可选择 */
    private boolean ifDisabled = false;

	/**脱敏显示字段1*/
	private String encryptStr1;

	/** 临时字段 是否可以添加子节点 */
	private boolean ifCanAddSub = true;
    
	public TsSimpleCode() {
	}

    public TsSimpleCode(Integer rid) {
        this.rid = rid;
    }

    public TsSimpleCode(String codeNo, String codeName) {
		this.codeNo = codeNo;
		this.codeName = codeName;
	}
    
    /**
     * 不允许删除
     * @param rid 主键
     * @param codeName 名称
     * @param codeTypeNo 主表类型编码
     */
    public TsSimpleCode(Integer rid, String codeName, String codeTypeNo) {
        this.rid = rid;
        this.codeName = codeName;
        this.codeTypeNo = codeTypeNo;
    }
    public TsSimpleCode(Integer rid, String extendS3) {
        this.rid = rid;
        this.extendS3 = extendS3;
    }

	public TsSimpleCode(Integer rid, String codeNo, String codeName, String codeTypeNo, String codeLevelNo) {
		this.rid = rid;
		this.codeNo = codeNo;
		this.codeName = codeName;
		this.codeTypeNo = codeTypeNo;
		this.codeLevelNo = codeLevelNo;
	}
	
	public TsSimpleCode(Integer rid, String codeNo, String codeName, String codeTypeNo, String codeLevelNo,String extendS3) {
		this.rid = rid;
		this.codeNo = codeNo;
		this.codeName = codeName;
		this.codeTypeNo = codeTypeNo;
		this.codeLevelNo = codeLevelNo;
		this.extendS3=extendS3;
	}
	
	public TsSimpleCode(Integer rid, String codeNo, String codeName, String codeTypeNo, String codeLevelNo,String extendS1,Integer extendS2,String extendS3) {
		this.rid = rid;
		this.codeNo = codeNo;
		this.codeName = codeName;
		this.codeTypeNo = codeTypeNo;
		this.codeLevelNo = codeLevelNo;
		this.extendS1=extendS1;
		this.extendS2=extendS2;
		this.extendS3=extendS3;
	}

    /**
     * 不允许删除
     * @param rid 主键
     * @param codeName 名称
     * @param codeTypeNo 主表类型编码
     * @param codeLevelNo 层级编码
     */
    public TsSimpleCode(Integer rid, String codeName, String codeTypeNo, String codeLevelNo) {
        this.rid = rid;
        this.codeName = codeName;
        this.codeTypeNo = codeTypeNo;
        this.codeLevelNo = codeLevelNo;
    }

    public TsSimpleCode(Integer rid, TsCodeType tsCodeType, String codeNo,
			Short ifReveal, Date createDate, Integer createManid) {
		this.rid = rid;
		this.tsCodeType = tsCodeType;
		this.codeNo = codeNo;
		this.ifReveal = ifReveal;
		this.createDate = createDate;
		this.createManid = createManid;
	}

    /**
     * @param codeNo 编码
     * @param codeName 名称
     * @param codeDesc 说明
     * @param codeLevelNo 层级编码
     * @param splsht 拼音简写
     * @param createDate 创建日期
     * @param createManid 创建人
     * @param codeTypeNo 类型编码
     */
    public TsSimpleCode(String codeNo, String codeName, String codeDesc, String codeLevelNo, String splsht,
                        Date createDate, Integer createManid, String codeTypeNo) {
        this.codeNo = codeNo;
        this.codeName = codeName;
        this.codeDesc = codeDesc;
        this.codeLevelNo = codeLevelNo;
        this.splsht = splsht;
        this.createDate = createDate;
        this.createManid = createManid;
        this.codeTypeNo = codeTypeNo;
    }


    /** full constructor */
	public TsSimpleCode(Integer rid, TsCodeType tsCodeType, String codeNo,
			String codeName, String codeDesc, String codeLevelNo,
			Short ifReveal, Date stopDate, String splsht,
			Date createDate, Integer createManid, Date modifyDate,
			Integer modifyManid,String extendS3,Integer extendS2,String extendS1) {
		this.rid = rid;
		this.tsCodeType = tsCodeType;
		this.codeNo = codeNo;
		this.codeName = codeName;
		this.codeDesc = codeDesc;
		this.codeLevelNo = codeLevelNo;
		this.ifReveal = ifReveal;
		this.stopDate = stopDate;
		this.splsht = splsht;
		this.createDate = createDate;
		this.createManid = createManid;
		this.modifyDate = modifyDate;
		this.modifyManid = modifyManid;
		this.extendS1 = extendS1;
		this.extendS2 = extendS2;
		this.extendS3 = extendS3;
	}

	public TsSimpleCode(TsSimpleCode simpleCode) {
		if (simpleCode == null) {
			return;
		}
		this.rid = simpleCode.getRid();
		this.codeName = simpleCode.getCodeName();
		this.extendS1 = simpleCode.getExtendS1();
		this.extendS2 = simpleCode.getExtendS2();
		this.extendS3 = simpleCode.getExtendS3();
		this.extendS4 = simpleCode.getExtendS4();
		this.extendS5 = simpleCode.getExtendS5();
		this.extendS6 = simpleCode.getExtendS6();
		this.extendS7 = simpleCode.getExtendS7();
		this.extendS8 = simpleCode.getExtendS8();
		this.codeNo = simpleCode.getCodeNo();
		this.codeTypeNo = simpleCode.getCodeTypeNo();
		this.tsCodeType = simpleCode.getTsCodeType();
		this.codeDesc = simpleCode.getCodeDesc();
		this.codePath = simpleCode.getCodePath();
		this.num = simpleCode.getNum();
	}

	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsSimpleCode_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "CODE_TYPE_ID" )
	public TsCodeType getTsCodeType() {
		return this.tsCodeType;
	}

	public void setTsCodeType(TsCodeType tsCodeType) {
		this.tsCodeType = tsCodeType;
	}

	@Column(name = "CODE_NO" , length = 20)
	public String getCodeNo() {
		return this.codeNo;
	}

	public void setCodeNo(String codeNo) {
		this.codeNo = codeNo;
	}

	@Column(name = "CODE_NAME")
	public String getCodeName() {
		return this.codeName;
	}

	public void setCodeName(String codeName) {
		this.codeName = codeName;
	}
	@Column(name = "EXTENDS1")
	public String getExtendS1() {
		return extendS1;
	}

	public void setExtendS1(String extendS1) {
		this.extendS1 = extendS1;
	}
	@Column(name = "EXTENDS2")
	public Integer getExtendS2() {
		return extendS2;
	}

	public void setExtendS2(Integer extendS2) {
		this.extendS2 = extendS2;
	}
	
	@Column(name = "EXTENDS3")
	public String getExtendS3() {
		return extendS3;
	}

	public void setExtendS3(String extendS3) {
		this.extendS3 = extendS3;
	}
	
	@Column(name = "EXTENDS4")
	public String getExtendS4() {
		return extendS4;
	}

	public void setExtendS4(String extendS4) {
		this.extendS4 = extendS4;
	}

	@Column(name = "CODE_DESC", length = 200)
	public String getCodeDesc() {
		return this.codeDesc;
	}

	public void setCodeDesc(String codeDesc) {
		this.codeDesc = codeDesc;
	}

	@Column(name = "CODE_LEVEL_NO", length = 30)
	public String getCodeLevelNo() {
		return this.codeLevelNo;
	}

	public void setCodeLevelNo(String codeLevelNo) {
		this.codeLevelNo = codeLevelNo;
	}

	@Column(name = "IF_REVEAL" , precision = 1, scale = 0)
	public Short getIfReveal() {
		return this.ifReveal;
	}

	public void setIfReveal(Short ifReveal) {
		this.ifReveal = ifReveal;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "STOP_DATE", length = 7)
	public Date getStopDate() {
		return this.stopDate;
	}

	public void setStopDate(Date stopDate) {
		this.stopDate = stopDate;
	}

	@Column(name = "SPLSHT")
	public String getSplsht() {
		return this.splsht;
	}

	public void setSplsht(String splsht) {
		this.splsht = splsht;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

    @Transient
    public String getCodeTypeNo() {
        return codeTypeNo;
    }

    public void setCodeTypeNo(String codeTypeNo) {
        this.codeTypeNo = codeTypeNo;
    }

    @Column(name = "CODE_PATH")
	public String getCodePath() {
		return codePath;
	}
    @Column(name="PUBLISH_TAG")
	public Short getPublishTag() {
		return publishTag;
	}
    
	public void setCodePath(String codePath) {
		this.codePath = codePath;
	}

	public void setPublishTag(Short publishTag) {
		this.publishTag = publishTag;
	}

    @Transient
    public boolean isIfSelected() {
        return ifSelected;
    }

    public void setIfSelected(boolean ifSelected) {
        this.ifSelected = ifSelected;
    }

    @Transient
	public List<TsSimpleCode> getSubCodeList() {
		return subCodeList;
	}

	public void setSubCodeList(List<TsSimpleCode> subCodeList) {
		this.subCodeList = subCodeList;
	}

	@Transient
	public String[] getSelCodeRids() {
		return selCodeRids;
	}

	public void setSelCodeRids(String[] selCodeRids) {
		this.selCodeRids = selCodeRids;
	}

	@Column(name="NUM")
	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	@Column(name="UUID")
	public String getUUID() {
		return UUID;
	}

	public void setUUID(String uUID) {
		UUID = uUID;
	}
	@Transient
	public String getLevelIndex() {
		return levelIndex;
	}

	public void setLevelIndex(String levelIndex) {
		this.levelIndex = levelIndex;
	}
	
	@Transient
	public String getLastCodeName() {
		return lastCodeName;
	}

	public void setLastCodeName(String lastCodeName) {
		this.lastCodeName = lastCodeName;
	}
	@Transient
	public boolean isIfDisabled() {
		return ifDisabled;
	}

	public void setIfDisabled(boolean ifDisabled) {
		this.ifDisabled = ifDisabled;
	}
	@Column(name = "EXTENDS5")
	public String getExtendS5() {
		return extendS5;
	}

	public void setExtendS5(String extendS5) {
		this.extendS5 = extendS5;
	}

	@Column(name = "EXTENDS6")
	public String getExtendS6() {
		return extendS6;
	}

	public void setExtendS6(String extendS6) {
		this.extendS6 = extendS6;
	}

	@Column(name = "EXTENDS7")
	public String getExtendS7() {
		return extendS7;
	}

	public void setExtendS7(String extendS7) {
		this.extendS7 = extendS7;
	}

	@Column(name = "EXTENDS8")
	public String getExtendS8() {
		return extendS8;
	}

	public void setExtendS8(String extendS8) {
		this.extendS8 = extendS8;
	}

	@Transient
	public String getEncryptStr1() {
		return encryptStr1;
	}

	public void setEncryptStr1(String encryptStr1) {
		this.encryptStr1 = encryptStr1;
	}

	@Transient
	public boolean isIfCanAddSub() {
		return ifCanAddSub;
	}

	public void setIfCanAddSub(boolean ifCanAddSub) {
		this.ifCanAddSub = ifCanAddSub;
	}
}