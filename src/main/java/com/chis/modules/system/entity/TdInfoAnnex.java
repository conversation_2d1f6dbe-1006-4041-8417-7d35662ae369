package com.chis.modules.system.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * 寻呼信息附件
 * 
 * <AUTHOR>
 * @history 2014-07-15
 */
@Entity
@Table(name = "TD_INFO_ANNEX")
@SequenceGenerator(name = "TdInfoAnnex_seq", sequenceName = "TD_INFO_ANNEX_SEQ", allocationSize = 1)
public class TdInfoAnnex implements java.io.Serializable {

	private Integer rid;//
	private TdInfoMain tdInfoMain;// 寻呼信息ID
	private String annexName;// 附件名称
	private String annexAddr;// 附件地址
	private Integer xh;// 序号

	public TdInfoAnnex() {
	}

	public TdInfoAnnex(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdInfoAnnex_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "MAIN_ID" )
	public TdInfoMain getTdInfoMain() {
		return this.tdInfoMain;
	}

	public void setTdInfoMain(TdInfoMain tdInfoMain) {
		this.tdInfoMain = tdInfoMain;
	}

	@Column(name = "ANNEX_NAME" , length = 200)
	public String getAnnexName() {
		return this.annexName;
	}

	public void setAnnexName(String annexName) {
		this.annexName = annexName;
	}

	@Column(name = "ANNEX_ADDR" , length = 200)
	public String getAnnexAddr() {
		return this.annexAddr;
	}

	public void setAnnexAddr(String annexAddr) {
		this.annexAddr = annexAddr;
	}

	@Column(name = "XH" , precision = 1, scale = 0)
	public Integer getXh() {
		return this.xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}

}