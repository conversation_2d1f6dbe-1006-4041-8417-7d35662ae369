package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.chis.modules.system.enumn.SystemType;

/**
 * 报表模板
 * <AUTHOR>
 * @createDate 2015年2月2日下午5:24:01
 */
@Entity
@Table(name = "TS_RPT")
@SequenceGenerator(name = "TsRpt", sequenceName = "TS_RPT_SEQ",allocationSize=1)
@NamedQueries({
    @NamedQuery(name = "TsRpt.findByCode", query = "SELECT t FROM TsRpt t WHERE t.rptCod=:rptCod"),
    @NamedQuery(name = "TsRpt.findPathByCode", query = "SELECT new TsRpt(t.rptCod, t.rptpath, t.rptver) FROM TsRpt t WHERE t.rptCod=:rptCod")
})
public class TsRpt implements java.io.Serializable {

	private static final long serialVersionUID = -3773811992882600544L;
	// Fields
	private Integer rid;
	private SystemType systemType;
	private String rptCod;
	private String rptnam;
	private String rptpath;
	private Integer rptver;
	private String rmk;
	private Date createDate;
	private Integer createManid;
	
	/**模板文件的16进制字符串*/
	private String fileContent;
	/**数据集XML*/
	private String xmlData;
	/**业务模板版本，比如流程定义有多个版本，为了兼容老版本，内部定义模板路径为如：OA_1002_DEFID.fr3*/
    private String businessVersion = "";
	
	private Integer paramType;
	
	// Constructors
	public TsRpt() {
	}

	public TsRpt(Integer rid) {
		this.rid = rid;
	}
	
	public TsRpt(String rptCod, String rptpath, Integer rptver) {
		this.rptCod = rptCod;
		this.rptpath = rptpath;
		this.rptver = rptver;
	}

	public TsRpt(SystemType systemType, String rptCod, String rptnam, String rptpath, Integer rptver, String rmk, Date createDate, Integer createManid) {
		this.systemType = systemType;
		this.rptCod = rptCod;
		this.rptnam = rptnam;
		this.rptpath = rptpath;
		this.rptver = rptver;
		this.rmk = rmk;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsRpt")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Enumerated
    @Transient
	public SystemType getSystemType() {
		return systemType;
	}

	public void setSystemType(SystemType systemType) {
		this.systemType = systemType;
	}

	@Column(name = "CREATE_DATE" , length = 11)
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Column(name = "RPTCOD")
	public String getRptCod() {
		return rptCod;
	}

	public void setRptCod(String rptCod) {
		this.rptCod = rptCod;
	}

	@Column(name = "RPTNAM")
	public String getRptnam() {
		return rptnam;
	}

	public void setRptnam(String rptnam) {
		this.rptnam = rptnam;
	}

	@Column(name = "RPTPATH")
	public String getRptpath() {
		return rptpath;
	}

	public void setRptpath(String rptpath) {
		this.rptpath = rptpath;
	}

	@Column(name = "RPTVRE")
	public Integer getRptver() {
		return rptver;
	}

	public void setRptver(Integer rptver) {
		this.rptver = rptver;
	}

	@Column(name = "RMK")
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

	@Transient
	public String getFileContent() {
		return fileContent;
	}

	public void setFileContent(String fileContent) {
		this.fileContent = fileContent;
	}

	@Transient
	public String getXmlData() {
		return xmlData;
	}

	public void setXmlData(String xmlData) {
		this.xmlData = xmlData;
	}

	@Transient
	public String getBusinessVersion() {
		return businessVersion;
	}

	public void setBusinessVersion(String businessVersion) {
		this.businessVersion = businessVersion;
	}

	@Column(name = "PARAM_TYPE")
	public Integer getParamType() {
		return paramType;
	}

	public void setParamType(Integer paramType) {
		this.paramType = paramType;
	}
	
}