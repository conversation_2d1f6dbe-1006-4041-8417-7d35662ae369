package com.chis.modules.system.entity;

import javax.persistence.*;

/**
 * 
 * <AUTHOR>
 * @createTime 2019-11-30
 */
@Entity
@Table(name = "TS_CONTRA_MAIN")
@SequenceGenerator(name = "TsContraMain_seq", sequenceName = "TS_CONTRA_MAIN_SEQ", allocationSize = 1)
public class TsContraMain implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String contraCode;
	private String descr;

	private String rmk;
	
	public TsContraMain() {
	}

	public TsContraMain(Integer rid) {
		this.rid = rid;
	}
	@Id
	@Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsContraMain_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "CONTRA_CODE")	
	public String getContraCode() {
		return contraCode;
	}

	public void setContraCode(String contraCode) {
		this.contraCode = contraCode;
	}	
			
	@Column(name = "DESCR")	
	public String getDescr() {
		return descr;
	}

	public void setDescr(String descr) {
		this.descr = descr;
	}

	@Column(name = "RMK")
	public String getRmk() {
		return rmk;
	}
	public void setRmk(String rmk) {
		this.rmk = rmk;
	}
}