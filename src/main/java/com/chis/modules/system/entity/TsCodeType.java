package com.chis.modules.system.entity;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.enumn.SystemType;

/**
 * TsCodeType entity. <AUTHOR> Persistence Tools
 */
@Entity
@SequenceGenerator(name = "TsCodeType_seq", sequenceName = "TS_CODE_TYPE_SEQ",allocationSize=1)
@Table(name = "TS_CODE_TYPE", uniqueConstraints = @UniqueConstraint(columnNames = "CODE_TYPE_NAME"))
public class TsCodeType implements java.io.Serializable {

	private static final long serialVersionUID = -5240230421753829465L;
	private Integer rid;
    private SystemType systemType;
	private String codeTypeName;
	private String codeTypeDesc;
	private Short treeTag;
	private Short isuserType;
    private Integer num;
	private Set<TsSimpleCode> tsSimpleCodes = new HashSet<TsSimpleCode>(0);
    private List<TsRoleCodeauth> authList = new ArrayList<TsRoleCodeauth>(0);

    private String paramTypeName;
    private Integer paramType;
	/** +自检状态20240120 */
	private Integer state;
	/** +自检失败原因20240120 */
	private String errRsn;
	/** +备注20240120 */
	private String rmk;
    
	public TsCodeType() {
	}

    public TsCodeType(Integer rid) {
        this.rid = rid;
    }

	public TsCodeType(String codeTypeName) {
		this.codeTypeName = codeTypeName;
	}

    public TsCodeType(Integer rid, String codeTypeName, Short treeTag) {
		this.rid = rid;
		this.codeTypeName = codeTypeName;
		this.treeTag = treeTag;
	}

    /**
     * @param codeTypeName 类别编码
     * @param codeTypeDesc 类别名称
     * @param treeTag 是否树形标识
     * @param isuserType 是否用户标识
     */
    public TsCodeType(String codeTypeName, String codeTypeDesc, Short treeTag, Short isuserType) {
        this.codeTypeName = codeTypeName;
        this.codeTypeDesc = codeTypeDesc;
        this.treeTag = treeTag;
        this.isuserType = isuserType;
    }

    /**
     * @param systemType 系统类型
     * @param codeTypeName 类别编码
     * @param codeTypeDesc 类别名称
     * @param treeTag 是否树形标识
     * @param isuserType 是否用户标识
     */
    public TsCodeType(SystemType systemType, String codeTypeName, String codeTypeDesc, Short treeTag, Short isuserType) {
        this.systemType = systemType;
        this.codeTypeName = codeTypeName;
        this.codeTypeDesc = codeTypeDesc;
        this.treeTag = treeTag;
        this.isuserType = isuserType;
    }

    public TsCodeType(Integer rid, String codeTypeName, String codeTypeDesc,
			Short treeTag, Short isuserType, Set<TsSimpleCode> tsSimpleCodes) {
		this.rid = rid;
		this.codeTypeName = codeTypeName;
		this.codeTypeDesc = codeTypeDesc;
		this.treeTag = treeTag;
		this.isuserType = isuserType;
		this.tsSimpleCodes = tsSimpleCodes;
	}

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsCodeType_seq")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

    @Enumerated
    @Transient
    public SystemType getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemType systemType) {
        this.systemType = systemType;
    }


	@Column(name = "CODE_TYPE_NAME", unique = true , length = 50)
	public String getCodeTypeName() {
		return this.codeTypeName;
	}

	public void setCodeTypeName(String codeTypeName) {
		this.codeTypeName = codeTypeName;
	}

	@Column(name = "CODE_TYPE_DESC", length = 200)
	public String getCodeTypeDesc() {
		return this.codeTypeDesc;
	}

	public void setCodeTypeDesc(String codeTypeDesc) {
		this.codeTypeDesc = codeTypeDesc;
	}

	@Column(name = "TREE_TAG" , precision = 1, scale = 0)
	public Short getTreeTag() {
		return this.treeTag;
	}

	public void setTreeTag(Short treeTag) {
		this.treeTag = treeTag;
	}

	@Column(name = "ISUSER_TYPE", precision = 1, scale = 0)
	public Short getIsuserType() {
		return this.isuserType;
	}

	public void setIsuserType(Short isuserType) {
		this.isuserType = isuserType;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsCodeType")
	public Set<TsSimpleCode> getTsSimpleCodes() {
		return this.tsSimpleCodes;
	}

	public void setTsSimpleCodes(Set<TsSimpleCode> tsSimpleCodes) {
		this.tsSimpleCodes = tsSimpleCodes;
	}

    @Column(name = "NUM")
    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    @Transient
    public String getParamTypeName() {
        return paramTypeName;
    }

    public void setParamTypeName(String paramTypeName) {
        this.paramTypeName = paramTypeName;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsCodeType")
    public List<TsRoleCodeauth> getAuthList() {
        return authList;
    }

    public void setAuthList(List<TsRoleCodeauth> authList) {
        this.authList = authList;
    }

    @Column(name = "PARAM_TYPE")
	public Integer getParamType() {
		return paramType;
	}

	public void setParamType(Integer paramType) {
		this.paramType = paramType;
	}

	@Column(name = "STATE")
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	@Column(name = "ERR_RSN")
	public String getErrRsn() {
		return errRsn;
	}

	public void setErrRsn(String errRsn) {
		this.errRsn = errRsn;
	}

	@Column(name = "RMK")
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}
}