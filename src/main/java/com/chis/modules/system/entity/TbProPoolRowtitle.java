package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * 
 * <AUTHOR>
 * @createTime 2017-10-19
 */
@Entity
@Table(name = "TB_PRO_POOL_ROWTITLE")
@SequenceGenerator(name = "TbProPoolRowtitle", sequenceName = "TB_PRO_POOL_ROWTITLE_SEQ", allocationSize = 1)
public class TbProPoolRowtitle implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbProPoolTabdefine fkByTableId;
	private String title;
	private Integer rowIndex;
	private Integer colIndex;
	private Integer colspan;
	private Integer rowspan;
	private Date createDate;
	private Integer createManid;
	private Integer state;

	public TbProPoolRowtitle() {
	}

	public TbProPoolRowtitle(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbProPoolRowtitle")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "TABLE_ID")
	public TbProPoolTabdefine getFkByTableId() {
		return fkByTableId;
	}

	public void setFkByTableId(TbProPoolTabdefine fkByTableId) {
		this.fkByTableId = fkByTableId;
	}

	@Column(name = "TITLE")
	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	@Column(name = "ROW_INDEX")
	public Integer getRowIndex() {
		return rowIndex;
	}

	public void setRowIndex(Integer rowIndex) {
		this.rowIndex = rowIndex;
	}

	@Column(name = "COL_INDEX")
	public Integer getColIndex() {
		return colIndex;
	}

	public void setColIndex(Integer colIndex) {
		this.colIndex = colIndex;
	}

	@Column(name = "COLSPAN")
	public Integer getColspan() {
		return colspan;
	}

	public void setColspan(Integer colspan) {
		this.colspan = colspan;
	}

	@Column(name = "ROWSPAN")
	public Integer getRowspan() {
		return rowspan;
	}

	public void setRowspan(Integer rowspan) {
		this.rowspan = rowspan;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE")
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID")
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Column(name = "STATE")
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}
}