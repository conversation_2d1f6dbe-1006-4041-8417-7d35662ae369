package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.system.enumn.IWritTypeEnum;
import com.chis.modules.system.enumn.PxWritTypeEnum;
import com.chis.modules.system.enumn.WritTypeEnum;

/**
 * <p>类描述：文书类型</p>
 * @ClassAuthor qrr,2018年4月21日,TbZwWritsort
 * 
 * <p>修订内容：添加构造方法</p>
 * @ClassReviser qrr,2018年4月24日,TbZwWritsort
 */
@Entity
@Table(name = "TB_ZW_WRITSORT")
@SequenceGenerator(name = "TbZwWritsort", sequenceName = "TB_ZW_WRITSORT_SEQ", allocationSize = 1)
@NamedQueries({
		@NamedQuery(name = "TbZwWritsort.findByCode", query = "SELECT t FROM TbZwWritsort t WHERE t.writCode=:writCode")
})
public class TbZwWritsort implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer writSort;
	private TsRpt fkByRptTemplId;
	private String writCode;
	private String writShortname;
	private String writName;
	private Integer ifHuman;
	private Integer multiple;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TbZwWritsort() {
	}

	public TbZwWritsort(Integer rid) {
		this.rid = rid;
	}

	public TbZwWritsort(Integer rid, String writShortname) {
		this.rid = rid;
		this.writShortname = writShortname;
	}

	public TbZwWritsort(Integer writSort, WritTypeEnum writType, String writShortname) {
		this.writSort = writSort;
		this.writName = writType.getWritName();
		this.writCode = writType.getWritCode();
		this.writShortname = writShortname;
	}
	public TbZwWritsort(Integer writSort, PxWritTypeEnum writType, String writShortname) {
		this.writSort = writSort;
		this.writName = writType.getWritName();
		this.writCode = writType.getWritCode();
		this.writShortname = writShortname;
	}
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2020年12月30日,TbZwWritsort
	 * */
	public TbZwWritsort(Integer writSort, IWritTypeEnum writType, String writShortname) {
		this.writSort = writSort;
		this.writName = writType.getWritName();
		this.writCode = writType.getWritCode();
		this.writShortname = writShortname;
	}
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbZwWritsort")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "WRIT_SORT")	
	public Integer getWritSort() {
		return writSort;
	}

	public void setWritSort(Integer writSort) {
		this.writSort = writSort;
	}	
			
	@ManyToOne
	@JoinColumn(name = "RPT_TEMPL_ID")			
	public TsRpt getFkByRptTemplId() {
		return fkByRptTemplId;
	}

	public void setFkByRptTemplId(TsRpt fkByRptTemplId) {
		this.fkByRptTemplId = fkByRptTemplId;
	}	
			
	@Column(name = "WRIT_CODE")	
	public String getWritCode() {
		return writCode;
	}

	public void setWritCode(String writCode) {
		this.writCode = writCode;
	}	
			
	@Column(name = "WRIT_SHORTNAME")	
	public String getWritShortname() {
		return writShortname;
	}

	public void setWritShortname(String writShortname) {
		this.writShortname = writShortname;
	}	
			
	@Column(name = "WRIT_NAME")	
	public String getWritName() {
		return writName;
	}

	public void setWritName(String writName) {
		this.writName = writName;
	}	
			
	@Column(name = "IF_HUMAN")	
	public Integer getIfHuman() {
		return ifHuman;
	}

	public void setIfHuman(Integer ifHuman) {
		this.ifHuman = ifHuman;
	}	
			
	@Column(name = "MULTIPLE")	
	public Integer getMultiple() {
		return multiple;
	}

	public void setMultiple(Integer multiple) {
		this.multiple = multiple;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}