package com.chis.modules.system.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "TS_ROLE_SIMPAUTH")
@SequenceGenerator(name = "TsRoleSimpauth", sequenceName = "TS_ROLE_SIMPAUTH_SEQ", allocationSize = 1)
public class TsRoleSimpauth implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1993325567815667344L;
	private Integer rid;
	private TsRole tsRole;
	private TsSimpleCode tsSimpleCode;
	private Date createDate;
	private Integer createManid;

	public TsRoleSimpauth() {
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsRoleSimpauth")
	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "ROLE_ID" )
	public TsRole getTsRole() {
		return tsRole;
	}

	public void setTsRole(TsRole tsRole) {
		this.tsRole = tsRole;
	}

	@ManyToOne
	@JoinColumn(name = "SIMPCODE_ID" )
	public TsSimpleCode getTsSimpleCode() {
		return tsSimpleCode;
	}

	public void setTsSimpleCode(TsSimpleCode tsSimpleCode) {
		this.tsSimpleCode = tsSimpleCode;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

}
