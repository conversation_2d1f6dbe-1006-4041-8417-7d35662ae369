package com.chis.modules.system.entity;

import com.chis.common.utils.ObjectUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 地区TREE
 *
 * <AUTHOR>
 * @version 1.1
 */
public class TsZoneTree implements Serializable {

    private static final long serialVersionUID = 1L;

    private TsZone zone;

    private String zoneGb;

    private String zoneGbNo0;

    private Short zoneType;

    private Short realZoneType;

    private List<TsZoneTree> childZoneList;

    public TsZoneTree() {
        this.childZoneList = new ArrayList<>();
    }

    public TsZoneTree(TsZone zone) {
        this.setZone(zone);
        this.childZoneList = new ArrayList<>();
    }

    public void addChildZone(TsZone zone) {
        this.childZoneList.add(new TsZoneTree(zone));
    }

    public boolean hasChild() {
        return this.childZoneList.size() > 0;
    }

    public List<TsZone> toZoneList(String zoneGb, boolean real, Short typeMin, Short typeMax) {
        List<TsZone> zoneList = new ArrayList<>();
        //zone或zoneGb为空时需要跳过当前zone，直接获取下级地区
        boolean skip = ObjectUtil.isEmpty(this.zone) || ObjectUtil.isEmpty(this.zoneGb);
        //当前地区或下级所有地区的地区编码不可能被查询出来时直接返回空List
        if (!skip && ObjectUtil.isNotEmpty(zoneGb)
                && !this.zoneGb.startsWith(zoneGb) && !zoneGb.startsWith(this.zoneGbNo0)) {
            return zoneList;
        }
        //当前地区级别小于指定级别时不需要添加当前地区
        boolean needAddThis = !skip &&
                (ObjectUtil.isEmpty(typeMin) ||
                        (real && typeMin <= this.realZoneType) || (!real && typeMin <= this.zoneType));
        if (needAddThis && this.zoneGb.startsWith(zoneGb)) {
            zoneList.add(this.zone);
        }
        //当前地区级别大于指定级别时不需要再添加下级地区
        boolean needAddChild = skip || ObjectUtil.isEmpty(typeMax) ||
                (real && typeMax > this.realZoneType) || (!real && typeMax > this.zoneType);
        if (needAddChild && hasChild()) {
            for (TsZoneTree zoneTree : this.childZoneList) {
                zoneList.addAll(zoneTree.toZoneList(zoneGb, real, typeMin, typeMax));
            }
        }
        return zoneList;
    }

    public TsZone getZone() {
        return zone;
    }

    public void setZone(TsZone zone) {
        this.zone = zone;
        this.zoneGb = zone.getZoneGb();
        this.zoneGbNo0 = remove0(zone.getZoneGb());
        this.zoneType = zone.getZoneType();
        this.realZoneType = zone.getRealZoneType();
    }

    public String remove0(String zoneGb) {
        if (zoneGb.endsWith("00")) {
            return remove0(zoneGb.substring(0, zoneGb.length() - 2));
        }
        return zoneGb;
    }

    public String getZoneGb() {
        return zoneGb;
    }

    public void setZoneGb(String zoneGb) {
        this.zoneGb = zoneGb;
    }

    public String getZoneGbNo0() {
        return zoneGbNo0;
    }

    public void setZoneGbNo0(String zoneGbNo0) {
        this.zoneGbNo0 = zoneGbNo0;
    }

    public Short getZoneType() {
        return zoneType;
    }

    public void setZoneType(Short zoneType) {
        this.zoneType = zoneType;
    }

    public Short getRealZoneType() {
        return realZoneType;
    }

    public void setRealZoneType(Short realZoneType) {
        this.realZoneType = realZoneType;
    }

    public List<TsZoneTree> getChildZoneList() {
        return childZoneList;
    }

    public void setChildZoneList(List<TsZoneTree> childZoneList) {
        this.childZoneList = childZoneList;
    }
}
