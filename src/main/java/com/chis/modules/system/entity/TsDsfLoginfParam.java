package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;


@Entity
@Table(name = "TS_DSF_LOGINF_PARAM")
@SequenceGenerator(name = "TsDsfLoginfParam_seq", sequenceName = "TS_DSF_LOGINF_PARAM_SEQ", allocationSize = 1)
public class TsDsfLoginfParam implements java.io.Serializable{

	private static final long serialVersionUID = -788957753373584383L;

	private Integer rid;
	private TsDsfLoginf tsDsfLoginfId;
	private String paramEn;
	private String paramValue;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TsDsfLoginfParam(){
		
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsDsfLoginfParam_seq")
	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "LOGINF_ID" )
	public TsDsfLoginf getTsDsfLoginfId() {
		return tsDsfLoginfId;
	}

	public void setTsDsfLoginfId(TsDsfLoginf tsDsfLoginfId) {
		this.tsDsfLoginfId = tsDsfLoginfId;
	}

	@Column(name = "PARAM_EN" , length = 50)
	public String getParamEn() {
		return paramEn;
	}

	public void setParamEn(String paramEn) {
		this.paramEn = paramEn;
	}

	@Column(name = "PARAM_VALUE" , length = 100)
	public String getParamValue() {
		return paramValue;
	}

	public void setParamValue(String paramValue) {
		this.paramValue = paramValue;
	}

	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	
	
}
