package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.chis.modules.system.enumn.SystemType;

/**
 * 定时任务
 * <AUTHOR>
 * @createDate 2015年1月21日下午5:24:01
 */
@Entity
@Table(name = "TS_QUARTZ")
@SequenceGenerator(name = "TsQuartz", sequenceName = "TS_QUARTZ_SEQ",allocationSize=1)
@NamedQueries({
    @NamedQuery(name = "TsQuartz.findByCode", query = "SELECT t FROM TsQuartz t WHERE t.taskCode=:taskCode")
})
public class TsQuartz implements java.io.Serializable {

	private static final long serialVersionUID = -3773811992882600544L;
	// Fields
	private Integer rid;
	private SystemType systemType;
	private String taskCode;
	private String taskClass;
	private String taskDescr;
	private Short periodKind = Short.valueOf("0");
	private Short periodType = Short.valueOf("0");
	private Short dayOfMon;
	private Short dayOfWeek;
	private Short hourOfDay;
	private Short minOfDay;
	private Integer intervalHour;
	private Integer intervalMin;
	private String expressions;
	private Short ifReveal = Short.valueOf("0");
	private Date createDate;
	private Integer createManid;
	
	private String periodDesc;
	private Integer paramType;
	
	// Constructors
	public TsQuartz() {
	}

	public TsQuartz(Integer rid) {
		this.rid = rid;
	}

	// Property accessors
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsQuartz")
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Enumerated
    @Transient
	public SystemType getSystemType() {
		return systemType;
	}

	public void setSystemType(SystemType systemType) {
		this.systemType = systemType;
	}

	@Column(name = "TASK_CODE")
	public String getTaskCode() {
		return taskCode;
	}

	public void setTaskCode(String taskCode) {
		this.taskCode = taskCode;
	}

	@Column(name = "TASK_CLASS")
	public String getTaskClass() {
		return taskClass;
	}

	public void setTaskClass(String taskClass) {
		this.taskClass = taskClass;
	}

	@Column(name = "TASK_DESCR")
	public String getTaskDescr() {
		return taskDescr;
	}

	public void setTaskDescr(String taskDescr) {
		this.taskDescr = taskDescr;
	}

	@Column(name = "PERIOD_KIND")
	public Short getPeriodKind() {
		return periodKind;
	}

	public void setPeriodKind(Short periodKind) {
		this.periodKind = periodKind;
	}

	@Column(name = "PERIOD_TYPE")
	public Short getPeriodType() {
		return periodType;
	}

	public void setPeriodType(Short periodType) {
		this.periodType = periodType;
	}

	@Column(name = "DAY_OF_MON")
	public Short getDayOfMon() {
		return dayOfMon;
	}

	public void setDayOfMon(Short dayOfMon) {
		this.dayOfMon = dayOfMon;
	}

	@Column(name = "DAY_OF_WEEK")
	public Short getDayOfWeek() {
		return dayOfWeek;
	}

	public void setDayOfWeek(Short dayOfWeek) {
		this.dayOfWeek = dayOfWeek;
	}

	@Column(name = "HOUR_OF_DAY")
	public Short getHourOfDay() {
		return hourOfDay;
	}

	public void setHourOfDay(Short hourOfDay) {
		this.hourOfDay = hourOfDay;
	}

	@Column(name = "MIN_OF_DAY")
	public Short getMinOfDay() {
		return minOfDay;
	}

	public void setMinOfDay(Short minOfDay) {
		this.minOfDay = minOfDay;
	}

	@Column(name = "INTERVAL_HOUR")
	public Integer getIntervalHour() {
		return intervalHour;
	}

	public void setIntervalHour(Integer intervalHour) {
		this.intervalHour = intervalHour;
	}

	@Column(name = "INTERVAL_MIN")
	public Integer getIntervalMin() {
		return intervalMin;
	}

	public void setIntervalMin(Integer intervalMin) {
		this.intervalMin = intervalMin;
	}

	@Column(name = "EXPRESSIONS")
	public String getExpressions() {
		return expressions;
	}

	public void setExpressions(String expressions) {
		this.expressions = expressions;
	}

	@Column(name = "IF_REVEAL")
	public Short getIfReveal() {
		return ifReveal;
	}

	public void setIfReveal(Short ifReveal) {
		this.ifReveal = ifReveal;
	}
	
	@Column(name = "CREATE_DATE" , length = 11)
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Transient
	public String getPeriodDesc() {
		return periodDesc;
	}

	public void setPeriodDesc(String periodDesc) {
		this.periodDesc = periodDesc;
	}

	@Column(name = "PARAM_TYPE")
	public Integer getParamType() {
		return paramType;
	}

	public void setParamType(Integer paramType) {
		this.paramType = paramType;
	}
	
}