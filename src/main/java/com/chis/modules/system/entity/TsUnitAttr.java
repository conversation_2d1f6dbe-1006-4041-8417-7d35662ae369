package com.chis.modules.system.entity;

import javax.persistence.*;

/**
 * TsUserRole entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_UNIT_ATTR")
@SequenceGenerator(name = "TsUnitAttr_seq", sequenceName = "TS_UNIT_ATTR_SEQ", allocationSize = 1)
public class TsUnitAttr implements java.io.Serializable {

	private static final long serialVersionUID = 1195209375999319575L;
	private Integer rid;
	private TsUnit tsUnit;
	private TsBsSort tsBsSort;

	public TsUnitAttr() {
	}



	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsUnitAttr_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

    @ManyToOne
    @JoinColumn(name = "UNIT_RID")
    public TsUnit getTsUnit() {
        return tsUnit;
    }

    public void setTsUnit(TsUnit tsUnit) {
        this.tsUnit = tsUnit;
    }

    @ManyToOne
    @JoinColumn(name = "ATTR_ID")
    public TsBsSort getTsBsSort() {
        return tsBsSort;
    }

    public void setTsBsSort(TsBsSort tsBsSort) {
        this.tsBsSort = tsBsSort;
    }


}