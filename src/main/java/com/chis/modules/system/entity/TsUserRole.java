package com.chis.modules.system.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * TsUserRole entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_USER_ROLE")
@SequenceGenerator(name = "TsUserRole_seq", sequenceName = "TS_USER_ROLE_SEQ", allocationSize = 1)
public class TsUserRole implements java.io.Serializable {

	private static final long serialVersionUID = 1195209375999319575L;
	private Integer rid;
	private TsUserInfo tsUserInfo;
	private TsRole tsRole;

	public TsUserRole() {
	}

	public TsUserRole(Integer rid, TsUserInfo tsUserInfo, TsRole tsRole) {
		this.rid = rid;
		this.tsUserInfo = tsUserInfo;
		this.tsRole = tsRole;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsUserRole_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "USER_INFO_ID" )
	public TsUserInfo getTsUserInfo() {
		return this.tsUserInfo;
	}

	public void setTsUserInfo(TsUserInfo tsUserInfo) {
		this.tsUserInfo = tsUserInfo;
	}

	@ManyToOne
	@JoinColumn(name = "ROLE_ID" )
	public TsRole getTsRole() {
		return this.tsRole;
	}

	public void setTsRole(TsRole tsRole) {
		this.tsRole = tsRole;
	}

}