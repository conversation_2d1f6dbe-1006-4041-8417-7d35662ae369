package com.chis.modules.system.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.chis.modules.system.enumn.QuestType;

/**
 * 问卷题目[通用]
 * 
 * <AUTHOR>
 * @createTime 2016-3-24
 */
@Entity
@Table(name = "TS_PROB_SUBJECT")
@SequenceGenerator(name = "TsProbSubject", sequenceName = "TS_PROB_SUBJECT_SEQ", allocationSize = 1)
public class TsProbSubject implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	/** RID */
	private Integer rid;
	/** 问卷ID */
	private TsProbLib tsProbLibByQuestlibId;
	/** 显示的题号 */
	private String showCode;
	/** 题目编码 */
	private String qesCode;
	/** 题目层级编码 */
	private String qesLevelCode;
	/** 序号 */
	private Integer num;
	/** 题目描述 */
	private String titleDesc;
	/** 类型0：单选题1：多项题2：滑动题3：评价题4：文本填空题5：标题6：日期填空题7：整数填空8：下拉单选 */
	private QuestType questType;
	private Integer questTypeValue;
	/** 是否必答0:否1：是 */
	private Integer mustAsk;
	/** 多选必答至少选几个 */
	private Integer minSelectNum;
	/** 选项布局0：竖向1：横向2：随题目显示（多用于填空） */
	private Integer optLayout;
	/** 横向列数 */
	private Integer cols;
	/** 显示脚本 */
	private String showScript;
	/** 单位 */
	private String questUnit;
	/** 状态0：停用1：启用 */
	private Integer state;
	/** 附加文字 */
	private String otherDesc;
	/** 附加图片 */
	private String otherImg;
	/** 跳转类型0：正常1：跳转到下面题目2：依赖上面的题目 */
	private Integer jumpType;
	/** 跳转表达式 */
	private String jumpQuestCode;
	/** 创建日期 */
	private Date createDate;
	/** 创建人 */
	private Integer createManid;
	/** 选项集合 */
	private List<TsProOpt> proOptList = new ArrayList<TsProOpt>();

	private Integer slideMinval;// 滑动题最小值
	private Integer slideMaxval;// 滑动题最大值
	private String slideMinvalDesc;// 滑动题最小值描述
	private String slideMaxvalDesc;// 滑动题最大值描述
	private TsProbExampool pool;
	private String invokeScrt;
	private List<TsProbSubject> childList;
	/** +分值 */
	private Integer optionScore;
	/** +填空是否多项 */
	private Integer isMulti;
	private TbProbTabdefine fkByTableId;
	/** 数字填空验证脚本 **/
	private String fillMaxRange;
	/** 依赖题目编码 */
	private String relQueCode;
	/** 依赖选项序号 */
	private List<String> relQueOptNums;
	/** 对照编码 */
	private String rightCode;

	public TsProbSubject() {
	}

	public TsProbSubject(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsProbSubject")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "QUESTLIB_ID")
	public TsProbLib getTsProbLibByQuestlibId() {
		return tsProbLibByQuestlibId;
	}

	public void setTsProbLibByQuestlibId(TsProbLib tsProbLibByQuestlibId) {
		this.tsProbLibByQuestlibId = tsProbLibByQuestlibId;
	}

	@Column(name = "SHOW_CODE")
	public String getShowCode() {
		return showCode;
	}

	public void setShowCode(String showCode) {
		this.showCode = showCode;
	}

	@Column(name = "QES_CODE")
	public String getQesCode() {
		return qesCode;
	}

	public void setQesCode(String qesCode) {
		this.qesCode = qesCode;
	}

	@Column(name = "QES_LEVEL_CODE")
	public String getQesLevelCode() {
		return qesLevelCode;
	}

	public void setQesLevelCode(String qesLevelCode) {
		this.qesLevelCode = qesLevelCode;
	}

	@Column(name = "NUM")
	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	@Column(name = "TITLE_DESC")
	public String getTitleDesc() {
		return titleDesc;
	}

	public void setTitleDesc(String titleDesc) {
		this.titleDesc = titleDesc;
	}

	@Column(name = "QUEST_TYPE")
	public QuestType getQuestType() {
		return questType;
	}

	public void setQuestType(QuestType questType) {
		this.questType = questType;
	}

	@Column(name = "MUST_ASK")
	public Integer getMustAsk() {
		return mustAsk;
	}

	public void setMustAsk(Integer mustAsk) {
		this.mustAsk = mustAsk;
	}

	@Column(name = "MIN_SELECT_NUM")
	public Integer getMinSelectNum() {
		return minSelectNum;
	}

	public void setMinSelectNum(Integer minSelectNum) {
		this.minSelectNum = minSelectNum;
	}

	@Column(name = "OPT_LAYOUT")
	public Integer getOptLayout() {
		return optLayout;
	}

	public void setOptLayout(Integer optLayout) {
		this.optLayout = optLayout;
	}

	@Column(name = "COLS")
	public Integer getCols() {
		return cols;
	}

	public void setCols(Integer cols) {
		this.cols = cols;
	}

	@Column(name = "SHOW_SCRIPT")
	public String getShowScript() {
		return showScript;
	}

	public void setShowScript(String showScript) {
		this.showScript = showScript;
	}

	@Column(name = "QUEST_UNIT")
	public String getQuestUnit() {
		return questUnit;
	}

	public void setQuestUnit(String questUnit) {
		this.questUnit = questUnit;
	}

	@Column(name = "STATE")
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	@Column(name = "OTHER_DESC")
	public String getOtherDesc() {
		return otherDesc;
	}

	public void setOtherDesc(String otherDesc) {
		this.otherDesc = otherDesc;
	}

	@Column(name = "OTHER_IMG")
	public String getOtherImg() {
		return otherImg;
	}

	public void setOtherImg(String otherImg) {
		this.otherImg = otherImg;
	}

	@Column(name = "JUMP_TYPE")
	public Integer getJumpType() {
		return jumpType;
	}

	public void setJumpType(Integer jumpType) {
		this.jumpType = jumpType;
	}

	@Column(name = "JUMP_QUEST_CODE")
	public String getJumpQuestCode() {
		return jumpQuestCode;
	}

	public void setJumpQuestCode(String jumpQuestCode) {
		this.jumpQuestCode = jumpQuestCode;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE")
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID")
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsProbSubjectByQuestId", orphanRemoval = true)
	@OrderBy(value = "num")
	public List<TsProOpt> getProOptList() {
		return proOptList;
	}

	public void setProOptList(List<TsProOpt> proOptList) {
		this.proOptList = proOptList;
	}

	@Transient
	public Integer getQuestTypeValue() {
		return questTypeValue;
	}

	public void setQuestTypeValue(Integer questTypeValue) {
		this.questTypeValue = questTypeValue;
	}

	@Column(name = "SLIDE_MINVAL")
	public Integer getSlideMinval() {
		return slideMinval;
	}

	public void setSlideMinval(Integer slideMinval) {
		this.slideMinval = slideMinval;
	}

	@Column(name = "SLIDE_MAXVAL")
	public Integer getSlideMaxval() {
		return slideMaxval;
	}

	public void setSlideMaxval(Integer slideMaxval) {
		this.slideMaxval = slideMaxval;
	}

	@Column(name = "SLIDE_MIN_DESC")
	public String getSlideMinvalDesc() {
		return slideMinvalDesc;
	}

	public void setSlideMinvalDesc(String slideMinvalDesc) {
		this.slideMinvalDesc = slideMinvalDesc;
	}

	@Column(name = "SLIDE_MAX_DESC")
	public String getSlideMaxvalDesc() {
		return slideMaxvalDesc;
	}

	public void setSlideMaxvalDesc(String slideMaxvalDesc) {
		this.slideMaxvalDesc = slideMaxvalDesc;
	}

	@ManyToOne
	@JoinColumn(name = "POOL_ID")
	public TsProbExampool getPool() {
		return pool;
	}

	public void setPool(TsProbExampool pool) {
		this.pool = pool;
	}

	@Column(name = "INVOKE_SCRT")
	public String getInvokeScrt() {
		return invokeScrt;
	}

	public void setInvokeScrt(String invokeScrt) {
		this.invokeScrt = invokeScrt;
	}

	@Transient
	public List<TsProbSubject> getChildList() {
		return childList;
	}

	public void setChildList(List<TsProbSubject> childList) {
		this.childList = childList;
	}

	@Column(name = "OPTION_SCORE")
	public Integer getOptionScore() {
		return optionScore;
	}

	public void setOptionScore(Integer optionScore) {
		this.optionScore = optionScore;
	}

	@Column(name = "IS_MULTI")
	public Integer getIsMulti() {
		return isMulti;
	}

	public void setIsMulti(Integer isMulti) {
		this.isMulti = isMulti;
	}

	@ManyToOne
	@JoinColumn(name = "TABLE_ID")
	public TbProbTabdefine getFkByTableId() {
		return fkByTableId;
	}

	public void setFkByTableId(TbProbTabdefine fkByTableId) {
		this.fkByTableId = fkByTableId;
	}

	@Column(name = "FILL_MAX_RANGE")
	public String getFillMaxRange() {
		return fillMaxRange;
	}

	public void setFillMaxRange(String fillMaxRange) {
		this.fillMaxRange = fillMaxRange;
	}

	@Column(name = "RIGHT_CODE")
	public String getRightCode() {
		return rightCode;
	}

	public void setRightCode(String rightCode) {
		this.rightCode = rightCode;
	}

	@Transient
	public String getRelQueCode() {
		return relQueCode;
	}

	public void setRelQueCode(String relQueCode) {
		this.relQueCode = relQueCode;
	}

	@Transient
	public List<String> getRelQueOptNums() {
		return relQueOptNums;
	}

	public void setRelQueOptNums(List<String> relQueOptNums) {
		this.relQueOptNums = relQueOptNums;
	}

}