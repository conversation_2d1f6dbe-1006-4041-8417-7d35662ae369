package com.chis.modules.system.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import org.hibernate.validator.constraints.NotBlank;

/**
 * TsRole entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_ROLE")
@SequenceGenerator(name = "TsRole_seq", sequenceName = "TS_ROLE_SEQ", allocationSize = 1)
public class TsRole implements java.io.Serializable {

	private Integer rid;
	private String roleName;
	private String roleDesc;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private List<TsRoleMenu> tsRoleMenuList = new ArrayList<TsRoleMenu>(0);
	private List<TsUnitRole> tsUnitRoleList = new ArrayList<TsUnitRole>(0);
	private List<TsUserRole> tsUserRoleList = new ArrayList<TsUserRole>(0);
	private List<TsRolePower> tsRolePowerList = new ArrayList<TsRolePower>(0);
    private Integer unitId;
    private Integer ifSuperManageRole;
    private String typeName;
    
    private boolean disabled;
    /** +角色编码20220331 给第三方应用提供的权限编码 */
	private String roleCode;

	public TsRole() {
	}

    public TsRole(Integer rid) {
        this.rid = rid;
    }

    public TsRole(Integer rid, String roleName, Date createDate,
			Integer createManid) {
		this.rid = rid;
		this.roleName = roleName;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsRole_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@NotBlank(message="角色名称不允许为空！")
	@Column(name = "ROLE_NAME" , length = 20)
	public String getRoleName() {
		return this.roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	@Column(name = "ROLE_DESC", length = 100)
	public String getRoleDesc() {
		return this.roleDesc;
	}

	public void setRoleDesc(String roleDesc) {
		this.roleDesc = roleDesc;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsRole")
	public List<TsRoleMenu> getTsRoleMenuList() {
		return tsRoleMenuList;
	}

	public void setTsRoleMenuList(List<TsRoleMenu> tsRoleMenuList) {
		this.tsRoleMenuList = tsRoleMenuList;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsRole")
	public List<TsUnitRole> getTsUnitRoleList() {
		return tsUnitRoleList;
	}

	public void setTsUnitRoleList(List<TsUnitRole> tsUnitRoleList) {
		this.tsUnitRoleList = tsUnitRoleList;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsRole", orphanRemoval=true)
	public List<TsUserRole> getTsUserRoleList() {
		return tsUserRoleList;
	}

	public void setTsUserRoleList(List<TsUserRole> tsUserRoleList) {
		this.tsUserRoleList = tsUserRoleList;
	}

    @Transient
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByRoleId", orphanRemoval=true)
	public List<TsRolePower> getTsRolePowerList() {
		return tsRolePowerList;
	}

	public void setTsRolePowerList(List<TsRolePower> tsRolePowerList) {
		this.tsRolePowerList = tsRolePowerList;
	}

	@Column(name = "IF_SUPER_MANAGE_ROLE")
	public Integer getIfSuperManageRole() {
		return ifSuperManageRole;
	}

	public void setIfSuperManageRole(Integer ifSuperManageRole) {
		this.ifSuperManageRole = ifSuperManageRole;
	}

	@Column(name = "ROLE_CODE")
	public String getRoleCode() {
		return roleCode;
	}

	public void setRoleCode(String roleCode) {
		this.roleCode = roleCode;
	}

	@Transient
	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	@Transient
	public boolean isDisabled() {
		return disabled;
	}

	public void setDisabled(boolean disabled) {
		this.disabled = disabled;
	}
}