package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * TsUserDesk entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_USER_DESK")
@SequenceGenerator(name = "TsUserDesk_seq", sequenceName = "TS_USER_DESK_SEQ", allocationSize = 1)
public class TsUserDesk implements java.io.Serializable {

	private static final long serialVersionUID = -5475304275643374364L;
	private Integer rid;
	private TsUserInfo tsUserInfo;
	private TsMenu tsMenu;
	private Short deskNo;
	private Integer orderNo;
	private Date createDate;
	private Integer createManid;

	public TsUserDesk() {
	}

	public TsUserDesk(Integer rid, TsMenu tsMenu, Date createDate,
			Integer createManid) {
		this.rid = rid;
		this.tsMenu = tsMenu;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsUserDesk_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "USER_ID")
	public TsUserInfo getTsUserInfo() {
		return this.tsUserInfo;
	}

	public void setTsUserInfo(TsUserInfo tsUserInfo) {
		this.tsUserInfo = tsUserInfo;
	}

	@ManyToOne
	@JoinColumn(name = "MENU_ID" )
	public TsMenu getTsMenu() {
		return this.tsMenu;
	}

	public void setTsMenu(TsMenu tsMenu) {
		this.tsMenu = tsMenu;
	}

	@Column(name = "DESK_NO", precision = 2, scale = 0)
	public Short getDeskNo() {
		return this.deskNo;
	}

	public void setDeskNo(Short deskNo) {
		this.deskNo = deskNo;
	}

	@Column(name = "ORDER_NO", precision = 4, scale = 0)
	public Integer getOrderNo() {
		return this.orderNo;
	}

	public void setOrderNo(Integer orderNo) {
		this.orderNo = orderNo;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

}