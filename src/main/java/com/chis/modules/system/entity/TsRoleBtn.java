package com.chis.modules.system.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 描述
 *
 * <AUTHOR>
 * @createdate 2015-8-28
 */
@Entity
@Table(name = "TS_ROLE_BTN")
@SequenceGenerator(name = "TsRoleBtn_seq", sequenceName = "TS_ROLE_BTN_SEQ", allocationSize = 1)
public class TsRoleBtn implements Serializable {
    private static final long serialVersionUID = -40113008711371363L;

    private Integer rid;
    private TsRole tsRole;
    private TsMenuBtn tsMenuBtn;

    public TsRoleBtn() {
    }

    public TsRoleBtn(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID", unique = true , precision = 22, scale = 0)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsRoleBtn_seq")
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "ROLE_ID")
    public TsRole getTsRole() {
        return tsRole;
    }

    public void setTsRole(TsRole tsRole) {
        this.tsRole = tsRole;
    }

    @ManyToOne
    @JoinColumn(name = "BTN_ID")
    public TsMenuBtn getTsMenuBtn() {
        return tsMenuBtn;
    }

    public void setTsMenuBtn(TsMenuBtn tsMenuBtn) {
        this.tsMenuBtn = tsMenuBtn;
    }
}
