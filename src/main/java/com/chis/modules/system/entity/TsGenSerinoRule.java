package com.chis.modules.system.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2019-11-30
 */
@Entity
@Table(name = "TS_GEN_SERINO_RULE")
@SequenceGenerator(name = "TsGenSerinoRule_seq", sequenceName = "TS_GEN_SERINO_RULE_SEQ",allocationSize=1)
public class TsGenSerinoRule implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer busType;
	private String prefix;
	private Integer currSerino;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TsGenSerinoRule() {
	}

	public TsGenSerinoRule(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsGenSerinoRule_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "BUS_TYPE")	
	public Integer getBusType() {
		return busType;
	}

	public void setBusType(Integer busType) {
		this.busType = busType;
	}	
			
	@Column(name = "PREFIX")	
	public String getPrefix() {
		return prefix;
	}

	public void setPrefix(String prefix) {
		this.prefix = prefix;
	}	
			
	@Column(name = "CURR_SERINO")	
	public Integer getCurrSerino() {
		return currSerino;
	}

	public void setCurrSerino(Integer currSerino) {
		this.currSerino = currSerino;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}