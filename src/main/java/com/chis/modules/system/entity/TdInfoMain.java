package com.chis.modules.system.entity;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.validator.constraints.NotBlank;

/**
 * 寻呼信息
 * 
 * <AUTHOR>
 * @history 2014-07-15
 */
@Entity
@Table(name = "TD_INFO_MAIN")
@SequenceGenerator(name = "TdInfoMain_seq", sequenceName = "TD_INFO_MAIN_SEQ", allocationSize = 1)
public class TdInfoMain implements java.io.Serializable {

	private Integer rid;
	private TsUserInfo tsUserInfo;// 发布人
	private String infoTitle;// 标题
	private String infoContent;// 内容
	private Date publishTime;// 发布时间
	private List<TdInfoSub> tdInfoSubs = new LinkedList<TdInfoSub>();// 寻呼信息子表
	private List<TdInfoAnnex> tdInfoAnnexes = new LinkedList<TdInfoAnnex>();// 寻呼信息附件
	private List<TdMsgMain> tdMsgMains = new LinkedList<TdMsgMain>();// 消息主表

	public TdInfoMain() {
	}

	public TdInfoMain(Integer rid) {
		this.rid = rid;
	}

	public TdInfoMain(Integer rid, TsUserInfo tsUserInfo, String infoTitle,
			String infoContent, Date publishTime, List<TdInfoSub> tdInfoSubs,
			List<TdInfoAnnex> tdInfoAnnexes, List<TdMsgMain> tdMsgMains) {
		this.rid = rid;
		this.tsUserInfo = tsUserInfo;
		this.infoTitle = infoTitle;
		this.infoContent = infoContent;
		this.publishTime = publishTime;
		this.tdInfoSubs = tdInfoSubs;
		this.tdInfoAnnexes = tdInfoAnnexes;
		this.tdMsgMains = tdMsgMains;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdInfoMain_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "PUBLISH_MAN" )
	public TsUserInfo getTsUserInfo() {
		return this.tsUserInfo;
	}

	public void setTsUserInfo(TsUserInfo tsUserInfo) {
		this.tsUserInfo = tsUserInfo;
	}


	@Column(name = "INFO_TITLE" , length = 200)
	public String getInfoTitle() {
		return this.infoTitle;
	}

	public void setInfoTitle(String infoTitle) {
		this.infoTitle = infoTitle;
	}


	@Column(name = "INFO_CONTENT")
	public String getInfoContent() {
		return this.infoContent;
	}

	public void setInfoContent(String infoContent) {
		this.infoContent = infoContent;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "PUBLISH_TIME" , length = 7)
	public Date getPublishTime() {
		return this.publishTime;
	}

	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdInfoMain")
	public List<TdInfoSub> getTdInfoSubs() {
		return this.tdInfoSubs;
	}

	public void setTdInfoSubs(List<TdInfoSub> tdInfoSubs) {
		this.tdInfoSubs = tdInfoSubs;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdInfoMain")
	public List<TdInfoAnnex> getTdInfoAnnexes() {
		return this.tdInfoAnnexes;
	}

	public void setTdInfoAnnexes(List<TdInfoAnnex> tdInfoAnnexes) {
		this.tdInfoAnnexes = tdInfoAnnexes;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdInfoMain")
	public List<TdMsgMain> getTdMsgMains() {
		return this.tdMsgMains;
	}

	public void setTdMsgMains(List<TdMsgMain> tdMsgMains) {
		this.tdMsgMains = tdMsgMains;
	}

}