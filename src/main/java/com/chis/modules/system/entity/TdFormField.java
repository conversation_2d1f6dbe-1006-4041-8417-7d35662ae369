package com.chis.modules.system.entity;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 
 * <AUTHOR>
 * @createTime 2015-12-3
 */
@Entity
@Table(name = "TD_FORM_FIELD")
@SequenceGenerator(name = "TdFormField", sequenceName = "TD_FORM_FIELD_SEQ", allocationSize = 1)
public class TdFormField implements java.io.Serializable, Cloneable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdFormTable tdFormTableByTableId;
	private String fdCnname;
	private String fdEnname;
	private String fdDbtype;
	private Integer lenChar = 0;
	private Integer lenInt = 0;
	private Integer lenDemi = 0;
	private Integer isReq = 0;
	private Integer isList = 1;
	private Integer isShow = 1;
	private Integer rowNum = 1;
	private Integer colNum = 1;
	private Integer colSpan = 1;
	private String dataSrc;
	/**如果字段来源于码表，需要选择码表类型*/
	private String codeTypeNo;
	private String dataScript;
	private Integer isProVal = 0;

	/**界面字段输入的值*/
	private Object value;
	/**关联的码表*/
	private List<TsSimpleCode> codeList = new ArrayList<TsSimpleCode>(0);
	
	/** 字段单选项 */
	private boolean reqBol = false;
	private boolean listBol = true;
	private boolean proValBol = false;
	/** 字段类型 1：正常 2：新增 3：删除  */
	private Integer fieldType = 1;
	
	private String dataSrcStr;
	private String fdDbtypeStr;
	
	private Integer lenCharLast = 0;
	private Integer lenIntLast = 0;
	private Integer lenDemiLast = 0;
	
	private String querySql ;
	private String dataFrom = "1";//数据来源，默认为码表，可以选择SQL方式（2）加载 
	/** 生成FreeMarker内容 */
	private String freeMarkerStr;
	private String freeMarkerModelStr;
	private String ifNewLine = "1";//是否新启一行，默认新启一行
	
	/** 脚本类型 */
	private Integer scriptType;
	/** 是否查询条件 */
	private Integer isSearch;
	private boolean ifSearch = false;
	/** 查询类型 */
	private Integer searchType;
	/** 排序类型 */
	private Integer orderType;
	/** 聚合类型 */
	private Integer convergeType;
	/**拼接sql使用*/
	private String fieldSql;
	
	private String fjPath;
	private String fjName;
	
	
	public TdFormField() {
	}

	public TdFormField(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdFormField")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "TABLE_ID")			
	public TdFormTable getTdFormTableByTableId() {
		return tdFormTableByTableId;
	}

	public void setTdFormTableByTableId(TdFormTable tdFormTableByTableId) {
		this.tdFormTableByTableId = tdFormTableByTableId;
	}	
			
	@Column(name = "FD_CNNAME")	
	public String getFdCnname() {
		return fdCnname;
	}

	public void setFdCnname(String fdCnname) {
		this.fdCnname = fdCnname;
	}	
			
	@Column(name = "FD_ENNAME")	
	public String getFdEnname() {
		return fdEnname;
	}

	public void setFdEnname(String fdEnname) {
		this.fdEnname = fdEnname;
	}	
			
	@Column(name = "FD_DBTYPE")	
	public String getFdDbtype() {
		return fdDbtype;
	}

	public void setFdDbtype(String fdDbtype) {
		this.fdDbtype = fdDbtype;
	}	
			
	@Column(name = "LEN_CHAR")	
	public Integer getLenChar() {
		return lenChar;
	}

	public void setLenChar(Integer lenChar) {
		this.lenChar = lenChar;
	}	
			
	@Column(name = "LEN_INT")	
	public Integer getLenInt() {
		return lenInt;
	}

	public void setLenInt(Integer lenInt) {
		this.lenInt = lenInt;
	}	
			
	@Column(name = "LEN_DEMI")	
	public Integer getLenDemi() {
		return lenDemi;
	}

	public void setLenDemi(Integer lenDemi) {
		this.lenDemi = lenDemi;
	}	
			
	@Column(name = "IS_REQ")	
	public Integer getIsReq() {
		return isReq;
	}

	public void setIsReq(Integer isReq) {
		this.isReq = isReq;
	}	
			
	@Column(name = "IS_LIST")	
	public Integer getIsList() {
		return isList;
	}

	public void setIsList(Integer isList) {
		this.isList = isList;
	}	
			
	@Column(name = "IS_SHOW")	
	public Integer getIsShow() {
		return isShow;
	}

	public void setIsShow(Integer isShow) {
		this.isShow = isShow;
	}	
			
	@Column(name = "ROW_NUM")	
	public Integer getRowNum() {
		return rowNum;
	}

	public void setRowNum(Integer rowNum) {
		this.rowNum = rowNum;
	}	
			
	@Column(name = "COL_NUM")	
	public Integer getColNum() {
		return colNum;
	}

	public void setColNum(Integer colNum) {
		this.colNum = colNum;
	}	
			
	@Column(name = "DATA_SRC")	
	public String getDataSrc() {
		return dataSrc;
	}

	public void setDataSrc(String dataSrc) {
		this.dataSrc = dataSrc;
	}
			
	@Column(name = "CODE_TYPE_NO")	
	public String getCodeTypeNo() {
		return codeTypeNo;
	}

	public void setCodeTypeNo(String codeTypeNo) {
		this.codeTypeNo = codeTypeNo;
	}	
			
	@Column(name = "DATA_SCRIPT")	
	public String getDataScript() {
		return dataScript;
	}

	public void setDataScript(String dataScript) {
		this.dataScript = dataScript;
	}	
			
	@Column(name = "IS_PRO_VAL")	
	public Integer getIsProVal() {
		return isProVal;
	}

	public void setIsProVal(Integer isProVal) {
		this.isProVal = isProVal;
	}	
			
	@Column(name = "COL_SPAN")	
	public Integer getColSpan() {
		return colSpan;
	}

	public void setColSpan(Integer colSpan) {
		this.colSpan = colSpan;
	}

	@Transient
	public Object getValue() {
		return value;
	}

	public void setValue(Object value) {
		this.value = value;
	}

	@Transient
	public List<TsSimpleCode> getCodeList() {
		return codeList;
	}

	public void setCodeList(List<TsSimpleCode> codeList) {
		this.codeList = codeList;
	}

	@Transient
	public boolean isReqBol() {
		return reqBol;
	}

	public void setReqBol(boolean reqBol) {
		this.reqBol = reqBol;
	}

	@Transient
	public boolean isListBol() {
		return listBol;
	}

	public void setListBol(boolean listBol) {
		this.listBol = listBol;
	}

	@Transient
	public boolean isProValBol() {
		return proValBol;
	}

	public void setProValBol(boolean proValBol) {
		this.proValBol = proValBol;
	}

	@Transient
	public Integer getFieldType() {
		return fieldType;
	}

	public void setFieldType(Integer fieldType) {
		this.fieldType = fieldType;
	}

	@Transient
	public String getDataSrcStr() {
		return dataSrcStr;
	}

	public void setDataSrcStr(String dataSrcStr) {
		this.dataSrcStr = dataSrcStr;
	}

	@Transient
	public String getFdDbtypeStr() {
		return fdDbtypeStr;
	}

	public void setFdDbtypeStr(String fdDbtypeStr) {
		this.fdDbtypeStr = fdDbtypeStr;
	}	
	
	@Override
	public Object clone() {
		TdFormField o = null;
		try {
			o = (TdFormField) super.clone();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return o;
	}

	@Transient
	public Integer getLenCharLast() {
		return lenCharLast;
	}

	public void setLenCharLast(Integer lenCharLast) {
		this.lenCharLast = lenCharLast;
	}

	@Transient
	public Integer getLenIntLast() {
		return lenIntLast;
	}

	public void setLenIntLast(Integer lenIntLast) {
		this.lenIntLast = lenIntLast;
	}

	@Transient
	public Integer getLenDemiLast() {
		return lenDemiLast;
	}

	public void setLenDemiLast(Integer lenDemiLast) {
		this.lenDemiLast = lenDemiLast;
	}

	@Column(name="QUERY_SQL")
	public String getQuerySql() {
		return querySql;
	}

	public void setQuerySql(String querySql) {
		this.querySql = querySql;
	}

	@Transient
	public String getDataFrom() {
		return dataFrom;
	}

	public void setDataFrom(String dataFrom) {
		this.dataFrom = dataFrom;
	}

	@Transient
	public String getFreeMarkerStr() {
		return freeMarkerStr;
	}

	public void setFreeMarkerStr(String freeMarkerStr) {
		this.freeMarkerStr = freeMarkerStr;
	}

	@Transient
	public String getFreeMarkerModelStr() {
		return freeMarkerModelStr;
	}

	public void setFreeMarkerModelStr(String freeMarkerModelStr) {
		this.freeMarkerModelStr = freeMarkerModelStr;
	}

	@Transient
	public String getIfNewLine() {
		return ifNewLine;
	}

	public void setIfNewLine(String ifNewLine) {
		this.ifNewLine = ifNewLine;
	}

	@Column(name="SCRIPT_TYPE")
	public Integer getScriptType() {
		return scriptType;
	}

	public void setScriptType(Integer scriptType) {
		this.scriptType = scriptType;
	}
	
    @Column(name="IS_SEARCH")
	public Integer getIsSearch() {
		return isSearch;
	}

	public void setIsSearch(Integer isSearch) {
		this.isSearch = isSearch;
	}

	@Column(name="SEARCH_TYPE")
	public Integer getSearchType() {
		return searchType;
	}

	public void setSearchType(Integer searchType) {
		this.searchType = searchType;
	}

	@Transient
	public boolean isIfSearch() {
		return ifSearch;
	}

	public void setIfSearch(boolean ifSearch) {
		this.ifSearch = ifSearch;
	}

	@Transient
	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	@Transient
	public Integer getConvergeType() {
		return convergeType;
	}

	public void setConvergeType(Integer convergeType) {
		this.convergeType = convergeType;
	}
	@Transient
	public String getFieldSql() {
		return fieldSql;
	}

	public void setFieldSql(String fieldSql) {
		this.fieldSql = fieldSql;
	}

	@Transient
	public String getFjPath() {
		return fjPath;
	}

	public void setFjPath(String fjPath) {
		this.fjPath = fjPath;
	}

	@Transient
	public String getFjName() {
		return fjName;
	}

	public void setFjName(String fjName) {
		this.fjName = fjName;
	}
	
	
	
}