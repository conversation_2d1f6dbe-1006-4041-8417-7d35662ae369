package com.chis.modules.system.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 通讯接收状态报告
 * 
 * <AUTHOR>
 * @创建时间 2014-11-25
 */
@Entity
@Table(name = "TS_TXRPT")
@SequenceGenerator(name = "TsTxrpt_Seq", sequenceName = "TS_TXRPT_SEQ", allocationSize = 1)
public class TsTxrpt implements java.io.Serializable {
	
	private static final long serialVersionUID = 2679528452964795542L;
	private Integer rid;
	private TsTxtype tsTxtype;
	private String msgId;
	private Short sendStatus;
	private Short receiveStatus;
	private Date receiveTime;
	private String msgCont;
	
	private String msgErr;//+语音未接听原因
	private Integer msgSeconds;//+语音接听时长
	//发给哪个用户了
	private Integer tarUserId; 
	private String mobile;

	// Constructors

	/** default constructor */
	public TsTxrpt() {
	}

	/** minimal constructor */
	public TsTxrpt(Integer rid) {
		this.rid = rid;
	}


	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsTxrpt_Seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "TX_ID" )
	public TsTxtype getTsTxtype() {
		return tsTxtype;
	}

	public void setTsTxtype(TsTxtype tsTxtype) {
		this.tsTxtype = tsTxtype;
	}
	
	@Column(name = "MSG_ID", length = 100)
	public String getMsgId() {
		return this.msgId;
	}
	
	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}
	
	@Column(name = "SEND_STATUS", precision = 1, scale = 0)
	public Short getSendStatus() {
		return this.sendStatus;
	}
	
	public void setSendStatus(Short sendStatus) {
		this.sendStatus = sendStatus;
	}
	
	@Column(name = "RECEIVE_STATUS", precision = 1, scale = 0)
	public Short getReceiveStatus() {
		return this.receiveStatus;
	}
	
	public void setReceiveStatus(Short receiveStatus) {
		this.receiveStatus = receiveStatus;
	}
	
	@Temporal(TemporalType.DATE)
	@Column(name = "RECEIVE_TIME", length = 7)
	public Date getReceiveTime() {
		return this.receiveTime;
	}
	
	public void setReceiveTime(Date receiveTime) {
		this.receiveTime = receiveTime;
	}
	
	@Column(name = "MSG_CONT", length = 200)
	public String getMsgCont() {
		return this.msgCont;
	}
	
	public void setMsgCont(String msgCont) {
		this.msgCont = msgCont;
	}

	@Column(name = "MSG_ERR", length = 200)
	public String getMsgErr() {
		return msgErr;
	}

	public void setMsgErr(String msgErr) {
		this.msgErr = msgErr;
	}

	@Column(name = "MSG_SECONDS")
	public Integer getMsgSeconds() {
		return msgSeconds;
	}

	public void setMsgSeconds(Integer msgSeconds) {
		this.msgSeconds = msgSeconds;
	}

	@Column(name = "TAR_USER_ID")
	public Integer getTarUserId() {
		return tarUserId;
	}

	public void setTarUserId(Integer tarUserId) {
		this.tarUserId = tarUserId;
	}

	@Column(name = "MOBILE")
	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	
	
}