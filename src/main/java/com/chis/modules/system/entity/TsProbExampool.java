package com.chis.modules.system.entity;

import com.chis.modules.system.enumn.QuestType;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 问卷题库[通用]
 * TsProbExampool entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_PROB_EXAMPOOL")
@SequenceGenerator(name = "TsProbExampool_seq", sequenceName = "TS_PROB_EXAMPOOL_SEQ", allocationSize = 1)
public class TsProbExampool implements java.io.Serializable {

	// Fields

	private Integer rid;
	private TsSimpleCode tsSimpleCodeByTypeId;
	private String qesCode;
	private String qesLevelCode;
	private String titleDesc;
	private QuestType questType;
	private Integer mustAsk;
	private Integer minSelectNum;
	private Integer optLayout;
	private Integer cols;
	private Integer state;
	private String otherDesc;
	private String otherImg;
	private Integer jumpType;
	private String jumpQuestCode;
	private Integer slideMinval;
	private Integer slideMaxval;
	private String slideMaxDesc;
	private String slideMinDesc;
	private Date createDate;
	private Integer createManid;
	private String invokeScrt;
	private List<TsProPoolOpt> tsProPoolOpts = new ArrayList<TsProPoolOpt>(0);
	private Integer poolDot;
	private Integer examTypeDot;
	
	private boolean selected;
	/**+填空是否多项*/
	private Integer isMulti;
	/**数字填空验证脚本**/
	private String fillMaxRange;
	
	private TbProPoolTabdefine fkByTableId;

	/** 得分脚本20210923 */
	private String scoreScript;
	/** 分值 */
	private BigDecimal optionScore;
	/** 附件地址 */
	private String annexAddr;

	private String showCode;
	/**参考答案*/
	private String ansDesc;

	private Integer prochkExtractQue;
	private BigDecimal totalScore;
	private BigDecimal score;

	private Boolean editScore;

	private Integer num;

	private Integer queNum;
	/**
	 * 难易程度
	 */
	private Integer hardLevel;
	private TsSimpleCode fkByHardLevelId;
	/** 页面脚本20231018 */
	private String execScript;

	// Constructors

	/** default constructor */
	public TsProbExampool() {
	}

	/** minimal constructor */
	public TsProbExampool(Integer rid, String qesCode, String qesLevelCode,
			String titleDesc, QuestType questType, Integer mustAsk,
			Integer state, Date createDate, Integer createManid) {
		this.rid = rid;
		this.qesCode = qesCode;
		this.qesLevelCode = qesLevelCode;
		this.titleDesc = titleDesc;
		this.questType = questType;
		this.mustAsk = mustAsk;
		this.state = state;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	/** full constructor */
	public TsProbExampool(Integer rid, TsSimpleCode tsSimpleCodeByTypeId,
			String qesCode, String qesLevelCode, String titleDesc,
			QuestType questType, Integer mustAsk, Integer minSelectNum,
			Integer optLayout, Integer cols, Integer state, String otherDesc,
			String otherImg, Integer jumpType, String jumpQuestCode,
			Integer slideMinval, Integer slideMaxval,
			String slideMaxDesc, String slideMinDesc, Date createDate,
			Integer createManid, List<TsProPoolOpt> tsProPoolOpts,String ansDesc) {
		this.rid = rid;
		this.tsSimpleCodeByTypeId=tsSimpleCodeByTypeId;
		this.qesCode = qesCode;
		this.qesLevelCode = qesLevelCode;
		this.titleDesc = titleDesc;
		this.questType = questType;
		this.mustAsk = mustAsk;
		this.minSelectNum = minSelectNum;
		this.optLayout = optLayout;
		this.cols = cols;
		this.state = state;
		this.otherDesc = otherDesc;
		this.otherImg = otherImg;
		this.jumpType = jumpType;
		this.jumpQuestCode = jumpQuestCode;
		this.slideMinval = slideMinval;
		this.slideMaxval = slideMaxval;
		this.slideMaxDesc = slideMaxDesc;
		this.slideMinDesc = slideMinDesc;
		this.createDate = createDate;
		this.createManid = createManid;
		this.tsProPoolOpts = tsProPoolOpts;
		this.ansDesc=ansDesc;
	}

	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsProbExampool_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Column(name = "QES_CODE" , length = 20)
	public String getQesCode() {
		return this.qesCode;
	}

	public void setQesCode(String qesCode) {
		this.qesCode = qesCode;
	}

	@Column(name = "QES_LEVEL_CODE" , length = 200)
	public String getQesLevelCode() {
		return this.qesLevelCode;
	}

	public void setQesLevelCode(String qesLevelCode) {
		this.qesLevelCode = qesLevelCode;
	}

	@Column(name = "TITLE_DESC" , length = 200)
	public String getTitleDesc() {
		return this.titleDesc;
	}

	public void setTitleDesc(String titleDesc) {
		this.titleDesc = titleDesc;
	}

	@Column(name = "QUEST_TYPE" , precision = 1, scale = 0)
	public QuestType getQuestType() {
		return this.questType;
	}

	public void setQuestType(QuestType questType) {
		this.questType = questType;
	}

	@Column(name = "MUST_ASK" , precision = 1, scale = 0)
	public Integer getMustAsk() {
		return this.mustAsk;
	}

	public void setMustAsk(Integer mustAsk) {
		this.mustAsk = mustAsk;
	}

	@Column(name = "MIN_SELECT_NUM", precision = 22, scale = 0)
	public Integer getMinSelectNum() {
		return this.minSelectNum;
	}

	public void setMinSelectNum(Integer minSelectNum) {
		this.minSelectNum = minSelectNum;
	}

	@Column(name = "OPT_LAYOUT", precision = 1, scale = 0)
	public Integer getOptLayout() {
		return this.optLayout;
	}

	public void setOptLayout(Integer optLayout) {
		this.optLayout = optLayout;
	}

	@Column(name = "COLS", precision = 2, scale = 0)
	public Integer getCols() {
		return this.cols;
	}

	public void setCols(Integer cols) {
		this.cols = cols;
	}

	@Column(name = "STATE" , precision = 1, scale = 0)
	public Integer getState() {
		return this.state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	@Column(name = "OTHER_DESC", length = 2000)
	public String getOtherDesc() {
		return this.otherDesc;
	}

	public void setOtherDesc(String otherDesc) {
		this.otherDesc = otherDesc;
	}

	@Column(name = "OTHER_IMG", length = 500)
	public String getOtherImg() {
		return this.otherImg;
	}

	public void setOtherImg(String otherImg) {
		this.otherImg = otherImg;
	}

	@Column(name = "JUMP_TYPE", precision = 1, scale = 0)
	public Integer getJumpType() {
		return this.jumpType;
	}

	public void setJumpType(Integer jumpType) {
		this.jumpType = jumpType;
	}

	@Column(name = "JUMP_QUEST_CODE", length = 200)
	public String getJumpQuestCode() {
		return this.jumpQuestCode;
	}

	public void setJumpQuestCode(String jumpQuestCode) {
		this.jumpQuestCode = jumpQuestCode;
	}

	@Column(name = "SLIDE_MINVAL", precision = 22, scale = 0)
	public Integer getSlideMinval() {
		return this.slideMinval;
	}

	public void setSlideMinval(Integer slideMinval) {
		this.slideMinval = slideMinval;
	}

	@Column(name = "SLIDE_MAXVAL", precision = 22, scale = 0)
	public Integer getSlideMaxval() {
		return this.slideMaxval;
	}

	public void setSlideMaxval(Integer slideMaxval) {
		this.slideMaxval = slideMaxval;
	}

	@Column(name = "SLIDE_MAX_DESC", length = 20)
	public String getSlideMaxDesc() {
		return this.slideMaxDesc;
	}

	public void setSlideMaxDesc(String slideMaxDesc) {
		this.slideMaxDesc = slideMaxDesc;
	}

	@Column(name = "SLIDE_MIN_DESC", length = 20)
	public String getSlideMinDesc() {
		return this.slideMinDesc;
	}

	public void setSlideMinDesc(String slideMinDesc) {
		this.slideMinDesc = slideMinDesc;
	}
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@OrderBy("num")
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsProbExampool",orphanRemoval=true)
	public List<TsProPoolOpt> getTsProPoolOpts() {
		return this.tsProPoolOpts;
	}

	public void setTsProPoolOpts(List<TsProPoolOpt> tsProPoolOpts) {
		this.tsProPoolOpts = tsProPoolOpts;
	}
	@Column(name = "INVOKE_SCRT")
	public String getInvokeScrt() {
		return invokeScrt;
	}

	public void setInvokeScrt(String invokeScrt) {
		this.invokeScrt = invokeScrt;
	}
	@Transient
	public Integer getPoolDot() {
		return poolDot;
	}

	public void setPoolDot(Integer poolDot) {
		this.poolDot = poolDot;
	}
	@Transient
	public Integer getExamTypeDot() {
		return examTypeDot;
	}

	public void setExamTypeDot(Integer examTypeDot) {
		this.examTypeDot = examTypeDot;
	}

	@Transient
	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}
	
	@Column(name = "IS_MULTI")
	public Integer getIsMulti() {
		return isMulti;
	}

	public void setIsMulti(Integer isMulti) {
		this.isMulti = isMulti;
	}
	@Column(name = "FILL_MAX_RANGE")
	public String getFillMaxRange() {
		return fillMaxRange;
	}

	public void setFillMaxRange(String fillMaxRange) {
		this.fillMaxRange = fillMaxRange;
	}
	
	@ManyToOne
	@JoinColumn(name = "TYPE_ID")
	public TsSimpleCode getTsSimpleCodeByTypeId() {
		return tsSimpleCodeByTypeId;
	}

	public void setTsSimpleCodeByTypeId(TsSimpleCode tsSimpleCodeByTypeId) {
		this.tsSimpleCodeByTypeId = tsSimpleCodeByTypeId;
	}
	
	@ManyToOne
	@JoinColumn(name = "TABLE_ID")			
	public TbProPoolTabdefine getFkByTableId() {
		return fkByTableId;
	}

	public void setFkByTableId(TbProPoolTabdefine fkByTableId) {
		this.fkByTableId = fkByTableId;
	}

	@Column(name = "SCORE_SCRIPT")
	public String getScoreScript() {
		return scoreScript;
	}

	public void setScoreScript(String scoreScript) {
		this.scoreScript = scoreScript;
	}

	@Column(name = "OPTION_SCORE")
	public BigDecimal getOptionScore() {
		return optionScore;
	}

	public void setOptionScore(BigDecimal optionScore) {
		this.optionScore = optionScore;
	}

	@Column(name = "ANNEX_ADDR")
	public String getAnnexAddr() {
		return annexAddr;
	}

	public void setAnnexAddr(String annexAddr) {
		this.annexAddr = annexAddr;
	}

	@Transient
	public String getShowCode() {
		return showCode;
	}

	public void setShowCode(String showCode) {
		this.showCode = showCode;
	}

	@Column(name = "ANS_DESC")
	public String getAnsDesc() {
		return ansDesc;
	}

	public void setAnsDesc(String ansDesc) {
		this.ansDesc = ansDesc;
	}

	@Transient
	public Integer getProchkExtractQue() {
		return prochkExtractQue;
	}

	public void setProchkExtractQue(Integer prochkExtractQue) {
		this.prochkExtractQue = prochkExtractQue;
	}

	@Transient
	public BigDecimal getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(BigDecimal totalScore) {
		this.totalScore = totalScore;
	}

	@Transient
	public BigDecimal getScore() {
		return score;
	}

	public void setScore(BigDecimal score) {
		this.score = score;
	}

	@Transient
	public Boolean getEditScore() {
		return editScore;
	}

	public void setEditScore(Boolean editScore) {
		this.editScore = editScore;
	}

	@Column(name = "NUM")
	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	@Transient
	public Integer getQueNum() {
		return queNum;
	}

	public void setQueNum(Integer queNum) {
		this.queNum = queNum;
	}

	@Column(name = "HARD_LEVEL")
	public Integer getHardLevel() {
		return hardLevel;
	}

	public void setHardLevel(Integer hardLevel) {
		this.hardLevel = hardLevel;
	}
	@ManyToOne
	@JoinColumn(name = "HARD_LEVEL_ID")
	public TsSimpleCode getFkByHardLevelId() {
		return fkByHardLevelId;
	}

	public void setFkByHardLevelId(TsSimpleCode fkByHardLevelId) {
		this.fkByHardLevelId = fkByHardLevelId;
	}

	@Column(name = "EXEC_SCRIPT")
	public String getExecScript() {
		return execScript;
	}

	public void setExecScript(String execScript) {
		this.execScript = execScript;
	}
}