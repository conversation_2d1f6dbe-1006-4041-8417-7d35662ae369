package com.chis.modules.system.entity;

import java.util.Date;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * TsProbExamtype entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_PROB_EXAMTYPE")
@SequenceGenerator(name = "TsProbExamtype_seq", sequenceName = "TS_PROB_EXAMTYPE_SEQ", allocationSize = 1)
public class TsProbExamtype implements java.io.Serializable {

	// Fields

	private Integer rid;
	private Integer paramType;
	private String typeCode;
	private String levelNp;
	private String typeName;
	private String rmk;
	private Date createDate;
	private Integer createManid;
	private Integer levelNum;//级别号长度
	/**
	 * 是否被选中
	 */
	private boolean selected;
	// Constructors

	/** default constructor */
	public TsProbExamtype() {
	}

	public TsProbExamtype(Integer rid) {
		super();
		this.rid = rid;
	}

	/** minimal constructor */
	public TsProbExamtype(Integer rid, String typeCode, String typeName,
			Date createDate, Integer createManid) {
		this.rid = rid;
		this.typeCode = typeCode;
		this.typeName = typeName;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	/** full constructor */
	public TsProbExamtype(Integer rid, String typeCode, String typeName,
			String rmk, Date createDate, Integer createManid,
			List<TsProbExampool> tsProbExampools) {
		this.rid = rid;
		this.typeCode = typeCode;
		this.typeName = typeName;
		this.rmk = rmk;
		this.createDate = createDate;
		this.createManid = createManid;
	}

	
	
	public TsProbExamtype(Integer rid, String typeName) {
		super();
		this.rid = rid;
		this.typeName = typeName;
	}
	
	

	public TsProbExamtype(Integer rid, String typeCode, String levelNp,
			String typeName) {
		super();
		this.rid = rid;
		this.typeCode = typeCode;
		this.levelNp = levelNp;
		this.typeName = typeName;
	}

	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsProbExamtype_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Column(name = "TYPE_CODE" , length = 50)
	public String getTypeCode() {
		return this.typeCode;
	}

	public void setTypeCode(String typeCode) {
		this.typeCode = typeCode;
	}

	@Column(name = "TYPE_NAME" , length = 50)
	public String getTypeName() {
		return this.typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	@Column(name = "RMK", length = 200)
	public String getRmk() {
		return this.rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}


	@Column(name = "PARAM_TYPE")
	public Integer getParamType() {
		return paramType;
	}

	public void setParamType(Integer paramType) {
		this.paramType = paramType;
	}

	@Column(name = "LEVEL_NP")
	public String getLevelNp() {
		return levelNp;
	}

	public void setLevelNp(String levelNp) {
		this.levelNp = levelNp;
	}

	@Transient
	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}
	@Transient
	public Integer getLevelNum() {
		return levelNum;
	}

	public void setLevelNum(Integer levelNum) {
		this.levelNum = levelNum;
	}

}