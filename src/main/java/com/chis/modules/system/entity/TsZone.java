package com.chis.modules.system.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * TsZone entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_ZONE")
@SequenceGenerator(name = "TsZone_seq", sequenceName = "TS_ZONE_SEQ", allocationSize = 1)
public class TsZone implements java.io.Serializable {

    private static final long serialVersionUID = -7377815965623553694L;
    private Integer rid;
    private String zoneGb;
    private String zoneName;
    private Short zoneType;
    private Short ifReveal;
    private Date stopDate;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    private Set<TsZoneMdref> tsZoneMdrefsForOldZoneid = new HashSet<TsZoneMdref>(0);
    private Set<TsZoneMdref> tsZoneMdrefsForNewZoneid = new HashSet<TsZoneMdref>(0);
    private Set<TsUnit> tsUnits = new HashSet<TsUnit>(0);

    private String zoneCode;
    private String fullName;
    //第三方编码
    private String dsfCode;
    private Short realZoneType;
    private String ifCityDirect;
    private String ifProvDirect;
    //+地区简称20220727
    private String zoneShortName;
    private Short state;
    private String errRsn;

    public TsZone() {
    }

    public TsZone(Integer rid) {
        this.rid = rid;
    }

    public TsZone(Integer rid, String zoneGb, String zoneName, Short zoneType) {
        this.rid = rid;
        this.zoneGb = zoneGb;
        this.zoneName = zoneName;
        this.zoneType = zoneType;
    }

    public TsZone(Integer rid, String zoneGb, String zoneName, Short zoneType, Short realZoneType) {
        this.rid = rid;
        this.zoneGb = zoneGb;
        this.zoneName = zoneName;
        this.zoneType = zoneType;
        this.realZoneType = realZoneType;
    }

    public TsZone(Integer rid, String zoneGb, String zoneName, Short zoneType, String zoneCode) {
        this.rid = rid;
        this.zoneGb = zoneGb;
        this.zoneName = zoneName;
        this.zoneType = zoneType;
        this.zoneCode = zoneCode;
    }

    public TsZone(Integer rid, String zoneGb, String zoneName, Short zoneType, String zoneCode, String dsfCode) {
        this.rid = rid;
        this.zoneGb = zoneGb;
        this.zoneName = zoneName;
        this.zoneType = zoneType;
        this.zoneCode = zoneCode;
        this.dsfCode = dsfCode;
    }

    public TsZone(Integer rid, String zoneGb, String zoneName, Short zoneType, String zoneCode, String dsfCode, String fullName) {
        this.rid = rid;
        this.zoneGb = zoneGb;
        this.zoneName = zoneName;
        this.zoneType = zoneType;
        this.zoneCode = zoneCode;
        this.dsfCode = dsfCode;
        this.fullName = fullName;
    }

    public TsZone(Integer rid, String zoneGb, String zoneName, Short zoneType, String zoneCode, String dsfCode, String fullName, String ifCityDirect) {
        this.rid = rid;
        this.zoneGb = zoneGb;
        this.zoneName = zoneName;
        this.zoneType = zoneType;
        this.zoneCode = zoneCode;
        this.dsfCode = dsfCode;
        this.fullName = fullName;
        this.ifCityDirect = ifCityDirect;
    }

    /**
     * minimal constructor
     */
    public TsZone(Integer rid, String zoneGb, String zoneName, Short zoneType, Short ifReveal, Date createDate, Integer createManid) {
        this.rid = rid;
        this.zoneGb = zoneGb;
        this.zoneName = zoneName;
        this.zoneType = zoneType;
        this.ifReveal = ifReveal;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    /**
     * full constructor
     */
    public TsZone(Integer rid, String zoneGb, String zoneName, Short zoneType, Short ifReveal, Date stopDate, Date createDate, Integer createManid, Date modifyDate, Integer modifyManid,
                  Set<TsZoneMdref> tsZoneMdrefsForOldZoneid, Set<TsZoneMdref> tsZoneMdrefsForNewZoneid, Set<TsUnit> tsUnits) {
        this.rid = rid;
        this.zoneGb = zoneGb;
        this.zoneName = zoneName;
        this.zoneType = zoneType;
        this.ifReveal = ifReveal;
        this.stopDate = stopDate;
        this.createDate = createDate;
        this.createManid = createManid;
        this.modifyDate = modifyDate;
        this.modifyManid = modifyManid;
        this.tsZoneMdrefsForOldZoneid = tsZoneMdrefsForOldZoneid;
        this.tsZoneMdrefsForNewZoneid = tsZoneMdrefsForNewZoneid;
        this.tsUnits = tsUnits;
    }

    // Property accessors
    @Id
    @Column(name = "RID", unique = true, precision = 22, scale = 0)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsZone_seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Column(name = "ZONE_GB", unique = true, length = 10)
    public String getZoneGb() {
        return this.zoneGb;
    }

    public void setZoneGb(String zoneGb) {
        this.zoneGb = zoneGb;
    }

    @Column(name = "ZONE_NAME", length = 50)
    public String getZoneName() {
        return this.zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    @Column(name = "ZONE_TYPE", precision = 1, scale = 0)
    public Short getZoneType() {
        return this.zoneType;
    }

    public void setZoneType(Short zoneType) {
        this.zoneType = zoneType;
    }

    @Column(name = "IF_REVEAL", precision = 1, scale = 0)
    public Short getIfReveal() {
        return this.ifReveal;
    }

    public void setIfReveal(Short ifReveal) {
        this.ifReveal = ifReveal;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "STOP_DATE", length = 7)
    public Date getStopDate() {
        return this.stopDate;
    }

    public void setStopDate(Date stopDate) {
        this.stopDate = stopDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE", length = 11)
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID", precision = 22, scale = 0)
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE", length = 11)
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID", precision = 22, scale = 0)
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsZoneByOldZoneid")
    public Set<TsZoneMdref> getTsZoneMdrefsForOldZoneid() {
        return this.tsZoneMdrefsForOldZoneid;
    }

    public void setTsZoneMdrefsForOldZoneid(Set<TsZoneMdref> tsZoneMdrefsForOldZoneid) {
        this.tsZoneMdrefsForOldZoneid = tsZoneMdrefsForOldZoneid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsZoneByNewZoneid")
    public Set<TsZoneMdref> getTsZoneMdrefsForNewZoneid() {
        return this.tsZoneMdrefsForNewZoneid;
    }

    public void setTsZoneMdrefsForNewZoneid(Set<TsZoneMdref> tsZoneMdrefsForNewZoneid) {
        this.tsZoneMdrefsForNewZoneid = tsZoneMdrefsForNewZoneid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsZone")
    public Set<TsUnit> getTsUnits() {
        return this.tsUnits;
    }

    public void setTsUnits(Set<TsUnit> tsUnits) {
        this.tsUnits = tsUnits;
    }

    @Column(name = "ZONE_CODE")
    public String getZoneCode() {
        return zoneCode;
    }

    public void setZoneCode(String zoneCode) {
        this.zoneCode = zoneCode;
    }

    @Column(name = "FULL_NAME")
    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    @Column(name = "DSF_CODE")
    public String getDsfCode() {
        return dsfCode;
    }

    public void setDsfCode(String dsfCode) {
        this.dsfCode = dsfCode;
    }

    @Column(name = "REAL_ZONE_TYPE", precision = 1, scale = 0)
    public Short getRealZoneType() {
        return realZoneType;
    }

    public void setRealZoneType(Short realZoneType) {
        this.realZoneType = realZoneType;
    }

    @Column(name = "IF_CITY_DIRECT")
    public String getIfCityDirect() {
        return ifCityDirect;
    }

    public void setIfCityDirect(String ifCityDirect) {
        this.ifCityDirect = ifCityDirect;
    }

    @Column(name = "IF_PROV_DIRECT")
    public String getIfProvDirect() {
        return ifProvDirect;
    }

    public void setIfProvDirect(String ifProvDirect) {
        this.ifProvDirect = ifProvDirect;
    }

    @Column(name = "ZONE_SHORT_NAME")
    public String getZoneShortName() {
        return zoneShortName;
    }

    public void setZoneShortName(String zoneShortName) {
        this.zoneShortName = zoneShortName;
    }

    @Column(name = "STATE")
    public Short getState() {
        return state;
    }

    public void setState(Short state) {
        this.state = state;
    }

    @Column(name = "ERR_RSN")
    public String getErrRsn() {
        return errRsn;
    }

    public void setErrRsn(String errRsn) {
        this.errRsn = errRsn;
    }
}