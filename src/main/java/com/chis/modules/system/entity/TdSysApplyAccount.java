package com.chis.modules.system.entity;


import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-7-30
 */
@Entity
@Table(name = "TD_SYS_APPLY_ACCOUNT")
@SequenceGenerator(name = "TdSysApplyAccount", sequenceName = "TD_SYS_APPLY_ACCOUNT_SEQ", allocationSize = 1)
public class TdSysApplyAccount implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsZone fkByZoneId;
	private String unitname;
	private String creditCode;
	private String unitTel;
	private String username;
	private String mobileNum;
	private String idc;
	private String userNo;
	private String password;
	private Integer stateMark;
	private TsUserInfo fkByCheckUserId;
	private String backRsn;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	//是否分支机构
	private Integer ifSubOrg;
	//申请材料
	private List<TdSysApplyAnnex> applyAnnexList;
	//单位属性
	private List<TdSysApplySort> applySortList;

	//临时身份证号码
	private String tempIdc;

	public TdSysApplyAccount() {
	}

	public TdSysApplyAccount(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdSysApplyAccount")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ZONE_ID")			
	public TsZone getFkByZoneId() {
		return fkByZoneId;
	}

	public void setFkByZoneId(TsZone fkByZoneId) {
		this.fkByZoneId = fkByZoneId;
	}	
			
	@Column(name = "UNITNAME")	
	public String getUnitname() {
		return unitname;
	}

	public void setUnitname(String unitname) {
		this.unitname = unitname;
	}	
			
	@Column(name = "CREDIT_CODE")	
	public String getCreditCode() {
		return creditCode;
	}

	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}	
			
	@Column(name = "UNIT_TEL")	
	public String getUnitTel() {
		return unitTel;
	}

	public void setUnitTel(String unitTel) {
		this.unitTel = unitTel;
	}	
			
	@Column(name = "USERNAME")	
	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}	
			
	@Column(name = "MOBILE_NUM")	
	public String getMobileNum() {
		return mobileNum;
	}

	public void setMobileNum(String mobileNum) {
		this.mobileNum = mobileNum;
	}	
			
	@Column(name = "IDC")	
	public String getIdc() {
		return idc;
	}

	public void setIdc(String idc) {
		this.idc = idc;
	}	
			
	@Column(name = "USER_NO")	
	public String getUserNo() {
		return userNo;
	}

	public void setUserNo(String userNo) {
		this.userNo = userNo;
	}	
			
	@Column(name = "PASSWORD")	
	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}	
			
	@Column(name = "STATE_MARK")	
	public Integer getStateMark() {
		return stateMark;
	}

	public void setStateMark(Integer stateMark) {
		this.stateMark = stateMark;
	}	
			
	@ManyToOne
	@JoinColumn(name = "CHECK_USER_ID")			
	public TsUserInfo getFkByCheckUserId() {
		return fkByCheckUserId;
	}

	public void setFkByCheckUserId(TsUserInfo fkByCheckUserId) {
		this.fkByCheckUserId = fkByCheckUserId;
	}	
			
	@Column(name = "BACK_RSN")	
	public String getBackRsn() {
		return backRsn;
	}

	public void setBackRsn(String backRsn) {
		this.backRsn = backRsn;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId" ,orphanRemoval = true)
	public List<TdSysApplyAnnex> getApplyAnnexList() {
		return applyAnnexList;
	}

	public void setApplyAnnexList(List<TdSysApplyAnnex> applyAnnexList) {
		this.applyAnnexList = applyAnnexList;
	}

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId" ,orphanRemoval = true)
    public List<TdSysApplySort> getApplySortList() {
        return applySortList;
    }

    public void setApplySortList(List<TdSysApplySort> applySortList) {
        this.applySortList = applySortList;
    }

	@Column(name = "IF_SUB_ORG")
	public Integer getIfSubOrg() {
		return ifSubOrg;
	}

	public void setIfSubOrg(Integer ifSubOrg) {
		this.ifSubOrg = ifSubOrg;
	}

	@Transient
	public String getTempIdc() {
		return tempIdc;
	}

	public void setTempIdc(String tempIdc) {
		this.tempIdc = tempIdc;
	}
}