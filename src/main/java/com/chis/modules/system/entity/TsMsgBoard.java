package com.chis.modules.system.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.NotBlank;

/**
 *  <p>类描述：留言板</p>
 *
 * @ClassAuthor maox,2018年6月4日,TsMsgBoard
 *
 */
@Entity
@Table(name = "TS_MSG_BOARD")
@SequenceGenerator(name = "TsMsgBoard_seq", sequenceName = "TS_MSG_BOARD_SEQ", allocationSize = 1)
public class TsMsgBoard {
	private Integer rid;
	private String linkMan;
	private String linkTel;
	private String feedbackMsg;
	private Date msgDate;
	private Integer createManID;
	private Date createDate;
	private Integer modifyManID;
	private Date modifyDate;
	
	private TsUserInfo tsUserInfo; 
	
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsMsgBoard_seq")
	public Integer getRid() {
		return rid;
	}
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@Column(name = "LINK_MAN")
	public String getLinkMan() {
		return linkMan;
	}
	public void setLinkMan(String linkMan) {
		this.linkMan = linkMan;
	}
	
	@Column(name = "LINK_TEL")
	public String getLinkTel() {
		return linkTel;
	}
	public void setLinkTel(String linkTel) {
		this.linkTel = linkTel;
	}
	
	@Column(name = "FEEDBACK_MSG")
	public String getFeedbackMsg() {
		return feedbackMsg;
	}
	public void setFeedbackMsg(String feedbackMsg) {
		this.feedbackMsg = feedbackMsg;
	}
	
	@Column(name = "MSG_DATE")
	public Date getMsgDate() {
		return msgDate;
	}
	public void setMsgDate(Date msgDate) {
		this.msgDate = msgDate;
	}
	
	@Column(name = "CREATE_MANID")
	public Integer getCreateManID() {
		return createManID;
	}
	public void setCreateManID(Integer createManID) {
		this.createManID = createManID;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE")
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
	@Column(name = "MODIFY_MANID")
	public Integer getModifyManID() {
		return modifyManID;
	}
	public void setModifyManID(Integer modifyManID) {
		this.modifyManID = modifyManID;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE")
	public Date getModifyDate() {
		return modifyDate;
	}
	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}
	
	@ManyToOne
	@JoinColumn(name = "SUBMIT_PSN" )
	public TsUserInfo getTsUserInfo() {
		return tsUserInfo;
	}
	public void setTsUserInfo(TsUserInfo tsUserInfo) {
		this.tsUserInfo = tsUserInfo;
	}
}
