package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.chis.modules.system.enumn.SystemType;

/**
 * 模板元素
 * <AUTHOR>
 */
@Entity
@Table(name = "TB_TEMPMETA_DEFINE")
@SequenceGenerator(name = "TbTempmetaDefine", sequenceName = "TB_TEMPMETA_DEFINE_SEQ", allocationSize = 1)
@NamedQueries({
    @NamedQuery(name = "TbTempmetaDefine.findAll", query = "SELECT t FROM TbTempmetaDefine t order by t.systemType"),
    @NamedQuery(name = "TbTempmetaDefine.findByMetaName", query = "SELECT t FROM TbTempmetaDefine t WHERE t.metaName=:metaName")
})
public class TbTempmetaDefine implements java.io.Serializable {

	private static final long serialVersionUID = 8840034881533099838L;

	private Integer rid;
	private SystemType systemType; 
	private String codeLevelNo;
	private String metaName;
	private String impClassname;
	private String demoDesc;
	private Date createDate;
	private Integer createManid;
	private String metaEname;

	public TbTempmetaDefine() {
	}

	public TbTempmetaDefine(Integer rid) {
		this.rid = rid;
	}
	
	public TbTempmetaDefine(SystemType systemType, String codeLevelNo, String metaName, String impClassname, String demoDesc, Date createDate, Integer createManid, String metaEname) {
		this.systemType = systemType;
		this.codeLevelNo = codeLevelNo;
		this.metaName = metaName;
		this.impClassname = impClassname;
		this.demoDesc = demoDesc;
		this.createDate = createDate;
		this.createManid = createManid;
		this.metaEname = metaEname;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTempmetaDefine")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
    @Enumerated
    @Column(name = "PARAM_TYPE" , precision = 2, scale = 0)
    public SystemType getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemType systemType) {
        this.systemType = systemType;
    }
    
    @Column(name = "CODE_LEVEL_NO")
	public String getCodeLevelNo() {
		return codeLevelNo;
	}

	public void setCodeLevelNo(String codeLevelNo) {
		this.codeLevelNo = codeLevelNo;
	}
	
	@Column(name = "META_NAME")
	public String getMetaName() {
		return metaName;
	}

	public void setMetaName(String metaName) {
		this.metaName = metaName;
	}
	
	@Column(name = "META_ENAME")
	public String getMetaEname() {
		return metaEname;
	}

	public void setMetaEname(String metaEname) {
		this.metaEname = metaEname;
	}

	@Column(name = "IMP_CLASSNAME")
	public String getImpClassname() {
		return impClassname;
	}

	public void setImpClassname(String impClassname) {
		this.impClassname = impClassname;
	}

	@Column(name = "DEMO_DESC")
	public String getDemoDesc() {
		return demoDesc;
	}

	public void setDemoDesc(String demoDesc) {
		this.demoDesc = demoDesc;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}


}