package com.chis.modules.system.entity;
import javax.persistence.*;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2016-11-2
 */
@Entity
@Table(name = "TS_KLEANX")
@SequenceGenerator(name = "TsKleanx", sequenceName = "TS_KLEANX_SEQ", allocationSize = 1)
public class TsKleanx implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsKlemain mainId;
	private String fileName;
	private String filePath;
	private Date createDate;
	private Integer createManid;
	
	public TsKleanx() {
	}

	public TsKleanx(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsKleanx")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID" )
	public TsKlemain getMainId() {
		return mainId;
	}

	public void setMainId(TsKlemain mainId) {
		this.mainId = mainId;
	}	
			
	@Column(name = "FILE_NAME")	
	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}	
			
	@Column(name = "FILE_PATH")	
	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
}