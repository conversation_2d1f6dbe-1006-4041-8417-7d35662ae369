package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/** 系统登录信息
 * <AUTHOR> 
 * @version 创建时间：2017年8月10日 上午9:06:13 
 * 
 */
@Entity
@Table(name = "TS_LOGIN_INFO")
@SequenceGenerator(name = "TsLoginInfo_seq", sequenceName = "TS_LOGIN_INFO_SEQ", allocationSize = 1)
public class TsLoginInfo implements java.io.Serializable{

	private static final long serialVersionUID = 5812571051596090122L;

	private Integer rid;
	private Date  happenDate;
	private String userNo;
	private String clientIp;
	private Date createDate;
	private Integer createManid;
	private Date  modifyDate;
	private Integer modifyManid;
	private Date  lastLoginTime;
	
	
	public TsLoginInfo() {
	}

    public TsLoginInfo(Integer rid) {
        this.rid = rid;
    }

    public TsLoginInfo( Integer rid,Date  happenDate,String userNo,String clientIp,Date createDate,
    		Integer createManid,Date  modifyDate,Integer modifyManid,Date  lastLoginTime) {
		this.rid = rid;
		this.happenDate = happenDate;
		this.userNo = userNo;
		this.clientIp = clientIp;
		this.modifyDate = modifyDate;
		this.modifyManid = modifyManid;
		this.createDate = createDate;
		this.createManid = createManid;
		this.lastLoginTime = lastLoginTime;
	}


	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsLoginInfo_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "HAPPEN_DATE" , length = 11)
	public Date getHappenDate() {
		return happenDate;
	}

	public void setHappenDate(Date happenDate) {
		this.happenDate = happenDate;
	}
	
	@Column(name = "USER_NO", unique = true , length = 20)
	public String getUserNo() {
		return userNo;
	}

	public void setUserNo(String userNo) {
		this.userNo = userNo;
	}
	@Column(name = "CLIENT_IP", unique = true , length = 50)
	public String getClientIp() {
		return clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_LOGIN_TIME" , length = 11)
	public Date getLastLoginTime() {
		return lastLoginTime;
	}

	public void setLastLoginTime(Date lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE" , length = 11)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "MODIFY_DATE", length = 11)
	public Date getModifyDate() {
		return this.modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	@Column(name = "MODIFY_MANID", precision = 22, scale = 0)
	public Integer getModifyManid() {
		return this.modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	
	
}
