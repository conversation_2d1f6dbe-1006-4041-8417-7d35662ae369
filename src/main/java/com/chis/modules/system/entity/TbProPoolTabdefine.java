package com.chis.modules.system.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * 题库表格题[通用]
 * 
 * <AUTHOR>
 * @createTime 2017-10-19
 */
@Entity
@Table(name = "TB_PRO_POOL_TABDEFINE")
@SequenceGenerator(name = "TbProPoolTabdefine", sequenceName = "TB_PRO_POOL_TABDEFINE_SEQ", allocationSize = 1)
public class TbProPoolTabdefine implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String tabName;
	private String rmk;
	private Date createDate;
	private Integer createManid;
	private Integer rowFixed;
	private Integer defaultLineNum;
	/** 列定义 */
	private List<TbProPoolColsdefine> colsdefines = new ArrayList<>(0);
	/** 行标题 */
	private List<TbProPoolRowtitle> rowtitles = new ArrayList<>(0);

	private List<TsProbExampool> pools = new ArrayList<>(0);

	public TbProPoolTabdefine() {
	}

	public TbProPoolTabdefine(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbProPoolTabdefine")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Column(name = "TAB_NAME")
	public String getTabName() {
		return tabName;
	}

	public void setTabName(String tabName) {
		this.tabName = tabName;
	}

	@Column(name = "RMK")
	public String getRmk() {
		return rmk;
	}

	public void setRmk(String rmk) {
		this.rmk = rmk;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATE_DATE")
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID")
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Column(name = "ROW_FIXED")
	public Integer getRowFixed() {
		return rowFixed;
	}

	public void setRowFixed(Integer rowFixed) {
		this.rowFixed = rowFixed;
	}

	@Column(name = "DEFAULT_LINE_NUM")
	public Integer getDefaultLineNum() {
		return defaultLineNum;
	}

	public void setDefaultLineNum(Integer defaultLineNum) {
		this.defaultLineNum = defaultLineNum;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByTableId", orphanRemoval = true)
	public List<TbProPoolColsdefine> getColsdefines() {
		return colsdefines;
	}

	public void setColsdefines(List<TbProPoolColsdefine> colsdefines) {
		this.colsdefines = colsdefines;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByTableId", orphanRemoval = true)
	public List<TbProPoolRowtitle> getRowtitles() {
		return rowtitles;
	}

	public void setRowtitles(List<TbProPoolRowtitle> rowtitles) {
		this.rowtitles = rowtitles;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByTableId", orphanRemoval = true)
	public List<TsProbExampool> getPools() {
		return pools;
	}

	public void setPools(List<TsProbExampool> pools) {
		this.pools = pools;
	}
}