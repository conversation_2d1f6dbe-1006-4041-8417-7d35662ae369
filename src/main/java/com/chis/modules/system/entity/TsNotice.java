package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-3-16
 */
@Entity
@Table(name = "TS_NOTICE")
@SequenceGenerator(name = "TsNotice", sequenceName = "TS_NOTICE_SEQ", allocationSize = 1)
public class TsNotice implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String noticeTitle;
	private Date noticeDate;
	private String filePath;
	private Integer stateMark;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	/**+是否发布20200330*/
	private Integer ifPublish;
	
	public TsNotice() {
	}

	public TsNotice(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsNotice")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "NOTICE_TITLE")	
	public String getNoticeTitle() {
		return noticeTitle;
	}

	public void setNoticeTitle(String noticeTitle) {
		this.noticeTitle = noticeTitle;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "NOTICE_DATE")			
	public Date getNoticeDate() {
		return noticeDate;
	}

	public void setNoticeDate(Date noticeDate) {
		this.noticeDate = noticeDate;
	}	
			
	@Column(name = "FILE_PATH")	
	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}	
			
	@Column(name = "STATE_MARK")	
	public Integer getStateMark() {
		return stateMark;
	}

	public void setStateMark(Integer stateMark) {
		this.stateMark = stateMark;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

    @Column(name = "IF_PUBLISH")
    public Integer getIfPublish() {
        return ifPublish;
    }

    public void setIfPublish(Integer ifPublish) {
        this.ifPublish = ifPublish;
    }
}