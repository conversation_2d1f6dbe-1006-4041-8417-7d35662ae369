package com.chis.modules.system.entity;

import javax.persistence.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 主表
 * <AUTHOR>
 * @createTime 2016-3-21
 */
@Entity
@Table(name = "TS_DSF_SYS")
@SequenceGenerator(name = "TsDsfSys", sequenceName = "TS_DSF_SYS_SEQ", allocationSize = 1)
public class TsDsfSys implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	/** RID */
	private Integer rid;
	/** 系统名称  */
	private String sysName;
	/**系统简称 */
	private String sysJc;
	/**系统图标 */
	private String sysIcon;
	/**系统类型  1：BS   2：CS */
	private Integer xtType;
	/**程序地址 */
	private String sysUrl;
	/** 创建日期 */
	private Date createDate;
	/** 创建人*/
	private Integer createManid;
	/** 修改日期*/
	private Date modifyDate;
	/**修改人 */
	private Integer modifyManid;

	 
	private List<TsDsfSysParam> list = new ArrayList<TsDsfSysParam>();
	
	public TsDsfSys() {
	}

	public TsDsfSys(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsDsfSys")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "SYS_NAME")	
	public String getSysName() {
		return sysName;
	}

	public void setSysName(String sysName) {
		this.sysName = sysName;
	}	
			
	@Column(name = "SYS_JC")	
	public String getSysJc() {
		return sysJc;
	}

	public void setSysJc(String sysJc) {
		this.sysJc = sysJc;
	}	
			
	@Column(name = "SYS_ICON")	
	public String getSysIcon() {
		return sysIcon;
	}

	public void setSysIcon(String sysIcon) {
		this.sysIcon = sysIcon;
	}	
			
	@Column(name = "XT_TYPE")	
	public Integer getXtType() {
		return xtType;
	}

	public void setXtType(Integer xtType) {
		this.xtType = xtType;
	}	
			
	@Column(name = "SYS_URL")	
	public String getSysUrl() {
		return sysUrl;
	}

	public void setSysUrl(String sysUrl) {
		this.sysUrl = sysUrl;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsDsfSysId",orphanRemoval = true)
	public List<TsDsfSysParam> getList() {
		return list;
	}

	public void setList(List<TsDsfSysParam> list) {
		this.list = list;
	}

	
}