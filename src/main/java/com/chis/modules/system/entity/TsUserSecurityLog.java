package com.chis.modules.system.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-3-20
 */
@Entity
@Table(name = "TS_USER_SECURITY_LOG")
@SequenceGenerator(name = "TsUserSecurityLog", sequenceName = "TS_USER_SECURITY_LOG_SEQ", allocationSize = 1)
public class TsUserSecurityLog implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String loginIp;
	private Date loginDate;
	private Date lockDate;
	private Integer ctuFailTimes;
	private String loginInfo;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TsUserSecurityLog() {
	}

	public TsUserSecurityLog(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsUserSecurityLog")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "LOGIN_IP")	
	public String getLoginIp() {
		return loginIp;
	}

	public void setLoginIp(String loginIp) {
		this.loginIp = loginIp;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "LOGIN_DATE")			
	public Date getLoginDate() {
		return loginDate;
	}

	public void setLoginDate(Date loginDate) {
		this.loginDate = loginDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "LOCK_DATE")			
	public Date getLockDate() {
		return lockDate;
	}

	public void setLockDate(Date lockDate) {
		this.lockDate = lockDate;
	}	
			
	@Column(name = "CTU_FAIL_TIMES")	
	public Integer getCtuFailTimes() {
		return ctuFailTimes;
	}

	public void setCtuFailTimes(Integer ctuFailTimes) {
		this.ctuFailTimes = ctuFailTimes;
	}	
			
	@Column(name = "LOGIN_INFO")	
	public String getLoginInfo() {
		return loginInfo;
	}

	public void setLoginInfo(String loginInfo) {
		this.loginInfo = loginInfo;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}