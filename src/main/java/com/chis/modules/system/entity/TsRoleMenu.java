package com.chis.modules.system.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * TsRoleMenu entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_ROLE_MENU", uniqueConstraints = @UniqueConstraint(columnNames = {
		"ROLE_ID", "MENU_ID" }))
@SequenceGenerator(name = "TsRoleMenu_seq", sequenceName = "TS_ROLE_MENU_SEQ", allocationSize = 1)
public class TsRoleMenu implements java.io.Serializable {

	// Fields

	private Integer rid;
	private TsRole tsRole;
	private TsMenu tsMenu;

	// Constructors

	/** default constructor */
	public TsRoleMenu() {
	}

	/** full constructor */
	public TsRoleMenu(Integer rid, TsRole tsRole, TsMenu tsMenu) {
		this.rid = rid;
		this.tsRole = tsRole;
		this.tsMenu = tsMenu;
	}

	// Property accessors
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsRoleMenu_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "ROLE_ID" )
	public TsRole getTsRole() {
		return this.tsRole;
	}

	public void setTsRole(TsRole tsRole) {
		this.tsRole = tsRole;
	}

	@ManyToOne
	@JoinColumn(name = "MENU_ID" )
	public TsMenu getTsMenu() {
		return this.tsMenu;
	}

	public void setTsMenu(TsMenu tsMenu) {
		this.tsMenu = tsMenu;
	}

}