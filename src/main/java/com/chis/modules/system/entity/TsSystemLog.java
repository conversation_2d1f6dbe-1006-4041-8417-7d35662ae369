package com.chis.modules.system.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * TsSystemLog entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_SYSTEM_LOG")
@SequenceGenerator(name = "TsSystemLog_seq", sequenceName = "TS_SYSTEM_LOG_SEQ", allocationSize = 1)
public class TsSystemLog implements java.io.Serializable {

	private static final long serialVersionUID = 2376049860772174516L;
	private Integer rid;
	private Date happenDate;
	private String userNo;
	private String menuId;
	private String errorNo;
	private String hintInfo;
	private String detailInfo;
	private String clientIp;

	public TsSystemLog() {
	}

	public TsSystemLog(Integer rid, Date happenDate) {
		this.rid = rid;
		this.happenDate = happenDate;
	}


	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsSystemLog_seq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "HAPPEN_DATE" , length = 11)
	public Date getHappenDate() {
		return this.happenDate;
	}

	public void setHappenDate(Date happenDate) {
		this.happenDate = happenDate;
	}

	@Column(name = "USER_NO", length = 20)
	public String getUserNo() {
		return this.userNo;
	}

	public void setUserNo(String userNo) {
		this.userNo = userNo;
	}

	@Column(name = "MENU_ID", length = 20)
	public String getMenuId() {
		return this.menuId;
	}

	public void setMenuId(String menuId) {
		this.menuId = menuId;
	}

	@Column(name = "ERROR_NO", length = 20)
	public String getErrorNo() {
		return this.errorNo;
	}

	public void setErrorNo(String errorNo) {
		this.errorNo = errorNo;
	}

	@Column(name = "HINT_INFO", length = 200)
	public String getHintInfo() {
		return hintInfo;
	}

	public void setHintInfo(String hintInfo) {
		this.hintInfo = hintInfo;
	}

	@Column(name = "DETAIL_INFO")
	public String getDetailInfo() {
		return this.detailInfo;
	}



	public void setDetailInfo(String detailInfo) {
		this.detailInfo = detailInfo;
	}

	@Column(name = "CLIENT_IP", length = 50)
	public String getClientIp() {
		return this.clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

}