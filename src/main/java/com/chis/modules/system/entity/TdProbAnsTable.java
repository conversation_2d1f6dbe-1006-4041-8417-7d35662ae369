package com.chis.modules.system.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-8-24
 */
@Entity
@Table(name = "TD_PROB_ANS_TABLE")
@SequenceGenerator(name = "TdProbAnsTable", sequenceName = "TD_PROB_ANS_TABLE_SEQ", allocationSize = 1)
public class TdProbAnsTable implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdProbAnsDetail fkBySurSubjectid;
	private TbProbColsdefine fkByColId;
	private Integer num;
	private String colValue;
	private Date createDate;
	private Integer createManid;
	
	public TdProbAnsTable() {
	}

	public TdProbAnsTable(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdProbAnsTable")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "SUR_SUBJECTID")			
	public TdProbAnsDetail getFkBySurSubjectid() {
		return fkBySurSubjectid;
	}

	public void setFkBySurSubjectid(TdProbAnsDetail fkBySurSubjectid) {
		this.fkBySurSubjectid = fkBySurSubjectid;
	}	
			
	@ManyToOne
	@JoinColumn(name = "COL_ID")			
	public TbProbColsdefine getFkByColId() {
		return fkByColId;
	}

	public void setFkByColId(TbProbColsdefine fkByColId) {
		this.fkByColId = fkByColId;
	}	
			
	@Column(name = "NUM")	
	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}	
			
	@Column(name = "COL_VALUE")	
	public String getColValue() {
		return colValue;
	}

	public void setColValue(String colValue) {
		this.colValue = colValue;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
}