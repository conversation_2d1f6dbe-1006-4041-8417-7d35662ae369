package com.chis.modules.system.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * TsUserRole entity. <AUTHOR> Persistence Tools
 */
@Entity
@Table(name = "TS_ROLE_CODEAUTH")
@SequenceGenerator(name = "TsRoleCodeauth", sequenceName = "TS_ROLE_CODEAUTH_SEQ", allocationSize = 1)
public class TsRoleCodeauth implements java.io.Serializable {

	private static final long serialVersionUID = 1195209375999319575L;
	private Integer rid;
	private TsCodeType tsCodeType;
	private TsRole tsRole;
    private Date createDate;
    private Integer createManid;

	public TsRoleCodeauth() {
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsRoleCodeauth")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

    @ManyToOne
    @JoinColumn(name = "CODE_TYPEID" )
    public TsCodeType getTsCodeType() {
        return this.tsCodeType;
    }

    public void setTsCodeType(TsCodeType tsCodeType) {
        this.tsCodeType = tsCodeType;
    }

	@ManyToOne
	@JoinColumn(name = "ROLE_ID" )
	public TsRole getTsRole() {
		return this.tsRole;
	}

	public void setTsRole(TsRole tsRole) {
		this.tsRole = tsRole;
	}

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" , length = 11)
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" , precision = 22, scale = 0)
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }
}