package com.chis.modules.system.entity;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.*;

import com.chis.modules.system.enumn.SystemType;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "TS_CODE_RULE")
@SequenceGenerator(name = "TsCodeRule", sequenceName = "TS_CODE_RULE_SEQ", allocationSize = 1)
public class TsCodeRule implements java.io.Serializable {

	// Fields

	private Integer rid;
	private String idcode;
    private SystemType systemType;
	private String bsDesc;
	private String pfx;
	private String splitStr;
	private String suf;
	private Short codeLenth;
	private String impClass;
	private String ruleDesc;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
	private Set<TsSysIds> tsSysIdses = new HashSet<TsSysIds>(0);
	private Integer paramType;

	public TsCodeRule() {
	}

    public TsCodeRule(Integer rid) {
        this.rid = rid;
    }

    /**
     * 不允许修改
     * @param idcode 编码
     * @param systemType 系统类型
     * @param bsDesc 业务描述
     * @param pfx 前缀
     * @param splitStr 分割符
     * @param suf 后缀
     * @param codeLenth 流水号长度
     * @param impClass 实现类全类名
     * @param createDate 创建日期
     * @param createManid 创建人
     */
    public TsCodeRule(String idcode, SystemType systemType, String bsDesc, String pfx, String splitStr, String suf, Short codeLenth, String impClass, Date createDate, Integer createManid) {
        this.idcode = idcode;
        this.systemType = systemType;
        this.bsDesc = bsDesc;
        this.pfx = pfx;
        this.splitStr = splitStr;
        this.suf = suf;
        this.codeLenth = codeLenth;
        this.impClass = impClass;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    @Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TsCodeRule")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Column(name = "IDCODE", unique = true , length = 50)
	public String getIdcode() {
		return this.idcode;
	}

	public void setIdcode(String idcode) {
		this.idcode = idcode;
	}

    @Enumerated
	@Transient
    //@Column(name = "PARAM_TYPE" , precision = 2, scale = 0)
    public SystemType getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemType systemType) {
        this.systemType = systemType;
    }

	@Column(name = "BS_DESC" , length = 50)
	public String getBsDesc() {
		return this.bsDesc;
	}

	public void setBsDesc(String bsDesc) {
		this.bsDesc = bsDesc;
	}

	@Column(name = "PFX", length = 20)
	public String getPfx() {
		return this.pfx;
	}

	public void setPfx(String pfx) {
		this.pfx = pfx;
	}

	@Column(name = "SPLIT_STR", length = 20)
	public String getSplitStr() {
		return this.splitStr;
	}

	public void setSplitStr(String splitStr) {
		this.splitStr = splitStr;
	}

	@Column(name = "SUF", length = 20)
	public String getSuf() {
		return this.suf;
	}

	public void setSuf(String suf) {
		this.suf = suf;
	}

    @Column(name = "CODE_LENTH" , precision = 2, scale = 0)
    public Short getCodeLenth() {
        return codeLenth;
    }

    public void setCodeLenth(Short codeLenth) {
        this.codeLenth = codeLenth;
    }

	@Column(name = "IMP_CLASS" , length = 200)
	public String getImpClass() {
		return this.impClass;
	}

	public void setImpClass(String impClass) {
		this.impClass = impClass;
	}

	@Column(name = "RULE_DESC" , length = 200)
	public String getRuleDesc() {
		return this.ruleDesc;
	}

	public void setRuleDesc(String ruleDesc) {
		this.ruleDesc = ruleDesc;
	}

    @Column(name = "CREATE_DATE" , length = 11)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" , precision = 22, scale = 0)
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Column(name = "MODIFY_DATE", length = 11)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID", precision = 22, scale = 0)
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tsCodeRule")
	public Set<TsSysIds> getTsSysIdses() {
		return this.tsSysIdses;
	}

	public void setTsSysIdses(Set<TsSysIds> tsSysIdses) {
		this.tsSysIdses = tsSysIdses;
	}

	@Column(name = "PARAM_TYPE" , precision = 2, scale = 0)
	public Integer getParamType() {
		return paramType;
	}

	public void setParamType(Integer paramType) {
		this.paramType = paramType;
	}
}