package com.chis.modules.system.utils;

import org.primefaces.component.datatable.DataTable;

import javax.faces.context.FacesContext;

/**
 * Primefaces工具类
 *
 * <AUTHOR>
 * @date 2022/5/12
 */
public class PrimefacesUtil {

    /**
     * 重定向表格页码
     *
     * @param tableId 表格ID
     * @param page    页码
     */
    public static void dataTablePage(String tableId, int page) {
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(tableId);
        dataTable.setFirst(page);
    }
}
