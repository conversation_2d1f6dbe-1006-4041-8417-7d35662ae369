package com.chis.modules.system.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SqlHandleUtil {

	/**
	 * 根据sql和参数清理查询sql和参数
	 * 例：SELECT * FROM A WHERE 1=1 AND T.TEST=:TEST GROUP BY T.A
	 * @param map
	 * @param sql
	 * @return
	 */
	public static String buildSql(Map<String,Object> map,String sql){
		sql = sql.toUpperCase();
		if(map == null)
			map = new HashMap<String,Object>();
		Map<String,Object> paramMap = new HashMap<String,Object>();
		List<String> sqlFilters = findSqlFilters(sql,true);
		for(String key:sqlFilters){
			//找到里对应占位
			Object obj = map.get(key);
			if(obj!=null && obj.toString()!="")
				paramMap.put(key, obj);
			else
				sql = clearFilter(sql,key);
		}
		map.clear();
		map.putAll(paramMap);
		return sql;
	}
	
	public static String clearFilter(String sql,String key){
		return sql.replaceFirst("AND\\s+[A-Za-z0-9.:\\s',_()-]+\\s*[=><A-Za-z]+[\\sA-Za-z|'(%,]*:"+key+"[|\\s,'%)]*", " ");
	}

	/**
	 * 获取sql中的占位数据，过滤掉：
	 * @param sql
	 * @return
	 */
	public static List<String> findSqlFilters(String sql,boolean ifFilter) {
		List<String> result = new ArrayList<String>();
		String regEx = ":[A-Za-z0-9]+[_]+[A-Za-z0-9_]+";
		Pattern pat = Pattern.compile(regEx);
		Matcher mat = pat.matcher(sql);
		while(mat.find()){
			String item = mat.group(0);
			if(ifFilter)
				item = item.replaceFirst(":", "");
			result.add(item);
		}
		return result;
	}
}
