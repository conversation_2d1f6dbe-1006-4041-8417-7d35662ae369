package com.chis.modules.system.utils;

import java.util.Random;

/**
 * <p>类描述：随机数工具类</p>
 *
 * @ClassAuthor mxp, 2017年11月22日, RandomUtil
 */
public class RandomUtil {
    private static char[] getChar() {
        return new char[]{ 'A', 'B', 'C', '7', 'D', 'a', 'E', 'G', 'p',
                '8', 'h', 'K', 'L', 'w', 'N', 'P', 'Q', 'R', 'S', '2',
                'T', 'U', 'V', 'W', '3', 'X', 'v', 'Z', 'b', 'c', '4', 'd',
                'e', 'f', 'J', 'k', 'm', 'n', 'H',
                'r', 'M', 's', 't', '6', 'u', 'Y', '5', 'x', 'F', 'z' };
    }

    /**
     * <p>方法描述：随机生成4位验证码</p>
     *
     * @MethodAuthor mxp,2017/11/22,initIdentifyingCode
     */
    public static char[] initIdentifyingCode(){
        char[] chars = new char[4];
        // 创建一个随机数生成器类
        Random random = new Random();
        for (int i = 0; i < 4; i++) {
            // 得到随机产生的验证码数字。
            chars[i] = getChar()[random.nextInt(getChar().length)];
        }
        return chars;
    }

}
