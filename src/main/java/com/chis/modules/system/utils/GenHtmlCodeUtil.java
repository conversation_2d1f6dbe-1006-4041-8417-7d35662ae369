package com.chis.modules.system.utils;

import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.chis.common.utils.EnumUtils;
import com.chis.modules.system.entity.TdFormField;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.enumn.DynaFieldShowType;
import com.chis.modules.system.enumn.DynaFieldType;
import com.chis.modules.system.enumn.SearchFlag;
import com.chis.modules.system.service.CommServiceImpl;

/**
 * 动态表单生成Html代码工具类
 * 
 * <AUTHOR>
 * @createDate 2015年12月14日
 */
public class GenHtmlCodeUtil {

	/** 动态表单主表ID前缀 */
	public final String DYNC_PREFIX_MAIN_TAB = "DYNA_MAIN";
	/** 动态表单主=子表ID前缀 */
	public final String DYNC_PREFIX_SUB_TAB = "DYNA_SUB";
	/** 动态表单主表ID前缀 */
	public static final String DYNC_PREFIX_MAIN_SEARCH_TAB = "DYNA_SEARCH_MAIN";
	/** 动态表单主表ID前缀 */
	public static final String DYNC_PREFIX_NEXT_MAIN_SEARCH_TAB = "DYNA_SEARCH_NEXT_MAIN";
	/** 动态表单主=子表ID前缀 */
	public static final String DYNC_PREFIX_SUB_SEARCH_TAB = "DYNA_SEARCH_SUB";
	/** 文件分隔符 */
	public final String FILE_LINE_SEPARATOR = System.getProperty("line.separator");

	/**
	 * 生成验证规则
	 * 
	 * @param tdFormField
	 * @return
	 */
	private String genVerifyTextVal(TdFormField tdFormField) {
		if (null != tdFormField) {
			String fdDbtype = tdFormField.getFdDbtype();
			DynaFieldType findEnum = (DynaFieldType) EnumUtils.findEnum(DynaFieldType.class, fdDbtype);
			StringBuilder str = new StringBuilder();
			switch (findEnum) {
			case INTEGER:
				str.append(" maxlength=\"").append(tdFormField.getLenInt()).append("\" ");
				str.append(" onkeyup=\"SYSTEM.clearNoNum(this);\" onblur=\"SYSTEM.clearNoNum(this);\" ");
				break;
			case NUMBER:
				// 整数长度
				Integer lenInt = tdFormField.getLenInt();
				// 小数长度
				Integer lenDemi = tdFormField.getLenDemi();
				Integer totalLen = lenInt + lenDemi + 2;
				str.append(" maxlength=\"").append(totalLen).append("\" ");
				str.append(" onkeyup=\"SYSTEM.verifyNum(this,").append(lenInt).append(",").append(lenDemi)
						.append(");\" ");
				str.append(" onblur=\"SYSTEM.verifyNum(this,").append(lenInt).append(",").append(lenDemi)
						.append(");\" ");
				break;
			case NVARCHAR2:
				str.append(" maxlength=\"").append(tdFormField.getLenChar()).append("\" ");
			default:
				break;
			}
			return str.toString();
		}
		return "";
	}

	/**
	 * 生成主子表js
	 * 
	 * @param tdFormField
	 * @return
	 */
	public String genHtmlScripts(List<TdFormField> mTdFormFieldList, List<TdFormField> sTdFormFieldList, CommServiceImpl commService) {
		StringBuilder jsStr = new StringBuilder();
		jsStr.append("<script type=\"text/javascript\">").append(FILE_LINE_SEPARATOR);
		jsStr.append("//<![CDATA[ ").append(FILE_LINE_SEPARATOR);
		jsStr.append("  function dyna_dom_style_init() {").append(FILE_LINE_SEPARATOR);
		jsStr.append("    jQuery(\"#defaultMsg\").puigrowl();").append(FILE_LINE_SEPARATOR);
		if (null != mTdFormFieldList && mTdFormFieldList.size() > 0) {
			for (TdFormField tdFormField : mTdFormFieldList) {
				if (tdFormField.getIsShow() != null
						&& (tdFormField.getIsShow().intValue() == 1 || tdFormField.getIsShow().intValue() == 2)) {
					jsStr.append(this.genHtmlScript(tdFormField, true,commService));
				}
			}
		}
		if (null != sTdFormFieldList && sTdFormFieldList.size() > 0) {
			jsStr.append("    jQuery('#DYNA_BTN_TH').puibutton();").append(FILE_LINE_SEPARATOR);
			for (TdFormField tdFormField : sTdFormFieldList) {
				if (tdFormField.getIsShow() != null
						&& (tdFormField.getIsShow().intValue() == 1 || tdFormField.getIsShow().intValue() == 2)) {
					jsStr.append(this.genHtmlScript(tdFormField, false,commService));
				}
			}
		}
		jsStr.append("  }").append(FILE_LINE_SEPARATOR);
		jsStr.append("//]]>").append(FILE_LINE_SEPARATOR);
		jsStr.append("</script>").append(FILE_LINE_SEPARATOR);
		return jsStr.toString();
	}

	/**
	 * 生成主子表js
	 * 
	 * @param tdFormField
	 * @return
	 */
	public String genHtmlScript(TdFormField tdFormField, boolean ifMain, CommServiceImpl commService) {
		if (null != tdFormField && tdFormField.getIsShow() != null
				&& (tdFormField.getIsShow().intValue() == 1 || tdFormField.getIsShow().intValue() == 2)) {
			String dataSrc = tdFormField.getDataSrc();
			DynaFieldShowType findEnum = (DynaFieldShowType) EnumUtils.findEnum(DynaFieldShowType.class, dataSrc);
			//复选框需要特殊处理
			StringBuilder str = new StringBuilder();
			if( DynaFieldShowType.DICT_SELECT_MANY.getTypeNo().equals(dataSrc) )	{
				// 对应码表类型
				String codeTypeNo = tdFormField.getCodeTypeNo();
				if (StringUtils.isNotBlank(codeTypeNo)) {
					List<TsSimpleCode> sList = commService.findSimpleCodeListByTypeNo(codeTypeNo);
					if (null != sList) {
						for ( int i = 0 ; i < sList.size() ; i++) {
							String checkboxId = this.genCheckboxId(ifMain, tdFormField, i);
							str.append(this.genCheckboxJs(ifMain, checkboxId, i));
						}
					}
				} else if (StringUtils.isNotBlank(tdFormField.getQuerySql())) {
					List<Object[]> findDataBySqlNoPage = commService.findDataBySqlNoPage(tdFormField.getQuerySql(),
							null);
					if (null != findDataBySqlNoPage) {
						for (int i = 0; i < findDataBySqlNoPage.size(); i++) {
							String checkboxId = this.genCheckboxId(ifMain, tdFormField, i);
							str.append(this.genCheckboxJs(ifMain, checkboxId, i));
						}
					}
				}
			}else{
				StringBuilder fieldId = new StringBuilder();
				if (ifMain) {
					fieldId.append("#").append(DYNC_PREFIX_MAIN_TAB).append("_").append(tdFormField.getFdEnname());
				} else {
					fieldId.append("[id*=").append(DYNC_PREFIX_SUB_TAB).append("_").append(tdFormField.getFdEnname())
							.append("]");
				}

				// JS初始化列字符串
				StringBuilder initStr = new StringBuilder();
				switch (findEnum) {
					case INPUT:
						initStr.append(".puiinputtext();");
						break;
					case TEXTAREA:
						initStr.append(".puiinputtextarea({autoResize: false ");
						if (null != tdFormField.getLenChar() && tdFormField.getLenChar() > 0) {
							initStr.append(", counter: jQuery('").append(fieldId).append("_COUNT'), ");
							initStr.append("counterTemplate: '还可以输入{0}个字。' , maxlength: ").append(tdFormField.getLenChar());
						}
						initStr.append(" });");
						break;
					case DATE:
						initStr.append(".puiinputtext();");
						break;
					case DICT_SELECT_ONE:
						initStr.append(".puidropdown();");
						break;
					// case LABEL:
					// return "";
					default:
						return "";
				}
				if (ifMain) {
					str.append("    jQuery('").append(fieldId).append("')").append(initStr).append(FILE_LINE_SEPARATOR);
				} else {
					str.append("    jQuery(\"").append(fieldId).append("\").each(function() {").append(FILE_LINE_SEPARATOR);
					str.append("    jQuery(this)").append(initStr).append(FILE_LINE_SEPARATOR);
					str.append("    });").append(FILE_LINE_SEPARATOR);
				}
			}
			return str.toString();
		}
		return "";
	}
	
	private String genCheckboxId(boolean ifMain,TdFormField tdFormField,int i){
		StringBuilder fieldId = new StringBuilder();
		if (ifMain) {
			fieldId.append("#").append(DYNC_PREFIX_MAIN_TAB).append("_").append(tdFormField.getFdEnname());
			fieldId.append("_").append(i);
		} else {
			fieldId.append("[id*=").append(DYNC_PREFIX_SUB_TAB).append("_").append(tdFormField.getFdEnname());
			fieldId.append("_").append(i).append("]");
		}
		return fieldId.toString();
	}
	
	private String genCheckboxJs(boolean ifMain,String fieldId,int index)	{
		StringBuilder str = new StringBuilder();
		if (ifMain) {
			str.append("    jQuery('").append(fieldId).append("').puicheckbox();").append(FILE_LINE_SEPARATOR);
		} else {
			str.append("    jQuery(\"").append(fieldId).append("\").each(function() {").append(FILE_LINE_SEPARATOR);
			str.append("      if(jQuery(this).attr(\"type\") == 'checkbox')	{").append(FILE_LINE_SEPARATOR);
			str.append("    	jQuery(this).puicheckbox();").append(FILE_LINE_SEPARATOR);
			str.append("      }").append(FILE_LINE_SEPARATOR);
			str.append("    });").append(FILE_LINE_SEPARATOR);
		}
		return str.toString();
	}

	/**
	 * 生成页面元素
	 * 
	 * @return
	 */
	public String genHtmlElement(TdFormField tdFormField, boolean ifMain, Boolean ifModel, CommServiceImpl commService) {
		if (null != tdFormField) {
			String fdDbtype = tdFormField.getFdDbtype();
			DynaFieldType dynaFieldType = (DynaFieldType) EnumUtils.findEnum(DynaFieldType.class, fdDbtype);

			String dataSrc = tdFormField.getDataSrc();
			StringBuilder str = new StringBuilder();
			DynaFieldShowType findEnum = (DynaFieldShowType) EnumUtils.findEnum(DynaFieldShowType.class, dataSrc);
			// 字段ID
			StringBuilder elementId = new StringBuilder();
			if (ifMain) {
				elementId.append(DYNC_PREFIX_MAIN_TAB).append("_").append(tdFormField.getFdEnname());
			} else {
				elementId.append(DYNC_PREFIX_SUB_TAB).append("_").append(tdFormField.getFdEnname());
				elementId.append("_").append(ifModel ? "{{row}}" : "${t-1}");
			}
			switch (findEnum) {
			case INPUT:
				str.append("<input id=\"").append(elementId).append("\" type=\"text\"  size=\"")
						.append(ifMain ? "30" : "10").append("\" ");
				if ( null != tdFormField.getIsReq() &&  tdFormField.getIsReq() == 1 ) {
					str.append(" required=\"required\" requiredMessage=\"").append(tdFormField.getFdCnname())
							.append("不允许为空！\" ");
				}
				if (tdFormField.getIsShow().intValue() == 2) {
					str.append(" readonly=\"readonly\"  ");
				}
				String genVerifyTextVal = this.genVerifyTextVal(tdFormField);
				if (StringUtils.isNotBlank(genVerifyTextVal)) {
					str.append(genVerifyTextVal);
				}
				str.append(" ></input>");
				break;
			case TEXTAREA:
				str.append("<textarea id=\"").append(elementId).append("\" rows=\"5\" cols=\"60\" ");
				if ( null != tdFormField.getIsReq() &&  tdFormField.getIsReq() == 1 ) {
					str.append(" required=\"required\" ");
					str.append(" requiredMessage=\"").append(tdFormField.getFdCnname()).append("不允许为空！\" ");
				}
				if (tdFormField.getIsShow().intValue() == 2) {
					str.append(" readonly=\"readonly\"  ");
				}
				genVerifyTextVal = this.genVerifyTextVal(tdFormField);
				if (StringUtils.isNotBlank(genVerifyTextVal)) {
					str.append(genVerifyTextVal);
				}
				str.append(" ></textarea>");
				str.append("<BR>");
				str.append("<span id=\"").append(elementId).append("_COUNT\"></span>");
				break;
			case DATE:
				String format = "yyyy-MM-dd";
				if (dynaFieldType.getTypeNo().equals(DynaFieldType.TIMESTAMP.getTypeNo())) {
					format = "yyyy-MM-dd HH:mm:ss";
				}

				str.append("<input id= \"").append(elementId)
						.append("\" class=\"pui-inputtext ui-widget ui-state-default ui-corner-all\" ");
				str.append(" type=\"text\"");
				if (tdFormField.getIsShow().intValue() != 2) {
					str.append(" onFocus=\"WdatePicker({dateFmt:'").append(format).append("'})\" ");
				}
				if ( null != tdFormField.getIsReq() &&  tdFormField.getIsReq() == 1 ) {
					str.append(" required=\"required\" ");
					str.append(" requiredMessage=\"").append(tdFormField.getFdCnname()).append("不允许为空！\" ");
				}
				if (tdFormField.getIsShow().intValue() == 2) {
					str.append(" readonly=\"readonly\"  ");
				}
				str.append(" ></input>");
				break;
			case DICT_SELECT_ONE:
				str.append("<select id=\"").append(elementId).append("\"  ");
				if ( null != tdFormField.getIsReq() &&  tdFormField.getIsReq() == 1 ) {
					str.append(" required=\"required\" ");
					str.append(" requiredMessage=\"").append(tdFormField.getFdCnname()).append("不允许为空！\" ");
				}
				if (tdFormField.getIsShow().intValue() == 2) {
					str.append(" disabled=\"disabled\"  ");
				}
				str.append(" >");
				str.append("<option value=\"\">--请选择--</option>");
				// 对应码表类型
				String codeTypeNo = tdFormField.getCodeTypeNo();
				if (StringUtils.isNotBlank(codeTypeNo)) {
					List<TsSimpleCode> sList = commService.findSimpleCodeListByTypeNo(codeTypeNo);
					if (null != sList) {
						for (TsSimpleCode ts : sList) {
							str.append("<option value=\"").append(ts.getCodeNo()).append("\">")
									.append(ts.getCodeName()).append("</option>");
						}
					}
				} else if (StringUtils.isNotBlank(tdFormField.getQuerySql())) {
					List<Object[]> findDataBySqlNoPage = commService.findDataBySqlNoPage(tdFormField.getQuerySql(),
							null);
					if (null != findDataBySqlNoPage) {
						for (Object[] objArr : findDataBySqlNoPage) {
							str.append("<option value=\"").append(objArr[0]).append("\">").append(objArr[1])
									.append("</option>");
						}
					}
				}
				str.append("</select>");
				break;
			case LABEL:
				str.append("<span id=\"").append(elementId).append("\"></span>");
				break;
			case FJ:
				str.append("<a id=\"").append(elementId).append("_UP\"  style=\" color: #25AAE1;\"  href=\"javascript:dynaUploadFile('").append(elementId).append("')\" >上传</a>").append(FILE_LINE_SEPARATOR);
				str.append("<a id=\"").append(elementId).append("_DOWN\" style=\"display:none; color: #25AAE1;\" href=\"javascript:dynaDownLoadFile('").append(elementId).append("')\" >&nbsp;&nbsp;下载</a>").append(FILE_LINE_SEPARATOR);
				str.append("<a id=\"").append(elementId).append("_DEL\"  style=\"display:none;color: #25AAE1;\" href=\"javascript:dynaDelFile('").append(elementId).append("')\" >&nbsp;&nbsp;删除</a>").append(FILE_LINE_SEPARATOR);
				
				str.append("<span id=\"").append(elementId).append("_SPAN\"></span>").append(FILE_LINE_SEPARATOR);
				str.append("<input id=\"").append(elementId).append("PATH\" type=\"text\"  style=\"display:none\"  value=\"\"  ></input>").append(FILE_LINE_SEPARATOR);
				str.append("<input id=\"").append(elementId).append("NAME\" fieldTyp=\"fj\" type=\"text\" style=\"height:0px;margin:0;padding:0;border:none;width:1px\"  value=\"\" ");
				if ( null != tdFormField.getIsReq() &&  tdFormField.getIsReq() == 1 ) {
					str.append(" required=\"required\" requiredMessage=\"").append(tdFormField.getFdCnname())
					.append("必须上传！\" ");
				}
				str.append(" ></input>").append(FILE_LINE_SEPARATOR);
				break;
			case DICT_SELECT_MANY:
				// 对应码表类型
				String codeTypeNo2 = tdFormField.getCodeTypeNo();
				if (StringUtils.isNotBlank(codeTypeNo2)) {
					List<TsSimpleCode> sList = commService.findSimpleCodeListByTypeNo(codeTypeNo2);
					if (null != sList) {
						StringBuilder tempStr = new StringBuilder();
						for ( int i = 0 ; i < sList.size() ; i++) {
							TsSimpleCode ts = sList.get(i);
							
							tempStr.append("   <input type=\"checkbox\" name=\"").append(elementId).append("\" ");
							tempStr.append(" id=\"").append(elementId).append("_").append(i).append("\" value=\"").append(ts.getCodeNo()).append("\" ");
							if (tdFormField.getIsShow().intValue() == 2) {
								tempStr.append(" disabled=\"disabled\"  ");
							}
							tempStr.append(" />");
							
							tempStr.append("  <label for=\"").append(elementId).append("_").append(i).append("\" class=\"ui-widget\">").append(ts.getCodeName()).append("</label>");
							
							if( i%5 == 4 || i == sList.size()-1 ){
								str.append("<div class=\"ui-grid-row\">");
								str.append(" <div class=\"ui-grid-col-1\">");
								str.append(tempStr);
								if(i == sList.size()-1)	{
									//必填字段
									str.append("<input id=\"").append(elementId).append("\" type=\"text\" style=\"height:0px;margin:0;padding:0;border:none;width:1px\"  value=\"\" ");
									if ( null != tdFormField.getIsReq() &&  tdFormField.getIsReq() == 1 ) {
										str.append(" required=\"required\" requiredMessage=\"").append(tdFormField.getFdCnname())
										.append("不允许为空！\" cbHide= '1'");
									}
									str.append(" ></input>").append(FILE_LINE_SEPARATOR);
								}
								str.append(" </div></div>");
								tempStr = new StringBuilder();
							}
						
						}
					}
				} else if (StringUtils.isNotBlank(tdFormField.getQuerySql())) {
					List<Object[]> findDataBySqlNoPage = commService.findDataBySqlNoPage(tdFormField.getQuerySql(),
							null);
					if (null != findDataBySqlNoPage) {
						StringBuilder tempStr = new StringBuilder();
						for (int i = 0; i < findDataBySqlNoPage.size(); i++) {
							Object[] objArr = findDataBySqlNoPage.get(i);
							
							
							tempStr.append("<input type=\"checkbox\" name=\"").append(elementId).append("\" ");
							tempStr.append(" id=\"").append(elementId).append("_").append(i).append("\" value=\"").append(objArr[0]).append("\" ");
							if (tdFormField.getIsShow().intValue() == 2) {
								tempStr.append(" disabled=\"disabled\"  ");
							}
							tempStr.append(" /></div>");

							tempStr.append(" <div class=\"ui-grid-col-11\"><label for=\"").append(elementId).append("_").append(i).append("\" class=\"ui-widget\">").append(objArr[1])
									.append("</label>");
							
							if( i%5 == 4 || i == findDataBySqlNoPage.size()-1 ){
								str.append("<div class=\"ui-grid-row\">");
								str.append(" <div class=\"ui-grid-col-1\">");
								str.append(tempStr);
								//加在最后一个复选框上
								if(i == findDataBySqlNoPage.size()-1 ){
									str.append("<input id=\"").append(elementId).append("\" type=\"text\" style=\"height:0px;margin:0;padding:0;border:none;width:1px\"  value=\"\" ");
									if ( null != tdFormField.getIsReq() &&  tdFormField.getIsReq() == 1 ) {
										str.append(" required=\"required\" requiredMessage=\"").append(tdFormField.getFdCnname())
										.append("不允许为空！\" cbHide=\"1\" ");
									}
									str.append(" ></input>").append(FILE_LINE_SEPARATOR);
								}
								
								str.append(" </div></div>");
								tempStr = new StringBuilder();
							}
						}
					}
				}
				break;
			default:
				break;
			}
			return str.toString();
		}
		return "";
	}
	
	/**
	 * 生成页面元素
	 * @param betweenFlag 
	 * 
	 * @return
	 */
	public String genSearchHtmlElement(TdFormField tdFormField, boolean ifMain, Boolean ifModel, CommServiceImpl commService, boolean betweenFlag) {
		if (null != tdFormField) {
			String fdDbtype = tdFormField.getFdDbtype();
			DynaFieldType dynaFieldType = (DynaFieldType) EnumUtils.findEnum(DynaFieldType.class, fdDbtype);

			String dataSrc = tdFormField.getDataSrc();
			StringBuilder str = new StringBuilder();
			DynaFieldShowType findEnum = (DynaFieldShowType) EnumUtils.findEnum(DynaFieldShowType.class, dataSrc);
			// 字段ID
			StringBuilder elementId = new StringBuilder();
			if (ifMain) {
				if(betweenFlag)
					elementId.append(DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(tdFormField.getFdEnname());
				else
					elementId.append(DYNC_PREFIX_NEXT_MAIN_SEARCH_TAB).append("_").append(tdFormField.getFdEnname());
			} else {
				elementId.append(DYNC_PREFIX_SUB_SEARCH_TAB).append("_").append(tdFormField.getFdEnname());
				elementId.append("_").append(ifModel ? "{{row}}" : "${t-1}");
			}
			switch (findEnum) {
			case TEXTAREA:
			case INPUT:
//				str.append("<input id=\"").append(elementId).append("\" type=\"text\"  size=\"")
//						.append(ifMain ? "30" : "10").append("\" ");
				str.append("<input id=\"").append(elementId).append("\" type=\"text\" ");
				String genVerifyTextVal = this.genVerifyTextVal(tdFormField);
				if (StringUtils.isNotBlank(genVerifyTextVal)) {
					str.append(genVerifyTextVal);
				}
				str.append(" ></input>");
				break;
			case DATE:
				String format = "yyyy-MM-dd";
				if (dynaFieldType.getTypeNo().equals(DynaFieldType.TIMESTAMP.getTypeNo())) {
					format = "yyyy-MM-dd HH:mm:ss";
				}

				str.append("<input id= \"").append(elementId)
						.append("\" class=\"pui-inputtext ui-widget ui-state-default ui-corner-all\" ");
				str.append(" type=\"text\"");
				if (tdFormField.getIsShow().intValue() != 2) {
					str.append(" onFocus=\"WdatePicker({dateFmt:'").append(format).append("'})\" ");
				}
				str.append(" ></input>");
				break;
			case DICT_SELECT_ONE:
				str.append("<select id=\"").append(elementId).append("\"  ");
				str.append(" >");
				str.append("<option value=\"\">--请选择--</option>");
				// 对应码表类型
				String codeTypeNo = tdFormField.getCodeTypeNo();
				if (StringUtils.isNotBlank(codeTypeNo)) {
					List<TsSimpleCode> sList = commService.findSimpleCodeListByTypeNo(codeTypeNo);
					if (null != sList) {
						for (TsSimpleCode ts : sList) {
							str.append("<option value=\"").append(ts.getCodeNo()).append("\">")
									.append(ts.getCodeName()).append("</option>");
						}
					}
				} else if (StringUtils.isNotBlank(tdFormField.getQuerySql())) {
					List<Object[]> findDataBySqlNoPage = commService.findDataBySqlNoPage(tdFormField.getQuerySql(),
							null);
					if (null != findDataBySqlNoPage) {
						for (Object[] objArr : findDataBySqlNoPage) {
							str.append("<option value=\"").append(objArr[0]).append("\">").append(objArr[1])
									.append("</option>");
						}
					}
				}
				str.append("</select>");
				break;
			case LABEL:
				str.append("<span id=\"").append(elementId).append("\"></span>");
				break;
			default:
				break;
			}
			if(SearchFlag.BETWEEN.getFlag().equals(tdFormField.getSearchType()) && betweenFlag)
				str.append("~").append(genSearchHtmlElement(tdFormField, ifMain, ifModel, commService,false));
			return str.toString();
		}
		return "";
	}

}
