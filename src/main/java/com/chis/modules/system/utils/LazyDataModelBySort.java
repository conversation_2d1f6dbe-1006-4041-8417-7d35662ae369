package com.chis.modules.system.utils;

import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

import java.util.*;

/**
 * <AUTHOR>
 * @description:  针对页面列排序的功能
 */
public class LazyDataModelBySort<T> extends LazyDataModel<T> {

    private static final long serialVersionUID = -2705120688106338304L;
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    private List<T> data;
    private String hql;
    private String countHql;

    /**查询条件*/
    private Map<String, Object> paramMap;
    /**数据处理接口*/
    private IProcessData processData;
    private boolean ifSQL = Boolean.TRUE;
    /**查询条件map用于*/
    protected Map<String, String> columnMap;
    /**排序*/
    protected Map<String, String> sortMap = new HashMap<>();

    public LazyDataModelBySort() {

    }

    public LazyDataModelBySort(String hql, String countHql, Map<String, Object> paramMap, IProcessData processData,Map<String, String> columnMap) {
        this.hql = hql;
        this.countHql = countHql;
        this.paramMap = paramMap;
        this.processData = processData;
        this.columnMap = columnMap;
        this.data = new ArrayList<>();
        this.init();
    }




    private void init() {
        if(this.ifSQL) {
            this.setRowCount(this.commService.findTotalNumBySQL(this.countHql, this.paramMap));
        }else {
            this.setRowCount(this.commService.findTotalNum(this.countHql, this.paramMap));
        }
    }

    @Override
    public List<T> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map<String, String> filters) {
        int rowCount = this.getRowCount();
        if (rowCount == 0) {
            return Collections.emptyList();
        }
        if (first >= rowCount) {
            // 当查询使用的首记录超出记录总数时，调整首记录
            if (rowCount % pageSize == 0) {
                first = ((rowCount / pageSize) - 1) * pageSize;
            } else {
                first = (rowCount / pageSize) * pageSize;
            }
        }
        if (this.ifSQL) {
            StringBuilder sb = new StringBuilder();
            if(ObjectUtil.isNotEmpty(sortField) && this.columnMap.containsKey(sortField)){
                String field = this.columnMap.get(sortField);
                String[] str = this.hql.split("ORDER BY");
                String sortBy ="ASC";
                if("ASCENDING".equals(sortOrder.name())){
                    sortBy ="ASC";
                }else  if("DESCENDING".equals(sortOrder.name())){
                    sortBy ="DESC";
                }
                this.sortMap.put("sortField",sortField);
                this.sortMap.put("sortBy",sortBy);
                if(str.length > 1){
                    sb.append(str[0]).append(" ORDER BY "+field+" "+sortBy+",").append(str[1]);
                }else {
                    sb.append(str[0]).append(" ORDER BY "+field+" "+sortBy);
                }
            }else {
                sb.append(this.hql);
            }
            this.data = this.commService.findDataBySQL(sb.toString(), this.paramMap, first, pageSize);
        } else {
            this.data = this.commService.findData(this.hql, this.paramMap, first, pageSize);
        }
        if(null != this.processData) {
            this.processData.processData(this.data);
        }
        return this.data;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public Map<String, String> getSortMap() {
        return sortMap;
    }

    public void setSortMap(Map<String, String> sortMap) {
        this.sortMap = sortMap;
    }

}
