package com.chis.modules.system.utils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.primefaces.model.DefaultScheduleEvent;
import org.primefaces.model.LazyScheduleModel;
import org.primefaces.model.ScheduleEvent;
import org.quartz.CronExpression;

import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TdOaSchedule;
import com.chis.modules.system.service.CommServiceImpl;

public class DefaultScheduleModel extends LazyScheduleModel{
	
	private static final long serialVersionUID = -1447160842334065265L;
	private String userId;
	private Map<String, Object> paramMap;
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	
	public DefaultScheduleModel(String userId, Map<String, Object> paramMap) {
		this.userId = userId;
		this.paramMap = paramMap;
	}

	@Override
	public void loadEvents(Date start, Date end) {
		if(StringUtils.isNotBlank(this.userId)) {
			if(null == this.paramMap) {
				this.paramMap = new HashMap<String, Object>();
			}
			this.paramMap.put("start", start);
			this.paramMap.put("end", end);
			
			/**
			 * 非周期性的
			 */
			StringBuilder sb = new StringBuilder();
			sb.append(" select new TdOaSchedule(t.rid, t.scheduleTitle, t.beginTime, t.endTime) from TdOaSchedule t  where ");
			sb.append(" t.executeMan.rid='").append(userId);
			sb.append("' and t.seasonal='0' ");
			sb.append(" and (t.beginTime<=:end and t.endTime>=:start) ");
			
			List<TdOaSchedule> list = this.commService.findData(sb.toString(), this.paramMap);
			if(null != list && list.size() > 0) {
				for(TdOaSchedule entity:list) {
					ScheduleEvent event = new DefaultScheduleEvent(entity.getScheduleTitle(), entity.getBeginTime(), entity.getEndTime(),entity.getRid());
					this.addEvent(event);
				}
			}
			
			/**
			 * 周期性的
			 */
			sb = new StringBuilder();
			sb.append(" select new TdOaSchedule(t.rid, t.scheduleTitle, t.beginTime, t.endTime, t.seasonal, t.expressions, t.periodType) from TdOaSchedule t  where ");
			sb.append(" t.executeMan.rid='").append(userId);
			sb.append("' and t.seasonal='1' and t.expressions is not null ");
			List<TdOaSchedule> list2 = this.commService.findData(sb.toString(), null);
			
			if(null != list2 && list2.size() > 0) {
				CronExpression exp = null;
				for(TdOaSchedule entity:list2) {
					//周期
					try {
						Date begin = (Date) start.clone();
						exp = new CronExpression(entity.getExpressions());
						
						begin = exp.getNextValidTimeAfter(begin);
						while(null != begin && begin.before(end)) {
							ScheduleEvent event = new DefaultScheduleEvent(entity.getScheduleTitle(), begin, begin,entity.getRid());
							this.addEvent(event);
							begin = exp.getNextValidTimeAfter(begin);
						}
						
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}
	}
	
	
	public static String getWeekOfInt(int i) {
		String week = null;
		switch (i) {
		case 1:
			week = "MON";
			break;
		case 2:
			week = "TUE";
			break;
		case 3:
			week = "WED";
			break;
		case 4:
			week = "THU";
			break;
		case 5:
			week = "FRI";
			break;
		case 6:
			week = "SAT";
			break;
		case 7:
			week = "SUN";
			break;
		default:
			week = "MON";
		}
		return week;
	}
	
}
