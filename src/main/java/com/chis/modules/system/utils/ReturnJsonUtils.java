package com.chis.modules.system.utils;

import com.alibaba.fastjson.JSON;
import org.apache.ibatis.type.Alias;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
/**
 * <p>类描述：签章接口返回实体</p>
 * @ClassAuthor qrr,2018年4月21日,ReturnJsonUtils
 * */
@Alias("ReturnJsonUtil")
public class ReturnJsonUtils {

	/**
	 * 生成回复经过base64编码Json字符串
	 * 
	 * @return
	 */
	public static String genJsonBase64(Object obj) {
		if (null != obj) {
			String rtJson = JSON.toJSONString(obj);
//			String encode = StringUtils.encode(rtJson);
			return rtJson;
		}
		return null;
	}
	
	/**
	 * 封装请求和响应信息
	 * @param req
	 * @param resp
	 * @param pojo
	 * @throws IOException
	 */
	public static void packResponseInfo(HttpServletRequest req,HttpServletResponse resp,Object pojo) throws IOException{
		String respString = ReturnJsonUtils.genJsonBase64(pojo);
		resp.getWriter().print(respString);
		req.setAttribute("responseInfo", respString);
	}
}
