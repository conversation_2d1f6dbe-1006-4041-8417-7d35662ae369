package com.chis.modules.system.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.chis.common.utils.SimplePropertyFilter;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.annotation.ZwxLog;
import com.chis.modules.system.entity.TsRole;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.LogNoEnum;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * @Description : 切面类
 * @ClassAuthor : anjing
 * @Date : 2020/1/10 13:57
 **/
@Aspect
@Component
public class LogInterceptor {

    @Pointcut("@annotation(com.chis.modules.system.annotation.ZwxLog)")
    public void aspectZwxLog() {
    }

    @Before("aspectZwxLog()")
    public void doBefore(JoinPoint joinPoint) {
        try {
            Object[] obj = getDescOfZwxLog(joinPoint);
            String hintInfo = (String) obj[0];
            LogNoEnum type = (LogNoEnum)obj[1];
            String detailInfo = (String) obj[2];
            if(StringUtils.isBlank(hintInfo)) {
                hintInfo = type.getTypeCN();
            }
            if(StringUtils.isBlank(detailInfo)) {
                detailInfo = hintInfo;
            }
            LogUtil.logIntoDB(type, hintInfo, detailInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取注解中对方法的描述信息
     * @param joinPoint 切点
     * @return 方法描述
     * @throws Exception
     */
    public static Object[] getDescOfZwxLog(JoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method mtd = methodSignature.getMethod();
        ZwxLog zwxLog = mtd.getAnnotation(ZwxLog.class);

        Object[] obj = new Object[3];
        obj[0] = zwxLog.value();
        obj[1] = zwxLog.type();

        Object[] args = joinPoint.getArgs();
        String rst = "";
        if(null != args && args.length > 0) {
            if(args[0] instanceof  Integer) {
                obj[2] = zwxLog.value() + "（主键Rid：" + args[0] + "）";
            } else if(args[0] instanceof TsUserInfo) {
                TsUserInfo tsUserInfo = (TsUserInfo) args[0];
                rst = JSON.toJSONString(tsUserInfo, SimplePropertyFilter.getInstance(),  SerializerFeature.PrettyFormat);
                obj[2] = zwxLog.value() + "（" + rst + "）";
            } else if(args[0] instanceof TsRole) {
                TsRole tsRole = (TsRole) args[0];
                rst = JSON.toJSONString(tsRole, SimplePropertyFilter.getInstance(),  SerializerFeature.PrettyFormat);
                obj[2] = zwxLog.value() + "（" + rst + "）";
            } else if(args[0] instanceof TsUnit) {
                TsUnit tsUnit = (TsUnit) args[0];
                rst = JSON.toJSONString(tsUnit, SimplePropertyFilter.getInstance(),  SerializerFeature.PrettyFormat);
                obj[2] = zwxLog.value() + "（" + rst + "）";
            } else {
                obj[2] = zwxLog.value();
            }
        }
        return obj;
    }
}
