package com.chis.modules.system.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;

import com.chis.common.utils.JsfUtil;
import com.chis.modules.system.entity.TbSysEmp;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.logic.SessionData;

public class UsersUtil {

	/**
	 * 生成常用的当前登录人的相关信息
	 * 
	 * @return
	 */
	public static Map<String, Object> generateCommonUtilMap() {
		Map<String, Object> map = new HashMap<String, Object>();
		HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext()
				.getRequest();
		SessionData sessionData = (SessionData) request.getSession().getAttribute(SessionData.SESSION_DATA);
		map.put("user", sessionData.getUser());
		map.put("unit", sessionData.getUser().getTsUnit());
		map.put("ip", JsfUtil.getIpAddr(request));

		TbSysEmp emp = sessionData.getUser().getTbSysEmp();
		TsOffice office = null;
		if (null != emp) {
			office = emp.getTsOffice();
		}
		if (null == office) {
			office = new TsOffice();
		}
		map.put("office", office);

		return map;
	}
}
