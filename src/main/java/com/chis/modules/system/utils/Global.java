package com.chis.modules.system.utils;

import java.util.Set;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.JsfUtil;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;

/**
 * 全局通用方法
 * 
 * <AUTHOR>
 * @createTime 2016年1月12日
 */
public class Global {

	/**
	 * 获取sessionData对象，目前存在session中，以后可能会通过jedis存内存中<br/>
	 * 
	 * @return
	 */
	public static SessionData getSessionData() {
		SessionData data = (SessionData) JsfUtil.getSession().getAttribute(
				SessionData.SESSION_DATA);
		return data;
	}

	/**
	 * 获取当前登录人信息
	 * 
	 * @return
	 */
	public static TsUserInfo getUser() {
		return getSessionData().getUser();
	}

	/**
	 * 获取当前登录人所在科室
	 * 
	 * @return
	 */
	public static TsOffice getOffice() {
		return getSessionData().getUser().getTsOffice();
	}

	/**
	 * 当前登录人是否是超管
	 * 
	 * @return
	 */
	public static boolean admin() {
		return Constants.ADMIN.equals(getUser().getUserNo());
	}


	/** 获取菜单按钮 */
	public static Set<String> getBtnSet() {
		return getSessionData().getBtnSet();
	}
	/**
 	 * <p>方法描述：错误信息提示</p>
 	 * @MethodAuthor qrr,2020年4月26日,markErrorInfo
     * */
	public static void markErrorInfo(boolean ifTip,String id,String errorMsg) {
    	if (ifTip) {
        	JsfUtil.addErrorMessage(errorMsg);
		}
        RequestContext.getCurrentInstance().execute("SYSTEM.markErrorInfo('"+id+"')");
	}
}
