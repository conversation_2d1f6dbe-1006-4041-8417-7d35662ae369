package com.chis.modules.system.utils;

import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.primefaces.model.UploadedFile;

import java.io.IOException;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * @Description : 解析EXCEL文件
 * @ClassAuthor : anjing
 * @Date : 2019/8/13 9:48
 **/
public class ImportExcelUtil {

    /**
    * @Description : 读取excel方法
    * @MethodAuthor: anjing
    * @Date : 2019/8/13 9:49
    **/
    public static List<List<Object>> readExcel(UploadedFile file) throws IOException, InvalidFormatException {
        List<List<Object>> list = new ArrayList<>();
        if (null != file) {
            Workbook wb = getWorkbook(file);
            CellStyle cellStyle = wb.createCellStyle();
            if (wb != null) {
                Sheet sheet = getSheet(wb, 0);
                list = getSheetData(wb, sheet, cellStyle);
            }
        }
        return list;
    }

    /**
     * <p>方法描述：通过Workbook读取对应sheet数据 过滤掉空行</p>
     * @MethodAuthor pw,2022年05月27日
     */
    public static List<List<Object>> readExcel(Workbook wb, int sheetIndex){
        List<List<Object>> list = new ArrayList<>();
        if (wb != null) {
            CellStyle cellStyle = wb.createCellStyle();
            Sheet sheet = getSheet(wb, sheetIndex);
            list = getSheetData(wb, sheet, cellStyle);
        }
        return list;
    }

    /**
     * <p>方法描述：通过Workbook读取对应sheet数据 包含空行</p>
     * @MethodAuthor pw,2022年05月30日
     */
    public static List<List<Object>> readExcelWithEmptyRow(Workbook wb, int sheetIndex){
        List<List<Object>> list = new ArrayList<>();
        if (wb != null) {
            CellStyle cellStyle = wb.createCellStyle();
            Sheet sheet = getSheet(wb, sheetIndex);
            list = getSheetDataWithEmptyRow(wb, sheet, cellStyle);
        }
        return list;
    }

    /**
     * <p>方法描述：通过Workbook读取对应sheet数据 包含空行</p>
     * @MethodAuthor pw,2022年05月30日
     */
    public static List<List<Object>> readNewExcelWithEmptyRow(Workbook wb, int sheetIndex){
        List<List<Object>> list = new ArrayList<>();
        if (wb != null) {
            Sheet sheet = getSheet(wb, sheetIndex);
            list = getNewSheetDataWithEmptyRow(sheet);
        }
        return list;
    }

    /**
     * <p>方法描述：通过Workbook读取对应sheet数据行数 </p>
     * @MethodAuthor pw,2022年05月27日
     */
    public static int countExcel(Workbook wb, int sheetIndex) throws IOException{
        return countSheet(wb, sheetIndex);
    }

    public static  int countExcel(Workbook wb) throws IOException {
        return countSheet(wb, 0);
    }

    public static int countSheet(Workbook wb, int sheetIndex) throws IOException  {
        int count = 0;
        if (wb != null) {
            Sheet sheet = getSheet(wb, sheetIndex);
            count = sheet.getLastRowNum();
        }
        return count;
    }

    /**
    * @Description : 据excel文件来获取workbook
    * @MethodAuthor: anjing
    * @Date : 2019/8/13 9:50
    **/
    public static Workbook getWorkbook(UploadedFile file) throws IOException, InvalidFormatException {
        Workbook wb = null;
        InputStream inp = file.getInputstream();
        wb = WorkbookFactory.create(inp);
        if(null != inp){
            inp.close();
        }
        return wb;
    }

    /**
    * @Description : 根据workbook和sheet的下标索引值来获取sheet
    * @MethodAuthor: anjing
    * @Date : 2019/8/13 9:50
    **/
    public static Sheet getSheet(Workbook wb, int sheetIndex) {
        return wb.getSheetAt(sheetIndex);
    }

    /**
    * @Description : 根据sheet获取该sheet的所有数据
    * @MethodAuthor: anjing
    * @Date : 2019/8/13 9:51
    **/
    public static List<List<Object>> getSheetData(Workbook wb, Sheet sheet, CellStyle cellStyle) {
        List<List<Object>> list = new ArrayList<List<Object>>();
        Iterator<Row> rowIterator = sheet.rowIterator();
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            boolean allRowIsBlank = isBlankRow(wb, row, cellStyle);
            // 整行都空，就跳过
            if (allRowIsBlank) {
                continue;
            }
            List<Object> rowData = getRowData(wb, row, cellStyle);
            list.add(rowData);
        }
        return list;
    }

    /**
     * <p>方法描述：根据sheet获取该sheet的所有数据 空行null</p>
     * @MethodAuthor pw,2022年05月30日
     */
    public static List<List<Object>> getSheetDataWithEmptyRow(Workbook wb, Sheet sheet, CellStyle cellStyle) {
        List<List<Object>> list = new ArrayList<List<Object>>();
        int lastRowNum = sheet.getLastRowNum();
        if(lastRowNum >= 0){
            //注意 row 包含lastRowNum
            for(int i=0;i<=lastRowNum;i++){
                Row row = sheet.getRow(i);
                if(null == row){
                    list.add(null);
                    continue;
                }
                boolean allRowIsBlank = isBlankRow(wb, row, cellStyle);
                // 整行都空，就跳过
                if (allRowIsBlank) {
                    list.add(null);
                    continue;
                }
                List<Object> rowData = getRowData(wb, row, cellStyle);
                list.add(rowData);
            }
        }
        return list;
    }

    /**
     * <p>方法描述：根据sheet获取该sheet的所有数据 空行null</p>
     * @MethodAuthor pw,2022年05月30日
     */
    public static List<List<Object>> getNewSheetDataWithEmptyRow(Sheet sheet) {
        List<List<Object>> list = new ArrayList<List<Object>>();
        int lastRowNum = sheet.getLastRowNum();
        if(lastRowNum >= 0){
            //注意 row 包含lastRowNum
            for(int i=0;i<=lastRowNum;i++){
                Row row = sheet.getRow(i);
                if(null == row){
                    list.add(null);
                    continue;
                }
                boolean allRowIsBlank = isNewBlankRow(row);
                // 整行都空，就跳过
                if (allRowIsBlank) {
                    list.add(null);
                    continue;
                }
                List<Object> rowData = getNewRowData(row);
                list.add(rowData);
            }
        }
        return list;
    }


    /**
    * @Description : 判断整行是不是都为空
    * @MethodAuthor: anjing
    * @Date : 2019/8/13 9:51
    **/
    public static boolean isBlankRow(Workbook wb, Row row, CellStyle cellStyle) {
        boolean allRowIsBlank = true;
        Iterator<Cell> cellIterator = row.cellIterator();
        while (cellIterator.hasNext()) {
            Object cellValue = getCellValue(wb, cellIterator.next(), cellStyle);
            if (cellValue != null && !"".equals(cellValue)) {
                allRowIsBlank = false;
                break;
            }
        }
        return allRowIsBlank;
    }

  /**
   * <p>方法描述：判断整行是不是都为空</p>
   * @MethodAuthor： yzz
   * @Date：2022-09-21
   **/
    public static boolean isNewBlankRow(Row row) {
        boolean allRowIsBlank = true;
        Iterator<Cell> cellIterator = row.cellIterator();
        while (cellIterator.hasNext()) {
            Object cellValue = getNewCellValue(cellIterator.next());
            if (cellValue != null && !"".equals(cellValue)) {
                allRowIsBlank = false;
                break;
            }
        }
        return allRowIsBlank;
    }


    /**
     * <p>方法描述：获取行的数据</p>
     * @MethodAuthor： yzz
     * @Date：2022-09-21
     **/
    public static List<Object> getNewRowData(Row row) {
        List<Object> rowData = new ArrayList<Object>();
        //注意 cell 不包含LastCellNum
        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            Object cellValue = getNewCellValue(cell);
            rowData.add(cellValue);
        }
        return rowData;
    }

    /**
    * @Description : 获取行的数据
    * @MethodAuthor: anjing
    * @Date : 2019/8/13 9:51
    **/
    public static List<Object> getRowData(Workbook wb, Row row, CellStyle cellStyle) {
        List<Object> rowData = new ArrayList<Object>();
        //注意 cell 不包含LastCellNum
        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            Object cellValue = getCellValue(wb, cell, cellStyle);
            rowData.add(cellValue);
        }
        return rowData;
    }

    /**
    * @Description : 获取单元格值
    * @MethodAuthor: anjing
    * @Date : 2019/8/13 9:52
    **/
    @Deprecated
    public static Object getCellValue(Workbook wb, Cell cell, CellStyle cellStyle) {
        if (cell == null || (cell.getCellType() == Cell.CELL_TYPE_STRING && StringUtils.isBlank(cell.getStringCellValue()))) {
            return null;
        }
        // 如果该单元格为数字， 则设置该单元格类型为文本格式
        DataFormat dataFormat =wb.createDataFormat();
        cellStyle.setDataFormat(dataFormat.getFormat("@"));
        cell.setCellStyle(cellStyle);
        cell.setCellType(Cell.CELL_TYPE_STRING);
        // 格式化 number String字符
        DecimalFormat df = new DecimalFormat("0");
        // 格式化数字
        DecimalFormat nf = new DecimalFormat("0");

        int cellType = cell.getCellType();
        switch (cellType) {
            case Cell.CELL_TYPE_BLANK:
                return null;
            case Cell.CELL_TYPE_BOOLEAN:
                return cell.getBooleanCellValue();
            case Cell.CELL_TYPE_ERROR:
                return cell.getErrorCellValue();
            case Cell.CELL_TYPE_FORMULA:
                return cell.getNumericCellValue();
            case Cell.CELL_TYPE_NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else if ("@".equals(cell.getCellStyle().getDataFormatString())) {
                    String value = df.format(cell.getNumericCellValue());
                    if (StringUtils.isBlank(value)) {
                        return null;
                    }
                    return value;
                } else if ("General".equals(cell.getCellStyle().getDataFormatString())) {
                    String value = nf.format(cell.getNumericCellValue());
                    if (StringUtils.isBlank(value)) {
                        return null;
                    }
                    return value;
                } else {
                    double doubleValue = cell.getNumericCellValue();
                    long longValue = (long) doubleValue;
                    if (doubleValue - longValue > 0) {
                        return String.valueOf(doubleValue);
                    }
                    else {
                        return longValue;
                    }
                }
            case Cell.CELL_TYPE_STRING:
                String value = cell.getStringCellValue();
                if (StringUtils.isBlank(value)) {
                    return null;
                } else {
                    return value;
                }
            default:
                return null;
        }
    }


  /**
   * <p>方法描述：获取单元格值</p>
   * @MethodAuthor： yzz
   * @Date：2022-09-21
   **/
    public static Object getNewCellValue(Cell cell) {
        if (cell == null || (cell.getCellType() == Cell.CELL_TYPE_STRING && StringUtils.isBlank(cell.getStringCellValue()))) {
            return null;
        }
        // 格式化 number String字符
        DecimalFormat df = new DecimalFormat("0");
        // 格式化数字
        DecimalFormat nf = new DecimalFormat("0");

        int cellType = cell.getCellType();
        switch (cellType) {
            case Cell.CELL_TYPE_BLANK:
                return null;
            case Cell.CELL_TYPE_BOOLEAN:
                return cell.getBooleanCellValue();
            case Cell.CELL_TYPE_ERROR:
                return cell.getErrorCellValue();
            case Cell.CELL_TYPE_FORMULA:
                return cell.getNumericCellValue();
            case Cell.CELL_TYPE_NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else if ("@".equals(cell.getCellStyle().getDataFormatString())) {
                    String value = df.format(cell.getNumericCellValue());
                    if (StringUtils.isBlank(value)) {
                        return null;
                    }
                    return value;
                } else if ("General".equals(cell.getCellStyle().getDataFormatString())) {
                    String value = nf.format(cell.getNumericCellValue());
                    if (StringUtils.isBlank(value)) {
                        return null;
                    }
                    return value;
                } else {
                    double doubleValue = cell.getNumericCellValue();
                    long longValue = (long) doubleValue;
                    if (doubleValue - longValue > 0) {
                        return String.valueOf(doubleValue);
                    }
                    else {
                        return longValue;
                    }
                }
            case Cell.CELL_TYPE_STRING:
                String value = cell.getStringCellValue();
                if (StringUtils.isBlank(value)) {
                    return null;
                } else {
                    return value;
                }
            default:
                return null;
        }
    }
    /**
     * 检查上传的Excel文件的第一行的列数是否与指定数量相等。
     *
     * @param wb 上传的文件流，用于读取Excel文件内容。
     * @param num 预期的列数，用于与文件中的实际列数进行比较。
     * @return 如果文件的第一行的列数与指定的数量相等，则返回true；否则返回false。
     *         如果读取文件过程中发生异常，也返回false。
     */
    public static Boolean getRowSize(Workbook wb, int num) {
        if (wb == null) {
            return false;
        }
        try {
            Sheet sheet = wb.getSheetAt(0);
            if (sheet == null) {
                return false;
            }
            Row firstRow = sheet.getRow(0);
            if (firstRow != null) {
                int rowSize = firstRow.getLastCellNum();
                return rowSize == num;
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return false;
    }
    /**
     *  <p>方法描述：判断是否存在数据，存在数据返回true
     * @param wb：Excel文件
     * @param rowSize：表头行数
     * @param colSize：列数
     * </p>
     * @MethodAuthor hsj 2025-03-25 14:41
     */
    public static boolean verifyWorkBook(Workbook wb, int rowSize,int colSize) {
        try {
            if (wb == null || wb.getNumberOfSheets() <= 0) {
                return false;
            }
            Sheet sheet = wb.getSheetAt(0);
            if (sheet == null) {
                return false;
            }
            for (int i = rowSize; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    for (int j = 0; j < colSize; j++) {
                        Cell cell = row.getCell(j);
                        //判断值是否为空，若不为空返回true
                        Object cellValue = getNewCellValue(cell);
                        if(ObjectUtil.isNotNull(cellValue)){
                            return true;
                        }
                    }
                }
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


}
