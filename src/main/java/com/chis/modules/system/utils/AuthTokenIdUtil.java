package com.chis.modules.system.utils;

import java.util.UUID;

import javax.servlet.http.HttpServletRequest;

import com.chis.common.utils.CacheUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.enumn.ResponseState;
import com.chis.modules.system.logic.AuthTokenIdPojo;
import com.chis.modules.system.service.SystemServiceImpl;

/**
 * <p>类描述：获取tokenId工具类</p>
 * @ClassAuthor qrr,2018年4月21日,AuthTokenIdUtil
 */
public class AuthTokenIdUtil {


    private static SystemServiceImpl systemService = SpringContextHolder.getBean(SystemServiceImpl.class);
    /** 单位编码为key的缓存 */
	private static final String UNIT_CACHE = "orgUnitCache";
    /** tokenId为key的缓存 */
	public static final String UNIT_TOKENID_CACHE = "unitTokenIdCache";
    /**
     * <p>方法描述：获取tokenId</p>
     *
     * @MethodAuthor mxp, 2018/2/9,getTokenId
     */
    public static String getTokenId(){
        AuthTokenIdPojo authTokenIdPojo = authUnit();
        if(authTokenIdPojo!=null){
            return authTokenIdPojo.getTokenId();
        }
        return null;
    }
    /**
     * <p>方法描述：获取tokenId</p>
     *
     * @MethodAuthor mxp, 2018/2/9,getTokenId
     */
    private static AuthTokenIdPojo authUnit()  {
        String unitCode = PropertyUtils.getValue("rpt.unitCode");
        String regCode = PropertyUtils.getValue("rpt.password");
        HttpServletRequest req = JsfUtil.getRequest();
        AuthTokenIdPojo authPojo = new AuthTokenIdPojo();
        // 判断账户名与密码是否为空
        if (StringUtils.isBlank(unitCode) || StringUtils.isBlank(regCode)) {
            authPojo.setType(ResponseState.UNIT_VERIFY_FAIL.getTypeNo());
            authPojo.setMess("账户名或密码为空！");
            return authPojo;
        }

        // 在数据库中查询其账户密码是否存在
        TsUnit selectUnit = authTokenId(unitCode, regCode);
        if (selectUnit == null) {
            authPojo.setType(ResponseState.UNIT_VERIFY_FAIL.getTypeNo());
            authPojo.setMess("账户名与密码输入有误！");
        } else {
            // 判断用户名密码在数据库是否已找到。
            authPojo.setTokenId(selectUnit.getTokenId());
            authPojo.setValidateTime("24");
            authPojo.setType(ResponseState.SUCCESS_PROCESS.getTypeNo());
            authPojo.setMess("验证成功！");


        }
        return authPojo;

    }

    /**
	 * 根据单位编码与注册码 缓存tokenId与单位信息
	 * @param unitCode
	 * @param regCode
	 * @return
	 */
	public static TsUnit authTokenId(String unitCode, String regCode) {
		if (StringUtils.isNotBlank(unitCode) && StringUtils.isNotBlank(regCode)) {
			TsUnit rtnUnit = selectUnit(unitCode, regCode);
			if (null != rtnUnit && null != rtnUnit.getRid()) {
				// 生成唯一键
				String uuid = UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
				rtnUnit.setTokenId(uuid);
				CacheUtils.put(UNIT_TOKENID_CACHE, uuid, rtnUnit);
			}
			return rtnUnit;
		}
		return null;
	}

	/**
	 * 根据单位编码与注册进行缓存数据
	 * @param unitCode
	 * @param regCode
	 * @return
	 */
	public static TsUnit selectUnit(String unitCode, String regCode) {
		if (StringUtils.isBlank(unitCode))
			return null;
		if (StringUtils.isBlank(regCode))
			return null;
		TsUnit rtnUnit = (TsUnit) CacheUtils.get(UNIT_CACHE, unitCode);
		if (null == rtnUnit){
			rtnUnit = systemService.findTsUnitByUnitCode(unitCode,regCode);
			if (null != rtnUnit && null != rtnUnit.getRid()) {
				CacheUtils.put(UNIT_CACHE, unitCode, rtnUnit);
			}
		}else{
			//如果缓存中的注册码跟传入的注册码不相同，则返回查询不到
			if( !regCode.equals(rtnUnit.getRegCode()))
				return null;
		}
		return rtnUnit;
	}
    /** 获取单位缓存 */
    public static TsUnit findTokenIdCachUnit(HttpServletRequest req, String tokenId) {
        if( null == req) return null;
        TsUnit cachUnit = (TsUnit) CacheUtils.get(UNIT_TOKENID_CACHE, tokenId);
        return cachUnit;
    }
}
