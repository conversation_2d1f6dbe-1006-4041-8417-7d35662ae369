package com.chis.modules.system.utils;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.*;
import com.chis.modules.system.enumn.SystemMessageEnum;
import com.chis.modules.system.enumn.WordReportCodeEnum;
import com.chis.modules.system.logic.WordReportDTO;
import com.chis.modules.system.logic.WordReportJson;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URLEncoder;

/**
 * @Description: 辅助生成word模板的报告
 *
 * @ClassAuthor pw,2022年04月24日,WordReportGenerateUtil
 */
public class WordReportGenerateUtil {

    /**
     * <p>方法描述：下载文件 </p>
     * pw 2025/6/9
     **/
    public static StreamedContent downloadReport(String filePath, String fileName, String contentType) {
        try {
            String path=getAbsolutePath(filePath);
            InputStream stream = new FileInputStream(path);
            return new DefaultStreamedContent(stream, contentType, URLEncoder.encode(fileName, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            SystemMessageEnum.FILE_NOT_EXIST.showMessage();
        }
        return null;
    }

    /**
     * <p>方法描述：验证文件是否存在 </p>
     * pw 2025/6/9
     **/
    public static boolean validateFileExists(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return false;
        }
        File file = new File(getAbsolutePath(filePath));
        if (null == file || !file.exists()) {
            return false;
        }
        return true;
    }

    /**
     * <p>方法描述：获取文件绝对路径 </p>
     * pw 2025/6/9
     **/
    public static String getAbsolutePath(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return null;
        }
        String path;
        String xnPath = JsfUtil.getAbsolutePath();
        if (null == xnPath) {
            xnPath = "";
        }
        if (!xnPath.endsWith("/")) {
            xnPath = xnPath + "/";
        }
        if (filePath.indexOf(xnPath) != -1) {
            path = filePath;
        } else {
            path = xnPath + filePath;
        }
        return path;
    }

    /**
     * <p>方法描述：生成文书 </p>
     * pw 2025/6/9
     **/
    public static String generateReport(Integer rid, String rptCode) {
        if (null == rid || StringUtils.isBlank(rptCode)) {
            return null;
        }
        String returnJson = null;
        try {
            WordReportJson reportJson = new WordReportJson();
            reportJson.setRid(rid);
            reportJson.setRptCode(rptCode);
            String requestJson = JSON.toJSONString(reportJson);
            String encodeJson = null;
            String debug = PropertyUtils.getValue("encrypt.debug");
            String encryptKey = PropertyUtils.getValue("encrypt.key");
            if ("true".equals(debug)) {
                encodeJson = requestJson;
            } else {
                //加密
                encodeJson = AesEncryptUtils.aesEncrypt(requestJson, encryptKey);
            }
            //调用接口
            String delUrl = PropertyUtils.getValue("delUrl");
            String reposeJson = HttpRequestUtil.httpRequestByRaw(delUrl + "/word/generate", encodeJson);

            //解密
            if ("true".equals(debug)) {
                returnJson = reposeJson;
            } else {
                returnJson = AesEncryptUtils.aesDecrypt(reposeJson, encryptKey);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        if (StringUtils.isBlank(returnJson)) {
            return null;
        }
        WordReportDTO returnDTO = JSON.parseObject(returnJson, WordReportDTO.class);
        if (null == returnDTO || !"00".equals(returnDTO.getType())) {
            System.out.println("生成失败："+returnDTO.getMess());
            return null;
        }
        return returnDTO.getFilePath();
    }

    /**
     * @Description: 按报告类型以及主表rid生成报告
     *
     * @MethodAuthor pw,2022年04月24日
     */
    public static WordReportDTO generateReport(WordReportCodeEnum codeEnum, Integer rid, boolean ifExtraNetUrl){
        WordReportDTO reportDTO = new WordReportDTO();
        if(null == rid){
            reportDTO.setMess("rid不允许为空，生成报告失败！");
            reportDTO.setType("99");
            return reportDTO;
        }
        if(null == codeEnum || StringUtils.isBlank(codeEnum.getRptCode())){
            reportDTO.setMess("模板报告类型异常，生成报告失败！");
            reportDTO.setType("99");
            return reportDTO;
        }
        try{
            WordReportJson reportJson = new WordReportJson();
            reportJson.setRid(rid);
            reportJson.setRptCode(codeEnum.getRptCode());
            String requestJson = JSON.toJSONString(reportJson);
            String encodeJson = null;
            String debug = PropertyUtils.getValue("encrypt.debug");
            String encryptKey = PropertyUtils.getValue("encrypt.key");
            if ("true".equals(debug)) {
                encodeJson = requestJson;
            }else {
                //加密
                encodeJson = AesEncryptUtils.aesEncrypt(requestJson, encryptKey);
            }
            //调用接口
            String delUrl = PropertyUtils.getValue(ifExtraNetUrl? "delExtraNetUrl" : "delUrl");
            String reposeJson = HttpRequestUtil.httpRequestByRaw(delUrl+(null == codeEnum.getRequestUrl() ? "/word/generate" : codeEnum.getRequestUrl()),encodeJson);

            String returnJson = null;
            //解密
            if ("true".equals(debug)) {
                returnJson = reposeJson;
            }else {
                returnJson = AesEncryptUtils.aesDecrypt(reposeJson, encryptKey);
            }
            if(StringUtils.isNotBlank(returnJson)){
                reportDTO = JSON.parseObject(returnJson, WordReportDTO.class);
            }
        }catch(Exception e){
            e.printStackTrace();
            reportDTO.setMess("生成报告失败！");
            reportDTO.setType("99");
        }
        return reportDTO;
    }
}
