package com.chis.modules.system.utils;

import java.awt.Color;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.logic.PdfMarkBean;
import com.google.common.collect.Lists;
import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.FontFactory;
import com.lowagie.text.HeaderFooter;
import com.lowagie.text.Image;
import com.lowagie.text.PageSize;
import com.lowagie.text.Phrase;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.ColumnText;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfCopy;
import com.lowagie.text.pdf.PdfGState;
import com.lowagie.text.pdf.PdfImportedPage;
import com.lowagie.text.pdf.PdfReader;
import com.lowagie.text.pdf.PdfStamper;
import com.lowagie.text.pdf.PdfWriter;

/**
 * 多个图片生成PDF
 * 
 * <AUTHOR>
 * @createTime 2016年7月26日
 */
public class GeneratePdf {

	/**
	 * 生成PDF文件
	 * 
	 * @param imagePath
	 * @param mOutputPdfFileName
	 * @param marks
	 * @return
	 */
	public File pdf(String imagePath, String mOutputPdfFileName,
			List<PdfMarkBean> marks) {
		Document doc = new Document(PageSize.A4, 20, 20, 20, 20);
		try {
			PdfWriter writer = PdfWriter.getInstance(doc, new FileOutputStream(
					mOutputPdfFileName));
			doc.open();

			String[] pathArr = imagePath.split(",");
			for (String path : pathArr) {
				if (StringUtils.isNotBlank(path)) {
					doc.newPage();
					Image png1 = Image.getInstance(path);
					float heigth = png1.getHeight();
					float width = png1.getWidth();
					int percent = this.getPercent2(heigth, width);
					png1.setAlignment(Image.MIDDLE);
					png1.scalePercent(percent);
					doc.add(png1);
					if (marks != null && marks.size() > 0) {
						this.handleText(writer, marks);
					}
				}
			}
			doc.close();
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (DocumentException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}

		File mOutputPdfFile = new File(mOutputPdfFileName);
		if (!mOutputPdfFile.exists()) {
			mOutputPdfFile.deleteOnExit();
			return null;
		}
		return mOutputPdfFile;
	}

	/**
	 * 根据原PDF文件路径，
	 * 
	 * @param filePath
	 */
	public static List<String> splitPdf(String filePath,
			BigDecimal singleFileSize) {
		List<String> rstList = Lists.newLinkedList();
		try {

			// we create a reader for a certain document
			PdfReader reader = new PdfReader(filePath);
			 /** 
             * 解决Exception in thread "main" java.lang.IllegalArgumentException: 
             * PdfReader not opened with owner password 
             */  
			java.lang.reflect.Field f = reader.getClass().getDeclaredField(  
                    "encrypted");  
            f.setAccessible(true);  
            f.set(reader, false);  
			
			
			// we retrieve the total number of pages
			int filePages = reader.getNumberOfPages();
			File file = new File(filePath);
			long fileSize = file.length();

			int fileNum = BigDecimal.valueOf(fileSize)
					.divide(singleFileSize, RoundingMode.UP).intValue();
			int pagesOfFile = filePages / fileNum;

			for (int i = 1; i <= fileNum; i++) {
				String splitPath = filePath.substring(0,
						filePath.lastIndexOf("."))
						+ "-" + StringUtils.leftPad(i + "", 3, "0") + ".pdf";
				rstList.add(splitPath);
				FileOutputStream fos = new FileOutputStream(splitPath);

				int start = (i - 1) * pagesOfFile + 1;
				int end = 0;
				if (i != fileNum) {
					end = i * pagesOfFile;
				} else {
					end = filePages;
				}
				splitPDF(reader, fos, start, end);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return rstList;
	}

	/**
	 * 拆分PDF文件，从原PDF文件中，从fromPage页开始，到toPage页面的内容，生成到outputStream的文件中
	 * 
	 * @param inputPDF
	 *            原PDF文件
	 * @param outputStream
	 *            生成文件的输出流
	 * @param fromPage
	 *            开始页码，一般从第1页开始
	 * @param toPage
	 *            结束页面
	 */
	public static void splitPDF(PdfReader inputPDF, OutputStream outputStream,
			int fromPage, int toPage) {
		Document document = null;
		PdfCopy copy = null;
		try {

			int n = inputPDF.getNumberOfPages();
			if (toPage == 0) {
				toPage = n;
			}

			document = new Document(inputPDF.getPageSize(1));
			copy = new PdfCopy(document, outputStream);
			document.open();
			for (int j = fromPage; j <= toPage; j++) {
				document.newPage();
				PdfImportedPage page = copy.getImportedPage(inputPDF, j);
				copy.addPage(page);
			}
			document.close();

		} catch (IOException e) {
			e.printStackTrace();
		} catch (DocumentException e) {
			e.printStackTrace();
		}
	}

	private void handleText(PdfWriter writer, List<PdfMarkBean> marks) {
		PdfContentByte canvas = writer.getDirectContent();
		PdfGState gs = new PdfGState();
		gs.setFillOpacity(0.7f);
		canvas.setGState(gs);
		try {
			BaseFont font = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",
					BaseFont.NOT_EMBEDDED);
			for (PdfMarkBean mark : marks) {
				Phrase phrase = new Phrase(mark.getContent(), new Font(font,
						12, Font.COURIER, Color.black));
				ColumnText.showTextAligned(canvas, Element.ALIGN_UNDEFINED,
						phrase, mark.getX(), mark.getY(), mark.getZ());
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 
	 * 
	 * 
	 * @param inputFile
	 *            源文件路径
	 * 
	 * @param outputFile
	 *            目标文件路径
	 * 
	 * @param waterMarkName
	 *            水印文字内容
	 * 
	 * @param imageFilePath
	 *            水印图片的路径
	 */

	private static void waterMark(String inputFile, String outputFile,
			String waterMarkName) {
		try {
			PdfReader reader = new PdfReader(inputFile);
			PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(
					outputFile));
			Rectangle pageRect = null;
			PdfContentByte content = null;
			//
			BaseFont base = BaseFont.createFont(BaseFont.COURIER_BOLD,
					BaseFont.CP1250, BaseFont.NOT_EMBEDDED);
			// new BaseFont();

			// Image image = Image.getInstance(imageFilePath);
			// image.setAbsolutePosition(0, 0);
			// 设置透明度为0.4
			PdfGState gs = new PdfGState();
			gs.setFillOpacity(0.4f);
			gs.setStrokeOpacity(0.4f);
			int toPage = stamper.getReader().getNumberOfPages();
			for (int i = 1; i <= toPage; i++) {
				pageRect = stamper.getReader().getPageSizeWithRotation(i);
				// 计算水印X,Y坐标
				float x = pageRect.getWidth() / 2;
				float y = pageRect.getHeight() / 2;
				// 获得PDF最顶层
				content = stamper.getOverContent(i);
				content.saveState();
				// set Transparency
				content.setGState(gs);
				content.beginText();
				content.setColorFill(Color.GRAY);
				content.setFontAndSize(base, 60);
				// 水印文字成45度角倾斜
				content.showTextAligned(Element.ALIGN_CENTER, waterMarkName, x,
						y, 45);
				content.endText();
			}
			stamper.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public int getPercent1(float h, float w) {
		int p = 0;
		float p2 = 0.0f;
		if (h > w) {
			p2 = 297 / h * 100;
		} else {
			p2 = 210 / w * 100;
		}
		p = Math.round(p2);
		return p;
	}

	private int getPercent2(float h, float w) {
		int p = 0;
		float p2 = 0.0f;
		p2 = 530 / w * 100;
		p = Math.round(p2);
		return p;
	}

	public static void main(String[] args) {
		// waterMark("D:/webFile/pdi/xc/icon2.pdf",
		// "D:/webFile/pdi/xc/icon3.pdf",DateUtils.formatDate(new Date(),
		// "yyyy-MM-dd HH:mm:ss"));
		String filePath = "c:/tmp/aaa.pdf";
		System.err.println(filePath.substring(0, filePath.lastIndexOf("."))
				+ "-" + StringUtils.leftPad(1 + "", 3, "0") + ".pdf");

	}
}