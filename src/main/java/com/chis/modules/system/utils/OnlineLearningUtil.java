package com.chis.modules.system.utils;

import com.alibaba.fastjson.JSONObject;
import com.chis.common.utils.OkHttpUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.service.CommServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 第三方-在线学习课程接口
 *
 * <AUTHOR>
 * @version 1.0
 */
public class OnlineLearningUtil {
    private static final Logger logger = LoggerFactory.getLogger(OnlineLearningUtil.class);
    private static final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private static String ONLINE_LEARNING_SERVER_URL = "";
    private static String ONLINE_LEARNING_SERVER_OUT_URL = "";
    private static final String GET_LOGIN_TO_TOKEN_URL = "/zxjy/getLoginToToken-0";
    private static final String USER_LOGIN_ACTION_URL = "/zxjy/userLoginAction-0";
    private static final String AUTH_TOKEN_URL = "/zxjy/authToken-0";
    private static final String GET_RATE_LEANING_URL = "/zxjy/getRateLeaning-0";

    public static Map<String, String> getLoginToToken(TsUserInfo user) {
        return onlineLearning("1", user);
    }

    public static Map<String, String> userLoginAction(TsUserInfo user) {
        return onlineLearning("2", user);
    }

    public static Map<String, String> getRateLeaning(TsUserInfo user) {
        return onlineLearning("3", user);
    }

    /**
     * 调用第三方-在线学习课程接口
     *
     * @param type 接口类型 <pre>1: 课程管理接口</pre><pre>2: 在线学习接口</pre><pre>3: 获取学习进度</pre>
     * @param user 用户信息
     * @return 调用结果 <pre>type-响应类型编码 00:成功 </pre>
     */
    private static Map<String, String> onlineLearning(String type, TsUserInfo user) {
        Map<String, String> returnMap = new HashMap<>();
        ONLINE_LEARNING_SERVER_URL = StringUtils.objectToString(commService.findParamValue("ONLINE_LEARNING_SERVER_URL"));
        ONLINE_LEARNING_SERVER_OUT_URL = StringUtils.objectToString(commService.findParamValue("ONLINE_LEARNING_SERVER_OUT_URL"));
        boolean unExist = StringUtils.isBlank(ONLINE_LEARNING_SERVER_URL) || "http://".equals(ONLINE_LEARNING_SERVER_URL)
                || StringUtils.isBlank(ONLINE_LEARNING_SERVER_OUT_URL) || "http://".equals(ONLINE_LEARNING_SERVER_OUT_URL);
        if (unExist) {
            returnMap.put("type", "99");
            returnMap.put("mess", "请检查系统参数（江苏在线培训管理第三方系统地址、江苏在线培训管理第三方系统外网地址）！");
            return returnMap;
        }
        if (user == null || StringUtils.isBlank(user.getUserNo()) || StringUtils.isBlank(user.getPassword())) {
            returnMap.put("type", "01");
            returnMap.put("mess", "权限验证失败！");
            return returnMap;
        }
        //获取tokenId
        String tokenId;
        JSONObject responseAuthTokenJson = getTokenId(user, "1".equals(type) ? "1" : "2");
        if (responseAuthTokenJson == null) {
            returnMap.put("type", "99");
            returnMap.put("mess", "获取token失败！");
            return returnMap;
        }
        if ("00".equals(responseAuthTokenJson.get("type"))) {
            tokenId = StringUtils.objectToString(responseAuthTokenJson.get("tokenId"));
        } else {
            returnMap.put("type", "99");
            returnMap.put("mess", StringUtils.objectToString(responseAuthTokenJson.get("mess")));
            return returnMap;
        }
        if (StringUtils.isBlank(tokenId)) {
            returnMap.put("type", "99");
            returnMap.put("mess", "获取token失败！");
            return returnMap;
        }
        //跳转课程管理
        if ("1".equals(type)) {
            returnMap.put("type", "00");
            returnMap.put("mess", "成功");
            returnMap.put("data", ONLINE_LEARNING_SERVER_OUT_URL + GET_LOGIN_TO_TOKEN_URL + "?tokenId=" + tokenId);
            return returnMap;
        }
        //跳转在线学习
        if ("2".equals(type)) {
            returnMap.put("type", "00");
            returnMap.put("mess", "成功");
            returnMap.put("data", ONLINE_LEARNING_SERVER_OUT_URL + USER_LOGIN_ACTION_URL + "?tokenId=" + tokenId);
            return returnMap;
        }
        //调用获取学习进度接口
        if ("3".equals(type)) {
            return getRateLeaning(tokenId);
        }
        return returnMap;
    }

    private static Map<String, String> getRateLeaning(String tokenId) {
        Map<String, String> returnMap = new HashMap<>();
        Map<String, String> postGetRateLeaningMap = new HashMap<>();
        postGetRateLeaningMap.put("tokenId", tokenId);
        JSONObject responseGetRateLeaningJson = sendRequest(false, ONLINE_LEARNING_SERVER_URL + GET_RATE_LEANING_URL, postGetRateLeaningMap);
        if (responseGetRateLeaningJson == null) {
            returnMap.put("type", "99");
            returnMap.put("mess", "学习进度获取失败！");
            return returnMap;
        }
        if ("00".equals(responseGetRateLeaningJson.get("type"))) {
            String data = StringUtils.objectToString(responseGetRateLeaningJson.get("data"));
            if (StringUtils.isBlank(tokenId)) {
                returnMap.put("type", "99");
                returnMap.put("mess", "学习进度获取失败！");
                return returnMap;
            }
            returnMap.put("type", "00");
            returnMap.put("mess", "成功");
            returnMap.put("data", data);
        } else {
            returnMap.put("type", "99");
            returnMap.put("mess", StringUtils.objectToString(responseGetRateLeaningJson.get("mess")));
        }
        return returnMap;
    }

    private static JSONObject getTokenId(TsUserInfo user, String type) {
        Map<String, String> postAuthTokenMap = new HashMap<>();
        postAuthTokenMap.put("userNo", user.getUserNo());
        postAuthTokenMap.put("passWord", user.getPassword());
        postAuthTokenMap.put("type", type);
        return sendRequest(true, ONLINE_LEARNING_SERVER_URL + AUTH_TOKEN_URL, postAuthTokenMap);
    }

    private static JSONObject sendRequest(boolean isPost, String url, Map<String, String> postMap) {
        String responseBody = "";
        try {
            if (isPost) {
                responseBody = OkHttpUtils.post(url, postMap, null);
            } else {
                responseBody = OkHttpUtils.get(url, postMap);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), new Throwable(e));
            e.printStackTrace();
        }
        return JSONObject.parseObject(StringUtils.objectToString(responseBody));
    }
}
