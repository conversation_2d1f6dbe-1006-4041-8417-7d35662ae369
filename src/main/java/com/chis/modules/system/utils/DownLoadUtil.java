package com.chis.modules.system.utils;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.StringWriter;
import java.net.URLEncoder;

import javax.faces.context.FacesContext;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

public class DownLoadUtil {

	public static String uploadFile2Database(FileInputStream fis)
			throws IOException {
		StringWriter sw = new StringWriter();
		int len = 1;
		byte[] temp = new byte[len];

		/* 16进制转化模块 */
		for (; (fis.read(temp, 0, len)) != -1;) {
			if (temp[0] > 0xf && temp[0] <= 0xff) {
				sw.write(Integer.toHexString(temp[0]));
			} else if (temp[0] >= 0x0 && temp[0] <= 0xf) {// 对于只有1位的16进制数前边补“0”
				sw.write("0" + Integer.toHexString(temp[0]));
			} else { // 对于int<0的位转化为16进制的特殊处理，因为Java没有Unsigned
				// int，所以这个int可能为负数
				sw.write(Integer.toHexString(temp[0]).substring(6));
			}
		}
		return sw.toString();
	}

	public static void writeNew2Binary(ServletOutputStream sos, String replaced)
			throws NumberFormatException, IOException {
		for (int i = 0; i < replaced.length(); i = i + 2) {
			sos.write(Integer.parseInt(replaced.substring(i, i + 2), 16));
		}
	}

	/**
	 * 文件下载
	 */
	public static void downFile(String fileName, String fileContent) {
		try {
			HttpServletResponse httpServletResponse = (HttpServletResponse) FacesContext
					.getCurrentInstance().getExternalContext().getResponse();

			// 从页面下载的流
			httpServletResponse.setHeader(
					"Content-disposition",
					"attachment; filename="
							+ URLEncoder.encode(fileName, "UTF-8"));
			httpServletResponse
					.setContentLength((int) fileContent.length() / 2);
			httpServletResponse.setContentType("application/x-download");
			// 文件名编码转换
			ServletOutputStream servletOutputStream = httpServletResponse
					.getOutputStream();
			writeNew2Binary(servletOutputStream, fileContent);
			servletOutputStream.flush();
			servletOutputStream.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		FacesContext.getCurrentInstance().responseComplete();
	}
}
