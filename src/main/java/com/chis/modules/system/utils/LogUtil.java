package com.chis.modules.system.utils;

import java.io.IOException;
import java.util.Date;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.enumn.LogNoEnum;
import org.apache.log4j.Logger;

import com.chis.common.utils.Exceptions;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TsSystemLog;
import com.chis.modules.system.service.LogImpl;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;

/**
 * 记录日志的工具类
 * <AUTHOR>
 */
public class LogUtil {
	
	private static LogImpl logService = SpringContextHolder.getBean(LogImpl.class);
	private static Logger log = Logger.getLogger(LogUtil.class);

	/**
	 * 记录日志到数据库中
	 * @param msg 具体的错误信息
	 */
	public static void logIntoDB(String msg) {
		logService.logError(msg);
	}
	
	/**
	 * 记录日志到数据库中
	 * @param e 具体的错误信息
	 */
	public static void logIntoDB(Throwable e) {
		try {
            logService.logError(Exceptions.exception(e));
		} catch (IOException e1) {
			e1.printStackTrace();
		}
	}
	
	/**
	 * 记录日志到数据库中
	 * @param tsSystemLog 日志实体
	 */
	public static void logIntoDB(TsSystemLog tsSystemLog) {
		logService.logError(tsSystemLog);
	}

	/**
	* @Description : 记录日志到数据库中
	* @MethodAuthor: anjing
	* @Date : 2020/1/10 14:10
	**/
    public static void logIntoDB(LogNoEnum logNo, String hintInfo, String detailInfo) {
        try {
            if(null == Global.getUser()) {
                return;
            }
        } catch (Exception e) {
            return;
        }

        Date now = new Date();
        TsSystemLog log = new TsSystemLog();
        log.setHappenDate(now);
        log.setUserNo(Global.getUser().getUserNo());
        if(StringUtils.isNoneBlank(Global.getSessionData().getMenuId()) && null != Global.getSessionData().getMenuId()) {
            log.setMenuId(Global.getSessionData().getMenuId());
        }
        if("1001".equals(logNo.getTypeNo()) || "1002".equals(logNo.getTypeNo())){
            log.setMenuId(null);
        }
        log.setErrorNo(logNo.getTypeNo());
        if(hintInfo.length() > 100) {
            log.setHintInfo(hintInfo.substring(0,99));
        } else {
            log.setHintInfo(hintInfo);
        }
        log.setDetailInfo(detailInfo);
        log.setClientIp(StringUtils.getRemoteAddr(JsfUtil.getRequest()));

        // 异步保存日志
        new SaveLogThread(log).start();
    }

    /**
    * @Description : 保存日志线程
    * @MethodAuthor: anjing
    * @Date : 2020/1/10 14:16
    **/
    public static class SaveLogThread extends Thread{
        private TsSystemLog log;

        public SaveLogThread(TsSystemLog log){
            super(SaveLogThread.class.getSimpleName());
            this.log = log;
        }

        @Override
        public void run() {
            // 保存日志信息
            logService.saveObj(log);
        }
    }
}
