package com.chis.modules.system.utils;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.primefaces.push.PushContext;
import org.primefaces.push.PushContextFactory;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TdMsgMain;
import com.chis.modules.system.entity.TdMsgSub;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.logic.WebSocketMsgBean;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * 系统内发送消息
 * 
 * <AUTHOR>
 */
public class MsgSendUtil {

	private static SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private static CommServiceImpl commService = (CommServiceImpl) SpringContextHolder
			.getBean(CommServiceImpl.class);

	private MsgSendUtil() {

	}

	/**
	 * 给用户发送消息，消息内容为：您有1条未阅消息！ 链接到我的消息
	 * 
	 * @param userIds
	 *            用户IDS
	 */
	public static void sendNewMsg(String userIds) {
		Map<String, String> map = commService.buildMsg(userIds);
		if (null != map && map.size() > 0) {
			PushContext pushContext = PushContextFactory.getDefault()
					.getPushContext();

			Set<String> set = map.keySet();
			for (String key : set) {
				WebSocketMsgBean bean = new WebSocketMsgBean();
				bean.setMsgType("1");
				bean.setGlobalInfo(StringEscapeUtils.escapeHtml4(map.get(key)));
				pushContext.push("/MsgChanel" + key, JSON.toJSONString(bean));
				/*
				 * pushContext.push("/psnTodoChanel" + key,
				 * StringEscapeUtils.escapeHtml4(map.get(key)));
				 */
			}
		}
	}

	/**
	 * 给用户发送消息，消息内容为：您有1条未阅消息！ 链接到我的消息
	 * 
	 * @param main
	 *            消息主表
	 */
	public static void sendNewMsg(TdMsgMain main) {
		List<TdMsgSub> subList = main.getTdMsgSubs();
		if (null != subList && subList.size() > 0) {
			StringBuilder sb = new StringBuilder();
			for (TdMsgSub t : subList) {
				sb.append(",").append(t.getTsUserInfo().getRid());
			}
			sendNewMsg(sb.toString().substring(1));
		}
	}

	/**
	 * 刷新个人页面上的未读消息条数
	 */
	public static void updateMsgInfo() {
		PushContext pushContext = PushContextFactory.getDefault()
				.getPushContext();
		WebSocketMsgBean bean = new WebSocketMsgBean();
		bean.setMsgType("2");
		bean.setGlobalInfo("");
		pushContext.push(
				"/MsgChanel"
						+ ((SessionData) JsfUtil.getSessionMap().get(
								SessionData.SESSION_DATA)).getUser().getRid(),
				JSON.toJSONString(bean));
		// pushContext.push("/TodoChanel","");
	}

	/**
	 * 改变消息的状态
	 * 
	 * @param id
	 *            消息子表id
	 * @param state
	 *            修改的状态
	 * @param userId
	 *            用户id
	 */
	public static void updateMsgState(String id, String state, Integer userId) {
		systemModuleService.editSubState(id, state, userId);
		updateMsgInfo();
	}

	/**
	 * 其他页面操作将消息变为已阅，更改待办任务状态 消息小类，业务主键id，是否待办任务，待办任务状态,用户id
	 */
	public static void updateMsgStateFromOther(Integer msgType,
			String businessId, Integer isTodo, Integer todoState, Integer userId) {
		if (null != msgType && StringUtils.isNotBlank(businessId)
				&& null != userId) {
			systemModuleService.updateMsgState(msgType, businessId, userId);
		}
		if (isTodo == 1) {// 需要更新待办任务状态
			if (null != msgType && StringUtils.isNotBlank(businessId)
					&& null != todoState && null != userId) {
				systemModuleService.updateTodoState(msgType, businessId,
						userId, todoState);
			}
		}
	}

}
