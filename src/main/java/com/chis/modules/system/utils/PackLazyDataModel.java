package com.chis.modules.system.utils;

import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.interfaces.IProcessPackData;
import com.chis.modules.system.service.CommServiceImpl;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 封装分页模型
 * <AUTHOR>
 */
public class PackLazyDataModel<T> extends LazyDataModel<T>{

	private static final long serialVersionUID = -2705120688106331128L;
	private CommServiceImpl commService = (CommServiceImpl)SpringContextHolder.getBean(CommServiceImpl.class);
	private List data;
	private List<T> packData;
	private String hql;
	private String countHql;
    /**绑定表格的id*/
    private String tableId;
    /**查询条件*/
    private Map<String, Object> paramMap;
    /**数据处理接口*/
    private IProcessPackData<T> processData;
    /**是否第一次查询*/
    private boolean firstSearch = Boolean.TRUE;
    /**传入的是否是SQL语句，默认HQL语句*/
    private boolean ifSQL = Boolean.FALSE;

	public PackLazyDataModel() {
		this.init();
	}

	public PackLazyDataModel(String hql, String countHql, Map<String, Object> paramMap, String tableId) {
		this.hql = hql;
		this.countHql = countHql;
		this.paramMap = paramMap;
        this.tableId = tableId;
		this.init();
	}

	public PackLazyDataModel(String hql, String countHql, Map<String, Object> paramMap, IProcessPackData processData, String tableId) {
		this.hql = hql;
		this.countHql = countHql;
		this.paramMap = paramMap;
		this.processData = processData;
        this.tableId = tableId;
		this.init();
	}

	public PackLazyDataModel(String hql, String countHql, Map<String, Object> paramMap, IProcessPackData processData, String tableId, boolean ifSQL) {
		this.hql = hql;
		this.countHql = countHql;
		this.paramMap = paramMap;
		this.processData = processData;
        this.tableId = tableId;
        this.ifSQL = ifSQL;
		this.init();
	}
	
	private void init() {
		if(this.countHql == null) {
			this.setRowCount(0);
			return;
		}
		if(this.ifSQL) {
            this.setRowCount(this.commService.findTotalNumBySQL(this.countHql, this.paramMap));
		}else {
			this.setRowCount(this.commService.findTotalNum(this.countHql, this.paramMap));
		}
    }
	
	@Override
	public List<T> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map<String, String> filters) {
		int rowCount = this.getRowCount();
        if (rowCount == 0) {
            return Collections.emptyList();
        }
        if (first >= rowCount) {
            // 当查询使用的首记录超出记录总数时，调整首记录
            if (rowCount % pageSize == 0) {
                first = ((rowCount / pageSize) - 1) * pageSize;
            } else {
                first = (rowCount / pageSize) * pageSize;
            }
        }
		if (this.ifSQL) {
			this.data = this.commService.findDataBySQL(this.hql, this.paramMap, first, pageSize);
		} else {
			this.data = this.commService.findData(this.hql, this.paramMap, first, pageSize);
		}
        if(null != this.processData) {
			packData = this.processData.processPackData(this.data);
			return packData;
		}
        return this.data;
	}

	public List getData() {
		return data;
	}

	public void setData(List data) {
		this.data = data;
	}

	public List<T> getPackData() {
		return packData;
	}

	public void setPackData(List<T> packData) {
		this.packData = packData;
	}
}
