package com.chis.modules.system.utils;

import com.chis.common.utils.Exceptions;
import com.chis.common.utils.HttpRequestUtil;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.ZipUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <p>修订内容：http请求</p>
 * @ClassReviser qrr,2018年4月21日,HttpRequestComponent
 * */
@Component
public class HttpRequestComponent{

	private static Logger logger = Logger.getLogger(HttpRequestComponent.class);
	
	public static String httpRequestForFile(String requestUrl, FileInputStream fis) throws IOException {
		return HttpRequestUtil.httpRequest(requestUrl, fis);
	}

	public static String httpRequest(String requestUrl, String msg, String methon) throws IOException {
		return HttpRequestUtil.httpRequest(requestUrl, methon, msg);
	}

	public static String httpRequest(String requestUrl, String msg){
		String zipFilePath = null;
		File zipFile = null;
		FileInputStream fis = null;
		String returnJson = null;
		try {
			zipFilePath = ZipUtils.ZipFileToZip(PropertyUtils.getValue("virtual.directory")+
					"temp/", msg);
			zipFile = new File(zipFilePath);
			fis = new FileInputStream(zipFile);
			returnJson = httpRequestForFile(requestUrl, fis);
		} catch (IOException e) {
			e.printStackTrace();
		} catch (RuntimeException e) {
			e.printStackTrace();
		}finally{
			if(fis!=null){
				try {
					fis.close();
				} catch (IOException e) {
					e.printStackTrace();
					logger.error(Exceptions.getStackTraceAsString(e));
				}
			}
			if(zipFile!=null && zipFile.exists()){
				zipFile.delete();
			}
		}
		return returnJson;
	}

	/**
	 *
	 * <p>方法描述：使用hpps进行post请求</p>
	 *
	 * @MethodAuthor xq,2017年11月14日,doPost
	 */
	public static String doPost(String url, Map<String,String> map, String charset){
		HttpClient httpClient = null;
		HttpPost httpPost = null;
		String result = null;
		try{
			httpClient = new SSLClient();
			httpPost = new HttpPost(url);
			//设置参数
			List<NameValuePair> list = new ArrayList<NameValuePair>();
			Iterator<Map.Entry<String, String>> iterator = map.entrySet().iterator();
			while(iterator.hasNext()){
				Map.Entry<String,String> elem = (Map.Entry<String, String>) iterator.next();
				list.add(new BasicNameValuePair(elem.getKey(),elem.getValue()));
			}
			if(list.size() > 0){
				UrlEncodedFormEntity entity = new UrlEncodedFormEntity(list,charset);
				httpPost.setEntity(entity);
			}
			HttpResponse response = httpClient.execute(httpPost);
			if(response != null){
				HttpEntity resEntity = response.getEntity();
				if(resEntity != null){
					result = EntityUtils.toString(resEntity,charset);
				}
			}
		}catch(Exception ex){
			ex.printStackTrace();
		}
		return result;
	}
}
