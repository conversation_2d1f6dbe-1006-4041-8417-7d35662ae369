package com.chis.modules.system.utils;

import com.chis.common.utils.CacheUtils;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.sm.Sm4Util;
import com.chis.modules.system.logic.Constants;

/**
 *  <p>类描述：国密4 加解密字段单独处理-只适合精确查询</p>
 * @ClassAuthor hsj 2023-01-14 9:10
 */
public class EncryptFieldUtil {

    private static String ENCRY_KEY = Constants.ENCRY_KEY;
    /**
     *  <p>方法描述：加密</p>
     * @MethodAuthor hsj 2023-01-14 9:13
     */
    public static String strEncode(String str) {
        String infoEncryKey = CacheUtils.get("ENCRYKEY","infoEncryKey") == null ? null : CacheUtils.get("ENCRYKEY","infoEncryKey").toString();
        if(StringUtils.isNotBlank(infoEncryKey)){
            return  Sm4Util.strEncode(str,infoEncryKey);
        }
        return str;
    }
    /**
     *  <p>方法描述：解密</p>
     * @MethodAuthor hsj 2023-01-14 9:13
     */
    public static String strDecode(String str) {
        String infoEncryKey = CacheUtils.get("ENCRYKEY","infoEncryKey") == null ? null : CacheUtils.get("ENCRYKEY","infoEncryKey").toString();
        if(StringUtils.isNotBlank(infoEncryKey)){
            return  Sm4Util.strDecode(str,infoEncryKey);
        }
        return str;
    }

    /**
     *  <p>方法描述：解密-无需根据参数控制
     *  适用于：与数据库无关仅限于页面传输解密</p>
     * @MethodAuthor hsj 2023-08-24 10:11
     */
    public static String parameterFreeDecode(String str) {
        if(StringUtils.isBlank(str) || StringUtils.isBlank(ENCRY_KEY)){
            return str;
        }
        return  Sm4Util.strDecode(str,ENCRY_KEY);
    }
}
