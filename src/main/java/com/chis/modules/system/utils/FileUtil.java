package com.chis.modules.system.utils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.UUID;

import org.primefaces.context.RequestContext;
import org.primefaces.model.UploadedFile;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import com.chis.common.utils.FileUtils;
import com.chis.common.utils.JsfUtil;
import com.lowagie.text.pdf.BaseFont;

/**
 * <AUTHOR>
 * @createDate 2015-1-14
 */
public class FileUtil {


    /**
     * pdf 打印
     * @param htmlCode 打印内容
     * @param targetPath 目标地址
     * @param title 标题
     * @param direction 打印方向
     */
    public static void pdfPrint(String htmlCode,String targetPath,String title,int direction){
        String pdfdirPath = JsfUtil.getAbsolutePath()+"temp/toPDFprint";
        reMakeDir(pdfdirPath);
        String uid = UUID.randomUUID().toString().replaceAll("-", "");
        String pdfPath = pdfdirPath+"/"+uid+".pdf";
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
        html.append("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
        html.append ("<head>");
        html.append("<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />");
        html.append("<style type=\"text/css\" mce_bogus=\"1\"> body {font-family: SimSun;}");
        if(direction == 1){
            html.append(" @page  {size: 297mm 210mm;} ");
        }
        html.append("</style>");
        html.append("</head>");
        html.append("<body>");
        html.append("<div>");
        html.append(htmlCode);
        html.append("</div>");
        html.append("</body></html>");
        htmlCode2PDF(html.toString(), pdfPath,null);
        StringBuilder msg = new StringBuilder();
        msg.append("top.win_TabMenu.OpenTabWin(\"m01\", '");
        msg.append(title);
        msg.append("', '");
        msg.append(targetPath);
        msg.append("?filesrc=/temp/toPDFprint/").append(uid).append(".pdf");
        msg.append("', 'm03');");
        RequestContext.getCurrentInstance().execute(msg.toString());
    }

    /**
     * 带图片打印
     * @param htmlCode 打印内容
     * @param targetPath 目标地址
     * @param title 标题
     * @param direction 打印方向
     * @param picPath  图片相对地址
     */
    public static void pdfPrint(String htmlCode,String targetPath,String title,int direction,String picPath ){
        String pdfdirPath = JsfUtil.getAbsolutePath()+"temp/toPDFprint";
        reMakeDir(pdfdirPath);
        String uid = UUID.randomUUID().toString().replaceAll("-", "");
        String pdfPath = pdfdirPath+"/"+uid+".pdf";
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
        html.append("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
        html.append ("<head>");
        html.append("<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />");
        html.append("<style type=\"text/css\" mce_bogus=\"1\"> body {font-family: SimSun;}");
        if(direction == 1){
            html.append(" @page  {size: 297mm 210mm;} ");
        }
        html.append("</style>");
        html.append("</head>");
        html.append("<body>");
        html.append("<div>");
        html.append(htmlCode);
        html.append("</div>");
        html.append("</body></html>");
        htmlCode2PDF(html.toString(), pdfPath,picPath);
        StringBuilder msg = new StringBuilder();
        msg.append("top.win_TabMenu.OpenTabWin(\"m01\", '");
        msg.append(title);
        msg.append("', '");
        msg.append(targetPath);
        msg.append("?filesrc=/temp/toPDFprint/").append(uid).append(".pdf");
        msg.append("', 'm03');");
        RequestContext.getCurrentInstance().execute(msg.toString());
    }


    /**
     * 创建目录，如果已存在则清空目录内容
     * @param filepath 目录路径
     */
    public static void reMakeDir(String filepath){
        File temp = new File(filepath);
        if(!temp.exists()){
            temp.mkdirs();
        }else{
            if(temp.isDirectory()){
                File[] files = temp.listFiles();
                for (File f : files) {
                    f.delete();
                }
            }
        }
    }

    /**
     * HTML转pdf
     * @param htmlCode HTML内容
     * @param pdfPath PDF文件路劲
     */
    public static void htmlCode2PDF(String htmlCode, String pdfPath,String picPath) {
        try {
            OutputStream os = new FileOutputStream(pdfPath);
            ITextRenderer renderer = new ITextRenderer();
            ITextFontResolver fontResolver = renderer.getFontResolver();
            fontResolver.addFont(FileUtils.getWebRootPath() + "/resources/fonts/simsun.ttc", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            renderer.setDocumentFromString(htmlCode);
            if(picPath != null){
                renderer.getSharedContext().setBaseURL("file:/"+picPath);
            }
            renderer.layout();
            renderer.createPDF(os);
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
	/**
	 * 文件拷贝
	 * 
	 * @param filePath 目标文件路径
	 * @param inFile 源文件
	 * @return
	 */
	public static void copyFile(String filePath, UploadedFile inFile) {
		File outFile = new File(filePath);
		InputStream in = null;
		FileOutputStream out = null;
		try {
			if(!outFile.exists()) {
				FileUtils.createFile(outFile);
			}
			
			in = inFile.getInputstream();
			out = new FileOutputStream(outFile);
			int cha = 0;
			byte[] b = new byte[4096];  
			while ((cha = in.read(b)) != -1) {
				out.write(b,0,cha);// 写
			}
			out.flush();// 将缓存中的数据写入文件
		} catch (Exception e) {
			e.printStackTrace();
		} finally {// 关闭输入输出流
			try {
				in.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			try {
				out.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	
	/** 
     * 文件转化为字节数组 
     *  
     * @param file 文件
     * @return 字节数组
     */  
    public static byte[] convertFile2Bytes(File file) {  
        byte[] ret = null;  
        try {  
            if (file == null) {  
                return null;  
            }  
            FileInputStream in = new FileInputStream(file);  
            ByteArrayOutputStream out = new ByteArrayOutputStream(4096);  
            byte[] b = new byte[4096];  
            int n;  
            while ((n = in.read(b)) != -1) {  
                out.write(b, 0, n);  
            }  
            in.close();  
            out.close();  
            ret = out.toByteArray();  
        } catch (IOException e) {  
            e.printStackTrace();  
        }  
        return ret;  
    } 

}
