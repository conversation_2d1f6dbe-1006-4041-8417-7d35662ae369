package com.chis.modules.system.utils;

import com.chis.common.utils.StringUtils;

import javax.faces.component.UIViewRoot;
import javax.faces.context.FacesContext;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <p>类描述：处理点击菜单一次，生成一个托管Bean，并且托管Bean不销毁，越来越多 </p>
 * pw 2025/7/22
 **/
public class ClearBeanCacheUtil {

    /**
     * <p>方法描述：去除多余的托管Bean，托管Bean构造方法里增加调用exeClear方法
     * 实现方式，菜单加入beanTag标记，同一个托管Bean名称，同一个beanTag仅缓存一个托管Bean
     * 缓存的托管Bean，放在SessionData的Map里
     * 发现同一个托管Bean，同一个beanTag的对象存在，就从session中移除之前的托管Bean，并将新的托管Bean更新到SessionData的Map
     * </p>
     * pw 2025/7/22
     **/
    public static void exeClear(String beanTag, String beanName, Object curBean) {
        HttpSession session = (HttpSession) FacesContext
                .getCurrentInstance().getExternalContext().getSession(false);
        if (StringUtils.isNotBlank(beanTag) && null != session) {
            Map<String,Object> beanMap = Global.getSessionData().getBeanMap().get(beanName);
            if (null == beanMap) {
                beanMap = new HashMap<>();
                Global.getSessionData().getBeanMap().put(beanName, beanMap);
            }
            Object beanObj = beanMap.get(beanTag);
            Map<String, UIViewRoot> tmpMap = (Map<String, UIViewRoot>)session.getAttribute("com.sun.faces.application.view.activeViewMaps");
            if (null != beanObj && null != tmpMap) {
                Set<String> keySet = new HashSet<>(tmpMap.keySet());
                for (String innerKey : keySet) {
                    Map<String,Object> innerMap = (Map<String,Object>)tmpMap.get(innerKey);
                    Object innerObj = innerMap.get(beanName);
                    // 发现缓存的托管Bean，在session中存在，就从session中移除
                    if (null != innerObj && innerObj.equals(beanObj)) {
                        innerMap.remove(beanName);
                    }
                    // 为空就清空
                    if (innerMap.isEmpty()) {
                        tmpMap.remove(innerKey);
                    }
                }
            }
            beanMap.put(beanTag, curBean);
        }
    }
}
