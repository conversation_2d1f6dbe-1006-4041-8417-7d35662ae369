package com.chis.modules.system.utils;

import java.io.File;
import java.util.List;
import java.util.Map;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.logic.LucenePojo;

/**
 * 模块说明：全文检索加载与查询工具类
 * 
 * <AUTHOR>
 * @createDate 2016年11月1日
 */
public class LoadSearchUtils {

	/**
	 * 将查询到的数据封装到Set与Map中
	 * 
	 * @param list
	 * @param fileSet
	 * @param fileMap
	 */
	public static void addDataToMap(List<LucenePojo> list, Map<String, LucenePojo> fileMap) {
		if (null != list && list.size() > 0 && null != fileMap) {
			for (LucenePojo lucenePojo : list) {
				// 文件路径
				String annexPath = lucenePojo.getAnnexPath();
				// 判断文件路径是否存在，且存在 斜杠
				if (StringUtils.isNotBlank(annexPath)) {
					// 判断是否绝对路径
					int indexOf = annexPath.indexOf(":");
					if (indexOf == -1) {
						// 如果是相对路径，则需要加上虚拟路劲
						if(annexPath.startsWith("/")){
							annexPath = annexPath.substring(1);
						}
						annexPath = JsfUtil.getAbsolutePath() + annexPath;
					}
					if (annexPath.lastIndexOf("/") >= 0 && new File(annexPath).exists()) {// 存在 /说名文件 放在文件夹下，且文件存在
						fileMap.put(annexPath, lucenePojo);
					}
				}
			}
		}
	}
}
