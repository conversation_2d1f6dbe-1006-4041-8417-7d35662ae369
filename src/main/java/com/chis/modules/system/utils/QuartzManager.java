package com.chis.modules.system.utils;

import java.text.ParseException;
import java.util.Map;
import java.util.Map.Entry;

import org.quartz.CronScheduleBuilder;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SchedulerFactory;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.quartz.TriggerKey;
import org.quartz.impl.StdSchedulerFactory;

/**
 * 定时任务公用方法
 * 
 * <AUTHOR>
 * @createDate 2015年1月22日下午7:04:38
 */
public class QuartzManager {
	private static SchedulerFactory sf = new StdSchedulerFactory();
	private static String TRIGGER_GROUP_NAME = "trigger1";

	/** */
	/**
	 * 添加一个定时任务，使用默认的任务组名，触发器名，触发器组名
	 * 
	 * @param jobName
	 *            任务名
	 * @param job
	 *            任务
	 * @param time
	 *            时间设置，参考quartz说明文档
	 * @throws SchedulerException
	 * @throws ParseException
	 */
	public static void addJob(String jobName, Job job, String time) throws SchedulerException, ParseException {
		QuartzManager.removeJob(jobName);
		Scheduler sched = sf.getScheduler();
		JobDetail jobDetail = JobBuilder.newJob(job.getClass()).withIdentity(jobName, Scheduler.DEFAULT_GROUP).build();
		// 触发器
		TriggerBuilder<Trigger> builder = TriggerBuilder.newTrigger().withIdentity(jobName, TRIGGER_GROUP_NAME);
		org.quartz.CronExpression cronExpression = new org.quartz.CronExpression(time);

		Trigger trigger = builder.withSchedule(CronScheduleBuilder.cronSchedule(cronExpression)).build();
		sched.scheduleJob(jobDetail, trigger);
		// 启动
		if (!sched.isShutdown())
			sched.start();
	}

	/**
	 * 添加一个定时任务，使用默认的任务组名，触发器名，触发器组名，含有变量
	 * 
	 * @param jobName
	 *            任务名
	 * @param job
	 *            任务
	 * @param time
	 *            时间设置，参考quartz说明文档
	 * @param dataMap
	 *            存放Job变量
	 * @throws SchedulerException
	 * @throws ParseException
	 */
	public static void addJob(String jobName, Job job, String time, Map<String, Object> dataMap)
			throws SchedulerException, ParseException {
		QuartzManager.removeJob(jobName);
		
		Scheduler sched = sf.getScheduler();
		
		JobDetail jobDetail = JobBuilder.newJob(job.getClass()).withIdentity(jobName, Scheduler.DEFAULT_GROUP).build();// 任务名，任务组，任务执行类
		if (null != dataMap && dataMap.size() > 0) {
			for (Entry<String, Object> entry : dataMap.entrySet()) {
				jobDetail.getJobDataMap().put(entry.getKey(), entry.getValue());
			}
		}
		// 触发器
		TriggerBuilder<Trigger> builder = TriggerBuilder.newTrigger().withIdentity(jobName, TRIGGER_GROUP_NAME);
		org.quartz.CronExpression cronExpression = new org.quartz.CronExpression(time);
		Trigger trigger = builder.withSchedule(CronScheduleBuilder.cronSchedule(cronExpression)).build();
		sched.scheduleJob(jobDetail, trigger);
		// 启动
		if (!sched.isShutdown()) {
			sched.start();
		}
	}

	/** */
	/**
	 * 添加一个定时任务
	 * 
	 * @param jobName
	 *            任务名
	 * @param jobGroupName
	 *            任务组名
	 * @param triggerName
	 *            触发器名
	 * @param triggerGroupName
	 *            触发器组名
	 * @param job
	 *            任务
	 * @param time
	 *            时间设置，参考quartz说明文档
	 * @throws SchedulerException
	 * @throws ParseException
	 */
	public static void addJob(String jobName, String jobGroupName, String triggerName, String triggerGroupName,
			Job job, String time) throws SchedulerException, ParseException {
		QuartzManager.removeJob(jobName);
		
		Scheduler sched = sf.getScheduler();
		JobDetail jobDetail = JobBuilder.newJob(job.getClass()).withIdentity(jobName, Scheduler.DEFAULT_GROUP).build();// 任务名，任务组，任务执行类
		// 触发器
		TriggerBuilder<Trigger> builder = TriggerBuilder.newTrigger().withIdentity(jobName, triggerGroupName);
		org.quartz.CronExpression cronExpression = new org.quartz.CronExpression(time);
		Trigger trigger = builder.withSchedule(CronScheduleBuilder.cronSchedule(cronExpression)).build();
		
		sched.scheduleJob(jobDetail, trigger);
		if (!sched.isShutdown())
			sched.start();
	}

	/** */
	/**
	 * 修改一个任务的触发时间(使用默认的任务组名，触发器名，触发器组名)
	 * 
	 * @param jobName
	 * @param time
	 * @throws SchedulerException
	 * @throws ParseException
	 */
	public static void modifyJobTime(String jobName, String time) throws SchedulerException, ParseException {
		Scheduler sched = sf.getScheduler();
		TriggerKey triggerKey = new TriggerKey(jobName, TRIGGER_GROUP_NAME);
		Trigger trigger = sched.getTrigger(triggerKey);
		if (trigger != null) {
			TriggerBuilder<Trigger> builder = TriggerBuilder.newTrigger().withIdentity(triggerKey);
			org.quartz.CronExpression cronExpression = new org.quartz.CronExpression(time);
			trigger = builder.withSchedule(CronScheduleBuilder.cronSchedule(cronExpression)).build();
			sched.rescheduleJob(triggerKey, trigger);
		}
	}


	/** */
	/**
	 * 移除一个任务(使用默认的任务组名，触发器名，触发器组名)
	 * 
	 * @param jobName
	 * @throws SchedulerException
	 */
	public static void removeJob(String jobName) throws SchedulerException {
		Scheduler sched = sf.getScheduler();
		TriggerKey triggerKey = new TriggerKey(jobName, TRIGGER_GROUP_NAME);
		JobKey jobKey = new JobKey(jobName);
		sched.pauseTrigger(triggerKey);// 停止触发器
		sched.unscheduleJob(triggerKey);// 移除触发器
		sched.deleteJob(jobKey);// 删除任务
	}

	/** */
	/**
	 * 移除一个任务
	 * 
	 * @param jobName
	 * @param jobGroupName
	 * @param triggerName
	 * @param triggerGroupName
	 * @throws SchedulerException
	 */
	public static void removeJob(String jobName, String jobGroupName, String triggerName, String triggerGroupName)
			throws SchedulerException {
		Scheduler sched = sf.getScheduler();
		TriggerKey triggerKey = new TriggerKey(jobName, triggerGroupName);
		sched.pauseTrigger(triggerKey);// 停止触发器
		sched.unscheduleJob(triggerKey);// 移除触发器
		JobKey jobKey = new JobKey(jobName, jobGroupName);
		sched.deleteJob(jobKey);// 删除任务
	}
}
