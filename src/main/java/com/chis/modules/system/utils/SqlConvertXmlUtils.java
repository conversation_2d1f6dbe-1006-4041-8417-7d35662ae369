package com.chis.modules.system.utils;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringEscapeUtils;

import com.alibaba.druid.sql.SQLUtils;
import com.chis.common.utils.JaxbMapper;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.logic.SQLSentence;
import com.chis.modules.system.logic.SQLSentences;

/**
 * 模块说明：web类型的项目将升级语句java方式更换成xml方式
 * 
 * <AUTHOR>
 * @createDate 2019年12月16日
 */
public class SqlConvertXmlUtils {

	public static void main(String[] args) {

//		String convertJava2Xml = convertJava2Xml(SystemType.FLOW, DataStructurePluginObj.sqlList);
//		System.out.println(convertJava2Xml);

	}

	/**
	 * 
	 * <p>
	 * 方法描述：升级语句将java的格式转换成xml
	 * </p>
	 * 
	 * @MethodAuthor xt,2019年12月16日,convertJava2Xml
	 * @param systemType
	 * @param sqlList
	 * @return
	 */
	public static String convertJava2Xml(SystemType systemType, List<String> sqlList) {

		if (null == systemType || null == sqlList || sqlList.size() == 0) {
			System.out.println("系统类型为空或sql语句集合为空！");
			return null;
		}

		SQLSentences sqlSentences = new SQLSentences();
		sqlSentences.setDescription(systemType.getTypeCN());

		List<SQLSentence> sqlObjList = new ArrayList<SQLSentence>();
		sqlSentences.setSqlList(sqlObjList);

		for (int i = 0; i < sqlList.size(); i++) {
			String sql = sqlList.get(i);

			SQLSentence sqlSentence = new SQLSentence();
			sqlSentence.setVer(i + 1);

			StringBuilder formatSql = new StringBuilder();
			formatSql.append(System.getProperty("line.separator"));
			formatSql.append("          " + "<![CDATA[");
			formatSql.append(System.getProperty("line.separator"));
			formatSql.append("            " + SQLUtils.formatOracle(sql).replaceAll("\n", "\n" + "            ").replaceAll("\t", "  "));
			formatSql.append(System.getProperty("line.separator"));
			formatSql.append("          " + "]]>");
			formatSql.append(System.getProperty("line.separator"));

			sqlSentence.setSql(formatSql.toString());
			sqlObjList.add(sqlSentence);
		}

		String xml = StringEscapeUtils.unescapeXml(JaxbMapper.toXml(sqlSentences)).replaceAll("</sql>", "        </sql>");
		return xml;
	}

}
