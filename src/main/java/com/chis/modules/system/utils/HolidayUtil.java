package com.chis.modules.system.utils;

import cn.hutool.core.date.DateUtil;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.service.CommServiceImpl;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class HolidayUtil {

	private static CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

	/**
	 * 计算剩余工作日天数
	 * startDate ,传入起始日期。
	 * endDate，当前日期
	 * limitTime：配置的限制工作日天数
	 */
	public static int calRemainingDate(Date startDate, Date endDate,String limitTime) {
		//接受日期是否是节假日
		boolean acceptHoliday =false;
		String s = DateUtils.formatDate(startDate);
		Date sdate = DateUtils.parseDate(s);
		String end = DateUtils.formatDate(endDate);
		Date edate = DateUtils.parseDate(end);

		if(sdate.compareTo(edate)>=0){
			return Integer.valueOf(limitTime);
		}
		//当前日期与开始日期相差的工作日天数
		int leaveDays = 0;
		//从startTime开始循环，若该日期不是节假日或者不是周六日则请假天数+1
		//设置循环开始日期
		Calendar cal = Calendar.getInstance();
		cal.setTime(sdate);
		cal.add(Calendar.DAY_OF_MONTH, +1);
		Date flag = cal.getTime();
		//循环遍历每个日期
		while (flag.compareTo(edate) <= 0) {
//			if(sdate.compareTo(flag)==0){
//				continue;
//			}
			cal.setTime(flag);
			//判断是否为周六日
			int week = cal.get(Calendar.DAY_OF_WEEK) - 1;
			//0为周日，6为周六
			if (week == 0 || week == 6) {
				//查询这一天周末是否需要补班
				StringBuffer sql2 = new StringBuffer();
				sql2.append(" SELECT COUNT(T.RID) FROM TS_SYS_HOLIDAY T WHERE T.STATE = 1 AND T.HOLI_TYPE = 2 ");
				sql2.append(" and T.START_DATE <= TO_DATE('").append(DateUtils.formatDate(flag)).append(" ','YYYY-MM-DD ')");
				sql2.append(" and T.END_DATE >= TO_DATE('").append(DateUtils.formatDate(flag)).append(" ','YYYY-MM-DD ')");
				/*count为从数据库查出的行数*/
				int count = commService.findCountBySql(sql2.toString());
				//若是无需补班跳出循环进入下一个日期
				if(count==0){
//					if(sdate.compareTo(flag)==0){
//						acceptHoliday = true;
//					}
					cal.add(Calendar.DAY_OF_MONTH, +1);
					flag = cal.getTime();
					continue;
				}
			}
			//判断是否为节假日
			try {
				//从数据库查找该日期是否在节假日中
				/*这里为数据库操作*/
				/*传入该日期flag,使用sql语句判断flag是否between节假日开始日期and节假日结束日期*/
				StringBuffer sql = new StringBuffer();
				sql.append(" SELECT COUNT(T.RID) FROM TS_SYS_HOLIDAY T WHERE T.STATE = 1 AND T.HOLI_TYPE = 1 ");
				sql.append(" and T.START_DATE <= TO_DATE('").append(DateUtils.formatDate(flag)).append(" ','YYYY-MM-DD ')");
				sql.append(" and T.END_DATE >= TO_DATE('").append(DateUtils.formatDate(flag)).append(" ','YYYY-MM-DD ')");
				/*count为从数据库查出的行数*/
				int count = commService.findCountBySql(sql.toString());
				if (count > 0) {
					//跳出循环进入下一个日期
					cal.add(Calendar.DAY_OF_MONTH, +1);
					flag = cal.getTime();
					continue;
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			//不是节假日或者周末，天数+1
			leaveDays = leaveDays + 1;
			//日期往后加一天
			cal.add(Calendar.DAY_OF_MONTH, +1);
			flag = cal.getTime();
		}
		int a =Integer.valueOf(limitTime).intValue()+1- leaveDays;
//		if(acceptHoliday){
//			 a =Integer.valueOf(limitTime).intValue()+1- leaveDays;
//		}
		if(a>=Integer.valueOf(limitTime).intValue()){
			return Integer.valueOf(limitTime).intValue();
		}
		return a;
	}


	/**
	* <p>Description：计算超期日期 </p>
	* <p>Author： yzz 2023-10-12 </p>
	*/
	public static Date calOverDate(Date endDate, String limitTime){
		String end = DateUtils.formatDate(endDate);
		Date edate = DateUtils.parseDate(end);
		if(!edate.before(new Date())){
			return new Date();
		}
		//当前日期与超期日期相差的工作日天数
		int leaveDays = 0;
		Calendar cal = Calendar.getInstance();
		cal.setTime(edate);
		Date flag = cal.getTime();
		while(true){
			cal.setTime(flag);
			//判断是否为周六日
			int week = cal.get(Calendar.DAY_OF_WEEK) - 1;
			//0为周日，6为周六
			if (week == 0 || week == 6) {
				//查询这一天周末是否需要补班
				StringBuffer sql2 = new StringBuffer();
				sql2.append(" SELECT COUNT(T.RID) FROM TS_SYS_HOLIDAY T WHERE T.STATE = 1 AND T.HOLI_TYPE = 2 ");
				sql2.append(" and T.START_DATE <= TO_DATE('").append(DateUtils.formatDate(flag)).append(" ','YYYY-MM-DD ')");
				sql2.append(" and T.END_DATE >= TO_DATE('").append(DateUtils.formatDate(flag)).append(" ','YYYY-MM-DD ')");
				/*count为从数据库查出的行数*/
				int count = commService.findCountBySql(sql2.toString());
				//若是无需补班跳出循环进入下一个日期
				if(count==0){
					cal.add(Calendar.DAY_OF_MONTH, -1);
					flag = cal.getTime();
					continue;
				}else{
					if(leaveDays>=Integer.parseInt(limitTime)){
						return cal.getTime();
					}
				}
			}
			//判断是否为节假日
			try {
				//从数据库查找该日期是否在节假日中
				/*这里为数据库操作*/
				/*传入该日期flag,使用sql语句判断flag是否between节假日开始日期and节假日结束日期*/
				StringBuffer sql = new StringBuffer();
				sql.append(" SELECT COUNT(T.RID) FROM TS_SYS_HOLIDAY T WHERE T.STATE = 1 AND T.HOLI_TYPE = 1 ");
				sql.append(" and T.START_DATE <= TO_DATE('").append(DateUtils.formatDate(flag)).append(" ','YYYY-MM-DD ')");
				sql.append(" and T.END_DATE >= TO_DATE('").append(DateUtils.formatDate(flag)).append(" ','YYYY-MM-DD ')");
				/*count为从数据库查出的行数*/
				int count = commService.findCountBySql(sql.toString());
				if (count > 0) {
					//跳出循环进入下一个日期
					cal.add(Calendar.DAY_OF_MONTH, -1);
					flag = cal.getTime();
					continue;
				}else{
					if(leaveDays>=Integer.parseInt(limitTime)){
						return cal.getTime();
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			leaveDays += 1;
			cal.add(Calendar.DAY_OF_MONTH, -1);
			flag = cal.getTime();
		}
	}

	/**
	 * 计算前N个工作日的日期以及到指定日期距离日期N个工作日之后剩余的工作日天数
	 *
	 * @param givenDate 指定日期
	 * @param maxDays   最大工作日天数
	 * @return map <pre>key: 日期</pre><pre>value: 今天距离日期N个工作日之后剩余的工作日天数</pre>
	 */
	public static Map<Date, Integer> calDaysBeforeTheWorkingDay(Date givenDate, int maxDays) {
		Map<Date, Integer> weekdaysmap = new HashMap<>();
		// 小于等于0 仅当天
		Date date = DateUtil.date(DateUtils.getDateOnly(givenDate));
		int days = maxDays;
		if (days <= 0) {
			weekdaysmap.put(date, 0);
			return weekdaysmap;
		}
		boolean isAWorkingDayFirst = verifyDateIsAWorkingDay(date);
		while (days >= 0) {
			// 指定日期是节假日则不存在当天截止（0）的情况，直接退出循环
			if (days == 0 && !isAWorkingDayFirst) {
				break;
			}
			weekdaysmap.put(date, Math.min(isAWorkingDayFirst ? days + 1 : days, maxDays));
			// 如果当前日期是工作日则剩余工作日天数减一
			boolean isAWorkingDay = verifyDateIsAWorkingDay(date);
			if (isAWorkingDay) {
				days--;
			}
			// 前一天
			date = DateUtil.offsetDay(date, -1);
		}
		return weekdaysmap;
	}

	/**
	 * 验证日期是否工作日
	 *
	 * @param date 日期
	 * @return boolean
	 */
	public static boolean verifyDateIsAWorkingDay(Date date) {
		Map<String, Object> paramMap = new HashMap<>();
		String sql = "SELECT COUNT(T.RID) FROM TS_SYS_HOLIDAY T " +
				"WHERE T.STATE = 1 " +
				"   AND T.HOLI_TYPE = :holiType " +
				"   AND T.START_DATE <= TO_DATE(:date,'YYYY-MM-DD') " +
				"   AND T.END_DATE >= TO_DATE(:date,'YYYY-MM-DD') ";
		paramMap.put("date", DateUtils.formatDate(date));
		// 判断日期是否为周六或周日
		boolean weekend = DateUtil.isWeekend(date);
		if (weekend) {
			// 判断是否是周末补班
			paramMap.put("holiType", 2);
			int count = commService.findCountBySql(sql, paramMap);
			return count > 0;
		}
		// 判断是否是节假日
		paramMap.put("holiType", 1);
		int count = commService.findCountBySql(sql, paramMap);
		return count == 0;
	}

}
