package com.chis.modules.system.utils;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;

import javax.persistence.EntityManager;
import javax.sql.DataSource;

import org.springframework.transaction.annotation.Transactional;

import com.chis.common.utils.SpringContextHolder;

/**
 * Jdbc的一些常用方法
 * 
 * <AUTHOR> 2014-11-5
 */
@Transactional(readOnly = false)
public class JdbcUtil {

	/**
	 * 批量执行SQL语句
	 * 
	 * @param dataSource
	 *            数据源
	 * @param sqlList
	 *            SQL语句集合
	 */
	public static void insertSql(List<String> sqlList) {
		DataSource dataSource = SpringContextHolder.getBean("dataSource");
		Connection conn = null;
		Statement stmt = null;
		try {
			conn = dataSource.getConnection();
			stmt = conn.createStatement();
			for (String sql : sqlList) {
				stmt.addBatch(sql);
			}
			stmt.executeBatch();
			stmt.close();
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		} finally {
			if (null != stmt) {
				try {
					stmt.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * 批量执行SQL语句
	 * 
	 * @param dataSource
	 *            数据源
	 * @param sqlList
	 *            SQL语句集合
	 */
	public static void insertSql(List<String> sqlList, EntityManager em) {
		try {
			if (null != sqlList && sqlList.size() > 0 && null != em) {
				for (String sql : sqlList) {
					em.createNativeQuery(sql).executeUpdate();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}

}
