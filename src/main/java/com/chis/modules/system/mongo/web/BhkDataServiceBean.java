package com.chis.modules.system.mongo.web;


import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsUnitAttr;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.mongo.entity.bhkdataservcie.TdZwlogUpload;
import com.chis.modules.system.mongo.entity.bhkdataservcie.TdZwlogUpload2;
import com.chis.modules.system.mongo.entity.bhkdataservcie.TdZwlogUploadDetail;
import com.chis.modules.system.mongo.entity.bhkdataservcie.TdZwlogUploadText;
import com.chis.modules.system.mongo.pojo.BhkResponseData;
import com.chis.modules.system.mongo.pojo.ErrorMainData;
import com.chis.modules.system.mongo.service.MongoService;
import com.chis.modules.system.mongo.utils.MongoFacesEditBean;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.XmlFormatter;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.primefaces.context.RequestContext;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.UnwindOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.util.*;


/***
 * <p>类描述: 体检日志查询模块 </p>
 *
 * @ClassAuthor mxp,2018/12/14,BhkDataServiceBean
 */
@ManagedBean
@ViewScoped
public class BhkDataServiceBean extends MongoFacesEditBean<TdZwlogUpload2,TdZwlogUpload> implements IProcessData {
    private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    private MongoService mongoService = SpringContextHolder.getBean(MongoService.class);
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private String tag;//1 企业 2 人员
    /**是否是超管*/
    private boolean ifAdmin = Boolean.TRUE;
    //查询实体
    private TdZwlogUpload2 searchEntity;
    //上传标记 0 失败 1 成功
    private List<String> uploadTags;
    //处理标记 0 未处理 1 已处理
    private List<String> dealStates;
    /**查询条件：地区名称*/
    private String searchZoneName;
    /**查询条件：地区编码*/
    private String searchZoneCode;
    /**查询条件：地区级别*/
    private String searchZoneType;
    /**能看到的地区集合*/
    private List<TsZone> zoneList;
    /**添加页面：单位下啦*/
    private Map<String, String> searchUnitMap = new HashMap<String, String>(0);
    private Map<String, String> uploadUnitMap = new HashMap<String, String>(0);
    private Map<String, String> uploadZoneMap = new HashMap<String, String>(0);
    /**查询条件：地区名称*/
    private String searchZoneName2;
    /**查询条件：地区编码*/
    private String searchZoneCode2;
    /**查询条件：地区级别*/
    private String searchZoneType2;
    /**能看到的地区集合*/
    private List<TsZone> zoneList2;

    private TdZwlogUpload tdZwlogUpload;
    private String showContent;
    private String showErrorContent;
    private String dealState;//处理状态
    //1 主 请求数据 2 主
    private String viewType;
    private TdZwlogUploadText mainText;
    private TdZwlogUploadText subText;
    // 导出文件名称
    private String exportFileName;

    /***
     * <p>方法描述: 初始化</p>
     *
     * @MethodAuthor mxp,2018/12/17,init
     */
    @PostConstruct
    public void init(){
        tag = JsfUtil.getRequestParameter("tag");
        TsUserInfo tsUserInfo = this.sessionData.getUser();
        if(!tsUserInfo.getUserNo().equals(Constants.ADMIN)) {
            ifAdmin = Boolean.FALSE;
        }
        this.searchZoneCode = tsUserInfo.getTsUnit().getTsZone().getZoneCode();
        this.searchZoneName = tsUserInfo.getTsUnit().getTsZone().getZoneName();
        this.searchZoneType = tsUserInfo.getTsUnit().getTsZone().getZoneType().toString();

        //初始化查询条件
        searchEntity = new TdZwlogUpload2();
        searchEntity.setSubs(new TdZwlogUploadDetail());
        searchEntity.setZone_gb(this.searchZoneCode);
        if("1".equals(tag)){
            try {
                Date date = DateUtils.parseDate(DateUtils.getYear() + "-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
                searchEntity.setVisit_time_start(date);
                String formatDate = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
                searchEntity.setVisit_time_end(DateUtils.parseDate(formatDate+" 23:59:59"));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }else{
            try {
                Date date = DateUtils.parseDate(DateUtils.getYear() + "-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
                searchEntity.getSubs().setBhk_date_start(date);
                String formatDate = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
                searchEntity.getSubs().setBhk_date_end(DateUtils.parseDate(formatDate+" 23:59:59"));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }





        //地区初始化
        if(null == this.zoneList || this.zoneList.size() <=0) {
            this.zoneList = this.commService.findZoneListNew(this.ifAdmin, searchZoneCode,null,"6");
            String fullName;
            for (TsZone tsZone : zoneList) {
                fullName = StringUtils.trimToEmpty(tsZone.getFullName());
                fullName = fullName.replaceFirst(".*?_","");
                uploadZoneMap.put(tsZone.getZoneGb(),fullName);
            }
        }
        /**
         * 初始化单位
         */
        this.searchUnitMap = this.filterUnit(this.searchZoneCode, this.searchZoneType);


        List<Object[]> list = this.systemModuleService.findSrvorgByZoneCode(true, true,null,null);
        if(list!=null&&list.size()>0){
            for (Object[] strings : list) {
                if(strings[0]!=null&&strings[1]!=null){
                    uploadUnitMap.put(strings[0].toString(),strings[1].toString());
                }
            }
        }

        searchAction();
    }

    /***
     * <p>方法描述: 查询</p>
     *
     * @MethodAuthor mxp,2018/12/17,searchAction
     */
    public void searchAction(){
        if("1".equals(tag)&&searchEntity.getVisit_time_start()!=null&&searchEntity.getVisit_time_end()!=null&&
                searchEntity.getVisit_time_start().after(searchEntity.getVisit_time_end())){
            JsfUtil.addErrorMessage("上传日期开始日期应小于等于结束日期！");
            return;
        }
        if(searchEntity.getVisit_time_end()!=null){
            String formatDate = DateUtils.formatDate(searchEntity.getVisit_time_end(), "yyyy-MM-dd");
            searchEntity.setVisit_time_end(DateUtils.parseDate(formatDate+" 23:59:59"));
        }
        if("2".equals(tag)&&searchEntity.getSubs().getBhk_date_start()!=null&&searchEntity.getSubs().getBhk_date_end()!=null&&
                searchEntity.getSubs().getBhk_date_start().after(searchEntity.getSubs().getBhk_date_end())){
            JsfUtil.addErrorMessage("体检日期开始日期应小于等于体检结束日期！");
            return;
        }
        if(searchEntity.getSubs().getBhk_date_end()!=null){
            String formatDate = DateUtils.formatDate(searchEntity.getSubs().getBhk_date_end(), "yyyy-MM-dd");
            searchEntity.getSubs().setBhk_date_end(DateUtils.parseDate(formatDate+" 23:59:59"));
        }
        super.searchActionWithAgg();

    }

    /***
     * <p>方法描述: 更新状态</p>
     *
     * @MethodAuthor mxp,2018/12/19,updateStateAction
     */
    public void updateStateAction(){
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("rid").is(searchEntity.getRid()).and("subs").elemMatch(Criteria.where("rid").is(searchEntity.getSubs().getRid())));
            Update update = new Update();
            update.set("subs.$.deal_state","1");
            mongoService.updateFirst(query, update, TdZwlogUpload.class);

            if("1".equals(dealState)){
                List<TdZwlogUploadDetail> subs = tdZwlogUpload.getSubs();
                if(subs!=null&&subs.size()>0){
                    for (TdZwlogUploadDetail sub : subs) {
                        if(sub.getRid().equals(searchEntity.getSubs().getRid())){
                            sub.setDeal_state("1");
                            break;
                        }
                    }
                }
            }
            JsfUtil.addSuccessMessage("操作成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("操作失败！");
            e.printStackTrace();
        }
    }

    /**
     * 根据地区刷单位
     * @param zoneCode 地区编码
     * @param zoneType 地区级别
     * @return 单位集合
     */
    private Map<String, String> filterUnit(String zoneCode, String zoneType) {
        TsUserInfo user = this.sessionData.getUser();
        Map<String, String> map = new LinkedHashMap<String, String>();
        Set<TsUnitAttr> tsUnitAttrs = user.getTsUnit().getTsUnitAttrs();
        boolean flag = false;
        if(tsUnitAttrs != null){
            for(TsUnitAttr tsUnitAttr : tsUnitAttrs){
                if(tsUnitAttr.getTsBsSort() != null && tsUnitAttr.getTsBsSort().getSortCode().equals("2001")){
                    flag = true;
                }
            }
        }

        List<Object[]> list = this.systemModuleService.findSrvorgByZoneCode(this.ifAdmin, flag,user.getTsUnit().getRid(), zoneCode);
        if(list!=null&&list.size()>0){
            for (Object[] strings : list) {
                if(strings[0]!=null&&strings[1]!=null){
                    map.put(strings[1].toString(),strings[0].toString());
                    if(!flag && !this.ifAdmin){
                        this.searchEntity.setUnit_code(strings[0].toString());
                    }
                }

            }
        }
        return map;
    }

    /***
     * <p>方法描述: 列表查询管道语句</p>
     *
     * @MethodAuthor mxp,2018/12/19,buildOperations
     */
    @Override
    protected List<AggregationOperation> buildOperations() {
        List<AggregationOperation> operations= new ArrayList<>();
        UnwindOperation unwind = Aggregation.unwind("subs");
        operations.add(unwind);
        return operations;
    }

    /***
     * <p>方法描述: 列表查询语句</p>
     *
     * @MethodAuthor mxp,2018/12/19,buildCriteria
     */
    @Override
    protected Criteria buildCriteria() {
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("inter_type").is(tag));
        //上传地区
        String zone_gb = searchEntity.getZone_gb();
        if(StringUtils.isNotBlank(zone_gb)){
            criteriaList.add(Criteria.where("zone_gb").regex("^"+ZoneUtil.zoneSelect(zone_gb)+".*"));
        }

        //上传机构
        String unit_code = searchEntity.getUnit_code();
        if(StringUtils.isNotBlank(unit_code)){
            criteriaList.add(Criteria.where("unit_code").is(unit_code));
        }

        //上传日期
        Date visit_time_start = searchEntity.getVisit_time_start();
        if(visit_time_start!=null){
            criteriaList.add(Criteria.where("visit_time").gte(visit_time_start));
        }
        Date visit_time_end = searchEntity.getVisit_time_end();
        if(visit_time_end!=null){
            criteriaList.add(Criteria.where("visit_time").lte(visit_time_end));
        }
        //体检日期
        Date bhk_date_start = searchEntity.getSubs().getBhk_date_start();
        if(bhk_date_start!=null){
            criteriaList.add(Criteria.where("subs.bhk_date").gte(bhk_date_start));
        }
        Date bhk_date_end = searchEntity.getSubs().getBhk_date_end();
        if(bhk_date_end!=null){
            criteriaList.add(Criteria.where("subs.bhk_date").lte(bhk_date_end));
        }

        //企业名称
        String crpt_name = searchEntity.getSubs().getBhk_crpt_name();
        if(StringUtils.isNotBlank(crpt_name)){
            criteriaList.add(Criteria.where("subs.bhk_crpt_name").regex(crpt_name,"i"));
        }

        String crpt_name2 = searchEntity.getSubs().getCrpt_name();
        if(StringUtils.isNotBlank(crpt_name2)){
            criteriaList.add(Criteria.where("subs.crpt_name").regex(crpt_name2,"i"));
        }

        //姓名
        String bhk_psn_name = searchEntity.getSubs().getBhk_psn_name();
        if(StringUtils.isNotBlank(bhk_psn_name)){
            criteriaList.add(Criteria.where("subs.bhk_psn_name").regex(bhk_psn_name,"i"));
        }
        //身份证号
        String bhk_idc = searchEntity.getSubs().getBhk_idc();
        if(StringUtils.isNotBlank(bhk_idc)){
            criteriaList.add(Criteria.where("subs.bhk_idc").regex(bhk_idc,"i"));
        }



        //社会信用代码
        String credit_code = searchEntity.getSubs().getCredit_code();
        if(StringUtils.isNotBlank(credit_code)){
            criteriaList.add(Criteria.where("subs.credit_code").regex(credit_code,"i"));
        }
        //上传标记
        if(uploadTags!=null&&uploadTags.size()==1){
            criteriaList.add(Criteria.where("subs.upload_tag").is(uploadTags.get(0)));
        }
        //错误原因


        //处理状态
        if(dealStates!=null&&dealStates.size()==1){
            criteriaList.add(Criteria.where("subs.deal_state").is(dealStates.get(0)));
        }
        Criteria criteria = new Criteria().andOperator((Criteria[]) criteriaList.toArray(new Criteria[criteriaList.size()]));
        return criteria;
    }

    @Override
    public void addInit() {
        
    }

    @Override
    public void viewInit() {

    }

    /***
     * <p>方法描述: 修改操作</p>
     *
     * @MethodAuthor mxp,2018/12/19,modInit
     */
    @Override
    public void modInit() {
       /* List<AggregationOperation> operations = new ArrayList<>();
        MatchOperation match = Aggregation.match(Criteria.where("rid").is(searchEntity.getRid()).and("subs.rid").is(searchEntity.getSubs().getRid()));
        UnwindOperation unwind = Aggregation.unwind("subs");
        operations.add(unwind);
        operations.add(match);
        List<TdZwlogUpload2> tdZwlogUpload2s = mongoService.pageList(operations, TdZwlogUpload2.class, TdZwlogUpload2.class);*/
        tdZwlogUpload = mongoService.findOne(searchEntity.getRid(), TdZwlogUpload.class);
//        Query query = query(Criteria.where("rid").is(searchEntity.getRid()));
//        List<TdZwlogUpload> tdZwlogUploads = mongoService.find(query, TdZwlogUpload.class);
//        if(tdZwlogUploads!=null&&tdZwlogUploads.size()>0){
//            tdZwlogUpload = tdZwlogUploads.get(0);
        if(tdZwlogUpload==null){
            tdZwlogUpload = new TdZwlogUpload();
        }
            if("2".equals(tag)){
                List<TdZwlogUploadDetail> subs = tdZwlogUpload.getSubs();
                if(subs!=null){
                    for (TdZwlogUploadDetail sub : subs) {
                        sub.setBhk_idc(StringUtils.encryptIdc(sub.getBhk_idc()));
                    }
                }
            }

//        }
        //
        mainText = mongoService.findOne(searchEntity.getRid(), TdZwlogUploadText.class);
        if(mainText==null){
            mainText = new TdZwlogUploadText();
        }
        subText = mongoService.findOne(searchEntity.getSubs().getRid(), TdZwlogUploadText.class);
        if(subText==null){
            subText = new TdZwlogUploadText();
        }else if (StringUtils.isNotBlank(subText.getResp_text())){
            try {
                BhkResponseData bhkResponseData = JaxbMapper.fromXml(subText.getResp_text(), BhkResponseData.class);
                if(bhkResponseData!=null&&bhkResponseData.getErrors()!=null){
                    ErrorMainData errorMainData = bhkResponseData.getErrors().get(0);
                    if(errorMainData!=null){
                        showErrorContent = StringUtils.trimToEmpty(errorMainData.getDesc())+StringUtils.trimToEmpty(errorMainData.getErrorDatas());
                    }
                }
            } catch (Exception e) {
                ErrorMainData errorMainData = JaxbMapper.fromXml(subText.getResp_text(), ErrorMainData.class);
                if(errorMainData!=null){
                    showErrorContent = StringUtils.trimToEmpty(errorMainData.getDesc())+StringUtils.trimToEmpty(errorMainData.getErrorDatas());
                }
            }
        }


    }

    /***
     * <p>方法描述: 显示信息</p>
     *
     * @MethodAuthor mxp,2018/12/19,showContentAction
     */
    public void showContentAction(){
        StringBuilder sb = new StringBuilder();
        if(StringUtils.isNotBlank(showContent)){
            String[] split = showContent.split("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>");
            for (int i = 0; i < split.length; i++) {
               sb.append(XmlFormatter.format(split[i]));
            }
        }
        showContent = sb.toString();
        RequestContext.getCurrentInstance().execute("PF('RespTextDialog').show()");
    }

    public void postProcessXLS(Object document) {
        HSSFWorkbook wb = (HSSFWorkbook) document;
        HSSFSheet sheet = wb.getSheetAt(0);
        HSSFRow header = sheet.getRow(0);
        HSSFCell cell = header.getCell(0);

        HSSFFont font = wb.createFont();
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        HSSFCellStyle cellStyle = wb.createCellStyle();
        // 文字居中
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        cellStyle.setFont(font);
        cell.setCellStyle(cellStyle);
        cell.setCellValue("企业日志");

        CellRangeAddress region = new CellRangeAddress(0, 0, 0, 7);
        sheet.addMergedRegion(region);
        // 遍历表格
        for (int i = 2; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            row.getCell(8).setCellValue("");
            /*
             * for (int j = 0; j <= row.getLastCellNum(); j++) { if (null !=
             * row.getCell(j)) { Cell cell = row.getCell(j);
             * cell.setCellStyle(cellStyle); } }
             */
        }
    }

    public void postProcessXLS2(Object document) {
        HSSFWorkbook wb = (HSSFWorkbook) document;
        HSSFSheet sheet = wb.getSheetAt(0);
        HSSFRow header = sheet.getRow(0);
        HSSFCell cell = header.getCell(0);

        HSSFFont font = wb.createFont();
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        HSSFCellStyle cellStyle = wb.createCellStyle();
        // 文字居中
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        cellStyle.setFont(font);
        cell.setCellStyle(cellStyle);
        cell.setCellValue("人员日志");


        CellRangeAddress region = new CellRangeAddress(0, 0, 0, 11);
        sheet.addMergedRegion(region);
        // 遍历表格
        for (int i = 2; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            row.getCell(12).setCellValue("");
            /*
             * for (int j = 0; j <= row.getLastCellNum(); j++) { if (null !=
             * row.getCell(j)) { Cell cell = row.getCell(j);
             * cell.setCellStyle(cellStyle); } }
             */
        }
    }

    public void preProcessXLS(Object document) {
        HSSFWorkbook wb = (HSSFWorkbook) document;
        HSSFSheet sheet = wb.getSheetAt(0);
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(CellStyle.ALIGN_CENTER);// 左右居中
        cellStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        // 表列名称
        HSSFRow row2 = sheet.createRow(1);
        HSSFCell cell0 = row2.createCell(0);
        cell0.setCellValue("地区");
        cell0.setCellStyle(cellStyle);
        sheet.setColumnWidth(0, 3000);
        HSSFCell cell1 = row2.createCell(1);
        cell1.setCellValue("上传机构名称");
        cell1.setCellStyle(cellStyle);
        sheet.setColumnWidth(1, 4500);
        HSSFCell cell2 = row2.createCell(2);
        cell2.setCellValue("企业名称");
        cell2.setCellStyle(cellStyle);
        sheet.setColumnWidth(2, 5500);
        HSSFCell cell3 = row2.createCell(3);
        cell3.setCellValue("社会信用代码");
        cell3.setCellStyle(cellStyle);
        sheet.setColumnWidth(3, 4000);
        HSSFCell cell4 = row2.createCell(4);
        cell4.setCellValue("上传时间");
        cell4.setCellStyle(cellStyle);
        sheet.setColumnWidth(4, 5000);
        HSSFCell cell5 = row2.createCell(5);
        cell5.setCellValue("上传标记");
        cell5.setCellStyle(cellStyle);
        sheet.setColumnWidth(5, 3000);
        HSSFCell cell6 = row2.createCell(6);
        cell6.setCellValue("状态");
        cell6.setCellStyle(cellStyle);
        sheet.setColumnWidth(6, 4000);
        HSSFCell cell7 = row2.createCell(7);
        cell7.setCellValue("失败原因");
        cell7.setCellStyle(cellStyle);
        sheet.setColumnWidth(7, 4000);
        HSSFCell cell8 = row2.createCell(8);
    }

    public void preProcessXLS2(Object document) {
        HSSFWorkbook wb = (HSSFWorkbook) document;
        HSSFSheet sheet = wb.getSheetAt(0);
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(CellStyle.ALIGN_CENTER);// 左右居中
        cellStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        // 表列名称
        HSSFRow row2 = sheet.createRow(1);
        HSSFCell cell0 = row2.createCell(0);
        cell0.setCellValue("地区");
        cell0.setCellStyle(cellStyle);
        sheet.setColumnWidth(0, 3000);
        HSSFCell cell1 = row2.createCell(1);
        cell1.setCellValue("上传机构");
        cell1.setCellStyle(cellStyle);
        sheet.setColumnWidth(1, 4500);
        HSSFCell cell2 = row2.createCell(2);
        cell2.setCellValue("人员姓名");
        cell2.setCellStyle(cellStyle);
        sheet.setColumnWidth(2, 5500);
        HSSFCell cell3 = row2.createCell(3);
        cell3.setCellValue("身份证号");
        cell3.setCellStyle(cellStyle);
        sheet.setColumnWidth(3, 4000);
        HSSFCell cell4 = row2.createCell(4);
        cell4.setCellValue("体检编号");
        cell4.setCellStyle(cellStyle);
        sheet.setColumnWidth(4, 5000);
        HSSFCell cell5 = row2.createCell(5);
        cell5.setCellValue("企业名称");
        cell5.setCellStyle(cellStyle);
        sheet.setColumnWidth(5, 3000);
        HSSFCell cell6 = row2.createCell(6);
        cell6.setCellValue("社会信用代码");
        cell6.setCellStyle(cellStyle);
        sheet.setColumnWidth(6, 5500);
        HSSFCell cell7 = row2.createCell(7);
        cell7.setCellValue("体检日期");
        cell7.setCellStyle(cellStyle);
        sheet.setColumnWidth(7, 4000);
        HSSFCell cell8 = row2.createCell(8);
        cell8.setCellValue("上传时间");
        cell8.setCellStyle(cellStyle);
        sheet.setColumnWidth(8, 4000);
        HSSFCell cell9= row2.createCell(9);
        cell9.setCellValue("上传标记");
        cell9.setCellStyle(cellStyle);
        sheet.setColumnWidth(9, 4000);
        HSSFCell cell10 = row2.createCell(10);
        cell10.setCellValue("状态");
        cell10.setCellStyle(cellStyle);
        sheet.setColumnWidth(10, 4000);
        HSSFCell cell11 = row2.createCell(11);
        cell11.setCellValue("失败原因");
        cell11.setCellStyle(cellStyle);
        sheet.setColumnWidth(11, 4000);
        HSSFCell cell12 = row2.createCell(12);
    }

    @Override
    public void saveAction() {

    }


    /**
     * 查询条件，地区树选择事件
     */
    public void onSearchNodeSelect() {
        this.searchEntity.setUnit_code(null);
        this.searchUnitMap = this.filterUnit(this.searchEntity.getZone_gb(), this.searchZoneType);
    }

    @Override
    public Query buildQuery() {
        return null;
    }

    @Override
    public Class<TdZwlogUpload2> newOutputClazz() {
        return TdZwlogUpload2.class;
    }

    @Override
    public Class newInputClazz() {
        return TdZwlogUpload.class;
    }


    public TdZwlogUpload2 getSearchEntity() {
        return searchEntity;
    }

    public void setSearchEntity(TdZwlogUpload2 searchEntity) {
        this.searchEntity = searchEntity;
    }

    public List<String> getUploadTags() {
        return uploadTags;
    }

    public void setUploadTags(List<String> uploadTags) {
        this.uploadTags = uploadTags;
    }

    public List<String> getDealStates() {
        return dealStates;
    }

    public void setDealStates(List<String> dealStates) {
        this.dealStates = dealStates;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneType() {
        return searchZoneType;
    }

    public void setSearchZoneType(String searchZoneType) {
        this.searchZoneType = searchZoneType;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneName2() {
        return searchZoneName2;
    }

    public void setSearchZoneName2(String searchZoneName2) {
        this.searchZoneName2 = searchZoneName2;
    }

    public String getSearchZoneCode2() {
        return searchZoneCode2;
    }

    public void setSearchZoneCode2(String searchZoneCode2) {
        this.searchZoneCode2 = searchZoneCode2;
    }

    public String getSearchZoneType2() {
        return searchZoneType2;
    }

    public void setSearchZoneType2(String searchZoneType2) {
        this.searchZoneType2 = searchZoneType2;
    }

    public List<TsZone> getZoneList2() {
        return zoneList2;
    }

    public void setZoneList2(List<TsZone> zoneList2) {
        this.zoneList2 = zoneList2;
    }

    public Map<String, String> getSearchUnitMap() {
        return searchUnitMap;
    }

    public void setSearchUnitMap(Map<String, String> searchUnitMap) {
        this.searchUnitMap = searchUnitMap;
    }

    public TdZwlogUpload getTdZwlogUpload() {
        return tdZwlogUpload;
    }

    public void setTdZwlogUpload(TdZwlogUpload tdZwlogUpload) {
        this.tdZwlogUpload = tdZwlogUpload;
    }

    public String getShowContent() {
        return showContent;
    }

    public void setShowContent(String showContent) {
        this.showContent = showContent;
    }

    public Map<String, String> getUploadUnitMap() {
        return uploadUnitMap;
    }

    public void setUploadUnitMap(Map<String, String> uploadUnitMap) {
        this.uploadUnitMap = uploadUnitMap;
    }

    public String getDealState() {
        return dealState;
    }

    public void setDealState(String dealState) {
        this.dealState = dealState;
    }

    public Map<String, String> getUploadZoneMap() {
        return uploadZoneMap;
    }

    public void setUploadZoneMap(Map<String, String> uploadZoneMap) {
        this.uploadZoneMap = uploadZoneMap;
    }

    public boolean getIfAdmin() {
        return ifAdmin;
    }

    public void setIfAdmin(boolean ifAdmin) {
        this.ifAdmin = ifAdmin;
    }

    @Override
    public void processData(List<?> list) {
        int size = list.size();
        for (int i = 0; i < size; i++) {
            TdZwlogUpload2 o = (TdZwlogUpload2)list.get(i);
            if(o.getSubs()!=null && "0".equals(o.getSubs().getUpload_tag())){
                TdZwlogUploadText tdZwlogUploadText = mongoService.findOne(o.getSubs().getRid(),TdZwlogUploadText.class);
                String respText = tdZwlogUploadText.getResp_text();
                List<String> respText2 = subStr(tag,respText);
                Set<String> set = new HashSet<>(respText2);
                StringBuilder stringBuilder = new StringBuilder();
                for(String s : set){
                    stringBuilder.append(s);
                }
                o.getSubs().setResp_text(stringBuilder.toString());
            }
        }
        if(!"2".equals(tag)&&list==null){
            return;
        }
        for (int i = 0; i < size; i++) {
            TdZwlogUpload2 o = (TdZwlogUpload2)list.get(i);
            if(o.getSubs()!=null){
                o.getSubs().setBhk_idc(StringUtils.encryptIdc(o.getSubs().getBhk_idc()));
            }
        }
    }


    /**
     * 截取 { } 内的字符串到list中
     * @param origin 源字符串
     * @return List<String>
     */
    public static List<String> subStr(String tag,String origin) {
        String substart = "<DESC>";
        if (origin == null) {
            return null;
        }
        List<String> ret = new ArrayList<>();
        int ch = 0, start, end;
        while (ch < origin.length()) {
            // 索引出现负数，说明在源字符串指定位置之后已经没有 '{' 或者 '}'
            if("2".equals(tag)){
                start = origin.indexOf("<DESC>", ch);
                end = origin.indexOf("</DESC>", ch);
            }else{
                start = origin.indexOf("<DESC>", origin.indexOf("<DESC>", ch)+1);
                end = origin.indexOf("</DESC>", origin.indexOf("</DESC>", ch)+1);
            }

            // substring 内部索引禁止出现负数
            if (origin.indexOf("<DESC>", ch) == -1 || origin.indexOf("</DESC>", ch) == -1) {
                break;
            }
            String tmp = origin.substring(start, end).substring(substart.length());
            //保存上一次截取时的索引
            ch = end + 1;
            ret.add(tmp);
        }
        return ret;
    }

    public TdZwlogUploadText getMainText() {
        return mainText;
    }

    public void setMainText(TdZwlogUploadText mainText) {
        this.mainText = mainText;
    }

    public TdZwlogUploadText getSubText() {
        return subText;
    }

    public void setSubText(TdZwlogUploadText subText) {
        this.subText = subText;
    }

    public String getShowErrorContent() {
        return showErrorContent;
    }

    public void setShowErrorContent(String showErrorContent) {
        this.showErrorContent = showErrorContent;
    }

    public String getExportFileName() {
        try {
            if("1".equals(tag)){
                exportFileName = "企业日志";
            }else{
                exportFileName = "人员日志";
            }
            return exportFileName = new String(exportFileName.getBytes("GBK"), "ISO-8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    public void setExportFileName(String exportFileName) {
        this.exportFileName = exportFileName;
    }
}
