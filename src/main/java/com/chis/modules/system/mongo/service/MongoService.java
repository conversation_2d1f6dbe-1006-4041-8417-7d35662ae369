package com.chis.modules.system.mongo.service;

import com.chis.modules.system.mongo.entity.MongoPage;
import com.chis.modules.system.mongo.entity.bhkdataservcie.TdZwlogUpload;
import com.chis.modules.system.mongo2.entity.MongoPage2;
import com.mongodb.WriteResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/***
 * <p>类描述: mongo常用方法业务层 </p>
 *
 * @ClassAuthor mxp,2018/12/17,MongoService
 */
@Service
public class MongoService {

    @Autowired
    private MongoOperations mongoTemplate;
   /* @Autowired
    private MongoTemplate mongoTemplate;*/

    public  <T> MongoPage pageList(int pageNum, Query query, Class<T> clazz){

        MongoPage mongoPage = new MongoPage();
        mongoPage.setCurrent(pageNum);
        //总记录数
        long total = mongoTemplate.count(query, clazz);

        //查询记录数
        query.skip((int)(pageNum * mongoPage.getSize())).limit((int)mongoPage.getSize());
        List<T> list = mongoTemplate.find(query, clazz);
        mongoPage.setRecords(list);
        mongoPage.setTotal(total);
        return mongoPage;
    }

    /***
     * <p>方法描述: 更新</p>
     *
     * @MethodAuthor mxp,2018/12/19,updateFirst
     */
    public <T> WriteResult updateFirst(Query query, Update update, Class<T> entityClass){
        return mongoTemplate.updateFirst(query,update,entityClass);
    }
    /***
     * <p>方法描述: 更新</p>
     *
     * @MethodAuthor mxp,2018/12/19,updateMulti
     */
    public <T> WriteResult updateMulti(Query query, Update update, Class<T> entityClass){
        return mongoTemplate.updateMulti(query,update,entityClass);
    }


    /***
     * <p>方法描述: 批量查询</p>
     *
     * @MethodAuthor mxp,2018/12/12,find
     */
    public <T> List<T> find(Query query, Class<T> entityClass){
        System.out.println("列表查询语句："+query);
        return mongoTemplate.find(query,entityClass);
    }

    /***
     * <p>方法描述: 批量查询</p>
     *
     * @MethodAuthor mxp,2018/12/12,find
     */
    public <T> List<T> find(Query query, Class<T> entityClass,String collectionName){
        return mongoTemplate.find(query,entityClass,collectionName);
    }
    /***
     * <p>方法描述: 记录数查询</p>
     *
     * @MethodAuthor mxp,2018/12/13,count
     */
    public <T> long count(Query query,Class<T> entityClass){
        System.out.println("列表查询语句："+query);
        return mongoTemplate.count(query,entityClass);
    }

    /***
     * <p>方法描述: 记录数查询</p>
     *
     * @MethodAuthor mxp,2018/12/13,count
     */
    public <T> long count(Query query,String collectionName){
        return mongoTemplate.count(query,collectionName);
    }

    /***
     * <p>方法描述:聚合查询</p>
     *
     * @MethodAuthor mxp,2018/12/17,pageList
     */
    public <T,I> List<T> pageList(Aggregation aggregation,Class<T> entityClass,Class<I> inClass){
        AggregationResults<T> aggregate = mongoTemplate.aggregate(aggregation,inClass, entityClass);
        List<T> mappedResults = aggregate.getMappedResults();
        return mappedResults;
    }
    /**
     *  <p>方法描述：聚合查询</p>
     * @MethodAuthor hsj 2023-03-17 13:56
     */
    public <T> List<T> aggregationList(Aggregation aggregation,String collectionName,Class<T> entityClass){
        System.out.println(aggregation);
        AggregationResults<T> aggregate = mongoTemplate.aggregate(aggregation,collectionName, entityClass);
        List<T> mappedResults = aggregate.getMappedResults();
        return mappedResults;
    }

    /***
     * <p>方法描述:聚合查询</p>
     *
     * @MethodAuthor mxp,2018/12/17,pageList
     */
    public <T,I> List<T> pageList(List<AggregationOperation> operations,Class<T> entityClass,Class<I> inClass){
        Aggregation agg = Aggregation.newAggregation(operations);
        System.out.println(agg);
        AggregationResults<T> aggregate2 = mongoTemplate.aggregate(agg,inClass, entityClass);
        List<T> mappedResults = aggregate2.getMappedResults();
        return mappedResults;
    }

    public <T> List<T> pageList2(int first , int pageSize, Query query, Class<T> clazz){
        //查询记录数
        query.skip(first).limit(pageSize+1);
        System.out.println("列表查询语句："+query);
        List<T> list = mongoTemplate.find(query, clazz);
        return list;
    }

    public  int count(List<AggregationOperation> operations){
        TypedAggregation<TdZwlogUpload> agg = Aggregation.newAggregation(TdZwlogUpload.class, operations);
        AggregationResults<Map> aggregate = mongoTemplate.aggregate(agg, Map.class);
        List<Map> mappedResults = aggregate.getMappedResults();
        if(mappedResults!=null&&mappedResults.size()>0){
            return  (Integer)mappedResults.get(0).get("count");
        }
        return 0;

    }
    /***
     * <p>方法描述: 根据id 查询</p>
     *
     * @MethodAuthor mxp,2018/12/19,findOne
     */
    public <T> T findOne(String rid,Class<T> entityClass) {
        if(rid!=null){
            return mongoTemplate.findById(rid, entityClass);
        }
        return  null;
    }


    /***
     * @Description : mongodb日志查询，此方法每次返回数据多一条
     * @ClassAuthor : mxp
     * @Date : 2019/5/18 10:24
     */
    public <T> MongoPage2<T> pageList(MongoPage2<T> mongoPage, Query query, Class<T> tClass) {
        int current = mongoPage.getCurrent();
        int size = mongoPage.getSize();
        query.skip((current-1) * size).limit(size+1);
        String[] descs = mongoPage.getDescs();
        if(descs!=null&&descs.length>0){
            query.with(new Sort(Sort.Direction.DESC , descs));
        }
        System.out.println("列表查询语句："+query);
        mongoPage.setRecords(mongoTemplate.find(query, tClass));
        return mongoPage;
    }
    /***
     * @Description : mongodb日志查询
     * @ClassAuthor : mxp
     * @Date : 2019/5/18 10:24
     */
    public <T> MongoPage2<T> pageListCommon(MongoPage2<T> mongoPage, Query query, Class<T> tClass) {
        int current = mongoPage.getCurrent();
        int size = mongoPage.getSize();
        query.skip((current-1) * size).limit(size);
        String[] descs = mongoPage.getDescs();
        if(descs!=null&&descs.length>0){
            query.with(new Sort(Sort.Direction.DESC , descs));
        }
        System.out.println("列表查询语句："+query);
        mongoPage.setRecords(mongoTemplate.find(query, tClass));
        return mongoPage;
    }
}
