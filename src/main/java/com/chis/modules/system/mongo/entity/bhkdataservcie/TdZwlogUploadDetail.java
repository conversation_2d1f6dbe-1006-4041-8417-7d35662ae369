package com.chis.modules.system.mongo.entity.bhkdataservcie;


import java.util.Date;

/***
 * <p>类描述: 明细 </p>
 *
 * @ClassAuthor mxp,2018/12/19,TdZwlogUploadDetail
 */
public class TdZwlogUploadDetail {

    //RID
    private String rid;//	VARCHAR2(50)	50		TRUE	FALSE	TRUE
    //请求唯一标识
    private String uid;//	VARCHAR2(50)	50		FALSE	FALSE	TRUE
    //企业名称(企业时使用)
    private String crpt_name;//	VARCHAR2(50)	50		FALSE	FALSE	TRUE
    //社会信用代码(企业时使用
    private String credit_code;//	CREDIT_CODE	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //所属地区(企业时使用)
    private String crpt_zone_code;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //所属地区名称(企业时使用)
    private String crpt_zone_name;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE

    //操作标识(体检记录时使用)
    private String opt_tag;//	VARCHAR2(50)	50		FALSE	FALSE	TRUE
    //体检机构编号(体检记录时使用)
    private String bhk_org_code;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //体检机构名称(体检记录时使用)
    private String bhk_org_name;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //体检编号(体检记录时使用)
    private String bhk_code;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //企业名称(体检记录时使用)
    private String bhk_crpt_name;//	VARCHAR2(50)	50		FALSE	FALSE	TRUE
    //社会信用代码(体检记录时使用)
    private String bhk_credit_code;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //人员姓名(体检记录时使用)
    private String bhk_psn_name;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //身份证号(体检记录时使用)
    private String bhk_idc;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //体检日期(体检记录时使用)
    private Date bhk_date;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    private Date bhk_date_start;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    private Date bhk_date_end;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //是否为复检(体检记录时使用)
    private String bhk_if_rhk;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //上传标记
    private String upload_tag;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //响应内容
    private String resp_text;//	CLOB FALSE	FALSE	FALSE
    //请求内容
    private String req_text;//
    //处理状态 2 成功无需处理 0 错误未处理 1 错误已处理
    private String deal_state;//

    public String getReq_text() {
        return req_text;
    }

    public void setReq_text(String req_text) {
        this.req_text = req_text;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getCrpt_name() {
        return crpt_name;
    }

    public void setCrpt_name(String crpt_name) {
        this.crpt_name = crpt_name;
    }

    public String getCredit_code() {
        return credit_code;
    }

    public void setCredit_code(String credit_code) {
        this.credit_code = credit_code;
    }

    public String getCrpt_zone_code() {
        return crpt_zone_code;
    }

    public void setCrpt_zone_code(String crpt_zone_code) {
        this.crpt_zone_code = crpt_zone_code;
    }

    public String getCrpt_zone_name() {
        return crpt_zone_name;
    }

    public void setCrpt_zone_name(String crpt_zone_name) {
        this.crpt_zone_name = crpt_zone_name;
    }

    public String getOpt_tag() {
        return opt_tag;
    }

    public void setOpt_tag(String opt_tag) {
        this.opt_tag = opt_tag;
    }

    public String getBhk_org_code() {
        return bhk_org_code;
    }

    public void setBhk_org_code(String bhk_org_code) {
        this.bhk_org_code = bhk_org_code;
    }

    public String getBhk_org_name() {
        return bhk_org_name;
    }

    public void setBhk_org_name(String bhk_org_name) {
        this.bhk_org_name = bhk_org_name;
    }

    public String getBhk_code() {
        return bhk_code;
    }

    public void setBhk_code(String bhk_code) {
        this.bhk_code = bhk_code;
    }

    public String getBhk_crpt_name() {
        return bhk_crpt_name;
    }

    public void setBhk_crpt_name(String bhk_crpt_name) {
        this.bhk_crpt_name = bhk_crpt_name;
    }

    public String getBhk_credit_code() {
        return bhk_credit_code;
    }

    public void setBhk_credit_code(String bhk_credit_code) {
        this.bhk_credit_code = bhk_credit_code;
    }

    public String getBhk_psn_name() {
        return bhk_psn_name;
    }

    public void setBhk_psn_name(String bhk_psn_name) {
        this.bhk_psn_name = bhk_psn_name;
    }

    public String getBhk_idc() {
        return bhk_idc;
    }

    public void setBhk_idc(String bhk_idc) {
        this.bhk_idc = bhk_idc;
    }

    public Date getBhk_date() {
        return bhk_date;
    }

    public void setBhk_date(Date bhk_date) {
        this.bhk_date = bhk_date;
    }

    public String getBhk_if_rhk() {
        return bhk_if_rhk;
    }

    public void setBhk_if_rhk(String bhk_if_rhk) {
        this.bhk_if_rhk = bhk_if_rhk;
    }

    public String getUpload_tag() {
        return upload_tag;
    }

    public void setUpload_tag(String upload_tag) {
        this.upload_tag = upload_tag;
    }

    public String getResp_text() {
        return resp_text;
    }

    public void setResp_text(String resp_text) {
        this.resp_text = resp_text;
    }

    public String getDeal_state() {
        return deal_state;
    }

    public void setDeal_state(String deal_state) {
        this.deal_state = deal_state;
    }

    public Date getBhk_date_start() {
        return bhk_date_start;
    }

    public void setBhk_date_start(Date bhk_date_start) {
        this.bhk_date_start = bhk_date_start;
    }

    public Date getBhk_date_end() {
        return bhk_date_end;
    }

    public void setBhk_date_end(Date bhk_date_end) {
        this.bhk_date_end = bhk_date_end;
    }
}
