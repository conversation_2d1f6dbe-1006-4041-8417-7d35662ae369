package com.chis.modules.system.mongo.pojo;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "BHKRESPONSEDATA")
public class BhkResponseData {
    private String retCode;
    private String desc;
    private List<ErrorMainData> errors;

    @XmlElement(name = "RETCODE")
    public String getRetCode() {
        return retCode;
    }

    public void setRetCode(String retCode) {
        this.retCode = retCode;
    }

    @XmlElement(name = "DESC")
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @XmlElementWrapper(name = "ERRORS")
    @XmlElement(name = "ERRORMAINDATA")
    public List<ErrorMainData> getErrors() {
        return errors;
    }

    public void setErrors(List<ErrorMainData> errors) {
        this.errors = errors;
    }
}
