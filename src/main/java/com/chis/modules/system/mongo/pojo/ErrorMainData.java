package com.chis.modules.system.mongo.pojo;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "ERRORMAINDATA")
public class ErrorMainData {
    private String tableName;
    private String uuid;
    private String desc;
    private String errorDatas;

    @XmlElement(name = "TABLE_NAME")
    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    @XmlElement(name = "UUID")
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @XmlElement(name = "DESC")
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @XmlElement(name = "ERRORDATAS")
    public String getErrorDatas() {
        return errorDatas;
    }

    public void setErrorDatas(String errorDatas) {
        this.errorDatas = errorDatas;
    }
}
