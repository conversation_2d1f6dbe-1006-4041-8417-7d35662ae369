package com.chis.modules.system.mongo.entity.bhkdataservcie;

import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import java.util.Date;

/***
 * <p>类描述: 请求报文 </p>
 *
 * @ClassAuthor mxp,2019/1/18,TdZwlogUploadText
 */
@Document(collection = "tdmg_bhk_service_log_text")
public class TdZwlogUploadText {

    @Id
    private String id;

    private String req_text;
    private String resp_text;
    private Date create_date;
    private Integer create_manid;

    public Date getCreate_date() {
        return create_date;
    }

    public void setCreate_date(Date create_date) {
        this.create_date = create_date;
    }

    public Integer getCreate_manid() {
        return create_manid;
    }

    public void setCreate_manid(Integer create_manid) {
        this.create_manid = create_manid;
    }
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getReq_text() {
        return req_text;
    }

    public void setReq_text(String req_text) {
        this.req_text = req_text;
    }

    public String getResp_text() {
        return resp_text;
    }

    public void setResp_text(String resp_text) {
        this.resp_text = resp_text;
    }
}
