package com.chis.modules.system.mongo.entity.bhkdataservcie;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/***
 * <p>类描述: 主信息 </p>
 *
 * @ClassAuthor mxp,2018/12/19,TdZwlogUpload
 */
@Document(collection = "tdmg_bhk_service_log")
public class TdZwlogUpload {
    @Id
    private String rid;

    //客户端IP
    private String client_ip;//	VARCHAR2(50)	50		FALSE	FALSE	TRUE
    //调用地址
    private String visit_adr;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //接口类型
    private String inter_type;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //调用时间
    private Date visit_time;//	timestamp			FALSE	FALSE	FALSE
    //请求内容
    private String req_text;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //响应内容
    private String resp_text;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //明细内
    private List<TdZwlogUploadDetail> subs = new ArrayList<>();//（子文档见明细表）	TD_ZWLOG_UPLOAD_DETAIL	VARCHAR2(50)	50		FALSE	FALSE	FALSE

    private String zone_gb;
    //上传机构编码
    private String unit_code;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //上传机构名称
    private String unit_name;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //子数据总数
    private String sub_count;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //成功数
    private String success_count;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    private Date create_date;
    private Integer create_manid;

    public Date getCreate_date() {
        return create_date;
    }

    public void setCreate_date(Date create_date) {
        this.create_date = create_date;
    }

    public Integer getCreate_manid() {
        return create_manid;
    }

    public void setCreate_manid(Integer create_manid) {
        this.create_manid = create_manid;
    }

    public String getZone_gb() {
        return zone_gb;
    }

    public void setZone_gb(String zone_gb) {
        this.zone_gb = zone_gb;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getClient_ip() {
        return client_ip;
    }

    public void setClient_ip(String client_ip) {
        this.client_ip = client_ip;
    }

    public String getVisit_adr() {
        return visit_adr;
    }

    public void setVisit_adr(String visit_adr) {
        this.visit_adr = visit_adr;
    }

    public String getInter_type() {
        return inter_type;
    }

    public void setInter_type(String inter_type) {
        this.inter_type = inter_type;
    }

    public Date getVisit_time() {
        return visit_time;
    }

    public void setVisit_time(Date visit_time) {
        this.visit_time = visit_time;
    }

    public String getReq_text() {
        return req_text;
    }

    public void setReq_text(String req_text) {
        this.req_text = req_text;
    }

    public String getResp_text() {
        return resp_text;
    }

    public void setResp_text(String resp_text) {
        this.resp_text = resp_text;
    }

    public List<TdZwlogUploadDetail> getSubs() {
        return subs;
    }

    public void setSubs(List<TdZwlogUploadDetail> subs) {
        this.subs = subs;
    }

    public String getUnit_code() {
        return unit_code;
    }

    public void setUnit_code(String unit_code) {
        this.unit_code = unit_code;
    }

    public String getUnit_name() {
        return unit_name;
    }

    public void setUnit_name(String unit_name) {
        this.unit_name = unit_name;
    }

    public String getSub_count() {
        return sub_count;
    }

    public void setSub_count(String sub_count) {
        this.sub_count = sub_count;
    }

    public String getSuccess_count() {
        return success_count;
    }

    public void setSuccess_count(String success_count) {
        this.success_count = success_count;
    }



}
