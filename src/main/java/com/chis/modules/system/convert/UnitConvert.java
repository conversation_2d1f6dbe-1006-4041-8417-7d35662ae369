package com.chis.modules.system.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.logic.UnitPO;

/**
 * 用于角色维护模块中
 * Created by zww on 2014-5-7.
 */
@FacesConverter("system.UnitConvert")
public class UnitConvert implements Converter {
    @Override
    public Object getAsObject(FacesContext facesContext, UIComponent component, String submittedValue) {
        if(StringUtils.isNotBlank(submittedValue)) {
            String[] arr = submittedValue.split(":");
            UnitPO u = new UnitPO(Integer.valueOf(arr[0]));
            u.setUnitName(arr[1]);
            u.setZoneName(arr[2]);
            return u;
        }
        return null;
    }

    @Override
    public String getAsString(FacesContext facesContext, UIComponent component, Object value) {
        if (value == null) {
            return "";
        } else {
            UnitPO u = (UnitPO)value;
            return u.getUnitId()+":"+u.getUnitName()+":"+u.getZoneName();
        }
    }
}
