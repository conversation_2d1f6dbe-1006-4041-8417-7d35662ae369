package com.chis.modules.system.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 用于消息发送
 * Created by zww on 2014-5-7.
 */
@FacesConverter("system.UserMsgConvert")
public class UserMsgConvert implements Converter {
    @Override
    public Object getAsObject(FacesContext facesContext, UIComponent component, String submittedValue) {
        if(StringUtils.isNotBlank(submittedValue)) {
            String[] arr = submittedValue.split(":");
            TsUserInfo t = new TsUserInfo(Integer.valueOf(arr[0]));
            t.setUsername(arr[1]);
            if(StringUtils.isNotBlank(arr[2]) &&  !"null".equals(arr[2])) {
            	t.setOfficeId(Integer.valueOf(arr[2]));
            }
            t.setOfficeName(arr[3]);
            if(StringUtils.isNotBlank(arr[4]) &&  !"null".equals(arr[4])) {
            	t.setDutyId(Integer.valueOf(arr[4]));
            }
            if(StringUtils.isNotBlank(arr[5]) &&  !"null".equals(arr[5])) {
            	t.setIsLeader(Integer.valueOf(arr[5]));
            }
            if(StringUtils.isNotBlank(arr[6]) &&  !"null".equals(arr[6])) {
            	t.setMbPhone(arr[6]);
            }
            return t;
        }
        return null;
    }

    @Override
    public String getAsString(FacesContext facesContext, UIComponent component, Object value) {
        if (value == null) {
            return "";
        } else {
            TsUserInfo t = (TsUserInfo)value;
            StringBuilder sb = new StringBuilder();
            sb.append(t.getRid()).append(":").append(t.getUsername()).append(":").append(t.getOfficeId()).append(":");
            sb.append(t.getOfficeName()).append(":").append(t.getDutyId()).append(":").append(t.getIsLeader());
            sb.append(":").append(t.getMbPhone());
            return sb.toString();
        }
    }
}
