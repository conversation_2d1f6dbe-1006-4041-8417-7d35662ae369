package com.chis.modules.system.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 用于角色维护模块中
 * Created by zww on 2014-5-7.
 */
@FacesConverter("system.UserConvert")
public class UserConvert implements Converter {
    @Override
    public Object getAsObject(FacesContext facesContext, UIComponent component, String submittedValue) {
        if(StringUtils.isNotBlank(submittedValue)) {
            String[] arr = submittedValue.split(":");
            TsUserInfo t = new TsUserInfo(Integer.valueOf(arr[0]));
            t.setUsername(arr[1]);
            t.setUserNo(arr[2]);
            return t;
        }
        return null;
    }

    @Override
    public String getAsString(FacesContext facesContext, UIComponent component, Object value) {
        if (value == null) {
            return "";
        } else {
            TsUserInfo t = (TsUserInfo)value;
            return t.getRid() + ":" + t.getUsername() + ":" + t.getUserNo();
        }
    }
}
