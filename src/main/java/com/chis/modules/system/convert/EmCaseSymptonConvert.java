package com.chis.modules.system.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 * <AUTHOR>
 * @createDate 2014-12-10
 */
@FacesConverter("system.EmCaseSymptonConvert")
public class EmCaseSymptonConvert implements Converter {
    @Override
    public Object getAsObject(FacesContext facesContext, UIComponent uiComponent, String s) {
        return null;
    }

    @Override
    public String getAsString(FacesContext facesContext, UIComponent uiComponent, Object o) {
        if(o != null){
            String s = o.toString();
            if(s.length() > 56){
                String sub = s.substring(0,55);
                if(sub.substring(54,55).endsWith("，")){
                    sub = s.substring(0,54);
                }
                return sub+". . .";
            }else{
                return s;
            }
        }
        return "";
    }
}
