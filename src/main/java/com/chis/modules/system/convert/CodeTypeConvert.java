package com.chis.modules.system.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.enumn.SystemType;

/**
 * 用于角色维护模块中
 * Created by zww on 2014-5-7.
 */
@FacesConverter("system.CodeTypeConvert")
public class CodeTypeConvert implements Converter {
    @Override
    public Object getAsObject(FacesContext facesContext, UIComponent component, String submittedValue) {
        if(StringUtils.isNotBlank(submittedValue)) {
            String[] arr = submittedValue.split(":");
            TsCodeType t = new TsCodeType(Integer.valueOf(arr[0]));
            t.setCodeTypeName(arr[1]);
            t.setCodeTypeDesc(arr[2]);
            t.setSystemType((SystemType) EnumUtils.findEnum(SystemType.class, arr[3]));
            return t;
        }
        return null;
    }

    @Override
    public String getAsString(FacesContext facesContext, UIComponent component, Object value) {
        if (value == null) {
            return "";
        } else {
            TsCodeType t = (TsCodeType)value;
            return t.getRid() + ":" + t.getCodeTypeName() + ":" + t.getCodeTypeDesc() + ":" + t.getSystemType().getTypeNo();
        }
    }
}
