package com.chis.modules.system.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.chis.common.utils.StringUtils;
@FacesConverter("system.SimpleCodeObjConvert")
public class SimpleCodeObjConvert implements Converter{
	 @Override
	    public Object getAsObject(FacesContext facesContext, UIComponent component, String submittedValue) {
	        if(StringUtils.isNotBlank(submittedValue)) {
	            String[] arr = submittedValue.split(":");
	            Object[] obj=new Object[5];
	            obj[0]=arr[0];
	            obj[1]=arr[1];
	            obj[2]=arr[2];
	            obj[3]=arr[3];
	            obj[4]=arr[4];
	            return obj;
	        }
	        return null;
	    }

	    @Override
	    public String getAsString(FacesContext facesContext, UIComponent component, Object value) {
	        if (value == null) {
	            return "";
	        } else {
	        	Object[] obj = (Object[])value;
	            return obj[0] + ":" + obj[1]+":"+obj[2]+":"+obj[3]+":"+obj[4];
	        }
	    }
}
