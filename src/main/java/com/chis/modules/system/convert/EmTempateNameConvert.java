package com.chis.modules.system.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 * <AUTHOR>
 * @createDate 2014-12-10
 */
@FacesConverter("system.EmTempateNameConvert")
public class EmTempateNameConvert implements Converter {
    @Override
    public Object getAsObject(FacesContext facesContext, UIComponent uiComponent, String s) {
        return null;
    }

    @Override
    public String getAsString(FacesContext facesContext, UIComponent uiComponent, Object o) {
        if(o != null){
            String s = o.toString();
            if(s.length() > 8){
                return s.substring(0,7)+"...";
            }else{
                return s;
            }
        }
        return "";
    }
}
