package com.chis.modules.system.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.logic.ProbSubOrderBean;

/**
 * 用于问卷题目排序
 */
@FacesConverter("system.ProbSubOrderConvert")
public class ProbSubOrderConvert implements Converter {
	@Override
	public Object getAsObject(FacesContext facesContext, UIComponent component,
			String submittedValue) {
		if (StringUtils.isNotBlank(submittedValue)) {
			String[] arr = submittedValue.split("::#@");
			ProbSubOrderBean t = new ProbSubOrderBean(arr[0].toString(),
					arr[1].toString(), arr[2].toString());
			return t;
		}
		return null;
	}

	@Override
	public String getAsString(FacesContext facesContext, UIComponent component,
			Object value) {
		if (value == null) {
			return "";
		} else {
			ProbSubOrderBean t = (ProbSubOrderBean) value;
			return t.getShowCode() + "::#@" + t.getTitleDesc() + "::#@"
					+ t.getQesCode();
		}
	}
}
