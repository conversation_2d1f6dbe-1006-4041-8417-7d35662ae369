package com.chis.modules.system.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 用于待办人选择模块中  <br/>
 * Created by zww on 2014-11-22.  <br/>
 */
@FacesConverter("system.UserOfficeUnitConvert")
public class UserOfficeUnitConvert implements Converter {
	@Override
	public Object getAsObject(FacesContext facesContext, UIComponent component, String submittedValue) {
		if (StringUtils.isNotBlank(submittedValue)) {
			String[] o = submittedValue.split(":");
//			TsUserInfo t = new TsUserInfo(Integer.valueOf(arr[0]));
//			t.setUsername(arr[1]);
//
//			TsOffice office = new TsOffice(Integer.valueOf(arr[2]), arr[3]);
//			TsUnit unit = new TsUnit(Integer.valueOf(arr[4]));
//			unit.setUnitSimpname(arr[5]);
//
//			office.setTsUnit(unit);
//			t.setTsOffice(office);
//			t.setTsUnit(unit);

			
			 TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
			 t.setUsername(o[1]==null?"":o[1].toString());
			 
			 TsUnit unit = new TsUnit(Integer.valueOf(o[4].toString()));
			 unit.setUnitSimpname(o[5].toString());
			 
			 t.setOfficeIds(String.valueOf(o[2]));
			 t.setOfficeNames(String.valueOf(o[3]));
			 t.setTsUnit(unit);
			return t;
		}
		return null;
	}

	@Override
	public String getAsString(FacesContext facesContext, UIComponent component, Object value) {
		if (value == null) {
			return "";
		} else {
			TsUserInfo t = (TsUserInfo) value;
			StringBuilder sb = new StringBuilder();
			sb.append(t.getRid()).append(":").append(t.getUsername()).append(":");
			sb.append(t.getOfficeIds()).append(":").append(t.getOfficeNames()).append(":");
			sb.append(t.getTsUnit().getRid()).append(":").append(t.getTsUnit().getUnitSimpname());
			return sb.toString();
		}
	}
}
