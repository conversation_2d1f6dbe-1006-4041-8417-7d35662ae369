package com.chis.modules.system.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 用于角色维护模块中
 * Created by zww on 2014-5-7.
 */
@FacesConverter("system.UserOfficeFilterConvert")
public class UserOfficeFilterConvert implements Converter {
    @Override
    public Object getAsObject(FacesContext facesContext, UIComponent component, String submittedValue) {
        if(StringUtils.isNotBlank(submittedValue)) {
            String[] arr = submittedValue.split(":");
            TsUserInfo t = new TsUserInfo(Integer.valueOf(arr[0]));
            t.setUsername(arr[1]);
            t.setOfficeId( null == arr[2] ? null :Integer.valueOf(arr[2]));
            t.setOfficeName(arr[3]);
            t.setOfficeIds(arr[4]);
            t.setOfficeNames(arr[5]);
            return t;
        }
        return null;
    }

    @Override
    public String getAsString(FacesContext facesContext, UIComponent component, Object value) {
        if (value == null) {
            return "";
        } else {
            TsUserInfo t = (TsUserInfo)value;
			return new StringBuilder("").append(t.getRid()).append(":").append(t.getUsername()).append(":")
					.append(t.getOfficeId() == null ? "" : t.getOfficeId()).append(":")
					.append(t.getOfficeName() == null ? "" : t.getOfficeName()).append(":")
					.append(t.getOfficeIds() == null ? "" : t.getOfficeIds()).append(":")
					.append(t.getOfficeNames() == null ? "" : t.getOfficeNames()).toString();
		}
	}
}
