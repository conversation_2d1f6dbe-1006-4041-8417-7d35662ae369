package com.chis.modules.system.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 用于角色维护模块中
 * Created by zww on 2014-5-7.
 */
@FacesConverter("system.SimpleCodeConvert")
public class SimpleCodeConvert implements Converter {
    @Override
    public Object getAsObject(FacesContext facesContext, UIComponent component, String submittedValue) {
        if(StringUtils.isNotBlank(submittedValue)) {
            String[] arr = submittedValue.split(":");
            TsSimpleCode t = new TsSimpleCode(Integer.valueOf(arr[0]));
            t.setCodeName(arr[1]);
            return t;
        }
        return null;
    }

    @Override
    public String getAsString(FacesContext facesContext, UIComponent component, Object value) {
        if (value == null) {
            return "";
        } else {
            TsSimpleCode t = (TsSimpleCode)value;
            return t.getRid() + ":" + t.getCodeName();
        }
    }
}
