package com.chis.modules.system.convert;

import com.chis.modules.system.entity.TsSystemUpdate;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.logic.SQLSentence;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据库版本管理Convert
 *
 * <AUTHOR>
 * @date 2021/12/1
 */
public class DbVisionConvert {

    /**
     * 页面展示序号
     */
    private Integer index;

    /**
     * 版本是否一致
     */
    private boolean visionIdentical;

    /**
     * 系统类别
     */
    private SystemType systemType;

    /**
     * 系统类别中文名称
     */
    private String systemTypeNameCn;

    /**
     * 最新系统版本号
     */
    private Integer latestVision;

    /**
     * 数据库版本信息
     */
    private TsSystemUpdate tsSystemUpdate;

    /**
     * sql list
     */
    private List<SQLSentence> sqlSentenceList;

    /**
     * 错误信息（最新）
     */
    private String errors;

    public DbVisionConvert() {
        this.sqlSentenceList = new ArrayList<>();
        this.tsSystemUpdate = new TsSystemUpdate();
    }

    public void init(Integer index, SystemType systemType, List<SQLSentence> sqlSentenceList, TsSystemUpdate tsSystemUpdate) {
        this.index = index;
        this.systemType = systemType;
        this.systemTypeNameCn = systemType.getTypeCN();
        if (sqlSentenceList == null) {
            sqlSentenceList = new ArrayList<>();
        }
        this.sqlSentenceList = sqlSentenceList;
        if (sqlSentenceList.size() < 1) {
            this.latestVision = 0;
        } else {
            this.latestVision = sqlSentenceList.get(sqlSentenceList.size() - 1).getVer();
        }
        if (tsSystemUpdate == null) {
            tsSystemUpdate = new TsSystemUpdate();
            tsSystemUpdate.setCurVersion(0);
        }
        this.tsSystemUpdate = tsSystemUpdate;
        if (this.tsSystemUpdate.getCurVersion() == null || this.latestVision == null) {
            this.visionIdentical = false;
        } else {
            this.visionIdentical = this.tsSystemUpdate.getCurVersion().compareTo(this.latestVision) >= 0;
        }
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public boolean isVisionIdentical() {
        return visionIdentical;
    }

    public void setVisionIdentical(boolean visionIdentical) {
        this.visionIdentical = visionIdentical;
    }

    public SystemType getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemType systemType) {
        this.systemType = systemType;
    }

    public String getSystemTypeNameCn() {
        return systemTypeNameCn;
    }

    public void setSystemTypeNameCn(String systemTypeNameCn) {
        this.systemTypeNameCn = systemTypeNameCn;
    }

    public Integer getLatestVision() {
        return latestVision;
    }

    public void setLatestVision(Integer latestVision) {
        this.latestVision = latestVision;
    }

    public TsSystemUpdate getTsSystemUpdate() {
        return tsSystemUpdate;
    }

    public void setTsSystemUpdate(TsSystemUpdate tsSystemUpdate) {
        this.tsSystemUpdate = tsSystemUpdate;
    }

    public List<SQLSentence> getSqlSentenceList() {
        return sqlSentenceList;
    }

    public void setSqlSentenceList(List<SQLSentence> sqlSentenceList) {
        this.sqlSentenceList = sqlSentenceList;
    }

    public String getErrors() {
        return errors;
    }

    public void setErrors(String errors) {
        this.errors = errors;
    }
}
