package com.chis.modules.system.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 * <AUTHOR>
 * @createDate 2015-06-01
 */
@FacesConverter("system.EmCaseSymptonConvertLength")
public class EmCaseSymptonConvertLength implements Converter {
    @Override
    public Object getAsObject(FacesContext facesContext, UIComponent uiComponent, String s) {
        return null;
    }

    @Override
    public String getAsString(FacesContext facesContext, UIComponent uiComponent, Object o) {
        if(o != null){
            String s = o.toString();
            if(s.length() > 26){
                String sub = s.substring(0,25);
                if(sub.substring(24,25).endsWith("，")){
                    sub = s.substring(0,24);
                }
                return sub+". . .";
            }else{
                return s;
            }
        }
        return "";
    }
}
