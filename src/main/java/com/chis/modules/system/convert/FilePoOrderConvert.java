package com.chis.modules.system.convert;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.logic.FilePo;

/**
 * 用于文件上传排序
 */
@FacesConverter("system.FilePoOrderConvert")
public class FilePoOrderConvert implements Converter {
	@Override
	public Object getAsObject(FacesContext facesContext, UIComponent component,
			String submittedValue) {
		if (StringUtils.isNotBlank(submittedValue)) {
			String[] arr = submittedValue.split("::#@");
			FilePo t = new FilePo(arr[0].toString(), arr[1].toString());
			return t;
		}
		return null;
	}

	@Override
	public String getAsString(FacesContext facesContext, UIComponent component,
			Object value) {
		if (value == null) {
			return "";
		} else {
			FilePo t = (FilePo) value;
			return t.getFileName() + "::#@" + t.getFilePath();
		}
	}
}
