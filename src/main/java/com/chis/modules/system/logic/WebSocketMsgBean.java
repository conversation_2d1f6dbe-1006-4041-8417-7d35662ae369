package com.chis.modules.system.logic;

import java.io.Serializable;

/**
 * websocket消息处理javabean
 * 
 * <AUTHOR>
 * @createDate 2016年11月7日
 */
public class WebSocketMsgBean implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4273668634472028327L;
	/**
	 * 消息类型 1：刷新右上角new、右下弹出框、以及待办任务 2：刷新右上角new以及待办任务
	 */
	private String msgType;
	/** 右下角弹出框信息 */
	private String globalInfo;

	public String getMsgType() {
		return msgType;
	}

	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}

	public String getGlobalInfo() {
		return globalInfo;
	}

	public void setGlobalInfo(String globalInfo) {
		this.globalInfo = globalInfo;
	}

}
