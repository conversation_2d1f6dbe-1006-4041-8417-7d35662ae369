package com.chis.modules.system.logic;

/**
 * <p>类描述：签章对象</p>
 * @ClassAuthor qrr,2018年4月21日,SignRptPO
 */
public class SignRptPO {
	// 签章赋值
	// embed1.SealServerAdr="http://10.88.99.215:9090";
	// embed1.SealPrintAdr="10.88.99.202:8095";
	// embed1.SealtokenId="60806752F4EC4BA1931EC63B032FA8CE";
	// embed1.SealRid="1";
	// embed1.SealifSign="1";
	// embed1.SealsignParam="{'appId':'67f6c99b74484c2b8bcb35473850cdd7','channelId':'CHN_69120806E7D44CE1','orgCode':'1230012311','ruleNum':'354D44C55206A68E','signPath':'lctest.isignet.cn','signPort':13001}";
	// embed1.filePath="29/3/"
	private String sealServerAdr;// 平台接口地址
	private String sealPrintAdr;// 签章地址
	private String sealtokenId;// 请求tokenId
	// private String SealRid;//传递id
	private String sealifSign;// 是否签章 0：否 1：是 【默认为0，为1时，必传signParam对象】
	private String sealsignParam;// 签章参数
	private String filePath;// oss路径
	private String returnMsg;//返回结果
	private RptSignParamPO signParam;

	public RptSignParamPO getSignParam() {
		return signParam;
	}

	public void setSignParam(RptSignParamPO signParam) {
		this.signParam = signParam;
	}

	public String getSealServerAdr() {
		return sealServerAdr;
	}

	public void setSealServerAdr(String sealServerAdr) {
		this.sealServerAdr = sealServerAdr;
	}

	public String getSealPrintAdr() {
		return sealPrintAdr;
	}

	public void setSealPrintAdr(String sealPrintAdr) {
		this.sealPrintAdr = sealPrintAdr;
	}

	public String getSealtokenId() {
		return sealtokenId;
	}

	public void setSealtokenId(String sealtokenId) {
		this.sealtokenId = sealtokenId;
	}

	public String getSealifSign() {
		return sealifSign;
	}

	public void setSealifSign(String sealifSign) {
		this.sealifSign = sealifSign;
	}

	public String getSealsignParam() {
		return sealsignParam;
	}

	public void setSealsignParam(String sealsignParam) {
		this.sealsignParam = sealsignParam;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public String getReturnMsg() {
		return returnMsg;
	}

	public void setReturnMsg(String returnMsg) {
		this.returnMsg = returnMsg;
	}

}
