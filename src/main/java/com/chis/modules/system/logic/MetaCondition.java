package com.chis.modules.system.logic;

import java.io.Serializable;

/**
 * 模板元素替换的必要条件 
 * <AUTHOR> 2014-11-28
 */
public class MetaCondition implements Serializable, Cloneable{
	
	private static final long serialVersionUID = 6351509061431854280L;
	/**当前登录用户ID*/
	private Integer userId;
	/**当前登录所在的机构ID*/
	private Integer unitId;
	/**业务主键ID*/
	private String businessId;
	/**json形式的查询条件*/
	private String json;
	
	public MetaCondition() {
		
	}
	
	public MetaCondition(Integer userId, Integer unitId) {
		this.userId = userId;
		this.unitId = unitId;
	}

	public MetaCondition(Integer userId, Integer unitId, String json) {
		this.userId = userId;
		this.unitId = unitId;
		this.json = json;
	}

	@Override
	public Object clone() throws CloneNotSupportedException {
		return super.clone();
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getJson() {
		return json;
	}

	public void setJson(String json) {
		this.json = json;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public String getBusinessId() {
		return businessId;
	}

	public void setBusinessId(String businessId) {
		this.businessId = businessId;
	}

}
