package com.chis.modules.system.logic;

import java.util.Date;

/**
 * <p>类描述：门户待办任务</p>
 * @ClassAuthor qrr,2021年12月2日,PorTalToDoTasks
 * */
public class PorTalToDoTasks {
	private String infoMsg;
	/**菜单对象*/
	private MenuVO menuVO; 
	//默认当天
	private Date date = new Date();
	//显示序号
	private Integer xh;
	public String getInfoMsg() {
		return infoMsg;
	}
	public void setInfoMsg(String infoMsg) {
		this.infoMsg = infoMsg;
	}
	public Date getDate() {
		return date;
	}
	public void setDate(Date date) {
		this.date = date;
	}
	public MenuVO getMenuVO() {
		return menuVO;
	}
	public void setMenuVO(MenuVO menuVO) {
		this.menuVO = menuVO;
	}
	public Integer getXh() {
		return xh;
	}
	public void setXh(Integer xh) {
		this.xh = xh;
	}
}
