package com.chis.modules.system.logic;


import com.chis.common.utils.PropertyUtils;

/**
 * <p>修订内容：添加传真验证规则</p>
 * @ClassReviser qrr,2018年5月4日,Constants
 * */
public class Constants {

	/**默认分页表格，每页显示多少条记录*/
	public static final int PAGE_SIZE = 20;
	/**系统超级管理员*/
	public static final String ADMIN = "0000";
    /**系统初始化密码*/
    public static final String INIT_PWD = PropertyUtils.getValue("initialPassword");
    /**自动编号的实现类路径*/
    public static final String AUTOCODE_CLASSPATH = "com/chis/ejb/service/autocode/impl/";
    /**自动编号的实现类包名*/
    public static final String AUTOCODE_PACKAGE = "com.chis.ejb.service.autocode.impl";
    /**邮箱的验证正则表达式*/
    public static final String EMAIL_REGEX = "^([a-z0-9A-Z]+[-|_|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
    /**身份证的验证正则表达式*/
    public static final String IDC_REGEX = "^(\\d{6})(18|19|20)?(\\d{2})([01]\\d)([0123]\\d)(\\d{3})(\\d|X|x)?$";
    /**手机号的验证正则表达式*/
    public static final String MOBILE_REGEX = "^\\s*$|^[1]\\d{10}";
    /**手机号或者电话的验证正则表达式*/
    public static final String PHONE_MOBILE_REGEX = "(^\\s*$|^[1]\\d{10})|(^\\s*$|^0(([1,2]\\d)|([3-9]\\d{2}))[-]?\\d{7,8}$)";
    /**中国邮政编码验证方式*/
    public static final String POST_CODE = "^[0-9]{6}$";
    
    /**1-4位的验证正则表达式*/
    public static final String NUM_4_REGEX = "^\\d{1,4}$";
    /**固定的验证正则表达式*/
    public static final String PHONE = "^\\s*$|^0(([1,2]\\d)|([3-9]\\d{2}))[-]?\\d{7,8}$";
    /**固定的验证正则表达式*/
    public static final String EMAIL = "^\\s*$|^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$";
    /**验证IP地址*/
    public static final String IPADDR = "(?<=(\\b|\\D))(((\\d{1,2})|(1\\d{2})|(2[0-4]\\d)|(25[0-5]))\\.){3}((\\d{1,2})|(1\\d{2})|(2[0-4]\\d)|(25[0-5]))(?=(\\b|\\D))";
    /** 默认皮肤 */
    public static final String DEFALUTSKINNAME = "redmond";

    /** 流程中提交与返回按钮后台绑定返回值，用于表示数据流转成功 */
    public static final String FLOW_SUCCESS = "SUCCESS";
    /** 流程中提交与返回按钮后台绑定返回值，用于表示数据流转失败 */
    public static final String FLOW_FAILURE = null;
    /**GBZ 188标准名称*/
    public static final String GBZ188NAME ="职业健康监护标准（GBZ188）";
    /**体检平台对码表特殊处理的类包地址*/
    public static final String CODE_PROCESSPATH = "com/chis/ejb/chain/heth/";
    /**系统消息的标题最大长度*/
    public static final int MSG_MAX_LEN = 100;
    /**系统的错误提示*/
    public static final String BIG_ERROR = "软件出现未知错误，请联系相关人员！";
    /**我的消息打开脚本*/
    public static final String SYS_MSG_OPEN_SCRIPT = "top.win_TabMenu.OpenTabWin(\"m01\", '信息查看', 'webapp/system/tdMsgMainSearchList.faces', 'm03');";
    /**存于服务器的文件丢失错误*/
    public static final String DOWNLOAD_ERROR_FILENOTEXISTS = "该文件已不存在，不能下载！";
    
    /** ++虚拟路径访问目录名 */
    public static final String XNPATH_DIR = "/webFile";
    
    public static final String FAX_PATTERN = "^(\\d{3,4}-)?\\d{7,8}$";
    /**加密key*/
    public static final String ENCRY_KEY = "00d11fd69c844272";




}
