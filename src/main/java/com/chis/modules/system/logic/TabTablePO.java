package com.chis.modules.system.logic;

import com.chis.modules.system.utils.DefaultLazyDataModel;
import com.chis.modules.system.web.FacesBean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class TabTablePO<T> extends FacesBean implements Serializable  {
    /**
     * tab相关信息点
     */

    private String tabKey;
    private String tabName;

    /**
     * tab其他相关信息点
     */
    private T tabData;
    /**
     * 表格信息点
     */
    private DefaultLazyDataModel dataModel;
    /**
     * 查询条件信息点
     */
    private T queryData;

    /**
     * 当前页面已选择的
     */
    protected List<Object[]> selectEntitys = new ArrayList<Object[]>();

    public TabTablePO() {

    }

    public String getTabKey() {
        return tabKey;
    }

    public void setTabKey(String tabKey) {
        this.tabKey = tabKey;
    }

    public String getTabName() {
        return tabName;
    }

    public void setTabName(String tabName) {
        this.tabName = tabName;
    }

    public DefaultLazyDataModel getDataModel() {
        return dataModel;
    }

    public void setDataModel(DefaultLazyDataModel dataModel) {
        this.dataModel = dataModel;
    }

    public T getQueryData() {
        return queryData;
    }

    public void setQueryData(T queryData) {
        this.queryData = queryData;
    }

    public T getTabData() {
        return tabData;
    }

    public void setTabData(T tabData) {
        this.tabData = tabData;
    }

    public List<Object[]> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<Object[]> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }


}
