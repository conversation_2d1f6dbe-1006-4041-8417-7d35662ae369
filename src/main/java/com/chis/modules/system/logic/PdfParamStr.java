package com.chis.modules.system.logic;
/**
 * <p>类描述： PDF传参</p>
 * @ClassAuthor qrr,2018年4月21日,PdfParamStr
 */

public class PdfParamStr {
    private String ifSign;//是否签章【必传】
    private RptSignParamPO signParam;//签章参数， 签章为是【必传】 ==》值为 RptSignParamPO 实体转json字符串
    private String fr3Name;//报表文书编码

    private String sealServerAdr;//#卫生监督接口地址
    private String tokenId;//验证tokenId
    private String rid;//【暂时无用】
    private String filePath;//生成pdf路径地址

    public String getFr3Name() {
        return fr3Name;
    }

    public void setFr3Name(String fr3Name) {
        this.fr3Name = fr3Name;
    }

    public String getSealServerAdr() {
        return sealServerAdr;
    }

    public void setSealServerAdr(String sealServerAdr) {
        this.sealServerAdr = sealServerAdr;
    }

    public String getTokenId() {
        return tokenId;
    }

    public void setTokenId(String tokenId) {
        this.tokenId = tokenId;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getIfSign() {
        return ifSign;
    }

    public void setIfSign(String ifSign) {
        this.ifSign = ifSign;
    }

    public RptSignParamPO getSignParam() {
        return signParam;
    }

    public void setSignParam(RptSignParamPO signParam) {
        this.signParam = signParam;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
}
