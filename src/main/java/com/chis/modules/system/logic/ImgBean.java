package com.chis.modules.system.logic;

import java.io.Serializable;


/**
 * 图片跳转弹框查看辅助类
 * <AUTHOR>
 * @creatDate 2016-07-04
 */
public class ImgBean implements Serializable{
	private static final long serialVersionUID = 1L;
	private String path;
	private String desc;
	private Integer xh;
	
	public ImgBean() {
	}
	public String getPath() {
		return path;
	}
	public void setPath(String path) {
		this.path = path;
	}
	public String getDesc() {
		return desc;
	}
	public void setDesc(String desc) {
		this.desc = desc;
	}
	public Integer getXh() {
		return xh;
	}
	public void setXh(Integer xh) {
		this.xh = xh;
	}
}
