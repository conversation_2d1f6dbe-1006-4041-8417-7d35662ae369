package com.chis.modules.system.logic;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 菜单javaBean
 * <AUTHOR> 2015-03-03
 */
public class MenuVO implements Serializable {

	private static final long serialVersionUID = 6411885470589494518L;
	private String menuRid;
	private String menuLevelNo;
	private int menuLevel;
	private String menuCn;
	private String menuUri;
	private String bigIcon = "";
	private String icon = "";
	private int ifPop;
	private String menuEn;
	private List<MenuVO> childrenList = new ArrayList<MenuVO>(0);
	
	public MenuVO() {
	}
	
	public String getMenuRid() {
		return menuRid;
	}

	public void setMenuRid(String menuRid) {
		this.menuRid = menuRid;
	}

	public String getMenuCn() {
		return menuCn;
	}

	public void setMenuCn(String menuCn) {
		this.menuCn = menuCn;
	}

	public String getMenuUri() {
		return menuUri;
	}

	public void setMenuUri(String menuUri) {
		this.menuUri = menuUri;
	}

	public List<MenuVO> getChildrenList() {
		return childrenList;
	}

	public void setChildrenList(List<MenuVO> childrenList) {
		this.childrenList = childrenList;
	}

	public String getMenuLevelNo() {
		return menuLevelNo;
	}

	public void setMenuLevelNo(String menuLevelNo) {
		this.menuLevelNo = menuLevelNo;
	}

	public int getMenuLevel() {
		return menuLevel;
	}

	public void setMenuLevel(int menuLevel) {
		this.menuLevel = menuLevel;
	}

	public String getBigIcon() {
		return bigIcon;
	}

	public String getIcon() {
		return icon;
	}

	public int getIfPop() {
		return ifPop;
	}

	public void setBigIcon(String bigIcon) {
		this.bigIcon = bigIcon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public void setIfPop(int ifPop) {
		this.ifPop = ifPop;
	}

	public String getMenuEn() {
		return menuEn;
	}

	public void setMenuEn(String menuEn) {
		this.menuEn = menuEn;
	}
}
