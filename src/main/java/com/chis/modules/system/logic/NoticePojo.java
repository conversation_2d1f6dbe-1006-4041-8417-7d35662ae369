package com.chis.modules.system.logic;

import com.chis.modules.system.entity.TdPublishNoticeUnit;

import java.util.List;

/**
 *  <p>类描述：首页 通知信息的封装</p>
 * @ClassAuthor hsj 2021/11/30 11:18
 */
public class NoticePojo {
    /** 码表编码 */
    private String code;
    /** 名称*/
    private String codeName;
    /**通知对象*/
    private List<TdPublishNoticeUnit> tdPublishNoticeUnits;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCodeName() {
        return codeName;
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    public List<TdPublishNoticeUnit> getTdPublishNoticeUnits() {
        return tdPublishNoticeUnits;
    }

    public void setTdPublishNoticeUnits(List<TdPublishNoticeUnit> tdPublishNoticeUnits) {
        this.tdPublishNoticeUnits = tdPublishNoticeUnits;
    }
}
