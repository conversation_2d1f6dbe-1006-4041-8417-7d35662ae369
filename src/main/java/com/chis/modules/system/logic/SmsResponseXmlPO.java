package com.chis.modules.system.logic;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <returnsms>
 *     <returnstatus>Success</returnstatus>
 *     <message>ok</message>
 *     <remainpoint>183</remainpoint>
 *     <taskID>14839267</taskID>
 *     <successCounts>1</successCounts>
 * </returnsms>
 *
 */
@XStreamAlias("returnsms")
public class SmsResponseXmlPO {
    @XStreamAlias("returnstatus")
    private String returnstatus;
    @XStreamAlias("message")
    private String message;
    @XStreamAlias("remainpoint")
    private String remainpoint;
    @XStreamAlias("taskID")
    private String taskID;
    @XStreamAlias("successCounts")
    private String successCounts;

    public String getReturnstatus() {
        return returnstatus;
    }

    public void setReturnstatus(String returnstatus) {
        this.returnstatus = returnstatus;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getRemainpoint() {
        return remainpoint;
    }

    public void setRemainpoint(String remainpoint) {
        this.remainpoint = remainpoint;
    }

    public String getTaskID() {
        return taskID;
    }

    public void setTaskID(String taskID) {
        this.taskID = taskID;
    }

    public String getSuccessCounts() {
        return successCounts;
    }

    public void setSuccessCounts(String successCounts) {
        this.successCounts = successCounts;
    }
}
