package com.chis.modules.system.logic;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 第一列需要合并行的javabean
 * <AUTHOR> 2014-10-21
 */
public class OneRowSpanPO implements Serializable{
	
	private static final long serialVersionUID = 8373370431508558261L;
	/**第一列数据*/
	private Object fistCol;
	/**其他列数据集合*/
	private List<Object[]> objList = new ArrayList<Object[]>();
	
	public OneRowSpanPO() {
	}

	public OneRowSpanPO(Object fistCol) {
		this.fistCol = fistCol;
	}

	public Object getFistCol() {
		return fistCol;
	}

	public void setFistCol(Object fistCol) {
		this.fistCol = fistCol;
	}

	public List<Object[]> getObjList() {
		return objList;
	}

	public void setObjList(List<Object[]> objList) {
		this.objList = objList;
	}
}
