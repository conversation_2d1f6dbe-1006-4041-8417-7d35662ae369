package com.chis.modules.system.logic;

import java.io.Serializable;

public class Task<PERSON> implements Serializable {

	private static final long serialVersionUID = -8591139994699332618L;

	private String img;
	private String taskName;
	private String menuCN;
	private String url;
	private Integer counts;

	public TaskPO() {

	}

	public String getImg() {
		return img;
	}

	public void setImg(String img) {
		this.img = img;
	}

	public String getTaskName() {
		return taskName;
	}

	public void setTaskName(String taskName) {
		this.taskName = taskName;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getMenuCN() {
		return menuCN;
	}

	public void setMenuCN(String menuCN) {
		this.menuCN = menuCN;
	}

	public Integer getCounts() {
		return counts;
	}

	public void setCounts(Integer counts) {
		this.counts = counts;
	}

}
