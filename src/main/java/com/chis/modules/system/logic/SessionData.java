package com.chis.modules.system.logic;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import com.chis.modules.system.entity.TsUserInfo;

/**
 * session中的用户信息
 * <AUTHOR>
 * @修改人 xt
 * @修改时间 2014-10-10 新增皮肤属性
 */
public class SessionData implements Serializable {

	private static final long serialVersionUID = -6698668176947459466L;
	public static final String SESSION_DATA = "SESSION_DATA";

	private TsUserInfo user;
    private String menuId;
    private String skinName;//皮肤
    /**是否移动端访问*/
    private boolean wrap;
    private Map<String, String> buttonMap = new HashMap<String, String>();
    private Set<String> btnSet;//拥有的按钮编码集合
    /** 标识用户唯一标记 */
    private String userUuid;
    /**是否是资质培训 0：否 1：是*/
    private Integer isZZpx =0;
	/**是否贵州单点登录 0：否 1：是*/
    private Integer ifSSO =0;

	/** 缓存的Bean key Bean的Name subKey beanTag */
	private Map<String,Map<String, Object>> beanMap = new HashMap<>();
    
	public SessionData(TsUserInfo user) {
		this.user = user;
	}

	public TsUserInfo getUser() {
		return user;
	}

	public void setUser(TsUserInfo user) {
		this.user = user;
	}

    public String getMenuId() {
        return menuId;
    }

    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }

    public String getSkinName() {
        return skinName;
    }

    public void setSkinName(String skinName) {
        this.skinName = skinName;
    }

	public boolean isWrap() {
		return wrap;
	}

	public void setWrap(boolean wrap) {
		this.wrap = wrap;
	}

	public Map<String, String> getButtonMap() {
		return buttonMap;
	}

	public void setButtonMap(Map<String, String> buttonMap) {
		this.buttonMap = buttonMap;
	}

	public Set<String> getBtnSet() {
		return btnSet;
	}

	public void setBtnSet(Set<String> btnSet) {
		this.btnSet = btnSet;
	}

	public String getUserUuid() {
		return userUuid;
	}

	public void setUserUuid(String userUuid) {
		this.userUuid = userUuid;
	}

	public Integer getIsZZpx() {
		return isZZpx;
	}

	public void setIsZZpx(Integer isZZpx) {
		this.isZZpx = isZZpx;
	}

	public Integer getIfSSO() {
		return ifSSO;
	}

	public void setIfSSO(Integer ifSSO) {
		this.ifSSO = ifSSO;
	}

	public Map<String, Map<String, Object>> getBeanMap() {
		return beanMap;
	}

	public void setBeanMap(Map<String, Map<String, Object>> beanMap) {
		this.beanMap = beanMap;
	}
}
