package com.chis.modules.system.logic;

import java.util.ArrayList;
import java.util.List;

import com.chis.modules.system.entity.TsRole;
import com.chis.modules.system.entity.TsSimpleCode;
/**
 * <p>类描述：用户可选角色对象</p>
 * @ClassAuthor qrr,2020年7月31日,RoleTypePoNew
 * */
public class RoleTypePoNew {
	private TsSimpleCode roleType;
    private boolean ifSelected;
    private List<TsRole> rolePoList = new ArrayList<>();
    private List<String> itemSubs = new ArrayList<>();
	public TsSimpleCode getRoleType() {
		return roleType;
	}
	public void setRoleType(TsSimpleCode roleType) {
		this.roleType = roleType;
	}
	public boolean isIfSelected() {
		return ifSelected;
	}
	public void setIfSelected(boolean ifSelected) {
		this.ifSelected = ifSelected;
	}
	public List<TsRole> getRolePoList() {
		return rolePoList;
	}
	public void setRolePoList(List<TsRole> rolePoList) {
		this.rolePoList = rolePoList;
	}
	public List<String> getItemSubs() {
		return itemSubs;
	}
	public void setItemSubs(List<String> itemSubs) {
		this.itemSubs = itemSubs;
	}
}
