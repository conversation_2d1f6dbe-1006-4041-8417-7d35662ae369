package com.chis.modules.system.logic;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import javax.xml.bind.annotation.XmlType;

/**
 * 模块说明：解析xml格式的sql语句
 * 
 * <AUTHOR>
 * @createDate 2019年12月13日
 */
@XmlRootElement(name = "sqlsentences")
@XmlType(propOrder = { "description", "sqlList" })
public class SQLSentences implements Serializable {

	private static final long serialVersionUID = 1L;
	private String description;
	@XmlElement(name = "sqlsentence")
	private List<SQLSentence> sqlList;

	public SQLSentences() {
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@XmlTransient
	public List<SQLSentence> getSqlList() {
		return sqlList;
	}

	public void setSqlList(List<SQLSentence> sqlList) {
		this.sqlList = sqlList;
	}

}
