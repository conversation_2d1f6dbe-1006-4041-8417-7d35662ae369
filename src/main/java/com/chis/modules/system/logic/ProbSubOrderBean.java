package com.chis.modules.system.logic;

import java.io.Serializable;

/**
 * 问卷题目排序bean
 * 
 * <AUTHOR>
 * 
 */
public class ProbSubOrderBean implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 5503700390367910877L;
	private String showCode;
	public String getShowCode() {
		return showCode;
	}

	public void setShowCode(String showCode) {
		this.showCode = showCode;
	}

	/** 题目标题 */
	private String titleDesc;
	/**
	 * 题目编码
	 */
	private String qesCode;

	public ProbSubOrderBean() {
	}

	public ProbSubOrderBean(String showCode,String titleDesc, String qesCode) {
		this.showCode=showCode;
		this.titleDesc = titleDesc;
		this.qesCode = qesCode;
	}

	public String getTitleDesc() {
		return titleDesc;
	}

	public void setTitleDesc(String titleDesc) {
		this.titleDesc = titleDesc;
	}

	public String getQesCode() {
		return qesCode;
	}

	public void setQesCode(String qesCode) {
		this.qesCode = qesCode;
	}

}
