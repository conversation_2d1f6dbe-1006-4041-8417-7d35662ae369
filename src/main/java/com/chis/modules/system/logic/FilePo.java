package com.chis.modules.system.logic;

import java.io.Serializable;
import java.util.List;

import com.google.common.collect.Lists;

/**
 * 上传文件的po
 * 
 * <AUTHOR> 2014-10-21
 */
public class FilePo implements Serializable {

	private static final long serialVersionUID = 8373370431508558261L;
	private String fileName;
	private String filePath;
	private String imgPaths;
	private List<ImgBean> imgList = Lists.newArrayList();
	private Integer xh;

	public FilePo() {
	}
	
	

	public FilePo(String fileName, String filePath) {
		super();
		this.fileName = fileName;
		this.filePath = filePath;
	}



	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public Integer getXh() {
		return xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}

	public List<ImgBean> getImgList() {
		return imgList;
	}

	public void setImgList(List<ImgBean> imgList) {
		this.imgList = imgList;
	}

	public String getImgPaths() {
		return imgPaths;
	}

	public void setImgPaths(String imgPaths) {
		this.imgPaths = imgPaths;
	}

}
