package com.chis.modules.system.logic;

import java.util.Date;

/**
 * 模块说明：查询全文检索对象
 * 
 * <AUTHOR>
 * @createDate 2016年10月28日
 */
public class LucenePojo {
	/** 业务主键Id */
	private String rid;
	/** 附件名称 */
	private String annexName;
	/** 附件地址 */
	private String annexPath;
	/** 标题 */
	private String kleTitle;
	/**关键字*/
	private String keyWords;
	/** 类型*/
	private String infoType;
	/**图片类别*/
	private String picType;
	/** 修改日期 */
	private Date modifyDate;
	/** 地区编码 */
	private String zoneGb;
	/** 所属单位性质 */
	private String sortCodes;
	/** 单位Id */
	private String unitId;
	private String state;
	private String fileId;
	
	

	public String getRid() {
		return rid;
	}

	public void setRid(String rid) {
		this.rid = rid;
	}

	public String getAnnexName() {
		return annexName;
	}

	public void setAnnexName(String annexName) {
		this.annexName = annexName;
	}

	public String getAnnexPath() {
		return annexPath;
	}

	public void setAnnexPath(String annexPath) {
		this.annexPath = annexPath;
	}

	public String getZoneGb() {
		return zoneGb;
	}

	public void setZoneGb(String zoneGb) {
		this.zoneGb = zoneGb;
	}

	public String getSortCodes() {
		return sortCodes;
	}

	public void setSortCodes(String sortCodes) {
		this.sortCodes = sortCodes;
	}

	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

	public String getKleTitle() {
		return kleTitle;
	}

	public void setKleTitle(String kleTitle) {
		this.kleTitle = kleTitle;
	}

	public String getInfoType() {
		return infoType;
	}

	public void setInfoType(String infoType) {
		this.infoType = infoType;
	}

	public String getPicType() {
		return picType;
	}

	public void setPicType(String picType) {
		this.picType = picType;
	}

	public String getKeyWords() {
		return keyWords;
	}

	public void setKeyWords(String keyWords) {
		this.keyWords = keyWords;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getFileId() {
		return fileId;
	}

	public void setFileId(String fileId) {
		this.fileId = fileId;
	}

}
