package com.chis.modules.system.logic;

import com.chis.modules.system.entity.TsZone;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  <p>类描述：</p>
 * @ClassAuthor hsj 2022-09-20 15:43
 */
public class ZoneUnitVO {
    //机构rid
    private Integer rid;
    //地区编码（当前地区的下一级地区）
    private String zoneGb;
    //名称(rid 为空时 为地区名称，rid不为空时 业务表名称)
    private String name;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getZoneGb() {
        return zoneGb;
    }

    public void setZoneGb(String zoneGb) {
        this.zoneGb = zoneGb;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static List<ZoneUnitVO> getZoneUnitVO(List<Object[]> list , TsZone zone){
        List<ZoneUnitVO> zoneUnitVOList = new ArrayList<>();
        if(CollectionUtils.isEmpty(list)){
            return zoneUnitVOList;
        }
        Map<String, ZoneUnitVO> map = new HashMap<String, ZoneUnitVO>();
        //第一级地区
        ZoneUnitVO zoneUnitVO = new ZoneUnitVO();
        zoneUnitVO.setName(zone.getZoneName());
        zoneUnitVO.setZoneGb(zone.getZoneGb());
        zoneUnitVOList.add(zoneUnitVO);
        map.put(zone.getZoneGb(), zoneUnitVO);
        for (Object[] objects : list) {
            //地区编码
            String zoneGb = objects[2] == null ? null : objects[2].toString();
            String unitName = objects[1] == null ? null : objects[1].toString();
            String zoneName = objects[3] == null ? null : objects[3].toString();
            Integer rid = objects[0] == null ? null : Integer.valueOf(objects[0].toString());
            if(map.containsKey(zoneGb)){
                zoneUnitVO = new ZoneUnitVO();
                zoneUnitVO.setName(unitName);
                zoneUnitVO.setZoneGb(zoneGb);
                zoneUnitVO.setRid(rid);
                zoneUnitVOList.add(zoneUnitVO);
            }else {
                zoneUnitVO = new ZoneUnitVO();
                zoneUnitVO.setName(zoneName);
                zoneUnitVO.setZoneGb(zoneGb);
                zoneUnitVOList.add(zoneUnitVO);
                map.put(zoneGb,zoneUnitVO);

                zoneUnitVO = new ZoneUnitVO();
                zoneUnitVO.setName(unitName);
                zoneUnitVO.setZoneGb(zoneGb);
                zoneUnitVO.setRid(rid);
                zoneUnitVOList.add(zoneUnitVO);
            }
        }
        return zoneUnitVOList;
    }

}
