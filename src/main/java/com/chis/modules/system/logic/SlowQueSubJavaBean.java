package com.chis.modules.system.logic;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 答案数统计子表
 * 
 * <AUTHOR>
 */
public class SlowQueSubJavaBean implements Serializable {

	/** 选项ID */
	private Integer optionId;
	/** 选项描述 */
	private String optionDesc;
	/** 答题人数 */
	private BigDecimal replyCnt;
	/** 比例 */
	private BigDecimal replyPat;
	/** 答题总数 */
	private BigDecimal replySum;

	public Integer getOptionId() {
		return optionId;
	}

	public void setOptionId(Integer optionId) {
		this.optionId = optionId;
	}

	public String getOptionDesc() {
		return optionDesc;
	}

	public void setOptionDesc(String optionDesc) {
		this.optionDesc = optionDesc;
	}

	public BigDecimal getReplyCnt() {
		return replyCnt;
	}

	public void setReplyCnt(BigDecimal replyCnt) {
		this.replyCnt = replyCnt;
	}

	public BigDecimal getReplyPat() {
		return replyPat;
	}

	public void setReplyPat(BigDecimal replyPat) {
		this.replyPat = replyPat;
	}

	public BigDecimal getReplySum() {
		return replySum;
	}

	public void setReplySum(BigDecimal replySum) {
		this.replySum = replySum;
	}

}
