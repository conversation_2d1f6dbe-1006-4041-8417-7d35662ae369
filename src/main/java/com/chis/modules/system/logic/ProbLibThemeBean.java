package com.chis.modules.system.logic;

import java.io.Serializable;
/**
 * 问卷背景图
 * <AUTHOR>
 *
 */
public class ProbLibThemeBean implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 5279874362758387778L;
	private String value;
	//缩略图地址
	private String thumbPath;
	//是否被选中
	private boolean isSelected;

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getThumbPath() {
		return thumbPath;
	}

	public void setThumbPath(String thumbPath) {
		this.thumbPath = thumbPath;
	}

	public boolean isSelected() {
		return isSelected;
	}

	public void setSelected(boolean isSelected) {
		this.isSelected = isSelected;
	}

}
