package com.chis.modules.system.logic;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.chis.modules.system.entity.TsUserInfo;

/**
 * 退回节点的信息
 * <AUTHOR> 2015-03-03
 */
public class BackNodeVO implements Serializable {

	private static final long serialVersionUID = 6411885470589494518L;
	private String activitiNodeName;
	private String activitiNodeKey;
	private List<TsUserInfo> assigeeList = new ArrayList<TsUserInfo>(0);
	
	public BackNodeVO() {
	}
	
	public BackNodeVO(String activitiNodeName, String activitiNodeKey) {
		this.activitiNodeName = activitiNodeName;
		this.activitiNodeKey = activitiNodeKey;
	}
	public String getActivitiNodeName() {
		return activitiNodeName;
	}
	public void setActivitiNodeName(String activitiNodeName) {
		this.activitiNodeName = activitiNodeName;
	}
	public String getActivitiNodeKey() {
		return activitiNodeKey;
	}
	public void setActivitiNodeKey(String activitiNodeKey) {
		this.activitiNodeKey = activitiNodeKey;
	}
	public List<TsUserInfo> getAssigeeList() {
		return assigeeList;
	}
	public void setAssigeeList(List<TsUserInfo> assigeeList) {
		this.assigeeList = assigeeList;
	}
}
