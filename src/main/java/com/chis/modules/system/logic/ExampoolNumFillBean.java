package com.chis.modules.system.logic;

import java.util.List;

/***
 * 数字填空题与整数填空题辅助类
 * 
 * <AUTHOR>
 *  20160524
 * */
public class ExampoolNumFillBean {
	/**填空顺序**/
	private Integer num;
	/**触发危险因素最小值**/
	private Double minVal;
	/**触发危险因素最大值**/
	private Double maxVal;
	/**题目编码**/
	private String code;
	/**危险因素编码,动作;危险因素编码,动作**/
	private String factorAndAction;
	/**验证最小值**/
	private Double minVerifyVal;
	/**验证最大值**/
	private Double maxVerifyVal;
	
	
	public Integer getNum() {
		return num;
	}
	public void setNum(Integer num) {
		this.num = num;
	}
	public Double getMinVal() {
		return minVal;
	}
	public void setMinVal(Double minVal) {
		this.minVal = minVal;
	}
	public Double getMaxVal() {
		return maxVal;
	}
	public void setMaxVal(Double maxVal) {
		this.maxVal = maxVal;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getFactorAndAction() {
		return factorAndAction;
	}
	public void setFactorAndAction(String factorAndAction) {
		this.factorAndAction = factorAndAction;
	}
	public Double getMinVerifyVal() {
		return minVerifyVal;
	}
	public void setMinVerifyVal(Double minVerifyVal) {
		this.minVerifyVal = minVerifyVal;
	}
	public Double getMaxVerifyVal() {
		return maxVerifyVal;
	}
	public void setMaxVerifyVal(Double maxVerifyVal) {
		this.maxVerifyVal = maxVerifyVal;
	}
	
	
}
