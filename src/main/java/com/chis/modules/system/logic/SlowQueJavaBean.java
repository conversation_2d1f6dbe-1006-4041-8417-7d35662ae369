package com.chis.modules.system.logic;

import java.io.Serializable;
import java.util.List;

/**
 * 问卷答案数统计javaBean
 * 
 * <AUTHOR>
 */
public class SlowQueJavaBean implements Serializable {

	/** 问卷ID */
	private Integer queId;
	/** 题目ID */
	private Integer topicId;
	/** 题目类型 5 标题 0 其它 */
	private Integer topicType;
	/** 图表json内容 */
	private String showCode;
	/** 题目描述 */
	private String topicDesc;
	/** 图表json内容 */
	private String charJson;
	/** 选项集合 */
	private List<SlowQueSubJavaBean> optionList;
	
	public SlowQueJavaBean() {}
	
	public SlowQueJavaBean(Integer queId, Integer topicId, String topicDesc) {
		this.queId = queId;
		this.topicId = topicId;
		this.topicDesc = topicDesc;
	}

	public Integer getQueId() {
		return queId;
	}

	public void setQueId(Integer queId) {
		this.queId = queId;
	}

	public Integer getTopicId() {
		return topicId;
	}

	public void setTopicId(Integer topicId) {
		this.topicId = topicId;
	}

	public String getTopicDesc() {
		return topicDesc;
	}

	public void setTopicDesc(String topicDesc) {
		this.topicDesc = topicDesc;
	}

	public List<SlowQueSubJavaBean> getOptionList() {
		return optionList;
	}

	public void setOptionList(List<SlowQueSubJavaBean> optionList) {
		this.optionList = optionList;
	}

	public String getCharJson() {
		return charJson;
	}

	public void setCharJson(String charJson) {
		this.charJson = charJson;
	}

	public Integer getTopicType() {
		return topicType;
	}

	public void setTopicType(Integer topicType) {
		this.topicType = topicType;
	}

	public String getShowCode() {
		return showCode;
	}

	public void setShowCode(String showCode) {
		this.showCode = showCode;
	}

}
