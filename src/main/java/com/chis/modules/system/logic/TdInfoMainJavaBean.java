package com.chis.modules.system.logic;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 寻呼消息javabean 遍历人员信息
 * 
 * <AUTHOR>
 * 
 */
public class TdInfoMainJavaBean implements Serializable {

	private static final long serialVersionUID = -1098228610318700871L;
	private String userRid;// 人员id
	private String userName;// 人员名称
	private String officeRid;// 科室id
	private String officeName;// 科室名称
	private List<TdInfoMainJavaBean> userInfo = new ArrayList<>();// 人员信息
	private List<String> selUserRids = new ArrayList<>();// 选中的人员信息
	private Boolean select;// 是否选中
	private String unitRid;// 科室rid
	private String unitName;// 科室名称

	public String getOfficeRid() {
		return officeRid;
	}

	public void setOfficeRid(String officeRid) {
		this.officeRid = officeRid;
	}

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public List<TdInfoMainJavaBean> getUserInfo() {
		return userInfo;
	}

	public void setUserInfo(List<TdInfoMainJavaBean> userInfo) {
		this.userInfo = userInfo;
	}

	public String getUserRid() {
		return userRid;
	}

	public void setUserRid(String userRid) {
		this.userRid = userRid;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public List<String> getSelUserRids() {
		return selUserRids;
	}

	public void setSelUserRids(List<String> selUserRids) {
		this.selUserRids = selUserRids;
	}

	public Boolean getSelect() {
		return select;
	}

	public void setSelect(Boolean select) {
		this.select = select;
	}

	public String getUnitRid() {
		return unitRid;
	}

	public void setUnitRid(String unitRid) {
		this.unitRid = unitRid;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

}
