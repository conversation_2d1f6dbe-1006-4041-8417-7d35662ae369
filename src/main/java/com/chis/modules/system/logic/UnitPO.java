package com.chis.modules.system.logic;

import java.io.Serializable;

/**
 * 单位的pojo
 */
public class UnitPO implements Serializable{
    /**地区名称*/
    private String zoneName;
    /**单位名称*/
    private String unitName;
    /**单位ID*/
    private Integer unitId;

    public UnitPO() {
    }

    public UnitPO(Integer unitId) {
        this.unitId = unitId;
    }

    public UnitPO(String zoneName, String unitName, Integer unitId) {
        this.zoneName = zoneName;
        this.unitName = unitName;
        this.unitId = unitId;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        UnitPO unitPO = (UnitPO) o;

        if (unitId != null ? !unitId.equals(unitPO.unitId) : unitPO.unitId != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return unitId != null ? unitId.hashCode() : 0;
    }
}
