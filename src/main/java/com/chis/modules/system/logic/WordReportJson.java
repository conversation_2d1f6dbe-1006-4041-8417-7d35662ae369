package com.chis.modules.system.logic;

import java.io.Serializable;
/**
 * @Description: 调用生成Word报告的接口 请求JSON实体对象
 *
 * @ClassAuthor pw,2022年04月24日,WordReportJson
 */
public class WordReportJson implements Serializable {
    private static final long serialVersionUID = 980234341736172998L;
    private Integer rid;
    private String rptCode;
    private Integer ifPdf;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getRptCode() {
        return rptCode;
    }

    public void setRptCode(String rptCode) {
        this.rptCode = rptCode;
    }

    public Integer getIfPdf() {
        return ifPdf;
    }

    public void setIfPdf(Integer ifPdf) {
        this.ifPdf = ifPdf;
    }
}
