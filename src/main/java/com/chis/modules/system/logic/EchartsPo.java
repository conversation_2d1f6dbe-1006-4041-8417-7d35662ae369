package com.chis.modules.system.logic;

import java.util.List;

public class EchartsPo {

	private String[] legends;
	private String[] xAxisData;
	private List<String[]> datas;
	
	
	public EchartsPo() {
		super();
		// TODO Auto-generated constructor stub
	}
	public EchartsPo(String[] legends, String[] xAxisData, List<String[]> datas) {
		super();
		this.legends = legends;
		this.xAxisData = xAxisData;
		this.datas = datas;
	}
	public String[] getLegends() {
		return legends;
	}
	public void setLegends(String[] legends) {
		this.legends = legends;
	}
	public String[] getxAxisData() {
		return xAxisData;
	}
	public void setxAxisData(String[] xAxisData) {
		this.xAxisData = xAxisData;
	}
	public List<String[]> getDatas() {
		return datas;
	}
	public void setDatas(List<String[]> datas) {
		this.datas = datas;
	}
	
	
}
