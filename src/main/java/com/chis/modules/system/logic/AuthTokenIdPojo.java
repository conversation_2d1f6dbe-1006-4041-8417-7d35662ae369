package com.chis.modules.system.logic;


import org.apache.ibatis.type.Alias;

/**
 * <p>类描述：验证tokenId返回pojo</p>
 * @ClassAuthor qrr,2018年4月21日,AuthTokenIdPojo
 */
@Alias("AuthTokenIdPO")
public class AuthTokenIdPojo extends SysReturnPojo {

	/** 验证信息tokenId */
	private String tokenId;
	/** 有效时间，单位小时， 如24小时 */
	private String validateTime;

	public String getTokenId() {
		return tokenId;
	}

	public void setTokenId(String tokenId) {
		this.tokenId = tokenId;
	}

	public String getValidateTime() {
		return validateTime;
	}

	public void setValidateTime(String validateTime) {
		this.validateTime = validateTime;
	}

}
