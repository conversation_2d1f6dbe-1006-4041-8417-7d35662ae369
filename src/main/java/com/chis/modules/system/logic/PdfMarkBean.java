package com.chis.modules.system.logic;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 * @createDate 2016年8月10日
 */
public class PdfMarkBean implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 7058986791456907873L;
	private String content;
	private float x;
	private float y;
	private float z;

	public PdfMarkBean() {
		super();
		// TODO Auto-generated constructor stub
	}

	public PdfMarkBean(String content, float x, float y, float z) {
		super();
		this.content = content;
		this.x = x;
		this.y = y;
		this.z = z;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public float getX() {
		return x;
	}

	public void setX(float x) {
		this.x = x;
	}

	public float getY() {
		return y;
	}

	public void setY(float y) {
		this.y = y;
	}

	public float getZ() {
		return z;
	}

	public void setZ(float z) {
		this.z = z;
	}

}
