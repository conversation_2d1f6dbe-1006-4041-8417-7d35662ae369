package com.chis.modules.system.logic;

import java.util.List;

/**
 * 表头对象
 * <AUTHOR>
 * 
 * <p>类描述：添加stringVlue：数字型数据为字符串</p>
 * @ClassAuthor qrr,2018年3月5日,TopTitlePo
 *
 */
public class TopTitlePo {

	/**表头名称*/
	public String titleName;
	/**0数据列 1文字列*/
	public Integer colType;
	/**数据INDEX*/
	public Integer dataIndex;
	/**是否统计图数据*/
	public Boolean isEcharts = false;
	/**colSpan*/
	public Integer colSpan=1;
	/**rowSpan*/
	public Integer rowSpan=1;
	/**传输表头信息集合，用于导出使用*/
	public List<TopTitlePo> topTitles;
	/**业务主键*/
	public Integer bussinessId;
	/**数字型数据*/
	public Integer intVlue;
	/**临时使用*/
	public String key;
	/**数字型数据*/
	public String stringVlue;
	
	public TopTitlePo(Integer colType) {
		this.colType = colType;
	}
	
	public TopTitlePo(String titleName, Integer colSpan, Integer rowSpan) {
		this.titleName = titleName;
		this.colSpan = colSpan;
		this.rowSpan = rowSpan;
	}

	public TopTitlePo() {
		// TODO Auto-generated constructor stub
	}
	
	public Integer getDataIndex() {
		return dataIndex;
	}
	public void setDataIndex(Integer dataIndex) {
		this.dataIndex = dataIndex;
	}
	public String getTitleName() {
		return titleName;
	}
	public void setTitleName(String titleName) {
		this.titleName = titleName;
	}
	public Integer getColType() {
		return colType;
	}
	public void setColType(Integer colType) {
		this.colType = colType;
	}

	public Boolean getIsEcharts() {
		return isEcharts;
	}

	public void setIsEcharts(Boolean isEcharts) {
		this.isEcharts = isEcharts;
	}

	public List<TopTitlePo> getTopTitles() {
		return topTitles;
	}

	public void setTopTitles(List<TopTitlePo> topTitles) {
		this.topTitles = topTitles;
	}

	public Integer getColSpan() {
		return colSpan;
	}

	public void setColSpan(Integer colSpan) {
		this.colSpan = colSpan;
	}

	public Integer getRowSpan() {
		return rowSpan;
	}

	public void setRowSpan(Integer rowSpan) {
		this.rowSpan = rowSpan;
	}

	public Integer getBussinessId() {
		return bussinessId;
	}

	public void setBussinessId(Integer bussinessId) {
		this.bussinessId = bussinessId;
	}

	public Integer getIntVlue() {
		return intVlue;
	}

	public void setIntVlue(Integer intVlue) {
		this.intVlue = intVlue;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getStringVlue() {
		return stringVlue;
	}

	public void setStringVlue(String stringVlue) {
		this.stringVlue = stringVlue;
	}

}
