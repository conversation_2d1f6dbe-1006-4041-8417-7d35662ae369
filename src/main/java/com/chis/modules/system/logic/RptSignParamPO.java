package com.chis.modules.system.logic;

import com.alibaba.fastjson.JSON;

import java.io.Serializable;

/**
 * <p>类描述：签章参数</p>
 * @ClassAuthor qrr,2018年4月21日,RptSignParamPO
 * 
 */
public class RptSignParamPO implements Serializable {

	// frpt.SealsignParam="{'appId':'67f6c99b74484c2b8bcb35473850cdd7','channelId':'CHN_69120806E7D44CE1',
	// 'orgCode':'1230012311','ruleNum':'354D44C55206A68E','signPath':'lctest.isignet.cn','signPort':13001}";

	private static final long serialVersionUID = 6411885470589494518L;
	private String appId;// 签章appId
	private String channelId;// 签章channelId
	private String orgCode;// 签章组织机构编码
	private String ruleNum;// 签章规则号
	private String signPath;// 签章路径
	private String signPort;// 签章端口号

	public static void main(String[] args) {
		
		RptSignParamPO p = new RptSignParamPO();
		p.setAppId("67f6c99b74484c2b8bcb35473850cdd7");
		System.err.println(JSON.toJSONString(p));
	}
	
	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getChannelId() {
		return channelId;
	}

	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getRuleNum() {
		return ruleNum;
	}

	public void setRuleNum(String ruleNum) {
		this.ruleNum = ruleNum;
	}

	public String getSignPath() {
		return signPath;
	}

	public void setSignPath(String signPath) {
		this.signPath = signPath;
	}

	public String getSignPort() {
		return signPort;
	}

	public void setSignPort(String signPort) {
		this.signPort = signPort;
	}
}
