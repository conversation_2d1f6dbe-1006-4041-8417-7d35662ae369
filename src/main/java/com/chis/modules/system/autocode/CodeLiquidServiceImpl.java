package com.chis.modules.system.autocode;

import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.modules.system.entity.TsCodeRule;
import com.chis.modules.system.entity.TsSysIds;
import com.chis.modules.system.interfaces.IAutoCodeService;

/**
 * 规则的编码：表名_字段名
 * 此实现类的规则：只是流水号
 * 前缀为 ##
 * 存的是下一次要取的
 */
@Component(value="com.chis.ejb.service.autocode.impl.CodeLiquidServiceImpl")
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class CodeLiquidServiceImpl implements IAutoCodeService {

    @Override
    public String buildCode(TsCodeRule rule, String pfx, EntityManager em) {
        StringBuilder sb = new StringBuilder("  SELECT T.RID, T.NUM FROM TS_SYS_IDS T WHERE T.PFXID = '##' AND T.RULE_ID = '");
        sb.append(rule.getRid()).append("' FOR UPDATE ");
        List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();

        String rtnString = null;

        if(null != list && list.size() > 0) {
            Object[] o = list.get(0);
            rtnString = StringUtils.leftPad(o[1].toString(), rule.getCodeLenth(), "0");

            sb = new StringBuilder(" UPDATE TS_SYS_IDS SET NUM = ");
            sb.append(Integer.parseInt(rtnString)+1).append(" WHERE RID=").append(o[0].toString());

            em.createNativeQuery(sb.toString()).executeUpdate();

        }else {
            rtnString = StringUtils.leftPad("1", rule.getCodeLenth(), "0");

            TsSysIds t = new TsSysIds();
            t.setTsCodeRule(rule);
            t.setPfxid("##");
            t.setNum(2);
            t.setCreateManid(1);
            t.setCreateDate(new Date());

            em.persist(t);
            em.flush();
        }
        return rtnString;
    }

    @Override
    public String desciption() {
        return "纯流水号";
    }

    @Override
    public String buildTestCode(TsCodeRule rule, String pfx) {
        return StringUtils.leftPad("1", rule.getCodeLenth(), "0");
    }
}
