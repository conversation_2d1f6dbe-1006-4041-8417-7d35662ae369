package com.chis.modules.system.autocode;

import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsCodeRule;
import com.chis.modules.system.entity.TsSysIds;
import com.chis.modules.system.interfaces.IAutoCodeService;

/**
 * 规则的编码：表名_字段名
 */
@Component(value="com.chis.ejb.service.autocode.impl.CodeStaticYearServiceImpl")
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class CodeStaticYearServiceImpl implements IAutoCodeService {

    @Override
    public String buildCode(TsCodeRule rule, String pfx, EntityManager em) {
        String newPfx = StringUtils.convertNull2Empty(rule.getPfx()) + StringUtils.convertNull2Empty(rule.getSplitStr()) +  DateUtils.getYear();

        StringBuilder sb = new StringBuilder("  SELECT T.RID, T.NUM FROM TS_SYS_IDS T WHERE T.PFXID = '");
        sb.append(newPfx).append("' AND T.RULE_ID = '").append(rule.getRid()).append("' FOR UPDATE ");
        List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();

        String rtnString = null;

        if(null != list && list.size() > 0) {
            Object[] o = list.get(0);
            rtnString = newPfx + StringUtils.leftPad(o[1].toString(), rule.getCodeLenth(), "0") + StringUtils.convertNull2Empty(rule.getSuf());

            sb = new StringBuilder(" UPDATE TS_SYS_IDS SET NUM = ");
            sb.append(Integer.parseInt(o[1].toString())+1).append(" WHERE RID=").append(o[0].toString());

            em.createNativeQuery(sb.toString()).executeUpdate();

        }else {
            rtnString = newPfx + StringUtils.leftPad("1", rule.getCodeLenth(), "0") + StringUtils.convertNull2Empty(rule.getSuf());

            TsSysIds t = new TsSysIds();
            t.setTsCodeRule(rule);
            t.setPfxid(newPfx);
            t.setNum(2);
            t.setCreateManid(1);
            t.setCreateDate(new Date());

            em.persist(t);
            em.flush();
        }
        return rtnString;
    }

    @Override
    public String desciption() {
        return "固定前缀+分隔符+年份+流水号+后缀";
    }

    @Override
    public String buildTestCode(TsCodeRule rule, String pfx) {
        StringBuilder sb = new StringBuilder();
        sb.append(StringUtils.convertNull2Empty(rule.getPfx())).append(StringUtils.convertNull2Empty(rule.getSplitStr()));
        sb.append(DateUtils.getYear()).append(StringUtils.leftPad("1", rule.getCodeLenth(), "0"));
        sb.append(rule.getSuf());
        return sb.toString();
    }
}
