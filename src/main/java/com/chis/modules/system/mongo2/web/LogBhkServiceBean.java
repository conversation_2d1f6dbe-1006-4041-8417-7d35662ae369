package com.chis.modules.system.mongo2.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUnitAttr;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.mongo.service.MongoService;
import com.chis.modules.system.mongo2.entity.bhkdataservice.LogBhkServiceMain;
import com.chis.modules.system.mongo2.entity.bhkdataservice.LogBhkServiceText;
import com.chis.modules.system.mongo2.utils.MongoFacesEditBean2;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.XmlFormatter;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.primefaces.context.RequestContext;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/***
 * @Description : 日志查询（新）
 * @ClassAuthor : mxp
 * @Date : 2019/5/20 13:21
 */
public abstract class LogBhkServiceBean<T> extends MongoFacesEditBean2<T> {

    /**
     * 是否是超管
     */
    protected boolean ifAdmin = Boolean.TRUE;
    /**
     * 查询条件：地区名称
     */
    protected String searchZoneName;
    /**
     * 查询条件：地区编码
     */
    protected String searchZoneCode;
    /**
     * 查询条件：地区级别
     */
    protected String searchZoneType;
    /**
     * 地区集合
     */
    protected List<TsZone> zoneList;
    /**
     * 单位下拉
     */
    protected Map<String, String> searchUnitMap = new HashMap<String, String>(0);
    /**
     * 上传标记 0 失败 1 成功
     */
    protected List<String> uploadTags;
    /**
     * 处理标记 0 未处理 1 已处理
     */
    protected List<String> dealStates;

    /**
     * 处理状态
     */
    protected String dealState;
    // 上传机构编码
    protected String unitCode;
    // 弹出框内容
    protected String showContent;
    private LogBhkServiceMain entityMain;
    private LogBhkServiceText mainText;
    private LogBhkServiceText subText;

    protected StreamedContent downloadFile;

    protected Map<String, String> uploadZoneMap = new HashMap<String, String>(0);
    protected Map<String, String> uploadUnitMap = new HashMap<String, String>(0);

    protected SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    protected CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    protected SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    protected MongoService mongoService = SpringContextHolder.getBean(MongoService.class);

    public void init() {
        uploadTags = new ArrayList<>(Collections.singletonList("0"));
        dealStates = new ArrayList<>(Collections.singletonList("0"));


        TsUserInfo tsUserInfo = this.sessionData.getUser();
        if (!tsUserInfo.getUserNo().equals(Constants.ADMIN)) {
            ifAdmin = Boolean.FALSE;
        }
        this.searchZoneCode = tsUserInfo.getTsUnit().getTsZone().getZoneCode();
        this.searchZoneName = tsUserInfo.getTsUnit().getTsZone().getZoneName();
        this.searchZoneType = tsUserInfo.getTsUnit().getTsZone().getZoneType().toString();

        // 地区初始化
        if (null == this.zoneList || this.zoneList.size() <= 0) {
            this.zoneList = this.commService.findZoneListNew(this.ifAdmin, searchZoneCode, null, "6");
            String fullName;
            for (TsZone tsZone : zoneList) {
                fullName = StringUtils.trimToEmpty(tsZone.getFullName());
                fullName = fullName.replaceFirst(".*?_", "");
                uploadZoneMap.put(tsZone.getZoneGb(), fullName);
            }
        }

        // 初始化单位
        this.searchUnitMap = this.filterUnit(this.searchZoneCode, this.searchZoneType);

        List<Object[]> list = this.systemModuleService.findSrvorgByZoneCode(true, true, null, null);
        if (list != null && list.size() > 0) {
            for (Object[] strings : list) {
                if (strings[0] != null && strings[1] != null) {
                    uploadUnitMap.put(strings[0].toString(), strings[1].toString());
                }
            }
        }
    }

    /**
     * @Description : 根据地区刷单位
     * @MethodAuthor: anjing
     * @Date : 2019/5/8 10:24
     **/
    protected Map<String, String> filterUnit(String zoneCode, String zoneType) {
        TsUserInfo user = this.sessionData.getUser();
        Map<String, String> map = new LinkedHashMap<String, String>();
        Set<TsUnitAttr> tsUnitAttrs = user.getTsUnit().getTsUnitAttrs();
        boolean flag = false;
        if (tsUnitAttrs != null) {
            for (TsUnitAttr tsUnitAttr : tsUnitAttrs) {
                if (tsUnitAttr.getTsBsSort() != null && tsUnitAttr.getTsBsSort().getSortCode().equals("2001")) {
                    flag = true;
                }
            }
        }

        List<Object[]> list = this.systemModuleService.findSrvorgByZoneCode(this.ifAdmin, flag, user.getTsUnit().getRid(), zoneCode);
        if (list != null && list.size() > 0) {
            for (Object[] strings : list) {
                if (strings[0] != null && strings[1] != null) {
                    map.put(strings[1].toString(), strings[0].toString());
                    if (!flag && !this.ifAdmin) {
                        this.unitCode = strings[0].toString();
                    }
                }
            }
        }
        return map;
    }


    /***
     * <p>方法描述: 显示信息</p>
     *
     * @MethodAuthor mxp, 2018/12/19,showContentAction
     */
    public void showContentAction() {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(showContent)) {
            String[] split = showContent.split("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>");
            for (int i = 0; i < split.length; i++) {
                sb.append(XmlFormatter.format(split[i]));
            }
        }
        showContent = sb.toString();
        RequestContext.getCurrentInstance().execute("PF('RespTextDialog').show()");
    }

    /***
     * @Description : 编辑
     * @MethodAuthor: mxp
     * @Date : 2019/5/20 14:41
     */
    public void modInit(String mainId, String subId) {
        // 主表
        entityMain = mongoService.findOne(mainId, LogBhkServiceMain.class);
        if (entityMain == null) {
            entityMain = new LogBhkServiceMain();
        } else {
            // 主报文
            mainText = mongoService.findOne(mainId, LogBhkServiceText.class);
            if (mainText == null) {
                mainText = new LogBhkServiceText();
            }
        }

        // 子报文
        subText = mongoService.findOne(subId, LogBhkServiceText.class);
        if (subText == null) {
            subText = new LogBhkServiceText();
        }
    }

    public abstract DefaultStreamedContent export();


    /***
     * @Description : 导出
     * @MethodAuthor: mxp
     * @Date : 2019/5/20 16:47
     */
    protected DefaultStreamedContent export(String fileName, HSSFWorkbook wb) {
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("showStatus();");
        ByteArrayOutputStream baos = null;
        try {
            fileName = new String((fileName + ".xlsx").getBytes("UTF-8"), "ISO-8859-1");
            baos = new ByteArrayOutputStream();
            wb.write(baos);
            baos.flush();
            byte[] aa = baos.toByteArray();
            context.execute("hideStatus();");
            return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
        } catch (IOException e) {
            JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
        } finally {
            if (baos != null) {
                try {
                    baos.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        context.execute("hideStatus();");
        return null;

    }

    protected abstract boolean initExportData(HSSFWorkbook wb) throws Exception;


    protected HSSFWorkbook initTitle(String fileName, int lastCol) {
        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet();
        HSSFRow row = sheet.createRow(0);
        HSSFCell cell = row.createCell(0);

        HSSFFont font = wb.createFont();
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        HSSFCellStyle cellStyle = wb.createCellStyle();
        // 文字居中
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        cellStyle.setFont(font);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(fileName);
        CellRangeAddress region = new CellRangeAddress(0, 0, 0, lastCol);
        sheet.addMergedRegion(region);
        //初始化第二行
        initTitle2(wb);
        return wb;
    }


    protected abstract void initTitle2(HSSFWorkbook wb);


    public String getShowContent() {
        return showContent;
    }

    public void setShowContent(String showContent) {
        this.showContent = showContent;
    }

    public boolean isIfAdmin() {
        return ifAdmin;
    }

    public void setIfAdmin(boolean ifAdmin) {
        this.ifAdmin = ifAdmin;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneType() {
        return searchZoneType;
    }

    public void setSearchZoneType(String searchZoneType) {
        this.searchZoneType = searchZoneType;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public Map<String, String> getSearchUnitMap() {
        return searchUnitMap;
    }

    public void setSearchUnitMap(Map<String, String> searchUnitMap) {
        this.searchUnitMap = searchUnitMap;
    }

    public List<String> getUploadTags() {
        return uploadTags;
    }

    public void setUploadTags(List<String> uploadTags) {
        this.uploadTags = uploadTags;
    }

    public List<String> getDealStates() {
        return dealStates;
    }

    public void setDealStates(List<String> dealStates) {
        this.dealStates = dealStates;
    }

    public String getDealState() {
        return dealState;
    }

    public void setDealState(String dealState) {
        this.dealState = dealState;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Map<String, String> getUploadZoneMap() {
        return uploadZoneMap;
    }

    public void setUploadZoneMap(Map<String, String> uploadZoneMap) {
        this.uploadZoneMap = uploadZoneMap;
    }

    public Map<String, String> getUploadUnitMap() {
        return uploadUnitMap;
    }

    public void setUploadUnitMap(Map<String, String> uploadUnitMap) {
        this.uploadUnitMap = uploadUnitMap;
    }

    public LogBhkServiceMain getEntityMain() {
        return entityMain;
    }

    public void setEntityMain(LogBhkServiceMain entityMain) {
        this.entityMain = entityMain;
    }

    public LogBhkServiceText getMainText() {
        return mainText;
    }

    public void setMainText(LogBhkServiceText mainText) {
        this.mainText = mainText;
    }

    public LogBhkServiceText getSubText() {
        return subText;
    }

    public void setSubText(LogBhkServiceText subText) {
        this.subText = subText;
    }

    public StreamedContent getDownloadFile() {
        return export();
    }

    public void setDownloadFile(StreamedContent downloadFile) {
        this.downloadFile = downloadFile;
    }
}
