package com.chis.modules.system.mongo2.web;

import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsUnitAttr;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.mongo2.entity.bhkdataservice.LogBhkServiceCrpt;
import com.chis.modules.system.mongo2.utils.MongoFacesEditBean2;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * @Description : 企业明细查询模块
 * @ClassAuthor : anjing
 * @Date : 2019/5/8 10:11
 **/
@ManagedBean
@ViewScoped
public class TdmgBhkServiceLogCrptBean extends MongoFacesEditBean2<LogBhkServiceCrpt>{

    /**查询实体*/
    private LogBhkServiceCrpt searchEntity;
    /**是否是超管*/
    private boolean ifAdmin = Boolean.TRUE;
    /**查询条件：地区名称*/
    private String searchZoneName;
    /**查询条件：地区编码*/
    private String searchZoneCode;
    /**查询条件：地区级别*/
    private String searchZoneType;
    /**地区集合*/
    private List<TsZone> zoneList;
    /**单位下拉*/
    private Map<String, String> searchUnitMap = new HashMap<String, String>(0);
    /**上传标记 0 失败 1 成功*/
    private List<String> uploadTags;
    /**处理标记 0 未处理 1 已处理*/
    private List<String> dealStates;

    /**处理状态*/
    private String dealState;

    private Map<String, String> uploadZoneMap = new HashMap<String, String>(0);
    private Map<String, String> uploadUnitMap = new HashMap<String, String>(0);

    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);

    @PostConstruct
    public void init(){
        TsUserInfo tsUserInfo = this.sessionData.getUser();
        if(!tsUserInfo.getUserNo().equals(Constants.ADMIN)) {
            ifAdmin = Boolean.FALSE;
        }
        this.searchZoneCode = tsUserInfo.getTsUnit().getTsZone().getZoneCode();
        this.searchZoneName = tsUserInfo.getTsUnit().getTsZone().getZoneName();
        this.searchZoneType = tsUserInfo.getTsUnit().getTsZone().getZoneType().toString();

        // 初始化查询实体
        searchEntity = new LogBhkServiceCrpt();
        searchEntity.setZone_gb(this.searchZoneCode);

        // 地区初始化
        if(null == this.zoneList || this.zoneList.size() <=0) {
            this.zoneList = this.commService.findZoneListNew(this.ifAdmin, searchZoneCode,null,"6");
            String fullName;
            for (TsZone tsZone : zoneList) {
                fullName = StringUtils.trimToEmpty(tsZone.getFullName());
                fullName = fullName.replaceFirst(".*?_","");
                uploadZoneMap.put(tsZone.getZoneGb(),fullName);
            }
        }

        // 初始化单位
        this.searchUnitMap = this.filterUnit(this.searchZoneCode, this.searchZoneType);

        List<Object[]> list = this.systemModuleService.findSrvorgByZoneCode(true, true,null,null);
        if(list!=null&&list.size()>0){
            for (Object[] strings : list) {
                if(strings[0]!=null&&strings[1]!=null){
                    uploadUnitMap.put(strings[0].toString(),strings[1].toString());
                }
            }
        }

        this.searchAction();
    }

    /**
     * @Description : 根据地区刷单位
     * @MethodAuthor: anjing
     * @Date : 2019/5/8 10:24
     **/
    private Map<String, String> filterUnit(String zoneCode, String zoneType) {
        TsUserInfo user = this.sessionData.getUser();
        Map<String, String> map = new LinkedHashMap<String, String>();
        Set<TsUnitAttr> tsUnitAttrs = user.getTsUnit().getTsUnitAttrs();
        boolean flag = false;
        if(tsUnitAttrs != null){
            for(TsUnitAttr tsUnitAttr : tsUnitAttrs){
                if(tsUnitAttr.getTsBsSort() != null && tsUnitAttr.getTsBsSort().getSortCode().equals("2001")){
                    flag = true;
                }
            }
        }

        List<Object[]> list = this.systemModuleService.findSrvorgByZoneCode(this.ifAdmin, flag,user.getTsUnit().getRid(), zoneCode);
        if(list!=null&&list.size()>0){
            for (Object[] strings : list) {
                if(strings[0]!=null&&strings[1]!=null){
                    map.put(strings[1].toString(),strings[0].toString());
                    if(!flag && !this.ifAdmin){
                        this.searchEntity.setUnit_code(strings[0].toString());
                    }
                }

            }
        }
        return map;
    }

    /**
     * @Description : 查询条件，地区树选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/8 11:14
     **/
    public void onSearchNodeSelect() {
        this.searchEntity.setUnit_code(null);
        this.searchUnitMap = this.filterUnit(this.searchEntity.getZone_gb(), this.searchZoneType);
    }

    /**
    * @Description : 查询
    * @MethodAuthor: anjing
    * @Date : 2019/5/8 10:25
    **/
    public void searchAction(){
        if(searchEntity.getVisit_time_start() != null && searchEntity.getVisit_time_end() != null&&
                searchEntity.getVisit_time_start().after(searchEntity.getVisit_time_end())) {
            JsfUtil.addErrorMessage("上传日期开始日期应小于等于结束日期！");
            return;
        }
        if(searchEntity.getVisit_time_end() != null) {
            String formatDate = DateUtils.formatDate(searchEntity.getVisit_time_end(), "yyyy-MM-dd");
            searchEntity.setVisit_time_end(DateUtils.parseDate(formatDate+" 23:59:59"));
        }
        super.searchAction();
    }

    @Override
    protected void processData(List<LogBhkServiceCrpt> records) {

    }


    /**
    * @Description : 方法描述: 列表查询语句
    * @MethodAuthor: anjing
    * @Date : 2019/5/8 11:20
    **/
    protected Criteria buildCriteria() {
        List<Criteria> criteriaList = new ArrayList<>();

        //上传地区
        String zone_gb = searchEntity.getZone_gb();
        if(StringUtils.isNotBlank(zone_gb)){
            criteriaList.add(Criteria.where("zone_gb").regex("^"+ ZoneUtil.zoneSelect(zone_gb)+".*"));
        }
        //上传机构
        String unit_code = searchEntity.getUnit_code();
        if(StringUtils.isNotBlank(unit_code)){
            criteriaList.add(Criteria.where("unit_code").is(unit_code));
        }
        //上传日期
        Date visit_time_start = searchEntity.getVisit_time_start();
        if(visit_time_start!=null){
            criteriaList.add(Criteria.where("visit_time").gte(visit_time_start));
        }
        Date visit_time_end = searchEntity.getVisit_time_end();
        if(visit_time_end!=null){
            criteriaList.add(Criteria.where("visit_time").lte(visit_time_end));
        }
        //企业名称
        String crpt_name = searchEntity.getCrpt_name();
        if(StringUtils.isNotBlank(crpt_name)){
            criteriaList.add(Criteria.where("crpt_name").is(crpt_name));

//            criteriaList.add(Criteria.where("crpt_name").regex(".*" + crpt_name+".*"));
        }
        //社会信用代码
        String credit_code = searchEntity.getCredit_code();
        if(StringUtils.isNotBlank(credit_code)){
            //criteriaList.add(Criteria.where("credit_code").regex(credit_code,"i"));
            criteriaList.add(Criteria.where("credit_code").regex("^" + credit_code));
        }
        //上传标记
        if(uploadTags!=null&&uploadTags.size()==1){
            criteriaList.add(Criteria.where("upload_tag").is(uploadTags.get(0)));
        }
        //处理状态
        if(dealStates!=null&&dealStates.size()==1){
            criteriaList.add(Criteria.where("deal_state").is(dealStates.get(0)));
        }

        Criteria criteria = new Criteria().andOperator((Criteria[]) criteriaList.toArray(new Criteria[criteriaList.size()]));
        return criteria;
    }

    @Override
    public Query buildQuery() {
        Query query = new Query();
        query.addCriteria(this.buildCriteria());
        //上传时间降序
        super.mongoPage.setDesc("visit_time");
        return query;
    }








    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }


    public boolean isIfAdmin() {
        return ifAdmin;
    }

    public void setIfAdmin(boolean ifAdmin) {
        this.ifAdmin = ifAdmin;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneType() {
        return searchZoneType;
    }

    public void setSearchZoneType(String searchZoneType) {
        this.searchZoneType = searchZoneType;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public Map<String, String> getSearchUnitMap() {
        return searchUnitMap;
    }

    public void setSearchUnitMap(Map<String, String> searchUnitMap) {
        this.searchUnitMap = searchUnitMap;
    }

    public List<String> getUploadTags() {
        return uploadTags;
    }

    public void setUploadTags(List<String> uploadTags) {
        this.uploadTags = uploadTags;
    }

    public List<String> getDealStates() {
        return dealStates;
    }

    public void setDealStates(List<String> dealStates) {
        this.dealStates = dealStates;
    }

    public String getDealState() {
        return dealState;
    }

    public void setDealState(String dealState) {
        this.dealState = dealState;
    }

    public Map<String, String> getUploadZoneMap() {
        return uploadZoneMap;
    }

    public void setUploadZoneMap(Map<String, String> uploadZoneMap) {
        this.uploadZoneMap = uploadZoneMap;
    }

    public Map<String, String> getUploadUnitMap() {
        return uploadUnitMap;
    }

    public void setUploadUnitMap(Map<String, String> uploadUnitMap) {
        this.uploadUnitMap = uploadUnitMap;
    }

    public LogBhkServiceCrpt getSearchEntity() {
        return searchEntity;
    }

    public void setSearchEntity(LogBhkServiceCrpt searchEntity) {
        this.searchEntity = searchEntity;
    }
}
