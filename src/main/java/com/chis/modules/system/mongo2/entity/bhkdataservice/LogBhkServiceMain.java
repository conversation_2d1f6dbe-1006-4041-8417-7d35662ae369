package com.chis.modules.system.mongo2.entity.bhkdataservice;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * @Description : 职业卫生数据上传请求日志-原始请求
 * @ClassAuthor : anjing
 * @Date : 2019/5/8 10:07
 **/
@Document(collection = "log_bhk_service_main")
public class LogBhkServiceMain {

    @Id
    private String rid;

    /**客户端IP*/
    private String client_ip;

    /**调用地址*/
    private String visit_adr;

    /**接口类型*/
    private String inter_type;

    /**子数据总数*/
    private String sub_count;

    /**成功数*/
    private String success_count;

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getClient_ip() {
        return client_ip;
    }

    public void setClient_ip(String client_ip) {
        this.client_ip = client_ip;
    }

    public String getVisit_adr() {
        return visit_adr;
    }

    public void setVisit_adr(String visit_adr) {
        this.visit_adr = visit_adr;
    }

    public String getInter_type() {
        return inter_type;
    }

    public void setInter_type(String inter_type) {
        this.inter_type = inter_type;
    }

    public String getSub_count() {
        return sub_count;
    }

    public void setSub_count(String sub_count) {
        this.sub_count = sub_count;
    }

    public String getSuccess_count() {
        return success_count;
    }

    public void setSuccess_count(String success_count) {
        this.success_count = success_count;
    }
}
