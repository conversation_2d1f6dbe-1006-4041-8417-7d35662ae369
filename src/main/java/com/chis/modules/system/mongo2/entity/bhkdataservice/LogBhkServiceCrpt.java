package com.chis.modules.system.mongo2.entity.bhkdataservice;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * @Description : 职业卫生数据上传请求日志-企业明细
 * @ClassAuthor : anjing
 * @Date : 2019/5/8 9:59
 **/
@Document(collection = "log_bhk_service_crpt")
public class LogBhkServiceCrpt {

    @Id
    private String rid;

    /**请求唯一标识*/
    private String uid;

    /**调用时间*/
    private Date visit_time;

    /**上传地区编码*/
    private String zone_gb;

    /**上传机构编码*/
    private String unit_code;

    /**所属地区*/
    private String crpt_zone_code;

    /**企业名称*/
    private String crpt_name;

    /**社会信用代码*/
    private String credit_code;

    /**上传标记*/
    private String upload_tag;

    /**处理状态 2 成功无需处理 0 错误未处理 1 错误已处理*/
    private String deal_state;

    /**主表Id*/
    private String main_id;
    private String error_msg;//错误信息

    /************** 非实体字段 ***************/
    private Date visit_time_start; // 查询开始日期
    private Date visit_time_end; // 查询结束日期
    private String error_msg_temp;//临时错误原因
    /**
     * 需要更新单位名称
     */
    private String need_update_name;
    private String update_name;

    public String getError_msg_temp() {
        return error_msg_temp;
    }

    public void setError_msg_temp(String error_msg_temp) {
        this.error_msg_temp = error_msg_temp;
    }

    public String getError_msg() {
        return error_msg;
    }

    public void setError_msg(String error_msg) {
        this.error_msg = error_msg;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Date getVisit_time() {
        return visit_time;
    }

    public void setVisit_time(Date visit_time) {
        this.visit_time = visit_time;
    }

    public String getZone_gb() {
        return zone_gb;
    }

    public void setZone_gb(String zone_gb) {
        this.zone_gb = zone_gb;
    }

    public String getUnit_code() {
        return unit_code;
    }

    public void setUnit_code(String unit_code) {
        this.unit_code = unit_code;
    }

    public String getCrpt_zone_code() {
        return crpt_zone_code;
    }

    public void setCrpt_zone_code(String crpt_zone_code) {
        this.crpt_zone_code = crpt_zone_code;
    }

    public String getCrpt_name() {
        return crpt_name;
    }

    public void setCrpt_name(String crpt_name) {
        this.crpt_name = crpt_name;
    }

    public String getCredit_code() {
        return credit_code;
    }

    public void setCredit_code(String credit_code) {
        this.credit_code = credit_code;
    }

    public String getUpload_tag() {
        return upload_tag;
    }

    public void setUpload_tag(String upload_tag) {
        this.upload_tag = upload_tag;
    }

    public String getDeal_state() {
        return deal_state;
    }

    public void setDeal_state(String deal_state) {
        this.deal_state = deal_state;
    }

    public String getMain_id() {
        return main_id;
    }

    public void setMain_id(String main_id) {
        this.main_id = main_id;
    }

    public Date getVisit_time_start() {
        return visit_time_start;
    }

    public void setVisit_time_start(Date visit_time_start) {
        this.visit_time_start = visit_time_start;
    }

    public Date getVisit_time_end() {
        return visit_time_end;
    }

    public void setVisit_time_end(Date visit_time_end) {
        this.visit_time_end = visit_time_end;
    }

    public String getNeed_update_name() {
        return need_update_name;
    }

    public void setNeed_update_name(String need_update_name) {
        this.need_update_name = need_update_name;
    }

    public String getUpdate_name() {
        return update_name;
    }

    public void setUpdate_name(String update_name) {
        this.update_name = update_name;
    }
}
