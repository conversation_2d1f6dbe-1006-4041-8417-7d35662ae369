package com.chis.modules.system.mongo2.web;

import com.chis.common.utils.*;
import com.chis.modules.system.mongo2.entity.MongoPage2;
import com.chis.modules.system.mongo2.entity.bhkdataservice.LogBhkServiceBhk;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.CellStyle;
import org.primefaces.model.DefaultStreamedContent;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.text.ParseException;
import java.util.*;

/***
 * @Description : 人员日志查询（新）
 * @ClassAuthor : mxp
 * @Date : 2019/5/20 13:21
 */
@ManagedBean
@ViewScoped
public class LogBhkServiceBhkBean extends LogBhkServiceBean<LogBhkServiceBhk> {

    /**查询实体*/
    private LogBhkServiceBhk searchEntity;
    private LogBhkServiceBhk entity;
    private List<LogBhkServiceBhk> selectEntitys;


    @PostConstruct
    public void init(){
        super.init();
        // 初始化查询实体
        searchEntity = new LogBhkServiceBhk();
        searchEntity.setZone_gb(this.searchZoneCode);
        searchEntity.setUnit_code(super.unitCode);
        searchEntity.setUpload_tag("0");
        searchEntity.setDeal_state("0");
        try {
            Date date = DateUtils.parseDate(DateUtils.getYear() + "-"+DateUtils.getMonth()+"-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
            searchEntity.setBhk_date_start(date);
            String formatDate = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
            searchEntity.setBhk_date_end(DateUtils.parseDate(formatDate+" 23:59:59"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        this.searchAction();
    }



    /**
     * @Description : 查询条件，地区树选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/8 11:14
     **/
    public void onSearchNodeSelect() {
        this.searchEntity.setUnit_code(null);
        this.searchUnitMap = this.filterUnit(this.searchEntity.getZone_gb(), this.searchZoneType);
    }

    /**
     * @Description : 查询
     * @MethodAuthor: anjing
     * @Date : 2019/5/8 10:25
     **/
    public void searchAction(){
        if(searchEntity.getBhk_date_start()==null){
            JsfUtil.addErrorMessage("体检日期开始日期不能为空！");
            return ;
        }
        if(searchEntity.getBhk_date_end()==null){
            JsfUtil.addErrorMessage("体检日期结束日期不能为空！");
            return ;
        }
        if(searchEntity.getBhk_date_start()!=null&&searchEntity.getBhk_date_end()!=null&&
                searchEntity.getBhk_date_start().after(searchEntity.getBhk_date_end())){
            JsfUtil.addErrorMessage("体检日期开始日期应小于等于体检结束日期！");
            return;
        }
        if(searchEntity.getBhk_date_end()!=null){
            String formatDate = DateUtils.formatDate(searchEntity.getBhk_date_end(), "yyyy-MM-dd");
            searchEntity.setBhk_date_end(DateUtils.parseDate(formatDate+" 23:59:59"));
        }
        super.searchAction();
    }

    @Override
    protected void processData(List<LogBhkServiceBhk> records) {
        if(records!=null&&records.size()>0){
            for (LogBhkServiceBhk record : records) {
                record.setBhk_idc(StringUtils.encryptIdc(record.getBhk_idc()));
                if("0".equals(record.getUpload_tag())){
                    String error_msg = record.getError_msg();
                    record.setError_msg_temp(error_msg);
                    if(error_msg!=null&&error_msg.length()>10){
                        record.setError_msg_temp(error_msg.substring(0,10)+"...");
                    }
                }
            }
        }
    }


    /**
     * @Description : 方法描述: 列表查询语句
     * @MethodAuthor: anjing
     * @Date : 2019/5/8 11:20
     **/
    protected Criteria buildCriteria() {
        List<Criteria> criteriaList = new ArrayList<>();

        //上传地区
        String zone_gb = searchEntity.getZone_gb();
        if(StringUtils.isNotBlank(zone_gb)){
            criteriaList.add(Criteria.where("zone_gb").regex("^"+ ZoneUtil.zoneSelect(zone_gb)+".*"));
        }
        //上传机构
        String unit_code = searchEntity.getUnit_code();
        if(StringUtils.isNotBlank(unit_code)){
            criteriaList.add(Criteria.where("unit_code").is(unit_code));
        }
        //体检日期
        Date bhk_date_start = searchEntity.getBhk_date_start();
        if(bhk_date_start!=null){
            criteriaList.add(Criteria.where("bhk_date").gte(bhk_date_start));
        }
        Date bhk_date_end = searchEntity.getBhk_date_end();
        if(bhk_date_end!=null){
            criteriaList.add(Criteria.where("bhk_date").lte(bhk_date_end));
        }
        //企业名称
        String crpt_name = searchEntity.getBhk_crpt_name();
        if(StringUtils.isNotBlank(crpt_name)){
//            criteriaList.add(Criteria.where("bhk_crpt_name").regex("^"+crpt_name+".*"));
            criteriaList.add(Criteria.where("bhk_crpt_name").is(crpt_name));
        }
        //姓名
        String bhk_psn_name = searchEntity.getBhk_psn_name();
        if(StringUtils.isNotBlank(bhk_psn_name)){
            criteriaList.add(Criteria.where("bhk_psn_name").is(bhk_psn_name));
        }
        //身份证号
        String bhk_idc = searchEntity.getBhk_idc();
        if(StringUtils.isNotBlank(bhk_idc)){
            criteriaList.add(Criteria.where("bhk_idc").is(bhk_idc));
        }
        //上传标记
        if(uploadTags!=null&&uploadTags.size()==1){
            criteriaList.add(Criteria.where("upload_tag").is(uploadTags.get(0)));
        }
        //处理状态
        if(dealStates!=null&&dealStates.size()==1){
            criteriaList.add(Criteria.where("deal_state").is(dealStates.get(0)));
        }

        Criteria criteria = new Criteria().andOperator((Criteria[]) criteriaList.toArray(new Criteria[criteriaList.size()]));
        return criteria;
    }

    @Override
    public Query buildQuery() {
        Query query = new Query();
        query.addCriteria(this.buildCriteria());
        //上传时间降序
        super.mongoPage.setDesc("visit_time");
        return query;
    }

    /***
     * <p>方法描述: 更新状态</p>
     *
     * @MethodAuthor mxp,2018/12/19,updateStateAction
     */
    public void updateStateAction(){
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("rid").is(searchEntity.getRid()));
            Update update = new Update();
            update.set("deal_state","1");
            mongoService.updateFirst(query, update, LogBhkServiceBhk.class);
            entity.setDeal_state("1");
            this.searchAction();
            JsfUtil.addSuccessMessage("操作成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("操作失败！");
            e.printStackTrace();
        }
    }
    /***
     * <p>方法描述: 更新状态状态</p>
     *
     * @MethodAuthor mxp,2018/12/19,batchUpdateStateAction
     */
    public void batchUpdateStateAction(){
        try {
            if(selectEntitys==null||selectEntitys.size()==0){
                JsfUtil.addErrorMessage("请选择数据！");
                return ;
            }
            List<String> ids = new ArrayList<>();
            for (LogBhkServiceBhk selectEntity : selectEntitys) {
                if("0".equals(selectEntity.getDeal_state())){
                    ids.add(selectEntity.getRid());
                }
            }
            if(ids.size()==0){
                JsfUtil.addErrorMessage("请选择含有未阅的数据！");
                return ;
            }

            Query query = new Query();
            query.addCriteria(Criteria.where("rid").in(ids));
            Update update = new Update();
            update.set("deal_state","1");
            mongoService.updateMulti(query, update, LogBhkServiceBhk.class);
            this.searchAction();
            JsfUtil.addSuccessMessage("操作成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("操作失败！");
            e.printStackTrace();
        }
    }







    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }
    /***
     * @Description : 编辑
     * @MethodAuthor: mxp
     * @Date : 2019/5/20 14:41
     */
    @Override
    public void modInit() {
        if(entity==null){
            return;
        }
        super.modInit(entity.getMain_id(),entity.getRid());
    }

    @Override
    public void saveAction() {

    }

    /***
     * @Description : 导出
     * @MethodAuthor: mxp
     * @Date : 2019/5/20 16:47
     */
    public DefaultStreamedContent export(){
       String fileName="人员日志";
        // 初始化标题
        HSSFWorkbook wb = super.initTitle(fileName, 11);
        // 初始化第二行标题
        this.initTitle2(wb);
        // 组装数据导出
        boolean b = initExportData(wb);
        if(!b){
            return null;
        }
        return super.export(fileName,wb);

    }

    /***
     * @Description : 人员数据
     * @MethodAuthor: mxp
     * @Date : 2019/5/20 16:57
     * @return
     */
    protected boolean initExportData(HSSFWorkbook wb){
        //先查总共有多少条数据
        Query query = this.buildQuery();
        long count = mongoService.count(query, LogBhkServiceBhk.class);
        if(count>1000000){
            JsfUtil.addErrorMessage("导出数据大于1百万，无法导出！");
            return false;
        }

        HSSFSheet sheet = wb.getSheetAt(0);

        //每次查询10000条
        int  size = 10000;
        long num = count/size;
        if(count%size!=0){
            num++;
        }
        MongoPage2<LogBhkServiceBhk> mongoPage = new MongoPage2<>();
        mongoPage.setSize(size);//因为下面分页查询每次多查询出1条
        mongoPage.setDesc("visit_time");
        MongoPage2<LogBhkServiceBhk> mongoPage2;
        List<LogBhkServiceBhk> records;
        int rowNum=1;
        for (int i = 1; i <= num; i++) {
            mongoPage.setCurrent(i);
            mongoPage2 = mongoService.pageListCommon(mongoPage, query, LogBhkServiceBhk.class);
            records = mongoPage2.getRecords();
            if(records!=null&&records.size()>0){
                rowNum = (i-1)*size;
                for (LogBhkServiceBhk record : records) {
                    rowNum++;
                    HSSFRow row2 = sheet.createRow(rowNum+1);
                    HSSFCell cell0 = row2.createCell(0);
                    cell0.setCellValue(uploadZoneMap.get(record.getZone_gb()));
                    HSSFCell cell1 = row2.createCell(1);
                    cell1.setCellValue(uploadUnitMap.get(record.getUnit_code()));
                    HSSFCell cell2 = row2.createCell(2);
                    cell2.setCellValue(record.getBhk_psn_name());
                    HSSFCell cell3 = row2.createCell(3);
                    cell3.setCellValue(StringUtils.encryptIdc(record.getBhk_idc()));
                    HSSFCell cell4 = row2.createCell(4);
                    cell4.setCellValue(record.getBhk_code());
                    HSSFCell cell5 = row2.createCell(5);
                    cell5.setCellValue(record.getBhk_crpt_name());
                    HSSFCell cell6 = row2.createCell(6);
                    cell6.setCellValue(record.getBhk_credit_code());
                    HSSFCell cell7 = row2.createCell(7);
                    cell7.setCellValue(DateUtils.formatDate(record.getBhk_date()));
                    HSSFCell cell8 = row2.createCell(8);
                    cell8.setCellValue(DateUtils.formatDate(record.getVisit_time(),"yyyy-MM-dd HH:mm:ss"));
                    HSSFCell cell9= row2.createCell(9);
                    cell9.setCellValue("1".equals(record.getUpload_tag())?"成功":"失败");
                    HSSFCell cell10 = row2.createCell(10);
                    cell10.setCellValue("0".equals(record.getUpload_tag())?"未阅":"1".equals(record.getUpload_tag())?"已阅":"无需处理");
                    HSSFCell cell11 = row2.createCell(11);
                    cell11.setCellValue(record.getError_msg());

                }

            }
        }
        super.export("人员日志",wb);
        return true;
    }

    @Override
    public void initTitle2(HSSFWorkbook wb) {
        HSSFSheet sheet = wb.getSheetAt(0);
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(CellStyle.ALIGN_CENTER);// 左右居中
        cellStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        // 表列名称
        HSSFRow row2 = sheet.createRow(1);
        HSSFCell cell0 = row2.createCell(0);
        cell0.setCellValue("地区");
        cell0.setCellStyle(cellStyle);
        sheet.setColumnWidth(0, 3000);
        HSSFCell cell1 = row2.createCell(1);
        cell1.setCellValue("上传机构");
        cell1.setCellStyle(cellStyle);
        sheet.setColumnWidth(1, 4500);
        HSSFCell cell2 = row2.createCell(2);
        cell2.setCellValue("人员姓名");
        cell2.setCellStyle(cellStyle);
        sheet.setColumnWidth(2, 5500);
        HSSFCell cell3 = row2.createCell(3);
        cell3.setCellValue("身份证号");
        cell3.setCellStyle(cellStyle);
        sheet.setColumnWidth(3, 4000);
        HSSFCell cell4 = row2.createCell(4);
        cell4.setCellValue("体检编号");
        cell4.setCellStyle(cellStyle);
        sheet.setColumnWidth(4, 5000);
        HSSFCell cell5 = row2.createCell(5);
        cell5.setCellValue("企业名称");
        cell5.setCellStyle(cellStyle);
        sheet.setColumnWidth(5, 3000);
        HSSFCell cell6 = row2.createCell(6);
        cell6.setCellValue("社会信用代码");
        cell6.setCellStyle(cellStyle);
        sheet.setColumnWidth(6, 5500);
        HSSFCell cell7 = row2.createCell(7);
        cell7.setCellValue("体检日期");
        cell7.setCellStyle(cellStyle);
        sheet.setColumnWidth(7, 4000);
        HSSFCell cell8 = row2.createCell(8);
        cell8.setCellValue("上传时间");
        cell8.setCellStyle(cellStyle);
        sheet.setColumnWidth(8, 4000);
        HSSFCell cell9= row2.createCell(9);
        cell9.setCellValue("上传标记");
        cell9.setCellStyle(cellStyle);
        sheet.setColumnWidth(9, 4000);
        HSSFCell cell10 = row2.createCell(10);
        cell10.setCellValue("状态");
        cell10.setCellStyle(cellStyle);
        sheet.setColumnWidth(10, 4000);
        HSSFCell cell11 = row2.createCell(11);
        cell11.setCellValue("失败原因");
        cell11.setCellStyle(cellStyle);
        sheet.setColumnWidth(11, 4000);
    }


    public LogBhkServiceBhk getSearchEntity() {
        return searchEntity;
    }

    public void setSearchEntity(LogBhkServiceBhk searchEntity) {
        this.searchEntity = searchEntity;
    }

    public LogBhkServiceBhk getEntity() {
        return entity;
    }

    public void setEntity(LogBhkServiceBhk entity) {
        this.entity = entity;
    }

    public List<LogBhkServiceBhk> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<LogBhkServiceBhk> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }
}