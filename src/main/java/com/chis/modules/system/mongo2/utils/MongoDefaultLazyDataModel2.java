package com.chis.modules.system.mongo2.utils;

import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.mongo.service.MongoService;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Description : 分页
 * @ClassAuthor : anjing
 * @Date : 2019/5/8 10:46
 **/
public class MongoDefaultLazyDataModel2<T> extends LazyDataModel<T> {

    private static final long serialVersionUID = 1L;
    private Class<T> tClass ;
    private MongoService mongoService = SpringContextHolder.getBean(MongoService.class);
    private List<T> data;
    private Query query;
    /**数据处理接口*/
    private IProcessData processData;

    public MongoDefaultLazyDataModel2(Class<T> tClass, IProcessData buildProcessData, Query query) {
        this.tClass = tClass;
        this.processData = buildProcessData;
        this.query = query;
    }

    @Override
    public List<T> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map<String, String> filters) {
        this.data = this.mongoService.pageList2(first,pageSize, query, tClass);
        int size = data.size();
        // 计算当前加载数据总数
        int rowCount = first+size;
        this.setRowCount(rowCount);
        if (rowCount == 0) {
            return Collections.emptyList();
        }

        if(null != this.processData) {
            this.processData.processData(this.data);
        }
        return this.data;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }
}
