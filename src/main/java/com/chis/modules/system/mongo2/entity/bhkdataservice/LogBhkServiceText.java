package com.chis.modules.system.mongo2.entity.bhkdataservice;

import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import java.util.Date;

/***
 * <p>类描述: 请求报文 </p>
 *
 * @ClassAuthor mxp,2019/1/18,TdZwlogUploadText
 */
@Document(collection = "log_bhk_service_text")
public class LogBhkServiceText {

    @Id
    private String rid;

    private String req_text;
    private String resp_text;
    private String req_encrypt_text;

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getReq_text() {
        return req_text;
    }

    public void setReq_text(String req_text) {
        this.req_text = req_text;
    }

    public String getResp_text() {
        return resp_text;
    }

    public void setResp_text(String resp_text) {
        this.resp_text = resp_text;
    }

    public String getReq_encrypt_text() {
        return req_encrypt_text;
    }

    public void setReq_encrypt_text(String req_encrypt_text) {
        this.req_encrypt_text = req_encrypt_text;
    }
}
