package com.chis.modules.system.mongo2.entity.bhkdataservice;


import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/***
 * <p>类描述: 明细 </p>
 *
 * @ClassAuthor mxp,2018/12/19,TdZwlogUploadDetail
 */
@Document(collection = "log_bhk_service_bhk")
public class LogBhkServiceBhk {
    @Id
    private String rid;//	VARCHAR2(50)	50		TRUE	FALSE	TRUE
    //请求唯一标识
    private String uid;//	VARCHAR2(50)	50		FALSE	FALSE	TRUE
    /**调用时间*/
    private Date visit_time;
    /**上传地区编码*/
    private String zone_gb;
    /**上传机构编码*/
    private String unit_code;
    //操作标识(体检记录时使用)
    private String opt_tag;//	VARCHAR2(50)	50		FALSE	FALSE	TRUE
    //体检机构编号(体检记录时使用)
    private String bhk_org_code;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //体检机构名称(体检记录时使用)
    private String bhk_org_name;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //体检编号(体检记录时使用)
    private String bhk_code;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //企业所属地区编码
    private String bhk_crpt_zone_gb;
    //企业名称(体检记录时使用)
    private String bhk_crpt_name;//	VARCHAR2(50)	50		FALSE	FALSE	TRUE
    //社会信用代码(体检记录时使用)
    private String bhk_credit_code;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //人员姓名(体检记录时使用)
    private String bhk_psn_name;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //身份证号(体检记录时使用)
    private String bhk_idc;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //体检日期(体检记录时使用)
    private Date bhk_date;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    private Date bhk_date_start;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    private Date bhk_date_end;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //上传标记
    private String upload_tag;//	VARCHAR2(50)	50		FALSE	FALSE	FALSE
    //处理状态 2 成功无需处理 0 错误未处理 1 错误已处理
    private String deal_state;//

    private String error_msg;//错误信息

    /**主表Id*/
    private String main_id;
    //+体检类型20230307
    private String bhk_type;
    //+监测类型20230307
    private String jc_type;
    //+是否复查20230307
    private String if_rhk;
    //+报告出具日期20230307
    private Date rpt_date;
    //+是否纯放射危害因素20230307
    private String if_only_fs;

    /************** 非实体字段 用于查询***************/
    private Date visit_time_start; // 查询开始日期
    private Date visit_time_end; // 查询结束日期
    private String error_msg_temp;//临时错误原因

    //体检类型
    private String[] tjType;
    //监测类型
    private String[] jcType;
    //报告出具日期
    private Date rpt_date_start; // 查询开始日期
    private Date rpt_date_end; // 查询结束日期
    //是否复查
    private String[] fcState;


    public Date getRpt_date_start() {
        return rpt_date_start;
    }

    public void setRpt_date_start(Date rpt_date_start) {
        this.rpt_date_start = rpt_date_start;
    }

    public Date getRpt_date_end() {
        return rpt_date_end;
    }

    public void setRpt_date_end(Date rpt_date_end) {
        this.rpt_date_end = rpt_date_end;
    }

    public String getError_msg_temp() {
        return error_msg_temp;
    }

    public void setError_msg_temp(String error_msg_temp) {
        this.error_msg_temp = error_msg_temp;
    }
    public Date getVisit_time_start() {
        return visit_time_start;
    }

    public void setVisit_time_start(Date visit_time_start) {
        this.visit_time_start = visit_time_start;
    }

    public Date getVisit_time_end() {
        return visit_time_end;
    }

    public void setVisit_time_end(Date visit_time_end) {
        this.visit_time_end = visit_time_end;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Date getVisit_time() {
        return visit_time;
    }

    public void setVisit_time(Date visit_time) {
        this.visit_time = visit_time;
    }

    public String getZone_gb() {
        return zone_gb;
    }
    public void setZone_gb(String zone_gb) {
        this.zone_gb = zone_gb;
    }

    public String getUnit_code() {
        return unit_code;
    }

    public void setUnit_code(String unit_code) {
        this.unit_code = unit_code;
    }

    public String getOpt_tag() {
        return opt_tag;
    }

    public void setOpt_tag(String opt_tag) {
        this.opt_tag = opt_tag;
    }

    public String getBhk_org_code() {
        return bhk_org_code;
    }

    public void setBhk_org_code(String bhk_org_code) {
        this.bhk_org_code = bhk_org_code;
    }

    public String getBhk_org_name() {
        return bhk_org_name;
    }

    public void setBhk_org_name(String bhk_org_name) {
        this.bhk_org_name = bhk_org_name;
    }

    public String getBhk_code() {
        return bhk_code;
    }

    public void setBhk_code(String bhk_code) {
        this.bhk_code = bhk_code;
    }

    public String getBhk_crpt_zone_gb() {
        return bhk_crpt_zone_gb;
    }

    public void setBhk_crpt_zone_gb(String bhk_crpt_zone_gb) {
        this.bhk_crpt_zone_gb = bhk_crpt_zone_gb;
    }

    public String getBhk_crpt_name() {
        return bhk_crpt_name;
    }

    public void setBhk_crpt_name(String bhk_crpt_name) {
        this.bhk_crpt_name = bhk_crpt_name;
    }

    public String getBhk_credit_code() {
        return bhk_credit_code;
    }

    public void setBhk_credit_code(String bhk_credit_code) {
        this.bhk_credit_code = bhk_credit_code;
    }

    public String getBhk_psn_name() {
        return bhk_psn_name;
    }

    public void setBhk_psn_name(String bhk_psn_name) {
        this.bhk_psn_name = bhk_psn_name;
    }

    public String getBhk_idc() {
        return bhk_idc;
    }

    public void setBhk_idc(String bhk_idc) {
        this.bhk_idc = bhk_idc;
    }

    public Date getBhk_date() {
        return bhk_date;
    }

    public void setBhk_date(Date bhk_date) {
        this.bhk_date = bhk_date;
    }

    public Date getBhk_date_start() {
        return bhk_date_start;
    }

    public void setBhk_date_start(Date bhk_date_start) {
        this.bhk_date_start = bhk_date_start;
    }

    public Date getBhk_date_end() {
        return bhk_date_end;
    }

    public void setBhk_date_end(Date bhk_date_end) {
        this.bhk_date_end = bhk_date_end;
    }

    public String getUpload_tag() {
        return upload_tag;
    }

    public void setUpload_tag(String upload_tag) {
        this.upload_tag = upload_tag;
    }

    public String getDeal_state() {
        return deal_state;
    }

    public void setDeal_state(String deal_state) {
        this.deal_state = deal_state;
    }

    public String getError_msg() {
        return error_msg;
    }

    public void setError_msg(String error_msg) {
        this.error_msg = error_msg;
    }

    public String getMain_id() {
        return main_id;
    }

    public void setMain_id(String main_id) {
        this.main_id = main_id;
    }

    public String getBhk_type() {
        return bhk_type;
    }

    public void setBhk_type(String bhk_type) {
        this.bhk_type = bhk_type;
    }

    public String getJc_type() {
        return jc_type;
    }

    public void setJc_type(String jc_type) {
        this.jc_type = jc_type;
    }

    public String getIf_rhk() {
        return if_rhk;
    }

    public void setIf_rhk(String if_rhk) {
        this.if_rhk = if_rhk;
    }

    public Date getRpt_date() {
        return rpt_date;
    }

    public void setRpt_date(Date rpt_date) {
        this.rpt_date = rpt_date;
    }

    public String getIf_only_fs() {
        return if_only_fs;
    }

    public void setIf_only_fs(String if_only_fs) {
        this.if_only_fs = if_only_fs;
    }

    public String[] getTjType() {
        return tjType;
    }

    public void setTjType(String[] tjType) {
        this.tjType = tjType;
    }

    public String[] getJcType() {
        return jcType;
    }

    public void setJcType(String[] jcType) {
        this.jcType = jcType;
    }

    public String[] getFcState() {
        return fcState;
    }

    public void setFcState(String[] fcState) {
        this.fcState = fcState;
    }
}
