package com.chis.modules.system.mongo2.web;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.mongo2.entity.MongoPage2;
import com.chis.modules.system.mongo2.entity.bhkdataservice.LogBhkServiceCrpt;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellStyle;
import org.primefaces.model.DefaultStreamedContent;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/***
 * @Description : 企业日志查询（新）
 * @ClassAuthor : mxp
 * @Date : 2019/5/20 13:21
 */
@ManagedBean
@ViewScoped
public class LogBhkServiceCrptBean extends LogBhkServiceBean<LogBhkServiceCrpt> {

    /**查询实体*/
    private LogBhkServiceCrpt searchEntity;
    private LogBhkServiceCrpt entity;
    private List<LogBhkServiceCrpt> selectEntitys;

    @PostConstruct
    public void init(){
        super.init();
        // 初始化查询实体
        searchEntity = new LogBhkServiceCrpt();
        searchEntity.setZone_gb(this.searchZoneCode);
        searchEntity.setUnit_code(super.unitCode);
        searchEntity.setUpload_tag("0");
        searchEntity.setDeal_state("0");
        try {
            Date date = DateUtils.parseDate(DateUtils.getYear() + "-"+DateUtils.getMonth()+"-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
            searchEntity.setVisit_time_start(date);
            String formatDate = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
            searchEntity.setVisit_time_end(DateUtils.parseDate(formatDate+" 23:59:59"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        this.searchAction();
    }



    /**
     * @Description : 查询条件，地区树选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/8 11:14
     **/
    public void onSearchNodeSelect() {
        this.searchEntity.setUnit_code(null);
        this.searchUnitMap = this.filterUnit(this.searchEntity.getZone_gb(), this.searchZoneType);
    }

    /**
     * @Description : 查询
     * @MethodAuthor: anjing
     * @Date : 2019/5/8 10:25
     **/
    public void searchAction(){
        if(searchEntity.getVisit_time_start()==null){
            JsfUtil.addErrorMessage("上传开始日期不能为空！");
            return ;
        }
        if(searchEntity.getVisit_time_end()==null){
            JsfUtil.addErrorMessage("上传结束日期不能为空！");
            return ;
        }
        if(searchEntity.getVisit_time_start()!=null&&searchEntity.getVisit_time_end()!=null&&
                searchEntity.getVisit_time_start().after(searchEntity.getVisit_time_end())){
            JsfUtil.addErrorMessage("上传开始日期应小于等于上传结束日期！");
            return;
        }
        if(searchEntity.getVisit_time_end()!=null){
            String formatDate = DateUtils.formatDate(searchEntity.getVisit_time_end(), "yyyy-MM-dd");
            searchEntity.setVisit_time_end(DateUtils.parseDate(formatDate+" 23:59:59"));
        }
        super.searchAction();
    }

    @Override
    protected void processData(List<LogBhkServiceCrpt> records) {
        if(records!=null&&records.size()>0){
            for (LogBhkServiceCrpt record : records) {
                if("0".equals(record.getUpload_tag())){
                    String error_msg = record.getError_msg();
                    record.setError_msg_temp(error_msg);
                    if(error_msg!=null&&error_msg.length()>10){
                        record.setError_msg_temp(error_msg.substring(0,10)+"...");
                    }
                }
            }
        }

    }


    /**
     * @Description : 方法描述: 列表查询语句
     * @MethodAuthor: anjing
     * @Date : 2019/5/8 11:20
     **/
    protected Criteria buildCriteria() {
        List<Criteria> criteriaList = new ArrayList<>();

        //上传地区
        String zone_gb = searchEntity.getZone_gb();
        if(StringUtils.isNotBlank(zone_gb)){
            criteriaList.add(Criteria.where("zone_gb").regex("^"+ ZoneUtil.zoneSelect(zone_gb)+".*"));
        }
        //上传机构
        String unit_code = searchEntity.getUnit_code();
        if(StringUtils.isNotBlank(unit_code)){
            criteriaList.add(Criteria.where("unit_code").is(unit_code));
        }
        //上传日期
        Date visit_time_start = searchEntity.getVisit_time_start();
        if(visit_time_start!=null){
            criteriaList.add(Criteria.where("visit_time").gte(visit_time_start));
        }
        Date visit_time_end = searchEntity.getVisit_time_end();
        if(visit_time_end!=null){
            criteriaList.add(Criteria.where("visit_time").lte(visit_time_end));
        }
        //企业名称
        String crpt_name = searchEntity.getCrpt_name();
        if(StringUtils.isNotBlank(crpt_name)){
            criteriaList.add(Criteria.where("crpt_name").is(crpt_name));
        }
        //社会信用代码
        String credit_code = searchEntity.getCredit_code();
        if(StringUtils.isNotBlank(credit_code)){
            //criteriaList.add(Criteria.where("credit_code").regex(credit_code,"i"));
            criteriaList.add(Criteria.where("credit_code").is(credit_code));
        }
        //上传标记
        if(uploadTags!=null&&uploadTags.size()==1){
            criteriaList.add(Criteria.where("upload_tag").is(uploadTags.get(0)));
        }
        //处理状态
        if(dealStates!=null&&dealStates.size()==1){
            criteriaList.add(Criteria.where("deal_state").is(dealStates.get(0)));
        }

        Criteria criteria = new Criteria().andOperator((Criteria[]) criteriaList.toArray(new Criteria[criteriaList.size()]));
        return criteria;
    }

    @Override
    public Query buildQuery() {
        Query query = new Query();
        query.addCriteria(this.buildCriteria());
        //上传时间降序
        super.mongoPage.setDesc("visit_time");
        return query;
    }

    /***
     * <p>方法描述: 更新状态</p>
     *
     * @MethodAuthor mxp,2018/12/19,updateStateAction
     */
    public void updateStateAction(){
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("rid").is(searchEntity.getRid()));
            Update update = new Update();
            update.set("deal_state","1");
            mongoService.updateFirst(query, update, LogBhkServiceCrpt.class);
            entity.setDeal_state("1");
            this.searchAction();
            JsfUtil.addSuccessMessage("操作成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("操作失败！");
            e.printStackTrace();
        }
    }


    /***
     * <p>方法描述: 更新状态状态</p>
     *
     * @MethodAuthor mxp,2018/12/19,batchUpdateStateAction
     */
    public void batchUpdateStateAction(){
        try {
            if(selectEntitys==null||selectEntitys.size()==0){
                JsfUtil.addErrorMessage("请选择数据！");
                return ;
            }
            List<String> ids = new ArrayList<>();
            for (LogBhkServiceCrpt selectEntity : selectEntitys) {
                if("0".equals(selectEntity.getDeal_state())){
                    ids.add(selectEntity.getRid());
                }
            }
            if(ids.size()==0){
                JsfUtil.addErrorMessage("请选择含有未阅的数据！");
                return ;
            }

            Query query = new Query();
            query.addCriteria(Criteria.where("rid").in(ids));
            Update update = new Update();
            update.set("deal_state","1");
            mongoService.updateMulti(query, update, LogBhkServiceCrpt.class);
            selectEntitys = new ArrayList<>();
            this.searchAction();
            JsfUtil.addSuccessMessage("操作成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("操作失败！");
            e.printStackTrace();
        }
    }




    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }
    /***
     * @Description : 编辑
     * @MethodAuthor: mxp
     * @Date : 2019/5/20 14:41
     */
    @Override
    public void modInit() {
        if(entity==null){
            return;
        }
        super.modInit(entity.getMain_id(),entity.getRid());
    }

    @Override
    public void saveAction() {

    }


    /***
     * @Description : 导出
     * @MethodAuthor: mxp
     * @Date : 2019/5/20 16:47
     */
    public DefaultStreamedContent export(){
        String fileName="企业日志";
        // 初始化标题
        HSSFWorkbook wb = super.initTitle(fileName, 11);
        // 初始化第二行标题
        this.initTitle2(wb);
        // 组装数据导出
        boolean b = initExportData(wb);
        if(!b){
            return null;
        }
        return super.export(fileName,wb);
    }

    protected boolean initExportData(HSSFWorkbook wb) {
        //先查总共有多少条数据
        Query query = this.buildQuery();
        long count = mongoService.count(query, LogBhkServiceCrpt.class);
        if(count>1000000){
            JsfUtil.addErrorMessage("导出数据大于1百万，无法导出！");
            return false;
        }
        //每次查询10000条
        int  size = 10000;
        long num = count/size;
        if(count%size!=0){
            num++;
        }
        HSSFSheet sheet = wb.getSheetAt(0);
        MongoPage2<LogBhkServiceCrpt> mongoPage = new MongoPage2<>();
        mongoPage.setSize(size);//因为下面分页查询每次多查询出1条
        mongoPage.setDesc("visit_time");
        MongoPage2<LogBhkServiceCrpt> mongoPage2;
        List<LogBhkServiceCrpt> records;
        int rowNum=1;
        for (int i = 1; i <= num; i++) {
            mongoPage.setCurrent(i);
            mongoPage2 = mongoService.pageListCommon(mongoPage, query, LogBhkServiceCrpt.class);
            records = mongoPage2.getRecords();
            if(records!=null&&records.size()>0){
                rowNum = (i-1)*size;
                for (LogBhkServiceCrpt record : records) {
                    rowNum++;
                    HSSFRow row2 = sheet.createRow(rowNum+1);
                    HSSFCell cell0 = row2.createCell(0);
                    cell0.setCellValue(uploadZoneMap.get(record.getZone_gb()));
                    HSSFCell cell1 = row2.createCell(1);
                    cell1.setCellValue(uploadUnitMap.get(record.getUnit_code()));
                    HSSFCell cell2 = row2.createCell(2);
                    cell2.setCellValue(record.getCrpt_name());
                    HSSFCell cell3 = row2.createCell(3);
                    cell3.setCellValue(record.getCredit_code());
                    HSSFCell cell4 = row2.createCell(4);
                    cell4.setCellValue(DateUtils.formatDate(record.getVisit_time(),"yyyy-MM-dd HH:mm:ss"));
                    HSSFCell cell5 = row2.createCell(5);
                    cell5.setCellValue("1".equals(record.getUpload_tag())?"成功":"失败");
                    HSSFCell cell6 = row2.createCell(6);
                    cell6.setCellValue("0".equals(record.getUpload_tag())?"未阅":"1".equals(record.getUpload_tag())?"已阅":"无需处理");
                    HSSFCell cell7 = row2.createCell(7);
                    cell7.setCellValue(record.getError_msg());

                }

            }
        }
        super.export("企业日志",wb);
        return true;
    }

    @Override
    protected void initTitle2(HSSFWorkbook wb) {
        HSSFSheet sheet = wb.getSheetAt(0);
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(CellStyle.ALIGN_CENTER);// 左右居中
        cellStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        // 表列名称
        HSSFRow row2 = sheet.createRow(1);
        HSSFCell cell0 = row2.createCell(0);
        cell0.setCellValue("地区");
        cell0.setCellStyle(cellStyle);
        sheet.setColumnWidth(0, 3000);
        HSSFCell cell1 = row2.createCell(1);
        cell1.setCellValue("上传机构名称");
        cell1.setCellStyle(cellStyle);
        sheet.setColumnWidth(1, 4500);
        HSSFCell cell2 = row2.createCell(2);
        cell2.setCellValue("企业名称");
        cell2.setCellStyle(cellStyle);
        sheet.setColumnWidth(2, 5500);
        HSSFCell cell3 = row2.createCell(3);
        cell3.setCellValue("社会信用代码");
        cell3.setCellStyle(cellStyle);
        sheet.setColumnWidth(3, 4000);
        HSSFCell cell4 = row2.createCell(4);
        cell4.setCellValue("上传时间");
        cell4.setCellStyle(cellStyle);
        sheet.setColumnWidth(4, 5000);
        HSSFCell cell5 = row2.createCell(5);
        cell5.setCellValue("上传标记");
        cell5.setCellStyle(cellStyle);
        sheet.setColumnWidth(5, 3000);
        HSSFCell cell6 = row2.createCell(6);
        cell6.setCellValue("状态");
        cell6.setCellStyle(cellStyle);
        sheet.setColumnWidth(6, 4000);
        HSSFCell cell7 = row2.createCell(7);
        cell7.setCellValue("失败原因");
        cell7.setCellStyle(cellStyle);
        sheet.setColumnWidth(7, 4000);
        HSSFCell cell8 = row2.createCell(8);
    }


    public LogBhkServiceCrpt getSearchEntity() {
        return searchEntity;
    }

    public void setSearchEntity(LogBhkServiceCrpt searchEntity) {
        this.searchEntity = searchEntity;
    }

    public LogBhkServiceCrpt getEntity() {
        return entity;
    }

    public void setEntity(LogBhkServiceCrpt entity) {
        this.entity = entity;
    }

    public List<LogBhkServiceCrpt> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<LogBhkServiceCrpt> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }
}