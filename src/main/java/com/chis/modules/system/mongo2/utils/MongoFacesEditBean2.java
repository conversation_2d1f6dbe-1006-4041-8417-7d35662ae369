package com.chis.modules.system.mongo2.utils;

import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.mongo.service.MongoService;
import com.chis.modules.system.mongo2.entity.MongoPage2;
import com.chis.modules.system.web.FacesBean;
import org.springframework.data.mongodb.core.query.Query;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;


/**
 * @Description : 分页
 * @ClassAuthor : anjing
 * @Date : 2019/5/8 10:49
 **/
public abstract class MongoFacesEditBean2<T> extends FacesBean {

    private MongoService mongoService = SpringContextHolder.getBean(MongoService.class);

    /**当前活动的标签，从0开始，默认为0,0:主界面1：添加修改界面2：查看界面*/
    private int activeTab;
    /**表格的ID*/
    private static final String TABLE_ID = "mainForm:dataTable";
    protected MongoPage2<T> mongoPage = new MongoPage2<>();
    private Query query;
    private Class<T> tClass;

    public MongoFacesEditBean2() {
        Type t = getClass().getGenericSuperclass();
        ParameterizedType p = (ParameterizedType) t ;
        tClass = (Class<T>) p.getActualTypeArguments()[0];
    }

    /**
     * 查询操作，如果不满足，可重写
     */
    public void searchAction() {
        mongoPage.init();
        query = this.buildQuery();
        initPageAction();
    }

    /***
     * @Description : 初始化分页数据
     * @MethodAuthor: mxp
     * @Date : 2019/5/18 13:41
     */
    private void initPageAction() {
        mongoPage = mongoService.pageList(mongoPage, query, tClass);
        this.processData(mongoPage.getRecords());
    }

    /***
     * @Description : 对查出来的数据进行额外的处理
     * @MethodAuthor: mxp
     * @Date : 2019/5/18 13:48
     */
    protected abstract void processData(List<T> records);

    /***
     * @Description : 下一页
     * @MethodAuthor: mxp
     * @Date : 2019/5/18 13:38
     */
    public  void nextPageAction(){
        if(mongoPage.hasNext()){
            mongoPage.setCurrent(mongoPage.getCurrent()+1);
            this.initPageAction();
        }

    }
    /***
     * @Description : 上一页
     * @MethodAuthor: mxp
     * @Date : 2019/5/18 13:38
     */
    public  void previousPageAction(){
        if(mongoPage.hasPrevious()){
            mongoPage.setCurrent(mongoPage.getCurrent()-1);
            this.initPageAction();
        }
    }

    /***
     * @Description : 显示分页信息：总条数
     * @MethodAuthor: mxp
     * @Date : 2019/5/18 15:05
     */
    public void showTotalAction(){
        mongoPage.setTotal(mongoService.count(query, tClass));
        mongoPage.setShowInfo(true);
    }






    /**
     * 添加初始化
     */
    public void addInitAction() {
        this.addInit();
        this.forwardEditPage();
    }

    /**
     * 修改初始化
     */
    public void modInitAction() {
        this.modInit();
        this.forwardEditPage();
    }

    /**
     * 查看初始化
     */
    public void viewInitAction() {
        this.viewInit();
        this.forwardViewPage();
    }

    /**
     * 转向修改界面
     */
    public void forwardEditPage() {
        this.activeTab = 1;
    }

    /**
     * 转向到查看界面
     */
    public void forwardViewPage() { this.activeTab = 2; }

    /**
     * 转向到其他界面
     */
    public void forwardOtherPage() { this.activeTab = 3; }

    /**
     * 返回首页面
     */
    public void backAction() {
        this.activeTab = 0;
    }

    /**
     * 跳转到其他查看页面
     */
    public void forwardOtherViewPage() {this.activeTab = 4;}

    /**
     * 具体的添加初始化
     */
    public abstract void addInit();

    /**
     * 具体的修改初始化
     */
    public abstract void viewInit();

    /**
     * 具体的查看初始化
     * @param mainId
     * @param rid
     */
    public abstract void modInit();

    /**
     * 具体的添加
     */
    public abstract void saveAction();

    /**
     * 组织hql查询语句，同时有参数的话，向paramMap里面放值
     * @return 0-查询记录的hql语句 1-查询记录数的hql语句
     */
    public abstract Query buildQuery();



    //gets and sets
    public int getActiveTab() {
        return activeTab;
    }

    public void setActiveTab(int activeTab) {
        this.activeTab = activeTab;
    }

    public MongoPage2<T> getMongoPage() {
        return mongoPage;
    }

    public void setMongoPage(MongoPage2<T> mongoPage) {
        this.mongoPage = mongoPage;
    }
}
