package com.chis.modules.system.web;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.AesEncryptUtils;
import com.chis.common.utils.CacheUtils;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;
/**
 * @Description: 第三方平台登录信息验证
 * 配合JumpToOtherServlet
 * JumpToOtherServlet请求第三方平台后，第三方平台通过当前接口验证登录信息，验证通过，返回登录用户信息给第三方平台
 * @ClassAuthor pw,2022年04月2日,SSOValidateUserInfo
 */
@WebServlet(name="SSOValidateUserInfo",value="/sso/validateUserInfo")
public class SSOValidateUserInfo extends HttpServlet {
    private static final long serialVersionUID = -8187521037684935712L;

    private static final Logger logger = LoggerFactory.getLogger(SSOValidateUserInfo.class);
    @Override
    public void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        this.doPost(req, resp);
    }

    @Override
    public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("text/json; charset=utf-8");
        String cacheName = "OTHER_SYSTEM_JUMP";
        String uuid = request.getParameter("ticket");
        logger.info("第三方请求，UUID：{}",uuid);
        System.out.println("第三方请求，UUID："+uuid);
        Map<String,Object> resultMap = StringUtils.isBlank(uuid) ? null :
                (Map<String,Object>)CacheUtils.get(cacheName, uuid);
        if(null == resultMap){
            resultMap = new HashMap<>();
            resultMap.put("type", "99");
            resultMap.put("mess", "失败");
        }else{
            //缓存移除
            CacheUtils.remove(cacheName, uuid);
        }
        //加入时间戳 避免每次返回都一样
        resultMap.put("timestamp", System.currentTimeMillis());
        String requestJson = JSON.toJSONString(resultMap);
        try{
            String debug = PropertyUtils.getValue("encrypt.debug");
            String encryptKey = PropertyUtils.getValue("encrypt.thirdpart.key");
            if (!"true".equals(debug)) {
                System.out.println("不加密："+requestJson);
                logger.info("第三方请求，UUID：{} 不加密：{}",uuid, requestJson);
                //加密数据
                requestJson = AesEncryptUtils.aesEncrypt(requestJson, encryptKey);
                System.out.println("加密："+requestJson);
                logger.info("第三方请求，UUID：{} 加密：{}",uuid, requestJson);
            }
        }catch(Exception e){
            e.printStackTrace();
        }
        PrintWriter out = response.getWriter();
        out.println(requestJson);
        out.flush();
        out.close();
    }
}
