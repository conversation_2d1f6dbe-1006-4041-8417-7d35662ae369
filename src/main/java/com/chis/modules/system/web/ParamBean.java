package com.chis.modules.system.web;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSystemParam;
import com.chis.modules.system.enumn.DictType;
import com.chis.modules.system.enumn.FieldType;
import com.chis.modules.system.enumn.IEnum;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.logic.DateBean;
import com.chis.modules.system.logic.ListBean;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import org.primefaces.component.calendar.Calendar;
import org.primefaces.component.column.Column;
import org.primefaces.component.inputtext.InputText;
import org.primefaces.component.outputpanel.OutputPanel;
import org.primefaces.component.panel.Panel;
import org.primefaces.component.panelgrid.PanelGrid;
import org.primefaces.component.row.Row;
import org.primefaces.component.selectmanycheckbox.SelectManyCheckbox;
import org.primefaces.component.selectonemenu.SelectOneMenu;

import javax.el.ValueExpression;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.component.UIInput;
import javax.faces.component.UISelectItem;
import javax.faces.component.html.HtmlOutputText;
import javax.faces.context.FacesContext;
import java.text.ParseException;
import java.util.*;

/**
* 系统参数
* 如果是单选，多选参数，字典数据有三种类型：
 *    1-数据，多组数据之间用##符号隔开，lable与value之间用@@隔开，label在前
 *    2-SQL语句，0-lable 1-value
 *    3-枚举类，需要实现IEnum接口的枚举类
 *
 * 多选存的数据，多个用##隔开
 *
 * 能看所有模块的参数
 *
* <AUTHOR>
 *
 * @修改人 wlj
 * @修改时间 2014-10-11
 * @修改内容 “通用”模块的系统参数只允许0000用户查看和修改
*/
@ManagedBean(name="paramBean")
@ViewScoped
public class ParamBean extends FacesBean {

	private static final long serialVersionUID = -738445454536625063L;
	/**存在session中的对象*/
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/**ejb session bean*/
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    /**查询条件：参数名称*/
    private String searchParamName;
    /**查询条件：参数描述*/
    private String searchParamDesc;
    /**全局bean*/
    private ApplicationBean applicationBean = (ApplicationBean) getBean("applicationBean");
    /**
     * 两列表格的左边列样式
     */
    private static final String TEXT_ALIGN_RIGHT = "width:40%;text-align:right;";
    /**
     * 两列表格的右边列样式
     */
    private static final String TEXT_ALIGN_LEFT = "width:60%;";
    /**
     * 宽度200px的样式
     */
    private static final String WIDTH_200 = "width:200px;";
    /**
     * 最大长度2000
     */
    private static final Integer MAXLENGTH_2000 = 2000;
    /**
     * 下拉框的默认值
     */
    private static final String SELECT_DEFAULT = "--请选择--";
    /**
     * 多组数据的分隔符
     */
    private static final String SPLIT_STR = "##";
    /**
     * label,value的分隔符
     */
    private static final String SPLIT_KEY_VALUE = "@@";
    /**
     * 日期格式
     */
    private static final String CALENDAR_PATTERN = "yyyy-MM-dd";
    /**
     * 复选框一行最多几个
     */
    private static final int CHECKBOX_COLUMN = 5;

    /**
     * 上边距
     */
    private static final String MARGIN_TOP = "margin-top: 5px;";

    /**
     * 业务模块的布局表格
     */
    private OutputPanel businessPanel = (OutputPanel) JsfUtil.getApplication().createComponent(OutputPanel.COMPONENT_TYPE);
    /**
     * 业务系统参数控件绑定的值集合
     */
    private Map<String, Object> businessValueMap = new HashMap<String, Object>();

	public ParamBean() {
        this.searchAction();
	}

    public void searchAction() {
        this.businessValueMap.clear();
        Map<SystemType, List<TsSystemParam>> dataMap = this.systemModuleService.searchSystemParam(this.searchParamName, this.searchParamDesc);
        for (SystemType key : dataMap.keySet()) {
            for (int i = dataMap.get(key).size() - 1; i >= 0; i--) {
                TsSystemParam tsSystemParam = dataMap.get(key).get(i);
                if ("SYSTEM_MODULES".equals(tsSystemParam.getParamName())){
                    dataMap.get(key).remove(i);
                }
            }
        }
        this.buildOutputPanel(this.businessPanel, dataMap, this.businessValueMap);
        FacesContext.getCurrentInstance().renderResponse();
    }

    /**
     * 构建outputPanel，依次页面结构层次是：outputpanel, panel, panelGrid, row, column,控件
     * @param panel outputPanel，控件容器
     * @param dataMap 数据
     * @param valueMap 用于存放所有参数的键值
     *
     * @修改人 wlj
     * @修改时间 2014-10-11
     * @修改内容 “通用”模块的系统参数只允许0000用户查看和修改
     *
     */
    private void buildOutputPanel(OutputPanel panel, Map<SystemType, List<TsSystemParam>> dataMap, Map<String, Object> valueMap) {
        panel.getChildren().clear();
        String el = "businessValueMap";
        if(null != dataMap && dataMap.size() > 0) {
            Set<SystemType> sts = dataMap.keySet();
            /**
             * 超级管理员只负责comm类型的参数
             * 管理员只负责业务相关的参数
             */
            for(SystemType st: sts) {
            	if(st != null) {
            		List<TsSystemParam> paramList = dataMap.get(st);
            		if(null != paramList && paramList.size() > 0) {
            			final Panel p = (Panel) JsfUtil.getApplication().createComponent(Panel.COMPONENT_TYPE);
            			p.setHeader(st.getTypeCN());
            			p.setStyle(MARGIN_TOP);
            			
            			final PanelGrid grid = (PanelGrid) JsfUtil.getApplication().createComponent(PanelGrid.COMPONENT_TYPE);
            			grid.setStyle("width:100%");
            			
            			for(TsSystemParam t: paramList) {
            				final Row row = (Row) JsfUtil.getApplication().createComponent(Row.COMPONENT_TYPE);
            				
            				/**
            				 * 参数描述
            				 */
            				final Column nameColumn = (Column) JsfUtil.getApplication().createComponent(Column.COMPONENT_TYPE);
            				nameColumn.setStyle(TEXT_ALIGN_RIGHT);
            				
            				final HtmlOutputText nameText = (HtmlOutputText)JsfUtil.getApplication().createComponent(HtmlOutputText.COMPONENT_TYPE);
            				nameText.setValue(t.getParamRmk() + "(" + t.getParamName() + ")：");
            				nameColumn.getChildren().add(nameText);
            				
            				/**
            				 * 参数值
            				 */
            				final Column valueColumn = (Column) JsfUtil.getApplication().createComponent(Column.COMPONENT_TYPE);
            				valueColumn.setStyle(TEXT_ALIGN_LEFT);
            				
            				if(FieldType.STRINGS.equals(t.getFieldType())) {
            					this.stringField(valueColumn, t, valueMap, el);
            				}else if(FieldType.SELECT_ONE_MENU.equals(t.getFieldType())) {
            					this.selectOneMenuField(valueColumn, t, valueMap, el);
            				}else if(FieldType.SELECT_MANY.equals(t.getFieldType())) {
            					this.selectManyCheckboxField(valueColumn, t, valueMap, el);
            				}else if(FieldType.DATE.equals(t.getFieldType())) {
            					this.dateField(valueColumn, t, valueMap, el);
            				}
            				
            				row.getChildren().add(nameColumn);
            				row.getChildren().add(valueColumn);
            				
            				grid.getChildren().add(row);
            			}
            			p.getChildren().add(grid);
            			panel.getChildren().add(p);
            		}
            	}
            }
        }
    }

    /**
     * 添加string控件
     * @param valueColumn 列
     * @param t 参数对象
     * @param valueMap 绑定数据
     * @param el 绑定数据名称
     */
    private void stringField(Column valueColumn, TsSystemParam t, Map<String, Object> valueMap, String el) {
        valueMap.put(t.getParamName(), t.getParamValue());

        final InputText valueInputText = (InputText) JsfUtil.getApplication().createComponent(InputText.COMPONENT_TYPE);
        valueInputText.setRequired(Boolean.TRUE);
        valueInputText.setRequiredMessage("参数值：" + t.getParamName() + "不允许为空！");
        valueInputText.setStyle(WIDTH_200);
        valueInputText.setMaxlength(MAXLENGTH_2000);
        final String binding = "#{paramBean." + el + "[\"" + t.getParamName() + "\"]}";
        final ValueExpression nameExpression = JsfUtil.getApplication().getExpressionFactory().createValueExpression(FacesContext.getCurrentInstance().getELContext(), binding, String.class);
        valueInputText.setValueExpression("value",nameExpression);

        valueColumn.getChildren().add(valueInputText);
    }

    /**
     * 添加下拉单选框
     * @param valueColumn 列
     * @param t 参数对象
     * @param valueMap 绑定数据
     * @param el 绑定数据名称
     */
    private void selectOneMenuField(Column valueColumn, TsSystemParam t, Map<String, Object> valueMap, String el) {
        valueMap.put(t.getParamName(), t.getParamValue());

        final SelectOneMenu selectOneMenu = (SelectOneMenu) JsfUtil.getApplication().createComponent(SelectOneMenu.COMPONENT_TYPE);
        selectOneMenu.setRequired(Boolean.TRUE);
        selectOneMenu.setRequiredMessage("参数值：" + t.getParamName() + "不允许为空！");
        final String binding = "#{paramBean." + el + "[\"" + t.getParamName() + "\"]}";
        final ValueExpression nameExpression = JsfUtil.getApplication().getExpressionFactory().createValueExpression(FacesContext.getCurrentInstance().getELContext(), binding, String.class);
        selectOneMenu.setValueExpression("value",nameExpression);

        final UISelectItem selectItem = (UISelectItem) JsfUtil.getApplication().createComponent(UISelectItem.COMPONENT_TYPE);
        selectItem.setItemLabel(SELECT_DEFAULT);
        selectItem.setItemValue("-1");
        selectOneMenu.getChildren().add(selectItem);

        if(null != t.getDictType()) {
            if(DictType.SELF_DEFINE.equals(t.getDictType())) {
                //自定义
                if(StringUtils.isNotBlank(t.getDictValue())) {
                    this.addSelectItemByValue(t, selectOneMenu);
                }
            }else if(DictType.ENUMN.equals(t.getDictType())) {
                //枚举
                if(StringUtils.isNotBlank(t.getDictValue())) {
                    this.addSelectItemByEnumn(t, selectOneMenu);
                }
            }else {
                //sql
                this.addSelectItemBySQL(t, selectOneMenu);
            }
        }
        valueColumn.getChildren().add(selectOneMenu);
    }

    /**
     * 添加多选框
     * @param valueColumn 列
     * @param t 参数对象
     * @param valueMap 绑定数据
     * @param el 绑定数据名称
     */
    private void selectManyCheckboxField(Column valueColumn, TsSystemParam t, Map<String, Object> valueMap, String el) {
        List<String> bindList = StringUtils.string2list(t.getParamValue(), SPLIT_STR);
        ListBean lv = new ListBean(bindList);
        valueMap.put(t.getParamName(), lv);

        final SelectManyCheckbox selectManyCheckbox = (SelectManyCheckbox) JsfUtil.getApplication().createComponent(SelectManyCheckbox.COMPONENT_TYPE);
        selectManyCheckbox.setLayout("grid");
        selectManyCheckbox.setColumns(CHECKBOX_COLUMN);
        selectManyCheckbox.setRequired(Boolean.TRUE);
        selectManyCheckbox.setRequiredMessage("参数值：" + t.getParamName() + "不允许为空！");
        final String binding = "#{paramBean." + el + "['" + t.getParamName() + "'].valueList}";
        final ValueExpression nameExpression = JsfUtil.getApplication().getExpressionFactory().createValueExpression(FacesContext.getCurrentInstance().getELContext(), binding, List.class);
        selectManyCheckbox.setValueExpression("value",nameExpression);

        if(null != t.getDictType()) {
            if(DictType.SELF_DEFINE.equals(t.getDictType())) {
                //自定义
                if(StringUtils.isNotBlank(t.getDictValue())) {
                    this.addSelectItemByValue(t, selectManyCheckbox);
                }
            }else if(DictType.ENUMN.equals(t.getDictType())) {
                //枚举
                if(StringUtils.isNotBlank(t.getDictValue())) {
                    this.addSelectItemByEnumn(t, selectManyCheckbox);
                }
            }else {
                //sql
                this.addSelectItemBySQL(t, selectManyCheckbox);
            }
        }
        valueColumn.getChildren().add(selectManyCheckbox);
    }

    /**
     * 添加日期控件
     * @param valueColumn 列
     * @param t 参数对象
     * @param valueMap 绑定数据
     * @param el 绑定数据名称
     */
    private void dateField(Column valueColumn, TsSystemParam t, Map<String, Object> valueMap, String el) {
        DateBean dateBean = new DateBean();
        if(StringUtils.isNotBlank(t.getParamValue())) {
            try {
				dateBean.setValueDate(DateUtils.parseDate(t.getParamValue(), "yyyy-MM-dd"));
			} catch (ParseException e) {
				e.printStackTrace();
				throw new RuntimeException(e);
			}
        }
        valueMap.put(t.getParamName(), dateBean);

        final Calendar calendar = (Calendar) JsfUtil.getApplication().createComponent(Calendar.COMPONENT_TYPE);
        calendar.setRequired(Boolean.TRUE);
        calendar.setRequiredMessage("参数值：" + t.getParamName() + "不允许为空！");
        calendar.setPattern(CALENDAR_PATTERN);
        final String binding = "#{paramBean." + el + "['" + t.getParamName() + "'].valueDate}";
        final ValueExpression nameExpression = JsfUtil.getApplication().getExpressionFactory().createValueExpression(FacesContext.getCurrentInstance().getELContext(), binding, Date.class);
        calendar.setValueExpression("value",nameExpression);

        valueColumn.getChildren().add(calendar);

    }

    /**
     * 给单选、多选等类型的控件添加选项（数据集）
     * @param t 参数对象
     * @param uiInput  单选、多选等类型的控件
     */
    private void addSelectItemByValue(TsSystemParam t, UIInput uiInput) {
        if(StringUtils.isNotBlank(t.getDictValue())) {
            if(t.getDictValue().indexOf(SPLIT_STR) > -1) {
                //多个
                String[] arr = t.getDictValue().split(SPLIT_STR);
                for(String s: arr) {
                    if(s.indexOf(SPLIT_KEY_VALUE) > -1) {
                        String[] arrs = s.split(SPLIT_KEY_VALUE);
                        final UISelectItem ui = (UISelectItem) JsfUtil.getApplication().createComponent(UISelectItem.COMPONENT_TYPE);
                        ui.setItemLabel(arrs[0]);
                        ui.setItemValue(arrs[1]);
                        uiInput.getChildren().add(ui);
                    }
                }
            }else {
                if(t.getDictValue().indexOf(SPLIT_KEY_VALUE) > -1) {
                    String[] arr = t.getDictValue().split(SPLIT_KEY_VALUE);
                    final UISelectItem ui = (UISelectItem) JsfUtil.getApplication().createComponent(UISelectItem.COMPONENT_TYPE);
                    ui.setItemLabel(arr[0]);
                    ui.setItemValue(arr[1]);
                    uiInput.getChildren().add(ui);
                }
            }
        }
    }

    /**
     * 给单选、多选等类型的控件添加选项（SQL语句）
     * @param t 参数对象
     * @param uiInput  单选、多选等类型的控件
     */
    private void addSelectItemBySQL(TsSystemParam t, UIInput uiInput) {
        List<Object[]> objsByItemSQL = this.systemModuleService.findObjsByItemSQL(t.getDictValue());
        if(null != objsByItemSQL) {
            for(Object[] o: objsByItemSQL) {
                final UISelectItem ui = (UISelectItem) JsfUtil.getApplication().createComponent(UISelectItem.COMPONENT_TYPE);
                ui.setItemLabel(o[0].toString());
                ui.setItemValue(o[1].toString());
                uiInput.getChildren().add(ui);
            }
        }
    }

    /**
     * 给单选、多选等类型的控件添加选项（枚举类）
     * @param t 参数对象
     * @param uiInput  单选、多选等类型的控件
     */
    private void addSelectItemByEnumn(TsSystemParam t, UIInput uiInput) {
        try {
            Class clazz = Class.forName(t.getDictValue());
            Object[] obs = clazz.getEnumConstants();
            for(Object o: obs) {
                IEnum e = (IEnum)o;
                final UISelectItem ui = (UISelectItem) JsfUtil.getApplication().createComponent(UISelectItem.COMPONENT_TYPE);
                ui.setItemLabel(e.getTypeCN());
                ui.setItemValue(e.getTypeNo());
                uiInput.getChildren().add(ui);
            }
        }catch (Exception e) {
        	e.printStackTrace();
            //throw new RuntimeException(e);
        }
    }

	/**
	 * 保存
	 */
	public void saveAction() {
        Map<String, String> convertBusinessMap = convertMap(this.businessValueMap);

        String msg = this.verfyValue(convertBusinessMap);
        if(StringUtils.isNotBlank(msg)) {
            JsfUtil.addErrorMessage(msg);
            return;
        }

        this.systemModuleService.updateParams(convertBusinessMap);
        this.searchAction();
        JsfUtil.addSuccessMessage("修改成功！");
    }

    private String verfyValue(Map<String, String> map) {
        if(null != map && map.size() > 0) {
            Set<String> set = map.keySet();
            for(String s:set) {
                String value = map.get(s);
                if(value.length() > MAXLENGTH_2000) {
                    return "参数值：" + s + "太大！";
                }
            }
        }
        return null;
    }

    private static Map<String, String> convertMap(Map<String, Object> map) {
        if(null != map && map.size() > 0) {
            Map<String, String> stringMap = new HashMap<String, String>();
            Set<Map.Entry<String, Object>> entrySet = map.entrySet();
            for(Map.Entry<String, Object> entry:entrySet) {
                Object value = entry.getValue();
                if(value instanceof DateBean) {
                    DateBean db = (DateBean)value;
                    stringMap.put(entry.getKey(), DateUtils.formatDate(db.getValueDate()));
                }else if(value instanceof ListBean) {
                    ListBean lv = (ListBean)value;
                    stringMap.put(entry.getKey(), StringUtils.list2string(lv.getValueList(), SPLIT_STR));
                }else {
                    stringMap.put(entry.getKey(), value.toString());
                }
            }
            return stringMap;
        }else {
            return null;
        }
    }

    //getters and setters

    public String getSearchParamDesc() {
        return searchParamDesc;
    }

    public void setSearchParamDesc(String searchParamDesc) {
        this.searchParamDesc = searchParamDesc;
    }

    public String getSearchParamName() {
        return searchParamName;
    }

    public void setSearchParamName(String searchParamName) {
        this.searchParamName = searchParamName;
    }

    public Map<String, Object> getBusinessValueMap() {
        return businessValueMap;
    }

    public void setBusinessValueMap(Map<String, Object> businessValueMap) {
        this.businessValueMap = businessValueMap;
    }

    public OutputPanel getBusinessPanel() {
        return businessPanel;
    }

    public void setBusinessPanel(OutputPanel businessPanel) {
        this.businessPanel = businessPanel;
    }
}
