package com.chis.modules.system.web;

import java.util.Date;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.ietf.jgss.Oid;
import org.primefaces.context.RequestContext;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsProTempl;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * 问卷选项模版
 * 
 * <AUTHOR>
 * 
 */
@ManagedBean
@ViewScoped
public class ProTemplBean extends FacesSimpleBean {
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	private SystemModuleServiceImpl serviceImpl = SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private Integer rid;
	private TsProTempl proTempl = new TsProTempl();
	private String searchName;
	private String searchCode;
	/**
	 * 
	 */
	private static final long serialVersionUID = 4281037374262000176L;

	@PostConstruct
	public void Init() {
		searchAction();
	}

	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder();
		sb.append(" FROM TsProTempl t WHERE 1=1");
		if (StringUtils.isNotBlank(searchName)) {
			sb.append(" AND t.tmplName like '").append(searchName).append("%'");
		}
		if (StringUtils.isNotBlank(searchCode)) {
			sb.append(" AND t.tmplCode like '").append(searchCode).append("%'");
		}
		sb.append(" order by t.tmplCode");
		StringBuilder countSql = new StringBuilder();
		countSql.append("select count(t) ").append(sb.toString());
		StringBuilder searchSql = new StringBuilder();
		searchSql
				.append("SELECT new TsProTempl(t.rid,t.tmplName,t.tmplCode,t.tmplOpts) ")
				.append(sb.toString());
		return new String[] { sb.toString(), countSql.toString() };
	}

	public void saveAction() {
		try {
			if (serviceImpl.proTemplCodeIfExists(proTempl)) {
				JsfUtil.addErrorMessage("编码已存在!");
				return;
			}
			serviceImpl.saveOrUpdateProTempl(proTempl);
			JsfUtil.addSuccessMessage("保存成功");
			RequestContext.getCurrentInstance().execute(
					"PF('TemplEditDialog').hide();");
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("保存失败");
		}
	}

	public void addInit() {
		proTempl = new TsProTempl();
		proTempl.setCreateDate(new Date());
		proTempl.setCreateManid(sessionData.getUser().getRid());
	}

	public void deleteAction() {
		try {
			serviceImpl.deleteProTempl(rid);
			JsfUtil.addSuccessMessage("删除成功");
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("删除失败!");
		}
	}

	public void modInit() {
		proTempl = serviceImpl.findProTemplByRid(rid);
	}

	public SystemModuleServiceImpl getServiceImpl() {
		return serviceImpl;
	}

	public void setServiceImpl(SystemModuleServiceImpl serviceImpl) {
		this.serviceImpl = serviceImpl;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public TsProTempl getProTempl() {
		return proTempl;
	}

	public void setProTempl(TsProTempl proTempl) {
		this.proTempl = proTempl;
	}

	public String getSearchName() {
		return searchName;
	}

	public void setSearchName(String searchName) {
		this.searchName = searchName;
	}

	public String getSearchCode() {
		return searchCode;
	}

	public void setSearchCode(String searchCode) {
		this.searchCode = searchCode;
	}

}
