package com.chis.modules.system.web;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.xml.soap.Node;

import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.system.entity.RolePo;
import com.chis.modules.system.entity.RoleTypePo;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsUserRole;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.context.RequestContext;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.TreeNode;
import org.springframework.util.CollectionUtils;

/**
 * 用户管理
 *
 * <AUTHOR>
 * @修改人 wlj
 * @修改时间 2014-10-13
 * @修改内容 增加用户兼职功能
 */
@ManagedBean(name = "userNewBean")
@ViewScoped
public class UserNewBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = -738445454536625063L;
    /**
     * 存在session中的对象
     */
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    /**
     * ejb session bean
     */
    private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    /**
     * 用户对象
     */
    private TsUserInfo tsUserInfo = new TsUserInfo();
    /**
     * 用户对象rid
     */
    private Integer rid;
    /**
     * 查询条件：地区名称
     */
    private String searchZoneName;
    /**
     * 查询条件：地区编码
     */
    private String searchZoneCode;
    /**
     * 查询条件：地区级别
     */
    private String searchZoneType;
    /**
     * 查询条件：单位
     */
    private String searchUnitId;
    /**
     * 添加页面：单位下啦
     */
    private Map<String, String> searchUnitMap = new HashMap<String, String>(0);
    /**
     * 查询条件：用户姓名
     */
    private String searchUsername;
    /**
     * 查询条件：用户账号
     */
    private String searchUserNo;
    private Integer editZoneId;
    /**
     * 添加页面的地区名称
     */
    private String editZoneName;
    /**
     * 添加页面的地区编码
     */
    private String editZoneCode;
    /**
     * 添加页面的地区级别
     */
    private String editZoneType;
    /**
     * 添加页面：单位id
     */
    private String editUnitId;
    /**
     * 添加页面：单位下啦
     */
    private Map<String, String> editUnitMap = new HashMap<String, String>(0);

    private List<TsZone> zoneList;
    private String[] searchUserState;

    private boolean ifAdmin = Boolean.TRUE;

    private boolean ifYwRole = Boolean.FALSE;
    private List<RoleTypePo> roleTypeList;
    //初始密码
    private String initPassword;
    /** 角色类型码表*/
    private List<TsSimpleCode> roleCodeList;
    /** 角色类型名称*/
    private String selectOnRoleNames;
    /** 角色类型ID*/
    private String selectOnRoleIds;
    /** 弹出框 角色名称*/
    private Map<String, String> typeNameMap = new HashMap<String, String>(0);
    /** 弹出框 选中角色名称rid拼接的String */
    private String typeSelectRids;
    /** 弹出框 选中角色名称名称拼接的String */
    private String typeSelectNames;
    /** 角色类型树 */
    private TreeNode typeTree;
    /** 码表 */
    private List<TsSimpleCode> tsSimpleCodes;
    /**是否显示导出按钮*/
    private boolean ifShowExportBtn=Boolean.FALSE;
    /**导出数据*/
    private List<Object[]> dataList=new ArrayList<>();
    /**删除权限*/
    private boolean ifDel;


    public UserNewBean() {
        init();
    }

    public void init() {
        initPassword = PropertyUtils.getValue("initialPassword");
        TsUserInfo tsUserInfo = this.sessionData.getUser();
        if (tsUserInfo.getTsUnit().getFkByManagedZoneId() != null) {
            this.searchZoneCode = tsUserInfo.getTsUnit().getFkByManagedZoneId().getZoneGb();
            this.searchZoneName = tsUserInfo.getTsUnit().getFkByManagedZoneId().getZoneName();
            this.searchZoneType = tsUserInfo.getTsUnit().getFkByManagedZoneId().getZoneType().toString();
        } else {
            this.searchZoneCode = tsUserInfo.getTsUnit().getTsZone().getZoneGb();
            this.searchZoneName = tsUserInfo.getTsUnit().getTsZone().getZoneName();
            this.searchZoneType = tsUserInfo.getTsUnit().getTsZone().getZoneType().toString();
        }

        if (!tsUserInfo.getUserNo().equals(Constants.ADMIN)) {
            ifAdmin = Boolean.FALSE;
        }

        ifYwRole = this.systemModuleService.getUserIfyw(tsUserInfo.getRid());
        super.ifSQL = Boolean.TRUE;
        /**
         * 地区初始化
         */
        if (null == this.zoneList || this.zoneList.size() <= 0) {
            // 获取地区缓存数据
            this.zoneList = findZoneList(tsUserInfo.getTsUnit().getFkByManagedZoneId());
        }
        this.searchUnitMap = this.filterUnit(this.searchZoneCode, this.searchZoneType);

        searchUserState = new String[]{"1"};
        roleCodeList = commService.findSimpleCodesByTypeId("1201");
        initRoleTypeList();
        initType();
        //是否显示导出按钮
        if (Global.getBtnSet().contains("yhgl_export")) {
            ifShowExportBtn = true;
        }
        if (Global.getBtnSet().contains("yhgl_del")) {
            this.ifDel = true;
        }else {
            this.ifDel = false;
        }
        this.searchAction();
    }
    /**
     * @Description: 初始化弹出框角色名称
     *
     * @MethodAuthor gjy,2021年12月10日
     */
    private void initType() {
            this.tsSimpleCodes = new ArrayList<>();
            if(CollectionUtils.isEmpty(this.roleTypeList)){
                return;
            }
            for(int i=0;i<this.roleTypeList.size();i++){
                RoleTypePo roleTypePo = this.roleTypeList.get(i);
                TsSimpleCode fatherSimpleCode =new TsSimpleCode();
                fatherSimpleCode.setRid(roleTypePo.getRid());
                fatherSimpleCode.setCodeName(roleTypePo.getCodeName());
                fatherSimpleCode.setCodeNo(Integer.toString(i));
                fatherSimpleCode.setCodeLevelNo(Integer.toString(i));
                this.tsSimpleCodes.add(fatherSimpleCode);
                List<RolePo> roleNameList = roleTypePo.getRolePoList();
                if(!CollectionUtils.isEmpty(roleNameList)){
                    for(int j=0;j<roleNameList.size();j++){
                        RolePo rolePo = roleNameList.get(j);
                        TsSimpleCode childSimpleCode = new TsSimpleCode();
                        childSimpleCode.setRid(rolePo.getRid());
                        childSimpleCode.setCodeName(rolePo.getRoleName());
                        childSimpleCode.setCodeLevelNo(i+"."+j);
                        childSimpleCode.setCodeNo(Integer.toString(j));
                        this.tsSimpleCodes.add(childSimpleCode);
                    }
                }
            }
    }

    /**
     * 添加界面的 地区选中事件
     */
    public void onNodeSelect() {
        this.editUnitId = null;
        this.setEditUnitMap(this.filterUnit(this.editZoneCode, this.editZoneType));
    }


    /**
     * 删除
     */
    public void deleteAction() {
        String msg = this.systemModuleService.deleteUser(this.rid);
        if (StringUtils.isNotBlank(msg)) {
            JsfUtil.addErrorMessage(msg);
            return;
        }
        this.searchAction();
    }

    /**
     * 密码初始化
     */
    public void pwdInitAction() {
        this.systemModuleService.initUserPwd(this.rid);
        this.tsUserInfo.setPassword(new MD5Util().getMD5ofStr(PropertyUtils.getValue("initialPassword")));
        this.tsUserInfo.setIfModpsw((short) 0);
        this.tsUserInfo.setUploadTag(0);
        this.systemModuleService.updateUserLogTimes(tsUserInfo.getUserNo());
        JsfUtil.addSuccessMessage("密码初始化成功！");
    }

    /**
     * 停用
     */
    public void stopAction() {
        this.systemModuleService.updateUserState(this.rid, 0);
    }

    /**
     * 启用
     */
    public void startAction() {
        this.systemModuleService.updateUserState(this.rid, 1);
    }


    /**
     * 查询条件，地区树选择事件
     */
    public void onSearchNodeSelect() {
        this.searchUnitId = null;
        this.searchUnitMap = this.filterUnit(this.searchZoneCode, this.searchZoneType);
    }


    /**
     * 根据地区刷单位
     *
     * @param zoneCode 地区编码
     * @param zoneType 地区级别
     * @return 单位集合
     */
    private Map<String, String> filterUnit(String zoneCode, String zoneType) {
        TsUserInfo user = this.sessionData.getUser();
        Map<String, String> map = new LinkedHashMap<String, String>();
        List<TsUnit> list = this.systemModuleService.findUnitByZoneGbNew(this.ifAdmin, user.getTsUnit().getTsZone().getZoneGb(),
                user.getTsUnit().getRid(), zoneCode, zoneType);
        if (null != list && list.size() > 0) {
            for (TsUnit t : list) {
                map.put(t.getUnitname(), t.getRid().toString());
            }
        }
        return map;
    }

    @Override
    public void addInit() {
        TsUserInfo user = sessionData.getUser();
        //初始化一些条件
        this.tsUserInfo = new TsUserInfo();
        this.tsUserInfo.setUserType((short) 2);
        this.tsUserInfo.setCreateDate(new Date());
        this.tsUserInfo.setCreateManid(this.sessionData.getUser().getRid());
        this.tsUserInfo.setPassword(new MD5Util().getMD5ofStr(PropertyUtils.getValue("initialPassword")));
        this.tsUserInfo.setIfModpsw((short) 0);
        this.tsUserInfo.setIfReveal((short) 1);
        this.tsUserInfo.setValidBegDate(new Date());
        this.rid = null;
        this.editUnitId = null;

        if (null != user.getTsUnit().getFkByManagedZoneId()) {
            this.editZoneCode = user.getTsUnit().getFkByManagedZoneId().getZoneGb();
            this.editZoneName = user.getTsUnit().getFkByManagedZoneId().getZoneName();
            this.editZoneType = user.getTsUnit().getFkByManagedZoneId().getZoneType().toString();
        }

        /**
         * 初始化单位
         */
        this.editUnitMap = this.filterUnit(this.editZoneCode, this.editZoneType);
        initRoleTypeList();
    }

    @Override
    public void viewInit() {
        // TODO Auto-generated method stub

    }

    @Override
    public void modInit() {
        initRoleTypeList();

        this.tsUserInfo = this.systemModuleService.findUser(this.rid);
        this.tsUserInfo.setModifyDate(new Date());
        this.tsUserInfo.setModifyManid(this.sessionData.getUser().getRid());
        if (tsUserInfo.getValidBegDate() == null) {
            tsUserInfo.setValidBegDate(new Date());
        }

        this.editZoneCode = this.tsUserInfo.getTsUnit().getTsZone().getZoneGb();
        this.editZoneType = this.tsUserInfo.getTsUnit().getTsZone().getZoneType().toString();
        this.editZoneName = this.tsUserInfo.getTsUnit().getTsZone().getZoneName();
        this.editUnitId = this.tsUserInfo.getTsUnit().getRid().toString();

        /**
         * 初始化单位
         */
        this.editUnitMap = this.filterUnit(this.editZoneCode, this.editZoneType);

        List<Object[]> list = systemModuleService.findTsUserRoleByUserId(this.rid,this.ifAdmin);
        Map<Integer, List<String>> map = new HashMap<Integer, List<String>>();
        if (null != list && list.size() > 0) {
            for (Object[] orr : list) {
                if (map.containsKey(Integer.parseInt(orr[0].toString()))) {
                    List<String> iList = map.get(Integer.parseInt(orr[0].toString()));
                    iList.add(orr[1].toString());
                    map.put(Integer.parseInt(orr[0].toString()), iList);
                } else {
                    List<String> iList = new ArrayList<>();
                    iList.add(orr[1].toString());
                    map.put(Integer.parseInt(orr[0].toString()), iList);
                }
            }
        }

        if (null != roleTypeList && roleTypeList.size() > 0) {
            for (RoleTypePo typo : roleTypeList) {
                if (map.containsKey(typo.getRid())) {
                    typo.setItemSubs(map.get(typo.getRid()));
                    typo.setIfSelected(true);
                    List<RolePo> rolePoList = typo.getRolePoList();
                    if (null != rolePoList && rolePoList.size() > 0) {
                        for (RolePo role : rolePoList) {
                            role.setDisabled(false);
                        }
                    }
                }
            }
        }

    }

    /***
     *  <p>方法描述：初始化角色</p>
     *
     * @MethodAuthor maox, 2020年1月4日, initRoleTypeList
     */
    private void initRoleTypeList() {
        roleTypeList = new ArrayList<RoleTypePo>();
        if (null != roleCodeList && roleCodeList.size() > 0) {
            for (TsSimpleCode code : roleCodeList) {
                RoleTypePo typo = new RoleTypePo();
                typo.setRid(code.getRid());
                typo.setCodeName(code.getCodeName());
                List<RolePo> rolePoList = initRoleList(code.getRid());
                if (null != rolePoList && rolePoList.size() > 0) {
                    typo.setDisabled(false);
                    typo.setRolePoList(rolePoList);
                }
                roleTypeList.add(typo);
            }
        }
    }

    /***
     *  <p>方法描述：角色列表</p>
     *
     * @MethodAuthor maox, 2020年1月4日, initRoleList
     * @return
     */
    private List<RolePo> initRoleList(Integer codeId) {
        List<RolePo> rolePoList = new ArrayList<>();
        List<Object[]> orrList = systemModuleService.findTsRolesByTypeId(codeId, ifAdmin);
        if (null != orrList && orrList.size() > 0) {
            for (Object[] arr : orrList) {
                RolePo role = new RolePo();
                role.setRid(Integer.parseInt(arr[0].toString()));
                role.setRoleName(arr[1].toString());
                role.setDisabled(true);
                rolePoList.add(role);
                typeNameMap.put(arr[1].toString(),arr[0].toString());
            }
        }
        return rolePoList;
    }

    /***
     *  <p>方法描述：角色类型勾选</p>
     *
     * @MethodAuthor maox, 2020年1月4日, roleChangeAction
     */
    public void roleChangeAction() {
        if (null != roleTypeList && roleTypeList.size() > 0) {
            for (RoleTypePo typo : roleTypeList) {
                if (typo.isIfSelected()) {
                    List<RolePo> rolePoList = typo.getRolePoList();
                    if (null != rolePoList && rolePoList.size() > 0) {
                        for (RolePo role : rolePoList) {
                            role.setDisabled(false);
                        }
                    }

                } else {
                    typo.setItemSubs(null);
                    List<RolePo> rolePoList = typo.getRolePoList();
                    if (null != rolePoList && rolePoList.size() > 0) {
                        for (RolePo role : rolePoList) {
                            role.setDisabled(true);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void saveAction() {
        if (very()) {
            return;
        }
        //单位
        TsUnit unit = systemModuleService.findUnitWithSort(Integer.parseInt(editUnitId));
        tsUserInfo.setTsUnit(unit);

        if (roleTypeList != null && roleTypeList.size() > 0) {
            List<TsUserRole> tsUserRoles = new ArrayList<TsUserRole>();

            for (RoleTypePo typo : roleTypeList) {
                List<String> irr = typo.getItemSubs();
                if (irr != null && irr.size() > 0) {
                    for (String i : irr) {
                        TsUserRole userRole = new TsUserRole();
                        userRole.setTsRole(systemModuleService.findRole(Integer.parseInt(i)));
                        userRole.setTsUserInfo(tsUserInfo);
                        tsUserRoles.add(userRole);
                    }
                }
            }

            tsUserInfo.setTsUserRoles(tsUserRoles);
        }
        String msg;
        if (null != this.tsUserInfo.getRid()) {
            tsUserInfo.setUploadTag(0);
        } else {
            tsUserInfo.setIsAdd(0);
            tsUserInfo.setUploadTag(0);
        }


        systemModuleService.saveOrUpdateUserNew(tsUserInfo,this.ifAdmin);
        JsfUtil.addSuccessMessage("保存成功！");
        this.backAction();
    }


    private boolean very() {
        boolean flag = false;
        TsZone Zone = systemModuleService.findTsZoneByGb(editZoneCode);
        //地区
        if (StringUtils.isBlank(editZoneCode)) {
            JsfUtil.addErrorMessage("请选择地区！");
            flag = true;
        } else if (Zone.getRealZoneType() != null && Zone.getRealZoneType() < 4) {
            JsfUtil.addErrorMessage("地区请选择区县及以下！");
            flag = true;
        } else if (Zone.getRealZoneType() == null) {
            JsfUtil.addErrorMessage("请维护真实地区级别！");
            flag = true;
        }
        //单位
        if (StringUtils.isBlank(editUnitId)) {
            JsfUtil.addErrorMessage("请选择单位！");
            flag = true;
        }
        //账号
        if (StringUtils.isBlank(tsUserInfo.getUserNo())) {
            JsfUtil.addErrorMessage("请输入用户账号！");
            flag = true;
        } else {
            Integer count = systemModuleService.getRepetUser(tsUserInfo.getRid(), tsUserInfo.getUserNo(), null);
            if (count > 0) {
                JsfUtil.addErrorMessage("用户账号重复！");
                flag = true;
            }
        }
        //用户姓名
        if (StringUtils.isBlank(tsUserInfo.getUsername())) {
            JsfUtil.addErrorMessage("请输入用户姓名！");
            flag = true;
        }
        //手机号码
        if (StringUtils.isBlank(tsUserInfo.getMbNum())) {
            JsfUtil.addErrorMessage("请输入手机号码！");
            flag = true;
        } else if (!StringUtils.vertyMobilePhone(this.tsUserInfo.getMbNum())) {
            JsfUtil.addErrorMessage("手机号码格式不正确！");
            flag = true;
        }
        //身份证号
        if (StringUtils.isBlank(tsUserInfo.getIdc())) {
            JsfUtil.addErrorMessage("请输入身份证！");
            flag = true;
        } else if (StringUtils.isNoneBlank(IdcUtils.checkIDC(tsUserInfo.getIdc()))) {
            JsfUtil.addErrorMessage("身份证格式不正确！");
            flag = true;
        } else {
            Integer count = systemModuleService.getRepetUser(tsUserInfo.getRid(), null, tsUserInfo.getIdc());
            if (count > 0) {
                JsfUtil.addErrorMessage("身份证重复！");
                flag = true;
            }
        }
        if (tsUserInfo.getValidEndDate() == null) {
            JsfUtil.addErrorMessage("账号有效期不能为空！");
            flag = true;
        }
        if (roleTypeList != null && roleTypeList.size() > 0) {
            boolean hasRole = false;
            for (RoleTypePo typo : roleTypeList) {
                List<String> irr = typo.getItemSubs();
                if (irr != null && irr.size() > 0) {
                    hasRole = true;
                }
            }
            if (!hasRole) {
                JsfUtil.addErrorMessage("请选择至少一个角色！");
                flag = true;
            }
        }
        return flag;
    }

    private static boolean pattern(String regEx, String value) {
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(value);
        return m.find();
    }

    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder();
        sb.append(" from   TS_USER_INFO t  ");
        sb.append(" left join ts_unit t1 on t.unit_rid = t1.rid ");
        sb.append(" left join ts_zone t2 on t1.zone_id = t2.rid ");
        sb.append(" left join TS_USER_ROLE t3 on t.rid = t3.user_info_id ");
        sb.append(" left join ts_role t4 on t3.role_id = t4.rid ");
        sb.append(" where 1 =1 ");
        if (!ifAdmin) {
            sb.append(" AND T.USER_NO != '").append(Constants.ADMIN).append("' ");
        }
        if (StringUtils.isNotBlank(this.searchUnitId)) {
            sb.append(" AND T.UNIT_RID =").append(this.searchUnitId);
        } else {
            String loginZoneCode = this.sessionData.getUser().getTsUnit().getTsZone().getZoneGb();
            if ((!this.ifAdmin) && loginZoneCode.equals(this.searchZoneCode)) {
                if (ifYwRole) {
                    sb.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(this.sessionData.getUser().getTsUnit().getFkByManagedZoneId().getZoneGb())).append("%' ");
                } else {
                    sb.append(" and T.UNIT_RID =").append(this.sessionData.getUser().getTsUnit().getRid());
                }

            } else {
                sb.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%' ");
            }
        }
        if (StringUtils.isNotBlank(this.searchUsername)) {
            sb.append(" AND T.USERNAME LIKE :USERNAME ");
            this.paramMap.put("USERNAME", "%" + this.searchUsername.trim() + "%");
        }
        if (StringUtils.isNotBlank(this.searchUserNo)) {
            sb.append(" AND T.USER_NO LIKE :USER_NO ");
            this.paramMap.put("USER_NO", "%" + this.searchUserNo.trim() + "%");
        }
        if (searchUserState != null && searchUserState.length == 1) {
            sb.append(" AND T.IF_REVEAL =  '").append(searchUserState[0]).append("' ");
        }
        if(StringUtils.isNotBlank(this.selectOnRoleIds)){
            sb.append(" AND t4.rid in ( ").append(selectOnRoleIds).append(" )");
        }

        StringBuilder sql = new StringBuilder();
        sql.append(" with table1 as ( ");
        sql.append(" select distinct t.rid ,case when t2.zone_type>2 then substr(t2.full_name,instr(t2.full_name,'_')+1) else t2.zone_name end as zoneName,t1.unitname,t.username,t.user_no,t.IF_REVEAL,'' as type,'' as typeName,t2.ZONE_GB, t1.UNIT_CODE, ");
        sql.append(" t1.RID  as UNIT_RID, ");
        sql.append(" t1.CREDIT_CODE, ");
        sql.append(" t1.IF_SUB_ORG, ");
        sql.append(" t1.UNITADDR, ");
        sql.append(" t1.UNITTEL, ");
        sql.append(" t.IDC, ");
        sql.append(" t.MB_NUM, ");
        sql.append(" to_char(t.VALID_BEG_DATE,'yyyy-mm-dd')||'至'||to_char(t.VALID_END_DATE,'yyyy-mm-dd') as VALID_DATE ");
        sql.append(sb);
        sql.append(" ), ");
        sql.append("table2 as ( ");
        sql.append(" select T1.RID, listagg(t6.SORT_NAME,'，') within group ( order by T1.RID) as SORT_NAME ");
        sql.append(" from table1 T1 ");
        sql.append(" left join TS_UNIT_ATTR t5 on t5.UNIT_RID=T1.UNIT_RID ");
        sql.append(" left join TS_BS_SORT t6 on t5.ATTR_ID=T6.RID ");
        sql.append(" group by T1.RID ");
        sql.append(" ), ");

        sql.append(" table3 as ( ");
        sql.append(" select T1.RID, ");
        sql.append(" listagg(T3.ROLE_NAME, '，') within group ( order by T2.RID ) as ROLE_TYPE_NAME ");
        sql.append(" from table1 T1 ");
        sql.append("  left join TS_USER_ROLE T2 on T1.RID=T2.USER_INFO_ID ");
        sql.append("  left join TS_ROLE T3 on t2.ROLE_ID=T3.RID ");
        if(!ifAdmin){
            sql.append("  where T3.IF_SUPER_MANAGE_ROLE = 0 ");
        }
        sql.append("  group by T1.RID ");
        sql.append("  ), ");

        sql.append(" table4 as ( ");
        sql.append(" select T1.RID, ");
        sql.append(" listagg(T5.CODE_NAME,'，') within group ( order by T5.NUM ) as ROLE_NAME ");
        sql.append(" from table1 T1 ");
        sql.append("  left join TS_USER_ROLE T2 on T1.RID=T2.USER_INFO_ID ");
        sql.append("  left join TS_ROLE T3 on t2.ROLE_ID=T3.RID ");
        sql.append("  left join TS_ROLE_POWER T4 on t4.ROLE_ID=T3.RID ");
        sql.append("  left join TS_SIMPLE_CODE T5 on T4.ROLE_TYPE_ID=T5.RID ");
        sql.append("  group by T1.RID ");
        sql.append("  ) ");

        StringBuilder commSql=new StringBuilder();
        commSql.append(" from table1 a ");
        commSql.append(" left join table2 b on a.rid=b.rid ");
        commSql.append(" left join table3 c on a.rid=c.rid ");
        commSql.append(" left join table4 d on a.rid = d.rid ");
        commSql.append(" ORDER BY a.ZONE_GB, a.UNIT_CODE, a.USER_NO ");

        StringBuilder searchSql=new StringBuilder();
        searchSql.append(sql);
        searchSql.append(" select a.*,b.SORT_NAME,c.ROLE_TYPE_NAME,d.ROLE_NAME ");
        searchSql.append(commSql);

        StringBuilder countSql = new StringBuilder();
        countSql.append(sql);
        countSql.append(" SELECT  COUNT(distinct a.RID) ");
        countSql.append(commSql);

        return new String[]{searchSql.toString(), countSql.toString()};
    }

    @Override
    public void processData(List<?> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        for(Object[] objArr : (List<Object[]>) list){
            Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            if(rid==null){
                continue;
            }
            if(objArr[20]!=null){
                List<String> newRoleNames =new ArrayList<>();
                List<String> roleNames = StringUtils.string2list(objArr[20].toString(), "，");
                if(!CollectionUtils.isEmpty(roleNames)){
                    for (String roleName : roleNames) {
                       if(!newRoleNames.contains(roleName)){
                           newRoleNames.add(roleName);
                       }
                    }
                }
                if(!CollectionUtils.isEmpty(newRoleNames)){
                    objArr[20]=StringUtils.list2string(newRoleNames,"，");
                }
            }
        }
    }

    /**
     * <p>方法描述：</p>
     *
     * @MethodAuthor rcj, 2020年3月4日, findFlowByIdc
     */
    public boolean findFlowByIdc() {
        if (StringUtils.isNotBlank(tsUserInfo.getIdc())) {
            //如果不是保存操作，就不执行自动填写出生日期和男女的操作
            String checkIDC = IdcUtils.checkIDC(tsUserInfo.getIdc());
            if (StringUtils.isBlank(checkIDC)) {
            } else {
                JsfUtil.addErrorMessage(checkIDC);
            }
        }
        return true;
    }
    /**
     * 获取地区,地区是全国时当前省份放在最上面
     * @param tsZone 地区
     * @return 地区列表
     */
    private List<TsZone> findZoneList(TsZone tsZone) {
        List<TsZone> tsZoneList;
        if (this.ifAdmin || (tsZone != null && "000000000000".equals(tsZone.getZoneCode()))) {
            tsZoneList = this.systemModuleService
                    .findZoneListWithAllZoneAndEntireCountry(tsZone.getZoneCode().substring(0, 2));
        } else if (tsZone != null){
            tsZoneList = this.commService.findZoneListCache(this.ifAdmin, tsZone.getZoneCode(), null, "6");
        } else {
            return new ArrayList<>();
        }
        return tsZoneList;
    }

    /**
     * <p>方法描述： 导出前校验 </p>
     * @MethodAuthor： pw 2022/12/19
     **/
    public void preExport(){
        dataList=new ArrayList<>();
        String searchSql = buildHqls()[0];
        dataList = commService.findDataBySqlNoPage(searchSql, this.paramMap);
        if (CollectionUtils.isEmpty(dataList)) {
            JsfUtil.addErrorMessage("无数据导出！");
            return;
        }
        RequestContext.getCurrentInstance().execute("downloadFileClick()");
    }

    /**
     * <p>方法描述：导出</p>
     * @MethodAuthor： yzz
     * @Date：2023-01-03
     **/
    public DefaultStreamedContent export(){
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("showStatus();");
        ExcelExportUtil excelExportUtil = new ExcelExportUtil("用户管理数据", pakExcelHeaders(), pakExcelExportDataList());
        excelExportUtil.setNeedTitle(false);
        Workbook wb = excelExportUtil.exportExcel(null);
        if (wb != null) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                String fileName = "用户管理数据导出.xlsx";
                fileName = URLEncoder.encode(fileName, "UTF-8");
                wb.write(baos);
                baos.flush();
                byte[] aa = baos.toByteArray();
                context.execute("hideStatus();");
                return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
            } catch (Exception e) {
                JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            }
        }
        context.execute("hideStatus();");
        return null;
    }

    
    /**
     * <p>方法描述：导出表头</p>
     * @MethodAuthor： yzz
     * @Date：2023-01-03
     **/
    public String[][] pakExcelHeaders() {
        return new String[][]{{"序号", "行政区划所属地区","单位名称","社会信用代码","是否分支机构","单位地址","单位属性","单位联系电话","用户姓名","身份证号","手机号码","用户账号","角色类型","角色名称","状态","账号有效期"}};
    }

   /**
    * <p>方法描述：封装导出数据</p>
    * @MethodAuthor： yzz
    * @Date：2023-01-03
    **/
    public List<ExcelExportObject[]> pakExcelExportDataList(){
        List<ExcelExportObject[]> excelExportObjectList = new ArrayList<>();
        if(CollectionUtils.isEmpty(dataList)){
            return excelExportObjectList;
        }
        int count = 0;
        for (Object[] data : dataList) {
            count++;
            ExcelExportObject[] objects = new ExcelExportObject[16];
            objects[0] = new ExcelExportObject(count, XSSFCellStyle.ALIGN_CENTER);
            objects[1] = new ExcelExportObject(StringUtils.objectToString(data[1]), XSSFCellStyle.ALIGN_LEFT);
            objects[2] = new ExcelExportObject(StringUtils.objectToString(data[2]), XSSFCellStyle.ALIGN_LEFT);
            objects[3] = new ExcelExportObject(StringUtils.objectToString(data[11]), XSSFCellStyle.ALIGN_CENTER);
            objects[4] = new ExcelExportObject((data[12]!=null&&"1".equals(data[12].toString()))?"是":"否", XSSFCellStyle.ALIGN_CENTER);
            objects[5] = new ExcelExportObject(StringUtils.objectToString(data[13]), XSSFCellStyle.ALIGN_LEFT);
            objects[6] = new ExcelExportObject(StringUtils.objectToString(data[18]), XSSFCellStyle.ALIGN_LEFT);
            objects[7] = new ExcelExportObject(StringUtils.objectToString(data[14]), XSSFCellStyle.ALIGN_CENTER);
            objects[8] = new ExcelExportObject(StringUtils.objectToString(data[3]), XSSFCellStyle.ALIGN_CENTER);
            objects[9] = new ExcelExportObject(StringUtils.objectToString(data[15]), XSSFCellStyle.ALIGN_CENTER);
            objects[10] = new ExcelExportObject(StringUtils.objectToString(data[16]), XSSFCellStyle.ALIGN_CENTER);
            objects[11] = new ExcelExportObject(StringUtils.objectToString(data[4]), XSSFCellStyle.ALIGN_CENTER);
            if(data[20]!=null){
                List<String> newRoleNames =new ArrayList<>();
                List<String> roleNames = StringUtils.string2list(data[20].toString(), "，");
                if(!CollectionUtils.isEmpty(roleNames)){
                    for (String roleName : roleNames) {
                        if(!newRoleNames.contains(roleName)){
                            newRoleNames.add(roleName);
                        }
                    }
                }
                if(!CollectionUtils.isEmpty(newRoleNames)){
                    objects[12] = new ExcelExportObject(StringUtils.list2string(newRoleNames,"，"), XSSFCellStyle.ALIGN_LEFT);
                }else{
                    objects[12] = new ExcelExportObject(null, XSSFCellStyle.ALIGN_LEFT);
                }
            }else{
                objects[12] = new ExcelExportObject(null, XSSFCellStyle.ALIGN_LEFT);
            }
            objects[13] = new ExcelExportObject(StringUtils.objectToString(data[19]), XSSFCellStyle.ALIGN_LEFT);
            objects[14] = new ExcelExportObject((data[5]!=null&&"1".equals(data[5].toString()))?"启用":"停用", XSSFCellStyle.ALIGN_CENTER);
            objects[15] = new ExcelExportObject(StringUtils.objectToString(data[17]), XSSFCellStyle.ALIGN_CENTER);

            excelExportObjectList.add(objects);
        }
        return excelExportObjectList;
    }


    public TsUserInfo getTsUserInfo() {
        return tsUserInfo;
    }

    public void setTsUserInfo(TsUserInfo tsUserInfo) {
        this.tsUserInfo = tsUserInfo;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneType() {
        return searchZoneType;
    }

    public void setSearchZoneType(String searchZoneType) {
        this.searchZoneType = searchZoneType;
    }

    public String getSearchUnitId() {
        return searchUnitId;
    }

    public void setSearchUnitId(String searchUnitId) {
        this.searchUnitId = searchUnitId;
    }

    public Map<String, String> getSearchUnitMap() {
        return searchUnitMap;
    }

    public void setSearchUnitMap(Map<String, String> searchUnitMap) {
        this.searchUnitMap = searchUnitMap;
    }

    public String[] getSearchUserState() {
        return searchUserState;
    }

    public void setSearchUserState(String[] searchUserState) {
        this.searchUserState = searchUserState;
    }

    public String getSearchUsername() {
        return searchUsername;
    }

    public void setSearchUsername(String searchUsername) {
        this.searchUsername = searchUsername;
    }

    public String getSearchUserNo() {
        return searchUserNo;
    }

    public void setSearchUserNo(String searchUserNo) {
        this.searchUserNo = searchUserNo;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getEditZoneName() {
        return editZoneName;
    }

    public void setEditZoneName(String editZoneName) {
        this.editZoneName = editZoneName;
    }

    public Map<String, String> getEditUnitMap() {
        return editUnitMap;
    }

    public void setEditUnitMap(Map<String, String> editUnitMap) {
        this.editUnitMap = editUnitMap;
    }

    public String getEditZoneCode() {
        return editZoneCode;
    }

    public void setEditZoneCode(String editZoneCode) {
        this.editZoneCode = editZoneCode;
    }

    public String getEditZoneType() {
        return editZoneType;
    }

    public void setEditZoneType(String editZoneType) {
        this.editZoneType = editZoneType;
    }

    public String getEditUnitId() {
        return editUnitId;
    }

    public void setEditUnitId(String editUnitId) {
        this.editUnitId = editUnitId;
    }

    public List<RoleTypePo> getRoleTypeList() {
        return roleTypeList;
    }

    public void setRoleTypeList(List<RoleTypePo> roleTypeList) {
        this.roleTypeList = roleTypeList;
    }

    public boolean isIfYwRole() {
        return ifYwRole;
    }

    public void setIfYwRole(boolean ifYwRole) {
        this.ifYwRole = ifYwRole;
    }

    public Integer getEditZoneId() {
        return editZoneId;
    }

    public void setEditZoneId(Integer editZoneId) {
        this.editZoneId = editZoneId;
    }

    public String getInitPassword() {
        return initPassword;
    }

    public void setInitPassword(String initPassword) {
        this.initPassword = initPassword;
    }

	public String getSelectOnRoleNames() {
		return selectOnRoleNames;
	}

	public void setSelectOnRoleNames(String selectOnRoleNames) {
		this.selectOnRoleNames = selectOnRoleNames;
	}

	public String getSelectOnRoleIds() {
		return selectOnRoleIds;
	}

	public void setSelectOnRoleIds(String selectOnRoleIds) {
		this.selectOnRoleIds = selectOnRoleIds;
	}

    public List<TsSimpleCode> getRoleCodeList() {
        return roleCodeList;
    }

    public void setRoleCodeList(List<TsSimpleCode> roleCodeList) {
        this.roleCodeList = roleCodeList;
    }

    public Map<String, String> getTypeNameMap() {
        return typeNameMap;
    }

    public void setTypeNameMap(Map<String, String> typeNameMap) {
        this.typeNameMap = typeNameMap;
    }

    public String getTypeSelectRids() {
        return typeSelectRids;
    }

    public void setTypeSelectRids(String typeSelectRids) {
        this.typeSelectRids = typeSelectRids;
    }

    public String getTypeSelectNames() {
        return typeSelectNames;
    }

    public void setTypeSelectNames(String typeSelectNames) {
        this.typeSelectNames = typeSelectNames;
    }

    public TreeNode getTypeTree() {
        return typeTree;
    }

    public void setTypeTree(TreeNode typeTree) {
        this.typeTree = typeTree;
    }

    public List<TsSimpleCode> getTsSimpleCodes() {
        return tsSimpleCodes;
    }

    public void setTsSimpleCodes(List<TsSimpleCode> tsSimpleCodes) {
        this.tsSimpleCodes = tsSimpleCodes;
    }

    public boolean isIfShowExportBtn() {
        return ifShowExportBtn;
    }

    public void setIfShowExportBtn(boolean ifShowExportBtn) {
        this.ifShowExportBtn = ifShowExportBtn;
    }

    public List<Object[]> getDataList() {
        return dataList;
    }

    public void setDataList(List<Object[]> dataList) {
        this.dataList = dataList;
    }

    public boolean isIfDel() {
        return ifDel;
    }

    public void setIfDel(boolean ifDel) {
        this.ifDel = ifDel;
    }
}

