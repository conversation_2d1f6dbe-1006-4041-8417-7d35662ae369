package com.chis.modules.system.web;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsHoliday;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.service.SystemServiceImpl;
import com.chis.modules.system.utils.DefaultLazyDataModel;

@ManagedBean(name = "tsHolidayBean")
@ViewScoped
public class TsHolidayBean extends FacesEditBean {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	private Integer rid;
	/**
	 * 批量添加-日期类型
	 */
	private Integer addListtypes;
	/**
	 * 批量添加-日期开始时间
	 */
	private Date addListBeginDate;
	/**
	 * 批量添加-日期结束时间
	 */
	private Date addListEndDate;
	/**
	 * 添加-日期时间
	 */
	private Date addDate;
	/**
	 * 查询条件-日期开始时间
	 */
	private Date searchBeginDate;
	/**
	 * 查询条件-日期结束时间
	 */
	private Date searchEndDate;
	// 日期类型
	private String[] searchtypes;
	// 实体-节假日
	private TsHoliday tsHoliday;
	private List<TsHoliday> holidayList;
	private String date;

	private static String httpUrl = "http://apis.baidu.com/xiaogg/holiday/holiday";
	private SystemServiceImpl service = SpringContextHolder
			.getBean(SystemServiceImpl.class);
	private SystemModuleServiceImpl moduleService = SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	public TsHolidayBean() {
		 super.ifSQL = Boolean.TRUE;
		holidayList = new ArrayList<>();
		searchBeginDate = new Date();
		searchEndDate =null;
//		searchtsHoliday.setSearchBeginDate(new Date());
//		searchtsHoliday.setSearchEndDate(null);
		this.searchAction();
	}


	@Override
	public void addInit() {
		rid=null;
		addDate=null;
		tsHoliday = new TsHoliday();
		
	}
	public void addAction(){
		holidayList.add(new TsHoliday());
	}
	public void delTsHoliday(){
		List<TsHoliday> de = new ArrayList<>();
		de.add(tsHoliday);
		holidayList.removeAll(de);
	}
	public void changeDate() {
//		String result = request(httpUrl, "d=" + DateUtils.formatDate(addDate, "yyyyMMdd"));
//		tsHoliday.setDateType(Integer.valueOf(result.trim()));
//		RequestContext context = RequestContext.getCurrentInstance();
//		context.update("tabView:editForm:dtype");
	}

	/**
	 * @param urlAll
	 *            :请求接口
	 * @param httpArg
	 *            :参数
	 * @return 返回结果
	 */
	public static String request(String httpUrl, String httpArg) {
		BufferedReader reader = null;
		String result = null;
		StringBuffer sbf = new StringBuffer();
		httpUrl = httpUrl + "?" + httpArg;

		try {
			URL url = new URL(httpUrl);
			HttpURLConnection connection = (HttpURLConnection) url
					.openConnection();
			connection.setRequestMethod("GET");
			// 填入apikey到HTTP header
			connection.setRequestProperty("apikey",
					"8a5aac472c7e88dbf4c7c01a4ce25b54");
			connection.connect();
			InputStream is = connection.getInputStream();
			reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			String strRead = null;
			while ((strRead = reader.readLine()) != null) {
				sbf.append(strRead);
				sbf.append("\r\n");
			}
			reader.close();
			result = sbf.toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}
	@Override
	public void viewInit() {
		addListtypes = null;
		holidayList = new ArrayList<>();
	}

	@Override
	public void modInit() {
		if (rid != null) {
			tsHoliday = service.selectHoliday(this.rid);
			date = DateUtils.formatDate(tsHoliday.getHolDate(), "yyyy-MM-dd");
		}
	}

	public void saveAddListAction() {
		tsHoliday = new TsHoliday();
		List<Date> lDate = new ArrayList();
		List<Date> newdate  = new ArrayList();
		List<TsHoliday> list = new ArrayList<>();
		if (addListtypes == null) {
			JsfUtil.addErrorMessage("日期类型不能为空！");
			return;
		}
		if (holidayList!=null && holidayList.size()>0) {
			
			for (TsHoliday tsHoliday : holidayList) {
				if (tsHoliday.getSearchBeginDate()==null) {
					JsfUtil.addErrorMessage("开始日期不能为空！");
					return;
				}
				if (tsHoliday.getSearchEndDate()==null) {
					JsfUtil.addErrorMessage("结束日期不能为空！");
					return;
				}
				if (tsHoliday.getSearchBeginDate().after(tsHoliday.getSearchEndDate()) ) {
					JsfUtil.addErrorMessage("开始日期不能大于结束日期！");
					return;
				}
				Calendar calBegin = Calendar.getInstance();
				calBegin.setTime(tsHoliday.getSearchBeginDate());
				Calendar calEnd = Calendar.getInstance();
				calEnd.setTime(tsHoliday.getSearchEndDate());
				lDate.add(calBegin.getTime());
				while (tsHoliday.getSearchEndDate().after(calBegin.getTime())) {
					calBegin.add(Calendar.DAY_OF_MONTH, 1);
					lDate.add(calBegin.getTime());
				}
//				
				if (newdate!=null && newdate.size()>0) {
					for (Date d : newdate) {
						if(lDate.contains(d)){
							String d1 = DateUtils.formatDate(d, "yyyy-MM-dd");
							JsfUtil.addErrorMessage(d1.toString()+"已经存在！");
							return;	
						}
					}
				}
				for (Date date : lDate) {
					tsHoliday = new TsHoliday();
					tsHoliday.setHolDate(date);
					
					String holiday = service.selectByDate(date,rid);
					if (holiday != null ) {
						String d = DateUtils.formatDate(date, "yyyy-MM-dd");
						JsfUtil.addErrorMessage(d.toString()+"已经存在！");
						return;
					}
					
					tsHoliday.setDateType(addListtypes);
					tsHoliday.setHolDate(date);
					SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");
					String year = dateFormat.format(date);
					tsHoliday.setHolYear(Integer.valueOf(year));
					SimpleDateFormat dateFormat1 = new SimpleDateFormat("MM");
					String month = dateFormat1.format(date);
					tsHoliday.setHolMon(Integer.valueOf(month));
					tsHoliday.setCreateDate(new Date());
					tsHoliday.setCreateManid(sessionData.getUser().getRid());
					list.add(tsHoliday);
				}
				newdate.addAll(lDate);
				lDate = new ArrayList<>();
			}
			
		}else {
			JsfUtil.addErrorMessage("请添加日期！");
			return;
		}
	    moduleService.insertBatch(list);
		this.searchAction();
		this.backAction();
		JsfUtil.addSuccessMessage("保存成功！");
	}

	@Override
	public void saveAction() {
		if (rid == null) {
			if (addDate==null) {
				JsfUtil.addErrorMessage("日期不能为空！");
				return;
			}else {
				tsHoliday.setHolDate(addDate);
			}
			
			String holiday = service.selectByDate(addDate,rid);
			if (StringUtils.isNotBlank(holiday) ) {
				String d = DateUtils.formatDate(addDate, "yyyy-MM-dd");
				JsfUtil.addErrorMessage(d+"已经存在！");
				return;
			}
		}
		if (tsHoliday != null && tsHoliday.getRid() != null) {
			tsHoliday.setModifyDate(new Date());
			tsHoliday.setModifyManid(sessionData.getUser().getRid());
		} else {
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");
			String year = dateFormat.format(tsHoliday.getHolDate());
			tsHoliday.setHolYear(Integer.valueOf(year));
			SimpleDateFormat dateFormat1 = new SimpleDateFormat("MM");
			String month = dateFormat1.format(tsHoliday.getHolDate());
			tsHoliday.setHolMon(Integer.valueOf(month));
			tsHoliday.setCreateDate(new Date());
			tsHoliday.setCreateManid(sessionData.getUser().getRid());
		}
		if (tsHoliday != null) {
			TsHoliday t =new TsHoliday();
			t.setCreateDate(tsHoliday.getCreateDate());
			t.setCreateManid(tsHoliday.getCreateManid());
			t.setDateType(tsHoliday.getDateType());
			t.setHolDate(tsHoliday.getHolDate());
			t.setHolMon(tsHoliday.getHolMon());
			t.setHolYear(tsHoliday.getHolYear());
			t.setRid(tsHoliday.getRid());
			moduleService.saveOrUpdateHoliday(t);
		}
		this.searchAction();
		this.backAction();
		JsfUtil.addSuccessMessage("保存成功！");
	}
	@Override
	public String[] buildHqls() {
		StringBuffer sb = new StringBuffer();
		sb.append("select T.rid,T.HOL_DATE,T.DATE_TYPE");
		sb.append(" from TS_HOLIDAY T WHERE 1=1");
		if (searchtypes!=null && searchtypes.length>0) {
			StringBuffer stringBuffer = new StringBuffer();
			for (String type : searchtypes) {
				stringBuffer.append(",").append(type);
			}
			sb.append(" AND T.DATE_TYPE in (").append(stringBuffer.deleteCharAt(0).toString()).append(")");
		}
		
		if (searchBeginDate!=null) {
			sb.append(" and T.HOL_DATE >=TO_DATE('").append(DateUtils.formatDate(searchBeginDate)).append("', 'YYYY-MM-DD')");
		}
		if (searchEndDate!=null) {
			sb.append(" and T.HOL_DATE <=TO_DATE('").append(DateUtils.formatDate(searchEndDate)).append("', 'YYYY-MM-DD')");
		}
		sb.append(" order by T.HOL_DATE");
		String searchSql = sb.toString();
		String countSql = "select count(rid) from (" + sb.toString() + ")aa";
		return new String[] { searchSql, countSql };
	}
	public void deleteAction() {
		if (rid != null) {
			service.delHoliday(rid);
		}
		this.searchAction();
		JsfUtil.addSuccessMessage("删除成功！");
	}

	public TsHoliday getTsHoliday() {
		return tsHoliday;
	}

	public void setTsHoliday(TsHoliday tsHoliday) {
		this.tsHoliday = tsHoliday;
	}


	public Date getAddDate() {
		return addDate;
	}

	public void setAddDate(Date addDate) {
		this.addDate = addDate;
	}

	public Integer getAddListtypes() {
		return addListtypes;
	}

	public void setAddListtypes(Integer addListtypes) {
		this.addListtypes = addListtypes;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public Date getAddListBeginDate() {
		return addListBeginDate;
	}

	public List<TsHoliday> getHolidayList() {
		return holidayList;
	}

	public void setHolidayList(List<TsHoliday> holidayList) {
		this.holidayList = holidayList;
	}

	public void setAddListBeginDate(Date addListBeginDate) {
		this.addListBeginDate = addListBeginDate;
	}

	public Date getAddListEndDate() {
		return addListEndDate;
	}

	public void setAddListEndDate(Date addListEndDate) {
		this.addListEndDate = addListEndDate;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public Date getSearchBeginDate() {
		return searchBeginDate;
	}

	public void setSearchBeginDate(Date searchBeginDate) {
		this.searchBeginDate = searchBeginDate;
	}

	public Date getSearchEndDate() {
		return searchEndDate;
	}

	public void setSearchEndDate(Date searchEndDate) {
		this.searchEndDate = searchEndDate;
	}

	public String[] getSearchtypes() {
		return searchtypes;
	}

	public void setSearchtypes(String[] searchtypes) {
		this.searchtypes = searchtypes;
	}

	

}
