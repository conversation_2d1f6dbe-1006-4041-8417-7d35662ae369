package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;

import org.primefaces.model.TreeNode;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsMenu;
import com.chis.modules.system.entity.TsMenuBtn;
import com.chis.modules.system.entity.TsRole;
import com.chis.modules.system.entity.TsRoleMenu;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsUserMenu;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * 描述
 *
 * <AUTHOR>
 * @createdate 2015-9-1
 */
@ManagedBean(name="roleMenuEditBean")
@ViewScoped
public class RoleMenuEditBean extends FacesBean {
    private static final long serialVersionUID = -5972290406643208512L;
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private SystemModuleServiceImpl systemService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);

    private String from;
    /*角色id*/
    private String roleid;
    /**是否是超管*/
    private boolean ifAdmin = Boolean.TRUE;
    /**菜单授权的菜单树*/
    private TreeNode treeNode;
    /**菜单授权的已选择的树*/
    private TreeNode[] selectedNodes;
    /*所有按钮*/
    private Map<Integer,List<SelectItem>> menuBtnMap = new HashMap<Integer, List<SelectItem>>();
    /*查询条件*/
    private String searchMenuName;
    private String searchCode;
    /*该用户拥有菜单*/
    private List<TsMenu> menuList;
    private List<TsMenu> displayMenuList;
    /*用户*/
    private String userid;
    /*选项*/
    private String searchOption = "0";


    public RoleMenuEditBean() {
        this.roleid = JsfUtil.getRequest().getParameter("roleid");
        this.from = JsfUtil.getRequest().getParameter("from");
        this.from = this.from.replaceAll("@", "&");
        this.userid = JsfUtil.getRequest().getParameter("userid");
        if(!sessionData.getUser().getUserNo().equals(Constants.ADMIN)) {
            ifAdmin = Boolean.FALSE;
        }
        init();
        searchAction();
    }

    private void init(){
        Set<Integer> selectset = new HashSet<Integer>();
        Map<Integer,List<String>> map = new HashMap<Integer, List<String>>();
        if(roleid != null){
            //初始化已选角色的菜单
            TsRole tsRole = this.systemService.findRoleWithRoleMenu(Integer.valueOf(roleid));
            List<TsRoleMenu> roleMenuList = tsRole.getTsRoleMenuList();
            if(null != roleMenuList && roleMenuList.size() > 0) {
                for(TsRoleMenu t: roleMenuList) {
                    selectset.add(t.getTsMenu().getRid());
                }
            }
            //角色已选按钮
            List<TsMenuBtn> btnidlist = this.systemService.findSelectBtnByRole(Integer.valueOf(roleid));
            if(btnidlist != null && btnidlist.size() > 0){
                for(TsMenuBtn t : btnidlist){
                    List<String> list1 = new ArrayList<String>();
                    if(map.containsKey(t.getTsMenu().getRid())){
                        list1 = map.get(t.getTsMenu().getRid());
                    }
                    list1.add(t.getRid().toString());
                    map.put(t.getTsMenu().getRid(),list1);
                }
            }

        }else if(userid != null){
            TsUserInfo tsUserInfo = this.systemService.findUserWithUserMenu(Integer.valueOf(this.userid));
            List<TsUserMenu> menuList = tsUserInfo.getTsUserMenus();
            if(null != menuList && menuList.size() > 0) {
                for(TsUserMenu t: menuList) {
                    selectset.add(t.getTsMenu().getRid());
                }
            }
            List<TsMenuBtn> btnidlist = this.systemService.findSelectBtnByUser(Integer.valueOf(userid));
            if(btnidlist != null && btnidlist.size() > 0){
                for(TsMenuBtn t : btnidlist){
                    List<String> list1 = new ArrayList<String>();
                    if(map.containsKey(t.getTsMenu().getRid())){
                        list1 = map.get(t.getTsMenu().getRid());
                    }
                    list1.add(t.getRid().toString());
                    map.put(t.getTsMenu().getRid(),list1);
                }
            }

        }

        //初始化所有按钮
        List<TsMenuBtn> list = systemService.findAllMenuButton();
        if(list != null && list.size() > 0){
            menuBtnMap = new HashMap<Integer, List<SelectItem>>();
            for(TsMenuBtn t : list){
                List<SelectItem> list1 = new ArrayList<SelectItem>();
                if(menuBtnMap.containsKey(t.getTsMenu().getRid())){
                    list1 = menuBtnMap.get(t.getTsMenu().getRid());
                }
                list1.add(new SelectItem(t.getRid(),t.getBtnName()));
                menuBtnMap.put(t.getTsMenu().getRid(),list1);
            }
        }

        //初始化菜单
        TsUserInfo tsUserInfo = sessionData.getUser();
        menuList = this.systemService.findMenuList(this.ifAdmin, tsUserInfo.getRid(), tsUserInfo.getTsUnit().getRid());
        if(menuList != null && menuList.size() > 0){
            for(TsMenu h : menuList){
                //设置菜单长度
                if(null!=h.getMenuLevelNo()){
                    h.setLevelNum(h.getMenuLevelNo().split("[.]").length);
                }else{
                    h.setLevelNum(1);
                }
                //该菜单下的按钮
                h.setAllbtns(menuBtnMap.get(h.getRid()));
                //设置菜单选择
                h.setSelected(selectset.contains(h.getRid()));
                h.setTempselected(selectset.contains(h.getRid()));
                //已选按钮
                List<String> slist = map.get(h.getRid());
                if(slist != null && slist.size() > 0){
                    String[] str = new String[slist.size()];
                    for(int i = 0 ; i < slist.size() ; i++){
                        str[i] = slist.get(i);
                    }
                    h.setBtns(str);
                }
            }
        }

    }

    public void searchAction(){
        displayMenuList = new ArrayList<TsMenu>();
        if(StringUtils.isBlank(searchMenuName) && StringUtils.isBlank(searchCode) && searchOption.equals("0")){
            displayMenuList.addAll(menuList);
        }else{
            boolean boo = true;
            for(TsMenu t : menuList){
                boo = true;
                if(StringUtils.isNotBlank(searchMenuName) && !t.getMenuCn().contains(searchMenuName)){
                    boo = false;
                }
                if(StringUtils.isNotBlank(searchCode) && !t.getMenuLevelNo().contains(searchCode)){
                    boo = false;
                }
                //过滤未选中的
                if(searchOption.equals("1") && !t.isTempselected()){
                    boo = false;
                }
                if(boo){
                    displayMenuList.add(t);
                }
            }
        }

    }

    public void searchAllAction(){
        for(TsMenu t : displayMenuList){
            if(t.getBtns() != null){
                for(TsMenu t1 : menuList){
                    if(t1.getRid().equals(t.getRid())){
                        t1.setBtns(t.getBtns());break;
                    }
                }
            }
        }
        searchAction();
    }

    public void menuSelectAction(Integer menuid,boolean boo){
        //页面数据赋给menuList
        Map<Integer,TsMenu> temp = new HashMap<Integer, TsMenu>();
        for(TsMenu t : displayMenuList){
            temp.put(t.getRid(),t);
        }
        for(TsMenu t : menuList){
            if(temp.containsKey(t.getRid())){
                TsMenu tsMenu = temp.get(t.getRid());
                t.setBtns(tsMenu.getBtns());
                t.setSelected(tsMenu.isSelected());
            }
        }
        //对menuList中菜单设置选择
        TsMenu tsMenu = null;
        for(TsMenu t : menuList){
            if(t.getRid().equals(menuid)){
                tsMenu = t;
            }
            if(tsMenu != null && t.getMenuLevelNo().startsWith(tsMenu.getMenuLevelNo())){
                t.setSelected(boo);
                if(!boo){
                    t.setBtns(null);
                }
            }
        }
        //若选择，将上级菜单都选择
        if(boo && tsMenu != null){
            String sub = tsMenu.getMenuLevelNo();
            for(TsMenu t : menuList){
                if(sub.startsWith(t.getMenuLevelNo())){
                    t.setSelected(true);
                }
            }
        }
        //更新页面数据
        searchAction();
    }

    public void saveAction(){
        //页面数据赋给menuList
        Map<Integer,TsMenu> temp = new HashMap<Integer, TsMenu>();
        for(TsMenu t : displayMenuList){
            temp.put(t.getRid(),t);
        }
        for(TsMenu t : menuList){
            if(temp.containsKey(t.getRid())){
                TsMenu tsMenu = temp.get(t.getRid());
                t.setBtns(tsMenu.getBtns());
                t.setSelected(tsMenu.isSelected());
            }
        }
        //保存配置信息
        if(menuList != null && menuList.size() > 0){
            Set<Integer> menuid = new HashSet<Integer>();
            Set<Integer> btnid = new HashSet<Integer>();
            for(TsMenu t : menuList){
                if(t.isSelected()){
                    menuid.add(t.getRid());
                }
                if(t.getBtns() != null && t.getBtns().length > 0){
                    for(String s : t.getBtns()){
                        btnid.add(Integer.valueOf(s));
                    }
                }
            }
            if(roleid != null){
                //按钮授权
                this.systemService.menuBtnSqMenu(btnid, Integer.valueOf(this.roleid));
                //菜单授权
                this.systemService.sqMenu(menuid, Integer.valueOf(roleid));
            }else if(userid != null){
                this.systemService.menuBtnUserMenu(btnid, Integer.valueOf(this.userid));
                this.systemService.grantMenuToUser(menuid, Integer.valueOf(userid));
            }

            JsfUtil.addSuccessMessage("保存成功！");
            init();
            searchAction();
            //backAction();
        }

    }

    public void backAction(){
        try {
            FacesContext.getCurrentInstance().getExternalContext().redirect(this.from);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

    }

    public String getRoleid() {
        return roleid;
    }

    public void setRoleid(String roleid) {
        this.roleid = roleid;
    }

    public TreeNode getTreeNode() {
        return treeNode;
    }

    public void setTreeNode(TreeNode treeNode) {
        this.treeNode = treeNode;
    }

    public TreeNode[] getSelectedNodes() {
        return selectedNodes;
    }

    public void setSelectedNodes(TreeNode[] selectedNodes) {
        this.selectedNodes = selectedNodes;
    }

    public Map<Integer, List<SelectItem>> getMenuBtnMap() {
        return menuBtnMap;
    }

    public void setMenuBtnMap(Map<Integer, List<SelectItem>> menuBtnMap) {
        this.menuBtnMap = menuBtnMap;
    }

    public String getSearchMenuName() {
        return searchMenuName;
    }

    public void setSearchMenuName(String searchMenuName) {
        this.searchMenuName = searchMenuName;
    }

    public String getSearchCode() {
        return searchCode;
    }

    public void setSearchCode(String searchCode) {
        this.searchCode = searchCode;
    }

    public List<TsMenu> getMenuList() {
        return menuList;
    }

    public void setMenuList(List<TsMenu> menuList) {
        this.menuList = menuList;
    }

    public List<TsMenu> getDisplayMenuList() {
        return displayMenuList;
    }

    public void setDisplayMenuList(List<TsMenu> displayMenuList) {
        this.displayMenuList = displayMenuList;
    }

    public String getSearchOption() {
        return searchOption;
    }

    public void setSearchOption(String searchOption) {
        this.searchOption = searchOption;
    }
}
