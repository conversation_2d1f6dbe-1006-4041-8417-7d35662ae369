package com.chis.modules.system.web;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.io.Writer;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import com.chis.common.utils.PropertiesLoader;
import com.chis.modules.system.logic.PropertiesPanel;
import com.chis.modules.system.logic.PropertiesTable;

@ManagedBean(name="propertiesSearchListBean")
@ViewScoped
public class PropertiesSearchListBean {
	private List<PropertiesPanel> panels;
	public PropertiesSearchListBean(){
		try {
			panels = new ArrayList<>();
			Resource[] properResources = new PathMatchingResourcePatternResolver().getResources("classpath*:chiscdc*.properties");
			for (Resource resource : properResources) {
				PropertiesPanel panel = new PropertiesPanel();
				panel.setFileName(resource.getFilename());
				PropertiesLoader loader = new PropertiesLoader(new String[]{resource.getFilename()});
				Properties props = loader.getProperties();
				List<PropertiesTable> tables = new ArrayList<>();
				for (Object key : props.keySet()) {
					String keyStr = key.toString();
					String value = props.getProperty(keyStr);
					try {
						value = new String(value.getBytes("iso-8859-1"),"utf-8");
					} catch (UnsupportedEncodingException e) {
						e.printStackTrace();
					}
					props.store(new FileOutputStream(resource.getFile()), "111");
					PropertiesTable table = new PropertiesTable();
					table.setAttrName(keyStr);
					table.setAttrValue(value);
					tables.add(table);
					
					props.load(resource.getInputStream());
				}
				panel.setPropertiesTables(tables);
				panels.add(panel);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	private void orderStore(Writer writer) {
	}
	public List<PropertiesPanel> getPanels() {
		return panels;
	}
	public void setPanels(List<PropertiesPanel> panels) {
		this.panels = panels;
	}

}
