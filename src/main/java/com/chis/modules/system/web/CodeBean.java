package com.chis.modules.system.web;

import java.util.*;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeCollapseEvent;
import org.primefaces.event.NodeExpandEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.GB2Alpha;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.SystemParamType;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.javabean.MsrunitObj;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import org.springframework.util.CollectionUtils;

/**
* 码表管理
* 
* @editContent 删除buildProcessData()方法，如果查询出来的数据集需要修改，子类不需要再重写buildProcessData()  david 2014-09-04 
* 
* 
* 修改人：lxk 修改时间：2014-09-12<br/>
* 修改内容：发布后不能修改或删除，保存时记录修订版本号
*
* 修改人：lxk 修改时间：2014-10-08<br/>
* 修改内容：发布后修改报错
* 
* <AUTHOR>
*/
@ManagedBean(name="codeBean")
@ViewScoped
public class CodeBean extends FacesEditBean implements IProcessData{

	private static final long serialVersionUID = -738445454536625063L;
	/**存在session中的对象*/
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/**ejb session bean*/
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    /**码表对象*/
    private TsSimpleCode tsSimpleCode = new TsSimpleCode();
	/**码表类型rid*/
	private Integer codeTypeId;
	/**码表rid*/
	private Integer codeId;
    /**码表数据*/
    private List<TsSimpleCode> codeList = new ArrayList<TsSimpleCode>(0);
    /**当前码表类型名称*/
    private String curCodeTypeName;
    /**当前码表类型编码*/
    private String curCodeTypeNo;
    /**添加页面地区树*/
    private TreeNode treeNode;
    /**选中的树*/
    private TreeNode selectedNode;

    //===========查询条件=================
    private Map<String, String> systemTypeMap = new LinkedHashMap<String, String>();
    /**查询条件：系统类型*/
    private String searchSystemType;
    /**查询条件：码表类型名称*/
    private String searchTypeName;
    /**查询条件：码表名称*/
    private String searchCodeName;
    /** 查询条件：码表编码或结构层次编码 */
    private String searchMixCode;
    /**查询条件：码表状态*/
    private List<String> searchCodeState = new ArrayList<String>(0);
    /**查询条件：所有能看到的系统类型comm,risk ，隔开*/
    private String allSystem;

    private ApplicationBean applicationBean = (ApplicationBean) getBean("applicationBean");

    /**是否是根节点*/
    private boolean ifTree = Boolean.FALSE;
    /**是否是树*/
    private boolean ifRoot = Boolean.FALSE;
    /**是否是修改操作*/
    private boolean ifEidt = Boolean.FALSE;

    /** 对现有序号就行重新排序 */
    private List<TsSimpleCode> orderList = new ArrayList<TsSimpleCode>();
    /** 当前层级编码 */
    private String tempCodeLevel;
	/** ++新增码表类型维护修改 */
	private TsCodeType addCodeType = new TsCodeType();
	/** ++编辑系统类型 */
	private Short systemTypeEdit;
    /**特殊字符*/
	private MsrunitObj msrunitObj;
	private String addType;
	private List<TsSimpleCode> msrunitCodes;
	private boolean ifSpecial;
    /** 查询条件 编码 */
    private String searchTypeCode;
    /** 查询条件 自检状态 */
    private String[] searchTypeState;
    /** 维护说明 */
    private String codeTypeRmk;
    /** 自检状态 */
    private String checkState;
    /** 自检失败原因 */
    private String checkFailRsn;
    /** 添加子节点时 父节点的codePath */
    private String parentCodePath;
    /** 保存前是否收缩状态 */
    private Set<Integer> explandRidSet;

    @PostConstruct
	public void init() {
    	ifSpecial = false;
        super.ifSQL = Boolean.TRUE;

        TsUserInfo tsUserInfo = this.sessionData.getUser();
        
        msrunitObj = new MsrunitObj();
        msrunitCodes = commService.findSimpleCodesByTypeNo("'5116'");
        this.searchTypeState = new String[]{};
        this.explandRidSet = new HashSet<>();
        this.initSearchCondition();
        this.searchAction();
	}
	public void addCodeType() {
		addCodeType = new TsCodeType();
		addCodeType.setCodeTypeName(this.systemModuleService.getMaxTypeCode());
		addCodeType.setTreeTag((short) 0);
		addCodeType.setIsuserType((short) 0);
		systemTypeEdit = null;
	}

	public void modCodeType() {
		this.addCodeType = this.systemModuleService.findCodeType(this.codeTypeId);
        this.systemTypeEdit = null == this.addCodeType.getParamType() ? null : Short.valueOf(this.addCodeType.getParamType().toString());
	}

	public void delTypeAction() {
		String mess = this.systemModuleService.delTypeAction(codeTypeId);
		if (StringUtils.isNotBlank(mess)) {
			JsfUtil.addErrorMessage(mess);
		} else {
			JsfUtil.addSuccessMessage("删除成功！");
		}
	}

	public void saveCodeType() {
        String rmk = this.addCodeType.getRmk();
        if (StringUtils.isNotBlank(rmk) && rmk.length() > 2000) {
            JsfUtil.addErrorMessage("维护说明不能超过2000个字！");
            return;
        }
		//SystemType findEnum = (SystemType) EnumUtils.findEnum(SystemType.class, systemTypeEdit);
        this.addCodeType.setParamType(Integer.parseInt(this.systemTypeEdit.toString()));
		//addCodeType.setSystemType(findEnum);
		Integer userId = this.sessionData.getUser().getRid();
		this.systemModuleService.saveTsCodeType(this.addCodeType,userId);

		this.searchAction();

		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.execute("CodeTypeEditDialog.hide()");
		requestContext.update("tabView:mainForm:dataTable");
		JsfUtil.addSuccessMessage("保存成功！");
	}

    
    /**
     * 无树形结构排序
     */
    public void orderNoTreeAction() {
    	orderList = new ArrayList<TsSimpleCode>();
    	if( null != this.codeList && this.codeList.size() > 0)	{
    		orderList.addAll(this.codeList);
    		RequestContext cI = RequestContext.getCurrentInstance();
    		cI.execute("PF('CodeOrderDialog').show()");
    		cI.update("tabView:editForm:orderL");
    	}else{
    		JsfUtil.addErrorMessage("请先查询出可供排序的数据！");
    	}
    }
    
    /**
     * 无树形结构排序
     */
    public void orderHasTreeAction() {
    	orderList = new ArrayList<TsSimpleCode>();
    	if( null != tsSimpleCode){
    		List<TsSimpleCode> findCodeList = systemModuleService.findCodeList(tsSimpleCode.getTsCodeType().getRid(), tsSimpleCode.getCodeLevelNo());
    		if( null != findCodeList)	{
    			orderList.addAll(findCodeList);
        		RequestContext cI = RequestContext.getCurrentInstance();
        		cI.execute("PF('CodeOrderDialog').show()");
        		cI.update("tabView:editTreeForm:orderL");
    		}else{
        		JsfUtil.addErrorMessage("请先查询出可供排序的数据！");
        	}
    	}
    }
    
    /**
     * 重新保存无树排序
     */
    public void saveNoTreeOrder()	{
    	if( null != orderList && orderList.size() >0 )	{
    		this.systemModuleService.updateSimpleCodeNum(orderList);
    		JsfUtil.addSuccessMessage("更新排序成功！");
    		 if(this.ifTree) {
                 this.searchTreeSimpleCode();
             }else {
                 this.searchCodeList();
             }
			RequestContext requestContext = RequestContext.getCurrentInstance();
            if(this.ifTree) {
            	requestContext.update("tabView:editTreeForm:codeTreeDataTable");
                this.searchTreeSimpleCode();
            }else {
            	requestContext.update("tabView:editForm:codeDataTable");
                this.searchCodeList();
            }
    	}
    }
    
    /**
     * 初始化查询条件
     */
    private void initSearchCondition() {
        //查询码表中存在的系统类型
        List<Integer> typeList = this.commService.findAllParamTypeByTsCodeType();
        /**
         * 初始化系统类型
         */
        SystemType[] types = SystemType.values();
        for(SystemType st: types) {
            Integer typeNo = Integer.parseInt(st.getTypeNo().toString());
            if (!typeList.contains(typeNo)) {
                continue;
            }
            this.systemTypeMap.put(st.getTypeCN(), st.getTypeNo().toString());
        }
    }


    @Override
    public void addInit() {

    }

    /**
     * 转向到码表编辑页面
     */
    @Override
    public void viewInit() {
        this.searchCodeName = null;
        this.searchMixCode = null;
        this.ifTree = Boolean.TRUE;
        this.explandRidSet.clear();
        this.searchTreeSimpleCode();
        this.explanNode(this.treeNode);
    }

    /**
     * <p>方法描述：查询树形码表 </p>
     * pw 2024/2/1
     **/
    public void searchTreeSimpleCode(){
        this.treeNode = new DefaultTreeNode("root", null);
        this.treeNode.setExpanded(Boolean.TRUE);

        List<TsSimpleCode> list = this.systemModuleService.findCodeListByTypeId(this.codeTypeId);

        if(CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> firstLevelNoSet = new LinkedHashSet<String>();  //只有第一层
        Set<String> levelNoSet = new LinkedHashSet<String>();   //没有第一层
        Map<String, TsSimpleCode> allMap = new HashMap<String, TsSimpleCode>();    //所有马表

        Set<String> codeNoSet = new HashSet<>();
        Set<String> codeLevelSet = new HashSet<>();
        for(TsSimpleCode t : list) {
            String codeName = t.getCodeName();
            String codeNo = t.getCodeNo();
            String codeLevelNo = t.getCodeLevelNo();
            boolean ifContinue = StringUtils.isNotBlank(this.searchCodeName) &&
                    (StringUtils.isBlank(codeName) || !codeName.contains(this.searchCodeName.trim()));
            if (ifContinue) {
                continue;
            }
            ifContinue = StringUtils.isBlank(this.searchMixCode) ||
                    (StringUtils.isNotBlank(codeNo) && codeNo.contains(this.searchMixCode.trim())) ||
                    (StringUtils.isNotBlank(codeLevelNo) && codeLevelNo.contains(this.searchMixCode.trim()));
            if (!ifContinue) {
                continue;
            }
            codeNoSet.add(codeNo);
            codeLevelSet.add(codeLevelNo);
            allMap.put(codeLevelNo, t);
            if(StringUtils.isNotBlank(codeLevelNo)) {
                if(StringUtils.containsNone(codeLevelNo, ".")) {
                    firstLevelNoSet.add(codeLevelNo);
                }else {
                    levelNoSet.add(codeLevelNo);
                }
            }
        }

        List<TsSimpleCode> errSimpleCodeList = new ArrayList<>();
        for (String codeLevelNo : allMap.keySet()) {
            if(StringUtils.isBlank(codeLevelNo) || StringUtils.containsNone(codeLevelNo, ".")) {
                continue;
            }
            TsSimpleCode simpleCode = allMap.get(codeLevelNo);
            if (null == simpleCode || errSimpleCodeList.contains(simpleCode)) {
                continue;
            }
            String preLevelCode = codeLevelNo.substring(0, codeLevelNo.lastIndexOf("."));
            //存在上级层次编码不存在的情况
            if (!codeLevelSet.contains(preLevelCode)) {
                errSimpleCodeList.add(simpleCode);
                continue;
            }
            String[] levelArr = codeLevelNo.split("\\.");
            int length = levelArr.length;
            //存在上级编码不存在的情况
            for (int i=0;i<length-1;i++) {
                if (codeNoSet.contains(levelArr[i]) || errSimpleCodeList.contains(simpleCode)) {
                    continue;
                }
                errSimpleCodeList.add(simpleCode);
                break;
            }
        }

        for(String ln: firstLevelNoSet) {
            TsSimpleCode simpleCode = allMap.get(ln);
            errSimpleCodeList.remove(simpleCode);
            TreeNode node = new DefaultTreeNode(simpleCode, this.treeNode);
            if (this.explandRidSet.contains(simpleCode.getRid())) {
                node.setExpanded(true);
            }
            this.addChildNode(ln, levelNoSet, allMap, node, errSimpleCodeList);
        }

        if (CollectionUtils.isEmpty(errSimpleCodeList)) {
            return;
        }
        for (TsSimpleCode simpleCode : errSimpleCodeList) {
            simpleCode.setIfCanAddSub(false);
            TreeNode node = new DefaultTreeNode(simpleCode, this.treeNode);
        }
    }

    /**
     * 构建菜单树
     * @param levelNo 菜单层级编码
     * @param levelNoSet 二级以及以上的菜单的层级编码集合
     * @param allMap 所有数据级map
     * @param parentNode 上级树节点
     */
    private void addChildNode(String levelNo, Set<String> levelNoSet, Map<String, TsSimpleCode> allMap, TreeNode parentNode, List<TsSimpleCode> errSimpleCodeList) {
        int level = StringUtils.countMatches(levelNo, ".");
        for(String ln: levelNoSet) {
            if(StringUtils.countMatches(ln, ".")==(level+1) && StringUtils.startsWith(ln,levelNo+".")) {
                TsSimpleCode t = allMap.get(ln);
                errSimpleCodeList.remove(t);
                TreeNode node = new DefaultTreeNode(t, parentNode);
                if (CollectionUtils.isEmpty(this.explandRidSet) || this.explandRidSet.contains(t.getRid())) {
                    node.setExpanded(true);
                }
                this.addChildNode(ln, levelNoSet, allMap, node, errSimpleCodeList);
            }
        }
    }

    /**
     * 转向到码表无树形结构的编辑界面
     */
    @Override
    public void modInit() {
    	if("5121".equals(curCodeTypeNo)){
    		ifSpecial = true;
    	}else{
    		ifSpecial = false;
    	}
        this.ifTree = Boolean.FALSE;
        this.searchCodeName = null;
        this.searchCodeState = new ArrayList<String>(0);
        this.searchCodeList();
    }

    /**
     * 无树查询
     */
    public void searchCodeList() {
        String stateMark = null;
        if(null != this.searchCodeState && this.searchCodeState.size() == 1) {
            stateMark = this.searchCodeState.get(0);
        }
        this.codeList = this.systemModuleService.findCodeListByTypeId(this.codeTypeId, this.searchCodeName, stateMark);
    }
    
	/**
	 * 保存
	 */
    @Override
	public void saveAction() {
        boolean ifMarch = false;
        if (StringUtils.isBlank(this.tsSimpleCode.getCodeNo())) {
            JsfUtil.addErrorMessage("编码不允许为空！");
            ifMarch = true;
        }
        if (StringUtils.isBlank(this.tsSimpleCode.getCodeName())) {
            JsfUtil.addErrorMessage("名称不允许为空！");
            ifMarch = true;
        }
        if (null == this.tsSimpleCode.getNum()) {
            JsfUtil.addErrorMessage("序号不允许为空！");
            ifMarch = true;
        }
        if (this.ifTree && null != this.tsSimpleCode.getRid() && StringUtils.isBlank(this.tsSimpleCode.getCodeLevelNo())) {
            JsfUtil.addErrorMessage("结构层次编码不允许为空！");
            ifMarch = true;
        }
        if (ifMarch) {
            return;
        }
        this.tsSimpleCode.setSplsht(new GB2Alpha().String2Alpha(this.tsSimpleCode.getCodeName()));

        /**
         * 如果是树形，需要设置层级编码
         * 根的话层级编码和编码设置一样
         * 不是根的话，设置层级编码为父的层级编码+.+编码
         */
        if(this.ifTree) {
            this.executeTreeSimpleCode();
        } else {
            this.tsSimpleCode.setCodeLevelNo(this.tsSimpleCode.getCodeNo());
        }

        String msg = this.systemModuleService.saveOrUpdateSimpleCode(this.tsSimpleCode, ifTree);
        if(StringUtils.isNotBlank(msg)) {
            RequestContext requestContext = RequestContext.getCurrentInstance();
            if(this.ifTree) {
            	requestContext.update("tabView:editTreeForm:codeTreeDataTable");
                this.searchTreeSimpleCode();
            }else {
            	requestContext.update("tabView:editForm:codeDataTable");
                this.searchCodeList();
            }
            JsfUtil.addErrorMessage(msg);
        }else {
        	RequestContext requestContext = RequestContext.getCurrentInstance();
        	requestContext.execute("CodeEditDialog.hide()");
            if(this.ifTree) {
            	requestContext.update("tabView:editTreeForm:codeTreeDataTable");
                this.searchTreeSimpleCode();
            }else {
            	requestContext.update("tabView:editForm:codeDataTable");
                this.searchCodeList();
            }
            JsfUtil.addSuccessMessage("保存成功！");
        }
	}

    /**
     * <p>方法描述：找展开的节点 </p>
     * pw 2024/2/4
     **/
    private void explanNode(TreeNode node){
        if (null != node.getParent() && node.isExpanded() && node.getData() instanceof TsSimpleCode) {
            TsSimpleCode simpleCode = (TsSimpleCode) node.getData();
            this.explandRidSet.add(simpleCode.getRid());
        }
        List<TreeNode> childList = node.getChildren();
        if (CollectionUtils.isEmpty(childList)) {
            return;
        }
        for (TreeNode child : childList) {
            this.explanNode(child);
        }
    }

    /**
     * <p>方法描述：树形结构码表新增时 不显示层级编码 这里要设置层级编码 </p>
     * pw 2024/2/1
     **/
    private void executeTreeSimpleCode (){
        //新添加的使用这个逻辑
        if (null != this.tsSimpleCode.getRid()) {
            return;
        }
        if(ifRoot) {
            this.tsSimpleCode.setCodeLevelNo(this.tsSimpleCode.getCodeNo());
            this.tsSimpleCode.setCodePath(this.tsSimpleCode.getCodeName());
        }else {
            this.tsSimpleCode.setCodeLevelNo(tempCodeLevel+"."+this.tsSimpleCode.getCodeNo());
            String codePath = this.parentCodePath+"_"+this.tsSimpleCode.getCodeName();
            if (codePath.length() > 500) {
                codePath = codePath.substring(0,500);
            }
            this.tsSimpleCode.setCodePath(codePath);
        }
    }

	/**
	 * 删除
	 */
	public void deleteAction() {
        if(this.ifTree) {
        	boolean childFlag = systemModuleService.countSimpCodeHasChild(tsSimpleCode);
            if(childFlag) {
                JsfUtil.addErrorMessage("该数据已有子节点，不允许删除！");
                return;
            }
        }
//        if(null!=this.tsSimpleCode.getPublishTag() && this.tsSimpleCode.getPublishTag()==1) {
//        	JsfUtil.addErrorMessage("该数据已发布，不能删除！");
//        	return;
//        }
        String msg = this.systemModuleService.deleteSimpleCode(this.tsSimpleCode.getRid());
        if(StringUtils.isNotBlank(msg)) {
            JsfUtil.addErrorMessage(msg);
            return;
        }else {
            if(this.ifTree) {
                this.searchTreeSimpleCode();
                RequestContext.getCurrentInstance().update("tabView:editTreeForm:codeTreeDataTable");
            }else {
            	RequestContext.getCurrentInstance().update("tabView:editForm:codeDataTable");
                this.searchCodeList();
            }
        }
    }

    /**
     * 停用
     */
    public void stopAction() {
    	this.systemModuleService.updateCodeState(this.codeId, 0);
		 if(this.ifTree) {
			 this.searchTreeSimpleCode();
		 }else {
			 this.searchCodeList();
		 }
    }

    /**
     * 启用
     */
    public void startAction() {
    	this.tsSimpleCode = systemModuleService.findSimpleCodeByRid(codeId);
//    	if(null!= this.tsSimpleCode.getPublishTag() && this.tsSimpleCode.getPublishTag()==1) {
//        	JsfUtil.addErrorMessage("该数据已发布，不能启用！");
//        	return;
//        }
        String msg = this.systemModuleService.updateCodeState(this.codeId, 1);
        if(StringUtils.isNotBlank(msg)) {
        	JsfUtil.addErrorMessage(msg);
        }else {
        	if(this.ifTree) {
        		RequestContext.getCurrentInstance().update("tabView:editTreeForm:codeTreeDataTable");
        		this.searchTreeSimpleCode();
        	}else {
        		RequestContext.getCurrentInstance().update("tabView:editForm:codeDataTable");
        		this.searchCodeList();
        	}
        }
       
    }

    /**
     * 编码添加初始化
     */
    public void codeAddInitAction() {
    	tempCodeLevel = null;
    	if( null != this.tsSimpleCode)	{
    		tempCodeLevel = this.tsSimpleCode.getCodeLevelNo();
            this.parentCodePath = this.tsSimpleCode.getCodePath();
    	}
        this.ifEidt = Boolean.FALSE;
        this.tsSimpleCode = new TsSimpleCode();
        this.tsSimpleCode.setTsCodeType(new TsCodeType(this.codeTypeId));
        this.tsSimpleCode.setCreateDate(new Date());
        this.tsSimpleCode.setCreateManid(this.sessionData.getUser().getRid());
        this.tsSimpleCode.setModifyDate(new Date());
    }

    /**
     * 编码修改初始化
     */
    public void codeModInitAction() {
//    	this.tsSimpleCode = this.systemModuleService.findSimpleCodeByRid(this.codeId);
    	tempCodeLevel = null;
    	this.ifEidt = Boolean.TRUE;
        this.tsSimpleCode = this.systemModuleService.findSimpleCodeByRid(this.codeId);
//        if(null!=this.tsSimpleCode && null!=this.tsSimpleCode.getPublishTag() && this.tsSimpleCode.getPublishTag()==1) {
//        	JsfUtil.addErrorMessage("该数据已发布，不能修改！");
//        	return;
//        }
        this.tsSimpleCode.setModifyDate(new Date());
        this.tsSimpleCode.setModifyManid(this.sessionData.getUser().getRid());

        RequestContext requestContext = RequestContext.getCurrentInstance();
        if(ifTree) {
        	requestContext.reset("tabView:editTreeForm:codeEditDialog");
        }else{
        	requestContext.reset("tabView:editForm:codeEditDialog");
        }
        requestContext.execute("PF('CodeEditDialog').show()");
    }

    public void addMsrunitAction(){
		StringBuffer show = msrunitObj.getShow();
		if("1".equals(addType)){
			show.append(StringUtils.trimToEmpty(msrunitObj.getZf()));
			msrunitObj.setZf(null);
		}else if("2".equals(addType)){
			show.append("<sup>").append(StringUtils.trimToEmpty(msrunitObj.getSb())).append("</sup>");
			msrunitObj.setSb(null);
		}else if("3".equals(addType)){
			show.append("<sub>").append(StringUtils.trimToEmpty(msrunitObj.getXb())).append("</sub>");
			msrunitObj.setXb(null);
		}else if("4".equals(addType)){
			show.append(StringUtils.trimToEmpty(msrunitObj.getTszf()));
			msrunitObj.setTszf(null);
		}else if("5".equals(addType)){
			show.append(StringUtils.trimToEmpty(msrunitObj.getTszf()));
			msrunitObj.setShow(new StringBuffer());
		}

	}
    
    public void saveMsrunitAction(){
		String s = msrunitObj.getShow().toString();
		if(StringUtils.isNotBlank(s)){
			int a = s.replaceAll("(<sub>)|(</sub>)|(<sup>)|(</sup>)", "").length();
			if(a>10){
				JsfUtil.addErrorMessage("计量单位最大不能超过10个字符！");
				return;
			}
		}
		tsSimpleCode.setCodeName(msrunitObj.getShow().toString());
		RequestContext.getCurrentInstance().update("tabView:editForm:msrunit");
		RequestContext.getCurrentInstance().update("tabView:editForm:codeName");
		RequestContext.getCurrentInstance().execute("PF('MsrunitDialog').hide()");
	}
    
    public void showMsrunitDialogAction(){
		msrunitObj = new MsrunitObj();
		msrunitObj.getShow().append(StringUtils.trimToEmpty(tsSimpleCode.getCodeName()));
		RequestContext.getCurrentInstance().execute("PF('MsrunitDialog').show()");
	}
    
	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder(" SELECT T1.RID, T1.PARAM_TYPE, T1.CODE_TYPE_NAME, T1.CODE_TYPE_DESC, T1.TREE_TAG,T1.STATE,T1.ERR_RSN,T1.RMK,'' as err_tip,'' as rmk_tip  ");
        sb.append(" FROM TS_CODE_TYPE T1 ");
        sb.append(" WHERE 1=1 ");
        if(StringUtils.isNotBlank(this.searchSystemType)) {
            sb.append(" AND T1.PARAM_TYPE ='").append(this.searchSystemType).append("' ");
        }
        if(StringUtils.isNotBlank(this.searchTypeName)) {
            sb.append(" AND T1.CODE_TYPE_DESC LIKE :codeTypeDesc");
            this.paramMap.put("codeTypeDesc", "%"+this.searchTypeName.trim()+"%");
        }
        if (StringUtils.isNotBlank(this.searchTypeCode)) {
            sb.append(" AND T1.CODE_TYPE_NAME LIKE :searchTypeCode");
            this.paramMap.put("searchTypeCode", "%"+this.searchTypeCode.trim()+"%");
        }
        if (null != this.searchTypeState && this.searchTypeState.length > 0) {
            sb.append(" AND T1.STATE IN (").append(StringUtils.array2string(this.searchTypeState, ",")).append(") ");
        }
        sb.append(" ORDER BY T1.PARAM_TYPE, T1.CODE_TYPE_NAME ");
        String h1 = sb.toString();
		return new String[]{h1,"select count(*) FROM (" + h1 + ") " };
	}

    @Override
    public void processData(List<?> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for(int i=0; i<list.size(); i++) {
            Object[] os = (Object[]) list.get(i);
            os[1] = (SystemType)EnumUtils.findEnum(SystemType.class, os[1]);
            String rmk = null == os[7] ? null : os[7].toString();
            String errRsn = null == os[6] ? null : os[6].toString();
            //中文分号换行
            if (StringUtils.isNotBlank(rmk)) {
                os[9] = rmk.replace("；","<br/>");
            }
            if (StringUtils.isNotBlank(errRsn)) {
                os[8] = errRsn.replace("；","<br/>");
            }
        }
    }

    /**
     * <p>方法描述：自检 </p>
     * pw 2024/1/30
     **/
    public void autoCheck(){
        List<TsCodeType> codeTypeList = this.systemModuleService.findTreeNodeCodeType();
        if (CollectionUtils.isEmpty(codeTypeList)) {
            return;
        }
        int succCount = 0;
        for (TsCodeType codeType : codeTypeList) {
            String errMsg = this.checkTreeSimpleCode(codeType.getRid());
            if (StringUtils.isNotBlank(errMsg) && errMsg.length() > 2000) {
                errMsg = errMsg.substring(0, 2000);
            }
            if (StringUtils.isBlank(errMsg)) {
                succCount++;
            }
            codeType.setState(StringUtils.isBlank(errMsg) ? 1 : 2);
            codeType.setErrRsn(errMsg);
        }
        try {
            this.systemModuleService.updateCodeTypeState(codeTypeList);
            JsfUtil.addSuccessMessage("本次共自检"+codeTypeList.size()+"条；成功"+succCount+"条；失败"+(codeTypeList.size()-succCount)+"条！");
            //自检完成后查询
            this.searchAction();
        } catch(Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("自检失败！");
        }
    }

    /**
     * <p>方法描述：树形码表检测 </p>
     * pw 2024/1/30
     **/
    private String checkTreeSimpleCode(Integer codeTypeId){
        List<Object[]> codeList = this.systemModuleService.findTreeSimpleCodeInfoByCodeType(codeTypeId);
        if (CollectionUtils.isEmpty(codeList)) {
            return null;
        }
        StringBuffer buffer = new StringBuffer();
        Set<String> codeSet = new HashSet<>();
        Set<String> codeLevelSet = new HashSet<>();
        Set<String> stopCodeSet = new HashSet<>();
        for (Object[] objArr : codeList) {
            //CODE_NO,CODE_LEVEL_NO,IF_REVEAL
            String codeNo = null == objArr[0] ? null : objArr[0].toString();
            String codeLevelNo = null == objArr[1] ? null : objArr[1].toString();
            Integer ifReveal = null == objArr[2] ? null : Integer.parseInt(objArr[2].toString());
            if (StringUtils.isBlank(codeNo) || StringUtils.isBlank(codeLevelNo)) {
                continue;
            }
            String[] levelArr = codeLevelNo.split("\\.");
            codeSet.add(codeNo);
            codeLevelSet.add(codeLevelNo);
            if (null == ifReveal || 1 != ifReveal) {
                stopCodeSet.add(codeNo);
                //停用的 不检测
                continue;
            }
            int length = levelArr.length;
            if (!levelArr[length-1].equals(codeNo)) {
                buffer.append("编码").append(codeNo).append("的结构层次编码最末级与编码不一致；");
            }
            if (1 == length) {
                continue;
            }
            boolean ifMarch = false;
            Set<String> existCodeSet = new HashSet<>();
            //校验 上级编码
            for (int i=0; i<length-1;i++) {
                String curCode = levelArr[i];
                //当前自己的编码出现在上级层级编码中
                if (codeNo.equals(curCode)) {
                    ifMarch = true;
                    break;
                }
                //上级编码找不到
                if (!codeSet.contains(curCode)) {
                    ifMarch = true;
                    break;
                }
                //出现类似1001.1001.1003的情况
                if (existCodeSet.contains(curCode)) {
                    ifMarch = true;
                    break;
                }
                existCodeSet.add(curCode);
            }
            //上级层次编码
            String preLevelCode = codeLevelNo.substring(0, codeLevelNo.lastIndexOf("."));
            //校验上级结构层次编码
            if (!ifMarch && !codeLevelSet.contains(preLevelCode)) {
                ifMarch = true;
            }
            if (ifMarch) {
                buffer.append("编码").append(codeNo).append("的上级结构层次编码不存在；");
                continue;
            }
            if (null == ifReveal || 1 != ifReveal) {
                continue;
            }
            //stopCodeSet
            String[] parentCodes = preLevelCode.split("\\.");
            for (String parentCode : parentCodes) {
                if (stopCodeSet.contains(parentCode)){
                    ifMarch = true;
                    break;
                }
            }
            if (ifMarch) {
                buffer.append("启用的编码").append(codeNo).append("的上级状态不能为停用；");
            }
        }
        return buffer.length() > 0 ? buffer.toString() : null;
    }

    /**
     * <p>方法描述：展开树 </p>
     * pw 2024/2/4
     **/
    public void expandNode(NodeExpandEvent event){
        TreeNode node = event.getTreeNode();
        if (node.getData() instanceof TsSimpleCode) {
            this.explandRidSet.add(((TsSimpleCode) node.getData()).getRid());
        }
    }

    /**
     * <p>方法描述：收起树 </p>
     * pw 2024/2/4
     **/
    public void collapseNode(NodeCollapseEvent event){
        TreeNode node = event.getTreeNode();
        if (node.getData() instanceof TsSimpleCode) {
            this.explandRidSet.remove(((TsSimpleCode) node.getData()).getRid());
        }
    }

    //getters and setters
    public TsSimpleCode getTsSimpleCode() {
        return tsSimpleCode;
    }

    public void setTsSimpleCode(TsSimpleCode tsSimpleCode) {
        this.tsSimpleCode = tsSimpleCode;
    }

    public Integer getCodeTypeId() {
        return codeTypeId;
    }

    public void setCodeTypeId(Integer codeTypeId) {
        this.codeTypeId = codeTypeId;
    }

    public Integer getCodeId() {
        return codeId;
    }

    public void setCodeId(Integer codeId) {
        this.codeId = codeId;
    }

    public List<TsSimpleCode> getCodeList() {
        return codeList;
    }

    public void setCodeList(List<TsSimpleCode> codeList) {
        this.codeList = codeList;
    }

    public TreeNode getTreeNode() {
        return treeNode;
    }

    public void setTreeNode(TreeNode treeNode) {
        this.treeNode = treeNode;
    }

    public Map<String, String> getSystemTypeMap() {
        return systemTypeMap;
    }

    public void setSystemTypeMap(Map<String, String> systemTypeMap) {
        this.systemTypeMap = systemTypeMap;
    }

    public String getSearchSystemType() {
        return searchSystemType;
    }

    public void setSearchSystemType(String searchSystemType) {
        this.searchSystemType = searchSystemType;
    }

    public String getSearchTypeName() {
        return searchTypeName;
    }

    public void setSearchTypeName(String searchTypeName) {
        this.searchTypeName = searchTypeName;
    }

    public List<String> getSearchCodeState() {
        return searchCodeState;
    }

    public void setSearchCodeState(List<String> searchCodeState) {
        this.searchCodeState = searchCodeState;
    }

    public String getSearchCodeName() {
        return searchCodeName;
    }

    public void setSearchCodeName(String searchCodeName) {
        this.searchCodeName = searchCodeName;
    }

    public String getCurCodeTypeName() {
        return curCodeTypeName;
    }

    public void setCurCodeTypeName(String curCodeTypeName) {
        this.curCodeTypeName = curCodeTypeName;
    }

    public TreeNode getSelectedNode() {
        return selectedNode;
    }

    public void setSelectedNode(TreeNode selectedNode) {
        this.selectedNode = selectedNode;
    }

    public boolean isIfTree() {
        return ifTree;
    }

    public void setIfTree(boolean ifTree) {
        this.ifTree = ifTree;
    }

    public boolean isIfRoot() {
        return ifRoot;
    }

    public void setIfRoot(boolean ifRoot) {
        this.ifRoot = ifRoot;
    }

    public boolean isIfEidt() {
        return ifEidt;
    }

    public void setIfEidt(boolean ifEidt) {
        this.ifEidt = ifEidt;
    }


	public List<TsSimpleCode> getOrderList() {
		return orderList;
	}

	public void setOrderList(List<TsSimpleCode> orderList) {
		this.orderList = orderList;
	}
	
	public TsCodeType getAddCodeType() {
		return addCodeType;
	}

	public void setAddCodeType(TsCodeType addCodeType) {
		this.addCodeType = addCodeType;
	}

	public Short getSystemTypeEdit() {
		return systemTypeEdit;
	}

	public void setSystemTypeEdit(Short systemTypeEdit) {
		this.systemTypeEdit = systemTypeEdit;
	}
	public MsrunitObj getMsrunitObj() {
		return msrunitObj;
	}
	public void setMsrunitObj(MsrunitObj msrunitObj) {
		this.msrunitObj = msrunitObj;
	}
	public List<TsSimpleCode> getMsrunitCodes() {
		return msrunitCodes;
	}
	public void setMsrunitCodes(List<TsSimpleCode> msrunitCodes) {
		this.msrunitCodes = msrunitCodes;
	}
	public String getAddType() {
		return addType;
	}
	public void setAddType(String addType) {
		this.addType = addType;
	}
	public boolean isIfSpecial() {
		return ifSpecial;
	}
	public void setIfSpecial(boolean ifSpecial) {
		this.ifSpecial = ifSpecial;
	}
	public String getCurCodeTypeNo() {
		return curCodeTypeNo;
	}
	public void setCurCodeTypeNo(String curCodeTypeNo) {
		this.curCodeTypeNo = curCodeTypeNo;
	}

    public String getSearchTypeCode() {
        return searchTypeCode;
    }

    public void setSearchTypeCode(String searchTypeCode) {
        this.searchTypeCode = searchTypeCode;
    }

    public String[] getSearchTypeState() {
        return searchTypeState;
    }

    public void setSearchTypeState(String[] searchTypeState) {
        this.searchTypeState = searchTypeState;
    }

    public String getCodeTypeRmk() {
        return codeTypeRmk;
    }

    public void setCodeTypeRmk(String codeTypeRmk) {
        this.codeTypeRmk = codeTypeRmk;
    }

    public String getCheckState() {
        return checkState;
    }

    public void setCheckState(String checkState) {
        this.checkState = checkState;
    }

    public String getCheckFailRsn() {
        return checkFailRsn;
    }

    public void setCheckFailRsn(String checkFailRsn) {
        this.checkFailRsn = checkFailRsn;
    }

    public String getSearchMixCode() {
        return searchMixCode;
    }

    public void setSearchMixCode(String searchMixCode) {
        this.searchMixCode = searchMixCode;
    }

    public String getParentCodePath() {
        return parentCodePath;
    }

    public void setParentCodePath(String parentCodePath) {
        this.parentCodePath = parentCodePath;
    }
}
