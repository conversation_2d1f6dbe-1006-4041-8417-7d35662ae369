package com.chis.modules.system.web;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TsTxtype;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.protocol.IMsgSend;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * 通讯方式维护受管Bean(应急模块中迁出)
 *
 * <AUTHOR>
 * @createDate 2015年6月17日
 */
@ManagedBean(name = "tbTxTypeBean")
@ViewScoped
public class TbTxTypeBean extends FacesBean {
    private static final long serialVersionUID = -1004769444419436558L;
	/**ejb session bean*/
	private SystemModuleServiceImpl systemService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	/**状态查询*/
    private String[] searchStates;
    /**对象*/
    private TsTxtype tsTxtype = new TsTxtype();
    /**对象ID*/
    private Integer txTyperid;
    /**状态值*/
    private Short changeStateValue;
    /**所有的实现类*/
    private Map<String, String> implClassMap;
    /**查询结果*/
    private List<TsTxtype> resultList;
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	

    @PostConstruct
    public void init() {
        this.implClassMap = new HashMap<String, String>();
        Map<String, IMsgSend> beans = SpringContextHolder.getBeans(IMsgSend.class);
        if(null != beans && beans.size() > 0) {
        	for(IMsgSend msgSend : beans.values()) {
        		this.implClassMap.put(msgSend.description(), msgSend.getClass().getName());
        	}
        }
        searchStates = new String[]{"1"};
        searchAction();
    }

    /**
     * 数据库查询
     */
    public void searchAction(){
        resultList = systemService.findMsgType(searchStates);
    }

    /**
     * 保存
     */
    public void saveAction(){
    	tsTxtype.setModifyDate(new Date());
    	tsTxtype.setModifyManid(sessionData.getUser().getRid());
    	systemService.updateTxType(tsTxtype);
        JsfUtil.addSuccessMessage("修改成功！");
        searchAction();
        RequestContext.getCurrentInstance().execute("PF('EditDialog').hide()");
    }

    /**
     * 改状态
     */
    public void stateChangeAction(){
    	systemService.updateTbEmtxTypeState(txTyperid, changeStateValue);
        searchAction();
    }

    public String getImpl(String impl){
        Set<String> set = implClassMap.keySet();
        for(String s : set){
            if(implClassMap.get(s).equals(impl)){
                return s;
            }
        }
        return "";
    }



    public String[] getSearchStates() {
        return searchStates;
    }

    public void setSearchStates(String[] searchStates) {
        this.searchStates = searchStates;
    }

    public Integer getTxTyperid() {
        return txTyperid;
    }

    public void setTxTyperid(Integer txTyperid) {
        this.txTyperid = txTyperid;
    }

    public Short getChangeStateValue() {
        return changeStateValue;
    }

    public void setChangeStateValue(Short changeStateValue) {
        this.changeStateValue = changeStateValue;
    }

    public List<TsTxtype> getResultList() {
        return resultList;
    }

    public void setResultList(List<TsTxtype> resultList) {
        this.resultList = resultList;
    }

    public Map<String, String> getImplClassMap() {
        return implClassMap;
    }

    public void setImplClassMap(Map<String, String> implClassMap) {
        this.implClassMap = implClassMap;
    }

    public TsTxtype getTsTxtype() {
        return tsTxtype;
    }

    public void setTsTxtype(TsTxtype tsTxtype) {
        this.tsTxtype = tsTxtype;
    }
}
