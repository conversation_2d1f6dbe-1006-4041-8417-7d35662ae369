package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.GB2Alpha;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TdFormField;
import com.chis.modules.system.entity.TdFormTable;
import com.chis.modules.system.entity.TdFormType;
import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsRpt;
import com.chis.modules.system.enumn.DynaFieldShowType;
import com.chis.modules.system.enumn.DynaFieldType;
import com.chis.modules.system.enumn.SearchFlag;
import com.chis.modules.system.enumn.SystemParamType;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.DyncFormServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.google.common.collect.Sets;

/**
 * 动态表单数据结构维护
 * 
 * <AUTHOR>
 * 
 */
@ManagedBean(name = "tdDynaFormStructureBean")
@ViewScoped
public class TdDynaFormStructureBean extends FacesEditBean {

	private static final long serialVersionUID = -8855237274775385449L;
	/** ejb session bean */

	private DyncFormServiceImpl dyncFormServiceImpl = (DyncFormServiceImpl) SpringContextHolder
			.getBean(DyncFormServiceImpl.class);
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);

	// ====================查询条件开始==========================
	/** 表中文名 */
	private String searchTableCnName;
	/** 表英文名 */
	private String searchTableEnName;
	/** 表模式 */
	private String[] searchTableProp;
	/** 表状态 */
	private String[] searchTableState;
	// ====================查询条件结束==========================

	/** 表单Id */
	private Integer formId;
	/** 1：单表 2：主子表 */
	private Integer formModel;
	/** 主表单 */
	private TdFormTable mTdFormTable;
	/** 子表单 */
	private TdFormTable sTdFormTable;

	/** 流程表单类型Id */
	private Integer formTypeId;
	/** 流程表单集合 */
	private List<TdFormType> formTypeList = new ArrayList<TdFormType>();
	/** 流程表单名称 */
	private String formName;
	/** 状态 */
	private String[] searchState;

	// ====================查询条件结束==========================

	/** 选中的字段集合 */
	private TdFormField selTdFormField;
	/** 字段类型 */
	private List<SelectItem> fieldTypeList;
	/** 字段展示类型 */
	private List<SelectItem> fieldShowList;

	/** 绑定表单类别 */
	private TdFormType bindTdFormType;
	/** 弹出框 表单类型名称 */
	private String formTypeDiag;
	/** 表单类型集合 */
	private List<TdFormType> sourceTypeList;
	private List<TdFormType> showTypeList;

	// 报表模版选择
	/** 系统类型 */
	private Short sysType;
	/** 查询报表名称 */
	private String searchRptName;
	/** 系统类型列表 */
	private Map<String, Short> sysTypeMap;
	private List<TsRpt> sourceRptList;
	private List<TsRpt> showRptList;
	/** 选中报表实体 */
	private TsRpt selRpt;

	// 报表模版选择
	/** 系统类型 */
	private Short sysTypeC;
	/** 查询报表名称 */
	private String searchCodeName;
	private List<TsCodeType> sourceCodeList;
	private List<TsCodeType> showCodeList;
	/** 选中报表实体 */
	private TsCodeType selCode;

	/** 操作表类型 1：单表 2：主表 3：子表 */
	private Integer opType;

	/** 缓存码表类型集合 */
	private Map<String, TsCodeType> cachMap;

	/** 动态表单前缀 */
	private final String DYNC_TABLE_PREFIX = "TZ_";
	/** 系统字段，不能添加 */
	private Set<String> SYSTEM_FILEDS = Sets.newHashSet("ID", "MAIN_ID");

	/** ++生成的拼音字段 */
	private String pyMode;
	/** 查询条件：所有能看到的系统类型comm,risk ，隔开 */
	private String allSystem;

	/** +样式选择集合 */
	private List<SelectItem> styleList = null;
	/** +样式选择Id */
	private String styleId = "dynaModelPf.xml";
	
	private List<SelectItem> searchTypeList;

	@PostConstruct
	public void init() {
		initSearchType();
		this.initSearchCondition();
		this.searchAction();
	}
	
		
	/**
	 * 初始化查询匹配类型
	 */
	public void initSearchType() {
		searchTypeList = new ArrayList<SelectItem>();
		SearchFlag [] searchs = SearchFlag.values();
		for (SearchFlag s : searchs) {
			SelectItem itm = new SelectItem();
			itm.setValue(s.getFlag());
			itm.setLabel(s.getRealFlag());
			searchTypeList.add(itm);
		}
	}

	/**
	 * 根据不同类型生成拼音码
	 */
	public void genAutoPy() {
		if (StringUtils.isNotBlank(pyMode)) {
			if ("mainTB".equals(pyMode) || "MT".equals(pyMode)) {
				if (StringUtils.isBlank(mTdFormTable.getEnName())) {
					GB2Alpha gb2Alpha = new GB2Alpha();
					mTdFormTable.setEnName(gb2Alpha.String2Alpha(mTdFormTable.getCnName()));
				}
			} else if ("subTB".equals(pyMode)) {
				if (StringUtils.isBlank(sTdFormTable.getEnName())) {
					GB2Alpha gb2Alpha = new GB2Alpha();
					sTdFormTable.setEnName(gb2Alpha.String2Alpha(sTdFormTable.getCnName()));
				}
			} else if ("field".equals(pyMode)) {
				if (StringUtils.isBlank(selTdFormField.getFdEnname())) {
					GB2Alpha gb2Alpha = new GB2Alpha();
					selTdFormField.setFdEnname(gb2Alpha.String2Alpha(selTdFormField.getFdCnname()));
				}
			}
		}
	}

	/**
	 * 修改字段详细信息
	 */
	public void modFieldSub() {
		this.selCode = null;
		String codeTypeNo = this.selTdFormField.getCodeTypeNo();
		if (null != codeTypeNo) {
			this.selCode = this.cachMap.get(codeTypeNo);
		}

		// 字段类型
		this.selTdFormField.setFdDbtypeStr(null);
		String fdDbtype = this.selTdFormField.getFdDbtype();
		if (StringUtils.isNotBlank(fdDbtype)) {
			String findTypeCN = EnumUtils.findTypeCN(DynaFieldType.class, fdDbtype);
			this.selTdFormField.setFdDbtypeStr(findTypeCN);
		}

		// 展现形式
		this.selTdFormField.setDataSrcStr(null);
		String dataSrc = this.selTdFormField.getDataSrc();
		if (StringUtils.isNotBlank(dataSrc)) {
			String findTypeCN = EnumUtils.findTypeCN(DynaFieldShowType.class, dataSrc);
			this.selTdFormField.setDataSrcStr(findTypeCN);
		}

		// 跳转子表修改详情
		setActiveTab(3);
	}

	/**
	 * 修改字段详细信息
	 */
	public void viewFieldSub() {
		modFieldSub();
		// 跳转子表修改详情
		setActiveTab(4);
	}

	/**
	 * 保存子表信息
	 */
	public void saveSubAction() {
		this.selTdFormField.setCodeTypeNo(null);
		if (null != selCode) {
			this.selTdFormField.setCodeTypeNo(selCode.getCodeTypeName());
		}
		String validateField = this.validateField(this.selTdFormField);
		if (StringUtils.isNotBlank(validateField)) {
			if (null != this.opType) {
				if (1 == this.opType) {
					validateField = new StringBuffer("数据结构").append(validateField).toString();
				} else if (2 == this.opType) {
					validateField = new StringBuffer("主表数据结构").append(validateField).toString();
				} else if (3 == this.opType) {
					validateField = new StringBuffer("子表数据结构").append(validateField).toString();
				}
			}
			JsfUtil.addErrorMessage(validateField);
		} else {
			setActiveTab(1);
		}
	}

	/**
	 * 修改表模式类型
	 */
	public void changeTableModel() {
		if (null != this.formModel) {
			if (formModel.intValue() == 1) {// 单表
				this.mTdFormTable = new TdFormTable();
				this.mTdFormTable.setState(0);
				this.mTdFormTable.setFieldList(new ArrayList<TdFormField>());
				this.mTdFormTable.setFormProp(1);

				this.sTdFormTable = null;
			} else if (formModel.intValue() == 2) {// 主子表模式
				this.mTdFormTable = new TdFormTable();
				this.mTdFormTable.setFormProp(2);
				this.mTdFormTable.setState(0);
				this.mTdFormTable.setFieldList(new ArrayList<TdFormField>());

				this.sTdFormTable = new TdFormTable();
				this.sTdFormTable.setFormProp(3);
				this.sTdFormTable.setState(0);
				this.sTdFormTable.setFieldList(new ArrayList<TdFormField>());
			}
		}
	}

	/**
	 * 展示报表模版类型
	 */
	public void showCodeModel() {
		sysTypeC = null;
		if (null == sysTypeMap || sysTypeMap.size() == 0) {
			sysTypeMap = new LinkedHashMap<String, Short>();
			String paramValue = this.commService.findParamValue(SystemParamType.SYSTEM_MODULES.getTypeNo());
			if (StringUtils.isNotBlank(paramValue)) {
				String[] splits = paramValue.split("##");
				StringBuilder sb = new StringBuilder();
				for (String s : splits) {
					SystemType st = (SystemType) EnumUtils.findEnum(SystemType.class, s);
					this.sysTypeMap.put(st.getTypeCN(), st.getTypeNo());
					sb.append(",'").append(st.getTypeNo().toString()).append("'");
				}
				this.allSystem = sb.toString().substring(1);
			}
		}
		this.findCodeList();
	}

	/**
	 * 清空报表选择
	 */
	public void clearCodeAciton() {
		this.selRpt = null;
	}

	/**
	 * 初始化报表集合
	 */
	private void findCodeList() {
		Integer userId = this.sessionData.getUser().getRid();
		sourceCodeList = this.systemModuleService.findCodeTypeList(false, userId);
		if (null == sourceCodeList) {
			sourceCodeList = new ArrayList<TsCodeType>();
		}
		showCodeList = new ArrayList<TsCodeType>();
		showCodeList.addAll(sourceCodeList);
	}

	public void filterCodeList() {
		showCodeList = new ArrayList<TsCodeType>();
		if (null != sourceCodeList) {
			for (TsCodeType tsCodeType : sourceCodeList) {
				boolean param1Meet = false;
				boolean param2Meet = false;
				if (null == sysTypeC || sysTypeC.toString().equals(tsCodeType.getSystemType().getTypeNo().toString())) {
					param1Meet = true;
				}
				if (StringUtils.isBlank(searchCodeName) || searchCodeName.indexOf(tsCodeType.getCodeTypeDesc()) != -1) {
					param2Meet = true;
				}
				if (param1Meet && param2Meet) {
					showCodeList.add(tsCodeType);
				}
			}
		}
	}

	/**
	 * 展示报表模版类型
	 */
	public void showRptModel() {
		sysType = null;
		if (null == sysTypeMap || sysTypeMap.size() == 0) {
			sysTypeMap = new LinkedHashMap<String, Short>();
			SystemType[] systemTypes = SystemType.values();
			for (SystemType s : systemTypes) {
				sysTypeMap.put(s.getTypeCN(), s.getTypeNo());
			}
		}
		this.findRptList();
	}

	/**
	 * 清空报表选择
	 */
	public void clearRptAciton() {
		this.selRpt = null;
	}

	/**
	 * 初始化报表集合
	 */
	private void findRptList() {
		sourceRptList = this.dyncFormServiceImpl.findTsRpt(allSystem);
		if (null == sourceRptList) {
			sourceRptList = new ArrayList<TsRpt>();
		}
		showRptList = new ArrayList<TsRpt>();
		showRptList.addAll(sourceRptList);
	}

	public void filterRptList() {
		showRptList = new ArrayList<TsRpt>();
		if (null != sourceRptList) {
			for (TsRpt tsRpt : sourceRptList) {
				boolean param1Meet = false;
				boolean param2Meet = false;
				if (null == sysType || sysType.toString().equals(tsRpt.getSystemType().getTypeNo().toString())) {
					param1Meet = true;
				}
				if (StringUtils.isBlank(searchRptName) || searchRptName.indexOf(tsRpt.getRptnam()) != -1) {
					param2Meet = true;
				}
				if (param1Meet && param2Meet) {
					showRptList.add(tsRpt);
				}
			}
		}
	}

	/**
	 * 弹出单选框初始化
	 */
	public void showSingleAction() {
		this.formTypeDiag = null;
		this.searchSingAction();
	}

	/**
	 * 重新查询数据集
	 */
	private void searchSingAction() {
		sourceTypeList = this.dyncFormServiceImpl.findTdFormTypeList();
		if (null == sourceTypeList) {
			sourceTypeList = new ArrayList<TdFormType>();
		}
		showTypeList = new ArrayList<TdFormType>();
		showTypeList.addAll(sourceTypeList);
	}

	/**
	 * 保存表单类型
	 */
	public void saveSingleAction() {
		String verifyTdFormType = this.dyncFormServiceImpl.verifyTdFormType(bindTdFormType);
		if (StringUtils.isNotBlank(verifyTdFormType)) {
			JsfUtil.addErrorMessage(verifyTdFormType);
		} else {
			this.dyncFormServiceImpl.saveTdFormType(bindTdFormType);
			JsfUtil.addSuccessMessage("保存成功！");
			this.searchSingAction();
			this.filterSingelSel();

			RequestContext currentInstance = RequestContext.getCurrentInstance();
			currentInstance.execute("PF('SingleAddDialog').hide();");
			currentInstance.update("tabView:editForm:singleDatatable");
		}
	}

	/**
	 * 删除表单类型
	 */
	public void deleteSingleAction() {
		String deleteTdFormType = this.dyncFormServiceImpl.deleteTdFormType(this.bindTdFormType.getRid());
		if (StringUtils.isNotBlank(deleteTdFormType)) {
			JsfUtil.addErrorMessage(deleteTdFormType);
			return;
		}
		this.searchSingAction();
		this.filterSingelSel();
	}

	/**
	 * 过滤表单选择
	 */
	public void filterSingelSel() {
		this.showTypeList = new ArrayList<TdFormType>();
		for (TdFormType tdFormType : sourceTypeList) {
			String typeName = tdFormType.getTypeName();
			if (StringUtils.isBlank(formTypeDiag) || typeName.indexOf(formTypeDiag) != -1) {
				this.showTypeList.add(tdFormType);
			}
		}
	}

	/**
	 * 添加表单类型
	 */
	public void addSingleSel() {
		this.bindTdFormType = new TdFormType();
		this.bindTdFormType.setCreateDate(new Date());
		this.bindTdFormType.setCreateManid(this.sessionData.getUser().getRid());
	}

	/**
	 * 添加表单字段
	 */
	public void addSub() {
		if (null != opType) {
			if (1 == opType.intValue() || 2 == opType.intValue()) {// 主表
				List<TdFormField> fieldList = mTdFormTable.getFieldList();
				TdFormField tempField = new TdFormField();
				tempField.setTdFormTableByTableId(mTdFormTable);
				fieldList.add(tempField);
			} else if (3 == opType.intValue()) {// 子表
				List<TdFormField> fieldList = sTdFormTable.getFieldList();
				TdFormField tempField = new TdFormField();
				tempField.setTdFormTableByTableId(sTdFormTable);
				fieldList.add(tempField);
			}
		}
	}

	/**
	 * 移除主表查询的字段
	 */
	public void removeFieldSub() {
		if (null != opType) {
			if (1 == opType.intValue() || 2 == opType.intValue()) {// 主表
				List<TdFormField> fieldList = this.mTdFormTable.getFieldList();
				fieldList.remove(selTdFormField);
			} else if (3 == opType.intValue()) {// 子表
				List<TdFormField> fieldList = this.sTdFormTable.getFieldList();
				fieldList.remove(selTdFormField);
			}
		}
	}

	/**
	 * 提交记录
	 */
	public void confirmEditRecord(Integer state) {
		if (null != mTdFormTable) {
			if (state.intValue() == 0) {
				this.dyncFormServiceImpl.operateTableState(mTdFormTable.getRid(), 0);
				this.mTdFormTable.setState(0);
				JsfUtil.addSuccessMessage("撤销成功！");
			} else if (state.intValue() == 1) {
				// 首先保存记录
				if (verifyAction()) {
					// 提交猜需要保存
					this.saveAction();
					this.dyncFormServiceImpl.commitTdFormTable(mTdFormTable, sTdFormTable);
					this.mTdFormTable.setState(1);
					JsfUtil.addSuccessMessage("提交成功！");
				}
			}
		}
	}

	/**
	 * 删除方法
	 */
	public void deleteAction() {
		if (null != formId) {
			this.dyncFormServiceImpl.delTdFormDef(formId);
			JsfUtil.addSuccessMessage("删除成功！");
		}
	}

	/**
	 * 初始化编辑界面集合
	 */
	private void initEditList() {
		if (null == fieldTypeList || fieldTypeList.size() == 0) {
			fieldTypeList = new ArrayList<SelectItem>();
			DynaFieldType[] values = DynaFieldType.values();
			for (DynaFieldType dynaFieldType : values) {
				fieldTypeList.add(new SelectItem(dynaFieldType.getTypeNo(), dynaFieldType.getTypeCN()));
			}
		}
		if (null == fieldShowList || fieldShowList.size() == 0) {
			fieldShowList = new ArrayList<SelectItem>();
			DynaFieldShowType[] values = DynaFieldShowType.values();
			for (DynaFieldShowType dynaFieldShowType : values) {
				fieldShowList.add(new SelectItem(dynaFieldShowType.getTypeNo(), dynaFieldShowType.getTypeCN()));
			}
		}
		if (null == cachMap || cachMap.size() == 0) {
			cachMap = new HashMap<String, TsCodeType>();
			List<TsCodeType> findCodeTypeList = this.systemModuleService.findCodeTypeList(true, null);
			if (null != findCodeTypeList && findCodeTypeList.size() > 0) {
				for (TsCodeType tsCodeType : findCodeTypeList) {
					cachMap.put(tsCodeType.getCodeTypeName(), tsCodeType);
				}
			}
		}

	}

	@Override
	public void addInit() {
		// 默认添加单表模式
		this.formModel = 1;
		// 根据数据模型加载表
		this.changeTableModel();
		this.initEditList();
	}

	@Override
	public void viewInit() {
		modInit();
	}

	@Override
	public void modInit() {
		// 初始化页面集合
		this.initEditList();
		if (null != this.formId) {
			this.formModel = 1;
			this.sTdFormTable = null;
			this.mTdFormTable = this.dyncFormServiceImpl.findTdFormTable(this.formId);
			mTdFormTable.setEnName(mTdFormTable.getEnName().replaceFirst(DYNC_TABLE_PREFIX, ""));
			if (null != this.mTdFormTable && null != this.mTdFormTable.getChildList()
					&& this.mTdFormTable.getChildList().size() > 0) {
				this.sTdFormTable = this.mTdFormTable.getChildList().get(0);
				sTdFormTable.setEnName(sTdFormTable.getEnName().replaceFirst(DYNC_TABLE_PREFIX, ""));
				this.formModel = 2;
			}
			this.initSingleRadio();
		}
	}

	private void initSingleRadio() {
		if (null != this.mTdFormTable && null != this.mTdFormTable.getFieldList()) {// 单表
			List<TdFormField> fieldList = this.mTdFormTable.getFieldList();
			for (TdFormField tdFormField : fieldList) {
				this.switchBooleanToVal(tdFormField, false);
			}
		}
		if (null != this.sTdFormTable && null != this.sTdFormTable.getFieldList()) {// 单表
			List<TdFormField> fieldList = this.sTdFormTable.getFieldList();
			for (TdFormField tdFormField : fieldList) {
				this.switchBooleanToVal(tdFormField, false);
			}
		}
	}

	@Override
	public void saveAction() {
		if (verifyAction()) {
			mTdFormTable = this.dyncFormServiceImpl.saveTdFormTable(mTdFormTable, sTdFormTable);
			mTdFormTable.setEnName(mTdFormTable.getEnName().replaceFirst(DYNC_TABLE_PREFIX, ""));
			if (null != mTdFormTable && null != mTdFormTable && mTdFormTable.getChildList().size() > 0) {
				sTdFormTable = mTdFormTable.getChildList().get(0);
				sTdFormTable.setEnName(sTdFormTable.getEnName().replaceFirst(DYNC_TABLE_PREFIX, ""));
			}
			this.initSingleRadio();
			JsfUtil.addSuccessMessage("保存成功！");
			this.searchAction();
		}
	}

	private boolean verifyAction() {
		if (null == this.formModel) {
			JsfUtil.addErrorMessage("表单模式不允许为空！");
			return false;
		}
		// 当前字段
		Set<String> fieldNames = new HashSet<String>();
		// 单表模式
		String enName = mTdFormTable.getEnName();
		String fieldsetName = "数据结构";
		if (StringUtils.isBlank(mTdFormTable.getCnName())) {
			JsfUtil.addErrorMessage("表中文名不能为空！");
			return false;
		}
		if (StringUtils.isBlank(enName)) {
			JsfUtil.addErrorMessage("表名不能为空！");
			return false;
		}
		if (formModel.intValue() == 2) {
			fieldsetName = "主表数据结构";
			if (StringUtils.isBlank(sTdFormTable.getCnName())) {
				JsfUtil.addErrorMessage("子表中文名不能为空！");
				return false;
			}
			if (StringUtils.isBlank(sTdFormTable.getEnName())) {
				JsfUtil.addErrorMessage("子表名不能为空！");
				return false;
			}
			
			if(enName.equals(sTdFormTable.getEnName()))	{
				JsfUtil.addErrorMessage("主表名称与子表名称不能重复！");
				return false;
			}
		}
		// 验证表名（如果该表的版本号不等于1，则说明该表已经生成过，无需验证，否则进行验证）
		String[] tableName = new String[2];
		String[] rids = new String[2];

		tableName[0] = new StringBuilder(DYNC_TABLE_PREFIX).append(enName).toString();
		rids[0] = mTdFormTable.getRid() == null ? null : mTdFormTable.getRid().toString();

		if (formModel.intValue() == 2) {
			tableName[1] = new StringBuilder(DYNC_TABLE_PREFIX).append(sTdFormTable.getEnName()).toString();
			rids[1] = sTdFormTable.getRid() == null ? null : sTdFormTable.getRid().toString();
		}
		String verifyDynaFormTable = this.dyncFormServiceImpl.verifyDynaFormTable(tableName, rids);
		if (StringUtils.isNotBlank(verifyDynaFormTable)) {
			JsfUtil.addErrorMessage(verifyDynaFormTable);
			return false;
		}
		List<TdFormField> fieldList = mTdFormTable.getFieldList();
		if (null != fieldList && fieldList.size() > 0) {
			String verifyFieldList = this.verifyFieldList(fieldList, fieldNames);
			if (StringUtils.isNotBlank(verifyFieldList)) {
				JsfUtil.addErrorMessage(fieldsetName + "中" + verifyFieldList);
				return false;
			}
			for (TdFormField tdFormField : fieldList) {
				this.switchBooleanToVal(tdFormField, true);
			}
		} else {
			JsfUtil.addErrorMessage(fieldsetName + "中字段集合不允许为空！");
			return false;
		}
		if (formModel.intValue() == 2) {
			fieldList = sTdFormTable.getFieldList();
			if (null != fieldList && fieldList.size() > 0) {
				String verifyFieldList = this.verifyFieldList(fieldList, fieldNames);
				if (StringUtils.isNotBlank(verifyFieldList)) {
					JsfUtil.addErrorMessage("子表数据结构中" + verifyFieldList);
					return false;
				}
				for (TdFormField tdFormField : fieldList) {
					this.switchBooleanToVal(tdFormField, true);
				}
			} else {
				JsfUtil.addErrorMessage("子表数据结构中字段集合不允许为空！");
				return false;
			}
		}
		return true;
	}

	/**
	 * 验证字段集合中是否存在相同或者符合条件
	 * 
	 * @return
	 */
	private String verifyFieldList(List<TdFormField> fieldList, Set<String> fieldNames) {
		if (null != fieldList && fieldList.size() > 0) {
			for (TdFormField tdFormField : fieldList) {
				// 英文名
				String fdEnname = tdFormField.getFdEnname();
				if (StringUtils.isBlank(tdFormField.getFdCnname())) {
					return "字段说明不能为空！";
				}
				if (StringUtils.isBlank(tdFormField.getFdEnname())) {
					return "字段名不能为空！";
				}
				if (SYSTEM_FILEDS.contains(fdEnname)) {
					return "字段名：" + fdEnname + " 为系统字段，请勿使用！";
				}
				if (fieldNames.contains(fdEnname)) {
					return "字段名：" + fdEnname + " 重复！";
				}
				// 字段类型
				String fdDbtype = tdFormField.getFdDbtype();
				if (StringUtils.isBlank(fdDbtype)) {
					return "字段类型不能为空！";
				}
				String validateField = this.validateField(tdFormField);
				if (StringUtils.isNotBlank(validateField)) {
					return validateField;
				}
				if (tdFormField.getIsShow() != null
						&& (tdFormField.getIsShow().intValue() == 1 || tdFormField.getIsShow().intValue() == 2)) {
					Integer rowNum = tdFormField.getRowNum();
					if (null == rowNum || rowNum.intValue() <= 0) {
						return "字段说明：" + tdFormField.getFdCnname() + " 编辑时行号必须大于0！";
					}

					Integer colNum = tdFormField.getColNum();
					if (null == colNum || colNum.intValue() <= 0) {
						return "字段说明：" + tdFormField.getFdCnname() + " 编辑时列号必须大于0！";
					}

					Integer colSpan = tdFormField.getColSpan();
					if (null == colSpan || colSpan.intValue() <= 0 || colSpan.intValue() >= 12) {
						return "字段说明：" + tdFormField.getFdCnname() + " 合并列数必须大于0且不超过12！";
					}

					String dataSrc = tdFormField.getDataSrc();
					if (StringUtils.isBlank(dataSrc)) {
						return "字段说明：" + tdFormField.getFdCnname() + " 展示类型不允许为空！";
					}

				}
				// 集合中新增字段名
				fieldNames.add(tdFormField.getFdEnname());
			}
		}
		return null;
	}

	/**
	 * 验证字段类型
	 * 
	 * @return
	 */
	private String validateField(TdFormField tdFormField) {
		if (null != tdFormField) {
			// 字段类型
			String fdDbtype = tdFormField.getFdDbtype();
			DynaFieldType findEnum = (DynaFieldType) EnumUtils.findEnum(DynaFieldType.class, fdDbtype);
			// 字段名
			String fdCnname = tdFormField.getFdCnname();
			switch (findEnum) {
			case NVARCHAR2:
				Integer lenChar = tdFormField.getLenChar();
				if (null == lenChar) {
					return new StringBuffer("字段说明：").append(fdCnname).append("的字符串精度不能为空！").toString();
				} else if (lenChar <= 0 || lenChar > 4000) {
					return new StringBuffer("字段说明：").append(fdCnname).append("的字符串精度不满足1到4000限制！").toString();
				}
				tdFormField.setLenDemi(0);
				tdFormField.setLenInt(0);
				if (null != this.mTdFormTable.getRid() && tdFormField.getLenChar() < tdFormField.getLenCharLast()) {// 如果不是初次版本，则不允许修改字段长度比当前小
					return new StringBuffer("字段说明：").append(fdCnname).append("的字符串精度不允许小于升级前：")
							.append(tdFormField.getLenCharLast()).append(" ！").toString();
				}
				break;
			case INTEGER:
				Integer lenInt = tdFormField.getLenInt();
				if (null == lenInt) {
					return new StringBuffer("字段说明：").append(fdCnname).append("的整数精度不能为空！").toString();
				} else if (lenInt <= 0 || lenInt > 9) {
					return new StringBuffer("字段说明：").append(fdCnname).append("的整数精度不满足1到9位数限制！").toString();
				}
				if (null != this.mTdFormTable.getRid() && tdFormField.getLenInt() < tdFormField.getLenIntLast()) {// 如果不是初次版本，则不允许修改字段长度比当前小
					return new StringBuffer("字段说明：").append(fdCnname).append("的整数精度不允许小于升级前：")
							.append(tdFormField.getLenIntLast()).append(" ！").toString();
				}
				tdFormField.setLenDemi(0);
				tdFormField.setLenChar(0);
				break;
			case NUMBER:
				lenInt = tdFormField.getLenInt();
				Integer lenDemi = tdFormField.getLenDemi();
				if (null == lenInt) {
					return new StringBuffer("字段说明：").append(fdCnname).append("的整数精度不能为空！").toString();
				} else if (lenInt <= 0 || lenInt > 20) {
					return new StringBuffer("字段说明：").append(fdCnname).append("的整数精度不满足1到20位数限制！").toString();
				}

				if (null != this.mTdFormTable.getRid() && tdFormField.getLenInt() < tdFormField.getLenIntLast()) {// 如果不是初次版本，则不允许修改字段长度比当前小
					return new StringBuffer("字段说明：").append(fdCnname).append("的整数精度不允许小于升级前：")
							.append(tdFormField.getLenIntLast()).append(" ！").toString();
				}

				if (null == lenDemi) {
					return new StringBuffer("字段说明：").append(fdCnname).append("的小数精度不能为空！").toString();
				} else if (lenDemi < 0 || lenDemi > 20) {
					return new StringBuffer("字段说明：").append(fdCnname).append("的小数精度不满足1到20位数限制！").toString();
				}

				if (null != this.mTdFormTable.getRid() && tdFormField.getLenDemi() < tdFormField.getLenDemiLast()) {// 如果不是初次版本，则不允许修改字段长度比当前小
					return new StringBuffer("字段说明：").append(fdCnname).append("的整数精度不允许小于升级前：")
							.append(tdFormField.getLenDemiLast()).append(" ！").toString();
				}
				tdFormField.setLenChar(0);
				break;
			case DATE:
				tdFormField.setLenInt(0);
				tdFormField.setLenDemi(0);
				tdFormField.setLenChar(0);
				break;
			case CLOB:
				tdFormField.setLenInt(0);
				tdFormField.setLenDemi(0);
				tdFormField.setLenChar(0);
				break;
			default:
				break;
			}

			String dataSrc = tdFormField.getDataSrc();
			if (StringUtils.isNotBlank(dataSrc)) {
				DynaFieldShowType showType = (DynaFieldShowType) EnumUtils.findEnum(DynaFieldShowType.class, dataSrc);
				switch (showType) {
				case DICT_SELECT_ONE:
				case DICT_SELECT_MANY:
					String dataFrom = tdFormField.getDataFrom();
					if ("2".equals(dataFrom)) {// sql语句查询
						if (StringUtils.isNotBlank(tdFormField.getQuerySql())) {
							String querySql = tdFormField.getQuerySql();
							String verifyQuerySql = this.dyncFormServiceImpl.verifyQuerySql(querySql);
							if (StringUtils.isNotBlank(verifyQuerySql)) {
								return new StringBuffer("字段说明：").append(fdCnname).append("的").append(verifyQuerySql)
										.toString();
							}
						}
						// 选中SQL语句，则清空码表
						tdFormField.setCodeTypeNo(null);
					} else if ("1".equals(dataFrom)) {
						// 选中码表语句，则清空SQL语句
						tdFormField.setQuerySql(null);
					}
					break;
				default:
					break;
				}
			}
		}
		return null;
	}

	/**
	 * 将数值转换成值
	 * 
	 * @param ifBooleanToVal
	 *            true 布尔转值 false 值转布尔
	 * @param tdFormField
	 */
	private void switchBooleanToVal(TdFormField tdFormField, boolean ifBooleanToVal) {
		if (null != tdFormField) {
			if (ifBooleanToVal) {
				tdFormField.setIsList(tdFormField.isListBol() ? 1 : 0);
				tdFormField.setIsProVal(tdFormField.isProValBol() ? 1 : 0);
				tdFormField.setIsReq(tdFormField.isReqBol() ? 1 : 0);
				tdFormField.setIsSearch(tdFormField.isIfSearch() ? 1 : 0);
				tdFormField.setSearchType(tdFormField.isIfSearch() ? tdFormField.getSearchType() : null);
			} else {
				tdFormField
						.setListBol((null != tdFormField.getIsList() && 1 == tdFormField.getIsList().intValue()) ? true
								: false);
				tdFormField.setProValBol((null != tdFormField.getIsProVal() && 1 == tdFormField.getIsProVal()
						.intValue()) ? true : false);
				tdFormField.setReqBol((null != tdFormField.getIsReq() && 1 == tdFormField.getIsReq().intValue()) ? true
						: false);
			}

			// 字段类型
			tdFormField.setFdDbtypeStr(null);
			String fdDbtype = tdFormField.getFdDbtype();
			if (StringUtils.isNotBlank(fdDbtype)) {
				String findTypeCN = EnumUtils.findTypeCN(DynaFieldType.class, fdDbtype);
				tdFormField.setFdDbtypeStr(findTypeCN);
			}

			// 展现形式
			tdFormField.setDataSrcStr(null);
			String dataSrc = tdFormField.getDataSrc();
			if (StringUtils.isNotBlank(dataSrc)) {
				String findTypeCN = EnumUtils.findTypeCN(DynaFieldShowType.class, dataSrc);
				tdFormField.setDataSrcStr(findTypeCN);
			}
		}
	}

	@Override
	public String[] buildHqls() {
		StringBuffer searchCommSql = new StringBuffer();
		searchCommSql.append(" FROM TD_FORM_TABLE T WHERE T.MAIN_TAB_ID IS NULL AND 1=1");
		if (StringUtils.isNotBlank(this.searchTableCnName)) {
			searchCommSql.append(" AND T.CN_NAME LIKE :CNNAEM ESCAPE '\\\' ");
			this.paramMap.put("CNNAEM", "%" + StringUtils.convertBFH(this.searchTableCnName) + "%");
		}
		if (StringUtils.isNotBlank(this.searchTableEnName)) {
			searchCommSql.append(" AND T.EN_NAME LIKE :ENNAME ESCAPE '\\\' ");
			this.paramMap.put("ENNAME", "%" + StringUtils.convertBFH(this.searchTableEnName) + "%");
		}
		if (null != searchTableProp && searchTableProp.length > 0) {
			String array2string = StringUtils.array2string(searchTableProp, ",");
			searchCommSql.append(" AND T.FORM_PROP IN ( ").append(array2string).append(") ");
		}
		if (null != searchTableState && searchTableState.length > 0) {
			String array2string = StringUtils.array2string(searchTableState, ",");
			searchCommSql.append(" AND T.STATE IN ( ").append(array2string).append(") ");
		}

		String searchSql = new StringBuffer("SELECT T.EN_NAME,T.CN_NAME,T.FORM_PROP,T.STATE, T.RID ")
				.append(searchCommSql).append(" ORDER BY T.CN_NAME,T.EN_NAME ").toString();
		String countSql = new StringBuffer("SELECT COUNT(T.RID) ").append(searchCommSql).toString();
		return new String[] { searchSql, countSql };
	}

	/**
	 * 初始化查询条件
	 */
	private void initSearchCondition() {
		// 初始化查询条件
		this.formTypeList = this.dyncFormServiceImpl.findFormTypeList();
		this.formTypeId = null;
		this.formName = null;
		this.bindTdFormType = new TdFormType();
		this.ifSQL = true;
	}

	public Integer getFormTypeId() {
		return formTypeId;
	}

	public void setFormTypeId(Integer formTypeId) {
		this.formTypeId = formTypeId;
	}

	public List<TdFormType> getFormTypeList() {
		return formTypeList;
	}

	public void setFormTypeList(List<TdFormType> formTypeList) {
		this.formTypeList = formTypeList;
	}

	public String getFormName() {
		return formName;
	}

	public void setFormName(String formName) {
		this.formName = formName;
	}

	public String[] getSearchState() {
		return searchState;
	}

	public void setSearchState(String[] searchState) {
		this.searchState = searchState;
	}

	public Integer getFormId() {
		return formId;
	}

	public void setFormId(Integer formId) {
		this.formId = formId;
	}

	public TdFormType getBindTdFormType() {
		return bindTdFormType;
	}

	public void setBindTdFormType(TdFormType bindTdFormType) {
		this.bindTdFormType = bindTdFormType;
	}

	public TdFormField getSelTdFormField() {
		return selTdFormField;
	}

	public void setSelTdFormField(TdFormField selTdFormField) {
		this.selTdFormField = selTdFormField;
	}

	public List<SelectItem> getFieldTypeList() {
		return fieldTypeList;
	}

	public void setFieldTypeList(List<SelectItem> fieldTypeList) {
		this.fieldTypeList = fieldTypeList;
	}

	public List<SelectItem> getFieldShowList() {
		return fieldShowList;
	}

	public void setFieldShowList(List<SelectItem> fieldShowList) {
		this.fieldShowList = fieldShowList;
	}

	public String getFormTypeDiag() {
		return formTypeDiag;
	}

	public void setFormTypeDiag(String formTypeDiag) {
		this.formTypeDiag = formTypeDiag;
	}

	public List<TdFormType> getSourceTypeList() {
		return sourceTypeList;
	}

	public void setSourceTypeList(List<TdFormType> sourceTypeList) {
		this.sourceTypeList = sourceTypeList;
	}

	public List<TdFormType> getShowTypeList() {
		return showTypeList;
	}

	public void setShowTypeList(List<TdFormType> showTypeList) {
		this.showTypeList = showTypeList;
	}

	public Short getSysType() {
		return sysType;
	}

	public void setSysType(Short sysType) {
		this.sysType = sysType;
	}

	public String getSearchRptName() {
		return searchRptName;
	}

	public void setSearchRptName(String searchRptName) {
		this.searchRptName = searchRptName;
	}

	public Map<String, Short> getSysTypeMap() {
		return sysTypeMap;
	}

	public void setSysTypeMap(Map<String, Short> sysTypeMap) {
		this.sysTypeMap = sysTypeMap;
	}

	public List<TsRpt> getSourceRptList() {
		return sourceRptList;
	}

	public void setSourceRptList(List<TsRpt> sourceRptList) {
		this.sourceRptList = sourceRptList;
	}

	public List<TsRpt> getShowRptList() {
		return showRptList;
	}

	public void setShowRptList(List<TsRpt> showRptList) {
		this.showRptList = showRptList;
	}

	public TsRpt getSelRpt() {
		return selRpt;
	}

	public void setSelRpt(TsRpt selRpt) {
		this.selRpt = selRpt;
	}

	public TdFormTable getmTdFormTable() {
		return mTdFormTable;
	}

	public void setmTdFormTable(TdFormTable mTdFormTable) {
		this.mTdFormTable = mTdFormTable;
	}

	public TdFormTable getsTdFormTable() {
		return sTdFormTable;
	}

	public void setsTdFormTable(TdFormTable sTdFormTable) {
		this.sTdFormTable = sTdFormTable;
	}

	public Integer getOpType() {
		return opType;
	}

	public void setOpType(Integer opType) {
		this.opType = opType;
	}

	public Short getSysTypeC() {
		return sysTypeC;
	}

	public void setSysTypeC(Short sysTypeC) {
		this.sysTypeC = sysTypeC;
	}

	public String getSearchCodeName() {
		return searchCodeName;
	}

	public void setSearchCodeName(String searchCodeName) {
		this.searchCodeName = searchCodeName;
	}

	public List<TsCodeType> getSourceCodeList() {
		return sourceCodeList;
	}

	public void setSourceCodeList(List<TsCodeType> sourceCodeList) {
		this.sourceCodeList = sourceCodeList;
	}

	public List<TsCodeType> getShowCodeList() {
		return showCodeList;
	}

	public void setShowCodeList(List<TsCodeType> showCodeList) {
		this.showCodeList = showCodeList;
	}

	public TsCodeType getSelCode() {
		return selCode;
	}

	public void setSelCode(TsCodeType selCode) {
		this.selCode = selCode;
	}

	public String getPyMode() {
		return pyMode;
	}

	public void setPyMode(String pyMode) {
		this.pyMode = pyMode;
	}

	public String getStyleId() {
		return styleId;
	}

	public void setStyleId(String styleId) {
		this.styleId = styleId;
	}

	public List<SelectItem> getStyleList() {
		return styleList;
	}

	public void setStyleList(List<SelectItem> styleList) {
		this.styleList = styleList;
	}

	public String getSearchTableCnName() {
		return searchTableCnName;
	}

	public void setSearchTableCnName(String searchTableCnName) {
		this.searchTableCnName = searchTableCnName;
	}

	public String getSearchTableEnName() {
		return searchTableEnName;
	}

	public void setSearchTableEnName(String searchTableEnName) {
		this.searchTableEnName = searchTableEnName;
	}

	public String[] getSearchTableProp() {
		return searchTableProp;
	}

	public void setSearchTableProp(String[] searchTableProp) {
		this.searchTableProp = searchTableProp;
	}

	public String[] getSearchTableState() {
		return searchTableState;
	}

	public void setSearchTableState(String[] searchTableState) {
		this.searchTableState = searchTableState;
	}

	public Integer getFormModel() {
		return formModel;
	}

	public void setFormModel(Integer formModel) {
		this.formModel = formModel;
	}
	
	public List<SelectItem> getSearchTypeList() {
		return searchTypeList;
	}

	public void setSearchTypeList(List<SelectItem> searchTypeList) {
		this.searchTypeList = searchTypeList;
	}
	

}
