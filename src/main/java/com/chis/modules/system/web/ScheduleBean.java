package com.chis.modules.system.web;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.ScheduleEvent;
import org.quartz.CronExpression;
import org.quartz.Job;
import org.quartz.SchedulerException;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TdOaSchedule;
import com.chis.modules.system.entity.TsTxtype;
import com.chis.modules.system.job.SecheduleJob;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.ScheduleServiceImpl;
import com.chis.modules.system.utils.DefaultScheduleModel;
import com.chis.modules.system.utils.QuartzManager;

/**
 * 日程管理
 * 
 * <AUTHOR>
 * @createTime 2015年10月16日
 */
@ManagedBean(name = "scheduleBean")
@ViewScoped
public class ScheduleBean extends FacesBean {

	private static final long serialVersionUID = -69668282325896228L;
	/** EJB session bean */
	private ScheduleServiceImpl scheduleService = (ScheduleServiceImpl) SpringContextHolder.getBean(ScheduleServiceImpl.class);
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private DefaultScheduleModel defaultScheduleModel;
	private TdOaSchedule tdOaSchedule = new TdOaSchedule();
	/** 通讯方式 */
	private List<TsTxtype> txtypeList;

	public ScheduleBean() {
		this.txtypeList = this.commService.findAllUseableTxtype();
		this.searchAction();
	}

	public void searchAction() {
		this.defaultScheduleModel = new DefaultScheduleModel(this.sessionData.getUser().getRid().toString(), null);
	}

	public void onDateSelect(SelectEvent selectEvent) {
		System.out.println("--onDateSelect--");
		this.tdOaSchedule = new TdOaSchedule();
		this.tdOaSchedule.setBeginTime((Date) selectEvent.getObject());
		this.tdOaSchedule.setCreateManid(this.sessionData.getUser().getRid());
		this.tdOaSchedule.setExecuteMan(this.sessionData.getUser());
		this.tdOaSchedule.setIsRemind(0);
		this.tdOaSchedule.setPublicType(0);
		this.tdOaSchedule.setScheduleType(1);
	}

	public void onEventSelect(SelectEvent selectEvent) {
		System.out.println("--onEventSelect--");
		ScheduleEvent event = (ScheduleEvent) selectEvent.getObject();
		this.tdOaSchedule = scheduleService.findScheduleById((Integer) event.getData());
		if( null == this.tdOaSchedule)	{
			JsfUtil.addErrorMessage("你选择的日程记录已经不存在，请刷新页面！");
		}else{
			RequestContext cI = RequestContext.getCurrentInstance();
			cI.execute("PF('EditDialog').show()");
			cI.update("mainForm:EditDialog");
		}
	}

	public DefaultScheduleModel getDefaultScheduleModel() {
		return defaultScheduleModel;
	}

	public void saveAction() {
		if (this.tdOaSchedule.getRemindTime() > 0) {
			// 需要提醒
			this.tdOaSchedule.setIsRemind(1);
		} else {
			// 不需要提醒
			this.tdOaSchedule.setIsRemind(0);
		}
		if (this.tdOaSchedule.getSeasonal().intValue() == 0) {
			
			//不是周期
			if(null == this.tdOaSchedule.getBeginTime()) {
				JsfUtil.addErrorMessage("日程开始时间不能为空！");
				return;
			}
			if(null == this.tdOaSchedule.getEndTime()) {
				JsfUtil.addErrorMessage("日程结束时间不能为空！");
				return;
			}
			if(this.tdOaSchedule.getEndTime().before(this.tdOaSchedule.getBeginTime())) {
				JsfUtil.addErrorMessage("日程开始时间不能大于结束时间！");
				return;
			}
			
			// 不是周期性
			this.tdOaSchedule.setPeriodType((short) 0);
			this.tdOaSchedule.setDayOfMon(null);
			this.tdOaSchedule.setDayOfWeek(null);
			this.tdOaSchedule.setHourOfDay(null);
			this.tdOaSchedule.setMinOfDay(null);
		} else {
			//周期性
			this.tdOaSchedule.setBeginTime(null);
			this.tdOaSchedule.setEndTime(null);
		}
		this.formatCronExpression();

		// ~~~~~~~~~~~~~~~~~~~~~~~~~测试~~~~~~~~~~~~~~~~~~~~~~~~~~
		// String testCron = "0 32 9 17 9 ? 2015";
		// String testCron = "0,20,40 * 9-14 * * ?";
		// this.tdOaSchedule.setExpressions(testCron);
		// ~~~~~~~~~~~~~~~~~~~~~~~~~测试~~~~~~~~~~~~~~~~~~~~~~~~~~

		String msg = this.scheduleService.saveOrUpdateSchedule(this.tdOaSchedule);
		if (StringUtils.isNotBlank(msg) && StringUtils.isNumeric(msg)) {
			if (this.tdOaSchedule.getIsRemind().intValue() == 1) {
				Integer userId =this.sessionData.getUser().getRid();
				String quartzNo = new StringBuilder("schedule_").append(userId).append("_").append(msg).toString();
				try {
					/** 先移除，再添加 */
					QuartzManager.removeJob(quartzNo);

					/**
					 * 解析Cron表达式，获取下一次执行的时间是否大于当前时间； 如果大于当前时间则可以添加任务，否则返回
					 */
					CronExpression exp = new CronExpression(this.tdOaSchedule.getExpressions());
					Date now = new Date();
					now = exp.getNextValidTimeAfter(now);
					if (null != now) {
						Job job = new SecheduleJob();
						Map<String, Object> map = new HashMap<String, Object>();
						map.put("userId", userId);
						map.put("userNo", this.sessionData.getUser().getUserNo());
						map.put("mobile", null == this.sessionData.getUser().getMbNum() ? "" : this.sessionData.getUser().getMbNum());
						map.put("scheduleId", quartzNo);
						map.put("title", this.tdOaSchedule.getScheduleTitle());
						if(null != this.tdOaSchedule.getBeginTime()) {
							map.put("time", DateUtils.formatDate(this.tdOaSchedule.getBeginTime(), "yyyy-MM-dd HH:mm"));
						}
						map.put("remindMtd", null == this.tdOaSchedule.getRemindMtd() ? "" : this.tdOaSchedule.getRemindMtd());
						map.put("seasonal", this.tdOaSchedule.getSeasonal());

						QuartzManager.addJob(quartzNo, job, this.tdOaSchedule.getExpressions(), map);
					}
				} catch (SchedulerException e0) {
					e0.printStackTrace();
					try {
						QuartzManager.removeJob(quartzNo);
					} catch (SchedulerException e) {
						e.printStackTrace();
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		} else {
			JsfUtil.addErrorMessage(msg);
			return;
		}
		JsfUtil.addSuccessMessage("保存成功！");
		// RequestContext.getCurrentInstance().update("mainForm:mySchedule");
		RequestContext.getCurrentInstance().execute("mySchedule.update();EditDialog.hide()");
	}

	public void deleteScheduleAction() {
		if (null != this.tdOaSchedule && null != this.tdOaSchedule.getRid()) {
			Integer rid = this.tdOaSchedule.getRid();
			String msg = this.scheduleService.deleteSchedule(this.tdOaSchedule.getRid());
			if (StringUtils.isBlank(msg)) {
				try {
					Integer userId =this.sessionData.getUser().getRid();
					String quartzNo = new StringBuilder("schedule_").append(userId).append("_").append(msg).toString();
					QuartzManager.removeJob(quartzNo);
				} catch (SchedulerException e) {
					e.printStackTrace();
				}
				JsfUtil.addErrorMessage("删除成功！");
				// RequestContext.getCurrentInstance().update("mainForm:mySchedule");
				RequestContext.getCurrentInstance().execute("mySchedule.update();EditDialog.hide()");
			} else {
				JsfUtil.addErrorMessage(msg);
			}
		}
	}

	/**
	 * 根据schedule对象，初始化表达式
	 * 
	 * 
	 */
	private void formatCronExpression() {
		StringBuilder sb = new StringBuilder();
		this.tdOaSchedule.setExpressions(null);
		if (this.tdOaSchedule.getSeasonal().intValue() == 0 && this.tdOaSchedule.getIsRemind() > 0) {
			// 只执行一次的
			long l = this.tdOaSchedule.getBeginTime().getTime() - this.tdOaSchedule.getRemindTime();
			Calendar c = Calendar.getInstance();
			c.setTimeInMillis(l);

			sb.append(c.get(Calendar.SECOND)).append(" ").append(c.get(Calendar.MINUTE)).append(" ");
			sb.append(c.get(Calendar.HOUR_OF_DAY)).append(" ").append(c.get(Calendar.DATE)).append(" ");
			sb.append(c.get(Calendar.MONTH) + 1).append(" ? ").append(c.get(Calendar.YEAR)).append("-").append(c.get(Calendar.YEAR));

			this.tdOaSchedule.setExpressions(sb.toString());
			this.tdOaSchedule.setRemindDatetime(c.getTime());
		} else if (this.tdOaSchedule.getSeasonal().intValue() == 1) {
			//处理周期性表达式
			this.processSeasonalExp();
		}
	}
	
	/**
	 * 处理表达式
	 * 
	 * @return
	 */
	private void processSeasonalExp() {
		try {
			/**
			 * 周期性执行的
			 */
			Short execycle = this.tdOaSchedule.getPeriodType();
			// 提前时间 分钟
			Integer reTime = this.tdOaSchedule.getRemindTime() / 1000 / 60;
			String excep = "";
			if (execycle.intValue() == 4) {
				// 每月几号如 "0 15 10 15 * ?"
				StringBuilder time = new StringBuilder();
				time.append(this.tdOaSchedule.getDayOfMon()).append(" ").append(this.tdOaSchedule.getHourOfDay()).append(":")
						.append(this.tdOaSchedule.getMinOfDay());
				Date date = DateUtils.parseDate(time.toString(), "dd HH:mm");
				Date proDate = DateUtils.addMinutes(date, -reTime);
				String dateStr = DateUtils.formatDate(proDate, "mm HH dd");
				excep = "0 " + dateStr + " * ?";
			} else if (execycle.intValue() == 3) {
				// 每周周几如 "0 15 10 ? * MON"
				Date firsDate = DateUtils.findWeekDay(new Date(), this.tdOaSchedule.getDayOfWeek().intValue(), true);
				StringBuilder time = new StringBuilder(DateUtils.formatDate(firsDate, "yyyy-MM-dd"));
				time.append(" ").append(this.tdOaSchedule.getHourOfDay()).append(":").append(this.tdOaSchedule.getMinOfDay());
				Date date = DateUtils.parseDate(time.toString(), "yyyy-MM-dd HH:mm");
				Date proDate = DateUtils.addMinutes(date, -reTime);

				// 周数
				int dayForWeek = DateUtils.dayForWeek(proDate);
				String dateStr = DateUtils.formatDate(proDate, "mm HH");

				excep = "0 " + dateStr + " ? * " + DateUtils.getWeekOfInt(dayForWeek);
			} else if (execycle.intValue() == 2) {
				// 每天几点如 "0 15 10 * * ?"
				StringBuilder time = new StringBuilder();
				time.append(this.tdOaSchedule.getHourOfDay()).append(":").append(this.tdOaSchedule.getMinOfDay());
				Date date = DateUtils.parseDate(time.toString(), "HH:mm");
				Date proDate = DateUtils.addMinutes(date, -reTime);
				String dateStr = DateUtils.formatDate(proDate, "mm HH");
				excep = "0 " + dateStr + " * * ?";
			}
			System.err.println("SeasonExp:" + excep);
			this.tdOaSchedule.setExpressions(excep);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void setDefaultScheduleModel(DefaultScheduleModel defaultScheduleModel) {
		this.defaultScheduleModel = defaultScheduleModel;
	}

	public TdOaSchedule getTdOaSchedule() {
		return tdOaSchedule;
	}

	public void setTdOaSchedule(TdOaSchedule tdOaSchedule) {
		this.tdOaSchedule = tdOaSchedule;
	}

	public List<TsTxtype> getTxtypeList() {
		return txtypeList;
	}

	public void setTxtypeList(List<TsTxtype> txtypeList) {
		this.txtypeList = txtypeList;
	}

}
