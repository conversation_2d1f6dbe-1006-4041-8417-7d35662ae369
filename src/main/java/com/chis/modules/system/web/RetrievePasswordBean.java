package com.chis.modules.system.web;

import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsUserLoginRcd;
import com.chis.modules.system.logic.SmsResponseXmlPO;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemServiceImpl;
import com.chis.modules.system.service.TsUserLoginRcdService;
import com.chis.modules.system.utils.RandomUtil;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

/**
 *  <p>类描述：忘记账号密码</p>
 * @ClassAuthor hsj 2023-04-13 10:14
 */
@ManagedBean(name = "retrievePasswordBean")
@ViewScoped
public class RetrievePasswordBean {
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder
            .getBean(CommServiceImpl.class);
    private TsUserLoginRcdService loginRcdService = SpringContextHolder.getBean(TsUserLoginRcdService.class);
    private SystemServiceImpl systemService = (SystemServiceImpl)SpringContextHolder.getBean(SystemServiceImpl.class);
    /**************身份证验证*****************/
    //身份证号是否为空
    private boolean ifIdcEmtpy=Boolean.FALSE;
    //身份证号码格式是否错误
    private boolean ifIdcError=Boolean.FALSE;
    //身份证是否已存在
    private boolean ifIdcExsit=Boolean.FALSE;
    //身份证已存在是否停用
    private boolean ifIdcStop=Boolean.FALSE;
    //身份证号
    private String idc;
    private String encryIdc;

    /**************身份证验证*****************/
    /**************手机号码*****************/
    //手机号码是否为空
    private boolean ifMobileEmtpy=Boolean.FALSE;
    //手机号码格式是否错误
    private boolean ifMobileError=Boolean.FALSE;
    //手机号码是否匹配
    private boolean ifMobileMatch=Boolean.FALSE;
    //手机号码
    private String mobileNum;
    private String encryMobileNum;

    /**************手机号码*****************/
    /**************图形验证码*****************/
    private String verifyCode;
    //图形验证码
    private boolean ifVerifyError=Boolean.FALSE;
    private boolean ifVerifyMatch=Boolean.FALSE;

    /**************图形验证码*****************/
    /**************短信验证码*****************/
    private String smsVerifyCode;
    //是否达到上线
    private boolean ifSmsOnline=Boolean.FALSE;
    private boolean ifSmsErrror=Boolean.FALSE;
    /** 1天允许发送短信的次数 */
    private String sendSmsTimes;
    /** 短信验证码有效期，默认15分钟 */
    private String smsValidMin;
    /** 短信模板 */
    private String smsTemplate;
    /** 短信接口地址 */
    private String sendSmsUrl;
    /** 短信接口参数用户Id */
    private String smsUserId;
    /** 短信接口参数用户账号 */
    private String smsUserName;
    /** 短信接口参数密码 */
    private String smsPassWord;

    /**************短信验证码*****************/
    private  String encryptKey;
    private TsUserInfo user;
    /** 设置新密码页面登录账号 */
    private String userNo;
    /** 用于传递的加密密码 */
    private String encryptPassword1;
    private String encryptPassword2;
    /** 用于页面显示的密码 */
    private String password;
    private String password2;
    /** 设置新密码校验用 */
    private Integer validatePwd;
    private Integer validatePwd2;
    /** 传递uuid的key */
    private String pwdSetUuid;
    public  RetrievePasswordBean(){
        this.pwdSetUuid = "pwdSetUuid";
        this.userNo = null;
        HttpServletRequest req = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        //从session中获取数据
        String uuid = null == req.getSession().getAttribute(this.pwdSetUuid) ? null :
                req.getSession().getAttribute(this.pwdSetUuid).toString();
        if(StringUtils.isNotBlank(uuid)){
            this.userNo = null == req.getSession().getAttribute(uuid) ? null :
                    req.getSession().getAttribute(uuid).toString();
        }
        this.sendSmsTimes = PropertyUtils.getValueWithoutException("login.sendSmsTimes");
        this.smsValidMin = PropertyUtils.getValueWithoutException("login.smsValidMin");
        this.smsTemplate = PropertyUtils.getValueWithoutException("login.passwordSmsTemplate");
        this.sendSmsUrl = PropertyUtils.getValueWithoutException("login.sendSms.url");
        this.smsUserId = PropertyUtils.getValueWithoutException("login.smsUserId");
        this.smsUserName = PropertyUtils.getValueWithoutException("login.smsUserName");
        this.smsPassWord = PropertyUtils.getValueWithoutException("login.smsPassWord");
        this.encryptKey = PropertyUtils.getValueWithoutException("encrypt.key");
    }

    /**
     *  <p>方法描述：下一步</p>
     * @MethodAuthor hsj 2023-04-13 10:21
     */
    public void nextAction(){
        RequestContext.getCurrentInstance().execute("zwx_loading_stop();");
        //身份证+手机号验证+验证码
        boolean isFlag = veryData();
        //手机验证码验证
        this.ifSmsOnline =false;
        this.ifSmsErrror =false;
        List<Object[]> rcdList = this.loginRcdService.findNewestTsUserLoginRcd(this.mobileNum);
        if (CollectionUtils.isEmpty(rcdList)) {
            ifSmsErrror = true;
            isFlag =true;
        }
        Object[] obj = CollectionUtils.isEmpty(rcdList) ? null : rcdList.get(0);
        if (null != obj && !this.smsVerifyCode.equals(obj[1].toString())) {
            //验证码不正确
            ifSmsErrror = true;
            isFlag =true;
        }
        if(isFlag){
            changeVerImg();
            return;
        }
        //跳转到下一页
        try {
            this.redirectExeUpdatePwd();
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    /**
     *  <p>方法描述：验证</p>
     * @MethodAuthor hsj 2023-04-13 14:21
     */
    private boolean veryData() {
        boolean isFlag = false;
        this.user =new TsUserInfo();
        this.ifIdcEmtpy = false;
        this.ifIdcError = false;
        this.ifIdcExsit = false;
        this.ifIdcStop = false;
        this.ifMobileEmtpy = false;
        this.ifMobileError = false;
        this.ifMobileMatch = false;
        this.idc = null;
        this.mobileNum = null;
        try {
            //身份证验证
            if(StringUtils.isBlank(this.encryIdc)){
                this.ifIdcEmtpy = true;
                isFlag = true;
            }else {
                //身份证存在
                this.idc = AesEncryptUtils.aesDecrypt(this.encryIdc,this.encryptKey);
                if(StringUtils.isBlank(IdcUtils.checkIDC(this.idc))) {
                    //根据身份证查询
                    StringBuffer hql = new StringBuffer();
                    Map<String, Object> paramMap = new HashMap<>();
                    paramMap = new HashMap<>();
                    hql.append(" select t from  TsUserInfo t where t.idc =:idc");
                    paramMap.put("idc", this.idc);
                    List<TsUserInfo> list = commService.findDataByHqlNoPage(hql.toString(), paramMap);
                    if (CollectionUtils.isEmpty(list)) {
                        this.ifIdcExsit = true;
                        isFlag = true;
                    }else {
                        //身份证已存在是否停用
                        if(list.get(0).getIfReveal() == 0){
                            this.ifIdcStop = true;
                            isFlag = true;
                        }else{
                            //为空时也赋值 用于 下面 身份证号存在未停用的账户且手机号码符合规则
                            this.user = list.get(0);
                        }
                    }
                }else {
                    this.ifIdcError = true;
                    isFlag = true;
                }
            }
            //手机号验证
            if(StringUtils.isBlank(this.encryMobileNum)){
                this.ifMobileEmtpy = true;
                isFlag = true;
            }else {
                this.mobileNum = AesEncryptUtils.aesDecrypt(this.encryMobileNum,this.encryptKey);
                if(!StringUtils.vertyMobilePhone(this.mobileNum)){
                    this.ifMobileError = true;
                    isFlag = true;
                }else {
                    //身份证号存在未停用的账户且手机号码符合规则
                    if(user != null && user.getRid() != null && !this.mobileNum.equals(user.getMbNum())){
                        this.ifMobileMatch = true;
                        isFlag = true;
                    }
                }
            }
            //图形验证码
            this.ifVerifyError = false;
            this.ifVerifyMatch = false;
            String sessionString = String.valueOf(((char[])JsfUtil.getSession().getAttribute("sessionString")));
            if(StringUtils.isBlank(this.verifyCode)) {
                this.ifVerifyError =true;
                isFlag = true;
            }else {
                if(!this.verifyCode.trim().equalsIgnoreCase(sessionString.trim())) {
                    this.ifVerifyMatch =true;
                    isFlag = true;
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            isFlag = true;
        }
        return isFlag;
    }
   /**
    *  <p>方法描述：更新验证码</p>
    * @MethodAuthor hsj 2023-04-13 15:29
    */
    public void changeVerImg(){
        JsfUtil.getRequest().getSession().setAttribute("sessionString", RandomUtil.initIdentifyingCode());
        RequestContext.getCurrentInstance().execute("setVerImg()");
    }
    /**
     *  <p>方法描述：获取短信验证码</p>
     * @MethodAuthor hsj 2023-04-13 16:14
     */
    public void codeAction(){
        RequestContext.getCurrentInstance().execute("zwx_loading_stop();");
        this.ifSmsOnline = false;
        this.ifSmsErrror= false;
        if (veryData()){
            changeVerImg();
            return;
        }
        List<TsUserLoginRcd> list = loginRcdService.findTsUserLoginRcd(this.mobileNum);
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(list)) {
            Integer curSendSmsTimes = 20;
            if (StringUtils.isNotBlank(this.sendSmsTimes)) {
                curSendSmsTimes = Integer.parseInt(this.sendSmsTimes);
            }
            if (list.size() >= curSendSmsTimes) {
                this.ifSmsOnline = true;
                changeVerImg();
                return;
            }
        }
        sendMsg();
    }
    /**
     *  <p>方法描述：发送短信</p>
     * @MethodAuthor hsj 2023-04-13 16:28
     */
    public void sendMsg() {
        //短信日志
        TsUserLoginRcd rcd = new TsUserLoginRcd();
        rcd.setFkByUserId(this.user);
        rcd.setMobileNo(this.mobileNum);
        String checkCode = String.valueOf(new Random().nextInt(899999) + 100000);
        rcd.setCheckCode(checkCode);
        String message = this.smsTemplate.replace("checkCode", checkCode).replace("smsValidMin", this.smsValidMin);
        rcd.setNoticeCont(message);
        rcd.setValidDate(DateUtils.addMinutes(new Date(), Integer.valueOf(this.smsValidMin)));
        //调用短信接口
        try {
            String requestUrl = this.generateMsgUrl(this.mobileNum, message);
            if (StringUtils.isBlank(requestUrl)) {
                return;
            }
            String responseStr = HttpRequestUtil.httpRequestByRaw(requestUrl, "");
            SmsResponseXmlPO xmlPO = null;
            if (StringUtils.isNotBlank(responseStr)) {
                xmlPO = (SmsResponseXmlPO) XStreamUtils.fromXml(responseStr, SmsResponseXmlPO.class);
                System.out.println(xmlPO.getReturnstatus());
            }
            if (null != xmlPO && "Success".equals(xmlPO.getReturnstatus())) {
                rcd.setSendState(1);
                JsfUtil.addSuccessMessage("发送成功！");
                //页面倒计时
                RequestContext.getCurrentInstance().execute("setCountDownTime();");
            } else {
                rcd.setSendState(0);
                if (StringUtils.isNotBlank(responseStr)) {
                    rcd.setErrMsg(responseStr.length() > 1000 ? responseStr.substring(0, 1000) : responseStr);
                } else {
                    rcd.setErrMsg("短信发送失败！");
                }
                changeVerImg();
                JsfUtil.addErrorMessage("短信发送失败，请联系管理员！");
            }
            System.out.println(message);
        } catch (Exception e) {
            e.printStackTrace();
            changeVerImg();
            JsfUtil.addErrorMessage("短信发送失败，请联系管理员！");
            rcd.setSendState(0);
            rcd.setErrMsg("短信发送失败！");
            if (StringUtils.isNotBlank(e.getMessage())) {
                rcd.setErrMsg(e.getMessage().length() > 1000 ? e.getMessage().substring(0, 1000) : e.getMessage());
            }
        }
        //新增短信日志
        this.loginRcdService.saveTsUserLoginRcd(rcd);
    }

    /**
     * <p>方法描述： 设置新密码 </p>
     * @MethodAuthor： pw 2023/4/18
     **/
    public void changePwdAction() {
        this.validatePwd = null;
        this.validatePwd2 = null;
        String check = JsfUtil.getRequestParameterMap().get("check");
        if (StringUtils.isBlank(this.encryptPassword1)) {
            this.validatePwd = 0;
        }else if("1".equals(check) || "3".equals(check)){
            this.validatePwd = 1;
        }
        if (StringUtils.isBlank(this.encryptPassword2)) {
            this.validatePwd2 = 0;
        }else if("2".equals(check) || "3".equals(check)){
            this.validatePwd2 = 1;
        }

        //根据用户信息修改密码
        try {
            //非法访问 跳转登录页
            if(StringUtils.isBlank(this.userNo)){
                FacesContext.getCurrentInstance().getExternalContext()
                        .redirect("/login.faces");
                return;
            }
            if(null != this.validatePwd || null != this.validatePwd2){
                return;
            }
            if ("0".equals(check) || !encryptPassword1.equals(encryptPassword2)) {
                this.validatePwd2 = 2;
                return;
            }
            TsUserInfo userInfo = systemService.findUserByUserNo(this.userNo);
            //未找到用户信息 跳转登录页
            if(null == userInfo){
                FacesContext.getCurrentInstance().getExternalContext()
                        .redirect("/login.faces");
                return;
            }
            //更新上传标记 密码 以及初始密码是否修改
            userInfo.setUploadTag(0);
            userInfo.setPassword(this.encryptPassword1.toUpperCase());
            userInfo.setIfModpsw((short) 1);
            this.systemService.update(userInfo);
            HttpServletRequest req = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
            //session中数据移除
            String uuid = null == req.getSession().getAttribute(this.pwdSetUuid) ? null :
                    req.getSession().getAttribute(this.pwdSetUuid).toString();
            if(StringUtils.isNotBlank(uuid)){
                req.getSession().removeAttribute(uuid);
                req.getSession().removeAttribute(this.pwdSetUuid);
            }
            //修改成功
            RequestContext.getCurrentInstance().execute("showPwdSetSuccess();");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String generateMsgUrl(String mobilePhone, String content){
        StringBuffer urlBuffer = new StringBuffer();
        String timeStr = DateUtils.formatDate(new Date(),"yyyyMMddHHmmss");
        String sign = this.generateMsgSign(timeStr);
        if(StringUtils.isBlank(mobilePhone) || StringUtils.isBlank(content) ||
                StringUtils.isBlank(this.sendSmsUrl) || StringUtils.isBlank(this.smsUserId) ||
                StringUtils.isBlank(sign)){
            return null;
        }
        urlBuffer.append(this.sendSmsUrl)
                .append("?action=send&userid=").append(this.smsUserId)
                .append("&timestamp=").append(timeStr)
                .append("&sign=").append(sign)
                .append("&mobile=").append(mobilePhone)
                .append("&content=").append(content)
                .append("&sendTime=&extno=");
        return urlBuffer.toString();
    }
    private String generateMsgSign(String timeStr){
        String sign = new MD5Util().getMD5ofStr(this.smsUserName+this.smsPassWord+timeStr);
        return StringUtils.isBlank(sign) ? null : sign.toLowerCase();
    }

    /**
     * <p>方法描述： 跳转到设置新密码页面 </p>
     * @MethodAuthor： pw 2023/4/18
     **/
    private void redirectExeUpdatePwd() throws IOException {
        String uuid = StringUtils.uuid();
        HttpServletRequest req = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        this.userNo = null == this.user ? "" : this.user.getUserNo();
        //信息放入session中
        req.getSession().setAttribute(this.pwdSetUuid, uuid);
        req.getSession().setAttribute(uuid, this.userNo);
        FacesContext.getCurrentInstance().getExternalContext()
                .redirect("/newPwdSet.faces");
    }
    /*****************************************************/
    public boolean isIfIdcEmtpy() {
        return ifIdcEmtpy;
    }

    public void setIfIdcEmtpy(boolean ifIdcEmtpy) {
        this.ifIdcEmtpy = ifIdcEmtpy;
    }

    public boolean isIfIdcError() {
        return ifIdcError;
    }

    public void setIfIdcError(boolean ifIdcError) {
        this.ifIdcError = ifIdcError;
    }

    public boolean isIfIdcExsit() {
        return ifIdcExsit;
    }

    public void setIfIdcExsit(boolean ifIdcExsit) {
        this.ifIdcExsit = ifIdcExsit;
    }

    public boolean isIfIdcStop() {
        return ifIdcStop;
    }

    public void setIfIdcStop(boolean ifIdcStop) {
        this.ifIdcStop = ifIdcStop;
    }

    public String getIdc() {
        return idc;
    }

    public void setIdc(String idc) {
        this.idc = idc;
    }

    public boolean isIfMobileEmtpy() {
        return ifMobileEmtpy;
    }

    public void setIfMobileEmtpy(boolean ifMobileEmtpy) {
        this.ifMobileEmtpy = ifMobileEmtpy;
    }

    public boolean isIfMobileError() {
        return ifMobileError;
    }

    public void setIfMobileError(boolean ifMobileError) {
        this.ifMobileError = ifMobileError;
    }

    public String getMobileNum() {
        return mobileNum;
    }

    public void setMobileNum(String mobileNum) {
        this.mobileNum = mobileNum;
    }

    public boolean isIfMobileMatch() {
        return ifMobileMatch;
    }

    public void setIfMobileMatch(boolean ifMobileMatch) {
        this.ifMobileMatch = ifMobileMatch;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    public String getSmsVerifyCode() {
        return smsVerifyCode;
    }

    public void setSmsVerifyCode(String smsVerifyCode) {
        this.smsVerifyCode = smsVerifyCode;
    }

    public boolean isIfSmsOnline() {
        return ifSmsOnline;
    }

    public void setIfSmsOnline(boolean ifSmsOnline) {
        this.ifSmsOnline = ifSmsOnline;
    }

    public String getSendSmsTimes() {
        return sendSmsTimes;
    }

    public void setSendSmsTimes(String sendSmsTimes) {
        this.sendSmsTimes = sendSmsTimes;
    }

    public String getSmsValidMin() {
        return smsValidMin;
    }

    public void setSmsValidMin(String smsValidMin) {
        this.smsValidMin = smsValidMin;
    }

    public String getSmsTemplate() {
        return smsTemplate;
    }

    public void setSmsTemplate(String smsTemplate) {
        this.smsTemplate = smsTemplate;
    }

    public String getSendSmsUrl() {
        return sendSmsUrl;
    }

    public void setSendSmsUrl(String sendSmsUrl) {
        this.sendSmsUrl = sendSmsUrl;
    }

    public String getSmsUserId() {
        return smsUserId;
    }

    public void setSmsUserId(String smsUserId) {
        this.smsUserId = smsUserId;
    }

    public String getSmsUserName() {
        return smsUserName;
    }

    public void setSmsUserName(String smsUserName) {
        this.smsUserName = smsUserName;
    }

    public String getSmsPassWord() {
        return smsPassWord;
    }

    public void setSmsPassWord(String smsPassWord) {
        this.smsPassWord = smsPassWord;
    }

    public TsUserInfo getUser() {
        return user;
    }

    public void setUser(TsUserInfo user) {
        this.user = user;
    }

    public boolean isIfVerifyError() {
        return ifVerifyError;
    }

    public void setIfVerifyError(boolean ifVerifyError) {
        this.ifVerifyError = ifVerifyError;
    }

    public boolean isIfSmsErrror() {
        return ifSmsErrror;
    }

    public void setIfSmsErrror(boolean ifSmsErrror) {
        this.ifSmsErrror = ifSmsErrror;
    }

    public boolean isIfVerifyMatch() {
        return ifVerifyMatch;
    }

    public void setIfVerifyMatch(boolean ifVerifyMatch) {
        this.ifVerifyMatch = ifVerifyMatch;
    }

    public String getEncryIdc() {
        return encryIdc;
    }

    public void setEncryIdc(String encryIdc) {
        this.encryIdc = encryIdc;
    }

    public String getEncryMobileNum() {
        return encryMobileNum;
    }

    public void setEncryMobileNum(String encryMobileNum) {
        this.encryMobileNum = encryMobileNum;
    }

    public String getEncryptKey() {
        return encryptKey;
    }

    public void setEncryptKey(String encryptKey) {
        this.encryptKey = encryptKey;
    }

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public String getEncryptPassword1() {
        return encryptPassword1;
    }

    public void setEncryptPassword1(String encryptPassword1) {
        this.encryptPassword1 = encryptPassword1;
    }

    public String getEncryptPassword2() {
        return encryptPassword2;
    }

    public void setEncryptPassword2(String encryptPassword2) {
        this.encryptPassword2 = encryptPassword2;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPassword2() {
        return password2;
    }

    public void setPassword2(String password2) {
        this.password2 = password2;
    }

    public Integer getValidatePwd() {
        return validatePwd;
    }

    public void setValidatePwd(Integer validatePwd) {
        this.validatePwd = validatePwd;
    }

    public Integer getValidatePwd2() {
        return validatePwd2;
    }

    public void setValidatePwd2(Integer validatePwd2) {
        this.validatePwd2 = validatePwd2;
    }

    public String getPwdSetUuid() {
        return pwdSetUuid;
    }

    public void setPwdSetUuid(String pwdSetUuid) {
        this.pwdSetUuid = pwdSetUuid;
    }
}