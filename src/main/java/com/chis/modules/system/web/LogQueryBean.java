package com.chis.modules.system.web;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.service.LogImpl;

@ManagedBean
@ViewScoped
public class LogQueryBean extends FacesSimpleBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8814041318248638582L;

	private Integer rid;
	private String logDetail;
	private String searchUserNo;
	private String searchClientIP;
    private String searchState[];
	private LogImpl logService = SpringContextHolder.getBean(LogImpl.class);

	public LogQueryBean() {
		this.ifSQL = true;
		this.searchAction();
	}

	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder();
		sb.append(" FROM TS_SYSTEM_LOG T ");
		sb.append(" LEFT JOIN TS_MENU B ON T.MENU_ID = TO_CHAR(B.RID) ");
		sb.append(" WHERE 1 = 1 ");
        if (StringUtils.isNotBlank(this.searchUserNo)) {
            sb.append(" AND USER_NO = :userNo ");
            this.paramMap.put("userNo", new StringBuilder(StringUtils.convertBFH(this.searchUserNo.trim())).toString());
        }
        if (StringUtils.isNotBlank(this.searchClientIP)) {
            sb.append(" AND CLIENT_IP = :clientIp ");
            this.paramMap.put("clientIp", new StringBuilder(StringUtils.convertBFH(this.searchClientIP.trim())).toString());
        }
        if (null != this.searchState && 0 != this.searchState.length) {
            StringBuilder stateSb = new StringBuilder();
            for(String s : searchState) {
                stateSb.append(",").append("'").append(s).append("'");
            }
            //String stateStr = StringUtils.array2string(this.searchState, ",");
            sb.append(" AND T.ERROR_NO IN (").append(stateSb.substring(1)).append(") ");
        }
		sb.append(" ORDER BY T.HAPPEN_DATE DESC");
		StringBuilder searchSql = new StringBuilder();
		searchSql.append("SELECT T.RID,T.HAPPEN_DATE,T.USER_NO,T.ERROR_NO,T.HINT_INFO,T.CLIENT_IP,B.MENU_CN")
				.append(sb.toString());
		StringBuilder countSql = new StringBuilder();
		countSql.append("SELECT COUNT(T.RID)").append(sb.toString());
		return new String[] { searchSql.toString(), countSql.toString() };
	}

	public void detailAction() {
		this.logDetail = logService.queryDetailByRid(rid);
	}

	public String getLogDetail() {
		return logDetail;
	}

	public void setLogDetail(String logDetail) {
		this.logDetail = logDetail;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public String getSearchUserNo() {
		return searchUserNo;
	}

	public void setSearchUserNo(String searchUserNo) {
		this.searchUserNo = searchUserNo;
	}

	public String getSearchClientIP() {
		return searchClientIP;
	}

	public void setSearchClientIP(String searchClientIP) {
		this.searchClientIP = searchClientIP;
	}

    public String[] getSearchState() {
        return searchState;
    }

    public void setSearchState(String[] searchState) {
        this.searchState = searchState;
    }
}
