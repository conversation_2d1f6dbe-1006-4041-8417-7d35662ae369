package com.chis.modules.system.web;

import java.io.Serializable;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;

import com.chis.common.utils.JsfUtil;
import com.chis.modules.system.logic.Constants;

/**
 * 托管Bean最上层的基类
 * <AUTHOR>
 */
public abstract class FacesBean implements Serializable{
	
	protected int pageSize = Constants.PAGE_SIZE; 
	protected Logger logger = Logger.getLogger(getClass());
	private String perPageSize = "20,50,100";
	
    /**
     * 获取受管Bean
     * @param beanName 受管Bean名称
     * @return 受管Bean对象
     */
    protected Object getBean(String beanName) {
        return JsfUtil.getApplication().getVariableResolver().resolveVariable(JsfUtil.getFacesContext(), beanName);
    }

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
	/**
	 * 获取response对象
	 * @return response对象
	 */
	protected HttpServletResponse getResponse() {
		return (HttpServletResponse) FacesContext.getCurrentInstance().getExternalContext().getResponse();
	}

	public String getPerPageSize() {
		return perPageSize;
	}

	public void setPerPageSize(String perPageSize) {
		this.perPageSize = perPageSize;
	}
}
