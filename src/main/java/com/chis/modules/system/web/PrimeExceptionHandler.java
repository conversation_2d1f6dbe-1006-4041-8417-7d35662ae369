package com.chis.modules.system.web;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.el.ELException;
import javax.faces.FacesException;
import javax.faces.application.ViewExpiredException;
import javax.faces.application.ViewHandler;
import javax.faces.component.UIComponent;
import javax.faces.component.UIViewRoot;
import javax.faces.component.visit.VisitContext;
import javax.faces.context.ExceptionHandler;
import javax.faces.context.ExceptionHandlerWrapper;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.context.PartialResponseWriter;
import javax.faces.event.ExceptionQueuedEvent;
import javax.faces.event.PhaseId;
import javax.faces.view.ViewDeclarationLanguage;
import javax.servlet.http.HttpServletRequest;

import com.chis.modules.system.enumn.LogNoEnum;
import org.primefaces.expression.SearchExpressionFacade;
import org.primefaces.util.ComponentUtils;

import com.chis.common.utils.Exceptions;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSystemLog;
import com.chis.modules.system.logic.ExceptionInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.utils.LogUtil;

public class PrimeExceptionHandler extends ExceptionHandlerWrapper {

    private static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";

    private static final String ERROR_PAGE_ERROR =
            "<?xml version='1.0' encoding='UTF-8'?><partial-response id='error'><changes><update id='javax.faces.ViewRoot'>"
                    + "<![CDATA[<html lang='en'><head><script type='text/javascript'>window.open ('/login.faces','_top');</script>"
                    + "</head><body></body></html>]]></update></changes></partial-response>";

    private final ExceptionHandler wrapped;
    
    public PrimeExceptionHandler(ExceptionHandler wrapped) {
        this.wrapped = wrapped;
    }
    
    @Override
    public ExceptionHandler getWrapped() {
        return wrapped;
    }
    
    @Override
    public void handle() throws FacesException {
        FacesContext context = FacesContext.getCurrentInstance();

        if(null != context.getViewRoot()) {
            String viewId= context.getViewRoot().getViewId();
            if(StringUtils.isNotBlank(viewId)) {
            	System.err.println("viewId:" + viewId);
                if(!"/registerSuccessFs.xhtml".equals(viewId) &&!"/registerFs.xhtml".equals(viewId) &&!"/registerSuccess.xhtml".equals(viewId) &&!"/loginPsn.xhtml".equals(viewId) &&!"/register.xhtml".equals(viewId) &&!"/logout.xhtml".equals(viewId) && !"/login.xhtml".equals(viewId) 
                		&& !"/browser.xhtml".equals(viewId) && !"/head.xhtml".equals(viewId)&&!"/registerWh.xhtml".equals(viewId)&&!"/registerWhSuccess.xhtml".equals(viewId)
                        &&!"/retrievePassword.xhtml".equals(viewId) && !"/newPwdSet.xhtml".equals(viewId) && !"/webapp/heth/analy/error.xhtml".equals(viewId)&& !"/ssoAuth.xhtml".equals(viewId) ) {
                    if(!this.hasSession()) {
                    	System.out.println("没有session?" + !this.hasSession() + "," + viewId);
                        try {
                            this.handleRedirect(context, new ViewExpiredException(), null);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                        return;
                    }
                }
            }
        }

        if (context.getResponseComplete()) {
            return;
        }


        
        Iterable<ExceptionQueuedEvent> exceptionQueuedEvents = getUnhandledExceptionQueuedEvents();
        if (exceptionQueuedEvents != null && exceptionQueuedEvents.iterator() != null) {
            Iterator<ExceptionQueuedEvent> unhandledExceptionQueuedEvents = getUnhandledExceptionQueuedEvents().iterator();

            if (unhandledExceptionQueuedEvents.hasNext()) {
                try {
                    Throwable throwable = unhandledExceptionQueuedEvents.next().getContext().getException();
                    unhandledExceptionQueuedEvents.remove();

                    Throwable rootCause = getRootCause(throwable);
                    ExceptionInfo info = createExceptionInfo(rootCause);

                    // always log the exception
                    HttpServletRequest request = (HttpServletRequest) context.getExternalContext().getRequest();
                    SessionData sessionData  = (SessionData)request.getSession().getAttribute(SessionData.SESSION_DATA);
                    TsSystemLog tsSystemLog = new TsSystemLog();
                    tsSystemLog.setUserNo(sessionData.getUser().getUserNo());
                    tsSystemLog.setClientIp(request.getRemoteAddr());
                    if(StringUtils.isNotBlank(rootCause.getMessage())) {
                    	if(rootCause.getMessage().length() > 100) {
                    		tsSystemLog.setHintInfo(rootCause.getMessage().substring(0,99));
                    	}else {
                    		tsSystemLog.setHintInfo(rootCause.getMessage());
                    	}
                    } else {
                        if(StringUtils.isNotBlank(rootCause.toString())) {
                            if(rootCause.toString().length() > 100) {
                                tsSystemLog.setHintInfo(rootCause.toString().substring(0,99));
                            }else {
                                tsSystemLog.setHintInfo(rootCause.toString());
                            }
                        }
                    }
                    tsSystemLog.setDetailInfo(Exceptions.exception(rootCause));
                    tsSystemLog.setHappenDate(new Date());
                    if(StringUtils.isNotBlank(sessionData.getMenuId())) {
                        tsSystemLog.setMenuId(sessionData.getMenuId());
                    }
                    //System.out.println(rootCause.toString());
                    tsSystemLog.setErrorNo(LogNoEnum.NO_3001.getTypeNo());
                    LogUtil.logIntoDB(tsSystemLog);
                    //LogUtil.logIntoDB(rootCause);
                    
                    if (context.getPartialViewContext().isAjaxRequest()) {
                        handleAjaxException(context, rootCause, info);
                    } else {
                        handleRedirect(context, rootCause, info);
                    }
                } catch (Exception ex) {
                    ExternalContext externalContext = context.getExternalContext();
                    if (!externalContext.isResponseCommitted()) {
                        externalContext.responseReset();

                        externalContext.setResponseContentType("text/xml");
                        try {
                            externalContext.getResponseOutputWriter().write(ERROR_PAGE_ERROR);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                        context.responseComplete();
                    } else {
                        // Well, it's too late to handle. Just let it go.
                        throw new FacesException(ex);
                    }
                }
            }

            while (unhandledExceptionQueuedEvents.hasNext()) {
                unhandledExceptionQueuedEvents.next();
                unhandledExceptionQueuedEvents.remove();
            }
        }
    }

    @Override
    public Throwable getRootCause(Throwable throwable) {
        while ((ELException.class.isInstance(throwable) || FacesException.class.isInstance(throwable)) && throwable.getCause() != null) {
            throwable = throwable.getCause();
        }
        return throwable;
    }
    
    protected void handleAjaxException(FacesContext context, Throwable rootCause, ExceptionInfo info) throws Exception {
        ExternalContext externalContext = context.getExternalContext();
        
        if (context.getCurrentPhaseId().equals(PhaseId.RENDER_RESPONSE)) {
            if (!externalContext.isResponseCommitted()) {
                String characterEncoding = externalContext.getResponseCharacterEncoding();
                externalContext.responseReset();
                externalContext.setResponseCharacterEncoding(characterEncoding);
            }
        }

        rootCause = buildView(context, rootCause, rootCause);

        AjaxExceptionHandler handlerComponent = findHandlerComponent(context, rootCause);

        // redirect if no UIAjaxExceptionHandler available
        if (handlerComponent == null) {
            handleRedirect(context, rootCause, info);
        }
        // handle custom update / onexception callback
        else {
            context.getAttributes().put(ExceptionInfo.ATTRIBUTE_NAME, info);
            
            externalContext.addResponseHeader("Content-Type", "text/xml; charset=" + externalContext.getResponseCharacterEncoding());
            externalContext.addResponseHeader("Cache-Control", "no-cache");
            externalContext.setResponseContentType("text/xml");

            PartialResponseWriter writer = context.getPartialViewContext().getPartialResponseWriter();

            writer.startDocument();
            writer.startElement("changes", null);
            
            if (!ComponentUtils.isValueBlank(handlerComponent.getUpdate())) {
                List<UIComponent> updates = SearchExpressionFacade.resolveComponents(context, handlerComponent, handlerComponent.getUpdate());
                
                if (updates != null && updates.size() > 0) {
                    context.setResponseWriter(writer);

                    for (int i = 0; i < updates.size(); i++) {
                        UIComponent component = updates.get(i);
                        
                        writer.startElement("update", null);
                        writer.writeAttribute("id", component.getClientId(context), null);
                        writer.startCDATA();

                        component.encodeAll(context);

                        writer.endCDATA();
                        writer.endElement("update");
                    }
                }
            }

            if (!ComponentUtils.isValueBlank(handlerComponent.getOnexception())) {
                writer.startElement("eval", null);
                writer.startCDATA();

                writer.write("var hf=function(type,message,timestampp){");
                writer.write(handlerComponent.getOnexception());
                writer.write("};hf.call(this,'" + info.getType() + "','" + info.getMessage() + "','" + info.getFormattedTimestamp() + "');");
                
                writer.endCDATA();
                writer.endElement("eval");
            }

            writer.endElement("changes");
            writer.endDocument();

            context.responseComplete();
        }   
    }

    protected ExceptionInfo createExceptionInfo(Throwable rootCause) throws IOException {
        ExceptionInfo info = new ExceptionInfo();
        info.setException(rootCause);
        info.setMessage(rootCause.getMessage());
        info.setStackTrace(rootCause.getStackTrace());
        info.setTimestamp(new Date());
        info.setType(rootCause.getClass().getName());

        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        rootCause.printStackTrace(pw);
        info.setFormattedStackTrace(sw.toString().replaceAll("(\r\n|\n)", "<br/>"));
        pw.close();
        sw.close();

        SimpleDateFormat format = new SimpleDateFormat(DATE_FORMAT_PATTERN);
        info.setFormattedTimestamp(format.format(info.getTimestamp()));

        return info;
    }
    
    protected AjaxExceptionHandler findHandlerComponent(FacesContext context, Throwable throwable) {
        AjaxExceptionHandlerVisitCallback visitCallback = new AjaxExceptionHandlerVisitCallback(throwable);
        
        context.getViewRoot().visitTree(VisitContext.createVisitContext(context), visitCallback);
        
        return visitCallback.getHandler();
    }
    
    protected Throwable buildView(FacesContext context, Throwable throwable, Throwable rootCause) throws IOException
    {
        if (context.getViewRoot() == null) {
            ViewHandler viewHandler = context.getApplication().getViewHandler();

            String viewId = viewHandler.deriveViewId(context, calculateViewId(context));
            ViewDeclarationLanguage vdl = viewHandler.getViewDeclarationLanguage(context, viewId);
            UIViewRoot viewRoot = vdl.createView(context, viewId);
            context.setViewRoot(viewRoot);

            vdl.buildView(context, viewRoot);

            if (rootCause == null && throwable instanceof IllegalArgumentException) {
                rootCause = new javax.faces.application.ViewExpiredException(viewId);
            }
        }
        
        return rootCause;
    }

    protected String calculateViewId(FacesContext context) {
        Map<String, Object> requestMap = context.getExternalContext().getRequestMap();
        String viewId = (String) requestMap.get("javax.servlet.include.path_info");

        if (viewId == null) {
            viewId = context.getExternalContext().getRequestPathInfo();
        }

        if (viewId == null) {
            viewId = (String) requestMap.get("javax.servlet.include.servlet_path");
        }

        if (viewId == null) {
            viewId = context.getExternalContext().getRequestServletPath();
        }

        return viewId;
    }

    protected void handleRedirect(FacesContext context, Throwable rootCause, ExceptionInfo info) throws IOException {
        String errorPage = null;
        if(rootCause instanceof ViewExpiredException) {
        	System.out.println("【ViewExpiredException】" + Exceptions.exception(rootCause));
        	rootCause.printStackTrace();
        	System.out.println("=============================================");
            errorPage = "/logout.faces";
        }else {
            errorPage = "/error.faces?msg=" + Exceptions.exception(rootCause);
        }
        context.getExternalContext().redirect(context.getExternalContext().getRequestContextPath() + errorPage);
        context.responseComplete();
    }

    /**
     * 检查是否登录，即session中是否有SessionData对象
     * @return 是否session有用户信息
     */
    private boolean hasSession() {
        SessionData sessionData = (SessionData) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get(SessionData.SESSION_DATA);
        if(null != sessionData) {
            return Boolean.TRUE;
        }else {
            return Boolean.FALSE;
        }
    }
}