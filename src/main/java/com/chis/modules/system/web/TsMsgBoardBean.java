package com.chis.modules.system.web;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.entity.TsMsgBoard;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * <p>
 * 类描述：留言板
 * </p>
 * 
 * @ClassAuthor maox,2018年6月4日,tsMsgBoardBean
 * <AUTHOR>
 * 
 */
@ManagedBean(name = "tsMsgBoardBean")
@ViewScoped
public class TsMsgBoardBean extends FacesEditBean {

	private static final long serialVersionUID = 1L;
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder
			.getBean(CommServiceImpl.class);
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);

	/** 查找开始日期 **/
	private Date searchBeginDate;
	/** 查找结束日期 **/
	private Date searchEndDate;
	/** 留言者名称 **/
	private String searchUserName;
	/** 查询条件：地区名称 */
	private String searchZoneName;
	/** 查询条件：地区编码 */
	private String searchZoneCode;
	/** 查询条件：地区级别 */
	private String searchZoneType;
	/** 查询条件：单位 */
	private String searchUnitId;
	/** 查询条件：单位 **/
	private Map<String, String> searchUnitMap = new HashMap<String, String>(0);
	/** 能看到的地区集合 */
	private List<TsZone> zoneList;

	private String editLinkMan;

	private String editLinkTel;

	private String editFeedback;

	/** 登录用户 **/
	TsUserInfo user = this.sessionData.getUser();

	TsMsgBoard tsMsgBoard = new TsMsgBoard();

	public TsMsgBoardBean() {
		init();
	}

	/**
	 * <p>
	 * 方法描述：页面初始化
	 * </p>
	 * 
	 * @throws ParseException
	 * 
	 * @MethodAuthor maox,2018年6月4日,init
	 */
	public void init() {
		tsMsgBoard = new TsMsgBoard();
		this.ifSQL = Boolean.TRUE;
		boolean ifAdmin = true;
		if(!user.getUserNo().equals(Constants.ADMIN)) {
            ifAdmin = false;
        }
		try {
			Date dt = new Date();
			SimpleDateFormat matter1 = new SimpleDateFormat("yyyy-MM-dd");

			editLinkMan = user.getUsername();
			editLinkTel = user.getMbNum();
			String year = matter1.format(dt).toString().substring(0, 4);
			String beginDate = year + "-01-01  00:00:00";
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			searchBeginDate = df.parse(beginDate);
			searchEndDate = new Date();
			if (null != user.getTsUnit()) {
				this.searchZoneCode = user.getTsUnit().getTsZone()
						.getZoneCode();
				this.searchZoneName = user.getTsUnit().getTsZone()
						.getZoneName();
				this.searchZoneType = user.getTsUnit().getTsZone()
						.getZoneType().toString();
			}
			/*** 地区初始化 */
			if (null == this.zoneList || this.zoneList.size() <= 0) {
				this.zoneList = this.commService.findZoneListNew(ifAdmin,
						sessionData.getUser().getTsUnit().getTsZone()
								.getZoneCode(), null, "6");
			}

			/** 初始化单位 */
			this.searchUnitMap = this.filterUnit(this.searchZoneCode, this.searchZoneType);
			this.searchAction();
		} catch (Exception ex) {
			JsfUtil.addErrorMessage(ex.toString());
		}

	}
	
	@Override
    public void searchAction() {
        if(DateUtils.isDateAfter(searchBeginDate,searchEndDate)){
            JsfUtil.addErrorMessage("留言开始时间的开始日期应小于等于结束日期");
            return;
        }
        super.searchAction();
    }
	/**
	 * 验证电话号码
	 * 
	 * @param mobile
	 * @return
	 */
	public boolean verifyMobile(String mobile) {
		boolean flag = true;
		if (StringUtils.isNotBlank(mobile)
				&& !Pattern.matches(Constants.PHONE, mobile)
				&& !Pattern.matches(Constants.MOBILE_REGEX, mobile)) {
			flag = false;
		}
		return flag;
	}

	/**
	 * 根据地区刷单位
	 * 
	 * @param zoneCode地区编码
	 * @param zoneType地区级别
	 * @return 单位集合
	 */
	private Map<String, String> filterUnit(String zoneCode, String zoneType) {
		TsUserInfo user = this.sessionData.getUser();
		Map<String, String> map = new LinkedHashMap<String, String>();
		List<TsUnit> list = this.systemModuleService.findUnitByZoneIdNew(true,
				user.getTsUnit().getTsZone().getZoneCode(), user.getTsUnit()
						.getRid(), zoneCode, zoneType);
		if (null != list && list.size() > 0) {
			for (TsUnit t : list) {
				map.put(t.getUnitname(), t.getRid().toString());
			}
		}
		return map;
	}

	/**
	 * 查询条件，地区树选择事件
	 */
	public void onSearchNodeSelect() {
		this.searchUnitId = null;
		this.searchUnitMap = this.filterUnit(this.searchZoneCode,
				this.searchZoneType);
	}

	@Override
	public void addInit() {
		// TODO Auto-generated method stub

	}

	@Override
	public void viewInit() {
		// TODO Auto-generated method stub

	}

	@Override
	public void modInit() {
		// TODO Auto-generated method stub

	}

	@Override
	public void saveAction() {
		/** 联系人名称 **/
		if ("" == editLinkMan) {
			JsfUtil.addErrorMessage("联系人不允许为空！");
			return;
		}
		/** 联系人电话 **/
		if ("" == editLinkTel) {
			JsfUtil.addErrorMessage("联系人电话不允许为空！");
			return;
		}
		/** 联系人名称 **/
		if (!verifyMobile(editLinkTel)) {
			JsfUtil.addErrorMessage("联系人电话格式不正确！");
			return;
		}
		/** 留言内容 **/
		if ("" == editFeedback) {
			JsfUtil.addErrorMessage("留言内容不允许为空！");
			return;
		}

		tsMsgBoard.setLinkMan(editLinkMan);
		tsMsgBoard.setLinkTel(editLinkTel);
		tsMsgBoard.setFeedbackMsg(editFeedback);
		tsMsgBoard.setCreateManID(user.getRid());
		tsMsgBoard.setCreateDate(new Date());
		tsMsgBoard.setMsgDate(new Date());
		tsMsgBoard.setTsUserInfo(user);

		this.systemModuleService.saveFeedback(tsMsgBoard);
		JsfUtil.addSuccessMessage("保存成功！");
	}

	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder();
		StringBuilder searchSql = new StringBuilder();
		StringBuilder countSql = new StringBuilder();

		sb.append(" FROM TS_MSG_BOARD A ");
		sb.append(" LEFT JOIN TS_USER_INFO B ON A.SUBMIT_PSN = B.RID ");
		sb.append(" LEFT JOIN TS_UNIT C ON B.UNIT_RID = C.RID ");
		sb.append(" LEFT JOIN TS_ZONE D ON C.ZONE_ID = D.RID ");
		sb.append(" WHERE 1 = 1 ");		
		
		searchSql.append("SELECT D.ZONE_NAME,C.UNITNAME,A.LINK_MAN,A.LINK_TEL ");
		searchSql.append(",A.FEEDBACK_MSG,A.MSG_DATE,B.USERNAME ");

		/** 地区 **/
		if (!StringUtils.isBlank(searchZoneCode)) {
			sb.append(" AND D.ZONE_CODE like '")
					.append(ZoneUtil.zoneSelect(this.searchZoneCode))
					.append("%' ");
		}

		/** 单位 **/
		if (!StringUtils.isBlank(searchUnitId)) {
			sb.append(" AND C.RID = ").append(this.searchUnitId);
		}
		/** 开始日期 **/
		if (null != searchBeginDate) {
			sb.append(" AND A.MSG_DATE >= to_date('")
					.append(DateUtils.formatDate(searchBeginDate,"yyyy-MM-dd HH:mm:ss"))
					.append("','YYYY-MM-DD hh24:mi:ss') ");
		}
		/** 结束日期 **/
		try {
			if (null != searchEndDate) {
				SimpleDateFormat formatter = new SimpleDateFormat(
						"yyyy-MM-dd HH:mm:ss");
				String dateString = formatter.format(searchEndDate);
				dateString = dateString.replace("00:00:00", "23:59:59");
				DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				searchEndDate = df.parse(dateString);

				sb.append(" AND A.MSG_DATE <=to_date('")
				.append(DateUtils.formatDate(searchEndDate,"yyyy-MM-dd HH:mm:ss"))
				.append("','YYYY-MM-DD hh24:mi:ss') ");
			}
		} catch (Exception ex) {
			ex.fillInStackTrace();
		}
		if (null != searchUserName) {
			sb.append(" AND B.USERNAME LIKE :searchUserName ");
			this.paramMap.put("searchUserName", "%"+StringUtils.convertBFH(searchUserName)+"%");
		}
		
		searchSql.append(sb).append(" ORDER BY D.ZONE_GB,C.UNITNAME ,A.MSG_DATE DESC,B.USERNAME ");
		countSql.append(" SELECT COUNT(*) ").append(sb);

		return new String[] { searchSql.toString(), countSql.toString() };
	}

	public Date getSearchEndDate() {
		return searchEndDate;
	}

	public void setSearchEndDate(Date searchEndDate) {
		this.searchEndDate = searchEndDate;
	}

	public Date getSearchBeginDate() {
		return searchBeginDate;
	}

	public void setSearchBeginDate(Date searchBeginDate) {
		this.searchBeginDate = searchBeginDate;
	}

	public String getSearchUserName() {
		return searchUserName;
	}

	public void setSearchUserName(String searchUserName) {
		this.searchUserName = searchUserName;
	}

	public String getSearchZoneName() {
		return searchZoneName;
	}

	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}

	public String getSearchZoneCode() {
		return searchZoneCode;
	}

	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}

	public String getSearchZoneType() {
		return searchZoneType;
	}

	public void setSearchZoneType(String searchZoneType) {
		this.searchZoneType = searchZoneType;
	}

	public String getSearchUnitId() {
		return searchUnitId;
	}

	public void setSearchUnitId(String searchUnitId) {
		this.searchUnitId = searchUnitId;
	}

	public Map<String, String> getSearchUnitMap() {
		return searchUnitMap;
	}

	public void setSearchUnitMap(Map<String, String> searchUnitMap) {
		this.searchUnitMap = searchUnitMap;
	}

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public String getEditLinkMan() {
		return editLinkMan;
	}

	public void setEditLinkMan(String editLinkMan) {
		this.editLinkMan = editLinkMan;
	}

	public String getEditLinkTel() {
		return editLinkTel;
	}

	public void setEditLinkTel(String editLinkTel) {
		this.editLinkTel = editLinkTel;
	}

	public String getEditFeedback() {
		return editFeedback;
	}

	public void setEditFeedback(String editFeedback) {
		this.editFeedback = editFeedback;
	}

}
