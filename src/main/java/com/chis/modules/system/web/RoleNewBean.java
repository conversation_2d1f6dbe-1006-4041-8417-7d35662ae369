package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import com.chis.modules.system.service.CommServiceImpl;

import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.DualListModel;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsMenu;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsRole;
import com.chis.modules.system.entity.TsRoleMenu;
import com.chis.modules.system.entity.TsRolePower;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsUserRole;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.logic.UnitPO;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import org.springframework.util.CollectionUtils;

/**
 * 角色管理
 * <AUTHOR>
 */
@ManagedBean(name="roleNewBean")
@ViewScoped
public class RoleNewBean extends FacesSimpleBean implements IProcessData{

	private static final long serialVersionUID = -738445454536625063L;
	/**存在session中的对象*/
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/**ejb session bean*/
	private SystemModuleServiceImpl systemService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	/**角色对象*/
	private TsRole tsRole = new TsRole();
	/**角色对象rid*/
	private Integer rid;
    /**菜单授权的菜单树*/
    private TreeNode treeNode;
    /**菜单授权的已选择的树*/
    private TreeNode[] selectedNodes;
    /**选中的菜单*/
    private TreeNode selectedNode;
    /**查询条件：角色名称*/
    private String searchRoleName;
    /**用于机构授权的选择*/
    private DualListModel dualListModel = new DualListModel();
    /**用于缓存初始化的已选用户*/
    private List<TsUserInfo> storeTargetList;
    /**是否是超管*/
    private boolean ifAdmin = Boolean.TRUE;
    /**用户分配：地区名称*/
    private String userZoneName;
    /**用户分配：地区编码*/
    private String userZoneCode;
    /**用户分配：地区级别*/
    private String userZoneLevel;
    /**用户分配：单位id*/
    private Integer userUnitId;
    /**用户分配：单位下啦*/
    private Map<String, Integer> unitMap = new HashMap<String, Integer>(0);
    /**能看到的地区集合*/
    private List<TsZone> zoneList = new ArrayList<TsZone>(0);
    /**用于机构授权的选择*/
    private DualListModel userDualListModel = new DualListModel();
    /**某角色已经拥有的码表IDS*/
    private String selectedCodeIds;
    /**某角色已经拥有的码表数据IDS*/
    private String selectedSimpleCodeIds;
    /*++人员科室*/
    private Map<Integer,String> offoceMap;
    private Integer userUnitOfficeId;
    private Map<String,Integer> unitOfficeMap;
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    
    private Object adminss;
    private Integer type;
    /**角色类型*/
    List<TsSimpleCode> roleTypeList;
    /**角色类型选中rids*/
    private String roleTypeRids;

    @PostConstruct
	public void init() {
    	type = 1;
        TsUserInfo tsUserInfo = this.sessionData.getUser();
        if(!tsUserInfo.getUserNo().equals(Constants.ADMIN)) {
            ifAdmin = Boolean.FALSE;
        }
        initType();
        //获取URL中的rolename
        searchRoleName = JsfUtil.getRequest().getParameter("rolename");
        this.searchAction();
	}

    private void initType(){
    	roleTypeList = new ArrayList<>();
    	roleTypeList = commService.findSimpleCodesByTypeId("1201");
    }
	/**
	 * 添加初始化
	 */
	public void addInitAction() {
		this.tsRole = new TsRole();
		type = null;
		this.tsRole.setIfSuperManageRole(0);
		this.tsRole.setCreateDate(new Date());
		this.tsRole.setCreateManid(sessionData.getUser().getRid());

	}

	/**
	 * 修改初始化
	 */
	public void modInitAction() {
		this.tsRole = this.systemService.findRole(rid);
		List<TsRolePower> tsRolePowerList = tsRole.getTsRolePowerList();
		if(null != tsRolePowerList && tsRolePowerList.size() >0){
			type = tsRolePowerList.get(0).getFkByRoleTypeId().getRid();
		}
		this.tsRole.setModifyDate(new Date());
		this.tsRole.setModifyManid(this.sessionData.getUser().getRid());
	}

	/**
	 * 保存
	 */
	public void saveAction() {
		if(!very()){
			return;
		}
        if(null != this.tsRole.getRid()) {
        	this.systemService.delRolePower(this.tsRole.getRid());
    		tsRole.setTsRolePowerList(fzPower());
    		 this.systemService.saveOrUpdateRoleNew(this.tsRole);
        }else {
    		tsRole.setTsRolePowerList(fzPower());
    		this.systemService.saveOrUpdateRoleNew(this.tsRole);
            this.searchAction();
        }
        JsfUtil.addSuccessMessage("保存成功！");
        RequestContext.getCurrentInstance().execute("PF('RoleEditDialog').hide()");
	}
	
	private List<TsRolePower> fzPower(){
		TsRolePower power = new TsRolePower();
    	TsSimpleCode code=  systemService.findSimpleCodeByRid(type);
    	power.setFkByRoleId(tsRole);
    	power.setFkByRoleTypeId(code);
    	power.setCreateDate(new Date());
		power.setCreateManid(sessionData.getUser().getRid());
		List<TsRolePower> tsRolePowerList = new ArrayList<TsRolePower>();
		tsRolePowerList.add(power);
		return tsRolePowerList;
		
	}
	private boolean very(){
		boolean flag = true;
		if(StringUtils.isBlank(tsRole.getRoleName())){
			JsfUtil.addErrorMessage("请添加角色名称！");
			flag = false;
		}
		if(type == null){
			JsfUtil.addErrorMessage("请选择角色类型！");
			flag = false;
		}
		//判断角色编码是否有重复
        if(StringUtils.isNotBlank(this.tsRole.getRoleCode())){
            String roleCode = this.tsRole.getRoleCode().trim();
            this.tsRole.setRoleCode(roleCode);
            StringBuffer buffer = new StringBuffer();
            Map<String,Object> curParamMap = new HashMap<>();
            buffer.append(" SELECT COUNT(1) FROM TS_ROLE T WHERE T.ROLE_CODE = :roleCode ");
            curParamMap.put("roleCode", roleCode);
            if(null != this.tsRole.getRid()){
                buffer.append(" AND T.RID != :rid ");
                curParamMap.put("rid", this.tsRole.getRid());
            }
            if(this.commService.findCountBySql(buffer.toString(), curParamMap) > 0){
                JsfUtil.addErrorMessage("角色编码不允许重复！");
                flag = false;
            }
        }
		return flag;
	}

    /**
    * <p>Description：删除前验证 </p>
    * <p>Author： yzz 2024-01-29 </p>
    */
    public void beforDelAction(){
        if(this.rid==null){
            return;
        }
        TsRole role =this.systemService.findRoleNew(rid);
        if(!CollectionUtils.isEmpty(role.getTsUserRoleList())){
            JsfUtil.addErrorMessage("已有用户分配该角色，不允许删除！");
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show()");
    }

	/**
	 * 删除
	 */
	public void deleteAction() {
        try{
            this.systemService.deleteRoleNew(this.rid);
            this.searchAction();
            JsfUtil.addSuccessMessage("删除成功！");
        }catch (Exception e){
            JsfUtil.addErrorMessage("删除失败！");
        }

	}

    public void roleMenuEdit(){
        //转向
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("/webapp/system/roleMenuEdit.faces");
            sb.append("?ph=1&roleid=").append(rid);
            sb.append("&from=/webapp/system/roleListNew.faces?ph=1");
            if(StringUtils.isNotBlank(searchRoleName)){
                sb.append("@rolename=").append(searchRoleName);
            }
            FacesContext.getCurrentInstance().getExternalContext().redirect(sb.toString());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

	/**
	 * 分配菜单初始化:将自己能看到的菜单都查询出来，超管显示全部菜单
	 * 自己能看到的菜单:
	 * 		a.自己拥有的菜单
     * 	    b.自己拥有角色的菜单
     *      c.自己机构拥有角色的菜单
	 */
	public void fpMenuInitAction() {
        selectedNodes = null;
        TsUserInfo tsUserInfo = sessionData.getUser();
        //0000,全部菜单
        //非0000，获得自己拥有的菜单，自己角色拥有的菜单和自己所在单位的菜单
        List<TsMenu> allMenuList = this.systemService.findMenuList(this.ifAdmin, tsUserInfo.getRid(), tsUserInfo.getTsUnit().getRid());
        this.treeNode = new CheckboxTreeNode("root", null);

        if(null != allMenuList && allMenuList.size() > 0) {
            Set<String> firstLevelNoSet = new LinkedHashSet<String>();  //只有第一层
            Set<String> levelNoSet = new LinkedHashSet<String>();   //没有第一层
            Map<String, TsMenu> menuMap = new HashMap<String, TsMenu>();    //所有菜单
            //角色
            this.tsRole = this.systemService.findRoleWithRoleMenu(this.rid);
            //角色拥有的菜单
            List<TsRoleMenu> roleMenuList = this.tsRole.getTsRoleMenuList();
            List<TsMenu> selectList = new ArrayList<TsMenu>();
            if(null != roleMenuList && roleMenuList.size() > 0) {
                for(TsRoleMenu t: roleMenuList) {
                    selectList.add(t.getTsMenu());
                }
            }

            for(TsMenu t : allMenuList) {
                menuMap.put(t.getMenuLevelNo(), t);
                if(StringUtils.isNotBlank(t.getMenuLevelNo())) {
                    if(StringUtils.containsNone(t.getMenuLevelNo(), ".")) {
                        firstLevelNoSet.add(t.getMenuLevelNo());
                    }else {
                        levelNoSet.add(t.getMenuLevelNo());
                    }
                }
            }

            for(String ln: firstLevelNoSet) {
                TsMenu t = menuMap.get(ln);
                TreeNode node = new CheckboxTreeNode(menuMap.get(ln), this.treeNode);
                node.setSelected(selectAble(ln,selectList,allMenuList));
                this.addChildNode(ln, levelNoSet, menuMap, node,selectList,allMenuList);
            }
        }
	}



    private boolean selectAble(String code,List<TsMenu> slcList,List<TsMenu> allList){
        int count = 0,total = 0;
        for(TsMenu t : slcList){
            if(t.getMenuLevelNo().length() >= code.length() && t.getMenuLevelNo().substring(0,code.length()).equals(code)){
                count++;
            }
        }
        for(TsMenu t : allList){
            if(t.getMenuLevelNo().length() >= code.length() && t.getMenuLevelNo().substring(0,code.length()).equals(code)){
                total++;
            }
        }
        if(count == total){
            return true;
        }else{
            return false;
        }

    }


    /**
     * 构建菜单树
     * @param levelNo 菜单层级编码
     * @param levelNoSet 二级以及以上的菜单的层级编码集合
     * @param menuMap 菜单map
     * @param parentNode 上级树节点
     */
    private void addChildNode(String levelNo, Set<String> levelNoSet, Map<String, TsMenu> menuMap, TreeNode parentNode,List<TsMenu> slcList,List<TsMenu> allList) {
        int level = StringUtils.countMatches(levelNo, ".");
        for(String ln: levelNoSet) {
            if(StringUtils.countMatches(ln, ".")==(level+1) && StringUtils.startsWith(ln,levelNo+".")) {
                TsMenu t = menuMap.get(ln);
                TreeNode node = new CheckboxTreeNode(menuMap.get(ln), parentNode);
                node.setSelected(selectAble(ln,slcList,allList));
                this.addChildNode(ln, levelNoSet, menuMap, node, slcList,allList );
            }
        }
    }

	/**
	 * 分配菜单保存
	 */
	public void fpMenuAction() {
        Set<Integer> menuSet = new HashSet<Integer>();
        if(null != this.selectedNodes && this.selectedNodes.length > 0) {
            for(TreeNode node:this.selectedNodes) {
                this.chooseMenuIds(menuSet, node);
            }
        }
        this.systemService.sqMenu(menuSet, this.rid);
        JsfUtil.addSuccessMessage("保存成功！");
    }

    /**
     * 根据树节点，将其与其父节点的对象ID放到集合中
     * @param menuSet 菜单ID集合
     * @param node 树节点，内容存的是菜单对象
     */
    private void chooseMenuIds(Set<Integer> menuSet, TreeNode node) {
        if(null != node && !(node.equals(this.treeNode))) {
            TsMenu t = (TsMenu) node.getData();
            menuSet.add(t.getRid());
            this.chooseMenuIds(menuSet, node.getParent());
        }
    }

	/**
	 * 机构分配初始化
	 */
	public void jgfpInitAction() {
        this.treeNode = null;
        this.selectedNodes = null;

        TsUserInfo tsUserInfo = sessionData.getUser();
        List<List<UnitPO>> list = this.systemService.initUnitChoice(this.ifAdmin, tsUserInfo.getTsUnit().getTsZone().getZoneGb(),
                tsUserInfo.getTsUnit().getTsZone().getZoneType(), this.rid, tsUserInfo.getTsUnit().getRid());
        this.dualListModel = new DualListModel(list.get(0), list.get(1));
	}

	/**
	 * 机构分配保存
	 */
	public void jgfpAction() {
        TsUserInfo tsUserInfo = sessionData.getUser();
        this.systemService.jgfpRole(this.ifAdmin,tsUserInfo.getTsUnit().getTsZone().getZoneGb(),tsUserInfo.getTsUnit().getTsZone().getZoneType(),
                this.rid, tsUserInfo.getTsUnit().getRid(), (List<UnitPO>)this.dualListModel.getTarget());
	}

	/**
	 * 用户分配初始化
	 */
	public void yhfpInitAction() {
        this.userUnitId = null;

        TsUserInfo tsUserInfo = sessionData.getUser();

        /**
         * 地区初始化
         */
        if(null == this.zoneList || this.zoneList.size() <=0) {
            this.zoneList = this.commService.findZoneList(this.ifAdmin, tsUserInfo.getTsUnit().getTsZone().getZoneGb(),null,"5");
        }

        this.userZoneCode = tsUserInfo.getTsUnit().getTsZone().getZoneGb();
        this.userZoneName = tsUserInfo.getTsUnit().getTsZone().getZoneName();
        this.userZoneLevel = tsUserInfo.getTsUnit().getTsZone().getZoneType().toString();

        //初始化单位
        if(ifAdmin){
            this.unitMap = this.filterUnit(this.userZoneCode, this.userZoneLevel);
        }else{
            this.unitMap = new HashMap<String, Integer>();
            this.unitMap.put(tsUserInfo.getTsUnit().getUnitname(),tsUserInfo.getTsUnit().getRid());
        }
        this.userUnitId = sessionData.getUser().getTsUnit().getRid();

        //初始化用户
        this.storeTargetList = this.systemService.initUserChoice(this.ifAdmin, tsUserInfo.getTsUnit().getTsZone().getZoneGb(), tsUserInfo.getTsUnit().getTsZone().getZoneType(),
                this.rid, tsUserInfo.getTsUnit().getRid());
        this.userDualListModel = new DualListModel(new ArrayList<TsUserInfo>(0),this.storeTargetList);
        this.userDualListModel.setSource(this.systemService.findUserByUnitId(this.userUnitId,null, this.rid));

        //科室
        userUnitOfficeId = null;
        unitOfficeMap = new LinkedHashMap<String, Integer>();
        List<TsOffice> list = systemService.findOfficeByUnitId(userUnitId);
        if(list != null && list.size() > 0){
            for(TsOffice t : list){
                unitOfficeMap.put(t.getOfficename(),t.getRid());
            }
        }

        //单位人员科室
        offoceMap = systemService.findUserOffices(userUnitId,null);
	}

    public String getOfficeName(Integer userId){
        return offoceMap.get(userId);
    }


    /**
     * 根据地区刷单位
     * @param zoneCode 地区编码
     * @param zoneType 地区级别
     * @return 单位集合
     */
    private Map<String, Integer> filterUnit(String zoneCode, String zoneType) {
        TsUserInfo user = this.sessionData.getUser();
        Map<String, Integer> map = new LinkedHashMap<String, Integer>();
        List<TsUnit> list = this.systemService.findUnitByZoneId(this.ifAdmin, user.getTsUnit().getTsZone().getZoneGb(),
                user.getTsUnit().getRid(), zoneCode, zoneType);
        if(null != list && list.size() > 0) {
            for(TsUnit t:list) {
                map.put(t.getUnitname(), t.getRid());
            }
        }
        return map;
    }

    /**
     * 根据地区编码和地区级别获取上级地区编码
     * @param zoneCode 地区编码
     * @param zoneType 地区编码对应的地区级别
     * @return 上级地区编码
     */
    private static String findParentCode(String zoneCode, String zoneType) {
        if("2".equals(zoneType)) {
            return null;
        }else if("3".equals(zoneType)) {
            return zoneCode.substring(0, 2)+"00000000";
        }else if("4".equals(zoneType)) {
            return zoneCode.substring(0, 4)+"000000";
        }else if("5".equals(zoneType)) {
            return zoneCode.substring(0, 6)+"0000";
        }else if("6".equals(zoneType)) {
            return zoneCode.substring(0, 8)+"00";
        }else {
            return null;
        }
    }

    /**
     * 地区选中事件
     */
    public void onNodeSelect() {
        this.userUnitId = null;
        this.unitMap = this.filterUnit(this.userZoneCode, this.userZoneLevel);
        this.userDualListModel.setSource(new ArrayList<TsUserInfo>(0));
    }

    /**
     * 单位下啦change事件
     */
    public void onUnitChange() {
        if(null != this.userUnitId) {
            List<TsUserInfo> targetList = this.userDualListModel.getTarget();
            List<TsUserInfo> sourceList = this.systemService.findUserByUnitId(this.userUnitId,null, rid);
            this.userDualListModel.setSource(sourceList);
        }else {
            this.userDualListModel.setSource(new ArrayList<TsUserInfo>(0));
        }
        userUnitOfficeId = null;
        unitOfficeMap = new LinkedHashMap<String, Integer>();
        if(userUnitId != null){
            offoceMap = systemService.findUserOffices(userUnitId,null);
            List<TsOffice> list = systemService.findOfficeByUnitId(userUnitId);
            if(list != null && list.size() > 0){
                for(TsOffice t : list){
                    unitOfficeMap.put(t.getOfficename(),t.getRid());
                }
            }
        }

    }

    public void onofficeChange(){
        List<TsUserInfo> targetList = this.userDualListModel.getTarget();
        List<TsUserInfo> sourceList;
        if(userUnitOfficeId != null){
            sourceList = this.systemService.findUserByUnitId(this.userUnitId, this.userUnitOfficeId, rid);
        }else{
            sourceList = this.systemService.findUserByUnitId(this.userUnitId, null, rid);
        }
        this.userDualListModel.setSource(sourceList);

    }

	/**
	 * 用户分配保存
	 */
	public void yhfpAction() {
		this.systemService.yhfpRole(this.storeTargetList, this.userDualListModel.getTarget(), this.rid);
	}

	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder(" from TsRole t  ");
		sb.append(" left join t.tsRolePowerList t2  ");
		sb.append(" where 1 =1");
        //去掉过滤当前登录人过滤条件
        //sb.append(" and t.createManid =").append(this.sessionData.getUser().getRid());
        if(StringUtils.isNotBlank(this.searchRoleName)) {
            sb.append(" and t.roleName like :roleName");
            this.paramMap.put("roleName", "%"+this.searchRoleName+"%");
        }
        if(StringUtils.isNotBlank(this.roleTypeRids)){
            sb.append(" and t2.fkByRoleTypeId.rid in(").append(roleTypeRids).append(")");
        }
		String h2 = "select count(*) " + sb.toString();
		String h1 = "select t " + sb.append(" order by t.rid ");
		return new String[]{h1,h2};
	}
    //数据处理
    @Override
    public void processData(List<?> list) {
       if(list!=null&&list.size()>0){
           for(int i=0;i<list.size();i++){
               TsRole tr = (TsRole)list.get(i);
               if(tr==null)
            	   continue;
               TsUserInfo userInfo = this.systemService.findUser(tr.getCreateManid());
               if(userInfo==null)continue;
               tr.setUnitId(userInfo.getTsUnit().getRid());
               
               TsRole role = this.systemService.findRole(tr.getRid());
	       		List<TsRolePower> tsRolePowerList = role.getTsRolePowerList();
	       		if(null != tsRolePowerList && tsRolePowerList.size() >0){
	       			tr.setTypeName(tsRolePowerList.get(0).getFkByRoleTypeId().getCodeName());
	       		}
           }
       }
    }
    /**
     * 码表授权初始化
     */
    public void codeTypeInitAction() {
        this.selectedCodeIds = this.systemService.findCodeTypeOfRole(this.rid);

        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", false);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("height", 500);
        options.put("width", 900);
        options.put("contentWidth", 870);


        Map<String, List<String>> paramsMap = new HashMap<String, List<String>>();
        /**
         * 传入cntstmList参数office，值不为空表示要按照当前登录人所在的科室进行过滤
         */
        List<String> paramList = new ArrayList<String>(1);
        List<String> tagList = new ArrayList<String>(1);
        paramList.add(this.selectedCodeIds);
        tagList.add("1");
        paramsMap.put("selectIds", paramList);
        paramsMap.put("tag", tagList);
        RequestContext.getCurrentInstance().openDialog("/webapp/system/selectCodeType", options, paramsMap);
    }

    /**
     * 选择码表类型
     * @param event 弹出框关闭事件
     */
    public void onCodeTypeSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsCodeType> selectTypeList = (List<TsCodeType>) selectedMap.get("selectedCodeTypeList");

            List<String> codeTypeIdList = new ArrayList<String>();
            if(null != selectTypeList && selectTypeList.size() > 0) {
                for(TsCodeType t: selectTypeList) {
                    codeTypeIdList.add(t.getRid().toString());
                }
            }
            this.systemService.sqRoleCode(this.rid, this.selectedCodeIds, codeTypeIdList, this.sessionData.getUser().getRid());
        }
    }
    
    
    public void simpleCodeInitAction(){
    	  selectedSimpleCodeIds = this.systemService.findSimpleCodeOfRole(this.rid);
    	  Map<String, Object> options = new HashMap<String, Object>();
          options.put("modal", false);
          options.put("draggable", true);
          options.put("resizable", false);
          options.put("height", 500);
          options.put("width", 900);
          options.put("contentWidth", 870);


          Map<String, List<String>> paramsMap = new HashMap<String, List<String>>();
          List<String> paramList = new ArrayList<String>(1);
          List<String> tagList = new ArrayList<String>(1);
          paramList.add(selectedSimpleCodeIds);
          tagList.add("2");
          paramsMap.put("selectIds", paramList);
          paramsMap.put("tag", tagList);
          RequestContext.getCurrentInstance().openDialog("/webapp/system/selectSimpleCode", options, paramsMap);
    }
    public void onSimpleCodeSelect(SelectEvent event){
    	 Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
         if (null != selectedMap && selectedMap.size() > 0) {
             List<Object[]> selectSimpList = (List<Object[]>) selectedMap.get("selectedCodeTypeList");

             List<String> simpleCodeIdList = new ArrayList<String>();
             if(null != selectSimpList && selectSimpList.size() > 0) {
                 for(Object[] t: selectSimpList) {
                	 simpleCodeIdList.add(t[4].toString());
                 }
             }
             this.systemService.sqRoleSimpleCode(this.rid, this.selectedSimpleCodeIds, simpleCodeIdList, this.sessionData.getUser().getRid());
         }
    }

	public TsRole getTsRole() {
		return tsRole;
	}

	public void setTsRole(TsRole tsRole) {
		this.tsRole = tsRole;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

    public String getSearchRoleName() {
        return searchRoleName;
    }

    public void setSearchRoleName(String searchRoleName) {
        this.searchRoleName = searchRoleName;
    }

    public TreeNode getTreeNode() {
        return treeNode;
    }

    public void setTreeNode(TreeNode treeNode) {
        this.treeNode = treeNode;
    }

    public TreeNode[] getSelectedNodes() {
        return selectedNodes;
    }

    public void setSelectedNodes(TreeNode[] selectedNodes) {
        this.selectedNodes = selectedNodes;
    }

    public DualListModel getDualListModel() {
        return dualListModel;
    }

    public void setDualListModel(DualListModel dualListModel) {
        this.dualListModel = dualListModel;
    }

    public Integer getUserUnitId() {
        return userUnitId;
    }

    public void setUserUnitId(Integer userUnitId) {
        this.userUnitId = userUnitId;
    }

    public String getUserZoneName() {
        return userZoneName;
    }

    public void setUserZoneName(String userZoneName) {
        this.userZoneName = userZoneName;
    }

    public TreeNode getSelectedNode() {
        return selectedNode;
    }

    public void setSelectedNode(TreeNode selectedNode) {
        this.selectedNode = selectedNode;
    }

    public Map<String, Integer> getUnitMap() {
        return unitMap;
    }

    public void setUnitMap(Map<String, Integer> unitMap) {
        this.unitMap = unitMap;
    }

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public String getUserZoneCode() {
		return userZoneCode;
	}

	public void setUserZoneCode(String userZoneCode) {
		this.userZoneCode = userZoneCode;
	}

	public String getUserZoneLevel() {
		return userZoneLevel;
	}

	public void setUserZoneLevel(String userZoneLevel) {
		this.userZoneLevel = userZoneLevel;
	}

	public DualListModel getUserDualListModel() {
		return userDualListModel;
	}

	public void setUserDualListModel(DualListModel userDualListModel) {
		this.userDualListModel = userDualListModel;
	}

    public boolean isIfAdmin() {
        return ifAdmin;
    }

    public void setIfAdmin(boolean ifAdmin) {
        this.ifAdmin = ifAdmin;
    }

    public Map<String, Integer> getUnitOfficeMap() {
        return unitOfficeMap;
    }

    public void setUnitOfficeMap(Map<String, Integer> unitOfficeMap) {
        this.unitOfficeMap = unitOfficeMap;
    }

    public Integer getUserUnitOfficeId() {
        return userUnitOfficeId;
    }

    public void setUserUnitOfficeId(Integer userUnitOfficeId) {
        this.userUnitOfficeId = userUnitOfficeId;
    }

	public List<TsSimpleCode> getRoleTypeList() {
		return roleTypeList;
	}

	public void setRoleTypeList(List<TsSimpleCode> roleTypeList) {
		this.roleTypeList = roleTypeList;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Object getAdminss() {
		return adminss;
	}

	public void setAdminss(Object adminss) {
		this.adminss = adminss;
	}

    public String getRoleTypeRids() {
        return roleTypeRids;
    }

    public void setRoleTypeRids(String roleTypeRids) {
        this.roleTypeRids = roleTypeRids;
    }
}