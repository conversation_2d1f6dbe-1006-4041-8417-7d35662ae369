package com.chis.modules.system.web;

import javax.imageio.ImageIO;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Random;

/**
 * <p>类描述：验证码：根据session中的随机码生成图片</p>
 *
 * @ClassAuthor mxp,2017/11/22,AuthImage2
 */
@WebServlet(name="authImage2",value="/authImage2")
public class AuthImage2 extends HttpServlet{

	private static final long serialVersionUID = 5757128742980313450L;

	public void init() throws ServletException {
		super.init();
	}

	/**
	 * <p>方法描述：生成验证码图片</p>
	 *
	 * @MethodAuthor mxp,2017/11/22,service
	 */

	public void service(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		int width=70;
		int height=20;
		int pointx=width/4;
		int pointy=height-4;
		// 定义图像buffer
		BufferedImage buffImg = new BufferedImage(width, height,
				BufferedImage.TYPE_INT_RGB);
		Graphics2D g = buffImg.createGraphics();
		// 创建一个随机数生成器类
		Random random = new Random();
		// 将图像填充为白色
		g.setColor(Color.WHITE);
		g.fillRect(0, 0, width, height);
		// 创建字体，字体的大小应该根据图片的高度来定。
		Font font = new Font("Fixedsys", Font.BOLD, 20);
		// 设置字体。
		g.setFont(font);
		// 画边框。
		int red, green, blue;
		// 随机产生codeCount数字的验证码。
		char[] chars = (char[])request.getSession().getAttribute("sessionString");
		String strRand;
		for (int i = 0; i < 4; i++) {
			// 得到随机产生的验证码数字。
			strRand = String.valueOf(chars[i]);
			// 产生随机的颜色分量来构造颜色值，这样输出的每位数字的颜色值都将不同。
			red = random.nextInt(255);
			green = random.nextInt(100);
			blue = random.nextInt(255);
			// 用随机产生的颜色将验证码绘制到图像中。
			g.setColor(new Color(red, green, blue));
			g.drawString(strRand, i*pointx+2, pointy+1);
		}

		ServletOutputStream sos = response.getOutputStream();
		try {
			ImageIO.write(buffImg, "jpeg", sos);
		}finally {
			if(null != sos) {
				sos.close();
			}
		}
	}

	public void destroy() {
	}
}
