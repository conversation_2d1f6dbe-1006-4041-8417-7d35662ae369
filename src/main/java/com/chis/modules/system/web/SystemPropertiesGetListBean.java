package com.chis.modules.system.web;

import com.chis.common.utils.PropertiesLoader;
import com.chis.modules.system.logic.PropertiesContent;
import com.chis.modules.system.logic.PropertiesName;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * @Description <p>描述:配置文件查询<p>
 * @ClassAuthor wangxuan, 2021/1/14,11:09,SystemPropertiesGetListBean
 */
@ManagedBean(name = "systemPropertiesGetListBean")
@ViewScoped
public class SystemPropertiesGetListBean {
    /**
     * 配置类型列表集合
     */
    private List<PropertiesName> propertiesNameList;
    /**
     * 查询条件：配置类型key
     */
    private String searchKeySet;

    @PostConstruct
    public void init() {
        searchList();
    }

    /**
     * <p>
     * 方法描述://TOTO
     * <p>
     *
     * @MethodAuthor wangxuan 2021/1/20 15:21
     **/
    public void searchList() {
        List<PropertiesName> propertiesNamesTmp = systemPropertiesGetListBean();
        propertiesNameList = new ArrayList<>();
        if (searchKeySet != null) {
            for (PropertiesName propertiesName : propertiesNamesTmp) {
                List<PropertiesContent> list1 = new ArrayList<>();
                List<PropertiesContent> list = propertiesName.getPropertiesContentList();
                for (int i = 0; i < list.size(); i++) {
                    if (list.get(i).getAttributeName().toLowerCase().contains(searchKeySet.toLowerCase())) {
                        PropertiesContent propertiesContent = new PropertiesContent();
                        propertiesContent.setAttributeName(list.get(i).getAttributeName());
                        propertiesContent.setAttributeValue(list.get(i).getAttributeValue());
                        list1.add(propertiesContent);
                    }
                    propertiesName.setPropertiesContentList(list1);
                }
                propertiesNameList.add(propertiesName);
            }
        } else {
            propertiesNameList.addAll(propertiesNamesTmp);
        }
    }

    /**
     * @param
     * @Description:配置文件列表属性集合获取
     * @Date:2021/1/20 14:05
     * @author:wangxuan
     * @return:java.util.List<com.chis.modules.system.web.PropertiesName>
     **/
    public List<PropertiesName> systemPropertiesGetListBean() {
        List<PropertiesName> propertiesNameList = new ArrayList<>();
        try {
            //寻找文件路径
            ResourcePatternResolver loader = new PathMatchingResourcePatternResolver();
            Resource[] resources = loader.getResources("/*.properties");

            //遍历每一个配置文件
            for (Resource resource : resources) {
                //新建Properties文件载入工具对象
                PropertiesLoader propertiesLoader = new PropertiesLoader(new String[]{resource.getFilename()});
                //获取key对应的value值
                Properties properties = propertiesLoader.getProperties();
                //新建properties对面集合
                List<PropertiesContent> propertiesContentList = new ArrayList<>();
                //遍历配置文件对象（固定写法）
                for (Object key : properties.keySet()) {
                    String keyStr = key.toString();
                    String value = properties.getProperty(keyStr);
                    try {
                        value = new String(value.getBytes("iso-8859-1"), "utf-8");
                    } catch (UnsupportedEncodingException e) {
                        e.printStackTrace();
                    }
                    PropertiesContent propertiesContent1 = new PropertiesContent();
                    propertiesContent1.setAttributeName(keyStr);
                    propertiesContent1.setAttributeValue(value);
                    propertiesContentList.add(propertiesContent1);
                }
                PropertiesName propertiesName = new PropertiesName();
                propertiesName.setFileName(resource.getFilename());
                propertiesName.setPropertiesContentList(propertiesContentList);
                propertiesNameList.add(propertiesName);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return propertiesNameList;
    }

    public List<PropertiesName> getPropertiesNameList() {
        return propertiesNameList;
    }

    public void setPropertiesNameList(List<PropertiesName> propertiesNameList) {
        this.propertiesNameList = propertiesNameList;
    }

    public String getSearchKeySet() {
        return searchKeySet;
    }

    public void setSearchKeySet(String searchKeySet) {
        this.searchKeySet = searchKeySet;
    }

}
