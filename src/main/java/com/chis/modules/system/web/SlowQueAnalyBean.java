package com.chis.modules.system.web;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.logic.SlowQueJavaBean;
import com.chis.modules.system.logic.SlowQueSubJavaBean;
import com.chis.modules.system.service.CommServiceImpl;
import com.github.abel533.echarts.axis.CategoryAxis;
import com.github.abel533.echarts.axis.ValueAxis;
import com.github.abel533.echarts.code.AxisType;
import com.github.abel533.echarts.code.Magic;
import com.github.abel533.echarts.code.Orient;
import com.github.abel533.echarts.code.Position;
import com.github.abel533.echarts.code.Tool;
import com.github.abel533.echarts.code.Trigger;
import com.github.abel533.echarts.code.X;
import com.github.abel533.echarts.code.Y;
import com.github.abel533.echarts.feature.MagicType;
import com.github.abel533.echarts.json.GsonOption;
import com.github.abel533.echarts.series.Bar;

/**
 * 问卷答案数统计
 * <AUTHOR>
 */
@ViewScoped
@ManagedBean(name="slowQueAnalyBean")
public class SlowQueAnalyBean extends FacesBean{
	
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	/** 任务ID  */
	private Integer queId;
	private Integer taskId;
	/** 结果集合 */
	private List<SlowQueJavaBean> resultList;
	
	private String from;
	
	public SlowQueAnalyBean () {
		String val = JsfUtil.getRequest().getParameter("queId");
		String val2 = JsfUtil.getRequest().getParameter("taskId");
		from = JsfUtil.getRequest().getParameter("from");
		if (StringUtils.isNoneBlank(val)) {
			queId = Integer.valueOf(val);
		}
		
		if (StringUtils.isNoneBlank(val2)) {
			taskId = Integer.valueOf(val2);
		}
		
		if (StringUtils.isNoneBlank(from)) {
			from = from.replace("@", "&");
		}
		this.searchAction();
	}
	
	public String buildSql() {
		StringBuilder sb=new StringBuilder();
		sb.append(" SELECT X.QUESTLIB_ID, X.RID, X.TITLE_DESC, X.OPTION_DESC, X.DTS, X.SHOW_CODE, X.NUM, X.QUEST_TYPE ");
		sb.append("   FROM ( ");
		sb.append("        SELECT A.QUESTLIB_ID, A.RID, A.TITLE_DESC, B.OPTION_DESC, COUNT(Y.RID) DTS, ");
		sb.append("               A.SHOW_CODE, B.NUM, A.QUEST_TYPE ");
		sb.append("          FROM TS_PROB_SUBJECT A ");
		sb.append("         INNER JOIN TS_PRO_OPT B ON A.RID = B.QUEST_ID ");
		//sb.append("          LEFT JOIN TD_SLOW_ANSWER C ON A.RID = C.QUEST_ID AND B.RID = C.OPTION_VALUE ");
		sb.append("          LEFT JOIN ( ");
		sb.append("               SELECT C.RID, TASK.RID TASKID, C.QUEST_ID, C.Score_Value ");
		sb.append("                 FROM TD_SLOW_DC_TASK TASK INNER JOIN TD_SLOW_TASK_SUB SUB ON TASK.RID = SUB.TASK_ID ");
		sb.append("                INNER JOIN TD_SLOW_QUES_DC DC ON DC.TASK_ID = SUB.RID ");
		sb.append("                INNER JOIN TD_SLOW_ANSWER C ON C.MAIN_ID = DC.RID ");           
		sb.append("                WHERE TASK.RID = ").append(this.taskId);                  
		sb.append("                ) Y on  A.RID = Y.QUEST_ID  "); 
		sb.append("                AND INSTR(','||Y.SCORE_VALUE||',', ','||B.OPTION_VALUE||',') > 0  "); 
		sb.append("         WHERE A.QUEST_TYPE IN ( 0, 1, 2, 8, 10) ");
		sb.append("           AND A.QUESTLIB_ID = ").append(this.queId);           
		sb.append("         GROUP BY GROUPING SETS ((A.QUESTLIB_ID, A.RID, A.TITLE_DESC,A.SHOW_CODE, A.QUEST_TYPE), ");
		sb.append("               (A.QUESTLIB_ID, A.RID, A.TITLE_DESC, B.OPTION_DESC,A.SHOW_CODE, B.NUM, A.QUEST_TYPE) )");
		sb.append("         ORDER BY A.SHOW_CODE, B.NUM ) X ");
		sb.append("  UNION ");
		sb.append(" SELECT A.QUESTLIB_ID, A.RID, A.TITLE_DESC, '', 0, A.SHOW_CODE, 0, 5 ");
		sb.append("   FROM TS_PROB_SUBJECT A ");
		sb.append("  INNER JOIN TD_SLOW_DC_TASK B ON A.QUESTLIB_ID = B.QUESTLIB_ID ");
		sb.append("  WHERE B.RID  = ").append(this.taskId);
		sb.append("    AND A.QUESTLIB_ID = ").append(this.queId);   
		sb.append("    AND A.QUEST_TYPE = 5");
		return sb.toString();
	}
	
	public void searchAction() {
		String sql = this.buildSql();
		List<Object[]> data = this.commService.findDataBySqlNoPage(sql, null);
		if (data != null && data.size() > 0) {
			this.initResultData(data);
			this.initCharJson();
		}
	}
	
	public void initResultData(List<Object[]> data) {
		this.resultList = new ArrayList<SlowQueJavaBean>(0);
		String firstTopicId = "";
		SlowQueJavaBean bean = null;
		for (Object[] obj : data) {
			if (StringUtils.isBlank(firstTopicId) || !firstTopicId.equals(obj[1].toString())) {
				bean = new SlowQueJavaBean();
				bean.setCharJson(null);
				bean.setOptionList(new ArrayList<SlowQueSubJavaBean>());
				bean.setQueId(Integer.valueOf(obj[0].toString()));
				bean.setTopicId(Integer.valueOf(obj[1].toString()));
				bean.setTopicDesc(obj[2]== null ? "0" : obj[2].toString());
				bean.setTopicType(Integer.valueOf(obj[7].toString()));
				bean.setShowCode(obj[5]== null ? "0" : obj[5].toString());
				this.resultList.add(bean);
				firstTopicId = obj[1].toString();
			}
			
			SlowQueSubJavaBean subBean = new SlowQueSubJavaBean();
			subBean.setOptionDesc(obj[3] == null ? null : obj[3].toString());
			subBean.setReplyCnt(new BigDecimal(obj[4].toString()));
			bean.getOptionList().add(subBean);
		}
	}
	
	public void initCharJson () {
		if (this.resultList != null && this.resultList.size() > 0) {
			for  (SlowQueJavaBean bean : resultList) {
				if (bean.getTopicType().intValue() == 5 ) { // 标题
					continue;
				}
				
				List<SlowQueSubJavaBean> data = initReplyPate(bean.getOptionList());
				if (data == null || data.size() == 0 ) {
					continue;
				}
				
				bean.setOptionList(data);
				
				GsonOption op = new GsonOption();
				op.tooltip().trigger(Trigger.axis);
				op.toolbox().show(true)
						.feature(new MagicType(Magic.line, Magic.bar), Tool.restore,
								Tool.saveAsImage).padding(20).x(X.right).y(Y.top)
						.orient(Orient.horizontal);
				List<String> titles = new ArrayList<String>();
				List<String> values = new ArrayList<String>();
				for (SlowQueSubJavaBean obj : data) {
					if (StringUtils.isNoneBlank(obj.getOptionDesc())) {
						titles.add(obj.getOptionDesc());
						values.add(obj.getReplyPat().toString());
					}
				}
				
				//横坐标
				CategoryAxis xAxis = new CategoryAxis();
				xAxis.setData(titles);
				xAxis.setType(AxisType.category);
				xAxis.setShow(true);
				xAxis.setName("选项");
				op.xAxis().add(xAxis);
				
				// 纵坐标
				ValueAxis valueAxis = new ValueAxis();
				valueAxis.setType(AxisType.value);
				valueAxis.setName("%");
				valueAxis.axisLabel().formatter("{value}%");
				op.yAxis().add(valueAxis);
				Bar bar = new Bar();
				bar.itemStyle().normal().color("#6699FF");
				bar.itemStyle().normal().label().position(Position.top);
				bar.itemStyle().normal().label().textStyle().fontSize(15);
				bar.itemStyle().normal().label().textStyle().color("#000000");
				bar.itemStyle().normal().label().show(true);
				bar.data().addAll(values);
				op.series(bar);
				bean.setCharJson(op.toString());
			}
		}
	}
	
	/** 初始化比例 */
	public List<SlowQueSubJavaBean> initReplyPate(List<SlowQueSubJavaBean> data) {
		List<SlowQueSubJavaBean> returnData = new ArrayList<SlowQueSubJavaBean>(0);
		if (data != null && data.size() > 0) {
			BigDecimal sumCnt = new BigDecimal(0);
			for (SlowQueSubJavaBean bean : data) {
				if (StringUtils.isBlank(bean.getOptionDesc()) ) {
					sumCnt = bean.getReplyCnt();
				}
			}
			
			for (SlowQueSubJavaBean bean : data) {
				if (StringUtils.isNotBlank(bean.getOptionDesc()) ) {
					bean.setReplySum(sumCnt);
					if (sumCnt.intValue() == 0) {
						bean.setReplyPat(new BigDecimal(0));
					} else {
						bean.setReplyPat(bean.getReplyCnt().multiply(new BigDecimal(100)).divide(sumCnt, 2, BigDecimal.ROUND_HALF_DOWN));
					}
				}
				
				returnData.add(bean);
			}
		}
		return returnData;
	}
	
	public void backAction () {
		try {
			if (StringUtils.isNoneBlank(this.from)) {
				FacesContext.getCurrentInstance().getExternalContext().redirect(this.from);
			} else {
				return ;
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public Integer getQueId() {
		return queId;
	}

	public void setQueId(Integer queId) {
		this.queId = queId;
	}

	public List<SlowQueJavaBean> getResultList() {
		return resultList;
	}

	public void setResultList(List<SlowQueJavaBean> resultList) {
		this.resultList = resultList;
	}

	public String getFrom() {
		return from;
	}

	public void setFrom(String from) {
		this.from = from;
	}

	public Integer getTaskId() {
		return taskId;
	}

	public void setTaskId(Integer taskId) {
		this.taskId = taskId;
	}
	
}
