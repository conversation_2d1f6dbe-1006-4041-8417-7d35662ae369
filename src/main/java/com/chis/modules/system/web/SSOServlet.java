package com.chis.modules.system.web;

import java.io.IOException;
import java.util.Map;

import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MD5Util;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.service.SystemServiceImpl;
import com.chis.common.utils.StringUtils;



public class SSOServlet extends HttpServlet {
	
	private static final long serialVersionUID = -3621108376593353775L;
	/**url地址传用户ID的key*/
	private static final String USERID = "userno";
	private static final String PASSWORD="password";
	private static final String MENU_ADR = "url";
	//ejb session bean
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	private SystemServiceImpl systemService = (SystemServiceImpl)SpringContextHolder.getBean(SystemServiceImpl.class);
	
	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		String userId = request.getParameter("userno");
		String password = request.getParameter("password");
		
		if(StringUtils.isNotBlank(userId)) {
			TsUserInfo user = this.systemModuleService.findUserByUserNo(userId);
			if(null != user) {
	            if(user.getPassword().equalsIgnoreCase(new MD5Util().getMD5ofStr(password))) {
	                if(user.getIfReveal().intValue() == 1) {
	                    //登录成功
	                	SessionData sessionData = new SessionData(user);
	                	Map<String, String> btnMap = this.systemService.findUsersBtn(user.getRid());
	                	sessionData.setButtonMap(btnMap);
	                	sessionData.setBtnSet(systemService.findUserBtns(user.getRid()));
	                	request.getSession().setAttribute(SessionData.SESSION_DATA, sessionData);
	                	response.sendRedirect("head.faces");
	                } else {
	                	response.sendRedirect("login.faces");
	                }
	            }else {
	            	response.sendRedirect("login.faces");
	            }
	        }else {
	        	response.sendRedirect("login.faces");
	        }
		}else {
			response.sendRedirect("login.faces");
		}
	}
	
	@Override
	protected void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		this.doPost(request, response);
	}

}