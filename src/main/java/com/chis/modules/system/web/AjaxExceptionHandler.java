/*
 * Generated, Do Not Modify
 */
/*
 * Copyright 2009-2013 PrimeTek.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.chis.modules.system.web;

import javax.faces.application.ResourceDependencies;
import javax.faces.application.ResourceDependency;
import javax.faces.component.UIComponentBase;

@ResourceDependencies({
	@ResourceDependency(library="primefaces", name="primefaces.js")
})
public class AjaxExceptionHandler extends UIComponentBase {


	public static final String COMPONENT_TYPE = "org.primefaces.component.AjaxExceptionHandler";
	public static final String COMPONENT_FAMILY = "org.primefaces.component";

	protected enum PropertyKeys {

		onexception
		,update
		,type;

		String toString;

		PropertyKeys(String toString) {
			this.toString = toString;
		}

		PropertyKeys() {}

		public String toString() {
			return ((this.toString != null) ? this.toString : super.toString());
}
	}

	public AjaxExceptionHandler() {
		setRendererType(null);
	}

	public String getFamily() {
		return COMPONENT_FAMILY;
	}

	public String getOnexception() {
		return (String) getStateHelper().eval(PropertyKeys.onexception, null);
	}
	public void setOnexception(String _onexception) {
		getStateHelper().put(PropertyKeys.onexception, _onexception);
	}

	public String getUpdate() {
		return (String) getStateHelper().eval(PropertyKeys.update, null);
	}
	public void setUpdate(String _update) {
		getStateHelper().put(PropertyKeys.update, _update);
	}

	public String getType() {
		return (String) getStateHelper().eval(PropertyKeys.type, null);
	}
	public void setType(String _type) {
		getStateHelper().put(PropertyKeys.type, _type);
	}

}