package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;

import org.apache.commons.lang.StringUtils;
import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TsProbExampool;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.DefaultLazyDataModel;

/**
 * 
 * <AUTHOR> 问卷题库（通用）选择组件 可传入参数 paramType 系统类型 （可以传入多个，以逗号隔开） questType
 *         默认勾选的题目类型（可以传入多个，以逗号隔开） queTypeShow 显示的题目类型（可以传入多个，以逗号隔开） psnid
 *         病患id（传入病患id，则只会显示该病患所答题目） queId 问卷id（显示该问卷内的所有题目）
 */
@ManagedBean(name = "selectQuestionBean")
@ViewScoped
public class SelectQuestionBean implements IProcessData {
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private SystemModuleServiceImpl systemService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commServiceImpl = SpringContextHolder.getBean(CommServiceImpl.class);
	private String searchTitle;
	// 选择的题目类型
	private List<String> selectQueTypeList;

	// 选择的数据
	private List<TsProbExampool> selectList;
	private TsProbExampool tsProbExampool;
	// 类型名称
	private String examName;
	// 类型id
	private Integer examId;
	// 选中的节点
	private TreeNode selectedNode;
	private TreeNode examNode;
	private String paramType;
	private String queTypeShow;
	private List<SelectItem> showTypeList;
	private DefaultLazyDataModel<TsProbExampool> queDataModel;
	private String psnid;

	private Integer selectIndex;

	private boolean selected;
	/** 选择状态 1 已选 2 未选 */
	private String[] selectState;

	private String queId;
	/** 授权问卷类型的种类 **/
	private List<TsSimpleCode> queTypeList;

	public SelectQuestionBean() {
		init();
	}

	public void init() {
		selectList = new ArrayList<TsProbExampool>(0);
		selectQueTypeList = new ArrayList<String>(0);
		showTypeList = new ArrayList<SelectItem>(0);
		paramType = JsfUtil.getRequest().getParameter("paramType");
		String questType = JsfUtil.getRequest().getParameter("questType");
		queTypeShow = JsfUtil.getRequest().getParameter("queTypeShow");// 显示的类型
		psnid = JsfUtil.getRequest().getParameter("psnid");
		queId = JsfUtil.getRequest().getParameter("queId");
		selectState = new String[] { "2" };
		if (null != questType && StringUtils.isNotBlank(questType)) {
			String[] queType = questType.split(",");
			if (null != queType && queType.length > 0) {
				for (int i = 0; i < queType.length; i++) {
					selectQueTypeList.add(queType[i]);
				}
			}
		}
		if (sessionData.getUser().getUserNo().equals(Constants.ADMIN)) {
			queTypeList = this.commServiceImpl.findSimpleCodesByTypeId("13001");
		} else {
			queTypeList = systemService.getTsProbExamtypeByUserId(sessionData.getUser().getRid());
		}
		if (null != queTypeShow && StringUtils.isNotBlank(queTypeShow)) {
			String[] showType = queTypeShow.split(",");
			if (null != showType && showType.length > 0) {
				for (int i = 0; i < showType.length; i++) {
					if ("0".equals(showType[i].toString())) {
						showTypeList.add(new SelectItem("0", "单选题"));
					}
					if ("1".equals(showType[i].toString())) {
						showTypeList.add(new SelectItem("1", "多项选择题"));
					}
					if ("2".equals(showType[i].toString())) {
						showTypeList.add(new SelectItem("2", "滑动题"));
					}
					// if("3".equals(showType[i].toString())){
					// showTypeList.add(new SelectItem("3", "评价题"));
					// }
					if ("4".equals(showType[i].toString())) {
						showTypeList.add(new SelectItem("4", "文本填空题"));
					}
					if ("6".equals(showType[i].toString())) {
						showTypeList.add(new SelectItem("6", "日期填空题"));
					}
					if ("7".equals(showType[i].toString())) {
						showTypeList.add(new SelectItem("7", "整数填空题"));
					}
					if ("9".equals(showType[i].toString())) {
						showTypeList.add(new SelectItem("9", "数字填空题"));
					}
					if ("10".equals(showType[i].toString())) {
						showTypeList.add(new SelectItem("10", "是非题"));
					}
					if ("11".equals(showType[i].toString())) {
						showTypeList.add(new SelectItem("11", "表格题"));
					}
					if ("12".equals(showType[i].toString())) {
						showTypeList.add(new SelectItem("12", "只读显示"));
					}
				}
			}
		} else {
			showTypeList.add(new SelectItem("0", "单选题"));
			showTypeList.add(new SelectItem("1", "多项选择题"));
			showTypeList.add(new SelectItem("2", "滑动题"));
			showTypeList.add(new SelectItem("4", "文本填空题"));
			showTypeList.add(new SelectItem("6", "日期填空题"));
			showTypeList.add(new SelectItem("7", "整数填空题"));
			showTypeList.add(new SelectItem("9", "数字填空题"));
			showTypeList.add(new SelectItem("10", "是非题"));
			showTypeList.add(new SelectItem("11", "表格题"));
			showTypeList.add(new SelectItem("12", "只读显示"));
		}
		this.searchAction();
	}

	public void searchAction() {
		StringBuilder sb = new StringBuilder();
		StringBuilder searchSb = new StringBuilder();
		StringBuilder countSb = new StringBuilder();
		sb.append(" from TsProbExampool t where 1=1 and t.state=1 and t.qesCode=t.qesLevelCode");
		if (null != searchTitle && StringUtils.isNotBlank(searchTitle)) {
			searchTitle = searchTitle.replace("'", "");
			sb.append(" and t.titleDesc like '%").append(searchTitle).append("%'");
		}
		if (null != selectQueTypeList && selectQueTypeList.size() > 0) {
			StringBuilder selectQueTypeStr = new StringBuilder();
			for (int i = 0; i < selectQueTypeList.size(); i++) {
				selectQueTypeStr.append(",").append(selectQueTypeList.get(i).toString());
			}
			selectQueTypeStr.deleteCharAt(0);
			sb.append(" and t.questType in (").append(selectQueTypeStr).append(")");
		} else {
			if (null != queTypeShow && StringUtils.isNotBlank(queTypeShow)) {
				StringBuilder queTypeid = new StringBuilder();
				String[] showType = queTypeShow.split(",");
				if (null != showType && showType.length > 0) {
					for (int i = 0; i < showType.length; i++) {
						queTypeid.append(",").append(showType[i].toString());
					}
					queTypeid.deleteCharAt(0);
					sb.append(" and t.questType in (").append(queTypeid).append(")");
				}
			}
		}
		if (StringUtils.isNotBlank(examName) && null != examId) {
			sb.append(" and t.tsSimpleCodeByTypeId.rid =").append(examId);
		}

		if (this.selectState != null && this.selectState.length == 1) {
			if (null != selectList && selectList.size() > 0) {
				StringBuilder ridStr = new StringBuilder();
				for (TsProbExampool t : selectList) {
					ridStr.append(",").append(t.getRid());
				}
				ridStr.deleteCharAt(0);
				if (this.selectState[0].equals("1")) {
					sb.append(" and t.rid in (").append(ridStr).append(")");
				} else {
					sb.append(" and t.rid not in (").append(ridStr).append(")");
				}
			} else {
				if (this.selectState[0].equals("1")) { // 已选择为空 查询时则不显示内容
					sb.append(" and t.rid = -1 ");
				}
			}
		}

		if (null != paramType && StringUtils.isNotBlank(paramType)) {
			sb.append(" and t.tsSimpleCodeByTypeId.rid in (").append(paramType).append(")");
		}
		// 授权的题库问卷码表数据
		if (null != queTypeList && queTypeList.size() > 0) {
			String s = "";
			for (TsSimpleCode t : queTypeList) {
				s += "," + t.getRid().toString();
			}
			sb.append(" and t.tsSimpleCodeByTypeId.rid in (");
			sb.append(s.replaceFirst(",", "")).append(")");
		} else {
			sb.append(" and t.tsSimpleCodeByTypeId.rid in (-1)");
		}
		if (null != psnid && StringUtils.isNotBlank(psnid)) {
			sb.append(" and t.rid in (");
			sb.append("select t.tsProbSubjectByQuestId.pool from TdSlowAnswer t where t.tdSlowQuesDcByMainId.tdSlowPsnByPsnId.rid=");
			sb.append(Integer.valueOf(psnid)).append(")");
		}
		if (null != queId && StringUtils.isNotBlank(queId)) {
			sb.append(" and t.rid in (");
			sb.append(" select t3.pool.rid from TsProbSubject t3 where t3.rid in (");
			sb.append(" select t2.tsProbSubjectByQuestId.rid from TdSlowAnswer t2 where t2.tdSlowQuesDcByMainId.rid in(");
			sb.append(" select t1.rid from TdSlowQuesDc t1 where t1.tsProbLibByQuestionId.rid=");
			sb.append(queId).append(")))");
		}
		sb.append(" order by t.qesCode");
		searchSb.append("select t").append(sb);
		countSb.append("select count(t.rid)").append(sb);
		queDataModel = new DefaultLazyDataModel<>(searchSb.toString(), countSb.toString(), null, this,
				"selectedGdsTable");
	}

	@Override
	public void processData(List<?> list) {
		if (selectList != null && selectList.size() > 0) {
			for (TsProbExampool pool : selectList) {
				for (Object obj : list) {
					TsProbExampool pool2 = (TsProbExampool) obj;

					if (pool2.getRid().intValue() == pool.getRid().intValue()) {
						pool2.setSelected(true);
					}
				}
			}
		}
	}

	public void saveAction() {
		Map<String, Object> map = new HashMap<String, Object>();
		selectList = systemService.getNodeList(selectList);
		map.put("selectedList", selectList);
		RequestContext.getCurrentInstance().closeDialog(map);
	}

	public void closeAction() {
		RequestContext.getCurrentInstance().closeDialog(null);
	}
	
	public void selectAction(TsProbExampool tsProbExampool) {
		if (tsProbExampool.isSelected()) {
			selectList.add(tsProbExampool);
		} else {
			int i = 0;
			for (TsProbExampool pool : selectList) {
				if (pool.getRid().intValue() == tsProbExampool.getRid().intValue()) {
					selectList.remove(i);
					return;
				}
				i++;
			}
		}
	}

	// 初始化问卷题目类型树
	public void initExamTree() {
		examNode = new DefaultTreeNode("root", null);
		List<TsSimpleCode> list = new ArrayList<TsSimpleCode>(0);
		list = queTypeList;
		if (null != list && list.size() > 0) {
			Set<String> firstLevelNoSet = new LinkedHashSet<String>(); // 只有第一层
			Set<String> levelNoSet = new LinkedHashSet<String>(); // 没有第一层
			Map<String, TsSimpleCode> allMap = new HashMap<String, TsSimpleCode>();

			for (TsSimpleCode t : list) {
				allMap.put(t.getCodeLevelNo(), t);
				if (StringUtils.isNotBlank(t.getCodeLevelNo())) {
					if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
						firstLevelNoSet.add(t.getCodeLevelNo());
					} else {
						levelNoSet.add(t.getCodeLevelNo());
					}
				}
			}

			for (String ln : firstLevelNoSet) {
				TsSimpleCode t = allMap.get(ln);
				TreeNode node = new DefaultTreeNode(allMap.get(ln), this.examNode);
				this.addChildNode(ln, levelNoSet, allMap, node);
			}
		}
	}

	/**
	 * 构建菜单树
	 * 
	 * @param levelNo
	 *            菜单层级编码
	 * @param levelNoSet
	 *            二级以及以上的菜单的层级编码集合
	 * @param allMap
	 *            所有数据级map
	 * @param parentNode
	 *            上级树节点
	 */
	private void addChildNode(String levelNo, Set<String> levelNoSet, Map<String, TsSimpleCode> allMap,
			TreeNode parentNode) {
		int level = StringUtils.countMatches(levelNo, ".");
		for (String ln : levelNoSet) {
			if (StringUtils.countMatches(ln, ".") == (level + 1) && StringUtils.startsWith(ln, levelNo + ".")) {
				TsSimpleCode t = allMap.get(ln);
				TreeNode node = new DefaultTreeNode(allMap.get(ln), parentNode);
				node.setExpanded(false);
				this.addChildNode(ln, levelNoSet, allMap, node);
			}
		}
	}

	public void onchange() {

	}

	public void onNodeSelect(NodeSelectEvent event) {
		TreeNode node = event.getTreeNode();
		TsSimpleCode t = (TsSimpleCode) node.getData();
		examName = t.getCodeName();
		examId = t.getRid();
	}

	public void clearExamName() {
		examName = null;
		examId = null;
	}

	public List<String> getSelectQueTypeList() {
		return selectQueTypeList;
	}

	public void setSelectQueTypeList(List<String> selectQueTypeList) {
		this.selectQueTypeList = selectQueTypeList;
	}

	public List<TsProbExampool> getSelectList() {
		return selectList;
	}

	public void setSelectList(List<TsProbExampool> selectList) {
		this.selectList = selectList;
	}

	public TsProbExampool getTsProbExampool() {
		return tsProbExampool;
	}

	public void setTsProbExampool(TsProbExampool tsProbExampool) {
		this.tsProbExampool = tsProbExampool;
	}

	public String getSearchTitle() {
		return searchTitle;
	}

	public void setSearchTitle(String searchTitle) {
		this.searchTitle = searchTitle;
	}

	public String getExamName() {
		return examName;
	}

	public void setExamName(String examName) {
		this.examName = examName;
	}

	public TreeNode getSelectedNode() {
		return selectedNode;
	}

	public void setSelectedNode(TreeNode selectedNode) {
		this.selectedNode = selectedNode;
	}

	public TreeNode getExamNode() {
		return examNode;
	}

	public void setExamNode(TreeNode examNode) {
		this.examNode = examNode;
	}

	public List<SelectItem> getShowTypeList() {
		return showTypeList;
	}

	public void setShowTypeList(List<SelectItem> showTypeList) {
		this.showTypeList = showTypeList;
	}

	public DefaultLazyDataModel<TsProbExampool> getQueDataModel() {
		return queDataModel;
	}

	public void setQueDataModel(DefaultLazyDataModel<TsProbExampool> queDataModel) {
		this.queDataModel = queDataModel;
	}

	public Integer getSelectIndex() {
		return selectIndex;
	}

	public void setSelectIndex(Integer selectIndex) {
		this.selectIndex = selectIndex;
	}

	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}

	public String[] getSelectState() {
		return selectState;
	}

	public void setSelectState(String[] selectState) {
		this.selectState = selectState;
	}

}
