package com.chis.modules.system.web;

import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URLEncoder;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;

import com.chis.common.utils.JsfUtil;

@ManagedBean(name = "downLoadPreBean")
@ViewScoped
public class DownLoadPreBean {

	/**PF下载的文件*/
	private StreamedContent streamedContent;
	/**下载路径*/
	private String filePath;
	/**下载文件名*/
	private String fileName;
	
	public StreamedContent getStreamedContent() {
		System.err.println("【】：" + filePath);
		try {
			String xnPath = JsfUtil.getAbsolutePath();
			String path="";
			if(filePath.indexOf(xnPath) != -1){
				path = filePath;
			}else{
				path = xnPath + filePath;
			}
			InputStream stream = new FileInputStream(path);
	        this.streamedContent = new DefaultStreamedContent(stream, "application/pdf", URLEncoder.encode(fileName, "UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("系统找不到指定的文件！");
			return null;
		}
		return streamedContent;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public void setStreamedContent(StreamedContent streamedContent) {
		this.streamedContent = streamedContent;
	}
	
	
}
