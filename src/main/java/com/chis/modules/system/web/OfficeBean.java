package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import javax.validation.constraints.NotNull;

import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TbSysEmp;
import com.chis.modules.system.entity.TsDeptMdref;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.DefaultLazyDataModel;

/**
 * 科室管理
 * 
 * @editContent 删除buildProcessData()方法，如果查询出来的数据集需要修改，子类不需要再重写buildProcessData()  david 2014-09-04 
 * 
 * <AUTHOR>
 */
@ManagedBean(name="officeBean")
@ViewScoped
public class OfficeBean extends FacesSimpleBean implements IProcessData {

	private static final long serialVersionUID = -738445454536625063L;
	/**存在session中的对象*/
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/**ejb session bean*/
	private SystemModuleServiceImpl systemService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	/**科室对象*/
	private TsOffice tsOffice = new TsOffice();
	/**科室对象rid*/
	private Integer rid;
    /**查询条件：科室名称*/
    private String searchOfficeName;
    /**查询条件：状态*/
    private Short searchState;
    /**查询条件：所有用户单位查询*/
    private List<SelectItem> untiList;
    /**单位接收值*/
    private Integer unitId;

    /**科室状态*/
    private String ifReveal;

    /**科室树（编辑选择）*/
    private TreeNode upUnitTreeNode;
    /**选中后的上级科室*/
    private TreeNode selectedUpNote;
    /**选中上级科室的名称*/
    private String upOfficeName;
    /**选中上级科室的rid*/
    private Integer upOfficeRid;
    /**科室负责人*/
    private List<SelectItem> empList;
    /**分管领导*/
    private List<SelectItem> empList2;
    private Integer tbSysEmpByDeptLeaderId;//负责人
    private Integer tbSysEmpByManageManid;//总管




    /**科室树*/
    private TreeNode treeNode;
    /**选中的科室*/
    private TreeNode selectedNode;
    /**选择后的：科室名称*/
    private String userOfficeName;
    /**选择后的：科室id*/
    @NotNull(message="新科室名称不允许为空！")
    private Integer userOfficeId;

    /**记录老科室编码（修改用）*/
    private String oldOfficecode;

    /**归并科室名称查询*/
    private String selRefOffceName;
    /**归并科室单位选择*/
    private Integer refunitId;
    /**归并科室*/
    private DefaultLazyDataModel refList;
    /**取消归并*/
    private TsDeptMdref tsDeptMdref;
    /**是否是超管*/
    private boolean ifAdmin = Boolean.TRUE;
    /**超级管理员：所有用户单位查询（添加，修改）*/
    private List<SelectItem> unitEditList;
    @NotNull(message="请选择单位！")
    private Integer unitEditRid;

	@PostConstruct
	public void init() {
        TsUserInfo tsUserInfo = this.sessionData.getUser();
        if(!tsUserInfo.getUserNo().equals(Constants.ADMIN)) {
            ifAdmin = Boolean.FALSE;
        }
		searchState=1;
        searchOfficeName="";
        untiList=new LinkedList<SelectItem>();
        untiList.add(new SelectItem(null,"--全部--"));
        empList=new LinkedList<SelectItem>();
        empList.add(new SelectItem(null,"--请选择--"));
        empList2=new LinkedList<SelectItem>();
        empList2.add(new SelectItem(null,"--请选择--"));
        if(ifAdmin) {
            untiListInnit(untiList);
            unitId=null;
        }else{
            unitId=tsUserInfo.getTsUnit().getRid();
            List<TbSysEmp> emplist=systemService.selSysEmpList(unitId);
            if(null!=emplist&&emplist.size()>0){
                for(TbSysEmp h:emplist){
                    empList.add(new SelectItem(h.getRid(),h.getEmpName()));
                }
            }
        }

        this.searchAction();
	}

	/**
	 * 添加初始化
	 */
	public void addInitAction() {
		this.tsOffice = new TsOffice();
        if(ifAdmin) {
            unitEditList = new LinkedList<SelectItem>();
            unitEditList.add(new SelectItem(null,"--请选择--"));
            untiListInnit(unitEditList);
            unitEditRid=null;
        }else{
            unitEditRid=sessionData.getUser().getTsUnit().getRid();
        }
        tbSysEmpByDeptLeaderId=null;//负责人
        tbSysEmpByManageManid=null;//总管
		this.tsOffice.setCreateDate(new Date());
		this.tsOffice.setCreateManid(sessionData.getUser().getRid());
        tsOffice.setIfReveal((short)1);
        changeUnitRefOffice();
	}

	/**
	 * 修改初始化
	 */
	public void modInitAction() {
        unitEditRid=null!=tsOffice.getTsUnit()?tsOffice.getTsUnit().getRid():null;
        if(ifAdmin) {
            unitEditList = new LinkedList<SelectItem>();
            unitEditList.add(new SelectItem(null,"--请选择--"));
            untiListInnit(unitEditList);
        }

        tbSysEmpByDeptLeaderId=null!=tsOffice.getTbSysEmpByDeptLeaderId()?tsOffice.getTbSysEmpByDeptLeaderId().getRid():null;//负责人
        tbSysEmpByManageManid=null!=tsOffice.getTbSysEmpByManageManid()?tsOffice.getTbSysEmpByManageManid().getRid():null;//总管
        oldOfficecode=tsOffice.getOfficecode();
		this.tsOffice.setModifyDate(new Date());
		this.tsOffice.setModifyManid(this.sessionData.getUser().getRid());
        changeUnitRefOffice();
        if(null!=tsOffice.getParentOffice()){
            upOfficeRid=tsOffice.getParentOffice().getRid();
            upOfficeName=tsOffice.getParentOffice().getSimplName();
        }
	}

    /**
     * 编辑信息，选择上级科室
     * @param event
     */
    public void onNodeUpSelect(NodeSelectEvent event) {
        TreeNode node = event.getTreeNode();
        TsOffice office = (TsOffice) node.getData();
        upOfficeName=office.getSimplName();
        upOfficeRid=office.getRid();
    }
	/**
	 * 保存
	 */
	public void saveAction() {
        RequestContext requestContext = RequestContext.getCurrentInstance();
        boolean isaginTag=true;
        //判断是否重复
        if(null==tsOffice||null==tsOffice.getRid()){
            isaginTag=systemService.selOfficeCode(null,null, tsOffice.getOfficecode(),unitEditRid);
        }else {
            isaginTag=systemService.selOfficeCode(tsOffice.getRid(), oldOfficecode, tsOffice.getOfficecode(),unitEditRid);
        }
        if(!isaginTag){
            JsfUtil.addErrorMessage("输入的科室编码已存在！");
            return;
        }

       if(null!=tbSysEmpByDeptLeaderId){
           tsOffice.setTbSysEmpByDeptLeaderId(new TbSysEmp(tbSysEmpByDeptLeaderId));
       }if(null!=tbSysEmpByManageManid){
            tsOffice.setTbSysEmpByManageManid(new TbSysEmp(tbSysEmpByManageManid));
        }
        tsOffice.setTsUnit(new TsUnit(unitEditRid));
        if(null!=upOfficeRid) {
            tsOffice.setParentOffice(new TsOffice(upOfficeRid));
        }
        this.systemService.saveOrUpdateOffice(this.tsOffice);
        if(null == this.tsOffice.getRid()) {
            this.searchAction();
        }
        requestContext.execute("OfficeEditDialog.hide()");
	}
	
	/**
	 * 删除
	 */
	public void deleteAction() {
		String msg = this.systemService.deleteOffice(this.rid);
		if(StringUtils.isNotBlank(msg)) {
			JsfUtil.addErrorMessage(msg);
		}
	}

    /**
     * 启用、停用
     */
    public void stateChangeAction(){
        systemService.changeStateOffice(this.rid,Short.valueOf(ifReveal));
        this.searchAction();
    }

    /***
     * 归并初始化上级科室树
     */
    public void modRefInnitAction(){
        List<TsOffice> list= systemService.selAllOfficeList(null,tsOffice.getTsUnit().getRid());
        selectedNode=null;
        userOfficeName="";
        userOfficeId=null;
        treeNode = new DefaultTreeNode("root", null);
        treeSet(treeNode,list);
    }

    /**
     * 根据单位刷新上级树
     */
    public void changeUnitRefOffice(){
        empList=new LinkedList<SelectItem>();
        empList.add(new SelectItem(null,"--请选择--"));
        empList2=new LinkedList<SelectItem>();
        empList2.add(new SelectItem(null,"--请选择--"));
        if(null!=unitEditRid){
            upOfficeName="";
            upOfficeRid=null;
           List<TsOffice> list= systemService.selAllOfficeList(tsOffice.getRid(),unitEditRid);
            selectedUpNote=null;
            upUnitTreeNode = new DefaultTreeNode("root", null);
            treeSet(upUnitTreeNode,list);
            //初始化科室负责人
            List<TbSysEmp> emplist=systemService.selSysEmpList2(unitEditRid, rid);

            if(null!=emplist&&emplist.size()>0){
                for(TbSysEmp h:emplist){
                    empList.add(new SelectItem(h.getRid(),h.getEmpName()));
                }
            }
            //初始化分管领导人
            List<TbSysEmp> emplist2=systemService.selSysEmpList2(unitEditRid,null);

            if(null!=emplist2&&emplist2.size()>0){
                for(TbSysEmp h:emplist2){
                    empList2.add(new SelectItem(h.getRid(),h.getEmpName()));
                }
            }
        }else{
            upUnitTreeNode=null;
        }
    }

    /**
     * 公共封装树的方法调用
     * @param tagTreeNode 树节点对象
     * @param list 查询出的数据
     */
    private void treeSet(TreeNode tagTreeNode,List<TsOffice> list){
        Map<Integer, TreeNode> map = new HashMap<Integer, TreeNode>();
        if(null != list && list.size() > 0) {
            for (TsOffice tsOffice1 : list) {
                if (null == tsOffice1.getParentOffice() || tsOffice1.getParentOffice().getRid() == -1) {
                    TreeNode temNode = new DefaultTreeNode(tsOffice1, tagTreeNode);
                    map.put(tsOffice1.getRid(), temNode);
                }
            }
            for (TsOffice tsOffice1 : list) {
                if (null != tsOffice1.getParentOffice() && null != tsOffice1.getParentOffice().getRid() && tsOffice1.getParentOffice().getRid() != -1) {
                    Integer parentId = tsOffice1.getParentOffice().getRid();
                    if (null != parentId && parentId != -1) {
                        TreeNode parentNode = map.get(parentId);
                        if (null != parentNode) {
                            TreeNode node = new DefaultTreeNode(tsOffice1, parentNode);
                            map.put(tsOffice1.getRid(), node);
                        } else {
                            TreeNode temNode = new DefaultTreeNode(tsOffice1, tagTreeNode);
                            map.put(tsOffice1.getRid(), temNode);
                        }
                    } else {
                        TreeNode temNode = new DefaultTreeNode(tsOffice1, tagTreeNode);
                        map.put(tsOffice1.getRid(), temNode);
                    }
                }
            }
        }
        map.clear();
    }

    /**
     * 地区选中事件
     * @param event 选中事件
     */
    public void onNodeSelect(NodeSelectEvent event) {
        TreeNode node = event.getTreeNode();
        TsOffice office = (TsOffice) node.getData();
        userOfficeName=office.getOfficecode()+"　　"+office.getOfficename();
        userOfficeId=office.getRid();
    }

    /**
     * 归并保存
     */
    public void saveRefAction(){
        RequestContext requestContext = RequestContext.getCurrentInstance();
        if(tsOffice.getRid().intValue()==userOfficeId.intValue()){
            JsfUtil.addErrorMessage( "相同科室不能进行归并！");
            return;
        }
        systemService.saveRefOffice(tsOffice.getRid(), userOfficeId, this.sessionData.getUser().getRid());
        this.searchAction();
        requestContext.execute("OfficeRefDialog.hide()");
    }

    /**
     *  归并记录查询
     */
    public void searchRefAction(){
        if(!ifAdmin){
            refunitId=sessionData.getUser().getTsUnit().getRid();
        }
        StringBuilder sb=new StringBuilder(" from TsDeptMdref t where 1=1 ");
        Map refparamMap =new LinkedHashMap();
        if(StringUtils.isNotBlank(this.selRefOffceName)){
            sb.append(" and ( t.tsOfficeOld.officename like :officename");
            sb.append(" or t.tsOfficeNew.officename like :officename )");
           refparamMap.put("officename", "%"+this.selRefOffceName+"%");
        }
        if(null!=refunitId){
            sb.append(" and t.tsOfficeOld.tsUnit.rid=").append(refunitId);
            sb.append(" and t.tsOfficeNew.tsUnit.rid=").append(refunitId);
        }
        String h2 = "select count(*) " + sb.toString();
        String h1 = "select t " + sb.append(" order by t.rid ");
        this.refList = new DefaultLazyDataModel(h1, h2,refparamMap, null, "mainForm:dataRefTable");
    }

    /**
     * 取消归并
     */
    public void cancelRefAction(){
        systemService.cancleRefOffice(tsDeptMdref);
        searchRefAction();searchAction();
    }
	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder(" from TsOffice t  ");
        sb.append(" where t.ifReveal=").append(searchState);
        if(StringUtils.isNotBlank(this.searchOfficeName)) {
            sb.append(" and t.officename like :officename");
            this.paramMap.put("officename", "%"+this.searchOfficeName+"%");
        }
        if(null!=unitId){
            sb.append(" and t.tsUnit.rid=").append(unitId);
        }
		String h2 = "select count(*) " + sb.toString();
		String h1 = "select t " + sb.append(" order by t.tsUnit.rid,t.num ");
		return new String[]{h1,h2};
	}

    /**
     * 结果处理成层级关系
     * @param list 查询出来的结果
     */
    @Override
    public void processData(List<?> list) {
        List<TsOffice> rstlist=(ArrayList<TsOffice>)list;//原数据集
        List<TsOffice> newlist=new LinkedList<TsOffice>();//新数据集
        Map<Integer, TsOffice> map = new HashMap<Integer, TsOffice>();
        if(null!=rstlist&&rstlist.size()>0) {
            for (TsOffice h : rstlist) {
                if (null == h.getParentOffice() || null == h.getParentOffice().getRid() || -1 == h.getParentOffice().getRid()) {
                    //最顶级
                    h.setPaddingLeftSize(1);
                    newlist.add(h);
                    map.put(h.getRid(), h);
                    rstListReset(newlist, rstlist, h.getRid(), 1, map);
                }
            }
            listStart(rstlist, newlist, map);
        }
        list.clear();
        if(null!=newlist&&newlist.size()>0){
            for(TsOffice h :newlist){
                ((ArrayList<TsOffice>) list).add( h);
            }
        }

    }

    /**
     * 进行数据封装（起始节点）
     * @param rstlist
     * @param newlist
     * @param map
     */
    private void listStart( List<TsOffice> rstlist,List<TsOffice> newlist, Map<Integer, TsOffice> map ){

        if(null!=rstlist&&rstlist.size()>0) {
            List<TsOffice> lastList=new LinkedList<TsOffice>();
            if(map.size()<rstlist.size()){
                for (TsOffice h : rstlist) {
                    if(null==map.get(h.getRid())){
                        h.setPaddingLeftSize(1);
                        newlist.add(h);
                        map.put(h.getRid(), h);
                        rstListReset(newlist, rstlist, h.getRid(), 1,map);
                        lastList.add(h);
                    }
                }
                if(null!=lastList&&lastList.size()>0){
                    listStart( lastList,newlist,map);
                }
            }
        }
    }

    /**
     * 数据重新封装（递归）
     */
    private  void rstListReset(List<TsOffice> newList,List<TsOffice> rstList,Integer prantId,int tag, Map<Integer, TsOffice> map){
        tag++;
        boolean hasit=false;
        for(TsOffice h:rstList) {
            if (null!=h.getParentOffice()&&null!= h.getParentOffice().getRid()&&prantId.intValue() == h.getParentOffice().getRid().intValue()) {
                //最顶级
                hasit=true;
                h.setPaddingLeftSize(tag);
                newList.add(h);
                map.put(h.getRid(),h);
                rstListReset(newList, rstList, h.getRid(), tag, map);
            }
        }
        tag--;
        if(!hasit){
            return;
        }
    }

    /**初始化单位列表*/
    private void untiListInnit(List<SelectItem> list){
        List<TsUnit> infolist=systemService.selAllUnitList();
        if(null!=infolist&&infolist.size()>0){
            for(TsUnit h:infolist){
                list.add(new SelectItem(h.getRid(),h.getUnitSimpname()));
            }
        }
    }
    public TsOffice getTsOffice() {
        return tsOffice;
    }

    public void setTsOffice(TsOffice tsOffice) {
        this.tsOffice = tsOffice;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getSearchOfficeName() {
        return searchOfficeName;
    }

    public void setSearchOfficeName(String searchOfficeName) {
        this.searchOfficeName = searchOfficeName;
    }

    public Short getSearchState() {
        return searchState;
    }

    public void setSearchState(Short searchState) {
        this.searchState = searchState;
    }

    public String getIfReveal() {
        return ifReveal;
    }

    public void setIfReveal(String ifReveal) {
        this.ifReveal = ifReveal;
    }

    public TreeNode getTreeNode() {
        return treeNode;
    }

    public void setTreeNode(TreeNode treeNode) {
        this.treeNode = treeNode;
    }

    public TreeNode getSelectedNode() {
        return selectedNode;
    }

    public void setSelectedNode(TreeNode selectedNode) {
        this.selectedNode = selectedNode;
    }

    public String getOldOfficecode() {
        return oldOfficecode;
    }

    public void setOldOfficecode(String oldOfficecode) {
        this.oldOfficecode = oldOfficecode;
    }

    public String getSelRefOffceName() {
        return selRefOffceName;
    }

    public void setSelRefOffceName(String selRefOffceName) {
        this.selRefOffceName = selRefOffceName;
    }

    public DefaultLazyDataModel getRefList() {
        return refList;
    }

    public void setRefList(DefaultLazyDataModel refList) {
        this.refList = refList;
    }

    public TsDeptMdref getTsDeptMdref() {
        return tsDeptMdref;
    }

    public void setTsDeptMdref(TsDeptMdref tsDeptMdref) {
        this.tsDeptMdref = tsDeptMdref;
    }

    public List<SelectItem> getUntiList() {
        return untiList;
    }

    public void setUntiList(List<SelectItem> untiList) {
        this.untiList = untiList;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public boolean isIfAdmin() {
        return ifAdmin;
    }

    public void setIfAdmin(boolean ifAdmin) {
        this.ifAdmin = ifAdmin;
    }

    public List<SelectItem> getUnitEditList() {
        return unitEditList;
    }

    public void setUnitEditList(List<SelectItem> unitEditList) {
        this.unitEditList = unitEditList;
    }

    public Integer getUnitEditRid() {
        return unitEditRid;
    }

    public void setUnitEditRid(Integer unitEditRid) {
        this.unitEditRid = unitEditRid;
    }

    public TreeNode getUpUnitTreeNode() {
        return upUnitTreeNode;
    }

    public void setUpUnitTreeNode(TreeNode upUnitTreeNode) {
        this.upUnitTreeNode = upUnitTreeNode;
    }

    public TreeNode getSelectedUpNote() {
        return selectedUpNote;
    }

    public void setSelectedUpNote(TreeNode selectedUpNote) {
        this.selectedUpNote = selectedUpNote;
    }

    public String getUpOfficeName() {
        return upOfficeName;
    }

    public void setUpOfficeName(String upOfficeName) {
        this.upOfficeName = upOfficeName;
    }

    public Integer getUpOfficeRid() {
        return upOfficeRid;
    }

    public void setUpOfficeRid(Integer upOfficeRid) {
        this.upOfficeRid = upOfficeRid;
    }

    public String getUserOfficeName() {
        return userOfficeName;
    }

    public void setUserOfficeName(String userOfficeName) {
        this.userOfficeName = userOfficeName;
    }

    public Integer getUserOfficeId() {
        return userOfficeId;
    }

    public void setUserOfficeId(Integer userOfficeId) {
        this.userOfficeId = userOfficeId;
    }

    public Integer getRefunitId() {
        return refunitId;
    }

    public void setRefunitId(Integer refunitId) {
        this.refunitId = refunitId;
    }

    public List<SelectItem> getEmpList() {
        return empList;
    }

    public void setEmpList(List<SelectItem> empList) {
        this.empList = empList;
    }

    public Integer getTbSysEmpByDeptLeaderId() {
        return tbSysEmpByDeptLeaderId;
    }

    public void setTbSysEmpByDeptLeaderId(Integer tbSysEmpByDeptLeaderId) {
        this.tbSysEmpByDeptLeaderId = tbSysEmpByDeptLeaderId;
    }

    public Integer getTbSysEmpByManageManid() {
        return tbSysEmpByManageManid;
    }

    public void setTbSysEmpByManageManid(Integer tbSysEmpByManageManid) {
        this.tbSysEmpByManageManid = tbSysEmpByManageManid;
    }

    public List<SelectItem> getEmpList2() {
        return empList2;
    }

    public void setEmpList2(List<SelectItem> empList2) {
        this.empList2 = empList2;
    }
}
