//package com.chis.modules.system.web;
//
//import java.io.File;
//import java.io.IOException;
//
//import javax.servlet.ServletException;
//import javax.servlet.annotation.WebServlet;
//import javax.servlet.http.HttpServlet;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//
//import DBstep.iMsgServer2000;
//
//import com.chis.common.utils.JsfUtil;
//
///**
// * 存在使用漏洞，禁用此方法
// */
//@Deprecated
//@WebServlet(name="OfficeServlet",value="/OfficeServlet")
//public class ZwxFileServlet extends HttpServlet  {
//	private static final long serialVersionUID = -4086220786468535952L;
//    private final static String filePath ;
//    static {
//        String path = JsfUtil.getAbsolutePath();
//        String tempFile = new StringBuilder(path).append("/officeFiles").toString();
//        //路径不存在，则创建路径
//        File dirFile = new File(tempFile);
//        if(!dirFile.exists())    {
//            dirFile.mkdirs();
//        }
//        filePath  = tempFile+"/";
//    }
//
//	@Override
//	protected void doGet(HttpServletRequest req, HttpServletResponse resp)
//			throws ServletException, IOException {
//		doPost(req, resp);
//	}
//
//	@Override
//	protected void doPost(HttpServletRequest req, HttpServletResponse resp)
//			throws ServletException, IOException {
//		String mtd = req.getParameter("mtd");
//        //文件名
//        String fname = req.getParameter("fname");
//		if(null != mtd) {
//			iMsgServer2000 msgObj = new iMsgServer2000();
//			if("load".equals(mtd)) {//加载文件
//				msgObj.Load(req);
//				if(null != fname && !"".equals(fname)) {
//                    fname =  filePath + fname;
//                    File file = new File(fname);
//                    if(file.exists()){//如果文件不存在，则不加载文件
//                    	msgObj.MsgFileLoad(fname);
//                    	msgObj.Send(resp);
//                    }
//				}
//			}else if("savefile".equals(mtd)) {//保存文件
//                fname =  filePath + fname;
//				msgObj.Load(req);
//				msgObj.MsgFileSave(fname);
//			}
//		}
//	}
//
//}
