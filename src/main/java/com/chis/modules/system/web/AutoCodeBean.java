package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsCodeRule;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.interfaces.IAutoCodeService;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * 自动编号规则配置
 *
 * <AUTHOR>
 */
@ManagedBean(name = "autoCodeBean")
@ViewScoped
public class AutoCodeBean extends FacesBean {

    private static final long serialVersionUID = -738445454536625063L;
    /**
     * 存在session中的对象
     */
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    /**
     * ejb session bean
     */
    private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    /**
     * 查询条件：规则编码
     */
    private String searchIdCode;
    /**
     * 查询条件：业务描述
     */
    private String searchBsDesc;
    /**查询条件：系统类型*/
    private Map<String, String> systemTypeMap = new LinkedHashMap<String, String>();
    /**查询条件：系统类型*/
    private String searchSystemType;
    /**查询条件：所有能看到的系统类型comm,risk ，隔开*/
    private String allSystem;
    /**
     * 当前业务系统的自动编号
     */
    private List<TsCodeRule> businessList = new ArrayList<TsCodeRule>(0);
    /**
     * 所有自动编号的实现类
     */
    protected Map<String, String> implClassMap;
    /**
     * 全局bean
     */
    private ApplicationBean applicationBean = (ApplicationBean) getBean("applicationBean");
    /**
     * 查询条件参数，key为参数名称，value为值
     */
    protected Map<String, Object> paramMap = new HashMap<String, Object>();

    private TsCodeRule tsCodeRule = new TsCodeRule();
    /**
     * 测试编码
     */
    private String testCode;

    @PostConstruct
    public void init() {
        this.implClassMap = new HashMap<String, String>();
        Map<String, IAutoCodeService> beansMap = SpringContextHolder.getBeans(IAutoCodeService.class);
        if(null != beansMap && beansMap.size() > 0) {
        	for(IAutoCodeService codeService : beansMap.values()) {
        		this.implClassMap.put(codeService.desciption(), SpringContextHolder.getComponentName(codeService.getClass()));
        	}
        }

        //初始化系统类型，能看到所有的
        TsUserInfo tsUserInfo = this.sessionData.getUser();
        SystemType[] types = SystemType.values();
        for (SystemType st : types) {
            this.systemTypeMap.put(st.getTypeCN(), st.toString());
        }
        this.searchAction();
    }

    /**
     * 查询
     */
    public void searchAction() {
        this.businessList = this.systemModuleService.searchCodeRule(this.buildHqls(), this.paramMap);
    }

    /**
     * 修改初始化
     */
    public void editInitAction() {
        this.testCode = null;
    }

    /**
     * 测试按钮
     *
     * <p>
     *     问题描述： 点击测试按钮时候，前缀异常
     *     解决：直接提取规则中的前缀
     * </p>
     *
     * @MethodReviser pw,2020年10月16日,testAction
     */
    public void testAction() {
        this.testCode = this.commService.getTestAutoCode(this.tsCodeRule, (null == tsCodeRule || StringUtils.isEmpty(tsCodeRule.getPfx())) ? "" : tsCodeRule.getPfx());
    }

    /**
     * 保存
     */
    public void saveAction() {
        this.tsCodeRule.setModifyDate(new Date());
        this.tsCodeRule.setModifyManid(1);
        this.systemModuleService.updateCodeRule(this.tsCodeRule);
        this.searchAction();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.execute("CodeEditDialog.hide();");
    }

    public String buildHqls() {
        this.paramMap.clear();
        StringBuilder sb = new StringBuilder(" select t from TsCodeRule t where 1=1 ");
        if(StringUtils.isNotBlank(this.searchSystemType)) {
            sb.append(" and t.paramType ='").append(this.searchSystemType).append("' ");
        }
        if (StringUtils.isNotBlank(this.searchIdCode)) {
            sb.append(" and t.idcode like :idcode ");
            this.paramMap.put("idcode", "%" + this.searchIdCode.trim() + "%");
        }
        if (StringUtils.isNotBlank(this.searchBsDesc)) {
            sb.append(" and t.bsDesc like :bsDesc ");
            this.paramMap.put("bsDesc", "%" + this.searchBsDesc.trim() + "%");
        }
        return sb.toString();
    }

    //getters and setters
    public Map<String, String> getImplClassMap() {
        return implClassMap;
    }

    public void setImplClassMap(Map<String, String> implClassMap) {
        this.implClassMap = implClassMap;
    }

    public List<TsCodeRule> getBusinessList() {
        return businessList;
    }

    public void setBusinessList(List<TsCodeRule> businessList) {
        this.businessList = businessList;
    }

    public String getSearchBsDesc() {
        return searchBsDesc;
    }

    public void setSearchBsDesc(String searchBsDesc) {
        this.searchBsDesc = searchBsDesc;
    }

    public String getSearchIdCode() {
        return searchIdCode;
    }

    public void setSearchIdCode(String searchIdCode) {
        this.searchIdCode = searchIdCode;
    }

    public TsCodeRule getTsCodeRule() {
        return tsCodeRule;
    }

    public void setTsCodeRule(TsCodeRule tsCodeRule) {
        this.tsCodeRule = tsCodeRule;
    }

    public String getTestCode() {
        return testCode;
    }

    public void setTestCode(String testCode) {
        this.testCode = testCode;
    }

    public String getSearchSystemType() {
        return searchSystemType;
    }

    public void setSearchSystemType(String searchSystemType) {
        this.searchSystemType = searchSystemType;
    }

    public Map<String, String> getSystemTypeMap() {
        return systemTypeMap;
    }

    public void setSystemTypeMap(Map<String, String> systemTypeMap) {
        this.systemTypeMap = systemTypeMap;
    }
}
