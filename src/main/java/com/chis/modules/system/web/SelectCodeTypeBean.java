package com.chis.modules.system.web;

import java.util.*;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;
import org.primefaces.model.DualListModel;

import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.SystemParamType;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * 码表类别选择/码表数据选择
 * Created by zww on 2014-8-12.
 */
@ManagedBean(name = "selectCodeTypeBean")
@ViewScoped
public class SelectCodeTypeBean extends FacesBean {
    /**选择模型*/
    private DualListModel dualListModel = new DualListModel();
    /**是否是超管*/
    private boolean ifAdmin = Boolean.TRUE;
    /**存在session中的对象*/
    protected SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    /**已选的码表IDS，以‘,’隔开，在查询的时候需要过滤掉*/
    private String selectIds;
    /**ejb session bean*/
	private SystemModuleServiceImpl systemService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	/**1,码表类别选择;2,码表数据选择**/
	private String tag;
	/**所有可选码表类型*/
	private List<TsCodeType> typeCodeList;
	/**显示的可选码表类型*/
	private List<TsCodeType> showTypeCodeList;
	/**可选系统类型*/
	private List<SystemType> systemTypeList;
	/**选中的码表**/
	private String selectedCodeTypeId;
	/**选中的系统**/
	private String selectedSysTypeId;
    public SelectCodeTypeBean() {
        TsUserInfo tsUserInfo = this.sessionData.getUser();
        if(!tsUserInfo.getUserNo().equals(Constants.ADMIN)) {
            ifAdmin = Boolean.FALSE; 
        }

        this.selectIds = JsfUtil.getRequest().getParameter("selectIds");
        this.tag=JsfUtil.getRequest().getParameter("tag");
        if("2".equals(tag)){
        	systemTypeList=new ArrayList<SystemType>(0);
        	showTypeCodeList=new ArrayList<TsCodeType>(0);
        	selectedCodeTypeId=null;
        	selectedSysTypeId=null;
        }
        //查询
        this.searchAction();
    }

    /**
     * 查询方法
     * 如果是超管的话，查询出所有的码表类型
     * 不是超管，查出当前登录人所拥有的码表类型
     */
    public void searchAction() {
        if(!"2".equals(tag)){
	    	List<TsCodeType> sourceList = this.systemService.findCodeTypeList(this.ifAdmin, this.sessionData.getUser().getRid());
	        List<TsCodeType> targetList = new ArrayList<TsCodeType>(0);
	        if(StringUtils.isNotBlank(this.selectIds)) {
	            String[] splits = this.selectIds.split(",");
	            for(String s:splits) {
	                for(TsCodeType t: sourceList) {
	                    if(t.getRid().toString().equals(s)) {
	                        targetList.add(t);
	                        continue;
	                    }
	                }
	            }
	            sourceList.removeAll(targetList);
	        }
	        this.dualListModel.setSource(sourceList);
	        this.dualListModel.setTarget(targetList);
        }else{
        	getSimpleCodeData();
        }
        
    }
    /**
     * 查询方法
     * 1,如果是超管的话，查询出所有的码表类型;不是超管，查出当前登录人所拥有的系统类型、码表类型
     * 2,根据选择的码表类型，显示码表数据
     * 
     */
    private void getSimpleCodeData(){
		SystemType[] types = SystemType.values();
		this.systemTypeList.addAll(Arrays.asList(types));
    	typeCodeList=new ArrayList<TsCodeType>(0);
    	typeCodeList=this.systemService.findCodeTypeList(this.ifAdmin, this.sessionData.getUser().getRid());
    	List<TsSimpleCode> simpleCodeList=new ArrayList<TsSimpleCode>(0);
    	if(StringUtils.isNotBlank(this.selectIds)){
    		simpleCodeList=systemService.findCodeListByIds(selectIds);
    	}
    	createSourceAndTatget(simpleCodeList);
    }
    
    
    /**0,遍历Map，生成sourceList
     * 1，在每一个码表数据前插入一个码表类型
     * 2，根据selectIds过滤，将map中simpleCodeList中含有的simpleCode加入targetList,并插入此码表类型
     * 
     **/
    private void createSourceAndTatget(List<TsSimpleCode> selectedList){
    	List<Object[]> sourceList=new ArrayList<Object[]>(0); 
    	List<Object[]> targetList=new ArrayList<Object[]>(0); 
	    		if(null!=selectedList && selectedList.size()>0){
	    			targetList=insertToList(selectedList,targetList);
	    		}
    	  this.dualListModel.setSource(sourceList);
	      this.dualListModel.setTarget(targetList);
	      JsfUtil.addSuccessMessage("请先选择系统类型");
    }
    
    private List<Object[]> insertToList(List<TsSimpleCode> SimpleCodeList,List<Object[]> ObjectsList){
		for(TsSimpleCode t: SimpleCodeList){
				Object[] obj=new Object[5];
    			obj[0]=t.getTsCodeType().getSystemType().getTypeCN();
    			obj[1]=t.getTsCodeType().getCodeTypeDesc();
    			obj[2]=t.getCodeName();
    			obj[3]=t.getCodeLevelNo()==null?0:((t.getCodeLevelNo().split(".")).length);//空格，用于层级
    			obj[4]=t.getRid();//id,码表类型不需要
    			ObjectsList.add(obj);
		}
		return ObjectsList;
    }
    
    
    private Integer isHaveChild(List<TsSimpleCode> list,TsSimpleCode code){
    	Integer index=0;
    	String levelCode=code.getCodeLevelNo()+".";
    	for(TsSimpleCode t:list){
    		if(t.getCodeLevelNo().startsWith(levelCode)){
    			index++;
    		}
    	}
    	return index;
    }
    
    /**
     * 选择确定方法
     */
    public void selectAction() {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("selectedCodeTypeList",this.dualListModel.getTarget());
        RequestContext.getCurrentInstance().closeDialog(map);
    }

    /**
     * 关闭
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

    public void onSysChange(){
    	showTypeCodeList=new ArrayList<TsCodeType>(0);
    	selectedCodeTypeId=null;
    	this.dualListModel.setSource(new ArrayList<Object[]>(0));
    	if(null!=selectedSysTypeId){
    		for(TsCodeType t:typeCodeList){
    			if(selectedSysTypeId.toString().equals(t.getSystemType().getTypeNo().toString())){
    				showTypeCodeList.add(t);
    			}
    		}
    		JsfUtil.addSuccessMessage("请先选择码表类型");
    	}else{
    		JsfUtil.addErrorMessage("请先选择系统类型");
    	}
    }
    
    public void onCodeTypeChange(){
    	if(null!=selectedCodeTypeId){
    		List<Object[]> targetList=this.dualListModel.getTarget();
    		List<TsSimpleCode> sourceSimpleCodeList=new ArrayList<TsSimpleCode>(0);
    		sourceSimpleCodeList=systemService.findCodeListByTypeId(Integer.valueOf(selectedCodeTypeId));
    		List<Object[]> sourceList=new ArrayList<Object[]>(0);
    		sourceList=insertToList(sourceSimpleCodeList,sourceList);
    		if(null!=targetList && targetList.size()>0){
	    		for(Object[] obj:targetList){
	    			for(Object[] o:sourceList){
	    				if(null!=obj[4] && null!=o[4] && ((obj[4].toString()).equals(o[4].toString()))){
	    					sourceList.remove(o);
	    					break;
	    				}
	    			}
	    		}
    		}
    		this.dualListModel.setSource(sourceList);
    	}else{
    		this.dualListModel.setSource(new ArrayList<Object[]>(0));
    	}
    }
    
    //getters and setters
    public String getSelectIds() {
        return selectIds;
    }

    public void setSelectIds(String selectIds) {
        this.selectIds = selectIds;
    }

    public DualListModel getDualListModel() {
        return dualListModel;
    }

    public void setDualListModel(DualListModel dualListModel) {
        this.dualListModel = dualListModel;
    }

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public List<TsCodeType> getTypeCodeList() {
		return typeCodeList;
	}

	public void setTypeCodeList(List<TsCodeType> typeCodeList) {
		this.typeCodeList = typeCodeList;
	}

	public List<TsCodeType> getShowTypeCodeList() {
		return showTypeCodeList;
	}

	public void setShowTypeCodeList(List<TsCodeType> showTypeCodeList) {
		this.showTypeCodeList = showTypeCodeList;
	}

	public List<SystemType> getSystemTypeList() {
		return systemTypeList;
	}

	public void setSystemTypeList(List<SystemType> systemTypeList) {
		this.systemTypeList = systemTypeList;
	}

	public String getSelectedCodeTypeId() {
		return selectedCodeTypeId;
	}

	public void setSelectedCodeTypeId(String selectedCodeTypeId) {
		this.selectedCodeTypeId = selectedCodeTypeId;
	}

	public String getSelectedSysTypeId() {
		return selectedSysTypeId;
	}

	public void setSelectedSysTypeId(String selectedSysTypeId) {
		this.selectedSysTypeId = selectedSysTypeId;
	}
    
    
}
