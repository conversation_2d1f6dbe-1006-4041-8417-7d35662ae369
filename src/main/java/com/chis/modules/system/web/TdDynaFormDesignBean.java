package com.chis.modules.system.web;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.FileUtils;
import com.chis.common.utils.FreeMarkers;
import com.chis.common.utils.JaxbMapper;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.logic.DynaStyleTemplate;
import com.chis.modules.system.entity.TdFormDef;
import com.chis.modules.system.entity.TdFormField;
import com.chis.modules.system.entity.TdFormTable;
import com.chis.modules.system.entity.TdFormType;
import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsRpt;
import com.chis.modules.system.enumn.DynaFieldShowType;
import com.chis.modules.system.enumn.DynaFieldType;
import com.chis.modules.system.enumn.SearchFlag;
import com.chis.modules.system.enumn.SystemParamType;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.DyncFormServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.GenHtmlCodeUtil;
import com.google.common.collect.Lists;

/**
 * 动态表单维护
 * 
 * <AUTHOR>
 * 
 */
@ManagedBean(name = "tdDynaFormDesignBean")
@ViewScoped
public class TdDynaFormDesignBean extends FacesEditBean {

	private static final long serialVersionUID = 8521581962587506423L;
	/** ejb session bean */

	private DyncFormServiceImpl dyncFormServiceImpl = (DyncFormServiceImpl) SpringContextHolder
			.getBean(DyncFormServiceImpl.class);
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);

	// ====================查询条件开始==========================
	/** 流程表单类型Id */
	private Integer formTypeId;
	/** 流程表单集合 */
	private List<TdFormType> formTypeList = new ArrayList<TdFormType>();
	/** 流程表单名称 */
	private String formName;
	/** 状态 */
	private String[] searchState;

	// ====================查询条件结束==========================
	/** 表单Id */
	private Integer formId;
	/** 绑定表单 */
	private TdFormDef bindTdFormDef;
	/** 表单模式集合 */
	private List<SelectItem> formModelList;

	/** 绑定表单类别 */
	private TdFormType bindTdFormType;
	/** 弹出框 表单类型名称 */
	private String formTypeDiag;
	/** 表单类型集合 */
	private List<TdFormType> sourceTypeList;
	private List<TdFormType> showTypeList;

	// 报表模版选择
	/** 系统类型 */
	private Short sysType;
	/** 查询报表名称 */
	private String searchRptName;
	/** 系统类型列表 */
	private Map<String, Short> sysTypeMap;
	private List<TsRpt> sourceRptList;
	private List<TsRpt> showRptList;
	/** 选中报表实体 */
	private TsRpt selRpt;

	// 报表模版选择
	/** 系统类型 */
	private Short sysTypeC;
	/** 查询报表名称 */
	private String searchCodeName;
	private List<TsCodeType> sourceCodeList;
	private List<TsCodeType> showCodeList;
	/** 选中报表实体 */
	private TsCodeType selCode;

	// 主子表集合
	private TdFormTable mTdFormTable;
	private TdFormTable sTdFormTable;

	/** 缓存码表类型集合 */
	private Map<String, TsCodeType> cachMap;

	/** 查询条件：所有能看到的系统类型comm,risk ，隔开 */
	private String allSystem;

	/** +样式选择集合 */
	private List<SelectItem> styleList = null;
	/** +样式选择Id */
	private String styleId = "dynaModelPf.xml";
	private String listStyleId = "dynaModelList.xml";

	private static String DYNA_MODEL_PATH = "/resources/freemarker/system";

	/** 表名 */
	private String tableNameDiag;
	/** 表模式 */
	private String tableModelDiag;
	/** 表单类型集合 */
	private List<Object[]> sourceTableList;
	private List<Object[]> showTableList;
	/** 数据表ID */
	private Integer dynaFormId;
	/** 数据表名称 */
	private String dynaFormName;

	@PostConstruct
	public void init() {
		this.initSearchCondition();
		this.searchAction();
	}

	/**
	 * 初始化样式集合
	 */
	public void initStyleList() {
		styleId = null;
		styleList = Lists.newArrayList();

		String dynaModelPath = FileUtils.getWebRootPath() + DYNA_MODEL_PATH;
		File file = new File(dynaModelPath);
		String[] fns = file.list();
		if (null != fns && fns.length > 0) {
			for (String fileName : fns) {
				StringBuilder filePath = new StringBuilder();
				filePath.append(DYNA_MODEL_PATH).append("/").append(fileName);
				DynaStyleTemplate genDynaObject = this.genDynaObject(filePath.toString());
				if (null != genDynaObject) {
					styleList.add(new SelectItem(fileName, genDynaObject.getName()));
				}
			}
		}
	}

	/**
	 * 展示报表模版类型
	 */
	public void showCodeModel() {
		sysTypeC = null;
		if (null == sysTypeMap || sysTypeMap.size() == 0) {
			sysTypeMap = new LinkedHashMap<String, Short>();
			String paramValue = this.commService.findParamValue(SystemParamType.SYSTEM_MODULES.getTypeNo());
			if (StringUtils.isNotBlank(paramValue)) {
				String[] splits = paramValue.split("##");
				StringBuilder sb = new StringBuilder();
				for (String s : splits) {
					SystemType st = (SystemType) EnumUtils.findEnum(SystemType.class, s);
					this.sysTypeMap.put(st.getTypeCN(), st.getTypeNo());
					sb.append(",'").append(st.getTypeNo().toString()).append("'");
				}
				this.allSystem = sb.toString().substring(1);
			}
		}
		this.findCodeList();
	}

	/**
	 * 清空报表选择
	 */
	public void clearCodeAciton() {
		this.selRpt = null;
	}

	/**
	 * 初始化报表集合
	 */
	private void findCodeList() {
		Integer userId = this.sessionData.getUser().getRid();
		sourceCodeList = this.systemModuleService.findCodeTypeList(false, userId);
		if (null == sourceCodeList) {
			sourceCodeList = new ArrayList<TsCodeType>();
		}
		showCodeList = new ArrayList<TsCodeType>();
		showCodeList.addAll(sourceCodeList);
	}

	public void filterCodeList() {
		showCodeList = new ArrayList<TsCodeType>();
		if (null != sourceCodeList) {
			for (TsCodeType tsCodeType : sourceCodeList) {
				boolean param1Meet = false;
				boolean param2Meet = false;
				if (null == sysTypeC || sysTypeC.toString().equals(tsCodeType.getSystemType().getTypeNo().toString())) {
					param1Meet = true;
				}
				if (StringUtils.isBlank(searchCodeName) || searchCodeName.indexOf(tsCodeType.getCodeTypeDesc()) != -1) {
					param2Meet = true;
				}
				if (param1Meet && param2Meet) {
					showCodeList.add(tsCodeType);
				}
			}
		}
	}

	/**
	 * 展示报表模版类型
	 */
	public void showRptModel() {
		sysType = null;
		if (null == sysTypeMap || sysTypeMap.size() == 0) {
			sysTypeMap = new LinkedHashMap<String, Short>();
			SystemType[] systemTypes = SystemType.values();
			for (SystemType s : systemTypes) {
				sysTypeMap.put(s.getTypeCN(), s.getTypeNo());
			}
		}
		this.findRptList();
	}

	/**
	 * 清空报表选择
	 */
	public void clearRptAciton() {
		this.selRpt = null;
	}

	/**
	 * 初始化报表集合
	 */
	private void findRptList() {
		sourceRptList = this.dyncFormServiceImpl.findTsRpt(allSystem);
		if (null == sourceRptList) {
			sourceRptList = new ArrayList<TsRpt>();
		}
		showRptList = new ArrayList<TsRpt>();
		showRptList.addAll(sourceRptList);
	}

	public void filterRptList() {
		showRptList = new ArrayList<TsRpt>();
		if (null != sourceRptList) {
			for (TsRpt tsRpt : sourceRptList) {
				boolean param1Meet = false;
				boolean param2Meet = false;
				if (null == sysType || sysType.toString().equals(tsRpt.getSystemType().getTypeNo().toString())) {
					param1Meet = true;
				}
				if (StringUtils.isBlank(searchRptName) || searchRptName.indexOf(tsRpt.getRptnam()) != -1) {
					param2Meet = true;
				}
				if (param1Meet && param2Meet) {
					showRptList.add(tsRpt);
				}
			}
		}
	}

	/**
	 * 弹出单选框初始化
	 */
	public void showSingleAction() {
		this.formTypeDiag = null;
		this.searchSingAction();
	}

	/**
	 * 重新查询数据集
	 */
	private void searchSingAction() {
		sourceTypeList = this.dyncFormServiceImpl.findTdFormTypeList();
		if (null == sourceTypeList) {
			sourceTypeList = new ArrayList<TdFormType>();
		}
		showTypeList = new ArrayList<TdFormType>();
		showTypeList.addAll(sourceTypeList);
	}

	/**
	 * 清空弹出框选择
	 */
	public void clearSingleAciton() {
		this.bindTdFormDef.setTdFormTypeByTypeId(null);
	}

	/**
	 * 清空弹出框选择
	 */
	public void clearTableAciton() {
		this.dynaFormId = null;
		this.dynaFormName = null;
		this.bindTdFormDef.setTdFormTableByTableId(null);
	}

	/**
	 * 保存表单类型
	 */
	public void saveSingleAction() {
		String verifyTdFormType = this.dyncFormServiceImpl.verifyTdFormType(bindTdFormType);
		if (StringUtils.isNotBlank(verifyTdFormType)) {
			JsfUtil.addErrorMessage(verifyTdFormType);
		} else {
			this.dyncFormServiceImpl.saveTdFormType(bindTdFormType);
			JsfUtil.addSuccessMessage("保存成功！");
			this.searchSingAction();
			this.filterSingelSel();

			RequestContext currentInstance = RequestContext.getCurrentInstance();
			currentInstance.execute("PF('SingleAddDialog').hide();");
			currentInstance.update("tabView:editForm:singleDatatable");
		}
	}

	/**
	 * 删除表单类型
	 */
	public void deleteSingleAction() {
		String deleteTdFormType = this.dyncFormServiceImpl.deleteTdFormType(this.bindTdFormType.getRid());
		if (StringUtils.isNotBlank(deleteTdFormType)) {
			JsfUtil.addErrorMessage(deleteTdFormType);
			return;
		}
		this.searchSingAction();
		this.filterSingelSel();
	}

	/**
	 * 过滤表单选择
	 */
	public void filterSingelSel() {
		this.showTypeList = new ArrayList<TdFormType>();
		for (TdFormType tdFormType : sourceTypeList) {
			String typeName = tdFormType.getTypeName();
			if (StringUtils.isBlank(formTypeDiag) || typeName.indexOf(formTypeDiag) != -1) {
				this.showTypeList.add(tdFormType);
			}
		}
	}

	/**
	 * 弹出单选框初始化
	 */
	public void showTableAction() {
		this.tableModelDiag = null;
		this.tableNameDiag = null;
		this.searchTableAction();
	}

	/**
	 * 重新查询数据集
	 */
	private void searchTableAction() {
		sourceTableList = this.dyncFormServiceImpl.findAllTableList();
		if (null == sourceTableList) {
			sourceTableList = new ArrayList<Object[]>();
		}
		showTableList = new ArrayList<Object[]>();
		showTableList.addAll(sourceTableList);
	}

	/**
	 * 过滤表选择
	 */
	public void filterTableSel() {
		this.showTableList = new ArrayList<Object[]>();
		for (Object[] objArr : sourceTableList) {
			String enName = StringUtils.objectToString(objArr[0]);
			String model = StringUtils.objectToString(objArr[1]);
			boolean nameFlag = false;
			boolean modelFlag = false;
			if (StringUtils.isBlank(tableNameDiag) || enName.indexOf(tableNameDiag) != -1) {
				nameFlag = true;
			}
			if (StringUtils.isBlank(tableModelDiag) || model.equals(tableModelDiag)) {
				modelFlag = true;
			}
			if (nameFlag && modelFlag) {
				this.showTableList.add(objArr);
			}
		}
	}

	/**
	 * 选中集合
	 */
	public void selTableResult() {
		if (null != this.dynaFormId) {
			this.bindTdFormDef.setTdFormTableByTableId(new TdFormTable(this.dynaFormId));
			TdFormTable find = this.dyncFormServiceImpl.findTdFormTable(this.dynaFormId);
			if (null != find) {
				mTdFormTable = find;
				sTdFormTable = null;
				if (null != find.getChildList() && find.getChildList().size() > 0) {
					sTdFormTable = find.getChildList().get(0);
				}
				this.genHtmlCode(mTdFormTable, sTdFormTable);
				JsfUtil.addSuccessMessage("生成HTML成功！");
			}
		}
	}

	/**
	 * 选中集合
	 */
	public void selSingleResult() {
		this.bindTdFormDef.setTdFormTypeByTypeId(this.bindTdFormType);
	}

	/**
	 * 添加表单类型
	 */
	public void addSingleSel() {
		this.bindTdFormType = new TdFormType();
		this.bindTdFormType.setCreateDate(new Date());
		this.bindTdFormType.setCreateManid(this.sessionData.getUser().getRid());
	}

	/**
	 * 提交记录
	 */
	public void confirmEditRecord(Integer state) {
		if (null != bindTdFormDef) {
			if (state.intValue() == 0) {
				this.dyncFormServiceImpl.commitTdFormDef(bindTdFormDef.getRid(), state);
				this.bindTdFormDef.setState(state);
				JsfUtil.addSuccessMessage("撤销成功！");
			}else if (state.intValue() == 1) {
				if (verifyAction()) {
					// 首先保存记录
					this.saveAction();
					this.dyncFormServiceImpl.commitTdFormDef(bindTdFormDef.getRid(), state);
					this.bindTdFormDef.setState(state);
					JsfUtil.addSuccessMessage("提交成功！");
				}
			}
		}
	}

	/**
	 * 删除方法
	 */
	public void deleteAction() {
		if (null != formId) {
			try{
				this.dyncFormServiceImpl.delTdFormDef(formId);
				JsfUtil.addSuccessMessage("删除成功！");
			}catch(Exception e ){
				e.printStackTrace();
				JsfUtil.addSuccessMessage("该表单已被引用，无法删除！");
			}
		}
	}

	/**
	 * 初始化编辑界面集合
	 */
	private void initEditList() {
		if (null == cachMap || cachMap.size() == 0) {
			cachMap = new HashMap<String, TsCodeType>();
			List<TsCodeType> findCodeTypeList = this.systemModuleService.findCodeTypeList(true, null);
			if (null != findCodeTypeList && findCodeTypeList.size() > 0) {
				for (TsCodeType tsCodeType : findCodeTypeList) {
					cachMap.put(tsCodeType.getCodeTypeName(), tsCodeType);
				}
			}
		}

	}

	@Override
	public void addInit() {
		// 默认添加单表模式
		this.bindTdFormDef = new TdFormDef();
		this.bindTdFormDef.setState(0);
		this.bindTdFormDef.setCreateDate(new Date());
		this.bindTdFormDef.setCreateManid(this.sessionData.getUser().getRid());
		// 根据数据模型加载表
		this.bindTdFormDef.setTdFormTypeByTypeId(new TdFormType());
		this.bindTdFormDef.setTdFormTableByTableId(null);

		selRpt = null;
		this.dynaFormId = null;
		this.dynaFormName = null;
		this.initEditList();
	}

	@Override
	public void viewInit() {
		modInit();
	}

	@Override
	public void modInit() {
		this.selRpt = null;
		this.selCode = null;
		this.dynaFormId = null;
		this.dynaFormName = null;
		// 初始化页面集合
		this.initEditList();
		if (null != this.formId) {
			this.bindTdFormDef = this.dyncFormServiceImpl.findTdFormDef(this.formId);
			
			TdFormTable mTable = this.bindTdFormDef.getTdFormTableByTableId();
			this.dynaFormId = mTable.getRid();
			this.dynaFormName =  new StringBuilder(mTable.getCnName()).append("[").append(mTable.getEnName()).append("]").toString();
			String prtTplCode = this.bindTdFormDef.getPrtTplCode();
			if (StringUtils.isNotBlank(prtTplCode)) {
				this.selRpt = this.dyncFormServiceImpl.findTsRptByCode(prtTplCode);
			}
		}
	}

	@Override
	public void saveAction() {
		if (verifyAction()) {
			this.bindTdFormDef.setPrtTplCode(null != this.selRpt ? this.selRpt.getRptCod() : null);
			dyncFormServiceImpl.saveTdFormDef(bindTdFormDef);
			JsfUtil.addSuccessMessage("保存成功！");
			this.searchAction();
		}
	}

	private boolean verifyAction() {
		TdFormType tdFormType = this.bindTdFormDef.getTdFormTypeByTypeId();
		if (null == tdFormType || null == tdFormType.getRid()) {
			JsfUtil.addErrorMessage("表单类别不允许为空！");
			return false;
		}
		if (StringUtils.isBlank(this.bindTdFormDef.getFormName())) {
			JsfUtil.addErrorMessage("表单名称不允许为空！");
			return false;
		}
		if (  null == this.bindTdFormDef.getTdFormTableByTableId() || null == this.bindTdFormDef.getTdFormTableByTableId().getRid()) {
			JsfUtil.addErrorMessage("数据表不允许为空！");
			return false;
		}
		return true;
	}
	
//	/**
//	 * 生成HTML代码
//	 */
//	public void genHtmlCode() {
//		boolean verifyAction = this.verifyAction();
//		if (verifyAction) {
//			this.genHtmlCode(mTdFormTable, sTdFormTable);
//			JsfUtil.addSuccessMessage("生成HTML成功！");
//			RequestContext currentInstance = RequestContext.getCurrentInstance();
//			currentInstance.execute("PF('ThemeSelect').hide();");
//		}
//	}


	/**
	 * 生成HTML代码
	 */
	public void genHtmlCode(TdFormTable mTdFormTable, TdFormTable sTdFormTable) {
		genFormHtmlCode(mTdFormTable,sTdFormTable);
		List<TdFormField> searchField = genListHtmlCode(mTdFormTable);
		genListHtmlSql(mTdFormTable,searchField);
	}
	
	/**
	 * 生成列表htmlSQL
	 * @param searchField 
	 * @param mTdFormTable
	 *
	 */
	private void genListHtmlSql(TdFormTable formTable, List<TdFormField> searchField) {
		StringBuilder fieldsSql = new StringBuilder("SELECT T.RID");
		StringBuilder tempSql = new StringBuilder("");
		String orderStr = " ORDER BY T.RID";
		StringBuilder sql = new StringBuilder();
		//1单表2主表3子表
//		if(1==formTable.getFormProp()){
		List<TdFormField> fields = formTable.getFieldList();
		//为码表leftJoin使用
		List<String> leftJoinSql = Lists.newArrayList();
		for(TdFormField field:fields){
			if(1!=field.getIsList())
				continue;
			DynaFieldShowType findEnum = (DynaFieldShowType) EnumUtils.findEnum(DynaFieldShowType.class, field.getDataSrc());
			DynaFieldType dynaFieldType = (DynaFieldType) EnumUtils.findEnum(DynaFieldType.class, field.getFdDbtype());
			if(DynaFieldShowType.DATE.getTypeNo().equals(findEnum.getTypeNo())){
				String format = " ,'yyyy-mm-dd') ";
				if (dynaFieldType.getTypeNo().equals(DynaFieldType.TIMESTAMP.getTypeNo())) {
					format = " ,'yyyy-mm-dd hh24:mi:ss') ";
				}
				tempSql.append(",TO_CHAR(T.").append(field.getFdEnname()).append(format);
			}else if (DynaFieldShowType.DICT_SELECT_ONE.getTypeNo().equals(findEnum.getTypeNo())
					&& StringUtils.isNotBlank(field.getCodeTypeNo())){
				String codeIndex = "S"+leftJoinSql.size();
				StringBuilder itemSql = new StringBuilder();
				itemSql.append(" LEFT JOIN TS_SIMPLE_CODE ")
				.append(codeIndex).append(" ON T.").append(field.getFdEnname())
				.append(" = ").append(codeIndex).append(".CODE_NO ")
				.append(" INNER JOIN TS_CODE_TYPE  C").append(codeIndex)
				.append(" ON ").append(codeIndex).append(".CODE_TYPE_ID ")
				.append(" = C").append(codeIndex).append(".RID ")
				.append(" AND C").append(codeIndex).append(".CODE_TYPE_NAME=").append(field.getCodeTypeNo());
				leftJoinSql.add(itemSql.toString());
				tempSql.append(",").append(codeIndex).append(".CODE_NAME AS CODE_NAME_").append(codeIndex);
			}else{
				tempSql.append(",T.").append(field.getFdEnname());
			}
		}
		sql.append(" FROM ").append(formTable.getEnName()).append(" T ");
		for(String itemSql:leftJoinSql){
			sql.append(itemSql);
		}
		StringBuilder searchSql = new StringBuilder(" WHERE 1=1 ");
		for(TdFormField field:searchField){
			//获取查询条件，如果是null的情况下跳过
			searchSql.append(buildSearchSqlFilters(field));
		}
		searchSql.append(" AND T.STATE=1 ");
		String resultSql = fieldsSql.append(tempSql.toString()).append(sql.toString()).append(searchSql.toString()).append(orderStr).toString();
		bindTdFormDef.setSearchSql(resultSql);
	}
	
	/**
	 * 填充sql的过滤条件
	 * @param field
	 * @return
	 */
	private String buildSearchSqlFilters(TdFormField field) {
		//时间格式处理
		String fieldName = "";
		DynaFieldShowType findEnum = (DynaFieldShowType) EnumUtils.findEnum(DynaFieldShowType.class, field.getDataSrc());
		DynaFieldType dynaFieldType = (DynaFieldType) EnumUtils.findEnum(DynaFieldType.class, field.getFdDbtype());
		if(DynaFieldShowType.DATE.getTypeNo().equals(findEnum.getTypeNo())){
			StringBuilder tempSql = new StringBuilder();
			String format = " ,'yyyy-mm-dd') ";
			if (dynaFieldType.getTypeNo().equals(DynaFieldType.TIMESTAMP.getTypeNo())) {
				format = " ,'yyyy-mm-dd hh24:mi:ss') ";
			}
			tempSql.append(" TO_CHAR(T.").append(field.getFdEnname()).append(format);
			fieldName = tempSql.toString();
		}else{
			fieldName = " T."+field.getFdEnname()+" ";
		}
		
		StringBuilder sb = new StringBuilder();
		if(field.getSearchType()==(SearchFlag.GT.getFlag())){
			sb.append(" AND ").append(fieldName).append(SearchFlag.GT.getRealFlag()).append(":")
				.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
		}else if(field.getSearchType()==(SearchFlag.EGT.getFlag())){
			sb.append(" AND ").append(fieldName).append(SearchFlag.EGT.getRealFlag()).append(":")
				.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
		}else if(field.getSearchType()==(SearchFlag.LT.getFlag())){
			sb.append(" AND ").append(fieldName).append(SearchFlag.LT.getRealFlag()).append(":")
				.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
		}else if(field.getSearchType()==(SearchFlag.ELT.getFlag())){
			sb.append(" AND ").append(fieldName).append(SearchFlag.ELT.getRealFlag()).append(":")
				.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
		}else if(field.getSearchType()==(SearchFlag.LIKE.getFlag())){
			sb.append(" AND ").append(fieldName).append(SearchFlag.LIKE.getRealFlag()).append(" '%'||:")
				.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname()).append("||'%' ");
		}else if(field.getSearchType()==(SearchFlag.BETWEEN.getFlag())){
				sb.append(" AND ").append(fieldName).append(SearchFlag.EGT.getRealFlag())
				.append(":").append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
				sb.append(" AND ").append(fieldName).append(SearchFlag.ELT.getRealFlag())
				.append(":").append(GenHtmlCodeUtil.DYNC_PREFIX_NEXT_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
		}else{
			sb.append(" AND ").append(fieldName).append(SearchFlag.EQ.getRealFlag()).append(":")
			.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
		}
		return sb.toString();
	}

	/**
	 * 生成列表html代码
	 * @param mTdFormTable
	 * @return 
	 *
	 */
	private List<TdFormField> genListHtmlCode(TdFormTable mTdFormTable) {
		GenHtmlCodeUtil ghcUtil = new GenHtmlCodeUtil();
		Map<String, Object> model = new HashMap<String, Object>();
		List<TdFormField> mfieldList = mTdFormTable.getFieldList();
		this.sortFieldList(mfieldList);
		//生成查询字段数据
		model.put("gen_search_fields", buildSearchFields(mfieldList));
		model.put("gen_title_fields", buildTitleFields(mfieldList));
		String genFreeMarkerStr = this.genFreeMarkerStr(listStyleId, model);
		bindTdFormDef.setSearchHtml(genFreeMarkerStr);
		return mfieldList;
	}
	
	/**
	 * 创建查询字段集合
	 * @return
	 */
	private List<TdFormField> buildSearchFields(List<TdFormField> fields) {
		List<TdFormField> list = Lists.newArrayList();
		for(TdFormField field:fields){
			if(!new Integer(1).equals(field.getIsSearch()))
				continue;
			GenHtmlCodeUtil gu = new GenHtmlCodeUtil();
			field.setFreeMarkerStr(gu.genSearchHtmlElement(field, true, false, commService,true));
			list.add(field);
		}
		return list;
	}
	
	/**
	 * 创建列表显示字段询字段集合
	 * @return
	 */
	private List<TdFormField> buildTitleFields(List<TdFormField> fields) {
		List<TdFormField> list = Lists.newArrayList();
		for(TdFormField field:fields){
			if(!new Integer(1).equals(field.getIsList()))
				continue;
			list.add(field);
		}
		TdFormField handleField = new TdFormField();
		handleField.setFdCnname("操作");
		list.add(handleField);
		return list;
	}
	
	/**
	 * 生成子html内容
	 * 
	 * @param ghcUtil
	 * @param fieldList
	 * @return
	 */
	private List<TdFormField> genListCode(GenHtmlCodeUtil ghcUtil, List<TdFormField> fieldList) {
		List<TdFormField> showFieldList = new ArrayList<TdFormField>();
		if (null != fieldList && fieldList.size() > 0) {
			for (int i = 0; i < fieldList.size(); i++) {
				TdFormField tdFormField = fieldList.get(i);
				if (tdFormField.getIsShow() != null
						&& (tdFormField.getIsShow().intValue() == 1 || tdFormField.getIsShow().intValue() == 2)) {
					tdFormField.setDataSrc(DynaFieldShowType.LABEL.getTypeNo());
					// 生成列
					String elStr = ghcUtil.genHtmlElement(tdFormField, false, false, commService);
					// 生成字段模版
					tdFormField.setFreeMarkerStr(elStr);
					// 生成模版列
					elStr = ghcUtil.genHtmlElement(tdFormField, false, true, commService);
					// 生成字段模版
					tdFormField.setFreeMarkerModelStr(elStr);
					showFieldList.add(tdFormField);
				}
			}
		}
		return showFieldList;
	}

	/**
	 * 生成表单HTML代码
	 */
	public void genFormHtmlCode(TdFormTable mTdFormTable, TdFormTable sTdFormTable) {
		GenHtmlCodeUtil ghcUtil = new GenHtmlCodeUtil();
		Map<String, Object> model = new HashMap<String, Object>();
		List<TdFormField> mfieldList = mTdFormTable.getFieldList();
		this.sortFieldList(mfieldList);
		List<TdFormField> showList = this.genMainCode(ghcUtil, mfieldList);
		model.put("gen_Main_Fields", showList);
		if (null != mTdFormTable && null == sTdFormTable) {
			String genHtmlScripts = ghcUtil.genHtmlScripts(mfieldList, null,commService);
			model.put("gen_Js", genHtmlScripts);
		} else if (null != mTdFormTable && null != sTdFormTable) {

			List<TdFormField> sFieldList = sTdFormTable.getFieldList();
			// 生成子表模版，需要增加删行 列
			List<TdFormField> sNewFieldList = new ArrayList<TdFormField>();

			TdFormField createOpTypeField = this.createOpTypeField();
			sNewFieldList.add(createOpTypeField);

			List<TdFormField> showSubList = this.genSubCode(ghcUtil, sFieldList);
			sNewFieldList.addAll(showSubList);
			// 生成Js
			String genHtmlScripts = ghcUtil.genHtmlScripts(mfieldList, showSubList,commService);

			model.put("gen_Sub_Fields", sNewFieldList);
			model.put("gen_Js", genHtmlScripts);
		}
		String genFreeMarkerStr = this.genFreeMarkerStr(styleId, model);
		bindTdFormDef.setHtml(genFreeMarkerStr);
	}

	/**
	 * 解析FreeMarker字符串
	 * 
	 * @param fileName
	 * @param model
	 * @return
	 */
	private String genFreeMarkerStr(String fileName, Map<String, Object> model) {
		if (StringUtils.isNotBlank(fileName) && null != model) {
			StringBuilder filePath = new StringBuilder();
			filePath.append(DYNA_MODEL_PATH).append("/").append(fileName);
			DynaStyleTemplate genDynaObject = this.genDynaObject(filePath.toString());
			String content = genDynaObject.getContent();
			String renderString = FreeMarkers.renderString(content, model);
			return renderString;
		}
		return null;
	}

	private DynaStyleTemplate genDynaObject(String filePath) {
		if (StringUtils.isNotBlank(filePath)) {
			String fileContent = FileUtils.getFileContent(filePath.toString());
			DynaStyleTemplate fromXml = JaxbMapper.fromXml(fileContent, DynaStyleTemplate.class);
			return fromXml;
		}
		return null;
	}

	/**
	 * 创建操作列元素
	 * 
	 * @return
	 */
	private TdFormField createOpTypeField() {
		TdFormField subField = new TdFormField();
		subField.setFdCnname("操作");
		subField.setFreeMarkerStr("<a class=\"dyna_href_del\" href=\"javascript:dyna_href_del(${t-1})\">删行</a>");
		subField.setFreeMarkerModelStr("<a class=\"dyna_href_del\" href=\"javascript:dyna_href_del({{row}})\">删行</a>");
		return subField;
	}

	/**
	 * 对字段集合进行排序
	 * 
	 * @param fieldList
	 */
	private void sortFieldList(List<TdFormField> fieldList) {
		if (null != fieldList && fieldList.size() > 0) {
			Collections.sort(fieldList, new Comparator<TdFormField>() {
				@Override
				public int compare(TdFormField o1, TdFormField o2) {
					// 行
					Integer rowNum = o1.getRowNum();
					Integer rowNum2 = o2.getRowNum();
					// 列
					Integer colNum = o1.getColNum();
					Integer colNum2 = o2.getColNum();

					if (rowNum.intValue() == rowNum2.intValue()) {
						return colNum.compareTo(colNum2);
					} else {
						return rowNum.compareTo(rowNum2);
					}
				}
			});
		}
	}

	/**
	 * 生成主表元素
	 * 
	 * @param ghcUtil
	 * @param fieldList
	 * @return
	 */
	private List<TdFormField> genMainCode(GenHtmlCodeUtil ghcUtil, List<TdFormField> fieldList) {
		List<TdFormField> showFieldList = new ArrayList<TdFormField>();
		if (null != fieldList && fieldList.size() > 0) {
			// 临时行
			Integer tempRow = null;
			StringBuilder rowsStr = new StringBuilder();
			for (int i = 0; i < fieldList.size(); i++) {
				TdFormField tdFormField = fieldList.get(i);
				if (tdFormField.getIsShow() != null
						&& (tdFormField.getIsShow().intValue() == 1 || tdFormField.getIsShow().intValue() == 2)) {
					tdFormField.setIfNewLine("0");
					// 行
					Integer rowNum = tdFormField.getRowNum();
					// 如果行为空，或者行不等于当前行，或者为最后一行，则需要将当前行元素组织到HTML上
					if ((null != tempRow && rowNum.intValue() != tempRow.intValue())) {
						if (rowsStr.length() > 0) {
							// 生成行
							rowsStr = new StringBuilder();
							tdFormField.setIfNewLine("1");
						}
					}
					// 生成元素
					String elStr = ghcUtil.genHtmlElement(tdFormField, true, null, commService);
					tdFormField.setFreeMarkerStr(elStr);
					tempRow = rowNum;
					showFieldList.add(tdFormField);
				}
			}
		}
		return showFieldList;
	}

	/**
	 * 生成子html内容
	 * 
	 * @param ghcUtil
	 * @param fieldList
	 * @return
	 */
	private List<TdFormField> genSubCode(GenHtmlCodeUtil ghcUtil, List<TdFormField> fieldList) {
		List<TdFormField> showFieldList = new ArrayList<TdFormField>();
		if (null != fieldList && fieldList.size() > 0) {
			for (int i = 0; i < fieldList.size(); i++) {
				TdFormField tdFormField = fieldList.get(i);
				if (tdFormField.getIsShow() != null
						&& (tdFormField.getIsShow().intValue() == 1 || tdFormField.getIsShow().intValue() == 2)) {
					// 生成列
					String elStr = ghcUtil.genHtmlElement(tdFormField, false, false, commService);
					// 生成字段模版
					tdFormField.setFreeMarkerStr(elStr);
					// 生成模版列
					elStr = ghcUtil.genHtmlElement(tdFormField, false, true, commService);
					// 生成字段模版
					tdFormField.setFreeMarkerModelStr(elStr);
					showFieldList.add(tdFormField);
				}
			}
		}
		return showFieldList;
	}

	@Override
	public String[] buildHqls() {
		StringBuffer searchCommSql = new StringBuffer();
		searchCommSql.append(" FROM TD_FORM_DEF T INNER JOIN TD_FORM_TYPE T1 ON T.TYPE_ID = T1.RID ");
		searchCommSql.append(" INNER JOIN TD_FORM_TABLE T2 ON T2.RID = T.TABLE_ID WHERE 1=1");
		if (null != formTypeId) {
			searchCommSql.append(" AND T.TYPE_ID =").append(formTypeId);
		}
		if (StringUtils.isNotBlank(formName)) {
			searchCommSql.append(" AND T.FORM_NAME LIKE :FORMNAME ESCAPE '\\\'");
			this.paramMap.put("FORMNAME", "%" + StringUtils.convertBFH(formName) + "%");
		}
		if (null != searchState && searchState.length > 0) {
			String states = StringUtils.array2string(searchState, ",");
			searchCommSql.append(" AND T.STATE IN (").append(states).append(")");
		}

		String searchSql = new StringBuffer(
				"SELECT T1.TYPE_NAME,T.FORM_CODE,T.FORM_NAME,T.STATE,T.RID, T2.CN_NAME||'['||T2.EN_NAME||']' ")
				.append(searchCommSql).append(" ORDER BY T1.TYPE_CODE ,T.FORM_CODE").toString();
		String countSql = new StringBuffer("SELECT COUNT(T.RID)").append(searchCommSql).toString();
		return new String[] { searchSql, countSql };
	}

	/**
	 * 初始化查询条件
	 */
	private void initSearchCondition() {
		// 初始化查询条件
		this.formTypeList = this.dyncFormServiceImpl.findFormTypeList();
		this.formTypeId = null;
		this.formName = null;
		this.bindTdFormType = new TdFormType();
		this.ifSQL = true;
	}

	public Integer getFormTypeId() {
		return formTypeId;
	}

	public void setFormTypeId(Integer formTypeId) {
		this.formTypeId = formTypeId;
	}

	public List<TdFormType> getFormTypeList() {
		return formTypeList;
	}

	public void setFormTypeList(List<TdFormType> formTypeList) {
		this.formTypeList = formTypeList;
	}

	public String getFormName() {
		return formName;
	}

	public void setFormName(String formName) {
		this.formName = formName;
	}

	public String[] getSearchState() {
		return searchState;
	}

	public void setSearchState(String[] searchState) {
		this.searchState = searchState;
	}

	public Integer getFormId() {
		return formId;
	}

	public void setFormId(Integer formId) {
		this.formId = formId;
	}

	public TdFormDef getBindTdFormDef() {
		return bindTdFormDef;
	}

	public void setBindTdFormDef(TdFormDef bindTdFormDef) {
		this.bindTdFormDef = bindTdFormDef;
	}

	public TdFormType getBindTdFormType() {
		return bindTdFormType;
	}

	public void setBindTdFormType(TdFormType bindTdFormType) {
		this.bindTdFormType = bindTdFormType;
	}

	public List<SelectItem> getFormModelList() {
		return formModelList;
	}

	public void setFormModelList(List<SelectItem> formModelList) {
		this.formModelList = formModelList;
	}

	public SessionData getSessionData() {
		return sessionData;
	}

	public void setSessionData(SessionData sessionData) {
		this.sessionData = sessionData;
	}

	public String getFormTypeDiag() {
		return formTypeDiag;
	}

	public void setFormTypeDiag(String formTypeDiag) {
		this.formTypeDiag = formTypeDiag;
	}

	public List<TdFormType> getSourceTypeList() {
		return sourceTypeList;
	}

	public void setSourceTypeList(List<TdFormType> sourceTypeList) {
		this.sourceTypeList = sourceTypeList;
	}

	public List<TdFormType> getShowTypeList() {
		return showTypeList;
	}

	public void setShowTypeList(List<TdFormType> showTypeList) {
		this.showTypeList = showTypeList;
	}

	public Short getSysType() {
		return sysType;
	}

	public void setSysType(Short sysType) {
		this.sysType = sysType;
	}

	public String getSearchRptName() {
		return searchRptName;
	}

	public void setSearchRptName(String searchRptName) {
		this.searchRptName = searchRptName;
	}

	public Map<String, Short> getSysTypeMap() {
		return sysTypeMap;
	}

	public void setSysTypeMap(Map<String, Short> sysTypeMap) {
		this.sysTypeMap = sysTypeMap;
	}

	public List<TsRpt> getSourceRptList() {
		return sourceRptList;
	}

	public void setSourceRptList(List<TsRpt> sourceRptList) {
		this.sourceRptList = sourceRptList;
	}

	public List<TsRpt> getShowRptList() {
		return showRptList;
	}

	public void setShowRptList(List<TsRpt> showRptList) {
		this.showRptList = showRptList;
	}

	public TsRpt getSelRpt() {
		return selRpt;
	}

	public void setSelRpt(TsRpt selRpt) {
		this.selRpt = selRpt;
	}

	public Short getSysTypeC() {
		return sysTypeC;
	}

	public void setSysTypeC(Short sysTypeC) {
		this.sysTypeC = sysTypeC;
	}

	public String getSearchCodeName() {
		return searchCodeName;
	}

	public void setSearchCodeName(String searchCodeName) {
		this.searchCodeName = searchCodeName;
	}

	public List<TsCodeType> getSourceCodeList() {
		return sourceCodeList;
	}

	public void setSourceCodeList(List<TsCodeType> sourceCodeList) {
		this.sourceCodeList = sourceCodeList;
	}

	public List<TsCodeType> getShowCodeList() {
		return showCodeList;
	}

	public void setShowCodeList(List<TsCodeType> showCodeList) {
		this.showCodeList = showCodeList;
	}

	public TsCodeType getSelCode() {
		return selCode;
	}

	public void setSelCode(TsCodeType selCode) {
		this.selCode = selCode;
	}

	public TdFormTable getmTdFormTable() {
		return mTdFormTable;
	}

	public void setmTdFormTable(TdFormTable mTdFormTable) {
		this.mTdFormTable = mTdFormTable;
	}

	public TdFormTable getsTdFormTable() {
		return sTdFormTable;
	}

	public void setsTdFormTable(TdFormTable sTdFormTable) {
		this.sTdFormTable = sTdFormTable;
	}

	public List<SelectItem> getStyleList() {
		return styleList;
	}

	public void setStyleList(List<SelectItem> styleList) {
		this.styleList = styleList;
	}

	public String getStyleId() {
		return styleId;
	}

	public void setStyleId(String styleId) {
		this.styleId = styleId;
	}

	public String getTableNameDiag() {
		return tableNameDiag;
	}

	public void setTableNameDiag(String tableNameDiag) {
		this.tableNameDiag = tableNameDiag;
	}

	public String getTableModelDiag() {
		return tableModelDiag;
	}

	public void setTableModelDiag(String tableModelDiag) {
		this.tableModelDiag = tableModelDiag;
	}

	public List<Object[]> getSourceTableList() {
		return sourceTableList;
	}

	public void setSourceTableList(List<Object[]> sourceTableList) {
		this.sourceTableList = sourceTableList;
	}

	public List<Object[]> getShowTableList() {
		return showTableList;
	}

	public void setShowTableList(List<Object[]> showTableList) {
		this.showTableList = showTableList;
	}

	public Integer getDynaFormId() {
		return dynaFormId;
	}

	public void setDynaFormId(Integer dynaFormId) {
		this.dynaFormId = dynaFormId;
	}

	public String getDynaFormName() {
		return dynaFormName;
	}

	public void setDynaFormName(String dynaFormName) {
		this.dynaFormName = dynaFormName;
	}

}
