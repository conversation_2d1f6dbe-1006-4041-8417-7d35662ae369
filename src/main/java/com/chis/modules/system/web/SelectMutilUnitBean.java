package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;
import org.primefaces.model.DualListModel;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.logic.UnitPO;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * <p>类描述：</p>
 * 单位多选
 * @ClassAuthor xq,2018年1月15日,SelectMutilUnitBean
 * 
 * 返回的结果key-selectUnitList value-Collection<TsUnit> height:500 width:920
 * contentWidth:870
 */
@ManagedBean(name = "selectMutilUnitBean")
@ViewScoped
public class SelectMutilUnitBean extends FacesBean {

	private static final long serialVersionUID = 2350275983339647922L;

	/** 标题 */
	private String title = "单位选择";
	/** 已选用户IDS */
	private String selectIds;

	/** 用于缓存初始化的已选单位*/
	private List<UnitPO> storeTargetList;
	/** 是否是超管 */
	private boolean ifAdmin = Boolean.FALSE;
	/** 用户分配：地区名称 */
	private String zoneName;
	/** 用户分配：地区编码 */
	private String zoneCode;
	/** 用户分配：地区级别 */
	private String zoneLevel;
	/** 用户分配：单位id */
//	private Integer userUnitId;
	/** 能看到的地区集合 */
	private List<TsZone> zoneList = new ArrayList<TsZone>(0);
	/** 用于机构授权的选择 */
	private DualListModel unitDualListModel = new DualListModel();


	/** ejb session bean */
	private SystemModuleServiceImpl service = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);

	public SelectMutilUnitBean() {
		this.init();
	}

	/**
	 * 初始化
	 */
	public void init() {
		String s1 = JsfUtil.getRequest().getParameter("title");
		if (StringUtils.isNotBlank(s1)) {
			this.title = s1;
		}
		this.selectIds = JsfUtil.getRequest().getParameter("selectIds");
		TsUserInfo tsUserInfo = sessionData.getUser();

		/**
		 * 地区初始化
		 */
		if (null == this.zoneList || this.zoneList.size() <= 0) {
			this.zoneList = this.commService.findZoneList(this.ifAdmin, tsUserInfo.getTsUnit().getTsZone().getZoneGb(), null, "5");
		}

		this.zoneCode = tsUserInfo.getTsUnit().getTsZone().getZoneGb();
		this.zoneName = tsUserInfo.getTsUnit().getTsZone().getZoneName();
		this.zoneLevel = tsUserInfo.getTsUnit().getTsZone().getZoneType().toString();

		Integer userUnitId = sessionData.getUser().getTsUnit().getRid();
		
		// 初始化用户
		if (StringUtils.isNotBlank(selectIds)) {
			this.storeTargetList = service.findUnitByIds(ifAdmin, zoneCode, Integer.valueOf(zoneLevel), selectIds, userUnitId);
			
		} else {
			this.storeTargetList = new ArrayList<UnitPO>(0);
		}
		this.unitDualListModel = new DualListModel(new ArrayList<TsUnit>(0), this.storeTargetList);
		this.unitDualListModel.setSource(service.findUnitByZone(zoneCode, Integer.valueOf(zoneLevel), unitDualListModel.getTarget()));
	}


	/**
	 * 地区选中事件
	 */
	public void onNodeSelect() {
		this.unitDualListModel.setSource(service.findUnitByZone(zoneCode, Integer.valueOf(zoneLevel), unitDualListModel.getTarget()));
	}



	/**
	 * 选择确定方法
	 */
	public void selectAction() {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("selectUnitList", unitDualListModel.getTarget());
		RequestContext.getCurrentInstance().closeDialog(map);
	}

	/**
	 * 关闭
	 */
	public void dialogClose() {
		RequestContext.getCurrentInstance().closeDialog(null);
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public String getZoneName() {
		return zoneName;
	}

	public void setZoneName(String zoneName) {
		this.zoneName = zoneName;
	}

	public String getZoneCode() {
		return zoneCode;
	}

	public void setZoneCode(String zoneCode) {
		this.zoneCode = zoneCode;
	}

	public String getZoneLevel() {
		return zoneLevel;
	}

	public void setZoneLevel(String zoneLevel) {
		this.zoneLevel = zoneLevel;
	}

	public DualListModel getUnitDualListModel() {
		return unitDualListModel;
	}

	public void setUnitDualListModel(DualListModel unitDualListModel) {
		this.unitDualListModel = unitDualListModel;
	}

}
