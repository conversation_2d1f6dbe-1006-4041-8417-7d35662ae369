package com.chis.modules.system.web;

import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import cn.hutool.core.util.StrUtil;
import com.chis.common.pojo.ZipFilePo;
import com.chis.common.utils.*;
import com.chis.modules.system.entity.*;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.*;
import org.primefaces.model.*;
import org.springframework.util.CollectionUtils;

import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.TdPublishNoticeService;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;

/**
 * <p>类描述：通知发布</p>
 * @ClassAuthor qrr,2021年11月29日,TdPublishNoticeListBean
 * */
@ManagedBean(name="tdPublishNoticeListBean")
@ViewScoped
public class TdPublishNoticeListBean extends FacesEditBean implements IProcessData{
	private static final long serialVersionUID = 1L;
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private SystemModuleServiceImpl sysService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	private TdPublishNoticeService noticeService = SpringContextHolder.getBean(TdPublishNoticeService.class);
	/**查询条件-发布类型*/
	private List<TsSimpleCode> typeList;
	private String selectTypeNames;
	private String selectTypeIds;
	/**查询条件-标题*/
	private String searchTitle;
	/**查询条件：发布日期-开始日期*/
    private Date searchBdate;
    /**查询条件：发布日期-结束日期*/
    private Date searchEdate;
    /**查询条件：选中的状态 */
    private String[] states;
    
    private Integer rid;

    /****************************** 以下通知发布编辑与详情页参数 ↓ *****************************/
    /** 发布类型Map key 码表rid value 码表对象 */
    private Map<Integer,TsSimpleCode> publishTypeMap;
    /** 发布类型树 */
    private TreeNode publishTypeTree;
    /** 选中的发布类型 */
    private TsSimpleCode selectedPublishType;
    /** 选中的发布类型是否其他 */
    private boolean ifOtPublishType;
    /** 通知发布的附件显示列表 */
    private List<TdPublishNoticeAnnex> publishNoticeAnnexList;
    /** 通知发布删除的附件rid集合 */
    private List<Integer> delPublishNoticeAnnexRidList;
    /** 通知发布的通知对象显示列表 */
    private List<TdPublishNoticeUnit> publishNoticeUnitList;
    /** 通知发布删除的通知对象rid集合 */
    private List<Integer> delPublishNoticeUnitRidList;
    /** 当前操作的通知发布对象 */
    private TdPublishNotice curPublishNotice;
    /** 当前操作的附件对象 */
    private TdPublishNoticeAnnex curNoticeAnnex;
    /** 当前操作的通知对象 */
    private TdPublishNoticeUnit curNoticeUnit;
    /** 通知发布附件的下载对象 */
	private StreamedContent noiceAnnexStreamedContent;
	/** 通知对象列表每页显示的数据条数 */
	private int noticeUnitPageSize = 10;
	/** 通知对象弹出框每页显示的数据条数 */
	private int unitSelectPageSize = 10;

	/** 弹出框 当前选中的单位集合 */
	private List<Object[]> selectedUnitObjList;
	/** 弹出框 显示的地区集合 */
	private List<TsZone> publishNoticeShowZoneList;
	/** 弹出框 省级地区集合 */
	private List<TsZone> publishNoticeZoneList;
	/** 弹出框 管辖地区集合 */
	private List<TsZone> publishNoticeManageZoneList;
	/** 弹出框 地区名称 */
	private String noticeZoneName;
	/** 弹出框 地区编码 */
	private String noticeZoneCode;
	/** 弹出框 地区rid */
	private Integer noticeZoneId;
	/** 弹出框 单位属性 注意zwx:SelectManyMenu组件的key与value都需要是String 这里需要有序的 用LinkedHashMap */
	private Map<String, String> bsSortMap = new LinkedHashMap<>(0);
	/** 弹出框 显示的单位 */
	private List<Object[]> showUnitObjList;
	/** 弹出框 单位名称 */
	private String noticeUnitName;
	/** 弹出框 选中单位属性rid拼接的String */
	private String sortSelectRids;
	/** 弹出框 选中单位属性名称拼接的String */
	private String sortSelectNames;
	/** 弹出框 所有选中的单位rid集合 */
	private List<Integer> allSelectedUnitRidList;
	/** 列表中的通知对象对应的单位rid集合 */
	private List<Integer> noticeUnitRidList;

    /****************************** 以上通知发布编辑与详情页参数 ↑ *****************************/

	/** 回执下载对象 */
	private StreamedContent noticeUnitAnnexStreamedContent;
	/** key 单位名称 value 包含单位回执附件名称与路径的集合 */
	private Map<String,List<Object[]>> noticeUnitAnnexMap;

    public TdPublishNoticeListBean(){
    	this.ifSQL = true;
    	init();
		initPublishNoticeDialogData();
    	this.searchAction();
    }
    /**
 	 * <p>方法描述：初始化</p>
 	 * @MethodAuthor qrr,2021年11月29日,init
     * */
    private void init(){
    	this.typeList = commService.findLevelSimpleCodesByTypeId("5546");
    	this.searchEdate = DateUtils.getDateOnly(new Date());
    	this.searchBdate = DateUtils.addYears(searchEdate, -1);
    	//initPublishType有使用typeList initPublishType必须在初始化typeList后
		initPublishType();
    }
    
	@Override
	public void addInit() {
		publishNoticeModInit(true);
	}

	@Override
	public void viewInit() {
		preModOrViewPublishNotice(1);
	}

	@Override
	public void modInit() {
		preModOrViewPublishNotice(0);
	}

	@Override
	public void saveAction() {

	}

	@Override
	public String[] buildHqls() {
		StringBuffer sql = new StringBuffer();
    	sql.append("SELECT t.rid, ")
				.append(" t1.code_path, ")
				.append("t.OTHER_TYPE, ")
				.append("t.TITLE, ")
				.append("t.PUBLISH_DATE, ")
				.append("t.READS, ")
				.append("t.TOTALS, ")
				.append("t.STATE, ")
				.append(" 0, ")
				.append("t1.NUM, ")
				.append("t.IF_NEED_FEEDBACK ");
    	sql.append(" from TD_PUBLISH_NOTICE t ");
    	sql.append(" left join TS_SIMPLE_CODE t1 on t1.rid = t.PUBLISH_TYPE_ID ");
    	sql.append(" where t.PUBLISH_UNIT_ID =").append(Global.getUser().getTsUnit().getRid());
    	//发布类型
		if(StringUtils.isNotBlank(selectTypeIds)){
			sql.append(" AND T.PUBLISH_TYPE_ID in (").append(this.selectTypeIds).append(")");
		}
        //标题
        if (StringUtils.isNotBlank(this.searchTitle)) {
        	sql.append(" AND T.TITLE LIKE :searchTitle  escape '\\\'");
            this.paramMap.put("searchTitle", "%" + StringUtils.convertBFH(this.searchTitle.trim()) + "%");
        }
  		// 发布日期
		if (null != this.searchBdate) {
			sql.append(" AND T.PUBLISH_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
		}
		if (null != this.searchEdate) {
			sql.append(" AND T.PUBLISH_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
		}
		
		//状态
		if (null!=states && states.length==1) {
			sql.append(" AND T.STATE in (").append(this.states[0]).append(")");
		}
    	String h2 = "SELECT COUNT(*) FROM (" + sql.toString() + ")";
        String h1 = "SELECT * FROM (" + sql.toString() + ")AA  ORDER BY AA.PUBLISH_DATE DESC,AA.NUM,AA.TITLE";
        return new String[] { h1, h2 };
	}
	@Override
	public void processData(List<?> list) {
		if (!CollectionUtils.isEmpty(list)) {
			List<Object[]> result =(List<Object[]>)list;
			List<Integer> noticeRidList = new ArrayList<>();
			for (Object[] obj : result) {
				if (null != obj[7] && "1".equals(obj[7].toString()) && null!=obj[5] && null!=obj[6]) {
					BigDecimal val = new BigDecimal(obj[5].toString());
					BigDecimal tol = new BigDecimal(obj[6].toString());
					obj[8] = val.multiply(new BigDecimal(100)).divide(tol, 0, RoundingMode.HALF_UP);
				}

				if(null != obj[10] && "1".equals(obj[10].toString())){
					Integer readNum = null == obj[5] ? null : Integer.parseInt(obj[5].toString());
					Integer tmpRid = null == obj[0] ? null : Integer.parseInt(obj[0].toString());
					if(null != readNum && readNum > 0 && null != tmpRid){
						noticeRidList.add(tmpRid);
					}
				}
			}
			Map<Integer,Integer> noticeAnnexCountMap = CollectionUtils.isEmpty(noticeRidList) ? Collections.EMPTY_MAP :
					this.noticeService.groupFindNoticeUnitAnnexCount(noticeRidList);
			for (Object[] obj : result) {
				Integer tmpRid = null == obj[0] ? null : Integer.parseInt(obj[0].toString());
				Integer count = null == tmpRid ? null : noticeAnnexCountMap.get(tmpRid);
				if(null == count || count < 1){
					obj[10] = 0;
				}
			}
		}
	}
	/**
 	 * <p>方法描述：删除</p>
 	 * @MethodAuthor qrr,2021年11月29日,deleteAction
	 * */
	public void deleteAction() {
		try {
			noticeService.deleteTdPublishNotice(rid);
			JsfUtil.addSuccessMessage("删除成功！");
			this.searchAction();
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("删除失败！");
		}
	}
	/**
 	 * <p>方法描述：取消发布</p>
 	 * @MethodAuthor qrr,2021年11月29日,cancelAction
	 * */
	public void cancelAction() {
		try {
			noticeService.updateTdPublishNotice(rid, 0);
			JsfUtil.addSuccessMessage("取消发布成功！");
			this.searchAction();
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("取消发布失败！");
		}
	}

	/************************************* 以下通知发布编辑与详情区域  ↓*********************************************/

	/**
	 * @Description: 通知发布暂存
	 *
	 * @MethodAuthor pw,2021年12月1日
	 */
	public void savePublishNotice(){
		if(validatePublishNotice(0)){
			return;
		}
		List<String> annexPathList = null;
		try{
			annexPathList = executeSavePublishNotice(0);
			JsfUtil.addSuccessMessage("暂存成功！");
			this.searchAction();
		}catch(Exception e){
			e.printStackTrace();
			annexPathList = null;
			JsfUtil.addErrorMessage("暂存失败！");
		}
		executeDeleteAnnexFile(annexPathList);
	}

	/**
	 * @Description: 通知发布提交
	 *
	 * @MethodAuthor pw,2021年12月1日
	 */
	public void submitPublishNotice(){
		if(validatePublishNotice(1)){
			return;
		}
		List<String> annexPathList = null;
		try{
			annexPathList = executeSavePublishNotice(1);
			JsfUtil.addSuccessMessage("提交成功！");
			this.searchAction();
			this.backAction();
		}catch(Exception e){
			e.printStackTrace();
			annexPathList = null;
			JsfUtil.addErrorMessage("提交失败！");
		}
		executeDeleteAnnexFile(annexPathList);
	}

	/**
	 * @Description: 修改或者详情调用方法
	 *
	 * @MethodAuthor pw,2021年11月30日
	 */
	public void preModOrViewPublishNotice(Integer tag){
		if(null == this.rid){
			JsfUtil.addErrorMessage("rid为空，操作失败！");
			return;
		}
		this.curPublishNotice = this.commService.find(TdPublishNotice.class,this.rid);
		if(null == this.curPublishNotice){
			JsfUtil.addErrorMessage("未找到通知发布对象！");
			return;
		}
		publishNoticeModInit(false);
		//tag 1 详情
		if(null != tag && 1 == tag){
			// 初始化组件缓存问题
			DataTable dataViewTable = (DataTable) FacesContext
					.getCurrentInstance().getViewRoot()
					.findComponent("tabView:viewForm:noticeUnitViewDataTable");
			if(null != dataViewTable){
				dataViewTable.setRows(this.noticeUnitPageSize);
				dataViewTable.setFirst(0);
			}
			dataViewTable = (DataTable) FacesContext
					.getCurrentInstance().getViewRoot()
					.findComponent("tabView:viewForm:noticeAnnexViewDataTable");
			if(null != dataViewTable){
				dataViewTable.setRows(this.noticeUnitPageSize);
				dataViewTable.setFirst(0);
			}
		}else{
		}
	}

	/**
	 * 发布类型树的选择事件
	 *
	 * @param event
	 *            事件对象
	 */
	public void onPublishTypeNodeSelect(NodeSelectEvent event) {
		TreeNode selectNode = event.getTreeNode();
		/** 获得第一级的节点 */
		if (null != selectNode) {
			List<TreeNode> childs = selectNode.getChildren();
			/**
			 * 得到子级节点 无子级节点（最后一级）刷新输入框，并关闭弹出框
			 * */
			if (childs == null || childs.size() == 0) {
				TsSimpleCode t = (TsSimpleCode) selectNode.getData();
				this.selectedPublishType = t;
				if(CollectionUtils.isEmpty(this.publishNoticeUnitList) ||
						compareIfSameFatherCode(t,this.curPublishNotice.getFkByPublishTypeId())){
					this.curPublishNotice.setFkByPublishTypeId(t);
					this.ifOtPublishType = (null != t.getExtendS1() && "1".equals(t.getExtendS1())) ? true : false;
					RequestContext.getCurrentInstance().update("tabView:editForm:publishNoticeHeadId");
					RequestContext.getCurrentInstance().execute("PF('PublishTypePanel').hide()");
				}else{
					RequestContext.getCurrentInstance().execute("PF('NoticeConfirmDialog').show()");
				}
			}
		}
	}

	/**
	 * @Description: 确认修改发布类型
	 *
	 * @MethodAuthor pw,2021年11月30日
	 */
	public void sureChangeCheckType(){
		this.curPublishNotice.setFkByPublishTypeId(this.selectedPublishType);
		this.ifOtPublishType = (null != this.selectedPublishType.getExtendS1() && "1".equals(this.selectedPublishType.getExtendS1())) ? true : false;
		if(!CollectionUtils.isEmpty(this.publishNoticeUnitList)){
			for(TdPublishNoticeUnit noticeUnit : this.publishNoticeUnitList){
				if(null != noticeUnit.getRid()){
					//删除rid集合中加入
					this.delPublishNoticeUnitRidList.add(noticeUnit.getRid());
				}
			}
		}
		//清空通知对象
		this.publishNoticeUnitList.clear();
		this.noticeUnitRidList.clear();
	}

	/**
	 * @Description: 取消修改发布类型
	 *
	 * @MethodAuthor pw,2021年11月30日
	 */
	public void cancelChangeCheckType(){
		this.selectedPublishType = this.curPublishNotice.getFkByPublishTypeId();
	}

	/**
	 * @Description: 附件上传
	 *
	 * @MethodAuthor pw,2021年11月29日
	 */
	public synchronized void uploadPublishNoticeAnnex(FileUploadEvent event){
		if (null != event) {
			UploadedFile file = event.getFile();
			TdPublishNoticeAnnex publishNoticeAnnex = null;
			try {
				List<String> contentList = new ArrayList<>();
				contentList.add("application/pdf");//pdf
				contentList.add("application/msword");//doc
				contentList.add("application/vnd.openxmlformats-officedocument.wordprocessingml.document");//docx
				contentList.add("application/vnd.ms-excel");//xls
				contentList.add("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");//xlsx
				contentList.add("application/zip");//zip/rar
				contentList.add("application/x-zip-compressed");//zip/rar
				contentList.add("application/x-compressed");//zip/rar
				contentList.add("application/octet-stream");//7z
				String fileName = file.getFileName();// 文件名称
				String contentType = file.getContentType().toLowerCase();
				//验证word excel pdf格式
				boolean flag = false;
				if(StringUtils.isBlank(contentType)){
					flag = true;
				}else{
					flag = true;
					for(String str : contentList){
						if(contentType.startsWith(str)){
							flag = false;
						}
					}
				}
				if(flag){
					JsfUtil.addErrorMessage("仅支持doc、docx、xls、xlsx、pdf、zip、rar、7z类型文件！");
					return;
				}
				String suffix = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
				if (StrUtil.equals(suffix, "pdf") && FileUtils.containsJavaScript(file.getInputstream())) {
					JsfUtil.addErrorMessage("文件上传异常！");
					return;
				}
				String uuid = java.util.UUID.randomUUID().toString().replaceAll("-", "");
				String path = JsfUtil.getAbsolutePath();
				//文件夹sysnotice
				String relativePath = new StringBuffer("sysnotice/").append(uuid)
						.append(fileName.substring(fileName.lastIndexOf("."))).toString();
				// 文件路径
				String filePath = new StringBuilder(path).append(relativePath).toString();
				publishNoticeAnnex = new TdPublishNoticeAnnex();
				publishNoticeAnnex.setFkByMainId(this.curPublishNotice);
				publishNoticeAnnex.setAnnexName(fileName);
				publishNoticeAnnex.setAnnexAddr(relativePath);
				FileUtils.copyFile(filePath, file.getInputstream());
			} catch (Exception e) {
				JsfUtil.addErrorMessage("上传失败！");
				e.printStackTrace();
				return;
			}
			if(null != publishNoticeAnnex){
				this.publishNoticeAnnexList.add(publishNoticeAnnex);
				RequestContext.getCurrentInstance().update("tabView:editForm:noticeAnnexPanel");
			}
		}
	}

	/**
	 * @Description: 通知发布附件下载
	 *
	 * @MethodAuthor pw,2021年11月30日
	 */
	public synchronized StreamedContent getNoiceAnnexStreamedContent() {
		if(null == this.curNoticeAnnex){
			return null;
		}
		String filePath = this.curNoticeAnnex.getAnnexAddr();
		String fileName = this.curNoticeAnnex.getAnnexName();
		String contentType = "application/pdf";
		try {
			if(null == filePath){
				filePath = "";
			}
			String xnPath = JsfUtil.getAbsolutePath();
			String path="";
			if(filePath.indexOf(xnPath) != -1){
				path = filePath;
			}else{
				path = xnPath + filePath;
			}
			if(StringUtils.isNotBlank(filePath) && StringUtils.isNotBlank(fileName)){
				if(fileName.endsWith(".doc")){
					contentType = "application/msword";
				}else if(fileName.endsWith(".docx")){
					contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
				}else if(fileName.endsWith(".xls")){
					contentType = "application/vnd.ms-excel";
				}else if(fileName.endsWith(".xlsx")){
					contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
				}
				this.noiceAnnexStreamedContent = new DefaultStreamedContent(new FileInputStream(path),contentType ,
						URLEncoder.encode(fileName, "UTF-8"));
			}else{
				this.noiceAnnexStreamedContent = null;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return noiceAnnexStreamedContent;
	}

	/**
	 * @Description: 删除附件
	 *
	 * @MethodAuthor pw,2021年11月30日
	 */
	public synchronized void removePublishNoticeAnnex(){
		if(null == this.curNoticeAnnex){
			return;
		}
		try{
			Integer annexRid = this.curNoticeAnnex.getRid();
			if(null != annexRid && !this.delPublishNoticeAnnexRidList.contains(annexRid)){
				this.delPublishNoticeAnnexRidList.add(annexRid);
			}
			String filePath = this.curNoticeAnnex.getAnnexAddr();
			//不在数据库存在的记录 直接删除文件
			if(null == annexRid && StringUtils.isNotBlank(filePath)){
				String xnPath = JsfUtil.getAbsolutePath();
				String path="";
				if(filePath.indexOf(xnPath) != -1){
					path = filePath;
				}else{
					path = xnPath + filePath;
				}
				File removeAnnexFile = new File(path);
				//删除文件
				if(null != removeAnnexFile && removeAnnexFile.exists() && removeAnnexFile.isFile()){
					removeAnnexFile.delete();
				}
			}
			//从附件列表移除
			this.publishNoticeAnnexList.remove(this.curNoticeAnnex);
		}catch(Exception e){
			e.printStackTrace();
		}
		RequestContext.getCurrentInstance().update("tabView:editForm:noticeAnnexPanel");
	}

	/**
	 * @Description: 删除 通知对象
	 *
	 * @MethodAuthor pw,2021年12月1日
	 */
	public void removePublishNoticeUnit(){
		if(null == this.curNoticeUnit){
			return;
		}
		if(null != this.curNoticeUnit.getRid()){
			this.delPublishNoticeUnitRidList.add(this.curNoticeUnit.getRid());
		}
		Integer unitRid = null == this.curNoticeUnit.getFkByUnitId() ? null : this.curNoticeUnit.getFkByUnitId().getRid();
		if(null != unitRid){
			this.noticeUnitRidList.remove(unitRid);
		}
		this.publishNoticeUnitList.remove(this.curNoticeUnit);
		RequestContext.getCurrentInstance().update("tabView:editForm:noticeUnitPanel");
	}

	/**
	 * @Description: 通知对象弹出框列表行选中监听，添加元素
	 *
	 * @MethodAuthor pw,2021年12月1日
	 */
	public void rowSelectListener(SelectEvent event){
		Object[] objArr = (Object[])event.getObject();
		Integer unitRid = null != objArr && null != objArr[0] ? Integer.parseInt(objArr[0].toString()) : null;
		if(null != unitRid && !this.allSelectedUnitRidList.contains(unitRid)){
			//记录下已勾选的
			this.allSelectedUnitRidList.add(Integer.parseInt(objArr[0].toString()));
		}
	}

	/**
	 * @Description: 通知对象弹出框列表行取消，移除元素
	 *
	 * @MethodAuthor pw,2021年12月1日
	 */
	public void rowUnselectListener(UnselectEvent event){
		Object[] objArr = (Object[])event.getObject();
		if(null != objArr && null != objArr[0]){
			Integer curSelectUnitRid = Integer.parseInt(objArr[0].toString());
			this.allSelectedUnitRidList.remove(curSelectUnitRid);
		}
	}

	/**
	 * @Description: 通知对象弹出框列表全选或取消全选
	 *
	 * @MethodAuthor pw,2021年12月1日
	 */
	public void toggleSelectListener(ToggleSelectEvent event){
		DataTable dataTable = (DataTable) FacesContext
				.getCurrentInstance().getViewRoot()
				.findComponent("tabView:editForm:publishNoticeUnitDataTable");
		if(CollectionUtils.isEmpty(this.showUnitObjList)){//表格无数据
			return;
		}
		if(event.isSelected()){
			List<Object[]> list = (List)dataTable.getSelection();//当前datatable选中的项
			for(Object[] objArr : list){
				Integer unitRid = null != objArr && null != objArr[0] ? Integer.parseInt(objArr[0].toString()) : null;
				if(null != unitRid && !this.allSelectedUnitRidList.contains(unitRid)){
					this.allSelectedUnitRidList.add(Integer.parseInt(objArr[0].toString()));
				}
			}
		}else{//取消全选，需要移除当前页的所有元素
			int current = dataTable.getPage();//获取当前页码
			int rows = dataTable.getRows();//每页条数
			//遍历当前表格数据，将当前页的数据移除
			int curFirst = current * rows;//当前页第一个元素下标
			int curLast = Math.min(curFirst + rows - 1, this.showUnitObjList.size() - 1);//当前页最后一个元素的下标（取相对小的）
			for (int i = curFirst; i <= curLast; i++) {
				Object[] objArr = this.showUnitObjList.get(i);
				if(null != objArr && null != objArr[0]){
					Integer curSelectUnitRid = Integer.parseInt(objArr[0].toString());
					this.allSelectedUnitRidList.remove(curSelectUnitRid);
				}
			}
		}
	}

	/**
	 * @Description: 通知对象弹出框 弹出前预处理
	 *
	 * @MethodAuthor pw,2021年12月1日
	 */
	public void ifShowPublishNoticeUnitDialog(){
		if(null == this.selectedPublishType || null == this.selectedPublishType.getRid()){
			JsfUtil.addErrorMessage("请先选择发布类型！");
			return;
		}
		Integer extends2 = this.selectedPublishType.getExtendS2();
		if(null == extends2 || (1 != extends2 && 2 != extends2)){
			JsfUtil.addErrorMessage("通过发布类型无法确认地区！");
			return;
		}
		this.publishNoticeShowZoneList = new ArrayList<>();
		this.noticeZoneCode = null;
		this.noticeZoneName = null;
		this.noticeZoneId = null;
		this.sortSelectRids = null;
		this.sortSelectNames = null;
		this.noticeUnitName = null;
		this.showUnitObjList = new ArrayList<>();
		this.selectedUnitObjList = new ArrayList<>();
		this.allSelectedUnitRidList = new ArrayList<>();
		this.unitSelectPageSize = 10;
		DataTable dataTable = (DataTable) FacesContext
				.getCurrentInstance().getViewRoot()
				.findComponent("tabView:editForm:publishNoticeUnitDataTable");
		if(null != dataTable){
			dataTable.setRows(this.unitSelectPageSize);
			dataTable.setFirst(0);
		}
		if(1 == extends2){
			if(!CollectionUtils.isEmpty(this.publishNoticeManageZoneList)){
				this.publishNoticeShowZoneList.addAll(this.publishNoticeManageZoneList);
			}
		}else{
			if(!CollectionUtils.isEmpty(this.publishNoticeZoneList)){
				this.publishNoticeShowZoneList.addAll(this.publishNoticeZoneList);
			}
		}
		if(!CollectionUtils.isEmpty(this.publishNoticeShowZoneList)){
			this.noticeZoneCode = this.publishNoticeShowZoneList.get(0).getZoneGb();
			this.noticeZoneName = this.publishNoticeShowZoneList.get(0).getZoneName();
			this.noticeZoneId = this.publishNoticeShowZoneList.get(0).getRid();
			searchShowUnitObjList();
		}
		RequestContext.getCurrentInstance().update("tabView:editForm:publishNoticeUnitDialog");
		RequestContext.getCurrentInstance().execute("PF('PublishNoticeUnitDialog').show()");
	}

	/**
	 * @Description: 弹出框查询单位列表
	 *
	 * @MethodAuthor pw,2021年11月30日
	 */
	public void searchShowUnitObjList(){
		StringBuffer buffer = new StringBuffer();
		Map<String,Object> searchUnitParamMap = new HashMap<>();
		buffer.append(" SELECT ")
				.append(" T.RID, ")// 单位rid 0
				.append(" CASE WHEN T3.ZONE_TYPE > 2 THEN SUBSTR(T3.FULL_NAME, INSTR(T3.FULL_NAME, '_') + 1) ELSE T3.ZONE_NAME END, ") //显示单位地区 1
				.append(" T.UNITNAME, ")//单位名称2
				.append(" WM_CONCAT(T2.RID), ")//单位属性rid逗号拼接3
				.append(" T3.FULL_NAME, ")//单位全链接地区4
				.append(" T3.ZONE_TYPE, ")//地区类型5
				.append(" T3.ZONE_GB, ")//地区编码6
				.append(" '' ");//空字段
		buffer.append(" FROM TS_UNIT T ")
				.append(" INNER JOIN TS_UNIT_ATTR T1 ON T1.UNIT_RID = T.RID ")
				.append(" INNER JOIN TS_BS_SORT T2 ON T1.ATTR_ID = T2.RID ")
				.append(" LEFT JOIN TS_ZONE T3 ON T.ZONE_ID = T3.RID ");
		buffer.append(" WHERE 1 = 1 ")
				.append(" AND T.IF_REVEAL = 1 ");
		if(StringUtils.isNotBlank(this.noticeZoneCode)) {
			buffer.append(" AND T3.ZONE_GB LIKE :noticeZoneCode escape '\\\'");
			searchUnitParamMap.put("noticeZoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.noticeZoneCode).trim()) + "%");
		}
		if(StringUtils.isNotBlank(this.noticeUnitName)) {
			buffer.append(" AND T.UNITNAME LIKE :noticeUnitName  escape '\\\'");
			searchUnitParamMap.put("noticeUnitName", "%" + StringUtils.convertBFH(this.noticeUnitName.trim()) + "%");
		}
		if(StringUtils.isNotBlank(this.sortSelectRids)){
			buffer.append(" AND T2.RID IN (").append(this.sortSelectRids).append(")");
		}
		buffer.append(" GROUP BY T.RID, ")
				.append(" T3.FULL_NAME, ")
				.append(" T.UNITNAME, ")
				.append(" T3.ZONE_GB, ")
				.append(" T.UNIT_CODE, ")
				.append(" T3.ZONE_TYPE, ")
				.append(" T3.ZONE_NAME ");
		buffer.append(" ORDER BY T3.ZONE_GB, ")
				.append(" T.UNITNAME ");
		this.showUnitObjList = this.commService.findDataBySqlNoPage(buffer.toString(), searchUnitParamMap);
		//删除列表中已经存在的通知对象单位
		if(!CollectionUtils.isEmpty(this.noticeUnitRidList) && !CollectionUtils.isEmpty(this.showUnitObjList)){
			List<Object[]> removeList = new ArrayList<>();
			for(Object[] objArr : this.showUnitObjList){
				Integer unitRid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
				if(null == unitRid || this.noticeUnitRidList.contains(unitRid)){
					removeList.add(objArr);
				}
			}
			if(!CollectionUtils.isEmpty(removeList)){
				this.showUnitObjList.removeAll(removeList);
			}
		}
	}

	/**
	 * @Description: 通知对象弹出框 确认
	 *
	 * @MethodAuthor pw,2021年12月1日
	 */
	public void sureUnitObjSelect(){
		if(CollectionUtils.isEmpty(this.allSelectedUnitRidList)){
			JsfUtil.addErrorMessage("请选择通知对象！");
			return;
		}
		StringBuffer ridBuffer = new StringBuffer();
		List<Integer> tmpList = new ArrayList<>();
		List<TsUnit> unitList = new ArrayList<>();
		for(Integer unitRid : this.allSelectedUnitRidList){
			tmpList.add(unitRid);
			ridBuffer.append(",").append(unitRid);
			if(tmpList.size() > 990){
				List<TsUnit> tmpUnitList = this.commService.findByHql(" from TsUnit t where t.rid in ("+ridBuffer.substring(1)+")", TsUnit.class);
				if(!CollectionUtils.isEmpty(tmpUnitList)){
					unitList.addAll(tmpUnitList);
				}
				tmpList.clear();
				ridBuffer.setLength(0);
			}
		}
		if(ridBuffer.length() > 0){
			List<TsUnit> tmpUnitList = this.commService.findByHql(" from TsUnit t where t.rid in ("+ridBuffer.substring(1)+")", TsUnit.class);
			if(!CollectionUtils.isEmpty(tmpUnitList)){
				unitList.addAll(tmpUnitList);
			}
		}
		if(!CollectionUtils.isEmpty(unitList)){
			for(TsUnit tsUnit : unitList){
				TdPublishNoticeUnit publishNoticeUnit = new TdPublishNoticeUnit();
				publishNoticeUnit.setFkByMainId(this.curPublishNotice);
				publishNoticeUnit.setFkByUnitId(tsUnit);
				publishNoticeUnit.setState(0);
				this.publishNoticeUnitList.add(publishNoticeUnit);
				this.noticeUnitRidList.add(tsUnit.getRid());
			}
		}
		//刷新通知对象列表
		RequestContext.getCurrentInstance().update("tabView:editForm:noticeUnitPanel");
		RequestContext.getCurrentInstance().execute("PF('PublishNoticeUnitDialog').hide()");
	}

	/**
	 * @Description: 通知对象弹出框 全部选择
	 *
	 * @MethodAuthor pw,2021年12月1日
	 */
	public void selectAllUnitObjAction(){
		if(CollectionUtils.isEmpty(this.showUnitObjList)){
			return;
		}
		this.selectedUnitObjList = new ArrayList<>();
		this.selectedUnitObjList.addAll(this.showUnitObjList);
		//不要清空已经选择的列表
		if(null == this.allSelectedUnitRidList){
			this.allSelectedUnitRidList = new ArrayList<>();
		}
		for(Object[] objArr : this.showUnitObjList){
			Integer unitRid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
			if(null != unitRid && !this.allSelectedUnitRidList.contains(unitRid)){
				this.allSelectedUnitRidList.add(unitRid);
			}
		}
		DataTable dataTable = (DataTable) FacesContext
				.getCurrentInstance().getViewRoot()
				.findComponent("tabView:editForm:publishNoticeUnitDataTable");
		if(null != dataTable){
			dataTable.setSelection(this.selectedUnitObjList);
		}

	}

	/**
	 * @Description: 删除附件文件
	 *
	 * @MethodAuthor pw,2021年12月1日
	 */
	private void executeDeleteAnnexFile(List<String> annexPathList){
		if(CollectionUtils.isEmpty(annexPathList)){
			return;
		}
		for(String filePath : annexPathList){
			String xnPath = JsfUtil.getAbsolutePath();
			String path="";
			if(filePath.indexOf(xnPath) != -1){
				path = filePath;
			}else{
				path = xnPath + filePath;
			}
			try{
				File removeAnnexFile = new File(path);
				//删除文件
				if(null != removeAnnexFile && removeAnnexFile.exists() && removeAnnexFile.isFile()){
					removeAnnexFile.delete();
				}
			}catch(Exception e){
				System.out.println("删除文件异常："+path);
			}
		}
	}

	/**
	 * @Description: 执行暂存或者更新
	 *
	 * @MethodAuthor pw,2021年12月1日
	 */
	private List<String> executeSavePublishNotice(Integer tag) throws Exception{
		this.curPublishNotice.setTotals(this.publishNoticeUnitList.size());
		this.curPublishNotice.setPublishDate(new Date());
		this.curPublishNotice.setFkByPublishPsnId(Global.getUser());
		this.curPublishNotice.setFkByPublishUnitId(Global.getUser().getTsUnit());
		String extend1 = null == this.curPublishNotice.getFkByPublishTypeId() ? null :
				this.curPublishNotice.getFkByPublishTypeId().getExtendS1();
		if(StringUtils.isNotBlank(this.curPublishNotice.getOtherType()) && (StringUtils.isBlank(extend1) ||
				!"1".equals(extend1.trim()))){
			this.curPublishNotice.setOtherType(null);
		}
		if(null != tag && tag == 1){
			this.curPublishNotice.setState(1);
		}
		if(!CollectionUtils.isEmpty(this.publishNoticeAnnexList)){
			int size = this.publishNoticeAnnexList.size();
			for(int i=0; i<size; i++){
				TdPublishNoticeAnnex noticeAnnex = this.publishNoticeAnnexList.get(i);
				noticeAnnex.setXh(i+1);
			}
		}
		char ch = 160;
		//处理空格标签异常问题
		if(StringUtils.isNotBlank(this.curPublishNotice.getContent()) &&
				this.curPublishNotice.getContent().indexOf(ch) != -1){
			StringBuffer contextBuffer = new StringBuffer();
			String context = this.curPublishNotice.getContent();
			int length = context.length();
			for(int i=0; i<length;i++){
				if(context.charAt(i) == ch){
					contextBuffer.append("&nbsp;");
				}else{
					contextBuffer.append(context.charAt(i));
				}
			}
			this.curPublishNotice.setContent(contextBuffer.toString());
		}
		return this.noticeService.saveOrUpdatePublishNotice(this.curPublishNotice, this.publishNoticeAnnexList,
				this.delPublishNoticeAnnexRidList, this.publishNoticeUnitList, this.delPublishNoticeUnitRidList);
	}

	/**
	 * @Description: 判断发布类型是否同一大类
	 *
	 * @MethodAuthor pw,2021年11月30日
	 */
	private boolean compareIfSameFatherCode(TsSimpleCode simpleCode , TsSimpleCode typeCode){
		boolean flag = true;
		if(null != simpleCode && null != typeCode){
			String codeLevelLeft = simpleCode.getCodeLevelNo();
			String codeLevelRight = typeCode.getCodeLevelNo();
			if(StringUtils.isBlank(codeLevelLeft) || StringUtils.isBlank(codeLevelRight) ||
					codeLevelLeft.indexOf(".") == -1 || codeLevelRight.indexOf(".") == -1){
				return flag;
			}
			codeLevelLeft = codeLevelLeft.substring(0, codeLevelLeft.indexOf("."));
			if(!codeLevelRight.startsWith(codeLevelLeft+".")){
				flag = false;
			}
		}
		return flag;
	}

	public void viewTopAction(){

	}

	/**
	 * @Description: 添加 修改或者详情的初始化
	 *
	 * @MethodAuthor pw,2021年11月30日
	 */
	private void  publishNoticeModInit(boolean ifAdd){
		if(ifAdd){//添加
			this.curPublishNotice = new TdPublishNotice();
			this.curPublishNotice.setReads(0);
			this.curPublishNotice.setState(0);
			this.curPublishNotice.setIfNeedFeedback(0);
			//注意 提交的时候 更新通知总数 状态 发布时间 发布人以及发布单位
			this.publishNoticeAnnexList = new ArrayList<>();
			this.delPublishNoticeAnnexRidList = new ArrayList<>();
			this.publishNoticeUnitList = new ArrayList<>();
			this.delPublishNoticeUnitRidList = new ArrayList<>();
			this.ifOtPublishType = false;
			this.selectedPublishType = new TsSimpleCode();
			this.noticeUnitRidList = new ArrayList<>();
		}else{
			this.selectedPublishType = this.curPublishNotice.getFkByPublishTypeId();
			if(null == this.selectedPublishType){
				this.selectedPublishType = new TsSimpleCode();
			}
			//通知发布的实体对象在preModOrViewPublishNotice中调用
			this.publishNoticeAnnexList = this.commService.findEntityListByMainId(TdPublishNoticeAnnex.class,this.rid);
			String hql = " SELECT T FROM TdPublishNoticeUnit T WHERE T.fkByMainId.rid = " +this.rid +" ORDER BY T.state,T.fkByUnitId.tsZone.zoneGb,T.fkByUnitId.unitname ";
			this.publishNoticeUnitList = this.commService.findByHql(hql, TdPublishNoticeUnit.class);
			this.noticeUnitRidList = new ArrayList<>();
			if(!CollectionUtils.isEmpty(this.publishNoticeUnitList)){
				for(TdPublishNoticeUnit tdPublishNoticeUnit : this.publishNoticeUnitList){
					Integer unitRid = null == tdPublishNoticeUnit.getFkByUnitId() ? null :
							tdPublishNoticeUnit.getFkByUnitId().getRid();
					if(null != unitRid){
						this.noticeUnitRidList.add(unitRid);
					}
				}
			}
			if(null == this.publishNoticeAnnexList){
				this.publishNoticeAnnexList = new ArrayList<>();
			}
			if(null == this.publishNoticeUnitList){
				this.publishNoticeUnitList = new ArrayList<>();
			}
			if(!CollectionUtils.isEmpty(this.publishNoticeAnnexList)){
				//按序号排序
				Collections.sort(this.publishNoticeAnnexList, new Comparator<TdPublishNoticeAnnex>() {
					@Override
					public int compare(TdPublishNoticeAnnex o1, TdPublishNoticeAnnex o2) {
						Integer xh1 = o1.getXh();
						Integer xh2 = o2.getXh();
						if(null != xh1 && null != xh2){
							return xh1.compareTo(xh2);
						}else if(null == xh1 && null != xh2){
							return -1;
						}else if(null == xh2 && null != xh1){
							return 1;
						}
						return 0;
					}
				});
			}
			this.delPublishNoticeAnnexRidList = new ArrayList<>();
			this.delPublishNoticeUnitRidList = new ArrayList<>();
			this.ifOtPublishType = null != this.curPublishNotice && null != this.curPublishNotice.getFkByPublishTypeId()
					&& "1".equals(this.curPublishNotice.getFkByPublishTypeId().getExtendS1());
		}
		this.noticeUnitPageSize = 10;
		DataTable dataTable = (DataTable) FacesContext
				.getCurrentInstance().getViewRoot()
				.findComponent("tabView:editForm:noticeUnitDataTable");
		if(null != dataTable){
			dataTable.setRows(this.noticeUnitPageSize);
			dataTable.setFirst(0);
		}
		dataTable = (DataTable) FacesContext
				.getCurrentInstance().getViewRoot()
				.findComponent("tabView:editForm:noticeAnnexDataTable");
		if(null != dataTable){
			dataTable.setRows(this.noticeUnitPageSize);
			dataTable.setFirst(0);
		}
	}

	/**
	 * @Description: 初始化通知发布类型
	 *
	 * @MethodAuthor pw,2021年11月29日
	 */
	private void initPublishType() {
		// 初始化发布类型
		this.publishTypeTree = new DefaultTreeNode("root", null);
		this.publishTypeMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(this.typeList)) {
			// 只有第一层
			Set<String> firstLevelNoSet = new LinkedHashSet<String>();
			// 没有第一层
			Set<String> levelNoSet = new LinkedHashSet<String>();
			// 所有类别
			Map<String, TsSimpleCode> menuMap = new HashMap<String, TsSimpleCode>();
			for (TsSimpleCode t : this.typeList) {
				menuMap.put(t.getCodeLevelNo(), t);
				this.publishTypeMap.put(t.getRid(), t);
				if (StringUtils.isNotBlank(t.getCodeLevelNo())) {
					if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
						firstLevelNoSet.add(t.getCodeLevelNo());
					} else {
						levelNoSet.add(t.getCodeLevelNo());
					}
				}
			}
			// 由第一层开始遍历
			for (String ln : firstLevelNoSet) {
				TreeNode node = new DefaultTreeNode(menuMap.get(ln), this.publishTypeTree);
				this.addChildTreeNode(ln, levelNoSet, menuMap, node);
			}
			menuMap.clear();
		}
	}

	/**
	 * @Description: 弹出框数据初始化
	 *
	 * @MethodAuthor pw,2021年11月30日
	 */
	private void initPublishNoticeDialogData(){
		TsUnit tsUnit = Global.getUser().getTsUnit();
		TsZone tsZone = tsUnit.getTsZone();
		this.noticeZoneCode=tsZone.getZoneGb().substring(0,2)+"00000000";
		this.publishNoticeZoneList = this.sysService.findZoneListWithAllZoneByFlag(false,this.noticeZoneCode);
		tsZone = tsUnit.getFkByManagedZoneId();
		if(null != tsZone && null != tsZone.getZoneGb()){
			this.noticeZoneCode=tsZone.getZoneGb();
			this.publishNoticeManageZoneList = this.sysService.findZoneListWithAllZoneByFlag(false,this.noticeZoneCode);
		}
		if(null == this.publishNoticeZoneList){
			this.publishNoticeZoneList = new ArrayList<>();
		}
		if(null == this.publishNoticeManageZoneList){
			this.publishNoticeManageZoneList = new ArrayList<>();
		}

		List<TsBsSort> sortList = this.commService.findSortsByApp();
		if(!CollectionUtils.isEmpty(sortList)) {
			for(TsBsSort t:sortList) {
				this.bsSortMap.put(t.getSortName(), t.getRid().toString());
			}
		}
	}

	/**
	 * 构建类别树
	 *
	 * @param levelNo
	 *            类别层级编码
	 * @param levelNoSet
	 *            二级以及以上的菜单的类别编码集合
	 * @param menuMap
	 *            类别map
	 * @param parentNode
	 *            上级树节点
	 */
	private void addChildTreeNode(String levelNo, Set<String> levelNoSet, Map<String, TsSimpleCode> menuMap,
								  TreeNode parentNode) {
		int level = StringUtils.countMatches(levelNo, ".");
		for (String ln : levelNoSet) {
			if (StringUtils.countMatches(ln, ".") == (level + 1) && StringUtils.startsWith(ln, levelNo + ".")) {
				TreeNode node = new DefaultTreeNode(menuMap.get(ln), parentNode);
				this.addChildTreeNode(ln, levelNoSet, menuMap, node);
			}
		}
	}

	/**
	 * @Description: 通知发布暂存与提交验证
	 *
	 * @MethodAuthor pw,2021年12月1日
	 */
	private boolean validatePublishNotice(Integer tag){
		boolean flag = false;
		if(null == this.curPublishNotice.getFkByPublishTypeId() ||
				null == this.curPublishNotice.getFkByPublishTypeId().getRid()){
			flag = true;
			JsfUtil.addErrorMessage("请选择发布类型！");
		}
		if(StringUtils.isBlank(this.curPublishNotice.getTitle())){
			flag = true;
			JsfUtil.addErrorMessage("标题不允许为空！");
		}
		String extend1 = null == this.curPublishNotice.getFkByPublishTypeId() ? null :
				this.curPublishNotice.getFkByPublishTypeId().getExtendS1();
		if(StringUtils.isBlank(this.curPublishNotice.getOtherType()) && StringUtils.isNotBlank(extend1) &&
				"1".equals(extend1.trim())){
			flag = true;
			JsfUtil.addErrorMessage("其他发布类型不允许为空！");
		}
		if(null != tag && 1 == tag){
			if(null == this.curPublishNotice.getIfNeedFeedback()){
				flag = true;
				JsfUtil.addErrorMessage("请选择是否需要回执！");
			}
			if(CollectionUtils.isEmpty(this.publishNoticeUnitList)){
				flag = true;
				JsfUtil.addErrorMessage("通知对象不允许为空！");
			}
		}
		return flag;
	}

	/**
	 * <p>方法描述： 校验是否有回执附件 </p>
	 * @MethodAuthor： pw 2023/7/19
	 **/
	public void preDownloadNoticeUnitAnnex(){
		this.noticeUnitAnnexMap = this.noticeService.findNoticeUnitAnnexForDownLoad(this.rid);
		if(CollectionUtils.isEmpty(this.noticeUnitAnnexMap)){
			JsfUtil.addErrorMessage("没有通知单位回执附件可下载！");
			return;
		}
		RequestContext.getCurrentInstance().execute("generateClick()");
	}

	/**
	 * <p>方法描述： 下载完成后 删除临时文件 </p>
	 * @MethodAuthor： pw 2023/7/19
	 **/
	public void finishDownloadDelCacheFile(){
		try{
			String zipPath=new StringBuffer(JsfUtil.getAbsolutePath())
					.append("sysnotice/").append("download/")
					.append(Global.getUser().getRid()).append("/回执附件.zip").toString();
			FileUtils.delFile(zipPath);
		}catch(Exception e){
			e.printStackTrace();
		}
	}

	/**
	 * <p>方法描述： 下载回执附件 </p>
	 * @MethodAuthor： pw 2023/7/19
	 **/
	public synchronized StreamedContent getNoticeUnitAnnexStreamedContent() {
		String baseDirPath = new StringBuffer(JsfUtil.getAbsolutePath())
				.append("sysnotice/").append("download/").append(Global.getUser().getRid()).append("/").toString();
		String fileName = "回执附件.zip";
		String zipPath= baseDirPath + fileName;
		String contentType = "application/pdf";
		try {
			//如果已经存在 那么先删除
			FileUtils.delFile(zipPath);
			//如果父文件夹不存在 需要先创建文件夹 否则zipFileCRC32ByPo中会因为缺少文件夹导致拒绝访问的提示
			File baseDir = new File(baseDirPath);
			if(!baseDir.exists()){
				baseDir.mkdirs();
			}
			List<ZipFilePo> zipFilePoList = new ArrayList<>();
			this.copyNoticeUnitAnnexFileToBaseDir(zipFilePoList);
			FileUtils.zipFileCRC32ByPo(zipFilePoList,zipPath);
			this.noticeUnitAnnexStreamedContent = new DefaultStreamedContent(new FileInputStream(zipPath),contentType ,
					URLEncoder.encode(fileName, "UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return noticeUnitAnnexStreamedContent;
	}

	/**
	 * <p>方法描述： 单位回执附件复制到准备压缩的文件夹中 </p>
	 * @MethodAuthor： pw 2023/7/19
	 **/
	private void copyNoticeUnitAnnexFileToBaseDir(List<ZipFilePo> zipFilePoList){
		String xnPath = JsfUtil.getAbsolutePath();
		if(CollectionUtils.isEmpty(this.noticeUnitAnnexMap)){
			return;
		}
		for(Map.Entry<String,List<Object[]>> mapEntity : this.noticeUnitAnnexMap.entrySet()){
			String unitName = mapEntity.getKey();
			ZipFilePo zipFilePo = new ZipFilePo(unitName, null);
			zipFilePoList.add(zipFilePo);
			zipFilePo.setChildList(new ArrayList<ZipFilePo>());
			List<Object[]> unitAnnexArrList = mapEntity.getValue();
			Map<String,Integer> tmpMap = new HashMap<>();
			for(Object[] objArr : unitAnnexArrList){
				String annexName = null == objArr[2] ? null : objArr[2].toString();
				String annexAddr = null == objArr[3] ? null : objArr[3].toString();
				if(StringUtils.isBlank(annexName) || StringUtils.isBlank(annexAddr)){
					continue;
				}
				annexName = this.checkAnnexName(annexName, tmpMap);
				if(annexAddr.indexOf(xnPath) == -1){
					annexAddr = xnPath + annexAddr;
				}
				zipFilePo.getChildList().add(new ZipFilePo(unitName+"/"+annexName, annexAddr));
			}
		}
	}

	/**
	 * <p>方法描述： 避免重名的文件 </p>
	 * @MethodAuthor： pw 2023/7/19
	 **/
	private String checkAnnexName(String annexName, Map<String,Integer> tmpMap){
		if(StringUtils.isBlank(annexName)){
			return null;
		}
		String end = annexName.substring(annexName.lastIndexOf("."));
		annexName = annexName.substring(0,annexName.lastIndexOf("."));
		Integer count = tmpMap.get(annexName);
		if(null == count){
			count = 0;
		}else{
			count++;
		}
		tmpMap.put(annexName, count);
		if(count != 0){
			annexName = annexName+count;
		}
		return annexName+end;
	}

	public void setNoticeUnitAnnexStreamedContent(StreamedContent noticeUnitAnnexStreamedContent) {
		this.noticeUnitAnnexStreamedContent = noticeUnitAnnexStreamedContent;
	}

	/************************************* 以上通知发布编辑与详情区域  ↑*********************************************/

	public List<TsSimpleCode> getTypeList() {
		return typeList;
	}

	public void setTypeList(List<TsSimpleCode> typeList) {
		this.typeList = typeList;
	}

	public String getSelectTypeNames() {
		return selectTypeNames;
	}

	public void setSelectTypeNames(String selectTypeNames) {
		this.selectTypeNames = selectTypeNames;
	}

	public String getSelectTypeIds() {
		return selectTypeIds;
	}

	public void setSelectTypeIds(String selectTypeIds) {
		this.selectTypeIds = selectTypeIds;
	}

	public String getSearchTitle() {
		return searchTitle;
	}

	public void setSearchTitle(String searchTitle) {
		this.searchTitle = searchTitle;
	}

	public Date getSearchBdate() {
		return searchBdate;
	}

	public void setSearchBdate(Date searchBdate) {
		this.searchBdate = searchBdate;
	}

	public Date getSearchEdate() {
		return searchEdate;
	}

	public void setSearchEdate(Date searchEdate) {
		this.searchEdate = searchEdate;
	}

	public String[] getStates() {
		return states;
	}

	public void setStates(String[] states) {
		this.states = states;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public Map<Integer, TsSimpleCode> getPublishTypeMap() {
		return publishTypeMap;
	}

	public void setPublishTypeMap(Map<Integer, TsSimpleCode> publishTypeMap) {
		this.publishTypeMap = publishTypeMap;
	}

	public TreeNode getPublishTypeTree() {
		return publishTypeTree;
	}

	public void setPublishTypeTree(TreeNode publishTypeTree) {
		this.publishTypeTree = publishTypeTree;
	}

	public TsSimpleCode getSelectedPublishType() {
		return selectedPublishType;
	}

	public void setSelectedPublishType(TsSimpleCode selectedPublishType) {
		this.selectedPublishType = selectedPublishType;
	}

	public boolean isIfOtPublishType() {
		return ifOtPublishType;
	}

	public void setIfOtPublishType(boolean ifOtPublishType) {
		this.ifOtPublishType = ifOtPublishType;
	}

	public List<TdPublishNoticeAnnex> getPublishNoticeAnnexList() {
		return publishNoticeAnnexList;
	}

	public void setPublishNoticeAnnexList(List<TdPublishNoticeAnnex> publishNoticeAnnexList) {
		this.publishNoticeAnnexList = publishNoticeAnnexList;
	}

	public List<Integer> getDelPublishNoticeAnnexRidList() {
		return delPublishNoticeAnnexRidList;
	}

	public void setDelPublishNoticeAnnexRidList(List<Integer> delPublishNoticeAnnexRidList) {
		this.delPublishNoticeAnnexRidList = delPublishNoticeAnnexRidList;
	}

	public List<TdPublishNoticeUnit> getPublishNoticeUnitList() {
		return publishNoticeUnitList;
	}

	public void setPublishNoticeUnitList(List<TdPublishNoticeUnit> publishNoticeUnitList) {
		this.publishNoticeUnitList = publishNoticeUnitList;
	}

	public List<Integer> getDelPublishNoticeUnitRidList() {
		return delPublishNoticeUnitRidList;
	}

	public void setDelPublishNoticeUnitRidList(List<Integer> delPublishNoticeUnitRidList) {
		this.delPublishNoticeUnitRidList = delPublishNoticeUnitRidList;
	}

	public TdPublishNotice getCurPublishNotice() {
		return curPublishNotice;
	}

	public void setCurPublishNotice(TdPublishNotice curPublishNotice) {
		this.curPublishNotice = curPublishNotice;
	}

	public TdPublishNoticeAnnex getCurNoticeAnnex() {
		return curNoticeAnnex;
	}

	public void setCurNoticeAnnex(TdPublishNoticeAnnex curNoticeAnnex) {
		this.curNoticeAnnex = curNoticeAnnex;
	}

	public TdPublishNoticeUnit getCurNoticeUnit() {
		return curNoticeUnit;
	}

	public void setCurNoticeUnit(TdPublishNoticeUnit curNoticeUnit) {
		this.curNoticeUnit = curNoticeUnit;
	}

	public void setNoiceAnnexStreamedContent(StreamedContent noiceAnnexStreamedContent) {
		this.noiceAnnexStreamedContent = noiceAnnexStreamedContent;
	}

	public int getNoticeUnitPageSize() {
		return noticeUnitPageSize;
	}

	public void setNoticeUnitPageSize(int noticeUnitPageSize) {
		this.noticeUnitPageSize = noticeUnitPageSize;
	}

	public int getUnitSelectPageSize() {
		return unitSelectPageSize;
	}

	public void setUnitSelectPageSize(int unitSelectPageSize) {
		this.unitSelectPageSize = unitSelectPageSize;
	}

	public List<Object[]> getSelectedUnitObjList() {
		return selectedUnitObjList;
	}

	public void setSelectedUnitObjList(List<Object[]> selectedUnitObjList) {
		this.selectedUnitObjList = selectedUnitObjList;
	}

	public List<TsZone> getPublishNoticeShowZoneList() {
		return publishNoticeShowZoneList;
	}

	public void setPublishNoticeShowZoneList(List<TsZone> publishNoticeShowZoneList) {
		this.publishNoticeShowZoneList = publishNoticeShowZoneList;
	}

	public List<TsZone> getPublishNoticeZoneList() {
		return publishNoticeZoneList;
	}

	public void setPublishNoticeZoneList(List<TsZone> publishNoticeZoneList) {
		this.publishNoticeZoneList = publishNoticeZoneList;
	}

	public List<TsZone> getPublishNoticeManageZoneList() {
		return publishNoticeManageZoneList;
	}

	public void setPublishNoticeManageZoneList(List<TsZone> publishNoticeManageZoneList) {
		this.publishNoticeManageZoneList = publishNoticeManageZoneList;
	}

	public String getNoticeZoneName() {
		return noticeZoneName;
	}

	public void setNoticeZoneName(String noticeZoneName) {
		this.noticeZoneName = noticeZoneName;
	}

	public String getNoticeZoneCode() {
		return noticeZoneCode;
	}

	public void setNoticeZoneCode(String noticeZoneCode) {
		this.noticeZoneCode = noticeZoneCode;
	}

	public Map<String, String> getBsSortMap() {
		return bsSortMap;
	}

	public void setBsSortMap(Map<String, String> bsSortMap) {
		this.bsSortMap = bsSortMap;
	}

	public List<Object[]> getShowUnitObjList() {
		return showUnitObjList;
	}

	public void setShowUnitObjList(List<Object[]> showUnitObjList) {
		this.showUnitObjList = showUnitObjList;
	}

	public String getNoticeUnitName() {
		return noticeUnitName;
	}

	public void setNoticeUnitName(String noticeUnitName) {
		this.noticeUnitName = noticeUnitName;
	}

	public String getSortSelectRids() {
		return sortSelectRids;
	}

	public void setSortSelectRids(String sortSelectRids) {
		this.sortSelectRids = sortSelectRids;
	}

	public String getSortSelectNames() {
		return sortSelectNames;
	}

	public void setSortSelectNames(String sortSelectNames) {
		this.sortSelectNames = sortSelectNames;
	}

	public List<Integer> getAllSelectedUnitRidList() {
		return allSelectedUnitRidList;
	}

	public void setAllSelectedUnitRidList(List<Integer> allSelectedUnitRidList) {
		this.allSelectedUnitRidList = allSelectedUnitRidList;
	}

	public List<Integer> getNoticeUnitRidList() {
		return noticeUnitRidList;
	}

	public void setNoticeUnitRidList(List<Integer> noticeUnitRidList) {
		this.noticeUnitRidList = noticeUnitRidList;
	}

    public Integer getNoticeZoneId() {
        return noticeZoneId;
    }

    public void setNoticeZoneId(Integer noticeZoneId) {
        this.noticeZoneId = noticeZoneId;
    }
}
