package com.chis.modules.system.web;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.*;
import com.chis.modules.system.entity.*;
import com.chis.modules.system.enumn.QuestType;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.ExampoolNumFillBean;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.ProConfigFactor;
import com.chis.modules.system.service.ProConfigFactorFactory;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.google.common.collect.Lists;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * 题库管理Bean
 * */
@ManagedBean(name = "examPoolBean")
@ViewScoped
public class ExampoolBean extends FacesEditBean implements IProcessData {

	private static final long serialVersionUID = -4879486342008674108L;
	/** session bean */
	private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commServiceImpl = SpringContextHolder.getBean(CommServiceImpl.class);
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);

	/*** 题库 **/
	private TsProbExampool tsProbExampool;
	/*** 选项 **/
	private TsProPoolOpt tsProPoolOpt;
	/** 数字填空题 */
	private ExampoolNumFillBean numFill;
	/****/
	private List<ExampoolNumFillBean> numFillList;
	/** 选项数字填空 **/
	private List<ExampoolNumFillBean> optNumFillList;
	// 查询条件

	/** 题型 **/
	private Integer searchQusetType;
	/** 题目类型(标签) **/
	private String searchProTypeName;
	private Integer searchTsProbExamtypeId;
	/** 题目描述 **/
	private String searchTitleDesc;
	/** 状态 **/
	private Integer[] searchState;

	// 参数
	/** 添加子题目时传递层级编码 **/
	private String qesLevelCode = null;
	/****/
	private Integer rid = null;
	/** 题目树 */
	private TreeNode treeNode;
	/** 问卷类别编码 */
	private static String QUE_TYPE_NO = "13001";
	/** 编辑标签树 */
	private TreeNode tsProbExamtypeTree;
	/** 主页面标签树 */
	private TreeNode searchTsProbExamtypeTree;
	/** 选中的标签树节点 ***/
	private TreeNode selectedProTypeNode;
	/** 选中的标签树节点(主页面) ***/
	private TreeNode selectedSearchProTypeNode;
	/** 题库类型名 **/
	private String tsProbExamtypeName;
	/** 题库rid **/
	private Integer tsProbExamtypeId;
	/** 题库层级编码 **/
	private String searchTsProbExamLevelNp;
	/** 题型列表 **/
	private List<QuestType> questTypeList;
	/** 题目类型列表 **/
	private List<SelectItem> proTypeList;
	/** 图片标记 1：描述图片;2：题目附加图片 */
	private Integer uploadTag = null;
	/** 选项模版 */
	private List<TsProTempl> templList;

	/*** 批量选项生成内容 */
	private String templOpts;
	/*** 选中的码表 */
	private String selectedCodeType;
	/*** 选中的系统类型 */
	private Integer selecedSystemType;
	/*** 批量添加码表系统类型列表 */
	private List<SelectItem> systemTypes = new ArrayList<SelectItem>();
	/*** 批量添加码表 */
	private List<SelectItem> codeTypeList = new ArrayList<SelectItem>(0);
	/*** 批量添加码表时显示列表 */
	private List<TsSimpleCode> simpleCodeList = new ArrayList<TsSimpleCode>();
	/** 题目跳转脚本 **/
	private String jumpScript;
	/** 存放选项序号 **/
	private Integer num;
	/** 存放选项值 **/
	private String optValue;
	/** 危险因素编号 **/
	private Integer factorId;
	/** 动作id **/
	private Integer actType;
	/** 是否有父级题目 **/
	private boolean ifHaveFather;
	/** 父题目描述 **/
	private String fatherTitleDesc;
	/** 父级选项列表 **/
	private List<SelectItem> fatherNumList = new ArrayList<SelectItem>();
	/** 子题目依赖父题目选项 **/
	private List<String> dependNum;
	/** 被子题目依赖父题目选项集合 **/
	private List<String> dependNumList;
	/** 是否显示规则配置 **/
	private Integer ifshowConfig = 0;
	/** 选项修改时保存选项修改前的临时变量 **/
	private TsProPoolOpt tempTsProPoolOpt = new TsProPoolOpt();
	/** 行标题 */
	private TbProPoolRowtitle rowtitle = new TbProPoolRowtitle();
	/** 列定义 */
	private TbProPoolColsdefine colsdefine = new TbProPoolColsdefine();
	/** 列定义 */
	private TbProPoolColsdefine tempColsdefine;
	/*** 选项图片参数 ***/
	private String optImg;
	// 根据当前系统，获得对象脚本实现类
	private ProConfigFactor pcf = ProConfigFactorFactory.getProConfigFactor();
	/** 授权问卷类型的种类 **/
	private List<TsSimpleCode> queTypeList;
	/** 依赖题目下拉集合 */
	private List<TsProbExampool> relQueList;
	/** 选中的依赖题目编码 */
	private String selectedRelQueCode;
	/** 选中依赖题目选项集合 */
	private List<SelectItem> relQueOptList;
	/** 选中的依赖题目选项 */
	private List<String> selectedRelQueOpts = Lists.newArrayList();

	/** 附件上传1，文件上传0 */
	private Integer isImg;
	/**5590 难易程度*/
	private List<TsSimpleCode> hardLevelList;
	private Map<Integer,String> hardLevelMap;


	/** 初始化bean */
	public ExampoolBean() {
		/** 加载列表 */
		questTypeList = new ArrayList<QuestType>();
		templList=new ArrayList<>();
		List<Integer> excludeTypeNo = QuestType.getExcludeTypeNo();
		for (QuestType qt : QuestType.values()) {
			if (null == qt.getTypeNo() || excludeTypeNo.contains(qt.getTypeNo())) {
				continue;
			}
			questTypeList.add(qt);
		}
		sortquestTypeList(questTypeList);
		if (pcf != null) {
			ifshowConfig = 1;
		}
		templList = systemModuleService.findProTempls();
		queTypeList = this.commServiceImpl.findSimpleCodesByTypeId(QUE_TYPE_NO);
		this.hardLevelList = new ArrayList<>();
		this.hardLevelMap = new HashMap<>();
		this.hardLevelList = this.commServiceImpl.findNumSimpleCodesByTypeId("5590");
		if(!CollectionUtils.isEmpty(this.hardLevelList)){
			for(TsSimpleCode t:this.hardLevelList){
				this.hardLevelMap.put(t.getRid(),t.getExtendS1());
			}
		}
		initCodeTypeList();
		initSystemType();
		initTsProbExamtypeTree();
		initSearchTsProbExamtypeTree();
		tsProbExamtypeId = null;
		tsProbExamtypeName = null;
		/** 初始化查询条件 */
		ininSearchCondition();
		this.searchAction();

	}

	/** 初始化查询条件 **/
	public void ininSearchCondition() {
		this.searchProTypeName = null;
		this.searchTitleDesc = null;
		this.searchQusetType = null;
		this.searchState = null;
		this.searchTsProbExamtypeId = null;
		this.searchTsProbExamLevelNp = null;
	}

	/**
	 * 初始化码表
	 * 
	 * 修订内容：增加系统类别传染病，码表名称增加系统类别显示
	 * 
	 * @MethodReviser rj,2017年11月6日,initCodeTypeList
	 * 
	 **/
	public void initCodeTypeList() {
		// 将显示的码表清空
		simpleCodeList = new ArrayList<TsSimpleCode>();
		codeTypeList = new ArrayList<SelectItem>();
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT T.CODE_TYPE_NAME,T.CODE_TYPE_DESC,T.PARAM_TYPE FROM TS_CODE_TYPE T WHERE 1=1 ");
		sb.append(" AND T.PARAM_TYPE =0 ");
		List<Object[]> result = commServiceImpl.findDataBySqlNoPage(sb.toString(), null);
		for (Object[] obj : result) {
			switch (obj[2].toString()) {
			case "0":
				codeTypeList.add(new SelectItem(obj[0], obj[1].toString() + "【通用】"));
				break;
			default:
				break;
			}

		}
	}

	/** 初始化批量添加系统类型列表 */
	public void initSystemType() {
		SystemType[] types = SystemType.values();
		for (SystemType type : types) {
			systemTypes.add(new SelectItem(type.getTypeNo(), type.getTypeCN()));
		}
	}

	/** 初始化标签树 */
	private void initTsProbExamtypeTree() {
		// 根据当前登录人的角色，获得授权的题库类型数据
		this.tsProbExamtypeTree = new DefaultTreeNode("root", null);// 根节点
		// 数据总map
		if (null != queTypeList && queTypeList.size() > 0) {
			Map<String, TreeNode> treeMap = new LinkedHashMap<String, TreeNode>();
			for (TsSimpleCode t : queTypeList) {
				if(StringUtils.isBlank(t.getCodeLevelNo())){
					continue;
				}
				if (t.getCodeLevelNo().indexOf(".") == -1) {
					TreeNode node = new DefaultTreeNode(t, tsProbExamtypeTree);
					treeMap.put(t.getCodeLevelNo(), node);
				}
			}
			for (TsSimpleCode t : queTypeList) {
				if(StringUtils.isBlank(t.getCodeLevelNo())){
					continue;
				}
				if (t.getCodeLevelNo().indexOf(".") != -1) {
					// 有上级，找到层级编码中最后一个.之前的字符串对应的编码，将其设为父节点
					String subCode = t.getCodeLevelNo().substring(0, t.getCodeLevelNo().lastIndexOf("."));
					// 根据截取的层级编码找到
					TreeNode node = treeMap.get(subCode);
					TreeNode parentNode = null;
					if (null != node) {
						parentNode = new DefaultTreeNode(t, node);
					} else {
						parentNode = new DefaultTreeNode(t, tsProbExamtypeTree);
					}
					treeMap.put(t.getCodeLevelNo(), parentNode);
				}
			}
		}
	}

	/** 初始化查询标签树 */
	private void initSearchTsProbExamtypeTree() {
		this.searchTsProbExamtypeTree = new DefaultTreeNode("root", null);// 根节点
		// 数据总map
		if (null != queTypeList && queTypeList.size() > 0) {
			Map<String, TreeNode> treeMap = new LinkedHashMap<String, TreeNode>();
			for (TsSimpleCode t : queTypeList) {
				if(StringUtils.isBlank(t.getCodeLevelNo())){
					continue;
				}
				if (t.getCodeLevelNo().indexOf(".") == -1) {
					TreeNode node = new DefaultTreeNode(t, searchTsProbExamtypeTree);
					treeMap.put(t.getCodeLevelNo(), node);
				}
			}
			for (TsSimpleCode t : queTypeList) {
				if(StringUtils.isBlank(t.getCodeLevelNo())){
					continue;
				}
				if (t.getCodeLevelNo().indexOf(".") != -1) {
					// 有上级，找到层级编码中最后一个.之前的字符串对应的编码，将其设为父节点
					String subCode = t.getCodeLevelNo().substring(0, t.getCodeLevelNo().lastIndexOf("."));
					// 根据截取的层级编码找到
					TreeNode node = treeMap.get(subCode);
					TreeNode parentNode = null;
					if (null != node) {
						parentNode = new DefaultTreeNode(t, node);
					} else {
						parentNode = new DefaultTreeNode(t, searchTsProbExamtypeTree);
					}
					treeMap.put(t.getCodeLevelNo(), parentNode);
				}
			}
		}
	}

	/**
	 * 标签树选中事件
	 * 
	 * @param event
	 *            选中事件
	 */
	public void onProtypeNodeSelect(NodeSelectEvent event) {
		TreeNode node = event.getTreeNode();
		TsSimpleCode t = (TsSimpleCode) node.getData();
		tsProbExamtypeName = t.getCodeName();
		tsProbExamtypeId = t.getRid();
        relQueList = Lists.newArrayList();
        selectedRelQueCode = null;
        tsProbExampool.setJumpType(0);

	}

	/**
	 * 主页面标签树选中事件
	 * 
	 * @param event
	 *            选中事件
	 */
	public void onSearchProtypeNodeSelect(NodeSelectEvent event) {
		TreeNode node = event.getTreeNode();
		TsSimpleCode t = (TsSimpleCode) node.getData();
		searchProTypeName = t.getCodeName();
		searchTsProbExamtypeId = t.getRid();
		searchTsProbExamLevelNp = t.getCodeLevelNo();
	}

	/**  预览附件按钮事件*/
	public void previewAction(){
		String url = tsProbExampool.getAnnexAddr();
		if(StringUtils.isNotBlank(url)){
			StringBuilder urlBuilder = new StringBuilder();
			urlBuilder.append("preview('").append(url).append("')");
			RequestContext.getCurrentInstance().execute(urlBuilder.toString());
		}
	}

	/**
	 * 删除按钮事件
	 */
	public void deleteAnnexAddrAction(){
		tsProbExampool.setAnnexAddr(null);
	}

	/** 清空标签树 */
	public void clearSearchNodeSelected() {
		searchTsProbExamLevelNp = null;
		searchProTypeName = null;
		searchTsProbExamtypeId = null;
		selectedSearchProTypeNode = null;
	}

	/** 清空标签树 */
	public void clearNodeSelected() {
		tsProbExamtypeName = null;
		tsProbExamtypeId = null;
		selectedProTypeNode = null;
	}

	/** 题目启用、停用 **/
	public void changeStateAction() {
		systemModuleService.changeTsProbExampoolStateByCode(qesLevelCode, tsProbExampool.getState());
		qesLevelCode = null;
		searchAction();
	}

	/** 题目新增 */
	@Override
	public void addInit() {
		// 设置创建人、编码、层级编码
		this.ifHaveFather = false;
		if (rid != null) {// 新增子题目
			tsProbExampool = systemModuleService.findTsProbExampoolByRid(rid);
			this.tsProbExamtypeId = tsProbExampool.getTsSimpleCodeByTypeId().getRid();
			this.tsProbExamtypeName = tsProbExampool.getTsSimpleCodeByTypeId().getCodeName();
			this.fatherNumList = getNumList(tsProbExampool);
			this.fatherTitleDesc = tsProbExampool.getTitleDesc();// 父题目描述
			this.ifHaveFather = true;
			if(tsProbExampool.getFkByHardLevelId() == null){
				tsProbExampool.setFkByHardLevelId(new TsSimpleCode());
			}
		}
		rid = null;
		numFillList = new ArrayList<ExampoolNumFillBean>(0);
		dependNumList = new ArrayList<String>(0);
		tsProbExampool = new TsProbExampool();
		tsProPoolOpt = new TsProPoolOpt();
		tsProbExampool.setFkByHardLevelId(new TsSimpleCode());
		tsProbExampool.setState(1);
		tsProbExampool.setIsMulti(0);
		tsProbExampool.setMustAsk(1);
		tsProbExampool.setOptLayout(0);
		tsProbExampool.setCreateDate(new Date());
		tsProbExampool.setCreateManid(sessionData.getUser().getRid());
		Integer qesCode = systemModuleService.findMaxQesCode();
		tsProbExampool.setQesCode(qesCode.toString());
		tsProbExampool.setJumpType(0);// 统一设置跳转类型为不跳转
		if (qesLevelCode != null) {// 子题目显示父题目
			tsProbExampool.setQesLevelCode(qesLevelCode + "." + qesCode.toString());
		} else {// 普通题目
			tsProbExampool.setQesLevelCode(qesCode.toString());
			this.tsProbExamtypeId = null;
			this.tsProbExamtypeName = null;
			this.ifHaveFather = false;
		}
		qesLevelCode = null;
		dependNum = new ArrayList<String>(0);
		this.selectedRelQueCode = null;
		relQueList = Lists.newArrayList();
		relQueOptList = Lists.newArrayList();
		this.selectedRelQueOpts = Lists.newArrayList();
	}

	@Override
	public void viewInit() {
	}

	/**
	 * 修订内容：额外依赖增加初始化
	 * 
	 * @MethodReviser rj,2017年12月6日,modInit
	 * 
	 */
	/** 题目修改初始化 ***/
	@Override
	public void modInit() {
		// 题目修改前查询是否有子题目依赖它，若有，保存跳转脚本
		// 依赖题目可能有多个
		numFillList = new ArrayList<ExampoolNumFillBean>(0);
		dependNumList = new ArrayList<String>(0);
		dependNum = new ArrayList<String>(0);
		List<TsProbExampool> list = new ArrayList<TsProbExampool>();
		list = systemModuleService// 子题目
				.ifHaveChildrenProDepend(tsProbExampool.getQesCode());
		if (list != null) {
			for (TsProbExampool t : list) {
				this.jumpScript = t.getJumpQuestCode();
				if (StringUtils.isNotBlank(jumpScript)) {
					String[] jumpScripts = jumpScript.split(",");
					if (jumpScripts[1].contains(";")) {
						dependNumList.add(jumpScript.substring(jumpScript.indexOf(",") + 1, jumpScript.indexOf(";")));// 子题目依赖序号集合
					} else if (jumpScripts[1].contains("@")) {
						String[] ss = jumpScripts[1].split("@");
						for (int i = 0; i < ss.length; i++) {
							dependNumList.add(ss[i]);
						}
					} else {
						dependNumList.add(jumpScript.substring(jumpScript.indexOf(",") + 1));// 子题目依赖序号集合
					}

				}
			}
		}

		this.tsProbExampool = systemModuleService.findTsProbExampoolByRid(rid);
		this.tsProbExamtypeName = tsProbExampool.getTsSimpleCodeByTypeId() == null ? null : tsProbExampool
				.getTsSimpleCodeByTypeId().getCodeName();
		this.tsProbExamtypeId = tsProbExampool.getTsSimpleCodeByTypeId() == null ? null : tsProbExampool
				.getTsSimpleCodeByTypeId().getRid();
		this.tsProPoolOpt = new TsProPoolOpt();
		optSort(tsProbExampool.getTsProPoolOpts());
		ifHaveFather = false;
		if (tsProbExampool.getQuestType().getTypeNo() == 7 || tsProbExampool.getQuestType().getTypeNo() == 9) {
			String s = tsProbExampool.getInvokeScrt();
			String fmr = tsProbExampool.getFillMaxRange();
			if (s != null || fmr != null) {
				initNumFillScript(s, fmr);
			}
		}
		// 选项数字填空解析
		if (tsProbExampool.getQuestType().getTypeNo() == 0 || tsProbExampool.getQuestType().getTypeNo() == 1
				|| tsProbExampool.getQuestType().getTypeNo() == 10) {
			String fmr = tsProbExampool.getFillMaxRange();
			if (StringUtils.isNotBlank(fmr)) {
				initOptNumFillScript(fmr);
			}
		}

		// 判断是否是子题目,是则显示跳转选项
		if (tsProbExampool.getQesLevelCode().contains(".")) {
			String subLevelCode = tsProbExampool.getQesLevelCode().substring(0,
					tsProbExampool.getQesLevelCode().lastIndexOf("."));
			if (StringUtils.isNotBlank(tsProbExampool.getJumpQuestCode())) {
				jumpScript = tsProbExampool.getJumpQuestCode();
				String[] jumpScripts = jumpScript.split(",");
				if (jumpScripts[1].contains(";")) {
					jumpScripts[1].replace(";", "");
				}
				if (jumpScripts[1].contains("@")) {
					String[] ss = jumpScripts[1].split("@");
					for (int i = 0; i < ss.length; i++) {
						dependNum.add(ss[i].trim());
					}
				} else {
					dependNum.add(jumpScript.substring(jumpScript.indexOf(",") + 1).trim());// 子题目依赖序号集合
				}

			} else {
				dependNum = null;
			}
			fatherNumList = getNumList(systemModuleService.fetchTsProbExampool(subLevelCode));
			// 子题目显示父题目
			ifHaveFather = true;
			fatherTitleDesc = systemModuleService.fetchTsProbExampool(subLevelCode).getTitleDesc();
		}
		if (tsProbExampool.getIsMulti() == null) {
			tsProbExampool.setIsMulti(0);
		}
		if (tsProbExampool.getJumpType() == null) {
			tsProbExampool.setJumpType(0);
		}
		if (tsProbExampool.getFkByHardLevelId() == null) {
			tsProbExampool.setFkByHardLevelId(new TsSimpleCode());
		}
		if (!ifHaveFather && tsProbExampool.getJumpType() != null && tsProbExampool.getJumpType() == 2) {
			String[] arr = tsProbExampool.getJumpQuestCode().split(",");
			selectedRelQueCode = arr[0];
			selectedRelQueOpts.addAll(Arrays.asList(arr[1].split("@")));
			relQueList = systemModuleService.selectRelQueList(tsProbExamtypeId, this.tsProbExampool.getRid());
			relQueOptList = Lists.newArrayList();
			for (TsProbExampool pool : relQueList) {
				if (selectedRelQueCode.equals(pool.getQesCode())) {
					for (TsProPoolOpt opt : pool.getTsProPoolOpts()) {
						relQueOptList.add(new SelectItem(opt.getNum(), opt.getOptionDesc()));
					}
				}
			}
		} else {
			selectedRelQueCode = null;
			selectedRelQueOpts = Lists.newArrayList();
		}
	}

	/**
	 * 
	 * <p>
	 * 方法描述：跳转类型切换事件
	 * </p>
	 * 
	 * @MethodAuthor rj,2017年11月9日,onJumpTypeChange
	 * 
	 *               修订内容：未选择题库类型给予提示
	 * 
	 * @MethodReviser rj,2017年12月5日,onJumpTypeChange
	 * 
	 */
	public void onJumpTypeChange() {
		if (tsProbExamtypeId == null) {
			JsfUtil.addErrorMessage("请选择题库类型");
			tsProbExampool.setJumpType(0);
			return;
		} else if (tsProbExampool.getJumpType() == 0) {
			relQueList = Lists.newArrayList();
			selectedRelQueCode = null;
		} else {
			relQueList = systemModuleService.selectRelQueList(tsProbExamtypeId, this.tsProbExampool.getRid());
			relQueOptList = Lists.newArrayList();
			if(!CollectionUtils.isEmpty(relQueList)){
				selectedRelQueCode=relQueList.get(0).getQesCode();
				for (TsProbExampool pool : relQueList) {
					if (selectedRelQueCode.equals(pool.getQesCode())) {
						for (TsProPoolOpt opt : pool.getTsProPoolOpts()) {
							relQueOptList.add(new SelectItem(opt.getNum(), opt.getOptionDesc()));
						}
					}
				}
			}
		}
	}

	/**
	 * 
	 * <p>
	 * 方法描述：依赖题目选中事件
	 * </p>
	 * 
	 * @MethodAuthor rj,2017年11月15日,onRelQueSelect
	 */
	public void onRelQueSelect() {
		selectedRelQueOpts = Lists.newArrayList();
		relQueOptList = Lists.newArrayList();
		for (TsProbExampool pool : relQueList) {
			if (selectedRelQueCode.equals(pool.getQesCode())) {
				for (TsProPoolOpt opt : pool.getTsProPoolOpts()) {
					relQueOptList.add(new SelectItem(opt.getNum(), opt.getOptionDesc()));
				}
			}
		}
	}

	// 修改填空题时，解析危险因素脚本,验证脚本
	private void initNumFillScript(String s, String frm) {
		this.numFillList = new ArrayList<ExampoolNumFillBean>(0);
		if (s != null) {
			String[] scripts = s.split("};");
			// 处理危险因素
			if (scripts.length > 0) {

				String isHaveSame = "";// 用于解析时判断是否有重复序号
				for (int i = 0; i < scripts.length; i++) {
					ExampoolNumFillBean end = new ExampoolNumFillBean();
					String num = scripts[i].substring(scripts[i].indexOf("_") + 1, scripts[i].indexOf(")")).trim();
					String factorId = scripts[i].substring(scripts[i].indexOf("(\"") + 2, scripts[i].indexOf("\")"))
							.trim();
					String minval = null;
					String maxval = null;
					if (scripts[i].contains(">=")) {
						if (scripts[i].contains("<=")) {
							minval = scripts[i].substring(scripts[i].indexOf(">=") + 2, scripts[i].indexOf("&&"))
									.trim();
							maxval = scripts[i].substring(scripts[i].indexOf("<=") + 2, scripts[i].indexOf(") {"))
									.trim();

						} else {
							minval = scripts[i].substring(scripts[i].indexOf(">=") + 2, scripts[i].indexOf(") {"))
									.trim();
						}
					} else {
						maxval = scripts[i].substring(scripts[i].indexOf("<=") + 2, scripts[i].indexOf(") {")).trim();
					}
					end.setCode(this.tsProbExampool.getQesCode());
					end.setMaxVal(maxval == null ? null : Double.valueOf(maxval));
					end.setMinVal(minval == null ? null : Double.valueOf(minval));
					end.setNum(num == null ? null : Integer.valueOf(num));
					if (isHaveSame.contains("@" + num + "#")) {
						for (ExampoolNumFillBean e : numFillList) {
							if (e.getNum().toString().equals(num)) {
								e.setFactorAndAction(e.getFactorAndAction() + ";" + factorId + ",1");
							}
						}
					} else {
						isHaveSame += "@" + num + "#";
						end.setFactorAndAction(factorId + ",1");
						numFillList.add(end);
					}
				}
			}
		}
		sortNumFillList(numFillList);
		// 处理数字验证
		if (frm != null) {
			if (tsProbExampool.getIsMulti() != null && tsProbExampool.getIsMulti() == 0) {
				frm = frm.replace("0,", "1,");
			}
			String[] verifyScripts = frm.split(";");
			for (int i = 0; i < verifyScripts.length; i++) {
				String ss = verifyScripts[i];
				if (!"".equals(ss)) {
					String num = ss.substring(0, ss.indexOf(","));
					String min = ss.substring(ss.indexOf(",") + 1, ss.indexOf("~"));
					String max = ss.substring(ss.indexOf("~") + 1);
					boolean flag = false;
					for (ExampoolNumFillBean efb : numFillList) {
						if (num.equals(efb.getNum().toString())) {
							efb.setMinVerifyVal((min == null || "".equals(min)) ? null : Double.valueOf(min));
							efb.setMaxVerifyVal((max == null || "".equals(max)) ? null : Double.valueOf(max));
							flag = true;
						}
					}
					if (!flag) {
						ExampoolNumFillBean e = new ExampoolNumFillBean();
						e.setNum(Integer.valueOf(num));
						e.setMinVerifyVal((min == null || "".equals(min)) ? null : Double.valueOf(min));
						e.setMaxVerifyVal((max == null || "".equals(max)) ? null : Double.valueOf(max));
						numFillList.add(e);
					}

				}
			}
		}
		sortNumFillList(numFillList);
	}

	/** 解析选项数值填空脚本到选项 **/
	private void initOptNumFillScript(String frm) {
		List<TsProPoolOpt> opts = tsProbExampool.getTsProPoolOpts();
		String[] script = frm.split(";");
		for (int i = 0; i < script.length; i++) {
			if (StringUtils.isNotBlank(script[i])) {
				for (TsProPoolOpt t : opts) {
					if (script[i].contains(t.getNum().toString() + "_")) {
						t.setNumFillScript(((t.getNumFillScript() == null ? "" : t.getNumFillScript()) + script[i]
								.replace(t.getNum().toString() + "_", "_")) + ";");
						t.setIsNumFill(1);
						t.setNeedFill(1);
					}
				}
			}
		}
		tsProbExampool.setTsProPoolOpts(opts);
	}

	/*** 题目删除 **/
	public void deleteAction() {
		try {
			// 主题目删除，删除下面的子题目
			systemModuleService.deleteTsProbExampoolByCode(qesLevelCode);
			qesLevelCode = null;
			rid = null;
			searchAction();
			JsfUtil.addSuccessMessage("删除成功！");
		} catch (Exception e) {
			JsfUtil.addErrorMessage("删除失败，该题目可能已被引用！");
		}
	}

	/**
	 * 
	 * 
	 * 修订内容：增加表格题是否固定行数验证
	 * 
	 * @MethodReviser rj,2017年12月8日,beforeSavingAction
	 * 
	 */
	public boolean beforeSavingAction() {
		boolean flag = true;
		// 验证选项值选项序号是否重复
		if (tsProbExampool.getTsProPoolOpts() != null && tsProbExampool.getTsProPoolOpts().size() > 0) {
			List<TsProPoolOpt> list = tsProbExampool.getTsProPoolOpts();
			for (int i = 0; i < list.size(); i++) {
				for (int j = i + 1; j < list.size(); j++) {
					if (list.get(i).getNum().intValue() == list.get(j).getNum().intValue()) {
						JsfUtil.addErrorMessage("选项序号" + list.get(j).getNum() + "重复！");
						flag = false;
					}
					if (list.get(i).getOptionValue().equals(list.get(j).getOptionValue())) {
						JsfUtil.addErrorMessage("选项值" + list.get(j).getOptionValue() + "重复！");
						flag = false;
					}
				}
				if (StringUtils.isBlank(list.get(i).getOptionDesc())) {
					JsfUtil.addErrorMessage("选项文字不能为空！");
					flag = false;
				}else if(list.get(i).getOptionDesc().length() > 250){
					JsfUtil.addErrorMessage("选项文字长度不能超过250！");
					flag = false;
				}
				if(StringUtils.isNotBlank(list.get(i).getOtherDesc()) && list.get(i).getOtherDesc().length() > 1000){
					JsfUtil.addErrorMessage("说明长度不能超过1000！");
					flag = false;
				}
			}
		}
		if (StringUtils.isBlank(this.tsProbExamtypeName)) {
			JsfUtil.addErrorMessage("题库类型不能为空！");
			flag = false;
		}
		if (StringUtils.isBlank(tsProbExampool.getTitleDesc())) {
			JsfUtil.addErrorMessage("题目标题不能为空！");
			flag = false;
		}else if(this.tsProbExampool.getTitleDesc().length() > 500) {
			JsfUtil.addErrorMessage("题目标题长度不能超过500！");
			flag = false;
		}
		if(StringUtils.isNotBlank(tsProbExampool.getOtherDesc()) && this.tsProbExampool.getOtherDesc().length() > 1000) {
			JsfUtil.addErrorMessage("附加文字长度不能超过1000！");
			flag = false;
		}
		if(StringUtils.isNotBlank(tsProbExampool.getAnsDesc()) && this.tsProbExampool.getAnsDesc().length() > 500) {
			JsfUtil.addErrorMessage("参考答案长度不能超过500！");
			flag = false;
		}
		if (tsProbExampool.getQuestType() == null) {
			JsfUtil.addErrorMessage("题目类型不能为空！");
			flag = false;
		} else {
			// 填空题，多项填空的验证
			if (tsProbExampool.getQuestType().getTypeNo() == 7 || tsProbExampool.getQuestType().getTypeNo() == 9) {
				if (tsProbExampool.getIsMulti() == 0 && numFillList != null && numFillList.size() > 1) {
					JsfUtil.addErrorMessage("填空数大于1，请重新选择“是否为多项填空”！");
					flag = false;
				} else if (numFillList != null && numFillList.size() > 0) {
					for (int i = 0; i < numFillList.size(); i++) {
						if (numFillList.get(i).getNum() == null) {
							JsfUtil.addErrorMessage("填空序号不能为空!");
							flag = false;
						}

						/*if (numFillList.get(i).getMaxVal() == null && numFillList.get(i).getMinVal() == null
								&& numFillList.get(i).getMaxVerifyVal() == null
								&& numFillList.get(i).getMinVerifyVal() == null) {
							JsfUtil.addErrorMessage("填空值最大值、填空值最小值、触发危险因素最大值、触发危险因素最小值不能同时为空!");
							flag = false;
						} else {
							if (numFillList.get(i).getFactorAndAction() == null
									&& (numFillList.get(i).getMaxVal() != null || numFillList.get(i).getMinVal() != null)) {
								JsfUtil.addErrorMessage("第" + numFillList.get(i).getNum().toString() + "个填空请配置危险因素!");
								flag = false;
							}
						}
						if (numFillList.get(i).getMaxVal() != null && numFillList.get(i).getMinVal() != null
								&& numFillList.get(i).getMaxVal() < numFillList.get(i).getMinVal()) {
							JsfUtil.addErrorMessage("触发危险因素最大值小于触发危险因素最小值!");
							flag = false;
						}
						if (numFillList.get(i).getMaxVerifyVal() != null
								&& numFillList.get(i).getMinVerifyVal() != null
								&& numFillList.get(i).getMaxVerifyVal() < numFillList.get(i).getMinVerifyVal()) {
							JsfUtil.addErrorMessage("填空值最大值小于填空值最小值!");
							flag = false;
						}*/
						for (int j = i + 1; j < numFillList.size(); j++) {
							if (numFillList.get(i).getNum().intValue() == numFillList.get(j).getNum().intValue()) {
								JsfUtil.addErrorMessage("填空序号" + numFillList.get(j).getNum() + "重复！");
								flag = false;
							}
						}
					}
				}
			}
			if (tsProbExampool.getQuestType().getTypeNo() == 2 || tsProbExampool.getQuestType().getTypeNo() == 3
					|| tsProbExampool.getQuestType().getTypeNo() == 4 || tsProbExampool.getQuestType().getTypeNo() == 5
					|| tsProbExampool.getQuestType().getTypeNo() == 6 || tsProbExampool.getQuestType().getTypeNo() == 9
					|| tsProbExampool.getQuestType().getTypeNo() == 7) {
				tsProbExampool.setTsProPoolOpts(new ArrayList<TsProPoolOpt>());
			}
			if (tsProbExampool.getQuestType().getTypeNo() == 2) {
				if (tsProbExampool.getSlideMaxval() == null) {
					JsfUtil.addErrorMessage("滑动题最大值不能为空！");
					flag = false;
				}
				if (tsProbExampool.getSlideMinval() == null) {
					JsfUtil.addErrorMessage("滑动题最小值不能为空！");
					flag = false;
				}
				if (StringUtils.isBlank(tsProbExampool.getSlideMaxDesc())) {
					JsfUtil.addErrorMessage("滑动题最大值描述不能为空！");
					flag = false;
				}
				if (StringUtils.isBlank(tsProbExampool.getSlideMinDesc())) {
					JsfUtil.addErrorMessage("滑动题最小值描述不能为空！");
					flag = false;
				}
				if (tsProbExampool.getSlideMaxval() != null && tsProbExampool.getSlideMinval() != null) {
					if (tsProbExampool.getSlideMaxval() < tsProbExampool.getSlideMinval()) {
						JsfUtil.addErrorMessage("滑动题最大值不能小于滑动题最小值！");
						flag = false;
					}
				}
			} else {
				tsProbExampool.setSlideMaxval(null);
				tsProbExampool.setSlideMaxDesc(null);
				tsProbExampool.setSlideMinval(null);
				tsProbExampool.setSlideMinDesc(null);
			}

			if (tsProbExampool.getQuestType().getTypeNo() != 11) {
				tsProbExampool.setFkByTableId(null);
			} else {
				tsProbExampool.getFkByTableId().setCreateManid(sessionData.getUser().getRid());
				if (tsProbExampool.getFkByTableId().getRowFixed() == null) {
					JsfUtil.addErrorMessage("请选择是否固定行数！");
					flag = false;
				} else if (tsProbExampool.getFkByTableId().getRowFixed() == 1) {
					tsProbExampool.getFkByTableId().setDefaultLineNum(null);
				} else if (tsProbExampool.getFkByTableId().getRowFixed() == 0
						&& tsProbExampool.getFkByTableId().getDefaultLineNum() == null) {
					JsfUtil.addErrorMessage("默认行数不能为空！");
					flag = false;
				}
			}

			// 多选题以及单选题选项不能为空
			if ((tsProbExampool.getQuestType().getTypeNo() == 0 || tsProbExampool.getQuestType().getTypeNo() == 1)
					&& (tsProbExampool.getTsProPoolOpts().size() == 0 || tsProbExampool.getTsProPoolOpts() == null)) {
				JsfUtil.addErrorMessage("多选题以及单选题选项不能为空！");
				flag = false;
			}
		}

		if (tsProbExampool.getQesLevelCode().contains(".")) {
			if (tsProbExampool.getJumpType() == 2 && (dependNum == null || dependNum.size() == 0)) {
				JsfUtil.addErrorMessage("依赖选项不能为空！");
				flag = false;
			}
			if (tsProbExampool.getJumpType() == 2 && dependNum != null && dependNum.size() > 0) {
				// 获得父题目编码,拼接得到跳转脚本
				String code = tsProbExampool.getQesLevelCode().replace("." + tsProbExampool.getQesCode(), "");
				String code2 = code.substring(code.lastIndexOf(".") + 1);
				String dependNums = "";
				for (String a : dependNum) {
					dependNums += a + "@";
				}
				dependNums = dependNums.substring(0, dependNums.length() - 1);
				tsProbExampool.setJumpQuestCode(code2 + "," + dependNums);
			}
		} else if (tsProbExampool.getJumpType() == 2) {
			if (StringUtils.isBlank(selectedRelQueCode)) {
				JsfUtil.addErrorMessage("依赖题目不能为空！");
				flag = false;
			} else if (selectedRelQueOpts == null || selectedRelQueOpts.size() == 0) {
				JsfUtil.addErrorMessage("依赖选项不能为空！");
				flag = false;
			}
		} else {
			tsProbExampool.setJumpQuestCode(null);
		}
		List<TbProPoolColsdefine> colList = null == this.tsProbExampool || null == this.tsProbExampool.getFkByTableId() ?
				null : this.tsProbExampool.getFkByTableId().getColsdefines();
		if(!this.validateScript(colList)){
			flag = false;
		}
		return flag;
	}

	/*** 题目保存、保存前验证 ****/
	@Override
	public void saveAction() {
		if (!beforeSavingAction()) {
			return;
		}
		try {
			if (tsProbExampool.getQuestType().getTypeNo() == 7 || tsProbExampool.getQuestType().getTypeNo() == 9) {
				// 排序，重新生成 序号，生成脚本
				sortNumFillList(numFillList);
				String s = createNumFillScript(numFillList);
				// 生成验证脚本
				String frm = createVerifyScrt(numFillList);
				// if(s!=null){
				tsProbExampool.setInvokeScrt(s);
				tsProbExampool.setFillMaxRange(frm);
				// }
			}
			// 单选和多选,是非题时，若选项有脚本则生成数字填空脚本
			if (tsProbExampool.getQuestType().getTypeNo() == 0 || tsProbExampool.getQuestType().getTypeNo() == 1
					|| tsProbExampool.getQuestType().getTypeNo() == 10) {
				if (tsProbExampool.getTsProPoolOpts() != null && tsProbExampool.getTsProPoolOpts().size() > 0) {
					tsProbExampool.setFillMaxRange(createNumFillScrt(tsProbExampool.getTsProPoolOpts()));
				}
			}
			// 题型不是0,1,7,9,10时，没有填空验证脚本
			if (tsProbExampool.getQuestType().getTypeNo() != 0 && tsProbExampool.getQuestType().getTypeNo() != 1
					&& tsProbExampool.getQuestType().getTypeNo() != 7 && tsProbExampool.getQuestType().getTypeNo() != 9
					&& tsProbExampool.getQuestType().getTypeNo() != 10) {
				tsProbExampool.setFillMaxRange(null);
			}
			if(tsProbExampool.getFkByHardLevelId() == null || tsProbExampool.getFkByHardLevelId().getRid() == null ){
				tsProbExampool.setFkByHardLevelId(null);
				tsProbExampool.setHardLevel(null);
			}else {
				Integer hardLevel = null;
				try {
					if(this.hardLevelMap.containsKey(tsProbExampool.getFkByHardLevelId().getRid())){
						hardLevel = Integer.parseInt(this.hardLevelMap.get(tsProbExampool.getFkByHardLevelId().getRid()));
					}
				} catch (NumberFormatException e) {
					hardLevel = null;
				}
				tsProbExampool.setHardLevel(hardLevel);
			}
			saveRelScript();
			tsProbExampool.setTsSimpleCodeByTypeId(new TsSimpleCode(tsProbExamtypeId));
			systemModuleService.insertOrUpdateTsProbExampool(tsProbExampool);
			super.backAction();
			// ininSearchCondition();
			searchAction();
			tsProbExamtypeId = null;
			tsProbExamtypeName = null;
			rid = null;
			JsfUtil.addSuccessMessage("保存成功！");
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addSuccessMessage("保存失败！");
		}
		if (tsProbExampool.getFkByHardLevelId() == null) {
			tsProbExampool.setFkByHardLevelId(new TsSimpleCode());
		}
	}

	/**
	 * 
	 * <p>
	 * 方法描述：保存依赖脚本
	 * </p>
	 * 
	 * @MethodAuthor rj,2017年11月16日,saveRelScript
	 */
	private void saveRelScript() {
		if (StringUtils.isNotBlank(selectedRelQueCode) && selectedRelQueOpts != null && selectedRelQueOpts.size() > 0) {
			StringBuilder sb = new StringBuilder();
			for (String optNum : selectedRelQueOpts) {
				sb.append("@").append(optNum);
			}
			tsProbExampool.setJumpQuestCode(selectedRelQueCode + "," + sb.toString().replaceFirst("@", ""));
		}
	}

	/*** 题型不为多选且必答时，设置minSelectNum为null **/
	public void onQuestTypeChange() {
		if (tsProbExampool.getQuestType() != null
				&& !(tsProbExampool.getQuestType().getTypeNo() == 1 && tsProbExampool.getMustAsk() == 1)) {
			tsProbExampool.setMinSelectNum(null);
		}
		if (tsProbExampool.getQuestType() != null && tsProbExampool.getQuestType().getTypeNo() == 2) {
			tsProbExampool.setSlideMaxval(null);
			tsProbExampool.setSlideMaxDesc(null);
			tsProbExampool.setSlideMinval(null);
			tsProbExampool.setSlideMinDesc(null);
			RequestContext.getCurrentInstance().update("slideMaxval,slideMinval,slideMaxDesc,slideMinDesc");
		}
		// 选择是非题时，直接生成2个选项
		if (tsProbExampool.getQuestType() != null && tsProbExampool.getQuestType().getTypeNo() == 10) {
			TsProPoolOpt t1 = new TsProPoolOpt();
			t1.setState(1);
			t1.setNeedFill(0);
			t1.setIsMulti(0);
			t1.setIsNumFill(0);
			t1.setTsProbExampool(tsProbExampool);
			t1.setCreateManid(sessionData.getUser().getRid());
			t1.setCreateDate(new Date());
			t1.setNum(1);
			t1.setOptionDesc("正确");
			t1.setOptionValue("1");
			t1.setOptionImg(null);
			TsProPoolOpt t2 = new TsProPoolOpt();
			t2.setState(1);
			t2.setNeedFill(0);
			t2.setIsMulti(0);
			t2.setIsNumFill(0);
			t2.setTsProbExampool(tsProbExampool);
			t2.setCreateManid(sessionData.getUser().getRid());
			t2.setCreateDate(new Date());
			t2.setNum(2);
			t2.setOptionDesc("错误");
			t2.setOptionValue("2");
			t2.setOptionImg(null);
			List<TsProPoolOpt> l = new ArrayList<TsProPoolOpt>(0);
			l.add(t2);
			l.add(t1);
			optSort(l);
			tsProbExampool.setTsProPoolOpts(l);
			RequestContext.getCurrentInstance().update(":tabView:editForm:optInfoTable");

		}
		if (tsProbExampool.getQuestType() != null
				&& (tsProbExampool.getQuestType().getTypeNo() == 4 || tsProbExampool.getQuestType().getTypeNo() == 7 || tsProbExampool
						.getQuestType().getTypeNo() == 9)) {
			tsProbExampool.setIsMulti(0);
		}
		// 选择数字、整数填空题时，显示危险因素配置
		if (tsProbExampool.getQuestType() != null
				&& (tsProbExampool.getQuestType().getTypeNo() == 7 || tsProbExampool.getQuestType().getTypeNo() == 9)) {
			numFillList = new ArrayList<ExampoolNumFillBean>(0);
			tsProbExampool.setIsMulti(0);
		}

		if (tsProbExampool.getQuestType() != null
				&& (tsProbExampool.getQuestType().getTypeNo() == 1 || tsProbExampool.getQuestType().getTypeNo() == 0)) {
			tsProbExampool.setInvokeScrt(null);
		}
		// 表格题
		if (tsProbExampool.getQuestType() != null && tsProbExampool.getQuestType().getTypeNo() == 11) {
			tsProbExampool.setFkByTableId(new TbProPoolTabdefine());
		}
	}

	/*** 不为横向布局时触发 **/
	public void onOptLayoutChange() {
		if (tsProbExampool.getOptLayout() == null || tsProbExampool.getOptLayout() != 1) {
			tsProbExampool.setCols(null);
		}
	}

	/*** 添加图片 **/
	public void handleFileUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			String fileName = file.getFileName();
			// 得到唯一uid
			String uuid = UUID.randomUUID().toString().replaceAll("-", "");
			// 后缀名
			String hz = fileName.substring(fileName.lastIndexOf(".") + 1);
			// 文件路径
			String filePath = JsfUtil.getAbsolutePath() + "files/que/" + uuid + "." + hz;
			String showDir = "/files/que/" + uuid + "." + hz;
			if(isImg == 1){
				tsProbExampool.setAnnexAddr(showDir);
			}else{
				if (uploadTag == 1) {
					optImg = showDir;
					// tsProPoolOpt.setOptionImg(showDir);
				} else {
					tsProbExampool.setOtherImg(showDir);
				}
			}
			try {
				FileUtils.copyFile(filePath, file.getInputstream());
				if(isImg != 1){
					ImageUtil.compressImage(filePath, filePath, 124, 94);
				}
				JsfUtil.addSuccessMessage("上传成功！");
				RequestContext.getCurrentInstance().execute("fileUpVar.hide();");
				if(isImg != 1){
					if (uploadTag == 1) {
						RequestContext.getCurrentInstance().update("tabView:editForm:optionImgGroup");
					} else {
						RequestContext.getCurrentInstance().update("tabView:editForm:otherImgGroup");
					}
				}else{
					RequestContext.getCurrentInstance().update("tabView:editForm:optLayoutGrid");
				}
			} catch (IOException e) {
				FacesMessage msg = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", msg);
				e.printStackTrace();
			}
		}
	}

	/*** 删除图片 **/
	public void deleteDiskFile() {
		String iconFile;
		if (uploadTag == 1) {
			iconFile = tsProPoolOpt.getOptionImg();
		} else {
			iconFile = tsProbExampool.getOtherImg();
		}
		if (null != iconFile) {
			String filePath = JsfUtil.getAbsolutePath() + iconFile.substring(1);
			new File(filePath).delete();
			if (uploadTag == 1) {
				// ifDelOptPic=true;
				optImg = null;
				// tsProPoolOpt.setOptionImg(null);
			} else {
				tsProbExampool.setOtherImg(null);
			}
		}
	}

	/** 获得题目所有选项列表 */
	public List<SelectItem> getNumList(TsProbExampool t) {
		List<SelectItem> List = new ArrayList<SelectItem>();
		List<TsProPoolOpt> l = t.getTsProPoolOpts();
		optSort(l);
		for (TsProPoolOpt opt : l) {
			List.add(new SelectItem(opt.getNum(), opt.getOptionDesc()));
		}
		return List;
	}

	/** 选项新增初始化 */
	public void optAddInitAction() {
		tsProPoolOpt = new TsProPoolOpt();
		tsProPoolOpt.setIsCorrect(0);
		tsProPoolOpt.setState(1);
		tsProPoolOpt.setNeedFill(0);
		tsProPoolOpt.setIsMulti(0);
		tsProPoolOpt.setTsProbExampool(tsProbExampool);
		tsProPoolOpt.setCreateManid(sessionData.getUser().getRid());
		tsProPoolOpt.setIsAlter(0);
		tsProPoolOpt.setIsNumFill(0);
		num = null;
		optValue = null;
		optImg = null;

	}

	/** 选项修改初始化 */
	public void optModInitAction() {
		optImg = tsProPoolOpt.getOptionImg();
		if (tsProPoolOpt.getIsCorrect() == null) {
			tsProPoolOpt.setIsCorrect(0);
		}
		if (tsProbExampool.getQuestType() != null && tsProbExampool.getQuestType().getTypeNo() == 1
				&& tsProPoolOpt.getIsAlter() == null) {
			tsProPoolOpt.setIsAlter(0);
		}
		if (tsProbExampool.getQuestType().getTypeNo() == 0 || tsProbExampool.getQuestType().getTypeNo() == 10
				|| tsProbExampool.getQuestType().getTypeNo() == 1) {
			if (tsProPoolOpt.getIsMulti() == null) {
				tsProPoolOpt.setIsMulti(0);
			}
			if (tsProPoolOpt.getIsNumFill() == null) {
				tsProPoolOpt.setIsNumFill(0);
			}
		}

		try {
			this.tempTsProPoolOpt = (TsProPoolOpt) tsProPoolOpt.clone();
		} catch (CloneNotSupportedException e) {
			e.printStackTrace();
		}

	}

	public void optCancelAction() {
		if (null != tsProPoolOpt.getRid()) {
			tsProPoolOpt.setOptionDesc(tempTsProPoolOpt.getOptionDesc());
			tsProPoolOpt.setIsAlter(tempTsProPoolOpt.getIsAlter());
			tsProPoolOpt.setIsCorrect(tempTsProPoolOpt.getIsCorrect());
			tsProPoolOpt.setIsMulti(tempTsProPoolOpt.getIsMulti());
			tsProPoolOpt.setIsNumFill(tempTsProPoolOpt.getIsNumFill());
			tsProPoolOpt.setNeedFill(tempTsProPoolOpt.getNeedFill());
			tsProPoolOpt.setNum(tempTsProPoolOpt.getNum());
			tsProPoolOpt.setNumFillScript(tempTsProPoolOpt.getNumFillScript());
			tsProPoolOpt.setOptionScore(tempTsProPoolOpt.getOptionScore());
			tsProPoolOpt.setOptionValue(tempTsProPoolOpt.getOptionValue());
			tsProPoolOpt.setOtherDesc(tempTsProPoolOpt.getOtherDesc());
			tsProPoolOpt.setState(tempTsProPoolOpt.getState());
		}
		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.execute("PF('OptDialog').hide();");
	}

	/**
	 * 选项排序
	 * 
	 * @param opts
	 */
	public void optSort(List<TsProPoolOpt> opts) {
		Collections.sort(opts, new Comparator<TsProPoolOpt>() {
			@Override
			public int compare(TsProPoolOpt o1, TsProPoolOpt o2) {
				return o1.getNum() - o2.getNum();
			}
		});
	}

	public void sortNumFillList(List<ExampoolNumFillBean> l) {
		Collections.sort(l, new Comparator<ExampoolNumFillBean>() {
			@Override
			public int compare(ExampoolNumFillBean o1, ExampoolNumFillBean o2) {
				return o1.getNum() - o2.getNum();
			}
		});
	}

	public void sortquestTypeList(List<QuestType> l) {
		Collections.sort(l, new Comparator<QuestType>() {
			@Override
			public int compare(QuestType o1, QuestType o2) {
				return o1.getTypeCN().length() - o2.getTypeCN().length();
			}
		});
	}

	/**
	 * 选项保存验证
	 */
	public boolean beforeOptSaveAction() {
		boolean flag = true;
		Set<String> errorList = new HashSet<String>();
		for (TsProPoolOpt temp : tsProbExampool.getTsProPoolOpts()) {
			if (temp != tsProPoolOpt && tsProPoolOpt.getNum().intValue() == temp.getNum().intValue()) {
				errorList.add("已存在相同序号的选项！");

			}
			if (temp != tsProPoolOpt && tsProPoolOpt.getOptionValue() != null
					&& tsProPoolOpt.getOptionValue().equals(temp.getOptionValue())) {
				errorList.add("已存在相同选项值的选项！");

			}
			if (tsProPoolOpt.getJumpType() == null || tsProPoolOpt.getJumpType().intValue() != 3) {
				tsProPoolOpt.setJumpQuestCode(null);
			}
		}
		if(StringUtils.isNotBlank(tsProPoolOpt.getOptionDesc()) && tsProPoolOpt.getOptionDesc().length() > 250){
			JsfUtil.addErrorMessage("选项文字长度不能超过250！");
			flag = false;
		}
		if(StringUtils.isNotBlank(tsProPoolOpt.getOtherDesc()) && tsProPoolOpt.getOtherDesc().length() > 1000){
			JsfUtil.addErrorMessage("说明长度不能超过1000！");
			flag = false;
		}
		if (tsProPoolOpt.getOptionValue() == null) {
			JsfUtil.addErrorMessage("选项值不能为空！");
			flag = false;
		}
		if (tsProPoolOpt.getNeedFill() == 1 && tsProPoolOpt.getIsMulti() == null) {
			JsfUtil.addErrorMessage("请选择选项是否多项填空！");
			flag = false;
		} else {
			if ((tsProPoolOpt.getIsMulti() == 0 && StringUtils.isNotBlank(tsProPoolOpt.getNumFillScript()) && tsProPoolOpt
					.getNumFillScript().split(";").length > 1)) {
				JsfUtil.addErrorMessage("选项是否多项填空与选项数字填空数冲突！");
				flag = false;
			}
		}
		if (tsProPoolOpt.getNeedFill() == 1 && tsProPoolOpt.getIsNumFill() == null) {
			JsfUtil.addErrorMessage("请选择选项是否数字填空！");
			flag = false;
		}
		if (errorList.size() > 0) {
			for (String str : errorList) {
				JsfUtil.addErrorMessage(str);
			}
			flag = false;
		}
		return flag;
	}

	/** 选项保存 **/
	public void optSaveAction() {
		if (!beforeOptSaveAction()) {
			return;
		} else {
			if (num != null && num != tsProPoolOpt.getNum()) {
				if (dependNumList != null && dependNumList.size() > 0) {
					for (String subnum : dependNumList) {
						if (num.toString().equals(subnum)) {
							JsfUtil.addSuccessMessage("选项序号已修改，请及时修改子题目依赖选项！");
							break;
						}
					}
				}
			}
			tsProPoolOpt.setOptionImg(optImg);
			if (tsProPoolOpt.getNeedFill() == 0) {
				tsProPoolOpt.setIsMulti(0);
				tsProPoolOpt.setIsNumFill(0);
			}
			if (tsProPoolOpt.getNeedFill() != 1 || tsProPoolOpt.getIsNumFill() != 1) {
				tsProPoolOpt.setNumFillScript(null);
			} else {
				// 如果不是多项填空
				if (tsProPoolOpt.getIsMulti() == 0 && StringUtils.isNotBlank(tsProPoolOpt.getNumFillScript())) {
					String s = tsProPoolOpt.getNumFillScript();
					String s1 = s.substring(0, s.indexOf("_") + 1);
					String s2 = s.substring(s.indexOf(","), s.indexOf(";") + 1);
					tsProPoolOpt.setNumFillScript(s1 + "0" + s2);
				}
			}
			// 如果选项值发生改变，则修改脚本
			if (optValue != null && !optValue.equals(tsProPoolOpt.getOptionValue())) {
				if (pcf != null) {
					tsProbExampool.setInvokeScrt(pcf.updateScript(optValue, tsProPoolOpt.getOptionValue(),
							tsProbExampool.getInvokeScrt()));
				}
			}
			if (!tsProbExampool.getTsProPoolOpts().contains(tsProPoolOpt)) {
				tsProPoolOpt.setCreateDate(new Date());
				tsProbExampool.getTsProPoolOpts().add(tsProPoolOpt);
			}
			optSort(tsProbExampool.getTsProPoolOpts());
			RequestContext.getCurrentInstance().execute("PF('OptDialog').hide()");
			RequestContext.getCurrentInstance().update(":tabView:editForm:optInfoTable");
		}
	}

	/** 选项删除 */
	public void optDeleteAction() {
		// 若删除的选项被依赖
		if (dependNumList != null && dependNumList.size() > 0) {
			for (String subnum : dependNumList) {
				if (tsProPoolOpt.getNum().toString().equals(subnum)) {
					JsfUtil.addSuccessMessage("选项序号已删除，请及时修改子题目依赖选项！");
					break;
				}
			}
		}
		if (pcf != null) {
			// 根据选项值从脚本中删除对应的规则预警脚本
			pcf.deleteScriptByNum(tsProPoolOpt.getOptionValue(), tsProbExampool.getInvokeScrt());
		}
		tsProbExampool.getTsProPoolOpts().remove(tsProPoolOpt);
		optSort(tsProbExampool.getTsProPoolOpts());
	}

	/** 选项清空 */
	public void optClear() {
		tsProbExampool.getTsProPoolOpts().clear();
	}

	/***
	 * 根据码表批量显示选项
	 */
	public void optShowBatchByCode() {
		simpleCodeList = commServiceImpl.findSimpleCodesByTypeNo(selectedCodeType);
	}

	/**
	 * 根据码表批量生成选项
	 */
	public void optCreateBatchByCode() {
		try {
			List<TsSimpleCode> codes = commServiceImpl.findSimpleCodesByTypeNo(selectedCodeType);
			for (TsSimpleCode code : codes) {
				TsProPoolOpt opt = new TsProPoolOpt();
				opt.setTsProbExampool(tsProbExampool);
				opt.setCreateDate(new Date());
				opt.setCreateManid(sessionData.getUser().getRid());
				opt.setNum(tsProbExampool.getTsProPoolOpts().size() + 1);
				opt.setOptionValue(code.getRid().toString());
				opt.setOptionDesc(code.getCodeName());
				opt.setState(1);
				opt.setNeedFill(0);
				opt.setIsCorrect(0);
				opt.setIsMulti(0);
				// 多选题，是否互斥设为默认否
				if (tsProbExampool.getQuestType().getTypeNo().intValue() == 1) {
					tsProPoolOpt.setIsAlter(0);
				}
				tsProbExampool.getTsProPoolOpts().add(opt);
			}
			JsfUtil.addSuccessMessage("生成成功！");
			RequestContext.getCurrentInstance().execute("PF('CodeChooseDialog').hide();");
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("生成失败!");
		}
	}

	/**
	 * 选项批量生成
	 */
	public void optCreateBatch() {
		if (StringUtils.isBlank(templOpts.trim())) {
			JsfUtil.addErrorMessage("选项内容不能为空！");
			return;
		}
		if (templOpts.trim().length() > 250){
			JsfUtil.addErrorMessage("选项内容长度不能超过250！");
			return;
		}

		try {
			String[] opts = templOpts.split(",");
			for (String str : opts) {
				TsProPoolOpt opt = new TsProPoolOpt();
				opt.setTsProbExampool(tsProbExampool);
				opt.setCreateDate(new Date());
				opt.setCreateManid(sessionData.getUser().getRid());
				opt.setNum(tsProbExampool.getTsProPoolOpts().size() + 1);
				opt.setOptionValue(opt.getNum().toString());
				opt.setOptionDesc(str);
				opt.setState(1);
				opt.setNeedFill(0);
				opt.setIsCorrect(0);
				opt.setIsMulti(0);
				// 多选题，是否互斥设为默认否
				if (tsProbExampool.getQuestType().getTypeNo().intValue() == 1) {
					tsProPoolOpt.setIsAlter(0);
				}
				tsProbExampool.getTsProPoolOpts().add(opt);
			}
			RequestContext.getCurrentInstance().execute("PF('TemplChooseDialog').hide();");
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("解析失败！");
		}
	}

	/** 规则配置方法 */
	public void configAction() {
//		Map<String, Object> map = new HashMap<String, Object>();
//		map.put("num", tsProPoolOpt.getOptionValue());// 选项值
//		map.put("script", tsProbExampool.getInvokeScrt());
//		map.put("type", 0);//
//		pcf.doConfigFactor(map);

	}

	/** 危险因素规则返回后处理 */
	public void onFactorSelect(SelectEvent event) {
//		String script = pcf.doCloseDialogSelect(event, tsProPoolOpt.getOptionValue(), tsProbExampool.getInvokeScrt(),
//				tsProbExampool.getQesCode());
//		tsProbExampool.setInvokeScrt(script);
	}

	/** 数字填空新增配置 */
	public void numFillAddInitAction() {
		if (numFillList == null) {
			numFillList = new ArrayList<ExampoolNumFillBean>(0);
		}
		ExampoolNumFillBean enb = new ExampoolNumFillBean();
		// enb.setFactorAndAction(null);
		if (numFillList != null && numFillList.size() > 0) {
			enb.setNum(getMaxNum(numFillList));
		} else {
			enb.setNum(1);
		}
		numFillList.add(enb);
	}

	/** 清空数字填空配置 */
	public void numFillClear() {
		numFillList = new ArrayList<ExampoolNumFillBean>(0);
		tsProbExampool.setIsMulti(0);
	}

	/** 数字填空配置删除 **/
	public void numFillDeleteAction() {
		numFillList.remove(this.numFill);
	}

	/** 数字填空配置危险因素 */
	public void numFillConfigAction() {
//		Map<String, Object> map = new HashMap<String, Object>();
//		map.put("enb", numFill);
//		map.put("type", 1);// 1表示数字、整数填空题
//		pcf.doConfigFactor(map);
	}

	/** 数字填空题危险因素返回后处理 */
	public void numFillOnFactorSelect(SelectEvent event) {
//		pcf.doCloseDialogSelect(this.numFill, event);
	}

	/***/
	private Integer getMaxNum(List<ExampoolNumFillBean> l) {
		Integer i = 0;
		for (ExampoolNumFillBean enb : l) {
			i = enb.getNum() > i ? enb.getNum() : i;
		}
		return i + 1;
	}

	private String createNumFillScript(List<ExampoolNumFillBean> l) {
		String s = "";
		for (int i = 0; i < l.size(); i++) {
			ExampoolNumFillBean enb = new ExampoolNumFillBean();
			enb = l.get(i);
			if (enb.getFactorAndAction() != null) {
				String[] factorAndActions = enb.getFactorAndAction().split(";");
				for (int j = 0; j < factorAndActions.length; j++) {
					String factor = factorAndActions[j].substring(0, factorAndActions[j].indexOf(","));
					// String
					// actType=factorAndActions[j].substring(factorAndActions[j].indexOf(",")+1);
					if (enb.getNum() != null && (enb.getMaxVal() != null || enb.getMinVal() != null)) {
						s += "if( new BigDecimal(q" + this.tsProbExampool.getQesCode() + "_" + enb.getNum().toString()
								+ ")";
						if (enb.getMinVal() != null) {
							if (enb.getMaxVal() != null) {
								s += " >= " + enb.getMinVal() + "&& new BigDecimal(q"
										+ this.tsProbExampool.getQesCode() + "_" + enb.getNum().toString() + ")<="
										+ enb.getMaxVal() + ") { addSet.add(\"" + factor + "\"); };";

							} else {
								s += " >= " + enb.getMinVal() + ") { addSet.add(\"" + factor + "\"); };";
							}
						} else {
							s += " <= " + enb.getMaxVal() + ") { addSet.add(\"" + factor + "\"); };";
						}
					}
				}
			}
		}
		return "".equals(s) ? null : s;
	}

	private String createVerifyScrt(List<ExampoolNumFillBean> l) {
		String s = "";
		for (ExampoolNumFillBean efb : l) {
			if (efb.getMaxVerifyVal() != null || efb.getMinVerifyVal() != null) {
				s = s + efb.getNum().toString() + ",";
				s = s + (efb.getMinVerifyVal() == null ? "" : efb.getMinVerifyVal().toString());
				s = s + "~" + (efb.getMaxVerifyVal() == null ? "" : efb.getMaxVerifyVal().toString()) + ";";
			}
		}
		if (tsProbExampool.getIsMulti() == 0 && StringUtils.isNotBlank(s)) {
			s = "0" + s.substring(1, s.length());
		}
		return "".equals(s) ? null : s;
	}

	// =======选项数字脚本=====================

	/** 选项数字填空初始化 **/
	public void optNumFillAction() {
		optNumFillList = new ArrayList<ExampoolNumFillBean>(0);
		if (StringUtils.isNotBlank(tsProPoolOpt.getNumFillScript())) {
			String[] s = tsProPoolOpt.getNumFillScript().split(";");
			if (s.length > 0) {
				for (int i = 0; i < s.length; i++) {
					s[i] = s[i].replace("_0,", "_1,");
					if (StringUtils.isNotBlank(s[i])) {
						ExampoolNumFillBean enb = new ExampoolNumFillBean();
						String num = s[i].substring(1, s[i].indexOf(","));
						String min = s[i].substring(s[i].indexOf(",") + 1, s[i].indexOf("~"));
						String max = s[i].substring(s[i].indexOf("~") + 1);
						enb.setMinVerifyVal((min == null || "".equals(min)) ? null : Double.valueOf(min));
						enb.setMaxVerifyVal((max == null || "".equals(max)) ? null : Double.valueOf(max));
						enb.setNum(Integer.valueOf(num));
						optNumFillList.add(enb);
					}
				}
			}
		}
	}

	/** 选项数字填空新增 **/
	public void optNumFillAddInitAction() {
		tsProPoolOpt.setIsNumFill(1);
		ExampoolNumFillBean enb = new ExampoolNumFillBean();
		if (optNumFillList != null && optNumFillList.size() > 0) {
			enb.setNum(getMaxNum(optNumFillList));
		} else {
			enb.setNum(1);
		}
		optNumFillList.add(enb);
	}

	/** 选项数字填空保存 **/
	public void optNumFillSave() {
		if (!beforeOptNumFillSaveAction()) {
			return;
		} else {
			String s = "";
			for (ExampoolNumFillBean efb : optNumFillList) {
				s = s + "_" + efb.getNum().toString() + ",";
				s = s + (efb.getMinVerifyVal() == null ? "" : efb.getMinVerifyVal().toString());
				s = s + "~" + (efb.getMaxVerifyVal() == null ? "" : efb.getMaxVerifyVal().toString()) + ";";
			}
			if (optNumFillList != null && optNumFillList.size() > 1) {
				tsProPoolOpt.setIsMulti(1);
			}
			if (optNumFillList == null || optNumFillList.size() == 0) {
				tsProPoolOpt.setIsNumFill(0);
			}

			tsProPoolOpt.setNumFillScript("".equals(s) ? null : s);
			RequestContext.getCurrentInstance().execute("PF('optNumFillDialog').hide()");
			RequestContext.getCurrentInstance().update(":tabView:editForm:optFillGrid");
		}

	}

	private boolean beforeOptNumFillSaveAction() {
		boolean flag = true;
		if (optNumFillList != null && optNumFillList.size() > 0) {
			for (int i = 0; i < optNumFillList.size(); i++) {
				if (optNumFillList.get(i).getNum() == null) {
					JsfUtil.addErrorMessage("选项填空序号不能为空!");
					flag = false;
				}

				if (optNumFillList.get(i).getMaxVerifyVal() == null && optNumFillList.get(i).getMinVerifyVal() == null) {
					JsfUtil.addErrorMessage("选项填空值最大值、选项填空值最小值不能同时为空!");
					flag = false;
				}
				if (optNumFillList.get(i).getMaxVerifyVal() != null && optNumFillList.get(i).getMinVerifyVal() != null
						&& (optNumFillList.get(i).getMaxVerifyVal() < optNumFillList.get(i).getMinVerifyVal())) {
					JsfUtil.addErrorMessage("选项填空值最大值小于填空值最小值!");
					flag = false;
				}
				for (int j = i + 1; j < optNumFillList.size(); j++) {
					if (optNumFillList.get(i).getNum().intValue() == optNumFillList.get(j).getNum().intValue()) {
						JsfUtil.addErrorMessage("选项填空序号" + optNumFillList.get(j).getNum() + "重复！");
						flag = false;
					}
				}
			}
		}
		return flag;
	}

	/** 选项数字填空删除 **/
	public void optNumFillDeleteAction() {
		optNumFillList.remove(this.numFill);
	}

	/** 选项数字填空清空 **/
	public void optNumFillClear() {
		optNumFillList = new ArrayList<ExampoolNumFillBean>(0);
	}

	/** 选项数字填空脚本生成 **/
	private String createNumFillScrt(List<TsProPoolOpt> l) {
		String s = "";
		for (TsProPoolOpt t : l) {
			if (t.getIsNumFill() != null && t.getIsNumFill() == 1 && StringUtils.isNotBlank(t.getNumFillScript())) {
				s += t.getNumFillScript().replace("_", t.getNum() + "_");
			}
		}
		return "".equals(s) ? null : s;
	}

	/** 行标题添加初始化 */
	public void rowtitleAddInitAction() {
		rowtitle = new TbProPoolRowtitle();
		rowtitle.setCreateDate(new Date());
		rowtitle.setCreateManid(sessionData.getUser().getRid());
		rowtitle.setFkByTableId(this.tsProbExampool.getFkByTableId());
		rowtitle.setState(1);
	}

	/** 行标题删除 */
	public void rowtitleDeleteAction() {
		this.tsProbExampool.getFkByTableId().getRowtitles().remove(rowtitle);
	}

	/** 行标题保存 */
	public void rowtitleSaveAction() {
		boolean flag = true;
		if (flag) {
			if (!tsProbExampool.getFkByTableId().getRowtitles().contains(rowtitle)) {
				tsProbExampool.getFkByTableId().getRowtitles().add(rowtitle);
			}
			RequestContext.getCurrentInstance().execute("PF('RowtitleDialog').hide()");
			RequestContext.getCurrentInstance().update(":tabView:editForm:rowtitleTable");
		}
	}

	/** 行标题清空 */
	public void rowtitleClearAction() {
		tsProbExampool.getFkByTableId().getRowtitles().clear();
	}

	/** 列定义添加初始化 */
	public void colsdefineAddInitAction() {
		colsdefine = new TbProPoolColsdefine();
		colsdefine.setCreateDate(new Date());
		colsdefine.setCreateManid(sessionData.getUser().getRid());
		colsdefine.setFkByTableId(this.tsProbExampool.getFkByTableId());
		colsdefine.setState(1);
		tempColsdefine = null;
	}

	/** 列定义添加初始化 */
	public void colsdefineModInitAction() {
		tempColsdefine = new TbProPoolColsdefine();
		try {
			ObjectCopyUtil.copyProperties(colsdefine, tempColsdefine);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/** 列定义修改取消 */
	public void colsdefineModCancelAction() {
		if (tempColsdefine != null) {
			try {
				ObjectCopyUtil.copyProperties(tempColsdefine, colsdefine);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	/** 列定义删除 */
	public void colsdefineDeleteAction() {
		this.tsProbExampool.getFkByTableId().getColsdefines().remove(colsdefine);
	}

	/** 列定义保存 */
	public void colsdefineSaveAction() {
		boolean flag = true;
		if(null != this.colsdefine && StringUtils.isNotBlank(this.colsdefine.getExecScript()) &&
				!this.validateSingleScript(this.colsdefine.getExecScript())){
			JsfUtil.addErrorMessage("脚本有误！");
			flag = false;
		}
		if (flag) {
			if (!tsProbExampool.getFkByTableId().getColsdefines().contains(colsdefine)) {
				tsProbExampool.getFkByTableId().getColsdefines().add(colsdefine);
			}
			RequestContext.getCurrentInstance().execute("PF('ColsdefineDialog').hide()");
			RequestContext.getCurrentInstance().update(":tabView:editForm:colsdefineTable");
		}
	}

	/** 列定义清空 */
	public void colsdefineClearAction() {
		tsProbExampool.getFkByTableId().getColsdefines().clear();
	}

	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder();
		sb.append(" from TsProbExampool t where 1=1");
		// 题型
		if (searchQusetType != null) {
			sb.append(" and t.questType=").append(searchQusetType);
		}
		// 题目类型
		if (StringUtils.isNotBlank(searchTsProbExamLevelNp)) {
			sb.append(" and t.tsSimpleCodeByTypeId.codeLevelNo like '").append(searchTsProbExamLevelNp).append("%'");
		}
		// 状态
		if (searchState != null && searchState.length == 1) {
			sb.append(" and t.state=").append(searchState[0]);
		}
		// 编码
		if (StringUtils.isNotBlank(searchTitleDesc)) {
			sb.append(" and t.titleDesc like :titleDesc ");
			this.paramMap.put("titleDesc", "%" + this.searchTitleDesc.trim() + "%");
		}
		// 授权的题库问卷码表数据
		if (null != queTypeList && queTypeList.size() > 0) {
			String s = "";
			for (TsSimpleCode t : queTypeList) {
				s += "," + t.getRid().toString();
			}
			sb.append(" and t.tsSimpleCodeByTypeId.rid in (");
			sb.append(s.replaceFirst(",", "")).append(")");
		} else {
			sb.append(" and t.tsSimpleCodeByTypeId.rid in (-1)");
		}
		sb.append(" order by t.tsSimpleCodeByTypeId.codeNo");
		sb.append(",t.qesLevelCode ,t.questType");
		StringBuilder sb1 = new StringBuilder();
		StringBuilder countSB = new StringBuilder();
		sb1.append("select t ").append(sb);
		countSB.append("select count(t.rid) ").append(sb);

		return new String[] { sb1.toString(), countSB.toString() };
	}

	@Override
	public void processData(List<?> list) {
		String[] s = null;
		int a = 0;
		for (int j = 0; j < list.size(); j++) {
			TsProbExampool t = (TsProbExampool) list.get(j);
			s = StringUtils.split(t.getTsSimpleCodeByTypeId().getCodeLevelNo(), ".");
			a = s.length - 1;
			t.setExamTypeDot(a);
			s = StringUtils.split(t.getQesLevelCode(), ".");
			a = s.length - 1;
			t.setPoolDot(a);
		}
	}

	/**
	 * <p>方法描述： 验证表格列脚本 </p>
	 * @MethodAuthor： pw 2022/9/30
	 **/
	private boolean validateScript(List<TbProPoolColsdefine> colList){
		boolean flag = true;
		if(!CollectionUtils.isEmpty(colList)){
			for(TbProPoolColsdefine col : colList){
				if(StringUtils.isNotBlank(col.getExecScript())){
					if(!this.validateSingleScript(col.getExecScript())){
						//清空脚本
						col.setExecScript(null);
						/*JsfUtil.addErrorMessage(col.getColDesc()+"脚本有误！");
						flag = false;*/
					}
				}
			}
		}
		return flag;
	}

	/**
	 * <p>方法描述： 验证单个脚本 </p>
	 * @MethodAuthor： pw 2022/9/30
	 **/
	private boolean validateSingleScript(String execScript){
		if(StringUtils.isBlank(execScript)){
			return true;
		}
		String[] scriptArr = execScript.split("#@#@#@#@#");
		if(scriptArr.length == 0){
			return false;
		}
		for(String script : scriptArr){
			String tmp = script.trim();
			if(!tmp.endsWith("}")){
				return false;
			}
			String eventStr = tmp.substring(0,tmp.indexOf(";"));
			if(eventStr.indexOf(":") == -1){
				return false;
			}
			String[] tmpArr = eventStr.split(":");
			if(tmpArr.length != 2 || StringUtils.isBlank(tmpArr[0]) || StringUtils.isBlank(tmpArr[1]) ||
					!tmpArr[1].contains("(") || !tmpArr[1].contains(")")){
				return false;
			}
			String funStr = tmp.substring(tmp.indexOf(";")+1);
			if(!funStr.trim().startsWith("function ")){
				return false;
			}
			funStr = funStr.substring(funStr.indexOf("function")+8);
			if(!funStr.trim().startsWith(tmpArr[1].substring(0,tmpArr[1].indexOf("(")).trim())){
				return false;
			}
		}
		return true;
	}

	public Integer getSearchQusetType() {
		return searchQusetType;
	}

	public void setSearchQusetType(Integer searchQusetType) {
		this.searchQusetType = searchQusetType;
	}

	public String getSearchProTypeName() {
		return searchProTypeName;
	}

	public void setSearchProTypeName(String searchProTypeName) {
		this.searchProTypeName = searchProTypeName;
	}

	public String getSearchTitleDesc() {
		return searchTitleDesc;
	}

	public void setSearchTitleDesc(String searchTitleDesc) {
		this.searchTitleDesc = searchTitleDesc;
	}

	public Integer[] getSearchState() {
		return searchState;
	}

	public void setSearchState(Integer[] searchState) {
		this.searchState = searchState;
	}

	public TreeNode getTreeNode() {
		return treeNode;
	}

	public void setTreeNode(TreeNode treeNode) {
		this.treeNode = treeNode;
	}

	public List<QuestType> getQuestTypeList() {
		return questTypeList;
	}

	public void setQuestTypeList(List<QuestType> questTypeList) {
		this.questTypeList = questTypeList;
	}

	public List<SelectItem> getProTypeList() {
		return proTypeList;
	}

	public void setProTypeList(List<SelectItem> proTypeList) {
		this.proTypeList = proTypeList;
	}

	public TsProbExampool getTsProbExampool() {
		return tsProbExampool;
	}

	public void setTsProbExampool(TsProbExampool tsProbExampool) {
		this.tsProbExampool = tsProbExampool;
	}

	public TsProPoolOpt getTsProPoolOpt() {
		return tsProPoolOpt;
	}

	public void setTsProPoolOpt(TsProPoolOpt tsProPoolOpt) {
		this.tsProPoolOpt = tsProPoolOpt;
	}

	public Integer getUploadTag() {
		return uploadTag;
	}

	public void setUploadTag(Integer uploadTag) {
		this.uploadTag = uploadTag;
	}

	public List<TsProTempl> getTemplList() {
		return templList;
	}

	public void setTemplList(List<TsProTempl> templList) {
		this.templList = templList;
	}

	public String getTemplOpts() {
		return templOpts;
	}

	public void setTemplOpts(String templOpts) {
		this.templOpts = templOpts;
	}

	public String getSelectedCodeType() {
		return selectedCodeType;
	}

	public void setSelectedCodeType(String selectedCodeType) {
		this.selectedCodeType = selectedCodeType;
	}

	public Integer getSelecedSystemType() {
		return selecedSystemType;
	}

	public void setSelecedSystemType(Integer selecedSystemType) {
		this.selecedSystemType = selecedSystemType;
	}

	public List<SelectItem> getSystemTypes() {
		return systemTypes;
	}

	public void setSystemTypes(List<SelectItem> systemTypes) {
		this.systemTypes = systemTypes;
	}

	public List<SelectItem> getCodeTypeList() {
		return codeTypeList;
	}

	public void setCodeTypeList(List<SelectItem> codeTypeList) {
		this.codeTypeList = codeTypeList;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public String getQesLevelCode() {
		return qesLevelCode;
	}

	public void setQesLevelCode(String qesLevelCode) {
		this.qesLevelCode = qesLevelCode;
	}

	public TreeNode getTsProbExamtypeTree() {
		return tsProbExamtypeTree;
	}

	public void setTsProbExamtypeTree(TreeNode tsProbExamtypeTree) {
		this.tsProbExamtypeTree = tsProbExamtypeTree;
	}

	public TreeNode getSelectedProTypeNode() {
		return selectedProTypeNode;
	}

	public void setSelectedProTypeNode(TreeNode selectedProTypeNode) {
		this.selectedProTypeNode = selectedProTypeNode;
	}

	public String getTsProbExamtypeName() {
		return tsProbExamtypeName;
	}

	public void setTsProbExamtypeName(String tsProbExamtypeName) {
		this.tsProbExamtypeName = tsProbExamtypeName;
	}

	public Integer getTsProbExamtypeId() {
		return tsProbExamtypeId;
	}

	public void setTsProbExamtypeId(Integer tsProbExamtypeId) {
		this.tsProbExamtypeId = tsProbExamtypeId;
	}

	public Integer getSearchTsProbExamtypeId() {
		return searchTsProbExamtypeId;
	}

	public void setSearchTsProbExamtypeId(Integer searchTsProbExamtypeId) {
		this.searchTsProbExamtypeId = searchTsProbExamtypeId;
	}

	public TreeNode getSearchTsProbExamtypeTree() {
		return searchTsProbExamtypeTree;
	}

	public void setSearchTsProbExamtypeTree(TreeNode searchTsProbExamtypeTree) {
		this.searchTsProbExamtypeTree = searchTsProbExamtypeTree;
	}

	public TreeNode getSelectedSearchProTypeNode() {
		return selectedSearchProTypeNode;
	}

	public void setSelectedSearchProTypeNode(TreeNode selectedSearchProTypeNode) {
		this.selectedSearchProTypeNode = selectedSearchProTypeNode;
	}

	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	public Integer getFactorId() {
		return factorId;
	}

	public void setFactorId(Integer factorId) {
		this.factorId = factorId;
	}

	public Integer getActType() {
		return actType;
	}

	public void setActType(Integer actType) {
		this.actType = actType;
	}

	public boolean isIfHaveFather() {
		return ifHaveFather;
	}

	public void setIfHaveFather(boolean ifHaveFather) {
		this.ifHaveFather = ifHaveFather;
	}

	public List<SelectItem> getFatherNumList() {
		return fatherNumList;
	}

	public void setFatherNumList(List<SelectItem> fatherNumList) {
		this.fatherNumList = fatherNumList;
	}

	public List<String> getDependNum() {
		return dependNum;
	}

	public void setDependNum(List<String> dependNum) {
		this.dependNum = dependNum;
	}

	public Integer getIfshowConfig() {
		return ifshowConfig;
	}

	public void setIfshowConfig(Integer ifshowConfig) {
		this.ifshowConfig = ifshowConfig;
	}

	public String getOptValue() {
		return optValue;
	}

	public void setOptValue(String optValue) {
		this.optValue = optValue;
	}

	public String getFatherTitleDesc() {
		return fatherTitleDesc;
	}

	public void setFatherTitleDesc(String fatherTitleDesc) {
		this.fatherTitleDesc = fatherTitleDesc;
	}

	public List<TsSimpleCode> getSimpleCodeList() {
		return simpleCodeList;
	}

	public void setSimpleCodeList(List<TsSimpleCode> simpleCodeList) {
		this.simpleCodeList = simpleCodeList;
	}

	public String getOptImg() {
		return optImg;
	}

	public void setOptImg(String optImg) {
		this.optImg = optImg;
	}

	public ExampoolNumFillBean getNumFill() {
		return numFill;
	}

	public void setNumFill(ExampoolNumFillBean numFill) {
		this.numFill = numFill;
	}

	public List<ExampoolNumFillBean> getNumFillList() {
		return numFillList;
	}

	public void setNumFillList(List<ExampoolNumFillBean> numFillList) {
		this.numFillList = numFillList;
	}

	public List<ExampoolNumFillBean> getOptNumFillList() {
		return optNumFillList;
	}

	public void setOptNumFillList(List<ExampoolNumFillBean> optNumFillList) {
		this.optNumFillList = optNumFillList;
	}

	public TsProPoolOpt getTempTsProPoolOpt() {
		return tempTsProPoolOpt;
	}

	public void setTempTsProPoolOpt(TsProPoolOpt tempTsProPoolOpt) {
		this.tempTsProPoolOpt = tempTsProPoolOpt;
	}

	public TbProPoolRowtitle getRowtitle() {
		return rowtitle;
	}

	public void setRowtitle(TbProPoolRowtitle rowtitle) {
		this.rowtitle = rowtitle;
	}

	public TbProPoolColsdefine getColsdefine() {
		return colsdefine;
	}

	public void setColsdefine(TbProPoolColsdefine colsdefine) {
		this.colsdefine = colsdefine;
	}

	public List<TsProbExampool> getRelQueList() {
		return relQueList;
	}

	public void setRelQueList(List<TsProbExampool> relQueList) {
		this.relQueList = relQueList;
	}

	public List<SelectItem> getRelQueOptList() {
		return relQueOptList;
	}

	public void setRelQueOptList(List<SelectItem> relQueOptList) {
		this.relQueOptList = relQueOptList;
	}

	public List<String> getSelectedRelQueOpts() {
		return selectedRelQueOpts;
	}

	public void setSelectedRelQueOpts(List<String> selectedRelQueOpts) {
		this.selectedRelQueOpts = selectedRelQueOpts;
	}

	public String getSelectedRelQueCode() {
		return selectedRelQueCode;
	}

	public void setSelectedRelQueCode(String selectedRelQueCode) {
		this.selectedRelQueCode = selectedRelQueCode;
	}

	public Integer getIsImg() {
		return isImg;
	}

	public void setIsImg(Integer isImg) {
		this.isImg = isImg;
	}

	public List<TsSimpleCode> getHardLevelList() {
		return hardLevelList;
	}

	public void setHardLevelList(List<TsSimpleCode> hardLevelList) {
		this.hardLevelList = hardLevelList;
	}
}
