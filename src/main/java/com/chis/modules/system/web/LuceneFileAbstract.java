package com.chis.modules.system.web;

import java.util.List;
import java.util.Map;

import com.chis.modules.system.interfaces.ILuceneFile;
import com.chis.modules.system.logic.LucenePojo;
import com.chis.modules.system.utils.LoadSearchUtils;

public abstract class LuceneFileAbstract implements ILuceneFile{

	/**
	 * 将查询到的数据封装到Set与Map中
	 * 
	 * @param list
	 * @param fileSet
	 * @param fileMap
	 */
	public void addDataToMap(Map<String, LucenePojo> fileMap) {
		if (null != fileMap) {
			List<LucenePojo> list = this.findSearchList();
			if (null != list && list.size() > 0) {
				LoadSearchUtils.addDataToMap(list, fileMap);
			}
		}
	}
}
