package com.chis.modules.system.web;

import com.chis.common.utils.*;

import java.util.*;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.modules.system.utils.HolidayUtil;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.ToggleSelectEvent;
import org.primefaces.event.UnselectEvent;
import org.springframework.util.CollectionUtils;

import com.chis.modules.system.entity.*;
import com.chis.modules.system.entity.TdPublishNoticeUnit;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.logic.MenuVO;
import com.chis.modules.system.logic.NoticePojo;
import com.chis.modules.system.logic.PorTalToDoTasks;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.TdPublishNoticeService;
import com.chis.modules.system.service.PorTalToDoTasksService;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.io.FileInputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 *  <p>类描述：门户基类</p>
 * @ClassAuthor hsj 2021/11/30 13:38
 */
@ManagedBean(name = "tsUserDeskShowBean")
@ViewScoped
public class TsUserDeskShowBean extends FacesBean {
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private TdPublishNoticeService noticeService = SpringContextHolder.getBean(TdPublishNoticeService.class);
	private static final long serialVersionUID = 1L;
	private SystemModuleServiceImpl systemService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	private PorTalToDoTasksService  toDoTasksService = SpringContextHolder.getBean(PorTalToDoTasksService.class);
    /**一级码表目录*/
    private List<TsSimpleCode> noticeOneList;
    /**页面通知信息对象*/
    private List<NoticePojo> noticePojoList;
    private Integer headNoticeNum;
    /** 门户头部快捷菜单最多显示个数*/
    private Integer headMenuNum;
    /** 门户头部自定义快捷菜单*/
    List<Object[]> ownerMenuList;
    /**通知公告rid*/
    private String noticerRid;
    /**详情页实体*/
    private TdPublishNotice publishNotice;
    /** 当前操作的附件对象 */
    private TdPublishNoticeAnnex curNoticeAnnex;
    /** 通知发布附件的下载对象 */
    private StreamedContent noiceAnnexStreamedContent;

    /**待办任务列表*/
    private List<PorTalToDoTasks> toDoTasks;
    /** 审核级别 */
    private String checkLevel;
    /** 菜单层级map key menuLevelNo valye 对应层级的所有菜单 */
    private Map<String,List<String[]>> userMenuLevelMap;
    /** 弹出框中显示的菜单列表 */
    private List<String[]> showDeskMenuList;
    /** 数据库存储的快捷菜单 */
    private List<String[]> souceDeskMenuList;
    /** 所有选中的菜单 */
    private List<String[]> selectedDeskMenuList;
    /** 当前页选中的菜单 绑定dataTable选中行数据 地址会变化 */
    private List<String[]> curPageSelectedDeskMenuList;
    /** 用户拥有的所有菜单 */
    private List<String[]> allUserMenuList;
    /** 快捷菜单中菜单名称查询条件 */
    private String searchMenuName;
    /**代办-疑似职业病未报告数-配置文件中报告打印日期之后的数据*/
    private String bhkBeginDate;
    /**代办-疑似职业病未报告数-查询日期显示*/
    private Integer receiveDate;
    /**代办-疑似职业病未报告数-疑似职业病上报期限*/
    private String yszybNostdTime;

    private String colorTest="<font class='redText'>num</font>";

    public TsUserDeskShowBean(){
        /**通知条数*/
        noticerRid= JsfUtil.getRequest().getParameter("rid");
        headNoticeNum = null ==  PropertyUtils.getValue("headNoticeNum") ? 10 : Integer.valueOf(PropertyUtils.getValue("headNoticeNum")) ;
        headMenuNum = null ==  PropertyUtils.getValue("headMenuNum") ? 6 : Integer.valueOf(PropertyUtils.getValue("headMenuNum")) ;
        this.checkLevel = PropertyUtils.getValueWithoutException("checkLevel");
        //获取配置文件：上报审核数据开始日期
        this.bhkBeginDate = PropertyUtils.getValueWithoutException("reportCard.bhkBeginDate");
        //日期显示
        String val = PropertyUtils.getValueWithoutException("receiveDate");
        this.receiveDate =StringUtils.isNotBlank(val)?Integer.valueOf(val):null;
        //疑似职业病上报期限
        this.yszybNostdTime = PropertyUtils.getValueWithoutException("yszybNostdTime");
        //通知初始化
        initNotice();
        initQuickMenu();
        //待办任务
        initToDoTasks();
        //通知公告详情
        initNoticeDetail();
        //初始化层级菜单信息
        this.initDeskMenuInfo();
    }
    /**
     *  <p>方法描述：通知信息初始化</p>
     * @MethodAuthor hsj
     */
    private void initNotice() {
        noticeOneList = new ArrayList<>();
        noticePojoList = new ArrayList<>();
        //查询一级码表
        noticeOneList =  commService.findTsSimpleCodesByTypeNo("5546",true);
        if(!CollectionUtils.isEmpty(noticeOneList)){
            for(TsSimpleCode t : noticeOneList){
                NoticePojo noticePojo =new NoticePojo();
                noticePojo.setCode(t.getCodeNo());
                noticePojo.setCodeName(t.getCodeName());
               //通知信息
                List<TdPublishNoticeUnit> tdPublishNoticeUnitList = findTdPublishNoticeUnitByCode(t.getCodeNo());
                noticePojo.setTdPublishNoticeUnits(tdPublishNoticeUnitList);
                noticePojoList.add(noticePojo);
            }
        }
    }
    /**
     *
     * <p>描述：初始化快捷菜单</p>
     *
     *  @Author: 龚哲,2021/12/1 16:23,initQuickMenu
     */
    private void initQuickMenu(){
        //查询自定义桌面菜单
        ownerMenuList = this.findTsUserDeskMenu();
    }

    /**
     *  <p>方法描述：根据一级code查询</p>
     * @MethodAuthor hsj
     */
    private List<TdPublishNoticeUnit> findTdPublishNoticeUnitByCode(String codeNo) {
        StringBuffer hql = new StringBuffer();
        hql.append("select T from TdPublishNoticeUnit T where T.fkByUnitId.rid = ").append(Global.getUser().getTsUnit().getRid());
        hql.append(" and T.fkByMainId.state = 1");
        hql.append(" and  ( T.fkByMainId.fkByPublishTypeId.extendS3 != 1 or T.fkByMainId.fkByPublishTypeId.extendS3 is null )");
        hql.append(" and T.fkByMainId.fkByPublishTypeId.codeLevelNo like '").append(codeNo).append(".%'");
        //按发布日期倒序，发布类型，标题排序
        hql.append(" order by T.fkByMainId.publishDate desc,T.fkByMainId.fkByPublishTypeId.codeNo ,T.fkByMainId.title");
        return commService.findData(hql.toString(),null, 0,headNoticeNum);
    }

    /**
     *
     * <p>描述：查询用户自定义桌面菜单</p>
     *
     *  @Author: 龚哲,2021/12/1 16:01,findTsUserDesk
     */
    private List<Object[]> findTsUserDeskMenu(){
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT T1.RID,T1.MENU_SIMPLE,T1.BIG_ICON,T1.MENU_URI,T1.MENU_CN FROM TS_USER_DESK T LEFT JOIN TS_MENU T1 ON T.MENU_ID = T1.RID WHERE T.USER_ID = ").append(Global.getUser().getRid());
        sb.append(" ORDER BY T.ORDER_NO ");
        return commService.findDataBySQL(sb.toString(),null,0,headMenuNum);
    }
    /**
 	 * <p>方法描述：初始化待办任务</p>
 	 * @MethodAuthor qrr,2021年12月2日,initToDoTasks
     * */
    private void initToDoTasks() {
    	//查询菜单权限
    	this.toDoTasks = new ArrayList<>();
    	List<MenuVO> menuList = this.systemService.selectMenuList(Global.admin(), Global.getUser().getRid());
    	if (!CollectionUtils.isEmpty(menuList)) {
			for (MenuVO vo : menuList) {
				String menuEn = vo.getMenuEn();
				PorTalToDoTasks tasks = new PorTalToDoTasks();
				tasks.setMenuVO(vo);
				int count = 0;
				switch (menuEn) {
				case "heth_zyjkjcsshqkcx":
					//职业健康检查审核情况查询
					count = toDoTasksService.orgToDoTdTjBHkCheck();
					if (count>0) {
						tasks.setXh(1);
						tasks.setInfoMsg("职业健康检查数据审核情况：有"+count+"条数据已退回，请及时处理！");
						this.toDoTasks.add(tasks);
					}
					break;
				case "heth_zyjkjcsjsh":
					//职业健康检查数据审核
                    count = toDoTasksService.orgTdTjBhkDataUnCheckCount(this.checkLevel);
					if (count>0) {
						tasks.setXh(2);
						tasks.setInfoMsg("职业健康检查数据审核：有"+count+"条数据待审核，请及时处理！");
						this.toDoTasks.add(tasks);
					}
					break;
				case "heth_cfwfwyjczsh1":
					//超范围服务预警处置-初审
                    count = toDoTasksService.tdZwyjOtrWarnCount(1);
					if (count>0) {
						tasks.setXh(3);
						tasks.setInfoMsg("超范围服务预警初审：有"+count+"条数据待处理，请尽快处理！");
						this.toDoTasks.add(tasks);
					}
					break;
				case "heth_cfwfwyjczsh2":
					//超范围服务预警处置-终审
                    count = toDoTasksService.tdZwyjOtrWarnCount(2);
					if (count>0) {
						tasks.setXh(4);
						tasks.setInfoMsg("超范围服务预警终审：有"+count+"条数据待处理，请尽快处理！");
						this.toDoTasks.add(tasks);
					}
					break;
                case "heth_yszybbgknew":
                    //疑似职业病报告卡填报
                    if(bhkBeginDate==null){
                        break;
                    }
                    // 开始时间
                    long stime = System.currentTimeMillis();
                    System.out.println("开始用时："+stime);
                    List<Object[]> list=toDoTasksService.findRptWarnCount(bhkBeginDate,receiveDate);
                    if (CollectionUtils.isEmpty(list)) {
                        break;
                    }
                    Date day = HolidayUtil.calOverDate(new Date(), yszybNostdTime);
                    //计算超期数
                    int overTimeNum=0;
                    for (Object[] obj : list) {
                        Date date = (Date) obj[2];
                        if(date.before(day)){
                            overTimeNum+=1;
                        }
                    }
                    tasks.setXh(5);
                    tasks.setInfoMsg("疑似职业病报告卡填报：有"+list.size()+"条数据待处理"+(overTimeNum>0?"，其中超期数"+colorTest.replace("num",String.valueOf(overTimeNum)):"")+"，请尽快处理！");
                    this.toDoTasks.add(tasks);
                    break;
                    case "heth_zyjkrqyjcz_warn":
                        //职业健康人群预警处置
                        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
                        if (tsZone == null || ObjectUtil.isEmpty(tsZone.getRealZoneType()) || StringUtils.isBlank(tsZone.getZoneGb())) {
                            break;
                        }
                        String viewLevel;
                        if (tsZone.getRealZoneType().equals((short) 2)) {
                            viewLevel = "2";
                        } else if (tsZone.getRealZoneType().equals((short) 4)) {
                            viewLevel = "1";
                        } else {
                            break;
                        }
                        String zoneGb = tsZone.getZoneGb();
                        int dataCount = this.toDoTasksService.findWarnUnDisposeData(viewLevel, zoneGb);
                        if (dataCount <= 0) {
                            break;
                        }
                        tasks.setXh(6);
                        tasks.setInfoMsg("职业健康人群预警：有" + dataCount + "条数据待处置，请及时处置！");
                        this.toDoTasks.add(tasks);
                        break;
				default:
					break;
				}
			}
			//排序
			Collections.sort(this.toDoTasks, new Comparator<PorTalToDoTasks>() {
				@Override
				public int compare(PorTalToDoTasks o1, PorTalToDoTasks o2) {
					if (null==o1.getXh()||null==o2.getXh()) {
						return -1;
					}
					return o1.getXh().compareTo(o2.getXh());
				}
			});
		}
	}

    /**
     * <p>方法描述：通知公告详情</p>
     * @MethodAuthor： yzz
     * @Date：2021-12-01
     **/
    public void initNoticeDetail(){
        if (noticerRid != null) {
            publishNotice = noticeService.findPublishNotice(Integer.valueOf(noticerRid));
        }
    }
    /**
     * <p>方法描述：点击通知公告</p>
     * @MethodAuthor： yzz
     * @Date：2021-12-01
     **/
    public void clickNewNoticeLink(){
        if(StringUtils.isNotBlank(noticerRid)){
            noticeService.clickNewNoticeLink(noticerRid);
        }
        //RequestContext.getCurrentInstance().execute("openNewNoticeTab("+noticerRid+")");
    }

    /**
     * <p>方法描述：添加快捷菜单 </p>
     * @MethodAuthor： pw 2023/3/18
     **/
    public void addQuickMenu(){
        this.showDeskMenuList.clear();
        this.selectedDeskMenuList.clear();
        this.curPageSelectedDeskMenuList.clear();
        this.searchMenuName = null;
        if(!CollectionUtils.isEmpty(this.souceDeskMenuList)){
            this.selectedDeskMenuList.addAll(this.souceDeskMenuList);
        }
        if(!CollectionUtils.isEmpty(this.selectedDeskMenuList)){
            this.curPageSelectedDeskMenuList.addAll(this.selectedDeskMenuList);
        }
        if(!CollectionUtils.isEmpty(this.allUserMenuList)){
            this.showDeskMenuList.addAll(this.allUserMenuList);
        }
        RequestContext.getCurrentInstance().execute("PF('MenuSelectDialog').show()");
        RequestContext.getCurrentInstance().update("formView:searchMenuName");
        RequestContext.getCurrentInstance().update("formView:dataTable");
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("formView:dataTable");
        if(null != dataTable){
            //默认每页显示行数
            int pageRow = 10;
            // 第一次出现勾选的当前页的第一行数据下标
            int first = -1;
            if(!CollectionUtils.isEmpty(this.curPageSelectedDeskMenuList)){
                int size = this.showDeskMenuList.size();
                for(int i=0;i<size;i++){
                    if(-1 == first && this.curPageSelectedDeskMenuList.contains(this.showDeskMenuList.get(i))){
                        first = i - i%pageRow;
                        break;
                    }
                }
            }
            dataTable.setFirst(-1 == first ? 0 : first);
            dataTable.setRows(pageRow);
        }
    }

    /**
     * <p>方法描述： 按菜单名称过滤快捷菜单弹出框菜单 </p>
     * @MethodAuthor： pw 2023/3/18
     **/
    public void filterQuickMenu(){
        //定位到第一页
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("formView:dataTable");
        if(null != dataTable){
            dataTable.setFirst(0);
        }
        this.showDeskMenuList.clear();
        if(!CollectionUtils.isEmpty(this.allUserMenuList) && StringUtils.isBlank(this.searchMenuName)){
            this.showDeskMenuList.addAll(this.allUserMenuList);
        }
        if(!CollectionUtils.isEmpty(this.allUserMenuList) && StringUtils.isNotBlank(this.searchMenuName)){
            for(String[] arr : this.allUserMenuList){
                if(arr[2].contains(this.searchMenuName)){
                    this.showDeskMenuList.add(arr);
                }
            }
        }
        this.curPageSelectedDeskMenuList.clear();
        if(!CollectionUtils.isEmpty(this.selectedDeskMenuList)){
            this.curPageSelectedDeskMenuList.addAll(this.selectedDeskMenuList);
        }
        dataTable.setSelection(this.curPageSelectedDeskMenuList);
        RequestContext.getCurrentInstance().update("formView:dataTable");
    }
    
    /**
     * <p>方法描述： 快捷菜单弹出框翻页 监听 </p>
     * @MethodAuthor： pw 2023/3/18
     **/
    public void pageListener(){
        this.curPageSelectedDeskMenuList.clear();
        if(!CollectionUtils.isEmpty(this.selectedDeskMenuList)){
            this.curPageSelectedDeskMenuList.addAll(this.selectedDeskMenuList);
        }
    }

    /**
     * <p>方法描述： 快捷菜单弹出框行选中与取消 监听 </p>
     * @MethodAuthor： pw 2023/3/20
     **/
    public void rowToggleSelectListener(SelectEvent event){
        String[] arr = (String[]) event.getObject();
        this.curPageSelectedDeskMenuList.clear();
        String menuLevelNo = arr[1];
        boolean ifExist = this.selectedDeskMenuList.contains(arr);
        List<String[]> arrList = this.userMenuLevelMap.get(menuLevelNo);
        this.selectedDeskMenuList.removeAll(arrList);
        if(!ifExist){
            this.selectedDeskMenuList.addAll(arrList);
            // 子全选父选中
            this.childSelectJoinFatherSelect(menuLevelNo);
        }else{
            // 反向处理
            List<String[]> removeList = new ArrayList<>();
            if(menuLevelNo.indexOf(".") != -1){
                List<String[]> tmpList = this.userMenuLevelMap.get(menuLevelNo.split("\\.")[0]);
                for(String[] tmpArr : tmpList){
                    String tmpMenuLevelNo = tmpArr[1];
                    if(menuLevelNo.startsWith(tmpMenuLevelNo+".")){
                        removeList.add(tmpArr);
                    }
                }
            }
            if(!CollectionUtils.isEmpty(removeList)){
                this.selectedDeskMenuList.removeAll(removeList);
            }
        }
        if(!CollectionUtils.isEmpty(this.selectedDeskMenuList)){
            this.curPageSelectedDeskMenuList.addAll(this.selectedDeskMenuList);
        }
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("formView:dataTable");
        //取消选中后不更新的问题
        dataTable.setSelection(this.curPageSelectedDeskMenuList);
    }

    /**
     * <p>方法描述： 快捷菜单弹出框checkbox选中 监听 </p>
     * @MethodAuthor： pw 2023/3/18
     **/
    public void rowSelectListener(SelectEvent event){
        String[] arr = (String[]) event.getObject();
        this.curPageSelectedDeskMenuList.clear();
        if(!this.selectedDeskMenuList.contains(arr)){
            String menuLevelNo = arr[1];
            List<String[]> arrList = this.userMenuLevelMap.get(menuLevelNo);
            this.selectedDeskMenuList.removeAll(arrList);
            this.selectedDeskMenuList.addAll(arrList);
            // 子全选父选中
            this.childSelectJoinFatherSelect(menuLevelNo);
        }

        if(!CollectionUtils.isEmpty(this.selectedDeskMenuList)){
            //只是curPageSelectedDeskMenuList中add不好使 出现选中了一个上层菜单 下层菜单未被选中的问题
            this.curPageSelectedDeskMenuList.addAll(this.selectedDeskMenuList);
        }
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("formView:dataTable");
        //选中后不更新的问题
        dataTable.setSelection(this.curPageSelectedDeskMenuList);
    }

    /**
     * <p>方法描述： 控制子项全部选择后 父项默认选中 </p>
     * @MethodAuthor： pw 2023/3/22
     **/
    private void childSelectJoinFatherSelect(String menuLevelNo){
        if(menuLevelNo.indexOf(".") == -1){
            return;
        }
        List<String[]> tmpList = new ArrayList<>();
        String tmpLevelNo = menuLevelNo;
        while((tmpLevelNo = getFatherMenuNo(tmpLevelNo)) != null){
            tmpList.clear();
            List<String[]> strArrList = this.userMenuLevelMap.get(tmpLevelNo);
            if(CollectionUtils.isEmpty(strArrList)){
                continue;
            }
            tmpList.addAll(strArrList);
            tmpList.removeAll(this.selectedDeskMenuList);
            if(CollectionUtils.isEmpty(tmpList)){
                continue;
            }
            //有子项未选择 父项不可被选择
            if(!CollectionUtils.isEmpty(tmpList) && tmpList.size() > 1){
                break;
            }
            this.selectedDeskMenuList.removeAll(strArrList);
            this.selectedDeskMenuList.addAll(strArrList);
        }
    }

    /**
     * <p>方法描述： 获取父菜单MenuNo </p>
     * @MethodAuthor： pw 2023/3/22
     **/
    private String getFatherMenuNo(String menuLevelNo){
        if(menuLevelNo.indexOf(".") == -1){
            return null;
        }
        StringBuffer buffer = new StringBuffer();
        String[] arr = menuLevelNo.split("\\.");
        int size = arr.length - 1;
        for(int i=0;i<size; i++){
            buffer.append(buffer.length() > 0 ? "." : "").append(arr[i]);
        }
        return buffer.toString();
    }

    /**
     * <p>方法描述： 快捷菜单弹出框checkbox取消 监听 </p>
     * @MethodAuthor： pw 2023/3/18
     **/
    public void rowUnselectListener(UnselectEvent event){
        String[] arr = (String[]) event.getObject();
        this.curPageSelectedDeskMenuList.clear();
        String menuLevelNo = arr[1];
        List<String[]> arrList = this.userMenuLevelMap.get(menuLevelNo);
        this.selectedDeskMenuList.removeAll(arrList);
        List<String[]> removeList = new ArrayList<>();
        //反向处理
        if(menuLevelNo.indexOf(".") != -1){
            List<String[]> tmpList = this.userMenuLevelMap.get(menuLevelNo.split("\\.")[0]);
            for(String[] tmpArr : tmpList){
                String tmpMenuLevelNo = tmpArr[1];
                if(menuLevelNo.startsWith(tmpMenuLevelNo+".")){
                    removeList.add(tmpArr);
                }
            }
        }
        if(!CollectionUtils.isEmpty(removeList)){
            this.selectedDeskMenuList.removeAll(removeList);
        }
        if(!CollectionUtils.isEmpty(this.selectedDeskMenuList)){
            this.curPageSelectedDeskMenuList.addAll(this.selectedDeskMenuList);
        }
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("formView:dataTable");
        //取消选中后不更新的问题
        dataTable.setSelection(this.curPageSelectedDeskMenuList);
    }

    /**
     * <p>方法描述： 快捷菜单弹出框全部选中/全部取消 监听</p>
     * @MethodAuthor： pw 2023/3/18
     **/
    public void toggleSelectListener(ToggleSelectEvent event){
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("formView:dataTable");
        if(CollectionUtils.isEmpty(this.showDeskMenuList)){
            //表格无数据
            return;
        }
        if(event.isSelected()){
            //当前datatable选中的项
            List<String[]> list = (List<String[]>)dataTable.getSelection();
            if(!CollectionUtils.isEmpty(list)){
                for(String[] arr : list){
                    //判断下级及子级 全部加入
                    if(!this.selectedDeskMenuList.contains(arr)){
                        String menuLevelNo = arr[1];
                        List<String[]> arrList = this.userMenuLevelMap.get(menuLevelNo);
                        this.selectedDeskMenuList.removeAll(arrList);
                        this.selectedDeskMenuList.addAll(arrList);
                    }
                }
            }
        }else{//取消全选，需要移除当前页的所有元素
            int current = dataTable.getPage();//获取当前页码
            int rows = dataTable.getRows();//每页条数
            //遍历当前表格数据，将当前页的数据移除
            int curFirst = current * rows;//当前页第一个元素下标
            int curLast = Math.min(curFirst + rows - 1, this.showDeskMenuList.size() - 1);//当前页最后一个元素的下标（取相对小的）
            for (int i = curFirst; i <= curLast; i++) {
                String[] arr = this.showDeskMenuList.get(i);
                // 判断下级及子级 全部移除
                if(this.selectedDeskMenuList.contains(arr)){
                    String menuLevelNo = arr[1];
                    List<String[]> arrList = this.userMenuLevelMap.get(menuLevelNo);
                    this.selectedDeskMenuList.removeAll(arrList);
                }
            }
        }
        this.curPageSelectedDeskMenuList.clear();
        if(!CollectionUtils.isEmpty(this.selectedDeskMenuList)){
            this.curPageSelectedDeskMenuList.addAll(this.selectedDeskMenuList);
        }
        //选中与取消选中后不更新的问题
        dataTable.setSelection(this.curPageSelectedDeskMenuList);
    }

    /**
     * <p>方法描述： 快捷菜单弹出框 确定 </p>
     * @MethodAuthor： pw 2023/3/18
     **/
    public void sureUserDesk(){
        try{
            //最末级菜单
            List<String[]> childList = new ArrayList<>();
            List<Integer> menuRidList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(this.selectedDeskMenuList)){
                for(String[] arr : this.selectedDeskMenuList){
                    String menuLevelNo = arr[1];
                    List<String[]> arrList = this.userMenuLevelMap.get(menuLevelNo);
                    if(!CollectionUtils.isEmpty(arrList) && arrList.size() == 1){
                        childList.add(arr);
                        menuRidList.add(Integer.valueOf(arr[0]));
                    }
                    if(childList.size() > this.headMenuNum){
                        JsfUtil.addErrorMessage("快捷菜单最多支持"+this.headMenuNum+"个！");
                        return;
                    }
                }
            }
            //menuRidList
            this.systemService.saveTsUserDeskList(menuRidList);
            //更新
            this.souceDeskMenuList.clear();
            this.souceDeskMenuList.addAll(this.selectedDeskMenuList);
            this.initQuickMenu();
            RequestContext.getCurrentInstance().execute("PF('MenuSelectDialog').hide();");
            RequestContext.getCurrentInstance().update("one");
            RequestContext.getCurrentInstance().update("formView");
            JsfUtil.addSuccessMessage("保存成功！");
        }catch(Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
     * <p>方法描述： 快捷菜单初始化层级菜单信息 </p>
     * @MethodAuthor： pw 2023/3/18
     **/
    private void initDeskMenuInfo(){
        this.showDeskMenuList = new ArrayList<>();
        this.selectedDeskMenuList = new ArrayList<>();
        this.curPageSelectedDeskMenuList = new ArrayList<>();

        List<TsMenu> userMenuList = Global.getUser().getInUseMenus();
        this.userMenuLevelMap = new HashMap<>();
        this.souceDeskMenuList = new ArrayList<>();
        this.allUserMenuList = new ArrayList<>();
        if(CollectionUtils.isEmpty(userMenuList)){
            return;
        }
        //原始 TS_USER_DESK 菜单rid
        List<Integer> alreadExistMenu = this.systemService.findMenuIdListByUserFromUserDesk();
        StringBuffer noBuffer = new StringBuffer();
        //菜单名称buffer
        StringBuffer menuNameBuffer = new StringBuffer();
        //菜单简称buffer
        StringBuffer menuSimpleBuffer = new StringBuffer();
        for(TsMenu tsMenu : userMenuList){
            String menuLevelNo = tsMenu.getMenuLevelNo();
            if(StringUtils.isBlank(menuLevelNo)){
                continue;
            }
            noBuffer.setLength(0);
            String[] objArr = new String[4];
            objArr[0] = tsMenu.getRid().toString();
            objArr[1] = tsMenu.getMenuLevelNo();
            int size = menuLevelNo.split("\\.").length;
            menuNameBuffer.setLength(0);
            menuSimpleBuffer.setLength(0);
            for(int i=1;i<size;i++){
                menuNameBuffer.append("&nbsp;&nbsp;&nbsp;&nbsp;");
                menuSimpleBuffer.append("&nbsp;&nbsp;&nbsp;&nbsp;");
            }
            //显示菜单名称
            menuNameBuffer.append(tsMenu.getMenuCn());
            //显示菜单简称
            menuSimpleBuffer.append(tsMenu.getMenuSimple());
            objArr[2] = menuNameBuffer.toString();
            objArr[3] = menuSimpleBuffer.toString();
            for(String no : menuLevelNo.split("\\.")){
                noBuffer.append(noBuffer.length() == 0 ? "" : ".").append(no);
                String tmpMenuLevel = noBuffer.toString();
                List<String[]> tmpList = this.userMenuLevelMap.get(tmpMenuLevel);
                if(null == tmpList){
                    tmpList = new ArrayList<>();
                }
                tmpList.add(objArr);
                this.userMenuLevelMap.put(tmpMenuLevel, tmpList);
            }
            if(!CollectionUtils.isEmpty(alreadExistMenu) && alreadExistMenu.contains(tsMenu.getRid())){
                this.souceDeskMenuList.add(objArr);
            }
            this.allUserMenuList.add(objArr);
        }

        //子项如果全部选中了 父项选中
        this.exchangeSouceDeskMenuList();
    }

    /**
     * <p>方法描述： 初始化 如果子项全部选中 父项选中 </p>
     * @MethodAuthor： pw 2023/3/22
     **/
    private void exchangeSouceDeskMenuList(){
        if(CollectionUtils.isEmpty(this.souceDeskMenuList)){
            return;
        }
        List<String[]> arrList = new ArrayList<>();
        arrList.addAll(this.souceDeskMenuList);
        for(String[] arr : arrList){
            String menuLevelNo = arr[1];
            if(menuLevelNo.indexOf(".") == -1){
                continue;
            }
            List<String[]> tmpList = new ArrayList<>();
            String tmpLevelNo = menuLevelNo;
            while((tmpLevelNo = getFatherMenuNo(tmpLevelNo)) != null){
                tmpList.clear();
                List<String[]> strArrList = this.userMenuLevelMap.get(tmpLevelNo);
                if(CollectionUtils.isEmpty(strArrList)){
                    continue;
                }
                tmpList.addAll(strArrList);
                tmpList.removeAll(this.souceDeskMenuList);
                if(CollectionUtils.isEmpty(tmpList)){
                    continue;
                }
                //有子项未选择 父项不可被选择
                if(!CollectionUtils.isEmpty(tmpList) && tmpList.size() > 1){
                    break;
                }
                this.souceDeskMenuList.removeAll(strArrList);
                this.souceDeskMenuList.addAll(strArrList);
            }
        }
    }

    public List<TsSimpleCode> getNoticeOneList() {
        return noticeOneList;
    }

    public void setNoticeOneList(List<TsSimpleCode> noticeOneList) {
        this.noticeOneList = noticeOneList;
    }

    public List<NoticePojo> getNoticePojoList() {
        return noticePojoList;
    }

    public void setNoticePojoList(List<NoticePojo> noticePojoList) {
        this.noticePojoList = noticePojoList;
    }

    public Integer getHeadNoticeNum() {
        return headNoticeNum;
    }

    public void setHeadNoticeNum(Integer headNoticeNum) {
        this.headNoticeNum = headNoticeNum;
    }

    public List<Object[]> getOwnerMenuList() {
        return ownerMenuList;
    }

    public void setOwnerMenuList(List<Object[]> ownerMenuList) {
        this.ownerMenuList = ownerMenuList;
    }

    public String getNoticerRid() {
        return noticerRid;
    }

    public void setNoticerRid(String noticerRid) {
        this.noticerRid = noticerRid;
    }

    public TdPublishNotice getPublishNotice() {
        return publishNotice;
    }

    public void setPublishNotice(TdPublishNotice publishNotice) {
        this.publishNotice = publishNotice;
    }

    public TdPublishNoticeAnnex getCurNoticeAnnex() {
        return curNoticeAnnex;
    }

    public void setCurNoticeAnnex(TdPublishNoticeAnnex curNoticeAnnex) {
        this.curNoticeAnnex = curNoticeAnnex;
    }

    /**
     * @Description: 通知发布附件下载
     *
     * @MethodAuthor pw,2021年11月30日
     */
    public synchronized StreamedContent getNoiceAnnexStreamedContent() {
        if(null == this.curNoticeAnnex){
            return null;
        }
        String filePath = this.curNoticeAnnex.getAnnexAddr();
        String fileName = this.curNoticeAnnex.getAnnexName();
        String contentType = "application/pdf";
        try {
            if(null == filePath){
                filePath = "";
            }
            String xnPath = JsfUtil.getAbsolutePath();
            String path="";
            if(filePath.indexOf(xnPath) != -1){
                path = filePath;
            }else{
                path = xnPath + filePath;
            }
            if(StringUtils.isNotBlank(filePath) && StringUtils.isNotBlank(fileName)){
                if(fileName.endsWith(".doc")){
                    contentType = "application/msword";
                }else if(fileName.endsWith(".docx")){
                    contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                }else if(fileName.endsWith(".xls")){
                    contentType = "application/vnd.ms-excel";
                }else if(fileName.endsWith(".xlsx")){
                    contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                }
                this.noiceAnnexStreamedContent = new DefaultStreamedContent(new FileInputStream(path),contentType ,
                        URLEncoder.encode(fileName, "UTF-8"));
            }else{
                this.noiceAnnexStreamedContent = null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return noiceAnnexStreamedContent;
    }

    public void setNoiceAnnexStreamedContent(StreamedContent noiceAnnexStreamedContent) {
        this.noiceAnnexStreamedContent = noiceAnnexStreamedContent;
    }
	public List<PorTalToDoTasks> getToDoTasks() {
		return toDoTasks;
	}
	public void setToDoTasks(List<PorTalToDoTasks> toDoTasks) {
		this.toDoTasks = toDoTasks;
	}

    public String getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(String checkLevel) {
        this.checkLevel = checkLevel;
    }

    public Map<String, List<String[]>> getUserMenuLevelMap() {
        return userMenuLevelMap;
    }

    public void setUserMenuLevelMap(Map<String, List<String[]>> userMenuLevelMap) {
        this.userMenuLevelMap = userMenuLevelMap;
    }

    public List<String[]> getShowDeskMenuList() {
        return showDeskMenuList;
    }

    public void setShowDeskMenuList(List<String[]> showDeskMenuList) {
        this.showDeskMenuList = showDeskMenuList;
    }

    public List<String[]> getSouceDeskMenuList() {
        return souceDeskMenuList;
    }

    public void setSouceDeskMenuList(List<String[]> souceDeskMenuList) {
        this.souceDeskMenuList = souceDeskMenuList;
    }

    public List<String[]> getSelectedDeskMenuList() {
        return selectedDeskMenuList;
    }

    public void setSelectedDeskMenuList(List<String[]> selectedDeskMenuList) {
        this.selectedDeskMenuList = selectedDeskMenuList;
    }

    public List<String[]> getCurPageSelectedDeskMenuList() {
        return curPageSelectedDeskMenuList;
    }

    public void setCurPageSelectedDeskMenuList(List<String[]> curPageSelectedDeskMenuList) {
        this.curPageSelectedDeskMenuList = curPageSelectedDeskMenuList;
    }

    public List<String[]> getAllUserMenuList() {
        return allUserMenuList;
    }

    public void setAllUserMenuList(List<String[]> allUserMenuList) {
        this.allUserMenuList = allUserMenuList;
    }

    public String getSearchMenuName() {
        return searchMenuName;
    }

    public void setSearchMenuName(String searchMenuName) {
        this.searchMenuName = searchMenuName;
    }

    public String getColorTest() {
        return colorTest;
    }

    public void setColorTest(String colorTest) {
        this.colorTest = colorTest;
    }
}
