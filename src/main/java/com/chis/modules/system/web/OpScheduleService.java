package com.chis.modules.system.web;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.quartz.CronExpression;
import org.quartz.Job;
import org.quartz.SchedulerException;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TdOaSchedule;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.job.SecheduleJob;
import com.chis.modules.system.service.ScheduleServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.QuartzManager;

/**
 * 模块说明：操作平台日程
 * 
 * <AUTHOR>
 * @createDate 2016年5月23日
 */
@WebServlet(name = "opScheduleService", value = "/opScheduleService")
public class OpScheduleService extends HttpServlet {
	private static final long serialVersionUID = 4716881208884545892L;

	private ScheduleServiceImpl scheduleService = (ScheduleServiceImpl) SpringContextHolder.getBean(ScheduleServiceImpl.class);
	private SystemModuleServiceImpl systemModuleServiceImpl = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);

	public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		this.doPost(request, response);
	}

	public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setCharacterEncoding("utf-8");
		response.setContentType("text/html");
		PrintWriter out = response.getWriter();
		try {
			/** 1:增加 2：删除 */
			String flag = request.getParameter("flag");
			String userId = request.getParameter("userId");
			String scheId = request.getParameter("scheId");
			if (StringUtils.isNotBlank(flag) && StringUtils.isNotBlank(userId) && StringUtils.isNotBlank(scheId)) {
				if ("1".equals(flag)) {// 增加
					this.addJob(scheId, userId);
				} else if ("2".equals(flag)) {// 删除
					this.delJob(scheId, userId);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			return;
		}
		out.print("1");
		out.close();
	}

	private void delJob(String scheId, String userId) throws Exception {
		String quartzNo = new StringBuilder("schedule_").append(userId).append("_").append(scheId).toString();
		QuartzManager.removeJob(quartzNo);
	}

	private void addJob(String scheId, String userId) {
		TdOaSchedule tdOaSchedule = (TdOaSchedule) scheduleService.find(TdOaSchedule.class, new Integer(scheId));
		TsUserInfo tsUserInfo = systemModuleServiceImpl.findUser(new Integer(userId));
		if (null != tdOaSchedule && null != tsUserInfo) {
			if (tdOaSchedule.getIsRemind().intValue() == 1) {
				String quartzNo = new StringBuilder("schedule_").append(userId).append("_").append(tdOaSchedule.getRid()).toString();
				try {
					/** 先移除，再添加 */
					QuartzManager.removeJob(quartzNo);

					/**
					 * 解析Cron表达式，获取下一次执行的时间是否大于当前时间； 如果大于当前时间则可以添加任务，否则返回
					 */
					CronExpression exp = new CronExpression(tdOaSchedule.getExpressions());
					Date now = new Date();
					now = exp.getNextValidTimeAfter(now);
					if (null != now) {
						Job job = new SecheduleJob();
						Map<String, Object> map = new HashMap<String, Object>();
						map.put("userId", userId);
						map.put("userNo", tsUserInfo.getUserNo());
						map.put("mobile", tsUserInfo.getMbNum());
						map.put("scheduleId", quartzNo);
						map.put("title", tdOaSchedule.getScheduleTitle());
						if (null != tdOaSchedule.getBeginTime()) {
							map.put("time", DateUtils.formatDate(tdOaSchedule.getBeginTime(), "yyyy-MM-dd HH:mm"));
						}
						map.put("remindMtd", null == tdOaSchedule.getRemindMtd() ? "" : tdOaSchedule.getRemindMtd());
						map.put("seasonal", tdOaSchedule.getSeasonal());
						QuartzManager.addJob(quartzNo, job, tdOaSchedule.getExpressions(), map);
					}
				} catch (SchedulerException e0) {
					e0.printStackTrace();
					try {
						QuartzManager.removeJob(quartzNo);
					} catch (SchedulerException e) {
						e.printStackTrace();
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}

	}

}