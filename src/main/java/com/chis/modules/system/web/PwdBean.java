package com.chis.modules.system.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MD5Util;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemServiceImpl;
import org.hibernate.validator.constraints.NotEmpty;
import org.primefaces.context.RequestContext;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.io.Serializable;

/**
 * 密码的受管bean
 * <AUTHOR>
 */
@ManagedBean(name="pwdBean")
@ViewScoped
public class PwdBean extends FacesBean implements Serializable {

	private static final long serialVersionUID = -3740326227684495169L;
    /**存在session中的对象*/
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);

    private CommServiceImpl commService = (CommServiceImpl)SpringContextHolder.getBean(CommServiceImpl.class);
	@NotEmpty(message="新的密码不允许为空！")
//    @Length(min = 6,message = "输入密码长度最少不低于6位数！")
	private String password;
    @NotEmpty(message="确认密码不允许为空！")
//    @Length(min = 6,message = "输入密码长度最少不低于6位数！")
    private String password2;
    @NotEmpty(message="确认旧的密码不允许为空！")
//    @Length(min = 6,message = "旧密码长度最少不低于6位数！")
    private String password0;
    private String dialogName;
    private String encryptPassword0;
    private String encryptPassword1;
    private String encryptPassword2;
    /**密码修改天数*/
    private String pwsLiveDay;

    private SystemServiceImpl systemService = (SystemServiceImpl)SpringContextHolder.getBean(SystemServiceImpl.class);

	public PwdBean() {
        innit();
	}

    private void innit(){
        password="";
        password2="";
        password0="";
        //密码有效期天数
        this.pwsLiveDay = commService.findParamValue("PWS_LIVE_DAY");
        if(StringUtils.isBlank(pwsLiveDay)){
            this.pwsLiveDay="90";
        }
    }
    /**
     * 菜单桌面上的公共弹出Dialog方法
     */
    public void showDialogAction(){
    	password0="";
    	password="";
        password2="";
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.execute(dialogName+".show()");
        requestContext.update(":dialogDeskForm2:"+dialogName+"Grid");
    }

    /**
     * 头部修改密码方法
     */
    public void showDialogHeadAction(){
        dialogName="PwdDialog";
        showDialogAction();
    }
    /**
     * 密码修改方法
     * @return
     */
    public void saveAction(){
    	 TsUserInfo user = systemService.find(TsUserInfo.class, sessionData.getUser().getRid());
        //判断旧密码是否者正确
       if(null != user &&  !user.getPassword().equalsIgnoreCase(new MD5Util().getMD5ofStr(this.password0))){
            JsfUtil.addErrorMessage("旧的密码不正确！");
            return;
        }
        RequestContext requestContext = RequestContext.getCurrentInstance();
        if (!password.equals(password2)){
            JsfUtil.addErrorMessage("输入的新的密码与确认密码不一致！");
            return;
        }

        if (!StringUtils.validPassword(this.password)) {
            JsfUtil.addErrorMessage("密码应为8-16位，必须包含大小写字母、数字和特殊字符；不能包含连续3位顺序或逆序或重复的数字、字母、特殊字符！");
            return;
        }

        //根据用户信息修改密码
        try {
            systemService.updUserPwdById(sessionData.getUser().getRid(), new MD5Util().getMD5ofStr(this.password),this.pwsLiveDay);
            FacesMessage mess = new FacesMessage("密码修改成功，请牢记您的新密码！");
            FacesContext.getCurrentInstance().addMessage("Successful", mess);
            innit();
            RequestContext.getCurrentInstance().execute(dialogName+".hide()");
        }catch (Exception e){
            FacesMessage mess = new FacesMessage("操作失败！");
            FacesContext.getCurrentInstance().addMessage("Failure", mess);
            return;
        }
    }

    /**
     * 修改密码（加密）
     */
    public void changePwdAction() {
        String check = JsfUtil.getRequestParameterMap().get("check");
        if (StringUtils.isBlank(this.encryptPassword0)) {
            JsfUtil.addErrorMessage("请输入旧的密码！");
            return;
        }
        if (StringUtils.isBlank(this.encryptPassword1)) {
            JsfUtil.addErrorMessage("请输入新的密码！");
            return;
        }
        if (StringUtils.isBlank(this.encryptPassword2)) {
            JsfUtil.addErrorMessage("请输入确认密码！");
            return;
        }

        TsUserInfo user = systemService.find(TsUserInfo.class, sessionData.getUser().getRid());
        //判断旧密码是否者正确
        if (null != user && !user.getPassword().equalsIgnoreCase(this.encryptPassword0)) {
            JsfUtil.addErrorMessage("旧的密码不正确！");
            return;
        }
        if ("1".equals(check)) {
            JsfUtil.addErrorMessage("密码应为8-16位字符，必须包含大小写字母、数字和特殊字符；不能包含连续3位顺序或逆序或重复的数字、字母、特殊字符！");
            return;
        }
        if ("3".equals(check)){
            JsfUtil.addErrorMessage("输入的新的密码不能与旧的密码一致！");
            return;
        }
        if (!encryptPassword1.equals(encryptPassword2)) {
            JsfUtil.addErrorMessage("输入的新的密码与确认密码不一致！");
            return;
        }
        //根据用户信息修改密码
        try {
            systemService.updUserPwdById(sessionData.getUser().getRid(), this.encryptPassword1.toUpperCase(),this.pwsLiveDay);
            FacesMessage mess = new FacesMessage("密码修改成功，请牢记您的新密码！");
            FacesContext.getCurrentInstance().addMessage("Successful", mess);
            innit();
            RequestContext.getCurrentInstance().execute(dialogName + ".hide()");
        } catch (Exception e) {
            FacesMessage mess = new FacesMessage("操作失败！");
            FacesContext.getCurrentInstance().addMessage("Failure", mess);
        }
    }

    /**
     * <p>方法描述：包含大小写字母及数字且在6-16位</p>
 	 * 
 	 * @MethodAuthor rcj,2019年3月26日,isLetterDigit
     *
     */
    public static boolean isLetterDigit(String str) {
        boolean isDigit = false;//定义一个boolean值，用来表示是否包含数字
        boolean isLetter = false;//定义一个boolean值，用来表示是否包含字母
        for (int i = 0; i < str.length(); i++) {
            if (Character.isDigit(str.charAt(i))) {   //用char包装类中的判断数字的方法判断每一个字符
                isDigit = true;
            } else if (Character.isLetter(str.charAt(i))) {  //用char包装类中的判断字母的方法判断每一个字符
                isLetter = true;
            }
        }
        String regex = "^(?=.*?[a-z])(?=.*?[A-Z])(?=.*?[0-9])(?=.*?[!~@#$%^&*()_+\\-=\\\\\\|/.><,:;\"'{}\\[\\]`])[a-zA-Z0-9!~@#$%^&*()_+\\-=\\\\\\|/.><,:;\"'{}\\[\\]`]{8,16}";
        boolean isRight = isDigit && isLetter && str.matches(regex);
        return isRight;
    }
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPassword2() {
        return password2;
    }

    public void setPassword2(String password2) {
        this.password2 = password2;
    }

    public String getDialogName() {
        return dialogName;
    }

    public void setDialogName(String dialogName) {
        this.dialogName = dialogName;
    }

    public String getPassword0() {
        return password0;
    }

    public void setPassword0(String password0) {
        this.password0 = password0;
    }

    public String getEncryptPassword0() {
        return encryptPassword0;
    }

    public void setEncryptPassword0(String encryptPassword0) {
        this.encryptPassword0 = encryptPassword0;
    }

    public String getEncryptPassword1() {
        return encryptPassword1;
    }

    public void setEncryptPassword1(String encryptPassword1) {
        this.encryptPassword1 = encryptPassword1;
    }

    public String getEncryptPassword2() {
        return encryptPassword2;
    }

    public void setEncryptPassword2(String encryptPassword2) {
        this.encryptPassword2 = encryptPassword2;
    }

    public String getPwsLiveDay() {
        return pwsLiveDay;
    }

    public void setPwsLiveDay(String pwsLiveDay) {
        this.pwsLiveDay = pwsLiveDay;
    }
}
