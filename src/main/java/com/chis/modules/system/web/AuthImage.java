package com.chis.modules.system.web;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Random;

import javax.imageio.ImageIO;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
/**
 * 验证码生成器
 * <AUTHOR>
 */
@WebServlet(name="authImage",value="/authImage")
public class AuthImage extends HttpServlet {

	private static final long serialVersionUID = 4716881208884545892L;

	public void init() throws ServletException {
		super.init();
	}

	Color getRandColor(int fc, int bc) {
		Random random = new Random();
		if (fc > 255)
			fc = 255;
		if (bc > 255)
			bc = 255;
		int r = fc + random.nextInt(bc - fc);
		int g = fc + random.nextInt(bc - fc);
		int b = fc + random.nextInt(bc - fc);
		return new Color(r, g, b); 
	}

	public void service(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		int width=70;
		int height=20;
		int pointx=width/4;
		int pointy=height-4;
		// 定义图像buffer
		BufferedImage buffImg = new BufferedImage(width, height,
				BufferedImage.TYPE_INT_RGB);
		Graphics2D g = buffImg.createGraphics();
		// 创建一个随机数生成器类
		Random random = new Random();
		// 将图像填充为白色
		g.setColor(Color.WHITE);
		g.fillRect(0, 0, width, height);
		// 创建字体，字体的大小应该根据图片的高度来定。
		Font font = new Font("Fixedsys", Font.BOLD, 20);
		// 设置字体。
		g.setFont(font);
		// 画边框。
		g.setColor(Color.BLACK);
		g.drawRect(0, 0, width - 1, height - 1);
		// 随机产生干扰线，使图象中的认证码不易被其它程序探测到。
//		for (int i = 0; i < 40; i++) {
//			g.setColor(new Color(random.nextInt(255), random.nextInt(50), random.nextInt(1)));
//			int x = random.nextInt(width);
//			int y = random.nextInt(height);
//			int xl = random.nextInt(7);
//			int yl = random.nextInt(7);
//			g.drawLine(x, y, x + xl, y + yl);
//		}
		// randomCode用于保存随机产生的验证码，以便用户登录后进行验证。
		StringBuffer randomCode = new StringBuffer();
		int red = 0, green = 0, blue = 0;
		// 随机产生codeCount数字的验证码。
		for (int i = 0; i < 4; i++) {
			// 得到随机产生的验证码数字。
			String strRand = String.valueOf(getchar()[random.nextInt(getchar().length)]);
			// 产生随机的颜色分量来构造颜色值，这样输出的每位数字的颜色值都将不同。
			red = random.nextInt(255);
			green = random.nextInt(100);
			blue = random.nextInt(255);
			// 用随机产生的颜色将验证码绘制到图像中。
			g.setColor(new Color(red, green, blue));
			g.drawString(strRand, i*pointx+2, pointy+1);
			// 将产生的四个随机数组合在一起。
			randomCode.append(strRand);
		}
		String sessionString = randomCode.toString();

		request.getSession().setAttribute("sessionString", sessionString);
		ImageIO.write(buffImg, "jpeg", response.getOutputStream());
	}

	public char[] getchar() {
		char[] codeSequence = { 'A', 'B', 'C', '7', 'D', 'a', 'E', 'G', 'p',
				'8', 'h', 'K', 'L', 'w', 'N', 'P', 'Q', 'R', 'S', '2',
				'T', 'U', 'V', 'W', '3', 'X', 'v', 'Z', 'b', 'c', '4', 'd',
				'e', 'f', 'J', 'k', 'm', 'n', 'H',
				'r', 'M', 's', 't', '6', 'u', 'Y', '5', 'x', 'F', 'z' };
		return codeSequence;
	}

	public void destroy() {
	}
}