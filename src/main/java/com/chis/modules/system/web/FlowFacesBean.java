/*package com.chis.modules.system.web;

import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.chis.activiti.enumn.SupportAssigeeType;
import com.chis.activiti.service.IBusinessService;
import com.chis.activiti.service.IFlowBeanService;
import com.chis.activiti.service.IFlowButtons;
import com.chis.activiti.service.ISetAssignMen;
import com.chis.activiti.service.ISetAssignee;
import com.chis.activiti.service.ISetButtons;
import com.chis.ejb.service.system.ICommService;
import com.chis.web.javabean.FlowContext;
import com.chis.web.util.SessionBeanFactory;

*//**
 * 所有业务需要走流程的基类
 * 
 * <AUTHOR> 2014-12-2
 *//*
public abstract class FlowFacesBean extends FacesBean implements IBusinessService,ISetAssignee, ISetAssignMen {

	*//** 流程任务ID *//*
	protected String taskId;
	*//** 流程业务ID,JSON格式的，对应的POJO类是BusinessIdBean *//*
	protected String businessId;
	*//** ejb session bean *//*
	protected ICommService commService = (ICommService) SessionBeanFactory.getInstance().getPlugin("CommServiceImpl");
	*//** 流程操作的上下文 *//*
	protected FlowContext flowContext;


	public FlowFacesBean() {
		*//**
		 * 设置与流程处理Bean之间的关系
		 *//*
		String manageBeanName = (String) this.getRequestMap().get("manageBeanName");
		if (StringUtils.isNotBlank(manageBeanName)) {
			Object bean = getBean(manageBeanName);
			if (bean instanceof IFlowBeanService) {
				IFlowBeanService beanService = (IFlowBeanService) bean;
				if(null == beanService.getBusinessService()) {
					beanService.setBusinessService(this);
					this.flowContext = (FlowContext) beanService.getFlowContext();
					
					if(null != this.flowContext) {
						this.businessId = this.flowContext.getBusinessIdJSON();
						this.taskId = this.flowContext.getActivitiTaskId();
					}
					beanService.setPrintable(this.supportPrint());
				}
			}
			//将业务模块的按钮提供给流程处理Bean
			if(bean instanceof IFlowButtons && this instanceof ISetButtons) {
				IFlowButtons flowButtons = (IFlowButtons) bean;
				ISetButtons buttonService = (ISetButtons)this;
				flowButtons.setButtonList(buttonService.supportButtons());
			}
			
		}
	}
	

	@Override
	public void afterBackAction() {
	}

	@Override
	public void afterSaveAction() {
	}

	@Override
	public void afterSubmitAction() {
	}
	
    @Override
    public Map<String, Object> addVariables() {
        return null;
    }
	
	@Override
	public String settingAssigneeCondition() {
		return null;
	}
	
	@Override
	public String terminateAction() {
		return null;
	}

	@Override
	public Map<Integer, String> supplyPhoneNum(String userId) {
		if(StringUtils.isNotBlank(userId)) {
			return this.commService.findPhoneNums(userId);
		}
		return null;
	}
	
	@Override
	public String supportTaskMen() {
		return null;
	}
	
	@Override
	public SupportAssigeeType supportType() {
		return SupportAssigeeType.OR;
	}
	
	public abstract boolean supportPrint();

}
*/