package com.chis.modules.system.web;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.List;

@ManagedBean(name = "indusTypeCodeMulitySelectListBean")
@ViewScoped
public class IndusTypeCodeMulitySelectListBean extends CodeMulitySelectListBean {
    private static final long serialVersionUID = -738445454536625063L;
    /**
     * 门类列表
     */
    private List<TsSimpleCode> topTypeList;
    /**
     * 门类对应的大类列表
     */
    private List<TsSimpleCode> fatherTypeList;
    /**
     * 大类对应的中类列表
     */
    private List<TsSimpleCode> middleTypeList;
    /**
     * 门类编码
     */
    private String selectTopCodeNo;
    /**
     * 门类对应的大类编码
     */
    private String selectFatherCodeNo;
    /**
     * 大类对应的中类编码
     */
    private String selectMiddleCodeNo;

    public IndusTypeCodeMulitySelectListBean() {
        this.topTypeList = new ArrayList<>();
        if (CollectionUtils.isEmpty(super.getAllList())) {
            return;
        }
        List<String> codeList = new ArrayList<>();
        for (TsSimpleCode simpleCode : super.getAllList()) {
            String codeNo = StringUtils.isBlank(simpleCode.getCodeNo()) ? null : simpleCode.getCodeNo().trim();
            String codeLevelNo = StringUtils.isBlank(simpleCode.getCodeLevelNo()) ? null :
                    simpleCode.getCodeLevelNo().trim();
            if (null == codeNo || null == codeLevelNo) {
                continue;
            }
            int level = StringUtils.countMatches(codeLevelNo, ".");
            if (0 == level) {
                this.topTypeList.add(simpleCode);
            }
            String[] codeNoArr = codeLevelNo.split("\\.");
            int len = codeNoArr.length;
            for (int i = 0; i < len; i++) {
                String curNo = codeNoArr[i];
                if (len - 1 != i && !codeList.contains(curNo)) {
                    codeList.add(curNo.trim());
                }
            }
        }
    }

    /**
     * 门类选择
     */
    public void onTopCodeTypeSelect() {
        this.fatherTypeList = new ArrayList<>();
        this.middleTypeList = new ArrayList<>();
        this.selectFatherCodeNo = null;
        this.selectMiddleCodeNo = null;
        if (CollectionUtils.isEmpty(super.getAllList()) || StringUtils.isBlank(this.selectTopCodeNo)) {
            searchAction();
            return;
        }
        for (TsSimpleCode simpleCode : this.getAllList()) {
            String codeNo = StringUtils.isBlank(simpleCode.getCodeNo()) ? null : simpleCode.getCodeNo().trim();
            String codeLevelNo = StringUtils.isBlank(simpleCode.getCodeLevelNo()) ? null :
                    simpleCode.getCodeLevelNo().trim();
            if (null == codeNo || null == codeLevelNo || !codeLevelNo.startsWith(this.selectTopCodeNo + ".")) {
                continue;
            }
            int level = StringUtils.countMatches(codeLevelNo, ".");
            if (1 == level) {
                this.fatherTypeList.add(simpleCode);
            }
        }
        searchAction();
    }

    /**
     * 大类选择
     */
    public void onFatherCodeTypeSelect() {
        this.middleTypeList = new ArrayList<>();
        this.selectMiddleCodeNo = null;
        if (CollectionUtils.isEmpty(super.getAllList()) || StringUtils.isBlank(this.selectTopCodeNo) ||
                StringUtils.isBlank(this.selectFatherCodeNo)) {
            searchAction();
            return;
        }
        StringBuilder buffer = new StringBuilder();
        for (TsSimpleCode simpleCode : super.getAllList()) {
            String codeNo = StringUtils.isBlank(simpleCode.getCodeNo()) ? null : simpleCode.getCodeNo().trim();
            String codeLevelNo = StringUtils.isBlank(simpleCode.getCodeLevelNo()) ? null :
                    simpleCode.getCodeLevelNo().trim();
            buffer.setLength(0);
            buffer.append(this.selectTopCodeNo).append(".").append(this.selectFatherCodeNo).append(".");
            if (null == codeNo || null == codeLevelNo || !codeLevelNo.startsWith(buffer.toString())) {
                continue;
            }
            int level = StringUtils.countMatches(codeLevelNo, ".");
            if (2 == level) {
                this.middleTypeList.add(simpleCode);
            }
        }
        searchAction();
    }

    /**
     * 中类选择或者查询
     */
    public void searchAction() {
        super.setDisplayList(new ArrayList<TsSimpleCode>());
        if (CollectionUtils.isEmpty(super.getAllList())) {
            return;
        }
        StringBuilder builder = new StringBuilder();
        if (StringUtils.isNotBlank(this.selectTopCodeNo)) {
            builder.append(this.selectTopCodeNo);
        }
        if (StringUtils.isNotBlank(this.selectFatherCodeNo)) {
            builder.append(".").append(this.selectFatherCodeNo);
        }
        if (StringUtils.isNotBlank(this.selectMiddleCodeNo)) {
            builder.append(".").append(this.selectMiddleCodeNo);
        }
        String preCode = builder.toString();
        for (TsSimpleCode simpleCode : super.getAllList()) {
            String codeLevelNo = simpleCode.getCodeLevelNo();
            String codeName = simpleCode.getCodeName();
            String pym = simpleCode.getSplsht().toUpperCase();
            int level = StringUtils.countMatches(codeLevelNo, ".");
            if (StringUtils.isNotBlank(preCode)) {
                if (0 != level && !codeLevelNo.startsWith(preCode)) {
                    continue;
                }
                if (0 == level && !preCode.trim().equals(codeLevelNo)) {
                    continue;
                }
            }
            boolean flag = false;
            if (StringUtils.isNotBlank(super.getSearchNamOrPy())) {
                if (level < 3) {
                    continue;
                }
                String searchNamOrPy = super.getSearchNamOrPy().trim().toUpperCase();
                if (StringUtils.isNotBlank(codeName) && codeName.contains(searchNamOrPy)) {
                    flag = true;
                }
                if (StringUtils.isNotBlank(pym) && pym.contains(searchNamOrPy)) {
                    flag = true;
                }
            } else {
                flag = true;
            }
            if (flag) {
                this.getDisplayList().add(simpleCode);
            }
        }
    }

    /**
     * 获取对应层级名称
     */
    public String splitCodePath(String codePath, int index) {
        if (StringUtils.isBlank(codePath) || index < 0) {
            return "";
        }
        String[] arr = codePath.split("_");
        if (index >= arr.length) {
            return "";
        } else if (arr.length > 3 && 3 <= index) {
            StringBuilder builder = new StringBuilder();
            for (int i = 3; i < arr.length; i++) {
                builder.append("_").append(arr[i]);
            }
            return builder.substring(1);
        } else {
            return arr[index];
        }
    }

    public List<TsSimpleCode> getTopTypeList() {
        return topTypeList;
    }

    public void setTopTypeList(List<TsSimpleCode> topTypeList) {
        this.topTypeList = topTypeList;
    }

    public List<TsSimpleCode> getFatherTypeList() {
        return fatherTypeList;
    }

    public void setFatherTypeList(List<TsSimpleCode> fatherTypeList) {
        this.fatherTypeList = fatherTypeList;
    }

    public List<TsSimpleCode> getMiddleTypeList() {
        return middleTypeList;
    }

    public void setMiddleTypeList(List<TsSimpleCode> middleTypeList) {
        this.middleTypeList = middleTypeList;
    }

    public String getSelectTopCodeNo() {
        return selectTopCodeNo;
    }

    public void setSelectTopCodeNo(String selectTopCodeNo) {
        this.selectTopCodeNo = selectTopCodeNo;
    }

    public String getSelectFatherCodeNo() {
        return selectFatherCodeNo;
    }

    public void setSelectFatherCodeNo(String selectFatherCodeNo) {
        this.selectFatherCodeNo = selectFatherCodeNo;
    }

    public String getSelectMiddleCodeNo() {
        return selectMiddleCodeNo;
    }

    public void setSelectMiddleCodeNo(String selectMiddleCodeNo) {
        this.selectMiddleCodeNo = selectMiddleCodeNo;
    }
}
