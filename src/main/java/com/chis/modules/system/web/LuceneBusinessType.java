package com.chis.modules.system.web;

/**
 * 模块说明：查询业务类别
 * 
 * <AUTHOR>
 * @createDate 2016年11月2日
 */
public enum LuceneBusinessType {

	SYS_CHAR(1) {
		public String getTypeCN() {
			return "文字";
		}
	},
	
	SYS_PIC(2) {
		public String getTypeCN() {
			return "图片";
		}
	},
	
	SYS_RES(3) {
		public String getTypeCN() {
			return "资源";
		}
	}

	;

	private final Integer typeNo;

	LuceneBusinessType(Integer typeNo) {
		this.typeNo = typeNo;
	}

	public String toString() {
		return String.valueOf(typeNo);
	}

	public Integer getTypeNo() {
		return typeNo;
	}

	public abstract String getTypeCN();

}
