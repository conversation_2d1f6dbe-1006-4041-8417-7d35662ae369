package com.chis.modules.system.web;

import java.io.File;
import java.io.FilenameFilter;
import java.util.*;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.chis.common.utils.*;
import org.primefaces.context.RequestContext;

import com.chis.modules.system.entity.TsMenu;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import org.springframework.util.CollectionUtils;

/**
 * 菜单管理
 * 
 * @editContent 删除buildProcessData()方法，如果查询出来的数据集需要修改，子类不需要再重写buildProcessData()  david 2014-09-04 
 * 
 * <AUTHOR>
 */
@ManagedBean(name="menuBean")
@ViewScoped
public class MenuBean extends FacesSimpleBean implements IProcessData {

	private static final long serialVersionUID = -738445454536625063L;
	/**存在session中的对象*/
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/**ejb session bean*/
	private SystemModuleServiceImpl systemService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	/**菜单对象*/
	private TsMenu tsMenu = new TsMenu();
	/**菜单对象rid*/
	private Integer rid;
    /**查询条件：菜单名称*/
    private String searchMenuName;
    /**查询条件：级别编码*/
    private String searchCode;
    /**图片接受*/
    private List<String> imgUrlList;

    /**大图片接受*/
    private List<String> bigImgUrlList;

    /**记录老菜单编码（修改用）*/
    private String oldMenuEn;
    //查询条件-链接地址
    private String searchMenuUri;
    //查询条件-自检状态
    private String[] searchStates;
    //查询条件-菜单编码
    private String searchMenuEn;
	@PostConstruct
	public void init() {
        //初始化图片下拉列表（获取图片名称）
        imgListInit();
		searchMenuName="";
        this.searchAction();
	}


	/**
	 * 添加初始化
	 */
	public void addInitAction() {
		this.tsMenu = new TsMenu();
        oldMenuEn="";
		this.tsMenu.setCreateDate(new Date());
		this.tsMenu.setCreateManid(sessionData.getUser().getRid());
	}

	/**
	 * 修改初始化
	 */
	public void modInitAction() {
        oldMenuEn=tsMenu.getMenuEn();
		this.tsMenu.setModifyDate(new Date());
		this.tsMenu.setModifyManid(this.sessionData.getUser().getRid());
        if(ObjectUtil.isNull(tsMenu.getIfPop())){
            tsMenu.setIfPop(0);
        }
	}

	/**
	 * 保存
	 */
	public void saveAction() {
        RequestContext requestContext = RequestContext.getCurrentInstance();
        if(verifyData()){
            return;
        }
        this.systemService.saveOrUpdateMenu(this.tsMenu);
        if(null == this.tsMenu.getRid()) {
            this.searchAction();
        }
        requestContext.execute("MenuEditDialog.hide()");
	}

    /**
     *  <p>方法描述：验证</p>
     * @MethodAuthor hsj 2024-01-26 9:45
     */
    private boolean verifyData() {
        boolean flag = Boolean.FALSE;
        if(ObjectUtil.isEmpty(tsMenu.getMenuCn())){
            JsfUtil.addErrorMessage("菜单名称不允许为空！");
            flag = Boolean.TRUE;
        }else {
            boolean isaginTag;
            //判断是否重复
            if(null==tsMenu||null==tsMenu.getRid()){
                tsMenu.setMenuEn(new GB2Alpha().String2Alpha(tsMenu.getMenuCn()).toLowerCase());
                isaginTag=systemService.selMenuEn(null,null, tsMenu.getMenuEn());
            }else {
                isaginTag=systemService.selMenuEn(tsMenu.getRid(), oldMenuEn, tsMenu.getMenuEn());
            }
            if(!isaginTag){
                flag = Boolean.TRUE;
                JsfUtil.addErrorMessage("菜单编码已存在！");
            }
        }
        if(ObjectUtil.isEmpty(tsMenu.getMenuSimple())){
            JsfUtil.addErrorMessage("菜单简称不允许为空！");
            flag = Boolean.TRUE;
        }
        if(ObjectUtil.isEmpty(tsMenu.getMenuLevelNo())){
            JsfUtil.addErrorMessage("结构层次编码不允许为空！");
            flag = Boolean.TRUE;
        }else {
            //唯一
            if(systemService.verifyLevelNo(tsMenu)){
                JsfUtil.addErrorMessage("结构层次编码已存在！");
                flag = Boolean.TRUE;
            }
        }
        if(ObjectUtil.isNull(tsMenu.getIsfunc())){
            JsfUtil.addErrorMessage("请选择是否功能菜单！");
            flag = Boolean.TRUE;
        }else {
            if("0".equals(Convert.toStr(tsMenu.getIsfunc())) && !"#".equals(tsMenu.getMenuUri())){
                JsfUtil.addErrorMessage("是否功能菜单为“否”时链接地址必须为“#”！");
                flag = Boolean.TRUE;
            }
            if("1".equals(Convert.toStr(tsMenu.getIsfunc())) && "#".equals(tsMenu.getMenuUri())){
                JsfUtil.addErrorMessage("是否功能菜单为“是”时链接地址不可为“#”！");
                flag = Boolean.TRUE;
            }
        }
        if(ObjectUtil.isEmpty(tsMenu.getMenuUri())){
            JsfUtil.addErrorMessage("链接地址不允许为空！");
            flag = Boolean.TRUE;
        }
        if(ObjectUtil.isEmpty(tsMenu.getMenuIcon())){
            JsfUtil.addErrorMessage("菜单小图标不允许为空！");
            flag = Boolean.TRUE;
        }
        return flag;
    }

    /**
	 * 删除
	 */
	public void deleteAction() {
		String msg = this.systemService.deleteMenu(this.rid);
		if(StringUtils.isNotBlank(msg)) {
			JsfUtil.addErrorMessage(msg);
		}
        this.searchAction();
	}

	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder(" from TsMenu t where 1=1 ");
        if(StringUtils.isNotBlank(this.searchMenuName)) {
            sb.append(" and ( t.menuCn like :menuName or t.menuSimple like :menuName ) ");
            this.paramMap.put("menuName", "%"+StringUtils.convertBFH(this.searchMenuName.trim())+"%");
        }
        if(StringUtils.isNotBlank(this.searchMenuEn)) {
            sb.append(" and t.menuEn like :searchMenuEn");
            this.paramMap.put("searchMenuEn", "%"+StringUtils.convertBFH(this.searchMenuEn.trim())+"%");
        }
        if(StringUtils.isNotBlank(this.searchCode)) {
            sb.append(" and t.menuLevelNo like :searchCode");
            this.paramMap.put("searchCode", "%"+StringUtils.convertBFH(this.searchCode.trim())+"%");
        }
        if(StringUtils.isNotBlank(this.searchMenuUri)) {
            sb.append(" and t.menuUri like :searchMenuUri");
            this.paramMap.put("searchMenuUri", "%"+StringUtils.convertBFH(this.searchMenuUri.trim())+"%");
        }
        List<Integer>  states= Convert.toList(Integer.class,this.searchStates);
        if(!CollectionUtils.isEmpty(states)) {
            sb.append(" and t.state in (:searchStates)");
            this.paramMap.put("searchStates", states);
        }
		String h2 = "select count(*) " + sb.toString();
		String h1 = "select t " + sb.append(" order by t.menuLevelNo,t.num ");
		return new String[]{h1,h2};
	}

    /**
     * 处理数据
     * @param list 查询出来的结果
     */
    @Override
    public void processData(List<?> list) {
        List<TsMenu> list1=(ArrayList<TsMenu>)list;
        for(TsMenu h:list1){
            if(null!=h.getMenuLevelNo()){
                h.setLevelNum(h.getMenuLevelNo().split("[.]").length);
            }else{
                h.setLevelNum(1);
            }
        }
    }
    /**
     * 获取指定路径下的，所有图片名称及后缀名
     * @param path 路径
     * @return  图片数组
     */
    private String[] imgNameGet(String path){
        File file = new File(path);
        String[] fns = file.list(new FilenameFilter() {
            public boolean accept(File dir, String name) {
                if (name.toLowerCase().indexOf("png")!=-1||name.toLowerCase().indexOf("gif")!=-1||name.toLowerCase().indexOf("jpg")!=-1
                        ||name.toLowerCase().indexOf("bmp")!=-1) {
                    return true;
                } else {
                    return false;
                }
            }
        });
        return fns;
    }

    /**
     * 图片列表初始化
     */
    private void imgListInit(){
        String filePath = new StringBuilder(FileUtils.getWebRootPath()).append("resources/component/quickDesktop/image/16px").toString();
        String[] imgNames=imgNameGet(filePath);
        imgUrlList=new LinkedList<String>();
        if(null!=imgNames&&imgNames.length>0){
            for(int i=0;i<imgNames.length;i++){
                imgUrlList.add(imgNames[i]);
            }
        }
        String bigfilePath = new StringBuilder(FileUtils.getWebRootPath()).append("resources/component/quickDesktop/image/64px").toString();
        String[] bigImgNames=imgNameGet(bigfilePath);
        bigImgUrlList=new LinkedList<String>();
        if(null!=bigImgNames&&bigImgNames.length>0){
            for(int i=0;i<bigImgNames.length;i++){
                bigImgUrlList.add(bigImgNames[i]);
            }
        }

    }
    /**
     *  <p>方法描述：是否功能菜单选择</p>
     * @MethodAuthor hsj 2024-01-25 17:31
     */
    public void changeIsfunc() {
        if ("0".equals(Convert.toStr(tsMenu.getIsfunc()))) {
            tsMenu.setMenuUri("#");
        } else {
            tsMenu.setMenuUri(null);
        }

    }
    /**
     *  <p>方法描述：自检</p>
     * @MethodAuthor hsj 2024-01-26 14:04
     */
    public void selfInspectionAction(){
        StringBuffer hql = new StringBuffer("select t from TsMenu t order by t.menuLevelNo,t.num");
        List<TsMenu> menuList = this.systemService.findByHql(hql.toString());
        if(CollectionUtils.isEmpty(menuList)){
            JsfUtil.addSuccessMessage("无自检数据");
            return;
        }
        List<Integer> succList = new ArrayList<>();
        Map<Integer,String> errRsnMap = new HashMap<>();
        Map<String,List<TsMenu>> levelMap = dealLevelMap(menuList);
        for(TsMenu menu : menuList){
            List<String> errList = verifyMenu(menu,menuList,levelMap);
            if(CollectionUtils.isEmpty(errList)){
                succList.add(menu.getRid());
            }else {
                errRsnMap.put(menu.getRid(), StrUtil.sub(StringUtils.list2string(errList,"；"),0,200));
            }
        }
        try {
            this.systemService.saveState(succList,errRsnMap);
            JsfUtil.addSuccessMessage("本次共自检"+menuList.size()+"条；成功"+succList.size()+"条；失败"+errRsnMap.size()+"条！");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("本次共自检"+menuList.size()+"条；失败"+menuList.size()+"条！");
        }

    }
    /**
     *  <p>方法描述：层级处理</p>
     * @MethodAuthor hsj 2024-01-26 15:22
     */
    private Map<String, List<TsMenu>> dealLevelMap(List<TsMenu> menuList) {
        Map<String, List<TsMenu>> map =new HashMap<>();
        for(TsMenu menu:menuList){
            String menuLevelNo = menu.getMenuLevelNo();
            if(!map.containsKey(menuLevelNo)){
                map.put(menuLevelNo,new ArrayList<TsMenu>());
            }
            if (StringUtils.contains(menuLevelNo, ".")) {
                String code = menuLevelNo.substring(0,menuLevelNo.lastIndexOf("."));
                if(map.containsKey(code)){
                    map.get(code).add(menu);
                }
            }
        }
        return map;
    }

    /**
     *  <p>方法描述：自检单条验证</p>
     * @MethodAuthor hsj 2024-01-26 14:10
     */
    private List<String> verifyMenu(TsMenu menu, List<TsMenu> menuList,Map<String,List<TsMenu>> levelMap) {
        List<String> errList = new ArrayList<>();
        if(ObjectUtil.isEmpty(menu.getMenuCn())){
            errList.add("菜单名称不允许为空");
        }
        if(ObjectUtil.isEmpty(menu.getMenuSimple())){
            errList.add("菜单简称不允许为空");
        }
        if(ObjectUtil.isEmpty(menu.getMenuEn())){
            errList.add("菜单编码不允许为空");
        }else {
           boolean flag = verifyRepetition(menu.getRid(),menu.getMenuEn(),"getMenuEn",menuList);
           if(flag){
               errList.add("菜单编码不允许重复");
           }
        }
        if(ObjectUtil.isEmpty(menu.getMenuLevelNo())){
            errList.add("结构层次编码不允许为空");
        }else {
            String menuLevelNo = menu.getMenuLevelNo();
            //是否唯一
            boolean flag = verifyRepetition(menu.getRid(),menuLevelNo,"getMenuLevelNo",menuList);
            if(flag){
                errList.add("结构层次编码不允许重复");
            }
            //层级验证
            if (StringUtils.contains(menuLevelNo, ".")) {
                //不是第一级，需存在上一级
                String code = menuLevelNo.substring(0,menuLevelNo.lastIndexOf("."));
                if(!levelMap.containsKey(code)){
                    errList.add("结构层次编码上一级不存在");
                }
            }
            //是否存在下一级--若存在：是否为功能菜单必须为【否】
            flag = levelMap.containsKey(menuLevelNo) && levelMap.get(menuLevelNo).size() > 0 && !"0".equals(Convert.toStr(menu.getIsfunc()));
            if(flag){
                errList.add("结构层次编码存在下一级时是否为功能菜单必须为“否”");
            }

        }
        if(ObjectUtil.isNull(menu.getIsfunc())){
            errList.add("是否功能菜单不允许为空");
        }else {
            if("0".equals(Convert.toStr(menu.getIsfunc())) ){
                if(!"#".equals(menu.getMenuUri())){
                    errList.add("是否功能菜单为“否”时链接地址必须为“#”");
                }
                if("1".equals(Convert.toStr(menu.getIfPop()))){
                    errList.add("是否功能菜单为“否”时是否打开新的浏览窗口不可为“是”");
                }

            }else if("1".equals(Convert.toStr(menu.getIsfunc())) && "#".equals(menu.getMenuUri())){
                errList.add("是否功能菜单为“是”时链接地址不可为“#”");
            }
        }
        if(ObjectUtil.isEmpty(menu.getMenuUri())){
            errList.add("链接地址不允许为空");
        }
        if(ObjectUtil.isEmpty(menu.getMenuIcon())){
            errList.add("菜单小图标不允许为空");
        }
        return errList;
    }

    private Boolean verifyRepetition(Integer rid, String value, String methodName, List<TsMenu> menuList) {
        Map<String,List<TsMenu>> map = new HashMap<>();
        GroupUtil.listGroup2Map(menuList,map,TsMenu.class,methodName);
        if(!map.containsKey(value)){
            return false;//不存在重复
        }else {
            if(ObjectUtil.isNull(rid)){
                return true;
            }
            List<TsMenu> list = map.get(value);
            for(TsMenu menu : list){
                if(menu.getRid().compareTo(rid) != 0){
                    return true;
                }
            }
        }
        return false;
    }


    public TsMenu getTsMenu() {
        return tsMenu;
    }

    public void setTsMenu(TsMenu tsMenu) {
        this.tsMenu = tsMenu;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getSearchMenuName() {
        return searchMenuName;
    }

    public void setSearchMenuName(String searchMenuName) {
        this.searchMenuName = searchMenuName;
    }

    public String getSearchCode() {
        return searchCode;
    }

    public void setSearchCode(String searchCode) {
        this.searchCode = searchCode;
    }

    public List<String> getImgUrlList() {
        return imgUrlList;
    }

    public void setImgUrlList(List<String> imgUrlList) {
        this.imgUrlList = imgUrlList;
    }

    public String getOldMenuEn() {
        return oldMenuEn;
    }

    public void setOldMenuEn(String oldMenuEn) {
        this.oldMenuEn = oldMenuEn;
    }

    public List<String> getBigImgUrlList() {
        return bigImgUrlList;
    }

    public void setBigImgUrlList(List<String> bigImgUrlList) {
        this.bigImgUrlList = bigImgUrlList;
    }

    public String getSearchMenuUri() {
        return searchMenuUri;
    }

    public void setSearchMenuUri(String searchMenuUri) {
        this.searchMenuUri = searchMenuUri;
    }

    public String[] getSearchStates() {
        return searchStates;
    }

    public void setSearchStates(String[] searchStates) {
        this.searchStates = searchStates;
    }

    public String getSearchMenuEn() {
        return searchMenuEn;
    }

    public void setSearchMenuEn(String searchMenuEn) {
        this.searchMenuEn = searchMenuEn;
    }
}
