package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * 科室选择公用弹出树形选择框
 *
 * 返回值参数名： selectOffices ：科室集合
 * <AUTHOR>
 * @createDate 2015年4月15日
 */
@ManagedBean(name = "selectOfficeBean")
@ViewScoped
public class SelectOfficeBean extends FacesBean {

	private static final long serialVersionUID = -4193494054818860340L;
	private String officeTitle= "科室选择";
    /** 科室树 */
    private TreeNode treeNode;
    /** 已选择的科室*/
    private TreeNode[] selectedNodes;
    /**存在session中的对象*/
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/** ejb */
	private SystemModuleServiceImpl systemService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	
    public SelectOfficeBean() {
    	String param = JsfUtil.getRequest().getParameter("officeTitle");
    	if(StringUtils.isNotBlank(param))	{
    		officeTitle = param;
    	}
    	List<TsOffice> list= systemService.selAllOfficeList(null,sessionData.getUser().getTsUnit().getRid());
        treeNode = new CheckboxTreeNode("root", null);
        treeSet(treeNode,list);
    }

    /**
     * 公共封装树的方法调用
     * @param tagTreeNode 树节点对象
     * @param list 查询出的数据
     */
    private void treeSet(TreeNode tagTreeNode,List<TsOffice> list){
    	//选中的科室Id集合
    	String selectOfficeIds = JsfUtil.getRequest().getParameter("selectOfficeIds");
        Map<Integer, TreeNode> map = new HashMap<Integer, TreeNode>();
        if(null != list && list.size() > 0) {
            for (TsOffice tsOffice1 : list) {
                if (null == tsOffice1.getParentOffice() || tsOffice1.getParentOffice().getRid() == -1) {
                    TreeNode temNode = new DefaultTreeNode(tsOffice1, tagTreeNode);
                    if( StringUtils.isNotBlank(selectOfficeIds) && (","+selectOfficeIds+",").indexOf(","+tsOffice1.getRid()+",") != -1){
                    	temNode.setSelected(true);
                    }
                    map.put(tsOffice1.getRid(), temNode);
                }
            }
            for (TsOffice tsOffice1 : list) {
                if (null != tsOffice1.getParentOffice() && null != tsOffice1.getParentOffice().getRid() && tsOffice1.getParentOffice().getRid() != -1) {
                    Integer parentId = tsOffice1.getParentOffice().getRid();
                    if (null != parentId && parentId != -1) {
                        TreeNode parentNode = map.get(parentId);
                        if (null != parentNode) {
                            TreeNode node = new DefaultTreeNode(tsOffice1, parentNode);
                            if( StringUtils.isNotBlank(selectOfficeIds) && (","+selectOfficeIds+",").indexOf(","+tsOffice1.getRid()+",") != -1){
                            	node.setSelected(true);
                            }
                            map.put(tsOffice1.getRid(), node);
                        } else {
                            TreeNode temNode = new DefaultTreeNode(tsOffice1, tagTreeNode);
                            if( StringUtils.isNotBlank(selectOfficeIds) && (","+selectOfficeIds+",").indexOf(","+tsOffice1.getRid()+",") != -1){
                            	temNode.setSelected(true);
                            }
                            map.put(tsOffice1.getRid(), temNode);
                        }
                    } else {
                        TreeNode temNode = new DefaultTreeNode(tsOffice1, tagTreeNode);
                        if( StringUtils.isNotBlank(selectOfficeIds) && (","+selectOfficeIds+",").indexOf(","+tsOffice1.getRid()+",") != -1){
                        	temNode.setSelected(true);
                        }
                        map.put(tsOffice1.getRid(), temNode);
                    }
                }
            }
        }
        map.clear();
    }

    /**
     * 选择确定方法
     */
    public void selectAction() {
        Map<String, Object> map = new HashMap<String, Object>();
        List<TsOffice> selectOffices = new ArrayList<TsOffice>();
        if( null != selectedNodes && selectedNodes.length > 0)	{
        	for( TreeNode treeNode : selectedNodes)	{
        		selectOffices.add((TsOffice)treeNode.getData());
        	}
        }
        map.put("selectOffices",selectOffices);
        RequestContext.getCurrentInstance().closeDialog(map);
    }

    /**
     * 关闭
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

	public TreeNode getTreeNode() {
		return treeNode;
	}

	public void setTreeNode(TreeNode treeNode) {
		this.treeNode = treeNode;
	}

	public TreeNode[] getSelectedNodes() {
		return selectedNodes;
	}

	public void setSelectedNodes(TreeNode[] selectedNodes) {
		this.selectedNodes = selectedNodes;
	}

	public String getOfficeTitle() {
		return officeTitle;
	}

	public void setOfficeTitle(String officeTitle) {
		this.officeTitle = officeTitle;
	}
}
