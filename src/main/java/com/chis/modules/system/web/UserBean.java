package com.chis.modules.system.web;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;

import org.hibernate.validator.constraints.NotEmpty;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.TreeNode;
import org.primefaces.model.UploadedFile;
import org.springframework.transaction.annotation.Transactional;

import com.chis.common.utils.FileUtils;
import com.chis.common.utils.IdcUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MD5Util;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.annotation.ZwxLog;
import com.chis.modules.system.entity.TbSysEmp;
import com.chis.modules.system.entity.TsMenu;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsParttimeInfo;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsUserMenu;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.enumn.CodeType;
import com.chis.modules.system.enumn.LogNoEnum;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
* 用户管理
* <AUTHOR>
 *
 * @修改人 wlj
 * @修改时间 2014-10-13
 * @修改内容 增加用户兼职功能
*/
@ManagedBean(name="userBean")
@ViewScoped
public class UserBean extends FacesEditBean {

	private static final long serialVersionUID = -738445454536625063L;
	/**存在session中的对象*/
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/**ejb session bean*/
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	/**用户对象*/
	private TsUserInfo tsUserInfo = new TsUserInfo();
	/**用户对象rid*/
	private Integer rid;
    /**查询条件：地区名称*/
    private String searchZoneName;
    /**查询条件：地区编码*/
    private String searchZoneCode;
    /**查询条件：地区级别*/
    private String searchZoneType;
    /**查询条件：单位*/
    private String searchUnitId;
    /**添加页面：单位下啦*/
    private Map<String, String> searchUnitMap = new HashMap<String, String>(0);
    /**查询条件：科室*/
    private Integer searchOfficeId;
    /**添加页面：科室下啦*/
    private Map<String, Integer> searchOfficeMap = new HashMap<String, Integer>(0);
    /**查询条件：用户姓名*/
    private String searchUsername;
    /**查询条件：用户类型*/
    private Short searchUserType;
    /**添加页面的地区名称*/
    private String editZoneName;
    /**添加页面的地区编码*/
    private String editZoneCode;
    /**添加页面的地区级别*/
    private String editZoneType;
    /**添加页面：单位id*/
    @NotEmpty(message = "请选择单位！")
    private String editUnitId;
    /**添加页面：单位下啦*/
    private Map<String, String> editUnitMap = new HashMap<String, String>(0);
    /**添加页面：科室id*/
    private Integer editOfficeId;
    /**添加页面：科室下啦*/
    private Map<String, Integer> editOfficeMap = new HashMap<String, Integer>(0);
    /**菜单授权的菜单树*/
    private TreeNode treeNode;
    /**菜单授权的已选择的树*/
    private TreeNode[] selectedNodes;
    /**是否是超管*/
    private boolean ifAdmin = Boolean.TRUE;
    /**角色分配*/
    private Map<String, Integer> roleMap = new HashMap<String, Integer>(0);
    /**角色分配:已经选择的角色id*/
    private List<String> selectedRoles = new ArrayList<String>(0);
    /**民族下拉*/
    private Map<String, Integer> empNationMap = new HashMap<String, Integer>(0);
    /**职务下拉*/
    private Map<String, Integer> dutyMap = new HashMap<String, Integer>(0);
    /**职称下拉*/
    private Map<String, Integer> positionMap = new HashMap<String, Integer>(0);
    /**学历下拉*/
    private Map<String, Integer> eduDegreeMap = new HashMap<String, Integer>(0);
    /**政治面貌下拉*/
    private Map<String, Integer> politicsMap = new HashMap<String, Integer>(0);
    /**婚姻状况下拉*/
    private Map<String, Integer> maritalStatusMap = new HashMap<String, Integer>(0);
    /**宗教信仰下拉*/
    private Map<String, Integer> religionMap = new HashMap<String, Integer>(0);
    /**能看到的地区集合*/
    private List<TsZone> zoneList;
    /**兼职信息*/
    private TsParttimeInfo tsParttimeInfo;
    /**兼职信息集合*/
    private List<TsParttimeInfo> tsParttimeInfoList;
    /**兼职页面：科室下啦*/
    private Map<String, Integer> parttimeOfficeMap = new HashMap<String, Integer>(0);
    /**兼职科室id*/
    private Integer parttimeOfficeId;
    /**兼职职务id*/
    private Integer parttimeDutyId;
    /**删除的兼职信息*/
    private TsParttimeInfo tempParttimeInfo;
    /*人员属性*/
    private Integer psnPropId;
    /*人员属性列表*/
    private List<SelectItem> psnPropIdList;
    /*虚拟路径*/
    private String xnPath;
    /*临时路劲*/
    private List<String> psnsignList;
    /*用户状态*/
    private String[] searchUserState;

    /**
     * 要初始化的码表类型
     */
    private static final String CODE_TYPE_NOS = "'"+CodeType.MZ+"','"+CodeType.ZHW+"','"+CodeType.ZHCH+"','"+
            CodeType.XL+"','"+CodeType.ZHZHMM+"','"+CodeType.HYZHK+"','"+CodeType.ZJXY+"'";

	public UserBean() {
        this.init();
    }

	public void init() {
        TsUserInfo tsUserInfo = this.sessionData.getUser();
        if(!tsUserInfo.getUserNo().equals(Constants.ADMIN)) {
            ifAdmin = Boolean.FALSE;
        }
        super.ifSQL = Boolean.TRUE;
        /**
         * 地区初始化
         */
        if(null == this.zoneList || this.zoneList.size() <=0) {
            this.zoneList = this.commService.findZoneListNew(this.ifAdmin, sessionData.getUser().getTsUnit().getTsZone().getZoneCode(),null,"6");
        }
        if(StringUtils.isNotBlank(JsfUtil.getRequest().getParameter("ZoneCode"))){
            this.initNewCondition();
            /**
             * 初始化单位
             */
            this.searchUnitMap = this.filterUnit(this.searchZoneCode, this.searchZoneType);
            if(StringUtils.isNotBlank(searchUnitId)){
                onSearchUnitChange();
            }
        }else{
            this.initSearchCondition();
            /**
             * 初始化单位
             */
            this.searchUnitMap = this.filterUnit(this.searchZoneCode, this.searchZoneType);
            /**
             * 初始化科室
             */
            this.searchOfficeMap = new HashMap<String, Integer>(0);
            searchUserState = new String[]{"1"};
        }
        //初始化人员属性
        this.initpropIdList();
        //初始化虚拟路径
        xnPath = JsfUtil.getAbsolutePath();
        this.searchAction();
	}

    private void initNewCondition(){
        searchZoneCode = JsfUtil.getRequest().getParameter("ZoneCode");
        searchZoneName = JsfUtil.getRequest().getParameter("ZoneName");
        searchZoneType = JsfUtil.getRequest().getParameter("ZoneType");
        searchUnitId = JsfUtil.getRequest().getParameter("UnitId");
        String offid = JsfUtil.getRequest().getParameter("OfficeId");
        if(StringUtils.isNotBlank(offid)){
            searchOfficeId = Integer.valueOf(offid);
        }
        searchUsername = JsfUtil.getRequest().getParameter("Username");
        String type = JsfUtil.getRequest().getParameter("UserType");
        if(StringUtils.isNotBlank(type)){
            searchUserType = Short.valueOf(type);
        }
        String sta = JsfUtil.getRequest().getParameter("UserState");
        if(StringUtils.isNotBlank(sta)){
            char c[] = sta.toCharArray();
            searchUserState = new String[c.length];
            for(int i = 0 ; i < c.length ; i++){
                searchUserState[i] = "" + c[i];
            }
        }

    }

    private void initpropIdList(){
        this.psnPropIdList = new ArrayList<SelectItem>();
        List<TsSimpleCode> list = commService.findSimpleCodesByTypeId("'2008'");
        if(list != null && list.size() > 0){
            for(TsSimpleCode t : list){
                psnPropIdList.add(new SelectItem(t.getRid(),t.getCodeName()));
            }
        }
    }

    /**
     * 初始化查询条件
     */
    private void initSearchCondition() {
        this.searchUserType = (short)1;


        TsUserInfo user = this.sessionData.getUser();
        this.searchZoneCode = user.getTsUnit().getTsZone().getZoneCode();
        this.searchZoneName = user.getTsUnit().getTsZone().getZoneName();
        this.searchZoneType = user.getTsUnit().getTsZone().getZoneType().toString();


    }

    /**
     * 查询条件，地区树选择事件
     */
    public void onSearchNodeSelect() {
        this.searchUnitId = null;
        this.searchUnitMap = this.filterUnit(this.searchZoneCode, this.searchZoneType);

        this.searchOfficeMap = new HashMap<String, Integer>(0);
        this.searchOfficeId = null;
    }

    /**
     * 查询条件：单位下啦change事件
     */
    public void onSearchUnitChange() {
        this.searchOfficeMap = this.filterOffice(this.searchUnitId);
    }

    @Override
    public void addInit() {
        TsUserInfo user = sessionData.getUser();
        //初始化一些条件
        this.tsUserInfo = new TsUserInfo();
        this.tsUserInfo.setUserType((short)1);
        this.tsUserInfo.setCreateDate(new Date());
        this.tsUserInfo.setCreateManid(this.sessionData.getUser().getRid());
        this.tsUserInfo.setPassword(new MD5Util().getMD5ofStr(PropertyUtils.getValue("initialPassword")));
        this.tsUserInfo.setIfModpsw((short)0);
        this.tsUserInfo.setIfReveal((short)1);

        this.rid = null;
        this.editUnitId = null;
        this.editOfficeId = null;

        this.editZoneCode = user.getTsUnit().getTsZone().getZoneCode();
        this.editZoneName = user.getTsUnit().getTsZone().getZoneName();
        this.editZoneType = user.getTsUnit().getTsZone().getZoneType().toString();

        /**
         * 初始化单位
         */
        this.editUnitMap = this.filterUnit(this.editZoneCode, this.editZoneType);

        /**
         * 初始化科室
         */
        this.editOfficeMap = new HashMap<String, Integer>(0);

        TbSysEmp tbSysEmp = new TbSysEmp();
        tbSysEmp.setCreateDate(new Date());
        tbSysEmp.setCreateManid(this.sessionData.getUser().getRid());
        this.tsUserInfo.setTbSysEmp(tbSysEmp);

        /**
         * 码表初始化
         */
        if(null == this.empNationMap || this.empNationMap.size() == 0) {
            this.initSimpleCodeMap();
        }

        //初始化兼职列表
        this.tsParttimeInfoList = new ArrayList<TsParttimeInfo>();

        //初始化人员属性
        this.psnPropId = null;

        //初始化电子签名临时图片
        this.psnsignList = new ArrayList<String>();

    }

    /**
     * 初始化码表
     */
    private void initSimpleCodeMap() {
        /**民族下拉  1001*/
        this.empNationMap = new LinkedHashMap<String, Integer>();
        /**职务下拉  1002*/
        this.dutyMap = new LinkedHashMap<String, Integer>(0);
        /**职称下拉  1003*/
        this.positionMap = new LinkedHashMap<String, Integer>(0);
        /**学历下拉  1004*/
        this.eduDegreeMap = new LinkedHashMap<String, Integer>(0);
        /**政治面貌下拉  1005*/
        this.politicsMap = new LinkedHashMap<String, Integer>(0);
        /**婚姻状况下拉  1006*/
        this.maritalStatusMap = new LinkedHashMap<String, Integer>(0);
        /**宗教信仰下拉  1007*/
        this.religionMap = new LinkedHashMap<String, Integer>(0);

        List<TsSimpleCode> list = this.commService.findSimpleCodesByTypeId(CODE_TYPE_NOS);
        if(null != list && list.size() > 0) {
            for(TsSimpleCode t: list) {
                if(CodeType.MZ.getTypeNo().equals(t.getCodeTypeNo())) {
                    this.empNationMap.put(t.getCodeName(), t.getRid());
                }else if(CodeType.ZHW.getTypeNo().equals(t.getCodeTypeNo())) {
                    this.dutyMap.put(t.getCodeName(), t.getRid());
                }else if(CodeType.ZHCH.getTypeNo().equals(t.getCodeTypeNo())) {
                    this.positionMap.put(t.getCodeName(), t.getRid());
                }else if(CodeType.XL.getTypeNo().equals(t.getCodeTypeNo())) {
                    this.eduDegreeMap.put(t.getCodeName(), t.getRid());
                }else if(CodeType.ZHZHMM.getTypeNo().equals(t.getCodeTypeNo())) {
                    this.politicsMap.put(t.getCodeName(), t.getRid());
                }else if(CodeType.HYZHK.getTypeNo().equals(t.getCodeTypeNo())) {
                    this.maritalStatusMap.put(t.getCodeName(), t.getRid());
                }else if(CodeType.ZJXY.getTypeNo().equals(t.getCodeTypeNo())) {
                    this.religionMap.put(t.getCodeName(), t.getRid());
                }
            }
        }

    }

    /**
     * 根据地区刷单位
     * @param zoneCode 地区编码
     * @param zoneType 地区级别
     * @return 单位集合
     */
    private Map<String, String> filterUnit(String zoneCode, String zoneType) {
        TsUserInfo user = this.sessionData.getUser();
        Map<String, String> map = new LinkedHashMap<String, String>();
        List<TsUnit> list = this.systemModuleService.findUnitByZoneIdNew(this.ifAdmin, user.getTsUnit().getTsZone().getZoneCode(),
                user.getTsUnit().getRid(), zoneCode, zoneType);
        if(null != list && list.size() > 0) {
            for(TsUnit t:list) {
                map.put(t.getUnitname(), t.getRid().toString());
            }
        }
        return map;
    }

    /**
     * 添加界面的 地区选中事件
     */
    public void onNodeSelect() {
        this.editUnitId = null;
        this.editUnitMap = this.filterUnit(this.editZoneCode, this.editZoneType);

        this.editOfficeMap = new HashMap<String, Integer>(0);
        this.editOfficeId = null;
    }

    /**
     * 添加界面：单位下啦change事件
     */
    public void onUnitChange() {
        this.editOfficeMap = this.filterOffice(this.editUnitId);
    }

    /**
     * 根据单位过滤科室
     * @param unitId 单位ID
     * @return 科室下拉map
     */
    private Map<String, Integer> filterOffice(String unitId) {
        Map<String, Integer> map = new LinkedHashMap<String, Integer>(0);
        if(StringUtils.isNotBlank(unitId)) {
            List<TsOffice> list = this.systemModuleService.findOfficeByUnitId(Integer.valueOf(unitId));
            if(null != list && list.size() > 0) {
                for(TsOffice t:list) {
                    map.put(t.getOfficename(), t.getRid());
                }
            }
        }
        return map;
    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {
        this.tsUserInfo = this.systemModuleService.findUser(this.rid);
        this.tsUserInfo.setModifyDate(new Date());
        this.tsUserInfo.setModifyManid(this.sessionData.getUser().getRid());

        TbSysEmp emp = this.tsUserInfo.getTbSysEmp();
        if(null == emp) {
            emp = new TbSysEmp();
            emp.setCreateDate(new Date());
            emp.setCreateManid(this.sessionData.getUser().getRid());
        }else {
            emp.setModifyDate(new Date());
            emp.setModifyManid(this.sessionData.getUser().getRid());
        }
        this.tsUserInfo.setTbSysEmp(emp);

        this.editZoneCode = this.tsUserInfo.getTsUnit().getTsZone().getZoneCode();
        this.editZoneType = this.tsUserInfo.getTsUnit().getTsZone().getZoneType().toString();
        this.editZoneName = this.tsUserInfo.getTsUnit().getTsZone().getZoneName();
        this.editUnitId = this.tsUserInfo.getTsUnit().getRid().toString();
        if(null != this.tsUserInfo.getTbSysEmp().getTsOffice()) {
            this.editOfficeId = this.tsUserInfo.getTbSysEmp().getTsOffice().getRid();
        }else {
            this.editOfficeId = null;
        }

        /**
         * 初始化单位
         */
        this.editUnitMap = this.filterUnit(this.editZoneCode, this.editZoneType);

        /**
         * 初始化科室
         */
        this.editOfficeMap = this.filterOffice(this.editUnitId);

        /**
         * 码表初始化
         */
        if(null == this.empNationMap || this.empNationMap.size() == 0) {
            this.initSimpleCodeMap();
        }
        //兼职信息初始化
        if(null != tsUserInfo.getTbSysEmp() && null != tsUserInfo.getTbSysEmp().getTsParttimeInfoList()){
            tsParttimeInfoList = this.tsUserInfo.getTbSysEmp().getTsParttimeInfoList();
        }
        //人员初始化
        TsSimpleCode tsSimpleCode = this.tsUserInfo.getTbSysEmp().getPsnProp();
        if(tsSimpleCode!=null){
            this.psnPropId = this.tsUserInfo.getTbSysEmp().getPsnProp().getRid();
        }else{
            this.psnPropId = null;
        }
        this.psnsignList = new ArrayList<String>();

    }

	/**
	 * 保存
     *
     * @修改人 wlj
     * @修改时间 2014-10-13
     * @修改内容 保存兼职内容
	 */
	public void saveAction() {
        if(this.tsUserInfo.getUserType().intValue() == 1) {
            //内部用户
            if(null == this.editOfficeId) {
                JsfUtil.addErrorMessage("tabView:editForm:editOfficeListOneMenu","请选择科室！");
                return;
            }
            if(tsUserInfo!=null && StringUtils.isNotBlank(tsUserInfo.getMbNum())&&!pattern(Constants.MOBILE_REGEX, this.tsUserInfo.getMbNum())){
            	 JsfUtil.addErrorMessage("tabView:editForm:mbNum","请输入正确的手机号码！");
            	return;
            }
            if(tsUserInfo!=null && StringUtils.isNotBlank(tsUserInfo.getEmail())&&!pattern(Constants.EMAIL_REGEX, this.tsUserInfo.getEmail())) {
                JsfUtil.addErrorMessage("tabView:editForm:email","请输入正确的邮箱地址！");
                return;
            }
            if(null == this.tsUserInfo.getTbSysEmp().getEmpNation()) {
                JsfUtil.addErrorMessage("tabView:editForm:empNation","请选择民族！");
                return;
            }
            if(StringUtils.isBlank(this.tsUserInfo.getTbSysEmp().getEmpCode())) {
                JsfUtil.addErrorMessage("tabView:editForm:empCode","职工编号不允许为空！");
                return;
            }
            if(StringUtils.isNotBlank(this.tsUserInfo.getTbSysEmp().getIdc())) {
            	String checkIDC = IdcUtils.checkIDC(this.tsUserInfo.getTbSysEmp().getIdc());
                if(StringUtils.isNotBlank(checkIDC)) {
                    JsfUtil.addErrorMessage(checkIDC);
                    return;
                }
            }else {
                this.tsUserInfo.getTbSysEmp().setIdc(null);
            }
            if(null != this.tsUserInfo.getTbSysEmp().getBirthday() && null != this.tsUserInfo.getTbSysEmp().getWorkDay()) {
                if(this.tsUserInfo.getTbSysEmp().getWorkDay().before(this.tsUserInfo.getTbSysEmp().getBirthday())) {
                    JsfUtil.addErrorMessage("tabView:editForm:birthday","出生日期不能大于入职日期！");
                    return;
                }
            }
            if(null != this.tsUserInfo.getTbSysEmp().getRegularDay() && null != this.tsUserInfo.getTbSysEmp().getWorkDay()) {
                if(this.tsUserInfo.getTbSysEmp().getRegularDay().before(this.tsUserInfo.getTbSysEmp().getWorkDay())) {
                    JsfUtil.addErrorMessage("tabView:editForm:workDay","入职日期不能大于转正日期！");
                    return;
                }
            }
            //设置兼职信息
            if(tsUserInfo.getUserType() == 1){
                for(TsParttimeInfo t : tsParttimeInfoList){
                    if(t.getTsOffice().getRid().equals(editOfficeId)){
                        JsfUtil.addErrorMessage("兼职科室不允许与原科室相同！");
                        return;
                    }
                }
                this.tsUserInfo.getTbSysEmp().setTsParttimeInfoList(tsParttimeInfoList);
            }
            if(null != this.editOfficeId) {
                this.tsUserInfo.getTbSysEmp().setTsOffice(new TsOffice(this.editOfficeId));
            }
            this.tsUserInfo.getTbSysEmp().setEmpName(this.tsUserInfo.getUsername());
            //人员属性
            if(null != this.psnPropId) {
                this.tsUserInfo.getTbSysEmp().setPsnProp(new TsSimpleCode(psnPropId));
            }
            //同步手机号
            this.tsUserInfo.getTbSysEmp().setMbNum(this.tsUserInfo.getMbNum());
        }else {
            //外部用户
            this.tsUserInfo.setTbSysEmp(null);
        }

        if(StringUtils.isNotBlank(this.editUnitId)) {
            this.tsUserInfo.setTsUnit(new TsUnit(Integer.valueOf(this.editUnitId)));
        }


        String msg;
        if(null != this.tsUserInfo.getRid()) {
        	tsUserInfo.setUploadTag(0);
            msg = this.systemModuleService.saveOrUpdateUser(this.tsUserInfo);
        }else {
        	tsUserInfo.setIsAdd(0);
        	tsUserInfo.setUploadTag(0);
            msg = this.systemModuleService.saveOrUpdateUser(this.tsUserInfo);
        }

        if(StringUtils.isBlank(msg)) {
            //保存成功后删除电子签名文件
            deleteDiskFile();
            this.searchAction();
            this.backAction();
        }else {
            JsfUtil.addErrorMessage(msg);
            if(this.tsUserInfo.getUserType().intValue() == 2) {
                TbSysEmp t =new TbSysEmp();
                t.setCreateDate(new Date());
                t.setCreateManid(this.sessionData.getUser().getRid());
                this.tsUserInfo.setTbSysEmp(t);
                RequestContext requestContext = RequestContext.getCurrentInstance();
                requestContext.execute("empBlock.show()");
            }
        }
	}

    /**
     * 字符串匹配
     * @param regEx 正则表达式
     * @param value 要匹配的字符串
     * @return 匹配成功返回true
     */
    private static boolean pattern(String regEx, String value) {
        Pattern p=Pattern.compile(regEx);
        Matcher m=p.matcher(value);
        return m.find();
    }

	/**
	 * 删除
	 */
	public void deleteAction() {
		String msg = this.systemModuleService.deleteUser(this.rid);
		if(StringUtils.isNotBlank(msg)) {
			JsfUtil.addErrorMessage(msg);
            return;
		}
        this.searchAction();
	}

    /**
     * 密码初始化
     */
    public void pwdInitAction() {
        this.systemModuleService.initUserPwd(this.rid);
        this.tsUserInfo.setPassword(new MD5Util().getMD5ofStr(PropertyUtils.getValue("initialPassword")));
        this.tsUserInfo.setIfModpsw((short) 0);
        this.systemModuleService.updateUserLogTimes(tsUserInfo.getUserNo());
        JsfUtil.addSuccessMessage("密码初始化成功！");
    }

    /**
     * 停用
     */
    public void stopAction() {
        this.systemModuleService.updateUserState(this.rid, 0);
    }

    /**
     * 启用
     */
    public void startAction() {
        this.systemModuleService.updateUserState(this.rid, 1);
    }

	/**
	 * 分配角色初始化
	 */
	public void fpRoleInitAction() {
        /**
         * 初始化可选角色:
         * 1.自己拥有的角色
         * 2.自己单位拥有的角色
         */
        if(null == this.roleMap || this.roleMap.size() == 0) {
            this.roleMap = this.systemModuleService.findRoleMapICanSee(this.sessionData.getUser().getRid(), this.sessionData.getUser().getTsUnit().getRid());
        }
        this.selectedRoles = this.systemModuleService.findRoleListIHave(this.rid);
	}

    /**
     * 分配角色保存
     */
    public void fpRoleAction() {
        this.systemModuleService.grantRolesToUser(this.rid, this.selectedRoles);
    }

    /**
     * 构建菜单树
     * @param levelNo 菜单层级编码
     * @param levelNoSet 二级以及以上的菜单的层级编码集合
     * @param menuMap 菜单map
     * @param parentNode 上级树节点
     * @param selectedMenuMap 已有的菜单map
     */
    private void addChildNode(String levelNo, Set<String> levelNoSet, Map<String, TsMenu> menuMap, TreeNode parentNode, Map<Integer, Integer> selectedMenuMap) {
        int level = StringUtils.countMatches(levelNo, ".");
        for(String ln: levelNoSet) {
            if(StringUtils.countMatches(ln, ".")==(level+1) && StringUtils.startsWith(ln,levelNo+".")) {
                TsMenu t = menuMap.get(ln);
                TreeNode node = new CheckboxTreeNode(menuMap.get(ln), parentNode);
                if(null != selectedMenuMap.get(t.getRid())) {
                    node.setSelected(true);
                }
                this.addChildNode(ln, levelNoSet, menuMap, node, selectedMenuMap);
            }
        }
    }

    /**
     * 根据树节点，将其与其父节点的对象ID放到集合中
     * @param menuSet 菜单ID集合
     * @param node 树节点，内容存的是菜单对象
     */
    private void chooseMenuIds(Set<Integer> menuSet, TreeNode node) {
        if(null != node && !(node.equals(this.treeNode))) {
            TsMenu t = (TsMenu) node.getData();
            menuSet.add(t.getRid());
            this.chooseMenuIds(menuSet, node.getParent());
        }
    }

    public void menuRedirectAction(){
        //转向
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("/webapp/system/roleMenuEdit.faces");
            sb.append("?ph=1&userid=").append(rid);
            sb.append("&from=/webapp/system/userList.faces?ph=1");
            if(StringUtils.isNotBlank(searchZoneCode) && StringUtils.isNotBlank(searchZoneName) && StringUtils.isNotBlank(searchZoneType)){
                sb.append("@ZoneCode=").append(searchZoneCode).append("@ZoneName=").append(searchZoneName).append("@ZoneType=").append(searchZoneType);
            }
            if(StringUtils.isNotBlank(searchUnitId)){
                sb.append("@UnitId=").append(searchUnitId);
            }
            if(searchOfficeId != null){
                sb.append("@OfficeId=").append(searchOfficeId);
            }
            if(StringUtils.isNotBlank(searchUsername)){
                sb.append("@Username=").append(searchUsername);
            }
            if(searchUserType != null){
                sb.append("@UserType=").append(searchUserType);
            }
            if(searchUserState != null && searchUserState.length > 0){
                StringBuilder sn = new StringBuilder();
                for(String s : searchUserState){
                    sn.append(s);
                }
                sb.append("@UserState=").append(sn.toString());
            }
            FacesContext.getCurrentInstance().getExternalContext().redirect(sb.toString());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

	/**
	 * 分配菜单初始化
	 */
	public void menuInitAction() {
        TsUserInfo tsUserInfo = sessionData.getUser();
        List<TsMenu> allMenuList = this.systemModuleService.findMenuList(this.ifAdmin, tsUserInfo.getRid(), tsUserInfo.getTsUnit().getRid());
        this.treeNode = new CheckboxTreeNode("root", null);

        if(null != allMenuList && allMenuList.size() > 0) {
            Set<String> firstLevelNoSet = new LinkedHashSet<String>();  //只有第一层
            Set<String> levelNoSet = new LinkedHashSet<String>();   //没有第一层
            Map<String, TsMenu> menuMap = new HashMap<String, TsMenu>();    //所有菜单
            Map<Integer, Integer> selectedMenuMap = new HashMap<Integer, Integer>();    //已有的菜单

            this.tsUserInfo = this.systemModuleService.findUserWithUserMenu(this.rid);
            List<TsUserMenu> menuList = this.tsUserInfo.getTsUserMenus();
            if(null != menuList && menuList.size() > 0) {
                for(TsUserMenu t: menuList) {
                    selectedMenuMap.put(t.getTsMenu().getRid(), t.getTsMenu().getRid());
                }
            }

            for(TsMenu t : allMenuList) {
                menuMap.put(t.getMenuLevelNo(), t);
                if(StringUtils.isNotBlank(t.getMenuLevelNo())) {
                    if(StringUtils.containsNone(t.getMenuLevelNo(), ".")) {
                        firstLevelNoSet.add(t.getMenuLevelNo());
                    }else {
                        levelNoSet.add(t.getMenuLevelNo());
                    }
                }
            }

            for(String ln: firstLevelNoSet) {
                TsMenu t = menuMap.get(ln);
                TreeNode node = new CheckboxTreeNode(menuMap.get(ln), this.treeNode);
                if(null != selectedMenuMap.get(t.getRid())) {
                    node.setSelected(true);
                }
                this.addChildNode(ln, levelNoSet, menuMap, node, selectedMenuMap);
            }
        }
	}

	/**
	 * 分配菜单保存
	 */
    @Transactional(readOnly = false)
    @ZwxLog(type= LogNoEnum.NO_2001, value="菜单授权")
	public void menuAction() {
        Set<Integer> menuSet = new HashSet<Integer>();
        if(null != this.selectedNodes && this.selectedNodes.length > 0) {
            for(TreeNode node:this.selectedNodes) {
                this.chooseMenuIds(menuSet, node);
            }
        }
        this.systemModuleService.grantMenuToUser(menuSet, this.rid);
	}

	@Override
	public String[] buildHqls() {
        //2015-03-31 xt 修改显示科室兼职人员+合并兼职科室
        StringBuilder sb = new StringBuilder();
        sb.append(" FROM (SELECT T3.ZONE_NAME,T2.UNITNAME,");
        sb.append(" LISTAGG( C.OFFICENAME,',') WITHIN GROUP (ORDER BY DECODE (C.ZKS ,1,-1,C.NUM)) AS OFFICENAMES,");
	    sb.append(" T.USER_NO,T.USERNAME,T.RID,T.IF_REVEAL,T3.ZONE_GB,");
	    sb.append(" ','||LISTAGG( C.OFFICEID,',') WITHIN GROUP (ORDER BY C.OFFICEID)||',' AS OFFICEIDS,  ");
        sb.append(" SUM( DECODE(C.ZKS,1,C.NUM,0 )) AS ZKSNUM,C.EMPNUM,T2.UNIT_CODE FROM TS_USER_INFO T");
        sb.append(" LEFT JOIN ( SELECT A.RID,B.RID AS OFFICEID,");
        sb.append(" B.OFFICENAME,B.NUM , A.NUM EMPNUM,1 AS ZKS  FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
        sb.append(" UNION ALL SELECT * FROM ( SELECT A.RID,C.RID AS OFFICEID,");
        sb.append(" C.OFFICENAME,C.NUM,A.NUM EMPNUM, 0 AS ZKS FROM TB_SYS_EMP A");
        sb.append(" INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
        sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID ORDER BY C.NUM)) C  ON C.RID = T.EMP_ID");
        sb.append(" INNER JOIN TS_UNIT T2 ON T2.RID = T.UNIT_RID AND T2.IF_REVEAL = 1 ");
        sb.append(" INNER JOIN TS_ZONE T3 ON T3.RID = T2.ZONE_ID AND T3.IF_REVEAL = 1 WHERE 1=1");
        if(!ifAdmin) {
			sb.append(" AND T.USER_NO != '").append(Constants.ADMIN).append("' ");
		}
		if (StringUtils.isNotBlank(this.searchUnitId)) {
			sb.append(" AND T.UNIT_RID =").append(this.searchUnitId);
		} else {
			String loginZoneCode = this.sessionData.getUser().getTsUnit().getTsZone().getZoneCode();
			if ((!this.ifAdmin) && loginZoneCode.equals(this.searchZoneCode)) {
				sb.append(" and T.UNIT_RID =").append(this.sessionData.getUser().getTsUnit().getRid());
			} else {
				sb.append(" AND T3.ZONE_CODE LIKE '").append(ZoneUtil.zoneSelect(this.searchZoneCode))
						.append("%' ");
			}
		}
		if (StringUtils.isNotBlank(this.searchUsername)) {
			sb.append(" AND T.USERNAME LIKE :USERNAME ");
			this.paramMap.put("USERNAME", "%" + this.searchUsername.trim() + "%");
		}
		if (null != this.searchUserType) {
			sb.append(" AND T.USER_TYPE =").append(this.searchUserType);
		}
		 if(searchUserState !=null && searchUserState.length == 1){
	            sb.append(" AND T.IF_REVEAL =  '").append(searchUserState[0]).append("' ");
	        }
        sb.append(" GROUP BY T3.ZONE_NAME,T3.ZONE_GB,T2.UNITNAME, T.USERNAME,T.USER_NO,T.RID,T.IF_REVEAL,C.EMPNUM,T2.UNIT_CODE ) D");
        if (null != this.searchOfficeId) {
			sb.append(" WHERE D.OFFICEIDS  LIKE '%,").append(this.searchOfficeId).append(",%'");
		}
		StringBuilder searchSql = new StringBuilder("SELECT D.ZONE_NAME,D.UNITNAME,D.OFFICENAMES,");
		searchSql.append(" D.USER_NO,D.USERNAME,D.RID,D.IF_REVEAL ").append(sb);
		searchSql.append(" ORDER BY D.ZONE_GB, D.UNIT_CODE,  D.ZKSNUM,D.EMPNUM").toString();  
		
		StringBuilder countSql = new StringBuilder("SELECT COUNT(D.RID) ").append(sb);
		return new String[]{searchSql.toString(),countSql.toString()};
	}

    /**
     * 添加兼职信息初始化
     */
    public void addParttimeinit(){
        //对科室集合初始化
        selectTsOffice();
        //兼职记录初始化
        tsParttimeInfo = new TsParttimeInfo();
        //兼职id初始化
        parttimeOfficeId = null;
        parttimeDutyId = null;
    }

    /**
     * 对科室集合进行过滤，过滤掉主科室和已选科室,若主科室为空，则所以科室可选
     * <AUTHOR>
     * @createDate 2014-10-13
     */
    private void selectTsOffice(){
        parttimeOfficeMap = new HashMap<String, Integer>();
        if(editOfficeMap.size() > 0){
            Set<Map.Entry<String, Integer>> entrySet = editOfficeMap.entrySet();
            Iterator<Map.Entry<String, Integer>> iterator = entrySet.iterator();
            //遍历map集合，给可选科室添加元素
            while (iterator.hasNext()) {
                Map.Entry<String, Integer> entry = iterator.next();
                //过滤主科室
                if((editOfficeId == null) || (editOfficeId != null && !entry.getValue().equals(editOfficeId))){
                    //过滤已选科室
                    if (notContainparttimeList(entry.getValue())) {
                        parttimeOfficeMap.put(entry.getKey(),entry.getValue());
                    }
                }
            }
        }
    }

    /**
     * 过滤已选科室
     * @param officeId 科室id
     * @return 若已选列表中不含有该科室id，则返回true
     * <AUTHOR>
     * @createDate 2014-10-13
     */
    private boolean notContainparttimeList(Integer officeId){
        if(tsParttimeInfoList.size() > 0){
            for(TsParttimeInfo t : tsParttimeInfoList){
                if(t.getTsOffice().getRid().equals(officeId)){
                    return false;
                }
            }
            return true;
        }else{
            return true;
        }
    }

    /**
     * 保存兼职信息
     * <AUTHOR>
     * @createDate 2014-10-13
     */
    public void saveParttimeAction(){
        if(null == parttimeOfficeId){
            JsfUtil.addErrorMessage("兼职科室不允许为空！");
        }
        if(null == parttimeDutyId){
            JsfUtil.addErrorMessage("兼职职务不允许为空！");
        }
        if(null == parttimeOfficeId || null == parttimeDutyId) {
            return;
        }
        tsParttimeInfo.setTsOffice(systemModuleService.findOffice(parttimeOfficeId));
        tsParttimeInfo.setDutyId(systemModuleService.findSimpleCodeByRid(parttimeDutyId));
        tsParttimeInfo.setTbSysEmp(tsUserInfo.getTbSysEmp());
        if(!tsParttimeInfoList.contains(tsParttimeInfo)){
            tsParttimeInfoList.add(tsParttimeInfo);
        }
        RequestContext.getCurrentInstance().execute("ParttimeInfoListDialog.hide()");
    }

    /**
     * 删除兼职信息
     * <AUTHOR>
     * @createDate 2014-10-13
     */
    public void deleteParttimeAction(){
        tsParttimeInfoList.remove(tempParttimeInfo);
    }

    /**
     * 修改兼职信息
     * <AUTHOR>
     * @createDate 2014-10-13
     */
    public void modifyParttimeAction(){
        parttimeOfficeId = tempParttimeInfo.getTsOffice().getRid();
        parttimeDutyId = tempParttimeInfo.getDutyId().getRid();
        tsParttimeInfo = tempParttimeInfo;
        selectTsOffice();
        parttimeOfficeMap.put(tempParttimeInfo.getTsOffice().getOfficename(),tempParttimeInfo.getTsOffice().getRid());
    }

    /*文件上传*/
    public void handleFileUpload(FileUploadEvent event){
        if (null != event) {
            UploadedFile file = event.getFile();
            String fileName = file.getFileName();
            String contentType = file.getContentType().toLowerCase();
            String errorMsg = FileUtils.veryFormat(contentType, fileName, "1");
            if (StringUtils.isNotBlank(errorMsg)) {
				JsfUtil.addErrorMessage(errorMsg);
            	return;
			}
            String uuid = UUID.randomUUID().toString().replaceAll("-","");
            String hz = fileName.substring(fileName.lastIndexOf(".")+1);
            String filePath = xnPath+"sys/users/"+uuid+"."+hz;
            String showDir = "/sys/users/"+uuid+"."+hz;
            this.tsUserInfo.getTbSysEmp().setPsnSign(showDir);
            try{
            	FileUtils.copyFile(filePath, file.getInputstream());
                JsfUtil.addSuccessMessage("上传成功！");
                RequestContext.getCurrentInstance().execute("fileUIdVar.hide();");
            } catch (IOException e){
                FacesMessage msg = new FacesMessage("上传失败", file.getFileName()+ "上传失败！");
                FacesContext.getCurrentInstance().addMessage("上传失败", msg);
                e.printStackTrace();
            }
        }
    }


    public void deleteSysEmpPsnsign(){
        psnsignList.add(this.tsUserInfo.getTbSysEmp().getPsnSign());
        this.tsUserInfo.getTbSysEmp().setPsnSign(null);
    }

    /* 文件删除 */
    public void deleteDiskFile() {
        if(null != psnsignList && psnsignList.size() > 0){
            for(String s : psnsignList){
                String filePath = xnPath+s.substring(1);
                new File(filePath).delete();
            }
        }
    }

    
    /**
 	 * <p>方法描述：</p>
 	 * 
 	 * @MethodAuthor rcj,2020年3月4日,findFlowByIdc
	 * */
	public boolean findFlowByIdc(){
		if (StringUtils.isNotBlank(tsUserInfo.getTbSysEmp().getIdc())) {
			//如果不是保存操作，就不执行自动填写出生日期和男女的操作
				String checkIDC = IdcUtils.checkIDC(tsUserInfo.getTbSysEmp().getIdc());
				if (StringUtils.isBlank(checkIDC)) {
					if (Integer.valueOf(tsUserInfo.getTbSysEmp().getIdc().substring(
							tsUserInfo.getTbSysEmp().getIdc().length() - 2,
							tsUserInfo.getTbSysEmp().getIdc().length() - 1)) % 2 == 0) {
						tsUserInfo.getTbSysEmp().setEmpSex((short)2);
					} else {
						tsUserInfo.getTbSysEmp().setEmpSex((short)1);
					}// 性别
					Date birthday = IdcUtils.calBirthday(tsUserInfo.getTbSysEmp().getIdc());
					tsUserInfo.getTbSysEmp().setBirthday(birthday);
				}else{
					JsfUtil.addErrorMessage(checkIDC);
				}
		}
		return true;
	}
    
    //getters and setters

    public TsUserInfo getTsUserInfo() {
        return tsUserInfo;
    }

    public void setTsUserInfo(TsUserInfo tsUserInfo) {
        this.tsUserInfo = tsUserInfo;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TreeNode getTreeNode() {
        return treeNode;
    }

    public void setTreeNode(TreeNode treeNode) {
        this.treeNode = treeNode;
    }

    public TreeNode[] getSelectedNodes() {
        return selectedNodes;
    }

    public void setSelectedNodes(TreeNode[] selectedNodes) {
        this.selectedNodes = selectedNodes;
    }

    public Map<String, Integer> getRoleMap() {
        return roleMap;
    }

    public void setRoleMap(Map<String, Integer> roleMap) {
        this.roleMap = roleMap;
    }

    public List<String> getSelectedRoles() {
        return selectedRoles;
    }

    public void setSelectedRoles(List<String> selectedRoles) {
        this.selectedRoles = selectedRoles;
    }

    public Map<String, Integer> getEditOfficeMap() {
        return editOfficeMap;
    }

    public void setEditOfficeMap(Map<String, Integer> editOfficeMap) {
        this.editOfficeMap = editOfficeMap;
    }

    public Integer getEditOfficeId() {
        return editOfficeId;
    }

    public void setEditOfficeId(Integer editOfficeId) {
        this.editOfficeId = editOfficeId;
    }

    public Map<String, String> getEditUnitMap() {
        return editUnitMap;
    }

    public void setEditUnitMap(Map<String, String> editUnitMap) {
        this.editUnitMap = editUnitMap;
    }

    public String getEditUnitId() {
        return editUnitId;
    }

    public void setEditUnitId(String editUnitId) {
        this.editUnitId = editUnitId;
    }

    public String getEditZoneName() {
        return editZoneName;
    }

    public void setEditZoneName(String editZoneName) {
        this.editZoneName = editZoneName;
    }

    public Map<String, Integer> getEmpNationMap() {
        return empNationMap;
    }

    public void setEmpNationMap(Map<String, Integer> empNationMap) {
        this.empNationMap = empNationMap;
    }

    public Map<String, Integer> getDutyMap() {
        return dutyMap;
    }

    public void setDutyMap(Map<String, Integer> dutyMap) {
        this.dutyMap = dutyMap;
    }

    public Map<String, Integer> getPositionMap() {
        return positionMap;
    }

    public void setPositionMap(Map<String, Integer> positionMap) {
        this.positionMap = positionMap;
    }

    public Map<String, Integer> getEduDegreeMap() {
        return eduDegreeMap;
    }

    public void setEduDegreeMap(Map<String, Integer> eduDegreeMap) {
        this.eduDegreeMap = eduDegreeMap;
    }

    public Map<String, Integer> getPoliticsMap() {
        return politicsMap;
    }

    public void setPoliticsMap(Map<String, Integer> politicsMap) {
        this.politicsMap = politicsMap;
    }

    public Map<String, Integer> getMaritalStatusMap() {
        return maritalStatusMap;
    }

    public void setMaritalStatusMap(Map<String, Integer> maritalStatusMap) {
        this.maritalStatusMap = maritalStatusMap;
    }

    public Map<String, Integer> getReligionMap() {
        return religionMap;
    }

    public void setReligionMap(Map<String, Integer> religionMap) {
        this.religionMap = religionMap;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchUnitId() {
        return searchUnitId;
    }

    public void setSearchUnitId(String searchUnitId) {
        this.searchUnitId = searchUnitId;
    }

    public Integer getSearchOfficeId() {
        return searchOfficeId;
    }

    public void setSearchOfficeId(Integer searchOfficeId) {
        this.searchOfficeId = searchOfficeId;
    }

    public Map<String, String> getSearchUnitMap() {
        return searchUnitMap;
    }

    public void setSearchUnitMap(Map<String, String> searchUnitMap) {
        this.searchUnitMap = searchUnitMap;
    }

    public Map<String, Integer> getSearchOfficeMap() {
        return searchOfficeMap;
    }

    public void setSearchOfficeMap(Map<String, Integer> searchOfficeMap) {
        this.searchOfficeMap = searchOfficeMap;
    }

    public String getSearchUsername() {
        return searchUsername;
    }

    public void setSearchUsername(String searchUsername) {
        this.searchUsername = searchUsername;
    }

    public Short getSearchUserType() {
        return searchUserType;
    }

    public void setSearchUserType(Short searchUserType) {
        this.searchUserType = searchUserType;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

	public String getSearchZoneType() {
		return searchZoneType;
	}

	public void setSearchZoneType(String searchZoneType) {
		this.searchZoneType = searchZoneType;
	}

	public String getSearchZoneCode() {
		return searchZoneCode;
	}

	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}

	public String getEditZoneCode() {
		return editZoneCode;
	}

	public void setEditZoneCode(String editZoneCode) {
		this.editZoneCode = editZoneCode;
	}

	public String getEditZoneType() {
		return editZoneType;
	}

	public void setEditZoneType(String editZoneType) {
		this.editZoneType = editZoneType;
	}

    public boolean isIfAdmin() {
        return ifAdmin;
    }

    public void setIfAdmin(boolean ifAdmin) {
        this.ifAdmin = ifAdmin;
    }

    public TsParttimeInfo getTsParttimeInfo() {
        return tsParttimeInfo;
    }

    public void setTsParttimeInfo(TsParttimeInfo tsParttimeInfo) {
        this.tsParttimeInfo = tsParttimeInfo;
    }

    public List<TsParttimeInfo> getTsParttimeInfoList() {
        return tsParttimeInfoList;
    }

    public void setTsParttimeInfoList(List<TsParttimeInfo> tsParttimeInfoList) {
        this.tsParttimeInfoList = tsParttimeInfoList;
    }

    public Map<String, Integer> getParttimeOfficeMap() {
        return parttimeOfficeMap;
    }

    public void setParttimeOfficeMap(Map<String, Integer> parttimeOfficeMap) {
        this.parttimeOfficeMap = parttimeOfficeMap;
    }

    public Integer getParttimeOfficeId() {
        return parttimeOfficeId;
    }

    public void setParttimeOfficeId(Integer parttimeOfficeId) {
        this.parttimeOfficeId = parttimeOfficeId;
    }

    public TsParttimeInfo getTempParttimeInfo() {
        return tempParttimeInfo;
    }

    public void setTempParttimeInfo(TsParttimeInfo tempParttimeInfo) {
        this.tempParttimeInfo = tempParttimeInfo;
    }

    public Integer getParttimeDutyId() {
        return parttimeDutyId;
    }

    public void setParttimeDutyId(Integer parttimeDutyId) {
        this.parttimeDutyId = parttimeDutyId;
    }

    public List<SelectItem> getPsnPropIdList() {
        return psnPropIdList;
    }

    public void setPsnPropIdList(List<SelectItem> psnPropIdList) {
        this.psnPropIdList = psnPropIdList;
    }

    public Integer getPsnPropId() {
        return psnPropId;
    }

    public void setPsnPropId(Integer psnPropId) {
        this.psnPropId = psnPropId;
    }

    public String[] getSearchUserState() {
        return searchUserState;
    }

    public void setSearchUserState(String[] searchUserState) {
        this.searchUserState = searchUserState;
    }
}

