package com.chis.modules.system.web;


/**
 * 带有转向维护模块、无分页的托管Bean
 * <AUTHOR>
 */
public abstract class FacesNoPageBean extends FacesBean{
	/**当前活动的标签，从0开始，默认为0,0:主界面1：添加修改界面2：查看界面*/
	private int activeTab;

	public FacesNoPageBean() {

	}
	
	/**
	 * 查询操作
	 */
	public abstract void searchAction();
	
	/**
	 * 添加初始化
	 */
	public void addInitAction() {
		this.addInit();
		this.forwardEditPage();
	}
	
	/**
	 * 修改初始化
	 */
	public void modInitAction() {
		this.modInit();
		this.forwardEditPage();
	}

    /**
     * 查看初始化
     */
    public void viewInitAction() {
        this.viewInit();
        this.forwardViewPage();
    }
	
	/**
	 * 转向修改界面
	 */
	public void forwardEditPage() {
		this.activeTab = 1;
	}

    /**
     * 转向到查看界面
     */
    public void forwardViewPage() { this.activeTab = 2; }
	
	/**
	 * 返回首页面
	 */
	public void backAction() {
		this.activeTab = 0;
	}
	
	/**
	 * 具体的添加初始化
	 */
	public abstract void addInit();
	
	/**
	 * 具体的修改初始化
	 */
	public abstract void viewInit();

    /**
     * 具体的查看初始化
     */
    public abstract void modInit();
	
	/**
	 * 具体的添加
	 */
	public abstract void saveAction();
	
	//gets and sets
	public int getActiveTab() {
		return activeTab;
	}

	public void setActiveTab(int activeTab) {
		this.activeTab = activeTab;
	}

}
