package com.chis.modules.system.web;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;

import org.primefaces.context.RequestContext;
import org.primefaces.event.FlowEvent;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.FileUtils;
import com.chis.common.utils.FreeMarkers;
import com.chis.common.utils.JaxbMapper;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.logic.DynaStyleTemplate;
import com.chis.modules.system.entity.TdFormField;
import com.chis.modules.system.entity.TdFormStatisticsDef;
import com.chis.modules.system.entity.TdFormTable;
import com.chis.modules.system.entity.TdFormType;
import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsRpt;
import com.chis.modules.system.enumn.ConvergeType;
import com.chis.modules.system.enumn.DynaFieldShowType;
import com.chis.modules.system.enumn.DynaFieldType;
import com.chis.modules.system.enumn.EchartsType;
import com.chis.modules.system.enumn.MathType;
import com.chis.modules.system.enumn.OrderType;
import com.chis.modules.system.enumn.SearchFlag;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.logic.TopTitlePo;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.DyncFormServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Arith;
import com.chis.modules.system.utils.GenHtmlCodeUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

/**
 * 动态统计维护
 * 
 * <AUTHOR>
 * 
 */
@ManagedBean(name = "tdDynaFormDesignStatisticsBean")
@ViewScoped
public class TdDynaFormDesignStatisticsBean extends FacesEditBean {

	private static final long serialVersionUID = 8521581962587506423L;
	/** ejb session bean */

	private DyncFormServiceImpl dyncFormServiceImpl = (DyncFormServiceImpl) SpringContextHolder
			.getBean(DyncFormServiceImpl.class);
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);

	// ====================查询条件开始==========================
	/** 流程表单类型Id */
	private Integer formTypeId;
	/** 流程表单集合 */
	private List<TdFormType> formTypeList = new ArrayList<TdFormType>();
	/** 流程表单名称 */
	private String formName;
	/** 状态 */
	private String[] searchState;

	// ====================查询条件结束==========================
	/** 表单Id */
	private Integer formId;
	/** 绑定统计 */
	private TdFormStatisticsDef bindTdFormStatisticsDef;
	/** 表单模式集合 */
	private List<SelectItem> formModelList;

	/** 绑定表单类别 */
	private TdFormType bindTdFormType;
	/** 弹出框 表单类型名称 */
	private String formTypeDiag;
	/** 表单类型集合 */
	private List<TdFormType> sourceTypeList;
	private List<TdFormType> showTypeList;

	// 主子表集合
	private TdFormTable mTdFormTable;
	private TdFormTable sTdFormTable;

	/** 缓存码表类型集合 */
	private Map<String, TsCodeType> cachMap;

	/** +样式选择集合 */
	private List<SelectItem> styleList = null;

	private static String DYNA_MODEL_PATH = "/resources/freemarker/system";

	/** 表名 */
	private String tableNameDiag;
	/** 表模式 */
	private String tableModelDiag;
	/** 表单类型集合 */
	private List<Object[]> sourceTableList;
	private List<Object[]> showTableList;
	/** 数据表ID */
	private Integer dynaFormId;
	/** 数据表名称 */
	private String dynaFormName;

	@PostConstruct
	public void init() {
		this.initSearchCondition();
		this.searchAction();
	}

	/**
	 * 初始化样式集合
	 */
	public void initStyleList() {
		styleList = Lists.newArrayList();

		String dynaModelPath = FileUtils.getWebRootPath() + DYNA_MODEL_PATH;
		File file = new File(dynaModelPath);
		String[] fns = file.list();
		if (null != fns && fns.length > 0) {
			for (String fileName : fns) {
				StringBuilder filePath = new StringBuilder();
				filePath.append(DYNA_MODEL_PATH).append("/").append(fileName);
				DynaStyleTemplate genDynaObject = this.genDynaObject(filePath.toString());
				if (null != genDynaObject) {
					styleList.add(new SelectItem(fileName, genDynaObject.getName()));
				}
			}
		}
	}
	
	@Override
	public String[] buildHqls() {
		StringBuffer searchCommSql = new StringBuffer();
		searchCommSql.append(" FROM TD_FORM_STATISTICS_DEF T INNER JOIN TD_FORM_TYPE T1 ON T.TYPE_ID = T1.RID ");
		searchCommSql.append(" INNER JOIN TD_FORM_TABLE T2 ON T2.RID = T.TABLE_ID WHERE 1=1");
		if (null != formTypeId) {
			searchCommSql.append(" AND T.TYPE_ID =").append(formTypeId);
		}
		if (StringUtils.isNotBlank(formName)) {
			searchCommSql.append(" AND T.FORM_NAME LIKE :FORMNAME ESCAPE '\\\'");
			this.paramMap.put("FORMNAME", "%" + StringUtils.convertBFH(formName) + "%");
		}
		if (null != searchState && searchState.length > 0) {
			String states = StringUtils.array2string(searchState, ",");
			searchCommSql.append(" AND T.STATE IN (").append(states).append(")");
		}

		String searchSql = new StringBuffer(
				"SELECT T1.TYPE_NAME,T.FORM_CODE,T.FORM_NAME,T.STATE,T.RID, T2.CN_NAME||'['||T2.EN_NAME||']' ")
				.append(searchCommSql).append(" ORDER BY T1.TYPE_CODE ,T.FORM_CODE").toString();
		String countSql = new StringBuffer("SELECT COUNT(T.RID)").append(searchCommSql).toString();
		return new String[] { searchSql, countSql };
	}

	/**
	 * 初始化查询条件
	 */
	private void initSearchCondition() {
		// 初始化查询条件
		this.formTypeList = this.dyncFormServiceImpl.findFormTypeList();
		this.formTypeId = null;
		this.formName = null;
		this.bindTdFormType = new TdFormType();
		this.ifSQL = true;
	}


	/**
	 * 弹出单选框初始化
	 */
	public void showSingleAction() {
		this.formTypeDiag = null;
		this.searchSingAction();
	}

	/**
	 * 重新查询数据集
	 */
	private void searchSingAction() {
		sourceTypeList = this.dyncFormServiceImpl.findTdFormTypeList();
		if (null == sourceTypeList) {
			sourceTypeList = new ArrayList<TdFormType>();
		}
		showTypeList = new ArrayList<TdFormType>();
		showTypeList.addAll(sourceTypeList);
	}

	/**
	 * 清空弹出框选择
	 */
	public void clearSingleAciton() {
		this.bindTdFormStatisticsDef.setTdFormTypeByTypeId(null);
	}

	/**
	 * 清空弹出框选择
	 */
	public void clearTableAciton() {
		this.dynaFormId = null;
		this.dynaFormName = null;
		this.bindTdFormStatisticsDef.setTdFormTableByTableId(null);
	}

	/**
	 * 保存表单类型
	 */
	public void saveSingleAction() {
		String verifyTdFormType = this.dyncFormServiceImpl.verifyTdFormType(bindTdFormType);
		if (StringUtils.isNotBlank(verifyTdFormType)) {
			JsfUtil.addErrorMessage(verifyTdFormType);
		} else {
			this.dyncFormServiceImpl.saveTdFormType(bindTdFormType);
			JsfUtil.addSuccessMessage("保存成功！");
			this.searchSingAction();
			this.filterSingelSel();

			RequestContext currentInstance = RequestContext.getCurrentInstance();
			currentInstance.execute("PF('SingleAddDialog').hide();");
			currentInstance.update("tabView:editForm:singleDatatable");
		}
	}

	/**
	 * 删除表单类型
	 */
	public void deleteSingleAction() {
		String deleteTdFormType = this.dyncFormServiceImpl.deleteTdFormType(this.bindTdFormType.getRid());
		if (StringUtils.isNotBlank(deleteTdFormType)) {
			JsfUtil.addErrorMessage(deleteTdFormType);
			return;
		}
		this.searchSingAction();
		this.filterSingelSel();
	}

	/**
	 * 过滤表单选择
	 */
	public void filterSingelSel() {
		this.showTypeList = new ArrayList<TdFormType>();
		for (TdFormType tdFormType : sourceTypeList) {
			String typeName = tdFormType.getTypeName();
			if (StringUtils.isBlank(formTypeDiag) || typeName.indexOf(formTypeDiag) != -1) {
				this.showTypeList.add(tdFormType);
			}
		}
	}

	/**
	 * 弹出单选框初始化
	 */
	public void showTableAction() {
		this.tableModelDiag = null;
		this.tableNameDiag = null;
		this.searchTableAction();
	}

	/**
	 * 重新查询数据集
	 */
	private void searchTableAction() {
		sourceTableList = this.dyncFormServiceImpl.findAllTableList();
		if (null == sourceTableList) {
			sourceTableList = new ArrayList<Object[]>();
		}
		showTableList = new ArrayList<Object[]>();
		showTableList.addAll(sourceTableList);
	}

	/**
	 * 过滤表选择
	 */
	public void filterTableSel() {
		this.showTableList = new ArrayList<Object[]>();
		for (Object[] objArr : sourceTableList) {
			String enName = StringUtils.objectToString(objArr[0]);
			String model = StringUtils.objectToString(objArr[1]);
			boolean nameFlag = false;
			boolean modelFlag = false;
			if (StringUtils.isBlank(tableNameDiag) || enName.indexOf(tableNameDiag) != -1) {
				nameFlag = true;
			}
			if (StringUtils.isBlank(tableModelDiag) || model.equals(tableModelDiag)) {
				modelFlag = true;
			}
			if (nameFlag && modelFlag) {
				this.showTableList.add(objArr);
			}
		}
	}

	/**
	 * 选中集合
	 */
	public void selTableResult() {
		if (null != this.dynaFormId) {
			this.bindTdFormStatisticsDef.setTdFormTableByTableId(new TdFormTable(this.dynaFormId));
			TdFormTable find = this.dyncFormServiceImpl.findTdFormTable(this.dynaFormId);
			if (null != find) {
				mTdFormTable = find;
				sTdFormTable = null;
				if (null != find.getChildList() && find.getChildList().size() > 0) {
					sTdFormTable = find.getChildList().get(0);
				}
			}
		}
	}

	/**
	 * 选中集合
	 */
	public void selSingleResult() {
		this.bindTdFormStatisticsDef.setTdFormTypeByTypeId(this.bindTdFormType);
	}

	/**
	 * 添加表单类型
	 */
	public void addSingleSel() {
		this.bindTdFormType = new TdFormType();
		this.bindTdFormType.setCreateDate(new Date());
		this.bindTdFormType.setCreateManid(this.sessionData.getUser().getRid());
	}

	/**
	 * 提交记录
	 */
	public void confirmEditRecord(Integer state) {
		if (null != bindTdFormStatisticsDef) {
			if (state.intValue() == 0) {
				this.dyncFormServiceImpl.commitTdFormStatisticsDef(bindTdFormStatisticsDef.getRid(), state);
				this.bindTdFormStatisticsDef.setState(state);
				JsfUtil.addSuccessMessage("撤销成功！");
			}else if (state.intValue() == 1) {
				if (verifyAction()) {
					// 首先保存记录
					this.saveAction();
					this.dyncFormServiceImpl.commitTdFormStatisticsDef(bindTdFormStatisticsDef.getRid(), state);
					this.bindTdFormStatisticsDef.setState(state);
					JsfUtil.addSuccessMessage("提交成功！");
				}
			}
		}
	}

	/**
	 * 删除方法
	 */
	public void deleteAction() {
		if (null != formId) {
			try{
				this.dyncFormServiceImpl.delTdFormDef(formId);
				JsfUtil.addSuccessMessage("删除成功！");
			}catch(Exception e ){
				e.printStackTrace();
				JsfUtil.addSuccessMessage("该表单已被引用，无法删除！");
			}
		}
	}

	/**
	 * 初始化编辑界面集合
	 */
	private void initEditList() {
		if (null == cachMap || cachMap.size() == 0) {
			cachMap = new HashMap<String, TsCodeType>();
			List<TsCodeType> findCodeTypeList = this.systemModuleService.findCodeTypeList(true, null);
			if (null != findCodeTypeList && findCodeTypeList.size() > 0) {
				for (TsCodeType tsCodeType : findCodeTypeList) {
					cachMap.put(tsCodeType.getCodeTypeName(), tsCodeType);
				}
			}
		}

	}

	@Override
	public void addInit() {
		// 默认添加单表模式
		this.bindTdFormStatisticsDef = new TdFormStatisticsDef();
		this.bindTdFormStatisticsDef.setState(0);
		this.bindTdFormStatisticsDef.setCreateDate(new Date());
		this.bindTdFormStatisticsDef.setCreateManid(this.sessionData.getUser().getRid());
		// 根据数据模型加载表
		this.bindTdFormStatisticsDef.setTdFormTypeByTypeId(new TdFormType());
		this.bindTdFormStatisticsDef.setTdFormTableByTableId(null);

		this.dynaFormId = null;
		this.dynaFormName = null;
		this.initEditList();
	}

	@Override
	public void viewInit() {
		modInit();
	}

	@Override
	public void modInit() {
		this.dynaFormId = null;
		this.dynaFormName = null;
		// 初始化页面集合
		this.initEditList();
		if (null != this.formId) {
			this.bindTdFormStatisticsDef = this.dyncFormServiceImpl.findTdFormStatisticsDef(this.formId);
			
			TdFormTable mTable = this.bindTdFormStatisticsDef.getTdFormTableByTableId();
			this.dynaFormId = mTable.getRid();
			this.dynaFormName =  new StringBuilder(mTable.getCnName()).append("[").append(mTable.getEnName()).append("]").toString();
			
		}
	}

	@Override
	public void saveAction() {
		if (verifyAction()) {
			dyncFormServiceImpl.saveTdFormStatisticsDef(bindTdFormStatisticsDef);
			JsfUtil.addSuccessMessage("保存成功！");
//			this.searchAction();
		}
	}

	private boolean verifyAction() {
		TdFormType tdFormType = this.bindTdFormStatisticsDef.getTdFormTypeByTypeId();
		if (null == tdFormType || null == tdFormType.getRid()) {
			JsfUtil.addErrorMessage("表单类别不允许为空！");
			return false;
		}
		if (StringUtils.isBlank(this.bindTdFormStatisticsDef.getFormName())) {
			JsfUtil.addErrorMessage("表单名称不允许为空！");
			return false;
		}
		if (  null == this.bindTdFormStatisticsDef.getTdFormTableByTableId() || null == this.bindTdFormStatisticsDef.getTdFormTableByTableId().getRid()) {
			JsfUtil.addErrorMessage("数据表不允许为空！");
			return false;
		}
		return true;
	}

	private DynaStyleTemplate genDynaObject(String filePath) {
		if (StringUtils.isNotBlank(filePath)) {
			String fileContent = FileUtils.getFileContent(filePath.toString());
			DynaStyleTemplate fromXml = JaxbMapper.fromXml(fileContent, DynaStyleTemplate.class);
			return fromXml;
		}
		return null;
	}
	
	/**
	 *********************************************
	 * 生成辅助方法参数
	 * 
	 * 
	 *********************************************
	 **/
	
	//所有字段集合
	public List<SelectItem> fieldSelect;
	//所有聚合方式
	public List<SelectItem> convergeTypeList;
	//所有排序方式
	public List<SelectItem> orderTypeList;
	//所有过滤方式
	public List<SelectItem> searchTypeList;
	//字段配置类型
	public List<SelectItem> fieldConfigList;
	//辅助过滤条件使用集合
	public List<TdFormField> assistFieldList;
	//统计图集合
	public List<SelectItem> echartsTypeList;
	//过滤Filed传递参数
	public TdFormField filterField;
	//统计sql
	public String statisticsSql;
	//统计脚本
	public String statisticsShell;
	//处理方式
	public Integer dealMethod;
	//全部处理方式
	public List<SelectItem> dealMethods;
	//字段一编号
	public Integer fieldOne;
	//字段二编号
	public Integer fieldTwo;
	//脚本处理标识
	public final String SHELLHANDLEFLAG = "\t//=========DON'T DELETE =========\r\n";
	//wizard流程标识
	public String wizardIndex="";
	//表头集合
	public List<TopTitlePo> topTitles;
	//表头对象
	public TopTitlePo topTitlePo;
	//生成HTMLCODE
	public String statisticsHtmlCode;
	//统计图类型
	public Integer echartsType;
	
	//临时存储字段数据，包括查询，分组，聚合，排序
	private Map<Integer,List<TdFormField>> assistFieldMap;
	
	//缓存字段信息，给过滤字段生成页面时使用
	private Map<String,TdFormField> tempFieldTypeMap;
	//统计图生成使用
	public List<TopTitlePo> echartsTitleList;
	public List<TopTitlePo> echartsDataList;
	
	
	
	/**
	 * 进入辅助生成页面，辅助生成sql,脚本,html
	 * 
	 */
	public void toAssistPage(){
		if(!StringUtils.isNotBlank(bindTdFormStatisticsDef.getFormName())){
			JsfUtil.addErrorMessage("请填写表单名称！");
			return;
		}
		if (null != this.dynaFormId) {
			this.bindTdFormStatisticsDef.setTdFormTableByTableId(new TdFormTable(this.dynaFormId));
			TdFormTable find = this.dyncFormServiceImpl.findTdFormTable(this.dynaFormId);
			if (null != find) {
				
				mTdFormTable = find;
				sTdFormTable = null;
				if (null != find.getChildList() && find.getChildList().size() > 0) 
					sTdFormTable = find.getChildList().get(0);
				//初始化辅助编辑第一步选择过滤条件
				assistInit();
				wizardIndex="step_filters";
				RequestContext.getCurrentInstance().execute("PF('wizard').back();");
				forwardOtherPage();
				return;
			}
		}
		JsfUtil.addErrorMessage("请选择数据表！");
	}
	
	/**
	 * 辅助初始化
	 */
	private void assistInit() {
		fieldSelect = packFieldsToSelectList();
		fieldConfigList = packFieldConfigList();
		searchTypeList = SearchFlag.initSearchType();
		convergeTypeList = ConvergeType.initConvergeType();
		orderTypeList = OrderType.initOrderType();
		echartsTypeList = EchartsType.initEchartsType();
		assistFieldList =  Lists.newArrayList();
		assistFieldList.add(new TdFormField());
		
		statisticsSql = "";
		statisticsShell = "";
		
		topTitles = Lists.newArrayList();
		topTitles.add(new TopTitlePo(0));
		stepFiltersInit();
	}

	/**
	 * 字段配置列表
	 * @return
	 */
	private List<SelectItem> packFieldConfigList() {
		List<SelectItem> list = Lists.newArrayList();
		list.add(new SelectItem(1,"查询"));
		list.add(new SelectItem(2,"分组"));
		list.add(new SelectItem(3,"聚合"));
		list.add(new SelectItem(4,"排序"));
		return list;
	}

	/**
	 * 将字段数据封装到选择项中
	 * @return
	 */
	public List<SelectItem> packFieldsToSelectList(){
		tempFieldTypeMap = Maps.newHashMap();
		List<SelectItem> list = Lists.newArrayList();
		List<TdFormField> mainField = this.mTdFormTable.getFieldList();
		for(TdFormField field:mainField){
			packFieldsToSelectListAssist(list,field,"T");
		}
		if(this.sTdFormTable==null)
			return list;
		List<TdFormField> subField = this.sTdFormTable.getFieldList();
		for(TdFormField field:subField){
			packFieldsToSelectListAssist(list,field,"T1");
		}
		return list;
	}
	
	
	/**
	 * 根据字段类型封装选项数据
	 * @param list
	 * @param dataTableFlag 
	 * @param subField
	 */
	private void packFieldsToSelectListAssist(List<SelectItem> list,
			TdFormField field, String dataTableFlag) {
		DynaFieldShowType findEnum = (DynaFieldShowType) EnumUtils.findEnum(DynaFieldShowType.class, field.getDataSrc());
		DynaFieldType dynaFieldType = (DynaFieldType) EnumUtils.findEnum(DynaFieldType.class, field.getFdDbtype());
		String key = "";
		if(DynaFieldShowType.DATE.getTypeNo().equals(findEnum.getTypeNo())){
			String format = " ,'yyyy-mm-dd') ";
			if (dynaFieldType.getTypeNo().equals(DynaFieldType.TIMESTAMP.getTypeNo())) {
				format = " ,'yyyy-mm-dd hh24:mi:ss') ";
			}
			key = "TO_CHAR("+dataTableFlag+"."+field.getFdEnname()+format;
		}else{
			key = dataTableFlag+"."+field.getFdEnname();
		}
		SelectItem item = new SelectItem(key, field.getFdCnname());
		list.add(item);
		tempFieldTypeMap.put(key, field);
		
	}

	/**
	 * 流程切换时触发
	 * @param event
	 * @return
	 */
	public String onFlowProcess(FlowEvent event) {
//		JsfUtil.addErrorMessage(event.getNewStep());
		if("step_filters".equals(wizardIndex)){
			wizardIndex = "";
			return "step_filters";
		}
		boolean flag = true;
		switch (event.getNewStep()) {
			case "step_filters":
				flag = stepFiltersInit();
				break;
			case "step_sql":
				flag = stepSqlInit();
				break;
			case "step_data":
				flag = stepDataInit();
				break;
			case "step_echarts":
				flag = stepEchartsInit();
				break;
			case "step_title":
				flag = stepTitleInit();
				break;
			case "step_html":
				flag = stepHtmlInit();
				break;
			default:
				break;
		}
		if(flag)
			return event.getNewStep();
		else
			return event.getOldStep();
    }
	
	/**
	 * 选择过滤条件初始化
	 * @return 
	 */
	public boolean stepFiltersInit(){
		return true;
	}
	
	/**
	 * 删除过滤地段
	 */
	public void removeFilterField(){
		assistFieldList.remove(filterField);
	}
	
	/**
	 * 新增过滤地段
	 */
	public void addFilterField(){
		assistFieldList.add(new TdFormField());
	}
	
	/**
	 * 根据需求创建出统计sql
	 * @return
	 */
	private String buildStatisticsSql() {
		StringBuilder sb = new StringBuilder("SELECT ");
		String filterSql = buildSearchSqlFilters(assistFieldMap.get(1));
		String groupSql = buildGroupSql(assistFieldMap.get(2));
		String convergeSql = buildConvergeSql(assistFieldMap.get(3));
		String orderSql = buildOrderSql(assistFieldMap.get(4));
		String tableSql = buildTableSql();
		sb.append(groupSql);
		if(StringUtils.isNotBlank(groupSql) && StringUtils.isNoneBlank(convergeSql))
			sb.append(",");
		if(StringUtils.isNoneBlank(convergeSql)){
			sb.append(convergeSql);
		}
		sb.append(" FROM ")
			.append(tableSql).append(" WHERE 1=1 ").append(filterSql);
		if(StringUtils.isNotBlank(groupSql))
			sb.append(" GROUP BY ").append(groupSql);
		if(StringUtils.isNotBlank(orderSql))
			sb.append(" ORDER BY ").append(orderSql);
		return sb.toString();
	}
	
	/**
	 * 创建查询表语句
	 * @param mTdFormTable
	 * @param sTdFormTable
	 * @return
	 */
	private String buildTableSql() {
		StringBuilder result = new StringBuilder();
		result.append(mTdFormTable.getEnName()).append(" T ");
		if(sTdFormTable!=null)
			result.append(" LEFT JOIN ").append(sTdFormTable.getEnName()).append(" T1 ON T.RID = T1.MAIN_ID ");
		return result.toString();
	}

	/**
	 * 构建排序sql
	 * @param list
	 * @return
	 */
	private String buildOrderSql(List<TdFormField> list) {
		if(list==null)
			return "";
		StringBuilder sb = new StringBuilder();
		for(TdFormField field : list){
			String orderMethod = OrderType.getRealNameByValue(field.getOrderType());
			if(!StringUtils.isNotBlank(orderMethod))
				continue;
			sb.append(",").append(field.getFieldSql())
			.append(orderMethod);
		}
		return sb.length()>0?sb.substring(1):" ";
	}

	/**
	 * 构建聚合sql
	 * @param list
	 * @return
	 */
	private String buildConvergeSql(List<TdFormField> list) {
		if(list==null)
			return " * ";
		StringBuilder sb = new StringBuilder();
		for(TdFormField field : list){
			String convergeMethod = ConvergeType.getRealNameByRealValue(field.getConvergeType());
			if(!StringUtils.isNotBlank(convergeMethod))
				continue;
			sb.append(",").append(convergeMethod)
				.append(field.getFieldSql()).append(") ");
		}
		return sb.length()>0?sb.substring(1):" ";
	}

	/**
	 * 构建分组sql
	 * @param list
	 * @return
	 */
	private String buildGroupSql(List<TdFormField> list) {
		if(list==null)
			return "";
		StringBuilder sb = new StringBuilder();
		for(TdFormField field : list){
			sb.append(",").append(field.getFieldSql());
		}
		return sb.length()>0?sb.substring(1):" ";
	}

	/**
	 * 填充sql的过滤条件
	 * @param list
	 * @return
	 */
	private String buildSearchSqlFilters(List<TdFormField> list) {
		if(list==null)
			return "";		
		StringBuilder sb = new StringBuilder();
		for(TdFormField field : list){
			if(field.getSearchType()==(SearchFlag.GT.getFlag())){
				sb.append(" AND ").append(field.getFieldSql()).append(SearchFlag.GT.getRealFlag()).append(":")
					.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
			}else if(field.getSearchType()==(SearchFlag.EGT.getFlag())){
				sb.append(" AND ").append(field.getFieldSql()).append(SearchFlag.EGT.getRealFlag()).append(":")
					.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
			}else if(field.getSearchType()==(SearchFlag.LT.getFlag())){
				sb.append(" AND ").append(field.getFieldSql()).append(SearchFlag.LT.getRealFlag()).append(":")
					.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
			}else if(field.getSearchType()==(SearchFlag.ELT.getFlag())){
				sb.append(" AND ").append(field.getFieldSql()).append(SearchFlag.ELT.getRealFlag()).append(":")
					.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
			}else if(field.getSearchType()==(SearchFlag.LIKE.getFlag())){
				sb.append(" AND ").append(field.getFieldSql()).append(SearchFlag.LIKE.getRealFlag()).append(" '%'||:")
					.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname()).append("||'%' ");
			}else if(field.getSearchType()==(SearchFlag.BETWEEN.getFlag())){
					sb.append(" AND ").append(field.getFieldSql()).append(SearchFlag.EGT.getRealFlag())
					.append(":").append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
					sb.append(" AND ").append(field.getFieldSql()).append(SearchFlag.ELT.getRealFlag())
					.append(":").append(GenHtmlCodeUtil.DYNC_PREFIX_NEXT_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
			}else{
				sb.append(" AND ").append(field.getFieldSql()).append(SearchFlag.EQ.getRealFlag()).append(":")
				.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
			}
		}
		return sb.toString();
	}

	/**
	 * 分装字段数据
	 * 1查询 2分组 3聚合 4排序
	 */
	private void packAssistFieldMap() {
		this.assistFieldMap = Maps.newHashMap();
		for(TdFormField field:assistFieldList){
			Integer fieldType = field.getFieldType();
			List<TdFormField> list = assistFieldMap.get(fieldType);
			if(list==null)
				list = Lists.newArrayList();
			field.setFdEnname(tempFieldTypeMap.get(field.getFieldSql()).getFdEnname());
			list.add(field);
			assistFieldMap.put(fieldType, list);
		}
	}

	/**
	 * 生成SQL初始化
	 * @return 
	 */
	public boolean stepSqlInit(){
		packAssistFieldMap();
		statisticsSql = buildStatisticsSql();
		return true;
	}
	
	/**
	 * 添加统计脚本初始化
	 */
	public void addShellInit(){
		if(dealMethods==null)
			dealMethods = MathType.initOrderType();
		fieldOne=0;
		fieldTwo=1;
	}
	
	/**
	 * 初始化脚本
	 */
	private void initStatisticsShell() {
		// TODO Auto-generated method stub
		StringBuilder sb = new StringBuilder();
		sb.append("import java.util.List;\r\n")
		.append("import java.util.Arrays;\r\n")
		.append("import java.util.ArrayList;\r\n")
		.append("import com.chis.modules.system.utils.Arith;\r\n")
		.append("for(int i=0;i<dataList.size();i++){\r\n")
		.append("\tObject[] items = dataList.get(i);\r\n")
		.append("\tList<Object> itemList = new ArrayList(Arrays.asList(items));\r\n")
		.append(this.SHELLHANDLEFLAG)
		.append("\titems = itemList.toArray(new Object[itemList.size()]);\r\n")
		.append("\tdataList.remove(i);\r\n")
		.append("\tdataList.add(i, items);\r\n")
		.append("}\r\n");
		statisticsShell = sb.toString();
	}

	/**
	 * 添加统计脚本方法
	 */
	public void addStatisticsShell(){
		StringBuilder sb = new StringBuilder();
		switch (dealMethod) {
			case 0:
				//合计
				sb.append("\titemList.add(Arith.add(itemList.get(").append(fieldOne).append("), itemList.get(").append(fieldTwo).append(")));\r\n");
				break;
			case 1:
				//百分比
				sb.append("\titemList.add(Arith.div(itemList.get(").append(fieldOne).append("), itemList.get(").append(fieldTwo).append(")));\r\n");
				break;
			default:
				break;
		}
		statisticsShell = statisticsShell.replaceFirst(this.SHELLHANDLEFLAG, sb.append(this.SHELLHANDLEFLAG).toString());
	}
	
	/**
	 * 数据处理初始化
	 * @return 
	 */
	public boolean stepDataInit(){
		if(!StringUtils.isNotBlank(statisticsShell)){
			initStatisticsShell();
		}
		return true;
	}
	
	/**
	 * 统计图配置初始化
	 * @return 
	 */
	public boolean stepEchartsInit(){
		return true;
	}
	
	/**
	 * 对统计图需要的数据进行封装验证
	 */
	private boolean packEchartsInitData() {
		// TODO Auto-generated method stub
		echartsTitleList = Lists.newArrayList();
		echartsDataList = Lists.newArrayList();
		for(TopTitlePo item:topTitles){
			if(item.isEcharts){
				switch (item.getColType()) {
					case 0:
						echartsDataList.add(item);
						break;
					case 1:
						echartsTitleList.add(item);
						break;
					default:
						break;
				}
			}
		}
		if(echartsTitleList.size()!=1){
			JsfUtil.addErrorMessage("请只选择一项统计图描述！");
			return false;
		}
		if(echartsDataList.size()==0){
			JsfUtil.addErrorMessage("请选择至少一项统计图数据！");
			return false;
		}
		if(EchartsType.PIE.getValue().equals(echartsType)&&
				echartsDataList.size()!=1){
			JsfUtil.addErrorMessage("饼图只可选择一项统计图数据！");
			return false;
		}
		return true;
	}

	/**
	 * 删除表头定义
	 */
	public void removeTopTitle(){
		topTitles.remove(topTitlePo);
	}
	
	/**
	 * 新增表头定义
	 */
	public void addTopTitle(){
		TopTitlePo item = new TopTitlePo(0);
		topTitles.add(item);
	}
	
	/**
	 * 数据表头初始化
	 * @return 
	 */
	public boolean stepTitleInit(){
		return true;
	}
	
	/**
	 * HTML初始化
	 * @return 
	 */
	public boolean stepHtmlInit(){
		if(!packEchartsInitData()){
			return false;
		}
		genStatisticsHtmlCode();
		return true;
	}
	
	/**
	 * 生成列表html代码
	 * @param mTdFormTable
	 * @return 
	 *
	 */
	private void genStatisticsHtmlCode() {
		Map<String, Object> model = new HashMap<String, Object>();
		//生成查询字段数据
		model.put("gen_search_fields", buildSearchFields(assistFieldMap.get(1)));
		model.put("gen_title_fields", topTitles);
		model.put("echartsOption", buildEchartsOption());
		TopTitlePo toptitle = new TopTitlePo();
		toptitle.setTopTitles(topTitles);
		model.put("gen_top_title", JSON.toJSONString(toptitle));
		String genFreeMarkerStr = this.genFreeMarkerStr("dynaModelStatistics.xml", model);
		statisticsHtmlCode = genFreeMarkerStr;
	}
	
	/**
	 * 用来创建统计图option数据
	 * @return
	 */
	private String buildEchartsOption() {
		Map<String, Object> model = new HashMap<String, Object>();
		//生成查询字段数据
		model.put("gen_title", echartsTitleList);
		model.put("gen_data", echartsDataList);
		model.put("gen_type", echartsType);
		model.put("gen_form_name", bindTdFormStatisticsDef.getFormName());
		String genFreeMarkerStr = this.genFreeMarkerStr("dynaModelStatisticsEcharts.xml", model);
		return genFreeMarkerStr;
	}

	/**
	 * 解析FreeMarker字符串
	 * 
	 * @param fileName
	 * @param model
	 * @return
	 */
	private String genFreeMarkerStr(String fileName, Map<String, Object> model) {
		if (StringUtils.isNotBlank(fileName) && null != model) {
			StringBuilder filePath = new StringBuilder();
			filePath.append(DYNA_MODEL_PATH).append("/").append(fileName);
			DynaStyleTemplate genDynaObject = this.genDynaObject(filePath.toString());
			String content = genDynaObject.getContent();
			String renderString = FreeMarkers.renderString(content, model);
			return renderString;
		}
		return null;
	}
	
	/**
	 * 创建查询字段集合
	 * @return
	 */
	private List<TdFormField> buildSearchFields(List<TdFormField> fields) {
		List<TdFormField> list = Lists.newArrayList();
		for(TdFormField field:fields){
			GenHtmlCodeUtil gu = new GenHtmlCodeUtil();
			TdFormField item = tempFieldTypeMap.get(field.getFieldSql());
			if(item==null)
				continue;
			field.setFdDbtype(item.getFdDbtype());
			field.setDataSrc(item.getDataSrc());
			field.setFdCnname(item.getFdCnname());
			field.setFreeMarkerStr(gu.genSearchHtmlElement(field, true, false, commService,true));
			list.add(field);
		}
		return list;
	}
	
	/**
	 * 确认提交生成信息
	 */
	public void assistSubmit(){
		bindTdFormStatisticsDef.setStatisticsSql(statisticsSql);
		bindTdFormStatisticsDef.setStatisticsHtml(statisticsHtmlCode);
		bindTdFormStatisticsDef.setDataShell(statisticsShell);
		forwardEditPage();
	}
	
	public Integer getFormTypeId() {
		return formTypeId;
	}

	public void setFormTypeId(Integer formTypeId) {
		this.formTypeId = formTypeId;
	}

	public List<TdFormType> getFormTypeList() {
		return formTypeList;
	}

	public void setFormTypeList(List<TdFormType> formTypeList) {
		this.formTypeList = formTypeList;
	}

	public String getFormName() {
		return formName;
	}

	public void setFormName(String formName) {
		this.formName = formName;
	}

	public String[] getSearchState() {
		return searchState;
	}

	public void setSearchState(String[] searchState) {
		this.searchState = searchState;
	}

	public Integer getFormId() {
		return formId;
	}

	public void setFormId(Integer formId) {
		this.formId = formId;
	}

	public TdFormStatisticsDef getBindTdFormStatisticsDef() {
		return bindTdFormStatisticsDef;
	}

	public void setBindTdFormStatisticsDef(
			TdFormStatisticsDef bindTdFormStatisticsDef) {
		this.bindTdFormStatisticsDef = bindTdFormStatisticsDef;
	}

	public TdFormType getBindTdFormType() {
		return bindTdFormType;
	}

	public void setBindTdFormType(TdFormType bindTdFormType) {
		this.bindTdFormType = bindTdFormType;
	}

	public List<SelectItem> getFormModelList() {
		return formModelList;
	}

	public void setFormModelList(List<SelectItem> formModelList) {
		this.formModelList = formModelList;
	}

	public SessionData getSessionData() {
		return sessionData;
	}

	public void setSessionData(SessionData sessionData) {
		this.sessionData = sessionData;
	}

	public String getFormTypeDiag() {
		return formTypeDiag;
	}

	public void setFormTypeDiag(String formTypeDiag) {
		this.formTypeDiag = formTypeDiag;
	}

	public List<TdFormType> getSourceTypeList() {
		return sourceTypeList;
	}

	public void setSourceTypeList(List<TdFormType> sourceTypeList) {
		this.sourceTypeList = sourceTypeList;
	}

	public List<TdFormType> getShowTypeList() {
		return showTypeList;
	}

	public void setShowTypeList(List<TdFormType> showTypeList) {
		this.showTypeList = showTypeList;
	}

	public TdFormTable getmTdFormTable() {
		return mTdFormTable;
	}

	public void setmTdFormTable(TdFormTable mTdFormTable) {
		this.mTdFormTable = mTdFormTable;
	}

	public TdFormTable getsTdFormTable() {
		return sTdFormTable;
	}

	public void setsTdFormTable(TdFormTable sTdFormTable) {
		this.sTdFormTable = sTdFormTable;
	}

	public List<SelectItem> getStyleList() {
		return styleList;
	}

	public void setStyleList(List<SelectItem> styleList) {
		this.styleList = styleList;
	}

	public String getTableNameDiag() {
		return tableNameDiag;
	}

	public void setTableNameDiag(String tableNameDiag) {
		this.tableNameDiag = tableNameDiag;
	}

	public String getTableModelDiag() {
		return tableModelDiag;
	}

	public void setTableModelDiag(String tableModelDiag) {
		this.tableModelDiag = tableModelDiag;
	}

	public List<Object[]> getSourceTableList() {
		return sourceTableList;
	}

	public void setSourceTableList(List<Object[]> sourceTableList) {
		this.sourceTableList = sourceTableList;
	}

	public List<Object[]> getShowTableList() {
		return showTableList;
	}

	public void setShowTableList(List<Object[]> showTableList) {
		this.showTableList = showTableList;
	}

	public Integer getDynaFormId() {
		return dynaFormId;
	}

	public void setDynaFormId(Integer dynaFormId) {
		this.dynaFormId = dynaFormId;
	}

	public String getDynaFormName() {
		return dynaFormName;
	}

	public void setDynaFormName(String dynaFormName) {
		this.dynaFormName = dynaFormName;
	}

	public List<SelectItem> getFieldSelect() {
		return fieldSelect;
	}

	public void setFieldSelect(List<SelectItem> fieldSelect) {
		this.fieldSelect = fieldSelect;
	}

	public List<SelectItem> getSearchTypeList() {
		return searchTypeList;
	}

	public void setSearchTypeList(List<SelectItem> searchTypeList) {
		this.searchTypeList = searchTypeList;
	}

	public List<TdFormField> getAssistFieldList() {
		return assistFieldList;
	}

	public void setAssistFieldList(List<TdFormField> assistFieldList) {
		this.assistFieldList = assistFieldList;
	}

	public TdFormField getFilterField() {
		return filterField;
	}

	public void setFilterField(TdFormField filterField) {
		this.filterField = filterField;
	}

	public List<SelectItem> getFieldConfigList() {
		return fieldConfigList;
	}

	public void setFieldConfigList(List<SelectItem> fieldConfigList) {
		this.fieldConfigList = fieldConfigList;
	}

	public List<SelectItem> getConvergeTypeList() {
		return convergeTypeList;
	}

	public void setConvergeTypeList(List<SelectItem> convergeTypeList) {
		this.convergeTypeList = convergeTypeList;
	}

	public List<SelectItem> getOrderTypeList() {
		return orderTypeList;
	}

	public void setOrderTypeList(List<SelectItem> orderTypeList) {
		this.orderTypeList = orderTypeList;
	}

	public String getStatisticsSql() {
		return statisticsSql;
	}

	public void setStatisticsSql(String statisticsSql) {
		this.statisticsSql = statisticsSql;
	}

	public String getStatisticsShell() {
		return statisticsShell;
	}

	public void setStatisticsShell(String statisticsShell) {
		this.statisticsShell = statisticsShell;
	}

	public Integer getDealMethod() {
		return dealMethod;
	}

	public void setDealMethod(Integer dealMethod) {
		this.dealMethod = dealMethod;
	}

	public List<SelectItem> getDealMethods() {
		return dealMethods;
	}

	public void setDealMethods(List<SelectItem> dealMethods) {
		this.dealMethods = dealMethods;
	}

	public Integer getFieldOne() {
		return fieldOne;
	}

	public void setFieldOne(Integer fieldOne) {
		this.fieldOne = fieldOne;
	}

	public Integer getFieldTwo() {
		return fieldTwo;
	}

	public void setFieldTwo(Integer fieldTwo) {
		this.fieldTwo = fieldTwo;
	}

	public String getWizardIndex() {
		return wizardIndex;
	}

	public void setWizardIndex(String wizardIndex) {
		this.wizardIndex = wizardIndex;
	}

	public List<TopTitlePo> getTopTitles() {
		return topTitles;
	}

	public void setTopTitles(List<TopTitlePo> topTitles) {
		this.topTitles = topTitles;
	}

	public TopTitlePo getTopTitlePo() {
		return topTitlePo;
	}

	public void setTopTitlePo(TopTitlePo topTitlePo) {
		this.topTitlePo = topTitlePo;
	}

	public String getStatisticsHtmlCode() {
		return statisticsHtmlCode;
	}

	public void setStatisticsHtmlCode(String statisticsHtmlCode) {
		this.statisticsHtmlCode = statisticsHtmlCode;
	}

	public List<SelectItem> getEchartsTypeList() {
		return echartsTypeList;
	}

	public void setEchartsTypeList(List<SelectItem> echartsTypeList) {
		this.echartsTypeList = echartsTypeList;
	}

	public Integer getEchartsType() {
		return echartsType;
	}

	public void setEchartsType(Integer echartsType) {
		this.echartsType = echartsType;
	}


}
