//package com.chis.modules.system.web;
//
//import java.io.File;
//import java.io.IOException;
//import java.util.List;
//import java.util.Map;
//import java.util.Map.Entry;
//import java.util.Set;
//
//import javax.servlet.ServletException;
//import javax.servlet.http.HttpServlet;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//
//import org.apache.commons.lang.StringUtils;
//
//import DBstep.iMsgServer2000;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.chis.common.utils.JsfUtil;
//import com.chis.common.utils.SpringContextHolder;
//import com.chis.modules.system.entity.TdMsBookmark;
//import com.chis.modules.system.logic.MetaCondition;
//import com.chis.modules.system.service.CommServiceImpl;
//import com.chis.modules.system.utils.FileUtil;
//
///**
// * 存在使用漏洞，禁用此方法
// * 所有金格Office插件后台servlet的基类
// * <AUTHOR> 2015-04-03
// */
//@Deprecated
//public abstract class ZwxBaseFileServlet extends HttpServlet{
//
//	/**文件存放的路径*/
//	protected String docFilePath;
//	protected CommServiceImpl service = SpringContextHolder.getBean(CommServiceImpl.class);
//
//	@Override
//	public void init() throws ServletException {
//		super.init();
//
//		StringBuilder sb = new StringBuilder(JsfUtil.getAbsolutePath());
//        String tempFile = sb.append(this.initDocFilePath()).toString();
//        //路径不存在，则创建路径
//        File dirFile = new File(tempFile);
//        if(!dirFile.exists())    {
//            dirFile.mkdirs();
//        }
//        this.docFilePath  = tempFile+"/";
//	}
//
//	/**子类提供文件存放的路径*/
//	public abstract String initDocFilePath();
//
//	@Override
//	protected void doGet(HttpServletRequest req, HttpServletResponse resp)
//			throws ServletException, IOException {
//		doPost(req, resp);
//	}
//
//	/**
//	 *
//	 * msgObj.GetMsgByName的参数：OPTION、RECORDID、TEMPLATE、FILENAME、FILETYPE、USERNAME、EXTPARAM
//	 *
//	 * 其中EXTPARAM的格式为json字符串，有rid
//	 */
//	@Override
//	protected void doPost(HttpServletRequest req, HttpServletResponse resp)
//			throws ServletException, IOException {
//		iMsgServer2000 msgObj = new iMsgServer2000();
//		//office加载request里面的内容，进行初始化操作
//		msgObj.Load(req);
//		/**当前操作类型*/
//		String option = msgObj.GetMsgByName("OPTION");
//		/**office文件的名称,由页面传进来，服务器端可能存在，也可能不存在*/
//		String fileName = msgObj.GetMsgByName("FILENAME");
//		/**office文件的唯一键*/
//		String recordId = msgObj.GetMsgByName("RECORDID");
//		/**office文件的全路径*/
//		String fname = this.docFilePath + fileName;
//		/**office文件后缀名*/
//		String fileType = msgObj.GetMsgByName("FILETYPE");
//
//
//		//==============print test============
//		StringBuilder sb = new StringBuilder();
//		sb.append("【fname】").append(fname).append("\n");
//		sb.append("【option】").append(option).append("\n");
//		//==============print test============
//
//		System.out.println(sb.toString());
//
//		/**
//		 * LOADFILE：加载文件，不存在就创建一个新的文件
//		 * SAVEFILE: 保存文件
//		 * LISTBOOKMARKS:选择标签
//		 * LOADTEMPLATE：加载模板文件
//		 */
//		if(option.equalsIgnoreCase("LOADFILE")) {
//			File file = new File(fname);
//			if (file.exists()) {// 如果文件不存在，则不加载文件
//				msgObj.MsgFileLoad(fname);
//			}
//
//			//LOADFILE end
//		}else if(option.equalsIgnoreCase("SAVEFILE")) {
//			msgObj.MsgFileSave(fname);
//
//			//SAVEFILE end
//		}else if(option.equalsIgnoreCase("LISTBOOKMARKS")) {
//			msgObj.MsgTextClear(); // 清除文本信息
//
//			List<TdMsBookmark> list = this.service.findBookmarkList(recordId);
//			if(null != list && list.size() > 0) {
//				StringBuilder sb1 = new StringBuilder();
//				StringBuilder sb2 = new StringBuilder();
//
//				for(TdMsBookmark mark : list) {
//					sb1.append(mark.getBookmarkName()).append("\r\n");
//					sb2.append(mark.getBookmarkKey()).append("\r\n");
//				}
//
//				msgObj.SetMsgByName("BOOKMARK", sb1.toString());
//				msgObj.SetMsgByName("DESCRIPT", sb2.toString());
//			}
//
//			//LISTBOOKMARKS end
//		}else if(option.equalsIgnoreCase("LOADTEMPLATE")) {
//			// 获取模板recordId
//			String templateRcdId = msgObj.GetMsgByName("TEMPLATE");
//			msgObj.MsgFileClear();
//			msgObj.MsgTextClear();
//			if(StringUtils.isNotBlank(templateRcdId)) {
//				String templateFilePath = this.docFilePath + templateRcdId + fileType;
//				File file = new File(templateFilePath);
//				if (file.exists()) {// 如果文件不存在，则不加载文件
//					msgObj.MsgFileBody(FileUtil.convertFile2Bytes(file));
//				}
//			}
//			//LOADTEMPLATE end
//		}else if(option.equalsIgnoreCase("LOADBOOKMARKS")) {
////			msgObj.MsgTextClear();
//			/**json字符串 rid-主键*/
//			String extParam = msgObj.GetMsgByName("EXTPARAM");
//			// 获取模板recordId
//			String templateRcdId = msgObj.GetMsgByName("TEMPLATE");
//			//如果模版文件Id,不为空，则根据模版文件获取模版元素，否则根据当前文件获取
//			if(StringUtils.isBlank(templateRcdId))	{
//				templateRcdId = recordId;
//			}
//
//			JSONObject jsonObject = (JSONObject) JSON.parse(extParam);
//			MetaCondition cond = new MetaCondition();
//			cond.setBusinessId((String) jsonObject.get("rid"));
//			cond.setUnitId(Integer.parseInt(jsonObject.get("unitId").toString()));
//			cond.setUserId(Integer.parseInt(jsonObject.get("userId").toString()));
//
//			Map<String, String> markMap = this.service.resolveBookmarks(templateRcdId, cond);
//			if (null != markMap && markMap.size() > 0) {
//				Set<Entry<String, String>> entrySet = markMap.entrySet();
//				for (Entry<String, String> entry : entrySet) {
//					if (StringUtils.isNotBlank(entry.getValue())) {
//						if (!entry.getValue().equals(msgObj.GetMsgByName(entry.getKey()))) {
//							msgObj.SetMsgByName(entry.getKey(), entry.getValue());
//						}
//					}
//				}
//			}
//		}
//		msgObj.Send(resp);
//	}
//
//}
