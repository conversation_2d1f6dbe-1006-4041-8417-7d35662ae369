package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.flow.web.MetaConditionFactory;
import com.chis.modules.system.entity.TbTempmetaDefine;
import com.chis.modules.system.entity.TdTempmetaDefine;
import com.chis.modules.system.entity.TdTempmetaType;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * <AUTHOR>
 * @createDate 2015-02-03.
 */
@ManagedBean(name = "msgTemplateBean")
@ViewScoped
public class MsgTemplateBean extends FacesSimpleBean {
    private static final long serialVersionUID = -8149078357961029666L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/** ejb session bean */
	private SystemModuleServiceImpl systemService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);



    /*系统类型*/
    private Short sysType;
    /*系统类型列表*/
    private Map<String,Short> sysTypeMap;
    /*类型*/
    private TdTempmetaType tdTempmetaType = new TdTempmetaType();
    /*类型id*/
    private Integer tdTempmetaTypeId;
    /*编辑系统类型*/
    private Short editSystType;
    /*模板集合*/
    private List<TdTempmetaDefine> tdTempmetaDefineList;
    /*模板*/
    private TdTempmetaDefine tdTempmetaDefine;
    /*模板Id*/
    private Integer tdTempmetaDefineId;
    /**模板元素集合*/
    private List<TbTempmetaDefine> defineList;
    /*模板类型系统*/
    private SystemType tempSysType;
    /**模板元素集合*/
    private List<TbTempmetaDefine> filterDefineList = new ArrayList<TbTempmetaDefine>(0);
    /**表格的过滤下拉*/
    private List<SelectItem> filterDefineSelectList = new ArrayList<SelectItem>(0);
    /*模板内容*/
    private String templateContent;
    /*测试内容*/
    private String resolvedContent;
    /*模板元素*/
    private TbTempmetaDefine tbTempmetaDefine;


    public MsgTemplateBean() {
        initSysType();
        searchAction();
    }

    private void initSysType(){
        sysTypeMap = new LinkedHashMap<String, Short>();
        SystemType[] systemTypes = SystemType.values();
        for(SystemType s : systemTypes){
            sysTypeMap.put(s.getTypeCN(),s.getTypeNo());
        }
    }

    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder("");
        sb.append("from TdTempmetaType t where 1 = 1");
        if(sysType != null){
            sb.append(" and t.systemType = ").append(sysType);
        }
        String h2 = "select count(*) " + sb.toString();
        String h1 = "select t " + sb.append(" order by t.systemType,t.tempCode").toString();
        return new String[]{h1,h2};
    }

    /**
     * 添加类型
     */
    public void addInitAction(){
        tdTempmetaType = new TdTempmetaType();
        editSystType = null;
    }

    /**
     * 修改类型
     */
    public void modInitAction(){
        editSystType = tdTempmetaType.getSystemType().getTypeNo();
    }

    /**
     * 保存
     */
    public void saveAction(){
        tdTempmetaType.setSystemType((SystemType) EnumUtils.findEnum(SystemType.class,editSystType));
        if(tdTempmetaType.getRid() == null){
            tdTempmetaType.setCreateManid(sessionData.getUser().getRid());
        }
        tdTempmetaType.setCreateDate(new Date());
        String msg = systemService.saveOrUpdateTdTempmetaType(tdTempmetaType);
        if(msg != null){
            JsfUtil.addErrorMessage(msg);
            return;
        }else{
            if(tdTempmetaType.getRid()==null){
                JsfUtil.addSuccessMessage("保存成功！");
            }else{
                JsfUtil.addSuccessMessage("修改成功！");
            }
            RequestContext.getCurrentInstance().execute("PF('EditDialog').hide()");
        }
        searchAction();
    }

    /**
     * 删除类型
     */
    public void deleteAction(){
        String msg = systemService.deleteTdTempmetaType(tdTempmetaTypeId);
        if(null != msg ){
            JsfUtil.addErrorMessage(msg);
        }else{
            JsfUtil.addSuccessMessage("删除成功！");
            searchAction();
        }
    }

    /**
     * 类型模板维护
     */
    public void tempmetaDefineAction(){
        tdTempmetaDefineList = systemService.findTdTempmetaByType(tdTempmetaTypeId);
        //根据项目的枚举初始化元素列表
        defineList = systemService.findTemplateDefine(tempSysType);
    }

    /**
     * 添加模板
     */
    public void addTemplateInit(){
        //初始化模板元素
        initTemplateDefine();
        //测试内容
        resolvedContent = null;
        //模板内容
        templateContent = null;
        //模板初始化
        tdTempmetaDefine = new TdTempmetaDefine();
        //元素初始化
        tbTempmetaDefine = new TbTempmetaDefine();

    }

    /**
     * 初始化模板元素
     */
    private void initTemplateDefine(){
        //初始化模板元素集合
        if (null != this.defineList && !this.defineList.isEmpty()) {
            //元素系统类型
            this.filterDefineSelectList = new ArrayList<SelectItem>();
            Map<Short, Short> map = new HashMap<Short, Short>();
            //comm过滤元素
            this.filterDefineList = new ArrayList<TbTempmetaDefine>();
            for (TbTempmetaDefine define : this.defineList) {
                if (SystemType.COMM.equals(define.getSystemType())) {
                    this.filterDefineList.add(define);
                }
                if (null == map.get(define.getSystemType().getTypeNo())) {
                    this.filterDefineSelectList.add(new SelectItem(define.getSystemType().getTypeCN(), define.getSystemType().getTypeCN()));
                    map.put(define.getSystemType().getTypeNo(), define.getSystemType().getTypeNo());
                }
            }
        }
    }

    /**
     * 单击，刷新元素实例
     */
    public void refreshMetaDemo() {
        this.findDefine();
    }

    /**
     * 双击，添加元素
     */
    public void addMeta() {
        this.findDefine();
        if(StringUtils.isNotBlank(templateContent)) {
            templateContent = templateContent + tbTempmetaDefine.getMetaName();
        }else {
            templateContent = tbTempmetaDefine.getMetaName();
        }
    }

    /**
     * 元素的单击双击事件，根据传入的RID查找元素对象
     */
    private void findDefine() {
        String ridStr = JsfUtil.getRequestParameterMap().get("param1");
        if(StringUtils.isNotBlank(ridStr)) {
            for(TbTempmetaDefine define : this.defineList) {
                if(ridStr.equals(define.getRid().toString())) {
                    tbTempmetaDefine = define;
                    return;
                }
            }
        }else {
            tbTempmetaDefine = new TbTempmetaDefine();
        }
    }

    /**
     * 测试模板
     */
    public void resolveTemplateAction(){
        if(StringUtils.isNotBlank(templateContent)) {
            this.resolvedContent = commService.resolveTemplatemeta(templateContent,
                    MetaConditionFactory.produceCondition(this.sessionData), true);
        }else {
            this.resolvedContent = null;
        }
    }

    /**
     * 保存模板
     */
    public void saveTemplateAction(){
        if(StringUtils.isBlank(templateContent)){
            JsfUtil.addErrorMessage("模板内容不能为空！");
            return;
        }
        tdTempmetaDefine.setTempContent(templateContent);
        if(tdTempmetaDefine.getRid() == null){
            tdTempmetaDefine.setCreateManid(sessionData.getUser().getRid());
            tdTempmetaDefine.setTdTempmetaType(tdTempmetaType);
            //如果tdTempmetaDefineList为空或者没有默认模板，则设置为默认,否则不为默认
            boolean flag = false;
            for(TdTempmetaDefine t : tdTempmetaDefineList){
                if(t.getIsDefault().equals((short)1)){
                    flag = true;
                    break;
                }
            }
            if(!flag){
                tdTempmetaDefine.setIsDefault((short)1);
            }else {
                tdTempmetaDefine.setIsDefault((short)0);
            }
        }
        tdTempmetaDefine.setCreateDate(new Date());
        systemService.saveOrUpdateTdTempmetaDefine(tdTempmetaDefine);
        if(tdTempmetaDefine.getRid()==null){
            JsfUtil.addSuccessMessage("保存成功！");
        }else{
            JsfUtil.addSuccessMessage("修改成功！");
        }
        RequestContext.getCurrentInstance().execute("PF('EditTemplateDialog').hide()");
        tdTempmetaDefineList = systemService.findTdTempmetaByType(tdTempmetaTypeId);
    }

    /**
     * 修改模板
     */
    public void modTemplateAction(){
        initTemplateDefine();
        resolvedContent = null;
        templateContent = tdTempmetaDefine.getTempContent();
        //元素初始化
        tbTempmetaDefine = new TbTempmetaDefine();
    }

    /**
     * 删除模板
     */
    public void deleteTemplateAction(){
        String msg = systemService.deleteTdTempmetaDefine(tdTempmetaDefineId);
        if(null != msg ){
            JsfUtil.addErrorMessage(msg);
        }else{
            JsfUtil.addSuccessMessage("删除成功！");
            tdTempmetaDefineList = systemService.findTdTempmetaByType(tdTempmetaTypeId);
        }
    }

    /**
     * 设置默认
     */
    public void defaultTempSetAction(){
        systemService.updateTdTempmetaDefine(tdTempmetaTypeId,tdTempmetaDefineId);
        tdTempmetaDefineList = systemService.findTdTempmetaByType(tdTempmetaTypeId);
    }


    public Short getSysType() {
        return sysType;
    }

    public void setSysType(Short sysType) {
        this.sysType = sysType;
    }

    public Map<String, Short> getSysTypeMap() {
        return sysTypeMap;
    }

    public void setSysTypeMap(Map<String, Short> sysTypeMap) {
        this.sysTypeMap = sysTypeMap;
    }

    public TdTempmetaType getTdTempmetaType() {
        return tdTempmetaType;
    }

    public void setTdTempmetaType(TdTempmetaType tdTempmetaType) {
        this.tdTempmetaType = tdTempmetaType;
    }

    public Integer getTdTempmetaTypeId() {
        return tdTempmetaTypeId;
    }

    public void setTdTempmetaTypeId(Integer tdTempmetaTypeId) {
        this.tdTempmetaTypeId = tdTempmetaTypeId;
    }

    public Short getEditSystType() {
        return editSystType;
    }

    public void setEditSystType(Short editSystType) {
        this.editSystType = editSystType;
    }

    public List<TdTempmetaDefine> getTdTempmetaDefineList() {
        return tdTempmetaDefineList;
    }

    public void setTdTempmetaDefineList(List<TdTempmetaDefine> tdTempmetaDefineList) {
        this.tdTempmetaDefineList = tdTempmetaDefineList;
    }

    public TdTempmetaDefine getTdTempmetaDefine() {
        return tdTempmetaDefine;
    }

    public void setTdTempmetaDefine(TdTempmetaDefine tdTempmetaDefine) {
        this.tdTempmetaDefine = tdTempmetaDefine;
    }

    public Integer getTdTempmetaDefineId() {
        return tdTempmetaDefineId;
    }

    public void setTdTempmetaDefineId(Integer tdTempmetaDefineId) {
        this.tdTempmetaDefineId = tdTempmetaDefineId;
    }

    public SystemType getTempSysType() {
        return tempSysType;
    }

    public void setTempSysType(SystemType tempSysType) {
        this.tempSysType = tempSysType;
    }

    public List<TbTempmetaDefine> getDefineList() {
        return defineList;
    }

    public void setDefineList(List<TbTempmetaDefine> defineList) {
        this.defineList = defineList;
    }

    public List<TbTempmetaDefine> getFilterDefineList() {
        return filterDefineList;
    }

    public void setFilterDefineList(List<TbTempmetaDefine> filterDefineList) {
        this.filterDefineList = filterDefineList;
    }

    public List<SelectItem> getFilterDefineSelectList() {
        return filterDefineSelectList;
    }

    public void setFilterDefineSelectList(List<SelectItem> filterDefineSelectList) {
        this.filterDefineSelectList = filterDefineSelectList;
    }

    public String getTemplateContent() {
        return templateContent;
    }

    public void setTemplateContent(String templateContent) {
        this.templateContent = templateContent;
    }

    public String getResolvedContent() {
        return resolvedContent;
    }

    public void setResolvedContent(String resolvedContent) {
        this.resolvedContent = resolvedContent;
    }

    public TbTempmetaDefine getTbTempmetaDefine() {
        return tbTempmetaDefine;
    }

    public void setTbTempmetaDefine(TbTempmetaDefine tbTempmetaDefine) {
        this.tbTempmetaDefine = tbTempmetaDefine;
    }


}
