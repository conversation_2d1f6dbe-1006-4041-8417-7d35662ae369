package com.chis.modules.system.web;

import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.List;

@ManagedBean(name="indusTypeCodePanelBean")
@ViewScoped
public class IndusTypeCodePanelBean extends FacesBean{
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);

    /** 所有行业类别 */
    private List<TsSimpleCode> indusList;
    /** 显示的行业类别 */
    private List<TsSimpleCode> showIndusList;
    /** 门类列表 */
    private List<TsSimpleCode> topTypeList;
    /** 门类对应的大类列表 */
    private List<TsSimpleCode> fatherTypeList;
    /** 大类对应的中类列表 */
    private List<TsSimpleCode> middleTypeList;
    /** 门类编码 */
    private String selectTopCodeNo;
    /** 门类对应的大类编码 */
    private String selectFatherCodeNo;
    /** 大类对应的中类编码 */
    private String selectMiddleCodeNo;
    /** 检索文本框 */
    private String searchContext;
    public IndusTypeCodePanelBean(){
        this.init();
    }

    public IndusTypeCodePanelBean(List<TsSimpleCode> indusList){
        this.indusList = indusList;
        this.init();
    }

    /**
     * @Description: 局部刷新
     *
     * @MethodAuthor pw,2022年02月7日
     */
    public void partInit(){
        this.fatherTypeList = new ArrayList<>();
        this.middleTypeList = new ArrayList<>();
        this.selectTopCodeNo = null;
        this.selectFatherCodeNo = null;
        this.selectMiddleCodeNo = null;
        this.searchContext = null;
        this.showIndusList = new ArrayList<>();
        if(CollectionUtils.isEmpty(this.indusList)){
            return;
        }
        this.showIndusList.addAll(this.indusList);
    }

    /**
     * @Description: 初始化
     *
     * @MethodAuthor pw,2022年02月7日
     */
    public void init(){
        if(CollectionUtils.isEmpty(this.indusList)){
            this.indusList = commService.findNumSimpleCodesByTypeId("5002");
        }
        this.topTypeList = new ArrayList<>();
        if(CollectionUtils.isEmpty(this.indusList)){
            return;
        }
        List<String> codeList = new ArrayList<>();
        for(TsSimpleCode simpleCode : this.indusList){
            String codeNo = StringUtils.isBlank(simpleCode.getCodeNo()) ? null : simpleCode.getCodeNo().trim();
            String codeLevelNo = StringUtils.isBlank(simpleCode.getCodeLevelNo()) ? null :
                    simpleCode.getCodeLevelNo().trim();
            if(null == codeNo || null == codeLevelNo){
                continue;
            }
            int level = StringUtils.countMatches(codeLevelNo, ".");
            if(0 == level){
                this.topTypeList.add(simpleCode);
            }
            String codeNoArr[] = codeLevelNo.split("\\.");
            int len = codeNoArr.length;
            for(int i = 0; i < len; i++){
                String curNo = codeNoArr[i];
                if(len-1 != i && !codeList.contains(curNo)){
                    codeList.add(curNo.trim());
                }
            }
        }
        for(TsSimpleCode simpleCode : this.indusList){
            String codeNo = StringUtils.isBlank(simpleCode.getCodeNo()) ? null : simpleCode.getCodeNo().trim();
            if(null == codeNo || codeList.contains(codeNo)){
                continue;
            }
            simpleCode.setIfDisabled(true);
        }
        this.showIndusList = new ArrayList<>();
        if(CollectionUtils.isEmpty(this.indusList)){
            return;
        }
        partInit();
    }

    /**
     * @Description: 门类选择
     *
     * @MethodAuthor pw,2022年01月29日
     */
    public void onTopCodeTypeSelect(){
        this.fatherTypeList = new ArrayList<>();
        this.middleTypeList = new ArrayList<>();
        this.selectFatherCodeNo = null;
        this.selectMiddleCodeNo = null;
        if(CollectionUtils.isEmpty(this.indusList) || StringUtils.isBlank(this.selectTopCodeNo)){
            searchAction();
            return;
        }
        for(TsSimpleCode simpleCode : this.indusList){
            String codeNo = StringUtils.isBlank(simpleCode.getCodeNo()) ? null : simpleCode.getCodeNo().trim();
            String codeLevelNo = StringUtils.isBlank(simpleCode.getCodeLevelNo()) ? null :
                    simpleCode.getCodeLevelNo().trim();
            if(null == codeNo || null == codeLevelNo || !codeLevelNo.startsWith(this.selectTopCodeNo+".")){
                continue;
            }
            int level = StringUtils.countMatches(codeLevelNo, ".");
            if(1 == level){
                this.fatherTypeList.add(simpleCode);
            }
        }
        searchAction();
    }

    /**
     * @Description: 大类选择
     *
     * @MethodAuthor pw,2022年01月29日
     */
    public void onFatherCodeTypeSelect(){
        this.middleTypeList = new ArrayList<>();
        this.selectMiddleCodeNo = null;
        if(CollectionUtils.isEmpty(this.indusList) || StringUtils.isBlank(this.selectTopCodeNo) ||
                StringUtils.isBlank(this.selectFatherCodeNo)){
            searchAction();
            return;
        }
        StringBuffer buffer = new StringBuffer();
        for(TsSimpleCode simpleCode : this.indusList){
            String codeNo = StringUtils.isBlank(simpleCode.getCodeNo()) ? null : simpleCode.getCodeNo().trim();
            String codeLevelNo = StringUtils.isBlank(simpleCode.getCodeLevelNo()) ? null :
                    simpleCode.getCodeLevelNo().trim();
            buffer.setLength(0);
            buffer.append(this.selectTopCodeNo).append(".").append(this.selectFatherCodeNo).append(".");
            if(null == codeNo || null == codeLevelNo || !codeLevelNo.startsWith(buffer.toString())){
                continue;
            }
            int level = StringUtils.countMatches(codeLevelNo, ".");
            if(2 == level){
                this.middleTypeList.add(simpleCode);
            }
        }
        searchAction();
    }

    /**
     * @Description: 中类选择或者查询
     *
     * @MethodAuthor pw,2022年02月7日
     */
    public void searchAction(){
        this.showIndusList.clear();
        if(CollectionUtils.isEmpty(this.indusList)){
            return;
        }
        StringBuffer buffer = new StringBuffer();
        if(StringUtils.isNotBlank(this.selectTopCodeNo)){
            buffer.append(this.selectTopCodeNo);
        }
        if(StringUtils.isNotBlank(this.selectFatherCodeNo)){
            buffer.append(".").append(this.selectFatherCodeNo);
        }
        if(StringUtils.isNotBlank(this.selectMiddleCodeNo)){
            buffer.append(".").append(this.selectMiddleCodeNo);
        }
        String preCode = buffer.toString();
        for(TsSimpleCode simpleCode : this.indusList){
            String codeLevelNo = simpleCode.getCodeLevelNo();
            String codeName = simpleCode.getCodeName();
            String pym = simpleCode.getSplsht();
            int level = StringUtils.countMatches(codeLevelNo, ".");
            if(StringUtils.isNotBlank(preCode)){
                if(0 != level && !codeLevelNo.startsWith(preCode)){
                    continue;
                }
                if(0 == level && !preCode.trim().equals(codeLevelNo)){
                    continue;
                }
            }
            boolean flag = false;
            if(StringUtils.isNotBlank(this.searchContext)){
                if(level < 3){
                    continue;
                }
                if(StringUtils.isNotBlank(codeName) && codeName.contains(this.searchContext.trim())){
                    flag = true;
                }
                if(StringUtils.isNotBlank(pym) && pym.contains(this.searchContext.trim())){
                    flag = true;
                }
            }else{
                flag = true;
            }
            if(flag){
                this.showIndusList.add(simpleCode);
            }
        }
    }

    /**
     * @Description: 获取对应层级名称
     *
     * @MethodAuthor pw,2022年01月29日
     */
    public String splitCodePath(String codePath, int index){
        if(StringUtils.isBlank(codePath) || index < 0){
            return "";
        }
        String arr[] = codePath.split("_");
        if(index >= arr.length){
            return "";
        }else if(arr.length > 3 && 3 <= index){
            StringBuffer buffer = new StringBuffer();
            for(int i=3;i<arr.length;i++){
                buffer.append("_").append(arr[i]);
            }
            return buffer.substring(1);
        }else{
            return arr[index];
        }
    }

    public List<TsSimpleCode> getIndusList() {
        return indusList;
    }

    public void setIndusList(List<TsSimpleCode> indusList) {
        this.indusList = indusList;
    }

    public List<TsSimpleCode> getShowIndusList() {
        return showIndusList;
    }

    public void setShowIndusList(List<TsSimpleCode> showIndusList) {
        this.showIndusList = showIndusList;
    }

    public List<TsSimpleCode> getTopTypeList() {
        return topTypeList;
    }

    public void setTopTypeList(List<TsSimpleCode> topTypeList) {
        this.topTypeList = topTypeList;
    }

    public List<TsSimpleCode> getFatherTypeList() {
        return fatherTypeList;
    }

    public void setFatherTypeList(List<TsSimpleCode> fatherTypeList) {
        this.fatherTypeList = fatherTypeList;
    }

    public List<TsSimpleCode> getMiddleTypeList() {
        return middleTypeList;
    }

    public void setMiddleTypeList(List<TsSimpleCode> middleTypeList) {
        this.middleTypeList = middleTypeList;
    }

    public String getSelectTopCodeNo() {
        return selectTopCodeNo;
    }

    public void setSelectTopCodeNo(String selectTopCodeNo) {
        this.selectTopCodeNo = selectTopCodeNo;
    }

    public String getSelectFatherCodeNo() {
        return selectFatherCodeNo;
    }

    public void setSelectFatherCodeNo(String selectFatherCodeNo) {
        this.selectFatherCodeNo = selectFatherCodeNo;
    }

    public String getSelectMiddleCodeNo() {
        return selectMiddleCodeNo;
    }

    public void setSelectMiddleCodeNo(String selectMiddleCodeNo) {
        this.selectMiddleCodeNo = selectMiddleCodeNo;
    }

    public String getSearchContext() {
        return searchContext;
    }

    public void setSearchContext(String searchContext) {
        this.searchContext = searchContext;
    }
}
