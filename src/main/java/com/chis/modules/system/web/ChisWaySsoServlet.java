package com.chis.modules.system.web;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.service.SystemServiceImpl;
import org.primefaces.context.RequestContext;

import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

@WebServlet(name="ChisWaySsoServlet",value="/ChisWaySsoServlet")
public class ChisWaySsoServlet extends HttpServlet {
    /**url地址传用户ID的key*/
    //ejb session bean
    private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private SystemServiceImpl systemService = (SystemServiceImpl)SpringContextHolder.getBean(SystemServiceImpl.class);

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        Map<String,Object> params = new HashMap<String, Object>();
        BufferedReader br;
        try {
            br = request.getReader();
            String str, wholeStr = "";
            while((str = br.readLine()) != null){
                wholeStr += str;
            }
            if(StringUtils.isNotEmpty(wholeStr)){
                params = JSON.parseObject(wholeStr,Map.class);
            }
        } catch (IOException e1) {
            e1.printStackTrace();
        }
        String enParamObjectStr = params.get("enParamObjectStr").toString();
        String redirectUrl = PropertyUtils.getValueWithoutException("sso.redirectUrl");
        SymmetricCrypto sm4 = SmUtil.sm4("K8jP3nD2gFzQ7wR4".getBytes());
        String decryptStr = sm4.decryptStr(enParamObjectStr);
        JSONObject ssoData = JSONObject.parseObject(decryptStr);

        Map<String,Object> resultMap = new HashMap<>();
        JSONObject jsonData = new JSONObject();
        jsonData.put("redirectUrl", redirectUrl+"?ticket="+ssoData.getString("ticket"));
        resultMap.put("code", 200);
        resultMap.put("msg", "成功");
        resultMap.put("data", jsonData);
        String requestJson = JSON.toJSONString(resultMap);

        response.setContentType("text/json; charset=utf-8");
        PrintWriter out = response.getWriter();
        out.println(requestJson);
        out.flush();
        out.close();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        this.doPost(request, response);
    }
}
