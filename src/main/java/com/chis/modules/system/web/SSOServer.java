package com.chis.modules.system.web;

import java.io.IOException;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsMenu;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.service.SystemServiceImpl;

/**
 * 根据url直接打开某个开始
 * 参数u-用户名 m-菜单编码
 * <AUTHOR>
 * @createTime 2016年7月21日
 */
@WebServlet(name="SSOServer",value="/SSOServer")
public class SSOServer extends HttpServlet {
	
	private static final long serialVersionUID = -3621108376593353775L;
	//ejb session bean
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	private SystemServiceImpl systemService = (SystemServiceImpl)SpringContextHolder.getBean(SystemServiceImpl.class);
	private String baseUrl;
	
	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		String userno = request.getParameter("u");
		String password = request.getParameter("p");
		String menuNo = request.getParameter("m");

		
		this.baseUrl = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort();
			if(StringUtils.isNotBlank(request.getContextPath())){
				this.baseUrl = this.baseUrl+request.getContextPath();
			}
		SessionData sessionData = (SessionData) request.getSession().getAttribute(SessionData.SESSION_DATA);
		if(null == sessionData) {
			if(StringUtils.isNotBlank(userno)) {
				TsUserInfo user = this.systemModuleService.findUserByUserNo(userno);
				if(null != user) {
					sessionData = new SessionData(user);
					Map<String, String> btnMap = this.systemService.findUsersBtn(user.getRid());
	            	sessionData.setButtonMap(btnMap);
	            	sessionData.setBtnSet(systemService.findUserBtns(user.getRid()));
	            	request.getSession().setAttribute(SessionData.SESSION_DATA, sessionData);
	            	
	            	this.forward(request, response, menuNo);
				}else {
					response.sendRedirect(this.baseUrl+"/login.faces");
				}
			}else {
				response.sendRedirect(this.baseUrl+"/login.faces");
			}
		}else {
			this.forward(request, response, menuNo);
		}
	}
	
	private void forward(HttpServletRequest request, HttpServletResponse response, String menuEn) throws IOException, ServletException {
		if(StringUtils.isNotBlank(menuEn)) {
			TsMenu menu = this.systemModuleService.findMenuByMenuEn(menuEn);
			if(null != menu) {
				response.sendRedirect(this.baseUrl+menu.getMenuUri());
			}else {
				response.sendRedirect(this.baseUrl+"/login.faces");
			}
		}else {
			response.sendRedirect(this.baseUrl+"/login.faces");
		}

	}
	
	@Override
	protected void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		this.doPost(request, response);
	}

}