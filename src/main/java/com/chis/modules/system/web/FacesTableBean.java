package com.chis.modules.system.web;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.utils.DefaultLazyDataModel;

import java.util.HashMap;
import java.util.Map;

/**
 * 嵌入表格真分页的托管Bean
 */
public abstract class FacesTableBean extends FacesBean {
    protected boolean ifSQL = Boolean.FALSE;
    /**
     * 查询条件参数，key为参数名称，value为值
     */
    protected Map<String, Object> paramMap = new HashMap<>();
    /**
     * 真分页模型
     */
    private DefaultLazyDataModel dataModel;

    public FacesTableBean() {

    }

    /**
     * 查询操作，如果不满足，可重写
     */
    @SuppressWarnings("unchecked")
    public void searchAction() {
        this.paramMap.clear();
        String[] hql = this.buildHqls();
        String tableId = getTableId();
        if (StringUtils.isBlank(tableId)) {
            tableId = "mainForm:dataTable";
        }
        this.dataModel = new DefaultLazyDataModel(hql[0], hql[1], this.paramMap, this.buildProcessData(), tableId, this.ifSQL);
    }

    public abstract String getTableId();

    /**
     * 组织hql查询语句，同时有参数的话，向paramMap里面放值
     *
     * @return 0-查询记录的hql语句 1-查询记录数的hql语句
     */
    public abstract String[] buildHqls();

    /**
     * 查询数据处理接口，如果需要处理，则托管bean必须要实现
     * IProcessData接口，并且返回this；否则返回NULL
     */
    public IProcessData buildProcessData() {
        if (this instanceof IProcessData) {
            return (IProcessData) this;
        } else {
            return null;
        }
    }

    public DefaultLazyDataModel getDataModel() {
        return dataModel;
    }

    public void setDataModel(DefaultLazyDataModel dataModel) {
        this.dataModel = dataModel;
    }

}
