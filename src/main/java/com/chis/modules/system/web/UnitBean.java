package com.chis.modules.system.web;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.BaiduMapUtil;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.entity.TsBsSort;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUnitAttr;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
* 单位管理
* 
* @editContent 删除buildProcessData()方法，如果查询出来的数据集需要修改，子类不需要再重写buildProcessData()  david 2014-09-04
*  
* <AUTHOR>
*/
@ManagedBean(name="unitBean")
@ViewScoped
public class UnitBean extends FacesEditBean implements IProcessData{

	private static final long serialVersionUID = -738445454536625063L;
	/**存在session中的对象*/
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/**ejb session bean*/
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	/**单位对象*/
	private TsUnit tsUnit = new TsUnit();
	/**单位对象rid*/
	private Integer rid;
    /**查询条件：地区名称*/
    private String searchZoneName;
    /**查询条件：地区编码*/
    private String searchZoneCode;
    /**查询条件：单位名称*/
    private String searchUnitname;
    /**查询条件：状态*/
    private Short searchState;
    /**添加页面：单位属性下啦*/
    private Map<String, Integer> bsSortMap = new HashMap<String, Integer>(0);
    /**查询条件：单位属性*/
    private Integer searchSortId;
    /**添加页面选中的属性*/
    private List<String> selectSortList = new ArrayList<String>(0);
    /**添加页面的地区名称*/
    private String editZoneName;
    /**添加页面的地区id*/
    private Integer editZoneId;
    /**是否是超管*/
    private boolean ifAdmin = Boolean.TRUE;
    /**能看到的地区集合*/
    private List<TsZone> zoneList;
    /**当前最高地区级别*/
    private Short currentMaxZoneType;
	/** 纬度 */
	private BigDecimal degreeLat;
	private BigDecimal minuteLat;
	private BigDecimal secondLat;
	/** 经度 */
	private BigDecimal degreeLong;
	private BigDecimal minuteLong;
	private BigDecimal secondLong;

    @PostConstruct
	public void init() {
        TsUserInfo tsUserInfo = this.sessionData.getUser();
        if(!tsUserInfo.getUserNo().equals(Constants.ADMIN)) {
            ifAdmin = Boolean.FALSE;
        }
        this.initSearchCondition();
        this.searchAction();
	}

    /**
     * 初始化查询条件
     */
    private void initSearchCondition() {
        TsUserInfo user = this.sessionData.getUser();

        this.searchState = (short)1;
        this.searchZoneCode = user.getTsUnit().getTsZone().getZoneCode();
        this.searchZoneName = user.getTsUnit().getTsZone().getZoneName();

        /**
         * 地区初始化
         */
        if(null == this.zoneList || this.zoneList.size() <=0) {
            this.zoneList = this.commService.findZoneListNew(this.ifAdmin, user.getTsUnit().getTsZone().getZoneCode(),null,"6");
        }

        List<TsBsSort> sortList = this.commService.findSortsByApp();
        if(null != sortList && sortList.size() > 0) {
            for(TsBsSort t:sortList) {
                this.bsSortMap.put(t.getSortName(), t.getRid());
            }
        }

    }

    @Override
    public void addInit() {
        TsUserInfo user = sessionData.getUser();
        //初始化一些条件
        this.tsUnit = new TsUnit();
        this.tsUnit.setIfReveal((short)1);
        this.tsUnit.setCreateDate(new Date());
        this.tsUnit.setCreateManid(this.sessionData.getUser().getRid());

        this.rid = null;
        this.selectSortList = new ArrayList<String>(0);

        this.editZoneId = user.getTsUnit().getTsZone().getRid();
        this.editZoneName = user.getTsUnit().getTsZone().getZoneName();

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {
        this.tsUnit = this.systemModuleService.findUnitWithSort(this.rid);
        this.tsUnit.setModifyDate(new Date());
        this.tsUnit.setModifyManid(this.sessionData.getUser().getRid());

        this.selectSortList = new ArrayList<String>(0);

        Set<TsUnitAttr> attrSet = this.tsUnit.getTsUnitAttrs();
        if(null != attrSet && attrSet.size() > 0) {
            for(TsUnitAttr attr: attrSet) {
                this.selectSortList.add(attr.getTsBsSort().getRid().toString());
            }
        }

        this.editZoneId = this.tsUnit.getTsZone().getRid();
        this.editZoneName = this.tsUnit.getTsZone().getZoneName();
    }

	/**
	 * 保存
	 */
	public void saveAction() {
        TsUserInfo user = sessionData.getUser();

        //如果不是超管，不能创建平级单位
        if(this.editZoneId.equals(user.getTsUnit().getTsZone().getRid()) && null == this.tsUnit.getRid()) {
            if(!ifAdmin) {
                JsfUtil.addErrorMessage("不允许创建平级单位！");
                return;
            }
        }
        this.tsUnit.setTsZone(new TsZone(this.editZoneId));
        if(null != this.tsUnit.getRid()) {
            this.systemModuleService.saveOrUpdateUnit(this.tsUnit, this.selectSortList);
        }else {
            this.systemModuleService.saveOrUpdateUnit(this.tsUnit, this.selectSortList);
            this.searchAction();
        }
        this.backAction();
	}

	/**
	 * 删除
	 */
	public void deleteAction() {
		String msg = this.systemModuleService.deleteUnit(this.rid);
		if(StringUtils.isNotBlank(msg)) {
			JsfUtil.addErrorMessage(msg);
            return;
		}
        this.searchAction();
	}

    /**
     * 停用
     */
    public void stopAction() {
        this.systemModuleService.updateUnitState(this.rid, 0);
        this.searchAction();
    }

    /**
     * 启用
     */
    public void startAction() {
        this.systemModuleService.updateUnitState(this.rid, 1);
        this.searchAction();
    }




	@Override
	public String[] buildHqls() {
        StringBuilder countSB = new StringBuilder("select count(distinct t.rid) from TsUnit t left join t.tsUnitAttrs t2 where 1=1 ");
		StringBuilder sb = new StringBuilder("  select t from TsUnit t left join fetch t.tsUnitAttrs t2 where 1=1 ");
        sb.append(" and t.tsZone.zoneCode like '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%' ");
        countSB.append(" and t.tsZone.zoneCode like '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%' ");

        if(StringUtils.isNotBlank(this.searchUnitname)) {
            sb.append(" and t.unitname like :unitname ");
            countSB.append(" and t.unitname like :unitname ");
            this.paramMap.put("unitname", "%" + this.searchUnitname.trim() + "%");
        }
        if(null != this.searchSortId) {
            sb.append(" and t2.tsBsSort.rid =").append(this.searchSortId);
            countSB.append(" and t2.tsBsSort.rid =").append(this.searchSortId);
        }
        if(null != this.searchState) {
            sb.append(" and t.ifReveal =").append(this.searchState);
            countSB.append(" and t.ifReveal =").append(this.searchState);
        }
        sb.append(" order by t.tsZone.zoneCode, t.unitCode ");
		return new String[]{sb.toString(),countSB.toString()};
	}

    @Override
    public void processData(List<?> list) {
        if(null != list && list.size() > 0) {
            for(int i=0; i<list.size(); i++) {
                TsUnit t = (TsUnit)list.get(i);
                if(null != t.getTsUnitAttrs() && t.getTsUnitAttrs().size() > 0) {
                    StringBuilder sb = new StringBuilder();
                    for(TsUnitAttr tua : t.getTsUnitAttrs()) {
                        sb.append(",").append(tua.getTsBsSort().getSortName());
                    }
                    t.setUnitSortNames(sb.toString().substring(1));
                }
                if(i==0){
                    currentMaxZoneType=t.getTsZone().getZoneType();
                }
            }
        }
    }
    
    /** 转换经纬度 */
	public void convertLatOrLon() {
		// 纬度
		double a = this.minuteLat.divide(new BigDecimal(Double.valueOf(60)), 6, BigDecimal.ROUND_HALF_UP).doubleValue();
		double b = this.secondLat.divide(new BigDecimal(Double.valueOf(3600)), 6, BigDecimal.ROUND_HALF_UP).doubleValue();
		// 经度
		double c = this.minuteLong.divide(new BigDecimal(60), 6, BigDecimal.ROUND_HALF_UP).doubleValue();
		double d = this.secondLong.divide(new BigDecimal(3600), 6, BigDecimal.ROUND_HALF_UP).doubleValue();
		// 转换成百度坐标
		double yy = this.degreeLat.add(new BigDecimal(a)).add(new BigDecimal(b)).doubleValue();
		double xx = this.degreeLong.add(new BigDecimal(c)).add(new BigDecimal(d)).doubleValue();
		String value = BaiduMapUtil.findTransBaiduPoints(xx+"", yy+"");
		if (StringUtils.isBlank(value)){
			JsfUtil.addErrorMessage("坐标转换失败！");
			return ;
		}
		String lng = new BigDecimal(value.split("#@#")[0]).setScale(6, BigDecimal.ROUND_HALF_UP).toString();
		String lat = new BigDecimal(value.split("#@#")[1]).setScale(6, BigDecimal.ROUND_HALF_UP).toString();
		this.tsUnit.setLng(lng);
		this.tsUnit.setLat(lat);
		degreeLat = null;
		minuteLat = null;
		secondLat = null;
		degreeLong = null;
		minuteLong = null;
		secondLong = null;
		RequestContext.getCurrentInstance().execute("PF('GpsDialog').hide()");
	}

    //getters and setters


    public TsUnit getTsUnit() {
        return tsUnit;
    }

    public void setTsUnit(TsUnit tsUnit) {
        this.tsUnit = tsUnit;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
		return searchZoneCode;
	}

	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}

	public String getSearchUnitname() {
        return searchUnitname;
    }

    public void setSearchUnitname(String searchUnitname) {
        this.searchUnitname = searchUnitname;
    }

    public Short getSearchState() {
        return searchState;
    }

    public void setSearchState(Short searchState) {
        this.searchState = searchState;
    }

    public Map<String, Integer> getBsSortMap() {
        return bsSortMap;
    }

    public void setBsSortMap(Map<String, Integer> bsSortMap) {
        this.bsSortMap = bsSortMap;
    }

    public Integer getSearchSortId() {
        return searchSortId;
    }

    public void setSearchSortId(Integer searchSortId) {
        this.searchSortId = searchSortId;
    }

    public List<String> getSelectSortList() {
        return selectSortList;
    }

    public void setSelectSortList(List<String> selectSortList) {
        this.selectSortList = selectSortList;
    }

    public String getEditZoneName() {
        return editZoneName;
    }

    public void setEditZoneName(String editZoneName) {
        this.editZoneName = editZoneName;
    }

    public Integer getEditZoneId() {
        return editZoneId;
    }

    public void setEditZoneId(Integer editZoneId) {
        this.editZoneId = editZoneId;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public Short getCurrentMaxZoneType() {
        return currentMaxZoneType;
    }

	public BigDecimal getDegreeLat() {
		return degreeLat;
	}

	public void setDegreeLat(BigDecimal degreeLat) {
		this.degreeLat = degreeLat;
	}

	public BigDecimal getMinuteLat() {
		return minuteLat;
	}

	public void setMinuteLat(BigDecimal minuteLat) {
		this.minuteLat = minuteLat;
	}

	public BigDecimal getSecondLat() {
		return secondLat;
	}

	public void setSecondLat(BigDecimal secondLat) {
		this.secondLat = secondLat;
	}

	public BigDecimal getDegreeLong() {
		return degreeLong;
	}

	public void setDegreeLong(BigDecimal degreeLong) {
		this.degreeLong = degreeLong;
	}

	public BigDecimal getMinuteLong() {
		return minuteLong;
	}

	public void setMinuteLong(BigDecimal minuteLong) {
		this.minuteLong = minuteLong;
	}

	public BigDecimal getSecondLong() {
		return secondLong;
	}

	public void setSecondLong(BigDecimal secondLong) {
		this.secondLong = secondLong;
	}
}
