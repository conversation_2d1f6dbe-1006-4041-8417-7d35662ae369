package com.chis.modules.system.web;

import cn.hutool.json.JSONUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.TabTablePO;
import com.chis.modules.system.utils.DefaultLazyDataModel;
import org.primefaces.event.TabChangeEvent;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>类描述：多tab</p>
 *
 * @ClassAuthor hsj 2024-09-03 16:13
 */
public abstract class FacesTabEditBean {
    /**
     * 当前活动的标签，从0开始，默认为0,0:主界面1：添加修改界面2：查看界面
     */
    private int activeTab;
    /**
     * tabList
     */
    protected List<TabTablePO> tabList;

    protected String tabKey;


    /**
     * 查询条件参数，key为参数名称，value为值
     */
    protected Map<String, Map<String, Object>> paramMapMap = new HashMap<String, Map<String, Object>>();

    /**
     * 查询语句，0-查询记录的hql语句 1-查询记录数的hql语句
     */
    protected Map<String, String[]> hqlMap = new HashMap<String, String[]>();

    protected boolean ifSQL = Boolean.FALSE;

    public FacesTabEditBean() {

    }

    /**
     * <p>方法描述：查询单个</p>
     *
     * @MethodAuthor hsj 2024-09-04 14:05
     */
    @SuppressWarnings("unchecked")
    public void searchAction() {
        if (StringUtils.isBlank(this.tabKey)) {
            return;
        }
        this.searchByKeyAction(this.tabKey);
    }

    /**
     *  <p>方法描述：根据key查询</p>
     * @MethodAuthor hsj 2024-09-04 14:14
     */
    private void searchByKeyAction(String tabKey) {
        this.paramMapMap.put(tabKey, new HashMap<String, Object>());
        this.buildHqls(tabKey);
        String[] hql = hqlMap.get(tabKey);
        String TABLE_ID = "tabView:mainForm:tabMoreView:dataTable" + tabKey;
        DefaultLazyDataModel dataModel = new DefaultLazyDataModel(hql[0], hql[1], this.paramMapMap.get(tabKey), this.buildProcessData(), TABLE_ID, this.ifSQL);
        dealDataModel(dataModel, tabKey);
    }

    /**
     * <p>方法描述：赋值到tabList中</p>
     *
     * @MethodAuthor hsj 2024-09-04 14:05
     */
    private void dealDataModel(DefaultLazyDataModel dataModel, String tabKey) {
        for (TabTablePO tab : this.tabList) {
            if (!tab.getTabKey().equals(tabKey)) {
                continue;
            }
            tab.setDataModel(dataModel);
        }
    }


    /**
     * <p>方法描述：查询所有tab</p>
     *
     * @MethodAuthor hsj 2024-09-04 14:05
     */
    public void searchAllAction() {
        if (CollectionUtils.isEmpty(this.tabList)) {
            return;
        }
        for (TabTablePO tab : this.tabList) {
            this.searchByKeyAction(tab.getTabKey());
        }
    }

    /**
     * <p>方法描述：切换Tab--不刷新</p>
     *
     * @MethodAuthor hsj 2024-09-03 16:23
     */
    public void tabChange(TabChangeEvent event) {
        String tabId = event.getTab().getId();
        this.tabKey = StringUtils.string2list(tabId, "_").get(1);
    }

    /**
     * 添加初始化
     */
    public void addInitAction() {
        this.addInit();
        this.forwardEditPage();
    }

    /**
     * 修改初始化
     */
    public void modInitAction() {
        this.modInit();
        this.forwardEditPage();
    }

    /**
     * 查看初始化
     */
    public void viewInitAction() {
        this.viewInit();
        this.forwardViewPage();
    }

    /**
     * 转向修改界面
     */
    public void forwardEditPage() {
        this.activeTab = 1;
    }

    /**
     * 转向到查看界面
     */
    public void forwardViewPage() {
        this.activeTab = 2;
    }

    /**
     * 转向到其他界面
     */
    public void forwardOtherPage() {
        this.activeTab = 3;
    }

    /**
     * 返回首页面
     */
    public void backAction() {
        this.activeTab = 0;
    }

    /**
     * 跳转到其他查看页面
     */
    public void forwardOtherViewPage() {
        this.activeTab = 4;
    }

    /**
     * 跳转到其他查看页面
     */
    public void forwardEdit2Page() {
        this.activeTab = 5;
    }

    /**
     * 跳转到其他查看页面
     */
    public void forwardEdit3Page() {
        this.activeTab = 6;
    }

    /**
     * 跳转到其他查看页面
     */
    public void forwardEdit4Page() {
        this.activeTab = 7;
    }

    /**
     * 具体的添加初始化
     */
    public abstract void addInit();

    /**
     * 具体的修改初始化
     */
    public abstract void viewInit();

    /**
     * 具体的查看初始化
     */
    public abstract void modInit();

    /**
     * 具体的添加
     */
    public abstract void saveAction();

    /**
     * 组织hql查询语句，同时有参数的话，向paramMap里面放值
     *
     * @return 0-查询记录的hql语句 1-查询记录数的hql语句
     */
    public abstract void buildHqls(String tabKey);


    /**
     * 查询数据处理接口，如果需要处理，则托管bean必须要实现
     * IProcessData接口，并且返回this；否则返回NULL
     */
    public IProcessData buildProcessData() {
        if (this instanceof IProcessData) {
            return (IProcessData) this;
        } else {
            return null;
        }
    }
    public String getTabListJson() {
        if(CollectionUtils.isEmpty(this.tabList)){
            return "";
        }
        List<String> keyList = new ArrayList<>();
        for (TabTablePO tablePO : this.tabList) {
            keyList.add(tablePO.getTabKey());
        }
        return JSONUtil.toJsonStr(keyList);
    }
    //gets and sets
    public int getActiveTab() {
        return activeTab;
    }

    public void setActiveTab(int activeTab) {
        this.activeTab = activeTab;
    }


    public List<TabTablePO> getTabList() {
        return tabList;
    }

    public void setTabList(List<TabTablePO> tabList) {
        this.tabList = tabList;
    }

    public String getTabKey() {
        return tabKey;
    }

    public void setTabKey(String tabKey) {
        this.tabKey = tabKey;
    }


    public Map<String, Map<String, Object>> getParamMapMap() {
        return paramMapMap;
    }

    public void setParamMapMap(Map<String, Map<String, Object>> paramMapMap) {
        this.paramMapMap = paramMapMap;
    }

    public Map<String, String[]> getHqlMap() {
        return hqlMap;
    }

    public void setHqlMap(Map<String, String[]> hqlMap) {
        this.hqlMap = hqlMap;
    }

    public boolean isIfSQL() {
        return ifSQL;
    }

    public void setIfSQL(boolean ifSQL) {
        this.ifSQL = ifSQL;
    }
}
