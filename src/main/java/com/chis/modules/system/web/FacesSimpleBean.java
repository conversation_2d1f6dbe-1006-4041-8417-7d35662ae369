package com.chis.modules.system.web;

import java.util.HashMap;
import java.util.Map;

import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.utils.DefaultLazyDataModel;

/**
 * 不带转向维护模块的托管Bean
 * 
 * @editContent 修改buildProcessData()方法，如果查询的数据集需要修改，子类不需要再重写  david 2014-09-04 
 * 
 * <AUTHOR>
 * 修改人：lqq 修改时间：2014-09-12
 * 修改内容：新增sql语句查询
 */
public abstract class FacesSimpleBean extends FacesBean{

	/**真分页模型*/
	private DefaultLazyDataModel dataModel;
	/**查询条件参数，key为参数名称，value为值*/
	protected Map<String, Object> paramMap = new HashMap<String, Object>();
	/**查询语句，0-查询记录的hql语句 1-查询记录数的hql语句*/
	private String[] hql;
    /**表格的ID*/
    private static final String TABLE_ID = "mainForm:dataTable";
    
    protected boolean ifSQL = Boolean.FALSE;
	
	public FacesSimpleBean() {

	}
	
	/**
	 * 查询操作，如果不满足，可重写
	 */
	@SuppressWarnings("unchecked")
	public void searchAction() {
		this.paramMap.clear();
		this.hql = this.buildHqls();
		this.dataModel = new DefaultLazyDataModel(this.hql[0], this.hql[1], this.paramMap, this.buildProcessData(), TABLE_ID, this.ifSQL);
	}
	
	/**
	 * 组织hql查询语句，同时有参数的话，向paramMap里面放值
	 * @return 0-查询记录的hql语句 1-查询记录数的hql语句
	 */
	public abstract String[] buildHqls(); 
	
	/**
	 * 查询数据处理接口，如果需要处理，则托管bean必须要实现
	 * IProcessData接口，并且返回this；否则返回NULL
	 */
	public IProcessData buildProcessData() {
		if(this instanceof IProcessData) {
			return (IProcessData) this;
		}else {
			return null;
		}
	}

	public DefaultLazyDataModel getDataModel() {
		return dataModel;
	}

	public void setDataModel(DefaultLazyDataModel dataModel) {
		this.dataModel = dataModel;
	}
	
	
}
