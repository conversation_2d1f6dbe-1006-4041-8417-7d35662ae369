package com.chis.modules.system.web;

import java.util.List;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;
import org.primefaces.push.PushContext;
import org.primefaces.push.PushContextFactory;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.entity.TsPersonalSetting;
import com.chis.modules.portal.enumn.PersonSetType;
import com.chis.modules.portal.service.PortalBaseImpl;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.MessageType;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.MsgSendUtil;

/**
 * 登录的时候消息发送
 * 
 * <AUTHOR>
 */
@ManagedBean(name = "headBean")
@ViewScoped
public class HeadBean extends FacesBean {

	private static final long serialVersionUID = -7847564413855480498L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	/** ejb session bean */
	private SystemModuleServiceImpl systemService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commService = SpringContextHolder
			.getBean(CommServiceImpl.class);

	/** 查询我的门户列表 */
	private PortalBaseImpl service = (PortalBaseImpl) SpringContextHolder
			.getBean(PortalBaseImpl.class);
	/** 当前登录人的门户列表ID_NAME */
	private String portals;

	private String messNum;// 未阅消息格式
	private String warnMessNum; // 未阅的警告消息

	private String systemModules;
	// 是否显示多门户
	private String ifShowPortals;
	private Integer ifUserQickDesk;
	private List<Object[]> qickDeskList;

	/** 门户个人设置实体 */
	private TsPersonalSetting tsPersonalSetting;

	public HeadBean() {
		this.showMsgAction();
		this.showPortal();
		this.initFirstPage();
		this.qickDeskInit();

	}

	public void qickDeskInit() {
		TsUserInfo user = this.service.findTsUserInfo(this.sessionData
				.getUser().getRid());
		if (null != user.getDispKjmenu()) {
			ifUserQickDesk = user.getDispKjmenu();
			if (ifUserQickDesk == 1) {// 显示快捷菜单
				qickDeskList = this.service.findQickDeskList(user.getRid());
			}
		}
	}

	/**
	 * 发给自己最新的未读消息
	 */
	public void sendMsgAction() {
		MsgSendUtil.sendNewMsg(this.sessionData.getUser().getRid().toString());
	}

	/**
	 * 页面右上角显示消息图标
	 */
	public void showMsgAction() {
		TsUserInfo tsUserInfo = this.sessionData.getUser();
		this.messNum = this.systemService.countMsgNum(tsUserInfo.getRid());
	}

	/**
	 * 首页展示
	 */
	private void initFirstPage() {
		tsPersonalSetting = service.findPersonSetting(sessionData.getUser()
				.getRid());
		if (null == tsPersonalSetting) {
			tsPersonalSetting = new TsPersonalSetting();
			tsPersonalSetting
					.setDisplayName(PersonSetType.MENUDESK.getTypeCN());
			tsPersonalSetting.setDefaultUrl("content_menu.faces");
			sessionData.setSkinName(Constants.DEFALUTSKINNAME);
		} else {
			if (null != tsPersonalSetting.getDefaultUrlType()
					&& null != tsPersonalSetting.getDefaultUrl()) {
				tsPersonalSetting.setDisplayName(tsPersonalSetting
						.getDefaultUrlType().getTypeCN());
				sessionData.setSkinName(tsPersonalSetting.getDefaultSkin());
				// 如果为门户，则名称需要根据ID获取
				if (tsPersonalSetting.getDefaultUrlType().getTypeNo() == 0) {
					// 门户类型ID
					String portID = tsPersonalSetting.getDefaultUrl().split(
							"pid=")[1];
					if (StringUtils.isNotBlank(portals)) {
						String[] split = portals.split(";");
						for (String s : split) {
							if (StringUtils.isNotBlank(s)) {
								String[] split1 = s.split(",");
								if (split1[1].equals(portID)) {
									tsPersonalSetting.setDisplayName(split1[0]);
									break;
								}
							}
						}
					}
				}
			}
		}

		// 获取当前系统显示的模块
		this.systemModules = commService.findParamValue("SYSTEM_MODULES");
	}
	
	/**
	 * 定时刷新页面信息
	 */
	public void timeUpdateMsg(){
		this.sendMsgAction();
		this.showMsgAction();
	}

	/**
	 * 加载当前登录人拥有的portal
	 */
	public void showPortal() {
		this.ifShowPortals = commService.findParamValue("PORTAL_MANY_SELECT");
		this.portals = this.service.findMyPortal(this.sessionData.getUser()
				.getRid());
	}

	/**
	 * 显示预警信息
	 */
	public void showWarnAction() {
		TsUserInfo tsUserInfo = this.sessionData.getUser();
		this.warnMessNum = this.systemService.countMsgNumOfType(
				tsUserInfo.getRid(), MessageType.WARN);
	}

	public void openWarnMsgAction() {
		TsUserInfo tsUserInfo = this.sessionData.getUser();
		this.systemService.updateMsgNumOfType(tsUserInfo.getRid(),
				MessageType.WARN);

		StringBuilder sb = new StringBuilder(
				"/webapp/que/questionWarnList.faces");
		RequestContext currentInstance = RequestContext.getCurrentInstance();
		currentInstance.execute("top.ShortcutMenuClick(\"01\",\"采样预警\",\""
				+ sb.toString() + "\",\"\");");
	}

	public String getMessNum() {
		return messNum;
	}

	public void setMessNum(String messNum) {
		this.messNum = messNum;
	}

	public String getPortals() {
		return portals;
	}

	public void setPortals(String portals) {
		this.portals = portals;
	}

	public TsPersonalSetting getTsPersonalSetting() {
		return tsPersonalSetting;
	}

	public void setTsPersonalSetting(TsPersonalSetting tsPersonalSetting) {
		this.tsPersonalSetting = tsPersonalSetting;
	}

	public String getWarnMessNum() {
		return warnMessNum;
	}

	public void setWarnMessNum(String warnMessNum) {
		this.warnMessNum = warnMessNum;
	}

	public String getSystemModules() {
		return systemModules;
	}

	public void setSystemModules(String systemModules) {
		this.systemModules = systemModules;
	}

	public String getIfShowPortals() {
		return ifShowPortals;
	}

	public void setIfShowPortals(String ifShowPortals) {
		this.ifShowPortals = ifShowPortals;
	}

	public Integer getIfUserQickDesk() {
		return ifUserQickDesk;
	}

	public void setIfUserQickDesk(Integer ifUserQickDesk) {
		this.ifUserQickDesk = ifUserQickDesk;
	}

	public List<Object[]> getQickDeskList() {
		return qickDeskList;
	}

	public void setQickDeskList(List<Object[]> qickDeskList) {
		this.qickDeskList = qickDeskList;
	}

}
