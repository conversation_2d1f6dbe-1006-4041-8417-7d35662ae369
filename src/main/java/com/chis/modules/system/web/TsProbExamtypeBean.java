package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsMenu;
import com.chis.modules.system.entity.TsProbExamtype;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;
@ManagedBean
@ViewScoped
public class TsProbExamtypeBean extends FacesEditBean implements IProcessData {
	/**存在session中的对象*/
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/**ejb session bean*/
	private SystemModuleServiceImpl systemService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);

	/**问卷题目类型实体*/
	private TsProbExamtype tsProbExamtype;
	/**rid*/
	private Integer rid;
    /**查询条件：类型名称*/
    private String searchTypeName;
    /**区分添加的是根节点还是子节点*/
    private Integer x;
    /**记录父级编码*/
    private String oldLevelNp;
    /**系统类型*/
    private String tag;
    private String oldTypeCode;
    
    public TsProbExamtypeBean(){
    	this.tsProbExamtype=new TsProbExamtype();
    	this.searchAction();
    	tag = JsfUtil.getRequestParameter("tag");
    }
	
	//添加根节点初始化
	@Override
	public void addInit() {
		this.tsProbExamtype=new TsProbExamtype();
		this.oldLevelNp="";
		this.oldTypeCode="";
		
	}
	//添加子节点初始化
	public void add(){
		this.tsProbExamtype=new TsProbExamtype();
		TsProbExamtype selectTsProbExamtype = systemService.selectTsProbExamtype(rid);
		this.oldLevelNp=selectTsProbExamtype.getLevelNp();
		this.oldTypeCode="";
	}

	@Override
	public void viewInit() {
		
	}
    //修改初始化
	@Override
	public void modInit() {
	this.tsProbExamtype=systemService.selectTsProbExamtype(rid);
	this.oldTypeCode=tsProbExamtype.getTypeCode();
	this.oldLevelNp="";
	if(StringUtils.contains(tsProbExamtype.getLevelNp(), ".")){
	String levelNp = tsProbExamtype.getLevelNp();
	this.oldLevelNp= levelNp.substring(0,levelNp.lastIndexOf("."));
	}
	}

	@Override
	public void saveAction() {
		try {
		if(StringUtils.isBlank(tsProbExamtype.getTypeCode())){
			JsfUtil.addErrorMessage("编码不能为空");
			return;
		}
		if(StringUtils.isBlank(tsProbExamtype.getTypeName())){
			JsfUtil.addErrorMessage("类型名称不能为空");
			return;
		}
		// 验证编码唯一
		String test = systemService.BequestIdc(tsProbExamtype);
		if (test != null) {
			JsfUtil.addErrorMessage(test);
			return;
		}
		
		//如果根节点下有子节点 则它的编码不能修改
		List<TsProbExamtype> confirmation = systemService.confirmation(tsProbExamtype);
		if(confirmation!=null && confirmation.size()>0 && !tsProbExamtype.getTypeCode().equals(oldTypeCode)){
			JsfUtil.addErrorMessage("该编码已被引用 不能修改!");
			return;
		}
		
	if(StringUtils.isNotBlank(oldLevelNp)){
			StringBuilder sb = new StringBuilder();
			StringBuilder append = sb.append(this.oldLevelNp+"."+tsProbExamtype.getTypeCode());
			tsProbExamtype.setLevelNp(append.toString());
	}else{
		tsProbExamtype.setLevelNp(tsProbExamtype.getTypeCode());
	}
		tsProbExamtype.setCreateDate(new Date());
		tsProbExamtype.setCreateManid(this.sessionData.getUser().getRid());
		tsProbExamtype.setParamType(Integer.valueOf(tag));
		systemService.saveOrUpdateProb(tsProbExamtype);
		JsfUtil.addSuccessMessage("保存成功");
		RequestContext.getCurrentInstance().execute(
				"PF('FactoryDailogs').hide()");
		this.searchAction();
		} catch (Exception e) {
		e.printStackTrace();
		JsfUtil.addErrorMessage("保存失败");
		}
	}

	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder(" from TsProbExamtype t where 1=1 ");
        if(StringUtils.isNotBlank(this.searchTypeName)) {
            sb.append(" and t.typeName like :searchTypeName");
            this.paramMap.put("searchTypeName", "%"+this.searchTypeName+"%");
        } 
		String h2 = "select count(*) " + sb.toString();
		String h1 = "select t " + sb.append(" order by t.levelNp ");
		return new String[]{h1,h2};
	}

	//如果根节点下有子节点不能删除
	public void delete() {
		List<TsProbExamtype> deleteTs = systemService.delete(tsProbExamtype);
		if (deleteTs != null&&deleteTs.size()>0) {
			JsfUtil.addErrorMessage("该数据已被引用");
			return;
		}else{
		JsfUtil.addSuccessMessage("删除成功");
		this.searchAction();
		}
	}	
	
    /**
     * 处理数据
     * @param list 查询出来的结果
     */
    @Override
    public void processData(List<?> list) {
        List<TsProbExamtype> list1=(ArrayList<TsProbExamtype>) list;
        for(TsProbExamtype h:list1){
            if(null!=h.getLevelNp()){
                h.setLevelNum(h.getLevelNp().split("[.]").length);
            }else{
                h.setLevelNum(1);
            }
        }
    }

	public TsProbExamtype getTsProbExamtype() {
		return tsProbExamtype;
	}

	public void setTsProbExamtype(TsProbExamtype tsProbExamtype) {
		this.tsProbExamtype = tsProbExamtype;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public String getSearchTypeName() {
		return searchTypeName;
	}

	public void setSearchTypeName(String searchTypeName) {
		this.searchTypeName = searchTypeName;
	}

	public Integer getX() {
		return x;
	}

	public void setX(Integer x) {
		this.x = x;
	}

	public String getOldLevelNp() {
		return oldLevelNp;
	}

	public void setOldLevelNp(String oldLevelNp) {
		this.oldLevelNp = oldLevelNp;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}


}
