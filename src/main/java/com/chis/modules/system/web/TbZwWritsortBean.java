package com.chis.modules.system.web;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TbZwWritsort;
import com.chis.modules.system.entity.TsRpt;
import com.chis.modules.system.entity.TsSystemParam;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.SystemParamType;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;

/**
 * 
 * <p>
 * 类描述：文书类型维护模块
 * </p>
 * 
 * @ClassAuthor rcj,2018年4月23日,tbZwWritsortBean
 */
@ManagedBean(name = "tbZwWritsortBean")
@ViewScoped
public class TbZwWritsortBean extends FacesEditBean {

	private static final long serialVersionUID = 1L;
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commService = (CommServiceImpl)SpringContextHolder.getBean(CommServiceImpl.class);
	
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	/** 查询条件--文书编号 */
	private String searchWritCode;
	/** 查询条件--文书名称 */
	private String searchWritName;
	/** 文书查询对象 */
	private TbZwWritsort tbZwWritsort;
	/** 列表中对象id */
	private Integer rid;
	// 报表模板搜索
	/** 查询条件--报表模板名称 */
	private String searchRptName;
	/** 查询对象 */
	private TsRpt searchRpt;
	private Map<String, String> systemTypeMap = new LinkedHashMap<String, String>();
	/** 分页模型-- 报表模板搜索 */
	private List<TsRpt> searchRptList;
	private Integer paramType;

	public TbZwWritsortBean() {
		this.ifSQL = true;
		initSearchAction();
		initSystemType();
		searchAction();
	}

	/**
	 * <p>
	 * 方法描述：文书初始化
	 * </p>
	 * 
	 * @MethodAuthor rcj,2018年4月23日,initSearchAction
	 */
	public void initSearchAction() {
		this.tbZwWritsort = new TbZwWritsort();

	}

	@Override
	public void addInit() {
		this.rid = null;
		initWritInfo();
	}

	/**
	 * <p>
	 * 方法描述：初始化文书基本信息
	 * </p>
	 * 
	 * @MethodAuthor rcj,2018年4月23日,initWritInfo
	 */
	private void initWritInfo() {
		this.tbZwWritsort = new TbZwWritsort();
		this.searchRpt = new TsRpt();
		this.tbZwWritsort.setFkByRptTemplId(new TsRpt());
	}

	/**
	 * <p>
	 * 方法描述：系统类型初始化
	 * </p>
	 * 
	 * @MethodAuthor rcj,2018/04/23,initSystemType
	 */

	private void initSystemType() {
		/**
		 * 初始化系统类型
		 */
		this.systemTypeMap.put("--请选择--", null);
		//查询报表中存在的系统类型
		List<Integer> typeList = this.commService.findAllParamTypeByTsRpt();

		SystemType[] types = SystemType.values();
		for (SystemType st : types) {
			Integer typeNo = Integer.parseInt(st.getTypeNo().toString());
			if (!typeList.contains(typeNo)) {
				continue;
			}
			this.systemTypeMap.put(st.getTypeCN(), st.getTypeNo()
					.toString());
		}
	}

	/**
	 * <p>
	 * 方法描述：报表搜索参数初始化
	 * </p>
	 * 
	 * @MethodAuthor rcj,2018/04/23,initRptDialog
	 */
	public void initRptDialog() {
//		tbZwWritsort.setFkByRptTemplId(new TsRpt());
		if(null==searchRpt){
			searchRpt = new TsRpt();
		}
		searchRptAction();
	}

	public void clearRptTemp() {
		this.tbZwWritsort.setFkByRptTemplId(new TsRpt());
	}

	/**
	 * <p>
	 * 方法描述：确认模板
	 * </p>
	 * 
	 * @MethodAuthor rcj,2018年4月24日,checkTemp
	 */
	public void checkTemp() {
		if (searchRpt == null || searchRpt.getRid() == null) {
			JsfUtil.addErrorMessage("请选择模板！");
			return;
		}

		tbZwWritsort.setFkByRptTemplId(searchRpt);
	}

	/**
	 * <p>
	 * 方法描述：报表模板搜索
	 * </p>
	 * 
	 * @MethodAuthor rcj,2018/04/23,searchRptAction
	 */
	public void searchRptAction() {
		searchRptList = systemModuleService.findRptTemp(searchRptName,
				paramType);

	}

	@Override
	public void viewInit() {

	}

	@Override
	public void modInit() {
		if (null == rid) {
			return;
		}
		this.tbZwWritsort = systemModuleService.find(TbZwWritsort.class,rid);
		if (null == tbZwWritsort.getFkByRptTemplId()) {
			tbZwWritsort.setFkByRptTemplId(new TsRpt());
		}
	}

	@Override
	public void saveAction() {
		if (StringUtils.isBlank(tbZwWritsort.getWritCode())) {
			JsfUtil.addErrorMessage("文书编号不能为空！");
			return;
		}
		if (StringUtils.isBlank(tbZwWritsort.getWritName())) {
			JsfUtil.addErrorMessage("文书名称不能为空！");
			return;
		}
		//添加修改时验证文书编号唯一性
		
		if (StringUtils.isNotBlank(tbZwWritsort.getWritCode())) {
			TbZwWritsort writsort = systemModuleService.findTbZwWritsortByCode(
					tbZwWritsort.getWritCode(), this.rid);
			if (null != writsort) {
				JsfUtil.addErrorMessage("该文书编号已存在！");
				return;
			}
		}
		

		if (null == tbZwWritsort.getFkByRptTemplId().getRid()) {
			tbZwWritsort.setFkByRptTemplId(null);
		}
		TsUserInfo user = sessionData.getUser();
		systemModuleService
				.saveOrUpdateTbZwWritsort(tbZwWritsort, user);
		if (null == tbZwWritsort.getFkByRptTemplId()) {
			tbZwWritsort.setFkByRptTemplId(new TsRpt());
		}
		JsfUtil.addSuccessMessage("保存成功！");
		this.searchAction();
	}

	@Override
	public String[] buildHqls() {
		StringBuilder sql = new StringBuilder(
				" SELECT T.RID,T.WRIT_CODE,T.WRIT_NAME,T.WRIT_SHORTNAME,T1.RPTCOD  ");
		sql.append(" FROM TB_ZW_WRITSORT T LEFT JOIN TS_RPT T1 ON T.RPT_TEMPL_ID =T1.RID");
		sql.append(" WHERE 1=1 ");

		// 查询条件--文书编号
		if (StringUtils.isNotBlank(searchWritCode)) {
			sql.append(" AND T.WRIT_CODE LIKE :writCode ESCAPE '\\\' ");
			this.paramMap.put("writCode",
					"%" + StringUtils.convertBFH(this.searchWritCode) + "%");
		}
		// 查询条件--文书名称
		if (StringUtils.isNoneBlank(searchWritName)) {
			sql.append(" AND T.WRIT_NAME LIKE :writName ESCAPE '\\\' ");
			this.paramMap.put("writName",
					"%" + StringUtils.convertBFH(this.searchWritName) + "%");

		}

		StringBuilder countSql = new StringBuilder();
		countSql.append(" SELECT COUNT(1) FROM (").append(sql).append(")");
		sql.append(" ORDER BY  T.WRIT_CODE");
		return new String[] { sql.toString(), countSql.toString() };
	}

	/**
	 * <p>
	 * 方法描述：删除文书信息
	 * </p>
	 * 
	 * @MethodAuthor rcj,2018年4月24日,deleteAction
	 */
	public void deleteAction() {
		String delWritsort = systemModuleService.deleteTbZwWritsort(rid);
		if(null == delWritsort){
			JsfUtil.addSuccessMessage("删除成功！");
			this.searchAction();
		}else{
			JsfUtil.addErrorMessage(delWritsort);
		}
	}
	
	
	
	
	

	public TbZwWritsort getTbZwWritsort() {
		return tbZwWritsort;
	}

	public void setTbZwWritsort(TbZwWritsort tbZwWritsort) {
		this.tbZwWritsort = tbZwWritsort;
	}

	public TsRpt getSearchRpt() {
		return searchRpt;
	}

	public void setSearchRpt(TsRpt searchRpt) {
		this.searchRpt = searchRpt;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public Map<String, String> getSystemTypeMap() {
		return systemTypeMap;
	}

	public void setSystemTypeMap(Map<String, String> systemTypeMap) {
		this.systemTypeMap = systemTypeMap;
	}

	public List<TsRpt> getSearchRptList() {
		return searchRptList;
	}

	public void setSearchRptList(List<TsRpt> searchRptList) {
		this.searchRptList = searchRptList;
	}

	public SystemModuleServiceImpl getSystemModuleService() {
		return systemModuleService;
	}

	public void setSystemModuleService(
			SystemModuleServiceImpl systemModuleService) {
		this.systemModuleService = systemModuleService;
	}

	public Integer getParamType() {
		return paramType;
	}

	public void setParamType(Integer paramType) {
		this.paramType = paramType;
	}

	
	public SessionData getSessionData() {
		return sessionData;
	}

	public void setSessionData(SessionData sessionData) {
		this.sessionData = sessionData;
	}

	public String getSearchWritCode() {
		return searchWritCode;
	}

	public void setSearchWritCode(String searchWritCode) {
		this.searchWritCode = searchWritCode;
	}

	public String getSearchWritName() {
		return searchWritName;
	}

	public void setSearchWritName(String searchWritName) {
		this.searchWritName = searchWritName;
	}

	public String getSearchRptName() {
		return searchRptName;
	}

	public void setSearchRptName(String searchRptName) {
		this.searchRptName = searchRptName;
	}

}
