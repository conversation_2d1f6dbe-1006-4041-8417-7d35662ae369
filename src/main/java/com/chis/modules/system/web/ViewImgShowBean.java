package com.chis.modules.system.web;

import com.alibaba.fastjson.JSONArray;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.logic.ImgBean;
import com.chis.modules.system.web.FacesBean;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Created by Administrator on 2015-6-16.
 */
@ManagedBean(name = "viewImgShowBean")
@ViewScoped
public class ViewImgShowBean extends FacesBean {
    private static final long serialVersionUID = -8359180733176431947L;

    private List<ImgBean> relist = new ArrayList<ImgBean>(0);


    public ViewImgShowBean() {
        String imgs = JsfUtil.getRequest().getParameter("param");
        try {
			imgs=URLDecoder.decode(imgs,"UTF-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
        relist = JSONArray.parseArray(imgs, ImgBean.class);
    }

    public List<ImgBean> getRelist() {
        return relist;
    }

    public void setRelist(List<ImgBean> relist) {
        this.relist = relist;
    }
}


