package com.chis.modules.system.web;

import java.io.IOException;
import java.io.Serializable;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.faces.bean.ApplicationScoped;
import javax.faces.bean.ManagedBean;
import javax.faces.context.FacesContext;
import javax.servlet.http.HttpSession;

import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.enumn.LogNoEnum;
import com.chis.modules.system.enumn.SystemParamType;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.LogUtil;

/**
 * application范围的bean
 * 
 * <AUTHOR>
 */
@ManagedBean(name = "applicationBean")
@ApplicationScoped
public class ApplicationBean implements Serializable {

	private static final long serialVersionUID = -3287948569674069288L;
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder
			.getBean(CommServiceImpl.class);
	/** 应用程序标题 */
	private String appTitle;
	/** 当前系统 */
	private String systemType;
	/** 同一用户是否允许登录多次，默认允许 */
	private String ifOneLoginMany = "1";
	
	private static final String SEARCH_PARAM = "'"
			+ SystemParamType.APP_TITLE.getTypeNo() + "','"
			+ SystemParamType.SYSTEM_TYPE.getTypeNo() + "'";

	@PostConstruct
	public void init() {
		Map<String, String> map = this.commService
				.findParamValues(SEARCH_PARAM);
		this.appTitle = map.get(SystemParamType.APP_TITLE.getTypeNo());
		this.systemType = map.get(SystemParamType.SYSTEM_TYPE.getTypeNo());
		this.ifOneLoginMany = map.get(SystemParamType.SYS_ONE_LOGIN_MANY.getTypeNo());
	}

	/**
	 * 重新登录，清除session
	 */
	public void logOut() {
		try {
            LogUtil.logIntoDB(LogNoEnum.NO_1002, LogNoEnum.NO_1002.getTypeCN(), LogNoEnum.NO_1002.getTypeCN());
			SessionData session = (SessionData) FacesContext
					.getCurrentInstance().getExternalContext().getSessionMap()
					.get(SessionData.SESSION_DATA);
			if (session.getIsZZpx().equals(1)) {
				FacesContext.getCurrentInstance().getExternalContext()
						.getSessionMap().remove(SessionData.SESSION_DATA);
				FacesContext.getCurrentInstance().getExternalContext()
						.redirect("/loginPsn.faces");

			} else {
				FacesContext.getCurrentInstance().getExternalContext()
						.getSessionMap().remove(SessionData.SESSION_DATA);
				FacesContext.getCurrentInstance().getExternalContext()
						.redirect("/login.faces");
			}
			HttpSession curSession = (HttpSession)FacesContext
					.getCurrentInstance().getExternalContext().getSession(false);
			if (null != curSession) {
				curSession.invalidate();
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public String getAppTitle() {
		return appTitle;
	}

	public void setAppTitle(String appTitle) {
		this.appTitle = appTitle;
	}

	public String getSystemType() {
		return systemType;
	}

	public void setSystemType(String systemType) {
		this.systemType = systemType;
	}

	public String getIfOneLoginMany() {
		return ifOneLoginMany;
	}

	public void setIfOneLoginMany(String ifOneLoginMany) {
		this.ifOneLoginMany = ifOneLoginMany;
	}

	
}
