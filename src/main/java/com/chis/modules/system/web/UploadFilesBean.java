package com.chis.modules.system.web;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.UploadedFile;

import com.chis.common.utils.FileUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.logic.FilePo;
import com.chis.modules.system.utils.GeneratePdf;
import com.google.common.collect.Lists;

/**
 * 上传多个附件，界面大小750,580 <br/>
 * 返回 <br/>
 * 
 * 可以修改初始化， 1*.上传到的路径名 filePath<br/>
 * 2. 已上传的文件集合json字符串 dataJson <br/>
 * 3. 上传文件的格式，如：jpg、png、gif、jpeg <br/>
 * 4. 单个文件的大小，单位M <br/>
 * 5. 如果是PDF文件，是否需要拆分 split<br/>
 * 6. 拆分的话，单个大小是多少 pdfSigleSize<br/>
 * 7. 该页面的标题 title<br/>
 * 8. 是否需要必传ifNeed<br/>
 * 
 * <AUTHOR>
 * @createTime 2016年7月21日
 */
@ManagedBean(name = "uploadFilesBean")
@ViewScoped
public class UploadFilesBean extends FacesBean {

	private static final long serialVersionUID = -1100693111644511194L;

	/** 上传到的路径名，如"/temp" */
	private String filePath;
	/** 文件类型，如：jpg、png、gif、jpeg */
	private String fileTypes = "";
	/** 允许类型的正则表达式，如：'/(\.|\/)(gif|jpe?g|png)$/' */
	private String allowTypes = "/(\\.|\\/)/";
	/** 单个文件最大限制，单位：M */
	private Integer fileSize = 500;
	/** 单个文件最大限制，单位：字节, limitSize */
	private Long allowSize = 500000000L;
	/** 兆跟字节之间的转换汇率 */
	private static final long M_TO_B = 1000000;
	private List<FilePo> dataList = Lists.newLinkedList();
	private FilePo filePo;
	/** PDF文件是否需要拆分，0-不需要，1-需要 */
	private String split = "0";
	/** PDF拆分文件的话，单个大小，默认10M */
	private BigDecimal pdfSigleSize = BigDecimal.valueOf(10000000);
	/** 该界面标题 */
	private String title;
	/** 是否必传*/
	private boolean ifNeed;

	public UploadFilesBean() {
		this.initData();
	}

	private void initData() {
		this.filePath = JsfUtil.getRequest().getParameter("filePath");
		this.dataList = (List<FilePo>) JsfUtil.getSession().getAttribute(
				"fileList");
		JsfUtil.getSession().removeAttribute("fileList");
		if (null == this.dataList) {
			this.dataList = Lists.newLinkedList();
		}

		this.title = JsfUtil.getRequest().getParameter("title");
		if (StringUtils.isBlank(this.title)) {
			this.title = "文件扫描";
		}

		this.fileTypes = JsfUtil.getRequest().getParameter("fileTypes");
		if (StringUtils.isNotBlank(this.fileTypes)) {
			this.allowTypes = "/(\\.|\\/)("
					+ this.fileTypes.replaceAll("、", "|") + ")$/";
		}

		String limitSize = JsfUtil.getRequest().getParameter("limitSize");
		if (StringUtils.isNotBlank(limitSize)) {
			this.fileSize = Integer.valueOf(limitSize);
			this.allowSize = fileSize * M_TO_B;
		}

		this.split = JsfUtil.getRequest().getParameter("split");
		if (StringUtils.isBlank(this.split)) {
			this.split = "1";
		}

		String pdfSigleSizeStr = JsfUtil.getRequest().getParameter(
				"pdfSigleSize");
		if (StringUtils.isNoneBlank(pdfSigleSizeStr)) {
			pdfSigleSize = new BigDecimal(pdfSigleSizeStr);
		}
		
		String need=JsfUtil.getRequest().getParameter("ifNeed");
		if("false".equals(need)){
			ifNeed=false;
		}else{
			ifNeed=true;
		}
	}

	public void handleFileUpload(FileUploadEvent event) {
		UploadedFile file = event.getFile();
		try {
			String fileName = file.getFileName();// 文件名称
			String uuid = UUID.randomUUID().toString().replaceAll("-", "");
			String path = JsfUtil.getAbsolutePath();
			String relativePath = new StringBuilder(this.filePath).append("/")
					.append(uuid)
					.append(fileName.substring(fileName.lastIndexOf(".")))
					.toString();
			// 文件路径
			String filePath = new StringBuilder(path).append(relativePath)
					.toString();
			FileUtils.copyFile(filePath, file.getInputstream());

			if (filePath.toUpperCase().endsWith(".PDF") && "1".equals(split)) {
				List<String> splitList = GeneratePdf.splitPdf(filePath,
						pdfSigleSize);
				if (null != splitList && splitList.size() > 0) {
					for (int i = 0; i < splitList.size(); i++) {
						FilePo fp = new FilePo();
						fp.setFileName(fileName.substring(0,
								fileName.lastIndexOf("."))
								+ "-"
								+ StringUtils.leftPad(i + 1 + "", 3, "0")
								+ fileName.substring(fileName.lastIndexOf(".")));
						fp.setFilePath(splitList.get(i).replaceFirst(path, ""));
						fp.setXh(this.dataList.size() + 1);
						this.dataList.add(fp);
					}
				}
			}
		} catch (Exception e) {
			FacesMessage mess = new FacesMessage("上传失败", file.getFileName()
					+ "上传失败！");
			FacesContext.getCurrentInstance().addMessage("上传失败", mess);
			e.printStackTrace();
		}
	}

	public void deleteAction() {
		this.dataList.remove(filePo);
		this.reorderList();
		JsfUtil.addSuccessMessage("删除成功！");
	}

	private void reorderList() {
		for (int i = 0; i < this.dataList.size(); i++) {
			FilePo f = this.dataList.get(i);
			f.setXh(i + 1);
		}
	}

	/**
	 * 选择确定方法
	 */
	public void confirmAction() {
		Map<String, Object> map = new HashMap<String, Object>();
		if(this.ifNeed){
			if (null != this.dataList && this.dataList.size() > 0) {
				map.put("dataList", this.dataList);
				RequestContext.getCurrentInstance().closeDialog(map);
			} else {
				JsfUtil.addErrorMessage("请上传文件！");
			}
		}else{
			map.put("dataList", this.dataList);
			RequestContext.getCurrentInstance().closeDialog(map);
		}
	}

	/**
	 * 关闭
	 */
	public void dialogClose() {
		System.err.println("【弹出框关闭】：");
		RequestContext.getCurrentInstance().closeDialog(null);
	}

	public void initOrderAction() {
		if (this.dataList == null || this.dataList.size() == 0) {
			JsfUtil.addErrorMessage("没有需要排序的文件");
			return;
		}
		RequestContext.getCurrentInstance()
				.execute("PF('OrderDialog').show();");
	}

	public void orderSaveAction() {
		reorderList();
	}

	public List<FilePo> getDataList() {
		return dataList;
	}

	public void setDataList(List<FilePo> dataList) {
		this.dataList = dataList;
	}

	public String getFileTypes() {
		return fileTypes;
	}

	public void setFileTypes(String fileTypes) {
		this.fileTypes = fileTypes;
	}

	public String getAllowTypes() {
		return allowTypes;
	}

	public void setAllowTypes(String allowTypes) {
		this.allowTypes = allowTypes;
	}

	public Integer getFileSize() {
		return fileSize;
	}

	public void setFileSize(Integer fileSize) {
		this.fileSize = fileSize;
	}

	public Long getAllowSize() {
		return allowSize;
	}

	public void setAllowSize(Long allowSize) {
		this.allowSize = allowSize;
	}

	public FilePo getFilePo() {
		return filePo;
	}

	public void setFilePo(FilePo filePo) {
		this.filePo = filePo;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

}
