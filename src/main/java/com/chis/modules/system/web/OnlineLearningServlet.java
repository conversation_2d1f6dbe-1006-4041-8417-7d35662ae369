package com.chis.modules.system.web;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.utils.OnlineLearningUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 第三方-在线学习课程接口
 *
 * <AUTHOR>
 * @version 1.0
 */
@WebServlet(name = "OnlineLearningServlet", value = "/onlineLearningServer")
public class OnlineLearningServlet extends HttpServlet {
    private static final Logger logger = LoggerFactory.getLogger(OnlineLearningServlet.class);

    @Override
    public void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        this.doPost(req, resp);
    }

    @Override
    public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        String type = request.getParameter("type");
        Map<String, String> returnMap = new HashMap<>();
        if (StringUtils.isBlank(type)) {
            //错误信息如果中文 会乱码
            returnMap.put("type", "02");
            returnMap.put("mess", "type不能为空！");
            String msg = "type param is required!";
            response.sendRedirect("/error.faces?msg=" + dealString(returnMap.get("mess")));
            return;
        }
        if (!"1".equals(type) && !"2".equals(type)) {
            returnMap.put("type", "02");
            returnMap.put("mess", "type传入错误！");
            response.sendRedirect("/error.faces?msg=" + dealString(returnMap.get("mess")));
            return;
        }
        TsUserInfo user = null;
        try {
            user = ((SessionData) request.getSession().getAttribute(SessionData.SESSION_DATA)).getUser();
        } catch (Exception e) {
            logger.error(e.getMessage(), new Throwable(e));
        }
        if (user == null || user.getRid() == null) {
            returnMap.put("type", "01");
            returnMap.put("mess", "权限验证失败！");
            String msg = "permission validation failed!";
            response.sendRedirect("/error.faces?msg=" + msg);
            return;
        }
        if ("1".equals(type)) {
            returnMap = OnlineLearningUtil.getLoginToToken(user);
        }
        if ("2".equals(type)) {
            returnMap = OnlineLearningUtil.userLoginAction(user);
        }
        if ("00".equals(returnMap.get("type"))) {
            response.sendRedirect(returnMap.get("data"));
        } else {
            response.sendRedirect("/error.faces?msg=" + dealString(returnMap.get("mess")));
        }
    }

    private String dealString(String str) {
        return new String(StringUtils.objectToString(str).getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
    }
}
