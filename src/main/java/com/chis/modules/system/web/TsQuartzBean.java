package com.chis.modules.system.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TsQuartz;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.QuartzManager;
import org.primefaces.context.RequestContext;
import org.quartz.Job;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 定时任务
 *
 * <AUTHOR>
 * @createDate 2015年1月22日下午1:22:10
 */
@ManagedBean(name = "tsQuartzBean")
@ViewScoped
public class TsQuartzBean extends FacesBean {

    private static final long serialVersionUID = -738445454536625063L;
    /**
     * 存在session中的对象
     */
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    /**
     * ejb session bean
     */
	private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    /**
     * 查询条件：业务描述
     */
    private String searchBsDesc;
    /**查询条件：系统类型*/
    private Map<String, String> systemTypeMap = new LinkedHashMap<String, String>();
    private Map<String, String> systemTypeMap2 = new LinkedHashMap<String, String>();
    /**查询条件：系统类型*/
    private String searchSystemType;
    /**查询条件：所有能看到的系统类型comm,risk ，隔开*/
    private String allSystem;
    /**
     * 数据集
     */
    private List<TsQuartz> dataList;
    /**定时任务对象*/
    private TsQuartz tsQuartz = new TsQuartz();
    /**类型*/
    private short periodtype;

    private static final String[] NUMBERS = { "一", "二", "三", "四", "五", "六", "七" };

    public TsQuartzBean() {
        //初始化系统类型，能看到所有的
        TsUserInfo tsUserInfo = this.sessionData.getUser();
        SystemType[] types = SystemType.values();
        for(SystemType st: types) {
            this.systemTypeMap.put(st.getTypeCN(), st.toString());
            this.systemTypeMap2.put(st.getTypeNo().toString(),st.getTypeCN());
        }
        this.searchAction();
    }

    /**
     * 查询
     */
    public void searchAction() {
        this.dataList = this.systemModuleService.findQuartzList(this.searchBsDesc, this.searchSystemType, this.allSystem);
        if(null != this.dataList && this.dataList.size() > 0) {

            Map<Integer,SystemType> map = new HashMap();
            for (SystemType a : SystemType.values()) {
                map.put(a.getTypeNo().intValue(), a);
            }
            for(TsQuartz quartz : this.dataList) {
                quartz.setSystemType(map.get(quartz.getParamType()));
                if(quartz.getPeriodKind().intValue() == 1) {
                    quartz.setPeriodDesc(quartz.getExpressions());
                }else {
                    Short execycle = quartz.getPeriodType();
                    StringBuilder sb = new StringBuilder();
                    if (execycle.intValue()== 4) {
                        // 每月几号的几点几分执行一次
                        sb.append("每月").append(quartz.getDayOfMon()).append("日");
                        sb.append(quartz.getHourOfDay() == null ? 0 : quartz.getHourOfDay()).append("点");
                        sb.append(quartz.getMinOfDay() == null ? 0 : quartz.getMinOfDay()).append("分执行一次 ");
                    } else if (execycle.intValue()== 3) {
                        //每周几的几点几分执行一次
                        sb.append("每周").append(NUMBERS[quartz.getDayOfWeek()-1]);
                        sb.append(quartz.getHourOfDay() == null ? 0 : quartz.getHourOfDay()).append("点");
                        sb.append(quartz.getMinOfDay() == null ? 0 : quartz.getMinOfDay()).append("分执行一次 ");
                    } else if (execycle.intValue()== 2) {
                        //每天几点几分执行一次
                        sb.append("每天").append(quartz.getHourOfDay()).append("点");
                        sb.append(quartz.getMinOfDay() == null ? 0 : quartz.getMinOfDay()).append("分执行一次 ");
                    } else if (execycle.intValue()== 1) {
                        //几点钟执行一次
                        sb.append("间隔").append(quartz.getIntervalHour()).append("小时执行一次");
                    } else if (execycle.intValue()== 0) {
                        //几分钟执行一次
                        sb.append("间隔").append(quartz.getIntervalMin()).append("分钟执行一次");
                    }
                    quartz.setPeriodDesc(sb.toString());
                }
            }
        }
    }

    /**
     * 保存
     */
    public void saveAction() {
        if(periodtype == 0){
            if(tsQuartz.getIntervalMin() > 60){
                JsfUtil.addErrorMessage("间隔分钟不能大于60分钟！");
                return;
            }
        }else if(periodtype == 1){
            if(tsQuartz.getIntervalHour() > 24){
                JsfUtil.addErrorMessage("间隔时钟不能大于24小时！");
                return;
            }
        }
        tsQuartz.setPeriodType(periodtype);
        this.systemModuleService.updateQuartz(this.tsQuartz);
        JsfUtil.addSuccessMessage("保存成功！");
        RequestContext.getCurrentInstance().execute("CodeEditDialog.hide()");
        this.searchAction();
    }

    /**
     * 修改初始化
     */
    public void modInit() {
        if(null == this.tsQuartz.getPeriodType()) {
            periodtype = 0;
        }else{
            periodtype = this.tsQuartz.getPeriodType();
        }
    }

    /**
     * 开启任务
     */
    public void startTask() {
        try {
        	Job job = SpringContextHolder.getBean(this.tsQuartz.getTaskClass());
            QuartzManager.addJob(this.tsQuartz.getTaskCode(), job, this.formatCronExpression());
            this.tsQuartz.setIfReveal((short)1);
            this.systemModuleService.updateQuartz(this.tsQuartz);
            JsfUtil.addSuccessMessage("启动成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            //throw new RuntimeException(e);
            JsfUtil.addErrorMessage("启动失败！请联系管理员");
        }
    }

    /**
     * 停止任务
     */
    public void stopTask() {
        try {
            QuartzManager.removeJob(this.tsQuartz.getTaskCode());
            this.tsQuartz.setIfReveal((short)0);
            this.systemModuleService.updateQuartz(this.tsQuartz);
            JsfUtil.addSuccessMessage("暂停成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("暂停失败！请联系管理员");
        }
    }

    /**
     * 根据tsQuartz对象，初始化表达式
     */
    public String formatCronExpression() {
        if (this.tsQuartz.getPeriodKind().intValue() == 1) {
            return this.tsQuartz.getExpressions();
        } else {
            Short execycle = this.tsQuartz.getPeriodType();
            String excep="" ;
            if (execycle.intValue()== 4) {
                //每月几号如 "0 15 10 15 * ?"
                excep="0 "+this.tsQuartz.getMinOfDay() +" "+this.tsQuartz.getHourOfDay()+" "+ this.tsQuartz.getDayOfMon() +" * ?";
            } else if (execycle.intValue()== 3) {
                //每周周几如 "0 15 10 ? * MON"
                excep="0 "+this.tsQuartz.getMinOfDay() +" "+this.tsQuartz.getHourOfDay()+" ? * "+getWeekOfInt(this.tsQuartz.getDayOfWeek());
            } else if (execycle.intValue()== 2) {
                //每天几点如 "0 15 10 * * ?"
                excep="0 "+this.tsQuartz.getMinOfDay() +" "+this.tsQuartz.getHourOfDay()+" * * ?";
            } else if (execycle.intValue()== 1) {
                //每隔小时
                excep="0 0 */"+this.tsQuartz.getIntervalHour()+" * * ?";
            } else if (execycle.intValue()== 0) {
                //每隔分钟
                excep="0 */"+this.tsQuartz.getIntervalMin() +" * * * ?";
            }
            return excep;
        }
    }

    private String getWeekOfInt(short i){
        String week = null;
        switch (i){
            case 1:week = "MON";
                    break;
            case 2:week = "TUE";
                    break;
            case 3:week = "WED";
                    break;
            case 4:week = "THU";
                break;
            case 5:week = "FRI";
                break;
            case 6:week = "SAT";
                break;
            case 7:week = "SUN";
                break;
            default:week = "MON";
        }
        return week;
    }

    public String getSearchBsDesc() {
        return searchBsDesc;
    }

    public void setSearchBsDesc(String searchBsDesc) {
        this.searchBsDesc = searchBsDesc;
    }

    public Map<String, String> getSystemTypeMap() {
        return systemTypeMap;
    }

    public void setSystemTypeMap(Map<String, String> systemTypeMap) {
        this.systemTypeMap = systemTypeMap;
    }

    public String getSearchSystemType() {
        return searchSystemType;
    }

    public void setSearchSystemType(String searchSystemType) {
        this.searchSystemType = searchSystemType;
    }

    public List<TsQuartz> getDataList() {
        return dataList;
    }

    public void setDataList(List<TsQuartz> dataList) {
        this.dataList = dataList;
    }

    public TsQuartz getTsQuartz() {
        return tsQuartz;
    }

    public void setTsQuartz(TsQuartz tsQuartz) {
        this.tsQuartz = tsQuartz;
    }

    public short getPeriodtype() {
        return periodtype;
    }

    public void setPeriodtype(short periodtype) {
        this.periodtype = periodtype;
    }

    public Map<String, String> getSystemTypeMap2() {
        return systemTypeMap2;
    }

    public void setSystemTypeMap2(Map<String, String> systemTypeMap2) {
        this.systemTypeMap2 = systemTypeMap2;
    }
}
