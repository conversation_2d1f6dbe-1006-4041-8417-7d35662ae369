package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * 人员选择（单选）
 * Created by zww on 2014-8-4.
 */
@ManagedBean(name = "selectUserBean")
@ViewScoped
public class SelectUserBean extends FacesBean {

    /**科室名称*/
    private String userOfficeName;
    /**科室ID*/
    private Integer userOfficeRid;
    /** 流程类型树 */
    private TreeNode userOfficeTreeNode;
    /**姓名*/
    private String userName;
    /**用户集合*/
    private List<TsUserInfo> userList = new ArrayList<TsUserInfo>(0);
    /**已选的用户*/
    private TsUserInfo selectedUser;
    /**ejb session bean*/
	private SystemModuleServiceImpl systemService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);

    public SelectUserBean() {
        //初始化树
        this.initOfficeTree();
        this.searchAction();
    }

    /**
     * 初始化科室树
     */
    private void initOfficeTree() {
        //初始化科室树
        if(null == this.userOfficeTreeNode) {
            this.userOfficeTreeNode = new DefaultTreeNode("root", null);
            List<TsOffice> list = this.systemService.findOfficeList(this.sessionData.getUser().getTsUnit().getRid());
            if (null != list && list.size() > 0) {
                Map<Integer, TreeNode> map = new HashMap<Integer, TreeNode>();
                for (TsOffice t : list) {
                    if (null == t.getParentOffice()) {
                        TreeNode node = new DefaultTreeNode(t, this.userOfficeTreeNode);
                        map.put(t.getRid(), node);
                    } else {
                        TreeNode node = new DefaultTreeNode(t, map.get(t.getParentOffice().getRid()));
                        map.put(t.getRid(), node);
                    }
                }
                map.clear();
            }
        }
    }

    /**
     * 科室选中事件
     * @param event 选中事件
     */
    public void onOfficeNodeSelect(NodeSelectEvent event) {
        TreeNode node = event.getTreeNode();
        if (null != node) {
            TsOffice office = (TsOffice) node.getData();
            this.userOfficeRid = office.getRid();
            this.userOfficeName = office.getOfficename();
        }
        this.searchAction();
    }


    /**
     * 查询方法
     */
    public void searchAction() {
        //拼音码统一转成小写
        this.userList = this.systemService.findUserableUserList(this.userOfficeRid, this.userName, this.sessionData.getUser().getTsUnit().getRid());
    }

    /**
     * 选择确定方法
     */
    public void selectAction() {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("selectedUser",this.selectedUser);
        RequestContext.getCurrentInstance().closeDialog(map);
    }


    /**
     * 关闭
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

    public String getUserOfficeName() {
        return userOfficeName;
    }

    public void setUserOfficeName(String userOfficeName) {
        this.userOfficeName = userOfficeName;
    }

    public Integer getUserOfficeRid() {
        return userOfficeRid;
    }

    public void setUserOfficeRid(Integer userOfficeRid) {
        this.userOfficeRid = userOfficeRid;
    }

    public TreeNode getUserOfficeTreeNode() {
        return userOfficeTreeNode;
    }

    public void setUserOfficeTreeNode(TreeNode userOfficeTreeNode) {
        this.userOfficeTreeNode = userOfficeTreeNode;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public List<TsUserInfo> getUserList() {
        return userList;
    }

    public void setUserList(List<TsUserInfo> userList) {
        this.userList = userList;
    }

    public TsUserInfo getSelectedUser() {
        return selectedUser;
    }

    public void setSelectedUser(TsUserInfo selectedUser) {
        this.selectedUser = selectedUser;
    }
}
