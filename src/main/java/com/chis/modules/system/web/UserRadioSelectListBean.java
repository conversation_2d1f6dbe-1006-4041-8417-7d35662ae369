package com.chis.modules.system.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.service.CommServiceImpl;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 系统用户选择-单选
 */
@ManagedBean(name = "userRadioSelectListBean")
@ViewScoped
public class UserRadioSelectListBean extends FacesBean {

    private static final long serialVersionUID = -738445454536625063L;

    /**查询条件的名称*/
    private String searchName;
    /**自定义标题*/
    private String titleName;
    /** 选择的对象*/
    private TsUserInfo selectPro;

    /** 查询列集合*/
    private List<TsUserInfo> displayList;

    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**排除的rid*/
    private String excluderids;


    /**
     * <p>方法描述：初始化数据</p>
     * */
    public UserRadioSelectListBean() {
        //查询条件的名称
        this.searchName=JsfUtil.getRequest().getParameter("searchName");
        //自定义标题
        this.titleName = JsfUtil.getRequest().getParameter("titleName");
        //排除的记录
        this.excluderids = JsfUtil.getRequest().getParameter("excluderids");
        searchAction();
    }

    private void unitUserList() {
        displayList=new ArrayList<>();
        List<Object[]> userInfoList = commService.findUserInfo(this.searchName);
        if(!CollectionUtils.isEmpty(userInfoList)){
            for (Object[] objects : userInfoList) {
                if(objects[2]==null || objects[3]==null){
                    continue;
                }
                TsUserInfo userInfo=new TsUserInfo();
                userInfo.setRid(Integer.parseInt(objects[0].toString()));
                userInfo.setTsUnit(new TsUnit(Integer.parseInt(objects[1].toString()),objects[2].toString()));
                userInfo.setUsername(objects[3]!=null?objects[3].toString():"");
                userInfo.setMbNum(objects[4]!=null?StringUtils.encryptPhone(objects[4].toString()):"");
                displayList.add(userInfo);
            }
        }
    }

    /**
     * <p>方法描述：根据名称、拼音码过滤数据</p>
     */
    public void searchAction() {
        //初始化展示页面的数据集
        unitUserList();
    }

    /**
     * <p>方法描述：选择确定方法</p>
     */
    public void selectAction() {
        Map<String, Object> map = new HashMap<>();
        map.put("selectUser",selectPro);
        RequestContext.getCurrentInstance().closeDialog(map);
    }
    /**
     * <p>方法描述：关闭</p>
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

    public String getTitleName() {
        return titleName;
    }

    public void setTitleName(String titleName) {
        this.titleName = titleName;
    }

    public TsUserInfo getSelectPro() {
        return selectPro;
    }

    public void setSelectPro(TsUserInfo selectPro) {
        this.selectPro = selectPro;
    }

    public List<TsUserInfo> getDisplayList() {
        return displayList;
    }

    public void setDisplayList(List<TsUserInfo> displayList) {
        this.displayList = displayList;
    }

    public String getSearchName() {
        return searchName;
    }

    public void setSearchName(String searchName) {
        this.searchName = searchName;
    }

    public String getExcluderids() {
        return excluderids;
    }

    public void setExcluderids(String excluderids) {
        this.excluderids = excluderids;
    }
}
