package com.chis.modules.system.web;

import cn.hutool.core.convert.Convert;
import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsContraMain;
import com.chis.modules.system.entity.TsContraSub;
import com.chis.modules.system.service.ContrastManageService;
import com.chis.modules.system.utils.ImportExcelUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.io.*;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 */

@ManagedBean(name = "contrastManageBean")
@ViewScoped
public class ContrastManageBean extends FacesEditBean {

    private ContrastManageService contrastManageService = SpringContextHolder.getBean(ContrastManageService.class);
    /**
     * 左侧查询条件-对照类型/对照说明
     */
    private String contraCodeSearch;
    /**
     * 右侧查询条件-类型/类型说明
     */
    private String busiTypeSearch;
    /**
     * 右侧查询条件-LEFT_CODE
     */
    private String leftCodeSearch;
    /**
     * 右侧查询条件-RIGHT_CODE
     */
    private String rightCodeSearch;

    /**
     * 左侧列表
     */
    private List<Object[]> contraTableList;

    /**
     * 左侧选中的对照行
     */
    private Object[] selRecord;

    /**
     * 左侧选中的行的rid
     */
    private Integer rid;

    /**
     * 右侧选中的行的rid
     */
    private Integer subRid;

    /**
     * 添加、修改 操作实体
     */
    private TsContraMain contraMain = new TsContraMain();

    /**
     * 添加、修改 操作实体
     */
    private TsContraSub contraSub = new TsContraSub();

    /**
     * 全部删除子表类型
     */
    public List<String> busiTypeList;

    /**
     * 选中的子表需要删除的类型
     */
    private String delType;
    /**
     * 错误数据文件路径
     */
    private String importErrFilePath;
    /**
     * 导入错误文件名称
     */
    private String uuidFileName;

    /**
     * 右侧标题
     */
    private String subTitle;

    /**
     * 右侧表格真分页
     */
    // private ContrastManageRightTableBean subTableBean=new ContrastManageRightTableBean();
    public ContrastManageBean() {
        this.ifSQL = true;
        initContra();
    }

    /**
     * <p>Description：左侧列表数据初始化 </p>
     * <p>Author： yzz 2025-01-07 </p>
     */
    public void initContra() {
        this.contraTableList = new ArrayList<>();
        this.contraTableList = contrastManageService.findContraData(this.contraCodeSearch);
    }

    /**
     * <p>Description：右侧列表数据初始化 </p>
     * <p>Author： yzz 2025-01-07 </p>
     */
    public void initContraSub(Object[] selRecord) {
        if (selRecord == null) {
            return;
        }
        this.rid = Integer.parseInt(selRecord[0].toString());
        this.searchAction();
    }


    /**
     * <p>Description：左侧添加 </p>
     * <p>Author： yzz 2025-01-07 </p>
     */
    public void addContra() {
        contraMain = new TsContraMain();
        RequestContext.getCurrentInstance().execute("PF('ContraDialog').show();");
    }

    /**
     * <p>Description：左侧修改方法 </p>
     * <p>Author： yzz 2025-01-07 </p>
     */
    public void modContraAction() {
        if (this.rid == null) {
            return;
        }
        contraMain = this.contrastManageService.find(TsContraMain.class, this.rid);
        RequestContext.getCurrentInstance().execute("PF('ContraDialog').show();");
    }

    /**
     * <p>Description：左侧保存 </p>
     * <p>Author： yzz 2025-01-07 </p>
     */
    public void saveContra() {
        boolean flag = false;
        if (StringUtils.isBlank(this.contraMain.getContraCode())) {
            JsfUtil.addErrorMessage("业务类型不能为空！");
            flag = true;
        } else {
            int count = this.contrastManageService.findContraDataByCode(this.contraMain.getRid(), this.contraMain.getContraCode());
            if (count > 0) {
                JsfUtil.addErrorMessage("存在相同业务类型的记录！");
                flag = true;
            }
        }
        if (StringUtils.isBlank(this.contraMain.getDescr())) {
            JsfUtil.addErrorMessage("业务说明不能为空！");
            flag = true;
        }
        if (StringUtils.isNotBlank(this.contraMain.getRmk()) && this.contraMain.getRmk().length() > 1000) {
            JsfUtil.addErrorMessage("备注长度不能超过1000！");
            flag = true;
        }

        if (flag) {
            return;
        }
        // 保存
        try {
            this.contrastManageService.saveContraMain(this.contraMain);
            initContra();
            this.subTitle = this.contraMain.getDescr();
            JsfUtil.addSuccessMessage("保存成功！");
            RequestContext.getCurrentInstance().execute("PF('ContraDialog').hide();");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
     * <p>Description：左侧删除方法 </p>
     * <p>Author： yzz 2025-01-07 </p>
     */
    public void delContraAction() {
        if (this.rid == null) {
            return;
        }
        try {
            this.contrastManageService.delContraMain(this.rid);
            initContra();
            // 如果左侧列表不为空 且删除的是当前选中行
            if (this.selRecord != null && this.rid.equals(Integer.valueOf(this.selRecord[0].toString()))) {
                this.selRecord = null;
            }
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * <p>Description：右侧添加 </p>
     * <p>Author： yzz 2025-01-07 </p>
     */
    public void addContraSub() {
        if (this.selRecord == null) {
            JsfUtil.addErrorMessage("请选择一条业务类型！");
            return;
        }

        contraSub = new TsContraSub();
        RequestContext.getCurrentInstance().execute("PF('ContraSubDialog').show();");
    }
    /**
     * <p>Description：左侧修改方法 </p>
     * <p>Author： yzz 2025-01-07 </p>
     */
    public void modContraSubAction() {
        if (this.subRid == null) {
            return;
        }

        contraSub = this.contrastManageService.find(TsContraSub.class, this.subRid);
        RequestContext.getCurrentInstance().execute("PF('ContraSubDialog').show();");
    }

    /**
     * <p>Description：左侧删除方法 </p>
     * <p>Author： yzz 2025-01-07 </p>
     */
    public void delContraSubAction() {
        if (this.subRid == null) {
            return;
        }
        try {
            this.contrastManageService.delContraSub(this.subRid);
            initContraSub(this.selRecord);
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * <p>Description：右侧保存 </p>
     * <p>Author： yzz 2025-01-07 </p>
     */
    public void saveSubContra() {
        if (this.contraSub == null) {
            return;
        }
        boolean flag = false;
        if (contraSub.getBusiType() == null) {
            JsfUtil.addErrorMessage("对照类型不能为空！");
            flag = true;
        }
        if (StringUtils.isBlank(contraSub.getLeftCode())) {
            JsfUtil.addErrorMessage("LEFT_CODE不能为空！");
            flag = true;
        }
        if (StringUtils.isBlank(contraSub.getRightCode())) {
            JsfUtil.addErrorMessage("RIGHT_CODE不能为空！");
            flag = true;
        }
        if (StringUtils.isNotBlank(contraSub.getDsfSpecialDesc()) && contraSub.getDsfSpecialDesc().length() > 50) {
            JsfUtil.addErrorMessage("备注长度不能超过50！");
            flag = true;
        }
        if (contraSub.getBusiType() != null && StringUtils.isNotBlank(contraSub.getLeftCode()) && StringUtils.isNotBlank(contraSub.getRightCode())) {
            int count = this.contrastManageService.findContraSubByCode(contraSub.getRid(), contraSub.getBusiType(), contraSub.getLeftCode(), contraSub.getRightCode(), Integer.parseInt(this.selRecord[0].toString()));
            if (count > 0) {
                JsfUtil.addErrorMessage("同对照类型下存在LEFT_CODE和RIGHT_CODE相同记录！");
                return;
            }
        }
        if (flag) {
            return;
        }
        try {
            contraSub.setFkByMainId(new TsContraMain(Integer.parseInt(this.selRecord[0].toString())));
            this.contrastManageService.saveContraSubMain(this.contraSub);
            initContraSub(this.selRecord);
            JsfUtil.addSuccessMessage("保存成功！");
            RequestContext.getCurrentInstance().execute("PF('ContraSubDialog').hide();");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
     * <p>Description：左侧选中行 </p>
     * <p>Author： yzz 2024-08-09 </p>
     */
    public void onRowSelect(SelectEvent event) {
        this.selRecord = (Object[]) event.getObject();
        // 清空查询条件
        this.busiTypeSearch = null;
        this.leftCodeSearch = null;
        this.rightCodeSearch = null;
        initContraSub(this.selRecord);
        subTitle = this.selRecord[2].toString();
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("mainForm:subTable");
        dataTable.setFirst(0);
        dataTable.setRows(20);

    }

    /**
     * <p>Description：右侧实时查询 </p>
     * <p>Author： yzz 2025-01-08 </p>
     */
    public void searchSubAction() {
        if (this.selRecord == null) {
            return;
        }
        initContraSub(this.selRecord);
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("mainForm:subTable");
        dataTable.setFirst(0);
    }

    /**
     * <p>Description：右侧实时查询 </p>
     * <p>Author： yzz 2025-01-08 </p>
     */
    public void searchMainAction() {
        initContra();
        if (this.selRecord == null) {
            return;
        }
        initContraSub(this.selRecord);
        if (this.selRecord != null) {
            if (!CollectionUtils.isEmpty(contraTableList)) {
                boolean ifHasSel = false;
                for (Object[] objects : contraTableList) {
                    if (objects[0] != null && objects[0].toString().equals(this.selRecord[0].toString())) {
                        ifHasSel = true;
                        break;
                    }
                }
                if (!ifHasSel) {
                    this.selRecord = null;
                    RequestContext.getCurrentInstance().update("mainForm:subPanel");
                }
            } else {
                this.selRecord = null;
                RequestContext.getCurrentInstance().update("mainForm:subPanel");
            }
        }
    }


    public void searchAction() {
        super.searchAction();
    }

    /**
     * <p>Description：全部删除 弹框 </p>
     * <p>Author： yzz 2025-01-09 </p>
     */
    public void delSubByType() {
        if (this.selRecord == null) {
            return;
        }
        if (CollectionUtils.isEmpty(this.getDataModel().getData())) {
            JsfUtil.addErrorMessage("无可删除的数据！");
            return;
        }
        this.busiTypeList = this.contrastManageService.findBusTypeList(Integer.parseInt((this.selRecord[0].toString())));
        if (!CollectionUtils.isEmpty(this.busiTypeList)) {
            this.delType = this.busiTypeList.get(0);
        }
        RequestContext.getCurrentInstance().execute("PF('DelContraSubDialog').show();");
    }

    /**
     * <p>Description： 通过子表类型删除所有子表记录</p>
     * <p>Author： yzz 2025-01-09 </p>
     */
    public void delAllSubByType() {
        if (this.delType == null) {
            return;
        }
        try {
            this.contrastManageService.delAllSubByType(this.delType, Integer.parseInt((this.selRecord[0].toString())));
            initContraSub(this.selRecord);
            JsfUtil.addSuccessMessage("删除成功！");
            RequestContext.getCurrentInstance().execute("PF('DelContraSubDialog').hide();");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
        RequestContext.getCurrentInstance().execute("zwx_loading_stop();");
    }

    /**
     * <p>方法描述：导入模板下载</p>
     *
     * @MethodAuthor hsj 2025-01-16 14:19
     */
    public StreamedContent getTemplateFile() {
        String fileName = "对照维护导入模板.xlsx";
        if (StringUtils.isBlank(fileName)) {
            return null;
        }
        InputStream stream = null;
        try {
            String moudleFilePath = "/resources/template/excel/" + fileName;
            stream = FacesContext.getCurrentInstance().getExternalContext().getResourceAsStream(moudleFilePath);
            return new DefaultStreamedContent(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", URLEncoder.encode(fileName, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("文件下载失败!");
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }
        return null;
    }

    /**
     * <p>方法描述：文件导入前验证</p>
     *
     * @MethodAuthor hsj 2025-01-17 9:01
     */
    public void openImportDialog() {
        if (this.selRecord == null || this.selRecord[0] == null) {
            JsfUtil.addErrorMessage("请选择一条对照类型！");
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').show()");
    }

    /**
     * <p>方法描述：文件导入</p>
     *
     * @MethodAuthor hsj 2025-01-16 14:50
     */
    public void importDataAction(FileUploadEvent event) {
        this.importErrFilePath = null;
        // 删除历史错误文件
        File errorFile = new File(JsfUtil.getAbsolutePath() + "heth/system/temp/" + this.uuidFileName + ".xlsx");
        if (errorFile.exists()) {
            boolean ignore = errorFile.delete();
        }
        errorFile = new File(JsfUtil.getAbsolutePath() + "heth/system/temp/" + this.uuidFileName + ".xls");
        if (errorFile.exists()) {
            boolean ignore = errorFile.delete();
        }

        String updateFormId = "tabView:mainForm:uploadFileDialog";
        if (event == null || event.getFile() == null) {
            RequestContext.getCurrentInstance().update(updateFormId);
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
            JsfUtil.addErrorMessage("文件上传失败！");
            return;
        }

        Workbook wb = null;
        String fileName = null;
        List<List<Object>> excelRowList = new ArrayList<>();
        try {
            // 处理数据长度
            this.uuidFileName = StringUtils.uuid();
            UploadedFile file = event.getFile();
            fileName = file.getFileName();
            String errorMsg = FileUtils.veryFile(file.getInputstream(), file.getContentType(), fileName, "5");
            if (StringUtils.isNotBlank(errorMsg)) {
                JsfUtil.addErrorMessage(errorMsg);
                RequestContext.getCurrentInstance().update(updateFormId);
                RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
                return;
            }
            // 验证数据总数量是否满足
            wb = ImportExcelUtil.getWorkbook(file);
            // 读取workbook第一页sheet的数据
            if (!verifyWorkBook(wb, excelRowList)) {
                RequestContext.getCurrentInstance().update(updateFormId);
                RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
                return;
            }
            // 处理数据长度
            int colSize = 8;
            dealExcelRowList(excelRowList, colSize);
            List<TsContraSub> resultProList = new ArrayList<>();
            // 验证数据
            Map<Integer, String> errorMap = new HashMap<>();
            this.verifyData(excelRowList, resultProList, errorMap);
            // 数据并保存
            if (!CollectionUtils.isEmpty(resultProList)) {
                this.contrastManageService.saveTsContraSub(resultProList);
            }
            // 生成错误数据文件
            generateImportErrorFile(wb, fileName, colSize, excelRowList, errorMap);
            // 计算成功/失败条数并提示
            pakTipStr(excelRowList, errorMap);
            RequestContext.getCurrentInstance().update("mainForm:subTable");
            RequestContext.getCurrentInstance().update("mainForm:right_btn");
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
        } catch (Exception e) {
            e.printStackTrace();
            RequestContext.getCurrentInstance().update(updateFormId);
            RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
            JsfUtil.addErrorMessage("导入文件异常！");
        }
    }

    /**
     * <p>方法描述：数据验证</p>
     *
     * @MethodAuthor hsj 2025-01-16 15:41
     */
    private void verifyData(List<List<Object>> excelRowList, List<TsContraSub> resultProList, Map<Integer, String> errorMap) {
        if (ObjectUtil.isEmpty(excelRowList)) {
            return;
        }
        // 获取所有对照编码
        List<String> codeList = this.dealCodes();
        int lineNum = 1;
        for (List<Object> rowList : excelRowList) {
            TsContraSub tsContraSub = new TsContraSub();
            // 当前行数的错误信息
            List<String> errList = new ArrayList<>();
            if (CollectionUtils.isEmpty(rowList)) {
                errList.add("数据不能为空");
                errorMap.put(lineNum, StringUtils.list2string(errList, ";"));
                lineNum++;
                continue;
            }
            int colNum = 0;
            Object obj = removeSpaces(rowList, colNum++);
            if (null == obj) {
                errList.add("对照类型不能为空");
            } else {
                Integer busiType = Convert.toInt(obj);
                if (ObjectUtil.isNull(busiType) || busiType < 0) {
                    errList.add("对照类型应为整数类型");
                } else if (Convert.toStr(busiType).length() > 3) {
                    errList.add("对照类型长度应小于等于3");
                } else {
                    tsContraSub.setBusiType(busiType);
                }
            }
            // 类型说明
            validateField(rowList, errList, tsContraSub, "对照类型说明", colNum++, true, 50, "setBusDesc");
            validateField(rowList, errList, tsContraSub, "LEFT_CODE", colNum++, false, 50, "setLeftCode");
            validateField(rowList, errList, tsContraSub, "LEFT_DESC", colNum++, true, 100, "setLeftDesc");
            validateField(rowList, errList, tsContraSub, "RIGHT_CODE", colNum++, false, 50, "setRightCode");
            validateField(rowList, errList, tsContraSub, "RIGHT_DESC", colNum++, true, 100, "setDescr");
            obj = removeSpaces(rowList, colNum++);
            if (null != obj && !(Convert.toInt(obj) != null && Convert.toStr(obj).length() == 1)) {
                errList.add("特殊标记应为一位数的整数");
            } else {
                tsContraSub.setDsfTag(Convert.toInt(obj));
            }
            validateField(rowList, errList, tsContraSub, "备注", colNum, true, 50, "setDsfSpecialDesc");
            String key = tsContraSub.getBusiType() + "&" + tsContraSub.getLeftCode() + "&" + tsContraSub.getRightCode();
            if (codeList.contains(key)) {
                errList.add("同对照类型下存在LEFT_CODE和RIGHT_CODE相同记录");
            }
            if (CollectionUtils.isEmpty(errList)) {
                Integer mainId = Convert.toInt(this.selRecord[0]);
                tsContraSub.setFkByMainId(new TsContraMain(mainId));
                resultProList.add(tsContraSub);
                codeList.add(key);
            } else {
                errorMap.put(lineNum, StringUtils.list2string(errList, ";"));
            }
            lineNum++;
        }
    }

    /**
     * <p>方法描述：去除空格</p>
     *
     * @MethodAuthor hsj 2025-01-22 16:04
     */
    private Object removeSpaces(List<Object> rowList, int i) {
        Object obj = rowList.get(i);
        if (obj != null) {
            obj = Convert.toStr(obj).trim();
        }
        return obj;
    }

    /**
     * <p>方法描述：数据验证及存储</p>
     *
     * @param rowList          行数据
     * @param errList          错误信息
     * @param tsContraSub      实体对象
     * @param fieldName        提示名称
     * @param index            行下标
     * @param nullable         是否可为空
     * @param maxLength        最大值
     * @param setterMethodName 对应实体名称
     * @MethodAuthor hsj 2025-01-17 9:50
     */
    private void validateField(List<Object> rowList, List<String> errList, TsContraSub tsContraSub, String fieldName, int index, boolean nullable, int maxLength, String setterMethodName) {
        if (index >= rowList.size()) {
            errList.add(fieldName + "不存在");
            return;
        }
        Object obj = removeSpaces(rowList, index);
        if (obj == null) {
            if (!nullable) {
                errList.add(fieldName + "不能为空");
            }
            return;
        }
        String value = Convert.toStr(obj);
        if (value.length() > maxLength) {
            errList.add(fieldName + "长度应小于等于" + maxLength);
            return;
        }
        try {
            Method method = TsContraSub.class.getMethod(setterMethodName, String.class);
            method.invoke(tsContraSub, value);
        } catch (Exception e) {
            errList.add("设置字段失败：" + e.getMessage());
        }
    }

    /**
     * <p>方法描述：查询已存在的编码</p>
     *
     * @MethodAuthor hsj 2025-01-16 15:49
     */
    private List<String> dealCodes() {
        List<String> codeList = new ArrayList<>();
        if (this.selRecord == null) {
            return codeList;
        }
        Integer mainId = Convert.toInt(this.selRecord[0]);
        if (ObjectUtil.isNull(mainId)) {
            return codeList;
        }
        List<Object[]> contraSubList = contrastManageService.findContraSubData(mainId, null, null, null);
        if (CollectionUtils.isEmpty(contraSubList)) {
            return codeList;
        }
        for (Object[] objects : contraSubList) {
            String code = Convert.toStr(objects[1]) + "&" + Convert.toStr(objects[3]) + "&" + Convert.toStr(objects[5]);
            codeList.add(code);
        }
        return codeList;
    }

    /**
     * 封装提示信息并提示
     *
     * @param excelRowList excel数据
     * @param errorMap     错误信息Map
     */
    private void pakTipStr(List<List<Object>> excelRowList, Map<Integer, String> errorMap) {
        String str;
        int excelRowListSize = excelRowList.size();
        int failedCount = errorMap.size();
        int successCount = excelRowListSize - failedCount;
        str = "共导入" + excelRowListSize + "条数据。成功" + successCount + "条数据，失败" + failedCount + "条数据。";
        if (failedCount > 0) {
            str += "请下载错误数据！";
            JsfUtil.addErrorMessage(str);
        } else {
            JsfUtil.addSuccessMessage(str);
        }
        if (successCount > 0) {
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("mainForm:subTable");
            dataTable.setFirst(0);
            initContraSub(this.selRecord);
        }
    }

    /**
     * 生成错误数据文件
     *
     * @param wb           excel文件
     * @param oldFileName  原文件名称
     * @param colSize      列头行数
     * @param excelRowList excel数据
     * @param errorMap     错误信息Map
     */
    private void generateImportErrorFile(Workbook wb, String oldFileName, int colSize, List<List<Object>> excelRowList, Map<Integer, String> errorMap) {
        if (errorMap == null || errorMap.size() == 0) {
            return;
        }
        CellStyle cs = wb.createCellStyle();// 标题样式
        CellStyle cs2 = wb.createCellStyle();// 错误样式
        CellStyle cs3 = wb.createCellStyle();// 原始行样式
        this.errColumnStyle(wb, cs, cs2, cs3);
        int errColumn = colSize + 1;
        // 增加sheet0错误列
        Sheet sheet0 = wb.getSheetAt(0);
        Row headRow0 = sheet0.getRow(0);
        Cell headCell0 = headRow0.createCell(colSize);
        headCell0.setCellValue("原始行");
        headCell0.setCellStyle(cs);
        headCell0 = headRow0.createCell(errColumn);
        headCell0.setCellValue("数据错误原因");
        headCell0.setCellStyle(cs);
        if (colSize > 1) {
            sheet0.addMergedRegion(new CellRangeAddress(0, 0, colSize, colSize));
            sheet0.addMergedRegion(new CellRangeAddress(0, 0, errColumn, errColumn));
        }
        sheet0.setColumnWidth(colSize, 3000);
        sheet0.setColumnWidth(errColumn, 20000);
        List<Integer> removeRowIndexList = new ArrayList<>();
        int excelRowListSize = excelRowList.size() + 1;
        for (int i = 1; i < excelRowListSize; i++) {
            if (errorMap.containsKey(i)) {
                String errMsg = errorMap.get(i);
                Row curRow = sheet0.getRow(i);
                if (curRow == null) {
                    curRow = sheet0.createRow(i);
                }
                Cell cell = curRow.createCell(colSize);
                cell.setCellValue(i);
                cell.setCellStyle(cs3);
                cell = curRow.createCell(errColumn);
                cell.setCellValue(errMsg);
                cell.setCellStyle(cs2);
            } else {
                removeRowIndexList.add(i);
            }
        }
        Map<Integer, List<Integer>> sheetIndexWithRemoveIndexListMap = new HashMap<>();
        sheetIndexWithRemoveIndexListMap.put(0, removeRowIndexList);
        ExcelExportUtil.removeSheetRows(wb, sheetIndexWithRemoveIndexListMap);
        String endType = ".xlsx";
        if (StringUtils.isNotBlank(oldFileName) && oldFileName.contains(".")) {
            endType = oldFileName.substring(oldFileName.lastIndexOf("."));
        }
        // 错误信息写入到文件
        String filePath = JsfUtil.getAbsolutePath() + "heth/system/temp/" + this.uuidFileName + endType;
        File dirFile = new File(JsfUtil.getAbsolutePath() + "heth/system/temp/");
        if (!dirFile.exists()) {
            dirFile.mkdirs();
        }
        OutputStream outputStream = null;
        try {
            outputStream = Files.newOutputStream(Paths.get(filePath));
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
            this.importErrFilePath = "/heth/system/temp/" + this.uuidFileName + endType;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * <p>方法描述：错误数据行 增加样式</p>
     *
     * @MethodAuthor hsj 2025-01-16 15:33
     */
    private void errColumnStyle(Workbook wb, CellStyle cs, CellStyle cs2, CellStyle cs3) {
        // 创建两种字体
        Font f = wb.createFont();
        Font f2 = wb.createFont();
        Font f3 = wb.createFont();
        // 创建第一种字体样式（用于列名）
        f.setFontName("宋体");
        f.setFontHeightInPoints((short) 11);
        f.setColor(IndexedColors.BLACK.getIndex());
        f.setBoldweight(Font.BOLDWEIGHT_BOLD);
        // 创建第二种字体样式（用于值）
        f2.setFontName("宋体");
        // 设置行高
        f2.setFontHeightInPoints((short) 11);
        f2.setColor(IndexedColors.RED.getIndex());
        // 创建第三种字体样式（用于值）
        f3.setFontName("宋体");
        // 设置行高
        f3.setFontHeightInPoints((short) 11);
        f3.setColor(IndexedColors.BLACK.getIndex());
        // 设置第一种单元格的样式（用于列名）
        cs.setFont(f);
        cs.setAlignment(CellStyle.ALIGN_CENTER);
        cs.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
        // 设置第二种单元格的样式（用于值）
        cs2.setFont(f2);
        cs2.setAlignment(CellStyle.ALIGN_LEFT);
        cs2.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
        // 设置第二种单元格的样式（用于值）
        cs3.setFont(f3);
        cs3.setAlignment(CellStyle.ALIGN_CENTER);
        cs3.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);
    }

    /**
     * <p>方法描述：处理数据，保证list长度一致</p>
     *
     * @MethodAuthor hsj 2025-01-16 15:30
     */
    private void dealExcelRowList(List<List<Object>> excelRowList, int size) {
        if (ObjectUtil.isEmpty(excelRowList)) {
            return;
        }
        for (List<Object> objects : excelRowList) {
            if (ObjectUtil.isEmpty(objects)) {
                continue;
            }
            int size1 = objects.size();
            if (size1 < size) {
                while (size1++ < size) {
                    objects.add(null);
                }
            }
        }
    }

    /**
     * <p>方法描述：验证导入文件是否有数据</p>
     *
     * @MethodAuthor hsj 2025-01-16 14:53
     */
    private boolean verifyWorkBook(Workbook wb, List<List<Object>> excelRowList) {
        try {
            if (null == wb || wb.getNumberOfSheets() <= 0) {
                JsfUtil.addErrorMessage("Excel表格无数据，请重新选择文件导入！");
                return false;
            }
            int sheetNum = ImportExcelUtil.countExcel(wb, 0);
            if (sheetNum <= 0) {
                JsfUtil.addErrorMessage("Excel表格无数据，请重新选择文件导入！");
                return false;
            }
            List<List<Object>> readExcelRowList = ImportExcelUtil.readNewExcelWithEmptyRow(wb, 0);
            // 初始化表头下标
            if (ObjectUtil.isNotEmpty(readExcelRowList)) {
                readExcelRowList.remove(0);
            }
            if (isListAllNull(readExcelRowList)) {
                JsfUtil.addErrorMessage("Excel表格无数据，请重新选择文件导入！");
                return false;
            }
            excelRowList.addAll(readExcelRowList);
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("Excel表格无数据，请重新选择文件导入！");
            return false;
        }
        return true;
    }

    /**
     * 判断list是否都为null
     *
     * @param list list
     * @return list是否都为null
     */
    private boolean isListAllNull(List<List<Object>> list) {
        if (ObjectUtil.isEmpty(list)) {
            return true;
        }
        for (List<Object> objects : list) {
            if (ObjectUtil.isNotEmpty(objects)) {
                return false;
            }
        }
        return true;
    }

    /**
     * <p>方法描述：错误数据下载</p>
     *
     * @MethodAuthor hsj 2025-01-16 16:27
     */
    public StreamedContent getErrorImportFile() {
        if (StringUtils.isBlank(this.importErrFilePath)) {
            return null;
        }
        InputStream stream;
        try {
            stream = new FileInputStream(JsfUtil.getAbsolutePath() + this.importErrFilePath);
            String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            String fileName = "对照维护导入错误数据.xlsx";
            if (this.importErrFilePath.endsWith(".xls")) {
                contentType = "application/vnd.ms-excel";
                fileName = "对照维护导入错误数据.xls";
            }
            return new DefaultStreamedContent(stream, contentType, URLEncoder.encode(fileName, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("下载失败!");
        }
        return null;
    }
    @Override
    public void addInit() {

    }
    @Override
    public void viewInit() {

    }
    @Override
    public void modInit() {

    }
    @Override
    public void saveAction() {

    }
    @Override
    public String[] buildHqls() {
        StringBuilder sql = new StringBuilder();
        sql.append(" FROM TS_CONTRA_SUB T ");
        sql.append(" where T.MAIN_ID= ").append(rid);
        if (StringUtils.isNotBlank(busiTypeSearch)) {
            sql.append(" and ( T.BUSI_TYPE like :busiTypeSearch escape '\\\'");
            sql.append(" OR T.BUS_DESC like :busDesc escape '\\\'");
            sql.append(")");
            paramMap.put("busiTypeSearch", "%" + StringUtils.convertBFH(busiTypeSearch.trim()) + "%");
            paramMap.put("busDesc", "%" + StringUtils.convertBFH(busiTypeSearch.trim()) + "%");
        }
        if (StringUtils.isNotBlank(leftCodeSearch)) {
            sql.append(" and (T.LEFT_CODE like :leftCodeSearch escape '\\\'");
            sql.append(" OR T.LEFT_DESC like :leftDescSearch escape '\\\' )");
            paramMap.put("leftCodeSearch", "%" + StringUtils.convertBFH(leftCodeSearch.trim()) + "%");
            paramMap.put("leftDescSearch", "%" + StringUtils.convertBFH(leftCodeSearch.trim()) + "%");
        }
        if (StringUtils.isNotBlank(rightCodeSearch)) {
            sql.append(" and (T.RIGHT_CODE like :rightCodeSearch escape '\\\'");
            sql.append(" OR T.DESCR like :descr escape '\\\' )");
            paramMap.put("rightCodeSearch", "%" + StringUtils.convertBFH(rightCodeSearch.trim()) + "%");
            paramMap.put("descr", "%" + StringUtils.convertBFH(rightCodeSearch.trim()) + "%");
        }
        String h1 = "SELECT T.RID,T.BUSI_TYPE,T.BUS_DESC,T.LEFT_CODE,T.LEFT_DESC,T.RIGHT_CODE,T.DESCR,T.DSF_TAG,T.DSF_SPECIAL_DESC,T.MAIN_ID " + sql + " order by T.BUSI_TYPE,T.LEFT_CODE ";
        String h2 = "SELECT COUNT(*) " + sql;
        return new String[]{h1, h2};
    }

    public String getContraCodeSearch() {
        return contraCodeSearch;
    }
    public void setContraCodeSearch(String contraCodeSearch) {
        this.contraCodeSearch = contraCodeSearch;
    }
    public String getBusiTypeSearch() {
        return busiTypeSearch;
    }
    public void setBusiTypeSearch(String busiTypeSearch) {
        this.busiTypeSearch = busiTypeSearch;
    }
    public String getLeftCodeSearch() {
        return leftCodeSearch;
    }
    public void setLeftCodeSearch(String leftCodeSearch) {
        this.leftCodeSearch = leftCodeSearch;
    }
    public String getRightCodeSearch() {
        return rightCodeSearch;
    }
    public void setRightCodeSearch(String rightCodeSearch) {
        this.rightCodeSearch = rightCodeSearch;
    }
    public List<Object[]> getContraTableList() {
        return contraTableList;
    }
    public void setContraTableList(List<Object[]> contraTableList) {
        this.contraTableList = contraTableList;
    }
    public Object[] getSelRecord() {
        return selRecord;
    }
    public void setSelRecord(Object[] selRecord) {
        this.selRecord = selRecord;
    }

    public Integer getRid() {
        return rid;
    }
    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TsContraMain getContraMain() {
        return contraMain;
    }
    public void setContraMain(TsContraMain contraMain) {
        this.contraMain = contraMain;
    }

    public TsContraSub getContraSub() {
        return contraSub;
    }
    public void setContraSub(TsContraSub contraSub) {
        this.contraSub = contraSub;
    }

    public Integer getSubRid() {
        return subRid;
    }
    public void setSubRid(Integer subRid) {
        this.subRid = subRid;
    }

    public List<String> getBusiTypeList() {
        return busiTypeList;
    }
    public void setBusiTypeList(List<String> busiTypeList) {
        this.busiTypeList = busiTypeList;
    }
    public String getDelType() {
        return delType;
    }
    public void setDelType(String delType) {
        this.delType = delType;
    }

    public String getImportErrFilePath() {
        return importErrFilePath;
    }

    public void setImportErrFilePath(String importErrFilePath) {
        this.importErrFilePath = importErrFilePath;
    }


    public String getUuidFileName() {
        return uuidFileName;
    }

    public void setUuidFileName(String uuidFileName) {
        this.uuidFileName = uuidFileName;
    }

    public String getSubTitle() {
        return subTitle;
    }
    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

}
