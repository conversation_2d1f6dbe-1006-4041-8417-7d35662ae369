package com.chis.modules.system.web;

import java.util.LinkedList;
import java.util.List;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;

import org.primefaces.context.RequestContext;

import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TdMsgSub;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.MessageType;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.MsgSendUtil;
import com.google.common.collect.Lists;

/**
 * 我的消息查看
 * 
 * @editContent 修改待办任务的消息查看，增加判断，如果任务已被处理，则直接提醒 david 2014-09-04
 * 
 * <AUTHOR>
 * @history 2014-07-16
 */
@ManagedBean(name = "tdMsgMainSearchBean")
@ViewScoped
public class TdMsgMainSearchBean extends FacesSimpleBean implements IProcessData{

	private static final long serialVersionUID = -738445454536625063L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/** ejb session bean */
	private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	/*private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);*/
	/** 查询状态 */
	private String[] searchState;

	/** 查询标题 */
	private String searchInfoTitle;

	/** 选择的结果集 */
	private List<Object[]> selResultList;

	/** 修改状态 */
	private String state;

	private String rid;
	private TdMsgSub tdMsgSub;
	/** 查询类型 */
	private List<SelectItem> searchTypeName;
	private String[] type;
	
	/**我的委托，*/
	private List<Object[]> trustList;

	/**全部已阅的sql**/
	private String editAllsql;
	public TdMsgMainSearchBean() {
		String param = JsfUtil.getRequest().getParameter("param1");
		if (StringUtils.isNotBlank(param) && param.equals("1")) {
			JsfUtil.addSuccessMessage("操作成功！");
		}
		super.ifSQL = true;
		
		this.searchInfoTitle = "";
		this.searchState = new String[] { "0" };
		this.searchTypeName = new LinkedList<SelectItem>();
		MessageType[] type = MessageType.values();
		for (MessageType t : type) {
			this.searchTypeName.add(new SelectItem(t.getTypeNo(), t.getTypeCN()));
		}
		
		/*this.trustList = this.flowBusinessService.findMyTrust(this.sessionData.getUser().getRid());*/
		
		this.searchAction();
	}

	/**
	 * 查询
	 */
	@Override
	public void searchAction() {
		this.state = null;
		this.selResultList = Lists.newArrayList();
		super.searchAction();
	}
	
	/**
	 * 0-消息子表ID <br/>
	 * 1-消息类型 <br/>
	 * 2-消息子表接收状态 <br/>
	 * 3-消息链接地址 <br/> 
	 * 4-消息的发起人 <br/> 
	 * 5-消息的发送时间 <br/>
	 * 6-消息标题<br/>
	 * 7-消息主表ID<br/>
	 * 根据消息发起时间倒序<br/>
	 * 
	 * 查询发给自己的消息以及他人一段时间内委托我待办的流程消息（仅未阅的流程消息）<br/>
	 */
	@Override
	public String[] buildHqls() {
		/**
		 * 是否包含委托，（查询条件不含流程，不含未阅的话，就不需要委托了）<br/>
		 */
		boolean containsWt = true;
		StringBuilder temps=new StringBuilder();
		StringBuilder sb = new StringBuilder();
		StringBuilder count = new StringBuilder(" select count(*) from (");
		sb.append(" SELECT M.* FROM (");
		
		sb.append(" SELECT T1.RID, T3.MSG_TYPE, T1.ACCEPT_STATE, T3.NET_ADR, T4.USERNAME, ");
		sb.append(" TO_CHAR(T3.PUBLISH_TIME, 'YYYY-MM-DD HH24:MI') AS PUBLISH_TIME, ");
		sb.append(" T3.INFO_TITLE, T3.RID AS MAIN_ID ");
		sb.append(" FROM TD_MSG_SUB T1 ");
		sb.append(" INNER JOIN TS_USER_INFO T2 ON T1.PUBLISH_MAN = T2.RID ");
		sb.append(" INNER JOIN TD_MSG_MAIN T3 ON T1.MAIN_ID = T3.RID ");
		sb.append(" LEFT JOIN TS_USER_INFO T4 ON T3.PUBLISH_MAN = T4.RID ");
		sb.append(" WHERE 1=1 ");
		sb.append(" AND T2.RID = '").append(this.sessionData.getUser().getRid()).append("' ");
		if (null != this.searchState && 0 != this.searchState.length) {
			String stateStr = StringUtils.array2string(this.searchState, ",");
			if(!StringUtils.contains("," + stateStr + ",", ",0,")) {
				containsWt = false;
			}
			sb.append(" AND T1.ACCEPT_STATE IN (").append(stateStr).append(") ");
			temps.append(" AND T1.ACCEPT_STATE IN (").append(stateStr).append(") ");
		}else{
			sb.append(" $$ ");
		}
		if (null != this.type && 0 != this.type.length) {
			String typeStr = StringUtils.array2string(this.type, ",");
			if(!StringUtils.contains("," + typeStr + ",", ",1,")) {
				containsWt = false;
			}
			sb.append(" AND T3.MSG_TYPE IN (").append(typeStr).append(") ");
		}
		
		if(null != this.trustList && this.trustList.size() > 0 && containsWt) {
			for(Object[] o : this.trustList) {
				sb.append(" UNION ");
				sb.append(" SELECT T1.RID, T3.MSG_TYPE, T1.ACCEPT_STATE, T3.NET_ADR, T4.USERNAME, ");
				sb.append(" TO_CHAR(T3.PUBLISH_TIME, 'YYYY-MM-DD HH24:MI') AS PUBLISH_TIME,  ");
				sb.append(" '【委托】'||T3.INFO_TITLE, ");
				sb.append(" T3.RID AS MAIN_ID ");
				sb.append(" FROM TD_MSG_SUB T1 ");
				sb.append(" INNER JOIN TS_USER_INFO T2 ON T1.PUBLISH_MAN = T2.RID ");
				sb.append(" INNER JOIN TD_MSG_MAIN T3 ON T1.MAIN_ID = T3.RID ");
				sb.append(" INNER JOIN TS_USER_INFO T4 ON T3.PUBLISH_MAN = T4.RID ");
				sb.append(" INNER JOIN ACT_RU_TASK T5 ON SUBSTR(T3.NET_ADR, INSTR(T3.NET_ADR, '?taskId=')+8) = T5.ID_ ");
				sb.append(" AND T5.PROC_DEF_ID_ IN (").append(o[1]).append(") ");
				sb.append(" WHERE T3.MSG_TYPE = '1' AND T1.ACCEPT_STATE = '0' ");
				sb.append(" AND T2.RID = '").append(o[0]).append("' ");
				if (StringUtils.isNotBlank(this.searchInfoTitle)) {
					sb.append(" AND T3.INFO_TITLE LIKE :infoTitle ESCAPE '\\\' ");
					this.paramMap.put("infoTitle", "%" + StringUtils.convertBFH(this.searchInfoTitle) + "%");
				}
			}
		}
		sb.append(") M WHERE 1=1 ");
		
		if (StringUtils.isNotBlank(this.searchInfoTitle)) {
			sb.append(" AND M.INFO_TITLE LIKE :infoTitle ESCAPE '\\\' ");
			this.paramMap.put("infoTitle", "%" + StringUtils.convertBFH(this.searchInfoTitle) + "%");
		}
		sb.append(" ORDER BY M.PUBLISH_TIME DESC ");
		
		if(null != this.searchState && 0 != this.searchState.length){
			editAllsql=sb.toString().replace(temps.toString(), " $$ ");
		}else{
			editAllsql=sb.toString();
			sb=new StringBuilder(sb.toString().replace(" $$ ", ""));
		}
		return new String[]{sb.toString(),count.append(sb).append(")").toString()};
	}
	
	@Override
    public void processData(List<?> list) {
        if(null != list && list.size() > 0) {
            for(int i=0; i<list.size(); i++) {
                Object[] os = (Object[]) list.get(i);
                os[1] = (MessageType)EnumUtils.findEnum(MessageType.class, os[1]);
            }
        }
    }

	/**全部设为1，已阅，0未阅
	 * 
	 */
	public void editSubStateAll(){
		TsUserInfo tsUserInfo = this.sessionData.getUser();
		editAllsql=editAllsql.replace(" $$ ", " AND T1.ACCEPT_STATE IN (0) ");//查询未阅的
		editAllsql=" SELECT X.RID FROM ("+editAllsql+" ) X";
		this.systemModuleService.editAllSubState(editAllsql, this.state, tsUserInfo.getRid(),this.paramMap);
		MsgSendUtil.updateMsgInfo();
		this.searchAction();
		JsfUtil.addSuccessMessage("全部已阅成功！");
	}
	/**
	 * 批量修改状态
	 */
	public void boforeEditSubStatePl() {
		if (null == this.selResultList || this.selResultList.size() == 0) {
			JsfUtil.addErrorMessage("请选择需要批量已阅的数据！");
			return;
		}
		RequestContext context = RequestContext.getCurrentInstance();
		context.execute("PF('ConfirmDialog').show();");
	}
	/**
	 * 批量修改状态
	 */
	public void editSubStatePl() {
		TsUserInfo tsUserInfo = this.sessionData.getUser();
		if (null != this.selResultList && this.selResultList.size() > 0) {
			String ids = "";
			for (Object[] t : this.selResultList) {
				if (!"2".equals(t[2])) {
					ids += "," + t[0];
				}
			}
			if (StringUtils.isNotBlank(ids)) {
				ids = ids.substring(1);
				this.systemModuleService.editSubState(ids, this.state, tsUserInfo.getRid());
			}
		}
		MsgSendUtil.updateMsgInfo();
		RequestContext context = RequestContext.getCurrentInstance();
		context.execute("PF('ConfirmDialog').hide();");
		JsfUtil.addSuccessMessage("批量已阅成功！");
		this.searchAction();
	}

	/**
	 * 批量删除信息
	 */
	public void delSubStatePl() {
		if (null != this.selResultList && this.selResultList.size() > 0) {
			String ids = "";
			for (Object[] t : this.selResultList) {
				ids += "," + t[7];
			}
			if (StringUtils.isNotBlank(ids)) {
				ids = ids.substring(1);
				this.systemModuleService.deleteMsgPl(ids);
			}
		}
		MsgSendUtil.updateMsgInfo();
		this.searchAction();
	}

	/**
	 * 删除信息
	 */
	public void delSubState() {
		this.systemModuleService.deleteMsgPl(this.rid);
		MsgSendUtil.updateMsgInfo();
		this.searchAction();
	}

	/**
	 * 修改状态
	 */
	public void editSubState() {
		if (null != this.rid) {
			TsUserInfo tsUserInfo = this.sessionData.getUser();
			this.systemModuleService.editSubState(this.rid, this.state, tsUserInfo.getRid());
		}
		MsgSendUtil.updateMsgInfo();
		this.searchAction();
	}

	/**
	 * 打开链接
	 */
	public void openLinkAction() {
		// 消息是否失效，true表示失效
		boolean msgValid = Boolean.TRUE;
		/**
		 * 信息类型只有是流程待办任务的时候，才会去检查taskId对应的任务， taskId为空或者任务不存在，则不跳转，并且提示该消息已经失效！
		 */
		this.tdMsgSub = this.systemModuleService.findMsgSub(Integer.valueOf(this.rid));

		// 判断消息类型,不是待办任务，就不失效
		if (this.tdMsgSub.getTdMsgMain().getMessageType().equals(MessageType.ACTIVITI)
				|| this.tdMsgSub.getTdMsgMain().getMessageType().equals(MessageType.DISPATCH)) {
			String netAdr = this.tdMsgSub.getTdMsgMain().getNetAdr();
			// 待办任务一定要有taskId
			if (StringUtils.isNotBlank(netAdr) && netAdr.indexOf("taskId") != -1) {
				// 获取任务ID
				String taskId = StringUtils.parseUrl(netAdr, "taskId");
				// 待办任务一定要有taskId
				/*if (StringUtils.isNotBlank(taskId)) {
					// 0-节点ID 1-业务表ID 如果为空说明任务已经不存在
					Object[] obs = this.flowBusinessService.findTaskData(taskId);
					if (null != obs && obs.length >= 3) {
						msgValid = Boolean.FALSE;
						// 将待办任务的url完善全，加上节点ID和业务ID，放在taskId前面
						String[] split = netAdr.split("\\?");
						// 将待办任务的url完善全，加上节点ID和业务ID
						StringBuilder newAdr = new StringBuilder(split[0]);
						newAdr.append("?nodeId=").append(obs[0]).append("&businessId=").append(obs[1]);
						newAdr.append("&trustId=").append(this.sessionData.getUser().getRid());
						newAdr.append("&").append(split[1]).append("&msgSubId=").append(this.tdMsgSub.getRid());
						newAdr.append("&read=");
						if ("1".equals(obs[2].toString())) {
							// 待办任务
							newAdr.append("false");
						} else {
							// 历史任务只能只读
							newAdr.append("true");
						}

						this.tdMsgSub.getTdMsgMain().setNetAdr(newAdr.toString());
					}
				}*/
			}
		} else if (this.tdMsgSub.getTdMsgMain().getMessageType().equals(MessageType.EMERG)) {
			String netAdr = this.tdMsgSub.getTdMsgMain().getNetAdr();
			this.tdMsgSub.getTdMsgMain().setNetAdr(
					new StringBuilder(netAdr).append("&msgSubId=").append(this.tdMsgSub.getRid()).toString());
			msgValid = Boolean.FALSE;
		} else {
			String netAdr = this.tdMsgSub.getTdMsgMain().getNetAdr();
			if(!netAdr.contains("?"))	{//如果不带参数则需要增加占位符
				netAdr = new StringBuilder(netAdr).append("?ph=1").toString();
			}
			this.tdMsgSub.getTdMsgMain().setNetAdr(
					new StringBuilder(netAdr).append("&msgSubId=").append(this.tdMsgSub.getRid()).toString());
			msgValid = Boolean.FALSE;
		}

		// 消息失效，提醒
		if (msgValid) {
			JsfUtil.addErrorMessage("该业务数据已删除！");
		} else {
			if (null != this.rid) {
				TsUserInfo tsUserInfo = this.sessionData.getUser();
				this.systemModuleService.editSubState(this.rid,"1", tsUserInfo.getRid());
				MsgSendUtil.updateMsgInfo();
			}
			
			// 否则，打开
			StringBuilder script = new StringBuilder();
			script.append("forwordPage('").append(this.tdMsgSub.getTdMsgMain().getNetName());
			script.append("', '").append(this.tdMsgSub.getTdMsgMain().getNetAdr());
			//打开时传递消息子表状态（0，未阅；1，已阅；2，收藏）
			script.append("&msgSubAcceptState=").append(this.tdMsgSub.getAcceptState()).append("&from=/webapp/system/tdMsgMainSearchList.faces").append("')");
			System.out.println(script.toString());
			RequestContext.getCurrentInstance().execute(script.toString());
		}
	}

	
	public String[] getSearchState() {
		return searchState;
	}

	public void setSearchState(String[] searchState) {
		this.searchState = searchState;
	}

	public String getSearchInfoTitle() {
		return searchInfoTitle;
	}

	public void setSearchInfoTitle(String searchInfoTitle) {
		this.searchInfoTitle = searchInfoTitle;
	}

	public List<Object[]> getSelResultList() {
		return selResultList;
	}

	public void setSelResultList(List<Object[]> selResultList) {
		this.selResultList = selResultList;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getRid() {
		return rid;
	}

	public void setRid(String rid) {
		this.rid = rid;
	}

	public List<SelectItem> getSearchTypeName() {
		return searchTypeName;
	}

	public void setSearchTypeName(List<SelectItem> searchTypeName) {
		this.searchTypeName = searchTypeName;
	}

	public String[] getType() {
		return type;
	}

	public void setType(String[] type) {
		this.type = type;
	}

}