package com.chis.modules.system.web;

import java.io.IOException;
import java.io.OutputStream;
import java.sql.Clob;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.primefaces.context.RequestContext;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.FreeMarkers;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TdFormDef;
import com.chis.modules.system.entity.TdFormField;
import com.chis.modules.system.entity.TdFormTable;
import com.chis.modules.system.enumn.SearchFlag;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.DyncFormServiceImpl;
import com.chis.modules.system.utils.DefaultLazyDataModel;
import com.chis.modules.system.utils.GenHtmlCodeUtil;
import com.chis.modules.system.utils.SqlHandleUtil;
import com.chis.modules.system.utils.UsersUtil;
import com.chis.modules.system.web.FacesSimpleBean;
import com.google.common.collect.Lists;

@ManagedBean(name = "dynaFormListBean")
@ViewScoped
public class DynaFormListBean extends FacesEditBean{
	private DyncFormServiceImpl formService = (DyncFormServiceImpl) SpringContextHolder
			.getBean(DyncFormServiceImpl.class);
	private DyncFormServiceImpl dyncFormServiceImpl = (DyncFormServiceImpl) SpringContextHolder
			.getBean(DyncFormServiceImpl.class);
	private CommServiceImpl commService = SpringContextHolder
			.getBean(CommServiceImpl.class);

	//动态表单编号
	private String formCode;
	//动态表单对象
	private TdFormDef formDef;
	//表格头
	private List<String> titleList;
	//主键
	private Integer rid;
	//查询条件传值
	private String searchJson;
	//上一次查询条件(用来判断是否需要再次查询总数)
	private String preSearchJson;
	//查询列表数据
	private String dataListJson;
	//总数
	private Integer pageCount;
	//跳转到某页
	private Integer page=0;
	//查询字段
	private String searchHtml;
	/**真分页模型*/
	private DefaultLazyDataModel searchDataModel;
	//详情页面
	private String dataJson;
	/**1.单表维护 2.主子表维护*/
	private int formType = 1;
	private String html;
	
	//下载的sql语句
	private String downLoadSql;
	//下载的sql参数
	private Map<String, Object> downLoadParamMap = new HashMap<String, Object>();
	//excel标题
	private String title;
	private String fields;
	//excel导出字段类型
	private String fieldsType;
	//excel
	private String fieldsWidth;
	private String totalFields;
	
	public DynaFormListBean(){
		this.ifSQL = true;
		HttpServletRequest request = JsfUtil.getRequest();
		formCode = request.getParameter("formCode").toString();
		formDef = dyncFormServiceImpl.findTdFormDefByFormCode(formCode);
		if(formDef==null){
			JsfUtil.addErrorMessage("请先配置动态表单数据！");
			return;
		}
		this.searchHtml = formDef.getSearchHtml();
		searchAction();
	}
	
	/**
	 * 查询操作，如果不满足，可重写
	 */ 
	public void searchAction() {
//		String[]hql = this.buildHqls();

		try{
//		String sql = "SELECT T.RID,T.BMXZ,T.ZC FROM TZ_JJRBZB T  WHERE 1=1  AND T.BMXZ = :DYNA_SEARCH_MAIN_BMXZ AND T.ZC >= :DYNA_SEARCH_MAIN_ZC AND T.ZC <= :DYNA_SEARCH_NEXT_MAIN_ZC ORDER BY T.RID";
		String sql = formDef.getSearchSql();
		if(!StringUtils.isNotBlank(searchJson))
			searchJson = "{}";
		System.err.println("【dataJson】：" + searchJson);
		this.paramMap = JSON.parseObject(this.searchJson, Map.class);
		//处理sql和参数
		sql = SqlHandleUtil.buildSql(paramMap, sql);
		//判断和上次查询条件是否相同，不同查询Count
		if(!searchJson.equals(preSearchJson) || this.pageCount == null){
			this.page = 0;
			String countSql = "SELECT COUNT(1) FROM ( "+sql+" )";
			this.pageCount = commService.findTotalNumBySQL(countSql, paramMap);
		}
		//重新赋值
		preSearchJson = searchJson;
		downLoadSql=sql;
		downLoadParamMap=this.paramMap;
		List<Object[]> datas = commService.findDataBySQL(sql, paramMap, this.page, this.pageSize);
		this.dataListJson = JSON.toJSONString(datas);
		}catch(Exception e){
			JsfUtil.addErrorMessage("SQL维护异常！");
			e.printStackTrace();
		}
//		this.searchDataModel = new DefaultLazyDataModel(hql[0], hql[1], 
//				this.paramMap, this.buildProcessData(), "tabView:mainForm:dataTable", this.ifSQL);
    }
	public void dynaSearchData(){
		RequestContext context = RequestContext.getCurrentInstance();
		String userName = JsfUtil.getRequestParameter("DYNA_SEARCH_MAIN_WHRXM");
		String whdw = JsfUtil.getRequestParameter("DYNA_SEARCH_MAIN_WHDW");
		String jjjg = JsfUtil.getRequestParameter("DYNA_SEARCH_MAIN_JJJG");
		String wtdx = JsfUtil.getRequestParameter("DYNA_SEARCH_MAIN_WTDX");
		String whrq = JsfUtil.getRequestParameter("DYNA_SEARCH_MAIN_WHRQ");
		String whnextrq = JsfUtil.getRequestParameter("DYNA_SEARCH_NEXT_MAIN_WHRQ");
		String whxs = JsfUtil.getRequestParameter("DYNA_SEARCH_MAIN_WHCXSJ");
		String off = JsfUtil.getRequestParameter("BS/CS");
		float sum = commService.findTotalTime(userName,whdw,jjjg,wtdx,whrq,whnextrq,whxs,off);
		context.addCallbackParam("rst", JSON.toJSONString(sum));
	}
	@Override
	public String[] buildHqls() {
		/*System.err.println("【dataJson】：" + searchJson);
		this.paramMap = JSON.parseObject(this.searchJson, Map.class);
		titleList = Lists.newArrayList();
		String[] sqls = new String[2];
		StringBuilder fieldsSql = new StringBuilder("SELECT T.RID");
		StringBuilder tempSql = new StringBuilder("");
		StringBuilder countSql = new StringBuilder("SELECT COUNT(1) ");
		String orderStr = " ORDER BY T.RID";
		StringBuilder sql = new StringBuilder();
		TdFormTable formTable = formDef.getTdFormTableByTableId();
		//1单表2主表3子表
//		if(1==formTable.getFormProp()){
		List<TdFormField> fields = formTable.getFieldList();
		for(TdFormField field:fields){
			if(1!=field.getIsList())
				continue;
			titleList.add(field.getFdCnname());
			tempSql.append(",T.").append(field.getFdEnname());
		}
		sql.append(" FROM ").append(formTable.getEnName()).append(" T ");
		StringBuilder searchSql = new StringBuilder(" WHERE 1=1 ");
		if(this.paramMap!=null){
			for(TdFormField field:searchFieldList){
				//获取查询条件，如果是null的情况下跳过
				String key = GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB+"_"+field.getFdEnname();
				Object obj = paramMap.get(key);
				if(SearchFlag.BETWEEN.getFlag().equals(field.getSearchType())){
					String key2 = GenHtmlCodeUtil.DYNC_PREFIX_NEXT_MAIN_SEARCH_TAB+"_"+field.getFdEnname();
					Object obj2 = paramMap.get(key2);
					if((obj==null || !StringUtils.isNotBlank(obj.toString())) &&
							(obj2==null || !StringUtils.isNotBlank(obj2.toString()))){
						paramMap.remove(key);
						paramMap.remove(key2);
						continue;
					}else if(obj==null || !StringUtils.isNotBlank(obj.toString())){
						paramMap.remove(key);
					}else if(obj2==null || !StringUtils.isNotBlank(obj2.toString())){
						paramMap.remove(key2);
					}
				}else if(obj==null || !StringUtils.isNotBlank(obj.toString())){
					paramMap.remove(key);
					continue;
				}
				searchSql.append(buildSearchSqlFilters(field,paramMap));
			}
		}else{
			this.paramMap = new HashMap<String, Object>();
		}
		sqls[0] = fieldsSql.append(tempSql.toString()).append(sql.toString()).append(searchSql.toString()).append(orderStr).toString();
		sqls[1] = countSql.append(sql.toString()).append(searchSql.toString()).toString();
//		}else if(2==formTable.getFormProp()){
//			//主表字段
//			List<TdFormField> fields = formTable.getFieldList();
//			for(TdFormField field:fields){
//				if(1!=field.getIsList())
//					continue;
//				titleList.add(field.getFdCnname());
//				tempSql.append(",T.").append(field.getFdEnname());
//			}
//			//子表字段
//			TdFormTable subTable = formTable.getChildList().get(0);
//			List<TdFormField> subfields = subTable.getFieldList();
//			for(TdFormField field:subfields){
//				if(1!=field.getIsList())
//					continue;
//				titleList.add(field.getFdCnname());
//				tempSql.append(",T1.").append(field.getFdEnname());
//			}
//			sql.append(" FROM ").append(formTable.getEnName()).append(" T LEFT JOIN ").append(subTable.getEnName()).append(" T1 ON T.RID = T1.MAIN_ID ");
//			sqls[0] = fieldsSql.append(tempSql.substring(1)).append(sql.toString()).toString();
//			sqls[1] = countSql.append(sql.toString()).toString();
//		}
		return sqls;*/
		return null;
	}

	/**
	 * 根据不同的查询符号插入查询语句
	 * @param field
	 * @param key 
	 * @param paramMap 
	 * @param key 
	 * @param paramMap 
	 */
	private String buildSearchSqlFilters(TdFormField field, Map<String, Object> paramMap) {
		String key = GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB+"_"+field.getFdEnname();
		StringBuilder sb = new StringBuilder();
		if(field.getSearchType()==(SearchFlag.GT.getFlag())){
			sb.append(" AND T.").append(field.getFdEnname()).append(SearchFlag.GT.getRealFlag()).append(":")
				.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
		}else if(field.getSearchType()==(SearchFlag.EGT.getFlag())){
			sb.append(" AND T.").append(field.getFdEnname()).append(SearchFlag.EGT.getRealFlag()).append(":")
				.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
		}else if(field.getSearchType()==(SearchFlag.LT.getFlag())){
			sb.append(" AND T.").append(field.getFdEnname()).append(SearchFlag.LT.getRealFlag()).append(":")
				.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
		}else if(field.getSearchType()==(SearchFlag.ELT.getFlag())){
			sb.append(" AND T.").append(field.getFdEnname()).append(SearchFlag.ELT.getRealFlag()).append(":")
				.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
		}else if(field.getSearchType()==(SearchFlag.LIKE.getFlag())){
			sb.append(" AND T.").append(field.getFdEnname()).append(SearchFlag.LIKE.getRealFlag()).append(":")
				.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
			String str = paramMap.get(key).toString();
			str = "%"+str+"%";
			paramMap.put(key, str);
		}else if(field.getSearchType()==(SearchFlag.BETWEEN.getFlag())){
			String key2 = GenHtmlCodeUtil.DYNC_PREFIX_NEXT_MAIN_SEARCH_TAB+"_"+field.getFdEnname();
			Object obj = paramMap.get(key);
			Object obj2 = paramMap.get(key2);
			if(obj!=null && StringUtils.isNotBlank(obj.toString()))
				sb.append(" AND T.").append(field.getFdEnname()).append(SearchFlag.EGT.getRealFlag()).append(":")
				.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
			if(obj2!=null && StringUtils.isNotBlank(obj2.toString()))
				sb.append(" AND T.").append(field.getFdEnname()).append(SearchFlag.ELT.getRealFlag())
				.append(":").append(GenHtmlCodeUtil.DYNC_PREFIX_NEXT_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
		}else{
			sb.append(" AND T.").append(field.getFdEnname()).append(SearchFlag.EQ.getRealFlag()).append(":")
			.append(GenHtmlCodeUtil.DYNC_PREFIX_MAIN_SEARCH_TAB).append("_").append(field.getFdEnname());
		}
		return sb.toString();
	}
	
	
	/**
	 * 构建动态组件
	 */
	private void buildHtml() {
		Map<String, Object> varMap = new HashMap<String, Object>();
		varMap.put("rows", this.formDef.getRows());
		this.html = FreeMarkers.renderString(this.formDef.getHtml(), varMap);
		this.dataJson = JSON.toJSONString(this.formDef.getValueMap());
	}
	

	@Override
	public void addInit() {
		
	}

	@Override
	public void viewInit() {
		this.formDef = this.formService.findFormDef(formDef.getRid(), UsersUtil.generateCommonUtilMap(),this.rid); 
		this.formType = this.formDef.getTdFormTableByTableId().getFormProp();
		this.buildHtml();
	}

	@Override
	public void modInit() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void saveAction() {
		// TODO Auto-generated method stub
		
	}
	//获得下载的标题和
	public void getDownLoadParam(){
		RequestContext context = RequestContext.getCurrentInstance();
		title =JsfUtil.getRequestParameter("title");
		fields = JsfUtil.getRequestParameter("fields");
		fieldsType=JsfUtil.getRequestParameter("fieldsType");
		fieldsWidth=JsfUtil.getRequestParameter("fieldsWidth");
		totalFields=JsfUtil.getRequestParameter("totalFields");
		
	}
	
	//导出表格数据
	
	public void dynaDownLoadData(){
		String[] fieldsName=StringUtils.isNotBlank(fields)?fields.split(","):null;
		String[] fieldsTypes=StringUtils.isNotBlank(fieldsType)?fieldsType.split(","):null;
		String[] fieldsWid=StringUtils.isNotBlank(fieldsWidth)?fieldsWidth.split(","):null;
		String[] totalFds=StringUtils.isNotBlank(totalFields)?totalFields.split(","):null;
		List<Object[]> dataList = commService.findDataBySqlNoPage(downLoadSql, downLoadParamMap);
		preProcessXLS(title,fieldsName,fieldsTypes,fieldsWid,dataList,totalFds);
	}
	
	/**
	 * 导出前执行方法
	 * 
	 * @param document
	 */
	public void preProcessXLS(String title,String[] fieldsName,String[] fieldsType,String[] fieldsWidth,List<Object[]> dataList,String[] totalFds) {
		HSSFWorkbook wb = new HSSFWorkbook();
		HSSFSheet sheet = wb.createSheet(title);
		int rows = 1;
		// 单元格样式一
		HSSFFont font = wb.createFont();
		font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
		HSSFCellStyle cellStyle = wb.createCellStyle();
		cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平居中
		cellStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直居中
		cellStyle.setFont(font);
		// 单元格样式二
		HSSFFont font2 = wb.createFont();
		font2.setBoldweight(HSSFFont.BOLDWEIGHT_NORMAL);
		HSSFCellStyle cellStyle2 = wb.createCellStyle();
		cellStyle2.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平居中
		cellStyle2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直居中
		cellStyle2.setFont(font2);
		// 单元格样式三
		HSSFFont font3 = wb.createFont();
		HSSFCellStyle cellStyle3 = wb.createCellStyle();
		cellStyle3.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 水平居中
		cellStyle3.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直居中
		cellStyle3.setFont(font3);

		int cols = fieldsName.length;
		HSSFRow row1 = sheet.createRow(0);
		// 开始行，结束行，开始列，结束列
		CellRangeAddress firstRegion = new CellRangeAddress(0, 0, 0, cols-1);
		HSSFCell cell = row1.createCell(0);
		cell.setCellValue(title);
		cell.setCellStyle(cellStyle);
		sheet.addMergedRegion(firstRegion);

		// 表列名称
		HSSFRow row2 = sheet.createRow(rows++);
		if(fieldsName!=null && fieldsName.length>0){
			for(int i=0;i<fieldsName.length;i++){
				HSSFCell cell1 = row2.createCell(i);
				cell1.setCellValue(fieldsName[i]);
				cell1.setCellStyle(cellStyle2);
			}
		}
		//表格数据(第一个为rid，不取)
		if(dataList!=null && dataList.size()>0){
			for(Object[] objs:dataList){
				HSSFRow row = sheet.createRow(rows++);
				for(int i=0;i<fieldsName.length;i++){
					HSSFCell cell1 = row.createCell(i);
					cell1.setCellStyle(cellStyle3);
					if(fieldsType!=null){
						String type=fieldsType[i];
						if("Clob".equals(type)){
							Clob c=(Clob) objs[i+1];
							try {
								cell1.setCellValue(c!=null?c.getSubString((long) 1, (int) c.length()):null);
							} catch (SQLException e) {
								e.printStackTrace();
							}
						}else{//String
							cell1.setCellValue(objs[i+1]!=null?objs[i+1].toString():null);
						}
					}else{
						cell1.setCellValue(objs[i+1]!=null?objs[i+1].toString():null);
					}
				}
			}
		}
		if(fieldsWidth!=null && fieldsWidth.length>0){
			for(int i=0;i<fieldsWidth.length;i++){
				sheet.setColumnWidth(i, Integer.valueOf(fieldsWidth[i]));
			}
		}else{
			if(fieldsName!=null && fieldsName.length>0){
				for(int i=0;i<fieldsName.length;i++){
					sheet.autoSizeColumn(i);
				}
			}
		}
		if (totalFds!=null && totalFds.length>0) {
			HSSFFont font0 = wb.createFont();
			HSSFCellStyle cellStyle0 = wb.createCellStyle();
			cellStyle0.setAlignment(HSSFCellStyle.ALIGN_LEFT);// 水平居中
			cellStyle0.setVerticalAlignment(HSSFCellStyle.ALIGN_LEFT);
			cellStyle0.setFont(font0);
			int row = rows++;
			CellRangeAddress region = new CellRangeAddress(row, row, 0, cols-1);
			HSSFRow lastrow = sheet.createRow(row);
			HSSFCell cell0 = lastrow.createCell(0);
			cell0.setCellValue("维护总持续时间（小时）："+totalFds[0]);
			cell0.setCellStyle(cellStyle0);
			sheet.addMergedRegion(region);
			sheet.setColumnWidth(0, 2000);
		}
	//输出Excel文件  
		OutputStream output=null;
		try {
			HttpServletResponse response = JsfUtil.getResponse();
		    output = response.getOutputStream();
			String fileName = new String((title+".xls").getBytes("GBK"),"ISO-8859-1");
			response.setContentType("application/vnd.ms-excel");
			response.setHeader("Content-disposition", "attachment;filename="+ fileName);
			output = response.getOutputStream();
			wb.write(output);
			output.flush();
			output.close();
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("下载失败！");
		} finally {
			if (output != null) {
				try {
					output.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		FacesContext.getCurrentInstance().responseComplete();
	}
	
	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}
	public List<String> getTitleList() {
		return titleList;
	}

	public void setTitleList(List<String> titleList) {
		this.titleList = titleList;
	}

	public String getSearchJson() {
		return searchJson;
	}

	public void setSearchJson(String searchJson) {
		this.searchJson = searchJson;
	}

	public DefaultLazyDataModel getSearchDataModel() {
		return searchDataModel;
	}

	public void setSearchDataModel(DefaultLazyDataModel searchDataModel) {
		this.searchDataModel = searchDataModel;
	}

	public TdFormDef getFormDef() {
		return formDef;
	}

	public void setFormDef(TdFormDef formDef) {
		this.formDef = formDef;
	}

	public String getDataJson() {
		return dataJson;
	}

	public void setDataJson(String dataJson) {
		this.dataJson = dataJson;
	}

	public int getFormType() {
		return formType;
	}

	public void setFormType(int formType) {
		this.formType = formType;
	}

	public String getHtml() {
		return html;
	}

	public void setHtml(String html) {
		this.html = html;
	}

	public String getTotalFields() {
		return totalFields;
	}

	public void setTotalFields(String totalFields) {
		this.totalFields = totalFields;
	}

	public String getDataListJson() {
		return dataListJson;
	}

	public void setDataListJson(String dataListJson) {
		this.dataListJson = dataListJson;
	}

	public Integer getPageCount() {
		return pageCount;
	}

	public void setPageCount(Integer pageCount) {
		this.pageCount = pageCount;
	}

	public Integer getPage() {
		return page;
	}

	public void setPage(Integer page) {
		this.page = page;
	}

	public String getSearchHtml() {
		return searchHtml;
	}

	public void setSearchHtml(String searchHtml) {
		this.searchHtml = searchHtml;
	}	
}
