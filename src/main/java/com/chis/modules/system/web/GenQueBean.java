package com.chis.modules.system.web;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;

import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.ObjectCopyUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TbProPoolColsdefine;
import com.chis.modules.system.entity.TbProPoolRowtitle;
import com.chis.modules.system.entity.TbProbColsdefine;
import com.chis.modules.system.entity.TbProbRowtitle;
import com.chis.modules.system.entity.TbProbTabdefine;
import com.chis.modules.system.entity.TsProOpt;
import com.chis.modules.system.entity.TsProPoolOpt;
import com.chis.modules.system.entity.TsProbExampool;
import com.chis.modules.system.entity.TsProbExamtype;
import com.chis.modules.system.entity.TsProbLib;
import com.chis.modules.system.entity.TsProbSubject;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.enumn.QuestType;
import com.chis.modules.system.enumn.SystemParamType;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.FillMaxRangeBean;
import com.chis.modules.system.logic.ProbLibThemeBean;
import com.chis.modules.system.logic.ProbSubOrderBean;
import com.chis.modules.system.logic.QueSubTypeBean;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.queProducer.ProduceQuestPage;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * 模块描述：问卷生成管理
 * 
 * <AUTHOR>
 * @createDate
 * 
 *             <p>
 *             修订内容：优化问卷生成，增加标题依赖方式维护
 *             </p>
 * 
 * @ClassReviser rj,2017年11月8日,GenQueBean
 * 
 */
@ManagedBean
@ViewScoped
public class GenQueBean extends FacesEditBean {
	private static final long serialVersionUID = 8814041318248638582L;
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private SystemModuleServiceImpl systemModuleServiceImpl = SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commServiceImpl = SpringContextHolder.getBean(CommServiceImpl.class);
	private String searchQueName;
	/** 查询条件：问卷类别 */
	private Integer searchTypeId;
	private List<SelectItem> typeList = new ArrayList<SelectItem>();
	private Map<Integer, TsSimpleCode> typeMap = new HashMap<>();
	private String[] searchState = new String[] { "1" };
	/** 问卷类别编码 */
	private static String QUE_TYPE_NO = "13001";
	private Integer rid;
	private TsProbLib lib;
	/** 是否超管 */
	private boolean ifAdmin;
	private Integer ifEdit;
	/** 单位下拉 */
	private List<SelectItem> unitList;
	/** 主题列表 */
	private List<ProbLibThemeBean> themeList;
	private Integer selectedUnitID;
	private Integer selectedQueTypeID;
	private Integer state;
	/** 题目 */
	private TsProbSubject sub = new TsProbSubject();
	/** 选中皮肤主题 */
	private String selectedTheme;
	private String htmlName;
	/** 选项 */
	private TsProOpt opt = new TsProOpt();
	/** 选项图片标记 0：附加图片；1：描述图片;2：题目附加图片； */
	private Integer uploadTag = 0;
	/** 题目类型筛选 */
	private List<QueSubTypeBean> subTypeOptions;
	/** 题目类别 */
	private List<TsProbExamtype> examtypes;
	/** 题目展示树 */
	private TreeNode root = new DefaultTreeNode("root", null);
	/** 当前问卷题目列表 */
	private List<TsProbSubject> bindSubList;
	/** 选项跳转题目选择列表 */
	private List<TsProbSubject> jumpOptions = new ArrayList<>(0);
	/** 标题类型题目依赖选择列表 */
	private List<TsProbSubject> relOptions = new ArrayList<>(0);
	/** 依赖题目选项 */
	private List<SelectItem> relQueOpts = new ArrayList<>();
	/** 排序列表 */
	private List<ProbSubOrderBean> orderList = new ArrayList<>(0);
	/** */
	private List<FillMaxRangeBean> ranges;
	/** 行标题 */
	private TbProbRowtitle rowtitle;
	/** 列定义 */
	private TbProbColsdefine colsdefine;
	/** 系统类型下拉 */
	private Map<String, String> systemTypeMap = new LinkedHashMap<String, String>();
	/** 选中的系统类型 */
	private Short systemTypeEdit;
	/**
	 * 1：web2：mobile
	 */
	private Integer previewTag = 1;

	/**
	 * 进入页面初始化
	 */
	@PostConstruct
	public void init() {
		typeList = new ArrayList<SelectItem>();
		List<TsSimpleCode> simList = new ArrayList<TsSimpleCode>(0);
		if (sessionData.getUser().getUserNo().equals(Constants.ADMIN)) {
			simList = this.commServiceImpl.findLevelSimpleCodesByTypeId(QUE_TYPE_NO);
		} else {
			simList = this.systemModuleServiceImpl.getTsProbExamtypeByUserId(sessionData.getUser().getRid());
		}
		typeMap = new HashMap<>();
		if (null != simList) {
			for (TsSimpleCode tsCode : simList) {
				typeList.add(new SelectItem(tsCode.getRid(), tsCode.getCodeName()));
				typeMap.put(tsCode.getRid(), tsCode);
			}
		}
		if (sessionData.getUser().getUserNo().equals(Constants.ADMIN)) {
			ifAdmin = true;
			StringBuilder sb = new StringBuilder();
			sb.append("select rid,unitname from ts_unit");
			List<Object[]> result = commServiceImpl.findDataBySqlNoPage(sb.toString(), null);
			unitList = new ArrayList<>();
			for (Object[] obj : result) {
				SelectItem item = new SelectItem(Integer.valueOf(obj[0].toString()), obj[1].toString());
				unitList.add(item);
			}
		}
		//初始化系统类型
		SystemType[] types = SystemType.values();
		for (SystemType st : types) {
			this.systemTypeMap.put(st.getTypeCN(), st.getTypeNo().toString());
		}
		initThemeList();
		searchAction();
	}

	/**
	 * 初始化背景列表
	 */
	private void initThemeList() {
		String path = GenQueBean.class.getClassLoader().getResource("").getPath()
				.replace("WEB-INF/classes/", "resources/images/probLib/thumb");
		File file = new File(path);
		themeList = new ArrayList<>();
		if (file.exists() && file.list().length > 0) {
			for (String str : file.list()) {
				ProbLibThemeBean theme = new ProbLibThemeBean();
				theme.setThumbPath("/resources/images/probLib/thumb/" + str);
				theme.setValue(str.substring(str.lastIndexOf("_") + 1, str.lastIndexOf(".")));
				themeList.add(theme);
			}
		}
	}

	/** 背景选择事件 */
	public void onThemeSelected(String value) {
		updateThemeList(value);
	}

	/** 背景选择初始化 */
	public void themeSelectedInit() {
		updateThemeList(this.lib.getBackImage());
	}

	/**
	 * 主题选择确定
	 */
	public void themeSelectedConfirm() {
		for (ProbLibThemeBean theme : themeList) {
			if (theme.isSelected()) {
				this.lib.setBackImage(theme.getValue());
			}
		}
	}

	/**
	 * 更新主题列表
	 * 
	 * @param value
	 */
	private void updateThemeList(String value) {
		for (ProbLibThemeBean theme : themeList) {
			theme.setSelected(false);
		}
		if (StringUtils.isNoneBlank(value)) {
			for (ProbLibThemeBean theme : themeList) {
				if (theme.getValue().equals(value))
					theme.setSelected(true);
			}
		}
	}

	/**
	 * 添加标题初始化
	 */
	public void titleAddInit() {
		sub = new TsProbSubject();
		sub.setQuestType(QuestType.TITLE);
		sub.setCreateDate(new Date());
		sub.setCreateManid(sessionData.getUser().getRid());
		sub.setMustAsk(0);
		sub.setState(1);
		sub.setNum(createNum());
		sub.setTsProbLibByQuestlibId(lib);
		sub.setQesCode(UUID.randomUUID().toString());
		sub.setQesLevelCode(sub.getQesCode());
		RequestContext.getCurrentInstance().update("tabView:editForm:titleAddDialog");
		RequestContext.getCurrentInstance().execute("PF('TitleAddDialog').show()");
	}

	/**
	 * 标题保存
	 */
	public void titleSaveAction() {
		if (beforeSubSaveAction()) {
			bindSubList.add(sub);
			subSort(bindSubList);
			initSubList();
			RequestContext.getCurrentInstance().update("tabView:editForm:subjectInfoTable");
			RequestContext.getCurrentInstance().execute("PF('TitleAddDialog').hide()");
		}
	}

	@Override
	public void addInit() {
		lib = new TsProbLib();
		lib.setTsSimpleCodeByQuestSortid(new TsSimpleCode());
		lib.setCreateManid(sessionData.getUser().getRid());
		lib.setState(1);
		lib.setHtmlName(UUID.randomUUID().toString());
		selectedQueTypeID = null;
		selectedUnitID = null;
		if (!ifAdmin) {
			lib.setTsUnitByUnitId(sessionData.getUser().getTsUnit());
		}
		lib.setBackImage(this.themeList.get(0).getValue());
		updateThemeList(lib.getBackImage());
		this.bindSubList = new ArrayList<>();
		initSubList();
		systemTypeEdit = SystemType.COMM.getTypeNo();
	}

	/** 题库选择初始化 */
	public void subAddInitAction() {
		Map<String, Object> options = new HashMap<String, Object>();
		options.put("modal", true);
		options.put("draggable", true);
		options.put("resizable", false);
		options.put("contentWidth", 900);
		options.put("contentHeight", 500);
		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		RequestContext.getCurrentInstance().openDialog("selectQuestionList", options, paramMap);
	}

	/**
	 * 题目选择返回事件
	 * 
	 * @param event
	 * 
	 *            修订内容：修改题目赋值方式
	 * 
	 * @MethodReviser rj,2017年11月9日,onSubSelect
	 * 
	 */
	public void onSubSelect(SelectEvent event) {
		Map<String, List<TsProbExampool>> selectedMap = (Map<String, List<TsProbExampool>>) event.getObject();
		if (selectedMap != null) {
			List<TsProbExampool> selectedList = selectedMap.get("selectedList");
			if (selectedList != null && selectedList.size() > 0) {
				outer: for (TsProbExampool pool : selectedList) {
					for (TsProbSubject temp : bindSubList) {
						if (temp.getState() == 1 && temp.getQesCode().equals(pool.getQesCode())) {
							continue outer;
						}
					}
					try {
						TsProbSubject sub = new TsProbSubject();
						sub.setPool(pool);
						sub.setNum(createNum());
						sub.setTsProbLibByQuestlibId(lib);
						sub.setState(1);
						sub.setCreateDate(new Date());
						sub.setCreateManid(sessionData.getUser().getRid());
						sub.setSlideMaxvalDesc(pool.getSlideMaxDesc());
						sub.setSlideMinvalDesc(pool.getSlideMinDesc());
						ObjectCopyUtil.copyPropertiesInclude(pool, sub, new String[] { "invokeScrt", "jumpQuestCode",
								"jumpType", "minSelectNum", "mustAsk", "optLayout", "otherDesc", "otherImg", "qesCode",
								"qesLevelCode", "questType", "slideMaxval", "slideMinval",
								"titleDesc", "isMulti", "fillMaxRange" });
						this.bindSubList.add(sub);
						for (TsProPoolOpt poolOpt : pool.getTsProPoolOpts()) {
							if (poolOpt.getState().intValue() == 0) {
								continue;
							}
							TsProOpt opt = new TsProOpt();
							opt.setTsProbSubjectByQuestId(sub);
							opt.setCreateDate(new Date());
							opt.setCreateManid(sessionData.getUser().getRid());
							ObjectCopyUtil.copyPropertiesExclude(poolOpt, opt, new String[] { "createDate",
									"createManid", "tsProbSubjectByQuestId", "optionScore", "rid" });
							sub.getProOptList().add(opt);
						}
						if (pool.getFkByTableId() != null) {

							sub.setFkByTableId(new TbProbTabdefine());
							ObjectCopyUtil.copyProperties(pool.getFkByTableId(), sub.getFkByTableId());
							sub.getFkByTableId().setCreateDate(new Date());
							sub.getFkByTableId().setCreateManid(sessionData.getUser().getRid());
							sub.getFkByTableId().setRid(null);
							sub.getFkByTableId().setRowtitles(new ArrayList<TbProbRowtitle>(0));
							sub.getFkByTableId().setColsdefines(new ArrayList<TbProbColsdefine>(0));
							// 复制行标题
							for (TbProPoolRowtitle poolRowtitle : pool.getFkByTableId().getRowtitles()) {
								TbProbRowtitle rowtitle = new TbProbRowtitle();
								ObjectCopyUtil.copyPropertiesExclude(poolRowtitle, rowtitle, new String[] { "rid",
										"fkByTableId" });
								rowtitle.setCreateDate(new Date());
								rowtitle.setCreateManid(sessionData.getUser().getRid());
								rowtitle.setFkByTableId(sub.getFkByTableId());
								sub.getFkByTableId().getRowtitles().add(rowtitle);
							}
							// 复制列定义
							for (TbProPoolColsdefine poolColsdefine : pool.getFkByTableId().getColsdefines()) {
								TbProbColsdefine colsdefine = new TbProbColsdefine();
								ObjectCopyUtil.copyPropertiesExclude(poolColsdefine, colsdefine, new String[] { "rid",
										"fkByTableId" });
								colsdefine.setCreateDate(new Date());
								colsdefine.setCreateManid(sessionData.getUser().getRid());
								colsdefine.setFkByTableId(sub.getFkByTableId());
								sub.getFkByTableId().getColsdefines().add(colsdefine);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
			this.initSubList();
		}
	}

	/**
	 * 生成序号
	 * 
	 * @return
	 */
	private Integer createNum() {
		Integer count = 0;
		for (TsProbSubject sub : bindSubList) {
			if (sub.getState() == 1 && sub.getNum() != null && sub.getNum() > count) {
				count = sub.getNum();
			}
		}
		return count++;
	}

	/**
	 * 题目详情修改
	 * 
	 * 修订内容：编辑标题时增加初始化可选择依赖的题目下拉列表
	 * 
	 * @MethodReviser rj,2017年11月9日,subModInitAction
	 * 
	 */
	public void subModInitAction() {
		sub.setQuestTypeValue(sub.getQuestType().getTypeNo());
		initFillMaxRange();
		if (sub.getProOptList() != null && sub.getProOptList().size() > 0) {
			this.jumpOptions = new ArrayList<>();
			String filterCode = sub.getQesLevelCode();
			if (filterCode.contains(".")) {
				filterCode = filterCode.substring(0, filterCode.indexOf("."));
			}
			for (TsProbSubject temp : bindSubList) {
				if (temp.getState().intValue() == 0
						|| temp.getQesLevelCode().contains(filterCode)
						|| temp.getQesLevelCode().contains(".")
						|| (temp.getNum() != null && sub.getNum() != null && (temp.getNum().intValue() < sub.getNum()
								.intValue()))) {
					continue;
				}
				jumpOptions.add(temp);
			}
			optSort(sub.getProOptList());
		}
		// 当前题型标题,初始化序号小于当前题目的为依赖题目下拉
		if (sub.getQuestType().equals(QuestType.TITLE)) {
			this.relOptions = new ArrayList<>();
			for (TsProbSubject temp : bindSubList) {
				if (temp.getState().intValue() == 0
						|| temp.getQesLevelCode().contains(".")
						|| !(temp.getQuestType().equals(QuestType.SELECT_ONE)
								|| temp.getQuestType().equals(QuestType.SELECT_MANY) || temp.getQuestType().equals(
								QuestType.TRUE_OR_FALSES))
						|| (temp.getNum() != null && sub.getNum() != null && (temp.getNum().intValue() > sub.getNum()
								.intValue()))) {
					continue;
				}
				relOptions.add(temp);
			}
			if (StringUtils.isNotBlank(sub.getJumpQuestCode())) {
				String[] arr = sub.getJumpQuestCode().split(",");
				for (TsProbSubject tempSub : relOptions) {
					if (tempSub.getNum() != null && tempSub.getNum().toString().equals(arr[0])) {
						sub.setRelQueCode(tempSub.getQesCode());
					}
				}
				sub.setRelQueOptNums(Arrays.asList(arr[1].split("@")));
			}
		}
		this.forwardOtherPage();
	}

	/**
	 * 
	 * <p>
	 * 方法描述：依赖题目变更事件
	 * </p>
	 * 
	 * @MethodAuthor rj,2017年11月9日,onRelQueCodeChange
	 */
	public void onRelQueCodeChange() {
		relQueOpts.clear();
		sub.setRelQueOptNums(new ArrayList<String>(0));
		if (StringUtils.isNotBlank(sub.getRelQueCode())) {
			for (TsProbSubject temp : relOptions) {
				if (temp.getQesCode().equals(sub.getRelQueCode())) {
					for (TsProOpt opt : temp.getProOptList()) {
						relQueOpts.add(new SelectItem(opt.getNum(), opt.getOptionDesc()));
					}
				}
			}
		}
	}

	/**
	 * 初始化填空值范围
	 */
	private void initFillMaxRange() {
		if (StringUtils.isNotBlank(sub.getFillMaxRange())) {
			this.ranges = new ArrayList<>();
			String[] rangeArr = sub.getFillMaxRange().split(";");
			for (String str : rangeArr) {
				FillMaxRangeBean range = new FillMaxRangeBean();
				String index = str.substring(0, str.indexOf(","));
				if (index.contains("_")) {
					String[] arrs = index.split("_");
					range.setOptNum(new Integer(arrs[0]));
					range.setNum(new Integer(arrs[1]));
				} else {
					range.setNum(new Integer(index));
				}
				if (!"~".equals(str.charAt(str.indexOf(",") + 1))) {
					range.setFillMin(str.substring(str.indexOf(",") + 1, str.indexOf("~")));
				}
				if (str.length() - 1 > str.indexOf("~")) {
					range.setFillMax(str.substring(str.indexOf("~") + 1));
				}
				ranges.add(range);
			}
		} else {
			this.ranges = null;
		}
	}

	@Override
	public void viewInit() {

	}

	@Override
	public void modInit() {
		this.lib = this.systemModuleServiceImpl.findTsProbLib(rid);
		this.selectedUnitID = lib.getTsUnitByUnitId() == null ? null : lib.getTsUnitByUnitId().getRid();
		this.selectedQueTypeID = lib.getTsSimpleCodeByQuestSortid().getRid();
		this.bindSubList = lib.getSubjectList();
		systemTypeEdit = lib.getSystemType() == null ? null : lib.getSystemType().getTypeNo();
		updateSubs();
		updateThemeList(lib.getBackImage());
		initSubList();
	}

	/**
	 * 从题库题目更新问卷题目的信息
	 * 
	 * 修订内容：表格题更新重新保存行标题、列定义
	 * 
	 * @MethodReviser rj,2017年12月5日,updateSubs
	 * 
	 */
	private void updateSubs() {
		for (TsProbSubject sub : bindSubList) {
			try {
				if (sub.getPool() != null) {
					/**
					 * 修订内容：修改题目赋值方式
					 * 
					 * @MethodReviser rj,2017年11月9日,updateSubs
					 * 
					 */
					ObjectCopyUtil.copyPropertiesInclude(sub.getPool(), sub,
							new String[] { "titleDesc", "qesCode", "qesLevelCode", "questType", "jumpType", "isMulti",
									"fillMaxRange", "isCorrect", "isMulti" });
					// 更新已有选项
					for (TsProOpt opt2 : sub.getProOptList()) {
						for (TsProPoolOpt op1 : sub.getPool().getTsProPoolOpts()) {
							if (op1.getNum().intValue() == opt2.getNum().intValue()) {
								/**
								 * 修订内容：修改已有选项赋值方式
								 * 
								 * @MethodReviser rj,2017年11月9日,updateSubs
								 * 
								 */
								ObjectCopyUtil.copyPropertiesInclude(op1, opt2, new String[] { "needFill",
										"optionDesc", "optionImg", "optionValue", "otherDesc", "state", "isAlter",
										"isCorrect", "isMulti" });
							}
						}
					}
					for (TsProPoolOpt op1 : sub.getPool().getTsProPoolOpts()) {
						boolean has = false;
						for (TsProOpt op2 : sub.getProOptList()) {
							if (op1.getNum().intValue() == op2.getNum().intValue()) {
								has = true;
							}
						}
						// 新增选项
						if (!has && op1.getState().intValue() != 0) {
							TsProOpt opt = new TsProOpt();
							opt.setCreateDate(new Date());
							opt.setCreateManid(sessionData.getUser().getRid());
							opt.setTsProbSubjectByQuestId(sub);
							/**
							 * 修订内容：修改新增选项赋值方式
							 * 
							 * @MethodReviser rj,2017年11月9日,updateSubs
							 * 
							 */
							ObjectCopyUtil.copyPropertiesExclude(op1, opt, new String[] { "rid", "createDate",
									"createManid", "tsProbSubjectByQuestId", "optionScore" });
							sub.getProOptList().add(opt);
						}
					}
					// 更新表格题
					if (sub.getPool().getFkByTableId() != null) {
						if (sub.getFkByTableId() == null) {
							sub.setFkByTableId(new TbProbTabdefine());
							sub.getFkByTableId().setCreateDate(new Date());
							sub.getFkByTableId().setCreateManid(sessionData.getUser().getRid());
						}
						ObjectCopyUtil.copyPropertiesExclude(sub.getPool().getFkByTableId(), sub.getFkByTableId(),
								new String[] { "rid", "createDate", "createManid", "colsdefines", "rowtitles" });
						for (TbProbRowtitle row: sub.getFkByTableId().getRowtitles()) {
							row.setState(0);
						}
						for (TbProbColsdefine col: sub.getFkByTableId().getColsdefines()) {
							col.setState(0);
						}
						// 新增行标题
						for (TbProPoolRowtitle poolRow : sub.getPool().getFkByTableId().getRowtitles()) {
							TbProbRowtitle row = new TbProbRowtitle();
							row.setFkByTableId(sub.getFkByTableId());
							row.setCreateDate(new Date());
							row.setCreateManid(sessionData.getUser().getRid());
							sub.getFkByTableId().getRowtitles().add(row);
							ObjectCopyUtil.copyPropertiesExclude(poolRow, row, new String[] { "rid", "createDate",
									"createManid", "fkByTableId" });
						}
						// 新增列定义
						for (TbProPoolColsdefine poolCol : sub.getPool().getFkByTableId().getColsdefines()) {
							TbProbColsdefine col = new TbProbColsdefine();
							col.setFkByTableId(sub.getFkByTableId());
							col.setCreateDate(new Date());
							col.setCreateManid(sessionData.getUser().getRid());
							sub.getFkByTableId().getColsdefines().add(col);
							ObjectCopyUtil.copyPropertiesExclude(poolCol, col, new String[] { "rid", "createDate",
									"createManid", "fkByTableId" });
						}
					} else {
						sub.setFkByTableId(null);
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	/** 保存验证 */
	public boolean beforeSavingAction() {
		boolean flag = true;
		if (StringUtils.isBlank(lib.getQuestName())) {
			JsfUtil.addErrorMessage("问卷名称不能为空！");
			flag = false;
		}
		if (selectedQueTypeID == null) {
			JsfUtil.addErrorMessage("请选择问卷类型！");
			flag = false;
		}
		Set<String> errorList = new HashSet<String>();
		for (TsProbSubject sub : bindSubList) {
			if (sub.getState() == 0) {
				continue;
			}
			if (StringUtils.isBlank(sub.getTitleDesc())) {
				JsfUtil.addErrorMessage("题目标题不能为空！");
				flag = false;
			}
			if (sub.getQuestType().getTypeNo() == 2) {
				if (sub.getSlideMaxval() == null) {
					JsfUtil.addErrorMessage("滑动题最大值不能为空！");
					flag = false;
				}
				if (sub.getSlideMinval() == null) {
					JsfUtil.addErrorMessage("滑动题最小值不能为空！");
					flag = false;
				}
				if (StringUtils.isBlank(sub.getSlideMaxvalDesc())) {
					JsfUtil.addErrorMessage("滑动题最大值描述不能为空！");
					flag = false;
				}
				if (StringUtils.isBlank(sub.getSlideMinvalDesc())) {
					JsfUtil.addErrorMessage("滑动题最小值描述不能为空！");
					flag = false;
				}
				if (sub.getSlideMaxval() != null && sub.getSlideMinval() != null) {
					if (sub.getSlideMaxval() < sub.getSlideMinval()) {
						JsfUtil.addErrorMessage("滑动题最大值不能小于滑动题最小值！");
						flag = false;
					}
				}
			} else {
				sub.setSlideMaxval(null);
				sub.setSlideMaxvalDesc(null);
				sub.setSlideMinval(null);
				sub.setSlideMinvalDesc(null);
			}
			if (sub.getProOptList().size() > 0) {
				for (TsProOpt opt : sub.getProOptList()) {
					for (TsProOpt temp : sub.getProOptList()) {
						if (temp != opt && opt.getNum().intValue() == temp.getNum().intValue()) {
							errorList.add("题目【" + sub.getTitleDesc() + "】的选项序号重复！");
						}
					}
				}
			}
		}
		if (errorList.size() > 0) {
			for (String str : errorList) {
				JsfUtil.addErrorMessage(str);
			}
			flag = false;
		}
		return flag;
	}

	@Override
	public void saveAction() {
		if (beforeSavingAction()) {
			if (ifAdmin) {
				if (selectedUnitID != null) {
					lib.setTsUnitByUnitId(new TsUnit(selectedUnitID));
				} else {
					lib.setTsUnitByUnitId(null);
				}
			}
			/**
			 * 脚本处理
			 */
			processJumpCode();
			lib.setSubjectList(bindSubList);
			lib.setTsSimpleCodeByQuestSortid(this.typeMap.get(selectedQueTypeID));
			try {
				lib.setSystemType((SystemType) EnumUtils.findEnum(SystemType.class, systemTypeEdit));
				lib.setCreateDate(new Date());
				for (TsProbSubject sub : lib.getSubjectList()) {
					sub.setCreateDate(new Date());
					for (TsProOpt opt : sub.getProOptList()) {
						opt.setCreateDate(new Date());
					}
				}
				lib = systemModuleServiceImpl.saveOrUpdateProbLib(lib, JsfUtil.getRequest());
				this.bindSubList = lib.getSubjectList();
				subSort(lib.getSubjectList());
				initSubList();
				JsfUtil.addSuccessMessage("保存成功");
			} catch (Exception e) {
				e.printStackTrace();
				JsfUtil.addSuccessMessage("保存失败");
			}
		}
	}

	/**
	 * 处理问卷题目跳转脚本
	 */
	private void processJumpCode() {
		Map<String, Integer> codeMap = new HashMap<>();
		for (TsProbSubject sub : bindSubList) {
			if (sub.getState() == 0) {
				continue;
			}
			codeMap.put(sub.getQesCode(), sub.getNum());
		}
		for (TsProbSubject sub : bindSubList) {
			if (sub.getState() == 0) {
				if(sub.getFkByTableId()!=null&&sub.getFkByTableId().getColsdefines()!=null){
					for (TbProbColsdefine col : sub.getFkByTableId().getColsdefines()) {
//						col.setColName(null);//历史业务数据会根据colName查询组件
					}
				}
				continue;
			}
			if (sub.getProOptList() != null && sub.getProOptList().size() > 0) {
				StringBuilder script = new StringBuilder();
				for (TsProOpt opt : sub.getProOptList()) {
					if (StringUtils.isNotBlank(opt.getJumpQuestCode())) {
						script.append(";").append(codeMap.get(opt.getJumpQuestCode())).append(",")
								.append(opt.getOptionValue());
					}
				}
				if (StringUtils.isNotBlank(script.toString())) {
					sub.setJumpType(1);
					sub.setJumpQuestCode(script.substring(1));
				} else {
					sub.setJumpQuestCode(null);
				}
			}
			/**
			 * 设置跳转预警规则脚本
			 */
			if (sub.getPool() != null) {
				if (StringUtils.isNotBlank(sub.getPool().getInvokeScrt())) {
					sub.setInvokeScrt(sub.getPool().getInvokeScrt());
					/** 将编码替换成序号 */
					String scrt = sub.getInvokeScrt().substring(sub.getInvokeScrt().indexOf("(q") + 2,
							sub.getInvokeScrt().indexOf("(q") + 6);
					sub.setInvokeScrt(sub.getInvokeScrt().replace("q" + scrt,
							codeMap.get(scrt) == null ? ("q" + scrt) : ("q" + codeMap.get(scrt).toString())));
				} else {
					sub.setInvokeScrt(null);
				}
				/**
				 * 修订内容：跳转脚本替换题目编码为题目序号
				 * 
				 * @MethodReviser rj,2017年11月9日,processJumpCode
				 * 
				 */
				if (StringUtils.isNotBlank(sub.getPool().getJumpQuestCode())) {
					String code = sub.getPool().getJumpQuestCode()
							.substring(0, sub.getPool().getJumpQuestCode().indexOf(","));
					if (StringUtils.isNotBlank(code) && codeMap.get(code) != null) {
						sub.setJumpQuestCode(sub.getPool().getJumpQuestCode()
								.replace(code, codeMap.get(code).toString()));
					}
				} else if (sub.getJumpType() == 0) {
					sub.setJumpQuestCode(null);
				}
			}
			/**
			 * 修订内容：生成标题依赖脚本
			 * 
			 * @MethodReviser rj,2017年11月9日,processJumpCode
			 */
			if (sub.getQuestType().equals(QuestType.TITLE) && StringUtils.isNotBlank(sub.getRelQueCode())) {
				StringBuilder scriptStr = new StringBuilder();
				for (String optNum : sub.getRelQueOptNums()) {
					scriptStr.append("@").append(optNum);
				}
				sub.setJumpQuestCode(codeMap.get(sub.getRelQueCode()) + ","
						+ scriptStr.toString().replaceFirst("@", ""));
			} else if (sub.getQuestType().equals(QuestType.TITLE)
					&& (sub.getJumpType() == null || sub.getJumpType() == 0)) {
				sub.setJumpQuestCode(null);
			}
			/**
			 * 修订内容：生成表格题列标识
			 * 
			 * @MethodReviser rj,2017年11月9日,processJumpCode
			 * 
			 */
			if (sub.getFkByTableId() != null) {
				for (TbProbColsdefine col : sub.getFkByTableId().getColsdefines()) {
					col.setColName("q" + sub.getNum() + "_" + col.getColIndex());
				}
			}
		}
	}

	/**
	 * 题目保存
	 */
	public void subSaveAction() {
		if (beforeSubSaveAction()) {
			subSort(bindSubList);
			initSubList();
			forwardEditPage();
		}
	}

	/**
	 * 题目保存验证
	 * 
	 * 
	 * 修订内容：增加标题依赖验证
	 * 
	 * @MethodReviser rj,2017年11月9日,beforeSubSaveAction
	 * 
	 * 
	 * @return
	 */
	private boolean beforeSubSaveAction() {
		boolean flag = true;
		Set<String> errorList = new HashSet<String>();
		if (StringUtils.isBlank(sub.getTitleDesc())) {
			JsfUtil.addErrorMessage("题目标题不能为空！");
			flag = false;
		}
		if (sub.getQuestType().getTypeNo() == 2) {
			if (sub.getSlideMaxval() == null) {
				JsfUtil.addErrorMessage("滑动题最大值不能为空！");
				flag = false;
			}
			if (sub.getSlideMinval() == null) {
				JsfUtil.addErrorMessage("滑动题最小值不能为空！");
				flag = false;
			}
			if (StringUtils.isBlank(sub.getSlideMaxvalDesc())) {
				JsfUtil.addErrorMessage("滑动题最大值描述不能为空！");
				flag = false;
			}
			if (StringUtils.isBlank(sub.getSlideMinvalDesc())) {
				JsfUtil.addErrorMessage("滑动题最小值描述不能为空！");
				flag = false;
			}
			if (sub.getSlideMaxval() != null && sub.getSlideMinval() != null) {
				if (sub.getSlideMaxval() < sub.getSlideMinval()) {
					JsfUtil.addErrorMessage("滑动题最大值不能小于滑动题最小值！");
					flag = false;
				}
			}
		} else {
			sub.setSlideMaxval(null);
			sub.setSlideMaxvalDesc(null);
			sub.setSlideMinval(null);
			sub.setSlideMinvalDesc(null);
		}
		if (sub.getProOptList().size() > 0) {
			for (TsProOpt opt : sub.getProOptList()) {
				for (TsProOpt temp : sub.getProOptList()) {
					if (temp != opt && opt.getNum().intValue() == temp.getNum().intValue()) {
						errorList.add("选项序号重复！");
					}
				}
			}
		}
		if (sub.getJumpType() != null
				&& sub.getJumpType() == 2
				&& (StringUtils.isBlank(sub.getRelQueCode()) || sub.getRelQueOptNums() == null || sub
						.getRelQueOptNums().size() == 0)) {
			errorList.add("请选择需要依赖的题目！");
		}
		if (errorList.size() > 0) {
			for (String str : errorList) {
				JsfUtil.addErrorMessage(str);
			}
			flag = false;
		}
		return flag;
	}

	/**
	 * 删除题目
	 */
	public void subDeleteAction() {
		if (sub.getRid() == null) {
			Iterator<TsProbSubject> it = bindSubList.iterator();
			while (it.hasNext()) {
				TsProbSubject temp = it.next();
				if (temp.getQesLevelCode().contains(sub.getQesCode())) {
					if (temp.getRid() == null) {
						it.remove();
					} else {
						temp.setState(0);
					}
				}
			}
			this.bindSubList.remove(sub);
		} else {
			sub.setState(0);
			Iterator<TsProbSubject> it = bindSubList.iterator();
			while (it.hasNext()) {
				TsProbSubject temp = it.next();
				if (temp.getQesLevelCode().contains(sub.getQesCode())) {
					if (temp.getRid() == null) {
						it.remove();
					} else {
						temp.setState(0);
					}
				}
			}
		}
		initSubList();
	}

	/**
	 * 选项保存
	 */
	public void optSaveAction() {
		if (beforeOptSaveAction()) {
			if (!sub.getProOptList().contains(opt)) {
				sub.getProOptList().add(opt);
			}
			optSort(sub.getProOptList());
			RequestContext.getCurrentInstance().execute("PF('OptDialog').hide()");
			RequestContext.getCurrentInstance().update(":tabView:subEditForm:optInfoTable");
		}
	}

	/**
	 * 选项保存验证
	 */
	public boolean beforeOptSaveAction() {
		boolean flag = true;
		for (TsProOpt temp : sub.getProOptList()) {
			if (temp != opt && opt.getNum().intValue() == temp.getNum().intValue()) {
				JsfUtil.addErrorMessage("已存在相同序号的选项！");
				flag = false;
			}
			if (opt.getJumpType() == null || opt.getJumpType().intValue() != 3) {
				opt.setJumpQuestCode(null);
			}
		}
		if (opt.getOptionValue() == null) {
			JsfUtil.addErrorMessage("选项值不能为空！");
			flag = false;
		}
		return flag;
	}

	/**
	 * 问卷状态更新
	 */
	public void updateState() {
		try {
			systemModuleServiceImpl.updateProbLibState(rid, state);
			JsfUtil.addSuccessMessage((state == 1 ? "启用" : "停用") + "成功");
			this.searchAction();
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage((state == 1 ? "启用" : "停用") + "失败");
		}
	}

	/**
	 * 删除问卷
	 */
	public void deleteAction() {
		try {
			systemModuleServiceImpl.deleteProbLibByRid(rid);
			JsfUtil.addSuccessMessage("删除成功！");
			searchAction();
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addSuccessMessage("已被引用,无法删除！");
		}
	}

	public void backEditAction() {
		forwardEditPage();
	}

	/**
	 * 题目排序
	 * 
	 * @param subjects
	 */
	public void subSort(List<TsProbSubject> subjects) {
		Collections.sort(subjects, new Comparator<TsProbSubject>() {
			@Override
			public int compare(TsProbSubject o1, TsProbSubject o2) {
				return o1.getNum() - o2.getNum();
			}
		});
	}

	/**
	 * 选项排序
	 * 
	 * @param opts
	 */
	public void optSort(List<TsProOpt> opts) {
		Collections.sort(opts, new Comparator<TsProOpt>() {
			@Override
			public int compare(TsProOpt o1, TsProOpt o2) {
				return o1.getNum() - o2.getNum();
			}
		});
	}

	/**
	 * 生成HTML页面
	 */
	public void genHtmlAction() {
		if (ifEdit == 1) {
			if (bindSubList != null) {
				int count = 0;
				for (TsProbSubject sub : bindSubList) {
					if (sub.getState() == 1) {
						count++;
					}
				}
				if (count == 0) {
					JsfUtil.addErrorMessage("该问卷下不存在题目，请检查！");
				} else {
					if (selectedQueTypeID != null) {
						for (SelectItem item : this.typeList) {
							if (item.getValue().toString().equals(selectedQueTypeID.toString())) {
								lib.setTsSimpleCodeByQuestSortid(this.typeMap.get(selectedQueTypeID));
							}
						}
					}
					processJumpCode();
					String produceHtmlPage = ProduceQuestPage.produceHtmlPage(bindSubList, lib);
					if (StringUtils.isBlank(produceHtmlPage)) {
						JsfUtil.addSuccessMessage("生成成功！");
					} else {
						JsfUtil.addErrorMessage(produceHtmlPage);
					}
				}
			} else if (bindSubList == null) {
				JsfUtil.addErrorMessage("该问卷下不存在题目，请检查！");
			}
		} else {
			TsProbLib findTsProbLib = this.systemModuleServiceImpl.findTsProbLib(rid);
			if (null != findTsProbLib) {
				List<TsProbSubject> subjectList = findTsProbLib.getSubjectList();
				if (null != subjectList && subjectList.size() > 0) {
					this.bindSubList = findTsProbLib.getSubjectList();
					this.updateSubs();
					this.processJumpCode();
					findTsProbLib.setSubjectList(bindSubList);
					findTsProbLib.setCreateDate(new Date());
					for (TsProbSubject sub : findTsProbLib.getSubjectList()) {
						sub.setCreateDate(new Date());
						for (TsProOpt opt : sub.getProOptList()) {
							opt.setCreateDate(new Date());
						}
					}
					findTsProbLib = systemModuleServiceImpl.saveOrUpdateProbLib(findTsProbLib, JsfUtil.getRequest());
					subjectList = findTsProbLib.getSubjectList();
					String produceHtmlPage = ProduceQuestPage.produceHtmlPage(subjectList, findTsProbLib);
					if (StringUtils.isBlank(produceHtmlPage)) {
						JsfUtil.addSuccessMessage("生成成功！");
					} else {
						JsfUtil.addErrorMessage(produceHtmlPage);
					}
				} else {
					JsfUtil.addErrorMessage("该问卷下不存在题目，请检查！");
				}
			} else {
				JsfUtil.addErrorMessage("未查询到问卷，请检查！");
			}
		}
	}

	/**
	 * 生成预览
	 */
	public void forwardPreviewAction() {
		try {
			if (previewTag == 1) {
				File file = new File(JsfUtil.getAbsolutePath() + "/slowque/" + htmlName + ".html");
				if (!file.exists()) {
					JsfUtil.addErrorMessage("未找到问卷html文件，请确认是否生成成功！");
					return;
				}
				StringBuilder sb = new StringBuilder("/webFile/slowque/");
				sb.append(htmlName).append(".html?simpleView=1&randomTime=").append(new Date().getTime());
				RequestContext currentInstance = RequestContext.getCurrentInstance();
				currentInstance.execute("top.ShortcutMenuClick(\"01\",\"问卷预览\",\"" + sb.toString() + "\",\"\");");
			} else {
				File file = new File(JsfUtil.getAbsolutePath() + "/slowque/" + htmlName + "mobile.html");
				if (!file.exists()) {
					JsfUtil.addErrorMessage("未找到问卷html文件，请确认是否生成成功！");
					return;
				}
				StringBuilder sb = new StringBuilder("/webFile/slowque/");
				sb.append(htmlName).append("mobile.html?simpleView=1&randomTime=").append(new Date().getTime());
				RequestContext currentInstance = RequestContext.getCurrentInstance();
				currentInstance.execute("top.ShortcutMenuClick(\"01\",\"问卷预览\",\"" + sb.toString() + "\",\"\");");
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}

	/**
	 * 初始化题目treeTable
	 */
	private void initSubList() {
		this.root = new DefaultTreeNode("root", null);
		if (this.bindSubList != null && bindSubList.size() > 0) {
			Set<String> firstLevelNoSet = new LinkedHashSet<String>(); // 只有第一层
			Set<String> levelNoSet = new LinkedHashSet<String>(); // 没有第一层
			Map<String, TsProbSubject> allMap = new HashMap<String, TsProbSubject>(bindSubList.size()); //
			for (TsProbSubject sub : this.bindSubList) {
				if (sub.getState().intValue() == 0) {
					continue;
				}
				allMap.put(sub.getQesLevelCode(), sub);
				if (StringUtils.isNotBlank(sub.getQesLevelCode())) {
					if (StringUtils.containsNone(sub.getQesLevelCode(), ".")) {
						firstLevelNoSet.add(sub.getQesLevelCode());
					} else {
						levelNoSet.add(sub.getQesLevelCode());
					}
				}
			}
			for (String ln : firstLevelNoSet) {
				TsProbSubject t = allMap.get(ln);
				TreeNode node = new DefaultTreeNode(allMap.get(ln), this.root);
				node.setExpanded(true);
				this.addChildNode(ln, levelNoSet, allMap, node);
			}
		}
		RequestContext.getCurrentInstance().update("tabView:editForm:subjectInfoTable");
	}

	/**
	 * 构建树
	 * 
	 * @param levelNo
	 *            菜单层级编码
	 * @param levelNoSet
	 *            二级以及以上的菜单的层级编码集合
	 * @param allMap
	 *            所有数据级map
	 * @param parentNode
	 *            上级树节点
	 */
	private void addChildNode(String levelNo, Set<String> levelNoSet, Map<String, TsProbSubject> allMap,
			TreeNode parentNode) {
		int level = StringUtils.countMatches(levelNo, ".");
		for (String ln : levelNoSet) {
			if (StringUtils.countMatches(ln, ".") == (level + 1) && StringUtils.startsWith(ln, levelNo + ".")) {
				TsProbSubject t = allMap.get(ln);
				TreeNode node = new DefaultTreeNode(allMap.get(ln), parentNode);
				node.setExpanded(true);
				this.addChildNode(ln, levelNoSet, allMap, node);
			}
		}
	}

	/** 排序初始化 */
	public void initSubOrder() {
		this.orderList = new ArrayList<>();
		if (this.bindSubList != null && bindSubList.size() > 0) {
			updateSubListLevel();
			int level = StringUtils.countMatches(sub.getQesLevelCode(), ".");
			if (level == 0) {
				for (TsProbSubject temp : bindSubList) {
					if (temp.getState() == 1 && StringUtils.countMatches(temp.getQesLevelCode(), ".") == level) {
						orderList.add(new ProbSubOrderBean(temp.getShowCode(), temp.getTitleDesc(), temp.getQesCode()));
					}
				}
			} else {
				String parentLevelCode = sub.getQesLevelCode().substring(0, sub.getQesLevelCode().lastIndexOf("."));
				for (TsProbSubject temp : bindSubList) {
					if (temp.getState() == 1 && StringUtils.countMatches(temp.getQesLevelCode(), ".") == level
							&& StringUtils.startsWith(temp.getQesLevelCode(), parentLevelCode)) {
						orderList.add(new ProbSubOrderBean(temp.getShowCode(), temp.getTitleDesc(), temp.getQesCode()));
					}
				}
			}
		}
	}

	/** 保存题目排序 */
	public void saveSubOrderList() {
		int level = StringUtils.countMatches(sub.getQesLevelCode(), ".");
		List<TsProbSubject> tempList = new ArrayList<>();
		int index = 1;
		if (level == 0) {
			index = 1;
		} else {
			String parentCode = sub.getQesLevelCode().substring(0, sub.getQesLevelCode().lastIndexOf("."));
			for (TsProbSubject tsProbSubject : bindSubList) {
				if (tsProbSubject.getState() == 0) {
					continue;
				}
				if (parentCode.equals(tsProbSubject.getQesLevelCode())) {
					index = tsProbSubject.getNum() + 1;
				}
			}
		}
		for (ProbSubOrderBean order : orderList) {
			for (TsProbSubject item : bindSubList) {
				if (item.getState().intValue() == 0) {
					continue;
				}
				if (order.getQesCode().equals(item.getQesCode())) {
					tempList.add(item);
					if (item.getChildList().size() > 0) {
						addAllSub(tempList, item.getChildList());
					}
				}
			}
		}
		for (TsProbSubject itm : tempList) {
			itm.setNum(new Integer(index++));
		}
		tempList = null;
		subSort(bindSubList);
		initSubList();
		RequestContext.getCurrentInstance().update("tabView:editForm:subjectInfoTable");
		RequestContext.getCurrentInstance().execute("PF('SubOrderDialog').hide();");
	}

	private void addAllSub(List<TsProbSubject> allList, List<TsProbSubject> childList) {
		for (TsProbSubject child : childList) {
			allList.add(child);
			if (child.getChildList().size() > 0) {
				addAllSub(allList, child.getChildList());
			}
		}
	}

	/**
	 * 初始化题目treeTable
	 */
	private void updateSubListLevel() {
		if (this.bindSubList != null && bindSubList.size() > 0) {
			Set<String> firstLevelNoSet = new LinkedHashSet<String>(); // 只有第一层
			Set<String> levelNoSet = new LinkedHashSet<String>(); // 没有第一层
			Map<String, TsProbSubject> allMap = new HashMap<String, TsProbSubject>(bindSubList.size()); //
			for (TsProbSubject sub : this.bindSubList) {
				if (sub.getState().intValue() == 0) {
					continue;
				}
				sub.setChildList(new ArrayList<TsProbSubject>(0));
				allMap.put(sub.getQesLevelCode(), sub);
				if (StringUtils.isNotBlank(sub.getQesLevelCode())) {
					if (StringUtils.containsNone(sub.getQesLevelCode(), ".")) {
						firstLevelNoSet.add(sub.getQesLevelCode());
					} else {
						levelNoSet.add(sub.getQesLevelCode());
					}
				}
			}
			for (String ln : firstLevelNoSet) {
				TsProbSubject t = allMap.get(ln);
				this.addChildList(ln, levelNoSet, allMap, t);
			}
		}
	}

	/**
	 * 构建树
	 * 
	 * @param levelNo
	 *            菜单层级编码
	 * @param levelNoSet
	 *            二级以及以上的菜单的层级编码集合
	 * @param allMap
	 *            所有数据级map
	 * @param parentNode
	 *            上级树节点
	 */
	private void addChildList(String levelNo, Set<String> levelNoSet, Map<String, TsProbSubject> allMap,
			TsProbSubject parent) {
		int level = StringUtils.countMatches(levelNo, ".");
		for (String ln : levelNoSet) {
			if (StringUtils.countMatches(ln, ".") == (level + 1) && StringUtils.startsWith(ln, levelNo + ".")) {
				TsProbSubject t = allMap.get(ln);
				parent.getChildList().add(t);
				subSort(parent.getChildList());
				this.addChildList(ln, levelNoSet, allMap, t);
			}
		}
	}

	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder();
		sb.append(" from TsProbLib t where 1 = 1 ");
		if (StringUtils.isNotBlank(this.searchQueName)) {
			sb.append(" and t.questName like :searchQueName ");
			this.paramMap.put("searchQueName", "%" + StringUtils.convertBFH(this.searchQueName) + "%");
		}
		if (null != searchTypeId) {
			sb.append(" and t.tsSimpleCodeByQuestSortid.rid = ").append(this.searchTypeId).append("");
		}
		if (!ifAdmin) {
			sb.append(" and t.tsUnitByUnitId.rid = ").append(sessionData.getUser().getTsUnit().getRid());
		}
		if (searchState != null && searchState.length == 1) {
			sb.append(" and t.state = ").append(searchState[0]);
		}
		// 授权的题库问卷码表数据
		if (null != typeList && typeList.size() > 0) {
			String s = "";
			for (SelectItem t : typeList) {
				s += "," + t.getValue().toString();
			}
			sb.append(" and t.tsSimpleCodeByQuestSortid.rid in (");
			sb.append(s.replaceFirst(",", "")).append(")");
		} else {
			sb.append(" and t.tsSimpleCodeByQuestSortid.rid in (-1)");
		}
		sb.append(" order by t.tsSimpleCodeByQuestSortid.codeNo,to_number(t.num),t.questName,t.rid");
		StringBuilder searchSql = new StringBuilder();
		searchSql.append("select t ").append(sb.toString());
		StringBuilder countSql = new StringBuilder();
		countSql.append("select count(t.rid) ").append(sb.toString());
		return new String[] { searchSql.toString(), countSql.toString() };
	}

	public SessionData getSessionData() {
		return sessionData;
	}

	public void setSessionData(SessionData sessionData) {
		this.sessionData = sessionData;
	}

	public String getSearchQueName() {
		return searchQueName;
	}

	public void setSearchQueName(String searchQueName) {
		this.searchQueName = searchQueName;
	}

	public Integer getSearchTypeId() {
		return searchTypeId;
	}

	public void setSearchTypeId(Integer searchTypeId) {
		this.searchTypeId = searchTypeId;
	}

	public List<SelectItem> getTypeList() {
		return typeList;
	}

	public void setTypeList(List<SelectItem> typeList) {
		this.typeList = typeList;
	}

	public String[] getSearchState() {
		return searchState;
	}

	public void setSearchState(String[] searchState) {
		this.searchState = searchState;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public TsProbLib getLib() {
		return lib;
	}

	public void setLib(TsProbLib lib) {
		this.lib = lib;
	}

	public boolean isIfAdmin() {
		return ifAdmin;
	}

	public void setIfAdmin(boolean ifAdmin) {
		this.ifAdmin = ifAdmin;
	}

	public Integer getIfEdit() {
		return ifEdit;
	}

	public void setIfEdit(Integer ifEdit) {
		this.ifEdit = ifEdit;
	}

	public List<SelectItem> getUnitList() {
		return unitList;
	}

	public void setUnitList(List<SelectItem> unitList) {
		this.unitList = unitList;
	}

	public List<ProbLibThemeBean> getThemeList() {
		return themeList;
	}

	public void setThemeList(List<ProbLibThemeBean> themeList) {
		this.themeList = themeList;
	}

	public Integer getSelectedUnitID() {
		return selectedUnitID;
	}

	public void setSelectedUnitID(Integer selectedUnitID) {
		this.selectedUnitID = selectedUnitID;
	}

	public Integer getSelectedQueTypeID() {
		return selectedQueTypeID;
	}

	public void setSelectedQueTypeID(Integer selectedQueTypeID) {
		this.selectedQueTypeID = selectedQueTypeID;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public TsProbSubject getSub() {
		return sub;
	}

	public void setSub(TsProbSubject sub) {
		this.sub = sub;
	}

	public String getSelectedTheme() {
		return selectedTheme;
	}

	public void setSelectedTheme(String selectedTheme) {
		this.selectedTheme = selectedTheme;
	}

	public String getHtmlName() {
		return htmlName;
	}

	public void setHtmlName(String htmlName) {
		this.htmlName = htmlName;
	}

	public TsProOpt getOpt() {
		return opt;
	}

	public void setOpt(TsProOpt opt) {
		this.opt = opt;
	}

	public Integer getUploadTag() {
		return uploadTag;
	}

	public void setUploadTag(Integer uploadTag) {
		this.uploadTag = uploadTag;
	}

	public List<QueSubTypeBean> getSubTypeOptions() {
		return subTypeOptions;
	}

	public void setSubTypeOptions(List<QueSubTypeBean> subTypeOptions) {
		this.subTypeOptions = subTypeOptions;
	}

	public List<TsProbExamtype> getExamtypes() {
		return examtypes;
	}

	public void setExamtypes(List<TsProbExamtype> examtypes) {
		this.examtypes = examtypes;
	}

	public TreeNode getRoot() {
		return root;
	}

	public void setRoot(TreeNode root) {
		this.root = root;
	}

	public List<TsProbSubject> getBindSubList() {
		return bindSubList;
	}

	public void setBindSubList(List<TsProbSubject> bindSubList) {
		this.bindSubList = bindSubList;
	}

	public List<TsProbSubject> getJumpOptions() {
		return jumpOptions;
	}

	public void setJumpOptions(List<TsProbSubject> jumpOptions) {
		this.jumpOptions = jumpOptions;
	}

	public List<TsProbSubject> getRelOptions() {
		return relOptions;
	}

	public void setRelOptions(List<TsProbSubject> relOptions) {
		this.relOptions = relOptions;
	}

	public List<SelectItem> getRelQueOpts() {
		return relQueOpts;
	}

	public void setRelQueOpts(List<SelectItem> relQueOpts) {
		this.relQueOpts = relQueOpts;
	}

	public List<ProbSubOrderBean> getOrderList() {
		return orderList;
	}

	public void setOrderList(List<ProbSubOrderBean> orderList) {
		this.orderList = orderList;
	}

	public List<FillMaxRangeBean> getRanges() {
		return ranges;
	}

	public void setRanges(List<FillMaxRangeBean> ranges) {
		this.ranges = ranges;
	}

	public TbProbRowtitle getRowtitle() {
		return rowtitle;
	}

	public void setRowtitle(TbProbRowtitle rowtitle) {
		this.rowtitle = rowtitle;
	}

	public TbProbColsdefine getColsdefine() {
		return colsdefine;
	}

	public void setColsdefine(TbProbColsdefine colsdefine) {
		this.colsdefine = colsdefine;
	}

	public Map<String, String> getSystemTypeMap() {
		return systemTypeMap;
	}

	public void setSystemTypeMap(Map<String, String> systemTypeMap) {
		this.systemTypeMap = systemTypeMap;
	}

	public Short getSystemTypeEdit() {
		return systemTypeEdit;
	}

	public void setSystemTypeEdit(Short systemTypeEdit) {
		this.systemTypeEdit = systemTypeEdit;
	}

	public Integer getPreviewTag() {
		return previewTag;
	}

	public void setPreviewTag(Integer previewTag) {
		this.previewTag = previewTag;
	}

}
