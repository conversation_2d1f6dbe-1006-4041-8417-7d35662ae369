package com.chis.modules.system.web;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.primefaces.context.RequestContext;

import com.alibaba.fastjson.JSON;
import com.chis.common.bean.ReportOptType;
import com.chis.common.utils.FastReportUtil;
import com.chis.common.utils.FileUtils;
import com.chis.common.utils.HttpRequestUtil;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZipUtils;
import com.chis.modules.system.entity.TsRpt;
import com.chis.modules.system.enumn.ResponseState;
import com.chis.modules.system.interfaces.IFastReport;
import com.chis.modules.system.logic.AuthTokenIdPojo;
import com.chis.modules.system.logic.PdfParamStr;
import com.chis.modules.system.logic.SignRptPO;
import com.chis.modules.system.logic.SysReturnPojo;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.HttpRequestComponent;

/**
 * FastReport报表的工具类
 * 
 * <AUTHOR> 2015-02-11
 * 
 * <p>修订内容：新增签章功能</p>
 * @ClassReviser qrr,2018年4月21日,FastReportBean
 */
public class FastReportBean extends FacesBean {

	private static final long serialVersionUID = 1395670521105363033L;
	/** 虚拟路径 */
	public static final String VIRTUAL_PATH = JsfUtil.getAbsolutePath();
	// ejb session bean
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder
			.getBean(CommServiceImpl.class);
	/** 模板对象 */
	protected TsRpt tsRpt = new TsRpt();
	/** 业务Bean对象 */
	private IFastReport fsFastReport;
	/** 报表模板的编码 */
	private String fastReportCode;
	/**
	 * 业务模板版本，比如流程定义有多个版本，为了兼容老版本，内部定义模板路径为如：rptcode+
	 * 'zwx'+businessVersion+'_'+version.fr3
	 */
	private String businessVersion;
	
	/** 签章对象 */
    private SignRptPO signRptPO;
	/**fastReport版本*/
    private String version;
    private String tempPDFPath;

	public FastReportBean(IFastReport fsFastReport, String fastReportCode) {
		try {
            /**报表插件版本号*/
            this.version = commService.findParamValue("FAST_REPORT_VERSION");
            if(StringUtils.isBlank(version)){
                this.version = PropertyUtils.getValue("fastReport.version");
            }
        } catch (RuntimeException e) {
            e.printStackTrace();
        }
		this.fsFastReport = fsFastReport;
		this.fastReportCode = fastReportCode;
		readRptFile();
	}

	public FastReportBean(IFastReport fsFastReport, String fastReportCode,
			String businessVersion) {
		this.fsFastReport = fsFastReport;
		this.fastReportCode = fastReportCode;
		this.businessVersion = businessVersion;
		readRptFile();
	}

	/**
	 * 更新报表内容
	 */
	public void updateFastReport() {
		try {
			String commFrptCode = JsfUtil.getRequestParameterMap().get(
					"frptCode");
			String commFrptContent = JsfUtil.getRequestParameterMap().get(
					"frptContent");
			if (StringUtils.isNotBlank(commFrptCode)
					&& StringUtils.isNotBlank(commFrptContent)) {
				this.commService.updateReportTemplate(commFrptCode,
						ZipUtils.gunzip(commFrptContent), VIRTUAL_PATH,
						this.businessVersion);
				JsfUtil.addSuccessMessage("模板保存成功！");
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}

	/**
	 * 报表设计
	 */
	public void designAction() {
		readRptFile();
		this.buildReportData(ReportOptType.DESIGN);
	}

	/**
	 * 显示报表
	 */
	public void showReportAction() {
		readRptFile();
		this.buildReportData(ReportOptType.VIEW);
	}
 /**
   * <p>方法描述：新页签打开PDF方式显示报表</p>
   * filePath:传值则代表文书制作，会使用此值作为文书pdf路径，不传值则代表只是预览制作文书
   * @MethodAuthor mxp, 2018/2/8,showPDFReportAction
   */
    public SysReturnPojo showPDFReportAction(String filePath) {
        readRptFile();
    	this.buildReportData(ReportOptType.VIEW);
        return createPdfFiles(filePath);
    }
	/**
	 * 打印报表
	 */
	public void printReportAction() {
		readRptFile();
		this.buildReportData(ReportOptType.PRINT);
	}
	 /**
     * 导出PDF报表
     */
    public void exportReportAction() {
        readRptFile();
    	this.buildReportData(ReportOptType.EXPORT);
    }
	/**
	 * 为报表对象准备数据集
	 * 
	 * @param reportOptType
	 *            报表的操作类型
	 */
	private void buildReportData(ReportOptType reportOptType) {
		this.tsRpt.setXmlData(null);
		if (StringUtils.isNotBlank(this.fastReportCode)) {
			String xmlData = FastReportUtil.generateXml(
					this.fsFastReport.supportFastReportDataSet(),
					this.fsFastReport.supportFastReportDataRef(),
					reportOptType, null, "1", null);
			this.tsRpt.setXmlData(xmlData);
		}
	}

	/**
	 * 为报表对象准备模板文件内容
	 */
	private void readRptFile() {
		if (StringUtils.isNotBlank(this.fastReportCode)) {
			this.tsRpt = this.commService.findTsRptPath(this.fastReportCode,
					VIRTUAL_PATH, this.businessVersion);
			this.tsRpt.setBusinessVersion(this.businessVersion);
		}
	}
	 /**
     * <p>方法描述：生成临时pdf文件</p>
     *
     * @MethodAuthor mxp, 2018/2/7,createPdfFiles
     */
    private SysReturnPojo createPdfFiles(String filePath) {
        long l = System.currentTimeMillis();
        Map<String, String> dataMap = new HashMap<>();
        //获取tokenId
        String targetUrl = PropertyUtils.getValue("rpt.targetUrl");
//        String tokenIdUrl = targetUrl+ PropertyUtils.getValue("rpt.tokenIdUrl");
//        String unitCode = PropertyUtils.getValue("rpt.unitCode");
//        String regCode = PropertyUtils.getValue("rpt.password");
//        tokenIdUrl += "?unitCode=" + unitCode + "&regCode=" + regCode;
//        String returnJson = null;
//        try {
//            returnJson = HttpRequestUtil.httpRequest(tokenIdUrl,  "GET" ,null );
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
        // 获取tokenId对象
//        AuthTokenIdPojo tokenIdPojo = JSON.parseObject(returnJson, AuthTokenIdPojo.class);
//        if (tokenIdPojo == null || StringUtils.isBlank(tokenIdPojo.getTokenId())) {
            //throw new RuntimeException(returnJson + "请求失败！");
//            return new SysReturnPojo(ResponseState.TOKENID_VALID.getTypeNo(), ResponseState.TOKENID_VALID.getTypeCN());
//        }
//        String tokenId = tokenIdPojo.getTokenId();
//       String tokenId = AuthTokenIdUtil.getTokenId();
        //基本参数
        PdfParamStr paramStr = new PdfParamStr();
        String rptpath = this.tsRpt.getRptpath();
        if(StringUtils.isNotBlank(rptpath)){
            rptpath = rptpath.replaceFirst("/","").replace(".fr3","").replaceAll("/","\\\\");
        }
        paramStr.setFr3Name(rptpath);
        paramStr.setSealServerAdr(targetUrl);
        paramStr.setTokenId("123");
        if (signRptPO != null) {
            paramStr.setIfSign(signRptPO.getSealifSign());
//            RptSignParamPO signParamPO = JSON.parseObject(signRptPO.getSealsignParam(), RptSignParamPO.class);
            paramStr.setSignParam(signRptPO.getSignParam());
        } else {
            paramStr.setIfSign("0");
        }
        if(StringUtils.isNotBlank(filePath)){//制作
            paramStr.setFilePath(signRptPO.getFilePath());
        }else{//预览
            paramStr.setFilePath(PropertyUtils.getValue("pdf.path"));
        }
        dataMap.put("ParamStr", JSON.toJSONString(paramStr).replace("\\\\","\\"));
        dataMap.put("xmlStr", this.tsRpt.getXmlData());

        String qzUrl = PropertyUtils.getValue("qzService.url");
        String returnMsg = HttpRequestComponent.doPost(qzUrl, dataMap, "utf-8");//使用https进行请求
        long l1 = System.currentTimeMillis();
        System.out.println(returnMsg+"######"+"总时间：：：：："+(l1-l));
        SysReturnPojo sysReturnPojo = JSON.parseObject(returnMsg, SysReturnPojo.class);
        tempPDFPath = null;
        if (sysReturnPojo != null && "00".equals(sysReturnPojo.getType())) {
            tempPDFPath = sysReturnPojo.getMess();
            RequestContext.getCurrentInstance().execute("window.open('" + FileUtils.previewFile(sysReturnPojo.getMess(), "jswj-input") + "')");
        }
        if (sysReturnPojo == null) {
            return new SysReturnPojo(ResponseState.EXCEPTION_PROCESS.getTypeNo(), ResponseState.EXCEPTION_PROCESS.getTypeCN());
        }
        if(StringUtils.isNotBlank(filePath)){
            tempPDFPath = null;
        }
        return sysReturnPojo;
    }
    /**
     * <p>修订内容：删除临时生成的pdf文件</p>
     *
     * @MethodReviser mxp, 2018/2/7,deleteTempPDF
      * <p>修订内容：延迟100s删除临时文书</p>
      *
      * @MethodReviser mxp,2018/3/3,deleteTempPDF
      */
    public void deleteTempPDF() {
        final Runnable myRunnable = new Runnable(){
            public void run(){
                try {
                    Thread.sleep(100000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                if (StringUtils.isNotBlank(tempPDFPath)) {
                    FileUtils.delFile(tempPDFPath,"jswj-input");
                }
                System.out.println("Runnable running");
            }
        };
        Thread thread = new Thread(myRunnable);
        thread.start();
    }
	public TsRpt getTsRpt() {
		return tsRpt;
	}

	public void setTsRpt(TsRpt tsRpt) {
		this.tsRpt = tsRpt;
	}

	public String getFastReportCode() {
		return fastReportCode;
	}

	public void setFastReportCode(String fastReportCode) {
		this.fastReportCode = fastReportCode;
	}

	public IFastReport getFsFastReport() {
		return fsFastReport;
	}

	public void setFsFastReport(IFastReport fsFastReport) {
		this.fsFastReport = fsFastReport;
	}

	public SignRptPO getSignRptPO() {
		return signRptPO;
	}

	public void setSignRptPO(SignRptPO signRptPO) {
		this.signRptPO = signRptPO;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getTempPDFPath() {
		return tempPDFPath;
	}

	public void setTempPDFPath(String tempPDFPath) {
		this.tempPDFPath = tempPDFPath;
	}

}
