package com.chis.modules.system.web;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;

import com.chis.common.bean.FastReportData;
import com.chis.common.bean.FastReportDataRef;
import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsRpt;
import com.chis.modules.system.enumn.SystemParamType;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.interfaces.IFastReport;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemServiceImpl;

/**
 * 报表查询bean</br>
 * <AUTHOR>
 */
@ManagedBean(name="tsRptBean")
@ViewScoped
public class TsRptBean extends FacesBean implements Serializable ,IFastReport{

	private static final long serialVersionUID = -3740326227684495169L;
    /**存在session中的对象*/
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private SystemServiceImpl systemService = (SystemServiceImpl)SpringContextHolder.getBean(SystemServiceImpl.class);
    private CommServiceImpl commService = (CommServiceImpl)SpringContextHolder.getBean(CommServiceImpl.class);
    /**查询条件：系统类型*/
    private Map<String, String> systemTypeMap = new LinkedHashMap<String, String>();
	private Map<String, String> systemTypeMap2 = new LinkedHashMap<String, String>();
    /**查询条件：报表名称*/
    private String searchRptName;
    /*模板类型系统*/
    private SystemType tempSysType;
    private String tempCn;
    /**结果数据*/
    private List<TsRpt> tsRptList;
	/** 查询条件：所有能看到的系统类型comm,risk ，隔开 */
	private String allSystem;
	/** 添加系统类型 */
	private String systemTypeEdit;
	/** ++报表实体 */
	private TsRpt addTsRpt = new TsRpt();
	/** ++报表主键 */
	private Integer rptId;
	/** 报表插件 */
	private FastReportBean fastReportBean;
	private TsRpt editPo = new TsRpt();
	public TsRptBean() {
        innit();
	}
	
	public void addAction() {
		addTsRpt = new TsRpt();
		addTsRpt.setCreateDate(new Date());
		addTsRpt.setCreateManid(this.sessionData.getUser().getRid());
		addTsRpt.setRptver(1);
		systemTypeEdit = null;
	}

	public void modAction() {
		addTsRpt = this.systemService.findTsRpt(rptId);
		addTsRpt.setRptCod(addTsRpt.getRptCod().replaceFirst("TZ_", ""));
		//addTsRpt.setSystemType();
		//systemTypeEdit = addTsRpt.getSystemType() == null ? null : addTsRpt.getSystemType().getTypeNo().toString();
		systemTypeEdit =  addTsRpt.getParamType() == null ? null : addTsRpt.getParamType().toString();
	}

	public void delAction() {
		String mess = this.systemService.delRpt(rptId);
		if (StringUtils.isNotBlank(mess)) {
			JsfUtil.addErrorMessage(mess);
		} else {
			JsfUtil.addSuccessMessage("删除成功！");
			this.searchAction();
		}
	}
	/**
	 * 报表设计
	 */
	public void designAction() {
		if (editPo == null)
			return;
		try {
			this.fastReportBean = new FastReportBean(this, this.editPo.getRptCod());
			fastReportBean.designAction();
			RequestContext.getCurrentInstance().execute("frpt_design();");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 报表设计完成后 刷新界面
	 */
	public void updateFastReport(){
		fastReportBean.updateFastReport();
		searchAction();
	}
	
	
	public void saveAction() {
		SystemType findEnum = (SystemType) EnumUtils.findEnum(SystemType.class, systemTypeEdit);
		addTsRpt.setSystemType(findEnum);
		addTsRpt.setParamType(Integer.parseInt(systemTypeEdit));
		String saveTsRpt = this.systemService.saveTsRpt(addTsRpt);
		if (StringUtils.isNotBlank(saveTsRpt)) {
			JsfUtil.addErrorMessage(saveTsRpt);
			return;
		}

		this.searchAction();

		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.execute("CodeTypeEditDialog.hide()");
		requestContext.update("mainForm:dataTable");
		JsfUtil.addSuccessMessage("保存成功！");
	}

	private void innit() {
		// 初始化系统类型
		tempCn = null;
		//查询报表中存在的系统类型
		List<Integer> typeList = this.commService.findAllParamTypeByTsRpt();
		// 初始化系统类型
		this.systemTypeMap.put("--请选择--", null);
		//初始化系统类型
		SystemType[] types = SystemType.values();
		for (SystemType st : types) {
			Integer typeNo = Integer.parseInt(st.getTypeNo().toString());
			if (!typeList.contains(typeNo)) {
				continue;
			}
			this.systemTypeMap.put(st.getTypeCN(), st.getTypeNo().toString());
			this.systemTypeMap2.put(st.getTypeNo().toString(),st.getTypeCN());
		}
		searchAction();
	}

	public void searchAction() {
		tsRptList = this.systemService.findTsRptBySystemType(tempCn,searchRptName);
		Map<Integer,SystemType> map = new HashMap<>(16);
		
		for (SystemType a : SystemType.values()) {
			map.put(a.getTypeNo().intValue(), a);
		}
		if(null != tsRptList && tsRptList.size() >0){
			for(TsRpt rpt:tsRptList){
				rpt.setSystemType(map.get(rpt.getParamType()));
			}
		}
	}

    public List<TsRpt> getTsRptList() {
        return tsRptList;
    }

    public String getTempCn() {
        return tempCn;
    }

    public void setTempCn(String tempCn) {
        this.tempCn = tempCn;
    }


    public Map<String, String> getSystemTypeMap() {
        return systemTypeMap;
    }

	public String getSystemTypeEdit() {
		return systemTypeEdit;
	}

	public void setSystemTypeEdit(String systemTypeEdit) {
		this.systemTypeEdit = systemTypeEdit;
	}

	public TsRpt getAddTsRpt() {
		return addTsRpt;
	}

	public void setAddTsRpt(TsRpt addTsRpt) {
		this.addTsRpt = addTsRpt;
	}

	public Integer getRptId() {
		return rptId;
	}

	public void setRptId(Integer rptId) {
		this.rptId = rptId;
	}

	public void setTsRptList(List<TsRpt> tsRptList) {
		this.tsRptList = tsRptList;
	}

	@Override
	public FastReportBean getFastReportBean() {
		// TODO Auto-generated method stub
		return fastReportBean;
	}

	@Override
	public List<FastReportData> supportFastReportDataSet() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<FastReportDataRef> supportFastReportDataRef() {
		// TODO Auto-generated method stub
		return null;
	}

	public TsRpt getEditPo() {
		return editPo;
	}

	public void setEditPo(TsRpt editPo) {
		this.editPo = editPo;
	}

	public String getSearchRptName() {
		return searchRptName;
	}

	public void setSearchRptName(String searchRptName) {
		this.searchRptName = searchRptName;
	}
}
