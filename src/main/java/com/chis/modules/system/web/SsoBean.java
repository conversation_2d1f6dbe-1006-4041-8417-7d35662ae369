package com.chis.modules.system.web;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MD5Util;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemServiceImpl;
import org.primefaces.context.RequestContext;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@ManagedBean(name="ssoBean")
@ViewScoped
public class SsoBean implements Serializable {

    private SystemServiceImpl systemService = (SystemServiceImpl)SpringContextHolder.getBean(SystemServiceImpl.class);


    public void auth(){
        HttpServletRequest req = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String ticket = req.getParameter("ticket");
        String appKey = PropertyUtils.getValueWithoutException("sso.appKey");
        String appSecret = PropertyUtils.getValueWithoutException("sso.appSecret");
        String authCenterIp = PropertyUtils.getValueWithoutException("sso.authCenterIp");
        Map<String,String> map = new HashMap<>();
        map.put("ticket",ticket);
        map.put("appKey",appKey);
        map.put("appSecret",appSecret);
        HttpResponse response = HttpRequest.post(authCenterIp + "/auth/sso/getUserInfoByTicket")
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .body(JSON.toJSONString(map))
                .timeout(30 * 1000)
                .execute();
        if (200 == response.getStatus()) {
            String result = response.body();
            JSONObject r = JSONObject.parseObject(result);
            if (200 == r.getInteger("code")) {
                JSONObject userInfo = (JSONObject)r.get("data");
                TsUserInfo user = this.systemService.findUserByUserNo(userInfo.getString("userName"));
                if(null != user) {
                    if(user.getIfReveal().intValue() == 1) {

                        //登录成功
                        SessionData sessionData = new SessionData(user);
                        Map<String, String> btnMap = this.systemService.findUsersBtn(user.getRid());
                        sessionData.setButtonMap(btnMap);
                        sessionData.setBtnSet(systemService.findUserBtns(user.getRid()));
                        sessionData.setIsZZpx(0);
                        req.getSession().setAttribute(SessionData.SESSION_DATA, sessionData);
                    } else {
                        RequestContext.getCurrentInstance().execute("setVerImg()");
                        JsfUtil.addErrorMessage("该用户已被禁用！");
                        return;
                    }
                }else {
                    RequestContext.getCurrentInstance().execute("setVerImg()");
                    JsfUtil.addErrorMessage("请输入正确的用户名！");
                    return;
                }

                ExternalContext extContext = FacesContext.getCurrentInstance().getExternalContext();
                try {
                    extContext.redirect("head.faces");
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } else {
                throw new RuntimeException("获取用户信息失败");
            }
        } else {
            throw new RuntimeException("获取用户信息失败");
        }
    }
}
