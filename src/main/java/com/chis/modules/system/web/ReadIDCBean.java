package com.chis.modules.system.web;

import com.chis.common.utils.FileUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.StringUtils;

import java.io.File;
import java.util.UUID;

/**
 * <p>类描述：${END}</p>
 *
 * @ClassAuthor mxp, 2018-7-27, ReadIDCBean
 */
public class ReadIDCBean {
    /**
     * 身份证读卡时获取--身份证号
     */
    private String readerIdc;
    /**
     * 身份证读卡获取--姓名
     */
    private String readerName;
    /**
     * 身份证读卡获取--住址
     */
    private String readerAdd;
    /**
     * 身份证读卡获取--照片base64码
     */
    private String readerBSAE64;



    /**
     * 读卡
     */
    public void cardReaderAction() {
        if(StringUtils.isBlank(this.readerIdc)){
            JsfUtil.addErrorMessage("请检查设备！");
            return;
        }
        // 读取身份证中的信息内容
        this.readerIdc = this.readerIdc.substring(0, 18);
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        String path = JsfUtil.getAbsolutePath();
        path = path.substring(0, path.lastIndexOf("/"));
        // 存放目录
        String ifExistPath = path + "/files";
        File file = new File(ifExistPath);
        if (!file.exists()) {
            file.mkdirs();
        }
        String relativePath = "/files/" + uuid + ".jpg";
        // 文件路径
        String filePath = new StringBuilder(path).append(relativePath).toString();
        // base64码转图片
        FileUtils.base64ToImage(this.readerBSAE64, filePath);
    }


    public String getReaderIdc() {
        return readerIdc;
    }

    public void setReaderIdc(String readerIdc) {
        this.readerIdc = readerIdc;
    }

    public String getReaderName() {
        return readerName;
    }

    public void setReaderName(String readerName) {
        this.readerName = readerName;
    }

    public String getReaderAdd() {
        return readerAdd;
    }

    public void setReaderAdd(String readerAdd) {
        this.readerAdd = readerAdd;
    }

    public String getReaderBSAE64() {
        return readerBSAE64;
    }

    public void setReaderBSAE64(String readerBSAE64) {
        this.readerBSAE64 = readerBSAE64;
    }
}
