package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.DualListModel;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsGroup;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUserGroup;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * 组管理托管Bean
 *
 * <AUTHOR>
 *
 */
@ManagedBean(name = "tsGroupBean")
@ViewScoped
public class TsGroupBean extends FacesSimpleBean{
    private static final long serialVersionUID = -3263265959082094760L;
    /**存在session中的对象*/
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    /**ejb session bean*/
	private SystemModuleServiceImpl systemService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    //查询名称
    private String searchGroupName;
    //rid
    private Integer rid;
    //状态改变值
    private short changeState;
    //组实体
    private TsGroup tsGroup;
    //科室名
    private String userOfficeName;
    //科室ID
    private Integer userOfficeRid;
    //科室树
    private TreeNode userOfficeTreeNode;
    //双选择列表，用户集合
    private DualListModel<TsUserInfo> userInfoDualListModel;

    @PostConstruct
    public void init(){
        tsGroup = new TsGroup();
        searchGroupName = "";
        //初始化科室树
        initOfficeTree();
        this.searchAction();
    }

    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder("from TsGroup t where t.tsUnit.rid = ");
        sb.append(sessionData.getUser().getTsUnit().getRid());
        if(StringUtils.isNotBlank(this.searchGroupName)){
            sb.append(" and t.groupName like :groupname escape '\\\'");
            this.paramMap.put("groupname", "%" + convertBFH(this.searchGroupName.trim()) + "%");
        }
        String h2 = "select count(*) " + sb.toString();
        String h1 = "select t " + sb.append(" order by t.xh").toString();
        return new String[]{h1,h2};
    }

    /**
     * 处理查询中“%”的问题，可以将“%”作为查询内容
     * @param str
     *          原字符串
     * @return
     *      返回装换后的字符串
     */
    private String convertBFH(String str) {
        if (str.indexOf("%") != -1 || str.indexOf("％") != -1) {
            str = str.replaceAll("%", "\\\\%").replaceAll("％", "\\\\％");
        }
        return str;
    }

    //增加初始化
    public void addInit(){
        tsGroup = new TsGroup();
    }

    //保存新增的组或者修改内容
    public void saveAction(){
        //如果是新增，则设置的内容
        if(tsGroup.getRid()==null){
            tsGroup.setTsUnit(sessionData.getUser().getTsUnit());
            tsGroup.setIfReveal(0);
            tsGroup.setCreateDate(new Date());
            tsGroup.setCreateManId(sessionData.getUser().getRid());
        }else{
            //如果是修改，则设置的内容
            tsGroup.setModifyDate(new Date());
            tsGroup.setModifyManId(sessionData.getUser().getRid());
        }
        //保存或更新组内容
        String msg = this.systemService.saveOrUpdateGroup(tsGroup,sessionData.getUser().getTsUnit().getRid());
        //保存失败，组名称重复
        if(msg != null){
            JsfUtil.addErrorMessage("mainForm:groupDesc", msg);
            return;
        }else{
            //操作成功，关闭页面弹窗
            RequestContext.getCurrentInstance().execute("GroupAddDialog.hide()");
        }
        //如果是新增，则初始化查询条件
        if(tsGroup.getRid() == null){
            this.searchGroupName = "";
        }
        this.searchAction();
    }

    public void modInit(){
    }

    //删除操作
    public void deleteAction(){
        String msg = this.systemService.deleteGroup(rid);
        //删除失败，返回错误信息
        if(StringUtils.isNotBlank(msg)) {
            JsfUtil.addErrorMessage(msg);
        }
        this.searchAction();
    }

    //改变组状态
    public void changeStateAction(){
        this.systemService.changeTsGroupState(this.rid, this.changeState);
        this.searchAction();
    }

    //成员管理初始化
    public void userManagerInit(){
        this.userOfficeName = null;
        this.userOfficeRid = null;
        List<TsUserInfo> sourceList = new LinkedList<TsUserInfo>();
        List<TsUserInfo> targetList = this.systemService.findTargetList(rid);
        //查询本单位所有用户
        List<TsUserInfo> userList = this.systemService.findAllUsers(sessionData.getUser().getTsUnit().getRid(), null);
        for(TsUserInfo t : userList){
            boolean isExist = false;
            //判定目标列表中是否已有该用户，没有则将该用户增加到可选用户中
            for(TsUserInfo s : targetList){
                if(t.getRid().equals(s.getRid())){
                    isExist = true;
                }
            }
            if(!isExist){
                sourceList.add(t);
            }
        }
        this.userInfoDualListModel = new DualListModel<TsUserInfo>(sourceList,targetList);
    }

    // 初始化科室树 userOfficeTreeNode
    public void initOfficeTree(){
        if (null == this.userOfficeTreeNode) {
            this.userOfficeTreeNode = new DefaultTreeNode("root", null);
            List<TsOffice> list = this.systemService.findOfficeList(sessionData.getUser().getTsUnit().getRid());
            if (null != list && list.size() > 0) {
                Map<Integer, TreeNode> map = new HashMap<Integer, TreeNode>();
                for (TsOffice t : list) {
                    if (null == t.getParentOffice()) {
                        TreeNode node = new DefaultTreeNode(t, this.userOfficeTreeNode);
                        map.put(t.getRid(), node);
                    } else {
                        TreeNode node = new DefaultTreeNode(t, map.get(t.getParentOffice().getRid()));
                        map.put(t.getRid(), node);
                    }
                }
                map.clear();
            }
        }
    }

    //科室树选中事件
    public void onOfficeNodeSelect(NodeSelectEvent event) {
        TreeNode node = event.getTreeNode();
        if (null != node) {
            TsOffice office = (TsOffice) node.getData();
            this.userOfficeRid = office.getRid();
            this.userOfficeName = office.getOfficename();
            List<TsUserInfo> sourceList = new ArrayList<TsUserInfo>();
            List<TsUserInfo> targetList = this.userInfoDualListModel.getTarget();
            //查询该单位该科室的所有用户
            List<TsUserInfo> userList = this.systemService.findAllUsers(sessionData.getUser().getTsUnit().getRid(), userOfficeRid);
            for(TsUserInfo t : userList){
                boolean isExist = false;
                for(TsUserInfo s : targetList){
                    if(t.getRid().equals(s.getRid())){
                        isExist = true;
                    }
                }
                if(!isExist){
                    sourceList.add(t);
                }
            }
            this.userInfoDualListModel = new DualListModel<TsUserInfo>(sourceList,targetList);
        }
    }

    //用户管理提交
    public void userManagerAction(){
        TsUserGroup tsUserGroup = null;
        List<TsUserGroup> tsUserGroupList = new ArrayList<TsUserGroup>();
        List<TsUserInfo> targetList = this.userInfoDualListModel.getTarget();
        //将已选记录转换成用户组列表
        for(TsUserInfo t : targetList){
            tsUserGroup = new TsUserGroup(this.tsGroup,t);
            tsUserGroupList.add(tsUserGroup);
        }
        //将已选用户记录持久化到数据库中
        String msg = this.systemService.updateTsUserGroup(tsUserGroupList,this.rid);
        if(msg != null){
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    public SessionData getSessionData() {
        return sessionData;
    }

    public void setSessionData(SessionData sessionData) {
        this.sessionData = sessionData;
    }

    public String getSearchGroupName() {
        return searchGroupName;
    }

    public void setSearchGroupName(String searchGroupName) {
        this.searchGroupName = searchGroupName;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public short getChangeState() {
        return changeState;
    }

    public void setChangeState(short changeState) {
        this.changeState = changeState;
    }

    public TsGroup getTsGroup() {
        return tsGroup;
    }

    public void setTsGroup(TsGroup tsGroup) {
        this.tsGroup = tsGroup;
    }

    public String getUserOfficeName() {
        return userOfficeName;
    }

    public void setUserOfficeName(String userOfficeName) {
        this.userOfficeName = userOfficeName;
    }

    public Integer getUserOfficeRid() {
        return userOfficeRid;
    }

    public void setUserOfficeRid(Integer userOfficeRid) {
        this.userOfficeRid = userOfficeRid;
    }

    public TreeNode getUserOfficeTreeNode() {
        return userOfficeTreeNode;
    }

    public void setUserOfficeTreeNode(TreeNode userOfficeTreeNode) {
        this.userOfficeTreeNode = userOfficeTreeNode;
    }

    public DualListModel<TsUserInfo> getUserInfoDualListModel() {
        return userInfoDualListModel;
    }

    public void setUserInfoDualListModel(DualListModel<TsUserInfo> userInfoDualListModel) {
        this.userInfoDualListModel = userInfoDualListModel;
    }
}
