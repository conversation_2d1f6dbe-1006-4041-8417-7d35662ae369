package com.chis.modules.system.web;

import com.chis.common.utils.*;
import com.chis.modules.system.convert.DbVisionConvert;
import com.chis.modules.system.entity.TsSystemUpdate;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.logic.SQLSentence;
import com.chis.modules.system.logic.SQLSentences;
import com.chis.modules.system.service.PluginUpdateService;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 数据库版本管理Bean
 *
 * <AUTHOR>
 * @date 2021/11/30
 */
@ManagedBean(name = "dbVisionBean")
@ViewScoped
public class DbVisionBean extends FacesSimpleBean {

    protected PluginUpdateService pluginUpdateService = SpringContextHolder.getBean(PluginUpdateService.class);

    /**
     * 系统类别List
     */
    private List<DbVisionConvert> dbVisionConvertList;

    /**
     * 数据库版本管理页面展示
     */
    private DbVisionConvert dbVisionConvert;

    /**
     * 用于页面sql展示
     */
    SQLSentence viewSqlSentence;
    String viewSql;

    @Override
    public void searchAction() {

    }

    @Override
    public String[] buildHqls() {
        return new String[0];
    }

    public DbVisionBean() {
        init();
    }

    public void init() {
        dbVisionConvertList = new ArrayList<>();
        this.viewSqlSentence = new SQLSentence();
        this.viewSql = "";
        dbVisionConvert = new DbVisionConvert();

        int index = 1;
        //遍历系统类别枚举
        for (SystemType systemType : SystemType.values()) {
            //读取xml文件
            //先在新升级目录里找
            String path = "db/new/SQL_"+ systemType.getTypeNo() + ".xml";
            path = org.springframework.util.StringUtils.cleanPath(path);
            if (!new ClassPathResource(path).exists()) {
                //新升级目录里没有去旧目录里找
                path = "db/SQL_"+ systemType.getTypeNo() + ".xml";
                path = org.springframework.util.StringUtils.cleanPath(path);
                if (!new ClassPathResource(path).exists()) {
                    //文件不存在跳过
                    continue;
                }
            }
            SQLSentences sqlSentences = JaxbMapper.fileToObject(path, SQLSentences.class);

            //排除未使用的系统类别
            if (sqlSentences == null) {
                continue;
            }
            //从数据库获取对应系统类别的数据库版本信息
            TsSystemUpdate tsu = pluginUpdateService.findSystemUpdateByXml(systemType);
            DbVisionConvert dbVisionConvert = new DbVisionConvert();
            //dbVisionConvert初始化
            dbVisionConvert.init(index++, systemType, CollectionUtil.castList(SQLSentence.class, sqlSentences.getSqlList()), tsu);
            dbVisionConvertList.add(dbVisionConvert);
        }
    }

    /**
     * 升级数据库版本
     */
    public void updateDbVision() {
        if (dbVisionConvert != null) {
            //取出SQL
            List<SQLSentence> sqlList = dbVisionConvert.getSqlSentenceList();
            if (dbVisionConvert.getSystemType() == null) {
                return;
            }
            if (!CollectionUtils.isEmpty(sqlList)) {
                //获取数据库当前版本
                TsSystemUpdate tsu = pluginUpdateService.findSystemUpdateByXml(dbVisionConvert.getSystemType());
                int version = 0;
                if (null != tsu) {
                    version = tsu.getCurVersion();
                } else {
                    tsu = new TsSystemUpdate();
                    tsu.setParamType(dbVisionConvert.getSystemType().getTypeNo().intValue());
                }
                int listSize = sqlList.size();
                if (listSize >= version) {
                    String sql = "";
                    int ver = 0;
                    try {
                        for (int i = version; i < listSize; i++) {
                            SQLSentence sqlObj = sqlList.get(i);
                            if (null != sqlObj) {
                                sql = sqlObj.getSql();
                                ver = sqlObj.getVer();
                            }
                            // 如果sql语句为空时，则不执行
                            if (StringUtils.isNotBlank(sql)) {
                                pluginUpdateService.executeUpdate(sql, ver, false);
                            }
                            tsu.setCurVersion(ver);
                            tsu.setUpdateTime(new Date());
                            tsu = pluginUpdateService.updateSystemUpdate(tsu);
                        }
                    } catch (Exception e) {
                        StringBuilder errorMsg = new StringBuilder();
                        sql = sql.replaceAll("\n            ", "<br/>")
                                .replaceAll("\n         ", "")
                                .replaceAll(" ", "&nbsp;")
                                .replaceAll("^(&nbsp;<br/>)*", "")
                                .replaceAll("^(<br/>)*", "")
                                .replaceAll("\\s*", "");
                        errorMsg.append("版本号：").append(ver).append("<br/><br/>升级语句：").append(sql).append("<br/><br/>").append("错误信息：<br/>").append(e);
                        for (int j = 0; j < e.getStackTrace().length; j++) {
                            errorMsg.append("<br/>&nbsp;&nbsp;&nbsp;&nbsp;")
                                    .append(e.getStackTrace()[j].toString());
                        }
                        init();
                        dbVisionConvert.setErrors(errorMsg.toString());
                        RequestContext.getCurrentInstance().update(":mainForm:detailDialog");
                        RequestContext.getCurrentInstance().execute("PF('DetailDialog').show()");
                        JsfUtil.addErrorMessage("升级失败！");
                        return;
                    }
                    init();
                    JsfUtil.addSuccessMessage("升级成功！");
                }
            }
        }
    }

    /**
     * 处理页面显示SQL
     */
    public void showAllSql() {
        //重置页面弹出框当前页码
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent(":mainForm:allLatestSqlTable");
        if (dataTable != null) {
            dataTable.setFirst(0);
            dataTable.setRows(10);
        }
        RequestContext.getCurrentInstance().execute("PF('AllLatestSqlDialog').show()");
    }

    public void showSql() {
        this.viewSql = this.viewSqlSentence.getSql();
        if (StringUtils.isNotBlank(this.viewSql)) {
            this.viewSql = this.viewSql.replaceAll("\n            ", "<br/>")
                    .replaceAll("\n         ", "")
                    .replaceAll(" ", "&nbsp;")
                    .replaceAll("^(&nbsp;<br/>)*", "")
                    .replaceAll("^(<br/>)*", "")
                    .replaceAll("\\s*", "");
        }
        RequestContext.getCurrentInstance().execute("PF('ShowSql').show()");
    }

    public List<DbVisionConvert> getDbVisionConvertList() {
        return dbVisionConvertList;
    }

    public void setDbVisionConvertList(List<DbVisionConvert> dbVisionConvertList) {
        this.dbVisionConvertList = dbVisionConvertList;
    }

    public DbVisionConvert getDbVisionConvert() {
        return dbVisionConvert;
    }

    public void setDbVisionConvert(DbVisionConvert dbVisionConvert) {
        this.dbVisionConvert = dbVisionConvert;
    }

    public SQLSentence getViewSqlSentence() {
        return viewSqlSentence;
    }

    public void setViewSqlSentence(SQLSentence viewSqlSentence) {
        this.viewSqlSentence = viewSqlSentence;
    }

    public String getViewSql() {
        return viewSql;
    }

    public void setViewSql(String viewSql) {
        this.viewSql = viewSql;
    }
}
