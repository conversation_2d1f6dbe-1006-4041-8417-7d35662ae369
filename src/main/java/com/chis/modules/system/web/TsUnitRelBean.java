package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUnitRel;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;

@ManagedBean(name = "tsUnitRelBean")
@ViewScoped
public class TsUnitRelBean extends FacesEditBean {

	private static final long serialVersionUID = -738445454536625063L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	/** ejb session bean */
	private SystemModuleServiceImpl systemService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder
			.getBean(CommServiceImpl.class);
	/** 用户对象 */
	private TsUserInfo tsUserInfo = new TsUserInfo();
	/** 用户对象rid */
	private Integer rid;
	/** 地区列表 */
	private TsUnitRel tsUnitRel = new TsUnitRel();
	private List<TsZone> zoneList;
	private List<TsUnitRel> relTsUnitList;
	private boolean ifAdmin = Boolean.TRUE;;
    private List<String[]> mainList=new ArrayList<String[]>();
	private String[] relType;
	/** 查询条件：地区名称 */
	private String searchZoneName;
	/** 查询条件：地区编码 */
	private String searchZoneCode;
	/** 查询条件：地区级别 */
	private String searchZoneType;

	/** 添加主单位条件：地区名称 */
	private String mainZoneName;
	/** 添加主单位条件：地区编码 */
	private String mainZoneCode;
	/** 添加主单位条件：地区级别 */
	private String mainZoneType;

	private Integer searchUnitId;

	private Integer mainUnitId;

	private Integer relUnitId;

	private Integer relEditType;
	private Map<String, Integer> searchUnitMap = new HashMap<String, Integer>();

	private Map<String, Integer> mainUnitMap = new HashMap<String, Integer>();

	private Map<String, Integer> relUnitMap = new HashMap<String, Integer>();

	private Map<String, Integer> relMap = new HashMap<String, Integer>();

	private Integer checkMainUnitId;
   
    private String mainUnitName;
    
    private Integer relRid;
    
    private java.util.Date beginDate;
    private java.util.Date endDate;
	@Override
	public String[] buildHqls() {
		StringBuffer sb = new StringBuffer();
		sb.append("from  ts_unit_rel t inner join ts_unit t2 on t.unit_id=t2.rid ");
		sb.append("inner join ts_zone t3 on t2.zone_id=t3.rid where 1=1 ");
		if(searchUnitId!=null){
			sb.append( "and t.unit_id="+searchUnitId+" ");
		}
		StringBuffer h1 = new StringBuffer();
		h1.append("select distinct t.unit_id ,t.rel_type,t3.zone_name,t2.unitname ");
		h1.append(sb);
		StringBuffer h2 = new StringBuffer();
		h2.append("select count(*)  from (select distinct t.unit_id ,t.rel_type,t3.zone_name,t2.unitname ");
		h2.append(sb);
		h2.append(")");
		return new String[] { h1.toString(), h2.toString() };

	}

	public TsUnitRelBean() {
		this.init();
	}

	public void init() {
		TsUserInfo tsUserInfo = this.sessionData.getUser();
		if (!tsUserInfo.getUserNo().equals(Constants.ADMIN)) {
			ifAdmin = Boolean.FALSE;
		}
		super.ifSQL = Boolean.TRUE;
		/**
		 * 地区初始化
		 */
		if (null == this.zoneList || this.zoneList.size() <= 0) {
			this.zoneList = this.commService.findZoneListNew(this.ifAdmin,
					sessionData.getUser().getTsUnit().getTsZone().getZoneCode(),
					"1", "6");
		}
		this.searchAction();
		mainList=systemService.findRelUnitInfoByUnitId(rid);
	}
    
	/**
	 * 根据前台选择的地区获取当地监管机构
	 */
	public void onSearchNodeSelect() {
		this.searchUnitMap = this.filterUnit(this.searchZoneCode,
				this.searchZoneType);
	}

	/**
	 * 根据前台选择的地区获取当地监管机构
	 */
	public void onMainNodeSelect() {
		this.mainUnitMap = this
				.filterUnit(this.mainZoneCode, this.mainZoneType);

	}

	private Map<String, Integer> filterUnit(String zoneCode, String zoneType) {
		TsUserInfo user = this.sessionData.getUser();
		Map<String, Integer> map = new LinkedHashMap<String, Integer>();
		String[] sortcodes =new String[]{"2005","2006"};
		List<TsUnit> list = this.systemService.findUnitByZoneCodeSortCode(this.ifAdmin,
				user.getTsUnit().getTsZone().getZoneCode(), user.getTsUnit()
						.getRid(), zoneCode, zoneType,sortcodes);
		if (null != list && list.size() > 0) {
			for (TsUnit t : list) {
				map.put(t.getUnitname(), t.getRid());
			}
		}
		return map;
	}
    
	public void addRelAction() {
		if (relUnitId == null) {
			JsfUtil.addErrorMessage("请选择关系单位");

			return;
		}
		if (mainUnitId == null) {
			JsfUtil.addErrorMessage("请选择主单位");
			return;
			
		}
		if (relEditType == null) {
			JsfUtil.addErrorMessage("请选择关系类型");
			
			return;
		}
		if (relEditType == 1 && relTsUnitList.size() > 0) {
			JsfUtil.addErrorMessage("委托关系只能选择一个关系单位");
		
			return;
		}
		/**
		 * 判断主单位关系单位时候处在同一地区
		 */
		TsUnit mainUnit = new TsUnit();
		mainUnit = systemService.findUnitWithSort(mainUnitId);
		TsZone mainZone=mainUnit.getTsZone();
		TsUnit relUnit = new TsUnit();
		relUnit = systemService.findUnitWithSort(relUnitId);
		TsZone relZone=relUnit.getTsZone();
		if (mainZone.getZoneType() != relZone.getZoneType()) {
			JsfUtil.addErrorMessage("请选择相同级别单位！");
			return;

		}

		if(endDate!=null && endDate.compareTo(beginDate)==-1){
			JsfUtil.addErrorMessage("结束日期大于开始日期！");
			return;
		}
         
		/**
		 * 关系用户添加到list 中
		 */
		TsUnitRel unitRel = new TsUnitRel();
		unitRel.setFkByUnitId(mainUnit);
		unitRel.setFkByRelUnitId(relUnit);
		unitRel.setRelType(relEditType);
		unitRel.setBeginTime(beginDate);
		unitRel.setEndTime(endDate);
		relTsUnitList.add(unitRel);
		 RequestContext.getCurrentInstance().execute("PF('editRelDialog').hide()");
	}

	public void checkMainAction() {
		/** 获取主单位的地区信息 */
		if (checkMainUnitId != null &&checkMainUnitId != mainUnitId) {
			relUnitId = null;
			relTsUnitList.clear();
		}
		TsUnit tsUnit = new TsUnit();
		tsUnit = systemService.findUnitWithSort(mainUnitId);
		TsZone tsZone = tsUnit.getTsZone();
		Short zoneType = tsZone.getZoneType();
		/**
		 * 提示选择市级以下单位
		 */

		if (zoneType < 4) {
			JsfUtil.addErrorMessage("请选择市级以下单位！");
		}
		if (zoneType == 4) {
			relMap.clear();
			relMap.put("委托", 1);
		} else if (zoneType == 5) {
			relMap.clear();
			relMap.put("代管", 2);
		}
		checkMainUnitId = mainUnitId;
	}

	@Override
	public void addInit() {
		relMap = new HashMap<>();
		rid = null;
		beginDate = null;
		endDate = null;
		tsUnitRel = new TsUnitRel();
		checkMainUnitId = null;
		mainUnitId = null;
		relUnitId = null;
		mainUnitMap = new HashMap<>();
		relUnitMap = new HashMap<>();
		relTsUnitList = new ArrayList<TsUnitRel>();
		mainZoneName = null;
		mainZoneCode = null;
		mainZoneType = null;
		relEditType = null;
	}

	@Override
	public void viewInit() {
		relTsUnitList=new ArrayList<>();
    	TsUnit mainUnit=new TsUnit();
    	
    	mainUnit=systemService.findUnitWithSort(rid);
    	mainUnitName=mainUnit.getUnitname();
    	TsZone tsZone = mainUnit.getTsZone();
    	mainZoneName=tsZone.getZoneName();
    	if(relEditType == 1){
    		relMap.clear();
			relMap.put("委托", 1);
    	}else{
    		relMap.clear();
			relMap.put("代管", 2);
    	}
    	relTsUnitList =systemService.findRelUnitList(mainUnit, relEditType);
	}
	
	@Override
	public void modInit() {
        mainUnitId=rid;
    	relTsUnitList=new ArrayList<>();
    	TsUnit mainUnit=new TsUnit();
    	
    	mainUnit=systemService.findUnitWithSort(rid);
    	mainUnitName=mainUnit.getUnitname();
    	
    	TsZone tsZone = mainUnit.getTsZone();
    	mainZoneName=tsZone.getZoneName();
    	mainZoneCode=tsZone.getZoneCode();
    	mainZoneType=String.valueOf(tsZone.getZoneType()-1);
    	if("4".equals(mainZoneType)){
    		mainZoneCode=mainZoneCode.substring(0, 6)+"0000";
    	}else if("3".equals(mainZoneType)){
    		mainZoneCode=mainZoneCode.substring(0, 4)+"000000";
    	}
    	if(relEditType==1){
    		relMap.clear();
			relMap.put("委托", 1);
    	}else{
    		relMap.clear();
			relMap.put("代管", 2);
    	}
    	relTsUnitList =systemService.findRelUnitList(mainUnit, relEditType);
    	for(TsUnitRel rel:relTsUnitList){
    		rel.setRid(null);
    	}
    	
	}

	@Override
	public void saveAction() {
		if(relTsUnitList!=null&&relTsUnitList.size()==0&&rid==null){
			JsfUtil.addErrorMessage("请先添加关系单位");
			return;
		}
		TsUnit mainUnit=systemService.findUnitWithSort(mainUnitId);
		if(rid==null){
			
		  List<TsUnitRel> list=systemService.findRelUnitList(mainUnit, relEditType)	;
		  if(list.size()>0){
			  JsfUtil.addErrorMessage("该单位已存在关联关系，请查询后修改！");
				return;
		  }
		}
		if(mainUnitId!=null&&relEditType!=null){
			systemService.deleteTsUnitRelAndSave(mainUnitId,relEditType,relTsUnitList);
			 JsfUtil.addSuccessMessage("保存成功！");
		}
		
		
		
		this.searchAction();

		

	}
	
	public void deleteAction(){
		systemService.deleteTsUnitRelAndSave(rid,relEditType,null);
		
	}
     public void deleteRelAction(){

    	relTsUnitList.remove(tsUnitRel);
     }
      
     /**
      * 添加关系初始化
      */
     public void addRelInit(){
		relUnitId=null;
		beginDate=null;
		endDate=null;
		if(mainUnitId==null){
			JsfUtil.addErrorMessage("请先选择要委托或代管的单位");
			return;
		}
		this.relUnitMap = this.filterUnit(this.mainZoneCode, this.mainZoneType);
		 RequestContext.getCurrentInstance().execute("PF('editRelDialog').show()");
     }
     
	public TsUserInfo getTsUserInfo() {
		return tsUserInfo;
	}

	public void setTsUserInfo(TsUserInfo tsUserInfo) {
		this.tsUserInfo = tsUserInfo;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public boolean isIfAdmin() {
		return ifAdmin;
	}

	public void setIfAdmin(boolean ifAdmin) {
		this.ifAdmin = ifAdmin;
	}

	public String getSearchZoneName() {
		return searchZoneName;
	}

	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}

	public String getSearchZoneCode() {
		return searchZoneCode;
	}

	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}

	public String getSearchZoneType() {
		return searchZoneType;
	}

	public void setSearchZoneType(String searchZoneType) {
		this.searchZoneType = searchZoneType;
	}

	public Map<String, Integer> getSearchUnitMap() {
		return searchUnitMap;
	}

	public void setSearchUnitMap(Map<String, Integer> searchUnitMap) {
		this.searchUnitMap = searchUnitMap;
	}

	public Integer getSearchUnitId() {
		return searchUnitId;
	}

	public void setSearchUnitId(Integer searchUnitId) {
		this.searchUnitId = searchUnitId;
	}

	public TsUnitRel getTsUnitRel() {
		return tsUnitRel;
	}

	public void setTsUnitRel(TsUnitRel tsUnitRel) {
		this.tsUnitRel = tsUnitRel;
	}

	public String getMainZoneName() {
		return mainZoneName;
	}

	public void setMainZoneName(String mainZoneName) {
		this.mainZoneName = mainZoneName;
	}

	public String getMainZoneCode() {
		return mainZoneCode;
	}

	public void setMainZoneCode(String mainZoneCode) {
		this.mainZoneCode = mainZoneCode;
	}

	public String getMainZoneType() {
		return mainZoneType;
	}

	public void setMainZoneType(String mainZoneType) {
		this.mainZoneType = mainZoneType;
	}



	public Integer getMainUnitId() {
		return mainUnitId;
	}

	public void setMainUnitId(Integer mainUnitId) {
		this.mainUnitId = mainUnitId;
	}

	public Integer getRelUnitId() {
		return relUnitId;
	}

	public void setRelUnitId(Integer relUnitId) {
		this.relUnitId = relUnitId;
	}

	public Map<String, Integer> getMainUnitMap() {
		return mainUnitMap;
	}

	public void setMainUnitMap(Map<String, Integer> mainUnitMap) {
		this.mainUnitMap = mainUnitMap;
	}

	public Map<String, Integer> getRelUnitMap() {
		return relUnitMap;
	}

	public void setRelUnitMap(Map<String, Integer> relUnitMap) {
		this.relUnitMap = relUnitMap;
	}

	public List<TsUnitRel> getRelTsUnitList() {
		return relTsUnitList;
	}

	public void setRelTsUnitList(List<TsUnitRel> relTsUnitList) {
		this.relTsUnitList = relTsUnitList;
	}

	public Map<String, Integer> getRelMap() {
		return relMap;
	}

	public void setRelMap(Map<String, Integer> relMap) {
		this.relMap = relMap;
	}

	public String[] getRelType() {
		return relType;
	}

	public void setRelType(String[] relType) {
		this.relType = relType;
	}

	public Integer getRelEditType() {
		return relEditType;
	}

	public void setRelEditType(Integer relEditType) {
		this.relEditType = relEditType;
	}

	public List<String[]> getMainList() {
		return mainList;
	}

	public void setMainList(List<String[]> mainList) {
		this.mainList = mainList;
	}

	public String getMainUnitName() {
		return mainUnitName;
	}

	public void setMainUnitName(String mainUnitName) {
		this.mainUnitName = mainUnitName;
	}

	public Integer getRelRid() {
		return relRid;
	}

	public void setRelRid(Integer relRid) {
		this.relRid = relRid;
	}

	public java.util.Date getBeginDate() {
		return beginDate;
	}

	public void setBeginDate(java.util.Date beginDate) {
		this.beginDate = beginDate;
	}

	public java.util.Date getEndDate() {
		return endDate;
	}

	public void setEndDate(java.util.Date endDate) {
		this.endDate = endDate;
	}
     
}
