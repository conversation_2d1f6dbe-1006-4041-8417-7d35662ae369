package com.chis.modules.system.web;

import java.util.List;

import org.springframework.stereotype.Component;

import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.logic.LucenePojo;
import com.chis.modules.system.service.CommServiceImpl;

@Component
public class KleSearchFileBean extends LuceneFileAbstract{

	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	
	@Override
	public List<LucenePojo> findSearchList() {
		return commService.findFileList();
	}

	@Override
	public LuceneBusinessType findBusinessType() {
		return LuceneBusinessType.SYS_RES;
	}

	@Override
	public String powerSearchStr() {
		// TODO Auto-generated method stub
		return null;
	}

}
