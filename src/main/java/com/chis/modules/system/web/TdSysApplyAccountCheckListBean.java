package com.chis.modules.system.web;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.util.*;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;

import com.chis.common.utils.*;
import com.chis.modules.system.entity.*;
import com.chis.modules.system.interfaces.IProcessData;
import org.primefaces.context.RequestContext;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.util.CollectionUtils;

import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.RoleTypePoNew;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;

/**
 * <p>类描述：注册账号审核</p>
 * @ClassAuthor qrr,2020年7月31日,TdSysApplyAccountCheckListBean
 * */
@ManagedBean(name = "tdSysApplyAccountCheckListBean")
@ViewScoped
public class TdSysApplyAccountCheckListBean extends FacesEditBean implements IProcessData {
	private static final long serialVersionUID = 1L;
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private SystemModuleServiceImpl serviceImpl = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	
	/**查询条件：地区名称*/
    private String searchZoneName;
    /**查询条件：地区编码*/
    private String searchZoneCode;
    /**查询条件：地区集合*/
    private List<TsZone> zoneList;
    /**查询条件：社会信用代码*/
    private String searchCreditCode;
    /**查询条件：单位名称*/
    private String searchUnitName;
    /**查询条件：姓名*/
    private String searchPersonName;
    /**查询条件：身份证号*/
    private String searchIdc;
    /** 选中的状态 */
    private String[] states;
	
    private Integer rid;
    /**行政所在地区，市_区_街道*/
    private String zoneName;
    private TdSysApplyAccount applyAccount;
    private String sortNames;
    private List<TdSysApplySort> applySorts;
    private List<TdSysApplyAnnex> annexs;
    /**角色授权*/
    private List<RoleTypePoNew> roleList;
    private String allRoleName;
    private Date validBegDate;
    private Date validEndDate;
    /**角色map,key:角色Id,value:TsRole*/
    private Map<Integer, TsRole> roleMap = new HashMap<Integer, TsRole>();

	private Map<String, Integer> sortTypeMap = new LinkedHashMap<String, Integer>();
	private Integer sortId;
	/** 退回原因 */
	private String backRsn;
	private Integer defaultValidYear;

	/** 区分注册账号审核与注册账号查询的tag tag null或者1 注册账号审核 tag为2 注册账号查询 */
	private String tag;
	public TdSysApplyAccountCheckListBean(){
		this.ifSQL = true;
		this.states = new String[]{"0"};
		HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
		tag = request.getParameter("tag");//获取tag 区分审核或者查询

		this.defaultValidYear = ObjectUtil.convert(Integer.class, PropertyUtils.getValueWithoutException("account.defaultValidYear"));

		initSortList();
		initZone();
		this.searchAction();
	}

	public void initSortList() {
		sortTypeMap = new LinkedHashMap<String, Integer>();
		StringBuffer hql = new StringBuffer();
		hql.append(" select t from TbSysApplyAccount t  order by t.num ");
		List<TbSysApplyAccount> sortList = commService.findByHql(hql.toString(), TbSysApplyAccount.class);
		if (!CollectionUtils.isEmpty(sortList)) {
			for(TbSysApplyAccount account:sortList){
				TsBsSort sort = account.getFkBySortId();
				if(null != sort){
					sortTypeMap.put(sort.getSortName(),sort.getRid());
				}

			}
		}
	}
	/**
 	 * <p>方法描述：初始化查询条件地区</p>
 	 * @MethodAuthor qrr,2020年7月31日,initZone
	 * */
	private void initZone() {
		TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		if (null==tsZone) {
			tsZone = Global.getUser().getTsUnit().getTsZone();
		}
        this.searchZoneCode = tsZone.getZoneCode();
        this.searchZoneName = tsZone.getZoneName();
        this.zoneList = this.commService.findZoneList(false, tsZone.getZoneGb(), null, null);
	}
	@Override
	public void modInit() {
		initMod();
		
		if (1!=applyAccount.getStateMark()) {
			this.validBegDate = DateUtils.getDateOnly(new Date());
			this.validEndDate = null;
			if (this.defaultValidYear != null) {
				this.validEndDate = DateUtils.addYears(this.validBegDate, this.defaultValidYear);
			}
			Map<Integer, TsRole> configRoleMap = findConfigRoleMap();
			initRole(configRoleMap);
			
		}else if (1==applyAccount.getStateMark()) {
			//根据账号获取用户信息、角色
			TsUserInfo userInfo = serviceImpl.findUserByUserNo(this.applyAccount.getUserNo());
			if(null != userInfo){
				this.validBegDate = userInfo.getValidBegDate();
				this.validEndDate = userInfo.getValidEndDate();

				Map<Integer, TsRole> roleMap = new HashMap<>();
				List<TsUserRole> tsUserRoles = userInfo.getTsUserRoles();
				for (TsUserRole t : tsUserRoles) {
					roleMap.put(t.getTsRole().getRid(), t.getTsRole());
				}
				initRole(roleMap);
			}
		}
		
	}
	
	/**
 	 * <p>方法描述：查询注册信息</p>
 	 * @MethodAuthor qrr,2020年8月1日,initMod
	 * */
	private void initMod() {
		this.applyAccount = serviceImpl.find(TdSysApplyAccount.class, this.rid);
		this.annexs = serviceImpl.findEntityListByMainId(TdSysApplyAnnex.class, this.rid);
		this.applySorts = serviceImpl.findEntityListByMainId(TdSysApplySort.class, this.rid);
		this.sortNames = null;
		if (!CollectionUtils.isEmpty(applySorts)) {
			StringBuffer sortNameSb = new StringBuffer();
			StringBuffer sortIdSb = new StringBuffer();
			for (TdSysApplySort t : applySorts) {
				sortNameSb.append("，").append(t.getFkBySortId().getFkBySortId().getSortName());
				sortIdSb.append(",").append(t.getFkBySortId().getFkBySortId().getRid());
			}
			this.sortNames = sortNameSb.substring(1);
		}
		TsZone zone = this.applyAccount.getFkByZoneId();
		if (null != zone.getZoneType() && zone.getZoneType().intValue() > 3) {
			//市_区_街道
			String fullName = zone.getFullName();
			this.zoneName = fullName.substring(fullName.indexOf("_")+1);
		}else {
			this.zoneName = zone.getZoneName();
		}
		this.backRsn = null;

		applyAccount.setTempIdc(StringUtils.encryptIdc(applyAccount.getIdc()));

	}
	/**
 	 * <p>方法描述：根据单位属性查询已配置的角色</p>
 	 * @MethodAuthor qrr,2020年8月1日,findConfigRoleMap
	 * */
	private Map<Integer, TsRole> findConfigRoleMap() {
		Map<Integer, TsRole> configRoleMap = new HashMap<Integer, TsRole>();
		if (!CollectionUtils.isEmpty(applySorts)) {
			StringBuffer sortIdSb = new StringBuffer();
			for (TdSysApplySort t : applySorts) {
				sortIdSb.append(",").append(t.getFkBySortId().getRid());
			}
			String sortIds = sortIdSb.substring(1);
			List<TbSysApplyRole> applyRoles = serviceImpl.findApplyRole(sortIds);
			for (TbSysApplyRole t : applyRoles) {
				configRoleMap.put(t.getFkByRoleId().getRid(), t.getFkByRoleId());
			}
		}
		return configRoleMap;
	}
	/**
 	 * <p>方法描述：初始化角色</p>
 	 * @MethodAuthor qrr,2020年7月31日,initRole
	 * */
	private void initRole(Map<Integer, TsRole> selectRoleMap) {
		this.roleList = new ArrayList<RoleTypePoNew>();
		this.roleMap = new HashMap<>();
		this.allRoleName = "";
		List<String> roleNameList = new ArrayList<>();
		List<TsSimpleCode> list = commService.findSimpleCodesByTypeId("1201");
		List<TsRolePower> rolePowers = serviceImpl.findTsRoles(Global.getUser().getUserNo().equals(Constants.ADMIN));
		if (!CollectionUtils.isEmpty(list)) {
			for (TsSimpleCode t : list) {
				RoleTypePoNew typo = new RoleTypePoNew();
				typo.setRoleType(t);
				typo.setIfSelected(false);
				
				if (!CollectionUtils.isEmpty(rolePowers)) {
					for (TsRolePower rolePower : rolePowers) {
						TsSimpleCode roleType = rolePower.getFkByRoleTypeId();
						if (roleType.getRid().intValue() != t.getRid().intValue()) {
							continue;
						}
						TsRole role = rolePower.getFkByRoleId();
						this.roleMap.put(role.getRid(), role);
						if (null!=selectRoleMap.get(role.getRid())) {
							typo.setIfSelected(true);
							typo.getItemSubs().add(role.getRid().toString());
							roleNameList.add(StringUtils.objectToString(role.getRoleName()));
						}
						typo.getRolePoList().add(role);
					}
				}
				roleList.add(typo);
			}
			for (RoleTypePoNew t : roleList) {
				for (TsRole role : t.getRolePoList()) {
					if (t.isIfSelected()) {//父级选择
						role.setDisabled(false);
					}else {
						role.setDisabled(true);
					}
				}
			}
		}
		this.allRoleName = StringUtils.list2string(roleNameList, "、");
	}
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2020年7月31日,roleChangeAction
	 * */
	public void roleChangeAction() {
		if(null != roleList && roleList.size() >0){
			for(RoleTypePoNew typo :roleList){
				if(typo.isIfSelected()){
					List<TsRole> rolePoList = typo.getRolePoList();
					if(null != rolePoList && rolePoList.size() >0){
						for(TsRole role :rolePoList){
							role.setDisabled(false);
						}
					}
					
				}else{
					typo.setItemSubs(null);
					List<TsRole> rolePoList = typo.getRolePoList();
					if(null != rolePoList && rolePoList.size() >0){
						for(TsRole role :rolePoList){
							role.setDisabled(true);
						}
					}
				}
			}
		}
	}
	
	/**
 	 * <p>方法描述：审核前验证</p>
 	 * @MethodAuthor qrr,2020年7月31日,beforeSaveAction
	 * */
	public void beforeSaveAction() {
		boolean flag = false;
		if (null==validEndDate) {
			JsfUtil.addErrorMessage("账号有效期结束日期不能为空！");
			flag = true;
		}
		if (null!=validBegDate && null!=validEndDate) {
			if (validEndDate.before(validBegDate)) {
				JsfUtil.addErrorMessage("账号有效期开始日期应小于等于结束日期！");
				flag = true;
			}
		}
		if (!CollectionUtils.isEmpty(roleList)) {
			boolean notNull = false;
			for (RoleTypePoNew t : roleList) {
				if (!CollectionUtils.isEmpty(t.getItemSubs())) {
					notNull = true;
				}
			}
			if (!notNull) {
				JsfUtil.addErrorMessage("请至少选择一个角色！");
				flag = true;
			}
		}
		if (flag) {
			return;
		}
		RequestContext.getCurrentInstance().execute("PF('ReviewConfirmDialog').show();");
	}
	/**
 	 * <p>方法描述：审核通过</p>
 	 * @MethodAuthor qrr,2020年7月31日,saveAction
	 * */
	@Override
	public void saveAction() {
		try {
			serviceImpl.saveTdSysApplyAccount(applyAccount, applySorts,
					validBegDate, validEndDate, roleList, this.roleMap,Global.getUser().getUserNo().equals(Constants.ADMIN));
			this.searchAction();
			this.backAction();
			RequestContext.getCurrentInstance().update("tabView");
			JsfUtil.addSuccessMessage("审核成功！");
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("审核失败！");
		}
	}
	/**
 	 * <p>方法描述：退回</p>
 	 * @MethodAuthor qrr,2020年7月31日,returnAction
	 * */
	public void returnAction() {
		try {
			if (StringUtils.isBlank(backRsn)) {
				JsfUtil.addErrorMessage("退回原因不能为空！");
				return;
			} else if (backRsn.length() > 50) {
				JsfUtil.addErrorMessage("退回原因长度不能超过50！");
				return;
			}
			this.applyAccount.setStateMark(2);
			this.applyAccount.setFkByCheckUserId(Global.getUser());
			this.applyAccount.setBackRsn(backRsn);
			this.serviceImpl.upsertEntity(this.applyAccount);
			JsfUtil.addSuccessMessage("退回成功！");
			this.searchAction();
			this.backAction();
			RequestContext context = RequestContext.getCurrentInstance();
			context.execute("PF('ReasonDialog').hide();");
			context.update("tabView");
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("退回失败！");
		}
	}
	@Override
	public String[] buildHqls() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select T.RID,CASE WHEN t4.ZONE_TYPE >2 THEN SUBSTR(t4.FULL_NAME, INSTR(t4.FULL_NAME,'_')+1) ELSE t4.FULL_NAME END ZONE_NAME");
		sql.append(",t.CREDIT_CODE,t.UNITNAME,WM_CONCAT(t3.SORT_NAME),T.USERNAME,T.IDC,T.MOBILE_NUM,T.STATE_MARK,t4.ZONE_GB");
		sql.append(" from TD_SYS_APPLY_ACCOUNT t");
		sql.append(" left join TD_SYS_APPLY_SORT t1 on t1.MAIN_ID = t.rid");
		sql.append(" left join TB_SYS_APPLY_ACCOUNT t2 on t1.SORT_ID = t2.rid");
		sql.append(" left join TS_BS_SORT t3 on t2.SORT_ID = t3.rid");
		sql.append(" left join TS_ZONE t4 on t.ZONE_ID = t4.rid");
		sql.append(" WHERE 1=1 AND T.STATE_MARK IN (0,1,2)");
		if (StringUtils.isNotBlank(this.searchZoneCode)) {
			sql.append(" AND t4.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%'");
        }
		//	社会信用代码
		if (StringUtils.isNotBlank(this.searchCreditCode)) {
			sql.append(" AND t.CREDIT_CODE LIKE :searchCreditCode escape '\\\'");
            this.paramMap.put("searchCreditCode", "%"+StringUtils.convertBFH(this.searchCreditCode.trim()) + "%");
        }
		// 单位名称
		if (StringUtils.isNotBlank(this.searchUnitName)) {
			sql.append(" AND t.UNITNAME LIKE :searchUnitName escape '\\\'");
            this.paramMap.put("searchUnitName", "%"+StringUtils.convertBFH(this.searchUnitName.trim()) + "%");
        }
        if(null != sortId){
            sql.append(" And T3.RID = ").append(sortId);
        }
		// 姓名
		if (StringUtils.isNotBlank(this.searchPersonName)) {
			sql.append(" AND t.USERNAME LIKE :searchPersonName escape '\\\'");
            this.paramMap.put("searchPersonName", "%"+StringUtils.convertBFH(this.searchPersonName.trim()) + "%");
        }
		// 身份证号
		if (StringUtils.isNotBlank(this.searchIdc)) {
			sql.append(" AND t.IDC LIKE :searchIdc escape '\\\'");
            this.paramMap.put("searchIdc", "%"+StringUtils.convertBFH(this.searchIdc.trim()) + "%");
        }
		// 状态
		if (null != states && states.length > 0) {
            StringBuffer stateSb = new StringBuffer();
            for (String state : states) {
                stateSb.append(",").append(state);
            }
            sql.append(" AND T.STATE_MARK IN (").append(stateSb.substring(1)).append(")");
		}
		sql.append(" group by T.RID,t4.ZONE_TYPE,t4.FULL_NAME,t.CREDIT_CODE,t.UNITNAME,T.USERNAME,T.IDC,T.MOBILE_NUM,T.STATE_MARK,t4.ZONE_GB");
		String h2 = "SELECT COUNT(*) FROM (" + sql.toString() + ")";
        String h1 = "SELECT * FROM (" + sql.toString() + ")AA ORDER BY AA.ZONE_GB, AA.UNITNAME";
        return new String[] { h1, h2 };
	}

	@Override
	public void processData(List<?> list) {
		if (list != null && list.size() > 0) {
			List<Object[]> tempList = (List<Object[]>) list;
			for (Object[] objects : tempList) {
				if (null != objects[6]) {
					objects[6] = StringUtils.encryptIdc(objects[6].toString());
				}
			}

		}
	}

	@Override
	public void addInit() {
		// TODO Auto-generated method stub
		
	}
	@Override
	public void viewInit() {
		initMod();
		if (1==applyAccount.getStateMark()) {
			//根据账号获取用户信息、角色
			TsUserInfo userInfo = serviceImpl.findUserByUserNo(this.applyAccount.getUserNo());
			if(null != userInfo){
				this.validBegDate = userInfo.getValidBegDate();
				this.validEndDate = userInfo.getValidEndDate();

				Map<Integer, TsRole> roleMap = new HashMap<>();
				List<TsUserRole> tsUserRoles = userInfo.getTsUserRoles();
				for (TsUserRole t : tsUserRoles) {
					roleMap.put(t.getTsRole().getRid(), t.getTsRole());
				}
				initRole(roleMap);
			}
		}
	}
	public String getSearchZoneName() {
		return searchZoneName;
	}
	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}
	public String getSearchZoneCode() {
		return searchZoneCode;
	}
	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}
	public List<TsZone> getZoneList() {
		return zoneList;
	}
	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}
	public String getSearchCreditCode() {
		return searchCreditCode;
	}
	public void setSearchCreditCode(String searchCreditCode) {
		this.searchCreditCode = searchCreditCode;
	}
	public String getSearchUnitName() {
		return searchUnitName;
	}
	public void setSearchUnitName(String searchUnitName) {
		this.searchUnitName = searchUnitName;
	}
	public String getSearchPersonName() {
		return searchPersonName;
	}
	public void setSearchPersonName(String searchPersonName) {
		this.searchPersonName = searchPersonName;
	}
	public String getSearchIdc() {
		return searchIdc;
	}
	public void setSearchIdc(String searchIdc) {
		this.searchIdc = searchIdc;
	}
	public String[] getStates() {
		return states;
	}
	public void setStates(String[] states) {
		this.states = states;
	}
	public Integer getRid() {
		return rid;
	}
	public void setRid(Integer rid) {
		this.rid = rid;
	}
	public String getBackRsn() {
		return backRsn;
	}
	public void setBackRsn(String backRsn) {
		this.backRsn = backRsn;
	}
	public TdSysApplyAccount getApplyAccount() {
		return applyAccount;
	}
	public void setApplyAccount(TdSysApplyAccount applyAccount) {
		this.applyAccount = applyAccount;
	}
	public String getSortNames() {
		return sortNames;
	}
	public void setSortNames(String sortNames) {
		this.sortNames = sortNames;
	}
	public List<TdSysApplyAnnex> getAnnexs() {
		return annexs;
	}
	public void setAnnexs(List<TdSysApplyAnnex> annexs) {
		this.annexs = annexs;
	}
	public List<RoleTypePoNew> getRoleList() {
		return roleList;
	}
	public void setRoleList(List<RoleTypePoNew> roleList) {
		this.roleList = roleList;
	}

	public String getAllRoleName() {
		return allRoleName;
	}

	public void setAllRoleName(String allRoleName) {
		this.allRoleName = allRoleName;
	}

	public String getZoneName() {
		return zoneName;
	}
	public void setZoneName(String zoneName) {
		this.zoneName = zoneName;
	}
	public Date getValidBegDate() {
		return validBegDate;
	}
	public void setValidBegDate(Date validBegDate) {
		this.validBegDate = validBegDate;
	}
	public Date getValidEndDate() {
		return validEndDate;
	}
	public void setValidEndDate(Date validEndDate) {
		this.validEndDate = validEndDate;
	}

	public Map<String, Integer> getSortTypeMap() {
		return sortTypeMap;
	}

	public void setSortTypeMap(Map<String, Integer> sortTypeMap) {
		this.sortTypeMap = sortTypeMap;
	}

	public Integer getSortId() {
		return sortId;
	}

	public void setSortId(Integer sortId) {
		this.sortId = sortId;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}
}
