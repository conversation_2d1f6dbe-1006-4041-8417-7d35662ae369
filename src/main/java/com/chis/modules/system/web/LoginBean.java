package com.chis.modules.system.web;

import java.io.IOException;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;

import org.hibernate.validator.constraints.NotBlank;
import org.primefaces.context.RequestContext;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MD5Util;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemServiceImpl;

/**
 * 登录的受管bean
 * <AUTHOR>
 */
@ManagedBean(name="loginBean")
@ViewScoped
public class LoginBean implements Serializable{

    private static final long serialVersionUID = -3740326227684495169L;
    @NotBlank(message="用户名不允许为空！")
    private String userno ;
    @NotBlank(message="密码不允许为空！")
    private String password;
    @NotBlank(message="验证码不允许为空！")
    private String verifyCode;
    private String newpassword;
	private String repassword;
	 private String pwdLevel;

    private SystemServiceImpl systemService = (SystemServiceImpl)SpringContextHolder.getBean(SystemServiceImpl.class);
    private CommServiceImpl commService = (CommServiceImpl)SpringContextHolder.getBean(CommServiceImpl.class);
    /**存在session中的对象*/
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private String value;
    private TsUserInfo user;

    public LoginBean() {
    	value = commService.findParamValue("STRONG_PASSWORD");
    }

    /**
     * 1.验证码是否正确
     * 2.验证用户名密码
     * @return
     */
    public void loginAction() {
        HttpServletRequest req = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        //验证码
        String sessionString = (String) req.getSession().getAttribute("sessionString");

        if(StringUtils.isBlank(this.verifyCode)) {
            JsfUtil.addErrorMessage("请输入正确的验证码！");
            RequestContext.getCurrentInstance().execute("setVerImg()");
            return;
        }else {
            if(!this.verifyCode.trim().equalsIgnoreCase(sessionString.trim())) {
                JsfUtil.addErrorMessage("请输入正确的验证码！");
                RequestContext.getCurrentInstance().execute("setVerImg()");
                return;
            }
        }

         user = this.systemService.findUserByUserNo(this.userno);
         
        if(null != user) {
        	System.out.println(new MD5Util().getMD5ofStr(this.password));
            if(user.getPassword().equalsIgnoreCase(new MD5Util().getMD5ofStr(this.password))) {
                if(user.getIfReveal().intValue() == 1) {
                	if(user.getValidEndDate() != null && DateUtils.isDateAfter((new Date()),user.getValidEndDate())){
                        JsfUtil.addErrorMessage("当前账号已过期，请联系管理员！");
                        RequestContext.getCurrentInstance().execute("setVerImg()");
                        return;
                	}
                    //登录成功
                	SessionData sessionData = new SessionData(user);
                	Map<String, String> btnMap = this.systemService.findUsersBtn(user.getRid());
                	sessionData.setButtonMap(btnMap);
                	sessionData.setBtnSet(systemService.findUserBtns(user.getRid()));
                	sessionData.setIsZZpx(0);
                    req.getSession().setAttribute(SessionData.SESSION_DATA, sessionData);
                } else {
                	RequestContext.getCurrentInstance().execute("setVerImg()");
                    JsfUtil.addErrorMessage("该用户已被禁用！");
                    return;
                }
            }else {
            	RequestContext.getCurrentInstance().execute("setVerImg()");
                JsfUtil.addErrorMessage("请输入正确的密码！");
                return;
            }
        }else {
        	RequestContext.getCurrentInstance().execute("setVerImg()");
        		JsfUtil.addErrorMessage("请输入正确的用户名！");
        		return;
        }
        if (value.equals("1") && ((StringUtils.isNotBlank(password)&&password.equals("000000"))||(user!=null && user.getIfModpsw()!=null && String.valueOf(user.getIfModpsw()).equals("0")))) {
        	JsfUtil.addErrorMessage("初始密码级别过低，密码要求为长度不少于6位且为数字加字符！");
			RequestContext.getCurrentInstance().execute("PF('dlg').show();setFlag(1)");
			return;
		}
        ExternalContext extContext = FacesContext.getCurrentInstance().getExternalContext();
        try {
            extContext.redirect("head.faces");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    public void savePassword() {
		try {
				if (StringUtils.isBlank(this.newpassword)) {
					JsfUtil.addErrorMessage("新的密码不允许为空！");
					return;
				}
				if (StringUtils.isBlank(this.repassword)) {
					JsfUtil.addErrorMessage("确认密码不允许为空！");
					return;
				}
				//密码级别过低时是否保存
	        	if (value.equals("1")&& pwdLevel.equals("弱")) {
	        		JsfUtil.addErrorMessage("您输入的密码不符合安全强度要求，密码要求为长度不少于6位且为数字加字符！");
	        		return;
	        	}
				if (this.newpassword.length() < 6) {
					JsfUtil.addErrorMessage("输入密码长度最少不低于6位数！");
					return;
				}
				if (!this.newpassword.equals(this.repassword)) {
					JsfUtil.addErrorMessage("密码不一致，请重新输入密码！");
					return;

				}
				user.setPassword(newpassword);
				systemService.updUserPwd(user);
				//RequestContext.getCurrentInstance().execute("PF('dlg').hide();setFlag(0)");
				JsfUtil.addSuccessMessage("密码修改成功！");
				password = newpassword;
				ExternalContext extContext = FacesContext.getCurrentInstance().getExternalContext();
		        try {
		            extContext.redirect("head.faces");
		        } catch (IOException e) {
		            e.printStackTrace();
		        }
//			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

    /**
     * 浏览器判断不合格跳转至友情页面
     * @return
     */
    public String browserGo(){
        return "browser.faces";
    }

    /**
     * 超时页面跳转
     * @return
     */
    public String logOutGo(){return "logout.faces";}
    public String getUserno() {
        return userno;
    }

    public void setUserno(String userno) {
        this.userno = userno;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

	public String getNewpassword() {
		return newpassword;
	}

	public void setNewpassword(String newpassword) {
		this.newpassword = newpassword;
	}

	public String getRepassword() {
		return repassword;
	}

	public void setRepassword(String repassword) {
		this.repassword = repassword;
	}

	public String getPwdLevel() {
		return pwdLevel;
	}

	public void setPwdLevel(String pwdLevel) {
		this.pwdLevel = pwdLevel;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

}
