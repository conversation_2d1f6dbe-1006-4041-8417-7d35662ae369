package com.chis.modules.system.web;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.TdTjExportService;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.io.*;
import java.net.URLEncoder;
import java.sql.Clob;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ManagedBean(name="tdTjExportListBean")
@ViewScoped
public class tdTjExportListBean extends FacesEditBean implements IProcessData {
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final TdTjExportService tdTjExportService = SpringContextHolder.getBean(TdTjExportService.class);
    /**查询条件-导出类别*/
    private String searchExportTypeId;
    /**导出类型为码表5550*/
    private List<TsSimpleCode> exportTypeList;
    /**查询条件-导出时间*/
    private Date searchExportSDate;
    private Date searchExportEDate;
    /**查询条件-导出状态*/
    private List<String> searchState;
    /**是否是超管*/
    private Boolean ifAdmin=Boolean.TRUE;
    /**存在session中的对象*/
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    /**当前登录的用户对象*/
    private TsUserInfo tsUserInfo;
    /**异步导出的rid*/
    private Integer rid;
    /**导出文件下载对象*/
    private String fileName;
    private String filePath;
    /**导出文件下载 */
    private StreamedContent downLoadExport;
    private String exportCondition;
    private List<String> conditionList;

    /**文件保留的月份*/
    private Integer exportFileLivemonth;

    public tdTjExportListBean() {
        this.ifSQL = true;
        init();
        this.searchAction();
        conditionList=new ArrayList<>();
    }

    public void init(){
        tsUserInfo = this.sessionData.getUser();
        if(!tsUserInfo.getUserNo().equals(Constants.ADMIN)) {
            ifAdmin = Boolean.FALSE;
        }
        // 初始化码表
        exportTypeList= commService.findLevelSimpleCodesByTypeId("5550");
        searchExportEDate = DateUtils.getDateOnly(new Date());
        searchExportSDate = DateUtils.addMonths(searchExportEDate,-1);
        searchState = new ArrayList<>();
        searchState.add("0");
        searchState.add("1");
        //文件保留的月份
        String lileLivemonthStr = commService.findParamValue("EXPORT_FILE_LIVEMONTH");
        try {
            this.exportFileLivemonth = Integer.parseInt(lileLivemonthStr);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            this.exportFileLivemonth = null;
        }
    }

    @Override
    public void addInit() {
        
    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }


    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder("");
        sb.append(" FROM TD_TJ_EXPORT T ");
        sb.append(" LEFT JOIN TS_SIMPLE_CODE T1 ON T.BUS_TYPE_ID=T1.RID ");
        sb.append(" LEFT JOIN TS_UNIT T2 ON T.OPER_UNIT_ID=T2.RID ");
        sb.append(" LEFT JOIN TS_USER_INFO T3 ON T.OPER_PSN_ID=T3.RID ");
        sb.append(" WHERE 1=1 ");
        // 查询该人员的所有记录(不是超管)
        if(!ifAdmin){
            sb.append(" AND T.OPER_PSN_ID= ").append(tsUserInfo.getRid());
        }
        if(StringUtils.isNotBlank(this.searchExportTypeId)){
            sb.append(" AND T.BUS_TYPE_ID =").append(this.searchExportTypeId);
        }
        // 导出时间
        if (null!=searchExportSDate) {
            sb.append(" AND T.EXPORT_DATE >= TO_DATE('").append(DateUtils.formatDate(searchExportSDate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null!=searchExportEDate) {
            sb.append(" AND T.EXPORT_DATE <= TO_DATE('").append(DateUtils.formatDate(searchExportEDate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if(!CollectionUtils.isEmpty(searchState)){
            StringBuffer tmp=new StringBuffer();
            for (String s : searchState) {
                tmp.append(",").append(s);
            }
            sb.append(" AND T.STATE in ").append("(").append(tmp.substring(1)).append(")");
        }
        StringBuilder h2 = new StringBuilder();
        StringBuilder h1 = new StringBuilder();
        h1.append(" SELECT T.RID,T1.CODE_NAME,T.EXPORT_FILE_NAME,T.EXPORT_DATE,T.EXPORT_FILE_DATE,T.STATE,T.EXPORT_CONDITION_SHOW,T.ERROR_MSG,T.EXPORT_FILE_PATH,'' AS CONTION_SHOW,T2.UNITNAME,T3.USERNAME,T3.RID AS USERRID,'' ");
        h1.append(sb.toString());
        h1.append(" ORDER BY T.EXPORT_DATE DESC ");
        h2.append(" SELECT COUNT(*) ");
        h2.append(sb.toString());
        return new String[]{h1.toString(),h2.toString()};
    }

    @Override
    public void processData(List<?> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<Object[]> resultList = (List<Object[]>) list;
        String userRidStr = StringUtils.objectToString(tsUserInfo.getRid());
        for(Object[] objArr : resultList){
            Clob clob = null == objArr[6] ? null : (Clob) objArr[6];
            objArr[6] = ClobToString(clob);
            objArr[6]=objArr[6].toString().replaceAll("@\\*@","：").replaceAll("#\\*#","；");
            objArr[13] = StringUtils.objectToString(objArr[12]).equals(userRidStr);
        }
    }

    /**
     * @Description: 导出文件的下载
     *
     * @MethodAuthor gjy,2021年12月17日
     */
    public StreamedContent getDownLoadExport() {
        this.downLoadExport = null;
        try {
            String xnPath = JsfUtil.getAbsolutePath();
            String path="";
            String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            if(filePath.indexOf(xnPath) != -1){
                path = filePath;
            }else{
                path = xnPath + filePath;
            }
            File file=new File(path);
            String[] suffix=filePath.split("\\.");
            String suffixName=suffix[suffix.length-1];
            if(file.exists() && StringUtils.isNotBlank(filePath)){
                if(suffixName.equals("doc")){
                    contentType = "application/msword";
                }else if(suffixName.equals("docx")){
                    contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                }else if(suffixName.equals("xls")){
                    contentType = "application/vnd.ms-excel";
                }else if(suffixName.equals("pdf")){
                    contentType = "application/pdf";
                }
                InputStream stream = new FileInputStream(path);
                this.downLoadExport = new DefaultStreamedContent(stream, contentType, URLEncoder.encode(fileName+"."+suffixName, "UTF-8"));
                // 状态更新为3
                tdTjExportService.updateState(rid, 3);
            }else {
                JsfUtil.addErrorMessage("文件不存在！");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return this.downLoadExport;
    }
    /**
     * @Description: 将clob类型转化为String类型
     *
     * @MethodAuthor gjy,2021年12月17日
     */
    public String ClobToString(Clob clob) {
        String clobToString = "";
        if(null == clob){
            return clobToString;
        }
        try{
            Reader is = clob.getCharacterStream();// 得到流
            BufferedReader br = new BufferedReader(is);
            String s = br.readLine();
            StringBuffer sb = new StringBuffer();
            while (s != null) {// 执行循环将字符串全部取出付值给StringBuffer由StringBuffer转成STRING
                sb.append(s);
                s = br.readLine();
            }
            clobToString = sb.toString();
        }catch (Exception e){
            e.getStackTrace();
        }
        return clobToString;
    }
    /**
     * @Description: 导出条件详情显示
     *
     * @MethodAuthor gjy,2021年12月23日
     */
    public void showAction(){
        conditionList=new ArrayList<>();
        exportCondition=this.tdTjExportService.findConditionByRid(rid);
        String[] split = exportCondition.split("#\\*#");
        StringBuffer sping=new StringBuffer();
        if(split.length>0){
            for (String s : split) {
                if(StringUtils.isNotBlank(s)){
                    // sping.append(s).append("；").append("</br>");
                    conditionList.add(s);
                }
            }
        }
        exportCondition=sping.toString();
    }

    /**
     * 删除操作
     */
    public void deleteAction() {
        try {
            this.tdTjExportService.deleteTaskAndFileByRid(this.rid, this.filePath);
            JsfUtil.addSuccessMessage("删除成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    public String getSearchExportTypeId() {
        return searchExportTypeId;
    }

    public void setSearchExportTypeId(String searchExportTypeId) {
        this.searchExportTypeId = searchExportTypeId;
    }

    public List<TsSimpleCode> getExportTypeList() {
        return exportTypeList;
    }

    public void setExportTypeList(List<TsSimpleCode> exportTypeList) {
        this.exportTypeList = exportTypeList;
    }

    public Date getSearchExportSDate() {
        return searchExportSDate;
    }

    public void setSearchExportSDate(Date searchExportSDate) {
        this.searchExportSDate = searchExportSDate;
    }

    public Date getSearchExportEDate() {
        return searchExportEDate;
    }

    public void setSearchExportEDate(Date searchExportEDate) {
        this.searchExportEDate = searchExportEDate;
    }

    public List<String> getSearchState() {
        return searchState;
    }

    public void setSearchState(List<String> searchState) {
        this.searchState = searchState;
    }

    public Boolean getIfAdmin() {
        return ifAdmin;
    }

    public void setIfAdmin(Boolean ifAdmin) {
        this.ifAdmin = ifAdmin;
    }

    public TsUserInfo getTsUserInfo() {
        return tsUserInfo;
    }

    public void setTsUserInfo(TsUserInfo tsUserInfo) {
        this.tsUserInfo = tsUserInfo;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public void setDownLoadExport(StreamedContent downLoadExport) {
        this.downLoadExport = downLoadExport;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getExportCondition() {
        return exportCondition;
    }

    public void setExportCondition(String exportCondition) {
        this.exportCondition = exportCondition;
    }

    public List<String> getConditionList() {
        return conditionList;
    }

    public void setConditionList(List<String> conditionList) {
        this.conditionList = conditionList;
    }

    public Integer getExportFileLivemonth() {
        return exportFileLivemonth;
    }

    public void setExportFileLivemonth(Integer exportFileLivemonth) {
        this.exportFileLivemonth = exportFileLivemonth;
    }
}
