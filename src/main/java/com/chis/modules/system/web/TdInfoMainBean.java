package com.chis.modules.system.web;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.UUID;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.UploadedFile;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.FileUtils;
import com.chis.common.utils.JPushUtil;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TdInfoAnnex;
import com.chis.modules.system.entity.TdInfoMain;
import com.chis.modules.system.entity.TdInfoSub;
import com.chis.modules.system.entity.TdMsgMain;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.logic.TdInfoMainJavaBean;
import com.chis.modules.system.protocol.PushMsgToAppImpl;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.DownLoadUtil;
import com.chis.modules.system.utils.MsgSendUtil;

/**
 * 我的寻呼
 * 
 * <AUTHOR>
 * @history 2014-07-16
 * 
 * @history 文件存储到虚拟路径修改
 * @LastModify xt
 * @ModifyDate 2014年9月4日
 * 
 *             修改人：lqq 修改时间：2014-09-05 修改内容：如果是从操作栏点击进入，直接进入编辑页面
 */
@ManagedBean(name = "tdInfoMainBean")
@ViewScoped
public class TdInfoMainBean extends FacesEditBean {

	private static final long serialVersionUID = -738445454536625063L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/** ejb session bean */
	private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);

	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	/** 查询标题 */
	private String searchInfoTitle;
	/** 查询开始时间 */
	private Date searchBeginDate;
	/** 查询结束时间 */
	private Date searchEndDate;
	/** 寻呼消息主表 */
	private TdInfoMain tdInfoMain;
	/** 人员list */
	private List<TdInfoMainJavaBean> userJavaBeanList = new LinkedList<TdInfoMainJavaBean>();
	/** 详情页面人员信息list */
	private List<String[]> viewUserList = new LinkedList<String[]>();
	/** rid */
	private Integer rid;

	/** 附件list */
	private List<TdInfoAnnex> annexList = new LinkedList<TdInfoAnnex>();

	/** 寻呼信息子表 */
	private List<TdInfoSub> infoSubList = new LinkedList<TdInfoSub>();

	/** 附件表 */
	private TdInfoAnnex tdInfoAnnex;

	/** 选择的科室ids */
	private String selOfficeId;

	/** 触发全选的单位 */
	private String selUnitId;
	/** 触发全选的单位是否勾选 */
	private String ifUnitSelected;

	/** 选择的人员id */
	private String selUserIds;
	/** 选择人员list */
	private Map<String, List<TdInfoMainJavaBean>> unitUserMap = new LinkedHashMap<String, List<TdInfoMainJavaBean>>();

	/** 是否超管 **/
	private boolean ifAdmin = Boolean.TRUE;
	/** 是否所有单位全选 **/
	private boolean ifAll = false;

	@PostConstruct
	public void init() {
		this.searchBeginDate = null;
		this.searchEndDate = null;
		this.searchInfoTitle = null;
		TsUserInfo tsUserInfo = this.sessionData.getUser();
		if (!tsUserInfo.getUserNo().equals(Constants.ADMIN)) {
			ifAdmin = Boolean.FALSE;
		}
		// 获取页面参数，如果是从操作栏点击进入，直接进入编辑页面
		String type = JsfUtil.getRequest().getParameter("type");
		if ("head".equals(type)) {
			addInitAction();
			return;
		}

		this.searchAction();
	}

	/**
	 * <p>
	 * 方法描述：是否选中所有单位
	 * </p>
	 * 
	 * @MethodAuthor xt,2019年6月17日,changeAll
	 */
	public void changeAll() {
		this.selUserIds = "";
		if (null != unitUserMap) {
			Set<Entry<String, List<TdInfoMainJavaBean>>> entrySet = unitUserMap.entrySet();
			for (Entry<String, List<TdInfoMainJavaBean>> entry : entrySet) {
				List<TdInfoMainJavaBean> value = entry.getValue();
				for (TdInfoMainJavaBean jb : value) {
					jb.setSelect(ifAll);// 科室勾选框同一设为true
					List<String> l = new LinkedList<String>();
					for (TdInfoMainJavaBean j : jb.getUserInfo()) {
						if (ifAll) {
							l.add(j.getUserRid());
							if (this.selUserIds.indexOf("," + j.getUserRid() + ",") == -1) {
								this.selUserIds += "," + j.getUserRid() + ",";
							}
						}
						j.setSelect(ifAll);
					}
					jb.setSelUserRids(l);
				}
			}
		}
		
	}

	@Override
	public void addInit() {
		this.ifAll = false;
		this.tdInfoMain = new TdInfoMain();
		this.tdInfoAnnex = new TdInfoAnnex();
		this.annexList = new LinkedList<TdInfoAnnex>();
		this.infoSubList = new LinkedList<TdInfoSub>();
		this.userJavaBeanList = new LinkedList<TdInfoMainJavaBean>();
		this.selOfficeId = "";
		this.selUserIds = "";
		this.unitUserMap = new LinkedHashMap<String, List<TdInfoMainJavaBean>>();
		// 初始化人员科室list
		List<Object[]> list = this.systemModuleService.initOfficeUserInfo(ifAdmin, sessionData.getUser().getTsUnit().getTsZone().getZoneCode());
		Map<String, List<TdInfoMainJavaBean>> map = new LinkedHashMap<String, List<TdInfoMainJavaBean>>();
		String offid = "";
		for (Object[] obj : list) {
			String userRid = null != obj[0] ? String.valueOf(obj[0]) : "";
			String userName = null != obj[1] ? String.valueOf(obj[1]) : "";
			String officeRid = null != obj[2] ? String.valueOf(obj[2]) : "";
			String officeName = null != obj[3] ? String.valueOf(obj[3]) : "";
			String unitRid = null != obj[4] ? String.valueOf(obj[4]) : "";
			String unitName = null != obj[5] ? String.valueOf(obj[5]) : "";
			TdInfoMainJavaBean jb = new TdInfoMainJavaBean();
			jb.setOfficeRid(officeRid);
			jb.setOfficeName(officeName);
			jb.setUserName(userName);
			jb.setUserRid(userRid);
			jb.setUnitRid(unitRid);
			jb.setUnitName(unitName);
			if (!offid.equals(officeRid)) {
				map.put(officeRid, new LinkedList<TdInfoMainJavaBean>());
				offid = officeRid;
			}
			if (null != map.get(officeRid)) {
				map.get(officeRid).add(jb);
			}
		}
		String unitName = "";
		Iterator<String> iterator = map.keySet().iterator();
		List<TdInfoMainJavaBean> list1 = new LinkedList<TdInfoMainJavaBean>();
		while (iterator.hasNext()) {
			String s = iterator.next();
			List<TdInfoMainJavaBean> l = map.get(s);
			if (null != l) {
				TdInfoMainJavaBean jb = l.get(0);
				TdInfoMainJavaBean jb1 = new TdInfoMainJavaBean();
				jb1.setOfficeRid(jb.getOfficeRid());
				jb1.setOfficeName(jb.getOfficeName());
				jb1.setUserInfo(l);
				this.userJavaBeanList.add(jb1);
				if (!unitName.equals(jb.getUnitName())) {
					list1 = new LinkedList<TdInfoMainJavaBean>();
					unitUserMap.put(jb.getUnitName(), list1);
					unitName = jb.getUnitName();
				}
				if (null != unitUserMap.get(unitName)) {
					unitUserMap.get(unitName).add(jb1);
				}
			}
		}
	}

	@Override
	public void viewInit() {
		try {
			this.tdInfoMain = this.systemModuleService.findTdInfoMain(rid);
			this.viewUserList = new LinkedList<String[]>();
			this.annexList = new LinkedList<TdInfoAnnex>();
			if (null != tdInfoMain) {
				for (TdInfoSub sub : tdInfoMain.getTdInfoSubs()) {
					String officeName = sub.getTsUserInfo().getTbSysEmp().getTsOffice().getOfficename();
					String userName = sub.getTsUserInfo().getUsername();
					String pubTime = "";
					if (null != sub.getPublishTime()) {
						pubTime = DateUtils.formatDate(sub.getPublishTime(), "yyyy-MM-dd HH:mm");
					}
					this.viewUserList.add(new String[] { officeName, userName, pubTime });
				}
				if (null != tdInfoMain.getTdInfoAnnexes()) {
					this.annexList = tdInfoMain.getTdInfoAnnexes();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	@Override
	public void modInit() {
	}

	/**
	 * 保存
	 */
	public void saveAction() {
		if (StringUtils.isBlank(this.tdInfoMain.getInfoTitle())) {
			JsfUtil.addErrorMessage("标题不能为空！");
			return;
		}
		if (StringUtils.isBlank(this.tdInfoMain.getInfoContent())) {
			JsfUtil.addErrorMessage("内容不能为空！");
			return;
		}
		if (StringUtils.isNotBlank(this.selUserIds)) {
			if (StringUtils.isNotBlank(this.selUserIds)) {
				List<TdInfoSub> tdInfoSubs = new LinkedList<TdInfoSub>();
				String[] ids = this.selUserIds.split(",,");
				String users = "";
				// ++过滤重复人员id
				Set<String> userIds = new HashSet<String>();
				for (String id : ids) {
					String userId = id.replaceAll(",", "");
					userIds.add(userId);
					users += "," + userId;
				}
				for (String userIdNoSame : userIds) {
					TdInfoSub sub = new TdInfoSub();
					sub.setAcceptState(0);
					sub.setTdInfoMain(this.tdInfoMain);
					sub.setTsUserInfo(new TsUserInfo(new Integer(userIdNoSame)));
					tdInfoSubs.add(sub);
				}
				TsUserInfo tsUserInfo = this.sessionData.getUser();
				this.tdInfoMain.setTsUserInfo(tsUserInfo);
				this.tdInfoMain.setPublishTime(new Date());
				this.tdInfoMain.setTdInfoAnnexes(this.annexList);
				this.tdInfoMain.setTdInfoSubs(tdInfoSubs);
				TdMsgMain msgMain = this.systemModuleService.updateTdInfoMain(this.tdInfoMain);
				if (null != msgMain) {
					MsgSendUtil.sendNewMsg(msgMain);
					// 极光推送消息
//					this.sendPushMsg(users.substring(1), this.tdInfoMain.getInfoTitle(), msgMain.getRid());
				}
				this.backAction();
				this.searchAction();
			} else {
				JsfUtil.addErrorMessage("请选择人员信息！");
			}

		} else {
			JsfUtil.addErrorMessage("请选择人员信息！");
		}
		this.searchAction();
	}

	/** 极光推送 */
	public void sendPushMsg(String userIds, String content, Integer rid) {
		List<TsUserInfo> userList = this.commService.findUserByUserIds(userIds);
		Map<String, String> paramMap = new HashMap<>();
		paramMap.put("msgRid", rid.toString());
		paramMap.put("type", "1");
		if (userList != null && userList.size() > 0) {
			for (TsUserInfo user : userList) {
				new PushMsgToAppImpl(content, paramMap, user.getUserNo()).sendJPush();
			}
		}
	}

	@Override
	public String[] buildHqls() {
		TsUserInfo tsUserInfo = this.sessionData.getUser();
		StringBuilder countSB = new StringBuilder("");
		StringBuilder sb = new StringBuilder("");
		countSB.append("select count(distinct t.rid) from TdInfoMain t where t.tsUserInfo.rid = ").append(tsUserInfo.getRid());
		sb.append("select t from TdInfoMain t where t.tsUserInfo.rid = ").append(tsUserInfo.getRid());
		if (null != this.searchInfoTitle) {
			countSB.append(" and t.infoTitle like :infoTitle");
			sb.append(" and t.infoTitle like :infoTitle ");
			paramMap.put("infoTitle", "%" + this.searchInfoTitle + "%");
		}
		if (null != this.searchBeginDate) {
			countSB.append(" and t.publishTime >= :startDate");
			sb.append(" and t.publishTime >= :startDate ");
			paramMap.put("startDate", this.searchBeginDate);
		}
		if (null != this.searchEndDate) {
			countSB.append(" and t.publishTime < :endDate ");
			sb.append(" and t.publishTime < :endDate ");
			paramMap.put("endDate", DateUtils.addDays(this.searchEndDate, 1));
		}
		sb.append(" order by t.publishTime desc ");
		return new String[] { sb.toString(), countSB.toString() };
	}

	/**
	 * 文件上传
	 */
	public void handleFileUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			TdInfoAnnex annex = new TdInfoAnnex();
			try {
				String fileName = file.getFileName();// 文件名称
				String contentType = file.getContentType().toLowerCase();
				String errorMsg = FileUtils.veryFile(file.getInputstream(),contentType, fileName, "3");
				if (StringUtils.isNotBlank(errorMsg)) {
					JsfUtil.addErrorMessage(errorMsg);
					return;
				}
				String uuid = UUID.randomUUID().toString().replaceAll("-", "");
				String path = JsfUtil.getAbsolutePath();
				// 相对与虚拟目录的路径
				String relativePath = new StringBuilder("/files").append("/").append(uuid).append(fileName.substring(fileName.lastIndexOf(".")))
						.toString();
				String filePath = path + relativePath;
				// 将文件信息保存至list中
				annex.setAnnexName(fileName);// 文件名称
				annex.setAnnexAddr(relativePath);// 文件路径
				annex.setTdInfoMain(this.tdInfoMain);// 主表信息
				annex.setXh(this.annexList.size());// 序号
				this.annexList.add(annex);
				// 将文件上传方法抽用公用方法
				FileUtils.copyFile(filePath, file.getInputstream());
			} catch (Exception e) {
				FacesMessage mess = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", mess);
				e.printStackTrace();
			}
		}

	}

	/**
	 * 下载文件
	 */
	public void downLoadDiskFile() {
		String path = JsfUtil.getAbsolutePath() + this.tdInfoAnnex.getAnnexAddr();
		FileInputStream fis = null;
		try {
			File file = new File(path);
			fis = new FileInputStream(file);
			String fileString = DownLoadUtil.uploadFile2Database(fis);
			DownLoadUtil.downFile(this.tdInfoAnnex.getAnnexName(), fileString);
		} catch (Exception e1) {
			e1.printStackTrace();
		} finally {// 关闭输入输出流
			try {
				fis.close();
			} catch (IOException e2) {
				e2.printStackTrace();
			}
		}
	}

	/**
	 * 删除上传的文件
	 */
	public void deleteDiskFile() {
		this.annexList.remove(this.tdInfoAnnex);
		new File(JsfUtil.getAbsolutePath() + this.tdInfoAnnex.getAnnexAddr()).delete();
	}

	/**
	 * 选中科室
	 */
	public void selectOffice() {
		if (this.selOfficeId.indexOf("_") != -1) {
			String[] s = this.selOfficeId.split("_");
			if (s[0].equals("true")) {
				this.selUserIds += "," + s[1] + ",";
			} else {
				this.selUserIds = this.selUserIds.replaceAll("," + s[1] + ",", "");
			}
		} else {
			for (TdInfoMainJavaBean j : this.userJavaBeanList) {
				if (j.getOfficeRid().equals(this.selOfficeId)) {
					if (j.getSelect()) {
						List<String> l = new LinkedList<String>();
						for (TdInfoMainJavaBean sj : j.getUserInfo()) {
							l.add(sj.getUserRid());
							if (this.selUserIds.indexOf("," + sj.getUserRid() + ",") == -1) {
								this.selUserIds += "," + sj.getUserRid() + ",";
							}
						}
						j.setSelUserRids(l);
					} else {
						for (String s : j.getSelUserRids()) {
							this.selUserIds = this.selUserIds.replaceAll("," + s + ",", "");// 删除
						}
						j.setSelUserRids(new LinkedList<String>());
					}
					break;
				}
			}
		}
	}

	/**
	 * 选中单位
	 * 
	 */
	public void selectUnit() {
		long currentTimeMillis = System.currentTimeMillis();
		
		String unitName_ifUnitSelected = this.selUnitId;// 点击勾选框时 传入单位和勾选状态
		String[] s = unitName_ifUnitSelected.split("_");
		ifUnitSelected = s[1];
		String unitName = s[0];
		if ("true".equals(ifUnitSelected)) {
			List<TdInfoMainJavaBean> list = unitUserMap.get(unitName);
			for (TdInfoMainJavaBean jb : list) {
				jb.setSelect(true);// 科室勾选框同一设为true
				List<String> l = new LinkedList<String>();
				for (TdInfoMainJavaBean j : jb.getUserInfo()) {
					l.add(j.getUserRid());
					if (this.selUserIds.indexOf("," + j.getUserRid() + ",") == -1) {
						this.selUserIds += "," + j.getUserRid() + ",";
					}
					j.setSelect(true);
				}
				jb.setSelUserRids(l);
			}
		} else {
			List<TdInfoMainJavaBean> list = unitUserMap.get(unitName);
			for (TdInfoMainJavaBean jb : list) {
				jb.setSelect(false);// 科室勾选框同一设为false
				for (String s1 : jb.getSelUserRids()) {
					this.selUserIds = this.selUserIds.replaceAll("," + s1 + ",", "");// 删除
				}
				List<String> l = new LinkedList<String>();
				jb.setSelUserRids(l);
			}
		}
		long currentTimeMillis2 = System.currentTimeMillis();
		System.out.println(currentTimeMillis2);
		System.out.println(currentTimeMillis);
		System.out.println(currentTimeMillis2-currentTimeMillis);
	}

	/*** 编辑页面返回主页面 ***/
	public void backSearchPageAction() {
		this.backAction();
		this.searchAction();
	}

	public Date getSearchBeginDate() {
		return searchBeginDate;
	}

	public void setSearchBeginDate(Date searchBeginDate) {
		this.searchBeginDate = searchBeginDate;
	}

	public Date getSearchEndDate() {
		return searchEndDate;
	}

	public void setSearchEndDate(Date searchEndDate) {
		this.searchEndDate = searchEndDate;
	}

	public TdInfoMain getTdInfoMain() {
		return tdInfoMain;
	}

	public void setTdInfoMain(TdInfoMain tdInfoMain) {
		this.tdInfoMain = tdInfoMain;
	}

	public List<TdInfoMainJavaBean> getUserJavaBeanList() {
		return userJavaBeanList;
	}

	public void setUserJavaBeanList(List<TdInfoMainJavaBean> userJavaBeanList) {
		this.userJavaBeanList = userJavaBeanList;
	}

	public List<String[]> getViewUserList() {
		return viewUserList;
	}

	public void setViewUserList(List<String[]> viewUserList) {
		this.viewUserList = viewUserList;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public List<TdInfoAnnex> getAnnexList() {
		return annexList;
	}

	public void setAnnexList(List<TdInfoAnnex> annexList) {
		this.annexList = annexList;
	}

	public TdInfoAnnex getTdInfoAnnex() {
		return tdInfoAnnex;
	}

	public void setTdInfoAnnex(TdInfoAnnex tdInfoAnnex) {
		this.tdInfoAnnex = tdInfoAnnex;
	}

	public List<TdInfoSub> getInfoSubList() {
		return infoSubList;
	}

	public void setInfoSubList(List<TdInfoSub> infoSubList) {
		this.infoSubList = infoSubList;
	}

	public String getSelOfficeId() {
		return selOfficeId;
	}

	public void setSelOfficeId(String selOfficeId) {
		this.selOfficeId = selOfficeId;
	}

	public String getSelUserIds() {
		return selUserIds;
	}

	public void setSelUserIds(String selUserIds) {
		this.selUserIds = selUserIds;
	}

	public Map<String, List<TdInfoMainJavaBean>> getUnitUserMap() {
		return unitUserMap;
	}

	public void setUnitUserMap(Map<String, List<TdInfoMainJavaBean>> unitUserMap) {
		this.unitUserMap = unitUserMap;
	}

	public String getSelUnitId() {
		return selUnitId;
	}

	public void setSelUnitId(String selUnitId) {
		this.selUnitId = selUnitId;
	}

	public String getSearchInfoTitle() {
		return searchInfoTitle;
	}

	public void setSearchInfoTitle(String searchInfoTitle) {
		this.searchInfoTitle = searchInfoTitle;
	}

	public boolean isIfAll() {
		return ifAll;
	}

	public void setIfAll(boolean ifAll) {
		this.ifAll = ifAll;
	}

}
