package com.chis.modules.system.web;

import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.DeskImpl;

import javax.naming.NamingException;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.LinkedList;
import java.util.List;

@WebServlet(name="newShortcutDesktopAjax",value="/newShortcutDesktopAjax")
public class ShortcutDesktopAjax extends HttpServlet {
	private static final long serialVersionUID = 4716881208884545892L;
	
	private DeskImpl frameSB = (DeskImpl)SpringContextHolder.getBean(DeskImpl.class);

	public void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
	}

	public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		response.setCharacterEncoding("utf-8");
		response.setContentType("text/html");
		PrintWriter out = response.getWriter();
		String flag = request.getParameter("flag");
		SessionData sdata=(SessionData) request.getSession().getAttribute(SessionData.SESSION_DATA);
		try {
            if (null != sdata) {
				TsUserInfo user = sdata.getUser();
				String userno = user.getUserNo();
				if (null != flag) {
					if (flag.equals("SystemName")) {
						// 获取用户信息
						StringBuffer str = new StringBuffer();
						str.append(sdata.getUser().getTsUnit().getUnitname());
						str.append(",_,");
						str.append(sdata.getUser().getUsername());
						out.println(str.toString());
						return;
					}
					if (flag.equals("InitSD")) {
						// 快捷桌面（主菜单加载）
						out.println(menuInnit2(userno).toString());
						return;
					}
					if (flag.equals("InitSD_upd")) {
						// 修改桌号
						String deskNo = request.getParameter("deskNo");
						frameSB.UpdateMenuConnSingleTx(deskNo, String.valueOf(user.getRid()));

					}
					if (flag.equals("ShowFirstMenu")) {
						// 一级菜单加载
						StringBuffer str = retrieveMenu(userno, 1);
						out.println(str.toString());
						return;
					}
					if (flag.equals("ShowSecondMenu")) {
						// 二级菜单加载
						StringBuffer str = retrieveMenu(userno, 2);
						//out.println 输出的结果中会有换行符 因为IF_POP在最后 会导致最后一个IF_POP后会有换行符（windows中\r\n）
						//IF_POP如果是1 那么增加了换行符变成1\r\n 导致匹配失败
						out.print(str.toString());
						return;
					}
					if (flag.equals("ShowThirdMenu")) {
						// 三级菜单加载
						StringBuffer str = retrieveMenu(userno, 3);
						out.println(str.toString());
						return;
					}
					if (flag.equals("InitMenuList")) {
						// 加号弹出框
						out.println(menuInnit(userno).toString());
						request.getSession().setAttribute("SESSION_DATA", new SessionData(user));
						return;
					}
					if (flag.equals("AddMenuCallBack")) {
						// 会掉js调用
						String addTagIdxs = request.getParameter("add");
						String delTagIdxs = request.getParameter("del");
						String addTagIdxs_souc = request.getParameter("addSou");
						String delTagIdxs_souc = request.getParameter("delSou");
						updStateAction(addTagIdxs, delTagIdxs, addTagIdxs_souc, delTagIdxs_souc, user.getRid());
						out.println("1");
						return;
					}
					if (flag.equals("mDeskMoveFun")) {
						// 跨桌面移动
						String menuId = request.getParameter("menuId");
						String toDesktopNo = request.getParameter("toDesktopNo");
						mDeskMoveFun(menuId, toDesktopNo, String.valueOf(user.getRid()));
						out.println("1");
						return;
					}
					if (flag.equals("mMoveFun")) {
						// 菜单拖动排序
						String ordRids = request.getParameter("ordRids");
						String deskNo = request.getParameter("deskNo");
						mMoveFun(ordRids, deskNo);
						out.println("1");
						return;
					}
                    if(flag.equals("MenuIDSET")){
                        String menuId = request.getParameter("menuId");
                        if(StringUtils.isNotBlank(menuId)) {
                        	sdata.setMenuId(menuId);
                        }
                        return;
                    }
				}
			} else {
				if (flag.equals("AddMenuCallBack")) {
					// 会掉js调用
					String addTagIdxs = request.getParameter("add");
					String delTagIdxs = request.getParameter("del");
					String addTagIdxs_souc = request.getParameter("addSou");
					String delTagIdxs_souc = request.getParameter("delSou");
					String _uRid = request.getParameter("_uRid");
					if (null != _uRid && !"".equals(_uRid)) {
						updStateAction(addTagIdxs, delTagIdxs, addTagIdxs_souc, delTagIdxs_souc, Integer.valueOf(_uRid));
						out.println("1");
					}
					return;
				}else {
                    //session 失效
                    out.println("SESSIONOUT");
                }
			}
		} catch (NamingException e) {
			e.printStackTrace();
			// new BaseAppException("远程访问接口失败！详细信息：" + e.getMessage(), e,
			// applicationBean, this.getClass().getName() , "远程访问接口失败");
		} catch (Exception e) {
			e.printStackTrace();
			// new BaseAppException("获取菜单失败！详细信息：" + e.getMessage(), e,
			// applicationBean, this.getClass().getName() , "获取菜单失败");
		}

	}

	/**
	 * 跨桌面拖动排序
	 * 
	 * @return
	 */
	private void mDeskMoveFun(String moveRid, String tagertNo, String userRid) throws Exception {
		if (null != moveRid && !"".equals(moveRid.trim()) && null != tagertNo && !"".equals(tagertNo.trim())) {
			frameSB.UpdateMenuConnTx(tagertNo, userRid, moveRid);

		}
	}

	/**
	 * 菜单拖动排序 ordRids;顺序后的菜单rid deskNo;所在桌面号
	 */
	private void mMoveFun(String ordRids, String deskNo) throws Exception {
		if (null != ordRids && !"".equals(ordRids.trim()) && null != deskNo && !"".equals(deskNo.trim())) {
			String ordRidstr = ordRids.substring(1, ordRids.length()).replaceAll("_RD", "");
			String[] rids = ordRidstr.split(",");
			if (rids.length > 0) {
				frameSB.UpdateMenuConnStrTx(rids, deskNo);
			}
		}
	}

	/**
	 * 更改桌面是否显示操作
	 * 
	 * @param fsb
	 * @param addTagIdxs
	 * @param delTagIdxs
	 * @param addTagIdxs_souc
	 * @param delTagIdxs_souc
	 * @param userRid
	 * @throws Exception
	 */
	private void updStateAction(String addTagIdxs, String delTagIdxs, String addTagIdxs_souc, String delTagIdxs_souc, Integer userRid)
			throws Exception {
		addTagIdxs = null != addTagIdxs ? addTagIdxs.replaceAll(",,", ",") : "";
		delTagIdxs = null != delTagIdxs ? delTagIdxs.replaceAll(",,", ",") : "";
		addTagIdxs_souc = null != addTagIdxs_souc ? addTagIdxs_souc.replaceAll(",,", ",") : "";
		delTagIdxs_souc = null != delTagIdxs_souc ? delTagIdxs_souc.replaceAll(",,", ",") : "";
		// 判断是否有必要去操作数据库
		if (addTagIdxs.equals(addTagIdxs_souc) || delTagIdxs_souc.equals(delTagIdxs)) {
			return;
		} else {
			String[] a1 = addTagIdxs.split(",");
			String[] a2 = addTagIdxs_souc.split(",");
			if (a1.length == a2.length) {
				boolean hasitBig = true;
				for (int i = 0; i < a1.length; i++) {
					boolean hasit = false;
					for (int j = 0; j < a2.length; j++) {
						if (a1[i].equals(a2[j])) {
							hasit = true;
							break;
						}
					}
					if (!hasit) {
						hasitBig = false;
						break;
					}
				}
				if (hasitBig) {
					return;
				}
			}
		}
		if (!"".equals(addTagIdxs)) {
			addTagIdxs = addTagIdxs.substring(1, addTagIdxs.length() - 1);
		}
		if (!"".equals(delTagIdxs)) {
			delTagIdxs = delTagIdxs.substring(1, delTagIdxs.length() - 1);
		}
		frameSB.upd(String.valueOf(userRid), addTagIdxs, delTagIdxs);
	}

	/**
	 * 点击加号弹出框
	 */
	private StringBuffer menuInnit(String userno) throws Exception {
		LinkedList<LinkedList<String>> selmlist = frameSB.getDeskUMenu(userno);
		StringBuffer rtnStr = new StringBuffer();
		if (null != selmlist && selmlist.size() > 0) {
			LinkedList<String> mlist = selmlist.get(0);
			for (String str : mlist) {
				rtnStr.append(rtnStr.length() > 0 ? "@#@" : "");
				rtnStr.append(str);
			}
			rtnStr.append("TAGIDXS:");
			if (null != selmlist.get(1) && selmlist.get(1).size() > 0) {
				rtnStr.append(selmlist.get(1).get(0)).append("#");
				rtnStr.append(selmlist.get(1).get(1));
			}
		}

		return rtnStr;
	}
	/**
	 * 生成树形菜单
	 * 
	 * @param type
	 *            类别 1：一级 2：二级 3：三级
	 * <AUTHOR> 2014-01-07
	 */
	private StringBuffer retrieveMenu(String userno, int type) throws Exception {
		StringBuffer retnStr = new StringBuffer();
		StringBuffer str = null;
		List<Object[]> list=frameSB.getUserMenu(userno, type);
		if(null!=list&&list.size()>0){
			for(Object[] obj:list){
				str = new StringBuffer();
				if(type==1){
					str.append(String.valueOf(obj[1])).append(",_,").append(String.valueOf(obj[2]))
					.append(",_,").append(String.valueOf(obj[7])).append(",_,").append(String.valueOf(obj[13])).append(",_,").append(String.valueOf(obj[0]));//13为大图标
					str.append(",_,").append(String.valueOf(obj[14]));//14是否打开一个新的网页
					retnStr.append(retnStr.length() > 0 ? "@#@" : "");
					retnStr.append(str.toString());
				}
				if(type==2){
					String code=String.valueOf(obj[1]);
					str.append(code.substring(0, code.indexOf("."))).append(",_,").append(String.valueOf(obj[2]))
					.append(",_,").append(String.valueOf(obj[6])).append(",_,").append(String.valueOf(obj[7])).append(",_,").append(code).append(",_,").append(String.valueOf(obj[13])).append(",_,").append(String.valueOf(obj[0]));//13为大图标
					str.append(",_,").append(String.valueOf(obj[14]));//14是否打开一个新的网页
					retnStr.append(retnStr.length() > 0 ? "@#@" : "");
					retnStr.append(str.toString());
				}
				if(type==3){
					String code=String.valueOf(obj[1]);
					str.append(code.substring(0, code.lastIndexOf("."))).append(",_,").append(String.valueOf(obj[2]))
					.append(",_,").append(String.valueOf(obj[6])).append(",_,").append(String.valueOf(obj[7])).append(",_,").append(String.valueOf(obj[13])).append(",_,").append(String.valueOf(obj[0]));//13为大图标
					str.append(",_,").append(String.valueOf(obj[14]));//14是否打开一个新的网页
					retnStr.append(retnStr.length() > 0 ? "@#@" : "");
					retnStr.append(str.toString());
				}
			}

		}
		return retnStr;
	}

	/**
	 * 获取页面桌面显示主菜单
	 * 
	 * <AUTHOR>
	 * @return
	 */
	private StringBuffer menuInnit2(String userno) {
		StringBuffer dmArry = new StringBuffer();
		try {
			List<String> dmlist = frameSB.getDeskMMenu(userno);
			if (null != dmlist && dmlist.size() > 0) {
				// 是否需要进行批量初始化更新
				boolean hasupd = false;
				String rstr = dmlist.get(0);
				if (null != rstr && !"".equals(rstr)) {
					String afters = rstr.split(",#,")[0];
					String[] after = afters.split(",:,");
					if (after.length <= 1 || null == after[1] || "".equals(after[1]) || (after.length == 3 && (null == after[2] || "".equals(after[2]))))
						hasupd = true;
				}
				if (hasupd) {
					frameSB.UpdateMenuConnListTx(dmlist);
					dmlist = frameSB.getDeskMMenu(userno);
				}
				// 组成数组
				dmArry = new StringBuffer();
				dmArry.append(dmlist.get(0));
				for (int i = 1; i < dmlist.size(); i++) {
					dmArry.append("@#@").append(dmlist.get(i));
				}

			}
		} catch (Exception ex) {
			ex.printStackTrace();
			log("获取菜单失败！" + ex.getMessage());

		}
		return dmArry;
	}
}