package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.TransferEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.DualListModel;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TdMsgMain;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.CodeType;
import com.chis.modules.system.enumn.MessageType;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.MsgSendUtil;

/**
 * 弹出框的方式消息发送
 * <AUTHOR>
 */
@ManagedBean(name = "msgDialogSendBean")
@ViewScoped
public class MsgDialogSendBean extends FacesBean {

	private static final long serialVersionUID = -5927665119395504030L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/**ejb session bean*/
	private SystemModuleServiceImpl systemService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	/** 科室树 */
	private TreeNode userOfficeTreeNode;
	/**职务树**/
	private TreeNode dutyTree;
	/**是否领导*/
	private boolean ifManager;
	/**是否发送手机短消息*/
	private boolean mobileMsg;
	/**消息内容*/
	private String msgCont;
	/** 用于机构授权的选择 */
	private DualListModel dualListModel = new DualListModel();
	/** 可以选择的用户 */
	private List<TsUserInfo> allUserList;
	/** 选择的用户IDS */
	private String selectedManIds;
	/** 选择的用户名 */
	private String selectedManNames;

	public MsgDialogSendBean() {
		this.init();
	}
	
	/**
	 * 数据初始化
	 */
	private void init() {
		this.initOfficeTree();
		this.initDuty();
		
		List<TsUserInfo> sourceList = this.systemService.findUsers(this.sessionData.getUser().getTsUnit().getRid(), null, null);
		this.dualListModel.setSource(sourceList);
	}
	
	/**
	 * 初始化科室树
	 */
	public void initOfficeTree() {
		// 初始化科室树
		if (null == this.userOfficeTreeNode) {
			this.userOfficeTreeNode = new DefaultTreeNode("root", null);
			
			//加一个所有
			TsOffice topOffice = new TsOffice(0);
			topOffice.setParentOffice(null);
			topOffice.setOfficename("所有科室");
			TreeNode topNode = new DefaultTreeNode(topOffice, this.userOfficeTreeNode);
			
			List<TsOffice> list = this.systemService.findOfficeList(this.sessionData.getUser().getTsUnit().getRid());
			if (null != list && list.size() > 0) {
				Map<Integer, TreeNode> map = new HashMap<Integer, TreeNode>();
				for (TsOffice t : list) {
					if (null == t.getParentOffice()) {
						TreeNode node = new DefaultTreeNode(t, topNode);
						map.put(t.getRid(), node);
					} else {
						TreeNode node = new DefaultTreeNode(t, map.get(t.getParentOffice().getRid()));
						map.put(t.getRid(), node);
					}
				}
				map.clear();
			}
		}
	}
	
	/**
	 * 初始化职务
	 */
	public void initDuty() {
		if(null == this.dutyTree) {
			this.dutyTree = new DefaultTreeNode("root", null);
			
			//加一个所有
			TsSimpleCode topCode = new TsSimpleCode(0);
			topCode.setCodeName("所有职务");
			TreeNode topNode = new DefaultTreeNode(topCode, this.dutyTree);
			
			List<TsSimpleCode> dutyList = this.commService.findSimpleCodesByTypeId(CodeType.ZHW.getTypeNo());
			if(null != dutyList && dutyList.size() > 0) {
				for(TsSimpleCode t : dutyList) {
					TreeNode node = new DefaultTreeNode(t, topNode);
				}
			}
		}
	}

	/**
	 * 科室选中事件
	 * @param event 节点选中事件
	 */
	public void onOfficeNodeSelect(NodeSelectEvent event) {
		List<TsUserInfo>  targetList = this.dualListModel.getTarget();
		String filters = null;
		if(null != targetList && targetList.size() > 0) {
			StringBuilder sb = new StringBuilder();
			for(TsUserInfo t: targetList) {
				sb.append(",").append(t.getRid());
			}
			filters = sb.toString().substring(1);
		}
		
		TreeNode selectedNode = event.getTreeNode();
		TsOffice office = (TsOffice)selectedNode.getData();
		
		List<TsUserInfo> sourceList = new ArrayList<TsUserInfo>(0);
		if(office.getRid().intValue() == 0) {
			//选中所有科室
			sourceList = this.systemService.findUsers(this.sessionData.getUser().getTsUnit().getRid(), null, filters);
		}else {
			//选中某个科室，查询该科室及下级科室
			Map<String, String> conMap = new HashMap<String, String>();
			conMap.put("officeId", office.getRid().toString());
			sourceList = this.systemService.findUsers(this.sessionData.getUser().getTsUnit().getRid(), conMap, filters);
		}
		
		this.dualListModel.setSource(sourceList);
	}
	
	/**
	 * 选人事件，不允许删除该方法
	 * @param event
	 */
	public void handleTransfer(TransferEvent event) {
	}
	
	/**
	 * 职务选中事件
	 */
	public void onDutyNodeSelect(NodeSelectEvent event) {
		List<TsUserInfo>  targetList = this.dualListModel.getTarget();
		String filters = null;
		if(null != targetList && targetList.size() > 0) {
			StringBuilder sb = new StringBuilder();
			for(TsUserInfo t: targetList) {
				sb.append(",").append(t.getRid());
			}
			filters = sb.toString().substring(1);
		}
		
		TreeNode selectedNode = event.getTreeNode();
		TsSimpleCode duty = (TsSimpleCode)selectedNode.getData();
		
		List<TsUserInfo> sourceList = new ArrayList<TsUserInfo>(0);
		if(duty.getRid().intValue() == 0) {
			//选中所有科室
			sourceList = this.systemService.findUsers(this.sessionData.getUser().getTsUnit().getRid(), null, filters);
		}else {
			//选中某个科室，查询该科室及下级科室
			Map<String, String> conMap = new HashMap<String, String>();
			conMap.put("dutyId", duty.getRid().toString());
			sourceList = this.systemService.findUsers(this.sessionData.getUser().getTsUnit().getRid(), conMap, filters);
		}
		
		this.dualListModel.setSource(sourceList);
	}
	
	/**
	 * 是否领导选择
	 */
	public void onBossChgAction() {
		List<TsUserInfo>  targetList = this.dualListModel.getTarget();
		String filters = null;
		if(null != targetList && targetList.size() > 0) {
			StringBuilder sb = new StringBuilder();
			for(TsUserInfo t: targetList) {
				sb.append(",").append(t.getRid());
			}
			filters = sb.toString().substring(1);
		}
		
		List<TsUserInfo> sourceList = new ArrayList<TsUserInfo>(0);
		if(!this.ifManager) {
			//选中所有科室
			sourceList = this.systemService.findUsers(this.sessionData.getUser().getTsUnit().getRid(), null, filters);
		}else {
			//选中某个科室，查询该科室及下级科室
			Map<String, String> conMap = new HashMap<String, String>();
			conMap.put("ifLeader", "1");
			sourceList = this.systemService.findUsers(this.sessionData.getUser().getTsUnit().getRid(), conMap, filters);
		}
		
		this.dualListModel.setSource(sourceList);
	}

	/**
	 * 确定
	 */
	public void sendAction() {
		List<TsUserInfo> targetList = this.dualListModel.getTarget();
		if (null != targetList && targetList.size() > 0) {
			TdMsgMain msgMain = new TdMsgMain();
			msgMain.setInfoTitle(this.msgCont);
			msgMain.setMessageType(MessageType.INFO);
			msgMain.setNetAdr("/webapp/flow/tdFlowTaskList.faces");
			msgMain.setNetName("待办任务");
			msgMain.setTsUserInfo(new TsUserInfo(this.sessionData.getUser().getRid()));
			
			String msg = this.systemService.saveMsg(msgMain, targetList, this.mobileMsg);
			if(StringUtils.isBlank(msg)) {
				StringBuilder idsBuilder = new StringBuilder();
				for(TsUserInfo t: targetList) {
					idsBuilder.append(",").append(t.getRid());
				}
				MsgSendUtil.sendNewMsg(idsBuilder.toString().substring(1));
				
				//保存成功后要发送消息给各个人
				RequestContext requestContext = RequestContext.getCurrentInstance();
				requestContext.execute("window.close();");
			}else {
				JsfUtil.addErrorMessage(msg);
			}
		}else {
            JsfUtil.addErrorMessage("请选择要发送的用户！");
        }
	}


	public TreeNode getUserOfficeTreeNode() {
		return userOfficeTreeNode;
	}

	public void setUserOfficeTreeNode(TreeNode userOfficeTreeNode) {
		this.userOfficeTreeNode = userOfficeTreeNode;
	}

	public DualListModel getDualListModel() {
		return dualListModel;
	}

	public void setDualListModel(DualListModel dualListModel) {
		this.dualListModel = dualListModel;
	}

	public String getSelectedManIds() {
		return selectedManIds;
	}

	public void setSelectedManIds(String selectedManIds) {
		this.selectedManIds = selectedManIds;
	}

	public String getSelectedManNames() {
		return selectedManNames;
	}

	public void setSelectedManNames(String selectedManNames) {
		this.selectedManNames = selectedManNames;
	}

	public TreeNode getDutyTree() {
		return dutyTree;
	}

	public void setDutyTree(TreeNode dutyTree) {
		this.dutyTree = dutyTree;
	}

	public boolean isIfManager() {
		return ifManager;
	}

	public void setIfManager(boolean ifManager) {
		this.ifManager = ifManager;
	}

	public boolean isMobileMsg() {
		return mobileMsg;
	}

	public void setMobileMsg(boolean mobileMsg) {
		this.mobileMsg = mobileMsg;
	}

	public String getMsgCont() {
		return msgCont;
	}

	public void setMsgCont(String msgCont) {
		this.msgCont = msgCont;
	}

}
