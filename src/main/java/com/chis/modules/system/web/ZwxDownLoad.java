//package com.chis.modules.system.web;
//
//import java.io.BufferedInputStream;
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.IOException;
//
//import javax.servlet.ServletException;
//import javax.servlet.ServletOutputStream;
//import javax.servlet.annotation.WebServlet;
//import javax.servlet.http.HttpServlet;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//
///**
// * 存在使用漏洞，禁用此方法
// */
//@Deprecated
//@WebServlet(name="DownLoad",value="/DownLoad")
//public class ZwxDownLoad extends HttpServlet{
//
//	@SuppressWarnings("unchecked")
//	public void doPost(HttpServletRequest request, HttpServletResponse response)
//			throws ServletException, IOException {
//		request.setCharacterEncoding("utf-8");
//		ServletOutputStream out = null;
//		response.reset();
//		response.setContentType("APPLICATION/OCTET-STREAM");
//		String name = request.getParameter("name");
//		String path = request.getParameter("path");
//		File file = new File(path);
//
//		try {
//			response.addHeader("Content-Length", "" + file.length());
//			response.setHeader("Content-Disposition", "attachment; filename=\""
//					+ new String(name.getBytes(), "ISO-8859-1") + "\"");
//			BufferedInputStream in = null;
//			try {
//				in = new BufferedInputStream(
//						new FileInputStream(file));
//				out = response.getOutputStream();
//
//				int len = -1;
//				byte[] b = new byte[1024];
//				while ((len = in.read(b)) != -1) {
//					out.write(b, 0, len);
//					out.flush();
//				}
//			} catch (Exception e) {
//				e.printStackTrace();
//			} finally {
//				if (in != null) {
//					try {
//						in.close();
//					} catch (Exception e) {
//						e.printStackTrace();
//					}
//				}
//				if (out != null) {
//					try {
//						out.close();
//					} catch (Exception e) {
//						e.printStackTrace();
//					}
//				}
//			}
//
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//
//	}
//
//	@Override
//	protected void doGet(HttpServletRequest req, HttpServletResponse resp)
//			throws ServletException, IOException {
//		super.doGet(req, resp);
//	}
//}
