package com.chis.modules.system.web;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.chis.common.utils.FileUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.logic.FilePo;
import com.chis.modules.system.logic.ImgBean;
import com.chis.modules.system.logic.PdfMarkBean;
import com.chis.modules.system.utils.GeneratePdf;
import com.google.common.collect.Lists;

/**
 * 汉王快拍，界面大小650,580 <br/>
 * 返回单个PDF的相对路径 <br/>
 * 文件存放的位置：<br/>
 * 1.videoImgPath 2.videoImgPdfPath 可以修改初始化， 1*.需要传入参数"imgPath"，多个图片路径以,隔开 <br/>
 * 2*.pdfPath <br/>
 * 3.title,默认“文件扫描” 4. max,单个PDF最多能扫描多少张图片
 * 
 * <AUTHOR>
 * @createTime 2016年7月21日
 */
@ManagedBean(name = "takePicturesBean")
@ViewScoped
public class TakePicturesBean extends FacesBean {

	private static final long serialVersionUID = -1100693111644511194L;
	private String binaryStr;
	private List<ImgBean> imgList = Lists.newArrayList();
	private String imgsJson;
	/** 图片存放的位置 */
	private String videoImgPath;
	/** PDF存放的位置 */
	private String videoImgPdfPath;
	/** PDF数据集 */
	private List<FilePo> fileList;
	/** 单个PDF */
	private FilePo filePo;
	private String fileName;
	/**
	 * 文字水印
	 */
	private List<PdfMarkBean> marks;
	/** 该界面标题 */
	private String title;
	/** 单个PDF最多扫描多少张图片 */
	private Integer max;

	public TakePicturesBean() {
		this.initData();
	}

	private void initData() {
		// String allImgPath = JsfUtil.getRequest().getParameter("imgPath");
		this.fileList = (List<FilePo>) JsfUtil.getSession().getAttribute(
				"fileList");
		JsfUtil.getSession().removeAttribute("fileList");
		fileName = JsfUtil.getRequest().getParameter("fileName");
		if (null != this.fileList && this.fileList.size() > 0) {
			int index = 0;
			for (FilePo file : fileList) {
				List<ImgBean> list = Lists.newLinkedList();
				file.setImgList(list);
				file.setFileName(fileName
						+ "-"
						+ StringUtils.leftPad((index+ 1)
								+ "", 3, "0"));
				index++;
				if (StringUtils.isNotBlank(file.getImgPaths())) {
					String[] imgPaths = file.getImgPaths().split(",");
					for (int i = 0; i < imgPaths.length; i++) {
						ImgBean img = new ImgBean();
						img.setDesc(i + 1 + "");
						img.setPath(imgPaths[i]);
						file.getImgList().add(img);
					}
				}
			}
			this.filePo = this.fileList.get(0);
			this.imgList = this.filePo.getImgList();
			if (null == this.imgList) {
				this.imgList = Lists.newLinkedList();
			}
		} else {
			this.fileList = Lists.newLinkedList();
			this.filePo = new FilePo();
			filePo.setFileName(fileName + "-001");
			this.imgList = Lists.newLinkedList();
			filePo.setImgList(imgList);
			fileList.add(filePo);
		}
		this.imgsJson = JSON.toJSONString(this.imgList);
		String pdfMark = JsfUtil.getRequest().getParameter("pageMark");
		if (StringUtils.isNotBlank(pdfMark)) {
			marks = JSONArray.parseArray(pdfMark, PdfMarkBean.class);
		}
		this.videoImgPath = JsfUtil.getRequest().getParameter("videoImgPath");
		this.videoImgPdfPath = JsfUtil.getRequest().getParameter(
				"videoImgPdfPath");

		if (StringUtils.isBlank(this.videoImgPath)) {
			this.videoImgPath = "/vedioInput";
		}
		if (StringUtils.isBlank(this.videoImgPdfPath)) {
			this.videoImgPdfPath = "/vedioInputPdf";
		}

		this.title = JsfUtil.getRequest().getParameter("title");
		if (StringUtils.isBlank(this.title)) {
			this.title = "文件扫描";
		}

		String maxStr = JsfUtil.getRequest().getParameter("max");
		if (StringUtils.isBlank(maxStr)) {
			this.max = 50;
		} else {
			this.max = Integer.valueOf(maxStr);
		}
	}

	public void savePicture() {
		if (StringUtils.isBlank(this.binaryStr)) {
			JsfUtil.addErrorMessage("请重新拍照！");
			return;
		} else {
			StringBuilder sb = new StringBuilder();
			sb.append(this.videoImgPath);
			File file = new File(JsfUtil.getAbsolutePath() + sb.toString());
			if (!file.exists()) {
				file.mkdirs();
			}
			sb.append("/")
					.append(UUID.randomUUID().toString().replaceAll("-", ""))
					.append(".jpg");
			String imgPath = sb.toString();
			try {
				FileUtils.base64ToImage(this.binaryStr,
						JsfUtil.getAbsolutePath() + imgPath);

				String inx = JsfUtil.getRequestParameterMap().get("inx");
				ImgBean imgBean = new ImgBean();
				imgBean.setPath(imgPath);
				// 插入
				if (StringUtils.isNotBlank(inx)) {
					int index = Integer.parseInt(inx);
					if (imgList.size() >= this.max) {
						ImgBean last = imgList.remove(imgList.size() - 1);
						int fileIndex = fileList.indexOf(filePo);
						if (fileIndex < fileList.size() - 1) {
							FilePo next = fileList.get(fileIndex + 1);
							if (next.getImgList().size() < max) {
								next.getImgList().add(0, last);
								reorderImgList(next.getImgList());
							} else {
								next = new FilePo();
								next.setFileName(fileName
										+ "-"
										+ StringUtils.leftPad((fileIndex + 1)
												+ "", 3, "0"));
								List<ImgBean> nextImgList = Lists
										.newLinkedList();
								last.setDesc(1 + "");
								nextImgList.add(last);
								next.setImgList(nextImgList);
								this.fileList.add(fileIndex + 1, next);
								resetFileIndex();
							}
						} else {
							FilePo next = new FilePo();
							next.setFileName(fileName
									+ "-"
									+ StringUtils.leftPad((fileIndex + 1) + "",
											3, "0"));
							List<ImgBean> nextImgList = Lists.newLinkedList();
							last.setDesc(1 + "");
							nextImgList.add(last);
							next.setImgList(nextImgList);
							this.fileList.add(fileIndex + 1, next);
							resetFileIndex();
						}
					}
					this.imgList.add(index - 1, imgBean);
					this.reorderImgList();

				} else {
					// 超过每页限制
					if (imgList.size() >= this.max) {
						int fileIndex = fileList.indexOf(filePo);
						// 当前文件不是最后一个
						if (fileIndex < fileList.size() - 1) {
							// 下一个图片列表已放满
							if (fileList.get(fileIndex + 1).getImgList().size() >= this.max) {
								this.filePo = new FilePo();
								this.filePo.setFileName(fileName
										+ "-"
										+ StringUtils.leftPad((fileIndex + 1)
												+ "", 3, "0"));
								this.imgList = Lists.newLinkedList();
								this.filePo.setImgList(imgList);
								this.fileList.add(fileIndex + 1, filePo);
								resetFileIndex();
								imgBean.setDesc((this.imgList.size() + 1) + "");
								this.imgList.add(imgBean);
							} else {// 下一个图片列表未放满
								this.filePo = fileList.get(fileIndex + 1);
								this.imgList = filePo.getImgList();
								this.imgList.add(0, imgBean);
								this.reorderImgList();
							}
						} else {
							this.filePo = new FilePo();
							this.filePo.setFileName(fileName
									+ "-"
									+ StringUtils.leftPad((fileList.size() + 1)
											+ "", 3, "0"));
							this.imgList = Lists.newLinkedList();
							this.filePo.setImgList(imgList);
							this.fileList.add(this.filePo);
							imgBean.setDesc((this.imgList.size() + 1) + "");
							this.imgList.add(imgBean);
						}
					} else {
						imgBean.setDesc((this.imgList.size() + 1) + "");
						this.imgList.add(imgBean);
					}
				}
				this.imgsJson = JSON.toJSONString(this.imgList);
			} catch (Exception e) {
				e.printStackTrace();
				JsfUtil.addErrorMessage("网络异常，请重试！");
				return;
			}
		}
	}

	private void resetFileIndex() {
		String fileName = fileList.get(0).getFileName()
				.substring(0, fileList.get(0).getFileName().lastIndexOf("-"));
		for (int i = 0; i < fileList.size(); i++) {
			fileList.get(i).setFileName(
					fileName + "-" + StringUtils.leftPad((i + 1) + "", 3, "0"));
		}
	}

	public void deletePicture() {
		String inx = JsfUtil.getRequestParameterMap().get("inx");
		this.imgList.remove(Integer.parseInt(inx) - 1);

		this.reorderImgList();
		this.imgsJson = JSON.toJSONString(this.imgList);
	}

	private void reorderImgList() {
		for (int i = 0; i < this.imgList.size(); i++) {
			ImgBean imgBean = this.imgList.get(i);
			imgBean.setDesc((i + 1) + "");
		}
	}

	private void reorderImgList(List<ImgBean> imgList) {
		for (int i = 0; i < imgList.size(); i++) {
			ImgBean imgBean = imgList.get(i);
			imgBean.setDesc((i + 1) + "");
		}
	}

	/**
	 * 选择确定方法
	 */
	public void confirmAction() {
		Map<String, Object> map = new HashMap<String, Object>();

		try {
			GeneratePdf gp = new GeneratePdf();
			File file = new File(JsfUtil.getAbsolutePath()
					+ this.videoImgPdfPath);
			if (!file.exists()) {
				file.mkdirs();
			}
			String filePath = fileList.get(0).getFilePath();
			if (StringUtils.isBlank(filePath)) {
				filePath = UUID.randomUUID().toString().replaceAll("-", "");
			} else {
				if (filePath.contains("-")) {
					filePath = filePath.substring(
							filePath.lastIndexOf("/") + 1,
							filePath.lastIndexOf("-"));
				} else {
					filePath = filePath.substring(
							filePath.lastIndexOf("/") + 1,
							filePath.lastIndexOf("."));
				}
			}
			int i = 0;
			for (FilePo po : fileList) {
				StringBuilder sb = new StringBuilder();
				sb.append(this.videoImgPdfPath);
				sb.append("/").append(filePath);
				sb.append("-" + StringUtils.leftPad(i + 1 + "", 3, "0"));
				sb.append(".pdf");
				po.setFilePath(sb.toString());
				StringBuilder img = new StringBuilder();
				StringBuilder imgsPath = new StringBuilder();
				for (ImgBean ib : po.getImgList()) {
					img.append(",").append(JsfUtil.getAbsolutePath())
							.append(ib.getPath());
					imgsPath.append(",").append(ib.getPath());
				}
				String allImgPath = img.toString().replaceFirst(",", "");
				po.setImgPaths(imgsPath.toString().replaceFirst(",", ""));
				File pdf = gp.pdf(allImgPath,
						JsfUtil.getAbsolutePath() + po.getFilePath(), marks);
				pdf.createNewFile();
				i++;
			}
			map.put("dataList", this.fileList);
			RequestContext.getCurrentInstance().execute("vedioInputClear()");
			RequestContext.getCurrentInstance().closeDialog(map);
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("网络异常，请重试！");
			return;
		}
	}

	public void fileChangeAction() {
		this.imgList = filePo.getImgList();
		this.imgsJson = JSON.toJSONString(this.imgList);
	}

	/**
	 * 关闭
	 */
	public void dialogClose() {
		System.err.println("【弹出框关闭】：");
		RequestContext.getCurrentInstance().closeDialog(null);
	}

	public String getBinaryStr() {
		return binaryStr;
	}

	public void setBinaryStr(String binaryStr) {
		this.binaryStr = binaryStr;
	}

	public List<ImgBean> getImgList() {
		return imgList;
	}

	public void setImgList(List<ImgBean> imgList) {
		this.imgList = imgList;
	}

	public String getImgsJson() {
		return imgsJson;
	}

	public void setImgsJson(String imgsJson) {
		this.imgsJson = imgsJson;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Integer getMax() {
		return max;
	}

	public void setMax(Integer max) {
		this.max = max;
	}

	public List<FilePo> getFileList() {
		return fileList;
	}

	public void setFileList(List<FilePo> fileList) {
		this.fileList = fileList;
	}

	public FilePo getFilePo() {
		return filePo;
	}

	public void setFilePo(FilePo filePo) {
		this.filePo = filePo;
	}

}
