package com.chis.modules.system.web;

import com.chis.common.utils.*;
import com.chis.modules.system.entity.TdPublishNoticeUnit;
import com.chis.modules.system.entity.TdPublishNoticeUnitAnnex;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.TdPublishNoticeService;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

@ManagedBean(name="tdPublishNoticeQueryListBean")
@ViewScoped
public class TdPublishNoticeQueryListBean extends FacesEditBean{
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private TdPublishNoticeService noticeService = SpringContextHolder.getBean(TdPublishNoticeService.class);
    /**查询条件-发布类型*/
    private List<TsSimpleCode> typeList;
    private String selectTypeNames;
    private String selectTypeIds;
    /**查询条件-标题*/
    private String searchTitle;
    /**查询条件：发布日期-开始日期*/
    private Date searchBdate;
    /**查询条件：发布日期-结束日期*/
    private Date searchEdate;
    /**查询条件：选中的状态 */
    private String[] states;
    /** 用于详情的 */
    private TdPublishNoticeListBean noticeListBean;
    /** 通知单位rid */
    private Integer rid;
    /** 查看详情传递的rid */
    private Integer mainRid;
    /** 通知单位回执附件列表 */
    private List<TdPublishNoticeUnitAnnex> noticeUnitAnnexList;
    /** 回执附件分页行数 */
    private Integer noticeAnnexPageSize;
    /** 当前通知单位回执附件 */
    private TdPublishNoticeUnitAnnex curUnitAnnex;

    public TdPublishNoticeQueryListBean(){
        this.ifSQL = true;
        init();
        this.searchAction();
    }

    @Override
    public void addInit() {

    }

    /**
     * @Description: 详情调用
     *
     * @MethodAuthor pw,2022年03月15日
     */
    public void preView(){
        this.noticeUnitAnnexList.clear();
        if(null == this.rid){
            return;
        }
        try{
            TdPublishNoticeUnit noticeUnit = this.commService.find(TdPublishNoticeUnit.class, this.rid);
            if(null == noticeUnit){
                JsfUtil.addErrorMessage("未找到详情信息！");
                return;
            }
            this.mainRid = null == noticeUnit.getFkByMainId() ? null : noticeUnit.getFkByMainId().getRid();
            if(null == this.mainRid){
                return;
            }
            List<TdPublishNoticeUnitAnnex> queryUnitAnnexResultList = this.commService.findEntityListByMainId(TdPublishNoticeUnitAnnex.class, this.rid);
            if(!CollectionUtils.isEmpty(queryUnitAnnexResultList)){
                this.noticeUnitAnnexList.addAll(queryUnitAnnexResultList);
                int tmpNum = 0-this.noticeUnitAnnexList.size();
                //无序号的赋值
                for(TdPublishNoticeUnitAnnex unitAnnex : this.noticeUnitAnnexList){
                    if(null == unitAnnex.getXh()){
                        unitAnnex.setXh(tmpNum++);
                    }
                }
                Collections.sort(this.noticeUnitAnnexList, new Comparator<TdPublishNoticeUnitAnnex>() {
                    @Override
                    public int compare(TdPublishNoticeUnitAnnex o1, TdPublishNoticeUnitAnnex o2) {
                        Integer xh1 = o1.getXh();
                        Integer xh2 = o2.getXh();
                        return xh1.compareTo(xh2);
                    }
                });
            }
            if(null == noticeUnit.getState() || 1 != noticeUnit.getState()){
                this.noticeService.clickNewNoticeLink(String.valueOf(this.mainRid));
                //修改状态后 需重新查询
                this.searchAction();
            }
            this.viewInitAction();
        }catch(Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("读取详情失败！");
        }
    }

    @Override
    public void viewInit() {
        if(null == this.mainRid){
            return;
        }
        this.noticeListBean.setRid(this.mainRid);
        this.noticeListBean.preModOrViewPublishNotice(1);
    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT T1.RID, ")
                .append(" T2.CODE_PATH, ")
                .append(" T2.EXTENDS1, ")
                .append(" T.OTHER_TYPE, ")
                .append(" T.TITLE, ")
                .append(" T.PUBLISH_DATE, ")
                .append(" T3.UNITNAME, ")
                .append(" T1.STATE, ")
                .append(" T2.NUM, ")
                .append(" T.IF_NEED_FEEDBACK, ")
                .append(" '' ")
                .append(" FROM TD_PUBLISH_NOTICE T ")
                .append(" INNER JOIN TD_PUBLISH_NOTICE_UNIT T1 ON T1.MAIN_ID = T.RID ")
                .append(" INNER JOIN TS_SIMPLE_CODE T2 ON T.PUBLISH_TYPE_ID = T2.RID ")
                .append(" INNER JOIN TS_UNIT T3 ON T3.RID = T.PUBLISH_UNIT_ID ")
                .append(" WHERE T.STATE = 1 ")
                .append(" AND T1.UNIT_ID =").append(Global.getUser().getTsUnit().getRid()).append(" ");
        //发布类型
        if(StringUtils.isNotBlank(selectTypeIds)){
            sql.append(" AND T.PUBLISH_TYPE_ID in (").append(this.selectTypeIds).append(")");
        }
        //标题
        if (StringUtils.isNotBlank(this.searchTitle)) {
            sql.append(" AND T.TITLE LIKE :searchTitle  escape '\\\'");
            this.paramMap.put("searchTitle", "%" + StringUtils.convertBFH(this.searchTitle.trim()) + "%");
        }
        // 发布日期
        if (null != this.searchBdate) {
            sql.append(" AND T.PUBLISH_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchEdate) {
            sql.append(" AND T.PUBLISH_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }

        //状态
        if (null!=states && states.length==1) {
            sql.append(" AND T1.STATE in (").append(this.states[0]).append(")");
        }
        String h2 = "SELECT COUNT(*) FROM (" + sql.toString() + ")";
        String h1 = "SELECT * FROM (" + sql.toString() + ")AA  ORDER BY AA.STATE,AA.PUBLISH_DATE DESC,AA.NUM,AA.TITLE";
        return new String[] { h1, h2 };
    }

    /**
     * <p>方法描述： 删除附件 </p>
     * @MethodAuthor： pw 2023/7/18
     **/
    public void removeNoticeUnitAnnex(){
        if(null == this.curUnitAnnex || CollectionUtils.isEmpty(this.noticeUnitAnnexList)){
            return;
        }
        try{
            this.noticeUnitAnnexList.remove(this.curUnitAnnex);
            this.commService.deleteEntity(TdPublishNoticeUnitAnnex.class, this.curUnitAnnex.getRid());
            JsfUtil.addSuccessMessage("删除成功！");
        }catch(Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * <p>方法描述： 上传附件 </p>
     * @MethodAuthor： pw 2023/7/18
     **/
    public synchronized void fileUpload(FileUploadEvent event) {
        if (null == event) {
            return;
        }
        UploadedFile file = event.getFile();
        try {
            // 文件名称
            String fileName = file.getFileName();
            String uuid = java.util.UUID.randomUUID().toString().replaceAll("-", "");
            String path = JsfUtil.getAbsolutePath();
            String relativePath = new StringBuffer("sysnotice/").append(uuid)
                    .append(fileName.substring(fileName.lastIndexOf("."))).toString();
            // 文件路径
            String filePath = path + relativePath;
            String errorMsg = FileUtils.veryFile(file.getInputstream(),file.getContentType(), file.getFileName(), "7");
            if (StringUtils.isNotBlank(errorMsg)) {
                JsfUtil.addErrorMessage(errorMsg);
                return;
            }
            FileUtils.copyFile(filePath, file.getInputstream());
            TdPublishNoticeUnitAnnex curUnitAnnex = new TdPublishNoticeUnitAnnex();
            curUnitAnnex.setFkByMainId(new TdPublishNoticeUnit(this.rid));
            curUnitAnnex.setAnnexName(fileName);
            curUnitAnnex.setAnnexAddr(relativePath);
            Integer xh = CollectionUtils.isEmpty(this.noticeUnitAnnexList) ? 0 :
                    this.noticeUnitAnnexList.get(this.noticeUnitAnnexList.size()-1).getXh()+1;
            if(null == xh){
                xh = 0;
            }
            curUnitAnnex.setXh(xh);
            this.commService.upsertEntity(curUnitAnnex);
            this.noticeUnitAnnexList.add(curUnitAnnex);
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("PF('FileDialog').hide();");
            currentInstance.update("tabView:viewForm:noticeAnnexDataTable");
            JsfUtil.addSuccessMessage("上传成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("上传失败！");
            throw new RuntimeException(e);
        }
    }

    /**
     * @Description: 初始化
     *
     * @MethodAuthor pw,2022年03月15日
     */
    private void init(){
        this.noticeListBean = new TdPublishNoticeListBean();
        this.typeList = this.commService.findLevelSimpleCodesByTypeId("5546");
        this.searchEdate = DateUtils.getDateOnly(new Date());
        this.searchBdate = DateUtils.addYears(this.searchEdate, -1);
        this.states = new String[]{"0"};
        this.noticeAnnexPageSize = 10;
        this.noticeUnitAnnexList = new ArrayList<>();
    }

    public List<TsSimpleCode> getTypeList() {
        return typeList;
    }

    public void setTypeList(List<TsSimpleCode> typeList) {
        this.typeList = typeList;
    }

    public String getSelectTypeNames() {
        return selectTypeNames;
    }

    public void setSelectTypeNames(String selectTypeNames) {
        this.selectTypeNames = selectTypeNames;
    }

    public String getSelectTypeIds() {
        return selectTypeIds;
    }

    public void setSelectTypeIds(String selectTypeIds) {
        this.selectTypeIds = selectTypeIds;
    }

    public String getSearchTitle() {
        return searchTitle;
    }

    public void setSearchTitle(String searchTitle) {
        this.searchTitle = searchTitle;
    }

    public Date getSearchBdate() {
        return searchBdate;
    }

    public void setSearchBdate(Date searchBdate) {
        this.searchBdate = searchBdate;
    }

    public Date getSearchEdate() {
        return searchEdate;
    }

    public void setSearchEdate(Date searchEdate) {
        this.searchEdate = searchEdate;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public TdPublishNoticeListBean getNoticeListBean() {
        return noticeListBean;
    }

    public void setNoticeListBean(TdPublishNoticeListBean noticeListBean) {
        this.noticeListBean = noticeListBean;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Integer getMainRid() {
        return mainRid;
    }

    public void setMainRid(Integer mainRid) {
        this.mainRid = mainRid;
    }

    public List<TdPublishNoticeUnitAnnex> getNoticeUnitAnnexList() {
        return noticeUnitAnnexList;
    }

    public void setNoticeUnitAnnexList(List<TdPublishNoticeUnitAnnex> noticeUnitAnnexList) {
        this.noticeUnitAnnexList = noticeUnitAnnexList;
    }

    public Integer getNoticeAnnexPageSize() {
        return noticeAnnexPageSize;
    }

    public void setNoticeAnnexPageSize(Integer noticeAnnexPageSize) {
        this.noticeAnnexPageSize = noticeAnnexPageSize;
    }

    public TdPublishNoticeUnitAnnex getCurUnitAnnex() {
        return curUnitAnnex;
    }

    public void setCurUnitAnnex(TdPublishNoticeUnitAnnex curUnitAnnex) {
        this.curUnitAnnex = curUnitAnnex;
    }
}
