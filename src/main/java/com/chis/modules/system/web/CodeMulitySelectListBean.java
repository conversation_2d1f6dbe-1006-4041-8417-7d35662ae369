package com.chis.modules.system.web;

import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.*;
import java.util.Map.Entry;

/**
 * <p>类描述：码表弹出框</p>
 * @ClassAuthor qrr,2018年4月9日,CodeRadioSelectBean
 */
@ManagedBean(name = "codeMulitySelectListBean")
@ViewScoped
public class CodeMulitySelectListBean extends FacesBean {

	private static final long serialVersionUID = -738445454536625063L;

	/** 名称 或 拼音码*/
	private String searchNamOrPy;
	/**
	 * 是否显示查询条件: 编码
	 */
	private Boolean showSelectCodeNo;
	/**
	 * 查询条件: 编码
	 */
	private String searchCodeNo;
	/** 标题*/
	private String titleName;
	/** 码表类型*/
	private String typeNo;
	/**码表大类*/
	private String selectIds;
	private List<TsSimpleCode> firstList;
	private Map<String,String> firstMap;
	/**查询条件大类是否显示*/
	private Boolean ifShowFirstCode = false;
	/** 查询列集合*/
	private List<TsSimpleCode> displayList;
	/** 所有集合*/
	private List<TsSimpleCode> allList;

	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	/**为1时,提交时只选择最末级*/
	private String selectLast;
	/**为1时,选择时只选择最末级*/
	private String selLast;
	private Map<String, List<TsSimpleCode>> levelMap;
	/**所有码表map，key:层级编码，val:TsSimpleCode*/
	private Map<String, TsSimpleCode> allCodeMap;
	/**只可选择同一层级的选项，选择一个级别后，非此级别的灰掉不可选择*/
	private boolean selectSameLevel = false;
	/**可选择个数*/
	private Integer selectNum;
	/**主要粉尘只能选择一种 1：表示主要粉尘只能选一种  其他:表示无此工能*/
	private String ifMainDust;

	/**查询条件的名称*/
	private String searchName;

	/**列表列名*/
	private String colName;
	/**不可操作的码表rids*/
	private String selectIdDisabled;

	/**自定义列-显示字段的列名1*/
	private String columnName;
	/**自定义列-显示字段的列名1-取值下标*/
	private Integer columnIndex;
	/**自定义列-是否脱敏显示*/
	private String ifEncrypt;

	/**码表扩展字段6*/
	private String extends6;
	/**是否显示全部选择按钮*/
	private Boolean showAllSel;
	/**为1时,提交时若选择大类时只存储大类*/
	private String selectFirst;
	/** codeDesc包含的 英文逗号分隔 用于过滤 类似5619以及5620获取对应资质的码表 */
	private String codeDescContains;
	/**业务范围与检测项目关系配置 只适用于次模块 后面会优化掉*/
	private String ifBusScopeItemConfig;
	/**是否显示账号有效期*/
	private String ifShowExpiryDate;
	/**账号有效期-开始时间*/
	private  Date validBDate;
	/**账号有效期-结束时间*/
	private  Date validEDate;
	/**
	 * 根据codeLevelNo like查询
	 */
	private String selByCodeLevelNo;
	private String notSelectIds;
	/**
	 * 内蒙古质控考核表维护模块专用 选择指标码表弹框
	 * 层级为2时，最末级全选中，父级不显示
	 * 配合参数noContainsSelected和selectIds一起使用
	 */
	private boolean ifShowFirst;
	/**
	 * 只查询指定级别的数据(根据CODE_LEVEL_NO的.的数量过滤)
	 */
	private Integer onlyLevelNo;
	/**
	 * <p>方法描述：初始化数据</p>
	 * @MethodAuthor qrr,2018年4月9日,CodeRadioSelectBean
	 * */
	public CodeMulitySelectListBean() {
		this.titleName = JsfUtil.getRequest().getParameter("titleName");
		String typeNo = JsfUtil.getRequest().getParameter("typeNo");
		String extends1 = JsfUtil.getRequest().getParameter("extends1");
		String selectIds = JsfUtil.getRequest().getParameter("selectIds");//已选择的码表rid，以逗号隔开
		this.notSelectIds = JsfUtil.getRequest().getParameter("notSelectIds");//需排除码表rid，以逗号隔开
		String ifShowFirstCode = JsfUtil.getRequest().getParameter("ifShowFirstCode");
		String showIds = JsfUtil.getRequest().getParameter("showIds");
		this.ifShowExpiryDate= JsfUtil.getRequest().getParameter("ifShowExpiryDate");
		if(StringUtils.isNotBlank(JsfUtil.getRequest().getParameter("validBDate"))){
			this.validBDate= DateUtils.parseDate(JsfUtil.getRequest().getParameter("validBDate"));
		}
		if(StringUtils.isNotBlank(JsfUtil.getRequest().getParameter("validEDate"))){
			this.validEDate= DateUtils.parseDate(JsfUtil.getRequest().getParameter("validEDate"));
		}
		this.ifBusScopeItemConfig= JsfUtil.getRequest().getParameter("ifBusScopeItemConfig");
		this.codeDescContains = JsfUtil.getRequest().getParameter("codeDescContains");
        this.selectLast = JsfUtil.getRequest().getParameter("selectLast");
        this.selLast = JsfUtil.getRequest().getParameter("selLast");
		//查询条件名称
		this.searchName=JsfUtil.getRequest().getParameter("searchName");
		//列表列名
		this.colName=JsfUtil.getRequest().getParameter("colName");
		//自定义显示字段的列名1
		columnName = JsfUtil.getRequest().getParameter("columnName");
		String columnIndexStr = JsfUtil.getRequest().getParameter("columnIndex");
		this.columnIndex = StringUtils.isBlank(columnIndexStr) ? 11 : Integer.valueOf(columnIndexStr);
		//是否脱敏显示
		ifEncrypt = JsfUtil.getRequest().getParameter("ifEncrypt");
		//已选中的是否显示；
		String noContainsSelected = JsfUtil.getRequest().getParameter("noContainsSelected");
		//需要禁用的码表
		this.selectIdDisabled = JsfUtil.getRequest().getParameter("selectIdDisabled");
		//码表扩展字段6
		this.extends6 = JsfUtil.getRequest().getParameter("extends6");
		this.typeNo = typeNo;
		if (StringUtils.isNotBlank(ifShowFirstCode)) {
			if ("true".equals(ifShowFirstCode)) {
				this.ifShowFirstCode = true;
			}else if ("false".equals(ifShowFirstCode)) {
				this.ifShowFirstCode = false;
			}
		}
		String selectSameLevel = JsfUtil.getRequest().getParameter("selectSameLevel");
		if ("1".equals(selectSameLevel)) {
			this.selectSameLevel = true;
		}else {
			this.selectSameLevel = false;
		}
		String selectNum = JsfUtil.getRequest().getParameter("selectNum");
		if (StringUtils.isNotBlank(selectNum)) {
			this.selectNum = Integer.valueOf(selectNum);
		}
		//主要粉尘选一种
		ifMainDust=JsfUtil.getRequest().getParameter("ifMainDust");
		this.selectFirst = JsfUtil.getRequest().getParameter("selectFirst");

		this.selByCodeLevelNo = JsfUtil.getRequest().getParameter("selByCodeLevelNo");

		//层级为2时，最末级全选中，父级不显示
		String ifShowFirstStr = JsfUtil.getRequest().getParameter("ifShowFirst");
		this.ifShowFirst = "1".equals(ifShowFirstStr);
		
		String onlyLevelNoStr = JsfUtil.getRequest().getParameter("onlyLevelNo");
		this.onlyLevelNo = ObjectUtil.convert(Integer.class, onlyLevelNoStr, null);

        this.init(extends1,selectIds,noContainsSelected, showIds);

		//是否显示查询条件编码
		String showSelectCodeNoStr = JsfUtil.getRequest().getParameter("showSelectCodeNo");
		this.showSelectCodeNo = "true".equals(showSelectCodeNoStr);
		//是否显示选择全部按钮
		String showAllSelStr = JsfUtil.getRequest().getParameter("showAllSel");
		this.showAllSel = "true".equals(showAllSelStr);
    }
	private Set<String> generateFilterCodeDescSet() {
		Set<String> filterCodeDescSet = new HashSet<>();
		if (StringUtils.isBlank(this.codeDescContains)) {
			return filterCodeDescSet;
		}
		for (String str : this.codeDescContains.split(",")) {
			if(StringUtils.isBlank(str)) {
				continue;
			}
			filterCodeDescSet.add(str.trim());
		}
		return filterCodeDescSet;
	}

	private boolean judgeCodeDescContains(Set<String> filterCodeDescSet, String codeDesc) {
		if (CollectionUtils.isEmpty(filterCodeDescSet) || StringUtils.isBlank(codeDesc)) {
			return true;
		}
		for (String str : codeDesc.split(",")) {
			if (StringUtils.isBlank(str)) {
				continue;
			}
			if (filterCodeDescSet.contains(str.trim())) {
				return true;
			}
		}
		return false;
	}
    /**
 	 * <p>方法描述：查询码表数据</p>
 	 * @MethodAuthor qrr,2018年4月9日,CodeRadioSelectBean
     * */
    private void init(String extends1,String selectIds,String noContainsSelected, String showIds)   {
        this.displayList = new ArrayList<TsSimpleCode>();
        this.allList = new ArrayList<TsSimpleCode>();
        this.firstList = new ArrayList<TsSimpleCode>();
		this.firstMap = new HashMap<>();
		//查询启用的码表
		List<Object[]> list;
		if ("1".equals(ifBusScopeItemConfig)) {
			list = commService.findSimpleByParamTwo2(typeNo);
		} else if (ObjectUtil.isNotEmpty(this.selByCodeLevelNo)) {
			if (this.onlyLevelNo != null) {
				list = commService.findSimpleByParam3(typeNo, extends1, showIds, ifShowExpiryDate,notSelectIds, this.selByCodeLevelNo, this.onlyLevelNo);
			} else {
				list = this.commService.findSimpleByCodeLevelNo(this.typeNo, this.selByCodeLevelNo, selectIds);
			}
		} else if (StringUtils.isNotBlank(extends6)) {
			list = commService.findSimpleByParamTwo1(typeNo, extends6, showIds);
		} else if (this.onlyLevelNo != null) {
			list = commService.findSimpleByParam3(typeNo, extends1, showIds, ifShowExpiryDate,notSelectIds, this.selByCodeLevelNo, this.onlyLevelNo);
		} else {
			list = commService.findSimpleByParamTwo(typeNo, extends1, showIds, ifShowExpiryDate,notSelectIds);
		}
		List<TsSimpleCode> tsSimpleCodeList = new ArrayList<>();
		for (Object[] obj : list) {
			TsSimpleCode code = new TsSimpleCode();
			code.setRid(null!=obj[0]?Integer.valueOf(obj[0].toString()):null);
			code.setCodeName(null!=obj[1]?obj[1].toString():null);
			code.setCodeLevelNo(null!=obj[3]?obj[3].toString():null);
			code.setCodeNo(null!=obj[2]?obj[2].toString():null);
			code.setSplsht(null!=obj[4]?obj[4].toString():null);
			code.setExtendS1(null!=obj[5]?obj[5].toString():null);
			code.setExtendS2(null!=obj[6]?Integer.valueOf(obj[6].toString()):null);
			code.setExtendS3(StringUtils.objectToString(obj[7]));
			code.setExtendS4(StringUtils.objectToString(obj[8]));
			code.setExtendS5(StringUtils.objectToString(obj[9]));
			code.setLevelIndex(StringUtils.countMatches(code.getCodeLevelNo(), ".")+ "");
			if(StringUtils.isNotBlank(this.columnName)){
				code.setCodeDesc(null!=obj[11]?obj[11].toString():null);
				if("1".equals(ifEncrypt)){
					code.setEncryptStr1(null!=obj[this.columnIndex]?StringUtils.encryptPhone(obj[this.columnIndex].toString()):null);
				}else{
					code.setEncryptStr1(null!=obj[this.columnIndex]?obj[this.columnIndex].toString():null);
				}
			}
			tsSimpleCodeList.add(code);
		}
		dealLevelMap(tsSimpleCodeList);
		Set<String> filterCodeDescSet = this.generateFilterCodeDescSet();
        int row = -1;// 第一次出现勾选的当前页的第一行数
        TsSimpleCode selectLevelCode = null;
		if (null!=list && list.size()>0) {
			int  i = 0;
			//已选中的码表id
			List<String> resList = new ArrayList();
			if(StringUtils.isNotBlank(selectIds)){
				if(selectIds.contains(",")){
					String[] split = selectIds.split(",");
					for(String str:split){
						resList.add(str);
					}
				}else {
					resList.add(selectIds);
				}
			}

			for (Object[] obj : list) {
				if (null==obj[0]) {
					continue;
				}
				if ("1".equals(noContainsSelected)) {//已选择的不显示
					if (dealNoContainsSelected(resList,obj)) {
						continue;
					}
				}
				String codeDesc = null != obj[11] ? obj[11].toString() : null;
				//codeDesc 条件过滤
				if (!this.judgeCodeDescContains(filterCodeDescSet, codeDesc)) {
					continue;
				}
				TsSimpleCode code = new TsSimpleCode();
				code.setRid(null!=obj[0]?Integer.valueOf(obj[0].toString()):null);
				code.setCodeName(null!=obj[1]?obj[1].toString():null);
				code.setCodeLevelNo(null!=obj[3]?obj[3].toString():null);
				code.setCodeNo(null!=obj[2]?obj[2].toString():null);
				code.setSplsht(null!=obj[4]?obj[4].toString():null);
				code.setExtendS1(null!=obj[5]?obj[5].toString():null);
				code.setExtendS2(null!=obj[6]?Integer.valueOf(obj[6].toString()):null);
				code.setExtendS3(StringUtils.objectToString(obj[7]));
				code.setExtendS4(StringUtils.objectToString(obj[8]));
				code.setExtendS5(StringUtils.objectToString(obj[9]));
				code.setLevelIndex(StringUtils.countMatches(code.getCodeLevelNo(), ".")+ "");
				code.setCodePath(StringUtils.objectToString(obj[10]));
				if(StringUtils.isNotBlank(this.columnName)){
					code.setCodeDesc(codeDesc);
					if("1".equals(ifEncrypt)){
						code.setEncryptStr1(null!=obj[this.columnIndex]?StringUtils.encryptPhone(obj[this.columnIndex].toString()):null);
					}else{
						code.setEncryptStr1(null!=obj[this.columnIndex]?obj[this.columnIndex].toString():null);
					}
				}
				if (StringUtils.isNotBlank(selectIds)
						&& resList.contains(code.getRid().toString())) {
					code.setIfSelected(true);
					if (row == -1) {
						row = i - i % 10;
					}
					//已选择的级别
					if (null==selectLevelCode) {
						selectLevelCode = code;
					}
				}
				if (StringUtils.containsNone(code.getCodeLevelNo(), ".")) {
					firstList.add(code);
					firstMap.put(code.getRid().toString(),code.getCodeNo());
					if(this.ifShowFirst && this.levelMap.containsKey(code.getCodeLevelNo()) && CollectionUtils.isEmpty(this.levelMap.get(code.getCodeLevelNo()))){
						continue;
					}
				}
				allList.add(code);
				i++;
			}

		}
		//需要显示的码表list
		if( null != allList && allList.size() > 0)    {
			this.displayList.addAll(allList);
			//初始化选择当前页的第一行数
			if (row>-1) {
				DataTable dataTable = (DataTable) FacesContext
						.getCurrentInstance().getViewRoot()
						.findComponent("codeForm:selectedIndusTable");
				dataTable.setFirst(row);
			}
			if (selectSameLevel && null!=selectLevelCode) {
				dealIfDisabled(selectLevelCode);
			}
        }
        dealLevelMap(allList);
        if ("1".equals(selectLast)) {//子级全部勾选，父级勾选
        	initSelectParent();
        }
		if("1".equals(selectFirst)){
			//大类选中时以下的小类全部选中
			initSelectAllSubclass();
		}
		//只有最末级可选择
		if ("1".equals(selLast)) {
			lastSelectParent();
		}
		disabledSelectId();
    }
	/**
	 *  <p>方法描述：大类选中时以下的小类全部选中</p>
	 * @MethodAuthor hsj 2024-07-03 13:44
	 */
	private void initSelectAllSubclass() {
		if(CollectionUtils.isEmpty(this.allList)){
			return;
		}
		for(TsSimpleCode code: this.allList){
			if (!code.isIfSelected()) {
				continue;
			}
			List<TsSimpleCode> child2s = this.levelMap.get(code.getCodeLevelNo());
			if (CollectionUtils.isEmpty(child2s)) {
				continue;
			}
			selectAction(code);
		}

	}
	/**
	 *  <p>方法描述：禁用码表</p>
	 * @MethodAuthor hsj 2023-07-21 10:15
	 */
	private void disabledSelectId() {
		if(StringUtils.isBlank(this.selectIdDisabled) || CollectionUtils.isEmpty(displayList)){
			return;
		}
		List<String> list = StringUtils.string2list(this.selectIdDisabled,",");
		for(TsSimpleCode simpleCode :displayList){
			if(list.contains(simpleCode.getRid().toString())){
				simpleCode.setIfDisabled(true);
			}
		}
	}

	/**
	 *  <p>方法描述：只有最后以及可选择</p>
	 * @MethodAuthor hsj
	 */
	private void lastSelectParent() {
		if(CollectionUtils.isEmpty(displayList)){
			return;
		}
		for(TsSimpleCode simpleCode :displayList){
			if(ObjectUtil.isEmpty(simpleCode.getCodeLevelNo())){
				simpleCode.setIfDisabled(false);
				continue;
			}
			List<TsSimpleCode> childs = this.levelMap.get(simpleCode.getCodeLevelNo());
			if (CollectionUtils.isEmpty(childs)) {
				simpleCode.setIfDisabled(false);
			}else{
				simpleCode.setIfDisabled(true);
			}
		}
	}

	/**
	 * <p>方法描述：已选择的子级全部勾选，则父级勾选</p>
	 * @MethodAuthor qrr,2021年3月24日,initSelectParent
	 * */
	private void initSelectParent(){
		List<TsSimpleCode> parents = new ArrayList<>();
		for (Entry<String, List<TsSimpleCode>> entry : this.levelMap.entrySet()) {
			String key = entry.getKey();
			List<TsSimpleCode> childs = entry.getValue();
			if (!CollectionUtils.isEmpty(childs)) {
				TsSimpleCode parent = this.allCodeMap.get(key);
				parents.add(parent);
			}
		}
		//按照层级排序，层级高的在前面，若有3级，当只能选择最后一级时，最后一级全部勾选，第一、二级也勾选
		Collections.sort(parents, new Comparator<TsSimpleCode>() {
			@Override
			public int compare(TsSimpleCode o1, TsSimpleCode o2) {
				String codeLevelNo1 = o1.getCodeLevelNo();
				String codeLevelNo2 = o2.getCodeLevelNo();
				if (StringUtils.isNotBlank(codeLevelNo1)
						&& StringUtils.isNotBlank(codeLevelNo2)) {
					String[] split1 = codeLevelNo1.split("\\.");
					String[] split2 = codeLevelNo2.split("\\.");
					return split2.length - split1.length;
				}
				return 0;
			}
		});
		for (TsSimpleCode t : parents) {
			String codeLevelNo = t.getCodeLevelNo();
			if (StringUtils.isBlank(codeLevelNo)) {
				continue;
			}
			List<TsSimpleCode> childs = this.levelMap.get(codeLevelNo);
			selectParent(codeLevelNo, childs);
		}
	}
	/**
	 * <p>方法描述：子级全部勾选，父级勾选</p>
	 * @MethodAuthor qrr,2021年3月24日,selectParent
	 * */
	private void selectParent(String parentCode,List<TsSimpleCode> childs) {
		boolean ifAllChildSelect = true;
		if (!CollectionUtils.isEmpty(childs)) {
			for (TsSimpleCode t : childs) {
				if (!t.isIfSelected()) {
					ifAllChildSelect = false;
					break;
				}
			}
			if (ifAllChildSelect) {//子级全部勾选，父级勾选
				TsSimpleCode parent = this.allCodeMap.get(parentCode);
                if (parent == null) {
                    return;
                }
				parent.setIfSelected(true);
			}
		}
	}
	/**
	 * <p>方法描述：处理码表层级关系</p>
	 * @MethodAuthor qrr,2018年4月8日,dealCodelevel
	 * */
	private void dealLevelMap(List<TsSimpleCode> list){
		this.levelMap = new HashMap<>();
		this.allCodeMap = new HashMap<>();
		if (list != null && list.size() > 0) {
			for (TsSimpleCode obj : list) {
				String codeLevelNo = obj.getCodeLevelNo();
				if (StringUtils.isBlank(codeLevelNo)) {
					continue;
				}
				this.allCodeMap.put(codeLevelNo, obj);
				this.levelMap.put(codeLevelNo, new ArrayList<TsSimpleCode>());
				if (StringUtils.contains(codeLevelNo, ".")) {
					String[] split = codeLevelNo.split("\\.");
					StringBuffer parentCodeSb = new StringBuffer();
					for (int i = 0; i < split.length-1; i++) {//仅找出父级
						parentCodeSb.append(".").append(split[i]);
						String parentCode = parentCodeSb.substring(1);
						List<TsSimpleCode> childs = this.levelMap.get(parentCode);
						if (null==childs) {
							childs = new ArrayList<TsSimpleCode>();
							this.levelMap.put(parentCode, childs);
						}
						childs.add(obj);
					}
				}
			}
		}
	}
	/**
	 * <p>方法描述：处理已选择的数据，界面不显示</p>
	 * @MethodAuthor qrr,2020年2月15日,dealNoContainsSelected
	 * */
	private boolean dealNoContainsSelected(List resList,Object[] obj) {

		if (!CollectionUtils.isEmpty(resList)) {
			if (resList.contains(obj[0].toString())) {
				return true;
			}
			if(obj[3] == null){
				return false;
			}
			//最末级全部已选择，上级全部不显示
			List<TsSimpleCode> childs = this.levelMap.get(obj[3].toString());
			if (null!=childs && childs.size()>0) {
				boolean flag = false;
				for (TsSimpleCode child : childs) {
					if(ObjectUtil.isNotEmpty(child.getCodeLevelNo())){
						List<TsSimpleCode> child2s = this.levelMap.get(child.getCodeLevelNo());
						if (!CollectionUtils.isEmpty(child2s)) {//不是最末级，继续
							continue;
						}
					}
					if (!resList.contains(child.getRid().toString())) {//最末子级存在未选择的，则退出循环
						flag = true;
						break;
					}
				}
				if (!flag) {//最末级全部已选择，父级不显示
					return true;
				}
			}
		}
		return false;
	}
	/**
	 * <p>方法描述：勾选大类，则小类默认全部选择</p>
	 * @MethodAuthor qrr,2019年12月2日,selectAction
	 * */
	public void selectAction(TsSimpleCode code){
		if(!"1".equals(selLast)){
			if (!selectSameLevel) {
				dealIfSelected(code);
			}else {
				dealIfDisabled(code);
			}
		}
	}
	/**
	 * <p>方法描述：处理是否选中</p>
	 * @MethodAuthor qrr,2020年6月5日,dealIfSelected
	 * */
	private void dealIfSelected(TsSimpleCode code) {
		String codeLevelNo = code.getCodeLevelNo();
		if (StringUtils.isBlank(codeLevelNo)) {
			return;
		}
		if (code.isIfSelected()){//大类勾选，小类全部选择
			for (TsSimpleCode t : allList) {
				String levelNo = t.getCodeLevelNo();
				if (StringUtils.isBlank(levelNo)) {
					continue;
				}
				if (levelNo.startsWith(codeLevelNo + ".")) {//下级全部勾选
					t.setIfSelected(true);
				}
			}
			if (StringUtils.contains(codeLevelNo, ".")) {//子级勾选
				int endIndex = codeLevelNo.lastIndexOf(".");
				String parentCode = codeLevelNo.substring(0, endIndex);
				List<TsSimpleCode> childList = this.levelMap.get(parentCode);//上级下一级的所有子级
				selectParent(parentCode, childList);//子级全部勾选，父级勾选
			}
		}else {//大类不勾选，小类不选择
			for (TsSimpleCode t : allList) {
				String levelNo = t.getCodeLevelNo();
				if (StringUtils.isBlank(levelNo)) {
					continue;
				}
				if (codeLevelNo.startsWith(levelNo + ".")) {//上级不勾选
					t.setIfSelected(false);
				}
				if (levelNo.startsWith(codeLevelNo + ".")) {//下级不勾选
					t.setIfSelected(false);
				}
			}
		}
	}
	/**
	 * <p>方法描述：处理是否允许选择</p>
	 * @MethodAuthor qrr,2020年6月5日,dealIfSelected
	 * */
	private void dealIfDisabled(TsSimpleCode code) {
		if (code.isIfSelected()){
			if (StringUtils.isBlank(code.getCodeLevelNo())) {
				return;
			}
			String[] selectLevel = code.getCodeLevelNo().split("\\.");
			//非同级disabled
			for (TsSimpleCode t : allList) {
				if (StringUtils.isBlank(t.getCodeLevelNo())) {
					continue;
				}
				String[] level = t.getCodeLevelNo().split("\\.");
				if (selectLevel.length!=level.length) {
					t.setIfDisabled(true);
				}else {
					t.setIfDisabled(false);
				}
			}
		}else {
			//所有码表都未选中，所有码表允许选择
			for (TsSimpleCode t : allList) {
				if (t.isIfSelected()) {
					return;
				}
			}
			for (TsSimpleCode t : allList) {
				t.setIfDisabled(false);
			}
		}
	}
	/**
	 * <p>方法描述：根据名称、拼音码过滤数据</p>
	 * @MethodAuthor qrr,2018年4月9日,searchAction
	 */
	public void searchAction() {
		filterQueryCriteria();
		DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
				.findComponent("codeForm:selectedIndusTable");
		dataTable.setFirst(0);
	}

	private void filterQueryCriteria() {
		//初始化展示页面的数据集
		this.displayList = new ArrayList<>();
		if (org.apache.commons.collections.CollectionUtils.isEmpty(this.allList)) {
			return;
		}
		//选择的大类ID
		List<String> selectTopCodeList =
				StringUtils.string2list(StringUtils.objectToString(this.selectIds), ",");
		for (TsSimpleCode t : this.allList) {
			String codeLevelNo = StringUtils.objectToString(t.getCodeLevelNo());
			String codeName = StringUtils.objectToString(t.getCodeName());
			String codePym = StringUtils.objectToString(t.getSplsht());
			String codeNo = StringUtils.objectToString(t.getCodeNo());
			//过滤大类
			if (whetherItIsSelectTopCode(selectTopCodeList, codeLevelNo)) {
				continue;
			}
			//过滤名称/拼音
			boolean notMatchNameOrPy = StringUtils.isNotBlank(this.searchNamOrPy)
					&& !codeName.contains(this.searchNamOrPy)
					&& !codePym.toUpperCase().contains(this.searchNamOrPy.toUpperCase());
			if (notMatchNameOrPy) {
				continue;
			}
			//过滤编码
			boolean needFilterCodeNo = this.showSelectCodeNo && ObjectUtil.isNotEmpty(this.searchCodeNo);
			if (needFilterCodeNo && !codeNo.contains(this.searchCodeNo)) {
				continue;
			}
			this.displayList.add(t);
		}
	}

	/**
	 * 是否为已选码表大类（是或没有选择码表大类返回false，不是返回true）
	 *
	 * @param selectTopCodeList 已选码表大类
	 * @param codeLevelNo codeLevelNo
	 * @return boolean
	 */
	private boolean whetherItIsSelectTopCode(List<String> selectTopCodeList, String codeLevelNo) {
		if (org.apache.commons.collections.CollectionUtils.isEmpty(selectTopCodeList)) {
			return false;
		}
		for (String rid : selectTopCodeList) {
			if (this.firstMap.containsKey(rid) && codeLevelNo.startsWith(this.firstMap.get(rid))) {
				return false;
			}
		}
		return true;
	}

	/**
	 * <p>方法描述：提交</p>
	 * @MethodAuthor qrr,2019年12月2日,submitAction
	 * */
	public void submitAction() {
		if (null!=allList && allList.size()>0) {
			List<TsSimpleCode> results = new ArrayList<>();
			int count=0;
			for (TsSimpleCode t : allList) {
				if (t.isIfSelected()) {
					if ("1".equals(selectLast)) {
						if(ObjectUtil.isEmpty(t.getCodeLevelNo() )){
							results.add(t);
						}else {
							List<TsSimpleCode> childs = this.levelMap.get(t.getCodeLevelNo());
							if (CollectionUtils.isEmpty(childs)) {//最末级，无子级
								results.add(t);
							}
						}
					}else if("1".equals(this.selectFirst)){
						//若选择了大类只存储大类，若没有选择大类则直接存储
						if(selectBroadHeading(t)){
							results.add(t);
						}
					}else {
						results.add(t);
					}
					if("1".equals(t.getExtendS5())){
						count++;
					}
				}
			}
			if (null==results ||results.size()==0) {
				JsfUtil.addErrorMessage("请选择数据！");
				return;
			}else {
				if (null != selectNum && results.size() > selectNum.intValue()) {
					JsfUtil.addErrorMessage("最多只能选择"+selectNum+"条数据！");
					return;
				}
				if (results.size()>1000) {
					JsfUtil.addErrorMessage("最多只能选择1000条数据！");
					return;
				}
				if("1".equals(ifMainDust)&&count>1){
					JsfUtil.addErrorMessage("只能选择一种主要粉尘！");
					return;
				}
			}
			//有效期必填验证
			if("1".equals(this.ifShowExpiryDate)){
				boolean flag=false;
				if(validBDate==null){
					JsfUtil.addErrorMessage("账号有效期开始时间不能为空！");
					flag=true;
				}
				if(validEDate==null){
					JsfUtil.addErrorMessage("账号有效期结束时间不能为空！");
					flag=true;
				}
				if(flag){
					return;
				}
			}
			Map<String, Object> map = new HashMap<>();
			map.put("selectPros", results);
			if("1".equals(this.ifShowExpiryDate)){
				map.put("validBDate", validBDate);
				map.put("validEDate", validEDate);
			}
			RequestContext.getCurrentInstance().closeDialog(map);
		}else{
			JsfUtil.addErrorMessage("请选择数据！");
			return;
		}
	}

	/**
	 *  <p>方法描述：判断当前是否选择大类；没有返回true</p>
	 * @MethodAuthor hsj 2024-07-03 11:41
	 */
	private boolean selectBroadHeading(TsSimpleCode t) {
		if(CollectionUtils.isEmpty(allList)){
			return false;
		}
		String codeNo = t.getCodeNo();
		String codeLevelNo = t.getCodeLevelNo();
		if(StringUtils.isBlank(codeLevelNo)){
			return true;
		}
		if(codeLevelNo.equals(codeNo)){
			return true;
		}
		List<String> codeLevelNoList = new ArrayList<>();
		for(TsSimpleCode code : this.allList){
			if (!code.isIfSelected()) {
				continue;
			}
			codeLevelNoList.add(code.getCodeLevelNo());
		}
		List<String> codeList =StringUtils.string2list(codeLevelNo,"\\.");
		String str = "";
		for(int i = 0;i<codeList.size()-1;i++){
			str += codeList.get(i);
			if(codeLevelNoList.contains(str)){
				return false;
			}
			str+=".";
		}
		return true;
	}

	/**
	 *  <p>方法描述：全部选择---目前默认所有的可勾选若有其他情况需修改</p>
	 * @MethodAuthor hsj 2023-12-02 15:26
	 */
	public void showAllSelAction(){
		if(CollectionUtils.isEmpty(this.displayList)){
			return;
		}
		for(TsSimpleCode simpleCode:this.displayList){
			if(!simpleCode.isIfSelected()){
				simpleCode.setIfSelected(true);
			}
		}

	}
	/**
	 * <p>方法描述：关闭</p>
	 * @MethodAuthor qrr,2018年4月9日,dialogClose
	 */
	public void dialogClose() {
		RequestContext.getCurrentInstance().closeDialog(null);
	}


	public String getSearchNamOrPy() {
		return searchNamOrPy;
	}

	public void setSearchNamOrPy(String searchNamOrPy) {
		this.searchNamOrPy = searchNamOrPy;
	}

	public Boolean getShowSelectCodeNo() {
		return showSelectCodeNo;
	}

	public void setShowSelectCodeNo(Boolean showSelectCodeNo) {
		this.showSelectCodeNo = showSelectCodeNo;
	}

	public String getSearchCodeNo() {
		return searchCodeNo;
	}

	public void setSearchCodeNo(String searchCodeNo) {
		this.searchCodeNo = searchCodeNo;
	}

	public String getTitleName() {
		return titleName;
	}

	public void setTitleName(String titleName) {
		this.titleName = titleName;
	}

	public List<TsSimpleCode> getDisplayList() {
		return displayList;
	}

	public void setDisplayList(List<TsSimpleCode> displayList) {
		this.displayList = displayList;
	}


	public List<TsSimpleCode> getFirstList() {
		return firstList;
	}

	public void setFirstList(List<TsSimpleCode> firstList) {
		this.firstList = firstList;
	}

	public Boolean getIfShowFirstCode() {
		return ifShowFirstCode;
	}

	public void setIfShowFirstCode(Boolean ifShowFirstCode) {
		this.ifShowFirstCode = ifShowFirstCode;
	}
	public String getSelectLast() {
		return selectLast;
	}
	public void setSelectLast(String selectLast) {
		this.selectLast = selectLast;
	}
	public Integer getSelectNum() {
		return selectNum;
	}
	public void setSelectNum(Integer selectNum) {
		this.selectNum = selectNum;
	}
	public boolean isSelectSameLevel() {
		return selectSameLevel;
	}
	public void setSelectSameLevel(boolean selectSameLevel) {
		this.selectSameLevel = selectSameLevel;
	}

	public String getSelLast() {
		return selLast;
	}

	public void setSelLast(String selLast) {
		this.selLast = selLast;
	}

	public String getIfMainDust() {
		return ifMainDust;
	}

	public void setIfMainDust(String ifMainDust) {
		this.ifMainDust = ifMainDust;
	}

	public List<TsSimpleCode> getAllList() {
		return allList;
	}

	public void setAllList(List<TsSimpleCode> allList) {
		this.allList = allList;
	}


	public String getSelectIds() {
		return selectIds;
	}

	public void setSelectIds(String selectIds) {
		this.selectIds = selectIds;
	}

	public String getSearchName() {
		return searchName;
	}

	public void setSearchName(String searchName) {
		this.searchName = searchName;
	}

	public String getColName() {
		return colName;
	}

	public void setColName(String colName) {
		this.colName = colName;
	}

	public String getColumnName() {
		return columnName;
	}

	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}

	public String getIfEncrypt() {
		return ifEncrypt;
	}

	public void setIfEncrypt(String ifEncrypt) {
		this.ifEncrypt = ifEncrypt;
	}

	public String getExtends6() {
		return extends6;
	}

	public void setExtends6(String extends6) {
		this.extends6 = extends6;
	}

	public Boolean getShowAllSel() {
		return showAllSel;
	}

	public void setShowAllSel(Boolean showAllSel) {
		this.showAllSel = showAllSel;
	}

	public String getCodeDescContains() {
		return codeDescContains;
	}

	public void setCodeDescContains(String codeDescContains) {
		this.codeDescContains = codeDescContains;
	}
	public String getIfShowExpiryDate() {
		return ifShowExpiryDate;
	}
	public void setIfShowExpiryDate(String ifShowExpiryDate) {
		this.ifShowExpiryDate = ifShowExpiryDate;
	}
	public Date getValidBDate() {
		return validBDate;
	}
	public void setValidBDate(Date validBDate) {
		this.validBDate = validBDate;
	}
	public Date getValidEDate() {
		return validEDate;
	}
	public void setValidEDate(Date validEDate) {
		this.validEDate = validEDate;
	}
}
