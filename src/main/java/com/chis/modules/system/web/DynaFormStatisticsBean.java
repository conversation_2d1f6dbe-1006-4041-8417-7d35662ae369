package com.chis.modules.system.web;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.chis.activiti.utils.GroovyScriptEngine;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TdFormStatisticsDef;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.logic.TopTitlePo;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.DyncFormServiceImpl;
import com.chis.modules.system.utils.SqlHandleUtil;
import com.google.common.collect.Maps;

@ManagedBean(name = "dynaFormStatisticsBean")
@ViewScoped
public class DynaFormStatisticsBean extends FacesEditBean{
	private DyncFormServiceImpl formService = (DyncFormServiceImpl) SpringContextHolder
			.getBean(DyncFormServiceImpl.class);
	private DyncFormServiceImpl dyncFormServiceImpl = (DyncFormServiceImpl) SpringContextHolder
			.getBean(DyncFormServiceImpl.class);
	private CommServiceImpl commService = SpringContextHolder
			.getBean(CommServiceImpl.class);
	private SessionData sessionData = SpringContextHolder
			.getBean(SessionData.class);

	//动态表单编号
	private String formCode;
	//动态表单统计对象
	private TdFormStatisticsDef statisticsDef;
	//表格头
	private List<String> titleList;
	//主键
	private Integer rid;
	//查询条件传值
	private String searchJson;
	//查询列表数据
	private String dataListJson;
	//查询字段
	private String searchHtml;
	/**导出文件*/
	private StreamedContent downloadFile;
	/**导出使用表头集合*/
	private String tableTopTitleJson;
	/**查询数据，为导出缓存*/
	private List<Object[]> datas;
	/**1.单表维护 2.主子表维护*/
	private int formType = 1;
	private String html;
	
	public DynaFormStatisticsBean(){
		this.ifSQL = true;
		HttpServletRequest request = JsfUtil.getRequest();
		Object obj = request.getParameter("formCode");
		if(obj==null){
			JsfUtil.addErrorMessage("请先配置动态表单统计目录！");
			return;
		}
		formCode = request.getParameter("formCode").toString();
		statisticsDef = dyncFormServiceImpl.findTdFormStatisticsDefByFormCode(formCode);
		if(statisticsDef==null){
			JsfUtil.addErrorMessage("请先配置动态表单统计数据！");
			return;
		}
		this.searchHtml = statisticsDef.getStatisticsHtml();
//		searchAction();
	}
	
	/**
	 * 查询操作，如果不满足，可重写
	 */ 
	public void searchAction() {
		try{
			String sql = statisticsDef.getStatisticsSql();
			if(!StringUtils.isNotBlank(searchJson))
				searchJson = "{}";
			System.err.println("【dataJson】：" + searchJson);
			this.paramMap = JSON.parseObject(this.searchJson, Map.class);
			//处理sql和参数
//			paramMap.put(, sessionData.getUser().getTsOffice().getRid());
//			paramMap.put(, sessionData.getUser().getTsUnit().getRid());
		sessionData.getUser().getTsUnit();
			sql = SqlHandleUtil.buildSql(paramMap, sql);
			datas = commService.findDataBySqlNoPage(sql, paramMap);
			GroovyScriptEngine scriptEngine = SpringContextHolder
					.getBean(GroovyScriptEngine.class);
			Map<String, Object> vars = Maps.newHashMap();
			vars.put("dataList", datas);
			scriptEngine.execute(statisticsDef.getDataShell(), vars);
			this.dataListJson = JSON.toJSONStringWithDateFormat(datas, "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
		}catch(Exception e){
			JsfUtil.addErrorMessage("SQL维护异常！");
			e.printStackTrace();
		}
    }
	
	/**
	 * 导出方法
	 * @return 
	 */
	public DefaultStreamedContent downloadData(){
		if(!StringUtils.isNotBlank(tableTopTitleJson)){
			JsfUtil.addErrorMessage("未配置表头数据！");
        	return null;
		}
		TopTitlePo tableTopTitle = JSON.parseObject(this.tableTopTitleJson, TopTitlePo.class);
		if(tableTopTitle==null){
			JsfUtil.addErrorMessage("表头数据配置有误！");
        	return null;
		}
		List<TopTitlePo> topTitles = tableTopTitle.getTopTitles();
        if(topTitles==null || topTitles.size()==0){
        	JsfUtil.addErrorMessage("表头数据配置有误！");
        	return null;
        }
        
		ByteArrayOutputStream baos = null;
		try {
			XSSFWorkbook swBook = new XSSFWorkbook();
			XSSFCellStyle cellStyle2 = swBook.createCellStyle();
	        cellStyle2.setAlignment(XSSFCellStyle.ALIGN_LEFT);// 左右居中
	        cellStyle2.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);// 上下居中
	        cellStyle2.setBorderBottom((short) 1);// 下边框
	        cellStyle2.setBorderLeft((short) 1);// 左边框
	        cellStyle2.setBorderRight((short) 1);// 右边框
	        cellStyle2.setBorderTop((short) 1);// 上边框
	        cellStyle2.setWrapText(true);
	        
	        XSSFSheet sheet = swBook.createSheet();
	        XSSFRow row = sheet.createRow(0);
	        for(int i=0;i<topTitles.size();i++){
	        	XSSFCell cel= row.createCell(i);
	        	cel.setCellStyle(cellStyle2);
	        	String titleName = topTitles.get(i).getTitleName();
	        	cel.setCellValue(titleName==null?"-":titleName);
	        }
	        
			if(datas!=null && datas.size()>0){
				for(int i=0;i<datas.size();i++){
					XSSFRow dataRow = sheet.createRow(i+1);
					Object[] itemArr = datas.get(i);
					for(int j=0;j<topTitles.size();j++){
						//创建列并设定样式
						sheet.setColumnWidth(j, 6000);
						XSSFCell cel= dataRow.createCell(j);
						cel.setCellStyle(cellStyle2);
						Integer index = topTitles.get(j).getDataIndex();
						if(index==null)
							continue;
						cel.setCellValue(itemArr[index]==null?"-":itemArr[index].toString());
					}
				}
			}
			String tempFileName = statisticsDef.getFormName()+".xlsx";
			String fileName = new String(tempFileName.getBytes("GBK"),
					"ISO-8859-1");
			baos = new ByteArrayOutputStream();
			swBook.write(baos);
			baos.flush();
			byte[] aa = baos.toByteArray();
			return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName); 
		} catch (IOException e) {
			JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
		} finally{
			if (baos != null) {
				try {
					baos.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		return null;
//		FacesContext.getCurrentInstance().responseComplete();
	}

	@Override
	public String[] buildHqls() {
		return null;
	}
	
	
	@Override
	public void addInit() {
		
	}

	@Override
	public void viewInit() {
		
	}

	@Override
	public void modInit() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void saveAction() {
		// TODO Auto-generated method stub
		
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}
	public List<String> getTitleList() {
		return titleList;
	}

	public void setTitleList(List<String> titleList) {
		this.titleList = titleList;
	}

	public String getSearchJson() {
		return searchJson;
	}

	public void setSearchJson(String searchJson) {
		this.searchJson = searchJson;
	}

	public TdFormStatisticsDef getStatisticsDef() {
		return statisticsDef;
	}

	public void setStatisticsDef(TdFormStatisticsDef statisticsDef) {
		this.statisticsDef = statisticsDef;
	}

	public int getFormType() {
		return formType;
	}

	public void setFormType(int formType) {
		this.formType = formType;
	}

	public String getHtml() {
		return html;
	}

	public void setHtml(String html) {
		this.html = html;
	}

	public String getDataListJson() {
		return dataListJson;
	}

	public void setDataListJson(String dataListJson) {
		this.dataListJson = dataListJson;
	}

	public String getSearchHtml() {
		return searchHtml;
	}

	public void setSearchHtml(String searchHtml) {
		this.searchHtml = searchHtml;
	}

	public StreamedContent getDownloadFile() {
		return downloadData();
	}

	public void setDownloadFile(StreamedContent downloadFile) {
		this.downloadFile = downloadFile;
	}

	public String getTableTopTitleJson() {
		return tableTopTitleJson;
	}

	public void setTableTopTitleJson(String tableTopTitleJson) {
		this.tableTopTitleJson = tableTopTitleJson;
	}



	
	
}
