package com.chis.modules.system.web;

import com.chis.common.utils.*;
import com.chis.modules.system.entity.*;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.service.SystemServiceImpl;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.io.IOException;
import java.io.Serializable;
import java.util.*;

/**
 * 
 * <p>
 * 类描述：武汉注册用户界面
 * </p>
 * 
 * @ClassAuthor rcj,2020年7月30日,RegisterWhBean
 */
@ManagedBean(name = "registerWhBean")
@ViewScoped
public class RegisterWhBean implements Serializable {

	private static final long serialVersionUID = -3740326227684495169L;
	//隐藏密码
	private String password;
	private String password1;
	private SystemServiceImpl systemService = (SystemServiceImpl) SpringContextHolder
			.getBean(SystemServiceImpl.class);
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder
			.getBean(CommServiceImpl.class);
	private TsUserInfo user;
	private String value;
	private String applyCccountId;
	private List<TsZone> zoneList;
	private String zoneCode;
	private String zoneName;
	private TdSysApplyAccount tdSysApplyAccount;
	//单位属性
	private String[] unitSorts;
	private List<TbSysApplyAccount> sortList;

	//单位配置材料
	private List<TbSysApplyAnnex> annexList;
	//材料rid
	private TdSysApplyAnnex annexEntity;

	//地区必填为空
	private boolean ifZoneEmtpy=false;
	//单位名称必填为空
	private boolean ifUnitEmtpy=false;
	//单位名称重复
	private boolean ifUnitRepeat=false;
	//单位联系电话为空
	private boolean ifPhomeEmtpy=false;
	//社会信用代码必填为空
	private boolean ifCodeEmtpy=false;
	//单位属性必填为空
	private boolean ifSortEmtpy=false;
	//申请资料必填为空
	private boolean ifApplyEmtpy=false;
	//用户姓名为空
	private boolean ifUserNameEmtpy=false;
	//身份证号必填为空
	private boolean ifIdcEmtpy=false;
	//手机号码必填为空
	private boolean ifMobileEmtpy=false;
	//账户名称必填为空
	private boolean ifUserNoEmtpy=false;
	//登录密码必填为空
	private boolean ifPassEmtpy=false;
	//确认密码必填为空
	private boolean ifPassComfiremEmtpy=false;

	//地区必填为空
	private boolean ifZoneError=false;
	//单位联系电话格式不正确
	private boolean ifPhomeError=false;
	//社会信用代码格式不正确
	private boolean ifCodeError=false;
	//手机号码格式错误
	private boolean ifMobileError=false;
	//身份证号码格式错误
	private boolean ifIdcError=false;
	//登录密码必填为空
	private boolean ifPassError=false;
	//确认密码必填为空
	private boolean ifPassComfiremError=false;

	//社会信用代码已存在
	private boolean ifCodeExsit=false;
	//社会信用代码已申请
	private boolean ifCodeApply=false;
	//账户名称已存在
	private boolean ifUserNoExsit=false;
	//身份证已存在
	private boolean ifIdcExsit=false;

	//主体机构不存在
	private boolean ifSubOrgEmpty=false;


	public RegisterWhBean() {
		applyCccountId = JsfUtil.getRequestParameter("applyCccountId");
		user = new TsUserInfo();
		value = commService.findParamValue("STRONG_PASSWORD");
		initZone();
		initSortList();
		if(StringUtils.isBlank(applyCccountId)){
			tdSysApplyAccount = new TdSysApplyAccount();
			tdSysApplyAccount.setApplyAnnexList(new ArrayList<TdSysApplyAnnex>());
			tdSysApplyAccount.setApplySortList(new ArrayList<TdSysApplySort>());
			tdSysApplyAccount.setIfSubOrg(0);
			tdSysApplyAccount.setCreateDate(new Date());
			tdSysApplyAccount.setCreateManid(0);
		}else{
			modifyAction();
		}
	}

	/**
	 * <p>方法描述：修改</p>
	 * @MethodAuthor rcj,2020年8月13日,modifyAction
	 * */
	public void modifyAction(){
		tdSysApplyAccount = systemModuleService.findTdSysApplyAccountByRid(Integer.valueOf(applyCccountId));
		zoneCode = tdSysApplyAccount.getFkByZoneId().getZoneGb();
		zoneName = tdSysApplyAccount.getFkByZoneId().getZoneName();
		if(!CollectionUtils.isEmpty(tdSysApplyAccount.getApplySortList())){
			unitSorts = new String[tdSysApplyAccount.getApplySortList().size()];
			for (int i = 0; i <tdSysApplyAccount.getApplySortList().size(); i++) {
				unitSorts[i] = tdSysApplyAccount.getApplySortList().get(i).getFkBySortId().getRid().toString();
			}
		}
	}


	/**
	 * <p>方法描述：初始化地区</p>
	 * @MethodAuthor rcj,2020年7月31日,initZone
	 * */
	public void initSortList(){
		StringBuffer hql = new StringBuffer();
		hql.append(" select t from TbSysApplyAccount t  order by t.num ");
		sortList = commService.findByHql(hql.toString(), TbSysApplyAccount.class);
		if(null == sortList){
			sortList = new ArrayList<>();
		}

		StringBuffer hql2 = new StringBuffer();
		hql2.append(" select t from TbSysApplyAnnex t  order by t.num ");
		annexList = commService.findByHql(hql2.toString(), TbSysApplyAnnex.class);
		if(null == annexList){
			annexList = new ArrayList<>();
		}

	}

	/**
	 * <p>方法描述：初始化地区</p>
	 * @MethodAuthor rcj,2020年7月31日,initZone
	 * */
	private void initZone() {
		// 地区初始化
		String mainBusZoneGb = PropertyUtils.getValueWithoutException("mainBusZoneGb");
		if (StringUtils.isNotBlank(mainBusZoneGb)) {
			this.zoneList = this.systemModuleService.findZoneListICanSee(false, mainBusZoneGb);
			this.zoneCode = this.zoneList.get(0).getZoneGb();
			this.zoneName = this.zoneList.get(0).getZoneName();
		}

	}


	/**
	 * <p>
	 * 方法描述：选择单位属性，增加申请资料
	 * </p>
	 *
	 * @MethodAuthor rcj,2020年8月6日,changeSortAction
	 */
	public void changeSortAction(){
		if(unitSorts == null || unitSorts.length == 0){//未选择单位属性
			tdSysApplyAccount.setApplyAnnexList(new ArrayList<TdSysApplyAnnex>());
		}else{
			String s = StringUtils.array2string(unitSorts, ",");
			s = ","+s+",";
			if(!CollectionUtils.isEmpty(tdSysApplyAccount.getApplyAnnexList())){
				List<String> strings = Arrays.asList(unitSorts);
				List<TdSysApplyAnnex>  list = new ArrayList<>();
				Map<String,TdSysApplyAnnex>  map = new HashMap<>();
				for (String t:strings ) {
					//找到匹配的材料
					for (TdSysApplyAnnex annex:tdSysApplyAccount.getApplyAnnexList()) {
						if(annex.getFkByAnnexId().getFkByMainId().getRid().toString().equals(t)){
							list.add(annex);
							map.put(t,annex);
						}
					}
				}
				//增加未能匹配的选择的材料
				for (String t:strings ) {
					if(map.get(t) == null){
						if(!CollectionUtils.isEmpty(annexList)){
							for (TbSysApplyAnnex annex: annexList) {
								if(t.equals(annex.getFkByMainId().getRid().toString())){
									TdSysApplyAnnex ann = new TdSysApplyAnnex();
									ann.setFkByMainId(tdSysApplyAccount);
									ann.setFkByAnnexId(annex);
									ann.setCreateDate(new Date());
									ann.setCreateManid(0);
									list.add(ann);
								}
							}
						}
					}
				}
				tdSysApplyAccount.setApplyAnnexList(list);
			}else{
				//新增勾选
				if(!CollectionUtils.isEmpty(annexList)){
					for (TbSysApplyAnnex annex: annexList) {
						if(s.contains(","+annex.getFkByMainId().getRid()+",")){
							TdSysApplyAnnex ann = new TdSysApplyAnnex();
							ann.setFkByMainId(tdSysApplyAccount);
							ann.setFkByAnnexId(annex);
							ann.setCreateDate(new Date());
							ann.setCreateManid(0);
							tdSysApplyAccount.getApplyAnnexList().add(ann);
						}
					}
				}
			}
		}
	}
	
	/**
	 * <p>
	 * 方法描述：验证用户名唯一性，验证身份证号格式正确并且唯一
	 * </p>
	 * 
	 * @MethodAuthor rcj,2020年8月6日,reigsterAction
	 */
	public void reigsterAction() {

		if(StringUtils.isBlank(zoneCode)){
			ifZoneEmtpy = true;
			ifZoneError = false;
		}else{
			ifZoneEmtpy = false;
			int zoneType = ZoneUtil.getZoneType(zoneCode);
			if(zoneType <4){
				ifZoneError = true;
			}else{
				ifZoneError = false;
			}
		}

		if(StringUtils.isBlank(tdSysApplyAccount.getUnitname())){
			ifUnitEmtpy = true;
		}else{
			ifUnitEmtpy = false;
		}

		veryUnitName();

		if(StringUtils.isBlank(tdSysApplyAccount.getCreditCode())){
			ifCodeEmtpy = true;
			ifCodeError=false;
			ifCodeExsit=false;
			ifCodeApply=false;
		}else{
			veryCreditCode();
		}

		veryIfSubOrg();

		if(StringUtils.isBlank(tdSysApplyAccount.getUnitTel())){
			ifPhomeEmtpy = true;
			ifPhomeError = false;
		}else{
			ifPhomeEmtpy = false;
			if(!StringUtils.vertyPhone(tdSysApplyAccount.getUnitTel())){
				ifPhomeError= true;
			}else{
				ifPhomeError= false;
			}
		}

		if(unitSorts == null || unitSorts.length == 0){
			ifSortEmtpy = true;
		}else{
			ifSortEmtpy = false;
		}

		if(CollectionUtils.isEmpty(tdSysApplyAccount.getApplyAnnexList())){
			ifApplyEmtpy = true;
		}else{
			//是否有资料未上传
			boolean flag =false;
			for (TdSysApplyAnnex ann:tdSysApplyAccount.getApplyAnnexList()) {
				if(StringUtils.isBlank(ann.getAnnexName())){
					flag = true;
				}
			}
			if(flag){
				ifApplyEmtpy = true;
			}else{
				ifApplyEmtpy = false;
			}
		}

		if(StringUtils.isBlank(tdSysApplyAccount.getUsername())){
			ifUserNameEmtpy = true;
		}else{
			ifUserNameEmtpy = false;
		}

		if(StringUtils.isBlank(tdSysApplyAccount.getMobileNum())){
			ifMobileEmtpy = true;
			ifMobileError= false;
		}else{
			ifMobileEmtpy = false;
			if(!StringUtils.vertyMobilePhone(tdSysApplyAccount.getMobileNum())){
				ifMobileError= true;
			}else{
				ifMobileError= false;
			}
		}

		if(StringUtils.isBlank(tdSysApplyAccount.getIdc())){
			ifIdcEmtpy = true;
			ifIdcError = false;
			ifIdcExsit = false;
		}else{
			ifIdcEmtpy = false;
			ifIdcExsit = false;
			if(StringUtils.isBlank(IdcUtils.checkIDC(tdSysApplyAccount.getIdc()))){
				ifIdcError = false;
				//是否身份证已存在
				StringBuffer hql = new StringBuffer();
				Map<String, Object> paramMap = new HashMap<>();
				hql.append(" select t from  TdSysApplyAccount t where t.idc =:idc");
				paramMap.put("idc", tdSysApplyAccount.getIdc());
				if(tdSysApplyAccount.getRid() != null){
					hql.append(" and t.rid != ").append(tdSysApplyAccount.getRid());
				}
				List<TdSysApplyAccount> list = commService.findDataByHqlNoPage(hql.toString(), paramMap);

				StringBuffer hql2 = new StringBuffer();
				paramMap = new HashMap<>();
				hql2.append(" select t from  TsUserInfo t where t.idc =:idc");
				paramMap.put("idc", tdSysApplyAccount.getIdc());
				List<TsUserInfo> list2 = commService.findDataByHqlNoPage(hql2.toString(), paramMap);
				if(!CollectionUtils.isEmpty(list2) || !CollectionUtils.isEmpty(list)){
					ifIdcExsit = true;
				}else{
					ifIdcExsit = false;
				}
			}else{
				ifIdcError = true;
			}
		}

		if(StringUtils.isBlank(tdSysApplyAccount.getUserNo())){
			ifUserNoEmtpy = true;
			ifUserNoExsit = false;
		}else{
			ifUserNoEmtpy = false;
			//是否账户已存在
			StringBuffer hql = new StringBuffer();
			Map<String, Object> paramMap = new HashMap<>();
			hql.append(" select t from  TdSysApplyAccount t where t.userNo =:userNo");
			paramMap.put("userNo", tdSysApplyAccount.getUserNo());
			if(tdSysApplyAccount.getRid() != null){
				hql.append(" and t.rid != ").append(tdSysApplyAccount.getRid());
			}
			List<TdSysApplyAccount> list = commService.findDataByHqlNoPage(hql.toString(), paramMap);

			StringBuffer hql2 = new StringBuffer();
			paramMap = new HashMap<>();
			hql2.append(" select t from  TsUserInfo t where t.userNo =:userNo");
			paramMap.put("userNo", tdSysApplyAccount.getUserNo());
			List<TsUserInfo> list2 = commService.findDataByHqlNoPage(hql2.toString(), paramMap);
			if(!CollectionUtils.isEmpty(list2) || !CollectionUtils.isEmpty(list)){
				ifUserNoExsit = true;
			}else{
				ifUserNoExsit = false;
			}
		}

		if(StringUtils.isBlank(password)){
			ifPassEmtpy = true;
			ifPassError = false;
		}else{
			ifPassEmtpy = false;
			if(this.password.length()<8 || (value.equals("1") && (StringUtils.isNotBlank(password) && !StringUtils.validPassword(this.password)))){
				ifPassError = true;
			}else{
				ifPassError = false;
			}
		}

		if(StringUtils.isBlank(password1)){
			ifPassComfiremEmtpy = true;
			ifPassComfiremError = false;
		}else{
			ifPassComfiremEmtpy = false;
			if(!password1.equals(password)){
				ifPassComfiremError = true;
				password1 = null;
			}else{
				ifPassComfiremError = false;
			}
		}
		try {
			if(!ifPassComfiremError && !ifPassError && !ifIdcError && !ifMobileError && !ifCodeError && !ifPhomeError && !ifPassComfiremEmtpy
			  && !ifPassEmtpy && !ifUserNoEmtpy && !ifMobileEmtpy && !ifIdcEmtpy && !ifUserNameEmtpy && !ifApplyEmtpy && !ifSortEmtpy && !ifCodeEmtpy && !ifPhomeEmtpy
			  && !ifUnitEmtpy && !ifZoneEmtpy && !ifCodeExsit && !ifCodeApply && !ifUserNoExsit && !ifIdcExsit && !ifZoneError
			  && !ifSubOrgEmpty && !ifUnitRepeat){

				TsZone zone = systemModuleService.findTsZoneByGb(zoneCode);
				tdSysApplyAccount.setFkByZoneId(zone);
				List<String> strings = Arrays.asList(unitSorts);
				tdSysApplyAccount.setApplySortList(new ArrayList<TdSysApplySort>());
				for (String s:strings ) {
					TdSysApplySort entity = new TdSysApplySort();
					entity.setFkByMainId(tdSysApplyAccount);
					entity.setFkBySortId(new TbSysApplyAccount(Integer.valueOf(s)));
					entity.setCreateDate(new Date());
					entity.setCreateManid(0);
					tdSysApplyAccount.getApplySortList().add(entity);
				}
				tdSysApplyAccount.setPassword(new MD5Util().getMD5ofStr(password));
				systemModuleService.saveOrUpdateApplyAccount(tdSysApplyAccount);
				redirectSuccess();
			}
		} catch (Exception e) {
			JsfUtil.addErrorMessage("注册失败！");
			e.printStackTrace();
		}
	}
	/*public void changeUnitName(){
		veryUnitName();
		veryCreditCode();
	}*/

	public void changeUnitCode(){
		veryUnitName();
		veryCreditCode();
		veryIfSubOrg();
	}

	/*public void changeIfSubOrg(){
		veryCreditCode();
		veryIfSubOrg();
	}*/



	/**
	 * @MethodName: veryUnitName
	 * @Description: 同时验证单位名称
	 * @Param: []
	 * @Return: void
	 * @Author: maox
	 * @Date: 2020-09-23
	**/
	private void veryUnitName(){
		if(tdSysApplyAccount.getIfSubOrg()==1 && StringUtils.isNotBlank(tdSysApplyAccount.getUnitname()) && StringUtils.isNotBlank(tdSysApplyAccount.getCreditCode())){
			StringBuffer hql = new StringBuffer();
			Map<String, Object> paramMap = new HashMap<>();

			hql.append(" select t from  TsUserInfo t where t.tsUnit.creditCode =:creditCode");
			hql.append(" and t.tsUnit.unitname=:unitname");
			//hql.append(" and t.tsUnit.ifSubOrg =1");
			paramMap.put("creditCode",tdSysApplyAccount.getCreditCode());
			paramMap.put("unitname",tdSysApplyAccount.getUnitname());
			List<TsUserInfo> list = commService.findDataByHqlNoPage(hql.toString(),paramMap);

			hql = new StringBuffer();
			paramMap = new HashMap<>();
			hql.append(" select t from  TdSysApplyAccount t where t.creditCode =:creditCode");
			hql.append(" and t.unitname=:unitname");
			//hql.append("  and t.ifSubOrg =1");
			paramMap.put("creditCode",tdSysApplyAccount.getCreditCode());
			paramMap.put("unitname",tdSysApplyAccount.getUnitname());
			if(tdSysApplyAccount.getRid() != null){
				TdSysApplyAccount account = commService.find(TdSysApplyAccount.class,tdSysApplyAccount.getRid());
				if(null != account && account.getIfSubOrg()==tdSysApplyAccount.getIfSubOrg()){
					hql.append(" and t.rid != ").append(tdSysApplyAccount.getRid());
				}
			}
			List<TdSysApplyAccount> list2 = commService.findDataByHqlNoPage(hql.toString(),paramMap);

			if(CollectionUtils.isEmpty(list) && CollectionUtils.isEmpty(list2)){
				ifUnitRepeat =false;
			}else {
				ifUnitRepeat = true;
			}
		}else{
			ifUnitRepeat =false;
		}
	}

	private void veryCreditCode(){
		if(StringUtils.isBlank(tdSysApplyAccount.getCreditCode())){
			ifCodeEmtpy = false;
			ifCodeError=false;
			ifCodeExsit=false;
			ifCodeApply=false;
		}else{
			ifCodeEmtpy = false;
			if(!StringUtils.isCreditCode(tdSysApplyAccount.getCreditCode())){
				ifCodeError= true;
				ifCodeExsit=false;
				ifCodeApply=false;
			}else{
				ifCodeError= false;
				//系统中存在
				StringBuffer hql2 = new StringBuffer();
				Map<String, Object> paramMap = new HashMap<>();
				hql2.append(" select t from  TsUserInfo t where t.tsUnit.creditCode =:creditCode");

				if(tdSysApplyAccount.getIfSubOrg()==0){
					hql2.append(" and (t.tsUnit.ifSubOrg =").append(tdSysApplyAccount.getIfSubOrg()).append("or t.tsUnit.ifSubOrg is null)");
				}else{
					hql2.append(" and t.tsUnit.ifSubOrg =").append(tdSysApplyAccount.getIfSubOrg());
				}
				paramMap.put("creditCode",tdSysApplyAccount.getCreditCode());

				if(null != tdSysApplyAccount.getIfSubOrg() && tdSysApplyAccount.getIfSubOrg()==1 && StringUtils.isNotBlank(tdSysApplyAccount.getUnitname())){
					hql2.append(" AND t.tsUnit.unitname =:unitname");
					paramMap.put("unitname",tdSysApplyAccount.getUnitname());
				}

				List<TsUserInfo> list2 = commService.findDataByHqlNoPage(hql2.toString(),paramMap);
				if(!CollectionUtils.isEmpty(list2)){
						if(null != tdSysApplyAccount.getIfSubOrg() && tdSysApplyAccount.getIfSubOrg()==0){
							ifCodeExsit=true;
						}else{
							ifCodeExsit=false;
						}
						ifCodeApply=false;
				}else{
					ifCodeExsit=false;
					//申请中
						StringBuffer hql = new StringBuffer();
					   paramMap = new HashMap<>();
						hql.append(" select t from  TdSysApplyAccount t where t.stateMark !=1 and t.creditCode =:creditCode");
						paramMap.put("creditCode",tdSysApplyAccount.getCreditCode());
						if(tdSysApplyAccount.getIfSubOrg()==1 && StringUtils.isNotBlank(tdSysApplyAccount.getUnitname())){
							hql.append(" AND t.unitname =:unitname");
							paramMap.put("unitname",tdSysApplyAccount.getUnitname());
						}

						hql.append(" AND t.ifSubOrg =").append(tdSysApplyAccount.getIfSubOrg());
						if(tdSysApplyAccount.getRid() != null){
							hql.append(" and t.rid != ").append(tdSysApplyAccount.getRid());
						}
						List<TdSysApplyAccount> list = commService.findDataByHqlNoPage(hql.toString(),paramMap);
						if(!CollectionUtils.isEmpty(list)) {
							ifCodeApply=true;
						}else{
							ifCodeApply=false;
						}
					/*}else{
						ifCodeApply=false;
					}*/

				}

				}
			}
		}

	/**
	 * @MethodName: changeIfSubOrg
	 * @Description: 切换是否分支机构
	 * @Param: []
	 * @Return: void
	 * @Author: maox
	 * @Date: 2020-09-23
	**/
	private void veryIfSubOrg(){
		ifSubOrgEmpty = false;
		if(StringUtils.isNotBlank(tdSysApplyAccount.getCreditCode()) && null != tdSysApplyAccount.getIfSubOrg()){

			//主体机构应不存在
			Map<String, Object> paramMap = new HashMap<>();
			StringBuffer hql = new StringBuffer();
			hql.append(" select t from  TsUnit t where t.creditCode =:creditCode");
			paramMap.put("creditCode",tdSysApplyAccount.getCreditCode());
			List<TsUnit> list = commService.findDataByHqlNoPage(hql.toString(),paramMap);

			if(1==tdSysApplyAccount.getIfSubOrg()){
				if(CollectionUtils.isEmpty(list)){
					ifSubOrgEmpty =true;
				}else {
					ifSubOrgEmpty = false;
				}
			}
		}
	}

    public static boolean isLetterDigit(String str) {
        boolean isDigit = false;//定义一个boolean值，用来表示是否包含数字
        boolean isLetter = false;//定义一个boolean值，用来表示是否包含字母
        for (int i = 0; i < str.length(); i++) {
            if (Character.isDigit(str.charAt(i))) {   //用char包装类中的判断数字的方法判断每一个字符
                isDigit = true;
            } else if (Character.isLetter(str.charAt(i))) {  //用char包装类中的判断字母的方法判断每一个字符
                isLetter = true;
            }
        }
        String regex = "^(?=.*?[a-z])(?=.*?[A-Z])(?=.*?[0-9])(?=.*?[!~@#$%^&*()_+\\-=\\\\\\|/.><,:;\"'{}\\[\\]`])[a-zA-Z0-9!~@#$%^&*()_+\\-=\\\\\\|/.><,:;\"'{}\\[\\]`]{8,16}";
        boolean isRight = isDigit && isLetter && str.matches(regex);
        return isRight;
    }
	

	/**
	 * <p>方法描述：挑战注册成功页面</p>
 	 * 
 	 * @MethodAuthor rcj,2018年5月18日,redirectSuccess
	 * @throws IOException
	 */
	private void redirectSuccess() throws IOException {
		FacesContext.getCurrentInstance().getExternalContext()
				.redirect("/registerWhSuccess.faces");
	}

	
	
	/**
	 * <p>方法描述：注册成功界面跳转至登录页面</p>
 	 * 
 	 * @MethodAuthor rcj,2020年8月13日,backToLogin
	 */
	public void backToLogin() {
		try {
			FacesContext.getCurrentInstance().getExternalContext()
					.redirect("/login.faces");
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * <p>方法描述：上传文件</p>
	 *
	 * @MethodAuthor rcj,2020年7月31日,uploadAction
	 */
	/**<p>
	 * 方法描述：上传
	 * </p>
	 *
	 * @MethodAuthor rcj,2020年8月7日,fileUpload
	 * @param event
	 */
	public void fileUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			try {
				String errorMsg = FileUtils.veryFile(file.getInputstream(),file.getContentType(), file.getFileName(), "3");
				if (StringUtils.isNotBlank(errorMsg)) {
					JsfUtil.addErrorMessage(errorMsg);
					return;
				}
				String fileName = file.getFileName();// 文件名称
				String uuid = java.util.UUID.randomUUID().toString()
						.replaceAll("-", "");
				String path = JsfUtil.getAbsolutePath();
				String relativePath = new StringBuffer("law/")
						.append(uuid)
						.append(fileName.substring(fileName
								.lastIndexOf("."))).toString();
				// 文件路径
				String filePath = new StringBuilder(path).append(
						relativePath).toString();
				FileUtils.copyFile(filePath, file.getInputstream());
				int i = tdSysApplyAccount.getApplyAnnexList().indexOf(annexEntity);
				annexEntity.setAnnexName(fileName);
				annexEntity.setAnnexPath(relativePath);
				tdSysApplyAccount.getApplyAnnexList().remove(i);
				tdSysApplyAccount.getApplyAnnexList().add(i,annexEntity);
				RequestContext.getCurrentInstance().execute(
						"PF('FileDialog').hide()");
				RequestContext.getCurrentInstance().update(
						"personForm:dataTable");
				JsfUtil.addSuccessMessage("上传成功！");
			} catch (Exception e) {
				e.printStackTrace();
				JsfUtil.addErrorMessage("上传失败！");
			}
		}
	}

	/**
	 * <p>方法描述：删除材料</p>
	 *
	 * @MethodAuthor rcj,2020年7月31日,deleteAnnexAction
	 */
	public void  deleteAnnexAction(){
		int i = tdSysApplyAccount.getApplyAnnexList().indexOf(annexEntity);
		tdSysApplyAccount.getApplyAnnexList().remove(i);
		annexEntity.setAnnexPath(null);
		annexEntity.setAnnexName(null);
		tdSysApplyAccount.getApplyAnnexList().add(i,annexEntity);
	}


	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}


	public SystemServiceImpl getSystemService() {
		return systemService;
	}

	public void setSystemService(SystemServiceImpl systemService) {
		this.systemService = systemService;
	}

	public SessionData getSessionData() {
		return sessionData;
	}

	public void setSessionData(SessionData sessionData) {
		this.sessionData = sessionData;
	}

	public TsUserInfo getUser() {
		return user;
	}

	public void setUser(TsUserInfo user) {
		this.user = user;
	}

	public String getApplyCccountId() {
		return applyCccountId;
	}

	public void setApplyCccountId(String applyCccountId) {
		this.applyCccountId = applyCccountId;
	}

	public String getPassword1() {
		return password1;
	}

	public void setPassword1(String password1) {
		this.password1 = password1;
	}

	public String getZoneName() {
		return zoneName;
	}

	public void setZoneName(String zoneName) {
		this.zoneName = zoneName;
	}

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public String getZoneCode() {
		return zoneCode;
	}

	public void setZoneCode(String zoneCode) {
		this.zoneCode = zoneCode;
	}

	public TdSysApplyAccount getTdSysApplyAccount() {
		return tdSysApplyAccount;
	}

	public void setTdSysApplyAccount(TdSysApplyAccount tdSysApplyAccount) {
		this.tdSysApplyAccount = tdSysApplyAccount;
	}

	public String[] getUnitSorts() {
		return unitSorts;
	}

	public void setUnitSorts(String[] unitSorts) {
		this.unitSorts = unitSorts;
	}

	public TdSysApplyAnnex getAnnexEntity() {
		return annexEntity;
	}

	public void setAnnexEntity(TdSysApplyAnnex annexEntity) {
		this.annexEntity = annexEntity;
	}

	public boolean isIfZoneEmtpy() {
		return ifZoneEmtpy;
	}

	public void setIfZoneEmtpy(boolean ifZoneEmtpy) {
		this.ifZoneEmtpy = ifZoneEmtpy;
	}

	public boolean isIfUnitEmtpy() {
		return ifUnitEmtpy;
	}

	public void setIfUnitEmtpy(boolean ifUnitEmtpy) {
		this.ifUnitEmtpy = ifUnitEmtpy;
	}

	public boolean isIfPhomeEmtpy() {
		return ifPhomeEmtpy;
	}

	public void setIfPhomeEmtpy(boolean ifPhomeEmtpy) {
		this.ifPhomeEmtpy = ifPhomeEmtpy;
	}

	public boolean isIfCodeEmtpy() {
		return ifCodeEmtpy;
	}

	public void setIfCodeEmtpy(boolean ifCodeEmtpy) {
		this.ifCodeEmtpy = ifCodeEmtpy;
	}

	public boolean isIfSortEmtpy() {
		return ifSortEmtpy;
	}

	public void setIfSortEmtpy(boolean ifSortEmtpy) {
		this.ifSortEmtpy = ifSortEmtpy;
	}

	public boolean isIfApplyEmtpy() {
		return ifApplyEmtpy;
	}

	public void setIfApplyEmtpy(boolean ifApplyEmtpy) {
		this.ifApplyEmtpy = ifApplyEmtpy;
	}

	public boolean isIfUserNameEmtpy() {
		return ifUserNameEmtpy;
	}

	public void setIfUserNameEmtpy(boolean ifUserNameEmtpy) {
		this.ifUserNameEmtpy = ifUserNameEmtpy;
	}

	public boolean isIfIdcEmtpy() {
		return ifIdcEmtpy;
	}

	public void setIfIdcEmtpy(boolean ifIdcEmtpy) {
		this.ifIdcEmtpy = ifIdcEmtpy;
	}

	public boolean isIfMobileEmtpy() {
		return ifMobileEmtpy;
	}

	public void setIfMobileEmtpy(boolean ifMobileEmtpy) {
		this.ifMobileEmtpy = ifMobileEmtpy;
	}

	public boolean isIfUserNoEmtpy() {
		return ifUserNoEmtpy;
	}

	public void setIfUserNoEmtpy(boolean ifUserNoEmtpy) {
		this.ifUserNoEmtpy = ifUserNoEmtpy;
	}

	public boolean isIfPassEmtpy() {
		return ifPassEmtpy;
	}

	public void setIfPassEmtpy(boolean ifPassEmtpy) {
		this.ifPassEmtpy = ifPassEmtpy;
	}

	public boolean isIfPassComfiremEmtpy() {
		return ifPassComfiremEmtpy;
	}

	public void setIfPassComfiremEmtpy(boolean ifPassComfiremEmtpy) {
		this.ifPassComfiremEmtpy = ifPassComfiremEmtpy;
	}

	public boolean isIfPhomeError() {
		return ifPhomeError;
	}

	public void setIfPhomeError(boolean ifPhomeError) {
		this.ifPhomeError = ifPhomeError;
	}

	public boolean isIfCodeError() {
		return ifCodeError;
	}

	public void setIfCodeError(boolean ifCodeError) {
		this.ifCodeError = ifCodeError;
	}

	public boolean isIfMobileError() {
		return ifMobileError;
	}

	public void setIfMobileError(boolean ifMobileError) {
		this.ifMobileError = ifMobileError;
	}

	public boolean isIfIdcError() {
		return ifIdcError;
	}

	public void setIfIdcError(boolean ifIdcError) {
		this.ifIdcError = ifIdcError;
	}

	public boolean isIfPassError() {
		return ifPassError;
	}

	public void setIfPassError(boolean ifPassError) {
		this.ifPassError = ifPassError;
	}

	public boolean isIfPassComfiremError() {
		return ifPassComfiremError;
	}

	public void setIfPassComfiremError(boolean ifPassComfiremError) {
		this.ifPassComfiremError = ifPassComfiremError;
	}

	public List<TbSysApplyAccount> getSortList() {
		return sortList;
	}

	public void setSortList(List<TbSysApplyAccount> sortList) {
		this.sortList = sortList;
	}

	public List<TbSysApplyAnnex> getAnnexList() {
		return annexList;
	}

	public void setAnnexList(List<TbSysApplyAnnex> annexList) {
		this.annexList = annexList;
	}

	public boolean isIfZoneError() {
		return ifZoneError;
	}

	public void setIfZoneError(boolean ifZoneError) {
		this.ifZoneError = ifZoneError;
	}

	public boolean isIfCodeExsit() {
		return ifCodeExsit;
	}

	public void setIfCodeExsit(boolean ifCodeExsit) {
		this.ifCodeExsit = ifCodeExsit;
	}

	public boolean isIfCodeApply() {
		return ifCodeApply;
	}

	public void setIfCodeApply(boolean ifCodeApply) {
		this.ifCodeApply = ifCodeApply;
	}

	public boolean isIfUserNoExsit() {
		return ifUserNoExsit;
	}

	public void setIfUserNoExsit(boolean ifUserNoExsit) {
		this.ifUserNoExsit = ifUserNoExsit;
	}

	public boolean isIfIdcExsit() {
		return ifIdcExsit;
	}

	public void setIfIdcExsit(boolean ifIdcExsit) {
		this.ifIdcExsit = ifIdcExsit;
	}

	public boolean isIfUnitRepeat() {
		return ifUnitRepeat;
	}

	public void setIfUnitRepeat(boolean ifUnitRepeat) {
		this.ifUnitRepeat = ifUnitRepeat;
	}

	public boolean isIfSubOrgEmpty() {
		return ifSubOrgEmpty;
	}

	public void setIfSubOrgEmpty(boolean ifSubOrgEmpty) {
		this.ifSubOrgEmpty = ifSubOrgEmpty;
	}

}