package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;

/**
 * 一些类似常用语码表的选择（单选）
 * 必须传入码表类型编码和中文名称
 * Created by zww on 2014-8-6.
 */
@ManagedBean(name = "selectReasonBean")
@ViewScoped
public class SelectReasonBean extends FacesBean {

    /**码表集合*/
    private List<TsSimpleCode> dataList = new ArrayList<TsSimpleCode>(0);
    /**已选的码表*/
    private TsSimpleCode tsSimpleCode;
    /**码表类型编码*/
    private String codeTypeNo;
    /**码表类型中文名*/
    private String codeTypeName;


    /**ejb session bean*/
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);

    public SelectReasonBean() {
        this.codeTypeNo = JsfUtil.getRequest().getParameter("codeTypeNo");
        this.codeTypeName = JsfUtil.getRequest().getParameter("codeTypeName");
        if(StringUtils.isBlank(this.codeTypeNo) || StringUtils.isBlank(this.codeTypeName)) {
            throw new RuntimeException("codeTypeNo or codeTypeName is null!");
        }
        this.searchAction();
    }


    /**
     * 查询方法
     */
    public void searchAction() {
        //拼音码统一转成小写
        this.dataList = this.commService.findSimpleCodesByTypeId(this.codeTypeNo);
    }

    /**
     * 选择确定方法
     */
    public void selectAction() {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("selectSimpleCode",this.tsSimpleCode);
        RequestContext.getCurrentInstance().closeDialog(map);
    }

    /**
     * 关闭
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

    //getters and setters
    public List<TsSimpleCode> getDataList() {
        return dataList;
    }

    public void setDataList(List<TsSimpleCode> dataList) {
        this.dataList = dataList;
    }

    public TsSimpleCode getTsSimpleCode() {
        return tsSimpleCode;
    }

    public void setTsSimpleCode(TsSimpleCode tsSimpleCode) {
        this.tsSimpleCode = tsSimpleCode;
    }

    public String getCodeTypeName() {
        return codeTypeName;
    }

    public void setCodeTypeName(String codeTypeName) {
        this.codeTypeName = codeTypeName;
    }
}
