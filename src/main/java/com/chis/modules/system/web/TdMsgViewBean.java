package com.chis.modules.system.web;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.LinkedList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TdInfoAnnex;
import com.chis.modules.system.entity.TdInfoMain;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.DownLoadUtil;
import com.chis.modules.system.utils.MsgSendUtil;

/**
 * 我的寻呼查看
 * 
 * <AUTHOR>
 * @history 2014-07-16
 *
 * @history 文件存储到虚拟路径修改
 * @LastModify xt
 * @ModifyDate 2014年9月4日
 */
@ManagedBean(name = "tdMsgViewBean")
@ViewScoped
public class TdMsgViewBean extends FacesSearchBean {

	private static final long serialVersionUID = -738445454536625063L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(
			SessionData.SESSION_DATA);
	/** ejb session bean */
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);

	private TdInfoMain tdInfoMain = new TdInfoMain();
	/** 附件list */
	private List<TdInfoAnnex> annexList = new LinkedList<TdInfoAnnex>();
	/** 附件表 */
	private TdInfoAnnex tdInfoAnnex;
	/** 消息子表rid **/
	private String MsgSunRid;
	/** 消息子表状态**/
	private String state;
	
	@PostConstruct
	public void init() {
		String rid = JsfUtil.getRequest().getParameter("infoRid");
		state=JsfUtil.getRequest().getParameter("msgSubAcceptState");
		MsgSunRid=JsfUtil.getRequest().getParameter("msgSubId");
		if (StringUtils.isNotBlank(rid)) {
			this.tdInfoMain = this.systemModuleService.findTdInfoMain(Integer
					.valueOf(rid));
			if (null != tdInfoMain) {
				if (null != tdInfoMain.getTdInfoAnnexes()) {
					this.annexList = tdInfoMain.getTdInfoAnnexes();
				}
			}
		}
	}

	/**
	 * 下载文件
	 */
	public void downLoadDiskFile() {
		String path = JsfUtil.getAbsolutePath()+this.tdInfoAnnex.getAnnexAddr();
		FileInputStream fis = null;
		try {
			File file = new File(path);
			fis = new FileInputStream(file);
			String fileString = DownLoadUtil.uploadFile2Database(fis);
			DownLoadUtil.downFile(this.tdInfoAnnex.getAnnexName(), fileString);
		} catch (Exception e1) {
			e1.printStackTrace();
		} finally {// 关闭输入输出流
			try {
				fis.close();
			} catch (IOException e2) {
				e2.printStackTrace();
			}
		}
	}
	
	/**
	 * 修改状态
	 */
	public void editSubState() {
		if (null != this.MsgSunRid) {
			TsUserInfo tsUserInfo = this.sessionData.getUser();
			this.systemModuleService.editSubState(this.MsgSunRid, this.state, tsUserInfo.getRid());
		}
		MsgSendUtil.updateMsgInfo();
	}
	

	public TdInfoMain getTdInfoMain() {
		return tdInfoMain;
	}

	public void setTdInfoMain(TdInfoMain tdInfoMain) {
		this.tdInfoMain = tdInfoMain;
	}

	public List<TdInfoAnnex> getAnnexList() {
		return annexList;
	}

	public void setAnnexList(List<TdInfoAnnex> annexList) {
		this.annexList = annexList;
	}

	public TdInfoAnnex getTdInfoAnnex() {
		return tdInfoAnnex;
	}

	public void setTdInfoAnnex(TdInfoAnnex tdInfoAnnex) {
		this.tdInfoAnnex = tdInfoAnnex;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	
}
