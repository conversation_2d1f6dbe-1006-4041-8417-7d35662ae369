package com.chis.modules.system.web;

import java.util.List;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.common.utils.JsfUtil;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.FilePo;
import com.sun.star.uno.RuntimeException;

/**
 * pdf在线阅读，需要传入参数filePath,多个以,隔开，不需要带虚拟路径名<br/>
 * 
 * <AUTHOR>
 * @history 2014-08-18
 */
@ManagedBean(name = "pdfViewBean")
@ViewScoped
public class PdfViewBean extends FacesBean {

	private static final long serialVersionUID = 2695401462098651426L;
	/**
	 * 总页数
	 */
	private int totalPdfPage;
	/**
	 * 当前页
	 */
	private int currPdfPage;
	/**
	 * pdfList
	 */
	private List<FilePo> pdfList;
	/**当前文件*/
	private FilePo filePo;
	
	/** pdfHTML字符串 */
	private String pdfhtml;
	/** 字符串前 */
	private String PDFMODULE_FRONT = "<object type='application/pdf'  height='95%' width='100%' data='";
	/** 字符串后 */
	private String PDFMODULE_BACK = "' ></object>";

	public PdfViewBean() {
		this.pdfList = (List<FilePo>) JsfUtil.getSession().getAttribute("fileList");
		if(null != this.pdfList && this.pdfList.size() > 0) {
			JsfUtil.getSession().removeAttribute("fileList");
			for(FilePo file : pdfList) {
				file.setFilePath(Constants.XNPATH_DIR+file.getFilePath());
			}
			totalPdfPage = this.pdfList.size();
			currPdfPage = 1;
			this.filePo = this.pdfList.get(currPdfPage - 1);
			pdfhtml = new StringBuilder(PDFMODULE_FRONT).append(this.filePo.getFilePath()).append(PDFMODULE_BACK).toString();
		}else {
			throw new RuntimeException("未找到要浏览的文件！");
		}
	}

	/**
	 * 下载文件
	 */
	public void downLoadDiskFile() {
//		String path = JsfUtil.getAbsolutePath() + this.tdPortalNewsAnnex.getAnnexAddr();
//		FileInputStream fis = null;
//		try {
//			File file = new File(path);
//			fis = new FileInputStream(file);
//			String fileString = DownLoadUtil.uploadFile2Database(fis);
//			DownLoadUtil.downFile(this.tdPortalNewsAnnex.getAnnexName(), fileString);
//		} catch (Exception e1) {
//			e1.printStackTrace();
//			JsfUtil.addErrorMessage("文件下载失败！");
//		} finally {// 关闭输入输出流
//			try {
//				if (null != fis) {
//					fis.close();
//				}
//			} catch (IOException e2) {
//				e2.printStackTrace();
//			}
//		}
	}

	/**
	 * 上一个
	 */
	public void upPdf() {
		if (pdfList.size() > 0 && currPdfPage > 0) {
			currPdfPage = currPdfPage - 1;
			this.filePo = this.pdfList.get(currPdfPage - 1);
			pdfhtml = new StringBuilder(PDFMODULE_FRONT).append(this.filePo.getFilePath()).append(PDFMODULE_BACK).toString();
		}
	}

	/**
	 * 下一个
	 */
	public void downPdf() {
		if (pdfList.size() > 0 && currPdfPage < pdfList.size()) {
			currPdfPage = currPdfPage + 1;
			this.filePo = this.pdfList.get(currPdfPage - 1);
			pdfhtml = new StringBuilder(PDFMODULE_FRONT).append(this.filePo.getFilePath()).append(PDFMODULE_BACK).toString();
		}
	}
	
	public void viewAction() {
		currPdfPage = this.pdfList.indexOf(this.filePo) + 1;
		pdfhtml = new StringBuilder(PDFMODULE_FRONT).append(this.filePo.getFilePath()).append(PDFMODULE_BACK).toString();
	}


	public int getTotalPdfPage() {
		return totalPdfPage;
	}

	public void setTotalPdfPage(int totalPdfPage) {
		this.totalPdfPage = totalPdfPage;
	}

	public int getCurrPdfPage() {
		return currPdfPage;
	}

	public void setCurrPdfPage(int currPdfPage) {
		this.currPdfPage = currPdfPage;
	}

	public List<FilePo> getPdfList() {
		return pdfList;
	}

	public void setPdfList(List<FilePo> pdfList) {
		this.pdfList = pdfList;
	}

	public String getPdfhtml() {
		return pdfhtml;
	}

	public void setPdfhtml(String pdfhtml) {
		this.pdfhtml = pdfhtml;
	}

	public FilePo getFilePo() {
		return filePo;
	}

	public void setFilePo(FilePo filePo) {
		this.filePo = filePo;
	}



}
