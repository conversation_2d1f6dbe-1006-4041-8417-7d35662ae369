package com.chis.modules.system.web;


import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSysHoliday;
import com.chis.modules.system.service.SystemHolidayConfigService;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ManagedBean(name="systemHolidayConfigBean")
@ViewScoped
public class SystemHolidayConfigBean extends FacesEditBean {
    private SystemHolidayConfigService sysHoliConfService= SpringContextHolder.getBean(SystemHolidayConfigService.class);
    /**查询条件-开始日期*/
    private Date searchHolidaySDate;
    /**查询条件-结束日期*/
    private Date searchHolidayEDate;
    /**查询条件-日期类型*/
    private List<String> searchHolidayType;
    /**查询条件-日期状态*/
    private List<String> searchHolidayState;
    /**节假日rid*/
    private Integer rid;
    /**节假日*/
    private TsSysHoliday tsSysHoliday;

    public SystemHolidayConfigBean() {
        this.ifSQL=true;
        tsSysHoliday=new TsSysHoliday();
        searchHolidayType = new ArrayList<>();
        searchHolidayState = new ArrayList<>();
        searchHolidaySDate=new Date();
        this.searchAction();
    }


    @Override
    public void addInit() {
        tsSysHoliday=new TsSysHoliday();
        tsSysHoliday.setHoliType(1);
        tsSysHoliday.setStartDate(new Date());
        tsSysHoliday.setState(1);
        RequestContext.getCurrentInstance().execute("PF('HolidayComfigDialog').show()");
    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {
        tsSysHoliday=sysHoliConfService.find(TsSysHoliday.class, rid);
        RequestContext.getCurrentInstance().execute("PF('HolidayComfigDialog').show()");
    }

    @Override
    public void saveAction() {
        Boolean flag=false;
        Boolean time=false;
        if(tsSysHoliday.getHoliType()==null){
            JsfUtil.addErrorMessage("日期类型不能为空！");
            flag=true;
        }
        if(tsSysHoliday.getStartDate()==null || tsSysHoliday.getEndDate()==null){
            JsfUtil.addErrorMessage("配置时间段不能为空！");
            flag=true;
            time=true;
        }
        if(StringUtils.isBlank(tsSysHoliday.getHoliDesc())){
            JsfUtil.addErrorMessage("说明不能为空！");
            flag=true;
        }
        if(tsSysHoliday.getState()==null){
            JsfUtil.addErrorMessage("状态不能为空！");
            flag=true;
        }
        if(!time && sysHoliConfService.checkDate(tsSysHoliday)>0){
            JsfUtil.addErrorMessage("该日期段已配置，请勿重复配置！");
            flag=true;
        }
        if(flag){
            return;
        }
        sysHoliConfService.saveSysHoliday(tsSysHoliday);
        this.searchAction();
        RequestContext.getCurrentInstance().execute("PF('HolidayComfigDialog').hide()");
        JsfUtil.addSuccessMessage("保存成功！");
    }

    @Override
    public String[] buildHqls() {
        StringBuffer sb=new StringBuffer();
        sb.append("  from TS_SYS_HOLIDAY t where 1=1 ");
        if (null!=searchHolidaySDate) {
            sb.append(" AND t.START_DATE >= TO_DATE('").append(DateUtils.formatDate(searchHolidaySDate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null!=searchHolidayEDate) {
            sb.append(" AND T.END_DATE <= TO_DATE('").append(DateUtils.formatDate(searchHolidayEDate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        if(!CollectionUtils.isEmpty(searchHolidayType)&& searchHolidayType.size()==1){
            sb.append(" AND T.HOLI_TYPE = ").append(searchHolidayType.get(0));
        }
        if(!CollectionUtils.isEmpty(searchHolidayState)&& searchHolidayState.size()==1){
            sb.append(" AND T.STATE = ").append(searchHolidayState.get(0));
        }
        StringBuffer sql=new StringBuffer();
        sql.append(" select t.rid, t.HOLI_TYPE,t.START_DATE,t.END_DATE,t.HOLI_DESC,t.STATE ").append(sb).append(" ORDER BY T.START_DATE ");
        String h1 = " SELECT COUNT(*) " + sb.toString();
       return new String[]{sql.toString(),h1};
    }

    public Date getSearchHolidaySDate() {
        return searchHolidaySDate;
    }

    public void setSearchHolidaySDate(Date searchHolidaySDate) {
        this.searchHolidaySDate = searchHolidaySDate;
    }

    public Date getSearchHolidayEDate() {
        return searchHolidayEDate;
    }

    public void setSearchHolidayEDate(Date searchHolidayEDate) {
        this.searchHolidayEDate = searchHolidayEDate;
    }

    public List<String> getSearchHolidayType() {
        return searchHolidayType;
    }

    public void setSearchHolidayType(List<String> searchHolidayType) {
        this.searchHolidayType = searchHolidayType;
    }

    public List<String> getSearchHolidayState() {
        return searchHolidayState;
    }

    public void setSearchHolidayState(List<String> searchHolidayState) {
        this.searchHolidayState = searchHolidayState;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }


    public TsSysHoliday getTsSysHoliday() {
        return tsSysHoliday;
    }

    public void setTsSysHoliday(TsSysHoliday tsSysHoliday) {
        this.tsSysHoliday = tsSysHoliday;
    }
}
