package com.chis.modules.system.web;

import java.util.*;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.common.utils.*;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;

import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * 地区管理
 *
 * <AUTHOR>
 */
@ManagedBean(name = "zoneBean")
@ViewScoped
public class ZoneBean extends FacesSimpleBean {
    private static final long serialVersionUID = -738445454536625063L;
    /**
     * ejb session bean
     */
    private final SystemModuleServiceImpl systemService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    /**
     * 地区对象rid
     */
    private Integer rid;
    /**
     * 地区对象
     */
    private TsZone editZone = new TsZone();
    private TsZone oldZone = new TsZone();
    /**
     * 查询条件: 地区名称
     */
    private String searchZoneName;
    /**
     * 查询条件: 地区编码
     */
    private String searchZoneGb;
    /**
     * 查询条件: 地区级别
     */
    private String[] searchZoneLevel;
    /**
     * 查询条件: 是否市直属
     */
    private String[] searchIfCityDirect;
    /**
     * 查询条件: 是否省直属
     */
    private String[] searchIfProvDirect;
    /**
     * 查询条件: 状态
     */
    private String[] searchIfReveal;
    /**
     * 查询条件: 自检状态
     */
    private String[] searchState;
    /**
     * 状态切换操作
     */
    private String stateSwitch;
    private List<String> allMsgList;

    public ZoneBean() {
        this.searchIfReveal = new String[]{"1"};
        this.searchAction();
    }

    public void searchAction() {
        super.searchAction();
    }

    public String[] buildHqls() {
        String h1 = "select t ";
        String h2 = "select count(*) ";
        String base = "from TsZone t where 1 = 1 ";
        if (StringUtils.isNotBlank(this.searchZoneName)) {
            base += " and (t.zoneName like :searchZoneName escape '\\\' or t.fullName like :searchZoneName escape '\\\') ";
            super.paramMap.put("searchZoneName", "%" + StringUtils.convertBFH(this.searchZoneName) + "%");
        }
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            base += " and t.zoneGb like :searchZoneGb escape '\\\' ";
            super.paramMap.put("searchZoneGb", "%" + StringUtils.convertBFH(this.searchZoneGb) + "%");
        }
        if (ObjectUtil.isNotEmpty(this.searchZoneLevel)) {
            base += " and t.zoneType in (:searchZoneLevelList) ";
            List<Short> searchList = new ArrayList<>();
            for (String s : this.searchZoneLevel) {
                searchList.add(new Short(s));
            }
            super.paramMap.put("searchZoneLevelList", searchList);
        }
        if (ObjectUtil.isNotEmpty(this.searchIfCityDirect)) {
            base += " and nvl(t.ifCityDirect, 0) in (:searchIfCityDirectList) ";
            super.paramMap.put("searchIfCityDirectList", Arrays.asList(this.searchIfCityDirect));
        }
        if (ObjectUtil.isNotEmpty(this.searchIfProvDirect)) {
            base += " and nvl(t.ifProvDirect, 0) in (:searchIfProvDirectList) ";
            super.paramMap.put("searchIfProvDirectList", Arrays.asList(this.searchIfProvDirect));
        }
        if (ObjectUtil.isNotEmpty(this.searchIfReveal)) {
            base += " and nvl(t.ifReveal, 1) in (:searchIfRevealList) ";
            List<Short> searchList = new ArrayList<>();
            for (String s : this.searchIfReveal) {
                searchList.add(new Short(s));
            }
            super.paramMap.put("searchIfRevealList", searchList);
        }
        if (ObjectUtil.isNotEmpty(this.searchState)) {
            base += " and t.state in (:searchStateList) ";
            List<Short> searchList = new ArrayList<>();
            for (String s : this.searchState) {
                searchList.add(new Short(s));
            }
            super.paramMap.put("searchStateList", searchList);
        }
        h1 += base + " order by t.zoneGb ";
        h2 += base;
        return new String[]{h1, h2};
    }

    /**
     * 删除
     */
    public void deleteAction() {
        String msg = this.systemService.deleteZone(this.rid);
        if (StringUtils.isNotBlank(msg)) {
            JsfUtil.addErrorMessage(msg);
        } else {
            this.searchAction();
        }
    }

    /**
     * 启用、停用
     */
    public void stateChangeAction() {
        try {
            this.systemService.changeStateZone(this.rid, Short.valueOf(this.stateSwitch));
        } catch (NumberFormatException e) {
            JsfUtil.addErrorMessage("操作失败！");
        }
        this.searchAction();
    }

    /**
     * 添加初始化
     */
    public void addInitAction() {
        this.editZone = new TsZone();
        initZone();
    }

    /**
     * 修改初始化
     */
    public void modInitAction() {
        this.editZone = this.systemService.findZone(this.rid);
        this.editZone.setModifyDate(new Date());
        this.editZone.setModifyManid(Global.getUser().getRid());
        initZone();
    }

    public void initZone() {
        if (ObjectUtil.isEmpty(this.editZone.getIfReveal())) {
            this.editZone.setIfReveal(new Short("1"));
        }
        if (ObjectUtil.isEmpty(this.editZone.getIfCityDirect())) {
            this.editZone.setIfCityDirect("0");
        }
        if (ObjectUtil.isEmpty(this.editZone.getIfProvDirect())) {
            this.editZone.setIfProvDirect("0");
        }
    }

    /**
     * 输入10位地区编码后填充地区级别、第三方编码、真实地区级别
     */
    public void fillInfoByGb() {
        String zoneGb = this.editZone.getZoneGb();
        if (ObjectUtil.isEmpty(zoneGb) || (zoneGb.length() != 10 && zoneGb.length() != 12)) {
            return;
        }
        if (ObjectUtil.isEmpty(this.editZone.getZoneCode())) {
            this.editZone.setZoneCode(zoneGb + "00");
        }
        if (ObjectUtil.isEmpty(this.editZone.getDsfCode())) {
            this.editZone.setDsfCode(zoneGb);
        }
        Short zoneType = ObjectUtil.convert(Short.class, ZoneUtil.getZoneType(zoneGb));
        if (zoneType == null) {
            return;
        }
        if (ObjectUtil.isEmpty(this.editZone.getZoneType())) {
            this.editZone.setZoneType(zoneType);
        }
        if (ObjectUtil.isEmpty(this.editZone.getRealZoneType())) {
            this.editZone.setRealZoneType(zoneType);
        }
    }

    /**
     * 输入第三方编码后填充真实地区级别
     */
    public void fillInfoByDsfCode() {
        String dsfCode = StringUtils.objectToString(this.editZone.getDsfCode());
        if (ObjectUtil.isEmpty(dsfCode) || dsfCode.length() != 10) {
            return;
        }
        Short zoneType = ObjectUtil.convert(Short.class, ZoneUtil.getZoneType(dsfCode));
        if (zoneType == null) {
            return;
        }
        if (ObjectUtil.isEmpty(this.editZone.getRealZoneType())) {
            this.editZone.setRealZoneType(zoneType);
        }
    }

    /**
     * 输入地区名称后填充地区简称
     */
    public void fillInfoByName() {
        String zoneName = this.editZone.getZoneName();
        if (ObjectUtil.isEmpty(zoneName) || ObjectUtil.isNotEmpty(this.editZone.getZoneShortName())) {
            return;
        }
        this.editZone.setZoneShortName(zoneName);
    }

    /**
     * 保存
     */
    public void saveAction() {
        try {
            Integer zoneType = null;
            boolean success = true;
            String zoneGb = this.editZone.getZoneGb();
            if (ObjectUtil.isEmpty(zoneGb)) {
                JsfUtil.addErrorMessage("10位地区编码不能为空！");
                success = false;
            } else if (zoneGb.length() != 10) {
                JsfUtil.addErrorMessage("10位地区编码长度不正确！");
                success = false;
            } else {
                zoneType = ObjectUtil.convert(Integer.class, this.editZone.getZoneType());
                if (zoneType != null && ZoneUtil.getZoneType(zoneGb) != zoneType) {
                    JsfUtil.addErrorMessage("10位地区编码与地区级别不匹配！");
                    success = false;
                }
                if (this.systemService.hasSameZoneCode(1, zoneGb, this.editZone.getRid())) {
                    JsfUtil.addErrorMessage("10位地区编码已存在！");
                    success = false;
                }
                if (success) {
                    success = pakFullName(zoneGb);
                }
            }
            String zoneCode = this.editZone.getZoneCode();
            if (ObjectUtil.isEmpty(zoneCode)) {
                JsfUtil.addErrorMessage("12位地区编码不能为空！");
                success = false;
            } else if (zoneCode.length() != 12) {
                JsfUtil.addErrorMessage("12位地区编码长度不正确！");
                success = false;
            } else {
                if (zoneType != null && ZoneUtil.getZoneType(zoneCode) != zoneType) {
                    JsfUtil.addErrorMessage("12位地区编码与地区级别不匹配！");
                    success = false;
                }
                if (this.systemService.hasSameZoneCode(2, zoneCode, this.editZone.getRid())) {
                    JsfUtil.addErrorMessage("12位地区编码已存在！");
                    success = false;
                }
            }
            if (StringUtils.isBlank(this.editZone.getZoneName())) {
                JsfUtil.addErrorMessage("地区名称不能为空！");
                success = false;
            }
            if (StringUtils.isBlank(this.editZone.getZoneShortName())) {
                JsfUtil.addErrorMessage("地区简称不能为空！");
                success = false;
            }
            if (this.editZone.getZoneType() == null) {
                JsfUtil.addErrorMessage("请选择地区级别！");
                success = false;
            }
            String dsfCode = this.editZone.getDsfCode();
            if (StringUtils.isBlank(dsfCode)) {
                JsfUtil.addErrorMessage("第三方编码不能为空！");
                success = false;
            }
            if (this.editZone.getRealZoneType() == null) {
                JsfUtil.addErrorMessage("请选择真实地区级别！");
                success = false;
            } else if (StringUtils.isNotBlank(dsfCode) && dsfCode.length() == 10) {
                Integer realZoneType = ObjectUtil.convert(Integer.class, this.editZone.getRealZoneType());
                if (realZoneType != null && ZoneUtil.getZoneType(dsfCode) != realZoneType) {
                    JsfUtil.addErrorMessage("第三方编码与真实地区级别不匹配！");
                    success = false;
                }
            }
            if (!success) {
                return;
            }
            this.systemService.preEntity(this.editZone);
            this.systemService.saveOrUpdateZone(this.editZone);
            this.searchAction();
            RequestContext requestContext = RequestContext.getCurrentInstance();
            requestContext.execute("ZoneEditDialog.hide()");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
     * 封装全称
     *
     * @param zoneGb 当前地区ZONE_GB
     * @return 封装是否成功
     */
    private boolean pakFullName(String zoneGb) {
        String parentCode = ZoneUtil.getParentCode(zoneGb);
        //全国或省直接存名称
        if (StringUtils.isBlank(parentCode) || StringUtils.isBlank(ZoneUtil.getParentCode(parentCode))) {
            this.editZone.setFullName(this.editZone.getZoneName());
            return true;
        }
        TsZone parentZone = this.systemService.findZoneByZoneGb(parentCode);
        if (parentZone == null) {
            JsfUtil.addErrorMessage("上级地区不存在！");
            return false;
        } else if (StringUtils.isBlank(parentZone.getFullName())) {
            JsfUtil.addErrorMessage("上级地区未维护全称！");
            return false;
        }
        this.editZone.setFullName(parentZone.getFullName() + "_" + this.editZone.getZoneName());
        return true;
    }

    /**
     * 地区自检
     */
    public void checkAllTsZone() {
        String hql = " select t from TsZone t where t.ifReveal = '1' order by t.zoneGb ";
        List<TsZone> zoneList = this.systemService.findByHql(hql, TsZone.class);
        if (ObjectUtil.isEmpty(zoneList)) {
            return;
        }
        Map<String, TsZone> zoneMap = new HashMap<>();
        Map<Integer, TsZone> zoneNeedSaveMap = new HashMap<>();
        for (TsZone tsZone : zoneList) {
            if (!new Short("1").equals(tsZone.getState())) {
                zoneNeedSaveMap.put(tsZone.getRid(), tsZone);
            }
            tsZone.setState(new Short("1"));
            tsZone.setErrRsn("");
            zoneMap.put(tsZone.getZoneGb(), tsZone);
        }
        this.allMsgList = new ArrayList<>();
        int totleNum = zoneList.size();
        int successNum = 0;
        int failNum = 0;
        for (TsZone zone : zoneList) {
            Integer rid = zone.getRid();
            String fullName = StringUtils.objectToString(zone.getFullName());
            String zoneType = StringUtils.objectToString(zone.getZoneType());
            String realZoneType = StringUtils.objectToString(zone.getRealZoneType());
            String zoneGb = StringUtils.objectToString(zone.getZoneGb());
            String dsfCode = StringUtils.objectToString(zone.getDsfCode());
            String zeros = "0000000000";
            String msg = "";
            List<String> msgList = new ArrayList<>();
            //校验10位地区编码&地区类型
            boolean verifyZoneGb = true;
            try {
                if (ObjectUtil.isEmpty(zoneGb)) {
                    verifyZoneGb = false;
                    msgList.add("10位地区编码为空");
                    this.allMsgList.add("地区RID：" + rid + "的" + fullName + "对应的ZONE_GB为空！");
                } else if (zoneGb.length() != 10) {
                    verifyZoneGb = false;
                    msgList.add("10位地区编码长度不正确");
                    this.allMsgList.add("地区RID：" + rid + "的" + fullName + "对应的ZONE_GB长度不正确！");
                }
                if (ObjectUtil.isEmpty(zoneType)) {
                    msgList.add("地区级别为空");
                    this.allMsgList.add("地区RID：" + rid + "的地区编码ZONE_GB：" + zoneGb + "的" + fullName + "对应的ZONE_TYPE为空！");
                } else if (verifyZoneGb) {
                    boolean verifyZoneTypeSame = ObjectUtil.convert(Integer.class, zoneType, 5) < 5
                            && !zoneType.equals(StringUtils.objectToString(ZoneUtil.getZoneType(zoneGb)));
                    if (verifyZoneTypeSame) {
                        msgList.add("10位地区编码与地区级别不匹配");
                        this.allMsgList.add("地区RID：" + rid + "的地区编码ZONE_GB：" + zoneGb + "的" + fullName + "对应的ZONE_TYPE(" + zoneType + ")维护异常！");
                    }
                }
            } catch (Exception e) {
                msgList.add("10位地区编码与地区级别不匹配");
                this.allMsgList.add("地区RID：" + rid + "的地区编码ZONE_GB：" + zoneGb + "的" + fullName + "对应的ZONE_TYPE(" + zoneType + ")维护异常！");
            }
            //非国家地区校验是否存在上级
            boolean hasParentZone = !"1".equals(zoneType) && ObjectUtil.isEmpty(msgList) && zoneMap.get(ZoneUtil.getParentCode(zoneGb)) == null;
            if (hasParentZone) {
                msgList.add("10位地区编码对应上级地区不存在");
                this.allMsgList.add("地区RID：" + rid + "的地区编码ZONE_GB：" + zoneGb + "的" + fullName + "对应上级地区不存在！");
            }
            //校验第三方地区编码&真实地区级别
            try {
                boolean verifyDsfCode = true;
                if (ObjectUtil.isEmpty(dsfCode)) {
                    msgList.add("第三方编码为空");
                    this.allMsgList.add("地区RID：" + rid + "的" + fullName + "对应的DSF_CODE为空！");
                    verifyDsfCode = false;
                }
                if (ObjectUtil.isEmpty(realZoneType)) {
                    msgList.add("真实地区级别为空");
                    this.allMsgList.add("地区RID：" + rid + "的地区编码DSF_CODE：" + dsfCode + "的" + fullName + "对应的REAL_ZONE_TYPE为空！");
                    verifyDsfCode = false;
                }
                if (verifyDsfCode && dsfCode.length() == 10) {
                    boolean verifyZoneTypeSame = ObjectUtil.convert(Integer.class, realZoneType, 5) < 5
                            && !realZoneType.equals(StringUtils.objectToString(ZoneUtil.getZoneType(dsfCode)));
                    if (verifyZoneTypeSame) {
                        msgList.add("第三方编码与真实地区级别不匹配");
                        this.allMsgList.add("地区RID：" + rid + "的地区编码DSF_CODE：" + dsfCode + "的" + fullName + "对应的REAL_ZONE_TYPE(" + realZoneType + ")维护异常！");
                    }
                }
            } catch (Exception e) {
                msgList.add("第三方编码与真实地区级别不匹配");
                this.allMsgList.add("地区RID：" + rid + "的地区编码DSF_CODE：" + dsfCode + "的" + fullName + "对应的REAL_ZONE_TYPE(" + realZoneType + ")维护异常！");
            }
            if (ObjectUtil.isEmpty(msgList)) {
                successNum++;
                continue;
            }
            failNum++;
            zone.setState(new Short("2"));
            zone.setErrRsn(StringUtils.list2string(msgList, "；"));
            zoneNeedSaveMap.put(zone.getRid(), zone);
        }
        try {
            List<TsZone> saveZoneList = new ArrayList<>();
            for (Map.Entry<Integer, TsZone> zoneEntry : zoneNeedSaveMap.entrySet()) {
                saveZoneList.add(zoneEntry.getValue());
            }
            this.systemService.updateZoneCheckMsg(saveZoneList);
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("自检结果保存失败!");
            this.allMsgList.add("自检结果保存失败!");
        }
        if (ObjectUtil.isEmpty(this.allMsgList)) {
            JsfUtil.addSuccessMessage("本次共自检" + totleNum + "条记录，全部自检成功！");
        } else {
            JsfUtil.addSuccessMessage("本次共自检" + totleNum + "条记录，成功" + successNum + "条，失败" + failNum +"条！");
        }
        this.searchAction();
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TsZone getEditZone() {
        return editZone;
    }

    public void setEditZone(TsZone editZone) {
        this.editZone = editZone;
    }

    public TsZone getOldZone() {
        return oldZone;
    }

    public void setOldZone(TsZone oldZone) {
        this.oldZone = oldZone;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneGb() {
        return searchZoneGb;
    }

    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }

    public String[] getSearchZoneLevel() {
        return searchZoneLevel;
    }

    public void setSearchZoneLevel(String[] searchZoneLevel) {
        this.searchZoneLevel = searchZoneLevel;
    }

    public String[] getSearchIfCityDirect() {
        return searchIfCityDirect;
    }

    public void setSearchIfCityDirect(String[] searchIfCityDirect) {
        this.searchIfCityDirect = searchIfCityDirect;
    }

    public String[] getSearchIfProvDirect() {
        return searchIfProvDirect;
    }

    public void setSearchIfProvDirect(String[] searchIfProvDirect) {
        this.searchIfProvDirect = searchIfProvDirect;
    }

    public String[] getSearchIfReveal() {
        return searchIfReveal;
    }

    public void setSearchIfReveal(String[] searchIfReveal) {
        this.searchIfReveal = searchIfReveal;
    }

    public String[] getSearchState() {
        return searchState;
    }

    public void setSearchState(String[] searchState) {
        this.searchState = searchState;
    }

    public String getStateSwitch() {
        return stateSwitch;
    }

    public void setStateSwitch(String stateSwitch) {
        this.stateSwitch = stateSwitch;
    }

    public List<String> getAllMsgList() {
        return allMsgList;
    }

    public void setAllMsgList(List<String> allMsgList) {
        this.allMsgList = allMsgList;
    }
}
