package com.chis.modules.system.web;

import java.io.IOException;
import java.net.URL;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.namespace.QName;

import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.service.SystemServiceImpl;
import com.wondersgroup.sso.webservice.GetLoginNameByTicketReq;
import com.wondersgroup.sso.webservice.GetLoginNameByTicketRsp;
import com.wondersgroup.sso.webservice.Ssoservice;
import com.wondersgroup.sso.webservice.SsoservicePortType;


/**
 * 根据url、keyword、ticket打开某个开始
 * 参数keyword-接入系统的标识，门户网站传入 ticket-门户网站传入
 * <AUTHOR>
 * @createTime 2016年7月21日
 */
@WebServlet(name="WSDLServlet",value="/WSDLServlet")
public class WSDLServlet extends HttpServlet{
	private static final long serialVersionUID = 1L;
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	private SystemServiceImpl systemService = (SystemServiceImpl)SpringContextHolder.getBean(SystemServiceImpl.class);
	
	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		String keyword = request.getParameter("keyword");
		String ticket = request.getParameter("ticket");
		String url = PropertyUtils.getValue("ssoWdsl.serviceUrl");
		String servletUrl = PropertyUtils.getValue("ssoWdsl.servletUrl");
		QName qName = new QName("http://webservice.sso.wondersgroup.com/", "Ssoservice");
		URL serviceURL = new URL(com.wondersgroup.sso.webservice.Ssoservice.class.getResource("."), url);

		SsoservicePortType ssoservice = new Ssoservice(serviceURL,qName).getSsoserviceHttpSoap12Endpoint();
		GetLoginNameByTicketReq byTicketReq = new GetLoginNameByTicketReq();
		//接入系统的标识
		byTicketReq.setKeyword(keyword);
		//ticket，由门户网站传入
		byTicketReq.setTicket(ticket);
		//消息编号，可使用日期+流水号
		byTicketReq.setMsgid(String.valueOf(System.currentTimeMillis()));
		byTicketReq.setService(servletUrl);
		GetLoginNameByTicketRsp byTicketRsp = ssoservice.getLoginNameByTicket(byTicketReq);
		Integer code = byTicketRsp.getCode();
		/**	
		 * 0表示失败，message中存放的是失败原因说明
		 * 1表示成功，message中存放的是username
		 * */
		if (code==0) {
			response.sendRedirect(byTicketRsp.getLoginurl());
		}else {
			//
			String  unitNo = byTicketRsp.getMessage();
			TsUserInfo user = this.systemModuleService.findUserByUserNo(unitNo);
			if (null!=user) {
				SessionData sessionData = new SessionData(user);
            	Map<String, String> btnMap = this.systemService.findUsersBtn(user.getRid());
            	sessionData.setButtonMap(btnMap);
            	sessionData.setBtnSet(systemService.findUserBtns(user.getRid()));
            	request.getSession().setAttribute(SessionData.SESSION_DATA, sessionData);
            	response.sendRedirect("head.faces");
			}else {
				response.sendRedirect("login.faces");
			}
		}
			
	}
	
	@Override
	protected void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		this.doPost(request, response);
	}
}
