package com.chis.modules.system.web;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.common.utils.JsfUtil;

/**
 * <AUTHOR>
 * @createData 2014-1-10
 */
@ManagedBean(name = "PDFprintBean")
@ViewScoped
public class PDFprintBean extends FacesBean {
    private static final long serialVersionUID = -324024930020326308L;
    private String fileSrc;

    public PDFprintBean() {
        this.fileSrc = JsfUtil.getRequest().getParameter("filesrc");
    }

    public String getFileSrc() {
        return fileSrc;
    }

    public void setFileSrc(String fileSrc) {
        this.fileSrc = fileSrc;
    }
}
