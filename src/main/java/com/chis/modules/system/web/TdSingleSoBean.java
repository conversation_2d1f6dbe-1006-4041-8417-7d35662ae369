package com.chis.modules.system.web;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;



import org.primefaces.context.RequestContext;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsDsfLoginf;
import com.chis.modules.system.entity.TsDsfLoginfParam;
import com.chis.modules.system.entity.TsDsfSys;
import com.chis.modules.system.entity.TsDsfSysParam;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;

@ManagedBean(name="tdSingleSoBean")
@ViewScoped
public class TdSingleSoBean extends FacesEditBean implements Serializable{

	private static final long serialVersionUID = 1L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/** ejb session bean */
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);

	
	/**用户信息**/
	private TsUserInfo tsUserInfo;
	/**第三方系统注册信息**/
	private TsDsfSys tsDsfSys;
	/**第三方系统登录信息**/
	private TsDsfLoginf tsDsfLoginf;
	/**程序地址**/
	private String sysUrl;
	/** 子表：第三方系统登录信息 的集合 */
	private List<TsDsfLoginfParam> loginlist= new ArrayList<TsDsfLoginfParam>();
	/**子表：第三方系统注册信息的集合**/
	private List<TsDsfSysParam> syslist = new ArrayList<TsDsfSysParam>();
	/** 子表：第三方系统登录信息 的的单条数据 */
	private TsDsfLoginfParam tsDsfLoginfParam;
	
    /**打开dialog时传入的对应id**/
    private Integer dialogid;
    /**打开的类别1-已维护的；2-未维护的**/
    private Integer opentype;
    /**cs程序地址**/
    private String app_path;
	
	@PostConstruct
	public void init(){
		tsUserInfo = this.sessionData.getUser();
		tsDsfSys=new TsDsfSys();
		tsDsfLoginfParam=new TsDsfLoginfParam();
	}
	
	public void saveAction(){
		if(StringUtils.isBlank(this.sysUrl)){
			JsfUtil.addErrorMessage("程序地址不能为空！");			
			return;
		}
		loginlist=new ArrayList<TsDsfLoginfParam>();
		int id=systemModuleService.getSysLoginfid(this.dialogid,this.sessionData.getUser().getRid());
		if(id==0){
			tsDsfLoginf=new TsDsfLoginf();
		}else{
			this.tsDsfLoginf=systemModuleService.findSysLoginf(id);
		}
		tsUserInfo = this.sessionData.getUser();
		tsDsfLoginf.setTsUserInfo(tsUserInfo);
		tsDsfLoginf.setTsDsfSys(tsDsfSys);
		tsDsfLoginf.setSysUrl(sysUrl);
		tsDsfLoginf.setCreateDate(new Date());
		tsDsfLoginf.setCreateManid(this.sessionData.getUser().getRid());
		List<TsDsfLoginfParam> newlist=new ArrayList<TsDsfLoginfParam>();
		if(null != this.syslist){
			for(int i=0;i<syslist.size();i++){
				TsDsfSysParam t1=syslist.get(i);
				TsDsfLoginfParam t2=new TsDsfLoginfParam();
				t2.setParamEn(t1.getParamEn());
				t2.setParamValue(t1.getEnValue());
				t2.setCreateDate(new Date());
				t2.setCreateManid(this.sessionData.getUser().getRid());
				t2.setTsDsfLoginfId(tsDsfLoginf);
				newlist.add(t2);
			}
			loginlist=newlist;
		}
		tsDsfLoginf.setList(loginlist);
		systemModuleService.saveSysLoginf(tsDsfLoginf);
		RequestContext currentInstance = RequestContext.getCurrentInstance();
		currentInstance.execute("PF('SystemDialog').hide();");
	}
	
	public void delAction(){
		int id=systemModuleService.getSysLoginfid(this.dialogid,this.sessionData.getUser().getRid());
		this.tsDsfLoginf=systemModuleService.findSysLoginf(id);
		if(id>0){
			systemModuleService.delSysLoginf(id);
			RequestContext currentInstance = RequestContext.getCurrentInstance();
			currentInstance.execute("PF('SystemDialog').hide();");
		}
	}
	
	public void onClick(){
		tsDsfSys=new TsDsfSys();
		tsDsfLoginf=new TsDsfLoginf();
	    this.sysUrl=null;
	    this.tsDsfSys=systemModuleService.findSys(this.dialogid);
	    this.syslist=tsDsfSys.getList();
		if(this.opentype ==2){
			if(tsDsfSys.getXtType() == 1){
				this.sysUrl=tsDsfSys.getSysUrl();
			}
		}else{
			int id=systemModuleService.getSysLoginfid(this.dialogid,this.sessionData.getUser().getRid());
			this.tsDsfLoginf=systemModuleService.findSysLoginf(id);
			if(tsDsfLoginf.getTsDsfSys().getXtType() ==1){
				this.sysUrl=tsDsfLoginf.getTsDsfSys().getSysUrl();
			}else{
				this.sysUrl=tsDsfLoginf.getSysUrl();
			}
			this.loginlist=tsDsfLoginf.getList();
			List<TsDsfSysParam> newlist=new ArrayList<TsDsfSysParam>();
			for(int i=0;i<loginlist.size();i++){
				TsDsfSysParam t1=syslist.get(i);
				TsDsfLoginfParam t2=loginlist.get(i);
				if(t1.getParamEn().equals(t2.getParamEn())){
					t1.setEnValue(t2.getParamValue());
					newlist.add(t1);
				}
			}
			if(null != newlist && newlist.size()>0){
				syslist=newlist;
			}
		}	
	}
	
	public void showappPath(){
		if(null != this.app_path){
			this.sysUrl=app_path;
		}
	}
	
	public TsUserInfo getTsUserInfo() {
		return tsUserInfo;
	}

	public void setTsUserInfo(TsUserInfo tsUserInfo) {
		this.tsUserInfo = tsUserInfo;
	}

	public TsDsfSys getTsDsfSys() {
		return tsDsfSys;
	}

	public void setTsDsfSys(TsDsfSys tsDsfSys) {
		this.tsDsfSys = tsDsfSys;
	}

	public String getSysUrl() {
		return sysUrl;
	}

	public void setSysUrl(String sysUrl) {
		this.sysUrl = sysUrl;
	}

	public TsDsfLoginfParam getTsDsfLoginfParam() {
		return tsDsfLoginfParam;
	}

	public void setTsDsfLoginfParam(TsDsfLoginfParam tsDsfLoginfParam) {
		this.tsDsfLoginfParam = tsDsfLoginfParam;
	}

	public TsDsfLoginf getTsDsfLoginf() {
		return tsDsfLoginf;
	}

	public void setTsDsfLoginf(TsDsfLoginf tsDsfLoginf) {
		this.tsDsfLoginf = tsDsfLoginf;
	}

	@Override
	public void addInit() {
		
	}

	@Override
	public void viewInit() {
		
	}

	@Override
	public void modInit() {
		
	}

	@Override
	public String[] buildHqls() {
		return null;
	}
	
	public Integer getDialogid() {
		return dialogid;
	}

	public void setDialogid(Integer dialogid) {
		this.dialogid = dialogid;
	}

	public Integer getOpentype() {
		return opentype;
	}

	public void setOpentype(Integer opentype) {
		this.opentype = opentype;
	}

	public String getApp_path() {
		return app_path;
	}

	public void setApp_path(String app_path) {
		this.app_path = app_path;
	}

	public List<TsDsfLoginfParam> getLoginlist() {
		return loginlist;
	}

	public void setLoginlist(List<TsDsfLoginfParam> loginlist) {
		this.loginlist = loginlist;
	}

	public List<TsDsfSysParam> getSyslist() {
		return syslist;
	}

	public void setSyslist(List<TsDsfSysParam> syslist) {
		this.syslist = syslist;
	}

	
}
