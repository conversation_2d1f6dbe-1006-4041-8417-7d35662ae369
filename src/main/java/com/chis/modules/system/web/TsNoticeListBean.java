package com.chis.modules.system.web;

import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsNotice;
import com.chis.modules.system.service.TsNoticeService;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.UploadedFile;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.io.IOException;
import java.util.Date;
import java.util.UUID;

/**
 * @Description : 通知通告管理模块
 * @ClassAuthor : anjing
 * @Date : 2020/4/8 10:36
 **/
@ManagedBean(name="tsNoticeListBean")
@ViewScoped
public class TsNoticeListBean extends FacesEditBean {

    /**查询条件：通知开始日期*/
    private Date searchStartDate;
    /**查询条件：通知结束日期*/
    private Date searchEndDate;
    /**查询条件：通知标题*/
    private String searchNoticTitle;
    /**查询条件：是否发布*/
    private String[] searchIfPublish;
    /**查询条件：状态*/
    private String[] searchStateMark;

    /**主键Rid*/
    private Integer rid;
    /**通知通告对象*/
    private TsNotice tsNotice;

    private TsNoticeService tsNoticeService = SpringContextHolder.getBean(TsNoticeService.class);

    public TsNoticeListBean() {
        super.ifSQL = Boolean.TRUE;
        this.initParam();
        this.searchAction();
    }

    /**
    * @Description : 参数初始化
    * @MethodAuthor: anjing
    * @Date : 2020/4/8 10:39
    **/
    private void initParam() {
        // 通知日期（默认今年1-1~当前）
        this.searchStartDate = DateUtils.getYearFirstDay(new Date());
        this.searchEndDate = new Date();
        // 是否发布（是、否，默认是）
        this.searchIfPublish = new String[]{"1"};
        // 状态（待提交（数据库值0）、已提交（数据库值2）；默认待提交）
        this.searchStateMark = new String[]{"0"};
    }

    /**
    * @Description : 执行查询
    * @MethodAuthor: anjing
    * @Date : 2020/4/8 10:39
    **/
    @Override
    public void searchAction() {
        if(null != this.searchStartDate && null != this.searchEndDate) {
            if (this.searchEndDate.before(this.searchStartDate)) {
                JsfUtil.addErrorMessage("通知开始日期应小于等于结束日期！");
                return;
            }
        }
        super.searchAction();
    }

    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder();
        sb.append(" FROM TS_NOTICE T  WHERE 1=1 ");
        if(null != this.searchStartDate) {
            sb.append(" AND T.NOTICE_DATE >= TO_DATE('")
                    .append(DateUtils.formatDate(this.searchStartDate, "yyyy-MM-dd"))
                    .append("','yyyy-MM-dd')");
        }
        if (null != this.searchEndDate) {
            sb.append(" AND T.NOTICE_DATE <= TO_DATE('")
                    .append(DateUtils.formatDate(this.searchEndDate, "yyyy-MM-dd"))
                    .append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
        }
        if(StringUtils.isNotBlank(this.searchNoticTitle)) {
            sb.append(" AND T.NOTICE_TITLE LIKE :noticeTitle ");
            this.paramMap.put("noticeTitle", "%" + this.searchNoticTitle.trim() + "%");
        }
        if(null != this.searchIfPublish && this.searchIfPublish.length > 0) {
            StringBuffer publishSb = new StringBuffer();
            for(String state : this.searchIfPublish) {
                publishSb.append(",").append(state);
            }
            sb.append(" AND T.IF_PUBLISH IN (").append(publishSb.substring(1)).append(")");
        }
        if(null != this.searchStateMark && this.searchStateMark.length > 0) {
            StringBuffer stateSb = new StringBuffer();
            for(String state : this.searchStateMark) {
                stateSb.append(",").append(state);
            }
            sb.append(" AND T.STATE_MARK IN (").append(stateSb.substring(1)).append(")");
        } else {
            sb.append(" AND T.STATE_MARK IN (0, 2) ");
        }
        StringBuilder searchSql = new StringBuilder("SELECT T.RID, T.NOTICE_TITLE, T.NOTICE_DATE, T.IF_PUBLISH, T.STATE_MARK, T.FILE_PATH ").append(sb);
        searchSql.append(" ORDER BY T.NOTICE_DATE DESC ").toString();

        StringBuilder countSql = new StringBuilder("SELECT COUNT(t.RID) ").append(sb);
        return new String[]{searchSql.toString(),countSql.toString()};
    }

    @Override
    public void addInit() {
        this.tsNotice = new TsNotice();
        this.tsNotice.setIfPublish(1);
        this.tsNotice.setStateMark(0);
        this.tsNotice.setNoticeDate(new Date());
    }

    @Override
    public void viewInit() {
        this.modInit();
    }

    @Override
    public void modInit() {
        this.tsNotice = this.tsNoticeService.find(TsNotice.class, this.rid);
    }

    /**
    * @Description : 保存前校验
    * @MethodAuthor: anjing
    * @Date : 2020/4/8 16:51
    **/
    public boolean beforSave() {
        boolean flag = true;
        if(StringUtils.isBlank(this.tsNotice.getNoticeTitle())) {
            JsfUtil.addErrorMessage("通知标题不能为空！");
            flag = false;
        }
        if(null == this.tsNotice.getNoticeDate()) {
            JsfUtil.addErrorMessage("通知日期不能为空！");
            flag = false;
        }
        if(null == this.tsNotice.getIfPublish()) {
            JsfUtil.addErrorMessage("是否发布不能为空！");
            flag = false;
        }
        if(StringUtils.isBlank(this.tsNotice.getFilePath())) {
            JsfUtil.addErrorMessage("附件不能为空！");
            flag = false;
        }
        return flag;
    }

    @Override
    public void saveAction() {
        try {
            if(!beforSave()) {
                return;
            }
            this.tsNoticeService.upsertEntity(this.tsNotice);
            JsfUtil.addSuccessMessage("保存成功！");
            this.searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
    * @Description : 提交前验证
    * @MethodAuthor: anjing
    * @Date : 2020/4/8 14:04
    **/
    public void boforeSubmit() {
        try {
            if(!beforSave()) {
                return;
            }
            RequestContext context = RequestContext.getCurrentInstance();
            context.execute("PF('ConfirmDialog').show()");

        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
    * @Description : 执行提交
    * @MethodAuthor: anjing
    * @Date : 2020/4/8 14:04
    **/
    public void submitAction() {
        try {
            this.tsNotice.setStateMark(2);
            this.tsNoticeService.upsertEntity(this.tsNotice);
            JsfUtil.addSuccessMessage("提交成功！");
            this.searchAction();
            this.backAction();
            RequestContext context = RequestContext.getCurrentInstance();
            context.update("tabView");
            context.execute("PF('ConfirmDialog').hide()");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    /**
    * @Description : 执行撤销
    * @MethodAuthor: anjing
    * @Date : 2020/4/8 14:38
    **/
    public void revokeAction() {
        try {
            this.tsNotice.setStateMark(0);
            this.tsNoticeService.upsertEntity(this.tsNotice);
            JsfUtil.addSuccessMessage("撤销成功！");
            this.searchAction();
            RequestContext context = RequestContext.getCurrentInstance();
            context.update("tabView");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
    }

    /**
    * @Description : 发布/取消发布
    * @MethodAuthor: anjing
    * @Date : 2020/4/8 15:11
    **/
    public void publichAction() {
        String msg = "";
        this.tsNotice = this.tsNoticeService.find(TsNotice.class, this.rid);
        if(this.tsNotice.getIfPublish() == 1) {
            this.tsNotice.setIfPublish(0);
            msg = "取消发布";
        } else {
            this.tsNotice.setIfPublish(1);
            msg = "发布";
        }
        try {
            this.tsNoticeService.upsertEntity(this.tsNotice);
            JsfUtil.addSuccessMessage(msg+ "成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage(msg + "失败！");
        }
    }

    /**
    * @Description : 上传附件
    * @MethodAuthor: anjing
    * @Date : 2020/4/8 16:08
    **/
    public void fileUpload(FileUploadEvent event) {
        UploadedFile file = event.getFile();
        RequestContext.getCurrentInstance().update("tabView:editForm:fileDialog");
        try {
            String errorMsg = FileUtils.veryFile(file.getInputstream(),file.getContentType(), file.getFileName(), "2");
            if (StringUtils.isNotBlank(errorMsg)) {
                JsfUtil.addErrorMessage(errorMsg);
                return;
            }
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            Integer dotIndex = file.getFileName().lastIndexOf(".");
            String filePath = JsfUtil.getAbsolutePath() + "/files/notice/" + uuid + file.getFileName().substring(dotIndex);
            String showDir = "/files/notice/" + uuid + file.getFileName().substring(dotIndex);
            try {
                FileUtils.copyFile(filePath, file.getInputstream());
            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage(PropertyUtils.getValue("上传失败"));
                return;
            }
            this.tsNotice.setFilePath(showDir);
            RequestContext.getCurrentInstance().execute("PF('FileDialog').hide();");
            JsfUtil.addSuccessMessage("上传成功！");
        }catch (IOException e){
            JsfUtil.addErrorMessage("不支持的文件格式，请上传PDF！");
            e.printStackTrace();
        }

    }

    /**
    * @Description : 删除附件
    * @MethodAuthor: anjing
    * @Date : 2020/4/8 16:17
    **/
    public void  delAnnex(){
        try {
            this.tsNotice.setFilePath(null);
            this.tsNoticeService.upsertEntity(this.tsNotice);
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("删除失败！");
            e.printStackTrace();
        }
    }

    public Date getSearchStartDate() {
        return searchStartDate;
    }

    public void setSearchStartDate(Date searchStartDate) {
        this.searchStartDate = searchStartDate;
    }

    public Date getSearchEndDate() {
        return searchEndDate;
    }

    public void setSearchEndDate(Date searchEndDate) {
        this.searchEndDate = searchEndDate;
    }

    public String getSearchNoticTitle() {
        return searchNoticTitle;
    }

    public void setSearchNoticTitle(String searchNoticTitle) {
        this.searchNoticTitle = searchNoticTitle;
    }

    public String[] getSearchIfPublish() {
        return searchIfPublish;
    }

    public void setSearchIfPublish(String[] searchIfPublish) {
        this.searchIfPublish = searchIfPublish;
    }

    public String[] getSearchStateMark() {
        return searchStateMark;
    }

    public void setSearchStateMark(String[] searchStateMark) {
        this.searchStateMark = searchStateMark;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TsNotice getTsNotice() {
        return tsNotice;
    }

    public void setTsNotice(TsNotice tsNotice) {
        this.tsNotice = tsNotice;
    }
}
