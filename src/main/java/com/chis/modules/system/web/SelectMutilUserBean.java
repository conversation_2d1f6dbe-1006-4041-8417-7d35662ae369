package com.chis.modules.system.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;
import org.primefaces.model.DualListModel;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 *  * <p>类描述：</p>
 * 人员选择（多选） 按地区、单位、科室筛选所有人员
 * @ClassAuthor xq,2018年1月15日,SelectMutilUserBean
 * 返回的结果key-selectUserList value-Collection<TsUserInfo> height:500 width:920
 * contentWidth:870
 */
@ManagedBean(name = "selectMutilUserBean")
@ViewScoped
public class SelectMutilUserBean extends FacesBean {

	private static final long serialVersionUID = 2350275983339647922L;

	/** 标题 */
	private String title = "人员选择";
	/** 已选用户IDS */
	private String selectIds;

	/** 用于缓存初始化的已选用户 */
	private List<TsUserInfo> storeTargetList;
	/** 是否是超管 */
	private boolean ifAdmin = Boolean.FALSE;
	/** 用户分配：地区名称 */
	private String userZoneName;
	/** 用户分配：地区编码 */
	private String userZoneCode;
	/** 用户分配：地区级别 */
	private String userZoneLevel;
	/** 用户分配：单位id */
	private Integer userUnitId;
	/** 用户分配：单位下啦 */
	private Map<String, Integer> unitMap = new HashMap<String, Integer>(0);
	/** 能看到的地区集合 */
	private List<TsZone> zoneList = new ArrayList<TsZone>(0);
	/** 用于机构授权的选择 */
	private DualListModel userDualListModel = new DualListModel();
	/** ++人员科室 */
	private Map<Integer, String> offoceMap;
	private Integer userUnitOfficeId;
	private Map<String, Integer> unitOfficeMap;

	/** ejb session bean */
	private SystemModuleServiceImpl service = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);

	public SelectMutilUserBean() {
		this.init();
	}

	/**
	 * 初始化
	 */
	public void init() {
				
		String admin = JsfUtil.getRequest().getParameter("ifAdmin");
		if("1".equals(admin)){
            ifAdmin = Boolean.TRUE;
        }
		String s1 = JsfUtil.getRequest().getParameter("title");
		if (StringUtils.isNotBlank(s1)) {
			this.title = s1;
		}
		this.selectIds = JsfUtil.getRequest().getParameter("selectIds");

		this.userUnitId = null;

		TsUserInfo tsUserInfo = sessionData.getUser();

		/**
		 * 地区初始化
		 */
		if (null == this.zoneList || this.zoneList.size() <= 0) {
			this.zoneList = this.commService.findZoneList(this.ifAdmin, tsUserInfo.getTsUnit().getTsZone().getZoneGb(), null, "5");
		}

		this.userZoneCode = tsUserInfo.getTsUnit().getTsZone().getZoneGb();
		this.userZoneName = tsUserInfo.getTsUnit().getTsZone().getZoneName();
		this.userZoneLevel = tsUserInfo.getTsUnit().getTsZone().getZoneType().toString();

		// 初始化单位
		if (ifAdmin) {
			this.unitMap = this.filterUnit(this.userZoneCode, this.userZoneLevel);
		} else {
			this.unitMap = new HashMap<String, Integer>();
			this.unitMap.put(tsUserInfo.getTsUnit().getUnitname(), tsUserInfo.getTsUnit().getRid());
		}
		this.userUnitId = sessionData.getUser().getTsUnit().getRid();

		// 初始化用户
		if (StringUtils.isNotBlank(selectIds)) {
			this.storeTargetList = this.service.findUsersByIds(this.ifAdmin, tsUserInfo.getTsUnit().getTsZone().getZoneGb(), tsUserInfo
					.getTsUnit().getTsZone().getZoneType(), this.selectIds, tsUserInfo.getTsUnit().getRid());
		} else {
			this.storeTargetList = new ArrayList<TsUserInfo>(0);
		}
		this.userDualListModel = new DualListModel(new ArrayList<TsUserInfo>(0), this.storeTargetList);
		this.userDualListModel.setSource(this.service.findUserByUnitId(this.userUnitId, null, userDualListModel.getTarget()));

		// 科室
		userUnitOfficeId = null;
		unitOfficeMap = new LinkedHashMap<String, Integer>();
		List<TsOffice> list = service.findOfficeByUnitId(userUnitId);
		if (list != null && list.size() > 0) {
			for (TsOffice t : list) {
				unitOfficeMap.put(t.getOfficename(), t.getRid());
			}
		}

		// 单位人员科室
		offoceMap = service.findUserOffices(userUnitId, null);
	}

	/**
	 * 根据地区刷单位
	 * 
	 * @param zoneCode
	 *            地区编码
	 * @param zoneType
	 *            地区级别
	 * @return 单位集合
	 */
	private Map<String, Integer> filterUnit(String zoneCode, String zoneType) {
		TsUserInfo user = this.sessionData.getUser();
		Map<String, Integer> map = new LinkedHashMap<String, Integer>();
		List<TsUnit> list = service.findUnitByZoneId(this.ifAdmin, user.getTsUnit().getTsZone().getZoneGb(), user.getTsUnit().getRid(),
				zoneCode, zoneType);
		if (null != list && list.size() > 0) {
			for (TsUnit t : list) {
				map.put(t.getUnitname(), t.getRid());
			}
		}
		return map;
	}

	public String getOfficeName(Integer userId) {
		return offoceMap.get(userId);
	}

	/**
	 * 地区选中事件
	 */
	public void onNodeSelect() {
		this.userUnitId = null;
		this.unitMap = this.filterUnit(this.userZoneCode, this.userZoneLevel);
		this.userDualListModel.setSource(new ArrayList<TsUserInfo>(0));
	}

	/**
	 * 单位下啦change事件
	 */
	public void onUnitChange() {
		if (null != this.userUnitId) {
			List<TsUserInfo> targetList = this.userDualListModel.getTarget();
			List<TsUserInfo> sourceList = this.service.findUserByUnitId(this.userUnitId, null, userDualListModel.getTarget());
			this.userDualListModel.setSource(sourceList);
		} else {
			this.userDualListModel.setSource(new ArrayList<TsUserInfo>(0));
		}
		userUnitOfficeId = null;
		unitOfficeMap = new LinkedHashMap<String, Integer>();
		if (userUnitId != null) {
			offoceMap = service.findUserOffices(userUnitId, null);
			List<TsOffice> list = service.findOfficeByUnitId(userUnitId);
			if (list != null && list.size() > 0) {
				for (TsOffice t : list) {
					unitOfficeMap.put(t.getOfficename(), t.getRid());
				}
			}
		}

	}

	public void onofficeChange() {
		List<TsUserInfo> targetList = this.userDualListModel.getTarget();
		List<TsUserInfo> sourceList;
		if (userUnitOfficeId != null) {
			sourceList = this.service.findUserByUnitId(this.userUnitId, this.userUnitOfficeId, userDualListModel.getTarget());
		} else {
			sourceList = this.service.findUserByUnitId(this.userUnitId, null, userDualListModel.getTarget());
		}
		this.userDualListModel.setSource(sourceList);

	}

	/**
	 * 选择确定方法
	 */
	public void selectAction() {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("selectUserList", userDualListModel.getTarget());
		RequestContext.getCurrentInstance().closeDialog(map);
	}

	/**
	 * 关闭
	 */
	public void dialogClose() {
		RequestContext.getCurrentInstance().closeDialog(null);
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public String getUserZoneName() {
		return userZoneName;
	}

	public void setUserZoneName(String userZoneName) {
		this.userZoneName = userZoneName;
	}

	public String getUserZoneCode() {
		return userZoneCode;
	}

	public void setUserZoneCode(String userZoneCode) {
		this.userZoneCode = userZoneCode;
	}

	public String getUserZoneLevel() {
		return userZoneLevel;
	}

	public void setUserZoneLevel(String userZoneLevel) {
		this.userZoneLevel = userZoneLevel;
	}

	public Integer getUserUnitId() {
		return userUnitId;
	}

	public void setUserUnitId(Integer userUnitId) {
		this.userUnitId = userUnitId;
	}

	public Map<String, Integer> getUnitMap() {
		return unitMap;
	}

	public void setUnitMap(Map<String, Integer> unitMap) {
		this.unitMap = unitMap;
	}

	public DualListModel getUserDualListModel() {
		return userDualListModel;
	}

	public void setUserDualListModel(DualListModel userDualListModel) {
		this.userDualListModel = userDualListModel;
	}

	public Map<Integer, String> getOffoceMap() {
		return offoceMap;
	}

	public void setOffoceMap(Map<Integer, String> offoceMap) {
		this.offoceMap = offoceMap;
	}

	public Integer getUserUnitOfficeId() {
		return userUnitOfficeId;
	}

	public void setUserUnitOfficeId(Integer userUnitOfficeId) {
		this.userUnitOfficeId = userUnitOfficeId;
	}

	public Map<String, Integer> getUnitOfficeMap() {
		return unitOfficeMap;
	}

	public void setUnitOfficeMap(Map<String, Integer> unitOfficeMap) {
		this.unitOfficeMap = unitOfficeMap;
	}

}
