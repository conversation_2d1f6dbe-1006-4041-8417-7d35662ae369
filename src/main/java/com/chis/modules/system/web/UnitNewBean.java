package com.chis.modules.system.web;

import java.math.BigDecimal;
import java.util.*;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import cn.hutool.core.util.ObjectUtil;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import com.chis.common.utils.BaiduMapUtil;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.entity.TsBsSort;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsSystemParam;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUnitAttr;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * 单位管理
 *
 * @editContent 删除buildProcessData()方法，如果查询出来的数据集需要修改，子类不需要再重写buildProcessData()  david 2014-09-04
 *
 * <AUTHOR>
 */
@ManagedBean(name="unitNewBean")
@ViewScoped
public class UnitNewBean extends FacesEditBean{

    private static final long serialVersionUID = -738445454536625063L;
    /**存在session中的对象*/
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    /**ejb session bean*/
    private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    /**单位对象*/
    private TsUnit tsUnit = new TsUnit();
    /**单位对象rid*/
    private Integer rid;
    /**查询条件：地区名称*/
    private String searchZoneName;
    /**查询条件：地区编码*/
    private String searchZoneCode;
    /**查询条件：单位名称*/
    private String searchUnitname;
    /**查询条件：是否分支机构*/
    private List<Short> searchSubOrges;
    /**查询条件：状态*/
    private List<Short> searchStates;
    /**添加页面：单位属性下啦*/
    private Map<String, Integer> bsSortMap = new LinkedHashMap<>(0);
    /**查询条件：单位属性*/
    private Integer searchSortId;

    //社会信用代码
    private String searchCreditCode;
    /**添加页面选中的属性*/
    private List<String> selectSortList = new ArrayList<String>(0);
    /**添加页面的地区名称*/
    private String editZoneName;
    /**添加页面的地区id*/
    private Integer editZoneId;
    /**添加页面的业务地区名称*/
    private String editBsZoneName;
    /**添加页面的业务地区id*/
    private Integer editBsZoneId;
    /**是否是超管*/
    private boolean ifAdmin = Boolean.TRUE;
    /**行政能看到的地区集合*/
    private List<TsZone> zoneList;
    /**业务能看到的地区集合*/
    private List<TsZone> zoneBsList;
    /**当前最高地区级别*/
    private Short currentMaxZoneType;
    /** 纬度 */
    private BigDecimal degreeLat;
    private BigDecimal minuteLat;
    private BigDecimal secondLat;
    /** 经度 */
    private BigDecimal degreeLong;
    private BigDecimal minuteLong;
    private BigDecimal secondLong;

    private String message;

    /** 职务列表 */
    private List<String> careerList = new ArrayList<>();

    /**是否配置分支机构*/
    private String ifSubOrg;
    /**删除权限*/
    private boolean ifDel;
    /**机构性质*/
    private List<TsSimpleCode> unitTypeList;

    @PostConstruct
    public void init() {
        super.ifSQL = Boolean.TRUE;
        TsUserInfo tsUserInfo = this.sessionData.getUser();
        if(!tsUserInfo.getUserNo().equals(Constants.ADMIN)) {
            ifAdmin = Boolean.FALSE;
        }
        if (Global.getBtnSet().contains("dwgl_del")) {
            this.ifDel = true;
        }else {
            this.ifDel = false;
        }
        this.initSearchCondition();

        //初始化职业列表
        initCareerList();
        //机构性质
        this.unitTypeList = this.commService.findLevelSimpleCodesByTypeId("5587");

        this.searchAction();
    }


    /**
     * 初始化职业列表
     */
    private void initCareerList() {
        List<TsSimpleCode> list = commService.findSimpleCodesByTypeId("2002");
        if (list != null && list.size() > 0) {
            for (TsSimpleCode t : list) {
                careerList.add(t.getCodeName());
            }
        }
    }

    /**
     * 获取地区,地区是全国时当前省份放在最上面
     * @param tsZone 地区
     * @return 地区列表
     */
    private List<TsZone> findZoneList(TsZone tsZone) {
        List<TsZone> tsZoneList;
        if (this.ifAdmin || (tsZone != null && "000000000000".equals(tsZone.getZoneCode()))) {
            tsZoneList = this.systemModuleService
                    .findZoneListWithAllZoneAndEntireCountry(tsZone.getZoneCode().substring(0, 2));
        } else if (tsZone != null){
            tsZoneList = this.commService.findZoneListCache(this.ifAdmin, tsZone.getZoneCode(), null, "6");
        } else {
            return new ArrayList<>();
        }
        return tsZoneList;
    }

    /**
     * 初始化查询条件
     */
    private void initSearchCondition() {
        TsUserInfo user = this.sessionData.getUser();
        if (this.searchSubOrges == null) {
            this.searchSubOrges = new ArrayList<>();
        }
        if (this.searchStates == null) {
            this.searchStates = new ArrayList<>();
            this.searchStates.add((short) 1);
        }
        if (user.getTsUnit().getFkByManagedZoneId() != null) {
            if (StringUtils.isBlank(this.searchZoneCode) || StringUtils.isBlank(this.searchZoneName)) {
                this.searchZoneCode = user.getTsUnit().getFkByManagedZoneId().getZoneCode();
                this.searchZoneName = user.getTsUnit().getFkByManagedZoneId().getZoneName();
            }
        }

        //地区初始化
        if (CollectionUtils.isEmpty(this.zoneBsList)) {
            this.zoneBsList = findZoneList(user.getTsUnit().getFkByManagedZoneId());
        }

        if (CollectionUtils.isEmpty(this.zoneList)) {
            this.zoneList = findZoneList(user.getTsUnit().getTsZone());
        }

        if (this.bsSortMap == null || this.bsSortMap.size() == 0) {
            List<TsBsSort> sortList = this.commService.findSortsByApp();
            if (null != sortList && sortList.size() > 0) {
                for (TsBsSort t : sortList) {
                    this.bsSortMap.put(t.getSortName(), t.getRid());
                }
            }
        }

        if (StringUtils.isBlank(this.ifSubOrg)) {
            List<TsSystemParam> list = this.systemModuleService.findParamValue("IF_SUB_ORG");//是否配置分支机构
            if (!CollectionUtils.isEmpty(list)) {
                this.ifSubOrg = list.get(0).getParamValue();
            }
        }
    }

    @Override
    public void addInit() {
        TsUserInfo user = sessionData.getUser();
        //初始化一些条件
        this.tsUnit = new TsUnit();
        this.tsUnit.setIfReveal((short)1);
        this.tsUnit.setCreateDate(new Date());
        this.tsUnit.setCreateManid(this.sessionData.getUser().getRid());
        this.tsUnit.setIfSubOrg("0");
        this.rid = null;
        this.selectSortList = new ArrayList<String>(0);

        this.zoneBsList = findZoneList(user.getTsUnit().getFkByManagedZoneId());

        this.zoneList = findZoneList(user.getTsUnit().getTsZone());

        this.editZoneId = user.getTsUnit().getFkByManagedZoneId().getRid();
        this.editZoneName = user.getTsUnit().getFkByManagedZoneId().getZoneName();

        this.editBsZoneId = user.getTsUnit().getFkByManagedZoneId().getRid();
        this.editBsZoneName = user.getTsUnit().getFkByManagedZoneId().getZoneName();
    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {
        TsUserInfo user = sessionData.getUser();
        this.tsUnit = this.systemModuleService.findUnitWithSort(this.rid);
        this.tsUnit.setModifyDate(new Date());
        this.tsUnit.setModifyManid(this.sessionData.getUser().getRid());
        if (ObjectUtil.isNotEmpty(this.tsUnit.getUnitTypeId())) {
            this.tsUnit.setUnitTypeRid(this.tsUnit.getUnitTypeId().getRid());
        }
        this.selectSortList = new ArrayList<String>(0);

        this.zoneBsList = findZoneList(user.getTsUnit().getFkByManagedZoneId());

        this.zoneList = findZoneList(user.getTsUnit().getTsZone());

        Set<TsUnitAttr> attrSet = this.tsUnit.getTsUnitAttrs();
        if(null != attrSet && attrSet.size() > 0) {
            for(TsUnitAttr attr: attrSet) {
                this.selectSortList.add(attr.getTsBsSort().getRid().toString());
            }
        }

        this.editZoneId = this.tsUnit.getTsZone().getRid();
        this.editZoneName = this.tsUnit.getTsZone().getZoneName();

        this.editBsZoneId = this.tsUnit.getFkByManagedZoneId().getRid();
        this.editBsZoneName = this.tsUnit.getFkByManagedZoneId().getZoneName();
    }

    /***
     *  <p>方法描述：</p>
     *
     * @MethodAuthor maox,2020年1月6日,copyZone
     */
    public void copyZone(){
        this.editBsZoneId = this.editZoneId;
        this.editBsZoneName = this.editZoneName;
    }

    /**
     * 保存
     */
    @Override
    public void saveAction() {
        if (ObjectUtil.isNotEmpty(this.tsUnit.getUnitTypeRid())) {
            this.tsUnit.setUnitTypeId(new TsSimpleCode(this.tsUnit.getUnitTypeRid()));
        } else {
            this.tsUnit.setUnitTypeId(null);
        }
        TsZone xzZone = systemModuleService.findZone(this.editZoneId);
        TsZone bsZone = systemModuleService.findZone(this.editBsZoneId);

        this.tsUnit.setTsZone(xzZone);
        this.tsUnit.setFkByManagedZoneId(bsZone);
        if(null != this.tsUnit.getRid()) {
            this.systemModuleService.saveOrUpdateUnit(this.tsUnit, this.selectSortList);
        }else {
            this.systemModuleService.saveOrUpdateUnit(this.tsUnit, this.selectSortList);
        }
        this.searchAction();
        JsfUtil.addSuccessMessage("保存成功！");
        this.backAction();
    }



    public void BeforSaveAction(){
        TsZone xzZone = systemModuleService.findZone(this.editZoneId);
        TsZone bsZone = systemModuleService.findZone(this.editBsZoneId);

        boolean flag = false;
        if(!tsUnit.getOrgTel().matches(Constants.PHONE_MOBILE_REGEX)) {
            JsfUtil.addErrorMessage("请输入正确的联系人电话！");
            flag = true;
        }
        if(xzZone.getRealZoneType() != null && xzZone.getRealZoneType()<4){
            JsfUtil.addErrorMessage("行政区划所属地区必须选择到区县及以下！");
            flag = true;
        }else if(xzZone.getRealZoneType()==null){
            JsfUtil.addErrorMessage("请维护真实地区级别！");
            flag = true;
        }
        if(StringUtils.isBlank(tsUnit.getUnitname())){
            JsfUtil.addErrorMessage("单位名称不能为空！");
            flag = true;
        }
        if(StringUtils.isBlank(tsUnit.getUnitSimpname())){
            JsfUtil.addErrorMessage("单位简称不能为空！");
            flag = true;
        }
        if(StringUtils.isBlank(tsUnit.getCreditCode())){
            JsfUtil.addErrorMessage("社会信用代码不能为空！");
            flag = true;
        }else if(!StringUtils.isCreditCode(tsUnit.getCreditCode())){
            JsfUtil.addErrorMessage("社会信用代码不符合规范！");
            return;
        }else{
            if ("1".equals(this.ifSubOrg)) {//配置分支机构
                if (StringUtils.isBlank(this.tsUnit.getIfSubOrg())) {
                    JsfUtil.addErrorMessage("是否分支机构不能为空！");
                    flag = true;
                }else {
                    if ("1".equals(this.tsUnit.getIfSubOrg())) {//是分支机构
                        //主体机构必存在
                        Integer count = systemModuleService.findUnitIfExists(tsUnit.getCreditCode(), tsUnit.getRid(), null,"0");
                        if (null==count || count.intValue()==0) {
                            JsfUtil.addErrorMessage("同社会信用代码的主体机构不存在！");
                            flag = true;
                        }
                        //社会信用代码和单位名称联合唯一
                        if (StringUtils.isNotBlank(tsUnit.getUnitname())) {
                            Integer subCount = systemModuleService.findUnitIfExists(tsUnit.getCreditCode(), tsUnit.getRid(), tsUnit.getUnitname(),null);
                            if (null!=subCount && subCount.intValue()>0) {
                                JsfUtil.addErrorMessage("社会信用代码和单位名称已存在！");
                                flag = true;
                            }
                        }
                    }else {//主体机构
                        //社会信用代码唯一
                        Integer count = systemModuleService.findUnitIfExists(tsUnit.getCreditCode(), tsUnit.getRid(), null,"0");
                        if (null!=count && count.intValue()>0) {
                            JsfUtil.addErrorMessage("社会信用代码已存在！");
                            flag = true;
                        }
                    }
                }
            }else {
                //社会信用代码唯一
                Integer count = systemModuleService.findUnitIfExists(tsUnit.getCreditCode(), tsUnit.getRid(), null,null);
                if (null!=count && count.intValue()>0) {
                    JsfUtil.addErrorMessage("社会信用代码已存在！");
                    flag = true;
                }
            }
        }
        if(null ==selectSortList || selectSortList.size() ==0){
            JsfUtil.addErrorMessage("单位属性不能为空！");
            flag = true;
        }
        if(flag){
            return;
        }

        if(!ZoneUtil.zoneSelect(xzZone.getZoneGb()).contains(ZoneUtil.zoneSelect(bsZone.getZoneGb())) &&  !ZoneUtil.zoneSelect(bsZone.getZoneGb()).contains(ZoneUtil.zoneSelect(xzZone.getZoneGb()))){
            message = this.editZoneName+"不在"+this.editBsZoneName+"管辖范围内，确定保存吗？";
            RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show()");
        }else{
            saveAction();
        }
    }

    public void changeX(){

        if(StringUtils.isNotBlank(tsUnit.getLng())&&tsUnit.getLng().contains(",")){
            String[] arr1 = tsUnit.getLng().split(",");
            tsUnit.setLng(arr1[0]);
            tsUnit.setLat(arr1[1]);
        }

        if(StringUtils.isNotBlank(tsUnit.getLat())&&tsUnit.getLat().contains(",")){
            String[] arr1 = tsUnit.getLat().split(",");
            tsUnit.setLng(arr1[0]);
            tsUnit.setLat(arr1[1]);
        }
    }

    /**
     * 删除
     */
    public void deleteAction() {
        String msg = this.systemModuleService.deleteUnit(this.rid);
        if(StringUtils.isNotBlank(msg)) {
            JsfUtil.addErrorMessage(msg);
            return;
        }
        JsfUtil.addSuccessMessage("删除成功！");
        this.searchAction();
    }

    /**
     * 停用
     */
    public void stopAction() {
        this.systemModuleService.updateUnitState0(this.rid);
        this.searchAction();
    }

    /**
     * 启用
     */
    public void startAction() {
        this.systemModuleService.updateUnitState(this.rid, 1);
        this.searchAction();
    }




    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder();

        sb.append("          from ts_unit t ");
        sb.append("         left join TS_UNIT_ATTR t1 on t1.UNIT_RID = t.rid ");
        sb.append("         left join TS_BS_SORT t2 on t1.attr_id = t2.rid ");
        sb.append("         left join ts_zone t3 on t.zone_id = t3.rid ");
        sb.append("         left join ts_zone t4 on t.manage_zone_id  =t4.rid ");
        sb.append("  where 1 =1");
        if(StringUtils.isNotBlank(this.searchUnitname)) {
            sb.append(" and t.unitname like :unitname ");
            this.paramMap.put("unitname", "%" + this.searchUnitname.trim() + "%");
        }
        if(StringUtils.isNotBlank(this.searchCreditCode)) {
            sb.append(" and t.CREDIT_CODE like :CREDIT_CODE ");
            this.paramMap.put("CREDIT_CODE", "%" + this.searchCreditCode.trim() + "%");
        }
        if(null != this.searchSortId) {
            sb.append(" and t2.rid =").append(this.searchSortId);
        }
        if (null != this.searchStates && this.searchStates.size() > 0) {
            sb.append(" and t.IF_REVEAL in (").append(StringUtils.join(this.searchStates, ",")).append(")");
        }
        if (null != this.searchSubOrges && this.searchSubOrges.size() > 0) {
            sb.append(" and t.IF_SUB_ORG in (").append(StringUtils.join(this.searchSubOrges, ",")).append(")");
        }
        sb.append(" and t3.zone_gb like '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%'");


        StringBuilder searchSql = new StringBuilder();
        searchSql.append("select ");
        searchSql.append("  t.rid, ");
        searchSql.append("  case when t3.zone_type>2 then substr(t3.full_name,instr(t3.full_name,'_')+1) else t3.zone_name end, ");
        searchSql.append("  case when t4.zone_type>2 then substr(t4.full_name,instr(t4.full_name,'_')+1) else t4.zone_name end, ");
        searchSql.append("  t.unitname, t.credit_code, listagg(t2.sort_name,'，') within group (order by t2.LEVEL_CODE), ");
        searchSql.append("  t.if_reveal, t.IF_SUB_ORG ").append(sb);
        searchSql.append("  group by t.rid,t3.full_name,t4.full_name,t.unitname,t3.zone_gb, ");
        searchSql.append("      t4.zone_gb,t.credit_code,t.if_reveal,t.IF_SUB_ORG,t3.zone_type,t4.zone_type,t3.zone_name,t4.zone_name,t.unit_Code");
        searchSql.append("  order by t3.zone_gb,t4.zone_gb,t.unit_Code");

        StringBuilder countSql = new StringBuilder("SELECT COUNT(distinct t.RID) ").append(sb);

        return new String[]{searchSql.toString(),countSql.toString()};
    }

    /**
     * 查询操作
     */
    public void searchInit(){
        this.initSearchCondition();
        this.searchAction();
    }

    /** 转换经纬度 */
    public void convertLatOrLon() {
        // 纬度
        double a = this.minuteLat.divide(new BigDecimal(Double.valueOf(60)), 6, BigDecimal.ROUND_HALF_UP).doubleValue();
        double b = this.secondLat.divide(new BigDecimal(Double.valueOf(3600)), 6, BigDecimal.ROUND_HALF_UP).doubleValue();
        // 经度
        double c = this.minuteLong.divide(new BigDecimal(60), 6, BigDecimal.ROUND_HALF_UP).doubleValue();
        double d = this.secondLong.divide(new BigDecimal(3600), 6, BigDecimal.ROUND_HALF_UP).doubleValue();
        // 转换成百度坐标
        double yy = this.degreeLat.add(new BigDecimal(a)).add(new BigDecimal(b)).doubleValue();
        double xx = this.degreeLong.add(new BigDecimal(c)).add(new BigDecimal(d)).doubleValue();
        String value = BaiduMapUtil.findTransBaiduPoints(xx+"", yy+"");
        if (StringUtils.isBlank(value)){
            JsfUtil.addErrorMessage("坐标转换失败！");
            return ;
        }
        String lng = new BigDecimal(value.split("#@#")[0]).setScale(6, BigDecimal.ROUND_HALF_UP).toString();
        String lat = new BigDecimal(value.split("#@#")[1]).setScale(6, BigDecimal.ROUND_HALF_UP).toString();
        this.tsUnit.setLng(lng);
        this.tsUnit.setLat(lat);
        degreeLat = null;
        minuteLat = null;
        secondLat = null;
        degreeLong = null;
        minuteLong = null;
        secondLong = null;
        RequestContext.getCurrentInstance().execute("PF('GpsDialog').hide()");
    }

    //getters and setters


    public TsUnit getTsUnit() {
        return tsUnit;
    }

    public void setTsUnit(TsUnit tsUnit) {
        this.tsUnit = tsUnit;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchUnitname() {
        return searchUnitname;
    }

    public void setSearchUnitname(String searchUnitname) {
        this.searchUnitname = searchUnitname;
    }

    public Map<String, Integer> getBsSortMap() {
        return bsSortMap;
    }

    public void setBsSortMap(Map<String, Integer> bsSortMap) {
        this.bsSortMap = bsSortMap;
    }

    public Integer getSearchSortId() {
        return searchSortId;
    }

    public void setSearchSortId(Integer searchSortId) {
        this.searchSortId = searchSortId;
    }

    public List<String> getSelectSortList() {
        return selectSortList;
    }

    public void setSelectSortList(List<String> selectSortList) {
        this.selectSortList = selectSortList;
    }

    public String getEditZoneName() {
        return editZoneName;
    }

    public void setEditZoneName(String editZoneName) {
        this.editZoneName = editZoneName;
    }

    public Integer getEditZoneId() {
        return editZoneId;
    }

    public void setEditZoneId(Integer editZoneId) {
        this.editZoneId = editZoneId;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public Short getCurrentMaxZoneType() {
        return currentMaxZoneType;
    }

    public BigDecimal getDegreeLat() {
        return degreeLat;
    }

    public void setDegreeLat(BigDecimal degreeLat) {
        this.degreeLat = degreeLat;
    }

    public BigDecimal getMinuteLat() {
        return minuteLat;
    }

    public void setMinuteLat(BigDecimal minuteLat) {
        this.minuteLat = minuteLat;
    }

    public BigDecimal getSecondLat() {
        return secondLat;
    }

    public void setSecondLat(BigDecimal secondLat) {
        this.secondLat = secondLat;
    }

    public BigDecimal getDegreeLong() {
        return degreeLong;
    }

    public void setDegreeLong(BigDecimal degreeLong) {
        this.degreeLong = degreeLong;
    }

    public BigDecimal getMinuteLong() {
        return minuteLong;
    }

    public void setMinuteLong(BigDecimal minuteLong) {
        this.minuteLong = minuteLong;
    }

    public BigDecimal getSecondLong() {
        return secondLong;
    }

    public void setSecondLong(BigDecimal secondLong) {
        this.secondLong = secondLong;
    }

    public String getSearchCreditCode() {
        return searchCreditCode;
    }

    public void setSearchCreditCode(String searchCreditCode) {
        this.searchCreditCode = searchCreditCode;
    }

    public List<TsZone> getZoneBsList() {
        return zoneBsList;
    }

    public void setZoneBsList(List<TsZone> zoneBsList) {
        this.zoneBsList = zoneBsList;
    }

    public String getEditBsZoneName() {
        return editBsZoneName;
    }

    public void setEditBsZoneName(String editBsZoneName) {
        this.editBsZoneName = editBsZoneName;
    }

    public Integer getEditBsZoneId() {
        return editBsZoneId;
    }

    public void setEditBsZoneId(Integer editBsZoneId) {
        this.editBsZoneId = editBsZoneId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<String> getCareerList() {
        return careerList;
    }

    public void setCareerList(List<String> careerList) {
        this.careerList = careerList;
    }


    public String getIfSubOrg() {
        return ifSubOrg;
    }


    public void setIfSubOrg(String ifSubOrg) {
        this.ifSubOrg = ifSubOrg;
    }

    public List<Short> getSearchSubOrges() {
        return searchSubOrges;
    }

    public void setSearchSubOrges(List<Short> searchSubOrges) {
        this.searchSubOrges = searchSubOrges;
    }

    public List<Short> getSearchStates() {
        return searchStates;
    }

    public void setSearchStates(List<Short> searchStates) {
        this.searchStates = searchStates;
    }

    public boolean isIfDel() {
        return ifDel;
    }

    public void setIfDel(boolean ifDel) {
        this.ifDel = ifDel;
    }

    public List<TsSimpleCode> getUnitTypeList() {
        return unitTypeList;
    }

    public void setUnitTypeList(List<TsSimpleCode> unitTypeList) {
        this.unitTypeList = unitTypeList;
    }
}
