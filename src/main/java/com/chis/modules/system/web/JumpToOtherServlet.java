package com.chis.modules.system.web;

import com.chis.common.utils.CacheUtils;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsUserRole;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.SessionData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * @Description: 重定向到第三方平台的Servlet
 * 菜单配置访问这个Servlet 但要注意菜单的IF_POP需要配置成1
 * reqUrl参数为第三方平台的接口地址 必填
 * reqParamName参数为第三方平台参数名称 可空 如果为空 默认ticket
 * @ClassAuthor pw,2022年03月31日,JumpToOtherServlet
 */
@WebServlet(name="JumpToOtherServlet",value="/jumpToOtherServer")
public class JumpToOtherServlet extends HttpServlet {
    private static final long serialVersionUID = 1457172183040979455L;
    private static final Logger logger = LoggerFactory.getLogger(JumpToOtherServlet.class);
    @Override
    public void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        this.doPost(req, resp);
    }

    @Override
    public void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        String cacheName = "OTHER_SYSTEM_JUMP";
        String uuid = StringUtils.uuid();
        String reqUrl = request.getParameter("reqUrl");
        if(StringUtils.isBlank(reqUrl)){
            //错误信息如果中文 会乱码
            String msg = "reqUrl param is required!";
            response.sendRedirect("/error.faces?msg="+msg);
            return;
        }
        //处理问号后传值
        StringBuilder parameters = new StringBuilder();
        if (reqUrl.contains("@@")) {
            List<String> parameterList = StringUtils.string2list(reqUrl, "@@");
            if (CollectionUtils.isEmpty(parameterList)) {
                return;
            }
            reqUrl = parameterList.get(0);
            for (int i = 1; i < parameterList.size(); i++) {
                parameters.append("&").append(parameterList.get(i));
            }
        }
        String ticket = request.getParameter("reqParamName");
        if(StringUtils.isBlank(ticket)){
            ticket = "ticket";
        }
        SessionData data = (SessionData)request.getSession().getAttribute(SessionData.SESSION_DATA);

        Map<String, Object> map = new HashMap<String, Object>();
        if(null != data){
            TsUserInfo userInfo = data.getUser();
            if(null != userInfo){
                map.put("userNo", userInfo.getUserNo());
                map.put("password", userInfo.getPassword());
                map.put("userName", userInfo.getUsername());
                map.put("mbNum", userInfo.getMbNum());
                map.put("idc", userInfo.getIdc());
                TsUnit tsUnit = userInfo.getTsUnit();
                if(null != tsUnit){
                    map.put("unitName", tsUnit.getUnitname());
                    map.put("creditCode", tsUnit.getCreditCode());
                    map.put("ifSubOrg", tsUnit.getIfSubOrg());
                    TsZone tsZone = tsUnit.getFkByManagedZoneId();
                    if(null != tsZone){
                        //管辖地区
                        map.put("zoneCode", tsZone.getZoneGb());
                        map.put("zoneName", tsZone.getZoneName());
                    }
                    tsZone = tsUnit.getTsZone();
                    if(null != tsZone){
                        //行政地区
                        map.put("zoneCode1", tsZone.getZoneGb());
                        map.put("zoneName1", tsZone.getZoneName());
                    }
                }
                List<Map<String, Object>> roleList = new ArrayList<>();
                Set<String> roleCodeSet = new HashSet<>();
                List<TsUserRole> userRoles = userInfo.getTsUserRoles();
                if(!CollectionUtils.isEmpty(userRoles)){
                    for(TsUserRole userRole : userRoles){
                        String roleCode = null == userRole.getTsRole() ? null : userRole.getTsRole().getRoleCode();
                        if(StringUtils.isNotBlank(roleCode) && !roleCodeSet.contains(roleCode)){
                            Map<String, Object> roleCodeMap = new HashMap<>();
                            roleCodeMap.put("roleCode", roleCode);
                            //权限编码
                            roleList.add(roleCodeMap);
                            roleCodeSet.add(roleCode);
                        }
                    }
                }
                map.put("roleList", roleList);
                map.put("type", "00");
                map.put("mess", "成功");
                CacheUtils.put(cacheName, uuid, map);
            }
        }
        try{
            System.out.println("第三方地址："+reqUrl+" 请求ticket："+uuid);
            logger.info("第三方地址：{} 请求ticket：{}",reqUrl, uuid);
            response.sendRedirect(reqUrl + "?" + ticket + "=" + uuid + (parameters.length()>0 ? parameters : ""));
        }catch(Exception e){
            e.printStackTrace();
        }
    }
}
