package com.chis.modules.system.web;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.UploadedFile;

import com.chis.common.utils.FileUtils;
import com.chis.common.utils.GB2Alpha;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.entity.TsDsfSys;
import com.chis.modules.system.entity.TsDsfSysParam;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.DefaultLazyDataModel;

/**
 * 计免单位对照表
 * 
 * <AUTHOR>
 * 
 * @修改人 ds
 * @创建时间 2016-3-17
 * 
 */
@ManagedBean(name = "sysBean")
@ViewScoped
public class SysLoginBean extends FacesEditBean implements Serializable {

	private static final long serialVersionUID = 1L;

	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	/** ejb session bean */
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);

	/** 查询条件：系统名称 */
	private String searchSysName;

	/** 子表：第三方系统注册参数 的集合 */
	private List<TsDsfSysParam> list = new ArrayList<>();
	/** 子表：第三方系统注册参数 的的单条数据 */
	private TsDsfSysParam tsDsfSysParam = new TsDsfSysParam();
	/** 实例化主表：第三方系统注册 */
	private TsDsfSys tsDsfSys = new TsDsfSys();
	/** 子表参数 */
	private Integer tig;
	/** 给主表RID赋值 */
	private Integer rid;
	/** 查询条件：系统类型集合 */
	private String listType[];
	/** 类型 */
	private String type;

	/** 虚拟路径 */
	private String xnPath;

	public SysLoginBean() {
		super.ifSQL = true;
		super.searchAction();
	}

	/**
	 * 添加数据
	 * */
	@Override
	public void addInit() {

		tsDsfSys = new TsDsfSys();
		list = new ArrayList<TsDsfSysParam>();
		
			
		}
	

	@PostConstruct
	public void inti() {

		// 虚拟路径
		xnPath = JsfUtil.getAbsolutePath();

	}

	/**
	 * 查看数据
	 * */
	@Override
	public void viewInit() {
		this.tsDsfSys = new TsDsfSys();
		this.tsDsfSys.setRid(rid);
		this.tsDsfSys = systemModuleService.findSys(rid);
		this.list = systemModuleService.findParamList(tsDsfSys.getRid()); // 根据主表获取子表信息
	}

	/**
	 * 修改数据
	 * */
	@Override
	public void modInit() {
		// TODO Auto-generated method stub
		this.viewInit();
	}

	/**
	 * 保存数据
	 * */
	@Override
	public void saveAction() {
		// TODO Auto-generated method stub
		
		boolean flag = true;
		
		if(StringUtils.isBlank(tsDsfSys.getSysName())){
			JsfUtil.addErrorMessage("系统名称不能为空");			
			flag = false;
		}			
		if(null==(tsDsfSys.getXtType())){
			JsfUtil.addErrorMessage("类型不能为空");
			flag = false;
		}
		if(!(list.size()>0)){
			JsfUtil.addErrorMessage("注册参数不能为空");
			flag = false;
		}if (!flag) {
			return;
		}
		if(tsDsfSys.getXtType()==2){
			tsDsfSys.setSysUrl(null);
		}
		try{
		tsDsfSys.setCreateDate(new Date());
		tsDsfSys.setCreateManid(this.sessionData.getUser().getRid());			
		tsDsfSys.setList(list);
		tsDsfSysParam.setCreateDate(new Date());
		tsDsfSysParam.setCreateManid(1);
		tsDsfSys.setList(list);
		this.systemModuleService.saveSystem(tsDsfSys);
	
	this.searchAction();
		JsfUtil.addSuccessMessage("保存成功");	
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addSuccessMessage("保存失败");
		}
	}
	/** 子表保存与添加条件 */
	public void tdspSave() {
		if (tig == 1) {
			this.list.add(tsDsfSysParam);
			JsfUtil.addSuccessMessage("添加成功");

		}

		RequestContext requestContext = RequestContext.getCurrentInstance();

		requestContext.execute("PF('Dlg2').hide()");

	}

	/** 删除子表信息 */
	public void deleteSys() {
		this.list.remove(tsDsfSysParam);
	}

	/** 同时删除主子表信息 */
	public void delete() {
		
		this.viewInit();
		
		String message=this.systemModuleService.deleteSys(tsDsfSys.getRid());
		if(message!=null){
			JsfUtil.addErrorMessage(message);
		}
		super.searchAction();

	}

	/** 添加子表数据 */
	public void sysAdd() {
		this.tsDsfSysParam = new TsDsfSysParam();
		tsDsfSysParam.setCreateDate(new Date());
		tsDsfSysParam.setCreateManid(this.sessionData.getUser().getRid());
		this.tsDsfSysParam.setTsDsfSysId(tsDsfSys);
	}

	/* 文件删除 */
	public void deleteDiskFile() {
		String iconFile = this.tsDsfSys.getSysIcon();
		if (null != iconFile) {
			String filePath = xnPath + iconFile.substring(1);
			new File(filePath).delete();
			this.tsDsfSys.setSysIcon(null);
		}
	}

	// 文件上传
	public void handleFileUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			String fileName = file.getFileName();
			// 得到唯一uid
			String uuid = UUID.randomUUID().toString().replaceAll("-", "");
			// 根路径
			// String path =
			// System.getProperty("user.dir").replaceAll("\\\\","/");
			// 后缀名
			String hz = fileName.substring(fileName.lastIndexOf(".") + 1);
			// 文件路径
			String filePath = xnPath + "files/dsfLogo/" + uuid + "." + hz;
			String showDir = "/files/dsfLogo/" + uuid + "." + hz;
			this.tsDsfSys.setSysIcon(showDir);
			try {
				FileUtils.copyFile(filePath, file.getInputstream());
				JsfUtil.addSuccessMessage("上传成功！");
				RequestContext.getCurrentInstance().execute(
						"fileUIdVar.hide();");
			} catch (IOException e) {
				FacesMessage msg = new FacesMessage("上传失败", file.getFileName()
						+ "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", msg);
				e.printStackTrace();
			}
		}
	}

	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder();
		sb.append(" FROM TS_DSF_SYS T1 ");
		sb.append("  WHERE 1=1");

		if (StringUtils.isNotBlank(this.searchSysName)) {
			sb.append(" AND  T1.SYS_NAME LIKE :SYSNAME ");
			this.paramMap.put("SYSNAME", "%" + this.searchSysName.trim() + "%");
		}

		if (null != listType && listType.length > 0) {
			sb.append(" AND T1.XT_TYPE in (")
					.append(StringUtils.array2string(listType, ","))
					.append(")");
		}

		StringBuilder searchSql = new StringBuilder(
				"SELECT T1.RID,T1.SYS_NAME ,T1.SYS_ICON,T1.XT_TYPE,T1.SYS_URL")
				.append(sb);
		searchSql.append(" ORDER BY T1.SYS_NAME").toString();

		StringBuilder countSql = new StringBuilder("SELECT COUNT(T1.RID) ")
				.append(sb);
		return new String[] { searchSql.toString(), countSql.toString() };
	}

	public String getSearchSysName() {
		return searchSysName;
	}

	public void setSearchSysName(String searchSysName) {
		this.searchSysName = searchSysName;
	}

	public List<TsDsfSysParam> getList() {
		return list;
	}

	public void setList(List<TsDsfSysParam> list) {
		this.list = list;
	}

	public Integer getTig() {
		return tig;
	}

	public void setTig(Integer tig) {
		this.tig = tig;
	}

	public String getXnPath() {
		return xnPath;
	}

	public void setXnPath(String xnPath) {
		this.xnPath = xnPath;
	}

	public TsDsfSys getTsDsfSys() {
		return tsDsfSys;
	}

	public void setTsDsfSys(TsDsfSys tsDsfSys) {
		this.tsDsfSys = tsDsfSys;
	}

	public TsDsfSysParam getTsDsfSysParam() {
		return tsDsfSysParam;
	}

	public void setTsDsfSysParam(TsDsfSysParam tsDsfSysParam) {
		this.tsDsfSysParam = tsDsfSysParam;
	}

	public SystemModuleServiceImpl getSystemModuleService() {
		return systemModuleService;
	}

	public void setSystemModuleService(
			SystemModuleServiceImpl systemModuleService) {
		this.systemModuleService = systemModuleService;
	}

	public SessionData getSessionData() {
		return sessionData;
	}

	public void setSessionData(SessionData sessionData) {
		this.sessionData = sessionData;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public String[] getListType() {
		return listType;
	}

	public void setListType(String[] listType) {
		this.listType = listType;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

}