package com.chis.modules.system.web;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.component.tabview.Tab;
import org.primefaces.context.RequestContext;
import org.primefaces.event.TabChangeEvent;

import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TdMsgSub;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.MessageType;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.logic.TaskPO;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.MsgSendUtil;

/**
 * 首页的待办任务列表
 * 
 */
@ManagedBean(name="indexTodoTaskListBean")
@ViewScoped
public class IndexTodoTaskListBean extends FacesBean {

	private static final long serialVersionUID = -738445454536625063L;
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	/** 消息 */
	private List<TaskPO> taskList;
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	private List<Object[]> dbMsgList;
	private Integer rid;
	private Integer taskId;
	private List<Object[]> tabMenuList;
	/**我的委托，*/
	private List<Object[]> trustList;
	private BigDecimal lanHeight;
	private boolean ifFlow;
	private String firstMenu;
	private Integer tabIndex=0;
	private String nodeId;
	private String businessId;

	public IndexTodoTaskListBean() {
		this.initHeight();
		this.initTabView();
	}
	
	private String buildBusinessSql(String menuName){
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT M.* FROM (");
		sb.append(" SELECT T1.RID, T3.MSG_TYPE, T1.ACCEPT_STATE,T3.INFO_TITLE,TO_CHAR(T3.PUBLISH_TIME, 'YYYY-MM-DD') AS PUBLISH_TIME,");
		sb.append(" T3.NET_ADR, T4.USERNAME,T3.RID AS MAIN_ID");
		sb.append(" FROM TD_MSG_SUB T1 ");
		sb.append(" INNER JOIN TS_USER_INFO T2 ON T1.PUBLISH_MAN = T2.RID ");
		sb.append(" INNER JOIN TD_MSG_MAIN T3 ON T1.MAIN_ID = T3.RID ");
		sb.append(" INNER JOIN TS_USER_INFO T4 ON T3.PUBLISH_MAN = T4.RID ");
		sb.append(" LEFT JOIN TS_MENU T5 ON T3.MENU_ID=T5.RID");
		sb.append(" WHERE 1=1 ");
		sb.append(" AND T2.RID = '").append(this.sessionData.getUser().getRid()).append("' ");
		sb.append(" AND T3.IS_TODO=1");
		sb.append(" AND T3.TODO_STATE <> 1");
		if(StringUtils.isNotBlank(menuName) && null != menuName){
			sb.append(" AND T5.MENU_CN='").append(menuName).append("'");
		}
		sb.append(" AND T1.ACCEPT_STATE IN (0, 1)");
		sb.append(") M WHERE 1=1 ");
		sb.append(" ORDER BY M.PUBLISH_TIME DESC ");
		return sb.toString();
	}
	
	public void initHeight(){
		String colId=this.commService.findParamValue("SYSTEM_TODO_COLUMN");
		StringBuilder sb=new StringBuilder();
		sb.append("select a.HEIGHTH from TD_PORTAL_LAYOUT a");
        sb.append(" where a.col_id= ").append(colId);
        List<Object[]> list=this.commService.getSqlList(sb.toString());
        if(null != list && list.size()>0){
        	Object obj=list.get(0);
        	this.lanHeight=new BigDecimal(obj.toString());
        }
	}
	
	public void initTabView(){
		tabMenuList=new ArrayList<>();
		this.trustList = this.flowBusinessService.findMyTrust(this.sessionData.getUser().getRid());
		List<Object[]> flowList=this.commService.getSqlList(this.buildFlowSql(1,null));//找到流程待办任务
		List<Object[]> businessList=this.commService.getSqlList(this.buildSql());//找到业务待办任务
		Object[] value=new Object[2];
		if(null != flowList && flowList.size()>0){
			for(Object[] obj:flowList){
				if(null != obj[1] && !"0".equals(obj[1].toString())){
					value=new Object[2];
					value[0]=obj[0];
					value[1]=obj[1];
					tabMenuList.add(value);
				}
			}
		}
		if(null != businessList && businessList.size()>0){
			for(Object[] obj:businessList){
				if(null != obj[2] && !"0".equals(obj[2].toString())){
					value=new Object[2];
					value[0]=obj[0];
					value[1]=obj[2];
					tabMenuList.add(value);
				}
			}
		}
		if(null != tabMenuList && tabMenuList.size()>0){
			if(tabIndex>tabMenuList.size()-1){
				tabIndex=0;
			}
			Object[] obj=tabMenuList.get(tabIndex);
			this.firstMenu=obj[0].toString();
			this.onTabChange(null);
		}
	}
	
	public void onTabChange(TabChangeEvent event){
		dbMsgList=new ArrayList<>();
		if(null != event){
			Tab activeTab = event.getTab();
			this.firstMenu=activeTab.getId();
		}
		//判断选择的tab是流程待办还是业务模块待办
		ifFlow=false;
		List<Object[]> flowList=this.commService.getSqlList(this.buildFlowSql(0,null));//找到流程待办任务
		if(null != flowList && flowList.size()>0){
			for(Object[] obj:flowList){
				if(firstMenu.equals(obj[2].toString())){
					ifFlow=true;
					break;
				}
			}
		}
		if(ifFlow){
			this.dbMsgList=this.commService.getSqlList(this.buildFlowSql(0,firstMenu));
		}else{
			this.dbMsgList=this.commService.getSqlList(this.buildBusinessSql(firstMenu));
		}
	}
	
	public String buildFlowSql(Integer ifCount,String defName){
		StringBuilder sb=new StringBuilder();
		sb.append(" WITH TASKS AS ( ");
		sb.append(" SELECT DISTINCT RES.*, '").append(this.sessionData.getUser().getRid()).append("' AS USERID ");
		sb.append(" FROM ACT_RU_TASK RES ");
		sb.append("  INNER JOIN ACT_RU_IDENTITYLINK I ON I.TASK_ID_ = RES.ID_ ");
		sb.append(" WHERE RES.ASSIGNEE_ IS NULL ");
		sb.append("  AND I.TYPE_ = 'candidate' ");
		sb.append("  AND (I.USER_ID_ = '").append(this.sessionData.getUser().getRid()).append("') ");
		sb.append(" UNION ");
		sb.append(" SELECT DISTINCT RES.*, '").append(this.sessionData.getUser().getRid()).append("' AS USERID ");
		sb.append(" FROM ACT_RU_TASK RES ");
		sb.append("  WHERE RES.ASSIGNEE_ = '").append(this.sessionData.getUser().getRid()).append("' ");
		if(null != this.trustList && this.trustList.size() > 0 ) {
			for(Object[] o : this.trustList) {
				sb.append(" UNION ");
				sb.append(" SELECT DISTINCT RES.*, '").append(o[0]).append("' AS USERID FROM ACT_RU_TASK RES ");
				sb.append(" INNER JOIN ACT_RU_IDENTITYLINK I ON I.TASK_ID_ = RES.ID_ ");
				sb.append(" WHERE RES.ASSIGNEE_ IS NULL AND I.TYPE_ = 'candidate' AND (I.USER_ID_ = '").append(o[0]).append("') ");
				sb.append(" AND RES.PROC_DEF_ID_ IN (").append(o[1]).append(") ");
				sb.append(" UNION ");
				sb.append(" SELECT DISTINCT RES.*, '").append(o[0]).append("' AS USERID FROM ACT_RU_TASK RES ");
				sb.append(" WHERE RES.ASSIGNEE_ = '").append(o[0]).append("' ");
				sb.append(" AND RES.PROC_DEF_ID_ IN (").append(o[1]).append(") ");
			}
		}
		sb.append(" ) ");
		if(ifCount == 1){
			sb.append(" SELECT T2.DEF_NAME,COUNT(T2.RID)COUNTS ");
		}else{
			sb.append(" SELECT T1.ID_,T2.RID AS DEFID,T2.DEF_NAME,");
			sb.append(" DECODE(T1.USERID, '").append(this.sessionData.getUser().getRid()).append("','','') || T1.DESCRIPTION_||A3.BUSINESS_KEY_ AS BUSINESSTITLE, ");
			sb.append(" TO_CHAR(A3.START_TIME_, 'YYYY-MM-DD') AS START_TIME_,T3.RID AS NODEID,A4.TEXT_ AS businessId");
		}
		sb.append(" FROM TASKS T1 ");
		sb.append(" INNER JOIN TD_FLOW_DEF T2 ON T1.PROC_DEF_ID_ = T2.ACT_DEF_ID ");
		sb.append(" INNER JOIN TD_FLOW_NODE T3 ON T3.DEF_ID = T2.RID AND T3.ACT_NODE_ID = T1.TASK_DEF_KEY_ ");
		sb.append(" INNER JOIN ACT_HI_PROCINST A3 ON A3.PROC_INST_ID_ = T1.PROC_INST_ID_ ");
		sb.append(" LEFT JOIN ACT_RU_VARIABLE A4 ON A4.PROC_INST_ID_ = T1.PROC_INST_ID_ AND A4.NAME_ = 'businessId'");
		sb.append(" LEFT JOIN TS_USER_INFO T5 ON A3.START_USER_ID_ = T5.RID ");
		if(StringUtils.isNotBlank(defName) && null != defName){
			sb.append(" where T2.DEF_NAME='").append(defName).append("'");
		}
		if(ifCount ==1){
			sb.append(" GROUP BY T2.DEF_NAME ");
			sb.append(" ORDER BY T2.DEF_NAME ");
		}else{
			sb.append(" ORDER BY A3.START_TIME_ DESC");
		}
		return sb.toString();
	}

	
	public String buildSql() {
		StringBuilder sb = new StringBuilder();
		sb.append("  SELECT T3.MENU_CN as menuCN, ");
		sb.append("         T3.MENU_URI as url, ");
		sb.append("         COUNT(T3.RID) as counts ");
		sb.append("    FROM TD_MSG_MAIN T1");
		sb.append("   INNER JOIN TD_MSG_SUB T2 ON T2.MAIN_ID = T1.RID");
		sb.append("   INNER JOIN TS_MENU T3 ON T1.MENU_ID = T3.RID");
		sb.append("   WHERE T1.IS_TODO = '1' ");
		sb.append("     AND T1.TODO_STATE = '0' ");
		sb.append("     AND T3.MENU_CN != '待办任务'");
		sb.append("     AND T2.PUBLISH_MAN = '").append(this.sessionData.getUser().getRid()).append("'");
		sb.append("   GROUP BY T3.MENU_CN, T3.MENU_URI ");
		return sb.toString();
	}
	
	public void openLinkAction(){
		// 消息是否失效，true表示失效
		boolean msgValid = Boolean.TRUE;
		/**
		 * 信息类型只有是流程待办任务的时候，才会去检查taskId对应的任务， taskId为空或者任务不存在，则不跳转，并且提示该消息已经失效！
		 */
		if(ifFlow){
			//根据taskId找到subId
			StringBuilder sb=new StringBuilder();
			sb.append(" select b.rid from td_msg_main a ");
			sb.append(" inner join td_msg_sub b on b.main_id=a.rid where b.publish_man=").append(this.sessionData.getUser().getRid());
			sb.append(" and a.net_adr like '%").append(this.taskId).append("%'");
			List<Object[]> list=this.commService.getSqlList(sb.toString());
			if(null != list && list.size()>0){
				Object obj=list.get(0);
				this.rid=Integer.valueOf(obj.toString());
			}else{
				rid=null;
			}
		}
		
		if(null == this.rid){
			try {
				StringBuilder sb = new StringBuilder("/webapp/flow/tdFlowTaskEdit.faces?ph=1&read=false");
				sb.append("&trustId=").append(this.sessionData.getUser().getRid());
				if (null != taskId) {
					sb.append("&taskId=").append(this.taskId);
				}
				if (StringUtils.isNotBlank(nodeId)) {
					sb.append("&nodeId=").append(this.nodeId);
				}
				if (StringUtils.isNotBlank(businessId)) {
					sb.append("&businessId=").append(this.businessId);
				}
				// 否则，打开
				StringBuilder script = new StringBuilder();
				script.append("forwordPage('任务处理','").append(sb.toString()).append("')");
				RequestContext.getCurrentInstance().execute(script.toString());
			} catch (Exception e) {
				e.printStackTrace();
				throw new RuntimeException(e);
			}
		}else{
		TdMsgSub tdMsgSub = this.systemModuleService.findMsgSub(Integer.valueOf(this.rid));
		// 判断消息类型,不是待办任务，就不失效
		if (tdMsgSub.getTdMsgMain().getMessageType().equals(MessageType.ACTIVITI)
				|| tdMsgSub.getTdMsgMain().getMessageType().equals(MessageType.DISPATCH)) {
			String netAdr = tdMsgSub.getTdMsgMain().getNetAdr();
			// 待办任务一定要有taskId
			if (StringUtils.isNotBlank(netAdr) && netAdr.indexOf("taskId") != -1) {
				// 获取任务ID
				String taskId = StringUtils.parseUrl(netAdr, "taskId");
				// 待办任务一定要有taskId
				if (StringUtils.isNotBlank(taskId)) {
					// 0-节点ID 1-业务表ID 如果为空说明任务已经不存在
					Object[] obs = this.flowBusinessService.findTaskData(taskId);
					if (null != obs && obs.length >= 3) {
						msgValid = Boolean.FALSE;
						// 将待办任务的url完善全，加上节点ID和业务ID，放在taskId前面
						String[] split = netAdr.split("\\?");
						// 将待办任务的url完善全，加上节点ID和业务ID
						StringBuilder newAdr = new StringBuilder(split[0]);
						newAdr.append("?nodeId=").append(obs[0]).append("&businessId=").append(obs[1]);
						newAdr.append("&trustId=").append(this.sessionData.getUser().getRid());
						newAdr.append("&").append(split[1]).append("&msgSubId=").append(tdMsgSub.getRid());
						newAdr.append("&read=");
						if ("1".equals(obs[2].toString())) {
							// 待办任务
							newAdr.append("false");
						} else {
							// 历史任务只能只读
							newAdr.append("true");
						}

						tdMsgSub.getTdMsgMain().setNetAdr(newAdr.toString());
					}
				}
			}
		} else if (tdMsgSub.getTdMsgMain().getMessageType().equals(MessageType.EMERG)) {
			String netAdr = tdMsgSub.getTdMsgMain().getNetAdr();
			tdMsgSub.getTdMsgMain().setNetAdr(
					new StringBuilder(netAdr).append("&msgSubId=").append(tdMsgSub.getRid()).toString());
			msgValid = Boolean.FALSE;
		} else {
			String netAdr = tdMsgSub.getTdMsgMain().getNetAdr();
			if(!netAdr.contains("?"))	{//如果不带参数则需要增加占位符
				netAdr = new StringBuilder(netAdr).append("?ph=1").toString();
			}
			tdMsgSub.getTdMsgMain().setNetAdr(
					new StringBuilder(netAdr).append("&msgSubId=").append(tdMsgSub.getRid()).toString());
			msgValid = Boolean.FALSE;
		}

		// 消息失效，提醒
		if (msgValid) {
			JsfUtil.addErrorMessage("该业务数据已删除！");
		} else {
			if (null != this.rid) {
				TsUserInfo tsUserInfo = this.sessionData.getUser();
				this.systemModuleService.editSubState(this.rid.toString(),"1", tsUserInfo.getRid());
				MsgSendUtil.updateMsgInfo();
			}
			
			// 否则，打开
			StringBuilder script = new StringBuilder();
			script.append("forwordPage('").append(tdMsgSub.getTdMsgMain().getNetName());
			script.append("', '").append(tdMsgSub.getTdMsgMain().getNetAdr());
			//打开时传递消息子表状态（0，未阅；1，已阅；2，收藏）
			script.append("&msgSubAcceptState=").append(tdMsgSub.getAcceptState()).append("')");
			RequestContext.getCurrentInstance().execute(script.toString());
		}
		}
	}

	public List<TaskPO> getTaskList() {
		return taskList;
	}

	public void setTaskList(List<TaskPO> taskList) {
		this.taskList = taskList;
	}

	public List<Object[]> getDbMsgList() {
		return dbMsgList;
	}

	public void setDbMsgList(List<Object[]> dbMsgList) {
		this.dbMsgList = dbMsgList;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public List<Object[]> getTabMenuList() {
		return tabMenuList;
	}

	public void setTabMenuList(List<Object[]> tabMenuList) {
		this.tabMenuList = tabMenuList;
	}

	public BigDecimal getLanHeight() {
		return lanHeight;
	}

	public void setLanHeight(BigDecimal lanHeight) {
		this.lanHeight = lanHeight;
	}

	public Integer getTaskId() {
		return taskId;
	}

	public void setTaskId(Integer taskId) {
		this.taskId = taskId;
	}

	public boolean isIfFlow() {
		return ifFlow;
	}

	public void setIfFlow(boolean ifFlow) {
		this.ifFlow = ifFlow;
	}

	public Integer getTabIndex() {
		return tabIndex;
	}

	public void setTabIndex(Integer tabIndex) {
		this.tabIndex = tabIndex;
	}

	public String getNodeId() {
		return nodeId;
	}

	public void setNodeId(String nodeId) {
		this.nodeId = nodeId;
	}

	public String getBusinessId() {
		return businessId;
	}

	public void setBusinessId(String businessId) {
		this.businessId = businessId;
	}

	
}
