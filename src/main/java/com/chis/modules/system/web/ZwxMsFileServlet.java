//package com.chis.modules.system.web;
//
//import javax.servlet.annotation.WebServlet;
//
//
///**
// * 存在使用漏洞，禁用此方法
// * 所有涉及到金格office的文件均放在虚拟路径下的officeFiles目录下
// * <AUTHOR> 2015-3-18
// */
//@Deprecated
//@WebServlet(name="MsOfficeServlet",value="/MsOfficeServlet")
//public class ZwxMsFileServlet extends ZwxBaseFileServlet  {
//
//	/**
//	 *
//	 */
//	private static final long serialVersionUID = -8280998639003616130L;
//
//	@Override
//	public String initDocFilePath() {
//		return "archives";
//	}
//
//
//}
