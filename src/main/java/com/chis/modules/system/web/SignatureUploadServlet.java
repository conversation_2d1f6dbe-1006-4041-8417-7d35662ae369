package com.chis.modules.system.web;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.service.SignatureUploadService;
/**
 * <p>类描述：签章servlet（代码迁移）</p>
 * @ClassAuthor qrr,2018年4月21日,SignatureUploadServlet
 * */
@WebServlet(name="signatureUpload", value="/signatureUpload")
public class SignatureUploadServlet extends HttpServlet{
	private SignatureUploadService baseService = SpringContextHolder.getBean(SignatureUploadService.class);
	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		super.doGet(req, resp);
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		baseService.processReq(req, resp);
	}

}
