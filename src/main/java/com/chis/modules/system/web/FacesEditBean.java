package com.chis.modules.system.web;

import java.util.HashMap;
import java.util.Map;

import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.utils.DefaultLazyDataModel;

/**
 * 带有转向维护模块、真分页的托管Bean
 * 
 * @editContent 修改buildProcessData()方法，如果查询的数据集需要修改，子类不需要再重写  david 2014-09-04 
 *  
 * <AUTHOR>
 */
public abstract class FacesEditBean extends FacesBean{
	/**当前活动的标签，从0开始，默认为0,0:主界面1：添加修改界面2：查看界面*/
	private int activeTab;
	/**真分页模型*/
	private DefaultLazyDataModel dataModel;
	/**查询条件参数，key为参数名称，value为值*/
	protected Map<String, Object> paramMap = new HashMap<String, Object>();
	/**查询语句，0-查询记录的hql语句 1-查询记录数的hql语句*/
	private String[] hql;
    /**表格的ID*/
    private static final String TABLE_ID = "mainForm:dataTable";
    
    protected boolean ifSQL = Boolean.FALSE;
	public FacesEditBean() {

	}
	
	/**
	 * 查询操作，如果不满足，可重写
	 */
	@SuppressWarnings("unchecked")
	public void searchAction() {
		this.paramMap.clear();
		this.hql = this.buildHqls();
		this.dataModel = new DefaultLazyDataModel(this.hql[0], this.hql[1], this.paramMap, this.buildProcessData(), TABLE_ID, this.ifSQL);
    }
	
	/**
	 * 添加初始化
	 */
	public void addInitAction() {
		this.addInit();
		this.forwardEditPage();
	}
	
	/**
	 * 修改初始化
	 */
	public void modInitAction() {
		this.modInit();
		this.forwardEditPage();
	}

    /**
     * 查看初始化
     */
    public void viewInitAction() {
        this.viewInit();
        this.forwardViewPage();
    }
	
	/**
	 * 转向修改界面
	 */
	public void forwardEditPage() {
		this.activeTab = 1;
	}

    /**
     * 转向到查看界面
     */
    public void forwardViewPage() { this.activeTab = 2; }
    
    /**
     * 转向到其他界面
     */
    public void forwardOtherPage() { this.activeTab = 3; }
	
	/**
	 * 返回首页面
	 */
	public void backAction() {
		this.activeTab = 0;
	}
	
	/**
	 * 跳转到其他查看页面
	 */
	public void forwardOtherViewPage() {this.activeTab = 4;}
	/**
	 * 跳转到其他查看页面
	 */
	public void forwardEdit2Page() {this.activeTab = 5;}
	/**
	 * 跳转到其他查看页面
	 */
	public void forwardEdit3Page() {this.activeTab = 6;}
	/**
	 * 跳转到其他查看页面
	 */
	public void forwardEdit4Page() {this.activeTab = 7;}
	
	/**
	 * 具体的添加初始化
	 */
	public abstract void addInit();
	
	/**
	 * 具体的修改初始化
	 */
	public abstract void viewInit();

    /**
     * 具体的查看初始化
     */
    public abstract void modInit();
	
	/**
	 * 具体的添加
	 */
	public abstract void saveAction();
	
	/**
	 * 组织hql查询语句，同时有参数的话，向paramMap里面放值
	 * @return 0-查询记录的hql语句 1-查询记录数的hql语句
	 */
	public abstract String[] buildHqls(); 
	
	/**
	 * 查询数据处理接口，如果需要处理，则托管bean必须要实现
	 * IProcessData接口，并且返回this；否则返回NULL
	 */
	public IProcessData buildProcessData() {
		if(this instanceof IProcessData) {
			return (IProcessData) this;
		}else {
			return null;
		}
	}
	
	//gets and sets
	public int getActiveTab() {
		return activeTab;
	}

	public void setActiveTab(int activeTab) {
		this.activeTab = activeTab;
	}

	public DefaultLazyDataModel getDataModel() {
		return dataModel;
	}

	public void setDataModel(DefaultLazyDataModel dataModel) {
		this.dataModel = dataModel;
	} 
}
