package com.chis.modules.system.web;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsGroup;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * 人员选择（多选） 按科室、组列出该单位的所有人员 
 * Created by zww on 2014-8-4.
 * 
 * 返回的结果key-selectUserList value-Collection<TsUserInfo>
 * height:500
 * width:920
 * contentWidth:870
 */
@ManagedBean(name = "selectUsersBean")
@ViewScoped
public class SelectUsersBean extends FacesBean {
	
	private static final long serialVersionUID = 2350275983339647922L;
	/** 按科室的用户集合 */
	private Map<TsOffice, List<TsUserInfo>> userOfficeMap;
	/** 按组的用户集合 */
	private Map<TsGroup, List<TsUserInfo>> userGroupMap;
	/** 科室全选 */
	private boolean officeWholeSelect = Boolean.FALSE;
	/** 组全选 */
	private boolean groupWholeSelect = Boolean.FALSE;
	/** 科室ID */
	private Integer officeId;
	/** 组ID */
	private Integer groupId;
	/** 标题 */
	private String title = "人员选择";
	/** 已选用户IDS */
	private String selectIds;
	/** 是否显示用户组，默认显示 */
	private boolean ifShowGroup = true;
	/** 是否领导，默认非领导 */
	private boolean ifLeader = false;

	/** ejb session bean */
	private SystemModuleServiceImpl service = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	

	public SelectUsersBean() {
		this.init();
		this.searchAction();
	}
	
	/**
	 * 初始化
	 */
	public void init() {
		 String s1 = JsfUtil.getRequest().getParameter("title");
		 if(StringUtils.isNotBlank(s1)) {
			 this.title = s1;
		 }
		 String ifShowGroup = JsfUtil.getRequest().getParameter("ifShowGroup");
		 if(StringUtils.isNotBlank(ifShowGroup))	{
			 this.ifShowGroup = false;
		 }
		 String ifLeader = JsfUtil.getRequest().getParameter("ifLeader");
		 if(StringUtils.isNotBlank(ifLeader))	{
			 this.ifLeader = true;
		 }
		 this.selectIds = JsfUtil.getRequest().getParameter("selectIds");
	}

	/**
	 * 查询方法
	 */
	public void searchAction() {
		List list = this.service.findSelectUsersList(this.sessionData.getUser().getTsUnit().getRid(),ifLeader);
		this.userOfficeMap = (Map<TsOffice, List<TsUserInfo>>) list.get(0);
		this.userGroupMap = (Map<TsGroup, List<TsUserInfo>>) list.get(1);
		
		
		if(StringUtils.isNotBlank(this.selectIds)) {
			this.selectIds = "," + this.selectIds + ",";
			/**
			 * 需要对查询出来的结果进行处理一下，
			 * 1.已选的人要打上勾
			 * 2.某科室下所有人都选了，该科室要勾上
			 * 3.某组下所有人都选了，该组要勾上
			 * 4.科室都选了，全部科室要勾上
			 * 5.组都选了，全部组要勾上
			 */
			boolean allUserOffBool = Boolean.TRUE;
			boolean allOffBool = Boolean.TRUE;
			
			Set<TsOffice> keySet = this.userOfficeMap.keySet();
			for (TsOffice off : keySet) {
				List<TsUserInfo> userList = this.userOfficeMap.get(off);
				for (TsUserInfo t : userList) {
					if(StringUtils.contains(this.selectIds, ","+t.getRid()+",")) {
						t.setSelected(Boolean.TRUE);
					}else {
						allUserOffBool = Boolean.FALSE;
					}
				}
				
				if(allUserOffBool) {
					off.setSelected(Boolean.TRUE);
				}else {
					allOffBool = Boolean.FALSE;
				}
				
				allUserOffBool = Boolean.TRUE;
			}
			
			if(allOffBool) {
				this.officeWholeSelect = Boolean.TRUE;
			}
			
			if(null != this.userGroupMap && this.userGroupMap.size() > 0) {
				boolean allUserGroupBool = Boolean.TRUE;
				boolean allGroupBool = Boolean.TRUE;
				
				Set<TsGroup> groupSet = this.userGroupMap.keySet();
				for (TsGroup group : groupSet) {
					List<TsUserInfo> userList = this.userGroupMap.get(group);
					for (TsUserInfo t : userList) {
						if(StringUtils.contains(this.selectIds, ","+t.getRid()+",")) {
							t.setSelected(Boolean.TRUE);
						}else {
							allUserGroupBool = Boolean.FALSE;
						}
					}
					
					if(allUserGroupBool) {
						group.setSelected(Boolean.TRUE);
					}else {
						allGroupBool = Boolean.FALSE;
					}
					
					allUserGroupBool = Boolean.TRUE;
				}
				
				if(allGroupBool) {
					this.groupWholeSelect = Boolean.TRUE;
				}
			}
		}
	}

	/**
	 * 全选科室
	 */
	public void officeWholeSelectAction() {
		Set<TsOffice> keySet = this.userOfficeMap.keySet();
		for (TsOffice off : keySet) {
			off.setSelected(this.officeWholeSelect);
			List<TsUserInfo> list = this.userOfficeMap.get(off);
			for (TsUserInfo t : list) {
				t.setSelected(this.officeWholeSelect);
			}
		}
	}

	/**
	 * 某个科室全选
	 */
	public void officeSelectAction() {
		if(null != this.officeId) {
			Set<TsOffice> keySet = this.userOfficeMap.keySet();
			for (TsOffice off : keySet) {
				if(off.getRid().equals(this.officeId)) {
					List<TsUserInfo> list = this.userOfficeMap.get(off);
					for (TsUserInfo t : list) {
						t.setSelected(off.isSelected());
					}
				}
			}
		}
	}
	
	/**
	 * 全选组
	 */
	public void groupWholeSelectAction() {
		Set<TsGroup> keySet = this.userGroupMap.keySet();
		for (TsGroup off : keySet) {
			off.setSelected(this.groupWholeSelect);
			List<TsUserInfo> list = this.userGroupMap.get(off);
			for (TsUserInfo t : list) {
				t.setSelected(this.groupWholeSelect);
			}
		}
	}

	/**
	 * 某个组全选
	 */
	public void groupSelectAction() {
		if(null != this.groupId) {
			Set<TsGroup> keySet = this.userGroupMap.keySet();
			for (TsGroup off : keySet) {
				if(off.getRid().equals(this.groupId)) {
					List<TsUserInfo> list = this.userGroupMap.get(off);
					for (TsUserInfo t : list) {
						t.setSelected(off.isSelected());
					}
				}
			}
		}
	}

	/**
	 * 选择确定方法
	 */
	public void selectAction() {
		Map<String, Object> map = new HashMap<String, Object>();
		Map<String, TsUserInfo> userMap = new HashMap<String, TsUserInfo>();
		
		Collection<List<TsUserInfo>> officeValues = this.userOfficeMap.values();
		for(List<TsUserInfo> list : officeValues) {
			for(TsUserInfo t: list) {
				if(t.isSelected()) {
					userMap.put(t.getRid().toString(), t);
				}
			}
		}
		
		Collection<List<TsUserInfo>> groupValues = this.userGroupMap.values();
		for(List<TsUserInfo> list : groupValues) {
			for(TsUserInfo t: list) {
				if(t.isSelected()) {
					userMap.put(t.getRid().toString(), t);
				}
			}
		}
		map.put("selectUserList",userMap.values());
		RequestContext.getCurrentInstance().closeDialog(map);
	}

	/**
	 * 关闭
	 */
	public void dialogClose() {
		RequestContext.getCurrentInstance().closeDialog(null);
	}

	public Map<TsOffice, List<TsUserInfo>> getUserOfficeMap() {
		return userOfficeMap;
	}

	public void setUserOfficeMap(Map<TsOffice, List<TsUserInfo>> userOfficeMap) {
		this.userOfficeMap = userOfficeMap;
	}

	public Map<TsGroup, List<TsUserInfo>> getUserGroupMap() {
		return userGroupMap;
	}

	public void setUserGroupMap(Map<TsGroup, List<TsUserInfo>> userGroupMap) {
		this.userGroupMap = userGroupMap;
	}

	public boolean isOfficeWholeSelect() {
		return officeWholeSelect;
	}

	public void setOfficeWholeSelect(boolean officeWholeSelect) {
		this.officeWholeSelect = officeWholeSelect;
	}

	public boolean isGroupWholeSelect() {
		return groupWholeSelect;
	}

	public void setGroupWholeSelect(boolean groupWholeSelect) {
		this.groupWholeSelect = groupWholeSelect;
	}

	public Integer getOfficeId() {
		return officeId;
	}

	public void setOfficeId(Integer officeId) {
		this.officeId = officeId;
	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public boolean isIfShowGroup() {
		return ifShowGroup;
	}

	public void setIfShowGroup(boolean ifShowGroup) {
		this.ifShowGroup = ifShowGroup;
	}

	
}
