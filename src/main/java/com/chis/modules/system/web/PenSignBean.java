package com.chis.modules.system.web;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.apache.commons.lang.StringUtils;
import org.primefaces.context.RequestContext;

import com.chis.common.utils.FileUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.SystemModuleServiceImpl;

/**
 * 汉王签名版签名，界面大小420,220 <br/>
 * 返回签名图片的相对路径 <br/>
 * <AUTHOR>
 * @createTime 2016年7月21日
 */
@ManagedBean(name = "penSignBean")
@ViewScoped
public class PenSignBean extends FacesBean {

	private static final long serialVersionUID = -1100693111644511194L;
    /**ejb session bean*/
	private SystemModuleServiceImpl systemService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private String signBinary;

    public PenSignBean() {
    }

    /**
     * 选择确定方法
     */
    public void confirmAction() {
        Map<String, Object> map = new HashMap<String, Object>();
        if(StringUtils.isBlank(this.signBinary)) {
        	JsfUtil.addErrorMessage("请签名！");
        	return;
        }else {
        	StringBuilder sb = new StringBuilder();
        	sb.append(JsfUtil.getAbsolutePath()).append("/penSign");
        	File file = new File(sb.toString());
        	if (!file.exists()) {
                file.mkdirs();
            }
        	sb.append("/").append(UUID.randomUUID().toString().replaceAll("-", "")).append(".jpg");
            String imgPath = sb.toString();
            try {
            	FileUtils.base64ToImage(this.signBinary, imgPath);
            	map.put("imgPath", imgPath.replaceFirst(JsfUtil.getAbsolutePath(), ""));
            	RequestContext.getCurrentInstance().closeDialog(map);
			} catch (Exception e) {
				e.printStackTrace();
				JsfUtil.addErrorMessage("请重新签名！");
	        	return;
			}
        }
        
    }


    /**
     * 关闭
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

	public String getSignBinary() {
		return signBinary;
	}

	public void setSignBinary(String signBinary) {
		this.signBinary = signBinary;
	}
   
}
