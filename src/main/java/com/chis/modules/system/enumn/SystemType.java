package com.chis.modules.system.enumn;

/**
 * 系统类型
 * <AUTHOR>
 */
public enum SystemType implements IEnum{
	
	COMM((short)0) {
        public String getTypeCN() { return "通用";}
	},
	JKZ((short)5) {
        public String getTypeCN() { return "健康证";}
    },
    EMERG((short)15) {
        public String getTypeCN() { return "卫生应急系统";}
    },
    SH((short)20) {
        public String getTypeCN() { return "学生常见病";}
    },
    JC((short)21) {
        public String getTypeCN() { return "健康危险因素监测系统";}
    },
    /**100-199位职卫平台项目*/
    ZYWS_TY((short)100) {
        public String getTypeCN() { return "职卫卫生-通用";}
    },
    COMM_BASE((short)101) {
        public String getTypeCN() { return "职卫平台所有表结构升级";}
    },
    FSWS_TY((short)120) {
        public String getTypeCN() { return "放射卫生-通用";}
    },
    HETH((short)140) {
    	public String getTypeCN() { return "职业卫生平台";}
	},
    HETH_ZZSB((short)141) {
        public String getTypeCN() { return "职业卫生平台-资质申报";}
    },
    ZYWS_CXTJ((short)160) {
    	public String getTypeCN() { return "职卫卫生-查询统计";}
    },
    ZYWS_ZDZYB((short)161) {
    	public String getTypeCN() { return "职卫卫生-重点职业病";}
    },
    FSWS_CXTJ((short)180) {
    	public String getTypeCN() { return "放射卫生-查询统计";}
    },
    /**位职卫平台子系统从200开始*/
    CFBSF((short)200) {
    	public String getTypeCN() { return "尘肺病随访";}
    },
    LAB_COMPARE((short)201) {
    	public String getTypeCN() { return "实验室对比（盲样考核）";}
    },
    BADRSN_CHECK((short)202){
        public String getTypeCN() { return "职业病重点危害因素监测";}
    },
    ZK_CHECK((short)203){
        public String getTypeCN() { return "质控考核";}
    },
    ZK_CHECK_QUE((short)204){
        public String getTypeCN() { return "专业技术人员考核";}
    },
    KFZD((short)205){
        public String getTypeCN() { return "尘肺病康复站点";}
    },
    HETH_DIAG((short)206) {
        public String getTypeCN() { return "职业卫生-职业病诊断";}
    },
    HETH_ZYBJD((short)207) {
        public String getTypeCN() { return "职业卫生-职业病鉴定申请";}
    },
    HETH_ZYBZD((short)208) {
        public String getTypeCN() { return "职业卫生-职业病鉴定申请";}
    },
    HETH_TRAIN((short)209) {
        public String getTypeCN() { return "在线培训";}
    },
    HETH_CFB_EXTRACT((short)210) {
        public String getTypeCN() { return "尘肺病筛查";}
    },
    HETH_ZK_CHECK_RPT((short)211) {
        public String getTypeCN() { return "重点职业监测迟报、漏报调查表";}
    },
    HETH_WARN((short)212) {
        public String getTypeCN() { return "预警管理";}
    },
    HETH_ANALY_QZK((short)213) {
        public String getTypeCN() { return "前置库统计";}
    },
    HETH_CFB_DIAG((short)214) {
        public String getTypeCN() { return "尘肺病临床诊断";}
    },
    HETH_ZKCHECK_EXPERT((short)215) {
        public String getTypeCN() { return "个案抽取专家评审";}
    },
    HETH_REPORT((short)216){
        public String getTypeCN() { return "职业/放射卫生技术服务信息报送卡";}
    },
    /**其余系统从500开始*/
    SLOW((short)500) {
        public String getTypeCN() { return "慢病";}
    },
	RISK((short)501) {
        public String getTypeCN() { return "风险评估";}
	},
    EQU((short)502) {
        public String getTypeCN() { return "冷链市级平台";}
    },
    MYGH((short)503) {
        public String getTypeCN() { return "免疫规划";}
    },

    

    FLOW((short)504) {
        public String getTypeCN() { return "流程引擎";}
    },
    
    ART((short)505) {
        public String getTypeCN() { return "物资管理";}
    },

    PORTAL((short)506) {
        public String getTypeCN() { return "门户";}
    },
    
	YQ((short)507) {
		public String getTypeCN() { return "仪器管理";}
	},
	
    
	
    CRB((short)508) {
        public String getTypeCN() { return "传染病疫情系统";}
    },	
    
    KPI((short)509) {
        public String getTypeCN() { return "绩效管理系统";}
    },
    
    ZWGL((short)510) {
        public String getTypeCN() { return "总务管理";}
    },
    
    JXC((short)511) {
        public String getTypeCN() { return "疫苗配送系统";}
    },

    

    YWGZLFX((short)512) {
    	public String getTypeCN() { return "业务工作量分析";}
    },
    
    PDI((short)513) {
    	public String getTypeCN() { return "血吸虫防治平台";}
    },
    OA((short)514) {
        public String getTypeCN() { return "疾控办公";}
    },
    QUE((short)515) {
        public String getTypeCN() { return "问卷调查";}
    },
   
    
    MYGH_NEW((short)517) {
    	public String getTypeCN() { return "免疫规划V2";}
    },
    AGWS((short)518) {
    	public String getTypeCN() { return "爱国卫生";}
    },
    SLOW_QUE((short)519) {
    	public String getTypeCN() { return "慢病问卷调查";}
    },
    TUB((short)520) {
    	public String getTypeCN() { return "结核病管理";}
    },
    JCFZ((short)521) {
    	public String getTypeCN() { return "决策辅助";}
    },
    ZZJC((short)522) {
    	public String getTypeCN() { return "自助检测";}
    },
    ZYJKHB((short)523) {
    	public String getTypeCN() { return "湖北职业卫生";}
    },
    ES((short)524) {
    	public String getTypeCN() { return "ES查询";}
    };
	private final Short typeNo;
	
	SystemType(Short typeNo) {
		this.typeNo = typeNo;
	} 

	public String toString() {return String.valueOf(typeNo);}

	
	public Short getTypeNo() {
		return typeNo;
	}

}
