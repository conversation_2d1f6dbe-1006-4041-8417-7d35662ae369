package com.chis.modules.system.enumn;

/**
 * <p>方法描述：诊断流程文书</p>
 *
 * @MethodAuthor qrr, 2018年4月24日, WritTypeEnum
 *
 * <p>修订内容：添加字段dealStep，表示该文书处于的环节</p>
 * @MethodReviser qrr, 2018年5月7日, WritTypeEnum
 */
public enum WritTypeEnum {
    JZDJ("职业病诊断就诊登记表", "2001", true, "HETH_2001", "1"),
    DP("职业病诊断委托协议书", "2021", false, "HETH_2021", "1"),

    TG("关于提供职业病诊断有关材料的函", "2002", true, "HETH_2002", "2"),
    XZ("关于提请协助开展职业病诊断有关工作的函（资料收集）", "2003", true, "HETH_2003", "2"),
    BZ("职业病诊断资料补正通知书", "2004", true, "HETH_2004", "2"),

    QR("关于对用人单位提交资料确认通知书", "2005", true, "HETH_2005", "3"),
    ZC("申请劳动仲裁建议书", "2006", true, "HETH_2006", "3"),
    QR_XZ("关于提请协助开展职业病诊断有关工作的函（资料确认）", "2007", true, "HETH_2003", "3"),
    ZZ("职业病诊断中止通知书", "2008", true, "HETH_2007", "3"),
    CLJC("职业病诊断申请材料接收凭证", "2018", true, "HETH_2018", "3"),
    ZX("职业病诊断咨询登记表", "2020", true, "HETH_2020", "4"),
    
    TL("职业病诊断讨论记录", "2009", true, "HETH_2009", "4"),
    Z("职业病诊断证明书", "2010", true, "HETH_2010", "4"),
    YQ("职业病诊断延期通知书", "2011", true, "HETH_2011", "4"),
    HB("诊断医师/鉴定专家回避申请书", "2012", true, "HETH_2012", "4"),
    ZD("职业病诊断/鉴定现场调查表", "2015", true, "HETH_2015", "4"),
    JL("职业病诊断记录表", "2019", true, "HETH_2019", "4"),

    QS("《职业病诊断证明书》签收通知书  ", "2013", true, "HETH_2013", "5"),
    QSD("《职业病诊断证明书》签收单", "2014", true, "HETH_2014", "5"),

    CF("尘肺报告卡", "2016", true, "HETH_2016", "6"),
    ZYB("职业病报告卡", "2017", true, "HETH_2017", "6"),
    
    ZYB_HZ("职业病会诊", "2030", true, "HETH_2030", "1"),
    YSZYB("疑似职业病报告卡", "2031", true, "HETH_2031", "1"),
    ZYBJKJCHZ("职业健康检查汇总报告卡", "2032", true, "HETH_2032", "1"),
    ZDRPT("职业病诊断、鉴定相关信息报告卡", "2033", true, "HETH_2033", "1"),
    YJCZWS("预警处置文书", "2034", true, "HETH_2034", "1"),
    ZYB_ZD("职业病报告卡", "2035", true, "HETH_2035", "6"),//职业病诊断
    ZYBYHYSJC("职业性有害因素监测卡", "2037", true, "HETH_2037", "1"),
    ZYBJDBG("职业病鉴定报告卡", "2038", true, "HETH_2038" , "9"),
    /*****************************【诊断】********************************/
    //申请鉴定
    JD_SQ("职业病鉴定申请书  ","4001",true,"HETH_4001","1"),

    //资料收集
    JD_TG_TZ("诊断机构的关于提供职业病鉴定有关材料的通知书","4002",true,"HETH_4002","2"),//诊断机构
    JD_TG_TZ2("鉴定机构的关于提供职业病鉴定有关材料的通知书","4021",true,"HETH_4021","2"),//鉴定机构
    JD_TG_H("关于提供职业病鉴定有关材料的函","4003",true,"HETH_4003","2"),
    JD_XZ("关于提请协助开展职业病鉴定有关工作的函（资料收集）","4004",true,"HETH_4004","2"),
    JD_ZZ("职业病诊断/鉴定中止通知书","4005",true,"HETH_4005","2"),
    JD_BZ("职业病诊断/鉴定资料补正通知书","4006",true,"HETH_4006","2"),

    //鉴定申请通知书
    JD_SL_TZ("职业病鉴定受理通知书","4007",true,"HETH_4007","3"),

    //专家抽取
    JD_EXT_TZ("职业病鉴定抽取专家通知书","4008",true,"HETH_4008","4"),
    JD_EXT("职业病鉴定专家抽取记录","4009",true,"HETH_4009","4"),

    
    //专家鉴定
    JD_EXT_FK("鉴定专家审阅资料意见反馈书","4010",true,"HETH_4010","5"),
    JD_MDC_TZ("职业病鉴定医学检查通知书","4011",true,"HETH_4011","5"),
    JD_SPOT("职业病诊断/鉴定现场调查表","4012",true,"HETH_4012","5"),
    JD_XZ2("关于提请协助开展职业病鉴定有关工作的函（专家鉴定）","4013",true,"HETH_4004","5"),
    JD_NTC("职业病鉴定会通知书","4014",true,"HETH_4014","5"),
    JD_NTC_TJ("职业病鉴定会通知书","4022",true,"HETH_4022","5"),
    JD_EXT_NTC("职业病鉴定会专家通知书","4015",true,"HETH_4015","5"),
    JD_XW_JL("职业病鉴定陈述及询问记录","4016",true,"HETH_4016","5"),
    JD_EXT_TLJL("职业病鉴定专家讨论记录","4017",true,"HETH_4017","5"),
    //职业病鉴定
    JD_CERT("职业病鉴定书","4018",true,"HETH_4018","6"),
    //结果送达
    JD_QS("《职业病鉴定书》签收通知书  ", "4019", true, "HETH_4019", "7"),
    JD_QSD("《职业病鉴定书》签收单", "4020", true, "HETH_4020", "7"),
    JD_QSD_TJ("《职业病鉴定书》送达回执", "4023", true, "HETH_4023", "7"),
    //职业病诊断鉴定结论报告书
    JD_CONCLUSION_RPT("职业病诊断鉴定结论报告书", "4024", true, "HETH_4024", "7"),
    JD_EXPERT_APPRAISAL_GROUP("职业病诊断鉴定专家鉴定组名单", "4025", true, "HETH_4025", "7"),
    //盲样考核
    MY_CHECK("盲样考核证书", "4030", true, "HETH_4030", null),
    //职业卫生技术服务机构盲样考核
    ZW_MY_CHECK("职业卫生技术服务机构盲样考核证书", "4031", true, "HETH_4031", null),

    CFB_JZDJ("尘肺病临床诊断申请登记表", "4050", true, "HETH_4050", "1"),
    CFB_JZDJCNS("尘肺病临床诊断申请承诺书", "4051", true, "HETH_4051", "1"),
    CFB_LCTLJL("尘肺病临床诊断讨论记录","4052",true,"HETH_4052","2"),
    CFB_LCZMS("尘肺病临床诊断疾病证明书","4053",true,"HETH_4053","2"),
    QUE_EXAM("职业卫生专业技术人员集中理论考试人员报名表","4054",true,"HETH_4054",null);
    private WritTypeEnum(String writName, String writCode, boolean isMain, String writRptCode, String dealStep) {
        this.writName = writName;
        this.writCode = writCode;
        this.isMain = isMain;
        this.writRptCode = writRptCode;
        this.dealStep = dealStep;
    }

    private String writName;
    private String writCode;
    /**
     * 是否主要文书 true为主要文书，在流程中制作 false为次要位数，需要弹出页制作
     */
    private boolean isMain;
    /**
     * 文书模版编号
     */
    private String writRptCode;
    private String dealStep;

    public String getWritName() {
        return writName;
    }

    public void setWritName(String writName) {
        this.writName = writName;
    }

    public String getWritCode() {
        return writCode;
    }

    public void setWritCode(String writCode) {
        this.writCode = writCode;
    }

    public boolean isMain() {
        return isMain;
    }

    public void setMain(boolean isMain) {
        this.isMain = isMain;
    }

    public String getWritRptCode() {
        return writRptCode;
    }

    public void setWritRptCode(String writRptCode) {
        this.writRptCode = writRptCode;
    }

    public String getDealStep() {
        return dealStep;
    }

    public void setDealStep(String dealStep) {
        this.dealStep = dealStep;
    }
}
