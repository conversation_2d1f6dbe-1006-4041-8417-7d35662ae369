package com.chis.modules.system.enumn;

/**
 * 消息类型
 * 
 * <AUTHOR>
 */
public enum MessageType {

	INFO((short) 0) {
		public String getTypeCN() {
			return "寻呼消息";
		}
	},

	ACTIVITI((short) 1) {
		public String getTypeCN() {
			return "流程消息";
		}
	},
	
	EMERG((short)2) {
		public String getTypeCN() {
			return "应急任务";
		}
	},

	QUARTZ((short)3) {
		public String getTypeCN() {
			return "定时任务提醒消息";
		}
	},
	
	WARN((short)4) {
		public String getTypeCN() {
			return "预警信息";
		}
	},
	
	COMM_TASK((short) 5) {
		public String getTypeCN() {
			return "待办任务";
		}
	},
	
	NOTICE((short) 6) {
		public String getTypeCN() {
			return "通知公告";
		}
	},
	
	 DISPATCH((short) 7) {
		public String getTypeCN() {
			return "分发";
		}
	};

	private final Short typeNo;

	MessageType(Short typeNo) {
		this.typeNo = typeNo;
	}

	public String toString() {
		return String.valueOf(typeNo);
	}

	public Short getTypeNo() {
		return typeNo;
	}

	public abstract String getTypeCN();

}
