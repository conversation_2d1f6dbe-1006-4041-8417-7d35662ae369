package com.chis.modules.system.enumn;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

public enum EchartsType {

	BAR(0,"堆积柱状图"),
	LINE(1,"折线图"),
	PIE(2,"饼图");
	
	private Integer value;
	private String label;
	
	
	private EchartsType(Integer value, String label) {
		this.value = value;
		this.label = label;
	}
	public Integer getValue() {
		return value;
	}
	public void setValue(Integer value) {
		this.value = value;
	}
	public String getLabel() {
		return label;
	}
	public void setLabel(String label) {
		this.label = label;
	}
	
	/**
	 * 初始化查询匹配类型
	 */
	public static List<SelectItem> initEchartsType() {
		List<SelectItem> searchTypeList = new ArrayList<SelectItem>();
		EchartsType [] items = EchartsType.values();
		for (EchartsType s : items) {
			SelectItem itm = new SelectItem();
			itm.setValue(s.value);
			itm.setLabel(s.label);
			searchTypeList.add(itm);
		}
		return searchTypeList;
	}
}
