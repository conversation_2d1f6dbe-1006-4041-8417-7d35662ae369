package com.chis.modules.system.enumn;

/**
 * 动态表单字段类型
 * 
 * <AUTHOR>
 * @createDate 2015年12月9日
 */
public enum DynaFieldType {

	NVARCHAR2("NVARCHAR2") {
		public String getTypeCN() {
			return "字符型";
		}
	},

	INTEGER("INTEGER") {
		public String getTypeCN() {
			return "整数型";
		}
	},

	NUMBER("NUMBER") {
		public String getTypeCN() {
			return "小数型";
		}
	},

	DATE("DATE") {
		public String getTypeCN() {
			return "日期型";
		}
	},
	TIMESTAMP("TIMESTAMP") {
		public String getTypeCN() {
			return "日期时分秒型";
		}
	},
	CLOB("CLOB") {
		public String getTypeCN() {
			return "大字段类型";
		}
	};

	private final String typeNo;

	DynaFieldType(String typeNo) {
		this.typeNo = typeNo;
	}

	public String toString() {
		return typeNo;
	}

	public String getTypeNo() {
		return typeNo;
	}

	public abstract String getTypeCN();

}
