package com.chis.modules.system.enumn;

/**
 * 字段类型，用于系统参数
 * <AUTHOR>
 */
public enum FieldType {

	STRINGS((short)0) {
        public String getTypeCN() { return "字符串型";}
	},

	DATE((short)1) {
        public String getTypeCN() { return "日期型";}
	},

    SELECT_MANY((short)2) {
        public String getTypeCN() { return "多选类型";}
    },

    SELECT_ONE_MENU((short)3) {
        public String getTypeCN() { return "单选类型";}
    }

    ;

	private final Short typeNo;

	FieldType(Short typeNo) {
		this.typeNo = typeNo;
	} 
	
	public String toString() {return String.valueOf(typeNo);}
	
	
	public Short getTypeNo() {
		return typeNo;
	}

    public abstract String getTypeCN();

}
