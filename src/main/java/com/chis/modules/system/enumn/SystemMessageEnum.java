
package com.chis.modules.system.enumn;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.StringUtils;

import java.util.Objects;

/**
 * <p>Description：系统提示信息枚举 </p>
 * <p>Author： yzz 2025/3/5 </p>
 */
public enum SystemMessageEnum {

    // 通用提示
    SAVE_SUCCESS("保存成功！", true),
    SAVE_FAIL("保存失败！", false),
    TEMP_SAVE_SUCCESS("暂存成功！", true),
    TEMP_SAVE_FAIL("暂存失败！", false),
    ADD_SUCCESS("添加成功！", true),
    ADD_FAIL("添加失败！", false),
    SUBMIT_SUCCESS("提交成功！", true),
    SUBMIT_FAIL("提交失败！", false),
    CANCEL_SUCCESS("撤销成功！", true),
    CANCEL_FAIL("撤销失败！", false),
    DELETE_SUCCESS("删除成功！", true),
    DELETE_FAIL("删除失败！", false),
    ENABLE_SUCCESS("启用成功！", true),
    ENABLE_FAIL("启用失败！", false),
    DISABLE_SUCCESS("停用成功！", true),
    DISABLE_FAIL("停用失败！", false),
    MAKE_SUCCESS("制作成功！", true),
    MAKE_FAIL("制作失败！", false),
    SUM_SUCCESS("汇总成功！", true),
    SUM_FAIL("汇总失败！", false),
    ACTION_ERROR("操作异常！", false),
    GENERATE_SUCCESS("生成成功！", true),
    GENERATE_FAIL("生成失败！", false),
    // 导入、导出相关提示信息
    // 最后一个占位符可以替换成“。”或者“，请下载错误文件！”
    IMPORT_FILE_FAIL("导入失败！", false),
    IMPORT_SUCCESS("共导入%s条数据。成功%s条数据，失败0条数据！", true),
    IMPORT_FAIL("共导入%s条数据。成功%s条数据，失败%s条数据，请下载错误文件！", false),
    // 对照提示信息
    IMPORT_CONTRA_FAIL("%s【%s】在%s对照不存在！", false),
    // 导出
    EXPORT_SUCCESS("导出成功！", true),
    EXPORT_FAIL("导出失败！", false),
    EXPORT_NOT_DATA("无数据导出！", false),
    // 上传下载相关提示信息
    UPLOAD_SUCCESS("上传成功！", true),
    UPLOAD_FAIL("上传失败！", false),
    DOWNLOAD_FAIL("下载失败！", false),
    ATTACHMENT_NOT_EXIST("附件不存在，请重新上传！", false),
    FILE_NOT_EXIST("系统找不到指定的文件！", false),
    //文件导入相关提示信息
    IMPORT_EXCEPTION("文件导入异常！", false),
    IMPORT_EXCEL_NOT_DATA("Excel表格无数据，请重新选择文件导入！", false),
    IMPORT_EXCEL_SUCCESS("文件导入成功！", true),
    IMPORT_EXCEL_SUCCESS_REFRESH("文件导入成功，请稍后刷新页面！", true),
    // 验证相关提示信息
    NOT_EMPTY("%s不能为空！", false),
    LENGTH_VERIFY("%s长度不能超过%s！", false),
    PLEASE_SELECT("请选择%s！", false),
    PLEASE_UPLOAD("请上传%s！", false),
    PLEASE_MAKE("请制作%s！", false),
    FORMAT_DATA("%s格式不正确！", false),
    ALREADY_EXIST("%s已存在！", false),
    NOT_REPEAT("%s不能重复！", false),

    // 最后一个占位符可以替换成具体日期,
    GE_FOR_DATE_DETAIL("%s（%s）应大于等于%s（%s）！", false),
    LE_FOR_DATE_DETAIL("%s（%s）应小于等于%s（%s）！", false),
    GT_FOR_DATE_DETAIL("%s（%s）应大于%s（%s）！", false),
    LT_FOR_DATE_DETAIL("%s（%s）应小于%s（%s）！", false),
    // 数字类型相关提示信息
    GE_FOR_NUMBER("%s应大于等于%s！", false),
    LE_FOR_NUMBER("%s应小于等于%s！", false),
    GT_FOR_NUMBER("%s应大于%s！", false),
    LT_FOR_NUMBER("%s应小于%s！", false),

    // 动态行验证-头部有说明信息
    DYNAMIC_ROW_NOT_EMPTY("%s第%s行%s不能为空！", false),
    DYNAMIC_ROW_LENGTH_VERIFY("%s第%s行%s长度不能超过%s！", false),
    DYNAMIC_ROW_PLEASE_SELECT("%s请选择第%s行%s！", false),
    DYNAMIC_ROW_PLEASE_UPLOAD("%s请上传第%s行%s！", false),
    DYNAMIC_ROW_FORMAT_DATA("%s第%s行%s格式不正确！", false),
    //日期相关
    DYNAMIC_ROW_GE_FOR_DATE("%s第%s行%s（%s）应大于等于%s（%s）！", false),
    DYNAMIC_ROW_GT_FOR_DATE("%s第%s行%s（%s）应大于%s（%s）！", false),
    DYNAMIC_ROW_LE_FOR_DATE("%s第%s行%s（%s）应小于等于%s（%s）！", false),
    DYNAMIC_ROW_LT_FOR_DATE("%s第%s行%s（%s）应小于%s（%s）！", false),
    //数值相关
    DYNAMIC_ROW_GE_FOR_NUMBER("%s第%s行%s应大于等于%s！", false),
    DYNAMIC_ROW_GT_FOR_NUMBER("%s第%s行%s应大于%s！", false),
    DYNAMIC_ROW_LE_FOR_NUMBER("%s第%s行%s应小于等于%s！", false),
    DYNAMIC_ROW_LT_FOR_NUMBER("%s第%s行%s应小于%s！", false),

    // 动态行验证-头部无说明信息
    DYNAMIC_HEAD_NOT_EMPTY("第%s行%s不能为空！", false),
    DYNAMIC_HEAD_LENGTH_VERIFY("第%s行%s长度不能超过%s！", false),
    DYNAMIC_HEAD_PLEASE_SELECT("请选择第%s行%s！", false),
    DYNAMIC_HEAD_PLEASE_UPLOAD("请上传第%s行%s！", false),
    DYNAMIC_HEAD_FORMAT_DATA("第%s行%s格式不正确！", false),
    DYNAMIC_HEAD_NOT_REPEAT("第%s行%s不能重复！", false),
    //日期相关
    DYNAMIC_HEAD_GE_FOR_DATE("第%s行%s（%s）应大于等于%s（%s）！", false),
    DYNAMIC_HEAD_GT_FOR_DATE("第%s行%s（%s）应大于%s（%s）！", false),
    DYNAMIC_HEAD_LE_FOR_DATE("第%s行%s（%s）应小于等于%s（%s）！", false),
    DYNAMIC_HEAD_LT_FOR_DATE("第%s行%s（%s）应小于%s（%s）！", false),
    //数值相关
    DYNAMIC_HEAD_GE_FOR_NUMBER("第%s行%s应大于等于%s！", false),
    DYNAMIC_HEAD_GT_FOR_NUMBER("第%s行%s应大于%s！", false),
    DYNAMIC_HEAD_LE_FOR_NUMBER("第%s行%s应小于等于%s！", false),
    DYNAMIC_HEAD_LT_FOR_NUMBER("第%s行%s应小于%s！", false),

    // 审核相关提示信息
    CHECK_SUCCESS("审核通过成功！", true),
    CHECK_FAIL("审核通过失败！", false),
    CHECK_FAIL_SUCCESS("审核不通过成功！", true),
    CHECK_FAIL_FAIL("审核不通过失败！", false),
    CHECK_BACK_SUCCESS("审核退回成功！", true),
    CHECK_BACK_FAIL("审核退回失败！", false),
    CHOOSE_FAIL("请选择%s的数据！", false),
    CHECK_BATCH_SUCCESS("批量审核成功！", true),
    CHECK_BATCH_FAIL("批量审核失败！", false),
    CHECK_ALL_FAIL("全部审核失败！", false),
    CHECK_LOADING("审核中，请耐心等待！", true),
    CHECK_FOR_WAITING("存在正在审核的数据，请耐心等待！", true),
    CHECK_N_FOR_WAITING("存在%s条正在审核的数据，请耐心等待！", true),

    //状态
    CHECK_STATUS_CHANGE("状态已发生改变，请刷新页面！", false),
    //业务数据不存在，不可操作
    DATA_NOT_EXIST("业务数据不存在，请刷新页面！", false),

    // 维护异常 提示
    MAINTENANCE_EMPTY("%s未维护，请联系管理员！", false),
    MAINTENANCE_ERROR("%s维护异常，请联系管理员！", false);

    /**
     * 提示信息
     */
    private final String message;
    /**
     * 标记 true:成功提示信息  false: 错误提示信息
     */
    private final boolean ifSuccess;

    SystemMessageEnum(String message, boolean ifSuccess) {
        this.message = message;
        this.ifSuccess = ifSuccess;
    }


    /**
     * <p>Description：无需传参</p>
     * <p>@Param mark:1:成功提示  2：失败提示 </p>
     * <p>Author： yzz 2025/3/5 </p>
     */
    public void showMessage() {
        Objects.requireNonNull(message, "提示信息不能为空！");
        //包含占位符
        if(message.contains("%s")){
            addSystemMessage("提示异常！", false); // 返回错误信息
            return;
        }
        if (StringUtils.isNotBlank(message)) {
            addSystemMessage(message, ifSuccess);
        }
    }


    /**
     * <p>Description：需要动态入参</p>
     * <p>@Param mark:1:成功提示  2：失败提示 </p>
     * <p>Author： yzz 2025/3/5 </p>
     */
    public void formatMessage(Object... args) {
        Objects.requireNonNull(message, "提示信息不能为空！");
        // 检查 args 中是否存在 null 或空字符串
        if (hasNullOrEmpty(args)) {
            addSystemMessage("提示异常！", false); // 返回错误信息
            return;
        }
        String resultMsg;
        try {
            // 使用 String.format 进行格式化
            resultMsg = String.format(message, args);
        } catch (Exception e) {
            // 捕获异常并返回原始模板，避免程序崩溃
            System.err.println("格式化消息时出错：" + e.getMessage());
            resultMsg = "提示异常！";
        }
        if (StringUtils.isNotBlank(resultMsg)) {
            addSystemMessage(resultMsg, ifSuccess);
        }
    }

    /**
     * <p>Description：弹出提示信息 </p>
     * <p>Author： yzz 2025/3/5 </p>
     */
    private void addSystemMessage(String msg, boolean isSuccess) {
        if (isSuccess) {
            JsfUtil.addSuccessMessage(msg);
        } else {
            JsfUtil.addErrorMessage(msg);
        }
    }

    /**
     * 检查 args 中是否存在 null 或空字符串
     */
    private boolean hasNullOrEmpty(Object... args) {
        if (args == null || args.length == 0) {
            return true; // 如果没有参数，不需要检查
        }
        for (Object arg : args) {
            if (arg == null || (arg instanceof String && ((String) arg).isEmpty())) {
                return true; // 存在 null 或空字符串
            }
        }
        return false; // 没有 null 或空字符串
    }

}