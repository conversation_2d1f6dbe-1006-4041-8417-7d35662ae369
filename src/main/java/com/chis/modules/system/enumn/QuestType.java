package com.chis.modules.system.enumn;

import java.util.ArrayList;
import java.util.List;

/**
 * 模块描述：题目类型
 * 
 * <AUTHOR>
 * @createDate 2016年3月26日
 */
public enum QuestType {

	SELECT_ONE(0) {
		public String getTypeCN() {
			return "单选";
		}
	},

	SELECT_MANY(1) {
		public String getTypeCN() {
			return "多选题";
		}
	},

	SLIDE(2) {
		public String getTypeCN() {
			return "滑动题";
		}
	},

	EVALUATE(3) {
		public String getTypeCN() {
			return "大文本题";
		}
	},

	FILL_BLANK(4) {
		public String getTypeCN() {
			return "填空题";
		}
	},

	TITLE(5) {
		public String getTypeCN() {
			return "标题";
		}
	},

	DATE_FILL_BLANK(6) {
		public String getTypeCN() {
			return "日期填空题";
		}
	},

	INTEGER_FILL_BLANK(7) {
		public String getTypeCN() {
			return "整数填空题";
		}
	},

	PULL_DOWN_SELECT_ONE(8) {
		public String getTypeCN() {
			return "下拉单选题";
		}
	},
	NUMBER_FILL_BLANK(9) {
		public String getTypeCN() {
			return "数字填空题";
		}
	},
	TRUE_OR_FALSES(10) {
		public String getTypeCN() {
			return "是非题";
		}
	},
	TABLE(11) {
		public String getTypeCN() {
			return "表格题";
		}
	},
	READONLY(12) {
		public String getTypeCN() {
			return "只读显示";
		}
	},
	SCORE(13) {
		public String getTypeCN() {
			return "评分";
		}
	},
	SHOW_SCORE(14) {
		public String getTypeCN() {
			return "显示得分和参考答案";
		}
	}
	;// 如果新增的题目类型在题库管理中不需要 需要将编码加入到getExcludeTypeNo

	private final Integer typeNo;

	QuestType(Integer typeNo) {
		this.typeNo = typeNo;
	}

	public Integer getTypeNo() {
		return typeNo;
	}

	public abstract String getTypeCN();

	/**
	 * <p>方法描述：题库管理的题型需要排除的题目类型 </p>
	 * pw 2024/10/10
	 **/
	public static List<Integer> getExcludeTypeNo() {
		List<Integer> resultList = new ArrayList<>();
		resultList.add(5);
		resultList.add(8);
		resultList.add(13);
		resultList.add(14);
		return resultList;
	}

}
