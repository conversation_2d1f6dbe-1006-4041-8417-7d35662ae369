package com.chis.modules.system.enumn;

/**
 * 模块描述：生成HTML页面类型
 * 
 * <AUTHOR>
 * @createDate 2016年3月26日
 */
public enum GenHtmlType {

	WEB_HTML_PAGE(0) {
		public String getTypeCN() {
			return "网页版";
		}
	},

	MOBILE_HTML_PAGE(1) {
		public String getTypeCN() {
			return "手机版";
		}
	};

	private final Integer typeNo;

	GenHtmlType(Integer typeNo) {
		this.typeNo = typeNo;
	}

	public Integer getTypeNo() {
		return typeNo;
	}

	public abstract String getTypeCN();

}
