package com.chis.modules.system.enumn;

/**
 * 码表类型
 * <AUTHOR>
 */
public enum CodeType {

	MZ("1002") {
        public String getTypeCN() { return "民族";}
	},

	ZHW("2002") {
        public String getTypeCN() { return "职务";}
	},

	ZHCH("2003") {
        public String getTypeCN() { return "职称";}
	},

    XL("1001") {
        public String getTypeCN() { return "学历";}
    },

    ZHZHMM("2005") {
        public String getTypeCN() { return "政治面貌";}
    },

    HYZHK("1003") {
        public String getTypeCN() { return "婚姻状况";}
    },

    ZJXY("2007") {
        public String getTypeCN() { return "宗教信仰";}
    },

    RYSX("2008") {
        public String getTypeCN() { return "人员属性";}
    },
    ZY("2010") {
        public String getTypeCN() { return "职业";}
    },
    HJLX("2011") {
        public String getTypeCN() { return "户籍类型";}
    },
    JSLX("1201") {
        public String getTypeCN() { return "角色类型";}
    }
    ;

	private final String typeNo;

	CodeType(String typeNo) {
		this.typeNo = typeNo;
	} 
	
	public String toString() {return typeNo;}
	
	public String getTypeNo() {
		return typeNo;
	}

    public abstract String getTypeCN();

}






