package com.chis.modules.system.enumn;
/**
 * <p>类描述：资质人员培训证书</p>
 * @ClassAuthor qrr,2018年5月24日,PxWritTypeEnum
 * */
public enum PxWritTypeEnum {
	ZYB_ZDYS("职业病诊断医师","3001","HETH_3001","1001"),
	ZYB_JDZJ("职业病鉴定专家","3002","HETH_3002","1002"),
	ZY_FSYXYS("职业放射影像医师","3003","HETH_3003","1003"),
	ZY_JYJS("服务机构实验室人员","3004","HETH_3004","1004"),
	ZY_WSXXBGY("职业病信息报告人员","3005","HETH_3005","1005"),
	FSZYJSRY("放射专业技术人员","3006","HETH_3006","1006"),
	FSGZRY("放射工作人员","3007","HETH_3007","1007");

	private PxWritTypeEnum(String writName,String writCode,String writRptCode,String pxCodeNo){
		this.writName = writName;
		this.writCode = writCode;
		this.writRptCode = writRptCode;
		this.pxCodeNo = pxCodeNo;
	}
	//文书名称
	private String writName;
	//文书类型
	private String writCode;
	//报表模板类型
	private String writRptCode;
	//培训类型
	private String pxCodeNo;

	public String getWritName() {
		return writName;
	}


	public void setWritName(String writName) {
		this.writName = writName;
	}

	public String getWritRptCode() {
		return writRptCode;
	}


	public void setWritRptCode(String writRptCode) {
		this.writRptCode = writRptCode;
	}


	public String getPxCodeNo() {
		return pxCodeNo;
	}


	public void setPxCodeNo(String pxCodeNo) {
		this.pxCodeNo = pxCodeNo;
	}


	public String getWritCode() {
		return writCode;
	}


	public void setWritCode(String writCode) {
		this.writCode = writCode;
	}

}
