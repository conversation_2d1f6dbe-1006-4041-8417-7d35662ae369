package com.chis.modules.system.enumn;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

public enum ConvergeType {

	COUNT(1,"count"," COUNT( "),
	DISTINCT_COUNT(2,"distinct-count"," COUNT(DISTINCT "),
	MAX(3,"max"," MAX( "),
	MIN(4,"min"," MIN( "),
	SUM(5,"sum"," SUM( "),
	AVG(6,"avg"," AVG( ");
	
	private Integer realValue;
	private String showName;
	private String realName;
	private ConvergeType(Integer realValue, String showName, String realName) {
		this.realValue = realValue;
		this.showName = showName;
		this.realName = realName;
	}
	public Integer getRealValue() {
		return realValue;
	}
	public void setRealValue(Integer realValue) {
		this.realValue = realValue;
	}
	public String getShowName() {
		return showName;
	}
	public void setShowName(String showName) {
		this.showName = showName;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	
	public static String getRealNameByRealValue(Integer data){
		String result = "";
		for(ConvergeType s : ConvergeType.values()){
			if(!s.getRealValue().equals(data))
				continue;
			result = s.realName;
			break;
		}
		return result;
	}
	
	/**
	 * 初始化查询匹配类型
	 */
	public static List<SelectItem> initConvergeType() {
		List<SelectItem> searchTypeList = new ArrayList<SelectItem>();
		ConvergeType [] items = ConvergeType.values();
		for (ConvergeType s : items) {
			SelectItem itm = new SelectItem();
			itm.setValue(s.realValue);
			itm.setLabel(s.showName);
			searchTypeList.add(itm);
		}
		return searchTypeList;
	}
	
	
}
