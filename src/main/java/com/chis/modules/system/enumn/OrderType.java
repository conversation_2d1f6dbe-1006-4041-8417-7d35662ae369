package com.chis.modules.system.enumn;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

public enum OrderType {

	ASC(1,"ASC"," ASC "),
	DESC(2,"DESC"," DESC ");
	
	private Integer value;
	private String showName;
	private String realName;
	
	private OrderType(Integer value, String showName, String realName) {
		this.value = value;
		this.showName = showName;
		this.realName = realName;
	}
	public Integer getValue() {
		return value;
	}
	public void setValue(Integer value) {
		this.value = value;
	}
	public String getShowName() {
		return showName;
	}
	public void setShowName(String showName) {
		this.showName = showName;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	
	public static String getRealNameByValue(Integer data){
		String result = "";
		for(OrderType s : OrderType.values()){
			if(!s.getValue().equals(data))
				continue;
			result = s.realName;
			break;
		}
		return result;
	}
	
	/**
	 * 初始化查询匹配类型
	 */
	public static List<SelectItem> initOrderType() {
		List<SelectItem> searchTypeList = new ArrayList<SelectItem>();
		OrderType [] items = OrderType.values();
		for (OrderType s : items) {
			SelectItem itm = new SelectItem();
			itm.setValue(s.value);
			itm.setLabel(s.showName);
			searchTypeList.add(itm);
		}
		return searchTypeList;
	}
	
	
}
