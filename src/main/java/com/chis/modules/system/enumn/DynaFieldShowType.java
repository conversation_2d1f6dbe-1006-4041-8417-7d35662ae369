package com.chis.modules.system.enumn;

/**
 * 动态表单字段展示类型
 * 
 * <AUTHOR>
 * @createDate 2015年12月9日
 */
public enum DynaFieldShowType {
	INPUT("input") {
		public String getTypeCN() {
			return "文本框";
		}
	},

	DICT_SELECT_ONE("dict_select_one") {
		public String getTypeCN() {
			return "单选框";
		}
	},

	TEXTAREA("textarea") {
		public String getTypeCN() {
			return "多行文本框";
		}
	},

	DATE("date") {
		public String getTypeCN() {
			return "日期框";
		}
	},
	LABEL("label") {
		public String getTypeCN() {
			return "输出框";
		}
	},
	FJ("fj") {
		public String getTypeCN() {
			return "附件";
		}
	},
	DICT_SELECT_MANY("dict_select_many") {
		public String getTypeCN() {
			return "多选框";
		}
	}

	;

	private final String typeNo;

	DynaFieldShowType(String typeNo) {
		this.typeNo = typeNo;
	}

	public String toString() {
		return typeNo;
	}

	public String getTypeNo() {
		return typeNo;
	}

	public abstract String getTypeCN();
}
