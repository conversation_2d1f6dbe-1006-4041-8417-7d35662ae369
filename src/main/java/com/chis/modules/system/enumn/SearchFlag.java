package com.chis.modules.system.enumn;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

public enum SearchFlag {
	EQ(0," = "),
	GT(1," > "),
	EGT(2," >= "),
	LT(3," < "),
	ELT(4," <= "),
	LI<PERSON>(5," LIKE "),
	BETWEEN(6," BETWEEN ");
	
	private Integer flag;
	private String realFlag;

	private SearchFlag(Integer flag, String realFlag) {
		this.flag = flag;
		this.realFlag = realFlag;
	}

	public Integer getFlag() {
		return flag;
	}

	public void setFlag(Integer flag) {
		this.flag = flag;
	}

	public String getRealFlag() {
		return realFlag;
	}

	public void setRealFlag(String realFlag) {
		this.realFlag = realFlag;
	}
	
	/**
	 * 初始化查询匹配类型
	 */
	public static List<SelectItem> initSearchType() {
		List<SelectItem> searchTypeList = new ArrayList<SelectItem>();
		SearchFlag [] searchs = SearchFlag.values();
		for (SearchFlag s : searchs) {
			SelectItem itm = new SelectItem();
			itm.setValue(s.getFlag());
			itm.setLabel(s.getRealFlag());
			searchTypeList.add(itm);
		}
		return searchTypeList;
	}
	
	
}
