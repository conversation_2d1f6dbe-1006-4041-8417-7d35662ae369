package com.chis.modules.system.enumn;

public enum LogNoEnum {

	NO_1001("1001") {
        public String getTypeCN() { return "登录系统";}
	},
	
	NO_1002("1002") {
        public String getTypeCN() { return "退出系统";}
	},
	
	NO_2001("2001") {
        public String getTypeCN() { return "编辑";}
	},
	
	NO_2002("2002") {
        public String getTypeCN() { return "删除";}
	},
	
	NO_2003("2003") {
        public String getTypeCN() { return "查看";}
	},
	
	NO_2004("2004") {
        public String getTypeCN() { return "其它";}
	},
	
	NO_3001("3001") {
        public String getTypeCN() { return "普通异常";}
	},
	
	NO_3002("3002") {
		public String getTypeCN() { return "未知异常";}
	}
	;
	
	private final String typeNo;

	LogNoEnum(String typeNo) {
		this.typeNo = typeNo;
	} 
	
	public String toString() {return typeNo;}
	
	public String getTypeNo() {
		return typeNo;
	}

    public abstract String getTypeCN();
	
}
