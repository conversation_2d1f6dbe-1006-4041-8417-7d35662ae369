package com.chis.modules.system.enumn;

import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

public enum MathType {

	TOTAL(0,"合计"),
	PERCENT(1,"百分比");
	
	private Integer value;
	private String label;
	
	private MathType(Integer value, String label) {
		this.value = value;
		this.label = label;
	}

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	/**
	 * 初始化查询匹配类型
	 */
	public static List<SelectItem> initOrderType() {
		List<SelectItem> searchTypeList = new ArrayList<SelectItem>();
		MathType [] items = MathType.values();
		for (MathType s : items) {
			SelectItem itm = new SelectItem();
			itm.setValue(s.value);
			itm.setLabel(s.label);
			searchTypeList.add(itm);
		}
		return searchTypeList;
	}
}
