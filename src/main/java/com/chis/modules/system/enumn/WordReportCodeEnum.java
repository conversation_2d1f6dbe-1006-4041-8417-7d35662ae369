package com.chis.modules.system.enumn;
/**
 * @Description: word模板报告类型 枚举
 *
 * @ClassAuthor pw,2022年04月24日,WordReportCodeEnum
 */
public enum WordReportCodeEnum {
    HETH_JC_1001("HETH_JC_1001",null,"职业病危害因素监测报告"),
    HETH_JC_1001_CQ("HETH_JC_1001","/word/generate_cq","重庆职业病危害因素监测报告"),
    HETH_ZK_1001("HETH_ZK_1001",null,"职业健康检查机构检查考核意见反馈表"),
    HETH_ZK_1002("HETH_ZK_1002",null,"职业健康检查机构质量控制考核结果表"),
    HETH_BAHZ_1001("HETH_BAHZ_1001",null,"职业健康检查机构备案回执"),
    HETH_ZYB_DIAG_2001("HETH_ZYB_DIAG_2001","/word/generate_cq","职业病诊断就诊登记表"),
    HETH_ZYB_DIAG_2003("HETH_ZYB_DIAG_2003","/word/generate_cq","关于提请协助开展职业病诊断有关工作的函"),
    HETH_ZYB_DIAG_2010("HETH_ZYB_DIAG_2010","/word/generate_cq","职业病诊断证明书");
    //报告编码
    private String rptCode;
    //请求地址 如果空 默认word/generate
    private String requestUrl;
    //描述
    private String desc;
    WordReportCodeEnum(String rptCode, String requestUrl, String desc){
        this.rptCode = rptCode;
        this.requestUrl = requestUrl;
        this.desc = desc;
    }

    public String getRptCode() {
        return rptCode;
    }

    public void setRptCode(String rptCode) {
        this.rptCode = rptCode;
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
