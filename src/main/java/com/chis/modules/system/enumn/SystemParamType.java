package com.chis.modules.system.enumn;

/**
 * 系统参数类型
 * <AUTHOR>
 */
public enum SystemParamType {

    SYSTEM_TYPE("SYSTEM_TYPE") {
        public String getTypeCN() { return "当前的应用系统";}
	},

    STRONG_PASSWORD("STRONG_PASSWORD") {
        public String getTypeCN() { return "是否启用超强密码（0：否，1：是）";}
    },
    
    CREDIT_CODE("CREDIT_CODE") {
        public String getTypeCN() { return "是否启用登录验证社会信用代码正确性（0：否，1：是）";}
    },

    APP_TITLE("APP_TITLE") {
        public String getTypeCN() { return "应用程序显示的标题";}
	},

    SYS_LFILENAME("SYSLFileName") {
        public String getTypeCN() { return "系统全文搜索的文件夹名";}
    },

    SYSTEM_MODULES("SYSTEM_MODULES") {
        public String getTypeCN() { return "当前平台包含的模块";}
    },
    WECHAT_PUSHMODE("WECHAT_PUSHMODE"){
    	 public String getTypeCN() { return "是否启用随访计划微信分组推送模式（0：否，1：是）";}
    },
    SCH_DOCTOR_ROLE("SCH_DOCTOR_ROLE"){
   	 public String getTypeCN() { return "校医用户角色";}
    },
    SYS_ONE_LOGIN_MANY("SYS_ONE_LOGIN_MANY"){
        public String getTypeCN() { return "同一用户是否允许登录多次";}
    }
    ;

	private final String typeNo;

	SystemParamType(String typeNo) {
		this.typeNo = typeNo;
	} 
	
	public String toString() {return typeNo;}
	
	public String getTypeNo() {
		return typeNo;
	}

    public abstract String getTypeCN();

}






