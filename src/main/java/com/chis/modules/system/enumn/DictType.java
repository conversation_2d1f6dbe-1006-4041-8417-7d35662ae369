package com.chis.modules.system.enumn;

/**
 * 字典类型，用于系统参数
 * <AUTHOR>
 */
public enum DictType {

	NO_NEED((short)0) {
        public String getTypeCN() { return "不需要";}
	},

	SELF_DEFINE((short)1) {
        public String getTypeCN() { return "自定义型";}
	},

    SQL((short)2) {
        public String getTypeCN() { return "数据库SQL";}
    },

    ENUMN((short)3) {
        public String getTypeCN() { return "枚举类";}
    }

    ;

	private final Short typeNo;

	DictType(Short typeNo) {
		this.typeNo = typeNo;
	} 
	
	public String toString() {return String.valueOf(typeNo);}
	
	
	public Short getTypeNo() {
		return typeNo;
	}

    public abstract String getTypeCN();

}
