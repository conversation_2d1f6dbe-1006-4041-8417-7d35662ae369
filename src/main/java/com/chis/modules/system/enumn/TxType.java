package com.chis.modules.system.enumn;

/**
 * 通讯方式
 * <AUTHOR> 2014-11-27 
 */
public enum TxType {

	DX("0") {
        public String getTypeCN() { return "短信";}
	},

	APP("1") {
        public String getTypeCN() { return "APP推送";}
	},

	YY("2") {
        public String getTypeCN() { return "语音";}
	},
	EMAIL("3") {
        public String getTypeCN() { return "邮箱";}
	}
	
    ;

	private final String typeNo;

	TxType(String typeNo) {
		this.typeNo = typeNo;
	} 
	
	public String toString() {return typeNo;}
	
	public String getTypeNo() {
		return typeNo;
	}

    public abstract String getTypeCN();

}






