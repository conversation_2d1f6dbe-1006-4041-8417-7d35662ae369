package com.chis.modules.system.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsSystemParam;
import com.chis.modules.system.enumn.DictType;
import com.chis.modules.system.enumn.FieldType;
import com.chis.modules.system.enumn.SystemParamType;
import com.chis.modules.system.enumn.SystemType;

/**
 * 系统参数的插件
 * <AUTHOR>
 */
public class SystemParamPluginObj {

	public static Set<TsSystemParam> paramSet;
	
	static {
		paramSet = new HashSet<TsSystemParam>();
		/**
		 * 创建2014-04-17 zww
		 * 修改2014-04-18 zww
		 */
		paramSet.add(new TsSystemParam(SystemType.COMM, SystemParamType.STRONG_PASSWORD.getTypeNo(), "0",
                SystemParamType.STRONG_PASSWORD.getTypeCN(), Short.valueOf("1"),"SFQYCQMM", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
		/**
		 * 创建2014-05-14 zww
		 */
		paramSet.add(new TsSystemParam(SystemType.COMM, SystemParamType.APP_TITLE.getTypeNo(),
                "疾控综合决策辅助管理平台", SystemParamType.APP_TITLE.getTypeCN(), Short.valueOf("1"),"YYCXXSDBT", new Date(), 1,
                FieldType.STRINGS, DictType.NO_NEED, null));
        /**
         * 创建2014-07-01 hy
         */
        paramSet.add(new TsSystemParam(SystemType.COMM, SystemParamType.SYS_LFILENAME.getTypeNo(),
                "FilesUpload", SystemParamType.SYS_LFILENAME.getTypeCN(), Short.valueOf("1"),"SYSLFileName", new Date(), 1,
                FieldType.STRINGS, DictType.NO_NEED, null));

        /**
         * 创建2014-8-13 zww
         */
        paramSet.add(new TsSystemParam(SystemType.COMM, SystemParamType.SYSTEM_MODULES.getTypeNo(),
                "0", SystemParamType.SYSTEM_MODULES.getTypeCN(), Short.valueOf("1"),"dqptbhdmk", new Date(), 1,
                FieldType.SELECT_MANY, DictType.ENUMN, "com.chis.modules.system.enumn.SystemType"));
        
        /**
         * 创建2014-8-13 zww
         */
    	paramSet.add(new TsSystemParam(SystemType.COMM, "PAGE_REFRESH",
                "30", "页面ajax请求时间间隔", Short.valueOf("1"),"qqsjjg", new Date(), 1,
                FieldType.STRINGS, DictType.NO_NEED, null));
        
        /**
         * 创建2016-5-09 xt
         */
    	paramSet.add(new TsSystemParam(SystemType.COMM, "COMM_PT_VISIT_URL",
                "http://10.88.99.202:8095", "当前系统平台访问地址", Short.valueOf("1"),"comm_pt_visit_url", new Date(), 1,
                FieldType.STRINGS, DictType.NO_NEED, null));
    	
    	/**
    	 * 创建2016-06-02 xt
    	 */
    	paramSet.add(new TsSystemParam(SystemType.COMM, "WEB_XN_PATH",
    			"E:/webFile/", "当前系统虚拟路径地址", Short.valueOf("1"),"webxnpath", new Date(), 1,
    			FieldType.STRINGS, DictType.NO_NEED, null));
    	
		/**
		 * 是否启用门户多选，默认多选
		 */
		paramSet.add(new TsSystemParam(SystemType.COMM, "PORTAL_MANY_SELECT", "1",
                "是否启用功能图标门户多选，默认多选", Short.valueOf("1"),"portal_many_select", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
		
		/**
		 * 待办任务对应栏目
		 */
		String sql="SELECT T1.COL_NAME, T1.RID FROM TD_PORTAL_COLUMN T1";
		paramSet.add(new TsSystemParam(SystemType.COMM, "SYSTEM_TODO_COLUMN", "1",
                "待办任务对应栏目", Short.valueOf("1"),"system_todo_column", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SQL, sql));
		
		paramSet.add(new TsSystemParam(SystemType.COMM,"PASSWORD_ISLOGIN", "1",
				"判断初始密码是否强制修改",Short.valueOf("1"), "mmsfqzdl",new Date(), 1,FieldType.SELECT_ONE_MENU, 
				DictType.SELF_DEFINE, "是@@0##否@@1"));
		
		paramSet.add(new TsSystemParam(SystemType.COMM,"FLOWINFO_SHOWPLACE", "1",
				"流程信息显示位置是否显示在上方",Short.valueOf("1"), "lcxxxswzsfxszsf",new Date(), 1,FieldType.SELECT_ONE_MENU, 
				DictType.SELF_DEFINE, "是@@1##否@@0"));
		paramSet.add(new TsSystemParam(SystemType.COMM, "FAST_REPORT_VERSION", "20170802", "报表插件版本号",
				Short.valueOf("1"),"bbcjbbh",new Date(), 1,FieldType.STRINGS, DictType.NO_NEED, "bbcjbbh"));
		
		/**
		 * 职业病诊断证明书 备注
		 */
		/*paramSet.add(new TsSystemParam(SystemType.COMM, "DIAG_PROVE_BAK", "如对本诊断结论有异议，可在接到本证明书三十日内向                省（区，市）              市（区）卫生计生委申请社区的市级职业病鉴定。", "职业病诊断证明书备注",
				Short.valueOf("1"),"zybzdzmsbz",new Date(), 1,FieldType.STRINGS, DictType.NO_NEED, "zybzdzmsbz"));*/
		/**登录系统时社会信用代码校验*/
		paramSet.add(new TsSystemParam(SystemType.COMM, SystemParamType.CREDIT_CODE.getTypeNo(), "1",
                SystemParamType.CREDIT_CODE.getTypeCN(), Short.valueOf("1"),"shxydm", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
		
		/**单位存在分支机构*/
		paramSet.add(new TsSystemParam(new Integer(SystemType.COMM.getTypeNo()), "IF_SUB_ORG", "0",
                "系统单位是否存在分支机构（0：否，1：是）", Short.valueOf("1"),"XTDWSFCZFZJG", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
        /**是否有国家上传工具*/
		paramSet.add(new TsSystemParam(new Integer(SystemType.COMM.getTypeNo()), "IF_HAVE_GS_UPLOAD", "1",
                "是否有国家上传工具（0：否，1：是）", Short.valueOf("1"),"SFYGJSCGJ", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
        /**敏感信息是否加密 【四川职业】体检信息录入“证件号码、联系电话”字段数据库存储加密*/
		paramSet.add(new TsSystemParam(new Integer(SystemType.COMM.getTypeNo()), "IF_INFO_ENCRY", "0",
                "敏感信息是否加密（0：否，1：是）", Short.valueOf("1"),"MGXXJM", new Date(), 1, FieldType.SELECT_ONE_MENU,
                DictType.SELF_DEFINE, "否@@0##是@@1"));
        /**敏感信息加密Key*/
		paramSet.add(new TsSystemParam(new Integer(SystemType.COMM.getTypeNo()), "INFO_ENCRY_KEY",
				"00d11fd69c844272", "敏感信息加密Key", Short.valueOf("1"),"MGXXJMKEY", new Date(), 1,
				FieldType.STRINGS, DictType.NO_NEED, null));
        //江苏在线培训管理第三方系统地址
		paramSet.add(new TsSystemParam(new Integer(SystemType.COMM.getTypeNo()), "ONLINE_LEARNING_SERVER_URL",
				"http://", "江苏在线培训管理第三方系统地址", Short.valueOf("1"),"online_learning_server_url", new Date(), 1,
				FieldType.STRINGS, DictType.NO_NEED, null));
		//江苏在线培训管理第三方系统外网地址
		paramSet.add(new TsSystemParam(new Integer(SystemType.COMM.getTypeNo()), "ONLINE_LEARNING_SERVER_OUT_URL",
				"http://", "江苏在线培训管理第三方系统外网地址", Short.valueOf("1"),"online_learning_server_out_url", new Date(), 1,
				FieldType.STRINGS, DictType.NO_NEED, null));
		//修改密码 密码的有效天数
		paramSet.add(new TsSystemParam(new Integer(SystemType.COMM.getTypeNo()), "PWS_LIVE_DAY", "90",
				"密码有效期天数", Short.valueOf("1"),"MMYXQ", new Date(), 1, FieldType.STRINGS,
				DictType.NO_NEED, null));
		//是否开启登录验证密码有效性 1: 是 0 否 默认为0
		paramSet.add(new TsSystemParam(SystemType.COMM.getTypeNo().intValue(), "IF_PWS_LIVE", "0", "是否开启登录验证密码有效性（0：否，1：是）", Short.valueOf("1"), "ifpwslive", new Date(), 1, FieldType.STRINGS, DictType.NO_NEED,
				null));
		//异步导出文件保留月数 参数配置为整数
		paramSet.add(new TsSystemParam(SystemType.COMM.getTypeNo().intValue(), "EXPORT_FILE_LIVEMONTH", "0", "异步导出文件保留月数", Short.valueOf("1"), "exportFileLivemonth", new Date(), 1, FieldType.STRINGS, DictType.NO_NEED,
				null));
    }

}
