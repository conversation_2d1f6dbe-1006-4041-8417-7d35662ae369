package com.chis.modules.system.plugin;

import com.chis.modules.system.entity.TsQuartz;
import com.chis.modules.system.enumn.SystemType;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

public class QuartzPluginObj {
public static Set<TsQuartz> dataSet;
	
	
	static {
		dataSet = new HashSet<TsQuartz>();

		//失效物资定时提醒,默认每天十点,不启用
		TsQuartz quartz = new TsQuartz();
		quartz.setSystemType(SystemType.COMM);
		quartz.setTaskCode("COMM_0001");
		quartz.setTaskClass("com.chis.web.job.system.HolidayTypeJob");
		quartz.setTaskDescr("某年所有日期的节假日通知定时发送");
		quartz.setPeriodKind((short) 0);
		quartz.setPeriodType((short) 4);
		quartz.setDayOfMon((short)1);
		quartz.setIfReveal((short)0);
		quartz.setCreateManid(1);
		quartz.setCreateDate(new Date());
		dataSet.add(quartz);

		quartz = new TsQuartz();
		quartz.setSystemType(SystemType.COMM);
		quartz.setTaskCode("COMM_0002");
		quartz.setTaskClass("com.chis.web.job.system.UserValidJob");
		quartz.setTaskDescr("定时检测用户账号有效期，超过有效期设置为停用帐号");
		quartz.setPeriodKind((short) 0);
		quartz.setPeriodType((short) 2);
		quartz.setMinOfDay((short) 0);
		quartz.setHourOfDay((short)1);
		quartz.setIfReveal((short)0);
		quartz.setCreateManid(1);
		quartz.setCreateDate(new Date());
		dataSet.add(quartz);
    }
}
