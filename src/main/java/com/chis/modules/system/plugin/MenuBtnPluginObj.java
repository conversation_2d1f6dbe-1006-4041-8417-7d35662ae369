package com.chis.modules.system.plugin;

import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsMenuBtn;

/**
 * 菜单按钮的插件
 * <AUTHOR>
 */
public class MenuBtnPluginObj {

	public static Set<TsMenuBtn> menuSet;
	
	static {
		menuSet = new HashSet<TsMenuBtn>();
			
		//其中第一个参数,用来配置菜单级别，主要根据第二个参数来比较判断
		//角色管理（旧版本）菜单按钮
		menuSet.add(new TsMenuBtn("jsgl","jsgl_addBtn","增加"));
		menuSet.add(new TsMenuBtn("jsgl","jsgl_editLink","修改"));
		//用户管理 导出按钮
		menuSet.add(new TsMenuBtn("yhgl_new","yhgl_export","导出"));
		//用户管理 删除权限
		menuSet.add(new TsMenuBtn("yhgl_new","yhgl_del","删除"));
		//单位 删除权限
		menuSet.add(new TsMenuBtn("dwgl_new","dwgl_del","删除"));
    }
	
}