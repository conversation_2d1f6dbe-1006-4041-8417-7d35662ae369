package com.chis.modules.system.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsMenu;

/**
 * 系统菜单的插件
 * <AUTHOR>
 */
public class MenuPluginObj {

    public static Set<TsMenu> menuSet;

    static {
        menuSet = new HashSet<TsMenu>();
        //系统设置（全职卫通用功能放在这里，这边放出来的菜单一定是适用大部分平台的菜单）
        menuSet.add(new TsMenu("999","系统设置","xtsz","系统设置", Short.valueOf("0"),"#","default.png","default.png",new Date(),1,1));
        menuSet.add(new TsMenu("999.001","地区管理","dqgl","地区管理", Short.valueOf("1"),"/webapp/system/zoneList.faces","default.png","business-system.png",new Date(),1,2));
        menuSet.add(new TsMenu("999.002","单位管理(新版)","dwgl_new","单位管理", Short.valueOf("1"),"/webapp/system/unitListNew.faces","default.png","c6v2-products.png",new Date(),1,3));
        menuSet.add(new TsMenu("999.003","角色管理(新版)","jsgl_new","角色管理", Short.valueOf("1"),"/webapp/system/roleListNew.faces","default.png","customer-pool.png",new Date(),1,4));
        menuSet.add(new TsMenu("999.004","用户管理(新版)","yhgl_new","用户管理", Short.valueOf("1"),"/webapp/system/userListNew.faces","default.png","group-manage.png",new Date(),1,5));
        menuSet.add(new TsMenu("999.005","菜单管理","cdgl","菜单管理", Short.valueOf("1"),"/webapp/system/menuList.faces","default.png","workflow.png",new Date(),1,6));
        menuSet.add(new TsMenu("999.006","系统参数","xtcs_new","系统参数", Short.valueOf("1"),"/webapp/system/paramList.faces","default.png","forms-manage.png",new Date(),1,7));
        menuSet.add(new TsMenu("999.007","码表维护","mbwh","码表维护", Short.valueOf("1"),"/webapp/system/codeTypeList.faces","default.png","directories.png",new Date(),1,8));
        menuSet.add(new TsMenu("999.008","报表查询","system_bbcx","报表查询", Short.valueOf("1"),"/webapp/system/tsRptList.faces","view.png","png-0026.png",new Date(),1,9));
        menuSet.add(new TsMenu("999.009","文书类型维护","system_wslxwh","文书类型维护", Short.valueOf("1"),"/webapp/system/tbZwWritsortList.faces","default.png","png-0014.png",new Date(),1,10));
        menuSet.add(new TsMenu("999.010","企业日志查询","system_log_crpt_new_new","企业日志查询", Short.valueOf("1"),"/webapp/system/mongo3/tsMongoLogList_crptNew.faces?tag=1","default.png","task.png",new Date(),1,11));
        menuSet.add(new TsMenu("999.011","人员日志查询","system_log_person_new_new","人员日志查询", Short.valueOf("1"),"/webapp/system/mongo3/tsMongoLogList_personNew.faces?tag=2","default.png","task.png",new Date(),1,12));
		menuSet.add(new TsMenu("999.012","数据库版本管理","system_dbvision","数据库版本管理", Short.valueOf("1"),"/webapp/system/systemDbVisionManageList.faces","default.png","xtsz.png",new Date(),1,13));
		menuSet.add(new TsMenu("999.013","节假日配置","system_holidayConfig","节假日配置", Short.valueOf("1"),"/webapp/system/systemHolidayConfig.faces","default.png","xtsz.png",new Date(),1,14));
        menuSet.add(new TsMenu("999.014","系统日志查询","system_rzcx","系统日志查询", Short.valueOf("1"),"/webapp/system/logQuery.faces","default.png",null,new Date(),1,15));
        menuSet.add(new TsMenu("999.015","定时任务","system_dsrw","定时任务", Short.valueOf("1"),"/webapp/system/tsQuartzList.faces","task-manage.png","attendance-manage.png",new Date(),1,16));
        menuSet.add(new TsMenu("999.016","编号规则配置","bhgzpz","编号规则配置", Short.valueOf("1"),"/webapp/system/autoCodeList.faces","default.png","diary.png",new Date(),1,17));
        menuSet.add(new TsMenu("999.017","配置文件查询","system_pzwjcx","配置文件查询", Short.valueOf("1"),"/webapp/system/systemPropertiesGetList.faces","default.png","bhgzpz.png",new Date(),1,18));
		//非左右菜单样式项目右上角修改密码弹框，需要分配该菜单权限
		menuSet.add(new TsMenu("999.018","修改密码","xgmm","修改密码", Short.valueOf("1"),"PwdDialog","default.png","rights-manage.png",new Date(),1,1));
		//对照维护管理
		menuSet.add(new TsMenu("999.019","对照维护管理","system_contrast_manage","对照维护管理", Short.valueOf("1"),"/webapp/system/contrastManage.faces","default.png","task.png",new Date(),1,19));


		menuSet.add(new TsMenu("998","建议反馈","jyfk","建议反馈", Short.valueOf("0"),"#","slow_S_jdqk_10.png","myghnew_jzxx.png",new Date(),1,80));
		menuSet.add(new TsMenu("998.001","建议反馈 ","jyfk1","建议反馈 ", Short.valueOf("1"),"/webapp/system/tsMsgBoardEdit.faces","slow_S_jdqk_10.png","emerg_jbdj.png",new Date(),1,1));
		menuSet.add(new TsMenu("998.002","反馈内容查询","system_lybck","反馈内容查询", Short.valueOf("1"),"/webapp/system/tsMsgBoardList.faces","default.png","task.png",new Date(),1,2));
        

        //其余菜单，哪个项目需要时，菜单拷贝到具体项目的菜单插件里升级
		/*
		menuSet.add(new TsMenu("002.002","角色管理","jsgl","角色管理", Short.valueOf("1"),"/webapp/system/roleList.faces","default.png","customer-pool.png",new Date(),1,2));
		menuSet.add(new TsMenu("002.003","用户管理","yhgl","用户管理", Short.valueOf("1"),"/webapp/system/userList.faces","default.png","group-manage.png",new Date(),1,3));
		menuSet.add(new TsMenu("002.005","单位管理","dwgl","单位管理", Short.valueOf("1"),"/webapp/system/unitList.faces","default.png","c6v2-products.png",new Date(),1,5));
		//↑↑↑↑↑↑如上菜单不再使用（非职卫平台功能，现项目已独立）↑↑↑↑↑↑

		menuSet.add(new TsMenu("002.011","我的消息","wdxx","我的消息", Short.valueOf("1"),"/webapp/system/tdMsgMainSearchList.faces","knowledge.png","comment-set.png",new Date(),1,10));
		menuSet.add(new TsMenu("002.012","我的寻呼","wdxh","我的寻呼", Short.valueOf("1"),"/webapp/system/tdInfoMainList.faces","email.png","information-exchange.png",new Date(),1,11));
		menuSet.add(new TsMenu("002.013","用户组","yhz","用户组", Short.valueOf("1"),"/webapp/system/userGroupList.faces","group-manage.png","group-manage.png",new Date(),1,12));
		menuSet.add(new TsMenu("002.016","项目模板维护","system_xmmbwh","项目模板维护", Short.valueOf("1"),"/webapp/system/msgTemplateList.faces","default.png","bulletin-board-set.png",new Date(),1,14));

        //与国家职业病危害现状调查系统对接，单点登录跳转功能
		menuSet.add(new TsMenu("002.019","职业病危害现状调查","system_zybwhxzdc","职业病危害现状调查", Short.valueOf("1"),"/jumpToOtherServer?reqUrl=","default.png","bhgzpz.png",new Date(),1,19));
		//登录页面小喇叭区域通知显示，需同步配置“不登录就能访问的文件路径”参数
		menuSet.add(new TsMenu("002.107","通知通告管理","tztggl","通知通告管理", Short.valueOf("1"),"/webapp/system/tsNoticeList.faces","default.png","c6v2-products.png",new Date(),1,107));
		//体检机构、放射诊疗单位、用人单位 注册账号
		menuSet.add(new TsMenu("002.108","注册账号审核","zczhsh","注册账号审核", Short.valueOf("1"),"/webapp/system/tdSysApplyAccountCheckList.faces","default.png","c6v2-products.png",new Date(),1,108));
		menuSet.add(new TsMenu("002.109","注册账号查询","zczhcx","注册账号查询", Short.valueOf("1"),"/webapp/system/tdSysApplyAccountCheckList.faces?tag=2","default.png","c6v2-products.png",new Date(),1,109));

		//调查问卷管理
		menuSet.add(new TsMenu("005.001","题库管理","system_tkgl","题库管理",Short.valueOf("1"),"/webapp/system/examPoolList.faces","default.png","task.png", new Date(), 1, 1));
		menuSet.add(new TsMenu("005.002","问卷管理","system_wjscgl","问卷管理",Short.valueOf("1"),"/webapp/system/genQueList.faces","default.png","QES_004.png", new Date(), 1, 2));
		//门户首页相关功能菜单
		menuSet.add(new TsMenu("006.001","通知发布","system_tzfb","通知发布",Short.valueOf("1"),"/webapp/system/tdPublishNoticeList.faces","default.png","task.png", new Date(), 1, 1));
		menuSet.add(new TsMenu("006.002","通知查阅","system_tzcy","通知查阅",Short.valueOf("1"),"/webapp/system/tdPublishNoticeQueryList.faces","default.png","task.png", new Date(), 1, 2));
		//首页菜单必须是一级菜单，如果作为子菜单则无法实现登录后默认显示首页的功能
        menuSet.add(new TsMenu("007","首页","system_indexPortal","首页",Short.valueOf("1"),"/webapp/system/tsUserDeskShow.faces",DEFAULT_MENU_ICON,"task.png", new Date(), 1, 1));
		//个案查询、最新体检档案查询 异步导出
        menuSet.add(new TsMenu("002.020","导出文件下载","system_export","导出文件下载", Short.valueOf("1"),"/webapp/system/tdTjExportList.faces","default.png","bhgzpz.png",new Date(),1,20));

        menuSet.add(new TsMenu("046.007","课程管理","system_zxpx_kcgl","课程管理", Short.valueOf("1"),"/onlineLearningServer?type=1","default.png","bhgzpz.png",new Date(),1,7));
        menuSet.add(new TsMenu("046.008","在线学习","system_zxpx_zxxx","在线学习", Short.valueOf("1"),"/onlineLearningServer?type=2","default.png","bhgzpz.png",new Date(),1,8));

		*/
    }

}
