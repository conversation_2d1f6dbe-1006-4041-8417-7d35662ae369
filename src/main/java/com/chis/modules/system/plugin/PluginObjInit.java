package com.chis.modules.system.plugin;

import java.util.Date;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.entity.TdFlowNodePage;
import com.chis.modules.portal.plugin.PortalFlowNodePagePluginObj;
import com.chis.modules.system.entity.TbTempmetaDefine;
import com.chis.modules.system.entity.TsBsSort;
import com.chis.modules.system.entity.TsCodeRule;
import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsMenu;
import com.chis.modules.system.entity.TsMenuBtn;
import com.chis.modules.system.entity.TsQuartz;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsSystemParam;
import com.chis.modules.system.entity.TsTxtype;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.init.PluginInit;

@Component(value = "PluginObjInit_COMM_0")
public class PluginObjInit extends PluginInit {

	@Override
	public void run() {
		super.initDataStructureByXml();
		super.run();
		this.initUnitSort();
		this.initTxType();
		this.initTsMenuBtn();
		this.initTemplateDefine();
		this.initFlowNodePage();
		this.initQuartz();
		
		
	}
	

	private void initQuartz() {
		Set<TsQuartz> set = QuartzPluginObj.dataSet;
		if (null != set && set.size() > 0) {
			for (TsQuartz t : set) {
				List<TsQuartz> list = em.createNamedQuery("TsQuartz.findByCode")
						.setParameter("taskCode", t.getTaskCode()).getResultList();
				if (null == list || list.size() == 0) {
					t.setParamType(t.getSystemType().getTypeNo().intValue());
					pluginUpdateService.save(t);
				}
			}
		}
	}


	private void initFlowNodePage() {
		Set<TdFlowNodePage> set = PortalFlowNodePagePluginObj.tdFlowNodePageSet;
		if (null != set && set.size() > 0) {
			for (TdFlowNodePage t : set) {
				List<TdFlowNodePage> list = super.em.createNamedQuery("TdFlowNodePage.findByPageCode")
						.setParameter("pagecode", t.getPageCode()).getResultList();
				if (null == list || list.size() == 0) {
					pluginUpdateService.save(t);
				}
			}
		}
	}
	
	/**
	 * 初始化短信元素
	 */
	private void initTemplateDefine() {
		Set<TbTempmetaDefine> dataSet = TempmetaDefinePluginObj.dataSet;
		if (null != dataSet && !dataSet.isEmpty()) {
			for (TbTempmetaDefine t : dataSet) {
				List<TbTempmetaDefine> list = super.em.createNamedQuery("TbTempmetaDefine.findByMetaName")
						.setParameter("metaName", t.getMetaName()).getResultList();
				if (null == list || list.isEmpty()) {
					pluginUpdateService.save(t);
				}
			}
		}
	}

	/**
	 * 初始化单位属性
	 */
	private void initUnitSort() {
		Set<TsBsSort> sortSet = BsSortPluginObj.sortSet;
		if (null != sortSet && sortSet.size() > 0) {
			StringBuilder sb;
			for (TsBsSort t : sortSet) {
				sb = new StringBuilder("select t from TsBsSort t where t.sortCode ='").append(t.getSortCode()).append(
						"' ");
				List<TsBsSort> list = em.createQuery(sb.toString()).getResultList();
				if (null == list || list.size() == 0) {
					pluginUpdateService.save(t);
				}
			}
		}
	}

	/**
	 * 初始化通讯方式
	 */
	private void initTxType() {
		Set<TsTxtype> set = TxTypePluginObj.txtypeSet;
		if (null != set && set.size() > 0) {
			for (TsTxtype t : set) {
				List<TsTxtype> list = em.createNamedQuery("TsTxtype.findByCode")
						.setParameter("typeCode", t.getTypeCode()).getResultList();
				if (null == list || list.size() == 0) {
					t.setCreateDate(new Date());
					t.setCreateManid(1);
					t.setModifyDate(t.getCreateDate());
					t.setModifyManid(t.getModifyManid());
					pluginUpdateService.save(t);
				}
			}
		}
	}

	/**
	 * 初始化菜单按钮
	 * 
	 * <AUTHOR>
	 * @createDate 2015-05-25
	 */
	private void initTsMenuBtn() {
		Set<TsMenuBtn> set = MenuBtnPluginObj.menuSet;
		systemServiceImpl.saveTsMenuBtn(set);
	}

	@Override
	public List<String> buildDataStructurePlugin() {
		return null;
	}

	@Override
	public Set<TsMenu> buildMenuPlugin() {
		return MenuPluginObj.menuSet;
	}

	@Override
	public Set<TsCodeRule> buildCodeRulePlugin() {
		return CodeRulePluginObj.ruleSet;
	}

	@Override
	public Set<TsSystemParam> buildSystemParamPlugin() {
		return SystemParamPluginObj.paramSet;
	}

	@Override
	public Set<TsSimpleCode> buildCodePlugin() {
		return TsCodePluginObj.simpleCodeSet;
	}

	@Override
	public Set<TsCodeType> buildCodeTypePlugin() {
		return TsCodePluginObj.codeTypeSet;
	}

	@Override
	public SystemType buildSystemType() {
		return SystemType.COMM;
	}
}
