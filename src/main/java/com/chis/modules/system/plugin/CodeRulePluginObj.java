package com.chis.modules.system.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsCodeRule;
import com.chis.modules.system.enumn.SystemType;

/**
 * 自动编号规则的插件
 * <AUTHOR>
 */
public class CodeRulePluginObj {

	public static Set<TsCodeRule> ruleSet;

	static {
		ruleSet = new HashSet<TsCodeRule>();
		ruleSet.add(new TsCodeRule("TS_UNIT_DWBM", SystemType.COMM, "系统单位单位编码", null, null, null, (short) 4,
				"com.chis.ejb.service.autocode.impl.CodeDynamicOnlyServiceImpl", new Date(), 1));
	
		ruleSet.add(new TsCodeRule("SYSTEM_LCBD_BDBH", SystemType.COMM, "流程表单的表单编号", null, null, null, (short) 2,
				"com.chis.ejb.service.autocode.impl.CodeDynamicYearMonServiceImpl", new Date(), 1));
	}
}
