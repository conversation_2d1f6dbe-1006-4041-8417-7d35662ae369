package com.chis.modules.system.plugin;

import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsTxtype;
import com.chis.modules.system.enumn.TxType;

/**
 * 通讯方式的插件
 * <AUTHOR> 2014-11-27
 */
public class TxTypePluginObj {

	public static Set<TsTxtype> txtypeSet;

	static {
		txtypeSet = new HashSet<TsTxtype>();
		//语音
		txtypeSet.add(new TsTxtype("1001", TxType.YY, "com.chis.web.protocol.impl.MsgSendVoiceImpl", 1));
		//短信
		txtypeSet.add(new TsTxtype("1002", TxType.DX, "com.chis.web.protocol.impl.MbMsgSendImpl", 2));
		//疫苗发送短信
		txtypeSet.add(new TsTxtype("1003", TxType.DX, "com.chis.web.protocol.impl.YMMbMsgSendImpl", 3));
		//疫苗发送短信
		txtypeSet.add(new TsTxtype("1004", TxType.YY, "com.chis.web.protocol.impl.YmMsgSendVoiceImpl", 4));
		//APP
		txtypeSet.add(new TsTxtype("1005", TxType.APP, null, 5));
		//邮箱
		txtypeSet.add(new TsTxtype("1006", TxType.EMAIL, "com.chis.web.protocol.impl.SendEmailToUserImpl", 6));
	}
}
