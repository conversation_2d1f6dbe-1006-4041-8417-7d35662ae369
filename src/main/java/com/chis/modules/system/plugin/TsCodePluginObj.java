package com.chis.modules.system.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.enumn.CodeType;
import com.chis.modules.system.enumn.SystemType;

/**
 * 码表的插件
 * 码表编码是写死的
 * <AUTHOR>
 */
public class TsCodePluginObj {

	public static Set<TsCodeType> codeTypeSet;
	public static Set<TsSimpleCode> simpleCodeSet;

	static {
		codeTypeSet = new HashSet<TsCodeType>();
		/**
		 * 创建2014-05-06 zww
		 * 修改2014-05-06 zww
		 */
		codeTypeSet.add(new TsCodeType(SystemType.COMM, CodeType.MZ.getTypeNo(), "民族", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.COMM,CodeType.ZHW.getTypeNo(), "职务", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.COMM,CodeType.ZHCH.getTypeNo(), "职称", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.COMM,CodeType.XL.getTypeNo(), "学历", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.COMM,CodeType.ZHZHMM.getTypeNo(), "政治面貌", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.COMM,CodeType.HYZHK.getTypeNo(), "婚姻状况", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.COMM,CodeType.ZJXY.getTypeNo(), "宗教信仰", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.COMM,CodeType.RYSX.getTypeNo(),"人员属性",(short)0,(short)0));
		codeTypeSet.add(new TsCodeType(SystemType.COMM,CodeType.ZY.getTypeNo(),"职业",(short)0,(short)0));
		codeTypeSet.add(new TsCodeType(SystemType.COMM,CodeType.HJLX.getTypeNo(),"户籍类型",(short)0,(short)0));
		codeTypeSet.add(new TsCodeType(SystemType.COMM,CodeType.JSLX.getTypeNo(),"角色类型",(short)0,(short)0));
		codeTypeSet.add(new TsCodeType(SystemType.COMM,"13001","问卷类别",(short)1,(short)0));
		
		codeTypeSet.add(new TsCodeType(SystemType.COMM,"5546","发布类型",(short)1,(short)0));
        codeTypeSet.add(new TsCodeType(SystemType.COMM,"5550","导出类型",(short)0,(short)0));

        codeTypeSet.add(new TsCodeType(SystemType.COMM,"5854","职业健康检查机构",(short)0,(short)0));
        codeTypeSet.add(new TsCodeType(SystemType.COMM,"5855","个人剂量监测机构",(short)0,(short)0));

        codeTypeSet.add(new TsCodeType(SystemType.COMM,"5584","系统主题背景色",(short)0,(short)0));

        codeTypeSet.add(new TsCodeType(SystemType.COMM,"5590","难易程度",(short)0,(short)0));
        codeTypeSet.add(new TsCodeType(SystemType.COMM,"5587","机构性质",(short)0,(short)0));

		simpleCodeSet = new HashSet<TsSimpleCode>();
		simpleCodeSet.add(new TsSimpleCode("1001", "编内", "编内", "", "bn", new Date(), 1, "2008"));
		simpleCodeSet.add(new TsSimpleCode("1002", "编外", "编外", "", "by", new Date(), 1, "2008"));
		simpleCodeSet.add(new TsSimpleCode("1003", "未定岗", "未定岗", "", "wdg", new Date(), 1, "2008"));
		simpleCodeSet.add(new TsSimpleCode("1004", "外借", "外借", "", "wj", new Date(), 1, "2008"));

//        simpleCodeSet.add(new TsSimpleCode("2001", "护师（护士）", "护师（护士）", "", "hshs", new Date(), 1, "2003"));
//        simpleCodeSet.add(new TsSimpleCode("2002", "执业助理医师", "执业助理医师", "", "zyzlys", new Date(), 1, "2003"));
//        simpleCodeSet.add(new TsSimpleCode("2003", "执业医师", "执业医师", "", "zyys", new Date(), 1, "2003"));
//        simpleCodeSet.add(new TsSimpleCode("2004", "乡村医生", "乡村医生", "", "xcys", new Date(), 1, "2003"));
        
        simpleCodeSet.add(new TsSimpleCode("1001", "幼托儿童", "幼托儿童", "", "ytet", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1002", "散居儿童", "散居儿童", "", "sjet", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1003", "学生", "学生", "", "xs", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1004", "教师", "教师", "", "js", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1005", "保育员及保姆", "保育员及保姆", "", "byyjbm", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1006", "餐饮食品业", "餐饮食品业", "", "cyspy", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1007", "公共场所服务员", "公共场所服务员", "", "ggcsfwy", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1008", "商业服务", "商业服务", "", "syfw", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1009", "医务人员", "医务人员", "", "ywry", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1010", "工人", "工人", "", "gr", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1011", "民工", "民工", "", "mg", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1012", "农民", "农民", "", "nm", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1013", "牧民", "牧民", "", "mm", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1014", "渔（船）民", "渔（船）民", "", "ycm", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1015", "海员及长途驾驶员", "海员及长途驾驶员", "", "hyjctjsy", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1016", "干部职员", "干部职员", "", "gbzy", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1017", "离退人员", "离退人员", "", "ltry", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1018", "家政、家务及待业", "家政、家务及待业", "", "jzjwjdy", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1019", "不详", "不详", "", "bx", new Date(), 1, "2010"));
        simpleCodeSet.add(new TsSimpleCode("1020", "其它", "其它", "", "qt", new Date(), 1, "2010"));

        simpleCodeSet.add(new TsSimpleCode("1001", "本地", "本地", "", "bd", new Date(), 1, "2011"));
        simpleCodeSet.add(new TsSimpleCode("1002", "市内流动", "市内流动", "", "snld", new Date(), 1, "2011"));
        simpleCodeSet.add(new TsSimpleCode("1003", "市间流动（省内流动）", "市间流动（省内流动）", "", "sjldsnld", new Date(), 1, "2011"));
        simpleCodeSet.add(new TsSimpleCode("1004", "省间流动", "省间流动", "", "sjld", new Date(), 1, "2011"));
        simpleCodeSet.add(new TsSimpleCode("1005", "港澳台", "港澳台", "", "gat", new Date(), 1, "2011"));
        simpleCodeSet.add(new TsSimpleCode("1006", "外籍", "外籍", "", "wj", new Date(), 1, "2011"));
        
        simpleCodeSet.add(new TsSimpleCode("1001", "业务管理员", "业务管理员", "", "", new Date(), 1, CodeType.JSLX.getTypeNo()));
        simpleCodeSet.add(new TsSimpleCode("1002", "数据质控", "数据质控", "", "", new Date(), 1, CodeType.JSLX.getTypeNo()));
        simpleCodeSet.add(new TsSimpleCode("1003", "数据上报", "数据上报", "", "", new Date(), 1, CodeType.JSLX.getTypeNo()));

        simpleCodeSet.add(new TsSimpleCode("1001", "深色", "深色", "", "", new Date(), 1, "5584"));

        
	}

}
