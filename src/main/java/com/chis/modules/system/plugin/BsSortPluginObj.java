package com.chis.modules.system.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsBsSort;
import com.chis.modules.system.enumn.SystemType;

/**
 * 单位属性的插件
 * <AUTHOR>
 */
public class BsSortPluginObj {

	public static Set<TsBsSort> sortSet;

	static {
		sortSet = new HashSet<TsBsSort>();
		/**
	 	 * <p>修订内容：
	 	 * 该单位属性为各个项目中所要求的单位属性，并进行汇总。每个项目添加时，请一定要在此备注，若已存在的单位属性，请取已存在的单位编码。切记！！！！
	 	 * （原因：以防两个相互依赖的项目存在公用的单位属性，但编码不同）</p>
	 	 * @MethodReviser qrr,2018年4月12日,BsSortPluginObj
		 * */
//		sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "2000", "疫苗供应商", "2000", new Date(), 1));
//		sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "2001", "疾控中心", "2001", new Date(), 1));
//      sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "2002", "医疗机构", "2002", new Date(), 1));
//      sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "2003", "卫生局", "2003", new Date(), 1));
        
//        sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "2004", "卫生监督所", "2004", new Date(), 1));
//        sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "2005", "血站", "2005", new Date(), 1));
//        sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "2006", "医疗救治机构", "2006", new Date(), 1));
//        sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "2006", "社区服务中心", "2006", new Date(), 1));
//        sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "2008", "物流", "2008", new Date(), 1));
//        sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "2009", "民营企业", "2009", new Date(), 1));
		sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "2010", "鉴定机构", "2010", new Date(), 1));
		sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "2011", "随访机构", "2011", new Date(), 1));
		//湖北职业卫生（web-zyjk-hb）
//		sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "2001", "疾控中心", "2001", new Date(), 1));
//		sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "1002", "职业病防治院", "1002", new Date(), 1));
//		sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "1003", "体检机构", "1003", new Date(), 1));
//		sortSet.add(new TsBsSort(SystemType.COMM.getTypeNo(), "1004", "安全生产监督管理局", "1004", new Date(), 1));
	}
}
