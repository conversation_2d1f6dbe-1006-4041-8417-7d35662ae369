package com.chis.modules.system.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TbTempmetaDefine;
import com.chis.modules.system.enumn.SystemType;

/**
 * 短信模板的插件
 * <AUTHOR>
 */
public class TempmetaDefinePluginObj {

	public static Set<TbTempmetaDefine> dataSet;
	
	/**
	 * 增加规则
	 */
	static {
		dataSet = new HashSet<TbTempmetaDefine>();

		dataSet.add(new TbTempmetaDefine(SystemType.COMM, "1001", "【当前单位电话】",
				"com.chis.ejb.tempmeta.comm.TempmetaResolve4UnitTel",
				"系统当前登录人所在单位的联系电话，如：0510-8xxxxxxx", new Date(), 1, "sys_current_unitTel"));
	
		dataSet.add(new TbTempmetaDefine(SystemType.COMM, "1009", "【当前时间】",
				"com.chis.modules.system.tempmeta.TempmetaResolve4Time",
				"系统当前时间，如：2016-11-7 08:50:50", new Date(), 1, "sys_current_time"));

	}
}
