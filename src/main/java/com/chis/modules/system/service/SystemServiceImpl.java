package com.chis.modules.system.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.persistence.Query;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.MD5Util;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsBsSort;
import com.chis.modules.system.entity.TsHoliday;
import com.chis.modules.system.entity.TsLoginInfo;
import com.chis.modules.system.entity.TsMenu;
import com.chis.modules.system.entity.TsMenuBtn;
import com.chis.modules.system.entity.TsRpt;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUnitAttr;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsUserSecurityLog;
import com.chis.modules.system.enumn.SystemType;

@Service
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class SystemServiceImpl extends AbstractTemplate {
	@Transactional(readOnly = true)
	public List<TsRpt> findTsRptBySystemType(String rids,String searchRptName) {
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TsRpt t where 1=1");
		if (StringUtils.isNotBlank(rids))
			sb.append(" and  t.paramType in (").append(rids).append(")");
		if(StringUtils.isNoneBlank(searchRptName)){
			sb.append(" and t.rptnam like :rptnam  ");
			paramMap.put("rptnam", new StringBuilder("%").append(StringUtils.convertBFH(searchRptName.trim())).append("%").toString());
		}
		sb.append(" order by t.paramType,t.rptCod ");
		return this.findDataByHqlNoPage(sb.toString(), paramMap);
	}
	
	@Transactional(readOnly=true)
	public String delRpt(Integer rptId)	{
		if( null != rptId)	{
			try{
				StringBuilder sql = new StringBuilder();
				sql.append("DELETE FROM TS_RPT T WHERE T.RID = ?1");
				Query query = this.em.createNativeQuery(sql.toString());
				query.setParameter(1, rptId);
				query.executeUpdate();
			}catch(Exception e){
				e.printStackTrace();
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				return "该报表模版已被引用，无法删除！";
			}
		}
		return null;
	}
	
	@Transactional(readOnly=true)
	public TsRpt findTsRpt(Integer rptId)	{
		if( null != rptId)	{
			TsRpt find = this.em.find(TsRpt.class, rptId);
			return find;
		}
		return null;
	}
	
	@Transactional(readOnly=true)
	public String saveTsRpt(TsRpt tsRpt)	{
		if( null != tsRpt){
			tsRpt.setRptCod("TZ_"+tsRpt.getRptCod());
			StringBuilder sql = new StringBuilder();
			sql.append("SELECT T.RID FROM TS_RPT T WHERE T.RPTCOD = ?1");
			if( null != tsRpt.getRid() )	{
				sql.append(" AND T.RID != ?2");
			}
			Query query = this.em.createNativeQuery(sql.toString());
			query.setParameter(1, tsRpt.getRptCod());
			if( null != tsRpt.getRid() )	{
				query.setParameter(2, tsRpt.getRid());
			}
			List resultList = query.getResultList();
			if( null != resultList && resultList.size() > 0)	{
				return "报表编码已存在！";
			}
			if( null != tsRpt.getRid() )	{
				this.update(tsRpt);
			}else{
				this.save(tsRpt);
			}
		}
		return null;
	}
	
	@Transactional(readOnly=true)
	public TsUserInfo findUserByUserNo(String userNo) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TsUserInfo t where t.userNo = :userNo");
		Query query=em.createQuery(sb.toString()).setParameter(
				"userNo",
				new StringBuilder(StringUtils.convertBFH(userNo.trim())).toString());
		List<TsUserInfo> list = query.getResultList();
		if (null != list && list.size() > 0) {
			TsUserInfo tsUserInfo = list.get(0);
			tsUserInfo.getTsUserMenus().size();
			tsUserInfo.getTsUserRoles().size();
			this.processAttrMap(tsUserInfo);
			this.processUserIfAdmin(tsUserInfo);
			this.getAllMenuList(tsUserInfo);
			this.getInUseMenuList(tsUserInfo);
			return tsUserInfo;
			
		} else {
			return null;
		}
	}
	
	/***
	 *  <p>方法描述：封装所有菜单进入用户</p>
     *
     * @MethodAuthor maox,2020年1月16日,getAllMenuList
	 * @param tsUserInfo
	 */
	private void getAllMenuList(TsUserInfo tsUserInfo){
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TsMenu t");
		
		List<TsMenu> list = em.createQuery(sb.toString()).getResultList();
		tsUserInfo.setAllMenus(list);
	}
	
	private void getInUseMenuList(TsUserInfo tsUserInfo){
		List<TsMenu> list = findInUseMenuList(false, tsUserInfo.getRid());
		tsUserInfo.setInUseMenus(list);
	}
	
	/***
	 *  <p>方法描述：个人有的菜单</p>
     *
     * @MethodAuthor maox,2020年1月17日,findInUseMenuList
	 * @param ifAdmin
	 * @param userId
	 * @param unitId
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TsMenu> findInUseMenuList(boolean ifAdmin, Integer userId) {
		List<TsMenu> list = null;
		StringBuilder sb = new StringBuilder();
		if (ifAdmin) {
			sb.append(" select t from TsMenu t order by t.menuLevelNo ");
			list = em.createQuery(sb.toString()).getResultList();
		} else {
			sb.append(" select * from ( ");
			sb.append(" SELECT A.RID, A.MENU_LEVEL_NO, A.MENU_CN,A.ISFUNC,A.MENU_URI,A.MENU_SIMPLE ");
			sb.append(" FROM TS_MENU A ");
			sb.append(" INNER JOIN TS_USER_MENU B ON B.MENU_TEMPLATE_ID = A.RID ");
			sb.append(" WHERE B.USER_INFO_ID = ").append(userId);
			sb.append(" UNION ");
			sb.append(" SELECT C3.RID, C3.MENU_LEVEL_NO, C3.MENU_CN ,C3.ISFUNC ,C3.MENU_URI,C3.MENU_SIMPLE ");
			sb.append(" FROM TS_USER_ROLE C1 ");
			sb.append(" INNER JOIN TS_ROLE_MENU C2 ON C2.ROLE_ID = C1.ROLE_ID ");
			sb.append(" INNER JOIN TS_MENU C3 ON C2.MENU_ID = C3.RID ");
			sb.append(" WHERE C1.USER_INFO_ID = ").append(userId);
			sb.append("  ) order by MENU_LEVEL_NO ");
			List<Object[]> rstList = em.createNativeQuery(sb.toString()).getResultList();
			if (null != rstList && rstList.size() > 0) {
				list = new ArrayList<TsMenu>(rstList.size());
				for (Object[] o : rstList) {
					TsMenu t = new TsMenu();
					t.setRid(Integer.valueOf(o[0].toString()));
					t.setMenuLevelNo(o[1].toString());
					t.setMenuCn(o[2].toString());
					t.setIsfunc(Short.valueOf(o[3].toString()));
					t.setMenuUri(o[4]==null?"":o[4].toString());
					t.setMenuSimple(o[5]==null?"":o[5].toString());
					list.add(t);
				}
			}
		}
		return list;

	}
	
	@Transactional(readOnly=true)
	public Map<String, String> findUsersBtn(Integer userId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T3.BTN_CODE, T3.RID ");
		sb.append(" FROM TS_USER_BTN T1 ");
		sb.append(" INNER JOIN TS_MENU_BTN T3 ON T1.BTN_ID = T3.RID AND T3.IF_REVEAL = '1' ");
		sb.append(" INNER JOIN TS_MENU T2 ON T3.MENU_TEMPLATE_ID = T2.RID ");
		if(null != userId) {
			sb.append(" WHERE T1.USER_INFO_ID = '").append(userId).append("'");
		}
		List<Object[]> list = super.em.createNativeQuery(sb.toString()).getResultList();
		
		Map<String, String> returnMap = new HashMap<String, String>();
		if(null != list && list.size() > 0) {
			for(Object[] o : list) {
				returnMap.put(o[0].toString(), o[1].toString());
			}
		}
		return returnMap;
	}

	/**
	 * 将用户实体中的属性处理成Map<单位属性编码，单位属性实体>
	 *
	 * @param user 用户实体
	 * <AUTHOR>
	 * @createDate 2015-2-13
	 */
	@Transactional(readOnly=true)
	private void processAttrMap(TsUserInfo user)	{
		if( null != user && null != user.getTsUnit() && null != user.getTsUnit().getTsUnitAttrs()){
			Map<String,TsBsSort> map = new HashMap<String, TsBsSort>();
			Iterator<TsUnitAttr> tAit = user.getTsUnit().getTsUnitAttrs().iterator();
			while(tAit.hasNext()){
				TsBsSort tsBsSort = tAit.next().getTsBsSort();
				map.put(tsBsSort.getSortCode(), tsBsSort);
			}
			user.setTsBsSortMap(map);
		}
	}

	
	public void updUserPwdById(Integer uRid, String pwd) {
		TsUserInfo userInfo = (TsUserInfo) this.find(TsUserInfo.class, uRid);
		userInfo.setUploadTag(0);
		userInfo.setPassword(pwd);
        userInfo.setIfModpsw((short) 1);
		this.update(userInfo);
	}

	public void updUserPwdById(Integer uRid, String pwd, String pwsLiveDay) {
		TsUserInfo userInfo = (TsUserInfo) this.find(TsUserInfo.class, uRid);
		userInfo.setUploadTag(0);
		userInfo.setPassword(pwd);
        userInfo.setIfModpsw((short) 1);
		userInfo.setPwdBegDate(new Date());
		userInfo.setPwdEndDate(DateUtils.getDateByDays(Integer.parseInt(pwsLiveDay)));
		this.update(userInfo);
	}

	@Transactional(readOnly=true)
    public List<TsRpt> findTsRptBySystemType(SystemType systemType){
        StringBuilder sb = new StringBuilder();
        sb.append(" select t from TsRpt t where 1=1");
        if(systemType!=null)
        sb.append(" and  t.systemType = ").append(systemType);
        sb.append(" order by t.systemType,t.rptCod ");
        return em.createQuery(sb.toString()).getResultList();
    }
	// ======================服务方法=============================
/*	*//**
	 * 初始化单位属性
	 *//*
	@TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
	public void initUnitSort() {
		Set<TsBsSort> sortSet = BsSortPluginObj.sortSet;
        if(null != sortSet && sortSet.size() > 0) {
            StringBuilder sb;
            for(TsBsSort t: sortSet) {
                sb = new StringBuilder("select t from TsBsSort t where t.sortCode ='").append(t.getSortCode()).append("' ");
                List<TsBsSort> list = em.createQuery(sb.toString()).getResultList();
                if(null == list || list.size() == 0) {
                    this.save(t);
                }
            }
        }
	}
	
	*//**
	 * 初始化通讯方式
	 *//*
	@TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
	public void initTxType() {
		Set<TsTxtype> set = TxTypePluginObj.txtypeSet;
		if(null != set && set.size() > 0) {
			for(TsTxtype t: set) {
				List<TsTxtype> list = em.createNamedQuery("TsTxtype.findByCode").setParameter("typeCode", t.getTypeCode()).getResultList();
				if(null == list || list.size() == 0) {
					this.save(t);
				}
			}
		}
	}
	
	*//**
	 * 初始化定时任务
	 *//*
	@TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
	public void initQuartz() {
		Set<TsQuartz> set = QuartzPluginObj.dataSet;
		if(null != set && set.size() > 0) {
			for(TsQuartz t: set) {
				List<TsQuartz> list = em.createNamedQuery("TsQuartz.findByCode").setParameter("taskCode", t.getTaskCode()).getResultList();
				if(null == list || list.size() == 0) {
					this.save(t);
				}
			}
		}
	}
	
	*//**
	 * 初始化菜单按钮
	 * <AUTHOR>
	 * @createDate 2015-05-25
	 *//*
	@TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
	public void initTsMenuBtn() {
        Set<TsMenuBtn> set = MenuBtnPluginObj.menuSet;
        if (null != set && set.size() > 0) {
            for (TsMenuBtn t : set) {
                List<TsQuartz> list = em.createNamedQuery("TsMenuBtn.findByCode").setParameter("buttoncode", t.getBtnCode()).getResultList();
                if (null == list || list.size() == 0) {
                    TsMenu tsMenu = (TsMenu) em.createNamedQuery("TsMenu.findByMenuEn").setParameter("menuEn",t.getMenucode()).getSingleResult();
                    if(tsMenu != null){
                        t.setTsMenu(tsMenu);
                        t.setLevelno("1");
                        t.setIfReveal((short) 1);
                        this.save(t);
                    }
                }
            }
        }

	}

	
	public void init() {
		super.init();
		this.initUnitSort();
		this.initTxType();
		this.initQuartz();
		this.initQuartz();
		initTsMenuBtn();
	}

	
	public List<String> buildDataStructure() {
		return DataStructurePluginObj.sqlList;
	}

	
	public SystemType buildSystemType() {
		return SystemType.COMM;
	}

	
	public Set<TsMenu> buildMenu() {
		return MenuPluginObj.menuSet;
	}

	
	public Set<TsSystemParam> buildParam() {
		return SystemParamPluginObj.paramSet;
	}

	
	public Set<TsCodeType> buildCodeType() {
		return TsCodePluginObj.codeTypeSet;
	}

	
	public Set<TsSimpleCode> buildSimpleCode() {
		return TsCodePluginObj.simpleCodeSet;
	}

	
	public Set<TsCodeRule> buildCodeRule() {
		return CodeRulePluginObj.ruleSet;
	}*/

	@Transactional(readOnly=true)
	public String getDesc() {
		return "系统服务";
	}

	@Transactional(readOnly=true)
	public String getVersion() {
		return "1.0.0";
	}

	@Transactional(readOnly=true)
    public Set<String> findUserBtns(Integer userid){
        StringBuilder sb = new StringBuilder();
        sb.append("select ROLE_ID from TS_USER_ROLE where USER_INFO_ID = ").append(userid);
        List<Object> list = em.createNativeQuery(sb.toString()).getResultList();
        StringBuilder str = new StringBuilder();
        if(list != null && list.size() > 0){
            for(Object obj : list){
                str.append(",").append(obj);
            }
        }
        sb = new StringBuilder();
        sb.append("select t2.BTN_CODE from TS_USER_BTN t1 inner join TS_MENU_BTN t2 on t2.rid = t1.BTN_ID ");
        sb.append(" where t1.USER_INFO_ID = ").append(userid);
        if(str.toString().length() > 0){
            sb.append(" union ");
            sb.append("select t4.BTN_CODE from TS_ROLE_BTN t3 inner join TS_MENU_BTN t4 on t4.rid = t3.BTN_ID ");
            sb.append(" where t3.ROLE_ID in (").append(str.toString().substring(1)).append(") ");
        }
        List<Object> list1 = em.createNativeQuery(sb.toString()).getResultList();
        Set<String> resset =  new HashSet<String>();
        if(list1 != null && list1.size() > 0){
            for(Object obj : list1){
                resset.add(obj.toString());
            }
        }
        return resset;
    }
	@Transactional(readOnly=true)
	public String delHoliday(Integer rid)	{
		if( null != rid)	{
			try{
				StringBuilder sql = new StringBuilder();
				sql.append("DELETE FROM TS_HOLIDAY T WHERE T.RID = ?1");
				Query query = this.em.createNativeQuery(sql.toString());
				query.setParameter(1, rid);
				query.executeUpdate();
			}catch(Exception e){
				e.printStackTrace();
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				return "该报表模版已被引用，无法删除！";
			}
		}
		return null;
	}
	public String selectByDate(Date holdate,Integer rid){
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT T.RID");
		sql.append(" FROM TS_HOLIDAY T WHERE 1=1");
		if (holdate!=null) {
			sql.append(" AND T.HOL_DATE = TO_DATE('").append(DateUtils.formatDate(holdate)).append("','YYYY-MM-DD') ");
		}
		if (rid!=null) {
			sql.append(" AND T.rid != ").append(rid.toString());
		}
		 List<Object> list1 = em.createNativeQuery(sql.toString()).getResultList();
		 if(list1 != null && list1.size() > 0){
			 return list1.get(0).toString();
		 }
		return null;
	}
	public TsHoliday selectHoliday(Integer rid){
		StringBuilder hql = new StringBuilder();
		hql.append("SELECT T FROM TsHoliday T WHERE 1=1");
		if (rid!=null) {
			hql.append(" and T.rid = ").append(rid.toString());
		}
		 List<TsHoliday> list1 = em.createQuery(hql.toString()).getResultList();
		 if (list1!=null && list1.size()>0) {
			 return list1.get(0);
		}
		 return null; 
	}
	public void updUserPwd(TsUserInfo user) {
		TsUserInfo userInfo = (TsUserInfo) this.find(TsUserInfo.class, user.getRid());
		userInfo.setPassword(new MD5Util().getMD5ofStr(user.getPassword()));
		userInfo.setIfModpsw((short) 1);
		this.update(userInfo);
	}
	
	public void updUserPwd2(TsUserInfo user) {
		TsUserInfo userInfo = (TsUserInfo) this.find(TsUserInfo.class, user.getRid());
		userInfo.setPassword(user.getPassword());
		userInfo.setIfModpsw((short) 1);
		this.update(userInfo);
	}
	
	public void saveOrUpdateTsLoginInfo(TsLoginInfo info){
		if(info.getRid()==null){
			this.save(info);
		}else{
			this.update(info);
		}
	}
	
	
	@Transactional(readOnly=true)
	public TsLoginInfo findLoginInfoByUserno(String userNo) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TsLoginInfo t where t.userNo = :userNo");
		Query query=em.createQuery(sb.toString()).setParameter(
				"userNo",
				new StringBuilder(StringUtils.convertBFH(userNo.trim())).toString());
		List<TsLoginInfo> list = query.getResultList();
		if (null != list && list.size() > 0) {
			return  list.get(0);
		} else {
			return null;
		}
	}
	
	/**
 	 * <p>方法描述：根据年份、月份获取节假日数据</p>
 	 * @MethodAuthor qrr,2018年2月28日,selectHoliday
	 * */
	public List<TsHoliday> selectHolidayByYearAndMonth(String year,String month){
		StringBuilder hql = new StringBuilder();
		hql.append("SELECT T FROM TsHoliday T WHERE 1=1");
		if (StringUtils.isNotBlank(year)) {
			hql.append(" and T.holYear = '").append(year).append("'");
		}
		if (StringUtils.isNotBlank(month)) {
			hql.append(" and T.holMon = '").append(month).append("'");
		}
		List<TsHoliday> list1 = em.createQuery(hql.toString()).getResultList();
		return list1; 
	}
	
	/**
	 * <p>方法描述：根据单位rid更新单位的社会信用代码,联系人，联系方式</p>
 	 * 
 	 * @MethodAuthor rcj,2018年4月2日,updateCreditCodeById
	 * @param uRid
	 * @param creditCode
	 */
	@Transactional(readOnly=false)
	public void updateCreditCodeById(Integer uRid, String creditCode,String linkMan,String linkWay) {
		StringBuffer sql = new StringBuffer();
		sql.append("update TS_UNIT  set CREDIT_CODE= '");
		sql.append(creditCode);
		sql.append("' ,LINK_MAN= '");
		sql.append(linkMan);
		sql.append("' ,UNITTEL= '");
		sql.append(linkWay);
		sql.append("' WHERE RID=");
		sql.append(uRid);
		Query q = em.createNativeQuery(sql.toString());
		q.executeUpdate();
	}
	
	/**
	 * <p>方法描述：验证社会信用代码的唯一性</p>
 	 * 
 	 * @MethodAuthor rcj,2018年4月3日,validateOnlyCredit
	 * @param creditCode
	 * @return
	 */
	public boolean validateOnlyCredit(Integer rid,String creditCode){
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT * FROM TS_UNIT WHERE CREDIT_CODE ='");
		sql.append(creditCode);
		sql.append("' AND RID != '");
		sql.append(rid);
		sql.append("'");
		List<TsUnit> resultList = em.createNativeQuery(sql.toString()).getResultList();
		if( null != resultList && resultList.size() >0 ){
			return true;
		}
		return false;
	}
	
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2018年4月12日,findUserByUserNo
	 * */
	@Transactional(readOnly=true)
	public TsUnit findTsUnitByUnitCode(String unitCode,String regCode) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TsUnit t where t.unitCode = '").append(unitCode).append("'");
		sb.append(" and t.regCode ='").append(regCode).append("'");
		List<TsUnit> list=em.createQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			TsUnit unit = list.get(0);
			return unit;
			
		} else {
			return null;
		}
	}
	/**
 	 * <p>方法描述：保存菜单按钮</p>
 	 * @MethodAuthor qrr,2019年11月8日,saveTsMenuBtn
	 * */
	@Transactional(readOnly=false)
	public void saveTsMenuBtn(Set<TsMenuBtn> set) {
		if (null != set && set.size() > 0) {
			for (TsMenuBtn t : set) {
				List<TsMenuBtn> list = em
						.createNamedQuery("TsMenuBtn.findByCode")
						.setParameter("buttoncode", t.getBtnCode())
						.getResultList();
				if (null == list || list.size() == 0) {
					if (StringUtils.isNotBlank(t.getMenucode())) {
						List<TsMenu> results = em
								.createNamedQuery("TsMenu.findByMenuEn")
								.setParameter("menuEn", t.getMenucode())
								.getResultList();
						if (null != results && results.size() > 0) {
							TsMenu tsMenu = results.get(0);
							if (tsMenu != null) {
								t.setTsMenu(tsMenu);
								t.setLevelno("1");
								t.setIfReveal((short) 1);
								this.save(t);
							}
						}
					}
				}
			}
		}
	}
	/**
 	 * <p>方法描述：用户是否管理员</p>
 	 * @MethodAuthor qrr,2019年12月21日,processUserIfAdmin
	 */
	@Transactional(readOnly=true)
	private void processUserIfAdmin(TsUserInfo user)	{
		if( null != user && null != user.getRid()){
			StringBuffer sql = new StringBuffer();
			sql.append(" select 1 from TS_USER_ROLE t ");
			sql.append(" LEFT JOIN TS_ROLE T1 ON T1.RID = T.ROLE_ID");
			sql.append(" LEFT JOIN TS_ROLE_POWER T2 ON T2.ROLE_ID = T1.RID");
			sql.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T2.ROLE_TYPE_ID = T3.RID");
			sql.append(" WHERE 1=1 AND T.USER_INFO_ID =").append(user.getRid());
			sql.append(" AND T3.EXTENDS1 =1");
			int count = this.findCountBySql(sql.toString());
			if (count>0) {
				user.setIfAdmin(true);
			}else {
				user.setIfAdmin(false);
			}
		}
	}
	
	/***
	 *  <p>方法描述：根据Ip查找登录信息</p>
     *
     * @MethodAuthor maox,2020年3月20日,findTsUserSecurityLogByIp
	 * @param ip
	 * @return
	 */
	@Transactional(readOnly=true)
	public TsUserSecurityLog findTsUserSecurityLogByIp(String ip) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TsUserSecurityLog t where t.loginIp = :loginIp");
		Query query=em.createQuery(sb.toString()).setParameter(
				"loginIp",
				new StringBuilder(StringUtils.convertBFH(ip.trim())).toString());
		List<TsUserSecurityLog> list = query.getResultList();
		if (null != list && list.size() > 0) {
			return  list.get(0);
		} else {
			return null;
		}
	}
	
	/***
	 *  <p>方法描述：保存修改系统日志</p>
     *
     * @MethodAuthor maox,2020年3月20日,saveOrUpdateTsUserSecurityLog
	 * @param log
	 */
	@Transactional(readOnly=false)
	public void saveOrUpdateTsUserSecurityLog(TsUserSecurityLog log){
		if(log.getRid()==null){
			log.setCreateDate(new Date());
			log.setCreateManid(1);
			this.save(log);
		}else{
			log.setModifyDate(new Date());
			log.setModifyManid(1);
			this.update(log);
		}
	}

	@Transactional(readOnly=true)
	public TsUserInfo findUserByIdc(String idc) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TsUserInfo t where t.idc = :idc");
		Query query=em.createQuery(sb.toString()).setParameter(
				"idc",
				new StringBuilder(StringUtils.convertBFH(idc.trim())).toString());
		List<TsUserInfo> list = query.getResultList();
		if (null != list && list.size() > 0) {
			TsUserInfo tsUserInfo = list.get(0);
			tsUserInfo.getTsUserMenus().size();
			tsUserInfo.getTsUserRoles().size();
			this.processAttrMap(tsUserInfo);
			this.processUserIfAdmin(tsUserInfo);
			this.getAllMenuList(tsUserInfo);
			this.getInUseMenuList(tsUserInfo);
			return tsUserInfo;

		} else {
			return null;
		}
	}


	@Transactional(readOnly=true)
	public String findUserByMenuEn(String menuEn){
		StringBuffer sb = new StringBuffer();
		sb.append(" select * from ( ");
		sb.append(" SELECT B.USER_INFO_ID,A.RID, A.MENU_LEVEL_NO, A.MENU_CN,A.ISFUNC,A.MENU_URI ");
		sb.append(" FROM TS_MENU A ");
		sb.append(" INNER JOIN TS_USER_MENU B ON B.MENU_TEMPLATE_ID = A.RID ");
		sb.append(" WHERE A.MENU_EN = '").append(menuEn).append("'");
		sb.append(" UNION ");
		sb.append(" SELECT c1.USER_INFO_ID,C3.RID, C3.MENU_LEVEL_NO, C3.MENU_CN ,C3.ISFUNC ,C3.MENU_URI");
		sb.append(" FROM TS_USER_ROLE C1 ");
		sb.append(" INNER JOIN TS_ROLE_MENU C2 ON C2.ROLE_ID = C1.ROLE_ID ");
		sb.append(" INNER JOIN TS_MENU C3 ON C2.MENU_ID = C3.RID ");
		sb.append(" WHERE C3.MENU_EN = '").append(menuEn).append("')");
		sb.append("   order by MENU_LEVEL_NO ");
		List<Object[]> rstList = em.createNativeQuery(sb.toString()).getResultList();
		String rids ="";
		if(!CollectionUtils.isEmpty(rstList)){
			for(Object[] orr: rstList){
				rids +=orr[0].toString()+",";
			}
			rids = rids.substring(0,rids.length()-1);
		}
		return  rids;
	}

	public TsUnit findTsUnit(String userIds,String zoneGb,boolean ifCity){
		StringBuffer sb = new StringBuffer();
		sb.append(" select a.rid from TS_USER_INFO a");
		sb.append(" left join ts_unit b on a.UNIT_RID = b.rid");
		sb.append(" left join ts_zone c on c.rid = b.MANAGE_ZONE_ID");
		sb.append(" where 1= 1");
		if(StringUtils.isNotBlank(userIds)){
			sb.append("and a.rid in (").append(userIds).append(")");
		}
		if(StringUtils.isNotBlank(zoneGb)){
			sb.append("and c.zone_gb like '").append(zoneGb).append("%'");
		}
		if(ifCity){
			sb.append(" and c.zone_type =3");
		}else{
			sb.append(" and c.zone_type =4");
		}
		sb.append("Order by c.zone_gb desc");
		List<Object> rstList = em.createNativeQuery(sb.toString()).getResultList();
		if(null != rstList && rstList.size()>0){
			Integer userId = Integer.parseInt(rstList.get(0).toString());
			TsUserInfo user = find(TsUserInfo.class,userId);
			return  user.getTsUnit();
		}else{
			return null;
		}
	}
	/**
 	 * <p>方法描述：更新用户手机号</p>
 	 * @MethodAuthor qrr,2021年12月28日,updateUserPhone
	 * */
	public void updateUserPhone(Integer rid,String newPhone) {
		if (null==rid) {
			return;
		}
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("newPhone", newPhone);
		this.executeSql("UPDATE TS_USER_INFO SET MB_NUM =:newPhone WHERE RID ="+rid, paramMap);
	}
}
