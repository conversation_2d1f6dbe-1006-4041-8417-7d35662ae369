package com.chis.modules.system.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.FileItem;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.org.bjca.seal.esspdf.client.ClientConstant;
import cn.org.bjca.seal.esspdf.client.tools.ESSPDFClientTool;
import cn.org.bjca.seal.esspdf.platform.sdk.message.ChannelMessage;
import cn.org.bjca.seal.esspdf.platform.sdk.message.CloudRespMessage;
import cn.org.bjca.seal.esspdf.platform.sdk.message.DocumentInfo;
import cn.org.bjca.seal.esspdf.platform.sdk.message.ReqMessage;
import cn.org.bjca.seal.esspdf.platform.sdk.message.UserInfoMessage;
import cn.org.bjca.seal.esspdf.platform.sdk.utils.ClientUtil;

import com.alibaba.fastjson.JSON;
import com.chis.common.bean.FileBaseBean;
import com.chis.common.utils.Exceptions;
import com.chis.common.utils.FileUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.ProcessReqUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.enumn.ResponseState;
import com.chis.modules.system.logic.RptSignParamPO;
import com.chis.modules.system.logic.SysReturnPojo;
import com.chis.modules.system.utils.AuthTokenIdUtil;
import com.chis.modules.system.utils.ReturnJsonUtils;
/**
 * <p>类描述：签章接口迁移</p>
 * @ClassAuthor qrr,2018年4月21日,SignatureUploadService
 * */
@Service
@Transactional(readOnly = true)
public class SignatureUploadService {
	protected static Logger logger = Logger.getLogger(SignatureUploadService.class);

	public void processReq(HttpServletRequest req, HttpServletResponse resp) throws IOException {
		try {
			ProcessReqUtil.setReqResEncoding(req, resp);
			ProcessReqUtil.setRespCrossDomain(resp);
//			/** 请求参数集合 */
//			Map<String, String> requestMap = ProcessReqUtil.processRequestMap(req);
//
//			String tokenId = requestMap.get("tokenId");
//			/** 根据tokenId获取单位信息 */
//			TsUnit cachUnit = AuthTokenIdUtil.findTokenIdCachUnit(req,tokenId);
////
//			/** 根据tokenId获取单位信息 */
			// 验证tokenId是否有效
//			if (cachUnit == null) {
//				ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.TOKENID_VALID.getTypeNo(), "认证TokenId无效，请重新获取TokenId！"));
//				return;
//			} else {
				Map<String, FileBaseBean> fileMap = ProcessReqUtil.process(req);
				req.setAttribute("dataJson", "uploadFile");
				req.setAttribute("fileMap", fileMap);
				execute(req, resp);
//			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(Exceptions.getStackTraceAsString(e));
			ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.EXCEPTION_PROCESS.getTypeNo(), e.getMessage()));
		}
	}
	/**
	 * 执行类可重写
	 *
	 * @throws IOException
	 */
	public void execute(HttpServletRequest req, HttpServletResponse resp) throws IOException {
//		/** 请求Json字符串 */
		Object dataJson = req.getAttribute("dataJson");
		String json = dataJson == null ? "" : dataJson.toString();
		if (StringUtils.isBlank(json)) {
			ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.NO_DATA.getTypeNo(), "请求JSON数据为空！"));
			return;
		} else {
			this.verifyAndSaveData(req, resp);
		}
	}

	public void verifyAndSaveData(HttpServletRequest req,HttpServletResponse resp)throws IOException {
		//参数必填验证，参数数量较少不适用验证控件
		//		rid			附件RID，由业务平台提供	N..11			必填
		//		filePath	待签章PDF文件路径		S..400			必填
		//		ifSign		是否签章				N1		1是 0 否	必填
		//		signParam	签章参数				S..1000			如果是否签章为1此项必填
		Map<String, FileBaseBean> fileMap = (Map<String, FileBaseBean>) req.getAttribute("fileMap");

		String filePath= this.getFormValue(req,fileMap,"filePath");
		if(StringUtils.isBlank(filePath)){
			ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.EXCEPTION_PROCESS.getTypeNo(), "请求参数filePath为空！"));
			return;
		}
		
		String ifSign= this.getFormValue(req,fileMap,"ifSign");
		if(StringUtils.isBlank(ifSign)){
			ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.EXCEPTION_PROCESS.getTypeNo(), "请求参数ifSign为空！"));
			return;
		}
		
		if(!"1".equals(ifSign) && !"0".equals(ifSign)){
			ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.EXCEPTION_PROCESS.getTypeNo(), "请求参数ifSign允许值为1或0！"));
			return;
		}
		
		String signParam= this.getFormValue(req,fileMap,"signParam");
		if(StringUtils.isBlank(signParam) && "1".equals(ifSign)){
			ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.EXCEPTION_PROCESS.getTypeNo(), "请求参数ifSign为1，signParam必填！"));
			return;
		}

		
		String fileTempPath = JsfUtil.getAbsolutePath()+File.separator+"sign"+File.separator+ StringUtils.uuid()+".pdf";
		File file;
		FileItem fileItem;
		try{
			fileItem = getFileItem(fileMap);
			if(fileItem==null){
				ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.EXCEPTION_PROCESS.getTypeNo(), "本地文件写入失败！"));
				return ;
			}
			file = saveFileToTempDir(fileTempPath,fileItem);
		}catch(IOException e){
			e.printStackTrace();
			ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.EXCEPTION_PROCESS.getTypeNo(), "本地文件写入失败！"));
			return ;
		}
		if(file==null){
			ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.EXCEPTION_PROCESS.getTypeNo(), "pdf文件上传失败！"));
			return ;
		}
		//判断是否签章
		//签章完成后返回文件
		if("1".equals(ifSign) && signPdf(req, resp,file,signParam))
			return;
		
		//通过附件PATH获取附件地址
//		String filePath=tdJdcfAnnex.getAnnexPath();
//		filePath=filePath.substring(0, filePath.lastIndexOf("/")+1);
		try {
//			filePath = doUpload(file, filePath);
			filePath = doUpload(fileItem, filePath,fileTempPath);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(Exceptions.getStackTraceAsString(e));
			ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.EXCEPTION_PROCESS.getTypeNo(), "附件上传OSS失败！"));
			return ;
		}
		if(StringUtils.isBlank(filePath)){
			ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.EXCEPTION_PROCESS.getTypeNo(), "上传附件不能为空！"));
			return;
		}
//		tdJdcfAnnex.setAnnexPath(filePath);
//		tdJdcfAnnexService.saveOrUpdate(tdJdcfAnnex);
		ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.SUCCESS_PROCESS.getTypeNo(), filePath));
		if(file.exists())
			file.delete();
	}



	private String doUpload(FileItem fileItem, String filePath,String fileTempPath) throws Exception {
		String extName = fileTempPath.substring(fileTempPath.lastIndexOf("."));
		String uuid =  UUID.randomUUID().toString().replaceAll("-", "");
		String newFileName = uuid + extName;
		filePath = filePath + newFileName;
		FileUtils.uploadFile(filePath, fileItem);
		return filePath;
	}


	/**
	 * 获取请求参数
	 * @param req
	 * @param fileMap
	 * @param key
	 * @return
	 */
	private  String getFormValue(HttpServletRequest req, Map<String, FileBaseBean> fileMap, String key){
		Object object=req.getParameter(key);
		if(object!=null)
			return object.toString();
		FileBaseBean base = fileMap.get(key);
		if(base==null)
			return null;
		return (String) base.getIn();
	}
	/**
	 * 从流里面读取附件
	 * @param fileTempPath
	 * @param fileItem
	 * @return
	 * @throws IOException 
	 */
	private File saveFileToTempDir(String fileTempPath, FileItem fileItem) throws IOException {
		File date = new File(fileTempPath);
		FileUtils.createFile(date);
        //打开流  
        InputStream it = fileItem.getInputStream();
        
        OutputStream os = new FileOutputStream(date);  
        boolean flag = true;
        //文件拷贝      
        byte flush[]  = new byte[1024];  
        int len = 0;  
        while(0<=(len=it.read(flush))){  
            os.write(flush, 0, len); 
            if(flag)
            	flag = false;
        }  
        //关闭流的注意 先打开的后关  
        os.close();  
        it.close();  
        if(flag)
        	return null;
		return date;
	}

	private FileItem getFileItem(Map<String, FileBaseBean> fileMap) {
		FileBaseBean fileBean = null;
		Set<String> keys = fileMap.keySet();
		for (String key : keys) {
			if (!fileMap.get(key).isFormFiled()) {
				fileBean = fileMap.get(key);
				break;
			}
		}
		if (fileBean==null || fileBean.getIn()==null) {
			return null;
		}
		return (FileItem) fileBean.getIn();
	}

	/**
	 * 签章函数，如果失败直接返回响应信息并且return true
	 * 成功return false
	 * @param file
	 * @param signParam
	 * @return
	 * @throws IOException 
	 */
	private boolean signPdf(HttpServletRequest req,
			HttpServletResponse resp,File file, String signParam) throws IOException {
		long begin = System.currentTimeMillis();
		try{
			RptSignParamPO signParams = JSON.parseObject(signParam.replace("=\r\n", ""), RptSignParamPO.class);
			if(signParams==null){
				logger.error("signParam:"+signParam);
				ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.EXCEPTION_PROCESS.getTypeNo(), "签章参数传递异常！"));
				return true;
			}

			byte[] pdfBty = ClientUtil.readFileToByteArray(file);// pdf字节数组,
			
			ReqMessage reqMessage = new ReqMessage();
			reqMessage.setAppId(signParams.getAppId()); // 应用ID
			reqMessage.setRuleNum(signParams.getRuleNum()); // 签章模板规则号 定位签章位置
	
			DocumentInfo document = new DocumentInfo();
			document.setDocuName(StringUtils.uuid()+".pdf"); // 文档名称
			reqMessage.setDocumentInfo(document);
	
			// 设置 需要调用的企业机构信息
	
			reqMessage.setUserinfo(new UserInfoMessage());
			HashMap<String, String> creditCodes = new HashMap<String, String>();
			reqMessage.getUserinfo().setChannelId(signParams.getChannelId()); // 渠道编号
			creditCodes.put("ORG", signParams.getOrgCode()); // 安徽省食品药品监督管理局 -机构代码1230012311
			reqMessage.getUserinfo().setCreditCodes(creditCodes);
	
			// 调用签章
			int signPort = 13001;
			if(StringUtils.isNotBlank(signParams.getSignPort())){
				signPort = Integer.valueOf(signParams.getSignPort());
			}
			ESSPDFClientTool essPDFClientTool = new ESSPDFClientTool(signParams.getSignPath(), signPort); // 网关地址和端口
			ChannelMessage message = essPDFClientTool.cloudSealPdfSign(reqMessage,
					pdfBty);
			CloudRespMessage resMessage = message.getCloudRespMessage();
	
			if (resMessage != null
					&& ClientConstant.SUCCESS.equals(resMessage.getStatusCode())) {
				ClientUtil.writeByteArrayToFile(file,
						message.getBody());
			}else{
				ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.EXCEPTION_PROCESS.getTypeNo(), message.getStatusInfo()));
				return true;
			}
		}catch(Exception e){
			e.printStackTrace();
			ReturnJsonUtils.packResponseInfo(req, resp, new SysReturnPojo(ResponseState.EXCEPTION_PROCESS.getTypeNo(), "签章过程失败！"));
			return true;
		}
		long end = System.currentTimeMillis();
		System.out.println("********************PdfSign end :"
				+ (end - begin) / 1000f + "s");
		return false;
	}
	
	public String doUpload(File file, String path) throws Exception {
		if(!file.exists())
			return null;
		String extName = file.getName().substring(file.getName().lastIndexOf("."));
		String uuid =  UUID.randomUUID().toString().replaceAll("-", "");
		String newFileName = uuid + extName;
		String filePath = path + newFileName;
		FileUtils.uploadFile(filePath, new FileInputStream(file), null);
		return filePath;
	}
	
}
