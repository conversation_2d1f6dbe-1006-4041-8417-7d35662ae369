package com.chis.modules.system.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.ss.formula.functions.T;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MapUtils;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.logic.ExampoolNumFillBean;
/**
 * 
 *题目选项配置规则
 * <AUTHOR>
 *
 */

@Service
@Transactional(readOnly = false)
public class ConfigFactorSlowQue implements ProConfigFactor {
	 private String num;
	 private String script;
	//1，根据map中脚本script,选项序号num,解析脚本得到factor，和actType
	//2,将factor和actType传到配置危险因素界面
	@Override
	public void doConfigFactor(Map<String, Object> map) {
		Integer type=(Integer) map.get("type");
		List<String> paramList3 = new ArrayList<String>(1);
		paramList3.add(type.toString());
		if(type==0){
		
			Map<String, Object> options = MapUtils.produceDialogMap(null,600,
					null, 400);
			Map<String, List<String>> paramsMap = new HashMap<String, List<String>>();
			List<String> paramList1 = new ArrayList<String>(1);
			List<String> paramList2 = new ArrayList<String>(1);
			script = (String) map.get("script");
			num=(String) map.get("num");
			String factors="";
			String actions="";
			if (StringUtils.isNotBlank(script)) {
				String[] s = script.split("};");
				for (int i = 0; i < s.length; i++) {
					if (s[i].contains("\"" + num.toString() + "\"")) {
						if (s[i].contains("addSet")) {
							actions+="1,";
						} else {
							actions+="2,";
						}
						factors+=s[i].substring(s[i].indexOf("add(") + 5,
								s[i].indexOf("add(") + 9).trim()+",";
	
					}
				}
				
				paramList1.add("".equals(actions)?null:actions.substring(0, actions.length()-1));
				paramList2.add("".equals(factors)?null:factors.substring(0,factors.length()-1));
			} else {
				paramList1.add(null);
				paramList2.add(null);
			}
			paramsMap.put("factorId", paramList2);
			paramsMap.put("actType", paramList1);
			paramsMap.put("type", paramList3);
			RequestContext.getCurrentInstance().openDialog(
					"/webapp/slowque/selectSlowFactor", options, paramsMap);
		}
		if(type==1){
			
			Map<String, Object> options = MapUtils.produceDialogMap(null,400,
					null, 350);
			Map<String, List<String>> paramsMap = new HashMap<String, List<String>>();
			List<String> paramList1 = new ArrayList<String>(1);
			List<String> paramList2 = new ArrayList<String>(1);
			ExampoolNumFillBean enb=(ExampoolNumFillBean) map.get("enb");
			if(enb!=null && enb.getFactorAndAction()!=null){
				String[] factorAndActions=enb.getFactorAndAction().split(";");
				String factors="";
				String actions="";
				for(int i=0;i<factorAndActions.length;i++){
					actions+=factorAndActions[i].substring(factorAndActions[i].indexOf(",")+1)+",";
					factors+=factorAndActions[i].substring(0,factorAndActions[i].indexOf(","))+",";
				}
				paramList1.add(actions.substring(0, actions.length()-1));
				paramList2.add(factors.substring(0,factors.length()-1));
			}else{
				paramList1.add(null);
				paramList2.add(null);
			}
			paramsMap.put("factorId", paramList2);
			paramsMap.put("actType", paramList1);
			paramsMap.put("type", paramList3);
			RequestContext.getCurrentInstance().openDialog(
					"/webapp/slowque/selectSlowFactor", options, paramsMap);
			
		}
	}
	
	/** 危险因素规则返回后处理 */
	public String  doCloseDialogSelect(SelectEvent event,String num,String script,String qesCode) {
		Map<String, Object> selectedMap = (Map<String, Object>) event
				.getObject();
		String s=script;
		if (null != selectedMap && selectedMap.size() > 0) {
			Object actType =  selectedMap.get("actType");
			Object factorId = selectedMap.get("factorId");
			if(actType==null || factorId==null){//若返回null，表示删除该规则
				s=deleteScriptByNum(num, script);
				JsfUtil.addSuccessMessage("配置规则已删除，请点击保存！");
			}else{
				String[]  actTypes=actType.toString().replace("[", "").replace("]", "").split(",");
				String[]  factorIds=factorId.toString().replace("[", "").replace("]", "").split(",");
				List<String> actTypeList=new ArrayList<String>(0);
				List<String> factorIdList=new ArrayList<String>(0);
				for(String s1:actTypes){
					actTypeList.add(s1.trim());
				}
				for(String s2:factorIds){
					factorIdList.add(s2.trim());
				}
				s=updateScript(num, factorIdList, actTypeList,script,qesCode);
				JsfUtil.addSuccessMessage("配置规则已修改，请点击保存！");
			}
			
		} 
		return s;
	}

	/** 危险因素规则返回后处理 */
	public Object doCloseDialogSelect(Object entity,SelectEvent event){
		ExampoolNumFillBean enb=new ExampoolNumFillBean();
		enb=(ExampoolNumFillBean) entity;
		Map<String, Object> selectedMap = (Map<String, Object>) event
				.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			Object actType =  selectedMap.get("actType");
			Object factorId = selectedMap.get("factorId");
			if(actType==null || factorId==null){//若返回null，表示删除该规则
				enb.setFactorAndAction(null);
				JsfUtil.addSuccessMessage("配置规则已删除！");
			}else{
				String[]  actTypes=actType.toString().replace("[","").replace("]","").split(",");
				String[]  factorIds=factorId.toString().replace("[","").replace("]","").split(",");
				String factorIdsAndActTypes="";
				for(int i=0;i<factorIds.length;i++){
					factorIdsAndActTypes+=factorIds[i].trim()+","+actTypes[i].trim()+";";
				}
				enb.setFactorAndAction(factorIdsAndActTypes.substring(0,factorIdsAndActTypes.length()-1));
				
				JsfUtil.addSuccessMessage("配置规则已修改，请点击保存！");
			}
			
		} 
		return enb;
	};
	
	
	/** 根据传回来的actType，factor更新脚本 **/
	public String  updateScript(String num, List<String> factorId, List<String> actType,String script,String qesCode) {
		String s=deleteScriptByNum(num,script);
		return addScript(num, factorId, actType, s,qesCode);
	}

	/** 根据序号新增脚本 **/
	public String addScript(String num, List<String> factorId, List<String> actType,String script,String qesCode) {
		if (isHaveFactor(num,script) == null) {
			String s1="";
			for(int i=0;i<factorId.size();i++){
			 s1 += "if(\"" + num + "\".equals(q"
					+ qesCode + ")){ "
					+ ("1".equals(actType.get(i)) ? "add" : "del") + "Set.add(\"" + factorId.get(i)
					+ "\"); };";
			}
			script = (script == null ? "" : script) + s1;
			
		}
		return script;
	}

	/** 当前脚本是否有该序号规则 */
	//有则返回该序号对应脚本（缺失"};"）,没有返回null
	public String isHaveFactor(String num,String script) {
		if (StringUtils.isNotBlank(script)) {
			String[] s = script.split("};");
			for (int i = 0; i < s.length; i++) {
				if (s[i].contains("\"" + num.toString() + "\"")) {
					return s[i];
				}
			}
		}
		return null;
	}

	@Override
	public String updateScript(String startNum, String updateNum,
			String script) {
		String ss=script;
		if (!startNum.equals(  updateNum) && isHaveFactor(startNum,script) != null) {
			String s = isHaveFactor(startNum,script).replace("\"" + startNum.toString() + "\"",
					"\"" + updateNum.toString() + "\"");
			ss=deleteScriptByNum(startNum,script);
			ss=ss+ s+ "};";
		}
		return ss;
	}

	@Override
	public String deleteScriptByNum(String num, String script) {
		String ss = "";
		if (StringUtils.isNotBlank(script)) {
			String[] s = script.split("};");
			for (int i = 0; i < s.length; i++) {
				if (!s[i].contains("\"" + num.toString() + "\"")) {
					ss += s[i] + "};";
				}
			}
		}
		script = ss;
		return script;
	}
	
}
