package com.chis.modules.system.service;

import java.awt.Image;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.DecimalFormat;
import java.util.Iterator;
import java.util.Map;

import javax.imageio.ImageIO;

import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.TokenStream;
import org.apache.lucene.document.Document;
import org.apache.lucene.document.Field;
import org.apache.lucene.document.FieldType;
import org.apache.lucene.index.FieldInfo.IndexOptions;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.Term;
import org.apache.lucene.search.IndexSearcher;
import org.apache.lucene.search.highlight.Highlighter;
import org.apache.lucene.search.highlight.SimpleFragmenter;
import org.apache.lucene.search.highlight.TextFragment;
import org.apache.lucene.search.highlight.TokenSources;
import org.apache.poi.POIXMLDocument;
import org.apache.poi.POIXMLTextExtractor;
import org.apache.poi.hslf.HSLFSlideShow;
import org.apache.poi.hslf.model.Slide;
import org.apache.poi.hslf.model.TextRun;
import org.apache.poi.hslf.usermodel.SlideShow;
import org.apache.poi.hssf.extractor.ExcelExtractor;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.xssf.extractor.XSSFExcelExtractor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.pdfbox.pdfparser.PDFParser;
import org.pdfbox.pdmodel.PDDocument;
import org.pdfbox.util.PDFTextStripper;
import org.springframework.stereotype.Component;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.logic.LucenePojo;

/**
 * 模块说明：读取创建索引公共类
 * 
 * <AUTHOR>
 * @createDate 2016年11月1日
 */
@Component
public class CreateLuceneIndex {

	/** 不分词 */
	private FieldType ftIndexNoAnaly;
	private FieldType ftIndexAnaly;

	public CreateLuceneIndex() {
		// 不分词索引
		ftIndexNoAnaly = new FieldType(); // 索引类型
		ftIndexNoAnaly.setIndexed(true);// 设置索引为true
		ftIndexNoAnaly.setStored(true); // 设置保存为true
		ftIndexNoAnaly.setTokenized(false); // 设置分词为false
		ftIndexNoAnaly.setIndexOptions(IndexOptions.DOCS_AND_FREQS_AND_POSITIONS);   
		// 分词索引
		ftIndexAnaly = new FieldType(); // 索引类型
		ftIndexAnaly.setIndexed(true);// 设置索引为true
		ftIndexAnaly.setStored(true); // 设置保存为true
		ftIndexAnaly.setTokenized(true); // 设置分词为true
		ftIndexAnaly.setIndexOptions(IndexOptions.DOCS_AND_FREQS_AND_POSITIONS);
	}

	/**
	 * 将数据文档写入文件中
	 * 
	 * @param doc
	 * @param lucenePojo
	 * @param file
	 * @param writer
	 * @throws Exception
	 */
	private void writeInfoToDoc(IndexWriter writer, Document doc, LucenePojo lucenePojo, File file) throws Exception {
		if (null != writer && null != doc && null != lucenePojo && null != file) {

			String annexName = lucenePojo.getAnnexName();
			// 如果业务描述的文件名为空，则直接取文件名即可
			String fileName = StringUtils.isBlank(annexName) ? file.getName() : annexName;

			doc.add(new Field("filename", fileName, ftIndexAnaly));
			doc.add(new Field("filesize", FormetFileSize(file), ftIndexNoAnaly));

			// 过滤类型
			// 知识库标题
			if (StringUtils.isNotBlank(lucenePojo.getKleTitle())){
				doc.add(new Field("kleTitle",lucenePojo.getKleTitle(),ftIndexAnaly));
			}
			//关键词
			if(StringUtils.isNotBlank(lucenePojo.getKeyWords())){
				doc.add(new Field("keyWords",lucenePojo.getKeyWords(),ftIndexAnaly));
			}
			// 信息类别
			if (StringUtils.isNotBlank(lucenePojo.getInfoType())) {
				doc.add(new Field("infoType", lucenePojo.getInfoType(), ftIndexNoAnaly));
			}
			//分类类别
			if (StringUtils.isNotBlank(lucenePojo.getPicType())){
				doc.add(new Field("type", lucenePojo.getPicType(), ftIndexAnaly));
			}
			//信息状态
			if (StringUtils.isNotBlank(lucenePojo.getState())){
				doc.add(new Field("state", lucenePojo.getState(), ftIndexAnaly));
			}
			// 地区编码
			if (StringUtils.isNotBlank(lucenePojo.getZoneGb())) {
				doc.add(new Field("zoneGb", lucenePojo.getZoneGb(), ftIndexNoAnaly));
			}
			// 单位性质
			if (StringUtils.isNotBlank(lucenePojo.getSortCodes())) {
				doc.add(new Field("sortCodes", lucenePojo.getSortCodes(), ftIndexNoAnaly));
			}
			// 单位ID
			if (StringUtils.isNotBlank(lucenePojo.getUnitId())) {
				doc.add(new Field("unitId", lucenePojo.getUnitId(), ftIndexNoAnaly));
			}
			// 修改时间
			if (null != lucenePojo.getModifyDate()) {
				doc.add(new Field("modifyDate", String.valueOf(lucenePojo.getModifyDate().getTime()), ftIndexNoAnaly));
			}
			System.err.println("writeFilePath:" + file.getAbsolutePath());
			// 3、向索引库中写入文档内容
			writer.addDocument(doc);
		}
	}

	/**
	 * 根据不同文件创建文件索引
	 * 
	 * @param writer
	 *            写索引对象
	 * @param file
	 *            文件夹
	 * @throws Exception
	 */
	public void index(IndexWriter writer, File file, LucenePojo lucenePojo) throws Exception {
		// 1、根据文件创建文档Document
		// 获取文件路径
		String s = file.getPath();
		s=s.replace("\\","/");
		// 获取文件的后缀名称
		String fileSuffix = s.substring(s.lastIndexOf(".") + 1);
		Document doc = null;
		// 是否存在业务数据
		if (null != lucenePojo) {
			try {
				if (fileSuffix.equals("xls")) {
					doc = readExcel(s);
				} else if (fileSuffix.equals("xlsx")) {
					doc = readExcelx(s);
				} else if (fileSuffix.equals("doc")) {
					doc = readDoc(s);
				} else if (fileSuffix.equals("docx")) {
					doc = readDocx(s);
				} else if (fileSuffix.equals("ppt")) {
					doc = readPpt(s);
				} else if (fileSuffix.equals("pdf")) {
					doc = readPdf(s);
				} else if (fileSuffix.equals("txt") || fileSuffix.equals("xml") || fileSuffix.equals("tsv")) {
					doc = readTxtType(file);
				} else if (fileSuffix.equals("jpg") || fileSuffix.equals("jpeg") || fileSuffix.equals("bmp")){
					doc = readPic(s);
				}
				if (null != doc) {
					this.writeInfoToDoc(writer, doc, lucenePojo, file);
				}
			} catch (Exception e) {
				System.err.println(new StringBuilder("Index File Failure!FilePath:").append(s).append(".Cause:").append(e.getMessage()).toString());
			}
		}
	}

	/**
	 * 2003版本的xls 文件读取
	 * 
	 * @param xls
	 *            路径地址
	 * @return
	 * @throws Exception
	 */
	public Document readExcel(String xls) {
		InputStream in = null;
		try {
			// 创建输入流读取xls文件
			in = new FileInputStream(xls);// xls文件存储地址
			HSSFWorkbook workbook = new HSSFWorkbook(in); // 读取一个文件
			ExcelExtractor extractor = new ExcelExtractor(workbook);

			extractor.setFormulasNotResults(true);
			extractor.setIncludeSheetNames(false);

			String text = extractor.getText();
			// 返回文件的Sting类型文字
			Document docexcel = new Document();
			docexcel.add(new Field("description", text, ftIndexAnaly));
			docexcel.add(new Field("path", xls, ftIndexNoAnaly));
			return docexcel;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != in) {
				try {
					in.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return null;
	}

	/**
	 * 2003版本的xls 文件读取
	 * 
	 * @param xls
	 *            路径地址
	 * @return
	 * @throws Exception
	 */
	public Document readTxtType(File file) throws Exception {
		InputStream dataIn = null;
		BufferedReader br = null;
		try {
			Document doc = new Document();
			dataIn = new FileInputStream(file);
			br = new BufferedReader(new InputStreamReader(dataIn));
			String line = br.readLine();
			StringBuffer readLineStr = new StringBuffer();
			while (line != null) {
				line = line.trim();
				if (line.length() > 0) {
					readLineStr.append(line);
				}
				line = br.readLine();
			}
			doc.add(new Field("description", readLineStr.toString(), ftIndexAnaly));
			doc.add(new Field("path", file.getPath(), ftIndexNoAnaly));
			return doc;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != br) {
				try {
					br.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (null != dataIn) {
				try {
					dataIn.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return null;
	}

	/**
	 * 2007版本的xls 文件读取
	 * 
	 * @param xls
	 *            路径地址
	 * @return
	 * @throws Exception
	 */
	public Document readExcelx(String xls) throws Exception {
		InputStream in = null;
		try {
			// 创建输入流读取xls文件
			in = new FileInputStream(xls);// xls文件存储地址
			XSSFWorkbook workbook = new XSSFWorkbook(in); // 读取一个文件
			XSSFExcelExtractor extractor = new XSSFExcelExtractor(workbook);

			extractor.setFormulasNotResults(true);
			extractor.setIncludeSheetNames(false);

			String text = extractor.getText();
			// 返回文件的Sting类型文字
			Document docexcel = new Document();
			docexcel.add(new Field("description", text, ftIndexAnaly));
			docexcel.add(new Field("path", xls, ftIndexNoAnaly));
			return docexcel;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != in) {
				try {
					in.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return null;
	}

	/**
	 * 2007版本的doc文件读取
	 * 
	 * @param fileName
	 * @return
	 * @throws Exception
	 */
	public Document readDocx(String fileName) throws Exception {
		OPCPackage opcPackage = POIXMLDocument.openPackage(fileName);
		POIXMLTextExtractor ex = new XWPFWordExtractor(opcPackage);
		Document docexcel = new Document();
		String text = ex.getText();
		docexcel.add(new Field("description", text, ftIndexAnaly));
		docexcel.add(new Field("path", fileName, ftIndexNoAnaly));

		return docexcel;
	}

	/**
	 * 2003版本的doc文件读取
	 * 
	 * @param doc
	 *            路径地址
	 * @return
	 * @throws Exception
	 */
	public Document readDoc(String doc) throws Exception {
		FileInputStream in = null;
		try {
			// 创建输入流读取DOC文件
			in = new FileInputStream(doc);
			WordExtractor extractor = null; // 创建WordExtractor
			extractor = new WordExtractor(in);// 对DOC文件进行提取

			String text = extractor.getText();

			Document docdoc = new Document();
			docdoc.add(new Field("description", text, ftIndexAnaly));
			docdoc.add(new Field("path", doc, ftIndexNoAnaly));
			return docdoc;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != in) {
				try {
					in.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return null;
	}

	/**
	 * ppt文件读取
	 * 
	 * @param ppt
	 *            路径地址
	 * @return
	 * @throws Exception
	 */
	public Document readPpt(String ppt) throws Exception {
		FileInputStream is = null;
		try {
			// 创建输入流读取ppt文件
			is = new FileInputStream(ppt);
			SlideShow ss = new SlideShow(new HSLFSlideShow(is));// is
																// 为文件的InputStream，建立SlideShow
			Slide[] slides = ss.getSlides();// 获得每一张幻灯片

			String text = new String();
			for (int i = 0; i < slides.length; i++) {
				TextRun[] t = slides[i].getTextRuns();// 为了取得幻灯片的文字内容，建立TextRun
				for (int j = 0; j < t.length; j++) {
					// 这里会将文字内容加到content中去
					text += t[j].getText();
				}
			}
			Document docppt = new Document();
			docppt.add(new Field("description", text, ftIndexAnaly));
			docppt.add(new Field("path", ppt, ftIndexNoAnaly));
			return docppt;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != is) {
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return null;
	}

	/**
	 * pdf文件读取
	 * 
	 * @param pdf
	 *            路径地址
	 * @return
	 * @throws Exception
	 */
	public Document readPdf(String pdf) throws Exception {
		// 创建输入流读取pdf文件
		String result = "";
		FileInputStream is = null;
		PDDocument document = null;
		try {
			is = new FileInputStream(pdf);
			PDFParser parser = new PDFParser(is);
			parser.parse();
			document = parser.getPDDocument();
			PDFTextStripper stripper = new PDFTextStripper();
			result = stripper.getText(document);
			Document docpdf = new Document();
			docpdf.add(new Field("description", result, ftIndexAnaly));
			docpdf.add(new Field("path", pdf, ftIndexNoAnaly));
			return docpdf;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			if (document != null) {
				try {
					document.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		return null;
	}
	
	/**
	 * 图片文件读取
	 * 
	 * @param pdf
	 *            路径地址
	 * @return
	 * @throws Exception
	 */
	public Document readPic(String pic) throws Exception{
		String picname=pic.substring(pic.lastIndexOf("\\")+1);

		Document docdoc = new Document();
		docdoc.add(new Field("description", picname, ftIndexAnaly));
		docdoc.add(new Field("path", pic, ftIndexNoAnaly));
		return docdoc;
	}

	/**
	 * 递归扫描整个文件路径内容，存入Map （第一次扫描纪录存储）
	 * 
	 * @param file
	 *            需要扫描的文件
	 * @param dirFileMap
	 *            存放扫描文件名称
	 * 
	 */
	public void totalScan(File file, Map<String, Long> dirFileMap) {
		String[] fileList = file.list(); // 判断是否为空目录
		if (null != fileList) {
			for (int i = 0; i < fileList.length; i++) {
				String pname = file.getAbsolutePath() + "\\" + fileList[i];
				File tempFile = new File(pname);
				if (tempFile.isDirectory()) {
					// dirFileMap.put(pname, tempFile);
					totalScan(tempFile, dirFileMap);
				} else { // 毫秒数
					// 不相同的文件夹下，存放的文件可能名字相同，但是同一路径下的文件肯定不会相同，
					// 所以采用全路径做为key值
					dirFileMap.put(pname, tempFile.lastModified());
				}
			}
		}
	}

	/**
	 * 得到删除的文件及文件夹,并删除已经不存在的文件信息 （检索到信息被删除方法，此处删除Lucene索引） （查询前调用）
	 * 
	 * @param nowDirFileMap
	 *            现存在的文件
	 * @param oldDirFileMap
	 *            旧存文件信息
	 */
	public void getDeletedFile(Map<String, Long> nowDirFileMap, IndexWriter writer, Map<String, Long> oldDirFileMap) throws Exception {
		for (Iterator<String> iterator = oldDirFileMap.keySet().iterator(); iterator.hasNext();) {
			String key = iterator.next();
			if (null == nowDirFileMap.get(key)) {
				// System.out.println("删除" + key);
				iterator.remove();
				try {
					writer.deleteDocuments(new Term("path",key));
					writer.commit();
				} catch (Exception e) {
					e.printStackTrace();
				}
				oldDirFileMap.remove(key);
			}
		}
	}

	/**
	 * 得到增加的文件及文件夹,并增加到已有的文件信息中 （查询前调用）
	 * 
	 * @param oldDirFileMap
	 *            旧存文件信息
	 */
	public void getAddedFile(Map<String, Long> nowDirFileMap, IndexWriter writer, Map<String, Long> oldDirFileMap, Map<String, LucenePojo> fileMap) throws Exception {
		for (Iterator<String> iterator = nowDirFileMap.keySet().iterator(); iterator.hasNext();) {
			String key = iterator.next();
			if (null == oldDirFileMap.get(key)) {
				oldDirFileMap.put(key, nowDirFileMap.get(key));
				// System.out.println("新增" + "---" + key);
				try {
					index(writer, new File(key), fileMap.get(key));
					writer.commit();
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				// 文件内容是否被修改过
				Long old = oldDirFileMap.get(key);
				Long now = nowDirFileMap.get(key);
				// System.out.println("key:"+key+"    old:"+old+"    now:"+now);
				if (old.longValue() != now.longValue()) {
					// 内容被修改过
					// 删除原索引
					try {
						writer.deleteDocuments(new Term("path", key));
						// 创建新文件索引
						index(writer, new File(key), fileMap.get(key));
						writer.commit();
					} catch (Exception e) {
						e.printStackTrace();
					}
					// 修改oldMap值
					oldDirFileMap.put(key, nowDirFileMap.get(key));
				}
			}
		}
	}

	/**
	 * 高亮显示
	 */
	public String highLighter(Highlighter highlighter, String name, int id, String rstStrs, IndexSearcher searcher, Analyzer analyzer) throws Exception {
		TokenStream tokenStream = TokenSources.getAnyTokenStream(searcher.getIndexReader(), id, name, analyzer);
		highlighter.setTextFragmenter(new SimpleFragmenter(50));
		TextFragment[] frag = highlighter.getBestTextFragments(tokenStream, rstStrs, false, 10);
		StringBuffer rtnStr = new StringBuffer();
		if (frag.length > 0) {

			rtnStr.append((frag[0].toString()));
			for (int j = 1; j < frag.length; j++) {
				if ((frag[j] != null) && (frag[j].getScore() > 0)) {
					rtnStr.append("<br/>").append((frag[j].toString()));
				}
			}
			if (rtnStr.length() > 0) {
				return rtnStr.toString().replaceAll("<B>", "<B style='color: red'>");
			} else {
				return rstStrs;
			}
		}
		return rstStrs;
	}

	private String FormetFileSize(File f) throws Exception {// 转换文件大小
		long fileS = f.length();
		DecimalFormat df = new DecimalFormat("#.00");
		String fileSizeString = "";
		if (fileS < 1024) {
			fileSizeString = df.format((double) fileS) + "B";
		} else if (fileS < 1048576) {
			fileSizeString = df.format((double) fileS / 1024) + "K";
		} else if (fileS < 1073741824) {
			fileSizeString = df.format((double) fileS / 1048576) + "M";
		} else {
			fileSizeString = df.format((double) fileS / 1073741824) + "G";
		}
		return fileSizeString;
	}
}
