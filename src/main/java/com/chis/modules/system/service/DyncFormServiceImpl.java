package com.chis.modules.system.service;

import java.sql.Clob;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.persistence.Query;

import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.alibaba.druid.pool.DruidDataSource;
import com.chis.activiti.script.IScript;
import com.chis.activiti.utils.GroovyScriptEngine;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TdFormDef;
import com.chis.modules.system.entity.TdFormField;
import com.chis.modules.system.entity.TdFormStatisticsDef;
import com.chis.modules.system.entity.TdFormTable;
import com.chis.modules.system.entity.TdFormType;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsRpt;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.DynaFieldShowType;
import com.chis.modules.system.enumn.DynaFieldType;

/**
 * 动态表单
 * 
 * <AUTHOR>
 * @createDate 2015-12-3
 */
@Service
@Transactional(readOnly = false)
public class DyncFormServiceImpl extends AbstractTemplate {

	@Autowired
	private CommServiceImpl commService;
	@Autowired
	private DruidDataSource dataSource;

	/** 动态表单前缀 */
	public final String DYNC_TABLE_PREFIX = "TZ_";
	/** 附件路径后缀 */
	public final String FJ_PATH_SUFFIX = "PATH";
	/** 附件名称后缀 */
	public final String FJ_NAME_SUFFIX = "NAME";
	
	@Transactional(readOnly = true)
	public List<Object[]> findAllTableList()	{
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT T.CN_NAME||'['||T.EN_NAME||']',T.FORM_PROP,T.RID FROM TD_FORM_TABLE T");
		sql.append(" WHERE T.FORM_PROP IN (1,2) ORDER BY T.CN_NAME");
		List<Object[]> resultList = this.em.createNativeQuery(sql.toString()).getResultList();
		return resultList;
	}
	

	@Transactional(readOnly = true)
	public String verifyQuerySql(String sql) {
		if (StringUtils.isNotBlank(sql)) {
			try {
				List<Object[]> resultList = this.em.createNativeQuery(sql).getResultList();
				if (null != resultList && resultList.size() > 0) {
					Object[] objects = resultList.get(0);
					if (objects.length != 2) {
						return "查询字典SQL语句只能有两个字段列，请检查！";
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
				return "查询字典SQL语句有误，请检查！";
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public TdFormDef findFormDef(Integer defId, Map<String, Object> vars, Integer rid) {
		TdFormDef def = super.em.find(TdFormDef.class, defId);
		if (null != def) {
			TdFormTable table = def.getTdFormTableByTableId();
			this.initMainFieldList(def, table, vars, rid, true);

			if (null != table && table.getFormProp().intValue() == 2) {
				List<TdFormTable> childList = table.getChildList();
				this.initMainFieldList(def, childList.get(0), vars, rid, false);
			}
		}
		return def;
	}

	/**
	 * 设置主表字段的信息
	 * 
	 * @param table
	 * @param vars
	 * @param rid
	 */
	@Transactional(readOnly = true)
	private void initMainFieldList(TdFormDef def, TdFormTable table, Map<String, Object> vars, Integer rid, boolean main) {
		List<TdFormField> fieldList = table.getFieldList();
		if (null != fieldList && fieldList.size() > 0) {
			if (null == rid) {
				// 添加初始化
				GroovyScriptEngine scriptEngine = SpringContextHolder.getBean(GroovyScriptEngine.class);
				for (TdFormField field : fieldList) {
					if (null != field.getScriptType() && field.getScriptType().intValue() == 0 && StringUtils.isNotBlank(field.getDataScript())) {
						// 设置初始化值
						Object value = scriptEngine.executeObject(field.getDataScript(), vars);
						value = this.convertObject(value, field.getFdDbtype());

						if (main) {
							def.getValueMap().put("DYNA_MAIN_" + field.getFdEnname(), value);
						} else {
							def.getValueMap().put("DYNA_SUB_" + field.getFdEnname() + "_0", value);
						}
					}
				}

				if (!main) {
					def.setRows(1);
				}
			} else {
				// 修改初始化
				StringBuilder sb = new StringBuilder(" SELECT * FROM ");
				sb.append(table.getEnName());
				if (main) {
					sb.append(" WHERE RID = '").append(rid).append("' ");
				} else {
					sb.append(" WHERE MAIN_ID = '").append(rid).append("' ORDER BY RID ");
				}

				Query query = super.em.createNativeQuery(sb.toString());
				query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
				List rows = query.getResultList();

				if (!main) {
					def.setRows(rows.size());
				}

				if (null != rows && rows.size() > 0) {
					if (main) {
						Map row = (Map) rows.get(0);
						for (TdFormField field : fieldList) {
							if(DynaFieldShowType.FJ.getTypeNo().equals(field.getDataSrc()) )	{
								String fieldEnN = field.getFdEnname() + FJ_NAME_SUFFIX;
								Object value = row.get(fieldEnN);
								def.getValueMap().put("DYNA_MAIN_" + fieldEnN, value);

								fieldEnN =  field.getFdEnname() + FJ_PATH_SUFFIX;
								value = row.get(fieldEnN);
								def.getValueMap().put("DYNA_MAIN_" + fieldEnN,  null == value?"" : (JsfUtil.getAbsolutePath()+value));
							}else{
								Object value = row.get(field.getFdEnname());
								value = this.convertObject(value, field.getFdDbtype());
								def.getValueMap().put("DYNA_MAIN_" + field.getFdEnname(), value);
							}
						}
					} else {
						for (int i = 0; i < rows.size(); i++) {
							Map row = (Map) rows.get(i);
							for (TdFormField field : fieldList) {
								if (DynaFieldShowType.FJ.getTypeNo().equals(field.getDataSrc())) {
									String fieldEnN = field.getFdEnname() + FJ_NAME_SUFFIX;
									Object value = row.get(fieldEnN);
									def.getValueMap().put("DYNA_SUB_" + field.getFdEnname() + "_" + i + FJ_NAME_SUFFIX, value);

									fieldEnN = field.getFdEnname() + FJ_PATH_SUFFIX;
									value = row.get(fieldEnN);
									def.getValueMap().put("DYNA_SUB_" + field.getFdEnname() + "_" + i + FJ_PATH_SUFFIX, null == value ? "" : (JsfUtil.getAbsolutePath() + value));

								} else {
									// 设置初始化值
									Object value = row.get(field.getFdEnname());
									value = this.convertObject(value, field.getFdDbtype());
									def.getValueMap().put("DYNA_SUB_" + field.getFdEnname() + "_" + i, value);
								}
							}
						}
					}
				}
			}
		}
	}

	/**
	 * object转string， 根据页面控件需求
	 * 
	 * @param value
	 * @param dbtype
	 * @return
	 */
	@Transactional(readOnly = true)
	private Object convertObject(Object value, String dbtype) {
		if (DynaFieldType.DATE.getTypeNo().equals(dbtype)) {
			value = value != null ? DateUtils.formatDate((Date) value, "yyyy-MM-dd") : null;
		} else if (DynaFieldType.CLOB.getTypeNo().equals(dbtype)) {
			Clob clobtmp = (Clob) value;
			try {
				if (clobtmp == null || clobtmp.length() == 0) {
					value = "";
				} else {
					value = clobtmp.getSubString((long) 1, (int) clobtmp.length());
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		} else if (DynaFieldType.TIMESTAMP.getTypeNo().equals(dbtype)) {
			try {
				value = value != null ? DateUtils.formatDate((Date) value, "yyyy-MM-dd HH:mm:ss") : null;
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		return value;
	}

	/**
	 * string转object， 根据页面控件需求
	 * 
	 * @param valueStr
	 * @param dbtype
	 * @return
	 */
	@Transactional(readOnly = true)
	private Object convertObject2(String valueStr, String dbtype) {
		if( StringUtils.isNotBlank(valueStr))	{
			Object value = valueStr;
			try {
				if (DynaFieldType.DATE.getTypeNo().equals(dbtype)) {
					value = DateUtils.parseDate(valueStr, "yyyy-MM-dd");
				} else if (DynaFieldType.TIMESTAMP.getTypeNo().equals(dbtype)) {
					value = DateUtils.parseDate(valueStr, "yyyy-MM-dd HH:mm:ss");
				}
			} catch (ParseException e) {
				e.printStackTrace();
			}
			return value;
		}
		return null;
	}

	public Integer saveOrUpdateFormData(TdFormDef tdFormDef, Map<String, Object> vars, Integer rid,
			Map<String, String> valueMap, TsUserInfo tsUserInfo) {
		try {
			if (null != tdFormDef) {
				// 主表
				TdFormTable table = tdFormDef.getTdFormTableByTableId();
				// 单表维护
				Integer mainId = this.saveMainList(table, vars, rid, valueMap,tsUserInfo);

				Integer formProp = table.getFormProp();
				if (formProp.intValue() == 2) {
					// 子表维护
					TdFormTable subTable = table.getChildList().get(0);
					this.saveSubList(subTable, vars, mainId, valueMap);
				}
				return mainId;
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			throw new RuntimeException(e);
		}
		return null;
	}

	/**
	 * 保存主表
	 * 
	 * @param table
	 * @param vars
	 * @param rid
	 * @param tsUserInfo 
	 * @return
	 * @throws Exception
	 */
	private Integer saveMainList(TdFormTable table, Map<String, Object> vars, Integer rid, Map<String, String> valueMap, TsUserInfo tsUserInfo)
			throws Exception {
		List<TdFormField> fieldList = table.getFieldList();
		if (null != fieldList && fieldList.size() > 0) {
			// 添加初始化
			GroovyScriptEngine scriptEngine = SpringContextHolder.getBean(GroovyScriptEngine.class);
			if (vars == null) {
				vars = new HashMap<String, Object>();
			}
			// 组织脚本变量
			for (TdFormField field : fieldList) {
				// 判断展示类型，如果为附件，则需要处理两个字段
				if(DynaFieldShowType.FJ.getTypeNo().equals(field.getDataSrc()) )	{
					Object valueStr = valueMap.get("DYNA_MAIN_" + field.getFdEnname() + FJ_PATH_SUFFIX);
					Object valueStr2 = valueMap.get("DYNA_MAIN_" + field.getFdEnname() + FJ_NAME_SUFFIX);
					
					field.setFjPath(StringUtils.objectToString(valueStr).replaceFirst(JsfUtil.getAbsolutePath(), ""));
					field.setFjName(StringUtils.objectToString(valueStr2));
				}else{
					Object valueStr = valueMap.get("DYNA_MAIN_" + field.getFdEnname());
					if(null != valueStr) {
						Object value = this.convertObject2(valueStr.toString(), field.getFdDbtype());
						field.setValue(value);
						vars.put(field.getFdEnname(), field.getValue());
					}
				}
			}
			Map<String, IScript> beansMap = SpringContextHolder.getBeans(IScript.class); 
			vars.putAll(beansMap);
			// 设置隐藏的值
			for (TdFormField field : fieldList) {
				if ( null != field.getScriptType() && field.getScriptType().intValue() == 1 && StringUtils.isNotBlank(field.getDataScript())) {
					field.setValue(scriptEngine.executeObject(field.getDataScript(), vars));
				}
			}

			StringBuilder col = new StringBuilder();
			StringBuilder val = new StringBuilder();
			if (null == rid) {
				StringBuilder sb = new StringBuilder(" SELECT ");
				sb.append(table.getEnName()).append("_SEQ.NEXTVAL FROM DUAL ");
				rid = Integer.valueOf(super.em.createNativeQuery(sb.toString()).getSingleResult().toString());

				col.append("INSERT INTO ").append(table.getEnName()).append("(RID,CREATE_MANID,CREATE_DATE");
				val.append(" VALUES(").append(rid).append(",").append(tsUserInfo.getRid()).append(",TO_DATE('").append(DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss")).append("', 'YYYY-MM-DD HH24:MI:SS')");

				for (TdFormField field : fieldList) {
					if(DynaFieldShowType.FJ.getTypeNo().equals(field.getDataSrc()) )	{
						col.append(",").append(field.getFdEnname() + FJ_PATH_SUFFIX);
						col.append(",").append(field.getFdEnname() + FJ_NAME_SUFFIX);
						if(StringUtils.isNotBlank(field.getFjName()) && StringUtils.isNotBlank(field.getFjPath()))	{
							val.append(",'").append(field.getFjPath()).append("'");
							val.append(",'").append(field.getFjName()).append("'");
						}else{
							val.append(",NULL ");
							val.append(",NULL ");
						}
					}else{
						col.append(",").append(field.getFdEnname());
						if (null != field.getValue()) {
							if (field.getFdDbtype().equalsIgnoreCase(DynaFieldType.TIMESTAMP.getTypeNo())) {
								val.append(", TO_DATE('").append(
										DateUtils.formatDate((Date) field.getValue(), "yyyy-MM-dd HH:mm:ss"));
								val.append("', 'YYYY-MM-DD HH24:MI:SS') ");
							} else if (field.getFdDbtype().equalsIgnoreCase(DynaFieldType.DATE.getTypeNo())) {
								val.append(", TO_DATE('").append(
										DateUtils.formatDate((Date) field.getValue(), "yyyy-MM-dd"));
								val.append("', 'YYYY-MM-DD') ");
							} else {
								val.append(",'").append(field.getValue()).append("'");
							}
						} else {
							val.append(",NULL ");
						}
					}
				}
				val.append(") ");
				col.append(") ").append(val);
			} else {
				col.append("UPDATE ").append(table.getEnName()).append(" SET RID = '").append(rid).append("' ");
				col.append(",").append("MODIFY_MANID='").append(tsUserInfo.getRid()).append("',MODIFY_DATE=TO_DATE('").append(
						DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
				col.append("', 'YYYY-MM-DD HH24:MI:SS') ");
				for (TdFormField field : fieldList) {
					if(DynaFieldShowType.FJ.getTypeNo().equals(field.getDataSrc()) )	{
						//附件路径
						col.append(",").append(field.getFdEnname() + FJ_PATH_SUFFIX).append("=");
						if (StringUtils.isNotBlank(field.getFjPath())) {
							col.append("'").append(field.getFjPath()).append("'");
						} else {
							col.append("NULL ");
						}
						//附件名称
						col.append(",").append(field.getFdEnname() + FJ_NAME_SUFFIX).append("=");
						if (StringUtils.isNotBlank(field.getFjName())) {
							col.append("'").append(field.getFjName()).append("'");
						} else {
							col.append("NULL ");
						}
					}else{
						col.append(",").append(field.getFdEnname()).append("=");
						if (null != field.getValue()) {
							if (field.getFdDbtype().equalsIgnoreCase(DynaFieldType.TIMESTAMP.getTypeNo())) {
								col.append("TO_DATE('").append(
										DateUtils.formatDate((Date) field.getValue(), "yyyy-MM-dd HH:mm:ss"));
								col.append("', 'YYYY-MM-DD HH24:MI:SS') ");
							} else if (field.getFdDbtype().equalsIgnoreCase(DynaFieldType.DATE.getTypeNo())) {
								col.append("TO_DATE('").append(DateUtils.formatDate((Date) field.getValue(), "yyyy-MM-dd"));
								col.append("', 'YYYY-MM-DD') ");
							} else {
								col.append("'").append(field.getValue()).append("'");
							}
						} else {
							col.append("NULL ");
						}
					}
				}
				col.append(" WHERE RID = '").append(rid).append("' ");
			}
			super.em.createNativeQuery(col.toString()).executeUpdate();
			return rid;
		}
		return null;
	}

	/**
	 * 保存子表
	 * 
	 * @param table
	 * @param vars
	 * @param rid
	 * @return
	 * @throws Exception
	 */
	private void saveSubList(TdFormTable table, Map<String, Object> vars, Integer rid, Map<String, String> valueMap)
			throws Exception {
		StringBuilder sb = null;
		GroovyScriptEngine scriptEngine = SpringContextHolder.getBean(GroovyScriptEngine.class);
		if (null != rid) {
			sb = new StringBuilder(" DELETE FROM ");
			sb.append(table.getEnName()).append(" WHERE MAIN_ID = '").append(rid).append("' ");
			super.em.createNativeQuery(sb.toString()).executeUpdate();
		}

		List<TdFormField> fieldList = table.getFieldList();
		
		if (null != fieldList && fieldList.size() > 0) {
			if (vars == null) {
				vars = new HashMap<String, Object>();
			}
		
			sb = new StringBuilder(" INSERT INTO ");
			sb.append(table.getEnName()).append(" (RID, MAIN_ID");

			Map<Integer, StringBuilder> tempMap = new HashMap<Integer, StringBuilder>();

			Set<Integer> rownumSet = new LinkedHashSet<Integer>();
			Set<String> keySet = valueMap.keySet();
			for (String key : keySet) {
				if (StringUtils.startsWith(key, "DYNA_SUB_")) {
					String rownum = StringUtils.substring(key, StringUtils.lastIndexOf(key, "_") + 1,key.length());
					if (StringUtils.isNumeric(rownum) && !rownumSet.contains(rownum)) {
						rownumSet.add(Integer.valueOf(rownum));
					}
				}
			}
			if(rownumSet.size() == 0 )	{
				return;
			}
			
			Map<String, IScript> beansMap = SpringContextHolder.getBeans(IScript.class); 
			vars.putAll(beansMap);
			List<Integer> rownumList = new ArrayList<Integer>(rownumSet);
			Collections.sort(rownumList);
			for (Integer i : rownumList) {
				for (TdFormField field : fieldList) {
					Object valueStr = valueMap.get("DYNA_SUB_" + field.getFdEnname()+ "_" + i);
					if(null != valueStr) {
						Object value = this.convertObject2(valueStr.toString(), field.getFdDbtype());
						field.setValue(value);
						vars.put(field.getFdEnname()+"_"+i, field.getValue());
					}
					
				}
				for (TdFormField field : fieldList) {
					if ( null != field.getScriptType() && field.getScriptType().intValue() == 1 && StringUtils.isNotBlank(field.getDataScript())) {
						valueMap.put("DYNA_SUB_" + field.getFdEnname() + "_" + i,String.valueOf(scriptEngine.executeObject(field.getDataScript(), vars)));
					}
				}
			}
				
			for (int j = 0; j < fieldList.size(); j++) {
				TdFormField field = fieldList.get(j);
				
				// 判断展示类型，如果为附件，则需要处理两个字段
				if(DynaFieldShowType.FJ.getTypeNo().equals(field.getDataSrc()) )	{
					sb.append(",").append(field.getFdEnname()+FJ_NAME_SUFFIX);
					sb.append(",").append(field.getFdEnname()+FJ_PATH_SUFFIX);
				}else{
					sb.append(",").append(field.getFdEnname());
				}
				
				for (Integer i : rownumList) {
					if(DynaFieldShowType.FJ.getTypeNo().equals(field.getDataSrc()) )	{
						String subValName = valueMap.get("DYNA_SUB_" + field.getFdEnname() + "_" + i + FJ_NAME_SUFFIX);
						String subValPath = valueMap.get("DYNA_SUB_" + field.getFdEnname() + "_" + i + FJ_PATH_SUFFIX);
						StringBuilder valBr = tempMap.get(i);
						if (null != valBr) {
							if (StringUtils.isNotBlank(subValName) ) {
								valBr.append(",'").append(subValName).append("'").append(" AS F").append(j).append("_0");
								valBr.append(",'").append(subValPath.replaceFirst(JsfUtil.getAbsolutePath(), "")).append("'").append(" AS F").append(j).append("_1");
							} else {
								valBr.append(",NULL AS F").append(j).append("_0").append(",NULL AS F").append(j).append("_1");
							}
						} else {
							valBr = new StringBuilder(" SELECT ").append(rid);
							if (StringUtils.isNotBlank(subValName) ) {
								valBr.append(",'").append(subValName).append("'").append(" AS F").append(j).append("_0");
								valBr.append(",'").append(subValPath.replaceFirst(JsfUtil.getAbsolutePath(), "")).append("'").append(" AS F").append(j).append("_1");
							} else {
								valBr.append(",NULL AS F").append(j).append("_0").append(",NULL AS F").append(j).append("_1");
							}
							tempMap.put(i, valBr);
						}
					}else{
						
						String subVal = valueMap.get("DYNA_SUB_" + field.getFdEnname() + "_" + i);
						StringBuilder valBr = tempMap.get(i);
						if (null != valBr) {
							if (StringUtils.isNotBlank(subVal) ) {
								if (field.getFdDbtype().equalsIgnoreCase(DynaFieldType.TIMESTAMP.getTypeNo())) {
									valBr.append(", TO_DATE('").append(subVal);
									valBr.append("', 'YYYY-MM-DD HH24:MI:SS') ");
								} else if (field.getFdDbtype().equalsIgnoreCase(DynaFieldType.DATE.getTypeNo())) {
									valBr.append(", TO_DATE('").append(subVal);
									valBr.append("', 'YYYY-MM-DD') ");
								} else {
									valBr.append(",'").append(subVal).append("'");
								}
								valBr.append(" AS F").append(j);
							} else {
								valBr.append(",NULL AS F").append(j);
							}
						} else {
							valBr = new StringBuilder(" SELECT ").append(rid);
							if (StringUtils.isNotBlank(subVal) ) {
								if (field.getFdDbtype().equalsIgnoreCase(DynaFieldType.TIMESTAMP.getTypeNo())) {
									valBr.append(", TO_DATE('").append(subVal);
									valBr.append("', 'YYYY-MM-DD HH24:MI:SS') ");
								} else if (field.getFdDbtype().equalsIgnoreCase(DynaFieldType.DATE.getTypeNo())) {
									valBr.append(", TO_DATE('").append(subVal);
									valBr.append("', 'YYYY-MM-DD') ");
								} else {
									valBr.append(",'").append(subVal).append("'");
								}
								valBr.append(" AS F").append(j);
							} else {
								valBr.append(",NULL AS F").append(j);
							}
							tempMap.put(i, valBr);
						}
					}
				}
			}
			sb.append(") SELECT ").append(table.getEnName()).append("_SEQ.NEXTVAL, A.* FROM (");
			for (StringBuilder valBr : tempMap.values()) {
				sb.append(" UNION ALL ").append(valBr).append(" FROM DUAL ");
			}
			sb.append(" ) A ");

			String sql = sb.toString().replaceFirst("UNION ALL", "");
			System.err.println("【sql】：" + sql);
			super.em.createNativeQuery(sql).executeUpdate();
		}
	}

	public String deleteFormData(TdFormDef tdFormDef, Integer rid) {
		try {
			if (null != tdFormDef) {
				StringBuilder sb = null;
				TdFormTable table = tdFormDef.getTdFormTableByTableId();
				if (table.getFormProp().intValue() == 2) {
					// 主子表维护
					TdFormTable subTable = table.getChildList().get(0);
					sb = new StringBuilder(" DELETE FROM ");
					sb.append(subTable.getEnName()).append(" WHERE MAIN_ID = '").append(rid).append("' ");
					super.em.createNativeQuery(sb.toString()).executeUpdate();
				}

				sb = new StringBuilder(" DELETE FROM ");
				sb.append(table.getEnName()).append(" WHERE RID = '").append(rid).append("' ");
				super.em.createNativeQuery(sb.toString()).executeUpdate();
			}

		} catch (Exception e) {
			e.printStackTrace();
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return "已被其他数据引用，不能删除！";
		}
		return null;
	}

	public void saveTdFormType(TdFormType tdFormType) {
		if (null == tdFormType.getRid()) {
			this.save(tdFormType);
		} else {
			this.update(tdFormType);
		}
	}

	public String deleteTdFormType(Integer rid) {
		if (null != rid) {
			try {
				StringBuilder sql = new StringBuilder();
				sql.append("DELETE FROM TD_FORM_TYPE T WHERE T.RID = ?1");
				Query query = this.em.createNativeQuery(sql.toString());
				query.setParameter(1, rid);
				query.executeUpdate();
			} catch (Exception e) {
				e.printStackTrace();
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				return "表单类型已被引用，无法删除！";
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public String verifyTdFormType(TdFormType tdFormType) {
		if (null != tdFormType) {
			StringBuilder sql = new StringBuilder();
			sql.append("SELECT T.RID FROM TD_FORM_TYPE T WHERE T.TYPE_CODE =?1 ");
			if (null != tdFormType.getRid()) {
				sql.append(" AND T.RID != ?2");
			}
			Query query = this.em.createNativeQuery(sql.toString());
			query.setParameter(1, tdFormType.getTypeCode());
			if (null != tdFormType.getRid()) {
				query.setParameter(2, tdFormType.getRid());
			}
			List resultList = query.getResultList();
			if (null != resultList && resultList.size() > 0) {
				return "类型编码已存在！";
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TdFormType> findTdFormTypeList() {
		StringBuilder sql = new StringBuilder();
		sql.append(" from TdFormType t order by t.typeCode");
		List<TdFormType> resultList = this.em.createQuery(sql.toString()).getResultList();
		return resultList;
	}

	public void saveTdFormDef(TdFormDef tdFormDef) {
		if (null == tdFormDef.getRid()) {
			// 保存表单信息
			if (StringUtils.isBlank(tdFormDef.getFormCode())) {
				tdFormDef.setFormCode(this.commService.getAutoCode("SYSTEM_LCBD_BDBH", ""));
			}
			this.save(tdFormDef);
		} else {
			this.update(tdFormDef);
		}
	}
	
	//保存统计信息
	public void saveTdFormStatisticsDef(TdFormStatisticsDef tdFormStatisticsDef) {
		if (null == tdFormStatisticsDef.getRid()) {
			// 保存表单信息
			if (StringUtils.isBlank(tdFormStatisticsDef.getFormCode())) {
				tdFormStatisticsDef.setFormCode(this.commService.getAutoCode("SYSTEM_LCBD_BDBH", ""));
			}
			this.save(tdFormStatisticsDef);
		} else {
			this.update(tdFormStatisticsDef);
		}
	}
	
	public TdFormTable saveTdFormTable(TdFormTable mTdFormTable, TdFormTable sTdFormTable) {
		if (mTdFormTable.getFormProp().intValue() == 1) {// 单表
			// 保存主表信息，同时返回主表Id
			mTdFormTable.setEnName(new StringBuffer(DYNC_TABLE_PREFIX).append(mTdFormTable.getEnName()).toString());
		}else if(mTdFormTable.getFormProp().intValue() == 2)	{// 主子表
			// 将子表集合关联到主表信息中
			List<TdFormTable> childList = new ArrayList<TdFormTable>();
			sTdFormTable.setEnName(new StringBuffer(DYNC_TABLE_PREFIX).append(sTdFormTable.getEnName()).toString());
			sTdFormTable.setFkField("MAIN_ID");
			sTdFormTable.setTdFormTableByMainTabId(mTdFormTable);
			childList.add(sTdFormTable);
			mTdFormTable.setChildList(childList);
			mTdFormTable.setEnName(new StringBuffer(DYNC_TABLE_PREFIX).append(mTdFormTable.getEnName()).toString());
		}
		if (null == mTdFormTable.getRid()) {
			if (mTdFormTable.getFormProp().intValue() == 1) {// 单表
				mTdFormTable = (TdFormTable) this.saveObj(mTdFormTable);
			} else if (mTdFormTable.getFormProp().intValue() == 2) {// 主子表
				mTdFormTable = (TdFormTable) this.saveObj(mTdFormTable);
			}
		} else {
			if (mTdFormTable.getFormProp().intValue() == 1) {// 单表
				mTdFormTable = (TdFormTable) this.updateObj(mTdFormTable);
			} else if (mTdFormTable.getFormProp().intValue() == 2) {// 主子表
				mTdFormTable = (TdFormTable) this.updateObj(mTdFormTable);
			}
		}
		TdFormTable findTdFormTable = this.findTdFormTable(mTdFormTable.getRid());
		return findTdFormTable;
	}
	
	public void operateTableState(Integer rid,Integer state)	{
		if( null != rid)	{
			StringBuilder sql = new StringBuilder();
			sql.append("UPDATE TD_FORM_TABLE T SET T.STATE = ?1 WHERE T.RID = ?2");
			Query query = this.em.createNativeQuery(sql.toString());
			query.setParameter(1, state);
			query.setParameter(2, rid);
			query.executeUpdate();
		}
	}

	public String commitTdFormTable(TdFormTable mTdFormTable, TdFormTable sTdFormTable) {
		// 表单模式 1：单表 2：主子表
		List<String> sqlList = new ArrayList<String>();
		//主表名称
		String mName = new StringBuffer(DYNC_TABLE_PREFIX).append(mTdFormTable.getEnName()).toString();
		sqlList.add(this.genCreateTableSql(mName));
		sqlList.add(this.genSeqSql(mName));
		// 生成字段
		List<TdFormField> fieldList = mTdFormTable.getFieldList();
		this.addSqlList(fieldList, mName, sqlList);
		if (null != sTdFormTable) {
			//子表名称
			String sName = new StringBuffer(DYNC_TABLE_PREFIX).append(sTdFormTable.getEnName()).toString();
			// 增加子表
			sqlList.add(this.genCreateSubTableSql(sName, mName));
			sqlList.add(this.genSeqSql(sName));

			List<TdFormField> fieldList2 = sTdFormTable.getFieldList();
			this.addSqlList(fieldList2, sName, sqlList);
		}

		StringBuilder sb = new StringBuilder();
		sb.append("UPDATE TD_FORM_TABLE T SET T.STATE = 1 WHERE T.RID = ").append(mTdFormTable.getRid());
		sqlList.add(sb.toString());

		if (null != sqlList && sqlList.size() > 0) {
			try {
				for (String sql : sqlList) {
					this.executeUpdate(sql);
				}
			} catch (Exception e) {
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				e.printStackTrace();
			}
		}
		return null;
	}
	
	public void commitTdFormDef(Integer mainId,Integer state) {
		if( null != mainId && null != state)	{
			StringBuilder sql = new StringBuilder();
			sql.append("UPDATE TD_FORM_DEF T SET T.STATE = ?1 WHERE T.RID = ?2 ");
			Query query = this.em.createNativeQuery(sql.toString());
			query.setParameter(1, state);
			query.setParameter(2, mainId);
			query.executeUpdate();
		}
	}
	
	public void commitTdFormStatisticsDef(Integer mainId,Integer state) {
		if( null != mainId && null != state)	{
			StringBuilder sql = new StringBuilder();
			sql.append("UPDATE TD_FORM_STATISTICS_DEF T SET T.STATE = ?1 WHERE T.RID = ?2 ");
			Query query = this.em.createNativeQuery(sql.toString());
			query.setParameter(1, state);
			query.setParameter(2, mainId);
			query.executeUpdate();
		}
	}
	
	/**
	 * 生成修改字段的SQL
	 * 
	 * @param fieldList
	 * @param tdFormTable
	 * @param sqlList
	 */
	@Transactional(readOnly = true)
	private void addSqlList(List<TdFormField> fieldList, String mName, List<String> sqlList) {
		if (null != sqlList && null != fieldList && StringUtils.isNotBlank(mName)) {
			for (TdFormField tdFormField : fieldList) {
				String dataSrc = tdFormField.getDataSrc();
				if(DynaFieldShowType.FJ.getTypeNo().equals(dataSrc))	{
					sqlList.add(this.genAddColSql(mName, tdFormField.getFdEnname() + FJ_PATH_SUFFIX, tdFormField));
					sqlList.add(this.genAddColSql(mName, tdFormField.getFdEnname() + FJ_NAME_SUFFIX, tdFormField));
				}else{
					//如果是附件类型，需要新增两个字段
					sqlList.add(this.genAddColSql(mName, tdFormField.getFdEnname() ,tdFormField));
				}
				
				// 数据类型
				String fdDbtype = tdFormField.getFdDbtype();
				DynaFieldType dynaFieldType = (DynaFieldType) EnumUtils.findEnum(DynaFieldType.class, fdDbtype);
				if (null != dynaFieldType
						&& (DynaFieldType.INTEGER.getTypeNo().equals(dynaFieldType.getTypeNo())
								|| DynaFieldType.NUMBER.getTypeNo().equals(dynaFieldType.getTypeNo()) || DynaFieldType.NVARCHAR2
								.getTypeNo().equals(dynaFieldType.getTypeNo()))
						|| DynaFieldType.TIMESTAMP.getTypeNo().equals(dynaFieldType.getTypeNo())) {
					sqlList.addAll(this.genAlterColSqlList(mName, tdFormField));
				}
			}
		}
	}

	/**
	 * 生成增加字段语句
	 * 
	 * @param tableName
	 * @param tdFormField
	 * @return
	 */
	@Transactional(readOnly = true)
	private String genSeqSql(String tableName) {
		StringBuilder sb = new StringBuilder();
		sb.append("DECLARE");
		sb.append("  V1 NUMBER;");
		sb.append("BEGIN");
		sb.append("  SELECT COUNT(1)");
		sb.append("    INTO V1");
		sb.append("    FROM USER_SEQUENCES");
		sb.append("   WHERE SEQUENCE_NAME = '").append(tableName).append("_SEQ';");
		sb.append("  IF V1 = 0 THEN");
		sb.append("    EXECUTE IMMEDIATE 'CREATE SEQUENCE ").append(tableName).append("_SEQ");
		sb.append("          MINVALUE 0");
		sb.append("          MAXVALUE 999999999999999999");
		sb.append("          START WITH 3000");
		sb.append("          INCREMENT BY 1");
		sb.append("          CACHE 20';");
		sb.append("  END IF;");
		sb.append("END;");
		return sb.toString();
	}

	/**
	 * 生成增加字段语句
	 * 
	 * @param tableName
	 * @param tdFormField
	 * @return
	 */
	@Transactional(readOnly = true)
	private List<String> genAddColSqlList(String tableName, TdFormField tdFormField) {
		List<String> sqlList = new ArrayList<>();
		String dataSrc = tdFormField.getDataSrc();
		if(DynaFieldShowType.FJ.getTypeNo().equals(dataSrc))	{
			sqlList.add(this.genAddColSql(tableName, tdFormField.getFdEnname() + FJ_NAME_SUFFIX, tdFormField));
			sqlList.add(this.genAddColSql(tableName, tdFormField.getFdEnname() + FJ_PATH_SUFFIX, tdFormField));
		}else{
			sqlList.add(this.genAddColSql(tableName, tdFormField.getFdEnname(),tdFormField));
		}
		return sqlList;
	}

	/**
	 * 生成增加字段语句
	 * 
	 * @param tableName
	 * @param tdFormField
	 * @return
	 */
	@Transactional(readOnly = true)
	private String genAddColSql(String tableName, String fieldEnName ,  TdFormField tdFormField) {
		StringBuilder sb = new StringBuilder();
		sb.append("DECLARE");
		sb.append("  NUM INT;");
		sb.append("BEGIN");
		sb.append("  SELECT COUNT(1)");
		sb.append("    INTO NUM");
		sb.append("    FROM COLS");
		sb.append("   WHERE TABLE_NAME = UPPER('").append(tableName).append("')");
		sb.append("     AND COLUMN_NAME = UPPER('").append(fieldEnName).append("');");
		sb.append("  IF NUM = 0 THEN");
		sb.append("    EXECUTE IMMEDIATE 'ALTER TABLE ").append(tableName).append(" ADD ");
		sb.append(fieldEnName).append(" ").append(this.genFieldStr(tdFormField)).append("';");
		sb.append("  END IF;");
		sb.append("END;");
		System.err.println(sb.toString());
		return sb.toString();
	}
	
	/**
	 * 生成增加字段语句
	 * 
	 * @param tableName
	 * @param tdFormField
	 * @return
	 */
	@Transactional(readOnly = true)
	private List<String> genAlterColSqlList(String tableName, TdFormField tdFormField) {
		List<String> sqlList = new ArrayList<>();
		String dataSrc = tdFormField.getDataSrc();
		if(DynaFieldShowType.FJ.getTypeNo().equals(dataSrc))	{
			sqlList.add(this.genAlterColSql(tableName, tdFormField.getFdEnname() + FJ_NAME_SUFFIX,tdFormField));
			sqlList.add(this.genAlterColSql(tableName, tdFormField.getFdEnname()+ FJ_PATH_SUFFIX,tdFormField));
		}else{
			sqlList.add(this.genAlterColSql(tableName, tdFormField.getFdEnname(),tdFormField));
		}
		return sqlList;
	}
	
	/**
	 * 生成增加字段语句
	 * 
	 * @param tableName
	 * @param tdFormField
	 * @return
	 */
	@Transactional(readOnly = true)
	private String genAlterColSql(String tableName,String fieldEnName , TdFormField tdFormField) {
		StringBuilder sb = new StringBuilder();
		sb.append("DECLARE NUM NUMBER; ");
		sb.append(" BEGIN ");
		sb.append(" SELECT COUNT(1) INTO NUM FROM COLS WHERE  TABLE_NAME = UPPER('").append(tableName).append("') ");
		sb.append("       AND  COLUMN_NAME=UPPER('").append(fieldEnName).append("');");
		sb.append(" IF NUM=1 ");
		sb.append(" THEN ");
		sb.append(" EXECUTE IMMEDIATE ");
		sb.append(" 'ALTER TABLE ").append(tableName).append(" MODIFY ").append(fieldEnName).append(" ")
				.append(this.genFieldStr(tdFormField)).append("';");
		sb.append(" END IF;");
		sb.append(" END;");
		System.err.println(sb);
		return sb.toString();
	}

	/**
	 * 生成建表语句
	 * 
	 * @param tdFormTable
	 * @return
	 */
	@Transactional(readOnly = true)
	private String genCreateTableSql(String tableName) {
		StringBuilder sb = new StringBuilder();
		sb.append("DECLARE NUM NUMBER; ");
		sb.append(" BEGIN SELECT COUNT(1) INTO NUM FROM USER_TABLES");
		sb.append(" WHERE TABLE_NAME='").append(tableName).append("'; ");
		sb.append(" IF NUM=0 THEN EXECUTE IMMEDIATE ");
		sb.append(" '");
		sb.append(" CREATE TABLE ").append(tableName).append(" ");
		sb.append("(");
		sb.append("   RID INTEGER NOT NULL,");
		sb.append("   STATE NUMBER(1) DEFAULT(0),");
		sb.append("   CREATE_MANID INTEGER NOT NULL,");
		sb.append("   CREATE_DATE TIMESTAMP NOT NULL,");
		sb.append("   MODIFY_MANID INTEGER,");
		sb.append("   MODIFY_DATE TIMESTAMP,");
		sb.append(" CONSTRAINT PK_").append(tableName).append(" PRIMARY KEY (RID) ");
		sb.append(")");
		sb.append("'; ");
		sb.append("END IF; ");
		sb.append("END;");
		return sb.toString();
	}

	/**
	 * 生成子表语句
	 * 
	 * @param tdFormTable
	 * @return
	 */
	@Transactional(readOnly = true)
	private String genCreateSubTableSql(String sTableName, String mTableName) {
		StringBuilder sb = new StringBuilder();
		sb.append("DECLARE NUM NUMBER; ");
		sb.append(" BEGIN SELECT COUNT(1) INTO NUM FROM USER_TABLES");
		sb.append(" WHERE TABLE_NAME='").append(sTableName).append("'; ");
		sb.append(" IF NUM=0 THEN EXECUTE IMMEDIATE ");
		sb.append(" '");
		sb.append(" CREATE TABLE ").append(sTableName).append(" ");
		sb.append("(");
		sb.append("   RID INTEGER NOT NULL,");
		sb.append("   MAIN_ID INTEGER ,");
		sb.append(" CONSTRAINT PK_").append(sTableName).append(" PRIMARY KEY (RID), ");
		sb.append(" CONSTRAINT FK_").append(sTableName).append("1 FOREIGN KEY (MAIN_ID) REFERENCES ").append(mTableName)
				.append(" (RID) ");
		sb.append(")");
		sb.append("'; ");
		sb.append("END IF; ");
		sb.append("END;");
		return sb.toString();
	}

	/**
	 * 根据字段类型生成字段
	 * 
	 * @param tdFormField
	 * @return
	 */
	@Transactional(readOnly = true)
	private String genFieldStr(TdFormField tdFormField) {
		StringBuilder str = new StringBuilder();
		if (tdFormField.getFdDbtype().equals(DynaFieldType.NVARCHAR2.name())) {
			Integer lenChar = tdFormField.getLenChar();
			str.append(tdFormField.getFdDbtype()).append("(").append(lenChar).append(")");
		} else if (tdFormField.getFdDbtype().equals(DynaFieldType.NUMBER.name())) {
			// 整数长度
			Integer lenInt = tdFormField.getLenInt();
			// 小数长度
			Integer lenDemi = tdFormField.getLenDemi();
			str.append(tdFormField.getFdDbtype()).append("(").append(lenInt+lenDemi).append(lenDemi == 0 ? "" : "," + lenDemi)
					.append(")");
		} else if (tdFormField.getFdDbtype().equals(DynaFieldType.DATE.name())
				|| tdFormField.getFdDbtype().equals(DynaFieldType.CLOB.name())
				|| tdFormField.getFdDbtype().equals(DynaFieldType.INTEGER.name())
				|| tdFormField.getFdDbtype().equals(DynaFieldType.TIMESTAMP.name())) {
			str.append(tdFormField.getFdDbtype());
		}
		return str.toString();
	}

	@Transactional(readOnly = true)
	public TdFormTable findTdFormTable(Integer rid) {
		// 表单信息
		TdFormTable find = this.em.find(TdFormTable.class, rid);
		if (null != find) {
//			find.setEnName(find.getEnName().replaceFirst(DYNC_TABLE_PREFIX, ""));
			List<TdFormField> fieldList = find.getFieldList();
			if (null != fieldList && fieldList.size() > 0) {
				for (TdFormField tdFormField : fieldList) {
					if (StringUtils.isNotBlank(tdFormField.getCodeTypeNo())) {
						tdFormField.setDataFrom("1");
					} else if (StringUtils.isNotBlank(tdFormField.getQuerySql())) {
						tdFormField.setDataFrom("2");
					}

					if (tdFormField.getIsSearch() != null && tdFormField.getIsSearch().intValue() == 1) {
						tdFormField.setIfSearch(true);
					}
					
					tdFormField.setLenCharLast(tdFormField.getLenChar());
					tdFormField.setLenDemiLast(tdFormField.getLenDemi());
					tdFormField.setLenIntLast(tdFormField.getLenInt());
				}
			}
			List<TdFormTable> childList = find.getChildList();
			if (null != childList) {
				for (TdFormTable table : childList) {
//					table.setEnName(table.getEnName().replaceFirst(DYNC_TABLE_PREFIX, ""));
					List<TdFormField> fieldList2 = table.getFieldList();
					if (null != fieldList2 && fieldList2.size() > 0) {
						for (TdFormField tdFormField : fieldList2) {
							if (StringUtils.isNotBlank(tdFormField.getCodeTypeNo())) {
								tdFormField.setDataFrom("1");
							} else if (StringUtils.isNotBlank(tdFormField.getQuerySql())) {
								tdFormField.setDataFrom("2");
							}
							
							if (tdFormField.getIsSearch() != null && tdFormField.getIsSearch().intValue() == 1) {
								tdFormField.setIfSearch(true);
							}
							
							tdFormField.setLenCharLast(tdFormField.getLenChar());
							tdFormField.setLenDemiLast(tdFormField.getLenDemi());
							tdFormField.setLenIntLast(tdFormField.getLenInt());
						}
					}
				}
			}
		}
		return find;
	}
	@Transactional(readOnly = true)
	public TdFormDef findTdFormDef(Integer rid) {
		// 表单信息
		TdFormDef find = this.em.find(TdFormDef.class, rid);
		TdFormTable tdFormTable = find.getTdFormTableByTableId();
		tdFormTable.setEnName(tdFormTable.getEnName().replaceFirst(DYNC_TABLE_PREFIX, ""));
		if (null != tdFormTable) {
			List<TdFormField> fieldList = tdFormTable.getFieldList();
			if (null != fieldList && fieldList.size() > 0) {
				for (TdFormField tdFormField : fieldList) {
					if (StringUtils.isNotBlank(tdFormField.getCodeTypeNo())) {
						tdFormField.setDataFrom("1");
					} else if (StringUtils.isNotBlank(tdFormField.getQuerySql())) {
						tdFormField.setDataFrom("2");
					}
					tdFormField.setLenCharLast(tdFormField.getLenChar());
					tdFormField.setLenDemiLast(tdFormField.getLenDemi());
					tdFormField.setLenIntLast(tdFormField.getLenInt());
				}
			}
			List<TdFormTable> childList = tdFormTable.getChildList();
			if (null != childList) {
				for (TdFormTable table : childList) {
					table.setEnName(table.getEnName().replaceFirst(DYNC_TABLE_PREFIX, ""));
					List<TdFormField> fieldList2 = table.getFieldList();
					if (null != fieldList2 && fieldList2.size() > 0) {
						for (TdFormField tdFormField : fieldList) {
							if (StringUtils.isNotBlank(tdFormField.getCodeTypeNo())) {
								tdFormField.setDataFrom("1");
							} else if (StringUtils.isNotBlank(tdFormField.getQuerySql())) {
								tdFormField.setDataFrom("2");
							}
							tdFormField.setLenCharLast(tdFormField.getLenChar());
							tdFormField.setLenDemiLast(tdFormField.getLenDemi());
							tdFormField.setLenIntLast(tdFormField.getLenInt());
						}
					}
				}
			}
		}
		return find;
	}
	
	@Transactional(readOnly = true)
	public TdFormStatisticsDef findTdFormStatisticsDef(Integer rid) {
		// 表单信息
		TdFormStatisticsDef find = this.em.find(TdFormStatisticsDef.class, rid);
		TdFormTable tdFormTable = find.getTdFormTableByTableId();
		tdFormTable.setEnName(tdFormTable.getEnName().replaceFirst(DYNC_TABLE_PREFIX, ""));
		if (null != tdFormTable) {
			List<TdFormField> fieldList = tdFormTable.getFieldList();
			if (null != fieldList && fieldList.size() > 0) {
				for (TdFormField tdFormField : fieldList) {
					if (StringUtils.isNotBlank(tdFormField.getCodeTypeNo())) {
						tdFormField.setDataFrom("1");
					} else if (StringUtils.isNotBlank(tdFormField.getQuerySql())) {
						tdFormField.setDataFrom("2");
					}
					tdFormField.setLenCharLast(tdFormField.getLenChar());
					tdFormField.setLenDemiLast(tdFormField.getLenDemi());
					tdFormField.setLenIntLast(tdFormField.getLenInt());
				}
			}
			List<TdFormTable> childList = tdFormTable.getChildList();
			if (null != childList) {
				for (TdFormTable table : childList) {
					table.setEnName(table.getEnName().replaceFirst(DYNC_TABLE_PREFIX, ""));
					List<TdFormField> fieldList2 = table.getFieldList();
					if (null != fieldList2 && fieldList2.size() > 0) {
						for (TdFormField tdFormField : fieldList) {
							if (StringUtils.isNotBlank(tdFormField.getCodeTypeNo())) {
								tdFormField.setDataFrom("1");
							} else if (StringUtils.isNotBlank(tdFormField.getQuerySql())) {
								tdFormField.setDataFrom("2");
							}
							tdFormField.setLenCharLast(tdFormField.getLenChar());
							tdFormField.setLenDemiLast(tdFormField.getLenDemi());
							tdFormField.setLenIntLast(tdFormField.getLenInt());
						}
					}
				}
			}
		}
		return find;
	}

	private void executeUpdate(String sql) {
		Connection conn = null;
		try {
			conn = dataSource.getConnection();
			conn.createStatement().executeUpdate(sql);
		} catch (Exception e) {
			e.printStackTrace();
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		} finally {
			if (null != conn) {
				try {
					conn.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
	}

	@Transactional(readOnly = true)
	public List<TdFormType> findFormTypeList() {
		StringBuilder sql = new StringBuilder();
		sql.append("from TdFormType t order by t.typeCode");
		List<TdFormType> resultList = this.em.createQuery(sql.toString()).getResultList();
		return resultList;
	}

	@Transactional(readOnly = true)
	public List<TsRpt> findTsRpt(String systemTypes) {
		StringBuilder sql = new StringBuilder();
		sql.append(" from TsRpt t ");
		if (StringUtils.isNotBlank(systemTypes)) {
			sql.append(" where t.systemType in (").append(systemTypes).append(")");
		}
		sql.append(" order by t.systemType,t.rptCod");
		List<TsRpt> resultList = this.em.createQuery(sql.toString()).getResultList();
		return resultList;
	}

	public void delTdFormDef(Integer defId) {
		if (null != defId) {
			StringBuilder sql = new StringBuilder();
			sql.append("DELETE FROM TD_FORM_DEF T WHERE T.RID = ?1");
			Query query = this.em.createNativeQuery(sql.toString());
			query.setParameter(1, defId);
			query.executeUpdate();
		}
	}

	@Transactional(readOnly = true)
	public TsRpt findTsRptByCode(String code) {
		if (StringUtils.isNotBlank(code)) {
			StringBuilder sql = new StringBuilder();
			sql.append("from TsRpt t where t.rptCod = ?1");
			Query query = this.em.createQuery(sql.toString());
			query.setParameter(1, code);
			List<TsRpt> resultList = query.getResultList();
			if (null != resultList && resultList.size() > 0) {
				TsRpt tsRpt = resultList.get(0);
				return tsRpt;
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public String verifyDynaFormTable(String[] tableNames,String[] rids) {
		if (null != tableNames && tableNames.length > 0) {
			for(int i = 0 ; i < tableNames.length; i++)	{
				String tableName = tableNames[i];
				String rid = rids[i];
				StringBuilder sql = new StringBuilder();
				sql.append("SELECT T.RID FROM TD_FORM_TABLE T WHERE T.EN_NAME = ?1");
				if( StringUtils.isNotBlank(rid)){
					sql.append(" AND T.RID != ?2");
				}
				Query query = this.em.createNativeQuery(sql.toString());
				query.setParameter(1, tableName);
				if( StringUtils.isNotBlank(rid) )	{
					query.setParameter( 2, rid );
				}
				List resultList = query.getResultList();
				if (null != resultList && resultList.size() > 0) {
					return new StringBuilder("表名：").append(tableName).append(" 已存在，请重新命名！").toString();
				}
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public String getDesc() {
		return "动态表单管理服务";
	}

	@Transactional(readOnly = true)
	public String getVersion() {
		return "1.0.0";
	}

	//根据表单编号获取动态表单数据
	@Transactional(readOnly = true)
	public TdFormDef findTdFormDefByFormCode(String formCode) {
		// 表单信息
		String hql = "from TdFormDef t  where t.formCode="+formCode+" and t.state=1";
		List results =  this.em.createQuery(hql).getResultList();
		if(results==null || results.size()==0)
			return null;
		TdFormDef formDef = (TdFormDef) results.get(0);
		TdFormTable formTable = formDef.getTdFormTableByTableId();
		TdFormField field = formTable.getFieldList()==null?null:formTable.getFieldList().get(0);
		if(2==formTable.getFormProp()){
			//子表字段
			TdFormTable subTable = formTable.getChildList()==null?null:formTable.getChildList().get(0);
			TdFormField subfield = subTable.getFieldList()==null?null:subTable.getFieldList().get(0);
		}
		return formDef;
	}
	
	//根据表单编号获取动态表单统计数据
	@Transactional(readOnly = true)
	public TdFormStatisticsDef findTdFormStatisticsDefByFormCode(String formCode) {
		// 表单信息
		String hql = "from TdFormStatisticsDef t  where t.formCode="+formCode+" and t.state=1";
		List results =  this.em.createQuery(hql).getResultList();
		if(results==null || results.size()==0)
			return null;
		TdFormStatisticsDef formDef = (TdFormStatisticsDef) results.get(0);
		TdFormTable formTable = formDef.getTdFormTableByTableId();
		TdFormField field = formTable.getFieldList()==null?null:formTable.getFieldList().get(0);
		if(2==formTable.getFormProp()){
			//子表字段
			TdFormTable subTable = formTable.getChildList()==null?null:formTable.getChildList().get(0);
			TdFormField subfield = subTable.getFieldList()==null?null:subTable.getFieldList().get(0);
		}
		return formDef;
	}
	/**
	 * 根据科室ID查找所有的人员信息
	 */
	public List<TsUserInfo> getUsersByOfficId(Integer officeId) {
		List<TsUserInfo> userList = new ArrayList<>();
		StringBuilder sb = new StringBuilder();
		sb.append(" select b.rid,b.USERNAME from TS_OFFICE t left join TB_SYS_EMP a on a.DEPT_ID = t.rid left join TS_USER_INFO b on b.EMP_ID = a.rid where t.rid = ").append(officeId);
		sb.append("    and a.ONDUTY = 1 ");
		sb.append("    and b.IF_REVEAL = 1 ");
		sb.append("    order by a.IS_LEADER,a.NUM");
		List<Object[]> list = this.em.createNativeQuery(sb.toString()).getResultList();
		if (list!=null && list.size()>0) {
			for (Object[] objects : list) {
				TsUserInfo info = new TsUserInfo();
				info.setRid(Integer.valueOf(objects[0].toString()));
				info.setUsername(objects[1].toString());
				userList.add(info);
			}
		}
		return userList;
	}
	@Transactional(readOnly=true)
	public List<TsOffice> getOfficeByUnitId(Integer unitId,TsOffice tsOffice) {
		List<TsOffice> offList = new ArrayList<>();
		StringBuilder sb = new StringBuilder();
		sb.append(" select t.rid,t.OFFICENAME from TS_OFFICE t where t.UNIT_RID = ").append(unitId);
		sb.append("    and t.IF_REVEAL = 1 ");
		if (tsOffice!=null&&tsOffice.getRid()!=null) {
			sb.append("    and t.rid not in ( ").append(tsOffice.getRid()).append(")");
			TsOffice info = new TsOffice();
			info.setRid(tsOffice.getRid());
			info.setOfficename(tsOffice.getOfficename());
			offList.add(info);
		}
		sb.append("    order by t.OFFICECODE");
		List<Object[]> list = this.em.createNativeQuery(sb.toString()).getResultList();
		if (list!=null && list.size()>0) {
			for (Object[] objects : list) {
				TsOffice info = new TsOffice();
				info.setRid(Integer.valueOf(objects[0].toString()));
				info.setOfficename(objects[1].toString());
				offList.add(info);
			}
		}
		return offList;
	}

}
