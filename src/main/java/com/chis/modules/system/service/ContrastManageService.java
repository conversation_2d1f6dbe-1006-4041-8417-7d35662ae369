package com.chis.modules.system.service;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsContraMain;
import com.chis.modules.system.entity.TsContraSub;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 */

@Service
@Transactional(readOnly = true)
public class ContrastManageService extends AbstractTemplate{
    
    /**
    * <p>Description：查询左侧对照类型表 </p>
    * <p>Author： yzz 2025-01-07 </p>
    */
    public List<Object[]> findContraData(String contraCodeSearch) {
        Map<String, Object> paramMap=new HashMap<>();
        StringBuilder sql=new StringBuilder();
        sql.append(" select T.RID, T.CONTRA_CODE, T.DESCR, T.RMK " );
        sql.append(" from TS_CONTRA_MAIN T ");
        sql.append(" where 1=1 ");
        if(StringUtils.isNotBlank(contraCodeSearch)){
            sql.append(" and ( T.CONTRA_CODE like :contraCode escape '\\\'");
            sql.append(" OR T.DESCR like :descr escape '\\\'");
            sql.append(")");
            paramMap.put("contraCode","%"+StringUtils.convertBFH(contraCodeSearch.trim())+"%");
            paramMap.put("descr","%"+StringUtils.convertBFH(contraCodeSearch.trim())+"%");
        }
        sql.append(" order by to_number(T.CONTRA_CODE) ");
        return this.findDataBySqlNoPage(sql.toString(),paramMap);
    }
    
    /**
    * <p>Description：保存左侧 </p>
    * <p>Author： yzz 2025-01-08 </p>
    */
    public void saveContraMain(TsContraMain entity) {
        if (entity.getRid() == null) {
            saveObj(entity);
        } else {
            updateObj(entity);
        }
    }
    
    /**
    * <p>Description：删除左侧 </p>
    * <p>Author： yzz 2025-01-08 </p>
    */
    public void delContraMain(Integer rid) {
        //删除子表
        String sql = "delete from TS_CONTRA_SUB where MAIN_ID=" + rid;
        this.executeSql(sql,null);
        //删除主表
        sql = "delete from TS_CONTRA_MAIN where RID=" + rid;
        this.executeSql(sql,null);
    }
    
    /**
    * <p>Description：查询右侧 </p>
    * <p>Author： yzz 2025-01-08 </p>
    */
    public List<Object[]> findContraSubData(int rid, String busiTypeSearch,String leftCodeSearch,String rightCodeSearch) {
        Map<String, Object> paramMap=new HashMap<>();
        StringBuilder sql=new StringBuilder();
        sql.append(" SELECT T.RID,T.BUSI_TYPE,T.BUS_DESC,T.LEFT_CODE,T.LEFT_DESC,T.RIGHT_CODE,T.DESCR,T.DSF_TAG,T.DSF_SPECIAL_DESC,T.MAIN_ID ");
        sql.append(" FROM TS_CONTRA_SUB T ");
        sql.append(" where T.MAIN_ID= ").append(rid);
        if(StringUtils.isNotBlank(busiTypeSearch)){
            sql.append(" and ( T.BUSI_TYPE like :busiTypeSearch escape '\\\'");
            sql.append(" OR T.BUS_DESC like :busDesc escape '\\\'");
            sql.append(")");
            paramMap.put("busiTypeSearch","%"+StringUtils.convertBFH(busiTypeSearch.trim())+"%");
            paramMap.put("busDesc","%"+StringUtils.convertBFH(busiTypeSearch.trim())+"%");
        }
        if(StringUtils.isNotBlank(leftCodeSearch)){
            sql.append(" and T.LEFT_CODE like :leftCodeSearch escape '\\\'");
            paramMap.put("leftCodeSearch","%"+StringUtils.convertBFH(leftCodeSearch.trim())+"%");
        }
        if(StringUtils.isNotBlank(rightCodeSearch)){
            sql.append(" and T.RIGHT_CODE like :rightCodeSearch escape '\\\'");
            paramMap.put("rightCodeSearch","%"+StringUtils.convertBFH(rightCodeSearch.trim())+"%");
        }
        sql.append(" order by T.BUSI_TYPE,T.LEFT_CODE  ");
        return this.findDataBySqlNoPage(sql.toString(),paramMap);
    }
    
    /**
    * <p>Description：保存右侧子表 </p>
    * <p>Author： yzz 2025-01-08 </p>
    */
    public void saveContraSubMain(TsContraSub entity) {
        if (entity.getRid() == null) {
            saveObj(entity);
        } else {
            updateObj(entity);
        }
    }
    
    /**
    * <p>Description：删除右侧子表 </p>
    * <p>Author： yzz 2025-01-08 </p>
    */
    public void delContraSub(Integer subRid) {
        //删除子表
        String sql = "delete from TS_CONTRA_SUB where rid=" + subRid;
        this.executeSql(sql,null);
    }
    
    /**
    * <p>Description：查询需要删除的子表类型 </p>
    * <p>Author： yzz 2025-01-09 </p>
    */
    public List<String> findBusTypeList(Integer mainRids) {
        List<String> listItem = new java.util.ArrayList<>();
        String sql = "select distinct T.BUSI_TYPE from TS_CONTRA_SUB T where T.MAIN_ID=" + mainRids + " order by T.BUSI_TYPE";
        List<Object> list = this.findDataBySqlNoPage(sql, null);
        if (CollectionUtils.isEmpty(list)) {
            return listItem;
        }
        for (Object objects : list) {
            listItem.add(objects.toString());
        }
        return listItem;
    }
    
    /**
    * <p>Description：通过子表类型删除所有子表记录 </p>
    * <p>Author： yzz 2025-01-09 </p>
    */
    public void delAllSubByType(String delType,Integer mainRid) {
        String sql = "delete from TS_CONTRA_SUB where BUSI_TYPE='" + delType + "' and MAIN_ID="+mainRid;
        this.executeSql(sql,null);
    }
    
    /**
    * <p>Description：根据编码查对照 </p>
    * <p>Author： yzz 2025-01-15 </p>
    */
    public int findContraDataByCode(Integer rid,String contraCode) {
        StringBuilder sql=new StringBuilder();
        sql.append(" select count(1) " );
        sql.append(" from TS_CONTRA_MAIN T ");
        sql.append(" where T.CONTRA_CODE= ").append(contraCode);
        if(rid!=null){
            sql.append(" and T.rid!= ").append(rid);
        }
        return this.findCountBySql(sql.toString(),null);
    }

    /**
    * <p>Description：查询相同类型下 是否存在leftCode，rightCode相同的记录</p>
    * <p>Author： yzz 2025-01-15 </p>
    */
    public int findContraSubByCode(Integer rid,Integer busiType, String leftCode, String rightCode,Integer mainRid) {
        Map<String, Object> paramMap=new HashMap<>();
        StringBuilder sql=new StringBuilder();
        sql.append(" SELECT count(1) ");
        sql.append(" FROM TS_CONTRA_SUB T ");
        sql.append(" where 1=1 and T.MAIN_ID=").append(mainRid);
        if(rid!=null){
            sql.append(" and T.rid !=").append(rid);
        }
        if(busiType!=null){
            sql.append(" and T.BUSI_TYPE =:busiType ");
            paramMap.put("busiType",busiType);
        }
        if(StringUtils.isNotBlank(leftCode)){
            sql.append(" and T.LEFT_CODE =:leftCode ");
            paramMap.put("leftCode",leftCode);
        }
        if(StringUtils.isNotBlank(rightCode)){
            sql.append(" and T.RIGHT_CODE =:rightCode ");
            paramMap.put("rightCode",rightCode);
        }
        return this.findCountBySql(sql.toString(),paramMap);
    }
    /**
     *  <p>方法描述：对照子表存储</p>
     * @MethodAuthor hsj 2025-01-17 14:58
     */

    @Transactional(readOnly = false)
    public void saveTsContraSub(List<TsContraSub> resultProList) {
        if(CollectionUtils.isEmpty(resultProList)){
            return;
        }
        this.saveBatchObjs(resultProList);
    }
}
