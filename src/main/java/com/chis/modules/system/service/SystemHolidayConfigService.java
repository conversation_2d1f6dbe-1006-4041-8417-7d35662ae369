package com.chis.modules.system.service;

import com.chis.common.utils.DateUtils;
import com.chis.modules.system.entity.TsSysHoliday;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly = false, rollbackFor = Throwable.class)
public class SystemHolidayConfigService extends AbstractTemplate  {
    /**
     * @Description:保存数据
     *
     * @MethodAuthor gjy,2021年12月3日
     */
    public void saveSysHoliday(TsSysHoliday tsSysHoliday){
        upsertEntity(tsSysHoliday);
    }
    /**
     * @Description: 校验时间
     *
     * @MethodAuthor gjy,2021年12月3日
     */
    @Transactional(readOnly = true)
    public int checkDate(TsSysHoliday tsSysHoliday){
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT COUNT(*) FROM TS_SYS_HOLIDAY T where 1=1 ");
        if(tsSysHoliday.getRid()!=null) {
            sb.append(" AND T.rid!= ").append(tsSysHoliday.getRid());
        }
        if(tsSysHoliday.getStartDate()!=null && tsSysHoliday.getEndDate()!=null) {
            sb.append(" AND ((T.START_DATE <= TO_DATE('").append(DateUtils.formatDate(tsSysHoliday.getStartDate())).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
            sb.append(" AND T.END_DATE >= TO_DATE('").append(DateUtils.formatDate(tsSysHoliday.getStartDate())).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS'))");
            sb.append(" OR (T.START_DATE <= TO_DATE('").append(DateUtils.formatDate(tsSysHoliday.getEndDate())).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
            sb.append(" AND T.END_DATE >= TO_DATE('").append(DateUtils.formatDate(tsSysHoliday.getEndDate())).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS'))");
            sb.append(" OR (T.START_DATE >= TO_DATE('").append(DateUtils.formatDate(tsSysHoliday.getStartDate())).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
            sb.append(" AND T.END_DATE <= TO_DATE('").append(DateUtils.formatDate(tsSysHoliday.getEndDate())).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')))");
        }
        return this.findCountBySql(sb.toString());
    }
}
