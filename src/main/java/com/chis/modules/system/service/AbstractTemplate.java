package com.chis.modules.system.service;

import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.TypedQuery;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ReflectUtil;
import com.chis.common.utils.*;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.transaction.annotation.Transactional;

import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
public abstract class AbstractTemplate {

    @PersistenceContext(unitName = "OracleDS")
    protected EntityManager em;

    public void save(Object entity) {
        em.persist(entity);
        em.flush();
    }

    @Transactional(readOnly = false)
    public Object saveObj(Object entity) {
        em.persist(entity);
        em.flush();
        return entity;
    }

    @Transactional(readOnly = false)
    public void update(Object entity) {
        em.merge(entity);
        em.flush();
    }

    public Object updateObj(Object entity) {
        em.merge(entity);
        em.flush();
        return entity;
    }

    public void delete(Object entity) {
        em.remove(entity);
        em.flush();
    }

    /**
     * 批量删除
     *
     * @param list 删除的实体
     */
    public void deleteBatchObjs(List<Object> list) {
        for (Object entity : list) {
            em.remove(em.merge(entity));
        }
        em.flush();
    }

    public void delete(Class<?> entityClass, Object id) {
        em.remove(em.getReference(entityClass, id));
        em.flush();
    }

    public <T> T find(Class<T> entityClass, Object id) {
        return em.find(entityClass, id);
    }

    public List findSqlResultList(String sql) {
        return this.em.createNativeQuery(sql).getResultList();
    }

    public List findHqlResultList(String hql) {
        return this.em.createQuery(hql).getResultList();
    }

    /**
     * @param hql   hql语句
     * @param clazz 实体类类型
     * @return List<T> 返回实体集合
     * <AUTHOR>
     * @createDate 2017/9/13 14:32
     * @修改人
     * @修改时间
     * @修改内容
     */
    public <T> List<T> findByHql(String hql, Class<T> clazz) {
        return em.createQuery(hql).getResultList();
    }

    public List findByHql(String hql) {
        return em.createQuery(hql).getResultList();
    }

    /**
     * 批量保存或提交（根据RID判断保存或提交）
     *
     * @param list 保存或提交的实体
     */
    public void saveOrUpdateBatchObjs(List<Object> list) {
        for (Object entity : list) {
            preEntity(entity);
            Integer rid = Convert.toInt(ReflectUtil.invoke(entity, "getRid"));
            if (ObjectUtil.isEmpty(rid)) {
                em.persist(entity);
            } else {
                em.merge(entity);
            }
        }
        em.flush();
    }

    public void saveBatchObjs(List list) {
        for (Object entity : list) {
            em.persist(entity);
        }
        em.flush();
    }

    public void updateBatchObjs(List list) {
        for (Object entity : list) {
            em.merge(entity);
        }
        em.flush();
    }
    /**
 	 * <p>方法描述：暂不使用</p>
 	 * @MethodAuthor qrr,2020年11月3日,executeSql
     * */
    @Deprecated
    public int executeSql(String sql) {
        return em.createNativeQuery(sql).executeUpdate();
    }
    /**
 	 * <p>方法描述：参数化更新</p>
 	 * @MethodAuthor qrr,2020年11月4日,executeSql
     * */
    @Transactional(readOnly = false)
    public int executeSql(String sql, Map<String, Object> paramMap) {
    	Query query = em.createNativeQuery(sql);
    	if (null != paramMap && paramMap.size() > 0) {
    		for (Entry<String, Object> entry : paramMap.entrySet()) {
    			query.setParameter(entry.getKey(),entry.getValue());
			}
        }
        return query.executeUpdate();
    }

    /**
     * <p>
     * 方法描述：根据查询条件查询第一条数据
     * </p>
     *
     * @MethodAuthor mxp, 2018-04-27,findOneByHql
     */
    public <T> T findOneByHql(String hql, Class<T> t) {
        TypedQuery<T> query = em.createQuery(hql, t).setMaxResults(1);
        List<T> resultList = query.getResultList();
        if (resultList != null && resultList.size() > 0) {
            return resultList.get(0);
        }
        return null;
    }

    /**
     * <p>方法描述：新增或者更新</p>
     *
     * @MethodAuthor mxp, 2018-04-25,upsertEntity
     */
    @Transactional(readOnly = false)
    public <T> void upsertEntity(T entity) {
        Object rid = Reflections.getFieldValue(entity, "rid");
        if (rid == null) {
            preInsert(entity);
            saveObj(entity);
        } else {
            preUpdate(entity);
            updateObj(entity);
        }
    }

    @Transactional(readOnly = false)
    public <T> void upsertEntityAndClear(T entity) {
        Object rid = Reflections.getFieldValue(entity, "rid");
        if (rid == null) {
            preInsert(entity);
            saveObj(entity);
        } else {
            preUpdate(entity);
            updateObj(entity);
        }
        em.clear();
    }

    /**
     * <p>方法描述：应急新增或者更新- 没有modifyManid和modifyDate</p>
     *
     * @MethodAuthor yzd, 2018-10-25,upsertEntityNoMod
     */
    @Transactional(readOnly = false)
    public <T> void upsertEntityNoMod(T entity) {
        Object rid = Reflections.getFieldValue(entity, "rid");
        if (rid == null) {
            preInsert(entity);
            saveObj(entity);
        } else {
            updateObj(entity);
        }
    }

    /**
     * 根据是否有RID填入实体创建/更新信息
     *
     * @param entity 实体
     * @param <T>    实体
     */
    public <T> void preEntity(T entity) {
        Object rid = Reflections.getFieldValue(entity, "rid");
        if (rid == null) {
            preInsert(entity);
        } else {
            preUpdate(entity);
        }
    }

    /**
     * <p>方法描述：新增</p>
     *
     * @MethodAuthor mxp, 2018-04-23,preInsert
     */
    public static <T> void preInsert(T t) {
        SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
        TsUserInfo user = sessionData.getUser();
        Reflections.setFieldValue(t, "createManid", user.getRid());
        Reflections.setFieldValue(t, "createDate", new Date());
    }

    /**
     * <p>方法描述：更新</p>
     *
     * @MethodAuthor mxp, 2018-04-23,preUpdate
     */
    public static <T> void preUpdate(T t) {
        SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
        TsUserInfo user = sessionData.getUser();
        Reflections.setFieldValue(t, "modifyManid", user.getRid());
        Reflections.setFieldValue(t, "modifyDate", new Date());
    }

    /**
     * <p>
     * 方法描述：获取数量
     * </p>
     *
     * @MethodAuthor mxp, 2018-04-28,findCountBySql
     */
    public int findCountBySql(String sql) {
        List<Object> resultList = em.createNativeQuery(sql)
                .getResultList();
        if (resultList != null && resultList.size() > 0) {
            return Integer.valueOf(resultList.get(0).toString());
        }

        return 0;

    }

    /**
     * <p>
     * 方法描述：查询某个字段是否重复
     * </p>
     *
     * @MethodAuthor qrr, 2018年4月28日, findArchCode
     */
    public List<Object> findFieldValue(String tableName, String field,
                                       String archCode, Integer mainId) {
        if (StringUtils.isBlank(archCode)) {
            return null;
        }
        StringBuffer buffer = new StringBuffer();
        buffer.append(" select rid from ").append(tableName).append(" where ")
                .append(field).append(" = '").append(archCode).append("'");
        if (null != mainId) {
            buffer.append(" and rid !=").append(mainId);
        }
        List<Object> list = this.findSqlResultList(buffer.toString());
        return list;
    }

    /**
     * <p>
     * 方法描述：查询某个字段是否重复(不区分大小写)
     * </p>
     *
     * @MethodAuthor rcj, 2020年3月20日, findFieldValueIgnore
     */
    public List<Object> findFieldValueIgnore(String tableName, String field,
                                             String archCode, Integer mainId) {

        if (StringUtils.isBlank(archCode)) {
            return null;
        }
        StringBuffer buffer = new StringBuffer();
        buffer.append(" select rid from ").append(tableName).append(" where upper(")
                .append(field).append(") = upper('").append(archCode).append("')");
        if (null != mainId) {
            buffer.append(" and rid !=").append(mainId);
        }
        List<Object> list = this.findSqlResultList(buffer.toString());
        return list;

    }

    /**
     * <p>方法描述：获取实体对象</p>
     *
     * @MethodAuthor qrr, 2018年5月4日, findEntityByMainId
     */
    @Transactional(readOnly = true)
    public <T> T findEntityByMainId(Class<T> t, Integer mainId) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT T FROM ").append(t.getSimpleName()).append(" T WHERE T.fkByMainId.rid = '").append(mainId).append("'");
        return findOneByHql(sb.toString(), t);
    }

    /**
     * <p>方法描述：获取实体对象集合</p>
     *
     * @param <T>
     * @MethodAuthor qrr, 2018年5月4日, findEntityByMainId
     */
    @Transactional(readOnly = true)
    public <T> List<T> findEntityListByMainId(Class<T> t, Integer mainId) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT T FROM ").append(t.getSimpleName()).append(" T WHERE T.fkByMainId.rid = '").append(mainId).append("'");
        return findByHql(sb.toString(), t);
    }

    /**
     * <p>方法描述：获取实体对象集合(rid正序排序)</p>
     *
     * @param <T>
     * @MethodAuthor qrr, 2018年5月4日, findEntityByMainId
     */
    @Transactional(readOnly = true)
    public <T> List<T> findEntityListByMainIdOrderByRid(Class<T> t, Integer mainId) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT T FROM ").append(t.getSimpleName()).append(" T WHERE T.fkByMainId.rid = '").append(mainId).append("' order by T.rid");
        return findByHql(sb.toString(), t);
    }


    /**
     * <p>
     * 方法描述：删除
     * </p>
     *
     * @MethodAuthor mxp, 2018-04-27,deleteEntity
     */
    @Transactional(readOnly = false)
    public <T> void deleteEntity(T t) {
        delete(t);
    }

    /**
     * <p>
     * 方法描述：删除
     * </p>
     *
     * @MethodAuthor mxp, 2018-04-27,deleteEntity
     */
    @Transactional(readOnly = false)
    public <T> void deleteEntity(Class<T> entityClass, Integer rid) {
        delete(entityClass, rid);
    }

    public List findDataBySqlNoPage(String sql, Map<String, Object> paramMap) {
        Query query = em.createNativeQuery(sql);
        if (null != paramMap && paramMap.size() > 0) {
            Iterator itor = paramMap.entrySet().iterator();
            while (itor.hasNext()) {
                Map.Entry entry = (Map.Entry) itor.next();
                query.setParameter(String.valueOf(entry.getKey()),
                        entry.getValue());
            }
        }
        return query.getResultList();
    }

    public List findDataByHqlNoPage(String hql, Map<String, Object> paramMap) {
        Query query = em.createQuery(hql);
        if (null != paramMap && paramMap.size() > 0) {
            Iterator itor = paramMap.entrySet().iterator();
            while (itor.hasNext()) {
                Map.Entry entry = (Map.Entry) itor.next();
                query.setParameter(String.valueOf(entry.getKey()),
                        entry.getValue());
            }
        }
        return query.getResultList();
    }

    public <T> List<T> findDataByHqlNoPage(Class<T> clazz, String hql, Map<String, Object> paramMap) {
        Query query = em.createQuery(hql);
        if (null != paramMap && paramMap.size() > 0) {
            Iterator itor = paramMap.entrySet().iterator();
            while (itor.hasNext()) {
                Map.Entry entry = (Map.Entry) itor.next();
                query.setParameter(String.valueOf(entry.getKey()),
                        entry.getValue());
            }
        }
        return query.getResultList();
    }

    public List findDataBySql(String sql, Map<String, Object> paramMap) {
        Query query = em.createNativeQuery(sql);
        if (null != paramMap && paramMap.size() > 0) {
            Iterator itor = paramMap.entrySet().iterator();
            while (itor.hasNext()) {
                Map.Entry entry = (Map.Entry) itor.next();
                query.setParameter(String.valueOf(entry.getKey()),
                        entry.getValue());
            }
        }
        query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return (List<Map<String, Object>>) query.getResultList();
    }
    /**
     *  <p>方法描述：根据对象进行查询数量</p>
     * @MethodAuthor hsj
     */
    public int findCountBySql(String sql,Map<String, Object> paramMap) {
        List<Object> resultList =findDataBySqlNoPage(sql,paramMap);
        if (resultList != null && resultList.size() > 0) {
            return Integer.valueOf(resultList.get(0).toString());
        }
        return 0;
    }

    /**
     * 根据SQL查询一列BigDecimal类型结果并转换为Integer（去除转换失败的数据）
     *
     * @param sql      SQL
     * @param paramMap SQL参数
     * @return List
     */
    public List<Integer> findIntegerListBySql(String sql, Map<String, Object> paramMap) {
        List<Integer> dataList = new ArrayList<>();
        if (ObjectUtil.isEmpty(sql)) {
            return dataList;
        }
        List<BigDecimal> bigDecimalList =
                CollectionUtil.castList(BigDecimal.class, this.findDataBySqlNoPage(sql, paramMap));
        for (BigDecimal bigDecimal : bigDecimalList) {
            Integer data = ObjectUtil.convert(Integer.class, bigDecimal);
            if (data == null) {
                continue;
            }
            dataList.add(data);
        }
        return dataList;
    }
}
