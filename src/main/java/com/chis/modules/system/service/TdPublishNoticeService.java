package com.chis.modules.system.service;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TdPublishNoticeAnnex;
import com.chis.modules.system.entity.TdPublishNoticeUnit;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.modules.system.entity.TdPublishNotice;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <p>类描述：通知发布Service层</p>
 * @ClassAuthor qrr,2021年11月29日,TdPublishNoticeService
 * */
@Service
@Transactional(readOnly = false, rollbackFor = Throwable.class)
public class TdPublishNoticeService  extends AbstractTemplate{
	/**
 	 * <p>方法描述：更新发布状态</p>
 	 * @MethodAuthor qrr,2021年11月29日,updateTdPublishNotice
	 * */
	public void updateTdPublishNotice(Integer rid,Integer state) {
		if (null==rid || null==state) {
			return;
		}
		this.executeSql("UPDATE TD_PUBLISH_NOTICE SET STATE ="+state+" WHERE RID="+rid, null);
	}
	/**
 	 * <p>方法描述：删除发布记录</p>
 	 * @MethodAuthor qrr,2021年11月29日,deleteTdPublishNotice
	 * */
	public void deleteTdPublishNotice(Integer rid) {
		//删除发布附件
		this.executeSql("delete from TD_PUBLISH_NOTICE_ANNEX WHERE MAIN_ID="+rid, null);
		//删除通知单位
		this.executeSql("delete from TD_PUBLISH_NOTICE_UNIT WHERE MAIN_ID="+rid, null);
		//删除主表
		deleteEntity(TdPublishNotice.class, rid);
	}

	
	/**
	 * @Description: 通知发布修改与更新
	 *
	 * @MethodAuthor pw,2021年12月1日
	 */
	@Transactional(readOnly = false, rollbackFor = Throwable.class)
	public List<String> saveOrUpdatePublishNotice(TdPublishNotice curPublishNotice,
										  List<TdPublishNoticeAnnex> publishNoticeAnnexList,
										  List<Integer> delPublishNoticeAnnexRidList,
										  List<TdPublishNoticeUnit> publishNoticeUnitList,
										  List<Integer> delPublishNoticeUnitRidList){
		//用于删除文件
		List<String> deleteFilePath = new ArrayList<>();
		//删除发布通知附件
		if(!CollectionUtils.isEmpty(delPublishNoticeAnnexRidList)){
			for(Integer annexRid : delPublishNoticeAnnexRidList){
				TdPublishNoticeAnnex noticeAnnex = this.find(TdPublishNoticeAnnex.class,annexRid);
				if(null != noticeAnnex){
					String annexPath = noticeAnnex.getAnnexAddr();
					this.delete(noticeAnnex);
					deleteFilePath.add(annexPath);
				}
			}
		}
		StringBuffer unitRidBuffer = new StringBuffer();
		List<Integer> tmpList = new ArrayList<>();
		//删除通知单位
		if(!CollectionUtils.isEmpty(delPublishNoticeUnitRidList)){
			for(Integer unitRid : delPublishNoticeUnitRidList){
				tmpList.add(unitRid);
				unitRidBuffer.append(",").append(unitRid);
				if(tmpList.size() > 990){
					tmpList.clear();
					unitRidBuffer.setLength(0);
					this.executeSql(" DELETE FROM TD_PUBLISH_NOTICE_UNIT WHERE RID IN ("+unitRidBuffer.substring(1)+")", null);
				}
			}
			if(unitRidBuffer.length() > 0){
				this.executeSql(" DELETE FROM TD_PUBLISH_NOTICE_UNIT WHERE RID IN ("+unitRidBuffer.substring(1)+")", null);
			}
		}
		List<Object> updateObjList = new ArrayList<>();
		List<Object> saveObjList = new ArrayList<>();
		if(null == curPublishNotice.getRid()){
			preInsert(curPublishNotice);
			saveObjList.add(curPublishNotice);
		}else{
			preUpdate(curPublishNotice);
			updateObjList.add(curPublishNotice);
		}
		if(!CollectionUtils.isEmpty(publishNoticeAnnexList)){
			for(TdPublishNoticeAnnex noticeAnnex : publishNoticeAnnexList){
				if(null == noticeAnnex.getRid()){
					preInsert(noticeAnnex);
					saveObjList.add(noticeAnnex);
				}else{
					preUpdate(noticeAnnex);
					updateObjList.add(noticeAnnex);
				}
			}
		}
		if(!CollectionUtils.isEmpty(publishNoticeUnitList)){
			for(TdPublishNoticeUnit noticeUnit : publishNoticeUnitList){
				if(null == noticeUnit.getRid()){
					preInsert(noticeUnit);
					saveObjList.add(noticeUnit);
				}else{
					preUpdate(noticeUnit);
					updateObjList.add(noticeUnit);
				}
			}
		}
		//批量存储对象
		if(!CollectionUtils.isEmpty(saveObjList)){
			this.saveBatchObjs(saveObjList);
		}
		//批量更新对象
		if(!CollectionUtils.isEmpty(updateObjList)){
			this.updateBatchObjs(updateObjList);
		}
		return deleteFilePath;
	}

	/**
	 * <p>方法描述：查询</p>
	 * @MethodAuthor： yzz
	 * @Date：2021-12-01
	 **/
	public TdPublishNotice findPublishNotice(Integer rid){
		TdPublishNotice publishNotice=	this.find(TdPublishNotice.class,rid);
		if(!CollectionUtils.isEmpty(publishNotice.getNoticeAnnexList())){
			publishNotice.getNoticeAnnexList().size();
		}
		Collections.sort(publishNotice.getNoticeAnnexList(), new Comparator<TdPublishNoticeAnnex>() {
			public int compare(TdPublishNoticeAnnex o1, TdPublishNoticeAnnex o2) {
				return o1.getXh() - o2.getXh();
			}
		});
		return publishNotice;
	}

	/**
	 * <p>方法描述：</p>
	 * @MethodAuthor： yzz
	 * @Date：2021-12-02
	 **/
	public void clickNewNoticeLink(String rid){
		StringBuffer hql=new StringBuffer();
		//一个主表下 仅会有一条对应单位的记录
		hql.append(" select T from TdPublishNoticeUnit T where T.fkByUnitId=");
		hql.append(Global.getUser().getTsUnit().getRid());
		hql.append(" and T.fkByMainId.rid=");
		hql.append(rid);
		//避免传递同一个rid进入 导致查询到都是noticeUnit的state不是1 导致TD_PUBLISH_NOTICE的reads被多次加1
		synchronized (TdPublishNoticeService.class){
			TdPublishNoticeUnit noticeUnit=  this.findOneByHql(hql.toString(),TdPublishNoticeUnit.class);
			if(noticeUnit!=null){
				if(noticeUnit.getState()!=null&&noticeUnit.getState()!=1){
					noticeUnit.setState(1);
					this.upsertEntity(noticeUnit);
					//避免多线程导致reads异常
					String updateReadSql = " update TD_PUBLISH_NOTICE set reads=reads+1 where rid= "+rid;
					this.executeSql(updateReadSql, null);
				}
			}
		}
	}

	/**
	 * <p>方法描述：通过通知发布的rid集合 获取存在通知单位回执附件的通知单位回执附件数量 </p>
	 * @MethodAuthor： pw 2023/7/18
	 **/
	public Map<Integer,Integer> groupFindNoticeUnitAnnexCount(List<Integer> publishNoticeRidList){
		if(CollectionUtils.isEmpty(publishNoticeRidList)){
			return Collections.EMPTY_MAP;
		}
		StringBuffer sqlBuffer = new StringBuffer();
		sqlBuffer.append(" SELECT t.RID ,count(t2.RID) FROM TD_PUBLISH_NOTICE t  ")
				.append(" INNER JOIN TD_PUBLISH_NOTICE_UNIT t1 ON t1.MAIN_ID = t.RID ")
				.append(" INNER JOIN TD_PUBLISH_NOTICE_UNIT_ANNEX t2 ON t2.MAIN_ID = t1.RID  ")
				.append(" WHERE t.RID IN (:ridList) ")
				.append(" GROUP BY t.RID ");
		String sql = sqlBuffer.toString();
		Map<Integer,Integer> resultMap = new HashMap<>();
		List<List<Integer>> noticeRidGroup = StringUtils.splitListProxy(publishNoticeRidList, 1000);
		Map<String,Object> sqlParamMap = new HashMap<>();
		for(List<Integer> ridList : noticeRidGroup){
			sqlParamMap.put("ridList", ridList);
			List<Object[]> queryResultList = this.findDataBySqlNoPage(sql, sqlParamMap);
			if(CollectionUtils.isEmpty(queryResultList)){
				continue;
			}
			for(Object[] objArr : queryResultList){
				Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
				Integer count = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
				if(null == rid || null == count){
					continue;
				}
				resultMap.put(rid, count);
			}
		}
		return resultMap;
	}

	/**
	 * <p>方法描述： 通过通知发布的rid 获取对应的通知单位名称以及回执附件名称及附件地址 </p>
	 * @MethodAuthor： pw 2023/7/19
	 **/
	public Map<String, List<Object[]>> findNoticeUnitAnnexForDownLoad(Integer noticeRid){
		if(null == noticeRid){
			return Collections.EMPTY_MAP;
		}
		StringBuffer sqlBuffer = new StringBuffer();
		sqlBuffer.append(" SELECT T1.RID, T2.UNITNAME ,T3.ANNEX_NAME ,T3.ANNEX_ADDR ")
				.append("    FROM TD_PUBLISH_NOTICE T ")
				.append("    INNER JOIN TD_PUBLISH_NOTICE_UNIT T1 ON T1.MAIN_ID = T.RID ")
				.append("    INNER JOIN TS_UNIT T2 ON T1.UNIT_ID = T2.RID ")
				.append("    INNER JOIN TD_PUBLISH_NOTICE_UNIT_ANNEX T3 ON T3.MAIN_ID = T1.RID ")
				.append("    WHERE T.RID = ").append(noticeRid)
				.append("    ORDER BY T1.RID ");
		List<Object[]> queryResultList = this.findDataBySqlNoPage(sqlBuffer.toString(), null);
		if(CollectionUtils.isEmpty(queryResultList)){
			return Collections.EMPTY_MAP;
		}
		//key 单位名称 避免出现 两个单位名称一样 导致创建文件夹异常
		Map<String,Integer> unitNameMap = new HashMap<>();
		//缓存通知单位的名称
		Map<Integer, String> noticeUnitNameMap = new HashMap<>();
		Map<String, List<Object[]>> resultMap = new HashMap<>();
		Integer curRid = null;
		for(Object[] objArr : queryResultList){
			Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
			String unitName = null == objArr[1] ? null : objArr[1].toString();
			String annexName = null == objArr[2] ? null : objArr[2].toString();
			String annexAddr = null == objArr[3] ? null : objArr[3].toString();
			if(null == rid || StringUtils.isBlank(unitName) || StringUtils.isBlank(annexName)
					|| StringUtils.isBlank(annexAddr)){
				continue;
			}
			if(null == curRid || curRid.compareTo(rid) != 0){
				unitName = this.generateUnitName(unitNameMap, unitName);
				noticeUnitNameMap.put(rid, unitName);
				curRid = rid;
			}else{
				unitName = noticeUnitNameMap.get(rid);
			}
			objArr[1] = unitName;
			List<Object[]> tmpList = resultMap.get(unitName);
			if(null == tmpList){
				tmpList = new ArrayList<>();
			}
			tmpList.add(objArr);
			resultMap.put(unitName, tmpList);
		}
		return resultMap;
	}

	/**
	 * <p>方法描述： 单位名称处理 避免重复</p>
	 * @MethodAuthor： pw 2023/7/19
	 **/
	private String generateUnitName(Map<String,Integer> unitNameMap, String unitName){
		Integer num = unitNameMap.get(unitName);
		if(null == num){
			num = 0;
		}else{
			num++;
		}
		unitNameMap.put(unitName, num);
		if(num == 0){
			return unitName;
		}
		return unitName+num;
	}

}
