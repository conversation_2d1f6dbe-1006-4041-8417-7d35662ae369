package com.chis.modules.system.service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import javax.persistence.Query;
import javax.sql.DataSource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSystemUpdate;
import com.chis.modules.system.enumn.SystemType;


@Service
@Transactional(readOnly = false)
public class PluginUpdateService extends AbstractTemplate {

	private DataSource dataSource = SpringContextHolder.getBean("dataSource");
	
	public void dbUpdate(List<String> sqlList, SystemType systemType) {
		// 版本号从0存起，跟sqlList的索引保持一致
		if (null != sqlList && sqlList.size() > 0) {
			TsSystemUpdate tsu = this.findSystemUpdate(systemType);
			int version = 0;
			if (null != tsu) {
				version = tsu.getCurVersion();
				version++;
			}
			int listsize = sqlList.size();
			if (listsize >= version) {
				String sql;
				for (int i = version; i < listsize; i++) {
					sql = sqlList.get(i);
					// 如果sql语句为空时，则不执行
					if (StringUtils.isNotBlank(sql)) {
						try {
							this.executeUpdate(sql, version, true);
						} catch (Exception e) {
							System.out.println(sql);
							return;
						}
					}
					tsu.setCurVersion(i);
					tsu = this.updateSystemUpdate(tsu);
				}
			}
		}
	}
	
	@Transactional(readOnly = true)
	private TsSystemUpdate findSystemUpdate(SystemType systemType) {
		try {
			String hql = "select t from TsSystemUpdate t where t.paramType = :systemType ";
			Query query = em.createQuery(hql);
			query.setParameter("systemType",systemType.getTypeNo().intValue());
			List<TsSystemUpdate> list = query.getResultList();

			TsSystemUpdate tsu = null;
			if (null != list && list.size() > 0) {
				tsu = list.get(0);
			} else {
				tsu = new TsSystemUpdate();
				tsu.setParamType(systemType.getTypeNo().intValue());
				tsu.setCurVersion(-1);
				tsu.setUpdateTime(new Date());
				em.persist(tsu);
				em.flush();
			}
			return tsu;
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}
	

	/**
	 *  	
     * <p>方法描述：数据库升级xml的方式查询当前版本对象</p>
 	 * 
 	 * @MethodAuthor xt,2019年12月16日,findSystemUpdateByXml
	 * @param systemType
	 * @return
	 */
	@Transactional(readOnly = true)
	public TsSystemUpdate findSystemUpdateByXml(SystemType systemType) {
		try {
			String hql = "select t from TsSystemUpdate t where t.paramType = :systemType ";
			Query query = em.createQuery(hql);
			query.setParameter("systemType", systemType.getTypeNo().intValue());
			List<TsSystemUpdate> list = query.getResultList();

			TsSystemUpdate tsu = null;
			if (null != list && list.size() > 0) {
				tsu = list.get(0);
			}
			return tsu;
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}
	@Transactional(rollbackFor = Exception.class)
	public TsSystemUpdate updateSystemUpdate(TsSystemUpdate tsu) {
		if (tsu.getRid() == null) {
            saveObj(tsu);
        } else {
            updateObj(tsu);
        }
		return tsu;
	}

	/**
	 * 执行sql升级语句
	 * @param sql sql升级语句
	 * @param ver 版本
	 * @param saveLog 异常信息是否保存到log表中
	 */
	@Transactional(readOnly = true,rollbackFor = Exception.class)
	public void executeUpdate(String sql, int ver, boolean saveLog) {
		Connection conn = null;
		try {
			conn = dataSource.getConnection();
			conn.createStatement().executeUpdate(sql);
		} catch (Exception e) {
			e.printStackTrace();

			StringBuilder errorMsg = new StringBuilder();
			sql = sql.replaceAll("\n            ", "<br/>")
					.replaceAll("\n         ", "")
					.replaceAll(" ", "&nbsp;")
					.replaceAll("^(&nbsp;<br/>)*", "")
					.replaceAll("^(<br/>)*", "")
					.replaceAll("\\s*", "");
			errorMsg.append("版本号：").append(ver).append("<br/><br/>升级语句：").append(sql).append("<br/><br/>").append("错误信息：<br/>").append(e);
			for (int j = 0; j < e.getStackTrace().length; j++) {
				errorMsg.append("<br/>&nbsp;&nbsp;&nbsp;&nbsp;")
						.append(e.getStackTrace()[j].toString());
			}

			String tempSql = "INSERT INTO TS_SYSTEM_LOG (RID, HAPPEN_DATE, HINT_INFO, DETAIL_INFO) VALUES(TS_SYSTEM_LOG_SEQ.NEXTVAL, SYSDATE, 'ERROR UPDATE SQL',?)";
			try {
				if (conn != null && saveLog) {
					PreparedStatement pstms = conn.prepareStatement(tempSql);
					pstms.setString(1, errorMsg.toString());
					pstms.executeUpdate();
					conn.commit();
				}
			} catch (Exception e1) {
				e1.printStackTrace();
			}
			throw new RuntimeException(e);
		} finally {
			if (null != conn) {
				try {
					conn.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
		}
	}
	@Transactional(readOnly = false)
	@Override
	public void save(Object entity) {
		super.save(entity);
	}
	
}
