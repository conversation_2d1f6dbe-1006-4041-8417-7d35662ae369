package com.chis.modules.system.service;

import java.sql.Clob;
import java.sql.SQLException;
import java.util.Date;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chis.modules.system.entity.TsSystemLog;

/**
 * 日志服务
 * 
 * <AUTHOR>
 */
@Service
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class LogImpl extends AbstractTemplate {

	public void logError(String msg) {
		TsSystemLog log = new TsSystemLog();
		log.setDetailInfo(msg);
		log.setHappenDate(new Date());
		this.save(log);
	}

	public void logError(TsSystemLog tsSystemLog) {
		tsSystemLog.setHappenDate(new Date());
		this.save(tsSystemLog);
	}


	@Transactional(readOnly = true)
	public String queryDetailByRid(Integer rid) {
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT DETAIL_INFO FROM TS_SYSTEM_LOG WHERE RID =").append(
				rid);
		Clob clob = (Clob) em.createNativeQuery(sb.toString()).getResultList()
				.get(0);
		String detailinfo = "";
		if (clob != null) {
			try {
				detailinfo = clob.getSubString((long) 1, (int) clob.length());
			} catch (SQLException e) {
				e.printStackTrace();
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			}
		}
		return detailinfo;
	}
}
