package com.chis.modules.system.service;

import cn.hutool.core.collection.ListUtil;
import com.chis.common.utils.*;
import com.chis.modules.system.annotation.ZwxLog;
import com.chis.modules.system.entity.*;
import com.chis.modules.system.enumn.LogNoEnum;
import com.chis.modules.system.enumn.MessageType;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.interfaces.ICodeProcess;
import com.chis.modules.system.logic.*;
import com.chis.modules.system.utils.Global;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import javax.persistence.Entity;
import javax.persistence.Query;
import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

/**
 * 修改人：lxk 修改时间：2014-09-12<br/>
 * 修改内容：发布后不能修改或删除，保存时记录修订版本号
 * 
 * <AUTHOR>
 * @createDate 2014年9月12日 下午3:21:29
 * @LastModify LuXuekun
 * @ModifyDate 2014年9月12日 下午3:21:29
 */
@Service
@Transactional(readOnly = false, rollbackFor = Throwable.class)
public class SystemModuleServiceImpl extends AbstractTemplate {

	@Autowired
	private CommServiceImpl commService;
	@Autowired
	private LogImpl logImpl;

	private static final String All_ZONELIST_CACHE = "allZoneListCache";
	private static final String CUR_ZONELIST_CACHE = "currentZoneListCache";

	@Transactional(readOnly = true)
	public List<TsTxtype> findMsgType(String[] states) {
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT t FROM TsTxtype t WHERE 1 =1 ");
		if (states != null && states.length == 1) {
			sb.append(" and  t.status = ").append(states[0]);
		}
		sb.append(" ORDER BY t.xh");
		return em.createQuery(sb.toString()).getResultList();
	}

	public void updateTxType(TsTxtype tsTxtype) {
		super.update(tsTxtype);
	}

	public void updateTbEmtxTypeState(Integer rid, Short state) {
		StringBuilder sb = new StringBuilder("");
		sb.append(" UPDATE TS_TXTYPE T SET T.STATUS = ").append(state);
		sb.append(" WHERE T.RID = ").append(rid);
		em.createNativeQuery(sb.toString()).executeUpdate();
	}
	
	public void delRolePower(Integer rid) {
		StringBuilder sb = new StringBuilder("");
		sb.append(" delete TS_ROLE_POWER T ");
		sb.append(" WHERE T.ROLE_ID = ").append(rid);
		em.createNativeQuery(sb.toString()).executeUpdate();
	}

    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2001, value="保存数据")
	public void saveOrUpdateRole(TsRole role, Integer unitId) {
		if (null != role.getRid()) {
			// 修改
			this.update(role);
		} else {
			if (null == unitId) {
				throw new RuntimeException("参数传入错误！");
			} else {
				List<TsUnitRole> list = new ArrayList<TsUnitRole>(1);
				list.add(new TsUnitRole(new TsUnit(unitId), role));
				role.setTsUnitRoleList(list);
				this.save(role);
			}
		}
	}
    
    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2001, value="保存数据")
	public void saveOrUpdateRoleNew(TsRole role) {
		if (null != role.getRid()) {
			// 修改
			this.update(role);
		} else {
				this.save(role);
		}
	}
    
	@Transactional(readOnly = true)
	public TsRole findRole(Integer rid) {
		TsRole role = (TsRole) this.find(TsRole.class, rid);
		role.getTsRolePowerList().size();
		 return role;
	}



	@Transactional(readOnly = true)
	public TsRole findRoleNew(Integer rid) {
		TsRole role = (TsRole) this.find(TsRole.class, rid);
		role.getTsRolePowerList().size();
		role.getTsUserRoleList().size();
		return role;
	}

	@Transactional(readOnly = true)
	public TsRole findRoleWithRoleMenu(Integer rid) {
		TsRole role = (TsRole) this.find(TsRole.class, rid);
		if (null != role && null != role.getTsRoleMenuList()) {
			role.getTsRoleMenuList().size();
		}
		return role;
	}
	
	@Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2002, value="删除数据")
	public void deleteRoleNew(Integer rid) {
		StringBuilder sql=new StringBuilder();
		sql.append(" delete from TS_ROLE_MENU where ROLE_ID=").append(rid);
		this.em.createNativeQuery(sql.toString()).executeUpdate();

		sql=new StringBuilder();
		sql.append(" delete from TS_ROLE_BTN where ROLE_ID=").append(rid);
		this.em.createNativeQuery(sql.toString()).executeUpdate();

		sql=new StringBuilder();
		sql.append(" delete from TS_ROLE_POWER where ROLE_ID=").append(rid);
		this.em.createNativeQuery(sql.toString()).executeUpdate();

		this.delete(TsRole.class, rid);
	}
	
	
    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2002, value="删除数据")
	public String deleteRole(Integer rid, Integer unitId) {
		String msg = null;
		TsRole role = (TsRole) this.find(TsRole.class, rid);
		if (null != role) {
			List<TsRoleMenu> tsRoleMenus = role.getTsRoleMenuList();
			List<TsUnitRole> tsUnitRoles = role.getTsUnitRoleList();
			List<TsUserRole> tsUserRoles = role.getTsUserRoleList();

			if ((null == tsRoleMenus || tsRoleMenus.size() == 0)
					&& (null == tsUserRoles || tsUserRoles.size() == 0)
					&& (null != tsUnitRoles && tsUnitRoles.size() == 1 && tsUnitRoles.get(0).getTsUnit().getRid()
							.equals(unitId))) {
				try {
					this.delete(TsRole.class, rid);
				} catch (Exception e) {
					e.printStackTrace();
					msg = "该角色已被引用，不允许删除！";
					TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				}
			} else {
				msg = "该角色已被引用，不允许删除！";
			}
		} else {
			msg = "该记录已经被删除，请刷新页面！";
		}
		return msg;
	}

	@Transactional(readOnly = true)
	public List<TsMenu> findMenuList(boolean ifAdmin, Integer userId, Integer unitId) {
		List<TsMenu> list = null;
		StringBuilder sb = new StringBuilder();
		if (ifAdmin) {
			sb.append(" select new TsMenu(t.rid, t.menuLevelNo, t.menuCn, t.isfunc, t.menuEn, t.menuUri) from TsMenu t order by t.menuLevelNo ");
			list = em.createQuery(sb.toString()).getResultList();
		} else {
			sb.append(" select * from ( ");
			sb.append(" SELECT A.RID, A.MENU_LEVEL_NO, A.MENU_CN,A.ISFUNC, A.MENU_EN, A.MENU_URI ");
			sb.append(" FROM TS_MENU A ");
			sb.append(" INNER JOIN TS_USER_MENU B ON B.MENU_TEMPLATE_ID = A.RID ");
			sb.append(" WHERE B.USER_INFO_ID = ").append(userId);
			sb.append(" UNION ");
			sb.append(" SELECT C3.RID, C3.MENU_LEVEL_NO, C3.MENU_CN ,C3.ISFUNC, C3.MENU_EN, C3.MENU_URI");
			sb.append(" FROM TS_USER_ROLE C1 ");
			sb.append(" INNER JOIN TS_ROLE_MENU C2 ON C2.ROLE_ID = C1.ROLE_ID ");
			sb.append(" INNER JOIN TS_MENU C3 ON C2.MENU_ID = C3.RID ");
			sb.append(" WHERE C1.USER_INFO_ID = ").append(userId);
			sb.append(" UNION ");
			sb.append(" SELECT D3.RID, D3.MENU_LEVEL_NO, D3.MENU_CN,D3.ISFUNC, D3.MENU_EN, D3.MENU_URI ");
			sb.append(" FROM TS_UNIT_ROLE D1 ");
			sb.append(" INNER JOIN TS_ROLE_MENU D2 ON D2.ROLE_ID = D1.ROLE_ID ");
			sb.append(" INNER JOIN TS_MENU D3 ON D2.MENU_ID = D3.RID ");
			sb.append(" WHERE D1.UNIT_ID = ").append(unitId);
			sb.append("  ) order by MENU_LEVEL_NO ");
			List<Object[]> rstList = em.createNativeQuery(sb.toString()).getResultList();
			if (null != rstList && rstList.size() > 0) {
				list = new ArrayList<TsMenu>(rstList.size());
				for (Object[] o : rstList) {
					TsMenu t = new TsMenu();
					t.setRid(Integer.valueOf(o[0].toString()));
					t.setMenuLevelNo(o[1].toString());
					t.setMenuCn(o[2].toString());
					t.setIsfunc(Short.valueOf(o[3].toString()));
					t.setMenuEn(StringUtils.objectToString(o[4]));
					t.setMenuUri(StringUtils.objectToString(o[5]));
					list.add(t);
				}
			}
		}
		return list;

	}

	/** 查找当前登录人是否有某个菜单权限 */
	@Transactional(readOnly = true)
	public Boolean findMenuListByUserIdAndMenuEn(Integer userId, String menuEn) {
		List<TsMenu> list = null;
		StringBuilder sb = new StringBuilder();
		sb.append(" select t1.* from ts_menu t1");
		sb.append(" inner join TS_USER_MENU t2 on t1.rid=t2.menu_template_id");
		sb.append(" inner join ts_user_info t3 on t2.user_info_id=t3.rid");
		sb.append(" where t3.rid= ").append(userId);
		sb.append(" and t1.menu_en='").append(menuEn).append("'");
		sb.append(" union");
		sb.append(" select t4.* from ts_menu t4");
		sb.append(" inner join TS_ROLE_MENU t5 on t4.rid=t5.menu_id");
		sb.append(" inner join TS_USER_ROLE t6 on t5.role_id=t6.role_id");
		sb.append(" inner join ts_user_info t7 on t7.rid=t6.user_info_id");
		sb.append(" where t7.rid= ").append(userId);
		sb.append(" and t4.menu_en='").append(menuEn).append("'");
		list = super.em.createNativeQuery(sb.toString()).getResultList();
		return (list != null && list.size() > 0) ? true : false;
	}

	@Transactional(readOnly = true)
	public List<TsMenu> findMenuListAddBtn(Integer userId, Integer unitId) {
		List<TsMenu> list = null;
		StringBuilder sb = new StringBuilder();

		sb.append(" SELECT A.RID,"
				+ "NVL2(B1.BTN_NAME, A.MENU_LEVEL_NO ||'.'|| B1.LEVEL_NO, A.MENU_LEVEL_NO) MENU_LEVEL_NO,"
				+ "  NVL2(B1.BTN_NAME, B1.BTN_NAME, A.MENU_CN) MENU_CN ,B1.BTN_NAME ");
		sb.append(" FROM TS_MENU A ");
		sb.append(" INNER JOIN TS_USER_MENU B ON B.MENU_TEMPLATE_ID = A.RID ");
		sb.append(" LEFT JOIN TS_MENU_BTN B1 ON B1.MENU_TEMPLATE_ID = A.RID ");
		sb.append(" WHERE B.USER_INFO_ID = ").append(userId);
		sb.append(" UNION ");
		sb.append(" SELECT  C3.RID ,"
				+ "NVL2(C4.BTN_NAME, C3.MENU_LEVEL_NO ||'.'|| C4.LEVEL_NO, C3.MENU_LEVEL_NO) MENU_LEVEL_NO"
				+ ",NVL2(C4.BTN_NAME, C4.BTN_NAME, C3.MENU_CN) MENU_CN ,C4.BTN_NAME ");
		sb.append(" FROM TS_USER_ROLE C1 ");
		sb.append(" INNER JOIN TS_ROLE_MENU C2 ON C2.ROLE_ID = C1.ROLE_ID ");
		sb.append(" INNER JOIN TS_MENU C3 ON C2.MENU_ID = C3.RID ");
		sb.append("  LEFT JOIN TS_MENU_BTN C4 ON C4.MENU_TEMPLATE_ID = C3.RID ");
		sb.append(" WHERE C1.USER_INFO_ID = ").append(userId);
		sb.append(" UNION ");
		sb.append(" SELECT C3.RID,C3.MENU_LEVEL_NO " + ", C3.MENU_CN ,''BTN_NAME ");
		sb.append(" FROM TS_USER_ROLE C1 ");
		sb.append(" INNER JOIN TS_ROLE_MENU C2 ON C2.ROLE_ID = C1.ROLE_ID ");
		sb.append(" INNER JOIN TS_MENU C3 ON C2.MENU_ID = C3.RID ");
		sb.append(" WHERE C1.USER_INFO_ID = ").append(userId);

		sb.append(" UNION ");
		sb.append(" SELECT D3.RID,"
				+ "NVL2(D4.BTN_NAME, D3.MENU_LEVEL_NO ||'.'||D4.LEVEL_NO, D3.MENU_LEVEL_NO) MENU_LEVEL_NO"
				+ " , NVL2(D4.BTN_NAME, D4.BTN_NAME, D3.MENU_CN) MENU_CN,D4.BTN_NAME ");
		sb.append(" FROM TS_UNIT_ROLE D1 ");
		sb.append(" INNER JOIN TS_ROLE_MENU D2 ON D2.ROLE_ID = D1.ROLE_ID ");
		sb.append(" INNER JOIN TS_MENU D3 ON D2.MENU_ID = D3.RID ");
		sb.append("  LEFT JOIN TS_MENU_BTN D4 ON D4.MENU_TEMPLATE_ID = D3.RID ");
		sb.append(" WHERE D1.UNIT_ID = ").append(unitId);

		List<Object[]> rstList = em.createNativeQuery(sb.toString()).getResultList();
		if (null != rstList && rstList.size() > 0) {
			list = new ArrayList<TsMenu>(rstList.size());
			for (Object[] o : rstList) {
				TsMenu t = new TsMenu();
				t.setRid(Integer.valueOf(o[0].toString()));
				t.setMenuLevelNo(o[1].toString());
				t.setMenuCn(o[2].toString());
				list.add(t);
			}
		}
		// }
		return list;
	}

    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2001, value="菜单分配")
	public void sqMenu(Set<Integer> menuSet, Integer roleId) {
		StringBuilder sb = new StringBuilder(" delete from TsRoleMenu t where t.tsRole.rid =");
		sb.append(roleId);
		em.createQuery(sb.toString()).executeUpdate();

		if (null != menuSet && menuSet.size() > 0) {
			for (Integer menuId : menuSet) {
				TsRoleMenu trm = new TsRoleMenu();
				trm.setTsMenu(new TsMenu(menuId));
				trm.setTsRole(new TsRole(roleId));
				this.save(trm);
			}
		}
	}

	@Transactional(readOnly = true)
	public List<TsMenu> getTsMenuByMenuNo(Set<String> menuNoSet) {
		List<TsMenu> list = null;
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT T.RID,T.MENU_LEVEL_NO,T.MENU_CN FROM TS_MENU T WHERE T.Menu_Level_No IN (");
		if (null != menuNoSet && menuNoSet.size() > 0) {
			for (String code : menuNoSet) {
				sb.append("'").append(code).append("',");
			}
		}
		sb.append(" '' )");
		List<Object[]> rstList = em.createNativeQuery(sb.toString()).getResultList();
		if (null != rstList && rstList.size() > 0) {
			list = new ArrayList<TsMenu>(rstList.size());
			for (Object[] o : rstList) {
				TsMenu t = new TsMenu();
				t.setRid(Integer.valueOf(o[0].toString()));
				t.setMenuLevelNo(o[1].toString());
				t.setMenuCn(o[2].toString());
				list.add(t);
			}
		}

		return list;

	}

	@Transactional(readOnly = true)
	public List<List<UnitPO>> initUnitChoice(boolean ifAdmin, String zoneCode, Short zoneType, Integer roleId,
			Integer unitId) {
		/**
		 * 如果是超管，可以所有单位 如果是普通用户，只能看到自己和下级地区的单位
		 * 
		 * 0-sourceList 1-targetList
		 */
		List<List<UnitPO>> list = new ArrayList<List<UnitPO>>(2);
		List<UnitPO> sourceList = new ArrayList<UnitPO>();
		List<UnitPO> targetList = new ArrayList<UnitPO>();

		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT B.ZONE_NAME, A.UNITNAME, A.RID, C.RID AS CID, B.ZONE_GB ");
		sb.append(" FROM TS_UNIT A ");
		sb.append(" INNER JOIN TS_ZONE B ON A.ZONE_ID = B.RID ");
		sb.append(" LEFT JOIN TS_UNIT_ROLE C ON A.RID = C.UNIT_ID AND C.ROLE_ID = '").append(roleId).append("' ");
		sb.append(" WHERE A.IF_REVEAL = '1' AND B.IF_REVEAL = '1' ");

		if (!ifAdmin) {
			sb.append(" AND ((B.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
			sb.append(" AND B.ZONE_TYPE > '").append(zoneType).append("') ");
			sb.append(" OR (A.RID = '").append(unitId).append("') )");
		}
		sb.append(" ORDER BY B.ZONE_GB ");

		List<Object[]> temList = em.createNativeQuery(sb.toString()).getResultList();
		if (null != temList && temList.size() > 0) {
			for (Object[] o : temList) {
				UnitPO u = new UnitPO(o[0].toString(), o[1].toString(), Integer.valueOf(o[2].toString()));
				if (null != o[3]) {
					targetList.add(u);
				} else {
					sourceList.add(u);
				}
			}
		}

		list.add(sourceList);
		list.add(targetList);
		return list;
	}

    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2001, value="机构分配")
	public void jgfpRole(boolean ifAdmin, String zoneCode, Short zoneType, Integer roleId, Integer unitId,
			List<UnitPO> list) {
		StringBuilder sb = new StringBuilder();
		sb.append("DELETE FROM TS_UNIT_ROLE T WHERE T.RID IN (");
		if (ifAdmin) {
			sb.append(" SELECT A.RID FROM TS_UNIT_ROLE A WHERE A.UNIT_ID <> '").append(unitId)
					.append("' AND A.ROLE_ID = '").append(roleId).append("' ");
		} else {
			sb.append(" SELECT A.RID FROM TS_UNIT_ROLE A ");
			sb.append(" INNER JOIN TS_UNIT B ON A.UNIT_ID = B.RID ");
			sb.append(" INNER JOIN TS_ZONE C ON B.ZONE_ID = C.RID ");
			sb.append(" WHERE C.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
			sb.append(" AND C.ZONE_TYPE > '").append(zoneType).append("' ");
		}
		sb.append(")");
		em.createNativeQuery(sb.toString()).executeUpdate();
		em.flush();

		if (null != list && list.size() > 0) {
			for (UnitPO u : list) {
				if (!u.getUnitId().equals(unitId)) {
					TsUnitRole tur = new TsUnitRole();
					tur.setTsRole(new TsRole(roleId));
					tur.setTsUnit(new TsUnit(u.getUnitId()));
					this.save(tur);
				}
			}
		}
	}

	@Transactional(readOnly = true)
	public String findCodeTypeOfRole(Integer roleId) {
		StringBuilder sb = new StringBuilder(
				" SELECT LISTAGG(T.CODE_TYPEID, ',') WITHIN GROUP(ORDER BY T.CODE_TYPEID) AS CODE_TYPEID ");
		sb.append(" FROM TS_ROLE_CODEAUTH T ");
		sb.append(" WHERE T.ROLE_ID = '").append(roleId).append("'");
		List<Object> list = em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return list.get(0) == null ? "" : list.get(0).toString();
		}
		return null;
	}

	@Transactional(readOnly = true)
	public String findSimpleCodeOfRole(Integer roleId) {
		StringBuilder sb = new StringBuilder(
				" SELECT LISTAGG(T.SIMPCODE_ID, ',') WITHIN GROUP(ORDER BY T.SIMPCODE_ID) AS SIMPCODE_ID ");
		sb.append(" FROM TS_ROLE_SIMPAUTH T ");
		sb.append(" WHERE T.ROLE_ID = '").append(roleId).append("'");
		List<Object> list = em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return list.get(0) == null ? "" : list.get(0).toString();
		}
		return null;
	}

    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2001, value="码表类型授权")
	public void sqRoleCode(Integer roleId, String selectIds, List<String> chooseList, Integer userId) {
		StringBuilder sb = null;
		if (null != chooseList && chooseList.size() > 0) {
			if (StringUtils.isNotBlank(selectIds)) {
				// 找出新加的码表类别，添加授权表记录
				String[] splits = selectIds.split(",");
				List<String> selectList = new ArrayList<String>();
				for (String s : splits) {
					selectList.add(s);
				}

				List<String> allList = new ArrayList<String>(chooseList);
				allList.addAll(selectList);

				/**
				 * 要删的集合
				 */
				List<String> delList = CollectionUtil.getDiffentNoDuplicate(chooseList, allList);
				/**
				 * 要加的集合
				 */
				chooseList = CollectionUtil.getDiffentNoDuplicate(selectList, allList);

				StringBuilder del = new StringBuilder();
				String delIds = null;
				if (null != delList && delList.size() > 0) {
					for (String s : delList) {
						del.append(",").append(s);
					}
					delIds = del.toString().substring(1);
				}
				if (StringUtils.isNotBlank(delIds)) {
					sb = new StringBuilder(" DELETE FROM TS_ROLE_CODEAUTH WHERE ROLE_ID ='");
					sb.append(roleId).append("' AND CODE_TYPEID IN (").append(delIds).append(") ");
					em.createNativeQuery(sb.toString()).executeUpdate();
				}
			}
			for (String s : chooseList) {
				TsRoleCodeauth t = new TsRoleCodeauth();
				t.setTsRole(new TsRole(roleId));
				t.setTsCodeType(new TsCodeType(Integer.valueOf(s)));
				t.setCreateDate(new Date());
				t.setCreateManid(userId);
				this.save(t);
			}
		} else {
			// chooseList为空，删除该角色下的码表授权
			sb = new StringBuilder(" DELETE FROM TS_ROLE_CODEAUTH WHERE ROLE_ID ='");
			sb.append(roleId).append("' ");
			em.createNativeQuery(sb.toString()).executeUpdate();
		}

	}

    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2001, value="码表数据授权")
	public void sqRoleSimpleCode(Integer roleId, String selectIds, List<String> chooseList, Integer userId) {
		StringBuilder sb = null;
		if (null != chooseList && chooseList.size() > 0) {
			if (StringUtils.isNotBlank(selectIds)) {
				// 找出新加的码表类别，添加授权表记录
				String[] splits = selectIds.split(",");
				List<String> selectList = new ArrayList<String>();
				for (String s : splits) {
					selectList.add(s);
				}

				List<String> allList = new ArrayList<String>(chooseList);
				allList.addAll(selectList);

				/**
				 * 要删的集合
				 */
				List<String> delList = CollectionUtil.getDiffentNoDuplicate(chooseList, allList);
				/**
				 * 要加的集合
				 */
				chooseList = CollectionUtil.getDiffentNoDuplicate(selectList, allList);

				StringBuilder del = new StringBuilder();
				String delIds = null;
				if (null != delList && delList.size() > 0) {
					for (String s : delList) {
						del.append(",").append(s);
					}
					delIds = del.toString().substring(1);
				}
				if (StringUtils.isNotBlank(delIds)) {
					sb = new StringBuilder(" DELETE FROM TS_ROLE_SIMPAUTH WHERE ROLE_ID ='");
					sb.append(roleId).append("' AND SIMPCODE_ID IN (").append(delIds).append(") ");
					em.createNativeQuery(sb.toString()).executeUpdate();
				}
			}
			for (String s : chooseList) {
				TsRoleSimpauth t = new TsRoleSimpauth();
				t.setTsRole(new TsRole(roleId));
				t.setTsSimpleCode(new TsSimpleCode(Integer.valueOf(s)));
				t.setCreateDate(new Date());
				t.setCreateManid(userId);
				this.save(t);
			}
		} else {
			// chooseList为空，删除该角色下的码表授权
			sb = new StringBuilder(" DELETE FROM TS_ROLE_SIMPAUTH WHERE ROLE_ID ='");
			sb.append(roleId).append("' ");
			em.createNativeQuery(sb.toString()).executeUpdate();
		}

	}

	/**
	 *（此方法已废弃）
	 * <pre>新方法: {@link CommServiceImpl#findZoneListByGbAndType}
	 */
	@Deprecated
	@Transactional(readOnly = true)
	public List<TsZone> findZoneListICanSee(boolean ifAdmin, String zoneCode) {
		StringBuilder sb = new StringBuilder(
				" select t from TsZone t where t.ifReveal='1' ");
		if (!ifAdmin) {
			sb.append(" and t.zoneGb like '").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		}
		sb.append(" order by t.zoneGb ");
		return em.createQuery(sb.toString()).getResultList();
	}

	/**
	 * <p>方法描述：    </p>
	 * （此方法已废弃）
	 * <pre>新方法: {@link CommServiceImpl#findZoneListByGbAndType}
	 *
	 * @param flag   true 时返回全国的地区
	 * @param zoneGb 省级地区编码 比如重庆5000000000
	 **/
	@Deprecated
	public List<TsZone> findZoneListWithAllZoneByFlag(boolean flag, String zoneGb) {
		List<TsZone> editZoneList = new ArrayList<>();
		if(flag){
			//获取所有地区
			editZoneList = ObjectCopyUtil.deepCopy(
					CollectionUtil.castList(TsZone.class, CacheUtils.get(All_ZONELIST_CACHE, zoneGb))
			);
			if(CollectionUtils.isEmpty(editZoneList)){
				//此处应在findZoneListOrderByUserZone中调整 然后调用findZoneListOrderByUserZone方法 待web-system版本明确后 调整过去
				List<Integer> tmpZoneRidList = new ArrayList<>();
				editZoneList = findZoneListICanSee(false, zoneGb);
				if(!CollectionUtils.isEmpty(editZoneList)){
					for(TsZone tsZone : editZoneList){
						if(null != tsZone.getRid()){
							tmpZoneRidList.add(tsZone.getRid().intValue());
						}
					}
				}

				List<TsZone> tmpTotalZoneList = findZoneListICanSee(true, null);
				if(!CollectionUtils.isEmpty(tmpTotalZoneList)){
					for(TsZone tsZone : tmpTotalZoneList){
						//加入非本省地区
						if(null != tsZone.getRid() && !tmpZoneRidList.contains(tsZone.getRid().intValue())){
							if(null == editZoneList){
								editZoneList = new ArrayList<>();
							}
							editZoneList.add(tsZone);
						}
					}
				}
				if(!CollectionUtils.isEmpty(editZoneList)){
					CacheUtils.put(All_ZONELIST_CACHE, zoneGb, editZoneList);
				}
			}
		}else{
			editZoneList = (List<TsZone>)CacheUtils.get(CUR_ZONELIST_CACHE, zoneGb);
			if(CollectionUtils.isEmpty(editZoneList)){
				editZoneList = findZoneListICanSee(false, zoneGb);
				if(!CollectionUtils.isEmpty(editZoneList)){
					CacheUtils.put(CUR_ZONELIST_CACHE, zoneGb, editZoneList);
				}
			}
		}
		return editZoneList;
	}

	/**
	 * 获取全国地区并将传入地区放在最前面
	 * （此方法已废弃）
	 * <pre>新方法: {@link CommServiceImpl#findZoneListByGbAndType}
	 *
	 * @param zoneGb 地区编号
	 * @return 全国地区
	 */
	@Deprecated
	public List<TsZone> findZoneListWithAllZoneAndEntireCountry(String zoneGb) {
		List<TsZone> tsZoneList = findZoneListWithAllZoneByFlag(true, zoneGb);
		//将全国节点移至List.get(0)
		TsZone tsZone0 = null;
		for (int i = tsZoneList.size() - 1; i >= 0; i--) {
			TsZone tsZone = tsZoneList.get(i);
			if ("000000000000".equals(tsZone.getZoneCode())) {
				tsZone0 = tsZone;
			}
			//找到"全国"后将前面的对象后移一位直到下标为0放入"全国"
			if (tsZone0 != null) {
				if (i <= 0) {
					tsZoneList.set(0, tsZone0);
					continue;
				}
				tsZoneList.set(i, tsZoneList.get(i - 1));
			}
		}
		return tsZoneList;
	}

	/**
	 *（此方法已废弃）
	 * <pre>新方法: {@link CommServiceImpl#findZoneListByGbAndType}
	 */
	@Deprecated
	@Transactional(readOnly = true)
	public List<TsZone> findZoneList(String zoneCode) {
		StringBuilder sb = new StringBuilder(
				" select t.rid, t.zone_Gb, t.zone_Name, t.zone_Type from Ts_Zone t where t.if_Reveal='1' ");
		sb.append(" and t.zone_Gb like '").append(zoneCode.substring(0, 4)).append("%' ").append(" and t.zone_type<=5");
		sb.append(" order by t.zone_Gb ");
		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if (list != null && list.size() > 0) {
			List<TsZone> result = new ArrayList<>();
			for (Object[] obj : list) {
				TsZone tsZone = new TsZone();
				tsZone.setRid(obj[0] == null ? null : Integer.valueOf(obj[0].toString()));
				tsZone.setZoneGb(StringUtils.objectToString(obj[1]));
				tsZone.setZoneType((obj[3] == null ? null : Short.valueOf(obj[3].toString())));
				tsZone.setZoneName(StringUtils.objectToString(obj[2]));
				result.add(tsZone);
			}
			return result;
		}
		return null;
	}

	/**
	 *（此方法已废弃）
	 * <pre>新方法: {@link CommServiceImpl#findZoneListByGbAndType}
	 */
	@Deprecated
	public List<TsZone> findZoneListByLevel(String zoneGb, Integer subLevel) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TsZone t where t.ifReveal = 1 and t.zoneType <= 5");
		sb.append(" and t.zoneGb like '").append(zoneGb.substring(0, subLevel)).append("%'");
		sb.append(" order by zoneGb");
		return em.createQuery(sb.toString()).getResultList();
	}

	/**
	 * 委托和代管业务中的地区查询 登录人地区编码 委托和代管的单位集合
	 * （此方法已废弃）
	 * <pre>新方法: {@link CommServiceImpl#findZoneListByGbAndType}
	 */
	@Deprecated
	public List<TsZone> findRelZoneList(String zoneGb, List<TsUnitRel> relUnitList) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TsZone t where t.ifReveal = 1 and t.zoneType <=5");
		sb.append(" and ( t.zoneGb like '").append(ZoneUtil.zoneSelect(zoneGb)).append("%'");
		if (null != relUnitList && relUnitList.size() > 0) {
			for (TsUnitRel t : relUnitList) {
				sb.append(" or t.zoneGb like '")
						.append(ZoneUtil.zoneSelect(t.getFkByRelUnitId().getTsZone().getZoneGb())).append("%'");
			}
		}
		sb.append(")");
		sb.append(" order by t.zoneGb");
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public List<TsUnit> findUnitByZoneId(boolean ifAdmin, String curZoneCode, Integer unitId, String zoneCode,
			String zoneLevel) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select new TsUnit(t.rid, t.unitname) from TsUnit t where t.ifReveal='1' ");
		/**
		 * 选中的地区是当前地区要考虑是否是超管，超管能看到所有单位，普通用户只能看到自己
		 */
		if (curZoneCode.equals(zoneCode) && !ifAdmin) {
			sb.append(" and t.rid ='").append(unitId).append("' ");
		} else {
			sb.append(" and t.tsZone.zoneGb like'").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
			if (StringUtils.isNotBlank(zoneLevel)) {
				sb.append(" and (t.tsZone.zoneType = '").append(zoneLevel).append("' or t.tsZone.zoneType = '");
				sb.append((Integer.parseInt(zoneLevel) + 1)).append("') ");
			}
		}
		sb.append(" order by t.tsZone.zoneGb,t.unitname ");
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public List<TsUnit> findUnitByZoneCode(boolean ifAdmin, String curZoneCode, Integer unitId, String zoneCode,
			String zoneLevel) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t.rid, t.unitname  from Ts_Unit t ");
		sb.append(" LEFT JOIN TS_ZONE T1 ON T1.RID = T.ZONE_ID ");
		sb.append(" LEFT JOIN TS_UNIT_ATTR T2 ON T.RID = T2.UNIT_RID ");
		sb.append(" LEFT JOIN TS_BS_SORT T3 ON T3.RID = T2.ATTR_ID where t.if_Reveal='1' ");
		/**
		 * 选中的地区是当前地区要考虑是否是超管，超管能看到所有单位，普通用户只能看到自己
		 */
		if (curZoneCode.equals(zoneCode) && !ifAdmin) {
			sb.append(" and t.rid ='").append(unitId).append("' ");
		} else {
			sb.append(" and t1.zone_Gb like'").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
			if (StringUtils.isNotBlank(zoneLevel)) {
				sb.append(" and (t1.zone_Type = '").append(zoneLevel).append("' or t1.zone_Type = '");
				sb.append((Integer.parseInt(zoneLevel) + 1)).append("') ");
			}
			sb.append(" and T3.SORT_CODE IN (2001,2002,2006)");
		}
		sb.append(" order by t3.SORT_CODE,T1.zone_gb,t.unitname ");
		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if (list != null && list.size() > 0) {
			List<TsUnit> result = new ArrayList<>();
			for (Object[] objects : list) {
				TsUnit tsUnit = new TsUnit();
				tsUnit.setRid(Integer.valueOf(objects[0].toString()));
				tsUnit.setUnitname(objects[1].toString());
				result.add(tsUnit);
			}
			return result;
		}
		return null;
	}

	/**
	 * 根据地区编码查询本级单位
	 * 
	 * @param zoneGb
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TsUnit> findUnitByZoneId(String zoneGb) {
		if (StringUtils.isNotBlank(zoneGb)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select new TsUnit(t.rid, t.unitname) from TsUnit t where t.ifReveal='1' ");
			sb.append(" and t.tsZone.zoneGb like'").append(ZoneUtil.zoneSelect(zoneGb)).append("%' ");
			sb.append(" and (t.tsZone.zoneType = '").append(ZoneUtil.getZoneType(zoneGb)).append("' )");
			sb.append(" order by t.tsZone.zoneGb,t.unitname ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TsUserInfo> initUserChoice(boolean ifAdmin, String zoneCode, Short zoneType, Integer roleId,
			Integer unitId) {
		List<TsUserInfo> targetList = new ArrayList<TsUserInfo>();

		/**
		 * 先构件target再构件source
		 */
		StringBuilder sb;
		if (ifAdmin) {
			sb = new StringBuilder(
					" select new TsUserInfo(t.rid, t.userNo, t.username) from TsUserRole t0 inner join t0.tsUserInfo t ");
			sb.append(" where t.ifReveal=1 and t0.tsRole.rid =").append(roleId).append(" order by t.username ");
			targetList = em.createQuery(sb.toString()).getResultList();
		} else {
			sb = new StringBuilder(" SELECT DISTINCT B.RID, B.USERNAME, B.USER_NO FROM TS_USER_ROLE A ");
			sb.append(" INNER JOIN TS_USER_INFO B ON A.USER_INFO_ID = B.RID ");
			sb.append(" INNER JOIN TS_UNIT C ON B.UNIT_RID = C.RID ");
			sb.append(" INNER JOIN TS_ZONE D ON C.ZONE_ID = D.RID ");
			sb.append(" WHERE A.ROLE_ID = '").append(roleId).append("' ");
			sb.append(" AND (C.RID = '").append(unitId).append("' OR (D.ZONE_GB LIKE '")
					.append(ZoneUtil.zoneSelect(zoneCode)).append("%' AND D.ZONE_TYPE > '").append(zoneType)
					.append("'))");
			sb.append(" ORDER BY B.USERNAME ");

			List<Object[]> temList = em.createNativeQuery(sb.toString()).getResultList();
			if (null != temList && temList.size() > 0) {
				for (Object[] o : temList) {
					TsUserInfo tui = new TsUserInfo(Integer.valueOf(o[0].toString()), o[2].toString(), o[1].toString());
					targetList.add(tui);
				}
			}
		}
		return targetList;
	}
	/**
	 * 
	 * <p>方法描述：构造增加手机号</p>
	 *
	 * @MethodAuthor rj,2018年3月6日,findUserByUnitId
	 */
	@Transactional(readOnly = true)
	public List<TsUserInfo> findUserByUnitId(Integer unitId, Integer officeid, Integer rid) {
		List<TsUserInfo> reList = new ArrayList<TsUserInfo>();
		StringBuilder sb = new StringBuilder();
		// for (TsUserInfo t : targetList) {
		// sb.append(",").append(t.getRid());
		// }
		// String ids = sb.toString();
		sb = new StringBuilder(
				" select new TsUserInfo(t.rid, t.userNo, t.username,t.mbNum) from TsUserInfo t where t.tsUnit.rid =");
		sb.append(unitId);
		if (officeid != null) {
			sb.append(" and t.tbSysEmp.tsOffice.rid = ").append(officeid);
		}
		if (null != rid) {
			// ids = ids.substring(1);
			sb.append(
					" and not exists  (select 1 from TsUserRole t0 inner join t0.tsUserInfo t1 where t1.ifReveal=1 and t0.tsRole.rid =")
					.append(rid).append(" and t.rid = t1.rid) ");
		}
		sb.append(" and t.ifReveal = 1 ");
		List<TsUserInfo> list1 = em.createQuery(sb.toString()).getResultList();
		if (list1 != null && list1.size() > 0) {
			reList.addAll(list1);
		}
		if (officeid != null) {
			sb = new StringBuilder();
			sb.append("select new TsUserInfo(t.rid, t.userNo, t.username,t.mbNum) from TsUserInfo t where t.tbSysEmp.rid in (");
			sb.append("select t1.tbSysEmp.rid from TsParttimeInfo t1 where t1.tsOffice.rid = ").append(officeid);
			sb.append(")");
			List<TsUserInfo> list2 = em.createQuery(sb.toString()).getResultList();
			if (list2 != null && list2.size() > 0) {
				reList.addAll(list2);
			}
		}
		return reList;

	}

    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2001, value="用户分配")
	public void yhfpRole(List<TsUserInfo> storedTargetList, List<TsUserInfo> targetList, Integer roleId) {
		if (null != storedTargetList && storedTargetList.size() > 0) {
			StringBuilder sb = null;
			StringBuilder ids = new StringBuilder();
			int num = 0;
			for (TsUserInfo t : storedTargetList) {
				ids.append(",").append(t.getRid());
				num++;
				if (num > 200) {
					sb = new StringBuilder();
					sb.append(" delete from TsUserRole t where t.tsRole.rid =");
					sb.append(roleId).append(" and t.tsUserInfo.rid in (").append(ids.toString().substring(1))
							.append(")");
					em.createQuery(sb.toString()).executeUpdate();
					num = 0;
					ids = new StringBuilder();
				}
			}
			if (ids.toString().length() > 0) {
				sb = new StringBuilder();
				sb.append(" delete from TsUserRole t where t.tsRole.rid =");
				sb.append(roleId).append(" and t.tsUserInfo.rid in (").append(ids.toString().substring(1)).append(")");
				em.createQuery(sb.toString()).executeUpdate();
			}
		}

		if (null != targetList && targetList.size() > 0) {
			for (TsUserInfo t : targetList) {
				TsUserRole tur = new TsUserRole();
				tur.setTsRole(new TsRole(roleId));
				tur.setTsUserInfo(new TsUserInfo(t.getRid()));
				this.save(tur);
			}
		}

	}

	// ===================用户管理======================

	@Transactional(readOnly = true)
	public List<TsOffice> findOfficeByUnitId(Integer unitId) {
		StringBuilder sb = new StringBuilder(" select new TsOffice(t.rid, t.officename) from TsOffice t ");
		sb.append(" where t.ifReveal=1 and t.tsUnit.rid = ").append(unitId);
		return em.createQuery(sb.toString()).getResultList();
	}

    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2001, value="保存数据")
	public String saveOrUpdateUser(TsUserInfo user) {
		StringBuilder sb = new StringBuilder(" select new TsUserInfo(t.rid) from TsUserInfo t where t.userNo ='");
		sb.append(user.getUserNo()).append("' ");
		if (null != user.getRid()) {
			sb.append(" and t.rid <> ").append(user.getRid());
		}
		List<TsUserInfo> list = em.createQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return "用户登录名不允许重复！";
		}

		/**
		 * 先保存职工，再保存用户
		 */
		if (user.getUserType().intValue() == 1) {
			TbSysEmp emp = user.getTbSysEmp();
			if (null != emp.getRid()) {
				this.update(emp);
			} else {
				this.save(emp);
			}
		}

		if (null != user.getRid()) {
			this.update(user);
		} else {
			this.save(user);
		}
		return null;
	}

	@Transactional(readOnly = true)
	public TsUserInfo findUser(Integer rid) {
		TsUserInfo user = (TsUserInfo) this.find(TsUserInfo.class, rid);
		if (user == null)
			return null;
		if (null != user.getTbSysEmp() && null != user.getTbSysEmp().getTsParttimeInfoList()) {
			user.getTbSysEmp().getTsParttimeInfoList().size();
		}
		if(null != user.getTsUserRoles() && user.getTsUserRoles().size() >0){
			user.getTsUserRoles().size();
		}
		// 将用户实体中的属性处理成Map<单位属性编码，单位属性实体>
		this.processAttrMap(user);
		return user;
	}

	/**
	 * 将用户实体中的属性处理成Map<单位属性编码，单位属性实体>
	 * 
	 * @param user
	 *            用户实体
	 * <AUTHOR>
	 * @createDate 2015-2-13
	 */
	private void processAttrMap(TsUserInfo user) {
		if (null != user && null != user.getTsUnit() && null != user.getTsUnit().getTsUnitAttrs()) {
			Map<String, TsBsSort> map = new HashMap<String, TsBsSort>();
			Iterator<TsUnitAttr> tAit = user.getTsUnit().getTsUnitAttrs().iterator();
			while (tAit.hasNext()) {
				TsBsSort tsBsSort = tAit.next().getTsBsSort();
				map.put(tsBsSort.getSortCode(), tsBsSort);
			}
			user.setTsBsSortMap(map);
		}
	}

	@Transactional(readOnly = true)
	public TsUserInfo findUserWithUserMenu(Integer rid) {
		TsUserInfo user = (TsUserInfo) this.find(TsUserInfo.class, rid);
		if (null != user && null != user.getTsUserMenus()) {
			user.getTsUserMenus().size();
		}
		return user;
	}

    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2002, value="删除数据")
	public String deleteUser(Integer userId) {
		try {
			this.delete(TsUserInfo.class, userId);
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return "该用户已被引用，不允许删除！";
		}
		return null;
	}

	/***
	 * <p>修订内容：停用更新上传标记为0</p>
     * 
     * @MethodReviser maox,2018年12月5日,updateUserState

	 * @param rid
	 * @param stateMark
	 */
    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2001, value="启用/停用数据")
	public void updateUserState(Integer rid, int stateMark) {
		StringBuilder sb = new StringBuilder();
		sb.append(" UPDATE TS_USER_INFO T SET T.IF_REVEAL = '").append(stateMark).append("'");
		if(stateMark==0){
			sb.append(" , T.UPLOAD_TAG = 0");
		}
		sb.append(" WHERE T.RID = '").append(rid).append("'");
		em.createNativeQuery(sb.toString()).executeUpdate();
	}

	@Transactional(readOnly = true)
	public Map<String, Integer> findRoleMapICanSee(Integer userId, Integer unitId) {
		Map<String, Integer> map = new LinkedHashMap<String, Integer>();
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT B.ROLE_NAME, B.RID ");
		sb.append(" FROM TS_USER_ROLE A INNER JOIN TS_ROLE B ON A.ROLE_ID = B.RID ");
		sb.append(" WHERE A.USER_INFO_ID = '").append(userId).append("' ");
		sb.append(" UNION ");
		sb.append(" SELECT D.ROLE_NAME, D.RID ");
		sb.append(" FROM TS_UNIT_ROLE C INNER JOIN TS_ROLE D ON C.ROLE_ID = D.RID ");
		sb.append(" WHERE C.UNIT_ID = '").append(unitId).append("' ");
		sb.append(" ORDER BY ROLE_NAME ");
		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			for (Object[] o : list) {
				map.put(o[0].toString(), Integer.valueOf(o[1].toString()));
			}
		}
		return map;
	}

	@Transactional(readOnly = true)
	public List<String> findRoleListIHave(Integer userId) {
		List<String> list = new ArrayList<String>(0);
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT A.ROLE_ID FROM TS_USER_ROLE A WHERE A.USER_INFO_ID = '").append(userId).append("'");
		List<Object> temList = em.createNativeQuery(sb.toString()).getResultList();
		if (null != temList && temList.size() > 0) {
			for (Object o : temList) {
				list.add(o.toString());
			}
		}
		return list;
	}

    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2001, value="角色分配")
	public void grantRolesToUser(Integer userId, List<String> roleList) {
		StringBuilder sb = new StringBuilder();
		sb.append(" delete from TsUserRole t where t.tsUserInfo =").append(userId);
		em.createQuery(sb.toString()).executeUpdate();

		if (null != roleList && roleList.size() > 0) {
			for (String roleId : roleList) {
				TsUserRole tur = new TsUserRole();
				tur.setTsRole(new TsRole(Integer.valueOf(roleId)));
				tur.setTsUserInfo(new TsUserInfo(userId));
				this.save(tur);
			}
		}
	}

    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2001, value="菜单授权")
	public void grantMenuToUser(Set<Integer> menuSet, Integer userId) {
		StringBuilder sb = new StringBuilder(" delete from TsUserMenu t where t.tsUserInfo.rid =");
		sb.append(userId);
		em.createQuery(sb.toString()).executeUpdate();

		if (null != menuSet && menuSet.size() > 0) {
			for (Integer menuId : menuSet) {
				TsUserMenu trm = new TsUserMenu();
				trm.setTsMenu(new TsMenu(menuId));
				trm.setTsUserInfo(new TsUserInfo(userId));
				this.save(trm);
			}
		}
	}

    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2001, value="密码初始化")
	public void initUserPwd(Integer rid) {
		StringBuilder sb = new StringBuilder(" UPDATE TS_USER_INFO SET PASSWORD ='");
		sb.append(new MD5Util().getMD5ofStr(Constants.INIT_PWD)).append("',IF_MODPSW = 0,UPLOAD_TAG =0  WHERE RID ='").append(rid).append("' ");

		em.createNativeQuery(sb.toString()).executeUpdate();
	}
    
    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2001, value="初始化用户登录次数")
	public void updateUserLogTimes(String userNo) {
		StringBuilder sb = new StringBuilder(" UPDATE TS_USER_SECURITY_LOG SET CTU_FAIL_TIMES =0,LOCK_DATE=null");
		sb.append(" where LOGIN_INFO like '%''userNo'':''").append(userNo).append("''%'");
		em.createNativeQuery(sb.toString()).executeUpdate();
	}

	@Transactional(readOnly = true)
	public TsUnit findUnitWithSort(Integer unitId) {
		TsUnit unit = (TsUnit) this.find(TsUnit.class, unitId);
		if (null != unit && null != unit.getTsUnitAttrs()) {
			unit.getTsUnitAttrs().size();
		}
		return unit;
	}

    @Transactional(readOnly = true)
    @ZwxLog(type=LogNoEnum.NO_2001, value="保存数据")
	public TsUnit saveOrUpdateUnit(TsUnit unit, List<String> selectedSortList) {
		if (StringUtils.isBlank(unit.getRegCode())) {
			String uuid = UUID.randomUUID().toString().replace("-", "").toUpperCase();
			unit.setRegCode(uuid);
		}
		if (StringUtils.isBlank(unit.getUnitCode())) {
			unit.setUnitCode(commService.getAutoCode("TS_UNIT_DWBM",
					((TsZone) super.find(TsZone.class, unit.getTsZone().getRid())).getZoneCode().substring(0, 6)));
		}

        boolean tjFlag = false;
		if(null != selectedSortList && selectedSortList.size()>0){
			for(String sortId : selectedSortList) {
				TsBsSort tsBsSort = this.find(TsBsSort.class, Integer.valueOf(sortId));
				if(StringUtils.isNotBlank(tsBsSort.getSortCode())
						&& tsBsSort.getSortCode().equals("1003")) {
					tjFlag = true;
					break;
				}
			}
		}

		if (null != unit.getRid()) {
			if (null == selectedSortList) {
				selectedSortList = new ArrayList<String>(0);
			}

			StringBuilder sb = new StringBuilder();
			List<String> oldList = new ArrayList<String>();
			if (null != unit.getTsUnitAttrs() && unit.getTsUnitAttrs().size() > 0) {
				for (TsUnitAttr tua : unit.getTsUnitAttrs()) {
				    if(CollectionUtils.isEmpty(selectedSortList)) {
                        if (StringUtils.isNotBlank(tua.getTsBsSort().getSortCode())
                                && tua.getTsBsSort().getSortCode().equals("1003")) {
                            tjFlag = true;
                        }
                    }
					oldList.add(tua.getTsBsSort().getRid().toString());
				}
			}

			List<String> allList = new ArrayList<String>(oldList);
			allList.addAll(selectedSortList);

			/**
			 * 要删的集合
			 */
			List<String> delList = CollectionUtil.getDiffentNoDuplicate(selectedSortList, allList);
			/**
			 * 要加的集合
			 */
			selectedSortList = CollectionUtil.getDiffentNoDuplicate(oldList, allList);

			StringBuilder del = new StringBuilder();
			if (null != delList && delList.size() > 0) {
				for (String s : delList) {
					del.append(",").append(s);
				}
			}
			String delIds = del.toString();
			if (StringUtils.isNotBlank(delIds)) {
				sb.append(" DELETE FROM TS_UNIT_ATTR WHERE UNIT_RID ='").append(unit.getRid()).append("' ");
				sb.append(" AND ATTR_ID IN (").append(delIds.substring(1)).append(") ");
				em.createNativeQuery(sb.toString()).executeUpdate();
				em.flush();
			}
			unit.setTsUnitAttrs(null);
			unit = (TsUnit) this.updateObj(unit);
		} else {
			// 添加
			// 插入基本信息
			unit = (TsUnit) this.saveObj(unit);
		}

		if (null != selectedSortList && selectedSortList.size() > 0) {
			for (String sortId : selectedSortList) {
				TsUnitAttr tua = new TsUnitAttr();
				tua.setTsUnit(unit);
				TsBsSort tsBsSort = this.find(TsBsSort.class, Integer.valueOf(sortId));
				tua.setTsBsSort(tsBsSort);
				this.save(tua);
			}
		}

        // 判断当前单位单位属性勾选了“体检机构”
        if(tjFlag) {
            List<Object[]> tbtjSrvorgList = this.findTbtjSrvorgListByRegOrgId(unit.getRid());
            // 判断当前单位与资质注册表关联没有数据,新增一条资质注册信息
            if(CollectionUtils.isEmpty(tbtjSrvorgList)) {
                this.saveTbtjSrvorgInfo(unit);
            }
        }
		return unit;
	}

	/**
	* @Description : 根据单位信息新增一条资质服务机构注册信息
	* @MethodAuthor: anjing
	* @Date : 2020/5/22 11:10
	**/
	public void saveTbtjSrvorgInfo(TsUnit tsUnit) {
        TsSimpleCode sortSimpleCode = new TsSimpleCode();
	    List<TsSimpleCode> tsSimpleCodeList = this.commService.findSimpleCodesByTypeId("5001");
        if(!CollectionUtils.isEmpty(tsSimpleCodeList)) {
            for(TsSimpleCode tsSimpleCode : tsSimpleCodeList) {
                if(StringUtils.isNotBlank(tsSimpleCode.getCodeNo()) && tsSimpleCode.getCodeNo().equals("1001")) {
                    sortSimpleCode.setRid(tsSimpleCode.getRid());
                    sortSimpleCode.setCodeNo(tsSimpleCode.getCodeNo());
                    sortSimpleCode.setCodeName(tsSimpleCode.getCodeName());
                    break;
                }
            }
        }
        StringBuilder sb = new StringBuilder(" SELECT ");
        sb.append(" TB_TJ_SRVORG_SEQ.NEXTVAL FROM DUAL ");
        Integer rid = Integer.valueOf(super.em.createNativeQuery(sb.toString()).getSingleResult().toString());
        sb = new StringBuilder();
        sb.append("INSERT INTO TB_TJ_SRVORG(RID, ZONE_ID, UNIT_CODE, UNIT_NAME, APT_SORTID, STOP_TAG, REG_CODE, REG_ORGID, UUID, CREATE_DATE, CREATE_MANID) ");
        sb.append(" VALUES(").append(rid).append(", ");
        sb.append(tsUnit.getTsZone().getRid()).append(", ");
        sb.append("'").append(tsUnit.getUnitCode()).append("', ");
        sb.append("'").append(tsUnit.getUnitname()).append("', ");
        if(null != sortSimpleCode) {
            sb.append(sortSimpleCode.getRid()).append(", ");
        } else {
            sb.append("NULL, ");
        }
        sb.append("1, ");
        sb.append("NULL, ");
        sb.append(tsUnit.getRid()).append(", ");
        sb.append("NULL, ");
        sb.append("TO_DATE('").append(DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss")).append("', 'YYYY-MM-DD HH24:MI:SS'), ");
        sb.append(Global.getUser().getRid()).append(") ");
        super.em.createNativeQuery(sb.toString()).executeUpdate();
    }

	/**
	* @Description : 根据注册体检机构ID获取资质服务机构注册信息集合
	* @MethodAuthor: anjing
	* @Date : 2020/5/22 11:07
	**/
	public List<Object[]> findTbtjSrvorgListByRegOrgId(Integer regOrgId) {
        if(null != regOrgId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" SELECT T.RID, T.ZONE_ID, T.UNIT_CODE, T.UNIT_NAME, T.APT_SORTID, T.STOP_TAG, T.REG_CODE, T.REG_ORGID, ");
            sb.append(" T.UUID, T.CREATE_DATE, T.CREATE_MANID ");
            sb.append(" FROM TB_TJ_SRVORG T WHERE 1=1 ");
            sb.append(" AND T.REG_ORGID = '").append(regOrgId).append("' ");
            return em.createNativeQuery(sb.toString()).getResultList();
        }
        return null;
    }

    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2002, value="删除数据")
	public String deleteUnit(Integer unitId) {
		try {
			this.delete(TsUnit.class, unitId);
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return "该单位已被引用，不允许删除！";
		}
		return null;
	}

    @Transactional(readOnly = true)
    @ZwxLog(type=LogNoEnum.NO_2001, value="启用/停用数据")
	public void updateUnitState(Integer rid, int stateMark) {
		StringBuilder sb = new StringBuilder();
		sb.append(" UPDATE TS_UNIT T SET T.IF_REVEAL = '").append(stateMark).append("', ");
		if ("0".equals(stateMark)) {
			sb.append(" T.STOP_DATE = SYSDATE ");
		} else {
			sb.append(" T.STOP_DATE = NULL ");
		}
		sb.append(" WHERE T.RID = '").append(rid).append("'");
		em.createNativeQuery(sb.toString()).executeUpdate();
	}

	@ZwxLog(type=LogNoEnum.NO_2001, value="启用/停用数据")
	public void updateUnitState0(Integer rid) {
		Map<String, Object> paramMap = new HashMap<>();
		String sql = "UPDATE TS_UNIT T SET T.IF_REVEAL = 0, T.STOP_DATE = :nowDate, T.MODIFY_DATE = :nowDate, T.MODIFY_MANID = :manId WHERE T.RID = :rid ";
		paramMap.put("nowDate", new Date());
		paramMap.put("rid", rid);
		paramMap.put("manId", Global.getUser().getRid());
		executeSql(sql, paramMap);
		sql = "UPDATE TS_USER_INFO UI SET UI.IF_REVEAL = 0, UI.MODIFY_DATE = :nowDate, UI.MODIFY_MANID = :manId WHERE UI.UNIT_RID = :rid ";
		executeSql(sql, paramMap);
	}

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findCodeListByTypeId(Integer typeId, String codeName, String stateMark) {
		StringBuilder sb = new StringBuilder(" select t from TsSimpleCode t where t.tsCodeType.rid =");
		sb.append(typeId);
		if (StringUtils.isNotBlank(codeName)) {
			sb.append(" and t.codeName like :codeName ");
		}
		if (StringUtils.isNotBlank(stateMark)) {
			sb.append(" and t.ifReveal=").append(stateMark);
		}
		sb.append(" order by t.num,t.codeNo ");
		Query query = em.createQuery(sb.toString());
		if (StringUtils.isNotBlank(codeName)) {
			query.setParameter("codeName", "%" + codeName + "%");
		}
		return query.getResultList();
	}

	public String saveOrUpdateSimpleCode(TsSimpleCode simpleCode) {
		Map<String,Object> paramMap = new HashMap<>();
		// 检查类别内编码的唯一性
		StringBuilder sb = new StringBuilder(" select t from TsSimpleCode t where t.tsCodeType.rid = ");
		sb.append(simpleCode.getTsCodeType().getRid()).append(" and t.codeNo =:codeNo ");
		if (null != simpleCode.getRid()) {
			sb.append(" and t.rid <>").append(simpleCode.getRid());
		}
		paramMap.put("codeNo",simpleCode.getCodeNo());
		List<TsSimpleCode> list = this.findDataByHqlNoPage(sb.toString(), paramMap);
		if (null != list && list.size() > 0) {
			return "编码不允许重复！";
		}

		/**
		 * 发布状态的码表，只能停用，不能改也不能删，而且停了就不能启用，允许修改，只是不允许修改名称与编码
		 */
		// if (null != simpleCode.getRid() && null !=
		// simpleCode.getPublishTag()) {
		// if (simpleCode.getPublishTag().intValue() == 1) {
		// return "该标准已被发布，不允许修改！";
		// } else {
		// return "该标准已被停用，不允许修改！";
		// }
		// }

		// 如果是树形的，需要添加全路径
		// if (StringUtils.isNotBlank(simpleCode.getCodeLevelNo())) {
		// if (simpleCode.getCodeLevelNo().indexOf(".") != -1) {
		// // 不是最上级
		// String tempCode = simpleCode.getCodeLevelNo().substring(0,
		// simpleCode.getCodeLevelNo().lastIndexOf("."));
		// sb = new
		// StringBuilder(" select t from TsSimpleCode t where t.tsCodeType.rid = ");
		// sb.append(simpleCode.getTsCodeType().getRid()).append(" and t.codeLevelNo ='");
		// sb.append(tempCode).append("' ");
		//
		// list = em.createQuery(sb.toString()).getResultList();
		// if (null != list && list.size() > 0) {
		// TsSimpleCode upCode = list.get(0);
		// simpleCode.setCodePath(upCode.getCodePath() + "_" +
		// simpleCode.getCodeName());
		// }
		// } else {
		// // 最上级
		// simpleCode.setCodePath(simpleCode.getCodeName());
		// }
		// }

		if (null != simpleCode.getRid()) {
			this.update(simpleCode);
		} else {
			simpleCode.setModifyDate(new Date());
			simpleCode.setUUID(UUID.randomUUID().toString());
			this.save(simpleCode);
		}
		/*try {
			em.createNativeQuery("{call SIMPLECODE_PATH_DEAL()}").executeUpdate();
		} catch (Exception e) {
			e.printStackTrace();
		}*/
		return null;
	}

	@Transactional(readOnly = true)
	public TsSimpleCode findSimpleCodeByRid(Integer rid) {
		return (TsSimpleCode) this.find(TsSimpleCode.class, rid);
	}

	public String updateCodeState(Integer rid, int stateMark) {
		StringBuilder sb = new StringBuilder();
		/**
		 * 发布状态的码表，只能停用，不能改也不能删，而且停了就不能启用
		 */
		if (stateMark == 1) {
			TsSimpleCode code = em.find(TsSimpleCode.class, rid);
//			if (null != code.getPublishTag()) {
//				return "该标准已被停用，不允许再启用！";
//			}
		}

		/**
		 * 如果是树形的并且停用操作，需要将子节点都设置为停用
		 */
		StringBuilder ids = new StringBuilder();
		ids.append(rid);
		if (stateMark == 0) {
			sb.append(" SELECT T3.RID ");
			sb.append(" FROM TS_SIMPLE_CODE T1 ");
			sb.append(" INNER JOIN TS_CODE_TYPE T2 ON T1.CODE_TYPE_ID = T2.RID AND T2.TREE_TAG = '1' ");
			sb.append(" INNER JOIN TS_SIMPLE_CODE T3 ON T3.CODE_TYPE_ID = T2.RID AND T3.CODE_LEVEL_NO LIKE T1.CODE_LEVEL_NO ||'.%' ");
			sb.append(" WHERE T1.RID = '").append(rid).append("' ");
			List list = super.em.createNativeQuery(sb.toString()).getResultList();

			if (null != list && list.size() > 0) {
				for (int i = 0; i < list.size(); i++) {
					ids.append(",").append(list.get(i));
				}
			}
		}

		sb = new StringBuilder();
		sb.append(" UPDATE TS_SIMPLE_CODE T SET T.IF_REVEAL = '").append(stateMark).append("',T.MODIFY_DATE=SYSDATE, ");
		if ("0".equals(stateMark)) {
			sb.append(" T.STOP_DATE = SYSDATE ");
		} else {
			sb.append(" T.STOP_DATE = NULL ");
		}
		sb.append(" WHERE T.RID in (").append(ids.toString()).append(")");
		em.createNativeQuery(sb.toString()).executeUpdate();

		// 保存完码表后进行修改记录和停用GBZ 188处理
		TsSimpleCode tempCode = em.find(TsSimpleCode.class, rid);
		Map<String, ICodeProcess> beanMap = SpringContextHolder.getBeans(ICodeProcess.class);
		if (null != beanMap && beanMap.size() > 0) {
			for (ICodeProcess process : beanMap.values()) {
				process.doProcess(tempCode, em, false);
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findCodeListByTypeId(Integer typeId) {
		StringBuilder sb = new StringBuilder(" select t from TsSimpleCode t where t.tsCodeType.rid =");
		sb.append(typeId).append(" order by t.num,t.codeLevelNo ");
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findCodeListByIds(String ids) {
		StringBuilder sb = new StringBuilder(" select t from TsSimpleCode t where t.rid in (");
		sb.append(ids).append(") order by t.codeLevelNo ");
		return em.createQuery(sb.toString()).getResultList();
	}

	public String deleteSimpleCode(Integer codeId) {
		try {
			TsSimpleCode tsSimpleCode = em.find(TsSimpleCode.class, codeId);
			this.delete(TsSimpleCode.class, codeId);
			// 保存完码表后进行修改记录和停用GBZ 188处理
			Map<String, ICodeProcess> beanMap = SpringContextHolder.getBeans(ICodeProcess.class);
			if (null != beanMap && beanMap.size() > 0) {
				for (ICodeProcess process : beanMap.values()) {
					process.doProcess(tsSimpleCode, em, true);
				}
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return "该数据已被引用，不允许删除！";
		}
		return null;
	}

	@Transactional(readOnly = true)
	public Map<SystemType, List<TsSystemParam>> searchSystemParam(String paramName, String paramRmk) {
		SystemType[] sts = SystemType.values();
		Map<SystemType, List<TsSystemParam>> map = new LinkedHashMap<SystemType, List<TsSystemParam>>();
		for (SystemType st : sts) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSystemParam t where  t.paramType = :systemType ");
			if (StringUtils.isNotBlank(paramName)) {
				sb.append(" and t.paramName like :paramName ");
			}
			if (StringUtils.isNotBlank(paramRmk)) {
				sb.append(" and t.paramRmk like :paramRmk ");
			}
			Query q = em.createQuery(sb.toString());
			q.setParameter("systemType", st.getTypeNo().intValue());
			if (StringUtils.isNotBlank(paramName)) {
				q.setParameter("paramName", "%" + paramName.trim() + "%");
			}
			if (StringUtils.isNotBlank(paramRmk)) {
				q.setParameter("paramRmk", "%" + paramRmk.trim() + "%");
			}
			map.put(st, q.getResultList());
		}
		return map;
	}

	@Transactional(readOnly = true)
	public List<Object[]> findObjsByItemSQL(String sql) {
		return em.createNativeQuery(sql).getResultList();
	}

	public void updateParams(Map<String, String> businessMap) {
		if (null != businessMap && businessMap.size() > 0) {
			Set<Map.Entry<String, String>> entrySet = businessMap.entrySet();
			for (Map.Entry<String, String> ent : entrySet) {
				this.updateParam(ent.getKey(), ent.getValue());
			}
		}
	}

	@Transactional(readOnly = true)
	public List<TsCodeRule> searchCodeRule(String hql, Map<String, Object> paramMap) {
		Query query = em.createQuery(hql);
		if (null != paramMap && paramMap.size() > 0) {
			Iterator itor = paramMap.entrySet().iterator();
			while (itor.hasNext()) {
				Map.Entry entry = (Map.Entry) itor.next();
				query.setParameter(String.valueOf(entry.getKey()), entry.getValue());
			}
		}
		return query.getResultList();
	}

	public void updateCodeRule(TsCodeRule rule) {
		this.update(rule);
	}

	/**
	 * 根据主键RID更新参数值
	 * 
	 * @param paramName
	 *            参数名
	 * @param value
	 *            参数值
	 */
	private void updateParam(String paramName, String value) {
		StringBuilder sb = new StringBuilder(" UPDATE TS_SYSTEM_PARAM T SET T.PARAM_VALUE = '");
		sb.append(value).append("' WHERE T.PARAM_NAME = '").append(paramName).append("' ");
		em.createNativeQuery(sb.toString()).executeUpdate();
	}

	// ========地区管理============
	/**
	 * 删除地区，如果地区表的子表中没有地区的记录， 则允许删除，否则不允许删除
	 * 
	 * @param rid
	 *            地区rid
	 * @return 返回信息，如果删除成功就返回null
	 */

	public String deleteZone(Integer rid) {
		String msg = null;
		TsZone zone = (TsZone) this.find(TsZone.class, rid);
		if (null != zone) {
			Set<TsUnit> tsunit = zone.getTsUnits();
			Set<TsZoneMdref> tsZoneNew = zone.getTsZoneMdrefsForNewZoneid();
			Set<TsZoneMdref> tsZoneOld = zone.getTsZoneMdrefsForOldZoneid();

			if ((null == tsunit || tsunit.size() == 0) && (null == tsZoneNew || tsZoneNew.size() == 0)
					&& (null != tsZoneOld && tsZoneOld.size() == 0)) {
				try {
					this.delete(TsZone.class, rid);
				} catch (Exception e) {
					TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
					msg = "该地区已被引用，不允许删除！";
				}
			} else {
				msg = "该地区已被引用，不允许删除！";
			}
		} else {
			msg = "该记录已经被删除，请刷新页面！";
		}
		return msg;
	}

	/**
	 * 添加或修改地区
	 * 
	 * @param zone
	 *            地区对象
	 */

	public void saveOrUpdateZone(TsZone zone) {
		if (null != zone.getRid()) {
			// 修改
			this.update(zone);
		} else {
			// 保存
			this.save(zone);
		}
		try {
			em.createNativeQuery("{call TS_ZONE_FULLNAME_DEAL()}").executeUpdate();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 根据主键查询地区
	 * 
	 * @param rid
	 *            角色rid
	 * @return 地区对象
	 */
	@Transactional(readOnly = true)
	public TsZone findZone(Integer rid) {
		return (TsZone) this.find(TsZone.class, rid);
	}

	/**
	 * 根据主键修改地区状态
	 * 
	 * @param rid
	 *            主键
	 * @param ifReveal
	 *            状态
	 */
	public void changeStateZone(Integer rid, Short ifReveal) {
		TsZone tsZone = findZone(rid);
		tsZone.setIfReveal(ifReveal);
		this.update(tsZone);
	}

	/**
	 * 地区归并逻辑
	 * 
	 * @param zoneRid
	 *            原地区rid
	 * @param targetZoneRid
	 *            现地区rid
	 * @param createManId
	 *            创建人id
	 */

	public void saveRefZone(Integer zoneRid, Integer targetZoneRid, Integer createManId) {
		// 已归并 原有的地区
		changeStateZone(zoneRid, (short) 2);
		// 新建关系记录
		TsZoneMdref tsZoneMdref = new TsZoneMdref();
		tsZoneMdref.setCreateDate(new Date());
		tsZoneMdref.setCreateManid(createManId);
		tsZoneMdref.setTsZoneByOldZoneid(findZone(zoneRid));
		tsZoneMdref.setTsZoneByNewZoneid(findZone(targetZoneRid));
		save(tsZoneMdref);
		// 将原地区下的科室统一归并到新地区下
		StringBuffer updUnitStr = new StringBuffer("update TsUnit n set n.tsZone.rid=").append(targetZoneRid)
				.append(" where n.rid in ( select t.rid from TsUnit t where t.tsZone.rid=").append(zoneRid).append(")");
		em.createQuery(updUnitStr.toString()).executeUpdate();
	}

	/**
	 * 地区列表
	 * 
	 * @return 地区列表
	 */
	@Transactional(readOnly = true)
	public List<TsZone> findZoneListICanSee() {
		StringBuilder sb = new StringBuilder(
				" select new TsZone(t.rid, t.zoneGb, t.zoneName, t.zoneType) from TsZone t where t.ifReveal='1' ");
		sb.append(" order by t.zoneType,t.zoneGb ");
		return em.createQuery(sb.toString()).getResultList();
	}

	/**
	 * 取消归并功能
	 * 
	 * @param tsZoneMdref
	 *            关系表
	 */
	public void cancleRefZone(TsZoneMdref tsZoneMdref) {
		TsZone oldZone = tsZoneMdref.getTsZoneByOldZoneid();
		// 删除关系表
		delete(find(TsZoneMdref.class, tsZoneMdref.getRid()));
		// 修改状态（原地区）为1
		oldZone.setIfReveal((short) 1);
		this.update(oldZone);

	}

	/**
	 * 查询是否存在重复地区编码
	 *
	 * @param type 地区编码类型 <pre>1: ZONE_GB</pre><pre>2: ZONE_CODE</pre>
	 * @param code 地区编码
	 * @param rid  需要排除的记录
	 * @return 是否存在重复地区编码 <pre>true: 存在</pre>
	 */
	public boolean hasSameZoneCode(int type, String code, Integer rid) {
		String zoneCode = "ZONE_GB";
		if (type == 2) {
			zoneCode = "ZONE_CODE";
		}
		Map<String, Object> paramMap = new HashMap<>();
		String sql = "SELECT COUNT(1) FROM TS_ZONE WHERE IF_REVEAL = '1' AND " + zoneCode + " = :code ";
		paramMap.put("code", code);
		if (rid != null) {
			sql += " AND RID <> :rid ";
			paramMap.put("rid", rid);
		}
		return super.findCountBySql(sql, paramMap) > 0;
	}

	/**
	 * 根据ZONE_GB获取地区
	 *
	 * @param zoneGb zoneGb
	 * @return 地区
	 */
	public TsZone findZoneByZoneGb(String zoneGb) {
		String hql = "select t from TsZone t where t.ifReveal = '1' and t.zoneGb = '" + zoneGb + "' ";
		return super.findOneByHql(hql, TsZone.class);
	}

	/**
	 * 保存地区自检结果
	 *
	 * @param zoneList 地区
	 */
	public void updateZoneCheckMsg(List<TsZone> zoneList) {
		super.updateBatchObjs(zoneList);
	}

	/**
	 * 查询是否存在重复地区编码
	 * 
	 * @param rid
	 * @param oldGb
	 * @param newGb
	 * @return 存在返回false
	 */
	@Transactional(readOnly = true)
	public boolean selZoneGB(Integer rid, String oldGb, String newGb) {
		if (null == rid) {
			// 添加查询
			StringBuffer hql = new StringBuffer("select t from TsZone t where t.zoneGb='").append(newGb).append("'");
			List list = em.createQuery(hql.toString()).getResultList();
			if (null != list && list.size() > 0) {
				return false;
			}
		} else {
			// 修改查询
			StringBuffer hql = new StringBuffer("select t from TsZone t where t.zoneGb='").append(newGb)
					.append("' and t.zoneGb<>'").append(oldGb).append("'");
			List list = em.createQuery(hql.toString()).getResultList();
			if (null != list && list.size() > 0) {
				return false;
			}
		}
		return true;
	}

	/***
	 *  <p>方法描述：12位地区编码验证</p>
     *
     * @MethodAuthor maox,2018年10月18日,selZoneCode
	 * @param tsZone
	 * @return
	 */
	@Transactional(readOnly = true)
	public boolean selZoneCode(TsZone tsZone) {
		StringBuffer hql = new StringBuffer("select t from TsZone t where t.zoneCode='").append(tsZone.getZoneCode());
		hql.append("' and t.rid<>").append(tsZone.getRid());
		List list = em.createQuery(hql.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return false;
		}
		return true;
	}
	
	/**
	 * 保存菜单信息
	 * 
	 * @param tsMenu
	 */
	public void saveOrUpdateMenu(TsMenu tsMenu) {
		if (null != tsMenu.getRid()) {
			// 修改
			this.update(tsMenu);
		} else {
			// 保存
			this.save(tsMenu);
		}
	}

	/**
	 * 删除菜单，如果菜单表的子表中没有地区的记录，则允许删除，否则不允许删除
	 * <p>删除时同步删除关联角色菜单按钮权限、关联用户菜单按钮权限、关联菜单按钮、关联角色菜单权限
	 *
	 * @param rid 菜单rid
	 * @return 返回信息，如果删除成功就返回空字符串
	 */
	public String deleteMenu(Integer rid) {
		String msg = "";
		TsMenu menu = this.find(TsMenu.class, rid);
		if (null == menu) {
			msg = "该记录已经被删除，请刷新页面！";
			return msg;
		}
		try {
			String trbSql = "DELETE FROM TS_ROLE_BTN TRB WHERE " +
					"EXISTS(SELECT 1 FROM TS_MENU_BTN TMB WHERE TRB.BTN_ID = TMB.RID AND TMB.MENU_TEMPLATE_ID = :rid )";
			String tubSql = "DELETE FROM TS_USER_BTN TUB WHERE " +
					"EXISTS(SELECT 1 FROM TS_MENU_BTN TMB WHERE TUB.BTN_ID = TMB.RID AND TMB.MENU_TEMPLATE_ID = :rid )";
			String tmbSql = "DELETE FROM TS_MENU_BTN TMB WHERE TMB.MENU_TEMPLATE_ID = :rid ";
			em.createNativeQuery(trbSql).setParameter("rid", rid).executeUpdate();
			em.createNativeQuery(tubSql).setParameter("rid", rid).executeUpdate();
			em.createNativeQuery(tmbSql).setParameter("rid", rid).executeUpdate();
			this.delete(TsMenu.class, rid);
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			msg = "该数据已被引用，不允许删除！";
		}
		return msg;
	}

	/**
	 * 查询是否存在重复菜单编码
	 * 
	 * @param rid
	 * @param oldMenu
	 * @param newMenu
	 * @return 存在返回false
	 */
	@Transactional(readOnly = true)
	public boolean selMenuEn(Integer rid, String oldMenu, String newMenu) {
		if (null == rid) {
			// 添加查询
			StringBuffer hql = new StringBuffer("select t from TsMenu t where t.menuEn=:newMenu");
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("newMenu",newMenu);
			List list = this.findDataByHqlNoPage(hql.toString(),paramMap);
			if (null != list && list.size() > 0) {
				return false;
			}
		} else {
			// 修改查询
			StringBuffer hql = new StringBuffer("select t from TsMenu t where t.menuEn=:newMenu")
					.append(" and t.menuEn<> :oldMenu ");
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("newMenu",newMenu);
			paramMap.put("oldMenu",oldMenu);
			List list = this.findDataByHqlNoPage(hql.toString(),paramMap);
			if (null != list && list.size() > 0) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 获取所有的系统单位
	 * 
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TsUnit> selAllUnitList() {
		String hql = "select t from TsUnit t where t.ifReveal=1 order by t.unitCode";
		return em.createQuery(hql).getResultList();
	}

	/**
	 * 根据科室Rid查询信息
	 * 
	 * @param modRid
	 *            主键id
	 * @param unitRid
	 *            单位id
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TsOffice> selAllOfficeList(Integer modRid, Integer unitRid) {
		StringBuffer hql = new StringBuffer();
		hql.append("select t from TsOffice t where t.ifReveal=1");
		if (null != modRid) {
			hql.append(" and t.rid <>").append(modRid);
		}
		if (null != unitRid) {
			hql.append(" and t.tsUnit.rid=").append(unitRid);
		}
		hql.append(" order by t.num");
		// hql.append(" order by t.tsUnit.unitcode,t.num");
		return em.createQuery(hql.toString()).getResultList();

	}

	/**
	 * 取消归并功能(科室)
	 * 
	 * @param tsDeptMdref
	 *            关系表
	 */
	public void cancleRefOffice(TsDeptMdref tsDeptMdref) {
		TsOffice oldOffice = tsDeptMdref.getTsOfficeOld();
		// 删除关系表
		delete(find(TsDeptMdref.class, tsDeptMdref.getRid()));
		// 修改状态（原地区）为1
		oldOffice.setIfReveal((short) 1);
		this.update(oldOffice);
	}

	/**
	 * 科室归并逻辑
	 * 
	 * @param officeRid
	 *            原科室rid
	 * @param targetOfficeRid
	 *            现科室rid
	 * @param createManId
	 *            创建人id
	 */
	public void saveRefOffice(Integer officeRid, Integer targetOfficeRid, Integer createManId) {
		// 已归并 原有的科室
		changeStateOffice(officeRid, (short) 2);
		// 新建关系记录
		TsDeptMdref tsDeptMdref = new TsDeptMdref();
		tsDeptMdref.setCreateDate(new Date());
		tsDeptMdref.setCreateManid(createManId);
		tsDeptMdref.setTsOfficeOld(findOffice(officeRid));
		tsDeptMdref.setTsOfficeNew(findOffice(targetOfficeRid));
		save(tsDeptMdref);
		// 将原科室下的用户统一归并到新科室下TB_SYS_EMP
		StringBuffer updUnitStr = new StringBuffer("update TbSysEmp n set n.tsOffice.rid=").append(targetOfficeRid)
				.append(" where n.rid in ( select t.rid from TbSysEmp t where t.tsOffice.rid=").append(officeRid)
				.append(")");
		em.createQuery(updUnitStr.toString()).executeUpdate();
	}

	/**
	 * 根据主键查询科室
	 * 
	 * @param rid
	 *            科室rid
	 * @return 科室对象
	 */
	@Transactional(readOnly = true)
	public TsOffice findOffice(Integer rid) {
		return (TsOffice) this.find(TsOffice.class, rid);
	}

	/**
	 * 根据主键修改科室状态
	 * 
	 * @param rid
	 *            主键
	 * @param ifReveal
	 *            状态
	 */
	public void changeStateOffice(Integer rid, Short ifReveal) {
		TsOffice tsOffice = findOffice(rid);
		tsOffice.setIfReveal(ifReveal);
		this.update(tsOffice);
	}

	/**
	 * 删除科室，如果科室表的子表中没有科室的记录， 则允许删除，否则不允许删除
	 * 
	 * @param rid
	 *            科室rid
	 * @return 返回信息，如果删除成功就返回null
	 */
	public String deleteOffice(Integer rid) {
		String msg = null;
		TsOffice tsOffice = findOffice(rid);
		if (null != tsOffice) {
			try {
				this.delete(TsOffice.class, rid);
			} catch (Exception e) {
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				msg = "该科室已被引用，不允许删除！";
			}
		} else {
			msg = "该记录已经被删除，请刷新页面！";
		}
		return msg;
	}

	/**
	 * 添加或修改科室
	 * 
	 * @param office
	 *            科室对象
	 */

	public void saveOrUpdateOffice(TsOffice office) {
		if (null != office.getRid()) {
			// 修改
			this.update(office);
		} else {
			// 保存
			this.save(office);
		}
	}

	/**
	 * 根据单位查询人员
	 * 
	 * @param unit
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TbSysEmp> selSysEmpList(Integer unit) {
		String hql = "select t from TbSysEmp t where t.isLeader=1 and t.onduty = 1 and t.tsOffice.tsUnit.rid=" + unit;
		return em.createQuery(hql).getResultList();
	}

	/**
	 * 查询是否存在重复科室编码
	 * 
	 * @param rid
	 * @param oldCode
	 * @param newCode
	 * @param unitid
	 *            单位id
	 * @return 存在返回false
	 */
	@Transactional(readOnly = true)
	public boolean selOfficeCode(Integer rid, String oldCode, String newCode, Integer unitid) {
		if (null == rid) {
			// 添加查询
			StringBuffer hql = new StringBuffer("select t from TsOffice t where t.officecode='").append(newCode)
					.append("' and t.tsUnit.rid=").append(unitid);
			List list = em.createQuery(hql.toString()).getResultList();
			if (null != list && list.size() > 0) {
				return false;
			}
		} else {
			// 修改查询
			StringBuffer hql = new StringBuffer("select t from TsOffice t where t.officecode='").append(newCode)
					.append("' and t.officecode<>'").append(oldCode).append("' and t.tsUnit.rid=").append(unitid);
			List list = em.createQuery(hql.toString()).getResultList();
			if (null != list && list.size() > 0) {
				return false;
			}
		}
		return true;
	}

	@Transactional(readOnly = true)
	public List<TsOffice> findOfficeList(Integer unitId) {
		List<TsOffice> rtnList = new ArrayList<TsOffice>(0);
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT A.OFFICENAME, A.RID, A.UP_ID  ");
		sb.append(" FROM TS_OFFICE A  ");
		sb.append(" WHERE A.IF_REVEAL = '1' ");
		if (null != unitId) {
			sb.append(" AND A.UNIT_RID = '").append(unitId).append("' ");
		}
		sb.append(" START WITH A.UP_ID IS NULL ");
		sb.append(" CONNECT BY PRIOR A.RID = A.UP_ID ");
		sb.append(" ORDER SIBLINGS BY A.RID ");

		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			for (Object[] o : list) {
				TsOffice t = new TsOffice();
				t.setOfficename(String.valueOf(o[0]));
				t.setRid(Integer.valueOf(o[1].toString()));
				if (null != o[2]) {
					t.setParentOffice(new TsOffice(Integer.valueOf(o[2].toString())));
				}
				rtnList.add(t);
			}
		}
		return rtnList;
	}

	@Transactional(readOnly = true)
	public List<TsUserInfo> findUsers(Integer unitId, Map<String, String> conMap, String filterIds) {
		List<TsUserInfo> rtnList = new ArrayList<TsUserInfo>();
		StringBuilder sb = new StringBuilder(
				" SELECT T1.RID, T1.USERNAME, C.OFFICENAME, C.OFFID,C.DUTY, C.IS_LEADER, C.MB_NUM ");
		sb.append(" FROM TS_USER_INFO T1 ");
		// 2015-3-31 xt 新增过滤兼职科室
		sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFID,A.DUTY, A.IS_LEADER,");
		sb.append(" A.MB_NUM FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
		sb.append(" UNION ALL");
		sb.append(" SELECT A.RID,C.OFFICENAME,C.RID AS OFFID,B.DUTY_ID AS DUTY, B.IS_LEADER,A.MB_NUM FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
		sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID ) C ON C.RID = T1.EMP_ID ");
		sb.append(" WHERE T1.IF_REVEAL = '1' ");
		if (null != unitId) {
			sb.append(" AND T3.UNIT_RID = '").append(unitId).append("' ");
		}
		if (null != conMap && conMap.size() > 0) {
			// 科室IDS
			String officeIds = conMap.get("officeId");
			// 职务IDS
			String dutyIds = conMap.get("dutyId");
			// 是否领导
			String ifLeader = conMap.get("ifLeader");
			if (StringUtils.isNotBlank(officeIds)) {
				sb.append(" AND C.OFFID IN (  SELECT T.RID FROM TS_OFFICE T START WITH T.RID='").append(officeIds)
						.append("' CONNECT BY PRIOR T.RID = T.UP_ID) ");
			}
			if (StringUtils.isNotBlank(dutyIds)) {
				sb.append(" AND C.DUTY = '").append(dutyIds).append("' ");
			}
			if (StringUtils.isNotBlank(ifLeader)) {
				sb.append(" AND C.IS_LEADER ='").append(ifLeader).append("' ");
			}
		}
		if (StringUtils.isNotBlank(filterIds)) {
			sb.append(" AND T1.RID NOT IN (").append(filterIds).append(") ");
		}
		sb.append(" ORDER BY T1.USERNAME ");
		List<Object[]> temList = em.createNativeQuery(sb.toString()).getResultList();
		if (null != temList && temList.size() > 0) {
			for (Object[] o : temList) {
				TsUserInfo tui = new TsUserInfo(Integer.valueOf(o[0].toString()));
				tui.setUsername(o[1] == null ? "" : o[1].toString());
				tui.setOfficeName(o[2] == null ? "" : o[2].toString());
				if (null != o[3]) {
					tui.setOfficeId(Integer.valueOf(o[3].toString()));
				}
				if (null != o[4]) {
					tui.setDutyId(Integer.valueOf(o[4].toString()));
				}
				if (null != o[5]) {
					tui.setIfReveal(Short.valueOf(o[5].toString()));
				}
				if (null != o[6]) {
					tui.setMbPhone(o[6].toString());
				}
				rtnList.add(tui);
			}
		}
		return rtnList;
	}

	public String saveMsg(TdMsgMain msgMain, List<TsUserInfo> userList, boolean mobileMsg) {
		msgMain = (TdMsgMain) this.saveObj(msgMain);
		if (null != userList && userList.size() > 0) {
			for (TsUserInfo t : userList) {
				TdMsgSub sub = new TdMsgSub();
				sub.setTdMsgMain(msgMain);
				sub.setTsUserInfo(t);
				this.save(sub);
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public TdMsgMain findMyLatestMsg(Integer userId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TdMsgMain t inner join t.tdMsgSubs t2 where t2.tsUserInfo.rid =");
		sb.append(userId).append(" and t2.acceptState = 0 order by t. publishTime desc ");

		List<TdMsgMain> list = em.createQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	/**
	 * 保存我的寻呼消息
	 */
	public TdMsgMain updateTdInfoMain(TdInfoMain main) {
		if (null != main) {
			// 保存我的寻呼
			main = (TdInfoMain) this.saveObj(main);

			// 保存我的消息主表
			TdMsgMain msgMain = new TdMsgMain();
			msgMain.setTsUserInfo(main.getTsUserInfo());
			msgMain.setTdInfoMain(main);
			msgMain.setMessageType(MessageType.INFO);
			if (StringUtils.isNotBlank(main.getInfoTitle()) && main.getInfoTitle().length() > Constants.MSG_MAX_LEN) {
				msgMain.setInfoTitle(main.getInfoTitle().substring(0, Constants.MSG_MAX_LEN));
			} else {
				msgMain.setInfoTitle(main.getInfoTitle());
			}
			msgMain.setPublishTime(main.getPublishTime());
			msgMain.setNetAdr("/webapp/system/tdMsgView.faces?infoRid=" + main.getRid());
			msgMain.setNetName("寻呼消息");

			// 我的消息子表
			List<TdMsgSub> tdMsgSubs = new LinkedList<TdMsgSub>();
			for (TdInfoSub sub : main.getTdInfoSubs()) {
				TdMsgSub msgSub = new TdMsgSub();
				msgSub.setTsUserInfo(sub.getTsUserInfo());
				msgSub.setTdMsgMain(msgMain);
				msgSub.setAcceptState(0);
				tdMsgSubs.add(msgSub);
			}
			msgMain.setTdMsgSubs(tdMsgSubs);
			return (TdMsgMain) this.saveObj(msgMain);
		}
		return null;
	}

	/**
	 * 获取我的寻呼实体
	 * 
	 * @param rid
	 * @return
	 */
	@Transactional(readOnly = true)
	public TdInfoMain findTdInfoMain(Integer rid) {
		if (null != rid) {
			TdInfoMain t = em.find(TdInfoMain.class, rid);
			if (null != t.getTdInfoAnnexes()) {
				t.getTdInfoAnnexes().size();
			}
			if (null != t.getTdInfoSubs()) {
				t.getTdInfoSubs().size();
			}
			return t;
		}
		return null;
	}

	/**
	 * 查询我的消息list
	 * 
	 * @param title
	 *            查询标题
	 * @param state
	 *            查询状态
	 * @param type
	 *            查询类型
	 * @param userId
	 *            当前登录人id
	 * @return List<TdMsgSub>
	 * <AUTHOR>
	 */
	@Transactional(readOnly = true)
	public List<TdMsgSub> searchTdMsgSubList(String title, String[] state, String[] type, Integer userId) {
		StringBuffer hql = new StringBuffer("");
		hql.append(" select t from TdMsgSub t where ");
		hql.append(" t.tsUserInfo.rid = :userId");

		if (StringUtils.isNotBlank(title)) {
			hql.append(" and t.tdMsgMain.infoTitle like :infoTitle");
		}

		if (null != state && 0 != state.length) {
			String str = "";
			for (String s : state) {
				str += "," + s;
			}
			str = str.substring(1);
			hql.append(" and t.acceptState in (").append(str).append(") ");
		}

		if (null != type && 0 != type.length) {
			String str = "";
			for (String s : type) {
				str += "," + s;
			}
			str = str.substring(1);
			hql.append(" and t.tdMsgMain.messageType in (").append(str).append(")");
		}

		hql.append(" order by t.tdMsgMain.messageType,t.acceptState,t.tdMsgMain.publishTime desc");
		Query query = em.createQuery(hql.toString());
		query.setParameter("userId", userId);
		if (StringUtils.isNotBlank(title)) {
			query.setParameter("infoTitle", "%" + title + "%");
		}
		return query.getResultList();
	}

	/**
	 * 修改消息子表状态
	 * 
	 * @param searchSql
	 *            能查询出要修改数据的sql
	 * @param state
	 *            要修改的状态
	 * @param userId
	 *            当前登录人id
	 * 
	 */
	@SuppressWarnings("rawtypes")
	public void editAllSubState(String searchSql, String state, Integer userId, Map<String, Object> paramMap) {
		if (StringUtils.isNotBlank(searchSql)) {
			StringBuffer sql = new StringBuffer("");
			sql.append(" UPDATE TD_MSG_SUB SET PUBLISH_TIME = sysdate,ACCEPT_STATE = '").append(state).append("' ");
			sql.append(" WHERE RID IN (").append(searchSql).append(") ");
			Query query1 = em.createNativeQuery(sql.toString());
			if (null != paramMap && paramMap.size() > 0) {
				Iterator itor = paramMap.entrySet().iterator();
				while (itor.hasNext()) {
					Map.Entry entry = (Map.Entry) itor.next();
					query1.setParameter(String.valueOf(entry.getKey()), entry.getValue());
				}
			}
			query1.executeUpdate();

			sql = new StringBuffer("");
			sql.append(" UPDATE  TD_INFO_SUB A SET A.PUBLISH_TIME = SYSDATE,A.ACCEPT_STATE = 1 ");
			sql.append(" WHERE A.MAIN_ID IN ( ");
			sql.append(" SELECT T1.INFO_ID FROM TD_MSG_SUB T  ");
			sql.append(" INNER JOIN TD_MSG_MAIN T1 ON T.MAIN_ID = T1.RID ");
			sql.append(" WHERE T.RID IN (").append(searchSql).append(") ) AND A.PUBLISH_MAN = ").append(userId);
			em.createNativeQuery(sql.toString());
			Query query2 = em.createNativeQuery(sql.toString());
			if (null != paramMap && paramMap.size() > 0) {
				Iterator itor = paramMap.entrySet().iterator();
				while (itor.hasNext()) {
					Map.Entry entry = (Map.Entry) itor.next();
					query2.setParameter(String.valueOf(entry.getKey()), entry.getValue());
				}
			}
			query2.executeUpdate();
		}

	}

	/**
	 * 修改消息子表状态
	 * 
	 * @param ids
	 *            要修改数据的ids
	 * @param state
	 *            要修改的状态
	 * @param userId
	 *            当前登录人id
	 * <AUTHOR>
	 */
	public void editSubState(String ids, String state, Integer userId) {
		StringBuffer sql = new StringBuffer("");
		sql.append(" UPDATE TD_MSG_SUB SET PUBLISH_TIME = sysdate,ACCEPT_STATE = '").append(state).append("' ");
		sql.append(" WHERE RID IN (").append(ids).append(") ");
		em.createNativeQuery(sql.toString()).executeUpdate();

		sql = new StringBuffer("");
		sql.append(" UPDATE  TD_INFO_SUB A SET A.PUBLISH_TIME = SYSDATE,A.ACCEPT_STATE = 1 ");
		sql.append(" WHERE A.MAIN_ID IN ( ");
		sql.append(" SELECT T1.INFO_ID FROM TD_MSG_SUB T  ");
		sql.append(" INNER JOIN TD_MSG_MAIN T1 ON T.MAIN_ID = T1.RID ");
		sql.append(" WHERE T.RID IN (").append(ids).append(") ) AND A.PUBLISH_MAN = ").append(userId);
		em.createNativeQuery(sql.toString()).executeUpdate();
	}

	/**
	 * 修改代办任务消息状态为已办
	 */
	public void editTodoState(String ids, Integer state) {
		StringBuilder sb = new StringBuilder();
		sb.append("update TD_MSG_MAIN set TODO_STATE=").append(state).append(" where rid in (");
		sb.append("select MAIN_ID from TD_MSG_SUB where rid=");
		sb.append(ids).append(")");
		em.createNativeQuery(sb.toString()).executeUpdate();
	}

	/**
	 * 初始化本单位的科室与人员信息
	 * 
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<Object[]> initOfficeUserInfo(Boolean ifAdmin, String zoneCode) {
		StringBuffer sql = new StringBuffer("");
		// 2015-3-31 xt 新增过滤兼职科室
		sql.append(" SELECT T.RID,T.USERNAME,C.OFFRID,C.OFFICENAME");
		sql.append(" ,T3.RID AS UNITRID,T3.UNIT_SIMPNAME FROM TS_USER_INFO T ");
		sql.append(" INNER JOIN (SELECT A.RID,B.RID AS OFFRID,B.OFFICENAME,B.UNIT_RID AS UNITRID FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
		sql.append(" UNION ALL");
		sql.append(" SELECT A.RID,C.RID AS OFFRID,C.OFFICENAME,C.UNIT_RID AS UNITRID FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
		sql.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID ) C ON C.RID = T.EMP_ID");
		sql.append(" INNER JOIN (SELECT T0.RID, T0.UNITNAME,T1.ZONE_GB,T0.UNIT_CODE,T0.UNIT_SIMPNAME FROM TS_UNIT T0");
		if (!ifAdmin) {
			sql.append(" LEFT JOIN  TS_ZONE T1 ON T0.ZONE_ID=T1.RID WHERE T1.IF_REVEAL = 1 AND T1.ZONE_GB LIKE '")
					.append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		} else {
			sql.append(" LEFT JOIN  TS_ZONE T1 ON T0.ZONE_ID=T1.RID WHERE T1.IF_REVEAL = 1 ");
		}
		sql.append(" AND T0.IF_REVEAL=1 ) T3 ON C.UNITRID = T3.RID ");
		sql.append(" WHERE T.IF_REVEAL = 1 ORDER BY T3.ZONE_GB,T3.UNIT_CODE,C.OFFRID,C.RID ");
		return em.createNativeQuery(sql.toString()).getResultList();
	}

	/**
	 * 查找登录人有几条未阅消息，包含委托我的未阅记录
	 * 
	 * @param userRid
	 *            人员id
	 * @return
	 */
	@Transactional(readOnly = true)
	public String countMsgNum(Integer userRid) {
		if (null != userRid) {
			StringBuilder sb = new StringBuilder();
			sb.append(" SELECT E1.USER_ID, LISTAGG(''''||E3.ACT_DEF_ID||'''', ',') WITHIN GROUP (ORDER BY E3.ACT_DEF_ID) AS  ACT_DEF_ID ");
			sb.append(" FROM TD_FLOW_TRUST E1 ");
			sb.append(" INNER JOIN TD_FLOW_TRUST_DEF E2 ON E2.TRUST_ID = E1.RID ");
			sb.append(" INNER JOIN TD_FLOW_DEF E3 ON E2.DEF_ID = E3.RID ");
			sb.append(" WHERE E1.STATE = '1' ");
			sb.append(" AND E1.TRUST_USER_ID = '").append(userRid).append("' ");
			sb.append(" AND SYSDATE BETWEEN E1.BEGIN_TIME AND E1.END_TIME ");
			sb.append(" GROUP BY E1.USER_ID ");
			// 我的委托记录
			List<Object[]> trustList = super.em.createNativeQuery(sb.toString()).getResultList();

			StringBuilder sql = new StringBuilder(" SELECT COUNT(*) FROM (");
			sql.append(" SELECT T.RID FROM TD_MSG_SUB T ");
			sql.append(" WHERE T.PUBLISH_MAN = '").append(userRid).append("' ");
			sql.append(" AND T.ACCEPT_STATE = 0 ");
			if (null != trustList && trustList.size() > 0) {
				for (Object[] o : trustList) {
					sql.append(" UNION ");
					sql.append(" SELECT T1.RID ");
					sql.append(" FROM TD_MSG_SUB T1 ");
					sql.append(" INNER JOIN TS_USER_INFO T2 ON T1.PUBLISH_MAN = T2.RID ");
					sql.append(" INNER JOIN TD_MSG_MAIN T3 ON T1.MAIN_ID = T3.RID ");
					sql.append(" INNER JOIN TS_USER_INFO T4 ON T3.PUBLISH_MAN = T4.RID ");
					sql.append(" INNER JOIN ACT_RU_TASK T5 ON SUBSTR(T3.NET_ADR, INSTR(T3.NET_ADR, '?taskId=')+8) = T5.ID_ ");
					sql.append(" AND T5.PROC_DEF_ID_ IN (").append(o[1]).append(") ");
					sql.append(" WHERE T3.MSG_TYPE = '1' AND T1.ACCEPT_STATE = '0' ");
					sql.append(" AND T2.RID = '").append(o[0]).append("' ");
				}
			}
			sql.append(" ) ");

			List<Object[]> list = em.createNativeQuery(sql.toString()).getResultList();
			if (null != list && list.size() > 0) {
				return String.valueOf(list.get(0));
			} else {
				return "0";
			}
		}
		return "0";
	}

	/**
	 * 查找登录人有几条预警未阅消息
	 * 
	 * @param userRid
	 *            人员id
	 * @return
	 */
	@Transactional(readOnly = true)
	public String countMsgNumOfType(Integer userRid, MessageType mt) {
		if (null != userRid) {
			StringBuffer sql = new StringBuffer("");
			sql.append(" SELECT COUNT(T.RID) FROM TD_MSG_SUB T ");
			if (null != mt) {
				sql.append(" INNER JOIN TD_MSG_MAIN T2 ON T.MAIN_ID = T2.RID AND T2.MSG_TYPE ='");
				sql.append(mt.getTypeNo()).append("' ");
			}
			sql.append(" WHERE T.PUBLISH_MAN = '").append(userRid).append("' ");
			sql.append(" AND T.ACCEPT_STATE = 0 ");
			List<Object[]> list = em.createNativeQuery(sql.toString()).getResultList();
			if (null != list && list.size() > 0) {
				return String.valueOf(list.get(0));
			} else {
				return "0";
			}
		}
		return "0";
	}

	public void updateMsgNumOfType(Integer userRid, MessageType mt) {
		StringBuffer sql = new StringBuffer("");
		sql.append(" UPDATE TD_MSG_SUB T1 ");
		sql.append(" SET T1.ACCEPT_STATE = '1' WHERE EXISTS (SELECT T2.RID FROM TD_MSG_MAIN T2 WHERE T1.MAIN_ID = T2.RID ");
		sql.append(" AND T1.PUBLISH_MAN = '").append(userRid);
		if (null != mt) {
			sql.append("' AND T2.MSG_TYPE = '").append(mt.getTypeNo()).append("') ");
		} else {
			sql.append("') ");
		}
		super.em.createNativeQuery(sql.toString()).executeUpdate();
	}

	@Transactional(readOnly = true)
	public List<TsUserInfo> findUserableUserList(Integer officeId, String username, Integer unitId) {
		StringBuilder sb = new StringBuilder();
		// 科室Id
		String officeIds = null;
		// 如果科室不为空，则加载其对应的所有子类科室Id
		if (null != officeId) {
			sb.append("SELECT LISTAGG( T.RID,',') WITHIN GROUP (ORDER BY T.RID) AS RIDS FROM TS_OFFICE T START WITH T.RID='");
			sb.append(officeId).append("' CONNECT BY PRIOR T.RID = T.UP_ID");
			List resultList = this.em.createNativeQuery(sb.toString()).getResultList();
			if (null != resultList && resultList.size() > 0) {
				officeIds = resultList.get(0) == null ? null : resultList.get(0).toString();
			}
		}
		sb = new StringBuilder();
		sb.append(" SELECT D.RID, D.USERNAME, D.OFFICENAMES,D.OFFICEIDS");
		sb.append(" FROM (SELECT T1.RID, T1.USERNAME, LISTAGG(C.OFFICENAME,',') WITHIN GROUP(ORDER BY DECODE(C.ZKS, 1, -1, C.NUM)) AS OFFICENAMES");
		sb.append(" , ','||LISTAGG(C.OFFICEID,',') WITHIN GROUP(ORDER BY C.OFFICEID)||',' AS OFFICEIDS ");
		sb.append(" ,SUM(DECODE(C.ZKS, 1, C.NUM, NULL)) AS ZKSNUM,C.EMPNUM FROM TS_USER_INFO T1 ");
		// 2015-3-31 xt 新增过滤兼职科室
		sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
		sb.append(" UNION ALL");
		sb.append(" SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
		sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
		sb.append(" WHERE 1=1 AND T1.IF_REVEAL='1' AND T1.UNIT_RID = '").append(unitId).append("' ");
		//TODO
		if (StringUtils.isNotBlank(username)) {
			sb.append(" AND T1.USERNAME LIKE:username");
		}
		sb.append(" GROUP BY T1.RID, T1.USERNAME ,C.EMPNUM ");
		sb.append(" ) D ");

		if (StringUtils.isNotBlank(officeIds)) {
			StringBuilder officePiece = new StringBuilder();
			String[] idArr = officeIds.split(",");
			for (String offid : idArr) {
				officePiece.append(" OR D.OFFICEIDS LIKE '%,").append(offid).append(",%'");
			}
			sb.append(" WHERE (").append(officePiece.substring(3)).append(")");
		}
		sb.append(" ORDER BY D.ZKSNUM, D.EMPNUM ");
		Query query = em.createNativeQuery(sb.toString());
		if (StringUtils.isNotBlank(username)) {
			query.setParameter("username", "%" + username + "%");
		}
		List<Object[]> list = query.getResultList();
		List<TsUserInfo> rtnList = new ArrayList<TsUserInfo>();
		if (null != list && list.size() > 0) {
			for (Object[] o : list) {
				TsUserInfo t = new TsUserInfo(Integer.valueOf(o[0].toString()));
				t.setUsername(o[1] == null ? "" : o[1].toString());
				t.setOfficeName(o[2] == null ? "" : o[2].toString());
				t.setOfficeIds(o[3] == null ? "" : o[3].toString());

				// t.setOfficeId(Integer.valueOf(o[3].toString()));
				rtnList.add(t);
			}
		}
		return rtnList;
	}

	@Transactional(readOnly = true)
	public List<TsCodeType> findCodeTypeList(boolean ifAdmin, Integer userId) {
		return em.createQuery(" select t from TsCodeType t order by t.paramType, t.codeTypeName").getResultList();
	}

	/****************************** 用户组 ************************************/

	public String saveOrUpdateGroup(TsGroup group, Integer unitId) {
		String msg;
		if (selectedTsGroupByName(group.getRid(), group.getGroupName(), unitId)) {
			msg = "组名称不允许重复！";
			return msg;
		} else {
			if (group.getRid() != null) {
				this.update(group);
			} else {
				this.save(group);
			}
			return null;
		}
	}

	@Transactional(readOnly = true)
	public boolean selectedTsGroupByName(Integer rid, String name, Integer unitId) {
		StringBuilder sb = new StringBuilder("select t from TsGroup t where t.groupName = '");
		sb.append(name).append("'");
		sb.append(" and t.tsUnit.rid =").append(unitId);
		if (rid != null) {
			sb.append(" and t.rid <> ").append(rid);
		}
		List list = this.em.createQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return true;
		} else {
			return false;
		}
	}

	public String deleteGroup(Integer rid) {
		String msg = null;
		TsGroup tsGroup = (TsGroup) this.find(TsGroup.class, rid);
		if (null != tsGroup) {
			try {
				StringBuilder sb = new StringBuilder("DELETE TS_GROUP WHERE RID = ");
				sb.append(rid);
				em.createNativeQuery(sb.toString()).executeUpdate();
			} catch (Exception e) {
				msg = "该记录已被引用，不允许删除！";
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			}
		} else {
			msg = "该记录已经被删除";
		}
		return msg;
	}

	public void changeTsGroupState(Integer rid, short value) {
		StringBuffer sql = new StringBuffer("");
		sql.append(" UPDATE TS_GROUP T SET T.IF_REVEAL = ").append(value);
		sql.append(" WHERE T.RID = ").append(rid);
		em.createNativeQuery(sql.toString()).executeUpdate();
	}

	@Transactional(readOnly = true)
	public List<TsUserInfo> findTargetList(Integer rid) {
		List<TsUserInfo> targetList = new ArrayList<TsUserInfo>();
		// 2015-4-3 xt 新增过滤兼职科室
		StringBuilder sb = new StringBuilder("SELECT B.RID,B.USERNAME, B.OFFICENAMES");
		sb.append(" FROM (SELECT A.RID,A.USERNAME, LISTAGG(A.OFFICENAME, ',') WITHIN GROUP(ORDER BY DECODE(A.ZKS, 1, -1, A.NUM)) AS OFFICENAMES,");
		sb.append(" ',' || LISTAGG(A.OFFICEID, ',') WITHIN GROUP(ORDER BY A.OFFICEID) || ',' AS OFFICEIDS");
		sb.append(" ,SUM(DECODE(A.ZKS, 1, A.NUM, NULL)) AS ZKSNUM,A.EMPNUM");
		sb.append(" FROM (SELECT T1.RID,T1.USERNAME,C.OFFICENAME,C.OFFICEID,C.EMPNUM,C.ZKS,C.NUM FROM TS_USER_INFO T1 ");
		sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID ,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
		sb.append(" UNION ALL");
		sb.append("  SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
		sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID  ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
		sb.append(" WHERE T1.IF_REVEAL = 1 AND T1.RID IN ");
		sb.append("(SELECT T.USER_INFO_ID FROM TS_USER_GROUP T WHERE T.GROUP_ID = ");
		sb.append(rid);
		sb.append(")) A GROUP BY A.RID,A.USERNAME ,A.EMPNUM ) B ");
		sb.append(" ORDER BY B.ZKSNUM,B.EMPNUM");
		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if (list != null && list.size() > 0) {
			for (Object[] obj : list) {
				TsUserInfo tsUserInfo = new TsUserInfo(Integer.valueOf(obj[0].toString()));
				tsUserInfo.setUsername(obj[1] == null ? "" : obj[1].toString());
				tsUserInfo.setOfficeName(obj[2] == null ? "" : obj[2].toString());
				targetList.add(tsUserInfo);
			}
		}
		return targetList;
	}

	@Transactional(readOnly = true)
	public List<TsUserInfo> findAllUsers(Integer unitId, Integer officeId) {
		List<TsUserInfo> userList = new ArrayList<TsUserInfo>();
		StringBuilder sb = new StringBuilder("SELECT B.RID,B.USERNAME, B.OFFICENAMES");
		sb.append(" FROM (SELECT A.RID,A.USERNAME, LISTAGG(A.OFFICENAME, ',') WITHIN GROUP(ORDER BY DECODE(A.ZKS, 1, -1, A.NUM)) AS OFFICENAMES,");
		sb.append(" ',' || LISTAGG(A.OFFICEID, ',') WITHIN GROUP(ORDER BY A.OFFICEID) || ',' AS OFFICEIDS");

		sb.append(" ,SUM(DECODE(A.ZKS, 1, A.NUM, NULL)) AS ZKSNUM,A.EMPNUM");
		sb.append(" FROM (SELECT T1.RID,T1.USERNAME,C.OFFICENAME,C.OFFICEID,C.EMPNUM,C.ZKS,C.NUM FROM TS_USER_INFO T1 ");
		// 2015-3-31 xt 新增过滤兼职科室
		sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID ,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
		sb.append(" UNION ALL");
		sb.append("  SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
		sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID  ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
		sb.append(" WHERE T1.IF_REVEAL = 1 AND T1.UNIT_RID = ").append(unitId);
		sb.append(") A GROUP BY A.RID,A.USERNAME ,A.EMPNUM ) B ");// A ORDER BY
																	// A.OFFICENAME,A.USERNAME
		if (officeId != null) {
			sb.append(" WHERE B.OFFICEIDS LIKE '%,").append(officeId).append(",%'");
		}
		sb.append(" ORDER BY B.ZKSNUM,B.EMPNUM");
		List<Object[]> resultList = em.createNativeQuery(sb.toString()).getResultList();
		if (resultList != null && resultList.size() > 0) {
			for (Object[] obj : resultList) {
				TsUserInfo tsUserInfo = new TsUserInfo(Integer.valueOf(obj[0].toString()));
				tsUserInfo.setUsername(obj[1] == null ? "" : obj[1].toString());
				tsUserInfo.setOfficeName(obj[2] == null ? "" : obj[2].toString());
				userList.add(tsUserInfo);
			}
		}
		return userList;
	}

	public String updateTsUserGroup(List<TsUserGroup> tsUserGroupList, Integer groupId) {
		String msg = deleteUserGroup(groupId);
		if (msg == null) {
			for (TsUserGroup t : tsUserGroupList) {
				this.save(t);
			}
			return null;
		} else {
			return msg;
		}
	}

	public String deleteUserGroup(Integer groupId) {
		String msg = null;
		try {
			StringBuilder sb = new StringBuilder("DELETE TS_USER_GROUP WHERE GROUP_ID = ");
			sb.append(groupId);
			em.createNativeQuery(sb.toString()).executeUpdate();
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			msg = "删除失败！";
		}
		return msg;
	}

	/***********************************************************************/

	/**
	 * 批量删除消息
	 * 
	 * @param ids
	 *            删除的ids
	 */
	public void deleteMsgPl(String ids) {
		StringBuffer sql = new StringBuffer("");
		sql.append(" DELETE TD_MSG_SUB WHERE MAIN_ID IN (").append(ids).append(") ");
		em.createNativeQuery(sql.toString()).executeUpdate();
		sql = new StringBuffer("");
		sql.append(" DELETE TD_MSG_MAIN WHERE RID IN (").append(ids).append(") ");
		em.createNativeQuery(sql.toString()).executeUpdate();
	}

	@Transactional(readOnly = true)
	public List findSelectUsersList(Integer unitId) {
		return this.findSelectUsersListIfLeader(unitId, false);
	}

	@Transactional(readOnly = true)
	public List findSelectUsersList(Integer unitId, boolean ifLeader) {
		return this.findSelectUsersListIfLeader(unitId, ifLeader);
	}

	@Transactional(readOnly = true)
	private List findSelectUsersListIfLeader(Integer unitId, boolean ifLeader) {

		// 返回的结果
		List rtnList = new ArrayList(2);

		Map<String, TsOffice> officeMap = new HashMap<String, TsOffice>();
		Map<String, TsGroup> groupMap = new HashMap<String, TsGroup>();

		Map<TsOffice, List<TsUserInfo>> userOfficeMap = new LinkedHashMap<TsOffice, List<TsUserInfo>>();
		Map<TsGroup, List<TsUserInfo>> userGroupMap = new LinkedHashMap<TsGroup, List<TsUserInfo>>();

		// 查询科室的人员
		StringBuilder sb = new StringBuilder(" SELECT T1.USERNAME, T1.RID AS USERID, C.OFFICENAME, C.OFFID ");
		sb.append(" FROM TS_USER_INFO T1 ");
		// 2015-3-31 xt 新增过滤兼职科室
		sb.append(" INNER JOIN (SELECT A.RID,B.OFFICENAME,B.RID AS OFFID,B.UNIT_RID,B.OFFICECODE,B.IF_REVEAL ,B.NUM, A.NUM EMPNUM FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
		if (ifLeader) {
			sb.append(" AND A.IS_LEADER = 1");
		}
		sb.append(" UNION ALL");
		sb.append(" SELECT A.RID,C.OFFICENAME,C.RID AS OFFID,C.UNIT_RID,C.OFFICECODE,C.IF_REVEAL,C.NUM, A.NUM EMPNUM FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
		sb.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID");
		if (ifLeader) {
			sb.append(" AND B.IS_LEADER = 1");
		}
		sb.append(" ) C ON C.RID = T1.EMP_ID");
		sb.append(" WHERE T1.IF_REVEAL = '1' AND C.IF_REVEAL = '1' AND  C.UNIT_RID = '").append(unitId).append("' ");
		sb.append(" ORDER BY C.NUM, C.EMPNUM");

		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			for (Object[] obs : list) {
				String offId = obs[3].toString();
				TsOffice to = officeMap.get(offId);
				if (null == to) {
					to = new TsOffice(Integer.valueOf(offId));
					to.setOfficename(obs[2].toString());
					officeMap.put(offId, to);
				}

				TsUserInfo user = new TsUserInfo(Integer.valueOf(obs[1].toString()));
				user.setUsername(obs[0].toString());

				List<TsUserInfo> userList = userOfficeMap.get(to);
				if (null == userList) {
					userList = new ArrayList<TsUserInfo>();
				}
				userList.add(user);
				userOfficeMap.put(to, userList);
			}
		}

		// 查询组的人员
		sb = new StringBuilder("  SELECT T1.USERNAME, T1.RID AS USERID, T3.GROUP_NAME, T3.RID AS GID ");
		sb.append(" FROM TS_USER_INFO T1 ");
		sb.append("  INNER JOIN TS_USER_GROUP T2 ON T2.USER_INFO_ID = T1.RID ");
		sb.append("  INNER JOIN TS_GROUP T3 ON T3.RID = T2.GROUP_ID ");
		sb.append(" WHERE T1.IF_REVEAL = '1' AND T3.IF_REVEAL = '1' AND T1.UNIT_RID = '").append(unitId).append("' ");
		sb.append(" ORDER BY T3.XH, T1.USER_NO ");

		list = em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			for (Object[] obs : list) {
				String groupId = obs[3].toString();
				TsGroup group = groupMap.get(groupId);
				if (null == group) {
					group = new TsGroup();
					group.setRid(Integer.valueOf(groupId));
					group.setGroupName(obs[2].toString());
					groupMap.put(groupId, group);
				}

				TsUserInfo user = new TsUserInfo(Integer.valueOf(obs[1].toString()));
				user.setUsername(obs[0].toString());

				List<TsUserInfo> userList = userGroupMap.get(group);
				if (null == userList) {
					userList = new ArrayList<TsUserInfo>();
				}
				userList.add(user);
				userGroupMap.put(group, userList);
			}
		}

		rtnList.add(userOfficeMap);
		rtnList.add(userGroupMap);

		return rtnList;
	}

	@Transactional(readOnly = true)
	public TdMsgSub findMsgSub(Integer rid) {
		return super.em.find(TdMsgSub.class, rid);
	}

	@Transactional(readOnly = true)
	public List<TsQuartz> findQuartzList(String descr, String systemParam, String allSystemParam) {
		StringBuilder sb = new StringBuilder();
		Map<String, Object> extractParamMap = new HashMap<>(16);
		sb.append(" select t from TsQuartz t where 1=1 ");
		if (StringUtils.isNotBlank(systemParam)) {
			sb.append(" and t.paramType ='").append(systemParam).append("' ");
		}
		if (StringUtils.isNotBlank(descr)) {
			sb.append(" and t.taskDescr like :taskDescr escape '\\\' ");
			extractParamMap.put("taskDescr", "%" +StringUtils.convertBFH(descr).trim() + "%");

		}
		sb.append(" order by t.taskCode ");
		return findDataByHqlNoPage(sb.toString(), extractParamMap);
	}

	public String saveOrUpdateTdTempmetaType(TdTempmetaType tdTempmetaType) {
		StringBuilder sb = new StringBuilder(" select rid from TD_TEMPMETA_TYPE where TEMP_CODE  ='");
		sb.append(tdTempmetaType.getTempCode()).append("' ");
		if (null != tdTempmetaType.getRid()) {
			sb.append(" and rid <> ").append(tdTempmetaType.getRid());
		}
		List<TsUserInfo> list = em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return "编码不允许重复！";
		}
		if (null != tdTempmetaType.getRid()) {
			this.update(tdTempmetaType);
		} else {
			this.save(tdTempmetaType);
		}
		return null;
	}

	public String deleteTdTempmetaType(Integer typeRid) {
		String msg = null;
		TdTempmetaType tdTempmetaType = (TdTempmetaType) super.find(TdTempmetaType.class, typeRid);
		if (null != tdTempmetaType) {
			try {
				StringBuilder sb = new StringBuilder();
				sb.append("delete TD_TEMPMETA_DEFINE where TYPE_ID = ").append(typeRid);
				em.createNativeQuery(sb.toString()).executeUpdate();
				sb = new StringBuilder("DELETE TD_TEMPMETA_TYPE WHERE RID = ");
				sb.append(typeRid);
				em.createNativeQuery(sb.toString()).executeUpdate();
			} catch (Exception e) {
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				msg = "该模板类型已被引用，不能删除！";
			}
		} else {
			msg = "该模板类型已经被删除！";
		}
		return msg;
	}

	@Transactional(readOnly = true)
	public List<TdTempmetaDefine> findTdTempmetaByType(Integer type) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TdTempmetaDefine t where t.tdTempmetaType.rid = ").append(type);
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public List<TbTempmetaDefine> findTemplateDefine(SystemType systemType) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TbTempmetaDefine t where t.systemType = ").append(SystemType.COMM);
		sb.append(" or t.systemType = ").append(systemType);
		sb.append(" order by t.systemType,t.codeLevelNo ");
		return em.createQuery(sb.toString()).getResultList();
	}

	public void saveOrUpdateTdTempmetaDefine(TdTempmetaDefine tdTempmetaDefine) {
		if (tdTempmetaDefine.getRid() == null) {
			super.save(tdTempmetaDefine);
		} else {
			super.update(tdTempmetaDefine);
		}
	}

	public String deleteTdTempmetaDefine(Integer defineRid) {
		String msg = null;
		TdTempmetaDefine tdTempmetaDefine = (TdTempmetaDefine) super.find(TdTempmetaDefine.class, defineRid);
		if (null != tdTempmetaDefine) {
			try {
				StringBuilder sb = new StringBuilder();
				sb.append("delete TD_TEMPMETA_DEFINE where rid = ").append(defineRid);
				em.createNativeQuery(sb.toString()).executeUpdate();
			} catch (Exception e) {
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				msg = "该模板已被引用，不能删除！";
			}
		} else {
			msg = "该模板已经被删除！";
		}
		return msg;
	}

	public void updateTdTempmetaDefine(Integer typeId, Integer defineId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" update TD_TEMPMETA_DEFINE set IF_DEFAULT = 0 where TYPE_ID = ").append(typeId);
		em.createNativeQuery(sb.toString()).executeUpdate();
		sb = new StringBuilder();
		sb.append(" update TD_TEMPMETA_DEFINE set IF_DEFAULT = 1,CREATE_DATE=SYSDATE where rid = ").append(defineId);
		em.createNativeQuery(sb.toString()).executeUpdate();
	}

	@Transactional(readOnly = true)
	public List<TbSysEmp> selSysEmpList2(Integer unit, Integer office) {
		List<TbSysEmp> tbSysEmps = new ArrayList<TbSysEmp>();
		StringBuilder sb = new StringBuilder();
		sb.append(" with M as (select tt0.office_id,tt1.rid,tt1.emp_name,tt1.emp_sex,tt0.IS_LEADER,tt1.position,tt1.edu_degree,tt1.politics,tt1.PSN_PROP,tt1.num  from  TS_PARTTIME_INFO tt0  ");
		sb.append("   inner join ts_office to3 on to3.rid = tt0.office_id ");
		sb.append("  inner join tb_sys_emp tt1 on tt1.rid = tt0.emp_id ");
		sb.append("  where to3.if_reveal = 1 ");
		sb.append("  and tt1.ONDUTY =1");
		sb.append("  union all ");
		sb.append("  select tt2.dept_id,tt2.rid,tt2.emp_name,tt2.emp_sex,tt2.IS_LEADER,tt2.position,tt2.edu_degree,tt2.politics,tt2.PSN_PROP,tt2.num from tb_sys_emp tt2 ");
		sb.append("  where tt2.ONDUTY =1");
		sb.append("  )");
		sb.append(" select distinct m.rid,m.emp_name,m.num from M m");
		sb.append("  inner join  ts_office t1 on t1.rid = m.office_id");
		sb.append(" inner join ts_user_info user_info on user_info.emp_id = m.rid and user_info.if_reveal=1 ");
		sb.append("  where t1.unit_rid =  ").append(unit);
		if (office != null) {
			sb.append(" and m.office_id = ").append(office);
		}
		sb.append("  and t1.IF_REVEAL = 1");
		sb.append("  and m.IS_LEADER = 1");
		sb.append("  order by m.num ");
		List<Object[]> objects = this.em.createNativeQuery(sb.toString()).getResultList();
		if (objects != null && objects.size() > 0) {
			for (Object[] objs : objects) {
				TbSysEmp tbSysEmp = new TbSysEmp();
				tbSysEmp.setRid(Integer.valueOf(objs[0].toString()));
				tbSysEmp.setEmpName(objs[1].toString());
				tbSysEmps.add(tbSysEmp);
			}
		}
		return tbSysEmps;
	}

	public void updateQuartz(TsQuartz quartz) {
		super.update(quartz);
	}

	@Transactional(readOnly = true)
	public List<TsUnit> findUnitByZoneCode(String zoneCode) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select new TsUnit(t.rid, t.unitname) from TsUnit t where t.ifReveal='1' ");
		sb.append(" and t.tsZone.ifReveal = 1");
		sb.append(" and t.tsZone.zoneGb like'").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		sb.append(" order by t.unitname ");
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public List<TsUnit> findUnitByZoneCodeAndType(String zoneCode, Integer zoneType) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select new TsUnit(t.rid, t.unitname) from TsUnit t where t.ifReveal='1' ");
		sb.append(" and t.tsZone.ifReveal = 1");
		sb.append(" and t.tsZone.zoneCode like'").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		sb.append(" and t.tsZone.zoneType in(").append(zoneType + "," + (zoneType + 1)).append(")");
		sb.append(" order by t.tsZone.zoneCode ");
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public List<TsQuartz> findAllRunTimeTask() {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TsQuartz t where t.ifReveal = '").append(1).append("'");
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public TsUserInfo findUserByUserNo(String userNo) {
		Query query = super.em.createNamedQuery("TsUserInfo.findByUserNo");
		query.setParameter("userNo", userNo);
		List<TsUserInfo> list = query.getResultList();
		if (null != list && list.size() > 0) {
			for (TsUserInfo t : list) {
				t.getTsUserRoles().size();
			}
			return list.get(0);
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TsUnit> findUnitByZoneCode2(String zoneCode) {
		List<TsUnit> tsUnits = new ArrayList<TsUnit>();
		StringBuilder sb = new StringBuilder();
		sb.append(" select  t.rid,t.unitname ,tz.rid as tz_rid,tz.zone_gb,tz.ZONE_NAME,tz.zone_type from ts_unit t inner join ts_zone tz on tz.rid = t.zone_id ");
		sb.append(" and tz.if_reveal = 1 inner join ts_unit_attr tua on tua.unit_rid = t.rid ");
		sb.append(" and t.if_reveal = 1 inner join  TS_BS_SORT tbs on tbs.rid = tua.attr_id ");
		sb.append(" where tbs.sort_code in ('2002','2006') ");
		sb.append(" and tz.zone_gb like '").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		sb.append(" order by tz.zone_gb,t.unitname ");
		List<Object[]> list = this.em.createNativeQuery(sb.toString()).getResultList();
		if (list != null && list.size() > 0) {
			for (Object[] objs : list) {
				TsUnit tsUnit = new TsUnit();
				tsUnit.setRid(Integer.valueOf(objs[0].toString()));
				tsUnit.setUnitname(objs[1].toString());
				TsZone tsZone = new TsZone(Integer.valueOf(objs[2].toString()), objs[3].toString(), objs[4].toString(),
						Short.valueOf(objs[5].toString()));
				tsUnit.setTsZone(tsZone);
				tsUnits.add(tsUnit);
			}
		}
		return tsUnits;
	}

	@Transactional(readOnly = true)
	public List<TbTempmetaDefine> findTemplateDefine2(SystemType systemType) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TbTempmetaDefine t where 1 =1");
		if (systemType != null)
			sb.append(" and t.systemType = ").append(systemType);
		sb.append(" order by t.systemType,t.codeLevelNo ");
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public Map<Integer, String> findUserOffices(Integer unitId, Integer officeId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t.rid userid,t2.rid empid from TS_USER_INFO t inner join TS_UNIT t1 on t1.rid = t.UNIT_RID ");
		sb.append(" inner join TB_SYS_EMP t2 on t.EMP_ID = t2.rid ");
		sb.append(" inner join TS_OFFICE t3 on t2.DEPT_ID = t3.rid ");
		sb.append(" where 1 = 1");
		if (unitId != null) {
			sb.append(" and t1.rid = ").append(unitId);
		}
		if (officeId != null) {
			sb.append(" and t3.rid = ").append(officeId);
		}
		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		Map<Integer, String> map = new HashMap<Integer, String>();
		if (list != null && list.size() > 0) {
			for (Object[] obj : list) {
				sb = new StringBuilder();
				sb.append(" select t1.officename maoff  from TB_SYS_EMP t inner join TS_OFFICE t1 on t.dept_id = t1.rid");
				sb.append(" where t.rid = '").append(obj[1].toString()).append("'");
				sb.append(" union ");
				sb.append(" select t3.officename suoff from TS_PARTTIME_INFO t2 inner join TS_OFFICE t3 on t2.office_id = t3.rid ");
				sb.append(" where t2.emp_id = '").append(obj[1].toString()).append("'");
				List<Object> list1 = em.createNativeQuery(sb.toString()).getResultList();
				if (list1 != null && list1.size() > 0) {
					sb = new StringBuilder();
					for (Object o : list1) {
						sb.append(",").append(o.toString());
					}
					map.put(Integer.valueOf(obj[0].toString()), sb.toString().substring(1));
				}
			}
		}
		return map;

	}

	/**
	 * 传入码表集合更新码表的序号
	 * 
	 * @param orderList
	 */
	public void updateSimpleCodeNum(List<TsSimpleCode> orderList) {
		if (null != orderList && orderList.size() > 0) {
			StringBuilder sql = new StringBuilder();
			sql.append(" BEGIN");
			for (int i = 0; i < orderList.size(); i++) {
				TsSimpleCode tempCode = orderList.get(i);
				sql.append(" UPDATE TS_SIMPLE_CODE T SET T.NUM = '").append(i + 1).append("' ");
				sql.append(" WHERE T.RID = '").append(tempCode.getRid()).append("';");
			}
			sql.append(" END;");
			this.em.createNativeQuery(sql.toString()).executeUpdate();
		}
	}

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findCodeList(Integer codeTypeId, String levelCode) {
		if (null != codeTypeId && StringUtils.isNotBlank(levelCode)) {
			String[] split = levelCode.split("[.]");
			int levelLen = split.length;
			StringBuilder sql = new StringBuilder();
			sql.append("select t from TsSimpleCode t where t.tsCodeType.rid = ").append(codeTypeId);
			if (levelLen == 1) {
				sql.append(" and t.codeNo = t.codeLevelNo");
			} else {
				StringBuilder upCode = new StringBuilder();
				for (int i = 0; i < split.length - 1; i++) {
					upCode.append(".").append(split[i]);
				}
				sql.append(" and t.codeLevelNo like '").append(upCode.substring(1)).append("%'");
				sql.append(" and instr(t.codeLevelNo,'.',1,").append(levelLen - 1).append(") > 0 ");
				sql.append(" and instr(t.codeLevelNo,'.',1,").append(levelLen).append(") = 0 ");
			}
			sql.append(" order by t.num,t.codeLevelNo");
			List<TsSimpleCode> resultList = this.em.createQuery(sql.toString()).getResultList();
			return resultList;
		}
		return null;
	}

	@Transactional(readOnly = true)
	public boolean countSimpCodeHasChild(TsSimpleCode tsSimpleCode) {
		if (null != tsSimpleCode) {
			StringBuilder sql = new StringBuilder();
			sql.append("SELECT T.RID FROM TS_SIMPLE_CODE T WHERE T.CODE_LEVEL_NO LIKE '")
					.append(tsSimpleCode.getCodeLevelNo()).append(".%'");
			List resultList = this.em.createNativeQuery(sql.toString()).getResultList();
			if (null != resultList && resultList.size() > 0) {
				return true;
			}
		}
		return false;
	}

	@Transactional(readOnly = true)
	public List<TsMenuBtn> findSelectBtnByRole(Integer roleid) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t.tsMenuBtn from TsRoleBtn t where t.tsRole.rid = ").append(roleid);
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public List<TsMenuBtn> findSelectBtnByUser(Integer userid) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t.tsMenuBtn from TsUserBtn t where t.tsUserInfo.rid = ").append(userid);
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public List<TsMenuBtn> findAllMenuButton() {
		String hql = "select t from TsMenuBtn t where t.ifReveal = 1 ";
		return em.createQuery(hql).getResultList();
	}

	public void menuBtnSqMenu(Set<Integer> btnSet, Integer roleId) {
		StringBuilder sb = new StringBuilder(" delete TS_ROLE_BTN t where t.ROLE_ID =");
		sb.append(roleId);
		em.createNativeQuery(sb.toString()).executeUpdate();

		if (null != btnSet && btnSet.size() > 0) {
			for (Integer menuId : btnSet) {
				TsRoleBtn trm = new TsRoleBtn();
				trm.setTsMenuBtn(new TsMenuBtn(menuId));
				trm.setTsRole(new TsRole(roleId));
				this.save(trm);
			}
		}
	}

	public void menuBtnUserMenu(Set<Integer> btnSet, Integer userId) {
		StringBuilder sb = new StringBuilder(" delete TS_USER_BTN t where t.USER_INFO_ID =");
		sb.append(userId);
		em.createNativeQuery(sb.toString()).executeUpdate();

		if (null != btnSet && btnSet.size() > 0) {
			for (Integer menuId : btnSet) {
				TsUserBtn trm = new TsUserBtn();
				trm.setTsMenuBtn(new TsMenuBtn(menuId));
				trm.setTsUserInfo(new TsUserInfo(userId));
				this.save(trm);
			}
		}
	}

	@Transactional(readOnly = true)
	public String findOfficeNameByUserId(String userId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T3.OFFICENAME ");
		sb.append(" FROM TS_USER_INFO T1 ");
		sb.append(" INNER JOIN TB_SYS_EMP T2 ON T1.EMP_ID = T2.RID ");
		sb.append(" INNER JOIN TS_OFFICE T3 ON T2.DEPT_ID = T3.RID ");
		sb.append(" WHERE T1.RID = '").append(userId).append("' ");

		List list = super.em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return list.get(0).toString();
		}
		return "";
	}

	@Transactional(readOnly = true)
	public String findIsLeaderByUserId(String userId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T2.IS_LEADER ");
		sb.append(" FROM TS_USER_INFO T1 ");
		sb.append(" INNER JOIN TB_SYS_EMP T2 ON T1.EMP_ID = T2.RID ");
		sb.append(" WHERE T1.RID = '").append(userId).append("' ");

		List list = super.em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return list.get(0).toString();
		}
		return "";
	}

	@Transactional(readOnly = true)
	public String getDesc() {
		return "系统基础模块服务";
	}

	@Transactional(readOnly = true)
	public String getVersion() {

		return "1.0.0";
	}

	@Transactional(readOnly = true)
	public List<TsUnit> findUnitByZoneIdNew(boolean ifAdmin, String curZoneCode, Integer unitId, String zoneCode,
			String zoneLevel) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select new TsUnit(t.rid, t.unitname) from TsUnit t where t.ifReveal='1' ");
		/**
		 * 选中的地区是当前地区要考虑是否是超管，超管能看到所有单位，普通用户只能看到自己
		 */
		/*if (curZoneCode.equals(zoneCode) && !ifAdmin) {
			sb.append(" and t.rid ='").append(unitId).append("' ");
		} else {*/
		sb.append(" and t.tsZone.zoneCode like'").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		if (StringUtils.isNotBlank(zoneLevel)) {
			sb.append(" and (t.tsZone.zoneType = '").append(zoneLevel).append("' or t.tsZone.zoneType = '");
			sb.append((Integer.parseInt(zoneLevel) + 1)).append("') ");
		}
		sb.append(" order by t.tsZone.zoneCode,t.unitname ");
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public List<TsUnit> findUnitByZoneGbNew(boolean ifAdmin, String curZoneCode, Integer unitId, String zoneGb,
											String zoneLevel) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select new TsUnit(t.rid, t.unitname) from TsUnit t where t.ifReveal='1' ");
		sb.append(" and t.tsZone.zoneGb like'").append(ZoneUtil.zoneSelect(zoneGb)).append("%' ");
		if (StringUtils.isNotBlank(zoneLevel)) {
			sb.append(" and (t.tsZone.zoneType = '").append(zoneLevel).append("' or t.tsZone.zoneType = '");
			sb.append((Integer.parseInt(zoneLevel) + 1)).append("') ");
		}
		sb.append(" order by t.tsZone.zoneCode,t.unitname ");
		return em.createQuery(sb.toString()).getResultList();
	}

	/***
	 * <p>方法描述: 查询注册服务机构信息</p>
	 *
	 * @MethodAuthor mxp,2018/12/18,findSrvorgByZoneCode
	 */
	@Transactional(readOnly = true)
	public List<Object[]> findSrvorgByZoneCode(boolean ifAdmin, boolean ifJk, Integer unitId, String zoneCode) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t.UNIT_CODE,t.UNIT_NAME from TB_TJ_SRVORG t" +
				" left join ts_zone t1 on t1.rid = t.zone_id" +
				" LEFT JOIN ts_unit t2 on t2.rid = t.REG_ORGID");
		if(ifAdmin && StringUtils.isBlank(zoneCode)){
        }else if(ifAdmin && StringUtils.isNotBlank(zoneCode)){
            sb.append(" where t1.zone_code like '").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
        }else if(ifJk){//疾控中心看本级及下级
			sb.append(" where t1.zone_code like '").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		}else{//普通用户只能看到自己单位
			sb.append(" where t2.rid ='").append(unitId).append("' ");
		}
		sb.append(" order by t1.ZONE_CODE, t.UNIT_CODE ");
		return em.createNativeQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public List<TsUnit> findUnitByZoneIdSortCode(boolean ifAdmin, String curZoneCode, Integer unitId, String zoneCode,
			String zoneLevel, String[] sortCodes) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select new TsUnit(t.rid, t.unitname) from TsUnit t where t.ifReveal='1' ");
		if (sortCodes != null && sortCodes.length > 0) {
			sb.append(" and exists ( select 1 from TsUnitAttr tua where tua.tsUnit.rid=t.rid and tua.tsBsSort.sortCode in ( ");
			for (int i = 0; i < sortCodes.length; i++) {
				sb.append("'" + sortCodes[i] + "'");
				if (i != (sortCodes.length - 1)) {
					sb.append(",");
				}
			}
			sb.append(") )");
		}
		/**
		 * 选中的地区是当前地区要考虑是否是超管，超管能看到所有单位，普通用户只能看到自己
		 */
		if (curZoneCode.equals(zoneCode) && !ifAdmin) {
			sb.append(" and t.rid ='").append(unitId).append("' ");
		} else {
			sb.append("and t.tsZone.zoneGb <> '" + zoneCode + "' ");

			sb.append(" and t.tsZone.zoneGb like'").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");

			if (StringUtils.isNotBlank(zoneLevel)) {
				sb.append(" and (t.tsZone.zoneType = '").append(zoneLevel).append("' or t.tsZone.zoneType = '");
				sb.append((Integer.parseInt(zoneLevel) + 1)).append("') ");
			}
		}
		sb.append(" order by t.unitname ");
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public List<TsUnit> findUnitByZoneCodeSortCode(boolean ifAdmin, String curZoneCode, Integer unitId,
			String zoneCode, String zoneLevel, String[] sortCodes) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select new TsUnit(t.rid, t.unitname) from TsUnit t where t.ifReveal='1' ");
		if (sortCodes != null && sortCodes.length > 0) {
			sb.append(" and exists ( select 1 from TsUnitAttr tua where tua.tsUnit.rid=t.rid and tua.tsBsSort.sortCode in ( ");
			for (int i = 0; i < sortCodes.length; i++) {
				sb.append("'" + sortCodes[i] + "'");
				if (i != (sortCodes.length - 1)) {
					sb.append(",");
				}
			}
			sb.append(") )");
		}
		/**
		 * 选中的地区是当前地区要考虑是否是超管，超管能看到所有单位，普通用户只能看到自己
		 */
		if (curZoneCode.equals(zoneCode) && !ifAdmin) {
			sb.append(" and t.rid ='").append(unitId).append("' ");
		} else {
			sb.append("and t.tsZone.zoneCode <> '" + zoneCode + "' ");

			sb.append(" and t.tsZone.zoneCode like'").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");

			if (StringUtils.isNotBlank(zoneLevel)) {
				sb.append(" and (t.tsZone.zoneType = '").append(zoneLevel).append("' or t.tsZone.zoneType = '");
				sb.append((Integer.parseInt(zoneLevel) + 1)).append("') ");
			}
		}
		sb.append(" order by t.unitname ");
		return em.createQuery(sb.toString()).getResultList();
	}

	public String delTypeAction(Integer codeId) {
		if (null != codeId) {
			try {
				StringBuilder sql = new StringBuilder();
				sql.append("DELETE FROM TS_CODE_TYPE T WHERE T.RID = ?1");
				Query query = this.em.createNativeQuery(sql.toString());
				query.setParameter(1, codeId);
				query.executeUpdate();
			} catch (Exception e) {
				e.printStackTrace();
				return "该码表类型已被引用，无法删除！";
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public TsCodeType findCodeType(Integer codeId) {
		if (null != codeId) {
			TsCodeType find = this.em.find(TsCodeType.class, codeId);
			return find;
		}
		return null;
	}

	public void saveTsCodeType(TsCodeType tsCodeType, Integer userId) {
		if (null == tsCodeType) {
			return;
		}
		if (null == tsCodeType.getRid()) {
			this.saveObj(tsCodeType);
		} else {
			this.update(tsCodeType);
		}
	}

	@Transactional(readOnly = true)
	public String getMaxTypeCode() {
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT CASE WHEN A.MAXCODE IS NULL THEN 100001 ELSE TO_NUMBER(A.MAXCODE)+1 END AS MCODE");
		sql.append(" FROM (SELECT MAX(T.CODE_TYPE_NAME) MAXCODE FROM TS_CODE_TYPE T ");
		sql.append(" WHERE T.CODE_TYPE_NAME >= 100000 ) A");
		Object singleResult = this.em.createNativeQuery(sql.toString()).getSingleResult();
		return singleResult == null ? null : singleResult.toString();
	}

	@Transactional(readOnly = false)
	public TsProbLib findTsProbLib(Integer rid) {
		if (null != rid) {
			TsProbLib find = (TsProbLib) this.find(TsProbLib.class, rid);
			List<TsProbSubject> subjectList = find.getSubjectList();
			if (null != subjectList && subjectList.size() > 0) {
				for (TsProbSubject tsProbSubject : subjectList) {
					List<TsProOpt> proOptList = tsProbSubject.getProOptList();
					if (null != proOptList) {
						proOptList.size();
					}
					if (tsProbSubject.getPool() != null && null != tsProbSubject.getPool().getTsProPoolOpts()) {
						tsProbSubject.getPool().getTsProPoolOpts().size();
					}
					if (tsProbSubject.getFkByTableId() != null) {
						tsProbSubject.getFkByTableId().getRowtitles().size();
						tsProbSubject.getFkByTableId().getColsdefines().size();
					}
					if (tsProbSubject.getPool() != null && tsProbSubject.getPool().getFkByTableId() != null) {
						tsProbSubject.getPool().getFkByTableId().getRowtitles().size();
						tsProbSubject.getPool().getFkByTableId().getColsdefines().size();
					}
				}
			}
			return find;
		}
		return null;
	}

	/**
	 * 
	 * 添加修改数据
	 * 
	 * */
	public void saveSystem(TsDsfSys tds) {
		if (null != tds.getRid()) {
			this.update(tds);
		} else {
			this.save(tds);

		}
		return;
	}

	/** 查询主表实体和子表实体 */
	@Transactional(readOnly = true)
	public TsDsfSys findSys(Integer rid) {

		TsDsfSys tsDsfSys = (TsDsfSys) this.find(TsDsfSys.class, rid);
		tsDsfSys.getList().size();
		return tsDsfSys;
	}

	public List<TsDsfSysParam> findParamList(Integer rid) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TsDsfSysParam t where t.tsDsfSysId.rid=").append(rid);
		sb.append(" order by t.rid");
		return em.createQuery(sb.toString()).getResultList();
	}

	/** 删除主子表信息 */
	@Transactional(readOnly = false)
	public String deleteSys(Integer sys) {
		try {
			this.delete(TsDsfSys.class, sys);
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return "该纪录已被引用，不允许删除！";
		}
		return null;

	}

	@Transactional(readOnly = false)
	public void saveSysLoginf(TsDsfLoginf tsDsfLoginf) {
		if (null != tsDsfLoginf.getRid()) {
			this.update(tsDsfLoginf);
		} else {
			this.save(tsDsfLoginf);
		}
		return;
	}

	@Transactional(readOnly = false)
	public String delSysLoginf(Integer id) {
		try {
			this.delete(TsDsfLoginf.class, id);
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return "该纪录已被引用，不允许删除！";
		}
		return null;
	}

	@Transactional(readOnly = true)
	public TsDsfLoginf findSysLoginf(Integer rid) {
		TsDsfLoginf tsDsfLoginf = (TsDsfLoginf) this.find(TsDsfLoginf.class, rid);
		tsDsfLoginf.getList().size();
		return tsDsfLoginf;
	}

	public Integer getSysLoginfid(Integer sysid, Integer userId) {
		StringBuilder sb = new StringBuilder();
		sb.append("select rid from Ts_Dsf_Loginf  where DSF_SYS_ID=").append(sysid);
		sb.append(" and USER_ID=").append(userId);
		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			Object obj = list.get(0);
			Integer id = Integer.valueOf(obj.toString());
			return id;
		}
		return 0;
	}

	/**
	 * 更新问卷状态
	 * 
	 * @param rid
	 * @param state
	 */
	public void updateProbLibState(Integer rid, Integer state) {
		StringBuilder sb = new StringBuilder();
		sb.append("update TS_PROB_LIB set state = ").append(state).append(" where rid = ").append(rid);
		em.createNativeQuery(sb.toString()).executeUpdate();
	}

	/**
	 * 保存问卷
	 * 
	 * @param lib
	 */
	public TsProbLib saveOrUpdateProbLib(TsProbLib lib, HttpServletRequest request) {
		TsUserInfo user = ((SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA)).getUser();
		TsSystemLog log = new TsSystemLog();
		log.setClientIp(getIpAddr(request));
		log.setDetailInfo("【" + user.getUsername() + "】修改【" + lib.getQuestName() + "】");
		log.setUserNo(user.getUserNo());
		log.setHintInfo(log.getDetailInfo());
		logImpl.logError(log);
		TsProbLib newLib = null;
		for (TsProbSubject subject : lib.getSubjectList()) {
			if (subject.getFkByTableId() != null) {
				if (subject.getFkByTableId().getRid() == null) {
					save(subject.getFkByTableId());
				} else {
					update(subject.getFkByTableId());
				}
			}
		}
		if (lib.getRid() == null) {
			newLib = (TsProbLib) super.saveObj(lib);
		} else {
			lib.setCreateManid(user.getRid());
			newLib = (TsProbLib) super.updateObj(lib);
			StringBuilder sb = new StringBuilder();
			sb.append("UPDATE  TS_PROB_SUBJECT SET POOL_ID = NULL WHERE STATE = 0 AND QUESTLIB_ID = ").append(
					lib.getRid());
			em.createNativeQuery(sb.toString()).executeUpdate();
		}
		return this.findTsProbLib(newLib.getRid());
	}

	/**
	 * 获取当前网络ip
	 * 
	 * @param request
	 * @return
	 */
	public String getIpAddr(HttpServletRequest request) {
		String ipAddress = request.getHeader("x-forwarded-for");
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getRemoteAddr();
			if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
				// 根据网卡取本机配置的IP
				InetAddress inet = null;
				try {
					inet = InetAddress.getLocalHost();
				} catch (UnknownHostException e) {
					e.printStackTrace();
				}
				ipAddress = inet.getHostAddress();
			}
		}
		// 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
		if (ipAddress != null && ipAddress.length() > 15) { // "***.***.***.***".length()
															// = 15
			if (ipAddress.indexOf(",") > 0) {
				ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
			}
		}
		return ipAddress;
	}

	/**
	 * 删除问卷
	 * 
	 * @param rid
	 */
	public void deleteProbLibByRid(Integer rid) {
		super.delete(TsProbLib.class, rid);
	}

	/**
	 * 删除题目
	 * 
	 * @param rid
	 */
	public void deleteProbSubByRid(Integer rid) {
		super.delete(TsProbSubject.class, rid);
	}

	/**
	 * 删除选项模版
	 * 
	 * @param rid
	 */
	public void deleteProTempl(Integer rid) {
		super.delete(TsProTempl.class, rid);
	}

	public void saveOrUpdateProTempl(TsProTempl templ) {
		if (templ.getRid() == null) {
			save(templ);
		} else {
			update(templ);
		}
	}

	/**
	 * 根据主键查询
	 * 
	 * @param rid
	 */
	@Transactional(readOnly = true)
	public TsProTempl findProTemplByRid(Integer rid) {
		return (TsProTempl) find(TsProTempl.class, rid);
	}

	/**
	 * 判断模版编码是否存在
	 * 
	 * @param templ
	 * @return
	 */
	@Transactional(readOnly = true)
	public boolean proTemplCodeIfExists(TsProTempl templ) {
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT COUNT(T.RID) FROM TS_PRO_TEMPL T");
		sb.append(" WHERE T.TMPL_CODE = '").append(templ.getTmplCode()).append("'");
		if (templ.getRid() != null) {
			sb.append(" AND T.RID <> ").append(templ.getRid());
		}
		return Integer.valueOf(em.createNativeQuery(sb.toString()).getSingleResult().toString()) > 0;
	}

	@Transactional(readOnly = true)
	public List<TsProTempl> findProTempls() {
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT new TsProTempl(t.tmplName,t.tmplOpts) FROM TsProTempl t order by t.tmplCode");
		return em.createQuery(sb.toString()).getResultList();
	}

	public List<TsProbExampool> findTsProbExampool(String hql) {
		List<TsProbExampool> list = em.createQuery(hql).getResultList();
		if (null != list && list.size() > 0) {
			for (TsProbExampool pool : list) {
				pool.getTsProPoolOpts().size();
			}
			return list;
		}
		return null;
	}

	/**
	 * 获取问卷题目类型
	 **/
	@Transactional(readOnly = true)
	public List<TsProbExamtype> getTsProbExamtype() {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TsProbExamtype t where 1=1");
		return em.createQuery(sb.toString()).getResultList();
	}

	/**
	 * 根据获取问卷题目类型
	 * 
	 * @param userId
	 **/
	@Transactional(readOnly = true)
	public List<TsSimpleCode> getTsProbExamtypeByUserId(Integer id) {
		List<TsSimpleCode> l = new ArrayList<TsSimpleCode>(0);
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT DISTINCT T.RID,T.CODE_NO,T.CODE_NAME,T.CODE_LEVEL_NO,T.EXTENDS6 FROM TS_SIMPLE_CODE T ");
		sb.append(" INNER JOIN TS_ROLE_SIMPAUTH T1 ON T1.SIMPCODE_ID=t.rid");
		sb.append(" INNER JOIN TS_ROLE T2 ON T2.RID =T1.ROLE_ID");
		sb.append(" INNER JOIN TS_USER_ROLE T3 ON T2.RID=T3.ROLE_ID");
		sb.append(" INNER JOIN TS_CODE_TYPE T4 ON T4.RID=T.CODE_TYPE_ID");
		sb.append(" WHERE T4.CODE_TYPE_NAME ='13001' AND T3.USER_INFO_ID=").append(id);
		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			for (Object[] obj : list) {
				TsSimpleCode t = new TsSimpleCode();
				t.setRid(obj[0] == null ? null : Integer.valueOf(obj[0].toString()));
				t.setCodeNo(obj[1] == null ? null : obj[1].toString());
				t.setCodeLevelNo(obj[3] == null ? null : obj[3].toString());
				t.setCodeName(obj[2] == null ? null : obj[2].toString());
				t.setExtendS6(obj[4] == null ? null : obj[4].toString());
				l.add(t);
			}
		}
		return l;
	}

	/**
	 * 题库查询 根据id
	 **/
	@Transactional(readOnly = true)
	public TsProbExampool findTsProbExampoolByRid(Integer rid) {
		TsProbExampool t = (TsProbExampool) super.find(TsProbExampool.class, rid);
		t.getTsProPoolOpts().size();
		if (t.getFkByTableId() != null) {
			t.getFkByTableId().getRowtitles().size();
			t.getFkByTableId().getColsdefines().size();
		}
		return t;
	}

	/** 题库新增、修改 **/
	public void insertOrUpdateTsProbExampool(TsProbExampool t) {
		if (t.getFkByTableId() != null) {
			t.getFkByTableId().setTabName(t.getTitleDesc());
			if (t.getFkByTableId().getRid() != null) {
				update(t.getFkByTableId());
			} else {
				t.getFkByTableId().setCreateDate(new Date());
				save(t.getFkByTableId());
			}
		}
		if (t.getRid() != null) {
			super.update(t);
		} else {
			super.save(t);
		}
	}

	/***
	 * 题库删除题目，同时删除下面所有的题目,以及选项
	 * 
	 * @param 层级编码
	 *****/
	public void deleteTsProbExampoolByCode(String levelCode) {
		StringBuilder sb = new StringBuilder();
		sb.append("delete from TS_PRO_POOL_OPT a where exists ");
		sb.append("(select 1 from TS_PROB_EXAMPOOL b where a.quest_id=b.rid and  b.QES_LEVEL_CODE like '")
				.append(levelCode.trim()).append("%')");
		StringBuilder sb1 = new StringBuilder();
		sb1.append("delete  from TS_PROB_EXAMPOOL  where QES_LEVEL_CODE like '").append(levelCode.trim()).append("%'");
		em.createNativeQuery(sb.toString()).executeUpdate();
		em.createNativeQuery(sb1.toString()).executeUpdate();
	}

	/***
	 * 题库题目修改状态，同时修改下面所有的题目** * @param 层级编码
	 ********/
	public void changeTsProbExampoolStateByCode(String levelCode, Integer state) {
		TsProbExampool t = fetchTsProbExampool(levelCode);
		StringBuilder sb = new StringBuilder();
		sb.append("update TS_PROB_EXAMPOOL t set t.state=decode(" + state);
		sb.append(",0,1,1,0) where t.QES_LEVEL_CODE like '").append(levelCode.trim()).append("%'");
		em.createNativeQuery(sb.toString()).executeUpdate();
	}

	/** 题目类型 **/
	@Transactional(readOnly = true)
	public List<TsProbExamtype> findTsProbExamtype(Integer paramType) {
		StringBuilder sb = new StringBuilder();
		sb.append("select new TsProbExamtype(t.rid,t.typeCode,t.levelNp,t.typeName) from TsProbExamtype  t where 1=1 ");
		if (paramType != null) {
			sb.append("and t.paramType=").append(paramType);
		}
		List<TsProbExamtype> l = em.createQuery(sb.toString()).getResultList();
		return l;
	}

	/** 根据层级编码找到对应的题目 */
	@Transactional(readOnly = true)
	public TsProbExampool fetchTsProbExampool(String levelCode) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TsProbExampool  t where 1=1 ");
		if (levelCode != null) {
			sb.append("and t.qesLevelCode ='").append(levelCode).append("'");
		}
		List<TsProbExampool> l = em.createQuery(sb.toString()).getResultList();
		if (l.get(0) != null) {
			l.get(0).getTsProPoolOpts().size();
			return l.get(0);
		} else {
			return null;
		}
	}

	/** 查找当前最大的题目编码 **/
	@Transactional(readOnly = true)
	public Integer findMaxQesCode() {
		StringBuilder sb = new StringBuilder();
		sb.append("select decode(count(*),0,1001,max(to_number(t.qes_code)+1)) from TS_PROB_EXAMPOOL  t  ");
		return Integer.valueOf(em.createNativeQuery(sb.toString()).getSingleResult().toString());
	}

	/** 查找当前题目是否有子题目依赖 **/
	@Transactional(readOnly = true)
	public List<TsProbExampool> ifHaveChildrenProDepend(String code) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TsProbExampool t where t.jumpQuestCode like '").append(code).append(",%'");
		List<TsProbExampool> l = em.createQuery(sb.toString()).getResultList();
		if (l == null || l.size() == 0) {
			return null;
		} else {
			return l;
		}
	}

	/** 多条件查询题库 **/
	@Transactional(readOnly = true)
	public List<TsProbExampool> getTsProbExampoolList(Integer qusetType, String proLevelNp, String titleDesc,
			Integer[] state) {
		StringBuilder sb = new StringBuilder();
		List<TsProbExampool> l = new ArrayList<TsProbExampool>();
		sb.append("select t from TsProbExampool t where 1=1");
		// 题型
		if (qusetType != null) {
			sb.append(" and t.questType=").append(qusetType);
		}
		// 题目类型
		if (StringUtils.isNotBlank(proLevelNp)) {
			sb.append(" and t.tsProbExamtype.levelNp like '").append(proLevelNp).append("%'");
		}
		// 状态
		if (state != null && state.length == 1) {
			sb.append(" and t.state=").append(state[0]);
		}
		// 编码
		if (StringUtils.isNotBlank(titleDesc)) {
			sb.append(" and t.titleDesc like :titleDesc ");
			sb.append(" order by t.tsProbExamtype.typeCode,t.qesLevelCode ");
			l = em.createQuery(sb.toString()).setParameter("titleDesc", "%" + titleDesc.trim() + "%").getResultList();
		} else {
			sb.append(" order by t.tsProbExamtype.typeCode,t.qesLevelCode ");
			l = em.createQuery(sb.toString()).getResultList();
		}
		return l;

	}

	/**
	 * 将选择的题目的子题目带出
	 */
	@Transactional(readOnly = true)
	public List<TsProbExampool> getNodeList(List<TsProbExampool> list) {
		List<TsProbExampool> totalList = new ArrayList<TsProbExampool>();

		StringBuilder sb = new StringBuilder();
		sb.append("select t from TsProbExampool t where t.state=1 and t.qesCode <> t.qesLevelCode order by t.qesLevelCode");
		List<TsProbExampool> poolList = em.createQuery(sb.toString()).getResultList();
		for (TsProbExampool t1 : list) {
			t1 = (TsProbExampool) super.find(TsProbExampool.class, t1.getRid());
			t1.getTsProPoolOpts().size();
			if (t1.getFkByTableId() != null) {
				t1.getFkByTableId().getRowtitles().size();
				t1.getFkByTableId().getColsdefines().size();
			}
			totalList.add(t1);
			for (TsProbExampool t2 : poolList) {
				if (t2.getQesLevelCode().contains(t1.getQesCode())) {
					t2.getTsProPoolOpts().size();
					if (t2.getFkByTableId() != null) {
						t2.getFkByTableId().getRowtitles().size();
						t2.getFkByTableId().getColsdefines().size();
					}
					totalList.add(t2);
				}
			}
		}
		return totalList;
	}

	/**
	 * 随机挑选题库题目
	 */
	@Transactional(readOnly = true)
	public List<TsProbExampool> getExamPoolForRandom(Integer randomMode, Integer totalRandomCount, Integer typeID,
			List<QueSubTypeBean> subTypes) {
		if (randomMode == 0) {
		} else {

		}
		return null;
	}

	/** 根据主键rid */
	@Transactional(readOnly = true)
	public TsProbExamtype selectTsProbExamtype(Integer rid) {
		return (TsProbExamtype) find(TsProbExamtype.class, rid);
	}

	public void saveOrUpdateProb(TsProbExamtype tsProbExamtype) {
		if (tsProbExamtype.getRid() != null) {
			super.update(tsProbExamtype);
		} else {
			super.save(tsProbExamtype);
		}
	}

	// 验证编码的唯一
	public String BequestIdc(TsProbExamtype tsProbExamtype) {
		StringBuilder sb = new StringBuilder(" select rid from TsProbExamtype where typeCode  ='");
		sb.append(tsProbExamtype.getTypeCode()).append("' ");
		if (null != tsProbExamtype.getRid()) {
			sb.append(" and rid <> ").append(tsProbExamtype.getRid());
		}
		List<Object[]> list = em.createQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return "编码已存在，请重新输入！";
		}
		return null;
	}

	public List<TsProbExamtype> delete(TsProbExamtype tsProbExamtype) {
		if (tsProbExamtype != null) {
			List<TsProbExamtype> deleteTs = this.validate(tsProbExamtype);
			if (deleteTs != null && deleteTs.size() > 0) {
				return deleteTs;
			} else {
				super.delete(TsProbExamtype.class, tsProbExamtype.getRid());
			}

		}
		return null;
	}

	// 验证根节点下是否有子节点
	public List<TsProbExamtype> validate(TsProbExamtype tsProbExamtype) {
		String levelNp = tsProbExamtype.getLevelNp();// 取得根节点的层级编码
		String np = levelNp + ".";
		StringBuilder sb = new StringBuilder("select t from TsProbExamtype t where t.levelNp like '");
		sb.append(np).append("%").append("'");
		List<TsProbExamtype> resultList = em.createQuery(sb.toString()).getResultList();
		return resultList;
	}

	public List<TsProbExamtype> confirmation(TsProbExamtype tsProbExamtype) {
		if (tsProbExamtype != null) {
			List<TsProbExamtype> validate = this.validate(tsProbExamtype);
			return validate;
		}
		return null;
	}

	public TsMenu findMenuByMenuEn(String menuEn) {
		if (StringUtils.isNotBlank(menuEn)) {
			Query query = super.em.createNamedQuery("TsMenu.findFullFieldByMenuEn");
			query.setParameter("menuEn", menuEn);
			List list = query.getResultList();
			if (null != list && list.size() > 0) {
				return (TsMenu) list.get(0);
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TsUnit> findUnitByZoneAndSorts(String zoneGb, Integer logUnitId, Map<String, TsBsSort> tsBsSortMap) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select new TsUnit(t.rid, t.unitname) from TsUnit t where t.ifReveal='1' ");
		int zoneType = ZoneUtil.getZoneType(zoneGb);
		sb.append(" and t.tsZone.zoneGb like'").append(ZoneUtil.zoneSelect(zoneGb)).append("%' ");
		sb.append(" and (t.tsZone.zoneType = '").append(zoneType).append("' or t.tsZone.zoneType = '");
		sb.append(zoneType + 1).append("') ");

		if (tsBsSortMap == null)
			tsBsSortMap = new HashMap<>();
		if (null != tsBsSortMap) {
			if (tsBsSortMap.containsKey("2001")) {// 疾控中心
				// 疾控中心只能查看自己以及本级以下的疾控性质的单位
				sb.append("  and exists (select 1 FROM TsUnitAttr ta");
				sb.append("  where ta.tsUnit.rid = t.rid and ta.tsBsSort.sortCode in (2001))  ");
			} else if (tsBsSortMap.containsKey("2003")) {// 卫生局
				// 卫生局可以查看自己以及本级以下 性质为：卫生局、疾控中心以及医疗机构的单位
				sb.append("  and exists (select 1 FROM TsUnitAttr ta");
				sb.append("  where ta.tsUnit.rid = t.rid and ta.tsBsSort.sortCode in (2001,2002,2003))  ");
			} else {
				if (null != logUnitId) {
					sb.append("  and exists (select 1 FROM TsUnitAttr ta");
					sb.append("  where ta.tsUnit.rid = t.rid and ta.tsUnit.rid = ").append(logUnitId).append(")  ");
				}
			}
		}
		sb.append(" order by t.tsZone.zoneGb, t.unitCode,t.unitname ");
		return em.createQuery(sb.toString()).getResultList();
	}

	/**
	 * 
	 * @param zonecode
	 * @param sortCodes
	 * @return
	 */
	public List<TsUnit> findUnitBySorts(String zonecode, String sortCodes) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TsUnit t where 1=1");
		sb.append(" and t.tsZone.zoneCode like '").append(ZoneUtil.zoneSelect(zonecode)).append("%'");

		int zoneType = ZoneUtil.getZoneType(zonecode);

		sb.append(" and (t.tsZone.zoneType = '").append(zoneType).append("' or t.tsZone.zoneType = '");
		sb.append(zoneType + 1).append("') ");

		if (StringUtils.isNotBlank(sortCodes)) {
			sb.append("  and exists (select 1 FROM TsUnitAttr ta");
			sb.append("  where ta.tsUnit.rid = t.rid and ta.tsBsSort.sortCode in (").append(sortCodes).append("))  ");
		}

		sb.append(" order by t.tsZone.zoneCode, t.unitCode,t.unitname ");

		return em.createQuery(sb.toString()).getResultList();
	}

	/**
	 * 根据单位属性的属性编码查找单位
	 * 
	 * @param sortCodes
	 * @return
	 * <AUTHOR>
	 * @createDate 2017/9/14 15:09
	 * 
	 * @修改人
	 * @修改时间
	 * @修改内容
	 */
	public List<TsUnit> findUnitBySorts(String sortCodes) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TsUnit t where t.ifReveal=1");
		if (StringUtils.isNotBlank(sortCodes)) {
			sb.append("  and exists (select 1 FROM TsUnitAttr ta");
			sb.append("  where ta.tsUnit.rid = t.rid and ta.tsBsSort.sortCode in (").append(sortCodes).append("))  ");
		}

		sb.append(" order by  t.unitCode,t.unitname ");

		return em.createQuery(sb.toString()).getResultList();
	}

	public void updateMsgState(Integer msgType, String businessId, Integer userId) {
		StringBuilder sb = new StringBuilder();
		sb.append("update TD_MSG_SUB set ACCEPT_STATE=1 where MAIN_ID in ");
		sb.append("(select rid from TD_MSG_MAIN where SUB_TYPE=").append(msgType);
		sb.append(" and APPEND_KEYS='").append(businessId).append("')");
		sb.append(" and PUBLISH_MAN=").append(userId);
		this.em.createNativeQuery(sb.toString()).executeUpdate();
	}

	public void updateTodoState(Integer msgType, String businessId, Integer userId, Integer todoState) {
		StringBuilder sb = new StringBuilder();
		sb.append(" update Td_Msg_Main a set TODO_STATE = ").append(todoState);
		sb.append(" WHERE 1=1 and exists(select 1 from TD_MSG_SUB b where a.rid= b.main_id");
		sb.append(" and b.PUBLISH_MAN=").append(userId).append(")");
		sb.append(" and a.SUB_TYPE=").append(msgType);
		sb.append(" and a.APPEND_KEYS='").append(businessId).append("'");
		this.em.createNativeQuery(sb.toString()).executeUpdate();
	}

	@Transactional(readOnly = true)
	public List<TsKletype> findTsKleType() {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TsKletype t");
		return this.em.createQuery(sb.toString()).getResultList();

	}

	public void saveOrUpdateTsKleMain(TsKlemain tTsKleMain, Integer rid) {
		if (tTsKleMain.getRid() == null) {
			tTsKleMain.setCreateDate(new Date());
			tTsKleMain.setCreateManid(rid);
			super.save(tTsKleMain);
		} else {
			super.update(tTsKleMain);
		}
	}

	@Transactional(readOnly = true)
	public TsKlemain findTsKleMain(Integer rid) {
		if (rid != null) {
			TsKlemain tsKlemain = (TsKlemain) find(TsKlemain.class, rid);
			if (tsKlemain.getRid() != null) {
				StringBuilder sb = new StringBuilder();
				sb.append(" select t from TsKleanx t where t.mainId=").append(tsKlemain.getRid());
				List<TsKleanx> resultList = em.createQuery(sb.toString()).getResultList();
				tsKlemain.setTsKleanx(resultList);
			}
			return tsKlemain;
		}
		return null;
	}

	@Transactional(readOnly = true)
	public String findTsKleTypeName(Integer rid) {
		if (rid != null) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsKletype t where t.rid=").append(rid);
			List<TsKletype> resultList = em.createQuery(sb.toString()).getResultList();
			if (resultList != null && resultList.size() > 0) {
				return resultList.get(0).getTypeName();
			}
		}
		return null;
	}

	// 跟新知识库主表状态
	public TsKlemain updateTsKleMainState(Integer rid, Integer state) {
		StringBuilder sb = new StringBuilder();
		sb.append(" update TS_KLEMAIN set STATE = ").append(state);
		sb.append(" where rid=").append(rid);
		em.createNativeQuery(sb.toString()).executeUpdate();

		StringBuilder sql = new StringBuilder();
		sql.append(" select t from TsKlemain t where t.rid=").append(rid);
		List<TsKlemain> resultList = em.createQuery(sql.toString()).getResultList();
		if (resultList != null && resultList.size() > 0) {
			return resultList.get(0);
		}
		return null;
	}

	// 查询拥有该菜单的用户
	@Transactional(readOnly = true)
	public List<TsUserInfo> findSendToUser(String menu) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select  T.USER_INFO_ID  from TS_USER_MENU t");
		sb.append(" inner join ts_user_info t1 on t1.rid=t.USER_INFO_ID");
		sb.append(" inner join TS_MENU t2 on t2.rid=t.MENU_TEMPLATE_ID");
		sb.append(" where t2.MENU_EN = '").append(menu).append("'");
		List<Object> resultList = em.createNativeQuery(sb.toString()).getResultList();
		List<TsUserInfo> userList = new ArrayList<TsUserInfo>();
		if (resultList != null && resultList.size() > 0) {
			for (Object obj : resultList) {
				TsUserInfo ts = new TsUserInfo();
				ts.setRid(Integer.valueOf(obj.toString()));
				userList.add(ts);
			}
		}
		return userList;
	}

	public void sendMsg(String titleInfo, Integer createMainId, List<TsUserInfo> userList, Integer rid, String menuEn) {
		TdMsgMain msgMain = new TdMsgMain();
		msgMain.setMessageType(MessageType.COMM_TASK);
		msgMain.setInfoTitle(titleInfo);
		Integer tag = 0;
		// if ("system_zsksh".equals(menuEn)) {
		// tag = 2;
		// } else {
		// tag = 1;
		// }
		msgMain.setNetAdr("/webapp/system/tsKleMainShList.faces?ph=1" + "&mainId=" + rid + "&tabView=2");
		msgMain.setNetName("待办任务");
		msgMain.setSubType(0); // 消息类型
		msgMain.setIsTodo((short) 1); // 是否待办
		msgMain.setTodoState((short) 0); // 待办状态
		msgMain.setTsUserInfo(new TsUserInfo(createMainId));
		msgMain.setPublishTime(new Date());
		for (TsUserInfo user : userList) {
			TdMsgSub sub = new TdMsgSub();
			sub.setTsUserInfo(user);
			sub.setTdMsgMain(msgMain);
			sub.setPublishTime(new Date());
			sub.setAcceptState(0);
			msgMain.getTdMsgSubs().add(sub);
		}
		this.save(msgMain);
	}

	public void deleteMsg(Integer rid) {
		StringBuilder sql = new StringBuilder();
		sql.append(
				" delete TD_MSG_SUB where main_id in (select rid from TD_MSG_MAIN where NET_ADR='/webapp/system/tsKleMainShList.faces?ph=1&mainId=")
				.append(rid).append("&tabView=2')");
		em.createNativeQuery(sql.toString()).executeUpdate();

		StringBuilder sb = new StringBuilder();
		sb.append(" delete TD_MSG_MAIN t where t.NET_ADR ='/webapp/system/tsKleMainShList.faces?ph=1&mainId=")
				.append(rid).append("&tabView=2'");
		em.createNativeQuery(sb.toString()).executeUpdate();
	}

	public String deleteKleMain(Integer rid) {
		String msg = null;
		StringBuilder sb = new StringBuilder();
		sb.append(" select  t from TsKlemain t where t.rid=").append(rid);
		List<TsKlemain> resultList = em.createQuery(sb.toString()).getResultList();
		if (resultList != null && resultList.size() > 0) {
			if (resultList.get(0).getState() != 0) {
				msg = "该知识已提交！不能删除";
			} else {
				StringBuilder sql = new StringBuilder();
				sql.append(" begin");
				sql.append(" delete TS_KLEANX where MAIN_ID=").append(rid).append(";");
				sql.append(" delete TS_KLEMAIN where rid=").append(rid).append(";");
				sql.append(" end;");
				em.createNativeQuery(sql.toString()).executeUpdate();
				msg = "删除成功";
			}
		}
		return msg;
	}

	public void saveOrUpdateHoliday(TsHoliday tsHoliday) {
		if (null != tsHoliday.getRid()) {
			// 修改
			this.update(tsHoliday);
		} else {
			// 保存
			this.save(tsHoliday);
		}
	}

	public void insertBatch(List<TsHoliday> list) {
		if (list != null && list.size() > 0) {
			for (TsHoliday tsHoliday : list) {
				this.save(tsHoliday);
			}
		}
	}

	/**
	 * 根据登录人单位查询其委托或代管的单位 登录人单位 关系类型
	 */
	public List<TsUnitRel> findRelUnitList(TsUnit unit, Integer relType) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TsUnitRel t where 1=1");
		if (null != unit && null != unit.getRid()) {
			sb.append(" and t.fkByUnitId.rid=").append(unit.getRid());
		}
		if (null != relType) {
			sb.append(" and t.relType=").append(relType);
		}

		sb.append(" order by t.fkByRelUnitId.tsZone.zoneCode");
		return em.createQuery(sb.toString()).getResultList();
	}

	public List<String[]> findRelUnitInfoByUnitId(Integer unitRid) {
		StringBuffer sb = new StringBuffer();
		sb.append("select distinct(t.unit_id),t3.zone_name,t2.unitname ");
		sb.append("from  ts_unit_rel t inner join ts_unit t2 on t.unit_id=t2.rid ");
		sb.append("inner join ts_zone t3 on t2.zone_id=t3.rid  ");
		sb.append("where 1=1 ");
		if (unitRid != null) {
			sb.append("and t.unit_id=" + unitRid);
		}

		return em.createNativeQuery(sb.toString()).getResultList();

	}

	public void deleteTsUnitRelAndSave(Integer id, Integer type, List<TsUnitRel> list) {
		StringBuffer sb = new StringBuffer();
		sb.append("delete from ts_unit_rel t ");
		sb.append("where t.unit_id =  " + id + " ");
		sb.append("and t.rel_type= " + type);
		em.createNativeQuery(sb.toString()).executeUpdate();
		if (list != null && list.size() > 0) {
			for (TsUnitRel rel : list) {
				this.saveObj(rel);
			}
		}
	}

	/**
	 * 
	 * <p>
	 * 方法描述：查询可供依赖的题目集合 条件：1.单选、多选、是非题 2.过滤停用 3.过滤自己
	 * </p>
	 * 
	 * 
	 * @MethodAuthor rj,2017年11月9日,selectRelQueList
	 */
	public List<TsProbExampool> selectRelQueList(Integer typeID, Integer selfID) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TsProbExampool t where 1=1");
		sb.append(" AND t.tsSimpleCodeByTypeId.rid = ").append(typeID);
		sb.append(" AND t.rid <> ").append(selfID);
		sb.append(" AND t.questType IN (0,1,10,8)");
		sb.append(" AND t.state = 1");
		sb.append(" AND t.qesLevelCode = t.qesCode");
		List<TsProbExampool> dataList = em.createQuery(sb.toString()).getResultList();
		for (TsProbExampool pool : dataList) {
			pool.getTsProPoolOpts().size();
		}
		return dataList;
	}
	/**
	 *	<p>方法描述：</p>
 	 * 根据选中的id查找用户
 	 * @MethodAuthor xq,2018年1月9日,findUsersByIds
	 * 根据id查询用户
	 * @param ifAdmin
	 * @param zoneCode
	 * @param zoneType
	 * @param unitId
	 * @param ids
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TsUserInfo> findUsersByIds(boolean ifAdmin, String zoneCode, Short zoneType,String ids, Integer unitId){
		List<TsUserInfo> targetList = new ArrayList<TsUserInfo>();
		StringBuilder sb;
		if (ifAdmin) {
			String selectedIds=getInParameter(StringUtils.string2list(ids, ",")," t.rid ");
			sb = new StringBuilder(" select new TsUserInfo(t.rid, t.userNo, t.username) from TsUserInfo t where t.ifReveal=1 and  t.rid in (");
			sb.append(selectedIds).append(") order by t.username ");
			targetList = em.createQuery(sb.toString()).getResultList();
		} else {
			String selectedIds=getInParameter(StringUtils.string2list(ids, ",")," B.RID ");
			sb = new StringBuilder(" SELECT DISTINCT B.RID, B.USERNAME, B.USER_NO");
			sb.append(" FROM  TS_USER_INFO B  ");
			sb.append(" INNER JOIN TS_UNIT C ON B.UNIT_RID = C.RID ");
			sb.append(" INNER JOIN TS_ZONE D ON C.ZONE_ID = D.RID ");
			sb.append(" WHERE B.RID IN (").append(selectedIds).append(")");
			sb.append(" AND (C.RID = '").append(unitId).append("' OR (D.ZONE_GB LIKE '")
					.append(ZoneUtil.zoneSelect(zoneCode)).append("%' AND D.ZONE_TYPE > '").append(zoneType)
					.append("'))");
			sb.append(" ORDER BY B.USERNAME ");

			List<Object[]> temList = em.createNativeQuery(sb.toString()).getResultList();
			if (null != temList && temList.size() > 0) {
				for (Object[] o : temList) {
					TsUserInfo tui = new TsUserInfo(Integer.valueOf(o[0].toString()), o[2].toString(), o[1].toString());
					targetList.add(tui);
				}
			}
		}
		return targetList;
		
	}
	
	 /** 
     * 根据传入的List和参数，拼接in条件，防止in超过999条 
     * 
     * @param list 
     * @param parameter 
     * @return list.size()=n  'list1','list2',...,'list900') or parameter in ('list901','list902',...,'list1800') or parameter in ('list1801','list1802',...,'listn' 
     * list.size()=0  '' 
     */  
    private String getInParameter(List list, String parameter) {  
        if (!list.isEmpty()) {  
            List<String> setList = new ArrayList<String>(0);  
            Set set = new HashSet();  
            StringBuffer stringBuffer = new StringBuffer();  
            for (int i = 1; i <= list.size(); i++) {  
                set.add("'" + list.get(i - 1) + "'");  
                if (i % 900 == 0) {//900为阈值  
                    setList.add(StringUtils.join(set.iterator(), ","));  
                    set.clear();  
                }  
            }  
            if (!set.isEmpty()) {  
                setList.add(StringUtils.join(set.iterator(), ","));  
            }  
            stringBuffer.append(setList.get(0));  
            for (int j = 1; j < setList.size(); j++) {  
                stringBuffer.append(") or " + parameter + " in (");  
                stringBuffer.append(setList.get(j));  
            }  
            return stringBuffer.toString();  
        } else {  
            return "''";  
        }  
  
    }  
	
	/**
	 * 
	 * @param unitId
	 * @param officeid
	 * @param targetList
	 * @return
	 * <p>方法描述：</p>
 	 * 根据当前选择的单位科室中未选择的人
 	 * @MethodAuthor xq,2018年1月15日,findUserByUnitId

	 */
	@Transactional(readOnly = true)
	public List<TsUserInfo> findUserByUnitId(Integer unitId, Integer officeid, List<TsUserInfo> targetList) {
		List<TsUserInfo> reList = new ArrayList<TsUserInfo>();
		StringBuilder sb = new StringBuilder();
		 for (TsUserInfo t : targetList) {
			 sb.append(",").append(t.getRid());
		 }
		String ids = sb.toString();
		
		sb = new StringBuilder(
				" select new TsUserInfo(t.rid, t.userNo, t.username) from TsUserInfo t where t.tsUnit.rid =");
		sb.append(unitId);
		if (officeid != null) {
			sb.append(" and t.tbSysEmp.tsOffice.rid = ").append(officeid);
		}
		sb.append(" and t.ifReveal = 1 ");
		List<TsUserInfo> list = em.createQuery(sb.toString()).getResultList();
		if (list != null && list.size() > 0) {
			if(StringUtils.isBlank(ids)){
				reList.addAll(list);
			}else{
				for(TsUserInfo user:list){
					if(!ids.contains(","+user.getRid())){
						reList.add(user);
					}
				}
			}
		}
		return reList;
	}
	
	
	
	/**
	 *	<p>方法描述：</p>
 	 * 根据选中的id查找用户,超管可以查找所有单位，否则显示当前单位以及下级单位
 	 * @MethodAuthor xq,2018年1月9日,findUnitByIds
	 * 根据id查询用户
	 * @param ifAdmin
	 * @param zoneCode
	 * @param zoneType
	 * @param unitId
	 * @param ids
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<UnitPO> findUnitByIds(boolean ifAdmin, String zoneCode, Integer zoneType,String ids, Integer unitId){
		List<UnitPO> list = new ArrayList<UnitPO>();
		String selectedIds=getInParameter(StringUtils.string2list(ids, ",")," t.rid ");
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT B.ZONE_NAME, A.UNITNAME, A.RID");
		sb.append(" FROM TS_UNIT A ");
		sb.append(" INNER JOIN TS_ZONE B ON A.ZONE_ID = B.RID ");
		sb.append(" WHERE A.IF_REVEAL = '1' AND B.IF_REVEAL = '1' ");
		sb.append(" AND A.RID IN (").append(selectedIds).append(") ");
		
		if (!ifAdmin) {
			sb.append(" AND (A.RID = '").append(unitId).append("' OR (B.ZONE_GB LIKE '")
					.append(ZoneUtil.zoneSelect(zoneCode)).append("%' AND B.ZONE_TYPE >= '").append(zoneType)
					.append("'))");
		}	
		sb.append(" ORDER BY B.ZONE_CODE,A.UNITNAME ");
		
		List<Object[]> temList = em.createNativeQuery(sb.toString()).getResultList();
		if (null != temList && temList.size() > 0) {
			for (Object[] o : temList) {
				UnitPO po = new UnitPO(o[0].toString(), o[1].toString(), Integer.valueOf(o[2].toString()));
				list.add(po);
			}
		}
		return list;
	}
	
	/**
	 * 
	 * @param zoneCode
	 * @param zoneType
	 * @param selectedList
	 * @return
	 *  <p>方法描述：</p>
 	 * 根据当前地区下未选择的单位
 	 * @MethodAuthor xq,2018年1月16日,findUnitByZone
	 * 
	 */
	@Transactional(readOnly = true)
	public List<UnitPO> findUnitByZone(String zoneCode,Integer zoneType,List<UnitPO> selectedList){
		List<UnitPO> reList = new ArrayList<UnitPO>();
		if(StringUtils.isNotBlank(zoneCode)){
			StringBuilder sb = new StringBuilder();
			 for (UnitPO t : selectedList) {
				 sb.append(",").append(t.getUnitId());
			 }
			String ids = sb.toString();
			sb = new StringBuilder();
			sb.append(" SELECT B.ZONE_NAME, A.UNITNAME, A.RID ");
			sb.append(" FROM TS_UNIT A ");
			sb.append(" INNER JOIN TS_ZONE B ON A.ZONE_ID = B.RID ");
			sb.append(" WHERE A.IF_REVEAL = '1' AND B.IF_REVEAL = '1' ");
			sb.append(" AND B.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(zoneCode));
			sb.append("%' AND B.ZONE_TYPE >= ").append(zoneType);
			sb.append(" ORDER BY B.ZONE_CODE,A.UNITNAME ");
			List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
			if (list != null && list.size() > 0) {
				for(Object[] o:list){
					if(StringUtils.isBlank(ids) || !ids.contains(","+o[2])){
						reList.add(new UnitPO(o[0].toString(), o[1].toString(), Integer.valueOf(o[2].toString())));
					}
				}
			}
		}
		return reList;
	}
	
	/**
	 * <p>方法描述：系统类型查询</p>
 	 * 
 	 * @MethodAuthor rcj,2018年4月24日,findParamValue
	 * @param paramName
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TsSystemParam> findParamValue(String paramName){
		StringBuilder sb  = new StringBuilder();
		sb.append(" SELECT t FROM TsSystemParam t WHERE 1=1 ");
		if(null != paramName ){
			sb.append(" AND t.paramName = '").append(paramName).append("'");
		}
		sb.append(" ORDER BY t.rid");
		return em.createQuery(sb.toString()).getResultList();
	}
	
	
	/**
	 * 查询报表模板
	 * @param rptname
	 * @param systemtype
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TsRpt> findRptTemp(String rptname,Integer systemtype){
		StringBuilder sql2 = new StringBuilder();
    	sql2.append(" SELECT t ");
    	sql2.append(" from TsRpt t ");
    	sql2.append(" where 1=1");
    	if(StringUtils.isNotBlank(rptname)){
    		sql2.append(" AND t.rptnam like :rptnam ESCAPE '\\\' ");
    	}
    	if(null != systemtype){
    		sql2.append(" AND t.paramType = '").append(systemtype).append("'");
    	}
		sql2.append(" order by  t.rptCod");
		Query query = em.createQuery(sql2.toString());
		if(StringUtils.isNotBlank(rptname)){
    		query.setParameter("rptnam", "%" + StringUtils.convertBFH(rptname) + "%");
    	}
		List<TsRpt> resultList = query.getResultList();
		return resultList;
	}
	
	
	/**
	 * <p>
	 * 方法描述：保存活更新文书类型
	 * </p>
	 * 
	 * @MethodAuthor rcj,2018年4月24日,saveOrUpdateTbZwWritsort
	 */
	@Transactional(readOnly = false)
	public void saveOrUpdateTbZwWritsort(TbZwWritsort tbZwWritsort,
			TsUserInfo userId) {
		if (null == tbZwWritsort) {
			return;
		}
		if (null == tbZwWritsort.getRid()) {
			tbZwWritsort.setCreateDate(new Date());
			tbZwWritsort.setCreateManid(userId.getRid());
			this.save(tbZwWritsort);
		} else {
			tbZwWritsort.setModifyDate(new Date());
			tbZwWritsort.setModifyManid(userId.getRid());
			this.update(tbZwWritsort);
		}

	}

	/**
	 * <p>
	 * 方法描述：删除文书类型
	 * </p>
	 * 
	 * @MethodAuthor rcj,2018年4月24日,deleteTbZwWritsort
	 * @param rid
	 */
	@Transactional(readOnly = false)
	public String deleteTbZwWritsort(Integer rid) {
		try {
			this.delete(TbZwWritsort.class, rid);
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return "该文书类型数据被引用，不允许删除！";
		}
		return null;
		
	}

	
	/**
	 * <p>方法描述：验证文书编码唯一性</p>
 	 * 
 	 * @MethodAuthor rcj,2018年4月25日,findTbZwWritsortByCode
	 * @return
	 */
	@Transactional(readOnly = true)
	public TbZwWritsort findTbZwWritsortByCode(String writcode ,Integer rid){
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TbZwWritsort t where t.writCode = '").append(writcode).append("'");
		if(null!=rid){
			sb.append(" and t.rid not in (");
			sb.append(rid);
			sb.append(")");
		}
		List<TbZwWritsort>  resultList = em.createQuery(sb.toString()).getResultList();
		if(null!=resultList && resultList.size()>0){
			return  resultList.get(0);
		}
		return null;
	}
	
	/**
	 *  <p>方法描述：保存留言板信息</p>
     *
     * @MethodAuthor maox,2018年6月5日,saveFeedback
	 * @param tsMsgBoard
	 */
	@Transactional(readOnly = false)
	public void saveFeedback(TsMsgBoard tsMsgBoard){
		if(tsMsgBoard.getRid() == null){
			this.save(tsMsgBoard);
		}else{
			this.update(tsMsgBoard);
		}		
	}

	/***
	 * <p>方法描述: 查询本级及下级所有疾控中心性质的单位</p>
	 *
	 * @MethodAuthor mxp, 2018/10/31,initUnitList
	 */
	public List<Object[]> initUnitList(String searchZoneCode) {

		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT A.RID, A.UNITNAME ");
		sb.append("   FROM TS_UNIT A ");
		sb.append("  INNER JOIN TS_UNIT_ATTR B ON A.RID = B.UNIT_RID ");
		sb.append("  INNER JOIN TS_BS_SORT C ON B.ATTR_ID = C.RID ");
		sb.append("  INNER JOIN TS_ZONE Z ON A.ZONE_ID = Z.RID ");
		sb.append("  WHERE A.IF_REVEAL = 1 AND C.SORT_CODE = '2001' ");
		sb.append("    AND Z.ZONE_CODE LIKE '")
				.append(ZoneUtil.zoneSelect(searchZoneCode)).append("%'");
		sb.append("  ORDER BY Z.ZONE_CODE ");
		return  this.commService.findDataBySqlNoPage(sb.toString(),
				null);
	}

	/***
	 * <p>方法描述: 查询所有疾控中心性质的单位</p>
	 *
	 * @MethodAuthor mxp, 2018/10/31,initUnitList2
	 */
	public List<Object[]> initUnitListAll() {

		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT A.RID, A.UNITNAME ");
		sb.append("   FROM TS_UNIT A ");
		sb.append("  INNER JOIN TS_UNIT_ATTR B ON A.RID = B.UNIT_RID ");
		sb.append("  INNER JOIN TS_BS_SORT C ON B.ATTR_ID = C.RID ");
		sb.append("  INNER JOIN TS_ZONE Z ON A.ZONE_ID = Z.RID ");
		sb.append("  WHERE A.IF_REVEAL = 1 AND C.SORT_CODE = '2001' ");
		sb.append("  ORDER BY Z.ZONE_CODE ");
		return  this.commService.findDataBySqlNoPage(sb.toString(),
				null);
	}

	
	/**
	 * <p>方法描述：根据社会信用代码查询单位，若是修改排除自身</p>
 	 * 
 	 * @MethodAuthor rcj,2019年1月22日,findTsUnitByCreditCOde
	 * @param tsUnit
	 */
	public List<TsUnit> findTsUnitByCreditCOde(TsUnit tsUnit) {
		StringBuffer hql = new StringBuffer();
		hql.append(" SELECT T FROM TsUnit T WHERE 1 = 1 ");
		if(StringUtils.isNotBlank(tsUnit.getCreditCode())){
			hql.append(" AND T.creditCode = '").append(tsUnit.getCreditCode()).append("'");
		}
		if(null != tsUnit.getRid()){
			hql.append(" AND T.rid NOT IN (").append(tsUnit.getRid()).append(")");
		}
		List<TsUnit> list = findByHql(hql.toString(), TsUnit.class);
		return list;
	}
	
	/**
	 * <p>方法描述：根据社会信用代码查询单位，若是修改排除自身</p>
 	 * 
 	 * @MethodAuthor rcj,2019年1月22日,findTsUnitByCreditCOde
	 * @param tsUnit
	 */
	public List<TsUnit> findTsUnitByCreditCode(String creditCode,Integer rid) {
		StringBuffer hql = new StringBuffer();
		hql.append(" SELECT T FROM TsUnit T WHERE 1 = 1 ");
		if(StringUtils.isNotBlank(creditCode)){
			hql.append(" AND T.creditCode = '").append(creditCode).append("'");
		}
		if(null != rid){
			hql.append(" AND T.rid NOT IN (").append(rid).append(")");
		}
		List<TsUnit> list = findByHql(hql.toString(), TsUnit.class);
		return list;
	}


	/**
 	 * <p>方法描述：保存消息</p>
 	 * @MethodAuthor qrr,2019年11月6日,saveTdMsg
	 * */
	public void saveTdMsg(TdMsgMain msgMain, List<TdMsgSub> subList) {
		msgMain = (TdMsgMain) this.saveObj(msgMain);
		if (null != subList && subList.size() > 0) {
			for (TdMsgSub sub : subList) {
				sub.setTdMsgMain(msgMain);
				this.save(sub);
			}
		}
	}
	
	@Transactional(readOnly = true)
	public List<Object[]> findTsRolesByTypeId(Integer codeId,boolean ifAdmin) {
		StringBuffer sb= new StringBuffer();
		sb.append(" select t.rid ,t.role_name  from TS_ROLE t left join TS_ROLE_POWER t1 on t1.role_id = t.rid ");
		sb.append(" where t1.role_type_id = ").append(codeId);
		if(!ifAdmin){
			sb.append("and  t.if_super_manage_role <> 1");
		}
		return  this.commService.findDataBySqlNoPage(sb.toString(),null);
	}
	
	/***
	 *  <p>方法描述：验证重复</p>
     *
     * @MethodAuthor maox,2020年1月4日,getRepetUser
	 * @param userId
	 * @param userNo
	 * @param Idc
	 * @return
	 */
	public Integer getRepetUser(Integer userId,String userNo,String Idc) {
		StringBuilder sb = new StringBuilder();
		sb.append("select count(*) from TS_USER_INFO   where 1 =1");
		if(userId != null){
			sb.append(" and rid !=").append(userId);
		}
		if(StringUtils.isNotBlank(userNo)){
			sb.append(" and USER_NO =:userno");
		}
		if(StringUtils.isNotBlank(Idc)){
			sb.append(" and upper(Idc) =:idc");
		}
		Query query = em.createNativeQuery(sb.toString());
		if (StringUtils.isNotBlank(userNo)) {
			query.setParameter("userno", userNo);
		}
		if (StringUtils.isNotBlank(Idc)) {
			query.setParameter("idc", StringUtils.upperCase(Idc));
		}
		List<Object[]> list = query.getResultList();
		
		if (null != list && list.size() > 0) {
			Object obj = list.get(0);
			Integer count = Integer.valueOf(obj.toString());
			return count;
		}
		return 0;
	}

    @Transactional(readOnly = false)
    @ZwxLog(type=LogNoEnum.NO_2001, value="保存数据")
	public String saveOrUpdateUserNew(TsUserInfo user,boolean ifAdmin) {

		if (null != user.getRid()) {
			List<String> str =new ArrayList<>();
			if(!ifAdmin){
				StringBuffer sb= new StringBuffer();
				sb.append(" select t.rid ,t.USER_INFO_ID  from TS_USER_ROLE  t left join TS_ROLE t1 on t.role_id = t1.rid ");
				sb.append(" where t1.if_super_manage_role <> 1 ");
				sb.append(" and t.USER_INFO_ID = ").append(user.getRid());
				List<Object[]>  list  = this.commService.findDataBySqlNoPage(sb.toString(),null);
				if(!CollectionUtils.isEmpty(list)){
					for(Object[] o : list){
						str.add(ObjectUtil.toStr(o[0]));
					}
				}
			}
			//先删除用户角色关系表
			StringBuilder sb = new StringBuilder(" delete TS_USER_ROLE t where t.USER_INFO_ID = ").append(user.getRid());
			if(!ifAdmin){
				if(!CollectionUtils.isEmpty(str)){
					//非管理员只删除超管可见角色
					sb.append("and  t.rid in (").append(StringUtils.list2string(str,",")).append(")");
					em.createNativeQuery(sb.toString()).executeUpdate();
				}
			}else{
				//删除所有
				em.createNativeQuery(sb.toString()).executeUpdate();
			}
			this.update(user);
		} else {
			this.save(user);
		}
		return null;
	}
	
	public List<Object[]> findTsUserRoleByUserId(Integer rid,boolean ifAdmin) {
		StringBuffer sb= new StringBuffer();
		sb.append(" select b.role_type_id,a.role_id from TS_USER_ROLE a left join TS_ROLE_POWER b on a.role_id = b.role_id ");
		sb.append(" LEFT JOIN TS_ROLE c on c.RID = b.ROLE_ID ");
		sb.append(" where a.user_info_id = ").append(rid);
		sb.append(" and b.role_type_id is not null ");
		if(!ifAdmin){
			sb.append("and  c.if_super_manage_role <> 1");
		}
		return  this.commService.findDataBySqlNoPage(sb.toString(),null);
	}
	
	/***
	 *  <p>方法描述：是否业务员</p>
     *
     * @MethodAuthor maox,2020年1月7日,getUserIfyw
	 * @param userId
	 * @return
	 */
	public boolean getUserIfyw(Integer userId) {
		StringBuilder sb = new StringBuilder();
		sb.append("select count(*) from ts_user_info a");
		sb.append(" left join TS_USER_ROLE b on a.rid = b.user_info_id");
		sb.append(" left join ts_role c on b.role_id = c.rid ");
		sb.append(" left join ts_role_power d on c.rid = d.role_id ");
		sb.append(" left join ts_simple_code e on d.role_type_id = e.rid ");
		sb.append(" where e.extends1 = 1 ");
		sb.append("  and a.rid = ").append(userId);
		List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0) {
			return true;
		}
		return false;
	}
	
	public TsZone findTsZoneByCode(String zoneCode) {
		StringBuffer hql = new StringBuffer();
		hql.append(" SELECT T FROM TsZone T WHERE 1 = 1 ");
		if(StringUtils.isNotBlank(zoneCode)){
			hql.append(" AND T.zoneCode = '").append(zoneCode).append("'");
		}
		List<TsZone> list = findByHql(hql.toString(), TsZone.class);
		if(null != list && list.size()>0){
			return list.get(0);
		}else{
			return null;
		}
		
	}
	
	public List<MenuVO> findMenuList(Boolean admin, Integer userId) {
		List<MenuVO> rtnList = new ArrayList<MenuVO>();
		// 查询所有符合条件的菜单集合
		List<MenuVO> list = this.selectMenuList(admin, userId);
		if (null != list && list.size() > 0) {
			Map<String, MenuVO> menuMap = new HashMap<String, MenuVO>();
			for (MenuVO menu : list) {
				// 层级编码
				String menuLevelNo = menu.getMenuLevelNo();
				int countMatches = StringUtils.countMatches(menuLevelNo, ".");
				menu.setMenuLevel(countMatches);

				menuMap.put(menu.getMenuLevelNo(), menu);
				if (menu.getMenuLevel() == 0) {
					rtnList.add(menu);
				} else {
					String parentCode = StringUtils.substring(menu.getMenuLevelNo(), 0,
							StringUtils.lastIndexOf(menu.getMenuLevelNo(), "."));
					MenuVO parentMenu = menuMap.get(parentCode);
					if(null != parentMenu) {
						parentMenu.getChildrenList().add(menu);
					}
				}
			}
			menuMap.clear();
		}
		//处理一级菜单：如果有二级菜单，清空一级菜单的menuUri
		for (MenuVO menuVO : list) {
			if(menuVO.getChildrenList()!=null&&menuVO.getChildrenList().size()>0){
				menuVO.setMenuUri(null);
			}else if("#".equals(menuVO.getMenuUri())){
				menuVO.setMenuUri(null);
			}
		}
		return rtnList;
	}
	/***
	 * <p>方法描述: 查询所有菜单</p>
	 *
	 * @MethodAuthor mxp,2018/11/9,findMenuList
	 */
	public List<MenuVO> selectMenuList(Boolean admin, Integer userId) {
		List<MenuVO> rtnList = new ArrayList<MenuVO>();
		StringBuffer sb = new StringBuffer();
		if (admin) {
			sb.append("	SELECT");
			sb.append("	T.RID AS menuRid,");
			sb.append("	T.MENU_LEVEL_NO AS menuLevelNo,");
			sb.append("	T.MENU_CN AS menuCn,");
			sb.append("	T.MENU_URI AS menuUri,");
			sb.append("	T.NUM AS num,");
			sb.append("	T.BIG_ICON AS bigIcon,");
			sb.append("	T.MENU_ICON AS icon,");
			sb.append("	T.IF_POP AS ifPop,");
			sb.append("	T.MENU_EN AS menuEn");
			sb.append("	FROM");
			sb.append("	TS_MENU T");
			sb.append("	ORDER BY LENGTH(T.MENU_LEVEL_NO) ,");
			sb.append("			T.MENU_LEVEL_NO");
		} else {
			sb.append("SELECT ");
			sb.append("	  M.* ");
			sb.append("	FROM");
			sb.append("	  (SELECT ");
			sb.append("		T1.RID AS menuRid,");
			sb.append("		T1.MENU_LEVEL_NO AS menuLevelNo,");
			sb.append("		T1.MENU_CN AS menuCn,");
			sb.append("		T1.MENU_URI AS menuUri,");
			sb.append("		T1.NUM AS num,");
			sb.append("		T1.BIG_ICON AS bigIcon,");
			sb.append("		T1.MENU_ICON AS icon , ");
			sb.append("		T1.IF_POP AS ifPop,	");
			sb.append("		T1.MENU_EN AS menuEn ");
			sb.append("	  FROM");
			sb.append("		TS_MENU T1 ");
			sb.append("		INNER JOIN TS_USER_MENU T2 ");
			sb.append("		  ON T2.MENU_TEMPLATE_ID = T1.RID ");
			if (userId != null) {
				sb.append("WHERE T2.USER_INFO_ID =").append(userId);
			}
			sb.append("	  UNION");
			sb.append("	  SELECT ");
			sb.append("		A1.RID,");
			sb.append("		A1.MENU_LEVEL_NO,");
			sb.append("		A1.MENU_CN,");
			sb.append("		A1.MENU_URI,");
			sb.append("		A1.NUM,");
			sb.append("		A1.BIG_ICON,");
			sb.append("		A1.MENU_ICON, ");
			sb.append("		A1.IF_POP AS ifPop,");
			sb.append("		A1.MENU_EN AS menuEn ");
			sb.append("	  FROM");
			sb.append("		TS_MENU A1 ");
			sb.append("		INNER JOIN TS_ROLE_MENU A2 ");
			sb.append("		  ON A2.MENU_ID = A1.RID ");
			sb.append("		INNER JOIN TS_USER_ROLE A3 ");
			sb.append("		  ON A3.ROLE_ID = A2.ROLE_ID ");
			if (userId != null) {
				sb.append("WHERE A3.USER_INFO_ID =").append(userId);
			}
			sb.append("	  ) M ");
			sb.append("	ORDER BY LENGTH(M.menuLevelNo),");
			sb.append("	  M.NUM,");
			sb.append("	  M.menuLevelNo ");
		}

		List<Object[]> sqlResultList = super.findSqlResultList(sb.toString());
		if (sqlResultList != null && sqlResultList.size() > 0) {
			MenuVO menuVO;
			for (Object[] objects : sqlResultList) {
				menuVO = new MenuVO();
				menuVO.setMenuRid(StringUtils.objectToString(objects[0]));
				menuVO.setMenuLevelNo(StringUtils.objectToString(objects[1]));
				menuVO.setMenuCn(StringUtils.objectToString(objects[2]));
				menuVO.setMenuUri(StringUtils.objectToString(objects[3]));
				menuVO.setBigIcon(StringUtils.objectToString(objects[5]));
				menuVO.setIcon(StringUtils.objectToString(objects[6]));
				menuVO.setIfPop(objects[7] == null ? 0 : Integer.valueOf(objects[7].toString()));
				menuVO.setMenuEn(StringUtils.objectToString(objects[8]));
				rtnList.add(menuVO);
			}
		}
		return rtnList;
	}

	/**
	* @Description : 根据用户身份证号获取用户集合，排除自身
	* @MethodAuthor: anjing
	* @Date : 2020/2/12 20:12
	**/
    public List<TsUserInfo> findTsUserInfoListByIdc(TsUserInfo tsUserInfo) {
        StringBuffer hql = new StringBuffer();
        hql.append(" SELECT T FROM TsUserInfo T WHERE 1 = 1 ");
        if(StringUtils.isNotBlank(tsUserInfo.getIdc())){
            hql.append(" AND upper(T.idc) = upper('").append(tsUserInfo.getIdc()).append("')");
        }
        if(null != tsUserInfo.getRid()){
            hql.append(" AND T.rid NOT IN (").append(tsUserInfo.getRid()).append(")");
        }
        List<TsUserInfo> list = findByHql(hql.toString(), TsUserInfo.class);
        return list;
    }
    /**
 	 * <p>方法描述：优先查询zoneCode的省级地区</p>
 	 * @MethodAuthor qrr,2020年3月17日,findZoneListOrderByUserZone
     * */
    @Transactional(readOnly = true)
	public List<TsZone> findZoneListOrderByUserZone(String zoneGb) {
		StringBuilder sb = new StringBuilder();
		sb.append("select * from (");
		sb.append("select t.rid, t.zone_Gb, t.zone_Name, t.zone_Type, t.dsf_Code,t.zone_code from Ts_Zone t where t.if_Reveal='1' ");
		if (StringUtils.isNotBlank(zoneGb)) {
			sb.append(" and t.zone_GB like '").append(zoneGb.substring(0, 2)).append("%' ");
		}
		sb.append(" order by t.zone_GB )");
		sb.append(" UNION ALL");
		sb.append(" select * from (select t.rid, t.zone_Gb, t.zone_Name, t.zone_Type, t.dsf_Code,t.zone_code from Ts_Zone t where t.if_Reveal='1' ");
		if (StringUtils.isNotBlank(zoneGb)) {
			sb.append(" and t.zone_GB not like '").append(zoneGb.substring(0, 2)).append("%' ");
		}
		sb.append(" order by t.zone_GB )");
		List<Object[]> list = this.findSqlResultList(sb.toString());
		List<TsZone> results = new ArrayList<>();
		if (null!=list && list.size()>0) {
			for (Object[] obj : list) {
				TsZone zone = new TsZone();
				zone.setRid(Integer.valueOf(obj[0].toString()));
				zone.setZoneGb(obj[1].toString());
				zone.setZoneName(obj[2].toString());
				if (null!=obj[3]) {
					zone.setZoneType(Short.valueOf(obj[3].toString()));
				}
				if (null!=obj[4]) {
					zone.setDsfCode(obj[4].toString());
				}
				if (null!=obj[5]) {
					zone.setZoneCode(obj[5].toString());
				}
				results.add(zone);
			}
		}
		return results;
	}
    /**
 	 * <p>方法描述：根据单位属性查询配置的角色</p>
 	 * @MethodAuthor qrr,2020年7月31日,findApplyRole
     * */
    public List<TbSysApplyRole> findApplyRole(String sortIds) {
    	StringBuffer hql = new StringBuffer();
    	hql.append(" select t from TbSysApplyRole t where t.fkByMainId.rid in (").append(sortIds).append(")");
    	return this.findHqlResultList(hql.toString());
	}
    /**
 	 * <p>方法描述：查询角色</p>
 	 * @MethodAuthor qrr,2020年7月31日,findTsRolesByTypeId
     * */
    public List<TsRolePower> findTsRoles(boolean ifAdmin) {
		StringBuffer sb= new StringBuffer();
		sb.append(" select t  from TsRolePower t where 1=1 ");
		if(!ifAdmin){
			sb.append("and  t.fkByRoleId.ifSuperManageRole <> 1");
		}
		sb.append(" order by t.fkByRoleTypeId.num,t.fkByRoleTypeId.codeNo,t.fkByRoleId.rid");
		List<TsRolePower> list = this.commService.findHqlResultList(sb.toString());
		return  list;
	}
    /**	
 	 * <p>方法描述：注册账号审核通过</p>
 	 * @MethodAuthor qrr,2020年7月31日,saveTdSysApplyAccount
	 * */
	@Transactional(readOnly=false)
	public void saveTdSysApplyAccount(TdSysApplyAccount applyAccount,
			List<TdSysApplySort> applySorts,
			Date validBegDate, Date validEndDate,
			List<RoleTypePoNew> roleTypeList,
			Map<Integer, TsRole> roleMap,boolean ifAdmin) {
		if (null==applyAccount) {
			return;
		}
		applyAccount.setStateMark(1);
		applyAccount.setFkByCheckUserId(Global.getUser());
		applyAccount.setBackRsn(null);
		this.upsertEntity(applyAccount);
		
		// 单位
		String creditCode = applyAccount.getCreditCode();
		StringBuffer hql = new StringBuffer();
		hql.append(" select T from TsUnit t WHERE t.creditCode = '").append(creditCode).append("'");

		if(null != applyAccount.getIfSubOrg()){
			if(1 ==applyAccount.getIfSubOrg()){
				hql.append("  AND t.unitname = '").append(applyAccount.getUnitname()).append("'");
			}
			hql.append(" AND t.ifSubOrg =").append(applyAccount.getIfSubOrg());
		}

		List<TsUnit> list = this.findByHql(hql.toString());
		TsUnit unit = new TsUnit();
		if (!CollectionUtils.isEmpty(list)) {
			unit = list.get(0);
		}
		unit.setIfSubOrg(null !=applyAccount.getIfSubOrg()?applyAccount.getIfSubOrg().toString():"");
		unit.setTsZone(applyAccount.getFkByZoneId());
		unit.setFkByManagedZoneId(applyAccount.getFkByZoneId());
		unit.setUnitname(applyAccount.getUnitname());
		unit.setUnitSimpname(applyAccount.getUnitname());
		unit.setIfReveal((short)1);
		unit.setUnittel(applyAccount.getUnitTel());
		unit.setCreditCode(applyAccount.getCreditCode());
		// 单位属性
		List<String> selectedSortList = new ArrayList<String>();
		if (!CollectionUtils.isEmpty(applySorts)) {
			for (TdSysApplySort t : applySorts) {
				selectedSortList.add(t.getFkBySortId().getFkBySortId().getRid().toString());
			}
		}
		if (null!=unit.getRid()) {
			preUpdate(unit);
		}else {
			preInsert(unit);
		}
		this.saveOrUpdateUnit(unit, selectedSortList);
		// 用户
		TsUserInfo userInfo = new TsUserInfo();
		userInfo.setTsUnit(unit);
		userInfo.setUserType((short)2);
		userInfo.setUserNo(applyAccount.getUserNo());
		userInfo.setUsername(applyAccount.getUsername());
		userInfo.setPassword(applyAccount.getPassword());
		userInfo.setIfModpsw((short)1);
		userInfo.setIfReveal((short)1);
		userInfo.setUseradmin(false);
		userInfo.setMbNum(applyAccount.getMobileNum());
		userInfo.setIsAdd(0);
		userInfo.setUploadTag(0);
		userInfo.setIdc(applyAccount.getIdc());
		userInfo.setValidBegDate(validBegDate);
		userInfo.setValidEndDate(validEndDate);
		
		//角色
		if(roleTypeList != null && roleTypeList.size() >0){
			List<TsUserRole> tsUserRoles = new ArrayList<TsUserRole>();
			for(RoleTypePoNew typo :roleTypeList){
				List<String> irr = typo.getItemSubs();
				if(irr != null && irr.size() >0){
					for(String i :irr){
						TsUserRole  userRole = new TsUserRole();
						userRole.setTsRole(roleMap.get(Integer.valueOf(i)));
						userRole.setTsUserInfo(userInfo);
						tsUserRoles.add(userRole);
					}
				}
			}
			
			userInfo.setTsUserRoles(tsUserRoles);
		}
		preInsert(userInfo);
		this.saveOrUpdateUserNew(userInfo,ifAdmin);
		
	}
    /**
 	 * <p>方法描述：注册账号</p>
 	 * @MethodAuthor rcj,2020年7月31日,saveOrUpdateApplyAccount
	 * */
	@Transactional(readOnly=false)
	public void saveOrUpdateApplyAccount(TdSysApplyAccount applyAccount) {
		if (null==applyAccount) {
			return;
		}
		applyAccount.setStateMark(0);
		applyAccount.setBackRsn(null);
		if (applyAccount.getRid() == null) {
			saveObj(applyAccount);
		} else {
			updateObj(applyAccount);
		}
		if(!CollectionUtils.isEmpty(applyAccount.getApplyAnnexList())){
			applyAccount.getApplyAnnexList().size();
		}
		if(!CollectionUtils.isEmpty(applyAccount.getApplySortList())){
			applyAccount.getApplySortList().size();
		}

	}
	/**
	 * <p>方法描述：根据地区编码找到地区</p>
	 * @MethodAuthor rcj,2020年8月13日,findTsZoneByGb
	 * */
	public TsZone findTsZoneByGb(String zoneGb) {
		StringBuffer hql = new StringBuffer();
		hql.append(" SELECT T FROM TsZone T WHERE 1 = 1 ");
		if(StringUtils.isNotBlank(zoneGb)){
			hql.append(" AND T.zoneGb = '").append(zoneGb).append("'");
		}
		List<TsZone> list = findByHql(hql.toString(), TsZone.class);
		if(null != list && list.size()>0){
			return list.get(0);
		}else{
			return null;
		}

	}

	/**
	 * <p>方法描述：根据地区编码找到地区</p>
	 * @MethodAuthor rcj,2020年8月13日,findTsZoneByGb
	 * */
	public TdSysApplyAccount findTdSysApplyAccountByRid(Integer rid) {

		TdSysApplyAccount tdSysApplyAccount = find(TdSysApplyAccount.class, rid);
		if(!CollectionUtils.isEmpty(tdSysApplyAccount.getApplyAnnexList())){
			tdSysApplyAccount.getApplyAnnexList().size();
		}
		if(!CollectionUtils.isEmpty(tdSysApplyAccount.getApplySortList())){
			tdSysApplyAccount.getApplySortList().size();
		}
		return tdSysApplyAccount;

	}

	public List<TdSysApplyAccount> findTdSysApplyAccountByCreditCOde(TdSysApplyAccount tsUnit) {
		Map<String, Object> paramMap = new HashMap<>();
		StringBuffer hql = new StringBuffer();
		hql.append(" SELECT T FROM TdSysApplyAccount T WHERE 1 = 1 ");
		if(StringUtils.isNotBlank(tsUnit.getCreditCode())){
			hql.append(" AND T.creditCode =:creditCode");
			paramMap.put("creditCode",tsUnit.getCreditCode());

		}
		if(StringUtils.isNotBlank(tsUnit.getUnitname())){
			hql.append(" AND T.unitname =:unitname");
			paramMap.put("unitname",tsUnit.getUnitname());
		}
		if(null !=tsUnit.getIfSubOrg() ){
			hql.append(" AND T.ifSubOrg = ").append(tsUnit.getIfSubOrg());
		}
		  return findDataByHqlNoPage(hql.toString(), paramMap);
	}

	/**
	 * @MethodName: checkReg
	 * @Description:
	 * @Param: [zoneGb, zoneType]
	 * @Return: java.util.List<java.lang.Object[]>
	 * @Author: maox
	 * @Date: 2020-08-14
	**/
	public List<Object[]> checkReg(String zoneGb,Integer zoneType ){
		StringBuffer sb = new StringBuffer();
		sb.append(" select t.unitname,t.unittel ");
		sb.append("    from ts_unit t");
		sb.append("		inner join (select t1.unit_rid from ts_user_info t1");
		sb.append("		left join ts_user_role t2 on t1.rid = t2.user_info_id ");
		sb.append("		left join ts_role t3 on t2.role_id = t3.rid");
		sb.append("		left join ts_role_power t4 on t4.role_id = t3.rid ");
		sb.append("		left join Ts_Simple_Code t5 on t4.role_type_id = t5.rid ");
		sb.append("		where t5.extends1 =1 and t1.IF_REVEAL=1 group by t1.unit_rid) t6 on t.rid = t6.unit_rid ");
		sb.append(" 	left join ts_zone t7 on t.manage_zone_id = t7.rid");
		sb.append("		where 1 =1 and t.IF_REVEAL=1 and t.UNITTEL is not null");
		if(StringUtils.isNotBlank(zoneGb)){
			sb.append(" and t7.zone_gb like '").append(zoneGb).append("%'");
		}
		if(zoneType != null){
			sb.append(" and t7.zone_type = ").append(zoneType);
		}
		sb.append("		order by t7.zone_gb,t.unitname ");
		List<Object[]> rstList = em.createNativeQuery(sb.toString()).getResultList();
		return rstList;
	}
	/**
 	 * <p>方法描述：查询单位是否存在</p>
 	 * @MethodAuthor qrr,2020年9月22日,findUnitIfExists
	 * */
	public Integer findUnitIfExists(String creditCode,Integer rid,String unitName,String ifSubOrg){
		Map<String, Object> map = new HashMap<>();
		StringBuffer sql = new StringBuffer();
		sql.append(" select count(1) from ts_unit a where 1=1");
		if (StringUtils.isNotBlank(creditCode)) {
			sql.append(" and a.CREDIT_CODE =:creditCode");
			map.put("creditCode", creditCode);
		}
		if (StringUtils.isNotBlank(unitName)) {
			sql.append(" and a.UNITNAME =:unitName");
			map.put("unitName", unitName);
		}
		if (StringUtils.isNotBlank(ifSubOrg)) {
			sql.append(" and NVL(a.IF_SUB_ORG,0) =").append(ifSubOrg);
		}
		if(rid != null){
			sql.append(" and a.rid != ").append(rid);
		}
		
		List<Object> list = this.findDataBySqlNoPage(sql.toString(),map);
		if (list != null && list.size() > 0) {
            return Integer.valueOf(list.get(0).toString());
        }
		return null;
	}
	/**
 	 * <p>方法描述：查询启用的问卷</p>
 	 * @MethodAuthor qrr,2021年8月23日,findTsProbLib
	 * */
	public List<Object[]> findNewestTsProbLib(String extendS2) {
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT T.RID,T.QUEST_NAME,HTML_NAME ,T1.RID as  queId");
		sql.append(" FROM TS_PROB_LIB T ");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T1 ON T1.RID = T.QUEST_SORTID ");
		sql.append(" WHERE T1.extendS2 =").append(extendS2);
		sql.append(" AND T.STATE = 1 ");
		List<Object[]> list = this.findSqlResultList(sql.toString());
		return list;
	}
	/**
 	 * <p>方法描述：删除问卷答题结果</p>
 	 * @MethodAuthor qrr,2021年8月21日,deleteProbAnsResult
	 * */
	public void deleteProbAnsResult(Integer rid,Integer mainType) {
		this.executeSql("DELETE FROM TD_PROB_ANS_TABLE WHERE SUR_SUBJECTID IN (SELECT RID FROM TD_PROB_ANS_DETAIL WHERE MAIN_TYPE="+mainType+" AND MAIN_ID = "+rid+") ", null);
		this.executeSql("DELETE FROM TD_PROB_ANS_DETAIL WHERE MAIN_TYPE="+mainType+" AND MAIN_ID = "+rid, null);
	}
	
	/**
 	 * <p>方法描述：删除问卷答题结果</p>
 	 * @MethodAuthor qrr,2021年8月21日,deleteProbAnsResult
	 * */
	public void deleteProbAnsResultNew(Integer rid,Integer queTypeId) {
		this.executeSql("DELETE FROM TD_PROB_ANS_TABLE WHERE SUR_SUBJECTID IN (SELECT RID FROM TD_PROB_ANS_DETAIL WHERE QUE_TYPE_ID="+queTypeId+" AND MAIN_ID = "+rid+") ", null);
		this.executeSql("DELETE FROM TD_PROB_ANS_DETAIL WHERE QUE_TYPE_ID="+queTypeId+" AND MAIN_ID = "+rid, null);
	}

	/**
	 * <p>描述 更新过期用户信息</p>
	 *	IF_REVEAL = 0,IDC = NULL
	 * @param date 过期时间
	 * @MethodAuthor gongzhe,2022/6/27 16:36,updateExpiredUserInfo
	 * @return void
	 */
	public void updateExpiredUserInfo(String date){
		StringBuilder sb = new StringBuilder();
		sb.append("UPDATE TS_USER_INFO SET IF_REVEAL = 0,IDC = NULL WHERE USER_TYPE_TAG = 1 AND IF_REVEAL = 1 ");
		sb.append(" AND VALID_END_DATE < TO_DATE('").append(date).append("','YYYY-MM-DD') ");
		em.createNativeQuery(sb.toString()).executeUpdate();
	}
	/**
	 *  <p>类描述：单位信息查询 按照机构行政地区分组    排序机构地区、管辖地区、机构名称排序</p>
	 *  zoneGb ：地区编码
	 *  zoneType ：地区级别
	 *  sortCode ：单位属性
	 * @ClassAuthor hsj 2022-09-21 9:40
	 */
	public List<Object[]> findObjectByZoneCode(String zoneGb, Integer zoneType,String sortCode ) {
		String realZoneType = String.valueOf(ZoneUtil.getZoneType(zoneGb));
		StringBuffer sb = new StringBuffer();
		sb.append(" WITH   unit AS ( ");
		sb.append("  SELECT T.RID, T.UNITNAME, T1.ZONE_Name,T1.ZONE_GB,  T5.ZONE_GB AS MANAGE_ZONE_GB ");
		if ("2".equals(realZoneType)) {
			//当前地区为省
			sb.append(",concat(substr(T1.zone_gb, 0,").append(zoneType*2).append("),'").append("0000000000".substring(0,10-zoneType*2)).append("') zoneGb ");
		} else if ("3".equals(realZoneType)) {
			//当前地区为市
			sb.append(",concat(substr(T1.zone_gb, 0,").append(zoneType*2).append("),'").append("0000000000".substring(0,10-zoneType*2)).append("') zoneGb ");
		} else if ("4".equals(realZoneType)) {
			//当前地区为区
			sb.append(",concat(substr(T1.zone_gb, 0,").append(2*(zoneType-1)).append("),'").append("0000000000".substring(0,10-2*(zoneType-1))).append("') zoneGb ");
		} else {
			sb.append(",T1.zone_gb as zoneGb");
		}
		sb.append("   from TS_UNIT T   ");
		sb.append("   LEFT JOIN TS_ZONE T1 on T.ZONE_ID = T1.RID  ");
		sb.append("    LEFT JOIN TS_ZONE T5 on T.MANAGE_ZONE_ID = T5.RID  ");
		if(StringUtils.isNotBlank(sortCode)){
		sb.append(" LEFT JOIN TS_UNIT_ATTR T2 ON T2.UNIT_RID = T.RID ");
		sb.append(" LEFT JOIN TS_BS_SORT T3 ON T2.ATTR_ID = T3.RID  ");
		}
		sb.append(" WHERE 1=1 AND T.IF_REVEAL = 1 ");
		if(StringUtils.isNotBlank(sortCode)){
			sb.append(" AND T3.SORT_CODE = '").append(sortCode).append("'");
		}
		sb.append(" AND T1.ZONE_GB like '")
				.append(ZoneUtil.zoneSelect(zoneGb)).append("%' ");
		sb.append(" AND T1.REAL_ZONE_TYPE >='").append(realZoneType).append("' ");
		sb.append(" ),");
		sb.append(" ZONE AS ( SELECT   ");
		if ("2".equals(realZoneType)) {
			//当前地区为省
			sb.append("concat(substr(T1.zone_gb, 0,").append(zoneType*2).append("),'").append("0000000000".substring(0,10-zoneType*2)).append("') zoneGb ");
		} else if ("3".equals(realZoneType)) {
			//当前地区为市
			sb.append("concat(substr(T1.zone_gb, 0,").append(zoneType*2).append("),'").append("0000000000".substring(0,10-zoneType*2)).append("') zoneGb ");
		} else if ("4".equals(realZoneType)) {
			//当前地区为区
			sb.append("concat(substr(T1.zone_gb, 0,").append(2*(zoneType-1)).append("),'").append("0000000000".substring(0,10-2*(zoneType-1))).append("') zoneGb ");
		} else {
			sb.append("T1.zone_gb as zoneGb");
		}
		sb.append("  FROM TS_ZONE T1 Where 1=1 ");
		sb.append(" AND T1.ZONE_GB like '")
				.append(ZoneUtil.zoneSelect(zoneGb)).append("%' ");
		sb.append(" AND T1.REAL_ZONE_TYPE >='").append(realZoneType).append("' ");
		sb.append(" GROUP BY ");
		if ("2".equals(realZoneType)) {
			//当前地区为省
			sb.append("concat(substr(T1.zone_gb, 0,").append(zoneType*2).append("),'").append("0000000000".substring(0,10-zoneType*2)).append("')  ");
		} else if ("3".equals(realZoneType)) {
			//当前地区为市
			sb.append("concat(substr(T1.zone_gb, 0,").append(zoneType*2).append("),'").append("0000000000".substring(0,10-zoneType*2)).append("')  ");
		} else if ("4".equals(realZoneType)) {
			//当前地区为区
			sb.append("concat(substr(T1.zone_gb, 0,").append(2*(zoneType-1)).append("),'").append("0000000000".substring(0,10-2*(zoneType-1))).append("')  ");
		} else {
			sb.append("T1.zone_gb");
		}
		sb.append(" ) ");
		sb.append(" SELECT u.RID,U.UNITNAME,U.zoneGb,t1.ZONE_NAME FROM unit u LEFT JOIN ZONE z ON u.zoneGb = z.zoneGb ");
		sb.append(" LEFT JOIN Ts_zone t1 ON U.zoneGb = t1.ZONE_GB  ");
		sb.append(" ORDER BY u.ZONE_GB,u.MANAGE_ZONE_GB,u.UNITNAME ");
		return  findSqlResultList(sb.toString());
	}

	/**
	 * <p>方法描述：通过当前用户id 获取用户自定义桌面菜单ID集合 </p>
	 * @MethodAuthor： pw 2023/3/18
	 **/
	@Transactional(readOnly = true)
	public List<Integer> findMenuIdListByUserFromUserDesk(){
		String sql = "SELECT T.MENU_ID,'' FROM TS_USER_DESK T WHERE T.USER_ID = :userId";
		Map<String,Object> paramMap = new HashMap<>();
		paramMap.put("userId", Global.getUser().getRid());
		List<Object[]> queryResultList = this.findDataBySqlNoPage(sql, paramMap);
		if(CollectionUtils.isEmpty(queryResultList)){
			return Collections.EMPTY_LIST;
		}
		List<Integer> resultList = new ArrayList<>();
		for(Object[] objArr : queryResultList){
			resultList.add(Integer.parseInt(objArr[0].toString()));
		}
		return resultList;
	}

	/**
	 * <p>方法描述： 存储添加的快捷菜单 </p>
	 * @MethodAuthor： pw 2023/3/20
	 **/
	public void saveTsUserDeskList(List<Integer> menuRidList){
		//先执行删除
		String sql = " DELETE FROM TS_USER_DESK WHERE USER_ID = :userId ";
		Map<String,Object> paramMap = new HashMap<>();
		paramMap.put("userId", Global.getUser().getRid());
		this.executeSql(sql, paramMap);
		if(CollectionUtils.isEmpty(menuRidList)){
			return;
		}
		List<TsUserDesk> addList = new ArrayList<>();
		int size = menuRidList.size();
		TsUserInfo userInfo = Global.getUser();
		for(int i=0;i<size;i++){
			TsUserDesk userDesk = new TsUserDesk();
			userDesk.setTsUserInfo(userInfo);
			userDesk.setTsMenu(new TsMenu(menuRidList.get(i)));
			userDesk.setOrderNo(i+1);
			userDesk.setCreateDate(new Date());
			userDesk.setCreateManid(userInfo.getRid());
			addList.add(userDesk);
		}
		this.saveBatchObjs(addList);
	}

	/**
	 *  <p>方法描述：验证层级是否存在重复</p>
	 * @MethodAuthor hsj 2024-01-26 10:25
	 */
	public boolean verifyLevelNo(TsMenu tsMenu) {
		StringBuffer hql = new StringBuffer("select t from TsMenu t where t.menuLevelNo=:menuLevelNo");
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("menuLevelNo",tsMenu.getMenuLevelNo());
		List<TsMenu> list = this.findDataByHqlNoPage(hql.toString(),paramMap);
		if(CollectionUtils.isEmpty(list)){
			return false;
		}
		if(ObjectUtil.isNull(tsMenu.getRid())){
			return true;
		}
		for(TsMenu tsMenu1: list){
			if(!tsMenu1.getRid().toString().equals(tsMenu.getRid().toString())){
				return  true;
			}
		}
		return false;
	}

	/**
	 *  <p>方法描述：更新</p>
	 * @MethodAuthor hsj 2024-01-26 15:53
	 */
	@Transactional(readOnly = false)
	public void saveState(List<Integer> succList, Map<Integer, String> errRsnMap) {
		if(!CollectionUtils.isEmpty(succList)){
			List<List<Integer>> lists = ListUtil.partition(succList, 1000);
			for(List<Integer> list:lists){
				Map<String, Object> paramMap = new HashMap<>();
				String sql = "UPDATE TS_MENU T SET T.STATE = 1,T.ERR_RSN = null where  T.RID in (:list)";
				paramMap.put("list",list);
				this.executeSql(sql,paramMap);
			}
		}
		if(errRsnMap == null || errRsnMap.size() == 0){
			return;
		}
		for(Map.Entry<Integer, String> entity : errRsnMap.entrySet()){
			Map<String, Object> paramMap = new HashMap<>();
			String sql = "UPDATE TS_MENU  T SET  T.STATE = 2, T.ERR_RSN=:errRsn where  T.RID =:rid";
			paramMap.put("rid",entity.getKey());
			paramMap.put("errRsn",entity.getValue());
			this.executeSql(sql,paramMap);
		}
	}

	/**
	 * <p>方法描述：查询树形码表类型 </p>
	 * pw 2024/1/30
	 **/
	@Transactional(readOnly = true)
	public List<TsCodeType> findTreeNodeCodeType() {
		String hql = " select t from TsCodeType t where t.treeTag='1' ";
		return this.findDataByHqlNoPage(hql, null);
	}

	/**
	 * <p>方法描述：查询树形码表信息点 </p>
	 * pw 2024/1/30
	 **/
	@Transactional(readOnly = true)
	public List<Object[]> findTreeSimpleCodeInfoByCodeType(Integer codeTypeId) {
		if (null == codeTypeId) {
			return Collections.emptyList();
		}
		String sql = " SELECT t.CODE_NO,t.CODE_LEVEL_NO,t.IF_REVEAL FROM TS_SIMPLE_CODE t WHERE t.CODE_TYPE_ID = "+codeTypeId+" ORDER BY t.CODE_LEVEL_NO ";
		return this.findDataBySqlNoPage(sql, null);
	}

	/**
	 * <p>方法描述：更新自检状态与自检失败信息 </p>
	 * pw 2024/1/30
	 **/
	@Transactional(readOnly = false)
	public void updateCodeTypeState(List<TsCodeType> codeTypeList){
		if (CollectionUtils.isEmpty(codeTypeList)) {
			return;
		}
		this.updateBatchObjs(codeTypeList);
	}

	/**
	 * <p>方法描述：码表存储 </p>
	 * pw 2024/2/1
	 **/
	public String saveOrUpdateSimpleCode(TsSimpleCode simpleCode, boolean ifTree) {
		Map<String,Object> paramMap = new HashMap<>();
		// 检查类别内编码的唯一性
		StringBuilder sb = new StringBuilder(" select t from TsSimpleCode t where t.tsCodeType.rid = ");
		sb.append(simpleCode.getTsCodeType().getRid()).append(" and t.codeNo =:codeNo ");
		if (null != simpleCode.getRid()) {
			sb.append(" and t.rid <>").append(simpleCode.getRid());
		}
		paramMap.put("codeNo",simpleCode.getCodeNo());
		List<TsSimpleCode> list = this.findDataByHqlNoPage(sb.toString(), paramMap);
		if (null != list && list.size() > 0) {
			return "编码不允许重复！";
		}

		Integer rid = simpleCode.getRid();
		if (null == rid) {
			simpleCode.setModifyDate(new Date());
			simpleCode.setUUID(UUID.randomUUID().toString());
			this.save(simpleCode);
			return null;
		}
		if (!ifTree) {
			this.update(simpleCode);
			return null;
		}
		String codeLevelNo = simpleCode.getCodeLevelNo();
		String[] levelArr = codeLevelNo.split("\\.");
		int length = levelArr.length;
		if (1 == length) {
			simpleCode.setCodePath(simpleCode.getCodeName());
		}
		StringBuffer msgBuffer = new StringBuffer();
		if (!codeLevelNo.substring(codeLevelNo.lastIndexOf(".")+1).equals(simpleCode.getCodeNo())) {
			msgBuffer.append("、").append("结构层次编码最末级与编码不一致");
		}
		TsSimpleCode lastSimpleCode = this.validateParentAndFindParentSimpleCode(simpleCode);
		if (1 != length && null == lastSimpleCode) {
			msgBuffer.append("、").append("上级结构层次编码不存在");
		}
		if (msgBuffer.length() > 0) {
			msgBuffer.append("！");
			return msgBuffer.substring(1);
		}
		int codePathSize = 500;
		if (1 != length && null != lastSimpleCode && StringUtils.isNotBlank(lastSimpleCode.getCodePath())) {
			String codePath = lastSimpleCode.getCodePath()+"_"+simpleCode.getCodeName();
			simpleCode.setCodePath(codePath.length() > codePathSize ? codePath.substring(0,codePathSize) : codePath);
		}
		String codePath = simpleCode.getCodePath();
		//校验层级编码中上级编码是否存在 校验层级编码是否正常
		List<TsSimpleCode> updateList = new ArrayList<>();
		updateList.add(simpleCode);
		TsSimpleCode dataBase = this.find(TsSimpleCode.class, rid);
		String oldCodeLevel = dataBase.getCodeLevelNo();
		String oldCodePath = dataBase.getCodePath();
		List<TsSimpleCode> subList = this.findSimpleCodeByParentLevelCode(oldCodeLevel,
				simpleCode.getTsCodeType().getRid());
		if (!CollectionUtils.isEmpty(subList)) {
			for (TsSimpleCode subCode : subList) {
				//更新子级节点的层级目录
				String curSubLevelNo = subCode.getCodeLevelNo();
				String concatCode = curSubLevelNo.substring(oldCodeLevel.length());
				curSubLevelNo = codeLevelNo+concatCode;
				subCode.setCodeLevelNo(curSubLevelNo);
				if (StringUtils.isBlank(codePath)) {
					continue;
				}
				//更新直接子级节点的codePath
				String curSubCodePath = subCode.getCodePath();
				String tmpCode = concatCode.startsWith(".") ? concatCode.substring(1) : concatCode;
				if (tmpCode.contains(".") && StringUtils.isBlank(curSubCodePath)) {
					continue;
				}
				if (StringUtils.isNotBlank(curSubCodePath) &&
						(StringUtils.isBlank(oldCodePath) || !curSubCodePath.startsWith(oldCodePath))) {
					continue;
				}
				if (StringUtils.isBlank(curSubCodePath)) {
					curSubCodePath = codePath+"_"+subCode.getCodeName();
				} else {
					curSubCodePath = codePath+curSubCodePath.substring(oldCodePath.length());
				}
				subCode.setCodePath(curSubCodePath.length()>codePathSize?curSubCodePath.substring(0,codePathSize) : curSubCodePath);
			}
			updateList.addAll(subList);
		}
		this.updateBatchObjs(updateList);
		return null;
	}

	/**
	 * <p>方法描述：通过父级层级编码找子节点 </p>
	 * pw 2024/2/1
	 **/
	public List<TsSimpleCode> findSimpleCodeByParentLevelCode(String codeLevelNo, Integer codeTypeId){
		if (StringUtils.isBlank(codeLevelNo) || null == codeTypeId) {
			return Collections.emptyList();
		}
		String hql = " select t from TsSimpleCode t where t.tsCodeType.rid="+codeTypeId+" and t.codeLevelNo like :parentCodeLevelNo ";
		Map<String,Object> paramMap = new HashMap<>();
		paramMap.put("parentCodeLevelNo",codeLevelNo+".%");
		return this.findDataByHqlNoPage(hql, paramMap);
	}

	/**
	 * <p>方法描述：判断上级结构层次编码是否存在 </p>
	 * pw 2024/2/1
	 **/
	public TsSimpleCode validateParentAndFindParentSimpleCode(TsSimpleCode simpleCode){
		if (null == simpleCode.getRid()) {
			return null;
		}
		String codeNo = simpleCode.getCodeNo();
		String codeLevelNo = simpleCode.getCodeLevelNo();
		String[] levelArr = codeLevelNo.split("\\.");
		int length = levelArr.length;
		if (1 == length) {
			return null;
		}
		List<String> existCodeList = new ArrayList<>();
		//校验 上级编码
		for (int i=0; i<length-1;i++) {
			String curCode = levelArr[i];
			//当前自己的编码出现在上级层级编码中
			if (codeNo.equals(curCode)) {
				return null;
			}
			//出现类似1001.1001.1003的情况
			if (existCodeList.contains(curCode)) {
				return null;
			}
			existCodeList.add(curCode);
		}
		Integer codeTypeRid = simpleCode.getTsCodeType().getRid();
		//上级层次编码
		String preLevelCode = codeLevelNo.substring(0, codeLevelNo.lastIndexOf("."));
		Map<String,Object> paramMap = new HashMap<>();
		paramMap.put("preLevelCode",preLevelCode);
		String queryHql = " select t from TsSimpleCode t where t.tsCodeType.rid="+codeTypeRid+" AND t.codeLevelNo = :preLevelCode ";
		List<TsSimpleCode> simpleCodeList = this.findDataByHqlNoPage(queryHql, paramMap);
		if (CollectionUtils.isEmpty(simpleCodeList)) {
			return null;
		}
		paramMap = new HashMap<>();
		paramMap.put("codeNoList", existCodeList);
		String querySql = "SELECT count(DISTINCT t.CODE_NO) FROM TS_SIMPLE_CODE t WHERE t.CODE_TYPE_ID = "+codeTypeRid+" AND t.rid <> "+simpleCode.getRid()+" AND t.CODE_NO IN (:codeNoList) ";
		int count = this.findCountBySql(querySql, paramMap);
		if (count == (length - 1)) {
			return simpleCodeList.get(0);
		}
		return null;
	}
}
