package com.chis.modules.system.service;

import java.util.Map;

import org.apache.poi.ss.formula.functions.T;
import org.primefaces.event.SelectEvent;

import com.chis.modules.system.logic.ExampoolNumFillBean;

/***
 * 题目选项配置
 * 规则处理接口
 * <AUTHOR>
 *
 */
public interface ProConfigFactor {

	/****
	 * @param 传入的参数 map<String,Object>
	 * 
	 */
	public void doConfigFactor(Map<String,Object> map);
	
	
	/**关闭dialog,返回参数处理方法
	 * @param event 
	 * @param num 选项值
	 * @param script 
	 * 
	 * @return script 处理后脚本
	 * */
	public String  doCloseDialogSelect(SelectEvent event,String num,String script,String qesCode);
	
	/**关闭dialog,返回参数处理方法
	 * @param event 
	 * @param Object entity
	 * @return Object entity
	 * */
	public Object doCloseDialogSelect(Object entity,SelectEvent event);
	
	
	/****
	 * 序号修改后更新脚本
	 * @param 初始选项值        startNum
	 * @param 变更后选项值    uptadedNum
	 * @param script
	 * 
	 * @return script
	 */
	public String updateScript(String startNum,String updateNum,String script);
	
	/****
	 * 根据选项值删除脚本
	 * 
	 */
	
	public String deleteScriptByNum(String num,String script);
}
