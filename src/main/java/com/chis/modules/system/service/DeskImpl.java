package com.chis.modules.system.service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.druid.pool.DruidDataSource;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.MenuVO;

/**
 * 
 * <AUTHOR>
 * @date 2014-4-10
 * @DeskImpl.java
 */
@Service
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class DeskImpl  extends AbstractTemplate{
	
	@Autowired
	private DruidDataSource dataSource;

	public DeskImpl() throws Exception {
		super();
	}
	
	@Transactional(readOnly = true)
	public List<MenuVO> findMyMenuList(String userno, Integer userId) {
		StringBuilder sb = new StringBuilder();
		if(userno.equals(Constants.ADMIN)) {
			sb.append(" SELECT LENGTH(REGEXP_REPLACE(T1.MENU_LEVEL_NO,'[^.]',NULL))  AS MENU_LEVEL,");
			sb.append(" T1.RID,T1.MENU_CN, T1.MENU_LEVEL_NO, T1.MENU_URI ");
			sb.append(" FROM TS_MENU T1 ");
			sb.append(" ORDER BY REGEXP_COUNT(T1.MENU_LEVEL_NO, '\\.'), T1.NUM ");
		}else {
			sb.append(" WITH M AS (");
			sb.append(" SELECT T1.RID,T1.MENU_CN, T1.MENU_LEVEL_NO, T1.MENU_URI, T1.NUM ");
			sb.append(" FROM TS_MENU T1 ");
			sb.append(" INNER JOIN TS_USER_MENU T2 ON T2.MENU_TEMPLATE_ID = T1.RID ");
			sb.append(" WHERE T2.USER_INFO_ID = '").append(userId).append("' ");
			sb.append(" UNION ");
			sb.append(" SELECT A1.RID, A1.MENU_CN, A1.MENU_LEVEL_NO, A1.MENU_URI, A1.NUM ");
			sb.append(" FROM TS_MENU A1 ");
			sb.append(" INNER JOIN TS_ROLE_MENU A2 ON A2.MENU_ID = A1.RID ");
			sb.append(" INNER JOIN TS_USER_ROLE A3 ON A3.ROLE_ID = A2.ROLE_ID ");
			sb.append(" WHERE A3.USER_INFO_ID = '").append(userId).append("' ");
			sb.append(" ) SELECT LENGTH(REGEXP_REPLACE(M.MENU_LEVEL_NO,'[^.]',NULL))  AS MENU_LEVEL, M.* ");
			sb.append(" FROM M ");
			sb.append(" ORDER BY REGEXP_COUNT(M.MENU_LEVEL_NO, '\\.'), M.NUM ");
		}
		List<Object[]> list = super.em.createNativeQuery(sb.toString()).getResultList();
		
		List<MenuVO> rtnList = new ArrayList<MenuVO>();
		//key-level_no 
		Map<String, MenuVO> menuMap = new HashMap<String, MenuVO>(); 
		
		if(null != list && list.size() > 0) {
			for(Object[] o : list) {
				MenuVO menu = new MenuVO();
				menu.setMenuLevel(null == o[0] ? 0 : Integer.parseInt(o[0].toString()));
				menu.setMenuRid(o[1].toString());
				menu.setMenuCn(o[2].toString());
				menu.setMenuLevelNo(o[3].toString());
				menu.setMenuUri(null == o[4] ? "#" : o[4].toString());
				
				menuMap.put(menu.getMenuLevelNo(), menu);
				if(menu.getMenuLevel() == 0) {
					rtnList.add(menu);
				}else {
					String parentCode = StringUtils.substring(menu.getMenuLevelNo(), 0, StringUtils.lastIndexOf(menu.getMenuLevelNo(), "."));
					MenuVO parentMenu = menuMap.get(parentCode);
					parentMenu.getChildrenList().add(menu);
				}
			}
			menuMap.clear();
		}
		return rtnList;
	}
	
	/**
	 * 根据用户编码查询桌面能显示的菜单
	 * 
	 * <AUTHOR>
	 * @histroy 2013-11-26
	 * @return List<RID,:,所在桌面号,:,排序号,#,菜单名称,_,图标,_,连接地址>
	 */
	@Transactional(readOnly = true)
	public List<String> getDeskMMenu(String userno) throws Exception {
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT E.R1 FROM (");
		sql.append(" SELECT A6.RID||',:,'||A6.DESK_NO||',:,'||A6.ORDER_NO||',#,'||A2.menu_cn||',_,'||A2.MENU_ICON||',_,'||A2.MENU_URI||',_,'||A2.BIG_ICON||',_,'||A2.RID as R1, A6.DESK_NO AS DNO,A6.ORDER_NO AS ONO,A6.RID as rid");
		sql.append(" FROM TS_USER_MENU A1");
		sql.append(" INNER JOIN TS_MENU A2 ON A2.RID=A1.MENU_TEMPLATE_ID");
		sql.append(" INNER JOIN TS_USER_INFO A3 ON A3.RID=A1.USER_INFO_ID");
		sql.append(" INNER JOIN TS_USER_DESK A6 ON A6.MENU_ID=A2.RID AND A6.USER_ID=A3.RID");
		sql.append(" WHERE A3.USER_NO= ?1 ");
		sql.append(" UNION");
		sql.append(" SELECT B6.RID||',:,'||B6.DESK_NO||',:,'||B6.ORDER_NO||',#,'||B2.menu_cn||',_,'||B2.MENU_ICON||',_,'||B2.MENU_URI||',_,'||B2.BIG_ICON||',_,'||B2.RID as R1,B6.DESK_NO AS DNO,B6.ORDER_NO AS ONO,B6.RID as rid");
		sql.append(" FROM TS_USER_ROLE B1");
		sql.append(" INNER JOIN TS_USER_INFO B3 ON B3.RID=B1.USER_INFO_ID");
		sql.append(" INNER JOIN TS_ROLE_MENU B5 ON B5.ROLE_ID=B1.ROLE_ID");                                  
		sql.append(" INNER JOIN TS_MENU B2 ON B2.RID=B5.MENU_ID");
		sql.append(" INNER JOIN TS_USER_DESK B6 ON B6.MENU_ID=B2.RID AND B6.USER_ID=B3.RID");
		sql.append(" WHERE B3.USER_NO= ?2 ");
		sql.append(" )E ORDER BY E.DNO,E.ONO");
		Query q = em.createNativeQuery(sql.toString());
		q.setParameter(1, userno);
		q.setParameter(2, userno);
		return q.getResultList();
	}
	
	@Transactional(readOnly = true)
	public List<String> getDeskSysMenu(Integer userid) throws Exception{
		StringBuilder sb=new StringBuilder();
		sb.append(" select a.rid||',:,'||1||',:,'||0||',#,'||b.sys_name||',_,'||b.sys_icon||',_,'||nvl");
		sb.append(" (b.sys_url,a.sys_url)||',_,'||b.xt_type||',#,'||c.enco||',#,'||c.vlco");
		sb.append(" as value from ts_dsf_loginf a");
		sb.append(" left join ts_dsf_sys b on a.dsf_sys_id=b.rid");
		sb.append(" left join(select loginf_id,wm_concat(param_en)enco,wm_concat(param_value)vlco from ");
		sb.append(" ts_dsf_loginf_param group by loginf_id)c on a.rid=c.loginf_id");
		sb.append(" where a.create_manid=").append(userid);
		return em.createNativeQuery(sb.toString()).getResultList();
	}

	public List<String> getDeskLoginValue(Integer userid) throws Exception{
		StringBuilder sb=new StringBuilder();
		sb.append(" select a.rid||',_,'||b.xt_type||',_,'||nvl(b.sys_url,a.sys_url)||',_,'||c.param_en||',_,'||c.param_value||',_,'||d.param_en||',_,'||d.param_value as value from ts_dsf_loginf a");
		sb.append(" left join ts_dsf_sys b on a.dsf_sys_id=b.rid");
		sb.append(" left join (select * from ts_dsf_loginf_param where rid in(");
		sb.append(" select min(rid) from ts_dsf_loginf_param group by loginf_id)) c on a.rid=c.loginf_id");
		sb.append(" left join (select * from ts_dsf_loginf_param where rid in(");
		sb.append(" select max(rid) from ts_dsf_loginf_param group by loginf_id)) d on a.rid=d.loginf_id");
		sb.append(" where a.create_manid=").append(userid);
		return em.createNativeQuery(sb.toString()).getResultList();
	}
	
	public void UpdateMenuConnListTx(List<String> dmlist) throws Exception {
		StringBuffer updSql = new StringBuffer();
		updSql.append(" begin ");
		int dno = 0;
		for (int i = 0; i < dmlist.size(); i++) {
			String rid = dmlist.get(i).split(",#,")[0].split(",:,")[0];
			if (i / 24 == dno) {
				dno++;
			}
			updSql.append(" update TS_USER_DESK t set t.DESK_NO=");
			updSql.append(dno);
			updSql.append(",t.ORDER_NO=");
			updSql.append(i);
			updSql.append(" where t.rid=");
			updSql.append(rid);
			updSql.append(";");
		}
		updSql.append(" end; ");
		Query q = em.createNativeQuery(updSql.toString());
		q.executeUpdate();
	}

	public void UpdateMenuConnSingleTx(String deskNo, String rid) throws Exception {
		StringBuffer upd = new StringBuffer();
		deskNo = deskNo.substring(1, deskNo.length());
		String[] nos = deskNo.split(",");
		upd.append("begin ");
		for (int i = 0; i < nos.length; i++) {
			upd.append("update TS_USER_DESK e set e.desk_no=");
			upd.append(nos[i].split(":")[0]);
			upd.append(" where e.desk_no=");
			upd.append(nos[i].split(":")[1]);
			upd.append(" and e.user_id=");
			upd.append(rid);
			upd.append(";");
		}
		upd.append(" end; ");
		Query q = em.createNativeQuery(upd.toString());
		q.executeUpdate();

	}

	@Transactional(readOnly = true)
	public List<Object[]> getUserMenu(String userno,int leavl) throws Exception {
		StringBuffer whereStr=new StringBuffer();
        if(leavl==1){
            //一级菜单
            whereStr.append(" and m.ISFUNC=0 and instr(m.menu_level_no,'.',1,1)=0");
        }
        if(leavl==2){
            //二级菜单
            whereStr.append(" and   instr(m.menu_level_no,'.',1,2)=0 and instr(m.menu_level_no,'.',1,1)>0");
        }
        if(leavl==3){
            //三级菜单
            whereStr.append(" and m.ISFUNC=1 and instr(m.menu_level_no,'.',1,2)>0");
        }
		if(!userno.equals(Constants.ADMIN)){
		StringBuffer sqlStr = new StringBuffer();		
		sqlStr.append(" select * from (");
		sqlStr.append(" select   m.rid,m.menu_level_no,m.menu_cn,m.menu_en,m.menu_simple,m.isfunc,m.menu_uri,m.menu_icon,m.create_date,m.create_manid,m.modify_date,m.modify_manid,m.num,m.big_icon,m.IF_POP from TS_USER_MENU  um ");
		sqlStr.append(" join TS_MENU m on m.rid=um.menu_template_id");
		sqlStr.append(" join TS_USER_INFO u on u.rid=um.user_info_id");
		sqlStr.append(" where u.user_no = ?1");
		sqlStr.append(whereStr);
		sqlStr.append(" union ");
		sqlStr.append(" select    m.rid,m.menu_level_no,m.menu_cn,m.menu_en,m.menu_simple,m.isfunc,m.menu_uri,m.menu_icon,m.create_date,m.create_manid,m.modify_date,m.modify_manid,m.num,m.big_icon,m.IF_POP  from TS_ROLE_MENU rm ");
		sqlStr.append(" join TS_MENU m on m.rid=rm.menu_id");
		sqlStr.append(" join TS_USER_ROLE r on r.role_id=rm.role_id");
		sqlStr.append(" join TS_USER_INFO u on  u.rid=r.user_info_id");
		sqlStr.append(" where u.user_no = ?2");
		sqlStr.append(whereStr);
		sqlStr.append(" ) a  order by a.num");
		
		System.out.println("sqlStr:" + sqlStr);
		Query q = em.createNativeQuery(sqlStr.toString());
		q.setParameter(1, userno);
		q.setParameter(2, userno);
		return  q.getResultList();
		}else{
			StringBuffer sqlStr = new StringBuffer();
			sqlStr.append(" select   m.rid,m.menu_level_no,m.menu_cn,m.menu_en,m.menu_simple,m.isfunc,m.menu_uri,m.menu_icon,m.create_date,m.create_manid,m.modify_date,m.modify_manid,m.num,m.big_icon,m.IF_POP from TS_MENU m  where 1=1 ");
			sqlStr.append(whereStr);
            sqlStr.append(" order by m.num");
			Query q = em.createNativeQuery(sqlStr.toString());
			return  q.getResultList();
		}
	}

	@Transactional(readOnly = true)
	public LinkedList<LinkedList<String>> getDeskUMenu(String userno) throws Exception {
        //判断是否为超管
        boolean flag = Constants.ADMIN.equals(userno)?true:false;
		StringBuffer sql = new StringBuffer();
		LinkedList<LinkedList<String>> rstlist = new LinkedList<LinkedList<String>>();
		LinkedList<String> obj = new LinkedList<String>();
		sql.append(" SELECT '['||T.UPCN||',:,'||LISTAGG(T.MENU_ID||',_,'||T.MENU_CN||',_,'||T.CON||',_,'||T.BIG_ICON,");
		sql.append(" ',#,') WITHIN GROUP(ORDER BY T.NUM)||']' AS R1");
		sql.append(" FROM (  ");
		sql.append(" SELECT DISTINCT");
		sql.append(" (SELECT M.MENU_SIMPLE FROM TS_MENU M   WHERE M.MENU_LEVEL_NO= SUBSTR(B8.MENU_LEVEL_NO,0,DECODE(INSTR(B8.MENU_LEVEL_NO,'.',-1,1),0,LENGTH(B8.MENU_LEVEL_NO),3))) AS UPCN,");
			   
		sql.append(" B3.RID AS MENU_ID, B3.MENU_SIMPLE AS MENU_CN,");
		sql.append(" NVL2(B7.USER_ID,1,0) AS CON, B3.BIG_ICON, B3.MENU_LEVEL_NO,B3.NUM,B8.NUM AS BIGNUM");
		sql.append(" FROM TS_USER_ROLE B1");
		sql.append(" INNER JOIN TS_ROLE_MENU B2 ON B1.ROLE_ID = B2.ROLE_ID");
		sql.append(" INNER JOIN TS_MENU B3 ON B2.MENU_ID = B3.RID AND B3.ISFUNC = '1'");
		sql.append(" INNER JOIN TS_USER_INFO B6 ON B1.USER_INFO_ID = B6.RID");
		sql.append(" LEFT JOIN TS_USER_DESK B7 ON B7.USER_ID = B6.RID AND B7.MENU_ID = B3.RID");
		sql.append(" INNER JOIN TS_MENU B8 ON B8.ISFUNC = '0'");
		sql.append(" AND SUBSTR(B3.MENU_LEVEL_NO,0,DECODE(INSTR(B3.MENU_LEVEL_NO,'.',-1,1),0,LENGTH(B3.MENU_LEVEL_NO),INSTR(B3.MENU_LEVEL_NO,'.',-1,1)-1)) = B8.MENU_LEVEL_NO");
		if(!flag) {
            sql.append(" WHERE B6.USER_NO = ?1 ");
        }
		sql.append(" UNION  ");    
		sql.append(" SELECT DISTINCT (SELECT M.MENU_SIMPLE FROM TS_MENU M WHERE M.MENU_LEVEL_NO= SUBSTR(A7.MENU_LEVEL_NO,0,DECODE(INSTR(A7.MENU_LEVEL_NO,'.',-1,1),0,LENGTH(A7.MENU_LEVEL_NO),3)))  AS UPCN,A2.RID AS MENU_ID,A2.MENU_SIMPLE AS MENU_CN,");
		sql.append(" NVL2(A6.USER_ID, 1, 0) AS CON, A2.BIG_ICON,A2.MENU_LEVEL_NO,A2.NUM,A7.NUM AS BIGNUM");
		sql.append(" FROM TS_USER_MENU A1");
		sql.append(" INNER JOIN TS_MENU A2 ON A1.MENU_TEMPLATE_ID = A2.RID AND A2.ISFUNC = '1'");
			  
		sql.append(" INNER JOIN TS_USER_INFO A5 ON A1.USER_INFO_ID = A5.RID ");
		sql.append(" LEFT JOIN TS_USER_DESK A6 ON A6.USER_ID = A5.RID AND A6.MENU_ID = A2.RID");
		sql.append(" INNER JOIN TS_MENU A7 ON A7.ISFUNC = '0'  ");
		sql.append(" AND SUBSTR(A2.MENU_LEVEL_NO,0,DECODE(INSTR(A2.MENU_LEVEL_NO,'.',-1,1),0,LENGTH(A2.MENU_LEVEL_NO),INSTR(A2.MENU_LEVEL_NO,'.',-1,1)-1)) = A7.MENU_LEVEL_NO");
		if(!flag) {
            sql.append(" WHERE A5.USER_NO = ?2 ");
        }
		sql.append(" ) T ");
		sql.append(" GROUP BY T.UPCN");//,T.BIGNUM
		//sql.append(" ORDER BY  T.BIGNUM");
		Query q = em.createNativeQuery(sql.toString());
        if(!flag) {
            q.setParameter(1, userno);
            q.setParameter(2, userno);
        }
			List<Object> list = q.getResultList();
			StringBuffer addTagIdxsStr = new StringBuffer();
			StringBuffer delTagIdxsStr = new StringBuffer();
			if(null!=list&&list.size()>0){
				for (Object rs:list) {
					String funStr = String.valueOf(rs);
					obj.add(funStr);
					if (null != funStr && !"".equals(funStr.trim())) {
						String[] fas = funStr.split(",:,");
						if (fas.length > 1 && !"".equals(fas[1])) {
							String[] submS = fas[1].split(",#,");
							if (submS.length > 0) {
								for (int k = 0; k < submS.length; k++) {
									if (!"".equals(submS[k].split(",_,")[0])) {
										if ("1".equals(submS[k].split(",_,")[2])) {
											addTagIdxsStr.append(",").append(submS[k].split(",_,")[0]).append(",");
										} else {
											delTagIdxsStr.append(",").append(submS[k].split(",_,")[0]).append(",");
										}
									}
								}
							}
	
						}
					}
				}
			}
			rstlist.add(obj);
			LinkedList<String> obj2 = new LinkedList<String>();
			obj2.add(addTagIdxsStr.toString());
			obj2.add(delTagIdxsStr.toString());
			rstlist.add(obj2);
		return rstlist;
	}
	
	/**获得未维护的系统集合**/
	public List<Object[]> getUndefendList(Integer userid){
		StringBuilder sb =new StringBuilder();
		sb.append("select rid,sys_name,sys_icon,nvl(sys_url,' ')sys_url from ts_dsf_sys where rid not in (select DSF_SYS_ID from TS_DSF_LOGINF where create_manid =");
		sb.append(userid).append(")");
		List<Object[]> list=em.createNativeQuery(sb.toString()).getResultList();
		return list;
	}
	
	/**获得已维护的系统集合**/
	public List<Object[]> getdefendList(Integer userid){
		StringBuilder sb =new StringBuilder();
		sb.append("select a.rid,a.sys_name,a.sys_icon,nvl(a.sys_url,b.sys_url)sys_url from ts_dsf_sys a");
		sb.append(" left join ts_dsf_loginf b on a.rid=b.dsf_sys_id");
		sb.append(" where b.create_manid=").append(userid);
		List<Object[]> list=em.createNativeQuery(sb.toString()).getResultList();
		return list;
	}

	public void upd(String userid, String addTagIdxs, String delTagIdxs) throws Exception {
		Connection conn = dataSource.getConnection();
		PreparedStatement pstmt = null;
		StringBuffer sql = new StringBuffer(" begin ");
		if (null != addTagIdxs && !"".equals(addTagIdxs.trim())) {
			// 已存在的菜单记录id
			String ysIdxs = null;
			// 记录去除需删除菜单后的桌面号及对应的菜单数量
			Map<Integer, Integer> deskNumsMap = new HashMap<Integer, Integer>();
			if (null != delTagIdxs && !"".equals(delTagIdxs.trim())) {
				sql.append(" delete from TS_USER_DESK xumc where xumc.USER_ID ='").append(userid).append("'  and xumc.MENU_ID in(")
						.append(delTagIdxs).append(");");

				StringBuffer selSql = new StringBuffer();
				// 已存在的菜单查询
				selSql.append(
						" select listagg('@'||xumc.MENU_ID||'@') within group(order by xumc.MENU_ID) as menus from TS_USER_DESK xumc where xumc.USER_ID ='")
						.append(userid).append("' and xumc.MENU_ID not in(").append(delTagIdxs).append(")");
				PreparedStatement pstmt1 = conn.prepareStatement(selSql.toString());
				ResultSet rs = pstmt1.executeQuery();
				if (rs.next()) {
					ysIdxs = rs.getString("menus");
				}
				rs.close();
				pstmt1.close();
				// 查询去除需要删除的菜单，获取桌面号及对应的菜单还剩数量
				selSql = new StringBuffer();
				selSql.append(" select  t.desk_no,count(t.rid) as nums from TS_USER_DESK t");
				selSql.append(" where t.user_id ='").append(userid).append("' and t.desk_no is not null and t.MENU_ID not in(").append(delTagIdxs)
						.append(")");
				selSql.append("  group by t.desk_no");
				PreparedStatement pstmt2 = conn.prepareStatement(selSql.toString());
				ResultSet rs2 = pstmt2.executeQuery();

				while (rs2.next()) {
					deskNumsMap.put(rs2.getInt("desk_no"), rs2.getInt("nums"));
				}
				rs2.close();
				pstmt2.close();

			} else {
				// 全部添加（无删除记录）
				// 已存在的菜单查询
				StringBuffer selSql = new StringBuffer();
				selSql.append(
						" select listagg('@'||xumc.MENU_ID||'@') within group(order by xumc.MENU_ID) as menus from TS_USER_DESK xumc where xumc.USER_ID ='")
						.append(userid).append("' and xumc.MENU_ID  in(").append(addTagIdxs).append(")");
				PreparedStatement pstmt1 = conn.prepareStatement(selSql.toString());
				ResultSet rs = pstmt1.executeQuery();
				if (rs.next()) {
					ysIdxs = rs.getString("menus");
				}
				// 查询去除已有的菜单，获取桌面号及对应的菜单还剩数量
				selSql = new StringBuffer();
				selSql.append(" select  t.desk_no,count(t.rid) as nums from TS_USER_DESK t");
				selSql.append(" where t.user_id ='").append(userid).append("' and t.desk_no is not null and t.MENU_ID  in(").append(addTagIdxs)
						.append(")");
				selSql.append("  group by t.desk_no");
				PreparedStatement pstmt2 = conn.prepareStatement(selSql.toString());
				ResultSet rs2 = pstmt2.executeQuery();

				while (rs2.next()) {
					deskNumsMap.put(rs2.getInt("desk_no"), rs2.getInt("nums"));
				}
				rs2.close();
				pstmt2.close();

			}
			String[] addids = addTagIdxs.split(",");
			if (null != deskNumsMap && deskNumsMap.size() > 0) {
				int dno = 1;
				if (null == deskNumsMap.get(dno)) {
					// 更新桌面号从1开始
					StringBuffer updDEK = new StringBuffer();
					updDEK.append(" update TS_USER_DESK t set t.desk_no=(t.desk_no-1) where t.user_id ='").append(userid)
							.append("' and t.desk_no is not null and t.MENU_ID not in(").append(delTagIdxs).append(")");
					// System.err.println("更新桌面号从1开始updDEK:"+updDEK.toString());
					PreparedStatement pstmt3 = conn.prepareStatement(updDEK.toString());
					pstmt3.execute();
					pstmt3.close();
					// 查询去除需要删除的菜单，获取桌面号及对应的菜单还剩数量
					StringBuffer selSql = new StringBuffer();
					selSql.append(" select  t.desk_no,count(t.rid) as nums from TS_USER_DESK t");
					selSql.append(" where t.user_id ='").append(userid).append("' and t.desk_no is not null and t.MENU_ID not in(").append(delTagIdxs)
							.append(")");
					selSql.append("  group by t.desk_no ");
					System.err.println("selSql:" + selSql.toString());
					PreparedStatement pstmt2 = conn.prepareStatement(selSql.toString());
					ResultSet rs2 = pstmt2.executeQuery();

					while (rs2.next()) {
						deskNumsMap.put(rs2.getInt("desk_no"), rs2.getInt("nums"));
					}
					rs2.close();
					pstmt2.close();
				}
				// 记录需要添加的菜单起始标记
				int addTag = 0;
				// 桌面最大10个
				for (int d = dno; d < 11; d++) {
					boolean hasnext = false;
					int[] deskNums = getDeskNo(deskNumsMap, d);
					d = deskNums[1];
					int nums = deskNums[0];
					for (int i = addTag; i < addids.length; i++) {
						if (null == ysIdxs || "".equals(ysIdxs.trim()) || ysIdxs.indexOf("@" + addids[i] + "@") == -1) {
							if (nums / 24 == 1) {
								// d++;
								hasnext = true;
								addTag = i;
								break;
							}
							sql.append(" insert into TS_USER_DESK (RID,USER_ID,MENU_ID,desk_no,CREATE_DATE,CREATE_MANID) values (TS_USER_DESK_SEQ.Nextval,");
							sql.append(userid).append(" ,").append(addids[i]).append(",").append(d).append(",sysdate,").append(userid).append("); ");
							nums++;
						}
					}
					if (!hasnext) {
						break;
					}
				}

			} else {
				// 没有桌面
				int dno = 1;
				int no = 0;
				for (int i = 0; i < addids.length; i++) {
					if (null == ysIdxs || "".equals(ysIdxs.trim()) || ysIdxs.indexOf("@" + addids[i] + "@") == -1) {

						if (no / 24 == dno) {
							dno++;
						}
						sql.append(" insert into TS_USER_DESK (RID,USER_ID,MENU_ID,desk_no,CREATE_DATE,CREATE_MANID) values (TS_USER_DESK_SEQ.Nextval,");
						sql.append(userid).append(" ,").append(addids[i]).append(",").append(dno).append(",sysdate,").append(userid).append("); ");
						no++;
					}
				}

			}
		} else {
			// 没有需要添加的，直接删除所有
			sql.append(" delete from TS_USER_DESK xumc where xumc.USER_ID='").append(userid).append("'; ");
		}
		sql.append(" end; ");
		System.err.println(">>>" + sql.toString());
		pstmt = conn.prepareStatement(sql.toString());
		pstmt.execute();
		if (null != pstmt) {
			pstmt.close();
		}
	}
	
/*	private Connection getConnection() throws NamingException, SQLException {
		try {
			InitialContext ic = new InitialContext();
			javax.sql.DataSource ds = (javax.sql.DataSource) ic.lookup("core-base");
			return ds.getConnection();
		} catch (Exception e) {
			throw new SQLException("获取核心数据库数据源core_base失败!" + e.toString());
		}
	}*/
	
	public void UpdateMenuConnTx(String tagertNo, String userRid, String moveRid) throws Exception {
		StringBuffer sql = new StringBuffer();
		sql.append("update TS_USER_DESK t set t.DESK_NO=");
		sql.append(tagertNo);
		sql.append(",t.ORDER_NO=(select max(decode(s.ORDER_NO,null,0,s.ORDER_NO))+1 from TS_USER_DESK s where s.DESK_NO=");
		sql.append(tagertNo);
		sql.append(" and s.user_id='");
		sql.append(userRid);
		sql.append("' ) where t.rid=");
		sql.append(moveRid.replaceAll("_RD", ""));
		Query q = em.createNativeQuery(sql.toString());
		q.executeUpdate();
	}

	public void UpdateMenuConnStrTx(String[] rids, String deskNo) throws Exception {
		StringBuffer sql = new StringBuffer(" begin ");
		for (int i = 0; i < rids.length; i++) {
			sql.append(" update TS_USER_DESK t set t.DESK_NO=");
			sql.append(deskNo);
			sql.append(",t.ORDER_NO=");
			sql.append(i);
			sql.append(" where t.rid=");
			sql.append(rids[i]);
			sql.append(";");
		}
		sql.append(" end; ");
		Query q = em.createNativeQuery(sql.toString());
		q.executeUpdate();
	}
	/**
	 * 获取桌面上的未填满的数据
	 * 
	 * @param deskNumsMap
	 * @param deskno
	 * @return {起始号，桌号}
	 */
	private int[] getDeskNo(Map<Integer, Integer> deskNumsMap, int deskno) {

		if (null != deskNumsMap.get(deskno)) {
			int nums = deskNumsMap.get(deskno);
			if (nums / 24 == 1) {
				deskno++;
				return getDeskNo(deskNumsMap, deskno);
			} else {
				int[] num = { nums, deskno };
				return num;
			}
		} else {
			int[] num = { 0, deskno };
			return num;
		}
	}

	@Transactional(readOnly=true)
	public String getDesc() {
		return "系统登录服务";
	}

	@Transactional(readOnly=true)
	public String getVersion() {
		return "1.0.0";
	}
}
