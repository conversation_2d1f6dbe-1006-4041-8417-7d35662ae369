package com.chis.modules.system.service;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.entity.TsZone;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.modules.system.utils.Global;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>类描述：门户待办任务service</p>
 * @ClassAuthor qrr,2021年12月2日,PorTalToDoTasksService
 * */
@Service
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class PorTalToDoTasksService extends AbstractTemplate{
	/**
 	 * <p>方法描述：机构个案审核待办汇总数据</p>
 	 * @MethodAuthor qrr,2021年12月2日,orgToDoTdTjBHkCheck
	 * */
	@Transactional(readOnly = true)
	public int orgToDoTdTjBHkCheck() {
		StringBuffer sql = new StringBuffer();
		Map<String,Object> paramMap = new HashMap<>();
		sql.append(" SELECT COUNT(*) ");
		sql.append(" FROM TD_TJ_BHK T ");
		sql.append(" INNER JOIN TB_TJ_SRVORG T3 ON T3.RID = T.BHKORG_ID ");
		sql.append(" INNER JOIN TB_TJ_CRPT T1 ON T1.RID = T.ENTRUST_CRPT_ID ");
		sql.append(" INNER JOIN TS_ZONE T4 ON T4.RID = T1.ZONE_ID ");
		sql.append(" WHERE 1=1 AND T.IF_INTO_CHECK =1 ");
		sql.append(" AND T3.REG_ORGID= ").append(Global.getUser().getTsUnit().getRid());
		sql.append(" AND T.STATE IN (0,2,4,7) ");//已退回
		TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		if (ObjectUtil.isNotEmpty(tsZone)
				&& ObjectUtil.isNotEmpty(tsZone.getZoneGb()) && tsZone.getZoneGb().length() >= 2) {
			String provZoneGb = tsZone.getZoneGb().substring(0, 2);
			sql.append(" AND T4.ZONE_GB LIKE :zonecode escape '\\\'");
			paramMap.put("zonecode", StringUtils.convertBFH(provZoneGb) + "%");
		}
		return this.findCountBySql(sql.toString(), paramMap);
	}

	/**
	 * @Description: 个案审核 待审核数据条数
	 *
	 * @MethodAuthor pw,2021年12月2日
	 */
	@Transactional(readOnly = true)
	public int orgTdTjBhkDataUnCheckCount(String checkLevel){
		TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		Integer zoneType = null;
		if (null == tsZone) {
			tsZone = Global.getUser().getTsUnit().getTsZone();
		}
		if(null == tsZone.getRealZoneType()){
			zoneType = tsZone.getZoneType().intValue();
		}else {
			zoneType = tsZone.getRealZoneType().intValue();
		}
		if(null == zoneType){
			return 0;
		}
		String state = null;
		if("2".equals(checkLevel)){//2级审核
			if (zoneType <= 3) {//终审
				state = "5";
			}else {//初审
				state = "1";
			}
		}else if ("3".equals(checkLevel)) {//3级审核
			if (zoneType == 2) {//终审
				state = "5";
			}else if (zoneType == 3){//复审
				state = "3";
			}else {//初审
				state = "1";
			}
		}
		if(null == state){
			return 0;
		}
		String searchZoneCode = tsZone.getZoneGb();
		Map<String,Object> paramMap = new HashMap<>();
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT COUNT(*) FROM TD_TJ_BHK T ");
		sql.append(" LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.ENTRUST_CRPT_ID ");
		sql.append(" LEFT JOIN TB_TJ_SRVORG T3 ON T3.RID = T.BHKORG_ID ");
		sql.append(" LEFT JOIN TS_ZONE T4 ON T4.RID = T1.ZONE_ID ");
		sql.append(" WHERE 1=1 AND T.IF_INTO_CHECK =1 ");
		if (StringUtils.isNotBlank(searchZoneCode)) {
			sql.append(" AND T4.ZONE_GB LIKE :zonecode escape '\\\'");
			paramMap.put("zonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(searchZoneCode).trim()) + "%");
		}
		sql.append(" AND (T.STATE = '").append(state).append("' ");
		if ("3".equals(checkLevel)) {
			if (zoneType == 3) {
				if(state.contains("3")){//市级审核-待审核（3+市直属状态为待初审）
					sql.append(" OR (T4.IF_CITY_DIRECT=1 AND T.STATE=1)");
				}
			}
		}
		sql.append(")");
		return this.findCountBySql(sql.toString(),paramMap);
	}

	/**
	 *
	 * <p>描述：查询超范围预警待处置数量</p>
	 *
	 *  @Author: 龚哲,2021/12/2 16:43,tdZwyjOtrWarnCount
	 */
	@Transactional(readOnly = true)
	public int tdZwyjOtrWarnCount(Integer checkType){
		TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		Map<String,Object> paramMap = new HashMap<>();
		StringBuffer sql = new StringBuffer("SELECT COUNT(*) ");
		sql.append(" FROM TD_ZWYJ_OTR_WARN T");
		sql.append(" LEFT JOIN TS_ZONE T4 ON T.CITY_ZONE_ID = T4.RID ");
		sql.append(" WHERE 1=1 ");
		//地区
		if(checkType ==1){
			sql.append(" AND T4.ZONE_GB LIKE :cityzonecode escape '\\\'");
			paramMap.put("cityzonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(tsZone.getZoneCode()).trim()) + "%");
		}
		if (checkType == 2) {
			sql.append(" AND T.STATE_MARK = 1 ");
		}  else {
			sql.append(" AND T.STATE_MARK IN (0,2) ");
		}
		return this.findCountBySql(sql.toString(),paramMap);
	}

	/**
	* <p>Description：查询疑似职业病未报告数 </p>
	* <p>Author： yzz 2023-10-11 </p>
	*/
	public List<Object[]> findRptWarnCount(String bhkBeginDate, Integer receiveDate) {
		TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		String searchZoneCode=tsZone.getZoneGb().substring(0,2)+"00000000";
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT * ");
		sql.append(" FROM (SELECT distinct NULL             AS RID,T.RID            AS       REL_BHK_ID, ");
		if(new Integer("1").equals(receiveDate)){
			sql.append(" T.RPT_PRINT_DATE AS RCV_DATE");
		}else{
			sql.append(" T.BHK_DATE AS RCV_DATE");
		}
		sql.append(" 		,T9.code_name ");
		sql.append(" 		FROM TD_TJ_BHK T ");
		sql.append(" 		INNER JOIN TB_TJ_SRVORG T1 ON T.BHKORG_ID = T1.RID ");
		sql.append(" 		INNER JOIN TS_UNIT T2 ON T1.REG_ORGID = T2.RID ");
		sql.append(" 		LEFT JOIN TB_TJ_CRPT T4 ON T.ENTRUST_CRPT_ID = T4.RID ");
		sql.append(" 		LEFT JOIN TS_ZONE T5 ON T4.ZONE_ID = T5.RID ");
		sql.append(" 		LEFT JOIN TD_TJ_MHKRST T6 ON T6.BHK_ID = T.RID ");
		sql.append(" 		LEFT JOIN TS_SIMPLE_CODE T7 ON T6.BHKRST_ID = T7.RID ");
		sql.append(" 		Inner JOIN TD_TJ_SUPOCCDISELIST t8 on t8.BHK_ID = t.rid ");
		sql.append(" 		LEFT JOIN TS_SIMPLE_CODE T9 ON T9.RID = T8.OCC_DISEID ");
		sql.append(" 		WHERE 1 = 1 ");
		sql.append(" 		AND T.BHK_TYPE IN (3, 4) ");
		sql.append(" 		AND T4.INTER_PRC_TAG = 1 ");
		sql.append(" 		AND T7.EXTENDS2 = 5 ");
		sql.append(" 		AND NOT EXISTS(SELECT 1 ");
		sql.append(" 				FROM TD_ZW_YSZYB_RPT TT ");
		sql.append(" 				INNER JOIN TD_ZW_BGK_LAST_STA TT1 ON TT1.BUS_ID = TT.RID ");
		sql.append(" 				INNER JOIN TS_SIMPLE_CODE TT2 ON TT2.RID = TT.SOURCE_ID ");
		sql.append(" 				WHERE NVL(TT.DEL_MARK, 0) = 0 ");
		sql.append(" 				AND TT.REL_BHK_ID = T.RID ");
		sql.append(" 				and tt.OCC_DISEID = T8.OCC_DISEID ");
		sql.append(" 				AND TT2.EXTENDS1 = 1 ");
		sql.append(" 				AND TT1.CART_TYPE = 2) ");
		sql.append(" 		AND T2.RID = ").append(Global.getUser().getTsUnit().getRid());
		if(new Integer("1").equals(receiveDate)) {
			sql.append(" 		AND T.RPT_PRINT_DATE >= TO_DATE('").append(bhkBeginDate).append("', 'yyyy-MM-dd') ");
			sql.append(" 		AND T.RPT_PRINT_DATE <= TO_DATE('").append(DateUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss")).append("', 'YYYY-MM-DD HH24:MI:SS') ");
		}else{
			sql.append(" 		AND T.BHK_DATE >= TO_DATE('").append(bhkBeginDate).append("', 'yyyy-MM-dd') ");
			sql.append(" 		AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss")).append("', 'YYYY-MM-DD HH24:MI:SS') ");
		}
		sql.append(" 		AND T5.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(searchZoneCode)).append("%' ");
		sql.append(" UNION ALL ");
		sql.append(" SELECT T.RID,T.REL_BHK_ID                                                                AS REL_BHK_ID, ");
		sql.append(" 		T4.ORG_RCV_DATE                                                             AS RCV_DATE ");
		sql.append(" 		,t5.CODE_NAME ");
		sql.append(" FROM TD_ZW_YSZYB_RPT T ");
		sql.append(" INNER JOIN TS_UNIT T2 ON T.RPT_UNIT_ID = T2.RID ");
		sql.append(" LEFT JOIN TS_ZONE T3 ON T.EMP_ZONE_ID = T3.RID ");
		sql.append(" INNER JOIN TD_ZW_BGK_LAST_STA T4 ON T4.BUS_ID = T.RID ");
		sql.append(" LEFT join TS_SIMPLE_CODE t5 on t.OCC_DISEID = t5.rid ");
		sql.append(" INNER JOIN TS_SIMPLE_CODE TT2 ON TT2.RID = T.SOURCE_ID ");
		sql.append(" WHERE NVL(T.DEL_MARK, 0) = 0 ");
		sql.append(" AND T4.CART_TYPE = 2 ");
		sql.append(" AND TT2.EXTENDS1 = 1 ");
		sql.append(" AND T2.RID = ").append(Global.getUser().getTsUnit().getRid());
		sql.append(" AND T3.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(searchZoneCode)).append("%' ");
		sql.append(" AND T4.ORG_RCV_DATE <= TO_DATE('").append(DateUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss")).append("', 'YYYY-MM-DD HH24:MI:SS') ");
		sql.append(" AND (case when T3.IF_CITY_DIRECT = 1 and T4.STATE = 3 then 1 else T4.STATE end) IN (0, 2, 4, 6)) M ");
		return this.findDataBySqlNoPage(sql.toString(),null);
	}

	/**
	 * 查询预警未处置数据
	 *
	 * @param viewLevel 预警查看权限
	 * @param zoneGb    地区
	 * @return 预警未处置数据
	 */
	public int findWarnUnDisposeData(String viewLevel, String zoneGb) {
		String sql = "SELECT COUNT(1) " +
				"FROM TD_ZW_WARN_INFO WI " +
				"         LEFT JOIN TS_ZONE Z ON WI.WARN_ZONE = Z.RID " +
				"WHERE NVL(WI.STATE_MARK, 0) = 0 " +
				"  AND WI.VIEW_LEVEL = :viewLevel " +
				"  AND Z.ZONE_GB LIKE :zoneGb ";
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("viewLevel", viewLevel);
		paramMap.put("zoneGb", ZoneUtil.zoneSelect(zoneGb) + "%");
		return this.findCountBySql(sql, paramMap);
	}
}
