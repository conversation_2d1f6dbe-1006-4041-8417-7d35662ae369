package com.chis.modules.system.service;

import cn.hutool.core.convert.ConvertException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.enumn.SystemMessageEnum;
import com.chis.modules.system.logic.CompanySearchItemSystemVO;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日志查询模块Service
 */
@Service
@Transactional
public class LogBhkServiceImpl extends AbstractTemplate {
    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
     * 根据社会信用代码+有独立社会信用代码+未标删更新单位名称
     *
     * @param institutionCode 社会信用代码
     * @param name            单位名称
     */
    public void updateCrptNameByInstitutionCode(String institutionCode, String name) {
        String sql = "UPDATE TB_TJ_CRPT SET CRPT_NAME = :name, MASTER_DATA_TIME = SYSDATE, MODIFY_DATE = :modifyDate, MODIFY_MANID = :modifyManid " +
                "WHERE DEL_MARK = 0 AND IF_SUB_ORG = 0 AND INSTITUTION_CODE = :institutionCode";
        Map<String, Object> param = new HashMap<>();
        param.put("institutionCode", institutionCode);
        param.put("name", name);
        param.put("modifyDate", new Date());
        param.put("modifyManid", Global.getUser().getRid());
        executeSql(sql, param);
    }

    /**
     * 通过天眼查查询企业信息（带页面错误信息）
     */
    public List<CompanySearchItemSystemVO> searchCompanyByTyc(String searchCompanyWord) {
        if (ObjectUtil.isEmpty(searchCompanyWord)) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("单位名称或社会信用代码");
            throw new RuntimeException("调用天眼查接口失败：单位名称或社会信用代码不能为空！");
        }
        if (searchCompanyWord.length() < 4) {
            JsfUtil.addErrorMessage("单位名称或社会信用代码长度不能小于4！");
            throw new RuntimeException("调用天眼查接口失败：单位名称或社会信用代码长度不能小于4！");
        }
        if (searchCompanyWord.length() > 100) {
            SystemMessageEnum.LENGTH_VERIFY.formatMessage("单位名称或社会信用代码", 100);
            throw new RuntimeException("调用天眼查接口失败：单位名称或社会信用代码长度不能超过100！");
        }
        String companySearchUrl = this.commService.findParamValue("COMPANY_SEARCH_URL");
        if (ObjectUtil.isEmpty(companySearchUrl)) {
            SystemMessageEnum.MAINTENANCE_EMPTY.formatMessage("查询企业接口地址");
            throw new RuntimeException("调用天眼查接口失败：查询企业接口地址未维护！");
        }
        JSONObject entries = new JSONObject();
        entries.set("word", searchCompanyWord);
        String responseStr = "";
        System.out.println("调用天眼查接口，URL：" + companySearchUrl + "，请求参数：" + entries);
        try (HttpResponse response = HttpRequest.post(companySearchUrl)
                .body(entries.toString(), "application/json")
                .execute()) {
            System.out.println("调用天眼查接口，状态码：" + response.getStatus() + "，响应：" + response.body());
            if (response.getStatus() != 200) {
                throw new RuntimeException("调用天眼查接口失败！");
            }
            if (!org.springframework.util.StringUtils.hasText(response.body())) {
                throw new RuntimeException("调用天眼查接口失败！");
            }
            responseStr = response.body();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("调用天眼查接口失败！");
            throw new RuntimeException("调用天眼查接口失败！");
        }
        try {
            JSONObject response = JSONUtil.toBean(responseStr, JSONObject.class);

            // 返回状态码
            String code = response.get("code", String.class);
            // 请求无数据
            if (!"200".equals(code)) {
                throw new RuntimeException("调用天眼查接口失败！");
            }
            JSONObject data = response.get("data", JSONObject.class);
            if (ObjectUtil.isEmpty(data)) {
                throw new RuntimeException("调用天眼查接口失败！");
            }
            // 企业信息集合
            return data.getBeanList("rows", CompanySearchItemSystemVO.class);
        } catch (ConvertException e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("调用天眼查接口失败！");
            throw new RuntimeException("调用天眼查接口失败！");
        }
    }

}
