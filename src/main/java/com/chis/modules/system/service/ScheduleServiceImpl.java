package com.chis.modules.system.service;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chis.modules.system.entity.TdOaSchedule;

/**
 * 日程管理
 *
 * <AUTHOR>
 * @createDate 2015-03-13
 */
@Service
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class ScheduleServiceImpl extends AbstractTemplate {

	@Transactional(readOnly=true)
	public TdOaSchedule findScheduleById(Integer rid) {
		return (TdOaSchedule) super.find(TdOaSchedule.class, rid);
	}
	
	
	public String saveOrUpdateSchedule(TdOaSchedule schedule) {
		if(null != schedule.getRid()) {
			schedule = (TdOaSchedule) super.updateObj(schedule);
		}else {
			schedule = (TdOaSchedule) super.saveObj(schedule);
		}
		return schedule.getRid().toString();
	}
	
	
	public String deleteSchedule(Integer rid) {
		StringBuilder sb = new StringBuilder();
		sb.append(" DELETE FROM TD_OA_SCHEDULE WHERE RID = '").append(rid).append("' ");
		try {
			super.em.createNativeQuery(sb.toString()).executeUpdate();
		} catch (Exception e) {
			e.printStackTrace();
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return "操作失败，不允许删除！";
		}
		return null;
	}
	
	public String deleteScheduleBatch(String rid) {
		StringBuilder sb = new StringBuilder();
		sb.append(" DELETE FROM TD_OA_SCHEDULE WHERE RID in (").append(rid).append(") ");
		try {
			super.em.createNativeQuery(sb.toString()).executeUpdate();
		} catch (Exception e) {
			e.printStackTrace();
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return "操作失败，不允许停用！";
		}
		return null;
	}
	
	
	
	@Transactional(readOnly = true)
	public List<Object[]> findNeedRemindSchdule() {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T.RID AS quartzNo, T.EXPRESSIONS, T.EXECUTE_MAN_ID, T2.MB_NUM, ");
		sb.append(" T.SCHEDULE_TITLE, DECODE(T.SEASONAL, '1', T.HOUR_OF_DAY||':'||T.MIN_OF_DAY,TO_CHAR(T.BEGIN_TIME, 'YYYY-MM-DD HH24:MI')),");
		sb.append(" T.REMIND_MTD, T.SEASONAL,T2.USER_NO ");
		sb.append(" FROM TD_OA_SCHEDULE T ");
		sb.append(" INNER JOIN  TS_USER_INFO T2 ON T.EXECUTE_MAN_ID = T2.RID ");
		sb.append(" WHERE T.IS_REMIND = '1' ");
		sb.append(" AND (T.SEASONAL = '1' OR (T.SEASONAL = '0' AND T.REMIND_DATETIME > SYSDATE)) ");
		
		return super.em.createNativeQuery(sb.toString()).getResultList();
	}

    
    public String getDesc() {
        return "日程管理服务";
    }

    
    public String getVersion() {
        return "1.0.0";
    }

}
