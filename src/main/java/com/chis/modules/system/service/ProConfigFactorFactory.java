package com.chis.modules.system.service;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;

/**
 * 题目选项规则配置工厂类
 * 
 * <AUTHOR>
 * 
 */
public class ProConfigFactorFactory {

	private static CommServiceImpl commServiceImpl = SpringContextHolder
			.getBean(CommServiceImpl.class);
	

	/**
	 * 根据系统类型获取相应的实现类
	 */
	public static ProConfigFactor getProConfigFactor() {
		/***
		 * 获得当前系统类型(可能有多个)
		 */
		String systemTypes = commServiceImpl
				.findParamValue("SYSTEM_MODULES");
		String[] systemTypeIds = systemTypes.split("##");
		
		Short systemTypeNo;
		ProConfigFactor pcf = null;
		// 当前只有慢病系统需要脚本配置
		for (int i = 0; i < systemTypeIds.length; i++) {
			systemTypeNo = Short.valueOf(systemTypeIds[i]);

			switch (systemTypeNo) {
			case (short) 24:
				pcf = new ConfigFactorSlowQue();
				break;

				
			}
		}
		
//		if(pcf==null){
//			JsfUtil.addErrorMessage("未找到系统匹配的题目脚本实现类,无法进行选项规则配置!");
//		}
		return pcf;
	}
}
