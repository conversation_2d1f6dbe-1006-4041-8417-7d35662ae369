package com.chis.modules.system.service;

import com.chis.common.utils.*;
import com.chis.modules.system.entity.*;
import com.chis.modules.system.interfaces.IAutoCodeService;
import com.chis.modules.system.interfaces.ITempmetaResolve;
import com.chis.modules.system.logic.LucenePojo;
import com.chis.modules.system.logic.MetaCondition;
import com.chis.modules.system.utils.Global;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import javax.persistence.Query;
import java.io.*;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Transactional(readOnly = false, rollbackFor = Throwable.class)
public class CommServiceImpl extends AbstractTemplate {

	private static final Logger log = LoggerFactory.getLogger(CommServiceImpl.class);

	@Transactional(readOnly = true)
	public String findParamValue(String paramName) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select new TsSystemParam(t.paramName, t.paramValue) from TsSystemParam t where t.paramName = '");
		sb.append(paramName).append("' ");
		List<TsSystemParam> list = em.createQuery(sb.toString())
				.getResultList();
		if (null != list && list.size() > 0) {
			return list.get(0).getParamValue();
		} else {
			return null;
		}
	}

	@Transactional(readOnly = true)
	public Map<String, String> findParamValues(String paramNames) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select new TsSystemParam(t.paramName, t.paramValue) from TsSystemParam t where t.paramName in (");
		sb.append(paramNames).append(") ");
		List<TsSystemParam> list = em.createQuery(sb.toString())
				.getResultList();
		if (null != list && list.size() > 0) {
			Map<String, String> map = new HashMap<String, String>();
			for (TsSystemParam param : list) {
				map.put(param.getParamName(), param.getParamValue());
			}
			return map;
		} else {
			return null;
		}
	}

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleCodesByTypeId(String typeNo) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select new TsSimpleCode(t.rid,t.codeNo, t.codeName, t.tsCodeType.codeTypeName,t.codeLevelNo,t.extendS3) from TsSimpleCode t ");
			sb.append(" where t.ifReveal=1 and t.tsCodeType.codeTypeName in (")
					.append(typeNo).append(") order by t.num,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleCodesByTypeNo(String typeNo) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select new TsSimpleCode(t.rid, t.codeName, t.tsCodeType.codeTypeName, t.codeLevelNo) from TsSimpleCode t ");
			sb.append(" where t.ifReveal=1 and t.tsCodeType.codeTypeName in (")
					.append(typeNo)
					.append(") order by t.codeLevelNo,t.codeNo");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findLevelSimpleCodesByTypeId(String typeNo) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.ifReveal=1 and t.tsCodeType.codeTypeName in (")
					.append(typeNo).append(") order by t.num,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}
	
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findallSimpleCodesByTypeId(String typeNo) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (")
					.append(typeNo).append(") order by t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

    @Transactional(readOnly = true)
    public List<TsSimpleCode> findallSimpleCodesByTypeIdOrderByNum(String typeNo) {
        if (StringUtils.isNotBlank(typeNo)) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TsSimpleCode t ");
            sb.append(" where t.tsCodeType.codeTypeName in (")
                    .append(typeNo).append(") order by t.num, t.codeNo ");
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findallSimpleCodesByTypeIdAndCodeLevelNo(String typeNo) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (").append(typeNo).append(") ");
			sb.append(" and t.codeNo != t.codeLevelNo ");
			sb.append(" order by t.num,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findallSimpleCodesByTypeIdAndExtends1(String typeNo, Integer extends1) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (")
					.append(typeNo).append(")  and t.extendS1 = '"+extends1+"' order by t.num, t.codeLevelNo,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findallSimpleCodesByTypeIdAndExtends2(String typeNo, String extends2) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (")
					.append(typeNo).append(") and t.ifReveal=1 ");
			if (StringUtils.isNotBlank(extends2)) {
				sb.append(" and t.extendS2 in (").append(extends2).append(")");
			}
			sb.append(" order by t.num, t.codeLevelNo,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findallSimpleCodesByTypeIdOrderByExtends1(String typeNo) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (").append(typeNo).append(")");
			sb.append(" order by t.extendS1,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

    @Transactional(readOnly = true)
    public List<TsSimpleCode> findallSimpleCodesByTypeId2(String typeNo, Integer extends1) {
        if (StringUtils.isNotBlank(typeNo)) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TsSimpleCode t ");
            sb.append(" where t.tsCodeType.codeTypeName in (")
                    .append(typeNo).append(")  and t.extendS1 != '"+extends1+"' order by t.codeNo ");
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findNumSimpleCodesByTypeId(String typeNo) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.ifReveal=1 and t.tsCodeType.codeTypeName in (")
					.append(typeNo).append(") order by t.num,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

	/**
	 * 传入项目类型,层级编码，查询该类型下的所有项目，按层级编码及项目编码排序
	 * 
	 * @param typeNo
	 * @param ifAllUse
	 * @param levelCodeNo
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findTsSimpleCodesByTypeNoAndLevelCode(
			String typeNo, boolean ifAllUse, String levelCodeNo) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (").append(typeNo)
					.append(") ");
			if (ifAllUse) {
				sb.append(" and t.ifReveal= 1");
			}
			if (StringUtils.isNotBlank(levelCodeNo)) {
				sb.append("and (t.codeLevelNo like '").append(levelCodeNo)
						.append(".%'");
				sb.append(" or t.codeLevelNo ='").append(levelCodeNo).append("')");
			}
			sb.append(" order by t.codeLevelNo ,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}
	/**
	 *  <p>方法描述：查询一级 按照序号,编码排序</p>
	 * @MethodAuthor hsj
	 */
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findTsSimpleCodesByTypeNo(
			String typeNo, boolean ifAllUse) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (").append(typeNo)
					.append(") ");
			if (ifAllUse) {
				sb.append(" and t.ifReveal= 1");
			}
			//codeLevelNo可能不维护
			sb.append(" and ( t.codeLevelNo = t.codeNo or t.codeLevelNo is null)");
			sb.append(" order by t.num,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}
	/**
	 *  <p>方法描述：查询当前对象的所有下级 不包含本级
	 *  按照序号,编码排序</p>
	 * @MethodAuthor hsj 2023-03-18 18:48
	 */
	public List<TsSimpleCode> findParentCodeList(String typeNo,String codeNo, boolean ifAllUse){
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.ifReveal=1 and t.tsCodeType.codeTypeName in (")
					.append(typeNo)
					.append(")  ");
			if (ifAllUse) {
				sb.append(" and t.ifReveal= 1");
			}
			sb.append(" and t.codeLevelNo like '").append(codeNo).append(".%'");
			sb.append(" order by  t.num,t.codeNo");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}
	/**
	 *  <p>方法描述：码表查询当前对象的最后一级
	 * 	 *  按照序号,编码排序
	 * 	 ifAll ： 最后一级是否为启用</p>
	 * @MethodAuthor hsj 2023-05-16 17:34
	 */
	public List<TsSimpleCode> findParentCodeLastList(String typeNo,  boolean ifAll){
		List<TsSimpleCode> simpleCodes = new ArrayList<>();
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" WITH Tab AS ( SELECT tsc.* FROM TS_SIMPLE_CODE tsc LEFT JOIN TS_CODE_TYPE tct ON tct.RID = tsc.CODE_TYPE_ID WHERE tct.CODE_TYPE_NAME = '");
			sb.append(typeNo);
			sb.append("'");
			sb.append(")");
			sb.append(" , Tab1 AS ( SELECT tsc.*,SUBSTR (CODE_LEVEL_NO,0,INSTR (CODE_LEVEL_NO , '.',-1)-1) AS leavelNo  FROM TS_SIMPLE_CODE tsc LEFT JOIN TS_CODE_TYPE tct ON tct.RID = tsc.CODE_TYPE_ID WHERE tct.CODE_TYPE_NAME = '");
			sb.append(typeNo);
			sb.append("'");
			sb.append(")");
			sb.append("  select tab.rid,tab.code_no,tab.CODE_NAME,tab.CODE_PATH from tab LEFT JOIN tab1 ON tab.CODE_LEVEL_NO = tab1.leavelNo");
			sb.append(" where tab1.rid is null ");
			if(ifAll){
				sb.append(" and tab.if_Reveal = 1");
			}
			sb.append(" order by  tab.num,tab.code_no");
			List<Object[]> sqlResultList = super.findDataBySqlNoPage(sb.toString(),null);
			if(!CollectionUtils.isEmpty(sqlResultList)){
				for (Object[] o:sqlResultList){
					TsSimpleCode tsSimpleCode = new TsSimpleCode();
					tsSimpleCode.setRid(Integer.valueOf(ObjectUtil.toStr(o[0])));
					tsSimpleCode.setCodeNo(ObjectUtil.toStr(o[1]));
					tsSimpleCode.setCodeName(ObjectUtil.toStr(o[2]));
					tsSimpleCode.setCodePath(ObjectUtil.toStr(o[3]));
					simpleCodes.add(tsSimpleCode);
				}
			}
		}
		return simpleCodes;
	}

	@Transactional(readOnly = true)
	public List<TsBsSort> findSortsByApp() {
		StringBuilder sb = new StringBuilder(
				"select new TsBsSort(t.sortName, t.rid) from TsBsSort t order by t.levelCode ");
		return em.createQuery(sb.toString()).getResultList();
	}

	public String getAutoCode(String idcode, String pfx) {
		StringBuilder sb = new StringBuilder(
				" select t from TsCodeRule t where t.idcode ='");
		sb.append(idcode).append("'");
		TsCodeRule rule = (TsCodeRule) em.createQuery(sb.toString())
				.getSingleResult();
		IAutoCodeService service = SpringContextHolder.getBean(rule
				.getImpClass());
		return service.buildCode(rule, pfx, em);
	}

	public String getTestAutoCode(TsCodeRule rule, String pfx) {
		IAutoCodeService service = SpringContextHolder.getBean(rule
				.getImpClass());
		return service.buildTestCode(rule, pfx);
	}

	@SuppressWarnings("rawtypes")
	@Transactional(readOnly = true)
	public int findTotalNum(String countHql, Map<String, Object> paramMap) {
		Query countQuery = em.createQuery(countHql);
		if (null != paramMap && paramMap.size() > 0) {
			Iterator itor = paramMap.entrySet().iterator();
			while (itor.hasNext()) {
				Map.Entry entry = (Map.Entry) itor.next();
				countQuery.setParameter(String.valueOf(entry.getKey()),
						entry.getValue());
			}
		}
		Object countObj = countQuery.getSingleResult();
		return Integer.parseInt(countObj == null ? "0" : countObj.toString());
	}

	@SuppressWarnings("rawtypes")
	@Transactional(readOnly = true)
	public int findTotalNumBySQL(String countSQL, Map<String, Object> paramMap) {
		Query countQuery = em.createNativeQuery(countSQL);
		if (null != paramMap && paramMap.size() > 0) {
			Iterator itor = paramMap.entrySet().iterator();
			while (itor.hasNext()) {
				Map.Entry entry = (Map.Entry) itor.next();
				countQuery.setParameter(String.valueOf(entry.getKey()),
						entry.getValue());
			}
		}
		Object countObj = countQuery.getSingleResult();
		return Integer.parseInt(countObj == null ? "0" : countObj.toString());
	}

	@SuppressWarnings("rawtypes")
	@Transactional(readOnly = true)
	public List findData(String hql, Map<String, Object> paramMap, int first,
			int pageSize) {
		Query query = em.createQuery(hql);
		if (null != paramMap && paramMap.size() > 0) {
			Iterator itor = paramMap.entrySet().iterator();
			while (itor.hasNext()) {
				Map.Entry entry = (Map.Entry) itor.next();
				query.setParameter(String.valueOf(entry.getKey()),
						entry.getValue());
			}
		}
		List list = query.setFirstResult(first).setMaxResults(pageSize)
				.getResultList();
		return list;
	}

	@Transactional(readOnly = true)
	public List findData(String hql, Map<String, Object> paramMap) {
		Query query = em.createQuery(hql);
		if (null != paramMap && paramMap.size() > 0) {
			Iterator itor = paramMap.entrySet().iterator();
			while (itor.hasNext()) {
				Map.Entry entry = (Map.Entry) itor.next();
				query.setParameter(String.valueOf(entry.getKey()),
						entry.getValue());
			}
		}
		List list = query.getResultList();
		return list;
	}

	@SuppressWarnings("rawtypes")
	@Transactional(readOnly = true)
	public List findDataBySQL(String sql, Map<String, Object> paramMap,
			int first, int pageSize) {
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT * FROM (");
		sb.append(" SELECT KKK0.*, ROWNUM AS RN FROM (").append(sql);
		sb.append(" ) KKK0 ) WHERE RN > '").append(first)
				.append("' AND RN <= '").append((first + pageSize))
				.append("' ");

		Query query = em.createNativeQuery(sb.toString());
		if (null != paramMap && paramMap.size() > 0) {
			Iterator itor = paramMap.entrySet().iterator();
			while (itor.hasNext()) {
				Map.Entry entry = (Map.Entry) itor.next();
				query.setParameter(String.valueOf(entry.getKey()),
						entry.getValue());
			}
		}
		List list = query.getResultList();
		return list;
	}

	@Transactional(readOnly = true)
	public List findDataBySqlNoPage(String sql, Map<String, Object> paramMap) {
		Query query = em.createNativeQuery(sql);
		if (null != paramMap && paramMap.size() > 0) {
			Iterator itor = paramMap.entrySet().iterator();
			while (itor.hasNext()) {
				Map.Entry entry = (Map.Entry) itor.next();
				query.setParameter(String.valueOf(entry.getKey()),
						entry.getValue());
			}
		}
		List list = query.getResultList();
		return list;
	}
	@Transactional(readOnly = true)
	public List findDataBySql(String sql, Map<String, Object> paramMap) {
		Query query = em.createNativeQuery(sql);
		if (null != paramMap && paramMap.size() > 0) {
			Iterator itor = paramMap.entrySet().iterator();
			while (itor.hasNext()) {
				Map.Entry entry = (Map.Entry) itor.next();
				query.setParameter(String.valueOf(entry.getKey()),
						entry.getValue());
			}
		}
		query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
		List<Map<String,Object>> list = query.getResultList();
		return list;
	}
	@Transactional(readOnly = true)
	public List<TsZone> findZoneList(String zoneCode, int zoneType) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select new TsZone(t.rid, t.zoneGb, t.zoneName,t.zoneType) ");
		sb.append(" from TsZone t where t.ifReveal = 1 and t.zoneGb like '")
				.append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		sb.append(" and t.zoneType = ").append(zoneType + 1);
		sb.append(" order by t.zoneGb ");
		return em.createQuery(sb.toString()).getResultList();
	}

	/**
	* <p>Description：查询地区  不过滤停用 </p>
	* <p>Author： yzz 2023-12-06 </p>
	*/
	@Transactional(readOnly = true)
	public List<TsZone> findZoneListByNoStop(String zoneCode, int zoneType) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select new TsZone(t.rid, t.zoneGb, t.zoneName,t.zoneType) ");
		sb.append(" from TsZone t where 1=1 and t.zoneGb like '")
				.append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		sb.append(" and t.zoneType = ").append(zoneType + 1);
		sb.append(" order by t.zoneGb ");
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public List<TsZone> findZoneListAllField(String zoneCode, int zoneType) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t ");
		sb.append(" from TsZone t where t.ifReveal = 1 and t.zoneGb like '")
				.append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		sb.append(" and t.zoneType = ").append(zoneType + 1);
		sb.append(" order by t.zoneGb ");
		return em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public TsZone findParentZone(String zoneCode, int zoneType) {
		TsZone zone = null;
		if (zoneType > 2 && zoneType <= 6) {
			String parentCode = ZoneUtil.getParentCode(zoneCode);
			StringBuilder sb = new StringBuilder();
			sb.append(" select new TsZone(t.rid, t.zoneGb, t.zoneName,t.zoneType) ");
			sb.append(" from TsZone t where t.ifReveal = 1 and t.zoneGb = '")
					.append(parentCode).append("' ");

			List<TsZone> list = em.createQuery(sb.toString()).getResultList();
			if (null != list && list.size() > 0) {
				zone = list.get(0);
			}
		}
		return zone;
	}

	@Transactional(readOnly = true)
	public Map<String, String> buildMsg(String userIds) {
		Map<String, String> map = new HashMap<String, String>();
		if (StringUtils.isNotBlank(userIds)) {
			StringBuilder sb = new StringBuilder(
					" SELECT T.PUBLISH_MAN, COUNT(T.PUBLISH_MAN) ");
			sb.append(" FROM TD_MSG_SUB T ");
			sb.append(" WHERE T.ACCEPT_STATE = '0' ")
					.append(" AND T.PUBLISH_MAN IN (").append(userIds)
					.append(")");
			sb.append(" GROUP BY T.PUBLISH_MAN ");

			List<Object[]> list = em.createNativeQuery(sb.toString())
					.getResultList();
			if (null != list && list.size() > 0) {
				for (Object[] o : list) {
					sb = new StringBuilder();
					sb.append("<a href=\"javascript:forwordPage('信息查看','/webapp/system/tdMsgMainSearchList.faces','')\">");
					sb.append("您有").append(o[1].toString())
							.append("条未阅消息！</a>");

					map.put(o[0].toString(), sb.toString());
				}
			}
		}

		return map;
	}

	/**
	 * 根据类型id，获取码表集合（包含停用）
	 * 
	 * @param typeNo
	 *            编码
	 * @return 返回该类型下面的所有码表信息，包括停用的
	 * <AUTHOR>
	 * @history 2014年9月10日
	 */
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findAllSimpleCodes(String typeNo) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (").append(typeNo)
					.append(") order by t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

	/**
	 * 通过ZoneGb和ZoneType或RealZoneType获取地区列表(全国时不显示全国)
	 *
	 * @param zoneGb  ZoneGb
	 * @param real    是否真实地区级别
	 * @param typeMin 最高地区级别
	 * @param typeMax 最低地区级别
	 * @return 地区列表
	 */
	@Transactional(readOnly = true)
	public List<TsZone> findZoneListByGbAndTypeNoNation(String zoneGb, boolean real, String typeMin, String typeMax) {
		List<TsZone> zoneList = findZoneListByGbAndType(zoneGb, real, typeMin, typeMax);
		if (ObjectUtil.isEmpty(zoneList)) {
			return new ArrayList<>();
		}
		if (ObjectUtil.isEmpty(zoneGb) && ObjectUtil.isNotEmpty(zoneList.get(0).getZoneGb()) && zoneList.get(0).getZoneGb().startsWith("00")) {
			zoneList.remove(0);
		}
		return zoneList;
	}

	/**
	 * 通过ZoneGb和ZoneType或RealZoneType获取地区列表(全国时显示全国)
	 *
	 * @param zoneGb  ZoneGb
	 * @param real    是否真实地区级别
	 * @param typeMin 最高地区级别
	 * @param typeMax 最低地区级别
	 * @return 地区列表
	 */
	@Transactional(readOnly = true)
	public List<TsZone> findZoneListByGbAndType(String zoneGb, boolean real, String typeMin, String typeMax) {
		zoneGb = StringUtils.objectToString(ZoneUtil.zoneSelect(StringUtils.objectToString(zoneGb)));
		typeMin = StringUtils.objectToString(typeMin);
		typeMax = StringUtils.objectToString(typeMax);
		String key = real ? "RealZoneType" : "ZoneType";
		TsZoneTree zoneTree = (TsZoneTree) CacheUtils.get("zoneListByGb", key);
		if (ObjectUtil.isEmpty(zoneTree)) {
			zoneTree = getZoneTree(real);
			CacheUtils.put("zoneListByGb", key, zoneTree);
		}
		short typeMinShort = ObjectUtil.convert(Short.class, typeMin, (short) 0);
		short typeMaxShort = ObjectUtil.convert(Short.class, typeMax, (short) 9);
		return zoneTree.toZoneList(zoneGb, real, typeMinShort, typeMaxShort);
	}

	/**
	 * <p>方法描述：查询码表中的系统类型 </p>
	 * pw 2024/1/29
	 **/
	public List<Integer> findAllParamTypeByTsCodeType() {
		String sql = " SELECT DISTINCT t.PARAM_TYPE  FROM TS_CODE_TYPE t ";
		return this.findParamTypeComm(this.findDataBySqlNoPage(sql, null));
	}
	
	/**
	 * <p>方法描述：查询报表中的系统类型 </p>
	 * pw 2024/1/30
	 **/
	public List<Integer> findAllParamTypeByTsRpt() {
		String sql = " SELECT DISTINCT t.PARAM_TYPE FROM TS_RPT t ";
		return this.findParamTypeComm(this.findDataBySqlNoPage(sql, null));
	}

	/**
	 * <p>方法描述：处理查询的系统类型 </p>
	 * pw 2024/1/30
	 **/
	private List<Integer> findParamTypeComm(List<Object> queryResultList){
		if (CollectionUtils.isEmpty(queryResultList)) {
			return Collections.emptyList();
		}
		List<Integer> resultList = new ArrayList<>();
		for (Object obj : queryResultList) {
			if (null == obj) {
				continue;
			}
			resultList.add(Integer.parseInt(obj.toString()));
		}
		return resultList;
	}

	private TsZoneTree getZoneTree(boolean real) {
		List<TsZone> zoneList = findByHql(" select t from TsZone t where t.ifReveal='1' order by t.zoneGb ", TsZone.class);
		checkAllZoneList(zoneList);
		zoneList = pakNationwideZoneList(zoneList);
		TsZone zone = zoneList.get(0);
		TsZoneTree zoneTree;
		int startIndex = 0;
		if (zone.getZoneGb().startsWith("00")) {
			if (ObjectUtil.isEmpty(zoneList.get(0).getZoneType())) {
				zoneList.get(0).setZoneType((short) 1);
			}
			if (ObjectUtil.isEmpty(zoneList.get(0).getRealZoneType())) {
				zoneList.get(0).setRealZoneType((short) 1);
			}
			zoneTree = new TsZoneTree(zoneList.get(0));
			startIndex = 1;
		} else {
			zoneTree = new TsZoneTree();
			zoneTree.setRealZoneType((short) 0);
			zoneTree.setZoneType((short) 0);
		}

		//存储当前层级ZONE_TYPE
		List<Short> zoneTypeList = new ArrayList<>();
		pakZoneTreeByZoneList(zoneTree, zoneList, real, startIndex, zoneTypeList);
		return zoneTree;
	}

	/**
	 * 检查ZONE_TYPE/REAL_ZONE_TYPE与ZONE_GB/DSF_CODE是否维护一致
	 *
	 * @param zoneList 地区List
	 */
	private void checkAllZoneList(List<TsZone> zoneList) {
		if (ObjectUtil.isEmpty(zoneList)) {
			return;
		}
		boolean error = false;
		for (TsZone zone : zoneList) {
			Integer rid = zone.getRid();
			String fullName = StringUtils.objectToString(zone.getFullName());
			String zoneType = StringUtils.objectToString(zone.getZoneType());
			String realZoneType = StringUtils.objectToString(zone.getRealZoneType());
			String zoneGb = StringUtils.objectToString(zone.getZoneGb());
			String dsfCode = StringUtils.objectToString(zone.getDsfCode());
			String zeros = "0000000000";
			String msg = "";
			try {
				msg = "地区RID：" + rid + "的地区编码ZONE_GB：" + zoneGb + "的" + fullName + "对应的ZONE_TYPE(" + zoneType + ")维护异常！";
				if (ObjectUtil.isEmpty(zoneGb)) {
					msg = "地区RID：" + rid + "的" + fullName + "对应的ZONE_GB为空！";
					log.error(msg);
					error = true;
				} else if (ObjectUtil.isEmpty(zoneType)) {
					msg = "地区RID：" + rid + "的地区编码ZONE_GB：" + zoneGb + "的" + fullName + "对应的ZONE_TYPE为空！";
					log.error(msg);
					error = true;
				} else if (ObjectUtil.convert(Integer.class, zoneType, 5) < 5
						&& !zoneType.equals(StringUtils.objectToString(ZoneUtil.getZoneType(zoneGb)))) {
					log.error(msg);
					error = true;
				}
			} catch (Exception e) {
				log.error(msg);
				error = true;
			}
			try {
				msg = "地区RID：" + rid + "的地区编码DSF_CODE：" + dsfCode + "的" + fullName + "对应的REAL_ZONE_TYPE(" + realZoneType + ")维护异常！";
				if (ObjectUtil.isEmpty(dsfCode)) {
					msg = "地区RID：" + rid + "的" + fullName + "对应的DSF_CODE为空！";
					log.error(msg);
					error = true;
				} else if (ObjectUtil.isEmpty(realZoneType)) {
					msg = "地区RID：" + rid + "的地区编码DSF_CODE：" + dsfCode + "的" + fullName + "对应的REAL_ZONE_TYPE为空！";
					log.error(msg);
					error = true;
				}
				//DFS_CODE不足10位时补足到10位
				if (dsfCode.length() < 10) {
					dsfCode += zeros.substring(dsfCode.length());
				}
				if (ObjectUtil.convert(Integer.class, realZoneType, 5) < 5
						&& !realZoneType.equals(StringUtils.objectToString(ZoneUtil.getZoneType(dsfCode)))) {
					log.error(msg);
					error = true;
				}
			} catch (Exception e) {
				log.error(msg);
				error = true;
			}
		}
		if (error) {
			log.error("请先检查地区是否维护正确！");
			throw new RuntimeException("请先检查地区是否维护正确！");
		}
	}

	/**
	 * 根据地区list封装地区树
	 *
	 * @param zoneTree 地区树
	 * @param zoneList 地区list
	 * @param real     是否RealZoneType
	 * @param index    下标
	 * @return 地区树
	 */
	private int pakZoneTreeByZoneList(TsZoneTree zoneTree, List<TsZone> zoneList, boolean real, int index,
									  List<Short> zoneTypeList) {
		while (index < zoneList.size()) {
			TsZone zone = zoneList.get(index);
			Short zoneTreeType = real ? zoneTree.getRealZoneType() : zoneTree.getZoneType();
			Short zoneType = real ? zone.getRealZoneType() : zone.getZoneType();
			if (!checkZoneTypeList(zoneTypeList, zoneType, zone, real)) {
				index++;
				continue;
			}
			//若当前节点地区级别等于下一个地区级别（同级）则返回下标
			if (zoneTreeType >= zoneType) {
				return index;
			}
			int size = zoneTree.getChildZoneList().size();
			if (size == 0) {
				zoneTree.addChildZone(zone);
				index++;
			} else {
				TsZoneTree zoneTree1 = zoneTree.getChildZoneList().get(size - 1);
				zoneTreeType = real ? zoneTree1.getRealZoneType() : zoneTree1.getZoneType();
				if (zoneTreeType.equals(zoneType)) {
					zoneTree.addChildZone(zone);
					index++;
				} else {
					index = pakZoneTreeByZoneList(zoneTree1, zoneList, real, index, zoneTypeList);
				}
			}
		}
		return zoneList.size();
	}

	/**
	 * 处理查询全国地区时将本省放在前面
	 *
	 * @param zoneList 处理前的地区列表
	 * @return 处理后的地区列表
	 */
	private List<TsZone> pakNationwideZoneList(List<TsZone> zoneList) {
		try {
			List<TsZone> newZoneList = new ArrayList<>();
			List<TsZone> newZoneList1 = new ArrayList<>();
			String zoneGb = Global.getUser().getTsUnit().getTsZone().getZoneGb();
			if (ObjectUtil.isNotEmpty(zoneGb) && zoneGb.length() >= 2) {
				zoneGb = zoneGb.substring(0, 2);
			}
			for (TsZone tsZone : zoneList) {
				if (tsZone.getZoneGb().startsWith("00") || tsZone.getZoneGb().startsWith(zoneGb)) {
					newZoneList.add(tsZone);
				} else {
					newZoneList1.add(tsZone);
				}
			}
			newZoneList.addAll(newZoneList1);
			return newZoneList;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return zoneList;
	}

	/**
	 * 检查并移除数据库异常地区
	 */
	private boolean checkZoneTypeList(List<Short> zoneTypeList, Short zoneType, TsZone zone, boolean real) {
		String key = real ? "REAL_ZONE_TYPE" : "ZONE_TYPE";
		if (ObjectUtil.isEmpty(zoneType)) {
			String msg = "地区RID：" + zone.getRid() + "的地区编码" + zone.getZoneGb() + "的" + zone.getFullName() + "对应的" + key + "为空！";
			System.out.println(msg);
			log.error(msg);
			return false;
		}
		//正常的地区级别层级应为: 1>2>3>4>5>4>5>3>4>5>4>5/1>2>4>5>4>5
		//错误的地区级别层级: 1>2>4>5>3>4>5
		if (zoneTypeList.size() == 0) {
			zoneTypeList.add(zoneType);
			return true;
		}
		String msg = "地区RID：" + zone.getRid() + "的地区编码" + zone.getZoneGb() + "的" + zone.getFullName() + "对应的" + key + "维护异常！";
		int size = zoneTypeList.size() - 1;
		if (zoneType.equals(zoneTypeList.get(size))) {
			//例: 地区级别集合: 1>2>3>4>5 当前地区级别: 5
			return true;
		} else if (zoneType > zoneTypeList.get(size)) {
			//例: 地区级别集合: 1>2>3>4 当前地区级别: 5
			zoneTypeList.add(zoneType);
			return true;
		} else if (size == 0) {
			//当前地区在第二级且地区级别比上级级别小时为异常级别(比如: 2>1)
			System.out.println(msg);
			log.error(msg);
			return false;
		}

		for (int i = zoneTypeList.size() - 2; i >= 0; i--) {
			if (zoneType.equals(zoneTypeList.get(i))) {
				//例: 地区级别集合: 1>2>3>4>5 当前地区级别: 4
				//集合遍历到4时(4=4)代表无异常情况，此时应删除集合中4后的地区级别
				for (int j = zoneTypeList.size() - 1; j > i; j--) {
					zoneTypeList.remove(j);
				}
				return true;
			} else if (zoneType > zoneTypeList.get(i)) {
				//例: 地区级别集合: 1>2>4>5 当前地区级别: 3
				//集合遍历到2时(3>2)代表存在异常情况
				System.out.println(msg);
				log.error(msg);
				return false;
			}
		}

		//例: 地区级别集合: 1>2>4>5 当前地区级别: 0
		//集合遍历完仍没有找到比当前地区级别小或相等的级别代表存在异常情况
		System.out.println(msg);
		log.error(msg);
		return false;
	}

	/**
	 *（此方法已废弃）
	 * <pre>新方法: {@link #findZoneListByGbAndType}
	 */
	@Deprecated
	@Transactional(readOnly = true)
	public List<TsZone> findZoneList(Boolean admin, String zoneCode,
			String zoneTypeMin, String zoneTypeMax) {
		StringBuilder sb = new StringBuilder(
				" select new TsZone(t.rid, t.zoneGb, t.zoneName, t.zoneType,zoneCode,dsfCode,fullName,ifCityDirect) from TsZone t where t.ifReveal='1' ");
		if (null == admin || !admin) {
			sb.append(" and t.zoneGb like '")
					.append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		}
		if (StringUtils.isNotBlank(zoneTypeMin)) {
			sb.append(" and t.zoneType >='").append(zoneTypeMin).append("' ");
		}
		if (StringUtils.isNotBlank(zoneTypeMax)) {
			sb.append(" and t.zoneType <='").append(zoneTypeMax).append("' ");
		}
		sb.append(" order by t.zoneGb ");
		return em.createQuery(sb.toString()).getResultList();
	}

	/**
	 * （此方法已废弃）
	 * <pre>新方法: {@link #findZoneListByGbAndType}
	 */
	@Deprecated
	@Transactional(readOnly = true)
	public List<TsZone> findZoneListAllField(Boolean admin, String zoneCode,
									 String zoneTypeMin, String zoneTypeMax) {
		StringBuilder sb = new StringBuilder(
				" select t from TsZone t where t.ifReveal='1' ");
		if (null == admin || !admin) {
			sb.append(" and t.zoneGb like '")
					.append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		}
		if (StringUtils.isNotBlank(zoneTypeMin)) {
			sb.append(" and t.zoneType >='").append(zoneTypeMin).append("' ");
		}
		if (StringUtils.isNotBlank(zoneTypeMax)) {
			sb.append(" and t.zoneType <='").append(zoneTypeMax).append("' ");
		}
		sb.append(" order by t.zoneGb ");
		return em.createQuery(sb.toString()).getResultList();
	}

	/**
	 * 查询地区（此方法已废弃）
	 * <pre>新方法: {@link #findZoneListByGbAndType}
	 */
	@Deprecated
	@Transactional(readOnly = true)
	public List<TsZone> findZoneListNew(Boolean admin, String zoneCode,
			String zoneTypeMin, String zoneTypeMax) {
		StringBuilder sb = new StringBuilder(
				" select t from TsZone t where t.ifReveal='1' ");
		if (null == admin || !admin) {
			sb.append(" and t.zoneCode like '")
					.append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		}
		if (StringUtils.isNotBlank(zoneTypeMin)) {
			sb.append(" and t.zoneType >='").append(zoneTypeMin).append("' ");
		}
		if (StringUtils.isNotBlank(zoneTypeMax)) {
			sb.append(" and t.zoneType <='").append(zoneTypeMax).append("' ");
		}
		sb.append(" order by t.zoneCode ");
		return em.createQuery(sb.toString()).getResultList();
	}
	/**
	 *  <p>方法描述：地址查询添加缓存内</p>（此方法废弃）
	 * <pre>新方法: {@link #findZoneListByGbAndType}
	 * @MethodAuthor hsj
	 */
	@Deprecated
	@Transactional(readOnly = true)
	public List<TsZone> findZoneListCache(Boolean admin, String zoneCode,
													  String zoneTypeMin, String zoneTypeMax){
		String key  = admin + "_" + zoneCode + "_" + zoneTypeMin + "_" + zoneTypeMax ;
		List<TsZone> zoneList = (List<TsZone>) CacheUtils.get("zoneList", key);
		if(CollectionUtils.isEmpty(zoneList)){
			 zoneList = findZoneListNew(admin,zoneCode, zoneTypeMin, zoneTypeMax);
			CacheUtils.put("zoneList", key, zoneList);
		}
		return zoneList;
	}

	/**
	 * @Description: 地区查询 增加ifReveal参数
	 * 注意 传递short 如果用Integer 需要强转
	 * （此方法已废弃）
	 * <pre>新方法: {@link #findZoneListByGbAndType}
	 * @MethodAuthor pw,2022年03月14日
	 */
	@Deprecated
	@Transactional(readOnly = true)
	public List<TsZone> findZoneListCache(Boolean admin, String zoneCode,
										  String zoneTypeMin, String zoneTypeMax, Short ifReveal){
		String key  = admin + "_" + zoneCode + "_" + zoneTypeMin + "_" + zoneTypeMax + "_" + ifReveal;
		List<TsZone> zoneList = (List<TsZone>) CacheUtils.get("zoneListWithReveal", key);
		if(CollectionUtils.isEmpty(zoneList)){
			StringBuilder sb = new StringBuilder(
					" select t from TsZone t where 1=1 ");
			if(null != ifReveal){
				sb.append(" and t.ifReveal=").append(ifReveal).append(" ");
			}
			if (null == admin || !admin) {
				sb.append(" and t.zoneCode like '")
						.append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
			}
			if (StringUtils.isNotBlank(zoneTypeMin)) {
				sb.append(" and t.zoneType >='").append(zoneTypeMin).append("' ");
			}
			if (StringUtils.isNotBlank(zoneTypeMax)) {
				sb.append(" and t.zoneType <='").append(zoneTypeMax).append("' ");
			}
			sb.append(" order by t.zoneCode ");
			zoneList = em.createQuery(sb.toString()).getResultList();
			CacheUtils.put("zoneListWithReveal", key, zoneList);
		}
		return zoneList;
	}

	/**
	 * @Description: 地区查询(zonegb) 增加ifReveal参数
	 * 注意 传递short 如果用Integer 需要强转
	 * （此方法已废弃）
	 * <pre>新方法: {@link #findZoneListByGbAndType}
	 * @MethodAuthor pw,2022年03月14日
	 */
	@Deprecated
	@Transactional(readOnly = true)
	public List<TsZone> findZoneListCacheByZoneGbIncludeDeactivate(Boolean admin, String zoneGb,
										  String zoneTypeMin, String zoneTypeMax, Short ifReveal) {
		String key = admin + "_" + zoneGb + "_" + zoneTypeMin + "_" + zoneTypeMax + "_" + ifReveal;
		List<TsZone> zoneList = (List<TsZone>) CacheUtils.get("zoneListWithRevealByZoneGb", key);
		if (!CollectionUtils.isEmpty(zoneList)) {
			return zoneList;
		}
		StringBuilder sb = new StringBuilder(" select t from TsZone t where 1=1 ");
		if (null != ifReveal) {
			sb.append(" and t.ifReveal=").append(ifReveal).append(" ");
		}
		if (null == admin || !admin) {
			sb.append(" and t.zoneGb like '").append(ZoneUtil.zoneSelect(zoneGb)).append("%' ");
		}
		if (StringUtils.isNotBlank(zoneTypeMin)) {
			sb.append(" and t.realZoneType >='").append(zoneTypeMin).append("' ");
		}
		if (StringUtils.isNotBlank(zoneTypeMax)) {
			sb.append(" and t.realZoneType <='").append(zoneTypeMax).append("' ");
		}
		sb.append(" order by t.zoneGb ");
		zoneList = em.createQuery(sb.toString()).getResultList();
		CacheUtils.put("zoneListWithRevealByZoneGb", key, zoneList);
		return zoneList;
	}

	/**
	 * <p>方法描述：通过admin、zoneGb查询地区，过滤zoneType、ifReveal </p>
	 * pw 2024/12/20
	 **/
	@Deprecated
	@Transactional(readOnly = true)
	public List<TsZone> findZoneListCacheByZoneGbIncludeDeactivateWithZoneType(Boolean admin, String zoneGb,
																   String zoneTypeMin, String zoneTypeMax, Short ifReveal) {
		String key = admin + "_" + zoneGb + "_" + zoneTypeMin + "_" + zoneTypeMax + "_" + ifReveal;
		List<TsZone> zoneList = (List<TsZone>) CacheUtils.get("zoneListWithZoneTypeRevealByZoneGb", key);
		if (!CollectionUtils.isEmpty(zoneList)) {
			return zoneList;
		}
		StringBuilder sb = new StringBuilder(" select t from TsZone t where 1=1 ");
		if (null != ifReveal) {
			sb.append(" and t.ifReveal=").append(ifReveal).append(" ");
		}
		if (null == admin || !admin) {
			sb.append(" and t.zoneGb like '").append(ZoneUtil.zoneSelect(zoneGb)).append("%' ");
		}
		if (StringUtils.isNotBlank(zoneTypeMin)) {
			sb.append(" and t.zoneType >='").append(zoneTypeMin).append("' ");
		}
		if (StringUtils.isNotBlank(zoneTypeMax)) {
			sb.append(" and t.zoneType <='").append(zoneTypeMax).append("' ");
		}
		sb.append(" order by t.zoneGb ");
		zoneList = em.createQuery(sb.toString()).getResultList();
		CacheUtils.put("zoneListWithZoneTypeRevealByZoneGb", key, zoneList);
		return zoneList;
	}


	@Transactional(readOnly = true)
	public String findDsfCodeByZoneCode(String zoneCode) {
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT DSF_CODE FROM TS_ZONE WHERE ZONE_CODE = '")
				.append(zoneCode).append("'");
		List<String> result = em.createNativeQuery(sb.toString())
				.getResultList();
		return result.size() > 0 ? result.get(0) : null;
	}

	@Transactional(readOnly = true)
	public String resolveTemplatemeta(String template, MetaCondition condition,
			boolean test) {
		String[] metas = StringUtils.substringsBetween(template, "【", "】");
		if (null != metas && metas.length > 0) {
			StringBuilder sb = new StringBuilder();
			for (String meta : metas) {
				sb.append(",'【").append(meta).append("】'");
			}
			String meta = sb.toString().replaceFirst(",", "");
			sb = new StringBuilder(
					"SELECT t FROM TbTempmetaDefine t WHERE t.metaName in(");
			sb.append(meta).append(")");

			List<TbTempmetaDefine> list = super.em.createQuery(sb.toString())
					.getResultList();
			if (null != list && list.size() > 0) {
				for (TbTempmetaDefine define : list) {
					try {
						ITempmetaResolve resolve = SpringContextHolder
								.getBean(define.getImpClassname());
						if (test) {
							template = StringUtils.replace(template,
									define.getMetaName(), resolve.testResult());
						} else {
							String rst = resolve.resolve(condition, super.em);
							template = StringUtils.replace(template, define
									.getMetaName(), rst == null ? "" : rst);
						}
					} catch (Exception e) {
						e.printStackTrace();
						TransactionAspectSupport.currentTransactionStatus()
								.setRollbackOnly();
					}
				}
			}
		}
		return template;
	}

	@Transactional(readOnly = true)
	public Map<Integer, String> findPhoneNums(String userIds) {
		Map<Integer, String> map = new HashMap<Integer, String>();
		if (StringUtils.isNotBlank(userIds)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" SELECT T1.RID, T2.MB_NUM ");
			sb.append(" FROM TS_USER_INFO T1 ");
			sb.append(" INNER JOIN TB_SYS_EMP T2 ON T1.EMP_ID = T2.RID AND T2.MB_NUM IS NOT NULL ");
			sb.append(" WHERE T1.RID IN (").append(userIds).append(") ");
			List<Object[]> list = super.em.createNativeQuery(sb.toString())
					.getResultList();
			if (null != list && list.size() > 0) {
				for (Object[] o : list) {
					map.put(Integer.valueOf(o[0].toString()), o[1].toString());
				}
			}
		}
		return map;
	}

	@Transactional(readOnly = true)
	public List<Object[]> findLeaderOfUnit(Integer unitId) {
		Map<String, Integer> map = new LinkedHashMap<String, Integer>();
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT T1.USERNAME, T1.RID ");
		sb.append(" FROM TS_USER_INFO T1 ");
		sb.append(" INNER JOIN TB_SYS_EMP T2 ON T1.EMP_ID = T2.RID AND T2.IS_LEADER = '1' ");
		sb.append(" WHERE T1.UNIT_RID = '").append(unitId).append("' ");
		sb.append(" ORDER BY T2.NUM ");

		return super.em.createNativeQuery(sb.toString()).getResultList();
	}

	/**
	 * 根据地区编码获取地区全名
	 * 
	 * @param zoneCode
	 *            地区编码
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 * @createDate 2014-12-11
	 */
	@Transactional(readOnly = true)
	public String getZoneFullName(String zoneCode) {
		if (null != zoneCode && zoneCode.length() == 10) {
			StringBuilder codes = new StringBuilder();
			codes.append(zoneCode.substring(0, 2)).append("00000000");
			if (!"00".equals(zoneCode.substring(2, 4))) {
				codes.append(",").append(zoneCode.substring(0, 4))
						.append("000000");
			}
			if (!"00".equals(zoneCode.substring(4, 6))) {
				codes.append(",").append(zoneCode.substring(0, 6))
						.append("0000");
			}
			if (!"00".equals(zoneCode.substring(6, 8))) {
				codes.append(",").append(zoneCode.substring(0, 8)).append("00");
			}
			if (!"00".equals(zoneCode.substring(8, 10))) {
				codes.append(",").append(zoneCode);
			}
			StringBuilder tSql = new StringBuilder();
			tSql.append("SELECT T.ZONE_NAME FROM TS_ZONE T WHERE T.ZONE_GB ");
			tSql.append(" IN (").append(codes).append(") ORDER BY T.ZONE_GB");
			List<String> resultList = em.createNativeQuery(tSql.toString())
					.getResultList();
			if (null != resultList && resultList.size() > 0) {
				StringBuilder zoneName = new StringBuilder();
				for (int i = 0; i < resultList.size(); i++) {
					zoneName.append(resultList.get(i) == null ? "" : resultList
							.get(i).toString());
				}
				return zoneName.toString();
			}
		}
		return null;
	}

	@Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
	public List<TdTempmetaDefine> findTempDefineByTypeCode(String typeCode,
			MetaCondition condition) {
		if (StringUtils.isNotBlank(typeCode)) {
			List<TdTempmetaDefine> list = super.em
					.createNamedQuery("TdTempmetaDefine.findByTypeCode")
					.setParameter("tempCode", typeCode).getResultList();
			if (null != list && list.size() > 0) {
				for (TdTempmetaDefine define : list) {
					if (StringUtils.isNotBlank(define.getTempContent())) {
						define.setTempContent(this.resolveTemplatemeta(
								define.getTempContent(), condition, false));
					}
				}
				return list;
			}
		}
		return null;
	}

	@Transactional(readOnly = true)
	public TsRpt findTsRptPath(String rptCode, String virtualPath, String businessVersion) {
		if (StringUtils.isNotBlank(rptCode)) {
			List<TsRpt> list = super.em
					.createNamedQuery("TsRpt.findPathByCode")
					.setParameter("rptCod", rptCode).getResultList();
			if (null != list && list.size() > 0) {
				TsRpt rpt = list.get(0);
				// 读取fr3文件内容，赋值给rpt
				if (StringUtils.isNotBlank(rpt.getRptpath())) {
					String filePath = virtualPath + rpt.getRptpath();
					if(StringUtils.isNotBlank(businessVersion)) {
						filePath = filePath.replace(".fr3", "zwx"+businessVersion+".fr3");
					}
					File file = new File(filePath);
					if (file.exists()) {
						String FileContent = "";
						FileInputStream fis = null;
						InputStreamReader isr = null;
						try {
							fis = new FileInputStream(file);
							isr = new InputStreamReader(fis, "UTF-8");
							BufferedReader br = new BufferedReader(isr);
							String line = null;
							while ((line = br.readLine()) != null) {
								FileContent += line;
							}
						} catch (Exception e) {
							e.printStackTrace();
						} finally {
							try {
								if (fis != null) {
									fis.close();
								}
								if (isr != null) {
									isr.close();
								}
							} catch (Exception e) {
								e.printStackTrace();
							}
						}
						rpt.setFileContent(ZipUtils.gzip(FileContent.toString()));
					}
				}
				return rpt;
			}
		}
		return null;
	}

	public void updateReportTemplate(String rptCode, String rptContent, String virtualPath, String businessVersion) {
		String hql = "select t from TsRpt t where t.rptCod=:rptCod";
		List<TsRpt> list = super.em.createQuery(hql)
				.setParameter("rptCod", rptCode).getResultList();
		if (null != list && list.size() > 0) {
			TsRpt rpt = list.get(0);
			String filePath = virtualPath + rpt.getRptpath();
			if(StringUtils.isNotBlank(businessVersion)) {
				filePath = filePath.replace(".fr3", "zwx"+businessVersion+".fr3");
			}
			File file = new File(filePath);
			if (!file.exists()) {
				try {
					FileUtils.createFile(file);
				} catch (IOException e) {
					e.printStackTrace();
					TransactionAspectSupport.currentTransactionStatus()
							.setRollbackOnly();
				}
			}

			FileOutputStream fos = null;
			OutputStreamWriter osw = null;
			try {
				fos = new FileOutputStream(file);
				osw = new OutputStreamWriter(fos, "UTF-8");
				osw.write(rptContent);
				osw.flush();
			} catch (Exception e) {
				e.printStackTrace();
				TransactionAspectSupport.currentTransactionStatus()
						.setRollbackOnly();
			} finally {
				try {
					if (fos != null) {
						fos.close();
					}
					if (osw != null) {
						osw.close();
					}
				} catch (IOException e) {
					e.printStackTrace();
					TransactionAspectSupport.currentTransactionStatus()
							.setRollbackOnly();
				}
			}

			rpt.setRptver(rpt.getRptver() + 1);
			this.update(rpt);
		}
	}

	@Transactional(readOnly = true)
	public List<TdMsBookmark> findBookmarkList(String recordId) {
		if (StringUtils.isNotBlank(recordId)) {
			return super.em.createNamedQuery("TdMsBookmark.findByRecordId")
					.setParameter("recordid", recordId).getResultList();
		}
		return null;
	}

	@Transactional(readOnly = true)
	public Map<String, String> resolveBookmarks(String recordId,
			MetaCondition condition) {
		Map<String, String> map = new HashMap<String, String>();
		if (StringUtils.isNotBlank(recordId)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" SELECT T1.BOOKMARK_NAME, T2.IMP_CLASSNAME ");
			sb.append(" FROM TD_MS_BOOKMARK T1 ");
			sb.append(" INNER JOIN TB_TEMPMETA_DEFINE T2 ON T1.BOOKMARK_NAME = T2.META_ENAME ");
			sb.append(" WHERE T1.RECORDID = '").append(recordId).append("' ");

			List<Object[]> list = super.em.createNativeQuery(sb.toString())
					.getResultList();
			if (null != list && list.size() > 0) {
				for (Object[] o : list) {
					try {
						ITempmetaResolve resolve = SpringContextHolder
								.getBean(o[1].toString());
						String rst = resolve.resolve(condition, super.em);
						map.put(o[0].toString(), rst);
					} catch (Exception e) {
						e.printStackTrace();
						TransactionAspectSupport.currentTransactionStatus()
								.setRollbackOnly();
					}
				}
			}
		}
		return map;
	}

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findTsSimpleCodeByCodeTypeNoAndLevel(
			String codeTypeNo, Integer level) {
		int lev = 0;
		if (level != null) {
			if (level == 1) {
				lev = 0;
			} else {
				lev = (level - 2) * 5;
			}
		}
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TsSimpleCode t ");
		sb.append(" where t.tsCodeType.codeTypeName like '").append(codeTypeNo)
				.append("' ");
		if (level != null) {
			sb.append(" and instr(t.codeLevelNo,'.') = ").append(lev);
		}
		sb.append(" order by t.codeNo ");
		return (List<TsSimpleCode>) em.createQuery(sb.toString())
				.getResultList();
	}

	/**
	 * 根据用户Id获取其所在的科室及兼职科室的Id用逗号分割<br />
	 * 
	 * @param userId
	 *            用户Id
	 * @return
	 * <AUTHOR>
	 * @createDate 2015-4-16
	 */
	@Transactional(readOnly = true)
	public String findUserAllOfficeIds(Integer userId) {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT LISTAGG(C.OFFICEID,',') WITHIN GROUP (ORDER BY C.OFFICEID) AS OFFICEIDS ");
		sql.append(" FROM TS_USER_INFO T1 INNER JOIN ( SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM,");
		sql.append(" 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
		sql.append(" UNION ALL");
		sql.append(" SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
		sql.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
		sql.append(" WHERE 1=1 AND T1.IF_REVEAL='1' AND T1.RID = '")
				.append(userId).append("' ");
		List resultList = this.em.createNativeQuery(sql.toString())
				.getResultList();
		if (null != resultList && resultList.size() > 0) {
			Object result = resultList.get(0);
			return result == null ? null : result.toString();
		}
		return null;
	}
	
	public List<TsUserInfo> findUserByUserIds(String userIds){
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TsUserInfo t where t.rid in (").append(userIds).append(")");
		return this.em.createQuery(sb.toString()).getResultList();
	}

	/**
	 * 根据科室Ids获取科室集合
	 * 
	 * @param officeIds
	 *            科室Ids
	 * @return
	 * <AUTHOR>
	 * @createDate 2015-4-16
	 */
	@Transactional(readOnly = true)
	public List<TsOffice> findOfficesByIds(String officeIds) {
		if (StringUtils.isNotBlank(officeIds)) {
			StringBuilder sql = new StringBuilder();
			sql.append("select t from TsOffice t where t.rid in (")
					.append(officeIds).append(")");
			sql.append(" order by t.num");
			List<TsOffice> resultList = this.em.createQuery(sql.toString())
					.getResultList();
			return resultList;
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findScAllOrNoByTypeId(String typeNo,boolean ifAllUse) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (").append(typeNo)
					.append(") ");
			if (ifAllUse) {
				sb.append(" and t.ifReveal= 1");
			}
			sb.append(" order by t.codeLevelNo ,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TsTxtype> findAllUseableTxtype() {
		return super.em.createNamedQuery("TsTxtype.findUsable").getResultList();
	}

	@Transactional(readOnly = true)
	public List<TsTxtype> findTxtypeByIds(String ids) {
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT t FROM TsTxtype t WHERE t.status=1 AND t.rid in (")
				.append(ids).append(")");
		return super.em.createQuery(sb.toString()).getResultList();
	}

	@Transactional(readOnly = true)
	public String getDesc() {
		return "公用工具服务";
	}

	@Transactional(readOnly = true)
	public String getVersion() {
		return "1.0.0";
	}

	@Transactional(readOnly = true)
	public TsCodeType findCodeTypeByCodeTypeName(String codeTypeName) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TsCodeType t where t.codeTypeName = '")
				.append(codeTypeName).append("'");
		List<TsCodeType> result = em.createQuery(sb.toString()).getResultList();
		if (result != null && result.size() > 0) {
			return result.get(0);
		}
		return null;
	}

	@Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleCodeListByTypeNo(String typeNo) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select new TsSimpleCode(t.codeNo, t.codeName) from TsSimpleCode t ");
			sb.append(" where t.ifReveal=1 and t.tsCodeType.codeTypeName in (")
					.append(typeNo).append(") order by t.num,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}
	
	/**
 	 * <p>方法描述：暂不使用</p>
 	 * @MethodAuthor qrr,2020年11月3日,executeUpdate
     * */
    @Deprecated
	public void executeUpdate(String sql) {
		super.em.createNativeQuery(sql).executeUpdate();
	}

	/**
	 * 加载地区下某一级地区
	 * @param zoneCode
	 * @param i
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TsZone> getNextLvZone(String zoneCode, int i) {
		StringBuilder sb = new StringBuilder(
				" select new TsZone(t.rid, t.zoneGb, t.zoneName, t.zoneType, t.zoneCode, t.dsfCode) from TsZone t where t.ifReveal='1' ");
		sb.append(" and t.zoneCode like '")
				.append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
			sb.append(" and t.zoneType ='").append(i).append("' ");
		sb.append(" order by t.zoneCode ");
		return em.createQuery(sb.toString()).getResultList();
	}
	
	/**
	 * 地区方法优化，获取用户的下两级地区
	 * @param admin	是否是超管
	 * @param zoneCode	普通用户从某一级开始加载
	 * @param zoneTypeMin	超管从某一级开始加载	
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TsZone> findZoneListImprove(Boolean admin, String zoneCode,
			String zoneTypeMinStr) {
		StringBuilder sb = new StringBuilder(
				" select new TsZone(t.rid, t.zoneGb, t.zoneName, t.zoneType, t.zoneCode, t.dsfCode) from TsZone t where t.ifReveal='1' ");
		if (null == admin || !admin) {
			//不是超管直接获取下一级zoneTypeMin可以为null,zoneTypeMax赋值地区类型+1
			sb.append(" and t.zoneCode like '").append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
			sb.append(" and t.zoneType <='").append(ZoneUtil.getZoneType(zoneCode)+1).append("' ");
		}else{
			//超管直接根据最小级别获取下一级，如果最小级别为空自动默认最小级别
			Integer zoneTypeMin = 1;
			if (StringUtils.isNotBlank(zoneTypeMinStr))
				try{
					zoneTypeMin = Integer.valueOf(zoneTypeMinStr);
				}catch(NumberFormatException e){
					zoneTypeMin = 1;
				}
			sb.append(" and t.zoneType >='").append(zoneTypeMin).append("' ");
			sb.append(" and t.zoneType <='").append((zoneTypeMin+1)>6?6:(zoneTypeMin+1)).append("' ");
		}
		sb.append(" order by t.zoneCode ");
		return em.createQuery(sb.toString()).getResultList();
	}
	
	@Transactional(readOnly = true)
	public TsSimpleCode findTsSimpleCodeByRid(Integer rid){
		StringBuilder sb=new StringBuilder();
		sb.append("select t from TsSimpleCode t where 1=1 and t.rid=").append(rid);
		List<TsSimpleCode> list=em.createQuery(sb.toString()).getResultList();
		if(null != list && list.size()>0){
			return list.get(0);
		}
		return null;
	}
	
	@Transactional(readOnly = true)
	public Object[] getAddressByRid(Integer rid){
		StringBuilder sb=new StringBuilder();
		sb.append("select ARCH_PATH,ARCH_TITLE from TD_ARCH_OUT where rid = ").append(rid);
		List<Object[]> list=em.createNativeQuery(sb.toString()).getResultList();
		if(null != list && list.size()>0){
			return list.get(0);
		}
		return null;
	}
	
	@Transactional(readOnly = true)
	public TsZone findParentZoneNew(String zoneCode) {
		if(StringUtils.isNotBlank(zoneCode))	{
			TsZone zone = null;
			String parentCode = ZoneUtil.getParentCode(zoneCode);
			
			StringBuilder hql = new StringBuilder();
			hql.append("select new TsZone(t.rid, t.zoneGb, t.zoneName,t.zoneType,t.zoneCode) ");
			hql.append("from TsZone t where t.ifReveal = 1 ");
			if(zoneCode.length() == 10 )	{//10位地区编码
				hql.append(" and t.zoneGb = '").append(parentCode).append("'");
			}else if(zoneCode.length() == 12 )	{//12位地区编码
				hql.append(" and t.zoneCode = '").append(parentCode).append("'");
			}else{
				return null;
			}
			List<TsZone> list = em.createQuery(hql.toString()).getResultList();
			if (null != list && list.size() > 0) {
				zone = list.get(0);
			}
			return zone;
		}
		return null;
	}
	@Transactional(readOnly = true)
	public List<TsZone> findZoneCountyNew(Boolean admin, String zoneCode,
			String zoneTypeMin, String zoneTypeMax) {
		StringBuilder sb = new StringBuilder(
				" select new TsZone(t.rid, t.zoneGb, t.zoneName, t.zoneType, t.zoneCode, t.dsfCode) from TsZone t where t.ifReveal='1' ");
		if (null == admin || !admin) {
			sb.append(" and t.zoneCode like '")
					.append(ZoneUtil.zoneSelect(zoneCode)).append("%' ");
		}
//		if (StringUtils.isNotBlank(zoneTypeMin)) {
//			sb.append(" and t.zoneType >='").append(zoneTypeMin).append("' ");
//		}
		if (StringUtils.isNotBlank(zoneTypeMax)) {
			sb.append(" and t.zoneType ='").append(zoneTypeMax).append("' ");
		}
		sb.append(" order by t.zoneCode ");
		return em.createQuery(sb.toString()).getResultList();
	}
	
	@Transactional(readOnly = true)
	public List<Object[]> getSqlList(String sql){
		return em.createNativeQuery(sql.toString()).getResultList();
	}
	
	 /**
     * 获取分管领导
     */
    public List<TsUserInfo> findOfficeLeaders(Integer unitId) {
    	StringBuilder sb = new StringBuilder();
		sb.append(" select t from TsUserInfo t ");
		sb.append("  where exists( ");
		sb.append(" 	   select 1 from TsOffice t1 ");
		sb.append("         where t1.tbSysEmpByManageManid.rid = t.tbSysEmp.rid ");
		sb.append(" 	      and t1.tbSysEmpByManageManid.rid is not null ) ");
		sb.append("    and t.tsUnit.rid = ").append(unitId);
		sb.append("    and t.ifReveal = 1 ");
		List<TsUserInfo> userList = this.em.createQuery(sb.toString()).getResultList();
		return userList;
    }
    
    /**
     * 获取科室负责人
     * @param unitId
     * @return
     */
    public List<TsUserInfo> findOfficeHeader(Integer unitId) {
    	StringBuilder sb = new StringBuilder();
		sb.append(" select t from TsUserInfo t ");
		sb.append("  where exists( ");
		sb.append(" 	   select 1 from TsOffice t1 ");
		sb.append("         where t1.tbSysEmpByDeptLeaderId.rid = t.tbSysEmp.rid ");
		sb.append(" 	      and t1.tbSysEmpByDeptLeaderId.rid is not null ) ");
		sb.append("    and t.tsUnit.rid = ").append(unitId);
		sb.append("    and t.ifReveal = 1 ");
		List<TsUserInfo> userList = this.em.createQuery(sb.toString()).getResultList();
		return userList;
    }
    
    /**
	 * 根据科室ID查找所有的科员信息
	 */
	public List<TsUserInfo> getAllUserByOfficId(Integer officeId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TsUserInfo t where t.tbSysEmp.tsOffice.rid = ").append(officeId);
		sb.append("    and t.tbSysEmp.isLeader = 0 ");
		sb.append("    and t.ifReveal = 1 ");
		List<TsUserInfo> userList = this.em.createQuery(sb.toString()).getResultList();
		if (userList != null && userList.size() > 0) {
			for (TsUserInfo user : userList) {
				user.setOfficeNames(user.getTbSysEmp().getTsOffice().getOfficename());
			}
		}
		
		return userList;
	}
	
	public List<Object[]> getMsgRid(String sql,Map<String, Object> paramMap){
		Query query=em.createNativeQuery(sql);
		if (null != paramMap && paramMap.size() > 0) {
			Iterator itor = paramMap.entrySet().iterator();
			while (itor.hasNext()) {
				Map.Entry entry = (Map.Entry) itor.next();
				query.setParameter(String.valueOf(entry.getKey()),
						entry.getValue());
			}
		}
		return query.getResultList();
	}
	@Transactional(readOnly = true)
	public BigDecimal findParentHelpamo(Integer unitRid,String year) {
		StringBuilder sb = new StringBuilder();
		String parentcode = null;
		sb.append(" SELECT a.zone_code ");
		sb.append("   from TS_UNIT t  left join ts_zone a on a.rid = t.zone_id where t.rid ='").append(unitRid).append("'");
		List<Object> zonecode = em.createNativeQuery(sb.toString()).getResultList();
		if (zonecode!=null && zonecode.size()>0) {
			for (Object obj : zonecode) {
					parentcode = ZoneUtil.getParentCode(obj.toString());
			}
		}
		if (StringUtils.isNotBlank(parentcode)) {
			StringBuilder s = new StringBuilder(); 
			s.append(" SELECT t.help_amo  from TB_SC_PLANDETAIL t  left join TB_SC_HELPPLAN ss on ss.rid = t.main_id");
			s.append("  where t.ORG_ID in (select s.rid  from ts_zone tt left join TS_UNIT s on tt.rid = s.zone_id where tt.zone_code = '").append(parentcode).append("'").append(")");
			s.append("   and ss.years ='").append(year).append("'");
			List<BigDecimal> parentamo = em.createNativeQuery(s.toString()).getResultList();
			if (parentamo!=null && parentamo.size()>0 ) {
				for (BigDecimal objects : parentamo) {
					return  objects;
				}
			}
		}
		return null;
	}

@Transactional(readOnly = true)
public Object[] findHelpamo(Integer unitRid, Integer planId) {
	StringBuilder sb = new StringBuilder();
	String parentcode = null;
	sb.append(" SELECT a.zone_code ");
	sb.append("   from TS_UNIT t  left join ts_zone a on a.rid = t.zone_id where t.rid ='").append(unitRid).append("'");
	List<Object> zonecode = em.createNativeQuery(sb.toString()).getResultList();
	if (zonecode!=null && zonecode.size()>0) {
		for (Object obj : zonecode) {
				parentcode = ZoneUtil.getParentCode(obj.toString());
		}
	}
	StringBuilder s = new StringBuilder();
	s.append(" SELECT COUNT(1), SUM(APPLY_AMO) ");
	s.append("   FROM TD_SC_HELPAPPLY A  left join TS_UNIT b on b.rid = A.ORG_ID left join  ts_zone c  on c.rid = b.zone_id  WHERE PLAN_ID = '").append(planId)
			.append("'");
	s.append("    AND c.ZONE_CODE like '%").append(parentcode.subSequence(0, 4)).append("%'");
	List<Object[]> data = this.em.createNativeQuery(s.toString()).getResultList();
	if (data != null && data.size() > 0) {
		return  data.get(0);
	}
	return null;
}

	public List<TsKletype> findKletypeList(String type){
		StringBuilder sb=new StringBuilder();
		sb.append("select t from TsKletype t where t.types = ").append(type);
		return em.createQuery(sb.toString()).getResultList();
	}
	
	
	public List<LucenePojo> findFileList(){
		StringBuilder sb=new StringBuilder();
		sb.append(" select T.RID,T.KLE_NAME,T.KLE_KEYS,T1.TYPE_NAME,");
		sb.append(" T1.TYPES,T2.FILE_NAME,T2.FILE_PATH,T.CREATE_DATE,");
		sb.append(" T3.UNIT_RID,T7.ZONE_CODE,wm_concat(t6.Sort_Code) as sorts,");
		sb.append(" T.KLE_TYPE_ID,T.KLE_KEYS,nvl(T.STATE,0)state");
		sb.append(" from TS_KLEMAIN T");
		sb.append(" LEFT JOIN TS_KLETYPE T1 ON T.KLE_TYPE_ID = T1.RID");
		sb.append(" LEFT JOIN TS_KLEANX T2 ON T.RID = T2.MAIN_ID");
		sb.append(" LEFT JOIN TS_USER_INFO T3 ON T.FB_MANID = T3.RID");
		sb.append(" LEFT JOIN TS_UNIT T4 ON T3.UNIT_RID = T4.RID");
		sb.append(" LEFT JOIN ts_unit_attr T5 ON T4.RID = T5.UNIT_RID");
		sb.append(" LEFT JOIN TS_BS_SORT T6 ON T5.ATTR_ID = T6.RID");
		sb.append(" LEFT JOIN TS_ZONE T7 ON T4.ZONE_ID = T7.RID");
		sb.append(" WHERE T.STATE=3");
		sb.append(" group by T.RID,T.KLE_NAME,T.KLE_KEYS,T1.TYPE_NAME,T1.TYPES,");
		sb.append(" T2.FILE_NAME,T2.FILE_PATH,T.CREATE_DATE,T3.UNIT_RID,T7.ZONE_CODE,T.KLE_TYPE_ID,T.KLE_KEYS,T.STATE");
		sb.append(" order by T.CREATE_DATE DESC");
		List<Object[]> list=em.createNativeQuery(sb.toString()).getResultList();
		if(null != list && list.size()>0){
			List<LucenePojo> fileList=new ArrayList<>();
			for(int i=0;i<list.size();i++){
				Object[] obj=list.get(i);
				LucenePojo pojo=new LucenePojo();
				pojo.setRid(obj[0].toString());
				pojo.setAnnexName(null != obj[5]?obj[5].toString():null);
				pojo.setAnnexPath(null != obj[6]?obj[6].toString():null);
				pojo.setKleTitle(null != obj[1]?obj[1].toString():null);
				pojo.setInfoType(null != obj[4]?obj[4].toString():null);
				pojo.setZoneGb(null != obj[9]?obj[9].toString():null);
				pojo.setSortCodes(null != obj[10]?obj[10].toString():null);
				pojo.setUnitId(null != obj[8]?obj[8].toString():null);
				pojo.setPicType(null != obj[11]?obj[11].toString():null);
				pojo.setKeyWords(null != obj[12]?obj[12].toString():null);
				pojo.setState(null != obj[13]?obj[13].toString():null);
				try {
					DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
					pojo.setModifyDate(null != obj[7]?format.parse(obj[7].toString()):null);
				} catch (ParseException e) {
					e.printStackTrace();
				}
				fileList.add(pojo);
			}
			return fileList;
		}
		return null;
	}
	
	public List<Object[]> findPicList(String picDesc,String picTypes){
		StringBuilder sb=new StringBuilder();
		sb.append(" select T.KLE_NAME,T1.FILE_NAME,T1.FILE_PATH from TS_KLEMAIN T");
		sb.append(" LEFT JOIN TS_KLEANX T1 ON T1.MAIN_ID=T.RID");
		sb.append(" LEFT JOIN TS_KLETYPE T2 ON T.KLE_TYPE_ID=T2.RID");
		sb.append(" WHERE T2.TYPES=2");
		if(StringUtils.isNotBlank(picDesc)){
			sb.append(" AND (T.KLE_NAME LIKE '%").append(picDesc).append("%'");
			sb.append(" or T.KLE_KEYS LIKE '%").append(picDesc).append("%'");
			sb.append(" or T1.FILE_NAME LIKE '%").append(picDesc).append("%'").append(")");
		}
		if(StringUtils.isNotBlank(picTypes)){
			sb.append(" AND T2.RID IN (").append(picTypes).append(")");
		}
		sb.append(" ORDER BY T2.CREATE_DATE DESC,T2.LVL_CODE");
		return em.createNativeQuery(sb.toString()).getResultList();
	}
	/**
	 * 根据科室ID查找所有的人员信息
	 */
	public List<TsUserInfo> getUsersByOfficId(Integer officeId) {
		StringBuilder sb = new StringBuilder();
		sb.append(" select t from TsUserInfo t where t.tbSysEmp.tsOffice.rid = ").append(officeId);
		sb.append("    and t.tbSysEmp.onduty = 1 ");
		sb.append("    and t.ifReveal = 1 ");
		sb.append("    order by t.tbSysEmp.isLeader,t.tbSysEmp.num");
		List<TsUserInfo> userList = this.em.createQuery(sb.toString()).getResultList();
		
		return userList;
	}
	@Transactional(readOnly = true)
	public Map<Integer, String> findEmailByEmail(String userIds) {
		Map<Integer, String> map = new HashMap<Integer, String>();
		if (StringUtils.isNotBlank(userIds)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" SELECT T1.RID, T1.EMAIL ");
			sb.append(" FROM TS_USER_INFO T1 ");
			sb.append(" WHERE T1.RID IN (").append(userIds).append(") ");
			sb.append(" AND T1.EMAIL IS NOT NULL");
			List<Object[]> list = super.em.createNativeQuery(sb.toString()).getResultList();
			if (null != list && list.size() > 0) {
				for (Object[] o : list) {
					map.put(Integer.valueOf(o[0].toString()), o[1].toString());
				}
			}
		}
		return map;
	}
	@Transactional(readOnly = true)
	public float findTotalTime(String userName,String whdw,String jjjg,String wtdx,String whrq,String whnextrq,String whxs,String off) {
		float sumTime=(float) 0.0;	
		StringBuilder sb = new StringBuilder();
		sb.append(" SELECT sum(T.WHCXSJ) as SUMTIME");
		sb.append("  FROM TZ_XCWH T  LEFT JOIN TS_SIMPLE_CODE S0 ON T.WHDW = S0.CODE_NO  INNER JOIN TS_CODE_TYPE  CS0 ON S0.CODE_TYPE_ID  = CS0.RID AND CS0.CODE_TYPE_NAME=100002");
		sb.append("  LEFT JOIN TS_SIMPLE_CODE S1 ON T.JJJG = S1.CODE_NO  ");
		sb.append(" INNER JOIN TS_CODE_TYPE  CS1 ON S1.CODE_TYPE_ID  = CS1.RID  AND CS1.CODE_TYPE_NAME=100004 ");
		sb.append(" LEFT JOIN TS_SIMPLE_CODE S2 ON T.WTDX = S2.CODE_NO  INNER JOIN TS_CODE_TYPE  CS2 ON S2.CODE_TYPE_ID  = CS2.RID  AND CS2.CODE_TYPE_NAME=100007 ");
		sb.append(" INNER JOIN TS_USER_INFO CS3 on CS3.rid = t.whr ");
		if (StringUtils.isNotBlank(off)) {
			if ("1".equals(off)) {
				sb.append(" INNER JOIN TB_SYS_EMP CS4 on CS3.EMP_ID = CS4.rid INNER JOIN TS_OFFICE CS5 on CS5.rid = CS4.DEPT_ID and CS5.Officecode ='201488'  WHERE 1=1 ");
			}
			if ("2".equals(off)) {
				sb.append(" INNER JOIN TB_SYS_EMP CS4 on CS3.EMP_ID = CS4.rid INNER JOIN TS_OFFICE CS5 on CS5.rid = CS4.DEPT_ID and CS5.Officecode ='201410'  WHERE 1=1 ");
			}
		}
		if (StringUtils.isNotBlank(userName)) {
			sb.append(" AND  T.WHRXM  LIKE '%").append(userName).append("%'");
		}
		if (StringUtils.isNotBlank(whdw)) {
			sb.append(" AND T.WHDW =").append(whdw);
		}
		if (StringUtils.isNotBlank(jjjg)) {
			sb.append(" AND T.JJJG =").append(jjjg);
		}
		if (StringUtils.isNotBlank(wtdx)) {
			sb.append(" AND T.WTDX =").append(wtdx);
		}
		if (StringUtils.isNotBlank(whrq)) {
			sb.append(" AND TO_CHAR(T.WHRQ ,'yyyy-MM-dd')  >= '").append(whrq).append("'");
		}
		if (StringUtils.isNotBlank(whnextrq)) {
			sb.append(" AND TO_CHAR(T.WHRQ ,'yyyy-MM-dd')  <= '").append(whnextrq).append("'");
		}
		if (StringUtils.isNotBlank(whxs)) {
			sb.append(" AND T.WHCXSJ LIKE '%").append(whxs).append("%'");
		}
		List<Object> list = super.em.createNativeQuery(sb.toString()).getResultList();
		if (null != list && list.size() > 0 && list.get(0)!=null) {
			return Float.parseFloat(list.get(0).toString());
		}
		return (float) 0.0;
	}
	
	
	public List<TsSimpleCode> findUpCodeList(String typeNo){
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.ifReveal=1 and t.tsCodeType.codeTypeName in (")
					.append(typeNo).append(") ");
			sb.append(" and t.codeNo=t.codeLevelNo");
			sb.append(" order by t.num,t.codeNo");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}
	/**
 	 * <p>方法描述：根据码表类型、扩展字段1获取码表数据</p>
 	 * @MethodAuthor qrr,2018年5月3日,findSimpleByParam
	 * */
    public List<Object[]> findSimpleByParam(String typeNo, String extends1) {
        StringBuffer buffer = new StringBuffer();
		Map<String, Object> paramMap  = new HashMap<>();
        buffer.append(
                "SELECT T.RID,T.CODE_NAME,T.CODE_NO,T.CODE_LEVEL_NO,T.SPLSHT,T.EXTENDS1,T.EXTENDS2,T.EXTENDS3,T.EXTENDS4,T.CODE_DESC FROM TS_SIMPLE_CODE T LEFT JOIN TS_CODE_TYPE T1 ON T1.RID = T.CODE_TYPE_ID WHERE 1=1");
		buffer.append(" AND T1.CODE_TYPE_NAME = :CODE_TYPE_NAME");
		paramMap.put("CODE_TYPE_NAME",  StringUtils.trim(typeNo) );
        buffer.append(" AND T.IF_REVEAL = 1 ");
        if (StringUtils.isNotBlank(extends1)) {
            buffer.append("AND T.EXTENDS1 = :EXTENDS1");
			paramMap.put("EXTENDS1",  StringUtils.trim(extends1) );
        }
        buffer.append(" order by t.num,t.CODE_LEVEL_NO,t.CODE_NO");
        List<Object[]> sqlResultList = super.findDataBySqlNoPage(buffer.toString(),paramMap);
        return sqlResultList;
    }

	/**
	 * <p>方法描述：根据码表类型、扩展字段1获取码表数据、排除记录</p>
	 * */
	public List<Object[]> findSimpleByParam2(String typeNo, String extends1,String excluderids) {
		StringBuffer buffer = new StringBuffer();
		Map<String, Object> paramMap  = new HashMap<>();
		buffer.append(
				"SELECT T.RID,T.CODE_NAME,T.CODE_NO,T.CODE_LEVEL_NO,T.SPLSHT,T.EXTENDS1,T.EXTENDS2,T.EXTENDS3,T.EXTENDS4,T.CODE_DESC " +
						"FROM TS_SIMPLE_CODE T LEFT JOIN TS_CODE_TYPE T1 ON T1.RID = T.CODE_TYPE_ID WHERE 1=1");
		buffer.append(" AND T1.CODE_TYPE_NAME = :CODE_TYPE_NAME");
		paramMap.put("CODE_TYPE_NAME",  StringUtils.trim(typeNo) );
		buffer.append(" AND T.IF_REVEAL = 1 ");
		if (StringUtils.isNotBlank(extends1)) {
			buffer.append("AND T.EXTENDS1 = :EXTENDS1");
			paramMap.put("EXTENDS1",  StringUtils.trim(extends1) );
		}
		if(StringUtils.isNotBlank(excluderids)) {
			buffer.append("AND T.RID NOT IN (:excluderids)");
			List<String> list = Arrays.asList(excluderids.split(","));
			paramMap.put("excluderids",  list );
		}
		buffer.append(" order by t.num,t.CODE_LEVEL_NO,t.CODE_NO");
		List<Object[]> sqlResultList = super.findDataBySqlNoPage(buffer.toString(),paramMap);
		return sqlResultList;
	}

	/**
 	 * <p>方法描述：根据码表大类获取大类以及小类数据</p>
 	 * @MethodAuthor qrr,2018年6月21日,findSimpleCodeByAndLevelCode
	 * @param typeNo
	 * @param ifAllUse
	 * @param levelCodeNo
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleCodeByAndLevelCode(
			String typeNo, boolean ifAllUse, String levelCodeNo) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (").append(typeNo)
					.append(") ");
			if (ifAllUse) {
				sb.append(" and t.ifReveal= 1");
			}
			if (StringUtils.isNotBlank(levelCodeNo)) {
				String[] split = levelCodeNo.split(",");
				sb.append(" and (");
				StringBuffer buffer = new StringBuffer();
				for (String string : split) {
					buffer.append(" or t.codeLevelNo like '").append(string).append(".")
					.append("%'");
					buffer.append(" or t.codeLevelNo ='").append(string).append("'");
				}
				sb.append(buffer.substring(3));
				sb.append(" )");
			}
			sb.append(" order by t.codeLevelNo ,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

	public List<Object[]> getCrpt(String searchCrptName,String searchCrptCode) {
		StringBuffer buffer = new StringBuffer();
		buffer.append("select t.rid,t.SAFETY_PRINCIPAL,t.SAFEPHONE,t.ADDRESS,t.POSTCODE,t1.zone_name,t.CRPT_NAME,t.INSTITUTION_CODE,t1.zone_type ");
		buffer.append(" ,t.ECONOMY_ID ,t.INDUS_TYPE_ID ,t.CRPT_SIZE_ID from TB_TJ_CRPT t left join TS_ZONE t1 on t.ZONE_ID = t1.rid where 1=1 ");
		// 单位名称
		if (StringUtils.isNotBlank(searchCrptName)) {
			buffer.append(" and t.CRPT_NAME like :crptname");
		}
		// 社会信用代码
		if (StringUtils.isNotBlank(searchCrptCode)) {
			buffer.append(" and t.INSTITUTION_CODE like :crptno");
		}
		buffer.append(" and rownum<=50 order by t1.zone_Code");
		Query query = em.createNativeQuery(buffer.toString());
		if (StringUtils.isNotBlank(searchCrptName)) {
			query.setParameter("crptname", "%"+searchCrptName+"%");
		}
		if (StringUtils.isNotBlank(searchCrptCode)) {
			query.setParameter("crptno", "%"+searchCrptCode+"%");
		}
		List<Object[]> list = query.getResultList();
		
		return list;
	}

	/**
	* @Description : 根据码表类型、码表描述查询码表集合
	* @MethodAuthor: anjing
	* @Date : 2021/3/11 13:59
	**/
    @Transactional(readOnly = true)
    public List<TsSimpleCode> findSimpleCodesByTypeIdAndCodeDesc(String typeNo, String codeDesc) {
        if(StringUtils.isNotBlank(typeNo)) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TsSimpleCode t ");
            sb.append(" where t.ifReveal=1 ");
            sb.append(" and t.tsCodeType.codeTypeName in (").append(typeNo).append(") ");
            if(StringUtils.isNotBlank(codeDesc)) {
                sb.append(" and (t.codeDesc like '%").append(codeDesc).append("%' ");
                sb.append(" or t.codeDesc is null) ");
            }
            sb.append(" order by t.num,t.codeNo ");
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

	public List<Object[]> findSimpleByParamTwo(String typeNo, String extends1, String showIds, String ifShowExpiryDate) {
        StringBuffer buffer = new StringBuffer();
		Map<String, Object> paramMap  = new HashMap<>();
        buffer.append(
                "SELECT T.RID,T.CODE_NAME,T.CODE_NO,T.CODE_LEVEL_NO,T.SPLSHT,T.EXTENDS1,T.EXTENDS2,T.EXTENDS3,T.EXTENDS4,T.EXTENDS5,T.CODE_PATH,T.CODE_DESC FROM TS_SIMPLE_CODE T LEFT JOIN TS_CODE_TYPE T1 ON T1.RID = T.CODE_TYPE_ID WHERE 1=1 ");
		buffer.append(" AND T1.CODE_TYPE_NAME = :CODE_TYPE_NAME");
		paramMap.put("CODE_TYPE_NAME",  StringUtils.trim(typeNo) );
        buffer.append(" AND T.IF_REVEAL = 1 ");
        if (StringUtils.isNotBlank(extends1)) {
			buffer.append("AND T.EXTENDS1 = :EXTENDS1");
			paramMap.put("EXTENDS1",  StringUtils.trim(extends1) );
        }
        if(StringUtils.isNotBlank(showIds)) {
			buffer.append("AND T.RID IN (:RID)");
			List<String> list = Arrays.asList(showIds.split(","));
			paramMap.put("RID",  list );
        }
		if("1".equals(ifShowExpiryDate)){
			buffer.append(" order by T.CODE_NAME ");
		}else{
			buffer.append(" order by t.num,t.CODE_LEVEL_NO,t.CODE_NO");
		}
		List<Object[]> sqlResultList = super.findDataBySqlNoPage(buffer.toString(),paramMap);
        return sqlResultList;
    }
	/**
	 *  <p>方法描述：码表多选弹出框-新增方法</p>
	 * @MethodAuthor hsj 2024-10-28 15:50
	 */
	public List<Object[]> findSimpleByParamTwo(String typeNo, String extends1, String showIds, String ifShowExpiryDate, String notSelectIds) {
        StringBuffer buffer = new StringBuffer();
		Map<String, Object> paramMap  = new HashMap<>();
        buffer.append(
                "SELECT T.RID,T.CODE_NAME,T.CODE_NO,T.CODE_LEVEL_NO,T.SPLSHT,T.EXTENDS1,T.EXTENDS2,T.EXTENDS3,T.EXTENDS4,T.EXTENDS5,T.CODE_PATH,T.CODE_DESC FROM TS_SIMPLE_CODE T LEFT JOIN TS_CODE_TYPE T1 ON T1.RID = T.CODE_TYPE_ID WHERE 1=1 ");
		buffer.append(" AND T1.CODE_TYPE_NAME = :CODE_TYPE_NAME");
		paramMap.put("CODE_TYPE_NAME",  StringUtils.trim(typeNo) );
        buffer.append(" AND T.IF_REVEAL = 1 ");
        if (StringUtils.isNotBlank(extends1)) {
			buffer.append("AND T.EXTENDS1 = :EXTENDS1");
			paramMap.put("EXTENDS1",  StringUtils.trim(extends1) );
        }
        if(StringUtils.isNotBlank(showIds)) {
			buffer.append("AND T.RID IN (:RID)");
			List<String> list = Arrays.asList(showIds.split(","));
			paramMap.put("RID",  list );
        }
        if(StringUtils.isNotBlank(notSelectIds)) {
			buffer.append(" AND T.RID NOT IN (:notRid)");
			List<String> list = Arrays.asList(notSelectIds.split(","));
			paramMap.put("notRid",  list );
        }
		if("1".equals(ifShowExpiryDate)){
			buffer.append(" order by T.CODE_NAME ");
		}else{
			buffer.append(" order by t.num,t.CODE_LEVEL_NO,t.CODE_NO");
		}
		List<Object[]> sqlResultList = super.findDataBySqlNoPage(buffer.toString(),paramMap);
		if (CollectionUtils.isEmpty(sqlResultList)) {
			return sqlResultList;
		}
		List<String> includRids = new ArrayList<>();
		List<String> removeRids = new ArrayList<>();
		if (StringUtils.isNotBlank(showIds)) {
			includRids = Arrays.asList(showIds.split(","));
		}
		if (StringUtils.isNotBlank(notSelectIds)) {
			removeRids = Arrays.asList(notSelectIds.split(","));
		}
		List<Object[]> removeList = new ArrayList<>();
		for (Object[] objects : sqlResultList) {
			String curRid = null == objects[0] ? null : objects[0].toString();
			if (null == curRid || (!CollectionUtils.isEmpty(includRids) && !includRids.contains(curRid)) || (!CollectionUtils.isEmpty(removeRids) && removeRids.contains(curRid))) {
				removeList.add(objects);
			}
		}
		if (!CollectionUtils.isEmpty(removeList)) {
			sqlResultList.removeAll(removeList);
		}
		return sqlResultList;
    }

	/**
	 * 码表多选弹出框
	 * v3
	 *
	 * @param typeNo       码表编码
	 * @param extends1     过滤拓展字段1
	 * @param showIds      查询RID范围内
	 * @param orderBy      排序方式 <pre>1：CODE_NAME</pre><pre>其他：NUM, CODE_LEVEL_NO, CODE_NO</pre>
	 * @param notSelectIds 排除的RID
	 * @param codeLevelNo  过滤CODE_LEVEL_NO
	 * @param onlyLevelNo  只查询指定级别的数据(根据CODE_LEVEL_NO的.的数量过滤)
	 */
	public List<Object[]> findSimpleByParam3(String typeNo, String extends1, String showIds,
											 String orderBy, String notSelectIds, String codeLevelNo,
											 Integer onlyLevelNo) {
		StringBuffer buffer = new StringBuffer();
		Map<String, Object> paramMap = new HashMap<>();
		buffer.append(
				"SELECT T.RID,T.CODE_NAME,T.CODE_NO,T.CODE_LEVEL_NO,T.SPLSHT,T.EXTENDS1,T.EXTENDS2,T.EXTENDS3,T.EXTENDS4,T.EXTENDS5,T.CODE_PATH,T.CODE_DESC FROM TS_SIMPLE_CODE T LEFT JOIN TS_CODE_TYPE T1 ON T1.RID = T.CODE_TYPE_ID WHERE 1=1 ");
		buffer.append(" AND T1.CODE_TYPE_NAME = :CODE_TYPE_NAME");
		paramMap.put("CODE_TYPE_NAME", StringUtils.trim(typeNo));
		buffer.append(" AND T.IF_REVEAL = 1 ");
		if (StringUtils.isNotBlank(extends1)) {
			buffer.append("AND T.EXTENDS1 = :EXTENDS1");
			paramMap.put("EXTENDS1", StringUtils.trim(extends1));
		}
		if (StringUtils.isNotBlank(codeLevelNo)) {
			buffer.append(" AND T.CODE_LEVEL_NO LIKE :codeLevelNo ");
			paramMap.put("codeLevelNo", codeLevelNo);
		}
		if (StringUtils.isNotBlank(codeLevelNo)) {
			buffer.append(" AND REGEXP_COUNT(T.CODE_LEVEL_NO, '\\.') = :onlyLevelNo ");
			paramMap.put("onlyLevelNo", onlyLevelNo);
		}
		if ("1".equals(orderBy)) {
			buffer.append(" order by T.CODE_NAME ");
		} else {
			buffer.append(" order by t.num,t.CODE_LEVEL_NO,t.CODE_NO");
		}
		List<Object[]> sqlResultList = super.findDataBySqlNoPage(buffer.toString(), paramMap);
		if (CollectionUtils.isEmpty(sqlResultList)) {
			return sqlResultList;
		}
		List<String> includRids = new ArrayList<>();
		List<String> removeRids = new ArrayList<>();
		if (StringUtils.isNotBlank(showIds)) {
			includRids = Arrays.asList(showIds.split(","));
		}
		if (StringUtils.isNotBlank(notSelectIds)) {
			removeRids = Arrays.asList(notSelectIds.split(","));
		}
		List<Object[]> removeList = new ArrayList<>();
		for (Object[] objects : sqlResultList) {
			String curRid = null == objects[0] ? null : objects[0].toString();
			if (null == curRid || (!CollectionUtils.isEmpty(includRids) && !includRids.contains(curRid)) || (!CollectionUtils.isEmpty(removeRids) && removeRids.contains(curRid))) {
				removeList.add(objects);
			}
		}
		if (!CollectionUtils.isEmpty(removeList)) {
			sqlResultList.removeAll(removeList);
		}
		return sqlResultList;
	}

	/**
	* @description: 根据extends6查询某一种大类
	* <AUTHOR>
	*/
	public List<Object[]> findSimpleByParamTwo1(String typeNo, String extends6, String showIds) {
		StringBuffer buffer = new StringBuffer();
		Map<String, Object> paramMap  = new HashMap<>();
		buffer.append(
				"SELECT T.RID,T.CODE_NAME,T.CODE_NO,T.CODE_LEVEL_NO,T.SPLSHT,T.EXTENDS1,T.EXTENDS2,T.EXTENDS3,T.EXTENDS4,T.EXTENDS5,T.CODE_PATH,T.CODE_DESC FROM TS_SIMPLE_CODE T LEFT JOIN TS_CODE_TYPE T1 ON T1.RID = T.CODE_TYPE_ID WHERE 1=1 ");
		buffer.append(" AND T1.CODE_TYPE_NAME = :CODE_TYPE_NAME");
		paramMap.put("CODE_TYPE_NAME",  StringUtils.trim(typeNo) );
		buffer.append(" AND T.IF_REVEAL = 1 ");
		if (StringUtils.isNotBlank(extends6)) {
			buffer.append("AND T.EXTENDS6 = :extends6");
			paramMap.put("extends6",  StringUtils.trim(extends6) );
		}
		if(StringUtils.isNotBlank(showIds)) {
			buffer.append("AND T.RID IN (:RID)");
			List<String> list = Arrays.asList(showIds.split(","));
			paramMap.put("RID",  list );
		}
		buffer.append(" order by t.num,t.CODE_LEVEL_NO,t.CODE_NO");
		List<Object[]> sqlResultList = super.findDataBySqlNoPage(buffer.toString(),paramMap);
		return sqlResultList;
	}

	/**
	* 查询码表（根据CODE_LEVEL_NO LIKE查询，并排除）
	*/
	public List<Object[]> findSimpleByCodeLevelNo(String typeNo, String codeLevelNo, String excludeIds) {
		StringBuffer buffer = new StringBuffer();
		Map<String, Object> paramMap = new HashMap<>();
		buffer.append(
				"SELECT T.RID,T.CODE_NAME,T.CODE_NO,T.CODE_LEVEL_NO,T.SPLSHT,T.EXTENDS1,T.EXTENDS2,T.EXTENDS3,T.EXTENDS4,T.EXTENDS5,T.CODE_PATH,T.CODE_DESC FROM TS_SIMPLE_CODE T LEFT JOIN TS_CODE_TYPE T1 ON T1.RID = T.CODE_TYPE_ID WHERE 1=1 ");
		buffer.append(" AND T1.CODE_TYPE_NAME = :CODE_TYPE_NAME ");
		paramMap.put("CODE_TYPE_NAME", StringUtils.trim(typeNo));
		buffer.append(" AND T.IF_REVEAL = 1 ");
		if (StringUtils.isNotBlank(codeLevelNo)) {
			buffer.append(" AND T.CODE_LEVEL_NO LIKE :codeLevelNo ");
			paramMap.put("codeLevelNo", codeLevelNo);
		}
		if (StringUtils.isNotBlank(excludeIds)) {
			buffer.append(" AND T.RID NOT IN (:RID)");
			List<String> list = Arrays.asList(excludeIds.split(","));
			paramMap.put("RID", list);
		}
		buffer.append(" order by t.num,t.CODE_LEVEL_NO,t.CODE_NO ");
		List<Object[]> sqlResultList = super.findDataBySqlNoPage(buffer.toString(), paramMap);
		return sqlResultList;
	}
    
    /**
 	 * <p>方法描述：根据码表类型查询扩展字段1启用的码表</p>
 	 * @MethodAuthor qrr,2021年10月14日,findSimpleCodesByTypeIdAndExtends1
     * */
    @Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleCodesByTypeIdAndExtends1(String typeNo, String extends1) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (")
					.append(typeNo).append(") and t.ifReveal=1 ");
			if (StringUtils.isNotBlank(extends1)) {
				sb.append(" and t.extendS1 in (").append(extends1).append(")");
			}
			sb.append(" order by t.num, t.codeLevelNo,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}


	/**
	 * <p>方法描述：根据文书编码获取文书信息</p>
	 * @MethodAuthor： yzz
	 * @Date：2021-10-25
	 **/
	public TbZwWritsort getWritsort(String writCode){
		if (StringUtils.isNotBlank(writCode)) {
			String hql = "select t from TbZwWritsort t where t.writCode = "
					+ writCode;
			TbZwWritsort writsort = this.findOneByHql(hql,
					TbZwWritsort.class);
			return writsort;
		}
		return null;
	}
	/**
 	 * <p>方法描述：获取启用的节假日配置</p>
 	 * @MethodAuthor qrr,2021年11月24日,findTsSysHoliday
	 * */
	public List<TsSysHoliday> findTsSysHoliday(Integer type) {
		StringBuffer hql = new StringBuffer();
		hql.append(" SELECT T FROM TsSysHoliday T WHERE T.state =1");
		if (null!=type) {
			hql.append(" and T.holiType =").append(type);
		}
		return this.findHqlResultList(hql.toString());
	}
	/**
	 *  <p>方法描述：查询码表当前级的上一级</p>
	 *  code:当前对象
	 *  type:码表类型
	 * @MethodAuthor hsj 2022/4/15 11:26
	 */
	public TsSimpleCode findTsSimpleUpOneLevel(TsSimpleCode code, String type) {
		StringBuffer buffer = new StringBuffer();
		buffer.append(" select t from TsSimpleCode t where t.tsCodeType.codeTypeName =").append(type);
		if (StringUtils.isNotBlank(code.getCodeLevelNo()) && code.getCodeLevelNo().split("\\.").length > 1) {
			buffer.append(" and t.codeNo = '").append(code.getCodeLevelNo().split("\\.")[code.getCodeLevelNo().split("\\.").length - 2]).append("'");
		}else {
			buffer.append(" and t.codeNo = '").append(code.getCodeNo()).append("'");
		}
		return findOneByHql(buffer.toString(), TsSimpleCode.class);
	}

	/**
	* <p>Description：查询码表当前级的上一级 </p>
	* <p>code：码表实体对象 </p>
	* <p>type：码表类型 </p>
	* <p>Author： yzz 2025/3/27 </p>
	*/
	public TsSimpleCode findPreviousLevelTsSimple(TsSimpleCode code, String type){
		StringBuffer buffer = new StringBuffer();
		buffer.append(" select t from TsSimpleCode t where t.tsCodeType.codeTypeName =").append(type);
		if (StringUtils.isNotBlank(code.getCodeLevelNo()) && code.getCodeLevelNo().split("\\.").length > 1) {
			buffer.append(" and t.codeLevelNo = '").append(code.getCodeLevelNo().substring(0,code.getCodeLevelNo().lastIndexOf("."))).append("'");
		}else {
			buffer.append(" and t.codeLevelNo = '").append(code.getCodeLevelNo()).append("'");
		}
		return findOneByHql(buffer.toString(), TsSimpleCode.class);
	}

	/**
	 * 根据大类获取对照数据
	 *
	 * @return 对照数据
	 */
	@Transactional(readOnly = true)
	public List<TsContraSub> getContraSubListByMainContraCode(String mainContraCode) {
		List<TsContraSub> contraSubList =
				CollectionUtil.castList(TsContraSub.class, CacheUtils.get("contraSubListByMainContraCode", mainContraCode));
		if (ObjectUtil.isEmpty(contraSubList)) {
			StringBuilder hql = new StringBuilder();
			hql.append("select t from TsContraSub t where ");
			hql.append("t.fkByMainId.contraCode = '").append(mainContraCode).append("'");
			contraSubList = super.findByHql(hql.toString(), TsContraSub.class);
			CacheUtils.put("contraSubListByMainContraCode", mainContraCode, contraSubList);
		}
		return contraSubList;
	}
	/**
	 *  <p>方法描述：查询所有地区缓存</p>
	 * @MethodAuthor hsj 2023-03-22 9:57
	 */
	public Map<String, TsZone> findAllZoneList() {
		Map<String, TsZone> zoneMap = (Map<String, TsZone>)CacheUtils.get("zoneListAll", "ALL");
		if(null == zoneMap){
			zoneMap = new HashMap<>();
			List<TsZone> zoneList = this.findByHql(" select t from TsZone t  ", TsZone.class);;
			if(!CollectionUtils.isEmpty(zoneList)){
				for(TsZone tsZone :zoneList){
					zoneMap.put(tsZone.getZoneGb(),tsZone);
				}
			}
			CacheUtils.put("zoneListAll", "ALL",zoneMap);
		}
	return zoneMap;
	}

	/**
	* @description: 获取用户信息
	* <AUTHOR>
	*/
	public List<Object[]> findUserInfo(String searchName) {
		Map<String, Object> paramMap  = new HashMap<>();
		StringBuilder sql=new StringBuilder();
		sql.append(" select T.RID,T1.RID as UNITRID,T1.UNITNAME,T.USERNAME,T.MB_NUM ");
		sql.append(" from TS_USER_INFO T ");
		sql.append(" inner join TS_UNIT T1 on T.UNIT_RID=T1.RID ");
		sql.append(" where T.IF_REVEAL=1 and T.VALID_END_DATE>=sysdate ");
		if(StringUtils.isNotBlank(searchName)){
			sql.append("AND T.USERNAME like :USERNAME escape '\\\'");
			paramMap.put("USERNAME",  "%"+searchName+"%" );
		}
		sql.append(" order by T1.UNITNAME,T.USERNAME ");
		return this.findDataBySqlNoPage(sql.toString(),paramMap);
	}

	/**
	 * 根据码表类型获取码表（树形码表）（只查询启用）
	 *
	 * @param typeNo 码表类型
	 * @return 码表
	 */
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleCodeListOrderByNumLevelNo(String typeNo) {
		return findSimpleCodeListOrderByNumLevelNo(typeNo, false);
	}

	/**
	 * 根据码表类型获取码表（树形码表）
	 *
	 * @param typeNo       码表类型
	 * @param ifDeactivate 是否查询停用
	 * @return 码表
	 */
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleCodeListOrderByNumLevelNo(String typeNo, boolean ifDeactivate) {
		return findSimpleCodeList(typeNo, "t.num, t.codeLevelNo, t.codeNo", "", "", ifDeactivate);
	}

	/**
	 * 根据码表类型获取码表（树形码表）（只查询启用）
	 *
	 * @param typeNo 码表类型
	 * @return 码表
	 */
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleCodeListOrderByLevelNumNo(String typeNo) {
		return findSimpleCodeListOrderByLevelNumNo(typeNo, false);
	}

	/**
	 * 根据码表类型获取码表（树形码表）
	 *
	 * @param typeNo       码表类型
	 * @param ifDeactivate 是否查询停用
	 * @return 码表
	 */
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleCodeListOrderByLevelNumNo(String typeNo, boolean ifDeactivate) {
		return findSimpleCodeList(typeNo, "t.codeLevelNo, t.num, t.codeNo", "", "", ifDeactivate);
	}

	/**
	 * 根据码表类型获取码表（非树形码表）（只查询启用）
	 *
	 * @param typeNo 码表类型
	 * @return 码表
	 */
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleCodeListOrderByNumNo(String typeNo) {
		return findSimpleCodeListOrderByNumNo(typeNo, false);
	}

	/**
	 * 根据码表类型获取码表（非树形码表）
	 *
	 * @param typeNo       码表类型
	 * @param ifDeactivate 是否查询停用
	 * @return 码表
	 */
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleCodeListOrderByNumNo(String typeNo, boolean ifDeactivate) {
		return findSimpleCodeList(typeNo, "t.num, t.codeNo", "", "", ifDeactivate);
	}

	/**
	 * 根据码表类型获取码表（只查询启用）
	 *
	 * @param typeNo       码表类型
	 * @param extendS      拓展字段
	 * @param extendSValue 拓展字段值
	 * @return 码表
	 */
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleCodeListByExtendS(String typeNo, String extendS, Object extendSValue) {
		return findSimpleCodeList(typeNo, "t.num, t.codeNo", extendS, extendSValue, false);
	}

	/**
	 * 根据码表类型获取码表
	 *
	 * @param typeNo           码表类型
	 * @param orderBy          排序
	 * @param extendS          拓展字段
	 * @param extendSValue     拓展字段值
	 * @param ifNeedDeactivate 是否查询停用
	 * @return 码表
	 */
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleCodeList(String typeNo, String orderBy,
												 String extendS, Object extendSValue,
												 boolean ifNeedDeactivate) {
		if (StringUtils.isBlank(typeNo)) {
			return new ArrayList<>();
		}
		Map<String, Object> paramMap = new HashMap<>();
		String hql = "select t from TsSimpleCode t where t.tsCodeType.codeTypeName = :typeNo ";
		paramMap.put("typeNo", typeNo);
		if (!ifNeedDeactivate) {
			hql += " and t.ifReveal = 1 ";
		}
		if (ObjectUtil.isNotEmpty(extendS) && ObjectUtil.isNotEmpty(extendSValue)) {
			hql += " and extendS" + extendS + " = :extendSValue ";
			paramMap.put("extendSValue", extendSValue);
		}
		hql += " order by " + orderBy;
		return CollectionUtil.castList(TsSimpleCode.class, super.findDataByHqlNoPage(hql, paramMap));
	}

	/**
	* <p>Description：查询单位信息 </p>
	* <p>Author： yzz 2023-10-27 </p>
	*/
	public TsUnit findTsUnitByRid(Integer rid){
		TsUnit tsUnit = this.find(TsUnit.class, rid);
		if(tsUnit!=null && tsUnit.getRid()!=null){
			tsUnit.getTsUnitAttrs().size();
		}
		return tsUnit;
	}


	public List<Object[]> findSimpleByParamTwo2(String typeNo) {
		StringBuffer buffer = new StringBuffer();
		Map<String, Object> paramMap  = new HashMap<>();
		buffer.append(
				"SELECT T.RID,T.CODE_NAME,T.CODE_NO,T.CODE_LEVEL_NO,T.SPLSHT,T.EXTENDS1,T.EXTENDS2,T.EXTENDS3,T.EXTENDS4,T.EXTENDS5,T.CODE_PATH,T.CODE_DESC FROM TS_SIMPLE_CODE T LEFT JOIN TS_CODE_TYPE T1 ON T1.RID = T.CODE_TYPE_ID WHERE 1=1 ");
		buffer.append(" AND T1.CODE_TYPE_NAME = :CODE_TYPE_NAME");
		paramMap.put("CODE_TYPE_NAME",  StringUtils.trim(typeNo) );
		buffer.append(" AND T.IF_REVEAL = 1 ");
		buffer.append(" AND not exists( select 1 from TB_ZW_BUSI_REL_ITEM TT where TT.ITEM_ID=T.RID ) ");
		buffer.append(" order by t.num,t.CODE_LEVEL_NO,t.CODE_NO");
		List<Object[]> sqlResultList = super.findDataBySqlNoPage(buffer.toString(),paramMap);
		return sqlResultList;
	}
}
