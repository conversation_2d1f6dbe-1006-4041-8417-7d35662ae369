package com.chis.modules.system.service;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.common.utils.DateUtils;
import com.chis.modules.system.entity.TsUserLoginRcd;

/**
 * <p>类描述：短信日期Service</p>
 * @ClassAuthor qrr,2021年12月16日,TsUserLoginRcdService
 * */
@Service
@Transactional(readOnly = false,rollbackFor=Throwable.class)
public class TsUserLoginRcdService extends AbstractTemplate{
	/**
 	 * <p>方法描述：查询当天发送记录数</p>
 	 * @MethodAuthor qrr,2021年12月16日,findTsUserLoginRcd
	 * */
	@Transactional(readOnly = true)
	public List<TsUserLoginRcd> findTsUserLoginRcd(String mobileNo) {
		StringBuffer hql = new StringBuffer();
		hql.append("SELECT T FROM TsUserLoginRcd T WHERE 1=1 ");
		hql.append(" AND T.mobileNo = '").append(mobileNo).append("'");
		hql.append(" AND TO_CHAR(T.createDate,'yyyy-MM-dd') = '").append(DateUtils.getDate()).append("'");
		return this.findHqlResultList(hql.toString());
	}
	/**
 	 * <p>方法描述：保存短信日志</p>
 	 * @MethodAuthor qrr,2021年12月17日,saveTsUserLoginRcd
	 * */
	public void saveTsUserLoginRcd(TsUserLoginRcd entity){
        if (null==entity.getRid()) {
        	entity.setCreateDate(new Date());
        	entity.setCreateManid(1);
            saveObj(entity);
        } else {
        	entity.setModifyDate(new Date());
        	entity.setModifyManid(1);
            updateObj(entity);
        }
	}
	/**
 	 * <p>方法描述：查询发送成功且有效的短信日志，按照创建日期倒序</p>
 	 * @MethodAuthor qrr,2021年12月17日,findNewestTsUserLoginRcd
	 * */
	public List<Object[]> findNewestTsUserLoginRcd(String mobileNo) {
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT T.RID,T.CHECK_CODE FROM TS_USER_LOGIN_RCD T WHERE 1=1 ");
		sql.append(" AND T.MOBILE_NO = '").append(mobileNo).append("'");
		sql.append(" AND T.SEND_STATE = 1");
		sql.append(" AND T.VALID_DATE >=TO_DATE('").append(DateUtils.getDateTime()).append("','YYYY-MM-DD HH24:MI:SS')");
		sql.append(" ORDER BY T.CREATE_DATE DESC");
		return this.findSqlResultList(sql.toString());
	}

	/**
	* @description: 获取最新一条短信发送信息
	* <AUTHOR>
	*/
	public List<Object[]> findLastUserLoginRcd(String mobileNo) {
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT T.RID,to_char(T.CREATE_DATE+1/24/60,'yyyy-mm-dd HH24:MI:SS') as CUR_CREATE_DATE ");
		sql.append(" FROM TS_USER_LOGIN_RCD T WHERE 1=1 ");
		sql.append(" AND T.MOBILE_NO = '").append(mobileNo).append("'");
		sql.append(" AND T.SEND_STATE = 1");
		sql.append(" ORDER BY T.CREATE_DATE DESC ");
		return this.findSqlResultList(sql.toString());
	}
}
