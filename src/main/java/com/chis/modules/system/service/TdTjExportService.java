package com.chis.modules.system.service;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.io.File;
import java.sql.Clob;
import java.sql.SQLException;

@Service
@Transactional(readOnly = true)
public class TdTjExportService extends AbstractTemplate {
    /**
     * @Description: 更新导出下载状态为3
     *
     * @MethodAuthor gjy,2021年12月20日
     */
    @Transactional(readOnly = false)
    public void updateState(Integer rid, int stateMark) {
        StringBuilder sb = new StringBuilder();
        sb.append(" UPDATE TD_TJ_EXPORT T SET T.STATE = '").append(stateMark).append("'");
        sb.append(" WHERE T.RID = '").append(rid).append("'");
        em.createNativeQuery(sb.toString()).executeUpdate();
    }
    /**
     * @Description: 根据rid查询导出条件
     *
     * @MethodAuthor gjy,2021年12月23日
     */
    public String findConditionByRid(Integer rid){
        StringBuilder sb = new StringBuilder();
        String contion="";
        sb.append(" select EXPORT_CONDITION_SHOW from  TD_TJ_EXPORT WHERE RID='").append(rid).append("'");
        Clob clob = (Clob) em.createNativeQuery(sb.toString()).getResultList()
                .get(0);
        if (clob != null) {
            try {
                contion = clob.getSubString((long) 1, (int) clob.length());
            } catch (SQLException e) {
                e.printStackTrace();
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        }
        return contion;
    }

    /**
     * 根据RID删除导出任务并删除文件
     *
     * @param rid      RID
     * @param filePath 文件路径
     */
    public void deleteTaskAndFileByRid(Integer rid, String filePath) {
        String sql = "DELETE FROM TD_TJ_EXPORT WHERE RID = '" + rid + "'";
        em.createNativeQuery(sql).executeUpdate();
        if (StringUtils.isBlank(filePath)){
            return;
        }
        String xnPath = JsfUtil.getAbsolutePath();
        String path;
        if (filePath.contains(xnPath)) {
            path = filePath;
        } else {
            path = xnPath + filePath;
        }
        File file = new File(path);
        if (!file.exists()) {
            return;
        }
        boolean ignore = file.delete();
    }
}
