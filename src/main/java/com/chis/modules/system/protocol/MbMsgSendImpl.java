package com.chis.modules.system.protocol;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.zip.GZIPOutputStream;

import com.chis.common.utils.PropertyUtils;
import org.springframework.stereotype.Component;

import sun.misc.BASE64Encoder;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.MD5Util;
import com.chis.common.utils.StringUtils;
import com.chis.sms.service.SmsServerProxy;

/**
 * 联通语音发送实现
 * 
 * <AUTHOR> 2014-11-25
 */
@Component(value="com.chis.web.protocol.impl.MbMsgSendImpl")
public class MbMsgSendImpl implements IMsgSend {

	/** 
	 * 疫苗通短信接口地址 
	 * http://**************:7002/SmsService/SmsService?wsdl
	 */

	/*public static void main(String[] args) {
		IMsgSend send = new MbMsgSendImpl();
		System.err.println(send.sendMsg("18261562532", "hello"));
	}*/

	@Override
	public String sendMsg(String phoneNum, String content) {
		try {
			SmsServerProxy proxy = new SmsServerProxy(PropertyUtils.getValue("send_url"));
			
			StringBuilder sb = new StringBuilder();
			if(StringUtils.isNotBlank(phoneNum) && StringUtils.isNotBlank(content)) {
				sb.append("<?xml version='1.0' encoding='GB2312'?>");
				sb.append("<SmsInfoes>");
				for(String phone : phoneNum.split(",")) {
					sb.append("<SmsInfo>");
					sb.append("<UserCode>").append(PropertyUtils.getValue("send_usename")).append("</UserCode>");
					sb.append("<TelNum>").append(phone).append("</TelNum>");
					sb.append("<Content>").append(content).append("</Content>");
					sb.append("<Count>1</Count>");
					sb.append("<InvalidDate>").append(DateUtils.formatDate(DateUtils.addDays(new Date(), 2))).append("</InvalidDate>");
					sb.append("<SpTag>0</SpTag>");
//					sb.append("<Sign>【无锡市疾控】</Sign>");
					sb.append("<SystemMark>5</SystemMark>");
					sb.append("<IsNight>1</IsNight>");
					sb.append("<NetTag>1</NetTag>");
					sb.append("</SmsInfo>");
				}
				sb.append("</SmsInfoes>");
				
				int rst = proxy.smsUpLoad(compress(sb.toString()), PropertyUtils.getValue("send_usename"), new MD5Util().getMD5ofStr(PropertyUtils.getValue("send_psw")).toLowerCase());
				if(rst == 1) {
					return "1";
				}
				return "发送失败" + rst;
			}
			return "发送失败";
		} catch (Exception e) {
			e.printStackTrace();
			return "发送失败";
		}
	}
	
	/**
	 * 压缩短信内容
	 * @param 短信xml
	 * @return 压缩的短信xml
	 */
	private static String compress(String s) {
		try {
			ByteArrayInputStream input = new ByteArrayInputStream(s.getBytes("GBK"));
			ByteArrayOutputStream output = new ByteArrayOutputStream(1024);
			GZIPOutputStream gzout = new GZIPOutputStream(output);
			
			byte[] buf = new byte[1024];
			int number;
			
			while ((number = input.read(buf)) != -1) {
				gzout.write(buf, 0, number);
			}
			
			gzout.close();
			input.close();
			
			String result = new BASE64Encoder().encode(output.toByteArray());
			
			output.close();
			
			return result;
		} catch (IOException e) {
			
		}
		return null;
	}

	@Override
	public String description() {
		return "中卫信疫苗通短信发送功能";
	}

}
