package com.chis.modules.system.protocol;

import java.util.Map;

import com.chis.common.utils.JPushUtil;

/**
 * 极光推送消息
 * <AUTHOR>
 */
public class PushMsgToAppImpl {

	private String appKey = "8ccced08526ac2e739b63d3a";
	
	private String masterSecret = "0695bcb88f1adbac9263a897";
	/** 标题 */
	private String title;
	
	private Map<String, String> paramMap;
	/** 设备别名 */
	private String anotherName;
	
	public static void main(String[] args) {
		PushMsgToAppImpl p = new PushMsgToAppImpl("标题sss", null , "zq");
		p.sendJPushAll();
		
		
		
	}
	
	
	
	
	public PushMsgToAppImpl(){}
	
	public PushMsgToAppImpl(String title, Map<String, String> paramMap) {
		this.title = title;
		this.paramMap = paramMap;
	}
	
	public PushMsgToAppImpl(String title, Map<String, String> paramMap, String anotherName){
		this.title = title;
		this.paramMap = paramMap;
		this.anotherName = anotherName;
	}
	
	public PushMsgToAppImpl(String appKey, String masterSecret, String title, Map<String, String> paramMap, String anotherName){
		this.appKey = appKey;
		this.masterSecret = masterSecret;
		this.title = title;
		this.paramMap = paramMap;
		this.anotherName = anotherName;
	}
	
	/** 
	 * 广播推送消息给所有用户
	 */
	public void sendJPushAll() {
		JPushUtil.sendJPushAll(appKey, masterSecret, title, paramMap,false);
	}
	
	/**
	 * 推送消息给指定用户
	 */
	public void sendJPush() {
		JPushUtil.sendJPush(appKey, masterSecret, anotherName, title, paramMap , false);
		System.err.println("App-Push-UserNo："+anotherName);
	}
}
