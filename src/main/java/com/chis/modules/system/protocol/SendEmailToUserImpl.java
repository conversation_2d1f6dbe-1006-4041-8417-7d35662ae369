package com.chis.modules.system.protocol;

import org.springframework.stereotype.Component;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JSendEmailUtil;
import com.chis.common.utils.PropertyUtils;
import com.sun.star.bridge.oleautomation.Date;

@Component(value="com.chis.web.protocol.impl.SendEmailToUserImpl")
public class SendEmailToUserImpl implements IMsgSend{
	private static final String host = PropertyUtils.getValue("host");
	private static final String port = PropertyUtils.getValue("port");
	private static final String username = PropertyUtils.getValue("username");
	private static final String password = PropertyUtils.getValue("password");
	private static final String url = PropertyUtils.getValue("url");

	@Override
	public String sendMsg(String toEmail, String content) {
		try {
			String[] cont = content.split("@#@");
			boolean falg =  JSendEmailUtil.sendEmail(toEmail,cont!=null && cont[1]!=null?cont[1]:null,cont!=null && cont[0]!=null?cont[0]:null, host, username, password, url);
			return falg?"发送成功！":"发送失败！";
		} catch (Exception e) {
			return "发送失败！";
		}
	}
	@Override
	public String description() {
		return "邮件发送模块";
	}
}
