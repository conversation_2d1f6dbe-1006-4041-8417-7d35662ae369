package com.chis.modules.system.javabean;

import java.io.Serializable;

/**
 * 文件
 * 
 * <AUTHOR>
 * 
 */
public class FilePojoNew implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -8732166862466137509L;

	private String filePath;

	private String fileName;

	private String showName;

	private String fileType;
	/**
	 * 1：PDF 2：图片类型 3：视频 4：录音
	 */
	private String annexType;

	private String videoPath;
	private String videoCover;
	
	private String writNo;
	/**
	 * 上一个序号 为空则为第一个
	 */
	private Integer preIndex;
	/**
	 * 下一个序号 为空则为最后
	 */
	private Integer nextIndex;


	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFileType() {
		return fileType;
	}

	public void setFileType(String fileType) {
		this.fileType = fileType;
	}

	public String getAnnexType() {
		return annexType;
	}

	public void setAnnexType(String annexType) {
		this.annexType = annexType;
	}

	public String getVideoPath() {
		return videoPath;
	}

	public void setVideoPath(String videoPath) {
		this.videoPath = videoPath;
	}

	public String getVideoCover() {
		return videoCover;
	}

	public void setVideoCover(String videoCover) {
		this.videoCover = videoCover;
	}

	public String getShowName() {
		return showName;
	}

	public void setShowName(String showName) {
		this.showName = showName;
	}

	public String getWritNo() {
		return writNo;
	}

	public void setWritNo(String writNo) {
		this.writNo = writNo;
	}

	public Integer getPreIndex() {
		return preIndex;
	}

	public void setPreIndex(Integer preIndex) {
		this.preIndex = preIndex;
	}

	public Integer getNextIndex() {
		return nextIndex;
	}

	public void setNextIndex(Integer nextIndex) {
		this.nextIndex = nextIndex;
	}

}
