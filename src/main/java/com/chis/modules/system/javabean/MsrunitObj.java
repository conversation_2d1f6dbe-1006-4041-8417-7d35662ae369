package com.chis.modules.system.javabean;

/***
 * <p>类描述: 计量单位 </p>
 *
 * @ClassAuthor mxp,2018/12/20,MsrunitObj
 */
public class MsrunitObj {

    private String zf;//字符
    private String sb;//上标
    private String xb;//下标
    private String tszf;//特殊字符
    private StringBuffer show = new StringBuffer();

    public String getZf() {
        return zf;
    }

    public void setZf(String zf) {
        this.zf = zf;
    }

    public String getSb() {
        return sb;
    }

    public void setSb(String sb) {
        this.sb = sb;
    }

    public String getXb() {
        return xb;
    }

    public void setXb(String xb) {
        this.xb = xb;
    }

    public String getTszf() {
        return tszf;
    }

    public void setTszf(String tszf) {
        this.tszf = tszf;
    }

    public StringBuffer getShow() {
        return show;
    }

    public void setShow(StringBuffer show) {
        this.show = show;
    }
}
