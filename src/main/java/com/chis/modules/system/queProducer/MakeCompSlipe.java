package com.chis.modules.system.queProducer;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsProbSubject;
import com.chis.modules.system.enumn.GenHtmlType;
import org.springframework.util.CollectionUtils;

import java.util.Map;

/**
 * 模块描述：创建滑动组件
 * 
 * <AUTHOR>
 * @createDate 2016年3月26日
 */
public class MakeCompSlipe extends MakeComponet {

	@Override
	public String makeCompHtml(TsProbSubject tsProbSubject, GenHtmlType genHtmlType) {
		String htmlStr = null;
		switch (genHtmlType) {
		case WEB_HTML_PAGE:
			htmlStr = this.produceSlipe(tsProbSubject);
			break;
		case MOBILE_HTML_PAGE:
			htmlStr = this.produceSlipeMobile(tsProbSubject);
			break;
		default:
			break;
		}
		return htmlStr;
	}

	/**
	 * 生成滑动题（手机）
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	private String produceSlipeMobile(TsProbSubject tsProbSubject) {
		StringBuilder htmlStr = new StringBuilder();
		if (null != tsProbSubject) {
			Integer queNum = tsProbSubject.getNum();
			htmlStr.append("<div class=\"field ui-field-contain\" id=\"div").append(queNum)
					.append("\" type=\"9\" topic=\"").append(queNum).append("\"  data-role=\"fieldcontain\" ");
			if (null != tsProbSubject.getMustAsk() && tsProbSubject.getMustAsk() == 1) {// 如果必答，则需要加上红星
				htmlStr.append(" req=\"1\" ");
			} else {
				htmlStr.append(" req=\"0\" ");
			}// 加入需要跳转的代码
			String jumpOrRela = this.produceJumpOrRelaMobile(tsProbSubject);
			if (StringUtils.isNotBlank(jumpOrRela)) {
				htmlStr.append(jumpOrRela);
			}
			htmlStr.append(">");
			htmlStr.append("<div class=\"field-label\">");
			htmlStr.append(tsProbSubject.getShowCode() == null ? "" : tsProbSubject.getShowCode()).append(" ");
			htmlStr.append(tsProbSubject.getTitleDesc().replaceAll(" ", "&nbsp;"));
			if (null != tsProbSubject.getMustAsk() && tsProbSubject.getMustAsk() == 1) {// 如果必答，则需要加上红星
				htmlStr.append("<span style=\"color:red;\">&#160;*</span>");
			}
			htmlStr.append("</div>");
			htmlStr.append("<table cellspacing=\"0\" class=\"matrix-rating\">");
			htmlStr.append("<tbody>");
			// 滑动最大值
			Integer slideMaxval = tsProbSubject.getSlideMaxval();
			// 滑动最小值
			Integer slideMinval = tsProbSubject.getSlideMinval();
			// 滑动最大值
			String slideMaxvalDesc = tsProbSubject.getSlideMaxvalDesc();
			// 滑动最小值
			String slideMinvalDesc = tsProbSubject.getSlideMinvalDesc();
			htmlStr.append("<tr><td class=\"title\" style=\"text-align:left;\">").append(slideMinvalDesc)
					.append("</td>");
			htmlStr.append("<td class=\"title\" style=\"text-align:right;\">").append(slideMaxvalDesc)
					.append("</td></tr>");
			htmlStr.append("<tr><td colspan=\"2\">");
			htmlStr.append("<input type=\"text\" class=\"ui-slider-input\" id=\"q").append(queNum).append("\" ")
					.append("name=\"q").append(queNum).append("\" ").append(" min=\"").append(slideMinval)
					.append("\" ").append(" max=\"").append(slideMaxval).append("\" ")
					.append(" style=\"position: absolute; width: 40px; height: 20px; overflow: hidden;\">");
			htmlStr.append("</div></td></tr></tbody>");
			htmlStr.append("</table>");
			htmlStr.append("<div class=\"errorMessage\"></div>");
			String otherDesc = tsProbSubject.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				htmlStr.append("<div class=\"qinsert\">提示：").append(otherDesc).append("</div>");
			}
			htmlStr.append("</div>");
		}
		return htmlStr.toString();
	}

	/**
	 * 生成滑动题
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	private String produceSlipe(TsProbSubject tsProbSubject) {
		StringBuilder selectOneStr = new StringBuilder();
		if (null != tsProbSubject) {
			Map<String,String> pageEventMap = this.findEventByScript(null == tsProbSubject.getPool() ? null : tsProbSubject.getPool().getExecScript());
			Integer queNum = tsProbSubject.getNum();
			selectOneStr.append("<div class=\"div_question\" id=\"div").append(queNum).append("\" ");
			// 加入需要跳转的代码
			String jumpOrRela = this.produceJumpOrRela(tsProbSubject);
			if (StringUtils.isNotBlank(jumpOrRela)) {
				selectOneStr.append(jumpOrRela);
			}
			selectOneStr.append(">");
			selectOneStr.append("<div class=\"div_title_question_all\">");

			selectOneStr.append("<div class=\"div_topic_question\">");
			selectOneStr.append("<b>").append(tsProbSubject.getShowCode() == null ? "" : tsProbSubject.getShowCode())
					.append(" </b>");
			selectOneStr.append("</div>");
			selectOneStr.append("<div id=\"divTitle").append(queNum).append("\" class=\"div_title_question\">");
			selectOneStr.append(tsProbSubject.getTitleDesc());
			if (null != tsProbSubject.getMustAsk() && tsProbSubject.getMustAsk() == 1) {// 如果必答，则需要加上红星
				selectOneStr.append("<span style=\"color:red;\">&#160;*</span>");
			}
			selectOneStr.append("</div>");
			selectOneStr.append("<div style=\"clear:both;\"></div>");
			selectOneStr.append("</div>");

			selectOneStr.append("<div class=\"div_table_radio_question\"  style=\"width:60%;\" id=\"divquestion")
					.append(queNum).append("\">");
			selectOneStr.append("<div class=\"div_table_clear_top\"></div>");
			selectOneStr.append("<br style=\"clear:both;\"><div style=\"clear:both;\">");

			// 滑动最大值
			Integer slideMaxval = tsProbSubject.getSlideMaxval();
			// 滑动最小值
			Integer slideMinval = tsProbSubject.getSlideMinval();
			// 滑动最大值
			String slideMaxvalDesc = tsProbSubject.getSlideMaxvalDesc();
			// 滑动最小值
			String slideMinvalDesc = tsProbSubject.getSlideMinvalDesc();
			boolean ifHasDesc = false;
			if (StringUtils.isNotBlank(slideMaxvalDesc) && StringUtils.isNotBlank(slideMinvalDesc)) {
				ifHasDesc = true;
			}

			if (null != slideMaxval && null != slideMinval) {
				selectOneStr.append("<span class=\"spanLeft\" style=\"color:red;\">")
						.append(ifHasDesc ? slideMinvalDesc : "").append(ifHasDesc ? "(" : "");
				selectOneStr.append(slideMinval).append(ifHasDesc ? ")" : "").append("</span>");

				selectOneStr.append("<span class=\"spanRight\" style=\"color:red;\">")
						.append(ifHasDesc ? slideMaxvalDesc : "").append(ifHasDesc ? "(" : "");
				selectOneStr.append(slideMaxval).append(ifHasDesc ? ")" : "").append("</span>");

			}
			selectOneStr.append("<div style=\"clear:both;\"></div></div>");

			selectOneStr.append("<div rel=\"slider").append(queNum).append("\" defvalue=\"\" minvalue=\"")
					.append(slideMinval);
			selectOneStr.append("\" maxvalue=\"").append(slideMaxval)
					.append("\" style=\"clear:both;\" class=\"slider\" id=\"divSlider").append(queNum)
					.append("\"></div>");

			selectOneStr.append("<span style=\"color:red;float:left;\"></span>");
			selectOneStr
					.append("<br style=\"clear:both;\"><div style=\"clear:both;\"></div><div style=\"clear:both;height:10px;\">");
			selectOneStr.append("</div><div><div style=\"height:16px;line-height:16px;display:none;\" id=\"slider")
					.append(queNum).append("\">");
			selectOneStr.append("</div></div><div class=\"div_table_clear_bottom\"></div></div>");
			selectOneStr.append("<input type=\"hidden\" name=\"q").append(queNum).append("\" id=\"q").append(queNum)
					.append("\" ");
			//加入页面脚本事件
			if(!CollectionUtils.isEmpty(pageEventMap)){
				for(Map.Entry<String,String> mapEntity : pageEventMap.entrySet()){
					selectOneStr.append(" ").append(mapEntity.getKey()).append("=\"").append(mapEntity.getValue()).append("\" ");
				}
			}
			if (null != tsProbSubject.getMustAsk() && tsProbSubject.getMustAsk() == 1) {// 如果必答，则需要加上验证
				selectOneStr.append(" zwx:valid=\"requireSlider\" zwx:errmsg=\"此题为必答题，请填写答案!\" ");
			}
			selectOneStr.append("/>");

			selectOneStr.append("<div class=\"errorMessage\">");
			selectOneStr.append("</div>");
			String otherDesc = tsProbSubject.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				selectOneStr.append("<div class=\"div_ins_question\">提示：").append(otherDesc).append("</div>");
			}
			selectOneStr.append("</div>");
		}
		return selectOneStr.toString();
	}

}
