package com.chis.modules.system.queProducer;

import java.util.List;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsProPoolOpt;
import com.chis.modules.system.entity.TsProbExampool;
import com.chis.modules.system.enumn.GenHtmlType;

/**
 * 模拟试题多选生成组件
 * 
 * <AUTHOR>
 * @createDate 2016年8月8日
 */
public class MakeSECompSelectM extends MakeSimulateExamComponet {

	@Override
	public String makeCompHtml(TsProbExampool sub, GenHtmlType genHtmlType) {
		String htmlStr = null;
		switch (genHtmlType) {
		case WEB_HTML_PAGE:
			htmlStr = this.produceSelectMany(sub);
			break;
		case MOBILE_HTML_PAGE:
			htmlStr = this.produceSelectManyMobile(sub);
			break;
		default:
			break;
		}
		return htmlStr;
	}

	/**
	 * 生成多选组件(微信)
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	private String produceSelectManyMobile(TsProbExampool sub) {
		StringBuilder selectOneStr = new StringBuilder();

		return selectOneStr.toString();
	}

	/**
	 * 生成多选组件
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	private String produceSelectMany(TsProbExampool sub) {
		StringBuilder selectOneStr = new StringBuilder();
		if (null != sub && null != sub.getTsProPoolOpts()
				&& sub.getTsProPoolOpts().size() > 0) {
			String queNum = sub.getQesCode();
			selectOneStr.append("<div class=\"div_question\" id=\"div")
					.append(queNum).append("\" ");
			String mutex = this.produceMutex(sub);
			if (StringUtils.isNotBlank(mutex)) {
				selectOneStr.append(mutex);
			}
			selectOneStr.append(">");
			selectOneStr.append("<div class=\"div_title_question_all\">");

			selectOneStr.append("<div class=\"div_topic_question\">");
			selectOneStr.append("<b>").append(sub.getShowCode() == null ? ""
					: sub.getShowCode())
					.append(" </b>");
			selectOneStr.append("</div>");
			selectOneStr.append("<div id=\"divTitle").append(queNum)
					.append("\" class=\"div_title_question\">");
			selectOneStr.append(sub.getTitleDesc());
			if (null != sub.getMustAsk() && sub.getMustAsk() == 1) {// 如果必答，则需要加上红星
				selectOneStr
						.append("<span style=\"color:red;\">&#160;*</span>");
			}
			selectOneStr.append("<span class=\"qtypetip\">&#160;[多选题]</span>");
			selectOneStr.append("</div>");
			selectOneStr.append("<div style=\"clear:both;\"></div>");
			selectOneStr.append("</div>");

			selectOneStr
					.append("<div class=\"div_table_radio_question\" id=\"divquestion")
					.append(queNum).append("\">");
			selectOneStr.append("<div class=\"div_table_clear_top\"></div>");
			selectOneStr.append("<ul class=\"ulradiocheck\">");
			// 单选选项，验证时需要加在第一个标签上
			List<TsProPoolOpt> opts = sub.getTsProPoolOpts();
			for (int i = 0; i < opts.size(); i++) {
				TsProPoolOpt opt = opts.get(i);
				if (opt.getState().intValue() == 0) {
					continue;
				}
				selectOneStr.append("<li style=\"width: 99%;\">");
				// 选项序号
				Integer optNum = opt.getNum();
				selectOneStr.append("<input type=\"checkbox\" name=\"q")
						.append(queNum).append("\" id=\"q").append(queNum)
						.append("_").append(optNum).append("\" ");
				selectOneStr.append("value=\"").append(opt.getOptionValue())
						.append("\"  ");
				if (null != sub.getMustAsk() && sub.getMustAsk() == 1 && i == 0) {// 如果必答，则需要加上验证
					selectOneStr
							.append(" zwx:valid=\"requireChecked\" zwx:errmsg=\"此题为必答题，请填写答案!\" ");
				}
				selectOneStr.append(" /><label for=\"q").append(queNum)
						.append("_").append(optNum).append("\" >");
				selectOneStr.append(opt.getOptionDesc().replaceAll(" ",
						"&nbsp;"));
				selectOneStr.append("</label>");
				// 其他描述
				String otherDesc = opt.getOtherDesc();
				if (StringUtils.isNotBlank(otherDesc)) {
					selectOneStr.append("<div class=\"div_item_desc\" rel=\"q")
							.append(queNum).append("_").append(optNum)
							.append("\" >").append(otherDesc).append("</div>");
				}
				// 是否需要填空

				selectOneStr.append("</li>");
			}
			selectOneStr.append("<div style=\"clear:both;\"></div>");
			selectOneStr.append("</ul>");
			selectOneStr.append("<div style=\"clear:both;\"></div>");
			selectOneStr.append("<div class=\"div_table_clear_bottom\"></div>");
			selectOneStr.append("</div>");
			selectOneStr.append("<div class=\"errorMessage\"></div>");
			String otherDesc = sub.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				selectOneStr.append("<div class=\"div_ins_question\">提示：")
						.append(otherDesc).append("</div>");
			}
			selectOneStr.append("</div>");
		}
		return selectOneStr.toString();
	}
}
