package com.chis.modules.system.queProducer;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsProOpt;
import com.chis.modules.system.entity.TsProbSubject;
import com.chis.modules.system.enumn.GenHtmlType;
import org.springframework.util.CollectionUtils;

/**
 * 模块描述：创建单选组件
 * 
 * <AUTHOR>
 * @createDate 2016年3月26日
 */
public class MakeCompSelectO extends MakeComponet {

	@Override
	public String makeCompHtml(TsProbSubject tsProbSubject, GenHtmlType genHtmlType) {
		String htmlStr = null;
		switch (genHtmlType) {
		case WEB_HTML_PAGE:
			htmlStr = this.produceSelectOne(tsProbSubject);
			break;
		case MOBILE_HTML_PAGE:
			htmlStr = this.produceSelectOneMobile(tsProbSubject);
			break;
		default:
			break;
		}
		return htmlStr;
	}

	/**
	 * 生成单选组件
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	private String produceSelectOne(TsProbSubject tsProbSubject) {
		StringBuilder selectOneStr = new StringBuilder();
		if (null != tsProbSubject && null != tsProbSubject.getProOptList() && tsProbSubject.getProOptList().size() > 0) {
			Map<String,String> pageEventMap = this.findEventByScript(null == tsProbSubject.getPool() ? null : tsProbSubject.getPool().getExecScript());
			Integer queNum = tsProbSubject.getNum();
			selectOneStr.append("<div class=\"div_question\" id=\"div").append(queNum).append("\" ");
			// 加入需要跳转的代码
			String jumpOrRela = this.produceJumpOrRela(tsProbSubject);
			if (StringUtils.isNotBlank(jumpOrRela)) {
				selectOneStr.append(jumpOrRela);
			}
			selectOneStr.append(">");
			selectOneStr.append("<div class=\"div_title_question_all\">");

			selectOneStr.append("<div class=\"div_topic_question\">");
			selectOneStr.append("<b>").append(tsProbSubject.getShowCode() == null ? "" : tsProbSubject.getShowCode())
					.append(" </b>");
			selectOneStr.append("</div>");
			selectOneStr.append("<div id=\"divTitle").append(queNum).append("\" class=\"div_title_question\">");
			selectOneStr.append(tsProbSubject.getTitleDesc());
			if (null != tsProbSubject.getMustAsk() && tsProbSubject.getMustAsk() == 1) {// 如果必答，则需要加上红星
				selectOneStr.append("<span style=\"color:red;\">&#160;*</span>");
			}
			selectOneStr.append("</div>");
			selectOneStr.append("<div style=\"clear:both;\"></div>");
			selectOneStr.append("</div>");

			selectOneStr.append("<div class=\"div_table_radio_question\" id=\"divquestion").append(queNum)
					.append("\">");
			selectOneStr.append("<div class=\"div_table_clear_top\"></div>");
			selectOneStr.append("<ul class=\"ulradiocheck\">");
			// 单选选项，验证时需要加在第一个标签上
			List<TsProOpt> tbQuestOpts = tsProbSubject.getProOptList();
			for (int i = 0; i < tbQuestOpts.size(); i++) {
				TsProOpt tbQuestOpt = tbQuestOpts.get(i);
				if (tbQuestOpt.getState().intValue() == 0) {
					continue;
				}
				// 是否需要填空
				Integer needFill = tbQuestOpt.getNeedFill();
				selectOneStr.append("<li style=\"width: 99%;\">");
				// 选项序号
				Integer optNum = tbQuestOpt.getNum();
				selectOneStr.append("<input type=\"radio\"  name=\"q").append(queNum).append("\" id=\"q")
						.append(queNum).append("_").append(optNum).append("\" ");
				selectOneStr.append("value=\"").append(tbQuestOpt.getOptionValue()).append("\"  ");
				if (null != tsProbSubject.getMustAsk() && tsProbSubject.getMustAsk() == 1 && i == 0) {// 如果必答，则需要加上验证
					selectOneStr.append(" zwx:valid=\"requireChecked\" zwx:errmsg=\"此题为必答题，请填写答案!\" ");
				}
				//加入页面脚本事件
				if(!CollectionUtils.isEmpty(pageEventMap)){
					for(Map.Entry<String,String> mapEntity : pageEventMap.entrySet()){
						selectOneStr.append(" ").append(mapEntity.getKey()).append("=\"").append(mapEntity.getValue()).append("\" ");
					}
				}
				selectOneStr.append(" /><label for=\"q").append(queNum).append("_").append(optNum).append("\">");
				if (null != needFill && needFill == 1 && tbQuestOpt.getIsMulti() != null
						&& tbQuestOpt.getIsMulti() == 1) {
					String title = tbQuestOpt.getOptionDesc().replaceAll(" ", "&nbsp;");
					Matcher matcher = Pattern.compile("_+").matcher(title);
					int fillIndex = 1;
					while (matcher.find()) {
						String temp = title.substring(matcher.start(), matcher.end());
						title = title.replaceFirst(temp, createInput(queNum, optNum, fillIndex++, temp.length()));
						matcher = Pattern.compile("_+").matcher(title);
					}
					selectOneStr.append(title.replaceAll("@", "_").replaceAll("\n", "<br/>"));
				} else {
					selectOneStr.append(tbQuestOpt.getOptionDesc().replaceAll(" ", "&nbsp;"));
				}
				selectOneStr.append("</label>");
				// 其他描述
				String otherDesc = tbQuestOpt.getOtherDesc();
				if (StringUtils.isNotBlank(otherDesc)) {
					selectOneStr.append("<div class=\"div_item_desc\" rel=\"q").append(queNum).append("_")
							.append(optNum).append("\" >").append(otherDesc).append("</div>");
				}

				// 是否需要填空

				if (null != needFill && needFill == 1
						&& (tbQuestOpt.getIsMulti() == null || tbQuestOpt.getIsMulti() == 0)) {
					selectOneStr.append(createInput(queNum, optNum, null, null).replaceAll("@", "_"));
				}
				selectOneStr.append("</li>");
			}
			selectOneStr.append("<div style=\"clear:both;\"></div>");
			selectOneStr.append("</ul>");
			selectOneStr.append("<div style=\"clear:both;\"></div>");
			selectOneStr.append("<div class=\"div_table_clear_bottom\"></div>");
			selectOneStr.append("</div>");
			selectOneStr.append("<div class=\"errorMessage\"></div>");
			String otherDesc = tsProbSubject.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				selectOneStr.append("<div class=\"qinsert\">提示：").append(otherDesc).append("</div>");
			}
			selectOneStr.append("</div>");
		}
		return selectOneStr.toString();
	}

	/**
	 * 生成单选组件(手机)
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	private String produceSelectOneMobile(TsProbSubject tsProbSubject) {
		StringBuilder selectOneStr = new StringBuilder();
		if (null != tsProbSubject && null != tsProbSubject.getProOptList() && tsProbSubject.getProOptList().size() > 0) {
			Integer queNum = tsProbSubject.getNum();
			selectOneStr.append("<div class=\"field ui-field-contain\" id=\"div").append(queNum)
					.append("\" type=\"3\"  topic=\"").append(queNum).append("\" data-role=\"fieldcontain\" ");
			if (null != tsProbSubject.getMustAsk() && tsProbSubject.getMustAsk() == 1) {// 如果必答，则需要加上红星
				selectOneStr.append(" req=\"1\" ");
			} else {
				selectOneStr.append(" req=\"0\" ");
			}

			// 加入需要跳转的代码
			String jumpOrRela = this.produceJumpOrRelaMobile(tsProbSubject);
			if (StringUtils.isNotBlank(jumpOrRela)) {
				selectOneStr.append(jumpOrRela);
			}
			selectOneStr.append(" >");
			// 如果有跳转需要在指定选项上增加跳转内容
			selectOneStr.append("<div class=\"field-label\">");
			selectOneStr.append(tsProbSubject.getShowCode() == null ? "" : tsProbSubject.getShowCode()).append(" ")
					.append(tsProbSubject.getTitleDesc());
			if (null != tsProbSubject.getMustAsk() && tsProbSubject.getMustAsk() == 1) {// 如果必答，则需要加上红星
				selectOneStr.append(" <span class=\"req\">*</span>");
			}
			selectOneStr.append("</div>");

			selectOneStr.append("<div class=\"ui-controlgroup\">");
			List<TsProOpt> tbQuestOpts = tsProbSubject.getProOptList();

			// 跳转题号
			String toNum = null;
			// 选中项目
			String selectVal = null;
			if (null != tsProbSubject.getJumpType() && tsProbSubject.getJumpType().intValue() == 1
					&& StringUtils.isNotBlank(tsProbSubject.getJumpQuestCode())) {
				String[] split = tsProbSubject.getJumpQuestCode().split(",");
				if (split.length == 2) {
					toNum = split[0];
					selectVal = split[1];
				}
			}

			for (int i = 0; i < tbQuestOpts.size(); i++) {
				TsProOpt tbQuestOpt = tbQuestOpts.get(i);
				if (tbQuestOpt.getState().intValue() == 0) {
					continue;
				}
				// 是否需要填空
				Integer needFill = tbQuestOpt.getNeedFill();
				// 选项序号
				Integer optNum = tbQuestOpt.getNum();
				// 选项值
				String optVal = tbQuestOpt.getOptionValue();
				selectOneStr.append("<div class=\"ui-radio\">");
				selectOneStr.append("<span class=\"jqradiowrapper\">");
				selectOneStr.append("<input type=\"radio\" value=\"").append(tbQuestOpt.getOptionValue())
						.append("\" id=\"q").append(queNum).append("_").append(optNum).append("\" name=\"q")
						.append(queNum).append("\" style=\"display:none;\" ");
				if (StringUtils.isNotBlank(selectVal) && selectVal.equals(optVal.toString())
						&& StringUtils.isNotBlank(toNum)) {
					selectOneStr.append(" jumpto=\"").append(toNum).append("\" ");
				}
				selectOneStr.append("/>");

				selectOneStr.append("<a class=\"jqradio\" href=\"javascript:;\"></a>");
				selectOneStr.append("</span>");
				selectOneStr.append("<label for=\"q").append(queNum).append("_").append(optNum).append("\">");
				if (null != needFill && needFill == 1 && tbQuestOpt.getIsMulti() != null
						&& tbQuestOpt.getIsMulti() == 1) {
					String title = tbQuestOpt.getOptionDesc().replaceAll(" ", "&nbsp;");
					Matcher matcher = Pattern.compile("_+").matcher(title);
					int fillIndex = 1;
					while (matcher.find()) {
						String temp = title.substring(matcher.start(), matcher.end());
						title = title.replaceFirst(temp, createInputMobile(queNum, optNum, fillIndex++, temp.length()));
						matcher = Pattern.compile("_+").matcher(title);
					}
					selectOneStr.append(title.replaceAll("@", "_").replaceAll("\n", "<br/>"));
				} else {
					selectOneStr.append(tbQuestOpt.getOptionDesc().replaceAll(" ", "&nbsp;"));
				}
				selectOneStr.append("</label>");
				// 是否需要填空

				if (null != needFill && needFill == 1
						&& (tbQuestOpt.getIsMulti() == null || tbQuestOpt.getIsMulti() == 0)) {
					selectOneStr.append(createInput(queNum, optNum, null, null).replaceAll("@", "_"));
				}
				// 其他描述
				String otherDesc = tbQuestOpt.getOtherDesc();
				if (StringUtils.isNotBlank(otherDesc)) {
					selectOneStr.append("<div class=\"div_item_desc\" rel=\"q").append(queNum).append("_")
							.append(optNum).append("\" >").append(otherDesc).append("</div>");
				}
				selectOneStr.append("</div>");

			}
			selectOneStr.append("</div>");
			selectOneStr.append("<div class=\"errorMessage\"></div>");
			String otherDesc = tsProbSubject.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				selectOneStr.append("<div class=\"qinsert\">提示：").append(otherDesc).append("</div>");
			}
			selectOneStr.append("</div>");
		}
		return selectOneStr.toString();
	}

	/**
	 * 
	 * <p>
	 * 方法描述：
	 * </p>
	 * 
	 * 修订内容：
	 * 
	 * @MethodReviser rj,2018年3月5日,createInput
	 * 
	 */
	private String createInput(Integer queNum, Integer optNum, Integer fillNum, Integer length) {
		StringBuilder sb = new StringBuilder();
		sb.append("<input type=\"text\" maxlength=\"100\" name=\"q").append(queNum).append("@").append(optNum)
				.append(fillNum == null ? "" : ("@" + fillNum)).append("\" class=\"underline\" rel=\"q").append(queNum)
				.append("@").append(optNum).append("\" style=\"position: static;")
				.append(length == null ? "" : ("width:" + 21 * length + "px;")).append("\" />");
		return sb.toString();
	}

	/**
	 * 
	 * 
	 * 修订内容：修改输入框样式
	 * 
	 * @MethodReviser rj,2017年11月16日,createInputMobile 
	 * 
	 * 修订内容：rel格式修改
	 * 
	 * @MethodReviser rj,2018年3月5日,createInputMobile
	 * 
	 */
	private String createInputMobile(Integer queNum, Integer optNum, Integer fillNum, Integer length) {
		StringBuilder sb = new StringBuilder();
		sb.append("<input type=\"text\" maxlength=\"100\" name=\"q")
				.append(queNum)
				.append("@")
				.append(optNum)
				.append(fillNum == null ? "" : ("@" + fillNum))
				.append("\" class=\"ui-input-text OtherRadioText\" rel=\"q")
				.append(queNum)
				.append("@")
				.append(optNum)
				.append("\" style=\"line-height:24px;padding:2px 4px;position: static;margin-left:5px;margin-right:3px;")
				.append(length == null ? "" : ("width:" + 21 * length + "px;")).append("\" />");
		return sb.toString();
	}
}
