package com.chis.modules.system.queProducer;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsProbSubject;
import com.chis.modules.system.enumn.GenHtmlType;
import org.springframework.util.CollectionUtils;


/**
 *  <p>类描述：创建文本只读组件</p>
 * @ClassAuthor hsj 2021/8/25 10:26
 */
public class MakeCompText extends MakeComponet {
	@Override
	public String makeCompHtml(TsProbSubject tsProbSubject,
							   GenHtmlType genHtmlType) {
		String htmlStr = null;
		switch (genHtmlType) {
			case WEB_HTML_PAGE:
				htmlStr = this.produceText(tsProbSubject);
				break;
			case MOBILE_HTML_PAGE:
				htmlStr = this.produceTextMobile(tsProbSubject);
				break;
			default:
				break;
		}
		return htmlStr;
	}
	/**
	 *  <p>方法描述：生成文本只读显示（手机）</p>
	 * @MethodAuthor hsj
	 */
	private String produceTextMobile(TsProbSubject tsProbSubject) {
		StringBuilder htmlStr = new StringBuilder();
		if (null != tsProbSubject) {
			Integer queNum = tsProbSubject.getNum();
			htmlStr.append("<div class=\"field ui-field-contain\" id=\"div")
					.append(queNum).append("\" type=\"1\" topic=\"")
					.append(queNum).append("\"  data-role=\"fieldcontain\" ");
			htmlStr.append(" req=\"0\" ");
			htmlStr.append(">");

			htmlStr.append("<div class=\"field-label\">");
			htmlStr.append(
					tsProbSubject.getShowCode() == null ? "" : tsProbSubject
							.getShowCode()).append(" ");
			String title = tsProbSubject.getTitleDesc().replaceAll(" ",
					"&nbsp;");
			Matcher matcher = Pattern.compile("_+").matcher(title);
			int fillIndex = 1;
			while (matcher.find()) {
				String temp = title.substring(matcher.start(),
						matcher.end());
				title = title.replaceFirst(
						temp,
						createInputMobile(tsProbSubject.getNum(), fillIndex++, temp.length()));
				matcher = Pattern.compile("_+").matcher(title);
			}
			htmlStr.append(title.replaceAll("@", "_").replaceAll("\n",
					"<br/>"));
			htmlStr.append("</div>");

			htmlStr.append("<div class=\"errorMessage\"></div>");
			String otherDesc = tsProbSubject.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				htmlStr.append("<div class=\"qinsert\">提示：")
						.append(otherDesc).append("</div>");
			}
			htmlStr.append("</div>");
		}
		return htmlStr.toString();
	}

	/**
	 *  <p>方法描述：生成文本只读显示（web）</p>
	 * @MethodAuthor hsj
	 */
	private String produceText(TsProbSubject tsProbSubject) {
		StringBuilder htmlStr = new StringBuilder();
		if (null != tsProbSubject) {
			Map<String,String> pageEventMap = this.findEventByScript(null == tsProbSubject.getPool() ? null : tsProbSubject.getPool().getExecScript());
			Integer queNum = tsProbSubject.getNum();
			htmlStr.append("<div class=\"div_question\" id=\"div")
					.append(queNum).append("\" ");
			htmlStr.append(">");
			htmlStr.append("<div class=\"div_title_question_all\">");
			htmlStr.append("<div class=\"div_topic_question\">");
			htmlStr.append("<b>")
					.append(tsProbSubject.getShowCode() == null ? ""
							: tsProbSubject.getShowCode()).append(" </b>");
			htmlStr.append("</div>");

			htmlStr.append("<div id=\"divTitle").append(queNum)
					.append("\" class=\"div_title_question\">");
			String title = tsProbSubject.getTitleDesc().replaceAll(" ",
					"&nbsp;");
			Matcher matcher = Pattern.compile("_+").matcher(title);
			int fillIndex = 1;
			while (matcher.find()) {
				String temp = title.substring(matcher.start(),
						matcher.end());
				title = title.replaceFirst(
						temp,
						createInput(tsProbSubject.getNum(), fillIndex++, temp.length(), pageEventMap));
				matcher = Pattern.compile("_+").matcher(title);
			}
			htmlStr.append(title.replaceAll("@", "_").replaceAll("\n",
					"<br/>"));

			htmlStr.append("</div>");
			htmlStr.append("<div style=\"clear:both;\"></div>");
			htmlStr.append("</div>");

			htmlStr.append(
					"<div class=\"div_table_radio_question\" id=\"divquestion")
					.append(queNum).append("\">");
			htmlStr.append("<div class=\"div_table_clear_top\"></div>");

			if (StringUtils.isNotBlank(tsProbSubject.getQuestUnit())) {
				htmlStr.append(" ").append(tsProbSubject.getQuestUnit());
			}
			htmlStr.append("<div style=\"clear:both;\"></div>");
			htmlStr.append("<div class=\"div_table_clear_bottom\"></div>");
			htmlStr.append("</div>");
			htmlStr.append("<div class=\"errorMessage\"></div>");
			String otherDesc = tsProbSubject.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				htmlStr.append("<div class=\"qinsert\">提示：")
						.append(otherDesc).append("</div>");
			}
			htmlStr.append("</div>");

		}
		return htmlStr.toString();
	}

	private String createInput(Integer qesIndex, Integer fillIndex, Integer length, Map<String,String> pageEventMap) {
		StringBuilder input = new StringBuilder();
		input.append("<input type=\"text\" readonly name=\"q")
				.append(qesIndex + (fillIndex == null ? "" : ("@" + fillIndex)))
				.append("\" ");
		if (length != null) {
			input.append("style=\"width:").append(21 * length).append("px\" ");
		}
		input.append(" id=\"q")
				.append(qesIndex + (fillIndex == null ? "" : ("@" + fillIndex)))
				.append( "\" class=\"underline\" ");
		//加入页面脚本事件
		if(!CollectionUtils.isEmpty(pageEventMap)){
			for(Map.Entry<String,String> mapEntity : pageEventMap.entrySet()){
				input.append(" ").append(mapEntity.getKey()).append("=\"").append(mapEntity.getValue()).append("\" ");
			}
		}
		input.append(" />");
		return input.toString();
	}

	private String createInputMobile(Integer qesIndex, Integer fillIndex,Integer length) {
		StringBuilder input = new StringBuilder();
		input.append("<input type=\"text\" readonly name=\"q")
				.append(qesIndex + (fillIndex == null ? "" : ("@" + fillIndex)))
				.append("\" ");
		if (length != null) {
			input.append("style=\"line-height:24px;padding:2px 4px;margin-left:5px;margin-right:3px;width:").append(21 * length).append("px\" ");
		}
		input.append(" id=\"q")
				.append(qesIndex + (fillIndex == null ? "" : ("@" + fillIndex)))
				.append( "\" class=\"ui-input-text\" ");
		input.append(" />");
		return input.toString();
	}
}
