package com.chis.modules.system.queProducer;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsProbSubject;
import com.chis.modules.system.enumn.GenHtmlType;

/**
 * 模块描述：创建单选组件
 * 
 * <AUTHOR>
 * @createDate 2016年3月26日
 */
public class MakeCompTitle extends MakeComponet {
	@Override
	public String makeCompHtml(TsProbSubject tsProbSubject, GenHtmlType genHtmlType) {
		String htmlStr = null;
		switch (genHtmlType) {
		case WEB_HTML_PAGE:
			htmlStr = this.produceTitle(tsProbSubject);
			break;
		case MOBILE_HTML_PAGE:
			htmlStr = this.produceTitleMobile(tsProbSubject);
			break;
		default:
			break;
		}
		return htmlStr;
	}

	/**
	 * 生成标题组件(微信)
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	private String produceTitleMobile(TsProbSubject tsProbSubject) {
		StringBuilder htmlStr = new StringBuilder();
		if (null != tsProbSubject) {
			Integer queNum = tsProbSubject.getNum();
			htmlStr.append("<div class=\"field ui-field-contain\" id=\"div").append(queNum)
					.append("\" type=\"1\" topic=\"").append(queNum).append("\"  data-role=\"fieldcontain\" ");
			// 加入需要跳转的代码
			String jumpOrRela = this.produceJumpOrRelaMobile(tsProbSubject);
			if (StringUtils.isNotBlank(jumpOrRela)) {
				htmlStr.append(jumpOrRela);
			}
			htmlStr.append(">");
			htmlStr.append("<div class=\"field-label\">");
			htmlStr.append("<b>");
			htmlStr.append(tsProbSubject.getShowCode() == null ? "" : tsProbSubject.getShowCode()).append(" ")
					.append(tsProbSubject.getTitleDesc().replaceAll("\n", "<br/>").replaceAll(" ", "&nbsp;"))
					.append("</b>");
			String otherDesc = tsProbSubject.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				htmlStr.append("<div class=\"qinsert\">提示：").append(otherDesc).append("</div>");
			}
			htmlStr.append("</div>");
			htmlStr.append("</div>");
		}
		return htmlStr.toString();
	}

	/**
	 * 生成标题组件
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	private String produceTitle(TsProbSubject tsProbSubject) {
		StringBuilder htmlStr = new StringBuilder();
		if (null != tsProbSubject) {
			htmlStr.append("<div class=\"div_title_page_question div_question\" id=\"div")
					.append(tsProbSubject.getNum()).append("\"");
			// 加入需要跳转的代码
			String jumpOrRela = this.produceJumpOrRela(tsProbSubject);
			if (StringUtils.isNotBlank(jumpOrRela)) {
				htmlStr.append(jumpOrRela);
			}
			htmlStr.append("> ");
			htmlStr.append("<span style=\"font-size:16px;\">").append(
					tsProbSubject.getShowCode() == null ? "" : tsProbSubject.getShowCode());
			htmlStr.append(tsProbSubject.getTitleDesc().replaceAll("\n", "<br/>").replaceAll(" ", "&nbsp;")).append(
					"</span><br></br>");
			String otherDesc = tsProbSubject.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				htmlStr.append("<div class=\"div_ins_question\">提示：").append(otherDesc).append("</div>");
			}
			htmlStr.append("</div>");

		}
		return htmlStr.toString();
	}

}
