package com.chis.modules.system.queProducer;

import com.chis.modules.system.entity.TsProbSubject;
import com.chis.modules.system.enumn.GenHtmlType;

/**
 * 模块描述：创建下拉单选组件
 * 
 * <AUTHOR>
 * @createDate 2016年3月26日
 */
public class MakeCompPullDownSelectO extends MakeComponet {

	@Override
	public String makeCompHtml(TsProbSubject tsProbSubject, GenHtmlType genHtmlType) {
		String htmlStr = null;
		switch (genHtmlType) {
		case WEB_HTML_PAGE:
			break;
		case MOBILE_HTML_PAGE:
			break;
		default:
			break;
		}
		return htmlStr;
	}

}
