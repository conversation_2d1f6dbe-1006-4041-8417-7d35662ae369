package com.chis.modules.system.queProducer;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.util.CollectionUtils;

import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TbProbColsdefine;
import com.chis.modules.system.entity.TbProbRowtitle;
import com.chis.modules.system.entity.TsProOpt;
import com.chis.modules.system.entity.TsProbSubject;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.enumn.GenHtmlType;
import com.chis.modules.system.service.CommServiceImpl;

/**
 * 
 * <p>
 * 类描述：表格题生成器
 * <p>
 * 
 * @ClassAuthor rj,2017年11月8日,MakeCompTable
 * 
 * 
 */
public class MakeCompTable extends MakeComponet {
	private CommServiceImpl commServiceImpl = SpringContextHolder.getBean(CommServiceImpl.class);

	@Override
	public String makeCompHtml(TsProbSubject sub, GenHtmlType genHtmlType) {
		String htmlStr = null;
		switch (genHtmlType) {
		case WEB_HTML_PAGE:
			htmlStr = this.produceTable(sub);
			break;
		case MOBILE_HTML_PAGE:
			htmlStr = this.produceTableMobile(sub);
			break;
		default:
			break;
		}
		return htmlStr;
	}

	/**
	 * 
	 * <p>
	 * 方法描述：WEB版表格题
	 * </p>
	 * 
	 * @MethodAuthor rj,2017年11月8日,produceTable
	 */
	private String produceTable(TsProbSubject sub) {
		StringBuilder queHtml = new StringBuilder();
		if (null != sub && sub.getFkByTableId() != null) {
			Integer queNum = sub.getNum();
			queHtml.append("<div class=\"div_question\" id=\"div").append(queNum).append("\" ");
			// 加入需要跳转的代码
			String jumpOrRela = this.produceJumpOrRela(sub);
			if (StringUtils.isNotBlank(jumpOrRela)) {
				queHtml.append(jumpOrRela);
			}
			queHtml.append(">");
			queHtml.append("<div class=\"div_title_question_all\">");
			queHtml.append("<div class=\"div_topic_question\">");
			queHtml.append("<b>").append(sub.getShowCode() == null ? "" : sub.getShowCode()).append(" </b>");
			queHtml.append(sub.getTitleDesc());
			if (sub.getFkByTableId().getRowFixed() == 0) {
				queHtml.append("<a class=\"addrow blue\" style=\"margin-left:12px\" onclick=\"addRow('table" + queNum
						+ "')\">添行</a>");
			}
			if (null != sub.getMustAsk() && sub.getMustAsk() == 1) {// 如果必答，则需要加上红星
				queHtml.append("<span style=\"color:red;\">&#160;*</span>");
			}
			queHtml.append("</div>");
			queHtml.append("	<div style=\"clear:both;\"></div>");
			queHtml.append("</div>");
			queHtml.append(
					"<div class=\"div_table_radio_question\" style=\"padding: 20px 0px 20px 0;\"   id=\"divquestion")
					.append(queNum).append("\">");
			queHtml.append("	<div class=\"div_table_clear_top\"></div>");
			produceTableHtml(sub, queHtml);
			queHtml.append("	<div class=\"div_table_clear_bottom\"></div>");
			queHtml.append("</div>");
			queHtml.append("<div class=\"errorMessage\"></div>");
			String otherDesc = sub.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				queHtml.append("<div class=\"div_ins_question\">提示：").append(otherDesc).append("</div>");
			}
			queHtml.append("</div>");
		}
		return queHtml.toString();
	}

	/**
	 * 
	 * <p>
	 * 方法描述：tableHtml生成
	 * </p>
	 * 
	 * 修订内容：逻辑调整
	 * 
	 * @MethodReviser rj,2017年12月15日,produceTableHtml
	 * 
	 * 
	 * 
	 * @MethodAuthor rj,2017年11月8日,produceTableHtml
	 */
	private void produceTableHtml(TsProbSubject sub, StringBuilder queHtml) {
		List<TbProbRowtitle> rowtitles = sub.getFkByTableId().getRowtitles();
		List<TbProbColsdefine> colsdefines = sub.getFkByTableId().getColsdefines();
		// rowNum ------ colNum
		Map<Integer, Map<Integer, TbProbRowtitle>> rowTitleModel = new HashMap<Integer, Map<Integer, TbProbRowtitle>>(0);
		Map<Integer, Map<Integer, TbProbColsdefine>> colModel = new HashMap<Integer, Map<Integer, TbProbColsdefine>>(0);
		// 标题列数
		Integer titleCols = 0;
		Integer queNum = sub.getNum();
		// 所需生成行数
		Integer needRow = sub.getFkByTableId().getDefaultLineNum() == null ? 0 : sub.getFkByTableId()
				.getDefaultLineNum();
		// 标题列行数
		Integer headerRowNum = 0;
		Integer headerColNum = 0;
		for (TbProbRowtitle row : rowtitles) {
			Map<Integer, TbProbRowtitle> titleColumnMap = rowTitleModel.get(row.getRowIndex());
			if (sub.getFkByTableId().getDefaultLineNum() == null && row.getRowIndex() > needRow) {
				needRow = row.getRowIndex();
			}
			if (titleColumnMap == null) {
				titleColumnMap = new HashMap<>();
				rowTitleModel.put(row.getRowIndex(), titleColumnMap);
			}
			if (row.getColIndex() > titleCols) {
				titleCols = row.getColIndex();
			}
			titleColumnMap.put(row.getColIndex(), row);
		}
		for (TbProbColsdefine col : colsdefines) {
			//停用的过滤掉
			if(null == col.getState() || 1 != col.getState()){
				continue;
			}
			Map<Integer, TbProbColsdefine> colMap = colModel.get(col.getRowIndex());
			if (colMap == null) {
				colMap = new HashMap<>();
				colModel.put(col.getRowIndex(), colMap);
			}
			colMap.put(col.getColIndex(), col);
			if (col.getRowIndex() != null && col.getRowIndex() > headerRowNum) {
				headerRowNum = col.getRowIndex();
			}
			if (col.getColIndex() != null && col.getColIndex() > headerColNum) {
				headerColNum = col.getColIndex();
			}
		}
		queHtml.append("<div style=\"overflow:auto;\">");
		queHtml.append("<table style=\"width:100%;white-space: nowrap;margin-bottom: 0px;\" border=\"0px\" id=\"table"
				+ sub.getNum() + "\" class=\"matrix-rating\" select=\"1\" type=\"10\" ");
		Map<String,String> pageEventMap = this.findEventByScript(null == sub.getPool() ? null : sub.getPool().getExecScript());
		//加入页面脚本事件
		if(!CollectionUtils.isEmpty(pageEventMap)){
			for(Map.Entry<String,String> mapEntity : pageEventMap.entrySet()){
				queHtml.append(" ").append(mapEntity.getKey()).append("=\"").append(mapEntity.getValue()).append("\" ");
			}
		}
		queHtml.append(" >");
		if (sub.getFkByTableId().getRowFixed() != 1) {
			queHtml.append("<thead>");
		}
		for (int r = 1; r <= colModel.size(); r++) {
			queHtml.append("<tr>");
			if (r == 1 && sub.getFkByTableId().getRowFixed() != 1) {
				queHtml.append("<th align=\"center\" rowspan=\"" + headerRowNum + "\">操作</th>");
			}
			if (rowTitleModel.get(r) != null) {
				Map<Integer, TbProbRowtitle> titleColumnMap = rowTitleModel.get(r);
				for (int t = 1; t <= titleColumnMap.size(); t++) {
					TbProbRowtitle title = titleColumnMap.get(t);
					if (title == null) {
						continue;
					}
					queHtml.append("<th colspan=\"").append(title.getColspan()).append("\"");
					queHtml.append(" rowspan=\"").append(title.getRowspan()).append("\" align=\"center\">");
					queHtml.append(title.getTitle());
					queHtml.append("</th>");
				}
			}
			Map<Integer, TbProbColsdefine> colMap = colModel.get(r);
			for (int c = 1; c <= headerColNum; c++) {
				TbProbColsdefine col = colMap.get(c);
				if (col == null) {
					continue;
				}
				queHtml.append("<th colspan=\"").append(col.getCols()).append("\"");
				queHtml.append(" rowspan=\"").append(col.getRowspan()).append("\" align=\"center\">");
				if (null!=col.getColMust() && col.getColMust()==1) {
					queHtml.append("<span style = \"color:red;\">*</span>");
				}
				queHtml.append(col.getColDesc());
				queHtml.append("</th>");
			}
			queHtml.append("</tr>");
		}
		if (sub.getFkByTableId().getRowFixed() != 1) {
			queHtml.append("</thead><tbody>");
		}
		int rowIndex = 1;
		for (int r = colModel.size() + 1; r <= needRow; r++) {
			queHtml.append("<tr>");
			if (sub.getFkByTableId().getRowFixed() != 1) {
				queHtml.append("<td align=\"center\"><a href=\"javascript:;\" class=\"rowRemove\"> 删除</a></td>");
			}
			if (rowTitleModel.get(r) != null) {
				Map<Integer, TbProbRowtitle> titleColumnMap = rowTitleModel.get(r);
				for (int t = 1; t <= titleColumnMap.size(); t++) {
					TbProbRowtitle title = titleColumnMap.get(t);
					if (title == null) {
						continue;
					}
					queHtml.append("<th colspan=\"").append(title.getColspan()).append("\"");
					queHtml.append(" rowspan=\"").append(title.getRowspan()).append("\" align=\"center\">");
					queHtml.append(title.getTitle());
					queHtml.append("</th>");
				}
			} else {
				for (int i = 1; i <= titleCols; i++) {
					queHtml.append("<th align=\"center\">-");
					queHtml.append("</th>");
				}
			}
			Map<Integer, TbProbColsdefine> colMap = colModel.get(colModel.size());
			for (int c = 1; c <= headerColNum; c++) {
				TbProbColsdefine col = colMap.get(c);
				if (col == null) {
					for (int t = 1; t <= colModel.size(); t++) {
						Map<Integer, TbProbColsdefine> tempMap = colModel.get(t);
						TbProbColsdefine tempCol = tempMap.get(c);
						if (tempCol != null && tempCol.getColType() != 0) {
							col = tempCol;
							break;
						}
					}
					if (col == null)
						continue;
				}
				String execScript = col.getExecScript();
				Map<String,String> eventMap = this.findEventByScript(execScript);
				StringBuffer eventBuffer = new StringBuffer("");
				//加入事件
				if(!CollectionUtils.isEmpty(eventMap)){
					for(Map.Entry<String,String> mapEntity : eventMap.entrySet()){
						eventBuffer.append(" ").append(mapEntity.getKey()).append("=\"").append(mapEntity.getValue()).append("\" ");
					}
				}
				queHtml.append("<td  align=\"center\">");
				String colID = "q" + queNum + "_" + c + "_" + rowIndex;
				String colDesc ="此题";
				if(!"".equals(col.getColDesc())){
					colDesc = col.getColDesc();
				}
				if (null!=col.getDsType() && col.getDsType().intValue() ==1) {
					//取值类型为一般
					if(col.getColType() == 1 ){
						//文本
						queHtml.append("<input name=\"" + colID + "\" id=\"" + colID
								+ "\" type=\"text\" maxlength=\"100\" class=\"ui-input-text\" style=\"overflow: hidden;\" ");
						if (null != col.getColMust() && col.getColMust() == 1) {// 如果必答，则需要加上验证
							queHtml.append(" zwx:valid=\"required\" zwx:errmsg=\""+colDesc+"为必答题，请填写答案!\" ");
						}
						queHtml.append(eventBuffer).append("/>");
					}else if(col.getColType() == 2){
						//整数
						queHtml.append("<input name=\"" + colID + "\" id=\"" + colID
								+ "\" type=\"text\" maxlength=\"100\" class=\"ui-input-text\" style=\"overflow: hidden;\" ");
						if (null != col.getColMust() && col.getColMust() == 1) {// 如果必答，则需要加上验证
							queHtml.append(" zwx:valid=\"required|isInt\" zwx:errmsg=\""+colDesc+"为必答题，请填写答案!|"+colDesc+"数据格式为整数，请正确填写答案!\" ");
						}else{
							queHtml.append(" zwx:valid=\"isInt\" zwx:errmsg=\""+colDesc+"数据格式为整数，请正确填写答案!\" ");
						}
						queHtml.append(eventBuffer).append("/>");
					}else if(col.getColType() == 3){
						//小数
						queHtml.append("<input name=\"" + colID + "\" id=\"" + colID
								+ "\" type=\"text\" maxlength=\"100\" class=\"ui-input-text\" style=\"overflow: hidden;\" ");
						if (null != col.getColMust() && col.getColMust() == 1) {// 如果必答，则需要加上验证
							queHtml.append(" zwx:valid=\"required|isNumber\" zwx:errmsg=\""+colDesc+"为必答题，请填写答案!|"+colDesc+"数据格式为数字，请正确填写答案!\" ");
						} else {
							queHtml.append(" zwx:valid=\"isNumber\" zwx:errmsg=\""+colDesc+"数据格式为数字，请正确填写答案!\" ");
						}
						queHtml.append(eventBuffer).append("/>");
					}
				}else if (null!=col.getDsType() && 2==col.getDsType().intValue()) {//码表
					List<TsSimpleCode> list = commServiceImpl.findLevelSimpleCodesByTypeId(col.getDsCdcode());
					if (col.getColType() == 1 || col.getColType() == 2 || col.getColType() == 3) {
						if (!CollectionUtils.isEmpty(list)) {
							int i = 0;
							queHtml.append("<div style=\"display: flex;align-items: center;justify-content: center;\">");
							for (TsSimpleCode t : list) {
								queHtml.append("<input type=\"radio\"  name=\"").append(colID).append("\" id=\"").append(colID).append("\" ");
								queHtml.append("value=\"").append(t.getRid()).append("\"  ");
								if (null != col.getColMust() && col.getColMust() == 1 && i == 0) {// 如果必答，则需要加上验证
									queHtml.append(" zwx:valid=\"requireChecked\" zwx:errmsg=\""+colDesc+"为必答题，请填写答案!\" ");
								}
								queHtml.append(" style=\"width: 15px;\"");
								queHtml.append(eventBuffer).append(" >").append(t.getCodeName()).append("</input>");
								i++;
							}
							queHtml.append("</div>");
						}
					}else if(6 == col.getColType()){
						//使用原生的select组件
						this.generateColType6(col,colID,colDesc,queHtml, eventBuffer, list, true);
					}
				}
				/**
				 * 修订内容：日期填空样式修改
				 * 
				 * @MethodReviser rj,2017年11月8日,produceTableHtml
				 * 
				 */
				if (col.getColType() == 4) {
					queHtml.append("<input name=\""
							+ colID
							+ "\" id=\""
							+ colID
							+ "\" type=\"text\" maxlength=\"100\" onclick=\"calendar.show(this,null,event);\" style=\"text-align:center;\" ");
					if (null != col.getColMust() && col.getColMust() == 1) {// 如果必答，则需要加上验证
						queHtml.append(" zwx:valid=\"required|isDate\" zwx:errmsg=\""+colDesc+"为必答题，请填写答案!|"+colDesc+"数据格式为日期格式，请正确填写答案!\" ");
					}else{
						queHtml.append(" zwx:valid=\"isDate\" zwx:errmsg=\""+colDesc+"数据格式为日期格式，请正确填写答案!\" ");
					}
					queHtml.append(eventBuffer).append("/>");
				}
				queHtml.append("</td>");
			}
			queHtml.append("</tr>");
			rowIndex++;
		}
		if (sub.getFkByTableId().getRowFixed() != 1) {
			queHtml.append("</tbody>");
		}
		queHtml.append("</table></div>");
	}

	/**
	 * <p>方法描述： 生成类型是6的组件 </p>
	 * @MethodAuthor： pw 2022/10/5
	 **/
	private void generateColType6(TbProbColsdefine col,String colID, String colDesc, StringBuilder queHtml,
					 StringBuffer eventBuffer, List<TsSimpleCode> list, boolean flag){

		if(flag){
			//普通select下拉框
			if (!CollectionUtils.isEmpty(list)){
				queHtml.append("<div style=\"display: flex;align-items: center;justify-content: center;\">");
				queHtml.append("<select style=\"width: 100%;\"  name=\"").append(colID).append("\" id=\"").append(colID).append("\" ");
				//加入事件
				queHtml.append(eventBuffer);
				if (null != col.getColMust() && col.getColMust() == 1) {// 如果必答，则需要加上验证
					queHtml.append(" zwx:valid=\"required\" zwx:errmsg=\""+colDesc+"为必答题，请选择答案!\" ");
				}
				queHtml.append(">");
				queHtml.append("<option value=\"\">-- 请选择 --</option>");
				for (TsSimpleCode t : list) {
					String codeName = t.getCodeName();
					String codeNo = t.getCodeNo();
					String extends1 = null == t.getExtendS1() ? "" : t.getExtendS1();
					String extends2 = null == t.getExtendS2() ? "" : t.getExtendS2().toString();
					String extends3 = null == t.getExtendS3() ? "" : t.getExtendS3();
					String extends4 = null == t.getExtendS4() ? "" : t.getExtendS4();
					String extends5 = null == t.getExtendS5() ? "" : t.getExtendS5();
					String extends6 = null == t.getExtendS6() ? "" : t.getExtendS6();
					String extends7 = null == t.getExtendS7() ? "" : t.getExtendS7();
					queHtml.append("<option value=\"").append(codeName).append("\"");
					queHtml.append(" codeNo=\"").append(codeNo).append("\"");
					queHtml.append(" rid=\"").append(t.getRid()).append("\"");
					queHtml.append(" extends1=\"").append(extends1).append("\"");
					queHtml.append(" extends2=\"").append(extends2).append("\"");
					queHtml.append(" extends3=\"").append(extends3).append("\"");
					queHtml.append(" extends4=\"").append(extends4).append("\"");
					queHtml.append(" extends5=\"").append(extends5).append("\"");
					queHtml.append(" extends6=\"").append(extends6).append("\"");
					queHtml.append(" extends7=\"").append(extends7).append("\"");
					queHtml.append(">").append(codeName);
					queHtml.append("</option>");
				}
				queHtml.append("</select>");
				queHtml.append("</div>");
			}
			return;
		}
		String datalistId = colID+"_"+"datalist";
		//html5 datalist
		queHtml.append("<input name=\"" + colID + "\" id=\"" + colID
				+ "\" type=\"text\" list=\""+datalistId+"\" maxlength=\"100\" class=\"ui-input-text\" style=\"overflow: hidden;\" ");
		if (null != col.getColMust() && col.getColMust() == 1) {// 如果必答，则需要加上验证
			queHtml.append(" zwx:valid=\"required\" zwx:errmsg=\""+colDesc+"为必答题，请填写答案!\" ");
		}
		queHtml.append(eventBuffer).append("/>");
		queHtml.append("<datalist").append(" id=\"").append(datalistId).append("\" ").append(">");
		for (TsSimpleCode t : list) {
			String codeName = t.getCodeName();
			String codeNo = t.getCodeNo();
			String extends1 = null == t.getExtendS1() ? "" : t.getExtendS1();
			String extends2 = null == t.getExtendS2() ? "" : t.getExtendS2().toString();
			String extends3 = null == t.getExtendS3() ? "" : t.getExtendS3();
			String extends4 = null == t.getExtendS4() ? "" : t.getExtendS4();
			String extends5 = null == t.getExtendS5() ? "" : t.getExtendS5();
			String extends6 = null == t.getExtendS6() ? "" : t.getExtendS6();
			String extends7 = null == t.getExtendS7() ? "" : t.getExtendS7();
			queHtml.append("<option value=\"").append(codeName).append("\"");
			queHtml.append(" codeNo=\"").append(codeNo).append("\"");
			queHtml.append(" rid=\"").append(t.getRid()).append("\"");
			queHtml.append(" extends1=\"").append(extends1).append("\"");
			queHtml.append(" extends2=\"").append(extends2).append("\"");
			queHtml.append(" extends3=\"").append(extends3).append("\"");
			queHtml.append(" extends4=\"").append(extends4).append("\"");
			queHtml.append(" extends5=\"").append(extends5).append("\"");
			queHtml.append(" extends6=\"").append(extends6).append("\"");
			queHtml.append(" extends7=\"").append(extends7).append("\"");
			queHtml.append(">").append(codeName);
			queHtml.append("</option>");
		}
		queHtml.append("</datalist>");
	}

	/**
	 * 
	 * <p>
	 * 方法描述：手机版表格题生成
	 * </p>
	 * 
	 * @MethodAuthor rj,2017年11月8日,produceTableMobile
	 * 
	 *               修订内容：修改移动版生成方式
	 * 
	 * @MethodReviser rj,2017年11月11日,produceTableMobile
	 * 
	 */
	private String produceTableMobile(TsProbSubject sub) {
		StringBuilder queHtml = new StringBuilder();
		if (null != sub && sub.getFkByTableId() != null) {
			Integer queNum = sub.getNum();
			queHtml.append("<div class=\"field ui-field-contain\" id=\"div").append(queNum)
					.append("\" type=\"1\" topic=\"").append(queNum).append("\"")
					.append(" data-role=\"fieldcontain\" ");
			if (null != sub.getMustAsk() && sub.getMustAsk() == 1) {// 如果必答，则需要加上红星
				queHtml.append(" req=\"1\" ");
			} else {
				queHtml.append(" req=\"0\" ");
			}
			// 加入需要跳转的代码
			String jumpOrRela = this.produceJumpOrRela(sub);
			if (StringUtils.isNotBlank(jumpOrRela)) {
				queHtml.append(jumpOrRela);
			}
			queHtml.append(">");
			queHtml.append("<div class=\"div_table_radio_question\"   id=\"divquestion").append(queNum).append("\">");
			queHtml.append("<div class=\"field-label\">");
			queHtml.append("<b>").append(sub.getShowCode() == null ? "" : sub.getShowCode()).append(sub.getTitleDesc())
					.append(" </b>");

			if (sub.getFkByTableId().getRowFixed() == 0) {
				queHtml.append("<a class=\"addrow blue\" style=\"margin-left:12px\" onclick=\"addRow('table" + queNum
						+ "')\">添行</a>");
			}
			if (null != sub.getMustAsk() && sub.getMustAsk() == 1) {// 如果必答，则需要加上红星
				queHtml.append("<span style=\"color:red;\">&#160;*</span>");
			}
			queHtml.append("</div>");
			queHtml.append("	<div class=\"div_table_clear_top\"></div>");
			produceTableMobileHtml(sub, queHtml);
			queHtml.append("	<div class=\"div_table_clear_bottom\"></div>");
			queHtml.append("</div>");
			queHtml.append("<div class=\"errorMessage\"></div>");
			String otherDesc = sub.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				queHtml.append("<div class=\"div_ins_question\">提示：").append(otherDesc).append("</div>");
			}
			queHtml.append("</div>");
		}
		return queHtml.toString();
	}

	/**
	 * 
	 * <p>
	 * 方法描述：手机版tableHtml
	 * </p>
	 * 
	 * @MethodAuthor rj,2017年11月8日,produceTableMobileHtml
	 * 
	 *               修订内容：增加操作列行合并
	 * 
	 * @MethodReviser rj,2017年12月15日,produceTableMobileHtml
	 * 
	 */
	private void produceTableMobileHtml(TsProbSubject sub, StringBuilder queHtml) {
		List<TbProbRowtitle> rowtitles = sub.getFkByTableId().getRowtitles();
		List<TbProbColsdefine> colsdefines = sub.getFkByTableId().getColsdefines();
		// rowNum ------ colNum
		Map<Integer, Map<Integer, TbProbRowtitle>> rowTitleModel = new HashMap<Integer, Map<Integer, TbProbRowtitle>>(0);
		Map<Integer, Map<Integer, TbProbColsdefine>> colModel = new HashMap<Integer, Map<Integer, TbProbColsdefine>>(0);
		// 标题列数
		Integer titleCols = 0;
		Integer queNum = sub.getNum();
		// 所需生成行数
		Integer needRow = sub.getFkByTableId().getDefaultLineNum() == null ? 0 : sub.getFkByTableId()
				.getDefaultLineNum();
		// 标题列行数
		Integer headerRowNum = 0;
		Integer headerColNum = 0;
		for (TbProbRowtitle row : rowtitles) {
			Map<Integer, TbProbRowtitle> titleColumnMap = rowTitleModel.get(row.getRowIndex());
			if (sub.getFkByTableId().getDefaultLineNum() == null && row.getRowIndex() > needRow) {
				needRow = row.getRowIndex();
			}
			if (titleColumnMap == null) {
				titleColumnMap = new HashMap<>();
				rowTitleModel.put(row.getRowIndex(), titleColumnMap);
			}
			if (row.getColIndex() > titleCols) {
				titleCols = row.getColIndex();
			}
			titleColumnMap.put(row.getColIndex(), row);
		}
		for (TbProbColsdefine col : colsdefines) {
			Map<Integer, TbProbColsdefine> colMap = colModel.get(col.getRowIndex());
			if (colMap == null) {
				colMap = new HashMap<>();
				colModel.put(col.getRowIndex(), colMap);
			}
			colMap.put(col.getColIndex(), col);
			if (col.getRowIndex() != null && col.getRowIndex() > headerRowNum) {
				headerRowNum = col.getRowIndex();
			}
			if (col.getColIndex() != null && col.getColIndex() > headerColNum) {
				headerColNum = col.getColIndex();
			}
		}
		queHtml.append("<div style=\"overflow:auto;\">");
		queHtml.append("<table style=\"width:100%;white-space: nowrap;margin-bottom: 0px;\" border=\"0px\" id=\"table"
				+ sub.getNum() + "\" class=\"matrix-rating\" select=\"1\" type=\"10\">");
		if (sub.getFkByTableId().getRowFixed() != 1) {
			queHtml.append("<thead>");
		}
		for (int r = 1; r <= colModel.size(); r++) {
			queHtml.append("<tr>");
			if (r == 1 && sub.getFkByTableId().getRowFixed() != 1) {
				queHtml.append("<th align=\"center\" rowspan=\"" + headerRowNum + "\">操作</th>");
			}
			if (rowTitleModel.get(r) != null) {
				Map<Integer, TbProbRowtitle> titleColumnMap = rowTitleModel.get(r);
				for (int t = 1; t <= titleColumnMap.size(); t++) {
					TbProbRowtitle title = titleColumnMap.get(t);
					if (title == null) {
						continue;
					}
					queHtml.append("<th colspan=\"").append(title.getColspan()).append("\"");
					queHtml.append(" rowspan=\"").append(title.getRowspan()).append("\" align=\"center\">");
					queHtml.append(title.getTitle());
					queHtml.append("</th>");
				}
			}
			Map<Integer, TbProbColsdefine> colMap = colModel.get(r);
			for (int c = 1; c <= headerColNum; c++) {
				TbProbColsdefine col = colMap.get(c);
				if (col == null) {
					continue;
				}
				queHtml.append("<th colspan=\"").append(col.getCols()).append("\"");
				queHtml.append(" rowspan=\"").append(col.getRowspan()).append("\" align=\"center\">");
				queHtml.append(col.getColDesc());
				queHtml.append("</th>");
			}
			queHtml.append("</tr>");
		}
		if (sub.getFkByTableId().getRowFixed() != 1) {
			queHtml.append("</thead><tbody>");
		}
		int rowIndex = 1;
		for (int r = colModel.size() + 1; r <= needRow; r++) {
			queHtml.append("<tr>");
			if (sub.getFkByTableId().getRowFixed() != 1) {
				queHtml.append("<td align=\"center\"><a href=\"javascript:;\" class=\"rowRemove\"> 删除</a></td>");
			}
			if (rowTitleModel.get(r) != null) {
				Map<Integer, TbProbRowtitle> titleColumnMap = rowTitleModel.get(r);
				for (int t = 1; t <= titleColumnMap.size(); t++) {
					TbProbRowtitle title = titleColumnMap.get(t);
					if (title == null) {
						continue;
					}
					queHtml.append("<th colspan=\"").append(title.getColspan()).append("\"");
					queHtml.append(" rowspan=\"").append(title.getRowspan()).append("\" align=\"center\">");
					queHtml.append(title.getTitle());
					queHtml.append("</th>");
				}
			} else {
				for (int i = 1; i <= titleCols; i++) {
					queHtml.append("<th align=\"center\">-");
					queHtml.append("</th>");
				}
			}
			Map<Integer, TbProbColsdefine> colMap = colModel.get(colModel.size());
			for (int c = 1; c <= headerColNum; c++) {
				TbProbColsdefine col = colMap.get(c);
				if (col == null) {
					for (int t = 1; t <= colModel.size(); t++) {
						Map<Integer, TbProbColsdefine> tempMap = colModel.get(t);
						TbProbColsdefine tempCol = tempMap.get(c);
						if (tempCol != null && tempCol.getColType() != 0) {
							col = tempCol;
							break;
						}
					}
					if (col == null)
						continue;
				}
				queHtml.append("<td  align=\"center\">");
				String colID = "q" + queNum + "_" + c + "_" + rowIndex;
				if (col.getColType() == 1 || col.getColType() == 2 || col.getColType() == 3) {
					queHtml.append("<input name=\"" + colID + "\" id=\"" + colID
							+ "\" type=\"text\" maxlength=\"100\" class=\"ui-input-text\" style=\"overflow: hidden;\" ");
					if (null != sub.getMustAsk() && sub.getMustAsk() == 1) {// 如果必答，则需要加上验证
						queHtml.append(" zwx:valid=\"required\" zwx:errmsg=\"此题为必答题，请填写答案!\" ");
					}
					queHtml.append("/>");
				}
				/**
				 * 修订内容：日期填空样式修改
				 * 
				 * @MethodReviser rj,2017年11月8日,produceTableHtml
				 * 
				 */
				if (col.getColType() == 4) {
					queHtml.append("<div class=\"ui-input-text\" style=\"position:relative;\">");
					queHtml.append("<input type=\"text\" maxlength=\"100\" style=\"padding-left:30px;\" name=\"").append(colID)
							.append("\" ");
					queHtml.append(" id=\"").append(colID).append("\"  data-role=\"datebox\" readonly=\"readonly\"");
					if (null != sub.getMustAsk() && sub.getMustAsk() == 1) {// 如果必答，则需要加上验证
						queHtml.append(" zwx:valid=\"required\" zwx:errmsg=\"此题为必答题，请填写答案!\" ");
					}
					queHtml.append("/>");
					queHtml.append("<img src=\"/resources/images/que/date.png\"  style=\"position:absolute;top:10px;left:3px;\">");
					queHtml.append("</div>");
				}
				queHtml.append("</td>");
			}
			queHtml.append("</tr>");
			rowIndex++;
		}
		if (sub.getFkByTableId().getRowFixed() != 1) {
			queHtml.append("</tbody>");
		}
		queHtml.append("</table></div>");
	}
}
