package com.chis.modules.system.queProducer;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsProbExampool;
import com.chis.modules.system.enumn.GenHtmlType;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 模块描述：创建单选组件
 * 
 * <AUTHOR>
 * @createDate 2016年3月26日
 */
public class MakeSECompEvaluate extends MakeSimulateExamComponet {

	@Override
	public String makeCompHtml(TsProbExampool sub, GenHtmlType genHtmlType) {
		String htmlStr = null;
		switch (genHtmlType) {
			case WEB_HTML_PAGE:
				htmlStr = this.produceText(sub);
				break;
			case MOBILE_HTML_PAGE:
				break;
			default:
				break;
		}
		return htmlStr;
	}



	/**
	 * 生成文本填空组件
	 *
	 * @param sub
	 * @return
	 */
	private String produceText(TsProbExampool sub) {
		StringBuilder selectOneStr = new StringBuilder();
		if (null != sub && null != sub.getTsProPoolOpts()){
			String  queNum = sub.getQesCode();
			selectOneStr.append("<div class=\"div_question\" id=\"div")
					.append(queNum).append("\" ");
			selectOneStr.append(">");
			selectOneStr.append("<div class=\"div_title_question_all\">");
			selectOneStr.append("<div class=\"div_topic_question\">");
			selectOneStr.append("<b>")
					.append(sub.getShowCode() == null ? ""
							: sub.getShowCode()).append(" </b>");
			selectOneStr.append("</div>");

			selectOneStr.append("<div id=\"divTitle").append(queNum)
					.append("\" class=\"div_title_question\">");

			if (sub.getIsMulti() != null
					&& sub.getIsMulti() == 1
					&& sub.getTitleDesc().contains("_")) {
				String title = sub.getTitleDesc().replaceAll(" ",
						"&nbsp;");
				Matcher matcher = Pattern.compile("_+").matcher(title);
				int fillIndex = 1;
				while (matcher.find()) {
					String temp = title.substring(matcher.start(),
							matcher.end());
					title = title.replaceFirst(
							temp,
							createInput(queNum, fillIndex++,
									sub.getMustAsk(), temp.length()));
					matcher = Pattern.compile("_+").matcher(title);
				}
				selectOneStr.append(title.replaceAll("@", "_").replaceAll("\n",
						"<br/>"));
			} else {
				selectOneStr.append(sub.getTitleDesc().replaceAll(" ",
						"&nbsp;"));
			}

			if (null != sub.getMustAsk()
					&& sub.getMustAsk() == 1) {// 如果必答，则需要加上红星
				selectOneStr.append("<span style=\"color:red;\">&#160;*</span>");
			}
			selectOneStr.append("</div>");
			selectOneStr.append("<div style=\"clear:both;\"></div>");
			selectOneStr.append("</div>");

			selectOneStr.append(
							"<div class=\"div_table_radio_question\" id=\"divquestion")
					.append(queNum).append("\">");
			selectOneStr.append("<div class=\"div_table_clear_top\"></div>");

			if (!(sub.getIsMulti() != null
					&& sub.getIsMulti() == 1 && sub
					.getTitleDesc().contains("_"))) {
				selectOneStr.append(createInput(queNum, null,
						sub.getMustAsk(), null).replaceAll("@", "_"));
			}
			selectOneStr.append("<div style=\"clear:both;\"></div>");
			selectOneStr.append("<div class=\"div_table_clear_bottom\"></div>");
			selectOneStr.append("</div>");
			selectOneStr.append("<div class=\"errorMessage\"></div>");
			String otherDesc = sub.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				selectOneStr.append("<div class=\"div_ins_question\">提示：")
						.append(otherDesc).append("</div>");
			}
			selectOneStr.append("</div>");

		}
		return selectOneStr.toString();
	}

	private String createInput(String qesIndex, Integer fillIndex,
							   Integer mustAsk, Integer length) {
		StringBuilder input = new StringBuilder();
		input.append("<textarea   style=\"resize:none;outline:none;\" name=\"q")
				.append(qesIndex + (fillIndex == null ? "" : ("@" + fillIndex)))
				.append("\" ").append("\" rows=\"4\" cols=\"109\" maxlength=\"500\"");
		input.append(" id=\"q")
				.append(qesIndex + (fillIndex == null ? "" : ("@" + fillIndex)))
				.append(fillIndex == null ? "\" class=\"inputtext\" "
						: "\" class=\"underline\" ");
		if (null != mustAsk && mustAsk == 1) {// 如果必答，则需要加上验证
			input.append(" zwx:valid=\"required\" zwx:errmsg=\"此题为必答题，请填写答案!\" ");
		}
		input.append(" ></textarea>");
		return input.toString();
	}
}
