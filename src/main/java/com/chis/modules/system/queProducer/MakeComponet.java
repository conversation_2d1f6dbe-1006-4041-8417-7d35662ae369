package com.chis.modules.system.queProducer;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsProOpt;
import com.chis.modules.system.entity.TsProbSubject;
import com.chis.modules.system.enumn.GenHtmlType;
import com.chis.modules.system.web.IMakeComponent;

import java.util.HashMap;
import java.util.Map;

/**
 * 模块描述：生成组件代码基类
 * 
 * <AUTHOR>
 * @createDate 2016年3月26日
 */
public abstract class MakeComponet implements IMakeComponent {

	public abstract String makeCompHtml(TsProbSubject tsProbSubject,
			GenHtmlType genHtmlType);

	/**
	 * 将生成的NULl转换成""字符串
	 */
	@Override
	public String makeComponent(TsProbSubject tsProbSubject,
			GenHtmlType genHtmlType) {
		String makeCompHtml = null;
		// 如果题目类型Id不为空，则生成html代码，否则不执行
		if (null != tsProbSubject) {
			makeCompHtml = makeCompHtml(tsProbSubject, genHtmlType);
		}
		if (null == makeCompHtml) {
			makeCompHtml = "";
		}
		return makeCompHtml;
	}

	/**
	 * 根据题目跳转类型生成跳转标识(手机)
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	protected String produceJumpOrRelaMobile(TsProbSubject tsProbSubject) {
		StringBuilder retVal = new StringBuilder();
		if (null != tsProbSubject) {
			Integer jumpType = tsProbSubject.getJumpType();
			if (null != jumpType
					&& StringUtils.isNotBlank(tsProbSubject.getJumpQuestCode())) {
				if (jumpType.intValue() == 1) { // 1：跳转到下面题目
					retVal.append(" hasjump=\"1\" anyjump=\"0\" ");
				} else if (jumpType.intValue() == 2) {// 2：依赖上面的题目
					retVal.append(" relation=\"")
							.append(tsProbSubject.getJumpQuestCode())
							.append("\" style=\"display:none;\" ");
				}
			}
		}
		return retVal.toString();
	}

	/**
	 * 根据题目跳转类型生成跳转标识
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	protected String produceJumpOrRela(TsProbSubject tsProbSubject) {
		StringBuilder retVal = new StringBuilder();
		if (null != tsProbSubject) {
			Integer jumpType = tsProbSubject.getJumpType();
			if (null != jumpType
					&& StringUtils.isNotBlank(tsProbSubject.getJumpQuestCode())) {
				if (jumpType.intValue() == 1) { // 1：跳转到下面题目
					retVal.append(" jumpto=\"")
							.append(tsProbSubject.getJumpQuestCode())
							.append("\" ");
				} else if (jumpType.intValue() == 2) {// 2：依赖上面的题目
					retVal.append(" relation=\"")
							.append(tsProbSubject.getJumpQuestCode())
							.append("\" style=\"display:none;\" ");
				}
			}
		}
		return retVal.toString();
	}

	/**
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	protected String produceMutex(TsProbSubject tsProbSubject) {
		if (tsProbSubject.getProOptList() != null
				&& tsProbSubject.getProOptList().size() > 0) {
			StringBuilder sb = new StringBuilder();
			for (TsProOpt opt : tsProbSubject.getProOptList()) {
				if (opt.getIsAlter() != null && 1 == opt.getIsAlter()) {
					sb.append(",").append(opt.getOptionValue());
				}
			}
			if (sb.length() > 0) {
				return "mutex=\"" + sb.substring(1) + "\" ";
			}
		}
		return null;
	}

	/**
	 * <p>方法描述： 通过脚本获取组件事件 </p>
	 * pw 2023/10/19
	 **/
	public Map<String,String> findEventByScript (String execScript) {
		Map<String,String> resultMap = new HashMap<>();
		if(StringUtils.isBlank(execScript)){
			return resultMap;
		}
		String[] scriptArr = execScript.split("#@#@#@#@#");
		if(scriptArr.length == 0){
			return resultMap;
		}
		for(String script : scriptArr){
			String tmp = script.trim();
			String eventStr = tmp.substring(0,tmp.indexOf(";"));
			if(eventStr.indexOf(":") == -1){
				return resultMap;
			}
			String[] tmpArr = eventStr.split(":");
			if(tmpArr.length != 2 || StringUtils.isBlank(tmpArr[0]) || StringUtils.isBlank(tmpArr[1])){
				return resultMap;
			}
			resultMap.put(tmpArr[0].trim(), tmpArr[1].trim());
		}
		return resultMap;
	}
}
