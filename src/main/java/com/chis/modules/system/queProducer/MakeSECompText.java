package com.chis.modules.system.queProducer;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsProbExampool;
import com.chis.modules.system.entity.TsProbSubject;
import com.chis.modules.system.enumn.GenHtmlType;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 *  <p>类描述：创建文本只读组件</p>
 * @ClassAuthor hsj 2021/8/25 10:26
 */
public class MakeSECompText extends MakeSimulateExamComponet {
	@Override
	public String makeCompHtml(TsProbExampool sub, GenHtmlType genHtmlType) {
		String htmlStr = null;
		switch (genHtmlType) {
			case WEB_HTML_PAGE:
				htmlStr = this.produceText(sub);
				break;
			case MOBILE_HTML_PAGE:
//				htmlStr = this.produceTextMobile(sub);
				break;
			default:
				break;
		}
		return htmlStr;
	}
	/**
	 *  <p>方法描述：生成文本只读显示（手机）</p>
	 * @MethodAuthor hsj
	 */
	private String produceTextMobile(TsProbSubject tsProbSubject) {
		StringBuilder htmlStr = new StringBuilder();
		if (null != tsProbSubject) {
			Integer queNum = tsProbSubject.getNum();
			htmlStr.append("<div class=\"field ui-field-contain\" id=\"div")
					.append(queNum).append("\" type=\"1\" topic=\"")
					.append(queNum).append("\"  data-role=\"fieldcontain\" ");
			htmlStr.append(" req=\"0\" ");
			htmlStr.append(">");

			htmlStr.append("<div class=\"field-label\">");
			htmlStr.append(
					tsProbSubject.getShowCode() == null ? "" : tsProbSubject
							.getShowCode()).append(" ");
			String title = tsProbSubject.getTitleDesc().replaceAll(" ",
					"&nbsp;");
			Matcher matcher = Pattern.compile("_+").matcher(title);
			int fillIndex = 1;
			while (matcher.find()) {
				String temp = title.substring(matcher.start(),
						matcher.end());
				title = title.replaceFirst(
						temp,
						createInputMobile(tsProbSubject.getNum(), fillIndex++, temp.length()));
				matcher = Pattern.compile("_+").matcher(title);
			}
			htmlStr.append(title.replaceAll("@", "_").replaceAll("\n",
					"<br/>"));
			htmlStr.append("</div>");

			htmlStr.append("<div class=\"errorMessage\"></div>");
			String otherDesc = tsProbSubject.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				htmlStr.append("<div class=\"qinsert\">提示：")
						.append(otherDesc).append("</div>");
			}
			htmlStr.append("</div>");
		}
		return htmlStr.toString();
	}

	/**
	 *  <p>方法描述：生成文本只读显示（web）</p>
	 * @MethodAuthor hsj
	 */
	private String produceText(TsProbExampool sub) {
		StringBuilder selectOneStr = new StringBuilder();
		if (null != sub && null != sub.getTsProPoolOpts()){
			selectOneStr.append("<div class=\"div_question\" id=\"div")
					.append(sub.getQesCode()).append("\" ");
			selectOneStr.append(">");
			selectOneStr.append("<div class=\"div_title_question_all\">");
			selectOneStr.append("<div class=\"div_topic_question\">");
			selectOneStr.append("<b>").append(sub.getShowCode() == null ? ""
					: sub.getShowCode())
					.append(" </b>");
			selectOneStr.append("</div>");

			selectOneStr.append("<div id=\"divTitle").append(sub.getQesCode())
					.append("\" class=\"div_title_question\">");
			String title =sub.getTitleDesc().replaceAll(" ",
					"&nbsp;");
			Matcher matcher = Pattern.compile("_+").matcher(title);
			int fillIndex = 1;
			while (matcher.find()) {
				String temp = title.substring(matcher.start(),
						matcher.end());
				title = title.replaceFirst(
						temp,
						createInput(sub.getQesCode(), fillIndex++, temp.length()));
				matcher = Pattern.compile("_+").matcher(title);
			}
			selectOneStr.append(title.replaceAll("@", "_").replaceAll("\n",
					"<br/>"));
			//判断是否存在附件
			String annexAddr = sub.getAnnexAddr();
			if(StringUtils.isNotBlank(annexAddr)){
				selectOneStr.append(createImg(sub.getQesCode(),"/webFile"+annexAddr));
			}
			selectOneStr.append("</div>");
			selectOneStr.append("<div style=\"clear:both;\"></div>");
			selectOneStr.append("</div>");

			selectOneStr.append(
					"<div class=\"div_table_radio_question read\" id=\"divquestion")
					.append(sub.getQesCode()).append("\">");
			selectOneStr.append("<div class=\"div_table_clear_top\"></div>");

			selectOneStr.append("<div style=\"clear:both;\"></div>");
			selectOneStr.append("<div class=\"div_table_clear_bottom\"></div>");
			selectOneStr.append("</div>");
			selectOneStr.append("<div class=\"errorMessage\"></div>");
			String otherDesc = sub.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				selectOneStr.append("<div class=\"qinsert\">提示：")
						.append(otherDesc).append("</div>");
			}
			selectOneStr.append("</div>");

		}
		return selectOneStr.toString();
	}

    /**
     * web端图片生成
	 * @param num
     * @param annexAddr
	 */
	private String createImg(String  num, String annexAddr) {
		StringBuilder imgStr = new StringBuilder();
		imgStr.append("<br><br><div class=\"div_title_question\"> ");
		imgStr.append("<img style =\"width:100%;height:100%;\"  src='").append(annexAddr).append("' />");
		imgStr.append("</div> ");
		return imgStr.toString();
	}

	private String createInput(String qesIndex, Integer fillIndex, Integer length) {
		StringBuilder input = new StringBuilder();
		input.append("<input type=\"text\" readonly name=\"q")
				.append(qesIndex + (fillIndex == null ? "" : ("@" + fillIndex)))
				.append("\" ");
		if (length != null) {
			input.append("style=\"width:").append(21 * length).append("px\" ");
		}
		input.append(" id=\"q")
				.append(qesIndex + (fillIndex == null ? "" : ("@" + fillIndex)))
				.append( "\" class=\"underline\" ");
		input.append(" />");
		return input.toString();
	}

	private String createInputMobile(Integer qesIndex, Integer fillIndex,Integer length) {
		StringBuilder input = new StringBuilder();
		input.append("<input type=\"text\" readonly name=\"q")
				.append(qesIndex + (fillIndex == null ? "" : ("@" + fillIndex)))
				.append("\" ");
		if (length != null) {
			input.append("style=\"line-height:24px;padding:2px 4px;margin-left:5px;margin-right:3px;width:").append(21 * length).append("px\" ");
		}
		input.append(" id=\"q")
				.append(qesIndex + (fillIndex == null ? "" : ("@" + fillIndex)))
				.append( "\" class=\"ui-input-text\" ");
		input.append(" />");
		return input.toString();
	}


}
