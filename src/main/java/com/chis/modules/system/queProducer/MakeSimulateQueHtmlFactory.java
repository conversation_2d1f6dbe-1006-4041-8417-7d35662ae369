package com.chis.modules.system.queProducer;

import com.chis.modules.system.enumn.QuestType;
import com.chis.modules.system.interfaces.IMakeSimulateExamComponet;

/**
 * 模块描述：根据组件类型创建不同的对象
 * 
 * <AUTHOR>
 * @createDate 2016年3月26日
 */
public class MakeSimulateQueHtmlFactory {

	public IMakeSimulateExamComponet makeComponetFactory(QuestType questType) {
		IMakeSimulateExamComponet iMC = null;
		switch (questType) {
		case SELECT_ONE:// 单选题
			iMC = new MakeSECompSelectO();
			break;
		case SELECT_MANY:// 多项题
			iMC = new MakeSECompSelectM();
			break;		
		case TRUE_OR_FALSES://是非题
			iMC = new MakeSECompSelectO();
			break;
		case READONLY://只读题
			iMC = new MakeSECompText();
			break;
		case FILL_BLANK://文本填空题
			iMC = new MakeSECompFillB();
			break;
		case INTEGER_FILL_BLANK://整数填空
			iMC = new MakeSECompIntFillB();
			break;
		case EVALUATE://大文本题
			iMC = new MakeSECompEvaluate();
			break;
		case SCORE://评分
			iMC = new MakeSECompScore();
			break;
		case SHOW_SCORE: // 显示得分和参考答案
			iMC = new MakeSECompShowScore();
			break;
		default:
			break;
		}
		return iMC;

	}

}
