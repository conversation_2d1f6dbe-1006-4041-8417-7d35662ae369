package com.chis.modules.system.queProducer;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsProbSubject;
import com.chis.modules.system.enumn.GenHtmlType;
import org.springframework.util.CollectionUtils;

/**
 * 模块描述：创建文本填空组件
 * 
 * <AUTHOR>
 * @createDate 2016年3月26日
 */
public class MakeCompFillB extends MakeComponet {
	@Override
	public String makeCompHtml(TsProbSubject tsProbSubject,
			GenHtmlType genHtmlType) {
		String htmlStr = null;
		switch (genHtmlType) {
		case WEB_HTML_PAGE:
			htmlStr = this.produceText(tsProbSubject);
			break;
		case MOBILE_HTML_PAGE:
			htmlStr = this.produceTextMobile(tsProbSubject);
			break;
		default:
			break;
		}
		return htmlStr;
	}

	/**
	 * 生成文本填空组件（手机）
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	private String produceTextMobile(TsProbSubject tsProbSubject) {
		StringBuilder htmlStr = new StringBuilder();
		if (null != tsProbSubject) {
			Map<String,String> pageEventMap = this.findEventByScript(null == tsProbSubject.getPool() ? null : tsProbSubject.getPool().getExecScript());
			Integer queNum = tsProbSubject.getNum();
			htmlStr.append("<div class=\"field ui-field-contain\" id=\"div")
					.append(queNum).append("\" type=\"1\" topic=\"")
					.append(queNum).append("\"  data-role=\"fieldcontain\" ");
			if (null != tsProbSubject.getMustAsk()
					&& tsProbSubject.getMustAsk() == 1) {// 如果必答，则需要加上红星
				htmlStr.append(" req=\"1\" ");
			} else {
				htmlStr.append(" req=\"0\" ");
			}
			// 加入需要跳转的代码
			String jumpOrRela = this.produceJumpOrRelaMobile(tsProbSubject);
			if (StringUtils.isNotBlank(jumpOrRela)) {
				htmlStr.append(jumpOrRela);
			}
			htmlStr.append(">");

			htmlStr.append("<div class=\"field-label\">");
			htmlStr.append(
					tsProbSubject.getShowCode() == null ? "" : tsProbSubject
							.getShowCode()).append(" ");
			if (tsProbSubject.getIsMulti() != null
					&& tsProbSubject.getIsMulti() == 1
					&& tsProbSubject.getTitleDesc().contains("_")) {
				String title = tsProbSubject.getTitleDesc().replaceAll(" ",
						"&nbsp;");
				Matcher matcher = Pattern.compile("_+").matcher(title);
				int fillIndex = 1;
				while (matcher.find()) {
					String temp = title.substring(matcher.start(),
							matcher.end());
					title = title.replaceFirst(
							temp,
							createInputMobile(tsProbSubject.getNum(), fillIndex++,
									tsProbSubject.getMustAsk(), temp.length()));
					matcher = Pattern.compile("_+").matcher(title);
				}
				htmlStr.append(title.replaceAll("@", "_").replaceAll("\n",
						"<br/>"));
			} else {
				htmlStr.append(tsProbSubject.getTitleDesc().replaceAll(" ",
						"&nbsp;"));
			}
			if (null != tsProbSubject.getMustAsk()
					&& tsProbSubject.getMustAsk() == 1) {// 如果必答，则需要加上红星
				htmlStr.append("<span style=\"color:red;\">&#160;*</span>");
			}
			htmlStr.append("</div>");

			if (!(tsProbSubject.getIsMulti() != null
					&& tsProbSubject.getIsMulti() == 1 && tsProbSubject
					.getTitleDesc().contains("_"))) {
				htmlStr.append("<div class=\"ui-input-text\">");
				htmlStr.append(createInput(queNum, null,
						tsProbSubject.getMustAsk(), null, pageEventMap).replaceAll("@", "_"));
				htmlStr.append("</div>");
			}
			htmlStr.append("<div class=\"errorMessage\"></div>");
			String otherDesc = tsProbSubject.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				htmlStr.append("<div class=\"div_ins_question\">提示：")
						.append(otherDesc).append("</div>");
			}
			htmlStr.append("</div>");
		}
		return htmlStr.toString();
	}

	/**
	 * 生成文本填空组件
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	private String produceText(TsProbSubject tsProbSubject) {
		StringBuilder htmlStr = new StringBuilder();
		if (null != tsProbSubject) {
			Map<String,String> pageEventMap = this.findEventByScript(null == tsProbSubject.getPool() ? null : tsProbSubject.getPool().getExecScript());
			Integer queNum = tsProbSubject.getNum();
			htmlStr.append("<div class=\"div_question\" id=\"div")
					.append(queNum).append("\" ");
			// 加入需要跳转的代码
			String jumpOrRela = this.produceJumpOrRela(tsProbSubject);
			if (StringUtils.isNotBlank(jumpOrRela)) {
				htmlStr.append(jumpOrRela);
			}
			htmlStr.append(">");
			htmlStr.append("<div class=\"div_title_question_all\">");
			htmlStr.append("<div class=\"div_topic_question\">");
			htmlStr.append("<b>")
					.append(tsProbSubject.getShowCode() == null ? ""
							: tsProbSubject.getShowCode()).append(" </b>");
			htmlStr.append("</div>");

			htmlStr.append("<div id=\"divTitle").append(queNum)
					.append("\" class=\"div_title_question\">");

			if (tsProbSubject.getIsMulti() != null
					&& tsProbSubject.getIsMulti() == 1
					&& tsProbSubject.getTitleDesc().contains("_")) {
				String title = tsProbSubject.getTitleDesc().replaceAll(" ",
						"&nbsp;");
				Matcher matcher = Pattern.compile("_+").matcher(title);
				int fillIndex = 1;
				while (matcher.find()) {
					String temp = title.substring(matcher.start(),
							matcher.end());
					title = title.replaceFirst(
							temp,
							createInput(tsProbSubject.getNum(), fillIndex++,
									tsProbSubject.getMustAsk(), temp.length(), pageEventMap));
					matcher = Pattern.compile("_+").matcher(title);
				}
				htmlStr.append(title.replaceAll("@", "_").replaceAll("\n",
						"<br/>"));
			} else {
				htmlStr.append(tsProbSubject.getTitleDesc().replaceAll(" ",
						"&nbsp;"));
			}

			if (null != tsProbSubject.getMustAsk()
					&& tsProbSubject.getMustAsk() == 1) {// 如果必答，则需要加上红星
				htmlStr.append("<span style=\"color:red;\">&#160;*</span>");
			}
			htmlStr.append("</div>");
			htmlStr.append("<div style=\"clear:both;\"></div>");
			htmlStr.append("</div>");

			htmlStr.append(
					"<div class=\"div_table_radio_question\" id=\"divquestion")
					.append(queNum).append("\">");
			htmlStr.append("<div class=\"div_table_clear_top\"></div>");

			if (!(tsProbSubject.getIsMulti() != null
					&& tsProbSubject.getIsMulti() == 1 && tsProbSubject
					.getTitleDesc().contains("_"))) {
				htmlStr.append(createInput(queNum, null,
						tsProbSubject.getMustAsk(), null, pageEventMap).replaceAll("@", "_"));
			}

			if (StringUtils.isNotBlank(tsProbSubject.getQuestUnit())) {
				htmlStr.append(" ").append(tsProbSubject.getQuestUnit());
			}
			htmlStr.append("<div style=\"clear:both;\"></div>");
			htmlStr.append("<div class=\"div_table_clear_bottom\"></div>");
			htmlStr.append("</div>");
			htmlStr.append("<div class=\"errorMessage\"></div>");
			String otherDesc = tsProbSubject.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				htmlStr.append("<div class=\"div_ins_question\">提示：")
						.append(otherDesc).append("</div>");
			}
			htmlStr.append("</div>");

		}
		return htmlStr.toString();
	}

	private String createInput(Integer qesIndex, Integer fillIndex,
			Integer mustAsk, Integer length, Map<String,String> pageEventMap) {
		StringBuilder input = new StringBuilder();
		input.append("<input type=\"text\" maxlength=\"100\" name=\"q")
				.append(qesIndex + (fillIndex == null ? "" : ("@" + fillIndex)))
				.append("\" ");
		if (length != null) {
			input.append("style=\"width:").append(21 * length).append("px\" ");
		}
		input.append(" id=\"q")
				.append(qesIndex + (fillIndex == null ? "" : ("@" + fillIndex)))
				.append(fillIndex == null ? "\" class=\"inputtext\" "
						: "\" class=\"underline\" ");
		if (null != mustAsk && mustAsk == 1) {// 如果必答，则需要加上验证
			input.append(" zwx:valid=\"required\" zwx:errmsg=\"此题为必答题，请填写答案!\" ");
		}
		//加入页面脚本事件
		if(!CollectionUtils.isEmpty(pageEventMap)){
			for(Map.Entry<String,String> mapEntity : pageEventMap.entrySet()){
				input.append(" ").append(mapEntity.getKey()).append("=\"").append(mapEntity.getValue()).append("\" ");
			}
		}
		input.append(" />");
		return input.toString();
	}
	
	private String createInputMobile(Integer qesIndex, Integer fillIndex,
			Integer mustAsk, Integer length) {
		StringBuilder input = new StringBuilder();
		input.append("<input type=\"text\" maxlength=\"100\" name=\"q")
				.append(qesIndex + (fillIndex == null ? "" : ("@" + fillIndex)))
				.append("\" ");
		if (length != null) {
			input.append("style=\"line-height:24px;padding:2px 4px;margin-left:5px;margin-right:3px;width:").append(21 * length).append("px\" ");
		}
		input.append(" id=\"q")
				.append(qesIndex + (fillIndex == null ? "" : ("@" + fillIndex)))
				.append(fillIndex == null ? "\" class=\"inputtext\" "
						: "\" class=\"ui-input-text\" ");
		if (null != mustAsk && mustAsk == 1) {// 如果必答，则需要加上验证
			input.append(" zwx:valid=\"required\" zwx:errmsg=\"此题为必答题，请填写答案!\" ");
		}
		input.append(" />");
		return input.toString();
	}
}
