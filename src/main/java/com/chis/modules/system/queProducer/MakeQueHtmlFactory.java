package com.chis.modules.system.queProducer;

import com.chis.modules.system.enumn.QuestType;
import com.chis.modules.system.web.IMakeComponent;

/**
 * 模块描述：根据组件类型创建不同的对象
 * 
 * <AUTHOR>
 * @createDate 2016年3月26日
 */
public class MakeQueHtmlFactory {

	public IMakeComponent makeComponetFactory(QuestType questType) {
		IMakeComponent iMC = null;
		switch (questType) {
		case SELECT_ONE:// 单选题
			iMC = new MakeCompSelectO();
			break;
		case SELECT_MANY:// 多项题
			iMC = new MakeCompSelectM();
			break;
		case SLIDE:// 滑动题
			iMC = new MakeCompSlipe();
			break;
		case EVALUATE:// 评价题
			iMC = new MakeCompEvaluate();
			break;
		case FILL_BLANK:// 文本填空题
			iMC = new MakeCompFillB();
			break;
		case TITLE:// 标题
			iMC = new MakeCompTitle();
			break;
		case DATE_FILL_BLANK:// 日期填空题
			iMC = new MakeCompDateFillB();
			break;
		case INTEGER_FILL_BLANK:// 整数填空
			iMC = new MakeCompIntFillB();
			break;
		case PULL_DOWN_SELECT_ONE:// 下拉单选
			iMC = new MakeCompPullDownSelectO();
			break;
		case NUMBER_FILL_BLANK:// 数字填空题
			iMC = new MakeCompNumFillB();
			break;
		case TRUE_OR_FALSES:
			iMC = new MakeCompSelectO();
			break;
		case TABLE:
			iMC = new MakeCompTable();
			break;
		case READONLY://只读显示
			iMC = new MakeCompText();
			break;
		default:
			break;
		}
		return iMC;

	}

}
