package com.chis.modules.system.queProducer;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsProPoolOpt;
import com.chis.modules.system.entity.TsProbExampool;
import com.chis.modules.system.entity.TsProbSubject;
import com.chis.modules.system.enumn.GenHtmlType;
import com.chis.modules.system.interfaces.IMakeSimulateExamComponet;

/**
 * 模拟试题生成基类
 * <AUTHOR>
 * @createDate 2016年8月8日
 */
public abstract class MakeSimulateExamComponet implements IMakeSimulateExamComponet{

	public abstract String makeCompHtml(TsProbExampool pool,
			GenHtmlType genHtmlType);

	/**
	 * 将生成的NULl转换成""字符串
	 */
	@Override
	public String makeComponent(TsProbExampool pool,
			GenHtmlType genHtmlType) {
		String makeCompHtml = null;
		// 如果题目类型Id不为空，则生成html代码，否则不执行
		if (null != pool) {
			makeCompHtml = makeCompHtml(pool, genHtmlType);
		}
		if (null == makeCompHtml) {
			makeCompHtml = "";
		}
		return makeCompHtml;
	}

	/**
	 * 根据题目跳转类型生成跳转标识(微信)
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	protected String produceJumpOrRelaMobile(TsProbSubject tsProbSubject) {
		StringBuilder retVal = new StringBuilder();
		if (null != tsProbSubject) {
			Integer jumpType = tsProbSubject.getJumpType();
			if (null != jumpType
					&& StringUtils.isNotBlank(tsProbSubject.getJumpQuestCode())) {
				if (jumpType.intValue() == 1) { // 1：跳转到下面题目
					retVal.append(" hasjump=\"1\" anyjump=\"0\" ");
				} else if (jumpType.intValue() == 2) {// 2：依赖上面的题目
					retVal.append(" relation=\"")
							.append(tsProbSubject.getJumpQuestCode())
							.append("\" style=\"display:none;\" ");
				}
			}
		}
		return retVal.toString();
	}

	/**
	 * 根据题目跳转类型生成跳转标识
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	protected String produceJumpOrRela(TsProbSubject tsProbSubject) {
		StringBuilder retVal = new StringBuilder();
		if (null != tsProbSubject) {
			Integer jumpType = tsProbSubject.getJumpType();
			if (null != jumpType
					&& StringUtils.isNotBlank(tsProbSubject.getJumpQuestCode())) {
				if (jumpType.intValue() == 1) { // 1：跳转到下面题目
					retVal.append(" jumpto=\"")
							.append(tsProbSubject.getJumpQuestCode())
							.append("\" ");
				} else if (jumpType.intValue() == 2) {// 2：依赖上面的题目
					retVal.append(" relation=\"")
							.append(tsProbSubject.getJumpQuestCode())
							.append("\" style=\"display:none;\" ");
				}
			}
		}
		return retVal.toString();
	}

	/**
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	protected String produceMutex(TsProbExampool sub) {
		if (sub.getTsProPoolOpts() != null
				&& sub.getTsProPoolOpts().size() > 0) {
			StringBuilder sb = new StringBuilder();
			for (TsProPoolOpt opt : sub.getTsProPoolOpts()) {
				if (opt.getIsAlter() != null && 1 == opt.getIsAlter()) {
					sb.append(",").append(opt.getOptionValue());
				}
			}
			if (sb.length() > 0) {
				return "mutex=\"" + sb.substring(1) + "\" ";
			}
		}
		return null;
	}
}
