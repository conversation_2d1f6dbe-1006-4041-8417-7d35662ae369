package com.chis.modules.system.queProducer;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsProbExampool;
import com.chis.modules.system.enumn.GenHtmlType;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 创建评分组件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/07/26
 */
public class MakeSECompScore extends MakeSimulateExamComponet {

    @Override
    public String makeCompHtml(TsProbExampool pool, GenHtmlType genHtmlType) {
        String htmlStr = null;
        if (genHtmlType == GenHtmlType.WEB_HTML_PAGE) {
            htmlStr = this.produceTextInteger(pool);
        }
        return htmlStr;
    }

    private String produceTextInteger(TsProbExampool pool) {
        StringBuilder selectOneStr = new StringBuilder();
        if (null != pool && null != pool.getTsProPoolOpts()) {
            String queNum = pool.getQesCode();
            selectOneStr.append("<div class=\"div_question div_question_score\" id=\"div_score_").append(queNum).append("\">");
            selectOneStr.append("   <div class=\"div_title_question_all score\">");
            selectOneStr.append("       <div ").append("class=\"div_score\">");
            selectOneStr.append("           <div style=\"color:green;min-width: 75px;\">参考答案：</div>");
            selectOneStr.append("           <div>").append(StringUtils.objectToString(pool.getAnsDesc())).append("</div>");
            selectOneStr.append("       </div>");
            selectOneStr.append("       <div style=\"margin: 10px auto 18px;\" class=\"div_score\">");
            selectOneStr.append("           <div class=\"div_score\" >得分：</div>");
            selectOneStr.append(createInput(pool));
            selectOneStr.append("       </div>");
            selectOneStr.append("       <div style=\"clear:both;\"></div>");
            selectOneStr.append("   </div>");
            selectOneStr.append("<div class=\"errorMessage errorMessageScore\"></div>");
            selectOneStr.append("</div>");
        }
        return selectOneStr.toString();
    }

    private String createInput(TsProbExampool pool) {
        StringBuilder input = new StringBuilder();
        String queNum = pool.getQesCode();
        input.append("<div><input style=\"width:120px;\" ")
                .append(pool.getEditScore() ? "class=\"score_input\"  " : "")
                .append(" type=\"number\" step=\"any\" ");
        if (pool.getEditScore()) {
            input.append("zwx:max=\"").append(pool.getOptionScore()).append("\" zwx:min=\"0\" ");
        }
        input.append("name=\"divScore").append(queNum).append("\" id=\"divScore").append(queNum).append("\" value=\"")
                .append(StringUtils.objectToString(pool.getScore())).append("\"/></div>")
                .append(pool.getEditScore() ? "<span style=\"color:red;\">&#160;*</span>":"");
        return input.toString();
    }
}
