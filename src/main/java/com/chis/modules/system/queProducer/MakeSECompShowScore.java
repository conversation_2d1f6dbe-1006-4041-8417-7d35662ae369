package com.chis.modules.system.queProducer;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsProbExampool;
import com.chis.modules.system.enumn.GenHtmlType;

/**
 * <p>类描述：显示得分和参考答案组件 </p>
 * pw 2024/10/10
 **/
public class MakeSECompShowScore extends MakeSimulateExamComponet {

    @Override
    public String makeCompHtml(TsProbExampool pool, GenHtmlType genHtmlType) {
        String htmlStr = null;
        if (genHtmlType == GenHtmlType.WEB_HTML_PAGE) {
            htmlStr = this.produceTextInteger(pool);
        }
        return htmlStr;
    }

    private String produceTextInteger(TsProbExampool pool) {
        StringBuilder selectOneStr = new StringBuilder();
        String queNum = pool.getQesCode();
        selectOneStr.append("<div class=\"div_question div_question_score\" id=\"div_score_").append(queNum).append("\">");
        selectOneStr.append("   <div class=\"div_title_question_all score\">");
        selectOneStr.append("       <div ").append("class=\"div_score\">");
        selectOneStr.append("           <div style=\"color:green;min-width: 75px;\">参考答案：</div>");
        selectOneStr.append("           <div>").append(StringUtils.objectToString(pool.getAnsDesc())).append("</div>");
        selectOneStr.append("       </div>");
        selectOneStr.append("       <div style=\"margin: 10px auto 18px;\" class=\"div_score\">");
        selectOneStr.append("           <div class=\"div_score\" >得分：</div>");
        selectOneStr.append(createInput(pool));
        selectOneStr.append("       </div>");
        selectOneStr.append("       <div style=\"clear:both;\"></div>");
        selectOneStr.append("   </div>");
        selectOneStr.append("<div class=\"errorMessage errorMessageScore\"></div>");
        selectOneStr.append("</div>");
        return selectOneStr.toString();
    }

    private String createInput(TsProbExampool pool) {
        StringBuilder input = new StringBuilder();
        String queNum = pool.getQesCode();
        input.append("<div><input style=\"width:120px;\" ")
                .append(" type=\"number\" step=\"any\" ");
        input.append("name=\"divScore").append(queNum).append("\" id=\"divScore").append(queNum).append("\" value=\"")
                .append(StringUtils.objectToString(pool.getScore())).append("\"/></div>");
        return input.toString();
    }
}
