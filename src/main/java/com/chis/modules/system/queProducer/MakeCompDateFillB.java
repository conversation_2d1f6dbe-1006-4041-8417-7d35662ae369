package com.chis.modules.system.queProducer;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsProbSubject;
import com.chis.modules.system.enumn.GenHtmlType;
import org.springframework.util.CollectionUtils;

import java.util.Map;

/**
 * 模块描述：创建单选组件
 * 
 * <AUTHOR>
 * @createDate 2016年3月26日
 */
public class MakeCompDateFillB extends MakeComponet {

	@Override
	public String makeCompHtml(TsProbSubject tsProbSubject, GenHtmlType genHtmlType) {
		String htmlStr = null;
		switch (genHtmlType) {
		case WEB_HTML_PAGE:
			htmlStr = this.produceDate(tsProbSubject);
			break;
		case MOBILE_HTML_PAGE:
			htmlStr = this.produceDateMobile(tsProbSubject);
			break;
		default:
			break;
		}
		return htmlStr;
	}

	/**
	 * 生成日期填空组件（手机）
	 * 
	 * @param tsProbSubject
	 * @return
	 * 
	 *         修订内容：移动版组件修改
	 * 
	 * @MethodReviser rj,2017年11月11日,produceDateMobile
	 * 
	 */
	private String produceDateMobile(TsProbSubject tsProbSubject) {
		StringBuilder htmlStr = new StringBuilder();
		if (null != tsProbSubject) {
			Integer queNum = tsProbSubject.getNum();
			htmlStr.append("<div class=\"field ui-field-contain\" id=\"div").append(queNum)
					.append("\" type=\"1\" topic=\"").append(queNum).append("\"")
					.append(" data-role=\"fieldcontain\" ");
			if (null != tsProbSubject.getMustAsk() && tsProbSubject.getMustAsk() == 1) {// 如果必答，则需要加上红星
				htmlStr.append(" req=\"1\" ");
			} else {
				htmlStr.append(" req=\"0\" ");
			}
			// 加入需要跳转的代码
			String jumpOrRela = this.produceJumpOrRela(tsProbSubject);
			if (StringUtils.isNotBlank(jumpOrRela)) {
				htmlStr.append(jumpOrRela);
			}
			htmlStr.append(">");

			htmlStr.append("<div class=\"field-label\">");
			htmlStr.append(tsProbSubject.getShowCode() == null ? "" : tsProbSubject.getShowCode()).append(" ")
					.append(tsProbSubject.getTitleDesc());
			if (null != tsProbSubject.getMustAsk() && tsProbSubject.getMustAsk() == 1) {// 如果必答，则需要加上红星
				htmlStr.append("<span style=\"color:red;\">&#160;*</span>");
			}
			htmlStr.append("</div>");
			htmlStr.append("<div class=\"ui-input-text\" style=\"position:relative;\">");
			htmlStr.append("<input type=\"text\" maxlength=\"100\" style=\"padding-left:30px;\" name=\"q").append(queNum).append("\" ");
			htmlStr.append(" id=\"q").append(queNum).append("\"  data-role=\"datebox\" readonly=\"readonly\" >");
			htmlStr.append("<img src=\"/resources/images/que/date.png\"  style=\"position:absolute;top:3px;left:3px;\">");
			htmlStr.append("</div>");
			htmlStr.append("<div class=\"errorMessage\"></div>");
			String otherDesc = tsProbSubject.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				htmlStr.append("<div class=\"div_ins_question\">提示：").append(otherDesc).append("</div>");
			}
			htmlStr.append("</div>");
		}
		return htmlStr.toString();
	}

	/**
	 * 生成日期填空组件
	 * 
	 * @param tsProbSubject
	 * @return
	 */
	private String produceDate(TsProbSubject tsProbSubject) {
		StringBuilder htmlStr = new StringBuilder();
		if (null != tsProbSubject) {
			Integer queNum = tsProbSubject.getNum();
			htmlStr.append("<div class=\"div_question\" id=\"div").append(queNum).append("\" ");
			// 加入需要跳转的代码
			String jumpOrRela = this.produceJumpOrRela(tsProbSubject);
			if (StringUtils.isNotBlank(jumpOrRela)) {
				htmlStr.append(jumpOrRela);
			}
			htmlStr.append(">");
			htmlStr.append("<div class=\"div_title_question_all\">");
			htmlStr.append("<div class=\"div_topic_question\">");
			htmlStr.append("<b>").append(tsProbSubject.getShowCode() == null ? "" : tsProbSubject.getShowCode())
					.append(" </b>");
			htmlStr.append("</div>");

			htmlStr.append("<div id=\"divTitle").append(queNum).append("\" class=\"div_title_question\">");
			htmlStr.append(tsProbSubject.getTitleDesc());
			if (null != tsProbSubject.getMustAsk() && tsProbSubject.getMustAsk() == 1) {// 如果必答，则需要加上红星
				htmlStr.append("<span style=\"color:red;\">&#160;*</span>");
			}
			htmlStr.append("</div>");
			htmlStr.append("<div style=\"clear:both;\"></div>");
			htmlStr.append("</div>");
			htmlStr.append("<div class=\"div_table_radio_question\" id=\"divquestion").append(queNum).append("\">");
			htmlStr.append("<div class=\"div_table_clear_top\"></div>");

			htmlStr.append("<input type=\"text\" maxlength=\"100\" name=\"q").append(queNum).append("\" ");
			htmlStr.append(" id=\"q").append(queNum)
					.append("\" class=\"inputtext\" onclick=\"calendar.show(this,null,event);\" ");
			if (null != tsProbSubject.getMustAsk() && tsProbSubject.getMustAsk() == 1) {// 如果必答，则需要加上验证
				htmlStr.append(" zwx:valid=\"required|isDate\" zwx:errmsg=\"此题为必答题，请填写答案!|此题数据格式为日期格式，请正确填写答案!\" ");
			} else {
				htmlStr.append(" zwx:valid=\"isDate\" zwx:errmsg=\"此题数据格式为日期格式，请正确填写答案!\" ");
			}
			Map<String,String> pageEventMap = this.findEventByScript(null == tsProbSubject.getPool() ? null : tsProbSubject.getPool().getExecScript());
			//加入页面脚本事件
			if(!CollectionUtils.isEmpty(pageEventMap)){
				for(Map.Entry<String,String> mapEntity : pageEventMap.entrySet()){
					htmlStr.append(" ").append(mapEntity.getKey()).append("=\"").append(mapEntity.getValue()).append("\" ");
				}
			}
			htmlStr.append("/> ");

			htmlStr.append("<div style=\"clear:both;\"></div>");
			htmlStr.append("<div class=\"div_table_clear_bottom\"></div>");
			htmlStr.append("</div>");
			htmlStr.append("<div class=\"errorMessage\"></div>");
			String otherDesc = tsProbSubject.getOtherDesc();
			if (StringUtils.isNotBlank(otherDesc)) {
				htmlStr.append("<div class=\"div_ins_question\">提示：").append(otherDesc).append("</div>");
			}
			htmlStr.append("</div>");
		}
		return htmlStr.toString();
	}

}
