package com.chis.modules.system.queProducer;

import com.chis.common.utils.FileUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.*;
import com.chis.modules.system.enumn.GenHtmlType;
import com.chis.modules.system.interfaces.IMakeSimulateExamComponet;
import com.chis.modules.system.web.IMakeComponent;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 生成静态Html界面
 * 
 * <AUTHOR>
 * @createDate 2015年9月17日
 */
public class ProduceQuestPage {

	/** 随访问卷模版地址 */
	public static final String NARE_HTML_TEMPLATE_PATH = "WEB-INF/templates/SYSTEM_TYPE/questionNareTemplate.html";
	/** 随访问卷模版(微信)地址 */
	public static final String NARE_HTML_MOBILE_TEMPLATE_PATH = "WEB-INF/templates/SYSTEM_TYPE/questionNareMobileTemplate.html";
	/** html页面名称 */
	public static final String NARE_HTML_SUFFIX = ".html";
	/** 页面生成HTML文件夹 */
	public static final String GEN_HTML_FLODER_NAME = "/slowque";
	/** 页面生成HTML文件夹 */
	public static final String MOBILE_SUFFIX = "mobile";

	/**
	 * 传入所有的题库，根据不同的题目类型生成题目
	 * 
	 * @param questList
	 * @return
	 */
	public static String produceHtmlPage(List<TsProbSubject> questList, TsProbLib tsProbLib) {
		// 生成界面的产生信息
		String retVal = null;
		try {
			if (null != questList && questList.size() > 0 && tsProbLib != null) {
//				String systemType = null;
//				switch (tsProbLib.getSystemType().getTypeNo()) {
//				case 17:
//					systemType = "pdi";
//					break;
//				case 11:
//					systemType = "crb";
//					break;
//				default:
//					systemType = "slowque";
//					break;
//				}
				String extendS3 = null;
				String extends6 = null;
				//题目实际显示宽度
				Integer subjectShowWidth = 920;
				TsSimpleCode questSortid = tsProbLib.getTsSimpleCodeByQuestSortid();//问卷类别
				if (null!=questSortid) {
					extendS3 = questSortid.getExtendS3();
					extends6 = StringUtils.isNotBlank(questSortid.getExtendS6())?questSortid.getExtendS6():"920";
					try {
						//两边预留70px边距
						subjectShowWidth = Integer.valueOf(extends6) - 140;
					}catch (ClassCastException e){
                        subjectShowWidth = subjectShowWidth - 140;
						e.printStackTrace();
					}
				}
				// 网页HTML页面
				StringBuilder htmlName = new StringBuilder(tsProbLib.getHtmlName()).append(NARE_HTML_SUFFIX);
				StringBuilder htmlMobileName = new StringBuilder(tsProbLib.getHtmlName()).append(MOBILE_SUFFIX).append(
						NARE_HTML_SUFFIX);

				// 网页title
				String title = tsProbLib.getTsSimpleCodeByQuestSortid().getCodeName();
				// 问卷标题
				String questName = tsProbLib.getQuestName();

				// 生成问卷的Html
				StringBuilder htmlPage = new StringBuilder();
				// 生成问卷(微信)的Html
				StringBuilder wxHtmlPage = new StringBuilder();

				StringBuffer javaScriptBuffer = new StringBuffer("");
				// 对每个问题进行解析生成页面
				for (TsProbSubject tsProbSubject : questList) {
					if (tsProbSubject.getState() != null && tsProbSubject.getState().intValue() == 0) {
						continue;
					}
					//表格脚本
					List<TbProbColsdefine> colsdefines = null == tsProbSubject.getFkByTableId() ? null :
							tsProbSubject.getFkByTableId().getColsdefines();
					if(!CollectionUtils.isEmpty(colsdefines)){
						for(TbProbColsdefine col : colsdefines){
							fillTableScript(col, javaScriptBuffer);
						}
					}
					//页面脚本
					fillPageScript(null == tsProbSubject.getPool() ? null : tsProbSubject.getPool().getExecScript(), javaScriptBuffer);
					// 处理问卷题目生成DIV
					MakeQueHtmlFactory mqF = new MakeQueHtmlFactory();
					IMakeComponent makeComponent = mqF.makeComponetFactory(tsProbSubject.getQuestType());
					String compStr = makeComponent.makeComponent(tsProbSubject, GenHtmlType.WEB_HTML_PAGE);
					// 将处理的题目拼接到总内容中
					htmlPage.append(compStr);

					compStr = makeComponent.makeComponent(tsProbSubject, GenHtmlType.MOBILE_HTML_PAGE);
					// 将处理的题目拼接到总内容中
					wxHtmlPage.append(compStr);
				}
				// 虚拟路径
				String absolutePath = JsfUtil.getAbsolutePath() + GEN_HTML_FLODER_NAME;
				// 替换参数
				Map<String, String> elementsMap = new HashMap<String, String>();
				elementsMap.put("htmlPage", htmlPage.toString());
				elementsMap.put("mainCssWidth",extends6);
				elementsMap.put("subjectShowWidth",subjectShowWidth.toString());
				elementsMap.put("title", title);
				elementsMap.put("questName", questName);
//				elementsMap.put("systemType", systemType);
				String backImage = StringUtils.isNotBlank(tsProbLib.getBackImage()) ? tsProbLib.getBackImage() : "38";
				elementsMap.put("backImage", backImage);
				// javaScript
				elementsMap.put("javaScript",javaScriptBuffer.toString());

				// 写入问卷
				retVal = writeToFile(NARE_HTML_TEMPLATE_PATH.replace("SYSTEM_TYPE", "que_"+extendS3), absolutePath,
						htmlName.toString(), elementsMap);

				elementsMap.put("htmlPage", wxHtmlPage.toString());
				// 写入问卷（微信）
				retVal = writeToFile(NARE_HTML_MOBILE_TEMPLATE_PATH.replace("SYSTEM_TYPE", "que_"+extendS3), absolutePath,
						htmlMobileName.toString(), elementsMap);
			} else {
				retVal = "请先维护随访问卷的题库！";
			}
		} catch (Exception e) {
			e.printStackTrace();
			retVal = "生成失败！";
		}

		return retVal;
	}

	/**
	 * <p>方法描述：页面脚本填充 </p>
	 * pw 2023/10/19
	 **/
	private static void fillPageScript (String execScript, StringBuffer javaScriptBuffer) {
		if (StringUtils.isBlank(execScript)) {
			return;
		}

		String[] scriptArr = execScript.split("#@#@#@#@#");
		if(scriptArr.length == 0){
			return;
		}
		for(String script : scriptArr){
			String tmp = script.trim();
			if(tmp.indexOf(";") == -1){
				return;
			}
			String eventStr = tmp.substring(0,tmp.indexOf(";"));
			if(eventStr.indexOf(":") == -1){
				return;
			}
			String[] tmpArr = eventStr.split(":");
			if(tmpArr.length != 2 || StringUtils.isBlank(tmpArr[0]) || StringUtils.isBlank(tmpArr[1])){
				return;
			}
			eventStr = tmp.substring(tmp.indexOf(";")+1);
			javaScriptBuffer.append("   ").append(eventStr);
		}
	}

	/**
	 * <p>方法描述：填充表格题的脚本function </p>
	 * @MethodAuthor： pw 2022/9/30
	 **/
	private static void fillTableScript(TbProbColsdefine col, StringBuffer javaScriptBuffer){
		if(null == col || null == col.getState() || 1 != col.getState() || StringUtils.isBlank(col.getExecScript())){
			return;
		}
		fillPageScript(col.getExecScript(), javaScriptBuffer);
	}

	/**
	 * 读取模版替换元素将内容写成静态页面
	 * 
	 * @param tempName
	 *            模版名称
	 * @param absolutePath
	 *            虚拟路径
	 * @param writeFileName
	 *            写入后的文件名
	 */
	private static String writeToFile(String tempName, String absolutePath, String writeFileName,
			Map<String, String> elementsMap) {
		String error = null;
		if (null != elementsMap) {
			// 获取模版内容
			String templateContent = getTemplateContent(tempName);
			if (StringUtils.isBlank(templateContent)) {
				error = "模板不存在！";
				return error;
			}
			// 替换页面内容
			templateContent = templateContent.replaceAll("###title###", elementsMap.get("title"));
			// 替换页面内容
			templateContent = templateContent.replaceAll("###backImage###", elementsMap.get("backImage"));
			// 替换页面内容
			templateContent = templateContent.replaceAll("###TITLE###", elementsMap.get("questName"));
			// 替换页面内容
			templateContent = templateContent.replaceAll("###content###", elementsMap.get("htmlPage"));
			// 替换页面内容
			templateContent = templateContent.replaceAll("###mainCssWidth###", elementsMap.get("mainCssWidth"));
			// 替换页面内容
			templateContent = templateContent.replaceAll("###subjectShowWidth###", elementsMap.get("subjectShowWidth"));
			// 替换页面内容
//			templateContent = templateContent.replaceAll("###systemType###", elementsMap.get("systemType"));
			//替换页面内容 加入JavaScript代码
			//templateContent = templateContent.replaceAll("###javaScript###", elementsMap.get("javaScript"));//特殊字符导致java.lang.IllegalArgumentException: Illegal group reference
			String[] arr = templateContent.split("###javaScript###");
			if(arr.length == 2){
				templateContent = arr[0]+elementsMap.get("javaScript")+arr[1];
			}
			// 写入文件
			writeHtmlContent(absolutePath, writeFileName, templateContent);
		}
		return error;
	}

	/**
	 * 根据模版名称获取模版内容
	 * 
	 * @param fileName
	 * @return
	 */
	private static String getTemplateContent(String fileName) {
		// 获取模版并生成页面
		// 问卷模版文件
		String filePath = new StringBuilder(FileUtils.getWebRootPath()).append(fileName).toString();
		String templateContent = readHtmlTemplateContent(filePath);
		return templateContent;
	}

	/**
	 * 读取HMTL文件模板文件
	 * 
	 * @param tempName
	 *            模板文件名
	 * @return HTML文件模块内容
	 * <AUTHOR>
	 * @createDate 2015年9月17日
	 */
	private static String readHtmlTemplateContent(String tempName) {
		// 文件流
		FileInputStream fis = null;
		// 模板文件内容
		String templateContent = null;
		try {
			fis = new FileInputStream(tempName);
			// 读取模板文件
			int lenght = fis.available();
			byte bytes[] = new byte[lenght];
			fis.read(bytes);
			templateContent = new String(bytes, "UTF-8");
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			// 关闭文件流
			if (null != fis) {
				try {
					fis.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}

		}
		return templateContent;
	}

	/**
	 * 将生成好的HMTL文件写入磁盘
	 * 
	 * @param filePath
	 *            文件路径
	 * @param fileName
	 *            文件名称
	 * @param templateContent
	 *            文件内容
	 * @createDate 2014年11月22日
	 * @LastModify LuXuekun
	 * @ModifyDate 2014年11月22日
	 */
	private static void writeHtmlContent(String filePath, String fileName, String templateContent) {
		// 如果文件夹不存在，则创建文件夹
		File dirFile = new File(filePath);
		if (!dirFile.exists()) {
			dirFile.mkdir();
		}
		// 生成的html文件保存路径
		fileName = filePath + "/" + fileName;
		OutputStreamWriter out = null;
		try {
			out = new OutputStreamWriter(new FileOutputStream(fileName), "UTF-8");
			out.write(templateContent);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				try {
					out.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}

	}

	/**
	 * 生成模拟考试试题html内容
	 * 
	 * @param subs
	 * @return
	 */
	public static String produceSimulateExamHtml(List<TsProbExampool> subs) {
		// 生成问卷的Html
		StringBuilder htmlPage = new StringBuilder();

		// 对每个问题进行解析生成页面
		for (TsProbExampool sub : subs) {
			if (sub.getState() != null && sub.getState().intValue() == 0) {
				continue;
			}
			// 处理问卷题目生成DIV
			MakeSimulateQueHtmlFactory mqF = new MakeSimulateQueHtmlFactory();
			IMakeSimulateExamComponet makeComponent = mqF.makeComponetFactory(sub.getQuestType());
			if(makeComponent != null){
				String compStr = makeComponent.makeComponent(sub, GenHtmlType.WEB_HTML_PAGE);
				// 将处理的题目拼接到总内容中
				htmlPage.append(compStr);
			}

		}
		return htmlPage.toString();
	}

	/**
	 * 生成模拟考试试题html内容(不过滤被禁用题)
	 *
	 * @param subs
	 * @return
	 */
	public static String produceSimulateExamHtmlNoState(List<TsProbExampool> subs) {
		// 生成问卷的Html
		StringBuilder htmlPage = new StringBuilder();

		// 对每个问题进行解析生成页面
		for (TsProbExampool sub : subs) {
			// 处理问卷题目生成DIV
			MakeSimulateQueHtmlFactory mqF = new MakeSimulateQueHtmlFactory();
			IMakeSimulateExamComponet makeComponent = mqF.makeComponetFactory(sub.getQuestType());
			if(makeComponent != null){
				//NUM不为空代表题干
				if (ObjectUtil.isNotEmpty(sub.getQueNum())) {
					htmlPage.append("<div id='archor").append(sub.getQueNum()).append("'></div>");
				}
				String compStr = makeComponent.makeComponent(sub, GenHtmlType.WEB_HTML_PAGE);
				// 将处理的题目拼接到总内容中
				htmlPage.append(compStr);
			}

		}
		return htmlPage.toString();
	}
}
