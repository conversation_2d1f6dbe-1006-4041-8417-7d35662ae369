package com.chis.modules.system.mongo3.utils;

import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.mongo.service.MongoService;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortOrder;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * @Description : 分页（第三版）
 * @ClassAuthor : anjing
 * @Date : 2019/6/11 13:52
 **/
public class MongoDefaultLazyDataModel3<T, I> extends LazyDataModel<T> {

    private static final long serialVersionUID = 1L;
    private Class<T> tClass ;
    private Class<I> iClass ;
    private MongoService mongoService = SpringContextHolder.getBean(MongoService.class);
    private List<T> data;
    private Query query;
    private Criteria criteria;
    private String queryStr;
    /**绑定表格的id*/
    private String tableId;
    /**查询条件*/
    private Map<String, Object> paramMap;
    /**数据处理接口*/
    private IProcessData processData;
    /**是否第一次查询*/
    private boolean firstSearch = Boolean.TRUE;
    /**传入的是否是SQL语句，默认HQL语句*/
    private boolean ifSQL = Boolean.FALSE;
    //是否是聚合查询
    private boolean ifAgg = Boolean.FALSE;
    private List<AggregationOperation> operations;

    public MongoDefaultLazyDataModel3(Query query,Class<T> tClass, String tableId) {
        this.query = query==null?new Query():query;
        this.tClass = tClass;
        this.tableId = tableId;
        this.init();
    }


    public MongoDefaultLazyDataModel3(Query query, String queryStr,Class<T> tClass, IProcessData processData, String tableId, boolean ifSQL,boolean ifAgg) {
        this.query = query==null?new Query():query;
        this.queryStr = queryStr;
        this.tClass = tClass;
        this.processData = processData;
        this.tableId = tableId;
        this.ifSQL = ifSQL;
        this.ifAgg = ifAgg;
        this.init();
    }

    public MongoDefaultLazyDataModel3(String queryStr, Class<T> tClass, IProcessData buildProcessData, String tableId) {
        this.ifSQL = true;
        this.queryStr = queryStr;
        this.tClass = tClass;
        this.processData = buildProcessData;
        this.tableId = tableId;
        this.init();
    }

    public MongoDefaultLazyDataModel3(Query query, Class<T> tClass, IProcessData buildProcessData, String tableId) {
        this.ifSQL = false;
        this.ifAgg = false;
        this.query = query;
        this.tClass = tClass;
        this.processData = buildProcessData;
        this.tableId = tableId;
        this.init();
    }

    public MongoDefaultLazyDataModel3(List<AggregationOperation> operations, Class<I> iClass,Criteria criteria, Class<T> tClass, IProcessData buildProcessData, String tableId) {
        this.ifSQL = false;
        this.ifAgg = true;
        this.criteria = criteria;
        this.tClass = tClass;
        this.processData = buildProcessData;
        this.tableId = tableId;
        this.operations = operations;
        this.iClass = iClass;
        this.init();
    }

    private void init() {
        if(this.ifSQL) {

        }else if(this.ifAgg){
            GroupOperation count = Aggregation.group().count().as("count");
            operations.add(count);
            int countNum = mongoService.count(operations);
            this.setRowCount(countNum);
        }else {
            Integer searchLimit = 10000;
            String searchLimitCof = PropertyUtils.getValue("searchLimit");
            if(StringUtils.isNotBlank(searchLimitCof)) {
                searchLimit = Integer.parseInt(searchLimitCof);
            }
            query.limit(searchLimit);
            this.data = this.mongoService.find(query, tClass);
            this.setRowCount(this.data.size());
        }
    }

    @Override
    public List<T> load(int first, int pageSize, String sortField, SortOrder sortOrder, Map<String, String> filters) {
        int rowCount = this.getRowCount();
        if (rowCount == 0) {
            return Collections.emptyList();
        }
        if (first >= rowCount) {
            // 当查询使用的首记录超出记录总数时，调整首记录
            if (rowCount % pageSize == 0) {
                first = ((rowCount / pageSize) - 1) * pageSize;
            } else {
                first = (rowCount / pageSize) * pageSize;
            }
        }
        /** 实现多余数据分页 */
        if((first + pageSize) >= rowCount) {
            pageSize = rowCount % pageSize;
        }
        if (this.ifSQL) {

        }else if(this.ifAgg){
            SkipOperation skip = Aggregation.skip(first);
            LimitOperation limit = Aggregation.limit(pageSize);
            Iterator<AggregationOperation> iterator = operations.iterator();
            while(iterator.hasNext()){
                AggregationOperation next = iterator.next();
                if(next instanceof  SkipOperation||next instanceof  LimitOperation|| next instanceof GroupOperation){
                    iterator.remove();
                }
            }
            operations.add(skip);
            operations.add(limit);
            this.data = this.mongoService.pageList(operations,tClass,iClass);
        } else {
            query.skip(first).limit(pageSize);
            this.data = this.mongoService.find(query, tClass);
        }
        if(null != this.processData) {
            this.processData.processData(this.data);
        }
        return this.data;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }
}
