package com.chis.modules.system.mongo3.web;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import com.chis.common.utils.*;
import com.chis.modules.system.logic.CompanySearchItemSystemVO;
import com.chis.modules.system.service.LogBhkServiceImpl;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.model.DefaultStreamedContent;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.mongo2.entity.MongoPage2;
import com.chis.modules.system.mongo2.entity.bhkdataservice.LogBhkServiceCrpt;

/**
 * @Description : 企业日志查询（第三版）
 * @ClassAuthor : anjing
 * @Date : 2019/6/11 11:30
 **/
@ManagedBean(name = "logBhkServiceCrptBean3")
@ViewScoped
public class LogBhkServiceCrptBean3 extends LogBhkServiceBean3<LogBhkServiceCrpt, LogBhkServiceCrpt> implements IProcessData {

    private LogBhkServiceImpl logBhkService = SpringContextHolder.getBean(LogBhkServiceImpl.class);
    /**查询实体*/
    private LogBhkServiceCrpt searchEntity;
    private LogBhkServiceCrpt entity;
    private List<LogBhkServiceCrpt> selectEntitys;
    private Integer searcchLimit = 10000;

    /**显示总记录数*/
    private boolean showInfo;
    private long total = 0;

    /**
     * 搜索结果
     */
    private List<CompanySearchItemSystemVO> companyList;
    /**
     * 是否使用天眼查通用用人单位弹出框
     */
    private Boolean useNewCrptSelect;
    /**
     * 页面显示天眼查名称
     */
    private String showCrptName;

    @PostConstruct
    @Override
    public void init(){
        String searchLimitCof = PropertyUtils.getValue("searchLimit");
        if(StringUtils.isNotBlank(searchLimitCof)) {
            this.searcchLimit = Integer.parseInt(searchLimitCof);
        }

        this.showInfo = false;
        this.total = 0;
        super.init();

        this.useNewCrptSelect = "1".equals(this.commService.findParamValue("USE_NEW_CRPT_SELECT"));

        // 初始化查询实体
        searchEntity = new LogBhkServiceCrpt();
        searchEntity.setZone_gb(this.searchZoneCode);
        searchEntity.setUnit_code(super.unitCode);
        searchEntity.setUpload_tag("0");
        searchEntity.setDeal_state("0");
        try {
            Date date = DateUtils.parseDate(DateUtils.getYear() + "-"+DateUtils.getMonth()+"-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
            searchEntity.setVisit_time_start(date);
            String formatDate = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
            searchEntity.setVisit_time_end(DateUtils.parseDate(formatDate+" 23:59:59"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        this.searchAction();
    }

    /**
     * @Description : 查询
     * @MethodAuthor: anjing
     * @Date : 2019/5/8 10:25
     **/
    public void searchAction(){
    	if (!ifAdmin && StringUtils.isBlank(this.searchEntity.getUnit_code())) {
            return;
		}
        this.showInfo = false;
        this.total = 0;
        if(searchEntity.getVisit_time_start()==null){
            JsfUtil.addErrorMessage("上传开始日期不能为空！");
            return ;
        }
        if(searchEntity.getVisit_time_end()==null){
            JsfUtil.addErrorMessage("上传结束日期不能为空！");
            return ;
        }
        if(searchEntity.getVisit_time_start()!=null&&searchEntity.getVisit_time_end()!=null&&
                searchEntity.getVisit_time_start().after(searchEntity.getVisit_time_end())){
            JsfUtil.addErrorMessage("上传开始日期应小于等于上传结束日期！");
            return;
        }
        if(searchEntity.getVisit_time_end()!=null){
            String formatDate = DateUtils.formatDate(searchEntity.getVisit_time_end(), "yyyy-MM-dd");
            searchEntity.setVisit_time_end(DateUtils.parseDate(formatDate+" 23:59:59"));
        }
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:mainForm:dataTable");
        dataTable.setFirst(0);
        super.searchActionWithHQL();
    }

    /**
     * @Description : 查询条件，地区树选择事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/8 11:14
     **/
    public void onSearchNodeSelect() {
        this.searchEntity.setUnit_code(null);
        this.searchUnitMap = this.filterUnit(this.searchEntity.getZone_gb(), this.searchZoneType);
    }

    /**
     * @Description : 方法描述: 列表查询语句
     * @MethodAuthor: anjing
     * @Date : 2019/5/8 11:20
     **/
    @Override
    protected Criteria buildCriteria() {
        List<Criteria> criteriaList = new ArrayList<>();

        //上传地区
        String zone_gb = searchEntity.getZone_gb();
        if(StringUtils.isNotBlank(zone_gb)){
            criteriaList.add(Criteria.where("zone_gb").regex("^"+ ZoneUtil.zoneSelect(zone_gb)+".*"));
        }
        //上传机构
        String unit_code = searchEntity.getUnit_code();
        if(StringUtils.isNotBlank(unit_code)){
            criteriaList.add(Criteria.where("unit_code").is(unit_code));
        }
        //上传日期
        Date visit_time_start = searchEntity.getVisit_time_start();
        if(visit_time_start!=null){
            criteriaList.add(Criteria.where("visit_time").gte(visit_time_start));
        }
        Date visit_time_end = searchEntity.getVisit_time_end();
        if(visit_time_end!=null){
            criteriaList.add(Criteria.where("visit_time").lte(visit_time_end));
        }
        //企业名称
        String crpt_name = searchEntity.getCrpt_name();
        if(StringUtils.isNotBlank(crpt_name)){
            criteriaList.add(Criteria.where("crpt_name").is(crpt_name));
        }
        //社会信用代码
        String credit_code = searchEntity.getCredit_code();
        if(StringUtils.isNotBlank(credit_code)){
            criteriaList.add(Criteria.where("credit_code").is(credit_code));
        }
        //上传标记
        if(uploadTags!=null&&uploadTags.size()==1){
            criteriaList.add(Criteria.where("upload_tag").is(uploadTags.get(0)));
        }
        //处理状态
        if(dealStates!=null&&dealStates.size()==1){
            criteriaList.add(Criteria.where("deal_state").is(dealStates.get(0)));
        }

        Criteria criteria = new Criteria().andOperator((Criteria[]) criteriaList.toArray(new Criteria[criteriaList.size()]));
        return criteria;
    }

    @Override
    public Query buildQuery() {
        Query query = new Query();
        query.addCriteria(this.buildCriteria());
        query.with(new Sort(Sort.Direction.DESC , "visit_time"));
        return query;
    }

    /**
    * @Description : 显示总记录数
    * @MethodAuthor: anjing
    * @Date : 2019/6/13 15:35
    **/
    public void showTotalAction() {
        this.showInfo = true;
        Query query = this.buildQuery();
        this.total = mongoService.count(query, LogBhkServiceCrpt.class);
    }

    @Override
    public void processData(List<?> list) {
        int size = list.size();
        for (int i = 0; i < size; i++) {
            LogBhkServiceCrpt record = (LogBhkServiceCrpt) list.get(i);
            if("0".equals(record.getUpload_tag())){
                String error_msg = record.getError_msg();
                record.setError_msg_temp(error_msg);
                if(error_msg!=null&&error_msg.length()>10){
                    record.setError_msg_temp(error_msg.substring(0,10)+"...");
                }
            }
        }
    }

    @Override
    public Class<LogBhkServiceCrpt> newOutputClazz() {
        return LogBhkServiceCrpt.class;
    }

    /***
     * <p>方法描述: 更新状态</p>
     *
     * @MethodAuthor mxp,2018/12/19,updateStateAction
     */
    public void updateStateAction(){
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("rid").is(searchEntity.getRid()));
            Update update = new Update();
            update.set("deal_state","1");
            mongoService.updateFirst(query, update, LogBhkServiceCrpt.class);
            entity.setDeal_state("1");
            this.searchAction();
            JsfUtil.addSuccessMessage("操作成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("操作失败！");
            e.printStackTrace();
        }
    }

    /***
     * 更新单位名称操作
     */
    public void updateCompanyNameAction() {
        if (ObjectUtil.isEmpty(this.entity) || ObjectUtil.isEmpty(this.entity.getCredit_code())) {
            JsfUtil.addErrorMessage("社会信用代码不能为空！");
            return;
        }
        try {
            String searchCompanyWord = StringUtils.objectToString(this.entity.getCredit_code());
            try {
                this.companyList = this.logBhkService.searchCompanyByTyc(searchCompanyWord);
            } catch (Exception e) {
                this.companyList = new ArrayList<>();
                return;
            }
            for (int i = this.companyList.size() - 1; i >= 0; i--) {
                CompanySearchItemSystemVO companyVO = this.companyList.get(i);
                if (!searchCompanyWord.equals(companyVO.getCreditCode())) {
                    this.companyList.remove(i);
                }
            }
            if (ObjectUtil.isEmpty(this.companyList)) {
                JsfUtil.addErrorMessage("获取天眼查无数据！");
                return;
            }
            RequestContext.getCurrentInstance().update("tabView:mainForm:searchCompanyTable");
            RequestContext.getCurrentInstance().execute("PF('SearchCompanyDialog').show()");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("更新失败！");
        }
    }

    /**
     * 更新单位名称
     *
     * @param companyVO 单位信息
     */
    public void updateCompanyName(CompanySearchItemSystemVO companyVO) {
        try {
            this.logBhkService.updateCrptNameByInstitutionCode(companyVO.getCreditCode(), companyVO.getName());
            Query query = new Query();
            query.addCriteria(Criteria.where("rid").is(this.searchEntity.getRid()));
            Update update = new Update();
            update.set("need_update_name", "1");
            update.set("update_name", companyVO.getName());
            this.mongoService.updateFirst(query, update, LogBhkServiceCrpt.class);
            this.entity.setNeed_update_name("1");
            this.searchAction();
            JsfUtil.addSuccessMessage("更新成功！");
            RequestContext.getCurrentInstance().update("tabView");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("更新失败！");
        }
    }

    /***
     * <p>方法描述: 更新状态状态</p>
     *
     * @MethodAuthor mxp,2018/12/19,batchUpdateStateAction
     */
    public void batchUpdateStateAction(){
        try {
            if(selectEntitys==null||selectEntitys.size()==0){
                JsfUtil.addErrorMessage("请选择数据！");
                return ;
            }
            List<String> ids = new ArrayList<>();
            for (LogBhkServiceCrpt selectEntity : selectEntitys) {
                if("0".equals(selectEntity.getDeal_state())){
                    ids.add(selectEntity.getRid());
                }
            }
            if(ids.size()==0){
                JsfUtil.addErrorMessage("请选择含有未阅的数据！");
                return ;
            }

            Query query = new Query();
            query.addCriteria(Criteria.where("rid").in(ids));
            Update update = new Update();
            update.set("deal_state","1");
            mongoService.updateMulti(query, update, LogBhkServiceCrpt.class);
            selectEntitys = new ArrayList<>();
            this.searchAction();
            JsfUtil.addSuccessMessage("操作成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("操作失败！");
            e.printStackTrace();
        }
    }

    @Override
    public void modInit() {
        if(entity==null){
            return;
        }
        super.modInit(entity.getMain_id(),entity.getRid());
    }

    /***
     * @Description : 导出
     * @MethodAuthor: mxp
     * @Date : 2019/5/20 16:47
     */
    @Override
    public DefaultStreamedContent export(){
        String fileName="企业日志";
        // 初始化标题
        XSSFWorkbook wb = super.initTitle(fileName, 11);
		if (!ifAdmin && StringUtils.isBlank(this.searchEntity.getUnit_code())) {
			return super.export(fileName, wb);
		}
        // 初始化第二行标题
        //this.initTitle2(wb);
        // 组装数据导出
        boolean b = initExportData(wb);
        if(!b){
            return null;
        }
        return super.export(fileName,wb);
    }

    /***
     * @Description : 企业数据
     * @MethodAuthor: mxp
     * @Date : 2019/5/20 16:57
     * @return
     */
    @Override
    protected boolean initExportData(XSSFWorkbook wb) {
        if(searchEntity.getVisit_time_end()!=null){
            String formatDate = DateUtils.formatDate(searchEntity.getVisit_time_end(), "yyyy-MM-dd");
            searchEntity.setVisit_time_end(DateUtils.parseDate(formatDate+" 23:59:59"));
        }
        //先查总共有多少条数据
        Query query = this.buildQuery();
        long count = mongoService.count(query, LogBhkServiceCrpt.class);
        if(count>1000000){
            JsfUtil.addErrorMessage("导出数据大于1百万，无法导出！");
            return false;
        }
        //每次查询10000条
        int  size = 10000;
        long num = count/size;
        if(count%size!=0){
            num++;
        }
        XSSFSheet sheet = wb.getSheetAt(0);
        MongoPage2<LogBhkServiceCrpt> mongoPage = new MongoPage2<>();
        mongoPage.setSize(size);//因为下面分页查询每次多查询出1条
        mongoPage.setDesc("visit_time");
        MongoPage2<LogBhkServiceCrpt> mongoPage2;
        List<LogBhkServiceCrpt> records;
        int rowNum=1;
        for (int i = 1; i <= num; i++) {
            mongoPage.setCurrent(i);
            mongoPage2 = mongoService.pageListCommon(mongoPage, query, LogBhkServiceCrpt.class);
            records = mongoPage2.getRecords();
            if(records!=null&&records.size()>0){
                rowNum = (i-1)*size;
                for (LogBhkServiceCrpt record : records) {
                    rowNum++;
                    XSSFRow row2 = sheet.createRow(rowNum+1);
                    XSSFCell cell0 = row2.createCell(0);
                    cell0.setCellValue(uploadZoneMap.get(record.getZone_gb()));
                    XSSFCell cell1 = row2.createCell(1);
                    cell1.setCellValue(uploadUnitMap.get(record.getUnit_code()));
                    XSSFCell cell2 = row2.createCell(2);
                    cell2.setCellValue(record.getCrpt_name());
                    XSSFCell cell3 = row2.createCell(3);
                    cell3.setCellValue(record.getCredit_code());
                    XSSFCell cell4 = row2.createCell(4);
                    cell4.setCellValue(DateUtils.formatDate(record.getVisit_time(),"yyyy-MM-dd HH:mm:ss"));
                    XSSFCell cell5 = row2.createCell(5);
                    cell5.setCellValue("1".equals(record.getUpload_tag())?"成功":"失败");
                    XSSFCell cell6 = row2.createCell(6);
                    cell6.setCellValue("0".equals(record.getUpload_tag())?"未阅":"1".equals(record.getUpload_tag())?"已阅":"无需处理");
                    XSSFCell cell7 = row2.createCell(7);
                    cell7.setCellValue(record.getError_msg());
                }
            }
        }
        //super.export("企业日志",wb);
        return true;
    }

    @Override
    protected void initTitle2(XSSFWorkbook wb) {
        XSSFSheet sheet = wb.getSheetAt(0);
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(CellStyle.ALIGN_CENTER);// 左右居中
        cellStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        // 表列名称
        XSSFRow row2 = sheet.createRow(1);
        XSSFCell cell0 = row2.createCell(0);
        cell0.setCellValue("地区");
        cell0.setCellStyle(cellStyle);
        sheet.setColumnWidth(0, 3000);
        XSSFCell cell1 = row2.createCell(1);
        cell1.setCellValue("上传机构名称");
        cell1.setCellStyle(cellStyle);
        sheet.setColumnWidth(1, 4500);
        XSSFCell cell2 = row2.createCell(2);
        cell2.setCellValue("企业名称");
        cell2.setCellStyle(cellStyle);
        sheet.setColumnWidth(2, 5500);
        XSSFCell cell3 = row2.createCell(3);
        cell3.setCellValue("社会信用代码");
        cell3.setCellStyle(cellStyle);
        sheet.setColumnWidth(3, 4000);
        XSSFCell cell4 = row2.createCell(4);
        cell4.setCellValue("上传时间");
        cell4.setCellStyle(cellStyle);
        sheet.setColumnWidth(4, 5000);
        XSSFCell cell5 = row2.createCell(5);
        cell5.setCellValue("上传标记");
        cell5.setCellStyle(cellStyle);
        sheet.setColumnWidth(5, 3000);
        XSSFCell cell6 = row2.createCell(6);
        cell6.setCellValue("状态");
        cell6.setCellStyle(cellStyle);
        sheet.setColumnWidth(6, 4000);
        XSSFCell cell7 = row2.createCell(7);
        cell7.setCellValue("失败原因");
        cell7.setCellStyle(cellStyle);
        sheet.setColumnWidth(7, 4000);
        XSSFCell cell8 = row2.createCell(8);
    }

    @Override
    public Class<LogBhkServiceCrpt> newInputClazz() {
        return null;
    }

    @Override
    protected List<AggregationOperation> buildOperations() {
        return null;
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void saveAction() {

    }

    public LogBhkServiceCrpt getSearchEntity() {
        return searchEntity;
    }

    public void setSearchEntity(LogBhkServiceCrpt searchEntity) {
        this.searchEntity = searchEntity;
    }

    public LogBhkServiceCrpt getEntity() {
        return entity;
    }

    public void setEntity(LogBhkServiceCrpt entity) {
        this.entity = entity;
    }

    public List<LogBhkServiceCrpt> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<LogBhkServiceCrpt> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }

    public boolean isShowInfo() {
        return showInfo;
    }

    public void setShowInfo(boolean showInfo) {
        this.showInfo = showInfo;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public Integer getSearcchLimit() {
        return searcchLimit;
    }

    public void setSearcchLimit(Integer searcchLimit) {
        this.searcchLimit = searcchLimit;
    }

    public List<CompanySearchItemSystemVO> getCompanyList() {
        return companyList;
    }

    public void setCompanyList(List<CompanySearchItemSystemVO> companyList) {
        this.companyList = companyList;
    }

    public Boolean getUseNewCrptSelect() {
        return useNewCrptSelect;
    }

    public void setUseNewCrptSelect(Boolean useNewCrptSelect) {
        this.useNewCrptSelect = useNewCrptSelect;
    }

    public String getShowCrptName() {
        return showCrptName;
    }

    public void setShowCrptName(String showCrptName) {
        this.showCrptName = showCrptName;
    }
}
