package com.chis.modules.system.mongo3.utils;

import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.mongo.service.MongoService;
import com.chis.modules.system.web.FacesBean;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

/**
 * @Description : 分页（第三版）
 * @ClassAuthor : anjing
 * @Date : 2019/6/11 13:41
 **/
public abstract class  MongoFacesEditBean3<T, I> extends FacesBean {

    /**当前活动的标签，从0开始，默认为0,0:主界面1：添加修改界面2：查看界面*/
    private int activeTab;
    /**真分页模型*/
    private MongoDefaultLazyDataModel3<T, I> dataModel;
    /**查询语句，0-查询记录的hql语句 1-查询记录数的hql语句*/
    private String  queryStr;
    private Query query;
    private Criteria criteria;
    private Class<T> tClass;
    /**表格的ID*/
    private static final String TABLE_ID = "mainForm:dataTable";

    protected boolean ifSQL = Boolean.FALSE;
    protected boolean ifAgg = Boolean.FALSE;
    private List<AggregationOperation> operations;
    private Class<I> iClass ;

    private MongoService mongoService = SpringContextHolder.getBean(MongoService.class);

    public MongoFacesEditBean3() {
    }

    /**
     * 查询操作，如果不满足，可重写
     */
    public void searchActionWithSQL() {
        queryStr = this.buildQueryStr();
        tClass = this.newOutputClazz();
        this.dataModel = new MongoDefaultLazyDataModel3<T,I>(this.queryStr, tClass,this.buildProcessData(), TABLE_ID);
    }

    /**
     * 查询操作，如果不满足，可重写
     */
    public void searchActionWithHQL() {
        query = this.buildQuery();
        tClass = this.newOutputClazz();
        this.dataModel = new MongoDefaultLazyDataModel3<T, I>(this.query,tClass,this.buildProcessData(), TABLE_ID);
    }

    /**
     * 查询操作，如果不满足，可重写
     */
    public void searchActionWithAgg() {
        criteria = this.buildCriteria();
        tClass = this.newOutputClazz();
        iClass = this.newInputClazz();
        operations = this.buildOperations();
        operations.add(Aggregation.match(criteria));
        this.dataModel = new MongoDefaultLazyDataModel3<T,I>(operations,iClass,this.criteria, tClass,this.buildProcessData(), TABLE_ID);
    }

    public abstract Query buildQuery();

    protected String buildQueryStr() {
        return null;
    }

    public abstract Class<T> newOutputClazz();
    public abstract Class<I> newInputClazz();

    protected abstract List<AggregationOperation> buildOperations();

    protected abstract Criteria buildCriteria();

    /**
     * 查询数据处理接口，如果需要处理，则托管bean必须要实现
     * IProcessData接口，并且返回this；否则返回NULL
     */
    public IProcessData buildProcessData() {
        if(this instanceof IProcessData) {
            return (IProcessData) this;
        }else {
            return null;
        }
    }

    /**
     * 添加初始化
     */
    public void addInitAction() {
        this.addInit();
        this.forwardEditPage();
    }

    /**
     * 修改初始化
     */
    public void modInitAction() {
        this.modInit();
        this.forwardEditPage();
    }

    /**
     * 查看初始化
     */
    public void viewInitAction() {
        this.viewInit();
        this.forwardViewPage();
    }

    /**
     * 转向修改界面
     */
    public void forwardEditPage() {
        this.activeTab = 1;
    }

    /**
     * 转向到查看界面
     */
    public void forwardViewPage() { this.activeTab = 2; }

    /**
     * 转向到其他界面
     */
    public void forwardOtherPage() { this.activeTab = 3; }

    /**
     * 返回首页面
     */
    public void backAction() {
        this.activeTab = 0;
    }

    /**
     * 跳转到其他查看页面
     */
    public void forwardOtherViewPage() {this.activeTab = 4;}

    /**
     * 具体的添加初始化
     */
    public abstract void addInit();

    /**
     * 具体的修改初始化
     */
    public abstract void viewInit();

    /**
     * 具体的查看初始化
     */
    public abstract void modInit();

    /**
     * 具体的添加
     */
    public abstract void saveAction();

    public int getActiveTab() {
        return activeTab;
    }

    public void setActiveTab(int activeTab) {
        this.activeTab = activeTab;
    }

    public MongoDefaultLazyDataModel3<T, I> getDataModel() {
        return dataModel;
    }

    public void setDataModel(MongoDefaultLazyDataModel3<T, I> dataModel) {
        this.dataModel = dataModel;
    }
}
