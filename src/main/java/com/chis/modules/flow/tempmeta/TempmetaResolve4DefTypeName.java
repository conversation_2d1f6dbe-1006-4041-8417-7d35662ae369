package com.chis.modules.flow.tempmeta;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.entity.TdFlowDef;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.interfaces.ITempmetaResolve;
import com.chis.modules.system.logic.MetaCondition;
import com.chis.modules.system.logic.OtherTempCon;

/**
 * 流程定义类型名称的解析
 * <AUTHOR>
 */
@Component(value="com.chis.modules.flow.tempmeta.TempmetaResolve4DefTypeName")
public class TempmetaResolve4DefTypeName implements ITempmetaResolve {

	@Override
	public String resolve(MetaCondition condition, EntityManager em) {
		try {
			String json = condition.getJson();
			if(StringUtils.isNotBlank(json)) {
				OtherTempCon o = JSON.parseObject(json, OtherTempCon.class);
				TdFlowDef def = em.find(TdFlowDef.class, o.getDefId());
				return def.getTdFlowType().getTypeName();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public String description() {
		return "获取当前流程类型的名称，如：行政办公";
	}

	@Override
	public String testResult() {
		return "行政办公";
	}

}
