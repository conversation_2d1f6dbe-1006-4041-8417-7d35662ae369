package com.chis.modules.flow.web;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.primefaces.event.NodeSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.entity.TdFlowType;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.flow.logic.TdFlowTaskCondBean;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.web.FacesSimpleBean;

/**
 * 流程待办任务
 * 
 * 2014-9-2 david  增加检查url是否含有taskId,如果有跳转到任务详情界面
 * 
 * <AUTHOR>
 */
@ManagedBean(name = "tdFlowTaskBean")
@ViewScoped
public class TdFlowTaskBean extends FacesSimpleBean {

	private static final long serialVersionUID = -738445454536625063L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);

	/** 查询条件：流程类型树 */
	private TreeNode searchTreeNode;
	/** 查询条件 */
	private TdFlowTaskCondBean condition;
	/** 任务ID */
	private String taskId;
	/** 节点ID */
	private String nodeId;
	/** 业务表单ID */
	private String businessId;
	/**我的委托，*/
	private List<Object[]> trustList;
	/**0-包含委托, 1-不包含委托, 2-仅委托*/
	private Integer searchTrust = 0;
	public TdFlowTaskBean() {
		super.ifSQL = Boolean.TRUE;
		this.init();
	}
	/**
	 * 初始化方法
	 */
	private void init() {
		String param = JsfUtil.getRequest().getParameter("param1");
		if (StringUtils.isNotBlank(param) && param.equals("1")) {
			JsfUtil.addSuccessMessage("操作成功！");
		}
		this.initSearchCondition();
		this.searchAction();
	}

	/**
	 * 初始化查询条件
	 */
	private void initSearchCondition() {
		if (null == this.searchTreeNode) {
			this.searchTreeNode = new DefaultTreeNode("root", null);
			List<TdFlowType> list = this.flowBusinessService.findFlowTypeList();
			if (null != list && list.size() > 0) {
				Map<Integer, TreeNode> map = new HashMap<Integer, TreeNode>();
				for (TdFlowType t : list) {
					if (null == t.getParent()) {
						TreeNode node = new DefaultTreeNode(t, this.searchTreeNode);
						map.put(t.getRid(), node);
					} else {
						TreeNode node = new DefaultTreeNode(t, map.get(t.getParent().getRid()));
						map.put(t.getRid(), node);
					}
				}
				map.clear();
			}
		}
		
		/**
		 * 设置查询条件，可能是从待办任务处理页面转向过来的
		 */
		/**
		 * 非空的话是从详情界面转向过来的
		 */
		String con = JsfUtil.getRequest().getParameter("param");
		if (StringUtils.isNotBlank(con)) {
			this.condition = JSON.parseObject(con, TdFlowTaskCondBean.class);
		} else {
			this.condition = new TdFlowTaskCondBean();
		}
		
		this.trustList = this.flowBusinessService.findMyTrust(this.sessionData.getUser().getRid());
	}

	/**
	 * 流程类型选中事件
	 * 
	 * @param event
	 *            选中事件
	 */
	public void onSearchNodeSelect(NodeSelectEvent event) {
		TreeNode node = event.getTreeNode();
		if (null != node) {
			TdFlowType type = (TdFlowType) node.getData();
			this.condition.setSearchFlowTypeId(type.getRid());
			this.condition.setSearchFlowTypeName(type.getTypeName());
		}
	}
	
	/**
	 * 转向到待办任务处理界面
	 * @return
	 */
	public void taskDealInitAction(){
		try {
			StringBuilder sb = new StringBuilder("/webapp/flow/tdFlowTaskEdit.faces?ph=1&read=false");
			sb.append("&trustId=").append(this.sessionData.getUser().getRid());
			if (StringUtils.isNotBlank(this.taskId)) {
				sb.append("&taskId=").append(this.taskId);
			}
			if (StringUtils.isNotBlank(this.nodeId)) {
				sb.append("&nodeId=").append(this.nodeId);
			}
			if (StringUtils.isNotBlank(this.businessId)) {
				sb.append("&businessId=").append(this.businessId);
			}
			if (null != this.condition) {
				sb.append("&from=/webapp/flow/tdFlowTaskList.faces?param=").append(JSON.toJSONString(this.condition));
			}
			FacesContext.getCurrentInstance().getExternalContext().redirect(sb.toString());
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}


	/**
	 * 0-任务ID <br/>
	 * 1-流程类型ID <br/>
	 * 2-流程类型名称 <br/>
	 * 3-流程定义ID(非activiti) <br/> 
	 * 4-流程定义名称 <br/> 
	 * 5-任务名称 <br/>
	 * 6-流程节点ID<br/>
	 * 7-流程节点名称<br/>
	 * 8-流程发起者姓名<br/>
	 * 9-流程发起时间<br/>
	 * 10-业务主键JSON<br/>
	 * 11-任务优先级<br/>
	 * 根据任务优先级倒序、任务创建时间倒序<br/>
	 * 
	 * 查询自己的待办任务以及他人一段时间内委托我待办的任务 <br/>
	 * 
	 */
	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder();
		/**
 		 *包含委托 0 
		 *不包含委托 1 
		 *仅委托 2
		 */ 
		sb.append(" WITH TASKS AS ( ");
		if(this.searchTrust != 2) {
			sb.append(" SELECT DISTINCT RES.*, '").append(this.sessionData.getUser().getRid()).append("' AS USERID ");
			sb.append(" FROM ACT_RU_TASK RES ");
			sb.append("  INNER JOIN ACT_RU_IDENTITYLINK I ON I.TASK_ID_ = RES.ID_ ");
			sb.append(" WHERE RES.ASSIGNEE_ IS NULL ");
			sb.append("  AND I.TYPE_ = 'candidate' ");
			sb.append("  AND (I.USER_ID_ = '").append(this.sessionData.getUser().getRid()).append("') ");
			
			sb.append(" UNION ");
			
			sb.append(" SELECT DISTINCT RES.*, '").append(this.sessionData.getUser().getRid()).append("' AS USERID ");
			sb.append(" FROM ACT_RU_TASK RES ");
			sb.append("  WHERE RES.ASSIGNEE_ = '").append(this.sessionData.getUser().getRid()).append("' ");
		}else {
			sb.append(" SELECT RES.*, '").append(this.sessionData.getUser().getRid()).append("' AS USERID ");
			sb.append(" FROM ACT_RU_TASK RES ");
			sb.append("  INNER JOIN ACT_RU_IDENTITYLINK I ON 1=2 ");
		}
		if(null != this.trustList && this.trustList.size() > 0 && this.searchTrust != 1) {
			for(Object[] o : this.trustList) {
				sb.append(" UNION ");
				sb.append(" SELECT DISTINCT RES.*, '").append(o[0]).append("' AS USERID FROM ACT_RU_TASK RES ");
				sb.append(" INNER JOIN ACT_RU_IDENTITYLINK I ON I.TASK_ID_ = RES.ID_ ");
				sb.append(" WHERE RES.ASSIGNEE_ IS NULL AND I.TYPE_ = 'candidate' AND (I.USER_ID_ = '").append(o[0]).append("') ");
				sb.append(" AND RES.PROC_DEF_ID_ IN (").append(o[1]).append(") ");
				sb.append(" UNION ");
				sb.append(" SELECT DISTINCT RES.*, '").append(o[0]).append("' AS USERID FROM ACT_RU_TASK RES ");
				sb.append(" WHERE RES.ASSIGNEE_ = '").append(o[0]).append("' ");
				sb.append(" AND RES.PROC_DEF_ID_ IN (").append(o[1]).append(") ");
			}
		}
		sb.append(" ) ");
		sb.append(" SELECT T1.ID_ AS TASKID, T4.RID AS TYPEID, T4.TYPE_NAME, T2.RID AS DEFID, T2.DEF_NAME, ");
		sb.append(" DECODE(T1.USERID, '").append(this.sessionData.getUser().getRid()).append("','','【委托】') || T1.DESCRIPTION_||A3.BUSINESS_KEY_ AS BUSINESSTITLE, ");
		sb.append(" T3.RID AS NODEID, T3.NODE_NAME, T5.USERNAME, TO_CHAR(A3.START_TIME_, 'YYYY-MM-DD HH24:MI:SS') AS START_TIME_ , A4.TEXT_ AS businessId,T1.PRIORITY_ ");
		sb.append(" FROM TASKS T1 ");
		sb.append(" INNER JOIN TD_FLOW_DEF T2 ON T1.PROC_DEF_ID_ = T2.ACT_DEF_ID ");
		sb.append(" INNER JOIN TD_FLOW_NODE T3 ON T3.DEF_ID = T2.RID AND T3.ACT_NODE_ID = T1.TASK_DEF_KEY_ ");
		sb.append(" INNER JOIN TD_FLOW_TYPE T4 ON T2.TPYE_ID = T4.RID ");
		if (null != this.condition.getSearchFlowTypeId()) {
			sb.append(" AND  T4.RID IN(SELECT Z.RID FROM TD_FLOW_TYPE Z START WITH RID='").append(this.condition.getSearchFlowTypeId()).append("' CONNECT BY PRIOR RID = PARENT_ID) ");
		}
		sb.append(" INNER JOIN ACT_HI_PROCINST A3 ON A3.PROC_INST_ID_ = T1.PROC_INST_ID_ ");
		if(StringUtils.isNotBlank(this.condition.getSearchTaskName())) {
			sb.append(" AND A3.BUSINESS_KEY_ LIKE :taskName");
			this.paramMap.put("taskName", "%" + this.condition.getSearchTaskName() + "%");
		}
		if(null != this.condition.getSearchStartTime()) {
			sb.append(" AND A3.START_TIME_ >= TO_DATE('").append(DateUtils.formatDate(this.condition.getSearchStartTime()));
			sb.append(" 00:00:00', 'YYYY-MM-DD HH24:MI:SS') ");
		}
		if(null != this.condition.getSearchEndTime()) {
			sb.append(" AND A3.START_TIME_ <= TO_DATE('").append(DateUtils.formatDate(this.condition.getSearchEndTime()));
			sb.append(" 23:59:59', 'YYYY-MM-DD HH24:MI:SS') ");
		}
		sb.append(" LEFT JOIN ACT_RU_VARIABLE A4 ON A4.PROC_INST_ID_ = T1.PROC_INST_ID_ AND A4.NAME_ = 'businessId' ");
		sb.append(" LEFT JOIN TS_USER_INFO T5 ON A3.START_USER_ID_ = T5.RID ");

		sb.append(" ORDER BY T1.PRIORITY_ DESC, T1.CREATE_TIME_ DESC ");

		String h1 = sb.toString();
		String h2 = "SELECT COUNT(*) FROM (" + sb.toString() + ") ";
		return new String[] { h1, h2 };
	}

	public TreeNode getSearchTreeNode() {
		return searchTreeNode;
	}

	public void setSearchTreeNode(TreeNode searchTreeNode) {
		this.searchTreeNode = searchTreeNode;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String getNodeId() {
		return nodeId;
	}

	public void setNodeId(String nodeId) {
		this.nodeId = nodeId;
	}

	public String getBusinessId() {
		return businessId;
	}

	public void setBusinessId(String businessId) {
		this.businessId = businessId;
	}

	public TdFlowTaskCondBean getCondition() {
		return condition;
	}

	public void setCondition(TdFlowTaskCondBean condition) {
		this.condition = condition;
	}

	public Integer getSearchTrust() {
		return searchTrust;
	}

	public void setSearchTrust(Integer searchTrust) {
		this.searchTrust = searchTrust;
	}
	
}
