package com.chis.modules.flow.web;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.lang.reflect.Field;
import java.sql.Clob;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import com.chis.common.bean.FastReportData;
import com.chis.common.utils.ClobTransfer;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.Reflections;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.ZipUtils;
import com.chis.modules.flow.logic.FrptTestJavaBean;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.DownLoadUtil;
import com.chis.modules.system.web.FacesBean;

@ManagedBean(name = "fastReportTestBean")
@ViewScoped
public class FastReportTestBean extends FacesBean{

	/**
	 * 
	 */
	private static final long serialVersionUID = -4215955970500622195L;
	
	private String hello = "Hello,World!";
	/**ejb session bean*/
	private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    /**xml 数据集*/
    private String xmlData;
    

	public FastReportTestBean() {
		this.print();
	}
	
	public void init() {
		
	}
	
	private String getImg() {
		String filePath = "c:/CHISCDC_LOGO2.bmp";
		File file = new File(filePath);
		try {
			return DownLoadUtil.uploadFile2Database(new FileInputStream(file));
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public void print() {
		/**
		 * 1.组织数据集
		 * 2.转xml
		 */
		FrptTestJavaBean javaBean = new FrptTestJavaBean("超管", null, 24, new Date());
	
		Clob clob = ClobTransfer.stringToClob(getImg());
		javaBean.setClob(clob);
		FrptTestJavaBean javaBean2 = new FrptTestJavaBean("张三", null, 36, new Date());
		
		List<FrptTestJavaBean> userList = new ArrayList<FrptTestJavaBean>(1);
		userList.add(javaBean);
		userList.add(javaBean2);
		
		//真正的数据集
		List<FastReportData> rptDataList = new ArrayList<FastReportData>();
		FastReportData data = new FastReportData(FrptTestJavaBean.class,"users", userList);
		rptDataList.add(data);
		
		// 建立document对象
		Document document = DocumentHelper.createDocument();
		// XML根节点
		Element reportElement = document.addElement("report-config");
		// 打印设置toXml
		reportElement.addAttribute("optype", "0");
		reportElement.addAttribute("filepath", "");
		reportElement.addAttribute("copies", "1");
		reportElement.addAttribute("pageNumbers", "");
		
		// 数据集内容toXml
		Element datapacketElement = reportElement.addElement("datapacket");
		
		if(null != rptDataList && rptDataList.size() > 0) {
			for(FastReportData frd : rptDataList) {
				Element dataElement = datapacketElement.addElement("data");
				dataElement.addAttribute("datename", frd.getDataname());
				
				Element fieldsElement = null;
				Element rowdataElement = null;
				
				List dataList = frd.getList();
				for(Object obj : dataList) {
					Field[] fields = obj.getClass().getDeclaredFields();
					//字段说明只执行一次
					if(null == fieldsElement) {
						fieldsElement = dataElement.addElement("fields");
						if(null != fields && fields.length > 0) {
							for(Field field : fields) {
								field.setAccessible(true);
								try {
									if (!field.getName().toUpperCase().equals("serialVersionUID".toUpperCase())) {
										Element fieldElement = fieldsElement.addElement("field");
										fieldElement.addAttribute("attrname", field.getName());
										fieldElement.addAttribute("fieldtype",field.getType().getSimpleName());
									}
								} catch (IllegalArgumentException e) {
									e.printStackTrace();
									throw new RuntimeException(e);
								}
							}
						}
					}
					
					if(null == rowdataElement) {
						rowdataElement = dataElement.addElement("rowdata");
					}
					Element rowElement = rowdataElement.addElement("row");
					for(Field field : fields) {
						try {
							if (!field.getName().toUpperCase().equals("serialVersionUID".toUpperCase())) {
								Object value = Reflections.invokeGetter(field.getName(), obj);
								if(null != value && value instanceof Clob) {
									rowElement.addAttribute(field.getName(), getImg());
								}else if(null != value && value instanceof Date) {
									rowElement.addAttribute(field.getName(), DateUtils.formatDateTime(new Date()));
								} else {
									rowElement.addAttribute(field.getName(), null == value ? "" : value.toString());
								}
							}
						} catch (Exception e) {
							e.printStackTrace();
							throw new RuntimeException(e);
						}
					}
				}
			}
		}
		
		
		// 数据关系toXml
		
		// 压缩xml字符串
		try {
			System.err.println("【xmlData】：" + document.asXML());
			this.xmlData = ZipUtils.gzip(document.asXML());
			// xml字符串去除回车
			if (this.xmlData != null) {
				this.xmlData = this.xmlData.replace("\r\n", "");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public String getHello() {
		return hello;
	}

	public void setHello(String hello) {
		this.hello = hello;
	}

	public String getXmlData() {
		return xmlData;
	}

	public void setXmlData(String xmlData) {
		this.xmlData = xmlData;
	}
	
}
