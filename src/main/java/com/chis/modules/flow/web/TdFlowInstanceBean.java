package com.chis.modules.flow.web;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.activiti.entity.TdFlowDef;
import com.chis.activiti.entity.TdFlowType;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.web.FacesBean;

/**
 * 发起流程
 * <AUTHOR>
 */
@ManagedBean(name = "tdFlowInstanceBean")
@ViewScoped
public class TdFlowInstanceBean extends FacesBean{

	private static final long serialVersionUID = 2695401462098651426L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	
	private List<TdFlowType> leftList;
	private List<TdFlowType> rightList;
	private TdFlowDef tdFlowDef;

	@PostConstruct
	public void init() {
		this.searchAction();
	}
	
	public void searchAction() {
		this.leftList = new ArrayList<TdFlowType>(0);
		this.rightList = new ArrayList<TdFlowType>(0);
		List<TdFlowType> typeList = this.flowBusinessService.searchMyFlowTypes(this.sessionData.getUser().getRid());
		if(null != typeList && typeList.size() > 0) {
			for(int i=0; i<typeList.size(); i++) {
				if(i % 2 == 0) {
					this.leftList.add(typeList.get(i));
				}else {
					this.rightList.add(typeList.get(i));
				}
			}
			typeList.clear();
		}
	}
	
	/**
	 * 启动流程
	 */
	public String startProcess() {
		//System.out.println("--startProcess--");
		JsfUtil.getRequestMap().put("tdFlowDef", this.tdFlowDef);
		return "tdFlowInstanceEdit";
	}


	public List<TdFlowType> getLeftList() {
		return leftList;
	}

	public void setLeftList(List<TdFlowType> leftList) {
		this.leftList = leftList;
	}

	public List<TdFlowType> getRightList() {
		return rightList;
	}

	public void setRightList(List<TdFlowType> rightList) {
		this.rightList = rightList;
	}

	public TdFlowDef getTdFlowDef() {
		return tdFlowDef;
	}

	public void setTdFlowDef(TdFlowDef tdFlowDef) {
		this.tdFlowDef = tdFlowDef;
	}

	
}
