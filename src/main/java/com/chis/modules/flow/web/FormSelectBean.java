package com.chis.modules.flow.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.apache.commons.lang.StringUtils;
import org.primefaces.context.RequestContext;

import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.entity.TdFormDef;
import com.chis.modules.system.web.FacesBean;

/**
 * 动态表单选择
 * 
 * <AUTHOR>
 * @createDate 2015年12月16日
 */
@ManagedBean(name = "formSelectBean")
@ViewScoped
public class FormSelectBean extends FacesBean {
	private static final long serialVersionUID = -1239036568323798376L;
	private FlowBusinessServiceImpl flowBusinessService = (FlowBusinessServiceImpl) SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	/* 系统选项 */
	private String searchName;
	/* 页面数据 */
	private List<TdFormDef> srclist = new ArrayList<TdFormDef>();
	/* 显示数据 */
	private List<TdFormDef> displayList;
	/* 选择的页面 */
	private TdFormDef tdFormDef;

	public FormSelectBean() {
		srclist = flowBusinessService.findTdFormDefs();
		if (srclist == null) {
			srclist = new ArrayList<TdFormDef>();
		}

		searchAction();
	}

	public void searchAction() {
		this.displayList = new ArrayList<TdFormDef>();
		if (srclist != null && srclist.size() > 0) {
			for (TdFormDef tdFormDef : srclist) {
				if (StringUtils.isBlank(searchName) || tdFormDef.getFormName().indexOf(searchName) != -1) {
					displayList.add(tdFormDef);
				}
			}
		}
	}

	public void selectAction() {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("tdFormDef", tdFormDef);
		RequestContext.getCurrentInstance().closeDialog(map);
	}

	public String getSearchName() {
		return searchName;
	}

	public void setSearchName(String searchName) {
		this.searchName = searchName;
	}

	public List<TdFormDef> getSrclist() {
		return srclist;
	}

	public void setSrclist(List<TdFormDef> srclist) {
		this.srclist = srclist;
	}

	public List<TdFormDef> getDisplayList() {
		return displayList;
	}

	public void setDisplayList(List<TdFormDef> displayList) {
		this.displayList = displayList;
	}

	public TdFormDef getTdFormDef() {
		return tdFormDef;
	}

	public void setTdFormDef(TdFormDef tdFormDef) {
		this.tdFormDef = tdFormDef;
	}

}
