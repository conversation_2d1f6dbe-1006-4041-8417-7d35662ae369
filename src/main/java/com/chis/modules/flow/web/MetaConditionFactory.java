package com.chis.modules.flow.web;

import com.chis.modules.system.logic.MetaCondition;
import com.chis.modules.system.logic.SessionData;

/**
 * 生产模板元素条件的工厂类
 * <AUTHOR> 2014-12-1
 *
 */
public final class MetaConditionFactory {
	
	private MetaConditionFactory(){}
	
	/**
	 * 生产模板元素条件
	 * @param sessionData 放在session中的javaBean
	 * @return 返回条件对象
	 */
	public static MetaCondition produceCondition(SessionData sessionData) {
		//初始化解析元素的查询条件
		MetaCondition condition = new MetaCondition();
		condition.setUserId(sessionData.getUser().getRid());
		condition.setUnitId(sessionData.getUser().getTsUnit().getRid());
		return condition;
	}
	
	/**
	 * 生产模板元素条件
	 * @param sessionData 放在session中的javaBean
	 * @param json json对象
	 * @param businessId 业务主键ID
	 * @return 返回条件对象
	 */
	public static MetaCondition produceCondition(SessionData sessionData,String businessId, String json) {
		//初始化解析元素的查询条件
		MetaCondition condition = new MetaCondition();
		condition.setUserId(sessionData.getUser().getRid());
		condition.setUnitId(sessionData.getUser().getTsUnit().getRid());
		condition.setBusinessId(businessId);
		condition.setJson(json);
		return condition;
	}
	
	/**
	 * 生产模板元素条件
	 * @param sessionData 放在session中的javaBean
	 * @param json json对象
	 * @return 返回条件对象
	 */
	public static MetaCondition produceCondition(SessionData sessionData, String json) {
		//初始化解析元素的查询条件
		MetaCondition condition = new MetaCondition();
		condition.setUserId(sessionData.getUser().getRid());
		condition.setUnitId(sessionData.getUser().getTsUnit().getRid());
		condition.setJson(json);
		return condition;
	}

}
