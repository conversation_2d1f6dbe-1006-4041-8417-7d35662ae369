package com.chis.modules.flow.web;

import java.io.File;
import java.io.FileInputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.chis.activiti.entity.TdFlowDef;
import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.ClobTransfer;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MapUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.flow.rptvo.NodeDealVo;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.utils.DownLoadUtil;

/**
 * 流程与业务交互的上下文
 * 
 * 初始化条件：
 * 
 * <AUTHOR> 2015-01-27
 */
public class FlowContext implements Serializable{

	private static final long serialVersionUID = -5530220148489052321L;
	
	/**activiti当前任务ID*/
	private String activitiTaskId;
	/**当前流程实例ID*/
	private String activitiInstanceId;
	/**当前流程定义的key*/
	private String activitiDefKey;
	/**当前流程实例ID*/
	private String flowNodeId;
	/**当前业务主键的json*/
	private String businessIdJSON;
	/**当前节点对象*/
	private TdFlowNode tdFlowNode;
	/**当前流程定义对象*/
	private TdFlowDef tdFlowDef;
	/**流程发起者的ID*/
	private String startUserId;
    /**虚拟路径*/
    private static final String VIRTUAL_PATH = JsfUtil.getAbsolutePath();
	/**流程标题*/
	private String businessKey;
	
	//ejb session beans
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	
	
	private FlowContext() {
		
	}
	
	/**
	 * 待办任务处理类初始化入口
	 * @param activitiInstanceId 流程实例ID
	 * @param userId 系统当前用户ID
	 */
	public FlowContext(String activitiInstanceId, Integer userId) {
		this.activitiInstanceId = activitiInstanceId;
		this.initByProcessId(userId);
	}

	/**
	 * 待办任务处理类初始化入口
	 * @param activitiTaskId 任务ID
	 * @param flowNodeId 节点ID
	 * @param businessIdJSON 业务数据的JSON变量
	 */
	public FlowContext(String activitiTaskId, String flowNodeId, String businessIdJSON) {
		this.activitiTaskId = activitiTaskId;
		this.flowNodeId = flowNodeId;
		this.businessIdJSON = businessIdJSON;
		this.initByTaskId();
	}
	
	/**
	 * 获取各节点的处理情况
	 * @param userId 当前登录人ID
	 * @return 0-节点名称 1-任务处理人ID 2-任务key 3-结束时间
	 */
	@Deprecated
	public List<Object[]> findTaskInfoInProcess(Integer userId) {
		List<Object[]> rtnList = null;
		if(StringUtils.isNotBlank(this.activitiInstanceId)) {
			rtnList = this.flowBusinessService.findTaskInfoInProcess(this.activitiInstanceId);
			if(!this.processIsEnded()) {
				Object[] o = new Object[] {this.tdFlowNode.getNodeName(), userId, this.tdFlowNode.getActNodeId(), DateUtils.formatDateTime(new Date())};
				rtnList.add(o);
			}
		}
		return rtnList;
	}
	
	/**
	 * 获取各节点的处理情况
	 * @param user 当前登录人ID
	 * @return 节点处理对象集合
	 * 
 	 * <p>修订内容：没有走的节点，节点名称修改</p>
	 * 
 	 * @MethodReviser qrr,2018年1月2日,findTasksDealedInfo
	 */	
	public List<NodeDealVo> findTasksDealedInfo(TsUserInfo user) {
		List<NodeDealVo> rtnList = new ArrayList<NodeDealVo>();
		if(StringUtils.isNotBlank(this.activitiInstanceId)) {
			List<Object[]> list = this.flowBusinessService.findTaskInfoInProcess(this.activitiInstanceId);
			
			/**
			 * 用于存放已经加过的节点任务的taskDefKey
			 */
			Map<String, String> map = new HashMap<String, String>();
			
			if(null != list && list.size() > 0) {
				for(Object[] o : list) {
					/**
					 * 0-节点名称 1-任务处理人ID 2-任务key 3-结束时间 <br/>
					 * 4-处理人的用户名 5-处理人所在科室名称 6-处理人所在单位名称 <br/>
					 * 7-处理人所在单位简称 8-处理人移动电话 9-处理人的签名 10-处理意见 <br/>
					 */
					NodeDealVo vo = new NodeDealVo();
					vo.setNodeName(StringUtils.objectToString(o[0]));
					vo.setUserId(StringUtils.objectToString(o[1]));
					vo.setTaskDefKey(StringUtils.objectToString(o[2]));
					if(null != o[3]) {
						vo.setDealTime(DateUtils.parseDate(o[3].toString()));
					}
					vo.setUsername(StringUtils.objectToString(o[4]));
					vo.setOfficeName(StringUtils.objectToString(o[5]));
					vo.setUnitName(StringUtils.objectToString(o[6]));
					vo.setUnitSimpleName(StringUtils.objectToString(o[7]));
					vo.setMobilePhone(StringUtils.objectToString(o[8]));
					if(null != o[9]) {
						try {
							File file = new File(VIRTUAL_PATH+o[9].toString());
							if(file.exists()) {
								vo.setSignImg(ClobTransfer.stringToClob(DownLoadUtil.uploadFile2Database(new FileInputStream(file))));
							}
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
					vo.setAdvice(StringUtils.objectToString(o[10]));
					
					map.put(vo.getTaskDefKey(), vo.getTaskDefKey());
					rtnList.add(vo);
				}
			}
			
			try {
				if(!this.processIsEnded()&&null!=tdFlowNode) {
					NodeDealVo vo = new NodeDealVo();
					vo.setNodeName(this.tdFlowNode.getNodeName());
					vo.setUserId(StringUtils.objectToString(user.getRid()));
					vo.setTaskDefKey(this.tdFlowNode.getActNodeId());
					vo.setDealTime(new Date());
					vo.setUsername(user.getUsername());
					if(null != user.getTbSysEmp() && null != user.getTbSysEmp().getTsOffice()) {
						vo.setOfficeName(user.getTbSysEmp().getTsOffice().getOfficename());
						vo.setMobilePhone(user.getTbSysEmp().getMbNum());
						if(StringUtils.isNotBlank(user.getTbSysEmp().getPsnSign())) {
							File file = new File(VIRTUAL_PATH + user.getTbSysEmp().getPsnSign());
							if(file.exists()) {
								vo.setSignImg(ClobTransfer.stringToClob(DownLoadUtil.uploadFile2Database(new FileInputStream(file))));
							}
						}
					}
					if(null != user.getTsUnit()) {
						vo.setUnitName(user.getTsUnit().getUnitname());
						vo.setUnitSimpleName(user.getTsUnit().getUnitSimpname());
					}
					
					map.put(vo.getTaskDefKey(), vo.getTaskDefKey());
					rtnList.add(vo);
				}
				
				/**
				 * 没有走的节点也要传，只是空
				 */
				if(null != this.tdFlowDef) {
					List<TdFlowNode> nodeList = this.tdFlowDef.getTdFlowNodeList();
					if(null != nodeList && nodeList.size() > 0) {
						for(TdFlowNode node : nodeList) {
							if(!map.containsKey(node.getActNodeId())) {
								NodeDealVo vo = new NodeDealVo();
								vo.setNodeName(node.getNodeName());
								vo.setUserId("");
								vo.setTaskDefKey(node.getActNodeId());
								vo.setDealTime(new Date());
								vo.setUsername("");
								vo.setOfficeName("");
								vo.setMobilePhone("");
								vo.setSignImg(null);
								vo.setUnitName(user.getTsUnit().getUnitname());
								vo.setUnitSimpleName(user.getTsUnit().getUnitSimpname());
								
								map.put(vo.getTaskDefKey(), vo.getTaskDefKey());
								rtnList.add(vo);
							}
						}
					}
				}
				
			} catch (Exception e) {
				e.printStackTrace();
			}

		}		
		return rtnList;
	}
	
	/**
	 * 流程是否结束
	 * @return 流程是否结束 
	 */
	public Boolean processIsEnded() {
		if(StringUtils.isNotBlank(this.activitiInstanceId)) {
			return this.flowBusinessService.processIsEnded(this.activitiInstanceId);
		}
		return null;
	}
	
	/**
	 * 获取当前节点对应界面的参数
	 * @return 界面参数
	 */
	public String getPageParam() {
		if(null != this.tdFlowNode && null != this.tdFlowNode.getTdFlowNodePage()) {
			return this.tdFlowNode.getTdFlowNodePage().getPageParm();
		}
		return null;
	}
	
	//======================================================================
	
	/**
	 * 根据流程实例ID进行初始化
	 */
	private void initByProcessId(Integer userId) {
		Map<String, Object> initMap = this.flowBusinessService.initFlowContextByProcess(this.activitiInstanceId, userId);
		MapUtils.initFields(initMap, this);
	}
	
	/**
	 * 根据任务ID初始化上下文对象的各字段值
	 */
	private void initByTaskId() {
		Map<String, Object> initMap = this.flowBusinessService.initFlowContextByTask(this.activitiTaskId);
		MapUtils.initFields(initMap, this);
	}
	
	//getters and setters
	
	public String getActivitiTaskId() {
		return activitiTaskId;
	}

	public String getActivitiInstanceId() {
		return activitiInstanceId;
	}

	public String getActivitiDefKey() {
		return activitiDefKey;
	}

	public String getFlowNodeId() {
		return flowNodeId;
	}

	public String getBusinessIdJSON() {
		return businessIdJSON;
	}

	public TdFlowNode getTdFlowNode() {
		return tdFlowNode;
	}

	public TdFlowDef getTdFlowDef() {
		return tdFlowDef;
	}

	public String getStartUserId() {
		return startUserId;
	}

	public String getBusinessKey() {
		return businessKey;
	}

	public void setBusinessKey(String businessKey) {
		this.businessKey = businessKey;
	}
	
}
