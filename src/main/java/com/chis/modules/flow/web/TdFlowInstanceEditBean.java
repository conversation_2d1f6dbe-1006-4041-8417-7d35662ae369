package com.chis.modules.flow.web;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.activiti.engine.history.HistoricTaskInstance;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.entity.TdFlowDef;
import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.entity.TdFlowNodeBtn;
import com.chis.activiti.enumn.FlowBtnType;
import com.chis.activiti.enumn.FlowInType;
import com.chis.activiti.enumn.SupportAssigeeType;
import com.chis.activiti.javabean.BusinessButton;
import com.chis.activiti.service.IBusinessService;
import com.chis.activiti.service.IFlowBeanService;
import com.chis.activiti.service.IFlowButtons;
import com.chis.activiti.service.IFlowPassBeanService;
import com.chis.activiti.service.ISetAssignMen;
import com.chis.activiti.service.ISetAssignee;
import com.chis.activiti.service.impl.ActivitiServiceImpl;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.activiti.utils.BusinessIdUtil;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MapUtils;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SimplePropertyFilter;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.flow.logic.FlowChoiceConditionBean;
import com.chis.modules.system.entity.TsTxtype;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.TxType;
import com.chis.modules.system.interfaces.IFastReport;
import com.chis.modules.system.logic.MetaCondition;
import com.chis.modules.system.logic.OtherTempCon;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.protocol.IMsgSend;
import com.chis.modules.system.protocol.PushMsgToAppImpl;
import com.chis.modules.system.protocol.SendEmailToUserImpl;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.MsgSendUtil;
import com.chis.modules.system.web.FacesBean;

/**
 * 发起流程的表单保存并提交 <br/>
 * 有多种链接进来： <br/>
 * 1.发起流程模块，会将TdFlowDef对象传递进来 <br/>
 * 2.业务模块传递进来，通过url传递流程定义的KEY_,URL的KEY为process <br/>
 * 还有from=url,是从哪儿链接进来的，以及业务自己传的值 <br/>
 * 3.菜单链接，通过url传递流程定义的KEY_,URL的KEY为process <br/>
 * 4.从其他流程链接进来的
 * 
 * <AUTHOR>
 * 
 * 
 * <p>修订内容：添加自定义按钮</p>
 *
 * @ClassReviser qrr,2018年2月27日,TdFlowInstanceEditBean
 */
@ManagedBean(name = "tdFlowInstanceEditBean")
@ViewScoped
public class TdFlowInstanceEditBean extends FacesBean implements
		IFlowBeanService, IFlowPassBeanService, IFlowButtons {

	private static final long serialVersionUID = 2695401462098651426L;
	/** 存在session中的对象 */
	private CommServiceImpl commService = SpringContextHolder
			.getBean(CommServiceImpl.class);
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder
			.getBean(FlowBusinessServiceImpl.class);
	private ActivitiServiceImpl activitiServiceImpl = SpringContextHolder
			.getBean(ActivitiServiceImpl.class);

	/** 任务ID */
	private String taskId;
	/** 节点对象 */
	private TdFlowNode tdFlowNode = new TdFlowNode();
	/** 流程定义对象 */
	private TdFlowDef tdFlowDef;
	/** 流程意见 */
	private String advice;
	/** 是否是未提交 */
	private boolean noSubmit = Boolean.TRUE;
	/** 操作类型 */
	private String actionType;
	/** 业务表单ID */
	private String businessId;
	/**
	 * 流程表单接口
	 */
	private IBusinessService businessService;
	/**
	 * 设置待办人接口
	 */
	private ISetAssignee assigneeService;

	/**
	 * 1-发起流程进行跳转的, 4-从流程发起的流程，返回时返回发起的流程节点 2-业务模块跳转的 3-菜单
	 * 
	 */
	private int turn;
	/**
	 * 业务模块跳转进来，业务模块的url
	 */
	private String fromUrl = null;
	/** 能否显示的按钮 key-按钮类型，见FlowBtnType value-TdFlowNodeBtn对象 */
	private Map<String, TdFlowNodeBtn> btnDispMap;
	/** 是否有打印功能 */
	private boolean printable = false;
	/** 流程上下文 */
	private FlowContext flowContext;
	/** 流程标题 */
	private String businessKey;
	/** 从流程节点发起的流程，返回时的参数 **/
	private Map<String, String> flowBackParam;
	/**业务模块提供的按钮集合*/
	private List<BusinessButton> buttonList = new ArrayList<BusinessButton>(0);

	public TdFlowInstanceEditBean() {
		this.init();
	}

	public void init() {
		this.tdFlowDef = (TdFlowDef) JsfUtil.getRequestMap().get("tdFlowDef");
		if (null != this.tdFlowDef) {
			this.turn = 1;
			// 获取该流程的第一个节点
			this.tdFlowNode = this.flowBusinessService
					.findFirstNode(this.tdFlowDef.getRid());
			this.fromUrl = (String) JsfUtil.getRequestMap().get("from");
			if (StringUtils.isNotBlank(this.fromUrl)) {
				flowBackParam = (Map<String, String>) JsfUtil.getRequestMap()
						.get("param");
			}
		} else {
			// 流程定义的KEY_
			String processKey = JsfUtil.getRequestParameterMap().get("process");
			this.fromUrl = JsfUtil.getRequestParameterMap().get("from");
			if (StringUtils.isNotBlank(processKey)) {
				if (StringUtils.isNotBlank(this.fromUrl)) {
					this.turn = 2;
				} else {
					this.turn = 3;
				}
				this.tdFlowDef = this.flowBusinessService
						.findFlowDefByKey(processKey);
				// 获取该流程的第一个节点
				this.tdFlowNode = this.tdFlowDef.getTdFlowNodeList().get(0);
			} else {
				throw new RuntimeException("流程配置有误！");
			}
		}

		/**
		 * 初始化按钮
		 */
		if (null != this.tdFlowNode) {
			System.err.println("JspUrl:======" + this.tdFlowNode.getJspUrl());
			this.initBtnMap(this.tdFlowNode);
		}
		// 告诉业务Bean当前托管Bean的名字
		JsfUtil.getRequestMap().put("manageBeanName", "tdFlowInstanceEditBean");
	}

	/**
	 * 保存业务表单
	 */
	public String saveAction() {
		if (null != this.businessService) {
			/**
			 * 返回null表示保存失败，否则保存成功！
			 */
			this.businessId = this.businessService.saveAction();
			if (StringUtils.isBlank(this.businessId)) {
				return null;
			} else {
				this.buildBusinessTitle();

				// 如果为空，需要发起流程
				if (StringUtils.isBlank(this.taskId)) {
					String piId = this.flowBusinessService
							.startProcessInstanceNew(this.tdFlowDef,
									this.sessionData.getUser().getRid(),
									this.businessKey, this.businessId,
									this.tdFlowNode.getActNodeId());
					this.taskId = this.flowBusinessService
							.findCurrentTaskId(piId);

					// 初始化流程上下文
					this.flowContext = new FlowContext(piId, this.sessionData
							.getUser().getRid());
				} else {
					// 需要保存最新的bid
					Map<String, Object> map = new HashMap<String, Object>();
					map.put("businessId", this.businessId);
					this.flowBusinessService.saveVariable(this.taskId, map);
				}

				/**
				 * 保存流程标题
				 */
				this.flowBusinessService.saveProcessTitle(
						this.flowContext.getActivitiInstanceId(),
						this.businessKey);

				JsfUtil.getRequestMap().put("taskId", this.taskId);
				this.businessService.afterSaveAction();
				if ("saveAction".equals(this.actionType)) {
					JsfUtil.addSuccessMessage("保存成功！");
					return null;
				}
			}
			return this.businessId;
		}
		return null;
	}

	/**
	 * 提交下一步
	 */
	public String submitAction() {
		this.actionType = null;
		/**
		 * 返回null表示保存失败，否则保存成功！
		 */
		this.businessId = this.saveAction();
		if (StringUtils.isBlank(this.businessId)) {
			return null;
		}

		/**
		 * 首先判断当前节点是否是拟办节点， 如果是拟办节点，需要弹出被拟办的节点，以供选人，选完人提交下一步流程；
		 * 如果不是拟办节点，再判断当前节点是否是自由跳转节点， 如果是，直接提交下一步流程；
		 * 如果不是自由跳转，而是选择待办人跳转，则弹出选人框，并提交下一步流程 如果下一节点是空，则直接提交
		 */
		FlowInType flowInType = this.tdFlowNode.getFlowInType();
		switch (flowInType) {
		// 拟办跳转
		case NBJD:
			this.nbtzAction();
			break;

		// 自由跳转
		case ZYTZ:
			this.zytzAction();
			break;

		// 选择跳转
		case XZTZ:
			/**
			 * 1.下一步要走多个节点 2.下一步要走1个节点 3.下一步是结束节点，但是业务模块传进来了待办人 4.下一步是结束节点
			 * 
			 * 1和2的情况都是根据配置的规则，和业务模块提供的待办人<br/>
			 * 进行或、且等合并
			 */
			List<TdFlowNode> nextNodeList = this.flowBusinessService
					.findNextNodeList(this.taskId, this.tdFlowNode
							.getTdFlowDef().getRid().toString(),
							this.businessService.addVariables(),
							this.tdFlowNode);
			// 业务模块提供待办人的接口
			ISetAssignMen assignMen = (ISetAssignMen) this.businessService;
			String assigeeJson = assignMen.supportTaskMen(); // 待办人
			SupportAssigeeType supportType = assignMen.supportType(); // 关系

			if (null == nextNodeList || nextNodeList.size() == 0) {
				// 下一步应该是结束节点，要判断业务模块是否提供待办人，如果提供还是要显示出来
				if (StringUtils.isNotBlank(assigeeJson)) {
					this.gdxrAction(assigeeJson, supportType);
				} else {
					this.zytzAction();
				}
			} else if (nextNodeList.size() == 1) {
				// 下一步只有1个节点
				this.xztzAction(nextNodeList.get(0), assigeeJson, supportType);
			} else {
				// 下一步要走多个节点
				this.djdXztzAction(nextNodeList, assigeeJson, supportType);
			}
			break;
		// switch end
		}
		return null;
	}

	/**
	 * 拟办跳转，打开拟办跳转的弹出框
	 */
	private void nbtzAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 830,
				null, 500);

		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();

		List<String> paramList = new ArrayList<String>(1);
		paramList.add(this.tdFlowNode.getTdFlowDef().getRid().toString());
		paramMap.put("defId", paramList);

		List<String> paramList2 = new ArrayList<String>(1);
		paramList2.add(this.tdFlowNode.getRid().toString());
		paramMap.put("nodeId", paramList2);

		List<String> paramList3 = new ArrayList<String>(1);
		paramList3.add(this.taskId);
		paramMap.put("taskId", paramList3);

		List<String> paramList4 = new ArrayList<String>(1);
		paramList4.add(this.businessId);
		paramMap.put("businessId", paramList4);

		RequestContext.getCurrentInstance().openDialog("tdFlowSubmitNBTZ",
				options, paramMap);
	}

	/**
	 * 自由跳转，打开自由跳转的弹出框
	 */
	private void zytzAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 830,
				null, 500);

		// 构建传递的条件
		FlowChoiceConditionBean bean = new FlowChoiceConditionBean();
		// 当前节点对象
		bean.setCurrentFlowNodeJSON(this.tdFlowNode.getRid() == null ? ""
				: this.tdFlowNode.getRid().toString());
		// 当前业务主键的json
		bean.setBusinessIdJSON(this.businessId);

		String condition = JSON.toJSONString(bean);

		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();

		// 用于TdFlowHisTaskBean获取
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(StringUtils.encode(condition));
		paramMap.put("condition", paramList);

		RequestContext.getCurrentInstance().openDialog("tdFlowSubmitZYTZ",
				options, paramMap);
	}

	/**
	 * 选择跳转，打开选择跳转的弹出框
	 */
	private void xztzAction(TdFlowNode nextNode, String assigeeJson,
			SupportAssigeeType supportType) {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 830,
				null, 500);

		// 构建传递的条件
		FlowChoiceConditionBean bean = new FlowChoiceConditionBean();
		// 下一个节点
		bean.setNextFlowNodeJSON(nextNode.getRid() == null ? "" : nextNode
				.getRid().toString());
		// 当前节点对象
		bean.setCurrentFlowNodeJSON(this.tdFlowNode.getRid() == null ? ""
				: this.tdFlowNode.getRid().toString());
		// 当前节点的任务ID
		bean.setActivitiTaskId(this.taskId);
		// 业务模块提供的待办人
		bean.setAssigeesJSON(assigeeJson);
		// 业务模块提供的待办人与规则提供的待办人最后要形成集合的关系
		bean.setAssigeeType(supportType);
		// 当前业务主键的json
		bean.setBusinessIdJSON(this.businessId);
		/** 业务模块提供规则的json查询条件 */
		if (this.businessService instanceof ISetAssignee) {
			ISetAssignee setAssignee = (ISetAssignee) this.businessService;
			bean.setAssigneeConditionJSON(setAssignee
					.settingAssigneeCondition());
		}

		String condition = JSON.toJSONString(bean);

		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		// 用于TdFlowHisTaskBean获取
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(StringUtils.encode(condition));
		paramMap.put("condition", paramList);

		RequestContext.getCurrentInstance().openDialog("tdFlowSubmitXRTZ",
				options, paramMap);
	}

	/**
	 * 多节点选择跳转，打开选择跳转的弹出框
	 */
	private void djdXztzAction(List<TdFlowNode> nextNodeList,
			String assigeeJson, SupportAssigeeType supportType) {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 830,
				null, 500);

		// 构建传递的条件
		FlowChoiceConditionBean bean = new FlowChoiceConditionBean();
		StringBuffer nextNodes = new StringBuffer();
		if (null != nextNodeList && nextNodeList.size() > 0) {
			for (TdFlowNode t : nextNodeList) {
				nextNodes.append(",").append(t.getRid());
			}
			// 下一步的节点
			bean.setNextFlowNodeJSON(nextNodes.substring(1));
		}
		// 当前节点对象
		bean.setCurrentFlowNodeJSON(this.tdFlowNode.getRid() == null ? ""
				: this.tdFlowNode.getRid().toString());

		// 当前节点的任务ID
		bean.setActivitiTaskId(this.taskId);
		// 当前业务主键的json
		bean.setBusinessIdJSON(this.businessId);
		/** 业务模块提供规则的json查询条件 */
		if (this.businessService instanceof ISetAssignee) {
			ISetAssignee setAssignee = (ISetAssignee) this.businessService;
			bean.setAssigneeConditionJSON(setAssignee
					.settingAssigneeCondition());
		}

		String condition = JSON.toJSONString(bean);

		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		// 用于TdFlowHisTaskBean获取
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(StringUtils.encode(condition));
		paramMap.put("condition", paramList);

		RequestContext.getCurrentInstance().openDialog("tdFlowSubmitDRTZ",
				options, paramMap);
	}

	/**
	 * 此功能只是为了应急最后一个节点需要发起一个新的流程用的<br/>
	 * supportType对应SupportAssigeeType的值
	 */
	private void gdxrAction(String assigeeJson, SupportAssigeeType supportType) {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 830,
				null, 500);

		// 构建传递的条件
		FlowChoiceConditionBean bean = new FlowChoiceConditionBean();
		// 业务模块提供的待办人
		bean.setAssigeesJSON(assigeeJson);
		// 当前节点对象
		bean.setCurrentFlowNodeJSON(this.tdFlowNode.getRid() == null ? ""
				: this.tdFlowNode.getRid().toString());
		// 当前节点的任务ID
		bean.setActivitiTaskId(this.taskId);
		// 当前业务主键的json
		bean.setBusinessIdJSON(this.businessId);
		// 业务模块提供的待办人与规则提供的待办人最后要形成集合的关系
		bean.setAssigeeType(supportType);

		String condition = JSON.toJSONString(bean);

		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		// 用于TdFlowHisTaskBean获取
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(StringUtils.encode(condition));
		paramMap.put("condition", paramList);

		RequestContext.getCurrentInstance().openDialog("tdFlowSubmitGDXR",
				options, paramMap);
	}

	/**
	 * 选择待办人 : 选人回调 真实的提交方法
	 * 
	 * @param event
	 *            弹出框关闭事件
	 */
	public void onPersonSelect(SelectEvent event) {
		// System.out.println("--onPersonSelect--");
		Map<String, Object> selectedMap = (Map<String, Object>) event
				.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			this.noSubmit = Boolean.FALSE;
			// 业务可能要传一些变量
			Map<String, Object> formVariables = this.businessService
					.addVariables();
			if (null != formVariables) {
				selectedMap.putAll(formVariables);
			}

			String submitMsg = this.businessService.submitAction();
			if (StringUtils.isNotBlank(submitMsg)) {
				/**
				 * 流程意见
				 */
				String advice = (String) selectedMap.get("advice");
				selectedMap.remove("advice");

				/**
				 * 通讯方式
				 */
				List<TsTxtype> selectedTxtypeList = (List<TsTxtype>) selectedMap
						.get("selectedTxtypeList");
				selectedMap.remove("selectedTxtypeList");

				/**
				 * 选择的用户
				 */
				List<TsUserInfo> selectedUserList = (List<TsUserInfo>) selectedMap
						.get("selectedUserList");
				// selectedMap.remove("selectedUserList");

				Integer loginUserId = this.sessionData.getUser().getRid();
				// 执行提交操作
				String pid = this.flowBusinessService.submitFlow(this.taskId,
						selectedMap, advice, loginUserId, loginUserId);
				// 发送消息
				if (null != selectedUserList && selectedUserList.size() > 0) {
					StringBuilder mans = new StringBuilder();
					for (TsUserInfo user : selectedUserList) {
						mans.append(",").append(user.getRid());
					}
					MsgSendUtil.sendNewMsg(mans.toString()
							.replaceFirst(",", ""));
				}
				/**
				 * 根据选择的通讯方式进行发送讯息
				 */
				if (null != selectedTxtypeList && selectedTxtypeList.size() > 0) {
					for (TsTxtype tx : selectedTxtypeList) {
						this.sendTxMsg(tx, selectedUserList, advice);
					}
				}

				JsfUtil.getRequestMap().put("advice", advice);
				this.businessService.afterSubmitAction();
				JsfUtil.addSuccessMessage("提交成功！");
			} else {
				JsfUtil.addErrorMessage("提交失败！");
			}
		}
	}

	/**
	 * 根据通讯方式，发送相关的信息
	 */
	private void sendTxMsg(TsTxtype tx, List<TsUserInfo> userList,
			String content) {
		TxType txType = tx.getTxType();
		if (null != userList && userList.size() > 0) {
			StringBuilder sb = new StringBuilder();
			for (TsUserInfo t : userList) {
				sb.append(",").append(t.getRid());
			}

			switch (txType) {
			case YY:
				this.sendVoiceMsg(tx, sb.toString().replaceFirst(",", ""),
						content);
				break;
			case DX:
				this.sendVoiceMsg(tx, sb.toString().replaceFirst(",", ""),
						content);
				break;
			// switch end
			case APP:
				this.sendPushMsg(sb.toString().replaceFirst(",", ""), content);
				break;
			case EMAIL:
				this.sendEmailMsg(tx, sb.toString().replaceFirst(",", ""),
						content);
				break;
			}
		}
	}

	public void sendEmailMsg(TsTxtype tx, String userIds, String content) {
		Map<Integer, String> emailMap = commService.findEmailByEmail(userIds);
		if (null == emailMap) {
			emailMap = new HashMap<Integer, String>();
		}
		if (null != emailMap && emailMap.size() > 0) {
			Set<Entry<Integer, String>> entrySet = emailMap.entrySet();
			IMsgSend send = SpringContextHolder.getBean(tx.getImplClass());
			for (Entry<Integer, String> entry : entrySet) {
				try {
					String akStr = null;
					// 发送邮件
					akStr = send.sendMsg(entry.getValue(), content);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}

	/** 极光推送 */
	public void sendPushMsg(String userIds, String content) {
		List<TsUserInfo> userList = this.commService.findUserByUserIds(userIds);
		Map<String, String> paramMap = new HashMap<>();
		paramMap.put("taskId", this.taskId);
		paramMap.put("type", "2");
		HistoricTaskInstance findHisTaskById = activitiServiceImpl
				.findHisTaskById(this.taskId);
		if (null != findHisTaskById) {
			String processInstanceId = findHisTaskById.getProcessInstanceId();
			paramMap.put("flowId", processInstanceId);
		}
		if (userList != null && userList.size() > 0) {
			for (TsUserInfo user : userList) {
				new PushMsgToAppImpl(content, paramMap, user.getUserNo())
						.sendJPush();
			}
		}
	}

	/**
	 * 发送语音短信
	 * 
	 * @param userIds
	 *            用户IDS
	 */
	private void sendVoiceMsg(TsTxtype tx, String userIds, String content) {
		/**
		 * 电话号码以业务模块提供的为准，没有就职工表里的电话
		 */
		Map<Integer, String> phoneMap = this.commService.findPhoneNums(userIds);
		if (null == phoneMap) {
			phoneMap = new HashMap<Integer, String>();
		}

		Map<Integer, String> phoneMap2 = this.businessService
				.supplyPhoneNum(userIds);
		if (null != phoneMap2 && phoneMap2.size() > 0) {
			phoneMap.putAll(phoneMap2);
		}

		Map<Integer, String> sendRstMap = new HashMap<Integer, String>();
		if (null != phoneMap && phoneMap.size() > 0) {
			Set<Entry<Integer, String>> entrySet = phoneMap.entrySet();
			/**
			 * 初始化语音发送方式
			 */
			IMsgSend send = SpringContextHolder.getBean(tx.getImplClass());
			if (null != send) {
				for (Entry<Integer, String> entry : entrySet) {
					String akStr = null;
					try {
						// 发送语音
						akStr = send.sendMsg(entry.getValue(), content);
					} catch (Exception e) {
						e.printStackTrace();
						akStr = null;
					}
					sendRstMap.put(entry.getKey(), akStr);
				}
			}

			// 保存语音发送状态记录
			this.flowBusinessService.saveTxRpt(this.taskId, sendRstMap,
					content, tx);
		}
	}

	@Override
	public void setAssigneeService(ISetAssignee assigneeService) {
		this.assigneeService = assigneeService;
	}

	public String backAction() {

		if (this.turn == 1) {
			if (null != flowBackParam) {
				backToFlow();
			}
			// 发起流程界面跳转过来的
			return "tdFlowInstanceList";
		} else {
			// 业务
			try {
				FacesContext.getCurrentInstance().getExternalContext()
						.redirect(this.fromUrl);
			} catch (IOException e) {
				e.printStackTrace();
				throw new RuntimeException(e);
			}
		}
		return null;
	}

	public void backToFlow() {
		try {
			StringBuilder sb = new StringBuilder(
					"/webapp/flow/tdFlowTaskEdit.faces?1=1");
			if (StringUtils.isNotBlank(flowBackParam.get("ph"))) {
				sb.append("&ph=").append(flowBackParam.get("ph"));
			}
			if (StringUtils.isNotBlank(flowBackParam.get("read"))) {
				sb.append("&read=").append(flowBackParam.get("read"));
			}
			if (StringUtils.isNotBlank(flowBackParam.get("trustId"))) {
				sb.append("&trustId=").append(flowBackParam.get("trustId"));
			}
			if (StringUtils.isNotBlank(flowBackParam.get("taskId"))) {
				sb.append("&taskId=").append(flowBackParam.get("taskId"));
			}
			if (StringUtils.isNotBlank(flowBackParam.get("nodeId"))) {
				sb.append("&nodeId=").append(flowBackParam.get("nodeId"));
			}
			if (StringUtils.isNotBlank(flowBackParam.get("businessId"))) {
				sb.append("&businessId=").append(
						flowBackParam.get("businessId"));
			}
			if (StringUtils.isNotBlank(flowBackParam.get("from"))) {
				sb.append("&from=").append(flowBackParam.get("from"));
			}
			FacesContext.getCurrentInstance().getExternalContext()
					.redirect(sb.toString());
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}

	/**
	 * 根据节点的配置，初始化按钮的显示状态
	 */
	private void initBtnMap(TdFlowNode node) {
		this.printable = false;
		this.btnDispMap = new HashMap<String, TdFlowNodeBtn>();

		for (FlowBtnType fbt : FlowBtnType.values()) {
			this.btnDispMap.put(fbt.getTypeNo(), new TdFlowNodeBtn());
		}

		if (null != node.getTdFlowNodeBtnList()
				&& node.getTdFlowNodeBtnList().size() > 0) {
			for (TdFlowNodeBtn nodeBtn : node.getTdFlowNodeBtnList()) {
				if (nodeBtn.getFlowBtnType().equals(FlowBtnType.PREVIEW)
						|| nodeBtn.getFlowBtnType().equals(FlowBtnType.PRINT)
						|| nodeBtn.getFlowBtnType().equals(FlowBtnType.DESIGN)
						|| nodeBtn.getFlowBtnType().equals(FlowBtnType.PRINTER)) {
					this.printable = true;
				}
				nodeBtn.setDisp(true);
				this.btnDispMap.put(nodeBtn.getFlowBtnType().getTypeNo(),
						nodeBtn);
			}
		}
	}

	/**
	 * 报表设计按钮
	 */
	public void designAction() {
		if (null != this.businessService
				&& this.businessService instanceof IFastReport) {
			IFastReport fast = (IFastReport) this.businessService;
			fast.getFastReportBean().designAction();
		} else {
			JsfUtil.addErrorMessage("此模块不支持该功能！");
		}
	}

	/**
	 * 打印按钮
	 */
	public void printAction() {
		if (null != this.businessService
				&& this.businessService instanceof IFastReport) {
			IFastReport fast = (IFastReport) this.businessService;
			fast.getFastReportBean().printReportAction();
		} else {
			JsfUtil.addErrorMessage("此模块不支持该功能！");
		}
	}

	/**
	 * 预览
	 */
	public void preViewAction() {
		if (null != this.businessService
				&& this.businessService instanceof IFastReport) {
			IFastReport fast = (IFastReport) this.businessService;
			fast.getFastReportBean().showReportAction();
		} else {
			JsfUtil.addErrorMessage("此模块不支持该功能！");
		}
	}

	/**
	 * 构建流程标题
	 */
	private void buildBusinessTitle() {
		OtherTempCon b = new OtherTempCon();
		b.setDefId(this.tdFlowDef.getRid());
		// 构建模板的依赖条件
		MetaCondition metaCondition = MetaConditionFactory.produceCondition(
				this.sessionData, BusinessIdUtil.parsePK(this.businessId),
				JSON.toJSONString(b));
		this.businessKey = this.flowBusinessService.analyFlowTilteTempl(
				this.tdFlowDef.getRid(), metaCondition);
		if (StringUtils.isBlank(this.businessKey)) {
			StringBuilder sb = new StringBuilder();
			sb.append(this.sessionData.getUser().getUsername()).append("发起的");
			sb.append(this.tdFlowDef.getDefName()).append("【")
					.append(DateUtils.formatDateTime(new Date())).append("】");
			this.businessKey = sb.toString();
		}
	}

	/**
	 * 再次新增
	 */
	public String addAgainAction() {
		System.out.println("--startProcess--");
		JsfUtil.getRequestMap().put("tdFlowDef", this.tdFlowDef);
		return "tdFlowInstanceEdit";
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public TdFlowNode getTdFlowNode() {
		return tdFlowNode;
	}

	public void setTdFlowNode(TdFlowNode tdFlowNode) {
		this.tdFlowNode = tdFlowNode;
	}

	public String getAdvice() {
		return advice;
	}

	public void setAdvice(String advice) {
		this.advice = advice;
	}

	@Override
	public IBusinessService getBusinessService() {
		return businessService;
	}

	@Override
	public void setBusinessService(IBusinessService businessService) {
		this.businessService = businessService;
	}

	@Override
	public Object getFlowContext() {
		return this.flowContext;
	}

	public boolean isNoSubmit() {
		return noSubmit;
	}

	public void setNoSubmit(boolean noSubmit) {
		this.noSubmit = noSubmit;
	}

	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public int getTurn() {
		return turn;
	}

	public void setTurn(int turn) {
		this.turn = turn;
	}

	public Map<String, TdFlowNodeBtn> getBtnDispMap() {
		return btnDispMap;
	}

	public void setBtnDispMap(Map<String, TdFlowNodeBtn> btnDispMap) {
		this.btnDispMap = btnDispMap;
	}

	public boolean isPrintable() {
		return printable;
	}

	public void setPrintable(boolean printable) {
		this.printable = printable;
	}

	public String getBusinessKey() {
		return businessKey;
	}

	public void setBusinessKey(String businessKey) {
		this.businessKey = businessKey;
	}

	public TdFlowDef getTdFlowDef() {
		return tdFlowDef;
	}

	public void setTdFlowDef(TdFlowDef tdFlowDef) {
		this.tdFlowDef = tdFlowDef;
	}
	@Override
	public List<BusinessButton> getButtonList() {
		return this.buttonList;
	}

	@Override
	public void setButtonList(List<BusinessButton> buttonList) {
		this.buttonList = buttonList;
	}

}
