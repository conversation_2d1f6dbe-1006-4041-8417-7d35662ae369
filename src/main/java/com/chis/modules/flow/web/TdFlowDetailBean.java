package com.chis.modules.flow.web;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletResponse;

import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;

import com.chis.activiti.entity.TdFlowDef;
import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.entity.TdFlowNodeBtn;
import com.chis.activiti.enumn.FlowBtnType;
import com.chis.activiti.javabean.BusinessButton;
import com.chis.activiti.service.IBusinessService;
import com.chis.activiti.service.IFlowBeanService;
import com.chis.activiti.service.IFlowButtons;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MapUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.interfaces.IFastReport;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.DownLoadUtil;
import com.chis.modules.system.utils.MsgSendUtil;
import com.chis.modules.system.web.FacesBean;
import com.google.common.collect.Lists;

/**
 * 流程处理详情<br/>
 * 由流程追踪或者已办任务转向过来，转向的时候要带着原来的url地址、还有processId<br/>
 * 
 * 详情逻辑： 寻找当前登录人所参与的最后一个节点的表单数据，<br/>
 * 如果找不到，就找第一个节点的表单数据<br/>
 * 
 ******************************** @Deprecated************************************* 流程追踪的详情逻辑：<br/>
 *             * 已结束的流程，显示最后一个节点的表单数据<br/>
 *             * 未结束的流程，显示第一个节点的表单数据<br/>
 * 
 *             已办任务的详情逻辑：<br/>
 *             * 显示自己参与过的最后一个节点的表单数据<br/>
 ******************************** @Deprecated*************************************
 * 
 * <AUTHOR>
 */
@ManagedBean(name = "tdFlowDetailBean")
@ViewScoped
public class TdFlowDetailBean extends FacesBean implements IFlowBeanService,
		IFlowButtons {

	private static final long serialVersionUID = 111512515615823917L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	private CommServiceImpl commService = SpringContextHolder
			.getBean(CommServiceImpl.class);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder
			.getBean(FlowBusinessServiceImpl.class);

	/** 当前节点对象 */
	private TdFlowNode tdFlowNode = null;
	/** 从哪儿转向过来的 */
	private String fromUrl;
	/** 流程实例ID */
	private String processId;
	/** 业务模块提供的按钮集合 */
	private List<BusinessButton> buttonList = new ArrayList<BusinessButton>(0);
	/** 流程上下文 */
	private FlowContext flowContext;
	/**
	 * 流程表单接口
	 */
	private IBusinessService businessService;
	/** 是否可打印 */
	protected boolean printable = false;
	/** 是否允许下载 */
	private boolean ifDownLoad;
	private String outId;
	/** 流程标题 */
	private String businessKey;
	/** 是否直接显示每一步的流程意见到页面上 */
	private Boolean dispPro = false;
	/** 流程每步处理的意见 */
	private List<Object[]> advDataList = Lists.newArrayList();
	/** 上一步节点的意见 */
	private List<Object[]> lastNodeAdvice;
	/** 能否显示的按钮 key-按钮类型，见FlowBtnType value-TdFlowNodeBtn对象 */
	private Map<String, TdFlowNodeBtn> btnDispMap;
	private String businessId;
	private String taskId;
	private String nodeId;
	private TdFlowDef def;
	/**流程信息显示位置，默认为上方，根据参数可调整为下方*/
	private boolean flowInfoShow=true;

	public TdFlowDetailBean() {
		this.fromUrl = JsfUtil.getRequest().getParameter("from");
		this.processId = JsfUtil.getRequest().getParameter("processId");
		this.businessKey = JsfUtil.getRequest().getParameter("businesskey");
		this.businessId = JsfUtil.getRequest().getParameter("businessId");
		this.taskId = JsfUtil.getRequest().getParameter("taskId");
		String userID = JsfUtil.getRequest().getParameter("userID");
		outId = JsfUtil.getRequest().getParameter("outId");
//		if (StringUtils.isNotBlank(taskId)) {
//			this.flowContext = new FlowContext(this.taskId, this.nodeId,
//					this.businessId);
//			this.tdFlowNode = this.flowContext.getTdFlowNode();
//		
//		} 
		if (StringUtils.isBlank(fromUrl)
				|| StringUtils.isBlank(processId)) {
			throw new RuntimeException(Constants.BIG_ERROR);
		} else {
			if (StringUtils.isNotBlank(userID)) {
				this.flowContext = new FlowContext(this.processId,
						Integer.valueOf(userID));
			} else {
				this.flowContext = new FlowContext(this.processId,
						this.sessionData.getUser().getRid());
			}
			this.tdFlowNode = this.flowContext.getTdFlowNode();
			this.printable = false;
			if (StringUtils.isNotBlank(JsfUtil.getRequest().getParameter(
					"ifView"))) {
				this.printable = true;
			} else if (null != this.tdFlowNode.getTdFlowNodeBtnList()
					&& this.tdFlowNode.getTdFlowNodeBtnList().size() > 0) {
				this.initBtnMap(this.tdFlowNode);
			}
		}
		def = this.flowContext.getTdFlowDef();
		if (null != def && null != def.getIsDispPro()
				&& def.getIsDispPro().intValue() == 1) {
			this.dispPro = true;
			this.advDataList = this.flowBusinessService.findHisTaskRecord(
					processId, 2);
		} else {
			// 上一节点的处理人、处理时间、处理意见
			String taskId = this.flowBusinessService.findLastTaskId(processId);
			this.lastNodeAdvice = this.flowBusinessService
					.findNodeAdvice(taskId);
		}
		JsfUtil.getRequestMap().put("manageBeanName", "tdFlowDetailBean");
		//加载流程信息显示位置
		String showPlace= commService.findParamValue("FLOWINFO_SHOWPLACE");
		if("0".equals(showPlace)){
			flowInfoShow=false;
		}
	}

	/**
	 * 返回操作
	 */
	public void backAction() {
		// 业务
		try {
			FacesContext.getCurrentInstance().getExternalContext()
					.redirect(this.fromUrl);
		} catch (IOException e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}

	/**
	 * 根据节点的配置，初始化按钮的显示状态
	 */
	private void initBtnMap(TdFlowNode node) {
		this.printable = false;
		this.btnDispMap = new HashMap<String, TdFlowNodeBtn>();

		for (FlowBtnType fbt : FlowBtnType.values()) {
			this.btnDispMap.put(fbt.getTypeNo(), new TdFlowNodeBtn());
		}

		if (null != node.getTdFlowNodeBtnList()
				&& node.getTdFlowNodeBtnList().size() > 0) {
			for (TdFlowNodeBtn nodeBtn : node.getTdFlowNodeBtnList()) {
				if (nodeBtn.getFlowBtnType().equals(FlowBtnType.PREVIEW)
						|| nodeBtn.getFlowBtnType().equals(FlowBtnType.PRINT)
						|| nodeBtn.getFlowBtnType().equals(FlowBtnType.DESIGN)
						|| nodeBtn.getFlowBtnType().equals(FlowBtnType.PRINTER)) {
					this.printable = true;
				}
				nodeBtn.setDisp(true);
				this.btnDispMap.put(nodeBtn.getFlowBtnType().getTypeNo(),
						nodeBtn);
			}
		}
	}

	/**
	 * 授权初始化
	 */
	public void ffInitAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(920, 870, 500,
				null);

		Map<String, List<String>> paramsMap = new HashMap<String, List<String>>();
		/**
		 * 传入title
		 */
		List<String> paramList = new ArrayList<String>(1);
		paramList.add("请选择人员");
		paramsMap.put("title", paramList);

		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("/webapp/system/selectUsersList", options,
				paramsMap);
	}

	/**
	 * 分发确定
	 * 
	 * @param event
	 *            弹出框关闭事件
	 */
	public void onFfSelect(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event
				.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			Collection<TsUserInfo> coll = (Collection<TsUserInfo>) selectedMap
					.get("selectUserList");

			if (null != coll && coll.size() > 0) {
				StringBuilder sb = new StringBuilder();
				for (TsUserInfo t : coll) {
					sb.append(",").append(t.getRid());
				}
				this.flowBusinessService.ffMsgInProcess(this.taskId, sb
						.toString().replaceFirst(",", ""), this.sessionData
						.getUser().getRid(), this.businessId);
				MsgSendUtil.sendNewMsg(sb.toString().replaceFirst(",", ""));
				JsfUtil.addSuccessMessage("分发成功！");
			}
		}
	}

	/**
	 * 显示流程图
	 */
	public void processPicAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 900,
				null, 600);

		// 用于TdFlowHisTaskBean获取
		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(this.processId);
		paramMap.put("processId", paramList);

		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("/webapp/flow/tdFlowPicList", options, paramMap);
	}

	/**
	 * 报表设计按钮
	 */
	public void designAction() {
		if (null != this.businessService
				&& this.businessService instanceof IFastReport) {
			IFastReport fast = (IFastReport) this.businessService;
			fast.getFastReportBean().designAction();
		} else {
			JsfUtil.addErrorMessage("此模块不支持该功能！");
		}
	}

	/**
	 * 打印按钮
	 */
	public void printAction() {
		if (null != this.businessService
				&& this.businessService instanceof IFastReport) {
			IFastReport fast = (IFastReport) this.businessService;
			fast.getFastReportBean().printReportAction();
		} else {
			JsfUtil.addErrorMessage("此模块不支持该功能！");
		}
	}

	/**
	 * 预览
	 */
	public void preViewAction() {
		if (null != this.businessService
				&& this.businessService instanceof IFastReport) {
			IFastReport fast = (IFastReport) this.businessService;
			fast.getFastReportBean().showReportAction();
		} else {
			JsfUtil.addErrorMessage("此模块不支持该功能！");
		}
	}

	public void downAction() {
		// 虚拟路径
		String xnPath = JsfUtil.getAbsolutePath() + "archives/";
		String xdPath = null;
		String fileName = null;
		if (StringUtils.isNotBlank(outId)) {
			Object[] obj = this.commService.getAddressByRid(Integer
					.valueOf(outId));
			xdPath = obj[0].toString();
			fileName = obj[1].toString();
		}
		if (StringUtils.isNotBlank(xdPath)) {
			xdPath = xdPath.substring(xdPath.lastIndexOf("/") + 1);
			// 虚拟路径加上文件的相对路径
			String path = xnPath + xdPath;
			FileInputStream fis = null;
			try {
				File file = new File(path);
				if (file.exists()) {
					fis = new FileInputStream(file);
					String fileString = DownLoadUtil.uploadFile2Database(fis);
					fileName = fileName + ".doc";
					DownLoadUtil.downFile(fileName, fileString);
				} else {
					JsfUtil.addErrorMessage(Constants.DOWNLOAD_ERROR_FILENOTEXISTS);
				}
			} catch (Exception e1) {
				e1.printStackTrace();
			} finally {// 关闭输入输出流
				try {
					fis.close();
				} catch (IOException e2) {
					e2.printStackTrace();
				}
			}
		}
	}

	@Override
	public IBusinessService getBusinessService() {
		return this.businessService;
	}

	@Override
	public void setBusinessService(IBusinessService businessService) {
		this.businessService = businessService;
	}

	@Override
	public Object getFlowContext() {
		return this.flowContext;
	}

	@Override
	public List<BusinessButton> getButtonList() {
		return this.buttonList;
	}

	@Override
	public void setButtonList(List<BusinessButton> buttonList) {
		this.buttonList = buttonList;
	}

	public TdFlowNode getTdFlowNode() {
		return tdFlowNode;
	}

	@Override
	public boolean isPrintable() {
		return printable;
	}

	@Override
	public void setPrintable(boolean printable) {
		this.printable = printable;
	}

	public boolean isIfDownLoad() {
		return ifDownLoad;
	}

	public void setIfDownLoad(boolean ifDownLoad) {
		this.ifDownLoad = ifDownLoad;
	}

	public String getBusinessKey() {
		return businessKey;
	}

	public void setBusinessKey(String businessKey) {
		this.businessKey = businessKey;
	}

	public Boolean getDispPro() {
		return dispPro;
	}

	public void setDispPro(Boolean dispPro) {
		this.dispPro = dispPro;
	}

	public List<Object[]> getAdvDataList() {
		return advDataList;
	}

	public void setAdvDataList(List<Object[]> advDataList) {
		this.advDataList = advDataList;
	}

	public List<Object[]> getLastNodeAdvice() {
		return lastNodeAdvice;
	}

	public void setLastNodeAdvice(List<Object[]> lastNodeAdvice) {
		this.lastNodeAdvice = lastNodeAdvice;
	}

	public Map<String, TdFlowNodeBtn> getBtnDispMap() {
		return btnDispMap;
	}

	public void setBtnDispMap(Map<String, TdFlowNodeBtn> btnDispMap) {
		this.btnDispMap = btnDispMap;
	}

	public String getBusinessId() {
		return businessId;
	}

	public void setBusinessId(String businessId) {
		this.businessId = businessId;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String getNodeId() {
		return nodeId;
	}

	public void setNodeId(String nodeId) {
		this.nodeId = nodeId;
	}

	public TdFlowDef getDef() {
		return def;
	}

	public void setDef(TdFlowDef def) {
		this.def = def;
	}

	public boolean isFlowInfoShow() {
		return flowInfoShow;
	}

	public void setFlowInfoShow(boolean flowInfoShow) {
		this.flowInfoShow = flowInfoShow;
	}

}
