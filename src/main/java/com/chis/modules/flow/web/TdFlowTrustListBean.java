package com.chis.modules.flow.web;

import java.util.Arrays;
import java.util.Date;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.web.FacesSimpleBean;

/**
 * 流程委托
 * 
 * <AUTHOR>
 * 
 */
@ManagedBean
@ViewScoped
public class TdFlowTrustListBean extends FacesSimpleBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4589131092860825184L;
	private FlowBusinessServiceImpl flowService = SpringContextHolder
			.getBean(FlowBusinessServiceImpl.class);
	private Integer rid;
	private Integer state;
	private String[] searchStates;
	private Date searchPreBeginDate;
	private Date searchBackBeginDate;
	private Date searchPreEndDate;
	private Date searchBackEndDate;

	@PostConstruct
	public void init() {
		this.ifSQL = true;
		searchStates = new String[] { "1" };
		if (StringUtils.isNotBlank(JsfUtil.getRequest().getParameter("tag"))) {
			initRedirectParam();
		}
		searchAction();
	}

	/**
	 * 初始化重定向参数
	 */
	private void initRedirectParam() {
		if (StringUtils.isNotBlank(JsfUtil.getRequest().getParameter(
				"searchStates"))
				&& JsfUtil.getRequest().getParameter("searchStates").length() >= 3) {
			searchStates = JsfUtil.getRequest().getParameter("searchStates")
					.replace("[", "").replace("]", "").replaceAll(" ", "")
					.split(",");
		} else {
			searchStates = new String[] {};
		}
		String preBeginDate = JsfUtil.getRequest().getParameter(
				"searchPreBeginDate");
		if (StringUtils.isNotBlank(preBeginDate)) {
			this.searchPreBeginDate = DateUtils.parseDate(preBeginDate);
		} else {
			this.searchPreBeginDate = null;
		}
		String preEndDate = JsfUtil.getRequest().getParameter(
				"searchPreEndDate");
		if (StringUtils.isNotBlank(preEndDate)) {
			this.searchPreEndDate = DateUtils.parseDate(preEndDate);
		} else {
			this.searchPreEndDate = null;
		}
		String backBeginDate = JsfUtil.getRequest().getParameter(
				"searchBackBeginDate");
		if (StringUtils.isNotBlank(preBeginDate)) {
			this.searchBackBeginDate = DateUtils.parseDate(backBeginDate);
		} else {
			this.searchBackBeginDate = null;
		}
		String backEndDate = JsfUtil.getRequest().getParameter(
				"searchBackEndDate");
		if (StringUtils.isNotBlank(backEndDate)) {
			this.searchBackEndDate = DateUtils.parseDate(backEndDate);
		} else {
			this.searchBackEndDate = null;
		}
	}

	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder();
		sb.append(" FROM TD_FLOW_TRUST T1");
		sb.append(" JOIN TS_USER_INFO T2 ON T1.USER_ID = T2.RID");
		sb.append(" JOIN TS_USER_INFO T3 ON T1.TRUST_USER_ID = T3.RID");
		sb.append(" LEFT JOIN TD_FLOW_TRUST_DEF T4 ON T4.TRUST_ID = T1.RID");
		sb.append(" LEFT JOIN TD_FLOW_DEF T5 ON T4.DEF_ID = T5.RID");
		sb.append(" WHERE 1=1");
		sb.append(" AND T1.USER_ID = ").append(
				((SessionData) JsfUtil.getSessionMap().get(
						SessionData.SESSION_DATA)).getUser().getRid());
		if (searchStates != null && searchStates.length == 1) {
			sb.append(" AND T1.STATE = ").append(searchStates[0]);
		}
		if (searchPreBeginDate != null) {
			sb.append(" AND T1.BEGIN_TIME >= TO_DATE('")
					.append(DateUtils.formatDate(searchPreBeginDate, null))
					.append("','YYYY-MM-dd')");
		}
		if (searchBackBeginDate != null) {
			sb.append(" AND T1.BEGIN_TIME <= TO_DATE('")
					.append(DateUtils.formatDate(searchBackBeginDate, null))
					.append("','YYYY-MM-dd')");
		}
		if (searchPreEndDate != null) {
			sb.append(" AND T1.END_TIME >= TO_DATE('")
					.append(DateUtils.formatDate(searchPreEndDate, null))
					.append("','YYYY-MM-dd')");
		}
		if (searchBackEndDate != null) {
			sb.append(" AND T1.END_TIME <= TO_DATE('")
					.append(DateUtils.formatDate(searchBackEndDate, null))
					.append("','YYYY-MM-dd')");
		}
		sb.append(" GROUP BY T1.RID,T2.USERNAME ,T3.USERNAME ,T1.BEGIN_TIME,T1.END_TIME,T1.STATE");
		sb.append(" ORDER BY T1.END_TIME DESC");
		StringBuilder searchSql = new StringBuilder();
		searchSql
				.append("SELECT T1.RID,T2.USERNAME WTR,T3.USERNAME SWTR,T1.BEGIN_TIME");
		searchSql.append(",T1.END_TIME,T1.STATE,WM_CONCAT(T5.DEF_NAME)");
		searchSql.append(sb.toString());
		StringBuilder countSql = new StringBuilder();
		countSql.append("SELECT COUNT(*)FROM (").append(searchSql.toString())
				.append(")");
		return new String[] { searchSql.toString(), countSql.toString() };
	}

	public void forwardEditPage() {
		try {
			String url = "/webapp/flow/tdFlowTrustEdit.faces?tag=1";
			String from = "/webapp/flow/tdFlowTrustList.faces?tag=1";
			StringBuilder sb = new StringBuilder(url);
			if (rid != null) {
				sb.append("&rid=").append(rid);
			}
			sb.append("&from=" + from);
			if (searchStates != null) {
				sb.append("@searchStates=").append(
						Arrays.toString(searchStates));
			}
			if (searchPreBeginDate != null) {
				sb.append("@searchPreBeginDate=").append(
						DateUtils.formatDate(searchPreBeginDate, null));
			}
			if (searchPreEndDate != null) {
				sb.append("@searchPreEndDate=").append(
						DateUtils.formatDate(searchPreEndDate, null));
			}
			if (searchBackBeginDate != null) {
				sb.append("@searchBackBeginDate=").append(
						DateUtils.formatDate(searchBackBeginDate, null));
			}
			if (searchBackEndDate != null) {
				sb.append("@searchBackEndDate=").append(
						DateUtils.formatDate(searchBackEndDate, null));
			}
			FacesContext.getCurrentInstance().getExternalContext()
					.redirect(sb.toString());
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}

	public void deleteAction() {
		try {
			this.flowService.deleteFlowTrustByRid(rid);
			JsfUtil.addSuccessMessage("删除成功!");
			searchAction();
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("已被引用删除失败！");
		}

	}

	public void updateStateAction() {
		try {
			flowService.updateFlowTrustState(rid, state);
			JsfUtil.addSuccessMessage("更新成功!");
			searchAction();
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("更新失败！");
		}
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public String[] getSearchStates() {
		return searchStates;
	}

	public void setSearchStates(String[] searchStates) {
		this.searchStates = searchStates;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getSearchPreBeginDate() {
		return searchPreBeginDate;
	}

	public void setSearchPreBeginDate(Date searchPreBeginDate) {
		this.searchPreBeginDate = searchPreBeginDate;
	}

	public Date getSearchBackBeginDate() {
		return searchBackBeginDate;
	}

	public void setSearchBackBeginDate(Date searchBackBeginDate) {
		this.searchBackBeginDate = searchBackBeginDate;
	}

	public Date getSearchPreEndDate() {
		return searchPreEndDate;
	}

	public void setSearchPreEndDate(Date searchPreEndDate) {
		this.searchPreEndDate = searchPreEndDate;
	}

	public Date getSearchBackEndDate() {
		return searchBackEndDate;
	}

	public void setSearchBackEndDate(Date searchBackEndDate) {
		this.searchBackEndDate = searchBackEndDate;
	}

}
