package com.chis.modules.flow.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.entity.TbFlowAdvtemplate;
import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.activiti.utils.BusinessIdUtil;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.flow.logic.FlowChoiceConditionBean;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsTxtype;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.MetaCondition;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesBean;

/**
 * 完成操作弹出框，即将自己任务节点直接跳转到结束
 * 内容宽度：830 内容高度：500
 * <AUTHOR>
 */
@ManagedBean(name = "tdFlowSubmitFinishBean")
@ViewScoped
public class TdFlowSubmitFinishBean extends FacesBean {

	private static final long serialVersionUID = -5927665119395504030L;

	/** 存在session中的对象 */
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	/** 任务ID */
	private String taskId;
	/** 流程意见 */
	private String advice;
	/** 当前节点对象 */
	private TdFlowNode tdFlowNode;
	/** 常用语 */
	private List<TsSimpleCode> cyyList;
	/** 意见模板 */
	private List<TbFlowAdvtemplate> advTemplateList;
	/**整个流程的任务待办人 key-节点名称 value-用户集合*/
	private Map<String, List<TsUserInfo>> allUserMap;
	/** 选中人员的IDS */
	private String selectUserIds;
	/** 通讯方式 */
	private List<TsTxtype> txtypeList;
	/** 选择的通讯方式 */
	private List<String> selectedTxtypeList = new ArrayList<String>(0);	
	/**是否需要分发消息,分发的对象是参与流程的人*/
	private boolean ffMsg = false;
	/** 业务主检ID JSON */
	private String businessId;
	
	public TdFlowSubmitFinishBean() {
		//传过来的条件,JSON格式
		String conJson = JsfUtil.getRequest().getParameter("condition");
		if(StringUtils.isNotBlank(conJson)) {
			FlowChoiceConditionBean bean = JSON.parseObject(StringUtils.decode(conJson), FlowChoiceConditionBean.class);
			
			//当前节点
			if(StringUtils.isNotBlank(bean.getCurrentFlowNodeJSON())) {
				this.tdFlowNode = flowBusinessService.findTdFlowNode(new Integer(bean.getCurrentFlowNodeJSON()));
			}
			
			//初始化流程意见
			this.initAdvTemplate(this.tdFlowNode.getRid(), BusinessIdUtil.parsePK(bean.getBusinessIdJSON()));
			
			//初始化通讯方式
			this.txtypeList = this.flowBusinessService.findTxtypeByNodeId(this.tdFlowNode.getRid());
			
			//初始化常用语
			this.initCyyList();
			
			//初始化流程各节点的处置人
			this.taskId = bean.getActivitiTaskId();
			this.businessId = bean.getBusinessIdJSON();
			if(this.ffMsg) {
				this.allUserMap = this.flowBusinessService.findAssigeeInProcessInstance(bean.getActivitiTaskId());
			}else {
				this.allUserMap = new HashMap<String, List<TsUserInfo>>();
			}
		}
	}
	
	/**
	 * 初始化意见模板
	 */
	private void initAdvTemplate(Integer nodeId, String businessId) {
		//构建模板的依赖条件
		MetaCondition metaCondition = MetaConditionFactory.produceCondition(this.sessionData, businessId, null);
		//查询该节点的意见模板集合
		this.advTemplateList = this.flowBusinessService.findAdvTemplateList(Integer.valueOf(nodeId), metaCondition);
		/**
		 * 初始化流程意见，如果配了流程意见的话
		 */
		if(null != this.advTemplateList && this.advTemplateList.size() > 0) {
			for(TbFlowAdvtemplate adv : this.advTemplateList) {
				if(adv.getIsDefault().intValue() == 1) {
					this.advice = adv.getAfterAnalyContent();
					return;
				}
			}
		}
	}
	
	/**
	 * 初始化常用语
	 */
	private void initCyyList() {
		this.cyyList = this.commService.findSimpleCodesByTypeId("1101");
	}
	
	/**
	 * 确定
	 */
	public void personSelectAction() {
		if(this.tdFlowNode.getIfAdv().intValue() == 1) {
			if(StringUtils.isBlank(this.advice)) {
				JsfUtil.addErrorMessage("请填写流程意见！");
				return;
			}
		}
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("advice", this.advice);
		
		if(this.ffMsg) {
			//设置需要发送消息的人
			if(StringUtils.isNotBlank(this.selectUserIds)) {
				if(this.selectUserIds.startsWith(",")) {
					this.selectUserIds = this.selectUserIds.replaceFirst(",", "");
				}
				
				//保存消息
				this.flowBusinessService.saveMsgInProcess(this.taskId, this.selectUserIds, this.sessionData.getUser().getRid(), this.businessId);
				
				//提取选中的人，传给任务处理类
				List<TsUserInfo> selectUsers = new ArrayList<TsUserInfo>();
				String[] split = this.selectUserIds.replaceAll("'", "").split(",");
				for(String s : split) {
					if(StringUtils.isNotBlank(s)) {
						TsUserInfo u = this.findFromAllUser(s);
						if(null != u) {
							selectUsers.add(u);
						}
					}
				}
				map.put("selectedUserList", selectUsers);
			}
		}
		
		//通讯方式
		List<TsTxtype> txList = new ArrayList<TsTxtype>();
		if(null != this.selectedTxtypeList && this.selectedTxtypeList.size() > 0) {
			for(String typeId : this.selectedTxtypeList) {
				for(TsTxtype tx : this.txtypeList) {
					if(tx.getRid().toString().equals(typeId)) {
						txList.add(tx);
						continue;
					}
				}
			}
		}
		map.put("selectedTxtypeList", txList);
		RequestContext.getCurrentInstance().closeDialog(map);
	}

	/**
	 * 从allUserMap中根据userId找出用户对象
	 * @param uid 用户id
	 * @return 用户对象
	 */
	private TsUserInfo findFromAllUser(String uid) {
		Collection<List<TsUserInfo>> coll = this.allUserMap.values();
		if(null != coll && coll.size() > 0) {
			for(List<TsUserInfo> list : coll) {
				if(null != list && list.size() > 0) {
					for(TsUserInfo t : list) {
						if(uid.equals(t.getRid().toString())) {
							return t;
						}
					}
				}
			}
		}
		return null;
	}
	
	/**
	 * 关闭
	 */
	public void dialogClose() {
		RequestContext.getCurrentInstance().closeDialog(null);
	}

	public String getAdvice() {
		return advice;
	}

	public void setAdvice(String advice) {
		this.advice = advice;
	}

	public TdFlowNode getTdFlowNode() {
		return tdFlowNode;
	}

	public void setTdFlowNode(TdFlowNode tdFlowNode) {
		this.tdFlowNode = tdFlowNode;
	}

	public List<TsSimpleCode> getCyyList() {
		return cyyList;
	}

	public void setCyyList(List<TsSimpleCode> cyyList) {
		this.cyyList = cyyList;
	}

	public List<TbFlowAdvtemplate> getAdvTemplateList() {
		return advTemplateList;
	}

	public void setAdvTemplateList(List<TbFlowAdvtemplate> advTemplateList) {
		this.advTemplateList = advTemplateList;
	}

	public List<TsTxtype> getTxtypeList() {
		return txtypeList;
	}

	public void setTxtypeList(List<TsTxtype> txtypeList) {
		this.txtypeList = txtypeList;
	}

	public List<String> getSelectedTxtypeList() {
		return selectedTxtypeList;
	}

	public void setSelectedTxtypeList(List<String> selectedTxtypeList) {
		this.selectedTxtypeList = selectedTxtypeList;
	}

	public Map<String, List<TsUserInfo>> getAllUserMap() {
		return allUserMap;
	}

	public void setAllUserMap(Map<String, List<TsUserInfo>> allUserMap) {
		this.allUserMap = allUserMap;
	}

	public String getSelectUserIds() {
		return selectUserIds;
	}

	public void setSelectUserIds(String selectUserIds) {
		this.selectUserIds = selectUserIds;
	}

	public boolean isFfMsg() {
		return ffMsg;
	}

	public void setFfMsg(boolean ffMsg) {
		this.ffMsg = ffMsg;
	}

}
