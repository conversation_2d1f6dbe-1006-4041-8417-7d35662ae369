package com.chis.modules.flow.web;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.el.ELContext;
import javax.el.ExpressionFactory;
import javax.el.MethodExpression;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.ServletContext;

import org.primefaces.component.commandbutton.CommandButton;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.entity.TdTestVacc;
import com.chis.activiti.enumn.SupportAssigeeType;
import com.chis.activiti.javabean.BusinessButton;
import com.chis.activiti.service.ISetButtons;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.activiti.service.impl.TestServiceImpl;
import com.chis.activiti.utils.BusinessIdUtil;
import com.chis.common.bean.FastReportData;
import com.chis.common.bean.FastReportDataRef;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MapUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.modules.system.interfaces.IFastReport;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.FilePo;
import com.chis.modules.system.logic.ImgBean;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.utils.DownLoadUtil;
import com.chis.modules.system.web.FastReportBean;
import com.google.common.collect.Lists;

@ManagedBean(name = "vaccBean")
@ViewScoped
public class VaccBean extends FlowFacesBean implements ISetButtons, IFastReport{

	private static final long serialVersionUID = 7313426388219619748L;
	/** 存在session中的对象 */
	private TestServiceImpl testService = SpringContextHolder.getBean(TestServiceImpl.class);
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	
	private TdTestVacc tdTestVacc = new TdTestVacc();
	private FastReportBean fastReportBean;
	private List<String> signList = Lists.newArrayList();
	
	/**PF下载的文件*/
	private StreamedContent streamedContent;
	private String downloadFilePath;
	
	public VaccBean() {
		this.init(BusinessIdUtil.parsePK(this.businessId));
	}
	
	/**
	 * 业务表单初始化
	 * @param obj 表单id
	 */
	@Override
	public void init(Object obj) {
		this.fastReportBean = new FastReportBean(this,"test_frpt");
		if(null != obj && !"".equals(obj.toString())) {
			this.tdTestVacc = this.testService.findVacc(Integer.valueOf(obj.toString()));
		}else {
			this.tdTestVacc = new TdTestVacc();
			this.tdTestVacc.setVaccMan(this.sessionData.getUser().getUsername());
		}
	}

	@Override
	public String saveAction() {
		System.out.println("--saveAction--");
		if(null != this.tdTestVacc) {
			this.tdTestVacc = this.testService.saveOrUpdateVacc(this.tdTestVacc);
			return BusinessIdUtil.toJsonString(this.tdTestVacc.getRid().toString());
		}
		return null;
	}

    @Override
    public String submitAction() {
    	/**
    	 * 应急测试，最后一个节点的时候另发起一个流程
    	 */
    	try {
			TdTestVacc vacc = (TdTestVacc) this.tdTestVacc.clone();
			vacc.setVaccMan(vacc.getVaccMan());
			vacc = this.testService.saveOrUpdateVacc(vacc);
//			
//			//转入流程
//			TdFlowDef zrDef = this.flowBusinessService.findDef(543);
//			this.flowBusinessService.startProcessInstance(zrDef, 24, "滨湖应急办", vacc.getRid().toString());
		} catch (CloneNotSupportedException e) {
			e.printStackTrace();
		}
    	
    	
        return  Constants.FLOW_SUCCESS;
    }
    
    /**
     * "{'arr':['a','b'],'int':1,'name':'json','func':function(i){ return this.arr[i]; },'bool':true}" <br/>
     */
	@Override
	public String settingAssigneeCondition() {
		//StringBuilder sb = new StringBuilder();
		//sb.append("{'david':'zhou'}");
		return null;
	}

    @Override
    public String backAction() {
        return  Constants.FLOW_SUCCESS;
    }

    /**
	 * 如果流程业务表单有需要向流程引擎设置变量，则需要实现该方法
	 * @return
	 */
	@Override
	public Map<String, Object> addVariables() {
		Map<String, Object> map = new HashMap<String, Object>();
		if(null != this.tdTestVacc) {
			map.put("days", this.tdTestVacc.getVaccDay());
			map.put("dealRst", 3);
			
			map.put("x", 1);
			map.put("y", 1);
			map.put("z", 1);
			
			map.put("ower", 2000);
		}
		return map;
	}
	
	@Override
	public String supportTaskMen() {
//		List<TsUserInfo> userList = new ArrayList<TsUserInfo>();
//		
//		TsUserInfo t0 = new TsUserInfo(2000);
//		t0.setUsername("周伟伟");
//
//		TsOffice office0 = new TsOffice(26, "BS项目部");
//		TsUnit unit0 = new TsUnit(1);
//		unit0.setUnitSimpname("中卫信");
//
//		office0.setTsUnit(unit0);
//		t0.setTsOffice(office0);
//		t0.setTsUnit(unit0);
//
//		userList.add(t0);
//		
//		
//		
//		
//		TsUserInfo t = new TsUserInfo(59);
//		t.setUsername("赵信");
//		
//		TsOffice office = new TsOffice(3, "策划区");
//		TsUnit unit = new TsUnit(4);
//		unit.setUnitSimpname("无锡市疾控");
//		
//		office.setTsUnit(unit);
//		t.setTsOffice(office);
//		t.setTsUnit(unit);
//		
//		userList.add(t);
//		
//		return JSON.toJSONString(userList);
		
		return null;
	}
	
	@Override
	public SupportAssigeeType supportType() {
		return SupportAssigeeType.OR;
	}
	
	@Override
	public List<BusinessButton> supportButtons() {
		List<BusinessButton> list = new ArrayList<BusinessButton>();
		BusinessButton btn = new BusinessButton("print", "打印");
		final CommandButton commandButton = (CommandButton) JsfUtil.getApplication().createComponent(CommandButton.COMPONENT_TYPE);
		//commandButton.setType("button");
		commandButton.setIcon("ui-icon-cart");
		
		ExpressionFactory ef = JsfUtil.getApplication().getExpressionFactory();
		FacesContext fc = FacesContext.getCurrentInstance();
		ELContext elContext = fc.getELContext();
		
		commandButton.setProcess("@this");
//		commandButton.addActionListener(new MethodExpressionActionListener(
//				ef.createMethodExpression(elContext, "#{vaccBean.testHello()}", Void.class, new Class[] {})));
		
		MethodExpression testMtd = ef.createMethodExpression(elContext,"#{vaccBean.testHello}", null, new Class[] {});
		commandButton.setActionExpression(testMtd);
		btn.setBtnObject(commandButton);
		list.add(btn);
		return list;
	}
	
	public void testHello() {
		System.err.println("【testHello】");
		List<Object[]> testList = this.flowContext.findTaskInfoInProcess(this.sessionData.getUser().getRid());
		if(null != testList && testList.size() > 0) {
			for(Object[] os : testList) {
				for(Object o : os ) {
					System.err.print(o + " ");
				}
				System.err.println();
			}
		}
		JsfUtil.addSuccessMessage("hello,world!");
	}
	
	public void penSignAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 420, null, 220);
		
		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		RequestContext.getCurrentInstance().openDialog("/webapp/system/penSign", options, paramMap);
	}
	
	public void onPenSignCallback(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			String imgPath = (String) selectedMap.get("imgPath");
			this.signList.add(imgPath);
			RequestContext.getCurrentInstance().update("tabView:editForm:signPanel");
		}
	}
	
	public void vedioInputAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 950, null, 580);
		
		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		
//		List<String> paramList = new ArrayList<String>(1);
//		paramList.add("/vedioInput/8d30617c7ad94b6696201552a29f16da.jpg,/vedioInput/731c8f34424a4dbfa86f0ccbdaa0f46c.jpg,/vedioInput/feef478261f14fbda4659efe02088771.jpg,/vedioInput/05b87bc3db3b4108bd224dbb6380966d.jpg,/vedioInput/ad298f25c1844afab847939ad46e4e4a.jpg");
//		paramMap.put("imgPath", paramList);
//		
		List<String> paramList2 = new ArrayList<String>(1);
		paramList2.add("测试");
		paramMap.put("fileName", paramList2);
		
		List<String> paramList3 = new ArrayList<String>(1);
		paramList3.add("5");
		paramMap.put("max", paramList3);
		
		RequestContext.getCurrentInstance().openDialog("/webapp/system/takePictures.xhtml", options, paramMap);
	}
	
	public void onVedioInputCallback(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			List<FilePo> fileList = (List<FilePo>) selectedMap.get("dataList");
			System.err.println("【json】：" + JSON.toJSONString(fileList));
		}
	}
	
	public void pdfViewAction() {
		StringBuilder sb = new StringBuilder("/webapp/system/pdfView.faces?filePath=");
		sb.append("/vedioInputPdf/abcd.pdf,/vedioInputPdf/abcd.pdf");
		RequestContext.getCurrentInstance().execute("top.win_TabMenu.OpenTabWin('m02', 'PDF阅读', '" + sb.toString() + "', '')");
	} 
	
	public void commonUploadAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 850, null, 500);
		
		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		
		List<String> paramList = new ArrayList<String>(1);
		paramList.add("/temp");
		paramMap.put("filePath", paramList);
		
		
		List<String> paramList2 = new ArrayList<String>();
		List<FilePo> fileList = Lists.newArrayList();
		FilePo f = new FilePo();
		f.setFileName("测试.png");
		f.setFilePath("/temp/9b430be8f5654e9094582f82a6522d62.png");
		fileList.add(f);
		fileList.add(f);
		fileList.add(f);
		fileList.add(f);
		fileList.add(f);
		fileList.add(f);
		fileList.add(f);
		fileList.add(f);
		fileList.add(f);
		fileList.add(f);
		fileList.add(f);
		paramList2.add(JSON.toJSONString(fileList));
		System.err.println("【json】：" + JSON.toJSONString(fileList));
		paramMap.put("dataJsonStr", paramList2);
		
		RequestContext.getCurrentInstance().openDialog("/webapp/system/uploadFiles.xhtml", options, paramMap);
	}
	
	public void onCommonUploadCallback(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			List<FilePo> fileList = (List<FilePo>) selectedMap.get("dataList");
			System.err.println("【json】：" + JSON.toJSONString(fileList));
		}
	}	
	
	public TdTestVacc getTdTestVacc() {
		return tdTestVacc;
	}

	public void setTdTestVacc(TdTestVacc tdTestVacc) {
		this.tdTestVacc = tdTestVacc;
	}
	
	@Override
	public String deleteAction() {
		return null;
	}

	@Override
	public void afterBackAction() {
		
	}

	@Override
	public void afterSaveAction() {
		
	}

	@Override
	public void afterSubmitAction() {
		
	}

	@Override
	public boolean supportPrint() {
		return true;
	}

	@Override
	public FastReportBean getFastReportBean() {
		// TODO Auto-generated method stub
		return this.fastReportBean;
	}

	@Override
	public List<FastReportData> supportFastReportDataSet() {
		return null;
	}

	@Override
	public List<FastReportDataRef> supportFastReportDataRef() {
		return null;
	}

	public List<String> getSignList() {
		return signList;
	}

	public void setSignList(List<String> signList) {
		this.signList = signList;
	}

	public StreamedContent getStreamedContent() {
		System.err.println("【】：" + downloadFilePath);
		try {
			String xnPath = JsfUtil.getAbsolutePath();
			//String path = xnPath + "/temp/ccc.pdf";
			String path = xnPath + downloadFilePath;
			InputStream stream = new FileInputStream(path);
	        this.streamedContent = new DefaultStreamedContent(stream, "application/pdf", URLEncoder.encode("测试.pdf", "UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return streamedContent;
	}

	public String getDownloadFilePath() {
		return downloadFilePath;
	}

	public void setDownloadFilePath(String downloadFilePath) {
		System.err.println("【setDownloadFilePath】：");
		this.downloadFilePath = downloadFilePath;
	}
	
	
}
