package com.chis.modules.flow.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;

import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.http.HttpRequest;

import com.chis.activiti.entity.TdFlowDef;
import com.chis.activiti.entity.TdFlowTrust;
import com.chis.activiti.entity.TdFlowTrustDef;
import com.chis.activiti.entity.TdFlowType;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MapUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.flow.logic.FlowDefDisPlayBean;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.web.FacesBean;

/**
 * 
 * <AUTHOR>
 * 
 */
@ManagedBean
@ViewScoped
public class TdFlowTrustEditBean extends FacesBean {
	/**
	 * 
	 */
	private static final long serialVersionUID = -7173463714516271875L;
	private FlowBusinessServiceImpl flowService = SpringContextHolder
			.getBean(FlowBusinessServiceImpl.class);
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);

	private TdFlowTrust editBean;
	private String from;
	private TsUserInfo user;
	private List<TdFlowDef> defs;
	private List<TdFlowType> types;
	private List<FlowDefDisPlayBean> displayList;

	@PostConstruct
	public void init() {
		from = JsfUtil.getRequest().getParameter("from");
		String rid = JsfUtil.getRequest().getParameter("rid");
		if (StringUtils.isNotBlank(from)) {
			from = from.replaceAll("@", "&");
		}
		if (StringUtils.isNotBlank(rid) && !"0".equalsIgnoreCase(rid)) {
			editBean = flowService.findFlowTrustByRid(Integer.valueOf(rid));
			user = editBean.getTsUserInfoByTrustUserId();
		} else {
			editBean = new TdFlowTrust();
			editBean.setState(1);
			editBean.setBeginTime(new Date());
		}
		initFlowDef();
	}

	public void initFlowDef() {
		defs = flowService.findFollowDefs(sessionData.getUser().getRid());
		if (editBean.getTdFlowTrustDefs() != null
				&& editBean.getTdFlowTrustDefs().size() > 0) {
			for (TdFlowDef def : defs) {
				for (TdFlowTrustDef trustDef : editBean.getTdFlowTrustDefs()) {
					if (trustDef.getDefId().equals(def.getRid().toString())) {
						def.setSelected(true);
					}
				}
			}
		}
		Map<Integer, TdFlowType> flowTypeMap = new HashMap<>();
		for (TdFlowDef def : defs) {
			TdFlowType type = flowTypeMap.get(def.getTdFlowType().getRid());
			if (type == null) {
				type = def.getTdFlowType();
				type.getTdFlowDefList().add(def);
				flowTypeMap.put(def.getTdFlowType().getRid(), type);
			} else {
				type.getTdFlowDefList().add(def);
			}
		}
		this.types = new ArrayList<>();
		for (TdFlowType type : flowTypeMap.values()) {
			types.add(type);
		}
		initDisplayList();
	}

	private void initDisplayList() {
		displayList = new ArrayList<>();
		for (TdFlowType type : types) {
			FlowDefDisPlayBean main = new FlowDefDisPlayBean();
			main.setRid(type.getRid());
			main.setName(type.getTypeName());
			main.setLevel(0);
			main.setSelected(type.isSelected());
			displayList.add(main);
			for (TdFlowDef def : type.getTdFlowDefList()) {
				FlowDefDisPlayBean sub = new FlowDefDisPlayBean();
				sub.setRid(def.getRid());
				sub.setName(def.getDefName());
				sub.setLevel(1);
				sub.setSelected(def.isSelected());
				displayList.add(sub);
			}
		}
	}

	/**
	 * 受委托人选择初始化
	 */
	public void selectUserInitAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(920, 870, 500,
				null);

		Map<String, List<String>> paramsMap = new HashMap<String, List<String>>();
		/**
		 * 传入title
		 */
		List<String> paramList = new ArrayList<String>(1);
		paramList.add("请选择人员");
		paramsMap.put("title", paramList);

		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("/webapp/system/selectUserList", options,
				paramsMap);
	}

	/**
	 * 
	 * 
	 * @param event
	 *            弹出框关闭事件
	 */
	public void onUserSelect(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event
				.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			user = (TsUserInfo) selectedMap.get("selectedUser");
		}
	}

	/**
	 * 
	 * 
	 * @param event
	 *            流程定义勾选事件
	 */
	public void onTypeSelect(Integer rid, Integer level, boolean selected) {
		for (TdFlowType type : types) {
			if (level == 0 && type.getRid().intValue() == rid.intValue()) {
				type.setSelected(selected);
				for (TdFlowDef def : type.getTdFlowDefList()) {
					def.setSelected(selected);
				}
			} else {
				for (TdFlowDef def : type.getTdFlowDefList()) {
					if (def.getRid().intValue() == rid.intValue()) {
						def.setSelected(selected);
					}
				}
			}
		}
		initDisplayList();
	}

	public void clearUser() {
		this.user = null;
	}

	public void saveAction() {
		if (user == null) {
			JsfUtil.addErrorMessage("请选择受委托人！");
			return;
		}
		if (editBean.getEndTime().getTime() < editBean.getBeginTime().getTime()) {
			JsfUtil.addErrorMessage("结束时间不能小于开始时间！");
			return;
		}
		List<TdFlowTrustDef> trustDefs = new ArrayList<>();
		for (FlowDefDisPlayBean def : displayList) {
			if (def.getLevel().intValue() == 1 && def.isSelected()) {
				TdFlowTrustDef trustDef = new TdFlowTrustDef();
				trustDef.setDefId(def.getRid().toString());
				trustDef.setTdFlowTrust(editBean);
				trustDefs.add(trustDef);
			}
		}
		if (trustDefs.size() == 0) {
			JsfUtil.addErrorMessage("请选择需要委托的流程！");
			return;
		}
		try {
			editBean.setTsUserInfoByUserId(sessionData.getUser());
			editBean.setTdFlowTrustDefs(trustDefs);
			editBean.setTsUserInfoByTrustUserId(user);
			flowService.saveOrUpdateFlowTrust(editBean);
			JsfUtil.addSuccessMessage("保存成功");
			backAction();
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("保存失败");
		}
	}

	public void backAction() {
		try {
			FacesContext.getCurrentInstance().getExternalContext()
					.redirect(this.from);
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}

	public TdFlowTrust getEditBean() {
		return editBean;
	}

	public void setEditBean(TdFlowTrust editBean) {
		this.editBean = editBean;
	}

	public TsUserInfo getUser() {
		return user;
	}

	public void setUser(TsUserInfo user) {
		this.user = user;
	}

	public List<TdFlowDef> getDefs() {
		return defs;
	}

	public void setDefs(List<TdFlowDef> defs) {
		this.defs = defs;
	}

	public List<TdFlowType> getTypes() {
		return types;
	}

	public void setTypes(List<TdFlowType> types) {
		this.types = types;
	}

	public List<FlowDefDisPlayBean> getDisplayList() {
		return displayList;
	}

	public void setDisplayList(List<FlowDefDisPlayBean> displayList) {
		this.displayList = displayList;
	}

}
