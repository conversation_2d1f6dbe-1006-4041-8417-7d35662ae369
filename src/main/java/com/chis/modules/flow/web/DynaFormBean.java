package com.chis.modules.flow.web;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;


import org.activiti.engine.impl.util.ReflectUtil;
import org.primefaces.context.RequestContext;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.entity.TdFlowDef;
import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.javabean.BusinessButton;
import com.chis.activiti.javabean.BusinessIdBean;
import com.chis.activiti.service.ISetButtons;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.bean.FastReportData;
import com.chis.common.bean.FastReportDataRef;
import com.chis.common.utils.FreeMarkers;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.flow.rptvo.FormData;
import com.chis.modules.flow.rptvo.NodeDealVo;
import com.chis.modules.system.entity.TdFormDef;
import com.chis.modules.system.entity.TdFormField;
import com.chis.modules.system.entity.TdFormTable;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.interfaces.IFastReport;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.DyncFormServiceImpl;
import com.chis.modules.system.utils.UsersUtil;
import com.chis.modules.system.web.FastReportBean;

@ManagedBean(name = "dynaFormBean")
@ViewScoped
public class DynaFormBean extends FlowFacesBean implements ISetButtons, IFastReport{

	private static final long serialVersionUID = 1L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private TdFormDef tdFormDef = new TdFormDef();
	private TdFormTable tdFormTable = new TdFormTable();
	private List<TdFormField> fieldList = new ArrayList<TdFormField>(0);
	private DyncFormServiceImpl formService = (DyncFormServiceImpl) SpringContextHolder.getBean(DyncFormServiceImpl.class);
	private CommServiceImpl commservice = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	private FlowBusinessServiceImpl flowService = (FlowBusinessServiceImpl) SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	/**1.单表维护 2.主子表维护*/
	private int formType = 1;
	private Integer defId;
	private Integer rid;
    /*报表*/
    private FastReportBean fastReportBean;
    
    private String html;
    private String dataJson;
    private boolean flag = false;
	
	public DynaFormBean() {
		if(StringUtils.isNotBlank(this.businessId)) {
			BusinessIdBean bean = JSON.parseObject(this.businessId, BusinessIdBean.class);
			if(null != bean) {
				if(StringUtils.isNotBlank(bean.getRid())) {
					this.rid = Integer.valueOf(bean.getRid());
				}
				this.defId = flowContext.getTdFlowNode().getTdFormDef().getRid();
				if( null == this.defId ){
					throw new RuntimeException("流程部署配置有误，请先配置动态表单！");
				}
			}
		}else {
			/********************************************************************************/
			TdFlowDef tdFlowDef = (TdFlowDef) JsfUtil.getRequestMap().get("tdFlowDef");
			TdFlowNode findFirstNode = flowService.findFirstNode(tdFlowDef.getRid());
			if( null != findFirstNode && null != findFirstNode.getTdFormDef().getRid() )	{
				this.defId = findFirstNode.getTdFormDef().getRid();
			}else{
				throw new RuntimeException("流程部署配置有误，请先配置动态表单！");
			}
		}
		this.init(rid);
	}
	@Override
	public void init(Object obj) {
		this.tdFormDef = this.formService.findFormDef(this.defId, UsersUtil.generateCommonUtilMap(),this.rid); 
		if(null != super.flowContext && null != super.flowContext.getTdFlowDef() && null != super.flowContext.getTdFlowDef().getRid()) {
			this.fastReportBean = new FastReportBean(this, this.tdFormDef.getPrtTplCode(), super.flowContext.getTdFlowDef().getRid().toString());
		}else{
		this.fastReportBean = new FastReportBean(this,this.tdFormDef.getPrtTplCode());
		}
		this.tdFormTable = this.tdFormDef.getTdFormTableByTableId();
		this.fieldList = this.tdFormTable.getFieldList();
		this.formType = this.tdFormDef.getTdFormTableByTableId().getFormProp();
		this.buildHtml();
	}
	
	/**
	 * 构建动态组件
	 */
	private void buildHtml() {
		Map<String, Object> varMap = new HashMap<String, Object>();
		varMap.put("rows", this.tdFormDef.getRows());
		this.html = FreeMarkers.renderString(this.tdFormDef.getHtml(), varMap);
		this.dataJson = JSON.toJSONString(this.tdFormDef.getValueMap());
	}
	
	@Override
	public String backAction() {
		return  Constants.FLOW_SUCCESS;
	}

	@Override
	public String deleteAction() {
		return this.formService.deleteFormData(this.tdFormDef, this.rid);
	}

	/**
	 *  	 * <p>修订内容：</p>
	 * 查询回调方法增加级联数据查询sql参数，子表行号参数
 	 * @MethodReviser zxf,2018年3月22日,dynaSearchData
	 */
	public void dynaSearchData(){
		RequestContext context = RequestContext.getCurrentInstance();
		String tag = JsfUtil.getRequestParameter("tag");
		String sql = JsfUtil.getRequestParameter("sql");
		//子表行号
		String ind = JsfUtil.getRequestParameter("ind");
		//级联子项数据查询语句
		String cascadeSql = JsfUtil.getRequestParameter("cascadeSql");
		List<Map<String, Object>> rstList = commservice.findDataBySql(sql, null);
		List<Map<String, Object>> cascadeList=new ArrayList<>();
		if(null!=cascadeSql && StringUtils.isNotBlank(cascadeSql)){
			cascadeList = commservice.findDataBySql(cascadeSql, null);
		}
		context.addCallbackParam("tag", StringUtils.objectToString(tag));
		context.addCallbackParam("ind", StringUtils.objectToString(ind));
		if(null!=rstList && rstList.size()>0){
			context.addCallbackParam("rst", JSON.toJSONString(rstList));
		}else{
			context.addCallbackParam("rst", "");
		}
		
		if(null!=cascadeList && cascadeList.size()>0){
			context.addCallbackParam("cascadeRst", JSON.toJSONString(cascadeList));
		}else{
			context.addCallbackParam("cascadeRst", "");
		}
		if (flag) {
			JsfUtil.addSuccessMessage("操作成功！");
		}
		
	}
	
	@Override
	public String saveAction() {
		System.err.println("【dataJson】：" + dataJson);
		if(StringUtils.isNotBlank(this.dataJson)) {
			Map map = JSON.parseObject(this.dataJson, Map.class);
			this.rid =  this.formService.saveOrUpdateFormData(this.tdFormDef, UsersUtil.generateCommonUtilMap(), this.rid, map,sessionData.getUser());
			this.flag = true;
			if (null != this.rid) {
				this.init(rid);
				
				BusinessIdBean b = new BusinessIdBean();
				b.setRid(this.rid.toString());
				b.setObject(this.defId);
				return JSON.toJSONString(b);
			}
		}
		return null;
	}

	@Override
	public String submitAction() {
		return  Constants.FLOW_SUCCESS;
	}

	@Override
	public List<BusinessButton> supportButtons() {
		return null;
	}
	
    @Override
    public Map<String, Object> addVariables() {
    	Map<String, Object> varMap = new HashMap<String, Object>();
    	Map<String, Object> valueMap = this.tdFormDef.getValueMap();
    	if(null != this.fieldList && this.fieldList.size() > 0) {
    		for(TdFormField f : this.fieldList) {
    			//是流程变量
    			if(null != f.getIsProVal() && f.getIsProVal().intValue() == 1) {
    				varMap.put(f.getFdEnname(), valueMap.get("DYNA_MAIN_"+f.getFdEnname()));
    			}
    		}
    	}
        return varMap;
    }

	@Override
	public boolean supportPrint() {
		return true;
	}
	
	@Override
	public FastReportBean getFastReportBean() {
		return this.fastReportBean;
	}

	@Override
	public List<FastReportData> supportFastReportDataSet() {
		List<FastReportData> rptDataList = new ArrayList<FastReportData>();
		Map<String, Object> valueMap = this.tdFormDef.getValueMap();
		
		//组织主表数据集
		if(null != this.fieldList) {
			FormData fd = new FormData();
			
			for(int i=0; i<this.fieldList.size(); i++) {
				TdFormField field = this.fieldList.get(i);
				Field fName = ReflectUtil.getField("name"+i, fd.getClass());
    			if( null != fName ) {
    				ReflectUtil.setField(fName, fd, field.getFdCnname());
    			}
    			Object object = valueMap.get("DYNA_MAIN_"+field.getFdEnname());
    			Field fValue = ReflectUtil.getField("value"+i, fd.getClass());
    			if( null != fValue ) {
    				ReflectUtil.setField(fValue, fd, object);
    			}
    			//特殊处理下拉框值
    			String filedName = "valuechange"+i;
    			this.processValueChange(field, object, filedName, fd);
			}
			List<FormData> l1 = new ArrayList<FormData>(1);
			l1.add(fd);
			rptDataList.add(new FastReportData<FormData>(FormData.class, this.tdFormTable.getEnName(), l1));
		}
		
		if(this.tdFormDef.getRows() > 0) {
			List<FormData> l2 = new ArrayList<FormData>(this.tdFormDef.getRows());
			TdFormTable subTable = this.tdFormTable.getChildList().get(0);
			List<TdFormField> subFieldList = subTable.getFieldList();
			for(int j=0; j<this.tdFormDef.getRows(); j++) {
				FormData fd = new FormData();
				
				for(int i=0; i<subFieldList.size(); i++) {
					TdFormField field = subFieldList.get(i);
					Field fName = ReflectUtil.getField("name"+i, fd.getClass());
	    			if(null != fName) {
	    				ReflectUtil.setField(fName, fd, field.getFdCnname());
	    			}
	    			Object object = valueMap.get("DYNA_SUB_"+field.getFdEnname()+"_"+j);
	    			Field fValue = ReflectUtil.getField("value"+i, fd.getClass());
	    			if( null != fValue) {
	    				ReflectUtil.setField(fValue, fd, object);
	    			}

	    			//特殊处理下拉框值
	    			String filedName = "valuechange"+i;
	    			this.processValueChange(field, object, filedName, fd);
				}
				l2.add(fd);
			}
			rptDataList.add(new FastReportData<FormData>(FormData.class, subTable.getEnName(), l2));
		}
		
		 
		
        
        Map<String,NodeDealVo> map = new HashMap<String, NodeDealVo>();
        List<NodeDealVo> list = flowContext.findTasksDealedInfo(this.sessionData.getUser());
        if(null != list && list.size() > 0) {
            for (NodeDealVo t : list) {
                map.put(t.getTaskDefKey().replaceAll("[0-9]", ""),t);
            }
            
//    		List<NodeDealVo> temList = new ArrayList<NodeDealVo>(map.values());
//            rptDataList.add(new FastReportData<NodeDealVo>(NodeDealVo.class, "dtbdForm", temList));
//			
            
            for(Entry<String, NodeDealVo> entry : map.entrySet()) {
            	List<NodeDealVo> temList = new ArrayList<NodeDealVo>(1);
            	temList.add(entry.getValue());
            	rptDataList.add(new FastReportData<NodeDealVo>(NodeDealVo.class, entry.getKey(), temList));
            }
            
        }
		return rptDataList;
	}

	/**
	 * 特殊处理值
	 * @param field
	 * @param valObj
	 * @param i
	 * @param fd
	 */
	private void processValueChange(TdFormField field, Object valObj,String filedName, FormData fd) {
		if( null != field && null != fd && StringUtils.isNotBlank(filedName)){
			// 如果为单选框，则需要查询字典进行赋值 "valuechange" + i
			String dataSrc = field.getDataSrc();
			Field fValueChange = ReflectUtil.getField(filedName, fd.getClass());
			if ( ("dict_select_one".equals(dataSrc) ||"dict_select_many".equals(dataSrc)  ) && null != fValueChange) {
				String valChange = null;
				if (null != valObj) {
					String objVal = valObj.toString();
					String codeTypeNo = field.getCodeTypeNo();
					if (StringUtils.isNotBlank(codeTypeNo)) {
						List<TsSimpleCode> sList = commService.findSimpleCodeListByTypeNo(codeTypeNo);
						if (null != sList) {
							StringBuilder tempStr = new StringBuilder();
							for (TsSimpleCode ts : sList) {
								if ( ("@#@" + objVal + "@#@").indexOf("@#@" + ts.getCodeNo() + "@#@") != -1) {
									tempStr.append(",").append(ts.getCodeName());
									break;
								}
							}
							if(tempStr.length() > 1){
								valChange = tempStr.substring(1);
							}
						}
					} else if (StringUtils.isNotBlank(field.getQuerySql())) {
						List<Object[]> findDataBySqlNoPage = commService.findDataBySqlNoPage(field.getQuerySql(), null);
						if (null != findDataBySqlNoPage) {
							StringBuilder tempStr = new StringBuilder();
							for (Object[] objArr : findDataBySqlNoPage) {
								if ( ("@#@" + objVal + "@#@").indexOf("@#@" + objArr[0] + "@#@") != -1) {
									tempStr.append(",").append(objArr[1]);
									break;
								}
							}
							if(tempStr.length() > 1){
								valChange = tempStr.substring(1);
							}
						}
					}
				}
				ReflectUtil.setField(fValueChange, fd, valChange);
			}
		}
	}
	
	@Override
	public List<FastReportDataRef> supportFastReportDataRef() {
		return null;
	}

	public int getFormType() {
		return formType;
	}
	public void setFormType(int formType) {
		this.formType = formType;
	}
	public String getHtml() {
		return html;
	}
	public void setHtml(String html) {
		this.html = html;
	}
	public String getDataJson() {
		return dataJson;
	}
	public void setDataJson(String dataJson) {
		this.dataJson = dataJson;
	}
	public TdFormDef getTdFormDef() {
		return tdFormDef;
	}
	public void setTdFormDef(TdFormDef tdFormDef) {
		this.tdFormDef = tdFormDef;
	}

}
