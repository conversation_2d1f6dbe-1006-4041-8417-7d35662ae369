package com.chis.modules.flow.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;

import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsTxtype;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.BackNodeVO;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesBean;

/**
 * 内容宽度：550 高度200
 * 回退的时候节点选择 
 * <AUTHOR>
 */
@ManagedBean(name = "backNodeSelectBean")
@ViewScoped
public class BackNodeSelectBean extends FacesBean{

	private static final long serialVersionUID = 3310862190370357018L;
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	/**当前任务ID*/
	private String taskId;
	// 指定退回的节点ID
	private String backNodeId;
	/** 流程意见 */
	private String advice;
	/** 常用语 */
	private List<TsSimpleCode> cyyList;
	/** 通讯方式 */
	private List<TsTxtype> txtypeList;
	/** 选择的通讯方式 */
	private List<String> selectedTxtypeList = new ArrayList<String>(0);	
	
	/**可供选择的退回节点*/
	private List<List<BackNodeVO>> backNodeList;
	
	
	public BackNodeSelectBean() {
		this.taskId = JsfUtil.getRequest().getParameter("taskId");
		
		/**
		 * 初始化可退回节点
		 */
		this.backNodeList = this.flowBusinessService.findBackableNodeList(this.taskId);
		
		//初始化通讯方式
		this.txtypeList = this.flowBusinessService.findTxtypeByTaskId(this.taskId);
		//初始化常用语
		this.initCyyList();
	}
	
	/**
	 * 初始化常用语
	 */
	private void initCyyList() {
		this.cyyList = this.commService.findSimpleCodesByTypeId("1101");
	}
	
	/**
	 * 选择确定方法
	 */
	public void selectAction() {
		if(StringUtils.isBlank(this.backNodeId)) {
			JsfUtil.addErrorMessage("请勾选要退回的流程环节！");
			return;
		}else if(this.backNodeId.startsWith(",")){
			this.backNodeId = this.backNodeId.replaceFirst(",", "");
		}
		
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("backNodeId", this.backNodeId);
		map.put("advice", this.advice);
		
		//通讯方式
		List<TsTxtype> txList = new ArrayList<TsTxtype>();
		if(null != this.selectedTxtypeList && this.selectedTxtypeList.size() > 0) {
			for(String typeId : this.selectedTxtypeList) {
				for(TsTxtype tx : this.txtypeList) {
					if(tx.getRid().toString().equals(typeId)) {
						txList.add(tx);
						continue;
					}
				}
			}
		}
		map.put("selectedTxtypeList", txList);
		
		//各节点的待办人
		List<TsUserInfo> selectedUserList = new ArrayList<TsUserInfo>();
		for(List<BackNodeVO> list : this.backNodeList) {
			for(BackNodeVO vo : list) {
				if((","+this.backNodeId+",").indexOf(","+vo.getActivitiNodeKey()+",") > -1) {
					selectedUserList.addAll(vo.getAssigeeList());
				}
			}
		}
		map.put("selectedUserList", selectedUserList);
		
		RequestContext.getCurrentInstance().closeDialog(map);
	}

	/**
	 * 关闭
	 */
	public void dialogClose() {
		RequestContext.getCurrentInstance().closeDialog(null);
	}

	public String getBackNodeId() {
		return backNodeId;
	}

	public void setBackNodeId(String backNodeId) {
		this.backNodeId = backNodeId;
	}

	public String getAdvice() {
		return advice;
	}

	public void setAdvice(String advice) {
		this.advice = advice;
	}

	public List<TsSimpleCode> getCyyList() {
		return cyyList;
	}

	public void setCyyList(List<TsSimpleCode> cyyList) {
		this.cyyList = cyyList;
	}

	public List<TsTxtype> getTxtypeList() {
		return txtypeList;
	}

	public void setTxtypeList(List<TsTxtype> txtypeList) {
		this.txtypeList = txtypeList;
	}

	public List<String> getSelectedTxtypeList() {
		return selectedTxtypeList;
	}

	public void setSelectedTxtypeList(List<String> selectedTxtypeList) {
		this.selectedTxtypeList = selectedTxtypeList;
	}

	public List<List<BackNodeVO>> getBackNodeList() {
		return backNodeList;
	}

	public void setBackNodeList(List<List<BackNodeVO>> backNodeList) {
		this.backNodeList = backNodeList;
	}

}
