package com.chis.modules.flow.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;
import org.primefaces.model.DualListModel;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.entity.TbFlowAdvtemplate;
import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.activiti.utils.BusinessIdUtil;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.flow.logic.FlowChoiceConditionBean;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsTxtype;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.MetaCondition;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesBean;

/**
 * 选人跳转弹出框
 * 内容宽度：830，内容高度500
 * 
 * 2014-9-2 david 根据规则查询待办人入参修改
 * 
 * <AUTHOR>
 */
@ManagedBean(name = "tdFlowSubmitGDXRBean")
@ViewScoped
public class TdFlowSubmitGDXRBean extends FacesBean {

	private static final long serialVersionUID = -5927665119395504030L;

	/** 存在session中的对象 */
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	/**单位ID*/
	private Integer searchUnitId;
	/**单位下拉*/
	private Map<String, Integer> searchUnitMap;
	/**科室ID*/
	private Integer searchOfficeId;
	/**科室下拉*/
	private Map<String, Integer> searchOfficeMap = new LinkedHashMap<String, Integer>();
    /**用户的选择*/
    private DualListModel dualListModel = new DualListModel();
    /**可以选择的用户*/
    private List<TsUserInfo> allUserList;
	/** 流程意见 */
	private String advice;
	/** 当前节点 */
	private TdFlowNode curNode;
	/** 常用语 */
	private List<TsSimpleCode> cyyList;
	/** 意见模板 */
	private List<TbFlowAdvtemplate> advTemplateList;
	/** 通讯方式 */
	private List<TsTxtype> txtypeList;
	/** 选择的通讯方式 */
	private List<String> selectedTxtypeList = new ArrayList<String>(0);
	
	public TdFlowSubmitGDXRBean() {
		//传过来的条件,JSON格式
		String conJson = JsfUtil.getRequest().getParameter("condition");
		
		if(StringUtils.isNotBlank(conJson)) {
			FlowChoiceConditionBean bean = JSON.parseObject(StringUtils.decode(conJson), FlowChoiceConditionBean.class);
			
			//当前节点
			if(StringUtils.isNotBlank(bean.getCurrentFlowNodeJSON())) {
				this.curNode = flowBusinessService.findTdFlowNode(new Integer(bean.getCurrentFlowNodeJSON()));
			}
			
			
			//待办人用业务模块传递进来的
			if(StringUtils.isNotBlank(bean.getAssigeesJSON())) {
				this.allUserList = JSON.parseArray(bean.getAssigeesJSON(), TsUserInfo.class);
			}
			
			List<TsUserInfo> sourceList = this.allUserList.subList(0, this.allUserList.size());
			/**
			 * 如果可选人只有1个人的话，自动选择
			 */
			if(null != sourceList && sourceList.size() == 1) {
				this.dualListModel = new DualListModel(new ArrayList(0),sourceList);
			}else {
				this.dualListModel = new DualListModel(sourceList, new ArrayList(0));
			}
			
			/**
			 * 根据allUserList初始化查询条件
			 */
			this.initUnitList();
			
			//初始化通讯方式
			this.txtypeList = this.flowBusinessService.findTxtypeByNodeId(this.curNode.getRid());
			
			//初始化流程意见
			this.initAdvTemplate(this.curNode.getRid(), BusinessIdUtil.parsePK(bean.getBusinessIdJSON()));
			
			//初始化常用语
			this.initCyyList();
		}
	}
	
	/**
	 * 根据待办人员初始化单位
	 */
	private void initUnitList() {
		this.searchUnitMap = new LinkedHashMap<String, Integer>();
		if(null != this.allUserList && this.allUserList.size() > 0) {
			for(TsUserInfo user : this.allUserList) {
				this.searchUnitMap.put(user.getTsUnit().getUnitSimpname(), user.getTsUnit().getRid());
			}
		}
	}
	
	/**
	 * 单位选择事件，需要刷新科室下拉和人员列表
	 */
	public void unitChangeAction() {
		this.searchOfficeId = null;
		this.searchOfficeMap.clear();
		if(null != this.searchUnitId) {
			//过滤出科室Id
			StringBuilder officeIds = new StringBuilder();
			if(null != this.allUserList && this.allUserList.size() > 0) {
				for(TsUserInfo user : this.allUserList) {
					if(this.searchUnitId.equals(user.getTsUnit().getRid())) {
						String offIds = user.getOfficeIds();
						officeIds.append(",").append(offIds.substring(1, offIds.length()-1));
					}
				}
			}
			if(officeIds.length() > 1)	{
				List<TsOffice> findOfficesByIds = commService.findOfficesByIds(officeIds.substring(1));
				if( null != findOfficesByIds && findOfficesByIds.size() > 0)	{
					for(TsOffice tsOffice :findOfficesByIds)	{
						this.searchOfficeMap.put(tsOffice.getOfficename(), tsOffice.getRid());
					}
				}
			}
		}
	}
	
	/**
	 * 科室选择事件，需要刷新人员列表
	 */
	public void officeChangeAction() {
		List<TsUserInfo> targetList = this.dualListModel.getTarget();
		//已选的人
		Map<Integer, TsUserInfo> targetMap = new HashMap<Integer, TsUserInfo>();
		if(null != targetList && targetList.size() > 0) {
			for(TsUserInfo t: targetList) {
				targetMap.put(t.getRid(), t);
			}
		}
		
		List<TsUserInfo> sourceList = new ArrayList<TsUserInfo>();
		if(null != this.allUserList && this.allUserList.size() > 0) {
			if(null != this.searchOfficeId) {
				for (TsUserInfo t : this.allUserList) {
					if(t.getOfficeIds().indexOf(","+this.searchOfficeId+",") != -1 && null == targetMap.get(t.getRid())) {
						sourceList.add(t);
					}
				}
			}else {
				for (TsUserInfo t : this.allUserList) {
					if(null == targetMap.get(t.getRid())) {
						sourceList.add(t);
					}
				}
			}
		}
		//初始化用户
		this.dualListModel.setSource(sourceList);
		this.dualListModel.setTarget(targetList);
	}
	
	/**
	 * 初始化常用语
	 */
	private void initCyyList() {
		this.cyyList = this.commService.findSimpleCodesByTypeId("1101");
	}
	
	/**
	 * 初始化意见模板
	 */
	private void initAdvTemplate(Integer nodeId, String businessId) {
		//构建模板的依赖条件
		MetaCondition metaCondition = MetaConditionFactory.produceCondition(this.sessionData, businessId, null);
		//查询该节点的意见模板集合
		this.advTemplateList = this.flowBusinessService.findAdvTemplateList(Integer.valueOf(nodeId), metaCondition);
		/**
		 * 初始化流程意见，如果配了流程意见的话
		 */
		if(null != this.advTemplateList && this.advTemplateList.size() > 0) {
			for(TbFlowAdvtemplate adv : this.advTemplateList) {
				if(adv.getIsDefault().intValue() == 1) {
					this.advice = adv.getAfterAnalyContent();
					return;
				}
			}
		}
	}
	
	/**
	 * 确定
	 */
	public void personSelectAction() {
		List<TsUserInfo> targetList = this.dualListModel.getTarget();
		if (null == targetList || targetList.size() == 0) {
			JsfUtil.addErrorMessage("请选择待办人员！");
			return;
		}else {
			if(this.curNode.getIfAdv().intValue() == 1) {
				if(StringUtils.isBlank(this.advice)) {
					JsfUtil.addErrorMessage("请填写流程意见！");
					return;
				}
			}
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("advice", this.advice);
			
			//通讯方式
			List<TsTxtype> txList = new ArrayList<TsTxtype>();
			if(null != this.selectedTxtypeList && this.selectedTxtypeList.size() > 0) {
				for(String typeId : this.selectedTxtypeList) {
					for(TsTxtype tx : this.txtypeList) {
						if(tx.getRid().toString().equals(typeId)) {
							txList.add(tx);
							continue;
						}
					}
				}
			}
			map.put("selectedTxtypeList", txList);
			map.put("selectedUserList", targetList);
			
			RequestContext.getCurrentInstance().closeDialog(map);
		}
	}

	/**
	 * 关闭
	 */
	public void dialogClose() {
		RequestContext.getCurrentInstance().closeDialog(null);
	}

	public String getAdvice() {
		return advice;
	}

	public void setAdvice(String advice) {
		this.advice = advice;
	}

	public DualListModel getDualListModel() {
		return dualListModel;
	}

	public void setDualListModel(DualListModel dualListModel) {
		this.dualListModel = dualListModel;
	}

	public TdFlowNode getCurNode() {
		return curNode;
	}

	public void setCurNode(TdFlowNode curNode) {
		this.curNode = curNode;
	}

	public List<TsSimpleCode> getCyyList() {
		return cyyList;
	}

	public void setCyyList(List<TsSimpleCode> cyyList) {
		this.cyyList = cyyList;
	}

	public Integer getSearchUnitId() {
		return searchUnitId;
	}

	public void setSearchUnitId(Integer searchUnitId) {
		this.searchUnitId = searchUnitId;
	}

	public Integer getSearchOfficeId() {
		return searchOfficeId;
	}

	public void setSearchOfficeId(Integer searchOfficeId) {
		this.searchOfficeId = searchOfficeId;
	}

	public Map<String, Integer> getSearchUnitMap() {
		return searchUnitMap;
	}

	public void setSearchUnitMap(Map<String, Integer> searchUnitMap) {
		this.searchUnitMap = searchUnitMap;
	}

	public Map<String, Integer> getSearchOfficeMap() {
		return searchOfficeMap;
	}

	public void setSearchOfficeMap(Map<String, Integer> searchOfficeMap) {
		this.searchOfficeMap = searchOfficeMap;
	}

	public List<TsTxtype> getTxtypeList() {
		return txtypeList;
	}

	public void setTxtypeList(List<TsTxtype> txtypeList) {
		this.txtypeList = txtypeList;
	}

	public List<String> getSelectedTxtypeList() {
		return selectedTxtypeList;
	}

	public void setSelectedTxtypeList(List<String> selectedTxtypeList) {
		this.selectedTxtypeList = selectedTxtypeList;
	}

	public List<TbFlowAdvtemplate> getAdvTemplateList() {
		return advTemplateList;
	}

	public void setAdvTemplateList(List<TbFlowAdvtemplate> advTemplateList) {
		this.advTemplateList = advTemplateList;
	}

}
