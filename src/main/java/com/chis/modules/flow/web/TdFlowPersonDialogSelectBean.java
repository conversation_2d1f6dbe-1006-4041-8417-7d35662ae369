package com.chis.modules.flow.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.event.NodeSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.DualListModel;
import org.primefaces.model.TreeNode;

import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.web.FacesBean;

/**
 * dialog方式的选人
 * 
 * 宽度750， 高度450
 * 
 * <AUTHOR>
 */
@ManagedBean(name = "tdFlowPersonDialogSelectBean")
@ViewScoped
public class TdFlowPersonDialogSelectBean extends FacesBean {

	private static final long serialVersionUID = -5927665119395504030L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);

	/** 科室名称 */
	private String userOfficeName;
	/** 科室ID */
	private Integer userOfficeRid;
	/** 流程类型树 */
	private TreeNode userOfficeTreeNode;
	/** 用于机构授权的选择 */
	private DualListModel dualListModel = new DualListModel();
	/** 可以选择的用户 */
	private List<TsUserInfo> allUserList;
	/** 选择的用户IDS */
	private String selectedManIds;
	/** 选择的用户名 */
	private String selectedManNames;

	public TdFlowPersonDialogSelectBean() {
		System.out.println("---err--");
		
		String nodeId = JsfUtil.getRequest().getParameter("nodeId");
		String selectIds = JsfUtil.getRequest().getParameter("selectIds");
		
		//当前任务ID
		String taskId = JsfUtil.getRequest().getParameter("taskId");
		if (StringUtils.isNotBlank(nodeId)) {
			this.initOfficeTree();
			
			//查询条件
			ResultCondition condition = new ResultCondition();
			condition.setNodeId(nodeId);
			condition.setUserId(this.sessionData.getUser().getRid());
			
			// nodeId节点所有可选的待办人
			this.allUserList = this.flowBusinessService.findUsersByNodeId(condition, taskId);

			List<TsUserInfo> sourceList = new ArrayList<TsUserInfo>();
			List<TsUserInfo> targetList = new ArrayList<TsUserInfo>();
			if (StringUtils.isNotBlank(selectIds)) {
				selectIds = "," + selectIds + ",";
				for (TsUserInfo t : this.allUserList) {
					if (StringUtils.contains(selectIds, "," + t.getRid() + ",")) {
						targetList.add(t);
					} else {
						sourceList.add(t);
					}
				}
				this.dualListModel.setSource(sourceList);
				this.dualListModel.setTarget(targetList);
			} else {
				sourceList = this.allUserList.subList(0, this.allUserList.size());
				this.dualListModel.setSource(sourceList);
				this.dualListModel.setTarget(targetList);
			}
		}
	}

	/**
	 * 初始化科室树
	 */
	private void initOfficeTree() {
		// 初始化科室树
		if (null == this.userOfficeTreeNode) {
			this.userOfficeTreeNode = new DefaultTreeNode("root", null);
			List<TsOffice> list = this.flowBusinessService.findOfficeList(this.sessionData.getUser().getTsUnit().getRid());
			if (null != list && list.size() > 0) {
				Map<Integer, TreeNode> map = new HashMap<Integer, TreeNode>();
				for (TsOffice t : list) {
					if (null == t.getParentOffice()) {
						TreeNode node = new DefaultTreeNode(t, this.userOfficeTreeNode);
						map.put(t.getRid(), node);
					} else {
						TreeNode node = new DefaultTreeNode(t, map.get(t.getParentOffice().getRid()));
						map.put(t.getRid(), node);
					}
				}
				map.clear();
			}
		}
	}

	/**
	 * 科室选中事件
	 * 
	 * @param event
	 *            选中事件
	 */
	public void onOfficeNodeSelect(NodeSelectEvent event) {
		TreeNode node = event.getTreeNode();
		if (null != node) {
			TsOffice office = (TsOffice) node.getData();
			this.userOfficeRid = office.getRid();
			this.userOfficeName = office.getOfficename();

			/**
			 * 同时刷新可选用户
			 */
			List<TsUserInfo> targetList = this.dualListModel.getTarget();
			// 已选的人
			Map<Integer, TsUserInfo> targetMap = new HashMap<Integer, TsUserInfo>();
			if (null != targetList && targetList.size() > 0) {
				for (TsUserInfo t : targetList) {
					targetMap.put(t.getRid(), t);
				}
			}

			List<TsUserInfo> sourceList = new ArrayList<TsUserInfo>();
			if (null != this.allUserList && this.allUserList.size() > 0) {
				for (TsUserInfo t : this.allUserList) {
					if (StringUtils.contains(t.getOfficeIds(),","+this.userOfficeRid+",") && null == targetMap.get(t.getRid())) {
						sourceList.add(t);
					}
				}
			}
			// 初始化用户
			this.dualListModel.setSource(sourceList);
			this.dualListModel.setTarget(targetList);
		}
	}

	/**
	 * 确定
	 */
	public void personSelectAction() {
		List<TsUserInfo> targetList = this.dualListModel.getTarget();
		if (null != targetList && targetList.size() > 0) {
			StringBuilder sb1 = new StringBuilder();
			StringBuilder sb2 = new StringBuilder();
			for (TsUserInfo t : targetList) {
				sb1.append(",").append(t.getUsername());
				sb2.append(",").append(t.getRid());
			}
			this.selectedManNames = sb1.toString().substring(1);
			this.selectedManIds = sb2.toString().substring(1);
		} else {
			this.selectedManNames = null;
			this.selectedManIds = null;
		}
	}

	public String getUserOfficeName() {
		return userOfficeName;
	}

	public void setUserOfficeName(String userOfficeName) {
		this.userOfficeName = userOfficeName;
	}

	public Integer getUserOfficeRid() {
		return userOfficeRid;
	}

	public void setUserOfficeRid(Integer userOfficeRid) {
		this.userOfficeRid = userOfficeRid;
	}

	public TreeNode getUserOfficeTreeNode() {
		return userOfficeTreeNode;
	}

	public void setUserOfficeTreeNode(TreeNode userOfficeTreeNode) {
		this.userOfficeTreeNode = userOfficeTreeNode;
	}

	public DualListModel getDualListModel() {
		return dualListModel;
	}

	public void setDualListModel(DualListModel dualListModel) {
		this.dualListModel = dualListModel;
	}

	public String getSelectedManIds() {
		return selectedManIds;
	}

	public void setSelectedManIds(String selectedManIds) {
		this.selectedManIds = selectedManIds;
	}

	public String getSelectedManNames() {
		return selectedManNames;
	}

	public void setSelectedManNames(String selectedManNames) {
		this.selectedManNames = selectedManNames;
	}

}
