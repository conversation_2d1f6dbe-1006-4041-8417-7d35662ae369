package com.chis.modules.flow.web;

import java.io.File;
import java.io.FilenameFilter;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;
import org.primefaces.model.UploadedFile;

import com.chis.activiti.entity.TdFlowType;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.FileUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.web.FacesBean;

/**
 * 流程类型维护
 * 
 * <AUTHOR>
 * @history 2014-07-09
 */
@ManagedBean(name = "tdFlowTypeBean")
@ViewScoped
public class TdFlowTypeBean extends FacesBean {

	private static final long serialVersionUID = 2695401462098651426L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	/** 类型名称 */
	private String searchTypeName;
	/** 树 */
	private TreeNode treeNode;
	/** 选中的树 */
	private TreeNode selectedNode;
	/** 流程类型bean */
	private TdFlowType tdFlowType;
	/** rid */
	private Integer rid;
	/** 图片接受 */
	private List<String> imgUrlList;
	/** 添加子节点 */
	private Boolean isCode;
	/** 是否查询到了数据 */
	private boolean haveData;
    /** 删除图片集合 */
    private List<String> delList = new ArrayList<String>();

	@PostConstruct
	public void init() {
		this.imgListInit();
		this.initTree();
	}

	/**
	 * 文件上传
	 */
	public void handlePicUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			String fileName = file.getFileName();
			String uuid = UUID.randomUUID().toString().replaceAll("-", "");
			String hz = fileName.substring(fileName.lastIndexOf(".") + 1);

			// 配置的虚拟路径
			String xnPath = JsfUtil.getAbsolutePath();

			String showDir = new StringBuilder("/mobile/oa/").append(uuid).append(".").append(hz).toString();
			String filePath = new StringBuilder(xnPath).append(showDir).toString();
			this.tdFlowType.setMobilePicUrl(showDir);
			try {
				FileUtils.copyFile(filePath, file.getInputstream());
				JsfUtil.addSuccessMessage("上传成功！");
				RequestContext.getCurrentInstance().execute("filePicUIdVar.hide();");
			} catch (IOException e) {
				FacesMessage msg = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", msg);
				e.printStackTrace();
			}
		}
	}
    
    public void deleteMobilePic(){
    	String mobilePicUrl = this.tdFlowType.getMobilePicUrl();
    	if(StringUtils.isNotBlank(mobilePicUrl)){
    		delList.add(mobilePicUrl);
    		this.tdFlowType.setMobilePicUrl(null);
    	}
    }

    /* 文件删除 */
    public void deleteDiskPic() {
        if(null != delList && delList.size() > 0){
        	String xnPath = JsfUtil.getAbsolutePath();
            for(String s : delList){
            	try{
            		String filePath = xnPath+s;
            		new File(filePath).delete();
            	}catch(Exception e){
            		e.printStackTrace();
            	}
            }
            delList.clear();
        }
    }
	
	private void initTree() {
		this.isCode = false;
		addInit();
		/** 初始化树 */
		List<TdFlowType> list = this.flowBusinessService.searchInit();
		this.treeNode = new DefaultTreeNode("root", null);
		// 数据总map
		Map<Integer, TreeNode> treeMap = new LinkedHashMap<Integer, TreeNode>();
		for (TdFlowType t : list) {
			if (null == t.getParent()) {// 没有上级
				TreeNode node = new DefaultTreeNode(t, treeNode);
				treeMap.put(t.getRid(), node);
			} else {// 有上级
				TreeNode node = treeMap.get(t.getParent().getRid());
				TreeNode parentNode = null;
				if (null != node) {
					parentNode = new DefaultTreeNode(t, node);
				} else {
					parentNode = new DefaultTreeNode(t, treeNode);
				}
				treeMap.put(t.getRid(), parentNode);
			}
		}
	}

	/**
	 * 首页面查询，根据分类名称遍历树
	 */
	public void searchAction() {
		if (StringUtils.isNotBlank(this.searchTypeName)) {
			this.haveData = false;
			if (null != this.treeNode) {
				if (null != this.treeNode.getChildren()
						&& this.treeNode.getChildren().size() > 0) {
					for (TreeNode t : this.treeNode.getChildren()) {
						// 展出选择前，先清空之前所有的展出、选择
						expandedTreeFalse(t);
						TdFlowType type = (TdFlowType) t.getData();
						if (type.getTypeName().indexOf(this.searchTypeName) != -1) {
							this.haveData = true;
							// 查找到信息，将每个节点逐个展出
							t.setSelected(true);
							findTree(t);
							expandedTree(t);
						} else {// 本级树没有找到
							findTree(t);
						}
					}
				}
				if (!this.haveData) {
					JsfUtil.addSuccessMessage("没有您要找的记录！");
				}
			}
		}
	}

	/**
	 * 添加初始化
	 */
	public void addInit() {
		this.rid = null;
		this.tdFlowType = new TdFlowType();
	}

	/***
	 * 修改初始化
	 */
	public void modInit() {
		TdFlowType s = (TdFlowType) this.selectedNode.getData();
		this.rid = s.getRid();
		this.tdFlowType = this.flowBusinessService.findFlowType(this.rid);
		if (StringUtils.isNotBlank(this.tdFlowType.getPicPath())) {
			String path = this.tdFlowType.getPicPath().replaceAll(
					"/resources/images/flowType/", "");
			this.tdFlowType.setPicPath(path);
		}
	}

	/**
	 * 删除
	 */
	public void deleteAction() {
		TdFlowType s = (TdFlowType) this.selectedNode.getData();
		this.rid = s.getRid();
		String msg = this.flowBusinessService.deleteFlowType(this.rid);
		if (StringUtils.isNotBlank(msg)) {
			JsfUtil.addErrorMessage(msg);
			return;
		} else {
			this.initTree();
		}
	}

	/**
	 * 保存
	 */
	public void saveAction() {
		TsUserInfo tsUserInfo = this.sessionData.getUser();
		if (null != this.rid) {
			this.tdFlowType.setModifyDate(new Date());
			this.tdFlowType.setModifyManid(tsUserInfo.getRid());
		} else {
			this.tdFlowType.setCreateDate(new Date());
			this.tdFlowType.setCreateManid(tsUserInfo.getRid());
		}

		// 如果是添加子节点
		if (this.isCode) {
			this.tdFlowType.setParent((TdFlowType) this.selectedNode.getData());
		}
		if (StringUtils.isNotBlank(this.tdFlowType.getPicPath())) {
			this.tdFlowType.setPicPath("/resources/images/flowType/"
					+ this.tdFlowType.getPicPath());
		}
		String msg = this.flowBusinessService
				.saveOrUpdateFlowType(this.tdFlowType);
		if (StringUtils.isNotBlank(msg)) {
			if (StringUtils.isNotBlank(this.tdFlowType.getPicPath())) {
				String path = this.tdFlowType.getPicPath().replaceAll(
						"/resources/images/flowType/", "");
				this.tdFlowType.setPicPath(path);
			}
			JsfUtil.addErrorMessage(msg);
			return;
		} else {
			RequestContext requestContext = RequestContext.getCurrentInstance();
			requestContext.execute("TypeEditDialog.hide()");
			this.searchTypeName = this.tdFlowType.getTypeName();
			this.initTree();
			this.deleteDiskPic();
			// 新增后，重新构建树、定位到新保存的数据
			searchAction();
		}
	}

	/**
	 * 遍历树 清空每个节点的展出、选择
	 * 
	 * @param treeNode
	 *            树节点
	 */
	private void expandedTreeFalse(TreeNode treeNode) {
		treeNode.setSelected(false);
		treeNode.setExpanded(false);
		for (TreeNode t : treeNode.getChildren()) {
			t.setSelected(false);
			t.setExpanded(false);
			expandedTreeFalse(t);
		}
	}

	/**
	 * 根据分类名称遍历树
	 * 
	 * @param treeNode
	 *            树节点
	 */
	private void findTree(TreeNode treeNode) {
		for (TreeNode t : treeNode.getChildren()) {
			TdFlowType type = (TdFlowType) t.getData();
			// 如果树中的名称与查询的分类名称相符，则将每个节点逐个展出
			if (type.getTypeName().indexOf(this.searchTypeName) != -1) {
				this.haveData = true;
				// 查找到信息，将每个节点逐个展出
				t.setSelected(true);
				findTree(t);
				expandedTree(t);
			} else {// 本级树没有找到，重新遍历
				findTree(t);
			}
		}
	}

	/**
	 * 遍历树 将每个节点逐个展出
	 * 
	 * @param treeNode
	 *            树节点
	 */
	private void expandedTree(TreeNode treeNode) {
		if (null != treeNode.getParent()) {
			treeNode.getParent().setExpanded(true);
			expandedTree(treeNode.getParent());
		}
	}

	/**
	 * 图片列表初始化
	 */
	private void imgListInit() {
		String filePath = new StringBuilder(FileUtils.getWebRootPath()).append(
				"resources/images/flowType").toString();
		String[] imgNames = imgNameGet(filePath);
		imgUrlList = new LinkedList<String>();
		if (null != imgNames && imgNames.length > 0) {
			for (int i = 0; i < imgNames.length; i++) {
				imgUrlList.add(imgNames[i]);
			}
		}
	}

	/**
	 * 获取指定路径下的，所有图片名称及后缀名
	 * 
	 * @param path
	 *            路径
	 * @return 图片数组
	 */
	private String[] imgNameGet(String path) {
		File file = new File(path);
		String[] fns = file.list(new FilenameFilter() {
			public boolean accept(File dir, String name) {
				if (name.toLowerCase().indexOf("png") != -1
						|| name.toLowerCase().indexOf("gif") != -1
						|| name.toLowerCase().indexOf("jpg") != -1
						|| name.toLowerCase().indexOf("bmp") != -1) {
					return true;
				} else {
					return false;
				}
			}
		});
		return fns;
	}

	public String getSearchTypeName() {
		return searchTypeName;
	}

	public void setSearchTypeName(String searchTypeName) {
		this.searchTypeName = searchTypeName;
	}

	public TreeNode getTreeNode() {
		return treeNode;
	}

	public void setTreeNode(TreeNode treeNode) {
		this.treeNode = treeNode;
	}

	public TreeNode getSelectedNode() {
		return selectedNode;
	}

	public void setSelectedNode(TreeNode selectedNode) {
		this.selectedNode = selectedNode;
	}

	public TdFlowType getTdFlowType() {
		return tdFlowType;
	}

	public void setTdFlowType(TdFlowType tdFlowType) {
		this.tdFlowType = tdFlowType;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public List<String> getImgUrlList() {
		return imgUrlList;
	}

	public void setImgUrlList(List<String> imgUrlList) {
		this.imgUrlList = imgUrlList;
	}

	public Boolean getIsCode() {
		return isCode;
	}

	public void setIsCode(Boolean isCode) {
		this.isCode = isCode;
	}

	public boolean isHaveData() {
		return haveData;
	}

	public void setHaveData(boolean haveData) {
		this.haveData = haveData;
	}

}
