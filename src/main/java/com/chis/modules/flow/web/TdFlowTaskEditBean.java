package com.chis.modules.flow.web;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.activiti.engine.ActivitiIllegalArgumentException;
import org.activiti.engine.history.HistoricTaskInstance;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.entity.TdFlowDef;
import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.entity.TdFlowNodeBtn;
import com.chis.activiti.enumn.FlowBtnType;
import com.chis.activiti.enumn.FlowInType;
import com.chis.activiti.enumn.SupportAssigeeType;
import com.chis.activiti.javabean.BusinessButton;
import com.chis.activiti.service.IBusinessService;
import com.chis.activiti.service.IFlowBeanService;
import com.chis.activiti.service.IFlowButtons;
import com.chis.activiti.service.IFlowPassBeanService;
import com.chis.activiti.service.ISetAssignMen;
import com.chis.activiti.service.ISetAssignee;
import com.chis.activiti.service.impl.ActivitiServiceImpl;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MapUtils;
import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SimplePropertyFilter;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.flow.logic.FlowChoiceConditionBean;
import com.chis.modules.system.entity.TsTxtype;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.TxType;
import com.chis.modules.system.interfaces.IFastReport;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.protocol.IMsgSend;
import com.chis.modules.system.protocol.PushMsgToAppImpl;
import com.chis.modules.system.protocol.SendEmailToUserImpl;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.MsgSendUtil;
import com.chis.modules.system.web.FacesBean;
import com.google.common.collect.Lists;

/**
 * 待办任务处理、提交<BR/>
 * 
 * <P>该页面有两种入口方式：</P> <BR/>
 * 	其一：由待办任务列表进来，有按钮操作 <BR/>
 * 		1.需要将待办任务列表的查询条件值传递进来，等页面跳转的时候再置回去 <BR/>
 *  其二：由消息传进来，或是待办，或是已办，或是详情（分发，多传一个参数ff） <BR/>
 *  	1.要根据read=true判断是否是只读 <BR/>
 *  
 *  <P>每一步流程流转，记录操作类型：</P> <BR/>
 *  ACT_OPT_TYPE:	 <BR/>
 *  	S：提交	<BR/>
 *   	R：驳回	<BR/>
 *   	D：删除	<BR/>
 *   	T：终止	<BR/>
 *   	F：办结	<BR/>
 *      C：撤销    <BR/>
 *      
 *  <P>终止、办结流程，TaskCompleteListener等是不会触发的，因此需要执行离开此节点的脚本。</P>
 *  
 *      
 *  
 * <AUTHOR> 2014-11-14
 * 
 */
@ManagedBean(name = "tdFlowTaskEditBean")
@ViewScoped
public class TdFlowTaskEditBean extends FacesBean implements IFlowBeanService, IFlowPassBeanService, IFlowButtons{

	private static final long serialVersionUID = -4322120926883719019L;

	/** 存在session中的对象 */
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	/** 是否只读 */
	private boolean readOnly = false;
	/**能否显示的按钮 key-按钮类型，见FlowBtnType value-TdFlowNodeBtn对象*/
	private Map<String, TdFlowNodeBtn> btnDispMap;
	/** 是否有打印功能 */
	private boolean printable;
	/** 任务ID */
	private String taskId;
	/** 当前节点ID */
	private String nodeId;
	/** 业务表单ID */
	private String businessId;
	/** 当前节点对象 */
	private TdFlowNode tdFlowNode = new TdFlowNode();
	/** 消息子表id */
	private String msgSubId;
	/** 操作类型 */
	private String actionType;
	/** 流程意见 */
	private String advice;
	/**
	 * 流程表单接口
	 */
	private IBusinessService businessService;
	/**
	 * 设置待办人接口
	 */
	private ISetAssignee assigneeService;
	/**提交是否成功 0-失败 1-成功*/
	private int actionState = 0;
	/**业务模块提供的按钮集合*/
	private List<BusinessButton> buttonList = new ArrayList<BusinessButton>(0);
	/**流程上下文*/
	private FlowContext flowContext;
	/** 从哪儿转向过来的 */
	private String fromUrl;
	/** 待办任务列表的URL */
	private  String DEFAULT_URL = "/webapp/flow/tdFlowTaskList.faces";
	/**上一步节点的意见*/
	private List<Object[]> lastNodeAdvice;
	/**是否分发*/
	private boolean ff = false;
	/**流程标题*/
	private String businessKey;
	/**委托人，如果是当前登录人的ID，那就不是委托人了*/
	private Integer trustId;
	private ActivitiServiceImpl activitiServiceImpl = SpringContextHolder.getBean(ActivitiServiceImpl.class);
	/**是否直接显示每一步的流程意见到页面上*/
	private Boolean dispPro = false;
	/**流程每步处理的意见*/
	private List<Object[]> advDataList = Lists.newArrayList();
	
	private TdFlowDef def;
	private Boolean flag = false;
	/**流程信息显示位置，默认为上方，根据参数可调整为下方*/
	private boolean flowInfoShow=true;
	
	public TdFlowTaskEditBean() {
		this.init();
	}
	
	private void init() {
		/**
		 * 消息URL要传taskId,nodeId,businessId,msgSubId,read,ff(分发); <br/>
		 * 待办任务,Request里面要传主界面的查询条件,taskId,nodeId,businessId,trustId,read,from(原url地址) <br/>
		 */
		this.taskId = JsfUtil.getRequest().getParameter("taskId");
		this.nodeId = JsfUtil.getRequest().getParameter("nodeId");
		this.businessId = JsfUtil.getRequest().getParameter("businessId");
		this.msgSubId = JsfUtil.getRequest().getParameter("msgSubId");
		this.fromUrl = JsfUtil.getRequest().getParameter("from");
		String trustStr = JsfUtil.getRequest().getParameter("trustId");
		if(StringUtils.isNotBlank(trustStr)) {
			this.trustId = Integer.valueOf(trustStr);
		}
		String ffStr = JsfUtil.getRequest().getParameter("ff");
		String read = JsfUtil.getRequest().getParameter("read");
		if(null != read && "true".equals(read)) {
			this.readOnly = true;
		}else {
			this.readOnly = false;
		}
		if(StringUtils.isNotBlank(ffStr)) {
			this.ff = true;
			this.readOnly = true;
		}
		//加载流程信息显示位置
		String showPlace= commService.findParamValue("FLOWINFO_SHOWPLACE");
		if("0".equals(showPlace)){
			flowInfoShow=false;
		}
		
		this.initData();
		
	}
	
	/**
	 * 初始化数据
	 */
	private void initData() {
		this.flowContext = new FlowContext(this.taskId, this.nodeId, this.businessId);
		this.businessKey = this.flowContext.getBusinessKey();
		
		this.tdFlowNode = this.flowContext.getTdFlowNode();
		System.err.println("flow_page:"+(null != this.tdFlowNode ? this.tdFlowNode.getJspUrl(): ""));
		JsfUtil.getRequestMap().put("manageBeanName", "tdFlowTaskEditBean");
		this.initBtnMap(this.tdFlowNode);
		
		def = this.flowContext.getTdFlowDef();
		if(null != def && null != def.getIsDispPro() && def.getIsDispPro().intValue() == 1) {
			this.dispPro = true;
			this.advDataList = this.flowBusinessService.findHisTaskRecord(this.taskId, 1);
		}else {
			//上一节点的处理人、处理时间、处理意见
			this.lastNodeAdvice = this.flowBusinessService.findLastNodeAdvice(this.taskId);
		}
	}
	
	/**
	 * 保存业务表单
	 */
    public void saveAction() {
		if (null != this.businessService) {
            /**
             * 返回null表示保存失败，否则保存成功！
             */
			this.businessId = this.businessService.saveAction();
            if(StringUtils.isBlank(this.businessId)) {
                return;
            }else {
            	Map<String, Object> map = new HashMap<String, Object>();
				map.put("businessId", this.businessId);
				this.flowBusinessService.saveVariable(this.taskId, map);
				
				JsfUtil.getRequestMap().put("taskId", this.taskId);
				this.businessService.afterSaveAction();
				
                if ("saveAction".equals(this.actionType)) {
                    JsfUtil.addSuccessMessage("保存成功！");
                }
            }
		}
    }

	/**
	 * 提交下一步 //0-自由跳转，1-选人跳转，2-拟办跳转
	 */
	public void submitAction() {
		this.saveAction();
        if(StringUtils.isBlank(this.businessId)) {
            return;
        }
		
		/**
		 * 首先判断当前节点是否是拟办节点， 如果是拟办节点，需要弹出被拟办的节点，以供选人，选完人提交下一步流程；
		 * 如果不是拟办节点，再判断当前节点是否是自由跳转节点， 如果是，直接提交下一步流程；
		 * 如果不是自由跳转，而是选择待办人跳转，则弹出选人框，并提交下一步流程 如果下一节点是空，则直接提交
		 */
		FlowInType flowInType = this.tdFlowNode.getFlowInType();
		switch(flowInType) {
			//拟办跳转
	    	case NBJD:
				this.nbtzAction();
	    		break;
	    		
	    	//自由跳转
	    	case ZYTZ:
				this.zytzAction();
	    		break;
	    		
	    	//选择跳转
	    	case XZTZ:
				/**
				 * 1.下一步要走多个节点
				 * 2.下一步要走1个节点
				 * 3.下一步是结束节点，但是业务模块传进来了待办人
				 * 4.下一步是结束节点
				 * 
				 * 1和2的情况都是根据配置的规则，和业务模块提供的待办人<br/>
				 * 进行或、且等合并
				 */
	    		List<TdFlowNode> nextNodeList = this.flowBusinessService.findNextNodeList(this.taskId, this.tdFlowNode.getTdFlowDef().getRid().toString(),
	    				this.businessService.addVariables(), this.tdFlowNode);
	    		//业务模块提供待办人的接口
	    		ISetAssignMen assignMen = (ISetAssignMen)this.businessService;
	    		String assigeeJson = assignMen.supportTaskMen();  //待办人
	    		SupportAssigeeType supportType = assignMen.supportType(); //关系
	    		
	    		if(null == nextNodeList || nextNodeList.size() == 0) {
	    			//下一步应该是结束节点，要判断业务模块是否提供待办人，如果提供还是要显示出来
	    			if(StringUtils.isNotBlank(assigeeJson)) {
	    				this.gdxrAction(assigeeJson, supportType);
	    			}else {
	    				this.zytzAction();
	    			}
	    		}else if(nextNodeList.size() == 1) {
	    			//下一步只有1个节点
	    			this.xztzAction(nextNodeList.get(0), assigeeJson, supportType);
	    		}else {
	    			//下一步要走多个节点
	    			this.djdXztzAction(nextNodeList, assigeeJson, supportType);
	    		}
	    		break;
	    //switch end		
		}
	}
	
	public void readAction() {
		this.saveAction();
        if(StringUtils.isBlank(this.businessId)) {
            return;
        }
        this.zytzReadAction();
	}
	
	/**
	 * 拟办跳转，打开拟办跳转的弹出框
	 */
	private void nbtzAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 830, null, 500);
		
		List<TdFlowNode> nbNodeList = this.flowBusinessService.findNbNextNodeList(this.taskId, this.tdFlowNode.getTdFlowDef().getRid().toString(),
				this.businessService.addVariables(), this.tdFlowNode);
		
		//构建传递的条件
		FlowChoiceConditionBean bean = new FlowChoiceConditionBean();
		//拟办要设置待办人的节点
		StringBuffer nextNodes = new StringBuffer();
		if (null != nbNodeList && nbNodeList.size() > 0) {
			for (TdFlowNode t : nbNodeList) {
				nextNodes.append(",").append(t.getRid());
			}
			// 下一步的节点
			bean.setNextFlowNodeJSON(nextNodes.substring(1));
		}
		//当前节点对象
		bean.setCurrentFlowNodeJSON(this.tdFlowNode.getRid()== null ? "" :this.tdFlowNode.getRid().toString());
		
		
		//当前节点的任务ID
		bean.setActivitiTaskId(this.taskId);
		//当前业务主键的json
		bean.setBusinessIdJSON(this.businessId);
		/**业务模块提供规则的json查询条件*/
		if(this.businessService instanceof ISetAssignee) {
			ISetAssignee setAssignee = (ISetAssignee)this.businessService;
			bean.setAssigneeConditionJSON(setAssignee.settingAssigneeCondition());
		}
		
		String condition = JSON.toJSONString(bean);
		
		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		// 用于TdFlowHisTaskBean获取
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(StringUtils.encode(condition));
		paramMap.put("condition", paramList);
		
		RequestContext.getCurrentInstance().openDialog("/webapp/flow/tdFlowSubmitNBTZ", options, paramMap);
	}
	
	public Map<String, List<String>> zytzInit(){
		//构建传递的条件
		FlowChoiceConditionBean bean = new FlowChoiceConditionBean();
		//当前节点对象
		bean.setCurrentFlowNodeJSON(this.tdFlowNode.getRid()== null ? "" :this.tdFlowNode.getRid().toString());
		//当前业务主键的json
		bean.setBusinessIdJSON(this.businessId);
		//当前节点的任务ID
		bean.setActivitiTaskId(this.taskId);
		
		String condition = JSON.toJSONString(bean);

		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();

		// 用于TdFlowHisTaskBean获取
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(StringUtils.encode(condition));
		paramMap.put("condition", paramList);
		return paramMap;
	}
	
	
	/**
	 * 自由跳转，打开自由跳转的弹出框
	 */
	private void zytzAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 830, null, 500);
		Map<String, List<String>> paramMap = this.zytzInit();
		RequestContext.getCurrentInstance().openDialog("/webapp/flow/tdFlowSubmitZYTZ", options, paramMap);
	}
	
	/**
	 * 自由跳转，已阅打开自由跳转的弹出框
	 */
	private void zytzReadAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 830, null, 400);
		Map<String, List<String>> paramMap = this.zytzInit();
	    List<String> list = new ArrayList<String>();
		list.add("read");
		paramMap.put("readFlag", list);
		RequestContext.getCurrentInstance().openDialog("/webapp/flow/tdFlowSubmitREAD", options, paramMap);
	}
	
	/**
	 * 选择跳转，打开选择跳转的弹出框
	 */
	private void xztzAction(TdFlowNode nextNode, String assigeeJson, SupportAssigeeType supportType) {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 830, null, 500);
		
		//构建传递的条件
		FlowChoiceConditionBean bean = new FlowChoiceConditionBean();
		//下一个节点
		bean.setNextFlowNodeJSON(nextNode.getRid()== null ? "" :nextNode.getRid().toString());
		//当前节点对象
		bean.setCurrentFlowNodeJSON(this.tdFlowNode.getRid()== null ? "" :this.tdFlowNode.getRid().toString());
		//当前节点的任务ID
		bean.setActivitiTaskId(this.taskId);
		//业务模块提供的待办人
		bean.setAssigeesJSON(assigeeJson);
		//业务模块提供的待办人与规则提供的待办人最后要形成集合的关系
		bean.setAssigeeType(supportType);
		//当前业务主键的json
		bean.setBusinessIdJSON(this.businessId);
		/**业务模块提供规则的json查询条件*/
		if(this.businessService instanceof ISetAssignee) {
			ISetAssignee setAssignee = (ISetAssignee)this.businessService;
			bean.setAssigneeConditionJSON(setAssignee.settingAssigneeCondition());
		}
		String condition = JSON.toJSONString(bean, SimplePropertyFilter.getInstance());
		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		// 用于TdFlowHisTaskBean获取
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(StringUtils.encode(condition));
		paramMap.put("condition", paramList);
		
		RequestContext.getCurrentInstance().openDialog("tdFlowSubmitXRTZ", options, paramMap);
	}
	
	/**
	 * 多节点选择跳转，打开选择跳转的弹出框
	 */
	private void djdXztzAction(List<TdFlowNode> nextNodeList, String assigeeJson, SupportAssigeeType supportType) {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 830, null, 500);
		
		//构建传递的条件
		FlowChoiceConditionBean bean = new FlowChoiceConditionBean();
		
		StringBuffer nextNodes = new StringBuffer();
		if (null != nextNodeList && nextNodeList.size() > 0) {
			for (TdFlowNode t : nextNodeList) {
				nextNodes.append(",").append(t.getRid());
			}
			// 下一步的节点
			bean.setNextFlowNodeJSON(nextNodes.substring(1));
		}
		//当前节点对象
		bean.setCurrentFlowNodeJSON(this.tdFlowNode.getRid()== null ? "" :this.tdFlowNode.getRid().toString());
		//当前节点的任务ID
		bean.setActivitiTaskId(this.taskId);
		//当前业务主键的json
		bean.setBusinessIdJSON(this.businessId);
		/**业务模块提供规则的json查询条件*/
		if(this.businessService instanceof ISetAssignee) {
			ISetAssignee setAssignee = (ISetAssignee)this.businessService;
			bean.setAssigneeConditionJSON(setAssignee.settingAssigneeCondition());
		}
		
		String condition = JSON.toJSONString(bean);
		
		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		// 用于TdFlowHisTaskBean获取
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(StringUtils.encode(condition));
		paramMap.put("condition", paramList);
		
		RequestContext.getCurrentInstance().openDialog("/webapp/flow/tdFlowSubmitDRTZ", options, paramMap);
	}	
	
	/**
	 * 此功能只是为了应急最后一个节点需要发起一个新的流程用的<br/>
	 * supportType对应SupportAssigeeType的值
	 */
	private void gdxrAction(String assigeeJson, SupportAssigeeType supportType) {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 830, null, 500);
		
		//构建传递的条件
		FlowChoiceConditionBean bean = new FlowChoiceConditionBean();
		//业务模块提供的待办人
		bean.setAssigeesJSON(assigeeJson);
		//当前节点对象
		bean.setCurrentFlowNodeJSON(this.tdFlowNode.getRid()== null ? "" :this.tdFlowNode.getRid().toString());
		//当前节点的任务ID
		bean.setActivitiTaskId(this.taskId);
		//当前业务主键的json
		bean.setBusinessIdJSON(this.businessId);
		//业务模块提供的待办人与规则提供的待办人最后要形成集合的关系
		bean.setAssigeeType(supportType);
		
		String condition = JSON.toJSONString(bean);
		
		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		// 用于TdFlowHisTaskBean获取
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(StringUtils.encode(condition));
		paramMap.put("condition", paramList);
		
		RequestContext.getCurrentInstance().openDialog("/webapp/flow/tdFlowSubmitGDXR", options, paramMap);
	}
	
	/**
	 * 选择待办人 : 选人回调 真实的提交方法
	 * 
	 * @param event
	 *            弹出框关闭事件
	 */
	public void onPersonSelect(SelectEvent event) {
		//标记操作状态默认失败
		this.actionState = 0;
		Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			// 业务可能要传一些变量
			Map<String, Object> formVariables = this.businessService.addVariables();
			if (null != formVariables) {
				selectedMap.putAll(formVariables);
			}
			
			String submitMsg = this.businessService.submitAction();
			if(StringUtils.isNotBlank(submitMsg)) {
				/**
				 * 流程意见
				 */
				String advice = (String) selectedMap.get("advice");
				selectedMap.remove("advice");
				
				/** 标识是否是已阅 */
				Object read = selectedMap.get("read");
				
				/**
				 * 通讯方式
				 */
				List<TsTxtype> selectedTxtypeList = (List<TsTxtype>) selectedMap.get("selectedTxtypeList");
				selectedMap.remove("selectedTxtypeList");
				
				/**
				 * 选择的用户
				 */
				List<TsUserInfo> selectedUserList = (List<TsUserInfo>) selectedMap.get("selectedUserList");
//				selectedMap.remove("selectedUserList");
				
				Integer loginUserId = this.sessionData.getUser().getRid();
				try {
					String pid = this.flowBusinessService.submitFlow(this.taskId, selectedMap, advice, loginUserId, this.trustId);
				} catch (ActivitiIllegalArgumentException e) {
					e.printStackTrace();
					if (read != null && StringUtils.isNoneBlank(read.toString())) {
						JsfUtil.addErrorMessage("流程不能做已阅操作，请点击提交或完成操作！");
						return ;
					}
				} catch(Exception e) {
					e.printStackTrace();
				}
				
				//发送消息
				if(null != selectedUserList && selectedUserList.size() > 0) {
					StringBuilder mans = new StringBuilder();
					for(TsUserInfo user : selectedUserList) {
						mans.append(",").append(user.getRid());
					}
					MsgSendUtil.sendNewMsg(mans.toString().replaceFirst(",", ""));
				}
				/**
				 * 根据选择的通讯方式进行发送讯息
				 */
				if(null != selectedTxtypeList && selectedTxtypeList.size() > 0) {
					for(TsTxtype tx : selectedTxtypeList) {
						if (tx.getTypeCode().equals("1006")) {
							this.sendTxMsg(tx, selectedUserList, flowContext.getBusinessKey()+"@#@"+advice);
						}else {
							this.sendTxMsg(tx, selectedUserList, advice);
						}
					}
				}
				
				this.actionState = 1;
				//提交后，将该消息置为已阅
				if(StringUtils.isNotBlank(this.msgSubId)){
					MsgSendUtil.updateMsgState(this.msgSubId, "1", loginUserId);
					//走页面JS执行返回界面方法
					this.actionState = 2;
					//刷新消息查看页面
//					RequestContext.getCurrentInstance().execute(Constants.SYS_MSG_OPEN_SCRIPT);
				}else{
					this.flowBusinessService.updateMsgState(taskId, this.sessionData.getUser().getRid());
				}
				this.flowBusinessService.updateToDoState(taskId, this.sessionData.getUser().getRid());
				MsgSendUtil.updateMsgInfo();
				MsgSendUtil.sendNewMsg(this.sessionData.getUser().getRid().toString());
				
				JsfUtil.getRequestMap().put("advice", advice);
				this.businessService.afterSubmitAction();
			}else {
				JsfUtil.addErrorMessage("提交失败！");
			}
		}
	}
	/**
	 * 退回初始化
	 */
	public void backProcessInit() {
		this.advice = null;
		Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 400);

		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(this.taskId);
		paramMap.put("taskId", paramList);

		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("/webapp/flow/backNodeDialogSelect", options, paramMap);
		
	}

	/**
	 * 退回
	 */
	public void backProcessAction(SelectEvent event) {
		this.actionState = 0;
		Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			this.advice = (String) selectedMap.get("advice");
			String backNodeId  = (String) selectedMap.get("backNodeId");
			JsfUtil.getRequestMap().put("advice", advice);
			
			/**
			 * 通讯方式
			 */
			List<TsTxtype> selectedTxtypeList = (List<TsTxtype>) selectedMap.get("selectedTxtypeList");
			selectedMap.remove("selectedTxtypeList");
			
			/**
			 * 选择的用户
			 */
			List<TsUserInfo> selectedUserList = (List<TsUserInfo>) selectedMap.get("selectedUserList");
			// selectedMap.remove("selectedUserList");
			
			Integer loginUserId = this.sessionData.getUser().getRid();
			// 根据退回节点的ID 查找任务ID
			
			this.flowBusinessService.backFlow(this.taskId, this.advice, backNodeId, loginUserId, this.trustId, selectedMap);
			this.actionState = 1;
			this.businessService.afterBackAction();
			
			//提交后，将该消息置为已阅
			if(StringUtils.isNotBlank(this.msgSubId)){
				MsgSendUtil.updateMsgState(this.msgSubId, "1", loginUserId);
				//刷新消息查看页面
//				RequestContext.getCurrentInstance().execute(Constants.SYS_MSG_OPEN_SCRIPT);
			}else{
				this.flowBusinessService.updateMsgState(taskId, this.sessionData.getUser().getRid());
			}
			this.flowBusinessService.updateToDoState(taskId, this.sessionData.getUser().getRid());
			MsgSendUtil.updateMsgInfo();
			MsgSendUtil.sendNewMsg(this.sessionData.getUser().getRid().toString());
			//发送消息
			if(null != selectedUserList && selectedUserList.size() > 0) {
				StringBuilder mans = new StringBuilder();
				for(TsUserInfo user : selectedUserList) { 
					mans.append(",").append(user.getRid());
				}
				MsgSendUtil.sendNewMsg(mans.toString().replaceFirst(",", ""));
			}
			/**
			 * 根据选择的通讯方式进行发送讯息
			 */
			if(null != selectedTxtypeList && selectedTxtypeList.size() > 0) {
				for(TsTxtype tx : selectedTxtypeList) {
					this.sendTxMsg(tx, selectedUserList, advice);
				}
			}
		}
	}
	
	/**
	 * 删除操作
	 * 当前节点是首节点并且是当前登录人发起的流程方可删除
	 */
	public void deleteAction() {
		if(null != this.businessService) {
			String msg = this.businessService.deleteAction();
			if(StringUtils.isBlank(msg)) {
				this.flowBusinessService.deleteProcessInstanceTaskId(this.taskId); 
				this.flowBusinessService.updateMsgState(taskId, this.sessionData.getUser().getRid());
				this.flowBusinessService.updateToDoState(taskId, this.sessionData.getUser().getRid());
				flag = true;
				this.backAction();
			}else {
				flag = false;
				//删除失败
				JsfUtil.addErrorMessage(msg);
			}
		}
	}
	
	/**
	 * 终止流程操作
	 */
	public void terminateAction() {
		if(null != this.businessService) {
			String msg = this.businessService.terminateAction();
			if(StringUtils.isBlank(msg)) {
				this.flowBusinessService.terminateProcess(this.taskId, this.businessService.addVariables(), null, this.sessionData.getUser().getRid(), this.trustId);
				JsfUtil.addSuccessMessage("流程终止成功！");
				flag = true;
				this.backAction();
			}else {
				//删除失败
				JsfUtil.addErrorMessage(msg);
			}
		}
	}
	
	/**
	 * 办结操作，类似于终止
	 */
	public void finishAction() {
		this.saveAction();
        if(StringUtils.isBlank(this.businessId)) {
            return;
        }
		this.flowBusinessService.finishProcess(this.taskId, this.businessService.addVariables(), null, this.sessionData.getUser().getRid(), this.trustId);
		JsfUtil.addSuccessMessage("操作成功！");
		flag=true;
		this.backAction();
	}
	
	/**
	 * 完成操作，类似于办结，与办结不同的是，只办结自己的任务
	 */
	public void finishSelfAction() {
		this.saveAction();
		if(StringUtils.isBlank(this.businessId)) {
			return;
		}
		Map<String, Object> options = MapUtils.produceDialogMap(null, 830, null, 500);
		//构建传递的条件
		FlowChoiceConditionBean bean = new FlowChoiceConditionBean();
		//当前节点对象
		bean.setCurrentFlowNodeJSON(this.tdFlowNode.getRid()== null ? "" :this.tdFlowNode.getRid().toString());
		//当前业务主键的json
		bean.setBusinessIdJSON(this.businessId);
		//当前节点的任务ID
		bean.setActivitiTaskId(this.taskId);
		
		String condition = JSON.toJSONString(bean);

		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();

		// 用于TdFlowHisTaskBean获取
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(StringUtils.encode(condition));
		paramMap.put("condition", paramList);
		flag = true;
		RequestContext.getCurrentInstance().openDialog("/webapp/flow/tdFlowSubmitFinish", options, paramMap);
	}
	
	/**
	 * 完成的回调操作
	 * 
	 * @param event 弹出框关闭事件
	 */
	public void onFinishSelf(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
		/**
		 * 流程意见
		 */
		if(null != selectedMap && selectedMap.size() > 0) {
			String advice = (String) selectedMap.get("advice");
			
			this.flowBusinessService.finishSelfProcess(this.taskId, this.businessService.addVariables(), advice, this.sessionData.getUser().getRid(), this.trustId);
			JsfUtil.addSuccessMessage("操作成功！");
			this.backAction();
		}
	}
	
	
	

	/**
	 * 显示流程图
	 */
	public void processPicAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 900, null, 600);

		// 用于TdFlowHisTaskBean获取
		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(this.taskId);
		paramMap.put("taskId", paramList);

		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("/webapp/flow/tdFlowPicList", options, paramMap);
	}
	
	/**
	 * 返回待办任务列表
	 * @return 地址
	 */
	public void backAction() {
		try {
			if(StringUtils.isNotBlank(this.fromUrl)) {
				if (flag) {
					fromUrl+="&param1=1";
				}
				FacesContext.getCurrentInstance().getExternalContext().redirect(this.fromUrl);
			}else {
				FacesContext.getCurrentInstance().getExternalContext().redirect(DEFAULT_URL);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 根据通讯方式，发送相关的信息
	 */
	private void sendTxMsg(TsTxtype tx, List<TsUserInfo> userList, String content) {
		TxType txType = tx.getTxType();
		if(null != userList && userList.size() > 0) {
			StringBuilder sb = new StringBuilder();
			for(TsUserInfo t : userList) {
				sb.append(",").append(t.getRid());
			}
			
			switch(txType) {
	    	case YY:
				this.sendVoiceMsg(tx, sb.toString().replaceFirst(",", ""), content);
	    		break;
	    	case DX:
	    		this.sendVoiceMsg(tx, sb.toString().replaceFirst(",", ""), content);
	    		break;
	    	case APP:
	    		this.sendPushMsg(sb.toString().replaceFirst(",", ""), content);
	    		break;
			case EMAIL:
	    		this.sendEmailMsg(tx,sb.toString().replaceFirst(",", ""), content);
	    		break;
			}
			//switch end	
		}
	}
	public void sendEmailMsg(TsTxtype tx,String userIds, String content) {
		Map<Integer, String> emailMap = commService.findEmailByEmail(userIds);
		if(null == emailMap){
			emailMap = new HashMap<Integer, String>();
		}
		if(null != emailMap && emailMap.size() > 0) {
			Set<Entry<Integer, String>> entrySet = emailMap.entrySet();
			IMsgSend send = SpringContextHolder.getBean(tx.getImplClass());
			if(null != send) {
				for(Entry<Integer, String> entry : entrySet) {
					String akStr = null;
					try {
						//发送邮件
						akStr = send.sendMsg(entry.getValue(), content);
					} catch (Exception e) {
						e.printStackTrace();
						akStr = null;
					}
				}
			}
		}
	}
	/** 极光推送 */
	public void sendPushMsg(String userIds, String content) {
		List<TsUserInfo> userList = this.commService.findUserByUserIds(userIds);
		Map<String, String> paramMap = new HashMap<>();
		paramMap.put("taskId", this.taskId);
		paramMap.put("type", "2");
		HistoricTaskInstance findHisTaskById = activitiServiceImpl.findHisTaskById(this.taskId);
		if( null != findHisTaskById )	{
			String processInstanceId = findHisTaskById.getProcessInstanceId();
			paramMap.put("flowId", processInstanceId);
		}
		if (userList != null && userList.size() > 0) {
			for (TsUserInfo user : userList) {
				new PushMsgToAppImpl(content, paramMap, user.getUserNo()).sendJPush();
			}
		}
	}
	
	/**
	 * 发送语音短信
	 * @param userIds 用户IDS
	 */
	private void sendVoiceMsg(TsTxtype tx, String userIds, String content ) {
		/**
		 * 电话号码以业务模块提供的为准，没有就职工表里的电话
		 */
		Map<Integer, String> phoneMap = this.commService.findPhoneNums(userIds);
		if(null == phoneMap){
			phoneMap = new HashMap<Integer, String>();
		}
		
		Map<Integer, String> phoneMap2 = this.businessService.supplyPhoneNum(userIds);
		if(null != phoneMap2 && phoneMap2.size() > 0) {
			phoneMap.putAll(phoneMap2);
		}
		
		Map<Integer, String> sendRstMap = new HashMap<Integer, String>();
		if(null != phoneMap && phoneMap.size() > 0) {
			Set<Entry<Integer, String>> entrySet = phoneMap.entrySet();
			/**
			 * 初始化语音发送方式
			 */
			IMsgSend send = SpringContextHolder.getBean(tx.getImplClass());
			if(null != send) {
				for(Entry<Integer, String> entry : entrySet) {
					String akStr = null;
					try {
						//发送语音
						akStr = send.sendMsg(entry.getValue(), content);
					} catch (Exception e) {
						e.printStackTrace();
						akStr = null;
					}
					sendRstMap.put(entry.getKey(), akStr);
				}
			}
			
			//保存语音发送状态记录
			this.flowBusinessService.saveTxRpt(this.taskId, sendRstMap, content, tx);
		}
	}
	
	/**
	 * 根据节点的配置，初始化按钮的显示状态
	 */
	private void initBtnMap(TdFlowNode node) {
		this.printable = false;
		this.btnDispMap = new HashMap<String, TdFlowNodeBtn>();
		
		for(FlowBtnType fbt : FlowBtnType.values()) {
			this.btnDispMap.put(fbt.getTypeNo(), new TdFlowNodeBtn());
		}
		
		if(null != node.getTdFlowNodeBtnList() && node.getTdFlowNodeBtnList().size() > 0) {
			for(TdFlowNodeBtn nodeBtn : node.getTdFlowNodeBtnList()) {
				if(nodeBtn.getFlowBtnType().equals(FlowBtnType.PREVIEW) || nodeBtn.getFlowBtnType().equals(FlowBtnType.PRINT)
						|| nodeBtn.getFlowBtnType().equals(FlowBtnType.DESIGN) || nodeBtn.getFlowBtnType().equals(FlowBtnType.PRINTER)) {
					this.printable = true;
				}
				nodeBtn.setDisp(true);
				this.btnDispMap.put(nodeBtn.getFlowBtnType().getTypeNo(), nodeBtn);
			}
		}
	}
	
	@Override
	public void setBusinessService(IBusinessService businessService) {
		this.businessService = businessService;
	}
	
	@Override
	public IBusinessService getBusinessService() {
		return this.businessService;
	}
	
	public Boolean getFlag() {
		return flag;
	}

	public void setFlag(Boolean flag) {
		this.flag = flag;
	}

	@Override
	public void setAssigneeService(ISetAssignee assigneeService) {
		this.assigneeService = assigneeService;
	}
	
	@Override
	public Object getFlowContext() {
		return this.flowContext;
	}
	
	/**
	 * 报表设计按钮
	 */
	public void designAction() {
		if(null != this.businessService && this.businessService instanceof IFastReport) {
			IFastReport fast = (IFastReport)this.businessService;
			fast.getFastReportBean().designAction();
		}else {
			JsfUtil.addErrorMessage("此模块不支持该功能！");
		}
	}
	
	/**
	 * 打印按钮
	 */
	public void printAction() {
		if(null != this.businessService && this.businessService instanceof IFastReport) {
			IFastReport fast = (IFastReport)this.businessService;
			fast.getFastReportBean().printReportAction();
		}else {
			JsfUtil.addErrorMessage("此模块不支持该功能！");
		}
	}
	
	/**
	 * 预览
	 */
	public void preViewAction() {
		if(null != this.businessService && this.businessService instanceof IFastReport) {
			IFastReport fast = (IFastReport)this.businessService;
			fast.getFastReportBean().showReportAction();
		}else {
			JsfUtil.addErrorMessage("此模块不支持该功能！");
		}
	}
	
	/**
	 * 授权初始化
	 */
	public void ffInitAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(920, 870, 500, null);

        Map<String, List<String>> paramsMap = new HashMap<String, List<String>>();
        /**
         * 传入title
         */
        List<String> paramList = new ArrayList<String>(1);
        paramList.add("请选择人员");
        paramsMap.put("title", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/selectUsersList", options, paramsMap);
	}
	
    /**
     * 分发确定
     * @param event 弹出框关闭事件
     */
    public void onFfSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
        	Collection<TsUserInfo> coll = (Collection<TsUserInfo>) selectedMap.get("selectUserList");
        	
        	if(null != coll && coll.size() > 0) {
        		StringBuilder sb = new StringBuilder();
        		for(TsUserInfo t: coll) {
        			sb.append(",").append(t.getRid());
        		}
        		this.flowBusinessService.ffMsgInProcess(this.taskId, sb.toString().replaceFirst(",", ""), this.sessionData.getUser().getRid(),
        				this.businessId);
        		JsfUtil.addSuccessMessage("分发成功！");
        	}
        }
    }

	public boolean isReadOnly() {
		return readOnly;
	}

	public void setReadOnly(boolean readOnly) {
		this.readOnly = readOnly;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String getNodeId() {
		return nodeId;
	}

	public void setNodeId(String nodeId) {
		this.nodeId = nodeId;
	}

	public TdFlowNode getTdFlowNode() {
		return tdFlowNode;
	}

	public void setTdFlowNode(TdFlowNode tdFlowNode) {
		this.tdFlowNode = tdFlowNode;
	}

	public String getBusinessId() {
		return businessId;
	}

	public void setBusinessId(String businessId) {
		this.businessId = businessId;
	}

	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public String getAdvice() {
		return advice;
	}

	public void setAdvice(String advice) {
		this.advice = advice;
	}

	public int getActionState() {
		return actionState;
	}

	public void setActionState(int actionState) {
		this.actionState = actionState;
	}

	public Map<String, TdFlowNodeBtn> getBtnDispMap() {
		return btnDispMap;
	}

	public void setBtnDispMap(Map<String, TdFlowNodeBtn> btnDispMap) {
		this.btnDispMap = btnDispMap;
	}

	@Override
	public List<BusinessButton> getButtonList() {
		return this.buttonList;
	}

	@Override
	public void setButtonList(List<BusinessButton> buttonList) {
		this.buttonList = buttonList;
	}

	public boolean isPrintable() {
		return printable;
	}

	public void setPrintable(boolean printable) {
		this.printable = printable;
	}

	public List<Object[]> getLastNodeAdvice() {
		return lastNodeAdvice;
	}

	public void setLastNodeAdvice(List<Object[]> lastNodeAdvice) {
		this.lastNodeAdvice = lastNodeAdvice;
	}

	public boolean isFf() {
		return ff;
	}

	public void setFf(boolean ff) {
		this.ff = ff;
	}

	public String getBusinessKey() {
		return businessKey;
	}

	public void setBusinessKey(String businessKey) {
		this.businessKey = businessKey;
	}

	public Boolean getDispPro() {
		return dispPro;
	}

	public void setDispPro(Boolean dispPro) {
		this.dispPro = dispPro;
	}

	public List<Object[]> getAdvDataList() {
		return advDataList;
	}

	public void setAdvDataList(List<Object[]> advDataList) {
		this.advDataList = advDataList;
	}

	public TdFlowDef getDef() {
		return def;
	}

	public void setDef(TdFlowDef def) {
		this.def = def;
	}

	public boolean isFlowInfoShow() {
		return flowInfoShow;
	}

	public void setFlowInfoShow(boolean flowInfoShow) {
		this.flowInfoShow = flowInfoShow;
	}
	
	
}
