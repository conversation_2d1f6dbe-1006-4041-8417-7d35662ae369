package com.chis.modules.flow.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;

import com.chis.activiti.entity.TdFlowNodePage;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesBean;

/**
 * 页面选择
 * <AUTHOR>
 */
@ManagedBean(name = "pageSelectBean")
@ViewScoped
public class PageSelectBean extends FacesBean {
    private static final long serialVersionUID = -1239036568323798376L;
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    /*系统选项*/
    private String selectItmSort;
    /*可选系统*/
    private Map<String,String> itmSortMap = new LinkedHashMap<String, String>();
    /*页面数据*/
    private List<TdFlowNodePage> srclist = new ArrayList<TdFlowNodePage>();
    /*显示数据*/
    private List<TdFlowNodePage> displayList;
    /*选择的页面*/
    private TdFlowNodePage tdFlowNodePage;

    public PageSelectBean(){
        List<TdFlowNodePage> templist = flowBusinessService.findAllFlowNodePage();
        String projects = commService.findParamValue("SYSTEM_MODULES");
        if(templist != null && templist.size() > 0 && projects != null && projects.length() > 0){
            String[] prj = projects.split("##");
            for(String s : prj){
                SystemType e = (SystemType) EnumUtils.findEnum(SystemType.class,s);
                itmSortMap.put(e.getTypeCN(), s);
            }
            for(TdFlowNodePage t : templist){
                if(itmSortMap.containsValue(t.getSystemType().getTypeNo().toString())){
                    srclist.add(t);
                }
            }
        }
        searchAction();
    }

    public void searchAction(){
        this.displayList = new ArrayList<TdFlowNodePage>();
        if(srclist != null && srclist.size() > 0){
            if(StringUtils.isNotBlank(selectItmSort)){
                for(TdFlowNodePage t : srclist){
                    if(t.getSystemType().getTypeNo().toString().equals(selectItmSort)){
                        displayList.add(t);
                    }
                }
            }else{
                displayList.addAll(srclist);
            }
        }
    }

    public void selectAction(){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("tdFlowNodePage",tdFlowNodePage);
        RequestContext.getCurrentInstance().closeDialog(map);
    }

    public TdFlowNodePage getTdFlowNodePage() {
        return tdFlowNodePage;
    }

    public void setTdFlowNodePage(TdFlowNodePage tdFlowNodePage) {
        this.tdFlowNodePage = tdFlowNodePage;
    }

    public List<TdFlowNodePage> getDisplayList() {
        return displayList;
    }

    public void setDisplayList(List<TdFlowNodePage> displayList) {
        this.displayList = displayList;
    }

    public Map<String, String> getItmSortMap() {
        return itmSortMap;
    }

    public void setItmSortMap(Map<String, String> itmSortMap) {
        this.itmSortMap = itmSortMap;
    }

    public String getSelectItmSort() {
        return selectItmSort;
    }

    public void setSelectItmSort(String selectItmSort) {
        this.selectItmSort = selectItmSort;
    }
}
