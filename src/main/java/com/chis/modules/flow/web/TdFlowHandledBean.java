package com.chis.modules.flow.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.DualListModel;
import org.primefaces.model.TreeNode;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.entity.TdFlowType;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MapUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.flow.logic.TdFlowHandledCondBean;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.MsgSendUtil;
import com.chis.modules.system.web.FacesSimpleBean;
import com.google.common.collect.Lists;

/**
 * 已办任务
 * 
 * <AUTHOR>
 */
@ManagedBean(name = "tdFlowHandledBean")
@ViewScoped
public class TdFlowHandledBean extends FacesSimpleBean {

	private static final long serialVersionUID = -738445454536625063L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	/** 查询条件：流程类型树 */
	private TreeNode searchTreeNode;
	/** 查询条件Bean */
	private TdFlowHandledCondBean condition;
	/** 流程实例ID */
	private String processId;
	private String businesskey;
	/** 流程实例ID */
	private String proDefId;
	/** 流程节点名称 */
	private String taskNodeName;
	
	/** 流程任务名称 */
	private String taskName;
	/*** 当前登录人的ID */
	private Integer curUserId;
	/**是否有撤销权限**/
	private Boolean ifCanCx = false;
	/**是否有撤销权限**/
	private Boolean ifCanJjq = false;
	/**某流程实例存活的所有节点的待办人*/
	private List<Object[]> actNodeUserList = Lists.newArrayList();
	/**修改数据的行数*/
	private Integer editRow;
	/**单位ID*/
	private Integer searchUnitId;
	/**单位下拉*/
	private Map<String, Integer> searchUnitMap;
	/**科室ID*/
	private Integer searchOfficeId;
	/**科室下拉*/
	private Map<String, Integer> searchOfficeMap = new LinkedHashMap<String, Integer>();
    /**用户的选择*/
    private DualListModel dualListModel = new DualListModel();
    /**可以选择的用户*/
    private List<TsUserInfo> allUserList = Lists.newArrayList();
    /** 业务表单ID */
	private String businessId;
	/**当前节点id*/
	private String noteId;
	/**任务Id*/
	private String taskId;
	public TdFlowHandledBean() {
		super.ifSQL = Boolean.TRUE;
		Set<String> s=sessionData.getBtnSet();
		if(s!=null && s.size() > 0) {
			if(s.contains("oa_ybrw_cxBtn")){
				this.ifCanCx=true;
			}
			if(s.contains("oa_ybrw_jjqBtn")){
				this.ifCanJjq=true;
			}
		}
		this.init();
		this.searchAction();
	}

	public void init() {
		this.initSearchCondition();
		/**
		 * 非空的话是从详情界面转向过来的
		 */
		String con = JsfUtil.getRequest().getParameter("param");
		if (StringUtils.isNotBlank(con)) {
			this.condition = JSON.parseObject(con, TdFlowHandledCondBean.class);
		} else {
			this.condition = new TdFlowHandledCondBean();
		}
	}

	/**
	 * 初始化查询条件
	 */
	private void initSearchCondition() {
		this.curUserId = this.sessionData.getUser().getRid();
		if (null == this.searchTreeNode) {
			this.searchTreeNode = new DefaultTreeNode("root", null);
			List<TdFlowType> list = this.flowBusinessService.findFlowTypeList();
			if (null != list && list.size() > 0) {
				Map<Integer, TreeNode> map = new HashMap<Integer, TreeNode>();
				for (TdFlowType t : list) {
					if (null == t.getParent()) {
						TreeNode node = new DefaultTreeNode(t, this.searchTreeNode);
						map.put(t.getRid(), node);
					} else {
						TreeNode node = new DefaultTreeNode(t, map.get(t.getParent().getRid()));
						map.put(t.getRid(), node);
					}
				}
				map.clear();
			}
		}
	}

	/**
	 * 流程类型选中事件
	 * 
	 * @param event
	 *            选中事件
	 */
	public void onSearchNodeSelect(NodeSelectEvent event) {
		TreeNode node = event.getTreeNode();
		if (null != node) {
			TdFlowType type = (TdFlowType) node.getData();
			this.condition.setSearchFlowTypeId(type.getRid());
			this.condition.setSearchFlowTypeName(type.getTypeName());
		}
	}

	/**
	 * 催办
	 */
	public void saveAction() {
		String userIds = this.flowBusinessService.cbFlow(this.processId, this.taskNodeName, this.taskName, this.curUserId);
		if (StringUtils.isNotBlank(userIds)) {
			MsgSendUtil.sendNewMsg(userIds);
		}
		JsfUtil.addSuccessMessage("催办成功！");
	}
	
	/**
	 * 转向到处理界面
	 */
	public void modInitAction() {
		try {
			StringBuilder sb = new StringBuilder("/webapp/flow/tdFlowDetail.faces?ph=1");
			if (StringUtils.isNotBlank(this.processId)) {
				sb.append("&processId=").append(this.processId);
			}
			if (StringUtils.isNotBlank(this.businesskey)) {
				sb.append("&businesskey=").append(this.businesskey);
			}
			if(StringUtils.isNotBlank(this.businessId)){
				sb.append("&businessId=").append(this.businessId);
			}
			if(StringUtils.isNotBlank(this.taskId)){
				sb.append("&taskId=").append(this.taskId);
			}
			if(StringUtils.isNotBlank(this.noteId)){
				sb.append("&noteId=").append(this.noteId);
			}
			if (null != this.condition) {
				sb.append("&from=/webapp/flow/tdFlowHandledTaskList.faces?param=").append(JSON.toJSONString(this.condition));
			}
			FacesContext.getCurrentInstance().getExternalContext().redirect(sb.toString());
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}
	
	/**
	 * <p>撤销操作</p>
	 * 1. 流程结束 <br/>
     * 1.1 结束节点的前一个节点的人可以撤销。<br/>
     * 1.2 否则不可以撤销 <br/>
	 * 2. 流程未结束 <br/>
	 * 2.1 当前节点是普通节点 <br/>
	 * 2.1.1 下一个节点是普通节点 <br/>
	 * 	2.1.1.1 下一个节点的任务还未处理？可撤销：不可撤销 <br/>
	 * 2.1.2 下一个节点是Gateway <br/>
	 * 	2.1.2.1 判断Gateway之前的节点是否有未处理的？可撤销：(判断Gateway之后的第一个节点是否都未处理的？可撤销：不可撤销) <br/>
	 * 2.1.3 下一个节点是会签节点 <br/>
	 *  2.1.3.1 该会签节点对应的任务是否都未处理？可撤销：不可撤销 <br/>
	 * 2.2 当前节点是会签节点 <br/>
	 *  2.2.1 当前节点是否还有未处理的任务？可撤销：（按照2.1.1，2.1.2，2.1.3检查一遍） <br/>
	 *  
	 *  由于有众多判断无法实现，暂取消该操作
	 */
	@Deprecated
	public void cancelAction() {
		try {
			String msg = this.flowBusinessService.cancelProcess(this.processId, this.curUserId, null);
			if(StringUtils.isNotBlank(msg)) {
				JsfUtil.addErrorMessage(msg);
			}else {
				JsfUtil.addSuccessMessage("撤销成功！");
				this.searchAction();
				RequestContext.getCurrentInstance().update("mainForm:dataTable");
			}
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("您不可以撤销该流程！");
		}
	}
	
	/**
	 * 追回操作
	 */
	public void replevyAction() {
		try {
			String msg = this.flowBusinessService.replevyProcess(this.processId, this.curUserId, null);
			if(StringUtils.isNotBlank(msg)) {
				JsfUtil.addErrorMessage(msg);
			}else {
				JsfUtil.addSuccessMessage("撤销成功！");
				this.searchAction();
				RequestContext.getCurrentInstance().update("mainForm:dataTable");
				
				
			}
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("您不可以撤销该流程！");
		}
	}
	
	/**
	 * 加减签初始化
	 */
	public void jjqInitAction() {
		this.actNodeUserList = this.flowBusinessService.findActNodeUsers(this.processId);
	}
	
	/**
	 * 针对某个节点的加减签初始化
	 */
	public void jjqInitAction2() {
		/**
		 * 0-TASK_DEF_KEY_, 
		 * 1-NAME_, 
		 * 2-WM_CONCAT(Q1.USERNAME),  
		 * 3-WM_CONCAT(Q1.USER_ID)
		 */
		Object[] o = this.actNodeUserList.get(this.editRow);
		TdFlowNode node = this.flowBusinessService.findFlowNode(this.proDefId, o[0].toString());
		
		/**
		 * 设置查询条件，
		 * 流程实例ID通过任务ID去找，selectIds通过nodeId去找
		 */
		ResultCondition condition = new ResultCondition();
		condition.setNodeId(node.getRid().toString());
		condition.setUserId(this.sessionData.getUser().getRid());
		condition.setProcessInstanceId(this.processId);
		
		//nodeId节点所有可选的待办人
		this.allUserList = this.flowBusinessService.findUsersByNodeId(condition);
		
		List<TsUserInfo> sourceList = Lists.newArrayList();
		List<TsUserInfo> targetList = Lists.newArrayList();
		
		if(null == this.allUserList) {
			this.allUserList = new ArrayList<TsUserInfo>();
		}else {
			String hasChoosenIds = ","+o[3]+",";
			for(TsUserInfo t : this.allUserList) {
				if(StringUtils.contains(hasChoosenIds, ","+t.getRid()+",")) {
					targetList.add(t);
				}else {
					sourceList.add(t);
				}
			}
		}
		this.dualListModel = new DualListModel(sourceList,targetList);
		
		/**
		 * 根据allUserList初始化查询条件
		 */
		this.initUnitList();
	}
	
	/**
	 * 加减签
	 */
	public void jjqAction() {
		try {
			Map<String, List<String>> map = this.flowBusinessService.counterSignProcess(this.processId, this.actNodeUserList);
			String manIds = this.flowBusinessService.sendMsgAfterCounterSign(this.processId, map, this.sessionData.getUser().getRid());
			MsgSendUtil.sendNewMsg(manIds);
			this.searchAction();
			JsfUtil.addSuccessMessage("操作成功！");
			RequestContext.getCurrentInstance().update("mainForm:dataTable");
			RequestContext.getCurrentInstance().execute("PF('UserDialog').hide()");
			
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("操作失败，请联系管理员！");
		}
	}
	
	/**
	 * 针对某个节点的加减签
	 */
	public void jjqAction2() {
		List<TsUserInfo> targetList = this.dualListModel.getTarget();
		if(null != targetList && targetList.size() > 0) {
			Object[] o = this.actNodeUserList.get(this.editRow);
			String names = "";
			String ids = "";
			for(TsUserInfo t : targetList) {
				names = ","+t.getUsername() + names;
				ids = ","+t.getRid() + ids;
			}
			o[2] = names.replaceFirst(",", "");
			o[3] = ids.replaceFirst(",", "");
			
			RequestContext requestContext = RequestContext.getCurrentInstance();
			requestContext.update("mainForm:jjqUserTable");
			requestContext.execute("PF('UserChooseDialog').hide();");
		}else {
			JsfUtil.addErrorMessage("至少选择一名待办人！");
			return;
		}
	}
	
	/**
	 * 单位选择事件，需要刷新科室下拉和人员列表
	 */
	public void unitChangeAction() {
		this.searchOfficeId = null;
		this.searchOfficeMap.clear();
		if(null != this.searchUnitId) {
			//过滤出科室Id
			StringBuilder officeIds = new StringBuilder();
			if(null != this.allUserList && this.allUserList.size() > 0) {
				for(TsUserInfo user : this.allUserList) {
					if(this.searchUnitId.equals(user.getTsUnit().getRid())) {
						String offIds = user.getOfficeIds();
						officeIds.append(",").append(offIds.substring(1, offIds.length()-1));
					}
				}
			}
			if(officeIds.length() > 1)	{
				List<TsOffice> findOfficesByIds = commService.findOfficesByIds(officeIds.substring(1));
				if( null != findOfficesByIds && findOfficesByIds.size() > 0)	{
					for(TsOffice tsOffice :findOfficesByIds)	{
						this.searchOfficeMap.put(tsOffice.getOfficename(), tsOffice.getRid());
					}
				}
			}
		}
	}
	
	/**
	 * 科室选择事件，需要刷新人员列表
	 */
	public void officeChangeAction() {
		List<TsUserInfo> targetList = this.dualListModel.getTarget();
		//已选的人
		Map<Integer, TsUserInfo> targetMap = new HashMap<Integer, TsUserInfo>();
		if(null != targetList && targetList.size() > 0) {
			for(TsUserInfo t: targetList) {
				targetMap.put(t.getRid(), t);
			}
		}
		
		List<TsUserInfo> sourceList = new ArrayList<TsUserInfo>();
		if(null != this.allUserList && this.allUserList.size() > 0) {
			if(null != this.searchOfficeId) {
				for (TsUserInfo t : this.allUserList) {
					if(t.getOfficeIds().indexOf(","+this.searchOfficeId+",") != -1 && null == targetMap.get(t.getRid())) {
						sourceList.add(t);
					}
				}
			}else {
				for (TsUserInfo t : this.allUserList) {
					if(null == targetMap.get(t.getRid())) {
						sourceList.add(t);
					}
				}
			}
		}
		//初始化用户
		this.dualListModel.setSource(sourceList);
		this.dualListModel.setTarget(targetList);
	}
	
	/**
	 * 根据待办人员初始化单位
	 */
	private void initUnitList() {
		this.searchUnitMap = new LinkedHashMap<String, Integer>();
		if(null != this.allUserList && this.allUserList.size() > 0) {
			for(TsUserInfo user : this.allUserList) {
				this.searchUnitMap.put(user.getTsUnit().getUnitSimpname(), user.getTsUnit().getRid());
			}
		}
	}

	/**
	 * 显示流程图
	 */
	public void processPicAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 900, null, 600);

		// 用于TdFlowHisTaskBean获取
		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(this.processId);
		paramMap.put("processId", paramList);
		
		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("tdFlowPicList", options, paramMap);
	}

	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder();
		sb.append(" WITH M AS ( ");	
		sb.append(" SELECT A2.ID_ AS INSTANCEID, T2.TYPE_NAME, A2.BUSINESS_KEY_, A4.TEXT_ AS businessId,T7.RID,");
		sb.append(" A2.START_USER_ID_, T3.USERNAME, NVL2(A2.END_TIME_, 1, 0) AS FLOWSTATE,A2.PROC_DEF_ID_,A1.End_Time_ ,A1.ID_ ");
		sb.append(" FROM ACT_HI_TASKINST A1 ");
		sb.append(" INNER JOIN ACT_HI_PROCINST A2 ON A1.PROC_INST_ID_ = A2.ID_ ");
		sb.append(" INNER JOIN TD_FLOW_DEF T1 ON A2.PROC_DEF_ID_ = T1.ACT_DEF_ID ");
		sb.append(" INNER JOIN TD_FLOW_TYPE T2 ON T1.TPYE_ID = T2.RID ");
		sb.append(" INNER JOIN TD_FLOW_NODE T7 ON T7.DEF_ID = T1.RID AND T7.ACT_NODE_ID =A1.TASK_DEF_KEY_");
		sb.append(" INNER JOIN TS_USER_INFO T3 ON A2.START_USER_ID_ = T3.RID ");
//		sb.append(" INNER JOIN ACT_HI_VARINST T8 ON A1.PROC_INST_ID_ = T8.PROC_INST_ID_ AND T8.EXECUTION_ID_ =A1.EXECUTION_ID_ ");
		sb.append(" LEFT JOIN ACT_RU_VARIABLE A4 ON A4.PROC_INST_ID_ =A2.ID_ AND A4.NAME_ = 'businessId' ");
		sb.append(" WHERE A2.DELETE_REASON_ IS NULL AND A1.END_TIME_ IS NOT NULL AND A1.ASSIGNEE_ = '").append(this.curUserId).append("' ");
		if (null != this.condition.getSearchFlowTypeId()) {
			sb.append(" AND T2.RID IN(SELECT Z.RID FROM TD_FLOW_TYPE Z START WITH RID='").append(this.condition.getSearchFlowTypeId()).append("' CONNECT BY PRIOR RID = PARENT_ID) ");
		}
		if (StringUtils.isNotBlank(this.condition.getSearchTaskName())) {
			sb.append(" AND A2.BUSINESS_KEY_ LIKE :searchTaskName ");
			this.paramMap.put("searchTaskName", "%" + this.condition.getSearchTaskName().trim() + "%");
		}
		if (this.condition.getSearchFlowState() == 0) {
			sb.append(" AND A2.END_TIME_ IS NULL ");
		} else if (this.condition.getSearchFlowState() == 1) {
			sb.append(" AND A2.END_TIME_ IS NOT NULL ");
		}
		if (this.condition.isSearchMyFlow()) {
			sb.append(" AND A2.START_USER_ID_ = '").append(this.curUserId).append("' ");
		}
		sb.append(" UNION ");
		sb.append(" SELECT A2.ID_ AS INSTANCEID, T2.TYPE_NAME, A2.BUSINESS_KEY_, A4.TEXT_ AS businessId,T7.RID, ");
		sb.append(" A2.START_USER_ID_, T3.USERNAME, NVL2(A2.END_TIME_, 1, 0) AS FLOWSTATE,A2.PROC_DEF_ID_,A1.End_Time_ ,A1.ID_ ");
		sb.append(" FROM ACT_HI_TASKINST A1 ");
		sb.append(" INNER JOIN ACT_HI_PROCINST A2 ON A1.PROC_INST_ID_ = A2.ID_ ");
		sb.append(" INNER JOIN TD_FLOW_DEF T1 ON A2.PROC_DEF_ID_ = T1.ACT_DEF_ID ");
		sb.append(" INNER JOIN TD_FLOW_NODE T7 ON T7.DEF_ID = T1.RID AND T7.ACT_NODE_ID =A1.TASK_DEF_KEY_");
		sb.append(" INNER JOIN TD_FLOW_TYPE T2 ON T1.TPYE_ID = T2.RID ");
//		sb.append(" INNER JOIN ACT_HI_VARINST T8 ON A1.PROC_INST_ID_ = T8.PROC_INST_ID_ AND T8.EXECUTION_ID_ =A1.EXECUTION_ID_ ");
		sb.append(" INNER JOIN TS_USER_INFO T3 ON A2.START_USER_ID_ = T3.RID ");
		sb.append(" INNER JOIN ACT_HI_IDENTITYLINK A3 ON A3.TASK_ID_ = A1.ID_ ");
		sb.append(" LEFT JOIN ACT_RU_VARIABLE A4 ON A4.PROC_INST_ID_ = A2.ID_ AND A4.NAME_ = 'businessId' ");
		sb.append(" WHERE A2.DELETE_REASON_ IS NULL AND A1.END_TIME_ IS NOT NULL AND A3.USER_ID_ = '").append(this.curUserId).append("' ");
		if (null != this.condition.getSearchFlowTypeId()) {
			sb.append(" AND T2.RID IN(SELECT Z.RID FROM TD_FLOW_TYPE Z START WITH RID='").append(this.condition.getSearchFlowTypeId()).append("' CONNECT BY PRIOR RID = PARENT_ID) ");
		}
		if (StringUtils.isNotBlank(this.condition.getSearchTaskName())) {
			sb.append(" AND A2.BUSINESS_KEY_ LIKE :searchTaskName ");
			this.paramMap.put("searchTaskName", "%" + this.condition.getSearchTaskName().trim() + "%");
		}
		if (this.condition.getSearchFlowState() == 0) {
			sb.append(" AND A2.END_TIME_ IS NULL ");
		} else if (this.condition.getSearchFlowState() == 1) {
			sb.append(" AND A2.END_TIME_ IS NOT NULL ");
		}
		if (this.condition.isSearchMyFlow()) {
			sb.append(" AND A2.START_USER_ID_ = '").append(this.curUserId).append("' ");
		}
		sb.append(" ) ");
		sb.append(" SELECT M.INSTANCEID, M.TYPE_NAME, M.BUSINESS_KEY_,M.START_USER_ID_,M.USERNAME,M.FLOWSTATE, ");
		sb.append(" wm_concat(distinct to_char(M2.NAME_)) NODENAME, M.PROC_DEF_ID_, max(M.End_Time_)End_Time_ , M.BUSINESSID,MAX(M.ID_)");
		sb.append(" FROM M ");
		sb.append(" LEFT JOIN ACT_RU_TASK M2 ON M2.PROC_INST_ID_ = M.INSTANCEID ");
		
		sb.append(" GROUP BY M.INSTANCEID, M.TYPE_NAME, M.BUSINESS_KEY_,M.START_USER_ID_,M.USERNAME,M.FLOWSTATE,M.PROC_DEF_ID_,M.businessId,to_char(M.End_Time_,'yyyy-MM-dd') ");
		sb.append(" ORDER BY End_Time_ DESC,M.TYPE_NAME, M.BUSINESS_KEY_ DESC ");

		String h1 = sb.toString();
		String h2 = "SELECT COUNT(*) FROM (" + sb.toString() + ") ";
		return new String[] { h1, h2 };
	}

	public TreeNode getSearchTreeNode() {
		return searchTreeNode;
	}

	public void setSearchTreeNode(TreeNode searchTreeNode) {
		this.searchTreeNode = searchTreeNode;
	}

	public TdFlowHandledCondBean getCondition() {
		return condition;
	}

	public void setCondition(TdFlowHandledCondBean condition) {
		this.condition = condition;
	}

	public String getProcessId() {
		return processId;
	}

	public void setProcessId(String processId) {
		this.processId = processId;
	}

	public Integer getCurUserId() {
		return curUserId;
	}

	public void setCurUserId(Integer curUserId) {
		this.curUserId = curUserId;
	}

	public String getTaskNodeName() {
		return taskNodeName;
	}

	public void setTaskNodeName(String taskNodeName) {
		this.taskNodeName = taskNodeName;
	}

	public String getTaskName() {
		return taskName;
	}

	public void setTaskName(String taskName) {
		this.taskName = taskName;
	}

	public Boolean getIfCanCx() {
		return ifCanCx;
	}

	public void setIfCanCx(Boolean ifCanCx) {
		this.ifCanCx = ifCanCx;
	}

	public Boolean getIfCanJjq() {
		return ifCanJjq;
	}

	public void setIfCanJjq(Boolean ifCanJjq) {
		this.ifCanJjq = ifCanJjq;
	}

	public List<Object[]> getActNodeUserList() {
		return actNodeUserList;
	}

	public void setActNodeUserList(List<Object[]> actNodeUserList) {
		this.actNodeUserList = actNodeUserList;
	}

	public Integer getSearchUnitId() {
		return searchUnitId;
	}

	public void setSearchUnitId(Integer searchUnitId) {
		this.searchUnitId = searchUnitId;
	}

	public Map<String, Integer> getSearchUnitMap() {
		return searchUnitMap;
	}

	public void setSearchUnitMap(Map<String, Integer> searchUnitMap) {
		this.searchUnitMap = searchUnitMap;
	}

	public Integer getSearchOfficeId() {
		return searchOfficeId;
	}

	public void setSearchOfficeId(Integer searchOfficeId) {
		this.searchOfficeId = searchOfficeId;
	}

	public Map<String, Integer> getSearchOfficeMap() {
		return searchOfficeMap;
	}

	public void setSearchOfficeMap(Map<String, Integer> searchOfficeMap) {
		this.searchOfficeMap = searchOfficeMap;
	}

	public DualListModel getDualListModel() {
		return dualListModel;
	}

	public void setDualListModel(DualListModel dualListModel) {
		this.dualListModel = dualListModel;
	}

	public Integer getEditRow() {
		return editRow;
	}

	public void setEditRow(Integer editRow) {
		this.editRow = editRow;
	}

	public String getProDefId() {
		return proDefId;
	}

	public void setProDefId(String proDefId) {
		this.proDefId = proDefId;
	}

	public String getBusinesskey() {
		return businesskey;
	}

	public void setBusinesskey(String businesskey) {
		this.businesskey = businesskey;
	}

	public String getBusinessId() {
		return businessId;
	}

	public void setBusinessId(String businessId) {
		this.businessId = businessId;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String getNoteId() {
		return noteId;
	}

	public void setNoteId(String noteId) {
		this.noteId = noteId;
	}

	
	
}
