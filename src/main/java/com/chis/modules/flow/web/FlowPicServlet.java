package com.chis.modules.flow.web;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;

import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.SpringContextHolder;
/**
 * 流程图
 * <AUTHOR>
 */
@WebServlet(name="FlowPicServlet",value="/FlowPicServlet")
public class FlowPicServlet extends HttpServlet {

	private static final long serialVersionUID = -7900656732154215033L;
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);

	public void init() throws ServletException {
		super.init();
	}

	public void service(HttpServletRequest request, HttpServletResponse resp)
			throws ServletException, IOException {
		InputStream is = null;
		String taskId = request.getParameter("taskId");
		if(StringUtils.isNotBlank(taskId)) {
			String instanceId = this.flowBusinessService.flowIsEnd(taskId);
			if(StringUtils.isNotBlank(instanceId)) {
				is = this.flowBusinessService.findImageStream(instanceId, 2);
			}else {
				is = this.flowBusinessService.findImageStream(taskId, 1);
			}
		}else {
			String processId = request.getParameter("processId");
			if(StringUtils.isNotBlank(processId)) {
				is = this.flowBusinessService.findImageStream(processId, 2);
			}
		}
		if (is != null) {
			resp.setContentType("image/png");
			OutputStream out = resp.getOutputStream();
			try {
				byte[] bs = new byte[1024];
				int n = 0;
				while ((n = is.read(bs)) != -1) {
					out.write(bs, 0, n);
				}
				out.flush();
			} catch (Exception ex) {
				ex.printStackTrace();
			} finally {
				is.close();
				out.close();
			}
		}
	}

	public void destroy() {
	}
}