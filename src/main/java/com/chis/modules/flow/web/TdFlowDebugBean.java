package com.chis.modules.flow.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.entity.TdFlowType;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MapUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.flow.logic.TdFlowDebugCondBean;
import com.chis.modules.system.entity.TdMsgMain;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.utils.MsgSendUtil;
import com.chis.modules.system.web.FacesSimpleBean;

/**
 * 流程追踪
 * 
 * <AUTHOR>
 */
@ManagedBean(name = "tdFlowDebugBean")
@ViewScoped
public class TdFlowDebugBean extends FacesSimpleBean {

	private static final long serialVersionUID = -797648210053613135L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder
			.getBean(FlowBusinessServiceImpl.class);
	/** 查询条件：流程类型树 */
	private TreeNode searchTreeNode;
	/** 查询条件Bean */
	private TdFlowDebugCondBean condition;
	/** 流程实例ID */
	private String processId;
	/** 流程节点名称 */
	private String taskNodeName;
	/** 流程任务名称 */
	private String taskName;
	private Integer userId;

	public TdFlowDebugBean() {
		super.ifSQL = Boolean.TRUE;
		this.init();
		this.searchAction();
	}

	private void init() {
		this.initSearchCondition();
		/**
		 * 非空的话是从详情界面转向过来的
		 */
		String con = JsfUtil.getRequest().getParameter("param");
		if (StringUtils.isNotBlank(con)) {
			this.condition = JSON.parseObject(con, TdFlowDebugCondBean.class);
		} else {
			this.condition = new TdFlowDebugCondBean();
		}
	}

	/**
	 * 初始化查询条件
	 */
	private void initSearchCondition() {
		this.searchTreeNode = new DefaultTreeNode("root", null);
		List<TdFlowType> list = this.flowBusinessService.findFlowTypeList();
		if (null != list && list.size() > 0) {
			Map<Integer, TreeNode> map = new HashMap<Integer, TreeNode>();
			for (TdFlowType t : list) {
				if (null == t.getParent()) {
					TreeNode node = new DefaultTreeNode(t, this.searchTreeNode);
					map.put(t.getRid(), node);
				} else {
					TreeNode node = new DefaultTreeNode(t, map.get(t
							.getParent().getRid()));
					map.put(t.getRid(), node);
				}
			}
			map.clear();
		}
	}

	/**
	 * 流程类型选中事件
	 * 
	 * @param event
	 *            选中事件
	 */
	public void onSearchNodeSelect(NodeSelectEvent event) {
		TreeNode node = event.getTreeNode();
		if (null != node) {
			TdFlowType type = (TdFlowType) node.getData();
			this.condition.setSearchFlowTypeId(type.getRid());
			this.condition.setSearchFlowTypeName(type.getTypeName());
		}
	}

	/**
	 * 催办
	 */
	public void saveAction() {
		String userIds = this.flowBusinessService.cbFlow(this.processId,
				this.taskNodeName, this.taskName, this.sessionData.getUser()
						.getRid());
		if (StringUtils.isNotBlank(userIds)) {
			MsgSendUtil.sendNewMsg(userIds);
		}
		JsfUtil.addSuccessMessage("催办成功！");
	}

	/**
	 * 转向到处理界面
	 */
	public void modInitAction() {
		try {
			StringBuilder sb = new StringBuilder(
					"/webapp/flow/tdFlowDetail.faces?ph=1");
			if (StringUtils.isNotBlank(this.processId)) {
				sb.append("&processId=").append(this.processId);
			}
			if (null != userId) {
				sb.append("&userID=").append(this.userId);
			}
			if (null != this.condition) {
				sb.append("&from=/webapp/flow/tdFlowDebugList.faces?param=")
						.append(JSON.toJSONString(this.condition));
			}
			FacesContext.getCurrentInstance().getExternalContext()
					.redirect(sb.toString());
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}

	/**
	 * 显示流程图
	 */
	public void processPicAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 900,
				null, 600);

		// 用于TdFlowHisTaskBean获取
		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		List<String> paramList = new ArrayList<String>(1);
		paramList.add(this.processId);
		paramMap.put("processId", paramList);

		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("tdFlowPicList", options, paramMap);
	}

	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder();
		sb.append(" WITH M AS ( ");
		sb.append(" SELECT DISTINCT A1.ID_, T2.TYPE_NAME, A1.BUSINESS_KEY_, T3.NODE_NAME, T4.USERNAME, NVL2(A1.END_TIME_, '结束', '未结束') AS FLOWSTATECN, ");
		sb.append(" NVL2(A1.END_TIME_, '1', '0') AS FLOWSTATE,T4.RID USERID");
		sb.append(" FROM ACT_HI_PROCINST A1 ");
		sb.append(" INNER JOIN TD_FLOW_DEF T1 ON A1.PROC_DEF_ID_ = T1.ACT_DEF_ID ");
		sb.append(" INNER JOIN TD_FLOW_TYPE T2 ON T1.TPYE_ID = T2.RID ");
		sb.append(" LEFT JOIN ACT_RU_EXECUTION A3 ON A3.PROC_INST_ID_ = A1.ID_ ");
		sb.append(" LEFT JOIN TD_FLOW_NODE T3 ON T3.ACT_NODE_ID = A3.ACT_ID_ AND T3.DEF_ID = T1.RID ");
		sb.append(
				" INNER JOIN TS_USER_INFO T4 ON A1.START_USER_ID_ = T4.RID AND T4.UNIT_RID =  '")
				.append(this.sessionData.getUser().getTsUnit().getRid())
				.append("' ");
		sb.append(" WHERE 1=1 AND A1.DELETE_REASON_ IS NULL ");
		if (null != this.condition.getSearchFlowTypeId()) {
			sb.append(
					" AND T2.RID IN(SELECT Z.RID FROM TD_FLOW_TYPE Z START WITH RID='")
					.append(this.condition.getSearchFlowTypeId())
					.append("' CONNECT BY PRIOR RID = PARENT_ID) ");
		}
		if (StringUtils.isNotBlank(this.condition.getSearchTaskName())) {
			sb.append(" AND A1.BUSINESS_KEY_ LIKE :searchTaskName ");
			this.paramMap.put("searchTaskName", "%"
					+ this.condition.getSearchTaskName().trim() + "%");
		}
		if (this.condition.getSearchFlowState() == 0) {
			sb.append(" AND A1.END_TIME_ IS NULL ");
		} else if (this.condition.getSearchFlowState() == 1) {
			sb.append(" AND A1.END_TIME_ IS NOT NULL ");
		}
		if (this.condition.isSearchMyFlow()) {
			sb.append(" AND A1.START_USER_ID_ = '")
					.append(this.sessionData.getUser().getRid()).append("' ");
		}

		sb.append(" ) SELECT  M.ID_,M.TYPE_NAME,M.BUSINESS_KEY_,LISTAGG(M.NODE_NAME, '，') WITHIN GROUP (ORDER BY M.ID_) NODE_NAME, ");
		sb.append(" M.USERNAME,M.FLOWSTATECN, M.FLOWSTATE,M.USERID FROM M ");
		sb.append(" GROUP BY M.ID_,M.TYPE_NAME,M.BUSINESS_KEY_,M.USERNAME,M.FLOWSTATECN,M.FLOWSTATE,M.USERID  ");
		sb.append(" ORDER BY M.TYPE_NAME, M.BUSINESS_KEY_ DESC ");

		String h1 = sb.toString();
		String h2 = "SELECT COUNT(*) FROM (" + sb.toString() + ") ";
		return new String[] { h1, h2 };
	}

	public TreeNode getSearchTreeNode() {
		return searchTreeNode;
	}

	public void setSearchTreeNode(TreeNode searchTreeNode) {
		this.searchTreeNode = searchTreeNode;
	}

	public TdFlowDebugCondBean getCondition() {
		return condition;
	}

	public void setCondition(TdFlowDebugCondBean condition) {
		this.condition = condition;
	}

	public String getProcessId() {
		return processId;
	}

	public void setProcessId(String processId) {
		this.processId = processId;
	}

	public String getTaskNodeName() {
		return taskNodeName;
	}

	public void setTaskNodeName(String taskNodeName) {
		this.taskNodeName = taskNodeName;
	}

	public String getTaskName() {
		return taskName;
	}

	public void setTaskName(String taskName) {
		this.taskName = taskName;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

}
