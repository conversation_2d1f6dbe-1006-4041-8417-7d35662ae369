package com.chis.modules.flow.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.UUID;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;

import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.DualListModel;
import org.primefaces.model.TreeNode;
import org.primefaces.model.UploadedFile;

import com.chis.activiti.entity.TbFlowAdvtemplate;
import com.chis.activiti.entity.TdFlowDef;
import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.entity.TdFlowNodeBtn;
import com.chis.activiti.entity.TdFlowNodePage;
import com.chis.activiti.entity.TdFlowNodeScript;
import com.chis.activiti.entity.TdFlowNodeTxType;
import com.chis.activiti.entity.TdFlowRule;
import com.chis.activiti.entity.TdFlowTitle;
import com.chis.activiti.entity.TdFlowType;
import com.chis.activiti.enumn.DealType;
import com.chis.activiti.enumn.FlowBtnType;
import com.chis.activiti.enumn.FlowInType;
import com.chis.activiti.javabean.DirectoryCondition;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.FileUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TbTempmetaDefine;
import com.chis.modules.system.entity.TdFormDef;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsTxtype;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.protocol.IMsgSend;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesEditBean;

/**
 * 流程定义管理
 *
 * @history 文件存储到虚拟路径修改
 * @LastModify xt
 * @ModifyDate 2014年9月4日
 *
 * <AUTHOR>
 */
@ManagedBean(name = "tdFlowDefBean")
@ViewScoped
public class TdFlowDefBean extends FacesEditBean {

    private static final long serialVersionUID = -738445454536625063L;
    /**
     * 存在session中的对象
     */
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
     * 查询条件：流程类型树
     */
    private TreeNode searchTreeNode;
    /**
     * 查询条件：流程类型选择ID
     */
    private Integer searchFlowTypeId;
    /**
     * 查询条件：流程类型名称
     */
    private String searchFlowTypeName;
    /**
     * 查询条件：流程名称
     */
    private String searchFlowName;

    /**
     * 添加界面：流程类型选择ID
     */
    private Integer flowTypeId;
    /**
     * 添加界面：流程类型名称
     */
    private String flowTypeName;
    /**
     * 流程定义对象
     */
    private TdFlowDef tdFlowDef = new TdFlowDef();
    /**
     * 操作类型
     */
    private String actionType;

    /**
     * 角色分配
     */
    private Map<String, Integer> roleMap = new HashMap<String, Integer>(0);
    /**
     * 角色分配:已经选择的角色id
     */
    private List<String> selectedRoles = new ArrayList<String>(0);
    /**
     * 流程定义RID
     */
    private Integer rid;
    /**
     * 流程定义名称
     */
    private String defName;

    /**
     * 用户授权:科室名称
     */
    private String userOfficeName;
    /**
     * 用户授权:科室ID
     */
    private Integer userOfficeRid;
    /**
     * 用户授权:流程类型树
     */
    private TreeNode userOfficeTreeNode;
    /**
     * 用户授权:用于机构授权的选择
     */
    private DualListModel dualListModel = new DualListModel();
    /**
     * 用户授权:用于缓存初始化的已选用户
     */
    private List<TsUserInfo> storeTargetList;

    /**
     * 流程节点配置
     */
    private List<TdFlowNode> flowNodeList = new ArrayList<TdFlowNode>();
    private TdFlowNode tdFlowNode;
    /**
     * 跳转方式
     */
    private Map<String, FlowInType> flowInTypeMap = new LinkedHashMap<String, FlowInType>(0);
    /**
     * 处理类型
     */
    private Map<String, DealType> dealTypeMap = new LinkedHashMap<String, DealType>(0);
    /**
     * 表单下拉
     */
    private Map<String, String> urlMap = new LinkedHashMap<String, String>(0);
    /**
     * 被拟办节点集合
     */
    private Map<String, String> bnbNodesMap = new LinkedHashMap<String, String>(0);
    /**
     * 被拟办节点选择
     */
    private List<String> bnbList = new ArrayList<String>(0);
    /**
     * 待办人选择规则集合
     */
    private Map<String, String> ruleMap = new LinkedHashMap<String, String>(0);
    /**
     * 待办人选择的规则编码
     */
    private String ruleCode;
    /**
     * 根据规则编码获取的数据字典集合
     */
    private Map<String, String> dictoryMap = new LinkedHashMap<String, String>(0);
    /**
     * 选中的数据字典集合
     */
    private List<String> dictoryList = new ArrayList<String>();
    /**
     * 选择的数据字典集合
     */
    private Map<String, String> selectDictoryMap = new LinkedHashMap<String, String>(0);
    /**
     * 选择的数据字典集合
     */
    private List<String> selectDictoryList;

    //============模板意见================
    /**
     * 模板意见列表
     */
    private List<TbFlowAdvtemplate> templateList = new ArrayList<TbFlowAdvtemplate>(0);
    /**
     * 模板对象
     */
    private TbFlowAdvtemplate tbFlowAdvtemplate = new TbFlowAdvtemplate();
    /**
     * 模板元素集合
     */
    private List<TbTempmetaDefine> defineList;
    /**
     * 模板元素集合
     */
    private List<TbTempmetaDefine> filterDefineList = new ArrayList<TbTempmetaDefine>(0);
    /**
     * 模板元素对象
     */
    private TbTempmetaDefine tbTempmetaDefine = new TbTempmetaDefine();
    /**
     * 解析后的语句
     */
    private String resolvedContent = null;
    /**
     * 表格的过滤下拉
     */
    private List<SelectItem> filterDefineSelectList = new ArrayList<SelectItem>(0);
    /*按钮集合*/
    private List<TdFlowNodeBtn> buttonList;
    /*按钮全选*/
    private Integer allSelect;
    /*通讯方式*/
    private List<TsTxtype> txtypeList;
    /*所有的实现类*/
    private Map<String, String> implClassMap;
    /*按钮全选*/
    private Integer txselected;
    /*脚本*/
    private TdFlowNodeScript tdFlowNodeScript = new TdFlowNodeScript();
    /**
     * 模板标题列表
     */
    private List<TdFlowTitle> titleTemplateList = new ArrayList<TdFlowTitle>(0);
    private TdFlowTitle tdFlowTitle = new TdFlowTitle();

    @PostConstruct
    public void init() {
        super.ifSQL = Boolean.TRUE;
        this.initSearchCondition();
        this.initTxTypeList();
        this.searchAction();
    }

	/**
	 * 弹出页面单选弹出框
	 */
	public void selectFormAction() {
		Map<String, Object> options = new HashMap<String, Object>();
		options.put("modal", true);
		options.put("draggable", true);
		options.put("resizable", false);
		options.put("width", 650);
		options.put("contentWidth", 620);
		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("/webapp/flow/formSelectList.faces", options, paramMap);
	}
	
	public void onFormSelect(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			if(selectedMap.get("tdFormDef") != null){
				tdFlowNode.setTdFormDef((TdFormDef)selectedMap.get("tdFormDef"));
			}
		}
	}

	public void clearFormName()  {
		tdFlowNode.setTdFormDef(null);
	}

    /**
     * 初始化可选通讯方式
     */
    private void initTxTypeList() {
        //查找所有通讯方式
        txtypeList = flowBusinessService.findAllUseableTxtype();
        //初始化实现类描述
        this.implClassMap = new HashMap<String, String>();
        
        Map<String, IMsgSend> beans = SpringContextHolder.getBeans(IMsgSend.class);
        if(null != beans && beans.size() > 0) {
        	for(IMsgSend msgSend : beans.values()) {
        		this.implClassMap.put(msgSend.description(), msgSend.getClass().getName());
        	}
        }
    }

    /**
     * 初始化查询条件
     */
    private void initSearchCondition() {
        if (null == this.searchTreeNode) {
            this.searchTreeNode = new DefaultTreeNode("root", null);
            List<TdFlowType> list = this.flowBusinessService.findFlowTypeList();
            if (null != list && list.size() > 0) {
                Map<Integer, TreeNode> map = new HashMap<Integer, TreeNode>();
                for (TdFlowType t : list) {
                    if (null == t.getParent()) {
                        TreeNode node = new DefaultTreeNode(t, this.searchTreeNode);
                        map.put(t.getRid(), node);
                    } else {
                        TreeNode node = new DefaultTreeNode(t, map.get(t.getParent().getRid()));
                        map.put(t.getRid(), node);
                    }
                }
                map.clear();
            }
        }

    }

    /**
     * 流程类型选中事件
     *
     * @param event 选中事件
     */
    public void onSearchNodeSelect(NodeSelectEvent event) {
        TreeNode node = event.getTreeNode();
        if (null != node) {
            TdFlowType type = (TdFlowType) node.getData();
            this.searchFlowTypeId = type.getRid();
            this.searchFlowTypeName = type.getTypeName();
        }
    }

    /**
     * 添加流程类型选中事件
     *
     * @param event 选中事件
     */
    public void onNodeSelect(NodeSelectEvent event) {
        TreeNode node = event.getTreeNode();
        if (null != node) {
            TdFlowType type = (TdFlowType) node.getData();
            this.flowTypeId = type.getRid();
            this.flowTypeName = type.getTypeName();
        }
    }

    /**
     * 添加修改初始化
     */
    public void deployInit() {
        if (null != this.actionType && "deployAddInit".equals(this.actionType)) {
            // 添加
            this.tdFlowDef = new TdFlowDef();
            this.tdFlowDef.setCreateDate(new Date());
            this.tdFlowDef.setIsDispapp(0);
            this.tdFlowDef.setCreateManid(this.sessionData.getUser().getRid());
            this.flowTypeId = null;
            this.flowTypeName = null;
            
        } else {
            // 修改
            this.tdFlowDef = this.flowBusinessService.findDef(this.rid);
            this.tdFlowDef.setModifyDate(new Date());
            this.tdFlowDef.setModifyManid(this.sessionData.getUser().getRid());
            TdFlowType type = this.tdFlowDef.getTdFlowType();
            if (null != type) {
                this.flowTypeId = type.getRid();
                this.flowTypeName = type.getTypeName();
            }
        }
    }

    /**
     * 文件上传
     */
    public void handleFileUpload(FileUploadEvent event) {
        try {
            if (null != event) {
                UploadedFile file = event.getFile();
                String fileName = file.getFileName();
                this.tdFlowDef.setZipName(fileName);
                fileName = fileName.substring(fileName.lastIndexOf("\\") + 1, fileName.length());
				try {
					String uuid = UUID.randomUUID().toString().replaceAll("-", "");
					// 配置的虚拟路径
					String path = JsfUtil.getAbsolutePath();
					String filePath = new StringBuilder(path).append("/demosImg").toString();
					String relativePath = new StringBuffer("/demosImg/").append(uuid)
							.append(fileName.substring(fileName.lastIndexOf("."), fileName.length())).toString();
					filePath = new StringBuilder(path).append(relativePath).toString();
					FileUtils.copyFile(filePath, file.getInputstream());
					this.tdFlowDef.setZipPath(filePath);
					JsfUtil.addSuccessMessage("上传成功！");

					RequestContext requestContext = RequestContext.getCurrentInstance();
					requestContext.execute("fileUIdVar.hide();");
				} catch (Exception e) {
					JsfUtil.addErrorMessage("上传失败！");
					e.printStackTrace();
				}
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除上传的文件
     */
    public void deleteDiskFile() {
        this.tdFlowDef.setZipPath(null);
    }

    /**
     * 分配角色初始化
     */
    public void fpRoleInitAction() {
        /**
         * 初始化可选角色: 1.自己拥有的角色 2.自己单位拥有的角色
         */
        if (null == this.roleMap || this.roleMap.size() == 0) {
            this.roleMap = this.flowBusinessService.findRoleMapICanSee(this.sessionData.getUser().getRid(), this.sessionData.getUser().getTsUnit().getRid());
        }
        this.selectedRoles = this.flowBusinessService.findRoleListIHave(this.rid);
    }

    /**
     * 分配角色保存
     */
    public void fpRoleAction() {
        this.flowBusinessService.grantRolesToDef(this.rid, this.selectedRoles);
    }

    /**
     * 用户分配初始化
     */
    public void yhfpInitAction() {
        TsUserInfo tsUserInfo = sessionData.getUser();
        this.userOfficeName = null;
        this.userOfficeRid = null;

        // 初始化科室树
        if (null == this.userOfficeTreeNode) {
            this.userOfficeTreeNode = new DefaultTreeNode("root", null);
            List<TsOffice> list = this.flowBusinessService.findOfficeList(tsUserInfo.getTsUnit().getRid());
            if (null != list && list.size() > 0) {
                Map<Integer, TreeNode> map = new HashMap<Integer, TreeNode>();
                for (TsOffice t : list) {
                    if (null == t.getParentOffice()) {
                        TreeNode node = new DefaultTreeNode(t, this.userOfficeTreeNode);
                        map.put(t.getRid(), node);
                    } else {
                        TreeNode node = new DefaultTreeNode(t, map.get(t.getParentOffice().getRid()));
                        map.put(t.getRid(), node);
                    }
                }
                map.clear();
            }
        }

        // 已授权的用户
        this.storeTargetList = this.flowBusinessService.findUsers(this.rid);
        StringBuilder sb = new StringBuilder();
        String selectIds = null;
        if (null != this.storeTargetList && this.storeTargetList.size() > 0) {
            for (TsUserInfo t : this.storeTargetList) {
                sb.append(",").append(t.getRid());
            }
            selectIds = sb.toString().substring(1);
        }
        List<TsUserInfo> sourceList = this.flowBusinessService.findUsers(tsUserInfo.getTsUnit().getRid(), null, selectIds);
        // 初始化用户
        this.dualListModel = new DualListModel(sourceList, this.storeTargetList);
    }

    /**
     * 用户分配:科室选中事件
     *
     * @param event 选中事件
     */
    public void onOfficeNodeSelect(NodeSelectEvent event) {
        TreeNode node = event.getTreeNode();
        if (null != node) {
            TsOffice office = (TsOffice) node.getData();
            this.userOfficeRid = office.getRid();
            this.userOfficeName = office.getOfficename();

            // 同时刷新可选用户
            List<TsUserInfo> targetList = this.dualListModel.getTarget();
            StringBuilder sb = new StringBuilder();
            String selectIds = null;
            if (null != targetList && targetList.size() > 0) {
                for (TsUserInfo t : targetList) {
                    sb.append(",").append(t.getRid());
                }
                selectIds = sb.toString().substring(1);
            }

            List<TsUserInfo> sourceList = this.flowBusinessService.findUsers(null, this.userOfficeRid, selectIds);
            // 初始化用户
            this.dualListModel = new DualListModel(sourceList, targetList);
        }
    }

    /**
     * 用户分配保存
     */
    public void yhfpAction() {
        this.flowBusinessService.yhfpRole(this.storeTargetList, this.dualListModel.getTarget(), this.rid);
    }

    /**
     * 流程定义页面，节点配置转向节点列表页面
     */
    @Override
    public void addInit() {
        this.flowNodeList = this.flowBusinessService.findFlowNodes(this.rid);
    }

    /**
     * 节点配置初始化
     */
    public void nodeAddInit() {
        this.tdFlowNode = this.flowBusinessService.findTdFlowNode(this.tdFlowNode.getRid());
        if(null == tdFlowNode.getMulNodeRule()){
        	tdFlowNode.setMulNodeRule(0);
        }
        if (this.flowInTypeMap.size() == 0) {
            FlowInType[] types = FlowInType.values();
            if (null != types && types.length > 0) {
                for (FlowInType f : types) {
                    this.flowInTypeMap.put(f.getTypeCN(), f);
                }
            }
        }
        if (this.dealTypeMap.size() == 0) {
            DealType[] types = DealType.values();
            if (null != types && types.length > 0) {
                for (DealType f : types) {
                    this.dealTypeMap.put(f.getTypeCN(), f);
                }
            }
        }
        if (this.urlMap.size() == 0) {
            //this.urlMap = SelectMap.getUrlMap();
        }


        this.bnbList.clear();
        this.bnbNodesMap.clear();
        if (this.tdFlowNode.getFlowInType().equals(FlowInType.NBJD)) {
            int curNum = this.tdFlowNode.getNums().intValue();
            for (TdFlowNode node : this.flowNodeList) {
                if (node.getNums().intValue() > curNum) {
                    this.bnbNodesMap.put(node.getNodeName(), node.getRid().toString());
                    if (node.getIfNbNode().intValue() == 1) {
                        this.bnbList.add(node.getRid().toString());
                    }
                }
            }
        }
    }

    /**
     * 节点跳转方式的选择
     */
    public void flowInTypeChgAction() {
        //System.out.println("选择的跳转：" + this.tdFlowNode.getFlowInType().getTypeCN());
        this.bnbList.clear();
        this.bnbNodesMap.clear();

        if (this.tdFlowNode.getFlowInType().equals(FlowInType.NBJD)) {
            int curNum = this.tdFlowNode.getNums().intValue();
            for (TdFlowNode node : this.flowNodeList) {
                if (node.getNums().intValue() > curNum) {
                    this.bnbNodesMap.put(node.getNodeName(), node.getRid().toString());
                }
            }
        }

    }

    /**
     * 节点保存按钮
     */
    public void nodeSaveAction() {
    	TdFlowDef tempDef = tdFlowNode.getTdFlowDef();
		// 如果是动态表单，则获取数据库中动态表单地址
		if (null != tempDef && null != tempDef.getIfDynaForm() && tempDef.getIfDynaForm().intValue() == 1) {
			TdFlowNodePage dynaFormUrl = this.flowBusinessService.getDynaFormUrl("sys_dynaform");
			this.tdFlowNode.setJspUrl(dynaFormUrl == null ? null : dynaFormUrl.getPageUrl());
			this.tdFlowNode.setTdFlowNodePage(dynaFormUrl);
			if (tdFlowNode.getTdFormDef() == null) {
				JsfUtil.addErrorMessage("请选择动态表单！");
				return;
			}
		} else {
			if (tdFlowNode.getTdFlowNodePage() == null) {
				JsfUtil.addErrorMessage("请选择表单地址！");
				return;
			}
			this.tdFlowNode.setJspUrl(tdFlowNode.getTdFlowNodePage().getPageUrl());
			this.tdFlowNode.setTdFormDef(null);
		}
        this.tdFlowNode.setModifyDate(new Date());
        this.tdFlowNode.setModifyManid(this.sessionData.getUser().getRid());
        String msg = this.flowBusinessService.updateTdFlowNode(this.tdFlowNode, this.bnbList);
        if (StringUtils.isNotBlank(msg)) {
            JsfUtil.addErrorMessage(msg);
        } else {
            //重新查一遍各节点
            this.addInit();
            RequestContext requestContext = RequestContext.getCurrentInstance();
            requestContext.execute("NodeEditDialog.hide()");
        }
    }

    /**
     * 节点设置待办人初始化
     */
    public void nodeSqInit() {
        if (this.ruleMap.size() == 0) {
            List<TdFlowRule> rulesList = this.flowBusinessService.findFlowRules();
            if (null != rulesList && rulesList.size() > 0) {
                for (TdFlowRule rule : rulesList) {
                    this.ruleMap.put(rule.getRuleDesc(), rule.getIdcode());
                }
            }
        }

        this.dictoryMap = new LinkedHashMap<String, String>(0);
        this.dictoryList = new ArrayList<String>(0);
        this.ruleCode = null;

        this.selectDictoryMap = this.flowBusinessService.findSelectedRules(this.tdFlowNode.getRid());
        this.selectDictoryList = new ArrayList<String>(0);
    }

    /**
     * 根据规编码刷新数据字典
     */
    public void onRuleChgAction() {
        //System.out.println("--onRuleChgAction--" + this.ruleCode);
        DirectoryCondition condition = new DirectoryCondition();
        condition.setNodeId(this.tdFlowNode.getRid());
        condition.setUserId(this.sessionData.getUser().getRid());

        this.dictoryMap = this.flowBusinessService.findDirectoryDatas(condition, this.ruleCode);
        if (null != this.selectDictoryMap && this.selectDictoryMap.size() > 0) {
            Set<String> keySet = this.selectDictoryMap.keySet();
            for (String key : keySet) {
                this.dictoryMap.remove(key);
            }
        }
    }

    /**
     * 节点设置待办人保存
     */
    public void nodeSqAction() {
        Collection<String> coll = this.selectDictoryMap.values();
        if (null == coll || coll.size() == 0) {
            JsfUtil.addErrorMessage("请选择待办人角色！");
            return;
        }
        this.flowBusinessService.saveOrUpdateFlowRuleNode(this.tdFlowNode.getRid(), coll);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.execute("AssigneeEditDialog.hide()");
    }

    /**
     * 节点设置 添加角色
     */
    public void nodeSqAdd() {
        if (null != this.dictoryList && this.dictoryList.size() > 0) {
            for (String s : this.dictoryList) {
                String key = findLabel(this.dictoryMap, s);
                this.selectDictoryMap.put(key, s);
                this.dictoryMap.remove(key);
            }
        }
    }

    /**
     * 节点设置 添加角色
     */
    public void nodeSqDelete() {
        if (null != this.selectDictoryList && this.selectDictoryList.size() > 0) {
            for (String s : this.selectDictoryList) {
                String key = findLabel(this.selectDictoryMap, s);
                this.selectDictoryMap.remove(key);
                if (StringUtils.isNotBlank(this.ruleCode)) {
                    if (this.ruleCode.equals(s.split("_")[2])) {
                        this.dictoryMap.put(key, s);
                    }
                }
            }
        }
    }

    /**
     * 转向到码表编辑页面
     */
    @Override
    public void viewInit() {

    }

    /**
     * 转向到码表无树形结构的编辑界面
     */
    @Override
    public void modInit() {
    }

    /**
     * 保存
     */
    public void saveAction() {
        RequestContext requestContext = RequestContext.getCurrentInstance();
        if (null == this.flowTypeId) {
            JsfUtil.addErrorMessage("tabView:editForm:flowTypeName", "请选择流程类型！");
//			requestContext.execute("StatusDialog.hide()");
            return;
        }
        if(StringUtils.isBlank(this.tdFlowDef.getDefName()))	{
        	JsfUtil.addErrorMessage("tabView:editForm:defName", "流程名称不允许为空！");
        	return;
        }
        if (StringUtils.isBlank(this.tdFlowDef.getZipPath())) {
            JsfUtil.addErrorMessage("请上传流程部署包！");
            return;
        }
    	//如果是动态表单，则动态表单必填
//		if( null != this.tdFlowDef.getIfDynaForm() &&  this.tdFlowDef.getIfDynaForm().intValue() == 1 && null == this.tdFlowDef.getTdFormDef() )	{
//			JsfUtil.addErrorMessage("请选择动态表单！");
//			return;
//		}
		
        this.tdFlowDef.setTdFlowType(new TdFlowType(this.flowTypeId));

        if (null != this.tdFlowDef.getRid()) {
            this.flowBusinessService.updateDef(this.tdFlowDef);
            this.searchAction();
            requestContext.execute("DefEditDialog.hide()");
        } else {
            String msg = this.flowBusinessService.saveDef(this.tdFlowDef);
            if (StringUtils.isNotBlank(msg)) {
                JsfUtil.addErrorMessage(msg);
            } else {
                this.searchAction();
                requestContext.execute("DefEditDialog.hide()");
            }
        }

    }

    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder("  FROM ACT_RE_PROCDEF T2 ");
        sb.append(" INNER JOIN  ");
        sb.append("  (SELECT MAX(T1.VERSION_) AS VERSION_, T1.KEY_ FROM ACT_RE_PROCDEF T1 GROUP BY T1.KEY_) T3 ON T2.VERSION_ = T3.VERSION_ AND T2.KEY_ = T3.KEY_ ");
        sb.append("  INNER JOIN TD_FLOW_DEF T4 ON T4.ACT_DEF_ID = T2.ID_ ");
        sb.append(" INNER JOIN TD_FLOW_TYPE T5 ON T4.TPYE_ID = T5.RID ");
        sb.append(" WHERE 1=1 ");

        if (null != this.searchFlowTypeId) {
            sb.append(" AND  T5.RID IN (SELECT Z.RID FROM TD_FLOW_TYPE Z START WITH RID='").append(this.searchFlowTypeId).append("' CONNECT BY PRIOR RID = PARENT_ID) ");
        }
        if (StringUtils.isNotBlank(this.searchFlowName)) {
            sb.append(" AND T4.DEF_NAME LIKE :defName");
            this.paramMap.put("defName", "%" + this.searchFlowName.trim() + "%");
        }
        String h2 = "SELECT COUNT(*) " + sb.toString();
        String h1 = "SELECT T4.RID, T5.TYPE_NAME, T4.DEF_NAME, T4.DEF_DESC, T4.DEF_VERSION " + sb.append(" ORDER BY T5.NUMS, T4.NUMS ");
        return new String[]{h1, h2};
    }

    /**
     * 根据map的value找key,这里的map的值都是唯一的
     *
     * @param map   集合
     * @param value 值
     * @return 返回key
     */
    private static String findLabel(Map<String, String> map, String value) {
        if (null != map && map.size() > 0) {
            Set<Entry<String, String>> entrySet = map.entrySet();
            for (Entry<String, String> e : entrySet) {
                if (e.getValue().equals(value)) {
                    return e.getKey();
                }
            }
        }
        return null;
    }

    /**
     * 查询模板意见列表
     */
    public void searchTemplateList() {
        if (null != this.tdFlowNode && null != this.tdFlowNode.getRid()) {
            this.templateList = this.flowBusinessService.findAdvTemplateList(this.tdFlowNode.getRid());
        } else {
            this.templateList = new ArrayList<TbFlowAdvtemplate>(0);
        }
    }

    /**
     * 添加模板初始化
     */
    public void addTemplateInit() {
        //初始化模板元素集合
        this.initDefineList();
        this.resolvedContent = null;
        this.tbFlowAdvtemplate = new TbFlowAdvtemplate();
        this.tbFlowAdvtemplate.setCreateManid(this.sessionData.getUser().getRid());
        this.tbFlowAdvtemplate.setTdFlowNode(this.tdFlowNode);
    }

    /**
     * 修改模板初始化
     */
    public void modTemplateInit() {
        //初始化模板元素集合
        this.initDefineList();
        this.resolvedContent = null;
    }

    /**
     * 保存模板
     */
    public void saveTemplate() {
        if (StringUtils.isBlank(this.tbFlowAdvtemplate.getTemplateContent())) {
            JsfUtil.addErrorMessage("模板内容不允许为空！");
            return;
        } else {
            if (this.tbFlowAdvtemplate.getTemplateContent().length() > 1000) {
                JsfUtil.addErrorMessage("模板内容不允许超过1000个字！");
                return;
            }
        }

        String msg = this.flowBusinessService.saveOrUpdateFlowTemplate(this.tbFlowAdvtemplate);
        if (StringUtils.isNotBlank(msg)) {
            JsfUtil.addErrorMessage(msg);
        } else {
            JsfUtil.addSuccessMessage("保存成功！");
            this.searchTemplateList();
            RequestContext.getCurrentInstance().update("tabView:editForm:templateTable");
            RequestContext.getCurrentInstance().execute("TemplateMgrDialog.hide();");
        }
    }
    
    /**
     * 查询模板标题列表
     */
    public void searchTitleTemplateList() {
        if (null != this.tdFlowDef && null != this.tdFlowDef.getRid()) {
            this.titleTemplateList = this.flowBusinessService.findTitleTemplateList(this.tdFlowDef.getRid());
        } else {
            this.titleTemplateList = new ArrayList<TdFlowTitle>(0);
        }
    }
    
    /**
     * 添加标题模板初始化
     */
    public void addTitleTemplateInit() {
        //初始化模板元素集合
        this.initDefineList();
        this.resolvedContent = null;
        this.tdFlowTitle = new TdFlowTitle();
        this.tdFlowTitle.setCreateManid(this.sessionData.getUser().getRid());
        this.tdFlowTitle.setTdFlowDef(this.tdFlowDef);
    }

    /**
     * 刷新元素实例
     */
    public void refreshMetaDemo() {
        this.findDefine();
    }
    
    /**
     * 保存模板
     */
    public void saveTitleTemplate() {
        if (StringUtils.isBlank(this.tdFlowTitle.getTemplateContent())) {
            JsfUtil.addErrorMessage("模板内容不允许为空！");
            return;
        } else {
            if (this.tdFlowTitle.getTemplateContent().length() > 1000) {
                JsfUtil.addErrorMessage("模板内容不允许超过1000个字！");
                return;
            }
        }
        if(tdFlowTitle.getRid() == null){
        	tdFlowTitle.setIsDefault(1);
            tdFlowTitle.setState(1);
            tdFlowTitle.setCreateDate(new Date());
            tdFlowTitle.setCreateManid(this.sessionData.getUser().getRid());
        }
        String msg = this.flowBusinessService.saveOrUpdateTdFlowTitle(this.tdFlowTitle);
        if (StringUtils.isNotBlank(msg)) {
            JsfUtil.addErrorMessage(msg);
        } else {
            JsfUtil.addSuccessMessage("保存成功！");
            this.searchTitleTemplateList();
            RequestContext.getCurrentInstance().update("tabView:mainForm:templateTable");
            RequestContext.getCurrentInstance().execute("TemplateMgrDialog.hide();");
        }
    }

    /**
     * 添加元素
     */
    public void addMeta() {
        this.findDefine();
        if (StringUtils.isNotBlank(this.tbFlowAdvtemplate.getTemplateContent())) {
            this.tbFlowAdvtemplate.setTemplateContent(this.tbFlowAdvtemplate.getTemplateContent() + this.tbTempmetaDefine.getMetaName());
        } else {
            this.tbFlowAdvtemplate.setTemplateContent(this.tbTempmetaDefine.getMetaName());
        }
    }
    
    /**
     * 添加元素
     */
    public void addTitle() {
        this.findDefine();
        if (StringUtils.isNotBlank(this.tdFlowTitle.getTemplateContent())) {
            this.tdFlowTitle.setTemplateContent(this.tdFlowTitle.getTemplateContent() + this.tbTempmetaDefine.getMetaName());
        } else {
            this.tdFlowTitle.setTemplateContent(this.tbTempmetaDefine.getMetaName());
        }
    }

    /**
     * 解析模板语句
     */
    public void resolveTemplate() {
        if (StringUtils.isNotBlank(this.tbFlowAdvtemplate.getTemplateContent())) {
            this.resolvedContent = this.commService.resolveTemplatemeta(this.tbFlowAdvtemplate.getTemplateContent(),
                    MetaConditionFactory.produceCondition(this.sessionData), true);
        } else {
            this.resolvedContent = null;
        }
    }
    
    /**
     * 解析标题模板语句
     */
    public void resolveTitleTemplate() {
        if (StringUtils.isNotBlank(this.tdFlowTitle.getTemplateContent())) {
            this.resolvedContent = this.commService.resolveTemplatemeta(this.tdFlowTitle.getTemplateContent(),
                    MetaConditionFactory.produceCondition(this.sessionData), true);
        } else {
            this.resolvedContent = null;
        }
    }

    /**
     * 元素的单击双击事件，根据传入的RID查找元素对象
     */
    private void findDefine() {
        String ridStr = JsfUtil.getRequestParameterMap().get("param1");
        if (StringUtils.isNotBlank(ridStr)) {
            for (TbTempmetaDefine define : this.defineList) {
                if (ridStr.equals(define.getRid().toString())) {
                    this.tbTempmetaDefine = define;
                    return;
                }
            }
        } else {
            this.tbTempmetaDefine = new TbTempmetaDefine();
        }
    }

    /**
     * 初始化元素集合
     */
    private void initDefineList() {
        //初始化模板元素集合
        if (null == this.defineList || this.defineList.isEmpty()) {
            this.defineList = this.flowBusinessService.findAllTemplateDefine();

            if (null != this.defineList && !this.defineList.isEmpty()) {

                //初始化过滤下拉
                this.filterDefineSelectList.clear();
                Map<Short, Short> map = new HashMap<Short, Short>();

                this.filterDefineList.clear();
                for (TbTempmetaDefine define : this.defineList) {
                    if (SystemType.COMM.equals(define.getSystemType())) {
                        this.filterDefineList.add(define);
                    }

                    if (null == map.get(define.getSystemType().getTypeNo())) {
                        this.filterDefineSelectList.add(new SelectItem(define.getSystemType().getTypeCN(), define.getSystemType().getTypeCN()));
                        map.put(define.getSystemType().getTypeNo(), define.getSystemType().getTypeNo());
                    }
                }
            }
        }
    }

    /**
     * 选择模板
     */
    public void chooseTemplate() {
        String[] templateArr = JsfUtil.getRequest().getParameterValues("templateSelect");
        if (null != templateArr && templateArr.length > 0) {
            this.flowBusinessService.startDefaultFlowTemplate(this.tdFlowNode.getRid(), templateArr[0]);
        } else {
            this.flowBusinessService.startDefaultFlowTemplate(this.tdFlowNode.getRid(), null);
        }
    }
    
    /**
     * 选择模板
     */
    public void chooseTitleTemplate() {
        String[] templateArr = JsfUtil.getRequest().getParameterValues("templateSelect");
        if (null != templateArr && templateArr.length > 0) {
            this.flowBusinessService.editDefaultFlowTitleTemplate(this.tdFlowDef.getRid(), templateArr[0]);
        } else {
            this.flowBusinessService.editDefaultFlowTitleTemplate(this.tdFlowDef.getRid(), null);
        }
    }

    /**
     * 节点配置按钮初始化
     *
     * @return
     */
    public void buttonInitAction() {
        if (flowNodeList != null && flowNodeList.size() > 0) {
            flowBusinessService.resetButtonCfg(flowNodeList);
            JsfUtil.addSuccessMessage("按钮初始化成功！");
        }
    }

    /**
     * 按钮配置初始化
     */
    public void buttonCfgInit() {
        buttonList = new ArrayList<TdFlowNodeBtn>();
        FlowBtnType[] flowBtnTypes = FlowBtnType.values();
        List<TdFlowNodeBtn> tempList = flowBusinessService.findTdFlowNodeButton(tdFlowNode.getRid());
        //判断所有按钮是否全选
        if ((tdFlowNode.getNums().equals(1) && tempList.size() == flowBtnTypes.length) || (!tdFlowNode.getNums().equals(1) && tempList.size() == flowBtnTypes.length - 1)) {
            allSelect = 1;
        } else {
            allSelect = 0;
        }
        if (flowBtnTypes.length > 0) {
            //遍历所有按钮类型
            for (FlowBtnType t : flowBtnTypes) {
                //只有第一个节点可以配置删除按钮
                if (t.getTypeNo().equals("3") && !tdFlowNode.getNums().equals(1)) {
                    continue;
                }
                TdFlowNodeBtn tdFlowNodeBtn = null;
                boolean flag = false;
                if (tempList != null && tempList.size() > 0) {
                    //判断是否已配置，已配置则直接加入
                    for (TdFlowNodeBtn btn : tempList) {
                        if (btn.getFlowBtnType().getTypeNo().equals(t.getTypeNo())) {
                            buttonList.add(btn);
                            flag = true;
                        }
                    }
                }
                //如果没有配置过，则创建一个
                if (!flag) {
                    tdFlowNodeBtn = new TdFlowNodeBtn();
                    tdFlowNodeBtn.setFlowBtnType(t);
                    tdFlowNodeBtn.setTdFlowNode(tdFlowNode);
                    tdFlowNodeBtn.setFlowBtnName(t.getTypeCN());
                    buttonList.add(tdFlowNodeBtn);
                }
            }
        }
    }

    /**
     * 保存按钮配置
     */
    public void buttonCfgSaveAction() {
        //获取选择的按钮
        String[] tempArr = JsfUtil.getRequest().getParameterValues("btnSelect");
        List<TdFlowNodeBtn> list = new ArrayList<TdFlowNodeBtn>();
        if (null != tempArr && tempArr.length > 0) {
            //遍历已选按钮，将已选的添加到list
            for (String s : tempArr) {
                for (TdFlowNodeBtn t : buttonList) {
                    if (t.getFlowBtnType().getTypeNo().equals(s)) {
                        list.add(t);
                        break;
                    }
                }
            }
        }
        //保存配置
        flowBusinessService.saveFlowNodeButton(list, tdFlowNode.getRid());
        RequestContext.getCurrentInstance().execute("PF('ButtonDialog').hide()");
        JsfUtil.addSuccessMessage("按钮配置成功！");
    }

    /**
     * 通讯方式配置初始化
     */
    public void txTypeInitAction() {
        if (flowNodeList != null && flowNodeList.size() > 0) {
            flowBusinessService.resetTxTypeCfg(flowNodeList);
            JsfUtil.addSuccessMessage("通讯方式初始化成功！");
        }
    }

    /**
     * 配置通讯方式初始化
     */
    public void txTypeCfgInit() {
        List<Object> list = flowBusinessService.findAlreadSelectTx(tdFlowNode.getRid());
        List<Integer> selist = new ArrayList<Integer>();
        if (list.size() == txtypeList.size()) {
            txselected = 1;
        } else {
            txselected = 0;
        }
        for (Object o : list) {
            selist.add(Integer.valueOf(o.toString()));
        }
        for (TsTxtype t : txtypeList) {
            if (selist.contains(t.getRid())) {
                t.setSelected((short) 1);
            } else {
                t.setSelected((short) 0);
            }
        }
    }

    /**
     * 通讯方式保存
     */
    public void txTypeCfgSaveAction() {
        String[] tempArr = JsfUtil.getRequest().getParameterValues("txSelect");
        List<TdFlowNodeTxType> list = new ArrayList<TdFlowNodeTxType>();
        TdFlowNodeTxType tdFlowNodeTxType;
        if (tempArr != null && tempArr.length > 0) {
            for (String s : tempArr) {
                tdFlowNodeTxType = new TdFlowNodeTxType();
                tdFlowNodeTxType.setTsTxtype(new TsTxtype(Integer.valueOf(s)));
                tdFlowNodeTxType.setTdFlowNode(tdFlowNode);
                list.add(tdFlowNodeTxType);
            }
        }
        flowBusinessService.saveFlowNodeTxType(list, tdFlowNode.getRid());
        JsfUtil.addSuccessMessage("通讯方式配置成功！");
    }

    public String getImpl(String impl) {
        Set<String> set = implClassMap.keySet();
        for (String s : set) {
            if (implClassMap.get(s).equals(impl)) {
                return s;
            }
        }
        return "";
    }

    /**
     * 弹出页面单选弹出框
     */
    public void selectPageAction() {
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("width", 705);
        options.put("contentWidth", 677);
        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add(null != tdFlowNode.getTdFlowNodePage() ? tdFlowNode.getTdFlowNodePage().getRid().toString() : null);
        paramMap.put("pageId", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("pageSelectList", options, paramMap);
    }

    public void onPageSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            if (selectedMap.get("tdFlowNodePage") != null) {
                tdFlowNode.setTdFlowNodePage((TdFlowNodePage) selectedMap.get("tdFlowNodePage"));
            }
        }
    }

    public void clearPage() {
        tdFlowNode.setTdFlowNodePage(null);
    }


    public void nodeScriptInitAction() {
        tdFlowNodeScript = flowBusinessService.findNodeScriptByNodeid(tdFlowNode.getRid());
        if (tdFlowNodeScript == null) {
            tdFlowNodeScript = new TdFlowNodeScript();
            tdFlowNodeScript.setTdFlowNode(tdFlowNode);
        }

    }

    public void nodeScriptSaveAction() {
        flowBusinessService.saveOrUpdateTdFlowNodeScript(tdFlowNodeScript);
        JsfUtil.addSuccessMessage("保存成功！");
        RequestContext.getCurrentInstance().execute("PF('NodeScriptDialog').hide();");
    }

    public void deleteScriptAction() {
        this.flowBusinessService.deleteTdFlowNodeScript(tdFlowNodeScript.getRid());
        JsfUtil.addSuccessMessage("清空脚本成功！");
    }

    public TreeNode getSearchTreeNode() {
        return searchTreeNode;
    }

    public void setSearchTreeNode(TreeNode searchTreeNode) {
        this.searchTreeNode = searchTreeNode;
    }

    public Integer getSearchFlowTypeId() {
        return searchFlowTypeId;
    }

    public void setSearchFlowTypeId(Integer searchFlowTypeId) {
        this.searchFlowTypeId = searchFlowTypeId;
    }

    public String getSearchFlowTypeName() {
        return searchFlowTypeName;
    }

    public void setSearchFlowTypeName(String searchFlowTypeName) {
        this.searchFlowTypeName = searchFlowTypeName;
    }

    public String getSearchFlowName() {
        return searchFlowName;
    }

    public void setSearchFlowName(String searchFlowName) {
        this.searchFlowName = searchFlowName;
    }

    public Integer getFlowTypeId() {
        return flowTypeId;
    }

    public void setFlowTypeId(Integer flowTypeId) {
        this.flowTypeId = flowTypeId;
    }

    public String getFlowTypeName() {
        return flowTypeName;
    }

    public void setFlowTypeName(String flowTypeName) {
        this.flowTypeName = flowTypeName;
    }

    public TdFlowDef getTdFlowDef() {
        return tdFlowDef;
    }

    public void setTdFlowDef(TdFlowDef tdFlowDef) {
        this.tdFlowDef = tdFlowDef;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public Map<String, Integer> getRoleMap() {
        return roleMap;
    }

    public void setRoleMap(Map<String, Integer> roleMap) {
        this.roleMap = roleMap;
    }

    public List<String> getSelectedRoles() {
        return selectedRoles;
    }

    public void setSelectedRoles(List<String> selectedRoles) {
        this.selectedRoles = selectedRoles;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getUserOfficeName() {
        return userOfficeName;
    }

    public void setUserOfficeName(String userOfficeName) {
        this.userOfficeName = userOfficeName;
    }

    public Integer getUserOfficeRid() {
        return userOfficeRid;
    }

    public void setUserOfficeRid(Integer userOfficeRid) {
        this.userOfficeRid = userOfficeRid;
    }

    public TreeNode getUserOfficeTreeNode() {
        return userOfficeTreeNode;
    }

    public void setUserOfficeTreeNode(TreeNode userOfficeTreeNode) {
        this.userOfficeTreeNode = userOfficeTreeNode;
    }

    public DualListModel getDualListModel() {
        return dualListModel;
    }

    public void setDualListModel(DualListModel dualListModel) {
        this.dualListModel = dualListModel;
    }

    public String getDefName() {
        return defName;
    }

    public void setDefName(String defName) {
        this.defName = defName;
    }

    public List<TdFlowNode> getFlowNodeList() {
        return flowNodeList;
    }

    public void setFlowNodeList(List<TdFlowNode> flowNodeList) {
        this.flowNodeList = flowNodeList;
    }

    public TdFlowNode getTdFlowNode() {
        return tdFlowNode;
    }

    public void setTdFlowNode(TdFlowNode tdFlowNode) {
        this.tdFlowNode = tdFlowNode;
    }

    public Map<String, FlowInType> getFlowInTypeMap() {
        return flowInTypeMap;
    }

    public void setFlowInTypeMap(Map<String, FlowInType> flowInTypeMap) {
        this.flowInTypeMap = flowInTypeMap;
    }

    public Map<String, DealType> getDealTypeMap() {
        return dealTypeMap;
    }

    public void setDealTypeMap(Map<String, DealType> dealTypeMap) {
        this.dealTypeMap = dealTypeMap;
    }

    public Map<String, String> getUrlMap() {
        return urlMap;
    }

    public void setUrlMap(Map<String, String> urlMap) {
        this.urlMap = urlMap;
    }

    public Map<String, String> getRuleMap() {
        return ruleMap;
    }

    public void setRuleMap(Map<String, String> ruleMap) {
        this.ruleMap = ruleMap;
    }

    public String getRuleCode() {
        return ruleCode;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public Map<String, String> getDictoryMap() {
        return dictoryMap;
    }

    public void setDictoryMap(Map<String, String> dictoryMap) {
        this.dictoryMap = dictoryMap;
    }

    public List<String> getSelectDictoryList() {
        return selectDictoryList;
    }

    public void setSelectDictoryList(List<String> selectDictoryList) {
        this.selectDictoryList = selectDictoryList;
    }

    public List<String> getDictoryList() {
        return dictoryList;
    }

    public void setDictoryList(List<String> dictoryList) {
        this.dictoryList = dictoryList;
    }

    public Map<String, String> getSelectDictoryMap() {
        return selectDictoryMap;
    }

    public void setSelectDictoryMap(Map<String, String> selectDictoryMap) {
        this.selectDictoryMap = selectDictoryMap;
    }

    public Map<String, String> getBnbNodesMap() {
        return bnbNodesMap;
    }

    public void setBnbNodesMap(Map<String, String> bnbNodesMap) {
        this.bnbNodesMap = bnbNodesMap;
    }

    public List<String> getBnbList() {
        return bnbList;
    }

    public void setBnbList(List<String> bnbList) {
        this.bnbList = bnbList;
    }

    public List<TbFlowAdvtemplate> getTemplateList() {
        return templateList;
    }

    public void setTemplateList(List<TbFlowAdvtemplate> templateList) {
        this.templateList = templateList;
    }

    public TbFlowAdvtemplate getTbFlowAdvtemplate() {
        return tbFlowAdvtemplate;
    }

    public void setTbFlowAdvtemplate(TbFlowAdvtemplate tbFlowAdvtemplate) {
        this.tbFlowAdvtemplate = tbFlowAdvtemplate;
    }

    public List<TbTempmetaDefine> getDefineList() {
        return defineList;
    }

    public void setDefineList(List<TbTempmetaDefine> defineList) {
        this.defineList = defineList;
    }

    public TbTempmetaDefine getTbTempmetaDefine() {
        return tbTempmetaDefine;
    }

    public void setTbTempmetaDefine(TbTempmetaDefine tbTempmetaDefine) {
        this.tbTempmetaDefine = tbTempmetaDefine;
    }

    public String getResolvedContent() {
        return resolvedContent;
    }

    public void setResolvedContent(String resolvedContent) {
        this.resolvedContent = resolvedContent;
    }

    public List<TsUserInfo> getStoreTargetList() {
        return storeTargetList;
    }

    public void setStoreTargetList(List<TsUserInfo> storeTargetList) {
        this.storeTargetList = storeTargetList;
    }

    public List<TbTempmetaDefine> getFilterDefineList() {
        return filterDefineList;
    }

    public void setFilterDefineList(List<TbTempmetaDefine> filterDefineList) {
        this.filterDefineList = filterDefineList;
    }

    public List<SelectItem> getFilterDefineSelectList() {
        return filterDefineSelectList;
    }

    public void setFilterDefineSelectList(List<SelectItem> filterDefineSelectList) {
        this.filterDefineSelectList = filterDefineSelectList;
    }

    public List<TdFlowNodeBtn> getButtonList() {
        return buttonList;
    }

    public void setButtonList(List<TdFlowNodeBtn> buttonList) {
        this.buttonList = buttonList;
    }

    public Integer getAllSelect() {
        return allSelect;
    }

    public void setAllSelect(Integer allSelect) {
        this.allSelect = allSelect;
    }

    public List<TsTxtype> getTxtypeList() {
        return txtypeList;
    }

    public void setTxtypeList(List<TsTxtype> txtypeList) {
        this.txtypeList = txtypeList;
    }

    public Integer getTxselected() {
        return txselected;
    }

    public void setTxselected(Integer txselected) {
        this.txselected = txselected;
    }

    public TdFlowNodeScript getTdFlowNodeScript() {
        return tdFlowNodeScript;
    }

    public void setTdFlowNodeScript(TdFlowNodeScript tdFlowNodeScript) {
        this.tdFlowNodeScript = tdFlowNodeScript;
    }

	public List<TdFlowTitle> getTitleTemplateList() {
		return titleTemplateList;
	}

	public void setTitleTemplateList(List<TdFlowTitle> titleTemplateList) {
		this.titleTemplateList = titleTemplateList;
	}

	public TdFlowTitle getTdFlowTitle() {
		return tdFlowTitle;
	}

	public void setTdFlowTitle(TdFlowTitle tdFlowTitle) {
		this.tdFlowTitle = tdFlowTitle;
	}
    
}

