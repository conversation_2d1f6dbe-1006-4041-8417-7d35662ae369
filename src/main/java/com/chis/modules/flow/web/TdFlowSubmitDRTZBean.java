package com.chis.modules.flow.web;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;

import com.alibaba.fastjson.JSON;
import com.chis.activiti.entity.TbFlowAdvtemplate;
import com.chis.activiti.entity.TdFlowNode;
import com.chis.activiti.enumn.DealType;
import com.chis.activiti.javabean.MutiNodeDealBean;
import com.chis.activiti.javabean.ResultCondition;
import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.activiti.utils.BusinessIdUtil;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.flow.logic.FlowChoiceConditionBean;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsTxtype;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.MetaCondition;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesBean;

/**
 * 选人跳转弹出框
 * 内容宽度：830，内容高度500
 * 
 * 2014-9-2 david 根据规则查询待办人入参修改
 * 
 * <AUTHOR>
 */
@ManagedBean(name = "tdFlowSubmitDRTZBean")
@ViewScoped
public class TdFlowSubmitDRTZBean extends FacesBean {

	private static final long serialVersionUID = -5927665119395504030L;

	/** 存在session中的对象 */
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	
	/**按照节点组织可选择的数据*/
	private Map<TdFlowNode, MutiNodeDealBean> dataMap;
//	/**选中人员的值,key-节点id，value-选择的人员集合*/
//	private Map<String, List<String>> selectDataMap;
	/** 流程意见 */
	private String advice;
	/** 当前节点对象 */
	private TdFlowNode tdFlowNode;
	/** 当前节点的下一步要走的节点 */
	private List<TdFlowNode> nextFlowNodeList;
	/** 常用语 */
	private List<TsSimpleCode> cyyList;
	/** 意见模板 */
	private List<TbFlowAdvtemplate> advTemplateList;
	/** 通讯方式 */
	private List<TsTxtype> txtypeList;
	/** 选择的通讯方式 */
	private List<String> selectedTxtypeList = new ArrayList<String>(0);
	
//	private List testList = new ArrayList();
	
	public TdFlowSubmitDRTZBean() {
		//传过来的条件,JSON格式
		String conJson = JsfUtil.getRequest().getParameter("condition");
		
		if(StringUtils.isNotBlank(conJson)) {
			FlowChoiceConditionBean bean = JSON.parseObject(StringUtils.decode(conJson), FlowChoiceConditionBean.class);
			
			//当前节点
			if(StringUtils.isNotBlank(bean.getCurrentFlowNodeJSON())) {
				this.tdFlowNode = flowBusinessService.findTdFlowNode(new Integer(bean.getCurrentFlowNodeJSON()));
			}

			//下一个节点
			if(StringUtils.isNotBlank(bean.getNextFlowNodeJSON())) {
				String[] split = bean.getNextFlowNodeJSON().split(",");
				this.nextFlowNodeList = new ArrayList<>();
				for(String ridstr : split)	{
					this.nextFlowNodeList.add(flowBusinessService.findTdFlowNode(new Integer(ridstr)));
				}
			}
			
			/**
			 * 设置查询条件，
			 * 流程实例ID通过任务ID去找，selectIds通过nodeId去找
			 */
			ResultCondition condition = new ResultCondition();
			condition.setUserId(this.sessionData.getUser().getRid());
			if(StringUtils.isNotBlank(bean.getAssigneeConditionJSON())) {
				condition.setJson(bean.getAssigneeConditionJSON());
			}
			this.dataMap = this.flowBusinessService.findUsersByNodes(condition, bean.getActivitiTaskId(), this.nextFlowNodeList);
//			this.selectDataMap = new HashMap<String, List<String>>();
//			for(TdFlowNode tfn: this.dataMap.keySet()) {
//				this.selectDataMap.put(tfn.getRid().toString(), new ArrayList<String>());
//			}
//			this.testList = new ArrayList(this.nextFlowNodeList.size());
//			for(int i=0; i<this.nextFlowNodeList.size(); i++) {
//				this.testList.add(new ArrayList());
//			}
			
			
			
			//初始化通讯方式
			this.txtypeList = this.flowBusinessService.findTxtypeByNodeId(this.tdFlowNode.getRid());
			
			//初始化流程意见
			this.initAdvTemplate(this.tdFlowNode.getRid(), BusinessIdUtil.parsePK(bean.getBusinessIdJSON()));
			
			//初始化常用语
			this.initCyyList();
		}else {
			throw new RuntimeException("流程配置有误，请联系管理员！");
		}
	}
	
	
	/**
	 * 初始化意见模板
	 */
	private void initAdvTemplate(Integer nodeId, String businessId) {
		//构建模板的依赖条件
		MetaCondition metaCondition = MetaConditionFactory.produceCondition(this.sessionData, businessId, null);
		//查询该节点的意见模板集合
		this.advTemplateList = this.flowBusinessService.findAdvTemplateList(nodeId, metaCondition);
		/**
		 * 初始化流程意见，如果配了流程意见的话
		 */
		if(null != this.advTemplateList && this.advTemplateList.size() > 0) {
			for(TbFlowAdvtemplate adv : this.advTemplateList) {
				if(adv.getIsDefault().intValue() == 1) {
					this.advice = adv.getAfterAnalyContent();
					return;
				}
			}
		}
	}
	
	/**
	 * 初始化常用语
	 */
	private void initCyyList() {
		this.cyyList = this.commService.findSimpleCodesByTypeId("1101");
	}
	
	/**
	 * 确定
	 */
	public void personSelectAction() {
		Map<String, Object> map = new HashMap<String, Object>();
		
		if(this.tdFlowNode.getIfAdv().intValue() == 1) {
			if(StringUtils.isBlank(this.advice)) {
				JsfUtil.addErrorMessage("请填写流程意见！");
				return;
			}
		}
		
		//选择的待办人员
		List<TsUserInfo> selectedUserList = new ArrayList<TsUserInfo>();
		
		Set<TdFlowNode> nodeSet = this.dataMap.keySet();
		
		if(null == this.tdFlowNode.getMulNodeRule() || this.tdFlowNode.getMulNodeRule().intValue() == 0) {
			//都必选
			for(TdFlowNode node : nodeSet) {
				MutiNodeDealBean dealBean = this.dataMap.get(node);
				if(null == dealBean.getSelectUserIdsList() || dealBean.getSelectUserIdsList().size() == 0) {
					JsfUtil.addErrorMessage("请选择节点【"+node.getNodeName()+"】的待办人员！");
					return;
				}else {
					if (node.getDealType().equals(DealType.YBJD)) {
						// 一般跳转
						map.put(node.getActNodeId() + "_assignees", StringUtils.list2string(dealBean.getSelectUserIdsList(), ","));
					} else {
						// 会签节点
						map.put(node.getActNodeId() + "_multiUserList", dealBean.getSelectUserIdsList());
					}

					for (String userId : dealBean.getSelectUserIdsList()) {
						TsUserInfo selectUser = this.findCacheUserById(userId, dealBean.getUserList());
						if (!selectedUserList.contains(selectUser)) {
							selectedUserList.add(selectUser);
						}
					}			
				}
			}
		}else if (this.tdFlowNode.getMulNodeRule().intValue() == 1) {
			//至少选择1个就行
			boolean hasChooseUser = false;
			for(TdFlowNode node : nodeSet) {
				MutiNodeDealBean dealBean = this.dataMap.get(node);
				if(null != dealBean.getSelectUserIdsList() && dealBean.getSelectUserIdsList().size() > 0) {
					hasChooseUser = true;
					if (node.getDealType().equals(DealType.YBJD)) {
						// 一般跳转
						map.put(node.getActNodeId() + "_assignees", StringUtils.list2string(dealBean.getSelectUserIdsList(), ","));
					} else {
						// 会签节点
						map.put(node.getActNodeId() + "_multiUserList", dealBean.getSelectUserIdsList());
					}

					for (String userId : dealBean.getSelectUserIdsList()) {
						TsUserInfo selectUser = this.findCacheUserById(userId, dealBean.getUserList());
						if (!selectedUserList.contains(selectUser)) {
							selectedUserList.add(selectUser);
						}
					}	
				}else {
					if (node.getDealType().equals(DealType.YBJD)) {
						// 一般跳转
						map.put(node.getActNodeId() + "_assignees", "");
					} else {
						// 会签节点
						map.put(node.getActNodeId() + "_multiUserList", null);
					}
				}
			}
			
			if(!hasChooseUser) {
				JsfUtil.addErrorMessage("请至少选择1名待办人员！");
				return;
			}
			
		}else {
			throw new RuntimeException("流程配置有误，请联系管理员！");
		}
		

		
		map.put("selectedUserList", selectedUserList);
		map.put("advice", this.advice);
		
		//通讯方式
		List<TsTxtype> txList = new ArrayList<TsTxtype>();
		if(null != this.selectedTxtypeList && this.selectedTxtypeList.size() > 0) {
			for(String typeId : this.selectedTxtypeList) {
				for(TsTxtype tx : this.txtypeList) {
					if(tx.getRid().toString().equals(typeId)) {
						txList.add(tx);
						continue;
					}
				}
			}
		}
		map.put("selectedTxtypeList", txList);
		
		RequestContext.getCurrentInstance().closeDialog(map);		
	}
	
	/**
	 * 根据用户ID从集合中查找用户对象
	 * @param nodeId 用户ID 
	 * @return 用户对象
	 */
	private TsUserInfo findCacheUserById(String userId, List<TsUserInfo> cacheList) {
		for(TsUserInfo user : cacheList) {
			if(userId.equals(user.getRid().toString())) {
				return user;
			}
		}
		return null;
	}

	/**
	 * 关闭
	 */
	public void dialogClose() {
		RequestContext.getCurrentInstance().closeDialog(null);
	}
	
	public String getAdvice() {
		return advice;
	}

	public void setAdvice(String advice) {
		this.advice = advice;
	}

	public List<TsSimpleCode> getCyyList() {
		return cyyList;
	}

	public void setCyyList(List<TsSimpleCode> cyyList) {
		this.cyyList = cyyList;
	}

	public List<TsTxtype> getTxtypeList() {
		return txtypeList;
	}

	public void setTxtypeList(List<TsTxtype> txtypeList) {
		this.txtypeList = txtypeList;
	}

	public List<String> getSelectedTxtypeList() {
		return selectedTxtypeList;
	}

	public void setSelectedTxtypeList(List<String> selectedTxtypeList) {
		this.selectedTxtypeList = selectedTxtypeList;
	}

	public List<TbFlowAdvtemplate> getAdvTemplateList() {
		return advTemplateList;
	}

	public void setAdvTemplateList(List<TbFlowAdvtemplate> advTemplateList) {
		this.advTemplateList = advTemplateList;
	}

	public TdFlowNode getTdFlowNode() {
		return tdFlowNode;
	}

	public void setTdFlowNode(TdFlowNode tdFlowNode) {
		this.tdFlowNode = tdFlowNode;
	}

	public Map<TdFlowNode, MutiNodeDealBean> getDataMap() {
		return dataMap;
	}

	public void setDataMap(Map<TdFlowNode, MutiNodeDealBean> dataMap) {
		this.dataMap = dataMap;
	}

}
