package com.chis.modules.flow.web;

import java.util.ArrayList;
import java.util.List;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.activiti.service.impl.FlowBusinessServiceImpl;
import com.chis.common.utils.EnumUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.system.enumn.TxType;
import com.chis.modules.system.web.FacesBean;

/**
 * 流程历史审批记录
 * <AUTHOR>
 */
@ManagedBean(name = "tdFlowHisTaskBean")
@ViewScoped
public class TdFlowHisTaskBean extends FacesBean{

	private static final long serialVersionUID = -5927665119395504030L;
	private List<Object[]> dataList = new ArrayList<Object[]>(0);
	private FlowBusinessServiceImpl flowBusinessService = SpringContextHolder.getBean(FlowBusinessServiceImpl.class);
	
	private static final String SERVLET = "/FlowPicServlet";
	private String servletUrl;

	public TdFlowHisTaskBean() {
		String taskId = JsfUtil.getRequest().getParameter("taskId");
		String processId = JsfUtil.getRequest().getParameter("processId");
		if(StringUtils.isNotBlank(taskId)) {
			this.dataList = this.flowBusinessService.findHisTaskRecord(taskId, 1);
			this.servletUrl = SERVLET + "?taskId=" + taskId;
		}else if(StringUtils.isNotBlank(processId)) {
			this.dataList = this.flowBusinessService.findHisTaskRecord(processId, 2);
			this.servletUrl = SERVLET + "?processId=" + processId;
		}
		
		/**
		 *  0-节点名称 1-处理人员姓名 2-处理时间 3-处理意见 4-任务ID <br/>
		 *  5-处理人员ID 6-通讯方式 7发送状态 8接收状态 9接收时间 10-未接听原因 <br/>		
		 */
		if(null != this.dataList && this.dataList.size() > 0) {
			for(Object[] o : this.dataList) {
				if(null != o[6]) {
					TxType txType = (TxType) EnumUtils.findEnum(TxType.class, o[6]);
					o[6] = txType.getTypeCN();
					if(null != o[7]) {
						// 发送成功
						if ("1".equals(o[7].toString())) {
							o[7] = "发送成功";
						}else {
							o[7] = "发送失败";
						}						
					}
					if(null != o[8]) {
						// 发送成功
						if ("1".equals(o[8].toString())) {
							o[8] = "已接收";
						}else if ("0".equals(o[8].toString())) {
							o[8] = o[10];
						}else {
							o[8] = "未接收";
						}						
					}
				}
			}
		}
	}

	public List<Object[]> getDataList() {
		return dataList;
	}

	public void setDataList(List<Object[]> dataList) {
		this.dataList = dataList;
	}

	public String getServletUrl() {
		return servletUrl;
	}

	public void setServletUrl(String servletUrl) {
		this.servletUrl = servletUrl;
	}
}
