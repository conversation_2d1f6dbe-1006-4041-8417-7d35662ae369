package com.chis.modules.flow.plugin;

import java.util.HashSet;
import java.util.Set;

import com.chis.activiti.entity.TdFlowNodePage;
import com.chis.modules.system.enumn.SystemType;

/**
 * 流程节点页面插件
 * <AUTHOR> 2014-01-08
 */
public class FlowNodePagePluginObj {

	public static Set<TdFlowNodePage> tdFlowNodePageSet;

	static {
		tdFlowNodePageSet = new HashSet<TdFlowNodePage>();

		tdFlowNodePageSet.add(new TdFlowNodePage(SystemType.COMM,"sys_dynaform","系统-动态表单","/webapp/flow/dynaForm.xhtml",1));


	}
}
