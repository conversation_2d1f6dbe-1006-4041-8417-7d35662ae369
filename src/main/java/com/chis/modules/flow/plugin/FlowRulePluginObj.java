package com.chis.modules.flow.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.activiti.entity.TdFlowRule;

/**
 * 流程菜单的插件
 * <AUTHOR>
 */
public class FlowRulePluginObj {

	public static Set<TdFlowRule> dataSet;
	
	/**
	 * 增加流程规则
	 */
	static {
		dataSet = new HashSet<TdFlowRule>();
		dataSet.add(new TdFlowRule("1000", "com.chis.activiti.service.rule.impl.ActivitiRuleTestServiceImpl", "测试规则", new Date(), 1));
		dataSet.add(new TdFlowRule("1001", "com.chis.activiti.service.rule.impl.ActivitiRuleOfficeServiceImpl", "按科室分配", new Date(), 1));
		dataSet.add(new TdFlowRule("1002", "com.chis.activiti.service.rule.impl.ActivitiRuleDutyServiceImpl", "按职务分配", new Date(), 1));
		dataSet.add(new TdFlowRule("1003", "com.chis.activiti.service.rule.impl.ActivitiRuleStarterServiceImpl", "流程发起者", new Date(), 1));
		dataSet.add(new TdFlowRule("1004", "com.chis.activiti.service.rule.impl.ActivitiRuleSomeStepServiceImpl", "和某步处理者相同", new Date(), 1));
		dataSet.add(new TdFlowRule("1005", "com.chis.activiti.service.rule.impl.ActivitiRuleLastStepServiceImpl", "和前一步处理者相同", new Date(), 1));
		dataSet.add(new TdFlowRule("1006", "com.chis.activiti.service.rule.impl.ActivitiRuleStarterLeaderServiceImpl", "流程发起者的领导", new Date(), 1));
		dataSet.add(new TdFlowRule("1007", "com.chis.activiti.service.rule.impl.ActivitiRuleSomeStepLeaderServiceImpl", "某步处理者的领导", new Date(), 1));
		dataSet.add(new TdFlowRule("1008", "com.chis.activiti.service.rule.impl.ActivitiRuleLastStepLeaderServiceImpl", "前一步处理者的领导", new Date(), 1));
		dataSet.add(new TdFlowRule("1009", "com.chis.activiti.service.rule.impl.ActivitiRuleStarterSameOfficeServiceImpl", "与流程发起者相同部门的人", new Date(), 1));
		dataSet.add(new TdFlowRule("1010", "com.chis.activiti.service.rule.impl.ActivitiRuleSomeStepSameOfficeServiceImpl", "与某步处理者相同部门的人", new Date(), 1));
		dataSet.add(new TdFlowRule("1011", "com.chis.activiti.service.rule.impl.ActivitiRuleLastStepSameOfficeServiceImpl", "与前一步处理者相同部门的人", new Date(), 1));
		dataSet.add(new TdFlowRule("1012", "com.chis.activiti.service.rule.impl.ActivitiRuleUserGroupServiceImpl", "按用户组分配", new Date(), 1));
		dataSet.add(new TdFlowRule("1013", "com.chis.activiti.service.rule.impl.ActivitiRuleBigLeaderServiceImpl", "分管领导", new Date(), 1));
		dataSet.add(new TdFlowRule("1014", "com.chis.activiti.service.rule.impl.ActivitiRuleOfficeLeaderServiceImpl", "科室负责人", new Date(), 1));
		dataSet.add(new TdFlowRule("1015", "com.chis.activiti.service.rule.impl.ActivitiRuleStarterEmergOfficeServiceImpl", "本单位的应急办成员", new Date(), 1));
		dataSet.add(new TdFlowRule("1016", "com.chis.activiti.service.rule.impl.ActivitiRuleStarterEmergGroupLeaderServiceImpl", "本单位应急各处置组的领导", new Date(), 1));
		
		dataSet.add(new TdFlowRule("1017", "com.chis.activiti.service.rule.impl.ActivitiRuleSomeStepBigLeaderServiceImpl", "某步处理者的分管领导", new Date(), 1));
		dataSet.add(new TdFlowRule("1018", "com.chis.activiti.service.rule.impl.ActivitiRuleStarterOfficeLeaderServiceImpl", "流程发起者所在部门的领导", new Date(), 1));
		dataSet.add(new TdFlowRule("1019", "com.chis.activiti.service.rule.impl.ActivitiRuleSomeStepOfficeLeaderServiceImpl", "某步处理者所在部门的领导", new Date(), 1));
	}
}
