package com.chis.modules.flow.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.enumn.SystemType;

/**
 * 码表的插件
 * 码表编码是写死的
 * <AUTHOR>
 */
public class TsCodePluginObj {

	public static Set<TsCodeType> codeTypeSet;
	public static Set<TsSimpleCode> simpleCodeSet;

	static {
		codeTypeSet = new HashSet<TsCodeType>();
		codeTypeSet.add(new TsCodeType(SystemType.FLOW, "1101", "流程常用语", (short)1, (short)0));
		
        simpleCodeSet = new HashSet<TsSimpleCode>();
        simpleCodeSet.add(new TsSimpleCode("1001","同意","","1001", "ty",new Date(), 1, "1101"));
        simpleCodeSet.add(new TsSimpleCode("1002","不同意","","1002", "bty",new Date(), 1, "1101"));
	}

}
