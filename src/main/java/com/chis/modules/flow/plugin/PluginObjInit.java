package com.chis.modules.flow.plugin;

import java.util.List;
import java.util.Set;

import javax.persistence.Query;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.chis.activiti.entity.TdFlowNodePage;
import com.chis.activiti.entity.TdFlowRule;
import com.chis.modules.system.entity.TbTempmetaDefine;
import com.chis.modules.system.entity.TsCodeRule;
import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsMenu;
import com.chis.modules.system.entity.TsMenuBtn;
import com.chis.modules.system.entity.TsQuartz;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsSystemParam;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.init.PluginInit;

@Component(value = "PluginObjInit_FLOW_504")
@Transactional(readOnly = false)
public class PluginObjInit extends PluginInit {

	@Override
	public void run() {
		super.initDataStructureByXml();
		super.run();
		this.initFlowRule();
		this.initTemplateDefine();
		this.initTsMenuBtn();
		this.initFlowNodePage();
	}
	
	private void initFlowNodePage() {
		Set<TdFlowNodePage> set = FlowNodePagePluginObj.tdFlowNodePageSet;
		if (null != set && set.size() > 0) {
			for (TdFlowNodePage t : set) {
				List<TdFlowNodePage> list = super.em.createNamedQuery("TdFlowNodePage.findByPageCode")
						.setParameter("pagecode", t.getPageCode()).getResultList();
				if (null == list || list.size() == 0) {
					pluginUpdateService.save(t);
				}
			}
		}
	}

	private void initFlowRule() {
		Set<TdFlowRule> dataSet = FlowRulePluginObj.dataSet;
		if (null != dataSet && dataSet.size() > 0) {
			for (TdFlowRule t : dataSet) {
				Query query = em.createNamedQuery("TdFlowRule.findRuleByCode");
				query.setParameter("idcode", t.getIdcode());
				List<TdFlowRule> list = query.getResultList();
				if (null == list || list.size() == 0) {
					pluginUpdateService.save(t);
				}
			}
		}
	}

	private void initTemplateDefine() {
		Set<TbTempmetaDefine> dataSet = TempmetaDefinePluginObj.dataSet;
		if (null != dataSet && !dataSet.isEmpty()) {
			for (TbTempmetaDefine t : dataSet) {
				List<TbTempmetaDefine> list = super.em.createNamedQuery("TbTempmetaDefine.findByMetaName")
						.setParameter("metaName", t.getMetaName()).getResultList();
				if (null == list || list.isEmpty()) {
					pluginUpdateService.save(t);
				}
			}
		}
	}

	/**
	 * 初始化菜单按钮
	 * 
	 * <AUTHOR>
	 * @createDate 2015-05-25
	 */
	private void initTsMenuBtn() {
		Set<TsMenuBtn> set = MenuBtnPluginObj.menuSet;
		if (null != set && set.size() > 0) {
			for (TsMenuBtn t : set) {
				List<TsQuartz> list = em.createNamedQuery("TsMenuBtn.findByCode")
						.setParameter("buttoncode", t.getBtnCode()).getResultList();
				if (null == list || list.size() == 0) {
					TsMenu tsMenu = (TsMenu) em.createNamedQuery("TsMenu.findByMenuEn")
							.setParameter("menuEn", t.getMenucode()).getSingleResult();
					if (tsMenu != null) {
						t.setTsMenu(tsMenu);
						t.setLevelno("1");
						t.setIfReveal((short) 1);
						pluginUpdateService.save(t);
					}
				}
			}
		}
	}
	
	@Override
	public List<String> buildDataStructurePlugin() {
		return null;
	}

	@Override
	public Set<TsMenu> buildMenuPlugin() {
		return MenuPluginObj.menuSet;
	}

	@Override
	public Set<TsCodeRule> buildCodeRulePlugin() {
		return CodeRulePluginObj.ruleSet;
	}

	@Override
	public Set<TsSystemParam> buildSystemParamPlugin() {
		return SystemParamPluginObj.paramSet;
	}

	@Override
	public Set<TsSimpleCode> buildCodePlugin() {
		return TsCodePluginObj.simpleCodeSet;
	}

	@Override
	public Set<TsCodeType> buildCodeTypePlugin() {
		return TsCodePluginObj.codeTypeSet;
	}

	@Override
	public SystemType buildSystemType() {
		return SystemType.FLOW;
	}
}
