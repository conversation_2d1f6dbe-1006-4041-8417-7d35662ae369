package com.chis.modules.flow.plugin;

import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsMenuBtn;

/**
 * 菜单按钮的插件
 * <AUTHOR>
 */
public class MenuBtnPluginObj {

	public static Set<TsMenuBtn> menuSet;
	
	static {
		menuSet = new HashSet<TsMenuBtn>();
			
		//其中第一个参数,用来配置菜单级别，主要根据第二个参数来比较判断
	
//		menuSet.add(new TsMenuBtn("flow_ybrw","oa_ybrw_cxBtn","撤销"));
//		menuSet.add(new TsMenuBtn("flow_ybrw","oa_ybrw_jjqBtn","加减处理人"));
		
    }
	
}