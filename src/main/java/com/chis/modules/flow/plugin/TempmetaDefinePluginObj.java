package com.chis.modules.flow.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TbTempmetaDefine;
import com.chis.modules.system.enumn.SystemType;

/**
 * 流程菜单的插件
 * <AUTHOR>
 */
public class TempmetaDefinePluginObj {

	public static Set<TbTempmetaDefine> dataSet;
	
	/**
	 * 增加流程规则
	 */
	static {
		dataSet = new HashSet<TbTempmetaDefine>();
		
		dataSet.add(new TbTempmetaDefine(SystemType.COMM, "1001", "【当前登录人】", 
				"com.chis.ejb.tempmeta.comm.TempmetaResolve4Username",
				"系统当前登录人，如：张三", new Date(), 1, "sys_username"));
		
		dataSet.add(new TbTempmetaDefine(SystemType.COMM, "1002", "【当前单位】", 
				"com.chis.ejb.tempmeta.comm.TempmetaResolve4Unitname",
				"系统当前登录人所在的单位，如：无锡市疾病预防控制中心", new Date(), 1, "sys_current_unitname"));
		
		dataSet.add(new TbTempmetaDefine(SystemType.COMM, "1003", "【当前日期】", 
				"com.chis.ejb.tempmeta.comm.TempmetaResolve4Date",
				"系统当前日期，如：2014年11月28日", new Date(), 1, "sys_date_cn"));
		
		dataSet.add(new TbTempmetaDefine(SystemType.COMM, "1004", "【当前年份】", 
				"com.chis.ejb.tempmeta.comm.TempmetaResolve4Year",
				"系统当前年份，如：2014年", new Date(), 1, "sys_year_cn"));
		
		dataSet.add(new TbTempmetaDefine(SystemType.COMM, "1005", "【当前单位简称】", 
				"com.chis.ejb.tempmeta.comm.TempmetaResolve4UnitSimplename",
				"系统当前登录人所在的单位的简称，如：无锡市疾控", new Date(), 1, "sys_current_unitSimpleName"));
		
		dataSet.add(new TbTempmetaDefine(SystemType.COMM, "1006", "【当前单位的简称签名】", 
				"com.chis.ejb.tempmeta.comm.TempmetaResolve4SignUnitName",
				"系统当前登录人所在的单位的简称，如：【无锡市疾控】", new Date(), 1, "sys_current_unitSimpleName4Sign"));
		
		dataSet.add(new TbTempmetaDefine(SystemType.COMM, "1007", "【当前流程名称】", 
				"com.chis.modules.flow.tempmeta.TempmetaResolve4DefName",
				"获取当前流程的名称，如：请假", new Date(), 1, "sys_current_flowname"));	
		
		dataSet.add(new TbTempmetaDefine(SystemType.COMM, "1008", "【当前流程类型名称】", 
				"com.chis.modules.flow.tempmeta.TempmetaResolve4DefTypeName",
				"获取当前流程类型的名称，如：行政办公", new Date(), 1, "sys_current_flowtypename"));	
	}
}
