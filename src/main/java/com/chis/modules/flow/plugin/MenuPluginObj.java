package com.chis.modules.flow.plugin;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.chis.modules.system.entity.TsMenu;

/**
 * 流程菜单的插件
 * <AUTHOR>
 */
public class MenuPluginObj {

	public static Set<TsMenu> menuSet;
	/**
	 * 菜单默认图标
	 */
	private static final String DEFAULT_MENU_ICON = "default.png";
	
	static {
		menuSet = new HashSet<TsMenu>();

        /**
         * 创建2014-06-23 zww
         */
//        menuSet.add(new TsMenu("005","流程管理","flow_lcgl","流程管理",Short.valueOf("0"),null,DEFAULT_MENU_ICON, new Date(), 1, 1));
//        menuSet.add(new TsMenu("005.001","流程部署","flow_lcbs","流程部署",Short.valueOf("1"),"/webapp/flow/tdFlowDefList.faces",DEFAULT_MENU_ICON, new Date(), 1, 2));
//        menuSet.add(new TsMenu("005.002","发起流程","flow_fqlc","发起流程",Short.valueOf("1"),"/webapp/flow/tdFlowInstanceList.faces",DEFAULT_MENU_ICON, new Date(), 1, 3));
//        menuSet.add(new TsMenu("005.003","待办任务","flow_dbrw","待办任务",Short.valueOf("1"),"/webapp/flow/tdFlowTaskList.faces",DEFAULT_MENU_ICON, new Date(), 1, 4));
//        menuSet.add(new TsMenu("005.004","流程追踪","flow_lczz","流程追踪",Short.valueOf("1"),"/webapp/flow/tdFlowDebugList.faces",DEFAULT_MENU_ICON, new Date(), 1, 5));
//        menuSet.add(new TsMenu("005.005","已办任务","flow_ybrw","已办任务",Short.valueOf("1"),"/webapp/flow/tdFlowHandledTaskList.faces",DEFAULT_MENU_ICON, new Date(), 1, 6));
//        menuSet.add(new TsMenu("005.006","流程类型维护","flow_lclxwh","流程类型维护",Short.valueOf("1"),"/webapp/flow/tdFlowTypeList.faces",DEFAULT_MENU_ICON, new Date(), 1, 7));
//        menuSet.add(new TsMenu("005.008","流程委托","flow_lcwt","流程委托",Short.valueOf("1"),"/webapp/flow/tdFlowTrustList.faces",DEFAULT_MENU_ICON, new Date(), 1, 8));
	}
	
}
