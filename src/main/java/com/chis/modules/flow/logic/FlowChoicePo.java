package com.chis.modules.flow.logic;

import java.io.Serializable;

public class FlowChoicePo implements Serializable{

	private static final long serialVersionUID = -3954975889386643965L;
	/**当前节点信息*/
	private Integer cn_rid;
	private Short cn_ifAdv;
	
	/**activiti当前任务ID*/
	private String activitiTaskId;
	/**当前业务主键的json*/
	private String businessIdJSON;
	
	/**业务模块有可能提供规则的查询条件，形式为json*/
	private String assigneeConditionJSON;
	
	public FlowChoicePo() {
		
	}

}
