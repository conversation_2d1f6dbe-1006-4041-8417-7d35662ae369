package com.chis.modules.flow.logic;

import java.io.Serializable;
import java.sql.Clob;
import java.util.Date;

public class FrptTestJavaBean implements Serializable{

	private static final long serialVersionUID = 2684722503296720127L;
	private String username;
	private String imgUrl;
	private Clob clob;
	private Integer age;
	private Date birthDate;
	
	public FrptTestJavaBean() {
	}
	public FrptTestJavaBean(String username, String imgUrl, Integer age, Date birthDate) {
		this.username = username;
		this.imgUrl = imgUrl;
		this.age = age;
		this.birthDate = birthDate;
	}
	
	public Clob getClob() {
		return clob;
	}
	public void setClob(Clob clob) {
		this.clob = clob;
	}
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	public String getImgUrl() {
		return imgUrl;
	}
	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}
	public Integer getAge() {
		return age;
	}
	public void setAge(Integer age) {
		this.age = age;
	}
	public Date getBirthDate() {
		return birthDate;
	}
	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}
}
