package com.chis.modules.flow.logic;

import java.io.Serializable;

/**
 * 流程追踪-查询条件
 * <AUTHOR>
 * @create 2015年2月14日 上午10:11:42
 */
public class TdFlowDebugCondBean implements Serializable{

	private static final long serialVersionUID = 9066846906205892641L;
	/** 查询条件：流程类型选择ID */
	private Integer searchFlowTypeId;
	/** 查询条件：流程类型名称 */
	private String searchFlowTypeName;
	/** 查询条件：任务名称 */
	private String searchTaskName;
	/** 查询条件：流程状态0-未结束 1-结束 2-全部 */
	private int searchFlowState;
	/** 查询条件：是否是我发起的流程 */
	private boolean searchMyFlow = Boolean.FALSE;
	
	public TdFlowDebugCondBean() {
	}
	
	public Integer getSearchFlowTypeId() {
		return searchFlowTypeId;
	}
	public void setSearchFlowTypeId(Integer searchFlowTypeId) {
		this.searchFlowTypeId = searchFlowTypeId;
	}
	public String getSearchFlowTypeName() {
		return searchFlowTypeName;
	}
	public void setSearchFlowTypeName(String searchFlowTypeName) {
		this.searchFlowTypeName = searchFlowTypeName;
	}
	public String getSearchTaskName() {
		return searchTaskName;
	}
	public void setSearchTaskName(String searchTaskName) {
		this.searchTaskName = searchTaskName;
	}
	public int getSearchFlowState() {
		return searchFlowState;
	}
	public void setSearchFlowState(int searchFlowState) {
		this.searchFlowState = searchFlowState;
	}
	public boolean isSearchMyFlow() {
		return searchMyFlow;
	}
	public void setSearchMyFlow(boolean searchMyFlow) {
		this.searchMyFlow = searchMyFlow;
	}
}
