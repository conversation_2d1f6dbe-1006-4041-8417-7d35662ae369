package com.chis.modules.flow.logic;

import java.io.Serializable;

public class FlowDefDisPlayBean implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3663674138792875489L;
	private String name;
	private boolean selected;
	private Integer level;
	private Integer rid;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}

	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

}
