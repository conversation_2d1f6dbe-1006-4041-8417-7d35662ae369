package com.chis.modules.flow.logic;

import java.io.Serializable;

import com.chis.activiti.enumn.SupportAssigeeType;

/**
 * 流程提交下一步，选择待办人的初始化条件
 * <AUTHOR> 2014-12-20
 */
public class FlowChoiceConditionBean implements Serializable{

	private static final long serialVersionUID = 3047205413579238896L;
	
	/**流程定义ID*/
	private String flowDefId;
	/**当前节点对象的json，对应的类是TdFlowNode*/
	private String currentFlowNodeJSON;
	/**下一步要走的节点集合的json，对应的类型单个节点就是TdFlowNode，多个是List<TdFlowNode>*/
	private String nextFlowNodeJSON;
	/**activiti当前任务ID*/
	private String activitiTaskId;
	/**当前业务主键的json*/
	private String businessIdJSON;
	/**业务模块有可能提供规则的查询条件，形式为json*/
	private String assigneeConditionJSON;
	/**业务模块提供的待办人,对应的类型是List<TsUserInfo>*/
	private String assigeesJSON;
	/**业务模块提供的待办人与规则提供的待办人最后要形成集合的关系，如或、且、只用业务模块的待办人*/
	private SupportAssigeeType assigeeType; 
	
    public FlowChoiceConditionBean() {
    }

	public String getFlowDefId() {
		return flowDefId;
	}

	public void setFlowDefId(String flowDefId) {
		this.flowDefId = flowDefId;
	}

	public String getCurrentFlowNodeJSON() {
		return currentFlowNodeJSON;
	}

	public void setCurrentFlowNodeJSON(String currentFlowNodeJSON) {
		this.currentFlowNodeJSON = currentFlowNodeJSON;
	}

	public String getNextFlowNodeJSON() {
		return nextFlowNodeJSON;
	}

	public void setNextFlowNodeJSON(String nextFlowNodeJSON) {
		this.nextFlowNodeJSON = nextFlowNodeJSON;
	}

	public String getActivitiTaskId() {
		return activitiTaskId;
	}

	public void setActivitiTaskId(String activitiTaskId) {
		this.activitiTaskId = activitiTaskId;
	}

	public String getBusinessIdJSON() {
		return businessIdJSON;
	}

	public void setBusinessIdJSON(String businessIdJSON) {
		this.businessIdJSON = businessIdJSON;
	}

	public String getAssigneeConditionJSON() {
		return assigneeConditionJSON;
	}

	public void setAssigneeConditionJSON(String assigneeConditionJSON) {
		this.assigneeConditionJSON = assigneeConditionJSON;
	}

	public String getAssigeesJSON() {
		return assigeesJSON;
	}

	public void setAssigeesJSON(String assigeesJSON) {
		this.assigeesJSON = assigeesJSON;
	}

	public SupportAssigeeType getAssigeeType() {
		return assigeeType;
	}

	public void setAssigeeType(SupportAssigeeType assigeeType) {
		this.assigeeType = assigeeType;
	}
}
