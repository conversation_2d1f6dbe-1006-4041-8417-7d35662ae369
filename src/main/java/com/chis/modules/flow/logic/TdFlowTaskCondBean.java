package com.chis.modules.flow.logic;

import java.io.Serializable;
import java.util.Date;

/**
 * 待办任务-查询条件
 * <AUTHOR>
 * @create 2015年2月14日 上午10:11:42
 */
public class TdFlowTaskCondBean implements Serializable{

	private static final long serialVersionUID = -2374950419309824378L;
	/** 查询条件：流程类型选择ID，用于缓存列表页面 */
	private Integer searchFlowTypeId;
	/** 查询条件：流程类型名称，用于缓存列表页面 */
	private String searchFlowTypeName;
	/** 查询条件：任务名称，用于缓存列表页面 */
	private String searchTaskName;
	/** 查询条件：流程发起时间段，用于缓存列表页面 */
	private Date searchStartTime;
	/** 查询条件：流程发起时间段，用于缓存列表页面 */
	private Date searchEndTime;
	
	public TdFlowTaskCondBean() {
	}

	public Integer getSearchFlowTypeId() {
		return searchFlowTypeId;
	}

	public void setSearchFlowTypeId(Integer searchFlowTypeId) {
		this.searchFlowTypeId = searchFlowTypeId;
	}

	public String getSearchFlowTypeName() {
		return searchFlowTypeName;
	}

	public void setSearchFlowTypeName(String searchFlowTypeName) {
		this.searchFlowTypeName = searchFlowTypeName;
	}

	public String getSearchTaskName() {
		return searchTaskName;
	}

	public void setSearchTaskName(String searchTaskName) {
		this.searchTaskName = searchTaskName;
	}

	public Date getSearchStartTime() {
		return searchStartTime;
	}

	public void setSearchStartTime(Date searchStartTime) {
		this.searchStartTime = searchStartTime;
	}

	public Date getSearchEndTime() {
		return searchEndTime;
	}

	public void setSearchEndTime(Date searchEndTime) {
		this.searchEndTime = searchEndTime;
	}
}
