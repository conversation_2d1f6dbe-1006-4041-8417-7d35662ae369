package com.chis.modules.flow.rptvo;

import java.io.Serializable;
import java.sql.Clob;
import java.util.Date;

import com.chis.modules.system.rptvo.ZwxFieldDoc;

/**
 * 流程节点的处理信息
 * <AUTHOR> 2015-02-06
 */
public class NodeDealVo implements Serializable{

	private static final long serialVersionUID = -2940599314769171839L;
	@ZwxFieldDoc(value="节点名称")
	private String nodeName;
	@ZwxFieldDoc(value="节点处理人的ID")
	private String userId;
	@ZwxFieldDoc(value="节点处理人")
	private String username;
	@ZwxFieldDoc(value="节点处理人所在科室")
	private String officeName;
	@ZwxFieldDoc(value="节点处理人所在单位名称")
	private String unitName;
	@ZwxFieldDoc(value="节点处理人所在单位简称")
	private String unitSimpleName;
	@ZwxFieldDoc(value="节点处理人的联系电话")
	private String mobilePhone;
	@ZwxFieldDoc(value="节点处理人的签名")
	private Clob signImg;
	@ZwxFieldDoc(value="节点处理人的处理意见")
	private String advice;
	@ZwxFieldDoc(value="处理时间")
	private Date dealTime;
	@ZwxFieldDoc(value="任务节点的KEY")
	private String taskDefKey;
	
	public NodeDealVo() {
	}

	public String getNodeName() {
		return nodeName;
	}

	public void setNodeName(String nodeName) {
		this.nodeName = nodeName;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public String getUnitSimpleName() {
		return unitSimpleName;
	}

	public void setUnitSimpleName(String unitSimpleName) {
		this.unitSimpleName = unitSimpleName;
	}

	public String getMobilePhone() {
		return mobilePhone;
	}

	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

	public Clob getSignImg() {
		return signImg;
	}

	public void setSignImg(Clob signImg) {
		this.signImg = signImg;
	}

	public String getAdvice() {
		return advice;
	}

	public void setAdvice(String advice) {
		this.advice = advice;
	}

	public Date getDealTime() {
		return dealTime;
	}

	public void setDealTime(Date dealTime) {
		this.dealTime = dealTime;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getTaskDefKey() {
		return taskDefKey;
	}

	public void setTaskDefKey(String taskDefKey) {
		this.taskDefKey = taskDefKey;
	}
	
}
