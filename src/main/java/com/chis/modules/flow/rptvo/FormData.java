package com.chis.modules.flow.rptvo;

import java.io.Serializable;

public class FormData implements Serializable{

	private static final long serialVersionUID = 1L;
	private String name0;
	private Object value0;
	private Object valuechange0;
	private String name1;
	private Object value1;
	private Object valuechange1;
	private String name2;
	private Object value2;
	private Object valuechange2;
	private String name3;
	private Object value3;
	private Object valuechange3;
	private String name4;
	private Object value4;
	private Object valuechange4;
	private String name5;
	private Object value5;
	private Object valuechange5;
	private String name6;
	private Object value6;
	private Object valuechange6;
	private String name7;
	private Object value7;
	private Object valuechange7;
	private String name8;
	private Object value8;
	private Object valuechange8;
	private String name9;
	private Object value9;
	private Object valuechange9;
	private String name10;
	private Object value10;
	private Object valuechange10;
	private String name11;
	private Object value11;
	private Object valuechange11;
	private String name12;
	private Object value12;
	private Object valuechange12;
	private String name13;
	private Object value13;
	private Object valuechange13;
	private String name14;
	private Object value14;
	private Object valuechange14;
	private String name15;
	private Object value15;
	private Object valuechange15;
	private String name16;
	private Object value16;
	private Object valuechange16;
	private String name17;
	private Object value17;
	private Object valuechange17;
	private String name18;
	private Object value18;
	private Object valuechange18;
	private String name19;
	private Object value19;
	private Object valuechange19;
	private String name20;
	private Object value20;
	private Object valuechange20;
	private String name21;
	private Object value21;
	private Object valuechange21;
	private String name22;
	private Object value22;
	private Object valuechange22;
	private String name23;
	private Object value23;
	private Object valuechange23;
	private String name24;
	private Object value24;
	private Object valuechange24;
	private String name25;
	private Object value25;
	private Object valuechange25;
	private String name26;
	private Object value26;
	private Object valuechange26;
	private String name27;
	private Object value27;
	private Object valuechange27;
	private String name28;
	private Object value28;
	private Object valuechange28;
	private String name29;
	private Object value29;
	private Object valuechange29;
	private String name30;
	private Object value30;
	private Object valuechange30;
	private String name31;
	private Object value31;
	private Object valuechange31;
	private String name32;
	private Object value32;
	private Object valuechange32;
	private String name33;
	private Object value33;
	private Object valuechange33;
	private String name34;
	private Object value34;
	private Object valuechange34;
	private String name35;
	private Object value35;
	private Object valuechange35;
	private String name36;
	private Object value36;
	private Object valuechange36;
	private String name37;
	private Object value37;
	private Object valuechange37;
	private String name38;
	private Object value38;
	private Object valuechange38;
	private String name39;
	private Object value39;
	private Object valuechange39;
	private String name40;
	private Object value40;
	private Object valuechange40;
	private String name41;
	private Object value41;
	private Object valuechange41;
	private String name42;
	private Object value42;
	private Object valuechange42;
	private String name43;
	private Object value43;
	private Object valuechange43;
	private String name44;
	private Object value44;
	private Object valuechange44;
	private String name45;
	private Object value45;
	private Object valuechange45;
	private String name46;
	private Object value46;
	private Object valuechange46;
	private String name47;
	private Object value47;
	private Object valuechange47;
	private String name48;
	private Object value48;
	private Object valuechange48;
	private String name49;
	private Object value49;
	private Object valuechange49;
	private String name50;
	private Object value50;
	private Object valuechange50;
	private String name51;
	private Object value51;
	private Object valuechange51;
	private String name52;
	private Object value52;
	private Object valuechange52;
	private String name53;
	private Object value53;
	private Object valuechange53;
	private String name54;
	private Object value54;
	private Object valuechange54;
	private String name55;
	private Object value55;
	private Object valuechange55;
	private String name56;
	private Object value56;
	private Object valuechange56;
	private String name57;
	private Object value57;
	private Object valuechange57;
	private String name58;
	private Object value58;
	private Object valuechange58;
	private String name59;
	private Object value59;
	private Object valuechange59;
	private String name60;
	private Object value60;
	private Object valuechange60;
	private String name61;
	private Object value61;
	private Object valuechange61;
	private String name62;
	private Object value62;
	private Object valuechange62;
	private String name63;
	private Object value63;
	private Object valuechange63;
	private String name64;
	private Object value64;
	private Object valuechange64;
	private String name65;
	private Object value65;
	private Object valuechange65;
	private String name66;
	private Object value66;
	private Object valuechange66;
	private String name67;
	private Object value67;
	private Object valuechange67;
	private String name68;
	private Object value68;
	private Object valuechange68;
	private String name69;
	private Object value69;
	private Object valuechange69;
	private String name70;
	private Object value70;
	private Object valuechange70;
	private String name71;
	private Object value71;
	private Object valuechange71;
	private String name72;
	private Object value72;
	private Object valuechange72;
	private String name73;
	private Object value73;
	private Object valuechange73;
	private String name74;
	private Object value74;
	private Object valuechange74;
	private String name75;
	private Object value75;
	private Object valuechange75;
	private String name76;
	private Object value76;
	private Object valuechange76;
	private String name77;
	private Object value77;
	private Object valuechange77;
	private String name78;
	private Object value78;
	private Object valuechange78;
	private String name79;
	private Object value79;
	private Object valuechange79;
	private String name80;
	private Object value80;
	private Object valuechange80;
	private String name81;
	private Object value81;
	private Object valuechange81;
	private String name82;
	private Object value82;
	private Object valuechange82;
	private String name83;
	private Object value83;
	private Object valuechange83;
	private String name84;
	private Object value84;
	private Object valuechange84;
	private String name85;
	private Object value85;
	private Object valuechange85;
	private String name86;
	private Object value86;
	private Object valuechange86;
	private String name87;
	private Object value87;
	private Object valuechange87;
	private String name88;
	private Object value88;
	private Object valuechange88;
	private String name89;
	private Object value89;
	private Object valuechange89;
	private String name90;
	private Object value90;
	private Object valuechange90;
	private String name91;
	private Object value91;
	private Object valuechange91;
	private String name92;
	private Object value92;
	private Object valuechange92;
	private String name93;
	private Object value93;
	private Object valuechange93;
	private String name94;
	private Object value94;
	private Object valuechange94;
	private String name95;
	private Object value95;
	private Object valuechange95;
	private String name96;
	private Object value96;
	private Object valuechange96;
	private String name97;
	private Object value97;
	private Object valuechange97;
	private String name98;
	private Object value98;
	private Object valuechange98;
	private String name99;
	private Object value99;
	private Object valuechange99;
	private String name100;
	private Object value100;
	private Object valuechange100;
	public String getName0() {
		return name0;
	}
	public void setName0(String name0) {
		this.name0 = name0;
	}
	public Object getValue0() {
		return value0;
	}
	public void setValue0(Object value0) {
		this.value0 = value0;
	}
	public Object getValuechange0() {
		return valuechange0;
	}
	public void setValuechange0(Object valuechange0) {
		this.valuechange0 = valuechange0;
	}
	public String getName1() {
		return name1;
	}
	public void setName1(String name1) {
		this.name1 = name1;
	}
	public Object getValue1() {
		return value1;
	}
	public void setValue1(Object value1) {
		this.value1 = value1;
	}
	public Object getValuechange1() {
		return valuechange1;
	}
	public void setValuechange1(Object valuechange1) {
		this.valuechange1 = valuechange1;
	}
	public String getName2() {
		return name2;
	}
	public void setName2(String name2) {
		this.name2 = name2;
	}
	public Object getValue2() {
		return value2;
	}
	public void setValue2(Object value2) {
		this.value2 = value2;
	}
	public Object getValuechange2() {
		return valuechange2;
	}
	public void setValuechange2(Object valuechange2) {
		this.valuechange2 = valuechange2;
	}
	public String getName3() {
		return name3;
	}
	public void setName3(String name3) {
		this.name3 = name3;
	}
	public Object getValue3() {
		return value3;
	}
	public void setValue3(Object value3) {
		this.value3 = value3;
	}
	public Object getValuechange3() {
		return valuechange3;
	}
	public void setValuechange3(Object valuechange3) {
		this.valuechange3 = valuechange3;
	}
	public String getName4() {
		return name4;
	}
	public void setName4(String name4) {
		this.name4 = name4;
	}
	public Object getValue4() {
		return value4;
	}
	public void setValue4(Object value4) {
		this.value4 = value4;
	}
	public Object getValuechange4() {
		return valuechange4;
	}
	public void setValuechange4(Object valuechange4) {
		this.valuechange4 = valuechange4;
	}
	public String getName5() {
		return name5;
	}
	public void setName5(String name5) {
		this.name5 = name5;
	}
	public Object getValue5() {
		return value5;
	}
	public void setValue5(Object value5) {
		this.value5 = value5;
	}
	public Object getValuechange5() {
		return valuechange5;
	}
	public void setValuechange5(Object valuechange5) {
		this.valuechange5 = valuechange5;
	}
	public String getName6() {
		return name6;
	}
	public void setName6(String name6) {
		this.name6 = name6;
	}
	public Object getValue6() {
		return value6;
	}
	public void setValue6(Object value6) {
		this.value6 = value6;
	}
	public Object getValuechange6() {
		return valuechange6;
	}
	public void setValuechange6(Object valuechange6) {
		this.valuechange6 = valuechange6;
	}
	public String getName7() {
		return name7;
	}
	public void setName7(String name7) {
		this.name7 = name7;
	}
	public Object getValue7() {
		return value7;
	}
	public void setValue7(Object value7) {
		this.value7 = value7;
	}
	public Object getValuechange7() {
		return valuechange7;
	}
	public void setValuechange7(Object valuechange7) {
		this.valuechange7 = valuechange7;
	}
	public String getName8() {
		return name8;
	}
	public void setName8(String name8) {
		this.name8 = name8;
	}
	public Object getValue8() {
		return value8;
	}
	public void setValue8(Object value8) {
		this.value8 = value8;
	}
	public Object getValuechange8() {
		return valuechange8;
	}
	public void setValuechange8(Object valuechange8) {
		this.valuechange8 = valuechange8;
	}
	public String getName9() {
		return name9;
	}
	public void setName9(String name9) {
		this.name9 = name9;
	}
	public Object getValue9() {
		return value9;
	}
	public void setValue9(Object value9) {
		this.value9 = value9;
	}
	public Object getValuechange9() {
		return valuechange9;
	}
	public void setValuechange9(Object valuechange9) {
		this.valuechange9 = valuechange9;
	}
	public String getName10() {
		return name10;
	}
	public void setName10(String name10) {
		this.name10 = name10;
	}
	public Object getValue10() {
		return value10;
	}
	public void setValue10(Object value10) {
		this.value10 = value10;
	}
	public Object getValuechange10() {
		return valuechange10;
	}
	public void setValuechange10(Object valuechange10) {
		this.valuechange10 = valuechange10;
	}
	public String getName11() {
		return name11;
	}
	public void setName11(String name11) {
		this.name11 = name11;
	}
	public Object getValue11() {
		return value11;
	}
	public void setValue11(Object value11) {
		this.value11 = value11;
	}
	public Object getValuechange11() {
		return valuechange11;
	}
	public void setValuechange11(Object valuechange11) {
		this.valuechange11 = valuechange11;
	}
	public String getName12() {
		return name12;
	}
	public void setName12(String name12) {
		this.name12 = name12;
	}
	public Object getValue12() {
		return value12;
	}
	public void setValue12(Object value12) {
		this.value12 = value12;
	}
	public Object getValuechange12() {
		return valuechange12;
	}
	public void setValuechange12(Object valuechange12) {
		this.valuechange12 = valuechange12;
	}
	public String getName13() {
		return name13;
	}
	public void setName13(String name13) {
		this.name13 = name13;
	}
	public Object getValue13() {
		return value13;
	}
	public void setValue13(Object value13) {
		this.value13 = value13;
	}
	public Object getValuechange13() {
		return valuechange13;
	}
	public void setValuechange13(Object valuechange13) {
		this.valuechange13 = valuechange13;
	}
	public String getName14() {
		return name14;
	}
	public void setName14(String name14) {
		this.name14 = name14;
	}
	public Object getValue14() {
		return value14;
	}
	public void setValue14(Object value14) {
		this.value14 = value14;
	}
	public Object getValuechange14() {
		return valuechange14;
	}
	public void setValuechange14(Object valuechange14) {
		this.valuechange14 = valuechange14;
	}
	public String getName15() {
		return name15;
	}
	public void setName15(String name15) {
		this.name15 = name15;
	}
	public Object getValue15() {
		return value15;
	}
	public void setValue15(Object value15) {
		this.value15 = value15;
	}
	public Object getValuechange15() {
		return valuechange15;
	}
	public void setValuechange15(Object valuechange15) {
		this.valuechange15 = valuechange15;
	}
	public String getName16() {
		return name16;
	}
	public void setName16(String name16) {
		this.name16 = name16;
	}
	public Object getValue16() {
		return value16;
	}
	public void setValue16(Object value16) {
		this.value16 = value16;
	}
	public Object getValuechange16() {
		return valuechange16;
	}
	public void setValuechange16(Object valuechange16) {
		this.valuechange16 = valuechange16;
	}
	public String getName17() {
		return name17;
	}
	public void setName17(String name17) {
		this.name17 = name17;
	}
	public Object getValue17() {
		return value17;
	}
	public void setValue17(Object value17) {
		this.value17 = value17;
	}
	public Object getValuechange17() {
		return valuechange17;
	}
	public void setValuechange17(Object valuechange17) {
		this.valuechange17 = valuechange17;
	}
	public String getName18() {
		return name18;
	}
	public void setName18(String name18) {
		this.name18 = name18;
	}
	public Object getValue18() {
		return value18;
	}
	public void setValue18(Object value18) {
		this.value18 = value18;
	}
	public Object getValuechange18() {
		return valuechange18;
	}
	public void setValuechange18(Object valuechange18) {
		this.valuechange18 = valuechange18;
	}
	public String getName19() {
		return name19;
	}
	public void setName19(String name19) {
		this.name19 = name19;
	}
	public Object getValue19() {
		return value19;
	}
	public void setValue19(Object value19) {
		this.value19 = value19;
	}
	public Object getValuechange19() {
		return valuechange19;
	}
	public void setValuechange19(Object valuechange19) {
		this.valuechange19 = valuechange19;
	}
	public String getName20() {
		return name20;
	}
	public void setName20(String name20) {
		this.name20 = name20;
	}
	public Object getValue20() {
		return value20;
	}
	public void setValue20(Object value20) {
		this.value20 = value20;
	}
	public Object getValuechange20() {
		return valuechange20;
	}
	public void setValuechange20(Object valuechange20) {
		this.valuechange20 = valuechange20;
	}
	public String getName21() {
		return name21;
	}
	public void setName21(String name21) {
		this.name21 = name21;
	}
	public Object getValue21() {
		return value21;
	}
	public void setValue21(Object value21) {
		this.value21 = value21;
	}
	public Object getValuechange21() {
		return valuechange21;
	}
	public void setValuechange21(Object valuechange21) {
		this.valuechange21 = valuechange21;
	}
	public String getName22() {
		return name22;
	}
	public void setName22(String name22) {
		this.name22 = name22;
	}
	public Object getValue22() {
		return value22;
	}
	public void setValue22(Object value22) {
		this.value22 = value22;
	}
	public Object getValuechange22() {
		return valuechange22;
	}
	public void setValuechange22(Object valuechange22) {
		this.valuechange22 = valuechange22;
	}
	public String getName23() {
		return name23;
	}
	public void setName23(String name23) {
		this.name23 = name23;
	}
	public Object getValue23() {
		return value23;
	}
	public void setValue23(Object value23) {
		this.value23 = value23;
	}
	public Object getValuechange23() {
		return valuechange23;
	}
	public void setValuechange23(Object valuechange23) {
		this.valuechange23 = valuechange23;
	}
	public String getName24() {
		return name24;
	}
	public void setName24(String name24) {
		this.name24 = name24;
	}
	public Object getValue24() {
		return value24;
	}
	public void setValue24(Object value24) {
		this.value24 = value24;
	}
	public Object getValuechange24() {
		return valuechange24;
	}
	public void setValuechange24(Object valuechange24) {
		this.valuechange24 = valuechange24;
	}
	public String getName25() {
		return name25;
	}
	public void setName25(String name25) {
		this.name25 = name25;
	}
	public Object getValue25() {
		return value25;
	}
	public void setValue25(Object value25) {
		this.value25 = value25;
	}
	public Object getValuechange25() {
		return valuechange25;
	}
	public void setValuechange25(Object valuechange25) {
		this.valuechange25 = valuechange25;
	}
	public String getName26() {
		return name26;
	}
	public void setName26(String name26) {
		this.name26 = name26;
	}
	public Object getValue26() {
		return value26;
	}
	public void setValue26(Object value26) {
		this.value26 = value26;
	}
	public Object getValuechange26() {
		return valuechange26;
	}
	public void setValuechange26(Object valuechange26) {
		this.valuechange26 = valuechange26;
	}
	public String getName27() {
		return name27;
	}
	public void setName27(String name27) {
		this.name27 = name27;
	}
	public Object getValue27() {
		return value27;
	}
	public void setValue27(Object value27) {
		this.value27 = value27;
	}
	public Object getValuechange27() {
		return valuechange27;
	}
	public void setValuechange27(Object valuechange27) {
		this.valuechange27 = valuechange27;
	}
	public String getName28() {
		return name28;
	}
	public void setName28(String name28) {
		this.name28 = name28;
	}
	public Object getValue28() {
		return value28;
	}
	public void setValue28(Object value28) {
		this.value28 = value28;
	}
	public Object getValuechange28() {
		return valuechange28;
	}
	public void setValuechange28(Object valuechange28) {
		this.valuechange28 = valuechange28;
	}
	public String getName29() {
		return name29;
	}
	public void setName29(String name29) {
		this.name29 = name29;
	}
	public Object getValue29() {
		return value29;
	}
	public void setValue29(Object value29) {
		this.value29 = value29;
	}
	public Object getValuechange29() {
		return valuechange29;
	}
	public void setValuechange29(Object valuechange29) {
		this.valuechange29 = valuechange29;
	}
	public String getName30() {
		return name30;
	}
	public void setName30(String name30) {
		this.name30 = name30;
	}
	public Object getValue30() {
		return value30;
	}
	public void setValue30(Object value30) {
		this.value30 = value30;
	}
	public Object getValuechange30() {
		return valuechange30;
	}
	public void setValuechange30(Object valuechange30) {
		this.valuechange30 = valuechange30;
	}
	public String getName31() {
		return name31;
	}
	public void setName31(String name31) {
		this.name31 = name31;
	}
	public Object getValue31() {
		return value31;
	}
	public void setValue31(Object value31) {
		this.value31 = value31;
	}
	public Object getValuechange31() {
		return valuechange31;
	}
	public void setValuechange31(Object valuechange31) {
		this.valuechange31 = valuechange31;
	}
	public String getName32() {
		return name32;
	}
	public void setName32(String name32) {
		this.name32 = name32;
	}
	public Object getValue32() {
		return value32;
	}
	public void setValue32(Object value32) {
		this.value32 = value32;
	}
	public Object getValuechange32() {
		return valuechange32;
	}
	public void setValuechange32(Object valuechange32) {
		this.valuechange32 = valuechange32;
	}
	public String getName33() {
		return name33;
	}
	public void setName33(String name33) {
		this.name33 = name33;
	}
	public Object getValue33() {
		return value33;
	}
	public void setValue33(Object value33) {
		this.value33 = value33;
	}
	public Object getValuechange33() {
		return valuechange33;
	}
	public void setValuechange33(Object valuechange33) {
		this.valuechange33 = valuechange33;
	}
	public String getName34() {
		return name34;
	}
	public void setName34(String name34) {
		this.name34 = name34;
	}
	public Object getValue34() {
		return value34;
	}
	public void setValue34(Object value34) {
		this.value34 = value34;
	}
	public Object getValuechange34() {
		return valuechange34;
	}
	public void setValuechange34(Object valuechange34) {
		this.valuechange34 = valuechange34;
	}
	public String getName35() {
		return name35;
	}
	public void setName35(String name35) {
		this.name35 = name35;
	}
	public Object getValue35() {
		return value35;
	}
	public void setValue35(Object value35) {
		this.value35 = value35;
	}
	public Object getValuechange35() {
		return valuechange35;
	}
	public void setValuechange35(Object valuechange35) {
		this.valuechange35 = valuechange35;
	}
	public String getName36() {
		return name36;
	}
	public void setName36(String name36) {
		this.name36 = name36;
	}
	public Object getValue36() {
		return value36;
	}
	public void setValue36(Object value36) {
		this.value36 = value36;
	}
	public Object getValuechange36() {
		return valuechange36;
	}
	public void setValuechange36(Object valuechange36) {
		this.valuechange36 = valuechange36;
	}
	public String getName37() {
		return name37;
	}
	public void setName37(String name37) {
		this.name37 = name37;
	}
	public Object getValue37() {
		return value37;
	}
	public void setValue37(Object value37) {
		this.value37 = value37;
	}
	public Object getValuechange37() {
		return valuechange37;
	}
	public void setValuechange37(Object valuechange37) {
		this.valuechange37 = valuechange37;
	}
	public String getName38() {
		return name38;
	}
	public void setName38(String name38) {
		this.name38 = name38;
	}
	public Object getValue38() {
		return value38;
	}
	public void setValue38(Object value38) {
		this.value38 = value38;
	}
	public Object getValuechange38() {
		return valuechange38;
	}
	public void setValuechange38(Object valuechange38) {
		this.valuechange38 = valuechange38;
	}
	public String getName39() {
		return name39;
	}
	public void setName39(String name39) {
		this.name39 = name39;
	}
	public Object getValue39() {
		return value39;
	}
	public void setValue39(Object value39) {
		this.value39 = value39;
	}
	public Object getValuechange39() {
		return valuechange39;
	}
	public void setValuechange39(Object valuechange39) {
		this.valuechange39 = valuechange39;
	}
	public String getName40() {
		return name40;
	}
	public void setName40(String name40) {
		this.name40 = name40;
	}
	public Object getValue40() {
		return value40;
	}
	public void setValue40(Object value40) {
		this.value40 = value40;
	}
	public Object getValuechange40() {
		return valuechange40;
	}
	public void setValuechange40(Object valuechange40) {
		this.valuechange40 = valuechange40;
	}
	public String getName41() {
		return name41;
	}
	public void setName41(String name41) {
		this.name41 = name41;
	}
	public Object getValue41() {
		return value41;
	}
	public void setValue41(Object value41) {
		this.value41 = value41;
	}
	public Object getValuechange41() {
		return valuechange41;
	}
	public void setValuechange41(Object valuechange41) {
		this.valuechange41 = valuechange41;
	}
	public String getName42() {
		return name42;
	}
	public void setName42(String name42) {
		this.name42 = name42;
	}
	public Object getValue42() {
		return value42;
	}
	public void setValue42(Object value42) {
		this.value42 = value42;
	}
	public Object getValuechange42() {
		return valuechange42;
	}
	public void setValuechange42(Object valuechange42) {
		this.valuechange42 = valuechange42;
	}
	public String getName43() {
		return name43;
	}
	public void setName43(String name43) {
		this.name43 = name43;
	}
	public Object getValue43() {
		return value43;
	}
	public void setValue43(Object value43) {
		this.value43 = value43;
	}
	public Object getValuechange43() {
		return valuechange43;
	}
	public void setValuechange43(Object valuechange43) {
		this.valuechange43 = valuechange43;
	}
	public String getName44() {
		return name44;
	}
	public void setName44(String name44) {
		this.name44 = name44;
	}
	public Object getValue44() {
		return value44;
	}
	public void setValue44(Object value44) {
		this.value44 = value44;
	}
	public Object getValuechange44() {
		return valuechange44;
	}
	public void setValuechange44(Object valuechange44) {
		this.valuechange44 = valuechange44;
	}
	public String getName45() {
		return name45;
	}
	public void setName45(String name45) {
		this.name45 = name45;
	}
	public Object getValue45() {
		return value45;
	}
	public void setValue45(Object value45) {
		this.value45 = value45;
	}
	public Object getValuechange45() {
		return valuechange45;
	}
	public void setValuechange45(Object valuechange45) {
		this.valuechange45 = valuechange45;
	}
	public String getName46() {
		return name46;
	}
	public void setName46(String name46) {
		this.name46 = name46;
	}
	public Object getValue46() {
		return value46;
	}
	public void setValue46(Object value46) {
		this.value46 = value46;
	}
	public Object getValuechange46() {
		return valuechange46;
	}
	public void setValuechange46(Object valuechange46) {
		this.valuechange46 = valuechange46;
	}
	public String getName47() {
		return name47;
	}
	public void setName47(String name47) {
		this.name47 = name47;
	}
	public Object getValue47() {
		return value47;
	}
	public void setValue47(Object value47) {
		this.value47 = value47;
	}
	public Object getValuechange47() {
		return valuechange47;
	}
	public void setValuechange47(Object valuechange47) {
		this.valuechange47 = valuechange47;
	}
	public String getName48() {
		return name48;
	}
	public void setName48(String name48) {
		this.name48 = name48;
	}
	public Object getValue48() {
		return value48;
	}
	public void setValue48(Object value48) {
		this.value48 = value48;
	}
	public Object getValuechange48() {
		return valuechange48;
	}
	public void setValuechange48(Object valuechange48) {
		this.valuechange48 = valuechange48;
	}
	public String getName49() {
		return name49;
	}
	public void setName49(String name49) {
		this.name49 = name49;
	}
	public Object getValue49() {
		return value49;
	}
	public void setValue49(Object value49) {
		this.value49 = value49;
	}
	public Object getValuechange49() {
		return valuechange49;
	}
	public void setValuechange49(Object valuechange49) {
		this.valuechange49 = valuechange49;
	}
	public String getName50() {
		return name50;
	}
	public void setName50(String name50) {
		this.name50 = name50;
	}
	public Object getValue50() {
		return value50;
	}
	public void setValue50(Object value50) {
		this.value50 = value50;
	}
	public Object getValuechange50() {
		return valuechange50;
	}
	public void setValuechange50(Object valuechange50) {
		this.valuechange50 = valuechange50;
	}
	public String getName51() {
		return name51;
	}
	public void setName51(String name51) {
		this.name51 = name51;
	}
	public Object getValue51() {
		return value51;
	}
	public void setValue51(Object value51) {
		this.value51 = value51;
	}
	public Object getValuechange51() {
		return valuechange51;
	}
	public void setValuechange51(Object valuechange51) {
		this.valuechange51 = valuechange51;
	}
	public String getName52() {
		return name52;
	}
	public void setName52(String name52) {
		this.name52 = name52;
	}
	public Object getValue52() {
		return value52;
	}
	public void setValue52(Object value52) {
		this.value52 = value52;
	}
	public Object getValuechange52() {
		return valuechange52;
	}
	public void setValuechange52(Object valuechange52) {
		this.valuechange52 = valuechange52;
	}
	public String getName53() {
		return name53;
	}
	public void setName53(String name53) {
		this.name53 = name53;
	}
	public Object getValue53() {
		return value53;
	}
	public void setValue53(Object value53) {
		this.value53 = value53;
	}
	public Object getValuechange53() {
		return valuechange53;
	}
	public void setValuechange53(Object valuechange53) {
		this.valuechange53 = valuechange53;
	}
	public String getName54() {
		return name54;
	}
	public void setName54(String name54) {
		this.name54 = name54;
	}
	public Object getValue54() {
		return value54;
	}
	public void setValue54(Object value54) {
		this.value54 = value54;
	}
	public Object getValuechange54() {
		return valuechange54;
	}
	public void setValuechange54(Object valuechange54) {
		this.valuechange54 = valuechange54;
	}
	public String getName55() {
		return name55;
	}
	public void setName55(String name55) {
		this.name55 = name55;
	}
	public Object getValue55() {
		return value55;
	}
	public void setValue55(Object value55) {
		this.value55 = value55;
	}
	public Object getValuechange55() {
		return valuechange55;
	}
	public void setValuechange55(Object valuechange55) {
		this.valuechange55 = valuechange55;
	}
	public String getName56() {
		return name56;
	}
	public void setName56(String name56) {
		this.name56 = name56;
	}
	public Object getValue56() {
		return value56;
	}
	public void setValue56(Object value56) {
		this.value56 = value56;
	}
	public Object getValuechange56() {
		return valuechange56;
	}
	public void setValuechange56(Object valuechange56) {
		this.valuechange56 = valuechange56;
	}
	public String getName57() {
		return name57;
	}
	public void setName57(String name57) {
		this.name57 = name57;
	}
	public Object getValue57() {
		return value57;
	}
	public void setValue57(Object value57) {
		this.value57 = value57;
	}
	public Object getValuechange57() {
		return valuechange57;
	}
	public void setValuechange57(Object valuechange57) {
		this.valuechange57 = valuechange57;
	}
	public String getName58() {
		return name58;
	}
	public void setName58(String name58) {
		this.name58 = name58;
	}
	public Object getValue58() {
		return value58;
	}
	public void setValue58(Object value58) {
		this.value58 = value58;
	}
	public Object getValuechange58() {
		return valuechange58;
	}
	public void setValuechange58(Object valuechange58) {
		this.valuechange58 = valuechange58;
	}
	public String getName59() {
		return name59;
	}
	public void setName59(String name59) {
		this.name59 = name59;
	}
	public Object getValue59() {
		return value59;
	}
	public void setValue59(Object value59) {
		this.value59 = value59;
	}
	public Object getValuechange59() {
		return valuechange59;
	}
	public void setValuechange59(Object valuechange59) {
		this.valuechange59 = valuechange59;
	}
	public String getName60() {
		return name60;
	}
	public void setName60(String name60) {
		this.name60 = name60;
	}
	public Object getValue60() {
		return value60;
	}
	public void setValue60(Object value60) {
		this.value60 = value60;
	}
	public Object getValuechange60() {
		return valuechange60;
	}
	public void setValuechange60(Object valuechange60) {
		this.valuechange60 = valuechange60;
	}
	public String getName61() {
		return name61;
	}
	public void setName61(String name61) {
		this.name61 = name61;
	}
	public Object getValue61() {
		return value61;
	}
	public void setValue61(Object value61) {
		this.value61 = value61;
	}
	public Object getValuechange61() {
		return valuechange61;
	}
	public void setValuechange61(Object valuechange61) {
		this.valuechange61 = valuechange61;
	}
	public String getName62() {
		return name62;
	}
	public void setName62(String name62) {
		this.name62 = name62;
	}
	public Object getValue62() {
		return value62;
	}
	public void setValue62(Object value62) {
		this.value62 = value62;
	}
	public Object getValuechange62() {
		return valuechange62;
	}
	public void setValuechange62(Object valuechange62) {
		this.valuechange62 = valuechange62;
	}
	public String getName63() {
		return name63;
	}
	public void setName63(String name63) {
		this.name63 = name63;
	}
	public Object getValue63() {
		return value63;
	}
	public void setValue63(Object value63) {
		this.value63 = value63;
	}
	public Object getValuechange63() {
		return valuechange63;
	}
	public void setValuechange63(Object valuechange63) {
		this.valuechange63 = valuechange63;
	}
	public String getName64() {
		return name64;
	}
	public void setName64(String name64) {
		this.name64 = name64;
	}
	public Object getValue64() {
		return value64;
	}
	public void setValue64(Object value64) {
		this.value64 = value64;
	}
	public Object getValuechange64() {
		return valuechange64;
	}
	public void setValuechange64(Object valuechange64) {
		this.valuechange64 = valuechange64;
	}
	public String getName65() {
		return name65;
	}
	public void setName65(String name65) {
		this.name65 = name65;
	}
	public Object getValue65() {
		return value65;
	}
	public void setValue65(Object value65) {
		this.value65 = value65;
	}
	public Object getValuechange65() {
		return valuechange65;
	}
	public void setValuechange65(Object valuechange65) {
		this.valuechange65 = valuechange65;
	}
	public String getName66() {
		return name66;
	}
	public void setName66(String name66) {
		this.name66 = name66;
	}
	public Object getValue66() {
		return value66;
	}
	public void setValue66(Object value66) {
		this.value66 = value66;
	}
	public Object getValuechange66() {
		return valuechange66;
	}
	public void setValuechange66(Object valuechange66) {
		this.valuechange66 = valuechange66;
	}
	public String getName67() {
		return name67;
	}
	public void setName67(String name67) {
		this.name67 = name67;
	}
	public Object getValue67() {
		return value67;
	}
	public void setValue67(Object value67) {
		this.value67 = value67;
	}
	public Object getValuechange67() {
		return valuechange67;
	}
	public void setValuechange67(Object valuechange67) {
		this.valuechange67 = valuechange67;
	}
	public String getName68() {
		return name68;
	}
	public void setName68(String name68) {
		this.name68 = name68;
	}
	public Object getValue68() {
		return value68;
	}
	public void setValue68(Object value68) {
		this.value68 = value68;
	}
	public Object getValuechange68() {
		return valuechange68;
	}
	public void setValuechange68(Object valuechange68) {
		this.valuechange68 = valuechange68;
	}
	public String getName69() {
		return name69;
	}
	public void setName69(String name69) {
		this.name69 = name69;
	}
	public Object getValue69() {
		return value69;
	}
	public void setValue69(Object value69) {
		this.value69 = value69;
	}
	public Object getValuechange69() {
		return valuechange69;
	}
	public void setValuechange69(Object valuechange69) {
		this.valuechange69 = valuechange69;
	}
	public String getName70() {
		return name70;
	}
	public void setName70(String name70) {
		this.name70 = name70;
	}
	public Object getValue70() {
		return value70;
	}
	public void setValue70(Object value70) {
		this.value70 = value70;
	}
	public Object getValuechange70() {
		return valuechange70;
	}
	public void setValuechange70(Object valuechange70) {
		this.valuechange70 = valuechange70;
	}
	public String getName71() {
		return name71;
	}
	public void setName71(String name71) {
		this.name71 = name71;
	}
	public Object getValue71() {
		return value71;
	}
	public void setValue71(Object value71) {
		this.value71 = value71;
	}
	public Object getValuechange71() {
		return valuechange71;
	}
	public void setValuechange71(Object valuechange71) {
		this.valuechange71 = valuechange71;
	}
	public String getName72() {
		return name72;
	}
	public void setName72(String name72) {
		this.name72 = name72;
	}
	public Object getValue72() {
		return value72;
	}
	public void setValue72(Object value72) {
		this.value72 = value72;
	}
	public Object getValuechange72() {
		return valuechange72;
	}
	public void setValuechange72(Object valuechange72) {
		this.valuechange72 = valuechange72;
	}
	public String getName73() {
		return name73;
	}
	public void setName73(String name73) {
		this.name73 = name73;
	}
	public Object getValue73() {
		return value73;
	}
	public void setValue73(Object value73) {
		this.value73 = value73;
	}
	public Object getValuechange73() {
		return valuechange73;
	}
	public void setValuechange73(Object valuechange73) {
		this.valuechange73 = valuechange73;
	}
	public String getName74() {
		return name74;
	}
	public void setName74(String name74) {
		this.name74 = name74;
	}
	public Object getValue74() {
		return value74;
	}
	public void setValue74(Object value74) {
		this.value74 = value74;
	}
	public Object getValuechange74() {
		return valuechange74;
	}
	public void setValuechange74(Object valuechange74) {
		this.valuechange74 = valuechange74;
	}
	public String getName75() {
		return name75;
	}
	public void setName75(String name75) {
		this.name75 = name75;
	}
	public Object getValue75() {
		return value75;
	}
	public void setValue75(Object value75) {
		this.value75 = value75;
	}
	public Object getValuechange75() {
		return valuechange75;
	}
	public void setValuechange75(Object valuechange75) {
		this.valuechange75 = valuechange75;
	}
	public String getName76() {
		return name76;
	}
	public void setName76(String name76) {
		this.name76 = name76;
	}
	public Object getValue76() {
		return value76;
	}
	public void setValue76(Object value76) {
		this.value76 = value76;
	}
	public Object getValuechange76() {
		return valuechange76;
	}
	public void setValuechange76(Object valuechange76) {
		this.valuechange76 = valuechange76;
	}
	public String getName77() {
		return name77;
	}
	public void setName77(String name77) {
		this.name77 = name77;
	}
	public Object getValue77() {
		return value77;
	}
	public void setValue77(Object value77) {
		this.value77 = value77;
	}
	public Object getValuechange77() {
		return valuechange77;
	}
	public void setValuechange77(Object valuechange77) {
		this.valuechange77 = valuechange77;
	}
	public String getName78() {
		return name78;
	}
	public void setName78(String name78) {
		this.name78 = name78;
	}
	public Object getValue78() {
		return value78;
	}
	public void setValue78(Object value78) {
		this.value78 = value78;
	}
	public Object getValuechange78() {
		return valuechange78;
	}
	public void setValuechange78(Object valuechange78) {
		this.valuechange78 = valuechange78;
	}
	public String getName79() {
		return name79;
	}
	public void setName79(String name79) {
		this.name79 = name79;
	}
	public Object getValue79() {
		return value79;
	}
	public void setValue79(Object value79) {
		this.value79 = value79;
	}
	public Object getValuechange79() {
		return valuechange79;
	}
	public void setValuechange79(Object valuechange79) {
		this.valuechange79 = valuechange79;
	}
	public String getName80() {
		return name80;
	}
	public void setName80(String name80) {
		this.name80 = name80;
	}
	public Object getValue80() {
		return value80;
	}
	public void setValue80(Object value80) {
		this.value80 = value80;
	}
	public Object getValuechange80() {
		return valuechange80;
	}
	public void setValuechange80(Object valuechange80) {
		this.valuechange80 = valuechange80;
	}
	public String getName81() {
		return name81;
	}
	public void setName81(String name81) {
		this.name81 = name81;
	}
	public Object getValue81() {
		return value81;
	}
	public void setValue81(Object value81) {
		this.value81 = value81;
	}
	public Object getValuechange81() {
		return valuechange81;
	}
	public void setValuechange81(Object valuechange81) {
		this.valuechange81 = valuechange81;
	}
	public String getName82() {
		return name82;
	}
	public void setName82(String name82) {
		this.name82 = name82;
	}
	public Object getValue82() {
		return value82;
	}
	public void setValue82(Object value82) {
		this.value82 = value82;
	}
	public Object getValuechange82() {
		return valuechange82;
	}
	public void setValuechange82(Object valuechange82) {
		this.valuechange82 = valuechange82;
	}
	public String getName83() {
		return name83;
	}
	public void setName83(String name83) {
		this.name83 = name83;
	}
	public Object getValue83() {
		return value83;
	}
	public void setValue83(Object value83) {
		this.value83 = value83;
	}
	public Object getValuechange83() {
		return valuechange83;
	}
	public void setValuechange83(Object valuechange83) {
		this.valuechange83 = valuechange83;
	}
	public String getName84() {
		return name84;
	}
	public void setName84(String name84) {
		this.name84 = name84;
	}
	public Object getValue84() {
		return value84;
	}
	public void setValue84(Object value84) {
		this.value84 = value84;
	}
	public Object getValuechange84() {
		return valuechange84;
	}
	public void setValuechange84(Object valuechange84) {
		this.valuechange84 = valuechange84;
	}
	public String getName85() {
		return name85;
	}
	public void setName85(String name85) {
		this.name85 = name85;
	}
	public Object getValue85() {
		return value85;
	}
	public void setValue85(Object value85) {
		this.value85 = value85;
	}
	public Object getValuechange85() {
		return valuechange85;
	}
	public void setValuechange85(Object valuechange85) {
		this.valuechange85 = valuechange85;
	}
	public String getName86() {
		return name86;
	}
	public void setName86(String name86) {
		this.name86 = name86;
	}
	public Object getValue86() {
		return value86;
	}
	public void setValue86(Object value86) {
		this.value86 = value86;
	}
	public Object getValuechange86() {
		return valuechange86;
	}
	public void setValuechange86(Object valuechange86) {
		this.valuechange86 = valuechange86;
	}
	public String getName87() {
		return name87;
	}
	public void setName87(String name87) {
		this.name87 = name87;
	}
	public Object getValue87() {
		return value87;
	}
	public void setValue87(Object value87) {
		this.value87 = value87;
	}
	public Object getValuechange87() {
		return valuechange87;
	}
	public void setValuechange87(Object valuechange87) {
		this.valuechange87 = valuechange87;
	}
	public String getName88() {
		return name88;
	}
	public void setName88(String name88) {
		this.name88 = name88;
	}
	public Object getValue88() {
		return value88;
	}
	public void setValue88(Object value88) {
		this.value88 = value88;
	}
	public Object getValuechange88() {
		return valuechange88;
	}
	public void setValuechange88(Object valuechange88) {
		this.valuechange88 = valuechange88;
	}
	public String getName89() {
		return name89;
	}
	public void setName89(String name89) {
		this.name89 = name89;
	}
	public Object getValue89() {
		return value89;
	}
	public void setValue89(Object value89) {
		this.value89 = value89;
	}
	public Object getValuechange89() {
		return valuechange89;
	}
	public void setValuechange89(Object valuechange89) {
		this.valuechange89 = valuechange89;
	}
	public String getName90() {
		return name90;
	}
	public void setName90(String name90) {
		this.name90 = name90;
	}
	public Object getValue90() {
		return value90;
	}
	public void setValue90(Object value90) {
		this.value90 = value90;
	}
	public Object getValuechange90() {
		return valuechange90;
	}
	public void setValuechange90(Object valuechange90) {
		this.valuechange90 = valuechange90;
	}
	public String getName91() {
		return name91;
	}
	public void setName91(String name91) {
		this.name91 = name91;
	}
	public Object getValue91() {
		return value91;
	}
	public void setValue91(Object value91) {
		this.value91 = value91;
	}
	public Object getValuechange91() {
		return valuechange91;
	}
	public void setValuechange91(Object valuechange91) {
		this.valuechange91 = valuechange91;
	}
	public String getName92() {
		return name92;
	}
	public void setName92(String name92) {
		this.name92 = name92;
	}
	public Object getValue92() {
		return value92;
	}
	public void setValue92(Object value92) {
		this.value92 = value92;
	}
	public Object getValuechange92() {
		return valuechange92;
	}
	public void setValuechange92(Object valuechange92) {
		this.valuechange92 = valuechange92;
	}
	public String getName93() {
		return name93;
	}
	public void setName93(String name93) {
		this.name93 = name93;
	}
	public Object getValue93() {
		return value93;
	}
	public void setValue93(Object value93) {
		this.value93 = value93;
	}
	public Object getValuechange93() {
		return valuechange93;
	}
	public void setValuechange93(Object valuechange93) {
		this.valuechange93 = valuechange93;
	}
	public String getName94() {
		return name94;
	}
	public void setName94(String name94) {
		this.name94 = name94;
	}
	public Object getValue94() {
		return value94;
	}
	public void setValue94(Object value94) {
		this.value94 = value94;
	}
	public Object getValuechange94() {
		return valuechange94;
	}
	public void setValuechange94(Object valuechange94) {
		this.valuechange94 = valuechange94;
	}
	public String getName95() {
		return name95;
	}
	public void setName95(String name95) {
		this.name95 = name95;
	}
	public Object getValue95() {
		return value95;
	}
	public void setValue95(Object value95) {
		this.value95 = value95;
	}
	public Object getValuechange95() {
		return valuechange95;
	}
	public void setValuechange95(Object valuechange95) {
		this.valuechange95 = valuechange95;
	}
	public String getName96() {
		return name96;
	}
	public void setName96(String name96) {
		this.name96 = name96;
	}
	public Object getValue96() {
		return value96;
	}
	public void setValue96(Object value96) {
		this.value96 = value96;
	}
	public Object getValuechange96() {
		return valuechange96;
	}
	public void setValuechange96(Object valuechange96) {
		this.valuechange96 = valuechange96;
	}
	public String getName97() {
		return name97;
	}
	public void setName97(String name97) {
		this.name97 = name97;
	}
	public Object getValue97() {
		return value97;
	}
	public void setValue97(Object value97) {
		this.value97 = value97;
	}
	public Object getValuechange97() {
		return valuechange97;
	}
	public void setValuechange97(Object valuechange97) {
		this.valuechange97 = valuechange97;
	}
	public String getName98() {
		return name98;
	}
	public void setName98(String name98) {
		this.name98 = name98;
	}
	public Object getValue98() {
		return value98;
	}
	public void setValue98(Object value98) {
		this.value98 = value98;
	}
	public Object getValuechange98() {
		return valuechange98;
	}
	public void setValuechange98(Object valuechange98) {
		this.valuechange98 = valuechange98;
	}
	public String getName99() {
		return name99;
	}
	public void setName99(String name99) {
		this.name99 = name99;
	}
	public Object getValue99() {
		return value99;
	}
	public void setValue99(Object value99) {
		this.value99 = value99;
	}
	public Object getValuechange99() {
		return valuechange99;
	}
	public void setValuechange99(Object valuechange99) {
		this.valuechange99 = valuechange99;
	}
	public String getName100() {
		return name100;
	}
	public void setName100(String name100) {
		this.name100 = name100;
	}
	public Object getValue100() {
		return value100;
	}
	public void setValue100(Object value100) {
		this.value100 = value100;
	}
	public Object getValuechange100() {
		return valuechange100;
	}
	public void setValuechange100(Object valuechange100) {
		this.valuechange100 = valuechange100;
	}

}
