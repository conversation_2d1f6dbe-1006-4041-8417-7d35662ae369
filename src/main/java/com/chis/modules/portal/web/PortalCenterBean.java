package com.chis.modules.portal.web;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.component.html.HtmlOutputText;
import javax.faces.component.html.HtmlPanelGrid;

import org.primefaces.component.commandlink.CommandLink;
import org.primefaces.component.panel.Panel;
import org.primefaces.component.tabview.Tab;
import org.primefaces.component.tabview.TabView;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.entity.TdPortalColumn;
import com.chis.modules.portal.entity.TdPortalLayout;
import com.chis.modules.portal.entity.TdPortalLinks;
import com.chis.modules.portal.entity.TdPortalNews;
import com.chis.modules.portal.entity.TdPortalNewsAnnex;
import com.chis.modules.portal.entity.TdPortalNewsType;
import com.chis.modules.portal.entity.TdProtal;
import com.chis.modules.portal.entity.TsPersonalSetting;
import com.chis.modules.portal.enumn.PortalColType;
import com.chis.modules.portal.enumn.TemplType;
import com.chis.modules.portal.service.PortalBaseImpl;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.MsgSendUtil;
import com.chis.modules.system.web.FacesBean;

/**
 * 门户展示 图片栏：图片高度300px;
 * 
 */
@ManagedBean(name = "portalCenterBean")
@ViewScoped
public class PortalCenterBean extends FacesBean {

	private static final long serialVersionUID = 2350275983339647922L;
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap()
			.get(SessionData.SESSION_DATA);
	private PortalBaseImpl service = (PortalBaseImpl) SpringContextHolder
			.getBean(PortalBaseImpl.class);

	private CommServiceImpl commService = SpringContextHolder
			.getBean(CommServiceImpl.class);

	/** 门户对象 */
	private TdProtal tdProtal;
	/** 门户布局容器 */
	private HtmlPanelGrid panelGrid = (HtmlPanelGrid) JsfUtil.getApplication()
			.createComponent(HtmlPanelGrid.COMPONENT_TYPE);
	/** 门户布局容器列的宽度 */
	private String panelGridColumnStyle;
	/** 换算宽度百分比 */
	private static final BigDecimal TEN = new BigDecimal(10);
	/** 是否有图片栏 */
	private boolean imgColBool = Boolean.FALSE;
	/** 是否有公告栏 */
	private boolean gglColBool = Boolean.FALSE;
	/** 每一个布局容器中的栏目的panel的ID的前缀 */
	private static final String WIGET_FIX = "portal_col_";
	private String linkUrl;

	public PortalCenterBean() {
		this.init();
		this.searchAction();
	}

	/**
	 * 初始化
	 */
	public void init() {

	}

	/**
	 * 查询方法
	 */
	public void searchAction() {
		// 获取该门户需要的数据
		String pid = JsfUtil.getRequest().getParameter("pid");
		if (null == pid || StringUtils.isBlank(pid)) {
			TsPersonalSetting tsPersonalSetting = service
					.findPersonSetting(sessionData.getUser().getRid());
			pid = tsPersonalSetting.getDefaultUrl().split("pid=")[1];
		}
		/**
		 * 0- 门户对象 1- 栏目对应的信息 map<栏目ID， 信息>
		 */
		List initDataList = this.service.findPortalData(Integer.valueOf(pid),
				this.sessionData.getUser().getRid());

		// 门户对象
		this.tdProtal = (TdProtal) initDataList.get(0);
		List<TdPortalLayout> layoutList = this.tdProtal.getTdPortalLayouts();

		// 信息集合
		Map newsMap = (Map) initDataList.get(1);

		// 获取版式
		TemplType templType = this.tdProtal.getTemplType();
		String typeCN = templType.getTypeCN();
		String[] splits = typeCN.split(":");

		// 清空布局容器，并重新布局
		this.panelGrid.getChildren().clear();
		this.panelGrid.setColumns(splits.length);

		// 构建panelGrid的列宽度
		StringBuilder colBuilder = new StringBuilder();
		StringBuilder colStyleBuilder = new StringBuilder();
		colBuilder.append("<style type=\"text/css\">");

		// 初始化布局
		for (int i = 0; i < splits.length; i++) {
			// 版式各列的宽度
			colBuilder.append(" 	.portal_mainGrid_col").append(i).append("{ ");
			colBuilder.append(" 		width: ")
					.append(new BigDecimal(splits[i]).multiply(TEN))
					.append("%;");
			colBuilder.append(" 		vertical-align: top;");
			colBuilder.append("	} ");

			// panelGrid引用的列样式
			colStyleBuilder.append(",portal_mainGrid_col").append(i);

			final HtmlPanelGrid grid = (HtmlPanelGrid) JsfUtil.getApplication()
					.createComponent(HtmlPanelGrid.COMPONENT_TYPE);
			grid.setStyle("padding:0px;width:100%;vertical-align: top;");
			grid.setColumns(1);

			for (TdPortalLayout layout : layoutList) {
				if (layout.getPos().intValue() == i) {
					// 栏目对象
					TdPortalColumn portalColumn = layout.getTdPortalColumn();

					// 设置该栏目内的内容
					if (portalColumn.getColType().equals(PortalColType.IMG)) {

						final Panel panel = (Panel) JsfUtil.getApplication()
								.createComponent(Panel.COMPONENT_TYPE);
						StringBuilder sb = new StringBuilder();
						sb.append("<a href=\"javascript:forwordPage('")
								.append(portalColumn.getMoreUrl())
								.append("', '")
								.append(portalColumn.getColName())
								.append("')\">");
						sb.append(portalColumn.getColName()).append("</a>");
						// 设置title
						panel.setHeader(sb.toString());

						// 设置ID
						String columnClientId = WIGET_FIX
								+ layout.getTdPortalColumn().getRid();
						panel.setId(columnClientId);

						// 图片栏
						this.imgColBool = Boolean.TRUE;
						List<TdPortalNews> newsList = (List<TdPortalNews>) newsMap
								.get(portalColumn.getRid().toString());
						panel.setStyle("height:300px;width:100%;");

						final HtmlOutputText text = (HtmlOutputText) JsfUtil
								.getApplication().createComponent(
										HtmlOutputText.COMPONENT_TYPE);
						text.setEscape(Boolean.FALSE);

						if (null != newsList && newsList.size() > 0) {
							StringBuilder cont = new StringBuilder();
							cont.append("<div class=\"roll-news\">");
							cont.append("		<div class=\"roll-news-image\">");

							for (int j = 0; j < newsList.size(); j++) {
								TdPortalNews news = newsList.get(j);
								TdPortalNewsAnnex newsAnnex = news
										.getTdPortalNewsAnnex();
								if (j == 0) {
									cont.append("<img src=\"/webFile")
											.append(newsAnnex.getAnnexAddr())
											.append("\" title=\"");
								} else {
									cont.append("<img src=\"/webFile")
											.append(newsAnnex.getAnnexAddr())
											.append("\" style=\"display:none\" title=\"");
								}
								cont.append(newsAnnex.getAnnexDesc())
										.append("\" onclick=\"javascript:forwordPage('")
										.append(news.getNewAdr()).append("','");
								cont.append(newsAnnex.getAnnexDesc()).append(
										"')\" />");
							}
							cont.append("</div>");
							cont.append("<div class=\"roll-news-index\">");
							cont.append("		<ul>");

							for (int j = 0; j < newsList.size(); j++) {
								if (j == 0) {
									cont.append("		<li class=\"roll-news-index-hover\">1</li>");
								} else {
									cont.append("<li>").append((j + 1))
											.append("</li>");
								}
							}
							cont.append("</ul>");
							cont.append("</div>");
							cont.append("</div>");

							text.setValue(cont.toString());
							// System.out.println("【图片内容】:" + cont.toString());
						} else {
							text.setValue("没有内容");
							text.setStyle("color:gray");
						}

						panel.getChildren().add(text);
						grid.getChildren().add(panel);
					} else if (portalColumn.getColType().equals(
							PortalColType.GGL)) {
						final Panel panel = (Panel) JsfUtil.getApplication()
								.createComponent(Panel.COMPONENT_TYPE);
						// 设置title
						panel.setHeader(portalColumn.getColName());

						// 设置ID
						String columnClientId = WIGET_FIX
								+ layout.getTdPortalColumn().getRid();
						panel.setId(columnClientId);


						// 公告栏
						this.gglColBool = Boolean.TRUE;

						List<TdPortalNews> newsList = (List<TdPortalNews>) newsMap
								.get(portalColumn.getRid().toString());
						StringBuilder styleBuilder = new StringBuilder();
						styleBuilder
								.append("width:100%;vertical-align: top;text-align:left;height:300px;");
						// System.out.println("【公告栏样式】:" +
						// styleBuilder.toString());
						panel.setStyle(styleBuilder.toString());

						final HtmlOutputText text = (HtmlOutputText) JsfUtil
								.getApplication().createComponent(
										HtmlOutputText.COMPONENT_TYPE);
						text.setEscape(Boolean.FALSE);

						if (null != newsList && newsList.size() > 0) {
							StringBuilder cont = new StringBuilder();

							cont.append("			<div class=\"list_lh\">");
							cont.append("				<ul>");
							for (TdPortalNews news : newsList) {
								cont.append("<li>");
								cont.append("	<p>");
								cont.append(
										"		<a href=\"javascript:forwordPage('")
										.append(news.getNewAdr());
								cont.append("','").append(news.getNewsTitle())
										.append("')\"  title=\"")
										.append(news.getNewsTitle())
										.append("\">");

								// 如果设置了字段长度，并且标题超过了字段长度，需要截取字符串，加...
									cont.append(news.getNewsTitle());
								cont.append("</a>");
								if (news.isIfNew()) {
									cont.append("		<img src=\"/resources/images/login/new.gif\" style=\"margin-left:10px;float:left\"/>");
								}
								cont.append("<span>");
								if (layout.getDispDate().intValue() == 1) {
									cont.append(news.getNewsDateStr());
								}
								cont.append("</span>");
								cont.append("	</p>");
								cont.append("</li>");
							}
							cont.append("			</ul>");
							cont.append("		</div>");

							text.setValue(cont.toString());
							// System.out.println("【公告栏内容】:" + cont.toString());
						} else {
							text.setValue("没有内容");
							text.setStyle("color:gray");
						}

						panel.getChildren().add(text);
						grid.getChildren().add(panel);
					} else if (portalColumn.getColType().equals(
							PortalColType.PT)) {
						// 普通信息也分两种，一种是有子类型的（采用tab），一种是没有子类型的
						List<TdPortalNewsType> newsTypeList = portalColumn
								.getTdPortalNewsType().getTdPortalNewsTypes();
						if (null != newsTypeList && newsTypeList.size() > 0) {
							// 采用tabView
							final TabView tabView = (TabView) JsfUtil
									.getApplication().createComponent(
											TabView.COMPONENT_TYPE);
							Map<TdPortalNewsType, List<TdPortalNews>> comNewsMap = (Map<TdPortalNewsType, List<TdPortalNews>>) newsMap
									.get(portalColumn.getRid().toString());
							Set<TdPortalNewsType> keySet = comNewsMap.keySet();

							// 设置ID
							String columnClientId = WIGET_FIX
									+ layout.getTdPortalColumn().getRid();
							tabView.setId(columnClientId);

							int height;
							// 设置了高度，就用设置的，没有设置，就自动计算
							if (null != layout.getHeighth()) {
								height = layout.getHeighth();
							} else {
								if (null != layout.getLines()
										&& layout.getLines() != 0) {
									height = layout.getLines() * 40 + 20;
								} else {
									height = 300;
								}
							}
							StringBuilder styleBuilder = new StringBuilder();
							// System.out.println("【" +
							// portalColumn.getColName() + "】:" + height);
							styleBuilder
									.append("width:100%;vertical-align: top;text-align:left;height:")
									.append(height).append("px;");
							tabView.setStyle(styleBuilder.toString());

							for (TdPortalNewsType tnt : keySet) {
								final Tab tab = (Tab) JsfUtil.getApplication()
										.createComponent(Tab.COMPONENT_TYPE);

								// 设置title，设置ID，格式为tab_类型id_上级栏目名称-上级栏目ID
								tab.setTitle(tnt.getTypeName());
								tab.setId("tab_" + tnt.getRid() + "_"
										+ portalColumn.getColName() + "-"
										+ portalColumn.getRid());

								List<TdPortalNews> newsList = (List<TdPortalNews>) comNewsMap
										.get(tnt);

								final HtmlOutputText text = (HtmlOutputText) JsfUtil
										.getApplication().createComponent(
												HtmlOutputText.COMPONENT_TYPE);
								text.setEscape(Boolean.FALSE);

								if (null != newsList && newsList.size() > 0) {
									StringBuilder cont = new StringBuilder();

									cont.append("			<div>");
									cont.append("				<ul>");
									for (TdPortalNews news : newsList) {
										cont.append("<li>");
										cont.append("	<p>");
										cont.append(
												"		<a href=\"javascript:forwordPage('")
												.append(news.getNewAdr());
										cont.append("','")
												.append(news.getNewsTitle())
												.append("')\"  title=\"")
												.append(news.getNewsTitle())
												.append("\">");
										// 如果设置了字段长度，并且标题超过了字段长度，需要截取字符串，加...
											cont.append(news.getNewsTitle());
										cont.append("</a>");
										
										if (news.isIfNew()) {
											cont.append("		<img src=\"/resources/images/login/new.gif\" style=\"margin-left:10px;float:left\"/>");
										}
										cont.append("<span>");
										if (layout.getDispDate().intValue() == 1) {
											cont.append(news.getNewsDateStr());
										}
										cont.append("</span>");
										cont.append("	</p>");
										cont.append("</li>");
									}
									cont.append("			</ul>");
									cont.append("		</div>");

									text.setValue(cont.toString());
									// System.out.println("【信息内容】:" +
									// cont.toString());
								} else {
									text.setValue("没有内容");
									text.setStyle("color:gray");
								}
								tab.getChildren().add(text);
								tabView.getChildren().add(tab);
							}

							grid.getChildren().add(tabView);
						} else {
							// 采用Panel
							final Panel panel = (Panel) JsfUtil
									.getApplication().createComponent(
											Panel.COMPONENT_TYPE);

							// 设置ID
							String columnClientId = WIGET_FIX
									+ layout.getTdPortalColumn().getRid();
							panel.setId(columnClientId);


							// 设置title
							StringBuilder titBuild = new StringBuilder();
							titBuild.append(
									"<a href=\"javascript:forwordPage('")
									.append(portalColumn.getMoreUrl())
									.append("', '")
									.append(portalColumn.getColName())
									.append("')\">");
							titBuild.append(portalColumn.getColName()).append(
									"</a>");
							panel.setHeader(titBuild.toString());

							// 普通信息
							List<TdPortalNews> newsList = (List<TdPortalNews>) newsMap
									.get(portalColumn.getRid().toString());
							int height;
							// 设置了高度，就用设置的，没有设置，就自动计算
							if (null != layout.getHeighth()) {
								height = layout.getHeighth();
							} else {
								if (null != layout.getLines()
										&& layout.getLines() != 0) {
									height = layout.getLines() * 40 + 20;
								} else {
									height = 300;
								}
							}
							StringBuilder styleBuilder = new StringBuilder();
							// System.out.println("【" +
							// portalColumn.getColName() + "】:" + height);
							styleBuilder
									.append("width:100%;vertical-align: top;text-align:left;height:")
									.append(height).append("px;border:1");
							panel.setStyle(styleBuilder.toString());

							final HtmlOutputText text = (HtmlOutputText) JsfUtil
									.getApplication().createComponent(
											HtmlOutputText.COMPONENT_TYPE);
							text.setEscape(Boolean.FALSE);

							if (null != newsList && newsList.size() > 0) {
								StringBuilder cont = new StringBuilder();

								cont.append("			<div>");
								cont.append("				<ul>");
								for (TdPortalNews news : newsList) {
									cont.append("<li>");
									cont.append("	<p>");
									cont.append(
											"		<a href=\"javascript:forwordPage('")
											.append(news.getNewAdr());
									cont.append("','")
											.append(news.getNewsTitle())
											.append("')\"  title=\"")
											.append(news.getNewsTitle())
											.append("\">");
									cont.append(news.getNewsTitle());
									cont.append("</a>");
									if (news.isIfNew()) {
										cont.append("		<img src=\"/resources/images/login/new.gif\" style=\"margin-left:10px;float:left\"/>");
									}
									cont.append("<span>");
									if (layout.getDispDate().intValue() == 1) {
										cont.append(news.getNewsDateStr());
									}
									cont.append("</span>");
									cont.append("	</p>");
									cont.append("</li>");
								}
								cont.append("			</ul>");
								cont.append("		</div>");

								text.setValue(cont.toString());
								// System.out.println("【信息内容】:" +
								// cont.toString());
							} else {
								text.setValue("没有内容");
								text.setStyle("color:gray");
							}

							panel.getChildren().add(text);
							grid.getChildren().add(panel);
						}

					} else if (portalColumn.getColType().equals(
							PortalColType.LINK)) {
						// 常用链接
						final Panel panel = (Panel) JsfUtil.getApplication()
								.createComponent(Panel.COMPONENT_TYPE);

						// 设置ID
						String columnClientId = WIGET_FIX
								+ layout.getTdPortalColumn().getRid();
						panel.setId(columnClientId);

						// 设置title
						StringBuilder titBuild = new StringBuilder();
						titBuild.append(
								"<a href=\"javascript:forwordPage('/webapp/portal/portalLinksList.faces?opt=1', '")
								.append(portalColumn.getColName())
								.append("')\">");
						titBuild.append(portalColumn.getColName()).append(
								"</a>");
						panel.setHeader(titBuild.toString());

						List<TdPortalLinks> newsList = (List<TdPortalLinks>) newsMap
								.get(portalColumn.getRid().toString());
						int height;
						// 设置了高度，就用设置的，没有设置，就自动计算
						if (null != layout.getHeighth()) {
							height = layout.getHeighth();
						} else {
							if (null != layout.getLines()
									&& layout.getLines() != 0) {
								height = layout.getLines() * 50 + 20;
							} else {
								height = 300;
							}
						}
						StringBuilder styleBuilder = new StringBuilder();
						// System.out.println("【" + portalColumn.getColName() +
						// "】:" + height);
						styleBuilder
								.append("width:100%;vertical-align: top;height:")
								.append(height).append("px;overflow-y:auto;");
						panel.setStyle(styleBuilder.toString());

						final HtmlOutputText text = (HtmlOutputText) JsfUtil
								.getApplication().createComponent(
										HtmlOutputText.COMPONENT_TYPE);
						text.setEscape(Boolean.FALSE);

						if (null != newsList && newsList.size() > 0) {
							StringBuilder cont = new StringBuilder();

							// cont.append("<div style=\"width:155px;padding:0px;margin-left:auto;margin-right:auto;\">");
							for (TdPortalLinks news : newsList) {
								if (news.getLinkType().intValue() == 0) {
									// 外部打开
									cont.append("<a href=\"")
											.append(news.getLinkUrl())
											.append("\" target=\"_blank\" style=\"margin-top:3px;\">");
								} else {
									// 内部打开
									cont.append(
											"<a href=\"javascript:forwordPage('")
											.append(news.getLinkUrl())
											.append("','")
											.append(news.getLinkName())
											.append("')\" style=\"margin-top:3px;\">");
								}
								cont.append("<img src=\"/webFile")
										.append(news.getLinkIcon())
										.append("\" style=\"width:155px;height:45px;\" ");
								cont.append(" title=\"")
										.append(news.getLinkName())
										.append("\" ");
								cont.append("/>&#160;&#160;");
								cont.append("</a>");
							}
							// cont.append("</div>");
							text.setValue(cont.toString());
							// System.out.println("【信息内容】:" + cont.toString());
						} else {
							text.setValue("没有内容");
							text.setStyle("color:gray");
						}

						panel.getChildren().add(text);
						grid.getChildren().add(panel);
					} else if (portalColumn.getColType().equals(
							PortalColType.OTHER)) {
						// 其他栏目
						this.initOtherColumn(portalColumn, layout, grid,
								newsMap);
					}
				}
			}

			this.panelGrid.getChildren().add(grid);
		}

		colBuilder.append("</style>");
		// 设置panelGrid的列宽度
		this.panelGrid
				.setColumnClasses(colStyleBuilder.toString().substring(1));
		// panelGrid列样式
		this.panelGridColumnStyle = colBuilder.toString();

	}

	/**
	 * 将其他栏目以iframe的格式进行展示
	 * 
	 * @param portalColumn
	 *            列对象
	 * @param layout
	 *            布局类
	 */
	private void initOtherColumn(TdPortalColumn portalColumn,
			TdPortalLayout layout, HtmlPanelGrid grid, Map newsMap) {
		/**
		 * <script type=\"text/javascript\"> var ifm =
		 * document.createElement("iframe"); ifm.id="ifm"; ifm.width="100%";
		 * ifm.height="100%"; ifm.setAttribute("frameborder", "0", 0);
		 * ifm.src="http://127.0.0.1:8888/jsf2/pages/exam4.jsf";
		 * document.getElementById("sportsDiv").appendChild(ifm); </script>
		 * <IFRAME ID='ifm2' WIDTH='189' HEIGHT='190' ALIGN='CENTER'
		 * MARGINWIDTH='0' MARGINHEIGHT='0' HSPACE='0' VSPACE='0'
		 * FRAMEBORDER='0' SCROLLING='NO'
		 * SRC='http://weather.qq.com/inc/ss248.htm'></IFRAME>
		 */
		final Panel panel = (Panel) JsfUtil.getApplication().createComponent(
				Panel.COMPONENT_TYPE);
		if (portalColumn.getIfHideHeader() == null
				|| 0 == portalColumn.getIfHideHeader().intValue()) {
			panel.setHeader(portalColumn.getColName());
		} else {
			panel.setStyleClass("no_header");
		}

		// 设置ID
		String columnClientId = WIGET_FIX + layout.getTdPortalColumn().getRid();
		panel.setId(columnClientId);

		/**
		 * 添加刷新按钮
		 */
		final CommandLink commandLink = (CommandLink) JsfUtil.getApplication()
				.createComponent(CommandLink.COMPONENT_TYPE);
		commandLink
				.setStyleClass("ui-panel-titlebar-icon ui-corner-all ui-state-default");
		final HtmlOutputText output = (HtmlOutputText) JsfUtil.getApplication()
				.createComponent(HtmlOutputText.COMPONENT_TYPE);
		output.setStyleClass("ui-icon ui-icon-refresh");
		commandLink.getChildren().add(output);
		commandLink.setUpdate(columnClientId);
		commandLink.setProcess("@this");
		panel.getFacets().put("actions", commandLink);

		int height = 0;
		/**
		 * 高度的设置： 1.门户布局表中设置了高度，就用高度 2.url中传递了zwxh高度的值就用url传递的
		 * 3.门户布局表中设置了行数，就计算高度 4.都不满足，就设置为300px
		 */
		if (null != layout.getHeighth()) {
			height = layout.getHeighth();
		} else if (StringUtils.isNotBlank(portalColumn.getMoreUrl())) {
			String heightStr = StringUtils.parseUrl(portalColumn.getMoreUrl(),
					"zwxh");
			if (StringUtils.isNotBlank(heightStr)
					&& StringUtils.isNumeric(heightStr)) {
				height = Integer.parseInt(heightStr);
			}
		} else if (null != layout.getLines() && layout.getLines() > 0) {
			height = layout.getLines() * 50 + 20;
		}
		if (height <= 0) {
			height = 300;
		}

		StringBuilder styleBuilder = new StringBuilder();
		styleBuilder.append("width:100%;");
		styleBuilder.append("height:").append(height).append("px;");
		panel.setStyle(styleBuilder.toString());

		final HtmlOutputText text = (HtmlOutputText) JsfUtil.getApplication()
				.createComponent(HtmlOutputText.COMPONENT_TYPE);
		text.setEscape(Boolean.FALSE);
		/**
		 * 通过一个DIV里面放入IFRAME
		 */
		String frameDivId = "iframeDiv" + layout.getTdPortalColumn().getRid();
		StringBuilder sb = new StringBuilder();
		sb.append("<div id=\"").append(frameDivId)
				.append("\" style=\"width:100%;height:").append(height - 50)
				.append("px;\"></div>");
		sb.append("<script type=\"text/javascript\">");
		sb.append("var ifm = document.createElement(\"iframe\");");
		sb.append("ifm.id=\"iframe")
				.append(layout.getTdPortalColumn().getRid()).append("\";");
		if (portalColumn.getNeedSocketUpdate() != null
				&& portalColumn.getNeedSocketUpdate().intValue() == 1) {
			sb.append("ifm.name=\"need_socket_update\";");
		}
		sb.append("ifm.width=\"100%\";");
		sb.append("ifm.height=\"100%\";");
		sb.append("ifm.setAttribute(\"frameborder\", \"0\", 0);");
		sb.append("ifm.src=\"").append(portalColumn.getMoreUrl()).append("\";");
		sb.append("document.getElementById(\"").append(frameDivId)
				.append("\").appendChild(ifm);");
		sb.append("</script>");
		text.setValue(sb.toString());

		panel.getChildren().add(text);
		grid.getChildren().add(panel);
	}

	public void updateMsgState() {
		String rid = null;
		StringBuilder sb = new StringBuilder();
		sb.append("select a.rid from td_msg_sub a");
		sb.append(" inner join td_msg_main b on a.main_id=b.rid");
		sb.append(" where 1=1");
		sb.append(" and a.PUBLISH_MAN=").append(
				this.sessionData.getUser().getRid());
		sb.append(" and b.net_adr like :columnname escape '\\\'");
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("columnname",
				"%" + StringUtils.convertBFH(this.linkUrl.trim()) + "%");
		List<Object[]> list = this.commService.getMsgRid(sb.toString(),
				paramMap);
		if (null != list && list.size() > 0) {
			Object obj = list.get(0);
			rid = obj.toString();
		}
		MsgSendUtil.updateMsgState(rid, "1", this.sessionData.getUser()
				.getRid());
		MsgSendUtil.updateMsgInfo();
		MsgSendUtil.sendNewMsg(this.sessionData.getUser().getRid().toString());
	}

	// getters and setters
	public HtmlPanelGrid getPanelGrid() {
		return panelGrid;
	}

	public void setPanelGrid(HtmlPanelGrid panelGrid) {
		this.panelGrid = panelGrid;
	}

	public String getPanelGridColumnStyle() {
		return panelGridColumnStyle;
	}

	public void setPanelGridColumnStyle(String panelGridColumnStyle) {
		this.panelGridColumnStyle = panelGridColumnStyle;
	}

	public boolean isImgColBool() {
		return imgColBool;
	}

	public void setImgColBool(boolean imgColBool) {
		this.imgColBool = imgColBool;
	}

	public boolean isGglColBool() {
		return gglColBool;
	}

	public void setGglColBool(boolean gglColBool) {
		this.gglColBool = gglColBool;
	}

	public String getLinkUrl() {
		return linkUrl;
	}

	public void setLinkUrl(String linkUrl) {
		this.linkUrl = linkUrl;
	}

}
