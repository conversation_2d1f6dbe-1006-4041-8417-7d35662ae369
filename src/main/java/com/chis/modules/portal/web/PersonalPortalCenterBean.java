package com.chis.modules.portal.web;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.component.html.HtmlOutputText;

import org.primefaces.component.commandlink.CommandLink;
import org.primefaces.component.dashboard.Dashboard;
import org.primefaces.component.panel.Panel;
import org.primefaces.component.tabview.Tab;
import org.primefaces.component.tabview.TabView;
import org.primefaces.event.DashboardReorderEvent;
import org.primefaces.model.DashboardColumn;
import org.primefaces.model.DashboardModel;
import org.primefaces.model.DefaultDashboardColumn;
import org.primefaces.model.DefaultDashboardModel;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.entity.TdPortalColumn;
import com.chis.modules.portal.entity.TdPortalLayout;
import com.chis.modules.portal.entity.TdPortalLinks;
import com.chis.modules.portal.entity.TdPortalNews;
import com.chis.modules.portal.entity.TdPortalNewsAnnex;
import com.chis.modules.portal.entity.TdPortalNewsType;
import com.chis.modules.portal.entity.TdProtal;
import com.chis.modules.portal.enumn.PortalColType;
import com.chis.modules.portal.enumn.TemplType;
import com.chis.modules.portal.logic.PortalColumnSetBean;
import com.chis.modules.portal.service.PortalBaseImpl;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.web.FacesBean;

/**
 * 个人工作站展示 图片栏：图片高度300px;
 *
 * <AUTHOR>
 * @createDate 2014年9月19日下午3:02:49
 */
@ManagedBean(name = "personalPortalCenterBean")
@ViewScoped
public class PersonalPortalCenterBean extends FacesBean {
	
	private static final long serialVersionUID = 2350275983339647922L;
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private PortalBaseImpl service = (PortalBaseImpl) SpringContextHolder.getBean(PortalBaseImpl.class);
	/** 门户对象 */
	private TdProtal tdProtal;
	/** 门户布局容器列的宽度 */
	private String panelGridColumnStyle;
	/** 换算宽度百分比 */
	private static final BigDecimal TEN = new BigDecimal(10);
	/** 是否有图片栏 */
	private boolean imgColBool = Boolean.FALSE;
	/** 是否有公告栏 */
	private boolean gglColBool = Boolean.FALSE;
	
	/** 布局容器 */
	private Dashboard dashboard = (Dashboard) JsfUtil.getApplication()
			.createComponent(Dashboard.COMPONENT_TYPE);
	/** 布局模型 */
	private DashboardModel dashboardModel = new DefaultDashboardModel();
	/** 每一个布局容器中的栏目的panel的ID的前缀 */
	private static final String WIGET_FIX = "portal_col_";
	/** 缓存栏目设置 */
	private Map<String, PortalColumnSetBean> columnSetMap = new HashMap<String, PortalColumnSetBean>();
	/** 门户布局的选中的版式 */
	private String layout;

	public PersonalPortalCenterBean() {
		this.searchAction();
	}

	/**
	 * 查询方法
	 */
	public void searchAction() {
		this.columnSetMap = new HashMap<String, PortalColumnSetBean>();
		/**
		 * 0- 门户对象 1- 栏目对应的信息 map<栏目ID， 信息>
		 */
		List initDataList = this.service.findPersonalPortalData(this.sessionData.getUser().getRid());
		if(null == initDataList || initDataList.size() == 0) {
			JsfUtil.addErrorMessage("您还没有配置您的工作站！");
			return;
		}

		// 门户对象
		this.tdProtal = (TdProtal) initDataList.get(0);
		List<TdPortalLayout> layoutList = this.tdProtal.getTdPortalLayouts();
		// 信息集合
		Map newsMap = (Map) initDataList.get(1);

		// 获取版式
		TemplType templType = this.tdProtal.getTemplType();
		if(null != templType){
			String typeCN = templType.getTypeCN();
			this.layout = templType.getTypeNo().toString();
			String[] splits = typeCN.split(":");
			
			// 初始化每一列
			this.dashboardModel.getColumns().clear();
			this.dashboard.getChildren().clear();


			// 初始化布局
			for (int i = 0; i < splits.length; i++) {
				DashboardColumn col = new DefaultDashboardColumn();
				for (TdPortalLayout layout : layoutList) {
					
					/**
					 * 需要初始化每个已经布局的栏目的属性，如显示行数、显示日期
					 */
					String columnClientId = WIGET_FIX + layout.getTdPortalColumn().getRid();
					PortalColumnSetBean columnSetting = new PortalColumnSetBean();
					columnSetting.setDispDate(layout.getDispDate());
					columnSetting.setLines(layout.getLines());
					columnSetting.setWords(layout.getWords());
					columnSetting.setHeighth(layout.getHeighth());
					this.columnSetMap.put(columnClientId, columnSetting);
					
					if (layout.getPos().intValue() == i) {
						col.addWidget(columnClientId);
						// 栏目对象
						TdPortalColumn portalColumn = layout.getTdPortalColumn();
						// 设置该栏目内的内容
						if (portalColumn.getColType().equals(PortalColType.IMG)) {
							this.initImgColumn(portalColumn, newsMap, layout);
						} else if (portalColumn.getColType().equals(PortalColType.GGL)) {
							this.initGGLColumn(portalColumn, newsMap, layout);
						} else if (portalColumn.getColType().equals(PortalColType.PT)) {
							// 普通信息也分两种，一种是有子类型的（采用tab），一种是没有子类型的
							this.initPtColumn(portalColumn, newsMap, layout);
						} else if (portalColumn.getColType().equals(PortalColType.LINK)) {
							// 常用链接
							this.initLinkColumn(portalColumn, newsMap, layout);
						} else if (portalColumn.getColType().equals(PortalColType.OTHER)) {
							//其他栏目
							this.initOtherColumn(portalColumn, layout);
						}
						
					}
				}
				this.dashboardModel.addColumn(col);
			}
			this.dashboard.setModel(this.dashboardModel);
			initStyleInfo(splits);
		}else{
			JsfUtil.addErrorMessage("请先配置您的工作台");
			return;
		}
	}
	

	/**
	 * 栏目移动事件，必须要有，让PF自动保存一下当前的布局
	 */
	public void handleReorder(DashboardReorderEvent event) {
	        List<TdPortalLayout> list = new ArrayList<TdPortalLayout>();
			List<DashboardColumn> columns = this.dashboardModel.getColumns();
			if (null != columns && columns.size() > 0) {
				for (int i = 0; i < columns.size(); i++) {
					DashboardColumn col = columns.get(i);
					List<String> widgets = col.getWidgets();
					if (null != widgets && widgets.size() > 0) {
						for (int j = 0; j < widgets.size(); j++) {
							PortalColumnSetBean columnSetting = this.columnSetMap
									.get(widgets.get(j));

							String w = widgets.get(j).replace(WIGET_FIX, "");
							TdPortalLayout tpl = new TdPortalLayout();
							tpl.setTdProtal(this.tdProtal);
							tpl.setTdPortalColumn(new TdPortalColumn(Integer
									.valueOf(w)));
							tpl.setPos(i);
							tpl.setNums(j);
							if (null != columnSetting) {
								tpl.setLines(columnSetting.getLines());
								tpl.setDispDate(columnSetting.getDispDate());
								tpl.setWords(columnSetting.getWords());
								tpl.setHeighth(columnSetting.getHeighth());
							}
							list.add(tpl);
						}
					}
				}
				this.service.saveOrUpdatePortalLayout(list, this.tdProtal.getRid(), this.layout);
			}
	}
	
	/**
	 * 初始化图片栏目
	 * @param portalColumn 列对象
	 * @param newsMap 新闻内容
	 * @param layout 布局类
	 * @param grid 表格容器
	 */
	private void initImgColumn(TdPortalColumn portalColumn, Map newsMap, TdPortalLayout layout) {
		final Panel panel = (Panel) JsfUtil.getApplication().createComponent(Panel.COMPONENT_TYPE);
		// 设置title
        StringBuilder sb = new StringBuilder();
        sb.append("<a href=\"javascript:forwordPage('").append(portalColumn.getMoreUrl()).append("', '").append(portalColumn.getColName())
                .append("')\">");
        sb.append(portalColumn.getColName()).append("</a>");
        // 设置title
        panel.setHeader(sb.toString());
		String columnClientId = WIGET_FIX
				+ layout.getTdPortalColumn().getRid();
		panel.setId(columnClientId);
		
		/**
		 * 添加刷新按钮
		 */

		// 图片栏
		this.imgColBool = Boolean.TRUE;
		List<TdPortalNews> newsList = (List<TdPortalNews>) newsMap.get(portalColumn.getRid().toString());
		panel.setStyle("height:300px;");

		final HtmlOutputText text = (HtmlOutputText) JsfUtil.getApplication().createComponent(HtmlOutputText.COMPONENT_TYPE);
		text.setEscape(Boolean.FALSE);

		if (null != newsList && newsList.size() > 0) {
			StringBuilder cont = new StringBuilder();
			cont.append("<div class=\"roll-news\">");
			cont.append("		<div class=\"roll-news-image\">");

			for (int j = 0; j < newsList.size(); j++) {
				TdPortalNews news = newsList.get(j);
				TdPortalNewsAnnex newsAnnex = news.getTdPortalNewsAnnex();
				if (j == 0) {
					cont.append("<img src=\"/webFile").append(newsAnnex.getAnnexAddr()).append("\" title=\"");
				} else {
					cont.append("<img src=\"/webFile").append(newsAnnex.getAnnexAddr()).append("\" style=\"display:none\" title=\"");
				}
				cont.append(newsAnnex.getAnnexDesc()).append("\" onclick=\"javascript:forwordPage('").append(news.getNewAdr()).append("','");
				cont.append(newsAnnex.getAnnexDesc()).append("')\" />");
			}
			cont.append("</div>");
			cont.append("<div class=\"roll-news-index\">");
			cont.append("		<ul>");

			for (int j = 0; j < newsList.size(); j++) {
				if (j == 0) {
					cont.append("		<li class=\"roll-news-index-hover\">1</li>");
				} else {
					cont.append("<li>").append((j + 1)).append("</li>");
				}
			}
			cont.append("</ul>");
			cont.append("</div>");
			cont.append("</div>");

			text.setValue(cont.toString());
			//System.out.println("【图片内容】:" + cont.toString());
		} else {
			text.setValue("没有内容");
			text.setStyle("color:gray");
		}

		panel.getChildren().add(text);
		this.dashboard.getChildren().add(panel);
	}
	
	/**
	 * 初始化公告栏栏目
	 * @param portalColumn 列对象
	 * @param newsMap 新闻内容
	 * @param layout 布局类
	 * @param grid 表格容器
	 */
	private void initGGLColumn(TdPortalColumn portalColumn, Map newsMap, TdPortalLayout layout) {
		final Panel panel = (Panel) JsfUtil.getApplication().createComponent(Panel.COMPONENT_TYPE);
		// 设置title
		panel.setHeader(portalColumn.getColName());
		String columnClientId = WIGET_FIX
				+ layout.getTdPortalColumn().getRid();
		panel.setId(columnClientId);
		
		/**
		 * 添加刷新按钮
		 */
		
		// 公告栏
		this.gglColBool = Boolean.TRUE;

		List<TdPortalNews> newsList = (List<TdPortalNews>) newsMap.get(portalColumn.getRid().toString());
		StringBuilder styleBuilder = new StringBuilder();
		styleBuilder.append("vertical-align: top;text-align:left;height:300px;");
		//System.out.println("【公告栏样式】:" + styleBuilder.toString());
		panel.setStyle(styleBuilder.toString());

		final HtmlOutputText text = (HtmlOutputText) JsfUtil.getApplication().createComponent(HtmlOutputText.COMPONENT_TYPE);
		text.setEscape(Boolean.FALSE);

		if (null != newsList && newsList.size() > 0) {
			StringBuilder cont = new StringBuilder();

			cont.append("			<div class=\"list_lh\">");
			cont.append("				<ul>");
			for (TdPortalNews news : newsList) {
				cont.append("<li>");
				cont.append("	<p>");
				cont.append("		<a href=\"javascript:forwordPage('").append(news.getNewAdr());
				cont.append("','").append(news.getNewsTitle()).append("')\"  title=\"").append(news.getNewsTitle()).append("\">");

				// 如果设置了字段长度，并且标题超过了字段长度，需要截取字符串，加...
				cont.append(news.getNewsTitle());
				cont.append("</a><span>");
				if (layout.getDispDate().intValue() == 1) {
					cont.append(news.getNewsDateStr());
				}
				cont.append("</span>");

				if (news.isIfNew()) {
					cont.append("		<img src=\"/resources/images/login/new.gif\" style=\"margin-left:10px;\"/>");
				}
				cont.append("	</p>");
				cont.append("</li>");
			}
			cont.append("			</ul>");
			cont.append("		</div>");

			text.setValue(cont.toString());
			//System.out.println("【公告栏内容】:" + cont.toString());
		} else {
			text.setValue("没有内容");
			text.setStyle("color:gray");
		}

		panel.getChildren().add(text);
		this.dashboard.getChildren().add(panel);
	}
	
	/**
	 * 初始化普通信息栏目
	 * 
	 * 普通信息也分两种，一种是有子类型的（采用tab），一种是没有子类型的
	 * @param portalColumn 列对象
	 * @param newsMap 新闻内容
	 * @param layout 布局类
	 * @param grid 表格容器
	 */
	private void initPtColumn(TdPortalColumn portalColumn, Map newsMap, TdPortalLayout layout) {
		List<TdPortalNewsType> newsTypeList = portalColumn.getTdPortalNewsType().getTdPortalNewsTypes();
		if (null != newsTypeList && newsTypeList.size() > 0) {
			// 采用tabView
			final TabView tabView = (TabView) JsfUtil.getApplication().createComponent(TabView.COMPONENT_TYPE);
			Map<TdPortalNewsType, List<TdPortalNews>> comNewsMap = (Map<TdPortalNewsType, List<TdPortalNews>>) newsMap.get(portalColumn.getRid().toString());
			Set<TdPortalNewsType> keySet = comNewsMap.keySet();
			String columnClientId = WIGET_FIX
					+ layout.getTdPortalColumn().getRid();
			tabView.setId(columnClientId+"tabView");

			final Panel panel = (Panel) JsfUtil.getApplication().createComponent(Panel.COMPONENT_TYPE);
			panel.setId(columnClientId);
			
			/**
			 * 添加刷新按钮
			 */
			StringBuilder styleBuilder = new StringBuilder();
			styleBuilder.append("vertical-align: top;text-align:left;height:300px;border:1");
			panel.setStyle(styleBuilder.toString());
			
			tabView.setStyle("vertical-align: top;margin-top: -10px;margin-left: -17px;margin-right: -17px;height:300px;");
			for (TdPortalNewsType tnt : keySet) {
				final Tab tab = (Tab) JsfUtil.getApplication().createComponent(Tab.COMPONENT_TYPE);

				// 设置title，设置ID，格式为tab_类型id_上级栏目名称-上级栏目ID
				tab.setTitle(tnt.getTypeName());
				tab.setId("tab_" + tnt.getRid() + "_" + portalColumn.getColName() + "-" + portalColumn.getRid());

				List<TdPortalNews> newsList = (List<TdPortalNews>) comNewsMap.get(tnt);

				final HtmlOutputText text = (HtmlOutputText) JsfUtil.getApplication().createComponent(HtmlOutputText.COMPONENT_TYPE);
				text.setEscape(Boolean.FALSE);

				if (null != newsList && newsList.size() > 0) {
					StringBuilder cont = new StringBuilder();

					cont.append("			<div>");
					cont.append("				<ul>");
					for (TdPortalNews news : newsList) {
						cont.append("<li>");
						cont.append("	<p>");
						cont.append("		<a href=\"javascript:forwordPage('").append(news.getNewAdr());
						cont.append("','").append(news.getNewsTitle()).append("')\"  title=\"").append(news.getNewsTitle()).append("\">");
						// 如果设置了字段长度，并且标题超过了字段长度，需要截取字符串，加...
						cont.append(news.getNewsTitle());
						cont.append("</a><span>");
						if (layout.getDispDate().intValue() == 1) {
							cont.append(news.getNewsDateStr());
						}
						cont.append("</span>");
						if (news.isIfNew()) {
							cont.append("		<img src=\"/resources/images/login/new.gif\" style=\"margin-left:10px;\"/>");
						}
						cont.append("	</p>");
						cont.append("</li>");
					}
					cont.append("			</ul>");
					cont.append("		</div>");

					text.setValue(cont.toString());
				} else {
					text.setValue("没有内容");
					text.setStyle("color:gray");
				}
				tab.getChildren().add(text);
				tabView.getChildren().add(tab);
				panel.getChildren().add(tabView);
			}
			this.dashboard.getChildren().add(panel);
		} else {
			// 采用Panel
			final Panel panel = (Panel) JsfUtil.getApplication().createComponent(Panel.COMPONENT_TYPE);
			

			// 设置title
			StringBuilder titBuild = new StringBuilder();
			titBuild.append("<a href=\"javascript:forwordPage('").append(portalColumn.getMoreUrl()).append("', '").append(portalColumn.getColName())
					.append("')\">");
			titBuild.append(portalColumn.getColName()).append("</a>");
			panel.setHeader(titBuild.toString());
			String columnClientId = WIGET_FIX
					+ layout.getTdPortalColumn().getRid();
			panel.setId(columnClientId);
			
			/**
			 * 添加刷新按钮
			 */

			// 普通信息
			List<TdPortalNews> newsList = (List<TdPortalNews>) newsMap.get(portalColumn.getRid().toString());
			int height;
			// 设置了高度，就用设置的，没有设置，就自动计算
			if (null != layout.getHeighth()) {
				height = layout.getHeighth();
			} else {
				if(null != layout.getLines() &&  layout.getLines() != 0){
					height = layout.getLines() * 40 + 20;
				}else{
					height = 300;
				}
			}
			StringBuilder styleBuilder = new StringBuilder();
			//System.out.println("【" + portalColumn.getColName() + "】:" + height);
			styleBuilder.append("vertical-align: top;text-align:left;height:").append(height).append("px;border:1");
			panel.setStyle(styleBuilder.toString());

			final HtmlOutputText text = (HtmlOutputText) JsfUtil.getApplication().createComponent(HtmlOutputText.COMPONENT_TYPE);
			text.setEscape(Boolean.FALSE);

			if (null != newsList && newsList.size() > 0) {
				StringBuilder cont = new StringBuilder();

				cont.append("			<div>");
				cont.append("				<ul>");
				for (TdPortalNews news : newsList) {
					cont.append("<li>");
					cont.append("	<p>");
					cont.append("		<a href=\"javascript:forwordPage('").append(news.getNewAdr());
					cont.append("','").append(news.getNewsTitle()).append("')\"  title=\"").append(news.getNewsTitle()).append("\">");
					// 如果设置了字段长度，并且标题超过了字段长度，需要截取字符串，加...
					cont.append(news.getNewsTitle());
					cont.append("</a><span>");
					if (layout.getDispDate().intValue() == 1) {
						cont.append(news.getNewsDateStr());
					}
					cont.append("</span>");
					if (news.isIfNew()) {
						cont.append("		<img src=\"/resources/images/login/new.gif\" style=\"margin-left:10px;\"/>");
					}
					cont.append("	</p>");
					cont.append("</li>");
				}
				cont.append("			</ul>");
				cont.append("		</div>");

				text.setValue(cont.toString());
				//System.out.println("【信息内容】:" + cont.toString());
			} else {
				text.setValue("没有内容");
				text.setStyle("color:gray");
			}

			panel.getChildren().add(text);
			this.dashboard.getChildren().add(panel);
		}
	}
	
	/**
	 * 初始化常用链接栏目
	 * @param portalColumn 列对象
	 * @param newsMap 新闻内容
	 * @param layout 布局类
	 * @param grid 表格容器
	 */
	private void initLinkColumn(TdPortalColumn portalColumn, Map newsMap, TdPortalLayout layout) {
		final Panel panel = (Panel) JsfUtil.getApplication().createComponent(Panel.COMPONENT_TYPE);

		// 设置title
		StringBuilder titBuild = new StringBuilder();
		titBuild.append("<a href=\"javascript:forwordPage('/webapp/portal/portalLinksList.faces?opt=1', '").append(portalColumn.getColName()).append("')\">");
		titBuild.append(portalColumn.getColName()).append("</a>");
		panel.setHeader(titBuild.toString());
		String columnClientId = WIGET_FIX
				+ layout.getTdPortalColumn().getRid();
		panel.setId(columnClientId);
		
		/**
		 * 添加刷新按钮
		 */

		List<TdPortalLinks> newsList = (List<TdPortalLinks>) newsMap.get(portalColumn.getRid().toString());
		int height;
		// 设置了高度，就用设置的，没有设置，就自动计算
		if (null != layout.getHeighth()) {
			height = layout.getHeighth();
		} else {
			if(null != layout.getLines() &&  layout.getLines() != 0){
				height = layout.getLines() * 50 + 20;
			}else{
				height = 300;
			}
		}
		StringBuilder styleBuilder = new StringBuilder();
		styleBuilder.append("vertical-align: top;text-align:left;height:").append(height).append("px;overflow-y:auto;");
//		styleBuilder.append("vertical-align: top;height:300px;overflow-y:auto;");
		panel.setStyle(styleBuilder.toString());

		final HtmlOutputText text = (HtmlOutputText) JsfUtil.getApplication().createComponent(HtmlOutputText.COMPONENT_TYPE);
		text.setEscape(Boolean.FALSE);

		if (null != newsList && newsList.size() > 0) {
			StringBuilder cont = new StringBuilder();

			// cont.append("<div style=\"width:155px;padding:0px;margin-left:auto;margin-right:auto;\">");
			for (TdPortalLinks news : newsList) {
				if (news.getLinkType().intValue() == 0) {
					// 外部打开
					cont.append("<a href=\"").append(news.getLinkUrl()).append("\" target=\"_blank\" style=\"margin-top:3px;\">");
				} else {
					// 内部打开
					cont.append("<a href=\"javascript:forwordPage('").append(news.getLinkUrl()).append("','").append(news.getLinkName()).append("')\" style=\"margin-top:3px;\">");
				}
				cont.append("<img src=\"/webFile").append(news.getLinkIcon()).append("\" style=\"width:155px;height:45px;\" ");
				cont.append(" title=\"").append(news.getLinkName()).append("\" ");
				cont.append("/>&#160;&#160;");
				cont.append("</a>");
			}
			// cont.append("</div>");
			text.setValue(cont.toString());
		} else {
			text.setValue("没有内容");
			text.setStyle("color:gray");
		}

		panel.getChildren().add(text);
		this.dashboard.getChildren().add(panel);
	}
	
	
	/**
	 * 将其他栏目以iframe的格式进行展示
	 * @param portalColumn 列对象
	 * @param layout 布局类
	 */
	private void initOtherColumn(TdPortalColumn portalColumn, TdPortalLayout layout) {
		/**
		 *<script type=\"text/javascript\">
		 *	var ifm = document.createElement("iframe");
		 *	ifm.id="ifm";
		 *	ifm.width="100%";
		 *	ifm.height="100%";
		 *	ifm.setAttribute("frameborder", "0", 0);
		 *	ifm.src="http://127.0.0.1:8888/jsf2/pages/exam4.jsf";
		 *	document.getElementById("sportsDiv").appendChild(ifm);
		 *</script>
		 *<IFRAME ID='ifm2' WIDTH='189' HEIGHT='190' ALIGN='CENTER' MARGINWIDTH='0' 
		 *	MARGINHEIGHT='0' HSPACE='0' VSPACE='0' FRAMEBORDER='0' 
		 *  SCROLLING='NO' SRC='http://weather.qq.com/inc/ss248.htm'></IFRAME> 
		 */
		final Panel panel = (Panel) JsfUtil.getApplication().createComponent(Panel.COMPONENT_TYPE);
		if (portalColumn.getIfHideHeader() == null
				|| 0 == portalColumn.getIfHideHeader().intValue()) {
			panel.setHeader(portalColumn.getColName());
		} else {
			panel.setStyleClass("no_header");
		}
		String columnClientId = WIGET_FIX + layout.getTdPortalColumn().getRid();
		panel.setId(columnClientId);
		
		
		/**
		 * 添加刷新按钮
		 */
		final CommandLink commandLink = (CommandLink) JsfUtil.getApplication().createComponent(CommandLink.COMPONENT_TYPE);
		commandLink.setStyleClass("ui-panel-titlebar-icon ui-corner-all ui-state-default");
		final HtmlOutputText output = (HtmlOutputText) JsfUtil.getApplication().createComponent(HtmlOutputText.COMPONENT_TYPE);
		output.setStyleClass("ui-icon ui-icon-refresh");
		commandLink.getChildren().add(output);
		commandLink.setUpdate(columnClientId);
		commandLink.setProcess("@this");
		panel.getFacets().put("actions", commandLink);
		
		int height = 0;
		/**
		 * 高度的设置：
		 * 1.门户布局表中设置了高度，就用高度
		 * 2.url中传递了zwxh高度的值就用url传递的
		 * 3.门户布局表中设置了行数，就计算高度
		 * 4.都不满足，就设置为300px
		 */
		if (null != layout.getHeighth()) {
			height = layout.getHeighth();
		}else if(StringUtils.isNotBlank(portalColumn.getMoreUrl())) {
			String heightStr = StringUtils.parseUrl(portalColumn.getMoreUrl(), "zwxh");
			if(StringUtils.isNotBlank(heightStr) && StringUtils.isNumeric(heightStr)) {
				height = Integer.parseInt(heightStr);
			}
		}else if(null != layout.getLines() &&  layout.getLines() > 0) {
			height = layout.getLines() * 50 + 20;
		}
		if(height <= 0) {
			height = 300;
		}

		StringBuilder styleBuilder = new StringBuilder();
		styleBuilder.append("width:100%;");
		styleBuilder.append("height:").append(height).append("px;");
		panel.setStyle(styleBuilder.toString());

		final HtmlOutputText text = (HtmlOutputText) JsfUtil.getApplication().createComponent(HtmlOutputText.COMPONENT_TYPE);
		text.setEscape(Boolean.FALSE);
		/**
		 * 通过一个DIV里面放入IFRAME
		 */
		String frameDivId = "iframeDiv" + layout.getTdPortalColumn().getRid();
		StringBuilder sb = new StringBuilder();
		sb.append("<div id=\"").append(frameDivId).append("\" style=\"width:100%;height:").append(height-50).append("px;\"></div>");
		sb.append("<script type=\"text/javascript\">");
		sb.append("var ifm = document.createElement(\"iframe\");");
		sb.append("ifm.id=\"iframe").append(layout.getTdPortalColumn().getRid()).append("\";");
		sb.append("ifm.width=\"100%\";");
		sb.append("ifm.height=\"100%\";");
		sb.append("ifm.setAttribute(\"frameborder\", \"0\", 0);");
		sb.append("ifm.src=\"").append(portalColumn.getMoreUrl()).append("\";");
		sb.append("document.getElementById(\"").append(frameDivId).append("\").appendChild(ifm);");
		sb.append("</script>");
		text.setValue(sb.toString());
		
		panel.getChildren().add(text);
		this.dashboard.getChildren().add(panel);
	}
	
	/**
	 * 初始化样式
	 * 
	 * @param splits
	 *            版式
	 */
	private void initStyleInfo(String[] splits) {
		// 初始化样式
		StringBuilder sb = new StringBuilder();
		sb.append("<style type=\"text/css\">");
		sb.append("		.ui-panel, .ui-dashboard div.ui-state-hover {");
		sb.append("				margin: 10px;");
		sb.append("		}");
		for (int i = 0; i < splits.length; i++) {
			sb.append("		 .ui-dashboard    .ui-dashboard-column:nth-child(")
					.append(i + 1).append(")  { ");
			sb.append("		 		width: ")
					.append(new BigDecimal(splits[i]).multiply(TEN))
					.append("%;");
			sb.append("		 }");
		}
		sb.append("</style>");
		this.panelGridColumnStyle = sb.toString();
	}

	public String getPanelGridColumnStyle() {
		return panelGridColumnStyle;
	}

	public void setPanelGridColumnStyle(String panelGridColumnStyle) {
		this.panelGridColumnStyle = panelGridColumnStyle;
	}

	public boolean isImgColBool() {
		return imgColBool;
	}

	public void setImgColBool(boolean imgColBool) {
		this.imgColBool = imgColBool;
	}

	public boolean isGglColBool() {
		return gglColBool;
	}

	public void setGglColBool(boolean gglColBool) {
		this.gglColBool = gglColBool;
	}

	public Dashboard getDashboard() {
		return dashboard;
	}

	public void setDashboard(Dashboard dashboard) {
		this.dashboard = dashboard;
	}

	public DashboardModel getDashboardModel() {
		return dashboardModel;
	}

	public void setDashboardModel(DashboardModel dashboardModel) {
		this.dashboardModel = dashboardModel;
	}

	public Map<String, PortalColumnSetBean> getColumnSetMap() {
		return columnSetMap;
	}

	public void setColumnSetMap(Map<String, PortalColumnSetBean> columnSetMap) {
		this.columnSetMap = columnSetMap;
	}

	public String getLayout() {
		return layout;
	}

	public void setLayout(String layout) {
		this.layout = layout;
	}

}
