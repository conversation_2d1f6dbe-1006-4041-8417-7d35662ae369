package com.chis.modules.portal.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.entity.TdPortalNews;
import com.chis.modules.portal.entity.TdPortalNewsAnnex;
import com.chis.modules.portal.service.PortalBaseImpl;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.web.FacesBean;

/**
 * <AUTHOR>
 * @createDate 2015-3-5.
 */
@ManagedBean(name = "pictuerShowBean")
@ViewScoped
public class PictuerShowBean extends FacesBean {

    private static final long serialVersionUID = 5293857849695806438L;
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private PortalBaseImpl service = (PortalBaseImpl) SpringContextHolder.getBean(PortalBaseImpl.class);

    //总页数
    private Integer totalPage;
    //总条数
    private Integer totalRecords;
    //当前页数
    private Integer pageNo;
    //一页显示条数
    private Integer pagesize = 8;

    private List<Object[]> infoList;
    private List<Object[]> allList;

    /** 查询标题 */
    private String searchTitle;
    /** 查询科室id */
    private Integer searchOfficeId;
    /** 科室名称 */
    private String searchOfficeName;
    /** 查询发布人 */
    private String searchMan;
    /** 查询开始日期 */
    private Date searchStartDate;
    /** 查询结束日期 */
    private Date searchEndDate;
    /** 添加页面地区树 */
    private TreeNode treeNode;
    /** 选中的树 */
    private TreeNode selectedNode;


    public PictuerShowBean() {
        initTree();
        searchAction();
    }

    private void initTree() {
        this.treeNode = new DefaultTreeNode("root", null);

        TsOffice ts = new TsOffice();
        ts.setOfficename("所有科室");
        TreeNode allNode = new DefaultTreeNode(ts, this.treeNode);
        allNode.setExpanded(true);

        TsUserInfo tsUserInfo = this.sessionData.getUser();
        Integer unitRid = tsUserInfo.getTsUnit().getRid();
        List<TsOffice> list = this.service.findOfficeByUnitId(unitRid);
        if (null != list && list.size() > 0) {
            Set<String> firstLevelNoSet = new LinkedHashSet<String>(); // 只有第一层
            Set<String> levelNoSet = new LinkedHashSet<String>(); // 没有第一层
            Map<String, TsOffice> allMap = new HashMap<String, TsOffice>(); // 所有物资分类

            for (TsOffice t : list) {
                allMap.put(String.valueOf(t.getRid()), t);
                if (StringUtils.isNotBlank(String.valueOf(t.getRid()))) {
                    // 只有第一层
                    if (StringUtils.containsNone(String.valueOf(t.getRid()),
                            ".")) {
                        firstLevelNoSet.add(String.valueOf(t.getRid()));
                    } else {
                        levelNoSet.add(String.valueOf(t.getRid()));
                    }
                }
            }

            for (String ln : firstLevelNoSet) {
                TreeNode node = new DefaultTreeNode(allMap.get(ln), allNode);
                this.addChildNode(ln, levelNoSet, allMap, node);
            }
        }
    }

    private void addChildNode(String levelNo, Set<String> levelNoSet,
                              Map<String, TsOffice> allMap, TreeNode parentNode) {
        int level = StringUtils.countMatches(levelNo, ".");
        for (String ln : levelNoSet) {
            if (StringUtils.countMatches(ln, ".") == (level + 1)
                    && StringUtils.startsWith(ln, levelNo + ".")) {
                // 根据编码，获取树，组成新的树节点
                TreeNode node = new DefaultTreeNode(allMap.get(ln), parentNode);
                this.addChildNode(ln, levelNoSet, allMap, node);
            }
        }
    }

    public void searchSelectTreeNode(TreeNode node) {
        this.searchOfficeId = null;
        this.searchOfficeName = "";
        if (null != node) {
            TsOffice t = (TsOffice) node.getData();
            this.searchOfficeId = t.getRid();
            this.searchOfficeName = t.getOfficename();
        }
    }

    public void searchAction(){
        if(searchStartDate != null && searchEndDate != null && searchStartDate.after(searchEndDate)){
            JsfUtil.addErrorMessage("开始日期不允许大于结束日期！");
            return;
        }
        totalRecords=0;
        totalPage=0;
        pageNo=1;
        allList = new ArrayList<Object[]>();
        List<TdPortalNews> list = service.findPortalNews(4,sessionData.getUser().getTsUnit().getRid(),searchTitle,searchOfficeId,searchMan,searchStartDate,searchEndDate);
        if(list != null && list.size() > 0){
            for(TdPortalNews t : list){
                Object[] obj = new Object[5];
                obj[0] = t.getRid();
                obj[1] = t.getNewsTitle();
                String title = t.getNewsTitle();
                if(title.length() > 16){
                    obj[2] = title.substring(0,15)+"...";
                }else{
                    obj[2] = title;
                }
                List<TdPortalNewsAnnex> list1 = t.getTdPortalNewsAnnexes();
                if(list1 != null && list1.size() > 0){
                    obj[3] = list1.get(0).getAnnexAddr();
                }else{
                    obj[3] = "";
                }
                StringBuilder forpage= new StringBuilder();
                forpage.append("/webapp/portal/tdPortalNewsViewList.faces?newsType=4&rid=").append(t.getRid());
                obj[4] = forpage.toString();
                allList.add(obj);
            }
        }
        if(null!=this.allList&&this.allList.size()>0){
            this.pageSearchAction();
            this.totalRecords=this.allList.size();//总条数
            this.totalPage= this.totalRecords/this.pagesize+(this.totalRecords%this.pagesize>0?1:0);//总页数
        }else {
            infoList = new LinkedList<Object[]>();
        }
    }

    public void pageSearchAction(){
        this.infoList=new LinkedList<Object[]>();
        if(null!=allList&&allList.size()>0) {
            int endNuM = Math.min(allList.size(), pageNo*pagesize);
            for (int i = (pageNo-1)*pagesize; i <endNuM; ++i) {
                this.infoList.add(allList.get(i));
            }
        }
    }

    public Integer getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(Integer totalPage) {
        this.totalPage = totalPage;
    }

    public Integer getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(Integer totalRecords) {
        this.totalRecords = totalRecords;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPagesize() {
        return pagesize;
    }

    public void setPagesize(Integer pagesize) {
        this.pagesize = pagesize;
    }

    public List<Object[]> getInfoList() {
        return infoList;
    }

    public void setInfoList(List<Object[]> infoList) {
        this.infoList = infoList;
    }

    public String getSearchTitle() {
        return searchTitle;
    }

    public void setSearchTitle(String searchTitle) {
        this.searchTitle = searchTitle;
    }

    public Integer getSearchOfficeId() {
        return searchOfficeId;
    }

    public void setSearchOfficeId(Integer searchOfficeId) {
        this.searchOfficeId = searchOfficeId;
    }

    public String getSearchOfficeName() {
        return searchOfficeName;
    }

    public void setSearchOfficeName(String searchOfficeName) {
        this.searchOfficeName = searchOfficeName;
    }

    public String getSearchMan() {
        return searchMan;
    }

    public void setSearchMan(String searchMan) {
        this.searchMan = searchMan;
    }

    public Date getSearchStartDate() {
        return searchStartDate;
    }

    public void setSearchStartDate(Date searchStartDate) {
        this.searchStartDate = searchStartDate;
    }

    public Date getSearchEndDate() {
        return searchEndDate;
    }

    public void setSearchEndDate(Date searchEndDate) {
        this.searchEndDate = searchEndDate;
    }

    public TreeNode getTreeNode() {
        return treeNode;
    }

    public void setTreeNode(TreeNode treeNode) {
        this.treeNode = treeNode;
    }

    public TreeNode getSelectedNode() {
        return selectedNode;
    }

    public void setSelectedNode(TreeNode selectedNode) {
        this.selectedNode = selectedNode;
    }
}
