package com.chis.modules.portal.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.entity.TdPortalColumn;
import com.chis.modules.portal.entity.TdPortalNewsType;
import com.chis.modules.portal.entity.TdPortalTypAuth;
import com.chis.modules.portal.service.PortalBaseImpl;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesBean;

/**
 * 信息类型维护托管Bean
 * <AUTHOR>
 *
 *
 * <p>修订内容：</p>
 * 新增系统参数控制管理员授权范围
 * @ClassReviser xq,2018年1月16日,PortalNewsTypeBean
 */
@ManagedBean(name = "portalNewsTypeBean")
@ViewScoped
public class PortalNewsTypeBean extends FacesBean{
    private static final long serialVersionUID = 162405939712114871L;
    /** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private PortalBaseImpl service = (PortalBaseImpl) SpringContextHolder.getBean(PortalBaseImpl.class);
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /** 类型名称 */
    private String searchTypeName;
    /** 添加子节点 */
    private Boolean isCode;
    /** 树根节点 */
    private TreeNode rootNode;
    /** 选中的树节点 */
    private TreeNode selectedNode;
    /** 类型实体 */
    private TdPortalNewsType tdPortalNewsType;
    /** rid */
    private Integer rid;
    /** 层级控制 */
    private Map<Integer,Integer> numMap;
    /** 最大层数 */
    private static final Integer limitLevel =2;
    /** 是否管理员授权 */
    private boolean ifAdmin;


    @PostConstruct
    public void init(){
        tdPortalNewsType = new TdPortalNewsType();
        searchTypeName = "";
        //默认添加的是根节点
        this.isCode = false;
        treeTableInit();
    }

    /** 遍历树，查询要找的类型 */
    public void searchAction(){
        if(null != this.rootNode){
            if(StringUtils.isNotBlank(this.searchTypeName)){
                freeExpandedTree(this.rootNode);
                findTreeNode(this.rootNode);
            }else{
                freeExpandedTree(this.rootNode);
            }
        }
    }

    /**
     * 遍历树,清空每个节点的展出、选择
     * @param treeNode
     *            树节点
     */
    private void freeExpandedTree(TreeNode treeNode) {
        treeNode.setSelected(false);
        treeNode.setExpanded(false);
        if(null != treeNode.getChildren()){
            for (TreeNode t : treeNode.getChildren()) {
                freeExpandedTree(t);
            }
        }
    }

    /**
     * 遍历树,将类型名称相同的节点都选中并展开
     * @param treeNode
     *            树节点
     */
    private void findTreeNode(TreeNode treeNode) {
        if(null != treeNode.getChildren()){
            for (TreeNode t : treeNode.getChildren()) {
                TdPortalNewsType type = (TdPortalNewsType) t.getData();
                if(StringUtils.contains(type.getTypeName(),this.searchTypeName)){
                    t.setSelected(true);
                    expandedTree(t);
                    findTreeNode(t);
                } else {
                    findTreeNode(t);
                }
            }
        }
    }

    /**
     * 遍历树，将每个节点逐个展出
     * @param treeNode
     *            树节点
     */
    private void expandedTree(TreeNode treeNode) {
        if (null != treeNode.getParent()) {
            treeNode.getParent().setExpanded(true);
            expandedTree(treeNode.getParent());
        }
    }

   /** 查询初始化，建立类型树 */
    public void treeTableInit(){
        //建立根节点
        this.rootNode = new DefaultTreeNode("root", null);
        //得到所有类型的集合
        List<TdPortalNewsType> list = this.service.findPortalNewsTypes();
        //集合不为空
        if(list != null && list.size() > 0){
            numMap = new HashMap<Integer, Integer>();
            //遍历集合
            for (TdPortalNewsType t : list) {
                //将每个类型的rid和它的级数保存到map中
                numMap.put(t.getRid(), countNum(t));
                //如果该类型为根节点
                if(t.getTdPortalNewsType() == null){
                    //创建根节点，并建立该节点的树
                    TreeNode firstNode = new DefaultTreeNode(t,this.rootNode);
                    this.buildTree(t,firstNode);
                }
            }
        }
    }

    //创建节点树
    public void buildTree(TdPortalNewsType newsType,TreeNode node){
        List<TdPortalNewsType> childList = newsType.getTdPortalNewsTypes();
        Collections.sort(childList);
        //有子节点，递归调用创建节点树的方法，直到为末节点时，返回
        if(childList != null && childList.size() > 0){
            for(TdPortalNewsType t : childList){
                TreeNode childNode = new DefaultTreeNode(t,node);
                buildTree(t, childNode);
            }
        }else{
            return;
        }
    }

    //递归查询该类型的父级总数，确定该类型为第几级
    public Integer countNum(TdPortalNewsType t){
        if(t.getTdPortalNewsType()==null){
            return 1;
        }else{
            return countNum(t.getTdPortalNewsType())+1;
        }
    }

    /** 增加 */
    public void addInit(){
        this.tdPortalNewsType = new TdPortalNewsType();
    }

    /** 修改 */
    public void modInit(){
        this.isCode = false;
        this.tdPortalNewsType = (TdPortalNewsType) this.selectedNode.getData();
    }

    /** 删除 */
    public void deleteAction(){
        TdPortalNewsType newsType = (TdPortalNewsType)this.selectedNode.getData();
        String msg = this.service.deleteNewsType(newsType.getRid());
        if(StringUtils.isNotBlank(msg)){
            JsfUtil.addErrorMessage(msg);
            return;
        }
        this.treeTableInit();
        this.tdPortalNewsType = newsType.getTdPortalNewsType();
        if(null != tdPortalNewsType){
            this.localNewsType(this.rootNode);
        }
    }

    /** 保存 */
    public void saveAction(){
        //如果添加的是子节点，则设置父类型
        if(this.isCode){
            TdPortalNewsType t = (TdPortalNewsType)this.selectedNode.getData();
            if(numMap.get(t.getRid()) >= this.limitLevel){
                this.isCode = false;
                JsfUtil.addErrorMessage("无法添加子节点！");
                RequestContext.getCurrentInstance().execute("TypeEditDialog.hide()");
                return;
            }
            this.tdPortalNewsType.setTdPortalNewsType(new TdPortalNewsType(t.getRid()));
            //this.tdPortalNewsType.getTdPortalNewsTypes().add(this.tdPortalNewsType);
        }
        //如果是新增，则设置创建信息，否则设置修改信息
        if(this.tdPortalNewsType.getRid() == null){
            this.tdPortalNewsType.setCreateDate(new Date());
            this.tdPortalNewsType.setCreateManid(sessionData.getUser().getRid());
            //如果添加的是根类型，不是子类型，则给根类型添加一个门户栏目
            if(!this.isCode){
                TdPortalColumn tdPortalColumn = new TdPortalColumn();
                tdPortalColumn.setColName(this.tdPortalNewsType.getTypeName());
                tdPortalColumn.setTdPortalNewsType(this.tdPortalNewsType);
                this.tdPortalNewsType.getTdPortalColumns().add(tdPortalColumn);
            }
        }else{
            this.tdPortalNewsType.setModifyDate(new Date());
            this.tdPortalNewsType.setModifyManid(sessionData.getUser().getRid());
            List<TdPortalColumn> list = this.tdPortalNewsType.getTdPortalColumns();
            if(list != null  && list.size() > 0){
                list.get(0).setColName(this.tdPortalNewsType.getTypeName());
            }
        }
        String msg = this.service.saveOrUpdateNewsType(this.tdPortalNewsType);
        if(StringUtils.isNotBlank(msg)){
            JsfUtil.addErrorMessage(msg);
            return;
        }else{
            this.isCode = false;
            JsfUtil.addSuccessMessage("保存成功！");
            RequestContext requestContext = RequestContext.getCurrentInstance();
            requestContext.execute("TypeEditDialog.hide()");
            this.treeTableInit();
            this.localNewsType(this.rootNode);
        }
    }

    /**
     * 定位到新增或修改的位置
     * @param node
     */
    public void localNewsType(TreeNode node){
        if(null != node.getChildren()){
            for (TreeNode t : node.getChildren()) {
                TdPortalNewsType type = (TdPortalNewsType) t.getData();
                if (type.getTypeName().equals(this.tdPortalNewsType.getTypeName())) {
                    t.setSelected(true);
                    expandedTree(t);
                    break;
                } else {
                    localNewsType(t);
                }
            }
        }
    }

    /**
     * 用户选择
     */
    public void sqInitAction() {
        if(null == this.selectedNode){
            JsfUtil.addErrorMessage("请选择授权的信息类型！");
            return;
        }
        this.tdPortalNewsType = (TdPortalNewsType) this.selectedNode.getData();
        //对话框选项
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("height", 500);
        options.put("width", 920);
        options.put("contentWidth", 870);
        //对话框参数
        Map<String, List<String>> paramsMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>(1);
        //对话框标题
        if(ifAdmin){
            paramList.add("管理员授权");
        }else{
            paramList.add("用户授权");
        }
        paramsMap.put("title", paramList);
        paramList = new ArrayList<String>(1);
        //得到该信息类型已授权用户ID,所有rid以“,”隔开，组成一个字符串,放入map中
        String selectIds;
        selectIds = this.service.findPortalNewsTypeSqIds(this.tdPortalNewsType.getRid(),ifAdmin);
        paramList.add(selectIds);
        paramsMap.put("selectIds", paramList);
        paramList = new ArrayList<String>(1);
        paramList.add(ifAdmin?"1":"0");
        paramsMap.put("ifAdmin", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        
//        if(ifAdmin){
	        String systemParam = commService.findParamValue("PORTAL_SHOW_All_USER");
			if("1".equals(systemParam)){
				requestContext.openDialog("/webapp/system/selectMutilUserList", options, paramsMap);
			}else{
				requestContext.openDialog("/webapp/system/selectUsersList", options, paramsMap);
			}
//        }else{
//        	requestContext.openDialog("/webapp/system/selectUsersList", options, paramsMap);
//        }
    }

    /**
     * 用户授权
     * @param event 弹出框关闭事件
     */
    public void onUserSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            Collection<TsUserInfo> coll = (Collection<TsUserInfo>) selectedMap.get("selectUserList");
            List<TdPortalTypAuth> authList = new ArrayList<TdPortalTypAuth>();
            TdPortalTypAuth portalTypAuth = null;
            String msg;
            if(ifAdmin){
                for(TsUserInfo t : coll){
                    portalTypAuth = new TdPortalTypAuth(this.tdPortalNewsType,t,1);
                    authList.add(portalTypAuth);
                }
                msg = this.service.adminSqNewsType(authList,this.tdPortalNewsType.getRid());
            }else{
                for(TsUserInfo t : coll){
                    portalTypAuth = new TdPortalTypAuth(this.tdPortalNewsType,t,0);
                    authList.add(portalTypAuth);
                }
                msg = this.service.sqNewsType(authList,this.tdPortalNewsType.getRid());
            }
            if(null != msg){
                JsfUtil.addErrorMessage(msg);
            }else{
                JsfUtil.addSuccessMessage("授权成功！");
            }
        }
    }

    public boolean isIfAdmin() {
        return ifAdmin;
    }

    public void setIfAdmin(boolean ifAdmin) {
        this.ifAdmin = ifAdmin;
    }

    public SessionData getSessionData() {
        return sessionData;
    }

    public void setSessionData(SessionData sessionData) {
        this.sessionData = sessionData;
    }

    public String getSearchTypeName() {
        return searchTypeName;
    }

    public void setSearchTypeName(String searchTypeName) {
        this.searchTypeName = searchTypeName;
    }

    public Boolean getIsCode() {
        return isCode;
    }

    public void setIsCode(Boolean isCode) {
        this.isCode = isCode;
    }

    public TreeNode getRootNode() {
        return rootNode;
    }

    public void setRootNode(TreeNode rootNode) {
        this.rootNode = rootNode;
    }

    public TreeNode getSelectedNode() {
        return selectedNode;
    }

    public void setSelectedNode(TreeNode selectedNode) {
        this.selectedNode = selectedNode;
    }

    public TdPortalNewsType getTdPortalNewsType() {
        return tdPortalNewsType;
    }

    public void setTdPortalNewsType(TdPortalNewsType tdPortalNewsType) {
        this.tdPortalNewsType = tdPortalNewsType;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }
}
