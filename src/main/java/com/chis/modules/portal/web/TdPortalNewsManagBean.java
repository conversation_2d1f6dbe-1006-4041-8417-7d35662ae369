package com.chis.modules.portal.web;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.imageio.stream.FileImageOutputStream;
import javax.validation.constraints.Past;

import org.primefaces.component.imagecropper.ImageCropper;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.CroppedImage;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;
import org.primefaces.model.UploadedFile;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.FileUtils;
import com.chis.common.utils.ImageUtil;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.entity.TdPortalNews;
import com.chis.modules.portal.entity.TdPortalNewsAnnex;
import com.chis.modules.portal.entity.TdPortalNewsOffice;
import com.chis.modules.portal.entity.TdPortalNewsType;
import com.chis.modules.portal.entity.TdPortalNewsUnit;
import com.chis.modules.portal.enumn.NewsType;
import com.chis.modules.portal.service.PortalBaseImpl;
import com.chis.modules.system.entity.TbSysEmp;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.logic.UnitPO;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.DownLoadUtil;
import com.chis.modules.system.web.FacesEditBean;

/**
 * 信息管理
 * 
 * @history 文件存储到虚拟路径修改 图片剪辑功能修改
 * @LastModify xt
 * @ModifyDate 2014年9月4日
 * 
 * @history 新增PDF类型
 * @LastModify xt
 * @ModifyDate 2014年10月13日
 * 
 * 
 * <p>修订内容：</p>
 * 新增系统参数控制授权范围
 * @ClassReviser xq,2018年1月16日,TdPortalNewsManagBean
 */
@ManagedBean(name = "tdPortalNewsManagBean")
@ViewScoped
public class TdPortalNewsManagBean extends FacesEditBean {

	private static final long serialVersionUID = 4402129833200707875L;
	/** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private PortalBaseImpl portalBaseImpl = (PortalBaseImpl) SpringContextHolder.getBean(PortalBaseImpl.class);	
	private SystemModuleServiceImpl service = (SystemModuleServiceImpl) SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	
	/** 查询条件：信息分类名称 */
	private String searchNewsTypeName;
	/** 查询条件：信息分类名称 */
	private Integer searchNewsTypeId;
	/** 查询条件：信息分类标题 */
	private String searchInfoTitle;
	/** 上传开始日期 */
	@Past(message = "发布开始日期不能大于当天")
	private Date searchStartDate;
	/** 上传结束日期 */
	@Past(message = "发布结束日期不能大于当天")
	private Date searchEndDate;

	/** 主键ID */
	private Integer rid;
	/** 信息实体 */
	private TdPortalNews tdPortalNews;
	/** 图片信息数据 */
	private List<TdPortalNewsType> TdPortalNewsTypeList;

	/** 信息分类树 */
	private TreeNode portalTypeTree;
	/** 信息类型 */
	private List<SelectItem> newsType;
	/** 文件类型 */
	private String fileType;
	/** 文件名 */
	private String fileName;
	/** 选中的科室ID */
	private String selectOfficeIds;
	/** 选中的科室名称 */
	private String selectOfficeNames;

	/** 附件list */
	private List<TdPortalNewsAnnex> annexList = new LinkedList<TdPortalNewsAnnex>();
	/** 附件表 */
	private TdPortalNewsAnnex tdPortalNewsAnnex;
	/** 栏目下拉 */
	private List<SelectItem> colNews = new ArrayList<SelectItem>();
	/** 栏目数量 */
	private int colNewsSize = 0;

	/** 图片树 */
	private TreeNode photoTree;
	/** 截取图片对象 */
	private CroppedImage croppedImage;
	/** 是否选中树，默认为否 */
	private boolean ifSelTree;
	/** 信息类型，临时字段 */
	private int tempNewsType;
	/**
	 * 总页数
	 */
	private int totalPdfPage;
	/**
	 * 当前页
	 */
	private int currPdfPage;
	/**
	 * pdfList
	 */
	private List<String> pdfList;
	/** pdfHTML字符串 */
	private String pdfhtml;
	/** 字符串前 */
	private String PDFMODULE_FRONT = "<object type='application/pdf' height='1000px' width='100%' data='";
	/** 字符串后 */
	private String PDFMODULE_BACK = "' ></object>";
	/** 是否修改照片 */
	private boolean ifModPhoto = false;

	private ImageCropper imageCropper;
	/** ++是否显示截取组件 */
	private boolean ifShowCrop = false;
    /*+是否展示信息类型栏*/
    private Integer showNewsType;
    /*+图片类型*/
    private TdPortalNewsType pictureType;
    
    /** ++是否拥有管理员权限 */
    private boolean ifHasAdminInType;
    
    /** ++阅读状态情况 */
    private List<Object[]> readStateList;
    /**发布范围是否可以按单位选择**/
	private Boolean ifCanGrantAllUnit = Boolean.FALSE;
    
	public TdPortalNewsManagBean() {
		ifSQL = true;
		pageInit();
		searchVerifyAction();
	}
  
	/**
	 * 阅读情况
	 */
	public void readCaseAction(){
		readStateList = null;
		if( null != this.tdPortalNews)	{
			readStateList = this.portalBaseImpl.findPortalNewsReadState(tdPortalNews.getRid(),ifCanGrantAllUnit);
		}
	}
	
	
	/**
	 * 查询前验证科室是否为空，为空则不能进行查询
	 */
	public void searchVerifyAction() {
		TbSysEmp tbSysEmp = sessionData.getUser().getTbSysEmp();
		// 如果没有科室，则查询不到数据
		if (!ifCanGrantAllUnit && (null == tbSysEmp || null == tbSysEmp.getTsOffice() || null == tbSysEmp.getTsOffice().getRid())) {
			JsfUtil.addErrorMessage("请维护您的所属科室！");
		} else {
			this.searchAction();
		}
	}

	/**
	 * 初始化页面方法
	 */
	private void pageInit() {
		// 初始化信息分类树
		initSortTree();
		searchNewsTypeName = null;
		searchNewsTypeId = null;
		// 当前天前一个月
		searchStartDate = DateUtils.addMonths(new Date(), -1);
		// 当天
		searchEndDate = new Date();
		String systemParam = commService.findParamValue("PORTAL_SHOW_All_USER");
		if("1".equals(systemParam)){
			ifCanGrantAllUnit = Boolean.TRUE;
		}
	}

	@Override
	public String[] buildHqls() {
		StringBuffer tHql = new StringBuffer();
		tHql.append(" FROM TD_PORTAL_NEWS T LEFT JOIN TD_PORTAL_NEWS_TYPE T1 ON T.NEWS_TYPE_ID = T1.RID ");
		tHql.append(" LEFT JOIN TS_OFFICE T2 ON T2.RID = T.NEWS_DEPT LEFT JOIN TS_USER_INFO T3 ON T3.RID = T.NEWS_MAN ");
		tHql.append(" LEFT JOIN TD_PORTAL_NEWS_AUTH T4 ON T4.NEWS_ID = T.RID AND T4.USER_ID = '");
		tHql.append(sessionData.getUser().getRid()).append("' AND T4.READ_STATE=1");
		tHql.append(" WHERE T.STATE_MARK = 1 AND (T.NEWS_DEPT IN (");
		//增加兼职科室
		tHql.append(" SELECT  C.OFFICEID FROM TS_USER_INFO T1 INNER JOIN (");
		tHql.append(" SELECT A.RID,B.OFFICENAME,B.RID AS OFFICEID,B.NUM, A.NUM EMPNUM, 1 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_OFFICE B ON A.DEPT_ID = B.RID");
		tHql.append(" UNION ALL");
		tHql.append(" SELECT * FROM (SELECT A.RID,C.OFFICENAME,C.RID AS OFFICEID,C.NUM, A.NUM EMPNUM, 0 AS ZKS FROM TB_SYS_EMP A INNER JOIN TS_PARTTIME_INFO B ON A.RID = B.EMP_ID");
		tHql.append(" INNER JOIN TS_OFFICE C ON C.RID = B.OFFICE_ID ORDER BY C.NUM)) C ON C.RID = T1.EMP_ID");
		tHql.append(" WHERE 1=1 AND T1.IF_REVEAL='1' AND T1.RID = '").append(sessionData.getUser().getRid()).append("' )");
		
		//新增发布单位
		tHql.append(" OR T.NEWS_UNIT = ").append(sessionData.getUser().getTsUnit().getRid()).append(" ) ");
		// 过滤信息标题
		if (null != searchInfoTitle) {
			tHql.append(" AND T.NEWS_TITLE LIKE :newsTitle ESCAPE '\\\'");
			this.paramMap.put("newsTitle", "%" + this.convertBFH(this.searchInfoTitle.trim()) + "%");
		}
		if (null != searchStartDate) {// 发布开始日期
			tHql.append(" AND T.NEWS_DATE >= TO_DATE('").append(DateUtils.formatDate(searchStartDate))
					.append("','YYYY-MM-DD') ");
		}
		if (null != searchEndDate) {// 发布结束日期
			tHql.append(" AND T.NEWS_DATE <= TO_DATE('").append(DateUtils.formatDate(searchEndDate))
					.append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
		}
		// 过滤信息分类
		if (null != searchNewsTypeId) {
			tHql.append(" START WITH T1.RID = ").append(searchNewsTypeId).append("");
			tHql.append(" CONNECT BY T1.PARENT_ID = PRIOR T.NEWS_TYPE_ID");
		}
		// 用户Id
		Integer userId = sessionData.getUser().getRid();

		String h2 = "SELECT COUNT(*) " + tHql.toString();
		tHql.append(" ORDER BY T1.XH,T1.TYPE_NAME,T.NEWS_DATE DESC");
		StringBuffer h1 = new StringBuffer(
				"SELECT T1.TYPE_NAME,T.NEWS_TITLE,T.IF_NOTICE,T.IF_ZD,T2.OFFICENAME,T3.USERNAME,TO_CHAR(T.NEWS_DATE,'YYYY-MM-DD'),T.RID ");
		h1.append(
				",(SELECT SUM(DECODE(A.IF_ADMIN,'1',1,0)) FROM TD_PORTAL_TYP_AUTH A WHERE A.NEWS_ID= T1.RID AND A.USER_ID ='")
				.append(userId).append("' ) AS IFADMIN,DECODE(T4.RID,NULL,NULL,1) IFREAD  ");
		h1.append(tHql);
		return new String[] { h1.toString(), h2 };
	}

	/**
	 * 删除
	 */
	public void delAction() {
		String deleteNews = portalBaseImpl.deleteNews(rid);
		if(StringUtils.isNotBlank(deleteNews))	{
			JsfUtil.addErrorMessage(deleteNews);
		}else{
			JsfUtil.addSuccessMessage("删除成功!");
			this.searchAction();
		}
		
	}

	@Override
	public void addInit() {

	}

	@Override
	public void viewInit() {
		modInit();
	}

	@Override
	public void modInit() {
		pdfhtml = null;
		this.pdfList = new ArrayList<String>();
		this.tdPortalNews = this.portalBaseImpl.findTdPortalNews(this.rid,sessionData.getUser().getRid());
		this.ifHasAdminInType = this.portalBaseImpl.ifHasAdminInNews(this.tdPortalNews
				.getTdPortalNewsType().getRid(), sessionData.getUser().getRid());
		// office路径 或者 PDF 路径
		String newsAnnex = tdPortalNews.getNewsAnnex();
		if (StringUtils.isNotBlank(newsAnnex)) {
			if (tdPortalNews.getNewsType() == 1 || tdPortalNews.getNewsType() == 2 || tdPortalNews.getNewsType() == 3) {
				String tempFileName = newsAnnex.substring(newsAnnex.lastIndexOf("/") + 1, newsAnnex.length());
				String[] split = tempFileName.split("[.]");
				if (split.length == 2) {
					fileName = split[0];
					fileType = "." + split[1];
				}
			} else if (tdPortalNews.getNewsType() == 5) {
				String[] pdfAddrArr = newsAnnex.split(",");
				for (String addr : pdfAddrArr) {
					if(addr.contains("webFile")){
						pdfList.add(addr);
					}else{
						pdfList.add(Constants.XNPATH_DIR+addr);
					}
				}
				totalPdfPage = pdfList.size();
				currPdfPage = 1;
				pdfhtml = new StringBuffer(PDFMODULE_FRONT).append(pdfList.get(currPdfPage - 1)).append(PDFMODULE_BACK)
						.toString();
			}
		}

		tempNewsType = tdPortalNews.getNewsType();
		selectOfficeIds = null;
		selectOfficeNames = null;
		// 科室Id与名称
		StringBuilder officeIds = new StringBuilder();
		StringBuilder officeNames = new StringBuilder();
		if(ifCanGrantAllUnit){
			List<TdPortalNewsUnit> tdPortalNewsUnits = this.tdPortalNews.getTdPortalNewsUnits();
			if (null != tdPortalNewsUnits && tdPortalNewsUnits.size() > 0) {
				for (TdPortalNewsUnit tdPortalNewsUnit : tdPortalNewsUnits) {
					TsUnit tsUnit = tdPortalNewsUnit.getFkByUnitId();
					officeIds.append(",").append(tsUnit.getRid());
					officeNames.append(",").append(tsUnit.getUnitname());
				}
				selectOfficeIds = officeIds.substring(1);
				selectOfficeNames = officeNames.substring(1);
			}
			
		}else{
			List<TdPortalNewsOffice> tdPortalNewsOffices = this.tdPortalNews.getTdPortalNewsOffices();
			if (null != tdPortalNewsOffices && tdPortalNewsOffices.size() > 0) {
				for (TdPortalNewsOffice tdPortalNewsOffice : tdPortalNewsOffices) {
					TsOffice tsOffice = tdPortalNewsOffice.getTsOffice();
					officeIds.append(",").append(tsOffice.getRid());
					officeNames.append(",").append(tsOffice.getOfficename());
				}
				selectOfficeIds = officeIds.substring(1);
				selectOfficeNames = officeNames.substring(1);
			}
		}
		// 获取附件集合
		annexList = this.tdPortalNews.getTdPortalNewsAnnexes();

		// 如果信息分类为空，则初始化
		if (null == tdPortalNews.getTdPortalNewsType()) {
			TdPortalNewsType newType = new TdPortalNewsType();
			tdPortalNews.setTdPortalNewsType(newType);
		}
		// 初始化页面数据
		initPage();
		initColNews();
		if (null == this.tdPortalNews.getTdPortalNewsType() && null != this.colNews && this.colNews.size() > 0) {
			// 栏目Id
			String rid = String.valueOf(colNews.get(0).getValue());
			TdPortalNewsType t = new TdPortalNewsType();
			t.setRid(Integer.valueOf(rid));
			this.tdPortalNews.setTdPortalNewsType(t);
		}
		
		//初始化滚动天数
		if (this.tdPortalNews.getIfNotice().intValue() == 0) {
			this.tdPortalNews.setRollDays(Integer.parseInt(this.commService
					.findParamValue("PORTAL_SHOW_GGL_DAYS")));
		}

        //信息类型为图片，不显示栏目
        if(tdPortalNews.getNewsType().equals(4)){
            showNewsType = 0;
        }else{
            showNewsType = 1;
        }
        //初始化图片类型
        pictureType = portalBaseImpl.findPictureType();
		
		RequestContext currentInstance = RequestContext.getCurrentInstance();
		currentInstance.execute("showOffBtn();");
	}

	@Override
	public void saveAction() {
		if (verifyInfo()) {// 验证数据合法性
			if (null == this.tdPortalNews.getRid()) {// 新增
				// 发布时间
				this.tdPortalNews.setNewsDate(new Date());
				// 发布部门
				TsOffice tsOffice = null;
				TbSysEmp tbSysEmp = sessionData.getUser().getTbSysEmp();
				if (null != tbSysEmp) {
					tsOffice = tbSysEmp.getTsOffice();
				}
				this.tdPortalNews.setTsOffice(tsOffice);
				// 发布人
				Integer fbRid = sessionData.getUser().getRid();
				this.tdPortalNews.setTsUserInfo(new TsUserInfo(fbRid));
				// 保存状态
				this.tdPortalNews.setStateMark(0);
			}
			// 信息关系
			
			this.tdPortalNews.setIfAll((short) 0);
			
			//发布人员Ids
			StringBuilder perRangeIds = new StringBuilder(); 
			if (StringUtils.isNotBlank(selectOfficeIds)) {
				perRangeIds = new StringBuilder(selectOfficeIds);
			}  else {//如果发布范围人数为空，则默认发给所有人员
			// this.tdPortalNews.setIfAll((short) 1);
				List<TsOffice> list= service.selAllOfficeList(null,sessionData.getUser().getTsUnit().getRid());
				for (TsOffice tsOffice : list) {
					perRangeIds.append(",").append(tsOffice.getRid());
				}
				if (perRangeIds.length() > 1) {
					perRangeIds = perRangeIds.deleteCharAt(0);
				}
			}
			if(ifCanGrantAllUnit){
				List<TdPortalNewsUnit> tdPortalNewsUnits = new ArrayList<TdPortalNewsUnit>(0);
				this.tdPortalNews.setTdPortalNewsUnits(tdPortalNewsUnits);
				//将人员Id处理到信息查看权限实体中，进行级联保存
				if( StringUtils.isNotBlank(perRangeIds.toString()))	{
					String[] idsArr = perRangeIds.toString().split(",");
					for (String id : idsArr) {
						TdPortalNewsUnit tdPortalNewsUnit = new TdPortalNewsUnit();
						tdPortalNewsUnit.setFkByNewsId(this.tdPortalNews);
						tdPortalNewsUnit.setFkByUnitId(new TsUnit(Integer.valueOf(id)));
						tdPortalNewsUnits.add(tdPortalNewsUnit);
					}
				}
			}else{
			
				List<TdPortalNewsOffice> tdPortalNewsOffices = new ArrayList<TdPortalNewsOffice>(0);
				this.tdPortalNews.setTdPortalNewsOffices(tdPortalNewsOffices);
				//将人员Id处理到信息查看权限实体中，进行级联保存
				if( StringUtils.isNotBlank(perRangeIds.toString()))	{
					String[] idsArr = perRangeIds.toString().split(",");
					for (String id : idsArr) {
						TdPortalNewsOffice tdPortalNewsOffice = new TdPortalNewsOffice();
						tdPortalNewsOffice.setTdPortalNews(this.tdPortalNews);
						TsOffice tsOffice = new TsOffice(Integer.valueOf(id));
						tdPortalNewsOffice.setTsOffice(tsOffice);
						tdPortalNewsOffices.add(tdPortalNewsOffice);
					}
				}
			}
			//如果不是发布到公告栏，则清空滚动天数
			if (this.tdPortalNews.getIfNotice().intValue() == 0) {
				this.tdPortalNews.setRollDays(null);
			}
			
			tdPortalNews.setTdPortalNewsAnnexes(annexList);

            //如果是图片类型
            if(tdPortalNews.getNewsType().equals(4) && pictureType != null){
                tdPortalNews.setTdPortalNewsType(pictureType);
            }

			tdPortalNews = this.portalBaseImpl.saveNews(this.tdPortalNews, false, 0);
			this.searchAction();
			this.backAction();
		} else {
			RequestContext.getCurrentInstance().execute("showOffBtn();");
		}

	}

	/**
	 * 初始化栏目类型
	 */
	private void initColNews() {
		// 初始化栏目Radio
		colNews = new ArrayList<SelectItem>();
		colNewsSize = 0;
		// 用户ID
		Integer userId = null;
		// 用户管理员，则不过滤条件
		boolean userAdmin = sessionData.getUser().getUseradmin();
		if (!userAdmin) {
			userId = sessionData.getUser().getRid();
		}
		List<Object[]> list = this.portalBaseImpl.fetchColNews(userId);
		if (null != list && list.size() > 0) {
			for (Object[] objArr : list) {
				// 栏目ID
				String rid = String.valueOf(objArr[0]);
				// 栏目名称
				String name = String.valueOf(objArr[1]);
				colNews.add(new SelectItem(rid, name));
			}
			colNewsSize = colNews.size();
		}
	}

	/**
	 * 添加图片初始化
	 */
	public void addPhotoAction() {
		tdPortalNewsAnnex = new TdPortalNewsAnnex();
		tdPortalNewsAnnex.setAnnexAddr("1");
		croppedImage = null;
		// 添加初始化时，将是否修改照片标识为false
		this.ifModPhoto = false;
	}

	/**
	 * 截图，获取截取的内容
	 */
	public void crop() {
		FileImageOutputStream imageOutput = null;
		try {
			if (StringUtils.isBlank(this.tdPortalNewsAnnex.getAnnexName())) {
				JsfUtil.addErrorMessage("图片标题不能为空！");
				return;
			}
			if (StringUtils.isBlank(this.tdPortalNewsAnnex.getAnnexDesc())) {
				JsfUtil.addErrorMessage("图片描述不能为空！");
				return;
			}
			if (!this.ifModPhoto) {
				if (croppedImage == null) {
					JsfUtil.addErrorMessage("剪辑图片不能为空！");
					return;
				}
				String fileNamepath = tdPortalNewsAnnex.getAnnexAddr();// 文件名称
				// 配置的虚拟路径
				String pzPath = JsfUtil.getAbsolutePath();
				// 实际文件路径
				String factFilePath = pzPath + fileNamepath;

				if (new File(factFilePath).exists()) {
					new File(factFilePath).delete();
				}

				imageOutput = new FileImageOutputStream(new File(factFilePath));
				imageOutput.write(croppedImage.getBytes(), 0, croppedImage.getBytes().length);
				this.annexList.add(tdPortalNewsAnnex);
				initPhotoTree();
			}
			RequestContext cI = RequestContext.getCurrentInstance();
			this.ifShowCrop = false;
			cI.update("photoDes");
			cI.execute("PF('photoDescript').hide();");
		} catch (Exception e) {
			JsfUtil.addErrorMessage("剪辑图片失败，请重新剪辑！");
		} finally {
			if (null != imageOutput) {
				try {
					imageOutput.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * 初始化图片树
	 */
	private void initPhotoTree() {
		photoTree = new DefaultTreeNode("root", null);
		portalTypeTree.setExpanded(true);
		if (null != annexList && annexList.size() > 0) {
			for (TdPortalNewsAnnex tdPortalNewsAnnex : annexList) {
				new DefaultTreeNode(tdPortalNewsAnnex, photoTree);
			}
		}
	}

	/**
	 * 授权初始化
	 */
	public void sqInitAction() {
		Map<String, Object> options = new HashMap<String, Object>();
		if(ifCanGrantAllUnit){
			  options.put("modal", true);
		        options.put("draggable", true);
		        options.put("resizable", false);
		        options.put("height", 500);
		        options.put("width", 920);
		        options.put("contentWidth", 870);
		        //对话框参数
		        Map<String, List<String>> paramsMap = new HashMap<String, List<String>>();
		        List<String> paramList = new ArrayList<String>(1);
		        //对话框标题
		        paramList.add("发布范围");
		        paramsMap.put("title", paramList);
		        
		        //选中的单位ids
		        paramList = new ArrayList<String>(1);
				paramList.add(selectOfficeIds);
		        paramsMap.put("selectIds", paramList);
		        RequestContext requestContext = RequestContext.getCurrentInstance();
		        requestContext.openDialog("/webapp/system/selectMutilUnitList", options, paramsMap);
		}else{
		
			options.put("modal", true);
			options.put("draggable", true);
			options.put("resizable", false);
			options.put("width", 350);
			options.put("height", 400);
			options.put("contentWidth", 320);
			options.put("contentHeight", 380);
	//		options.put("contentWidth", 870);
	
			Map<String, List<String>> paramsMap = new HashMap<String, List<String>>();
			/**
			 * 传入标题
			 */
			List<String> paramList = new ArrayList<String>(1);
			paramList.add("发布范围");
			paramsMap.put("officeTitle", paramList);
			/**
			 * 选中科室Id
			 */
			paramList = new ArrayList<String>(1);
			paramList.add(selectOfficeIds);
			paramsMap.put("selectOfficeIds", paramList);
	
			RequestContext requestContext = RequestContext.getCurrentInstance();
			requestContext.openDialog("/webapp/system/selectOfficesList", options, paramsMap);
		}
	}
	
	
	
	

	/**
	 * 选择人
	 * 
	 * @param event
	 *            弹出框关闭事件
	 */
	@SuppressWarnings("unchecked")
	public void onUserSelect(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			selectOfficeIds = "";
		    selectOfficeNames = "";
		    // 单次选中的ID
			StringBuffer selBuffer = new StringBuffer();
			// 单次选中的名称
			StringBuffer selNameBuffer = new StringBuffer();
		    if(ifCanGrantAllUnit){
				Collection<UnitPO> coll = (Collection<UnitPO>) selectedMap.get("selectUnitList");
				for(UnitPO po:coll){
					selBuffer.append(",").append(po.getUnitId());
					selNameBuffer.append(",").append(po.getUnitName());
				}
				
			}else{
			    Collection<TsOffice> coll = (Collection<TsOffice>) selectedMap.get("selectOffices");
				
				Set<String> nameSet = new LinkedHashSet<String>();
				for (TsOffice t : coll) {
					selBuffer.append(",").append(t.getRid());
					nameSet.add(t.getOfficename());
				}
				Iterator<String> iterator = nameSet.iterator();
				while (iterator.hasNext()) {
					String next = iterator.next();
					selNameBuffer.append(",").append(next);
				}
			}
			if (selBuffer.length() > 1) {
				selBuffer = selBuffer.deleteCharAt(0);
			}
			selectOfficeIds = selBuffer.toString();
			if (selNameBuffer.length() > 1) {
				selNameBuffer = selNameBuffer.deleteCharAt(0);
			}
			selectOfficeNames = selNameBuffer.toString();
		}
	}

	/**
	 * 信息分类选中事件
	 * 
	 * @param event
	 *            选中事件
	 */
	public void onportalTypeNodeSelect(NodeSelectEvent event) {
		this.tdPortalNews.setTdPortalNewsType(null);
		TreeNode selectNode = event.getTreeNode();
		if (null != selectNode) {
			TdPortalNewsType t = (TdPortalNewsType) selectNode.getData();
			this.tdPortalNews.setTdPortalNewsType(t);
		}
	}

	/**
	 * 查询页面：信息分类选中事件
	 * 
	 * @param event
	 *            选中事件
	 */
	public void onSearchTypeNodeSelect(NodeSelectEvent event) {
		this.searchNewsTypeId = null;
		this.searchNewsTypeName = null;
		TreeNode selectNode = event.getTreeNode();
		if (null != selectNode) {
			TdPortalNewsType t = (TdPortalNewsType) selectNode.getData();
			searchNewsTypeId = t.getRid();
			this.searchNewsTypeName = t.getTypeName();
		}
	}

	/**
	 * 图片列表
	 * 
	 * @param event
	 */
	public void onPhotoNodeSelect(NodeSelectEvent event) {
		this.tdPortalNewsAnnex = null;
		ifSelTree = false;
		TreeNode selectNode = event.getTreeNode();
		if (null != selectNode) {
			tdPortalNewsAnnex = (TdPortalNewsAnnex) selectNode.getData();
			ifSelTree = true;
		}
	}

	/**
	 * 删除图片
	 */
	public void delPhoto() {
		if (null == tdPortalNewsAnnex || !ifSelTree) {
			JsfUtil.addErrorMessage("请选择图片名称，再进行删除！");
			return;
		} else {
			this.annexList.remove(tdPortalNewsAnnex);
			initPhotoTree();
			this.ifSelTree = false;
		}
	}

	/**
	 * 修改图片
	 */
	public void modPhoto() {
		if (null == this.tdPortalNewsAnnex || !ifSelTree) {
			JsfUtil.addErrorMessage("请选择图片名称，再进行修改！");
		} else {
			RequestContext.getCurrentInstance().execute("PF('photoDescript').show();");
			// RequestContext.getCurrentInstance().update("photoRow,photoPanel,photoDes,croppedImage");
			//
			// ImageCropper imageCropper = (ImageCropper)
			// FacesContext.getCurrentInstance().getViewRoot().findComponent("tabView:editForm:croppedImage");
			// imageCropper.setInitialCoords("0,0,580,380");
			// imageCropper = new ImageCropper();
			// imageCropper.resetValue();
			this.ifSelTree = false;
			this.ifModPhoto = true;
			this.ifShowCrop = true;
		}
	}

	/**
	 * 选中不同的信息类别，初始化不同类型的信息内容
	 */
	public void selectNewsType() {
		if (0 == this.tdPortalNews.getNewsType()) {// 超文本
			if (tempNewsType == 4) {
				annexList = new ArrayList<TdPortalNewsAnnex>();
			}
		} else if (1 == this.tdPortalNews.getNewsType()) {// word
			fileType = ".doc";
			fileName = UUID.randomUUID().toString().replace("-", "");
			if (tempNewsType == 4) {
				annexList = new ArrayList<TdPortalNewsAnnex>();
			}
		} else if (2 == this.tdPortalNews.getNewsType()) {// excel
			fileType = ".xls";
			fileName = UUID.randomUUID().toString().replace("-", "");
			if (tempNewsType == 4) {
				annexList = new ArrayList<TdPortalNewsAnnex>();
			}
		} else if (3 == this.tdPortalNews.getNewsType()) {// ppt
			fileType = ".ppt";
			fileName = UUID.randomUUID().toString().replace("-", "");
			if (tempNewsType == 4) {
				annexList = new ArrayList<TdPortalNewsAnnex>();
			}
		} else if (4 == this.tdPortalNews.getNewsType()) {// 图片
			annexList = new ArrayList<TdPortalNewsAnnex>();
			tdPortalNewsAnnex = new TdPortalNewsAnnex();
			tdPortalNewsAnnex.setAnnexAddr("1");
			initPhotoTree();
		} else if (5 == this.tdPortalNews.getNewsType()) {// PDF
			currPdfPage = 0;
			totalPdfPage = 0;
			pdfList = new ArrayList<String>();
			tdPortalNews.setNewsAnnex(null);
			if (tempNewsType == 4) {
				annexList = new ArrayList<TdPortalNewsAnnex>();
			}
		}
		// 确认后修改属性
		tempNewsType = this.tdPortalNews.getNewsType();
        // 确认后修改属性
        tempNewsType = this.tdPortalNews.getNewsType();
        if(tdPortalNews.getNewsType() == 4){
            showNewsType = 0;
        }else{
            showNewsType = 1;
        }
	}

	/**
	 * 还原信息类型数据
	 */
	public void resetNewsType() {
		this.tdPortalNews.setNewsType(tempNewsType);
	}

	/**
	 * 页面加载，初始化信息
	 */
	private void initPage() {
		initSortTree();
		initNewsType();
		initPhotoTree();
		ifSelTree = false;
		tdPortalNewsAnnex = new TdPortalNewsAnnex();
		tdPortalNewsAnnex.setAnnexAddr("1");
	}

	/**
	 * 初始化物品类别树
	 */
	private void initSortTree() {
		portalTypeTree = new DefaultTreeNode("root", null);
		portalTypeTree.setExpanded(true);

		List<TdPortalNewsType> typeList = null;
		// 查询所有数据，不过滤
		Integer userId = null;
		// 如果是可用树的初始化，则查询可用集合
		typeList = this.portalBaseImpl.findNewsType(userId);

		if (null != typeList && typeList.size() > 0) {

			Set<String> firstSet = new LinkedHashSet<String>(); // 第一层
			Set<String> subSet = new LinkedHashSet<String>(); // 非第一层
																// 信息分类ID#@#父级信息分类ID
			Map<String, TdPortalNewsType> typeMap = new HashMap<String, TdPortalNewsType>(); // 所有类别
			for (TdPortalNewsType type : typeList) {
				// 信息分类ID
				String rid = String.valueOf(type.getRid());
				// 信息分类上级ID
				String parentId = type.getTdPortalNewsType() == null ? "" : String.valueOf(type.getTdPortalNewsType()
						.getRid());

				// 非第一层节点，添加到子集集合中
				if (!"".equals(parentId)) {
					subSet.add(rid + "#@#" + parentId);
				} else {// 第一层节点，添加到所有分类下
					firstSet.add(rid);
				}
				// 将所有的节点，封装进Map,下面将循环遍历处理树形数据
				typeMap.put(rid, type);
			}
			// 获取到父级节点的迭代器
			Iterator<String> parentIdItor = firstSet.iterator();
			while (parentIdItor.hasNext()) {
				// 获取到父级ID,封装进树形的第一层数据
				String parentId = parentIdItor.next();
				TdPortalNewsType parentType = typeMap.get(parentId);
				TreeNode parentNode = new DefaultTreeNode(parentType, portalTypeTree);
				addChildNode(parentId, parentNode, subSet, typeMap);
			}
		}
	}

	/**
	 * 构建类别树
	 * 
	 * @param parentId
	 *            父级ID
	 * @param parentNode
	 *            父级节点
	 * @param subSet
	 *            子级与父级封装的字符串集合
	 * @param typeMap
	 *            所有子集的数据集合
	 */
	private void addChildNode(String parentId, TreeNode parentNode, Set<String> subSet,
			Map<String, TdPortalNewsType> typeMap) {
		Iterator<String> subIter = subSet.iterator();
		while (subIter.hasNext()) {
			// 遍历子级与父级的集合
			String combStr = subIter.next();
			String[] combArr = combStr.split("#@#");
			// 子级ID
			String subRid = combArr[0];
			// 父级ID
			String parentTemp = combArr[1];
			// 如果父级Id在集合中存在，说明存在下级节点
			if (parentId.equals(parentTemp)) {
				TdPortalNewsType subType = typeMap.get(subRid);
				TreeNode subNode = new DefaultTreeNode(subType, parentNode);
				addChildNode(subRid, subNode, subSet, typeMap);
			}
		}
	}

	/**
	 * 初始化信息类型
	 */
	private void initNewsType() {
		newsType = new ArrayList<SelectItem>();
		for (NewsType type : NewsType.values()) {
			int typeNo = type.getTypeNo();
			String typeCn = type.getTypeCN();
			newsType.add(new SelectItem(typeNo, typeCn));
		}
	}

	/**
	 * 验证数据正确性
	 * 
	 * @return
	 */
	private boolean verifyInfo() {

		if (null == this.tdPortalNews.getTdPortalNewsType() || null == this.tdPortalNews.getTdPortalNewsType().getRid()) {
			JsfUtil.addErrorMessage("tabView:editForm:typeName", "栏目不能为空！");
			return false;
		}

		if(ifCanGrantAllUnit && (StringUtils.isBlank(selectOfficeIds) || StringUtils.isBlank(selectOfficeNames))){
			JsfUtil.addErrorMessage("tabView:editForm:selectNames", "发布范围不能为空！");
			return false;
		}
		
		if (StringUtils.isBlank(this.tdPortalNews.getNewsTitle())) {
			JsfUtil.addErrorMessage("tabView:editForm:newsTitle", "信息标题不能为空！");
			return false;
		}

		if (null == this.tdPortalNews.getTdPortalNewsType() || null == this.tdPortalNews.getTdPortalNewsType().getRid()) {
			JsfUtil.addErrorMessage("editForm:typeName", "信息分类不能为空！");
			return false;
		}
		
		if (this.tdPortalNews.getIfNotice().intValue() == 1 && null == this.tdPortalNews.getRollDays()) {
			JsfUtil.addErrorMessage("tabView:editForm:rollDays", "滚动天数不能为空！");
			return false;
		}

		if (Short.valueOf(this.tdPortalNews.getNewsType().toString()) == NewsType.HYPERTEXT.getTypeNo()) {// 超文本
			if (StringUtils.isBlank(this.tdPortalNews.getNewsCont())) {
				JsfUtil.addErrorMessage("editForm:myTextarea", "超文本内容不能为空！");
				return false;
			}
			this.tdPortalNews.setNewsAnnex(null);
		} else if (Short.valueOf(this.tdPortalNews.getNewsType().toString()) == NewsType.WORD.getTypeNo()
				|| Short.valueOf(this.tdPortalNews.getNewsType().toString()) == NewsType.EXCEL.getTypeNo()
				|| Short.valueOf(this.tdPortalNews.getNewsType().toString()) == NewsType.PPT.getTypeNo()) {// office
			StringBuilder path = new StringBuilder(JsfUtil.getAbsolutePath());
			path.append("/officeFiles/").append(fileName).append(fileType);
			File tempFile = new File(path.toString());
			if (!tempFile.exists()) {
				JsfUtil.addErrorMessage("Office信息不能为空！");
				return false;
			}
			// 设置office路径
			this.tdPortalNews.setNewsAnnex(path.toString());
			// 设置FCK内容
			this.tdPortalNews.setNewsCont(null);
		} else if (Short.valueOf(this.tdPortalNews.getNewsType().toString()) == NewsType.PHOTOS.getTypeNo()) {// photos
			// 清空FCK与Office
			this.tdPortalNews.setNewsCont(null);
			this.tdPortalNews.setNewsAnnex(null);
			if (this.annexList.size() == 0) {
				JsfUtil.addErrorMessage("图片信息不能为空！");
				return false;
			}
		} else if (Short.valueOf(this.tdPortalNews.getNewsType().toString()) == NewsType.PDF.getTypeNo()) {// PDF格式
			this.tdPortalNews.setNewsCont(null);
			// PDF地址
			StringBuffer addrPdfs = new StringBuffer();
			if (null != pdfList && pdfList.size() > 0) {
				for (String s : pdfList) {
					addrPdfs.append(",").append(s);
				}
				this.tdPortalNews.setNewsAnnex(addrPdfs.substring(1));
			} else {
				JsfUtil.addErrorMessage("PDF文件不能为空！");
				return false;
			}
		}
		return true;
	}

	/**
	 * 下载文件
	 */
	public void downLoadDiskFile() {
		// 虚拟路径
		String xnPath = JsfUtil.getAbsolutePath();
		String path = xnPath + this.tdPortalNewsAnnex.getAnnexAddr();
		FileInputStream fis = null;
		try {
			File file = new File(path);
			fis = new FileInputStream(file);
			String fileString = DownLoadUtil.uploadFile2Database(fis);
			DownLoadUtil.downFile(this.tdPortalNewsAnnex.getAnnexName(), fileString);
		} catch (Exception e1) {
			e1.printStackTrace();
		} finally {// 关闭输入输出流
			try {
				fis.close();
			} catch (IOException e2) {
				e2.printStackTrace();
			}
		}
	}

	/**
	 * 删除上传的文件
	 */
	public void deleteDiskFile() {
		// 配置的虚拟路径
		String xnPath = JsfUtil.getAbsolutePath();
		this.annexList.remove(this.tdPortalNewsAnnex);
		// 附件文件如果存在，则删除
		File tempFile = new File(xnPath + this.tdPortalNewsAnnex.getAnnexAddr());
		if (tempFile.exists()) {
			tempFile.delete();
		}
	}

	/**
	 * 文件上传
	 */
	public void handleFileUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			TdPortalNewsAnnex annex = new TdPortalNewsAnnex();
			try {
				String fileName = file.getFileName();// 文件名称
				String uuid = UUID.randomUUID().toString().replaceAll("-", "");
				String path = JsfUtil.getAbsolutePath();
				String relativePath = new StringBuffer("/files/").append(uuid)
						.append(fileName.substring(fileName.lastIndexOf("."))).toString();
				// 文件路径
				String filePath = new StringBuilder(path).append(relativePath).toString();
				// 将文件信息保存至list中
				annex.setAnnexName(fileName);// 文件名称
				annex.setAnnexAddr(relativePath);// 文件路径
				annex.setTdPortalNews(this.tdPortalNews);// 主表信息
				annex.setXh(this.annexList.size());// 序号
				this.annexList.add(annex);
				FileUtils.copyFile(filePath, file.getInputstream());
			} catch (Exception e) {
				FacesMessage mess = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", mess);
				e.printStackTrace();
			}
		}
	}

	/**
	 * 文件上传
	 */
	public void handlePhotoUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			tdPortalNewsAnnex = new TdPortalNewsAnnex();
			try {
				String uuid = UUID.randomUUID().toString().replaceAll("-", "");
				String fileName = file.getFileName();// 文件名称
				// 配置的虚拟路径
				String pzPath = JsfUtil.getAbsolutePath();
				// 实际文件路径
				String factFilePath = pzPath + "resources/files/" + uuid
						+ fileName.substring(fileName.lastIndexOf("."));
				// 页面显示路径
				String xdpath = "/resources/files/" + uuid + fileName.substring(fileName.lastIndexOf("."));
				// 将文件信息保存至list中
				tdPortalNewsAnnex.setAnnexName(fileName.substring(0,fileName.lastIndexOf(".")));// 文件名称
				tdPortalNewsAnnex.setAnnexAddr(xdpath);// 文件路径
				tdPortalNewsAnnex.setTdPortalNews(this.tdPortalNews);// 主表信息
				tdPortalNewsAnnex.setXh(this.annexList.size());// 序号
				tdPortalNewsAnnex.setAnnexDesc("");
				
				// 虚拟路径存储一个，项目下临时存取一个
				FileUtils.copyFile(factFilePath, file.getInputstream());
				ImageUtil.compressImage(factFilePath, factFilePath, 520, 390);
				this.ifShowCrop = true;
			} catch (Exception e) {
				this.ifShowCrop = false;
				FacesMessage mess = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", mess);
				e.printStackTrace();
			}
		}
	}

	/**
	 * PDF文件上传
	 */
	public void handlePDFUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			try {
				String fileName = file.getFileName();// 文件名称
				String uuid = UUID.randomUUID().toString().replaceAll("-", "");
				String path = JsfUtil.getAbsolutePath();
				String relativePath = new StringBuffer("/files/").append(uuid)
						.append(fileName.substring(fileName.lastIndexOf("."))).toString();
				// 文件路径
				String filePath = new StringBuilder(path).append(relativePath).toString();
				pdfList.add(relativePath);
				totalPdfPage = pdfList.size();
				currPdfPage = totalPdfPage;
				pdfhtml = new StringBuffer(PDFMODULE_FRONT).append(pdfList.get(currPdfPage - 1)).append(PDFMODULE_BACK)
						.toString();
				FileUtils.copyFile(filePath, file.getInputstream());
			} catch (Exception e) {
				FacesMessage mess = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", mess);
				e.printStackTrace();
			}
		}
	}

	/**
	 * 删除正文
	 */
	public void delLoadPdf() {
		if (pdfList.size() >= currPdfPage && pdfList.size() > 0) {
			totalPdfPage--;
			pdfList.remove(currPdfPage - 1);
			if (currPdfPage > totalPdfPage) {
				currPdfPage = totalPdfPage;
			}
			if (currPdfPage >= 1) {
				pdfhtml = new StringBuffer(PDFMODULE_FRONT).append(pdfList.get(currPdfPage - 1)).append(PDFMODULE_BACK)
						.toString();
			} else {
				pdfhtml = null;
			}
		}
	}

	/**
	 * 上一个
	 */
	public void upPdf() {
		if (pdfList.size() > 0 && currPdfPage > 0) {
			currPdfPage = currPdfPage - 1;
			pdfhtml = new StringBuffer(PDFMODULE_FRONT).append(pdfList.get(currPdfPage - 1)).append(PDFMODULE_BACK)
					.toString();
		}
	}

	/**
	 * 下一个
	 */
	public void downPdf() {
		if (pdfList.size() > 0 && currPdfPage < pdfList.size()) {
			currPdfPage = currPdfPage + 1;
			pdfhtml = new StringBuffer(PDFMODULE_FRONT).append(pdfList.get(currPdfPage - 1)).append(PDFMODULE_BACK)
					.toString();
		}
	}

	/**
	 * 解决页面模糊查询时，输入% 或 ％查询数据有误问题
	 * 
	 * @param str
	 *            页面传入的值
	 * @return
	 */
	private String convertBFH(String str) {
		if (str.indexOf("%") != -1 || str.indexOf("％") != -1) {
			str = str.replaceAll("%", "\\\\%").replaceAll("％", "\\\\％");
		}
		return str;
	}

	public TreeNode getPortalTypeTree() {
		return portalTypeTree;
	}

	public void setPortalTypeTree(TreeNode portalTypeTree) {
		this.portalTypeTree = portalTypeTree;
	}

	public List<SelectItem> getNewsType() {
		return newsType;
	}

	public void setNewsType(List<SelectItem> newsType) {
		this.newsType = newsType;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public TdPortalNews getTdPortalNews() {
		return tdPortalNews;
	}

	public void setTdPortalNews(TdPortalNews tdPortalNews) {
		this.tdPortalNews = tdPortalNews;
	}

	public List<TdPortalNewsType> getTdPortalNewsTypeList() {
		return TdPortalNewsTypeList;
	}

	public void setTdPortalNewsTypeList(List<TdPortalNewsType> tdPortalNewsTypeList) {
		TdPortalNewsTypeList = tdPortalNewsTypeList;
	}

	public String getFileType() {
		return fileType;
	}

	public void setFileType(String fileType) {
		this.fileType = fileType;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public List<TdPortalNewsAnnex> getAnnexList() {
		return annexList;
	}

	public void setAnnexList(List<TdPortalNewsAnnex> annexList) {
		this.annexList = annexList;
	}

	public TdPortalNewsAnnex getTdPortalNewsAnnex() {
		return tdPortalNewsAnnex;
	}

	public void setTdPortalNewsAnnex(TdPortalNewsAnnex tdPortalNewsAnnex) {
		this.tdPortalNewsAnnex = tdPortalNewsAnnex;
	}

	public TreeNode getPhotoTree() {
		return photoTree;
	}

	public void setPhotoTree(TreeNode photoTree) {
		this.photoTree = photoTree;
	}

	public CroppedImage getCroppedImage() {
		return croppedImage;
	}

	public void setCroppedImage(CroppedImage croppedImage) {
		this.croppedImage = croppedImage;
	}

	public boolean isIfSelTree() {
		return ifSelTree;
	}

	public void setIfSelTree(boolean ifSelTree) {
		this.ifSelTree = ifSelTree;
	}

	public String getSearchNewsTypeName() {
		return searchNewsTypeName;
	}

	public void setSearchNewsTypeName(String searchNewsTypeName) {
		this.searchNewsTypeName = searchNewsTypeName;
	}

	public Integer getSearchNewsTypeId() {
		return searchNewsTypeId;
	}

	public void setSearchNewsTypeId(Integer searchNewsTypeId) {
		this.searchNewsTypeId = searchNewsTypeId;
	}

	public String getSearchInfoTitle() {
		return searchInfoTitle;
	}

	public void setSearchInfoTitle(String searchInfoTitle) {
		this.searchInfoTitle = searchInfoTitle;
	}

	public Date getSearchStartDate() {
		return searchStartDate;
	}

	public void setSearchStartDate(Date searchStartDate) {
		this.searchStartDate = searchStartDate;
	}

	public Date getSearchEndDate() {
		return searchEndDate;
	}

	public void setSearchEndDate(Date searchEndDate) {
		this.searchEndDate = searchEndDate;
	}

	public int getTempNewsType() {
		return tempNewsType;
	}

	public void setTempNewsType(int tempNewsType) {
		this.tempNewsType = tempNewsType;
	}

	public List<SelectItem> getColNews() {
		return colNews;
	}

	public void setColNews(List<SelectItem> colNews) {
		this.colNews = colNews;
	}

	public int getColNewsSize() {
		return colNewsSize;
	}

	public void setColNewsSize(int colNewsSize) {
		this.colNewsSize = colNewsSize;
	}

	public int getTotalPdfPage() {
		return totalPdfPage;
	}

	public void setTotalPdfPage(int totalPdfPage) {
		this.totalPdfPage = totalPdfPage;
	}

	public int getCurrPdfPage() {
		return currPdfPage;
	}

	public void setCurrPdfPage(int currPdfPage) {
		this.currPdfPage = currPdfPage;
	}

	public List<String> getPdfList() {
		return pdfList;
	}

	public void setPdfList(List<String> pdfList) {
		this.pdfList = pdfList;
	}

	public String getPdfhtml() {
		return pdfhtml;
	}

	public void setPdfhtml(String pdfhtml) {
		this.pdfhtml = pdfhtml;
	}

	public ImageCropper getImageCropper() {
		return imageCropper;
	}

	public void setImageCropper(ImageCropper imageCropper) {
		this.imageCropper = imageCropper;
	}

	public boolean isIfModPhoto() {
		return ifModPhoto;
	}

	public void setIfModPhoto(boolean ifModPhoto) {
		this.ifModPhoto = ifModPhoto;
	}

	public boolean isIfShowCrop() {
		return ifShowCrop;
	}

	public void setIfShowCrop(boolean ifShowCrop) {
		this.ifShowCrop = ifShowCrop;
	}

    public Integer getShowNewsType() {
        return showNewsType;
    }

    public void setShowNewsType(Integer showNewsType) {
        this.showNewsType = showNewsType;
    }

    public TdPortalNewsType getPictureType() {
        return pictureType;
    }

    public void setPictureType(TdPortalNewsType pictureType) {
        this.pictureType = pictureType;
    }

	public boolean isIfHasAdminInType() {
		return ifHasAdminInType;
	}

	public void setIfHasAdminInType(boolean ifHasAdminInType) {
		this.ifHasAdminInType = ifHasAdminInType;
	}

	public String getSelectOfficeIds() {
		return selectOfficeIds;
	}

	public void setSelectOfficeIds(String selectOfficeIds) {
		this.selectOfficeIds = selectOfficeIds;
	}

	public String getSelectOfficeNames() {
		return selectOfficeNames;
	}

	public void setSelectOfficeNames(String selectOfficeNames) {
		this.selectOfficeNames = selectOfficeNames;
	}

	public List<Object[]> getReadStateList() {
		return readStateList;
	}

	public void setReadStateList(List<Object[]> readStateList) {
		this.readStateList = readStateList;
	}

	public Boolean getIfCanGrantAllUnit() {
		return ifCanGrantAllUnit;
	}

	public void setIfCanGrantAllUnit(Boolean ifCanGrantAllUnit) {
		this.ifCanGrantAllUnit = ifCanGrantAllUnit;
	}
	
}
