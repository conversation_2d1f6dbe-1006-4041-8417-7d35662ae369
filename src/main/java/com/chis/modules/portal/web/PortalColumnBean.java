package com.chis.modules.portal.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import org.primefaces.context.RequestContext;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.DualListModel;
import org.primefaces.model.TreeNode;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.entity.TdPortalColauth;
import com.chis.modules.portal.entity.TdPortalColumn;
import com.chis.modules.portal.enumn.PortalColType;
import com.chis.modules.portal.service.PortalBaseImpl;
import com.chis.modules.system.entity.TsOffice;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesSimpleBean;

/**
 * Created by Administrator on 14-8-18.
 * 
 * 
 * <p>修订内容：</p>
 * 新增系统参数控制授权范围
 * @ClassReviser xq,2018年1月16日,PortalColumnBean
 */
@ManagedBean(name = "portalColumnBean")
@ViewScoped
public class PortalColumnBean extends FacesSimpleBean {

    private static final long serialVersionUID = -995003163372147965L;
    /** 存在session中的对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private PortalBaseImpl service = (PortalBaseImpl) SpringContextHolder.getBean(PortalBaseImpl.class);
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /** 栏目实例 */
    private TdPortalColumn tdPortalColumn;
    /** 栏目rid */
    private Integer rid;
    /** 查询名称 */
    private String searchName;
    //科室名
    private String userOfficeName;
    //科室ID
    private Integer userOfficeRid;
    //科室树
    private TreeNode userOfficeTreeNode;
    //双选择列表，用户集合
    private DualListModel<TsUserInfo> userInfoDualListModel;
    //查询类型
    private Integer searchType;
    //类型map
    private Map<String,Integer> searchTypeMap;

    //栏目授权是否可以选择其他单位人员
    private Boolean ifShowAllUser = Boolean.FALSE;
    
    @PostConstruct
    public void init(){
        tdPortalColumn = new TdPortalColumn();
        searchName = "";
        searchType = null;
        String systemParam = commService.findParamValue("PORTAL_SHOW_All_USER");
		if("1".equals(systemParam)){
			ifShowAllUser = Boolean.TRUE;
		}
        //初始化科室树
        initOfficeTree();
        initTypeMap();
        this.searchAction();
    }

    public void initTypeMap(){
        searchTypeMap = new HashMap<String, Integer>();
        searchTypeMap.put("普通栏目",0);
        searchTypeMap.put("图案栏目",1);
        searchTypeMap.put("公告栏目",2);
        searchTypeMap.put("常用链接",3);
        searchTypeMap.put("其它栏目",4);
    }

    public void onSearchUnitChange(){
        this.searchAction();
    }

    /** 添加初始化 */
    public void addInitAction(){
        this.tdPortalColumn = new TdPortalColumn();
        tdPortalColumn.setIfHideHeader(0);
    }


    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder("from TdPortalColumn t where 1 = 1");
        if(null != searchType){
            sb.append(" and t.colType = ").append(searchType);
        }
        if(StringUtils.isNotBlank(this.searchName)){
            sb.append(" and t.colName like :columnname escape '\\\'");
            this.paramMap.put("columnname", "%" + StringUtils.convertBFH(this.searchName.trim()) + "%");
        }
        String h2 = "select count(*) " + sb.toString();
        String h1 = "select t " + sb.append(" order by t.colType,t.colName").toString();
        return new String[]{h1,h2};
    }

    //保存新增的组或者修改内容
    public void saveAction(){
        if(null == this.tdPortalColumn.getRid()){
            this.tdPortalColumn.setColType(PortalColType.OTHER);
        }
        //保存或更新组内容
        this.service.updateColumn(this.tdPortalColumn);
        //操作成功，关闭页面弹窗
        JsfUtil.addSuccessMessage("保存成功！");
        RequestContext.getCurrentInstance().execute("EditDialog.hide()");
        this.searchAction();
    }

    public void modInit(){
    }

    //删除操作
    public void deleteAction(){
        String msg = this.service.deleteColumn(rid);
        //删除失败，返回错误信息
        if(StringUtils.isNotBlank(msg)) {
            JsfUtil.addErrorMessage(msg);
        }
        this.searchAction();
    }

    //成员管理初始化
    public void userManagerInit(){
        this.userOfficeName = null;
        this.userOfficeRid = null;
        List<TsUserInfo> sourceList = new LinkedList<TsUserInfo>();
        List<TsUserInfo> targetList = this.service.findCloumnUserList(rid,false);
        //查询本单位所有用户
        List<TsUserInfo> userList = this.service.findAllUsers(sessionData.getUser().getTsUnit().getRid(), null);
        for(TsUserInfo t : userList){
            boolean isExist = false;
            //判定目标列表中是否已有该用户，没有则将该用户增加到可选用户中
            for(TsUserInfo s : targetList){
                if(t.getRid().equals(s.getRid())){
                    isExist = true;
                    break;
                }
            }
            if(!isExist){
                sourceList.add(t);
            }
        }
        this.userInfoDualListModel = new DualListModel<TsUserInfo>(sourceList,targetList);
    }

    // 初始化科室树 userOfficeTreeNode
    public void initOfficeTree(){
        if (null == this.userOfficeTreeNode) {
            this.userOfficeTreeNode = new DefaultTreeNode("root", null);
            List<TsOffice> list = this.service.findOfficeList(sessionData.getUser().getTsUnit().getRid());
            if (null != list && list.size() > 0) {
                Map<Integer, TreeNode> map = new HashMap<Integer, TreeNode>();
                for (TsOffice t : list) {
                    if (null == t.getParentOffice()) {
                        TreeNode node = new DefaultTreeNode(t, this.userOfficeTreeNode);
                        map.put(t.getRid(), node);
                    } else {
                        TreeNode node = new DefaultTreeNode(t, map.get(t.getParentOffice().getRid()));
                        map.put(t.getRid(), node);
                    }
                }
                map.clear();
            }
        }
    }

    //科室树选中事件
    public void onOfficeNodeSelect(NodeSelectEvent event) {
        TreeNode node = event.getTreeNode();
        if (null != node) {
            TsOffice office = (TsOffice) node.getData();
            this.userOfficeRid = office.getRid();
            this.userOfficeName = office.getOfficename();
            List<TsUserInfo> sourceList = new ArrayList<TsUserInfo>();
            List<TsUserInfo> targetList = this.userInfoDualListModel.getTarget();
            //查询该单位该科室的所有用户
            List<TsUserInfo> userList = this.service.findAllUsers(sessionData.getUser().getTsUnit().getRid(), userOfficeRid);
            for(TsUserInfo t : userList){
                boolean isExist = false;
                for(TsUserInfo s : targetList){
                    if(t.getRid().equals(s.getRid())){
                        isExist = true;
                        break;
                    }
                }
                if(!isExist){
                    sourceList.add(t);
                }
            }
            this.userInfoDualListModel = new DualListModel<TsUserInfo>(sourceList,targetList);
        }
    }

    //用户管理提交
    public void userManagerAction(){
        TdPortalColauth tdPortalColauth = null;
        List<TdPortalColauth> tdPortalColauths = new ArrayList<TdPortalColauth>();
        List<TsUserInfo> targetList = this.userInfoDualListModel.getTarget();
        //将已选记录转换成用户组列表
        for(TsUserInfo t : targetList){
            tdPortalColauth = new TdPortalColauth();
            tdPortalColauth.setTdPortalColumn(this.tdPortalColumn);
            tdPortalColauth.setTsUserInfo(t);
            tdPortalColauths.add(tdPortalColauth);
        }
        //将已选用户记录持久化到数据库中
        String msg = this.service.updateTdPortalColauth(tdPortalColauths, this.rid);
        if(msg != null){
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    
    /**
	 * 授权初始化
	 * <p>方法描述：</p>
 	 * 当系统参数PORTAL_SHOW_All_USER为是时，授权执行此方法
 	 * @MethodAuthor xq,2018年1月15日,sqInitAction
	 */
	public void sqInitAction() {
		Map<String, Object> options = new HashMap<String, Object>();
		options.put("modal", true);
		options.put("draggable", true);
		options.put("resizable", false);
		options.put("height", 500);
		options.put("width", 920);
		options.put("contentWidth", 880);
		options.put("contentHeight", 480);
		Map<String, List<String>> paramsMap = new HashMap<String, List<String>>();
		/**
		 * 传入title
		 */
		List<String> paramList = new ArrayList<String>(1);
		paramList.add("栏目授权");
		paramsMap.put("title", paramList);

		/**
		 * 传入已选择的用户
		 */
		paramList = new ArrayList<String>(1);
		String selectIds = "";
		List<TsUserInfo> selectUsers = this.service.findCloumnUserList(rid,true);
		if(selectUsers!=null && selectUsers.size()>0){
			for(TsUserInfo user:selectUsers){
				selectIds+=","+user.getRid().toString();
			}
		}
		paramList.add(selectIds.replaceFirst(",", ""));
		paramsMap.put("selectIds", paramList);

		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("/webapp/system/selectMutilUserList", options, paramsMap);
		
	}

	/**
	 * 选择人
	 * 
	 * @param event 弹出框关闭事件
	 * 
	 * <p>方法描述：</p>
 	 * 授权弹框回调方法
 	 * @MethodAuthor xq,2018年1月15日,onUserSelect

	 */
	public void onUserSelect(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			Collection<TsUserInfo> coll = (Collection<TsUserInfo>) selectedMap.get("selectUserList");
			TdPortalColauth tdPortalColauth = null;
	        List<TdPortalColauth> tdPortalColauths = new ArrayList<TdPortalColauth>();
	        //将已选记录转换成用户组列表
	        for(TsUserInfo t : coll){
	            tdPortalColauth = new TdPortalColauth();
	            tdPortalColauth.setTdPortalColumn(this.tdPortalColumn);
	            tdPortalColauth.setTsUserInfo(t);
	            tdPortalColauths.add(tdPortalColauth);
	        }
	        //将已选用户记录持久化到数据库中
	        String msg = this.service.updateTdPortalColauth(tdPortalColauths, this.rid);
	        if(msg != null){
	            JsfUtil.addErrorMessage("保存失败！");
	        }
		}
	}
    
    
    public SessionData getSessionData() {
        return sessionData;
    }

    public void setSessionData(SessionData sessionData) {
        this.sessionData = sessionData;
    }

    public TdPortalColumn getTdPortalColumn() {
        return tdPortalColumn;
    }

    public void setTdPortalColumn(TdPortalColumn tdPortalColumn) {
        this.tdPortalColumn = tdPortalColumn;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getSearchName() {
        return searchName;
    }

    public void setSearchName(String searchName) {
        this.searchName = searchName;
    }

    public String getUserOfficeName() {
        return userOfficeName;
    }

    public void setUserOfficeName(String userOfficeName) {
        this.userOfficeName = userOfficeName;
    }

    public Integer getUserOfficeRid() {
        return userOfficeRid;
    }

    public void setUserOfficeRid(Integer userOfficeRid) {
        this.userOfficeRid = userOfficeRid;
    }

    public TreeNode getUserOfficeTreeNode() {
        return userOfficeTreeNode;
    }

    public void setUserOfficeTreeNode(TreeNode userOfficeTreeNode) {
        this.userOfficeTreeNode = userOfficeTreeNode;
    }

    public DualListModel<TsUserInfo> getUserInfoDualListModel() {
        return userInfoDualListModel;
    }

    public void setUserInfoDualListModel(DualListModel<TsUserInfo> userInfoDualListModel) {
        this.userInfoDualListModel = userInfoDualListModel;
    }

    public Integer getSearchType() {
        return searchType;
    }

    public void setSearchType(Integer searchType) {
        this.searchType = searchType;
    }

    public Map<String, Integer> getSearchTypeMap() {
        return searchTypeMap;
    }

    public void setSearchTypeMap(Map<String, Integer> searchTypeMap) {
        this.searchTypeMap = searchTypeMap;
    }

	public Boolean getIfShowAllUser() {
		return ifShowAllUser;
	}

	public void setIfShowAllUser(Boolean ifShowAllUser) {
		this.ifShowAllUser = ifShowAllUser;
	}
    
    
}
