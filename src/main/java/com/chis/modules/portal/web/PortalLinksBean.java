package com.chis.modules.portal.web;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.UUID;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;

import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.UploadedFile;

import com.chis.common.utils.FileUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.portal.entity.TdPortalLinks;
import com.chis.modules.portal.service.PortalBaseImpl;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.web.FacesSimpleBean;

/**
 * Created by Administrator on 14-8-19.
 */
@ManagedBean(name = "portalLinksBean")
@ViewScoped
public class PortalLinksBean extends FacesSimpleBean {

    private static final long serialVersionUID = 7116106137455937963L;
    /**存在session中的对象*/
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	private PortalBaseImpl service = (PortalBaseImpl) SpringContextHolder.getBean(PortalBaseImpl.class);
    /** 名称查询 */
    private String searchName;
    /** 打开方式查询 */
    private String[] searchType;
    /** 链接实例 */
    private TdPortalLinks tdPortalLinks;
    /** 记录RID */
    private Integer rid;
    private String xnPath;
    /* 页面控制标识 */
    private boolean flag;

    @PostConstruct
    public void inti(){
        String param = JsfUtil.getRequest().getParameter("opt");
        flag = StringUtils.isNotBlank(param);
        searchName = "";
        searchType = new String[]{};
        tdPortalLinks = new TdPortalLinks();
        //虚拟路径
        xnPath = JsfUtil.getAbsolutePath();
        searchAction();
    }

    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder("");
        sb.append("from TdPortalLinks t where 1 = 1");
        //条件查询，多状态查询
        if(searchType.length > 0){
            sb.append(" and (t.linkType = ").append(Integer.valueOf(searchType[0]));
            for(int count = 1 ; count < searchType.length ; count++){
                sb.append(" or t.linkType = ").append(Integer.valueOf(searchType[count]));
            }
            sb.append(")");
        }
        if(StringUtils.isNotBlank(searchName)){
            sb.append(" and t.linkName like :linkname escape '\\\'");
            this.paramMap.put("linkname", "%" + convertBFH(this.searchName.trim()) + "%");
        }
        String h2 = "select count(*) " + sb.toString();
        String h1 = "select t " + sb.append(" order by t.nums").toString();
        return new String[]{h1,h2};
    }

    /** 处理查询时“%”问题 */
    private String convertBFH(String str) {
        if (str.indexOf("%") != -1 || str.indexOf("％") != -1) {
            str = str.replaceAll("%", "\\\\%").replaceAll("％", "\\\\％");
        }
        return str;
    }

    /** 新增初始化 */
    public void addInit(){
        tdPortalLinks = new TdPortalLinks();
    }

    /** 修改初始化 */
    public void modInit(){
    }

    /** 保存 */
    public void saveAction(){
        if(null == this.tdPortalLinks.getLinkIcon()){
            JsfUtil.addErrorMessage("图标不允许为空！");
            return;
        }
        if(null == tdPortalLinks.getRid()){
            tdPortalLinks.setCreateManid(sessionData.getUser().getRid());
            tdPortalLinks.setCreateDate(new Date());
        }else{
            tdPortalLinks.setModifyManid(sessionData.getUser().getRid());
            tdPortalLinks.setModifyDate(new Date());
        }
        String msg = this.service.saveOrUpdatePortalLinks(tdPortalLinks);
        if(StringUtils.isNotBlank(msg)){
            this.deleteDiskFile();
            JsfUtil.addErrorMessage("mainForm:groupDesc", msg);
            return;
        }else{
            JsfUtil.addSuccessMessage("保存成功！");
            RequestContext.getCurrentInstance().execute("EditDialog.hide()");
        }
        if(tdPortalLinks.getRid() == null){
            searchName = "";
            searchType = new String[]{};
        }
        this.searchAction();
    }

    /** 删除 */
    public void deleteAction(){
        this.deleteDiskFile();
        String msg = this.service.deletePortalLinks(rid);
        if(StringUtils.isNotBlank(msg)) {
            JsfUtil.addErrorMessage(msg);
        }
        this.searchAction();
    }

    //文件上传
    public void handleFileUpload(FileUploadEvent event){
        if (null != event) {
            UploadedFile file = event.getFile();
            String fileName = file.getFileName();
            //得到唯一uid
            String uuid = UUID.randomUUID().toString().replaceAll("-","");
            //根路径
            //String path = System.getProperty("user.dir").replaceAll("\\\\","/");
            //后缀名
            String hz = fileName.substring(fileName.lastIndexOf(".")+1);
            //文件路径
            String filePath = xnPath+"files/icons/"+uuid+"."+hz;
            String showDir = "/files/icons/"+uuid+"."+hz;
            this.tdPortalLinks.setLinkIcon(showDir);
            try{
            	FileUtils.copyFile(filePath, file.getInputstream());
                JsfUtil.addSuccessMessage("上传成功！");
                RequestContext.getCurrentInstance().execute("fileUIdVar.hide();");
            } catch (IOException e){
                FacesMessage msg = new FacesMessage("上传失败", file.getFileName()+ "上传失败！");
                FacesContext.getCurrentInstance().addMessage("上传失败", msg);
                e.printStackTrace();
            }
        }
    }

 


    /* 文件删除 */
    public void deleteDiskFile() {
        String iconFile = this.tdPortalLinks.getLinkIcon();
        if(null != iconFile){
            String filePath = xnPath+iconFile.substring(1);
            new File(filePath).delete();
            this.tdPortalLinks.setLinkIcon(null);
        }
    }

    public String getSearchName() {
        return searchName;
    }

    public void setSearchName(String searchName) {
        this.searchName = searchName;
    }

    public String[] getSearchType() {
        return searchType;
    }

    public void setSearchType(String[] searchType) {
        this.searchType = searchType;
    }

    public SessionData getSessionData() {
        return sessionData;
    }

    public void setSessionData(SessionData sessionData) {
        this.sessionData = sessionData;
    }

    public TdPortalLinks getTdPortalLinks() {
        return tdPortalLinks;
    }

    public void setTdPortalLinks(TdPortalLinks tdPortalLinks) {
        this.tdPortalLinks = tdPortalLinks;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public boolean isFlag() {
        return flag;
    }

    public void setFlag(boolean flag) {
        this.flag = flag;
    }
}
